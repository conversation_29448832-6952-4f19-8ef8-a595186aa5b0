/** @format */

declare namespace sap.modeling {
  export namespace ontology {
    export class BaseEntity {
      public displayName: string;
      public technicalName: string;
      public relatedSymbols: any;
      public ontologyUri: string;
      public firePropertyChanged(oEventArgs, sPass, bIsCascadeAllowed): any;
      public deleteObject(): void;
    }
    export class Entity extends BaseEntity {
      public constructor(...args);
      public getSourceLinkObjects(): any[];
    }
    export class RelationshipLink extends BaseEntity {
      public constructor(...args);
    }

    export class SubconceptLink extends BaseEntity {
      public constructor(...args);
    }

    export class Model {
      public entities: sap.modeling.ontology.Entity[];
      public relationshipLinks: sap.modeling.ontology.RelationshipLink[];
      public subconceptLinks: sap.modeling.ontology.SubconceptLink[];
      public previousSelectedEntities: sap.modeling.ontology.BaseEntity[];
      public constructor(...args);
    }

    export namespace ui {
      export class Diagram {
        public symbols: IDiagramSymbols;
        public constructor(...args);
      }

      export class DiagramEditorExtension {
        public constructor(...args);
      }

      export interface IDiagramSymbol {
        object: any;
        getLinkSymbols(): object[];
        isLinkSymbol: boolean;
        get(index: number): object;
      }

      export interface IDiagramSymbols extends Array<any> {
        get(index: number): any;
      }
    }
  }
}
// Object and File deployment status.  obsolete --- replaced by ObjectStatus
declare enum DeploymentStatus {
  New = 0, // Not deployed
  Active = 1, // Deployed and not modified
  Revised = 2, // Deployed but modified
  Failed = 3, // Deployed but failed
  Pending = 4, // Deployment pending
}

// Object status.
declare enum ObjectStatus {
  notDeployed = 0, // Not deployed
  deployed = 1, // Deployed
  changesToDeploy = 2, // Changes to deploy
  designTimeError = 3, // Design time error
  runTimeError = 4, // Run time errors
  pending = 5, // Deployment pending
  hasNoObjectStatus = 999, // Object does not provide status (example: kind 'sap.dis.dataflow')
}

interface ISharingObjectList {
  [name: string]: {
    description: string;
    typeName: string;
    sharedWith: IShareSpace[];
  };
}

interface IcsnObject {
  file: any;
  setBusy: (isBusy: boolean, sTitle?: string) => any;
  space: string;
  spaceSourceLange?: string;
  getNotifications?: Function;
  model;
  createFragmentElement: Function;
  doImportCallback?: Function;
  addDependentToView: Function;
  isReplicationBuilder?: boolean;
  erDiagramEditor?;
  onRepoObjectsSelected?;
  refreshCurrentView?: Function;
  objects?: any;
  isSingleSelectionAllowed?: boolean;
  showObjectLink?: boolean;
  folderAssignment?: string;
  isAdmin?: boolean;
}
interface IShareSpace {
  businessName?: string;
  qualifiedName: string; //space id
  id?: string; // UUID
  accessType: string;
  properties: {
    description: string;
  };
}

declare namespace sap {
  class galilei {
    static namespace(name: string, func?: Function): any;
    static ELK: object;
  }
}

declare namespace sap.galilei {
  class core {
    static defineClass(object: IDefineClassObject): any;
  }

  interface IDefineClassObject {
    fullClassName: string;
    parent?: any;
    fields?: { [key: string]: any };
    statics?: { [key: string]: any };
    methods?: { [key: string]: Function };
    properties?: { [key: string]: any };
    initialize?: Function;
  }
}

declare namespace sap.galilei {
  namespace core {
    function copyProperties(
      oObjectTarget: any,
      oObjectSource: any,
      bForceCopy: boolean,
      bClone: boolean,
      bDeepCopy: boolean
    ): void;
    class Event {
      static publish(sEvent: string, params: object, oScope: any, oTarget: any): any;
      static subscribe(
        sEvent: string,
        fnCallback: Function,
        oScope: any,
        oTarget: any,
        bAllowMultipleHandlers?: boolean
      ): any;
      static unSubscribeScope(sEvent: string, oScope: any);
    }
  }

  namespace ui5 {
    class GalileiModel extends sap.ui.model.json.JSONModel {
      constructor(options: any);
      public container?: any;
      public aggregatedValidations?: any;
      public setProperty(propertyName: string, propertyValue: any, oContext?: any): any;
      public refresh(bForceUpdate: boolean): void;
      public forceNoCache(): void;
      public updateBindings(bForce?: boolean): void;
      public setData({}): void;
    }

    class GalileiListBinding {
      public filter(aFilter: sap.ui.model.Filter[], sFilterType: string);
    }

    class Editor {
      constructor(options: any);
      public _editorExtensionClass: any;
      public _editor: any;
      public getInnerEditor(): sap.galilei.ui.editor.DiagramEditor;
      public setEditorExtensionClassName(sEditorExtensionClassName: string): Editor;
      public setDiagram(oDiagram: sap.galilei.ui.diagram.Diagram): void;
      public getDiagram(): sap.galilei.ui.diagram.Diagram;
      public getId(): string;
    }
  }

  namespace model {
    function getClass(className: string, resource?: any): any;
    function isInstanceOf(oInstance: any, sQualifiedName: string): boolean;
    function getResource(resourceId: string): sap.galilei.model.Resource;
    function isInUndoRedo(): boolean;

    interface BaseCollection<T = any> {
      isCollection: true;
      length: number;
      push(value: T): void;
      add(value: T): void;
      insert(index: number, value: T): void;
      removeAt(index: number): void;
      get(index: number): T;
      contains(value: T): boolean;
      clear(): void;
      toArray(): T[];
      indexOf(value: T): number;
      concat(value: T[]): void;
      selectObject(filters: any, machingLogic?: Function): T;
      selectAllObjects(filters: any, machingLogic?: Function): sap.galilei.model.BaseCollection;
      forEach<args>(callback: { (thisArgs: args, currentObject: T): void }, thisArg?: args): void;
      filter(callback: Function, thisArg?: any);
      map(callback: Function, thisArg?: any);
    }

    class ArrayCollection implements BaseCollection {
      isCollection: true;
      length: number;
      push(value: any): void;
      add(value: any): void;
      insert(index: number, value: any): void;
      removeAt(index: number): void;
      get(index: number): any;
      contains(value: any): boolean;
      clear(): void;
      toArray(): any[];
      indexOf(value: any): number;
      concat(elements: any[]): void;
      selectObject(filters: any, machingLogic: Function): any;
      selectAllObjects(filters: any, machingLogic?: Function): sap.galilei.model.BaseCollection;
      forEach<args>(callback: { (thisArgs: args, currentObject: any): void }, thisArg?: args): void;
      filter(callback: Function, thisArg?: any);
      map(callback: Function, thisArg?: any);
    }

    class ChildrenCollection extends ArrayCollection {
      deleteAll();
    }

    class dataTypes {
      public static gBlob: string;
      public static gBool: string;
      public static gChar: string;
      public static gDate: string;
      public static gDouble: string;
      public static gFloat: string;
      public static gGuid: string;
      public static gInteger: string;
      public static gLong: string;
      public static gShort: string;
      public static gString: string;
      public static gUInteger: string;
      public static gULong: string;
      public static gUShort: string;
    }

    class Resource {
      constructor(id?: string);
      isLoading?: boolean;
      isDisableUndoRedo?: boolean;
      enableCopyPaste?: boolean;
      canRedo(): boolean;
      canUndo(): boolean;
      public selectObject(criteria: any): any;
      public selectAllObjects(criteria: any): any;
      public clearListOfActions();
      public clearUndoStack();
      public undo(): any;
      public redo(): any;
      public clear(): any;
      public getObject(objectName: string): any;
      public applyUndoableAction(
        fnFunction: Function,
        sDescription: string,
        bIsProtectedFromUndo?: boolean,
        bBatchMode?: boolean,
        fnError?: Function,
        fnPostApplyAction?: Function
      ): void;
      public model: sap.galilei.common.Model;
    }

    class JSONReader {
      public load(oResource: Resource, oModelLoadData: any, batchMode?: boolean, listMetaResouces?: Resource[]);
    }

    class JSONWriter {
      public save(
        resource: Resource,
        params: {
          includeVolatileObjects?: boolean;
          isSaveDefaultValue?: boolean;
          objectIdMap?: any;
        }
      );
    }

    interface Object {
      classDefinition: any;
      qualifiedClassName: string;
      superClassName: string;
      isInternal: boolean;
      isVolatile: boolean;
      isAsync: boolean;
      name: string;
      displayName: string;
      objectId: string;
      tagValues: string;
      isProxy: boolean;
      proxiedObjectId: string;
      proxiedObjectResourceId: string;
      isProxyAutoResolve: boolean;
      isDeleting: boolean;
      isAlive: boolean;
      level: number;
      canCopy: boolean;
      resource: sap.galilei.model.Resource;
      children: sap.galilei.model.Object;
      container: sap.galilei.model.Object;
      rootContainer: sap.galilei.model.Object;
      proxiedObject: sap.galilei.model.Object;
      type: string;
      unhandledCsn?: any;
      deleteObject(): void;
      defaultOnBeforeDelete(): void;
      isInstanceOf(sClassName: string): boolean;
    }
  }

  namespace common {
    class Model {
      public resource: sap.galilei.model.Resource;
      name: string;
      displayName: string;
      isModel: boolean;
      packages: any;
      diagrams: sap.galilei.model.Object;
      qualifiedClassName: string;
      classDefinition: any;
      container: any;
      rootContainer: any;
      objectNameDisplay: any;
    }

    interface LinkObject extends sap.galilei.model.Object {
      isLinkObject: boolean;
      source: sap.galilei.model.Object;
      target: sap.galilei.model.Object;
    }
  }

  namespace ui {
    namespace common {
      enum PortSide {
        west,
        east,
        north,
        south,
      }

      enum LinkDirection {
        west,
        east,
        north,
        south,
      }

      enum LineStyle {
        rounded,
        square,
        straight,
      }

      enum DashStyle {
        dot,
        dash,
        solid,
      }

      enum HorizontalAlignment {
        center,
      }

      enum VerticalAlignment {
        middle,
      }

      enum DockPosition {
        topLeft,
        bottomLeft,
      }

      enum ShapeLayoutModes {
        icon,
      }

      class library {
        static defineLibrary: any;
      }

      class TransitionDefinition {
        constructor(op: any);
      }

      namespace shape {
        enum RoundedSide {
          left,
          right,
          top,
          bottom,
        }
      }

      namespace style {
        class StyleSheet {
          constructor(options: any);
        }

        class LinearGradient {
          constructor(options: any);
          public createGradient(params: any): any;
        }

        class DropShadow {
          constructor(options: any);
          public create(viewer: any): DropShadow;
        }

        class Theme {
          static getInstance(): any;
        }
      }

      namespace svg {
        class Viewer {
          public static focusedElement: any;
        }
      }

      class FileManager {
        static open(oFile: File, fnOnOpen: Function, fnOnError: Function, sFormat: string): void;
        public static saveAs(blob: string, fileName: string);
      }
      namespace command {
        class Command {}

        class CommandRegistry {
          public static add(commandId: string, command: any);
        }
      }

      class Blob {
        public static createBlobFromString(string: string, type: string);
      }
    }

    namespace symbol {
      function getObjectSymbols(oObject: any, oDiagram: any, bIncludedHidden?: boolean, bGetFirst?: boolean);
      function createObjectSymbol(oDiagram: any, oEntity: any);
      function createLinkObjectSymbol(oDiagram: any, oEntity: any);
      function createAllObjectSymbols(oDiagram: any, oResource: any);
      enum EdgeType {
        ALL,
      }
      class LinkSymbolLayout {
        static removeAllLinkSymbolsOverlap: any;
      }
    }

    namespace editor {
      function defineDiagramEditorExtension(options: any): any;
      class DiagramEditor {
        constructor(options: any);
        public isReadOnly: boolean;
        public showOnlySecondaryEditor: boolean;
        public enableCopyPaste: boolean;
        public isDisableUndoRedo: boolean;
        public selectSymbolAfterCreate: boolean;
        public controller;
        public selectedSymbols: any;
        public viewer: any;
        public previewService: any;
        public model: any;
        public resource: any;
        public extension: any;
        public diagram: any;
        public transitionDefinition: any;
        public grayoutNonConnectedSymbols: boolean;
        public showConnectedSymbols: boolean;
        public isContextPadMenuStyle: boolean;
        public enableSymbolEditingDelay: boolean;
        public registerAllSymbolEvents(): void;
        public createSymbolAndObject(
          sSymbolClass: string,
          oSymbolParams,
          oSymbolParent,
          nSymbolIndex: number,
          sObjectClass: string,
          oObjectParams,
          oObjectParent,
          sObjectReference: string,
          nObjectIndex: number
        ): any;
        public showGlobalView(bUseAnimation?: boolean): DiagramEditor;
        public selectTool(sToolName: string, bUnselectSymbols: boolean, oCustomParams?: any): any;
        public undo(): void;
        public redo(): void;
        public deleteSelectedSymbols(
          bDeleteObject?: boolean,
          bPreserveLink?: boolean,
          bUseUndoAction?: boolean,
          bTriggerPostDelete?: boolean,
          bCheckCanDelete?: boolean
        ): void;
        public postCreateSymbol(oSymbol: any): any;
        public drawSymbol(oSymbol, oTransitionDef?, aAttributes?): void;
        public selectPointerTool(bUnselectSymbols?: boolean): any;
        public bringSymbolToView(oSymbol: any, bPreserveZoomScale: boolean, bCenterInView: boolean): any;
        public bringSymbolsToView(aSymbols: any[], bPreserveZoomScale: boolean, bCenterInView: boolean): any;
        public selectSymbol(
          oSymbol: any,
          bDraw?: boolean,
          oTransitionDef?: any,
          bNotify?: boolean,
          bBringToView?: boolean,
          bShowContextButtonPad?: boolean,
          bForce?: boolean
        ): void;
        public selectSymbols(
          aSymbols: any[],
          bSelectSubSymbols?: boolean,
          bDraw?: boolean,
          oTransitionDef?: any,
          bShowContextButtonPad?: boolean
        );
        public unselectSymbol(
          oSymbol,
          bDraw?: boolean,
          oTransitionDef?,
          bNotify?: boolean,
          bNotUpdateMultiSelectionContextButtonPad?: boolean
        ): void;
        public canDeleteSymbol(oSymbol): boolean;
        public unselectAllSymbols(): void;
        public unselectSymbols(aSymbols, bDraw?: boolean, oTransitionDef?, oExcludeSybmol?, bNotify?: boolean): void;
        public getOrCreateMultiSelectionTracker(): any;
        public deleteAllSymbols(): void;
        public drawAllSymbols(): void;
        public unhighlightAllSymbols(): void;
        public highlightSymbol(oSymbol: any, oStylesheet: any): void;
        public isSymbolInView(oSymbol: any): boolean;
        public bringSymbolToView(oSymbol: any, bPreserveZoomScale: boolean, bCenterInView: boolean): void;
      }

      namespace command {
        class Command {}
      }
      namespace tool {
        function getTool(id: string): any;
        enum Types {
          createLinkSymbolTool,
          createNodeSymbolTool,
          deleteNodeSymbolTool,
        }
      }

      namespace layout {
        function DiagramAutoLayout(): void;
        function DiagramMultiAutoLayout(): void;
        export namespace DiagramAutoLayout {
          export class ELK_ALGORITHMS {
            public static ELK_STRESS: string;
            public static ELK_FORCE: string;
            public static ELK_LAYERED: string;
            public static ELK_BOX: string;
            public static ELK_MRTREE: string;
            public static ELK_RADIAL: string;
            public static ELK_RANDOMIZER: string;
            public static ELK_RECTPACKING: string;
            public static ELK_SPORE_COMPACTION: string;
            public static ELK_SPORE_SPORE_OVERLAP: string;
          }

          export class LAYOUTER_NAMES {
            public static ELK: string;
          }
        }
      }
      namespace widget {
        class Widget {
          constructor(options: any);
        }
      }
    }

    namespace diagram {
      class Diagram {
        constructor(options: any);
        public model: sap.galilei.common.Model;
      }
    }
  }

  namespace impactAnalysis.generic {
    function createModelAndRootObject(options: any): any;
  }

  namespace d3 {
    function select(id: string): any;
  }
}

declare namespace sap.cdw {
  namespace lineageAnalysis {
    class ServiceProvider {
      static getInstance(): ServiceProvider;
      _IA_Data: any;
      user: any;
      public convertObjectDependencies(dependencies: any[]): any;
      public getObjectName(object: any): string;
    }
  }

  // Common metamodel definition sap.cdw.commonmodel
  namespace commonmodel {
    class CsnToModel {
      static getInstance(): CsnToModel;
      postProcessElement(entity, element, postProcessings: any[]): void;
      updateObjectFromCsn(object, csn, force?: boolean, oFileObject?, fnCallback?);
      addObjectMap(name, entity);
      importObjectsFromCSN(model, definitions, properties: string[]);
      updateAssociation(associationModel, associationCsn, singleMode: boolean);
      importContextFromCsn(model, contextName, csnDefinitiono);
      getEntityElements(entity, csnElements);
      getDefaultPropertyMapping(oModelObject, oCsnObject, bForce?);
      getAnnotationsForObject(oModelObject);
      getAnnotationsForClassDefinition(classDefinition);
      getHandledAnnotationsForEntity(entity);
      setUnhandledCsnForObject(object, csn);
      getCsnEditParams(entity);
      updateEntityAnnotations(entity, csn);
    }

    namespace sharedDefinitions {
      interface IBusinessDefinitionProperties {
        businessDefinition: string;
        businessDefinitionPurpose: string;
        businessDefinitionContact: string;
        businessDefinitionResponsibleTeam: string;
        businessDefinitionTags: string;
      }

      interface IBusinessDefinitionMethods {
        addBusinessDefinitionTags: { (...addedTags: any[]): void };
        removeBusinessDefinitionTags: { (...removedTags: any[]): void };
      }

      interface INamedObjectProperties {
        name: string;
        label: string;
        comment: string;
        displayName: string;
        writeAccess: boolean;
        isReadOnlyObject: boolean;
      }

      interface IModelProcessingProperties {
        processes: any[];
        currentProcessDescription: string;
      }

      interface IModelRepoPackageProperties {
        packageValue: string;
        packageStatus: string;
      }

      interface IOrdProperties {
        connection: string;
        ordId: string;
      }

      interface IProcess {
        id: string;
        description: string;
      }

      interface IRepositoryObjectProperties {
        documentId: string;
        creator: string;
        creationDate: number;
        modifier: string;
        modificationDate: number;
        deploymentDate: number;
        deploymentStatus: DeploymentStatus;
        deploymentErrorCode: string;
        deploymentErrorDescription: string;
        "#objectStatus": ObjectStatus;
        "#deploymentExecutionStatus": string;
        releaseStateValue?: string;
        releaseContractCSN?: any;
        repositoryCSN?: any;
        _repositoryCSN?: any;
      }

      interface IDependentEntityProperties {
        impact: any;
      }

      interface IBaseEntityProperties extends INamedObjectProperties, IBusinessDefinitionProperties {
        technicalName: string;
        elementCount: number;
        parameterCount: number;
        assocCount: number;
        type: string;
      }

      interface IBaseEntityReferences {
        elements: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.BaseElement>;
        orderedElements: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.BaseElement>;
      }

      interface IBaseEntityElementsMethods {
        initElementsIndexOrder: { (): void };
        setDataCategory: { (dataCategory: string): void };
        changeMeasuresToAttributes: { (): void };
        changeAttributesToMeasures: { (): void };
        createElement: { (params: Partial<ermodeler.Element>, index?: number): void };
        deleteSelectedElements: { (): void };
      }

      interface IBaseEntityMethods extends IBusinessDefinitionMethods, IBaseEntityElementsMethods {
        isUnionView(): boolean;
        updateReleaseContractCSN(properties: any);
      }

      interface IRepositoryObjectMethods {
        setObjectStatus(objectStatus, deploymentExecutionStatus?: string): void;
      }

      interface IEntityProperties extends IBaseEntityProperties, IRepositoryObjectProperties {
        useAs: string;
        contextDisplayName: string;
        crossSpaceName: string;
        crossSpaceLabel: string;
        crossSpaceDisplayName: string;
        crossSpaceEntityName: string;
        qualifiedName: string;
        dbViewType: string;
        identifyingValue: string;
        isQualified: boolean;
        shortName: string;
        isDataLayer: boolean;
        isBusinessLayer: boolean;
        isEntity: boolean;
        isTable: boolean;
        isView: boolean;
        isDataAccessControl: boolean;
        hasDataAccessControl: boolean;
        hasFilterDataAccessControl: boolean;
        entityIndex: number;
        remote: any;
        localSchema: any;
        deltaTable: any;
        crossSpace: any;
        isRemote: boolean;
        isDeltaTable: boolean;
        isDeltaOutboundOn: boolean;
        isLTF: boolean;
        isDataTransportAllowed?: boolean;
        partitionedColumns?: any[];
        deltaTableName: string;
        deltaOutboundTableName: string;
        hasNoKeyWithPartitions: boolean;
        isLocalSchema: boolean;
        isCrossSpace: boolean;
        isLocal: boolean;
        isPersistenceExists: boolean;
        isPersistenceSkip: boolean;
        isSapReserved: boolean;
        isPinToMemoryEnabled: boolean;
        isToolingHidden: boolean;
        remoteReadyStatus: string;
        isEditable: boolean;
        isNew: boolean;
        dataCategory: string;
        isDataCategoryChanged: boolean;
        isDimension: boolean;
        isFact: boolean;
        isCube: boolean;
        isMeasure: boolean;
        isHierarchy: boolean;
        isHierarchyWithDirectory: boolean;
        hierarchyWithDirectory: any;
        isText: boolean;
        isBasicMeasure: boolean;
        isCalculatedMeasure: boolean;
        isKpi: boolean;
        isQuery: boolean;
        isAllowConsumption: boolean;
        isUseOLAPDBHint: boolean;
        isRestrictAccess: boolean;
        csn?: ICsn;
        unresolvedAssociations: any[];
        isHiddenInUi: boolean;
        unhandledCsn?: ICsn;
        isRemoteDetailsLoaded?: boolean;
        releaseState?: string;
        initialState?: string;
        releaseDate?: string;
        deprecationDate?: string;
        decommissioningDate?: string;
        successorObject?: string;
        canRevert?: boolean;
        revertErrors?: string;
        contentOwner?: string;
        dimensionType?: string;
      }

      interface IGenAIEntityProperties {
        hasAIChange?: boolean;
        hasAIChangeOnElements?: boolean;
        dataCategoryBeforeAI?: string;
        dataCategoryAI?: string;
        hasAIChangeOnDataCategory?: boolean;
        hasAIChangeOnAttributes?: boolean;
        hasAIChangeOnMeasures?: boolean;
        reasonDataCategory?: string;
        reviewInfoDataCategory?: string;
        possibleDataCategoriesAI?: any;
        dimensionElementsStatic?: any;
      }

      interface IGenAIElementProperties {
        hasAIChange?: boolean;
        isKeyBeforeAI?: boolean;
        isKeyAI?: boolean;
        hasAIChangeOnIsKey?: boolean;
        semanticTypeBeforeAI?: string;
        semanticTypeAI?: boolean;
        hasAIChangeOnSemanticType?: boolean;
        hasAIChangeOnIsMeasure?: boolean;
        isMeasureBeforeAI?: boolean;
        isMeasureAI?: boolean;
        reason?: string;
        reviewInfo?: any;
        possibleSemanticTypesAI?: any;
        labelElementBeforeAI?: string;
        labelElementAI?: string;
        hasAIChangeOnLabelElement?: boolean;
        unitTypeElementBeforeAI?: string;
        unitTypeElementAI?: string;
        hasAIChangeOnUnitTypeElement?: boolean;
        foreignKeyBeforeAI?: string;
        foreignTextBeforeAI?: string;
      }

      interface IEntityReferences extends IBaseEntityReferences {
        hierarchyNameColumn: sap.cdw.commonmodel.BaseElement;
        fiscalTimeSettingsStart?: sap.cdw.commonmodel.BaseElement;
        fiscalTimeSettingsEnd?: sap.cdw.commonmodel.BaseElement;
        context: sap.cdw.commonmodel.Context;
        rootContext: sap.cdw.commonmodel.Context;
        measureElements: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element & { isMeasure: true }>;
        // @deprecated
        measures: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element & { isMeasure: true }>;
        dimensionElements: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element & { isMeasure: false }>;
        filterableColumns?: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element>;
        allowedOperators?: any[];
        sourceAssociations: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Association>;
        targetAssociations: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Association>;
        allAssociations?: sap.cdw.commonmodel.Association[];
        hierarchies: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.BaseHierarchy>;
        allHierarchies?: any[];
        hierarchyParentNode: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element>;
        hierarchyChildNode: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element>;
        entityMapping: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.EntityMapping>;
        viewDataAccessControls: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.ViewDataAccessControl>;
        aggregatedValidations: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.ValidationStatus>;
        aggregatedValidationsForTableEditor?: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.ValidationStatus>;
        representativeKey: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element>;
        compoundKeySequence: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element>;
      }

      interface IEntityMethods extends IHierarchyMethods {
        validate(): string;
        validateHierarchySource(): string;
      }

      interface ITableProperties {
        isTable: boolean;
        isView: boolean;
        isDataAccessControl: boolean;
        schema: string;
        entityName: string;
        hasData?: boolean;
        dataAccess?: string;
        latency?: string;
        canDeploy?: boolean;
        currentDataAccess?: string;
        replicationStatus?: string;
        replicationType?: string;
        location?: string;
        dpAgent?: string;
        adapter?: string;
        remoteSourceName?: string;
        partitioningExists?: boolean;
        isRefresh?: boolean;
        isUserNotAuthorized?: boolean;
        isRemoteTableRequestFailed?: boolean;
        changeManagementInfo?: any;
        validationMessages?: any;
        statisticsExists?: boolean;
      }

      interface IViewProperties {
        isTable: boolean;
        isView: boolean;
        isDataAccessControl: boolean;
      }

      interface IDataAccessControlProperties {
        isTable: boolean;
        isView: boolean;
        isDataAccessControl: boolean;
        errorText: string;
        sourceEntityName: string;
        principalElementName: string;
      }

      interface IFactProperties {
        dataCategory: string;
      }

      interface IDimensionProperties {
        dataCategory: string;
      }

      interface ICubeProperties {
        dataCategory: string;
      }

      interface IBaseMeasureProperties {
        dataCategory: string;
      }

      interface IBasicMeasureProperties {}

      interface ICalculatedMeasureProperties {}

      interface IQueryProperties {
        dataCategory: string;
      }

      interface IDataTypeProperties {
        dataType: string;
        primitiveDataType: string;
        displayType: string;
        primitiveDisplayType: string;
        nativeDataType?: string;
        length?: number;
        precision?: number;
        scale?: number;
        srid?: number;
      }

      interface IElementFilterProperties {
        filterSelectionType: string;
        filterMandatory: boolean;
        filterDefaultValue: string;
        filterDefaultValueHigh: string;
        filterHidden: boolean;
        filterMultipleSelections: boolean;
      }
      interface IRemoteFilterItemProperties {
        name: string;
        operation?: string;
        value1?: string;
        value2?: string;
        dataType?: string;
        lowValueState?: string;
        lowValueStateText?: string;
        highValueState?: string;
        highValueStateText?: string;
        length?: number;
        precision?: number;
        scale?: number;
        possibleFilterOperations?: string[];
        filterableColumns?: any;
        generatedKey?: string;
      }

      interface IPartitionProperties {
        id?: string;
        partitionType?: string;
        isKey?: boolean;
        columnLabel?: string;
        partitionDatatype?: string;
        noOfHashPartitions?: number;
      }

      interface IPartitionRangeProperties {
        partitionId?: string;
        low?: string;
        high?: string;
      }

      interface IBaseTypeProperties extends INamedObjectProperties {
        isType: boolean;
        contextDisplayName?: string;
        qualifiedName: string;
      }

      interface IBaseElementProperties extends INamedObjectProperties, IDataTypeProperties {
        name: string;
        label: string;
        displayName: string;
        identifyingProperties: string[];
        identifyingValue: string;
        qualifiedNameAsArray: string[];
        isKey: boolean;
        foreignKey: string;
        foreignHierarchy: string;
        foreignText: string;
        isNotNull: boolean;
        default?: string;
        indexOrder: number;
        isEditable: boolean;
        allowedExpressions?: string[];
        filterEnabled?: string;
      }

      interface IBaseTypeReferences {
        context?: sap.cdw.commonmodel.Context;
        rootContext?: sap.cdw.commonmodel.Context;
        baseType?: sap.cdw.commonmodel.BaseType;
        rootType?: sap.cdw.commonmodel.BaseType;
        allBaseTypes: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.BaseType>;
        types: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.BaseType>;
        elements: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element>;
      }

      interface IBaseElementReferences {}

      interface IBaseElementMethods {}

      interface ISimpleTypeProperties extends IDataTypeProperties {
        isSimpleType: boolean;
      }

      interface IElementProperties {
        newName: string;
        isCalculated: boolean;
        isMeasure: boolean;
        isDimension: boolean;
        isVisible: boolean;
        isAuxiliary: boolean;
        isAggregatable: boolean;
        isLanguageColumn: boolean;
        isTextEntity: boolean;
        isText: boolean;
        isLabelElement: boolean;
        isLinkToText: boolean;
        isLinkToWrongColumnOfText: boolean;
        isLanguage: boolean;
        isCDCColumn: boolean;
        isPartitionedColumn: boolean;
        isSemanticElemRef: boolean;
        displaySemanticType: string;
        semanticType: string;
        defaultAggregation: string;
        dimensionSource: string;
        textSource: string;
        displayType: string;
        dataType: string;
        // _DP_ Analytic Measures
        isComputedMeasure: boolean;
        isFormula: boolean;
        isRestrictedMeasure: boolean;
        isExceptionAggregation: boolean;
        isCountDistinct: boolean;
        isAnalyticMeasure: boolean;
        exceptionAggregationType: string;
        unhandledCsn?: ICsn;
        isExcluded: boolean;
      }

      interface IElementReferences {
        labelElement: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element>;
        unitTypeElement: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element>;
        possibleSemanticTypes: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.SemanticType>;
        possibleSemanticTypesAI: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.SemanticType>;
        baseType?: sap.cdw.commonmodel.BaseType;
        rootType?: sap.cdw.commonmodel.BaseType;
        allBaseTypes: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.BaseType>;
        possibleDataTypes: any[];
        possibleLabelColumns: any[];
        possibleLabelColumnsAI: any[];
        possibleSemanticTypeElements: any[];
        possibleSemanticTypeElementsAI: any[];
        possibleSridValues: any[];
        aggregationTypes: any[];
        parentElementHierarchies: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.ParentChildHierarchy>;
        childElementHierarchies: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.ParentChildHierarchy>;
        sourceMeasure: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element>; // _DP_ Source Measure for Restricted Measure
        referenceAttributes: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element>; //_DP_ ReferenceAttributes for Count Distinct
        possibleSourceMeasures: any[];
        possibleExceptionAggregationTypes: any[];
        possibleExceptionAggregationSourceTypes: any[];
        exceptionAggregationAttributes: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element>; // _DP_ Selected exception aggregation attributes
        attributeElements: any[]; // _DP_ All Attributes for Exception Aggregation
        allowedFilterOperationsRef: any[];
      }

      interface IElementMethods {
        validate(): string;
        changeAttributeToMeasure: any;
        changeMeasureToAttribute: any;
        onBeforeDelete: Function;
      }

      interface IHierarchyMethods {
        addHierarchy: Function;
        removeHierarchies: Function;
      }

      interface IBaseHierarchyProperties {
        name: string;
        label: string;
        unhandledCsn?: any;
      }

      interface IParentChildHierarchyReferences {
        parentElement: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element>;
        childElement: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element>;
      }

      interface ILevelBasedHierarchyReferences {
        levels: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.LevelElement>;
        orderedLevels: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.LevelElement>;
      }

      interface IAssociationReferences {
        source: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Entity>;
        target: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Entity>;
        mappings: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.ElementMapping>;
        sourceElement: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.BaseElement>;
        targetElement: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.BaseElement>;
      }

      interface ICardinalityReferences {
        possibleCardinalityValues: { key: string; text: string }[];
      }

      interface IViewDataAccessControlReferences {
        view: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Entity>;
        dataAccessControl: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Entity>;
        mappings: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.ElementMapping>;
      }

      interface IModelProperties {
        isDataLayer?: boolean;
        isBusinessLayer?: boolean;
        namespace: string;
        businessTeamsArray?: any[];
        unresolvedAssociations: any[];
        availableContexts: any[];
        availableSimpleTypes: any[];
        isNew: boolean;
      }

      interface IModelReferences {
        types: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.BaseType>;
        simpleTypes: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.SimpleType>;
        contexts: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Context>;
        allContexts: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Context>;
        entities: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Entity>;
        tables: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Entity & { isTable: true }>;
        views: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Entity & { isView: true }>;
        dataAccessControls: sap.galilei.model.BaseCollection<
          sap.cdw.commonmodel.Entity & { isDataAccessControl: true }
        >;
        facts: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Entity & { dataCategory: "FACT" }>;
        dimensions: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Entity & { dataCategory: "DIMENSION" }>;
        cubes: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Entity & { dataCategory: "CUBE" }>;
        measures: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Entity & { dataCategory: "MEASURE" }>;
        queries: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Entity & { dataCategory: "QUERY" }>;
        associations: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Association>;
        aggregatedValidations: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.ValidationStatus>;
      }

      interface IModelMethods {
        getModelImpl(): any;
        validate(): Promise<any>;
      }

      interface IContextProperties {
        qualifiedName: string;
        displayName: String;
        isContext: boolean;
        isSapReserved: boolean;
        crossSpace?: any;
        isCrossSpace: boolean;
      }

      interface IContextReferences {
        contexts: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Context>;
        allContexts: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Context>;
        parentContext: sap.cdw.commonmodel.Context;
        rootContext: sap.cdw.commonmodel.Context;
        entities: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Entity>;
        types: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.BaseType>;
      }

      interface IContextMethods {}

      interface IValidationReferences {
        validations: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.ValidationStatus>;
        aggregatedValidations: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.ValidationStatus>;
      }
    }

    class BaseModel extends sap.galilei.common.Model {
      constructor(oResource: sap.galilei.model.Resource, params?: any, id?: string);
      label: string;
      documentId: string;
      owner: string;
      modificationDate: string;
      deploymentDate: string;
      isNew: boolean;
      objectNameDisplay: string;
      validations: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Validation>;
      aggregatedValidations: { status: string; validations: sap.cdw.commonmodel.Validation[] };
      validate(): Promise<any>;
      clearValidation(sPropertyName?: string): void;
    }

    interface BaseObject extends sap.galilei.model.Object {
      label: string;
      identifyingValue: string;
      identifyingProperties: string[];
      validations: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.ValidationStatus>;
      aggregatedValidations: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.ValidationStatus>;
      clearValidation(sPropertyName?: string): void;
      unhandledCsn?: any;
    }

    interface BaseLinkObject extends sap.galilei.common.LinkObject {
      label: string;
      identifyingValue: string;
      identifyingProperties: string[];
      validations: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.ValidationStatus>;
      aggregatedValidations: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.ValidationStatus>;
      clearValidation(sPropertyName?: string): void;
    }

    interface BaseType extends BaseObject {
      isType: boolean;
      qualifiedName: string;
      context?: sap.cdw.commonmodel.Context;
      rootContext?: sap.cdw.commonmodel.Context;
      baseType?: sap.cdw.commonmodel.BaseType;
      rootType?: sap.cdw.commonmodel.BaseType;
      allBaseTypes: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.BaseType>;
      types: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.BaseType>;
      elements: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element>;
    }

    interface BaseElement extends sap.cdw.commonmodel.BaseObject {
      newName: string;
      identifyingProperties: string[];
      identifyingValue: string;
      qualifiedNameAsArray: string[];
      label: string;
      dataType: string;
      primitiveDataType: string;
      displayType: string;
      primitiveDisplayType: string;
      nativeDataType: string;
      length?: number;
      precision?: number;
      scale?: number;
      srid?: number;
      isKey: boolean;
      isNotNull: boolean;
      default?: any;
      isCalculated: boolean;
      isSelected: boolean;
      isMeasure: boolean;
      isDimension: boolean;
      isVisible: boolean;
      isAuxiliary: boolean;
      isLanguageColumn: boolean;
      isLanguage: boolean;
      isTextEntity: boolean;
      isText: boolean;
      isLinkToText: boolean;
      isLinkToWrongColumnOfText: boolean;
      isSemanticElemRef: boolean;
      displaySemanticType: string;
      semanticType: string;
      defaultAggregation: string;
      dimensionSource: string;
      textSource: string;
      Rank: number;
      foreignKey: string;
      foreignHierarchy: string;
      foreignText: string;
      indexOrder: number;
      isEditable: boolean;
      filterSelectionType: string;
      filterDefaultValue: string;
      filterDefaultValueHigh: string;
      filterMandatory: boolean;
      filterMultipleSelections: boolean;
      filterHidden: boolean;
      possibleSemanticTypes: { [key: string]: { key: string; text: string; type: string } };
      possibleDataTypes: { [key: string]: { key: string; text: string; uiKey: string } };
      possibleSridValues: { [key: string]: { key: string; text: string; uiKey: string } };
      aggregationTypes: { key: string; text: string }[];
      clearValidation(sPropertyName?: string): void;
      defaultValue: any;
      value: any;
    }

    interface BaseEntity
      extends sap.cdw.commonmodel.BaseObject,
        sharedDefinitions.IBaseEntityProperties,
        sharedDefinitions.IBusinessDefinitionProperties,
        sharedDefinitions.IBaseEntityElementsMethods,
        sharedDefinitions.IBusinessDefinitionMethods {
      elements: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element>;
      selectedElements: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element>;
      orderedElements: sap.cdw.ermodeler.Element[];
      measures: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element & { isMeasure: true }>;
      measureElements: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element & { isMeasure: true }>;
      dimensionElements: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element & { isMeasure: false }>;
      sourceAssociations: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Association>;
      targetAssociations: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Association>;
      hierarchies: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.BaseHierarchy>;
      aggregatedValidations: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.ValidationStatus>;
      remoteReadyStatus: string;
      elementCount: number;
      parameterCount: number;
      assocCount: number;
      isUnionView(): boolean;
    }

    class Model extends BaseModel {
      isNew: boolean;
      contexts: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Context>;
      allContexts: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Context>;
      types: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.BaseType>;
      simpleTypes: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.SimpleType>;
      entities: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Entity>;
      tables: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Table & { isTable: true }>;
      views: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.View & { isView: true }>;
      dataAccessControls: sap.galilei.model.BaseCollection<
        sap.cdw.commonmodel.DataAccessControl & { isDataAccessControl: true }
      >;
      facts: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Entity & { dataCategory: "FACT" }>;
      dimensions: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Entity & { dataCategory: "DIMENSION" }>;
      cubes: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Entity & { dataCategory: "CUBE" }>;
      measures: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Entity & { dataCategory: "MEASURE" }>;
      queries: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Entity & { dataCategory: "QUERY" }>;
      associations: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Association>;
      unresolvedAssociations: any[];
      availableContexts: any[];
      availableSimpleTypes: any[];
      skipEntitiesUniquenessValidation: boolean;
      editorSettings?: any; // Holds in some case editor settings section
    }

    interface SimpleType extends BaseType {
      isSimpleType: boolean;
      dataType: string;
      primitiveDataType: string;
      displayType: string;
      primitiveDisplayType: string;
      nativeDataType: string;
      length?: number;
      precision?: number;
      scale?: number;
      srid?: number;
      unhandledCsn?: any;
    }

    interface Context extends BaseObject {
      contexts: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Context>;
      allContexts: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Context>;
      entities: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Entity>;
      parentContext?: sap.cdw.commonmodel.Context;
      rootContext: sap.cdw.commonmodel.Context;
      isContext: boolean;
      qualifiedName: string;
      isSapReserved: boolean;
      crossSpace?: { schema?: string; space?: string };
      isCrossSpace: boolean;
      unhandledCsn?: any;
    }

    interface Entity
      extends BaseEntity,
        sharedDefinitions.IRepositoryObjectProperties,
        sharedDefinitions.IRepositoryObjectMethods {
      isDataTransportAllowed?: boolean;
      partitionedColumns: any[];
      fileStorage?: boolean;
      ltfTable?: Entity;
      isRemote?: boolean;
      impact: any;
      shortName: string;
      qualifiedName: string;
      isQualified: boolean;
      isDataLayer: boolean;
      isBusinessLayer: boolean;
      isTable: boolean;
      isView: boolean;
      isDataAccessControl: boolean;
      dataCategory: string;
      dbViewType: string;
      isFact: boolean;
      isDimension: boolean;
      isText: boolean;
      isCube: boolean;
      isMeasure: boolean;
      isHierarchy: boolean;
      isQuery: boolean;
      isPersistenceExists: boolean;
      isPersistenceSkip: boolean;
      isSapReserved: boolean;
      isAllowConsumption: boolean;
      isUseOLAPDBHint: boolean;
      isRestrictAccess: boolean;
      isDeltaOutboundOn: boolean;
      isNew: boolean;
      isLocal: boolean;
      isLTF?: boolean;
      dimensionType?: string;
      deltaTableName: string;
      isToolingHidden?: boolean;
      remote?: { connection?: string; table?: string };
      deltaTable?: { type?: string; mode?: string; timeStamp?: string };
      localSchema?: { schema?: string; table?: string };
      crossSpace?: { spaceName?: string; spaceLabel?: string };
      isCrossSpace: boolean;
      crossSpaceName: string;
      crossSpaceLabel: string;
      crossSpaceDisplayName: string;
      crossSpaceEntityName: string;
      csn?: ICsnDefinition;
      elements: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element>;
      parameters?: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Parameter>;
      analyticParameters?: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Parameter>;
      viewDataAccessControls: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.ViewDataAccessControl>;
      partitions?: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Partitions>;
      orderedElements: sap.cdw.commonmodel.Element[];
      context?: sap.cdw.commonmodel.Context;
      rootContext?: sap.cdw.commonmodel.Context;
      updateReleaseContractCSN(properties: any);
      validate(async?: boolean): string;
      unresolvedAssociations: any;
      allAssociations: any;
      getRepositoryCSN(): any;
      unhandledCsn?: any;
      isHierarchyWithDirectory?: boolean;
      hierarchyWithDirectory?: any;
      releaseState?: string;
      initialState?: string;
      releaseDate?: string;
      deprecationDate?: string;
      decommissioningDate?: string;
      successorObject?: string;
      canRevert?: boolean;
      revertErrors?: string;
      packageValue?: string;
      packageStatus?: string;
      fiscalTimeSettingsStart?: sap.cdw.commonmodel.Element;
      fiscalTimeSettingsEnd?: sap.cdw.commonmodel.Element;
    }

    interface Table extends Entity {
      contentOwner?: any;
      ltfTable?: any;
      remote?: { connection?: string; table?: string };
      localSchema?: { schema?: string; table?: string };
      deltaTable?: { type?: string; mode?: string; timestamp?: string };
      partitions?: any;
      fileStorage?: any;
      remoteFilters?: any;
      remoteFilter?: any;
      representativeKey: sap.cdw.commonmodel.Element[];
      compoundKeySequence: sap.cdw.commonmodel.Element[];
    }

    interface View extends Entity {}

    interface DataAccessControl extends Entity {
      errorText?: string;
      sourceEntityName?: string;
      principalElementName?: string;
    }

    interface Fact extends Entity {}

    interface Dimension extends Entity {}

    interface Cube extends Entity {}

    interface Measure extends Entity {}

    interface Query extends Entity {}

    interface Element extends BaseElement {
      parsedExpression: any;
      dataCleansingGeoValue: string;
      filterSelectionType: any;
      validate(): string;
      unitTypeElement?: sap.cdw.commonmodel.Element;
      labelElement?: sap.cdw.commonmodel.Element;
      baseType?: sap.cdw.commonmodel.BaseType;
      rootType?: sap.cdw.commonmodel.BaseType;
      allBaseTypes: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.BaseType>;
      isRemoved?: boolean;
      savedCsn?: any;
      unhandledCsn?: any;
      // _DP_ Analytic Measures
      isComputedMeasure?: boolean;
      isFormula?: boolean;
      isRestrictedMeasure?: boolean;
      isCountDistinct?: boolean;
      isAnalyticMeasure?: boolean;
      isExceptionAggregation?: boolean;
      exceptionAggregationAttributes: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element>;
      exceptionAggregationType: string;
      sourceMeasure: sap.cdw.commonmodel.Element;
      isExcluded: boolean;
    }

    interface Parameter extends BaseElement {
      valueHelpDefinition: sap.cdw.commonmodel.ReferenceEntity;
    }

    interface ReferenceEntity extends BaseObject {
      entity?: sap.cdw.commonmodel.Entity;
      element?: sap.cdw.commonmodel.Element;
      entityName?: string;
      elementName?: string;
    }

    interface Association extends BaseLinkObject {
      type: string;
      minCardinality: string;
      maxCardinality: string;
      source: sap.cdw.commonmodel.Entity;
      target: sap.cdw.commonmodel.Entity;
      sourceElement: sap.cdw.commonmodel.Element;
      targetElement: sap.cdw.commonmodel.Element;
      mappings: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.ElementMapping>;
      csn?: any;
      isHierarchy: boolean;
      isHierarchyWithDirectory: boolean;
      isText: boolean;
      validate(bSkipRefreshDecorator?: boolean): string;
      initiatedFrom?: string;
      unhandledCsn?: any;
    }

    interface ViewDataAccessControl extends BaseLinkObject {
      view: sap.cdw.commonmodel.View;
      dataAccessControl: sap.cdw.commonmodel.DataAccessControl;
      mappings: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.ElementMapping>;
      csn?: any;
      validate(bSkipRefreshDecorator?: boolean): string;
    }

    interface BaseHierarchy extends BaseObject {
      name: string;
      label: string;
      signature: string;
      validate(): string;
    }

    interface ParentChildHierarchy extends BaseHierarchy {
      childElement: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element>;
      parentElement: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element>;
    }

    interface LevelBasedHierarchy extends BaseHierarchy {
      levels: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.LevelElement>;
      orderedLevels: sap.cdw.commonmodel.LevelElement[];
    }

    interface LevelElement extends BaseObject {
      element: sap.cdw.commonmodel.Element;
    }

    interface EntityMapping extends BaseLinkObject {}

    interface ElementMapping extends BaseLinkObject {}

    interface SemanticType extends BaseObject {}

    interface StyleInfo {
      // fill: string,
      // stroke: string,
      // fontColor: string,
      calloutCssClass: string;
      textCssClass: string;
    }

    interface IValidationStatusParams {
      oContainer: any;
      sStatus: string;
      sCustomMessageGroupId?: string;
      sMessageId?: string;
      aParams?: any[];
      nTokenPosition?: number;
      sPropertyName?: string;
      sMessageDescriptionId?: string;
      aDescriptionParams?: any[];
      sDescriptionMarkup?: string;
      sTitleActiveData?: object;
      bIsExternal?: boolean;
      sMessageSubtitleId?: string;
      aMessageSubtitleParams?: any[];
      bIsBlocker?: boolean;
    }

    class ValidationStatus {
      static STATUS_ERROR: string;
      static STATUS_OK: string;
      static STATUS_WARN: string;
      static STATUS_INFO: string;

      public static createErrorInstance(
        object: any,
        messageGroupId: string,
        messageId: string,
        params?: any[],
        tokenPosition?: any,
        propertyName?: string,
        messageDescriptionId?: string,
        descriptionParams?: any[],
        descriptionMarkup?: string,
        titleActiveData?: object,
        bIsExternal?: boolean,
        messageSubtitleId?: string,
        messageSubtitleParams?: any[],
        isBlocker?: boolean
      ): sap.cdw.commonmodel.ValidationStatus;
      public static createWarnInstance(
        object: any,
        messageGroupId: string,
        messageId: string,
        params?: any[],
        tokenPosition?: any,
        propertyName?: string,
        messageDescriptionId?: string,
        descriptionParams?: any[],
        descriptionMarkup?: string,
        titleActiveData?: object,
        messageSubtitleId?: string,
        messageSubtitleParams?: any[]
      ): sap.cdw.commonmodel.ValidationStatus;
      public static createInfoInstance(
        object: any,
        messageGroupId: string,
        messageId: string,
        params?: any[],
        tokenPosition?: any,
        propertyName?: string,
        messageDescriptionId?: string,
        descriptionParams?: any[],
        descriptionMarkup?: string,
        titleActiveData?: object,
        messageSubtitleId?: string,
        messageSubtitleParams?: any[]
      ): sap.cdw.commonmodel.ValidationStatus;
      public static createInstance(
        object: any,
        status: string,
        sMessageGroupId?: string,
        sMessageId?: string,
        aParams?: any[],
        nTokenPosition?: any,
        sPropertyName?: string,
        messageDescriptionId?: string,
        descriptionParams?: any[],
        descriptionMarkup?: string,
        titleActiveData?: object,
        messageSubtitleId?: string,
        messageSubtitleParams?: any[]
      ): sap.cdw.commonmodel.ValidationStatus;
      public static getStatus(): string;
      public status: string;
      public tokenPosition: string;
      public message: string;
      public propertyName: string;
      // if true --> we should not save model (save anyway should be blocked!)
      // aplied only if status === STATUS_ERROR
      public isBlocker: boolean;
      // In case if the warning do not concern current model.
      // used for instance warnings that not related to the model itself (impacted views warnings)
      public isExternal: Boolean;
    }

    class Validation {
      public static VALIDATION_CHANNEL: string;
      public static REFRESH_VALIDATION_EVENT: string;
      public static VALIDATIONS_CHANGED: string;
      message: any;
      public static publishValidationsChanged(model: sap.cdw.commonmodel.Model);
      public static createValidationStatus(params: IValidationStatusParams): sap.cdw.commonmodel.ValidationStatus;
      public static getMessageFromValidations(aList): string;
      public static isEntityNameUnique(entity: sap.cdw.commonmodel.Entity): Promise<boolean>;
      public static validateModel(model: sap.cdw.commonmodel.BaseModel): Promise<any>;
      public static validateElement(element: sap.cdw.commonmodel.BaseElement, bSkipClear: boolean): string;
      public static validateDataType(element: sap.cdw.commonmodel.BaseElement): string;
      public static validateLength(element: sap.cdw.commonmodel.BaseElement): string;
      public static validatePrecision(element: sap.cdw.commonmodel.BaseElement): string;
      public static validateScale(element: sap.cdw.commonmodel.BaseElement): string;
      public static validateSrid(element: sap.cdw.commonmodel.BaseElement): string;
      public static validateEntity(entity: sap.cdw.commonmodel.Entity, async?: boolean): Promise<string>;
      public static validateEntityDependency(
        entity: sap.cdw.commonmodel.Entity,
        model?: sap.cdw.commonmodel.Model,
        bSkipRefreshDecorator?: boolean
      );
      public static validateAssociationDependencies(
        entity: sap.cdw.commonmodel.Entity,
        model?: sap.cdw.commonmodel.Model,
        bSkipRefreshDecorator?: boolean
      );
      public static validateBaseEntity(
        entity: sap.cdw.commonmodel.Entity,
        bSkipClear?: boolean,
        bSkipColumnNames?: boolean
      ): string;
      ss;
      public static validateAssociations(entity: any);
      public static validateAssociation(
        association: sap.cdw.commonmodel.Association,
        bSkipRefreshDecorator?: boolean,
        oModel?: any
      ): string;
      public static validateHierarchies(entity: any): string;
      public static addAssociationImpactForAnalyticModel(entity: sap.cdw.commonmodel.Entity, hierarchy: string);
      public static validateReleaseState(entity: any): any;
      public static validateReleaseStateOutput(output: any): any;
      public static validateReleasedObject(entity: any): any;
      public static validateFiscalTimeSettings(entity: any): any;
      public static validateDataAccessControls(entity: any, oModel?: any): any;
      public static validateViewDataAccessControl(oVDAC: any, oModel?: any): any;
      public static validateViewAssociation(entity: any): any;
      public static getStatus(any: any): any;
      public static getAggregatedValidation(object: any, childern?: any[], bOnlyChildren?: boolean): any;
      public static validateDefaultVal(element: sap.cdw.commonmodel.BaseElement): string;
      public static validateDefaultValStoryFilter(element: sap.cdw.commonmodel.BaseElement): string;
      public static getImpactedViewsListAsString(impactedViews: string[]): string;
      public static validatePackage(object: any): string;
      public static validateTextDimension(entity: any, name?: string);
      public static validateTextAssociationInFact(entity: any, name?: string);
      public static validateDacHierarchySource(entity: any);
      public static validateDacHierarchyWithDirectorySource(entity: any);
      public static validateDacHierarchyWithDirectoryAssociationsSource(entity: any);
      public static validateBusinessDateElements(entity: any);
      public static validateExternalHierarchy(entity: any, name?: string);
      public static validateHierarchyWithDirectory(entity: any);
      public static validateElementForShowInStory(element: any);
      public static validateRepresentativeKey(entity: any, name?: string);
      public static getAggregatedStatus?(aStatus: string[]): string;
      public static getImpactedViewsListForObjectLink(repoElement): any;
    }

    class Classifiers {}

    class ObjectImpl {
      static isLTF(entity: sap.cdw.commonmodel.Entity): boolean;
      static isDataTransportAllowed(entity: sap.cdw.commonmodel.Entity): boolean;
      static partitionedColumns(entity: sap.cdw.commonmodel.Entity): any[];
      static isCrossSpaceContext(entity: sap.cdw.commonmodel.Context): boolean;
      static isTable(entity: sap.cdw.commonmodel.Entity): boolean;
      static isRemoteTable(entity: sap.cdw.commonmodel.Entity): boolean;
      static isLocalSchemaTable(entity: sap.cdw.commonmodel.Entity): boolean;
      static isDeltaTable(entity: sap.cdw.commonmodel.Entity): boolean;
      static isDeltaOutboundOn(entity: sap.cdw.commonmodel.Entity): boolean;
      static hasNoKeyWithPartitions(entity: sap.cdw.commonmodel.Entity): boolean;
      static isCrossSpaceEntity(entity: sap.cdw.commonmodel.Entity): boolean;
      static getCrossSpaceEntityName(entity: sap.cdw.commonmodel.Entity): string;
      static getSpaceAndObjectNames(entity: sap.cdw.commonmodel.Entity): { spaceName?: string; objectName?: string };
      static hasQueryInCSN(csnEntity: any): boolean;
      static hasQuery(entity: sap.cdw.commonmodel.Entity): boolean;
      static isView(entity: sap.cdw.commonmodel.Entity): boolean;
      static isDataAccessControl(entity: sap.cdw.commonmodel.Entity): boolean;
      static isDataAccessControlCSN(csnEntity: any): boolean;
      static hasDataAccessControl(entity: sap.cdw.commonmodel.Entity): boolean;
      static areThereFilterDacsAttached(entity: sap.cdw.commonmodel.Entity): boolean;
      static getDistinctDacTypesAttached(entity: sap.cdw.commonmodel.Entity): string[];
      static onDefaultProposeMappings(object: any, targetType?: any, selectedElement?: any): void;
      static getForeignAttributeName(object: any): string;
      static ignoreSpecialCharacters(colName: string): string;
      static getAllContexts(
        oContextContainer: sap.cdw.commonmodel.Model | sap.cdw.commonmodel.Context
      ): sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Context>;
      static getRootContext(
        object: sap.cdw.commonmodel.Context | sap.cdw.commonmodel.Entity
      ): sap.cdw.commonmodel.Context;
      static attachObjectToContext(
        model: sap.cdw.commonmodel.Model,
        object: sap.cdw.commonmodel.Entity,
        bChangeName?: boolean,
        contextName?: string
      );
      static updateObjectBaseType(
        object: sap.cdw.commonmodel.SimpleType | sap.cdw.commonmodel.Element,
        dataType?: string,
        cascadeProperties?: boolean
      );
      static attachElementsToBaseTypes(entity: any);
      static attachObjectsToBaseTypes(model: sap.cdw.commonmodel.Model);
      static cascadeChangeSimpleTypeProperty(
        simpleType: sap.cdw.commonmodel.SimpleType,
        property: string,
        oldValue: any,
        newValue: any
      );
      static cascadeAttachTypeToObject(
        dataTypeObject: sap.cdw.commonmodel.SimpleType | sap.cdw.commonmodel.Element,
        baseType?: sap.cdw.commonmodel.BaseType
      );
      static cascadeDetachTypeFromObject(
        dataTypeObject: sap.cdw.commonmodel.SimpleType | sap.cdw.commonmodel.Element,
        baseType?: sap.cdw.commonmodel.BaseType
      );
      static attachElementsToBaseTypes(entity: any);
      static setEntityCrossSpaceProperties(entity: sap.cdw.commonmodel.Entity);
      static getAllBaseTypes(
        object: sap.cdw.commonmodel.BaseType | sap.cdw.commonmodel.Element
      ): sap.galilei.model.BaseCollection<sap.cdw.commonmodel.BaseType>;
      static getRootType(
        object: sap.cdw.commonmodel.BaseType | sap.cdw.commonmodel.Element
      ): sap.cdw.commonmodel.BaseType;
      static computeContextQualifiedName(context: any): string;
      static computeEntityQualifiedName(entity: any): string;
      static computeEntityIndex(entity: any): number;
      static computeEntityShortName(entity: any, bFirstQualifier?: boolean): string;
      static getQualifierName(qualifiedName: string, bFirstQualifier?: boolean): string;
      static removeQualifierInName(qualifiedName: string, bFirstQualifier?): string;
      static getShortName(qualifiedName: string): string;
      static getMaxLengthText(name: string, maxLength: number): string;
      static computeDisplayType(oElement: any): string;
      static isCDIadapter(entity: any): boolean;
      static isFilterableColumn(element: any): boolean;
      /**
       * Computes the display type from element CSN
       * @param {elementCSN} the element CSN
       * @param {resource} the model resource used to get current simple types
       */
      static computeDisplayTypeFromCSN(elementCSN: any, resource: any): string;
      static computePrimitiveDataType(
        elementOrSimpleType: sap.cdw.commonmodel.Element | sap.cdw.commonmodel.SimpleType
      ): string;
      static computeDisplayType(
        elementOrSimpleType: sap.cdw.commonmodel.Element | sap.cdw.commonmodel.SimpleType,
        dataType?: string
      ): string;
      static computePrimitiveDisplayType(
        elementOrSimpleType: sap.cdw.commonmodel.Element | sap.cdw.commonmodel.SimpleType
      ): string;
      static computeRemoteHost(definitions: any): string;
      static computeRemoteTable(definitions: any): string;
      static getBusinessName(definitions: any): string;
      static sanitizeQualifiedName(inString: string): string;
      static getSemanticTypesText(semanticType: any);
      static isPartitionedColumn(element: any);
      static getQualifiedNameAsArray(oElement: any, useNewName?: boolean, bUseIndex?: boolean): any[];
      static changeDataType(element: any, dataType: string);
      static setElementForeignAssociation(element: any, association: any, targetElement: any);
      static clearElementForeignAssociation(element: any, associationName: string);
      static getSemanticTypesForDataType(dataType: string, isMeasure): any[];
      static getSemanticTypesAI(dataType: string, element): any[];
      static checkAnalyticMeasureAttributesAllowedDataTypes(element: any);
      static updateRestrictedMeasureParsedExpression(restrictedMeasure: any, sourceMeasure: any);
      static getPossibleDataTypes(element: any): any[];
      static getPossibleSridValues(element: any): any[];
      static getPossibleFilterOperations(element: any): any[];
      static getPossibleCardinalityValues(): any[];
      static getPossibleLabelColumnsAI(element: any): any[];
      static getAggregationDisplayTypes(): any[];
      static getExceptionAggregationDisplayTypes(): any[];
      static getExceptionAggregationSourceTypes(): any[];
      static getTeamsAsArray(model: sap.cdw.commonmodel.Model): any[];
      static deleteElement(element: sap.cdw.commonmodel.Element);
      static createMangedAssociationElements(association: sap.cdw.commonmodel.Association);
      static removeMangedAssociationElements(oAssociation: sap.cdw.commonmodel.Association, bForceRemove: boolean);
      static removeTextAssociationFromElement(entity: any, element?: any);
      static onNameChangeAssociation(oAssociation: sap.cdw.commonmodel.Association);
      static onCascadeDeleteAssociation(oAssociation: sap.cdw.commonmodel.Association);
      static onBeforeDeleteTable(oTable: sap.cdw.commonmodel.Table);
      static onBeforeDeleteEntity(oEntity: sap.cdw.commonmodel.Entity);
      static onCascadeChangeEntities(oEventArgs: any);
      static onCascadeChangeEntityName(oEventArgs: any);
      static onCascadeChangeKey(oEventArgs: any);
      static onCascadeChangeElements(oEventArgs: any);
      static onCascadeChangeParams(oEventArgs: any);
      static onCascadeChangeFilters(oEventArgs: any);
      static onCascadeChangePartitions(oEventArgs: any);
      static onCascadeChangeHierarchyName(oEventArgs: any);
      static removeOrphanHierarchyLevels(oDimension: sap.cdw.commonmodel.Entity, oElement: sap.cdw.commonmodel.Element);
      static removeUselessAnalyticParameters(oView: sap.cdw.commonmodel.Entity);
      static onCascadeChangeDefaultAggr(oEventArgs: any);
      static onCascadeChangeElementName(oEventArgs: any);
      static onCascadeChangeUnresolvedAssociations(oEventArgs: any, model: any);
      // External Hierarchies minimal implementation:
      // Define getters for parent and child nodes
      // An entity with HIERARCHY semantic type will have only ONE hierarchy of type Parent/child
      static getExternalHierarchyParentNode(
        entity: sap.cdw.commonmodel.Entity
      ): sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element>;
      static getExternalHierarchyChildNode(
        entity: sap.cdw.commonmodel.Entity
      ): sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element>;
      static createMapping(oAssociation: any, source: any, target: any): void;
      // _DP_ Add Analytic parameter helpers
      static getAnalyticParameterReferenceElement(
        analyticParameter: sap.cdw.commonmodel.Parameter
      ): sap.cdw.commonmodel.Element;
      static setAnalyticParameterReferenceElement(
        analyticParameter: sap.cdw.commonmodel.Parameter,
        refElement: sap.cdw.commonmodel.Element
      );
      static setAnalyticParameterReferenceElementFromName(
        analyticParameter: sap.cdw.commonmodel.Parameter,
        refElementName: string
      );
      static setAnalyticParameterDataTypeByReferenceElement(
        analyticParameter: sap.cdw.commonmodel.Parameter,
        refElement: sap.cdw.commonmodel.Element
      );
    }

    class ModelImpl {
      static OBJECT_TABLE: string;
      static getObjectCollectionName(oObject: sap.cdw.commonmodel.BaseObject, sClassName?: string): string;
      static createObject(
        sClassName: string,
        oParam: any,
        oContainer: sap.cdw.commonmodel.BaseObject | sap.cdw.commonmodel.BaseModel,
        index?: number
      ): sap.cdw.commonmodel.BaseObject;
      static setDefaultObjectName(oObject: sap.cdw.commonmodel.BaseObject): void;
      static createContext(oParam: any, oModel: sap.cdw.commonmodel.BaseModel): sap.cdw.commonmodel.Context;
      static getContext(
        sContextQualifiedName: string,
        oModel: sap.cdw.commonmodel.BaseModel
      ): sap.cdw.commonmodel.Context;
      static getOrCreateContext(
        sContextQualifiedName: string,
        oModel: sap.cdw.commonmodel.BaseModel,
        oParam?: any
      ): sap.cdw.commonmodel.Context;
      static getSimpeTypesAndContexts(model: sap.cdw.commonmodel.Model, spaceName?: string): Promise<any>;
      static getSimpleType(name: string, model: sap.cdw.commonmodel.Model): sap.cdw.commonmodel.SimpleType;
      static geOrCreatetSimpleType(name: string, model: sap.cdw.commonmodel.Model): sap.cdw.commonmodel.SimpleType;
      static getAssociationBusinessName(source: string, target: string): string;
      static getUniqueName(name: string, existingNames: any[], index: number, isTechnicalName: boolean): string;
      static updateSymbol(container: any): void;
      static onCascadeDeleteElement(element: any): void;
      static onCascadeChangeOrdSetting(oEventArgs: any);
      static onCascadeChangePackageStatus(oEventArgs: any);
      static onCascadeChangePackageValue(oEventArgs: any);
      static getReverseReferenceOwningObjects(oElement: any, reverseReference: any);
      static getOrCreateSimpleType(dataType: string, model: sap.cdw.commonmodel.Model);
      static onPrimaryKeyChange(oElement: any): void;
    }

    class ModelToCsn {
      static CSN_VERSION: string;
      static CSN_PROP_MAPPING: any;
      static getInstance(): ModelToCsn;
      CSN_CREATOR: string;
      CSN_KIND: string;
      CSN_PROP_MAPPING: any;
      getEmptyCSNDocument(oParam: any): any;
      getValue(sName?: string, oModel?: sap.cdw.commonmodel.Model, oOptions?: any): any;
      convertToCsn(
        name: string,
        model: any,
        options?: { rootObject?: any; serializeModel?: any; modelKind?: any }
      ): any;
      getCsnForEntity(entity: any): any;
      getCsnDocumentForEntity(entity: any): any;
      getAssociationName(association: sap.cdw.commonmodel.Association): string;
      addAssociation(oCsnElements: any, association: sap.cdw.commonmodel.Association, isMixin: boolean);
      addDefaultPropertyMapping(oCsnObject: any, object: sap.cdw.commonmodel.BaseObject);
    }
  }

  namespace tableEditor {
    class Model {
      constructor(oRessource: sap.galilei.model.Resource, oOptions: any);
    }

    class ModelImpl {
      static checkIfFiltersOrAssociationsChanged(table: any, csn: any): boolean;
      static updateRemoteTableChanges(
        table: any,
        csn: any,
        excludedColumns?,
        partitionedColumn?,
        selectedNewObjects?,
        spaceType?,
        isRemoteSourceChange?: boolean,
        SdiToSda?: boolean,
        csnParams?: any
      ): any;
      static checkIfRemoteTableChanged(
        table: any,
        csn: any,
        isPreDeploy?: boolean,
        spaceType?: string,
        csnParams?: any
      ): boolean;
      static updateRemoteTableChangesWithValidation(
        table: any,
        validationResults: any,
        excludedColumns?,
        partitionedColumn?,
        selectedNewObjects?,
        spaceType?
      ): any;
      static updateLocalTableVersionChanges(table: any, currentCsn: any, versionCsn: any, model: any): any;
      static updateValidationMessages(table: any, validationResults: any);
      static computeExcludeandNewColumnList(table: any, spaceId: any, model: any, csn?);
    }
  }

  namespace databuilder {
    class ShareObjectsCommand {
      public static execute(oParam: {
        data: { spaceName: string; sharingObjects: ISharingObjectList };
        controller: any;
      }): void;
    }

    class ImportCsnFileCommand {
      public static execute(oParam: { csnObject: IcsnObject }): void;
    }
  }

  // ER Modeler definition sap.cdw.ermodeler
  namespace ermodeler {
    namespace sharedDefinitions {}

    class Validation extends sap.cdw.commonmodel.Validation {}

    class DiagramImpl {
      static calculateSymbolLoc(allSymbols: any): any;
      static ER_DIAGRAM_CHANNEL: string;
      static UNDO_REDO_EVENT: string;
      static autoLayout(oDiagramEditor: any, bShowGlobalView?: boolean, oSymbolSelection?: any): void;
      static getDiagram(oModel: any): any;
      static createSymbol(oDiagramEditor: any, oObject: any, bDraw?: boolean);
      static _createSymbol(oDiagramEditor: any, oObject: any, sSymbolClassName?: string);
    }

    class ModelImpl {
      static OBJECT_ELEMENT: string;
      static ERMODEL_CHANNEL: string;
      static ERMODEL_NEWMODEL_EVENT: string;
      static OBJECT_TABLE: string;
      static OBJECT_ASSOCIATION: string;
      static OBJECT_VIEW: string;
      static UNIQUE_ERD_RESOURCE_ID: string;
      static SOURCE_OBJECT_TYPE: { [key: string]: number };
      static createObject(sClassName: string, oParam: any, oContainer: any, index?: number): any;
      static importEntitiesAndViewsFromList(
        oData: any,
        oDiagramEditor: any,
        notDelete?: boolean,
        repositoryCSNs?: any,
        editorSettings?: any
      ): Promise<void>;
      static getOrCreateModel(
        sName?: string,
        oLoadingData?: any,
        sResourceId?: string,
        oService?: any,
        aMissingEntities?: any[]
      ): any;
      static createModel(
        name: string,
        loadingData: { file; csn },
        resoure: any,
        service: any,
        modelNs: string,
        diagramNs: string
      ): any;

      // static updateGalileiObjects(oModel: any, oController: any): void;
      static handleEntitySaveAndDeployStatus(oEntity: any, oDropParams: any);
      static setObjectsCSN(oModel: any, oCSN: any): any;
      static getAssociationsAndMappings(ParentEntity: any, oEntity: any, isJoin: boolean): any;
      static updateEntityFromDropDefinitions(galileiEntity: any, csnEntity: any): void;
      static onDropDataSource(target: any, oDropParams: any);
      static checkSymbolExists(oObject: any, oModel: any, customFilter?: any): any;
      static createSymbols(sChannelId: String, oAssociation, oTargetEntity: any, oModel: any, isExists: boolean);
      static getUniqueName(businessNameVal: string, bNameArr: any[], index?: number, isTechnicalName?: boolean): any;
      static createAssociation(
        sClassName: string,
        source: any,
        target: any,
        oModel: any,
        targetType?,
        selectedElement?,
        skipDefaultMappings?: boolean
      ): void;
      static processAssociations(
        rootModel: any,
        targetFound: any,
        repoObject: any,
        unresolvedInfo: any,
        targetType?: any,
        selectedElement?: any,
        isDisableUndo?: boolean
      ): any;
      static createTargetFromRepoObject(object: any, oModel: any, isTargetTypeView: boolean): any;
      static createAssociationAndSymbol(
        source: any,
        target: any,
        targetFound: any,
        unresolvedInfo: any,
        targetType?,
        selectedElement?
      ): any;
    }

    class ObjectImpl {
      static onCascadeChangeDisplayName: (event: any) => void;
      static onDefaultProposeMappings(entity: any, targetType?: any, selectedElement?: any): void;
      static computeRemoteHost(definitions: any): string;
      static computeRemoteTable(definitions: any): string;
    }

    class Model extends sap.cdw.commonmodel.Model {
      constructor(oResource: sap.galilei.model.Resource, params?: any, id?: string);
    }

    interface Table extends sap.cdw.commonmodel.Table {
      isRemote?: boolean;
      connection?: string;
      ordId?: string;
    }

    interface View extends sap.cdw.commonmodel.View {}

    interface Association extends sap.cdw.commonmodel.Association {}

    interface Element extends sap.cdw.commonmodel.Element {}

    namespace ui {
      class Diagram {
        constructor(oResource: sap.galilei.model.Resource, params?: any, id?: string);
      }
      class DiagramEditorExtension {}
    }

    class EditWithCommand {
      public execute(oParam: any): void;
    }

    class DataPreviewCommand {
      public execute(oParam: any): void;
    }

    class ImportCsnCommand {
      public static execute(oParam: any): void;
    }

    class MassImportCommand {
      public static execute(oParam: any): void;
      public static fetchRepositoryObjects(oParam: any): any;
    }

    class MassImportRemoteCommand {
      public static execute(oParam: any): void;
    }

    class MergeAdapter {
      public static mergeModel(oNewModel: any, oExistingModel: any, notDelete?: boolean);
    }

    class ErCsnToModel extends sap.cdw.commonmodel.CsnToModel {
      public static getInstance(value?: boolean): ErCsnToModel;
      createContextAndAttachToObject(model, entity, contextName, csnDefinition, force?: boolean);
      updateOrdFromCsnFile(oEntity: any, csnFile: any);
      updateOrdFromErModel(oEntity: any, oModel: any);
    }

    class ErModelToCsn extends sap.cdw.commonmodel.ModelToCsn {
      public static ERMODELER_DIAGRAM_KIND;
      public static getEmptyCSNDocument(): any;
      public static getCSN(name: string, model: string, o: any): Promise<any>;
      public static getInstance(): ErModelToCsn;
    }
  }

  // Table editor definition sap.cdw.tableEditor
  namespace tableEditor {
    interface Model extends sap.cdw.ermodeler.Model {
      table: any;
    }
  }

  // File upload
  namespace fileupload {
    class ImportFileCommand {
      public static execute(oParam: any): void;
    }
  }
}

declare namespace sap.cdw {
  let GVE_FORCE_DISPLAY_ENTITY_TYPES_LABEL: boolean; // used to enable the feature also for GVE (outside transformation flow)
}

// View Builder
declare namespace sap.cdw.querybuilder {
  let Extensions: {
    functionsList?: (defaultList: any[], context: any) => any[];
    dataTypeList?: (defaultList: any[], context: any) => any[];
    supportJoinCardinality?: boolean;
    supportCurrencyConversionUi?: boolean;
    supportDatapreviewOnIntermediateNode?: boolean;
    cache?: {
      supportedFunctionNames?: string[];
    };
    skipParametersValidation?: Function;
    hideInputParameterLabel?: Function;
    invalidateComplexExpressionsWithCDCColumns?: boolean;
  };

  let DISPLAY_ENTITY_TYPES_LABEL: boolean; // dynamic setting to display type entity label in view builder diagram
  namespace sharedDefinitions {
    interface IEntityAssociationsReferences {
      sourceAssociations: sap.galilei.model.BaseCollection<sap.cdw.querybuilder.Association>;
      targetAssociations: sap.galilei.model.BaseCollection<sap.cdw.querybuilder.Association>;
      hierarchyAssociation: sap.cdw.querybuilder.Association;
    }
  }

  class DataPreviewCommand {
    public execute(oParam: any): void;
  }

  class PreviewSQLCommand {
    public execute(oParam: any): void;
  }

  class ParsedExpressionImpl {
    public static COMPARE_OPERATORS;
    public static LOGIAL_OPERATORS;
    public static OPERATORS;
    public static OPERATORS2;
    public static CASE_EXPRESSIONS;
    public static PREDICATES1;
    public static PREDICATES2;
    public static PREDICATES3;
  }

  class ModelImpl {
    static CHANGEMANAGEMENT_EVENT: string;
    static UNIQUE_CSN_RESOURCE_ID: string;
    static OUTPUT_TYPE: { GRAPHIC: string; SQL: string };
    public static JOIN_TYPES: any;
    public static CSNMODEL_CHANNEL: any;
    public static CSNMODEL_LOADED: any;
    public static CSNMODEL_ASYNC_ELTS_LOADED: any;
    public static CSNMODEL_REQUEST_USER_ACTION: any;
    public static CSNMODEL_REQUEST_PARAMETER_MAPPING: any;
    public static CSNMODEL_LOADED_AND_VALIDATED: any;
    static HANDLE_JOIN_DUPLICATE: boolean;
    static ENTITY_DROPPED: string;

    static getOrCreateModel(
      sName?: string,
      oLoadingData?: any,
      sResourceId?: string,
      oService?: any,
      graphicView?: boolean,
      oCsnDocument?: any
    ): any;
    static replaceSource(replacementInfo: any): any;
    public static resetCalcElementExpression(oCalculatedElement: any): void;
    public static importObjects(selectedRemoteObjects, oDiagramEditor): void;
    static createObject(sClassName: string, oParam: any, oContainer: any, index?: number): any;
    static onDropDataSource(oEntity: any, oDropParams: any, isDimension?: boolean): any;
    static requestAdjustDiagramsContent(oOptions?: any): void;
    static createNewParameterElement(node: any, isForAnalyticParameters?: boolean): any;
    static createNewValueHelpEntityForParameter(node: any): any;
    static autoRemoveDuplicateElements(oNode: any, bResolveDuplicateByRemove: boolean, aDuplicatedElements?: any): void;
    public static onDropDimension(oEntity: any, oDropParams: any, isDimension: boolean): any;
    static updateEntityFromCsn(object: any, csn: any, model?: any, service?: any): void;
    static onCreateIntermediateNode(oAfterNode: any, arg1: any);
    public static parseAllModelExpressions(model: any): Promise<any>;
    static swapJoinInput(oJoin: any): void;
    public static performParameterMapping(parameters: any, oNode: sap.cdw.querybuilder.Node);
    public static getUniqueParameterName(oNode: any, name: String, isTechnicalName: boolean, index?: number): any;
    static onPostDropDataSource(oEntity, oParentEntity?, oOptions?);
    static migrateLevelBasedHierarchy(model, query);
    static createDimension(model: any, params: any): any;
    static pushAssociation(unresolvedAssociation: any, targetCsn: any, output: any, model: any): any;
  }

  class NodeImpl {
    public static reorderElementByDrop(oNode: any, oElement: Element, oRefElement: Element, sPosition: string): void;
    public static changeElementIndexOrder(oNode: any, oElement: Element, nOldIndex: number, nNewIndex: number): void;
    public static getLineageOfElement(oElement: Element, oCurrentResults?: any): object;
    public static removeAllElements(oNode: any): void;
    public static copyElementsForSetNode(oSetNode: any, oInputNode: any, bRemoveExistingElements?: boolean): void;
    public static copyElementForSetNode(oSetNode: any, oInputElement: any): void;
    public static createNewCalculatedElement(
      oCalcNode: any,
      options?: { simpleCalculation?: boolean; currencyConversion?: boolean },
      spaceName?: string
    ): any;
    public static getOnePredecessorElement(oPredecessorNode: any, oNode: any, oElement: any);
    public static computeNodePredecessors(output: any);
    public static computeNodePredecessorsRecursive(oNode: any): any;
    public static computePredecessorEntityOrJoin(oNode: any): any;
    public static getSuccessorJoin(oNode: any): any;
    public static createNewFormulaElement(output: any): any;
    public static createNewExceptionAggregationElement(output: any): any;
    public static createRestrictedMeasure(output: any): any;
    public static createCountDistinct(output: any): any;
    public static isDeleting?: boolean;
    public static isAdding?: boolean;
    public static stopPropagating?: boolean;
    static _copyElement(oA, oB);
  }

  class DiagramImpl {
    public static highlightNodes(aNodesToHighlight?: any, oNodeToExclude?: any, bIncludeSuccessorLinks?: boolean): void;
    public static QUERY_DIAGRAM_CHANNEL: string;
    public static UNDO_REDO_EVENT: string;
    public static DIAGRAM_CHANNEL: string;
    public static PROCESSING_EVENT: string;
    public static getDiagram(oModel: any): any;
    public static autoLayout(oDiagramEditor: any, bShowGlobalView?: boolean, oSymbolSelection?: any): void;
    static refreshValidationDecoratorsByModel(model: sap.cdw.querybuilder.DiagramImpl): void;
  }

  class ViewModelToCsn {
    constructor();
    public static CDS_ANNOTATION_UIMODEL;
    public static getInstance(): any;
    public static getEmptyCSNDocument: any;
    public static parseAllNodeExpressions(
      oNode: any,
      aFilteredExpressionsToParse?: any,
      fnCallback?: Function,
      bForce?: boolean
    ): Promise<any>;
    public static metadata: { [key: string]: Function };
    public static quoteName(name: string): string;
    public static asyncParsing: boolean;
    public static getCSN(sName: string, oModel: any, oOptions?: any, skipSubQueries?): Promise<any>;
    static _createAndEvaluateParsedExpression(oExpressedObject, oExpression, oError?);
    public getDefaultPropertyMapping(element: any, options: any): any;
  }

  class CsnToModel {
    constructor();
    public getDefaultPropertyMapping(element: any, options: any): any;
    public static createOrUpdateSQLOutput(
      oExistingModelOrNull: any,
      oCsnDocument: any,
      repoObjects: any,
      updateFromSql: any
    ): any;
    public static updateOutput(oCsnEntity: any, oOutput: any, bForce?: boolean): any;
    public static reverse(oCsn: any, oModel: any, repoObjects: any, oExistingModel: any, bForce?: boolean): any;
  }

  class MergeAdapter {
    public static mergeModel(oNewModel: any, oExistingModel: any, updateFromSql?: boolean, featureFlags?: any);
  }

  class Validation {
    public static generateValidationDescriptionMarkup(
      descriptionId: string,
      descriptionParamneters: any,
      descriptionParamneterMarkupListIndex: string,
      markupLines: any
    ): any;
    public static requestRefreshDecorators(oModel: any): void;
  }

  class Model extends sap.cdw.commonmodel.BaseModel {
    constructor(oResource: sap.galilei.model.Resource, params?: any, id?: string);
    nodes: sap.galilei.model.BaseCollection<sap.cdw.querybuilder.Node>;
    entities: sap.galilei.model.BaseCollection<sap.cdw.querybuilder.Entity>;
    dimensionNodes: sap.galilei.model.BaseCollection<sap.cdw.querybuilder.DimensionNode>;
    output: sap.cdw.querybuilder.Output;
    associations: sap.galilei.model.BaseCollection<sap.cdw.querybuilder.Association>;
    unresolvedAssociations: any[];
    changeManagement: any;
  }

  interface Node
    extends sap.cdw.commonmodel.BaseObject,
      sap.cdw.commonmodel.sharedDefinitions.IBaseEntityElementsMethods {
    elementCount: number;
    parameterCount: number;
    assocCount: number;
    qualifiedName: string;
    displayName: string;
    label: string;
    sourceEntityName: string;
    isDistinct: boolean;
    isEntity: boolean;
    orderedElements: sap.cdw.querybuilder.Element[];
    selectedElements: sap.cdw.querybuilder.Element[];
    filteredOrderedElements: sap.cdw.querybuilder.Element[];
    elements: sap.galilei.model.BaseCollection<sap.cdw.querybuilder.Element>;
    dimensionElements: sap.galilei.model.BaseCollection<sap.cdw.querybuilder.Element & { isMeasure: false }>;
    associations: sap.galilei.model.BaseCollection<sap.cdw.querybuilder.Association>;
    successorNode: sap.cdw.querybuilder.Node;
    predecessorNodes: sap.galilei.model.BaseCollection<sap.cdw.querybuilder.Node>;
    entities: sap.cdw.querybuilder.Entity[];
    onBeforeDelete: Function;
    getBranchNodes: Function;
    getAllExpressionsToParse: Function;
    parseAllExpressions: Function;
    clearValidation(sPropertyName?: string): void;
    getAllChangesByIndex: Function;
    applyChangesByIndex: Function;
    representativeKey: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element>;
    compoundKeySequence: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Element>;
    repositoryCSN: ICsn;
    technicalName: string;
    definition: ICsnDefinitions;
  }

  interface DimensionNode extends Node {}

  interface Element extends sap.cdw.ermodeler.Element {
    isCDCColumn: boolean;
    name: string;
    newName: string;
    identifyingProperties: string[];
    isRemoved: boolean;
    indexOrder: number;
    successorElement: Element;
    sourceElement: Element;
    _NotYetSaved: boolean;
  }

  interface CalculatedElement extends Element {
    expression: string;
    isCalculated: boolean;
    isNew: boolean;
    qualifiedNameAsArray: string[];
    length: number;
    precision: number;
    scale: number;
    validate(): string;
    setParedExpression: Function;
    sourceCalculatedElement: CalculatedElement;
    element: sap.cdw.querybuilder.Element;
    parsedExpressions: sap.galilei.model.BaseCollection<sap.cdw.querybuilder.ParsedExpression>;
    parsedExpression: sap.cdw.querybuilder.ParsedExpression;
  }

  interface ParsedExpression extends sap.cdw.commonmodel.BaseObject {
    expression: any;
    error: any;
    evaluate: Function;
    toCSNExp: Function;
    stringifyParsedExpression: Function;
    isWindowFunctionExpression: Function;
    isAggregateExpression: Function;
  }

  interface Entity
    extends Node,
      commonmodel.sharedDefinitions.IBaseEntityProperties,
      commonmodel.sharedDefinitions.IBusinessDefinitionProperties,
      commonmodel.sharedDefinitions.IBusinessDefinitionMethods,
      sharedDefinitions.IEntityAssociationsReferences {
    crossSpaceName?: string;
    crossSpaceEntityName?: string;
    useAs: string;
    repositoryCSN: ICsn;
    shortName: string;
    alias: string;
    aliasOrShortName: string;
    displayName: string;
    type: string;
    remote: any;
    csn: ICsn;
    remoteReadyStatus: string;
    entityIndex: number;
    qualifiedName: string;
    identifyingValue: string;
    isRemote: boolean;
    isQualified: boolean;
    isEntity: boolean;
    isHiddenInUi: boolean;
    validate: Function;
    unhandledCsn?: any;
  }
  interface Output
    extends Node,
      commonmodel.sharedDefinitions.IBaseEntityProperties,
      commonmodel.sharedDefinitions.IBusinessDefinitionProperties,
      sharedDefinitions.IEntityAssociationsReferences,
      commonmodel.sharedDefinitions.IBusinessDefinitionMethods {
    dbViewType: any;
    repositoryCSN: any;
    sqlCSN: any;
    parameters: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Parameter>;
    displayName: string;
    technicalName: string;
    label: string;
    isView: boolean;
    isNew: boolean;
    isEntity: false;
    params: sap.galilei.model.BaseCollection<any>;
    measures: sap.galilei.model.BaseCollection<sap.cdw.querybuilder.Element> & { isMeasure: true };
    dimensionElements: sap.galilei.model.BaseCollection<sap.cdw.querybuilder.Element & { isMeasure: false }>;
    //addHierarchy: ermodeler.sharedDefinitions.IHierarchyMethods["addHierarchy"];
    validate: Function;
    getAllChangesByIndex: Function;
    applyChangesByIndex: Function;
  }

  interface Operation extends Node {
    validate: Function;
  }

  interface Association extends sap.cdw.commonmodel.BaseLinkObject {
    type: string;
    targetEntityType: string;
    minCardinality: string;
    maxCardinality: string;
    isSupportManaged: boolean;
    isExistsInSource: boolean;
    source: Node;
    target: Node;
    mappings: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.ElementMapping>;
    onBeforeDelete: Function;
    validate: Function;
    proposeDefaultMappings: Function;
    getForeignAttributeName: Function;
    initiatedFrom?: string;
  }

  interface ElementMapping extends sap.cdw.commonmodel.ElementMapping {}

  namespace ui {
    class DiagramEditorExtension {}
  }
}

declare namespace sap.cdw.dataflowmodeler {
  class Model extends galilei.common.Model {
    packageValue: string;
    packageStatus: string;
    executionStatus: any;
    isEmpty: boolean;
    targetCount: number;
    clearValidation: Function;
    nodes: any;
    isNew: boolean;
    label: any;
    hasUnconnectedOperator: boolean;
    processJoin: boolean;
    getAllProcess: Function;
    changeManagement: any;
    diagrams: any;
    diagramBuilder: any;
    deploymentDate: any;
    revertLastVersion: any;
    isPipelineMode: boolean;
    dataVolume: any;
    autoRestart: boolean;
    remoteConnectionsList: any[];
    parameters: any;
    isPrimaryKeysProcessed: any;
    customTypesMap: any;
    constructor(oRessource: sap.galilei.model.Resource, params?: any);
  }

  class Validation extends sap.cdw.commonmodel.Validation {
    public static requestRefreshDecorators(oModel: any): void;
    public static validateNode(
      oNode: Node,
      bSkipRefreshDecorators?: boolean,
      bSkipClear?: boolean,
      bSkipNoAttributes?: boolean,
      bSkipDuplicate?: boolean
    ): void;
    public static validateAttribute(
      oAttribute: Attribute,
      oNode?: Node,
      bSkipRefreshDecorators?: boolean,
      bSkipClear?: boolean
    ): void;
  }

  class Port {
    name: any;
    vtype: Vtype;
    container: Node;
    getLinkObjects: Function;
    resource: any;
    constructor(oResource: sap.galilei.model.Resource, params?: any);
  }

  class Input extends Port {}

  class Output extends Port {}

  class Node {
    relatedSymbols: any;

    constructor(oResource: sap.galilei.model.Resource, params?: any);
    config: any;
    metadata: any;
    inputs: any;
    outputs: any;
    properties: any;
    tableMappings: any;
    updateConfig: Function;
    unionAll: boolean;
    name: string;
    aggregationDefinitions: any;
    resource: any;
    attributeMappings: any;
    expression: string;
    displayName: string;
    component: string;
    joinDefinitions: any;
    nodeType: string;
    container: any;
    validate: Function;
    clearValidation: Function;
    getAttributes: Function;
    getAttributesArray: Function;
    layeredConfig: Function;
    isNew: boolean;
    businessName: string;
    deploymentStatus: string;
    "#objectStatus": number;
    icon: string;
    uniqueTechnicalName: any;
    attributes: any;
    remoteConnection: string;
    remoteSchemaList: any[];
    remoteSchema: string;
    remoteTableName: string;
    isRemoteTarget: boolean;
    fullyQualifiedName: string;
    isActiveRecordsView: boolean;
    isSACArtefact: boolean;
    packageValue: string;
  }

  class Flow {
    constructor(oResource: sap.galilei.model.Resource, params?: any);
  }

  class Group {
    updateConfig(property: string, arg1: any): any;
    constructor(oResource: sap.galilei.model.Resource, params?: any);
  }

  class TableMapping {
    source: Output;
    target: Input;
  }

  class AtttributeMapping {
    source: Attribute;
    target: Attribute;
    expression: string;
  }

  class Attribute {
    name: string;
    datatype: string;
    length: any;
    precision: any;
    scale: any;
    deleteObject: Function;
    source: string;
    isCalculatedColumn: boolean;
    isCustomScriptColumn: boolean;
    key: boolean;
  }

  class Vtype {
    scope: any;
    type: any;
    loaded: boolean;
    attributes: any;
    constructor(oResource: sap.galilei.model.Resource, params?: any);
  }

  class InputParameter {
    name: string;
    dataType: string;
    defaultValue?: string;
    parameterMappings?: any;
    constructor(parameterObject: any);
  }

  namespace ui {
    class Diagram {
      constructor(oResource: sap.galilei.model.Resource, params?: any, id?: string);
    }

    class InputSymbol {
      constructor(oResource: sap.galilei.model.Resource, params?: any);
    }

    class OutputSymbol {
      constructor(oResource: sap.galilei.model.Resource, params?: any);
    }

    class ExternalInPortSymbol {
      constructor(oResource: sap.galilei.model.Resource, params?: any);
    }

    class ExternalOutPortSymbol {
      constructor(oResource: sap.galilei.model.Resource, params?: any);
    }

    class NodeSymbol {
      width: number;
      height: number;
      minWidth: number;
      maxWidth: number;
      minHeight: number;
      symbols: any;
      updateBoundarySymbols(): any;
      constructor(oResource: sap.galilei.model.Resource, params?: any);
    }

    class FlowSymbol {
      constructor(oResource: sap.galilei.model.Resource, params?: any);
    }

    class GroupSymbol {
      x: any;
      y: any;
      extendToContent(arg0: { extendParent: boolean }, ALL: any, viewer: any): any;
      constructor(oResource: sap.galilei.model.Resource, params?: any);
    }

    class DiagramEditorExtension {}
    class Library {}
  }
}

// Transformation Flow
declare namespace sap.cdw.transformationflow {
  class Model extends galilei.common.Model {
    label: string;
    isNew: boolean;
    diagramBuilder: any;
    revertLastVersion: boolean;
    deploymentDate: any;
    changeManagement: any;
    diagrams: any;
    nodes: any;
    loadType: string;
    isTemplate: boolean;
    aggregatedValidations: any;
    isLoadTypeModifiedByUser: boolean;
    highWaterMarkExists: boolean;
    targetTableTechnicalName: string;
    config: any;
    customTypesMap: any;
    availableSimpleTypes: any;
    constructor(oResource: sap.galilei.model.Resource, params?: any);
    validate(): Promise<any>;
    public clearValidation(): void;
    public getChangeManagementInfo(): any;
    public getAllProcess(): any[];
    packageValue: string;
    packageStatus: string;
    runtime: string;
    isLargeSystemSpace: boolean;
    showSecondarySources: boolean;
    parameters: any;
  }

  class Node {
    config: any;
    metadata: any;
    inputs: any;
    outputs: any;
    name: string;
    attributes: any;
    definition: any;
    attributeMappings: any;
    component: string;
    isNew: boolean;
    icon: string;
    technicalName: string;
    businessName: string;
    meta: any;
    version: any;
    $version: string;
    editorSettings: any;
    truncate: boolean;
    isTemplate: boolean;
    resource: any;
    displayName: string;
    isTargetTable: boolean;
    container: Model;
    relatedSymbols: any;
    uniqueTechnicalName: boolean;
    cdcColumns: string[];
    isDeltaTable: boolean;
    deploymentStatus: any;
    deltaTableTechnicalName: string;
    uniqueDeltaCaptureTableName: boolean;
    internalValidationStatus: string;
    changeManagement: any;
    packageValue: string;
    isValidationSkipped: boolean;
    isTargetDelta: boolean;
    aggregatedValidations: { status: string; validations: sap.cdw.commonmodel.Validation[] };
    isViewTransform: boolean;
    hasBWBridgeDeltaSource: boolean;
    isLTF: boolean;
    secondaryNodes: sap.galilei.model.BaseCollection<sap.cdw.querybuilder.Node>;
    sourceDefinitions: any;
    isSourceTable: boolean;
    useAs: string;
    deltaTableName: string;
    displayTechnicalName: string;
    hasIncrementalAggregation: boolean;

    public getAttributesArray(): Attribute[];
    public getSourceNode(): Node;
    public getViewTransformNode(): Node;
    public getTargetNode(): Node;
    public getTargetTableNode(): Node;
    public getViewTransformNode(): Node;
    public getCSN();
    public validate(bSkipRefreshDecorators: boolean): void;
    public clearValidation(): void;
    public getTimestampColumns(): string[];
    public getTFSourceNode(): Node;
  }

  class SecondaryNode {
    name: string;
    attributes: sap.cdw.transformationflow.Attribute[];
    outputs: any;
    x: string;
    y: string;
    useAs: string;
    isRemote: boolean;
    isView: boolean;
    isLocal: boolean;
    isLTF: boolean;
    isDeltaTable: boolean;
    isSecondaryNodeSql: boolean;
    parameters: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Parameter>;
    container: sap.cdw.transformationflow.Model;
    public getAttributesArray(): Attribute[];
  }

  class Port {
    name: string;
    container: Node;
    getLinkObjects: Function;
    resource: any;
  }

  class Input extends Port {}

  class Output extends Port {}

  class AttributeMapping {
    source: Attribute;
    target: Attribute;
  }

  class Attribute {
    name: string;
    label: string;
    key: boolean;
    datatype: string;
    length: any;
    precision: any;
    scale: any;
    deleteObject: Function;
    container: any;
    isCDCColumn: boolean;
    notNull: boolean;
    default: any;
    displayName: string;
    isCustomScriptColumn: boolean;
    aggregation: string;
  }

  class Validation extends sap.cdw.commonmodel.Validation {
    public static requestRefreshDecorators(oModel: Model): void;
    public static validateNode(
      oNode: Node,
      bSkipRefreshDecorators?: boolean,
      bSkipClear?: boolean,
      bSkipNoAttributes?: boolean,
      bSkipDuplicate?: boolean
    ): void;
    public static validateAttribute(
      oAttribute: Attribute,
      oNode?: Node,
      bSkipRefreshDecorators?: boolean,
      bSkipClear?: boolean
    ): void;
  }

  namespace ui {
    class Diagram {
      constructor(oResource: sap.galilei.model.Resource, params?: any, id?: string);
    }

    class DiagramEditorExtension {}
  }
}

declare namespace sap.cdw.intelligentlookup {
  class Model extends galilei.common.Model {
    isEmpty: boolean;
    targetCount: number;
    clearValidation: Function;
    nodes: any;
    flows: any;
    isNew: boolean;
    label: string;
    executionStatus: any;
    hasUnconnectedOperator: boolean;
    spaceName: string;
    output: sap.cdw.commonmodel.Entity;
    businessName: string;
    deploymentStatus: any;
    deploymentDate: any;
    maxRuleId: number;
    revertLastVersion: boolean;
    technicalName: string;
    bDirty: boolean;
    hasData: boolean;
    deletionInProgress: boolean;
    rulesData: any;
    packageValue: string;
    packageStatus: string;
    constructor(oRessource: sap.galilei.model.Resource);
  }

  class Validation extends sap.cdw.commonmodel.Validation {
    public static requestRefreshDecorators(oModel: any): void;
    public static validateNode(
      oNode: Node,
      bSkipRefreshDecorators?: boolean,
      bSkipClear?: boolean,
      bSkipNoAttributes?: boolean,
      bSkipDuplicate?: boolean
    ): void;
    public static validateAttribute(
      oAttribute: Attribute,
      oNode?: Node,
      bSkipRefreshDecorators?: boolean,
      bSkipClear?: boolean
    ): void;
  }

  class Port {
    name: any;
    vtype: Vtype;
    container: Node;
    getLinkObjects: Function;
    resource: any;
    constructor(oResource: sap.galilei.model.Resource, params?: any);
  }

  class Input extends Port {}

  class Output extends Port {}

  class Node {
    relatedSymbols: any;
    sourceRuleId: string;
    sourceBucket: string;
    lookupScope: string;
    outputViewProperties: any;

    constructor(oResource: sap.galilei.model.Resource, params?: any);
    config: any;
    metadata: any;
    x: string;
    y: string;
    inputs: any;
    outputs: any;
    properties: any;
    tableMappings: any;
    updateConfig: Function;
    unionAll: boolean;
    name: string;
    aggregationDefinitions: any;
    resource: any;
    attributeMappings: any;
    expression: string;
    displayName: string;
    component: string;
    joinDefinitions: any;
    matchDefinitions: any;
    nodeType: string;
    container: any;
    validate: Function;
    clearValidation: Function;
    getAttributes: Function;
    getAttributesArray: Function;
    layeredConfig: Function;
    isNew: boolean;
    businessName: string;
    deploymentStatus: number;
    "#objectStatus": number;
    remoteSourceLoaded: number;
    icon: string;
    KPImatchType: string;
    keyColumns: string[];
    definitions: any;
    pairingColumns: string[];
    returnColumns: string[];
    columns: any;
    newTableDropped: boolean;
    isLookup: boolean;
    showTabs: boolean;
    rowsCount: string;
    isExecutebuttonClicked: boolean;
    rowCountofMultipleMatch: number;
    rowCountofNoMatch: number;
    rowCountofTrusted: number;
    rowCountofReview: number;
    rowCountofExcluded: number;
    totalCount: number;
    reviewAndExcludedVisibility: boolean;
    technicalName: string;
    rowCountofUnProcessed: number;
    caseSensitive: boolean;
    trimSpaces: boolean;
    className: string;
    filters: any;
    packageValue: string;
    PackageStatus: string;
  }

  class Flow {
    constructor(oResource: sap.galilei.model.Resource, params?: any);
  }

  class Group {
    updateConfig(property: string, arg1: any): any;
    constructor(oResource: sap.galilei.model.Resource, params?: any);
  }

  class TableMapping {
    source: Output;
    target: Input;
  }

  class AtttributeMapping {
    source: Attribute;
    target: Attribute;
    expression: string;
  }

  class Attribute {
    name: string;
    datatype: string;
    length: any;
    precision: any;
    scale: any;
    deleteObject: Function;
    source: string;
    isCalculatedColumn: boolean;
  }

  class Vtype {
    scope: any;
    type: any;
    loaded: boolean;
    attributes: any;
    constructor(oResource: sap.galilei.model.Resource, params?: any);
  }
  namespace ui {
    class Diagram {
      constructor(oResource: sap.galilei.model.Resource, params?: any, id?: string);
    }

    class InputSymbol {
      constructor(oResource: sap.galilei.model.Resource, params?: any);
    }

    class OutputSymbol {
      constructor(oResource: sap.galilei.model.Resource, params?: any);
    }

    class ExternalInPortSymbol {
      constructor(oResource: sap.galilei.model.Resource, params?: any);
    }

    class ExternalOutPortSymbol {
      constructor(oResource: sap.galilei.model.Resource, params?: any);
    }

    class NodeSymbol {
      width: number;
      height: number;
      minWidth: number;
      maxWidth: number;
      minHeight: number;
      symbols: any;
      updateBoundarySymbols(): any;
      constructor(oResource: sap.galilei.model.Resource, params?: any);
    }

    class FlowSymbol {
      constructor(oResource: sap.galilei.model.Resource, params?: any);
    }

    class GroupSymbol {
      x: any;
      y: any;
      extendToContent(arg0: { extendParent: boolean }, ALL: any, viewer: any): any;
      constructor(oResource: sap.galilei.model.Resource, params?: any);
    }

    class DiagramEditorExtension {}
    class Library {}
  }
}

declare namespace sap.modeling.vflow.ui {
  class DiagramEditorExtension {}
}

declare namespace sap.cdw.taskchainmodeler {
  class Model extends sap.galilei.common.Model {
    constructor(oResource: sap.galilei.model.Resource, params?: any, id?: string);
    label: string;
    isEmpty: boolean;
    hasUnconnectedNodes: boolean;
    documentId: string;
    owner: string;
    modificationDate: string;
    changeManagement: any;
    deploymentDate: string;
    isNew: boolean;
    objectNameDisplay: string;
    executionStatus: any;
    hasUnconnectedOperator: boolean;
    isParallelChainEnabled: boolean;
    isOthersTabEnabled: boolean;
    spaceName: string;
    spaceId: string;
    output: sap.cdw.commonmodel.Entity;
    businessName: string;
    deploymentStatus: any;
    revertLastVersion: boolean;
    technicalName: string;
    diagrams: any;
    diagramBuilder: any;
    bDirty: boolean;
    hasData: boolean;
    isVerticalView: boolean;
    bRedrawDiagram: boolean;
    validations: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Validation>;
    aggregatedValidations: { status: string; validations: sap.cdw.commonmodel.Validation[] };
    validate(): Promise<any>;
    clearValidation(sPropertyName?: string): void;
    nodes: sap.galilei.model.BaseCollection<sap.cdw.taskchainmodeler.Node>;
    links: sap.galilei.model.BaseCollection<sap.cdw.taskchainmodeler.Link>;
    options: sap.galilei.model.BaseCollection<sap.cdw.taskchainmodeler.NotificationOption>;
    packageValue: string;
    packageStatus: [];
    applicationIdActivity: [];
    isBWProcessChainError: boolean;
    error: any;
    connectionList: [];
    isConnectionListEmpty: boolean;
    isConnectionHasError: boolean;
    connectionError: any;
  }

  interface INotChainableMessage {
    messageBundleId: string;
    messageBundleKey: string;
    parameters: [];
  }

  interface IBaseNodeProperties extends sap.galilei.model.Object {
    id: number;
    type: string;
    name: string;
    notChainableMsg: INotChainableMessage;
    links: sap.galilei.model.BaseCollection<sap.cdw.taskchainmodeler.Link>;
    predecessorNodes: sap.galilei.model.BaseCollection<sap.cdw.taskchainmodeler.Node>;
    successorNodes: sap.galilei.model.BaseCollection<sap.cdw.taskchainmodeler.Node>;
    validations: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.ValidationStatus>;
    aggregatedValidations: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.ValidationStatus>;
    clearValidation(sPropertyName?: string): void;
    validate(bSkipRefreshDecorators: boolean): any;
  }
  interface Link extends sap.galilei.common.LinkObject {
    isLinkObject: boolean;
    source: sap.galilei.model.Object;
    target: sap.galilei.model.Object;
    validate(bSkipRefreshDecorators: boolean): any;
  }

  interface Node extends IBaseNodeProperties {
    parameters: any;
    isSQLScriptProcedure: boolean;
    isBWProcessChain: boolean;
    isConsumedInOtherTask: boolean;
    consumedTasks: [];
    applicationId: string;
    activity: string;
    technicalName: string;
    businessName: string;
    nodeIndex: number;
    isChainable: boolean;
    nodeIndexForJSON: number;
    isOperator: boolean;
    isDanglingBranch: boolean;
    isStart: boolean;
    isTask: boolean;
    isCrossSpace: boolean;
    crossSpaceName: string;
    isBWProcessChainDeleted: boolean;
    isRestApi: boolean;
    technicalNameValueState: string;
    connectionNameValueState: string;
    invokeAPI: any;
    statusAPI: any;
    connectionName: string;
    resource: any;
    isNotificationTask: boolean;
  }

  interface EmailNotificationSettings {
    notificationCondition: string;
    body: string;
    subject: string;
  }

  interface NotificationOption {
    emailNotifications: EmailNotificationSettings;
  }

  interface Operator extends IBaseNodeProperties {}

  namespace ui {
    class Diagram {
      constructor(oResource: sap.galilei.model.Resource, params?: any, id?: string);
    }
    class DiagramEditorExtension {}
  }

  class DiagramImpl {
    public static highlightNodes(aNodesToHighlight?: any, oNodeToExclude?: any, bIncludeSuccessorLinks?: boolean): void;
    public static TASKCHAIN_DIAGRAM_CHANNEL: string;
    public static UNDO_REDO_EVENT: string;
    public static DIAGRAM_CHANNEL: string;
    public static PROCESSING_EVENT: string;
    public static getDiagram(oModel: any): any;
    public static getInputSymbol(oSymbol: any): any;
    public static getOutputSymbol(oSymbol: any, oLink: any): any;
    public static getAllLinkSymbolss(oSymbol: any, bIncludeSource?: boolean, bIncludeTarget?: boolean): any;
    public static autoLayout(oDiagramEditor: any, bShowGlobalView?: boolean, oSymbolSelection?: any): void;
    public static addPlaceHolderSymbols(oSymbol: any, oDiagram: any, oDiagramEditor: any): any;
    public static addOperationSymbol(oSymbol: any, oDiagramEditor: any, operator: any): any;
    public static createBeginSymbol(oDiagramEditor: any, oSymbol: any): any;
    public static addBoundarySymbols(oSymbol: any, oDiagram: any): any;
    public static relatedSymbols(oNode: any, oDiagram: any): any;
    public static initiateDiagram(oDiagramEditor: any): any;
    static refreshValidationDecoratorsByModel(model: sap.cdw.taskchainmodeler.DiagramImpl, bRecreate?: boolean): void;
    public static createRestApiTask(oNode: any, oNodeObject: any, oModel: any): any;
  }

  class ModelImpl {
    public static JSONMODEL_CHANNEL: any;
    public static JSONMODEL_LOADED: any;
    public static CREATE_PLACEHOLDER_EVENT: any;

    static getOrCreateModel(
      sName?: string,
      oLoadingData?: any,
      sResourceId?: string,
      oService?: any,
      graphicView?: boolean,
      oCsnDocument?: any
    ): any;

    static createObject(sClassName: string, oParam: any, oContainer: any, index?: number): any;
    static onDropDataSource(oEntity: any, oDropParams: any, isDimension?: boolean): any;
    static requestAdjustDiagramsContent(oOptions?: any): void;
    static onPostDropDataSource(oEntity, oParentEntity?, oOptions?);
    static updateLinks(oEntity, oParentEntity, bLinkCreation);
    static createLinkByConnector(oEntity, oParentEntity, bLinkCreation);
    static getTaskTooltip(oNode);

    public static isDeleting?: boolean;
    static computeSourceEntityName(oNode: any): string;
    static onCascadeChangeNodeIndex(oEventArgs: any): void;
    static onCascadeChangeSuccessor(oEventArgs: any): void;
    static onCascadeDeleteLink(oLink: sap.cdw.taskchainmodeler.Link): void;
    static onCascadeDeleteNode(oNode: any): void;
    static createOrMergeLink(oSourceNode: any, oLink: any): any;
    static updateNodeIndex(oSourceNode: any, nodeIndex: number): any;
    static deleteObject(oNode: any, deleteSymbol: boolean): void;
    static checkBeginPlaceHolder(oModel: any): void;
    static getUniqueAlias(objects: any, name: string): string;
    static getListOfParametersInSQLScriptProcedure(oNode: any): any;
    static showIPPopUP(oNode: any, oParam: any): any;
    static createParameters(oNode: any, oParam: any): any;
  }

  class ModelToJSON {
    static metadata: any;
    public static getJSON(name: string, model: string, o: any): Promise<any>;
  }
  class Validation extends sap.cdw.commonmodel.Validation {
    public static validateLink(
      link: sap.cdw.taskchainmodeler.Link,
      oNode: sap.cdw.taskchainmodeler.Node,
      bSkipRefreshDecorator?: boolean,
      bSkipClear?: boolean
    ): string;
    public static requestRefreshDecorators(oModel: any): void;
    public static validateTask(
      oNode: sap.cdw.taskchainmodeler.Node,
      bSkipRefreshDecorator?: boolean,
      bSkipClear?: boolean
    ): any;
    public static validateOperator(
      oNode: sap.cdw.taskchainmodeler.Node,
      bSkipRefreshDecorator?: boolean,
      bSkipClear?: boolean
    ): any;
    public static checkCircularValidation(oNode: sap.cdw.taskchainmodeler.Node): any;
    public static _checkCircularValidation(oNode: sap.cdw.taskchainmodeler.Node, []: any): any;
    public static checkParallelNodes(oNode: sap.cdw.taskchainmodeler.Node): any;
  }
}

declare namespace sap.cdw.replicationflow {
  class Model extends sap.galilei.common.Model {
    packageValue?: string;
    packageStatus?: string;
    clearValidation();
    revertLastVersion: any;
    constructor(oResource: sap.galilei.model.Resource, params?: any);
    isNew: boolean;
    isEmpty: boolean;
    label: string;
    name: string;
    deploymentDate: string;
    executionDetails: any;
    replicationTasks: sap.galilei.model.BaseCollection<sap.cdw.replicationflow.Task>;
    sourceSystems: sap.galilei.model.BaseCollection<sap.cdw.replicationflow.System>;
    targetSystems: sap.galilei.model.BaseCollection<sap.cdw.replicationflow.System>;
    aggregatedValidations: any;
    isDeltaCheckConfigurationAllowed: boolean;
    isDeltaReplicationsExistsInFlow: boolean;
    deltaCheckIntervalHour: string;
    deltaCheckIntervalMinute: string;
    replicationTaskSetting: sap.cdw.replicationflow.TaskSetting;
    replicationFlowSetting: sap.cdw.replicationflow.FlowSetting;
  }

  interface System extends sap.cdw.commonmodel.BaseObject {
    maxConnections: number;
    systemType: string;
    systemContainer: any;
    connectionType: any;
    connectionId: any;
    metadata: Record<any, any>;
    systemProperties: sap.cdw.replicationflow.Properties;
  }

  interface Task extends sap.cdw.commonmodel.BaseObject {
    validate(arg0: boolean);
    truncate: boolean;
    priority: number;
    loadType: string;
    maxDesiredParallelDeltaTransfers: string;
    sourceSystem: sap.cdw.replicationflow.System;
    targetSystem: sap.cdw.replicationflow.System;
    sourceObject: sap.cdw.replicationflow.Dataset;
    targetObject: sap.cdw.replicationflow.Dataset;
    transform: sap.cdw.replicationflow.Transform;
    rowHighlight: sap.cdw.replicationflow.Highlight;
    loadTypeMetadata: sap.cdw.replicationflow.LoadTypeMetadata;
  }

  interface Transform extends sap.cdw.commonmodel.BaseObject {
    sourceObject: sap.cdw.replicationflow.Dataset;
    targetObject: sap.cdw.replicationflow.Dataset;
    filters: sap.galilei.model.BaseCollection<sap.cdw.replicationflow.Filter>;
    attributeMappings: sap.galilei.model.BaseCollection<sap.cdw.replicationflow.AttributeMapping>;
  }
  interface Filter extends sap.cdw.commonmodel.BaseObject {
    filterElements: sap.galilei.model.BaseCollection<sap.cdw.replicationflow.FilterElement>;
  }

  interface FilterElement extends sap.cdw.commonmodel.BaseObject {
    isExcluding: any;
    high: any;
    comparison: any;
    low: any;
  }

  interface AttributeMapping extends sap.cdw.commonmodel.BaseObject {
    expression: string;
    source: sap.cdw.replicationflow.Attribute;
    target: sap.cdw.replicationflow.Attribute;
  }

  interface Dataset extends sap.cdw.commonmodel.BaseObject {
    isUniqueTechnicalName: boolean;
    isSupportedDataset: boolean;
    setObjectStatus(arg0: any): unknown;
    businessName?: any;
    isDWCDeltaCapture(): boolean;
    isLTF: boolean;
    deltaCaptureTableName: string;
    isDeltaDisabled: boolean;
    datasetType: string;
    attributes: sap.galilei.model.BaseCollection<sap.cdw.replicationflow.Attribute>;
    datasetProperties: sap.cdw.replicationflow.Properties;
    isNew: boolean;
    isAutoRenamed: boolean;
    isCDCDataset: boolean;
    invalidColumns: string[];
    type: string;
    metadata: Record<string, any>;
  }
  interface Attribute extends sap.cdw.commonmodel.BaseObject {
    key: boolean;
    businessName?: string;
    datatype: any;
    name: any;
    scale: any;
    precision: any;
    length: any;
    filterNotAllowed?: boolean;
    isCDCColumn: boolean;
    CDCColumnType?: string;
    isAutoRenamed?: boolean;
    isAutoDatatype?: boolean;
    attributeMapping: sap.cdw.replicationflow.AttributeMapping;
    isDPIDColumn?: boolean;
    metadata: {};
  }

  interface Properties extends sap.cdw.commonmodel.BaseObject {
    kafkaCompressionFormat: any;
    kafkaSerializationType: any;
    kafkaReplicationFactor: any;
    kafkaNumPartitions: any;
    orient: string;
    isHeaderIncluded: string;
    groupDeltaFilesBy: string;
    format: any;
    compression: string;
    columnDelimiter: string;
    customerExit: string;
    "com.sap.rms.write.forceDecfloatsToTargetValueRange": string;
    "objectstore.write.useDuplicateSuppressionInitialLoad": string;
    "objectstore.write.parquet.compatibilityMode": string;
    kafkaUseSchemaRegistry: string;
    kafkaSchemaSubjectNameStrategy: string;
    kafkaSchemaCompatibilityMode: string;
    kafkaSchemaRecordName: string;
    clampingData: boolean;
    failOnIncompatible: boolean;
    maxPartition: number;
    includeSubfolder: boolean;
    fileGlobalPattern: string;
    csvEncoding: string;
    confluentConsumeOtherSchemaVersions: string;
    confluentHandleSchemaMismatch: string;
    confluentFailTruncate: string;
    confluentOffsetMode: string;
    confluentReadSchemaSubject: string;
    confluentReadSchemaVersion: string;
    confluentMessageIdColumn: string;
    confluentSchemaType: string;
    abapTableCategory: string;
    confluentFlatteningConfig?: string;
    confluentReadOpCodeConfig?: Record<string, unknown>;
    includeTechnicalKey?: boolean;
    includeNotExpandedArraysAndMaps?: boolean;
    confluentReadExpandedArray?: string;
    confluentReadExpandedMap?: string;
  }

  class Validation extends sap.cdw.commonmodel.Validation {
    public static requestRefreshDecorators(oModel: any): void;
    public static validateReplicationTask(
      oNode: Task,
      bSkipRefreshDecorators?: boolean,
      bSkipClear?: boolean,
      bSkipNoAttributes?: boolean,
      bSkipDuplicate?: boolean
    ): void;
  }

  interface Highlight extends sap.cdw.commonmodel.BaseObject {
    isError: boolean;
    isWarning: boolean;
    isInfo: boolean;
  }
  interface FlowSetting extends sap.cdw.commonmodel.BaseObject {
    ABAPcontentType: string;
    ABAPcontentTypeDisabled: boolean;
    isAutoMergeEnabledForTarget: boolean;
  }
  interface TaskSetting extends sap.cdw.commonmodel.BaseObject {
    hasSkipMappingCapability: boolean;
    globalDeltaPartitionValue: string;
  }
  interface LoadTypeMetadata extends sap.cdw.commonmodel.BaseObject {
    isInitialSupported: boolean;
    isInitialAndDeltaSupported: boolean;
    isDeltaOnlySupported: boolean;
    supportedLoadTypes: string[];
    isLoadTypeChanged: boolean;
  }
}
