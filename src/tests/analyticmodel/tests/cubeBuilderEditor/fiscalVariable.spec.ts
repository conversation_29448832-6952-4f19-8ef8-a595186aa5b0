/** @format */
import { assert } from "chai";
import _ from "lodash";
import sinon from "sinon";
import {
  AnalyticModelParameterType,
  AnalyticModelVariableProcessingType,
} from "../../../../../shared/queryBuilder/AnalyticModel";
import { elements, fiscalDataEntityDetails } from "../../mocks/fiscalDimension";
import { cleanup, getCubeBuilderEditor, initController } from "../../utils/utils";

let controller;
let sandbox: sinon.SinonSandbox;
describe("src/tests/analyticmodel/tests/cubeBuilderEditor/fiscalVariable.spec.ts", () => {
  beforeEach(() => {
    sandbox = sinon.createSandbox();
    controller = getCubeBuilderEditor();
    initController(controller, sandbox, {
      DWCO_MODELING_SUPPORT_FISCAL_TIME: true,
    });
    controller.uiModel.setProperty("/allSourceDataEntityDetails", _.cloneDeep(fiscalDataEntityDetails));
    sandbox.stub(controller.model, "getFirstFactSource").returns({
      dataEntity: {
        key: "MCT_OpportunityItems_FiscalJoao",
      },
    });
  });

  afterEach(() => {
    cleanup(controller, controller.getOwnerComponent());
    sandbox.restore();
  });

  it("a fiscal variant variable should be created if the analytic model has a fiscal dimension", async () => {
    await controller.setQueryModelProperties(elements);
    const variables = controller.model.getProperty("/variables");
    assert.deepStrictEqual(variables, {
      FISCAL_VARIANT: {
        text: "Fiscal Variant",
        parameterType: AnalyticModelParameterType.FiscalVariant,
        variableProcessingType: AnalyticModelVariableProcessingType.MANUAL_INPUT,
        order: 1,
      },
    });
  });
});
