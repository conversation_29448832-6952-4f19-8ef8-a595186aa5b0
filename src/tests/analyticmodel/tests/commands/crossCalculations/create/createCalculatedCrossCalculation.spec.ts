/** @format */

import { expect } from "chai";
import { AnalyticModelCrossCalculationType } from "../../../../../../../shared/queryBuilder/AnalyticModel";
import CommandStack from "../../../../../../components/cubebuilder/command/AnalyticModelCommandStack";
import { CreateCalculatedCrossCalculation } from "../../../../../../components/cubebuilder/command/crossCalculations/create/CreateCalculatedCrossCalculation";
import QueryUIModel from "../../../../../../components/cubebuilder/model/QueryUIModel";
import {
  createDummyCdsFormulaToExprService,
  createDummyDataEntityDetailsService,
  createFeatureFlagModel,
} from "../../../../utils/utils";

describe("src/tests/analyticmodel/tests/commands/crossCalculations/create/createCalculatedCrossCalculation.spec.ts", () => {
  let model: QueryUIModel;
  let cs;
  let featureFlagModel;
  let i18nModel;

  beforeEach(() => {
    model = new QueryUIModel();
    featureFlagModel = createFeatureFlagModel();
    i18nModel = new sap.ui.model.resource.ResourceModel({
      bundleName: require("../../../../../../components/cubebuilder/i18n/i18n.properties"),
    });
    cs = new CommandStack(
      model,
      featureFlagModel,
      i18nModel,
      createDummyDataEntityDetailsService(),
      createDummyCdsFormulaToExprService()
    );
  });

  it("It should create calculated cross calculation and undo the changes", () => {
    // Creates the command instance to add the cross calculation to the model
    const createCalculatedCrossCalculation = new CreateCalculatedCrossCalculation();
    // Use the stack to execute the command
    cs.execute(createCalculatedCrossCalculation);

    expect(cs.getState().oData.crossCalculations).to.have.property("Calculated_Cross_Calculation");
    expect(cs.getState().oData.crossCalculations.Calculated_Cross_Calculation).to.deep.equal({
      text: "Calculated Cross Calculation",
      formulaRaw: "",
      crossCalculationType: AnalyticModelCrossCalculationType.CalculatedCrossCalculation,
    });
    cs.undo();
    // Cross calculations does not exist on initial query model
    expect(cs.getState().oData).to.not.have.property("crossCalculations");
  });
});
