/** @format */

import { expect } from "chai";
import { AnalyticModelCrossCalculationType } from "../../../../../../../shared/queryBuilder/AnalyticModel";
import { IDataEntityCrossCalculation } from "../../../../../../../shared/queryBuilder/DataEntityDetails";
import CommandStack from "../../../../../../components/cubebuilder/command/AnalyticModelCommandStack";
import { CreateFactSourceCrossCalculation } from "../../../../../../components/cubebuilder/command/crossCalculations/create/CreateFactSourceCrossCalculation";
import QueryUIModel from "../../../../../../components/cubebuilder/model/QueryUIModel";
import {
  createDummyCdsFormulaToExprService,
  createDummyDataEntityDetailsService,
  createFeatureFlagModel,
} from "../../../../utils/utils";

describe("src/tests/analyticmodel/tests/commands/crossCalculations/create/createFactSourceCrossCalculation.spec.ts", () => {
  let model: QueryUIModel;
  let cs;
  let featureFlagModel;
  let i18nModel;

  beforeEach(() => {
    model = new QueryUIModel();
    featureFlagModel = createFeatureFlagModel();
    i18nModel = new sap.ui.model.resource.ResourceModel({
      bundleName: require("../../../../../../components/cubebuilder/i18n/i18n.properties"),
    });
    cs = new CommandStack(
      model,
      featureFlagModel,
      i18nModel,
      createDummyDataEntityDetailsService(),
      createDummyCdsFormulaToExprService()
    );
  });

  it("It should create fact source cross calculation and undo the changes", () => {
    const key = "Fact_Source_Cross_Calculation";
    const dataEntityCrossCalculation: IDataEntityCrossCalculation = {
      crossCalculationType: AnalyticModelCrossCalculationType.RestrictedCrossCalculation,
      key: "Cross_Calculation_Key_1",
      text: "Cross Calculation Text 1",
    };
    const sourceKey = "Fact_Source_Key_1";

    const createFactSourceCrossCalculation = new CreateFactSourceCrossCalculation(
      key,
      dataEntityCrossCalculation,
      sourceKey
    );
    cs.execute(createFactSourceCrossCalculation);

    expect(cs.getState().oData.crossCalculations).to.have.property("Fact_Source_Cross_Calculation");
    expect(cs.getState().oData.crossCalculations.Fact_Source_Cross_Calculation).to.deep.equal({
      crossCalculationType: AnalyticModelCrossCalculationType.FactSourceCrossCalculation,
      isAuxiliary: false,
      key: "Cross_Calculation_Key_1",
      sourceKey: "Fact_Source_Key_1",
      text: "Cross Calculation Text 1",
    });
    cs.undo();
    expect(cs.getState().oData).to.not.have.property("crossCalculations");
  });
});
