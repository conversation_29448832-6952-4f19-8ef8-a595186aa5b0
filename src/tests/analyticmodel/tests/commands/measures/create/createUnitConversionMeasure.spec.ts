/** @format */

import { expect } from "chai";
import { AnalyticModelMeasureType } from "../../../../../../../shared/queryBuilder/AnalyticModel";
import CommandStack from "../../../../../../components/cubebuilder/command/AnalyticModelCommandStack";
import { CreateConversionMeasure } from "../../../../../../components/cubebuilder/command/measures/create/CreateConversionMeasure";
import QueryUIModel from "../../../../../../components/cubebuilder/model/QueryUIModel";
import { factSourceForUnitConversion } from "../../../../mocks/sourceModelCommands";
import { allSourceDataEntityDetails as allSourceDataEntityDetails1 } from "../../../../mocks/variableDetails";
import {
  createDummyCdsFormulaToExprService,
  createDummyDataEntityDetailsService,
  createFeatureFlagModel,
  setAllSourceDataEntityDetailsOnStack,
} from "../../../../utils/utils";
describe("src/tests/analyticmodel/tests/commands/measures/create/createUnitConversionMeasure.spec.ts", () => {
  let model: QueryUIModel;
  let cs;
  let featureFlagModel;
  let i18nModel;

  beforeEach(() => {
    model = new QueryUIModel();
    featureFlagModel = createFeatureFlagModel({ DWCO_MODELING_AM_UNIT_CONVERSION: true });
    i18nModel = new sap.ui.model.resource.ResourceModel({
      bundleName: require("../../../../../../components/cubebuilder/i18n/i18n.properties"),
    });
    cs = new CommandStack(
      model,
      featureFlagModel,
      i18nModel,
      createDummyDataEntityDetailsService(),
      createDummyCdsFormulaToExprService()
    );
    model.setFactSource("0", factSourceForUnitConversion);
  });

  it("It should create a Unit Conversion Measure and undo the changes", () => {
    const QuantityMeasure = {
      measureType: AnalyticModelMeasureType.FactSourceMeasure,
      sourceKey: "0",
      text: "Quantity",
      key: "Quantity",
      isAuxiliary: false,
    };
    model.setMeasure("Quantity1", QuantityMeasure as any);
    const clientValue = { key: "000", value: "000" };
    // Creates the command instance to set Non Cumulative Measure
    const newUnitConversionMeasure = new CreateConversionMeasure(clientValue);
    setAllSourceDataEntityDetailsOnStack(cs, allSourceDataEntityDetails1);
    cs.execute(newUnitConversionMeasure);
    expect(cs.getState().oData.measures.Conversion_Measure.client).to.deep.equal("000");
    expect(cs.getState().oData.measures.Conversion_Measure.targetUnitType).to.deep.equal(
      "AnalyticModelTargetUnitType.constantValue"
    );
    expect(cs.getState().oData.measures.Conversion_Measure.errorHandling).to.deep.equal(
      "AnalyticModelErrorHandling.null"
    );
    expect(cs.getState().oData.measures.Conversion_Measure.isAuxiliary).to.deep.equal(false);
    expect(cs.getState().oData.measures.Conversion_Measure.measureType).to.deep.equal(
      "AnalyticModelMeasureType.UnitConversionMeasure"
    );
    expect(cs.getState().oData.measures.Conversion_Measure.sourceUnitType).to.deep.equal(
      "AnalyticModelSourceUnitType.derived"
    );
    expect(cs.getState().oData.measures.Conversion_Measure.text).to.deep.equal("Conversion Measure");
    cs.undo();
    expect(cs.getState().oData.measures).to.not.have.property("Conversion_Measure");
  });

  it("It should always create a Unit Conversion Measure with Client as 000", () => {
    const QuantityMeasure = {
      measureType: AnalyticModelMeasureType.FactSourceMeasure,
      sourceKey: "0",
      text: "Quantity",
      key: "Quantity",
      isAuxiliary: false,
    };
    model.setMeasure("Quantity1", QuantityMeasure as any);
    const clientValue = { key: "100", value: "100" };
    // Creates the command instance to set Non Cumulative Measure
    const newUnitConversionMeasure = new CreateConversionMeasure(clientValue);
    setAllSourceDataEntityDetailsOnStack(cs, allSourceDataEntityDetails1);
    cs.execute(newUnitConversionMeasure);
    expect(cs.getState().oData.measures.Conversion_Measure.client).to.deep.equal("000");
  });
});
