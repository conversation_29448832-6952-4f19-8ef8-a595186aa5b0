/** @format */

import { expect } from "chai";
import CommandStack from "../../../../../components/cubebuilder/command/AnalyticModelCommandStack";
import QueryUIModel from "../../../../../components/cubebuilder/model/QueryUIModel";

import { AnalyticModelCollisionHandlingPriority } from "../../../../../../shared/queryBuilder/AnalyticModel";
import { UpdateCollisionHandlingPriority } from "../../../../../components/cubebuilder/command/collisionHandling/UpdateCollisionHandlingPriority";
import { ResourceBundle, ResourceModel } from "../../../utils/mockClasses";
import { createDummyCdsFormulaToExprService, createDummyDataEntityDetailsService } from "../../../utils/utils";

describe("src/tests/analyticmodel/tests/commands/collisionHandling/updateCollisionHandlingPriority.spec.ts", () => {
  let model: QueryUIModel;
  let cs;
  let featureFlagModel;
  let i18nModel;

  beforeEach(() => {
    model = new QueryUIModel();
    i18nModel = new ResourceModel(new ResourceBundle());
    cs = new CommandStack(
      model,
      featureFlagModel,
      i18nModel,
      createDummyDataEntityDetailsService(),
      createDummyCdsFormulaToExprService()
    );

    model.setCollisionHandlingPriority(AnalyticModelCollisionHandlingPriority.Measures);
  });

  it("should update the collision handling priority and undo the changes", () => {
    const command = new UpdateCollisionHandlingPriority(AnalyticModelCollisionHandlingPriority.CrossCalculations);
    cs.execute(command);
    expect(cs.getState().oData).to.have.property("collisionHandlingPriority");
    expect(cs.getState().oData.collisionHandlingPriority).to.deep.equal(
      AnalyticModelCollisionHandlingPriority.CrossCalculations
    );
    cs.undo();
    expect(cs.getState().oData.collisionHandlingPriority).to.deep.equal(
      AnalyticModelCollisionHandlingPriority.Measures
    );
  });

  it("should not change the collision handling priority if it is already set to the same value", () => {
    const initialPriority = model.getCollisionHandlingPriority();
    const command = new UpdateCollisionHandlingPriority(initialPriority);
    const response = cs.execute(command);
    expect(response.success).to.be.false;
    expect(cs.getState().oData.collisionHandlingPriority).to.deep.equal(initialPriority);
    expect(cs.stack.length).to.equal(0);
  });
});
