/** @format */

import { assert } from "chai";
import _ from "lodash";
import sinon from "sinon";
import { DbHints } from "../../../../../shared/queryBuilder/QueryModel";
import { CsnAnnotations } from "../../../../components/commonmodel/csn/csnAnnotations";
import { cleanup, getComponent } from "../../utils/utils";

let component;
let sandbox: sinon.SinonSandbox;
describe("src/tests/analyticmodel/tests/component/dbHints.spec.ts", () => {
  context("DWCO_MODELING_HINTS_FOR_MDS feature flag enabled", () => {
    beforeEach(() => {
      sandbox = sinon.createSandbox();
      component = getComponent(sandbox, { DWCO_MODELING_HINTS_FOR_MDS: true });
    });

    afterEach(() => {
      cleanup(undefined, component);
      sandbox.restore();
    });

    it("should not set dbHints if csn does not have the proper annotation", async () => {
      const csn = {
        "not.dbHint.annotation": {
          test: "test",
        },
      };
      await component.setDbHintsProperty(csn);

      const dbHints = component.uiModel.getProperty("/dbHints");
      assert.deepEqual(dbHints, undefined);
    });

    it("should set dbHints if csn have the proper annotation", async () => {
      const expectedDbHints: DbHints = [
        {
          engine: "MDS",
          hints: [
            { key: "OPTIMIZE", value: "true" },
            { key: "CACHE", value: "enabled" },
          ],
        },
        {
          engine: "SQL",
          hints: [{ key: "INDEX", value: "my_index" }],
        },
      ];
      const csn = {
        [CsnAnnotations.Analytics.dbHints]: _.cloneDeep(expectedDbHints),
      };
      await component.setDbHintsProperty(csn);

      const dbHints = component.uiModel.getProperty("/dbHints");
      assert.deepEqual(dbHints, expectedDbHints);
    });
  });

  context("DWCO_MODELING_HINTS_FOR_MDS feature flag disabled", () => {
    beforeEach(() => {
      sandbox = sinon.createSandbox();
      component = getComponent(sandbox, { DWCO_MODELING_HINTS_FOR_MDS: false });
    });

    afterEach(() => {
      cleanup(undefined, component);
      sandbox.restore();
    });

    it("should not set dbHints if csn does not have the proper annotation", async () => {
      const csn = {
        "not.dbHint.annotation": {
          test: "test",
        },
      };
      await component.setDbHintsProperty(csn);

      const dbHints = component.uiModel.getProperty("/dbHints");
      assert.deepEqual(dbHints, undefined);
    });

    it("should not set dbHints if csn have the proper annotation", async () => {
      const expectedDbHints: DbHints = [
        {
          engine: "MDS",
          hints: [
            { key: "OPTIMIZE", value: "true" },
            { key: "CACHE", value: "enabled" },
          ],
        },
        {
          engine: "SQL",
          hints: [{ key: "INDEX", value: "my_index" }],
        },
      ];
      const csn = {
        [CsnAnnotations.Analytics.dbHints]: _.cloneDeep(expectedDbHints),
      };
      await component.setDbHintsProperty(csn);

      const dbHints = component.uiModel.getProperty("/dbHints");
      assert.deepEqual(dbHints, undefined);
    });
  });
});
