/** @format */

import { expect } from "chai";
import sinon from "sinon";
import { AnalyticModelParameterType } from "../../../../../shared/queryBuilder/AnalyticModel";
import { UsedInType } from "../../../../components/cubebuilder/utility/Enum";
import Formatter from "../../../../components/cubebuilder/utility/Formatter";
import { cleanup, getModelProperties, initController } from "../../utils/utils";

let amFormatter = Formatter;
const multiStructureActive = true;
const isNotStackedVariable = false;
let controller;
let sandbox: sinon.SinonSandbox;

describe("src/tests/analyticmodel/tests/impactedElements/restrictionVariableLabels.spec.ts", () => {
  beforeEach(() => {
    sandbox = sinon.createSandbox();
    controller = getModelProperties();
    initController(controller, sandbox, {});
    amFormatter = controller.amFormatter;
  });

  afterEach(() => {
    cleanup(controller, controller.getOwnerComponent());
    sandbox.restore();
  });

  // usedInTokenDescriptionFormatter and variableTypeTextFormatter are covered in the
  // same test, because we call variableTypeTextFormatter from these functions
  it("should return the correct label for variableTypeTextFormatter", () => {
    const label = amFormatter.variableTypeTextFormatter(
      AnalyticModelParameterType.Filter,
      isNotStackedVariable, // staking isn't relevant here
      multiStructureActive
    );
    const expectedLabel = "restrictionVariable";
    expect(label).to.equal(expectedLabel);
  });

  it("should return the correct label for usedInAttributesPopoverFormatter", () => {
    const label = amFormatter.usedInAttributesPopoverFormatter(
      "Restriction Variable",
      "Restriction_Variable",
      undefined, // without full name
      UsedInType.VARIABLE,
      AnalyticModelParameterType.Filter,
      undefined, // without display name configuration
      multiStructureActive
    );
    const expectedLabel = "Restriction Variable (restrictionVariable)";
    expect(label).to.equal(expectedLabel);
  });

  // objectUsedInDescription
  it("should return the correct label for objectUsedInDescription", () => {
    const label = amFormatter.objectUsedInDescription(
      AnalyticModelParameterType.Filter,
      undefined, // without text parameters
      multiStructureActive
    );
    const expectedLabel = "restrictionVariable";
    expect(label).to.equal(expectedLabel);
  });
});
