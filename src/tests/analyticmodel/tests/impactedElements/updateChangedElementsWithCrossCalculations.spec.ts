/** @format */
import { assert } from "chai";
import _ from "lodash";
import sinon from "sinon";
import { ImpactedElements } from "../../../../components/cubebuilder/extensions/dependencies/ImpactedElements";
import QueryUIModel from "../../../../components/cubebuilder/model/QueryUIModel";
import { AnalyticModelObjectType } from "../../../../components/cubebuilder/utility/Enum";
import { dependenciesModel, dependenciesUiModel } from "../../mocks/analyticModelPropertiesBase";
import { cleanup, getModelProperties, initController } from "../../utils/utils";

let controller;
let sandbox: sinon.SinonSandbox;
let currentModel;
let initialModel;
describe("src/tests/analyticmodel/tests/impactedElements/updateChangedElementsWithCrossCalculations.spec.ts", () => {
  beforeEach(() => {
    sandbox = sinon.createSandbox();
    // just so global properties are initialized correctly
    controller = getModelProperties();
    initController(controller, sandbox, {});

    controller.model.setData(_.cloneDeep(dependenciesModel));
    controller.uiModel.setData(_.cloneDeep(dependenciesUiModel));

    currentModel = controller.model;
    initialModel = new QueryUIModel(currentModel.getData());
  });

  afterEach(() => {
    cleanup(controller, controller.getOwnerComponent());
    sandbox.restore();
  });

  it("should ignore changes to cross calculation text", () => {
    currentModel.oData.crossCalculations.RESTRICTED_CC.text = "a";

    const impactedElements: any = {
      [AnalyticModelObjectType.CROSS_CALCULATION]: {},
    };
    new ImpactedElements(currentModel, initialModel)["updateChangedElementsWithCrossCalculations"](impactedElements);
    assert.deepStrictEqual(impactedElements, {
      [AnalyticModelObjectType.CROSS_CALCULATION]: {},
    });
  });

  it("should consider only isAuxiliary changes if it was changed from false to true", () => {
    currentModel.oData.crossCalculations.RESTRICTED_CC.isAuxiliary = false;
    initialModel.oData.crossCalculations.RESTRICTED_CC.isAuxiliary = false;
    let impactedElements: any = {
      [AnalyticModelObjectType.CROSS_CALCULATION]: {},
    };
    new ImpactedElements(currentModel, initialModel)["updateChangedElementsWithCrossCalculations"](impactedElements);
    assert.deepStrictEqual(impactedElements, {
      [AnalyticModelObjectType.CROSS_CALCULATION]: {},
    });

    currentModel.oData.crossCalculations.RESTRICTED_CC.isAuxiliary = true;
    initialModel.oData.crossCalculations.RESTRICTED_CC.isAuxiliary = true;
    impactedElements = {
      [AnalyticModelObjectType.CROSS_CALCULATION]: {},
    };
    new ImpactedElements(currentModel, initialModel)["updateChangedElementsWithCrossCalculations"](impactedElements);
    assert.deepStrictEqual(impactedElements, {
      [AnalyticModelObjectType.CROSS_CALCULATION]: {},
    });

    currentModel.oData.crossCalculations.RESTRICTED_CC.isAuxiliary = false;
    initialModel.oData.crossCalculations.RESTRICTED_CC.isAuxiliary = true;
    impactedElements = {
      [AnalyticModelObjectType.CROSS_CALCULATION]: {},
    };
    new ImpactedElements(currentModel, initialModel)["updateChangedElementsWithCrossCalculations"](impactedElements);
    assert.deepStrictEqual(impactedElements, {
      [AnalyticModelObjectType.CROSS_CALCULATION]: {},
    });

    currentModel.oData.crossCalculations.RESTRICTED_CC.isAuxiliary = true;
    initialModel.oData.crossCalculations.RESTRICTED_CC.isAuxiliary = false;
    impactedElements = {
      [AnalyticModelObjectType.CROSS_CALCULATION]: {},
    };
    new ImpactedElements(currentModel, initialModel)["updateChangedElementsWithCrossCalculations"](impactedElements);
    assert.deepStrictEqual(impactedElements, {
      [AnalyticModelObjectType.CROSS_CALCULATION]: {
        RESTRICTED_CC: {
          "GC_DEPENDENCIES.RESTRICTED_CC": {
            entity: "GC_DEPENDENCIES",
            key: "RESTRICTED_CC",
          },
        },
      },
    });
  });

  it("should consider other changes", () => {
    currentModel.oData.crossCalculations.RESTRICTED_CC.constantSelectionType = "ALL";
    let impactedElements: any = {
      [AnalyticModelObjectType.CROSS_CALCULATION]: {},
    };
    new ImpactedElements(currentModel, initialModel)["updateChangedElementsWithCrossCalculations"](impactedElements);
    assert.deepStrictEqual(impactedElements, {
      [AnalyticModelObjectType.CROSS_CALCULATION]: {
        RESTRICTED_CC: {
          "GC_DEPENDENCIES.RESTRICTED_CC": {
            entity: "GC_DEPENDENCIES",
            key: "RESTRICTED_CC",
          },
        },
      },
    });
  });

  it("should consider deleted cross calculation", () => {
    delete currentModel.oData.crossCalculations.RESTRICTED_CC;
    let impactedElements: any = {
      [AnalyticModelObjectType.CROSS_CALCULATION]: {},
    };
    new ImpactedElements(currentModel, initialModel)["updateChangedElementsWithCrossCalculations"](impactedElements);
    assert.deepStrictEqual(impactedElements, {
      [AnalyticModelObjectType.CROSS_CALCULATION]: {
        RESTRICTED_CC: {
          "GC_DEPENDENCIES.RESTRICTED_CC": {
            entity: "GC_DEPENDENCIES",
            key: "RESTRICTED_CC",
          },
        },
      },
    });
  });

  it("should not consider new cross calculation", () => {
    currentModel.oData.crossCalculations.CALCULATED_CC = _.cloneDeep(
      currentModel.oData.crossCalculations.CALCULATED_CC
    );
    let impactedElements: any = {
      [AnalyticModelObjectType.CROSS_CALCULATION]: {},
    };
    new ImpactedElements(currentModel, initialModel)["updateChangedElementsWithCrossCalculations"](impactedElements);
    assert.deepStrictEqual(impactedElements, {
      [AnalyticModelObjectType.CROSS_CALCULATION]: {},
    });
  });

  it("should ignore changes related to parsing of formulas", () => {
    currentModel.oData.crossCalculations.RESTRICTED_CC.formula = "a";
    currentModel.oData.crossCalculations.RESTRICTED_CC.elements = "b";
    currentModel.oData.crossCalculations.CALCULATED_CC.formula = "a";
    currentModel.oData.crossCalculations.CALCULATED_CC.elements = "b";

    initialModel.oData.crossCalculations.RESTRICTED_CC.formula = "a";
    initialModel.oData.crossCalculations.RESTRICTED_CC.elements = "b";
    initialModel.oData.crossCalculations.CALCULATED_CC.formula = "a";
    initialModel.oData.crossCalculations.CALCULATED_CC.elements = "b";

    let impactedElements: any = {
      [AnalyticModelObjectType.CROSS_CALCULATION]: {},
    };
    new ImpactedElements(currentModel, initialModel)["updateChangedElementsWithCrossCalculations"](impactedElements);
    assert.deepStrictEqual(impactedElements, {
      [AnalyticModelObjectType.CROSS_CALCULATION]: {},
    });
  });
});
