/** @format */
import { assert } from "chai";
import sinon from "sinon";
import { allSourceDataEntityDetailsPopoverVariablesModel, popoverVariablesModel } from "../../mocks/popover";
import { cleanup, getModelProperties, initController } from "../../utils/utils";

let controller;
let sandbox: sinon.SinonSandbox;
let variableInfoPopover: sap.m.Popover;
describe("src/tests/analyticmodel/tests/popovers/variablePopover.spec.ts", () => {
  beforeEach(() => {
    sandbox = sinon.createSandbox();
    controller = getModelProperties();
    initController(controller, sandbox, {});
    variableInfoPopover = new sap.m.Popover();
    controller.popoverHandler.variableInfoPopover = variableInfoPopover;
  });

  afterEach(() => {
    cleanup(controller, controller.getOwnerComponent());
    sandbox.restore();
  });

  const mockModels = () => {
    controller.uiModel.setProperty("/allSourceDataEntityDetails", allSourceDataEntityDetailsPopoverVariablesModel);
    controller.uiModel.setProperty("/variableModel", [
      {
        parameterType: "AnalyticModelParameterType.Input",
        text: "ValueParam",
        defaultValue: "EUR",
        order: 1,
        key: "VALUEPARAM",
        selected: false,
        hidden: false,
        _validationMessageType: "Success",
        mandatory: true,
        usedInSource: {
          text: "MCT_OpportunityItems_View",
          dataEntity: {
            key: "MCT_OpportunityItems_View",
          },
          parameterMappings: {
            VALUEPARAM: {
              mappingType: "AnalyticModelSourceParameterMappingType.MapToSourceParameter",
              variableName: "VALUEPARAM",
            },
          },
        },
        groupUnderDimension: {
          text: "MCT_OpportunityItems_View",
          dataEntity: {
            key: "MCT_OpportunityItems_View",
          },
          parameterMappings: {
            VALUEPARAM: {
              mappingType: "AnalyticModelSourceParameterMappingType.MapToSourceParameter",
              variableName: "VALUEPARAM",
            },
          },
        },
      },
      {
        text: "VIP_Rebate",
        parameterType: "AnalyticModelParameterType.Input",
        variableProcessingType: "AnalyticModelVariableProcessingType.Lookup",
        order: 2,
        dataType: {
          type: "cds.Integer64",
        },
        lookupEntity: "Derivation_lookup_status_from_OpportunityItems_for",
        resultElement: "ItemStatus",
        parameterBinding: {},
        key: "VIP_REBATE",
        selected: false,
        hidden: true,
        _validationMessageType: "Success",
        mandatory: true,
        usedInSource: {
          text: "MCT_OpportunityItems_View",
          dataEntity: {
            key: "MCT_OpportunityItems_View",
          },
          parameterMappings: {
            VALUEPARAM: {
              mappingType: "AnalyticModelSourceParameterMappingType.MapToSourceParameter",
              variableName: "VALUEPARAM",
            },
          },
        },
        groupUnderDimension: {
          text: "MCT_OpportunityItems_View",
          dataEntity: {
            key: "MCT_OpportunityItems_View",
          },
          parameterMappings: {
            VALUEPARAM: {
              mappingType: "AnalyticModelSourceParameterMappingType.MapToSourceParameter",
              variableName: "VALUEPARAM",
            },
          },
        },
      },
      {
        referenceAttribute: "Currency",
        parameterType: "AnalyticModelParameterType.StoryFilter",
        selectionType: "AnalyticModelVariableSelectionType.SINGLE",
        variableProcessingType: "AnalyticModelVariableProcessingType.DynamicDefault",
        multipleSelections: false,
        mandatory: false,
        order: 3,
        lookupEntity: "BBDEV.Neha_CurrencyOfCountry",
        resultElement: "Currency",
        parameterBinding: {
          COUNTRY: {
            mappingType: "AnalyticModelSourceParameterMappingType.ConstantValue",
            constantValue: "DE",
          },
        },
        key: "Currency",
        selected: false,
        hidden: false,
        text: "Currency",
        _validationMessageType: "Warning",
        usedInSource: "Currency",
        groupUnderDimension: {
          text: "MCT_OpportunityItems_View",
          dataEntity: {
            key: "MCT_OpportunityItems_View",
          },
          parameterMappings: {
            VALUEPARAM: {
              mappingType: "AnalyticModelSourceParameterMappingType.MapToSourceParameter",
              variableName: "VALUEPARAM",
            },
          },
        },
      },
    ]);
    controller.model.setData(popoverVariablesModel);
  };

  it("variable info popovers with shared derivation entity (with all possibilities for used in)", async () => {
    mockModels();

    const variableKey = "Currency";
    const data = controller.popoverHandler.getVariableData(variableKey);
    variableInfoPopover.setModel(new sap.ui.model.json.JSONModel(data));
    await controller.popoverHandler.updateLookupEntityDetails(data);

    const value = data;
    const expectedValue = {
      referenceAttribute: "Currency",
      parameterType: "AnalyticModelParameterType.StoryFilter",
      selectionType: "AnalyticModelVariableSelectionType.SINGLE",
      variableProcessingType: "AnalyticModelVariableProcessingType.DynamicDefault",
      multipleSelections: false,
      mandatory: false,
      order: 3,
      lookupEntity: "BBDEV.Neha_CurrencyOfCountry",
      resultElement: "Currency",
      parameterBinding: {
        COUNTRY: {
          mappingType: "AnalyticModelSourceParameterMappingType.ConstantValue",
          constantValue: "DE",
        },
      },
      key: "Currency",
      selected: false,
      hidden: false,
      text: "Currency",
      _validationMessageType: "Warning",
      usedInSource: "Currency",
      groupUnderDimension: {
        text: "MCT_OpportunityItems_View",
        dataEntity: {
          key: "MCT_OpportunityItems_View",
        },
        parameterMappings: {
          VALUEPARAM: {
            mappingType: "AnalyticModelSourceParameterMappingType.MapToSourceParameter",
            variableName: "VALUEPARAM",
          },
        },
      },
      usedIn:
        '<a target="_self" href=#/databuilder&/db/space/model/option/folderAssignment/attribute/Currency>variableInfoFilterOnAttributeText</a><br>',
      usedInCount: 1,
      usedInMaxChars: 137,
      valueHelp: "-",
      dataType: "String(500)",
      dimension: "Currency (MCT_OpportunityItems_View)",
      lookupEntityText: "Neha_Currency Of Country",
    };
    assert.deepStrictEqual(value, expectedValue);
  });
});
