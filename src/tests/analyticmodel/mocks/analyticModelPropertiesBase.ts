/** @format */

export const dependenciesModel = {
  identifier: {
    key: "GC_DEPENDENCIES",
  },
  text: "GC_DEPENDENCIES",
  sourceModel: {
    factSources: {
      GC_VIEW_PARAMS: {
        text: "GC_VIEW_PARAMS",
        dataEntity: {
          key: "GC_VIEW_PARAMS",
        },
        parameterMappings: {
          PARAM_1: {
            mappingType: "AnalyticModelSourceParameterMappingType.MapToSourceParameter",
            variableName: "PARAM_1",
          },
        },
      },
    },
    dimensionSources: {
      _Calculated_1: {
        key: "_Calculated_1",
        text: "Calculated_1",
        dataEntity: {
          key: "SAP.TIME.VIEW_DIMENSION_DAY",
        },
        associationContexts: [
          {
            sourceKey: "GC_VIEW_PARAMS",
            sourceType: "AnalyticModelSourceType.Fact",
            associationSteps: ["_VIEW_DIME"],
          },
        ],
        technicalAffix: {
          text: "_Calculated_1",
          type: "AnalyticModelDimensionAffixType.SUFFIX",
        },
      },
    },
  },
  exposedAssociations: {},
  attributes: {
    ItemID: {
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      attributeMapping: {
        GC_VIEW_PARAMS: {
          key: "ItemID",
        },
      },
      text: "ItemID",
      duplicated: false,
    },
    Opportunity: {
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      attributeMapping: {
        GC_VIEW_PARAMS: {
          key: "Opportunity",
        },
      },
      text: "Opportunity",
      duplicated: false,
    },
    Product: {
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      attributeMapping: {
        GC_VIEW_PARAMS: {
          key: "Product",
        },
      },
      text: "Product",
      duplicated: false,
    },
    Unit: {
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      attributeMapping: {
        GC_VIEW_PARAMS: {
          key: "Unit",
        },
      },
      text: "Unit",
      duplicated: false,
    },
    Currency: {
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      attributeMapping: {
        GC_VIEW_PARAMS: {
          key: "Currency",
        },
      },
      text: "Currency",
      duplicated: false,
    },
    FactAttr: {
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      attributeMapping: {
        GC_VIEW_PARAMS: {
          key: "FactAttr",
        },
      },
      text: "FactAttr",
      duplicated: false,
    },
    ItemStatus: {
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      attributeMapping: {
        GC_VIEW_PARAMS: {
          key: "ItemStatus",
        },
      },
      text: "ItemStatus ID",
      duplicated: false,
    },
    ControllingArea: {
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      attributeMapping: {
        GC_VIEW_PARAMS: {
          key: "ControllingArea",
        },
      },
      text: "Controlling Area",
      duplicated: false,
    },
    SendingCostCenter: {
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      attributeMapping: {
        GC_VIEW_PARAMS: {
          key: "SendingCostCenter",
        },
      },
      text: "Sending Cost Center",
      duplicated: false,
    },
    ReceivingCostCenter: {
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      attributeMapping: {
        GC_VIEW_PARAMS: {
          key: "ReceivingCostCenter",
        },
      },
      text: "Receiving Cost Center",
      duplicated: false,
      isAuxiliary: true,
    },
    Calculated_1: {
      text: "Calculated_1",
      attributeMapping: {
        GC_VIEW_PARAMS: {
          key: "Calculated_1",
        },
      },
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      duplicated: false,
      usedForDimensionSourceKey: "_Calculated_1",
    },
    YEAR_Calculated_1: {
      key: "YEAR",
      text: "Year",
      sourceKey: "_Calculated_1",
      attributeType: "AnalyticModelAttributeType.DimensionSourceAttribute",
      duplicated: false,
    },
    CALQUARTER_Calculated_1: {
      key: "CALQUARTER",
      text: "Calendar Quarter",
      sourceKey: "_Calculated_1",
      attributeType: "AnalyticModelAttributeType.DimensionSourceAttribute",
      duplicated: false,
    },
    CALMONTH_Calculated_1: {
      key: "CALMONTH",
      text: "Calendar Month",
      sourceKey: "_Calculated_1",
      attributeType: "AnalyticModelAttributeType.DimensionSourceAttribute",
      duplicated: false,
    },
    CALWEEK_Calculated_1: {
      key: "CALWEEK",
      text: "Calendar Week",
      sourceKey: "_Calculated_1",
      attributeType: "AnalyticModelAttributeType.DimensionSourceAttribute",
      duplicated: false,
    },
    Calculated_2: {
      text: "Calculated_2",
      attributeMapping: {
        GC_VIEW_PARAMS: {
          key: "Calculated_2",
        },
      },
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      duplicated: false,
    },
  },
  measures: {
    Quantity: {
      measureType: "AnalyticModelMeasureType.FactSourceMeasure",
      sourceKey: "GC_VIEW_PARAMS",
      text: "Quantity",
      key: "Quantity",
      isAuxiliary: false,
    },
    Value: {
      measureType: "AnalyticModelMeasureType.FactSourceMeasure",
      sourceKey: "GC_VIEW_PARAMS",
      text: "Value",
      key: "Value",
      isAuxiliary: false,
    },
    Calculated_Measure: {
      text: "Calculated Measure",
      measureType: "AnalyticModelMeasureType.CalculatedMeasure",
      isAuxiliary: false,
      formulaRaw: "Quantity",
      formula: "[0]",
      elements: {
        "0": {
          operandType: "AnalyticModelCalculatedMeasureOperandType.Element",
          key: "Quantity",
        },
      },
    },
    Calculated_Measure1: {
      text: "Calculated Measure1",
      measureType: "AnalyticModelMeasureType.CalculatedMeasure",
      isAuxiliary: false,
      formulaRaw: "FactAttr",
      formula: "[0]",
      elements: {
        "0": {
          operandType: "AnalyticModelCalculatedMeasureOperandType.Element",
          key: "FactAttr",
        },
      },
    },
    Calculated_Measure2: {
      text: "Calculated Measure2",
      measureType: "AnalyticModelMeasureType.CalculatedMeasure",
      isAuxiliary: false,
      formulaRaw: ":PARAM_2",
      formula: "[0]",
      elements: {
        "0": {
          operandType: "AnalyticModelCalculatedMeasureOperandType.FormulaVariable",
          key: "PARAM_2",
        },
      },
    },
    Restricted_Measure: {
      text: "Restricted Measure",
      measureType: "AnalyticModelMeasureType.RestrictedMeasure",
      isAuxiliary: false,
      key: "Value",
      constantSelectionType: "AnalyticModelConstantSelectionType.None",
      formula: "[0] = [1]",
      formulaRaw: "Unit = :RESTRICTED_MEASURE_VARIABLE_1",
      elements: {
        "0": {
          operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
          key: "Unit",
        },
        "1": {
          operandType: "AnalyticModelRestrictedMeasureOperandType.FilterVariable",
          key: "RESTRICTED_MEASURE_VARIABLE_1",
        },
      },
    },
    Count_Distinct_Measure: {
      countDistinctAttributes: ["ItemStatus"],
      measureType: "AnalyticModelMeasureType.CountDistinct",
      text: "Count Distinct Measure",
      isAuxiliary: false,
    },
    Calculated_Measure3: {
      text: "Calculated Measure3",
      measureType: "AnalyticModelMeasureType.CalculatedMeasure",
      isAuxiliary: false,
      formulaRaw: "5",
      formula: "[0]",
      elements: {
        "0": {
          operandType: "AnalyticModelCalculatedMeasureOperandType.ConstantValue",
          value: 5,
        },
      },
      exceptionAggregationType: "AnalyticModelExceptionAggregationType.MAX",
      exceptionAggregationAttributes: ["ControllingArea"],
    },
    Restricted_Measure1: {
      text: "Restricted Measure1",
      measureType: "AnalyticModelMeasureType.RestrictedMeasure",
      isAuxiliary: false,
      key: "Quantity",
      constantSelectionType: "AnalyticModelConstantSelectionType.Selected",
      formula: "",
      formulaRaw: "",
      elements: {},
      constantSelectionAttributes: ["SendingCostCenter"],
    },
    Non_Cumulative_Measure: {
      text: "Non Cumulative Measure",
      measureType: "AnalyticModelMeasureType.NonCumulativeMeasure",
      isAuxiliary: false,
      key: "Quantity",
      exceptionAggregationNcumType: "AnalyticModelExceptionAggregationNcumType.LAST",
      setUnbookedDeltaToZero: false,
    },
    Currency_Conversion_Measure: {
      key: "Value",
      targetCurrencyType: "AnalyticModelTargetCurrencyType.attribute",
      targetCurrency: {
        key: "Currency",
      },
      conversionType: {
        key: "Opportunity",
      },
      conversionTypeType: "AnalyticModelConversionTypeType.attribute",
      referenceDateType: "AnalyticModelReferenceDateType.attribute",
      referenceDate: {
        key: "Calculated_2",
      },
      sourceCurrencyType: "AnalyticModelSourceCurrencyType.derived",
      measureType: "AnalyticModelMeasureType.CurrencyConversionMeasure",
      text: "Currency Conversion Measure",
      isAuxiliary: false,
      client: "000",
      errorHandling: "AnalyticModelErrorHandling.null",
    },
    Currency_Conversion_Measure1: {
      key: "Value",
      targetCurrencyType: "AnalyticModelTargetCurrencyType.variable",
      targetCurrency: {
        key: "TARGET_CURRENCY_1",
      },
      conversionType: {
        key: "EXCHANGE_RATE_TYPE_1",
      },
      conversionTypeType: "AnalyticModelConversionTypeType.variable",
      referenceDateType: "AnalyticModelReferenceDateType.variable",
      referenceDate: {
        key: "REFERENCE_DATE_1",
      },
      sourceCurrencyType: "AnalyticModelSourceCurrencyType.derived",
      measureType: "AnalyticModelMeasureType.CurrencyConversionMeasure",
      text: "Currency Conversion Measure1",
      isAuxiliary: false,
      client: "000",
      errorHandling: "AnalyticModelErrorHandling.null",
    },
  },
  version: "1.3.8",
  supportedCapabilities: {
    _DWC_AM_EDITABLE_DIMENSION_NAMES: true,
  },
  variables: {
    PARAM_1: {
      parameterType: "AnalyticModelParameterType.Input",
      text: "PARAM_1",
      order: 1,
      variableProcessingType: "AnalyticModelVariableProcessingType.Lookup",
      lookupEntity: "Neha_CurrencyOfCountry",
      resultElement: "Country",
      parameterBinding: {
        COUNTRY: {
          mappingType: "AnalyticModelSourceParameterMappingType.MapToSourceParameter",
          variableName: "COUNTRY",
        },
      },
      dataType: {
        type: "cds.String",
        length: 10,
      },
    },
    RESTRICTED_MEASURE_VARIABLE_1: {
      text: "Restricted Measure Variable 1",
      parameterType: "AnalyticModelParameterType.Filter",
      referenceAttribute: "Unit",
      selectionType: "AnalyticModelVariableSelectionType.SINGLE",
      multipleSelections: false,
      mandatory: false,
      order: 2,
    },
    Currency: {
      referenceAttribute: "Currency",
      parameterType: "AnalyticModelParameterType.StoryFilter",
      selectionType: "AnalyticModelVariableSelectionType.SINGLE",
      multipleSelections: false,
      mandatory: false,
      order: 3,
    },
    COUNTRY: {
      text: "Country",
      parameterType: "AnalyticModelParameterType.Input",
      defaultValue: "IN",
      variableProcessingType: "AnalyticModelVariableProcessingType.ManualInput",
      order: 4,
    },
    PARAM_2: {
      text: "Standard Variable 1",
      parameterType: "AnalyticModelParameterType.Input",
      order: 5,
      dataType: {
        type: "cds.Double",
      },
    },
    TARGET_CURRENCY_1: {
      parameterType: "AnalyticModelParameterType.Input",
      text: "Target Currency 1",
      variableProcessingType: "AnalyticModelVariableProcessingType.ManualInput",
      order: 6,
    },
    REFERENCE_DATE_1: {
      parameterType: "AnalyticModelParameterType.Input",
      text: "Reference Date 1",
      variableProcessingType: "AnalyticModelVariableProcessingType.ManualInput",
      order: 7,
    },
    EXCHANGE_RATE_TYPE_1: {
      parameterType: "AnalyticModelParameterType.Input",
      text: "Exchange Rate Type 1",
      variableProcessingType: "AnalyticModelVariableProcessingType.ManualInput",
      order: 8,
    },
  },
  preventAnalyticsCloudDataExport: false,
  filter: {
    text: "Filter",
    formulaRaw: "ItemID = 5",
    formula: "[0] = [1]",
    elements: {
      "0": {
        operandType: "AnalyticModelFilterOperandType.Attribute",
        key: "ItemID",
      },
      "1": {
        operandType: "AnalyticModelFilterOperandType.ConstantValue",
        value: 5,
      },
    },
  },
  dataAccessControls: {
    DAC_Values_Multiple: {
      dacMapping: {
        country: "Opportunity",
        lob: "Product",
      },
    },
  },
  nonCumulativeSettings: {
    timeDimensionKey: "_Calculated_1",
    recordTypeAttributeKey: "ReceivingCostCenter",
  },
  crossCalculations: {
    RESTRICTED_CC: {
      text: "Restricted Cross Calculation",
      crossCalculationType: "AnalyticModelCrossCalculationType.RestrictedCrossCalculation",
      formulaRaw: "Opportunity = :RMV_CROSS_CALCULATION",
      formula: "[0] = [1]",
      elements: {
        "0": {
          operandType: "AnalyticModelRestrictedCrossCalculationOperandType.Attribute",
          key: "Opportunity",
        },
        "1": {
          operandType: "AnalyticModelRestrictedCrossCalculationOperandType.FilterVariable",
          key: "RMV_CROSS_CALCULATION",
        },
      },
    },
    CALCULATED_CC: {
      text: "Calculated Cross Calculation",
      crossCalculationType: "AnalyticModelCrossCalculationType.CalculatedCrossCalculation",
      formulaRaw: "Status_Opportunit = :SV_CROSS_CALCULATION",
      formula: "[0] = [1]",
      elements: {
        "0": {
          operandType: "AnalyticModelCalculatedCrossCalculationOperandType.Element",
          key: "Status_Opportunit",
        },
        "1": {
          operandType: "AnalyticModelCalculatedCrossCalculationOperandType.FormulaVariable",
          key: "SV_CROSS_CALCULATION",
        },
      },
    },
  },
};

export const dependenciesUiModel = {
  isNew: false,
  objectStatus: "0",
  deploymentDate: null,
  deploymentExecutionStatus: "not_deployed",
  revertLastVersion: false,
  isDirty: false,
  modelDependentObjects: {},
  modelDependentObjectsSearch: "",
  skipSymbolCallback: false,
  isEditMode: true,
  newTechnicalName: false,
  diagramSelectedSource: {},
  attributes: [
    {
      key: "ItemID",
      text: "ItemID",
      hasTextAssociation: false,
      hasTextElement: false,
      isNotSupportedPrimitiveTypes: false,
      isHidden: false,
      isKey: true,
      type: "cds.Integer64",
      semanticType: "noValue",
      primitiveType: "cds.Integer64",
      keyOnModel: "ItemID",
      selected: true,
      attributeInModel: {
        attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
        attributeMapping: {
          GC_VIEW_PARAMS: {
            key: "ItemID",
          },
        },
        text: "ItemID",
        duplicated: false,
      },
      enabled: true,
      foreignKeyAssociationNotAdded: true,
      displayText: "ItemID",
      hasTimeDependency: false,
      hasHierarchies: false,
      associationHasTextElement: false,
      associationHasTextAssociation: false,
    },
    {
      key: "Opportunity",
      text: "Opportunity",
      hasTextAssociation: false,
      hasTextElement: false,
      foreignKeyAssociation: "_MCT_Oppor",
      isNotSupportedPrimitiveTypes: false,
      isHidden: false,
      isKey: false,
      type: "cds.Integer64",
      semanticType: "noValue",
      primitiveType: "cds.Integer64",
      keyOnModel: "Opportunity",
      selected: true,
      attributeInModel: {
        attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
        attributeMapping: {
          GC_VIEW_PARAMS: {
            key: "Opportunity",
          },
        },
        text: "Opportunity",
        duplicated: false,
      },
      enabled: true,
      foreignKeyAssociationNotAdded: true,
      displayText: "Opportunity",
      hasTimeDependency: false,
      hasHierarchies: false,
      associationHasTextElement: false,
      associationHasTextAssociation: false,
    },
    {
      key: "Product",
      text: "Product",
      hasTextAssociation: false,
      hasTextElement: false,
      isNotSupportedPrimitiveTypes: false,
      isHidden: false,
      isKey: false,
      type: "cds.String",
      semanticType: "noValue",
      primitiveType: "cds.String",
      length: 5000,
      keyOnModel: "Product",
      selected: true,
      attributeInModel: {
        attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
        attributeMapping: {
          GC_VIEW_PARAMS: {
            key: "Product",
          },
        },
        text: "Product",
        duplicated: false,
      },
      enabled: true,
      foreignKeyAssociationNotAdded: true,
      displayText: "Product",
      hasTimeDependency: false,
      hasHierarchies: false,
      associationHasTextElement: false,
      associationHasTextAssociation: false,
    },
    {
      key: "Unit",
      text: "Unit",
      hasTextAssociation: false,
      hasTextElement: false,
      isNotSupportedPrimitiveTypes: false,
      isHidden: false,
      isKey: false,
      type: "cds.String",
      semanticType: "@Semantics.unitOfMeasure",
      primitiveType: "cds.String",
      length: 5000,
      keyOnModel: "Unit",
      selected: true,
      attributeInModel: {
        attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
        attributeMapping: {
          GC_VIEW_PARAMS: {
            key: "Unit",
          },
        },
        text: "Unit",
        duplicated: false,
      },
      enabled: true,
      foreignKeyAssociationNotAdded: true,
      displayText: "Unit",
      hasTimeDependency: false,
      hasHierarchies: false,
      associationHasTextElement: false,
      associationHasTextAssociation: false,
    },
    {
      key: "Currency",
      text: "Currency",
      hasTextAssociation: false,
      hasTextElement: false,
      isNotSupportedPrimitiveTypes: false,
      isHidden: false,
      isKey: false,
      type: "cds.String",
      semanticType: "@Semantics.currencyCode",
      primitiveType: "cds.String",
      length: 5000,
      keyOnModel: "Currency",
      selected: true,
      attributeInModel: {
        attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
        attributeMapping: {
          GC_VIEW_PARAMS: {
            key: "Currency",
          },
        },
        text: "Currency",
        duplicated: false,
      },
      enabled: true,
      foreignKeyAssociationNotAdded: true,
      displayText: "Currency",
      hasTimeDependency: false,
      hasHierarchies: false,
      associationHasTextElement: false,
      associationHasTextAssociation: false,
    },
    {
      key: "FactAttr",
      text: "FactAttr",
      hasTextAssociation: false,
      hasTextElement: true,
      textElements: [
        {
          key: "FactAttrLabel",
          text: "FactAttrLabel",
        },
      ],
      isNotSupportedPrimitiveTypes: false,
      isHidden: false,
      isKey: false,
      type: "cds.String",
      semanticType: "noValue",
      primitiveType: "cds.String",
      length: 5000,
      keyOnModel: "FactAttr",
      selected: true,
      attributeInModel: {
        attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
        attributeMapping: {
          GC_VIEW_PARAMS: {
            key: "FactAttr",
          },
        },
        text: "FactAttr",
        duplicated: false,
      },
      enabled: true,
      foreignKeyAssociationNotAdded: true,
      displayText: "FactAttr",
      hasTimeDependency: false,
      hasHierarchies: false,
      associationHasTextElement: false,
      associationHasTextAssociation: false,
    },
    {
      key: "ItemStatus",
      text: "ItemStatus ID",
      hasTextAssociation: false,
      hasTextElement: false,
      isNotSupportedPrimitiveTypes: false,
      isHidden: false,
      isKey: false,
      type: "cds.Integer64",
      semanticType: "noValue",
      primitiveType: "cds.Integer64",
      keyOnModel: "ItemStatus",
      selected: true,
      attributeInModel: {
        attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
        attributeMapping: {
          GC_VIEW_PARAMS: {
            key: "ItemStatus",
          },
        },
        text: "ItemStatus ID",
        duplicated: false,
      },
      enabled: true,
      foreignKeyAssociationNotAdded: true,
      displayText: "ItemStatus ID",
      hasTimeDependency: false,
      hasHierarchies: false,
      associationHasTextElement: false,
      associationHasTextAssociation: false,
    },
    {
      key: "ControllingArea",
      text: "Controlling Area",
      hasTextAssociation: false,
      hasTextElement: false,
      isNotSupportedPrimitiveTypes: false,
      isHidden: false,
      isKey: false,
      type: "cds.Integer64",
      semanticType: "noValue",
      primitiveType: "cds.Integer64",
      keyOnModel: "ControllingArea",
      selected: true,
      attributeInModel: {
        attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
        attributeMapping: {
          GC_VIEW_PARAMS: {
            key: "ControllingArea",
          },
        },
        text: "Controlling Area",
        duplicated: false,
      },
      enabled: true,
      foreignKeyAssociationNotAdded: true,
      displayText: "Controlling Area",
      hasTimeDependency: false,
      hasHierarchies: false,
      associationHasTextElement: false,
      associationHasTextAssociation: false,
    },
    {
      key: "SendingCostCenter",
      text: "Sending Cost Center",
      hasTextAssociation: false,
      hasTextElement: false,
      isNotSupportedPrimitiveTypes: false,
      isHidden: false,
      isKey: false,
      type: "cds.Integer64",
      semanticType: "noValue",
      primitiveType: "cds.Integer64",
      keyOnModel: "SendingCostCenter",
      selected: true,
      attributeInModel: {
        attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
        attributeMapping: {
          GC_VIEW_PARAMS: {
            key: "SendingCostCenter",
          },
        },
        text: "Sending Cost Center",
        duplicated: false,
      },
      enabled: true,
      foreignKeyAssociationNotAdded: true,
      displayText: "Sending Cost Center",
      hasTimeDependency: false,
      hasHierarchies: false,
      associationHasTextElement: false,
      associationHasTextAssociation: false,
    },
    {
      key: "ReceivingCostCenter",
      text: "Receiving Cost Center",
      hasTextAssociation: false,
      hasTextElement: false,
      isNotSupportedPrimitiveTypes: false,
      isHidden: false,
      isKey: false,
      type: "cds.Integer64",
      semanticType: "noValue",
      primitiveType: "cds.Integer64",
      keyOnModel: "ReceivingCostCenter",
      selected: true,
      attributeInModel: {
        attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
        attributeMapping: {
          GC_VIEW_PARAMS: {
            key: "ReceivingCostCenter",
          },
        },
        text: "Receiving Cost Center",
        duplicated: false,
        isAuxiliary: true,
      },
      enabled: true,
      foreignKeyAssociationNotAdded: true,
      displayText: "Receiving Cost Center",
      hasTimeDependency: false,
      hasHierarchies: false,
      associationHasTextElement: false,
      associationHasTextAssociation: false,
    },
    {
      key: "Calculated_1",
      text: "Calculated_1",
      hasTextAssociation: false,
      hasTextElement: false,
      foreignKeyAssociation: "_VIEW_DIME",
      isNotSupportedPrimitiveTypes: false,
      isHidden: false,
      isKey: false,
      type: "cds.Date",
      semanticType: "noValue",
      primitiveType: "cds.Date",
      keyOnModel: "Calculated_1",
      selected: true,
      attributeInModel: {
        text: "Calculated_1",
        attributeMapping: {
          GC_VIEW_PARAMS: {
            key: "Calculated_1",
          },
        },
        attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
        duplicated: false,
        usedForDimensionSourceKey: "_Calculated_1",
      },
      enabled: true,
      foreignKeyAssociationNotAdded: false,
      displayText: "Calculated_1",
      hasTimeDependency: false,
      hasHierarchies: true,
      associationHasTextElement: true,
      associationHasTextAssociation: false,
    },
    {
      key: "Calculated_2",
      text: "Calculated_2",
      hasTextAssociation: false,
      hasTextElement: false,
      isNotSupportedPrimitiveTypes: false,
      isHidden: false,
      isKey: false,
      type: "cds.Date",
      semanticType: "noValue",
      primitiveType: "cds.Date",
      keyOnModel: "Calculated_2",
      selected: true,
      attributeInModel: {
        text: "Calculated_2",
        attributeMapping: {
          GC_VIEW_PARAMS: {
            key: "Calculated_2",
          },
        },
        attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
        duplicated: false,
      },
      enabled: true,
      foreignKeyAssociationNotAdded: true,
      displayText: "Calculated_2",
      hasTimeDependency: false,
      hasHierarchies: false,
      associationHasTextElement: false,
      associationHasTextAssociation: false,
    },
  ],
  measures: [],
  associations: [
    {
      key: "_MCT_Oppor",
      text: "GC_VIEW_PARAMS to MCT Opportunity Header",
      targetEntity: {
        key: "MCT_Opportunities",
        text: "MCT Opportunity Header",
        displayText: "MCT Opportunity Header",
      },
      foreignKeys: ["Opportunity"],
      targetKeys: ["OpportunityID"],
      hasHierarchies: false,
      hasTimeDependency: false,
      timeDependencies: [],
      dimensionSourceAlias: "Opportunity",
      attributeForAssociation: "Opportunity",
      hasTextAssociation: false,
      hasTextElement: false,
      businessType: "DWC_DIMENSION",
      technicalType: "DWC_LOCAL_TABLE",
      displayText: "Opportunity",
      selected: false,
      isNonForeignKeyAssociation: false,
    },
    {
      key: "_VIEW_DIME",
      text: "GC_VIEW_PARAMS to Time Dimension - Day",
      targetEntity: {
        key: "SAP.TIME.VIEW_DIMENSION_DAY",
        text: "Time Dimension - Day",
        displayText: "Time Dimension - Day",
      },
      foreignKeys: ["Calculated_1"],
      targetKeys: ["DATE_SQL"],
      hasHierarchies: true,
      hasTimeDependency: false,
      timeDependencies: [],
      dimensionSourceAlias: "Calculated_1",
      attributeForAssociation: "Calculated_1",
      hasTextAssociation: false,
      hasTextElement: true,
      businessType: "DWC_DIMENSION",
      technicalType: "DWC_VIEW",
      displayText: "Calculated_1",
      selected: true,
      isNonForeignKeyAssociation: false,
    },
  ],
  parameters: [],
  selectedMeasures: [],
  selectedParameters: [],
  selectedDimensions: [],
  selectedDataAccessControls: [],
  activeParameters: [],
  inactiveParameters: [],
  attributeGrouping: [],
  modelMeasures: [
    {
      technicalName: "Quantity",
      text: "Quantity",
      isAuxiliary: false,
      measureType: "AnalyticModelMeasureType.FactSourceMeasure",
      key: "Quantity",
      typeIcon: "sap-icon://sac/measure",
      aggregationText: "SUM",
      semanticType: "@Semantics.quantity.unitOfMeasure",
      unitColumn: "Unit",
      selected: false,
      _validationMessageType: "Success",
    },
    {
      technicalName: "Value",
      text: "Value",
      isAuxiliary: false,
      measureType: "AnalyticModelMeasureType.FactSourceMeasure",
      key: "Value",
      typeIcon: "sap-icon://sac/measure",
      aggregationText: "SUM",
      semanticType: "@Semantics.amount.currencyCode",
      unitColumn: "Currency",
      selected: false,
      _validationMessageType: "Success",
    },
    {
      technicalName: "Calculated_Measure",
      text: "Calculated Measure",
      isAuxiliary: false,
      measureType: "AnalyticModelMeasureType.CalculatedMeasure",
      key: "",
      typeIcon: "sap-icon://sac/formula",
      aggregationText: "",
      semanticType: "",
      unitColumn: "",
      selected: false,
      _validationMessageType: "Success",
    },
    {
      technicalName: "Calculated_Measure1",
      text: "Calculated Measure1",
      isAuxiliary: false,
      measureType: "AnalyticModelMeasureType.CalculatedMeasure",
      key: "",
      typeIcon: "sap-icon://sac/formula",
      aggregationText: "",
      semanticType: "",
      unitColumn: "",
      selected: false,
      _validationMessageType: "Success",
    },
    {
      technicalName: "Calculated_Measure2",
      text: "Calculated Measure2",
      isAuxiliary: false,
      measureType: "AnalyticModelMeasureType.CalculatedMeasure",
      key: "",
      typeIcon: "sap-icon://sac/formula",
      aggregationText: "",
      semanticType: "",
      unitColumn: "",
      selected: false,
      _validationMessageType: "Success",
    },
    {
      technicalName: "Restricted_Measure",
      text: "Restricted Measure",
      isAuxiliary: false,
      measureType: "AnalyticModelMeasureType.RestrictedMeasure",
      key: "Value",
      typeIcon: "sap-icon://sac/measure-filter",
      aggregationText: "SUM",
      semanticType: "",
      unitColumn: "",
      selected: false,
      _validationMessageType: "Success",
    },
    {
      technicalName: "Count_Distinct_Measure",
      text: "Count Distinct Measure",
      isAuxiliary: false,
      measureType: "AnalyticModelMeasureType.CountDistinct",
      key: "",
      typeIcon: "sap-icon://sac/formula",
      aggregationText: "",
      semanticType: "",
      unitColumn: "",
      selected: false,
      _validationMessageType: "Success",
    },
    {
      technicalName: "Calculated_Measure3",
      text: "Calculated Measure3",
      isAuxiliary: false,
      measureType: "AnalyticModelMeasureType.CalculatedMeasure",
      key: "",
      typeIcon: "sap-icon://sac/formula",
      aggregationText: "",
      semanticType: "",
      unitColumn: "",
      selected: false,
      _validationMessageType: "Success",
    },
    {
      technicalName: "Restricted_Measure1",
      text: "Restricted Measure1",
      isAuxiliary: false,
      measureType: "AnalyticModelMeasureType.RestrictedMeasure",
      key: "Quantity",
      typeIcon: "sap-icon://sac/measure-filter",
      aggregationText: "SUM",
      semanticType: "",
      unitColumn: "",
      selected: false,
      _validationMessageType: "Success",
    },
    {
      technicalName: "Non_Cumulative_Measure",
      text: "Non Cumulative Measure",
      isAuxiliary: false,
      measureType: "AnalyticModelMeasureType.NonCumulativeMeasure",
      key: "Quantity",
      typeIcon: "sap-icon://sac/formula",
      aggregationText: "",
      semanticType: "",
      unitColumn: "",
      selected: false,
      _validationMessageType: "Success",
    },
    {
      technicalName: "Currency_Conversion_Measure",
      text: "Currency Conversion Measure",
      isAuxiliary: false,
      measureType: "AnalyticModelMeasureType.CurrencyConversionMeasure",
      key: "Value",
      typeIcon: "sap-icon://lead",
      aggregationText: "",
      semanticType: "",
      unitColumn: "",
      selected: false,
      _validationMessageType: "Success",
    },
    {
      technicalName: "Currency_Conversion_Measure1",
      text: "Currency Conversion Measure1",
      isAuxiliary: false,
      measureType: "AnalyticModelMeasureType.CurrencyConversionMeasure",
      key: "Value",
      typeIcon: "sap-icon://lead",
      aggregationText: "",
      semanticType: "",
      unitColumn: "",
      selected: false,
      _validationMessageType: "Success",
    },
  ],
  variableModel: [
    {
      parameterType: "AnalyticModelParameterType.Input",
      text: "PARAM_1",
      order: 1,
      variableProcessingType: "AnalyticModelVariableProcessingType.Lookup",
      lookupEntity: "Neha_CurrencyOfCountry",
      resultElement: "Country",
      parameterBinding: {
        COUNTRY: {
          mappingType: "AnalyticModelSourceParameterMappingType.MapToSourceParameter",
          variableName: "COUNTRY",
        },
      },
      dataType: {
        type: "cds.String",
        length: 10,
      },
      key: "PARAM_1",
      selected: false,
      _validationMessageType: "Warning",
    },
    {
      text: "Restricted Measure Variable 1",
      parameterType: "AnalyticModelParameterType.Filter",
      referenceAttribute: "Unit",
      selectionType: "AnalyticModelVariableSelectionType.SINGLE",
      multipleSelections: false,
      mandatory: false,
      order: 2,
      key: "RESTRICTED_MEASURE_VARIABLE_1",
      selected: false,
      _validationMessageType: "Success",
    },
    {
      referenceAttribute: "Currency",
      parameterType: "AnalyticModelParameterType.StoryFilter",
      selectionType: "AnalyticModelVariableSelectionType.SINGLE",
      multipleSelections: false,
      mandatory: false,
      order: 3,
      key: "Currency",
      selected: false,
      _validationMessageType: "Success",
      text: "Currency",
    },
    {
      text: "Country",
      parameterType: "AnalyticModelParameterType.Input",
      defaultValue: "IN",
      variableProcessingType: "AnalyticModelVariableProcessingType.ManualInput",
      order: 4,
      key: "COUNTRY",
      selected: false,
      _validationMessageType: "Success",
    },
    {
      text: "Standard Variable 1",
      parameterType: "AnalyticModelParameterType.Input",
      order: 5,
      dataType: {
        type: "cds.Double",
      },
      key: "PARAM_2",
      selected: false,
      _validationMessageType: "Success",
    },
    {
      parameterType: "AnalyticModelParameterType.Input",
      text: "Target Currency 1",
      variableProcessingType: "AnalyticModelVariableProcessingType.ManualInput",
      order: 6,
      key: "TARGET_CURRENCY_1",
      selected: false,
      _validationMessageType: "Success",
    },
    {
      parameterType: "AnalyticModelParameterType.Input",
      text: "Reference Date 1",
      variableProcessingType: "AnalyticModelVariableProcessingType.ManualInput",
      order: 7,
      key: "REFERENCE_DATE_1",
      selected: false,
      _validationMessageType: "Success",
    },
    {
      parameterType: "AnalyticModelParameterType.Input",
      text: "Exchange Rate Type 1",
      variableProcessingType: "AnalyticModelVariableProcessingType.ManualInput",
      order: 8,
      key: "EXCHANGE_RATE_TYPE_1",
      selected: false,
      _validationMessageType: "Success",
    },
  ],
  dataAccessControls: [
    {
      key: "DAC_Values_Multiple",
      text: "DAC Values Multiple",
      selected: false,
      _validationMessageType: "Success",
      dacSpaceId: "BBDEV",
      dacObjectName: "DAC_Values_Multiple",
      to: {
        technicalName: "DAC_Values_Multiple",
        businessName: "DAC Values Multiple",
      },
      leftObject: {
        technicalName: "GC_DEPENDENCIES",
        businessName: "GC_DEPENDENCIES",
      },
      leftList: [
        {
          technicalName: "ItemID",
          businessName: "ItemID",
          displayName: "ItemID (GC_VIEW_PARAMS)",
          primitiveDataType: "cds.Integer64",
          displayDataType: "Integer64",
          semanticType: "noValue",
          hidden: false,
        },
        {
          technicalName: "Opportunity",
          businessName: "Opportunity",
          displayName: "Opportunity (GC_VIEW_PARAMS)",
          primitiveDataType: "cds.Integer64",
          displayDataType: "Integer64",
          semanticType: "noValue",
          hidden: false,
        },
        {
          technicalName: "Product",
          businessName: "Product",
          displayName: "Product (GC_VIEW_PARAMS)",
          primitiveDataType: "cds.String",
          displayDataType: "String(5000)",
          semanticType: "noValue",
          hidden: false,
        },
        {
          technicalName: "Unit",
          businessName: "Unit",
          displayName: "Unit (GC_VIEW_PARAMS)",
          primitiveDataType: "cds.String",
          displayDataType: "String(5000)",
          semanticType: "@Semantics.unitOfMeasure",
          hidden: false,
        },
        {
          technicalName: "Currency",
          businessName: "Currency",
          displayName: "Currency (GC_VIEW_PARAMS)",
          primitiveDataType: "cds.String",
          displayDataType: "String(5000)",
          semanticType: "@Semantics.currencyCode",
          hidden: false,
        },
        {
          technicalName: "FactAttr",
          businessName: "FactAttr",
          displayName: "FactAttr (GC_VIEW_PARAMS)",
          primitiveDataType: "cds.String",
          displayDataType: "String(5000)",
          semanticType: "noValue",
          hidden: false,
        },
        {
          technicalName: "ItemStatus",
          businessName: "ItemStatus ID",
          displayName: "ItemStatus ID (GC_VIEW_PARAMS)",
          primitiveDataType: "cds.Integer64",
          displayDataType: "Integer64",
          semanticType: "noValue",
          hidden: false,
        },
        {
          technicalName: "ControllingArea",
          businessName: "Controlling Area",
          displayName: "Controlling Area (GC_VIEW_PARAMS)",
          primitiveDataType: "cds.Integer64",
          displayDataType: "Integer64",
          semanticType: "noValue",
          hidden: false,
        },
        {
          technicalName: "SendingCostCenter",
          businessName: "Sending Cost Center",
          displayName: "Sending Cost Center (GC_VIEW_PARAMS)",
          primitiveDataType: "cds.Integer64",
          displayDataType: "Integer64",
          semanticType: "noValue",
          hidden: false,
        },
        {
          technicalName: "ReceivingCostCenter",
          businessName: "Receiving Cost Center",
          displayName: "Receiving Cost Center (GC_VIEW_PARAMS)",
          primitiveDataType: "cds.Integer64",
          displayDataType: "Integer64",
          semanticType: "noValue",
          hidden: true,
        },
        {
          technicalName: "Calculated_1",
          businessName: "Calculated_1",
          displayName: "Calculated_1 (GC_VIEW_PARAMS)",
          primitiveDataType: "cds.Date",
          displayDataType: "Date",
          semanticType: "noValue",
          hidden: false,
        },
        {
          technicalName: "YEAR_Calculated_1",
          businessName: "Year",
          displayName: "Year (Calculated_1)",
          primitiveDataType: "cds.String",
          displayDataType: "String(4)",
          semanticType: "@Semantics.calendar.year",
          hidden: false,
        },
        {
          technicalName: "CALQUARTER_Calculated_1",
          businessName: "Calendar Quarter",
          displayName: "Calendar Quarter (Calculated_1)",
          primitiveDataType: "cds.String",
          displayDataType: "String(5)",
          semanticType: "@Semantics.calendar.yearQuarter",
          hidden: false,
        },
        {
          technicalName: "CALMONTH_Calculated_1",
          businessName: "Calendar Month",
          displayName: "Calendar Month (Calculated_1)",
          primitiveDataType: "cds.String",
          displayDataType: "String(6)",
          semanticType: "@Semantics.calendar.yearMonth",
          hidden: false,
        },
        {
          technicalName: "CALWEEK_Calculated_1",
          businessName: "Calendar Week",
          displayName: "Calendar Week (Calculated_1)",
          primitiveDataType: "cds.String",
          displayDataType: "String(6)",
          semanticType: "@Semantics.calendar.yearWeek",
          hidden: false,
        },
        {
          technicalName: "Calculated_2",
          businessName: "Calculated_2",
          displayName: "Calculated_2 (GC_VIEW_PARAMS)",
          primitiveDataType: "cds.Date",
          displayDataType: "Date",
          semanticType: "noValue",
          hidden: false,
        },
      ],
      rightObject: {
        kind: "DataAccessControl",
        technicalName: "DAC_Values_Multiple",
        businessName: "DAC Values Multiple",
        spaceName: "BBDEV",
      },
      rightList: [
        {
          technicalName: "country",
          businessName: "country",
          displayName: "country",
          primitiveDataType: "cds.String",
          hidden: false,
          semanticType: "noValue",
          displayDataType: "String(100)",
        },
        {
          technicalName: "lob",
          businessName: "lob",
          displayName: "lob",
          primitiveDataType: "cds.String",
          hidden: false,
          semanticType: "noValue",
          displayDataType: "String(100)",
        },
      ],
      mappings: [
        {
          source: {
            technicalName: "country",
            businessName: "country",
            displayName: "country",
            primitiveDataType: "cds.String",
            hidden: false,
            semanticType: "noValue",
            displayDataType: "String(100)",
          },
          target: {
            technicalName: "Opportunity",
            businessName: "Opportunity",
            displayName: "Opportunity (GC_VIEW_PARAMS)",
            primitiveDataType: "cds.Integer64",
            displayDataType: "Integer64",
            semanticType: "noValue",
            hidden: false,
          },
        },
        {
          source: {
            technicalName: "lob",
            businessName: "lob",
            displayName: "lob",
            primitiveDataType: "cds.String",
            hidden: false,
            semanticType: "noValue",
            displayDataType: "String(100)",
          },
          target: {
            technicalName: "Product",
            businessName: "Product",
            displayName: "Product (GC_VIEW_PARAMS)",
            primitiveDataType: "cds.String",
            displayDataType: "String(5000)",
            semanticType: "noValue",
            hidden: false,
          },
        },
      ],
    },
  ],
  globalFilter: [
    {
      text: "Filter",
      formula: "[0] = [1]",
      formulaRaw: "ItemID = 5",
      _validationMessageType: "Success",
    },
  ],
  allSourceDataEntityDetails: {
    "SAP.TIME.VIEW_DIMENSION_DAY": {
      businessName: "Time Dimension - Day",
      technicalName: "SAP.TIME.VIEW_DIMENSION_DAY",
      csn: {
        kind: "entity",
        elements: {
          DATE_SQL: {
            key: true,
            type: "cds.Date",
            "@Analytics.dimension": true,
            "@EndUserText.label": "Date",
            "@Semantics.date": true,
            "@ObjectModel.text.element": [
              {
                "=": "DAY_INT",
              },
            ],
          },
          DATE_SAP: {
            type: "cds.String",
            "@Analytics.dimension": true,
            "@Semantics.date": true,
            "@EndUserText.label": "Date (String)",
            length: 8,
            "@ObjectModel.text.element": [
              {
                "=": "DAY_INT",
              },
            ],
          },
          DAY_OF_WEEK: {
            type: "cds.String",
            "@Analytics.dimension": true,
            "@Semantics.calendar.dayOfYear": true,
            "@EndUserText.label": "Day of Week",
            length: 2,
            "@ObjectModel.text.association": {
              "=": "_DAY_OF_WEEK",
            },
          },
          CALWEEK: {
            type: "cds.String",
            "@Analytics.dimension": true,
            "@Semantics.calendar.yearWeek": true,
            "@EndUserText.label": "Calendar Week",
            length: 6,
          },
          CALMONTH: {
            type: "cds.String",
            "@Analytics.dimension": true,
            "@Semantics.calendar.yearMonth": true,
            "@EndUserText.label": "Calendar Month",
            "@ObjectModel.text.association": {
              "=": "_MONTH",
            },
            length: 6,
          },
          MONTH: {
            type: "cds.String",
            "@Analytics.dimension": true,
            "@Semantics.calendar.month": true,
            "@EndUserText.label": "Month",
            length: 2,
          },
          CALQUARTER: {
            type: "cds.String",
            "@Analytics.dimension": true,
            "@Semantics.calendar.yearQuarter": true,
            "@EndUserText.label": "Calendar Quarter",
            length: 5,
            "@ObjectModel.text.association": {
              "=": "_QUARTER",
            },
          },
          QUARTER: {
            type: "cds.String",
            "@Analytics.dimension": true,
            "@EndUserText.label": "Quarter",
            length: 2,
          },
          YEAR: {
            type: "cds.String",
            "@Analytics.dimension": true,
            "@Semantics.calendar.year": true,
            "@EndUserText.label": "Year",
            length: 4,
          },
          _DAY_OF_WEEK: {
            on: [
              {
                ref: ["DAY_OF_WEEK"],
              },
              "=",
              {
                ref: ["_DAY_OF_WEEK", "ID"],
              },
            ],
            target: "SAP.TIME.M_TIME_DIMENSION_TDAY",
            type: "cds.Association",
          },
          DAY_INT: {
            "@EndUserText.label": "Day (Number)",
            type: "cds.Integer",
            "@Semantics.text": true,
          },
          _MONTH: {
            on: [
              {
                ref: ["MONTH"],
              },
              "=",
              {
                ref: ["_MONTH", "ID"],
              },
            ],
            target: "SAP.TIME.M_TIME_DIMENSION_TMONTH",
            type: "cds.Association",
          },
          _QUARTER: {
            on: [
              {
                ref: ["QUARTER"],
              },
              "=",
              {
                ref: ["_QUARTER", "ID"],
              },
            ],
            target: "SAP.TIME.M_TIME_DIMENSION_TQUARTER",
            type: "cds.Association",
          },
        },
        query: {
          SELECT: {
            from: {
              ref: ["SAP.TIME.M_TIME_DIMENSION"],
              as: "M_TIME_DIMENSION",
            },
            distinct: true,
            columns: [
              {
                ref: ["DATE_SQL"],
              },
              {
                ref: ["DATE_SAP"],
              },
              {
                ref: ["DAY_OF_WEEK"],
              },
              {
                ref: ["CALWEEK"],
              },
              {
                ref: ["CALMONTH"],
              },
              {
                ref: ["MONTH"],
              },
              {
                ref: ["CALQUARTER"],
              },
              {
                ref: ["YEAR"],
              },
              {
                ref: ["QUARTER"],
              },
              {
                ref: ["_DAY_OF_WEEK"],
              },
              {
                ref: ["DAY_INT"],
              },
              {
                ref: ["_MONTH"],
              },
              {
                ref: ["_QUARTER"],
              },
            ],
            mixin: {
              _DAY_OF_WEEK: {
                on: [
                  {
                    ref: ["$projection", "DAY_OF_WEEK"],
                  },
                  "=",
                  {
                    ref: ["_DAY_OF_WEEK", "ID"],
                  },
                ],
                target: "SAP.TIME.M_TIME_DIMENSION_TDAY",
                type: "cds.Association",
              },
              _MONTH: {
                on: [
                  {
                    ref: ["$projection", "MONTH"],
                  },
                  "=",
                  {
                    ref: ["_MONTH", "ID"],
                  },
                ],
                target: "SAP.TIME.M_TIME_DIMENSION_TMONTH",
                type: "cds.Association",
              },
              _QUARTER: {
                on: [
                  {
                    ref: ["$projection", "QUARTER"],
                  },
                  "=",
                  {
                    ref: ["_QUARTER", "ID"],
                  },
                ],
                target: "SAP.TIME.M_TIME_DIMENSION_TQUARTER",
                type: "cds.Association",
              },
            },
          },
        },
        "@EndUserText.label": "Time Dimension - Day",
        "@Hierarchy.leveled": [
          {
            name: "YQMD",
            label: "Year, Quarter, Month, Day",
            levels: [
              {
                element: {
                  "=": "YEAR",
                },
              },
              {
                element: {
                  "=": "CALQUARTER",
                },
              },
              {
                element: {
                  "=": "CALMONTH",
                },
              },
              {
                element: {
                  "=": "DATE_SQL",
                },
              },
            ],
          },
          {
            name: "YMD",
            label: "Year, Month, Day",
            levels: [
              {
                element: {
                  "=": "YEAR",
                },
              },
              {
                element: {
                  "=": "CALMONTH",
                },
              },
              {
                element: {
                  "=": "DATE_SQL",
                },
              },
            ],
          },
          {
            name: "YWD",
            label: "Year, Week, Day",
            levels: [
              {
                element: {
                  "=": "YEAR",
                },
              },
              {
                element: {
                  "=": "CALWEEK",
                },
              },
              {
                element: {
                  "=": "DATE_SQL",
                },
              },
            ],
          },
        ],
        "@ObjectModel.modelingPattern": {
          "#": "ANALYTICAL_DIMENSION",
        },
        "@ObjectModel.supportedCapabilities": [
          {
            "#": "ANALYTICAL_PROVIDER",
          },
          {
            "#": "ANALYTICAL_DIMENSION",
          },
        ],
        "@Analytics.dimensionType": "#TIME",
        "@Internal.Entity.Hidden": true,
        _meta: {
          dependencies: {
            folderAssignment: "Folder_KLRQKSZN",
          },
        },
      },
      technicalType: "DWC_VIEW",
      associations: [],
      measures: [],
      attributes: [
        {
          key: "DATE_SQL",
          text: "Date",
          hasTextAssociation: false,
          hasTextElement: true,
          textElements: [
            {
              key: "DAY_INT",
              text: "Day (Number)",
            },
          ],
          isNotSupportedPrimitiveTypes: false,
          isHidden: false,
          isKey: true,
          type: "cds.Date",
          semanticType: "@Semantics.date",
          primitiveType: "cds.Date",
        },
        {
          key: "DATE_SAP",
          text: "Date (String)",
          hasTextAssociation: false,
          hasTextElement: true,
          textElements: [
            {
              key: "DAY_INT",
              text: "Day (Number)",
            },
          ],
          isNotSupportedPrimitiveTypes: false,
          isHidden: false,
          isKey: false,
          type: "cds.String",
          semanticType: "@Semantics.date",
          primitiveType: "cds.String",
          length: 8,
        },
        {
          key: "DAY_OF_WEEK",
          text: "Day of Week",
          hasTextAssociation: true,
          hasTextElement: false,
          textAssociation: {
            key: "SAP.TIME.M_TIME_DIMENSION_TDAY",
            text: "Translation Table - Day",
          },
          isNotSupportedPrimitiveTypes: false,
          isHidden: false,
          isKey: false,
          type: "cds.String",
          semanticType: "@Semantics.calendar.dayOfYear",
          primitiveType: "cds.String",
          length: 2,
        },
        {
          key: "CALWEEK",
          text: "Calendar Week",
          hasTextAssociation: false,
          hasTextElement: false,
          isNotSupportedPrimitiveTypes: false,
          isHidden: false,
          isKey: false,
          type: "cds.String",
          semanticType: "@Semantics.calendar.yearWeek",
          primitiveType: "cds.String",
          length: 6,
        },
        {
          key: "CALMONTH",
          text: "Calendar Month",
          hasTextAssociation: true,
          hasTextElement: false,
          textAssociation: {
            key: "SAP.TIME.M_TIME_DIMENSION_TMONTH",
            text: "Translation Table - Month",
          },
          isNotSupportedPrimitiveTypes: false,
          isHidden: false,
          isKey: false,
          type: "cds.String",
          semanticType: "@Semantics.calendar.yearMonth",
          primitiveType: "cds.String",
          length: 6,
        },
        {
          key: "MONTH",
          text: "Month",
          hasTextAssociation: false,
          hasTextElement: false,
          isNotSupportedPrimitiveTypes: false,
          isHidden: false,
          isKey: false,
          type: "cds.String",
          semanticType: "@Semantics.calendar.month",
          primitiveType: "cds.String",
          length: 2,
        },
        {
          key: "CALQUARTER",
          text: "Calendar Quarter",
          hasTextAssociation: true,
          hasTextElement: false,
          textAssociation: {
            key: "SAP.TIME.M_TIME_DIMENSION_TQUARTER",
            text: "Translation Table - Quarter",
          },
          isNotSupportedPrimitiveTypes: false,
          isHidden: false,
          isKey: false,
          type: "cds.String",
          semanticType: "@Semantics.calendar.yearQuarter",
          primitiveType: "cds.String",
          length: 5,
        },
        {
          key: "QUARTER",
          text: "Quarter",
          hasTextAssociation: false,
          hasTextElement: false,
          isNotSupportedPrimitiveTypes: false,
          isHidden: false,
          isKey: false,
          type: "cds.String",
          semanticType: "noValue",
          primitiveType: "cds.String",
          length: 2,
        },
        {
          key: "YEAR",
          text: "Year",
          hasTextAssociation: false,
          hasTextElement: false,
          isNotSupportedPrimitiveTypes: false,
          isHidden: false,
          isKey: false,
          type: "cds.String",
          semanticType: "@Semantics.calendar.year",
          primitiveType: "cds.String",
          length: 4,
        },
      ],
      parameters: [],
    },
    GC_VIEW_PARAMS: {
      businessName: "GC_VIEW_PARAMS",
      technicalName: "GC_VIEW_PARAMS",
      csn: {
        kind: "entity",
        elements: {
          ItemID: {
            "@EndUserText.label": "ItemID",
            type: "cds.Integer64",
            key: true,
            notNull: true,
          },
          Opportunity: {
            "@EndUserText.label": "Opportunity",
            type: "cds.Integer64",
            "@ObjectModel.foreignKey.association": {
              "=": "_MCT_Oppor",
            },
          },
          Product: {
            "@EndUserText.label": "Product",
            type: "cds.String",
            length: 5000,
          },
          Quantity: {
            "@EndUserText.label": "Quantity",
            precision: 20,
            scale: 3,
            type: "cds.Decimal",
            "@AnalyticsDetails.measureType": {
              "#": "BASE",
            },
            "@Semantics.quantity.unitOfMeasure": {
              "=": "Unit",
            },
            "@Aggregation.default": {
              "#": "SUM",
            },
          },
          Value: {
            "@EndUserText.label": "Value",
            precision: 20,
            scale: 2,
            type: "cds.Decimal",
            "@AnalyticsDetails.measureType": {
              "#": "BASE",
            },
            "@Semantics.amount.currencyCode": {
              "=": "Currency",
            },
            "@Aggregation.default": {
              "#": "SUM",
            },
          },
          Unit: {
            "@EndUserText.label": "Unit",
            type: "cds.String",
            length: 5000,
            "@Semantics.unitOfMeasure": true,
          },
          Currency: {
            "@EndUserText.label": "Currency",
            type: "cds.String",
            length: 5000,
            "@Semantics.currencyCode": true,
          },
          FactAttr: {
            "@EndUserText.label": "FactAttr",
            type: "cds.String",
            length: 5000,
            "@ObjectModel.text.element": [
              {
                "=": "FactAttrLabel",
              },
            ],
          },
          FactAttrLabel: {
            "@EndUserText.label": "FactAttrLabel",
            type: "cds.String",
            length: 5000,
            "@Semantics.text": true,
          },
          ItemStatus: {
            "@EndUserText.label": "ItemStatus ID",
            type: "cds.Integer64",
          },
          ControllingArea: {
            "@EndUserText.label": "Controlling Area",
            type: "cds.Integer64",
          },
          SendingCostCenter: {
            "@EndUserText.label": "Sending Cost Center",
            type: "cds.Integer64",
          },
          ReceivingCostCenter: {
            "@EndUserText.label": "Receiving Cost Center",
            type: "cds.Integer64",
          },
          Calculated_1: {
            "@EndUserText.label": "Calculated_1",
            type: "cds.Date",
            "@ObjectModel.foreignKey.association": {
              "=": "_VIEW_DIME",
            },
          },
          Calculated_2: {
            "@EndUserText.label": "Calculated_2",
            type: "cds.Date",
          },
          _MCT_Oppor: {
            type: "cds.Association",
            on: [
              {
                ref: ["Opportunity"],
              },
              "=",
              {
                ref: ["_MCT_Oppor", "OpportunityID"],
              },
            ],
            target: "MCT_Opportunities",
            "@EndUserText.label": "GC_VIEW_PARAMS to MCT Opportunity Header",
          },
          _VIEW_DIME: {
            type: "cds.Association",
            "@EndUserText.label": "GC_VIEW_PARAMS to Time Dimension - Day",
            on: [
              {
                ref: ["Calculated_1"],
              },
              "=",
              {
                ref: ["_VIEW_DIME", "DATE_SQL"],
              },
            ],
            target: "SAP.TIME.VIEW_DIMENSION_DAY",
          },
        },
        query: {
          SELECT: {
            from: {
              ref: ["MCT_OpportunityItems"],
            },
            columns: [
              {
                as: "ItemID",
                key: true,
                ref: ["MCT_OpportunityItems", "ItemID"],
              },
              {
                as: "Opportunity",
                ref: ["MCT_OpportunityItems", "Opportunity"],
              },
              {
                as: "Product",
                ref: ["MCT_OpportunityItems", "Product"],
              },
              {
                as: "Quantity",
                ref: ["MCT_OpportunityItems", "Quantity"],
              },
              {
                as: "Value",
                ref: ["MCT_OpportunityItems", "Value"],
              },
              {
                as: "Unit",
                ref: ["MCT_OpportunityItems", "Unit"],
              },
              {
                as: "Currency",
                ref: ["MCT_OpportunityItems", "Currency"],
              },
              {
                as: "FactAttr",
                ref: ["MCT_OpportunityItems", "FactAttr"],
              },
              {
                as: "FactAttrLabel",
                ref: ["MCT_OpportunityItems", "FactAttrLabel"],
              },
              {
                as: "ItemStatus",
                ref: ["MCT_OpportunityItems", "ItemStatus"],
              },
              {
                as: "ControllingArea",
                ref: ["MCT_OpportunityItems", "ControllingArea"],
              },
              {
                as: "SendingCostCenter",
                ref: ["MCT_OpportunityItems", "SendingCostCenter"],
              },
              {
                as: "ReceivingCostCenter",
                ref: ["MCT_OpportunityItems", "ReceivingCostCenter"],
              },
              {
                as: "Calculated_1",
                func: "TO_DATE",
                args: [
                  {
                    val: "11-11-2020",
                  },
                ],
              },
              {
                as: "Calculated_2",
                func: "TO_DATE",
                args: [
                  {
                    val: "10-10-2010",
                  },
                ],
              },
              {
                ref: ["_MCT_Oppor"],
              },
              {
                ref: ["_VIEW_DIME"],
              },
            ],
            where: [
              {
                ref: ["MCT_OpportunityItems", "Opportunity"],
              },
              "=",
              {
                ref: ["PARAM_1"],
                param: true,
              },
            ],
            mixin: {
              _MCT_Oppor: {
                type: "cds.Association",
                "@EndUserText.label": "GC_VIEW_PARAMS to MCT Opportunity Header",
                on: [
                  {
                    ref: ["$projection", "Opportunity"],
                  },
                  "=",
                  {
                    ref: ["_MCT_Oppor", "OpportunityID"],
                  },
                ],
                target: "MCT_Opportunities",
              },
              _VIEW_DIME: {
                type: "cds.Association",
                "@EndUserText.label": "GC_VIEW_PARAMS to Time Dimension - Day",
                on: [
                  {
                    ref: ["$projection", "Calculated_1"],
                  },
                  "=",
                  {
                    ref: ["_VIEW_DIME", "DATE_SQL"],
                  },
                ],
                target: "SAP.TIME.VIEW_DIMENSION_DAY",
              },
            },
          },
        },
        "@ObjectModel.supportedCapabilities": [
          {
            "#": "DATA_STRUCTURE",
          },
        ],
        "@EndUserText.label": "GC_VIEW_PARAMS",
        "@ObjectModel.modelingPattern": {
          "#": "ANALYTICAL_FACT",
        },
        params: {
          PARAM_1: {
            "@EndUserText.label": "PARAM_1",
            type: "cds.String",
            length: 10,
            dataEntityName: "GC_VIEW_PARAMS",
            variableKey: "PARAM_1",
          },
        },
        "@DataWarehouse.consumption.external": false,
        _meta: {
          dependencies: {
            folderAssignment: null,
          },
        },
      },
      technicalType: "DWC_VIEW",
      associations: [
        {
          key: "_MCT_Oppor",
          text: "GC_VIEW_PARAMS to MCT Opportunity Header",
          targetEntity: {
            key: "MCT_Opportunities",
            text: "MCT Opportunity Header",
          },
          foreignKeys: ["Opportunity"],
          targetKeys: ["OpportunityID"],
          hasHierarchies: false,
          hasTimeDependency: false,
          timeDependencies: [],
          dimensionSourceAlias: "Opportunity",
          attributeForAssociation: "Opportunity",
          hasTextAssociation: false,
          hasTextElement: false,
          businessType: "DWC_DIMENSION",
          technicalType: "DWC_LOCAL_TABLE",
        },
        {
          key: "_VIEW_DIME",
          text: "GC_VIEW_PARAMS to Time Dimension - Day",
          targetEntity: {
            key: "SAP.TIME.VIEW_DIMENSION_DAY",
            text: "Time Dimension - Day",
          },
          foreignKeys: ["Calculated_1"],
          targetKeys: ["DATE_SQL"],
          hasHierarchies: true,
          hasTimeDependency: false,
          timeDependencies: [],
          dimensionSourceAlias: "Calculated_1",
          attributeForAssociation: "Calculated_1",
          hasTextAssociation: false,
          hasTextElement: true,
          businessType: "DWC_DIMENSION",
          technicalType: "DWC_VIEW",
        },
      ],
      measures: [
        {
          key: "Quantity",
          text: "Quantity",
          measureType: "BASE",
          aggregation: "SUM",
          semanticType: "@Semantics.quantity.unitOfMeasure",
          isHidden: false,
          unitColumn: "Unit",
        },
        {
          key: "Value",
          text: "Value",
          measureType: "BASE",
          aggregation: "SUM",
          semanticType: "@Semantics.amount.currencyCode",
          isHidden: false,
          unitColumn: "Currency",
        },
      ],
      attributes: [
        {
          key: "ItemID",
          text: "ItemID",
          hasTextAssociation: false,
          hasTextElement: false,
          isNotSupportedPrimitiveTypes: false,
          isHidden: false,
          isKey: true,
          type: "cds.Integer64",
          semanticType: "noValue",
          primitiveType: "cds.Integer64",
          keyOnModel: "ItemID",
          selected: true,
          attributeInModel: {
            attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
            attributeMapping: {
              GC_VIEW_PARAMS: {
                key: "ItemID",
              },
            },
            text: "ItemID",
            duplicated: false,
          },
          enabled: true,
          foreignKeyAssociationNotAdded: true,
          displayText: "ItemID",
          hasTimeDependency: false,
          hasHierarchies: false,
          associationHasTextElement: false,
          associationHasTextAssociation: false,
        },
        {
          key: "Opportunity",
          text: "Opportunity",
          hasTextAssociation: false,
          hasTextElement: false,
          foreignKeyAssociation: "_MCT_Oppor",
          isNotSupportedPrimitiveTypes: false,
          isHidden: false,
          isKey: false,
          type: "cds.Integer64",
          semanticType: "noValue",
          primitiveType: "cds.Integer64",
          keyOnModel: "Opportunity",
          selected: true,
          attributeInModel: {
            attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
            attributeMapping: {
              GC_VIEW_PARAMS: {
                key: "Opportunity",
              },
            },
            text: "Opportunity",
            duplicated: false,
          },
          enabled: true,
          foreignKeyAssociationNotAdded: true,
          displayText: "Opportunity",
          hasTimeDependency: false,
          hasHierarchies: false,
          associationHasTextElement: false,
          associationHasTextAssociation: false,
        },
        {
          key: "Product",
          text: "Product",
          hasTextAssociation: false,
          hasTextElement: false,
          isNotSupportedPrimitiveTypes: false,
          isHidden: false,
          isKey: false,
          type: "cds.String",
          semanticType: "noValue",
          primitiveType: "cds.String",
          length: 5000,
          keyOnModel: "Product",
          selected: true,
          attributeInModel: {
            attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
            attributeMapping: {
              GC_VIEW_PARAMS: {
                key: "Product",
              },
            },
            text: "Product",
            duplicated: false,
          },
          enabled: true,
          foreignKeyAssociationNotAdded: true,
          displayText: "Product",
          hasTimeDependency: false,
          hasHierarchies: false,
          associationHasTextElement: false,
          associationHasTextAssociation: false,
        },
        {
          key: "Unit",
          text: "Unit",
          hasTextAssociation: false,
          hasTextElement: false,
          isNotSupportedPrimitiveTypes: false,
          isHidden: false,
          isKey: false,
          type: "cds.String",
          semanticType: "@Semantics.unitOfMeasure",
          primitiveType: "cds.String",
          length: 5000,
          keyOnModel: "Unit",
          selected: true,
          attributeInModel: {
            attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
            attributeMapping: {
              GC_VIEW_PARAMS: {
                key: "Unit",
              },
            },
            text: "Unit",
            duplicated: false,
          },
          enabled: true,
          foreignKeyAssociationNotAdded: true,
          displayText: "Unit",
          hasTimeDependency: false,
          hasHierarchies: false,
          associationHasTextElement: false,
          associationHasTextAssociation: false,
        },
        {
          key: "Currency",
          text: "Currency",
          hasTextAssociation: false,
          hasTextElement: false,
          isNotSupportedPrimitiveTypes: false,
          isHidden: false,
          isKey: false,
          type: "cds.String",
          semanticType: "@Semantics.currencyCode",
          primitiveType: "cds.String",
          length: 5000,
          keyOnModel: "Currency",
          selected: true,
          attributeInModel: {
            attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
            attributeMapping: {
              GC_VIEW_PARAMS: {
                key: "Currency",
              },
            },
            text: "Currency",
            duplicated: false,
          },
          enabled: true,
          foreignKeyAssociationNotAdded: true,
          displayText: "Currency",
          hasTimeDependency: false,
          hasHierarchies: false,
          associationHasTextElement: false,
          associationHasTextAssociation: false,
        },
        {
          key: "FactAttr",
          text: "FactAttr",
          hasTextAssociation: false,
          hasTextElement: true,
          textElements: [
            {
              key: "FactAttrLabel",
              text: "FactAttrLabel",
            },
          ],
          isNotSupportedPrimitiveTypes: false,
          isHidden: false,
          isKey: false,
          type: "cds.String",
          semanticType: "noValue",
          primitiveType: "cds.String",
          length: 5000,
          keyOnModel: "FactAttr",
          selected: true,
          attributeInModel: {
            attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
            attributeMapping: {
              GC_VIEW_PARAMS: {
                key: "FactAttr",
              },
            },
            text: "FactAttr",
            duplicated: false,
          },
          enabled: true,
          foreignKeyAssociationNotAdded: true,
          displayText: "FactAttr",
          hasTimeDependency: false,
          hasHierarchies: false,
          associationHasTextElement: false,
          associationHasTextAssociation: false,
        },
        {
          key: "ItemStatus",
          text: "ItemStatus ID",
          hasTextAssociation: false,
          hasTextElement: false,
          isNotSupportedPrimitiveTypes: false,
          isHidden: false,
          isKey: false,
          type: "cds.Integer64",
          semanticType: "noValue",
          primitiveType: "cds.Integer64",
          keyOnModel: "ItemStatus",
          selected: true,
          attributeInModel: {
            attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
            attributeMapping: {
              GC_VIEW_PARAMS: {
                key: "ItemStatus",
              },
            },
            text: "ItemStatus ID",
            duplicated: false,
          },
          enabled: true,
          foreignKeyAssociationNotAdded: true,
          displayText: "ItemStatus ID",
          hasTimeDependency: false,
          hasHierarchies: false,
          associationHasTextElement: false,
          associationHasTextAssociation: false,
        },
        {
          key: "ControllingArea",
          text: "Controlling Area",
          hasTextAssociation: false,
          hasTextElement: false,
          isNotSupportedPrimitiveTypes: false,
          isHidden: false,
          isKey: false,
          type: "cds.Integer64",
          semanticType: "noValue",
          primitiveType: "cds.Integer64",
          keyOnModel: "ControllingArea",
          selected: true,
          attributeInModel: {
            attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
            attributeMapping: {
              GC_VIEW_PARAMS: {
                key: "ControllingArea",
              },
            },
            text: "Controlling Area",
            duplicated: false,
          },
          enabled: true,
          foreignKeyAssociationNotAdded: true,
          displayText: "Controlling Area",
          hasTimeDependency: false,
          hasHierarchies: false,
          associationHasTextElement: false,
          associationHasTextAssociation: false,
        },
        {
          key: "SendingCostCenter",
          text: "Sending Cost Center",
          hasTextAssociation: false,
          hasTextElement: false,
          isNotSupportedPrimitiveTypes: false,
          isHidden: false,
          isKey: false,
          type: "cds.Integer64",
          semanticType: "noValue",
          primitiveType: "cds.Integer64",
          keyOnModel: "SendingCostCenter",
          selected: true,
          attributeInModel: {
            attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
            attributeMapping: {
              GC_VIEW_PARAMS: {
                key: "SendingCostCenter",
              },
            },
            text: "Sending Cost Center",
            duplicated: false,
          },
          enabled: true,
          foreignKeyAssociationNotAdded: true,
          displayText: "Sending Cost Center",
          hasTimeDependency: false,
          hasHierarchies: false,
          associationHasTextElement: false,
          associationHasTextAssociation: false,
        },
        {
          key: "ReceivingCostCenter",
          text: "Receiving Cost Center",
          hasTextAssociation: false,
          hasTextElement: false,
          isNotSupportedPrimitiveTypes: false,
          isHidden: false,
          isKey: false,
          type: "cds.Integer64",
          semanticType: "noValue",
          primitiveType: "cds.Integer64",
          keyOnModel: "ReceivingCostCenter",
          selected: true,
          attributeInModel: {
            attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
            attributeMapping: {
              GC_VIEW_PARAMS: {
                key: "ReceivingCostCenter",
              },
            },
            text: "Receiving Cost Center",
            duplicated: false,
            isAuxiliary: true,
          },
          enabled: true,
          foreignKeyAssociationNotAdded: true,
          displayText: "Receiving Cost Center",
          hasTimeDependency: false,
          hasHierarchies: false,
          associationHasTextElement: false,
          associationHasTextAssociation: false,
        },
        {
          key: "Calculated_1",
          text: "Calculated_1",
          hasTextAssociation: false,
          hasTextElement: false,
          foreignKeyAssociation: "_VIEW_DIME",
          isNotSupportedPrimitiveTypes: false,
          isHidden: false,
          isKey: false,
          type: "cds.Date",
          semanticType: "noValue",
          primitiveType: "cds.Date",
          keyOnModel: "Calculated_1",
          selected: true,
          attributeInModel: {
            text: "Calculated_1",
            attributeMapping: {
              GC_VIEW_PARAMS: {
                key: "Calculated_1",
              },
            },
            attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
            duplicated: false,
            usedForDimensionSourceKey: "_Calculated_1",
          },
          enabled: true,
          foreignKeyAssociationNotAdded: false,
          displayText: "Calculated_1",
          hasTimeDependency: false,
          hasHierarchies: true,
          associationHasTextElement: true,
          associationHasTextAssociation: false,
        },
        {
          key: "Calculated_2",
          text: "Calculated_2",
          hasTextAssociation: false,
          hasTextElement: false,
          isNotSupportedPrimitiveTypes: false,
          isHidden: false,
          isKey: false,
          type: "cds.Date",
          semanticType: "noValue",
          primitiveType: "cds.Date",
          keyOnModel: "Calculated_2",
          selected: true,
          attributeInModel: {
            text: "Calculated_2",
            attributeMapping: {
              GC_VIEW_PARAMS: {
                key: "Calculated_2",
              },
            },
            attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
            duplicated: false,
          },
          enabled: true,
          foreignKeyAssociationNotAdded: true,
          displayText: "Calculated_2",
          hasTimeDependency: false,
          hasHierarchies: false,
          associationHasTextElement: false,
          associationHasTextAssociation: false,
        },
      ],
      parameters: [
        {
          "@EndUserText.label": "PARAM_1",
          type: "cds.String",
          length: 10,
          key: "PARAM_1",
          text: "PARAM_1",
          primitiveType: "cds.String",
        },
      ],
    },
    Neha_CurrencyOfCountry: {
      businessName: "Neha_Currency Of Country",
      technicalName: "Neha_CurrencyOfCountry",
      csn: {
        kind: "entity",
        elements: {
          Country: {
            "@EndUserText.label": "Country",
            type: "cds.String",
            length: 2,
            key: true,
            notNull: true,
          },
          Currency: {
            "@EndUserText.label": "Currency",
            type: "cds.String",
            length: 4,
          },
        },
        query: {
          SELECT: {
            from: {
              ref: ["MCT_Country"],
            },
            columns: [
              {
                key: true,
                ref: ["Country"],
              },
              {
                ref: ["Currency"],
              },
            ],
            where: [
              {
                ref: ["MCT_Country", "Country"],
              },
              "=",
              {
                ref: ["COUNTRY"],
                param: true,
              },
            ],
          },
        },
        "@ObjectModel.supportedCapabilities": [
          {
            "#": "ANALYTICAL_FACT",
          },
        ],
        "@EndUserText.label": "Neha_Currency Of Country",
        "@ObjectModel.modelingPattern": {
          "#": "ANALYTICAL_FACT",
        },
        params: {
          COUNTRY: {
            "@EndUserText.label": "Country",
            type: "cds.String",
            length: 2,
            "@Consumption.valueHelpDefinition": [
              {
                entity: {
                  name: "MCT_Country",
                  element: "Country",
                  elementText: "Country",
                  text: "MCT Country",
                },
              },
            ],
            default: "IN",
          },
        },
        "@DataWarehouse.consumption.external": false,
        _meta: {
          dependencies: {
            folderAssignment: null,
          },
        },
      },
      technicalType: "DWC_VIEW",
      associations: [],
      measures: [],
      attributes: [
        {
          key: "Country",
          text: "Country",
          hasTextAssociation: false,
          hasTextElement: false,
          isNotSupportedPrimitiveTypes: false,
          isHidden: false,
          isKey: true,
          type: "cds.String",
          semanticType: "noValue",
          primitiveType: "cds.String",
          length: 2,
        },
        {
          key: "Currency",
          text: "Currency",
          hasTextAssociation: false,
          hasTextElement: false,
          isNotSupportedPrimitiveTypes: false,
          isHidden: false,
          isKey: false,
          type: "cds.String",
          semanticType: "noValue",
          primitiveType: "cds.String",
          length: 4,
        },
      ],
      parameters: [
        {
          "@EndUserText.label": "Country",
          type: "cds.String",
          length: 2,
          "@Consumption.valueHelpDefinition": [
            {
              entity: {
                name: "MCT_Country",
                element: "Country",
                elementText: "Country",
                text: "MCT Country",
              },
            },
          ],
          default: "IN",
          key: "COUNTRY",
          text: "Country",
          primitiveType: "cds.String",
        },
      ],
    },
    DAC_Values_Multiple: {
      businessName: "DAC Values Multiple",
      technicalName: "DAC_Values_Multiple",
      csn: {
        "@DataWarehouse.dataAccessControl.definition": {
          principalElement: "user",
          sourceEntity: "Values_Table_Multiple",
        },
        kind: "entity",
        "@EndUserText.label": "DAC Values Multiple",
        "@DataWarehouse.businessDefinition.contact": "",
        elements: {
          country: {
            length: 100,
            "@EndUserText.label": "country",
            type: "cds.String",
          },
          lob: {
            length: 100,
            "@EndUserText.label": "lob",
            type: "cds.String",
          },
        },
        _meta: {
          dependencies: {
            folderAssignment: "Folder_OKLZRTFE",
          },
        },
      },
      technicalType: "DWC_DAC",
      associations: [],
      measures: [],
      attributes: [
        {
          key: "country",
          text: "country",
          hasTextAssociation: false,
          hasTextElement: false,
          isNotSupportedPrimitiveTypes: false,
          isHidden: false,
          isKey: false,
          type: "cds.String",
          semanticType: "noValue",
          primitiveType: "cds.String",
          length: 100,
        },
        {
          key: "lob",
          text: "lob",
          hasTextAssociation: false,
          hasTextElement: false,
          isNotSupportedPrimitiveTypes: false,
          isHidden: false,
          isKey: false,
          type: "cds.String",
          semanticType: "noValue",
          primitiveType: "cds.String",
          length: 100,
        },
      ],
      parameters: [],
    },
  },
  attributeTree: [
    {
      hasTextAssociation: false,
      hasTextElement: true,
      isDimension: true,
      isAttribute: false,
      sourceKey: "_Calculated_1",
      text: "Calculated_1",
      icon: "sap-icon://sac/perspectives",
      removeButton: true,
      nodes: [
        {
          nodes: [],
          text: "Year",
          duplicated: false,
          attributeType: "AnalyticModelAttributeType.DimensionSourceAttribute",
          isDimension: false,
          isAttribute: true,
          isHidden: false,
          keyOnModel: "YEAR_Calculated_1",
          selected: false,
          hasTextAssociation: false,
          hasTextElement: false,
          semanticType: "@Semantics.calendar.year",
          primitiveType: "cds.String",
          _validationMessageType: "Success",
          sortingTechnicalName: "YEAR_Calculated_1",
          sourceKey: "_Calculated_1",
        },
        {
          nodes: [],
          text: "Calendar Quarter",
          duplicated: false,
          attributeType: "AnalyticModelAttributeType.DimensionSourceAttribute",
          isDimension: false,
          isAttribute: true,
          isHidden: false,
          keyOnModel: "CALQUARTER_Calculated_1",
          selected: false,
          hasTextAssociation: true,
          hasTextElement: false,
          semanticType: "@Semantics.calendar.yearQuarter",
          primitiveType: "cds.String",
          _validationMessageType: "Success",
          sortingTechnicalName: "CALQUARTER_Calculated_1",
          sourceKey: "_Calculated_1",
        },
        {
          nodes: [],
          text: "Calendar Month",
          duplicated: false,
          attributeType: "AnalyticModelAttributeType.DimensionSourceAttribute",
          isDimension: false,
          isAttribute: true,
          isHidden: false,
          keyOnModel: "CALMONTH_Calculated_1",
          selected: false,
          hasTextAssociation: true,
          hasTextElement: false,
          semanticType: "@Semantics.calendar.yearMonth",
          primitiveType: "cds.String",
          _validationMessageType: "Success",
          sortingTechnicalName: "CALMONTH_Calculated_1",
          sourceKey: "_Calculated_1",
        },
        {
          nodes: [],
          text: "Calendar Week",
          duplicated: false,
          attributeType: "AnalyticModelAttributeType.DimensionSourceAttribute",
          isDimension: false,
          isAttribute: true,
          isHidden: false,
          keyOnModel: "CALWEEK_Calculated_1",
          selected: false,
          hasTextAssociation: false,
          hasTextElement: false,
          semanticType: "@Semantics.calendar.yearWeek",
          primitiveType: "cds.String",
          _validationMessageType: "Success",
          sortingTechnicalName: "CALWEEK_Calculated_1",
          sourceKey: "_Calculated_1",
        },
      ],
      type: "AnalyticModelSourceType.Dimension",
      hasHierarchies: true,
      hasTimeDependency: false,
      selected: false,
      _validationMessageType: "Success",
      sortingTechnicalName: "_Calculated_1",
    },
    {
      nodes: [],
      text: "ItemID",
      duplicated: false,
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      isDimension: false,
      isAttribute: true,
      isHidden: false,
      keyOnModel: "ItemID",
      selected: false,
      hasTextAssociation: false,
      hasTextElement: false,
      semanticType: "noValue",
      primitiveType: "cds.Integer64",
      _validationMessageType: "Success",
      sortingTechnicalName: "ItemID",
    },
    {
      nodes: [],
      text: "Opportunity",
      duplicated: false,
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      isDimension: false,
      isAttribute: true,
      isHidden: false,
      keyOnModel: "Opportunity",
      selected: false,
      hasTextAssociation: false,
      hasTextElement: false,
      semanticType: "noValue",
      primitiveType: "cds.Integer64",
      _validationMessageType: "Success",
      sortingTechnicalName: "Opportunity",
    },
    {
      nodes: [],
      text: "Product",
      duplicated: false,
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      isDimension: false,
      isAttribute: true,
      isHidden: false,
      keyOnModel: "Product",
      selected: false,
      hasTextAssociation: false,
      hasTextElement: false,
      semanticType: "noValue",
      primitiveType: "cds.String",
      _validationMessageType: "Success",
      sortingTechnicalName: "Product",
    },
    {
      nodes: [],
      text: "Unit",
      duplicated: false,
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      isDimension: false,
      isAttribute: true,
      isHidden: false,
      keyOnModel: "Unit",
      selected: false,
      hasTextAssociation: false,
      hasTextElement: false,
      semanticType: "@Semantics.unitOfMeasure",
      primitiveType: "cds.String",
      _validationMessageType: "Success",
      sortingTechnicalName: "Unit",
    },
    {
      nodes: [],
      text: "Currency",
      duplicated: false,
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      isDimension: false,
      isAttribute: true,
      isHidden: false,
      keyOnModel: "Currency",
      selected: false,
      hasTextAssociation: false,
      hasTextElement: false,
      semanticType: "@Semantics.currencyCode",
      primitiveType: "cds.String",
      _validationMessageType: "Success",
      sortingTechnicalName: "Currency",
    },
    {
      nodes: [],
      text: "FactAttr",
      duplicated: false,
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      isDimension: false,
      isAttribute: true,
      isHidden: false,
      keyOnModel: "FactAttr",
      selected: false,
      hasTextAssociation: false,
      hasTextElement: true,
      semanticType: "noValue",
      primitiveType: "cds.String",
      _validationMessageType: "Success",
      sortingTechnicalName: "FactAttr",
    },
    {
      nodes: [],
      text: "ItemStatus ID",
      duplicated: false,
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      isDimension: false,
      isAttribute: true,
      isHidden: false,
      keyOnModel: "ItemStatus",
      selected: false,
      hasTextAssociation: false,
      hasTextElement: false,
      semanticType: "noValue",
      primitiveType: "cds.Integer64",
      _validationMessageType: "Success",
      sortingTechnicalName: "ItemStatus",
    },
    {
      nodes: [],
      text: "Controlling Area",
      duplicated: false,
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      isDimension: false,
      isAttribute: true,
      isHidden: false,
      keyOnModel: "ControllingArea",
      selected: false,
      hasTextAssociation: false,
      hasTextElement: false,
      semanticType: "noValue",
      primitiveType: "cds.Integer64",
      _validationMessageType: "Success",
      sortingTechnicalName: "ControllingArea",
    },
    {
      nodes: [],
      text: "Sending Cost Center",
      duplicated: false,
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      isDimension: false,
      isAttribute: true,
      isHidden: false,
      keyOnModel: "SendingCostCenter",
      selected: false,
      hasTextAssociation: false,
      hasTextElement: false,
      semanticType: "noValue",
      primitiveType: "cds.Integer64",
      _validationMessageType: "Success",
      sortingTechnicalName: "SendingCostCenter",
    },
    {
      nodes: [],
      text: "Receiving Cost Center",
      duplicated: false,
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      isDimension: false,
      isAttribute: true,
      isHidden: true,
      keyOnModel: "ReceivingCostCenter",
      selected: false,
      hasTextAssociation: false,
      hasTextElement: false,
      semanticType: "noValue",
      primitiveType: "cds.Integer64",
      _validationMessageType: "Success",
      sortingTechnicalName: "ReceivingCostCenter",
    },
    {
      nodes: [],
      text: "Calculated_2",
      duplicated: false,
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      isDimension: false,
      isAttribute: true,
      isHidden: false,
      keyOnModel: "Calculated_2",
      selected: false,
      hasTextAssociation: false,
      hasTextElement: false,
      semanticType: "noValue",
      primitiveType: "cds.Date",
      _validationMessageType: "Success",
      sortingTechnicalName: "Calculated_2",
    },
  ],
  attributeModelMap: {
    "GC_VIEW_PARAMS:AnalyticModelSourceType.Fact:ItemID": "ItemID",
    "GC_VIEW_PARAMS:AnalyticModelSourceType.Fact:Opportunity": "Opportunity",
    "GC_VIEW_PARAMS:AnalyticModelSourceType.Fact:Product": "Product",
    "GC_VIEW_PARAMS:AnalyticModelSourceType.Fact:Unit": "Unit",
    "GC_VIEW_PARAMS:AnalyticModelSourceType.Fact:Currency": "Currency",
    "GC_VIEW_PARAMS:AnalyticModelSourceType.Fact:FactAttr": "FactAttr",
    "GC_VIEW_PARAMS:AnalyticModelSourceType.Fact:ItemStatus": "ItemStatus",
    "GC_VIEW_PARAMS:AnalyticModelSourceType.Fact:ControllingArea": "ControllingArea",
    "GC_VIEW_PARAMS:AnalyticModelSourceType.Fact:SendingCostCenter": "SendingCostCenter",
    "GC_VIEW_PARAMS:AnalyticModelSourceType.Fact:ReceivingCostCenter": "ReceivingCostCenter",
    "GC_VIEW_PARAMS:AnalyticModelSourceType.Fact:Calculated_1": "Calculated_1",
    "_Calculated_1:AnalyticModelSourceType.Dimension:YEAR": "YEAR_Calculated_1",
    "_Calculated_1:AnalyticModelSourceType.Dimension:CALQUARTER": "CALQUARTER_Calculated_1",
    "_Calculated_1:AnalyticModelSourceType.Dimension:CALMONTH": "CALMONTH_Calculated_1",
    "_Calculated_1:AnalyticModelSourceType.Dimension:CALWEEK": "CALWEEK_Calculated_1",
    "GC_VIEW_PARAMS:AnalyticModelSourceType.Fact:Calculated_2": "Calculated_2",
  },
  measureModelMap: {
    "GC_VIEW_PARAMS:AnalyticModelSourceType.Fact:Quantity": ["Quantity"],
    "GC_VIEW_PARAMS:AnalyticModelSourceType.Fact:Value": ["Value"],
  },
  package: "_NONE_KEY_PACKAGE_",
  packageStatus: "",
  preview: {
    noDataBox: false,
    preconditionNotSatisfied: false,
    previewFlexBox: false,
    errorProperty: {
      hasError: false,
      errorDetails: null,
      errorMessage: "",
    },
    notDeployedObjects: [],
    dragonfly: {
      configObject: {
        QueryBuilder: {
          DragDropFromBuilderPanelToTable: true,
          WindowingSettings: {
            HorizontalLazyLoad: true,
          },
          FilterLineLazyMode: true,
          Calculations: false,
          PanelSettings: {
            NavigationPanel: {
              ShowGroupPresentationMenu: true,
              ShowGroupExpandCollapseOptions: true,
              DefaultGroupPresentation: "allCollapsed",
            },
          },
          DialogSettings: {
            VariableDialog: {
              NewSettingsEnabled: true,
              SettingsPopOverEnabled: true,
              GridLayoutEnabled: true,
              MultiInputEnabled: true,
            },
          },
          MenuSettings: {
            EngineVersion: "0.0.2",
            LoggingEnabled: false,
            Menus: [
              {
                Name: "Gds.Qb.Table.Toolbar",
                UiContext: ["Gds.Qb.Table.Toolbar"],
                Items: [
                  {
                    Action: "Olap.ResetToMetadata",
                  },
                  {
                    Type: "Separator",
                  },
                  {
                    Action: "Olap.Resultset.ForceRefresh",
                  },
                  {
                    Type: "Separator",
                  },
                  {
                    Action: "Olap.OpenCurrencyConversionDialog",
                  },
                  {
                    Action: "Olap.OpenVariableDialog",
                  },
                  {
                    Action: "Olap.OpenQueryInfoDialog",
                  },
                  {
                    Type: "Separator",
                  },
                  {
                    Type: "Submenu",
                    Name: "Document.Export",
                    LocalizationKey: "FF_GLOBAL_MENU_DOCUMENT_EXPORT",
                    Icon: "fpa/export",
                    Items: [
                      {
                        Action: "Document.Export.Excel",
                      },
                      {
                        Action: "Document.Export.Csv",
                      },
                    ],
                  },
                  {
                    Type: "Separator",
                  },
                  {
                    Action: "Panel.Show.Line.Filter",
                  },
                  {
                    Type: "Separator",
                  },
                  {
                    Action: "Olap.Resultset.PauseRefresh",
                  },
                ],
              },
              {
                Name: "Gds.Qb.Builder.PresentationSettings",
                UiContext: ["Gds.Qb.Builder.PresentationSettings"],
                Items: [
                  {
                    Action: "Panel.Presentation",
                  },
                ],
              },
              {
                Name: "Gds.Qb.Table.ContextMenu.Dimensions",
                UiContext: ["Gds.Qb.Table.ContextMenu"],
                DataContext: ["Olap.Dimension.* && ! Olap.Member.*"],
                Items: [
                  {
                    Submenu: "Dimension.Settings",
                    Flat: true,
                  },
                  {
                    Type: "Separator",
                  },
                  {
                    DataContext: ["Selection.Rows.Dimension.Headers"],
                    Submenu: "Axis.Rows.Settings",
                  },
                  {
                    DataContext: ["Selection.Columns.Dimension.Headers"],
                    Submenu: "Axis.Columns.Settings",
                  },
                  {
                    Submenu: "Query.Settings",
                  },
                  {
                    Type: "Separator",
                  },
                  {
                    Submenu: "GridControl.Settings",
                  },
                  {
                    Type: "Separator",
                  },
                  {
                    Submenu: "GenericActions",
                  },
                ],
              },
              {
                Name: "Gds.Qb.Table.ContextMenu.Members",
                UiContext: ["Gds.Qb.Table.ContextMenu"],
                DataContext: ["Olap.Dimension.* && Olap.Member.*"],
                Items: [
                  {
                    Submenu: "Member.Settings",
                    Flat: true,
                  },
                  {
                    Type: "Separator",
                  },
                  {
                    Submenu: "Query.Settings",
                  },
                  {
                    Type: "Separator",
                  },
                  {
                    Submenu: "GridControl.Settings",
                  },
                  {
                    Type: "Separator",
                  },
                  {
                    Submenu: "GenericActions",
                  },
                ],
              },
              {
                Name: "Gds.Qb.Table.ContextMenu.Cells",
                UiContext: ["Gds.Qb.Table.ContextMenu"],
                DataContext: ["Selection.Columns.Data && Selection.Rows.Data"],
                Items: [
                  {
                    Submenu: "Member.Settings",
                    Flat: true,
                  },
                  {
                    Type: "Separator",
                  },
                  {
                    Submenu: "Axis.Rows.Settings",
                  },
                  {
                    Submenu: "Axis.Columns.Settings",
                  },
                  {
                    Submenu: "Query.Settings",
                  },
                  {
                    Type: "Separator",
                  },
                  {
                    Submenu: "GridControl.Settings",
                  },
                  {
                    Type: "Separator",
                  },
                  {
                    Submenu: "GenericActions",
                  },
                ],
              },
              {
                Name: "Gds.Qb.Table.ContextMenu.Default",
                UiContext: ["Gds.Qb.Table.ContextMenu"],
                Items: [
                  {
                    Submenu: "Query.Settings",
                    Flat: true,
                  },
                  {
                    Type: "Separator",
                  },
                  {
                    Submenu: "GridControl.Settings",
                    Flat: true,
                  },
                  {
                    Type: "Separator",
                  },
                  {
                    Submenu: "GenericActions",
                  },
                ],
              },
              {
                Name: "Gds.Qb.Builder.ContextMenu.Dimensions",
                UiContext: ["Gds.Qb.Builder.ContextMenu"],
                DataContext: ["Olap.Dimension.Single.*"],
                Items: [
                  {
                    Submenu: "Dimension.Settings",
                    Flat: true,
                  },
                ],
              },
              {
                Name: "Gds.Qb.Builder.ContextMenu.Members",
                UiContext: ["Gds.Qb.Builder.ContextMenu"],
                DataContext: ["Olap.Structure.Member.*.Single"],
                Items: [
                  {
                    Submenu: "Member.Settings",
                    Flat: true,
                  },
                ],
              },
              {
                Name: "Gds.Qb.Builder.ContextMenu.Axis",
                UiContext: ["Gds.Qb.Builder.ContextMenu"],
                DataContext: ["Olap.Axis.*"],
                Items: [
                  {
                    Submenu: "Axis.Settings",
                    Flat: true,
                  },
                  {
                    Type: "Separator",
                  },
                  {
                    Submenu: "Query.Settings",
                  },
                ],
              },
            ],
            Submenus: [
              {
                Type: "Submenu",
                Name: "Query.Settings",
                Icon: "wrench",
                LocalizationKey: "FF_GDS_QB_OLAP_QUERY",
                Items: [
                  {
                    Action: "Olap.OpenCurrencyConversionDialog",
                  },
                  {
                    Action: "Olap.SwapAxis",
                  },
                  {
                    Action: "Olap.OpenVariableDialog",
                  },
                  {
                    Action: "Olap.OpenQueryInfoDialog",
                  },
                ],
              },
              {
                Type: "Submenu",
                Name: "Axis.Rows.Settings",
                Icon: "table-row",
                LocalizationKey: "FF_GDS_QB_AXIS_ROWS",
                RefocusContext: "Olap.QueryModel/Olap.Axis.Rows",
                Items: [
                  {
                    Submenu: "Axis.Settings",
                  },
                ],
              },
              {
                Type: "Submenu",
                Name: "Axis.Columns.Settings",
                Icon: "table-column",
                LocalizationKey: "FF_GDS_QB_AXIS_COLUMNS",
                RefocusContext: "Olap.QueryModel/Olap.Axis.Columns",
                Items: [
                  {
                    Submenu: "Axis.Settings",
                  },
                ],
              },
              {
                Type: "Submenu",
                Name: "Axis.Settings",
                Flat: true,
                Items: [
                  {
                    Action: "Olap.Axis.SuppressZero",
                  },
                  {
                    Type: "Submenu",
                    Name: "Olap.Axis.UDH",
                    Icon: "tree",
                    LocalizationKey: "FF_GDS_QB_DRILL_COMPACT_DISPLAY",
                    Items: [
                      {
                        Action: "Olap.Axis.UDH.ToggleUniversalDisplayHierarchy",
                        LocalizationKey: "FF_GDS_QB_DRILL_MERGE_DIMENSIONS_ON_AXIS",
                      },
                      {
                        Action: "Olap.Axis.UDH.SelectDisplayLevel",
                        HideIfLessThanNItems: 0,
                        FlatIfLessThanNItems: 0,
                      },
                    ],
                  },
                  {
                    Type: "Separator",
                  },
                  {
                    Action: "Olap.Axis.Totals.Pos",
                  },
                ],
              },
              {
                Type: "Submenu",
                Name: "Dimension.Settings",
                Icon: "dimension",
                LocalizationKey: "FF_GDS_QB_DIMENSION",
                Items: [
                  {
                    Action: "Olap.OpenDimensionDialog",
                  },
                  {
                    Type: "Submenu",
                    Name: "Olap.Dimension.Move",
                    LocalizationKey: "FF_GDS_QB_DIMENSION_MOVE",
                    Items: [
                      {
                        Type: "Submenu",
                        Name: "Olap.Field.Move",
                        DataContext: ["Olap.Dimension.Single.*/Olap.Field.*"],
                        LocalizationKey: "FF_GDS_QB_DIMENSION_FIELDS",
                        Items: [
                          {
                            Action: "Olap.Dimension.Field.MoveBefore",
                          },
                          {
                            Action: "Olap.Dimension.Field.MoveAfter",
                          },
                        ],
                      },
                      {
                        Type: "Submenu",
                        Name: "Olap.Attribute.Move",
                        LocalizationKey: "FF_GDS_QB_DIMENSION_ATTRIBUTES",
                        Items: [
                          {
                            Action: "Olap.Dimension.Attribute.MoveBefore",
                          },
                          {
                            Action: "Olap.Dimension.Attribute.MoveAfter",
                          },
                        ],
                      },
                      {
                        Action: "Olap.Dimension.MoveBefore",
                      },
                      {
                        Action: "Olap.Dimension.MoveAfter",
                      },
                      {
                        Type: "Separator",
                      },
                      {
                        Action: "Olap.Dimension.MoveToRows",
                      },
                      {
                        Action: "Olap.Dimension.MoveToColumns",
                      },
                    ],
                  },
                  {
                    Action: "Olap.Dimension.MoveToFree",
                  },
                  {
                    Type: "Separator",
                  },
                  {
                    Type: "Submenu",
                    Name: "Olap.Dimension.Presentation.Setting",
                    LocalizationKey: "FF_GDS_QB_PRESENTATIONS",
                    Items: [
                      {
                        Flat: true,
                        Action: "Olap.Dimension.Presentation",
                      },
                      {
                        Type: "Separator",
                      },
                      {
                        Type: "Submenu",
                        Name: "Olap.Dimension.Presentation.KeyType.Advanced",
                        LocalizationKey: "FF_GDS_QB_PRESENTATION_KEY_TYPE",
                        Items: [
                          {
                            Action: "Olap.Dimension.Presentation.KeyType",
                            Flat: true,
                          },
                          {
                            Type: "Separator",
                          },
                          {
                            Action: "Olap.Dimension.Presentation.KeyType.Default",
                          },
                        ],
                      },
                      {
                        Type: "Submenu",
                        Name: "Olap.Dimension.Presentation.TextType.Advanced",
                        LocalizationKey: "FF_GDS_QB_PRESENTATION_TEXT_TYPE",
                        Items: [
                          {
                            Action: "Olap.Dimension.Presentation.TextType",
                            Flat: true,
                          },
                          {
                            Type: "Separator",
                          },
                          {
                            Action: "Olap.Dimension.Presentation.TextType.Default",
                          },
                        ],
                      },
                    ],
                  },
                  {
                    Type: "Submenu",
                    Name: "Olap.Dimension.Field.ToggleDisplay",
                    LocalizationKey: "FF_GDS_QB_DIMENSION_FIELDS",
                    OverflowIfMoreThanNItems: 10,
                    OverflowLocalizationKey: "FF_GDS_QB_OVERFLOW_NEXT_PAGE",
                    Items: [
                      {
                        ContextIteration: "Olap.Dimension.Single.*/Olap.Field.*",
                        Action: "Olap.Dimension.Field.ToggleDisplay",
                      },
                    ],
                  },
                  {
                    Type: "Submenu",
                    Name: "Olap.Dimension.Sort.Generic",
                    LocalizationKey: "FF_GDS_QB_SORT",
                    Items: [
                      {
                        Action: "Olap.Dimension.Sort",
                        Name: "Olap.Dimension.Sort.Ascending",
                        LocalizationKey: "FF_GDS_QB_SORT_ASCENDING",
                        Items: [
                          {
                            Option: "Olap.Dimension.Sort.Dir.Ascending.ByKey",
                            LocalizationKey: "FF_GDS_QB_SORT_BY_KEY",
                          },
                          {
                            Option: "Olap.Dimension.Sort.Dir.Ascending.ByText",
                            LocalizationKey: "FF_GDS_QB_SORT_BY_TEXT",
                          },
                        ],
                      },
                      {
                        Action: "Olap.Dimension.Sort",
                        Name: "Olap.Dimension.Sort.Descending",
                        LocalizationKey: "FF_GDS_QB_SORT_DESCENDING",
                        Items: [
                          {
                            Option: "Olap.Dimension.Sort.Dir.Descending.ByKey",
                            LocalizationKey: "FF_GDS_QB_SORT_BY_KEY",
                          },
                          {
                            Option: "Olap.Dimension.Sort.Dir.Descending.ByText",
                            LocalizationKey: "FF_GDS_QB_SORT_BY_TEXT",
                          },
                        ],
                      },
                      {
                        Action: "Olap.Dimension.Sort",
                        Name: "Olap.Dimension.Sort.Default",
                        Flat: true,
                        Items: [
                          {
                            Option: "Olap.Dimension.Sort.Dir.DefaultOrder",
                          },
                        ],
                      },
                    ],
                  },
                  {
                    Type: "Submenu",
                    Name: "Olap.Dimension.Attributes.ToggleDisplay",
                    LocalizationKey: "FF_GDS_QB_DIMENSION_ATTRIBUTES",
                    OverflowIfMoreThanNItems: 10,
                    OverflowLocalizationKey: "FF_GDS_QB_OVERFLOW_NEXT_PAGE",
                    Items: [
                      {
                        ContextIteration: "Olap.Dimension.Single.*/Olap.Attribute.*",
                        Type: "Submenu",
                        Name: "Olap.Attribute.Presentation.Setting",
                        Items: [
                          {
                            Action: "Olap.Attribute.Presentation",
                            Flat: true,
                          },
                          {
                            Type: "Separator",
                          },
                          {
                            Type: "Submenu",
                            Name: "Olap.Attribute.Presentation.KeyType.Advanced",
                            LocalizationKey: "FF_GDS_QB_PRESENTATION_KEY_TYPE",
                            Items: [
                              {
                                Action: "Olap.Attribute.Presentation.KeyType",
                                Flat: true,
                              },
                              {
                                Type: "Separator",
                              },
                              {
                                Action: "Olap.Attribute.Presentation.KeyType.Default",
                              },
                            ],
                          },
                          {
                            Type: "Submenu",
                            Name: "Olap.Attribute.Presentation.TextType.Advanced",
                            LocalizationKey: "FF_GDS_QB_PRESENTATION_TEXT_TYPE",
                            Items: [
                              {
                                Action: "Olap.Attribute.Presentation.TextType",
                                Flat: true,
                              },
                              {
                                Type: "Separator",
                              },
                              {
                                Action: "Olap.Attribute.Presentation.TextType.Default",
                              },
                            ],
                          },
                        ],
                      },
                    ],
                  },
                  {
                    Type: "Separator",
                  },
                  {
                    Type: "Submenu",
                    Name: "Olap.Hierarchy",
                    FlatIfLessThanNItems: 1,
                    Icon: "tree",
                    LocalizationKey: "FF_GDS_QB_DRILL_HIERARCHY",
                    Items: [
                      {
                        Action: "Olap.OpenHierarchyDialog.Simple",
                      },
                      {
                        Action: "Olap.Dimension.Hierarchy.SelectDisplayLevel",
                      },
                      {
                        Action: "Olap.OpenDimensionDialog.Hierarchy",
                      },
                    ],
                  },
                  {
                    Type: "Submenu",
                    Name: "Olap.Dimension.Filter",
                    LocalizationKey: "FF_GDS_QB_FILTER",
                    Items: [
                      {
                        Action: "Olap.OpenFilterDialog",
                      },
                      {
                        Action: "Olap.Filter.ClearSelectedDimension",
                      },
                    ],
                  },
                  {
                    Type: "Separator",
                  },
                  {
                    Action: "Olap.Dimension.ReadMode.Toggle.GracefulUnbooked",
                    LocalizationKey: "FF_GDS_QB_READMODE_UNBOOKED_INCLUDE",
                  },
                  {
                    Type: "Separator",
                  },
                  {
                    Type: "Submenu",
                    Name: "Olap.Dimension.Totals",
                    LocalizationKey: "FF_GDS_QB_TOTALS",
                    Items: [
                      {
                        Action: "Olap.Dimension.Totals.Visibility",
                        Flat: true,
                      },
                      {
                        Type: "Separator",
                      },
                      {
                        Action: "Olap.Dimension.Totals.Member.Including.Visibility.Toggle",
                      },
                      {
                        Action: "Olap.Dimension.Totals.Member.Remaining.Visibility.Toggle",
                      },
                    ],
                  },
                ],
              },
              {
                Type: "Submenu",
                Name: "Member.Settings",
                Items: [
                  {
                    Action: "Olap.Dialogs.OpenMeasureDialog",
                  },
                  {
                    Action: "Olap.Dialogs.OpenAccountDialog",
                  },
                  {
                    Action: "Olap.Dialogs.OpenDataCellDialog",
                  },
                  {
                    Action: "Olap.DataCell.Sort",
                  },
                  {
                    DataContext: ["!Olap.QueryModel/Olap.Capabilities.DataCellSorting"],
                    Action: "Olap.Measure.Sort",
                  },
                  {
                    Type: "Separator",
                  },
                  {
                    Type: "Submenu",
                    Name: "Olap.Cellchart",
                    LocalizationKey: "FF_GDS_QB_CELLCHART",
                    Items: [
                      {
                        Action: "Olap.CellChart.ActivateOnMeasure",
                      },
                      {
                        Action: "Olap.CellChart.KeepValueToggle",
                      },
                      {
                        Action: "Olap.CellChart.Type",
                      },
                      {
                        Action: "Olap.CellChart.Orientation",
                      },
                      {
                        Action: "Olap.CellChart.LineColorSelection",
                      },
                      {
                        Action: "Olap.CellChart.BarColorSelection",
                      },
                    ],
                  },
                  {
                    Type: "Separator",
                  },
                  {
                    Action: "Olap.Filter.SelectedMember",
                  },
                  {
                    Action: "Olap.Filter.ExcludeSelectedMember",
                  },
                  {
                    Action: "Olap.Filter.SelectedMemberAndDrill",
                  },
                  {
                    Type: "Submenu",
                    Name: "Olap.Filter.FilterAndExchange",
                    LocalizationKey: "FF_GDS_QB_FILTER_DRILL_REPLACE_DIMENSION",
                    OverflowIfMoreThanNItems: 10,
                    OverflowLocalizationKey: "FF_GDS_QB_OVERFLOW_NEXT_PAGE",
                    Items: [
                      {
                        ContextIteration: "Olap.QueryModel/Olap.Dimension.Free.*",
                        Action: "Olap.Filter.SelectedMemberAndExchangeDimension",
                      },
                    ],
                  },
                ],
              },
              {
                Type: "Submenu",
                Name: "GenericActions",
                Flat: true,
                Items: [
                  {
                    Action: "Olap.ResetToMetadata",
                  },
                ],
              },
              {
                Type: "Submenu",
                Name: "GridControl.Settings",
                Icon: "provision",
                LocalizationKey: "FF_GDS_QB_GRID",
                Items: [
                  {
                    Type: "Submenu",
                    Name: "Grid.Freeze",
                    Icon: "locked",
                    LocalizationKey: "FF_GDS_QB_GRID_FREEZE",
                    Items: [
                      {
                        Action: "Grid.Freeze.Columns",
                      },
                      {
                        Action: "Grid.Freeze.Rows",
                      },
                      {
                        Action: "Grid.Freeze.Headers",
                      },
                      {
                        Type: "Separator",
                      },
                      {
                        Action: "Grid.Freeze.None",
                      },
                      {
                        Action: "Grid.Freeze.UpToRow",
                      },
                      {
                        Action: "Grid.Freeze.UpToColumn",
                      },
                      {
                        Type: "Separator",
                      },
                      {
                        Action: "Grid.Freeze.Lines",
                      },
                    ],
                  },
                  {
                    Type: "Submenu",
                    Name: "Grid.Toggle.Display",
                    Icon: "show",
                    LocalizationKey: "FF_GDS_QB_GRID_SHOW",
                    Items: [
                      {
                        Action: "Grid.Toggle.Display.Lines",
                      },
                      {
                        Action: "Grid.Toggle.Display.Headers",
                      },
                      {
                        Action: "Grid.Toggle.Display.Title",
                      },
                      {
                        Action: "Grid.Toggle.Display.SubTitle",
                      },
                      {
                        Type: "Separator",
                      },
                      {
                        Action: "ResultsetRender.Striping.Toggle.Rows",
                      },
                      {
                        Action: "ResultsetRender.Striping.Toggle.Columns",
                      },
                      {
                        Type: "Separator",
                      },
                      {
                        Action: "ResultsetRender.Display.Title.Toggle",
                      },
                      {
                        Action: "ResultsetRender.Highlight.Sections123.Toggle",
                      },
                      {
                        Action: "ResultsetRender.Highlight.Totals.Toggle",
                      },
                      {
                        Action: "ResultsetRender.MemberRepetition.Toggle",
                      },
                      {
                        Action: "ResultsetRender.MemberMerge.Toggle",
                      },
                    ],
                  },
                ],
              },
            ],
          },
        },
      },
    },
  },
  analyticModelViewMode: "cubeQueryModel",
  validationMessages: [
    {
      message: "Variable 'PARAM_1' result column data type does not match",
      messageKey: "validationMessageLookupEntityResultColumnType",
      descriptionKey: "validationDescriptionMessageLookupEntityResultColumnType",
      type: "Warning",
      parameters: ["PARAM_1", "Country", "String(2)", "Neha_CurrencyOfCountry", "PARAM_1", "String(10)"],
      propertyPath: "/variables/PARAM_1/resultElement",
      validatorName: "VariableValidator",
    },
  ],
  nonCumulativeSettings: {
    timeDimensionKey: "Calculated_1",
    timeDimensionText: "Calculated_1",
    recordTypeAttributeKey: "ReceivingCostCenter",
    recordTypeAttributeText: "Receiving Cost Center",
    reportingMinStartTime: "",
    reportingMaxEndTime: "",
  },
  attributesByCurrencyDetail: {
    ItemID: {
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      attributeMapping: {
        GC_VIEW_PARAMS: {
          key: "ItemID",
        },
      },
      text: "ItemID",
      duplicated: false,
      semanticType: "noValue",
      primitiveType: "cds.Integer64",
    },
    Opportunity: {
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      attributeMapping: {
        GC_VIEW_PARAMS: {
          key: "Opportunity",
        },
      },
      text: "Opportunity",
      duplicated: false,
      semanticType: "noValue",
      primitiveType: "cds.Integer64",
    },
    Product: {
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      attributeMapping: {
        GC_VIEW_PARAMS: {
          key: "Product",
        },
      },
      text: "Product",
      duplicated: false,
      semanticType: "noValue",
      primitiveType: "cds.String",
    },
    Unit: {
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      attributeMapping: {
        GC_VIEW_PARAMS: {
          key: "Unit",
        },
      },
      text: "Unit",
      duplicated: false,
      semanticType: "@Semantics.unitOfMeasure",
      primitiveType: "cds.String",
    },
    Currency: {
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      attributeMapping: {
        GC_VIEW_PARAMS: {
          key: "Currency",
        },
      },
      text: "Currency",
      duplicated: false,
      semanticType: "@Semantics.currencyCode",
      primitiveType: "cds.String",
    },
    FactAttr: {
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      attributeMapping: {
        GC_VIEW_PARAMS: {
          key: "FactAttr",
        },
      },
      text: "FactAttr",
      duplicated: false,
      semanticType: "noValue",
      primitiveType: "cds.String",
    },
    ItemStatus: {
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      attributeMapping: {
        GC_VIEW_PARAMS: {
          key: "ItemStatus",
        },
      },
      text: "ItemStatus ID",
      duplicated: false,
      semanticType: "noValue",
      primitiveType: "cds.Integer64",
    },
    ControllingArea: {
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      attributeMapping: {
        GC_VIEW_PARAMS: {
          key: "ControllingArea",
        },
      },
      text: "Controlling Area",
      duplicated: false,
      semanticType: "noValue",
      primitiveType: "cds.Integer64",
    },
    SendingCostCenter: {
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      attributeMapping: {
        GC_VIEW_PARAMS: {
          key: "SendingCostCenter",
        },
      },
      text: "Sending Cost Center",
      duplicated: false,
      semanticType: "noValue",
      primitiveType: "cds.Integer64",
    },
    ReceivingCostCenter: {
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      attributeMapping: {
        GC_VIEW_PARAMS: {
          key: "ReceivingCostCenter",
        },
      },
      text: "Receiving Cost Center",
      duplicated: false,
      isAuxiliary: true,
      semanticType: "noValue",
      primitiveType: "cds.Integer64",
    },
    Calculated_1: {
      text: "Calculated_1",
      attributeMapping: {
        GC_VIEW_PARAMS: {
          key: "Calculated_1",
        },
      },
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      duplicated: false,
      usedForDimensionSourceKey: "_Calculated_1",
    },
    YEAR_Calculated_1: {
      key: "YEAR",
      text: "Year",
      sourceKey: "_Calculated_1",
      attributeType: "AnalyticModelAttributeType.DimensionSourceAttribute",
      duplicated: false,
      semanticType: "@Semantics.calendar.year",
      primitiveType: "cds.String",
    },
    CALQUARTER_Calculated_1: {
      key: "CALQUARTER",
      text: "Calendar Quarter",
      sourceKey: "_Calculated_1",
      attributeType: "AnalyticModelAttributeType.DimensionSourceAttribute",
      duplicated: false,
      semanticType: "@Semantics.calendar.yearQuarter",
      primitiveType: "cds.String",
    },
    CALMONTH_Calculated_1: {
      key: "CALMONTH",
      text: "Calendar Month",
      sourceKey: "_Calculated_1",
      attributeType: "AnalyticModelAttributeType.DimensionSourceAttribute",
      duplicated: false,
      semanticType: "@Semantics.calendar.yearMonth",
      primitiveType: "cds.String",
    },
    CALWEEK_Calculated_1: {
      key: "CALWEEK",
      text: "Calendar Week",
      sourceKey: "_Calculated_1",
      attributeType: "AnalyticModelAttributeType.DimensionSourceAttribute",
      duplicated: false,
      semanticType: "@Semantics.calendar.yearWeek",
      primitiveType: "cds.String",
    },
    Calculated_2: {
      text: "Calculated_2",
      attributeMapping: {
        GC_VIEW_PARAMS: {
          key: "Calculated_2",
        },
      },
      attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
      duplicated: false,
      semanticType: "noValue",
      primitiveType: "cds.Date",
    },
  },
  isStackedModel: false,
  breadcrumbs: {
    currentText: {
      businessName: "GC_DEPENDENCIES",
      technicalName: "GC_DEPENDENCIES",
    },
    links: [],
  },
  header: {
    displayName: {
      businessName: "GC_DEPENDENCIES",
      technicalName: "GC_DEPENDENCIES",
    },
    displayIcon: "sap-icon://database",
  },
  measureDetails: {},
  newProperty: false,
  newPropertyExistsAlready: false,
  routingFromTechnicalNameUpdate: false,
  routingFromSameObject: false,
  dataAccessControlDetails: {
    key: "DAC_Values_Multiple",
    text: "DAC Values Multiple",
    selected: false,
    _validationMessageType: "Success",
    dacSpaceId: "BBDEV",
    dacObjectName: "DAC_Values_Multiple",
    to: {
      technicalName: "DAC_Values_Multiple",
      businessName: "DAC Values Multiple",
    },
    leftObject: {
      technicalName: "GC_DEPENDENCIES",
      businessName: "GC_DEPENDENCIES",
    },
    leftList: [
      {
        technicalName: "ItemID",
        businessName: "ItemID",
        displayName: "ItemID (GC_VIEW_PARAMS)",
        primitiveDataType: "cds.Integer64",
        displayDataType: "Integer64",
        semanticType: "noValue",
        hidden: false,
      },
      {
        technicalName: "Opportunity",
        businessName: "Opportunity",
        displayName: "Opportunity (GC_VIEW_PARAMS)",
        primitiveDataType: "cds.Integer64",
        displayDataType: "Integer64",
        semanticType: "noValue",
        hidden: false,
      },
      {
        technicalName: "Product",
        businessName: "Product",
        displayName: "Product (GC_VIEW_PARAMS)",
        primitiveDataType: "cds.String",
        displayDataType: "String(5000)",
        semanticType: "noValue",
        hidden: false,
      },
      {
        technicalName: "Unit",
        businessName: "Unit",
        displayName: "Unit (GC_VIEW_PARAMS)",
        primitiveDataType: "cds.String",
        displayDataType: "String(5000)",
        semanticType: "@Semantics.unitOfMeasure",
        hidden: false,
      },
      {
        technicalName: "Currency",
        businessName: "Currency",
        displayName: "Currency (GC_VIEW_PARAMS)",
        primitiveDataType: "cds.String",
        displayDataType: "String(5000)",
        semanticType: "@Semantics.currencyCode",
        hidden: false,
      },
      {
        technicalName: "FactAttr",
        businessName: "FactAttr",
        displayName: "FactAttr (GC_VIEW_PARAMS)",
        primitiveDataType: "cds.String",
        displayDataType: "String(5000)",
        semanticType: "noValue",
        hidden: false,
      },
      {
        technicalName: "ItemStatus",
        businessName: "ItemStatus ID",
        displayName: "ItemStatus ID (GC_VIEW_PARAMS)",
        primitiveDataType: "cds.Integer64",
        displayDataType: "Integer64",
        semanticType: "noValue",
        hidden: false,
      },
      {
        technicalName: "ControllingArea",
        businessName: "Controlling Area",
        displayName: "Controlling Area (GC_VIEW_PARAMS)",
        primitiveDataType: "cds.Integer64",
        displayDataType: "Integer64",
        semanticType: "noValue",
        hidden: false,
      },
      {
        technicalName: "SendingCostCenter",
        businessName: "Sending Cost Center",
        displayName: "Sending Cost Center (GC_VIEW_PARAMS)",
        primitiveDataType: "cds.Integer64",
        displayDataType: "Integer64",
        semanticType: "noValue",
        hidden: false,
      },
      {
        technicalName: "ReceivingCostCenter",
        businessName: "Receiving Cost Center",
        displayName: "Receiving Cost Center (GC_VIEW_PARAMS)",
        primitiveDataType: "cds.Integer64",
        displayDataType: "Integer64",
        semanticType: "noValue",
        hidden: true,
      },
      {
        technicalName: "Calculated_1",
        businessName: "Calculated_1",
        displayName: "Calculated_1 (GC_VIEW_PARAMS)",
        primitiveDataType: "cds.Date",
        displayDataType: "Date",
        semanticType: "noValue",
        hidden: false,
      },
      {
        technicalName: "YEAR_Calculated_1",
        businessName: "Year",
        displayName: "Year (Calculated_1)",
        primitiveDataType: "cds.String",
        displayDataType: "String(4)",
        semanticType: "@Semantics.calendar.year",
        hidden: false,
      },
      {
        technicalName: "CALQUARTER_Calculated_1",
        businessName: "Calendar Quarter",
        displayName: "Calendar Quarter (Calculated_1)",
        primitiveDataType: "cds.String",
        displayDataType: "String(5)",
        semanticType: "@Semantics.calendar.yearQuarter",
        hidden: false,
      },
      {
        technicalName: "CALMONTH_Calculated_1",
        businessName: "Calendar Month",
        displayName: "Calendar Month (Calculated_1)",
        primitiveDataType: "cds.String",
        displayDataType: "String(6)",
        semanticType: "@Semantics.calendar.yearMonth",
        hidden: false,
      },
      {
        technicalName: "CALWEEK_Calculated_1",
        businessName: "Calendar Week",
        displayName: "Calendar Week (Calculated_1)",
        primitiveDataType: "cds.String",
        displayDataType: "String(6)",
        semanticType: "@Semantics.calendar.yearWeek",
        hidden: false,
      },
      {
        technicalName: "Calculated_2",
        businessName: "Calculated_2",
        displayName: "Calculated_2 (GC_VIEW_PARAMS)",
        primitiveDataType: "cds.Date",
        displayDataType: "Date",
        semanticType: "noValue",
        hidden: false,
      },
    ],
    rightObject: {
      kind: "DataAccessControl",
      technicalName: "DAC_Values_Multiple",
      businessName: "DAC Values Multiple",
      spaceName: "BBDEV",
    },
    rightList: [
      {
        technicalName: "country",
        businessName: "country",
        displayName: "country",
        primitiveDataType: "cds.String",
        hidden: false,
        semanticType: "noValue",
        displayDataType: "String(100)",
      },
      {
        technicalName: "lob",
        businessName: "lob",
        displayName: "lob",
        primitiveDataType: "cds.String",
        hidden: false,
        semanticType: "noValue",
        displayDataType: "String(100)",
      },
    ],
    mappings: [
      {
        source: {
          technicalName: "country",
          businessName: "country",
          displayName: "country",
          primitiveDataType: "cds.String",
          hidden: false,
          semanticType: "noValue",
          displayDataType: "String(100)",
        },
        target: {
          technicalName: "Opportunity",
          businessName: "Opportunity",
          displayName: "Opportunity (GC_VIEW_PARAMS)",
          primitiveDataType: "cds.Integer64",
          displayDataType: "Integer64",
          semanticType: "noValue",
          hidden: false,
        },
      },
      {
        source: {
          technicalName: "lob",
          businessName: "lob",
          displayName: "lob",
          primitiveDataType: "cds.String",
          hidden: false,
          semanticType: "noValue",
          displayDataType: "String(100)",
        },
        target: {
          technicalName: "Product",
          businessName: "Product",
          displayName: "Product (GC_VIEW_PARAMS)",
          primitiveDataType: "cds.String",
          displayDataType: "String(5000)",
          semanticType: "noValue",
          hidden: false,
        },
      },
    ],
  },
  attributeDetails: {
    attributeType: "AnalyticModelAttributeType.FactSourceAttribute",
    attributeMapping: {
      GC_VIEW_PARAMS: {
        key: "Product",
      },
    },
    text: "Product",
    duplicated: false,
    keyOnModel: "Product",
    derivable: true,
    sourceName: {
      technicalName: "GC_VIEW_PARAMS",
      businessName: "GC_VIEW_PARAMS",
    },
    sourceField: {
      technicalName: "Product",
      businessName: "Product",
    },
    originalBusinessName: "Product",
    originalTechnicalName: "Product",
    usedIn: [
      {
        type: "UsedInType.DAC",
        businessName: "DAC Values Multiple",
        technicalName: "DAC_Values_Multiple",
        typeIcon: "sap-icon://permission",
      },
    ],
    dataType: "String(5000)",
    semanticType: "noValue",
    usedInNonForeignKeyAssociation: false,
    isReferenceAttribute: false,
    technicalNameStateText: "",
    businessNameStateText: "",
    sourceEntity: {
      icon: "sap-icon://sac/table-view",
      href: "#/databuilder&/db/BBDEV/GC_VIEW_PARAMS",
      businessName: "GC_VIEW_PARAMS",
      technicalName: "GC_VIEW_PARAMS",
    },
  },
  nonForeignKeyAssociations: [],
  crossCalculationDetails: {},
  crossCalculations: [
    {
      text: "Restricted Cross Calculation",
      technicalName: "RESTRICTED_CC",
      crossCalculationType: "AnalyticModelCrossCalculationType.RestrictedCrossCalculation",
      typeIcon: "sap-icon://sac/measure-filter",
      validationStatus: "Success",
    },
    {
      text: "Calculated Cross Calculation",
      technicalName: "CALCULATED_CC",
      crossCalculationType: "AnalyticModelCrossCalculationType.CalculatedCrossCalculation",
      typeIcon: "sap-icon://sac/formula",
      validationStatus: "Success",
    },
  ],
};

export const expectedDependencyGraph = {
  "MEASURE.Quantity": {
    technicalName: "Quantity",
    businessName: "Quantity",
    type: "MEASURE",
    subtype: "AnalyticModelMeasureType.FactSourceMeasure",
    dependentBy: {
      "MEASURE.Calculated_Measure": true,
      "MEASURE.Restricted_Measure1": true,
      "MEASURE.Non_Cumulative_Measure": true,
    },
    dependencies: {},
    icon: "sap-icon://sac/measure",
  },
  "MEASURE.Calculated_Measure": {
    technicalName: "Calculated_Measure",
    businessName: "Calculated Measure",
    type: "MEASURE",
    subtype: "AnalyticModelMeasureType.CalculatedMeasure",
    dependentBy: {},
    dependencies: {
      "MEASURE.Quantity": true,
    },
    icon: "sap-icon://sac/formula",
  },
  "MEASURE.Restricted_Measure1": {
    technicalName: "Restricted_Measure1",
    businessName: "Restricted Measure1",
    type: "MEASURE",
    subtype: "AnalyticModelMeasureType.RestrictedMeasure",
    dependentBy: {},
    dependencies: {
      "MEASURE.Quantity": true,
      "ATTRIBUTE.SendingCostCenter": true,
    },
    icon: "sap-icon://sac/measure-filter",
  },
  "MEASURE.Non_Cumulative_Measure": {
    technicalName: "Non_Cumulative_Measure",
    businessName: "Non Cumulative Measure",
    type: "MEASURE",
    subtype: "AnalyticModelMeasureType.NonCumulativeMeasure",
    dependentBy: {},
    dependencies: {
      "MEASURE.Quantity": true,
      "ATTRIBUTE.ReceivingCostCenter": true,
      "ATTRIBUTE.Calculated_1": true,
    },
    icon: "sap-icon://sac/formula",
  },
  "MEASURE.Value": {
    technicalName: "Value",
    businessName: "Value",
    type: "MEASURE",
    subtype: "AnalyticModelMeasureType.FactSourceMeasure",
    dependentBy: {
      "MEASURE.Restricted_Measure": true,
      "MEASURE.Currency_Conversion_Measure": true,
      "MEASURE.Currency_Conversion_Measure1": true,
    },
    dependencies: {},
    icon: "sap-icon://sac/measure",
  },
  "MEASURE.Restricted_Measure": {
    technicalName: "Restricted_Measure",
    businessName: "Restricted Measure",
    type: "MEASURE",
    subtype: "AnalyticModelMeasureType.RestrictedMeasure",
    dependentBy: {},
    dependencies: {
      "MEASURE.Value": true,
      "ATTRIBUTE.Unit": true,
      "VARIABLE.RESTRICTED_MEASURE_VARIABLE_1": true,
    },
    icon: "sap-icon://sac/measure-filter",
  },
  "MEASURE.Currency_Conversion_Measure": {
    technicalName: "Currency_Conversion_Measure",
    businessName: "Currency Conversion Measure",
    type: "MEASURE",
    subtype: "AnalyticModelMeasureType.CurrencyConversionMeasure",
    dependentBy: {},
    dependencies: {
      "MEASURE.Value": true,
      "ATTRIBUTE.Opportunity": true,
      "ATTRIBUTE.Currency": true,
      "ATTRIBUTE.Calculated_2": true,
    },
    icon: "sap-icon://lead",
  },
  "MEASURE.Currency_Conversion_Measure1": {
    technicalName: "Currency_Conversion_Measure1",
    businessName: "Currency Conversion Measure1",
    type: "MEASURE",
    subtype: "AnalyticModelMeasureType.CurrencyConversionMeasure",
    dependentBy: {},
    dependencies: {
      "MEASURE.Value": true,
      "VARIABLE.TARGET_CURRENCY_1": true,
      "VARIABLE.REFERENCE_DATE_1": true,
      "VARIABLE.EXCHANGE_RATE_TYPE_1": true,
    },
    icon: "sap-icon://lead",
  },
  "MEASURE.Calculated_Measure1": {
    technicalName: "Calculated_Measure1",
    businessName: "Calculated Measure1",
    type: "MEASURE",
    subtype: "AnalyticModelMeasureType.CalculatedMeasure",
    dependentBy: {},
    dependencies: {
      "ATTRIBUTE.FactAttr": true,
    },
    icon: "sap-icon://sac/formula",
  },
  "MEASURE.Calculated_Measure2": {
    technicalName: "Calculated_Measure2",
    businessName: "Calculated Measure2",
    type: "MEASURE",
    subtype: "AnalyticModelMeasureType.CalculatedMeasure",
    dependentBy: {},
    dependencies: {
      "VARIABLE.PARAM_2": true,
    },
    icon: "sap-icon://sac/formula",
  },
  "MEASURE.Count_Distinct_Measure": {
    technicalName: "Count_Distinct_Measure",
    businessName: "Count Distinct Measure",
    type: "MEASURE",
    subtype: "AnalyticModelMeasureType.CountDistinct",
    dependentBy: {},
    dependencies: {
      "ATTRIBUTE.ItemStatus": true,
    },
    icon: "sap-icon://sac/formula",
  },
  "MEASURE.Calculated_Measure3": {
    technicalName: "Calculated_Measure3",
    businessName: "Calculated Measure3",
    type: "MEASURE",
    subtype: "AnalyticModelMeasureType.CalculatedMeasure",
    dependentBy: {},
    dependencies: {
      "ATTRIBUTE.ControllingArea": true,
    },
    icon: "sap-icon://sac/formula",
  },
  "ATTRIBUTE.ItemID": {
    technicalName: "ItemID",
    businessName: "ItemID",
    type: "ATTRIBUTE",
    subtype: "",
    dependentBy: {
      "FILTER.Filter": true,
    },
    dependencies: {},
    icon: "",
  },
  "FILTER.Filter": {
    technicalName: "Filter",
    businessName: "Filter",
    type: "FILTER",
    subtype: "",
    dependentBy: {},
    dependencies: {
      "ATTRIBUTE.ItemID": true,
    },
    icon: "sap-icon://sac/filter",
  },
  "ATTRIBUTE.Opportunity": {
    technicalName: "Opportunity",
    businessName: "Opportunity",
    type: "ATTRIBUTE",
    subtype: "",
    dependentBy: {
      "MEASURE.Currency_Conversion_Measure": true,
      "DAC.DAC_Values_Multiple": true,
      "CROSS_CALCULATION.RESTRICTED_CC": true,
    },
    dependencies: {},
    icon: "",
  },
  "DAC.DAC_Values_Multiple": {
    technicalName: "DAC_Values_Multiple",
    businessName: "DAC Values Multiple",
    type: "DAC",
    subtype: "",
    dependentBy: {},
    dependencies: {
      "ATTRIBUTE.Opportunity": true,
      "ATTRIBUTE.Product": true,
    },
    icon: "sap-icon://permission",
  },
  "CROSS_CALCULATION.RESTRICTED_CC": {
    technicalName: "RESTRICTED_CC",
    businessName: "Restricted Cross Calculation",
    type: "CROSS_CALCULATION",
    subtype: "AnalyticModelCrossCalculationType.RestrictedCrossCalculation",
    dependentBy: {},
    dependencies: {
      "ATTRIBUTE.Opportunity": true,
    },
    icon: "sap-icon://sac/measure-filter",
  },
  "ATTRIBUTE.Product": {
    technicalName: "Product",
    businessName: "Product",
    type: "ATTRIBUTE",
    subtype: "",
    dependentBy: {
      "DAC.DAC_Values_Multiple": true,
    },
    dependencies: {},
    icon: "",
  },
  "ATTRIBUTE.Unit": {
    technicalName: "Unit",
    businessName: "Unit",
    type: "ATTRIBUTE",
    subtype: "",
    dependentBy: {
      "MEASURE.Restricted_Measure": true,
      "VARIABLE.RESTRICTED_MEASURE_VARIABLE_1": true,
    },
    dependencies: {},
    icon: "",
  },
  "VARIABLE.RESTRICTED_MEASURE_VARIABLE_1": {
    technicalName: "RESTRICTED_MEASURE_VARIABLE_1",
    businessName: "Restricted Measure Variable 1",
    type: "VARIABLE",
    subtype: "AnalyticModelParameterType.Filter",
    dependentBy: {
      "MEASURE.Restricted_Measure": true,
    },
    dependencies: {
      "ATTRIBUTE.Unit": true,
    },
    icon: "sap-icon://sac/variable",
  },
  "ATTRIBUTE.Currency": {
    technicalName: "Currency",
    businessName: "Currency",
    type: "ATTRIBUTE",
    subtype: "",
    dependentBy: {
      "MEASURE.Currency_Conversion_Measure": true,
      "VARIABLE.Currency": true,
    },
    dependencies: {},
    icon: "",
  },
  "VARIABLE.Currency": {
    technicalName: "Currency",
    businessName: "Currency",
    type: "VARIABLE",
    subtype: "AnalyticModelParameterType.StoryFilter",
    dependentBy: {},
    dependencies: {
      "ATTRIBUTE.Currency": true,
    },
    icon: "sap-icon://sac/variable",
  },
  "ATTRIBUTE.FactAttr": {
    technicalName: "FactAttr",
    businessName: "FactAttr",
    type: "ATTRIBUTE",
    subtype: "",
    dependentBy: {
      "MEASURE.Calculated_Measure1": true,
    },
    dependencies: {},
    icon: "",
  },
  "ATTRIBUTE.ItemStatus": {
    technicalName: "ItemStatus",
    businessName: "ItemStatus ID",
    type: "ATTRIBUTE",
    subtype: "",
    dependentBy: {
      "MEASURE.Count_Distinct_Measure": true,
    },
    dependencies: {},
    icon: "",
  },
  "ATTRIBUTE.ControllingArea": {
    technicalName: "ControllingArea",
    businessName: "Controlling Area",
    type: "ATTRIBUTE",
    subtype: "",
    dependentBy: {
      "MEASURE.Calculated_Measure3": true,
    },
    dependencies: {},
    icon: "",
  },
  "ATTRIBUTE.SendingCostCenter": {
    technicalName: "SendingCostCenter",
    businessName: "Sending Cost Center",
    type: "ATTRIBUTE",
    subtype: "",
    dependentBy: {
      "MEASURE.Restricted_Measure1": true,
    },
    dependencies: {},
    icon: "",
  },
  "ATTRIBUTE.ReceivingCostCenter": {
    technicalName: "ReceivingCostCenter",
    businessName: "Receiving Cost Center",
    type: "ATTRIBUTE",
    subtype: "",
    dependentBy: {
      "MEASURE.Non_Cumulative_Measure": true,
    },
    dependencies: {},
    icon: "",
  },
  "ATTRIBUTE.Calculated_1": {
    technicalName: "Calculated_1",
    businessName: "Calculated_1",
    type: "ATTRIBUTE",
    subtype: "",
    dependentBy: {
      "MEASURE.Non_Cumulative_Measure": true,
    },
    dependencies: {},
    icon: "",
  },
  "ATTRIBUTE.YEAR_Calculated_1": {
    technicalName: "YEAR_Calculated_1",
    businessName: "Year",
    type: "ATTRIBUTE",
    subtype: "",
    dependentBy: {},
    dependencies: {},
    icon: "",
  },
  "ATTRIBUTE.CALQUARTER_Calculated_1": {
    technicalName: "CALQUARTER_Calculated_1",
    businessName: "Calendar Quarter",
    type: "ATTRIBUTE",
    subtype: "",
    dependentBy: {},
    dependencies: {},
    icon: "",
  },
  "ATTRIBUTE.CALMONTH_Calculated_1": {
    technicalName: "CALMONTH_Calculated_1",
    businessName: "Calendar Month",
    type: "ATTRIBUTE",
    subtype: "",
    dependentBy: {},
    dependencies: {},
    icon: "",
  },
  "ATTRIBUTE.CALWEEK_Calculated_1": {
    technicalName: "CALWEEK_Calculated_1",
    businessName: "Calendar Week",
    type: "ATTRIBUTE",
    subtype: "",
    dependentBy: {},
    dependencies: {},
    icon: "",
  },
  "ATTRIBUTE.Calculated_2": {
    technicalName: "Calculated_2",
    businessName: "Calculated_2",
    type: "ATTRIBUTE",
    subtype: "",
    dependentBy: {
      "MEASURE.Currency_Conversion_Measure": true,
    },
    dependencies: {},
    icon: "",
  },
  "VARIABLE.PARAM_1": {
    technicalName: "PARAM_1",
    businessName: "PARAM_1",
    type: "VARIABLE",
    subtype: "",
    dependentBy: {
      "FACT.GC_VIEW_PARAMS": true,
    },
    dependencies: {
      "VARIABLE.COUNTRY": true,
    },
    icon: "sap-icon://sac/variable",
  },
  "FACT.GC_VIEW_PARAMS": {
    technicalName: "GC_VIEW_PARAMS",
    businessName: "PARAM_1",
    type: "FACT",
    subtype: "AnalyticModelSourceParameterMappingType.MapToSourceParameter",
    dependentBy: {},
    dependencies: {
      "VARIABLE.PARAM_1": true,
    },
    icon: "sap-icon://sac/fact-view",
  },
  "VARIABLE.COUNTRY": {
    technicalName: "COUNTRY",
    businessName: "Country",
    type: "VARIABLE",
    subtype: "",
    dependentBy: {
      "VARIABLE.PARAM_1": true,
    },
    dependencies: {},
    icon: "sap-icon://sac/variable",
  },
  "VARIABLE.PARAM_2": {
    technicalName: "PARAM_2",
    businessName: "Standard Variable 1",
    type: "VARIABLE",
    subtype: "",
    dependentBy: {
      "MEASURE.Calculated_Measure2": true,
    },
    dependencies: {},
    icon: "sap-icon://sac/variable",
  },
  "VARIABLE.TARGET_CURRENCY_1": {
    technicalName: "TARGET_CURRENCY_1",
    businessName: "Target Currency 1",
    type: "VARIABLE",
    subtype: "",
    dependentBy: {
      "MEASURE.Currency_Conversion_Measure1": true,
    },
    dependencies: {},
    icon: "sap-icon://sac/variable",
  },
  "VARIABLE.REFERENCE_DATE_1": {
    technicalName: "REFERENCE_DATE_1",
    businessName: "Reference Date 1",
    type: "VARIABLE",
    subtype: "",
    dependentBy: {
      "MEASURE.Currency_Conversion_Measure1": true,
    },
    dependencies: {},
    icon: "sap-icon://sac/variable",
  },
  "VARIABLE.EXCHANGE_RATE_TYPE_1": {
    technicalName: "EXCHANGE_RATE_TYPE_1",
    businessName: "Exchange Rate Type 1",
    type: "VARIABLE",
    subtype: "",
    dependentBy: {
      "MEASURE.Currency_Conversion_Measure1": true,
    },
    dependencies: {},
    icon: "sap-icon://sac/variable",
  },
  "CROSS_CALCULATION.CALCULATED_CC": {
    technicalName: "CALCULATED_CC",
    businessName: "Calculated Cross Calculation",
    type: "CROSS_CALCULATION",
    subtype: "AnalyticModelCrossCalculationType.CalculatedCrossCalculation",
    dependentBy: {},
    dependencies: {},
    icon: "sap-icon://sac/formula",
  },
};
