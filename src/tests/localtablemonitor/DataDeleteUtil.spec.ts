/** @format */

import { expect } from "chai";
import { CDSDataType } from "../../components/commonmodel/model/types/cds.types";
import { DATE_FILTERS, DATE_TIME_FILTERS } from "../../components/localtablemonitor/Utils/Constants";
import {
  DataType,
  Operator,
  calculateDynamicTodayValues,
  createDateObjectFromAPIResponse,
  createFilterModel,
  getBooleanValueForMDM,
  getDDRSummaryOperatorString,
  getDDRValueString,
  getDateDifference,
  getFilterOperator,
  getFilterText,
  getFilterValueForUIControl,
  getFormattedDate,
  getHighValueForMDM,
  getItemType,
  getLowValueForMDM,
  getSelectedDate,
  getSummaryFilterValueStringDDR,
  getSupportOperators,
  getUnsupportedDataTypesForFilter,
  getValueForFiltersHigh,
  getValueForFiltersLow,
  getValuesForMDM,
  isDateTimeTypeField,
  isDynamicDateTimeFilter,
  isRangeDateTileFilter,
  returnDynamicDateTimeFilters,
} from "../../components/localtablemonitor/Utils/DataDeleteUtil";

describe("src/components/localtablemonitor/Utils/DataDeleteUtil.ts", () => {
  /**
   * @issueid DW101-80812
   */
  describe("test getFilterText", () => {
    it("should return the correct filter text for sap.ui.model.FilterOperator.StartsWith", () => {
      const item = { option: Operator.StartsWith, dataType: DataType.STRING };
      expect(getFilterText(item, "test", [], false, false)).to.equal("test*");
    });

    it("should return the correct filter text for sap.ui.model.FilterOperator.EndsWith", () => {
      const item = { option: Operator.EndsWith, dataType: DataType.STRING };
      expect(getFilterText(item, "test", [], false, false)).to.equal("*test");
    });

    it("should return the correct filter text for sap.ui.model.FilterOperator.BT", () => {
      const item = { option: Operator.BT, dataType: DataType.STRING };
      expect(getFilterText(item, ["test1", "test2"], [], false, false)).to.equal(">=test1 and <=test2");
    });

    it("should return the correct filter text for sap.ui.model.FilterOperator.Contains", () => {
      const item = { option: Operator.Contains, dataType: DataType.STRING };
      expect(getFilterText(item, "test", [], false, false)).to.equal("*test*");
    });

    it("should return the correct filter text for Operator.BXD", () => {
      const item = { option: Operator.BXD, dataType: DataType.DATE };
      expect(getFilterText(item, ["1"], [], false, false)).to.contains("<=");
    });

    it("should return the correct filter text for Operator.Empty", () => {
      const item = { option: Operator.Empty, dataType: DataType.DEFAULT };
      expect(getFilterText(item, "", [], false, false)).to.equal("<empty>");
    });

    it("should return the correct filter text for default case", () => {
      const item = { option: Operator.EQ, dataType: DataType.STRING };
      expect(getFilterText(item, "test", [], false, false)).to.equal("=test");
    });

    describe("getFilterText dynamic date range cases", () => {
      it("should return correct filter text for DATERANGE with operands present", () => {
        // arrange
        const item = { option: "DATERANGE", dataType: DataType.DATE };
        const operand = [new Date("2024-01-09"), new Date("2025-08-31")].map((d) => d.toISOString().split("T")[0]);
        // act
        const result = getFilterText(item, operand, [], true, true);
        // assert
        expect(result).to.be.a("string");
        expect(result.length).to.be.greaterThan(0);
        // check if the two dates ranges are present irrespective of the format/order
        expect(result).to.include("31");
        expect(result).to.include("09");
      });
    });
  });

  /**
   * @issueid DW101-80711
   *
   */
  describe("test getSelectedDate", () => {
    it("should return the date part only", () => {
      expect(getSelectedDate(5, true)).to.match(/\d{4}-\d{2}-\d{2}/);
    });

    it("should return the correct date string for a given number of days when isDate is true", () => {
      const days = 5;
      const currentDate = new Date();
      const expectedDate = new Date(currentDate);
      expectedDate.setDate(currentDate.getDate() - days);
      const expectedDateString = expectedDate.toISOString().split("T")[0];

      expect(getSelectedDate(days, true)).to.equal(expectedDateString);
    });

    it("should return the correct datetime string for a given number of days when isDate is false", () => {
      const days = 5;
      const currentDate = new Date();
      const expectedDate = new Date(currentDate);
      expectedDate.setDate(currentDate.getDate() - days);
      const expectedDateTimeString = expectedDate.toISOString().split(".")[0];

      expect(getSelectedDate(days, false)).to.equal(expectedDateTimeString);
    });

    it("should handle edge cases correctly", () => {
      const days = 0;
      const currentDate = new Date();
      const expectedDate = new Date(currentDate);
      expectedDate.setDate(currentDate.getDate() - days);
      const expectedDateString = expectedDate.toISOString().split("T")[0];
      const expectedDateTimeString = expectedDate.toISOString().split(".")[0];

      expect(getSelectedDate(days, true)).to.equal(expectedDateString);
      expect(getSelectedDate(days, false)).to.equal(expectedDateTimeString);
    });

    it("should handle negative days correctly", () => {
      const days = -5;
      const currentDate = new Date();
      const expectedDate = new Date(currentDate);
      expectedDate.setDate(currentDate.getDate() - days);
      const expectedDateString = expectedDate.toISOString().split("T")[0];
      const expectedDateTimeString = expectedDate.toISOString().split(".")[0];

      expect(getSelectedDate(days, true)).to.equal(expectedDateString);
      expect(getSelectedDate(days, false)).to.equal(expectedDateTimeString);
    });
  });

  /**
   * @issueid DW101-81087
   */
  //

  describe("test getSupportOperators", () => {
    it("should return the correct operators for string data type", () => {
      expect(getSupportOperators(DataType.STRING)).to.deep.equal([
        Operator.EQ,
        Operator.BT,
        Operator.Contains,
        Operator.StartsWith,
        Operator.EndsWith,
        Operator.LT,
        Operator.LE,
        Operator.GT,
        Operator.GE,
        Operator.Empty,
      ]);
    });

    it("should return the correct operators for string data type with cdcType MODE", () => {
      expect(getSupportOperators(DataType.STRING, "MODE")).to.deep.equal([Operator.EQ]);
    });

    it("should return the correct operators for date data type", () => {
      expect(getSupportOperators(DataType.DATE)).to.deep.equal([]);
    });

    it("should return the correct operators for boolean data type", () => {
      expect(getSupportOperators(DataType.BOOLEAN)).to.deep.equal([Operator.EQ, Operator.Empty]);
    });

    it("should return the correct operators for numeric data type", () => {
      expect(getSupportOperators(DataType.NUMERIC)).to.deep.equal([
        Operator.EQ,
        Operator.BT,
        Operator.LT,
        Operator.LE,
        Operator.GT,
        Operator.GE,
        Operator.Empty,
      ]);
    });

    it("should return the correct operators for default case", () => {
      expect(getSupportOperators("unknown")).to.deep.equal([
        Operator.EQ,
        Operator.BT,
        Operator.LT,
        Operator.LE,
        Operator.GT,
        Operator.GE,
        Operator.Empty,
      ]);
    });
  });

  /**
   * @issueid DW101-81057, DW101-83530
   */
  //
  describe("test getUnsupportedDataTypesForFilter", () => {
    it("should return false for LargeString", () => {
      expect(getUnsupportedDataTypesForFilter({ type: CDSDataType.LARGE_STRING })).to.equal(false);
    });

    it("should return false for Geometry", () => {
      expect(getUnsupportedDataTypesForFilter({ type: CDSDataType.HANA_ST_GEOMETRY })).to.equal(false);
    });

    it("should return false for Binary", () => {
      expect(getUnsupportedDataTypesForFilter({ type: CDSDataType.BINARY })).to.equal(false);
    });

    it("should return false for LargeBinary", () => {
      expect(getUnsupportedDataTypesForFilter({ type: CDSDataType.LARGE_BINARY })).to.equal(false);
    });
    it("should return false for HanaBinary", () => {
      expect(getUnsupportedDataTypesForFilter({ type: CDSDataType.HANA_BINARY })).to.equal(false);
    });
  });

  describe("test getBooleanValueForMDM", () => {
    it("should return true for valid truthy values", () => {
      expect(getBooleanValueForMDM("TRUE")).to.equal("Yes");
    });

    it("should return false for valid falsy values", () => {
      expect(getBooleanValueForMDM("FALSE")).to.equal("No");
    });

    it("should return false for invalid values", () => {
      expect(getBooleanValueForMDM("")).to.equal(null);
    });
  });

  /**
   * @issueid DW101-86360, DW101-81044
   */
  //

  describe("test getValuesForMDM", () => {
    it("should return the correct date string when option is Operator.BXD and dataType is CDSDataType.DATE", () => {
      const item = { option: Operator.BXD, low: 5 };
      const dataType = CDSDataType.DATE;
      const expectedDate = getSelectedDate(item.low, true);

      expect(getValuesForMDM(item, dataType, "low", false)).to.equal(expectedDate);
    });

    it("should return the correct boolean value when dataType is CDSDataType.BOOLEAN", () => {
      const item = { option: "EQ", low: "TRUE" };
      const dataType = CDSDataType.BOOLEAN;
      const expectedBooleanValue = "Yes"; // Adjust based on actual implementation of getBooleanValueForMDM

      expect(getValuesForMDM(item, dataType, "low", false)).to.equal(expectedBooleanValue);
    });

    it("should return the low value when dataType is not CDSDataType.BOOLEAN and option is not Operator.BXD", () => {
      const item = { option: "someOption", low: "someValue" };
      const dataType = "someOtherDataType";

      expect(getValuesForMDM(item, dataType, "low", false)).to.equal(item.low);
    });

    it("should return formatted range value for range type filters", () => {
      const item = { option: "TODAYFROMTO", low: "1", high: "2" };
      const dataType = CDSDataType.DATE;
      // const expectedRangeValue = "2024-01-01 to 2024-01-31";

      const lowValue = getValuesForMDM(item, dataType, "low", true, true);
      const highValue = getValuesForMDM(item, dataType, "high", true, true);

      expect(lowValue).to.equal("1");
      expect(highValue).to.equal("2");
    });
  });
});

describe("test dynamicDateRange methods", () => {
  describe("test dynamicDateRange filter mapping", () => {
    it("should return only date type when dynamicDateRange[DWCO_LTA_DYNA_DATE_SELECT] feature is disabled", () => {
      // arrange
      const dataType = ["cds.Date", "cds.Time", "cds.TimeOfDay", "cds.Timestamp", "cds.DateTime", "cds.DateTimeOffset"];
      // act
      const mappedDataType = new Set(dataType.map((type) => getItemType(type, false)));
      // assert
      expect(mappedDataType).to.deep.equal(new Set([DataType.DATE]));
    });

    it("should return the different date types when dynamicDateRange[DWCO_LTA_DYNA_DATE_SELECT] feature is enabled", () => {
      // arrange
      const dataType = ["cds.Date", "cds.Timestamp", "cds.DateTime"];
      // act
      const mappedDataType = new Set(dataType.map((type) => getItemType(type, true)));
      // assert
      expect(mappedDataType).to.deep.equal(new Set([DataType.DATE, DataType.TIMESTAMP, DataType.DATETIME]));
    });

    it("should return true when datatype is 'date', 'datetime', 'timestamp'", () => {
      // arrange
      const dataType = [DataType.DATE, DataType.TIMESTAMP, DataType.DATETIME];
      // act & assert
      dataType.forEach((type) => {
        expect(isDateTimeTypeField(type)).to.deep.equal(true);
      });
    });

    it("should return dynamic datetime operators based on the column type", () => {
      // arrange
      const dataType = DataType.DATETIME;
      // act
      const dynamicDateFilters = returnDynamicDateTimeFilters(dataType);
      // assert
      expect(dynamicDateFilters).to.deep.equal([...DATE_FILTERS, ...DATE_TIME_FILTERS]);
    });

    it("should return date filters based on the column type", () => {
      // arrange
      const dataType = DataType.DATE;
      // act
      const dynamicDateFilters = returnDynamicDateTimeFilters(dataType);
      // assert
      expect(dynamicDateFilters).to.deep.equal(DATE_FILTERS);
    });

    it("should check rangeTypeFilters", () => {
      // arrange
      const filters = ["TODAYFROMTO", "FROM"];
      // act
      const result = filters.map((filter) => isRangeDateTileFilter(filter));
      // assert
      expect(result).to.deep.equal([true, false]);
    });

    it("should check todayTypeFilters", () => {
      // arrange
      const filters = ["FROM", "TODAYFROMTO"];
      // act
      const result = filters.map((filter) => isRangeDateTileFilter(filter));
      // assert
      expect(result).to.deep.equal([false, true]);
    });

    it("should check for isDynamicDateTimeFilter", () => {
      // arrange
      const filters = ["TODAYFROMTO", "FROM", "YEARTODATE"];
      // act
      const result = filters.map((filter) => isDynamicDateTimeFilter(filter));
      // assert
      expect(result).to.deep.equal([true, false, true]);
    });
  });

  describe("test getFilterOperator", () => {
    it("should return GT for FROM", () => {
      expect(getFilterOperator({ dataType: CDSDataType.DATE }, "FROM", true)).to.equal(Operator.GT);
    });
    it("should return GT for FROMDATETIME", () => {
      expect(getFilterOperator({ dataType: CDSDataType.DATE }, "FROMDATETIME", true)).to.equal(Operator.GT);
    });
    it("should return LT for TO", () => {
      expect(getFilterOperator({ dataType: CDSDataType.DATE }, "TO", true)).to.equal(Operator.LT);
    });
    it("should return LT for TODATETIME", () => {
      expect(getFilterOperator({ dataType: CDSDataType.DATE }, "TODATETIME", true)).to.equal(Operator.LT);
    });
    it("should return BT for unknown dynamic", () => {
      expect(getFilterOperator({ dataType: CDSDataType.DATE }, "TODAYFROMTO", true)).to.equal(Operator.BT);
    });
    it("should return EQ for DATE and dataType DATE", () => {
      expect(getFilterOperator({ dataType: CDSDataType.DATE }, "DATE", true)).to.equal(Operator.EQ);
    });
    it("should return option for non-dynamic", () => {
      expect(getFilterOperator({ dataType: CDSDataType.DATE }, "EQ", false)).to.equal("EQ");
    });
    it("should return BT operator for DATE Filter with timestamp field", () => {
      const item = { dataType: CDSDataType.TIMESTAMP, option: "DATE" };
      expect(getFilterOperator(item, "DATE", true)).to.equal("BT");
    });
    it("should return EQ operator for DATE filter with time/date field", () => {
      const item = { dataType: CDSDataType.DATE, option: "DATE" };
      expect(getFilterOperator(item, "DATE", false)).to.equal("EQ");
    });
  });

  describe("test formattedDate", () => {
    it("should return formatted date string for valid date", () => {
      const item = { value1: new Date("2024-01-01T00:00:00Z") };
      const expectedString = "2024-01-01";
      const formattedDate = getFormattedDate(item, "value1", true);
      expect(formattedDate).to.equal(expectedString);
    });
    it("should return formatted datetime string for valid datetime", () => {
      const item = { value1: new Date("2024-01-01T12:00:00Z") };
      const expectedString = "2024-01-01T12:00:00";
      const formattedDate = getFormattedDate(item, "value1", false);
      expect(formattedDate).to.equal(expectedString);
    });
  });

  describe("test calculateDynamicTodayValues", () => {
    it("should set value high to end of current day for YEARTODATE filter", () => {
      const today = new Date();
      const endOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59, 999);
      const value = { low: { oDate: new Date("2024-01-01") } };
      const result = calculateDynamicTodayValues("YEARTODATE", "high", value);
      expect(result.oDate).to.be.instanceOf(Date);
      expect(result.oDate).to.deep.equal(endOfToday);
    });

    it("should set value low to end of current day for DATETOYEAR filter", () => {
      const today = new Date();
      const endOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59, 999);
      const values = { high: { oDate: new Date("2024-01-01") } };
      const result = calculateDynamicTodayValues("DATETOYEAR", "low", values);
      expect(result.oDate).to.be.instanceOf(Date);
      expect(result.oDate).to.deep.equal(endOfToday);
    });
    it("should return values[objectKey] for other cases", () => {
      const values = { low: "rangeValue" };
      const result = calculateDynamicTodayValues("OTHER", "low", values);
      expect(result).to.equal("rangeValue");
    });
  });

  describe("test createDateObjectFromAPIResponse", () => {
    it("should return a date type field of all date type filters", () => {
      const dateFilters = DATE_FILTERS.reduce((acc, filter) => {
        if (!isDynamicDateTimeFilter(filter)) {
          const response = createDateObjectFromAPIResponse({ low: "2024-01-01" }, filter, "low", true);
          acc.push(response);
        }
        return acc;
      }, []);
      dateFilters.forEach((date) => {
        expect(date).to.be.instanceOf(Date);
      });
    });

    it("should set the low value to current date for DATETOYEAR filter", () => {
      const value = { low: new Date("2024-01-01"), high: new Date("2024-12-31") };

      const result = createDateObjectFromAPIResponse(value, "DATETOYEAR", "low", true);
      const today = new Date();
      const endOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59, 999);
      expect(result.oDate).to.be.instanceOf(Date);
      expect(result.oDate).to.deep.equal(endOfToday);
    });

    it("should set the high value to current date for YEARTODATE filter", () => {
      const value = { low: new Date("2024-01-01"), high: new Date("2024-04-20") };
      const result = createDateObjectFromAPIResponse(value, "YEARTODATE", "high", true);
      const today = new Date();
      const endOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59, 999);
      expect(result.oDate).to.be.instanceOf(Date);
      expect(result.oDate).to.deep.equal(endOfToday);
    });
  });

  describe("test createFilterModel", () => {
    it("should create filter model from filters and columnInfo", () => {
      const filters = { col1: { val: [{ option: "EQ", low: "a", high: "b" }] } };
      const columnInfo = [
        {
          name: "col1",
          length: 10,
          isKeyColumn: false,
          precision: 0,
          scale: 0,
          type: "string",
          cdcType: undefined,
          dataType: "cds.String",
        },
      ];
      const i18nModel = { getProperty: () => "" };
      const result = createFilterModel(filters, columnInfo, i18nModel, false);
      expect(result).to.be.an("array");
      expect(result[0].operation).to.equal("EQ");
      expect(result[0].value1).to.equal("a");
      expect(result[0].value2).to.equal("b");
    });
  });

  describe("test getDateDifference", () => {
    it("should return correct day difference", () => {
      const start = "2024-01-01T00:00:00Z";
      const end = "2024-01-05T00:00:00Z";
      expect(getDateDifference(start, end)).to.equal(4);
    });
  });

  describe("test getLowValueForMDM and getHighValueForMDM", () => {
    it("should return item.low and item.high if not dynamic date ranges", () => {
      const item = { low: "1", high: "2" };
      expect(getLowValueForMDM(item, false, false)).to.equal("1");
      expect(getHighValueForMDM(item, false, false)).to.equal("2");
    });

    it("should return ISO date string for date fields", () => {
      const isoString1 = "2024-01-01T00:00:00Z";
      const isoString2 = "2024-01-02T00:00:00Z";

      const item = {
        dataType: CDSDataType.DATE,
        low: new Date(isoString1),
        high: new Date(isoString2),
        option: "DATERANGE",
        filter: "DATERANGE",
      };

      const lowValue = getLowValueForMDM(item, true, true);
      const highValue = getHighValueForMDM(item, true, true);

      expect(lowValue).to.equal(isoString1.split("T")[0]);
      expect(highValue).to.equal(isoString2.split("T")[0]);
    });

    it("should return datetime formatted ISO string value for DATETIME fields", () => {
      const isoString1 = "2024-01-01T12:00:00Z";
      const isoString2 = "2024-01-02T12:00:00Z";
      const expectedISOString1 = "2024-01-01T12:00:00";
      const expectedISOString2 = "2024-01-02T12:00:00";

      const item = {
        dataType: CDSDataType.DATETIME,
        low: new Date(isoString1),
        high: new Date(isoString2),
        option: "DATERANGE",
        filter: "DATERANGE",
      };

      const lowValue = getLowValueForMDM(item, true, true);
      const highValue = getHighValueForMDM(item, true, true);

      expect(lowValue).to.equal(expectedISOString1);
      expect(highValue).to.equal(expectedISOString2);
    });

    it('should return correct values for DATE field with cdsType "DATE"', () => {
      const isoString1 = "2024-01-01T12:00:00Z";
      const isoString2 = "2024-01-02T12:00:00Z";

      const item = {
        dataType: CDSDataType.DATE,
        low: new Date(isoString1),
        high: new Date(isoString2),
        option: "DATE",
        filter: "DATE",
      };
      const lowValue = getLowValueForMDM(item, true, false);
      const highValue = getHighValueForMDM(item, true, false);
      console.log("Low value - high value", lowValue, highValue);
      expect(lowValue).to.equal("2024-01-01");
      expect(highValue).to.equal(undefined);
    });
  });

  describe("test getValueForFiltersLow and getValueForFiltersHigh", () => {
    it("should return item.value1 and item.value2 if not dynamic", () => {
      const item = { value1: "v1", value2: "v2", dataType: "string" };
      expect(getValueForFiltersLow(item, false, false)).to.equal("v1");
      expect(getValueForFiltersHigh(item, false, false)).to.equal("v2");
    });
  });

  describe("test getFilterValueForUIControl", () => {
    it("should return correct structure for BXD", () => {
      const result = getFilterValueForUIControl(Operator.BXD, "5", undefined, undefined, undefined);
      expect(result).to.deep.equal({ values: ["5"], operator: Operator.BXD });
    });
    it("should return correct structure for range", () => {
      const result = getFilterValueForUIControl("TODAYFROMTO", undefined, undefined, "2024-01-01", "2024-01-02");
      expect(result).to.deep.equal({ values: ["2024-01-01", "2024-01-02"], operator: "TODAYFROMTO" });
    });
    it("should return correct structure for value1/value2", () => {
      const result = getFilterValueForUIControl("EQ", "a", "b", undefined, undefined);
      expect(result).to.deep.equal({ values: ["a", "b"], operator: "EQ" });
    });
  });

  describe("getDDRSummaryOperatorString", () => {
    it("should return the correct summary string for known operator", () => {
      // arrange
      const operator = "FROM"; // assuming FROM is a valid DDR operator
      const ddr = new (window as any).sap.m.DynamicDateRange();
      const expected = ddr.getOption(operator).getText(ddr);
      // act
      const result = getDDRSummaryOperatorString(operator);
      // assert
      expect(result).to.equal(expected);
    });

    it("should return the correct summary string for another known operator", () => {
      // arrange
      const operator = "LASTDAYS"; // assuming LASTDAYS is a valid DDR operator
      const ddr = new (window as any).sap.m.DynamicDateRange();
      const expected = ddr.getOption(operator).getText(ddr);
      // act
      const result = getDDRSummaryOperatorString(operator);
      // assert
      expect(result).to.equal(expected);
    });
  });

  describe("getDDRValueString", () => {
    it("should return a formatted value string for a known operator and single value", () => {
      // arrange
      const operator = "LASTDAYS";
      const value = [3];
      // act
      const result = getDDRValueString(operator, value);
      // assert
      expect(result).to.be.a("string");
      expect(result.length).to.be.greaterThan(0);
    });

    it("should return a formatted value string for a known operator and value range", () => {
      // arrange
      const operator = "DATERANGE";
      const value = [new Date("2024-01-01"), new Date("2024-01-31")];
      // act
      const result = getDDRValueString(operator, value);
      // assert
      expect(result).to.be.a("string");
      expect(result.length).to.be.greaterThan(0);
    });
  });

  describe("getSummaryFilterValueString", () => {
    it("should return a summary string for FROM operation", () => {
      // arrange
      const item = { value1: new Date("2024-01-01"), value2: undefined };
      const index = 1;
      const operation = "FROM";
      // act
      const result = getSummaryFilterValueStringDDR(item, index, operation);
      // assert
      expect(result).to.be.a("string");
      expect(result.length).to.be.greaterThan(0);
    });

    it("should return a summary string for DATERANGE operation with two values", () => {
      // arrange
      const item = { value1: new Date("2024-01-01"), value2: new Date("2024-01-31") };
      const index = 1;
      const operation = "DATERANGE";
      // act
      const result = getSummaryFilterValueStringDDR(item, index, operation);
      // assert
      expect(result).to.be.a("string");
      expect(result.length).to.be.greaterThan(0);
    });
  });
});
