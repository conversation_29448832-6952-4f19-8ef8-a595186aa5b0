/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import assert from "assert";
import sinon from "sinon";
import { updateCsnQueryAliases } from "../../components/commonmodel/csn/csnUtils";
import "../../components/ermodeler/js/transform/erCsnToModel";
import "../../components/ermodeler/js/transform/erModelToCsn";
import "../support/erModelLoader";

const nsQueryBuilder = sap.cdw.querybuilder as any;
const ErCsnToModel = sap.cdw.ermodeler.ErCsnToModel;
const ErModelToCsn = sap.cdw.ermodeler.ErModelToCsn;
describe("baseCsnToModel", () => {
  let sandbox: sinon.SinonSandbox;
  let erCsnToModel;
  let erModelToCsn;
  beforeEach(() => {
    sandbox = sinon.createSandbox();
    erCsnToModel = new ErCsnToModel();
    erModelToCsn = new ErModelToCsn();
  });
  afterEach(() => {
    sandbox.restore();
  });
  describe("Update CSN Query Aliases", () => {
    it("test updating csn query aliases", function () {
      const oInput = {
        SELECT: {
          from: {
            ref: ["CUSTOMERS", "EMPLOYEES"],
          },
          columns: [
            {
              xpr: [
                {
                  ref: ["ID"],
                },
                "+",
                {
                  val: 1,
                },
              ],
            },
            {
              val: 3,
            },
            {
              ref: ["CUSTOMERS", "NAME"],
            },
            {
              ref: ["EMPLOYEES", "NAME"],
            },
          ],
          where: [
            {
              ref: ["ID"],
            },
            "in",
            {
              SELECT: {
                from: {
                  ref: ["INVOICES"],
                },
                columns: [
                  {
                    xpr: [
                      {
                        ref: ["INVOICES", "ID"],
                      },
                      "+",
                      {
                        val: 2,
                      },
                    ],
                  },
                ],
                where: [
                  {
                    ref: ["INVOICES", "AMOUNT"],
                  },
                  ">",
                  {
                    val: 1000,
                  },
                ],
              },
            },
          ],
        },
      };
      const oExpectedResult = {
        SELECT: {
          from: {
            ref: ["CUSTOMERS", "EMPLOYEES"],
          },
          columns: [
            {
              xpr: [
                {
                  ref: ["ID"],
                },
                "+",
                {
                  val: 1,
                },
              ],
              as: "xpr1",
            },
            {
              val: 3,
              as: "val1",
            },
            {
              ref: ["CUSTOMERS", "NAME"],
            },
            {
              ref: ["EMPLOYEES", "NAME"],
              as: "NAME1",
            },
          ],
          where: [
            {
              ref: ["ID"],
            },
            "in",
            {
              SELECT: {
                from: {
                  ref: ["INVOICES"],
                },
                columns: [
                  {
                    xpr: [
                      {
                        ref: ["INVOICES", "ID"],
                      },
                      "+",
                      {
                        val: 2,
                      },
                    ],
                    as: "xpr1",
                  },
                ],
                where: [
                  {
                    ref: ["INVOICES", "AMOUNT"],
                  },
                  ">",
                  {
                    val: 1000,
                  },
                ],
              },
            },
          ],
        },
      };
      updateCsnQueryAliases(oInput);
      const sResult = JSON.stringify(oInput);
      assert.equal(sResult, JSON.stringify(oExpectedResult), "Update Aliases.");
    });
  });

  // Test reading/writing parameter annotations
  describe("Reading csn mapped properties for Parameter", () => {
    it("test updating csn query aliases", function () {
      const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
      const oOutput = oNewModel.output;
      const oParameter = sap.cdw.commonmodel.ModelImpl.createObject(
        "sap.cdw.commonmodel.Parameter",
        { name: "AT_DATE" },
        oOutput
      ) as any;
      const inputParamCsn = {
        "@EndUserText.label": "Business Date",
        type: "cds.Date",
        "@Semantics.businessDate.at": true,
        "@myOwnAnnotation": true,
      };
      const unknownAnnotations = { "@myOwnAnnotation": true };
      // Reading..
      erCsnToModel.getDefaultPropertyMapping(oParameter, inputParamCsn);
      assert.strictEqual(oParameter.label, "Business Date", "Label updated");
      assert.strictEqual(oParameter.semanticType, "@Semantics.businessDate.at", "Semantic type is read");
      assert.deepStrictEqual(oParameter.unhandledCsn, unknownAnnotations, "Unknown annotation saved");

      // Writing..
      const outputParamCsn = {};
      erModelToCsn.addDefaultPropertyMapping(outputParamCsn, oParameter);
      assert.strictEqual(outputParamCsn["@Semantics.businessDate.at"], true, "Semantic type is written");
    });
  });

  // Test getting annotations
  describe("Getting Handled annotations", () => {
    it("test getting object annotations", function () {
      const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
      const oOutput = oNewModel.output;

      // Table
      const oTable = sap.cdw.commonmodel.ModelImpl.createObject(
        "sap.cdw.commonmodel.Table",
        { name: "annotatedTable" },
        oOutput
      ) as any;
      const tableExpectedAnnotations = [
        "@Common.Label",
        "@EndUserText.label",
        "name",
        "alias",
        "doc",
        "@DataWarehouse.businessDefinition.description",
        "@DataWarehouse.businessDefinition.purpose",
        "@DataWarehouse.businessDefinition.contact",
        "@DataWarehouse.businessDefinition.responsibleTeam",
        "@DataWarehouse.businessDefinition.tags",
        "@Analytics.dataCategory",
        "@ObjectModel.modelingPattern",
        "@ObjectModel.supportedCapabilities",
        "@Hierarchy.parentChild",
        "@Hierarchy.leveled",
        "@Analytics.dbViewType",
        "params",
        "@ObjectModel.representativeKey",
        "@DataWarehouse.compoundKeySequence",
        "@cds.persistence.exists",
        "@cds.persistence.skip",
        "@DataWarehouse.sap.reserved",
        "@DataWarehouse.dataTransport",
        "@DataWarehouse.remote.connection",
        "@DataWarehouse.remote.entity",
        "@DataWarehouse.external.schema",
        "@DataWarehouse.external.entity",
        "@DataWarehouse.contentImport.owner",
        "@DataWarehouse.delta",
        "@DataWarehouse.partition",
        "@DataWarehouse.consumption.external",
        "@DataWarehouse.pinToMemory",
        "@DataWarehouse.dataSource.schema",
        "@DataWarehouse.dataSource.entity",
        "@DataWarehouse.tooling.hidden",
      ];

      const tableActualAnnotations = erCsnToModel.getAnnotationsForObject(oTable);
      assert.deepStrictEqual(tableActualAnnotations, tableExpectedAnnotations, "Table annotations");

      // Element
      const oElement = sap.cdw.commonmodel.ModelImpl.createObject(
        "sap.cdw.commonmodel.Element",
        { name: "annotatedElement" },
        oOutput
      ) as any;
      const elementExpectedAnnotations = [
        "@Common.Label",
        "@EndUserText.label",
        "name",
        "alias",
        "doc",
        "type",
        "precision",
        "scale",
        "length",
        "srid",
        "dataType",
        "ref",
        "@DataWarehouse.native.dataType",
        "@DataWarehouse.capabilities.filter.allowedExpressions",
        "@DataWarehouse.capabilities.filter.enabled",
        "key",
        "notNull",
        "default",
        "@ObjectModel.foreignKey.association",
        "@ObjectModel.hierarchy.association",
        "@ObjectModel.text.association",
        "@Analytics.measure",
        "@AnalyticsDetails.measureType",
        "@AnalyticsDetails.exceptionAggregationSteps",
        "@Analytics.dimension",
        "@DataWarehouse.spatial.resultOnError",
        "@Common.IsLanguageIdentifier",
        "@Semantics.currencyCode",
        "@Semantics.unitOfMeasure",
        "@Semantics.amount.currencyCode",
        "@Semantics.quantity.unitOfMeasure",
        "@Semantics.text",
        "@Semantics.language",
        "@Semantics.imageUrl",
        "@Semantics.geoLocation.longitude",
        "@Semantics.geoLocation.latitude",
        "@Semantics.geoLocation.cartoId",
        "@Semantics.geoLocation.normalizedName",
        "@Semantics.businessDate.from",
        "@Semantics.businessDate.to",
        "@Semantics.businessDate.at",
        "@Semantics.fiscal.year",
        "@Semantics.fiscal.period",
        "@Semantics.fiscal.yearPeriod",
        "@Semantics.fiscal.yearVariant",
        "@Semantics.fiscal.quarter",
        "@Semantics.fiscal.yearQuarter",
        "@Semantics.fiscal.yearWeek",
        "@Semantics.date",
        "@Semantics.calendar.dayOfMonth",
        "@Semantics.calendar.dayOfYear",
        "@Semantics.calendar.week",
        "@Semantics.calendar.month",
        "@Semantics.calendar.quarter",
        "@Semantics.calendar.halfyear",
        "@Semantics.calendar.year",
        "@Semantics.calendar.yearWeek",
        "@Semantics.calendar.yearMonth",
        "@Semantics.calendar.yearQuarter",
        "@Semantics.calendar.yearHalfyear",
        "@Semantics.systemDate.createdAt",
        "@Semantics.systemDate.lastChangedAt",
        "@Consumption.labelElement",
        "@ObjectModel.text.element",
        "@Aggregation.default",
        "@Analytics.hidden",
        "@Consumption.hidden",
        "@Consumption.filter",
        "@Consumption.filter.mandatory",
        "@Consumption.filter.hidden",
        "@Consumption.filter.multipleSelections",
        "@Consumption.filter.defaultValue",
        "@Consumption.filter.defaultValueHigh",
        "@Consumption.filter.selectionType",
        "@Aggregation.referenceElement",
        "changedMeasureToAttribute",
      ];

      const elementActualAnnotations = erCsnToModel.getAnnotationsForObject(oElement);
      assert.deepStrictEqual(elementActualAnnotations, elementExpectedAnnotations, "Element annotations");
    });
  });
});
