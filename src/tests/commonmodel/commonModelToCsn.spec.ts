import assert from "assert";
import { CsnAnnotations } from "../../../src/components/commonmodel/csn/csnAnnotations";
import { CommonQualifiedClassNames } from "../../../src/components/commonmodel/model/const/model.const";
import "../../components/ermodeler/js/transform/erCsnToModel";
import "../support/commonModelLoader";
require("../../../src/components/commonmodel/csn/modelToCsn");

const nsCommonModel = sap.cdw.commonmodel;
const ErCsnToModel = sap.cdw.ermodeler.ErCsnToModel;
const nsErModeler = sap.cdw.ermodeler;

function createContextModel() {
  const resource = new sap.galilei.model.Resource();
  const model = new nsCommonModel.Model(resource, {});
  (model as any)._unitTesting = true;

  // Create context "sap" and "sap.finance"
  const contextFinance = nsCommonModel.ModelImpl.getOrCreateContext("sap.finance", model);
  contextFinance.label = "SAP Financial";
  const contextAccounting = nsCommonModel.ModelImpl.getOrCreateContext("sap.finance.accounting", model);
  const contextSap = nsCommonModel.ModelImpl.getContext("sap", model);
  const contextAriba = nsCommonModel.ModelImpl.getOrCreateContext("sap.ariba", model);
  const contextPartner = nsCommonModel.ModelImpl.getOrCreateContext("partner", model);

  // Entity "User"
  const entityUser = nsCommonModel.ModelImpl.createObject(CommonQualifiedClassNames.ENTITY, {
    name: "User",
    label: "The user",
    comment: "This is an User",
  }, model);

  // Entity "sap.Address"
  const entityAddress = nsCommonModel.ModelImpl.createObject(CommonQualifiedClassNames.ENTITY, {
    name: "Address",
    context: contextSap,
  }, model);

  // Entity "sap.finance.Orders"
  const entityOrders = nsCommonModel.ModelImpl.createObject(CommonQualifiedClassNames.ENTITY, {
    name: "Orders",
    context: contextFinance,
  }, model);
  // Add element "id"
  nsCommonModel.ModelImpl.createObject(CommonQualifiedClassNames.ELEMENT, {
    name: "id",
    label: "Order ID",
    comment: "The order ID",
    dataType: "cds.Integer",
    isKey: true,
    notNull: true,
    "default": 1,
  }, entityOrders);
  // Add element "name"
  nsCommonModel.ModelImpl.createObject(CommonQualifiedClassNames.ELEMENT, {
    name: "name",
    label: "Order name",
    comment: "The order name",
    dataType: "cds.String",
    length: 30,
    notNull: true,
  }, entityOrders);

  // Association from "User" to "sap.finance.Orders"
  const associationUserOrders = nsCommonModel.ModelImpl.createObject(CommonQualifiedClassNames.ASSOCIATION, {
    source: entityUser,
    target: entityOrders,
    name: "_Orders",
    label: "User to Orders",
  }, model);

  return model;
}

function createSimpleTypeModel() {
  const resource = new sap.galilei.model.Resource();
  const model = new nsCommonModel.Model(resource, {});
  (model as any)._unitTesting = true;

  // Type "TextType"
  const typeTextType = nsCommonModel.ModelImpl.createObject(CommonQualifiedClassNames.SIMPLE_TYPE, {
    name: "TextType",
    dataType: "cds.String",
    length: 80,
  }, model);

  // Type "Name"
  const typeName = nsCommonModel.ModelImpl.createObject(CommonQualifiedClassNames.SIMPLE_TYPE, {
    name: "Name",
    dataType: "TextType",
    length: 80,
  }, model);

  // Type "Email"
  const typeEmail = nsCommonModel.ModelImpl.createObject(CommonQualifiedClassNames.SIMPLE_TYPE, {
    name: "Email",
    label: "Email simple type",
    dataType: "TextType",
    length: 30,
  }, model);

  // Entity "User"
  const entityUser = nsCommonModel.ModelImpl.createObject(CommonQualifiedClassNames.ENTITY, {
    name: "User",
  }, model);
  entityUser.unhandledCsn = { "customEntity": "EntityValue" };
  // Add element "id"
  const elementId = nsCommonModel.ModelImpl.createObject(CommonQualifiedClassNames.ELEMENT, {
    name: "id",
    dataType: "cds.Integer",
    isKey: true,
  }, entityUser);
  // Add element "name"
  const elementName = nsCommonModel.ModelImpl.createObject(CommonQualifiedClassNames.ELEMENT, {
    name: "name",
    dataType: "Name",
    length: 80,
    "notNull": true,
  }, entityUser);
  // Add element "name"
  const elementEmail = nsCommonModel.ModelImpl.createObject(CommonQualifiedClassNames.ELEMENT, {
    name: "email",
    dataType: "Email",
    length: 30,
  }, entityUser);

  // Create context "abap"
  const contextAbap = nsCommonModel.ModelImpl.getOrCreateContext("abap", model);
  contextAbap.label = "ABAP context";

  // Type "abap.DATS"
  const typeAbapDats = nsCommonModel.ModelImpl.createObject(CommonQualifiedClassNames.SIMPLE_TYPE, {
    name: "DATS",
    label: "Date in Format YYYYMMDD",
    dataType: "cds.String",
    length: 8,
    context: contextAbap,
  }, model);

  nsCommonModel.ObjectImpl.attachObjectsToBaseTypes(model);

  return model;
}

function getByName(oCSN, sName): any {
  return oCSN[sName];
}

function getObjectByName(oCSN, sName): any {
  return getByName(oCSN.definitions, sName);
}

describe("Common Model Context CSN Generation tests", () => {
  let model: any;
  let oCSN: any;

  beforeEach(() => {
    model = createContextModel();
    const csnGenerator = sap.cdw.commonmodel.ModelToCsn.getInstance();
    oCSN = csnGenerator.convertToCsn(model.name, model);
  });

  describe("Context CSN Generation", () => {
    it("Generate context", function() {
      assert.notEqual(oCSN, undefined, "CSN can be generated.");

      // sap context
      const sapContextCSN = getObjectByName(oCSN, "sap");
      assert.equal(sapContextCSN.kind, "context", "Context sap is generated in CSN.");

      // sap.finnance context
      const financeContextCSN = getObjectByName(oCSN, "sap.finance");
      assert.notEqual(financeContextCSN, undefined, "Context sap.finance is generated in CSN.");
      assert.equal(financeContextCSN.kind, "context", "Context sap.finance is generated in CSN.");
      assert.equal(financeContextCSN[CsnAnnotations.EndUserText.label], "SAP Financial", "Context sap.finance label is generated in CSN.");
    });

    it("Generate entity", function() {
      // "User" entity
      const userEntityCSN = getObjectByName(oCSN, "User");
      assert.notEqual(userEntityCSN, undefined, "Entity User is generated in CSN.");
      assert.equal(userEntityCSN.kind, "entity", "Entity User is generated in CSN.");
      assert.equal(userEntityCSN[CsnAnnotations.EndUserText.label], "The user", "Entity User label is generated in CSN.");
      assert.equal(userEntityCSN.doc, "This is an User", "Entity User comment is generated in CSN.");

      // "sap.finance.Orders" entity
      const ordersEntityCSN = getObjectByName(oCSN, "sap.finance.Orders");
      assert.notEqual(ordersEntityCSN, undefined, "Entity sap.finance.Orders is generated in CSN.");
      assert.equal(ordersEntityCSN.kind, "entity", "Entity sap.finance.Orders is generated in CSN.");
    });

    it("Generate entity element", function() {
      // "sap.finance.Orders" entity
      const ordersEntityCSN = getObjectByName(oCSN, "sap.finance.Orders");

      // Element id
      const idCSN = getByName(ordersEntityCSN.elements, "id");
      assert.notEqual(idCSN, undefined, "Element id of sap.finance.Orders is generated in CSN.");

      // Element name
      const nameCSN = getByName(ordersEntityCSN.elements, "name");
      assert.notEqual(nameCSN, undefined, "Element name of sap.finance.Orders is generated in CSN.");
    });

    it("Generate association", function() {
      // "sap.finance.Orders" entity
      const userEntityCSN = getObjectByName(oCSN, "User");

      // Element id
      const ordersAssociationCSN = getByName(userEntityCSN.elements, "_Orders");
      assert.notEqual(ordersAssociationCSN, undefined, "Element for associaiton is generated in CSN.");
      assert.equal(ordersAssociationCSN.target, "sap.finance.Orders", "Element for associaiton's target is sap.finance.Orders.");
    });

    it("Generate cross space context", function() {
      const csnGenerator = sap.cdw.commonmodel.ModelToCsn.getInstance();

      // sap context
      const sapContext = model.contexts.get(0);
      sapContext.crossSpace = {
        schema: "SCHEMA_SAP",
        space: "sap",
      };
      oCSN = csnGenerator.convertToCsn(model.name, model);
      const sapContextCSN = getObjectByName(oCSN, "sap");
      assert.equal(sapContextCSN[CsnAnnotations.DataWarehouse.space_schema], "SCHEMA_SAP", "Context cross space schema is generated in CSN.");
      assert.equal(sapContextCSN[CsnAnnotations.DataWarehouse.space_name], "sap", "Context cross space name is generated in CSN.");
    });

    it("Generate cross space entity", function() {
      const csnGenerator = sap.cdw.commonmodel.ModelToCsn.getInstance();

      // sap context
      const sapContext = model.contexts.get(0);
      const sapFinanceContext = sapContext.contexts.get(0);
      const entityOrders = sapFinanceContext.entities.get(0);
      sapContext.label = "SAP Financial";
      sapContext.crossSpace = {
        schema: "SCHEMA_SAP",
        space: "sap",
      };
      nsCommonModel.ObjectImpl.setEntityCrossSpaceProperties(entityOrders);

      oCSN = csnGenerator.convertToCsn(model.name, model);
      const ordersEntityCSN = getObjectByName(oCSN, "sap.finance.Orders");
      assert.equal(ordersEntityCSN[CsnAnnotations.cds.persistence_exists], true, "Entity @cds.persistence.exists is set.");
    });
  });
});

describe("Common Model Simple Type CSN Generation tests", () => {
  let model: any;
  let csnGenerator: any;
  let oCSN: any;

  beforeEach(() => {
    model = createSimpleTypeModel();
    csnGenerator = sap.cdw.commonmodel.ModelToCsn.getInstance();
    oCSN = csnGenerator.convertToCsn(model.name, model);
  });

  describe("Simple Type CSN Generation", () => {
    it("Generate simple type", function() {
      assert.notEqual(oCSN, undefined, "CSN can be generated.");

      // TextType simple type
      const typeTextTypeCSN = getObjectByName(oCSN, "TextType");
      assert.notEqual(typeTextTypeCSN, undefined, "TextType is generated in CSN.");
      assert.equal(typeTextTypeCSN.kind, "type", "TextType's kind is 'type'.");
      assert.equal(typeTextTypeCSN.type, "cds.String", "TextType's type is cds.String.");
      assert.equal(typeTextTypeCSN.length, 80, "TextType's length is 80.");

      // Email simple type
      const typeEmailCSN = getObjectByName(oCSN, "Email");
      assert.notEqual(typeEmailCSN, undefined, "Email is generated in CSN.");
      assert.equal(typeEmailCSN.kind, "type", "Email's kind is 'type'.");
      assert.equal(typeEmailCSN.type, "TextType", "Email's dataType is TextType.");
      assert.equal(typeEmailCSN.length, 30, "Email's length is 30.");
      assert.equal(typeEmailCSN["@EndUserText.label"], "Email simple type", "Email's @EndUserText.label is generated.");

      // abap.DATS simple type
      const typeAbapDatsCSN = getObjectByName(oCSN, "abap.DATS");
      assert.notEqual(typeAbapDatsCSN, undefined, "abap.DATS is generated in CSN.");
      assert.equal(typeAbapDatsCSN.kind, "type", "abap.DATS's kind is 'type'.");

      // Entity User
      const entityUserCSN = getObjectByName(oCSN, "User");
      assert.notEqual(entityUserCSN, undefined, "Entity User is generated in CSN.");
      assert.deepStrictEqual(entityUserCSN.customEntity, "EntityValue", "Unhandled CSN preserved.");

      // Element email
      const elementEmailCSN = getByName(entityUserCSN.elements, "email");
      assert.notEqual(elementEmailCSN, undefined, "Element email is generated in CSN.");
      assert.equal(elementEmailCSN.type, "Email", "Element email's type is Email.");
      assert.equal(elementEmailCSN.length, 30, "Element email's length is 30.");
    });

  });

  describe("CSN Generation for annotation edition", () => {
    it("Generate/Edit CSN for one entity", function() {
      const entity = model.entities.get(0);
      const entityCsn = csnGenerator.getCsnForEntity(entity);
      const expected = { "User": { "kind": "entity", "customEntity": "EntityValue", "@EndUserText.label": "User", "@ObjectModel.modelingPattern": { "#": "DATA_STRUCTURE" }, "@ObjectModel.supportedCapabilities": [{ "#": "DATA_STRUCTURE" }], "elements": { "id": { "type": "cds.Integer", "key": true, "notNull": true }, "name": { "type": "Name", "length": 80 }, "email": { "type": "Email", "length": 30 } } } };
      assert.deepStrictEqual(entityCsn, expected, "Right CSN for ONE entity");

      const erCsnToModel = new ErCsnToModel();
      // Prepare params
      // Get Association Annotations
      const associationClass = sap.galilei.model.getClass(CommonQualifiedClassNames.ASSOCIATION);
      const asociationAnnotations = erCsnToModel.getAnnotationsForClassDefinition(associationClass);
      assert.deepStrictEqual(asociationAnnotations, ['@Common.Label', '@EndUserText.label', 'name', 'alias', 'doc', 'cardinality'], "Association class definition annotations");

      // Get entire entity annotations and check hierarchy annotations
      const entityAnnotations = erCsnToModel.getHandledAnnotationsForEntity(entity);
      assert.deepStrictEqual(entityAnnotations[CsnAnnotations.Hierarchy.parentChild], ['name', 'label', 'recurse', 'nodeType', 'directory'], "Parent/Child hierarchy annotations");
      assert.deepStrictEqual(entityAnnotations[CsnAnnotations.Hierarchy.leveled], ['name', 'label', 'levels'], "Level-based hierarchy annotations");

      // Check Params for Annotation editing (Handle DW101-73162: technical name is different)
      entity.technicalName = "newViewName";
      const csnParams = erCsnToModel.getCsnEditParams(entity);
      const expectedEntityAnnotations = ['@Common.Label', '@EndUserText.label', 'name', 'alias', 'doc', '@DataWarehouse.businessDefinition.description', '@DataWarehouse.businessDefinition.purpose', '@DataWarehouse.businessDefinition.contact', '@DataWarehouse.businessDefinition.responsibleTeam', '@DataWarehouse.businessDefinition.tags', '@Analytics.dataCategory', '@ObjectModel.modelingPattern', '@ObjectModel.supportedCapabilities', '@Analytics.dbViewType', '@ObjectModel.representativeKey', '@DataWarehouse.compoundKeySequence', '@cds.persistence.exists', '@cds.persistence.skip', '@DataWarehouse.sap.reserved', '@DataWarehouse.remote.connection', '@DataWarehouse.remote.entity', '@DataWarehouse.external.schema', '@DataWarehouse.external.entity', '@DataWarehouse.contentImport.owner', '@DataWarehouse.delta', '@DataWarehouse.partition', "@DataWarehouse.consumption.external", '@DataWarehouse.pinToMemory', '@DataWarehouse.dataSource.schema', '@DataWarehouse.dataSource.entity', '@DataWarehouse.tooling.hidden', 'kind', '@DataWarehouse.space.name', '@DataWarehouse.space.schema', '@DataWarehouse.space.businessName',  '@DataWarehouse.enclosingObject', '_meta'];
      assert.strictEqual(csnParams.root.name, "newViewName", "root name is OK");
      assert.strictEqual(csnParams.root.idKey, "Entity", "Class Definition key is OK");
      assert.deepStrictEqual(csnParams.root.doNotChange, ['@EndUserText.label'], "Do not change label is OK");
      assert.deepStrictEqual(csnParams.root.handledAnnotations, expectedEntityAnnotations, "Entity annotations are OK");
      // Check target is included in elements handled annotations
      assert.deepStrictEqual(csnParams.collections[0].doNotChangeIf, [{ "key": "type", "value": "cds.Association" }], "'doNotChangeIf is handled to highlight associations");
      assert.strictEqual(csnParams.collections[0].handledAnnotations.includes("target"), true, "'target' annotation is OK");
      // Check parameters handled annotations
      // Jira DW101-25694: '@Consumption.valueHelpDefinition' is not (yet?) handled in the UI, therefore it should be removed from parameter known annotations..
      // ==> bring back this annotation to the array below once handled in the UI
      const parameterAnnotations = ['@EndUserText.label', 'type', 'length', 'precision', 'scale', 'default', 'defaultValue', '@AnalyticsDetails.variable.usageType', '@AnalyticsDetails.variable.selectionType', '@AnalyticsDetails.variable.referenceElement', '@AnalyticsDetails.variable.multipleSelections'];
      assert.deepStrictEqual(csnParams.collections[1].handledAnnotations, parameterAnnotations, "View parameter annotations are OK");
    });

    it("Update entity after CSN annotation edition", function() {
      // Entity having unhandled annotations (at different levels)
      const oData = {
        "repositoryData": [
          {
            "name": "Entity1",
            "id": "Entity1",
            "definitions": {
              "Entity1": {
                "kind": "entity",
                "elements": {
                  "Element1": {
                    "type": "cds.String",
                    "key": true,
                    "notNull": true,
                    "length": 2,
                    "@EndUserText.label": "ElementLabel1",
                  },
                  "Element2": {
                    "type": "cds.integer",
                    "notNull": true,
                    "@EndUserText.label": "ElementLabel2",
                  },
                  "Element3": {
                    "type": "cds.String",
                    "length": 2,
                    "@EndUserText.label": "ElementLabel3",
                  },
                  "AssocElement": {
                    "type": "cds.Association",
                    "@EndUserText.label": "ElementLabel3",
                    "target": "Country",
                    "on": [
                      {
                        "ref": [
                          "AssocElement",
                          "countrycode",
                        ],
                      },
                      "=",
                      {
                        "ref": [
                          "Element3",
                        ],
                      },
                    ],
                  },
                },
                "@Hierarchy.parentChild": [{
                  "name": "pch1",
                  "label": "pchLabel",
                  "myFirstHierachyAnnotation": "First Hierachy Annotation Value",
                  "recurse": {
                    "parent": [{ "=": "Element3" }],
                    "child": [{ "=": "Element1" }],
                  },
                }],
                "@EndUserText.label": "EntityLabel1",
                "@Analytics.dataCategory": {
                  "#": "DIMENSION"
                },
              },
            },
          },
          {
            "name": "Country",
            "id": "Country",
            "definitions": {
              "Country": {
                "kind": "entity",
                "@ObjectModel.modelingPattern": {
                  "#": "DIMENSION",
                },
                "@ObjectModel.supportedCapabilities": [
                  {
                    "#": "DIMENSION",
                  },
                  {
                    "#": "CDS_MODELING_DATA_SOURCE",
                  },
                ],
                "elements": {
                  "countrycode": {
                    "length": 2,
                    "type": "cds.String",
                  },
                  "country_name": {
                    "length": 100,
                    "type": "cds.String",
                  },
                  "region": {
                    "length": 2,
                    "type": "cds.String",
                  },
                },
              },
            }
          },
        ],
      };
      const oNewModel = nsErModeler.ModelImpl.getOrCreateModel();
      oNewModel._unitTesting = true;

      const oDiagramEditor = { model: oNewModel, deleteSymbols: function() { } };
      sap.cdw.ermodeler.ModelImpl.importEntitiesAndViewsFromList(oData, oDiagramEditor);

      const entity1 = oNewModel.entities.get(0);
      const initialCsn = csnGenerator.getCsnForEntity(entity1);
      assert.strictEqual(initialCsn.Entity1.kind, "entity", "CSN correctly read-written for Entity1");

      // Simulate CSN edition..
      const editedCsn = {
        "Entity1": {
          "elements": {
            "Element1": {
              "@EndUserText.label": "ElementLabel1",
              "annotation1": "On Element1"
            },
            "Element2": {
              "@EndUserText.label": "ElementLabel2"
            },
            "Element3": {
              "@EndUserText.label": "ElementLabel3",
              "annotation3": "On Element3"
            },
            "AssocElement": {
              "type": "cds.Association",
              "@EndUserText.label": "ElementLabel3",
              "associationAnnotation": "On Association"
            }
          },
          "@EndUserText.label": "EntityLabel1",
          "isCustomRootAnnotationHandled": "yeah!",
          "@Hierarchy.parentChild": [
            {
              "name": "pch1",
              "label": "pchLabel",
              "recurse": {
                "parent": [
                  {
                    "=": "Element3"
                  }
                ],
                "child": [
                  {
                    "=": "Element1"
                  }
                ]
              },
              "myFirstHierachyAnnotation": "First Hierachy Annotation Value",
              "mySecondHierachyAnnotation": "On Hierarchy"
            }
          ]
        }
      };
      // Update entity
      const erCsnToModel = new ErCsnToModel();
      erCsnToModel.updateEntityAnnotations(entity1, JSON.parse(JSON.stringify(editedCsn)));
      // Check unhandled annotation...
      const expectedR = { "isCustomRootAnnotationHandled": "yeah!" };
      const expected1 = { "annotation1": "On Element1" };
      const expected3 = { "annotation3": "On Element3" };
      const expectedA = { "associationAnnotation": "On Association" };
      const expectedH = { "myFirstHierachyAnnotation": "First Hierachy Annotation Value", "mySecondHierachyAnnotation": "On Hierarchy" };
      const element1 = entity1.elements.toArray().find(e => e.name === "Element1");
      const element3 = entity1.elements.toArray().find(e => e.name === "Element3");
      const hierarchy = entity1.hierarchies.get(0);
      const association = entity1.allAssociations[0];
      assert.deepStrictEqual(element1.unhandledCsn, expected1, "Element1 annotation added");
      assert.deepStrictEqual(element3.unhandledCsn, expected3, "Element3 annotation added");
      assert.deepStrictEqual(hierarchy.unhandledCsn, expectedH, "Hierarchy annotation added");
      assert.deepStrictEqual(association.unhandledCsn, expectedA, "Association annotation added");
      assert.deepStrictEqual(entity1.unhandledCsn, expectedR, "Root annotation added");

      // DW101-21401
      // Check isDirty is true
      assert.deepStrictEqual(entity1.resource.isDirty, true, "Entity changed");
      // Undo
      entity1.resource.clearUndoStack();
      assert.deepStrictEqual(entity1.resource.isDirty, false, "Check Undo of Entity changes");
      // Simulate OK without changes
      erCsnToModel.updateEntityAnnotations(entity1, JSON.parse(JSON.stringify(editedCsn)));
      // Check isDirty is false
      assert.deepStrictEqual(entity1.resource.isDirty, false, "Entity has not been flagged as changed");
    });
  });
});
