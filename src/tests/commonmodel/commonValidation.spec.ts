/** @format */

require("../support/commonModelLoader");
import assert from "assert";
import sinon from "sinon";
import { CommonQualifiedClassNames } from "../../../src/components/commonmodel/model/const/model.const";
import {
  AggregationTypes,
  CDSDataType,
  DataCategory,
  SemanticType,
} from "../../components/commonmodel/model/types/cds.types";
import { unitColumnValueStateText } from "../../components/ermodeler/js/utility/sharedFunctions";

const nsCommonModel = sap.cdw.commonmodel;

function createModel(): sap.cdw.commonmodel.Model {
  const resource = new sap.galilei.model.Resource();
  const model = new nsCommonModel.Model(resource, {});
  (model as any)._unitTesting = true;

  // Type "Name"
  const typeName = nsCommonModel.ModelImpl.createObject(
    CommonQualifiedClassNames.SIMPLE_TYPE,
    {
      name: "Name",
      label: "Name",
      dataType: "cds.String",
      length: 20,
    },
    model
  );

  // Type "LargeText"
  const typeLargeText = nsCommonModel.ModelImpl.createObject(
    CommonQualifiedClassNames.SIMPLE_TYPE,
    {
      name: "Large Text",
      label: "LargeText",
      dataType: "cds.LargeString",
    },
    model
  );

  // Entity "User"
  const entityUser = nsCommonModel.ModelImpl.createObject(
    CommonQualifiedClassNames.ENTITY,
    {
      name: "User",
    },
    model
  );

  // Add element "id"
  const elementId = nsCommonModel.ModelImpl.createObject(
    CommonQualifiedClassNames.ELEMENT,
    {
      name: "id",
      dataType: "cds.String",
      length: 20,
    },
    entityUser
  );

  // Add element "name"
  const elementName = nsCommonModel.ModelImpl.createObject(
    CommonQualifiedClassNames.ELEMENT,
    {
      name: "name",
      baseType: typeName,
      dataType: "Name",
      length: 20,
    },
    entityUser
  );

  // Add element "comment"
  const elementComment = nsCommonModel.ModelImpl.createObject(
    CommonQualifiedClassNames.ELEMENT,
    {
      name: "comment",
      baseType: typeName,
      dataType: "Name",
      length: 20,
    },
    entityUser
  );

  return model;
}

describe("Common Model Validation tests", () => {
  let model: sap.cdw.commonmodel.Model;

  beforeEach(() => {
    model = createModel();
  });

  it("Test validations for TEXT entity", async function () {
    let stub;
    stub = sinon.stub(sap.ui.getCore().getModel("featureflags"), "getProperty");

    stub.returns(true);

    const nsErModeler = sap.cdw.ermodeler;
    const oNewModel = nsErModeler.ModelImpl.getOrCreateModel();
    const oEntity1 = nsErModeler.ModelImpl.createObject(
      "sap.cdw.commonmodel.Table",
      { name: "Entity1", label: "EntityLabel1" },
      oNewModel
    );
    const oElement1 = nsErModeler.ModelImpl.createObject(
      "sap.cdw.commonmodel.Element",
      { name: "Element1", dataType: CDSDataType.STRING, length: 10, isKey: false, label: "ElementLabel1" },
      oEntity1
    );
    const oElement2 = nsErModeler.ModelImpl.createObject(
      "sap.cdw.commonmodel.Element",
      { name: "Element2", dataType: CDSDataType.STRING, length: 10, isKey: false, label: "ElementLabel2" },
      oEntity1
    );
    const oElement3 = nsErModeler.ModelImpl.createObject(
      "sap.cdw.commonmodel.Element",
      { name: "Element3", dataType: CDSDataType.STRING, length: 10, isKey: false, label: "ElementLabel3" },
      oEntity1
    );
    const oElement4 = nsErModeler.ModelImpl.createObject(
      "sap.cdw.commonmodel.Element",
      { name: "Element4", dataType: CDSDataType.STRING, length: 10, isKey: false, label: "ElementLabel4" },
      oEntity1
    );
    oEntity1.dataCategory = DataCategory.TEXT;
    oEntity1.validate(/* async */ false);
    assert.strictEqual(
      oEntity1.aggregatedValidations.validations.map((v) => v.message.id).join(", "),
      "VAL_TEXT_KEY_NOT_LANGUAGE_SHOULD_LABEL, VAL_TEXT_DIMENSION_AT_LEAST_1_NONE_TYPE_KEY, VAL_TEXT_DIMENSION_CAN_ONLY_1_LANGUAGE_TYPE_KEY, VAL_TEXT_DIMENSION_AT_LEAST_1_TEXT_COLUMNS",
      "test 1"
    );
    oElement1.isKey = true;
    oEntity1.validate(/* async */ false);
    assert.strictEqual(
      oEntity1.aggregatedValidations.validations.map((v) => v.message.id).join(", "),
      "VAL_TEXT_KEY_NOT_LANGUAGE_SHOULD_LABEL, VAL_TEXT_DIMENSION_CAN_ONLY_1_LANGUAGE_TYPE_KEY, VAL_TEXT_DIMENSION_AT_LEAST_1_TEXT_COLUMNS",
      "test 2"
    );
    oElement2.isKey = true;
    oElement2.semanticType = SemanticType.LANGUAGE;
    oElement3.isKey = true;
    oElement3.semanticType = SemanticType.LANGUAGE;
    oEntity1.validate(/* async */ false);
    assert.strictEqual(
      oEntity1.aggregatedValidations.validations.map((v) => v.message.id).join(", "),
      "VAL_TEXT_KEY_NOT_LANGUAGE_SHOULD_LABEL, VAL_TEXT_DIMENSION_CAN_ONLY_1_LANGUAGE_TYPE_KEY, VAL_TEXT_DIMENSION_AT_LEAST_1_TEXT_COLUMNS",
      "test 3"
    );
    oElement3.isKey = false;
    oEntity1.validate(/* async */ false);
    assert.strictEqual(
      oEntity1.aggregatedValidations.validations.map((v) => v.message.id).join(", "),
      "VAL_TEXT_KEY_NOT_LANGUAGE_SHOULD_LABEL, VAL_TEXT_DIMENSION_AT_LEAST_1_TEXT_COLUMNS",
      "test 4"
    );
    oElement3.semanticType = SemanticType.TEXT;
    oEntity1.validate(/* async */ false);
    assert.strictEqual(
      oEntity1.aggregatedValidations.validations.map((v) => v.message.id).join(", "),
      "VAL_TEXT_KEY_NOT_LANGUAGE_SHOULD_LABEL",
      "test 5"
    );
    oElement3.isKey = true;
    oEntity1.validate(/* async */ false);
    assert.strictEqual(
      oEntity1.aggregatedValidations.validations.map((v) => v.message.id).join(", "),
      "VAL_TEXT_KEY_NOT_LANGUAGE_SHOULD_LABEL",
      "test 6"
    );
    oElement2.dataType = CDSDataType.INTEGER;
    oEntity1.validate(/* async */ false);
    assert.strictEqual(
      oEntity1.aggregatedValidations.validations.map((v) => v.message.id).join(", "),
      "VAL_TEXT_KEY_NOT_LANGUAGE_SHOULD_LABEL, VAL_TEXT_DIMENSION_CAN_ONLY_1_LANGUAGE_TYPE_KEY",
      "test 7"
    );

    // Set simple type of string to element, should not have validation created
    const typeName = nsCommonModel.ModelImpl.createObject(
      CommonQualifiedClassNames.SIMPLE_TYPE,
      {
        name: "Name",
        label: "Name",
        dataType: "cds.String",
        length: 20,
      },
      oNewModel
    );
    oElement2.dataType = "Name";
    oElement2.semanticType = SemanticType.LANGUAGE; // After set data type, need set semanticType again
    oEntity1.validate(/* async */ false);
    assert.strictEqual(
      oEntity1.aggregatedValidations.validations.map((v) => v.message.id).join(", "),
      "VAL_TEXT_KEY_NOT_LANGUAGE_SHOULD_LABEL",
      "test 8"
    );

    // Check possibleSemanticTypes for Text dimension
    assert.strictEqual(oElement1.possibleSemanticTypes.length, 6, "check possibleSemanticTypes");
    stub.restore();
  });

  it("Change data type from primitive String to Double, validateLength()", function () {
    const entityUser = model.entities.selectObject({ name: "User" });
    const elementId = entityUser.elements.selectObject({ name: "id" });
    const elementName = entityUser.elements.selectObject({ name: "name" });
    const elementComment = entityUser.elements.selectObject({ name: "comment" });

    // Change "id" data type from "cds.String" to "cds.Double"
    model.clearValidation();
    elementId.dataType = "cds.Double";
    assert.strictEqual((entityUser.aggregatedValidations as any).validations.length, 0, "No validation error");

    // Change "name" data type from "Name" to "cds.Double"
    model.clearValidation();
    elementName.dataType = "cds.Double";
    assert.strictEqual((entityUser.aggregatedValidations as any).validations.length, 0, "No validation error");

    // Change "comment" data type from "Name" to "LargeText"
    model.clearValidation();
    elementName.dataType = "LargeText";
    assert.strictEqual((entityUser.aggregatedValidations as any).validations.length, 0, "No validation error");
  });

  it("Test validations for Measure Element and formatter", async function () {
    const nsErModeler = sap.cdw.ermodeler;
    const oNewModel = nsErModeler.ModelImpl.getOrCreateModel();
    const oEntity1 = nsErModeler.ModelImpl.createObject(
      "sap.cdw.commonmodel.Table",
      { name: "Entity1", label: "EntityLabel1" },
      oNewModel
    );
    oEntity1.dataCategory = DataCategory.SQLFACT;
    const unitTypeElement = nsErModeler.ModelImpl.createObject(
      "sap.cdw.commonmodel.Element",
      { name: "unitTypeElement", dataType: CDSDataType.STRING, length: 10, isKey: false, label: "ElementLabel2" },
      oEntity1
    );
    unitTypeElement.semanticType = SemanticType.UNIT_OF_MEASURE;

    // Add measure element
    oEntity1.createElement({
      name: "measure1",
      isMeasure: true,
      isDimension: false,
      defaultAggregation: AggregationTypes.SUM,
      dataType: CDSDataType.INTEGER,
      semanticType: SemanticType.EMPTY,
    });
    const measure1 = oEntity1.elements.get(oEntity1.elements.length - 1);

    oEntity1.validate(/* async */ false);
    let validations = oEntity1.aggregatedValidations.validations;
    assert.strictEqual(validations.length === 0, true, "0 errors");

    measure1.semanticType = SemanticType.QUANTITY_WITH_UNIT;
    oEntity1.validate(/* async */ false);
    validations = oEntity1.aggregatedValidations.validations;
    assert.strictEqual(
      validations.length === 1 &&
        JSON.stringify(validations[0].message) ===
          '{"groupId":"i18n_commonmodel","id":"VAL_UNIT_COLUMN_EMPTY_UNIT","params":["measure1","Entity1"]}',
      true,
      "1 errors"
    );

    measure1.unitTypeElement = unitTypeElement;
    oEntity1.validate(/* async */ false);
    validations = oEntity1.aggregatedValidations.validations;
    assert.strictEqual(validations.length === 0, true, "0 errors");

    assert.strictEqual(unitColumnValueStateText(SemanticType.EMPTY, true, null), "");
    assert.strictEqual(unitColumnValueStateText(SemanticType.QUANTITY_WITH_UNIT, false, null), "");
    assert.strictEqual(unitColumnValueStateText(SemanticType.QUANTITY_WITH_UNIT, true, {}), "");
    // Following two tests are commented out because the texts may be changed in the future
    // assert.strictEqual(
    //   unitColumnValueStateText(SemanticType.QUANTITY_WITH_UNIT, true, null),
    //   'Select an attribute with semantic type "Unit of Measure".' // "VAL_STATE_UNIT_COLUMN_EMPTY_UNIT"
    // );
    // assert.strictEqual(
    //   unitColumnValueStateText(SemanticType.AMOUNT_WITH_CURRENCY, true, null),
    //   'Select an attribute with semantic type "Currency Code".' // "VAL_STATE_UNIT_COLUMN_EMPTY_CURRENCY"
    // );
    assert.strictEqual(unitColumnValueStateText(SemanticType.QUANTITY_WITH_UNIT, true, null) !== "", true);
    assert.strictEqual(unitColumnValueStateText(SemanticType.AMOUNT_WITH_CURRENCY, true, null) !== "", true);
  });

  it("Test validations for business date elements", async function () {
    const nsErModeler = sap.cdw.ermodeler;
    const oNewModel = nsErModeler.ModelImpl.getOrCreateModel();
    const oEntity1 = nsErModeler.ModelImpl.createObject(
      "sap.cdw.commonmodel.Table",
      { name: "Entity1", label: "EntityLabel1" },
      oNewModel
    );
    oEntity1.dataCategory = DataCategory.DIMENSION;
    const ele1 = nsErModeler.ModelImpl.createObject(
      "sap.cdw.commonmodel.Element",
      {
        name: "ele1",
        dataType: CDSDataType.DATE,
        isKey: false,
        label: "ele1",
        semanticType: SemanticType.BUSINESS_DATE_FROM,
      },
      oEntity1
    );
    nsErModeler.ModelImpl.createObject(
      "sap.cdw.commonmodel.Element",
      {
        name: "ele2",
        dataType: CDSDataType.DATE,
        isKey: false,
        label: "ele2",
        semanticType: SemanticType.BUSINESS_DATE_TO,
      },
      oEntity1
    );
    oEntity1.validate(/* async */ false);
    let validations = oEntity1.aggregatedValidations.validations;
    assert.equal(validations.length, 2, "2 warning");
    assert.equal(validations[0].message.id, "VAL_BUSINESS_DATE_NO_KEY", "VAL_BUSINESS_DATE_NO_KEY");

    ele1.isKey = true;
    oEntity1.validate(/* async */ false);
    validations = oEntity1.aggregatedValidations.validations;
    assert.equal(validations.length, 0, "fix warning");
  });
});

describe("Common.validation functions tests", () => {
  it("Test getImpactedViewsListForObjectLink", () => {
    const benchmark = [
      {
        text: "aa_View_1",
        title: "Open in New Tab",
        href: "#/databuilder&/db/MARCO_SPACE/aa_View_1",
        order: "0",
      },
      {
        text: "author_view",
        title: "Open in New Tab",
        href: "#/databuilder&/db/MARCO_SPACE/author_view",
        order: "0",
      },
      {
        text: "Book",
        title: "Open in New Tab",
        href: "#/databuilder&/db/MARCO_SPACE/Book",
        order: "0",
      },
      {
        text: "bookView",
        title: "Open in New Tab",
        href: "#/databuilder&/db/MARCO_SPACE/bookView",
        order: "0",
      },
      {
        text: "Data_Flow_1_new",
        title: "Open in New Tab",
        href: "#/databuilder&/db/MARCO_SPACE/Data_Flow_1_new",
        order: "0",
      },
      {
        text: "Intelligent_Lookup_1",
        title: "Open in New Tab",
        href: "#/databuilder&/db/MARCO_SPACE/Intelligent_Lookup_1",
        order: "0",
      },
      {
        text: "S7716_sv_association_dac",
        title: "Open in New Tab",
        href: "#/databuilder&/db/CHAOYUE_AUTO/S7716_sv_association_dac",
        order: "0",
      },
      {
        text: "sql_view_test1",
        title: "Open in New Tab",
        href: "#/databuilder&/db/MARCO_SPACE/sql_view_test1",
        order: "0",
      },
      {
        text: "sql_view_test122222",
        title: "Open in New Tab",
        href: "#/databuilder&/db/MARCO_SPACE/sql_view_test122222",
        order: "0",
      },
      {
        text: "sql_view_test122222new",
        title: "Open in New Tab",
        href: "#/databuilder&/db/MARCO_SPACE/sql_view_test122222new",
        order: "0",
      },
      {
        text: "sql_view_test122222new2",
        title: "Open in New Tab",
        href: "#/databuilder&/db/MARCO_SPACE/sql_view_test122222new2",
        order: "0",
      },
      {
        text: "sqlViewT1",
        title: "Open in New Tab",
        href: "#/databuilder&/db/MARCO_SPACE/sqlViewT1",
        order: "0",
      },
      {
        text: "View_1newnew",
        title: "Open in New Tab",
        href: "#/databuilder&/db/MARCO_SPACE/View_1newnew",
        order: "0",
      },
      {
        text: "View_1newnew22",
        title: "Open in New Tab",
        href: "#/databuilder&/db/MARCO_SPACE/View_1newnew22",
        order: "0",
      },
      { text: "E2E_AUTO_IL#Inaccessible_Object_1 (entity)", title: "", href: "undefined", order: "1" },
    ];
    const input1 = {
      name: "id",
      status: "loaded",
      impactedViews: [
        "bookView",
        "Book",
        "sql_view_test122222",
        "S7716_sv_association_dac (CHAOYUE_AUTO)",
        "sql_view_test122222new2",
        "sql_view_test122222new",
        "sql_view_test1",
        "View_1newnew22",
        "View_1newnew",
        "aa_View_1",
        "author_view",
        "sqlViewT1",
        "Data_Flow_1_new",
        "Intelligent_Lookup_1",
        "E2E_AUTO_IL#Inaccessible_Object_1 (entity)",
      ],
      impactedDependencies: [
        {
          properties: { "#spaceName": "MARCO_SPACE", "#spaceLabel": "MARCO_SPACE" },
          id: "3915C21023AF23E9170089CA2AB87A48",
          changeVersion: -99,
          qualifiedName: "bookView",
          kind: "entity",
          name: "bookView",
          folderId: "BFB66842749B9DB117006952BB6924EA",
          hash: "700d7a0db54dac9874e6ca383ff3af09aba807034e4b7cc0eb9b4bae42ba97a5",
          dependencyType: "cds.Association",
          impact: true,
          lineage: false,
        },
        {
          properties: { "#spaceName": "MARCO_SPACE", "#spaceLabel": "MARCO_SPACE" },
          id: "2C42BD1023AF23E9170089CA2AB87A48",
          changeVersion: -99,
          qualifiedName: "Book",
          kind: "entity",
          name: "Book",
          folderId: "BFB66842749B9DB117006952BB6924EA",
          hash: "49ba0cc65ac6fdf8f97b5761814271d2e913f44bf9a84c3a9ccc5bc23208d5c9",
          dependencyType: "cds.Association",
          impact: true,
          lineage: false,
        },
        {
          properties: { "#spaceName": "MARCO_SPACE", "#spaceLabel": "MARCO_SPACE" },
          id: "82B227E09080D7431800318E324E4A0C",
          changeVersion: -99,
          qualifiedName: "sql_view_test122222",
          kind: "entity",
          name: "sql_view_test122222",
          folderId: "BFB66842749B9DB117006952BB6924EA",
          hash: "43826ef2132195bc3f1e4ec8dd3fb73c7a87123a68a1c3b96616ebd139cb3766",
          dependencyType: "cds.Association",
          impact: true,
          lineage: false,
        },
        {
          properties: { "#spaceName": "CHAOYUE_AUTO", "#spaceLabel": "CHAOYUE_AUTO" },
          id: "49F1C790FA19FC4A18000E52D6B0717D",
          changeVersion: -99,
          qualifiedName: "S7716_sv_association_dac",
          kind: "entity",
          name: "S7716_sv_association_dac",
          folderId: "5EDC1F104A907B3D1700B6C69AAD1427",
          hash: "eb546fc876ce5602d66a0fb9edac1af7c2a649d0b1645badfeb23dd9006f26d0",
          dependencyType: "cds.Association",
          impact: true,
          lineage: false,
        },
        {
          properties: { "#spaceName": "MARCO_SPACE", "#spaceLabel": "MARCO_SPACE" },
          id: "2B070750A117EB481800805E38D2C1A3",
          changeVersion: -99,
          qualifiedName: "sql_view_test122222new2",
          kind: "entity",
          name: "sql_view_test122222new2",
          folderId: "BFB66842749B9DB117006952BB6924EA",
          hash: "e1f8fdb80f94b7661ceca4babe96118b2925999c069c1ce56f96bbc054692fae",
          dependencyType: "cds.Association",
          impact: true,
          lineage: false,
        },
        {
          properties: { "#spaceName": "MARCO_SPACE", "#spaceLabel": "MARCO_SPACE" },
          id: "4DBC0650A117EB481800805E38D2C1A3",
          changeVersion: -99,
          qualifiedName: "sql_view_test122222new",
          kind: "entity",
          name: "sql_view_test122222new",
          folderId: "BFB66842749B9DB117006952BB6924EA",
          hash: "2d0f8320c10a37ddddfb6269ab343368efb726083303812b152f2acb5caf2deb",
          dependencyType: "cds.Association",
          impact: true,
          lineage: false,
        },
        {
          properties: { "#spaceName": "MARCO_SPACE", "#spaceLabel": "MARCO_SPACE" },
          id: "29B227E09080D7431800318E324E4A0C",
          changeVersion: -99,
          qualifiedName: "sql_view_test1",
          kind: "entity",
          name: "sql_view_test1",
          folderId: "BFB66842749B9DB117006952BB6924EA",
          hash: "e6093f50bd05f1889a29ebe289aab9ddd48f6c0feea8de4cfb00e834773425dd",
          dependencyType: "cds.Association",
          impact: true,
          lineage: false,
        },
        {
          properties: { "#spaceName": "MARCO_SPACE", "#spaceLabel": "MARCO_SPACE" },
          id: "40B127E09080D7431800318E324E4A0C",
          changeVersion: -99,
          qualifiedName: "View_1newnew22",
          kind: "entity",
          name: "View_1newnew22",
          folderId: "BFB66842749B9DB117006952BB6924EA",
          hash: "5a060efc2a399a97e1d2d0587dc4ca41d23bf23baaa7092d02afc7581f194735",
          dependencyType: "cds.Association",
          impact: true,
          lineage: false,
        },
        {
          properties: { "#spaceName": "MARCO_SPACE", "#spaceLabel": "MARCO_SPACE" },
          id: "BDB027E09080D7431800318E324E4A0C",
          changeVersion: -99,
          qualifiedName: "View_1newnew",
          kind: "entity",
          name: "View_1newnew",
          folderId: "BFB66842749B9DB117006952BB6924EA",
          hash: "9ea5ff296fa8e00b0f6af0424fe70e5a29edbbc80473c4b584957cf54c660a61",
          dependencyType: "cds.Association",
          impact: true,
          lineage: false,
        },
        {
          properties: { "#spaceName": "MARCO_SPACE", "#spaceLabel": "MARCO_SPACE" },
          id: "22FB2660B6DD1C1F180016FA86347E9B",
          changeVersion: -99,
          qualifiedName: "aa_View_1",
          kind: "entity",
          name: "aa_View_1",
          folderId: "BFB66842749B9DB117006952BB6924EA",
          hash: "1959204baf9c2a5a629f60ee494dc80076efd098926ccf2cae87befa1d2e5f68",
          dependencyType: "csn.query.column",
          impact: true,
          lineage: false,
        },
        {
          properties: { "#spaceName": "MARCO_SPACE", "#spaceLabel": "MARCO_SPACE" },
          id: "C3F91E90B0A24BFF170074B6BADCD60A",
          changeVersion: -99,
          qualifiedName: "author_view",
          kind: "entity",
          name: "author_view",
          folderId: "BFB66842749B9DB117006952BB6924EA",
          hash: "f4bf11408b8b535ec087fa562ee0ebd312438fb47b526794dbff86786aa8cbf4",
          dependencyType: "csn.query.column",
          impact: true,
          lineage: false,
        },
        {
          properties: { "#spaceName": "MARCO_SPACE", "#spaceLabel": "MARCO_SPACE" },
          id: "33EC1560B6DD1C1F180016FA86347E9B",
          changeVersion: -99,
          qualifiedName: "sqlViewT1",
          kind: "entity",
          name: "sqlViewT1",
          folderId: "BFB66842749B9DB117006952BB6924EA",
          hash: "ca7b1efff8a584501e89d9b83ea0d0595f4db60c71ffad0ceee6d12d826e4093",
          dependencyType: "csn.query.column",
          impact: true,
          lineage: false,
        },
        {
          properties: { "#spaceName": "MARCO_SPACE", "#spaceLabel": "MARCO_SPACE" },
          id: "CBEE2C100B8A28301800250611252CF6",
          changeVersion: -99,
          qualifiedName: "Data_Flow_1_new",
          kind: "sap.dis.dataflow",
          name: "Data_Flow_1_new",
          folderId: "BFB66842749B9DB117006952BB6924EA",
          hash: "0207472d1a62bb78f20daba3bffef878af2e3421dddab5044fb946e94a79eb5c",
          dependencyType: "sap.dis.target.element",
          impact: true,
          lineage: false,
        },
        {
          properties: { "#spaceName": "MARCO_SPACE", "#spaceLabel": "MARCO_SPACE" },
          id: "F5131671307B9BD91700C08EBA5B0758",
          changeVersion: -99,
          qualifiedName: "Intelligent_Lookup_1",
          kind: "entity",
          name: "Intelligent_Lookup_1",
          folderId: "BFB66842749B9DB117006952BB6924EA",
          hash: "1898cc8281efdbdc265339a26a0349573b491c8006013ca640d96da59a488335",
          dependencyType: "sap.dwc.idtEntityColumn",
          impact: true,
          lineage: false,
        },
        {
          kind: "entity",
          name: "Inaccessible_Object_1",
          impact: true,
          lineage: false,
          properties: {
            "#spaceName": "E2E_AUTO_IL",
            "#spaceLabel": "E2E_Auto_IL",
          },
        },
      ],
      severity: 4,
    };
    const linksInfo = sap.cdw.commonmodel.Validation.getImpactedViewsListForObjectLink(input1);
    assert.strictEqual(JSON.stringify(linksInfo), JSON.stringify(benchmark), "test 1 OK");
  });
});
