/** @format */

import { State } from "@sap/dwc-circuit-breaker/dist/types";
import { BaseControllerClass } from "../../../components/basecomponent/controller/BaseController.controller";
import { SpaceDetailsFormatter } from "../../../components/managespaces/formatter/SpaceDetailsFormatter";
import { byteToGb, MAX_LENGTH_SPACENAME, SpaceTypeIconPath } from "../../../components/managespaces/utility/Constants";
import { LockReason } from "../../../components/managespaces/utility/Enums";
import { SpacesType } from "../../../components/reuse/utility/Constants";

const expect = require("chai").expect;

let files: string[];
const controllerStub = {
  getText: (text: string, joinArgs: string[]): string => (joinArgs ? `${text}(${joinArgs})` : text),
  byId: (id: string): any => ({ id, getValue: () => "" }),
  formatDateTime: (value: string): string => BaseControllerClass.prototype.formatDateTime(value),
  getView: (): any => ({ getModel: () => ({ getData: () => [{ name, files }] }) }),
  fetchData: (): any => "",
  initializeNewSpaceDetails: (): any => "",
};

describe("src/components/managespaces/formatter/SpaceDetailsFormatter", () => {
  beforeEach(() => {
    sap.ui.getCore().setModel(new sap.ui.model.json.JSONModel({ DataHANA: State.Green }), "circuitbreaker");
  });
  describe("getSpaceTypeIconPath", () => {
    it("No arguments", async () => {
      expect(SpaceDetailsFormatter.getSpaceTypeIconPath.apply(controllerStub, [])).to.equal(SpaceTypeIconPath.DEFAULT);
    });
    it("Returns marketplace iconpath", async () => {
      expect(SpaceDetailsFormatter.getSpaceTypeIconPath.apply(controllerStub, [SpacesType.Marketplace])).to.equal(
        SpaceTypeIconPath.MARKETPLACE
      );
    });
    it("Returns abapBridge iconpath", async () => {
      expect(SpaceDetailsFormatter.getSpaceTypeIconPath.apply(controllerStub, [SpacesType.AbapBridge])).to.equal(
        SpaceTypeIconPath.ABAPBRIDGE
      );
    });
    it("Returns default iconpath", async () => {
      expect(SpaceDetailsFormatter.getSpaceTypeIconPath.apply(controllerStub, [SpacesType.Default])).to.equal(
        SpaceTypeIconPath.DEFAULT
      );
    });
  });
  describe("formatStatusValidToTimestamp", () => {
    it("No argument", async () => {
      expect(SpaceDetailsFormatter.formatStatusValidToTimestamp.apply(controllerStub, [])).to.equal(undefined);
    });
    it("Remaining hours greater than 1", async () => {
      const statusValidToUtc = new Date(Date.now() + 60 * 60 * 1000 + 1);
      expect(SpaceDetailsFormatter.formatStatusValidToTimestamp.apply(controllerStub, [statusValidToUtc])).to.equal(
        "spaceTemporarilyUnlockedMessageStrip(1,hours)"
      );
    });
    it("Remaining hours less than or equal to 1", async () => {
      const statusValidToUtc = new Date(Date.now() + 59 * 60 * 1000);
      expect(SpaceDetailsFormatter.formatStatusValidToTimestamp.apply(controllerStub, [statusValidToUtc])).to.equal(
        "spaceTemporarilyUnlockedMessageStrip(59,minutes)"
      );
    });
  });

  describe("formatSpaceID", () => {
    beforeEach(() => {
      sap.ui.getCore().setModel(new sap.ui.model.json.JSONModel({}), "featureflags");
    });
    it("Default", async () => {
      expect(SpaceDetailsFormatter.formatSpaceID.apply(controllerStub, ["ABCD123"])).to.equal("ABCD123");
    });
    it("No spaceID", async () => {
      expect(SpaceDetailsFormatter.formatSpaceID.apply(controllerStub, [undefined])).to.equal("");
    });
  });
  describe("formatSpaceTypeText", () => {
    it("No argument", async () => {
      expect(SpaceDetailsFormatter.formatSpaceTypeText.apply(controllerStub, [undefined])).to.equal("notAvailable");
      expect(SpaceDetailsFormatter.formatSpaceTypeText.apply(controllerStub, [null])).to.equal("notAvailable");
    });
    it("Returns marketplace type text", async () => {
      expect(SpaceDetailsFormatter.formatSpaceTypeText.apply(controllerStub, [SpacesType.Marketplace])).to.equal(
        SpacesType.Marketplace + "SpaceType"
      );
    });
    it("Returns abapBridge type text", async () => {
      expect(SpaceDetailsFormatter.formatSpaceTypeText.apply(controllerStub, [SpacesType.AbapBridge])).to.equal(
        SpacesType.AbapBridge + "SpaceType"
      );
    });
    it("Returns default type text", async () => {
      expect(SpaceDetailsFormatter.formatSpaceTypeText.apply(controllerStub, [SpacesType.Default])).to.equal(
        SpacesType.Default + "SpaceType"
      );
    });
  });

  describe("formatBytesToGB", () => {
    it("No argument", async () => {
      expect(SpaceDetailsFormatter.formatBytesToGB.apply(controllerStub, [])).to.equal(-1);
    });
    it("Returns formatted filesize", async () => {
      expect(SpaceDetailsFormatter.formatBytesToGB.apply(controllerStub, [byteToGb])).to.equal(1);
    });
  });

  describe("getFormatterLanguageLabel", () => {
    it("Return language label", async () => {
      expect(SpaceDetailsFormatter.getFormatterLanguageLabel.apply(controllerStub, ["en"])).to.equal("en");
    });
  });

  describe("getLockedSpaceReasonText", () => {
    it("No argument", async () => {
      expect(SpaceDetailsFormatter.getLockedSpaceReasonText.apply(controllerStub, [undefined])).to.equal(
        "spaceLockedMessageStrip"
      );
      expect(SpaceDetailsFormatter.getLockedSpaceReasonText.apply(controllerStub, [null])).to.equal(
        "spaceLockedMessageStrip"
      );
    });
    it("Invalid argument", async () => {
      expect(SpaceDetailsFormatter.getLockedSpaceReasonText.apply(controllerStub, ["invalid"])).to.equal(
        "spaceLockedMessageStrip"
      );
    });
    it("Returns manual locked reason message", async () => {
      expect(SpaceDetailsFormatter.getLockedSpaceReasonText.apply(controllerStub, [LockReason.Manual])).to.equal(
        "manualLockedMessage"
      );
    });
    it("Returns audit log locked reason message", async () => {
      expect(SpaceDetailsFormatter.getLockedSpaceReasonText.apply(controllerStub, [LockReason.AuditLog])).to.equal(
        "auditLogLockedMessage"
      );
    });
    it("Returns quota locked reason message", async () => {
      expect(SpaceDetailsFormatter.getLockedSpaceReasonText.apply(controllerStub, [LockReason.Quota])).to.equal(
        "quotaLockedMessage"
      );
    });
  });

  describe("getApprovalIconPath", () => {
    it("No argument", async () => {
      expect(SpaceDetailsFormatter.getApprovalIconPath.apply(controllerStub, [undefined])).to.contain("decline");
      expect(SpaceDetailsFormatter.getApprovalIconPath.apply(controllerStub, [null])).to.contain("decline");
    });
    it("truthy", async () => {
      expect(SpaceDetailsFormatter.getApprovalIconPath.apply(controllerStub, [true])).to.contain("accept");
      expect(SpaceDetailsFormatter.getApprovalIconPath.apply(controllerStub, ["1"])).to.contain("accept");
    });
    it("falsy", async () => {
      expect(SpaceDetailsFormatter.getApprovalIconPath.apply(controllerStub, [false])).to.contain("decline");
      expect(SpaceDetailsFormatter.getApprovalIconPath.apply(controllerStub, [0])).to.contain("decline");
    });
  });

  describe("getApprovalIconColorName", () => {
    it("No argument", async () => {
      expect(SpaceDetailsFormatter.getApprovalIconColorName.apply(controllerStub, [undefined])).to.equal(
        sap.ui.core.IconColor.Negative
      );
      expect(SpaceDetailsFormatter.getApprovalIconColorName.apply(controllerStub, [null])).to.equal(
        sap.ui.core.IconColor.Negative
      );
    });
    it("truthy", async () => {
      expect(SpaceDetailsFormatter.getApprovalIconColorName.apply(controllerStub, [true])).to.equal(
        sap.ui.core.IconColor.Positive
      );
      expect(SpaceDetailsFormatter.getApprovalIconColorName.apply(controllerStub, ["1"])).to.equal(
        sap.ui.core.IconColor.Positive
      );
    });
    it("falsy", async () => {
      expect(SpaceDetailsFormatter.getApprovalIconColorName.apply(controllerStub, [false])).to.equal(
        sap.ui.core.IconColor.Negative
      );
      expect(SpaceDetailsFormatter.getApprovalIconColorName.apply(controllerStub, [0])).to.equal(
        sap.ui.core.IconColor.Negative
      );
    });
  });

  describe("getIconTooltipText", () => {
    it("No argument", async () => {
      expect(SpaceDetailsFormatter.getIconTooltipText.apply(controllerStub, [undefined])).to.equal("disabled");
      expect(SpaceDetailsFormatter.getIconTooltipText.apply(controllerStub, [null])).to.equal("disabled");
    });
    it("truthy", async () => {
      expect(SpaceDetailsFormatter.getIconTooltipText.apply(controllerStub, [true])).to.equal("enabled");
      expect(SpaceDetailsFormatter.getIconTooltipText.apply(controllerStub, ["1"])).to.equal("enabled");
    });
    it("falsy", async () => {
      expect(SpaceDetailsFormatter.getIconTooltipText.apply(controllerStub, [false])).to.equal("disabled");
      expect(SpaceDetailsFormatter.getIconTooltipText.apply(controllerStub, [0])).to.equal("disabled");
    });
  });

  describe("getDBUserInfoIconPath", () => {
    it("No argument", async () => {
      expect(SpaceDetailsFormatter.getDBUserInfoIconPath.apply(controllerStub, [undefined])).to.contain("info");
      expect(SpaceDetailsFormatter.getDBUserInfoIconPath.apply(controllerStub, [null])).to.contain("info");
    });
    it("Locked", async () => {
      expect(
        SpaceDetailsFormatter.getDBUserInfoIconPath.apply(controllerStub, ["a", [{ username: "a", isLocked: true }]])
      ).to.contain("public");
    });
    it("not Locked", async () => {
      expect(
        SpaceDetailsFormatter.getDBUserInfoIconPath.apply(controllerStub, ["a", [{ username: "a", isLocked: false }]])
      ).to.contain("info");
    });
    it("no runtimedata", async () => {
      expect(SpaceDetailsFormatter.getDBUserInfoIconPath.apply(controllerStub, ["a", []])).to.contain("info");
    });
  });

  describe("getDatabaseUserInfoDialogTooltipText", () => {
    it("No argument", async () => {
      expect(SpaceDetailsFormatter.getDatabaseUserInfoDialogTooltipText.apply(controllerStub, [undefined])).to.contain(
        "openDatabaseUserInfoDialog"
      );
      expect(SpaceDetailsFormatter.getDatabaseUserInfoDialogTooltipText.apply(controllerStub, [null])).to.contain(
        "openDatabaseUserInfoDialog"
      );
    });
    it("Locked", async () => {
      expect(
        SpaceDetailsFormatter.getDatabaseUserInfoDialogTooltipText.apply(controllerStub, [
          "a",
          [{ username: "a", isLocked: true }],
        ])
      ).to.contain("openLockedDatabaseUserInfoDialog");
    });
    it("not Locked", async () => {
      expect(
        SpaceDetailsFormatter.getDatabaseUserInfoDialogTooltipText.apply(controllerStub, [
          "a",
          [{ username: "a", isLocked: false }],
        ])
      ).to.contain("openDatabaseUserInfoDialog");
    });
    it("no runtimedata", async () => {
      expect(SpaceDetailsFormatter.getDatabaseUserInfoDialogTooltipText.apply(controllerStub, ["a", []])).to.contain(
        "openDatabaseUserInfoDialog"
      );
    });
  });

  describe("getDBUserHighlightState", () => {
    it("No argument", async () => {
      expect(SpaceDetailsFormatter.getDBUserHighlightState.apply(controllerStub, [undefined])).to.contain(
        sap.ui.core.MessageType.None
      );
    });
    it("isNew", async () => {
      expect(
        SpaceDetailsFormatter.getDBUserHighlightState.apply(controllerStub, [
          "a",
          [{ username: "a", isLocked: false }],
          true,
        ])
      ).to.contain(sap.ui.core.MessageType.Information);
    });
    it("isLocked", async () => {
      expect(
        SpaceDetailsFormatter.getDBUserHighlightState.apply(controllerStub, [
          "a",
          [{ username: "a", isLocked: true }],
          false,
        ])
      ).to.contain(sap.ui.core.MessageType.Error);
    });
    it("not locked, not new", async () => {
      expect(
        SpaceDetailsFormatter.getDBUserHighlightState.apply(controllerStub, [
          "a",
          [{ username: "a", isLocked: false }],
          false,
        ])
      ).to.contain(sap.ui.core.MessageType.None);
    });
  });

  describe("getDBUserObjectStatusState", () => {
    it("No argument", async () => {
      expect(SpaceDetailsFormatter.getDBUserObjectStatusState.apply(controllerStub, [undefined])).to.contain(
        sap.ui.core.MessageType.None
      );
    });
    it("isLocked", async () => {
      expect(
        SpaceDetailsFormatter.getDBUserObjectStatusState.apply(controllerStub, [
          "a",
          [{ username: "a", isLocked: true }],
          false,
        ])
      ).to.contain(sap.ui.core.MessageType.Error);
    });
    it("isDeployed", async () => {
      expect(
        SpaceDetailsFormatter.getDBUserObjectStatusState.apply(controllerStub, ["a", [{ username: "a" }], false])
      ).to.contain(sap.ui.core.MessageType.Success);
    });
    it("not deployed", async () => {
      expect(SpaceDetailsFormatter.getDBUserObjectStatusState.apply(controllerStub, ["a", [], false])).to.contain(
        sap.ui.core.MessageType.None
      );
    });
  });

  describe("getDBUserStatusText", () => {
    it("No argument", async () => {
      expect(SpaceDetailsFormatter.getDBUserStatusText.apply(controllerStub, [undefined])).to.equal("notDeployed");
    });
    it("Not deployed", async () => {
      expect(SpaceDetailsFormatter.getDBUserStatusText.apply(controllerStub, ["a", []])).to.equal("notDeployed");
    });
    it("Locked", async () => {
      expect(
        SpaceDetailsFormatter.getDBUserStatusText.apply(controllerStub, ["a", [{ username: "a", isLocked: true }]])
      ).to.equal("lockedLabel");
    });
    it("Active", async () => {
      expect(
        SpaceDetailsFormatter.getDBUserStatusText.apply(controllerStub, ["a", [{ username: "a", isLocked: false }]])
      ).to.equal("activeLabel");
    });
  });

  describe("getSpaceDescriptionValueState", () => {
    it("No argument", async () => {
      expect(SpaceDetailsFormatter.getSpaceDescriptionValueState.apply(controllerStub, [undefined])).to.contain(
        sap.ui.core.ValueState.Error
      );
    });
    it("Valid", async () => {
      expect(SpaceDetailsFormatter.getSpaceDescriptionValueState.apply(controllerStub, ["a"])).to.contain(
        sap.ui.core.ValueState.None
      );
    });
    it("Invalid", async () => {
      expect(SpaceDetailsFormatter.getSpaceDescriptionValueState.apply(controllerStub, [""])).to.contain(
        sap.ui.core.ValueState.Error
      );
    });
    it("Warning", async () => {
      expect(
        SpaceDetailsFormatter.getSpaceDescriptionValueState.apply(controllerStub, [
          "a".repeat(MAX_LENGTH_SPACENAME) + 1,
        ])
      ).to.contain(sap.ui.core.ValueState.Warning);
    });
  });

  describe("getSpaceDescriptionValueStateText", () => {
    it("No argument", async () => {
      expect(SpaceDetailsFormatter.getSpaceDescriptionValueStateText.apply(controllerStub, [undefined])).to.equal(
        "spaceNameEmptyValidationError"
      );
    });
    it("Valid", async () => {
      expect(SpaceDetailsFormatter.getSpaceDescriptionValueStateText.apply(controllerStub, ["a"])).to.equal("");
    });
    it("Invalid", async () => {
      expect(SpaceDetailsFormatter.getSpaceDescriptionValueStateText.apply(controllerStub, [""])).to.equal(
        "spaceNameEmptyValidationError"
      );
    });
    it("Warning", async () => {
      expect(
        SpaceDetailsFormatter.getSpaceDescriptionValueStateText.apply(controllerStub, [
          "a".repeat(MAX_LENGTH_SPACENAME) + 1,
        ])
      ).to.equal(`spaceNameLengthWarning(${MAX_LENGTH_SPACENAME})`);
    });
  });

  describe("getAssignedRamValueState", () => {
    it("No argument", async () => {
      expect(SpaceDetailsFormatter.getAssignedRamValueState.apply(controllerStub, [undefined])).to.contain(
        sap.ui.core.ValueState.None
      );
    });
    it("Valid", async () => {
      expect(SpaceDetailsFormatter.getAssignedRamValueState.apply(controllerStub, [1, 0, 3, true])).to.contain(
        sap.ui.core.ValueState.None
      );
      expect(SpaceDetailsFormatter.getAssignedRamValueState.apply(controllerStub, [1, 0, 3, false])).to.contain(
        sap.ui.core.ValueState.None
      );
    });
    it("Warning", async () => {
      expect(SpaceDetailsFormatter.getAssignedRamValueState.apply(controllerStub, [1, 1, 2, true])).to.contain(
        sap.ui.core.ValueState.Warning
      );
      expect(SpaceDetailsFormatter.getAssignedRamValueState.apply(controllerStub, [2, 1, 2, true])).to.contain(
        sap.ui.core.ValueState.Warning
      );
    });
  });

  describe("getAssignedRamValueStateText", () => {
    it("No argument", async () => {
      expect(SpaceDetailsFormatter.getAssignedRamValueStateText.apply(controllerStub, [undefined])).to.equal("");
    });
    it("Valid", async () => {
      expect(SpaceDetailsFormatter.getAssignedRamValueStateText.apply(controllerStub, [1, 0, 3, true])).to.equal("");
      expect(SpaceDetailsFormatter.getAssignedRamValueStateText.apply(controllerStub, [1, 0, 3, false])).to.equal("");
    });
    it("Warning", async () => {
      expect(SpaceDetailsFormatter.getAssignedRamValueStateText.apply(controllerStub, [1, 1, 2, true])).to.equal(
        "minRamLimitReachedLabel"
      );
      expect(SpaceDetailsFormatter.getAssignedRamValueStateText.apply(controllerStub, [2, 1, 2, true])).to.equal(
        "ramLimitReachedLabel"
      );
    });
  });

  describe("getMaxSpaceNameLength", () => {
    it("No argument", async () => {
      expect(SpaceDetailsFormatter.getMaxSpaceNameLength.apply(controllerStub, [])).to.equal(MAX_LENGTH_SPACENAME);
    });
  });
});
