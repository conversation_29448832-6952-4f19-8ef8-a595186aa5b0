/** @format */

import { State } from "@sap/dwc-circuit-breaker/dist/types";
import { SpaceStatus } from "../../../../shared/spaces/types";
import { BaseControllerClass } from "../../../components/basecomponent/controller/BaseController.controller";
import { SpacesOverviewFormatter } from "../../../components/managespaces/formatter/SpacesOverviewFormatter";
import { NOT_INITIALIZED, SpaceHealth } from "../../../components/managespaces/utility/Constants";
import { ObjectStatus } from "../../../components/reuse/utility/Types";
const expect = require("chai").expect;

let files: string[];
const controllerStub = {
  getText: (text: string, joinArgs: string[]): string => (joinArgs ? `${text}(${joinArgs})` : text),
  byId: (id: string): any => ({ id, getValue: () => "" }),
  formatDateTime: (value: string): string => BaseControllerClass.prototype.formatDateTime(value),
  getView: (): any => ({
    getModel: () => ({
      getData: () => [{ name, files }],
      getProperty: () => {},
    }),
  }),
  fetchData: (): any => "",
  initializeNewSpaceDetails: (): any => "",
};

describe("src/components/managespaces/formatter/SpacesOverviewFormatter", () => {
  beforeEach(() => {
    sap.ui.getCore().setModel(new sap.ui.model.json.JSONModel({ DataHANA: State.Green }), "circuitbreaker");
  });
  describe("formatPercentage", () => {
    it("No argument", async () => {
      expect(SpacesOverviewFormatter.formatPercentage.apply(controllerStub, [])).to.equal(undefined);
    });
    it("Returns percent value", async () => {
      expect(SpacesOverviewFormatter.formatPercentage.apply(controllerStub, ["1"])).to.equal("1");
    });
    it("Returns percent value if HANA state is green", async () => {
      expect(SpacesOverviewFormatter.formatPercentage.apply(controllerStub, ["1"])).to.equal("1");
    });
    it("Returns 0 if HANA state is red", async () => {
      sap.ui.getCore().setModel(new sap.ui.model.json.JSONModel({ DataHANA: State.Red }), "circuitbreaker");
      expect(SpacesOverviewFormatter.formatPercentage.apply(controllerStub, ["1"])).to.equal(0);
    });
  });

  describe("formatSpaceCount", () => {
    it("No argument", async () => {
      expect(SpacesOverviewFormatter.formatSpaceCount.apply(controllerStub, [])).to.equal(NOT_INITIALIZED);
    });
    it("Returns spacecount value", async () => {
      expect(SpacesOverviewFormatter.formatSpaceCount.apply(controllerStub, ["1"])).to.equal("1");
    });
    it("Returns spacecount value if HANA state is green", async () => {
      expect(SpacesOverviewFormatter.formatSpaceCount.apply(controllerStub, ["1"])).to.equal("1");
    });
    it("Returns 0 if HANA state is red", async () => {
      sap.ui.getCore().setModel(new sap.ui.model.json.JSONModel({ DataHANA: State.Red }), "circuitbreaker");
      expect(SpacesOverviewFormatter.formatSpaceCount.apply(controllerStub, ["1"])).to.equal("-");
    });
  });

  describe("getSpacePriorityIcon", () => {
    it("No argument", async () => {
      expect(SpacesOverviewFormatter.getSpacePriorityIcon(undefined)).to.equal(undefined);
    });
    it("Returns no priority icon", async () => {
      expect(SpacesOverviewFormatter.getSpacePriorityIcon(0)).to.equal(undefined);
    });
    it("Returns low priority icon path", async () => {
      expect(SpacesOverviewFormatter.getSpacePriorityIcon(1)).to.equal("sap-icon://expand-group");
    });
    it("Returns medium priority icon path", async () => {
      expect(SpacesOverviewFormatter.getSpacePriorityIcon(3)).to.equal("sap-icon://less");
    });
    it("Returns high priority icon path", async () => {
      expect(SpacesOverviewFormatter.getSpacePriorityIcon(9)).to.equal("sap-icon://collapse-group");
    });
  });

  describe("getSpacePriorityIconTooltip", () => {
    it("No argument", async () => {
      expect(SpacesOverviewFormatter.getSpacePriorityIconTooltip.apply(controllerStub, undefined)).to.equal(undefined);
    });
    it("Returns no priority tooltip", async () => {
      expect(SpacesOverviewFormatter.getSpacePriorityIconTooltip.apply(controllerStub, [0])).to.equal(undefined);
    });
    it("Returns a priority tooltip containing '1'", async () => {
      expect(SpacesOverviewFormatter.getSpacePriorityIconTooltip.apply(controllerStub, [1])).to.contain("1");
    });
    it("Returns a priority tooltip containing '6'", async () => {
      expect(SpacesOverviewFormatter.getSpacePriorityIconTooltip.apply(controllerStub, [6])).to.contain("6");
    });
  });

  describe("getSpaceHealthColor", () => {
    it("No argument", async () => {
      expect(SpacesOverviewFormatter.getSpaceHealthColor.apply(controllerStub, [])).to.equal(SpaceHealth.Unknown);
    });
    it("Show unknown state if hana state is red", async () => {
      sap.ui.getCore().setModel(new sap.ui.model.json.JSONModel({ DataHANA: State.Red }), "circuitbreaker");
      expect(SpacesOverviewFormatter.getSpaceHealthColor.apply(controllerStub, [SpaceHealth.Cold])).to.equal(
        SpaceHealth.Unknown
      );
    });
    it("Returns cold space health color", async () => {
      expect(SpacesOverviewFormatter.getSpaceHealthColor.apply(controllerStub, [SpaceHealth.Cold])).to.equal(
        SpaceHealth.Cold
      );
    });
    it("Returns hot space health color", async () => {
      expect(SpacesOverviewFormatter.getSpaceHealthColor.apply(controllerStub, [SpaceHealth.Hot])).to.equal(
        SpaceHealth.Hot
      );
    });
    it("Returns green space health color", async () => {
      expect(SpacesOverviewFormatter.getSpaceHealthColor.apply(controllerStub, [SpaceHealth.Green])).to.equal(
        SpaceHealth.Green
      );
    });
    it("Returns locked space health color", async () => {
      expect(SpacesOverviewFormatter.getSpaceHealthColor.apply(controllerStub, [SpaceHealth.Locked])).to.equal(
        SpaceHealth.Locked
      );
    });
    it("Returns unknown space health color", async () => {
      expect(SpacesOverviewFormatter.getSpaceHealthColor.apply(controllerStub, [SpaceHealth.Unknown])).to.equal(
        SpaceHealth.Unknown
      );
    });
  });

  describe("shouldSpaceTileContentBeVisible", () => {
    it("Deployed content visible", async () => {
      expect(
        SpacesOverviewFormatter.shouldSpaceTileContentBeVisible.apply(controllerStub, ["deployedContent", 2])
      ).to.equal(true);
    });
    it("Deployed content not visible", async () => {
      expect(
        SpacesOverviewFormatter.shouldSpaceTileContentBeVisible.apply(controllerStub, ["deployedContent", 0])
      ).to.equal(false);
    });
    it("Not deployed content visible", async () => {
      expect(
        SpacesOverviewFormatter.shouldSpaceTileContentBeVisible.apply(controllerStub, ["notDeployedContent", 0])
      ).to.equal(true);
    });
    it("Not deployed content not visible", async () => {
      expect(
        SpacesOverviewFormatter.shouldSpaceTileContentBeVisible.apply(controllerStub, ["notDeployedContent", 2])
      ).to.equal(false);
    });
    it("Broken content", async () => {
      expect(
        SpacesOverviewFormatter.shouldSpaceTileContentBeVisible.apply(controllerStub, ["brokenContent", "1F"])
      ).to.equal(false);
    });
    it("Default case", async () => {
      expect(SpacesOverviewFormatter.shouldSpaceTileContentBeVisible.apply(controllerStub, ["test", 0])).to.equal(
        false
      );
    });
  });

  describe("getSpaceObjectStatusText", () => {
    it("Space status - Ok", async () => {
      expect(SpacesOverviewFormatter.getSpaceObjectStatusText.apply(controllerStub, [SpaceStatus.Ok])).to.equal(
        "okLabel"
      );
    });
    it("Space status - Cold", async () => {
      expect(SpacesOverviewFormatter.getSpaceObjectStatusText.apply(controllerStub, [SpaceStatus.Cold])).to.equal(
        "coldLabel"
      );
    });
    it("Space status - Critical", async () => {
      expect(SpacesOverviewFormatter.getSpaceObjectStatusText.apply(controllerStub, [SpaceStatus.Critical])).to.equal(
        "criticalLabel"
      );
    });
    it("Space status - Locked", async () => {
      expect(SpacesOverviewFormatter.getSpaceObjectStatusText.apply(controllerStub, [SpaceStatus.Locked])).to.equal(
        "lockedLabel"
      );
    });
    it("Space status - Unknown", async () => {
      expect(SpacesOverviewFormatter.getSpaceObjectStatusText.apply(controllerStub, [SpaceStatus.Unknown])).to.equal(
        "unknownLabel"
      );
    });
  });

  describe("getSpaceObjectStatusState", () => {
    it("Space status - Ok", async () => {
      expect(SpacesOverviewFormatter.getSpaceObjectStatusState(SpaceStatus.Ok)).to.equal(
        sap.ui.core.ValueState.Success
      );
    });
    it("Space status - Cold", async () => {
      expect(SpacesOverviewFormatter.getSpaceObjectStatusState(SpaceStatus.Cold)).to.equal(
        sap.ui.core.ValueState.Information
      );
    });
    it("Space status - Critical", async () => {
      expect(SpacesOverviewFormatter.getSpaceObjectStatusState(SpaceStatus.Critical)).to.equal(
        sap.ui.core.ValueState.Warning
      );
    });
    it("Space status - Locked", async () => {
      expect(SpacesOverviewFormatter.getSpaceObjectStatusState(SpaceStatus.Locked)).to.equal(
        sap.ui.core.ValueState.Error
      );
    });
    it("Space status - Unknown", async () => {
      expect(SpacesOverviewFormatter.getSpaceObjectStatusState(SpaceStatus.Unknown)).to.equal(
        sap.ui.core.ValueState.None
      );
    });
  });

  describe("getStorageLabel", () => {
    it("No argument", async () => {
      expect(SpacesOverviewFormatter.getStorageLabel.apply(controllerStub, [])).to.equal("undefined(-,-)");
    });
    it("Storage quota assigned", async () => {
      sap.ui.getCore().setModel(new sap.ui.model.json.JSONModel({ DataHANA: State.Red }), "circuitbreaker");
      expect(
        SpacesOverviewFormatter.getStorageLabel.apply(controllerStub, ["usedStorageTemplate", 100000000, 200000000])
      ).to.equal("usedStorageTemplate(-,-)");
    });
    it("RAM quota assigned", async () => {
      sap.ui.getCore().setModel(new sap.ui.model.json.JSONModel({ DataHANA: State.Red }), "circuitbreaker");
      expect(
        SpacesOverviewFormatter.getStorageLabel.apply(controllerStub, ["usedRAMTemplate", 100000000, 200000000])
      ).to.equal("usedRAMTemplate(-,-)");
    });
  });

  describe("getFormattedValue", () => {
    it("HANA state red", async () => {
      sap.ui.getCore().setModel(new sap.ui.model.json.JSONModel({ DataHANA: State.Red }), "circuitbreaker");
      expect(SpacesOverviewFormatter.getFormattedValue(10000, State.Red)).to.equal("-");
    });
    it("HANA state green and value initialized", async () => {
      expect(SpacesOverviewFormatter.getFormattedValue(10000, State.Green)).to.equal("10 KB");
    });
    it("HANA state green and value not initialized", async () => {
      expect(SpacesOverviewFormatter.getFormattedValue(-1, State.Green)).to.equal("---");
    });
  });

  describe("getStorageProgressIndicatorLabel", () => {
    it("No arguments", async () => {
      expect(SpacesOverviewFormatter.getStorageProgressIndicatorLabel.apply(controllerStub, [])).to.equal("");
    });
    it("Hana state red", async () => {
      sap.ui.getCore().setModel(new sap.ui.model.json.JSONModel({ DataHANA: State.Red }), "circuitbreaker");
      expect(SpacesOverviewFormatter.getStorageProgressIndicatorLabel.apply(controllerStub, [100000, 0])).to.equal(
        "ofTemplate(-,-)"
      );
    });
    it("Hana state green with assigned value is zero", async () => {
      expect(SpacesOverviewFormatter.getStorageProgressIndicatorLabel.apply(controllerStub, [100000, 0])).to.equal(
        "ofTemplate(100 KB,---)"
      );
    });
    it("Hana state green with assigned value is not zero", async () => {
      expect(SpacesOverviewFormatter.getStorageProgressIndicatorLabel.apply(controllerStub, [100000, 200000])).to.equal(
        "ofTemplate(100 KB,200 KB)"
      );
    });
  });

  describe("getStorageProgressIndicatorState", () => {
    it("No arguments", async () => {
      expect(SpacesOverviewFormatter.getStorageProgressIndicatorState.apply(controllerStub, [])).to.equal("None");
    });
    it("percent equal to 100", async () => {
      expect(SpacesOverviewFormatter.getStorageProgressIndicatorState.apply(controllerStub, [100])).to.equal("Warning");
    });
    it("percent less than 100", async () => {
      expect(SpacesOverviewFormatter.getStorageProgressIndicatorState.apply(controllerStub, [80])).to.equal("None");
    });
  });

  describe("shouldCreateSpaceBeEnabled", () => {
    it("No arguments", async () => {
      expect(SpacesOverviewFormatter.shouldCreateSpaceBeEnabled.apply(controllerStub, [])).to.equal(false);
    });
    it("Either of spaces or resources loaded", async () => {
      expect(SpacesOverviewFormatter.shouldCreateSpaceBeEnabled.apply(controllerStub, [true, false])).to.equal(false);
    });
    it("Both, spaces and resources loaded and Hana state red successfully", async () => {
      sap.ui.getCore().setModel(new sap.ui.model.json.JSONModel({ DataHANA: State.Red }), "circuitbreaker");
      expect(SpacesOverviewFormatter.shouldCreateSpaceBeEnabled.apply(controllerStub, [true, true])).to.equal(false);
    });
    it("Both, spaces and resources loaded and Hana state green successfully", async () => {
      expect(SpacesOverviewFormatter.shouldCreateSpaceBeEnabled.apply(controllerStub, [true, true])).to.equal(true);
    });
  });

  describe("getSpaceCardDeployStatusText", () => {
    it("No arguments", async () => {
      expect(SpacesOverviewFormatter.getSpaceCardDeployStatusText.apply(controllerStub, [])).to.equal(
        controllerStub.getText("notAvailable", null)
      );
    });
    describe("deployed", () => {
      it("with date", async () => {
        expect(
          SpacesOverviewFormatter.getSpaceCardDeployStatusText.apply(controllerStub, [ObjectStatus.deployed, "1"])
        ).to.contain("deployedOn Jan 1, 2001");
      });
      it("without date", async () => {
        expect(
          SpacesOverviewFormatter.getSpaceCardDeployStatusText.apply(controllerStub, [ObjectStatus.deployed])
        ).to.equal(controllerStub.getText("deployed", null));
      });
    });
    it("changesToDeploy", async () => {
      expect(
        SpacesOverviewFormatter.getSpaceCardDeployStatusText.apply(controllerStub, [ObjectStatus.changesToDeploy])
      ).to.equal(controllerStub.getText("changesToDeploy", null));
    });
  });
});
