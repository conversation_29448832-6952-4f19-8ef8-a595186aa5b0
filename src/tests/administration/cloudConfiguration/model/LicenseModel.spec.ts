/** @format */

import { expect } from "chai";
import sinon from "sinon";
import { LargeSystemsFeatureState } from "../../../../components/administration/cloudConfiguration/featureState/LargeSystemsFeatureState";
import { LicenseModel } from "../../../../components/reuse/utility/LicenseModel";
import { MockJSONModel } from "./MockJSONModel";

describe("Tenant Configuration License Model", () => {
  let sandbox: sinon.SinonSandbox;
  let licensesModel: LicenseModel;
  let lsaEnabledStub: sinon.SinonStub;
  let lsaRequestsEnabledStub: sinon.SinonStub;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    const model = new MockJSONModel();
    licensesModel = new LicenseModel(model as any);
    licensesModel.setInitialData();

    lsaEnabledStub = sandbox.stub(LargeSystemsFeatureState, "isLargeSystemsEnabled").returns(false);
    lsaRequestsEnabledStub = sandbox.stub(LargeSystemsFeatureState, "isLargeSystemsRequestsEnabled").returns(false);
  });
  afterEach(() => {
    sandbox.restore();
  });

  describe("setInitialData", () => {
    it("should initialize empty data", () => {
      const data = licensesModel.getInnerModel().getData();
      expect(Object.keys(data).length).to.be.equal(0);
    });
  });

  describe("buildPayload", () => {
    it("should build payload with basic properties when FFs are off", () => {
      const payload = licensesModel.buildPayload();
      expect(payload).to.have.property("thresholdMemory");
      expect(payload).to.have.property("thresholdVCPU");
      expect(payload).to.have.property("thresholdStorage");
      expect(payload).to.have.property("thresholdDataLakeStorage");
      expect(payload).to.have.property("thresholdBWBridge1");
      expect(payload).to.have.property("thresholdRmsNodeHours");
      expect(payload).to.have.property("thresholdRmsPremiumOutbound");
      expect(payload).to.have.property("thresholdCatalogStorage");
      expect(payload).to.have.property("options");
      expect(payload).to.have.property("thresholdECNBlock");
      expect(payload).to.have.property("thresholdECNPerformanceClass");

      expect(payload).to.not.have.property("thresholdDWCCU");
    });
    it("should build payload with HDLF properties when LSA FF is on", () => {
      lsaEnabledStub.returns(true);
      const payload = licensesModel.buildPayload();
      expect(payload).to.have.property("thresholdLargeSystemsStorage");
    });
    it("should build payload with Spark properties when LSA FF is on", () => {
      lsaEnabledStub.returns(true);
      const payload = licensesModel.buildPayload();
      expect(payload).to.have.property("thresholdLargeSystemsCompute");
    });
    it("should build payload with API Calls properties when LSA Requests FF is on", () => {
      lsaRequestsEnabledStub.returns(true);
      const payload = licensesModel.buildPayload();
      expect(payload).to.have.property("thresholdLargeSystemsRequests");
    });
  });

  describe("buildLicense", () => {
    it("should build license with basic properties when FFs are off", () => {
      const payload = licensesModel.buildLicense();
      expect(payload).to.have.property("thresholdDWCCU");
      expect(payload).to.have.property("thresholdMemory");
      expect(payload).to.have.property("thresholdVCPU");
      expect(payload).to.have.property("thresholdStorage");
      expect(payload).to.have.property("thresholdDataLakeStorage");
      expect(payload).to.have.property("thresholdBWBridge1");
      expect(payload).to.have.property("thresholdRmsNodeHours");
      expect(payload).to.have.property("thresholdRmsPremiumOutbound");
      expect(payload).to.have.property("thresholdCatalogStorage");
      expect(payload).to.have.property("thresholdECNBlock");
      expect(payload).to.have.property("thresholdECNPerformanceClass");
      expect(payload).to.have.property("thresholdCatalogStorageIncluded");
      expect(payload).to.have.property("thresholdRmsNodeHoursIncluded");
      expect(payload).to.have.property("thresholdCatalogConcurrency");
      expect(payload).to.have.property("thresholdCatalogConcurrencyIncluded");
      expect(payload).to.have.property("thresholdCatalogNodeHoursIncluded");
      expect(payload).to.have.property("thresholdDataLakeCompute");
      expect(payload).to.have.property("thresholdRmsConcurrency");
      expect(payload).to.have.property("thresholdRmsConcurrencyIncluded");
      expect(payload).to.have.property("thresholdRmsNodeHoursIncluded");

      expect(payload).to.not.have.property("options");
    });
    it("should build payload with HDLF properties when LSA FF is on", () => {
      lsaEnabledStub.returns(true);
      const payload = licensesModel.buildLicense();
      expect(payload).to.have.property("thresholdLargeSystemsStorage");
    });
    it("should build payload with Spark properties when LSA FF is on", () => {
      lsaEnabledStub.returns(true);
      const payload = licensesModel.buildLicense();
      expect(payload).to.have.property("thresholdLargeSystemsCompute");
    });
    it("should build payload with API Calls properties when LSA Requests FF is on", () => {
      lsaRequestsEnabledStub.returns(true);
      const payload = licensesModel.buildLicense();
      expect(payload).to.have.property("thresholdLargeSystemsRequests");
    });
  });

  describe("isTenantLicenseConfigured", () => {
    it("should return false when no properties are set", () => {
      expect(licensesModel.isTenantLicenseConfigured()).to.be.false;
    });
    it("should return true when storage is set", () => {
      licensesModel.setProperty("thresholdStorage", 1);
      expect(licensesModel.isTenantLicenseConfigured()).to.be.true;
    });
    it("should return false when memory/vcpu are set but storage isn't", () => {
      licensesModel.setProperty("thresholdMemory", 1);
      licensesModel.setProperty("thresholdVCPU", 1);
      expect(licensesModel.isTenantLicenseConfigured()).to.be.false;
    });
  });
});
