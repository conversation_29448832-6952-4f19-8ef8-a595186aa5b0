/** @format */

import { expect } from "chai";
import sinon, { SinonSandbox } from "sinon";
import { DEFAULT_ECN_PERFORMANCE_CLASS } from "../../../../../shared/provisioning/ftc/defaultValues";
import { FtcParameters as Names, PerformanceClass } from "../../../../../shared/provisioning/ftc/types";
import { BwBridgeNewSizesFeatureState } from "../../../../components/administration/cloudConfiguration/featureState/BwBridgeNewSizesFeatureState";
import { FtcHanaMultiAZFeatureState } from "../../../../components/administration/cloudConfiguration/featureState/FtcHanaMultiAZFeatureState";
import { LargeSystemsFeatureState } from "../../../../components/administration/cloudConfiguration/featureState/LargeSystemsFeatureState";
import { UserCustomizationModel } from "../../../../components/administration/cloudConfiguration/model/UserCustomization";
import { ShellContainer } from "../../../../components/shell/utility/Container";
import { MockJSONModel } from "./MockJSONModel";

describe("User Customization Model", () => {
  let model;
  let ucModel: UserCustomizationModel;
  let sandbox: SinonSandbox;
  let lsaEnabledStub: sinon.SinonStub;
  let lsaRequestsEnabledStub: sinon.SinonStub;
  let multiAZEnabledStub: sinon.SinonStub;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    lsaEnabledStub = sandbox.stub(LargeSystemsFeatureState, "isLargeSystemsEnabled").returns(false);
    lsaRequestsEnabledStub = sandbox.stub(LargeSystemsFeatureState, "isLargeSystemsRequestsEnabled").returns(false);
    multiAZEnabledStub = sandbox.stub(FtcHanaMultiAZFeatureState, "isFtcHanaMultiAZEnabled").returns(false);
    sandbox.stub(BwBridgeNewSizesFeatureState, "isBwBridgeNewSizesEnabled").returns(false);
    model = new MockJSONModel();
    ucModel = new UserCustomizationModel(model);
    ucModel.setInitialData();
  });

  afterEach(() => {
    sandbox.restore();
  });

  context("Without FF", () => {
    context("Initialization", () => {
      it("should set empty initial data", () => {
        expect(ucModel.getInnerModel().getData()).to.deep.equal({
          "/thresholdMemory": 0,
          "/thresholdVCPU": 0,
          "/thresholdStorage": 0,
          "/thresholdDataLakeStorage": 0,
          "/thresholdBWBridge1": 0,
          "/thresholdCatalogStorage": 0,
          "/thresholdRmsNodeHours": 0,
          "/thresholdRmsPremiumOutbound": 0,
          "/thresholdECNBlock": 0,
          "/thresholdECNPerformanceClass": DEFAULT_ECN_PERFORMANCE_CLASS,
          "/deleteDataLake": false,
          "/isHanaScriptServerEnabled": false,
          "/calculatedCUs": 0,
          "/calculatedCUsEstimate": {
            averageHourlyEstimate: 0,
            monthlyEstimate: 0,
          },
          "/performanceClass": PerformanceClass.MEMORY,
        });
      });
    });
    context("Configuration Setting", () => {
      beforeEach(() => {
        sandbox.stub(ShellContainer.get(), "getFeatureFlagService").returns({ getFeatureValue: () => true } as any);
      });
      afterEach(() => {
        sandbox.restore();
      });
      it("should set the basic configuration from an object", () => {
        ucModel.setConfiguration({
          thresholdBWBridge1: 2,
          thresholdMemory: 256,
          thresholdVCPU: 16,
          thresholdDataLakeStorage: 0,
          thresholdStorage: 6,
          thresholdRmsPremiumOutbound: 10,
          thresholdCatalogStorage: 1024,
        });
        expect(ucModel.getInnerModel().getData()).to.have.property("/thresholdBWBridge1").that.equals(2);
        expect(ucModel.getInnerModel().getData()).to.have.property("/thresholdDataLakeStorage").that.equals(0);
        expect(ucModel.getInnerModel().getData()).to.have.property("/thresholdStorage").that.equals(6);
        expect(ucModel.getInnerModel().getData()).to.have.property("/thresholdCatalogStorage").that.equals(1);
        expect(ucModel.getInnerModel().getData()).to.have.property("/thresholdRmsPremiumOutbound").that.equals(10);
        expect(ucModel.getInnerModel().getData()).to.have.property("/thresholdMemory").that.equals(256);
        expect(ucModel.getInnerModel().getData()).to.have.property("/thresholdVCPU").that.equals(16);
        expect(ucModel.getInnerModel().getData())
          .to.have.property("/performanceClass")
          .that.equals(PerformanceClass.MEMORY);
      });
    });
    context("Bridge specific cases", () => {
      it("should cast to number", () => {
        ucModel.setProperty(Names.thresholdBWBridge1, "256" as any);
        expect(ucModel.getProperty(Names.thresholdBWBridge1)).to.be.a("number");
      });
      it("should check if item comes as object and convert if needed", () => {
        ucModel.setProperty(Names.thresholdBWBridge1, "256" as any);
        expect(ucModel.getProperty(Names.thresholdBWBridge1)).to.be.a("number");
        ucModel.setProperty(Names.thresholdBWBridge1, { value: "256" } as any);
        expect(ucModel.getProperty(Names.thresholdBWBridge1)).to.be.a("number");
        expect(ucModel.getProperty(Names.thresholdBWBridge1)).to.be.equal(256);
      });
    });
  });
  context("Premium Outbound", () => {
    context("Initialization", () => {
      it("should set empty initial data", () => {
        expect(ucModel.getInnerModel().getData()).to.deep.equal({
          "/thresholdMemory": 0,
          "/thresholdVCPU": 0,
          "/thresholdStorage": 0,
          "/thresholdDataLakeStorage": 0,
          "/thresholdBWBridge1": 0,
          "/thresholdCatalogStorage": 0,
          "/thresholdRmsNodeHours": 0,
          "/thresholdRmsPremiumOutbound": 0,
          "/thresholdECNBlock": 0,
          "/thresholdECNPerformanceClass": DEFAULT_ECN_PERFORMANCE_CLASS,
          "/isHanaScriptServerEnabled": false,
          "/deleteDataLake": false,
          "/calculatedCUs": 0,
          "/calculatedCUsEstimate": {
            averageHourlyEstimate: 0,
            monthlyEstimate: 0,
          },
          "/performanceClass": PerformanceClass.MEMORY,
        });
      });
    });
    context("Configuration Setting", () => {
      it("should set the basic configuration from an object", () => {
        sandbox.stub(ShellContainer.get(), "getFeatureFlagService").returns({ getFeatureValue: () => true } as any);

        ucModel.setConfiguration({
          thresholdBWBridge1: 2,
          thresholdDataLakeStorage: 0,
          thresholdStorage: 6,
          thresholdMemory: 256,
          thresholdVCPU: 16,
          thresholdRmsPremiumOutbound: 2,
          thresholdCatalogStorage: 1024,
        });
        expect(ucModel.getInnerModel().getData()).to.have.property("/thresholdBWBridge1").that.equals(2);
        expect(ucModel.getInnerModel().getData()).to.have.property("/thresholdDataLakeStorage").that.equals(0);
        expect(ucModel.getInnerModel().getData()).to.have.property("/thresholdStorage").that.equals(6);
        expect(ucModel.getInnerModel().getData()).to.have.property("/thresholdRmsPremiumOutbound").that.equals(2);
        expect(ucModel.getInnerModel().getData()).to.have.property("/thresholdCatalogStorage").that.equals(1);
        expect(ucModel.getInnerModel().getData()).to.have.property("/thresholdMemory").that.equals(256);
        expect(ucModel.getInnerModel().getData()).to.have.property("/thresholdVCPU").that.equals(16);
      });
    });
  });
  context("HANA Script Server", () => {
    it("should include isHanaScriptServerEnabled", () => {
      sandbox.stub(ShellContainer.get(), "getFeatureFlagService").returns({ getFeatureValue: () => true } as any);
      ucModel.setProperty(Names.isHanaScriptServerEnabled, true);
      expect(ucModel.buildFormPayload().options).to.have.property(Names.isHanaScriptServerEnabled).that.equals(true);
    });
  });
  context("After Commercial Reset", () => {
    context("Initialization", () => {
      it("should set empty initial data", () => {
        expect(ucModel.getInnerModel().getData()).to.deep.equal({
          "/thresholdStorage": 0,
          "/thresholdDataLakeStorage": 0,
          "/thresholdBWBridge1": 0,
          "/thresholdCatalogStorage": 0,
          "/thresholdRmsNodeHours": 0,
          "/thresholdRmsPremiumOutbound": 0,
          "/thresholdECNBlock": 0,
          "/thresholdECNPerformanceClass": DEFAULT_ECN_PERFORMANCE_CLASS,
          "/deleteDataLake": false,
          "/isHanaScriptServerEnabled": false,
          "/calculatedCUs": 0,
          "/calculatedCUsEstimate": {
            averageHourlyEstimate: 0,
            monthlyEstimate: 0,
          },
          "/performanceClass": PerformanceClass.MEMORY,
          "/thresholdMemory": 0,
          "/thresholdVCPU": 0,
        });
      });
    });
    context("Configuration Setting", () => {
      beforeEach(() => {
        sandbox.stub(ShellContainer.get(), "getFeatureFlagService").returns({ getFeatureValue: () => true } as any);
      });
      afterEach(() => {
        sandbox.restore();
      });
      it("should set the basic configuration from an object", () => {
        ucModel.setConfiguration({
          thresholdBWBridge1: 2,
          thresholdDataLakeStorage: 0,
          thresholdStorage: 6,
          thresholdCatalogStorage: 1024,
          thresholdMemory: 32,
          thresholdVCPU: 2,
        });
        expect(ucModel.getInnerModel().getData()).to.have.property("/thresholdBWBridge1").that.equals(2);
        expect(ucModel.getInnerModel().getData()).to.have.property("/thresholdDataLakeStorage").that.equals(0);
        expect(ucModel.getInnerModel().getData()).to.have.property("/thresholdStorage").that.equals(6);
        expect(ucModel.getInnerModel().getData()).to.have.property("/thresholdCatalogStorage").that.equals(1);
        expect(ucModel.getInnerModel().getData())
          .to.have.property("/performanceClass")
          .that.equals(PerformanceClass.MEMORY);
        expect(ucModel.getInnerModel().getData()).to.have.property("/thresholdMemory").that.equals(32);
        expect(ucModel.getInnerModel().getData()).to.have.property("/thresholdVCPU").that.equals(2);
      });
    });
    context("Get Active Values", () => {
      let sandbox: SinonSandbox;
      let isFeatureOn: boolean;
      beforeEach(() => {
        isFeatureOn = true;
        sandbox = sinon.createSandbox();
        sandbox
          .stub(ShellContainer.get(), "getFeatureFlagService")
          .returns({ getFeatureValue: () => isFeatureOn } as any);
        ucModel.setProperty(Names.thresholdMemory, 3600);
        ucModel.setProperty(Names.thresholdVCPU, 120);
        ucModel.setProperty(Names.thresholdStorage, 2);
        ucModel.setProperty(Names.thresholdBWBridge1, 5);
        ucModel.setProperty(Names.thresholdDataLakeStorage, 5);
        ucModel.setProperty(Names.thresholdRmsNodeHours, 0);
        ucModel.setProperty(Names.thresholdCatalogStorage, 1);
        ucModel.setProperty(Names.thresholdRmsPremiumOutbound, 10);
        ucModel.setProperty(Names.thresholdECNBlock, 10);
        ucModel.setProperty(Names.thresholdECNPerformanceClass, DEFAULT_ECN_PERFORMANCE_CLASS);
      });
      afterEach(() => {
        sandbox.restore();
      });
      it(".buildFormPayload should build object with active values", () => {
        expect(ucModel.buildFormPayload()).to.deep.equal({
          [Names.thresholdBWBridge1]: 5,
          [Names.thresholdDataLakeStorage]: 5,
          [Names.thresholdStorage]: 2,
          [Names.thresholdRmsNodeHours]: 0,
          [Names.thresholdCatalogStorage]: 1024,
          [Names.thresholdMemory]: 3600,
          [Names.thresholdVCPU]: 120,
          [Names.thresholdRmsPremiumOutbound]: 10,
          [Names.thresholdECNBlock]: 10,
          [Names.thresholdECNPerformanceClass]: DEFAULT_ECN_PERFORMANCE_CLASS,
          options: {
            [Names.deleteDataLake]: false,
            [Names.isHanaScriptServerEnabled]: false,
          },
        });
        expect(ucModel.getInnerModel().getData())
          .to.have.property("/performanceClass")
          .that.equals(PerformanceClass.MEMORY);
      });
    });
  });
  context("Large Systems", () => {
    beforeEach(() => {
      lsaEnabledStub.returns(true);
      lsaRequestsEnabledStub.returns(true);
    });
    it("should include LSA licenses", () => {
      ucModel.setProperty(Names.thresholdLargeSystemsStorage, 2);
      ucModel.setProperty(Names.thresholdLargeSystemsCompute, 2);

      expect(ucModel.buildFormPayload()).to.have.property(Names.thresholdLargeSystemsStorage).that.equals(2);
      expect(ucModel.buildFormPayload()).to.have.property(Names.thresholdLargeSystemsCompute).that.equals(2);
    });
  });
  context("HANA Multi AZ", () => {
    beforeEach(() => {
      multiAZEnabledStub.returns(true);
    });

    it("should have isHanaMultiAZEnabled in empty data", () => {
      ucModel.setInitialData();
      expect(ucModel.getInnerModel().getData()).to.deep.equal({
        "/thresholdMemory": 0,
        "/thresholdVCPU": 0,
        "/thresholdStorage": 0,
        "/thresholdDataLakeStorage": 0,
        "/thresholdBWBridge1": 0,
        "/thresholdCatalogStorage": 0,
        "/thresholdRmsNodeHours": 0,
        "/thresholdRmsPremiumOutbound": 0,
        "/thresholdECNBlock": 0,
        "/thresholdECNPerformanceClass": DEFAULT_ECN_PERFORMANCE_CLASS,
        "/deleteDataLake": false,
        "/isHanaScriptServerEnabled": false,
        "/isHanaMultiAZEnabled": false,
        "/calculatedCUs": 0,
        "/calculatedCUsEstimate": {
          averageHourlyEstimate: 0,
          monthlyEstimate: 0,
        },
        "/performanceClass": PerformanceClass.MEMORY,
      });
    });
    it("should have isHanaMultiAZEnabled in form payload", () => {
      ucModel.setProperty(Names.isHanaMultiAZEnabled, false);
      expect(ucModel.buildFormPayload().options).to.have.property(Names.isHanaMultiAZEnabled).that.equals(false);
      ucModel.setProperty(Names.isHanaMultiAZEnabled, true);
      expect(ucModel.buildFormPayload().options).to.have.property(Names.isHanaMultiAZEnabled).that.equals(true);
    });
  });
});
