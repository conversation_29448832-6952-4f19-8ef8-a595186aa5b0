/**
 *
 * @format
 */

import { Hyperscaler, Regions } from "@sap/dwc-flexible-tenant-sizing";
import { expect } from "chai";
import sinon, { SinonSandbox, SinonStubbedInstance } from "sinon";
import { ScalingErrors } from "../../../../shared/provisioning/ftc/types";
import { FTCConfigurationValidator } from "../../../components/administration/cloudConfiguration/FTCConfigurationValidator";
import { FTCModels } from "../../../components/administration/cloudConfiguration/model/FTCModelMap";
import { ViewModel } from "../../../components/administration/cloudConfiguration/model/ViewModel";
import { LicenseModel } from "../../../components/reuse/utility/LicenseModel";
import { mockFTCModelsMap } from "./rules/mockHelper";

describe("FTC Configuration Validator", () => {
  let sandbox: SinonSandbox;
  let licensesModel: SinonStubbedInstance<LicenseModel>;
  let viewModel: SinonStubbedInstance<ViewModel>;
  let ftcModels: SinonStubbedInstance<FTCModels>;
  let featureFlags: Record<string, boolean> = {};

  const initialLicense = {
    thresholdStorage: 256,
    thresholdMemory: 64,
    thresholdVCPU: 4,
  };

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    viewModel = sandbox.createStubInstance(ViewModel);
    licensesModel = sandbox.createStubInstance(LicenseModel);
    ftcModels = sandbox.createStubInstance(FTCModels);

    mockFTCModelsMap(ftcModels, {
      ViewModel: viewModel,
      LicensesModel: licensesModel,
    });

    viewModel.getProperty.withArgs("hyperscaler").returns(Hyperscaler.AWS);
    viewModel.getProperty.withArgs("region").returns(Regions.eu10);
    licensesModel.buildLicense.returns(initialLicense as any);
  });
  afterEach(() => {
    sandbox.restore();
  });

  it("should return OutOfRange error and ignore remaining validations", async () => {
    const payload = {
      thresholdStorage: 129,
      thresholdMemory: 32,
      thresholdVCPU: 2,
    };
    const ftcValidator = new FTCConfigurationValidator(payload as any, ftcModels, featureFlags);
    const result = await ftcValidator.validate();
    expect(result).to.include(ScalingErrors.OutOfRange);
  });

  it("should run remaining validations and return errors", async () => {
    const payload = {
      thresholdStorage: 128,
      thresholdMemory: 32,
      thresholdVCPU: 2,
    };
    const ftcValidator = new FTCConfigurationValidator(payload as any, ftcModels, featureFlags);
    const result = await ftcValidator.validate();
    expect(result).to.include(ScalingErrors.StorageDownsize);
  });
});
