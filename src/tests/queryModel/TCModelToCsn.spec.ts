/**
 * eslint-disable @typescript-eslint/unbound-method
 *
 * @format
 */

/* eslint-disable no-underscore-dangle */
import assert from "assert";
import {
  NullSupportedFeatureService,
  SupportedFeaturesService,
} from "../../../src/components/commonmodel/api/SupportedFeaturesService";
import { ModelToJSON } from "../../components/taskchainmodeler/js/model/ModelToJSON";
import Utils from "../../components/taskchainmodeler/js/utils";
import "../support/TCModelLoader";

const nsTCBuilder = sap.cdw.taskchainmodeler as any;

describe("TaskChain Model to Csn", () => {
  function createModel() {
    const resource = new sap.galilei.model.Resource(nsTCBuilder.ModelImpl.UNIQUE_CSN_RESOURCE_ID);
    const model = new sap.cdw.taskchainmodeler.Model(resource);
    resource.model = model;
    nsTCBuilder.ModelImpl.getOrCreateStartObject(model);
    const diagram = new sap.cdw.taskchainmodeler.ui.Diagram(resource, { model: model });
    model.diagrams.push(diagram);
    return model;
  }

  function createOperation(oModel, obj) {
    return sap.cdw.taskchainmodeler.ModelImpl.createObject("sap.cdw.taskchainmodeler.Operation", obj, oModel);
  }

  function createTask(oModel, obj) {
    const sTaskClassName = "sap.cdw.taskchainmodeler.Task";
    let oTask = sap.cdw.taskchainmodeler.ModelImpl.createObject(sTaskClassName, obj, oModel);
    if (obj.isSQLScriptProcedure === true) {
      sap.cdw.taskchainmodeler.ModelImpl.createParameters(oTask, obj.parameter);
    }
    if (Utils.canSupportDeleteData()) {
      oTask.resource.model.applicationIdActivity = [
        {
          applicationId: "REMOTE_TABLES",
          activity: "REPLICATE",
          technicalType: "DWC_REMOTE_TABLE",
        },
        {
          applicationId: "VIEWS",
          activity: "PERSIST",
          technicalType: "DWC_VIEW",
        },
        {
          applicationId: "REPLICATION_FLOWS",
          activity: "EXECUTE",
          technicalType: "DWC_REPLICATIONFLOW",
        },
        {
          applicationId: "TASK_CHAINS",
          activity: "RUN_CHAIN",
          technicalType: "DWC_TASKCHAIN",
        },
        {
          applicationId: "DATA_FLOWS",
          activity: "EXECUTE",
          technicalType: "DWC_DATAFLOW",
        },
        {
          applicationId: "LOCAL_TABLE",
          activity: "REMOVE_DELETED_RECORDS",
          technicalType: "DWC_LOCAL_TABLE",
        },
        {
          applicationId: "TRANSFORMATION_FLOWS",
          activity: "EXECUTE",
          technicalType: "DWC_TRANSFORMATIONFLOW",
        },
        {
          applicationId: "INTELLIGENT_LOOKUP",
          activity: "EXECUTE",
          technicalType: "DWC_IDT",
        },
        {
          applicationId: "SQL_SCRIPT_PROCEDURE",
          activity: "RUN",
        },
        {
          applicationId: "BW_PROCESS_CHAIN",
          activity: "RUN",
        },
        {
          applicationId: "REMOTE_TABLES",
          activity: "REMOVE_REPLICATED_DATA",
          technicalType: "DWC_REMOTE_TABLE",
        },
        {
          applicationId: "VIEWS",
          activity: "REMOVE_PERSISTED_DATA",
          technicalType: "DWC_VIEW",
        },
        {
          applicationId: "API",
          activity: "RUN",
        },
      ];
    }
    if (obj.isRestApi === true) {
      oTask.resource.model.connectionList = [
        {
          businessName: "ClientCredentialJSONReqType",
          technicalName: "ClientCredentialJSONReqType",
          url: "https://tf-rest-api.cfapps.sap.hana.ondemand.com/api/auth",
          ccmReplicationStatus: "SUCCESSFULLY_REPLICATED",
        },
        {
          businessName: "ClientCredentials",
          technicalName: "ClientCredentials",
          url: "https://tf-rest-api.cfapps.sap.hana.ondemand.com/api/auth",
          ccmReplicationStatus: "SUCCESSFULLY_REPLICATED",
        },
        {
          businessName: "NoAuth",
          technicalName: "NoAuth",
          url: "https://tf-rest-api.cfapps.sap.hana.ondemand.com/api/open",
          ccmReplicationStatus: "SUCCESSFULLY_REPLICATED",
        },
        {
          businessName: "UserAndPassword",
          technicalName: "UserAndPassword",
          url: "https://tf-rest-api.cfapps.sap.hana.ondemand.com/api/auth",
          ccmReplicationStatus: "SUCCESSFULLY_REPLICATED",
        },
        {
          businessName: "NoAuthCC",
          technicalName: "NoAuthCC",
          url: "https://virtual.tf-rest-api.cfapps.sap.hana.ondemand.com:8443/api/open",
          ccmReplicationStatus: "SUCCESSFULLY_REPLICATED",
        },
        {
          businessName: "x509",
          technicalName: "x509",
          url: "https://tf-rest-api.cfapps.sap.hana.ondemand.com/api/auth",
          ccmReplicationStatus: "SUCCESSFULLY_REPLICATED",
        },
        {
          businessName: "x509JSONReqType",
          technicalName: "x509JSONReqType",
          url: "https://tf-rest-api.cfapps.sap.hana.ondemand.com/api/auth",
          ccmReplicationStatus: "SUCCESSFULLY_REPLICATED",
        },
      ];
      sap.cdw.taskchainmodeler.DiagramImpl.createRestApiTask(obj, oTask, oModel);
    }
    return oTask;
  }

  function onPostCreateTask(oTask, oParentTask?, oOptions?) {
    sap.cdw.taskchainmodeler.ModelImpl.onPostDropDataSource(oTask, oParentTask, oOptions);
  }

  function createLinkByConnector(oTask, oParentTask?, oOptions?) {
    // Create connection from oParentTask to oTask
    sap.cdw.taskchainmodeler.ModelImpl.createLinkByConnector(oTask, oParentTask, oOptions);
  }

  afterEach(function () {
    // revert any FF activation
    SupportedFeaturesService.registerService(new NullSupportedFeatureService());
  });

  it("Start -> Task 1 -> Task 2 -> Task 3 => check JSON", async () => {
    // change element order in output and getCsn on output, should not change order of elements in union
    const oNewModel = createModel();
    oNewModel.name = "taskChainTest";
    assert.notEqual(undefined, oNewModel, "New model created");

    const oTask1 = createTask(oNewModel, { name: "Orders", isRemoteTable: true });
    // emulate drop task on diagram
    onPostCreateTask(oTask1);
    const oTask2 = createTask(oNewModel, { name: "View_Orders", isView: true });
    // emulate drop task2 on task1 on diagram
    onPostCreateTask(oTask2, oTask1);

    const oJSON = await ModelToJSON.serializeModel(oNewModel);
    assert.deepEqual(
      {
        nodes: [
          { id: 0, type: "START" },
          {
            id: 1,
            type: "TASK",
            taskIdentifier: { applicationId: "VIEWS", activity: "PERSIST", objectId: "View_Orders" },
          },
          {
            id: 2,
            type: "TASK",
            taskIdentifier: { applicationId: "REMOTE_TABLES", activity: "REPLICATE", objectId: "Orders" },
          },
        ],
        links: [{ startNode: { nodeId: 1, statusRequired: "COMPLETED" }, endNode: { nodeId: 2 }, id: 0 }],
      },
      oJSON,
      "Task Chain JSON generated successfully."
    );
  });

  it("Start -> Task 1, Task 2 => ALL operator => Task 3 => parallel - check JSON", async () => {
    // change element order in output and getCsn on output, should not change order of elements in union
    const oNewModel = createModel();
    oNewModel.name = "taskChainTest";
    assert.notEqual(undefined, oNewModel, "New model created");

    const oTask1 = createTask(oNewModel, { name: "Orders", isRemoteTable: true });
    // emulate drop task on diagram
    onPostCreateTask(oTask1, (oNewModel as any).startNode);
    const oTask2 = createTask(oNewModel, { name: "View_Orders", isView: true });
    // emulate drop task2 on task1 on diagram
    onPostCreateTask(oTask2, (oNewModel as any).startNode);

    let oJSON = await ModelToJSON.serializeModel(oNewModel);
    assert.deepEqual(
      {
        nodes: [
          { id: 0, type: "START" },
          {
            id: 1,
            type: "TASK",
            taskIdentifier: { applicationId: "REMOTE_TABLES", activity: "REPLICATE", objectId: "Orders" },
          },
          {
            id: 2,
            type: "TASK",
            taskIdentifier: { applicationId: "VIEWS", activity: "PERSIST", objectId: "View_Orders" },
          },
        ],
        links: [
          { startNode: { nodeId: 0, statusRequired: "ANY" }, endNode: { nodeId: 1 }, id: 0 },
          { startNode: { nodeId: 0, statusRequired: "ANY" }, endNode: { nodeId: 2 }, id: 1 },
        ],
      },
      oJSON,
      "JSON check for parallel nodes success"
    );
    const oOperation1 = createOperation(oNewModel, { name: "ALL 1", operationType: "ALL" });
    onPostCreateTask(oOperation1, oTask1);
    assert.equal(oOperation1.aggregatedValidations.validations.length, 2, "test validation count 2");
    assert.equal(
      oOperation1.aggregatedValidations.validations[0].message.id,
      "VAL_OPERATOR_SHOULD_HAVE_ATLEAST_TWO_INCOMING_LINK",
      "No incoming link error message is correct"
    );
    createLinkByConnector(oOperation1, oTask2);
    assert.equal(oOperation1.aggregatedValidations.validations.length, 1, "test validation count 1");
    assert.equal(
      oOperation1.aggregatedValidations.validations[0].message.id,
      "VAL_OPERATOR_SHOULD_HAVE_ONE_OUT_LINK",
      "No outgoing link error message is correct"
    );
    oJSON = await ModelToJSON.serializeModel(oNewModel);
    assert.deepEqual(
      {
        nodes: [
          { id: 0, type: "START" },
          {
            id: 1,
            type: "TASK",
            taskIdentifier: { applicationId: "REMOTE_TABLES", activity: "REPLICATE", objectId: "Orders" },
          },
          {
            id: 2,
            type: "TASK",
            taskIdentifier: { applicationId: "VIEWS", activity: "PERSIST", objectId: "View_Orders" },
          },
          { id: 3, type: "AND" },
        ],
        links: [
          { startNode: { nodeId: 0, statusRequired: "ANY" }, endNode: { nodeId: 1 }, id: 0 },
          { startNode: { nodeId: 0, statusRequired: "ANY" }, endNode: { nodeId: 2 }, id: 1 },
          { startNode: { nodeId: 1, statusRequired: "COMPLETED" }, endNode: { nodeId: 3 }, id: 2 },
          { startNode: { nodeId: 2, statusRequired: "COMPLETED" }, endNode: { nodeId: 3 }, id: 3 },
        ],
      },
      oJSON,
      "JSON check for parallel nodes with AND success"
    );
    const oTask3 = createTask(oNewModel, { name: "DF_Orders", isDataFlow: true });
    createLinkByConnector(oTask3, oOperation1);
    assert.equal(oOperation1.aggregatedValidations.validations.length, 0, "test validation count 0 for operator");
    onPostCreateTask(oTask3, (oNewModel as any).startNode, { operation: "Parallel" });
    assert.equal(
      oTask3.predecessorNodes?.[0],
      (oNewModel as any).startNode,
      "Added Task3 as parallel node successful."
    );
    oTask3.resource.undo();
    onPostCreateTask(oTask3, oTask1, { operation: "Replace" });
    assert.equal(oTask1.aggregatedValidations.validations.length, 2, "Unconnected task validation error");
    assert.equal(
      oTask1.aggregatedValidations.validations[0].message.id,
      "VAL_UNCONNECTED_TASK",
      "Unconnected task error is correct for replace action"
    );
  });

  it("Start -> Task 1 (Remote Table), Task 2 (View) => Task 3 (IL Run) - check JSON", async () => {
    // change element order in output and getCsn on output, should not change order of elements in union
    const oNewModel = createModel();
    oNewModel.name = "taskChainTest";
    assert.notEqual(undefined, oNewModel, "New model created");

    const oTask1 = createTask(oNewModel, { name: "Orders", isRemoteTable: true });
    // emulate drop task on diagram
    onPostCreateTask(oTask1);
    const oTask2 = createTask(oNewModel, { name: "View_Orders", isView: true });
    // emulate drop task2 on task1 on diagram
    onPostCreateTask(oTask2, oTask1);
    const oTask3 = createTask(oNewModel, { name: "IL_Orders", isIntelligentLookup: true });
    // Add IL task after View on diagram
    onPostCreateTask(oTask3, oTask2, { operation: "AddNew" });

    const oJSON = await ModelToJSON.serializeModel(oNewModel);
    assert.deepEqual(
      {
        nodes: [
          { id: 0, type: "START" },
          {
            id: 1,
            type: "TASK",
            taskIdentifier: { applicationId: "VIEWS", activity: "PERSIST", objectId: "View_Orders" },
          },
          {
            id: 2,
            type: "TASK",
            taskIdentifier: { applicationId: "REMOTE_TABLES", activity: "REPLICATE", objectId: "Orders" },
          },
          {
            id: 3,
            type: "TASK",
            taskIdentifier: { applicationId: "INTELLIGENT_LOOKUP", activity: "EXECUTE", objectId: "IL_Orders" },
          },
        ],
        links: [
          { startNode: { nodeId: 1, statusRequired: "COMPLETED" }, endNode: { nodeId: 3 }, id: 0 },
          { startNode: { nodeId: 3, statusRequired: "COMPLETED" }, endNode: { nodeId: 2 }, id: 1 },
        ],
      },
      oJSON,
      "Mismatch in expected Task Chain JSON."
    );
  });

  it("Start -> Task 1 (Remote Table) => Task 2 (Transformation Flow) - check JSON", async () => {
    // change element order in output and getCsn on output, should not change order of elements in union
    const oNewModel = createModel();
    oNewModel.name = "taskChainTest";
    assert.notEqual(undefined, oNewModel, "New model created");

    const oTask1 = createTask(oNewModel, { name: "Orders", isRemoteTable: true });
    // emulate drop task on diagram
    onPostCreateTask(oTask1);
    const oTask2 = createTask(oNewModel, { name: "TF_Orders", isTransformationFlow: true });
    // Add TF task after table on diagram
    onPostCreateTask(oTask2, oTask1, { operation: "AddNew" });

    let oJSON = await ModelToJSON.serializeModel(oNewModel);
    assert.deepEqual(
      {
        nodes: [
          { id: 0, type: "START" },
          {
            id: 1,
            type: "TASK",
            taskIdentifier: { applicationId: "REMOTE_TABLES", activity: "REPLICATE", objectId: "Orders" },
          },
          {
            id: 2,
            type: "TASK",
            taskIdentifier: { applicationId: "TRANSFORMATION_FLOWS", activity: "EXECUTE", objectId: "TF_Orders" },
          },
        ],
        links: [{ startNode: { nodeId: 1, statusRequired: "COMPLETED" }, endNode: { nodeId: 2 }, id: 0 }],
      },
      oJSON,
      "Transformation flow task, Expected Task Chain JSON."
    );

    const oTask3 = createTask(oNewModel, { name: "TF_Orders_2", isTransformationFlow: true });
    onPostCreateTask(oTask3, (oNewModel as any).startNode, { operation: "Parallel" });
    createLinkByConnector(oTask1, (oNewModel as any).startNode);
    oJSON = await ModelToJSON.serializeModel(oNewModel);
    assert.deepEqual(
      {
        nodes: [
          { id: 0, type: "START" },
          {
            id: 1,
            type: "TASK",
            taskIdentifier: { applicationId: "TRANSFORMATION_FLOWS", activity: "EXECUTE", objectId: "TF_Orders_2" },
          },
          {
            id: 2,
            type: "TASK",
            taskIdentifier: { applicationId: "REMOTE_TABLES", activity: "REPLICATE", objectId: "Orders" },
          },
          {
            id: 3,
            type: "TASK",
            taskIdentifier: { applicationId: "TRANSFORMATION_FLOWS", activity: "EXECUTE", objectId: "TF_Orders" },
          },
        ],
        links: [
          { startNode: { nodeId: 0, statusRequired: "ANY" }, endNode: { nodeId: 1 }, id: 0 },
          { startNode: { nodeId: 0, statusRequired: "ANY" }, endNode: { nodeId: 2 }, id: 1 },
          { startNode: { nodeId: 2, statusRequired: "COMPLETED" }, endNode: { nodeId: 3 }, id: 2 },
        ],
      },
      oJSON,
      "Transformation flow task, Expected Task Chain JSON."
    );
  });

  it("Start -> Task 1 (Remote Table) => Task 2 (Replication Flow) - check JSON", async () => {
    const oNewModel = createModel();
    oNewModel.name = "taskChainTest";
    assert.notEqual(undefined, oNewModel, "New model created");

    const oTask1 = createTask(oNewModel, { name: "Orders", isRemoteTable: true });
    // emulate drop task on diagram
    onPostCreateTask(oTask1);
    const oTask2 = createTask(oNewModel, { name: "RF_Orders", isReplicationFlow: true });
    // Add TF task after table on diagram
    onPostCreateTask(oTask2, oTask1, { operation: "AddNew" });

    let oJSON = await ModelToJSON.serializeModel(oNewModel);
    assert.deepEqual(
      {
        nodes: [
          { id: 0, type: "START" },
          {
            id: 1,
            type: "TASK",
            taskIdentifier: { applicationId: "REMOTE_TABLES", activity: "REPLICATE", objectId: "Orders" },
          },
          {
            id: 2,
            type: "TASK",
            taskIdentifier: { applicationId: "REPLICATION_FLOWS", activity: "EXECUTE", objectId: "RF_Orders" },
          },
        ],
        links: [{ startNode: { nodeId: 1, statusRequired: "COMPLETED" }, endNode: { nodeId: 2 }, id: 0 }],
      },
      oJSON,
      "Replication flow task, Expected Task Chain JSON."
    );

    const oTask3 = createTask(oNewModel, { name: "RF_Orders_2", isReplicationFlow: true });
    onPostCreateTask(oTask3, (oNewModel as any).startNode, { operation: "Parallel" });
    createLinkByConnector(oTask1, (oNewModel as any).startNode);
    oJSON = await ModelToJSON.serializeModel(oNewModel);
    assert.deepEqual(
      {
        nodes: [
          { id: 0, type: "START" },
          {
            id: 1,
            type: "TASK",
            taskIdentifier: { applicationId: "REPLICATION_FLOWS", activity: "EXECUTE", objectId: "RF_Orders_2" },
          },
          {
            id: 2,
            type: "TASK",
            taskIdentifier: { applicationId: "REMOTE_TABLES", activity: "REPLICATE", objectId: "Orders" },
          },
          {
            id: 3,
            type: "TASK",
            taskIdentifier: { applicationId: "REPLICATION_FLOWS", activity: "EXECUTE", objectId: "RF_Orders" },
          },
        ],
        links: [
          { startNode: { nodeId: 0, statusRequired: "ANY" }, endNode: { nodeId: 1 }, id: 0 },
          { startNode: { nodeId: 0, statusRequired: "ANY" }, endNode: { nodeId: 2 }, id: 1 },
          { startNode: { nodeId: 2, statusRequired: "COMPLETED" }, endNode: { nodeId: 3 }, id: 2 },
        ],
      },
      oJSON,
      "Replication flow task, Expected Task Chain JSON 2."
    );
  });

  it("Start -> Task 1 (Remote Table) => Task 2 (Delta Table) - check JSON", async () => {
    const oNewModel = createModel();
    oNewModel.name = "taskChainTest";
    assert.notEqual(undefined, oNewModel, "New model created");

    const oTask1 = createTask(oNewModel, { name: "Orders", isRemoteTable: true });
    // emulate drop task on diagram
    onPostCreateTask(oTask1);
    const oTask2 = createTask(oNewModel, { name: "DT_Orders", isDeltaTable: true, numberOfRetentionDays: 15 });
    // Add TF task after table on diagram
    onPostCreateTask(oTask2, oTask1, { operation: "AddNew" });

    let oJSON = await ModelToJSON.serializeModel(oNewModel);
    assert.deepEqual(
      {
        nodes: [
          { id: 0, type: "START" },
          {
            id: 1,
            type: "TASK",
            taskIdentifier: { applicationId: "REMOTE_TABLES", activity: "REPLICATE", objectId: "Orders" },
          },
          {
            id: 2,
            type: "TASK",
            taskIdentifier: {
              applicationId: "LOCAL_TABLE",
              activity: "REMOVE_DELETED_RECORDS",
              objectId: "DT_Orders",
              parameters: {
                retentionTimeInDays: {
                  val: 15,
                },
              },
            },
          },
        ],
        links: [{ startNode: { nodeId: 1, statusRequired: "COMPLETED" }, endNode: { nodeId: 2 }, id: 0 }],
      },
      oJSON,
      "Delete Records as task, Expected Task Chain JSON."
    );

    const oTask3 = createTask(oNewModel, { name: "DT_Orders_2", isDeltaTable: true });
    onPostCreateTask(oTask3, (oNewModel as any).startNode, { operation: "Parallel" });
    createLinkByConnector(oTask1, (oNewModel as any).startNode);
    oJSON = await ModelToJSON.serializeModel(oNewModel);
    assert.deepEqual(
      {
        nodes: [
          { id: 0, type: "START" },
          {
            id: 1,
            type: "TASK",
            taskIdentifier: {
              applicationId: "LOCAL_TABLE",
              activity: "REMOVE_DELETED_RECORDS",
              objectId: "DT_Orders_2",
              parameters: {
                retentionTimeInDays: {
                  val: 90,
                },
              },
            },
          },
          {
            id: 2,
            type: "TASK",
            taskIdentifier: { applicationId: "REMOTE_TABLES", activity: "REPLICATE", objectId: "Orders" },
          },
          {
            id: 3,
            type: "TASK",
            taskIdentifier: {
              applicationId: "LOCAL_TABLE",
              activity: "REMOVE_DELETED_RECORDS",
              objectId: "DT_Orders",
              parameters: {
                retentionTimeInDays: {
                  val: 15,
                },
              },
            },
          },
        ],
        links: [
          { startNode: { nodeId: 0, statusRequired: "ANY" }, endNode: { nodeId: 1 }, id: 0 },
          { startNode: { nodeId: 0, statusRequired: "ANY" }, endNode: { nodeId: 2 }, id: 1 },
          { startNode: { nodeId: 2, statusRequired: "COMPLETED" }, endNode: { nodeId: 3 }, id: 2 },
        ],
      },
      oJSON,
      "Delete Records as task, Expected Task Chain JSON."
    );
  });

  it("Start -> Task 1 (Remote Table) => Task 2 (Task Chain) - check JSON", async () => {
    const oNewModel = createModel();
    oNewModel.name = "taskChainTest";
    assert.notEqual(undefined, oNewModel, "New model created");

    const oTask1 = createTask(oNewModel, { name: "Orders", isRemoteTable: true });
    // emulate drop task on diagram
    onPostCreateTask(oTask1);
    const oTask2 = createTask(oNewModel, { name: "TC_Orders", isTaskChain: true });
    // Add TC task after table on diagram
    onPostCreateTask(oTask2, oTask1, { operation: "AddNew" });

    let oJSON = await ModelToJSON.serializeModel(oNewModel);
    assert.deepEqual(
      {
        nodes: [
          { id: 0, type: "START" },
          {
            id: 1,
            type: "TASK",
            taskIdentifier: { applicationId: "REMOTE_TABLES", activity: "REPLICATE", objectId: "Orders" },
          },
          {
            id: 2,
            type: "TASK",
            taskIdentifier: {
              applicationId: "TASK_CHAINS",
              activity: "RUN_CHAIN",
              objectId: "TC_Orders",
            },
          },
        ],
        links: [{ startNode: { nodeId: 1, statusRequired: "COMPLETED" }, endNode: { nodeId: 2 }, id: 0 }],
      },
      oJSON,
      "Nested Task Chain as task, Expected Task Chain JSON."
    );

    const oTask3 = createTask(oNewModel, { name: "TC_Orders_2", isTaskChain: true });
    onPostCreateTask(oTask3, (oNewModel as any).startNode, { operation: "Parallel" });
    createLinkByConnector(oTask1, (oNewModel as any).startNode);
    oJSON = await ModelToJSON.serializeModel(oNewModel);
    assert.deepEqual(
      {
        nodes: [
          { id: 0, type: "START" },
          {
            id: 1,
            type: "TASK",
            taskIdentifier: {
              applicationId: "TASK_CHAINS",
              activity: "RUN_CHAIN",
              objectId: "TC_Orders_2",
            },
          },
          {
            id: 2,
            type: "TASK",
            taskIdentifier: { applicationId: "REMOTE_TABLES", activity: "REPLICATE", objectId: "Orders" },
          },
          {
            id: 3,
            type: "TASK",
            taskIdentifier: {
              applicationId: "TASK_CHAINS",
              activity: "RUN_CHAIN",
              objectId: "TC_Orders",
            },
          },
        ],
        links: [
          { startNode: { nodeId: 0, statusRequired: "ANY" }, endNode: { nodeId: 1 }, id: 0 },
          { startNode: { nodeId: 0, statusRequired: "ANY" }, endNode: { nodeId: 2 }, id: 1 },
          { startNode: { nodeId: 2, statusRequired: "COMPLETED" }, endNode: { nodeId: 3 }, id: 2 },
        ],
      },
      oJSON,
      "Nested Task Chain as task, Expected Task Chain JSON."
    );
  });

  it("Start -> Task 1 (Remote Table) => Task 2 (SQL Script Procedure) - check JSON", async () => {
    const oNewModel = createModel();
    oNewModel.name = "taskChainTest";
    assert.notEqual(undefined, oNewModel, "New model created");

    const oTask1 = createTask(oNewModel, { name: "Orders", isRemoteTable: true });
    // emulate drop task on diagram
    onPostCreateTask(oTask1);
    const oTask2 = createTask(oNewModel, {
      name: "Procedure_1",
      isSQLScriptProcedure: true,
      parameter: {
        table_name: {
          type: "NVARCHAR",
          length: 256,
          primitiveDataType: "cds.String",
          value: "gdjsh",
        },
        num_records: {
          type: "INT",
          primitiveDataType: "cds.Integer",
          value: "23",
        },
      },
    });
    // Add TF task after table on diagram
    onPostCreateTask(oTask2, oTask1, { operation: "AddNew" });
    // onPostCreateTask(oTask2);
    let oJSON = await ModelToJSON.serializeModel(oNewModel);
    assert.deepEqual(
      {
        nodes: [
          {
            id: 0,
            type: "START",
          },
          {
            id: 1,
            type: "TASK",
            taskIdentifier: { applicationId: "REMOTE_TABLES", activity: "REPLICATE", objectId: "Orders" },
          },
          {
            id: 2,
            type: "TASK",
            taskIdentifier: {
              applicationId: "SQL_SCRIPT_PROCEDURE",
              activity: "RUN",
              objectId: "Procedure_1",
              parameters: {
                procedureParameters: {
                  table_name: {
                    type: "NVARCHAR",
                    length: 256,
                    primitiveDataType: "cds.String",
                    value: "gdjsh",
                    precision: undefined,
                    scale: undefined,
                  },
                  num_records: {
                    type: "INT",
                    primitiveDataType: "cds.Integer",
                    length: "0",
                    value: "23",
                    precision: undefined,
                    scale: undefined,
                  },
                },
              },
            },
          },
        ],
        links: [{ startNode: { nodeId: 1, statusRequired: "COMPLETED" }, endNode: { nodeId: 2 }, id: 0 }],
      },
      oJSON,
      "SQL Script Procedure, Expected Task Chain JSON."
    );

    const oTask3 = createTask(oNewModel, { name: "Procedure_2", isSQLScriptProcedure: true });
    onPostCreateTask(oTask3, (oNewModel as any).startNode, { operation: "Parallel" });
    createLinkByConnector(oTask1, (oNewModel as any).startNode);
    oJSON = await ModelToJSON.serializeModel(oNewModel);
    assert.deepEqual(
      {
        nodes: [
          { id: 0, type: "START" },
          {
            id: 1,
            type: "TASK",
            taskIdentifier: {
              applicationId: "SQL_SCRIPT_PROCEDURE",
              activity: "RUN",
              objectId: "Procedure_2",
              parameters: {
                procedureParameters: {},
              },
            },
          },
          {
            id: 2,
            type: "TASK",
            taskIdentifier: { applicationId: "REMOTE_TABLES", activity: "REPLICATE", objectId: "Orders" },
          },
          {
            id: 3,
            type: "TASK",
            taskIdentifier: {
              applicationId: "SQL_SCRIPT_PROCEDURE",
              activity: "RUN",
              objectId: "Procedure_1",
              parameters: {
                procedureParameters: {
                  table_name: {
                    type: "NVARCHAR",
                    length: 256,
                    primitiveDataType: "cds.String",
                    value: "gdjsh",
                    precision: undefined,
                    scale: undefined,
                  },
                  num_records: {
                    type: "INT",
                    primitiveDataType: "cds.Integer",
                    length: "0",
                    value: "23",
                    precision: undefined,
                    scale: undefined,
                  },
                },
              },
            },
          },
        ],
        links: [
          { startNode: { nodeId: 0, statusRequired: "ANY" }, endNode: { nodeId: 1 }, id: 0 },
          { startNode: { nodeId: 0, statusRequired: "ANY" }, endNode: { nodeId: 2 }, id: 1 },
          { startNode: { nodeId: 2, statusRequired: "COMPLETED" }, endNode: { nodeId: 3 }, id: 2 },
        ],
      },
      oJSON,
      "SQL Script Procedure as task, Expected Task Chain JSON."
    );
  });

  it("Start -> Task 1 (Remote Table) => Task 2 (Shared Task Chain) - check JSON", async () => {
    Utils.canSupportDeleteData = () => true;
    const oNewModel = createModel();
    oNewModel.name = "taskChainTest";
    assert.notEqual(undefined, oNewModel, "New model created");

    const obj = {
      name: "Orders",
      selectedActivityIndex: 1,
      "#technicalType": "DWC_REMOTE_TABLE",
    };
    // obj.repositoryCSN["#technicalType"] = "DWC_REMOTE_TABLE";

    const oTask1 = createTask(oNewModel, obj);
    // emulate drop task on diagram
    onPostCreateTask(oTask1);
    const oTask2 = createTask(oNewModel, {
      name: "Shared_TaskChain",
      "#technicalType": "DWC_TASKCHAIN",
      isCrossSpace: true,
      repositoryCSN: { crossSpaceName: "space1234" },
    });
    // Add TF task after table on diagram
    onPostCreateTask(oTask2, oTask1, { operation: "AddNew" });
    // onPostCreateTask(oTask2);
    let oJSON = await ModelToJSON.serializeModel(oNewModel);
    assert.deepEqual(
      {
        nodes: [
          {
            id: 0,
            type: "START",
          },
          {
            id: 1,
            type: "TASK",
            taskIdentifier: { applicationId: "REMOTE_TABLES", activity: "REMOVE_REPLICATED_DATA", objectId: "Orders" },
          },
          {
            id: 2,
            type: "TASK",
            taskIdentifier: {
              applicationId: "TASK_CHAINS",
              activity: "RUN_CHAIN",
              objectId: "Shared_TaskChain",
              spaceId: "space1234",
            },
          },
        ],
        links: [{ startNode: { nodeId: 1, statusRequired: "COMPLETED" }, endNode: { nodeId: 2 }, id: 0 }],
      },
      oJSON,
      "Shared Task Chain, Expected Task Chain JSON."
    );

    const oTask3 = createTask(oNewModel, {
      name: "Shared_TaskChain_2",
      "#technicalType": "DWC_TASKCHAIN",
      isCrossSpace: true,
      repositoryCSN: { crossSpaceName: "spaceShared" },
    });
    onPostCreateTask(oTask3, (oNewModel as any).startNode, { operation: "Parallel" });
    createLinkByConnector(oTask1, (oNewModel as any).startNode);
    oJSON = await ModelToJSON.serializeModel(oNewModel);
    assert.deepEqual(
      {
        nodes: [
          { id: 0, type: "START" },
          {
            id: 1,
            type: "TASK",
            taskIdentifier: {
              applicationId: "TASK_CHAINS",
              activity: "RUN_CHAIN",
              objectId: "Shared_TaskChain_2",
              spaceId: "spaceShared",
            },
          },
          {
            id: 2,
            type: "TASK",
            taskIdentifier: { applicationId: "REMOTE_TABLES", activity: "REMOVE_REPLICATED_DATA", objectId: "Orders" },
          },
          {
            id: 3,
            type: "TASK",
            taskIdentifier: {
              applicationId: "TASK_CHAINS",
              activity: "RUN_CHAIN",
              objectId: "Shared_TaskChain",
              spaceId: "space1234",
            },
          },
        ],
        links: [
          { startNode: { nodeId: 0, statusRequired: "ANY" }, endNode: { nodeId: 1 }, id: 0 },
          { startNode: { nodeId: 0, statusRequired: "ANY" }, endNode: { nodeId: 2 }, id: 1 },
          { startNode: { nodeId: 2, statusRequired: "COMPLETED" }, endNode: { nodeId: 3 }, id: 2 },
        ],
      },
      oJSON,
      "Shared Task Chain added parallelly, Expected Task Chain JSON."
    );
  });

  it("Start -> Task 1 (Remote Table) => Task 2 (BW Process Chain) - check JSON", async () => {
    Utils.canSupportDeleteData = () => true;
    const oNewModel = createModel();
    oNewModel.name = "taskChainTest";
    assert.notEqual(undefined, oNewModel, "New model created");

    const obj = {
      name: "Orders",
      selectedActivityIndex: 0,
      "#technicalType": "DWC_REMOTE_TABLE",
    };
    const oTask1 = createTask(oNewModel, obj);
    // emulate drop task on diagram
    onPostCreateTask(oTask1);
    const oTask2 = createTask(oNewModel, {
      name: "BWBRIDGE2DWS.DS00_391",
      isBWProcessChain: true,
      repositoryCSN: { typeId: "BW_PROCESS_CHAIN", spaceId: "BWBRIDGE2DWS" },
    });
    // Add TF task after table on diagram
    onPostCreateTask(oTask2, oTask1, { operation: "AddNew" });
    // onPostCreateTask(oTask2);
    let oJSON = await ModelToJSON.serializeModel(oNewModel);
    assert.deepEqual(
      {
        nodes: [
          {
            id: 0,
            type: "START",
          },
          {
            id: 1,
            type: "TASK",
            taskIdentifier: { applicationId: "REMOTE_TABLES", activity: "REPLICATE", objectId: "Orders" },
          },
          {
            id: 2,
            type: "TASK",
            taskIdentifier: {
              applicationId: "BW_PROCESS_CHAIN",
              activity: "RUN",
              objectId: "BWBRIDGE2DWS.DS00_391",
              spaceId: "BWBRIDGE2DWS",
            },
          },
        ],
        links: [{ startNode: { nodeId: 1, statusRequired: "COMPLETED" }, endNode: { nodeId: 2 }, id: 0 }],
      },
      oJSON,
      "BW Process Chain, Expected Task Chain JSON."
    );

    const oTask3 = createTask(oNewModel, {
      name: "BWBRIDGE2DWS.JD_CHAIN",
      isBWProcessChain: true,
      repositoryCSN: { typeId: "BW_PROCESS_CHAIN", spaceId: "BWBRIDGE2DWS" },
    });
    onPostCreateTask(oTask3, (oNewModel as any).startNode, { operation: "Parallel" });
    createLinkByConnector(oTask1, (oNewModel as any).startNode);
    oJSON = await ModelToJSON.serializeModel(oNewModel);
    assert.deepEqual(
      {
        nodes: [
          { id: 0, type: "START" },
          {
            id: 1,
            type: "TASK",
            taskIdentifier: {
              applicationId: "BW_PROCESS_CHAIN",
              activity: "RUN",
              objectId: "BWBRIDGE2DWS.JD_CHAIN",
              spaceId: "BWBRIDGE2DWS",
            },
          },
          {
            id: 2,
            type: "TASK",
            taskIdentifier: { applicationId: "REMOTE_TABLES", activity: "REPLICATE", objectId: "Orders" },
          },
          {
            id: 3,
            type: "TASK",
            taskIdentifier: {
              applicationId: "BW_PROCESS_CHAIN",
              activity: "RUN",
              objectId: "BWBRIDGE2DWS.DS00_391",
              spaceId: "BWBRIDGE2DWS",
            },
          },
        ],
        links: [
          { startNode: { nodeId: 0, statusRequired: "ANY" }, endNode: { nodeId: 1 }, id: 0 },
          { startNode: { nodeId: 0, statusRequired: "ANY" }, endNode: { nodeId: 2 }, id: 1 },
          { startNode: { nodeId: 2, statusRequired: "COMPLETED" }, endNode: { nodeId: 3 }, id: 2 },
        ],
      },
      oJSON,
      "BW Process Chain as task, Expected Task Chain JSON."
    );
  });

  it("Start -> Task 1 (Remote Table) => Task 2 (API Task) - check JSON", async () => {
    Utils.canSupportDeleteData = () => true;
    const oNewModel = createModel();
    oNewModel.name = "taskChainTest";
    assert.notEqual(undefined, oNewModel, "New model created");

    const obj = {
      name: "Orders",
      selectedActivityIndex: 0,
      "#technicalType": "DWC_REMOTE_TABLE",
    };
    const oTask1 = createTask(oNewModel, obj);
    // emulate drop task on diagram
    onPostCreateTask(oTask1);
    const oTask2 = createTask(oNewModel, {
      name: "API_TASK",
      isRestApi: true,
      repositoryCSN: { typeId: "API" },
      configuration: {
        connectionName: "x509",
        invokeAPI: {
          request: {
            method: "POST",
            mode: "ASYNC",
            apiPath: "/async-code-and-body",
            body: {
              id: "10",
            },
            csrfTokenRequired: false,
          },
          expectedResponse: {
            from: "BODY",
            body: {
              jobIdJsonPath: "id",
            },
          },
        },
        statusAPI: {
          request: {
            method: "GET",
            apiPath: "/status-code-success/{id}",
          },
          expectedResponse: {
            from: "CODE",
          },
        },
      },
    });
    // Add TF task after table on diagram
    onPostCreateTask(oTask2, oTask1, { operation: "AddNew" });
    // onPostCreateTask(oTask2);
    let oJSON = await ModelToJSON.serializeModel(oNewModel);
    assert.deepEqual(
      {
        nodes: [
          {
            id: 0,
            type: "START",
          },
          {
            id: 1,
            type: "TASK",
            taskIdentifier: { applicationId: "REMOTE_TABLES", activity: "REPLICATE", objectId: "Orders" },
          },
          {
            id: 2,
            type: "TASK",
            taskIdentifier: {
              applicationId: "API",
              activity: "RUN",
              objectId: "API_TASK",
            },
            configuration: {
              connectionName: "x509",
              invokeAPI: {
                request: {
                  method: "POST",
                  mode: "ASYNC",
                  apiPath: "/async-code-and-body",
                  body: {
                    id: "10",
                  },
                  csrfTokenRequired: false,
                },
                expectedResponse: {
                  from: "BODY",
                  body: {
                    jobIdJsonPath: "id",
                  },
                },
              },
              statusAPI: {
                request: {
                  method: "GET",
                  apiPath: "/status-code-success/{id}",
                },
                expectedResponse: {
                  from: "CODE",
                },
              },
            },
          },
        ],
        links: [{ startNode: { nodeId: 1, statusRequired: "COMPLETED" }, endNode: { nodeId: 2 }, id: 0 }],
      },
      oJSON,
      "API Task , Expected Task Chain JSON."
    );

    const oTask3 = createTask(oNewModel, {
      name: "API_TASK_1",
      isRestApi: true,
      repositoryCSN: { typeId: "API" },
      configuration: {
        connectionName: "x509",
        invokeAPI: {
          request: {
            method: "POST",
            mode: "ASYNC",
            apiPath: "/async-code-and-location-url-status-from-code",
            body: {
              id: "10",
            },
            csrfTokenRequired: false,
          },
          expectedResponse: {
            from: "CODE_AND_LOCATION_HEADER",
          },
        },
        statusAPI: {
          expectedResponse: {
            from: "CODE",
          },
        },
      },
    });
    onPostCreateTask(oTask3, (oNewModel as any).startNode, { operation: "Parallel" });
    createLinkByConnector(oTask1, (oNewModel as any).startNode);
    oJSON = await ModelToJSON.serializeModel(oNewModel);
    assert.deepEqual(
      {
        nodes: [
          { id: 0, type: "START" },
          {
            id: 1,
            type: "TASK",
            taskIdentifier: {
              applicationId: "API",
              activity: "RUN",
              objectId: "API_TASK_1",
            },
            configuration: {
              connectionName: "x509",
              invokeAPI: {
                request: {
                  method: "POST",
                  mode: "ASYNC",
                  apiPath: "/async-code-and-location-url-status-from-code",
                  body: {
                    id: "10",
                  },
                  csrfTokenRequired: false,
                },
                expectedResponse: {
                  from: "CODE_AND_LOCATION_HEADER",
                },
              },
              statusAPI: {
                expectedResponse: {
                  from: "CODE",
                },
              },
            },
          },
          {
            id: 2,
            type: "TASK",
            taskIdentifier: { applicationId: "REMOTE_TABLES", activity: "REPLICATE", objectId: "Orders" },
          },
          {
            id: 3,
            type: "TASK",
            taskIdentifier: {
              applicationId: "API",
              activity: "RUN",
              objectId: "API_TASK",
            },
            configuration: {
              connectionName: "x509",
              invokeAPI: {
                request: {
                  method: "POST",
                  mode: "ASYNC",
                  apiPath: "/async-code-and-body",
                  body: {
                    id: "10",
                  },
                  csrfTokenRequired: false,
                },
                expectedResponse: {
                  from: "BODY",
                  body: {
                    jobIdJsonPath: "id",
                  },
                },
              },
              statusAPI: {
                request: {
                  method: "GET",
                  apiPath: "/status-code-success/{id}",
                },
                expectedResponse: {
                  from: "CODE",
                },
              },
            },
          },
        ],
        links: [
          { startNode: { nodeId: 0, statusRequired: "ANY" }, endNode: { nodeId: 1 }, id: 0 },
          { startNode: { nodeId: 0, statusRequired: "ANY" }, endNode: { nodeId: 2 }, id: 1 },
          { startNode: { nodeId: 2, statusRequired: "COMPLETED" }, endNode: { nodeId: 3 }, id: 2 },
        ],
      },
      oJSON,
      "API Task, Expected Task Chain JSON."
    );
  });
});
