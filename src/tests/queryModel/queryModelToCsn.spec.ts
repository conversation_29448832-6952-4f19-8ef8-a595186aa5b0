/**
 * eslint-disable @typescript-eslint/unbound-method
 *
 * @format
 */

/* eslint-disable no-underscore-dangle */
import { ExpressionAnalyzer } from "@sap/skyline-consumer-sdk/dist/src/sap/skyline/consumer-sdk/expressionanalyzer/ExpressionAnalyzer";
import assert from "assert";
import { expect } from "chai";
import sinon from "sinon";
import {
  NullSupportedFeatureService,
  SupportedFeaturesService,
} from "../../../src/components/commonmodel/api/SupportedFeaturesService";
import * as csnUtils from "../../../src/components/commonmodel/csn/csnUtils";
import { cloneJson } from "../../../src/components/commonmodel/csn/csnUtils";
import { DataCategory, SemanticType } from "../../../src/components/commonmodel/model/types/cds.types";
import { LabelColumnAnnotation } from "../../components/commonmodel/model/annotations/labelColumnAnnotation";
import {
  CoastGuardValidator,
  toDeployedRevertableObjectStatus,
  updateReleaseContractFromObjectFile,
  validationRules,
} from "../../components/commonmodel/utility/CompatibilityContractUtils";
import { updateAnnotationByAssociation } from "../../components/commonui/utility/AssociationUtils";
import "../../components/csnquerybuilder/js/model/parsedExpressionImpl";
import "../../components/csnquerybuilder/js/transform/csnToViewModel";
import { loadUnresolvedAssociations } from "../../components/csnquerybuilder/utility/AssociationHelper";
import "../support/queryModelLoader";
import AssociationAnnotationIssues from "./AssociationAnnotationIssues.json";
import DifferentUnionAliasesView from "./DifferentUnionAliasesView.json";
import DuplicateElementUnionView from "./DuplicateElementUnionView.json";
import GraphicalViewWithSharedView from "./GraphicalViewWithSharedView.json";
import DW10132233 from "./JIRADW101-32233.json";
import DW10134743 from "./JIRADW101-34743.json";
import ********** from "./JIRADW101-36763.json";
import DW10138301 from "./JIRADW101-38301.json";
import DW10192955 from "./JIRADW101-92955.json";
import TwoUnionIssue from "./TwoUnionView.json";
const Functions = require("../../../src/components/ermodeler/js/model/functions").Functions;
const nsQueryBuilder = sap.cdw.querybuilder as any;

describe("Query Model to Csn", () => {
  function createOperation(oModel, sOperationName) {
    return sap.cdw.querybuilder.ModelImpl.createObject("sap.cdw.querybuilder." + sOperationName, {}, oModel);
  }

  function createIntermediateOperation(oModel, sOperationName, oAfterNode) {
    const oCreatedOperation = createOperation(oModel, sOperationName);
    sap.cdw.querybuilder.ModelImpl.onCreateIntermediateNode(oAfterNode, oCreatedOperation);
    return oCreatedOperation;
  }

  function createEntity(oModel, sEntityName, nElementsCount) {
    let sEntityClassName = "sap.cdw.querybuilder.Entity",
      sElementClassName = "sap.cdw.querybuilder.Element",
      nIndex,
      oEntity;
    oEntity = sap.cdw.querybuilder.ModelImpl.createObject(sEntityClassName, { name: sEntityName }, oModel);
    for (nIndex = 0; nIndex < nElementsCount; nIndex++) {
      sap.cdw.querybuilder.ModelImpl.createObject(
        sElementClassName,
        { name: "Element_" + nIndex, dataType: "cds.String", length: 10 },
        oEntity
      );
    }
    return oEntity;
  }

  function onPostCreateEntity(oEntity, oParentEntity?, oOptions?) {
    sap.cdw.querybuilder.ModelImpl.onPostDropDataSource(oEntity, oParentEntity, oOptions);
  }

  function createNewModelAndDiagrams() {
    const oModel = sap.cdw.querybuilder.ModelImpl.getOrCreateModel().model;
    (sap.cdw.querybuilder.ModelImpl as any).clearUndoRedo(oModel);
    return oModel;
  }

  afterEach(function () {
    // revert any FF activation
    SupportedFeaturesService.registerService(new NullSupportedFeatureService());
  });

  it("DW101-76255 MDM data preview on intermediate node which source node has simple type", async () => {
    // oEntity1(Has simple type) -> oCalc1 -> oOutput
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel.name = "csnTest";
    oNewModel._unitTesting = true;
    const oOutput = oNewModel.output;

    const oSimpleType1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.commonmodel.SimpleType",
      { name: "SimpleType1", dataType: "cds.String", length: 10 },
      oNewModel
    );
    oNewModel.types.push(oSimpleType1);
    const nsCommonModel = sap.cdw.commonmodel;
    nsCommonModel.ObjectImpl.updateObjectBaseType(oSimpleType1 as sap.cdw.commonmodel.SimpleType, undefined, false);

    const oEntity1 = createEntity(oNewModel, "Entity 1", 3);
    const oElement1 = oEntity1.elements.get(0);
    oElement1.dataType = "SimpleType1";
    nsCommonModel.ObjectImpl.attachElementsToBaseTypes(oEntity1);
    onPostCreateEntity(oEntity1);

    const oCalc1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.CalculatedElements",
      { name: "Calc1" },
      oNewModel
    );

    oEntity1.successorNode = oCalc1;
    oCalc1.successorNode = oOutput;

    let oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("Calc1", oNewModel, {
      dataPreview: true,
      isMdmPreview: true,
      rootObject: oCalc1,
    });
    assert.strictEqual(!!oCsn.definitions.SimpleType1, true, "CSN has simple type for mdm data preview");

    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("Calc1", oNewModel, {
      dataPreview: true,
      isMdmPreview: false,
      rootObject: oCalc1,
    });
    assert.strictEqual(!!oCsn.definitions.SimpleType1, false, "CSN has no simple type for non mdm data preview");
  });

  it("DW101-18880 Entity1,Entity2 -> Union -> output", async () => {
    // change element order in output and getCsn on output, should not change order of elements in union
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel.name = "csnTest";
    // assert.notEqual(undefined, oNewModel, "New model created");

    const oEntity1 = createEntity(oNewModel, "Entity 1", 3);
    // emulate drop entity on diagram
    onPostCreateEntity(oEntity1);
    const oEntity2 = createEntity(oNewModel, "Entity 2", 3);
    // emulate drop entity2 on entity1 on diagram
    onPostCreateEntity(oEntity2, oEntity1, "Union");
    const union = oEntity1.successorNode;

    // map enitity1 to the union
    nsQueryBuilder.NodeImpl.copyElementsForSetNode(union, oEntity2);

    const ele = oNewModel.output.elements.get(0);
    sap.cdw.querybuilder.NodeImpl.changeElementIndexOrder(oNewModel.output, ele, 0, 2);

    const oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    assert.strictEqual(ele.name === union.orderedElements[0].name, true, "union elements order no change");
  });

  it("Jira#FPA101-8742	check default value of srid of calculated element", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel._unitTesting = true;
    const oCalc1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.CalculatedElements",
      { name: "Calc1" },
      oNewModel
    );
    const oCalcElt1 = nsQueryBuilder.NodeImpl.createNewCalculatedElement(oCalc1);
    assert.equal(oCalcElt1.srid, 4326, "Default value is 4326");
  });

  it("Test serializeModel", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel._unitTesting = true;
    const oEntity1 = createEntity(oNewModel, "Entity 1", 1);
    onPostCreateEntity(oEntity1);
    createIntermediateOperation(oNewModel, "CalculatedElements", oEntity1);

    oNewModel.validate();
    const viewModelToCsn = nsQueryBuilder.ViewModelToCsn.getInstance();
    const result = viewModelToCsn.serializeModel(oNewModel);
    let i = 0,
      j = 0;
    const csn = JSON.parse(result);
    const contents = csn.contents;
    for (const o in contents) {
      const obj = contents[o];
      if (obj?.classDefinition === "sap.cdw.querybuilder.ParsedExpression") {
        i++;
      } else if (obj?.classDefinition === "sap.cdw.commonmodel.ValidationStatus") {
        j++;
      }
    }
    assert.strictEqual(i === 0 && j === 0, true, "Check galilei model CSN");
  });

  it("Test CONTAINS in filter", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel.name = "csnTest";
    oNewModel._unitTesting = true;
    const oOutput = oNewModel.output;
    const oEntity1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      {
        name: "Entity1",
      },
      oNewModel
    );
    const oElement1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      {
        name: "ElementSame",
        dataType: "cds.String",
        length: 10,
      },
      oEntity1
    );
    const oElement2 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      {
        name: "Element2",
        dataType: "cds.String",
        length: 10,
      },
      oEntity1
    );
    const oFilter1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Filter",
      {
        name: "Filter1",
      },
      oNewModel
    );

    /*
    oEntity1 --> Filter --> oOutput
    */
    oEntity1.successorNode = oFilter1;
    oFilter1.successorNode = oOutput;
    Functions.prepareAllFunctions();
    oFilter1.condition = "contains(Element2,'a')";
    const oExpr = {
      args: [{ ref: ["Entity1", "Element2"] }, { val: "a" }],
      func: "contains",
    };
    const oExprOrigin = cloneJson(oExpr);
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oFilter1, oExpr);

    // Validate filter
    oFilter1.validate();
    assert.strictEqual(oFilter1.aggregatedValidations.validations.length, 0, "No error");

    const oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    const sResult = JSON.stringify(oCsn.definitions.csnTest.query.SELECT.where[0]);
    assert.strictEqual(sResult, JSON.stringify(oExprOrigin), "where ok");
  });

  it("Jira#DW101-231 integer datatype element should not havee length in CSN", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel._unitTesting = true;

    const oEntity1 = createEntity(oNewModel, "Entity 1", 1);

    // emulate drop entity on diagram
    onPostCreateEntity(oEntity1);
    createIntermediateOperation(oNewModel, "CalculatedElements", oEntity1);

    const oCalc1 = oEntity1.successorNode;
    const oCalcElt1 = oCalc1.elements.get(0);
    assert.equal(oCalcElt1.dataType, "cds.String", "datatype is string");
    assert.equal(oCalcElt1.length, 10, "length 10 when datatype is string");
    oCalcElt1.dataType = "cds.Integer64";
    assert.equal(oCalcElt1.length, 0, "length 0 when datatype is integer");
    oCalcElt1.length = 10;

    const csnOut = await nsQueryBuilder.ViewModelToCsn.getCSN("csnOut", oNewModel, {});
    const viewCSN = csnOut.definitions.View_1;
    assert.equal(JSON.stringify(viewCSN.elements.Element_0), '{"type":"cds.Integer64"}', "csn ok");
  });
  it("Jira#DW101-36763 columns with window function should not be pushed under groupby in CSN", async () => {
    Functions.prepareAllFunctions();
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel.name = "csnTest";
    assert.notEqual(undefined, oNewModel, "New model created");
    // assert.equal(2, oNewModel.diagrams.length, "Query builder and association diagrams created");

    const oEntity1 = createEntity(oNewModel, "Entity 1", 3);
    // emulate drop entity on diagram
    onPostCreateEntity(oEntity1);

    createIntermediateOperation(oNewModel, "CalculatedElements", oEntity1);

    let oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    delete oCsn.definitions.csnTest[nsQueryBuilder.ViewModelToCsn.CDS_ANNOTATION_UIMODEL];
    delete oCsn.definitions.csnTest.params;
    delete oCsn.editorSettings;
    const expectedCsn = {
      definitions: {
        csnTest: {
          kind: "entity",
          elements: {
            Element_0: { type: "cds.String", length: 10 },
            Element_1: { type: "cds.String", length: 10 },
            Element_2: { type: "cds.String", length: 10 },
          },
          query: {
            SELECT: {
              from: { ref: ["Entity 1"] },
              columns: [
                { as: "Element_0", ref: ["Entity 1", "Element_0"] },
                { as: "Element_1", ref: ["Entity 1", "Element_1"] },
                { as: "Element_2", ref: ["Entity 1", "Element_2"] },
              ],
            },
          },
          "@DataWarehouse.consumption.external": true,
          "@ObjectModel.modelingPattern": { "#": "DATA_STRUCTURE" },
          "@ObjectModel.supportedCapabilities": [{ "#": "DATA_STRUCTURE" }],
          "@EndUserText.label": "View 1",
        },
      },
      version: { csn: "1.0" },
      meta: { creator: "View Editor", kind: "sap.dwc.viewmodel" },
      $version: "1.0",
    };
    assert.deepEqual(oCsn, expectedCsn, "Without agg ok");

    const calc = oEntity1.successorNode;

    // DW101-923: Set ST_POINT and ensure expression is OK
    const ce = calc.elements.get(0);
    ce.expression = "max(Element_1)";
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(calc.elements.get(0), {
      func: "max",
      args: [
        {
          ref: ["Element_1"],
        },
      ],
    });
    const lagEl = calc.elements.get(1);
    let sXpr = "LAG(Element_0,Element_1,-Element_0) OVER (PARTITION BY Element_1 ORDER BY Element_2)";
    let oXpr = {
      expr: {
        func: "LAG",
        args: [{ ref: ["Element_0"] }, { ref: ["Element_1"] }, { xpr: ["-", { ref: ["Element_0"] }] }],
        xpr: ["over", { xpr: ["partition", "by", { ref: ["Element_1"] }, "order", "by", { ref: ["Element_2"] }] }],
      },
    } as any;
    lagEl.expression = sXpr;
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(lagEl, oXpr);
    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    delete oCsn.definitions.csnTest[nsQueryBuilder.ViewModelToCsn.CDS_ANNOTATION_UIMODEL];
    delete oCsn.definitions.csnTest.params;
    const groupByVal = JSON.stringify(oCsn.definitions.csnTest.query.SELECT.groupBy);
    assert.deepEqual(groupByVal, JSON.stringify([{ ref: ["Entity 1", "Element_2"] }]), "csn ok");
  });
  it("Test CascadeChangeElementNewName", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel._unitTesting = true;
    const oOutput = oNewModel.output;
    const oEntity1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      { name: "Entity1" },
      oNewModel
    );
    const oElement1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "e1", dataType: "cds.String", length: 10 },
      oEntity1
    );
    const oElement2 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Element2", dataType: "cds.Integer", length: 10 },
      oEntity1
    );
    const oCalc1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.CalculatedElements",
      { name: "Calc1" },
      oNewModel
    );
    const oCalcElt1 = nsQueryBuilder.NodeImpl.createNewCalculatedElement(oCalc1);

    oCalcElt1.name = "Calc11";

    /*
    oEntity1 -- oCalc1--> oOutput
    */
    oEntity1.successorNode = oCalc1;
    oCalc1.successorNode = oOutput;
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oElement1.successorElement, {
      ref: ["e1"],
    });
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oElement2.successorElement, {
      ref: ["Element2"],
    });

    /**
     * test for FPA101-5984
     */
    const sXpr = "CASE WHEN e1 < 3 THEN 'S' WHEN e1 < 6 THEN 'M' ELSE 'L' END";
    const oXpr = {
      xpr: [
        {
          xpr: [
            "case",
            "when",
            { ref: ["e1"] },
            "<",
            { val: 3 },
            "then",
            { val: "S" },
            "when",
            { ref: ["e1"] },
            "<",
            { val: 6 },
            "then",
            { val: "M" },
            "else",
            { val: "L" },
            "end",
          ],
        },
      ],
    };
    oCalcElt1.expression = sXpr;
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oCalcElt1, oXpr);
    oCalcElt1.newName = "";
    assert.equal(oCalcElt1.expression, sXpr, "oCalcElt1 expression should not change when name changed to empty");
    oCalcElt1.newName = "CalcOne";
    assert.equal(oCalcElt1.expression, sXpr, "oCalcElt1 expression should not change when name changed to not empty");
  });
  it("Test Expression Datatype for CDS6", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel._unitTesting = true;
    const oOutput = oNewModel.output;
    const oEntity1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      { name: "Entity1" },
      oNewModel
    );
    const oElement1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "e1", dataType: "cds.String", length: 10 },
      oEntity1
    );
    const oElement2 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Element2", dataType: "cds.Integer" }, // DW101-93464: Integer data type does not need/have length property!
      oEntity1
    );
    const oCalc1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.CalculatedElements",
      { name: "Calc1" },
      oNewModel
    );
    const oCalcElt1 = nsQueryBuilder.NodeImpl.createNewCalculatedElement(oCalc1);

    oCalcElt1.name = "Calc11";

    /*
    oEntity1 -- oCalc1--> oOutput
    */
    oEntity1.successorNode = oCalc1;
    oCalc1.successorNode = oOutput;
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oElement1.successorElement, {
      ref: ["e1"],
    });
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oElement2.successorElement, {
      ref: ["Element2"],
    });
    const oElement3 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Element3", dataType: "cds.Decimal", precision: 7, scale: 3 },
      oEntity1
    );
    oCalcElt1.dataType = "cds.Integer64";
    oCalcElt1.validate();

    let sXpr = "CASE WHEN oElement1>10 THEN oElement1*0.8 ELSE oElement1*1.1 END";
    let oXpr = {
      xpr: [
        {
          xpr: [
            "case",
            "when",
            { ref: ["oElement1"] },
            ">",
            { val: 10 },
            "then",
            { ref: ["oElement1"] },
            "*",
            { val: 0.8 },
            "else",
            { ref: ["oElement1"] },
            "*",
            { val: 1.1 },
            "end",
          ],
        },
      ],
    };
    oCalcElt1.expression = sXpr;
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oCalcElt1, oXpr);
    assert.equal("cds.Double", oCalcElt1.dataType, "calculated element dataType inferred, ok. 20a");
    sXpr = "CASE WHEN oElement1>10 THEN oElement1*8 ELSE oElement1*2 END";
    oXpr = {
      xpr: [
        {
          xpr: [
            "case",
            "when",
            { ref: ["oElement1"] },
            ">",
            { val: 10 },
            "then",
            { ref: ["oElement1"] },
            "*",
            { val: 8 },
            "else",
            { ref: ["oElement1"] },
            "*",
            { val: 2 },
            "end",
          ],
        },
      ],
    };
    oCalcElt1.expression = sXpr;
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oCalcElt1, oXpr);
    assert.equal("cds.Integer", oCalcElt1.dataType, "calculated element dataType inferred, ok. 20a");

    // DW101-30528 & DW11-30538 Ignore data type derivation for SQL Editor and while loading
    // No derivation for SQL Editor
    const derivedDatatype = nsQueryBuilder.NodeImpl.getExpressionDerivedDataType(oXpr, { isSQLEditor: true });
    // check undefined returned data type
    assert.equal(undefined, derivedDatatype, "check undefined derived type for SQL Editor");

    // No derivation while loading..
    oCalcElt1.resource.isLoading = true;
    oCalcElt1.dataType = "cds.Double";
    sXpr = "oElement1*8";
    oXpr = {
      xpr: [{ xpr: [{ ref: ["oElement1"] }, "*", { val: 8 }] }],
    };
    oCalcElt1.expression = sXpr;
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oCalcElt1, oXpr);
    assert.equal("cds.Double", oCalcElt1.dataType, "calculated element dataType not inferred when loading");
    // But otherwise!
    oCalcElt1.resource.isLoading = false;
    oCalcElt1.dataType = "cds.Real";
    sXpr = "oElement1*4";
    oXpr = {
      xpr: [{ xpr: [{ ref: ["oElement1"] }, "*", { val: 4 }] }],
    };
    oCalcElt1.expression = sXpr;
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oCalcElt1, oXpr);
    assert.equal(
      "cds.Integer",
      oCalcElt1.dataType,
      "calculated element dataType corrected from Real to Integer if not loading"
    );

    // DW101-31057 Input parameters must be handled in data type derivation
    // add parameter to output node and check source data types
    const oParam = {
      name: "ViewParam",
      label: "View Parameter ",
      dataType: "cds.String",
      length: 10,
    };
    const oViewParameterClass = sap.galilei.model.getClass("sap.cdw.querybuilder.ViewParameter");
    const newParameter = oViewParameterClass.create(oOutput.resource, oParam);
    oOutput.parameters.push(newParameter);
    const expectedRefDataTypes = [
      { ref: ["Calc11"], dataType: { type: "cds.Integer" } },
      { ref: ["e1"], dataType: { type: "cds.String", length: 10 } },
      { ref: ["Element2"], dataType: { type: "cds.Integer" } },
      { ref: ["Element3"], dataType: { type: "cds.Decimal", precision: 7, scale: 3 } },
      { ref: ["ViewParam"], dataType: { type: "cds.String", length: 10 }, param: true },
    ];
    const actualRefDataTypes = nsQueryBuilder.NodeImpl.getSourceDataTypes(oOutput);
    assert.deepStrictEqual(
      actualRefDataTypes,
      expectedRefDataTypes,
      "Check source data types include input parameters"
    );
  });
  it("Expression with nested xpr in CDS6 should not throw error", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel._unitTesting = true;
    const oOutput = oNewModel.output;
    const oEntity1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      { name: "Entity1" },
      oNewModel
    );
    const oElement1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "e1", dataType: "cds.String", length: 10 },
      oEntity1
    );
    const oElement2 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Element2", dataType: "cds.String", length: 10 },
      oEntity1
    );
    const oElement3 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Element3", dataType: "cds.String", length: 10 },
      oEntity1
    );
    const oElement4 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Element4", dataType: "cds.String", length: 10 },
      oEntity1
    );
    const oElement5 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Element5", dataType: "cds.Date", length: 10 },
      oEntity1
    );

    const oCalc1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.CalculatedElements",
      { name: "Calc1" },
      oNewModel
    );
    const oCalcElt1 = nsQueryBuilder.NodeImpl.createNewCalculatedElement(oCalc1);

    oCalcElt1.name = "Calc11";

    /*
    oEntity1 -- oCalc1--> oOutput
    */
    oEntity1.successorNode = oCalc1;
    oCalc1.successorNode = oOutput;
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oElement1.successorElement, {
      ref: ["e1"],
    });
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oElement2.successorElement, {
      ref: ["Element2"],
    });
    oCalcElt1.dataType = "cds.Integer64";
    oCalcElt1.validate();

    const sXpr =
      "case when oElement1 = 'ACT01' and oElement2 = 'TBRR' and oElement3 = 'PR' and ( oElement4 = 'UNBILL_REV' or oElement4 = 'ACR_REV' or oElement4 = 'DEF_REV' ) and oElement5 <= CURRENT_DATE() then DAYS_BETWEEN(oElement5, CURRENT_DATE()) else 0 end";
    const oXpr = {
      xpr: [
        "case",
        "when",
        {
          ref: ["oElement1"],
        },
        "=",
        {
          val: "ACT01",
        },
        "and",
        {
          ref: ["oElement2"],
        },
        "=",
        {
          val: "TBRR",
        },
        "and",
        {
          ref: ["oElement3"],
        },
        "=",
        {
          val: "PR",
        },
        "and",
        {
          xpr: [
            {
              ref: ["oElement4"],
            },
            "=",
            {
              val: "UNBILL_REV",
            },
            "or",
            {
              ref: ["oElement4"],
            },
            "=",
            {
              val: "ACR_REV",
            },
            "or",
            {
              ref: ["oElement4"],
            },
            "=",
            {
              val: "DEF_REV",
            },
          ],
        },
        "and",
        {
          ref: ["oElement5"],
        },
        "<=",
        {
          func: "CURRENT_DATE",
          args: [],
        },
        "then",
        {
          func: "DAYS_BETWEEN",
          args: [
            {
              ref: ["oElement5"],
            },
            {
              func: "CURRENT_DATE",
              args: [],
            },
          ],
        },
        "else",
        {
          val: 0,
        },
        "end",
      ],
    };
    oCalcElt1.expression = sXpr;
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oCalcElt1, oXpr);
    oOutput.validate();
    assert.equal(
      oOutput.aggregatedValidations && oOutput.aggregatedValidations.validations.length,
      0,
      "NO validation error latest CDS6 expression2"
    );
  });
  it("Test CSN transformation with non-unique element names and Filter condition", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel.name = "csnTest";
    oNewModel._unitTesting = true;
    const oOutput = oNewModel.output;
    const oEntity1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      {
        name: "Entity1",
      },
      oNewModel
    );
    const oElement1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      {
        name: "ElementSame",
        dataType: "cds.integer",
      },
      oEntity1
    );
    const oElement2 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      {
        name: "Element2",
        dataType: "cds.double",
      },
      oEntity1
    );

    const oEntity2 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      {
        name: "Entity2",
      },
      oNewModel
    );
    const oElement21 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      {
        name: "ElementSame",
        dataType: "cds.String",
        length: 10,
      },
      oEntity2
    );
    const oElement22 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      {
        name: "Element22",
        dataType: "cds.String",
        length: 10,
      },
      oEntity2
    );

    const oEntity3 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      {
        name: "Entity3",
      },
      oNewModel
    );
    const oElement31 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      {
        name: "Element31",
        dataType: "cds.String",
        length: 10,
      },
      oEntity3
    );
    const oElement32 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      {
        name: "Element32",
        dataType: "cds.String",
        length: 10,
      },
      oEntity3
    );

    const oCalc1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.CalculatedElements",
      {
        name: "Calc1",
      },
      oNewModel
    );
    const oCalcElt11 = nsQueryBuilder.NodeImpl.createNewCalculatedElement(oCalc1);
    oCalcElt11.name = "Calc11";
    oCalcElt11.expression = "(ElementSame+Element2)";

    const oJoin1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Join",
      {
        name: "Join1",
        type: "inner",
      },
      oNewModel
    );

    const oFilter1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Filter",
      {
        name: "Filter1",
      },
      oNewModel
    );

    const oUnion1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Union",
      {
        name: "Union1",
        isUnionAll: false, // Default is union all, so set to union
      },
      oNewModel
    );

    const oAssociation1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Association",
      {
        name: "oAssociation1",
      },
      oNewModel
    );
    const oAssociation2 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Association",
      {
        name: "oAssociation2",
      },
      oNewModel
    );
    const oAssociation3 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Association",
      {
        name: "oAssociation3",
      },
      oNewModel
    );
    const oAssociation4 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Association",
      {
        name: "oAssociation4",
      },
      oNewModel
    );
    const oAssociation5 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Association",
      {
        name: "oAssociation5",
      },
      oNewModel
    );
    const oAssociation6 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Association",
      {
        name: "oAssociation6",
      },
      oNewModel
    );

    /*
    oEntity1 -- oCalc1 -->
                            oJoin1 --> oFilter1 -->
                 oEntity2 -->
                                                  oUnion1 --> oOutput
                                        oEntity3 -->
    */
    oEntity1.successorNode = oCalc1;
    oCalc1.successorNode = oJoin1;
    oEntity2.successorNode = oJoin1;
    oJoin1.successorNode = oFilter1;
    oFilter1.successorNode = oUnion1;
    oEntity3.successorNode = oUnion1;
    oUnion1.successorNode = oOutput;
    oAssociation1.source = oEntity1;
    oAssociation1.target = oJoin1;
    oAssociation2.source = oEntity2;
    oAssociation2.target = oJoin1;
    oAssociation3.source = oJoin1;
    oAssociation3.target = oFilter1;
    oAssociation4.source = oFilter1;
    oAssociation4.target = oUnion1;
    oAssociation5.source = oEntity3;
    oAssociation5.target = oUnion1;
    oAssociation6.source = oUnion1;
    oAssociation6.target = oOutput;

    oFilter1.condition = "Element2 > 11 and Element22 != 12";
    // since for unit tests we cannot call backend service to parse
    // expressions and our aim is not to validate parser. we give the
    // expected parsed expresion also.
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oFilter1, {
      xpr: [
        {
          ref: ["Element2"],
        },
        ">",
        {
          val: 11,
        },
        "and",
        {
          ref: ["Element22"],
        },
        "!=",
        {
          val: 12,
        },
      ],
    });

    // since for unit tests we cannot call backend service to parse
    // expressions and our aim is not to validate parser. we give the
    // expected parsed expresion also.
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oCalcElt11, {
      xpr: [
        "(",
        {
          ref: ["ElementSame"],
        },
        "+",
        {
          ref: ["Element2"],
        },
        ")",
      ],
    });
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oElement1.successorElement, {
      ref: ["ElementSame"],
    });
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oElement2.successorElement, {
      ref: ["Element2"],
    });
    // Adding double to string in new derivator gives a string
    assert.equal("cds.String", oCalcElt11.dataType, "calculated datatype inferred ok");

    // Set mappings for Join
    oJoin1.resource.isLoading = true;
    const oElementMapping1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.commonmodel.ElementMapping",
      {
        name: "ElementMapping1",
        source: oElement1,
        target: oElement21,
      },
      oJoin1
    );
    oJoin1.mappings.push(oElementMapping1);
    const oElementMapping2 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.commonmodel.ElementMapping",
      {
        name: "ElementMapping2",
        source: oElement2,
        target: oElement22,
      },
      oJoin1
    );
    oJoin1.mappings.push(oElementMapping2);
    oJoin1.resource.isLoading = false;

    // Set mapping for union
    let i,
      iMax = oFilter1.elements.length,
      oTempElement,
      oTempUnion1Element1;
    for (i = 0; i < iMax; i++) {
      oTempElement = oFilter1.elements.get(i);
      oTempUnion1Element1 = nsQueryBuilder.NodeImpl._copyElement(oTempElement, oUnion1);
    }
    iMax = oEntity3.elements.length;
    for (i = 0; i < iMax; i++) {
      oTempElement = oEntity3.elements.get(i);
      oTempUnion1Element1 = nsQueryBuilder.NodeImpl._copyElement(oTempElement, oUnion1);
    }

    const oExpectedResult = [
      {
        ref: ["Entity1", "ElementSame"],
      },
      "=",
      {
        ref: ["Entity2", "ElementSame"],
      },
      "and",
      {
        ref: ["Entity1", "Element2"],
      },
      "=",
      {
        ref: ["Entity2", "Element22"],
      },
    ];

    oCalcElt11.dataType = "cds.Binary";
    oCalcElt11.length = 0;

    const oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    let sResult = JSON.stringify(oCsn.definitions.csnTest.query.SET.args[1].SELECT.from.on);
    assert.equal(
      JSON.stringify(oExpectedResult),
      sResult,
      "To provide entity name for non unique elements name is correct for Join on."
    );
    sResult = JSON.stringify(oCsn.definitions.csnTest.query.SET.args[1].SELECT.columns[1].ref);
    assert.equal(
      '["Calc1","ElementSame"]',
      sResult,
      "To provide entity name for non unique elements name is correct for columns."
    );
    sResult = JSON.stringify(oCsn.definitions.csnTest.query.SET.args[1].SELECT.where);
    assert.equal(
      sResult,
      '[{"ref":["Calc1","Element2"]},">",{"val":11},"and",{"ref":["Entity2","Element22"]},"!=",{"val":12}]',
      "Filter condition to create Where clause is correct."
    );
    assert.equal("inner", oCsn.definitions.csnTest.query.SET.args[1].SELECT.from.join);
    assert.equal("union", oCsn.definitions.csnTest.query.SET.op);
    sResult = JSON.stringify(oCsn.definitions.csnTest.query.SET.args[1].SELECT.columns[0]);
    assert.equal(
      sResult,
      '{"ref":["Calc1","Calc11"]}',
      "not set value as null to the element if it is belonging to the sub set."
    );
    sResult = JSON.stringify(oCsn.definitions.csnTest.query.SET.args[0].SELECT.columns[0]);
    assert.equal(
      sResult,
      '{"val":null,"as":"Calc11"}',
      "set value as null to the element if it is not belonging to the sub set."
    );
    sResult = JSON.stringify(oCsn.definitions.csnTest.elements.Calc11);
    assert.equal(
      sResult,
      '{"@EndUserText.label":"Calculated_1","type":"cds.Binary","length":5000}',
      "default length of cds.Binary is set to 5000."
    );
  });
  it("Basic tests emulating data type derivation from SQL Editor", async function () {
    // Tests based on SQL Statement:
    // << select
    // e."empFirstName" || ', ' || e."empLastName" "employeeFullName",
    // YEARS_BETWEEN(CURRENT_DATE, e."empBirthDate") "employeeAge",
    // m."empLastName" "managerName"
    // from "Employee" e join "Employee" m on m."empId" = e."empManager" >>
    //
    // Based on Employee Table having elements:
    // << elements {
    //   "@EndUserText.label": "Employee Id",
    // "empId": {
    //   "type": "cds.Integer64",
    //   "key": true,
    //   "notNull": true
    // },
    // "empFirstName": {
    //   "@EndUserText.label": "Employee First Name",
    //   "type": "cds.String",
    //   "length": 64
    // },
    // "empLastName": {
    //   "@EndUserText.label": "Employee Last Name",
    //   "type": "cds.String",
    //   "length": 64
    // },
    // "empBirthDate": {
    //   "@EndUserText.label": "Employee Birth Date",
    //   "type": "cds.Date"
    // },
    // "empManager": {
    //   "@EndUserText.label": "Employee Manager Id",
    //   "type": "cds.Integer64",
    //   "@ObjectModel.foreignKey.association": {
    //     "=": "_Employee"
    //   }
    // },
    // } >>
    //
    // The buildcqn returns
    //   {"sql":"SELECT e.\"empFirstName\" || ', ' || e.\"empLastName\" \"employeeFullName\",\n\tYEARS_BETWEEN(CURRENT_DATE, e.\"empBirthDate\") \"employeeAge\",\n\tm.\"empLastName\" \"managerName\"\nFROM \"Employee\" e\n\tJOIN \"Employee\" m ON m.\"empId\" = e.\"empManager\"",
    //   "csn":{
    //    "SELECT":{
    //     "from":{"join":"inner","args":[{"ref":["Employee"],"as":"e"},{"ref":["Employee"],"as":"m"}],"on":[{"ref":["m","empId"]},"=",{"ref":["e","empManager"]}]},
    //     "columns":[
    //      {"xpr":[{"ref":["e","empFirstName"]},"||",{"val":", "},"||",{"ref":["e","empLastName"]}],"as":"employeeFullName"},
    //      {"func":"YEARS_BETWEEN","args":[{"func":"CURRENT_DATE"},{"ref":["e","empBirthDate"]}],"as":"employeeAge"},
    //      {"ref":["m","empLastName"],"as":"managerName"}
    //     ]
    //    }
    //   }
    //  }

    // Instantiate Expression Analyzer
    const expressionAnalyzer = new ExpressionAnalyzer();
    // First step: initialize ALL source columns using ALL sources (including aliases!)
    const params: any = {
      refDataTypes: [
        { ref: ["e", "empId"], dataType: { type: "cds.Integer64" } },
        { ref: ["e", "empFirstName"], dataType: { type: "cds.String", length: 64 } },
        { ref: ["e", "empLastName"], dataType: { type: "cds.String", length: 64 } },
        { ref: ["e", "empBirthDate"], dataType: { type: "cds.Date" } },
        { ref: ["e", "empManager"], dataType: { type: "cds.Integer64" } },
        { ref: ["m", "empId"], dataType: { type: "cds.Integer64" } },
        { ref: ["m", "empFirstName"], dataType: { type: "cds.String", length: 64 } },
        { ref: ["m", "empLastName"], dataType: { type: "cds.String", length: 64 } },
        { ref: ["m", "empBirthDate"], dataType: { type: "cds.Date" } },
        { ref: ["m", "empManager"], dataType: { type: "cds.Integer64" } },
      ],
    };
    // Initialize Expression Analyser only once
    expressionAnalyzer.initParameters(params);
    // Call data type derivation for each column
    const columns = [
      {
        parsedExpression: {
          xpr: [{ ref: ["e", "empFirstName"] }, "||", { val: ", " }, "||", { ref: ["e", "empLastName"] }],
          as: "employeeFullName",
        },
        expectedDataType: { type: "cds.String", length: 130 },
      },
      {
        parsedExpression: {
          func: "YEARS_BETWEEN",
          args: [{ func: "CURRENT_DATE" }, { ref: ["e", "empBirthDate"] }],
          as: "employeeAge",
        },
        expectedDataType: { type: "cds.Integer" },
      },
      {
        parsedExpression: { ref: ["m", "empLastName"], as: "managerName" },
        expectedDataType: { type: "cds.String", length: 64 },
      },
    ];
    // Valid expressions
    for (const c of columns) {
      const result: any = {};
      expressionAnalyzer.deriveDataType(c.parsedExpression, result);
      assert.equal(c.expectedDataType.type, result.dataType.type, "derived type OK");
      if (c.expectedDataType.length) {
        assert.equal(c.expectedDataType.length, result.dataType.length, "derived length OK");
      }
    }
    // TODO: invalid expressions? (Not yet handled in skyline side for such usage)
  });

  it("Test CSN transformation for simple calculated attribute", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel.name = "csnTest";
    oNewModel._unitTesting = true;
    const oOutput = oNewModel.output;
    const oEntity1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      {
        name: "Entity1",
      },
      oNewModel
    );
    const oElement1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      {
        name: "ElementSame",
        dataType: "cds.String",
        length: 10,
      },
      oEntity1
    );
    const oElement2 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      {
        name: "Element2",
        dataType: "cds.String",
        length: 10,
      },
      oEntity1
    );
    const oCalc1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.CalculatedElements",
      {
        name: "Calc1",
      },
      oNewModel
    );
    const oCalcElt11 = nsQueryBuilder.NodeImpl.createNewCalculatedElement(oCalc1);
    const oFilter1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Filter",
      {
        name: "Filter1",
      },
      oNewModel
    );

    oCalcElt11.name = "Calc11";

    /*
    oEntity1 -- oCalc1--> Filter --> oOutput
    */
    oEntity1.successorNode = oCalc1;
    oCalc1.successorNode = oFilter1;
    oFilter1.successorNode = oOutput;
    oFilter1.condition = "ElementSame>200";
    // since for unit tests we cannot call backend service to parse
    // expressions and our aim is not to validate parser. we give the
    // expected parsed expresion also.
    nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oFilter1, {
      xpr: [
        {
          ref: ["ElementSame"],
        },
        ">",
        {
          val: 200,
        },
      ],
    });

    oCalcElt11.expression = "(ElementSame+Element2)";
    // since for unit tests we cannot call backend service to parse
    // expressions and our aim is not to validate parser. we give the
    // expected parsed expresion also.
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oCalcElt11, {
      xpr: [
        "(",
        {
          ref: ["ElementSame"],
        },
        "+",
        {
          ref: ["Element2"],
        },
        ")",
      ],
    });
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oElement1.successorElement, {
      ref: ["ElementSame"],
    });
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oElement2.successorElement, {
      ref: ["Element2"],
    });

    oEntity1.isDistinct = true;

    let oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    let sResult = JSON.stringify(oCsn.definitions.csnTest.query.SELECT);
    assert.equal(
      sResult,
      '{"from":{"ref":["Entity1"]},"columns":[{"as":"Calc11","xpr":["(",{"ref":["Entity1","ElementSame"]},"+",{"ref":["Entity1","Element2"]},")"]},{"as":"ElementSame","ref":["Entity1","ElementSame"]},{"as":"Element2","ref":["Entity1","Element2"]}],"where":[{"ref":["Entity1","ElementSame"]},">",{"val":200}],"distinct":true}',
      // assert.equal(sResult, '{"from":{"ref":["Entity1"]},"columns":[{"as":"Calc11","xpr":["(",{"ref":["Entity1","ElementSame"]},"+",{"ref":["Entity1","Element2"]},")"]},{"ref":["ElementSame"]},{"ref":["Element2"]}],"where":[{"ref":["Entity1","ElementSame"]},">",{"val":200}]}',
      "simple case csn SELECT ok"
    );
    assert.equal("cds.String", oCalcElt11.dataType, "calculated element dataType inferred");

    // check new name propagations
    oElement1.newName = "ElementSameNameNew";
    oElement2.newName = "Element2New";
    assert.equal(
      "( ElementSameNameNew + Element2New )",
      oCalcElt11.expression,
      "expression updated after new name changed"
    );

    // filter case
    assert.equal("ElementSameNameNew > 200", oFilter1.condition, "expression updated after new name changed");

    // check alias propagations
    oEntity1.alias = "E1";
    let csnExp = oCalcElt11.parsedExpression.toCSNExp();
    assert.equal("E1", csnExp.xpr[1].ref[0], "expression updated after alias changed");
    assert.equal("E1", csnExp.xpr[3].ref[0], "expression updated after alias changed");

    // filter case
    csnExp = oFilter1.parsedExpression.toCSNExp();
    assert.equal("E1", csnExp.xpr[0].ref[0], "filter condition updated after alias changed");

    oCalc1.deleteObject();
    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    sResult = JSON.stringify(oCsn.definitions.csnTest.query.SELECT);
    assert.equal(
      sResult,
      '{"from":{"ref":["Entity1"],"as":"E1"},"columns":[{"as":"ElementSameNameNew","ref":["ElementSame"]},{"as":"Element2New","ref":["Element2"]}],"where":[{"ref":["E1","ElementSameNameNew"]},">",{"val":200}],"distinct":true}',
      "csn after delete SELECT ok"
    );

    oNewModel.resource.undo(); // undo delete calc elements
    oFilter1.condition = "Calc11>200";
    // since for unit tests we cannot call backend service to parse
    // expressions and our aim is not to validate parser. we give the
    // expected parsed expresion also.
    let parsedExpression: any = {
      xpr: [
        {
          ref: ["Calc11"],
        },
        ">",
        {
          val: 200,
        },
      ],
    };
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oFilter1, parsedExpression);
    // DW101-37120 - Check stringifying various parsed expressions..
    let expectedExpression = "Calc11 > 200";
    let stringifiedExpression = oFilter1.parsedExpression.stringifyParsedExpression(oFilter1.parsedExpression);
    assert.equal(stringifiedExpression, expectedExpression, "Simple stringified expression");
    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    sResult = JSON.stringify(oCsn.definitions.csnTest.query.SELECT.where);
    assert.equal(
      '["(","(",{"ref":["E1","ElementSameNameNew"]},"+",{"ref":["E1","Element2New"]},")",")",">",{"val":200}]',
      sResult,
      "csn after filter on calc column ok"
    );

    oCalcElt11.expression = "";
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oCalcElt11, {}); // Unit test is different than real runtime, so has to add this
    oCalcElt11.parsedExpression.expression = undefined; // Unit test is different than real runtime, so has to add this
    csnExp = oCalcElt11.parsedExpression.toCSNExp();
    assert.equal("{}", JSON.stringify(csnExp), "Empty the expression test");

    // DW101-37120 - more complex expressions
    // Function with more than two arguments: COALESCE(Calc11, Calc11 || '', 'X')
    // {"func":"COALESCE","args":[{"ref":["Calc11"]},{"xpr":[{"ref":["Calc11"]},"||",{"val":""}]},{"val":"X"}]}
    parsedExpression = {
      func: "COALESCE",
      args: [{ ref: ["Calc11"] }, { xpr: [{ ref: ["Calc11"] }, "||", { val: "++" }] }, { val: "X" }],
    };
    expectedExpression = "COALESCE(Calc11, Calc11 || '++', 'X')";
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oFilter1, parsedExpression); // Needed call for Unit tests
    stringifiedExpression = oFilter1.parsedExpression.stringifyParsedExpression(oFilter1.parsedExpression);
    assert.equal(
      stringifiedExpression,
      expectedExpression,
      "Stringified expression with function call having more than 3 arguments"
    );

    // IN clause: Calc11 in (1, 2, 3)
    // {xpr:[{"ref": ["Calc11"]},"in",{"list": [{"val": 1}, {"val": 2}, {"val": 3}]},]}
    parsedExpression = {
      xpr: [{ ref: ["Calc11"] }, "in", { list: [{ val: 1 }, { val: 2 }, { val: 3 }] }],
    };
    expectedExpression = "Calc11 in (1, 2, 3)";
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oFilter1, parsedExpression); // Needed call for Unit tests
    stringifiedExpression = oFilter1.parsedExpression.stringifyParsedExpression(oFilter1.parsedExpression);
    assert.equal(
      stringifiedExpression,
      expectedExpression,
      "Stringified expression with IN clause having numeric values"
    );

    // Nested parenthesis: ((Calc1+1234567891123456879)*5)/Calc1
    // {"xpr":[{"xpr":[{"xpr":[{"ref":["Calc1"]},"+",{"val":"1234567891123456879","literal":"number"}]},"*",{"val":5}]},"/",{"ref":["Calc1"]}]}
    parsedExpression = {
      xpr: [
        {
          xpr: [
            {
              xpr: [
                {
                  ref: ["Calc1"],
                },
                "+",
                { val: "1234567891123456879", literal: "number" },
              ],
            },
            "*",
            { val: 5 },
          ],
        },
        "/",
        { ref: ["Calc1"] },
      ],
    };
    expectedExpression = "((Calc1 + 1234567891123456879) * 5) / Calc1";
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oFilter1, parsedExpression); // Needed call for Unit tests
    stringifiedExpression = oFilter1.parsedExpression.stringifyParsedExpression(oFilter1.parsedExpression);
    assert.equal(
      stringifiedExpression,
      expectedExpression,
      "Stringified expression having nested parenthesis and huge integer"
    );
  });

  it("Test CSN transformation for calculated element refering to other calculated element", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel.name = "csnTest";
    oNewModel._unitTesting = true;
    const oOutput = oNewModel.output;
    const oEntity1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      {
        name: "Entity1",
      },
      oNewModel
    );
    const oElement1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      {
        name: "Element1",
        dataType: "cds.String",
        length: 10,
      },
      oEntity1
    );
    const oCalc1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.CalculatedElements",
      {
        name: "Calc1",
      },
      oNewModel
    );
    const oCalcElt1 = nsQueryBuilder.NodeImpl.createNewCalculatedElement(oCalc1);
    oCalcElt1.name = "CalcElt1";
    const oCalcElt2 = nsQueryBuilder.NodeImpl.createNewCalculatedElement(oCalc1);
    oCalcElt2.name = "CalcElt2";

    /*
    oEntity1 --> oCalc1 --> oOutput
    */
    oEntity1.successorNode = oCalc1;
    oCalc1.successorNode = oOutput;
    oCalcElt1.expression = "Element1+2";
    oCalcElt2.expression = "3+CalcElt1";
    // since for unit tests we cannot call backend service to parse
    // expressions and our aim is not to validate parser. we give the
    // expected parsed expresion also.
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oCalcElt1, {
      xpr: [
        {
          ref: ["Element1"],
        },
        "+",
        {
          val: 2,
        },
      ],
    });
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oCalcElt2, {
      xpr: [
        {
          val: 3,
        },
        "+",
        {
          ref: ["Calc1"],
        },
      ],
    });

    oEntity1.isDistinct = true;
    let csnExp = oCalcElt1.parsedExpression.toCSNExp();
    assert.equal(
      JSON.stringify(csnExp),
      '{"xpr":[{"ref":["Entity1","Element1"]},"+",{"val":2}]}',
      "toCSNExp on normal calculated element works."
    );

    oCalcElt2.parsedExpression.expression.xpr[2].refElement = oCalcElt1;
    csnExp = oCalcElt2.parsedExpression.toCSNExp();
    assert.equal(
      JSON.stringify(csnExp),
      '{"xpr":[{"val":3},"+","(",{"ref":["Entity1","Element1"]},"+",{"val":2},")"]}',
      "toCSNExp on calculated element refering to other calculated element works."
    );

    oCalcElt2.expression = "CEIL(CalcElt1)";
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oCalcElt2, {
      func: "CEIL",
      args: [{ ref: ["Calc1"] }],
    });
    oCalcElt2.parsedExpression.expression.args[0].refElement = oCalcElt1;
    csnExp = oCalcElt2.parsedExpression.toCSNExp();
    assert.equal(
      JSON.stringify(csnExp),
      '{"func":"CEIL","args":[{"xpr":["(",{"ref":["Entity1","Element1"]},"+",{"val":2},")"]}]}',
      "toCSNExp on calculated element refering to other calculated element in function works."
    );
  });

  it("Test CSN transformation for complex expression", async function () {
    const sXpr = "ABS(ElementSame + 1) > 1 AND (Element2 != ('ab' + ElementSame))";
    const oXpr = {
      xpr: [
        {
          func: "ABS",
          args: [
            {
              xpr: [
                {
                  ref: ["ElementSame"],
                },
                "+",
                {
                  val: 1,
                },
              ],
            },
          ],
        },
        ">",
        {
          val: 1,
        },
        "and",
        "(",
        {
          ref: ["Element2"],
        },
        "!=",
        "(",
        {
          val: "ab",
        },
        "+",
        {
          ref: ["ElementSame"],
        },
        ")",
        ")",
      ],
    };
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel.name = "csnTest";
    oNewModel._unitTesting = true;
    const oOutput = oNewModel.output;
    const oEntity1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      { name: "Entity1" },
      oNewModel
    );
    let oElement1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "ElementSame", dataType: "cds.String", length: 10 },
      oEntity1
    );
    let oElement2 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Element2", dataType: "cds.String", length: 10 },
      oEntity1
    );
    const oCalc1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.CalculatedElements",
      { name: "Calc1" },
      oNewModel
    );
    let oCalcElt11 = nsQueryBuilder.NodeImpl.createNewCalculatedElement(oCalc1);
    const oFilter1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Filter",
      { name: "Filter1" },
      oNewModel
    );

    oCalcElt11.name = "Calc11";

    /*
    oEntity1 -- oCalc1--> Filter --> oOutput
    */
    oEntity1.successorNode = oCalc1;
    oCalc1.successorNode = oFilter1;
    oFilter1.successorNode = oOutput;
    oFilter1.condition = sXpr;
    // since for unit tests we cannot call backend service to parse
    // expressions and our aim is not to validate parser. we give the
    // expected parsed expresion also.
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oFilter1, oXpr);
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oElement1.successorElement, {
      ref: ["ElementSame"],
    });
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oElement2.successorElement, {
      ref: ["Element2"],
    });

    oCalcElt11.expression = sXpr;
    // since for unit tests we cannot call backend service to parse
    // expressions and our aim is not to validate parser. we give the
    // expected parsed expresion also.
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oCalcElt11, oXpr);

    const oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    const sResult = JSON.stringify(oCsn.definitions.csnTest.query.SELECT);
    assert.equal(
      sResult,
      '{"from":{"ref":["Entity1"]},"columns":[{"as":"Calc11","xpr":[{"func":"ABS","args":[{"xpr":[{"ref":["Entity1","ElementSame"]},"+",{"val":1}]}]},">",{"val":1},"and","(",{"ref":["Entity1","Element2"]},"!=","(",{"val":"ab"},"+",{"ref":["Entity1","ElementSame"]},")",")"]},{"as":"ElementSame","ref":["Entity1","ElementSame"]},{"as":"Element2","ref":["Entity1","Element2"]}],"where":[{"func":"ABS","args":[{"xpr":[{"ref":["Entity1","ElementSame"]},"+",{"val":1}]}]},">",{"val":1},"and","(",{"ref":["Entity1","Element2"]},"!=","(",{"val":"ab"},"+",{"ref":["Entity1","ElementSame"]},")",")"]}',
      "complex case csn SELECT ok"
    );
    assert.equal("cds.Integer", oCalcElt11.dataType, "calculated element dataType inferred");

    /*
    oEntity1 -- RenameElements --> oCalcElts--> Filter --> oOutput
    */
    const oRenameElements = createIntermediateOperation(oNewModel, "RenameElements", oEntity1);
    oElement1 = oRenameElements.elements.get(0);
    oElement2 = oRenameElements.elements.get(1);
    oCalcElt11 = oCalc1.elements.get(0);

    // check new name propagations
    oElement1.newName = "ElementSameNameNew";
    oElement2.newName = "Element2New";
    assert.equal(
      oCalcElt11.expression,
      "ABS(ElementSameNameNew + 1) > 1 and ( Element2New != ( 'ab' + ElementSameNameNew ) )",
      "expression updated after new name changed"
    );

    // filter case
    assert.equal(
      oFilter1.condition,
      "ABS(ElementSameNameNew + 1) > 1 and ( Element2New != ( 'ab' + ElementSameNameNew ) )",
      "expression updated after new name changed"
    );
  });

  it("addQueryEntity use technicalName as key in csn", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel._unitTesting = true;
    const oViewModelToCsn = new nsQueryBuilder.ViewModelToCsn();
    const oOutput = oNewModel.output;
    const name = oOutput.name;
    oOutput.technicalName = name + "_tech";
    oOutput.name = name;
    const oCsn = { definitions: {} };
    oViewModelToCsn.addQueryEntity(oCsn, oOutput, false, undefined, false /* preview */);
    assert.deepEqual(
      oCsn.definitions,
      {
        View_1_tech: {
          kind: "entity",
          elements: {},
          query: {
            SELECT: {
              from: {},
              columns: [],
            },
          },
          "@EndUserText.label": "View 1",
          "@DataWarehouse.consumption.external": true,
          "@ObjectModel.modelingPattern": {
            "#": "DATA_STRUCTURE",
          },
          "@ObjectModel.supportedCapabilities": [
            {
              "#": "DATA_STRUCTURE",
            },
          ],
        },
      },
      "addQueryEntity use technicalName as key in csn ok"
    );
  });

  it("DW00-7159 New annotation @DataWarehouse.consumption.external and supportedCapabilities new values testing", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel._unitTesting = true;
    const oOutput = oNewModel.output;
    const oParameter = sap.cdw.commonmodel.ModelImpl.createObject("sap.cdw.commonmodel.Parameter", {}, oOutput) as any;
    oOutput.dataCategory = DataCategory.DIMENSION;

    let oCsn = {} as any;
    oOutput.isAllowConsumption = false;
    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("View_1", oNewModel);
    assert.deepEqual(
      oCsn.definitions.View_1["@ObjectModel.supportedCapabilities"],
      [{ "#": "ANALYTICAL_DIMENSION" }],
      "Annotation @ObjectModel.supportedCapabilities ok1"
    );
    assert.deepEqual(
      oCsn.definitions.View_1["@DataWarehouse.consumption.external"],
      false,
      "Annotation @DataWarehouse.consumption.external ok1"
    );
    oOutput.isAllowConsumption = true;
    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("View_1", oNewModel);
    assert.deepEqual(
      oCsn.definitions.View_1["@ObjectModel.supportedCapabilities"],
      [{ "#": "ANALYTICAL_DIMENSION" }],
      "Annotation @ObjectModel.supportedCapabilities ok2"
    );
    assert.deepEqual(
      oCsn.definitions.View_1["@DataWarehouse.consumption.external"],
      true,
      "Annotation @DataWarehouse.consumption.external ok2"
    );

    oOutput.dataCategory = DataCategory.FACT;
    oOutput.isAllowConsumption = false;
    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("View_1", oNewModel);
    assert.deepEqual(
      oCsn.definitions.View_1["@ObjectModel.supportedCapabilities"],
      [{ "#": "ANALYTICAL_FACT" }],
      "Annotation @ObjectModel.supportedCapabilities ok3"
    );
    assert.deepEqual(
      oCsn.definitions.View_1["@DataWarehouse.consumption.external"],
      false,
      "Annotation @DataWarehouse.consumption.external ok3"
    );
    oOutput.isAllowConsumption = true;
    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("View_1", oNewModel);
    assert.deepEqual(
      oCsn.definitions.View_1["@ObjectModel.supportedCapabilities"],
      [{ "#": "ANALYTICAL_FACT" }, { "#": "ANALYTICAL_PROVIDER" }],
      "Annotation @ObjectModel.supportedCapabilities ok4"
    );
    assert.deepEqual(
      oCsn.definitions.View_1["@DataWarehouse.consumption.external"],
      true,
      "Annotation @DataWarehouse.consumption.external ok4"
    );

    // Initialize with old value and ensure ANALYTICAL_PROVIDER is cleared for relational dataset
    oCsn = { "@ObjectModel.supportedCapabilities": [{ "#": "DATA_STRUCTURE" }, { "#": "ANALYTICAL_PROVIDER" }] };
    oParameter.isAllowConsumption = true;
    oParameter.dataCategory = DataCategory.DATASET;
    csnUtils.allowConsumptionWriteCsn(oParameter, oCsn);
    csnUtils.dataCategoryWriteCsn(oParameter, oCsn);
    assert.deepStrictEqual(
      oCsn,
      {
        "@ObjectModel.supportedCapabilities": [{ "#": "DATA_STRUCTURE" }],
        "@ObjectModel.modelingPattern": { "#": "DATA_STRUCTURE" },
        "@DataWarehouse.consumption.external": true,
      },
      "Update @ObjectModel.supportedCapabilities annotation and set @DataWarehouse.consumption.external to ture is ok"
    );

    // Initialize with old version of ADS and save to new version
    oCsn = { "@ObjectModel.supportedCapabilities": [{ "#": "ANALYTICAL_FACT" }, { "#": "ANALYTICAL_PROVIDER" }] };
    oParameter.isAllowConsumption = true;
    oParameter.dataCategory = DataCategory.FACT;
    csnUtils.allowConsumptionWriteCsn(oParameter, oCsn);
    csnUtils.dataCategoryWriteCsn(oParameter, oCsn);
    assert.deepStrictEqual(
      oCsn,
      {
        "@ObjectModel.supportedCapabilities": [{ "#": "ANALYTICAL_PROVIDER" }, { "#": "ANALYTICAL_FACT" }],
        "@ObjectModel.modelingPattern": { "#": "ANALYTICAL_FACT" },
        "@DataWarehouse.consumption.external": true,
      },
      "Update @ObjectModel.supportedCapabilities annotation and set @DataWarehouse.consumption.external to true is ok"
    );
  });

  it("DW00-6729 New semantic type: FACT", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel._unitTesting = true;
    const oViewModelToCsn = new nsQueryBuilder.ViewModelToCsn();
    const oOutput = oNewModel.output;
    const oParameter = sap.cdw.commonmodel.ModelImpl.createObject("sap.cdw.commonmodel.Parameter", {}, oOutput) as any;
    oOutput.dataCategory = DataCategory.SQLFACT;

    // check create new object
    let oCsn = {} as any;
    oOutput.isAllowConsumption = true;
    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("View_1", oNewModel);
    assert.deepEqual(
      oCsn.definitions.View_1["@ObjectModel.supportedCapabilities"],
      [{ "#": "DATA_STRUCTURE" }],
      "Annotation @ObjectModel.supportedCapabilities has correct value"
    );
    assert.deepEqual(
      oCsn.definitions.View_1["@ObjectModel.modelingPattern"],
      { "#": "ANALYTICAL_FACT" },
      "Annotation @ObjectModel.modelingPattern has correct value"
    );
    assert.deepEqual(
      oCsn.definitions.View_1["@DataWarehouse.consumption.external"],
      true,
      "Annotation @DataWarehouse.consumption.external has correct value"
    );
  });

  it("DW00-9414: New Semantic Type for URL-Provider in Dimension", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel.name = "csnTest";
    oNewModel._unitTesting = true;
    const oOutput = oNewModel.output;
    const oEntity1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      { name: "Entity1" },
      oNewModel
    );
    nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Element1", dataType: "cds.String", length: 10 },
      oEntity1
    );
    oEntity1.successorNode = oOutput;

    const elementInOutput = oOutput.elements.get(0);

    oOutput.dataCategory = DataCategory.FACT;
    const possibleSemanticTypesForADS = elementInOutput.possibleSemanticTypes;
    let isImageUrlExistInADS = false;
    for (const type of possibleSemanticTypesForADS) {
      if (type.key === "@Semantics.imageUrl") {
        isImageUrlExistInADS = true;
        break;
      }
    }
    // Check possibleSemanticTypes for ADS
    assert.strictEqual(isImageUrlExistInADS, false, "@Semantics.imageUrl does not exist in semantic type list");

    oOutput.dataCategory = DataCategory.DIMENSION;

    const possibleSemanticTypesForDimension = elementInOutput.possibleSemanticTypes;
    let isImageUrlExistInDimension = false;
    for (const type of possibleSemanticTypesForDimension) {
      if (type.key === "@Semantics.imageUrl") {
        isImageUrlExistInDimension = true;
        break;
      }
    }
    // Check possibleSemanticTypes for Dimension
    assert.strictEqual(isImageUrlExistInDimension, true, "@Semantics.imageUrl exists in semantic type list");

    elementInOutput.semanticType = SemanticType.IMAGE_URL;
    const oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    const oResult = oCsn.definitions.csnTest.elements;

    // check writeCsn
    assert.deepEqual(
      oResult,
      {
        Element1: { type: "cds.String", "@Semantics.imageUrl": true, length: 10 },
      },
      "Check writeCsn of Element"
    );

    // check readCsn
    elementInOutput.semanticType = SemanticType.EMPTY;
    const inputCsn = {
      "@Semantics.imageUrl": true,
    };
    csnUtils.semanticTypeReadCsn(elementInOutput, inputCsn);
    assert.strictEqual(elementInOutput.semanticType, SemanticType.IMAGE_URL, "Check readyCsn of Element");
  });

  it("Misc tests for missing parts in viewModelToCsn", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel._unitTesting = true;
    const oViewModelToCsn = new nsQueryBuilder.ViewModelToCsn();
    const oOutput = oNewModel.output;
    const oParameter = sap.cdw.commonmodel.ModelImpl.createObject("sap.cdw.commonmodel.Parameter", {}, oOutput) as any;

    let oCsn = {} as any;
    const sDataCategory = oOutput.dataCategory;
    oViewModelToCsn.CSN_PROP_MAPPING.DimensionNode.dataCategory.writeCsn(oOutput, oCsn);
    assert.deepEqual(
      oCsn,
      {
        "@ObjectModel.modelingPattern": {
          "#": "DATA_STRUCTURE",
        },
        "@ObjectModel.supportedCapabilities": [
          {
            "#": "DATA_STRUCTURE",
          },
        ],
      },
      "DimensionNode.dataCategory.writeCsn ok"
    );
    oOutput.dataCategory = "";
    oViewModelToCsn.CSN_PROP_MAPPING.DimensionNode.dataCategory.readCsn(oOutput, oCsn);
    assert.equal(oOutput.dataCategory, sDataCategory, "DimensionNode.dataCategory.readCsn ok");

    oCsn = {};
    oParameter.semanticType = "@Semantics.currencyCode";
    csnUtils.semanticTypeWriteCsn(oParameter, oCsn);
    assert.deepEqual(
      oCsn,
      {
        "@Semantics.currencyCode": true,
      },
      "semanticTypeWriteCsn() is ok"
    );

    oCsn = {};
    // Add ReferenceEntity
    const oEntity = sap.cdw.commonmodel.ModelImpl.createObject(
      "sap.cdw.commonmodel.Entity",
      {
        name: "name1",
      },
      oNewModel
    );
    const oElement = sap.cdw.commonmodel.ModelImpl.createObject(
      "sap.cdw.commonmodel.Element",
      {
        name: "element1",
      },
      oEntity
    ) as sap.cdw.commonmodel.Element;
    const oReferenceEntity = sap.cdw.commonmodel.ModelImpl.createObject(
      "sap.cdw.commonmodel.ReferenceEntity",
      {
        entityName: oEntity.name,
        elementName: oElement.name,
      },
      oParameter
    );
    oParameter.valueHelpDefinition = oReferenceEntity;
    // Save FF
    const savedFF = SupportedFeaturesService.getInstance().isParameterValueHelpEnabled();
    // valueHelp Disabled => writing does not change CSN as it's preserved in unhandledCsn..
    SupportedFeaturesService.getInstance().isParameterValueHelpEnabled = () => false;
    csnUtils.parameterValueHelpWriteCsn(oParameter, oCsn);
    assert.deepEqual(oCsn, {}, "parameterValueHelpWriteCsn() is ok when valueHelp FF disabled");
    // valueHelp Enabled => writing should update csn
    SupportedFeaturesService.getInstance().isParameterValueHelpEnabled = () => true;
    csnUtils.parameterValueHelpWriteCsn(oParameter, oCsn);
    assert.deepEqual(
      oCsn,
      {
        "@Consumption.valueHelpDefinition": [
          {
            entity: {
              name: "name1",
              element: "element1",
            },
          },
        ],
      },
      "parameterValueHelpWriteCsn() is ok when valueHelp FF enabled"
    );
    // Restore FF
    SupportedFeaturesService.getInstance().isParameterValueHelpEnabled = () => savedFF;

    assert.equal(oViewModelToCsn.CSN_CREATOR, nsQueryBuilder.ViewModelToCsn.VIEWEDITOR_CSN_CREATOR, "CSN_CREATOR ok");
    assert.equal(oViewModelToCsn.CSN_KIND, nsQueryBuilder.ViewModelToCsn.VIEWEDITOR_CSN_KIND, "CSN_KIND ok");

    oViewModelToCsn.dataPreview = true;
    const rootObject = { classDefinition: { name: "" }, container: oNewModel, objectId: "111xxx" };
    oCsn = oViewModelToCsn.convertToCsn("", oNewModel, { rootObject });
    delete oCsn.version;
    delete oCsn.meta;
    delete oCsn.$version;
    assert.deepEqual(
      oCsn,
      {
        definitions: {
          View_1: {
            kind: "entity",
            elements: {},
          },
          SQLView: {
            kind: "entity",
            query: {
              SELECT: {
                from: {},
                columns: [],
              },
            },
          },
        },
      },
      "convertToCsn ok"
    );

    oCsn = oViewModelToCsn.addQueryEntity(
      { definitions: {} },
      {
        dataCategory: DataCategory.HIERARCHY,
        name: "entity1",
      },
      false,
      undefined,
      undefined
    );
    assert.deepEqual(
      oCsn,
      {
        elements: {},
        kind: "entity",
        query: {
          SELECT: {
            columns: [],
            from: {},
          },
        },
      },
      "addQueryEntity ok"
    );
    SupportedFeaturesService.getInstance().isAnalyticMeasureFormulaEnabled = () => true;
    SupportedFeaturesService.getInstance().isDataActivationEnabled = () => true;
    SupportedFeaturesService.getInstance().isExceptionAggregationEnabled = () => true;

    oCsn = {};
    oElement.isMeasure = true;
    csnUtils.isMeasureWriteCsn(oElement, oCsn);
    assert.deepEqual(
      oCsn,
      {
        "@AnalyticsDetails.measureType": { "#": "BASE" },
      },
      "isMeasureWriteCsn() is ok"
    );

    oOutput.isAllowConsumption = false;
    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("View_1", oNewModel);
    assert.notEqual(
      oCsn.definitions.View_1["@ObjectModel.supportedCapabilities"],
      undefined,
      "New annotation @ObjectModel.supportedCapabilities ok"
    );
    assert.deepEqual(
      oCsn.definitions.View_1["@ObjectModel.supportedCapabilities"],
      [{ "#": "DATA_STRUCTURE" }],
      "New annotation @ObjectModel.supportedCapabilities ok1"
    );
    oOutput.isAllowConsumption = true;
    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("View_1", oNewModel);
    assert.deepEqual(
      oCsn.definitions.View_1["@ObjectModel.supportedCapabilities"],
      [{ "#": "DATA_STRUCTURE" }],
      "New annotation @ObjectModel.supportedCapabilities ok2"
    );

    // Initialize wrong value and ensure it's replaced
    oCsn = { "@ObjectModel.supportedCapabilities": ["#ANALYTICAL_PROVIDER"] };
    oParameter.isAllowConsumption = true;
    csnUtils.allowConsumptionWriteCsn(oParameter, oCsn);
    assert.deepEqual(
      oCsn,
      {
        "@DataWarehouse.consumption.external": true,
        "@ObjectModel.supportedCapabilities": [],
      },
      "allowConsumptionWriteCsn() is ok"
    );

    // Initialize with old value and ensure it's cleared
    oCsn = { "@Analytics.provider": true };
    oParameter.isAllowConsumption = true;
    csnUtils.allowConsumptionWriteCsn(oParameter, oCsn);
    assert.deepStrictEqual(
      oCsn,
      {
        "@DataWarehouse.consumption.external": true,
        "@ObjectModel.supportedCapabilities": [],
      },
      "Remove old @Analytics.provider annotation is ok"
    );

    // New Label column annotation
    // Read old value ...
    oCsn = { "@ObjectModel.text.element": { "=": "SHORT_DESC" } };
    let labelColumn = LabelColumnAnnotation.getLabelColumn(oCsn);
    assert.deepStrictEqual(labelColumn, "SHORT_DESC", "Old label column is read correctly");
    // Write new one..
    LabelColumnAnnotation.writeCsnValue(oCsn, labelColumn);
    assert.deepStrictEqual(oCsn, { "@ObjectModel.text.element": [{ "=": "SHORT_DESC" }] }, "write new label column OK");
    // Read new value..
    labelColumn = LabelColumnAnnotation.getLabelColumn(oCsn);
    assert.deepStrictEqual(labelColumn, "SHORT_DESC", "New label column is read correctly");
  });

  it("Test CSN transformation for misc tests", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel.name = "csnTest";
    oNewModel._unitTesting = true;
    const oOutput = oNewModel.output;
    const oEntity1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      { name: "Entity1" },
      oNewModel
    );
    const oElement1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Element1", dataType: "cds.String", length: 10 },
      oEntity1
    );
    const oElement2 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Element2", dataType: "cds.String", length: 10 },
      oEntity1
    );
    const oElement3 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Element3", dataType: "cds.String", length: 10 },
      oEntity1
    );

    oElement1.semanticType = SemanticType.AMOUNT_WITH_CURRENCY;
    oElement1.unitTypeElement = oElement3;
    oElement1.defaultAggregation = "defaultAggregation1";
    oElement1.isVisible = false;
    oElement1.foreignKey = "foreignKey1";
    oElement1.precision = 2;
    oElement1.scale = 1;
    oElement2.semanticType = "semanticType2";
    oElement2.length = 3;
    oElement3.semanticType = SemanticType.CURRENCY_CODE;

    /*
    oEntity1 --> oOutput
    */
    oEntity1.successorNode = oOutput;

    const oOutputElement1 = oOutput.elements.get(0);
    oOutputElement1.unitTypeElement = oElement1.unitTypeElement.successorElement;
    oOutputElement1.isVisible = oElement1.isVisible;
    oOutputElement1.foreignKey = oElement1.foreignKey;

    oOutput.dataCategory = "dataCategory1";

    let oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    const oResult = oCsn.definitions.csnTest.elements;

    // check writeCsn
    // DW101-41633 : foreign key annotation NOT written as the association does not exist!
    assert.deepEqual(
      oResult,
      {
        Element1: {
          type: "cds.String",
          "@Semantics.amount.currencyCode": { "=": "Element3" },
          "@Aggregation.default": { "#": "defaultAggregation1" },
          "@Analytics.hidden": true,
          length: 10,
        },
        Element2: { type: "cds.String", semanticType2: true, length: 3 },
        Element3: { type: "cds.String", "@Semantics.currencyCode": true, length: 10 },
      },
      "Check writeCsn of Element"
    );

    assert.equal(
      oCsn.definitions.csnTest["@ObjectModel.modelingPattern"]["#"],
      oOutput.dataCategory,
      "Check dataCategory in output"
    );

    assert.equal(nsQueryBuilder.ViewModelToCsn.sanitizeName("a2/*b"), "a2_b", "Check sanitizeName function");

    oNewModel._dataPreviewForUnitTest = true;
    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel, { rootObject: oEntity1 });
    assert.equal(
      oCsn.definitions.csnTest.query !== undefined && oCsn.definitions.SQLView === undefined,
      true,
      "Check dataPreview"
    );
    delete oNewModel._dataPreviewForUnitTest;

    /*
    oEntity1 --> oCalc1 --> oFilter1 --> oOutput
    */
    const oCalc1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.CalculatedElements",
      {
        name: "Calc1",
      },
      oNewModel
    );
    const oFilter1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Filter",
      {
        name: "Filter1",
      },
      oNewModel
    );

    oEntity1.successorNode = oCalc1;
    oCalc1.successorNode = oFilter1;
    oFilter1.successorNode = oOutput;

    const oCalcElt11 = nsQueryBuilder.NodeImpl.createNewCalculatedElement(oCalc1);
    oCalcElt11.name = "Calc11";
    const sExpCalc = "Element1 + 2";
    oCalcElt11.expression = sExpCalc;
    const sExpFilter = "Element2 > 11";
    oFilter1.condition = sExpFilter;

    const oParsedExpFilter = {
      xpr: [
        {
          ref: ["Element2"],
        },
        ">",
        {
          val: 11,
        },
      ],
    };
    const oParsedExpCalc = {
      xpr: [
        {
          ref: ["Element1"],
        },
        "+",
        {
          val: 2,
        },
      ],
    };
    const oParsedExpCalc2 = [
      {
        ref: ["Element2"],
      },
    ];
    const oParsedExpCalc1 = {
      ref: ["Element1"],
    };

    // since for unit tests we cannot call backend service to parse
    // expressions and our aim is not to validate parser. we give the
    // expected parsed expresion also.
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oFilter1, oParsedExpFilter);
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oCalc1.elements.get(0), oParsedExpCalc1);
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oCalc1.elements.get(1), oParsedExpCalc2);
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oCalcElt11, oParsedExpCalc);

    assert.equal(
      JSON.stringify(oFilter1.parsedExpression.toCSNExp()),
      '{"xpr":[{"ref":["Entity1","Element2"]},">",{"val":11}]}',
      "Check parseAllNodeExpressions function"
    );

    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    assert.equal(
      JSON.stringify(oCsn.definitions.csnTest.query.SELECT.where),
      '[{"ref":["Entity1","Element2"]},">",{"val":11}]',
      "Check parseAllModelExpressions related functions for filter"
    );
    assert.equal(
      JSON.stringify(oCsn.definitions.csnTest.query.SELECT.columns[3]),
      '{"as":"Calc11","xpr":[{"ref":["Entity1","Element1"]},"+",{"val":2}]}',
      "Check parseAllModelExpressions related functions for calculated elements"
    );
  });

  it("Test CSN transformation with Association then update sources (from CSN) including dimensions", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel.name = "csnTest";
    oNewModel._unitTesting = true;
    const oOutput = oNewModel.output;

    const oEntity1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      {
        name: "SalesOrder",
      },
      oNewModel
    );
    const oElement1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      {
        name: "SalesID",
        dataType: "cds.integer",
      },
      oEntity1
    );
    const oElement2 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      {
        name: "ProductID",
        dataType: "cds.Integer",
      },
      oEntity1
    );

    oEntity1.successorNode = oOutput;

    const oDimensionNode1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.DimensionNode",
      {
        name: "Product_DIM",
      },
      oNewModel
    );
    const oElement21 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      {
        name: "ProductID",
        dataType: "cds.Integer",
        key: true,
      },
      oDimensionNode1
    );
    const oElement22 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      {
        name: "Name",
        dataType: "cds.String",
        length: 30,
      },
      oDimensionNode1
    );
    const oElement23 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      {
        name: "Price",
        dataType: "cds.Integer",
      },
      oDimensionNode1
    );

    assert.equal("Product_DIM", oDimensionNode1.name, "Product_DIM created");
    assert.equal("ProductID", oDimensionNode1.elements.get(0).name, "Product_DIM has element 'ProductID'");

    const oAssociation1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Association",
      {
        name: "oAssociation1",
      },
      oNewModel
    );
    oAssociation1.source = oOutput;
    oAssociation1.target = oDimensionNode1;

    oAssociation1.proposeDefaultMappings(true);
    updateAnnotationByAssociation(oAssociation1);

    const csnResult = {
      kind: "entity",
      elements: {
        SalesID: {
          type: "cds.integer",
        },
        ProductID: {
          type: "cds.Integer",
          "@ObjectModel.foreignKey.association": { "=": "oAssociation1" },
        },
        oAssociation1: {
          "@EndUserText.label": "oAssociation1",
          on: [
            {
              ref: ["ProductID"],
            },
            "=",
            {
              ref: ["oAssociation1", "ProductID"],
            },
          ],
          target: "Product_DIM",
          type: "cds.Association",
        },
      },
      query: {
        SELECT: {
          from: {
            ref: ["SalesOrder"],
          },
          columns: [
            {
              ref: ["SalesID"],
            },
            {
              ref: ["ProductID"],
            },
            {
              ref: ["oAssociation1"],
            },
          ],
          mixin: {
            oAssociation1: {
              on: [
                {
                  ref: ["$projection", "ProductID"],
                },
                "=",
                {
                  ref: ["oAssociation1", "ProductID"],
                },
              ],
              target: "Product_DIM",
              type: "cds.Association",
            },
          },
        },
      },
      "@DataWarehouse.consumption.external": true,
      "@ObjectModel.modelingPattern": {
        "#": "DATA_STRUCTURE",
      },
      "@ObjectModel.supportedCapabilities": [
        {
          "#": "DATA_STRUCTURE",
        },
      ],
      "@EndUserText.label": "View 1",
    };

    const oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    delete oCsn.definitions.csnTest[nsQueryBuilder.ViewModelToCsn.CDS_ANNOTATION_UIMODEL];
    delete oCsn.definitions.csnTest.params;
    assert.deepEqual(oCsn.definitions.csnTest, csnResult, "Check csn with association dimensions is correct");

    // Update dimension node and check elements are not duplicated! (DW101-58)
    const up2dateDimensionCsn = {
      kind: "entity",
      "@EndUserText.label": "Prodcuct DIM",
      "@Analytics.dataCategory": { "#": "DIMENSION" },
      elements: {
        ProductID: { "@EndUserText.label": "Product ID", type: "cds.Integer", key: true, notNull: true },
        Name: { "@EndUserText.label": "Product Name", type: "cds.String", length: 40 },
        Price: { "@EndUserText.label": "Unit Price", type: "cds.Integer" },
      },
    };
    nsQueryBuilder.ModelImpl.updateEntityFromCsn(oDimensionNode1, up2dateDimensionCsn, oNewModel);
    // Elelemt number is correct
    assert.equal(oDimensionNode1.elements.length, 3, "Number of dimension elements is correct");
    // Element label and data type are up to date
    assert.equal(oDimensionNode1.elements.get(1).length, 40, "Data type length is correct");
    assert.equal(oDimensionNode1.elements.get(0).label, "Product ID", "Dimension Element Label is correct");

    // Check Association is not broken
    assert.equal(oOutput.sourceAssociations.length, 1, "Association still exists");
    assert.equal(oOutput.sourceAssociations.get(0).mappings.length, 1, "Association still have mapping");
    assert.equal(
      oOutput.sourceAssociations.get(0).mappings.get(0).target === oElement21,
      true,
      "Association still have correct target"
    );

    // Update source entity and check new values..
    const up2dateEntity = {
      kind: "entity",
      "@EndUserText.label": "Sales Order",
      "@Analytics.dataCategory": { "#": "DATASET" },
      elements: {
        SalesID: { "@EndUserText.label": "Sales ID", type: "cds.Integer", key: true, notNull: true },
        ProductID: { "@EndUserText.label": "Product ID", type: "cds.Integer", notNull: true },
      },
    };
    nsQueryBuilder.ModelImpl.updateEntityFromCsn(oEntity1, up2dateEntity, oNewModel);
    // Element label is up to date
    assert.equal(oEntity1.elements.get(0).label, "Sales ID", "Source Element Label is correct");
  });

  it("LabelColumn check", async () => {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel.name = "csnTest";
    assert.notEqual(undefined, oNewModel, "New model created");
    // assert.equal(2, oNewModel.diagrams.length, "Query builder and association diagrams created");

    const oEntity1 = createEntity(oNewModel, "Entity 1", 1);

    // emulate drop entity on diagram
    onPostCreateEntity(oEntity1);

    oNewModel.output.dataCategory = "FACT";
    oNewModel.output.orderedElements[0].dataType = "cds.Integer";
    createIntermediateOperation(oNewModel, "CalculatedElements", oEntity1);
    const oCalcElts = oEntity1.successorNode;
    const newCalcElt = nsQueryBuilder.NodeImpl.createNewCalculatedElement(oCalcElts);
    newCalcElt.expression = "Element_2 + 10";
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(newCalcElt, {
      xpr: [
        {
          ref: ["Element_2"],
        },
        "+",
        {
          val: 10,
        },
      ],
    });

    newCalcElt.successorElement.semanticType = SemanticType.TEXT;
    newCalcElt.newName = "CALC1";
    oNewModel.output.elements.get(0).labelElement = newCalcElt;
    let oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    assert.equal(
      oCsn.definitions.csnTest.elements.Element_0["@ObjectModel.text.element"][0]["="],
      "CALC1",
      "@ObjectModel.text.element before rename"
    );
    newCalcElt.newName = "Renamed";
    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    assert.equal(
      oCsn.definitions.csnTest.elements.Element_0["@ObjectModel.text.element"][0]["="],
      "Renamed",
      "@ObjectModel.text.element after rename"
    );

    // Edit CSN tests.. (Handle DW101-73162: technical name is different)
    const oldName = oNewModel.output.name;
    oNewModel.output.technicalName = "newViewName";
    oNewModel.output.name = oldName;
    const csnGenerator = sap.cdw.commonmodel.ModelToCsn.getInstance();
    const csnToEdit = csnGenerator.getCsnForEntity(oNewModel.output);
    assert.strictEqual(
      csnToEdit?.newViewName?.elements?.Renamed?.type,
      "cds.Integer",
      "Calculated element uses alias in Csn to Edit"
    );

    // 2- Simulate edition and check added annotations are handled for calculated element
    const editedCsn = {
      newViewName: {
        elements: {
          Renamed: {
            "@CalculatedElement": "Calculated Element Custom Annotation",
          },
        },
      },
    };
    const erCsnToModel = new sap.cdw.ermodeler.ErCsnToModel();
    erCsnToModel.updateEntityAnnotations(oNewModel.output, editedCsn);
    assert.deepStrictEqual(
      newCalcElt.successorElement.unhandledCsn,
      { "@CalculatedElement": "Calculated Element Custom Annotation" },
      "Custom annotations handled for Calculated Element"
    );

    // Check handled annotations for views
    const expectedViewAnnotations = [
      "@Common.Label",
      "@EndUserText.label",
      "name",
      "alias",
      "doc",
      "@DataWarehouse.businessDefinition.description",
      "@DataWarehouse.businessDefinition.purpose",
      "@DataWarehouse.businessDefinition.contact",
      "@DataWarehouse.businessDefinition.responsibleTeam",
      "@DataWarehouse.businessDefinition.tags",
      "@Analytics.dataCategory",
      "@ObjectModel.modelingPattern",
      "@ObjectModel.supportedCapabilities",
      "@Hierarchy.parentChild",
      "@Hierarchy.leveled",
      "@Analytics.dbViewType",
      "params",
      "@ObjectModel.representativeKey",
      "@DataWarehouse.compoundKeySequence",
      "@cds.persistence.exists",
      "@cds.persistence.skip",
      "@DataWarehouse.sap.reserved",
      "@DataWarehouse.dataTransport",
      "@UI.hidden",
      "@Analytics.provider",
      "@DataWarehouse.consumption.external",
      "@Consumption.dbHints",
      "@DataWarehouse.dataAccessControl.usage",
      "kind",
      "elements",
      "@DataWarehouse.space.name",
      "@DataWarehouse.space.schema",
      "@DataWarehouse.space.businessName",
      "query",
      "@DataWarehouse.sqlEditor.query",
      "@DataWarehouse.tableFunction.script",
      "@DataWarehouse.querybuilder.model",
      "@DataWarehouse.enclosingObject",
      "_meta",
    ];
    const actualViewAnnotations = erCsnToModel.getHandledAnnotationsForEntity(oNewModel.output);
    assert.deepStrictEqual(
      actualViewAnnotations.root,
      expectedViewAnnotations,
      "Checking expected annotations for Views"
    );
  });

  it("(Entity1-calc),Entity2 -> Join ->Proj -> Calc -> output (JIRA3#FPA101-4782)", async () => {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    assert.notEqual(undefined, oNewModel, "New model created");
    // assert.equal(2, oNewModel.diagrams.length, "Query builder and association diagrams created");

    const oEntity1 = createEntity(oNewModel, "Entity 1", 1);
    // emulate drop entity on diagram
    onPostCreateEntity(oEntity1);
    const oEntity2 = createEntity(oNewModel, "Entity 2", 1);
    // emulate drop entity2 on entity1 on diagram
    onPostCreateEntity(oEntity2, oEntity1);
    const oJoin = oEntity1.successorNode;
    const oProj = oJoin.successorNode;

    createIntermediateOperation(oNewModel, "CalculatedElements", oEntity1);
    const calc = oEntity1.successorNode;
    createIntermediateOperation(oNewModel, "CalculatedElements", oProj);
    const calc2 = oProj.successorNode;

    calc2.elements.get(0).expression = "'Constant'";
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(calc2.elements.get(0), {
      val: "'Constant'",
    });

    const oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    assert.deepEqual(
      oCsn.definitions.View_1.query.SELECT.columns[0],
      { as: "Element_0", val: "'Constant'" },
      "Check two calcs csn generation ok"
    );
  });

  it("((Entity1 -> Agg -> Proj),Entity2 -> Union -> Agg -> Proj -> output (JIRA#DW101-33046 Order issue)", async () => {
    // testing two same configuration (model1 & model2) only source entities creation order is reversed/
    const model1 = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    const model2 = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    assert.notEqual(undefined, model1, "New model created");
    assert.notEqual(undefined, model2, "New model created");

    const createUnion = (model, inverseOrderOfCreation = false) => {
      let entityOne;
      let entityTwo;
      if (inverseOrderOfCreation) {
        entityOne = createEntity(model, "Entity 1", 2);
        nsQueryBuilder.ModelImpl.createObject(
          "sap.cdw.querybuilder.Element",
          {
            name: "Element_3",
            dataType: "cds.integer",
          },
          entityOne
        );
        onPostCreateEntity(entityOne);

        entityTwo = createEntity(model, "Entity 2", 2);
        nsQueryBuilder.ModelImpl.createObject(
          "sap.cdw.querybuilder.Element",
          {
            name: "Element_3",
            dataType: "cds.integer",
          },
          entityTwo
        );

        // emulate drop agg on calc on diagram
        onPostCreateEntity(entityTwo, entityOne, "Union");
      } else {
        entityTwo = createEntity(model, "Entity 2", 2);
        nsQueryBuilder.ModelImpl.createObject(
          "sap.cdw.querybuilder.Element",
          {
            name: "Element_3",
            dataType: "cds.integer",
          },
          entityTwo
        );
        onPostCreateEntity(entityTwo);

        entityOne = createEntity(model, "Entity 1", 2);
        nsQueryBuilder.ModelImpl.createObject(
          "sap.cdw.querybuilder.Element",
          {
            name: "Element_3",
            dataType: "cds.integer",
          },
          entityOne
        );

        // emulate drop agg on calc on diagram
        onPostCreateEntity(entityOne, entityTwo, "Union");
      }

      return {
        union: entityOne.successorNode,
        entity1: entityOne,
      };
    };

    const createAggAndProjectionNodes = (model, union, entity) => {
      createIntermediateOperation(model, "AggregatedElements", entity);
      const agg1 = entity.successorNode;
      agg1.name = "Agg1";

      createIntermediateOperation(model, "RenameElements", agg1);
      const proj1 = agg1.successorNode;
      proj1.name = "Proj1";

      createIntermediateOperation(model, "AggregatedElements", union);
      const agg2 = union.successorNode;
      agg2.name = "Agg2";

      createIntermediateOperation(model, "RenameElements", agg2);
      const proj2 = agg1.successorNode;
      proj2.name = "Proj2";
    };

    const model1UnionGraph = createUnion(model1);
    const model2UnionGraph = createUnion(model2, true);

    createAggAndProjectionNodes(model1, model1UnionGraph.union, model1UnionGraph.entity1);
    createAggAndProjectionNodes(model2, model2UnionGraph.union, model2UnionGraph.entity1);

    const csn1 = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", model1);
    const csn2 = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", model2);
    delete csn1.definitions.View_1["@DataWarehouse.querybuilder.model"];
    delete csn2.definitions.View_1["@DataWarehouse.querybuilder.model"];
    delete csn1.editorSettings;
    delete csn2.editorSettings;
    const expectedCsn = {
      definitions: {
        View_1: {
          kind: "entity",
          elements: {
            Element_0: {
              type: "cds.String",
              length: 10,
            },
            Element_1: {
              type: "cds.String",
              length: 10,
            },
            Element_3: {
              type: "cds.integer",
            },
          },
          query: {
            SELECT: {
              from: {
                SELECT: {
                  from: {
                    SET: {
                      op: "union",
                      all: true,
                      args: [
                        {
                          SELECT: {
                            from: {
                              SELECT: {
                                from: {
                                  ref: ["Entity 1"],
                                },
                                columns: [
                                  {
                                    ref: ["Entity 1", "Element_0"],
                                  },
                                  {
                                    ref: ["Entity 1", "Element_1"],
                                  },
                                  {
                                    ref: ["Entity 1", "Element_3"],
                                  },
                                ],
                              },
                              as: "Agg1",
                            },
                            columns: [
                              {
                                ref: ["Agg1", "Element_0"],
                              },
                              {
                                ref: ["Agg1", "Element_1"],
                              },
                              {
                                ref: ["Agg1", "Element_3"],
                              },
                            ],
                          },
                        },
                        {
                          SELECT: {
                            from: {
                              ref: ["Entity 2"],
                            },
                            columns: [
                              {
                                ref: ["Entity 2", "Element_0"],
                              },
                              {
                                ref: ["Entity 2", "Element_1"],
                              },
                              {
                                ref: ["Entity 2", "Element_3"],
                              },
                            ],
                          },
                        },
                      ],
                    },
                    as: "Union 1",
                  },
                  columns: [
                    {
                      ref: ["Union 1", "Element_0"],
                    },
                    {
                      ref: ["Union 1", "Element_1"],
                    },
                    {
                      ref: ["Union 1", "Element_3"],
                    },
                  ],
                },
                as: "Agg2",
              },
              columns: [
                {
                  ref: ["Agg2", "Element_0"],
                },
                {
                  ref: ["Agg2", "Element_1"],
                },
                {
                  ref: ["Agg2", "Element_3"],
                },
              ],
            },
          },
          "@EndUserText.label": "View 1",
          "@ObjectModel.modelingPattern": {
            "#": "DATA_STRUCTURE",
          },
          "@ObjectModel.supportedCapabilities": [
            {
              "#": "DATA_STRUCTURE",
            },
          ],
          "@DataWarehouse.consumption.external": true,
        },
      },
      version: {
        csn: "1.0",
      },
      meta: {
        creator: "View Editor",
        kind: "sap.dwc.viewmodel",
      },
      $version: "1.0",
    };
    assert.deepEqual(csn2, expectedCsn, "csn ok with Agg + Proj + Union");
    expect(JSON.stringify(csn1)).to.equal(JSON.stringify(csn2));
  });

  it("(Entity1,Entity2 -> Join ->Proj),Entity3 -> union -> output (JIRA#FPA101-6660)", async () => {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    assert.notEqual(undefined, oNewModel, "New model created");
    // assert.equal(2, oNewModel.diagrams.length, "Query builder and association diagrams created");

    const oEntity1 = createEntity(oNewModel, "Entity 1", 1);
    // emulate drop entity on diagram
    onPostCreateEntity(oEntity1);
    const oEntity2 = createEntity(oNewModel, "Entity 2", 1);
    // emulate drop entity2 on entity1 on diagram
    onPostCreateEntity(oEntity2, oEntity1);
    const oJoin = oEntity1.successorNode;
    const oProj = oJoin.successorNode;

    const oEntity3 = createEntity(oNewModel, "Entity 3", 1);
    // Add source unresolved association and ensure the getCSN won't be altered
    // association csn
    const csn = {
      elements: {
        _toNothing: {
          type: "cds.Association",
          target: "Nothing",
          on: [
            {
              ref: ["Nothing", "noId"],
            },
            "=",
            {
              ref: ["Element_0"],
            },
          ],
        },
      },
    };
    loadUnresolvedAssociations(oEntity3, csn);
    // Ensure source association correctly created (before comparing CSN later)
    assert.deepStrictEqual(
      oEntity3.unresolvedAssociations[0].joinSourceElementNames[0],
      "Element_0",
      "Source association loaded"
    );

    // emulate drop entity3 on proj on diagram
    onPostCreateEntity(oEntity3, oProj, "Union");

    const oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    assert.deepEqual(
      oCsn.definitions.View_1.query,
      {
        SET: {
          op: "union",
          all: true,
          args: [
            { SELECT: { from: { ref: ["Entity 3"] }, columns: [{ ref: ["Entity 3", "Element_0"] }] } },
            {
              SELECT: {
                from: {
                  join: "inner",
                  args: [{ ref: ["Entity 1"] }, { ref: ["Entity 2"] }],
                  on: [{ ref: ["Entity 1", "Element_0"] }, "=", { ref: ["Entity 2", "Element_0"] }],
                },
                columns: [{ ref: ["Entity 1", "Element_0"] }],
              },
            },
          ],
        },
      },
      "Check two calcs csn generation ok"
    );

    // DW101-11602 Replace source should clear old unresolved associations
    const replaceCsn = {
      elements: {
        _toSomething: {
          type: "cds.Association",
          target: "Something",
          on: [
            {
              ref: ["Something", "noId"],
            },
            "=",
            {
              ref: ["Element_0"],
            },
          ],
        },
      },
    };
    loadUnresolvedAssociations(oEntity3, replaceCsn, true);
    // Ensure Only ONE unreolved asociation in the model
    assert.deepStrictEqual(
      oNewModel.unresolvedAssociations.length,
      1,
      "Unresolved associations updated in case of replacement"
    );
  });

  it("(Entity1->CalculatedElements),Entity2 -> Union -> output", async () => {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel.name = "csnTest";
    assert.notEqual(undefined, oNewModel, "New model created");
    // assert.equal(2, oNewModel.diagrams.length, "Query builder and association diagrams created");

    const oEntity1 = createEntity(oNewModel, "Entity 1", 3);
    // emulate drop entity on diagram
    onPostCreateEntity(oEntity1);
    const oEntity2 = createEntity(oNewModel, "Entity 2", 3);

    // emulate drop entity2 on entity1 on diagram
    onPostCreateEntity(oEntity2, oEntity1, "Union");
    const union = oEntity1.successorNode;

    createIntermediateOperation(oNewModel, "CalculatedElements", oEntity1);
    // map enitity1 to the union
    nsQueryBuilder.NodeImpl.copyElementsForSetNode(union, oEntity2);
    let oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    delete oCsn.definitions.csnTest[nsQueryBuilder.ViewModelToCsn.CDS_ANNOTATION_UIMODEL];
    delete oCsn.definitions.csnTest.params;
    delete oCsn.editorSettings;
    assert.deepEqual(
      oCsn,
      {
        definitions: {
          csnTest: {
            kind: "entity",
            elements: {
              Element_0: { type: "cds.String", length: 10 },
              Element_1: { type: "cds.String", length: 10 },
              Element_2: { type: "cds.String", length: 10 },
            },
            query: {
              SET: {
                op: "union",
                all: true,
                args: [
                  {
                    SELECT: {
                      from: { ref: ["Entity 1"] },
                      columns: [
                        { ref: ["Entity 1", "Element_0"] },
                        { ref: ["Entity 1", "Element_1"] },
                        { ref: ["Entity 1", "Element_2"] },
                      ],
                    },
                  },
                  {
                    SELECT: {
                      from: { ref: ["Entity 2"] },
                      columns: [
                        { ref: ["Entity 2", "Element_0"] },
                        { ref: ["Entity 2", "Element_1"] },
                        { ref: ["Entity 2", "Element_2"] },
                      ],
                    },
                  },
                ],
              },
            },
            "@EndUserText.label": "View 1",
            "@DataWarehouse.consumption.external": true,
            "@ObjectModel.modelingPattern": { "#": "DATA_STRUCTURE" },
            "@ObjectModel.supportedCapabilities": [{ "#": "DATA_STRUCTURE" }],
          },
        },
        version: { csn: "1.0" },
        meta: { creator: "View Editor", kind: "sap.dwc.viewmodel" },
        $version: "1.0",
      },
      "Check calc before union ok"
    );

    const calc = oEntity1.successorNode;
    // check val constant as calc
    // since for unit tests we cannot call backend service to parse
    // expressions and our aim is not to validate parser. we give the
    // expected parsed expresion also.
    calc.elements.get(0).expression = "'Constant'";
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(calc.elements.get(0), {
      val: "'Constant'",
    });
    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    delete oCsn.definitions.csnTest[nsQueryBuilder.ViewModelToCsn.CDS_ANNOTATION_UIMODEL];
    delete oCsn.definitions.csnTest.params;
    delete oCsn.editorSettings;
    assert.deepEqual(
      oCsn,
      {
        definitions: {
          csnTest: {
            kind: "entity",
            elements: {
              Element_0: { type: "cds.String", length: 10 },
              Element_1: { type: "cds.String", length: 10 },
              Element_2: { type: "cds.String", length: 10 },
            },
            query: {
              SET: {
                op: "union",
                all: true,
                args: [
                  {
                    SELECT: {
                      from: { ref: ["Entity 1"] },
                      columns: [
                        { val: "'Constant'", as: "Element_0" },
                        { ref: ["Entity 1", "Element_1"] },
                        { ref: ["Entity 1", "Element_2"] },
                      ],
                    },
                  },
                  {
                    SELECT: {
                      from: { ref: ["Entity 2"] },
                      columns: [
                        { ref: ["Entity 2", "Element_0"] },
                        { ref: ["Entity 2", "Element_1"] },
                        { ref: ["Entity 2", "Element_2"] },
                      ],
                    },
                  },
                ],
              },
            },
            "@EndUserText.label": "View 1",
            "@DataWarehouse.consumption.external": true,
            "@ObjectModel.modelingPattern": { "#": "DATA_STRUCTURE" },
            "@ObjectModel.supportedCapabilities": [{ "#": "DATA_STRUCTURE" }],
          },
        },
        version: { csn: "1.0" },
        meta: { creator: "View Editor", kind: "sap.dwc.viewmodel" },
        $version: "1.0",
      },
      "Check calc before union with constant ok"
    );

    sap.cdw.querybuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Calculated_1", isCalculated: true, expression: "Element_0", dataType: "cds.String", length: 10 },
      calc
    );
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(calc.elements.get(3), {
      ref: ["Element_0"],
    });
    nsQueryBuilder.NodeImpl.copyElementsForSetNode(union, calc, true);

    sap.cdw.querybuilder.NodeImpl.changeElementIndexOrder(oNewModel.output, oNewModel.output.elements.get(0), 0, 2);

    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    delete oCsn.definitions.csnTest[nsQueryBuilder.ViewModelToCsn.CDS_ANNOTATION_UIMODEL];
    delete oCsn.definitions.csnTest.params;
    delete oCsn.editorSettings;
    assert.deepEqual(
      oCsn,
      {
        definitions: {
          csnTest: {
            kind: "entity",
            elements: {
              Element_1: { type: "cds.String", length: 10 },
              Element_2: { type: "cds.String", length: 10 },
              Element_0: { type: "cds.String", length: 10 },
              Calculated_1: { type: "cds.String", length: 10 },
            },
            query: {
              SET: {
                op: "union",
                all: true,
                args: [
                  {
                    SELECT: {
                      from: { ref: ["Entity 1"] },
                      columns: [
                        { ref: ["Entity 1", "Element_1"] },
                        { ref: ["Entity 1", "Element_2"] },
                        { val: "'Constant'", as: "Element_0" },
                        { ref: ["Entity 1", "Element_0"], as: "Calculated_1" },
                      ],
                    },
                  },
                  {
                    SELECT: {
                      from: { ref: ["Entity 2"] },
                      columns: [
                        { val: null, as: "Element_1" },
                        { val: null, as: "Element_2" },
                        { val: null, as: "Element_0" },
                        { val: null, as: "Calculated_1" },
                      ],
                    },
                  },
                ],
              },
            },
            "@EndUserText.label": "View 1",
            "@DataWarehouse.consumption.external": true,
            "@ObjectModel.modelingPattern": { "#": "DATA_STRUCTURE" },
            "@ObjectModel.supportedCapabilities": [{ "#": "DATA_STRUCTURE" }],
          },
        },
        version: { csn: "1.0" },
        meta: { creator: "View Editor", kind: "sap.dwc.viewmodel" },
        $version: "1.0",
      },
      "Check simple new calc before union ok"
    );
  });

  it("(Entity1->CalculatedElements),Entity2 -> Join -> output", async () => {
    sap.cdw.querybuilder.ModelImpl.HANDLE_JOIN_DUPLICATE = true;
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel.name = "csnTest";
    assert.notEqual(undefined, oNewModel, "New model created");
    // assert.equal(2, oNewModel.diagrams.length, "Query builder and association diagrams created");

    const oEntity1 = createEntity(oNewModel, "Entity 1", 3);
    // emulate drop entity on diagram
    onPostCreateEntity(oEntity1);
    const oEntity2 = createEntity(oNewModel, "Entity 2", 3);

    // emulate drop entity2 on entity1 on diagram
    onPostCreateEntity(oEntity2, oEntity1, "Join");
    const join = oEntity1.successorNode;
    let oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel, {
      rootObject: join,
      dataPreview: true,
    });
    assert.deepEqual(
      oCsn.definitions["Join 1"],
      {
        kind: "entity",
        elements: {
          "1-Element_0": {
            type: "cds.String",
            length: 10,
          },
          "2-Element_0": {
            type: "cds.String",
            length: 10,
          },
          "1-Element_1": {
            type: "cds.String",
            length: 10,
          },
          "2-Element_1": {
            type: "cds.String",
            length: 10,
          },
          "1-Element_2": {
            type: "cds.String",
            length: 10,
          },
          "2-Element_2": {
            type: "cds.String",
            length: 10,
          },
        },
        query: {
          SELECT: {
            from: {
              join: "inner",
              args: [
                {
                  ref: ["Entity 1"],
                },
                {
                  ref: ["Entity 2"],
                },
              ],
              on: [
                {
                  ref: ["Entity 1", "Element_0"],
                },
                "=",
                {
                  ref: ["Entity 2", "Element_0"],
                },
              ],
            },
            columns: [
              {
                ref: ["Entity 1", "Element_0"],
                as: "1-Element_0",
              },
              {
                ref: ["Entity 2", "Element_0"],
                as: "2-Element_0",
              },
              {
                ref: ["Entity 1", "Element_1"],
                as: "1-Element_1",
              },
              {
                ref: ["Entity 2", "Element_1"],
                as: "2-Element_1",
              },
              {
                ref: ["Entity 1", "Element_2"],
                as: "1-Element_2",
              },
              {
                ref: ["Entity 2", "Element_2"],
                as: "2-Element_2",
              },
            ],
          },
        },
      },
      "Check join with duplicate elements ok"
    );

    let calc;
    oNewModel.resource.applyUndoableAction(function () {
      createIntermediateOperation(oNewModel, "CalculatedElements", join);
      calc = join.successorNode;
      calc.name = "Calc1";
    });

    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel, { rootObject: calc, dataPreview: true });
    assert.deepEqual(
      oCsn.definitions.Calc1,
      {
        kind: "entity",
        elements: {
          "1-Element_0": {
            type: "cds.String",
            length: 10,
          },
          "2-Element_0": {
            type: "cds.String",
            length: 10,
          },
          "1-Element_1": {
            type: "cds.String",
            length: 10,
          },
          "2-Element_1": {
            type: "cds.String",
            length: 10,
          },
          "1-Element_2": {
            type: "cds.String",
            length: 10,
          },
          "2-Element_2": {
            type: "cds.String",
            length: 10,
          },
        },
        query: {
          SELECT: {
            from: {
              join: "inner",
              args: [
                {
                  ref: ["Entity 1"],
                },
                {
                  ref: ["Entity 2"],
                },
              ],
              on: [
                {
                  ref: ["Entity 1", "Element_0"],
                },
                "=",
                {
                  ref: ["Entity 2", "Element_0"],
                },
              ],
            },
            columns: [
              {
                ref: ["Entity 1", "Element_0"],
                as: "1-Element_0",
              },
              {
                ref: ["Entity 2", "Element_0"],
                as: "2-Element_0",
              },
              {
                ref: ["Entity 1", "Element_1"],
                as: "1-Element_1",
              },
              {
                ref: ["Entity 2", "Element_1"],
                as: "2-Element_1",
              },
              {
                ref: ["Entity 1", "Element_2"],
                as: "1-Element_2",
              },
              {
                ref: ["Entity 2", "Element_2"],
                as: "2-Element_2",
              },
            ],
          },
        },
      },
      "Check calc after join with duplicate elements ok"
    );

    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel, {
      rootObject: oNewModel.output,
      dataPreview: false,
    });
    assert.deepEqual(
      oCsn.definitions.csnTest,
      {
        kind: "entity",
        elements: {
          Element_0: {
            type: "cds.String",
            length: 10,
          },
          Element_1: {
            type: "cds.String",
            length: 10,
          },
          Element_2: {
            type: "cds.String",
            length: 10,
          },
        },
        query: {
          SELECT: {
            from: {
              join: "inner",
              args: [
                {
                  ref: ["Entity 1"],
                },
                {
                  ref: ["Entity 2"],
                },
              ],
              on: [
                {
                  ref: ["Entity 1", "Element_0"],
                },
                "=",
                {
                  ref: ["Entity 2", "Element_0"],
                },
              ],
            },
            columns: [
              {
                as: "Element_0",
                ref: ["Entity 1", "Element_0"],
              },
              {
                as: "Element_1",
                ref: ["Entity 1", "Element_1"],
              },
              {
                as: "Element_2",
                ref: ["Entity 1", "Element_2"],
              },
            ],
          },
        },
        "@EndUserText.label": "View 1",
        "@DataWarehouse.consumption.external": true,
        "@ObjectModel.modelingPattern": {
          "#": "DATA_STRUCTURE",
        },
        "@ObjectModel.supportedCapabilities": [
          {
            "#": "DATA_STRUCTURE",
          },
        ],
      },
      "Check coutput with duplicate elements ok"
    );
    calc.resource.undo();

    createIntermediateOperation(oNewModel, "CalculatedElements", oEntity1);
    const oCalc1 = oEntity1.successorNode;
    oCalc1.name = "Calc1";
    // map enitity1 to the union
    // nsQueryBuilder.NodeImpl.copyElementsForSetNode(union, oEntity2);

    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    delete oCsn.definitions.csnTest[nsQueryBuilder.ViewModelToCsn.CDS_ANNOTATION_UIMODEL];
    delete oCsn.definitions.csnTest.params;
    delete oCsn.editorSettings;
    assert.deepEqual(
      oCsn,
      {
        definitions: {
          csnTest: {
            kind: "entity",
            elements: {
              Element_0: { type: "cds.String", length: 10 },
              Element_1: { type: "cds.String", length: 10 },
              Element_2: { type: "cds.String", length: 10 },
            },
            query: {
              SELECT: {
                from: {
                  join: "inner",
                  args: [
                    {
                      SELECT: {
                        from: { ref: ["Entity 1"] },
                        columns: [
                          { as: "Element_0", ref: ["Entity 1", "Element_0"] },
                          { as: "Element_1", ref: ["Entity 1", "Element_1"] },
                          { as: "Element_2", ref: ["Entity 1", "Element_2"] },
                        ],
                      },
                      as: "Calc1",
                    },
                    { ref: ["Entity 2"] },
                  ],
                  on: [{ ref: ["Calc1", "Element_0"] }, "=", { ref: ["Entity 2", "Element_0"] }],
                },
                columns: [
                  {
                    as: "Element_0",
                    ref: ["Calc1", "Element_0"],
                  },
                  {
                    as: "Element_1",
                    ref: ["Calc1", "Element_1"],
                  },
                  {
                    as: "Element_2",
                    ref: ["Calc1", "Element_2"],
                  },
                ],
              },
            },
            "@DataWarehouse.consumption.external": true,
            "@ObjectModel.modelingPattern": {
              "#": "DATA_STRUCTURE",
            },
            "@ObjectModel.supportedCapabilities": [
              {
                "#": "DATA_STRUCTURE",
              },
            ],
            "@EndUserText.label": "View 1",
          },
        },
        version: { csn: "1.0" },
        meta: { creator: "View Editor", kind: "sap.dwc.viewmodel" },
        $version: "1.0",
      },
      "Check calc before join ok"
    );

    calc = oEntity1.successorNode;
    // check val constant as calc
    // since for unit tests we cannot call backend service to parse
    // expressions and our aim is not to validate parser. we give the
    // expected parsed expresion also.
    calc.elements.get(0).expression = "'Constant'";
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(calc.elements.get(0), {
      val: "'Constant'",
    });
    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    delete oCsn.definitions.csnTest[nsQueryBuilder.ViewModelToCsn.CDS_ANNOTATION_UIMODEL];
    delete oCsn.definitions.csnTest.params;
    delete oCsn.editorSettings;
    // DW101-18511: Using subqueries should prevent (re)using expressions, even for constants!
    assert.deepEqual(
      oCsn,
      {
        definitions: {
          csnTest: {
            kind: "entity",
            elements: {
              Element_0: { type: "cds.String", length: 10 },
              Element_1: { type: "cds.String", length: 10 },
              Element_2: { type: "cds.String", length: 10 },
            },
            query: {
              SELECT: {
                from: {
                  join: "inner",
                  args: [
                    {
                      SELECT: {
                        from: { ref: ["Entity 1"] },
                        columns: [
                          { as: "Element_0", val: "'Constant'" },
                          { as: "Element_1", ref: ["Entity 1", "Element_1"] },
                          { as: "Element_2", ref: ["Entity 1", "Element_2"] },
                        ],
                      },
                      as: "Calc1",
                    },
                    { ref: ["Entity 2"] },
                  ],
                  on: [{ ref: ["Calc1", "Element_0"] }, "=", { ref: ["Entity 2", "Element_0"] }],
                },
                columns: [
                  {
                    as: "Element_0",
                    ref: ["Calc1", "Element_0"],
                  },
                  {
                    as: "Element_1",
                    ref: ["Calc1", "Element_1"],
                  },
                  {
                    as: "Element_2",
                    ref: ["Calc1", "Element_2"],
                  },
                ],
              },
            },
            "@DataWarehouse.consumption.external": true,
            "@ObjectModel.modelingPattern": {
              "#": "DATA_STRUCTURE",
            },
            "@ObjectModel.supportedCapabilities": [
              {
                "#": "DATA_STRUCTURE",
              },
            ],
            "@EndUserText.label": "View 1",
          },
        },
        version: { csn: "1.0" },
        meta: { creator: "View Editor", kind: "sap.dwc.viewmodel" },
        $version: "1.0",
      },
      "Check calc before join with constant ok"
    );
  });

  it("(Entity1->Entity2 -> Join(Cross) -> output", async () => {
    sap.cdw.querybuilder.ModelImpl.HANDLE_JOIN_DUPLICATE = true;
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel.name = "csnTest";
    assert.notEqual(undefined, oNewModel, "New model created");
    // assert.equal(2, oNewModel.diagrams.length, "Query builder and association diagrams created");

    const oEntity1 = createEntity(oNewModel, "Entity 1", 3);
    // emulate drop entity on diagram
    onPostCreateEntity(oEntity1);
    const oEntity2 = createEntity(oNewModel, "Entity 2", 3);

    // emulate drop entity2 on entity1 on diagram
    onPostCreateEntity(oEntity2, oEntity1, "Join");
    const join = oEntity1.successorNode;
    join.type = "cross";

    join.validate();
    assert.equal(
      join.aggregatedValidations && join.aggregatedValidations.validations.length,
      1,
      "One validation warning for cross join"
    );
    assert.equal(
      join.aggregatedValidations && join.aggregatedValidations.validations[0].message.id,
      "VAL_JOIN_CROSS_MAPPED",
      "One validation warning for cross join"
    );

    const oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    delete oCsn.definitions.csnTest[nsQueryBuilder.ViewModelToCsn.CDS_ANNOTATION_UIMODEL];
    delete oCsn.definitions.csnTest.params;
    delete oCsn.editorSettings;
    assert.deepEqual(
      oCsn,
      {
        definitions: {
          csnTest: {
            kind: "entity",
            elements: {
              Element_0: { type: "cds.String", length: 10 },
              Element_1: { type: "cds.String", length: 10 },
              Element_2: { type: "cds.String", length: 10 },
            },
            query: {
              SELECT: {
                from: {
                  join: "cross",
                  args: [{ ref: ["Entity 1"] }, { ref: ["Entity 2"] }],
                },
                columns: [
                  { ref: ["Entity 1", "Element_0"] },
                  { ref: ["Entity 1", "Element_1"] },
                  { ref: ["Entity 1", "Element_2"] },
                ],
              },
            },
            "@DataWarehouse.consumption.external": true,
            "@ObjectModel.modelingPattern": {
              "#": "DATA_STRUCTURE",
            },
            "@ObjectModel.supportedCapabilities": [
              {
                "#": "DATA_STRUCTURE",
              },
            ],
            "@EndUserText.label": "View 1",
          },
        },
        version: { csn: "1.0" },
        meta: { creator: "View Editor", kind: "sap.dwc.viewmodel" },
        $version: "1.0",
      },
      "Check CROSS join ok"
    );
  });

  it("(Entity1->CalculatedElements->output (test aggregate function)", async () => {
    Functions.prepareAllFunctions();
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel.name = "csnTest";
    assert.notEqual(undefined, oNewModel, "New model created");
    // assert.equal(2, oNewModel.diagrams.length, "Query builder and association diagrams created");

    const oEntity1 = createEntity(oNewModel, "Entity 1", 3);
    // emulate drop entity on diagram
    onPostCreateEntity(oEntity1);

    createIntermediateOperation(oNewModel, "CalculatedElements", oEntity1);

    let oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    delete oCsn.definitions.csnTest[nsQueryBuilder.ViewModelToCsn.CDS_ANNOTATION_UIMODEL];
    delete oCsn.definitions.csnTest.params;
    delete oCsn.editorSettings;
    assert.deepEqual(
      oCsn,
      {
        definitions: {
          csnTest: {
            kind: "entity",
            elements: {
              Element_0: { type: "cds.String", length: 10 },
              Element_1: { type: "cds.String", length: 10 },
              Element_2: { type: "cds.String", length: 10 },
            },
            query: {
              SELECT: {
                from: { ref: ["Entity 1"] },
                columns: [
                  { as: "Element_0", ref: ["Entity 1", "Element_0"] },
                  { as: "Element_1", ref: ["Entity 1", "Element_1"] },
                  { as: "Element_2", ref: ["Entity 1", "Element_2"] },
                ],
              },
            },
            "@DataWarehouse.consumption.external": true,
            "@ObjectModel.modelingPattern": { "#": "DATA_STRUCTURE" },
            "@ObjectModel.supportedCapabilities": [{ "#": "DATA_STRUCTURE" }],
            "@EndUserText.label": "View 1",
          },
        },
        version: { csn: "1.0" },
        meta: { creator: "View Editor", kind: "sap.dwc.viewmodel" },
        $version: "1.0",
      },
      "Without agg ok"
    );

    const calc = oEntity1.successorNode;

    // DW101-923: Set ST_POINT and ensure expression is OK
    const ce = calc.elements.get(0);
    ce.dataType = "ST_POINT";
    ce.longitudeElement = calc.elements.get(1);
    ce.latitudeElement = calc.elements.get(2);
    assert.strictEqual(
      ce.expression,
      "ST_GeomFromText('POINT(' || \"Element_1\" || ' ' || \"Element_2\" || ')', 4326)",
      "Geometry Expression is OK"
    );

    // check aggregate function
    // since for unit tests we cannot call backend service to parse
    // expressions and our aim is not to validate parser. we give the
    // expected parsed expresion also.
    ce.dataType = "cds.String";
    ce.length = 10;
    ce.expression = "max(Element_1)";
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(calc.elements.get(0), {
      func: "max",
      args: [
        {
          ref: ["Element_1"],
        },
      ],
    });
    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    delete oCsn.definitions.csnTest[nsQueryBuilder.ViewModelToCsn.CDS_ANNOTATION_UIMODEL];
    delete oCsn.definitions.csnTest.params;
    delete oCsn.editorSettings;
    assert.deepEqual(
      oCsn,
      {
        definitions: {
          csnTest: {
            kind: "entity",
            elements: {
              Element_0: { type: "cds.String", length: 10 },
              Element_1: { type: "cds.String", length: 10 },
              Element_2: { type: "cds.String", length: 10 },
            },
            query: {
              SELECT: {
                from: { ref: ["Entity 1"] },
                columns: [
                  { as: "Element_0", func: "max", args: [{ ref: ["Entity 1", "Element_1"] }] },
                  { as: "Element_1", ref: ["Entity 1", "Element_1"] },
                  { as: "Element_2", ref: ["Entity 1", "Element_2"] },
                ],
                groupBy: [{ ref: ["Entity 1", "Element_1"] }, { ref: ["Entity 1", "Element_2"] }],
              },
            },
            "@DataWarehouse.consumption.external": true,
            "@ObjectModel.modelingPattern": { "#": "DATA_STRUCTURE" },
            "@ObjectModel.supportedCapabilities": [{ "#": "DATA_STRUCTURE" }],
            "@EndUserText.label": "View 1",
          },
        },
        version: { csn: "1.0" },
        meta: { creator: "View Editor", kind: "sap.dwc.viewmodel" },
        $version: "1.0",
      },
      "Check with aggregate ok"
    );

    const oClass = sap.galilei.model.getClass("sap.cdw.querybuilder.Element");
    const oCalcElement1 = oClass.create(oNewModel.resource, {
      name: "CalculatedElement1",
      newName: "CalculatedElement1",
      isCalculated: true,
    });
    calc.elements.push(oCalcElement1);
    oCalcElement1.length = 10;
    oCalcElement1.expression = "Element_1";
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oCalcElement1, {
      ref: ["Element_1"],
    });
    const oCalcElement2 = oClass.create(oNewModel.resource, {
      name: "CalculatedElement2",
      newName: "CalculatedElement2",
      isCalculated: true,
    });
    calc.elements.push(oCalcElement2);
    oCalcElement2.length = 10;
    const oCalcElement3 = oClass.create(oNewModel.resource, {
      name: "CalculatedElement3",
      newName: "CalculatedElement3",
      isCalculated: true,
    });
    calc.elements.push(oCalcElement3);
    oCalcElement3.length = 10;

    oCalcElement2.expression = "CalculatedElement3";
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oCalcElement2, {
      ref: ["CalculatedElement3"],
    });
    oCalcElement3.expression = "CalculatedElement2";
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oCalcElement3, {
      ref: ["CalculatedElement2"],
    });

    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    assert.deepEqual(
      oCsn.definitions.csnTest.query.SELECT.columns,
      [
        { as: "Element_0", func: "max", args: [{ ref: ["Entity 1", "Element_1"] }] },
        { as: "Element_1", ref: ["Entity 1", "Element_1"] },
        { as: "Element_2", ref: ["Entity 1", "Element_2"] },
        { as: "CalculatedElement1", ref: ["Entity 1", "Element_1"] },
        { as: "CalculatedElement2", ref: ["CalculatedElement3"] },
        { as: "CalculatedElement3", ref: ["CalculatedElement2"] },
      ],
      "Circular references do not block csn generation"
    );
  });

  it("should transform the label column to csn", async () => {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel.name = "csnTest";
    const oEntity1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      {
        name: "Entity1",
      },
      oNewModel
    );
    oEntity1.successorNode = oNewModel.output;

    const elt1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      {
        name: "Element_1",
        dataType: "cds.integer",
      },
      oEntity1
    );

    const elt2 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      {
        name: "Element_2",
        dataType: "cds.String",
        length: 100,
        semanticType: SemanticType.TEXT,
      },
      oEntity1
    );

    elt1.successorElement.labelElement = elt2;

    const oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);

    assert.deepEqual(
      oCsn.definitions.csnTest.elements,
      {
        Element_1: { type: "cds.integer", "@ObjectModel.text.element": [{ "=": "Element_2" }] },
        Element_2: { type: "cds.String", length: 100, "@Semantics.text": true },
      },
      "Check label column"
    );
  });

  it("JIRA#DW101-92 E2E: Issue in the deployment of View with Shared artefacts", async () => {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel.name = "csnTest";
    const oEntity1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      {
        name: "Entity1",
      },
      oNewModel
    );
    oEntity1.successorNode = oNewModel.output;
    oNewModel.output.dataCategory = "FACT";

    const elt1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      {
        name: "Element_1",
        dataType: "cds.integer",
        semanticType: SemanticType.UNIT_OF_MEASURE,
      },
      oEntity1
    );

    const elt2 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      {
        name: "Element_2",
        dataType: "cds.String",
        length: 100,
        semanticType: SemanticType.QUANTITY_WITH_UNIT,
      },
      oEntity1
    );

    elt2.successorElement.isMeasure = true;
    elt2.successorElement.unitTypeElement = elt1.successorElement;

    let oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);

    assert.deepEqual(
      oCsn.definitions.csnTest.elements,
      {
        Element_1: {
          type: "cds.integer",
          "@Semantics.unitOfMeasure": true,
        },
        Element_2: {
          type: "cds.String",
          length: 100,
          "@AnalyticsDetails.measureType": { "#": "BASE" },
          "@Semantics.quantity.unitOfMeasure": {
            "=": "Element_1",
          },
        },
      },
      "Check label column"
    );

    elt2.successorElement.unitTypeElement = undefined;
    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);

    assert.deepEqual(
      oCsn.definitions.csnTest.elements,
      {
        Element_1: {
          type: "cds.integer",
          "@Semantics.unitOfMeasure": true,
        },
        Element_2: {
          type: "cds.String",
          length: 100,
          "@AnalyticsDetails.measureType": { "#": "BASE" },
        },
      },
      "Check label column"
    );
  });

  it("Test quoteName()", function () {
    let quotedName;
    const quotedNames = {
      Discount: "Discount",
      '"Customer-ID"': '!["Customer-ID"]',
      "@PostalCode": '"@PostalCode"',
      "Product-Base-Margin": '"Product-Base-Margin"',
      "Product Sub-Category": '"Product Sub-Category"',
      "Postal:Code": '"Postal:Code"',
      "Postal/Code": '"Postal/Code"',
      "Postal+Code": '"Postal+Code"',
      Postal$Code: '"Postal$Code"',
      "Postal%Code": '"Postal%Code"',
    };

    for (const name in quotedNames) {
      quotedName = nsQueryBuilder.ViewModelToCsn.quoteName(name);
      assert.equal(quotedName, quotedNames[name], `Quoted name of ${name} is ${quotedNames[name]}`);
    }
  });

  it("useOLAPDBHintWriteCsn()", function () {
    let oCsn = {};
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel.name = "csnTest";
    assert.notEqual(undefined, oNewModel, "New model created");

    const oEntity = createEntity(oNewModel, "Entity 1", 3);
    // emulate drop entity on diagram
    onPostCreateEntity(oEntity);
    oEntity.isUseOLAPDBHint = true;
    oEntity.isAllowConsumption = true;

    csnUtils.useOLAPDBHintWriteCsn(oEntity, oCsn);
    assert.equal(oCsn["@Consumption.dbHints"][0], "USE_OLAP_PLAN", "useOLAPDBHintWriteCsn() Positive is ok");

    oCsn = {};
    oEntity.isUseOLAPDBHint = false;
    csnUtils.useOLAPDBHintWriteCsn(oEntity, oCsn);
    assert.equal(oCsn["@Consumption.dbHints"], undefined, "useOLAPDBHintWriteCsn() Negative is ok");

    oCsn = {};
    oEntity.isUseOLAPDBHint = true;
    oEntity.isAllowConsumption = false;
    csnUtils.useOLAPDBHintWriteCsn(oEntity, oCsn);
    assert.equal(oCsn["@Consumption.dbHints"], undefined, "useOLAPDBHintWriteCsn() Negative II is ok");
  });

  it("useOLAPDBHintReadCsn()", function () {
    const oCsn = {};
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel.name = "csnTest";
    assert.notEqual(undefined, oNewModel, "New model created");

    const oEntity = createEntity(oNewModel, "Entity 1", 3);
    // emulate drop entity on diagram
    onPostCreateEntity(oEntity);

    csnUtils.useOLAPDBHintReadCsn(oEntity, oCsn);
    assert.equal(oEntity.isUseOLAPDBHint, false, "useOLAPDBHintReadCsn() Negative is ok");

    oCsn["@Consumption.dbHints"] = ["USE_OLAP_PLAN"];
    csnUtils.useOLAPDBHintReadCsn(oEntity, oCsn);
    assert.equal(oEntity.isUseOLAPDBHint, true, "useOLAPDBHintReadCsn() Positive is ok");
  });

  it("FPA101-10520: previewSQL - csn extension should be undefined", async function () {
    let oCsn = {} as any;
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel.name = "csnTest";
    assert.notEqual(undefined, oNewModel, "New model created");

    const oEntity = createEntity(oNewModel, "Entity 1", 3);
    // emulate drop entity on diagram
    onPostCreateEntity(oEntity);
    // Deployed version reverted saved changes
    oNewModel.revertLastVersion = true;
    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel, { previewSQL: true });
    assert.equal(oCsn.extensions, undefined, "extensions undefined is ok");
    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel, { previewSQL: false });
    assert.notEqual(oCsn.extensions, undefined, "extenstions present is ok");
  });
  it("Test DataCategory annotation change", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel._unitTesting = true;
    const oOutput = oNewModel.output;

    const oldDimensionCsn = { "@Analytics.dataCategory": { "#": "DIMENSION" } };
    const newDimensionCsn = {
      "@ObjectModel.modelingPattern": { "#": "ANALYTICAL_DIMENSION" },
      "@ObjectModel.supportedCapabilities": [{ "#": "ANALYTICAL_DIMENSION" }],
    };
    const oldDataSetCsn = { "@Analytics.dataCategory": { "#": "DataSet" } };
    const newDataSetCsn = {
      "@ObjectModel.modelingPattern": { "#": "DATA_STRUCTURE" },
      "@ObjectModel.supportedCapabilities": [{ "#": "DATA_STRUCTURE" }],
    };
    const oldFactCsn = { "@Analytics.dataCategory": { "#": "FACT" } };
    const newFactCsn = {
      "@ObjectModel.modelingPattern": { "#": "ANALYTICAL_FACT" },
      "@ObjectModel.supportedCapabilities": [{ "#": "ANALYTICAL_FACT" }],
    };
    const mixedFactCsn = { ...oldFactCsn, ...newFactCsn };
    const remoteDataSetCsn = {
      "@ObjectModel.modelingPattern": { "#": "DATA_STRUCTURE" },
      "@ObjectModel.supportedCapabilities": [{ "#": "DATA_STRUCTURE" }, { "#": "SQL_DATA_SOURCE" }],
    };

    oOutput.dataCategory = DataCategory.DATASET;
    const oldCsn = oldFactCsn;
    // Read old value...
    csnUtils.dataCategoryReadCsn(oOutput, oldCsn);
    assert.equal(oOutput.dataCategory, DataCategory.FACT, "Old annotation read with set FF");
    const newCsn = newDimensionCsn;
    // Read new value...
    csnUtils.dataCategoryReadCsn(oOutput, newCsn);
    assert.equal(oOutput.dataCategory, DataCategory.DIMENSION, "New annotation read with set FF");
    // Write new value
    delete newCsn["@ObjectModel.modelingPattern"];
    delete newCsn["@ObjectModel.supportedCapabilities"];
    oOutput.dataCategory = DataCategory.DATASET;
    csnUtils.dataCategoryWriteCsn(oOutput, newCsn);
    assert.deepEqual(newCsn, newDataSetCsn, "New Data Category annotation written with set FF");
    // Ensure old Value is erased!
    oOutput.dataCategory = DataCategory.FACT;
    csnUtils.dataCategoryWriteCsn(oOutput, mixedFactCsn);
    assert.deepStrictEqual(mixedFactCsn, newFactCsn, "Only new annotation is kept with set FF");

    // Remote source with SQL_DATA_SOURCE capability
    csnUtils.dataCategoryReadCsn(oOutput, remoteDataSetCsn);
    assert.deepEqual(oOutput.hasSqlDataSourceCapability, true, "Check SQL_DATA_SOURCE capability is set");
    const newRemoteDataSetCsn = {};
    csnUtils.dataCategoryWriteCsn(oOutput, newRemoteDataSetCsn);
    assert.deepStrictEqual(newRemoteDataSetCsn, remoteDataSetCsn, "Check SQL_DATA_SOURCE capability is generated");
  });

  it("Analytic Measure Formula changes", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel._unitTesting = true;
    const oOutput = oNewModel.output;
    const oEntity1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      { name: "Entity1" },
      oNewModel
    );
    const oElement1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "ProductId", dataType: "cds.String", length: 60 },
      oEntity1
    );
    const oElement2 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Quantity", dataType: "cds.Integer", length: 10 },
      oEntity1
    );
    const oElement3 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Price", dataType: "cds.Decimal", precision: 5, scale: 2 },
      oEntity1
    );

    oEntity1.successorNode = oOutput;
    oOutput.dataCategory = DataCategory.FACT;

    SupportedFeaturesService.getInstance().isAnalyticMeasureFormulaEnabled = () => true;
    SupportedFeaturesService.getInstance().isDataActivationEnabled = () => true;

    oOutput.elements.get(1).isMeasure = true;
    oOutput.elements.get(2).isMeasure = true;
    const oFormulaElement = nsQueryBuilder.NodeImpl.createNewFormulaElement(oOutput);
    const oCsnElement = {};
    csnUtils.isMeasureWriteCsn(oFormulaElement, oCsnElement);
    oFormulaElement.dataType = "cds.Integer";
    assert.deepEqual(
      oCsnElement,
      {
        "@AnalyticsDetails.measureType": { "#": "CALCULATION" },
      },
      "isMeasureWriteCsn() is ok"
    );

    await oOutput.validate();
    const aErrors = [];
    oNewModel.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.equal(
      aErrors.toString(),
      "VAL_ADS_DEPRECATED_WARN,VAL_FORMULA_EMPTY_EX",
      "2 validation error for empty expression"
    );
    const sXpr = "Quantity * Price";
    const oXpr = { xpr: [{ ref: ["Quantity"] }, "*", { ref: ["Price"] }] } as any;
    oFormulaElement.expression = sXpr;
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oFormulaElement, oXpr);
    assert.equal("cds.Decimal", oFormulaElement.dataType, "formula element data type, ok");

    await oOutput.validate();
    assert.equal(
      oNewModel.aggregatedValidations && oNewModel.aggregatedValidations.validations.length,
      1,
      "1 Validation error"
    );

    oOutput.elements.get(2).isMeasure = false;
    await oOutput.validate();
    assert.equal(
      oOutput.aggregatedValidations && oOutput.aggregatedValidations.validations.length,
      1,
      "1 validation error for Non Measure in expression"
    );
    // assert.equal(oOutput.aggregatedValidations && oOutput.aggregatedValidations.validations[0].message.id, "VAL_FORMULA_INVALID_REF", "Only measures are allowed in the expression");

    oOutput.elements.get(2).isMeasure = true;
    await oOutput.validate();

    const oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    assert.deepEqual(
      oCsn.definitions.View_1.elements[oFormulaElement.newName]["@Aggregation.default"],
      { "#": "FORMULA" },
      "Aggregation default is ok"
    );
    assert.deepEqual(
      oCsn.definitions.View_1.elements[oFormulaElement.newName]["@AnalyticsDetails.measureType"],
      { "#": "CALCULATION" },
      "Aggregation measureType is ok"
    );
    assert.deepEqual(
      oCsn.definitions.View_1.query,
      {
        SELECT: {
          from: { ref: ["Entity1"] },
          columns: [
            { ref: ["ProductId"] },
            { ref: ["Quantity"] },
            { ref: ["Price"] },
            { as: "Formula", xpr: [{ ref: ["Entity1", "Quantity"] }, "*", { ref: ["Entity1", "Price"] }] },
          ],
        },
      },
      "CSN is ok"
    );

    // Create Restricted Measure, set its base measure to formula..
    const oRestrictedMeasure = nsQueryBuilder.NodeImpl.createRestrictedMeasure(oOutput);
    oRestrictedMeasure.sourceMeasure = oFormulaElement;
    // .. and check default aggregation is not set to FORMULA!
    assert.deepStrictEqual(
      oRestrictedMeasure.defaultAggregation,
      "SUM",
      "Correct defaultAggregation for restricted measure based on Formula"
    );
  });

  it("Analytic Measure Formula changes - Calculated Columns", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel._unitTesting = true;

    SupportedFeaturesService.getInstance().isAnalyticMeasureFormulaEnabled = () => true;
    SupportedFeaturesService.getInstance().isDataActivationEnabled = () => true;
    const oOutput = oNewModel.output;
    const oEntity1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      { name: "Entity1" },
      oNewModel
    );
    const oElement1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "ProductId", dataType: "cds.String", length: 60 },
      oEntity1
    );
    const oElement2 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Quantity", dataType: "cds.Integer", length: 10 },
      oEntity1
    );
    const oElement3 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Price", dataType: "cds.Decimal", precision: 5, scale: 2 },
      oEntity1
    );
    oEntity1.successorNode = oOutput;
    createIntermediateOperation(oNewModel, "CalculatedElements", oEntity1);
    const oCalc1 = oEntity1.successorNode;
    const oCalcElt1 = nsQueryBuilder.NodeImpl.createNewCalculatedElement(oCalc1);
    oCalcElt1.name = "TotalPriceCalc";
    const oCalcElt2 = nsQueryBuilder.NodeImpl.createNewCalculatedElement(oCalc1);
    oCalcElt2.name = "SalesCalc";

    const oCalcElt3 = nsQueryBuilder.NodeImpl.createNewCalculatedElement(oCalc1);
    oCalcElt3.name = "SalesSum";

    let sXpr = "Quantity * Price";
    let oXpr = { xpr: [{ ref: ["Quantity"] }, "*", { ref: ["Price"] }] } as any;
    oCalcElt1.expression = sXpr;
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oCalcElt1, oXpr);

    sXpr = "Price * 1.03";
    oXpr = { xpr: [{ ref: ["Quantity"] }, "*", { val: 1.35 }] } as any;
    oCalcElt1.expression = sXpr;
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oCalcElt2, oXpr);

    sXpr = "SUM(Price)";
    oXpr = { func: "SUM", args: [{ ref: ["Price"] }] };
    oCalcElt3.expression = sXpr;
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oCalcElt3, oXpr);

    oOutput.elements.get(3).isMeasure = true;
    oOutput.elements.get(4).isMeasure = true;
    oOutput.elements.get(5).isMeasure = true;

    const oFormulaElement = nsQueryBuilder.NodeImpl.createNewFormulaElement(oOutput);
    const oCsnElement = {};
    csnUtils.isMeasureWriteCsn(oFormulaElement, oCsnElement);
    oFormulaElement.dataType = "cds.Integer";
    assert.deepEqual(
      oCsnElement,
      {
        "@AnalyticsDetails.measureType": { "#": "CALCULATION" },
      },
      "isMeasureWriteCsn() is ok"
    );

    oNewModel.validate();
    sXpr = "TotalPriceCalc / SalesSum";
    oXpr = { xpr: [{ ref: ["TotalPriceCalc"] }, "/", { ref: ["SalesSum"] }] } as any;
    oFormulaElement.expression = sXpr;
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oFormulaElement, oXpr);

    const oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    assert.deepEqual(
      oCsn.definitions.View_1.elements[oFormulaElement.newName]["@Aggregation.default"],
      { "#": "FORMULA" },
      "Aggregation default is ok"
    );
    assert.deepEqual(
      oCsn.definitions.View_1.elements[oFormulaElement.newName]["@AnalyticsDetails.measureType"],
      { "#": "CALCULATION" },
      "Aggregation measureType is ok"
    );
    // Group By clause change, analytic measures will not be added in group by
    assert.deepEqual(
      oCsn.definitions.View_1.query,
      {
        SELECT: {
          from: { ref: ["Entity1"] },
          columns: [
            { as: "ProductId", ref: ["Entity1", "ProductId"] },
            { as: "Quantity", ref: ["Entity1", "Quantity"] },
            { as: "Price", ref: ["Entity1", "Price"] },
            { as: "TotalPriceCalc", ref: ["TotalPriceCalc"] },
            { as: "SalesCalc", xpr: [{ ref: ["Entity1", "Quantity"] }, "*", { val: 1.35 }] },
            { as: "SalesSum", func: "SUM", args: [{ ref: ["Entity1", "Price"] }] },
            {
              as: "Formula",
              xpr: [{ ref: ["$projection", "TotalPriceCalc"] }, "/", { ref: ["$projection", "SalesSum"] }],
            },
          ],
          groupBy: [
            { ref: ["Entity1", "ProductId"] },
            { ref: ["Entity1", "Quantity"] },
            { ref: ["Entity1", "Price"] },
            { ref: ["TotalPriceCalc"] },
            { xpr: [{ ref: ["Entity1", "Quantity"] }, "*", { val: 1.35 }] },
          ],
        },
      },
      "CSN is ok"
    );
  });

  it("Restricted Measure changes", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel._unitTesting = true;
    const oOutput = oNewModel.output;
    const oOutputNode = oOutput as sap.cdw.querybuilder.Output;
    const oProduct = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      { name: "Product" },
      oNewModel
    );
    const oProductId = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "ProductId", dataType: "cds.String", length: 16 },
      oProduct
    );
    const oQuantity = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Quantity", dataType: "cds.Integer", length: 10 },
      oProduct
    );
    const oPrice = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Price", dataType: "cds.Decimal", precision: 5, scale: 2 },
      oProduct
    );

    oProduct.successorNode = oOutput;
    oOutput.dataCategory = DataCategory.FACT;

    SupportedFeaturesService.getInstance().isAnalyticMeasureFormulaEnabled = () => true;
    SupportedFeaturesService.getInstance().isDataActivationEnabled = () => true;
    // Set Quantity to measure
    oQuantity.successorElement.isMeasure = true;
    // Create Restricted Measure and set its base measure
    const oRestrictedMeasure = nsQueryBuilder.NodeImpl.createRestrictedMeasure(oOutput);
    // Check basic CSN generation
    const oCsnElement = {};
    csnUtils.isMeasureWriteCsn(oRestrictedMeasure, oCsnElement);
    assert.deepStrictEqual(
      oCsnElement,
      {
        "@AnalyticsDetails.measureType": { "#": "RESTRICTION" },
      },
      "isMeasureWriteCsn() is ok for Restricted Measure"
    );

    // Set basic measure, expression and write csn
    oRestrictedMeasure.sourceMeasure = oQuantity.successorElement;
    oRestrictedMeasure.defaultAggregation = "AVG";
    assert.deepStrictEqual(oRestrictedMeasure.dataType, "cds.Integer", "Data type ok for restricted measure");
    oRestrictedMeasure.expression = "ProductId > 1000";
    let srcParsedExpression = { xpr: [{ ref: ["ProductId"] }, ">", { val: 1000 }] };
    let newParsedExpression: any = {
      xpr: [
        "case",
        "when",
        { position: 2, ref: ["ProductId"] },
        ">",
        { val: 1000 },
        "then",
        { position: 6, ref: ["Quantity"] },
        "end",
      ],
    };
    // update expression references before compare
    newParsedExpression.xpr[2].refElement = oProductId.successorElement;
    newParsedExpression.xpr[6].refElement = oQuantity.successorElement;

    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oRestrictedMeasure, srcParsedExpression);
    assert.deepStrictEqual(
      oRestrictedMeasure.parsedExpression.expression,
      newParsedExpression,
      "Parsed expression updated for restricted measure"
    );

    // Write Csn and check element and query column are OK!
    const expectedQuery = {
      SELECT: {
        from: { ref: ["Product"] },
        columns: [
          { ref: ["ProductId"] },
          { ref: ["Quantity"] },
          { ref: ["Price"] },
          {
            as: "RestrictedMeasure",
            xpr: [
              "case",
              "when",
              { ref: ["Product", "ProductId"] },
              ">",
              { val: 1000 },
              "then",
              { ref: ["Product", "Quantity"] },
              "end",
            ],
          },
        ],
      },
    };

    const oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    assert.deepStrictEqual(
      oCsn.definitions.View_1.elements[oRestrictedMeasure.newName]["@Aggregation.default"],
      { "#": "AVG" },
      "Restriced Measure Aggregation is ok"
    );
    assert.deepStrictEqual(
      oCsn.definitions.View_1.elements[oRestrictedMeasure.newName]["@AnalyticsDetails.measureType"],
      { "#": "RESTRICTION" },
      "Restriced Measure Type is ok"
    );
    assert.deepStrictEqual(oCsn.definitions.View_1.query, expectedQuery, "CSN is ok");

    // DW101-5089 Change source measure and check no error
    const oDiscount = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Discount", dataType: "cds.Decimal", precision: 5, scale: 2 },
      oProduct
    );
    oDiscount.successorElement.isMeasure = true;
    oRestrictedMeasure.sourceMeasure = oDiscount.successorElement;
    oDiscount.deleteObject();
    oRestrictedMeasure.sourceMeasure = oQuantity.successorElement;
    await oOutput.validate();
    // Validations...
    let validations;
    validations = oOutput.aggregatedValidations?.validations;
    let aErrors = [];
    oOutput.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.strictEqual(
      aErrors.toString(),
      "VAL_ADS_DEPRECATED_WARN",
      "1 validation error after changing deleted source measure of restricted measure"
    );

    // DW101-18830 Only one reference allowed in a restricted measure expression
    oRestrictedMeasure.expression = "ProductId > Price";
    srcParsedExpression = { xpr: [{ ref: ["ProductId"] }, ">", { ref: ["Price"] }] };
    newParsedExpression = {
      xpr: [
        "case",
        "when",
        { position: 2, ref: ["ProductId"] },
        ">",
        { position: 4, ref: ["Price"] },
        "then",
        { position: 6, ref: ["Quantity"] },
        "end",
      ],
    };
    // update expression references before compare
    newParsedExpression.xpr[2].refElement = oProductId.successorElement;
    newParsedExpression.xpr[4].refElement = oPrice.successorElement;
    newParsedExpression.xpr[6].refElement = oQuantity.successorElement;

    // Parse..
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oRestrictedMeasure, srcParsedExpression);
    assert.deepStrictEqual(
      oRestrictedMeasure.parsedExpression.expression,
      newParsedExpression,
      "Parsed expression, with two references, updated for restricted measure"
    );

    // Validate
    await oOutput.validate();
    validations = oOutput.aggregatedValidations?.validations;
    aErrors = [];
    oOutput.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.strictEqual(
      aErrors.toString(),
      "VAL_ADS_DEPRECATED_WARN,VAL_EXCEEDING_RESTRICTED_MEASURE_ATTRIBUTE",
      "Using more than one attribute reference"
    );

    // Use Analytic Parameter and write Csn
    // Create analytic parameter and set value helper
    const oClass = sap.galilei.model.getClass("sap.cdw.commonmodel.AnalyticParameter");
    // const oClass = sap.galilei.model.getClass("sap.cdw.commonmodel.Parameter");
    const analyticParameter = oClass.create(oOutput.resource, { name: "P1" });

    // const analyticParameter = nsQueryBuilder.ModelImpl.createObject("sap.cdw.commonmodel.AnalyticParameter", { name: "P1" });
    if (analyticParameter) {
      oOutput.analyticParameters.push(analyticParameter);
    }
    sap.cdw.commonmodel.ObjectImpl.setAnalyticParameterReferenceElementFromName(analyticParameter, "ProductId");
    oRestrictedMeasure.expression = "ProductId > :P1";
    const srcParsedExpressionWithParam = { xpr: [{ ref: ["ProductId"] }, ">", { ref: ["P1"], param: true }] };
    const newParsedExpressionWithParam: any = {
      xpr: [
        "case",
        "when",
        { position: 2, ref: ["ProductId"] },
        ">",
        { position: 4, ref: ["P1"], param: true },
        "then",
        { position: 6, ref: ["Quantity"] },
        "end",
      ],
    };
    // update expression references before compare
    newParsedExpressionWithParam.xpr[2].refElement = oProductId.successorElement;
    newParsedExpressionWithParam.xpr[4].refElement = analyticParameter;
    newParsedExpressionWithParam.xpr[6].refElement = oQuantity.successorElement;

    // Check expressions to parse
    const aExpressionsToParse = oOutputNode?.getAllExpressionsToParse();
    assert.strictEqual(aExpressionsToParse?.length, 1, "One expression to parse for output node");

    // Parse and check..
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(
      oRestrictedMeasure,
      srcParsedExpressionWithParam
    );
    assert.deepStrictEqual(
      oRestrictedMeasure.parsedExpression.expression,
      newParsedExpressionWithParam,
      "Parsed expression with Input Parameter updated for restricted measure"
    );

    // Write Csn and check element and query column are OK!
    const expectedQueryWithParam = {
      SELECT: {
        from: { ref: ["Product"] },
        columns: [
          { ref: ["ProductId"] },
          { ref: ["Quantity"] },
          { ref: ["Price"] },
          {
            as: "RestrictedMeasure",
            xpr: [
              "case",
              "when",
              { ref: ["Product", "ProductId"] },
              ">",
              { ref: ["P1"], param: true },
              "then",
              { ref: ["Product", "Quantity"] },
              "end",
            ],
          },
        ],
      },
    };
    const expectedParams = {
      P1: {
        "@AnalyticsDetails.variable.multipleSelections": true,
        "@AnalyticsDetails.variable.referenceElement": { "=": "ProductId" },
        "@AnalyticsDetails.variable.selectionType": { "#": "SINGLE" },
        "@AnalyticsDetails.variable.usageType": { "#": "FILTER" },
        "@EndUserText.label": "P1",
        type: "cds.String",
        length: 16,
      },
    };

    const oCsnWithParam = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    assert.deepStrictEqual(
      oCsnWithParam.definitions.View_1.query,
      expectedQueryWithParam,
      "CSN with analytical parameter is ok"
    );
    assert.deepStrictEqual(oCsnWithParam.definitions.View_1.params, expectedParams, "Analytical parameters CSN ok");
    //
    // Empty source..
    oRestrictedMeasure.sourceMeasure = undefined;
    await oOutput.validate();
    validations = oOutput.aggregatedValidations?.validations;
    aErrors = [];
    oOutput.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.strictEqual(
      aErrors.toString(),
      "VAL_ADS_DEPRECATED_WARN,VAL_RESTRICT_MEASURE_EMPTY_SOURCE",
      "Missing Restricted Measure source"
    );
    // Invalid source..
    oRestrictedMeasure.sourceMeasure = oProductId.successorElement;
    await oOutput.validate();
    validations = oOutput.aggregatedValidations?.validations;
    aErrors = [];
    oOutput.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.strictEqual(
      aErrors.toString(),
      "VAL_ADS_DEPRECATED_WARN,VAL_RESTRICT_MEASURE_INVALID_SOURCE",
      "Invalid Restricted Measure source"
    );
    // Empty expression..
    oRestrictedMeasure.sourceMeasure = oQuantity.successorElement;
    oRestrictedMeasure.expression = undefined;
    await oOutput.validate();
    validations = oOutput.aggregatedValidations?.validations;
    assert.strictEqual(
      validations?.length,
      3,
      "Two validation errors for missing Restricted Measure expression and non consumed parameter"
    );
    assert.strictEqual(validations[0]?.message?.id, "VAL_ADS_DEPRECATED_WARN", "new fact warning");
    assert.strictEqual(validations[1]?.message?.id, "VAL_ANALYTIC_PARAMETER_UNCONSUMED", "Unconsumed Parameter");
    assert.strictEqual(
      validations[2]?.message?.id,
      "VAL_RESTRICT_MEASURE_EMPTY_EXPR",
      "Missing Restricted Measure expression"
    );
    // Invalid expression having no filterd attribute
    oRestrictedMeasure.expression = ":P1 < 1000";
    const srcParsedExpressionWithoutAttribute: any = { xpr: [{ ref: ["P1"], param: true }, "<", { val: 1000 }] };
    const newParsedExpressionWithoutAttribute: any = {
      xpr: [
        "case",
        "when",
        { position: 2, ref: ["P1"], param: true },
        "<",
        { val: 1000 },
        "then",
        { position: 6, ref: ["Quantity"] },
        "end",
      ],
    };
    // update expression references before compare
    newParsedExpressionWithoutAttribute.xpr[2].refElement = analyticParameter;
    newParsedExpressionWithoutAttribute.xpr[6].refElement = oQuantity.successorElement;

    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(
      oRestrictedMeasure,
      srcParsedExpressionWithoutAttribute
    );
    assert.deepStrictEqual(
      oRestrictedMeasure.parsedExpression.expression,
      newParsedExpressionWithoutAttribute,
      "Parsed expression with missing filtered attribute for restricted measure"
    );

    await oOutput.validate();
    validations = oOutput.aggregatedValidations?.validations;
    aErrors = [];
    oOutput.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.strictEqual(
      aErrors.toString(),
      "VAL_ADS_DEPRECATED_WARN,VAL_MISSING_RESTRICTED_MEASURE_ATTRIBUTE",
      "Missing filtered attribute in Restricted measure expression"
    );

    // Invalid function used in restriction measure formula
    oRestrictedMeasure.expression = "CONTAINS(ProductId, :P1)";
    const srcParsedExpressionWithContainsPredicate: any = {
      xpr: [{ func: "CONTAINS", args: [{ ref: ["ProductId"] }, { ref: ["P1"], param: true }] }],
    };
    const newParsedExpressionWithConainsPredicate: any = {
      xpr: [
        "case",
        "when",
        {
          position: 2,
          func: "CONTAINS",
          args: [
            { position: 3, ref: ["ProductId"] },
            { position: 4, ref: ["P1"], param: true },
          ],
        },
        "then",
        { position: 7, ref: ["Quantity"] },
        "end",
      ],
    };
    // update expression references before compare
    newParsedExpressionWithConainsPredicate.xpr[2].args[0].refElement = oProductId.successorElement;
    newParsedExpressionWithConainsPredicate.xpr[2].args[1].refElement = analyticParameter;
    newParsedExpressionWithConainsPredicate.xpr[4].refElement = oQuantity.successorElement;

    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(
      oRestrictedMeasure,
      srcParsedExpressionWithContainsPredicate
    );
    assert.deepStrictEqual(
      oRestrictedMeasure.parsedExpression.expression,
      newParsedExpressionWithConainsPredicate,
      "Parsed expression with CONTAINS predicate updated for restricted measure"
    );

    await oOutput.validate();
    validations = oOutput.aggregatedValidations?.validations;
    aErrors = [];
    oOutput.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.strictEqual(
      aErrors.toString(),
      "VAL_ADS_DEPRECATED_WARN,VAL_FUNCTION_RESTRICTED_MEASURE_ONLY_LOCATE",
      "Only locate function is allowd for restricted measure formula"
    );

    // Invalid function used in restriction measure formula
    oRestrictedMeasure.expression = "LOCATE(ProductId, :P1)";
    const srcParsedExpressionWithLocateFunction: any = {
      xpr: [{ func: "LOCATE", args: [{ ref: ["ProductId"] }, { ref: ["P1"], param: true }] }],
    };
    const newParsedExpressionWithLocateFunction: any = {
      xpr: [
        "case",
        "when",
        {
          position: 2,
          func: "LOCATE",
          args: [
            { position: 3, ref: ["ProductId"] },
            { position: 4, ref: ["P1"], param: true },
          ],
        },
        "then",
        { position: 7, ref: ["Quantity"] },
        "end",
      ],
    };
    // update expression references before compare
    newParsedExpressionWithLocateFunction.xpr[2].args[0].refElement = oProductId.successorElement;
    newParsedExpressionWithLocateFunction.xpr[2].args[1].refElement = analyticParameter;
    newParsedExpressionWithLocateFunction.xpr[4].refElement = oQuantity.successorElement;

    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(
      oRestrictedMeasure,
      srcParsedExpressionWithLocateFunction
    );
    assert.deepStrictEqual(
      oRestrictedMeasure.parsedExpression.expression,
      newParsedExpressionWithLocateFunction,
      "Parsed expression with LOCATE function updated for restricted measure"
    );

    await oOutput.validate();
    validations = oOutput.aggregatedValidations?.validations;
    aErrors = [];
    oOutput.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.strictEqual(
      aErrors.toString(),
      "VAL_ADS_DEPRECATED_WARN",
      "No validation error for LOCATE function in Restricted Measure expression"
    );

    // Invalid usage of IN predicate
    oRestrictedMeasure.expression = "ProductId IN :P1";
    const srcParsedExpressionWithWrongInPredicate: any = {
      xpr: [{ ref: ["ProductId"] }, "in", { ref: ["P1"], param: true }],
    };
    const newParsedExpressionWithWrongInPredicate: any = {
      xpr: [
        "case",
        "when",
        { position: 2, ref: ["ProductId"] },
        "in",
        { position: 4, ref: ["P1"], param: true },
        "then",
        { position: 6, ref: ["Quantity"] },
        "end",
      ],
    };
    // update expression references before compare
    newParsedExpressionWithWrongInPredicate.xpr[2].refElement = oProductId.successorElement;
    newParsedExpressionWithWrongInPredicate.xpr[4].refElement = analyticParameter;
    newParsedExpressionWithWrongInPredicate.xpr[6].refElement = oQuantity.successorElement;

    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(
      oRestrictedMeasure,
      srcParsedExpressionWithWrongInPredicate
    );
    assert.deepStrictEqual(
      oRestrictedMeasure.parsedExpression.expression,
      newParsedExpressionWithWrongInPredicate,
      "Parsed expression with wrong IN predicate updated for restricted measure"
    );

    await oOutput.validate();
    validations = oOutput.aggregatedValidations?.validations;
    aErrors = [];
    oOutput.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.strictEqual(
      aErrors.toString(),
      "VAL_ADS_DEPRECATED_WARN",
      "No validation error for wrong IN predicate in Restricted Measure expression"
    );

    // Valid usage of IN predicate after a function
    oRestrictedMeasure.expression = "LOCATE(ProductId,'X') > 1 AND Price IN (:P1)";
    const srcParsedExpressionWithValidInPredicate: any = {
      xpr: [
        { func: "LOCATE", args: [{ ref: ["ProductId"] }, { val: "X" }] },
        ">",
        { val: 1 },
        "and",
        { ref: ["Price"] },
        "in",
        "(",
        { ref: ["P1"], param: true },
        ")",
      ],
    };
    const newParsedExpressionWithValidInPredicate: any = {
      xpr: [
        "case",
        "when",
        { position: 2, func: "LOCATE", args: [{ position: 3, ref: ["ProductId"] }, { val: "X" }] },
        ">",
        { val: 1 },
        "and",
        { position: 9, ref: ["Price"] },
        "in",
        "(",
        { position: 12, ref: ["P1"], param: true },
        ")",
        "then",
        { position: 15, ref: ["Quantity"] },
        "end",
      ],
    };
    // update expression references before compare
    newParsedExpressionWithValidInPredicate.xpr[2].args[0].refElement = oProductId.successorElement;
    newParsedExpressionWithValidInPredicate.xpr[6].refElement = oPrice.successorElement;
    newParsedExpressionWithValidInPredicate.xpr[9].refElement = analyticParameter;
    newParsedExpressionWithValidInPredicate.xpr[12].refElement = oQuantity.successorElement;

    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(
      oRestrictedMeasure,
      srcParsedExpressionWithValidInPredicate
    );
    assert.deepStrictEqual(
      oRestrictedMeasure.parsedExpression.expression,
      newParsedExpressionWithValidInPredicate,
      "Parsed expression with valid IN predicate updated for restricted measure"
    );

    await oOutput.validate();
    validations = oOutput.aggregatedValidations?.validations;
    aErrors = [];
    oOutput.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.strictEqual(
      aErrors.toString(),
      "VAL_ADS_DEPRECATED_WARN",
      "No validation error for valid IN predicate in Restricted Measure expression"
    );

    // Invalid combination of parameter and BETWEEN predicate
    oRestrictedMeasure.expression = "ProductId BETWEEN :P1 and 1000";
    let srcParsedExpressionWithParamAndBetween: any = {
      xpr: [{ ref: ["ProductId"] }, "between", { val: 1000 }, "and", { ref: ["P1"], param: true }],
    };
    let newParsedExpressionWithParamAndBetween: any = {
      xpr: [
        "case",
        "when",
        { position: 2, ref: ["ProductId"] },
        "between",
        { val: 1000 },
        "and",
        { position: 6, ref: ["P1"], param: true },
        "then",
        { position: 8, ref: ["Quantity"] },
        "end",
      ],
    };
    // update expression references before compare
    newParsedExpressionWithParamAndBetween.xpr[2].refElement = oProductId.successorElement;
    newParsedExpressionWithParamAndBetween.xpr[6].refElement = analyticParameter;
    newParsedExpressionWithParamAndBetween.xpr[8].refElement = oQuantity.successorElement;

    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(
      oRestrictedMeasure,
      srcParsedExpressionWithParamAndBetween
    );
    assert.deepStrictEqual(
      oRestrictedMeasure.parsedExpression.expression,
      newParsedExpressionWithParamAndBetween,
      "Parsed expression with wrong combination of param and BETWEEN updated for restricted measure"
    );

    await oOutput.validate();
    validations = oOutput.aggregatedValidations?.validations;
    aErrors = [];
    oOutput.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.strictEqual(
      aErrors.toString(),
      "VAL_ADS_DEPRECATED_WARN,VAL_ANALYTIC_PARAM_BETWEEN",
      "Parameter not allowed as right operand of BETWEEN predicate in Restricted measure expression"
    );

    // DW101-5896: Use function parameter name syntax and check parsed expression evaluation..
    oRestrictedMeasure.expression = "LOCATE(str=>ProductId, chr=>'X') > 1";
    const srcParsedExpressionWithArgumentNames: any = {
      xpr: [{ func: "LOCATE", args: { str: { ref: ["ProductId"] }, chr: { val: "X" } } }, ">", { val: 1 }],
    };
    const newParsedExpressionWithArgumentNames: any = {
      xpr: [
        "case",
        "when",
        { position: 2, func: "LOCATE", args: { str: { position: 3, ref: ["ProductId"] }, chr: { val: "X" } } },
        ">",
        { val: 1 },
        "then",
        { position: 9, ref: ["Quantity"] },
        "end",
      ],
    };
    // update expression references before compare
    newParsedExpressionWithArgumentNames.xpr[2].args.str.refElement = oProductId.successorElement;
    newParsedExpressionWithArgumentNames.xpr[6].refElement = oQuantity.successorElement;

    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(
      oRestrictedMeasure,
      srcParsedExpressionWithArgumentNames
    );
    assert.deepStrictEqual(
      oRestrictedMeasure.parsedExpression.expression,
      newParsedExpressionWithArgumentNames,
      "Parsed expression with named arguments"
    );

    // Fix to use valid combination of parameter and BETWEEN predicate: Still invalid - waiting for SAC fix
    oRestrictedMeasure.expression = "ProductId BETWEEN :P1 and 1000";
    srcParsedExpressionWithParamAndBetween = {
      xpr: [{ ref: ["ProductId"] }, "between", { ref: ["P1"], param: true }, "and", { val: 1000 }],
    };
    newParsedExpressionWithParamAndBetween = {
      xpr: [
        "case",
        "when",
        { position: 2, ref: ["ProductId"] },
        "between",
        { position: 4, ref: ["P1"], param: true },
        "and",
        { val: 1000 },
        "then",
        { position: 8, ref: ["Quantity"] },
        "end",
      ],
    };
    // update expression references before compare
    newParsedExpressionWithParamAndBetween.xpr[2].refElement = oProductId.successorElement;
    newParsedExpressionWithParamAndBetween.xpr[4].refElement = analyticParameter;
    newParsedExpressionWithParamAndBetween.xpr[8].refElement = oQuantity.successorElement;

    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(
      oRestrictedMeasure,
      srcParsedExpressionWithParamAndBetween
    );
    assert.deepStrictEqual(
      oRestrictedMeasure.parsedExpression.expression,
      newParsedExpressionWithParamAndBetween,
      "Parsed expression with correct combination of param and BETWEEN updated for restricted measure"
    );
    // Check Restricted measure data type not changed to Integer64!
    assert.deepStrictEqual(
      oRestrictedMeasure.dataType,
      "cds.Integer",
      "Restricted Measure data type not changed by inferred type"
    );

    await oOutput.validate();
    validations = oOutput.aggregatedValidations?.validations;
    aErrors = [];
    oOutput.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.strictEqual(
      aErrors.toString(),
      "VAL_ADS_DEPRECATED_WARN,VAL_ANALYTIC_PARAM_BETWEEN",
      "Still validation error for correct combination of Parameter and BETWEEN predicate in Restricted Measure expression"
    );

    // add parameter to output node and ensure no additional error
    const oParam = {
      name: "ViewParam",
      label: "View Parameter ",
      dataType: "cds.String",
      length: 10,
    };
    const oViewParameterClass = sap.galilei.model.getClass("sap.cdw.querybuilder.ViewParameter");
    // const oClass = sap.galilei.model.getClass("sap.cdw.commonmodel.Parameter");
    const newParameter = oViewParameterClass.create(oOutput.resource, oParam);
    oOutput.parameters.push(newParameter);
    await oOutput.validate();
    validations = oOutput.aggregatedValidations?.validations;
    // Still one validation error after combining analytic measures with input parameters..
    aErrors = [];
    oOutput.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.strictEqual(
      aErrors.toString(),
      "VAL_ADS_DEPRECATED_WARN,VAL_ANALYTIC_PARAM_BETWEEN",
      "Analytic measures allowed with input parameters"
    );

    // DW101-24198: Ensure Value Help CSN read is correct when FF enabled
    if (SupportedFeaturesService.getInstance().isParameterValueHelpEnabled()) {
      const valueHelpCsn = {
        "@Consumption.valueHelpDefinition": [
          {
            entity: {
              name: "Value_Help_Example",
              element: "Region",
            },
          },
        ],
      };
      csnUtils.parameterValueHelpReadCsn(newParameter, valueHelpCsn);
      assert.strictEqual(
        newParameter.valueHelpDefinition.get(0).entityName,
        "Value_Help_Example",
        "Entity name is read from parameter value help csn"
      );
      assert.strictEqual(
        newParameter.valueHelpDefinition.get(0).elementName,
        "Region",
        "Element name is read from parameter value help csn"
      );
    }

    // Check read CSN: change pamater name and value helpattribute
    oOutput.analyticParameters.clear();
    const newParamsCsn = { params: { P2: { ...expectedParams.P1 } } };
    newParamsCsn.params.P2["@EndUserText.label"] = "New label";
    newParamsCsn.params.P2["@AnalyticsDetails.variable.referenceElement"]["="] = "Price";
    csnUtils.analyticParametersReadCsn(oOutput, newParamsCsn);
    assert.strictEqual(oOutput.analyticParameters.length, 1, "Analytic Parameters csn read ok");
    // Check the new value help attribute is correct
    assert.strictEqual(
      oOutput.analyticParameters.get(0).valueHelpAttribute === oPrice.successorElement,
      true,
      "Analytic Parameter Value help attribute correctly set from CSN"
    );

    // Edit CSN tests...
    // DW101-73267: Ensure input parameters handled if technical name is changed
    const oldName = oOutput.name;
    oOutput.technicalName = "viewTechName";
    oOutput.name = oldName;
    // 1- Get csn to edit, update and check analytic measure and analytic parameters edited annotations..
    const csnGenerator = sap.cdw.commonmodel.ModelToCsn.getInstance();
    const csnToEdit = csnGenerator.getCsnForEntity(oOutput);
    assert.strictEqual(
      csnToEdit?.viewTechName?.elements?.RestrictedMeasure?.type,
      "cds.Integer",
      "Restricted Measure is present in Csn to Edit"
    );
    assert.strictEqual(
      csnToEdit?.viewTechName?.params?.ViewParam?.type,
      "cds.String",
      "Parameters handled in simplified CSN"
    );

    // 2- Simulate edition and check added annotations are handled for input parameter, analytic measure and analytic parameter
    const editedCsn = {
      viewTechName: {
        elements: {
          RestrictedMeasure: {
            "@RestrictedMeasure": "Restricted Measure Custom Annotation",
          },
        },
        params: {
          ViewParam: {
            "@InputParameter": "Input Parameter Custom Annotation",
            "@Semantics.businessDate.at": true,
          },
          P2: {
            "@AnalyticParameter": "Analytic Parameter Custom Annotation",
          },
        },
      },
    };
    const erCsnToModel = new sap.cdw.ermodeler.ErCsnToModel();
    erCsnToModel.updateEntityAnnotations(oOutput, editedCsn);
    assert.deepStrictEqual(
      oOutput.parameters.get(0).semanticType,
      "@Semantics.businessDate.at",
      "Semantic type handled for Input parameter"
    );
    assert.deepStrictEqual(
      oOutput.parameters.get(0).unhandledCsn,
      { "@InputParameter": "Input Parameter Custom Annotation", "@Semantics.businessDate.at": true },
      "Custom annotations handled for input parameter"
    );
    assert.deepStrictEqual(
      oRestrictedMeasure.unhandledCsn,
      { "@RestrictedMeasure": "Restricted Measure Custom Annotation" },
      "Custom annotations handled for Analytic measure"
    );
    assert.deepStrictEqual(
      oOutput.analyticParameters.get(0).unhandledCsn,
      { "@AnalyticParameter": "Analytic Parameter Custom Annotation" },
      "Custom annotations handled for Analytic parameter"
    );

    const editedCsn2 = {
      viewTechName: {
        params: {
          ViewParam: {
            "@Semantics.businessDate.at": false,
          },
        },
      },
    };
    // Re-edit, set parameter Semantic type annotation to false and ensure the galilei attribute is cleaned!
    erCsnToModel.updateEntityAnnotations(oOutput, editedCsn2);
    assert.deepStrictEqual(oOutput.parameters.get(0).semanticType, "", "Semantic type cleaned for Input parameter");

    // Delete restricted measure and check analytic parameters are deleted
    oRestrictedMeasure.deleteObject();
    assert.strictEqual(
      oOutput.analyticParameters.length,
      0,
      "Analytic parameters deleted after deleting last restricted measure"
    );
  });

  it("Exception Aggregation changes", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel._unitTesting = true;
    const oOutput = oNewModel.output;
    const oProduct = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      { name: "Product" },
      oNewModel
    );
    let oProductId = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "ProductId", dataType: "cds.Integer", length: 10 },
      oProduct
    );
    const oQuantity = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Quantity", dataType: "cds.Integer", length: 10 },
      oProduct
    );
    const oPrice = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Price", dataType: "cds.Decimal", precision: 5, scale: 2 },
      oProduct
    );

    oProduct.successorNode = oOutput;
    oOutput.dataCategory = DataCategory.FACT;

    SupportedFeaturesService.getInstance().isAnalyticMeasureFormulaEnabled = () => true;
    SupportedFeaturesService.getInstance().isDataActivationEnabled = () => true;
    SupportedFeaturesService.getInstance().isExceptionAggregationEnabled = () => true;

    oQuantity.successorElement.isMeasure = true;
    // Create Exception Aggrgegation and set its base measure
    const oExceptionAggregation = nsQueryBuilder.NodeImpl.createNewExceptionAggregationElement(oOutput);

    // Check basic CSN generation
    const oCsnElement = {};

    // write CSN
    csnUtils.isMeasureWriteCsn(oExceptionAggregation, oCsnElement);
    assert.deepStrictEqual(
      oCsnElement,
      {
        "@AnalyticsDetails.measureType": { "#": "RESTRICTION" },
      },
      "isMeasureWriteCsn() is ok for Exception Aggregation"
    );
    // Set Quantity to source measure
    // Set default aggregation to AVG
    oExceptionAggregation.sourceMeasure = oQuantity.successorElement;
    oExceptionAggregation.defaultAggregation = "AVG";
    assert.deepStrictEqual(oExceptionAggregation.dataType, "cds.Integer", "Data type ok for exception aggregation");

    // Write Csn and check element and query column are OK!
    let expectedQuery = {
      SELECT: {
        from: { ref: ["Product"] },
        columns: [
          { ref: ["ProductId"] },
          { ref: ["Quantity"] },
          { ref: ["Price"] },
          { ref: ["Quantity"], as: "ExceptionAggregation" },
        ],
      },
    };

    // set attributes
    oExceptionAggregation.exceptionAggregationAttributes.push(oProductId.successorElement);
    csnUtils.exceptionAggregationStepsWriteCsn(oExceptionAggregation, oCsnElement);
    let oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    assert.deepStrictEqual(
      oCsn.definitions.View_1.elements[oExceptionAggregation.newName]["@Aggregation.default"],
      { "#": "AVG" },
      "Source aggregation type is ok"
    );
    assert.deepStrictEqual(
      oCsn.definitions.View_1.elements[oExceptionAggregation.newName]["@AnalyticsDetails.measureType"],
      { "#": "RESTRICTION" },
      "Exception Aggregation Type is ok"
    );
    assert.deepStrictEqual(
      oCsn.definitions.View_1.elements[oExceptionAggregation.newName]["@AnalyticsDetails.exceptionAggregationSteps"],
      [{ exceptionAggregationBehavior: { "#": "SUM" }, exceptionAggregationElements: ["ProductId"] }],
      "Exception Aggregation Steps is ok"
    );
    assert.deepStrictEqual(oCsn.definitions.View_1.query, expectedQuery, "CSN is ok");

    // rename a column used as an attribute for above created exception aggregation
    const oRenameElements = createIntermediateOperation(oNewModel, "RenameElements", oProduct);
    oProductId = oRenameElements.elements.get(0);
    oProductId.newName = "newProductID";
    expectedQuery = {
      SELECT: {
        from: { ref: ["Product"] },
        columns: [
          { as: "newProductID", ref: ["ProductId"] },
          { ref: ["Quantity"] },
          { ref: ["Price"] },
          { ref: ["Quantity"], as: "ExceptionAggregation" },
        ],
      },
    };

    // check new name propagations
    csnUtils.exceptionAggregationStepsWriteCsn(oExceptionAggregation, oCsnElement);
    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    assert.deepStrictEqual(
      oCsn.definitions.View_1.elements[oExceptionAggregation.newName]["@AnalyticsDetails.exceptionAggregationSteps"],
      [{ exceptionAggregationBehavior: { "#": "SUM" }, exceptionAggregationElements: ["newProductID"] }],
      "Rename Exception Aggregation Steps is ok"
    );
    assert.deepStrictEqual(oCsn.definitions.View_1.query, expectedQuery, "CSN is ok");

    // Validations...
    let validations;
    // Empty name
    oExceptionAggregation.newName = "";
    await oOutput.validate();
    validations = oOutput.aggregatedValidations?.validations;
    let aErrors = [];
    oOutput.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.strictEqual(
      aErrors.toString(),
      "VAL_ADS_DEPRECATED_WARN,VAL_EXCEPTION_AGG_EMPTY_NAME",
      "Missing Exception Aggrgegation name"
    );

    // Empty source..
    oExceptionAggregation.newName = "ExceptionAggregation";
    oExceptionAggregation.sourceMeasure = undefined;
    await oOutput.validate();
    validations = oOutput.aggregatedValidations?.validations;
    aErrors = [];
    oOutput.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.strictEqual(
      aErrors.toString(),
      "VAL_ADS_DEPRECATED_WARN,VAL_EXCEPTION_AGG_EMPTY_SOURCE",
      "Missing Exception Aggrgegation source"
    );

    // Invalid source..
    oExceptionAggregation.sourceMeasure = oProductId.successorElement;
    await oOutput.validate();
    validations = oOutput.aggregatedValidations?.validations;
    aErrors = [];
    oOutput.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.strictEqual(
      aErrors.toString(),
      "VAL_ADS_DEPRECATED_WARN,VAL_EXCEPTION_AGG_INVALID_SOURCE",
      "Invalid Exception Aggrgegation source"
    );

    // Empty aggregation attributes
    oQuantity.successorElement.isMeasure = true;
    oExceptionAggregation.sourceMeasure = oQuantity.successorElement;
    oExceptionAggregation.exceptionAggregationAttributes.clear();
    await oOutput.validate();
    validations = oOutput.aggregatedValidations?.validations;
    aErrors = [];
    oOutput.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.strictEqual(
      aErrors.toString(),
      "VAL_ADS_DEPRECATED_WARN,VAL_EXCEPTION_AGG_EMPTY_AGG_ATTRIBUTES",
      "Empty Exception Aggrgegation attributes"
    );

    // (DW101-4112) add calculated column and select it as exception aggregation attributes, newName should appear in exceptionAggregationElements in csn
    const oCalc1 = oProduct.successorNode;
    const oCalcElt1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.CalculatedElements",
      { name: "TotalPriceCalc" },
      oCalc1
    );
    oCalcElt1.newName = "TotalPriceCalc_changed";
    oCalcElt1.label = "TotalPriceCalc_changed";
    oExceptionAggregation.exceptionAggregationAttributes.clear();
    oExceptionAggregation.exceptionAggregationAttributes.push(oCalcElt1);
    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    expectedQuery = {
      SELECT: {
        from: { ref: ["Product"] },
        columns: [
          { as: "newProductID", ref: ["ProductId"] },
          { ref: ["Quantity"] },
          { ref: ["Price"] },
          { ref: ["Quantity"], as: "ExceptionAggregation" },
          { ref: ["TotalPriceCalc"] },
        ],
      },
    };
    assert.deepStrictEqual(
      oCsn.definitions.View_1.elements[oExceptionAggregation.newName]["@AnalyticsDetails.exceptionAggregationSteps"],
      [{ exceptionAggregationBehavior: { "#": "SUM" }, exceptionAggregationElements: ["TotalPriceCalc_changed"] }],
      "Use calculated column in Exception Aggregation attributes is ok"
    );
    assert.deepStrictEqual(
      oCsn.definitions.View_1.query,
      expectedQuery,
      "CSN with calculated column as aggregation attribute is ok"
    );
  });

  it("DW101-4119 : Exception Aggregation with formula as source measure aggregation type", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel._unitTesting = true;
    const oOutput = oNewModel.output;
    const oProduct = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      { name: "Product" },
      oNewModel
    );
    const oProductId = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "ProductId", dataType: "cds.Integer", length: 10 },
      oProduct
    );
    const oQuantity = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Quantity", dataType: "cds.Integer", length: 10 },
      oProduct
    );
    const oPrice = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Price", dataType: "cds.Decimal", precision: 5, scale: 2 },
      oProduct
    );

    oProduct.successorNode = oOutput;
    oOutput.dataCategory = DataCategory.FACT;

    SupportedFeaturesService.getInstance().isAnalyticMeasureFormulaEnabled = () => true;
    SupportedFeaturesService.getInstance().isDataActivationEnabled = () => true;
    SupportedFeaturesService.getInstance().isExceptionAggregationEnabled = () => true;
    // Check basic CSN generation
    const oCsnElement = {};
    // create a formula analytical measure
    const oFormulaElement = nsQueryBuilder.NodeImpl.createNewFormulaElement(oOutput);

    csnUtils.isMeasureWriteCsn(oFormulaElement, oCsnElement);
    oFormulaElement.dataType = "Integer";
    assert.deepEqual(
      oCsnElement,
      {
        "@AnalyticsDetails.measureType": { "#": "CALCULATION" },
      },
      "isMeasureWriteCsn() is ok"
    );

    oNewModel.validate();
    const sXpr = "Price * Quantity";
    const oXpr = { xpr: [{ ref: ["Price"] }, "/", { ref: ["Quantity"] }] } as any;
    oFormulaElement.expression = sXpr;
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oFormulaElement, oXpr);

    oQuantity.successorElement.isMeasure = true;
    // Create Exception Aggrgegation and set its base measure
    const oExceptionAggregation = nsQueryBuilder.NodeImpl.createNewExceptionAggregationElement(oOutput);

    // write CSN
    csnUtils.isMeasureWriteCsn(oExceptionAggregation, oCsnElement);
    assert.deepStrictEqual(
      oCsnElement,
      {
        "@AnalyticsDetails.measureType": { "#": "RESTRICTION" },
      },
      "isMeasureWriteCsn() is ok for Exception Aggregation"
    );
    // Set Quantity to source measure
    // Set default aggregation to AVG
    oExceptionAggregation.sourceMeasure = oFormulaElement;

    // Write Csn and check element and query column are OK!
    const expectedQuery = {
      SELECT: {
        from: { ref: ["Product"] },
        columns: [
          { ref: ["ProductId"] },
          { ref: ["Quantity"] },
          { ref: ["Price"] },
          {
            as: "Formula",
            xpr: [
              {
                ref: ["Product", "Price"],
              },
              "/",
              {
                ref: ["Product", "Quantity"],
              },
            ],
          },
          { ref: ["$projection", "Formula"], as: "ExceptionAggregation" },
        ],
      },
    };

    // set attributes
    oExceptionAggregation.exceptionAggregationAttributes.push(oProductId.successorElement);
    csnUtils.exceptionAggregationStepsWriteCsn(oExceptionAggregation, oCsnElement);
    const oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    assert.deepStrictEqual(
      oCsn.definitions.View_1.elements[oExceptionAggregation.newName]["@Aggregation.default"],
      { "#": "FORMULA" },
      "Source aggregation type is ok"
    );
    assert.deepStrictEqual(
      oCsn.definitions.View_1.elements[oExceptionAggregation.newName]["@AnalyticsDetails.measureType"],
      { "#": "CALCULATION" },
      "Exception Aggregation Type is ok"
    );
    assert.deepStrictEqual(
      oCsn.definitions.View_1.elements[oExceptionAggregation.newName]["@AnalyticsDetails.exceptionAggregationSteps"],
      [{ exceptionAggregationBehavior: { "#": "SUM" }, exceptionAggregationElements: ["ProductId"] }],
      "Exception Aggregation Steps is ok"
    );
    assert.deepStrictEqual(oCsn.definitions.View_1.query, expectedQuery, "CSN is ok");
  });

  it("DW101-4365: Analytic Measure Excepton aggregation changes with Union", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel._unitTesting = true;

    SupportedFeaturesService.getInstance().isAnalyticMeasureFormulaEnabled = () => true;
    SupportedFeaturesService.getInstance().isExceptionAggregationEnabled = () => true;

    const oOutput = oNewModel.output;
    const oEntity1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      { name: "Entity1" },
      oNewModel
    );
    const oElement1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "ProductId", dataType: "cds.String", length: 60 },
      oEntity1
    );
    const oElement2 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Quantity", dataType: "cds.Integer", length: 10 },
      oEntity1
    );
    const oElement3 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Price", dataType: "cds.Decimal", precision: 5, scale: 2 },
      oEntity1
    );

    const oEntity2 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      { name: "Entity2" },
      oNewModel
    );
    const oElement21 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "ProductId", dataType: "cds.String", length: 60 },
      oEntity2
    );
    const oElement22 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Quantity", dataType: "cds.Integer", length: 10 },
      oEntity2
    );
    const oElement23 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Price", dataType: "cds.Decimal", precision: 5, scale: 2 },
      oEntity2
    );

    const oUnion1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Union",
      {
        name: "Union1",
        isUnionAll: true, // Default is union all, so set to union
      },
      oNewModel
    );

    // emulate drop entity2 on entity1 on diagram
    onPostCreateEntity(oEntity2, oEntity1, "Union");
    const union = oEntity1.successorNode;

    // map enitity1 to the union
    nsQueryBuilder.NodeImpl.copyElementsForSetNode(union, oEntity2);

    union.successorNode = oOutput;

    oOutput.elements.get(1).isMeasure = true;
    oOutput.elements.get(2).isMeasure = true;

    const oFormulaElement = nsQueryBuilder.NodeImpl.createNewFormulaElement(oOutput);
    const oCsnElement = {};
    csnUtils.isMeasureWriteCsn(oFormulaElement, oCsnElement);
    oFormulaElement.dataType = "Integer";
    assert.deepEqual(
      oCsnElement,
      {
        "@AnalyticsDetails.measureType": { "#": "CALCULATION" },
      },
      "isMeasureWriteCsn() is ok"
    );

    oNewModel.validate();
    const sXpr = "Price * Quantity";
    const oXpr = { xpr: [{ ref: ["Price"] }, "/", { ref: ["Quantity"] }] } as any;
    oFormulaElement.expression = sXpr;
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oFormulaElement, oXpr);

    let oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    assert.deepEqual(
      oCsn.definitions.View_1.elements[oFormulaElement.newName]["@Aggregation.default"],
      { "#": "FORMULA" },
      "Aggregation default is ok"
    );
    assert.deepEqual(
      oCsn.definitions.View_1.elements[oFormulaElement.newName]["@AnalyticsDetails.measureType"],
      { "#": "CALCULATION" },
      "Aggregation measureType is ok"
    );
    assert.deepEqual(
      oCsn.definitions.View_1.query,
      {
        SELECT: {
          from: {
            SET: {
              op: "union",
              all: true,
              args: [
                {
                  SELECT: {
                    from: {
                      ref: ["Entity2"],
                    },
                    columns: [
                      {
                        ref: ["Entity2", "ProductId"],
                      },
                      {
                        ref: ["Entity2", "Quantity"],
                      },
                      {
                        ref: ["Entity2", "Price"],
                      },
                    ],
                  },
                },
                {
                  SELECT: {
                    from: {
                      ref: ["Entity1"],
                    },
                    columns: [
                      {
                        ref: ["Entity1", "ProductId"],
                      },
                      {
                        ref: ["Entity1", "Quantity"],
                      },
                      {
                        ref: ["Entity1", "Price"],
                      },
                    ],
                  },
                },
              ],
            },
            as: "unionSet",
          },
          columns: [
            {
              ref: ["ProductId"],
            },
            {
              ref: ["Quantity"],
            },
            {
              ref: ["Price"],
            },
            {
              as: "Formula",
              xpr: [
                {
                  ref: ["Price"],
                },
                "/",
                {
                  ref: ["Quantity"],
                },
              ],
            },
          ],
        },
      },
      "CSN is ok"
    );

    oElement22.successorElement.isMeasure = true;
    // Create Exception Aggrgegation and set its base measure
    const oExceptionAggregation = nsQueryBuilder.NodeImpl.createNewExceptionAggregationElement(oOutput);
    // write CSN
    csnUtils.isMeasureWriteCsn(oExceptionAggregation, oCsnElement);
    assert.deepStrictEqual(
      oCsnElement,
      {
        "@AnalyticsDetails.measureType": { "#": "RESTRICTION" },
      },
      "isMeasureWriteCsn() is ok for Exception Aggregation"
    );
    // Set Quantity to source measure
    // Set default aggregation to AVG
    oExceptionAggregation.sourceMeasure = oElement22.successorElement;
    oExceptionAggregation.defaultAggregation = "AVG";
    assert.deepStrictEqual(oExceptionAggregation.dataType, "cds.Integer", "Data type ok for exception aggregation");

    // Write Csn and check element and query column are OK!
    const expectedQuery = {
      SELECT: {
        from: {
          SET: {
            op: "union",
            all: true,
            args: [
              {
                SELECT: {
                  from: {
                    ref: ["Entity2"],
                  },
                  columns: [
                    {
                      ref: ["Entity2", "ProductId"],
                    },
                    {
                      ref: ["Entity2", "Quantity"],
                    },
                    {
                      ref: ["Entity2", "Price"],
                    },
                  ],
                },
              },
              {
                SELECT: {
                  from: {
                    ref: ["Entity1"],
                  },
                  columns: [
                    {
                      ref: ["Entity1", "ProductId"],
                    },
                    {
                      ref: ["Entity1", "Quantity"],
                    },
                    {
                      ref: ["Entity1", "Price"],
                    },
                  ],
                },
              },
            ],
          },
          as: "unionSet",
        },
        columns: [
          {
            ref: ["ProductId"],
          },
          {
            ref: ["Quantity"],
          },
          {
            ref: ["Price"],
          },
          {
            as: "Formula",
            xpr: [
              {
                ref: ["Price"],
              },
              "/",
              {
                ref: ["Quantity"],
              },
            ],
          },
          {
            as: "ExceptionAggregation",
            ref: ["$projection", "Quantity"],
          },
        ],
      },
    };

    // set attributes
    oExceptionAggregation.exceptionAggregationAttributes.push(oElement1.successorElement);
    csnUtils.exceptionAggregationStepsWriteCsn(oExceptionAggregation, oCsnElement);
    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    assert.deepStrictEqual(
      oCsn.definitions.View_1.elements[oExceptionAggregation.newName]["@Aggregation.default"],
      { "#": "AVG" },
      "Source aggregation type is ok"
    );
    assert.deepStrictEqual(
      oCsn.definitions.View_1.elements[oExceptionAggregation.newName]["@AnalyticsDetails.measureType"],
      { "#": "RESTRICTION" },
      "Exception Aggregation Type is ok"
    );
    assert.deepStrictEqual(
      oCsn.definitions.View_1.elements[oExceptionAggregation.newName]["@AnalyticsDetails.exceptionAggregationSteps"],
      [{ exceptionAggregationBehavior: { "#": "SUM" }, exceptionAggregationElements: ["ProductId"] }],
      "Exception Aggregation Steps is ok"
    );
    assert.deepStrictEqual(oCsn.definitions.View_1.query, expectedQuery, "CSN is ok");
  });

  it("Count distinct checks", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel._unitTesting = true;
    const oOutput = oNewModel.output;
    const oProduct = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      { name: "Product" },
      oNewModel
    );
    const oProductId = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "ProductId", dataType: "cds.Integer", length: 10 },
      oProduct
    );
    const oQuantity = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Quantity", dataType: "cds.Integer", length: 10 },
      oProduct
    );
    const oPrice = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Price", dataType: "cds.Decimal", precision: 5, scale: 2 },
      oProduct
    );

    oProduct.successorNode = oOutput;
    oOutput.dataCategory = DataCategory.FACT;

    SupportedFeaturesService.getInstance().isAnalyticMeasureFormulaEnabled = () => true;
    SupportedFeaturesService.getInstance().isCountDistinctEnabled = () => true;

    oQuantity.successorElement.isMeasure = true;
    // Create count Distinct
    const oCountDistinct = nsQueryBuilder.NodeImpl.createCountDistinct(oOutput);

    // Check basic CSN generation
    const oCsnElement = {};

    // write CSN
    csnUtils.isMeasureWriteCsn(oCountDistinct, oCsnElement);
    assert.deepStrictEqual(
      oCsnElement,
      {
        "@AnalyticsDetails.measureType": { "#": "BASE" },
      },
      "isMeasureWriteCsn() is ok for count distinct"
    );
    // Set Quantity to source measure
    // Set default aggregation to AVG
    oCountDistinct.sourceMeasure = oQuantity.successorElement;
    oCountDistinct.defaultAggregation = "COUNT_DISTINCT";
    assert.deepStrictEqual(oCountDistinct.dataType, "cds.Integer", "Data type ok for exception aggregation");

    // Write Csn and check element and query column are OK!
    const expectedQuery = {
      SELECT: {
        from: { ref: ["Product"] },
        columns: [
          { ref: ["ProductId"] },
          { ref: ["Quantity"] },
          { ref: ["Price"] },
          { as: "CountDistinct", val: null },
        ],
      },
    };

    // set attributes
    oCountDistinct.referenceAttributes.push(oProductId.successorElement);
    csnUtils.referenceElementWriteCsn(oCountDistinct, oCsnElement);
    const oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    assert.deepStrictEqual(
      oCsn.definitions.View_1.elements[oCountDistinct.newName]["@Aggregation.default"],
      { "#": "COUNT_DISTINCT" },
      "Source aggregation type is ok"
    );
    assert.deepStrictEqual(
      oCsn.definitions.View_1.elements[oCountDistinct.newName]["@AnalyticsDetails.measureType"],
      { "#": "BASE" },
      "Exception Aggregation Type is ok"
    );

    assert.deepStrictEqual(oCsn.definitions.View_1.query, expectedQuery, "CSN is ok");

    // Validations...
    let validations;
    // Empty name
    oCountDistinct.newName = "";
    await oOutput.validate();
    validations = oOutput.aggregatedValidations?.validations;
    let aErrors = [];
    oOutput.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.strictEqual(
      aErrors.toString(),
      "VAL_ADS_DEPRECATED_WARN,VAL_EXCEPTION_COUNT_DISTINCT_EMPTY_NAME",
      "Missing count distinct name"
    );

    // Invalid reference Attribute
    oCountDistinct.newName = "CountDistinct";
    oProductId.successorElement.isMeasure = true;
    await oOutput.validate();
    validations = oOutput.aggregatedValidations?.validations;
    aErrors = [];
    oOutput.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.strictEqual(
      aErrors.toString(),
      "VAL_ADS_DEPRECATED_WARN,VAL_EXCEPTION_COUNT_DISTINCT_INVALID_SOURCE",
      "Invalid reference attribute"
    );

    // Empty reference attributes
    oProductId.successorElement.isMeasure = false;
    oCountDistinct.referenceAttributes.clear();
    await oOutput.validate();
    validations = oOutput.aggregatedValidations?.validations;
    aErrors = [];
    oOutput.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.strictEqual(
      aErrors.toString(),
      "VAL_ADS_DEPRECATED_WARN,VAL_EXCEPTION_COUNT_DISTINCT_EMPTY_SOURCE",
      "Empty reference attributes"
    );
  });

  it("(Entity1->Entity2 -> Join -> output -> association", async () => {
    sap.cdw.querybuilder.ModelImpl.HANDLE_JOIN_DUPLICATE = true;
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel.name = "csnTest";
    assert.notEqual(undefined, oNewModel, "New model created");

    const oEntity1 = createEntity(oNewModel, "Entity 1", 3);
    // emulate drop entity on diagram
    onPostCreateEntity(oEntity1);
    const oEntity2 = createEntity(oNewModel, "Entity 2", 3);

    // emulate drop entity2 on entity1 on diagram
    onPostCreateEntity(oEntity2, oEntity1, "Join");

    // Create association and check the output
    // let oDimensionNode = nsQueryBuilder.ModelImpl.createDimension(oNewModel, { name: "dimensionNode", elements: { e: { type: "string" } } });
    const oDimensionNode = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.DimensionNode",
      { name: "dimensionNode" },
      oNewModel
    );
    nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      {
        name: "Element_0",
        dataType: "cds.String",
        length: 30,
      },
      oDimensionNode
    );
    const oAssociation1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Association",
      {
        name: "viewAssociation",
        maxCardinality: "1",
        source: oNewModel.output,
        target: oDimensionNode,
      },
      oNewModel
    );
    // Create mapping..
    nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.commonmodel.ElementMapping",
      {
        source: oNewModel.output.elements.get(0),
        target: oDimensionNode.elements.get(0),
      },
      oAssociation1.mappings
    );
    // setuiCore(true)
    let oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    assert.deepEqual(
      oCsn.definitions.csnTest.elements.viewAssociation,
      {
        "@EndUserText.label": "viewAssociation",
        on: [
          {
            ref: ["Element_0"],
          },
          "=",
          {
            ref: ["viewAssociation", "Element_0"],
          },
        ],
        target: "dimensionNode",
        type: "cds.Association",
      },
      "check csn element for output association"
    );
    assert.deepEqual(
      oCsn.definitions.csnTest.query.SELECT.mixin.viewAssociation,
      {
        on: [
          {
            ref: ["$projection", "Element_0"],
          },
          "=",
          {
            ref: ["viewAssociation", "Element_0"],
          },
        ],
        target: "dimensionNode",
        type: "cds.Association",
      },
      "check csn mixin for output association"
    );

    const oJoin = oEntity1.successorNode;
    oJoin.leftCardinality = "0,1";
    oJoin.rightCardinality = "*";

    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel, { rootObject: oJoin, dataPreview: true });
    assert.deepEqual(oCsn.definitions["Join 1"].query.SELECT.from, {
      join: "inner",
      args: [{ ref: ["Entity 1"] }, { ref: ["Entity 2"] }],
      cardinality: { src: 1, max: "*" },
      on: [
        {
          ref: ["Entity 1", "Element_0"],
        },
        "=",
        {
          ref: ["Entity 2", "Element_0"],
        },
      ],
    });
    nsQueryBuilder.ModelImpl.swapJoinInput(oJoin);
    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel, { rootObject: oJoin, dataPreview: true });
    assert.deepEqual(oCsn.definitions["Join 1"].query.SELECT.from, {
      join: "inner",
      args: [{ ref: ["Entity 2"] }, { ref: ["Entity 1"] }],
      cardinality: { max: 1 },
      on: [
        {
          ref: ["Entity 2", "Element_0"],
        },
        "=",
        {
          ref: ["Entity 1", "Element_0"],
        },
      ],
    });
  });

  it("Analytic Measure count distinct with Union", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel._unitTesting = true;

    SupportedFeaturesService.getInstance().isAnalyticMeasureFormulaEnabled = () => true;
    SupportedFeaturesService.getInstance().isCountDistinctEnabled = () => true;
    const oCsnElement = {};
    const oOutput = oNewModel.output;
    const oEntity1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      { name: "Entity1" },
      oNewModel
    );
    const oElement1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "ProductId", dataType: "cds.String", length: 60 },
      oEntity1
    );
    const oElement2 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Quantity", dataType: "cds.Integer", length: 10 },
      oEntity1
    );
    const oElement3 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Price", dataType: "cds.Decimal", precision: 5, scale: 2 },
      oEntity1
    );

    const oEntity2 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      { name: "Entity2" },
      oNewModel
    );
    const oElement21 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "ProductId", dataType: "cds.String", length: 60 },
      oEntity2
    );
    const oElement22 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Quantity", dataType: "cds.Integer", length: 10 },
      oEntity2
    );
    const oElement23 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Price", dataType: "cds.Decimal", precision: 5, scale: 2 },
      oEntity2
    );

    const oUnion1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Union",
      {
        name: "Union1",
        isUnionAll: true, // Default is union all, so set to union
      },
      oNewModel
    );

    // emulate drop entity2 on entity1 on diagram
    onPostCreateEntity(oEntity2, oEntity1, "Union");
    const union = oEntity1.successorNode;

    // map enitity1 to the union
    nsQueryBuilder.NodeImpl.copyElementsForSetNode(union, oEntity2);

    union.successorNode = oOutput;

    oOutput.elements.get(1).isMeasure = true;
    oOutput.elements.get(2).isMeasure = true;
    // Create count Distinct
    const oCountDistinct = nsQueryBuilder.NodeImpl.createCountDistinct(oOutput);
    // write CSN
    csnUtils.isMeasureWriteCsn(oCountDistinct, oCsnElement);
    assert.deepStrictEqual(
      oCsnElement,
      {
        "@AnalyticsDetails.measureType": { "#": "BASE" },
      },
      "isMeasureWriteCsn() is ok for count distinct"
    );

    oCountDistinct.defaultAggregation = "COUNT_DISTINCT";
    assert.deepStrictEqual(oCountDistinct.dataType, "cds.Integer", "Data type ok for exception aggregation");

    // Write Csn and check element and query column are OK!
    const expectedQuery = {
      SELECT: {
        from: {
          SET: {
            op: "union",
            all: true,
            args: [
              {
                SELECT: {
                  from: { ref: ["Entity2"] },
                  columns: [
                    { ref: ["Entity2", "ProductId"] },
                    { ref: ["Entity2", "Quantity"] },
                    { ref: ["Entity2", "Price"] },
                  ],
                },
              },
              {
                SELECT: {
                  from: { ref: ["Entity1"] },
                  columns: [
                    { ref: ["Entity1", "ProductId"] },
                    { ref: ["Entity1", "Quantity"] },
                    { ref: ["Entity1", "Price"] },
                  ],
                },
              },
            ],
          },
          as: "unionSet",
        },
        columns: [
          { ref: ["ProductId"] },
          { ref: ["Quantity"] },
          { ref: ["Price"] },
          { as: "CountDistinct", val: null },
        ],
      },
    };

    // set attributes
    oCountDistinct.referenceAttributes.push(oElement1);
    csnUtils.referenceElementWriteCsn(oCountDistinct, oCsnElement);
    const oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    assert.deepStrictEqual(
      oCsn.definitions.View_1.elements[oCountDistinct.newName]["@Aggregation.default"],
      { "#": "COUNT_DISTINCT" },
      "Source aggregation type is ok"
    );
    assert.deepStrictEqual(
      oCsn.definitions.View_1.elements[oCountDistinct.newName]["@AnalyticsDetails.measureType"],
      { "#": "BASE" },
      "Exception Aggregation Type is ok"
    );

    assert.deepStrictEqual(oCsn.definitions.View_1.query, expectedQuery, "CSN is ok");
  });

  it("Analytic Measure Formula changes - Union", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel._unitTesting = true;

    SupportedFeaturesService.getInstance().isAnalyticMeasureFormulaEnabled = () => true;
    SupportedFeaturesService.getInstance().isDataActivationEnabled = () => true;

    const oOutput = oNewModel.output;
    const oEntity1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      { name: "Entity1" },
      oNewModel
    );
    const oElement1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "ProductId", dataType: "cds.String", length: 60 },
      oEntity1
    );
    const oElement2 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Quantity", dataType: "cds.Integer", length: 10 },
      oEntity1
    );
    const oElement3 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Price", dataType: "cds.Decimal", precision: 5, scale: 2 },
      oEntity1
    );

    const oEntity2 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      { name: "Entity2" },
      oNewModel
    );
    const oElement21 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "ProductId", dataType: "cds.String", length: 60 },
      oEntity2
    );
    const oElement22 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Quantity", dataType: "cds.Integer", length: 10 },
      oEntity2
    );
    const oElement23 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Price", dataType: "cds.Decimal", precision: 5, scale: 2 },
      oEntity2
    );

    const oUnion1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Union",
      {
        name: "Union1",
        isUnionAll: true, // Default is union all, so set to union
      },
      oNewModel
    );

    // emulate drop entity2 on entity1 on diagram
    onPostCreateEntity(oEntity2, oEntity1, "Union");
    const union = oEntity1.successorNode;

    // map enitity1 to the union
    nsQueryBuilder.NodeImpl.copyElementsForSetNode(union, oEntity2);

    union.successorNode = oOutput;

    oOutput.elements.get(1).isMeasure = true;
    oOutput.elements.get(2).isMeasure = true;

    const oFormulaElement = nsQueryBuilder.NodeImpl.createNewFormulaElement(oOutput);
    const oCsnElement = {};
    csnUtils.isMeasureWriteCsn(oFormulaElement, oCsnElement);
    oFormulaElement.dataType = "cds.Integer";
    assert.deepEqual(
      oCsnElement,
      {
        "@AnalyticsDetails.measureType": { "#": "CALCULATION" },
      },
      "isMeasureWriteCsn() is ok"
    );

    oNewModel.validate();
    const sXpr = "Price * Quantity";
    const oXpr = { xpr: [{ ref: ["Price"] }, "/", { ref: ["Quantity"] }] } as any;
    oFormulaElement.expression = sXpr;
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oFormulaElement, oXpr);

    const oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    assert.deepEqual(
      oCsn.definitions.View_1.elements[oFormulaElement.newName]["@Aggregation.default"],
      { "#": "FORMULA" },
      "Aggregation default is ok"
    );
    assert.deepEqual(
      oCsn.definitions.View_1.elements[oFormulaElement.newName]["@AnalyticsDetails.measureType"],
      { "#": "CALCULATION" },
      "Aggregation measureType is ok"
    );
    assert.deepEqual(
      oCsn.definitions.View_1.query,
      {
        SELECT: {
          from: {
            SET: {
              op: "union",
              all: true,
              args: [
                {
                  SELECT: {
                    from: {
                      ref: ["Entity2"],
                    },
                    columns: [
                      {
                        ref: ["Entity2", "ProductId"],
                      },
                      {
                        ref: ["Entity2", "Quantity"],
                      },
                      {
                        ref: ["Entity2", "Price"],
                      },
                    ],
                  },
                },
                {
                  SELECT: {
                    from: {
                      ref: ["Entity1"],
                    },
                    columns: [
                      {
                        ref: ["Entity1", "ProductId"],
                      },
                      {
                        ref: ["Entity1", "Quantity"],
                      },
                      {
                        ref: ["Entity1", "Price"],
                      },
                    ],
                  },
                },
              ],
            },
            as: "unionSet",
          },
          columns: [
            {
              ref: ["ProductId"],
            },
            {
              ref: ["Quantity"],
            },
            {
              ref: ["Price"],
            },
            {
              as: "Formula",
              xpr: [
                {
                  ref: ["Price"],
                },
                "/",
                {
                  ref: ["Quantity"],
                },
              ],
            },
          ],
        },
      },
      "CSN is ok"
    );
  });

  it("Filter before/after join CSN", async () => {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel.name = "csnTest";
    assert.notEqual(undefined, oNewModel, "New model created");

    const oEntity1 = createEntity(oNewModel, "Entity 1", 3);
    // emulate drop entity on diagram
    onPostCreateEntity(oEntity1);

    createIntermediateOperation(oNewModel, "Filter", oEntity1);
    const filterBefore = oEntity1.successorNode;
    filterBefore.condition = "Element_1 > 1";
    // since for unit tests we cannot call backend service to parse
    // expressions and our aim is not to validate parser. we give the
    // expected parsed expresion also.
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(filterBefore, {
      xpr: [
        {
          ref: ["Element_1"],
        },
        ">",
        {
          val: 1,
        },
      ],
    });

    const oEntity2 = createEntity(oNewModel, "Entity 2", 3);

    // emulate drop entity2 on entity1 on diagram
    onPostCreateEntity(oEntity2, filterBefore, "Join");
    const join = oEntity2.successorNode;
    createIntermediateOperation(oNewModel, "Filter", join);
    const filterAfter = join.successorNode;
    filterAfter.condition = "Element_2 > 2";
    // since for unit tests we cannot call backend service to parse
    // expressions and our aim is not to validate parser. we give the
    // expected parsed expresion also.
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(filterAfter, {
      xpr: [
        {
          ref: ["Element_2"],
        },
        ">",
        {
          val: 2,
        },
      ],
    });
    let oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    assert.deepEqual(
      oCsn.definitions.csnTest.query,
      {
        SELECT: {
          from: {
            join: "inner",
            args: [
              {
                SELECT: {
                  from: {
                    ref: ["Entity 1"],
                  },
                  columns: [
                    {
                      ref: ["Entity 1", "Element_0"],
                    },
                    {
                      ref: ["Entity 1", "Element_1"],
                    },
                    {
                      ref: ["Entity 1", "Element_2"],
                    },
                  ],
                  where: [
                    {
                      ref: ["Entity 1", "Element_1"],
                    },
                    ">",
                    {
                      val: 1,
                    },
                  ],
                },
                as: "internal_1",
              },
              {
                ref: ["Entity 2"],
              },
            ],
            on: [
              {
                ref: ["Element_0"],
              },
              "=",
              {
                ref: ["Entity 2", "Element_0"],
              },
            ],
          },
          columns: [
            {
              ref: ["Element_0"],
            },
            {
              ref: ["Element_1"],
            },
            {
              ref: ["Element_2"],
            },
          ],
          where: [
            {
              ref: ["Element_2"],
            },
            ">",
            {
              val: 2,
            },
          ],
        },
      },
      "Check before and & after join' filters"
    );
    // setuiCore();
    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    assert.deepEqual(
      oCsn.definitions.csnTest.query,
      {
        SELECT: {
          from: {
            join: "inner",
            args: [
              {
                SELECT: {
                  from: { ref: ["Entity 1"] },
                  columns: [
                    { ref: ["Entity 1", "Element_0"] },
                    { ref: ["Entity 1", "Element_1"] },
                    { ref: ["Entity 1", "Element_2"] },
                  ],
                  where: [{ ref: ["Entity 1", "Element_1"] }, ">", { val: 1 }],
                },
                as: "internal_1",
              },
              { ref: ["Entity 2"] },
            ],
            on: [{ ref: ["Element_0"] }, "=", { ref: ["Entity 2", "Element_0"] }],
          },
          columns: [{ ref: ["Element_0"] }, { ref: ["Element_1"] }, { ref: ["Element_2"] }],
          where: [{ ref: ["Element_2"] }, ">", { val: 2 }],
        },
      },
      "Check before and & after join' filters (subselect mode)"
    );
    filterAfter.deleteObject();
    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel, { rootObject: join });
    assert.deepEqual(
      oCsn.definitions.csnTest.query,
      {
        SELECT: {
          from: {
            join: "inner",
            args: [
              {
                SELECT: {
                  from: { ref: ["Entity 1"] },
                  columns: [
                    { ref: ["Entity 1", "Element_0"] },
                    { ref: ["Entity 1", "Element_1"] },
                    { ref: ["Entity 1", "Element_2"] },
                  ],
                  where: [{ ref: ["Entity 1", "Element_1"] }, ">", { val: 1 }],
                },
                as: "internal_1",
              },
              { ref: ["Entity 2"] },
            ],
            on: [{ ref: ["Element_0"] }, "=", { ref: ["Entity 2", "Element_0"] }],
          },
          columns: [
            { ref: ["Element_0"] },
            { ref: ["Entity 2", "Element_0"] },
            { ref: ["Element_1"] },
            { ref: ["Entity 2", "Element_1"] },
            { ref: ["Element_2"] },
            { ref: ["Entity 2", "Element_2"] },
          ],
        },
      },
      "Check only after join' filters (subselect mode)"
    );
    sinon.restore();
  });

  it("Test merge - Business name will roll-back to use actual column name instead of alias name after making change", () => {
    function createElementsFromJSON(model, elements) {
      assert.equal("Output", model.output.classDefinition.name, "There is a output for csn model");
      const oResource = model.resource;
      const aKeys = Object.keys(elements);
      oResource.applyUndoableAction(
        function () {
          // eslint-disable-next-line @typescript-eslint/prefer-for-of
          for (let i = 0; i < aKeys.length; i++) {
            const obj: any = {};
            Object.assign(obj, elements[aKeys[i]]);
            obj.label = elements[aKeys[i]].label;
            obj.dataType = elements[aKeys[i]].type || elements[aKeys[i]].dataType;
            obj.name = elements[aKeys[i]].name;
            const go = nsQueryBuilder.ModelImpl.createObject("sap.cdw.querybuilder.Element", obj, model.output);
            go.newName = aKeys[i];
          }
        },
        "Create elements",
        false
      );
    }
    function createSQLModel() {
      const oModel = createNewModelAndDiagrams();
      const elements = {
        column1: {
          type: "string",
          name: "column1",
          label: "colummn 1",
        },
        column11: {
          type: "string",
          name: "column1",
          label: "colummn 1",
        },
      };
      createElementsFromJSON(oModel, elements);
      return oModel;
    }
    function createSidePanelModel() {
      const oModel = createNewModelAndDiagrams();
      const elements = {
        column1: {
          type: "string",
          label: "label 1",
          name: "column1",
        },
        column11: {
          type: "string",
          label: "label 2",
          name: "column1",
        },
      };
      createElementsFromJSON(oModel, elements);
      return oModel;
    }

    const newModel = createSQLModel();

    const existingModel = createSidePanelModel();

    sap.cdw.querybuilder.MergeAdapter.mergeModel(newModel, existingModel, true);
    assert.ok(existingModel.resource.listOfActions.length > 0, "There are some actions in model2 resource after merge");
    assert.equal("2", existingModel.output.elements.length, "Output now has 2 elements after merge");

    assert.equal(existingModel.output.elements.get(0).label, "label 1", "column 1 business Name updated ok");
    assert.equal(existingModel.output.elements.get(1).label, "label 2", "column 2 business Name updated ok");
  });

  it("Business name and Technical name swaped(DW101-2997)", () => {
    function createElementsFromJSON(model, elements) {
      assert.equal("Output", model.output.classDefinition.name, "There is a output for csn model");
      const oResource = model.resource;
      const aKeys = Object.keys(elements);
      oResource.applyUndoableAction(
        function () {
          // eslint-disable-next-line @typescript-eslint/prefer-for-of
          for (let i = 0; i < aKeys.length; i++) {
            const obj: any = {};
            Object.assign(obj, elements[aKeys[i]]);
            obj.label = elements[aKeys[i]].label;
            obj.dataType = elements[aKeys[i]].type || elements[aKeys[i]].dataType;
            obj.name = elements[aKeys[i]].name;
            const go = nsQueryBuilder.ModelImpl.createObject("sap.cdw.querybuilder.Element", obj, model.output);
            go.newName = aKeys[i];
            go.isMeasure = elements[aKeys[i]].isMeasure;
            go.defaultAggregation = elements[aKeys[i]].defaultAggregation;
          }
        },
        "Create elements",
        false
      );
    }
    function createSQLModel() {
      const oModel = createNewModelAndDiagrams();
      const elements = {
        sumPrice: {
          type: "string",
          name: "PRICE",
          label: "PRICE",
          defaultAggregation: "SUM",
          isMeasure: true,
        },
      };
      createElementsFromJSON(oModel, elements);
      return oModel;
    }
    function createSidePanelModel() {
      const oModel = createNewModelAndDiagrams();
      const elements = {
        sumPrice: {
          type: "integer",
          name: "sumPrice",
          label: "sumPrice",
          defaultAggregation: "SUM",
          isMeasure: true,
        },
      };
      createElementsFromJSON(oModel, elements);
      return oModel;
    }

    const newModel = createSQLModel();

    const existingModel = createSidePanelModel();

    sap.cdw.querybuilder.MergeAdapter.mergeModel(newModel, existingModel, true);
    assert.ok(existingModel.resource.listOfActions.length > 0, "There are some actions in model2 resource after merge");
    assert.equal(existingModel.output.elements.get(0).label, "sumPrice", "Business Name updated ok");
    assert.equal(existingModel.output.elements.get(0).newName, "sumPrice", "Technical Name updated ok");
  });

  it("Analytic Measures in Formula Expression changes", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel._unitTesting = true;
    const oOutput = oNewModel.output;
    const oEntity1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      { name: "Entity1" },
      oNewModel
    );
    const oElement1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "ProductId", dataType: "cds.String", length: 60 },
      oEntity1
    );
    const oElement2 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Quantity", dataType: "cds.Integer", length: 10 },
      oEntity1
    );
    const oElement3 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Price", dataType: "cds.Decimal", precision: 5, scale: 2 },
      oEntity1
    );

    oEntity1.successorNode = oOutput;
    oOutput.dataCategory = DataCategory.FACT;

    SupportedFeaturesService.getInstance().isAnalyticMeasureFormulaEnabled = () => true;
    SupportedFeaturesService.getInstance().isDataActivationEnabled = () => true;

    oOutput.elements.get(1).isMeasure = true;
    oOutput.elements.get(2).isMeasure = true;
    const oFormulaElement = nsQueryBuilder.NodeImpl.createNewFormulaElement(oOutput);
    const oCsnElement = {};
    csnUtils.isMeasureWriteCsn(oFormulaElement, oCsnElement);
    oFormulaElement.dataType = "cds.Integer";
    assert.deepEqual(
      oCsnElement,
      {
        "@AnalyticsDetails.measureType": { "#": "CALCULATION" },
      },
      "isMeasureWriteCsn() is ok"
    );

    await oOutput.validate();
    let aErrors = [];
    oOutput.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.strictEqual(
      aErrors.toString(),
      "VAL_ADS_DEPRECATED_WARN,VAL_FORMULA_EMPTY_EX",
      "2 validation error for empty expression"
    );
    let sXpr = "Quantity * Price";
    let oXpr = { xpr: [{ ref: ["Quantity"] }, "*", { ref: ["Price"] }] } as any;
    oFormulaElement.expression = sXpr;
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oFormulaElement, oXpr);
    assert.equal("cds.Decimal", oFormulaElement.dataType, "formula element data type, ok");

    await oOutput.validate();
    aErrors = [];
    oOutput.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.strictEqual(aErrors.toString(), "VAL_ADS_DEPRECATED_WARN", "No Validation error");

    oOutput.elements.get(2).isMeasure = false;
    await oOutput.validate();
    aErrors = [];
    oOutput.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.strictEqual(
      aErrors.toString(),
      "VAL_ADS_DEPRECATED_WARN",
      "No validation error for Non Measure in expression"
    );

    oOutput.elements.get(2).isMeasure = true;

    const oFormulaElement2 = nsQueryBuilder.NodeImpl.createNewFormulaElement(oOutput);

    await oOutput.validate();
    aErrors = [];
    oOutput.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.strictEqual(
      aErrors.toString(),
      "VAL_ADS_DEPRECATED_WARN,VAL_FORMULA_EMPTY_EX",
      "One validation error for empty expression"
    );
    sXpr = `${oFormulaElement.newName} * Price`;
    oXpr = { xpr: [{ ref: [oFormulaElement.newName] }, "*", { ref: ["Price"] }] };
    oFormulaElement2.expression = sXpr;
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oFormulaElement2, oXpr);

    const oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    assert.deepEqual(
      oCsn.definitions.View_1.elements[oFormulaElement.newName]["@Aggregation.default"],
      { "#": "FORMULA" },
      "Aggregation default is ok"
    );
    assert.deepEqual(
      oCsn.definitions.View_1.elements[oFormulaElement.newName]["@AnalyticsDetails.measureType"],
      { "#": "CALCULATION" },
      "Aggregation measureType is ok"
    );
    assert.deepEqual(
      oCsn.definitions.View_1.query,
      {
        SELECT: {
          from: { ref: ["Entity1"] },
          columns: [
            { ref: ["ProductId"] },
            { ref: ["Quantity"] },
            { ref: ["Price"] },
            { as: "Formula", xpr: [{ ref: ["Entity1", "Quantity"] }, "*", { ref: ["Entity1", "Price"] }] },
            { as: "Formula1", xpr: [{ ref: ["$projection", "Formula"] }, "*", { ref: ["Entity1", "Price"] }] },
          ],
        },
      },
      "CSN is ok"
    );
  });

  it("DW101-3849 - Deploy error: Parameter length, precision and scale values missing in the csn", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel._unitTesting = true;
    const oOutput = oNewModel.output;
    const oParameter = sap.cdw.commonmodel.ModelImpl.createObject("sap.cdw.commonmodel.Parameter", {}, oOutput) as any;

    const oCsn = {} as any;
    oParameter.name = "paramName";
    oParameter.label = "Parameter Label";
    oParameter.dataType = "cds.hana.NCHAR";
    oParameter.length = 10;
    csnUtils.paramsWriteCsn(oOutput, oCsn);
    assert.deepStrictEqual(
      oCsn.params[oParameter.name],
      {
        "@EndUserText.label": oParameter.label,
        type: "cds.hana.NCHAR",
        length: 10,
      },
      "paramsWriteCsn() is ok (char)"
    );

    oParameter.dataType = "cds.Decimal";
    oParameter.length = 0;
    oParameter.precision = 10;
    oParameter.scale = 5;
    oParameter.defaultValue = "100.05";
    csnUtils.paramsWriteCsn(oOutput, oCsn);
    assert.deepStrictEqual(
      oCsn.params[oParameter.name],
      {
        "@EndUserText.label": oParameter.label,
        default: "100.05",
        type: "cds.Decimal",
        precision: 10,
        scale: 5,
      },
      "paramsWriteCsn() is ok (decimal)"
    );
  });

  it("DW101-4229 precision, scale and srid values missing in the csn", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel._unitTesting = true;
    const oOutput = oNewModel.output;
    const oEntity1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      { name: "Entity1" },
      oNewModel
    );
    const oElement1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "abap_curr", dataType: "abap.curr", precision: 38, scale: 38 },
      oEntity1
    );

    let oCsn = {} as any;
    csnUtils.dataTypePropertiesWriteCsn(oElement1, oCsn);
    assert.deepEqual(
      oCsn,
      {
        type: "abap.curr",
        precision: 38,
        scale: 38,
      },
      "dataTypePropertiesWriteCsn() is ok"
    );

    const oElement2 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "abap_geom_ewkb", dataType: "abap.geom_ewkb", srid: 1000004326 },
      oEntity1
    );
    oCsn = {};
    csnUtils.dataTypePropertiesWriteCsn(oElement2, oCsn);
    assert.deepEqual(
      oCsn,
      {
        type: "abap.geom_ewkb",
        srid: 1000004326,
      },
      "dataTypePropertiesWriteCsn() is ok 1"
    );
  });

  it("Analytic Measures in Formula using hidden measure and attribute", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel._unitTesting = true;
    const oOutput = oNewModel.output;
    const oEntity1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      { name: "Entity1" },
      oNewModel
    );
    const oElement1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "ProductId", dataType: "cds.String", length: 60 },
      oEntity1
    );
    const oElement2 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Quantity", dataType: "cds.Integer", length: 10 },
      oEntity1
    );
    const oElement3 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Price", dataType: "cds.Decimal", precision: 5, scale: 2 },
      oEntity1
    );
    const oElement4 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "attr_2", dataType: "cds.String", length: 60 },
      oEntity1
    );
    const oElement5 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "measure_2", dataType: "cds.Integer", length: 10 },
      oEntity1
    );

    oEntity1.successorNode = oOutput;
    oOutput.dataCategory = DataCategory.FACT;

    SupportedFeaturesService.getInstance().isAnalyticMeasureFormulaEnabled = () => true;
    SupportedFeaturesService.getInstance().isDataActivationEnabled = () => true;

    oOutput.elements.get(1).isMeasure = true;
    oOutput.elements.get(1).isVisible = false;
    oOutput.elements.get(3).isVisible = false;
    oOutput.elements.get(4).isVisible = false;
    oOutput.elements.get(2).isMeasure = true;
    const oFormulaElement = nsQueryBuilder.NodeImpl.createNewFormulaElement(oOutput);
    const oCsnElement = {};
    csnUtils.isMeasureWriteCsn(oFormulaElement, oCsnElement);
    oFormulaElement.dataType = "cds.Integer";
    assert.deepEqual(
      oCsnElement,
      {
        "@AnalyticsDetails.measureType": { "#": "CALCULATION" },
      },
      "isMeasureWriteCsn() is ok"
    );

    await oOutput.validate();
    let aErrors = [];
    oOutput.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.strictEqual(
      aErrors.toString(),
      "VAL_ADS_DEPRECATED_WARN,VAL_FORMULA_EMPTY_EX",
      "One validation error for empty expression"
    );
    let sXpr = "Quantity * Price";
    let oXpr = { xpr: [{ ref: ["Quantity"] }, "*", { ref: ["Price"] }] } as any;
    oFormulaElement.expression = sXpr;
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oFormulaElement, oXpr);
    assert.equal("cds.Decimal", oFormulaElement.dataType, "formula element data type, ok");

    await oOutput.validate();
    aErrors = [];
    oOutput.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.strictEqual(aErrors.toString(), "VAL_ADS_DEPRECATED_WARN", "No Validation error");

    oOutput.elements.get(2).isMeasure = true;

    const oFormulaElement2 = nsQueryBuilder.NodeImpl.createNewFormulaElement(oOutput);

    await oOutput.validate();
    aErrors = [];
    oOutput.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.strictEqual(
      aErrors.toString(),
      "VAL_ADS_DEPRECATED_WARN,VAL_FORMULA_EMPTY_EX",
      "One validation error for empty expression"
    );
    sXpr = "CASE WHEN ProductId='AA' THEN Price ELSE Price-10 END";
    oXpr = {
      xpr: [
        "case",
        "when",
        { ref: ["ProductId"] },
        "=",
        { val: "AA" },
        "then",
        { ref: ["Price"] },
        "else",
        { ref: ["Price"] },
        "-",
        { val: 10 },
        "end",
      ],
    };
    oFormulaElement2.expression = sXpr;
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(oFormulaElement2, oXpr);
    assert.equal("cds.Decimal", oFormulaElement2.dataType, "formula element data type, ok");

    let oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);
    assert.deepEqual(
      oCsn.definitions.View_1.elements.Quantity["@Consumption.hidden"],
      true,
      "Consumption hidden for measure is ok"
    );
    assert.deepEqual(
      oCsn.definitions.View_1.elements.attr_2["@Analytics.hidden"],
      true,
      "Analytics hidden for measure is ok"
    );
    assert.deepEqual(
      oCsn.definitions.View_1.elements.measure_2["@Analytics.hidden"],
      true,
      "Analytics hidden for measure is ok"
    );

    // If set to visible then both annotations should be undefined
    oOutput.elements.get(1).isVisible = true;
    oOutput.elements.get(3).isVisible = true;
    oOutput.elements.get(4).isVisible = true;
    oCsn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnTest", oNewModel);

    assert.deepEqual(
      oCsn.definitions.View_1.elements.Quantity["@Consumption.hidden"],
      undefined,
      "Consumption hidden undefined for measure is ok"
    );
    assert.deepEqual(
      oCsn.definitions.View_1.elements.attr_2["@Analytics.hidden"],
      undefined,
      "Analytics hidden undefined for measure is ok"
    );
    assert.deepEqual(
      oCsn.definitions.View_1.elements.measure_2["@Analytics.hidden"],
      undefined,
      "Analytics hidden undefined for measure is ok"
    );
  });

  it("DW13-2393: Test filter latest CD6 version, Entity1 -> Filter -> output", async () => {
    const oNewModel = createNewModelAndDiagrams();
    assert.notEqual(undefined, oNewModel, "New model created");
    // assert.equal(2, oNewModel.diagrams.length, "Query builder and association diagrams created");

    const oEntity1 = createEntity(oNewModel, "Entity 1", 4);
    const oClass = sap.galilei.model.getClass("sap.cdw.querybuilder.Element");
    const oElement = oClass.create(oNewModel.resource, {
      name: "Elt1",
    });
    oEntity1.elements.push(oElement);
    const oElement2 = oClass.create(oNewModel.resource, {
      name: "Elt2",
    });
    oEntity1.elements.push(oElement2);

    // emulate drop entity on diagram
    onPostCreateEntity(oEntity1);

    createIntermediateOperation(oNewModel, "Filter", oEntity1);

    const FilterNode = oEntity1.successorNode;

    FilterNode.name = "Filter1";
    Functions.prepareAllFunctions();
    // since for unit tests we cannot call backend service to parse
    // expressions and our aim is not to validate parser. we give the
    // expected parsed expresion also.
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(FilterNode, {
      xpr: [
        {
          ref: ["Elt1"],
        },
        "in",
        {
          list: [
            {
              val: "Elt1",
            },
            {
              val: "Elt2",
            },
          ],
        },
      ],
    });
    assert.equal(FilterNode.aggregatedValidations.validations.length, 0, "Filter validation ok");
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(FilterNode, {
      xpr: [
        {
          ref: ["Elt1"],
        },
        "in",
        {
          list: [
            {
              val: "Elt1",
            },
            {
              val: "Elt2",
            },
          ],
        },
      ],
    });
    assert.equal(FilterNode.aggregatedValidations.validations.length, 0, "Filter validation ok");
  });

  it("DW101-22558 Restricted Measure Expression", async function () {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel._unitTesting = true;
    const oOutput = oNewModel.output;
    const oProduct = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Entity",
      { name: "Product" },
      oNewModel
    );
    const oProductId = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "ProductId", dataType: "cds.String", length: 16 },
      oProduct
    );
    const oQuantity = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.querybuilder.Element",
      { name: "Quantity", dataType: "cds.Integer", length: 10 },
      oProduct
    );

    oProduct.successorNode = oOutput;
    oOutput.dataCategory = DataCategory.FACT;
    SupportedFeaturesService.getInstance().isAnalyticMeasureFormulaEnabled = () => true;
    SupportedFeaturesService.getInstance().isDataActivationEnabled = () => true;
    // Set Quantity to measure
    oQuantity.successorElement.isMeasure = true;

    const oRestrictedMeasure = nsQueryBuilder.NodeImpl.createRestrictedMeasure(oOutput);

    const oCsnElement = {};
    csnUtils.isMeasureWriteCsn(oRestrictedMeasure, oCsnElement);
    // Set basic measure, expression and write csn
    oRestrictedMeasure.sourceMeasure = oQuantity.successorElement;
    oRestrictedMeasure.defaultAggregation = "AVG";
    const oClass = sap.galilei.model.getClass("sap.cdw.commonmodel.AnalyticParameter");

    const analyticParameter = oClass.create(oOutput.resource, { name: "P1" });
    if (analyticParameter) {
      oOutput.analyticParameters.push(analyticParameter);
    }
    sap.cdw.commonmodel.ObjectImpl.setAnalyticParameterReferenceElementFromName(analyticParameter, "ProductId");
    oRestrictedMeasure.expression = "ProductId IN (:P1, 'FR')";
    const srcParsedExpressionWithParam = {
      xpr: [{ ref: ["ProductId"] }, "in", { list: [{ ref: ["P1"], param: true }, { val: "FR" }] }],
    };
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(
      oRestrictedMeasure,
      srcParsedExpressionWithParam
    );
    await oOutput.validate();
    const validations = oOutput.aggregatedValidations?.validations;
    let aErrors = [];
    oOutput.aggregatedValidations.validations.forEach((a) => {
      aErrors.push(a.message.id);
    });
    assert.strictEqual(aErrors.toString(), "VAL_ADS_DEPRECATED_WARN", "No validation error ");
  });

  it("DW101-38301 Cannot deploy a complex view", async function () {
    const loadingData = {
      csn: DW10138301,
      query: DW10138301.definitions.UnionViewShowCase1,
      file: { name: "UnionViewShowCase1" },
    };
    const oModel = sap.cdw.querybuilder.ModelImpl.getOrCreateModel("UnionViewShowCase1", loadingData).model;
    const csn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnOut", oModel, {});
    assert.equal(
      JSON.stringify(csn.definitions.UnionViewShowCase1.query.SELECT.from.SET.args[1]),
      '{"SELECT":{"from":{"SET":{"op":"union","all":true,"args":[{"SELECT":{"from":{"ref":["sTable3ForUnion"]},"columns":[{"as":"Column_11","ref":["sTable3ForUnion","Column_31"]},{"as":"Column_12","ref":["sTable3ForUnion","Column_32"]},{"as":"Column_13","ref":["sTable3ForUnion","Column_33"]},{"as":"Column_14","ref":["sTable3ForUnion","Column_34"]}]}},{"SELECT":{"from":{"ref":["sTableForUnion1"]},"columns":[{"ref":["sTableForUnion1","Column_11"]},{"ref":["sTableForUnion1","Column_12"]},{"ref":["sTableForUnion1","Column_13"]},{"ref":["sTableForUnion1","Column_14"]}]}}]},"as":"Union 2"},"columns":[{"ref":["Union 2","Column_11"],"as":"Column_11"},{"ref":["Union 2","Column_12"],"as":"Column_12"},{"ref":["Union 2","Column_13"],"as":"Column_13"},{"ref":["Union 2","Column_14"],"as":"Column_14"},{"val":null,"as":"Column_22"},{"val":null,"as":"Column_23"},{"val":null,"as":"Column_24"},{"val":null,"as":"Column_25"}]}}'
    );
  });

  it("DW101-42650,DW101-42446 Missing or invalid association annotations", async function () {
    const associationIssues: any = AssociationAnnotationIssues;
    const loadingData = {
      csn: associationIssues,
      query: associationIssues.definitions.FACT_Retailer_Sales_Stock,
      file: { name: "FACT_Retailer_Sales_Stock" },
    };
    const oModel = sap.cdw.querybuilder.ModelImpl.getOrCreateModel("FACT_Retailer_Sales_Stock", loadingData).model;
    // Repair output
    // csnUtils.repairAssociationAnnotations(oModel.output);
    const expectedCsn =
      '{"@EndUserText.label":"Sales Organization","type":"cds.String","length":255,"key":true,"notNull":true}';
    const csn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnOut", oModel, {});
    assert.equal(
      JSON.stringify(csn.definitions.FACT_Retailer_Sales_Stock.elements.Sales_Organization),
      expectedCsn,
      "CSN repaired for Sales_Organization element"
    );
  });

  it("DW101-46636 Customer Incident: Wrong columns order in nested Unions", async function () {
    const twoUnionIssue: any = TwoUnionIssue;
    const loadingData = {
      csn: twoUnionIssue,
      query: twoUnionIssue.definitions.TWO_UNION_VIEW,
      file: { name: "TWO_UNION_VIEW" },
    };
    const oModel = sap.cdw.querybuilder.ModelImpl.getOrCreateModel("TWO_UNION_VIEW", loadingData).model;
    const csn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnOut", oModel, {});
    const query = csn.definitions.TWO_UNION_VIEW.query;
    const uc0 = query.SELECT.from.SET.args[0].SELECT.columns;
    const uc1 = query.SELECT.from.SET.args[1].SELECT.columns;
    const uc10 = query.SELECT.from.SET.args[1].SELECT.from.SET.args[0].SELECT.columns;
    const uc11 = query.SELECT.from.SET.args[1].SELECT.from.SET.args[1].SELECT.columns;
    // check all union columns comparing entity columns (uc0) and sub-union columns (uc1)
    const columnName = function (columnCSN) {
      return columnCSN.as || columnCSN.ref[columnCSN.ref.length - 1];
    };
    for (let iCol = 0; iCol < uc10.length; iCol++) {
      assert.equal(
        columnName(uc10[iCol]),
        columnName(uc11[iCol]),
        "Checking sub-union column name for index " + iCol.toString()
      );
    }
    for (let iCol = 0; iCol < uc0.length; iCol++) {
      assert.equal(
        columnName(uc1[iCol]),
        columnName(uc0[iCol]),
        "Checking main union column name for index " + iCol.toString()
      );
    }
  });

  it("DW101-69852 Customer Incident: Wrong Union ordering because of different aliases", async function () {
    const differentUnionAliasesView: any = DifferentUnionAliasesView;
    const loadingData = {
      csn: differentUnionAliasesView,
      query: differentUnionAliasesView.definitions.GV_RD_ACT_EXPN,
      file: { name: "GV_RD_ACT_EXPN" },
    };
    const oModel = sap.cdw.querybuilder.ModelImpl.getOrCreateModel("GV_RD_ACT_EXPN", loadingData).model;
    const csn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnOut", oModel, {});
    const query = csn.definitions.GV_RD_ACT_EXPN.query;
    const uc0 = query.SET.args[0].SELECT.columns;
    const uc1 = query.SET.args[1].SELECT.columns;
    // check all union columns comparing both predecessors uc0 and uc1
    const columnName = function (columnCSN) {
      return columnCSN.as || columnCSN.ref[columnCSN.ref.length - 1];
    };
    for (let iCol = 0; iCol < uc0.length; iCol++) {
      assert.equal(
        columnName(uc1[iCol]),
        columnName(uc0[iCol]),
        "Checking main union column name for index " + iCol.toString()
      );
    }
  });

  it("DW101-70178 Customer Incident: Duplicate element id for a UNION case", async function () {
    const duplicateElementUnionView: any = DuplicateElementUnionView;
    const loadingData = {
      csn: duplicateElementUnionView,
      query: duplicateElementUnionView.definitions.VR_DIM_WBSElement_SAP_debug,
      file: { name: "VR_DIM_WBSElement_SAP_debug" },
    };
    const oModel = sap.cdw.querybuilder.ModelImpl.getOrCreateModel("VR_DIM_WBSElement_SAP_debug", loadingData).model;
    const csn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnOut", oModel, {});
    const query = csn.definitions.VR_DIM_WBSElement_SAP_debug.query;
    const uc0 = query.SET.args[0].SELECT.columns;
    const uc1 = query.SET.args[1].SELECT.columns;
    // check all union columns comparing both predecessors uc0 and uc1
    const columnName = function (columnCSN) {
      return columnCSN.as || columnCSN.ref[columnCSN.ref.length - 1];
    };
    for (let iCol = 0; iCol < uc0.length; iCol++) {
      assert.equal(
        columnName(uc1[iCol]),
        columnName(uc0[iCol]),
        "Checking main union column name for index " + iCol.toString()
      );
    }
  });

  it("DW101-32233 unexpected error show when data preview on union node 'number of columns mismatch: different number of columns in set clause such as UNION, INTERSECT and EXCEPT: line 1 col 1135", async function () {
    SupportedFeaturesService.getInstance().isColumnValueHelpGVEnabled = () => true;
    const loadingData = { csn: DW10132233, query: DW10132233.definitions.deploy_union, file: { name: "deploy_union" } };
    const oModel = sap.cdw.querybuilder.ModelImpl.getOrCreateModel("deploy_union", loadingData).model;
    const csn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnOut", oModel, {});
    assert.deepEqual(csn.definitions.deploy_union.kind, "entity");
  });

  it("DW101-34743 Expression lost for aggregated, then calculated element", async function () {
    const loadingData = { csn: DW10134743, query: DW10134743.definitions.aggBugView, file: { name: "aggBugView" } };
    const oModel = sap.cdw.querybuilder.ModelImpl.getOrCreateModel("aggBugView", loadingData).model;
    // Set expression to aggregated element propagated to the calculated node
    const calcNode = oModel.nodes.get(3);
    assert.deepEqual(calcNode.classDefinition.name, "CalculatedElements");
    const numCol = calcNode.elements.get(2);
    assert.deepEqual(numCol.name, "numCol");
    // Verify numCol is aggregated
    assert.deepEqual(numCol.isAggregated, true);
    // Set expression and parsed expression (needed in unit test!)
    numCol.expression = "numCol + 1";
    const srcParsedExpression = { xpr: [{ ref: ["numCol"] }, "+", { val: 1 }] };
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(numCol, srcParsedExpression);
    // get generated CSN
    const csn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnOut", oModel, {});
    // Check the expression is present in the aggregated AND calculated column numCol
    assert.deepEqual(csn.definitions.aggBugView.query.SELECT.columns[2].as, "numCol");
    assert.deepEqual(csn.definitions.aggBugView.query.SELECT.columns[2].xpr.length, 3);
  });

  it("DW101-36763 References used in window Aggregation Functions not handled", async function () {
    const loadingData = { csn: **********, query: **********.definitions.winFuncView, file: { name: "winFuncView" } };
    const oModel = sap.cdw.querybuilder.ModelImpl.getOrCreateModel("winFuncView", loadingData).model;
    // Set expression to aggregated element propagated to the calculated node
    const calcNode = oModel.nodes.get(2);
    assert.deepEqual(calcNode.classDefinition.name, "CalculatedElements");
    const amountCol = calcNode.elements.get(2);
    assert.deepEqual(amountCol.name, "amount");

    // First case: Only Window functions used
    // get winFunc calculated columns
    const winFunCalc1 = calcNode.elements.get(5);
    assert.deepEqual(winFunCalc1.newName, "winFunCalc1");
    const winFuncXpr1 = {
      func: "CUME_DIST",
      args: [],
      xpr: ["over", { xpr: ["order", "by", { ref: ["intKey"] }, "asc"] }],
    };
    winFunCalc1.expression = "CUME_DIST() OVER (ORDER BY intKey ASC)";
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(winFunCalc1, winFuncXpr1);
    const winFunCalc2 = calcNode.elements.get(7);
    assert.deepEqual(winFunCalc2.newName, "winFunCalc2");
    winFunCalc2.expression = "PERCENT_RANK() OVER (PARTITION BY code ORDER BY bigInteger)";
    const winFuncXpr2 = {
      func: "PERCENT_RANK",
      args: [],
      xpr: ["over", { xpr: ["partition", "by", { ref: ["code"] }, "order", "by", { ref: ["bigInteger"] }] }],
    };
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(winFunCalc2, winFuncXpr2);
    // get generated CSN and Check No group by clause!
    let csn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnOut", oModel, {});
    assert.deepEqual(
      csn.definitions.winFuncView.query.SELECT.groupBy,
      undefined,
      "No group by clause when only window Functions"
    );

    // Second case: Window AND aggregation functions
    // set aggregation (assuming fix of DW101-38333)
    const amountXpr = { xpr: [{ func: "SUM", args: [{ ref: ["amount"] }] }, "+", { val: 1 }] };
    amountCol.expression = "SUM(amount)+1";
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(amountCol, amountXpr);
    // get generated CSN and Check group by clause contains right number of members
    csn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnOut", oModel, {});
    assert.deepEqual(
      csn.definitions.winFuncView.query.SELECT.groupBy.length,
      5,
      "Group by clause is defined and includes Window Function references"
    );

    // Special case: Add aggregated column to window function references and check!
    winFunCalc1.expression = "CUME_DIST() OVER (ORDER BY amount ASC)";
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(winFunCalc1, {
      func: "CUME_DIST",
      args: [],
      xpr: ["over", { xpr: ["order", "by", { ref: ["amount"] }, "asc"] }],
    });
    // get generated CSN and Check group by clause members increased !
    csn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnOut", oModel, {});
    assert.deepEqual(
      csn.definitions.winFuncView.query.SELECT.groupBy.length,
      6,
      "Group by clause is defined and includes Window Function references AND aggregate column!"
    );

    // Other special case: Change aggregation function and use it with window function predicates => Group by clause should not be generated!
    const newAmountXpr = {
      func: "NTH_VALUE",
      args: [{ ref: ["amount"] }, { val: 4 }],
      xpr: ["over", { xpr: ["partition", "by", { ref: ["bigInteger"] }, "order", "by", { ref: ["intKey"] }] }],
    };
    amountCol.expression = "NTH_VALUE(amount, 4) OVER (PARTITION BY bigInteger ORDER BY intKey)";
    await nsQueryBuilder.ViewModelToCsn._createAndEvaluateParsedExpression(amountCol, newAmountXpr);
    // get generated CSN and Check group by clause contains right number of members
    csn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnOut", oModel, {});
    assert.deepEqual(
      csn.definitions.winFuncView.query.SELECT.groupBy,
      undefined,
      "No group by clause when using Aggregate Function as Window Function"
    );
  });

  it("DW00-8170 Release Contracts", async function () {
    const loadingData = { csn: **********, query: **********.definitions.winFuncView, file: { name: "winFuncView" } };
    const oModel = sap.cdw.querybuilder.ModelImpl.getOrCreateModel("winFuncView", loadingData).model;
    const output = oModel.output;
    // Set Release state FF and value
    const savedFF = SupportedFeaturesService.getInstance().isCompatibilityContractsEnabled();
    SupportedFeaturesService.getInstance().isCompatibilityContractsEnabled = () => true;
    // Set View as RELEASED
    output.releaseState = "RELEASED";
    const csn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnOut", oModel, {});
    assert.deepEqual(
      csn.releaseContractDefinitions?.winFuncView?.releaseState?.["#"],
      "RELEASED",
      "Release state value set to RELEASED"
    );

    // Check Validation..
    // 1- Change view label and check with coast guard
    output.label = "win func view";

    output.repositoryCSN = csn;
    output.releaseContractCSN = csn.releaseContractDefinitions?.winFuncView;
    CoastGuardValidator.getValidatorI18nProvider = () => Promise.resolve({ getText: (k) => k });
    const result1 = await CoastGuardValidator.instance().validateReleaseContract(output);
    assert.strictEqual(result1?.errors?.length, 1, "One Coast guard validation error! ");
    assert.strictEqual(result1?.errors?.[0].rule, validationRules.CHANGE_ANNOTATION, "Change annotation error");
    // restore view label
    output.label = "winFuncView";

    // 2- Set to DEPRECATED without successor Object
    output.releaseState = "DEPRECATED";
    // Validate using CoastGuard
    const result3 = await CoastGuardValidator.instance().validateReleaseContract(output);
    assert.strictEqual(result3?.errors?.length, 2, "Two Coast guard validation errors! ");
    assert.strictEqual(result3?.errors?.[0].rule, validationRules.VALID_CONTRACT_DATES, "Not allowed to deprecate yet");
    assert.strictEqual(
      result3?.errors?.[1].rule,
      validationRules.VALID_RELEASE_STATE_TRANSITION,
      "Invalid transition as missing successor"
    );
    // Check validations are external
    await output.validate();
    assert.strictEqual(output.aggregatedValidations?.validations[0].isExternal, true, "External error");

    SupportedFeaturesService.getInstance().isCompatibilityContractsEnabled = () => savedFF;
  });

  it("Release Contracts Check validation response", async function () {
    const oldCsn = {
      definitions: {
        releaseEx1: {
          kind: "entity",
          elements: {
            "Item name": {
              "@EndUserText.label": "Item name",
              type: "cds.String",
              length: 5000,
            },
            Price: {
              "@EndUserText.label": "Price",
              type: "cds.String",
              length: 5000,
            },
            Description: {
              "@EndUserText.label": "Description",
              type: "cds.String",
              length: 5000,
            },
          },
          query: {
            SELECT: {
              from: {
                ref: ["Test4"],
              },
              columns: [
                {
                  ref: ["Item name"],
                },
                {
                  ref: ["Price"],
                },
                {
                  ref: ["Description"],
                },
              ],
            },
          },
          "@EndUserText.label": "releaseEx1",
          "@ObjectModel.modelingPattern": {
            "#": "DATA_STRUCTURE",
          },
          "@ObjectModel.supportedCapabilities": [
            {
              "#": "DATA_STRUCTURE",
            },
          ],
          "@DataWarehouse.consumption.external": false,
          _meta: {
            dependencies: {
              folderAssignment: null,
            },
          },
        },
      },
      releaseContractDefinitions: {
        releaseEx1: {
          releaseContract: {
            "#": "C1",
          },
          releaseState: {
            "#": "RELEASED",
          },
          releaseDate: "2024-02-09T09:58:13.584Z",
        },
      },
    };
    const newCsn = {
      definitions: {
        releaseEx1: {
          kind: "entity",
          elements: {
            "Item name": {
              "@EndUserText.label": "Item name",
              type: "cds.String",
              length: 5000,
            },
            Price: {
              "@EndUserText.label": "Price",
              type: "cds.String",
              length: 5000,
            },
            Description: {
              "@EndUserText.label": "Description",
              type: "cds.String",
              length: 5000,
            },
          },
          query: {
            SELECT: {
              from: {
                ref: ["Test4"],
              },
              columns: [
                {
                  ref: ["Item name"],
                },
                {
                  ref: ["Price"],
                },
                {
                  ref: ["Description"],
                },
              ],
            },
          },
          "@EndUserText.label": "releaseEx1",
          "@ObjectModel.modelingPattern": {
            "#": "DATA_STRUCTURE",
          },
          "@ObjectModel.supportedCapabilities": [
            {
              "#": "DATA_STRUCTURE",
            },
          ],
          "@DataWarehouse.consumption.external": false,
          _meta: {
            dependencies: {
              folderAssignment: null,
            },
          },
        },
      },
    };
    const newCsn1 = {
      definitions: {
        releaseEx1: {
          kind: "entity",
          elements: {
            "Item name": {
              "@EndUserText.label": "Item name",
              type: "cds.String",
              length: 5000,
            },
            Price: {
              "@EndUserText.label": "Price",
              type: "cds.String",
              length: 5000,
            },
            Description: {
              "@EndUserText.label": "Description",
              type: "cds.String",
              length: 5000,
            },
          },
          query: {
            SELECT: {
              from: {
                ref: ["Test4"],
              },
              columns: [
                {
                  ref: ["Item name"],
                },
                {
                  ref: ["Price"],
                },
                {
                  ref: ["Description"],
                },
              ],
            },
          },
          "@EndUserText.label": "releaseEx1",
          "@ObjectModel.modelingPattern": {
            "#": "DATA_STRUCTURE",
          },
          "@ObjectModel.supportedCapabilities": [
            {
              "#": "DATA_STRUCTURE",
            },
          ],
          "@DataWarehouse.consumption.external": false,
          _meta: {
            dependencies: {
              folderAssignment: null,
            },
          },
        },
      },
      releaseContractDefinitions: {
        releaseEx1: {
          releaseContract: {
            "#": "C1",
          },
          releaseState: {
            "#": "DEPRECATED",
          },
          releaseDate: "2024-02-09T09:58:13.584Z",
        },
      },
    };
    SupportedFeaturesService.getInstance().isCompatibilityContractsEnabled = () => true;
    const result1 = await CoastGuardValidator.instance().checkReleaseContract(newCsn, oldCsn, true);
    const result2 = await CoastGuardValidator.instance().checkReleaseContract(newCsn, oldCsn, false);
    assert.deepEqual(result1.errors.length, 0);
    assert.deepEqual(result1.warnings.length, 1);
    assert.deepEqual(
      result1.warnings[0].text,
      "COMPATIBILITY_CONTRACTS_STATE_CHANGE_NOT_ALLOWED",
      "Release state change is warning when backward transition is enabled"
    );
    assert.deepEqual(
      result2.errors[0].text,
      "COMPATIBILITY_CONTRACTS_STATE_CHANGE_NOT_ALLOWED",
      "Release state change is error when backward transition is disabled"
    );
    const resultAdmin = await CoastGuardValidator.instance().checkReleaseContract(newCsn1, oldCsn, true);
    const resultNotAdmin = await CoastGuardValidator.instance().checkReleaseContract(newCsn1, oldCsn, false);
    assert.deepEqual(
      resultAdmin.errors[0].text,
      "COMPATIBILITY_CONTRACTS_STATE_MISSING_SUCCESSOR",
      "Successor element must be added when backwardtransition enabled"
    );
    assert.deepEqual(
      resultNotAdmin.errors[0].text,
      "COMPATIBILITY_CONTRACTS_STATE_MISSING_SUCCESSOR",
      "Successor element must be added when backwardtransition disabled"
    );
  });

  it("DW101-68553 Release Contract Validation", async function () {
    const loadingData = { csn: **********, query: **********.definitions.winFuncView, file: { name: "winFuncView" } };
    const oModel = sap.cdw.querybuilder.ModelImpl.getOrCreateModel("winFuncView", loadingData).model;
    const output = oModel.output;
    // Set Release state FF and value
    const savedFF = SupportedFeaturesService.getInstance().isCompatibilityContractsEnabled();
    SupportedFeaturesService.getInstance().isCompatibilityContractsEnabled = () => true;
    // Set View as RELEASED
    output.releaseState = "RELEASED";
    const csn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnOut", oModel, {});
    assert.deepEqual(
      csn.releaseContractDefinitions?.winFuncView?.releaseState?.["#"],
      "RELEASED",
      "Release state value set to RELEASED"
    );

    // Check Validation after renaming output element (with emty csn object)
    output.csn = {};
    // Rename element and check with coast guard
    output.elements.get(0).newName = "renamed";

    output.repositoryCSN = csn;
    output.releaseContractCSN = csn.releaseContractDefinitions?.winFuncView;
    CoastGuardValidator.getValidatorI18nProvider = () => Promise.resolve({ getText: (k) => k });
    const result = await CoastGuardValidator.instance().validateReleaseContract(output);
    assert.strictEqual(result?.errors?.length, 1, "One Coast guard validation error! ");
    assert.strictEqual(
      result?.errors?.[0].rule,
      validationRules.ELEMENT_RENAME_OR_REMOVE,
      "Element rename or remove error"
    );
    SupportedFeaturesService.getInstance().isCompatibilityContractsEnabled = () => savedFF;
  });

  it("DW101-66962 Update Release contract Info for Shared View", async function () {
    const grWithSharedView: any = GraphicalViewWithSharedView;
    const loadingData = {
      csn: grWithSharedView,
      query: grWithSharedView.definitions.grWIthSharedSource,
      file: { name: "grWIthSharedSource" },
    };
    const oModel = sap.cdw.querybuilder.ModelImpl.getOrCreateModel("grWIthSharedSource", loadingData).model;
    const sharedView = oModel.nodes.get(2);
    // Check wrong data before updating
    assert.equal(sharedView.name, "I050650_CONTRACTS.BookSqlView", "Checking Shared view name");
    assert.equal(sharedView.releaseState, "NOTRELEASED", "Checking shared view wrong state before update");
    // Update From fileObject
    const fileObject = {
      spaceName: "I050650_CONTRACTS",
      name: "BookSqlView",
      csn: {
        releaseContractDefinitions: {
          BookSqlView: loadingData.csn.releaseContractDefinitions["BookSqlView"],
        },
      },
    };
    // Set Release state FF and value
    const savedFF = SupportedFeaturesService.getInstance().isCompatibilityContractsEnabled();
    SupportedFeaturesService.getInstance().isCompatibilityContractsEnabled = () => true;
    updateReleaseContractFromObjectFile(sharedView, fileObject);
    assert.equal(sharedView.releaseState, "RELEASED", "Checking shared view right state after update");

    // Decommissioning date should be empty even if set when not relevant
    sharedView.releaseContractCSN = {
      releaseContract: {
        "#": "C1",
      },
      releaseState: {
        "#": "DECOMMISSIONED",
      },
      successorObjects: [{ "=": "successor" }],
      releaseDate: "2022-04-10T15:05:31.363Z",
      deprecationDate: "2023-05-15T15:05:31.363Z",
      decommissioningDate: "2024-06-20T15:05:31.363Z",
    };
    const releaseDate = sharedView.releaseDate;
    const dKomDate = sharedView.decommissioningDate;
    assert.equal(releaseDate, "2022-04-10T15:05:31.363Z", "Check Release Date is OK");
    assert.equal(dKomDate, undefined, "Undefined decommissioning date if release state changed backward");

    SupportedFeaturesService.getInstance().isCompatibilityContractsEnabled = () => savedFF;

    // DW101-73581 Check revertable object statuses
    const revertableObjectStatusArray = toDeployedRevertableObjectStatus();
    // Check changes to deploy string value
    assert.equal(revertableObjectStatusArray.includes("2"), true, "Check Changes to Deploy status");
  });

  it("Coast Guard Release Contracts validation checks", async function () {
    const BookGViewCsn = {
      definitions: {
        BookGView: {
          kind: "entity",
          elements: {
            bookID: {
              "@EndUserText.label": "Book ID",
              type: "cds.Integer",
              key: true,
              notNull: true,
            },
            bookName: {
              "@EndUserText.label": "Book Name",
              type: "cds.String",
              length: 100,
            },
            bookAuthor: {
              "@EndUserText.label": "Book Author",
              type: "cds.Integer",
              "@ObjectModel.foreignKey.association": {
                "=": "_Author",
              },
            },
            _Author: {
              type: "cds.Association",
              "@EndUserText.label": "BookGView to Author",
              on: [
                {
                  ref: ["bookAuthor"],
                },
                "=",
                {
                  ref: ["_Author", "author_ID"],
                },
              ],
              target: "Author",
            },
          },
          "@ObjectModel.supportedCapabilities": [
            {
              "#": "DATA_STRUCTURE",
            },
          ],
          "@EndUserText.label": "BookGView",
          "@ObjectModel.modelingPattern": {
            "#": "DATA_STRUCTURE",
          },
          "@DataWarehouse.consumption.external": false,
        },
      },
      releaseContractDefinitions: {
        BookGView: {},
      },
    };
    const releasedCsn = {
      releaseContractDefinitions: {
        BookGView: {
          releaseContract: {
            "#": "C1",
          },
          releaseState: {
            "#": "RELEASED",
          },
          releaseDate: "2022-04-10T15:05:31.363Z",
        },
      },
    };
    const deprecatedCsn = {
      releaseContractDefinitions: {
        BookGView: {
          releaseContract: {
            "#": "C1",
          },
          releaseState: {
            "#": "DEPRECATED",
          },
          successorObjects: [{ "=": "successor" }],
          releaseDate: "2022-04-10T15:05:31.363Z",
          deprecationDate: "2023-05-15T15:05:31.363Z",
        },
      },
    };
    const decommissionedCsn = {
      releaseContractDefinitions: {
        BookGView: {
          releaseContract: {
            "#": "C1",
          },
          releaseState: {
            "#": "DECOMMISSIONED",
          },
          successorObjects: [{ "=": "successor" }],
          releaseDate: "2022-04-10T15:05:31.363Z",
          deprecationDate: "2023-05-15T15:05:31.363Z",
          decommissioningDate: "2024-06-20T15:05:31.363Z",
        },
      },
    };
    const savedFF = SupportedFeaturesService.getInstance().isCompatibilityContractsEnabled();
    SupportedFeaturesService.getInstance().isCompatibilityContractsEnabled = () => true;

    const oldCsn: any = BookGViewCsn;
    const newCsn = JSON.parse(JSON.stringify(BookGViewCsn));
    newCsn.definitions.BookGView.elements.bookID.key = false;
    // DW101-75348: Check changes are valid before view is released
    newCsn.releaseContractDefinitions = releasedCsn.releaseContractDefinitions;
    const result = await CoastGuardValidator.instance().checkReleaseContract(newCsn, oldCsn);
    assert.deepEqual(result.errors.length, 0, "");
    assert.deepEqual(result.warnings.length, 0);

    // But should get error if change occurs after view is released
    oldCsn.releaseContractDefinitions = releasedCsn.releaseContractDefinitions;
    const result2 = await CoastGuardValidator.instance().checkReleaseContract(newCsn, oldCsn);
    assert.deepEqual(result2.errors.length, 1, "Key should not be changed for Released View");
    assert.deepEqual(result2.warnings.length, 0);

    // Restore change..
    newCsn.definitions.BookGView.elements.bookID.key = true;

    // Backward transition should return error...
    newCsn.releaseContractDefinitions = deprecatedCsn.releaseContractDefinitions;
    oldCsn.releaseContractDefinitions = decommissionedCsn.releaseContractDefinitions;
    const result3 = await CoastGuardValidator.instance().checkReleaseContract(newCsn, oldCsn);
    assert.deepEqual(result3.errors.length, 1);
    assert.deepEqual(result3.warnings.length, 0);

    // .. But just a warning if allowed...
    const result4 = await CoastGuardValidator.instance().checkReleaseContract(newCsn, oldCsn, true);
    assert.deepEqual(result4.errors.length, 0);
    assert.deepEqual(result4.warnings.length, 1);

    SupportedFeaturesService.getInstance().isCompatibilityContractsEnabled = () => savedFF;
  });

  it("DW101-78095 adding simple type to csn", async () => {
    const oNewModel = nsQueryBuilder.ModelImpl.getOrCreateModel().model;
    oNewModel.name = "csnTest";
    oNewModel._unitTesting = true;
    const oOutput = oNewModel.output;

    const oSimpleType1 = nsQueryBuilder.ModelImpl.createObject(
      "sap.cdw.commonmodel.SimpleType",
      { name: "SimpleType1", dataType: "cds.String", length: 10 },
      oNewModel
    );
    oNewModel.types.push(oSimpleType1);
    const nsCommonModel = sap.cdw.commonmodel;
    nsCommonModel.ObjectImpl.updateObjectBaseType(oSimpleType1 as sap.cdw.commonmodel.SimpleType, undefined, false);

    const oEntity1 = createEntity(oNewModel, "Entity 1", 3);
    const oElement1 = oEntity1.elements.get(0);
    oElement1.dataType = "SimpleType1";
    nsCommonModel.ObjectImpl.attachElementsToBaseTypes(oEntity1);
    onPostCreateEntity(oEntity1);
    const oCsn = {
      definitions: {
        MyQuery: {
          kind: "entity",
          query: {},
          "@Analytics.provider": true,
        },
      },
    };
    const expectedCSN = { kind: "type", "@EndUserText.label": "SimpleType1", type: "cds.String", length: 10 };
    const oViewModelToCsn = nsQueryBuilder.ViewModelToCsn.getInstance();
    await oViewModelToCsn.addSimpleTypes(oCsn.definitions, oNewModel.simpleTypes);
    expect(oCsn.definitions["SimpleType1"]).to.deep.equal(expectedCSN);
  });
});

it("DW101-92955 Run In Analytic Model Issue", async function () {
  const loadingData = {
    csn: DW10192955,
    query: DW10192955.definitions.runInAnalyticModelView,
    file: { name: "runInAnalyticModelView" },
  };
  const oModel = sap.cdw.querybuilder.ModelImpl.getOrCreateModel("runInAnalyticModelView", loadingData).model;
  const output = oModel.output;

  // Set/Ensure isUseOLAPDBHint to true
  output.isAllowConsumption = true;
  output.isUseOLAPDBHint = true;

  // Get light CSN and ensure annotation is correct
  const lightCSN: any = sap.cdw.commonmodel.ModelToCsn.getInstance().getCsnDocumentForEntity(output);
  assert.deepEqual(
    lightCSN.definitions?.runInAnalyticModelView?.["@Consumption.dbHints"],
    ["USE_OLAP_PLAN"],
    "Light CSN OK"
  );

  // Set isUseOLAPDBHint to false, get CSN and ensure annotation is correct
  output.isUseOLAPDBHint = false;
  const csn = await nsQueryBuilder.ViewModelToCsn.getCSN("csnOut", oModel, {});
  assert.deepEqual(
    (csn.definitions?.runInAnalyticModelView?.["@Consumption.dbHints"] || []).length,
    0,
    "Generated CSN OK"
  );

  // DW101-80792 Self-Association default name
  // DS00-625 Check targetInfo shared by old and new ER models
  // Change technical name
  output.technicalName = "changed";
  const association = sap.cdw.ermodeler.ModelImpl.createAssociation(
    "sap.cdw.querybuilder.Association",
    output,
    output,
    oModel
  ) as any;
  assert.deepEqual(association.name, "_changed", "Default self-association name OK");
  assert.deepEqual(association.targetInfo.name, "changed", "name got from targetInfo OK");
});
