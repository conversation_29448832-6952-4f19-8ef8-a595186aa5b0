/** @format */

import { SupportedFeaturesService } from "../../components/commonmodel/api/SupportedFeaturesService";
import { NameUsage } from "../../components/commonui/utility/NameInputValidator";
import { NamingHelper } from "../../components/reuse/utility/NamingHelper";
import { ObjectType, validateName } from "@sap/dwc-name-validator";
const assert = require("assert");

describe("Common utilities tests", () => {
  describe("Common utilities functions", () => {
    it("Test Entity Naming Validation", async function () {
      const length50Name = "123456789A123456789B123456789C123456789D123456789E";
      const length51Name = "123456789A123456789B123456789C123456789D123456789EX";
      const length100Name =
        "123456789A123456789B123456789C123456789D123456789E123456789A123456789B123456789C123456789D123456789E";
      const length101Name =
        "123456789A123456789B123456789C123456789D123456789E123456789A123456789B123456789C123456789D123456789EX";
      const config = {
        skipValidation: false,
        computeDerivedName: true,
        supportIncreasedMaxLength: undefined,
      };
      // First case: Name > 50 and No long names support
      config.supportIncreasedMaxLength = false;
      let validationResult = validateName(config)(ObjectType.Entity, length51Name);
      assert.strictEqual(validationResult.status, "ERROR", "Case1: Name too long!");
      assert.strictEqual(
        validationResult.failedValidations[0].errorCode,
        "nameValidator_NameTooLong",
        "Case1: Check Error code"
      );
      assert.strictEqual(
        validationResult.failedValidations[0].details["maxLen"],
        50,
        "Case1: Check Returned maxLength"
      );
      assert.strictEqual(
        validationResult.derivationResults.derivedName,
        length50Name,
        "Case1: Check Right derived Name"
      );

      // Second case: Name > 50 with long names support
      config.supportIncreasedMaxLength = true;
      validationResult = validateName(config)(ObjectType.Entity, length51Name);
      assert.strictEqual(validationResult.status, "OK", "Case2: Long names supported with right Feature Flag");
      assert.strictEqual(
        validationResult.derivationResults.derivedName,
        length51Name,
        "Case2: Check Right derived Name"
      );

      // Third case: Name > 100 with long names support
      validationResult = validateName(config)(ObjectType.Entity, length101Name);
      assert.strictEqual(validationResult.status, "ERROR", "Case3: Name too long! (even with right FF)");
      assert.strictEqual(
        validationResult.failedValidations[0].errorCode,
        "nameValidator_NameTooLong",
        "Case3: Check Error code"
      );
      assert.strictEqual(
        validationResult.failedValidations[0].details["maxLen"],
        100,
        "Case3: Check Returned maxLength"
      );
      assert.strictEqual(
        validationResult.derivationResults.derivedName,
        length100Name,
        "Case3: Check Right derived Name"
      );
    });
    it("Test ADS Naming Validation", async function () {
      const length50Name = "123456789A123456789B123456789C123456789D123456789E";
      const length51Name = "123456789A123456789B123456789C123456789D123456789EX";
      const length60Name = "123456789A123456789B123456789C123456789A123456789B123456789C";
      const length61Name = "123456789A123456789B123456789C123456789A123456789B123456789CX";
      const config = {
        skipValidation: false,
        computeDerivedName: true,
        supportIncreasedMaxLength: undefined,
      };
      // First case: Name > 50 and No long names support
      config.supportIncreasedMaxLength = false;
      let validationResult = validateName(config)(ObjectType.AnalyticModel, length51Name);
      assert.strictEqual(validationResult.status, "ERROR", "Case1: Name too long!");
      assert.strictEqual(
        validationResult.failedValidations[0].errorCode,
        "nameValidator_NameTooLong",
        "Case1: Check Error code"
      );
      assert.strictEqual(
        validationResult.failedValidations[0].details["maxLen"],
        50,
        "Case1: Check Returned maxLength"
      );
      assert.strictEqual(
        validationResult.derivationResults.derivedName,
        length50Name,
        "Case1: Check Right derived Name"
      );

      // Second case: Name > 50 with long names support
      config.supportIncreasedMaxLength = true;
      validationResult = validateName(config)(ObjectType.AnalyticModel, length51Name);
      assert.strictEqual(validationResult.status, "OK", "Case2: Long names supported with right Feature Flag");
      assert.strictEqual(
        validationResult.derivationResults.derivedName,
        length51Name,
        "Case2: Check Right derived Name"
      );

      // Third case: Name > 60 with long names support
      validationResult = validateName(config)(ObjectType.AnalyticModel, length61Name);
      assert.strictEqual(validationResult.status, "ERROR", "Case3: Name too long! (even with right FF)");
      assert.strictEqual(
        validationResult.failedValidations[0].errorCode,
        "nameValidator_NameTooLong",
        "Case3: Check Error code"
      );
      assert.strictEqual(
        validationResult.failedValidations[0].details["maxLen"],
        60,
        "Case3: Check Returned maxLength"
      );
      assert.strictEqual(
        validationResult.derivationResults.derivedName,
        length60Name,
        "Case3: Check Right derived Name"
      );
    });
    it("Test Internal Hierarchy Naming Validation", async function () {
      const length10Name = "123456789A";
      const length11Name = "123456789AX";
      const length20Name = "123456789A123456789B";
      const length21Name = "123456789A123456789BX";
      const config = {
        skipValidation: false,
        computeDerivedName: true,
        supportIncreasedMaxLength: undefined,
      };
      // First case: Name > 10 and No long names support
      config.supportIncreasedMaxLength = false;
      let validationResult = validateName(config)(ObjectType.InternalHierarchy, length11Name);
      assert.strictEqual(validationResult.status, "ERROR", "Case1: Name too long!");
      assert.strictEqual(
        validationResult.failedValidations[0].errorCode,
        "nameValidator_NameTooLong",
        "Case1: Check Error code"
      );
      assert.strictEqual(
        validationResult.failedValidations[0].details["maxLen"],
        10,
        "Case1: Check Returned maxLength"
      );
      assert.strictEqual(
        validationResult.derivationResults.derivedName,
        length10Name,
        "Case1: Check Right derived Name"
      );

      // Second case: Name > 10 with long names support
      config.supportIncreasedMaxLength = true;
      validationResult = validateName(config)(ObjectType.InternalHierarchy, length11Name);
      assert.strictEqual(validationResult.status, "OK", "Case2: Long names supported with right Feature Flag");
      assert.strictEqual(
        validationResult.derivationResults.derivedName,
        length11Name,
        "Case2: Check Right derived Name"
      );

      // Third case: Name > 20 with long names support
      validationResult = validateName(config)(ObjectType.InternalHierarchy, length21Name);
      assert.strictEqual(validationResult.status, "ERROR", "Case3: Name too long! (even with right FF)");
      assert.strictEqual(
        validationResult.failedValidations[0].errorCode,
        "nameValidator_NameTooLong",
        "Case3: Check Error code"
      );
      assert.strictEqual(
        validationResult.failedValidations[0].details["maxLen"],
        20,
        "Case3: Check Returned maxLength"
      );
      assert.strictEqual(
        validationResult.derivationResults.derivedName,
        length20Name,
        "Case3: Check Right derived Name"
      );
    });
    it("Test Association Naming Validation", async function () {
      const length20Name = "123456789A123456789B";
      const length21Name = "123456789A123456789BX";
      const config = {
        skipValidation: false,
        computeDerivedName: true,
        supportIncreasedMaxLength: undefined,
      };
      // First case: Name > 20 and No long names support
      config.supportIncreasedMaxLength = false;
      let validationResult = validateName(config)(ObjectType.Association, length21Name);
      assert.strictEqual(validationResult.status, "ERROR", "Case1: Name too long!");
      assert.strictEqual(
        validationResult.failedValidations[0].errorCode,
        "nameValidator_NameTooLong",
        "Case1: Check Error code"
      );
      assert.strictEqual(
        validationResult.failedValidations[0].details["maxLen"],
        20,
        "Case1: Check Returned maxLength"
      );
      assert.strictEqual(
        validationResult.derivationResults.derivedName,
        length20Name,
        "Case1: Check Right derived Name"
      );

      // Second case: Name > 20 with long names support
      config.supportIncreasedMaxLength = true;
      validationResult = validateName(config)(ObjectType.Association, length21Name);
      assert.strictEqual(validationResult.status, "ERROR", "Case3: Name too long! (even with right FF)");
      assert.strictEqual(
        validationResult.failedValidations[0].errorCode,
        "nameValidator_NameTooLong",
        "Case2: Check Error code"
      );
      assert.strictEqual(
        validationResult.failedValidations[0].details["maxLen"],
        20,
        "Case3: Check Returned maxLength"
      );
      assert.strictEqual(
        validationResult.derivationResults.derivedName,
        length20Name,
        "Case2: Check Right derived Name"
      );
    });
    it("Test Element Naming Validation", async function () {
      const length30Name = "123456789A123456789B123456789C";
      const length31Name = "123456789A123456789B123456789CX";
      const length100Name =
        "123456789A123456789B123456789C123456789D123456789E123456789A123456789B123456789C123456789D123456789E";
      const length101Name =
        "123456789A123456789B123456789C123456789D123456789E123456789A123456789B123456789C123456789D123456789EX";
      const config = {
        skipValidation: false,
        computeDerivedName: true,
        supportIncreasedMaxLength: undefined,
      };
      // First case: Name > 30 and No long names support
      config.supportIncreasedMaxLength = false;
      let validationResult = validateName(config)(ObjectType.Element, length31Name);
      assert.strictEqual(validationResult.status, "ERROR", "Case1: Name too long!");
      assert.strictEqual(
        validationResult.failedValidations[0].errorCode,
        "nameValidator_NameTooLong",
        "Case1: Check Error code"
      );
      assert.strictEqual(
        validationResult.failedValidations[0].details["maxLen"],
        30,
        "Case1: Check Returned maxLength"
      );
      assert.strictEqual(
        validationResult.derivationResults.derivedName,
        length30Name,
        "Case1: Check Right derived Name"
      );

      // Second case: Name > 30 with long names support
      config.supportIncreasedMaxLength = true;
      validationResult = validateName(config)(ObjectType.Element, length31Name);
      assert.strictEqual(validationResult.status, "OK", "Case2: Long names supported with right Feature Flag");
      assert.strictEqual(
        validationResult.derivationResults.derivedName,
        length31Name,
        "Case2: Check Right derived Name"
      );

      // Third case: Name > 100 with long names support
      validationResult = validateName(config)(ObjectType.Element, length101Name);
      assert.strictEqual(validationResult.status, "ERROR", "Case3: Name too long! (even with right FF)");
      assert.strictEqual(
        validationResult.failedValidations[0].errorCode,
        "nameValidator_NameTooLong",
        "Case3: Check Error code"
      );
      assert.strictEqual(
        validationResult.failedValidations[0].details["maxLen"],
        100,
        "Case3: Check Returned maxLength"
      );
      assert.strictEqual(
        validationResult.derivationResults.derivedName,
        length100Name,
        "Case3: Check Right derived Name"
      );
    });
    it("Test Parameter Naming Validation", async function () {
      const length30Name = "123456789A123456789B123456789C";
      const length31Name = "123456789A123456789B123456789CX";
      const length100Name =
        "123456789A123456789B123456789C123456789D123456789E123456789A123456789B123456789C123456789D123456789E";
      const length101Name =
        "123456789A123456789B123456789C123456789D123456789E123456789A123456789B123456789C123456789D123456789EX";
      const config = {
        skipValidation: false,
        computeDerivedName: true,
        supportIncreasedMaxLength: undefined,
      };
      // First case: Name > 30 and No long names support
      config.supportIncreasedMaxLength = false;
      let validationResult = validateName(config)(ObjectType.Parameter, length31Name);
      assert.strictEqual(validationResult.status, "ERROR", "Case1: Name too long!");
      assert.strictEqual(
        validationResult.failedValidations[0].errorCode,
        "nameValidator_NameTooLong",
        "Case1: Check Error code"
      );
      assert.strictEqual(
        validationResult.failedValidations[0].details["maxLen"],
        30,
        "Case1: Check Returned maxLength"
      );
      assert.strictEqual(
        validationResult.derivationResults.derivedName,
        length30Name,
        "Case1: Check Right derived Name"
      );

      // Second case: Name > 30 with long names support
      config.supportIncreasedMaxLength = true;
      validationResult = validateName(config)(ObjectType.Parameter, length31Name);
      assert.strictEqual(validationResult.status, "OK", "Case2: Long names supported with right Feature Flag");
      assert.strictEqual(
        validationResult.derivationResults.derivedName,
        length31Name,
        "Case2: Check Right derived Name"
      );

      // Third case: Name > 100 with long names support
      validationResult = validateName(config)(ObjectType.Parameter, length101Name);
      assert.strictEqual(validationResult.status, "ERROR", "Case3: Name too long! (even with right FF)");
      assert.strictEqual(
        validationResult.failedValidations[0].errorCode,
        "nameValidator_NameTooLong",
        "Case3: Check Error code"
      );
      assert.strictEqual(
        validationResult.failedValidations[0].details["maxLen"],
        100,
        "Case3: Check Returned maxLength"
      );
      assert.strictEqual(
        validationResult.derivationResults.derivedName,
        length100Name,
        "Case3: Check Right derived Name"
      );
    });
    it("Test IntermediateNode Naming Validation", async function () {
      const length50Name = "123456789A123456789B123456789C123456789D123456789E";
      const length51Name = "123456789A123456789B123456789C123456789D123456789EX";
      const config = {
        skipValidation: false,
        computeDerivedName: true,
        supportIncreasedMaxLength: undefined,
      };
      // First case: Name > 50
      const validationResult = validateName(config)(ObjectType.IntermediateNode, length51Name);
      assert.strictEqual(validationResult.status, "ERROR", "Case1: Name too long!");
      assert.strictEqual(
        validationResult.failedValidations[0].errorCode,
        "nameValidator_NameTooLong",
        "Case1: Check Error code"
      );
      assert.strictEqual(
        validationResult.failedValidations[0].details["maxLen"],
        50,
        "Case1: Check Returned maxLength"
      );
      assert.strictEqual(
        validationResult.derivationResults.derivedName,
        length50Name,
        "Case1: Check Right derived Name"
      );
    });
    it("getTechnicalName() with isDotSupportEnabledFF", function () {
      const nameValidator = NamingHelper.getNameInputValidator();
      let technicalName: string;
      const businessName = "context.entity";
      const usage = NameUsage.entity;
      // eslint-disable-next-line @typescript-eslint/unbound-method
      sap.ui
        .getCore()
        .setModel(new sap.ui.model.json.JSONModel({ results: [{ name: "space1" }, { name: "space2" }] }), "namespaces");
      SupportedFeaturesService.getInstance().isDotSupportEnabledFF = () => true;
      technicalName = nameValidator.deriveTechnicalName(businessName, usage);
      assert.equal(technicalName, "context.entity", "getTechnicalName() can allow dot when feature flag is enable");
      sap.ui.getCore().setModel(new sap.ui.model.json.JSONModel({ results: [] }), "namespaces");
      technicalName = nameValidator.deriveTechnicalName(businessName, usage);
      assert.equal(
        technicalName,
        "contextentity",
        "getTechnicalName() not allow dot when feature flag is enable but no namespace"
      );
      // eslint-disable-next-line @typescript-eslint/unbound-method
      SupportedFeaturesService.getInstance().isDotSupportEnabledFF = () => false;
      technicalName = nameValidator.deriveTechnicalName(businessName, usage);
      assert.equal(technicalName, "contextentity", "getTechnicalName() not allow dot when feature flag is disable");
    });

    it("getTechnicalName() with undefined business name", function () {
      const nameValidator = NamingHelper.getNameInputValidator();
      let businessName;
      const usage = NameUsage.element;
      let technicalName = nameValidator.deriveTechnicalName(businessName, usage);
      assert.equal(technicalName, "", "getTechnicalName() supports undefined as business name");
      businessName = "";
      technicalName = nameValidator.deriveTechnicalName(businessName, usage);
      assert.equal(technicalName, "", "getTechnicalName() supports empty string as business name");
      businessName = "Таблица 12";
      technicalName = nameValidator.deriveTechnicalName(businessName, usage);
      // after name-validator upgrade, the derivator behavior changes and now only special characters are removed
      assert.equal(technicalName, "12", "getTechnicalName() supports special string as business name");
    });

    it("getTechnicalName()", function () {
      const nameValidator = NamingHelper.getNameInputValidator();
      let technicalName: string;
      const businessName = "context.entity";
      const usage = NameUsage.entity;
      // eslint-disable-next-line @typescript-eslint/unbound-method
      SupportedFeaturesService.getInstance().isDotSupportEnabled = () => true;
      technicalName = nameValidator.deriveTechnicalName(businessName, usage);
      assert.equal(technicalName, "context.entity", "getTechnicalName() can allow dot when feature flag is enable");
      // eslint-disable-next-line @typescript-eslint/unbound-method
      SupportedFeaturesService.getInstance().isDotSupportEnabled = () => false;
      technicalName = nameValidator.deriveTechnicalName(businessName, usage);
      assert.equal(technicalName, "contextentity", "getTechnicalName() not allow dot when feature flag is disable");
    });

    it("Should not allow DAC names to start with sequences of dots and underscores", function () {
      const nameValidator = NamingHelper.getNameInputValidator();
      SupportedFeaturesService.getInstance().isDotSupportEnabled = () => true;

      const testCases = [
        {
          input: "___ctx.dac",
          expected: "ctx.dac",
          message: "leading underscores should not be included in derived name",
        },
        {
          input: "...ctx.dac",
          expected: "ctx.dac",
          message: "leading dots should not be included in derived name",
        },
        {
          input: "._..__...___ctx.dac",
          expected: "ctx.dac",
          message: "result must not have leading dots or underscores",
        },
        {
          input: "ctx._dac",
          expected: "ctx.dac",
          message: "the entity name must not start with an underscore even inside a context",
        },
        {
          input: "ctx....dac",
          expected: "ctx.dac",
          message: "multiple dots are not allowed",
        },
        {
          input: "ctx.._.",
          expected: "ctx.",
          message: "invalid partial name cannot be completed to anything valid",
        },
        {
          input: "ctx__.._",
          expected: "ctx__.",
          message: "invalid partial name cannot be completed to anything valid",
        },
      ];

      for (const tc of testCases) {
        const actual = nameValidator.deriveTechnicalName(tc.input, NameUsage.authorization);
        assert.equal(tc.expected, actual, tc.message);
      }
    });

    describe("technicalName error message for invalidName error", () => {
      const usage = NameUsage.authorization;

      beforeEach(function () {
        NamingHelper.resetNameInputValidator();
      });

      before(function () {
        //
      });

      after(function () {
        NamingHelper.resetNameInputValidator();
      });

      it("errMsg should say $ is an invalid character", () => {
        NamingHelper.resetNameInputValidator();
        const nameValidator = NamingHelper.getNameInputValidator();
        const technicalName = "A$$b";
        const derivedTechnicalName = nameValidator.doDeriveTechnicalName(technicalName, usage, {
          skipAllErrors: false,
          skipInvalidError: false,
          skipTrailingErrors: false,
        });
        assert.equal(derivedTechnicalName.validatedName, "Ab");
        assert.equal(derivedTechnicalName._errorMessages[0], "$ is an invalid character.");
      });

      it("errMsg should say space is an invalid character", () => {
        const nameValidator = NamingHelper.getNameInputValidator();
        const technicalName = " A b";
        const space = nameValidator.localizeText("spaceCharacter", []);
        const derivedTechnicalName = nameValidator.doDeriveTechnicalName(technicalName, usage, {
          skipAllErrors: false,
          skipInvalidError: false,
          skipTrailingErrors: false,
        });
        assert.equal(derivedTechnicalName.validatedName, "A_b");
        assert.equal(derivedTechnicalName._errorMessages[0], `(${space}) is an invalid character.`);
      });

      it("errMsg should say $, %, & are invalid characters", () => {
        const nameValidator = NamingHelper.getNameInputValidator();
        const technicalName = "A$%&b";
        const derivedTechnicalName = nameValidator.doDeriveTechnicalName(technicalName, usage, {
          skipAllErrors: false,
          skipInvalidError: false,
          skipTrailingErrors: false,
        });
        assert.equal(derivedTechnicalName.validatedName, "Ab");
        assert.equal(derivedTechnicalName._errorMessages[0], "$, %, & are invalid characters.");
      });

      it("errMsg should say $ is an invalid character and that the name exceeds 50 characters", () => {
        const nameValidator = NamingHelper.getNameInputValidator();
        const technicalName = "Long$Bussiness$Name$That$Has$More$Than$50$Characters";
        const derivedTechnicalName = nameValidator.doDeriveTechnicalName(technicalName, usage, {
          skipAllErrors: false,
          skipInvalidError: false,
          skipTrailingErrors: false,
        });
        assert.equal(derivedTechnicalName.validatedName, "LongBussinessNameThatHasMoreThan50Characters");
        assert.equal(derivedTechnicalName._errorMessages[0], "$ is an invalid character.");
        assert.equal(derivedTechnicalName._errorMessages[1], "The name exceeds the maximum length 50.");
      });

      // Trailing dots test cases
      it("Trailing dots should be allowed while typing (liveChange)", () => {
        const nameValidator = NamingHelper.getNameInputValidator();
        const businessName = "Name.ending.with.dot.";
        // live change case (Skip trailing dots check)
        const liveChangeDerivedTechnicalName = nameValidator.doDeriveTechnicalName(businessName, usage, {
          skipAllErrors: false,
          skipInvalidError: false,
          skipTrailingErrors: true,
        });
        assert.equal(liveChangeDerivedTechnicalName.validatedName, "Name.ending.with.dot.");
        assert.equal(liveChangeDerivedTechnicalName._errorMessages?.length, 0, "No error");
        // live change case (Enforce trailing dots check)
        const submitDerivedTechnicalName = nameValidator.doDeriveTechnicalName(businessName, usage, {
          skipAllErrors: false,
          skipInvalidError: false,
          skipTrailingErrors: false,
        });
        assert.equal(submitDerivedTechnicalName._errorMessages?.length, 1, "One error");
        assert.equal(submitDerivedTechnicalName._errorMessages[0], ". is an invalid character.");
      });
    });
  });
});
