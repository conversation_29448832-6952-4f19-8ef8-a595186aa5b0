/** @format */

import { SupportedFeaturesService } from "../../components/commonmodel/api/SupportedFeaturesService";
import { NameUsage } from "../../components/commonui/utility/NameInputValidator";
import { NamingHelper } from "../../components/reuse/utility/NamingHelper";
const assert = require("assert");

describe("Common utilities tests", () => {
  describe("Common utilities functions", () => {
    it("getTechnicalName() with isDotSupportEnabledFF", function () {
      const nameValidator = NamingHelper.getNameInputValidator();
      let technicalName: string;
      const businessName = "context.entity";
      const usage = NameUsage.entity;
      // eslint-disable-next-line @typescript-eslint/unbound-method
      sap.ui
        .getCore()
        .setModel(new sap.ui.model.json.JSONModel({ results: [{ name: "space1" }, { name: "space2" }] }), "namespaces");
      SupportedFeaturesService.getInstance().isDotSupportEnabledFF = () => true;
      technicalName = nameValidator.deriveTechnicalName(businessName, usage);
      assert.equal(technicalName, "context.entity", "getTechnicalName() can allow dot when feature flag is enable");
      sap.ui.getCore().setModel(new sap.ui.model.json.JSONModel({ results: [] }), "namespaces");
      technicalName = nameValidator.deriveTechnicalName(businessName, usage);
      assert.equal(
        technicalName,
        "contextentity",
        "getTechnicalName() not allow dot when feature flag is enable but no namespace"
      );
      // eslint-disable-next-line @typescript-eslint/unbound-method
      SupportedFeaturesService.getInstance().isDotSupportEnabledFF = () => false;
      technicalName = nameValidator.deriveTechnicalName(businessName, usage);
      assert.equal(technicalName, "contextentity", "getTechnicalName() not allow dot when feature flag is disable");
    });

    it("getTechnicalName() with undefined business name", function () {
      const nameValidator = NamingHelper.getNameInputValidator();
      let businessName;
      const usage = NameUsage.element;
      let technicalName = nameValidator.deriveTechnicalName(businessName, usage);
      assert.equal(technicalName, "", "getTechnicalName() supports undefined as business name");
      businessName = "";
      technicalName = nameValidator.deriveTechnicalName(businessName, usage);
      assert.equal(technicalName, "", "getTechnicalName() supports empty string as business name");
      businessName = "Таблица 12";
      technicalName = nameValidator.deriveTechnicalName(businessName, usage);
      assert.equal(technicalName, "", "getTechnicalName() supports special string as business name");
    });

    it("getTechnicalName()", function () {
      const nameValidator = NamingHelper.getNameInputValidator();
      let technicalName: string;
      const businessName = "context.entity";
      const usage = NameUsage.entity;
      // eslint-disable-next-line @typescript-eslint/unbound-method
      SupportedFeaturesService.getInstance().isDotSupportEnabled = () => true;
      technicalName = nameValidator.deriveTechnicalName(businessName, usage);
      assert.equal(technicalName, "context.entity", "getTechnicalName() can allow dot when feature flag is enable");
      // eslint-disable-next-line @typescript-eslint/unbound-method
      SupportedFeaturesService.getInstance().isDotSupportEnabled = () => false;
      technicalName = nameValidator.deriveTechnicalName(businessName, usage);
      assert.equal(technicalName, "contextentity", "getTechnicalName() not allow dot when feature flag is disable");
    });

    it("Should truncate technicalName to a maximum length of 50 characters and get right error messages", function () {
      const entityUsage = NameUsage.entity;
      const operationNodeUsage = NameUsage.operationNode;
      const longBusinessName = "Very long business name having b;zzar i and more than 50 characters";
      const ch50BusinessName = "Very_long_business_name_having_bzzar_i_and_more_th";
      const ch30BusinessName = "Very_long_business_name_having";

      // Check with central name validation (CV: Central Validation)
      NamingHelper.resetNameInputValidator();
      const nameValidator = NamingHelper.getNameInputValidator();
      let deriveTechnicalNameResult = nameValidator.doDeriveTechnicalName(longBusinessName, entityUsage, {
        skipAllErrors: false,
        skipInvalidError: false,
        skipTrailingErrors: false,
      });
      let technicalName = deriveTechnicalNameResult.validatedName;
      assert.equal(technicalName, ch50BusinessName, "CV: Text should be truncated to 50 characters");
      assert.equal(deriveTechnicalNameResult.errors[0].errorCode, "InvalidName", "CV: Invalid Name error code");
      assert.equal(deriveTechnicalNameResult.maxLength, 50, "CV: Max length of 50 exceeded");

      // DW101-13174 : Operation node should stick to 30 characters limitation even with Entity MaxLength increased
      deriveTechnicalNameResult = nameValidator.doDeriveTechnicalName(longBusinessName, operationNodeUsage, {
        skipAllErrors: false,
        skipInvalidError: false,
        skipTrailingErrors: false,
      });
      technicalName = deriveTechnicalNameResult.validatedName;
      assert.equal(technicalName, ch30BusinessName, "CV: Text should be truncated to 30 characters for operation node");
      assert.equal(deriveTechnicalNameResult.errors[0].errorCode, "InvalidName", "CV: Invalid Name error code");
      assert.equal(deriveTechnicalNameResult.maxLength, 30, "CV: Max length of 30 exceeded");

      // Reset Input validator and restore FF for following tests..
      NamingHelper.resetNameInputValidator();
    });

    it("Should not allow DAC names to start with sequences of dots and underscores", function () {
      const nameValidator = NamingHelper.getNameInputValidator();
      SupportedFeaturesService.getInstance().isDotSupportEnabled = () => true;

      const testCases = [
        {
          input: "___ctx.dac",
          expected: "ctx.dac",
          message: "leading underscores should not be included in derived name",
        },
        {
          input: "...ctx.dac",
          expected: "ctx.dac",
          message: "leading dots should not be included in derived name",
        },
        {
          input: "._..__...___ctx.dac",
          expected: "ctx.dac",
          message: "result must not have leading dots or underscores",
        },
        {
          input: "ctx._dac",
          expected: "ctx.dac",
          message: "the entity name must not start with an underscore even inside a context",
        },
        {
          input: "ctx....dac",
          expected: "ctx.dac",
          message: "multiple dots are not allowed",
        },
        {
          input: "ctx.._.",
          expected: "ctx.",
          message: "invalid partial name cannot be completed to anything valid",
        },
        {
          input: "ctx__.._",
          expected: "ctx__.",
          message: "invalid partial name cannot be completed to anything valid",
        },
      ];

      for (const tc of testCases) {
        const actual = nameValidator.deriveTechnicalName(tc.input, NameUsage.authorization);
        assert.equal(tc.expected, actual, tc.message);
      }
    });

    describe("technicalName error message for invalidName error", () => {
      const usage = NameUsage.authorization;

      beforeEach(function () {
        NamingHelper.resetNameInputValidator();
      });

      before(function () {
        //
      });

      after(function () {
        NamingHelper.resetNameInputValidator();
      });

      it("errMsg should say $ is an invalid character", () => {
        NamingHelper.resetNameInputValidator();
        const nameValidator = NamingHelper.getNameInputValidator();
        const technicalName = "A$$b";
        const derivedTechnicalName = nameValidator.doDeriveTechnicalName(technicalName, usage, {
          skipAllErrors: false,
          skipInvalidError: false,
          skipTrailingErrors: false,
        });
        assert.equal(derivedTechnicalName.validatedName, "Ab");
        assert.equal(derivedTechnicalName._errorMessages[0], "$ is an invalid character.");
      });

      it("errMsg should say space is an invalid character", () => {
        const nameValidator = NamingHelper.getNameInputValidator();
        const technicalName = " A b";
        const space = nameValidator.localizeText("spaceCharacter", []);
        const derivedTechnicalName = nameValidator.doDeriveTechnicalName(technicalName, usage, {
          skipAllErrors: false,
          skipInvalidError: false,
          skipTrailingErrors: false,
        });
        assert.equal(derivedTechnicalName.validatedName, "A_b");
        assert.equal(derivedTechnicalName._errorMessages[0], `(${space}) is an invalid character.`);
      });

      it("errMsg should say $, %, & are invalid characters", () => {
        const nameValidator = NamingHelper.getNameInputValidator();
        const technicalName = "A$%&b";
        const derivedTechnicalName = nameValidator.doDeriveTechnicalName(technicalName, usage, {
          skipAllErrors: false,
          skipInvalidError: false,
          skipTrailingErrors: false,
        });
        assert.equal(derivedTechnicalName.validatedName, "Ab");
        assert.equal(derivedTechnicalName._errorMessages[0], "$, %, & are invalid characters.");
      });

      it("errMsg should say $ is an invalid character and that the name exceeds 50 characters", () => {
        const nameValidator = NamingHelper.getNameInputValidator();
        const technicalName = "Long$Bussiness$Name$That$Has$More$Than$50$Characters";
        const derivedTechnicalName = nameValidator.doDeriveTechnicalName(technicalName, usage, {
          skipAllErrors: false,
          skipInvalidError: false,
          skipTrailingErrors: false,
        });
        assert.equal(derivedTechnicalName.validatedName, "LongBussinessNameThatHasMoreThan50Characters");
        assert.equal(derivedTechnicalName._errorMessages[0], "$ is an invalid character.");
        assert.equal(derivedTechnicalName._errorMessages[1], "The name exceeds the maximum length 50.");
      });

      // Trailing dots test cases
      it("Trailing dots should be allowed while typing (liveChange)", () => {
        const nameValidator = NamingHelper.getNameInputValidator();
        const businessName = "Name.ending.with.dot.";
        // live change case (Skip trailing dots check)
        const liveChangeDerivedTechnicalName = nameValidator.doDeriveTechnicalName(businessName, usage, {
          skipAllErrors: false,
          skipInvalidError: false,
          skipTrailingErrors: true,
        });
        assert.equal(liveChangeDerivedTechnicalName.validatedName, "Name.ending.with.dot.");
        assert.equal(liveChangeDerivedTechnicalName._errorMessages?.length, 0, "No error");
        // live change case (Enforce trailing dots check)
        const submitDerivedTechnicalName = nameValidator.doDeriveTechnicalName(businessName, usage, {
          skipAllErrors: false,
          skipInvalidError: false,
          skipTrailingErrors: false,
        });
        assert.equal(submitDerivedTechnicalName._errorMessages?.length, 1, "One error");
        assert.equal(submitDerivedTechnicalName._errorMessages[0], ". is an invalid character.");
      });
    });
  });
});
