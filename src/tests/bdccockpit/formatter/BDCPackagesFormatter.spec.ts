/** @format */

import { expect } from "chai";
import sinon from "sinon";
import {
  BDCPackageCategory,
  BDCPackageStatus,
  BDCPackageType,
  SourceSystemType,
} from "../../../../shared/bdccockpit/Enums";
import {
  BDCAvailableContent,
  BDCInstalledContent,
  BDCProvider,
  PackageFolder,
} from "../../../../shared/bdccockpit/Types";
import { BaseControllerClass } from "../../../components/basecomponent/controller/BaseController.controller";
import { BDCPackagesFormatter } from "../../../components/bdccockpit/packages/formatter/BDCPackagesFormatter";
import { stubFeatureFlags } from "../common-stubs";
import { eachCase } from "../each-case";

const FEATURE_FLAGS = {
  DWCO_BDC_COCKPIT_FAILURE_DETAILS: true,
  DWCO_BDC_GA: true,
  DWC<PERSON>_BDC_MANUAL_ONBOARDING_RETRY: true,
  DWCO_BDC_MANUAL_OFFBOARDING: true,
};

describe("src/components/bdccockpit/packages/formatter/BDCPackagesFormatter", () => {
  let controllerStub: any;

  beforeEach(() => {
    controllerStub = {
      getText: (text: string, joinArgs: string[]): string => (joinArgs ? `${text}(${joinArgs})` : text),
      byId: (id: string): any => ({ id, getValue: () => "" }),
      formatDateTime: (value: string): string => BaseControllerClass.prototype.formatDateTime(value),
      fetchData: (): any => "",
      initializeNewSpaceDetails: (): any => "",
      copyCorrelationIdToClipboard: () => "copyCorrelationIdToClipboard",
    };
    stubFeatureFlags(sinon, FEATURE_FLAGS);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe("getTitleWithAmount", () => {
    it("should return the correct title with amount", () => {
      const args = ["title1", "title2"];
      const items = [1, 2, 3];
      const expectedResult = "title1 title2 (3)";
      expect(BDCPackagesFormatter.getTitleWithAmount.apply(controllerStub, [...args, items])).to.equal(expectedResult);
    });
  });

  describe("getPackageType", () => {
    eachCase([
      {
        caseName: "should return 'insightApplication' for INSIGHT_APPLICATION type",
        type: BDCPackageType.INSIGHT_APPLICATION,
        expected: "insightApplication",
      },
      {
        caseName: "should return 'dataPackage' for DATA_PACKAGE type",
        type: BDCPackageType.DATA_PACKAGE,
        expected: "dataPackage",
      },
    ]).describe("$caseName", ({ type, expected }) => {
      it(`returns ${expected}`, () => {
        expect(BDCPackagesFormatter.getPackageType.apply(controllerStub, [type])).to.equal(expected);
      });
    });
  });

  describe("getPackageCategory", () => {
    eachCase([
      {
        caseName: "should return 'finance' for FINANCE category",
        category: BDCPackageCategory.FINANCE,
        expected: "finance",
      },
      {
        caseName: "should return 'humanResources' for HUMAN_RESOURCES category",
        category: BDCPackageCategory.HUMAN_RESOURCES,
        expected: "humanResources",
      },
      {
        caseName: "should return 'other' for OTHER category",
        category: BDCPackageCategory.OTHER,
        expected: "other",
      },
      {
        caseName: "should return 'unknown' for unknown category",
        category: "UNKNOWN" as BDCPackageCategory,
        expected: "unknown",
      },
    ]).describe("$caseName", ({ category, expected }) => {
      it(`returns ${expected}`, () => {
        expect(BDCPackagesFormatter.getPackageCategory.apply(controllerStub, [category])).to.equal(expected);
      });
    });
  });

  describe("getPackageSystemType", () => {
    eachCase([
      {
        caseName: "should return 'S4HanaCloud' for S4 system type",
        systemType: SourceSystemType.S4,
        expected: "S4HanaCloud",
      },
      {
        caseName: "should return 'S4HanaCloudPE' for S4PCE system type",
        systemType: SourceSystemType.S4PCE,
        expected: "S4HanaCloudPE",
      },
      {
        caseName: "should return 'Successfactor' for HCM system type",
        systemType: SourceSystemType.HCM,
        expected: "Successfactor",
      },
      {
        caseName: "should return 'unknown' for unknown system type",
        systemType: "UNKNOWN" as SourceSystemType,
        expected: "unknown",
      },
    ]).describe("$caseName", ({ systemType, expected }) => {
      it(`returns ${expected}`, () => {
        expect(BDCPackagesFormatter.getPackageSystemType.apply(controllerStub, [systemType])).to.equal(expected);
      });
    });
  });

  describe("getFormattedDate", () => {
    it("should return a formatted date string", () => {
      const expectedResult = "Jan 1, 1970";
      const result = BDCPackagesFormatter.getFormattedDate.apply(controllerStub, ["0"]);
      expect(result).to.contain(expectedResult);
    });
    it("should return '---'", () => {
      const expectedResult = "---";
      const result = BDCPackagesFormatter.getFormattedDate.apply(controllerStub, [""]);
      expect(result).to.contain(expectedResult);
    });
  });

  describe("getTableObjectStatusText", () => {
    describe("INSTALLED status", () => {
      it('should return "installed"', () => {
        const packageData = {
          status: BDCPackageStatus.INSTALLED,
          type: BDCPackageType.INSIGHT_APPLICATION,
          isPackageUpdateAvailable: false,
        };
        expect(BDCPackagesFormatter.getTableObjectStatusText.apply(controllerStub, [packageData])).to.equal(
          "installed"
        );
      });
      it('should return "active"', () => {
        const packageData = {
          status: BDCPackageStatus.INSTALLED,
        };
        expect(BDCPackagesFormatter.getTableObjectStatusText.apply(controllerStub, [packageData])).to.equal("active");
      });
    });
    describe("INSTALLING status", () => {
      it('should return "installing"', () => {
        const packageData = {
          status: BDCPackageStatus.INSTALLING,
          type: BDCPackageType.INSIGHT_APPLICATION,
          isPackageUpdateAvailable: false,
        };
        expect(BDCPackagesFormatter.getTableObjectStatusText.apply(controllerStub, [packageData])).to.equal(
          "installing"
        );
      });
      it('should return "activating"', () => {
        const packageData = {
          status: BDCPackageStatus.INSTALLING,
        };
        expect(BDCPackagesFormatter.getTableObjectStatusText.apply(controllerStub, [packageData])).to.equal(
          "activating"
        );
      });
    });
    describe("INSTALLATION_FAILED status", () => {
      it('should return "installationFailed"', () => {
        const packageData = {
          status: BDCPackageStatus.INSTALLATION_FAILED,
          type: BDCPackageType.INSIGHT_APPLICATION,
          isPackageUpdateAvailable: false,
        };
        expect(BDCPackagesFormatter.getTableObjectStatusText.apply(controllerStub, [packageData])).to.equal(
          "installationFailed"
        );
      });
      it('should return "activationFailed"', () => {
        const packageData = {
          status: BDCPackageStatus.INSTALLATION_FAILED,
        };
        expect(BDCPackagesFormatter.getTableObjectStatusText.apply(controllerStub, [packageData])).to.equal(
          "activationFailed"
        );
      });
    });

    it('should return "updateAvailable" for update flag', () => {
      const packageData = {
        status: BDCPackageStatus.INSTALLED,
        isPackageUpdateAvailable: true,
      };
      expect(BDCPackagesFormatter.getTableObjectStatusText.apply(controllerStub, [packageData])).to.equal(
        "updateAvailable"
      );
    });
    it('should return "notAvailable" for undefined status', () => {
      expect(BDCPackagesFormatter.getTableObjectStatusText.apply(controllerStub, [{}])).to.equal("notAvailable");
    });
  });

  describe("getTableObjectStatusState", () => {
    it("should return sap.ui.core.ValueState.Success for INSTALLED status with no gracePeriodStart", () => {
      const packageData = { status: BDCPackageStatus.INSTALLED };
      expect(BDCPackagesFormatter.getTableObjectStatusState.apply(controllerStub, [packageData])).to.equal(
        sap.ui.core.ValueState.Success
      );
    });
    it("should return sap.ui.core.ValueState.Success for INSTALLED status with gracePeriodStart", () => {
      const packageData = { status: BDCPackageStatus.INSTALLED, gracePeriodStart: new Date() };
      expect(BDCPackagesFormatter.getTableObjectStatusState.apply(controllerStub, [packageData])).to.equal(
        sap.ui.core.ValueState.Warning
      );
    });
    it("should return sap.ui.core.ValueState.Information for INSTALLING status", () => {
      const packageData = { status: BDCPackageStatus.INSTALLING };
      expect(BDCPackagesFormatter.getTableObjectStatusState.apply(controllerStub, [packageData])).to.equal(
        sap.ui.core.ValueState.Information
      );
    });
    it("should return sap.ui.core.ValueState.Error for INSTALLATION_FAILED status", () => {
      const packageData = { status: BDCPackageStatus.INSTALLATION_FAILED };
      expect(BDCPackagesFormatter.getTableObjectStatusState.apply(controllerStub, [packageData])).to.equal(
        sap.ui.core.ValueState.Error
      );
    });
    it("should return sap.ui.core.ValueState.Information for update flag", () => {
      const packageData = { status: BDCPackageStatus.INSTALLED, isPackageUpdateAvailable: true };
      expect(BDCPackagesFormatter.getTableObjectStatusState.apply(controllerStub, [packageData])).to.equal(
        sap.ui.core.ValueState.Information
      );
    });
    it("should return sap.ui.core.ValueState.Error for undefined status", () => {
      expect(BDCPackagesFormatter.getTableObjectStatusState.apply(controllerStub, [{}])).to.equal(
        sap.ui.core.ValueState.Error
      );
    });
  });

  describe("getTableObjectStatusIcon", () => {
    it('should return "sap-icon://sys-enter-2" for ACTIVE status with no gracePeriodStart', () => {
      const packageData = { status: BDCPackageStatus.INSTALLED };
      expect(BDCPackagesFormatter.getTableObjectStatusIcon.apply(controllerStub, [packageData])).to.equal(
        "sap-icon://sys-enter-2"
      );
    });
    it('should return "sap-icon://alert" for ACTIVE status with gracePeriodStart', () => {
      const packageData = { status: BDCPackageStatus.INSTALLED, gracePeriodStart: new Date() };
      expect(BDCPackagesFormatter.getTableObjectStatusIcon.apply(controllerStub, [packageData])).to.equal(
        "sap-icon://alert"
      );
    });

    it('should return "sap-icon://pending" for INSTALLING status', () => {
      const packageData = { status: BDCPackageStatus.INSTALLING };
      expect(BDCPackagesFormatter.getTableObjectStatusIcon.apply(controllerStub, [packageData])).to.equal(
        "sap-icon://pending"
      );
    });

    it('should return "sap-icon://error" for INSTALLATION_FAILED status', () => {
      const packageData = { status: BDCPackageStatus.INSTALLATION_FAILED };
      expect(BDCPackagesFormatter.getTableObjectStatusIcon.apply(controllerStub, [packageData])).to.equal(
        "sap-icon://error"
      );
    });

    it('should return "sap-icon://future" for update flag', () => {
      const packageData = { status: BDCPackageStatus.INSTALLED, isPackageUpdateAvailable: true };
      expect(BDCPackagesFormatter.getTableObjectStatusIcon.apply(controllerStub, [packageData])).to.equal(
        "sap-icon://future"
      );
    });

    it('should return "sap-icon://error" for undefined status', () => {
      expect(BDCPackagesFormatter.getTableObjectStatusIcon.apply(controllerStub, [{}])).to.equal("sap-icon://error");
    });
  });

  describe("getTableProductObjectStatusIcon", () => {
    eachCase([
      {
        caseName: "should return '' if status is not INSTALLED",
        isActive: true,
        status: BDCPackageStatus.INSTALLING,
        expected: "",
      },
      {
        caseName: "should return 'sap-icon://sys-enter-2' if status is INSTALLED and isActive is true",
        isActive: true,
        status: BDCPackageStatus.INSTALLED,
        expected: "sap-icon://sys-enter-2",
      },
      {
        caseName: "should return 'sap-icon://alert' if status is INSTALLED and isActive is false",
        isActive: false,
        status: BDCPackageStatus.INSTALLED,
        expected: "sap-icon://alert",
      },
    ]).describe("$caseName", ({ isActive, status, expected }) => {
      it(`returns ${expected}`, () => {
        expect(BDCPackagesFormatter.getTableProductObjectStatusIcon.apply(controllerStub, [isActive, status])).to.equal(
          expected
        );
      });
    });
  });

  describe("getTableProductObjectStatusState", () => {
    eachCase([
      {
        caseName: "should return ValueState.Success when isActive is true",
        isActive: true,
        expected: sap.ui.core.ValueState.Success,
      },
      {
        caseName: "should return ValueState.Warning when isActive is false",
        isActive: false,
        expected: sap.ui.core.ValueState.Warning,
      },
    ]).describe("$caseName", ({ isActive, expected }) => {
      it(`returns ${expected}`, () => {
        expect(BDCPackagesFormatter.getTableProductObjectStatusState.apply(controllerStub, [isActive])).to.equal(
          expected
        );
      });
    });
  });

  describe("getActiveStatus", () => {
    eachCase([
      {
        caseName: "should return '' if status is not INSTALLED",
        isActive: true,
        status: BDCPackageStatus.INSTALLING,
        expected: "",
      },
      {
        caseName: "should return 'active' if status is INSTALLED and isActive is true",
        isActive: true,
        status: BDCPackageStatus.INSTALLED,
        expected: "active",
      },
      {
        caseName: "should return 'notActive' if status is INSTALLED and isActive is false",
        isActive: false,
        status: BDCPackageStatus.INSTALLED,
        expected: "notActive",
      },
    ]).describe("$caseName", ({ isActive, status, expected }) => {
      it(`returns ${expected}`, () => {
        expect(BDCPackagesFormatter.getActiveStatus.apply(controllerStub, [isActive, status])).to.equal(expected);
      });
    });
  });

  describe("getPackageGenericTagText", () => {
    it('should return "available" for AVAILABLE status', () => {
      const packageData = { status: BDCPackageStatus.AVAILABLE };
      expect(BDCPackagesFormatter.getPackageGenericTagText.apply(controllerStub, [packageData])).to.equal("available");
    });
    it('should return "active" for update flag', () => {
      const packageData = { status: BDCPackageStatus.INSTALLED, isPackageUpdateAvailable: true };
      expect(BDCPackagesFormatter.getPackageGenericTagText.apply(controllerStub, [packageData])).to.equal("active");
    });
    it('should return "toBeDeactivated" for started grace period', () => {
      const packageData = { status: BDCPackageStatus.INSTALLED, gracePeriodStart: new Date() };
      expect(BDCPackagesFormatter.getPackageGenericTagText.apply(controllerStub, [packageData])).to.equal(
        "toBeDeactivated"
      );
    });
    it('should return "installed" for update flag when insight app', () => {
      const packageData = {
        status: BDCPackageStatus.INSTALLED,
        isPackageUpdateAvailable: true,
        type: BDCPackageType.INSIGHT_APPLICATION,
      };
      expect(BDCPackagesFormatter.getPackageGenericTagText.apply(controllerStub, [packageData])).to.equal("installed");
    });

    it('should return "activating" for INSTALLING status', () => {
      const packageData = { status: BDCPackageStatus.INSTALLING };
      expect(BDCPackagesFormatter.getPackageGenericTagText.apply(controllerStub, [packageData])).to.equal(
        "activationInProgress"
      );
    });
    it('should return "installing" for INSTALLING status on insight apps', () => {
      const packageData = {
        status: BDCPackageStatus.INSTALLING,
        isPackageUpdateAvailable: false,
        type: BDCPackageType.INSIGHT_APPLICATION,
      };
      expect(BDCPackagesFormatter.getPackageGenericTagText.apply(controllerStub, [packageData])).to.equal(
        "installationInProgress"
      );
    });
    it('should return "activationFailed" for INSTALLATION_FAILED status', () => {
      const packageData = { status: BDCPackageStatus.INSTALLATION_FAILED };
      expect(BDCPackagesFormatter.getPackageGenericTagText.apply(controllerStub, [packageData])).to.equal(
        "activationFailed"
      );
    });
    it('should return "installationFailed" for INSTALLATION_FAILED status on insight apps', () => {
      const packageData = {
        status: BDCPackageStatus.INSTALLATION_FAILED,
        isPackageUpdateAvailable: false,
        type: BDCPackageType.INSIGHT_APPLICATION,
      };
      expect(BDCPackagesFormatter.getPackageGenericTagText.apply(controllerStub, [packageData])).to.equal(
        "installationFailed"
      );
    });
    it('should return "installed" for INSTALLED status', () => {
      const packageData = {
        status: BDCPackageStatus.INSTALLED,
        isPackageUpdateAvailable: false,
        type: BDCPackageType.INSIGHT_APPLICATION,
      };
      expect(BDCPackagesFormatter.getPackageGenericTagText.apply(controllerStub, [packageData])).to.equal("installed");
    });
    it('should return "active" for INSTALLED status', () => {
      const packageData = { status: BDCPackageStatus.INSTALLED };
      expect(BDCPackagesFormatter.getPackageGenericTagText.apply(controllerStub, [packageData])).to.equal("active");
    });
  });

  describe("getPackageGenericTagStatus", () => {
    it("should return sap.ui.core.ValueState.Information for AVAILABLE status", () => {
      const packageData = { status: BDCPackageStatus.AVAILABLE };
      expect(BDCPackagesFormatter.getPackageGenericTagStatus.apply(controllerStub, [packageData])).to.equal(
        sap.ui.core.ValueState.Information
      );
    });
    it("should return sap.ui.core.ValueState.Warning for AVAILABLE status without supportedVersion", () => {
      const packageData = {
        status: BDCPackageStatus.AVAILABLE,
        products: [{ isSourceSystemVersionCompatible: false }],
      };
      expect(BDCPackagesFormatter.getPackageGenericTagStatus.apply(controllerStub, [packageData])).to.equal(
        sap.ui.core.ValueState.Warning
      );
    });
    it("should return sap.ui.core.ValueState.Warning for started grace period", () => {
      const packageData = { status: BDCPackageStatus.INSTALLED, gracePeriodStart: new Date() };
      expect(BDCPackagesFormatter.getPackageGenericTagStatus.apply(controllerStub, [packageData])).to.equal(
        sap.ui.core.ValueState.Warning
      );
    });
    it("should return sap.ui.core.ValueState.Information for INSTALLING status", () => {
      const packageData = { status: BDCPackageStatus.INSTALLING };
      expect(BDCPackagesFormatter.getPackageGenericTagStatus.apply(controllerStub, [packageData])).to.equal(
        sap.ui.core.ValueState.Information
      );
    });
    it("should return sap.ui.core.ValueState.Error for INSTALLATION_FAILED status", () => {
      const packageData = { status: BDCPackageStatus.INSTALLATION_FAILED };
      expect(BDCPackagesFormatter.getPackageGenericTagStatus.apply(controllerStub, [packageData])).to.equal(
        sap.ui.core.ValueState.Error
      );
    });
    it("should return sap.ui.core.ValueState.Success for INSTALLED status", () => {
      const packageData = { status: BDCPackageStatus.INSTALLED };
      expect(BDCPackagesFormatter.getPackageGenericTagStatus.apply(controllerStub, [packageData])).to.equal(
        sap.ui.core.ValueState.Success
      );
    });
  });

  describe("getPackageSecondaryGenericTagText", () => {
    it('should return "updateAvailable" for update flag', () => {
      expect(
        BDCPackagesFormatter.getPackageSecondaryGenericTagText.apply(controllerStub, [BDCPackageStatus.INSTALLED])
      ).to.equal("updateAvailable");
    });
  });

  describe("getPackageSecondaryGenericTagVisibility", () => {
    let paramsForVisible: any = {
      isPackageUpdateAvailable: true,
      gracePeriodStart: null,
      status: BDCPackageStatus.INSTALLED,
    };

    beforeEach(() => {
      paramsForVisible = {
        isPackageUpdateAvailable: true,
        gracePeriodStart: null,
        status: BDCPackageStatus.INSTALLED,
      };
    });

    it("should be visible", () => {
      expect(
        BDCPackagesFormatter.getPackageSecondaryGenericTagVisibility.apply(controllerStub, [
          paramsForVisible.isPackageUpdateAvailable,
          paramsForVisible.gracePeriodStart,
          paramsForVisible.status,
        ])
      ).to.equal(true);
    });

    const invisibleCases = [
      { case: "no package update is available", ...paramsForVisible, isPackageUpdateAvailable: false },
      { case: "package update availability is not defined", ...paramsForVisible, isPackageUpdateAvailable: undefined },
      { case: "grace period has started", ...paramsForVisible, gracePeriodStart: new Date() },
      {
        case: "package status is not installing",
        ...paramsForVisible,
        status: BDCPackageStatus.UNINSTALLATION_FAILED,
      },
    ];

    eachCase(invisibleCases).describe(`when $case`, ({ isPackageUpdateAvailable, gracePeriodStart, status }) => {
      it(`should not be visible`, () => {
        expect(
          BDCPackagesFormatter.getPackageSecondaryGenericTagVisibility.apply(controllerStub, [
            isPackageUpdateAvailable,
            gracePeriodStart,
            status,
          ])
        ).to.equal(false);
      });
    });
  });

  describe("getRepairButtonVisibility", () => {
    describe("getRepairButtonVisibility both feature flags are on", () => {
      beforeEach(() => {
        stubFeatureFlags(sinon, {
          ...FEATURE_FLAGS,
          DWCO_BDC_MANUAL_ONBOARDING_RETRY: true,
          DWCO_BDC_FAILED_PACKAGE_MANUAL_OFFBOARDING_RETRY_AND_CLEANUP: true,
        });
      });

      const BDCPackageStatusList = [
        BDCPackageStatus.INSTALLING,
        BDCPackageStatus.INSTALLED,
        BDCPackageStatus.AVAILABLE,
        BDCPackageStatus.UNINSTALLING,
        BDCPackageStatus.UNINSTALLATION_FAILED,
        BDCPackageStatus.TO_BE_UNINSTALLED,
      ];

      it("should be visible", () => {
        expect(
          BDCPackagesFormatter.getCleanupButtonVisibility.apply(controllerStub, [
            BDCPackageStatus.INSTALLATION_FAILED,
            true,
          ])
        ).to.equal(true);
        expect(
          BDCPackagesFormatter.getRetryInstallationButtonVisibility.apply(controllerStub, [
            BDCPackageStatus.INSTALLATION_FAILED,
            null,
            true,
          ])
        ).to.equal(true);
        expect(
          BDCPackagesFormatter.getRepairButtonVisibility.apply(controllerStub, [
            BDCPackageStatus.INSTALLATION_FAILED,
            null,
            true,
          ])
        ).to.equal(true);
      });

      it("should not be visible: due to insufficient privileges", () => {
        expect(
          BDCPackagesFormatter.getCleanupButtonVisibility.apply(controllerStub, [
            BDCPackageStatus.INSTALLATION_FAILED,
            false,
          ])
        ).to.equal(false);
        expect(
          BDCPackagesFormatter.getRetryInstallationButtonVisibility.apply(controllerStub, [
            BDCPackageStatus.INSTALLATION_FAILED,
            null,
            false,
          ])
        ).to.equal(false);
        expect(
          BDCPackagesFormatter.getRetryInstallationButtonVisibility.apply(controllerStub, [
            BDCPackageStatus.INSTALLATION_FAILED,
            undefined,
            false,
          ])
        ).to.equal(false);
        expect(
          BDCPackagesFormatter.getRetryInstallationButtonVisibility.apply(controllerStub, [
            BDCPackageStatus.INSTALLATION_FAILED,
            new Date(),
            false,
          ])
        ).to.equal(false);
        expect(
          BDCPackagesFormatter.getRepairButtonVisibility.apply(controllerStub, [
            BDCPackageStatus.INSTALLATION_FAILED,
            null,
            false,
          ])
        ).to.equal(false);
        expect(
          BDCPackagesFormatter.getRepairButtonVisibility.apply(controllerStub, [
            BDCPackageStatus.INSTALLATION_FAILED,
            new Date(),
            false,
          ])
        ).to.equal(false);
      });

      it("should not be visible: due to entitlement check", () => {
        expect(
          BDCPackagesFormatter.getRepairButtonVisibility.apply(controllerStub, [
            BDCPackageStatus.INSTALLATION_FAILED,
            new Date(),
            true,
          ])
        ).to.equal(true);
        expect(
          BDCPackagesFormatter.getRetryInstallationButtonVisibility.apply(controllerStub, [
            BDCPackageStatus.INSTALLATION_FAILED,
            new Date(),
            true,
          ])
        ).to.equal(false);
      });

      it("should not be visible: due to installation status", () => {
        BDCPackageStatusList.forEach((status) => {
          [true, false].forEach((privilegeToUpdate) => {
            expect(
              BDCPackagesFormatter.getRepairButtonVisibility.apply(controllerStub, [status, null, privilegeToUpdate])
            ).to.equal(false);
            expect(
              BDCPackagesFormatter.getCleanupButtonVisibility.apply(controllerStub, [status, null, privilegeToUpdate])
            ).to.equal(false);
            expect(
              BDCPackagesFormatter.getRetryInstallationButtonVisibility.apply(controllerStub, [
                status,
                null,
                privilegeToUpdate,
              ])
            ).to.equal(false);
          });
        });
      });
    });

    describe("getRepairButtonVisibility DWCO_BDC_MANUAL_ONBOARDING_RETRY feature flag is off", () => {
      beforeEach(() => {
        stubFeatureFlags(sinon, {
          ...FEATURE_FLAGS,
          DWCO_BDC_MANUAL_ONBOARDING_RETRY: false,
          DWCO_BDC_FAILED_PACKAGE_MANUAL_OFFBOARDING_RETRY_AND_CLEANUP: true,
        });
      });

      it("should not be visible: due to feature flag off check", () => {
        expect(
          BDCPackagesFormatter.getCleanupButtonVisibility.apply(controllerStub, [
            BDCPackageStatus.INSTALLATION_FAILED,
            true,
          ])
        ).to.equal(true);
        expect(
          BDCPackagesFormatter.getRetryInstallationButtonVisibility.apply(controllerStub, [
            BDCPackageStatus.INSTALLATION_FAILED,
            null,
            true,
          ])
        ).to.equal(false);
        expect(
          BDCPackagesFormatter.getRepairButtonVisibility.apply(controllerStub, [
            BDCPackageStatus.INSTALLATION_FAILED,
            null,
            true,
          ])
        ).to.equal(true);
      });
    });

    describe("getRepairButtonVisibility DWCO_BDC_FAILED_PACKAGE_MANUAL_OFFBOARDING_RETRY_AND_CLEANUP feature flag is off", () => {
      beforeEach(() => {
        stubFeatureFlags(sinon, {
          ...FEATURE_FLAGS,
          DWCO_BDC_FAILED_PACKAGE_MANUAL_OFFBOARDING_RETRY_AND_CLEANUP: false,
          DWCO_BDC_MANUAL_ONBOARDING_RETRY: true,
        });
      });

      it("should not be visible: due to feature flag off check", () => {
        expect(
          BDCPackagesFormatter.getCleanupButtonVisibility.apply(controllerStub, [
            BDCPackageStatus.INSTALLATION_FAILED,
            null,
            true,
          ])
        ).to.equal(false);
        expect(
          BDCPackagesFormatter.getRetryInstallationButtonVisibility.apply(controllerStub, [
            BDCPackageStatus.INSTALLATION_FAILED,
            null,
            true,
          ])
        ).to.equal(true);
        expect(
          BDCPackagesFormatter.getRepairButtonVisibility.apply(controllerStub, [
            BDCPackageStatus.INSTALLATION_FAILED,
            null,
            true,
          ])
        ).to.equal(true);
      });
    });
  });

  describe("getRetryUninstallationButtonVisibility", () => {
    describe("Feature flag is on", () => {
      beforeEach(() => {
        stubFeatureFlags(sinon, {
          ...FEATURE_FLAGS,
          DWCO_BDC_FAILED_PACKAGE_MANUAL_OFFBOARDING_RETRY_AND_CLEANUP: true,
        });
      });

      it("should be visible when all conditions are met", () => {
        const status = BDCPackageStatus.UNINSTALLATION_FAILED;
        const privilegeToUpdate = true;

        expect(
          BDCPackagesFormatter.getRetryUninstallationButtonVisibility.apply(controllerStub, [status, privilegeToUpdate])
        ).to.equal(true);
      });

      it("should not be visible when status is not UNINSTALLATION_FAILED", () => {
        const privilegeToUpdate = true;

        [
          BDCPackageStatus.INSTALLING,
          BDCPackageStatus.INSTALLED,
          BDCPackageStatus.AVAILABLE,
          BDCPackageStatus.UNINSTALLING,
          BDCPackageStatus.TO_BE_UNINSTALLED,
        ].forEach((status) => {
          expect(
            BDCPackagesFormatter.getRetryUninstallationButtonVisibility.apply(controllerStub, [
              status,
              privilegeToUpdate,
            ])
          ).to.equal(false);
        });
      });

      it("should not be visible when privilegeToUpdate is false", () => {
        const status = BDCPackageStatus.UNINSTALLATION_FAILED;
        const privilegeToUpdate = false;

        expect(
          BDCPackagesFormatter.getRetryUninstallationButtonVisibility.apply(controllerStub, [status, privilegeToUpdate])
        ).to.equal(false);
      });
    });

    describe("Feature flag is off", () => {
      beforeEach(() => {
        stubFeatureFlags(sinon, {
          ...FEATURE_FLAGS,
          DWCO_BDC_FAILED_PACKAGE_MANUAL_OFFBOARDING_RETRY_AND_CLEANUP: false,
        });
      });

      it("should not be visible even if all other conditions are met", () => {
        const status = BDCPackageStatus.UNINSTALLATION_FAILED;
        const privilegeToUpdate = true;

        expect(
          BDCPackagesFormatter.getRetryUninstallationButtonVisibility.apply(controllerStub, [status, privilegeToUpdate])
        ).to.equal(false);
      });
    });
  });

  describe("retryUninstallationVisibilityConditions", () => {
    const cases = [
      {
        caseName: "should return true when status is UNINSTALLATION_FAILED and privilegeToUpdate is true",
        status: BDCPackageStatus.UNINSTALLATION_FAILED,
        privilegeToUpdate: true,
        expected: true,
      },
      {
        caseName: "should return false when status is not UNINSTALLATION_FAILED",
        status: BDCPackageStatus.INSTALLED,
        privilegeToUpdate: true,
        expected: false,
      },
      {
        caseName: "should return false when privilegeToUpdate is false",
        status: BDCPackageStatus.UNINSTALLATION_FAILED,
        privilegeToUpdate: false,
        expected: false,
      },
    ];
    eachCase(cases).describe("$caseName", ({ status, privilegeToUpdate, expected }) => {
      it(`returns ${expected}`, () => {
        expect(BDCPackagesFormatter.retryUninstallationVisibilityConditions(status, privilegeToUpdate)).to.equal(
          expected
        );
      });
    });
  });

  describe("cleanupInstallationVisibilityConditions", () => {
    const cases = [
      {
        caseName: "should return true when status is INSTALLATION_FAILED and privilegeToUpdate is true",
        status: BDCPackageStatus.INSTALLATION_FAILED,
        privilegeToUpdate: true,
        expected: true,
      },
      {
        caseName: "should return false when status is not INSTALLATION_FAILED",
        status: BDCPackageStatus.INSTALLED,
        privilegeToUpdate: true,
        expected: false,
      },
      {
        caseName: "should return false when privilegeToUpdate is false",
        status: BDCPackageStatus.INSTALLATION_FAILED,
        privilegeToUpdate: false,
        expected: false,
      },
    ];
    eachCase(cases).describe("$caseName", ({ status, privilegeToUpdate, expected }) => {
      it(`returns ${expected}`, () => {
        expect(BDCPackagesFormatter.cleanupInstallationVisibilityConditions(status, privilegeToUpdate)).to.equal(
          expected
        );
      });
    });
  });

  describe("getRetryInstallationButtonText", () => {
    it("should return 'retryInstall' for INSIGHT_APPLICATION", () => {
      const result = BDCPackagesFormatter.getRetryInstallationButtonText.apply(controllerStub, [
        BDCPackageType.INSIGHT_APPLICATION,
      ]);
      expect(result).to.equal("retryInstall");
    });

    it("should return 'retryActivation' for DATA_PACKAGE", () => {
      const result = BDCPackagesFormatter.getRetryInstallationButtonText.apply(controllerStub, [
        BDCPackageType.DATA_PACKAGE,
      ]);
      expect(result).to.equal("retryActivation");
    });
  });

  describe("getCleanupInstallationButtonText", () => {
    it("should return 'cleanupInstall' for INSIGHT_APPLICATION", () => {
      const result = BDCPackagesFormatter.getCleanupInstallationButtonText.apply(controllerStub, [
        BDCPackageType.INSIGHT_APPLICATION,
      ]);
      expect(result).to.equal("cleanupInstall");
    });

    it("should return 'cleanupActivation' for DATA_PACKAGE", () => {
      const result = BDCPackagesFormatter.getCleanupInstallationButtonText.apply(controllerStub, [
        BDCPackageType.DATA_PACKAGE,
      ]);
      expect(result).to.equal("cleanupActivation");
    });
  });

  describe("getRetryUninstallationButtonText", () => {
    it("should return 'retryUninstallation' for INSIGHT_APPLICATION", () => {
      const result = BDCPackagesFormatter.getRetryUninstallationButtonText.apply(controllerStub, [
        BDCPackageType.INSIGHT_APPLICATION,
      ]);
      expect(result).to.equal("retryUninstallation");
    });

    it("should return 'retryDeactivation' for DATA_PACKAGE", () => {
      const result = BDCPackagesFormatter.getRetryUninstallationButtonText.apply(controllerStub, [
        BDCPackageType.DATA_PACKAGE,
      ]);
      expect(result).to.equal("retryDeactivation");
    });
  });

  describe("getUninstallButtonText", () => {
    it("should return 'uninstall' for INSIGHT_APPLICATION", () => {
      const result = BDCPackagesFormatter.getUninstallButtonText.apply(controllerStub, [
        BDCPackageType.INSIGHT_APPLICATION,
      ]);
      expect(result).to.equal("uninstall");
    });

    it("should return 'deactivate' for DATA_PACKAGE", () => {
      const result = BDCPackagesFormatter.getUninstallButtonText.apply(controllerStub, [BDCPackageType.DATA_PACKAGE]);
      expect(result).to.equal("deactivate");
    });
  });

  describe("getInstallButtonVisibility", () => {
    it("should be visible", () => {
      expect(
        BDCPackagesFormatter.getInstallButtonVisibility.apply(controllerStub, [
          undefined,
          BDCPackageStatus.AVAILABLE,
          true,
        ])
      ).to.equal(true);
    });

    it("should not be visible", () => {
      [
        BDCPackageStatus.INSTALLED,
        BDCPackageStatus.INSTALLING,
        BDCPackageStatus.INSTALLATION_FAILED,
        undefined,
      ].forEach((status) => {
        expect(BDCPackagesFormatter.getInstallButtonVisibility.apply(controllerStub, [status, false])).to.equal(false);
      });
      expect(
        BDCPackagesFormatter.getInstallButtonVisibility.apply(controllerStub, [BDCPackageStatus.INSTALLED, false])
      ).to.equal(false);
    });
  });

  describe("getCleanupButtonVisibility", () => {
    const cases = [
      {
        caseName:
          "should return true when feature flag is on, status is INSTALLATION_FAILED and privilegeToUpdate is true",
        featureFlagEnabled: true,
        status: BDCPackageStatus.INSTALLATION_FAILED,
        privilegeToUpdate: true,
        expected: true,
      },

      {
        caseName: "should return false when feature flag is off",
        featureFlagEnabled: false,
        status: BDCPackageStatus.INSTALLATION_FAILED,
        privilegeToUpdate: true,
        expected: false,
      },
      {
        caseName: "should return false when status is not INSTALLATION_FAILED (INSTALLED)",
        featureFlagEnabled: true,
        status: BDCPackageStatus.INSTALLED,
        privilegeToUpdate: true,
        expected: false,
      },
      {
        caseName: "should return false when status is not INSTALLATION_FAILED (INSTALLING)",
        featureFlagEnabled: true,
        status: BDCPackageStatus.INSTALLING,
        privilegeToUpdate: true,
        expected: false,
      },
      {
        caseName: "should return false when privilegeToUpdate is false",
        featureFlagEnabled: true,
        status: BDCPackageStatus.INSTALLATION_FAILED,
        privilegeToUpdate: false,
        expected: false,
      },
    ];
    eachCase(cases).describe("$caseName", ({ featureFlagEnabled, status, privilegeToUpdate, expected }) => {
      it(`returns ${expected}`, () => {
        stubFeatureFlags(sinon, {
          ...FEATURE_FLAGS,
          DWCO_BDC_FAILED_PACKAGE_MANUAL_OFFBOARDING_RETRY_AND_CLEANUP: featureFlagEnabled,
        });
        expect(BDCPackagesFormatter.getCleanupButtonVisibility(status, privilegeToUpdate)).to.equal(expected);
      });
    });
  });

  describe("getRetryInstallationButtonVisibility", () => {
    const cases = [
      {
        caseName:
          "should return true when feature flag is on, status is INSTALLATION_FAILED, no gracePeriodStart, privilegeToUpdate is true",
        featureFlagEnabled: true,
        status: BDCPackageStatus.INSTALLATION_FAILED,
        gracePeriodStart: null,
        privilegeToUpdate: true,
        expected: true,
      },
      {
        caseName: "should return false when feature flag is off",
        featureFlagEnabled: false,
        status: BDCPackageStatus.INSTALLATION_FAILED,
        gracePeriodStart: null,
        privilegeToUpdate: true,
        expected: false,
      },
      {
        caseName: "should return false when status is not INSTALLATION_FAILED (INSTALLED)",
        featureFlagEnabled: true,
        status: BDCPackageStatus.INSTALLED,
        gracePeriodStart: null,
        privilegeToUpdate: true,
        expected: false,
      },
      {
        caseName: "should return false when gracePeriodStart is set",
        featureFlagEnabled: true,
        status: BDCPackageStatus.INSTALLATION_FAILED,
        gracePeriodStart: new Date(),
        privilegeToUpdate: true,
        expected: false,
      },
      {
        caseName: "should return false when privilegeToUpdate is false",
        featureFlagEnabled: true,
        status: BDCPackageStatus.INSTALLATION_FAILED,
        gracePeriodStart: null,
        privilegeToUpdate: false,
        expected: false,
      },
    ];

    eachCase(cases).describe(
      "$caseName",
      ({ featureFlagEnabled, status, gracePeriodStart, privilegeToUpdate, expected }) => {
        it(`returns ${expected}`, () => {
          stubFeatureFlags(sinon, {
            ...FEATURE_FLAGS,
            DWCO_BDC_MANUAL_ONBOARDING_RETRY: featureFlagEnabled,
          });
          expect(
            BDCPackagesFormatter.getRetryInstallationButtonVisibility(status, gracePeriodStart, privilegeToUpdate)
          ).to.equal(expected);
        });
      }
    );
  });

  describe("getRepairButtonVisibility", () => {
    const cases = [
      {
        caseName: "should return true when retry installation is visible",
        featureFlagEnabled: true,
        status: BDCPackageStatus.INSTALLATION_FAILED,
        gracePeriodStart: null,
        privilegeToUpdate: true,
        expected: true,
      },
      {
        caseName: "should return false when neither retry nor cleanup is visible (feature flag off)",
        featureFlagEnabled: false,
        status: BDCPackageStatus.INSTALLATION_FAILED,
        gracePeriodStart: null,
        privilegeToUpdate: true,
        expected: false,
      },
      {
        caseName: "should return false when neither retry nor cleanup is visible (wrong status)",
        featureFlagEnabled: true,
        status: BDCPackageStatus.INSTALLED,
        gracePeriodStart: null,
        privilegeToUpdate: true,
        expected: false,
      },
      {
        caseName: "should return false when neither retry nor cleanup is visible (no privilege)",
        featureFlagEnabled: true,
        status: BDCPackageStatus.INSTALLATION_FAILED,
        gracePeriodStart: null,
        privilegeToUpdate: false,
        expected: false,
      },
    ];
    eachCase(cases).describe(
      "$caseName",
      ({ featureFlagEnabled, status, gracePeriodStart, privilegeToUpdate, expected }) => {
        it(`returns ${expected}`, () => {
          stubFeatureFlags(sinon, {
            ...FEATURE_FLAGS,
            DWCO_BDC_MANUAL_ONBOARDING_RETRY: featureFlagEnabled,
            DWCO_BDC_FAILED_PACKAGE_MANUAL_OFFBOARDING_RETRY_AND_CLEANUP: featureFlagEnabled,
          });
          expect(BDCPackagesFormatter.getRepairButtonVisibility(status, gracePeriodStart, privilegeToUpdate)).to.equal(
            expected
          );
        });
      }
    );
  });

  describe("retryInstallationButtonVisibilityConditions", () => {
    const cases = [
      {
        caseName:
          "should return true when status is INSTALLATION_FAILED, no gracePeriodStart, privilegeToUpdate is true",
        status: BDCPackageStatus.INSTALLATION_FAILED,
        gracePeriodStart: null,
        privilegeToUpdate: true,
        expected: true,
      },
      {
        caseName: "should return false when status is not INSTALLATION_FAILED",
        status: BDCPackageStatus.INSTALLED,
        gracePeriodStart: null,
        privilegeToUpdate: true,
        expected: false,
      },
      {
        caseName: "should return false when gracePeriodStart is set",
        status: BDCPackageStatus.INSTALLATION_FAILED,
        gracePeriodStart: new Date(),
        privilegeToUpdate: true,
        expected: false,
      },
      {
        caseName: "should return false when privilegeToUpdate is false",
        status: BDCPackageStatus.INSTALLATION_FAILED,
        gracePeriodStart: null,
        privilegeToUpdate: false,
        expected: false,
      },
    ];

    eachCase(cases).describe("$caseName", ({ status, gracePeriodStart, privilegeToUpdate, expected }) => {
      it(`returns ${expected}`, () => {
        expect(
          BDCPackagesFormatter.retryInstallationButtonVisibilityConditions(status, gracePeriodStart, privilegeToUpdate)
        ).to.equal(expected);
      });
    });
  });

  describe("getUninstallButtonVisibility", () => {
    const cases = [
      {
        caseName:
          "should return true when all feature flags are on, installationId is set, status is INSTALLED, privilegeToUpdate is true",
        featureFlags: {
          DWCO_BDC_GA: true,
          DWCO_BDC_MANUAL_OFFBOARDING: true,
        },
        installationId: "some-id",
        status: BDCPackageStatus.INSTALLED,
        privilegeToUpdate: true,
        expected: true,
      },
      {
        caseName: "should return false when DWCO_BDC_GA is off",
        featureFlags: {
          DWCO_BDC_GA: false,
          DWCO_BDC_MANUAL_OFFBOARDING: true,
        },
        installationId: "some-id",
        status: BDCPackageStatus.INSTALLED,
        privilegeToUpdate: true,
        expected: false,
      },
      {
        caseName: "should return false when DWCO_BDC_MANUAL_OFFBOARDING is off",
        featureFlags: {
          DWCO_BDC_GA: true,
          DWCO_BDC_MANUAL_OFFBOARDING: false,
        },
        installationId: "some-id",
        status: BDCPackageStatus.INSTALLED,
        privilegeToUpdate: true,
        expected: false,
      },
      {
        caseName: "should return false when installationId is not set",
        featureFlags: {
          DWCO_BDC_GA: true,
          DWCO_BDC_MANUAL_OFFBOARDING: true,
        },
        installationId: undefined,
        status: BDCPackageStatus.INSTALLED,
        privilegeToUpdate: true,
        expected: false,
      },
      {
        caseName: "should return false when status is not INSTALLED",
        featureFlags: {
          DWCO_BDC_GA: true,
          DWCO_BDC_MANUAL_OFFBOARDING: true,
        },
        installationId: "some-id",
        status: BDCPackageStatus.AVAILABLE,
        privilegeToUpdate: true,
        expected: false,
      },
      {
        caseName: "should return false when privilegeToUpdate is false",
        featureFlags: {
          DWCO_BDC_GA: true,
          DWCO_BDC_MANUAL_OFFBOARDING: true,
        },
        installationId: "some-id",
        status: BDCPackageStatus.INSTALLED,
        privilegeToUpdate: false,
        expected: false,
      },
    ];
    eachCase(cases).describe("$caseName", ({ featureFlags, installationId, status, privilegeToUpdate, expected }) => {
      it(`returns ${expected}`, () => {
        stubFeatureFlags(sinon, {
          ...FEATURE_FLAGS,
          ...featureFlags,
        });
        expect(
          BDCPackagesFormatter.getUninstallButtonVisibility.apply(controllerStub, [
            installationId,
            status,
            privilegeToUpdate,
          ])
        ).to.equal(expected);
      });
    });
  });

  describe("getInstallButtonText", () => {
    it("should return 'install' for INSIGHT_APPLICATION", () => {
      const result = BDCPackagesFormatter.getInstallButtonText.apply(controllerStub, [
        BDCPackageType.INSIGHT_APPLICATION,
      ]);
      expect(result).to.equal("install");
    });

    it("should return 'activate' for DATA_PACKAGE", () => {
      const result = BDCPackagesFormatter.getInstallButtonText.apply(controllerStub, [BDCPackageType.DATA_PACKAGE]);
      expect(result).to.equal("activate");
    });
  });

  describe("getInstallButtonEnabled", () => {
    it("should be enabled", () => {
      [
        [undefined, [{ isSourceSystemVersionCompatible: true }], [], [{ isInstallable: true }]],
        [
          undefined,
          [{ isSourceSystemVersionCompatible: true }, { isSourceSystemVersionCompatible: true }],
          [],
          [{ isInstallable: true }],
        ],
      ].forEach((args) => {
        expect(BDCPackagesFormatter.getInstallButtonEnabled.apply(controllerStub, args)).to.equal(true);
      });
    });
    it("should be disabled", () => {
      [
        [undefined, [{ isSourceSystemVersionCompatible: false }]],
        [undefined, [{ isSourceSystemVersionCompatible: false }, { isSourceSystemVersionCompatible: true }]],
      ].forEach((args) => {
        expect(BDCPackagesFormatter.getInstallButtonEnabled.apply(controllerStub, args)).to.equal(false);
      });
    });
  });

  describe("getUpdateButtonVisibility", () => {
    let paramsForVisible: any;

    beforeEach(() => {
      paramsForVisible = {
        isPackageUpdateAvailable: true,
        privilegeToUpdate: true,
        gracePeriodStart: null,
        status: BDCPackageStatus.INSTALLED,
      };
    });

    it("should be visible", () => {
      expect(
        BDCPackagesFormatter.getUpdateButtonVisibility.apply(controllerStub, [
          paramsForVisible.isPackageUpdateAvailable,
          paramsForVisible.privilegeToUpdate,
          paramsForVisible.gracePeriodStart,
          paramsForVisible.status,
        ])
      ).to.equal(true);
    });

    const invisibleCases = [
      { case: "package update is not available", ...paramsForVisible, isPackageUpdateAvailable: false },
      {
        case: "when package update availability is not defined",
        ...paramsForVisible,
        isPackageUpdateAvailable: undefined,
      },
      { case: "it has no priviledge to update", ...paramsForVisible, privilegeToUpdate: false },
      { case: "grace period has started", ...paramsForVisible, gracePeriodStart: new Date() },
      { case: "status is missing", ...paramsForVisible, status: undefined },
      { case: "status is not installed", ...paramsForVisible, status: BDCPackageStatus.UNINSTALLATION_FAILED },
    ];

    eachCase(invisibleCases).describe(
      `when $case`,
      ({ isPackageUpdateAvailable, privilegeToUpdate, gracePeriodStart, status }) => {
        it(`should not be visible`, () => {
          expect(
            BDCPackagesFormatter.getUpdateButtonVisibility.apply(controllerStub, [
              isPackageUpdateAvailable,
              privilegeToUpdate,
              gracePeriodStart,
              status,
            ])
          ).to.equal(false);
        });
      }
    );
  });

  describe("getMessageStripText", () => {
    const runTestCases = (testCases: Array<{ description: string; packageData: any; expectedResult: string }>) => {
      testCases.forEach(({ description, packageData, expectedResult }) => {
        it(description, () => {
          expect(BDCPackagesFormatter.getMessageStripText.apply(controllerStub, [packageData])).to.equal(
            expectedResult
          );
        });
      });
    };

    describe("Data Packages", () => {
      const testCases = [
        {
          description: 'should return "" for AVAILABLE status',
          packageData: { status: BDCPackageStatus.AVAILABLE },
          expectedResult: "",
        },
        {
          description: 'should return "outdatedSystemVersionMessage" for AVAILABLE status and notSupportedVersion',
          packageData: {
            type: BDCPackageType.DATA_PACKAGE,
            status: BDCPackageStatus.AVAILABLE,
            products: [{ isSourceSystemVersionCompatible: false }],
          },
          expectedResult: "outdatedSystemVersionMessage(datapackage)",
        },
        {
          description: 'should return "packageActivationFailedMessage" & correlationID for INSTALLATION_FAILED status',
          packageData: {
            type: BDCPackageType.DATA_PACKAGE,
            status: BDCPackageStatus.INSTALLATION_FAILED,
            correlationId: "correlationID",
          },
          expectedResult: "packageActivationFailedMessage(correlationID) %%0.<br>%%1",
        },
        {
          description:
            'should return "packageDeactivationFailedMessage" & correlationID for UNINSTALLATION_FAILED status',
          packageData: {
            type: BDCPackageType.DATA_PACKAGE,
            status: BDCPackageStatus.UNINSTALLATION_FAILED,
            correlationId: "correlationID",
          },
          expectedResult: "packageDeactivationFailedMessage(correlationID) %%0.<br>%%1",
        },
        {
          description: 'should return "newVersionMessage" for INSTALLED status and package update available',
          packageData: {
            status: BDCPackageStatus.INSTALLED,
            products: [],
            isPackageUpdateAvailable: true,
          },
          expectedResult: "newVersionMessage",
        },
      ];

      runTestCases(testCases);
    });

    describe("Insight Applications", () => {
      const testCases = [
        {
          description: 'should return "" for AVAILABLE status',
          packageData: { type: BDCPackageType.INSIGHT_APPLICATION, status: BDCPackageStatus.AVAILABLE },
          expectedResult: "",
        },
        {
          description: 'should return "insightAppInstallingMessage" for INSTALLING status',
          packageData: { type: BDCPackageType.INSIGHT_APPLICATION, status: BDCPackageStatus.INSTALLING },
          expectedResult: "insightAppInstallingMessage",
        },
        {
          description:
            'should return "insightAppInstallationFailedMessage" & correlationID for INSTALLATION_FAILED status',
          packageData: {
            type: BDCPackageType.INSIGHT_APPLICATION,
            status: BDCPackageStatus.INSTALLATION_FAILED,
            correlationId: "correlationID",
          },
          expectedResult: "insightAppInstallationFailedMessage(correlationID) %%0.<br>%%1",
        },
        {
          description:
            'should return "insightAppUninstallationFailedMessage" & correlationID for UNINSTALLATION_FAILED status',
          packageData: {
            type: BDCPackageType.INSIGHT_APPLICATION,
            status: BDCPackageStatus.UNINSTALLATION_FAILED,
            correlationId: "correlationID",
          },
          expectedResult: "insightAppUninstallationFailedMessage(correlationID) %%0.<br>%%1",
        },
      ];

      runTestCases(testCases);
    });
  });

  describe("getMessageStripType", () => {
    it("should return sap.ui.core.MessageType.None for AVAILABLE status", () => {
      const packageData = { status: BDCPackageStatus.AVAILABLE };
      expect(BDCPackagesFormatter.getMessageStripType.apply(controllerStub, [packageData])).to.equal(
        sap.ui.core.MessageType.None
      );
    });
    it("should return sap.ui.core.MessageType.Warning for AVAILABLE status and notSupportedVersion", () => {
      const packageData = {
        status: BDCPackageStatus.AVAILABLE,
        products: [{ isSourceSystemVersionCompatible: false }],
      };
      expect(BDCPackagesFormatter.getMessageStripType.apply(controllerStub, [packageData])).to.equal(
        sap.ui.core.MessageType.Warning
      );
    });
    it("should return sap.ui.core.MessageType.Information for INSTALLING status", () => {
      const packageData = { status: BDCPackageStatus.INSTALLING };
      expect(BDCPackagesFormatter.getMessageStripType.apply(controllerStub, [packageData])).to.equal(
        sap.ui.core.MessageType.Information
      );
    });
    it("should return sap.ui.core.MessageType.Warning for INSTALLED status with gracePeriodStart", () => {
      const packageData = { status: BDCPackageStatus.INSTALLED, gracePeriodStart: new Date() };
      expect(BDCPackagesFormatter.getMessageStripType.apply(controllerStub, [packageData])).to.equal(
        sap.ui.core.MessageType.Warning
      );
    });
    it("should return sap.ui.core.MessageType.Warning for INSTALLED status when already installed everywhere", () => {
      const packageData = { status: BDCPackageStatus.INSTALLED, systems: [{ isInstallable: false }] };
      expect(BDCPackagesFormatter.getMessageStripType.apply(controllerStub, [packageData])).to.equal(
        sap.ui.core.MessageType.Warning
      );
    });
    it("should return sap.ui.core.MessageType.Error for INSTALLATION_FAILED status", () => {
      const packageData = { status: BDCPackageStatus.INSTALLATION_FAILED };
      expect(BDCPackagesFormatter.getMessageStripType.apply(controllerStub, [packageData])).to.equal(
        sap.ui.core.MessageType.Error
      );
    });
    it("should return sap.ui.core.MessageType.Error for UNINSTALLATION_FAILED status", () => {
      const packageData = { status: BDCPackageStatus.UNINSTALLATION_FAILED };
      expect(BDCPackagesFormatter.getMessageStripType.apply(controllerStub, [packageData])).to.equal(
        sap.ui.core.MessageType.Error
      );
    });
  });

  describe("getMessageStripVisibility", () => {
    it("should be visible for statuses INSTALLING, INSTALLATION_FAILED, UNINSTALLATION_FAILED", () => {
      [
        BDCPackageStatus.INSTALLING,
        BDCPackageStatus.INSTALLATION_FAILED,
        BDCPackageStatus.UNINSTALLATION_FAILED,
      ].forEach((status) => {
        const packageData = { status: status };
        expect(BDCPackagesFormatter.getMessageStripVisibility.apply(controllerStub, [packageData])).to.equal(true);
      });
    });
    it("should be visible for AVAILABLE status and notSupportedVersion", () => {
      const packageData = {
        status: BDCPackageStatus.AVAILABLE,
        products: [{ isSourceSystemVersionCompatible: false }],
      };
      expect(BDCPackagesFormatter.getMessageStripVisibility.apply(controllerStub, [packageData])).to.equal(true);
    });
    it("should be visible for INSTALLED status and supportedVersion and package update available", () => {
      const packageData = {
        status: BDCPackageStatus.INSTALLED,
        products: [{ isSourceSystemVersionCompatible: true }],
        isPackageUpdateAvailable: true,
      };
      expect(BDCPackagesFormatter.getMessageStripVisibility.apply(controllerStub, [packageData])).to.equal(true);
    });
    it("should be visible for AVAILABLE status and supportedVersion and not systemAvailableInFormation", () => {
      const packageData = {
        status: BDCPackageStatus.AVAILABLE,
        products: [{ isSourceSystemVersionCompatible: true }],
        contents: [{ isSystemAvailableInFormation: false }],
      };
      expect(BDCPackagesFormatter.getMessageStripVisibility.apply(controllerStub, [packageData])).to.equal(true);
    });
    it("should be visible for INSTALLED status and gracePeriodStart", () => {
      const packageData = { status: BDCPackageStatus.INSTALLED, gracePeriodStart: new Date() };
      expect(BDCPackagesFormatter.getMessageStripVisibility.apply(controllerStub, [packageData])).to.equal(true);
    });
    it("should not be visible for INSTALLED status and already installed on all systems", () => {
      const packageData = { status: BDCPackageStatus.INSTALLED, systems: [{ isInstallable: false }] };
      expect(BDCPackagesFormatter.getMessageStripVisibility.apply(controllerStub, [packageData])).to.equal(false);
    });
    it("should not be visible for INSTALLED status and not installed on all systems", () => {
      const packageData = {
        status: BDCPackageStatus.INSTALLED,
        systems: [{ isInstallable: true }, { isInstallable: false }],
      };
      expect(BDCPackagesFormatter.getMessageStripVisibility.apply(controllerStub, [packageData])).to.equal(false);
    });
    it("should not be visible for AVAILABLE status and no products and no contents", () => {
      const packageData = { status: BDCPackageStatus.AVAILABLE, products: [], contents: [] };
      expect(BDCPackagesFormatter.getMessageStripVisibility.apply(controllerStub, [packageData])).to.equal(false);
    });
    it("should not be visible for AVAILABLE status and no products", () => {
      const packageData = { status: BDCPackageStatus.AVAILABLE, products: [] };
      expect(BDCPackagesFormatter.getMessageStripVisibility.apply(controllerStub, [packageData])).to.equal(false);
    });
    it("should not be visible for INSTALLED status", () => {
      const packageData = { status: BDCPackageStatus.INSTALLED };
      expect(BDCPackagesFormatter.getMessageStripVisibility.apply(controllerStub, [packageData])).to.equal(false);
    });
  });

  describe("getMessageStripLinkText", () => {
    it("should have learnMore text for AVAILABLE status and source system version not compatible", () => {
      const packageData = {
        status: BDCPackageStatus.AVAILABLE,
        products: [{ isSourceSystemVersionCompatible: false }],
      };
      expect(BDCPackagesFormatter.getMessageStripLinkText.apply(controllerStub, [packageData])).to.equal("learnMore");
    });
    it("should have no text for AVAILABLE status and no products", () => {
      const packageData = {
        status: BDCPackageStatus.AVAILABLE,
        products: [],
      };
      expect(BDCPackagesFormatter.getMessageStripLinkText.apply(controllerStub, [packageData])).to.equal("");
    });
    it("should have copyToClipboard text for INSTALLATION_FAILED status", () => {
      const packageData = {
        status: BDCPackageStatus.INSTALLATION_FAILED,
      };
      expect(BDCPackagesFormatter.getMessageStripLinkText.apply(controllerStub, [packageData])).to.equal(
        "copyToClipboard"
      );
    });
    it("should have copyToClipboard text for UNINSTALLATION_FAILED status", () => {
      const packageData = {
        status: BDCPackageStatus.UNINSTALLATION_FAILED,
      };
      expect(BDCPackagesFormatter.getMessageStripLinkText.apply(controllerStub, [packageData])).to.equal(
        "copyToClipboard"
      );
    });
    it("should have learnMore text for INSTALLED status, no products and with package update available", () => {
      const packageData = {
        status: BDCPackageStatus.INSTALLED,
        products: [],
        isPackageUpdateAvailable: true,
      };
      expect(BDCPackagesFormatter.getMessageStripLinkText.apply(controllerStub, [packageData])).to.equal("learnMore");
    });
    it("should have learnMore text for INSTALLED status with gracePeriodStart", () => {
      const packageData = {
        status: BDCPackageStatus.INSTALLED,
        gracePeriodStart: new Date(),
      };
      expect(BDCPackagesFormatter.getMessageStripLinkText.apply(controllerStub, [packageData])).to.equal("learnMore");
    });
    it("should return 'learnMore' when gracePeriodStart is set", () => {
      const packageData = {
        status: BDCPackageStatus.INSTALLED,
        gracePeriodStart: new Date(),
        products: [{ isActive: true }],
      };
      expect(BDCPackagesFormatter.getMessageStripLinkText.apply(controllerStub, [packageData])).to.equal("learnMore");
    });

    it("should return 'learnMore' when partial installation (at least one product isActive=false)", () => {
      const packageData = {
        status: BDCPackageStatus.INSTALLED,
        products: [{ isActive: false }, { isActive: true }],
      };
      expect(BDCPackagesFormatter.getMessageStripLinkText.apply(controllerStub, [packageData])).to.equal("learnMore");
    });
  });

  describe("getMessageStripLinkViewDetailsVisible", () => {
    const cases = [
      {
        caseName: "should return true for INSTALLATION_FAILED and feature flag enabled",
        packageData: { status: BDCPackageStatus.INSTALLATION_FAILED },
        featureFlag: true,
        expected: true,
      },
      {
        caseName: "should return true for UNINSTALLATION_FAILED and feature flag enabled",
        packageData: { status: BDCPackageStatus.UNINSTALLATION_FAILED },
        featureFlag: true,
        expected: true,
      },
      {
        caseName: "should return false for INSTALLATION_FAILED and feature flag disabled",
        packageData: { status: BDCPackageStatus.INSTALLATION_FAILED },
        featureFlag: false,
        expected: false,
      },
      {
        caseName: "should return false for UNINSTALLATION_FAILED and feature flag disabled",
        packageData: { status: BDCPackageStatus.UNINSTALLATION_FAILED },
        featureFlag: false,
        expected: false,
      },
      {
        caseName: "should return false for other status even if feature flag enabled",
        packageData: { status: BDCPackageStatus.INSTALLED },
        featureFlag: true,
        expected: false,
      },
      {
        caseName: "should return false for other status and feature flag disabled",
        packageData: { status: BDCPackageStatus.INSTALLED },
        featureFlag: false,
        expected: false,
      },
    ];
    eachCase(cases).describe("$caseName", ({ packageData, featureFlag, expected }) => {
      it("$caseName", () => {
        stubFeatureFlags(sinon, { DWCO_BDC_COCKPIT_FAILURE_DETAILS: featureFlag });
        expect(
          BDCPackagesFormatter.getMessageStripLinkViewDetailsVisible.apply(controllerStub, [packageData])
        ).to.equal(expected);
      });
    });
  });

  describe("getMessageStripLinkViewDetailsHidden", () => {
    const cases = [
      {
        caseName:
          "should return true when both view details and no controls are false (INSTALLED status, feature flag off)",
        packageData: { status: BDCPackageStatus.INSTALLED },
        featureFlag: false,
        expected: true,
      },
      {
        caseName: "should return false when view details is true (INSTALLATION_FAILED status, feature flag on)",
        packageData: { status: BDCPackageStatus.INSTALLATION_FAILED },
        featureFlag: true,
        expected: false,
      },
      {
        caseName: "should return false when no controls is true (INSTALLING status, feature flag off)",
        packageData: { status: BDCPackageStatus.INSTALLING },
        featureFlag: false,
        expected: false,
      },
      {
        caseName:
          "should return false when both view details and no controls are true (INSTALLING status, feature flag on)",
        packageData: { status: BDCPackageStatus.INSTALLING },
        featureFlag: true,
        expected: false,
      },
    ];
    eachCase(cases).describe("$caseName", ({ packageData, featureFlag, expected }) => {
      it("$caseName", () => {
        stubFeatureFlags(sinon, { DWCO_BDC_COCKPIT_FAILURE_DETAILS: featureFlag });
        expect(BDCPackagesFormatter.getMessageStripLinkViewDetailsHidden.apply(controllerStub, [packageData])).to.equal(
          expected
        );
      });
    });
  });

  describe("getMessageStripLinkLocation", () => {
    it("should have correct link", () => {
      expect(
        BDCPackagesFormatter.getMessageStripLinkLocation.apply(controllerStub, [
          undefined,
          BDCPackageStatus.AVAILABLE,
          [{ isSourceSystemVersionCompatible: false }],
        ])
      ).to.equal(
        "https://help.sap.com/docs/SAP_BUSINESS_DATA_CLOUD/af80fabc26e0482f9e0fe1fe298a4c3b/96a5b59273824c1ba07ffe3d52003e78.html"
      );
      expect(
        BDCPackagesFormatter.getMessageStripLinkLocation.apply(controllerStub, [
          undefined,
          BDCPackageStatus.AVAILABLE,
          [],
        ])
      ).to.equal("https://www.sap.com/germany/index.html");
      expect(
        BDCPackagesFormatter.getMessageStripLinkLocation.apply(controllerStub, [
          undefined,
          BDCPackageStatus.INSTALLED,
          null,
          true,
        ])
      ).to.equal(
        "https://help.sap.com/docs/SAP_BUSINESS_DATA_CLOUD/f7acf8c9dad54e99b5ce5ebc633ed8e1/0d808e429996407b99b6a1da3c530fed.html"
      );
    });
  });

  describe("getMessageStripLinkVisibility", () => {
    it("should be visible for statuses INSTALLATION_FAILED, UNINSTALLATION_FAILED", () => {
      [BDCPackageStatus.INSTALLATION_FAILED, BDCPackageStatus.UNINSTALLATION_FAILED].forEach((status) => {
        const packageData = { status: status };
        expect(BDCPackagesFormatter.getMessageStripLinkVisibility.apply(controllerStub, [packageData])).to.equal(true);
      });
    });
    it("should be visible for AVAILABLE status and system version not compatible", () => {
      const packageData = {
        status: BDCPackageStatus.AVAILABLE,
        products: [{ isSourceSystemVersionCompatible: false }],
      };
      expect(BDCPackagesFormatter.getMessageStripLinkVisibility.apply(controllerStub, [packageData])).to.equal(true);
    });
    it("should be visible for INSTALLED status and system version compatible and package update available", () => {
      const packageData = {
        status: BDCPackageStatus.INSTALLED,
        products: [{ isSourceSystemVersionCompatible: true }],
        isPackageUpdateAvailable: true,
      };
      expect(BDCPackagesFormatter.getMessageStripLinkVisibility.apply(controllerStub, [packageData])).to.equal(true);
    });
    it("should be visible for INSTALLED status with gracePeriodStart", () => {
      const packageData = {
        status: BDCPackageStatus.INSTALLED,
        gracePeriodStart: new Date(),
      };
      expect(BDCPackagesFormatter.getMessageStripLinkVisibility.apply(controllerStub, [packageData])).to.equal(true);
    });
    it("should not be visible for AVAILABLE status and no products", () => {
      const packageData = { status: BDCPackageStatus.AVAILABLE, products: [] };
      expect(BDCPackagesFormatter.getMessageStripLinkVisibility.apply(controllerStub, [packageData])).to.equal(false);
    });
    it("should not be visible for INSTALLED status", () => {
      const packageData = { status: BDCPackageStatus.INSTALLED };
      expect(BDCPackagesFormatter.getMessageStripLinkVisibility.apply(controllerStub, [packageData])).to.equal(false);
    });
    it("should not be visible for INSTALLING status", () => {
      const packageData = { status: BDCPackageStatus.INSTALLING };
      expect(BDCPackagesFormatter.getMessageStripLinkVisibility.apply(controllerStub, [packageData])).to.equal(false);
    });
  });

  describe("getMessageStripLinkViewDetailsVisibility", () => {
    const getVisibility = (packageData): boolean =>
      BDCPackagesFormatter.getMessageStripLinkViewDetailsVisible.apply(controllerStub, [packageData]);

    beforeEach(() => {
      stubFeatureFlags(sinon, { DWCO_BDC_COCKPIT_FAILURE_DETAILS: true });
    });

    it("should return true for INSTALLATION_FAILED status", () => {
      const packageData = { status: BDCPackageStatus.INSTALLATION_FAILED };
      expect(getVisibility(packageData)).to.equal(true);
    });

    it("should return true for UNINSTALLATION_FAILED status", () => {
      const packageData = { status: BDCPackageStatus.UNINSTALLATION_FAILED };
      expect(getVisibility(packageData)).to.equal(true);
    });

    it("should return false for other statuses even", () => {
      const packageData = { status: BDCPackageStatus.INSTALLED };
      expect(getVisibility(packageData)).to.equal(false);
    });

    describe("when the feature flag is disabled (false)", () => {
      beforeEach(() => {
        stubFeatureFlags(sinon, { DWCO_BDC_COCKPIT_FAILURE_DETAILS: false });
      });

      it("should return false even for UNINSTALLATION_FAILED status", () => {
        const packageData = { status: BDCPackageStatus.UNINSTALLATION_FAILED };
        expect(getVisibility(packageData)).to.equal(false);
      });
    });
  });

  describe("getMessageStripNoControlsVisible", () => {
    const getVisibility = (packageData): boolean =>
      BDCPackagesFormatter.getMessageStripNoControlsVisible.apply(controllerStub, [packageData]);

    describe("when status is INSTALLING", () => {
      const packageData = { status: BDCPackageStatus.INSTALLING };
      it("should return true without details visibility", () => {
        sinon.stub(BDCPackagesFormatter, "getMessageStripLinkViewDetailsVisible").returns(false);
        expect(getVisibility(packageData)).to.equal(true);
      });
      it("should return false with view details visibility", () => {
        sinon.stub(BDCPackagesFormatter, "getMessageStripLinkViewDetailsVisible").returns(true);
        expect(getVisibility(packageData)).to.equal(false);
      });
    });

    describe("when status is non-INSTALLING", () => {
      const testCases = [
        { status: BDCPackageStatus.INSTALLED },
        { status: BDCPackageStatus.AVAILABLE },
        { status: BDCPackageStatus.INSTALLATION_FAILED },
        { status: BDCPackageStatus.TO_BE_UNINSTALLED },
        { status: BDCPackageStatus.UNINSTALLING },
        { status: BDCPackageStatus.UNINSTALLATION_FAILED },
      ];
      testCases.forEach(({ status }) => {
        it(`should return false for ${status.toUpperCase()} status`, () => {
          const packageData = { status };
          expect(getVisibility(packageData)).to.equal(false);
        });
      });
    });
  });

  describe("MessageStrip visibility functions mutual exclusivity", () => {
    const testCases = [
      { status: BDCPackageStatus.INSTALLED },
      { status: BDCPackageStatus.AVAILABLE },
      { status: BDCPackageStatus.INSTALLING },
      { status: BDCPackageStatus.INSTALLATION_FAILED },
      { status: BDCPackageStatus.TO_BE_UNINSTALLED },
      { status: BDCPackageStatus.UNINSTALLING },
      { status: BDCPackageStatus.UNINSTALLATION_FAILED },
    ];

    const checkMutualExclusivity = (packageData: { status: BDCPackageStatus }) => {
      const isViewDetailsVisible = BDCPackagesFormatter.getMessageStripLinkViewDetailsVisible.apply(controllerStub, [
        packageData,
      ]);
      const isViewDetailsHidden = BDCPackagesFormatter.getMessageStripLinkViewDetailsHidden.apply(controllerStub, [
        packageData,
      ]);
      const isNoControlsVisible = BDCPackagesFormatter.getMessageStripNoControlsVisible.apply(controllerStub, [
        packageData,
      ]);

      const results = [isViewDetailsVisible, isViewDetailsHidden, isNoControlsVisible];
      expect(results.filter(Boolean).length).to.equal(1);
    };

    testCases.forEach(({ status }) => {
      it(`should ensure only one function returns true for ${status.toUpperCase()} status`, () => {
        const packageData = { status };
        checkMutualExclusivity(packageData);
      });
    });
  });

  describe("getOpenApplicationButtonVisibility", () => {
    it("should be visible", () => {
      expect(
        BDCPackagesFormatter.getOpenApplicationButtonVisibility.apply(controllerStub, [
          BDCPackageStatus.INSTALLED,
          BDCPackageType.INSIGHT_APPLICATION,
        ])
      ).to.equal(true);
    });

    it("should not be visible", () => {
      [BDCPackageStatus.INSTALLING, BDCPackageStatus.AVAILABLE, BDCPackageStatus.INSTALLATION_FAILED].forEach(
        (status) => {
          expect(
            BDCPackagesFormatter.getOpenApplicationButtonVisibility.apply(controllerStub, [
              status,
              BDCPackageType.DATA_PACKAGE,
            ])
          ).to.equal(false);
        }
      );
    });
  });

  describe("getSupportedSystemVersionObjectStatusIcon", () => {
    it("should be ''", () => {
      expect(
        BDCPackagesFormatter.getSupportedSystemVersionObjectStatusIcon.apply(controllerStub, [undefined, []])
      ).to.equal("");
      expect(
        BDCPackagesFormatter.getSupportedSystemVersionObjectStatusIcon.apply(controllerStub, [
          undefined,
          [{ isSourceSystemVersionCompatible: true }, { isSourceSystemVersionCompatible: true }],
        ])
      ).to.equal("");
    });
    it("should be sap-icon://alert", () => {
      expect(
        BDCPackagesFormatter.getSupportedSystemVersionObjectStatusIcon.apply(controllerStub, [
          undefined,
          [{ isSourceSystemVersionCompatible: false }],
        ])
      ).to.equal("sap-icon://alert");
      expect(
        BDCPackagesFormatter.getSupportedSystemVersionObjectStatusIcon.apply(controllerStub, [
          undefined,
          [{ isSourceSystemVersionCompatible: false }, { isSourceSystemVersionCompatible: true }],
        ])
      ).to.equal("sap-icon://alert");
    });
  });

  describe("getSupportedSystemVersionObjectStatusState", () => {
    it("should be sap.ui.core.ValueState.None", () => {
      expect(
        BDCPackagesFormatter.getSupportedSystemVersionObjectStatusState.apply(controllerStub, [undefined, []])
      ).to.equal(sap.ui.core.ValueState.None);
      expect(
        BDCPackagesFormatter.getSupportedSystemVersionObjectStatusState.apply(controllerStub, [
          undefined,
          [{ isSourceSystemVersionCompatible: true }, { isSourceSystemVersionCompatible: true }],
        ])
      ).to.equal(sap.ui.core.ValueState.None);
    });
    it("should be sap.ui.core.ValueState.Warning", () => {
      expect(
        BDCPackagesFormatter.getSupportedSystemVersionObjectStatusState.apply(controllerStub, [
          undefined,
          [{ isSourceSystemVersionCompatible: false }],
        ])
      ).to.equal(sap.ui.core.ValueState.Warning);
      expect(
        BDCPackagesFormatter.getSupportedSystemVersionObjectStatusState.apply(controllerStub, [
          undefined,
          [{ isSourceSystemVersionCompatible: false }, { isSourceSystemVersionCompatible: true }],
        ])
      ).to.equal(sap.ui.core.ValueState.Warning);
    });
  });

  describe("getSystemVersionObjectStatusState", () => {
    it("should be sap.ui.core.ValueState.None", () => {
      expect(BDCPackagesFormatter.getSystemVersionObjectStatusState.apply(controllerStub, [])).to.equal(
        sap.ui.core.ValueState.None
      );
      expect(BDCPackagesFormatter.getSystemVersionObjectStatusState.apply(controllerStub, [false])).to.equal(
        sap.ui.core.ValueState.None
      );
    });
    it("should be sap.ui.core.ValueState.Warning", () => {
      expect(BDCPackagesFormatter.getSystemVersionObjectStatusState.apply(controllerStub, [true])).to.equal(
        sap.ui.core.ValueState.Warning
      );
    });
  });

  describe("getPackageIcon", () => {
    it("should have correct icon", () => {
      expect(BDCPackagesFormatter.getPackageIcon.apply(controllerStub, [])).to.equal("sap-icon://question-mark");
      expect(BDCPackagesFormatter.getPackageIcon.apply(controllerStub, ["abc"])).to.equal("sap-icon://question-mark");
      expect(BDCPackagesFormatter.getPackageIcon.apply(controllerStub, [BDCPackageType.INSIGHT_APPLICATION])).to.equal(
        "sap-icon://sac/insight-app"
      );
      expect(BDCPackagesFormatter.getPackageIcon.apply(controllerStub, [BDCPackageType.DATA_PACKAGE])).to.equal(
        "sap-icon://sac/data-package"
      );
    });
  });

  describe("getPreviewImage", () => {
    const cases = [
      {
        caseName: "should return the url if it is a full URL",
        preview: { url: "https://example.com/image.png" },
        expected: "https://example.com/image.png",
      },
      {
        caseName: "should return base64 data URI if url is not present but base64 is",
        preview: { base64: "abc123" },
        expected: "data:image/png;base64,abc123",
      },
      {
        caseName: "should return base64 data URI if url is not a full URL but base64 is present",
        preview: { url: "not-a-full-url", base64: "xyz456" },
        expected: "data:image/png;base64,xyz456",
      },
    ];
    eachCase(cases).describe("$caseName", ({ preview, expected }) => {
      it("$caseName", () => {
        expect(BDCPackagesFormatter.getPreviewImage.apply(controllerStub, [preview])).to.equal(expected);
      });
    });
  });

  describe("getPackageDSPSpaceName", () => {
    it('should return "spaceName"', () => {
      const content = [
        {
          name: "",
          provider: "sap.datasphere",
          version: "",
          spaces: [],
          spaceName: "spaceName",
        },
      ] as BDCAvailableContent[];
      const expectedResult = "spaceName";
      expect(BDCPackagesFormatter.getPackageDSPSpaceName.apply(controllerStub, [content])).to.equal(expectedResult);
    });
  });

  describe("getPackageSACWorkspaceName", () => {
    it('should return "workspaceName"', () => {
      const content = [
        {
          name: "",
          provider: "sap.analytics",
          version: "",
          spaces: [],
          workspaceName: "workspaceName",
        },
      ] as BDCAvailableContent[];
      const expectedResult = "workspaceName";
      expect(BDCPackagesFormatter.getPackageSACWorkspaceName.apply(controllerStub, [content])).to.equal(expectedResult);
    });
  });

  describe("getPackageContentByProvider", () => {
    it("should return BDCAvailableContent interface", () => {
      const contents = [
        {
          name: "",
          provider: "sap.analytics",
          version: "",
          spaces: [],
        },
      ] as BDCAvailableContent[];
      const provider = "sap.analytics" as BDCProvider;
      const expectedResult = contents[0];
      expect(BDCPackagesFormatter.getPackageContentByProvider.apply(controllerStub, [contents, provider])).to.equal(
        expectedResult
      );
    });
  });

  describe("shouldBeVisible", () => {
    const cases = [
      {
        caseName: "should return true for contentTable and INSIGHT_APPLICATION",
        id: "contentTable",
        type: BDCPackageType.INSIGHT_APPLICATION,
        packages: [{}],
        hasError: false,
        featureFlag: true,
        expected: true,
      },
      {
        caseName: "should return false for contentTable and DATA_PACKAGE",
        id: "contentTable",
        type: BDCPackageType.DATA_PACKAGE,
        packages: [{}],
        hasError: false,
        featureFlag: true,
        expected: false,
      },
      {
        caseName: "should return true for availablePackagesNoDataIllustration and no packages, no error",
        id: "availablePackagesNoDataIllustration",
        type: BDCPackageType.DATA_PACKAGE,
        packages: [],
        hasError: false,
        featureFlag: true,
        expected: true,
      },
      {
        caseName: "should return false for availablePackagesNoDataIllustration and has packages",
        id: "availablePackagesNoDataIllustration",
        type: BDCPackageType.DATA_PACKAGE,
        packages: [{}],
        hasError: false,
        featureFlag: true,
        expected: false,
      },
      {
        caseName: "should return false for availablePackagesNoDataIllustration and has error",
        id: "availablePackagesNoDataIllustration",
        type: BDCPackageType.DATA_PACKAGE,
        packages: [],
        hasError: true,
        featureFlag: true,
        expected: false,
      },
      {
        caseName: "should return true for installLocationTableColumn and DWCO_BDC_GA is true",
        id: "installLocationTableColumn",
        type: BDCPackageType.DATA_PACKAGE,
        packages: [{}],
        hasError: false,
        featureFlag: true,
        expected: true,
      },
      {
        caseName: "should return false for installLocationTableColumn and DWCO_BDC_GA is false",
        id: "installLocationTableColumn",
        type: BDCPackageType.DATA_PACKAGE,
        packages: [{}],
        hasError: false,
        featureFlag: false,
        expected: false,
      },
      {
        caseName: "should return undefined for unknown id",
        id: "unknownId",
        type: BDCPackageType.DATA_PACKAGE,
        packages: [{}],
        hasError: false,
        featureFlag: true,
        expected: undefined,
      },
    ];
    eachCase(cases).describe("$caseName", ({ id, type, packages, hasError, featureFlag, expected }) => {
      it("$caseName", () => {
        stubFeatureFlags(sinon, { DWCO_BDC_GA: featureFlag });
        expect(BDCPackagesFormatter.shouldBeVisible.apply(controllerStub, [id, type, packages, hasError])).to.equal(
          expected
        );
      });
    });
  });

  describe("getDSPLocationName", () => {
    it('should return "location name for DSP"', () => {
      const contents = [
        {
          name: "",
          provider: "sap.datasphere",
          version: "",
          spaces: [
            {
              name: "location name for DSP",
              url: "",
              sourceId: "",
              visible: true,
            },
          ] as PackageFolder[],
        },
      ] as BDCInstalledContent[];
      const workspaceLinks = [];
      const provider = "sap.datasphere" as BDCProvider;
      const expectedResult = "location name for DSP";
      expect(
        BDCPackagesFormatter.getLocationName.apply(controllerStub, [
          BDCPackageStatus.INSTALLED,
          provider,
          contents[0].spaces,
          workspaceLinks,
        ])
      ).to.equal(expectedResult);
    });
  });

  describe("getSACLocationName", () => {
    it('should return "location name for SAC"', () => {
      const contents = [
        {
          name: "",
          provider: "sap.analytics",
          version: "",
          spaces: [] as PackageFolder[],
        },
      ] as BDCInstalledContent[];
      const workspaceLinks = [
        {
          name: "location name for SAC",
          url: "",
        },
      ];
      const provider = "sap.analytics" as BDCProvider;
      const expectedResult = "location name for SAC";
      expect(
        BDCPackagesFormatter.getLocationName.apply(controllerStub, [
          BDCPackageStatus.INSTALLED,
          provider,
          contents[0].spaces,
          workspaceLinks,
        ])
      ).to.equal(expectedResult);
    });
  });

  describe("getLocation", () => {
    it('should return "location"', () => {
      const contents = [
        {
          name: "",
          provider: "sap.analytics",
          version: "",
          spaces: [] as PackageFolder[],
        },
      ] as BDCInstalledContent[];
      const workspaceLinks = [
        {
          name: "",
          url: "location",
        },
      ];
      const provider = "sap.analytics" as BDCProvider;
      const expectedResult = workspaceLinks[0];
      expect(
        BDCPackagesFormatter.getLocation.apply(controllerStub, [provider, contents[0].spaces, workspaceLinks])
      ).to.equal(expectedResult);
    });
  });

  describe("getDSPLocation", () => {
    it('should return "location for DSP"', () => {
      const contents = [
        {
          name: "",
          provider: "sap.datasphere",
          version: "",
          spaces: [
            {
              name: "",
              url: "location for DSP",
              sourceId: "",
              visible: true,
            },
          ] as PackageFolder[],
        },
      ] as BDCInstalledContent[];
      const workspaceLinks = [];
      const provider = "sap.datasphere" as BDCProvider;
      const expectedResult = "location for DSP";
      expect(
        BDCPackagesFormatter.getLocationUrl.apply(controllerStub, [
          BDCPackageStatus.INSTALLED,
          provider,
          contents[0].spaces,
          workspaceLinks,
        ])
      ).to.equal(expectedResult);
    });
  });

  describe("getSACLocation", () => {
    it('should return "location for SAC"', () => {
      const contents = [
        {
          name: "",
          provider: "sap.analytics",
          version: "",
          spaces: [] as PackageFolder[],
        },
      ] as BDCInstalledContent[];
      const workspaceLinks = [
        {
          name: "",
          url: "location for SAC",
        },
      ];
      const provider = "sap.analytics" as BDCProvider;
      const expectedResult = "location for SAC";
      expect(
        BDCPackagesFormatter.getLocationUrl.apply(controllerStub, [
          BDCPackageStatus.INSTALLED,
          provider,
          contents[0].spaces,
          workspaceLinks,
        ])
      ).to.equal(expectedResult);
    });
  });
});
