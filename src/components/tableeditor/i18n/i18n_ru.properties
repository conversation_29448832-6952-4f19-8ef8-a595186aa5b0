enterValidModelName=Введите действительное имя модели.
enterModelName=Введите имя модели.
key=Ключ
null=Ноль разрешен
fieldName=Имя столбца
length=Длина
precision=Точность
scale=Масштаб
description=Описание
type=Тип
dataView=Ракурс данных
details=Сведения
showHideSourceTree=Показать/скрыть исходное дерево
mode=Режим
general=Общее
remote=Дистанционно
viewJsonEditor=Показать редактор JSON
closeContext=Закрыть контекст
validate=Проверить
save=Сохранить
deploy=Развернуть
dataPreview=Предпросмотр данных
viewEditor=Показать редактор таблиц
accessType=Тип доступа
#XTIT Save Dialog param
modelNameTable=Таблица

changeAttributesToMeasures=Преобразовать выбранные атрибуты в показатели
changeMeasuresToAttributes=Преобразовать выбранные показатели в атрибуты
missingAttributesSelection=Атрибуты не выбраны
missingMeasuresSelection=Показатели не выбраны
attributesAndMeasuresAmountTitle=Атрибуты и показатели ({0})
openInMonitor=Открыть в мониторе
gotoStatisticsMonitor=Перейти к статистике дистанционных таблиц
gotoMonitor=Перейти к монитору дистанционных таблиц
#XTIT: Title for Filter section
filterTitle=Фильтры ({0})
#XMSG:Button to change remote table source
changeRemoteTableSource=Изменить источник дистанционной таблицы
#XTIT: Title of refresh confirmation dialog
confirmRefreshDialogTitle=Импорт
#XMSG: text in confirmation dialog that a user shall confirm the Refresh
confirmRefreshDialogContent=Вы собираетесь импортировать все изменения из базового источника. \n Продолжить?
#XMSG:text in confirmation dialog
refreshConfirmGenericMessage=Вы собираетесь импортировать все изменения из базового источника.
refreshMessageRemote=В источник добавлены новые столбцы. Выберите столбцы для добавления в дистанционную таблицу.
refreshMessageReplicate=В источник добавлены новые столбцы. Выберите столбцы для добавления в дистанционную таблицу. Помните, что нужно удалить тиражированные данные при добавлении новых столбцов.
#XBUT: Confirm Button in confirmation dialog, an additional button will be "Cancel"
confirmRefreshButton=Импорт
#XMSG: Toaster to display table up to date
txttableuptodate=Ваша таблица актуальна.
#XMSG:Message to show refreshing
importProgressmsg=Импорт изменений из {0}
#XMSG:Message to show refreshing
importProcessing=Поиск изменений в источнике...
#XMSG:Toaster to tell import completion
importCompleted=Импорт завершен
importFailed=Импорт не выполнен
#XTIT: Title of deploy error dialog
titError=Ошибка
titInfo=Информация
#XMSG:shows information that deployment cannot be proceeded.
deployErrorForReplicatedTables=Необходимо удалить тиражируемые данные в мониторе дистанционных таблиц,\nпрежде чем продолжить развертывание.
#XMSG:shows information that deployment cannot be proceeded.
deployErrorForPartitionFiltersNotSupported=Необходимо снять фильтры и удалить раздел в мониторе дистанционных таблиц,\n прежде чем продолжить развертывание.
#XMSG:shows information that deployment cannot be proceeded.
deployErrorForFiltersNotSupported=Необходимо снять фильтры,\n прежде чем продолжить развертывание.
#XMSG:shows information that deployment cannot be proceeded.
deployErrorForPartitionNotSupported=Необходимо удалить раздел в мониторе дистанционных таблиц,\n прежде чем продолжить развертывание.
#XMSG:Shows when the underlying source table is deleted and refresh is performed on DWC
errorMsgForTableNotExist=Дистанционная таблица ''{0}'' не существует.
#XMSG:Shows when the open SQL schema table is deleted and refresh is performed on DWC
errorMsgTableNotExist=Таблица ''{0}'' не существует.
#XBUT: Drop down menu button to load new snapshot
loadNewSnapshot=Загрузить новый мгновенный снимок
#XBUT: Drop down menu button to load new snapshot
loadNewSnapshotNew=Запустить тиражирование данных
#XBUT: Drop down menu button to remove replicated data
removeReplicatedData=Удалить тиражированные данные
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationNew=Активировать тиражирование данных в реальном времени
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplication=Активировать доступ в реальном времени
#XBUT: Drop down menu button to disable real time replication
disableRealTimeReplication=Деактивировать доступ в реальном времени
#XBUT: Drop down menu button to disable real time replication
disableRealTimeReplicationNew=Деактивировать тиражирование данных в реальном времени
#XBUT: Table replication menu button
tableReplication=Тиражирование данных
#XBUT: Table replication menu button
tableReplicationNew=Тиражирование данных
#XBUT: Schedule replication menu button
scheduleReplication=Запланировать тиражирование
#XBUT: Refresh replication menu button
refreshReplication=Обновить тиражирование
#XBUT: Refresh button for remote only tables
refreshfederated=Обновить
#XTXT: Create schedule replication description
createScheduleReplicationDescription=Создать планирование тиражирования
#XTXT: Edit schedule replication description
editScheduleReplicationDescription=Редактировать планирование тиражирования
#XTXT: Delete schedule replication description
DeleteScheduleReplicationDescription=Удалить планирование тиражирования
#XMSG: Refreshed successfully message
refreshedSuccessfully=Успешно обновлено
#XTXT: Last updated replication
lastUpdated=Последнее обновление
#XTXT: Refresh frequency
refreshFrequency=Частота обновления
scheduled=Запланировано
#XTXT: Paused
paused=Приостановлено
@addParentChild=Добавить родительский и дочерний элементы
#XTXT: Label for add column
addColumnText=Добавить столбцы
#XMSG: New columns message
newColumnLabelText=Новые столбцы в дистанционном источнике
#XMSG: Excluded columns message
excludedColText=Ранее исключенные столбцы
#XMSG: Column deletion error list message
deleteRemoteColErrorTxt=Приведенные ниже ограничения препятствуют удалению выбранных столбцов:
#XMSG: Key column error
keyColErrorTxt=Это ключевой столбец.
#XMSG: Association Column error
associationColErrorTxt=Этот столбец – часть связи.
#XMSG: partitoin fetch error
partitionFetchErrorTxt=Обнаружена ошибка при получении данных раздела для этой таблицы.
#XMSG: Partition column error
partitionColErrorTxt=Этот столбец используется в разделе.
#XMSG: Filter column error
filterColErrorTxt=Этот столбец используется в фильтрах.
#XMSG: Hierarchy column error
hierColErrorTxt=Этот столбец – часть иерархии.
#XMSG: Ok Text
okText=ОК
#XMSG: Cancel text
canceltext=Отменить
#XMSG: Ignore text
ignoreText=Игнорировать
#XMSG: Update text
updateText=Обновить
#XMSG: Info text for refresh remote table columns
refreshColumnInfoText=Источник дистанционной таблицы содержит обновления столбцов. Выберите обновления, чтобы применить их к данной таблице.
#XTXT: Label for column updates
updateColumnText=Обновления столбцов
#XMSG: search text
search=Поиск столбца
#XMSG: No updates text
noUpdatesText=Нет доступных обновлений для данной таблицы.
#XMSG-Validation messages
@emptyFilterValue=Введите действительное значение фильтра.

#XMSG: lower bound error text
lowerbounderrormsg=Нижний предел должен быть меньше верхнего предела.
#XMSG: lower bound error text for string
lowerbounderrormsgforString=Строка "{0}" больше, чем "{1}".
#XMSG: higher bound error text
higherbounderrormsg=Верхний предел должен быть больше нижнего предела.
#XMSG: higher bound error text for string
higherbounderrormsgforString=Строка "{1}" меньше, чем "{0}".
#XMSG Error msg for missing values
emptyBoundValuesErrorMsg=Укажите нижнее и верхнее значения.
VAL_LENGTH_EXCEED=Длина значения не должна превышать {0}.
VAL_DEFAULT_LENGTH_EXCEED=Длина значения слишком велика. Введите меньшее значение.
VAL_ENTER_VALID_INT=Введите действительное целое число.
VAL_ENTER_VALID_DECIMAL=Введите действительное десятичное значение c точностью {0} и масштабом {1}.
VAL_ENTER_VALID_DECIMAL_VALUE=Введите десятичное число.
VAL_ENTER_VALID_DATE=Введите допустимую дату.
VAL_ENTER_VALID_NUMBER=Введите действительное число.
VAL_DUPLICATE_FILTER=Продублируйте значение фильтра.
VAL_DEFAULT_RANGE_EXCEED_TINYINT=Введите действительное целочисленное значение от 0 до 255.
VAL_DEFAULT_RANGE_EXCEED_SMALLINT=Введите действительное целочисленное значение от -32768 до 32767.
VAL_DEFAULT_RANGE_EXCEED_INT=Введите действительное целочисленное значение от -2147483648 до 2147483647.
VAL_DEFAULT_RANGE_EXCEED_BIGINT=Введите действительное целочисленное значение от -9223372036854775808 до 9223372036854775807.
#XFLD: Placeholder for time format
@timeFormat=чч:мм:сс
#XFLD: Placeholder for date and time format
@dateTimeFormat=ДД/ММ/ГГГГ, чч:мм:сс
@param_true=истина
@param_false=ложь
@none=Нет
#XFLD: Placeholder for date format Filter
@dateFormatFilter=ГГГГ-ММ-ДД
#XFLD: Placeholder for time format Filter
@timeFormatFilter=ЧЧ:мм:сс
#XFLD: Placeholder for date and time format Filter
@dateTimeFormatFilterNew=ГГГГ-ММ-ДД ЧЧ:мм:сс
@upgradedpagent=Обновите дистанционную таблицу, прежде чем создавать фильтр. Убедитесь, что для соединения с дистанционной таблицей используется агент провизионирования данных версии >= 2.5.4.1.
@editCompoundKey=Редактировать составной ключ
#XFLD: view Details link
VIEW_ERROR_DETAILS=Просмотреть сведения
@parentChildHierarchy=Иерархия родительских и дочерних элементов:
@hierarchyParentList=Родительский элемент:
@hierarchyChildList=Дочерний элемент:
@removeRepresentativeKey=Удалить репрезентативный ключ "{0}"?
@remove=Удалить
#XTIT: Header for Table
txt_columns=Техническое имя
txt_new_columns=Новые столбцы в дистанционном источнике
txt_error=Ошибка
#XMSG
txt_errorMsgRefreshConfirmDlg=Необходимо добавить все новые ключевые столбцы источника в дистанционную таблицу.
#XMSG
txt_table_dropped=Дистанционная таблица была удалена из дистанционного источника. Удалите ее из репозитария.
#XBTN: Button to close the dialog
txt_close=Закрыть
txt_statisticsMenu=Статистика
txt_createStatistics=Создать статистику
txt_delStatistics=Удалить статистику
#XFLD
txt_stat_lastUpdated=Последнее обновление статистики
txt_statistics_type=Тип статистики
#XMSG: Replication will change
txt_replication_change=Тип тиражирования будет изменен.
txt_repl_viewdetails=Просмотреть сведения
msgStripDataExists=Локальные данные таблицы уже существуют. Их нужно удалить до определения разделов.
#XTIT Define Partitions dialog header
tit_definePartitions=Определить разделы
partitionSectionHeader=Разделы
GreaterThanOrEqualTo=>=
#XFLD: Label text
lowValPlaceHolder=Ввести минимальное значение
#XFLD: Label text
HighValPlaceHolder=Ввести максимальное значение
lessThan=<
#XFLD: Label text
AddRange=Добавить раздел
#XFLD: Label text
deleteRange=Удалить раздел
attributeLbl=Атрибут:
columnLbl=Столбец:
#XBTN Button to cancel partition dialog
cancel=Отменить
Name=Имя
Partition_Range=Диапазон раздела
Edit=Редактировать
Delete=Удалить
deletePartitionLabel=Удалить раздел
editPartitionLabel=Редактировать раздел

#XMSG: Column change confirmation text
columnChangeConfirmationTxt=Вы собираетесь изменить столбец разделения и удалить существующее разделение.
#XMSG: Partition type change confirmation text
partitionTypeChangeConfirmationTxt=Вы собираетесь изменить тип разделения и удалить существующее разделение.
#XMSG: unsaved data msg
unsavedDataTxt=Есть несохраненные данные. Продолжить?
#XMSG: If the table does not contain key columns, show warning that key cannot be defined again
warn_noKeys_defined=После создания разделов уже нельзя определять ключевые столбцы.
#XMSG: Message strip to display information that key property cannot be changed for partition column
msg_partition_withoutKeys=Невозможно изменить ключевое свойство, пока существует раздел.
msg_partition_withKeys=Ключевое свойство разделения столбца нельзя изменить.
txt_learn_more=Узнать больше...
txt_addAtleastoneCol=Таблица "{0}" не имеет столбцов. Для продолжения добавьте минимум один столбец.
txt_no_valid_partitionCol=Нет действительных столбцов для использования в разделении.
#XBTN Button to continue the partitions creation even if there are no key columns defined
txt_continueAnyway=Все равно продолжить
#XBTN Delete button in the confirmation dialog to delete partitions
txt_delete=Удалить
txt_delete_partitions=Удалить разделы?
#XTIT: Title of refresh confirmation dialog
tit_refreshValidationDlg=Обновить
txt_refreshValidationGenericMessage=Источник был изменен в дистанционной системе. Необходимо применить удаления и изменения столбцов.
txt_refreshValidationMsg=Вы можете добавить любые или все новые столбцы в определение дистанционной таблицы. По умолчанию будут добавлены только новые ключевые столбцы.
txt_new_columns_header=Новые столбцы в исходной таблице
#XBTN:Apply refresh action
txt_apply_refresh=Применить
#XBTN Cancel refresh action
txt_refresh_cancel=Отменить
txt_tableRefreshApplied=Обновление уже применено для таблицы "{0}". Разверните дистанционную таблицу, чтобы использовать обновленные свойства.
txt_no_data=Данные не найдены.
txt_confirmrestoreRefresh=Если восстановить развернутую версию, выполнение тиражирования завершится сбоем из-за изменения метаданных в источнике.\n Чтобы исправить эти динамические ошибки, нужно будет обновить таблицу, применить изменения и повторно развернуть дистанционную таблицу. \n Продолжить?
txt_restoreDeployedVersion=Восстановить развернутую версию
txt_restoreDeployedVersionNew=Вернуться к развернутой версии
txt_continue=Продолжить
allColumsSelectedPartitionTextNew=Вы не можете выбрать все столбцы в качестве столбцов раздела. Отмените выбор хотя бы одного столбца.
connectionIsUnavailable=Исходная система сейчас недоступна. Переподключите исходную систему, чтобы выполнить обновление.
restoreErrorLtf=Локальную таблицу (файловую) невозможно восстановить в предыдущей версии, поскольку она содержит данные, которые невозможно физически удалить.
restoreErrorHana=Локальную таблицу невозможно восстановить в предыдущей версии, поскольку определение раздела изменилось с момента последней версии. Удалите данные и повторите попытку.

#XBTN: Change Source
changeSource=Изменить источник
#XMSG:Information to say there is no data in table
txt_norecords=Таблица не содержит записей для удаления.
tit_norecords=Удалить
tit_read_only=Только для чтения
readOnlyInfo=Эта локальная таблица (файловая) создана {0}. Невозможно отредактировать таблицу.
txt_generate=Сгенерировать
txt_cancel=Отменить
#XLBL
lbl_num_hashPartitions=Число хеш-разделов:
lblType=Тип
lblPartitionType=Тип раздела
lblDefinition=Определение
confirmText=Подтвердить
#XMSG
txtPositiveInteger=Введите действительное положительное целое число
