enterValidModelName=Please enter a valid model name.
enterModelName=Please enter a model name.
key=Key
null=Null is Allowed
fieldName=Column Name
length=Length
precision=Precision
scale=Scale
description=Description
type=Type
dataView=Data View
details=Details
showHideSourceTree=Show or hide source tree
mode=Mode
general=General
remote=Remote
viewJsonEditor=View JSON Editor
closeContext=Close Context
validate=Validate
save=Save
deploy=Deploy
dataPreview=Data Preview
viewEditor=View Table Editor
accessType=Access Type
#XTIT Save Dialog param
modelNameTable=Table

changeAttributesToMeasures=Convert selected attributes to measures
changeMeasuresToAttributes=Convert selected measures to attributes
missingAttributesSelection=No attributes selected
missingMeasuresSelection=No measures selected
attributesAndMeasuresAmountTitle=Attributes & Measures ({0})
openInMonitor=Open in Monitor
gotoStatisticsMonitor=Go to Remote Table Statistics
gotoMonitor=Go to Remote Table Monitor
#XTIT: Title for Filter section
filterTitle=Filters ({0})
#XMSG:Button to change remote table source
changeRemoteTableSource=Change Remote Table Source
#XTIT: Title of refresh confirmation dialog
confirmRefreshDialogTitle=Import
#XMSG: text in confirmation dialog that a user shall confirm the Refresh
confirmRefreshDialogContent=You are about to import all changes from the underlying source.\n Do you want to continue?
#XMSG:text in confirmation dialog
refreshConfirmGenericMessage=You are about to import all changes from the underlying source.
refreshMessageRemote=New columns have been added to the source. Select the columns you want to add to the remote table.
refreshMessageReplicate=New columns have been added to the source. Select the columns you want to add to the remote table. Be aware that you need to remove replicated data when new columns are added.
#XBUT: Confirm Button in confirmation dialog, an additional button will be "Cancel"
confirmRefreshButton=Import
#XMSG: Toaster to display table up to date
txttableuptodate=Your table is up-to-date.
#XMSG:Message to show refreshing
importProgressmsg=Importing changes from {0}
#XMSG:Message to show refreshing
importProcessing=Looking for changes in the source...
#XMSG:Toaster to tell import completion
importCompleted=Import completed
importFailed=Import failed
#XTIT: Title of deploy error dialog
titError=Error
titInfo=Information
#XMSG:shows information that deployment cannot be proceeded.
deployErrorForReplicatedTables=You need to remove the replicated data in the Remote Table Monitor \n before you can proceed with deployment.
#XMSG:shows information that deployment cannot be proceeded.
deployErrorForPartitionFiltersNotSupported=You need to remove the filters and delete the partition in the Remote Table Monitor \n before you can proceed with deployment.
#XMSG:shows information that deployment cannot be proceeded.
deployErrorForFiltersNotSupported=You need to remove the filters before \n you can proceed with deployment.
#XMSG:shows information that deployment cannot be proceeded.
deployErrorForPartitionNotSupported=You need to delete the partition in the Remote Table Monitor \n before you can proceed with deployment.
#XMSG:Shows when the underlying source table is deleted and refresh is performed on DWC
errorMsgForTableNotExist=The remote table "{0}" does not exist.
#XMSG:Shows when the open SQL schema table is deleted and refresh is performed on DWC
errorMsgTableNotExist=The table "{0}" does not exist.
#XBUT: Drop down menu button to load new snapshot
loadNewSnapshot=Load New Snapshot
#XBUT: Drop down menu button to load new snapshot
loadNewSnapshotNew=Start Data Replication
#XBUT: Drop down menu button to remove replicated data
removeReplicatedData=Remove Replicated Data
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationNew=Enable Real-Time Data Replication
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplication=Enable Real-Time Access
#XBUT: Drop down menu button to disable real time replication
disableRealTimeReplication=Disable Real-Time Access
#XBUT: Drop down menu button to disable real time replication
disableRealTimeReplicationNew=Disable Real-Time Data Replication
#XBUT: Table replication menu button
tableReplication=Data Replication
#XBUT: Table replication menu button
tableReplicationNew=Data Replication
#XBUT: Schedule replication menu button
scheduleReplication=Schedule Replication
#XBUT: Refresh replication menu button
refreshReplication=Refresh Replication
#XBUT: Refresh button for remote only tables
refreshfederated=Refresh
#XTXT: Create schedule replication description
createScheduleReplicationDescription=Create schedule replication
#XTXT: Edit schedule replication description
editScheduleReplicationDescription=Edit schedule replication
#XTXT: Delete schedule replication description
DeleteScheduleReplicationDescription=Delete schedule replication
#XMSG: Refreshed successfully message
refreshedSuccessfully=Successful refresh
#XTXT: Last updated replication
lastUpdated=Last updated
#XTXT: Refresh frequency
refreshFrequency=Refresh Frequency
scheduled=Scheduled
#XTXT: Paused
paused=Paused
@addParentChild=Add Parent-Child
#XTXT: Label for add column
addColumnText=Add Columns
#XMSG: New columns message
newColumnLabelText=New Columns in Remote Source
#XMSG: Excluded columns message
excludedColText=Previously Excluded Columns
#XMSG: Column deletion error list message
deleteRemoteColErrorTxt=The constraints below prevented the selected columns from being deleted:
#XMSG: Key column error
keyColErrorTxt=This is a Key Column.
#XMSG: Association Column error
associationColErrorTxt=This column is part of an Association.
#XMSG: partitoin fetch error
partitionFetchErrorTxt=An error occurred while getting the Partition Data for this table.
#XMSG: Partition column error
partitionColErrorTxt=This column is used in a Partition.
#XMSG: Filter column error
filterColErrorTxt=This column is used in Filters.
#XMSG: Hierarchy column error
hierColErrorTxt=This column is part of a Hierarchy.
#XMSG: Ok Text
okText=OK
#XMSG: Cancel text
canceltext=Cancel
#XMSG: Ignore text
ignoreText=Ignore
#XMSG: Update text
updateText=Update
#XMSG: Info text for refresh remote table columns
refreshColumnInfoText=The remote table source contains column updates. Select the updates to apply to this table.
#XTXT: Label for column updates
updateColumnText=Column updates
#XMSG: search text
search=Search Column
#XMSG: No updates text
noUpdatesText=There are no updates available for this table.
#XMSG-Validation messages
@emptyFilterValue=Enter a valid filter value.

#XMSG: lower bound error text
lowerbounderrormsg=The lower bound must be lower than the higher bound.
#XMSG: lower bound error text for string
lowerbounderrormsgforString=The string "{0}" is bigger than "{1}".
#XMSG: higher bound error text
higherbounderrormsg=The higher bound must be higher than the lower bound.
#XMSG: higher bound error text for string
higherbounderrormsgforString=The string "{1}" is smaller than "{0}".
#XMSG Error msg for missing values
emptyBoundValuesErrorMsg=Specify low and high values.
VAL_LENGTH_EXCEED=The length of the value should not exceed {0}.
VAL_DEFAULT_LENGTH_EXCEED=The length of the value is too large. Enter a smaller value.
VAL_ENTER_VALID_INT=Enter a valid integer.
VAL_ENTER_VALID_DECIMAL=Enter a valid decimal value with precision {0} and scale {1}.
VAL_ENTER_VALID_DECIMAL_VALUE=Enter a decimal value.
VAL_ENTER_VALID_DATE=Enter a valid date.
VAL_ENTER_VALID_NUMBER=Enter a valid number.
VAL_DUPLICATE_FILTER=Duplicate filter value.
VAL_DEFAULT_RANGE_EXCEED_TINYINT=Enter a valid integer with a value between 0 and 255.
VAL_DEFAULT_RANGE_EXCEED_SMALLINT=Enter a valid integer with a value between -32768 and 32767.
VAL_DEFAULT_RANGE_EXCEED_INT=Enter a valid integer with a value between -2147483648 and 2147483647.
VAL_DEFAULT_RANGE_EXCEED_BIGINT=Enter a valid integer with a value between -9223372036854775808 and 9223372036854775807.
#XFLD: Placeholder for time format
@timeFormat=hh:mm:ss
#XFLD: Placeholder for date and time format
@dateTimeFormat=DD/MM/YYYY, hh:mm:ss
@param_true=true
@param_false=false
@none=None
#XFLD: Placeholder for date format Filter
@dateFormatFilter=YYYY-MM-DD
#XFLD: Placeholder for time format Filter
@timeFormatFilter=HH:mm:ss
#XFLD: Placeholder for date and time format Filter
@dateTimeFormatFilterNew=YYYY-MM-DD HH:mm:ss
@upgradedpagent=Refresh the remote table before creating a filter. Make sure, that the connection of the remote table uses a data provisioning agent with a version >= 2.5.4.1.
@editCompoundKey=Edit Compound Key
#XFLD: view Details link
VIEW_ERROR_DETAILS=View Details
@parentChildHierarchy=Parent-Child Hierarchy:
@hierarchyParentList=Parent:
@hierarchyChildList=Child:
@removeRepresentativeKey=Do you want to remove "{0}" representative key?
@remove=Remove
#XTIT: Header for Table
txt_columns=Technical Name
txt_new_columns=New Columns in Remote Source
txt_error=Error
#XMSG
txt_errorMsgRefreshConfirmDlg=You must add all new key columns of the source to the remote table.
#XMSG
txt_table_dropped=The remote table was deleted from the remote source. Please delete it from the repository.
#XBTN: Button to close the dialog
txt_close=Close
txt_statisticsMenu=Statistics
txt_createStatistics=Create Statistics
txt_delStatistics=Delete Statistics
#XFLD
txt_stat_lastUpdated=Statistics Last Updated
txt_statistics_type=Statistics Type
#XMSG: Replication will change
txt_replication_change=Replication type will be changed.
txt_repl_viewdetails=View Details
msgStripDataExists=Local table data already exists and must be deleted before you can define partitions.
#XTIT Define Partitions dialog header
tit_definePartitions=Define Partitions
partitionSectionHeader=Partitions
GreaterThanOrEqualTo=>=
#XFLD: Label text
lowValPlaceHolder=Enter low value
#XFLD: Label text
HighValPlaceHolder=Enter high value
lessThan=<
#XFLD: Label text
AddRange=Add Partition
#XFLD: Label text
deleteRange=Delete Partition
attributeLbl=Attribute:
columnLbl=Column:
#XBTN Button to cancel partition dialog
cancel=Cancel
Name=Name
Partition_Range=Partition Range
Edit=Edit
Delete=Delete
deletePartitionLabel=Delete Partition
editPartitionLabel=Edit Partition
resetPartitionLabel=Reset Partition
resetPartitionsConfirmation=Are you sure you want to reset partitions from extensions to the main definition? This will remove any partition overrides.
resetPartitionsSuccess=Partitions have been successfully reset to the main definition.

#XMSG: Column change confirmation text
columnChangeConfirmationTxt=You are about to change the partitioning column and delete existing partitioning.
#XMSG: Partition type change confirmation text
partitionTypeChangeConfirmationTxt=You are about to change the partitioning type and delete existing partitioning.
#XMSG: unsaved data msg
unsavedDataTxt=You have unsaved data. Do you want to continue?
#XMSG: If the table does not contain key columns, show warning that key cannot be defined again
warn_noKeys_defined=After creating partitions, you can no longer define key columns.
#XMSG: Message strip to display information that key property cannot be changed for partition column
msg_partition_withoutKeys=Key property cannot be changed while partition exists.
msg_partition_withKeys=Key property of partitioning column cannot be changed.
txt_learn_more=Learn More.
txt_addAtleastoneCol=The table "{0}" does not have any columns. Please add at least one column to proceed.
txt_no_valid_partitionCol=There are no valid columns that can be used for partitioning.
#XBTN Button to continue the partitions creation even if there are no key columns defined
txt_continueAnyway=Continue Anyway
#XBTN Delete button in the confirmation dialog to delete partitions
txt_delete=Delete
txt_delete_partitions=Delete partitions?
#XTIT: Title of refresh confirmation dialog
tit_refreshValidationDlg=Refresh
txt_refreshValidationGenericMessage=The source has been modified in the remote system. Column deletions and modifications must be applied.
txt_refreshValidationMsg=You can choose to add any or all of the new columns to the remote table definition. By default, only new key columns will be added.
txt_new_columns_header=New Columns in Source Table
#XBTN:Apply refresh action
txt_apply_refresh=Apply
#XBTN Cancel refresh action
txt_refresh_cancel=Cancel
txt_tableRefreshApplied=Refresh has already been applied for the table "{0}". Deploy the remote table to use the updated properties.
txt_no_data=No data found.
txt_confirmrestoreRefresh=If you restore the deployed version, the replication runtime will fail, because the metadata have changed in the source.\n To resolve these runtime errors, you will have to refresh the table, apply the changes and redeploy your remote table. \n Do you want to continue?
txt_restoreDeployedVersion= Restore Deployed Version
txt_restoreDeployedVersionNew=Revert to Deployed Version
txt_continue=Continue
allColumsSelectedPartitionTextNew=You cannot select all columns as partition columns. Please deselect at least one column.
connectionIsUnavailable=Currently, the source system is unavailable. Reconnect the source system to perform refresh.
restoreErrorLtf=The local table (File) can't be restored to a previous version because it contains data that can't be physically deleted.
restoreErrorHana=The local table can't be restored to a previous version because the partition definition has changed since the last version. Delete the data and try again.

#XBTN: Change Source
changeSource=Change Source
#XMSG:Information to say there is no data in table
txt_norecords=The table contains no records to delete.
tit_norecords=Delete
tit_read_only=Read-only
readOnlyInfo=This local table (file) has been created by {0}. You can not edit this table.
txt_generate=Generate
txt_cancel=Cancel
#XLBL
lbl_num_hashPartitions=Number of Hash Partitions:
lblType=Type
lblPartitionType=Partition Type
lblDefinition=Definition
confirmText=Confirm
#XMSG
txtPositiveInteger=Enter a valid positive integer
