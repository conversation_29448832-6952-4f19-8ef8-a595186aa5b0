enterValidModelName=Informe um nome de modelo válido.
enterModelName=Informe um nome de modelo.
key=Chave
null=Não é permitido usar valor nulo
fieldName=Nome da coluna
length=Comprimento
precision=Precisão
scale=Escala
description=Descrição
type=Tipo
dataView=Visão de dados
details=Detalhes
showHideSourceTree=Mostrar ou ocultar árvore de fontes
mode=Modo
general=Geral
remote=Remoto
viewJsonEditor=Exibir editor de JSON
closeContext=Fechar contexto
validate=Validar
save=Salvar
deploy=Implementar
dataPreview=Visualização de dados
viewEditor=Exibir editor de tabela
accessType=Tipo de acesso
#XTIT Save Dialog param
modelNameTable=Tabela

changeAttributesToMeasures=Converter atributos selecionados em medidas
changeMeasuresToAttributes=Converter medidas selecionadas em atributos
missingAttributesSelection=Nenhum atributo selecionado
missingMeasuresSelection=Nenhuma medida selecionada
attributesAndMeasuresAmountTitle=Atributos e medidas ({0})
openInMonitor=Abrir no Monitor
gotoStatisticsMonitor=Ir para Estatísticas de tabelas remotas
gotoMonitor=Ir para Monitor de tabelas remotas
#XTIT: Title for Filter section
filterTitle=Filtros ({0})
#XMSG:Button to change remote table source
changeRemoteTableSource=Alterar fonte de tabelas remotas
#XTIT: Title of refresh confirmation dialog
confirmRefreshDialogTitle=Importar
#XMSG: text in confirmation dialog that a user shall confirm the Refresh
confirmRefreshDialogContent=Você está prestes a importar todas as alterações da fonte subjacente.\n Deseja continuar?
#XMSG:text in confirmation dialog
refreshConfirmGenericMessage=Você está prestes a importar todas as alterações da fonte subjacente.
refreshMessageRemote=Novas colunas foram adicionadas à fonte. Selecione as colunas que você deseja adicionar à tabela remota.
refreshMessageReplicate=Novas colunas foram adicionadas à fonte. Selecione as colunas que você deseja adicionar à tabela remota. Lembre-se de que você precisa remover os dados replicados quando novas colunas são adicionadas.
#XBUT: Confirm Button in confirmation dialog, an additional button will be "Cancel"
confirmRefreshButton=Importar
#XMSG: Toaster to display table up to date
txttableuptodate=Sua tabela está atualizada.
#XMSG:Message to show refreshing
importProgressmsg=Importando alterações de {0}
#XMSG:Message to show refreshing
importProcessing=Procurando alterações na fonte...
#XMSG:Toaster to tell import completion
importCompleted=Importação concluída
importFailed=Falha na importação
#XTIT: Title of deploy error dialog
titError=Erro
titInfo=Informações
#XMSG:shows information that deployment cannot be proceeded.
deployErrorForReplicatedTables=Você precisa remover os dados replicados no Monitor de tabelas remotas \n antes para continuar a implementação.
#XMSG:shows information that deployment cannot be proceeded.
deployErrorForPartitionFiltersNotSupported=Você precisa remover os filtros e excluir a partição no Monitor de tabelas remotas \n antes para continuar a implementação.
#XMSG:shows information that deployment cannot be proceeded.
deployErrorForFiltersNotSupported=Você precisa remover os filtros antes \n para continuar a implementação.
#XMSG:shows information that deployment cannot be proceeded.
deployErrorForPartitionNotSupported=Você precisa excluir a partição no Monitor de tabelas remotas \n antes para continuar a implementação.
#XMSG:Shows when the underlying source table is deleted and refresh is performed on DWC
errorMsgForTableNotExist=A tabela remota "{0}" não existe.
#XMSG:Shows when the open SQL schema table is deleted and refresh is performed on DWC
errorMsgTableNotExist=A tabela "{0}" não existe.
#XBUT: Drop down menu button to load new snapshot
loadNewSnapshot=Carregar novo instantâneo
#XBUT: Drop down menu button to load new snapshot
loadNewSnapshotNew=Iniciar replicação de dados
#XBUT: Drop down menu button to remove replicated data
removeReplicatedData=Remover dados replicados
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationNew=Ativar replicação de dados em tempo real
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplication=Ativar acesso em tempo real
#XBUT: Drop down menu button to disable real time replication
disableRealTimeReplication=Desativar acesso em tempo real
#XBUT: Drop down menu button to disable real time replication
disableRealTimeReplicationNew=Desativar replicação de dados em tempo real
#XBUT: Table replication menu button
tableReplication=Replicação de dados
#XBUT: Table replication menu button
tableReplicationNew=Replicação de dados
#XBUT: Schedule replication menu button
scheduleReplication=Programar replicação
#XBUT: Refresh replication menu button
refreshReplication=Atualizar replicação
#XBUT: Refresh button for remote only tables
refreshfederated=Atualizar
#XTXT: Create schedule replication description
createScheduleReplicationDescription=Criar programação de replicação
#XTXT: Edit schedule replication description
editScheduleReplicationDescription=Editar programação de replicação
#XTXT: Delete schedule replication description
DeleteScheduleReplicationDescription=Excluir programação de replicação
#XMSG: Refreshed successfully message
refreshedSuccessfully=Atualização com sucesso
#XTXT: Last updated replication
lastUpdated=Última atualização
#XTXT: Refresh frequency
refreshFrequency=Frequência de atualização
scheduled=Programado
#XTXT: Paused
paused=Pausado
@addParentChild=Adicionar pai–filho
#XTXT: Label for add column
addColumnText=Adicionar colunas
#XMSG: New columns message
newColumnLabelText=Novas colunas na fonte remota
#XMSG: Excluded columns message
excludedColText=Colunas excluídas anteriormente
#XMSG: Column deletion error list message
deleteRemoteColErrorTxt=As restrições abaixo impediram que as colunas selecionadas fossem excluídas:
#XMSG: Key column error
keyColErrorTxt=Esta é uma coluna-chave.
#XMSG: Association Column error
associationColErrorTxt=Esta coluna é parte de uma associação.
#XMSG: partitoin fetch error
partitionFetchErrorTxt=Ocorreu um erro ao obter os dados de partição para esta tabela.
#XMSG: Partition column error
partitionColErrorTxt=Esta coluna é usada em uma Partição.
#XMSG: Filter column error
filterColErrorTxt=Esta coluna é usada em Filtros.
#XMSG: Hierarchy column error
hierColErrorTxt=Esta coluna é parte de uma Hierarquia.
#XMSG: Ok Text
okText=OK
#XMSG: Cancel text
canceltext=Cancelar
#XMSG: Ignore text
ignoreText=Ignorar
#XMSG: Update text
updateText=Atualizar
#XMSG: Info text for refresh remote table columns
refreshColumnInfoText=A fonte da tabela remota contém atualizações de coluna. Selecione as atualizações para aplicar a esta tabela.
#XTXT: Label for column updates
updateColumnText=Atualizações de coluna
#XMSG: search text
search=Procurar coluna
#XMSG: No updates text
noUpdatesText=Não há atualizações disponíveis para esta tabela.
#XMSG-Validation messages
@emptyFilterValue=Insira um valor de filtro válido.

#XMSG: lower bound error text
lowerbounderrormsg=O limite inferior deve ser menor do que o limite superior.
#XMSG: lower bound error text for string
lowerbounderrormsgforString=A string "{0}" é maior do que "{1}".
#XMSG: higher bound error text
higherbounderrormsg=O limite superior deve ser maior do que o limite inferior.
#XMSG: higher bound error text for string
higherbounderrormsgforString=A string "{1}" é menor do que "{0}".
#XMSG Error msg for missing values
emptyBoundValuesErrorMsg=Especifique os valores inferior e superior.
VAL_LENGTH_EXCEED=O comprimento do valor não pode exceder {0}.
VAL_DEFAULT_LENGTH_EXCEED=O comprimento do valor é muito grande. Insira um valor menor.
VAL_ENTER_VALID_INT=Insira um número inteiro válido.
VAL_ENTER_VALID_DECIMAL=Insira um valor decimal válido com precisão {0} e escala {1}.
VAL_ENTER_VALID_DECIMAL_VALUE=Insira um valor decimal.
VAL_ENTER_VALID_DATE=Insira uma data válida.
VAL_ENTER_VALID_NUMBER=Insira um número válido.
VAL_DUPLICATE_FILTER=Duplique o valor do filtro.
VAL_DEFAULT_RANGE_EXCEED_TINYINT=Insira um inteiro válido com valor entre 0 e 255.
VAL_DEFAULT_RANGE_EXCEED_SMALLINT=Insira um inteiro válido com valor entre -32768 e 32767.
VAL_DEFAULT_RANGE_EXCEED_INT=Insira um inteiro válido com valor entre -2147483648 e 2147483647.
VAL_DEFAULT_RANGE_EXCEED_BIGINT=Insira um inteiro válido com valor entre -9223372036854775808 e 9223372036854775807.
#XFLD: Placeholder for time format
@timeFormat=hh:mm:ss
#XFLD: Placeholder for date and time format
@dateTimeFormat=DD/MM/AAAA, hh:mm:ss
@param_true=verdadeiro
@param_false=falso
@none=Nenhum
#XFLD: Placeholder for date format Filter
@dateFormatFilter=AAAA-MM-DD
#XFLD: Placeholder for time format Filter
@timeFormatFilter=HH:mm:ss
#XFLD: Placeholder for date and time format Filter
@dateTimeFormatFilterNew=AAAA-MM-DD HH:mm:ss
@upgradedpagent=Atualize a tabela remota antes de criar um filtro. Verifique se a conexão da tabela remota usa um agente de provisionamento de dados com versão >= 2.5.4.1.
@editCompoundKey=Editar chave composta
#XFLD: view Details link
VIEW_ERROR_DETAILS=Exibir detalhes
@parentChildHierarchy=Hierarquia pai–filho:
@hierarchyParentList=Pai:
@hierarchyChildList=Filho:
@removeRepresentativeKey=Remover a chave representativa "{0}"?
@remove=Remover
#XTIT: Header for Table
txt_columns=Nome técnico
txt_new_columns=Novas colunas na fonte remota
txt_error=Erro
#XMSG
txt_errorMsgRefreshConfirmDlg=Você precisa adicionar todas as novas colunas de chave da fonte à tabela remota.
#XMSG
txt_table_dropped=A tabela remota foi excluída da fonte remota. Exclua essa tabela do repositório.
#XBTN: Button to close the dialog
txt_close=Fechar
txt_statisticsMenu=Estatística
txt_createStatistics=Criar estatística
txt_delStatistics=Excluir estatística
#XFLD
txt_stat_lastUpdated=Última atualização da estatística
txt_statistics_type=Tipo de estatística
#XMSG: Replication will change
txt_replication_change=O tipo de replicação será alterado.
txt_repl_viewdetails=Exibir detalhes
msgStripDataExists=Os dados da tabela local já existem e devem ser excluídos antes de definir as partições.
#XTIT Define Partitions dialog header
tit_definePartitions=Definir partições
partitionSectionHeader=Partições
GreaterThanOrEqualTo=>=
#XFLD: Label text
lowValPlaceHolder=Inserir valor inferior
#XFLD: Label text
HighValPlaceHolder=Inserir valor superior
lessThan=<
#XFLD: Label text
AddRange=Adicionar partição
#XFLD: Label text
deleteRange=Excluir partição
attributeLbl=Atributo:
columnLbl=Coluna:
#XBTN Button to cancel partition dialog
cancel=Cancelar
Name=Nome
Partition_Range=Intervalo de partição
Edit=Editar
Delete=Excluir
deletePartitionLabel=Excluir partição
editPartitionLabel=Editar partição

#XMSG: Column change confirmation text
columnChangeConfirmationTxt=Você está prestes a alterar a coluna de particionamento e excluir o particionamento existente.
#XMSG: Partition type change confirmation text
partitionTypeChangeConfirmationTxt=Você está prestes a alterar o tipo de particionamento e excluir o particionamento existente.
#XMSG: unsaved data msg
unsavedDataTxt=Você tem dados não salvos. Deseja continuar?
#XMSG: If the table does not contain key columns, show warning that key cannot be defined again
warn_noKeys_defined=Depois de criar partições, você não pode mais definir colunas de chave.
#XMSG: Message strip to display information that key property cannot be changed for partition column
msg_partition_withoutKeys=Não é possível alterar a propriedade de chave enquanto a partição existe.
msg_partition_withKeys=Não é possível alterar a propriedade de chave da coluna de particionamento.
txt_learn_more=Saiba mais.
txt_addAtleastoneCol=A tabela "{0}" não tem nenhuma coluna. Adicione, pelo menos, uma coluna para continuar.
txt_no_valid_partitionCol=Não há colunas válidas para serem usadas no particionamento.
#XBTN Button to continue the partitions creation even if there are no key columns defined
txt_continueAnyway=Continuar mesmo assim
#XBTN Delete button in the confirmation dialog to delete partitions
txt_delete=Excluir
txt_delete_partitions=Excluir partições?
#XTIT: Title of refresh confirmation dialog
tit_refreshValidationDlg=Atualizar
txt_refreshValidationGenericMessage=A origem foi modificada no sistema remoto. Exclusões e modificações de coluna devem ser aplicadas.
txt_refreshValidationMsg=Você pode optar por adicionar qualquer uma ou todas as novas colunas à definição de tabela remota. Por padrão, só novas colunas-chave serão adicionadas.
txt_new_columns_header=Novas colunas na tabela de origem
#XBTN:Apply refresh action
txt_apply_refresh=Aplicar
#XBTN Cancel refresh action
txt_refresh_cancel=Cancelar
txt_tableRefreshApplied=A atualização já foi aplicada para a tabela "{0}". Implemente a tabela remota para usar as propriedades atualizadas.
txt_no_data=Nenhum dado encontrado.
txt_confirmrestoreRefresh=Se você restaurar a versão implementada, ocorrerá falha no tempo de execução da replicação porque os metadados foram alterados na origem.\n Para resolver esses erros de tempo de execução, você terá que atualizar a tabela, aplicar as alterações e reimplementar sua tabela remota. \n Continuar?
txt_restoreDeployedVersion=Restaurar versão implementada
txt_restoreDeployedVersionNew=Reverter para versão implementada
txt_continue=Continuar
allColumsSelectedPartitionTextNew=Você não pode selecionar todas as colunas como colunas de partição. Desmarque pelo menos uma coluna.
connectionIsUnavailable=O sistema de origem está indisponível no momento. Reconecte o sistema de origem para efetuar a atualização.
restoreErrorLtf=Não é possível restaurar a tabela local (arquivo) para uma versão anterior porque ela contém dados que não podem ser excluídos fisicamente.
restoreErrorHana=Não é possível restaurar a tabela local para uma versão anterior porque a definição de partição foi alterada desde a última versão. Exclua os dados e tente novamente.

#XBTN: Change Source
changeSource=Alterar fonte
#XMSG:Information to say there is no data in table
txt_norecords=A tabela não contém registros para excluir.
tit_norecords=Excluir
tit_read_only=Somente leitura
readOnlyInfo=Esta tabela local (arquivo) foi criada por {0}. Você não pode editar esta tabela.
txt_generate=Gerar
txt_cancel=Cancelar
#XLBL
lbl_num_hashPartitions=Número de partições hash:
lblType=Tipo
lblPartitionType=Tipo de partição
lblDefinition=Definição
confirmText=Confirmar
#XMSG
txtPositiveInteger=Insira um inteiro positivo válido
