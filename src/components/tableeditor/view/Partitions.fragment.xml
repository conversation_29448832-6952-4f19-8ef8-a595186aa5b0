<!-- @format -->

<core:FragmentDefinition
  xmlns="sap.m"
  xmlns:core="sap.ui.core"
  xmlns:l="sap.ui.layout"
  xmlns:f="sap.ui.layout.form"
  xmlns:ac="sap.cdw.components.reuse.control.actionchecker"
  xmlns:cd="http://schemas.sap.com/sapui5/extension/sap.ui.core.CustomData/1"
>
  <ac:ActionChecker
    id="tableEditorPartitionAc"
    class="sapUiContentPadding"
    hanaState="{path:'circuitbreaker>/DataHANA', formatter:'.formatters.hanaStateFormatter'}"
    hanaProvisioningState="{path:'circuitbreaker>/DataHANAProvisioningState', formatter:'.formatters.hanaStateFormatter'}"
    actionControlIds="DefinePartition"
    hiddenMode="true"
  >
  </ac:ActionChecker>
  <VBox>
    <!-- Illustrated Message for No Partitions when feature flag is on -->
    <IllustratedMessage
      id="noPartitionsIllustration"
      title="{i18n_table>noPartitionsTitle}"
      description="{i18n_table>noPartitionsDescription}"
      illustrationType="sapIllus-EmptyList"
      illustrationSize="Dialog"
      visible="{parts:['featureflags>/DWCO_MODELING_ANNOTATE_PARTITIONS', 'galileiModel>/partitions', 'galileiModel>/allowExtensions', 'galileiModel>/partitionsFromExtension'], formatter:'.noPartitionsIllustratedMessageVisibleFormatter'}"
    >
      <additionalContent>
        <Button
          id="DefinePartitionFromIllustration"
          text="{= ${galileiModel>/fileStorage} ? ${i18n>@createPartitionsLtf} : ${i18n>@createPartitions}}"
          press="openPartitionsDialog"
          enabled="{parts:['galileiModel>/allowExtensions', 'galileiModel>/hasData', 'workbenchEnv>/canCreateOrUpdateModel', 'galileiModel>/partitions'], formatter:'.partitionCreateEnabledFormatter'}"
          type="Emphasized"
          cd:actionId="monitoring/editor/changeRemoteTableSource"
        />
      </additionalContent>
    </IllustratedMessage>
    <VBox width="50%" visible="{galileiModel>/hasData}">
      <MessageStrip
        id="msgStripDataExists"
        type="Information"
        enableFormattedText="true"
        text="{i18n_table>msgStripDataExists}"
        visible="{parts:['galileiModel>/hasData','galileiModel>/contentOwner'],formatter:'.partitionNoDataMsgStripVisibilityFormatter',mode:'OneWay'}"
        showIcon="true"
        showCloseButton="true"
        class="sapUiTinyMarginBottom"
      />
    </VBox>
    <Button
      id="DefinePartition"
      text="{= ${galileiModel>/fileStorage} ? ${i18n>@createPartitionsLtf} : ${i18n>@createPartitions}}"
      press="openPartitionsDialog"
      enabled="{parts:['galileiModel>/allowExtensions', 'galileiModel>/hasData', 'workbenchEnv>/canCreateOrUpdateModel', 'galileiModel>/partitions'], formatter:'.partitionCreateEnabledFormatter'}"
      visible="{parts:['featureflags>/DWCO_MODELING_ANNOTATE_PARTITIONS', 'galileiModel>/partitions'], formatter:'.createPartitionBtnVisibleFormatter'}"
      cd:actionId="monitoring/editor/changeRemoteTableSource"
    >
    </Button>
    <!-- partition Definition Section-->

    <HBox
      class="sapUiLargeMarginEnd"
      visible="{parts:['galileiModel>/partitions', 'galileiModel>/fileStorage'], formatter:'.partitionsDefinitionVisibilityFormatter'}"
      width="50%"
      justifyContent="SpaceBetween"
    >
      <HBox>
        <Label
          text="{path:'galileiModel>/dataCategory', formatter:'.columnLabelFormatter'}"
          class="sapUiTinyMargin"
          labelFor="partitionSectionColumn"
          visible="{= !${featureflags>/DWCO_TABLE_PARTITIONING_HASH}}"
        />
        <Select
          id="partitionColListSection"
          forceSelection="true"
          selectedKey=""
          class="sapUiTinyMarginBeginEnd"
          width="200px"
          editable="false"
          visible="{= !${featureflags>/DWCO_TABLE_PARTITIONING_HASH}}"
        >
          <core:ListItem
            icon="{path:'galileiModel>/partitions/0/partitionDatatype', formatter:'.dataTypeIconFormatter' }"
            key="{galileiModel>/partitions/0/id}"
            text="{galileiModel>/partitions/0/id}"
          />
        </Select>
        <Label
          text="{i18n_table>lblPartitionType}"
          class="sapUiTinyMargin groupTitleHeader"
          visible="{featureflags>/DWCO_TABLE_PARTITIONING_HASH}"
        />
      </HBox>
      <HBox justifyContent="End">
        <Button
          id="EditPartitionBtn"
          text="{i18n_table>Edit}"
          tooltip="{i18n_table>editPartitionLabel}"
          press=".onEditPartitions"
          type="Transparent"
          enabled="{parts:['galileiModel>/allowExtensions', 'galileiModel>/hasData', 'workbenchEnv>/canCreateOrUpdateModel'], formatter:'.partitionEditDeleteEnabledFormatter'}"
        >
        </Button>
        <Button
          id="DeletePartitionButton"
          tooltip="{i18n_table>deletePartitionLabel}"
          text="{i18n_table>Delete}"
          type="Transparent"
          press=".onDeletePartition"
          enabled="{parts:['galileiModel>/allowExtensions', 'galileiModel>/hasData', 'workbenchEnv>/canCreateOrUpdateModel'], formatter:'.partitionEditDeleteEnabledFormatter'}"
        >
        </Button>
        <Button
          id="ResetPartitionButton"
          tooltip="{i18n_table>resetPartitionLabel}"
          text="{i18n_table>Reset}"
          type="Transparent"
          press=".onResetPartitions"
          visible="{parts:['galileiModel>/allowExtensions', 'galileiModel>/partitionsFromExtension'], formatter:'.resetPartitionsVisibleFormatter'}"
        >
        </Button>
      </HBox>
    </HBox>

    <!-- PartitionType-->
    <VBox
      visible="{parts:['galileiModel>/partitions', 'galileiModel>/fileStorage'], formatter:'.partitionsDefinitionVisibilityFormatter'}"
    >
      <Label
        text="{i18n_table>lblType}"
        class="sapUiTinyMarginBeginEnd sapUiTinyMarginBottom"
        labelFor="partitionType"
        visible="{featureflags>/DWCO_TABLE_PARTITIONING_HASH}"
      />
      <Input
        id="partitionType"
        value="{path:'galileiModel>/partitions/0/partitionType',formatter:'.partitionTypeFormatter'}"
        visible="{featureflags>/DWCO_TABLE_PARTITIONING_HASH}"
        enabled="false"
        editable="false"
        width="50%"
        class="sapUiTinyMarginBeginEnd sapUiTinyMarginBottom"
      />

      <Label
        text="{i18n_table>lblDefinition}"
        class="sapUiTinyMarginBeginEnd sapUiSmallMarginTop groupTitleHeader"
        visible="{featureflags>/DWCO_TABLE_PARTITIONING_HASH}"
      />
      <!-- Column Info-->
      <VBox visible="{= ${galileiModel>/partitions/0/partitionType} === 'RANGE'}">
        <Label
          text="{path:'galileiModel>/dataCategory', formatter:'.columnLabelFormatter'}"
          class="sapUiTinyMargin"
          labelFor="partitionSectionColumnNew"
          visible="{featureflags>/DWCO_TABLE_PARTITIONING_HASH}"
        />
        <Select
          id="partitionColListSectionNew"
          forceSelection="true"
          selectedKey=""
          class="sapUiTinyMarginBeginEnd"
          width="50%"
          editable="false"
          visible="{featureflags>/DWCO_TABLE_PARTITIONING_HASH}"
        >
          <core:ListItem
            icon="{path:'galileiModel>/partitions/0/partitionDatatype', formatter:'.dataTypeIconFormatter' }"
            key="{galileiModel>/partitions/0/id}"
            text="{galileiModel>/partitions/0/id}"
          />
        </Select>
      </VBox>

      <!-- Range Partition definition-->
      <VBox
        visible="{parts:['galileiModel>/partitions','galileiModel>/partitions/0/partitionType','galileiModel>/fileStorage'], formatter:'.partitionRangeVisibleFormatter'}"
      >
        <Table
          id="rangeList"
          items="{galileiModel>/partitions/0/ranges}"
          class="rangeTableBorder sapUiSmallMarginBottom"
          width="50%"
          growing="true"
          growingThreshold="10"
          growingScrollToLoad="false"
          selectionChange="onRangeSelectionChange"
        >
          <columns>
            <Column width="15%">
              <Label text="{i18n_table>Name}" tooltip="{i18n_table>Name}" />
            </Column>
            <Column>
              <Label text="{i18n_table>Partition_Range}" tooltip="{i18n_table>Partition_Range}" />
            </Column>
          </columns>
          <items>
            <ColumnListItem>
              <cells>
                <HBox class="sapUiTinyMarginTop sapUiTinyMarginEnd">
                  <Text text="{path:'galileiModel>partitionId',formatter:'.partitionTextFormatter'}" wrapping="false" />
                </HBox>
                <HBox class="sapUiTinyMarginTop sapUiTinyMarginEnd">
                  <HBox class="sapUiTinyMarginTop sapUiSmallMarginEnd">
                    <Text text="{i18n_table>GreaterThanOrEqualTo}" wrapping="false" />
                  </HBox>
                  <HBox class="sapUiTinyMarginEnd">
                    <Input
                      id="lowInput"
                      value="{galileiModel>low}"
                      enabled="true"
                      placeholder="{i18n_table>lowValPlaceHolder}"
                      valueState="{lowValueState}"
                      valueStateText="{lowValueStateText}"
                      editable="false"
                    />
                  </HBox>
                  <HBox class="sapUiSmallMarginBegin sapUiTinyMarginTop sapUiSmallMarginEnd">
                    <Text text="{i18n_table>lessThan}" wrapping="false" />
                  </HBox>
                  <HBox class="sapUiTinyMarginEnd">
                    <Input
                      id="highInput"
                      value="{galileiModel>high}"
                      editable="false"
                      valueState="{highValueState}"
                      valueStateText="{highValueStateText}"
                    />
                  </HBox>
                </HBox>
              </cells>
            </ColumnListItem>
          </items>
        </Table>
      </VBox>
      <!-- Hash Partition definition-->
      <VBox
        visible="{parts:['galileiModel>/partitions/0/partitionType','galileiModel>/fileStorage'], formatter:'.partitionHashVisibleFormatter'}"
      >
        <Label text="{i18n_table>columnLbl}" class="sapUiTinyMargin" labelFor="hashedColumnsList" />
        <Input
          id="hashedColumnsList"
          value="{path:'galileiModel>/partitions/0/hashedColumns',formatter:'.hashColumnInfoFormatter'}"
          editable="false"
          class="sapUiTinyMargin"
          width="50%"
        />
        <Label
          text="{i18n_table>lbl_num_hashPartitions}"
          class="sapUiTinyMargin"
          editable="false"
          labelFor="hashPartitionNumber"
        />
        <Input
          id="hashPartitionNumber"
          type="number"
          class="sapUiTinyMargin"
          editable="false"
          width="50%"
          value="{galileiModel>/partitions/0/noOfHashPartitions}"
        />
      </VBox>
    </VBox>
    <!-- LTF related changes-->
    <OverflowToolbar
      width="30%"
      justifyContent="End"
      visible="{parts:['galileiModel>/partitions', 'galileiModel>/fileStorage'], formatter:'.partitionColumnsVisibleFormatter'}"
    >
      <ToolbarSpacer />
      <Button
        id="EditPartitionLtfBtn"
        text="{i18n_table>Edit}"
        tooltip="{i18n_table>editPartitionLabel}"
        press=".onEditPartitions"
        type="Transparent"
        enabled="{parts:['galileiModel>/allowExtensions', 'galileiModel>/hasData', 'workbenchEnv>/canCreateOrUpdateModel'], formatter:'.partitionEditDeleteEnabledFormatter'}"
      >
      </Button>
      <Button
        id="DeletePartitionLtfButton"
        tooltip="{i18n_table>deletePartitionLabel}"
        text="{i18n_table>Delete}"
        type="Transparent"
        press=".onDeletePartition"
        enabled="{parts:['galileiModel>/allowExtensions', 'galileiModel>/hasData', 'workbenchEnv>/canCreateOrUpdateModel'], formatter:'.partitionEditDeleteEnabledFormatter'}"
      >
      </Button>
      <Button
        id="ResetPartitionLtfButton"
        tooltip="{i18n_table>resetPartitionLabel}"
        text="{i18n_table>Reset}"
        type="Transparent"
        press=".onResetPartitions"
        visible="{parts:['galileiModel>/allowExtensions', 'galileiModel>/partitionsFromExtension'], formatter:'.resetPartitionsVisibleFormatter'}"
      >
      </Button>
    </OverflowToolbar>
    <VBox
      width="60%"
      visible="{parts:['galileiModel>/partitions', 'galileiModel>/fileStorage'], formatter:'.partitionColumnsVisibleFormatter'}"
    >
      <List id="ltfPartitionsList" width="50%" items="{galileiModel>/partitions}" growing="true">
        <StandardListItem title="{galileiModel>id}" />
      </List>
    </VBox>
  </VBox>
</core:FragmentDefinition>
