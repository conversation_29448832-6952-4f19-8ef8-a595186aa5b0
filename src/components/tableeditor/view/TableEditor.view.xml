<mvc:View
  controllerName="sap.cdw.components.tableeditor.controller.TableEditor"
  xmlns:core="sap.ui.core"
  xmlns="sap.m"
  xmlns:mvc="sap.ui.core.mvc"
  xmlns:form="sap.ui.layout.form"
  xmlns:layout="sap.ui.layout"
  xmlns:uxap="sap.uxap"
  xmlns:table="sap.ui.table"
  xmlns:pkgs="sap.skyline.tools.packageselector"
  xmlns:ai="sap.skyline.tools.ai"
  xmlns:dnd="sap.ui.core.dnd"
  xmlns:u="sap.ui.unified"
  xmlns:ac="sap.cdw.components.reuse.control.actionchecker"
  xmlns:cd="http://schemas.sap.com/sapui5/extension/sap.ui.core.CustomData/1"
  xmlns:dol="sap.cdw.components.reuse.control.dwcobjectlink"
  xmlns:dos="sap.cdw.components.reuse.control.dwcobjectstatus"
>
  <ac:ActionChecker
    id="tableEditorAc"
    class="sapUiContentPadding"
    hanaState="{path:'circuitbreaker>/DataHANA', formatter:'.formatters.hanaStateFormatter'}"
    hanaProvisioningState="{path:'circuitbreaker>/DataHANAProvisioningState', formatter:'.formatters.hanaStateFormatter'}"
    spaceType="{spaceDetails>/spaceType}"
    actionControlIds="tableReplicationMenu,scheduleActions,refreshReplicationStatus,navigationMenu,tableStatisticsMenu, changeRemoteTableSourceBtn, createAnalyticModelLink"
    hiddenMode="true"
  >
  </ac:ActionChecker>
  <uxap:ObjectPageLayout
    subSectionLayout="TitleOnLeft"
    upperCaseAnchorBar="false"
    sectionChange="onSectionChange"
    id="tableEditorPage"
  >

    <uxap:headerTitle>
      <uxap:ObjectPageDynamicHeaderTitle id="objectHeader">
        <uxap:heading>
          <HBox>
            <core:Icon
              visible="{= ${featureflags>/DWCO_MODELING_DFFERENTIATE_UNMANAGED_TABLES} === true}"
              src="{parts: ['galileiModel>/'], formatter:'.getTableIcon'}"
              class="sapUiTinyMarginTop sapUiTinyMarginEnd"
            />
            <ai:AIStatusPopoverButton
              visible="{= ${galileiModel>/hasAIChange} === true}"
              closeText="{i18n_erd>@closeText}"
              title="{i18n_erd>@titAIStatusPopover}"
              description="{i18n_erd>@txtAIStatusPopoverDesc}"
              tooltip="{i18n_erd>@totip_aistatus}">
           </ai:AIStatusPopoverButton>
            <Title text="{header>/label}" class="sapUiTinyMarginTop"></Title>
            <GenericTag
               text="{i18n_table>tit_read_only}"
               class="noTopMargin sapUiSmallMarginBegin"
               valueState="None"
               visible="{= ${featureflags>/DWCO_MODELING_DFFERENTIATE_UNMANAGED_TABLES} === true &amp;&amp; ${galileiModel>/contentOwner} === 'BW'}"
               press="showReadOnlyPopover">
				    </GenericTag>
          </HBox>
        </uxap:heading>

        <uxap:expandedContent>
          <HBox>
            <Text
              visible="{= ${featureflags>/DWCO_MODELING_DFFERENTIATE_UNMANAGED_TABLES} === true}"
              class="techincalNameHeaderCls"
              text="{header>/technicalName}"
            />
            <Text
              visible="{= ${featureflags>/DWCO_MODELING_DFFERENTIATE_UNMANAGED_TABLES} !== true}"
              text="{header>/technicalName}"
            />
          </HBox>
        </uxap:expandedContent>

        <!--uxap:actions>

      </uxap:actions-->
      </uxap:ObjectPageDynamicHeaderTitle>
    </uxap:headerTitle>
  <uxap:sections>
    <!-- GENERAL -->
      <uxap:ObjectPageSection
        id="generalSection"
        title="{i18n_erd>@general}"
        titleUppercase="false"
      >
        <uxap:subSections>
          <uxap:ObjectPageSubSection>
            <uxap:blocks>
              <form:SimpleForm
                id="entityPropertiesFormTable"
                editable="true"
                class="generalForm"
                layout="ResponsiveGridLayout"
                labelSpanXL="12"
                labelSpanL="12"
                labelSpanM="12"
                labelSpanS="12"
                adjustLabelSpan="true"
                columnsXL="1"
                columnsL="1"
                columnsM="1"
                emptySpanXL="6"
                emptySpanL="4"
                emptySpanM="2"
                singleContainerFullSize="true"
              >
                <form:content>
                  <!--Message Strip -->
                  <MessageStrip
                  id="msEntityHasAIChange"
                  text="{i18n_erd>@txtEntityHasAIChangeMsg}"
                  type="Information"
                  class="sapUiTinyMarginBottom"
                  showIcon="true"
                  visible="{= ${galileiModel>/hasAIChange} === true}"
                />
                  <!-- Business name -->
                  <Label text="{i18n_erd>@businessName}"></Label>
                  <Input
                    value="{
                            path: 'galileiModel>/label',
                            mode: 'OneWay'
                          }"
                    enabled="{galileiModel>/label__available/status}"
                    tooltip="{galileiModel>/label__available/messages/0}"
                    id="businessNameInput"
                    liveChange="onBusinessNameChange"
                    change="onBusinessNameSubmit"
                    maxLength="1000"
                  />
                  <!-- Technical name -->
                  <Label
                    text="{i18n_erd>@businessTechnicalName}"
                    required="true"
                  ></Label>
                  <Input
                    value="{
                      path: 'galileiModel>/technicalName',
                      mode: 'OneWay'
                    }"
                    enabled="{galileiModel>/technicalName__available/status}"
                    tooltip="{galileiModel>/technicalName__available/messages/0}"
                    id="technicalNameInput"
                    liveChange="onTechnicalNameChange"
                    change="onTechnicalNameSubmit"
                  />
                  <!-- Package Selector -->
                  <Label
                      text="{i18n_erd>@txtPackage}"
                      visible="{
                        parts: [
                          {path:'workbenchEnv>/packages'}
                        ],
                        formatter: '.displayPackageSelector'
                      }"
                    ></Label>
                  <VBox>
                    <pkgs:PackageSelector
                      id="packageSelector"
                      packages="{workbenchEnv>/packages}"
                      forceStatus="{
                        parts: [
                          {path:'privilege>/DWC_DATABUILDER'},
                          {path:'workbenchEnv>/forceStatus'},
                          {path:'galileiModel>/packageValue__available/status'}
                        ],
                        formatter: '.forceStatusPackageSelectorTable'
                      }"
                      packageValue="{galileiModel>/packageValue}"
                      packageStatus="{galileiModel>/packageStatus}"
                      packageSelectionChange="packageSelectionChange"
                      visible="{
                        parts: [
                          {path:'workbenchEnv>/packages'}
                        ],
                        formatter: '.displayPackageSelector'
                      }"
                    />
                  </VBox>

                  <!-- Semantic Usage -->
                  <Label text="{i18n_erd>@txtType}"></Label>
                  <VBox id="vBoxDataSetType">
                    <Select
                      id="dataSetTypeSel"
                      visible="{path:'galileiModel>/', formatter:'.oldControlVisibilityFormatter'}"
                      items="{DSTypeModel>/datasetTypes}"
                      selectedKey="{
                          path: 'galileiModel>/dataCategory',
                          mode: 'OneWay'
                        }"
                      change="onChangeType"
                      enabled="{galileiModel>/dataCategory__available/status}"
                      tooltip="{galileiModel>/dataCategory__available/messages/0}"
                      width="300%"
                    >
                      <core:Item
                        key="{DSTypeModel>type}"
                        text="{DSTypeModel>text}"
                      />
                    </Select>
                    <HBox>
                    <ai:AISelectWithRecommendation
                      visible="{path:'galileiModel>/', formatter:'.genAIControlVisibilityFormatter'}"
                      id="dataSetTypeSelAI"
                      items="{galileiModel>/possibleDataCategoriesAI}"
                      selectedKey="{
                        path: 'galileiModel>/dataCategory',
                        mode: 'OneWay'
                      }"
                      groupColumn="groupName"
                      recommendedColumn="recommended"
                      hasAIChange="{galileiModel>/hasAIChangeOnDataCategory}"
                      width="100%"
                      change="onChangeType"
                      enabled="{galileiModel>/dataCategory__available/status}"
                      tooltip="{galileiModel>/dataCategory__available/messages/0}"
                    >
                      <core:Item
                        key="{galileiModel>type}"
                        text="{galileiModel>text}"
                      />
                      <ai:layoutData>
                        <FlexItemData growFactor="1"/>
                      </ai:layoutData>
                    </ai:AISelectWithRecommendation>
                    <ai:AIReviewPopoverButton
                      id="reviewDataCategoryAIChange"
                      title="{i18n_erd>@reviewTitle}"
                      class="sapUiTinyMarginBegin"
                      visible="{= ${galileiModel>/hasAIChangeOnDataCategory} === true}"
                      enabled="{= ${galileiModel>/hasAIChangeOnDataCategory} === true}"
                      closeText="{i18n_erd>@revertChanges}"
                      tooltip1="{i18n_erd>@review}"
                      revertCallback=".onRevertDataCategoryAIChange"
                      info="{path: 'galileiModel>/reviewInfoDataCategory'}"
                    >
                    </ai:AIReviewPopoverButton>
                  </HBox>
                      <VBox class="sapUiTinyMarginTop" visible="{parts:[{path:'galileiModel>/dataCategory'}], formatter:'.dimensionTypeVisibleFormatter'}">
                        <Label
                          text="{i18n_erd>@txtDimensionType}"
                        ></Label>
                        <HBox>
                          <Select
                            id="dimensionTypeSel"
                            items="{galileiModel>/dimensionTypeList}"
                            selectedKey="{galileiModel>/dimensionType}"
                            change="onChangeDimensionType"
                            width="100%"
                          >
                            <core:Item
                              key="{galileiModel>type}"
                              text="{galileiModel>text}"
                            />
                            <layoutData>
                              <FlexItemData growFactor="1"/>
                            </layoutData>
                          </Select>
                          <Button
                            id="dimensionTypeSettingsBtn"
                            text="{i18n_erd>@txtDimensionTypeSettings}"
                            enabled="{= ${galileiModel>/dimensionType} === 'FISCAL_TIME'}"
                            press="onDimensionTypeSettingsClicked"
                            class="sapUiTinyMarginBegin"
                          ></Button>
                        </HBox>
                      </VBox>

                    <layoutData>
                      <FlexItemData growFactor="1" />
                    </layoutData>
                    <layoutData>
                      <layout:GridData span="XL6 L8 M10 S12" />
                    </layoutData>
                  </VBox>

                  <HBox>
                    <Link
                        id="createAnalyticModelLink"
                        text="{i18n_erd>@txtCreateAnalyticModel}"
                        visible="{= ${galileiModel>/isFact}===true}"
                        press="navigateToAnalyticModel"
                        enabled="{parts:['workbenchEnv>/toolbar/canUndo', 'privilege>/DWC_DATABUILDER/create', 'workbenchEnv>/spaceData/spaceType', 'workbenchEnv>/isVersioningReadOnlyMode'], formatter:'.createAnalyticModelLinkEnabledFormatter'}"
                      />

                      <layoutData>
                        <FlexItemData growFactor="1" />
                      </layoutData>
                      <layoutData>
                        <layout:GridData span="XL12 L12 M12 S12" />
                      </layoutData>
                  </HBox>

                  <!-- Hierarchy Parent Column -->
                  <Label
                    text="{i18n_erd>@hierarchyParent}"
                    visible="false"
                  />
                  <Select
                    id="hierarchyParentSelect"
                    items="{path: 'galileiModel>/dimensionElements', templateShareable:true}"
                    forceSelection="false"
                    selectedKey="{
                      path: 'galileiModel>/hierarchies/0/parentElement/0/newName',
                      mode: 'OneWay'
                    }"
                    change="onChangeParentElm"
                    enabled="{galileiModel>/hierarchies/0/parentElement__available/status}"
                    tooltip="{galileiModel>/hierarchies/0/parentElement__available/messages/0}"
                    valueState="{galileiModel>/hierarchies/0/parentElement__valueState}"
                    visible="false"
                  >
                    <core:Item
                      key="{galileiModel>newName}"
                      text="{galileiModel>displayName}"
                    >
                      <core:customData>
                        <core:CustomData
                          key="objectId"
                          value="{galileiModel>objectId}"
                        />
                      </core:customData>
                    </core:Item>
                    <customData>
                      <core:CustomData
                        key="objectId"
                        value="{galileiModel>objectId}"
                      />
                    </customData>
                  </Select>

                  <!-- Hierarchy Child Column -->
                  <Label
                    text="{i18n_erd>@hierarchyChild}"
                    visible="false"

                  />
                  <Select
                    id="hierarchyChildSelect"
                    items="{path: 'galileiModel>/dimensionElements', templateShareable:true}"
                    forceSelection="false"
                    selectedKey="{
                      path: 'galileiModel>/hierarchies/0/childElement/0/newName',
                      mode: 'OneWay'
                    }"
                    change="onChangeChildElm"
                    enabled="{galileiModel>/hierarchies/0/childElement__available/status}"
                    tooltip="{galileiModel>/hierarchies/0/childElement__available/messages/0}"
                    valueState="{galileiModel>/hierarchies/0/childElement__valueState}"
                    visible="false"
                  >
                    <customData>
                      <core:CustomData
                        key="objectId"
                        value="{galileiModel>objectId}"
                      />
                    </customData>
                    <core:Item
                      key="{galileiModel>newName}"
                      text="{galileiModel>displayName}"
                    >
                      <core:customData>
                        <core:CustomData
                          key="objectId"
                          value="{galileiModel>objectId}"
                        />
                      </core:customData>
                    </core:Item>
                  </Select>

                  <!-- Space/Context name -->
                  <Label
                    text="{= ${galileiModel>/isCrossSpace} ? ${i18n_erd>@txtSpaceName} : ${i18n_erd>@txtContextName} }"
                    visible="{= (${galileiModel>/isCrossSpace} || ${galileiModel>/context}) ? true: false}"
                  ></Label>
                  <Input
                    id="spaceOrContextName"
                    editable="false"
                    enabled="false"
                    visible="{= (${galileiModel>/isCrossSpace} || ${galileiModel>/context}) ? true: false}"
                    value="{= ${galileiModel>/isCrossSpace} ? ${galileiModel>/crossSpaceDisplayName} : ${galileiModel>/contextDisplayName} }"
                  />
                  <!-- Local schema -->
                  <Label
                    id="sourceObjLabel"
                    text="{path:'featureflags>/DWCO_MODELING_DFFERENTIATE_UNMANAGED_TABLES', formatter:'.formatters.sourceObjectLabel'}"
                    visible="{galileiModel>/isLocalSchema}"
                  ></Label>
                  <Input
                    editable="false"
                    enabled="false"
                    id="scma"
                    visible="{galileiModel>/isLocalSchema}"
                    value="{parts: ['featureflags>/DWCO_MODELING_DFFERENTIATE_UNMANAGED_TABLES', 'galileiModel>/'], formatter:'.getSourceObjectName'}"
                  />
                  <HBox
                  class="sapUinoMargin"
                  visible="{= ${galileiModel>/isHierarchy}===true}"
                  >
                  <VBox  class="sapUinoMargin" width="33%">
                  <Toolbar  class="sapUinoMargin" visible="{= ${galileiModel>/isHierarchy}===true}">
                    <content>
                    <Label text="{i18n_erd>@parentChildHierarchy}"  class="pcHierarchyLabelStyle"/>
                    <ToolbarSpacer/>
                     <Button
                        icon="sap-icon://add"
                        id="addPCElm"
                        press="onAddPCElm"
                        type="Transparent"
                        tooltip="{i18n_erd>@addParentChild}"
                        enabled="{=  ${featureflags>/DWCO_MODELING_TECHNICAL_VERSIONS} ? !${workbenchEnv>/isVersioningReadOnlyMode} : true}"
                      />
                    </content>
                    </Toolbar>
                  </VBox>
                    <layoutData>
                      <layout:GridData span="XL12 L12 M12 S12"/>
                    </layoutData>
                  </HBox>
                 <HBox
                  class="sapUiTinyMarginTop sapUiSmallMarginBottom"
                  visible="{= ${galileiModel>/isHierarchy}===true}"
                  width="33%">
                  <VBox class="sapUiMediumMarginEnd">
                     <Label text="{i18n_erd>@hierarchyParentList}"/>
                      <List id="parentElementId"  items="{path: 'hierarchyUiModel>/parentElements', templateShareable:true}" showSeparators="None">
                      <CustomListItem >
                        <Select
                          items="{path: 'galileiModel>/dimensionElements', templateShareable:true}"
                          forceSelection="false"
                          selectedKey="{
                            path: 'hierarchyUiModel>parent/newName',
                            mode: 'OneWay'
                          }"
                          change="onChangeParentElm"
                          enabled="{galileiModel>parentElement__available/status}"
                          tooltip="{galileiModel>parentElement__available/messages/0}"
                          valueState="{hierarchyUiModel>parent__valueState}"
                          valueStateText="{hierarchyUiModel>parent__valueStateText}"
                          width="100%">
                      <core:ListItem
                        key="{galileiModel>newName}"
                        text="{galileiModel>displayName}"
                        icon="{
                          path: 'galileiModel>isKey', formatter:'.formatters.keyFormatter'
                        }">
                        <core:customData>
                          <core:CustomData
                            key="objectId"
                            value="{galileiModel>objectId}"
                          />
                        </core:customData>
                      </core:ListItem>
                      <customData>
                        <core:CustomData
                          key="objectId"
                          value="{galileiModel>objectId}"
                        />
                      </customData>
                          </Select>
                      </CustomListItem>
                    </List>
                    <layoutData>
                      <FlexItemData growFactor="1" />
                    </layoutData>
                  </VBox>
                  <VBox>
                    <Label text="{i18n_erd>@hierarchyChildList}"/>
                    <List id="childElementId" items="{path: 'hierarchyUiModel>/childElements', templateShareable:true}" mode="{hierarchyUiModel>/listItemDltMode}" delete="onDeletePCElm"  showSeparators = "None"  backgroundDesign="Transparent">
                      <CustomListItem>
                        <Select
                      items="{path: 'galileiModel>/dimensionElements', templateShareable:true}"
                      forceSelection="false"
                      selectedKey="{
                        path: 'hierarchyUiModel>child/newName',
                        mode: 'OneWay'
                      }"
                      change="onChangeChildElm"
                      enabled="{galileiModel>childElement__available/status}"
                      tooltip="{galileiModel>childElement__available/messages/0}"
                      valueState="{hierarchyUiModel>child__valueState}"
                      valueStateText="{hierarchyUiModel>child__valueStateText}"
                      width="100%"

                    >
                      <customData>
                        <core:CustomData
                          key="objectId"
                          value="{galileiModel>objectId}"
                        />
                      </customData>
                      <core:ListItem
                        key="{galileiModel>newName}"
                        text="{galileiModel>displayName}"
                        icon="{
                          path: 'galileiModel>isKey', formatter:'.formatters.keyFormatter'
                        }"
                      >
                        <core:customData>
                          <core:CustomData
                            key="objectId"
                            value="{galileiModel>objectId}"
                          />
                        </core:customData>
                      </core:ListItem>
                        </Select>
                      </CustomListItem>
                    </List>
                    <layoutData>
                      <FlexItemData growFactor="1" />
                    </layoutData>
                  </VBox>
                  <layoutData>
                  	<layout:GridData span="XL12 L12 M12 S12"/>
                  </layoutData>
                </HBox>

                <!-- Conditional Switch for "Allow Data Transport" -->

                <VBox visible="{parts:['galileiModel>/dataCategory', 'featureflags>/DWCO_BDC_REPOSITORY_TRANSPORT_DATA', 'galileiModel>/isLocal'], formatter:'.allowDataTransportVisible'}">
                  <Label text="{i18n_erd>@txtAllowDataTransport}" />
                  <VBox>
                    <Switch
                      id="allowDataTransportSwitch"
                      state="{galileiModel>/isDataTransportAllowed}"
                      change="onAllowDataTransportChange"
                      tooltip="{i18n_erd>@txtAllowDataTransportTooltip}"
                    />
                </VBox>
                </VBox>


                <!--- Storage Type -->
                <Label
                    text="{i18n_erd>@txtStorageTypeTable}"
                />
                <VBox>
                  <Input
                    editable="false"
                    enabled="false"
                    id="storageTypeFileId"
                    visible="{galileiModel>/fileStorage}"
                    value="{i18n_erd>fileStorageText}"
                  />
                  <Select id="storageTypeSelectId" change="onStorageTypeChange" selectedKey="{path:'galileiModel>/isPinToMemoryEnabled', formatter: '.storageTypeFormatter'}" visible="{= !${galileiModel>/fileStorage}}" enabled="{galileiModel>/isPinToMemoryEnabled__available/status}"
                  >
                    <items>
                      <core:ListItem key="1" text="{i18n_erd>disk}"/>
                      <core:ListItem key="2" text="{i18n_erd>inMemory}"/>
                    </items>
                  </Select>
                </VBox>
                <Label
                  labelFor="deltaCapture"
                  text="{path:'galileiModel>/fileStorage',formatter:'.deltaCaptureLabelFormatter'}"
                  visible="{parts: ['galileiModel>/isLocal', 'galileiModel>/isDeltaTable', 'galileiModel>/isRemote'], formatter:'.deltaCaptureVisibilityFormatter'}"
                />
                <HBox>
                <Switch
                  id="deltaCapture"
                  class="sapUiTinyMarginEnd"
                  state="{parts:['galileiModel>/deltaTable', 'galileiModel>/fileStorage'], formatter:'.deltaCaptureStateFormatter'}"
                  enabled="{parts: ['galileiModel>/deploymentDate', 'galileiModel>/isLocal', 'galileiModel>/fileStorage'], formatter:'.deltaCaptureEnableFormatter'}"
                  change=".onDeltaCaptureChange"
                  visible="{parts: ['galileiModel>/isLocal', 'galileiModel>/isDeltaTable', 'galileiModel>/isRemote'], formatter:'.deltaCaptureVisibilityFormatter'}"
                />
                </HBox>

                <Label
                  labelFor="deltaCaptureTableName"
                  text="{i18n_erd>lblDeltaTableName}"
                  visible="{parts: ['galileiModel>/isLocal', 'galileiModel>/isDeltaTable', 'galileiModel>/isRemote','workbenchEnv>/deltaReadOnly'], formatter:'.deltaTableNameVisibilityFormatter'}"
                />
                <Input
                  id="deltaCaptureTableName"
                  value="{galileiModel>/deltaTableName}"
                  enabled="false"
                  visible="{parts: ['galileiModel>/isLocal', 'galileiModel>/isDeltaTable', 'galileiModel>/isRemote','workbenchEnv>/deltaReadOnly'], formatter:'.deltaTableNameVisibilityFormatter'}"
                />

                  <!-- Object status -->
                  <Label text="{i18n_erd>@objectStatus}" visible="{parts:[{path:'workbenchEnv>/isVersioningReadOnlyMode'}], formatter:'.formatters.objectStatusVisibleFormatter'}"></Label>
                  <HBox id="objectStatus" visible="{parts:[{path:'workbenchEnv>/isVersioningReadOnlyMode'}], formatter:'.formatters.objectStatusVisibleFormatter'}">
                    <dos:DWCObjectStatus id="objectStatusText" class="sapUiTinyMarginEnd" statusType ="{galileiModel>/#objectStatus}" />
                    <Button
                      id="revertObjectStatusButton"
                      visible="{parts: [{path:'galileiModel>/isReadOnlyObject'}, {path:'galileiModel>/#objectStatus'}, {path:'galileiModel>/deploymentDate'}, {path:'galileiModel>/revertLastVersion'}], formatter:'.formatters.revertObjectStatusVisible'}"
                      enabled="{galileiModel>/label__available/status}"
                      icon="{path:'galileiModel>/#objectStatus', formatter:'.formatters.revertObjectStatusIconFormatter'}"
                      text="{path:'galileiModel>/#objectStatus', formatter:'.formatters.revertObjectStatusTextFormatter'}"
                      press="openRevertDeploymentDialog"
                      type="Transparent"
                    ></Button>
                  </HBox>
                  <!-- Deployed on / deployment date -->
                  <Label
                    text="{i18n_erd>@deploymentDate}"
                    visible="{=${galileiModel>/deploymentDate}?true:false}"
                  ></Label>
                  <Text
                    id="deploymentDate"
                    visible="{=${galileiModel>/deploymentDate}?true:false}"
                    text="{path:'galileiModel>/deploymentDate', formatter:'.formatters.dateFormatter'}"
                  />
                </form:content>
              </form:SimpleForm>
            </uxap:blocks>
          </uxap:ObjectPageSubSection>

         </uxap:subSections>
      </uxap:ObjectPageSection>
    <!-- Delta Capture Section-->
      <uxap:ObjectPageSection
        id="deltaCaptureSection"
        title="{i18n_erd>@deltaCapture}"
        titleUppercase="false"
        visible="{= ${galileiModel>/isLocal} &amp;&amp; ${featureflags>/DWCO_TABLE_DELTA_UPSERT_READ_API}}"
      >
        <uxap:subSections>
          <uxap:ObjectPageSubSection>
            <uxap:blocks>
              <form:SimpleForm
                id="deltaCaptureTable"
                editable="true"
                class="deltaCaptureSection"
                layout="ResponsiveGridLayout"
                labelSpanXL="12"
                labelSpanL="12"
                labelSpanM="12"
                labelSpanS="12"
                adjustLabelSpan="true"
                columnsXL="1"
                columnsL="1"
                columnsM="1"
                emptySpanXL="6"
                emptySpanL="4"
                emptySpanM="2"
                singleContainerFullSize="true"
              >
                <form:content>
                  <MessageStrip
                    text="{i18n_erd>@txtDeltaCaptSettingsNoChange}"
                    type="Warning"
                    enableFormattedText="true"
                    showIcon="true"
                    visible="{parts: ['galileiModel>/deploymentDate', 'galileiModel>/fileStorage'], formatter:'.msgStripVisibilityFormatter'}"
                    width="30%"
                    class="sapUiTinyMarginBottom"
                  >
                  </MessageStrip>
                <Label
                  labelFor="deltaCapture_new"
                  text="{path:'galileiModel>/fileStorage',formatter:'.deltaCaptureLabelFormatter'}"
                  visible="{parts: ['galileiModel>/isLocal', 'galileiModel>/isDeltaTable', 'galileiModel>/isRemote'], formatter:'.deltaCaptureNewVisibilityFormatter'}"
                />
                <HBox>
                <Switch
                  id="deltaCapture_new"
                  class="sapUiTinyMarginEnd"
                  state="{parts:['galileiModel>/deltaTable', 'galileiModel>/fileStorage', 'galileiModel>/contentOwner','galileiModel>isNew'], formatter:'.deltaCaptureStateFormatter'}"
                  enabled="{parts: ['galileiModel>/deploymentDate', 'galileiModel>/isLocal', 'galileiModel>/fileStorage'], formatter:'.deltaCaptureEnableFormatter'}"
                  change=".onDeltaCaptureChange"
                  visible="{parts: ['galileiModel>/isLocal', 'galileiModel>/isDeltaTable', 'galileiModel>/isRemote'], formatter:'.deltaCaptureNewVisibilityFormatter'}"
                />
                </HBox>

                <Label
                  labelFor="deltaCaptureTableName_new"
                  text="{i18n_erd>lblDeltaTableName}"
                  visible="{parts: ['galileiModel>/isLocal', 'galileiModel>/isDeltaTable', 'galileiModel>/isRemote','workbenchEnv>/deltaReadOnly','galileiModel>/contentOwner'], formatter:'.deltaTableNameNewVisibilityFormatter'}"
                />
                <Input
                  id="deltaCaptureTableName_new"
                  value="{galileiModel>/deltaTableName}"
                  enabled="false"
                  visible="{parts: ['galileiModel>/isLocal', 'galileiModel>/isDeltaTable', 'galileiModel>/isRemote','workbenchEnv>/deltaReadOnly','galileiModel>/contentOwner'], formatter:'.deltaTableNameNewVisibilityFormatter'}"
                />
                <!-- Outbound table -->
                <Label
                labelFor="deltaOutboundToggle"
                text="{i18n_erd>deltaOutboundLbl}"
                visible="{parts: ['galileiModel>/isLocal', 'galileiModel>/isDeltaTable', 'galileiModel>/isRemote','galileiModel>/fileStorage','workbenchEnv>/deltaReadOnly'], formatter:'.deltaOutboundVisibilityFormatter'}"
              />
              <HBox>
              <Switch
                id="deltaOutboundToggle"
                class="sapUiTinyMarginEnd"
                state="{parts:['galileiModel>/isDeltaOutboundOn'], formatter:'.deltaOutboundStateFormatter'}"
                change=".onDeltaOutboundChange"
                visible="{parts: ['galileiModel>/isLocal', 'galileiModel>/isDeltaTable', 'galileiModel>/isRemote','galileiModel>/fileStorage','workbenchEnv>/deltaReadOnly'], formatter:'.deltaOutboundVisibilityFormatter'}"
              />
              </HBox>

              <Label
                  labelFor="deltaOutboundfield"
                  text="{i18n_erd>lblDeltaOutboundTableName}"
                  visible="{parts: ['galileiModel>/isDeltaTable','galileiModel>/isDeltaOutboundOn'], formatter:'.deltaOutboundTableNameVisibilityFormatter'}"
                />
                <Input
                  id="deltaOutboundfield"
                  value="{galileiModel>/deltaOutboundTableName}"
                  enabled="false"
                  visible="{parts: ['galileiModel>/isDeltaTable','galileiModel>/isDeltaOutboundOn'], formatter:'.deltaOutboundTableNameVisibilityFormatter'}"
                />
                </form:content>
              </form:SimpleForm>
            </uxap:blocks>
          </uxap:ObjectPageSubSection>

         </uxap:subSections>
      </uxap:ObjectPageSection>
      <uxap:ObjectPageSection
        id="remoteSection"
        title="{i18n_erd>@remote}"
        titleUppercase="false"
        visible="{= ${galileiModel>/isRemote} &amp;&amp; ${galileiModel>/isTable}}"
      >
        <uxap:subSections>
          <uxap:ObjectPageSubSection>
            <uxap:blocks>
              <VBox>
                <form:SimpleForm
                  editable="true"
                  class="generalForm"
                  layout="ResponsiveGridLayout"
                  labelSpanXL="12"
                  labelSpanL="12"
                  labelSpanM="12"
                  labelSpanS="12"
                  adjustLabelSpan="true"
                  columnsXL="1"
                  columnsL="1"
                  columnsM="1"
                  emptySpanXL="6"
                  emptySpanL="4"
                  emptySpanM="2"
                  singleContainerFullSize="true"
                >
                  <form:content>
                    <VBox>
                      <HBox visible="{= ${replicationModel>/hasSourceSharing} === true}">
                        <MessageStrip
                          id="sharedReplicaMsgStrip"
                          text="{
                                                                              parts: ['replicationModel>/hasReplica', 'replicationModel>/hasSourceSharing', 'replicationModel>/hasSourceSharingWithReplica'],
                                                                              formatter : '.sharedReplicaMessageText'
                                                                            }"
                          type="{
                                                                              parts: ['replicationModel>/hasReplica', 'replicationModel>/hasSourceSharing', 'replicationModel>/hasSourceSharingWithReplica'],
                                                                              formatter : '.sharedReplicaMessageType'
                                                                            }"
                          showIcon="true"
                          class="sapUiMediumMarginBottom"
                        >
                          <link>
                          <Link
                            id="SharedTableDetailsLink"
                            text="{i18n_table>VIEW_ERROR_DETAILS}"
                            press="handleSharedTableDetailsPress"
                            visible="true"
                          />
                          </link>
                        </MessageStrip>
                      </HBox>
                      <layoutData>
                        <FlexItemData growFactor="1" />
                      </layoutData>
                      <layoutData>
                        <layout:GridData span="XL12 L12 M12 S12" />
                      </layoutData>
                    </VBox>

                    <VBox
                      class="sapUiNoContentPadding"
                      visible="{parts: ['replicationModel>/scheduled', 'replicationModel>/scheduleActivationStatus', 'replicationModel>/usedInTaskChain', 'replicationModel>/dataAccess'], formatter:'.replMessageVisibilityFormatter'}"
                      width="50%"
                    >
                        <MessageStrip
                          id="replicationStrip"
                          text="{i18n_table>txt_replication_change}"
                          type="Warning"
                          showIcon="true"
                          visible="{parts: ['replicationModel>/scheduled', 'replicationModel>/scheduleActivationStatus', 'replicationModel>/usedInTaskChain', 'replicationModel>/dataAccess'], formatter:'.replMessageVisibilityFormatter'}"
                        >
                          <link>
                          <Link
                            id="warnReplicationChangeDetailsEditor"
                            text="{i18n_table>txt_repl_viewdetails}"
                            press="handleReplicationChangeViewDetails"
                            visible="true"
                          />
                          </link>
                        </MessageStrip>
                      <layoutData>
                        <layout:GridData span="XL12 L12 M12 S12" />
                      </layoutData>
                    </VBox>
                    <HBox
                      justifyContent="End"
                      width="100%"
                      visible="{= ((${galileiModel>/isLocalSchema}) || ${galileiModel>/isRemote}) &amp;&amp; ${galileiModel>/parameters}.length === 0}"
                    >
                      <MenuButton
                        type="Transparent"
                        text="{i18n_table>txt_statisticsMenu}"
                        tooltip="{i18n_table>txt_statisticsMenu}"
                        id="tableStatisticsMenu"
                        visible="{parts: ['replicationModel>/federationOnly', 'replicationModel>/dataAccess'], formatter:'.getStatisticsMenuVisibility'}"
                      >
                        <Menu>
                          <MenuItem
                            text="{i18n_table>txt_createStatistics}"
                            press="onCreateStatistics"
                            enabled="{path:'replicationModel>/adapter',formatter:'.createStatisticsMenuEnabledFormatter'}"
                          />
                          <MenuItem
                            text="{i18n_table>txt_delStatistics}"
                            press="onDeleteStatistics"
                            enabled="{path:'replicationModel>/statisticsType',formatter:'.deleteStatisticsMenuEnabledFormatter'}"
                          />

                        </Menu>
                      </MenuButton>
                      <MenuButton
                        icon="sap-icon://sac/data-save"
                        type="Transparent"
                        tooltip="{i18n_table>tableReplicationNew}"
                        id="tableReplicationMenu"
                        visible="{parts: ['replicationModel>/hasTableReplicationPrivilege', 'galileiModel>/#objectStatus', 'replicationModel>/federationOnly','replicationModel>/dataAccess','featureflags>/DWC_DUMMY_SPACE_PERMISSIONS','privilege>/DWC_DATAINTEGRATION/update','replicationModel>/isSpaceUnlocked'], formatter:'.formatters.getReplicationOptionsVisibility'}"
                        cd:actionId="monitoring/editor/replication"
                      >
                        <Menu>
                          <MenuItem
                            text="{i18n_table>loadNewSnapshotNew}"
                            press="loadNewSnapshot"
                            enabled="{
                                            parts: ['replicationModel>/loadNewSnapshot', 'replicationModel>/hasReplica', 'replicationModel>/hasSourceSharing', 'replicationModel>/hasSourceSharingWithReplica', 'replicationModel>/location', 'replicationModel>/dataAccess', 'featureflags>/'],
                                            formatter: '.loadSnapshotMenuEnablementFormatter'
                                          }"
                          />
                          <MenuItem
                            text="{i18n_table>removeReplicatedData}"
                            press="removeReplicatedData"
                            enabled="{
                                            parts: ['replicationModel>/removeReplicatedData', 'replicationModel>/hasReplica', 'replicationModel>/hasSourceSharing', 'replicationModel>/hasSourceSharingWithReplica', 'featureflags>/'],
                                            formatter: '.sharedReplicaMenuEnablementFormatter'
                                          }"
                          />
                          <MenuItem
                            text="{i18n_table>enableRealTimeReplicationNew}"
                            press="enableRealTimeReplication"
                            enabled="{
                                            parts: ['replicationModel>/enableRealTimeAccess', 'replicationModel>/hasReplica', 'replicationModel>/hasSourceSharing', 'replicationModel>/hasSourceSharingWithReplica', 'featureflags>/'],
                                            formatter: '.sharedReplicaMenuEnablementFormatter'
                                          }"
                            visible="{= ${replicationModel>/spaceType} !== 'abapbridge'}"
                          />
                          <MenuItem
                            text="{i18n_table>disableRealTimeReplicationNew}"
                            press="disableRealTimeReplication"
                            visible="{= ${replicationModel>/spaceType} !== 'abapbridge'}"
                            enabled="{
                                            parts: ['replicationModel>/disableRealTimeReplication', 'replicationModel>/hasReplica', 'replicationModel>/hasSourceSharing', 'replicationModel>/hasSourceSharingWithReplica', 'featureflags>/'],
                                            formatter: '.sharedReplicaMenuEnablementFormatter'
                                          }"
                          />

                        </Menu>
                      </MenuButton>
                      <MenuButton
                        id="scheduleActions"
                        type="Transparent"
                        tooltip="{i18n_table>scheduleReplication}"
                        icon="sap-icon://sac/calendar"
                        visible="{parts: ['replicationModel>/hasSchedulingReplicationPrivilege', 'galileiModel>/deploymentStatus','galileiModel>/#objectStatus', 'replicationModel>/federationOnly','replicationModel>/dataAccess','featureflags>/DWC_DUMMY_SPACE_PERMISSIONS','privilege>/','replicationModel>/isSpaceUnlocked'], formatter:'.formatters.getScheduleMenuVisibility'}"
                        cd:actionId="monitoring/editor/schedule"
                      >
                        <Menu>
                          <MenuItem
                            text="{i18n_erd>createScheduleLabel}"
                            press="onCreateScheduleReplication"
                            enabled="{
                                            parts: ['replicationModel>/canCreateSchedule', 'replicationModel>/hasReplica', 'replicationModel>/hasSourceSharing', 'replicationModel>/hasSourceSharingWithReplica', 'featureflags>/'],
                                            formatter: '.sharedReplicaMenuEnablementFormatter'
                                          }"
                          />
                          <MenuItem
                            text="{i18n_erd>changeScheduleLabel}"
                            press="onEditScheduleReplication"
                            enabled="{
                                            parts: ['replicationModel>/canEditSchedule', 'replicationModel>/hasReplica', 'replicationModel>/hasSourceSharing', 'replicationModel>/hasSourceSharingWithReplica', 'featureflags>/'],
                                            formatter: '.sharedReplicaMenuEnablementFormatter'
                                          }"
                          />
                          <MenuItem
                            text="{i18n_erd>deleteScheduleLabel}"
                            press="onDeleteScheduleReplication"
                            enabled="{
                                            parts: ['replicationModel>/canDeleteSchedule', 'replicationModel>/hasReplica', 'replicationModel>/hasSourceSharing', 'replicationModel>/hasSourceSharingWithReplica', 'featureflags>/'],
                                            formatter: '.sharedReplicaMenuEnablementFormatter'
                                          }"
                          />
                        </Menu>
                      </MenuButton>
                      <Button
                        id="refreshReplicationStatus"
                        type="Transparent"
                        icon="sap-icon://refresh"
                        tooltip="{path:'replicationModel>/federationOnly', formatter:'.refreshTooltipFormatter'}"
                        press="refreshReplicationStatus"
                        visible="{parts: ['replicationModel>/hasTableReplicationPrivilege', 'galileiModel>/#objectStatus','','', 'featureflags>/DWC_DUMMY_SPACE_PERMISSIONS','privilege>/DWC_DATAINTEGRATION/update','replicationModel>/isSpaceUnlocked'], formatter:'.formatters.getReplicationOptionsVisibility'}"
                        cd:actionId="monitoring/editor/refresh"
                      ></Button>
                      <MenuButton
                        id="navigationMenu"
                        tooltip="{i18n_table>openInMonitor}"
                        class="sapUiMediumMarginEnd"
                        type="Transparent"
                        width="auto"
                        enabled="{path:'galileiModel>/deploymentStatus', formatter:'.navigateMenuFormatter'}"
                        icon="sap-icon://sac/open-new-window"
                        cd:actionId="monitoring/editor/replication"
                      >
                        <Menu>
                          <MenuItem
                            text="{i18n_table>gotoMonitor}"
                            press="toRemoteTableMonitorPress"
                            visible="{= ${replicationModel>/federationOnly} !== true}"
                          />
                          <MenuItem
                            text="{i18n_table>gotoStatisticsMonitor}"
                            press="toRemoteTableStatisticsMonitorPress"
                            visible="{= ${replicationModel>/adapter} !== 'ABAPAdapter' &amp;&amp; ${replicationModel>/adapter} !== 'CloudDataIntegrationAdapter' &amp;&amp; ${replicationModel>/adapter} !== 'CDI::CDI'}"
                          />
                        </Menu>
                      </MenuButton>
                    </HBox>
                    <!-- Remote source -->
                    <Label text="{i18n_erd>@remoteSource}"></Label>
                    <Input
                      enabled="false"
                      id="rcnx"
                      value="{galileiModel>/remote/connection}"
                    />
                    <!-- Remote entity -->
                    <!-- <Label text="{i18n_erd>@remoteTable}"></Label> -->
                    <Label text="{=${featureflags>/DWCO_DL_SOURCE_OBJECT} === true ? ${i18n>@sourceObject} : ${i18n>@remoteTable}}"></Label>
                    <VBox>
                      <Input
                        enabled="false"
                        id="rtbl"
                        value="{path:'galileiModel>/remote/table', formatter:'.formatters.remoteTableNameFormatter'}"
                        tooltip="{path:'galileiModel>/remote/table', formatter:'.formatters.remoteTableNameFormatter'}"
                      />
                      <HBox
                        direction="RowReverse"
                      >
                       <Button
                          id="changeRemoteTableSourceBtn"
                          type="Transparent"
                          text="{i18n_table>changeSource}"
                          visible="{= ${workbenchEnv>/canCreateOrUpdateModel} === true}"
                          press="onRemoteTableSourceChange"
                          cd:actionId="monitoring/editor/changeRemoteTableSource"
                        />
                      </HBox>
                    </VBox>
                    <!-- Data Access -->
                    <Label
                      text="{i18n_erd>@dataAccessFrom}"
                      visible="{galileiModel>/isRemote}"
                    ></Label>
                    <Input
                      editable="false"
                      enabled="false"
                      id="rstat"
                      value="{parts: ['replicationModel>/dataAccess'], formatter:'.editorDataAccessFormatter'}"
                    />
                    <Label
                      text="{i18n_erd>persistencyStatus}"
                      visible="{= ${replicationModel>/federationOnly} !== true}"
                    />
                    <ObjectStatus
                      id="replicationStatus"
                      text="{parts:['replicationModel>/replicationState', 'replicationModel>/replicationType', 'replicationModel>/taskState'], formatter:'.getTextRepStatusRealTime'}"
                      tooltip="{parts:['replicationModel>/replicationState', 'replicationModel>/replicationType', 'replicationModel>/taskState', 'replicationModel>/replicationError'], formatter:'.getTextRepStatusRealTimeTooltip'}"
                      state="{
                                      parts:['replicationModel>/replicationState', 'replicationModel>/taskState'],
                                      formatter: '.getSemanticColorValue'
                                    }"
                      active="{parts:['replicationModel>/replicationState', 'replicationModel>/replicationType', 'replicationModel>/taskState'], formatter:'.getActiveStatus'}"
                      press="handleErrorPopover"
                      visible="{= ${replicationModel>/federationOnly} !== true}"
                    />
                    <Label
                      text="{i18n_table>lastUpdated}"
                      visible="{= ${replicationModel>/federationOnly} !== true}"
                    />
                    <ObjectStatus
                      id="latestUpdate"
                      text="{ parts: [{ path: 'replicationModel>/latestUpdate'}], formatter:'.formatDateAndTime' }"
                      visible="{= ${replicationModel>/federationOnly} !== true}"
                    />
                    <Label
                      text="{i18n_table>refreshFrequency}"
                      visible="{= ${replicationModel>/federationOnly} !== true}"
                    />
                    <ObjectStatus
                      id="refreshFrequency"
                      text="{parts:[{ path: 'replicationModel>/scheduled'},{ path: 'replicationModel>/scheduleActivationStatus'}],formatter:'.getRefreshFrequencyText' }"
                      state="{parts:[{ path: 'replicationModel>/scheduled'},{ path: 'replicationModel>/scheduleActivationStatus'}],formatter:'.refreshFrequencyStateValue' }"
                      visible="{= ${replicationModel>/federationOnly} !== true}"
                    />
                    <!-- Statistics type-->
                    <Label
                      text="{i18n_table>txt_statistics_type}"
                      visible="{= ${replicationModel>/federationOnly} === true }"
                    />
                    <ObjectStatus
                      id="statisticsType"
                      text="{path:'replicationModel>/statisticsType',formatter:'.getStatisticsType'}"
                      visible="{= ${replicationModel>/federationOnly} === true }"
                    />
                    <!-- Statistics Last updated-->
                    <Label
                      text="{i18n_table>txt_stat_lastUpdated}"
                      visible="{= ${replicationModel>/federationOnly} === true }"
                    />
                    <ObjectStatus
                      id="statisticsLastUpdate"
                      text="{ parts: [{ path: 'replicationModel>/statisticsLatestUpdate'}], formatter:'.formatDateAndTime' }"
                      visible="{= ${replicationModel>/federationOnly} === true }"
                    />
                  </form:content>
                </form:SimpleForm>
              </VBox>
            </uxap:blocks>
          </uxap:ObjectPageSubSection>
        </uxap:subSections>
      </uxap:ObjectPageSection>

      <!-- MEASURES SUBSECTION -->
      <uxap:ObjectPageSection
        id="measuresSection"
        title="{
              parts: [
                {path: 'galileiModel>/dataCategory'},
                {path: 'galileiModel>/orderedElements'}
              ],
              formatter: '.elementsTitleFormatter'
            }"
        titleUppercase="false"
      >
        <uxap:subSections>
          <uxap:ObjectPageSubSection>
            <VBox class="sapUiSmallMarginBegin">
              <VBox width="50%">
                <MessageStrip
                  id="msgStripPartitioning"
                  type="Information"
                  enableFormattedText="true"
                  text="{
                                    parts: [
                                      {path: 'galileiModel>/partitions'},
                                      {path: 'galileiModel>/hasNoKeyWithPartitions'}
                                    ],
                                    formatter: '.msgStripPartitioningFormatter'
                                  }"
                  visible="{parts: ['galileiModel>/partitions', 'galileiModel>/fileStorage'], formatter: '.msgStripPartitioningVisibleFormatter'}"
                  showIcon="true"
                  showCloseButton="true"
                  class="sapUiTinyMarginBottom"
                >
                  <link>
                  <Link
                    id="partitionLearnLink"
                    text="{i18n_table>txt_learn_more}"
                    target="_blank"
                    href="https://help.sap.com/docs/SAP_DATASPHERE/c8a54ee704e94e15926551293243fd1d/03191f36e9144b2aaa47b8c9eea039c1.html"
                  />
                  </link>
                </MessageStrip>
              </VBox>
              <VBox renderType="Bare">
                <!-- Attributes Table-->
                <table:Table
                  id="attributesTable"
                  class="EntityElementsTable"
                  alternateRowColors="true"
                  selectionBehavior="RowSelector"
                  selectionMode="Multi"
                  visibleRowCount="10"
                  rowSelectionChange="onAttributesRowSelectionChange"
                  rows="{
                    path:'galileiModel>/dimensionElements',
                    templateShareable:true
                  }"
                  noData="{
                    parts: [
                      {path: 'galileiModel>/dataCategory'},
                      {path: 'UiState>/attributes/search'},
                      {path: 'i18n_erd>@noDataForFilter'},
                      {path: 'i18n_erd>@noAttributesEditMode'},
                      {path: 'i18n_erd>@noColumnsEditMode'}
                    ],
                    formatter: '.formatters.columnsAndAttributesNoDataText'
                  }"
                >
                  <table:extension>
                    <Toolbar>
                      <content>
                        <Title
                          text="{i18n_erd>@attributes}"
                          visible="{
                            path: 'galileiModel>/dataCategory',
                            formatter: '.formatters.isFactTable'
                          }"
                        ></Title>
                        <ToolbarSpacer />
                        <Button
                          id="addAttributeButton"
                          icon="sap-icon://add"
                          visible="{galileiModel>/createElement__available/status}"
                          tooltip="{i18n_erd>@addAttribute}"
                          press="onAddAttributePress"
                          type="Transparent"
                        ></Button>
                        <Button
                        id="CompoundKeyButton"
                        icon="sap-icon://two-keys"
                        tooltip="{i18n_erd>@editCompoundKey}"
                        press="onPressEditCompoundKey"
                        type="Transparent"
                        visible="true"
                        enabled="{parts:[{path:'workbenchEnv>/canCreateOrUpdateModel'},{path:'galileiModel>/dimensionElements'},{path:'galileiModel>/allHierarchies'},{path:'galileiModel>/dataCategory'}],formatter:'.isEnableCompoundKeyColumn'}"
                        ></Button>
                        <Button
                          id="deleteAttributeButton"
                          icon="sap-icon://delete"
                          visible="{galileiModel>/deleteSelectedElements__available/status}"
                          enabled="{UiState>/attributes/selection}"
                          tooltip="{i18n_erd>@deleteColumns}"
                          press="onDeleteSelectedAttributes"
                          type="Transparent"
                        ></Button>
                        <Button
                          id="addColumnBtn"
                          icon="sap-icon://add"
                          visible="{= ${workbenchEnv>/canCreateOrUpdateModel} === true &amp;&amp; ${galileiModel>/isRemote} === true }"
                          enabled="{parts:[{path:'workbenchEnv>/canCreateOrUpdateModel'},{path:'galileiModel>/isRemote'},{path:'galileiModel>/location'},{path:'replicationModel>/projectionSupported'}],formatter:'.excludeColumnBtnFormatter'}"
                          tooltip="{i18n_erd>@addAttribute}"
                          press="onAddRemoteTableColumn"
                          type="Transparent"
                        ></Button>
                        <Button
                          id="excludeColumnBtn"
                          icon="sap-icon://delete"
                          visible="{= ${workbenchEnv>/canCreateOrUpdateModel} === true &amp;&amp; ${galileiModel>/isRemote} === true }"
                          enabled="{parts:[{path:'workbenchEnv>/canCreateOrUpdateModel'},{path:'galileiModel>/isRemote'},{path:'galileiModel>/location'},{path:'replicationModel>/projectionSupported'},{path:'UiState>/attributes/selection'}],formatter:'.excludeColumnBtnFormatter'}"
                          tooltip="{i18n_erd>@deleteColumns}"
                          press="onExcludeColumns"
                          type="Transparent"
                        ></Button>
                        <SearchField
                          id="filterAttributesButton"
                          placeholder="{i18n_erd>@search}"
                          liveChange="onFilterAttributes"
                          value="{UiState>/attributes/search}"
                          width="15rem"
                        />
                      </content>
                    </Toolbar>
                  </table:extension>
                  <table:columns>
                    <!-- Key -->
                    <table:Column
                      width="3rem"
                      hAlign="Center"
                    >
                      <core:Icon
                        class="nonInteractiveIconColor"
                        src="sap-icon://primary-key"
                        tooltip="{i18n_erd>@key}"
                      />
                      <table:template>
                        <VBox>
                        <CheckBox
                          visible="{path:'galileiModel>/', formatter:'.oldControlVisibilityFormatter'}"
                          selected="{path: 'galileiModel>isKey', formatter:'.formatters.formatKeyValue'}"
                          enabled="{parts:['galileiModel>isCDCColumn', 'galileiModel>isPartitionedColumn', 'galileiModel>/hasData', 'galileiModel>/fileStorage', 'galileiModel>/hasNoKeyWithPartitions', 'galileiModel>isKey__available/status', 'galileiModel>/isRemote', 'galileiModel>isNew'], formatter:'.isKeyEnabledFormatter'}"
                          tooltip="{galileiModel>isKey__available/messages/0}"
                          select="updateKeyColumn"
                        >
                        <customData>
                            <core:CustomData
                              key="objectId"
                              value="{galileiModel>objectId}"
                            />
                          </customData>
                        </CheckBox>
                        <ai:AICheckBox
                          visible="{path:'galileiModel>/', formatter:'.genAIControlVisibilityFormatter'}"
                          hasAIChange="{galileiModel>hasAIChangeOnIsKey}"
                          id="isKeyAI"
                          selected="{path: 'galileiModel>isKey', formatter:'.formatters.formatKeyValue'}"
                          select="updateKeyColumn"
                          enabled="{parts:['galileiModel>isCDCColumn', 'galileiModel>isPartitionedColumn', 'galileiModel>/hasData', 'galileiModel>/fileStorage', 'galileiModel>/hasNoKeyWithPartitions', 'galileiModel>isKey__available/status', 'galileiModel>/isRemote', 'galileiModel>isNew'], formatter:'.isKeyEnabledFormatter'}"
                          tooltip="{galileiModel>isKey__available/messages/0}"
                        >
                          <ai:customData>
                            <core:CustomData
                              key="objectId"
                              value="{galileiModel>objectId}"
                            />
                          </ai:customData>
                        </ai:AICheckBox>
                      </VBox>
                       </table:template>
                    </table:Column>

                    <!-- Label -->
                    <table:Column>
                      <Text text="{i18n_erd>@label}" />
                      <table:template>
                        <Input
                          id="columnBusinessName"
                          value="{
                            path: 'galileiModel>label',
                            mode: 'OneWay'
                          }"
                          enabled="{galileiModel>label__available/status}"
                          tooltip="{galileiModel>label__available/messages/0}"
                          liveChange="onBusinessNameChange"
                          change="onBusinessNameSubmit"
                        >
                        <customData>
                          <core:CustomData
                            key="objectId"
                            value="{galileiModel>objectId}"
                          />
                        </customData>
                        </Input>
                      </table:template>
                    </table:Column>
                    <!-- Technical Name -->
                    <table:Column>
                      <Text text="{i18n_erd>@businessTechnicalName}" />
                      <table:template>
                        <Input
                          id="columnTechnicalName"
                          value="{
                            path: 'galileiModel>newName',
                            mode: 'OneWay'
                          }"
                          enabled="{parts: [
                            {path: 'galileiModel>/deploymentStatus'},
                            {path: 'galileiModel>newName__available/status'},
                            {path: 'galileiModel>isNew'},
                            {path: 'galileiModel>isPartitionedColumn'}
                          ], formatter:'.formatters.deploymentStatusToDisableElement'}"
                          tooltip="{galileiModel>newName__available/messages/0}"
                          liveChange="onTechnicalNameChange"
                          change="onTechnicalNameSubmit"
                        >
                        <customData>
                          <core:CustomData
                            key="objectId"
                            value="{galileiModel>objectId}"
                          />
                        </customData>
                        </Input>
                      </table:template>
                    </table:Column>
                    <!-- Data type -->
                    <table:Column width="12rem">
                      <Text text="{i18n_erd>@dataType}" />
                      <table:template>
                        <Button
                          id="attribute-datatype"
                          press=".openDataTypePopover"
                          text="{galileiModel>displayType}"
                          enabled="{parts:['galileiModel>isCDCColumn', 'galileiModel>isPartitionedColumn', 'galileiModel>/hasData', 'galileiModel>/fileStorage', 'galileiModel>displayType__available/status', 'galileiModel>isNew'], formatter:'.dataTypeButtonEnabledFormatter'}"
                          tooltip="{galileiModel>displayType__available/messages/0}"
                          type="{path: 'galileiModel>validations', formatter:'.formatters.dataTypeButtonType'}"
                        >
                          <customData>
                            <core:CustomData
                              key="objectId"
                              value="{galileiModel>objectId}"
                            />
                          </customData>
                        </Button>
                      </table:template>
                    </table:Column>

                    <!-- Semantic type -->
                    <table:Column
                      visible="{
                        parts: [
                          {path:'galileiModel>/dataCategory'}
                        ],
                        formatter : '.formatters.semanticTypeVisible'
                      }"
                      width="20%"
                    >
                      <Text text="{i18n_erd>@txtSemanticType}" />
                      <table:template>
                        <VBox>
                        <Select
                          id="entityPropertiesListItemSemanticType"
                          items="{
                            path: 'galileiModel>possibleSemanticTypes',
                            templateShareable:true
                          }"
                          visible="{path:'galileiModel>/', formatter:'.oldControlVisibilityFormatter'}"
                          selectedKey="{
                            path:'galileiModel>semanticType',
                            formatter: '.formatters.semanticTypeEmpty'
                          }"
                          change="onChangeSemanticType"
                          forceSelection="false"
                          width="100%"
                          enabled="{= ${galileiModel>isCDCColumn} ? false : ${galileiModel>semanticType__available/status}}"
                          tooltip="{galileiModel>semanticType__available/messages/0}"
                        >
                          <core:Item
                            key="{
                              path:'galileiModel>key',
                              formatter: '.formatters.semanticTypeEmpty'
                            }"
                            text="{galileiModel>text}"
                          />
                        </Select>
                        <ai:AISelectWithRecommendation
                        visible="{path:'galileiModel>/', formatter:'.genAIControlVisibilityFormatter'}"
                        id="semanticTypeAISelect"
                        hasAIChange="{galileiModel>hasAIChangeOnSemanticType}"
                        width="100%"
                        groupColumn="groupName"
                        recommendedColumn="recommended"
                        items="{
                          path: 'galileiModel>possibleSemanticTypesAI',
                          templateShareable:false
                        }"
                        selectedKey="{
                          path:'galileiModel>semanticType',
                          formatter: '.formatters.semanticTypeEmpty'
                        }"
                        change="onChangeSemanticType"
                        enabled="{galileiModel>semanticType__available/status}"
                        tooltip="{galileiModel>semanticType__available/messages/0}"
                      >
                        <core:Item
                          key="{
                            path:'galileiModel>key',
                            formatter: '.formatters.semanticTypeEmpty'
                          }"
                          text="{galileiModel>text}"
                        />
                      </ai:AISelectWithRecommendation>
                      </VBox>
                      </table:template>
                    </table:Column>

                    <!-- Label Column -->
                    <table:Column width="20%" visible="{
                        parts: [
                          {path:'galileiModel>/dataCategory'}
                        ],
                        formatter : '.formatters.labelColumnVisible'
                      }">
                      <Text text="{i18n_erd>@txtLabelAssociation}" />
                      <table:template>
                        <VBox>
                          <Select
                            visible="{path:'galileiModel>/', formatter:'.oldControlVisibilityFormatter'}"
                            items="{
                              path: 'galileiModel>possibleLabelColumns',
                              templateShareable:true
                            }"
                            selectedKey="{
                              parts:[{path:'galileiModel>'},{path:'galileiModel>name'},{path:'galileiModel>container/resource/model/objectNameDisplay'},{path:'galileiModel>labelElement/objectId'},{path:'galileiModel>foreignKey'},{path:'galileiModel>foreignText'}],
                              formatter: '.formatters.labelColumnSelectedKeyFormatter'
                            }"
                            change="onChangeLabelColumn"
                            forceSelection="false"
                            width="100%"
                            enabled="{parts:['galileiModel>isCDCColumn', 'galileiModel>/fileStorage', 'galileiModel>/hasData', 'galileiModel>labelColumn__available/status', 'workbenchEnv>/canCreateOrUpdateModel'], formatter:'.labelColumnEnabledFormatter'}"
                            tooltip="{galileiModel>labelColumn__available/messages/0}"
                            showSecondaryValues= "true"
                          >
                            <core:ListItem
                              key="{
                                parts:[{path:'galileiModel>'},{path:'galileiModel>name'},{path:'galileiModel>container/resource/model/objectNameDisplay'},{path:'galileiModel>objectId'}],
                                formatter: '.formatters.labelColumnKeyFormatter'
                              }"
                              text="{galileiModel>displayName}"
                              icon="{parts:[{path:'galileiModel>'}], formatter:'.formatters.labelColumnIconFormatter'}"
                              additionalText="{parts:[{path:'galileiModel>'}], formatter:'.formatters.labelColumnTFormatter'}"
                            >
                              <core:customData>
                                <core:CustomData
                                  key="label-column-t"
                                  value="true"
                                  writeToDom="true"
                                />
                              </core:customData>
                            </core:ListItem>
                            <customData>
                              <core:CustomData
                                key="objectId"
                                value="{galileiModel>objectId}"
                              />
                            </customData>
                          </Select>
                          <ai:AISelectWithGroup
                              visible="{path:'galileiModel>/', formatter:'.genAIControlVisibilityFormatter'}"
                              id="labelColumnAI"
                              hasAIChange="{galileiModel>hasAIChangeOnLabelElement}"
                              width="100%"
                              itemsData="{galileiModel>possibleLabelColumnsAI}"
                              selectedKey="{
                                parts:[{path:'galileiModel>'},{path:'galileiModel>name'},{path:'galileiModel>container/resource/model/objectNameDisplay'},{path:'galileiModel>labelElement/objectId'},{path:'galileiModel>foreignKey'},{path:'galileiModel>foreignText'}],
                                formatter: '.formatters.labelColumnSelectedKeyFormatter'
                              }"
                              change="onChangeLabelColumn"
                              enabled="{
                                parts:[{path:'galileiModel>labelElement__available/status'},{path:'galileiModel>container'}],
                                formatter: '.formatters.labelColumnEnabledFormatter'
                              }"
                            >
                              <ai:customData>
                                <core:CustomData
                                  key="objectId"
                                  value="{galileiModel>objectId}"
                                />
                              </ai:customData>
                          </ai:AISelectWithGroup>
                      </VBox>
                      </table:template>
                    </table:Column>
                    <!-- Default Value -->
                    <table:Column
                      width="15rem"
                      visible="{parts:['galileiModel>/isRemote', 'galileiModel>/fileStorage'], formatter:'.DefaultValueColumnVisibilityFormatter'}"
                    >
                      <Text text="{i18n_erd>@default}" />
                      <table:template id="defaultTemp">
                        <VBox>
                          <Input
                            id="stringDefaultValue"
                            enabled="{parts:[{path:'galileiModel>primitiveDataType'}, {path:'galileiModel>isKey'}, {path:'galileiModel>displayName__available/status'}, {path:'galileiModel>/isRemote'}, {path:'galileiModel>/isLocalSchema'}, {path:'galileiModel>/isCrossSpace'},{path:'galileiModel>isCDCColumn'}], formatter:'.enableTextBox'}"
                            type="Text"
                            placeholder="{i18n_erd>stringplaceholdertext}"
                            value="{path:'galileiModel>default', type: 'sap.ui.model.type.String'}"
                            visible="{parts:[{path:'galileiModel>primitiveDataType'}], formatter:'.stringInputVisibility'}"
                            valueState="{path: 'galileiModel>default__valueState'}"
                          >
                          <customData>
                            <core:CustomData
                              key="objectId"
                              value="{galileiModel>objectId}"
                            />
                          </customData>
                          </Input>
                          <Input
                            id="intDefaultValue"
                            enabled="{parts:[{path:'galileiModel>primitiveDataType'}, {path:'galileiModel>isKey'}, {path:'galileiModel>displayName__available/status'}, {path:'galileiModel>/isRemote'}, {path:'galileiModel>/isLocalSchema'}, {path:'galileiModel>/isCrossSpace'}], formatter:'.enableTextBox'}"
                            type="Text"
                            placeholder="{i18n_erd>intplaceholdertext}"
                            value="{path:'galileiModel>default'}"
                            visible="{parts:[{path:'galileiModel>primitiveDataType'}], formatter:'.intInputVisibility'}"
                            valueState="{path: 'galileiModel>default__valueState'}"
                          >
                          <customData>
                            <core:CustomData
                              key="objectId"
                              value="{galileiModel>objectId}"
                            />
                          </customData>
                          </Input>
                          <Input
                            id="decDefaultValue"
                            enabled="{parts:[{path:'galileiModel>primitiveDataType'}, {path:'galileiModel>isKey'}, {path:'galileiModel>displayName__available/status'}, {path:'galileiModel>/isRemote'}, {path:'galileiModel>/isLocalSchema'}, {path:'galileiModel>/isCrossSpace'}], formatter:'.enableTextBox'}"
                            type="Text"
                            placeholder="{i18n_erd>decplaceholdertext}"
                            value="{path:'galileiModel>default'}"
                            visible="{parts:[{path:'galileiModel>primitiveDataType'}], formatter:'.decInputVisibility'}"
                            valueState="{path: 'galileiModel>default__valueState'}"
                          >
                          <customData>
                            <core:CustomData
                              key="objectId"
                              value="{galileiModel>objectId}"
                            />
                          </customData>
                          </Input>

                          <HBox
                            id="dateTypeSelect"
                            visible="{= ${galileiModel>primitiveDataType} === 'cds.Date' ? true : false}"
                          >
                            <Input
                              id="dateTypeSelectInput"
                              enabled="false"
                              value="{path:'galileiModel>default'}"
                              visible="{parts:[{path:'galileiModel>default'}], formatter:'.dateinputVisibility'}"
                              width="192px"
                            >
                            </Input>
                            <DatePicker
                              id="dateDefaultVal"
                              change="handleDateChange"
                              valueFormat="yyyy-MM-dd"
                              displayFormat="medium"
                              value="{path:'galileiModel>default'}"
                              visible="{parts:[{path:'galileiModel>default'}], formatter:'.dateSelectVisibility'}"
                              enabled="{= ${galileiModel>displayName__available/status} &amp;&amp; ${galileiModel>isKey} !== true &amp;&amp; ${galileiModel>/isRemote} !== true  &amp;&amp; ${galileiModel>/isLocalSchema} !== true &amp;&amp; ${galileiModel>/isCrossSpace} !== true}"
                              editable="false"
                            />
                            <core:Fragment
                              fragmentName="sap.cdw.components.ermodeler.view.DateMenu"
                              type="XML"
                            />
                          </HBox>
                          <HBox
                            id="timeTypeSelect"
                            visible="{= ${galileiModel>primitiveDataType} === 'cds.Time' ? true : false}"
                          >
                            <Input
                              id="timeTypeSelectInput"
                              enabled="false"
                              value="{path:'galileiModel>default'}"
                              visible="{parts:[{path:'galileiModel>default'}], formatter:'.dateinputVisibility'}"
                              width="192px"
                            >
                            </Input>
                            <TimePicker
                              id="timeDefaultVal"
                              change="handleDateChange"
                              value="{path:'galileiModel>default'}"
                              visible="{parts:[{path:'galileiModel>default'}], formatter:'.dateSelectVisibility'}"
                              enabled="{= ${galileiModel>displayName__available/status} &amp;&amp; ${galileiModel>isKey} !== true &amp;&amp; ${galileiModel>/isRemote} !== true &amp;&amp; ${galileiModel>/isLocalSchema} !== true &amp;&amp; ${galileiModel>/isCrossSpace} !== true}"
                              editable="false"
                            />
                            <core:Fragment
                              fragmentName="sap.cdw.components.ermodeler.view.TimeMenu"
                              type="XML"
                            />
                          </HBox>
                          <HBox
                            id="timestampTypeSelect"
                            visible="{= ${galileiModel>primitiveDataType} === 'cds.Timestamp' || ${galileiModel>primitiveDataType} === 'cds.DateTime' ? true : false}"
                          >
                            <Input
                              id="timestampTypeSelectInput"
                              enabled="false"
                              value="{path:'galileiModel>default'}"
                              visible="{parts:[{path:'galileiModel>default'}], formatter:'.dateinputVisibility'}"
                              width="192px"
                            >
                            </Input>
                            <DateTimePicker
                              id="timestampDefaultVal"
                              change="handleDateChange"
                              valueFormat="yyyy-MM-dd HH:mm:ss"
                              displayFormat="medium"
                              value="{path:'galileiModel>default'}"
                              visible="{parts:[{path:'galileiModel>default'}], formatter:'.dateSelectVisibility'}"
                              enabled="{= ${galileiModel>displayName__available/status} &amp;&amp; ${galileiModel>isKey} !== true &amp;&amp; ${galileiModel>/isRemote} !== true &amp;&amp; ${galileiModel>/isLocalSchema} !== true &amp;&amp; ${galileiModel>/isCrossSpace} !== true }"
                              editable="false"
                            />
                            <core:Fragment
                              fragmentName="sap.cdw.components.ermodeler.view.DateTime"
                              type="XML"
                            />
                          </HBox>

                          <Select
                            id="booleanSelect"
                            items="{path:'DSTypeModel>/booleanTypes', templateShareable:true}"
                            selectedKey="{
                                   path:'galileiModel>default',
                                   mode: 'OneWay'
                                  }"
                            change="onChangeBoolean"
                            width="100%"
                            visible="{= ${galileiModel>primitiveDataType} === 'cds.Boolean' ? true : false}"
                            enabled="{= ${galileiModel>displayName__available/status} &amp;&amp; ${galileiModel>isKey} !== true &amp;&amp; ${galileiModel>/isRemote} !== true &amp;&amp; ${galileiModel>/isLocalSchema} !== true &amp;&amp; ${galileiModel>/isCrossSpace} !== true}"
                          >
                            <core:Item
                              key="{DSTypeModel>type}"
                              text="{DSTypeModel>text}"
                            />
                            <customData>
                              <core:CustomData
                                key="objectId"
                                value="{galileiModel>objectId}"
                              />
                            </customData>
                          </Select>
                        </VBox>
                      </table:template>
                    </table:Column>

                    <!-- not Null -->
                    <table:Column
                      width="4.9rem"
                      hAlign="Center"
                    >
                      <Text text="{i18n_erd>@notNull}" />
                      <table:template>
                        <CheckBox
                          selected="{= ${galileiModel>isNotNull}}"
                          enabled="{parts:['galileiModel>isCDCColumn', 'galileiModel>/hasData', 'galileiModel>/fileStorage', 'galileiModel>displayName__available/status', 'galileiModel>isKey', 'galileiModel>/isRemote', 'galileiModel>/isLocalSchema', 'galileiModel>/isCrossSpace'], formatter:'.isNotNullEnabledFormatter'}"
                          select="updateNullProperty"
                        >
                          <customData>
                            <core:CustomData
                              key="objectId"
                              value="{galileiModel>objectId}"
                            />
                          </customData>
                        </CheckBox>
                      </table:template>
                    </table:Column>

                    <!-- Visibility -->
                    <table:Column
                      width="3rem"
                      hAlign="Center"
                      visible="{path: 'galileiModel>/dataCategory', formatter: '.hideColumnVisibleForAD'}"
                    >
                      <core:Icon
                        class="nonInteractiveIconColor"
                        src="sap-icon://show"
                        tooltip="{parts:['i18n_erd>@isVisible', 'i18n_erd>@showInStory'], formatter: '.hideColumnVisibleText'}"
                      />
                      <table:template>
                        <CheckBox
                          selected="{galileiModel>isVisible}"
                          enabled="{galileiModel>isVisible__available/status}"
                          tooltip="{galileiModel>isVisible__available/messages/0}"
                        >
                          <customData>
                            <core:CustomData
                              key="objectId"
                              value="{galileiModel>objectId}"
                            />
                          </customData>
                        </CheckBox>
                      </table:template>
                    </table:Column>
                    <!-- Review AI Change -->
                    <table:Column
                    width="4.9rem"
                    hAlign="Center"
                    visible="{= ${galileiModel>/hasAIChangeOnAttributes} === true}"
                  >
                    <Text text="{i18n_erd>@review}" />
                    <table:template>
                      <ai:AIReviewPopoverButton
                        title="{i18n_erd>@reviewTitle}"
                        visible="{= !!${galileiModel>reason}}"
                        enabled="{galileiModel>hasAIChange}"
                        closeText="{i18n_erd>@revertChanges}"
                        tooltip1="{i18n_erd>@review}"
                        revertCallback=".onRevertElementAIChange"
                        info="{path: 'galileiModel>reviewInfo'}"
                      >
                      </ai:AIReviewPopoverButton>
                    </table:template>
                  </table:Column>
                  </table:columns>
                  <table:dragDropConfig>
                    <dnd:DragInfo
                      groupName="elementDND"
                      sourceAggregation="rows"
                      enabled="{parts:[{path:'galileiModel>/'}],formatter:'.enableDragAndDrop'}"
                      dragStart="onDragStart"
                      dragEnd="onAttributeDragEnd"
                    />
                    <dnd:DropInfo
                      groupName="elementDND"
                      drop="onAttributesTableDrop"
                      targetAggregation="rows"
                      dropPosition="Between"
                    />
                  </table:dragDropConfig>
                </table:Table>

                <!-- Move Section-->
                <HBox
                  justifyContent="Center"
                  class="sapUiMediumMarginTop"
                  visible="{
                    path: 'galileiModel>/dataCategory',
                    formatter: '.formatters.isFactTable'
                  }"
                >
                  <Button
                    id="changeAttributeToMeasureButton"
                    class="sapUiSmallMarginEnd"
                    icon="sap-icon://navigation-down-arrow"
                    press="onChangeAttributeToMeasurePress"
                    enabled="{
                      path: 'UiState>/attributes/selection',
                      formatter: '.changeToMeasureButtonEnabledFormatter'
                    }"
                    tooltip="{
                      parts:[
                        {path: 'UiState>/attributes/selection'},
                        {path: 'i18n_table>missingAttributesSelection'},
                        {path: 'i18n_erd>@txtMeasureDropNotPossible'},
                        {path: 'i18n_table>changeAttributesToMeasures'}
                      ],
                      formatter: '.changeToMeasureButtonTooltipFormatter'
                    }"
                  ></Button>
                  <Button
                    id="changeMeasureToAttributeButton"
                    icon="sap-icon://navigation-up-arrow"
                    press="onChangeMeasureToAttributePress"
                    enabled="{UiState>/measures/selection}"
                    tooltip="{
                      parts: [
                        {path: 'UiState>/measures/selection'},
                        {path: 'i18n_table>changeMeasuresToAttributes'},
                        {path: 'i18n_table>missingMeasuresSelection'}
                      ],
                      formatter: '.formatters.toggleLabel'
                    }"
                  ></Button>
                </HBox>


                <!-- Measures Table -->
                <table:Table
                  id="measuresTable"
                  class="EntityElementsTable"
                  alternateRowColors="true"
                  selectionBehavior="Row"
                  selectionMode="Multi"
                  rowSelectionChange="onMeasuresRowSelectionChange"
                  visibleRowCount="10"
                  rows="{
                    path:'galileiModel>/measures',
                    templateShareable:true
                  }"
                  visible="{
                    path: 'galileiModel>/dataCategory',
                    formatter: '.formatters.isFactTable'
                  }"
                  noData="{
                    parts: [
                      {path: 'UiState>/measures/search'},
                      {path: 'i18n_erd>@noDataForFilter'},
                      {path: 'i18n_erd>@noMeasuresEditMode'}
                    ],
                    formatter: '.formatters.toggleLabel'
                  }"
                >
                  <table:extension>
                    <Toolbar>
                      <content>
                        <Title text="{i18n_erd>@txtMeasures}" />
                        <ToolbarSpacer />
                        <Button
                          id="addMeasureButton"
                          icon="sap-icon://add"
                          visible="{galileiModel>/createElement__available/status}"
                          tooltip="{i18n_erd>@addAttribute}"
                          press="onAddMeasurePress"
                          type="Transparent"
                        ></Button>
                        <Button
                          id="deleteMeasureButton"
                          icon="sap-icon://delete"
                          visible="{galileiModel>/deleteSelectedElements__available/status}"
                          enabled="{UiState>/measures/selection}"
                          tooltip="{i18n_erd>@deleteColumns}"
                          press="onDeleteSelectedMeasures"
                          type="Transparent"
                        ></Button>
                        <SearchField
                          id="filterMeasures"
                          placeholder="{i18n_erd>@search}"
                          liveChange="onFilterMeasures"
                          width="15rem"
                          value="{UiState>/measures/search}"
                        />
                      </content>
                    </Toolbar>
                  </table:extension>
                  <table:columns>
                    <!-- Label -->
                    <table:Column>
                      <Text text="{i18n_erd>@label}" />
                      <table:template>
                        <Input
                          id="columnmeasureBusinessName"
                          value="{
                            path: 'galileiModel>label',
                            mode: 'OneWay'
                          }"
                          enabled="{galileiModel>label__available/status}"
                          tooltip="{galileiModel>label__available/messages/0}"
                          liveChange="onBusinessNameChange"
                          change="onBusinessNameSubmit"
                        >
                        <customData>
                          <core:CustomData
                            key="objectId"
                            value="{galileiModel>objectId}"
                          />
                        </customData>
                        </Input>
                      </table:template>
                    </table:Column>
                    <!-- Technical Name -->
                    <table:Column>
                      <Text text="{i18n_erd>@businessTechnicalName}" />
                      <table:template>
                        <Input
                          id="columnmeasureTechnicalName"
                          value="{
                            path: 'galileiModel>newName',
                            mode: 'OneWay'
                          }"
                          enabled="{parts: [
                            {path: 'galileiModel>/deploymentStatus'},
                            {path: 'galileiModel>newName__available/status'},
                            {path: 'galileiModel>isNew'},
                            {path: 'galileiModel>isPartitionedColumn'}
                          ], formatter:'.formatters.deploymentStatusToDisableElement'}"
                          tooltip="{galileiModel>newName__available/messages/0}"
                          liveChange="onTechnicalNameChange"
                          change="onTechnicalNameSubmit"
                        >
                        <customData>
                          <core:CustomData
                            key="objectId"
                            value="{galileiModel>objectId}"
                          />
                        </customData>
                        </Input>
                      </table:template>
                    </table:Column>
                    <!-- Aggregation -->
                    <table:Column width="6em">
                      <Text text="{i18n_erd>@txtAggregation}" />
                      <table:template>
                        <Select
                          items="{path:'galileiModel>aggregationTypes', templateShareable:true}"
                          selectedKey="{galileiModel>defaultAggregation}"
                          change="onChangeDefaultAggregation"
                          enabled="{galileiModel>defaultAggregation__available/status}"
                          tooltip="{galileiModel>defaultAggregation__available/messages/0}"
                        >
                          <core:Item
                            key="{galileiModel>key}"
                            text="{galileiModel>text}"
                          />
                          <customData>
                            <core:CustomData
                              key="objectId"
                              value="{galileiModel>objectId}"
                            />
                          </customData>
                        </Select>
                      </table:template>
                    </table:Column>
                    <!-- Data Type-->
                    <table:Column width="9rem">
                      <Text text="{i18n_erd>@dataType}" />
                      <table:template>
                        <Button
                          id="measure-datatype"
                          press=".openDataTypePopover"
                          text="{galileiModel>displayType}"
                          enabled="{parts:['galileiModel>isCDCColumn', 'galileiModel>isPartitionedColumn', 'galileiModel>/hasData', 'galileiModel>/fileStorage', 'galileiModel>displayType__available/status', 'galileiModel>isNew'], formatter:'.dataTypeButtonEnabledFormatter'}"
                          tooltip="{galileiModel>displayType__available/messages/0}"
                          type="{path: 'galileiModel>validations', formatter:'.formatters.dataTypeButtonType'}"
                        >
                          <customData>
                            <core:CustomData
                              key="objectId"
                              value="{galileiModel>objectId}"
                            />
                          </customData>
                        </Button>
                      </table:template>
                    </table:Column>
                    <!-- Semantic type -->
                    <table:Column visible="{
                        parts: [
                          {path:'galileiModel>/dataCategory'}
                        ],
                        formatter : '.formatters.semanticTypeVisible'
                      }">
                      <Text text="{i18n_erd>@txtSemanticType}" />
                      <table:template>
                        <VBox>
                        <Select
                          items="{
                            path: 'galileiModel>possibleSemanticTypes',
                            templateShareable:true
                          }"
                          visible="{path:'galileiModel>/', formatter:'.oldControlVisibilityFormatter'}"
                          selectedKey="{
                            path:'galileiModel>semanticType',
                            formatter: '.formatters.semanticTypeEmpty'
                          }"
                          change="onChangeSemanticType"
                          forceSelection="false"
                          width="100%"
                          enabled="{galileiModel>semanticType__available/status}"
                          tooltip="{galileiModel>semanticType__available/messages/0}"
                        >
                          <core:Item
                            key="{
                              path:'galileiModel>key',
                              formatter: '.formatters.semanticTypeEmpty'
                            }"
                            text="{galileiModel>text}"
                          />
                        </Select>
                        <ai:AISelectWithRecommendation
                        visible="{path:'galileiModel>/', formatter:'.genAIControlVisibilityFormatter'}"
                        id="semanticTypeAISelectMeasures"
                        hasAIChange="{galileiModel>hasAIChangeOnSemanticType}"
                        width="100%"
                        groupColumn="groupName"
                        recommendedColumn="recommended"
                        items="{
                          path: 'galileiModel>possibleSemanticTypesAI',
                          templateShareable:false
                        }"
                        selectedKey="{
                          path:'galileiModel>semanticType',
                          formatter: '.formatters.semanticTypeEmpty'
                        }"
                        change="onChangeSemanticType"
                        enabled="{galileiModel>semanticType__available/status}"
                        tooltip="{galileiModel>semanticType__available/messages/0}"
                      >
                        <core:Item
                          key="{
                            path:'galileiModel>key',
                            formatter: '.formatters.semanticTypeEmpty'
                          }"
                          text="{galileiModel>text}"
                        />
                      </ai:AISelectWithRecommendation>
                    </VBox>
                      </table:template>
                    </table:Column>
                    <!-- Unit Column-->
                    <table:Column visible="{
                        parts: [
                          {path:'galileiModel>/dataCategory'}
                        ],
                        formatter : '.formatters.semanticTypeVisible'
                      }">
                      <Text text="{i18n_erd>@txtMeasureUnitColumn}" />
                      <table:template>
                        <VBox>
                        <Select
                          items="{
                            path: 'galileiModel>possibleSemanticTypeElements',
                            templateShareable:true
                          }"
                          visible="{path:'galileiModel>/', formatter:'.oldControlVisibilityFormatter'}"
                          selectedKey="{path:'galileiModel>unitTypeElement/objectId', mode: 'OneWay'}"
                          forceSelection="false"
                          width="100%"
                          enabled="{
                            parts: [
                              {path: 'galileiModel>semanticType'},
                              {path: 'galileiModel>semanticType__available/status'}
                            ],
                            formatter: '.formatters.unitColumnEnabled'
                          }"
                          tooltip="{galileiModel>semanticType__available/messages/0}"
                          valueState="{
                            parts: [
                              {path: 'galileiModel>semanticType'},
                              {path: 'galileiModel>semanticType__available/status'},
                              {path: 'galileiModel>unitTypeElement'}
                            ],
                            formatter: '.formatters.unitColumnValueState'
                          }"
                          valueStateText="{
                            parts: [
                              {path: 'galileiModel>semanticType'},
                              {path: 'galileiModel>semanticType__available/status'},
                              {path: 'galileiModel>unitTypeElement'}
                            ],
                            formatter: '.formatters.unitColumnValueStateText'
                          }"
                          change="onChangeMeasureUnitColumn"
                        >
                          <core:Item
                            key="{galileiModel>objectId}"
                            text="{galileiModel>displayName}"
                          />
                          <customData>
                            <core:CustomData
                              key="objectId"
                              value="{galileiModel>objectId}"
                            />
                          </customData>
                        </Select>
                        <ai:AISelectWithRecommendation
                        visible="{path:'galileiModel>/', formatter:'.genAIControlVisibilityFormatter'}"
                        hasAIChange="{galileiModel>hasAIChangeOnUnitTypeElement}"
                        width="100%"
                        forceSelection="false"
                        groupColumn="groupName"
                        recommendedColumn="recommended"
                        items="{
                          path: 'galileiModel>possibleSemanticTypeElementsAI',
                          templateShareable:false
                        }"
                        selectedKey="{path:'galileiModel>unitTypeElement/objectId', mode: 'OneWay'}"
                        change="onChangeMeasureUnitColumn"
                        enabled="{
                          parts: [
                            {path: 'galileiModel>semanticType'},
                            {path: 'galileiModel>semanticType__available/status'}
                          ],
                          formatter: '.formatters.unitColumnEnabled'
                        }"
                        tooltip="{galileiModel>semanticType__available/messages/0}"
                        valueState="{
                          parts: [
                            {path: 'galileiModel>semanticType'},
                            {path: 'galileiModel>semanticType__available/status'},
                            {path: 'galileiModel>unitTypeElement'}
                          ],
                          formatter: '.formatters.unitColumnValueState'
                        }"
                        valueStateText="{
                          parts: [
                            {path: 'galileiModel>semanticType'},
                            {path: 'galileiModel>semanticType__available/status'},
                            {path: 'galileiModel>unitTypeElement'}
                          ],
                          formatter: '.formatters.unitColumnValueStateText'
                        }"
                      >
                      <core:Item
                      key="{galileiModel>objectId}"
                      text="{galileiModel>displayName}"
                    />
                    <ai:customData>
                      <core:CustomData
                        key="objectId"
                        value="{galileiModel>objectId}"
                      />
                    </ai:customData>
                      </ai:AISelectWithRecommendation>
                      </VBox>
                      </table:template>
                    </table:Column>

                    <!-- Default Value -->
                    <table:Column width="15rem" visible="{parts:['galileiModel>/fileStorage'], formatter:'.DefaultValueMeasureVisibilityFormatter'}">
                      <Text text="{i18n_erd>@default}" />
                      <table:template id="defaultTempMeasure">
                        <VBox>
                          <Input
                            id="intDefaultValueMeasure"
                            enabled="{parts:[{path:'galileiModel>primitiveDataType'}, {path:'galileiModel>isKey'}, {path:'galileiModel>displayName__available/status'}, {path:'galileiModel>/isRemote'}, {path:'galileiModel>/isLocalSchema'}, {path:'galileiModel>/isCrossSpace'}], formatter:'.enableTextBox'}"
                            type="Text"
                            placeholder="{i18n_erd>intplaceholdertext}"
                            value="{path:'galileiModel>default'}"
                            visible="{parts:[{path:'galileiModel>primitiveDataType'}], formatter:'.intInputVisibility'}"
                            valueState="{path: 'galileiModel>default__valueState'}"
                          >
                          <customData>
                            <core:CustomData
                              key="objectId"
                              value="{galileiModel>objectId}"
                            />
                          </customData>
                          </Input>
                          <Input
                            id="decDefaultValueMeasure"
                            enabled="{parts:[{path:'galileiModel>primitiveDataType'}, {path:'galileiModel>isKey'}, {path:'galileiModel>displayName__available/status'}, {path:'galileiModel>/isRemote'}, {path:'galileiModel>/isLocalSchema'}, {path:'galileiModel>/isCrossSpace'}], formatter:'.enableTextBox'}"
                            type="Text"
                            placeholder="{i18n_erd>decplaceholdertext}"
                            value="{path:'galileiModel>default'}"
                            visible="{parts:[{path:'galileiModel>primitiveDataType'}], formatter:'.decInputVisibility'}"
                            valueState="{path: 'galileiModel>default__valueState'}"
                          >
                          <customData>
                            <core:CustomData
                              key="objectId"
                              value="{galileiModel>objectId}"
                            />
                          </customData>
                          </Input>
                        </VBox>
                      </table:template>
                    </table:Column>

                    <!-- not Null -->
                    <table:Column
                      width="4.9rem"
                      hAlign="Center"
                    >
                      <Text text="{i18n_erd>@notNull}" />
                      <table:template>
                        <CheckBox
                          selected="{= ${galileiModel>isNotNull} || ${galileiModel>isKey}}"
                          enabled="{= ${galileiModel>displayName__available/status} &amp;&amp; ${galileiModel>isKey} !== true &amp;&amp; ${galileiModel>/isRemote} !== true &amp;&amp; ${galileiModel>/isCrossSpace} !== true}"
                          select="updateNullProperty"
                        >
                          <customData>
                            <core:CustomData
                              key="objectId"
                              value="{galileiModel>objectId}"
                            />
                          </customData>
                        </CheckBox>
                      </table:template>
                    </table:Column>

                    <!-- Visibility -->
                    <table:Column
                      width="3rem"
                      hAlign="Center"
                      visible="{path: 'galileiModel>/dataCategory', formatter: '.hideColumnVisibleForAD'}"
                    >
                      <core:Icon
                        class="nonInteractiveIconColor"
                        src="sap-icon://show"
                        tooltip="{parts:['i18n_erd>@isVisible', 'i18n_erd>@showInStory'], formatter: '.hideColumnVisibleText'}"
                      />
                      <table:template>
                        <CheckBox
                          selected="{galileiModel>isVisible}"
                          enabled="{galileiModel>isVisible__available/status}"
                          tooltip="{galileiModel>isVisible__available/messages/0}"
                        >
                          <customData>
                            <core:CustomData
                              key="objectId"
                              value="{galileiModel>objectId}"
                            />
                          </customData>
                        </CheckBox>
                      </table:template>
                    </table:Column>
                       <!-- Review AI Change -->
                       <table:Column
                       width="4.9rem"
                       hAlign="Center"
                       visible="{= ${galileiModel>/hasAIChangeOnMeasures} === true}"
                     >
                       <Text text="{i18n_erd>@review}" />
                       <table:template>
                         <ai:AIReviewPopoverButton
                           title="{i18n_erd>@reviewTitle}"
                           visible="{= !!${galileiModel>reason}}"
                           enabled="{path: 'galileiModel>hasAIChange'}"
                           closeText="{i18n_erd>@revertChanges}"
                           tooltip1="{i18n_erd>@review}"
                           revertCallback=".onRevertElementAIChange"
                           info="{path: 'galileiModel>reviewInfo'}"
                         >
                         </ai:AIReviewPopoverButton>
                       </table:template>
                     </table:Column>
                  </table:columns>
                  <table:dragDropConfig>
                    <dnd:DragInfo
                      groupName="elementDND"
                      sourceAggregation="rows"
                      enabled="{parts:[{path:'galileiModel>/'}],formatter:'.enableDragAndDrop'}"
                      dragStart="onDragStart"
                    />
                    <dnd:DropInfo
                      groupName="elementDND"
                      drop="onMeasuresTableDrop"
                      dragEnter="onMeasureTableDragEnter"
                      targetAggregation="rows"
                      dropPosition="Between"
                    />
                  </table:dragDropConfig>
                </table:Table>

              </VBox>
              <MessageStrip
                text="{i18n_erd>@txtMeasureDropNotPossible}"
                type="Warning"
                showIcon="true"
                visible="{UiState>/measures/attributeToMeasureDisallowed}"
                customIcon="sap-icon://cancel"
                class="sapUiSmallMarginTop"
              >
              </MessageStrip>
            </VBox>
          </uxap:ObjectPageSubSection>

        </uxap:subSections>
      </uxap:ObjectPageSection>

    <!-- Filters -->
      <uxap:ObjectPageSection
        id="filterSection"
        title="{parts:[{path:'galileiModel>/remoteFilter'}],formatter:'.filterTitleFormatter'}"
        titleUppercase="false"
        visible="{= ${galileiModel>/isRemote} &amp;&amp; ${galileiModel>/isTable}}"
      >
        <uxap:subSections>
          <uxap:ObjectPageSubSection>
            <!-- New Filters -->
            <core:Fragment
              class="filterList"
              fragmentName="sap.cdw.components.tableeditor.view.Filters"
              type="XML"
            />
          </uxap:ObjectPageSubSection>
        </uxap:subSections>
      </uxap:ObjectPageSection>

    <!-- Input Parameters Section -->
      <uxap:ObjectPageSection
        id="InputParametersSection"
        title="{path:'galileiModel>/parameters',formatter:'.parametersTitleFormatter'}"
        titleUppercase="false"
        visible="{= ${galileiModel>/isTable} &amp;&amp; (( ${galileiModel>/isLocalSchema}) || ${galileiModel>/isRemote}) &amp;&amp; ${galileiModel>/parameters}.length > 0}"
      >
        <uxap:subSections>
          <uxap:ObjectPageSubSection>
            <!-- Parameters Table -->
            <core:Fragment
              fragmentName="sap.cdw.components.tableeditor.view.InputParametersSection"
              type="XML"
            />
          </uxap:ObjectPageSubSection>
        </uxap:subSections>
      </uxap:ObjectPageSection>

    <!-- ASSOCIATIONS SECTION-->
      <uxap:ObjectPageSection
        id="associationSection"
        title="{parts:[{path:'i18n_erd>@associationsList'},{path:'galileiModel>/allAssociations'}],formatter:'.collectionHeaderTextFormatter'}"
        titleUppercase="false"
      >
        <uxap:subSections>
          <uxap:ObjectPageSubSection>
            <NavContainer
              id="navCon"
              width="100%"
              height="100%"
              class="navContainerControl sapUiSmallMarginBottom"
            >
              <Page
                showHeader="false"
                showFooter="false"
                class="associationListPage"
                id="ListPage"
                enableScrolling="false"
              >
                <VBox
                  class="sapUiSmallMarginBegin"
                  height="100%"
                >
                  <VBox
                    renderType="Bare"
                    height="100%"
                  >
                    <!-- Associations Table-->
                    <table:Table
                      id="associationsList"
                      alternateRowColors="true"
                      selectionBehavior="RowSelector"
                      selectionMode="Multi"
                      visibleRowCount="10"
                      rowSelectionChange="onAssociationRowSelection"
                      rows="{path:'galileiModel>/allAssociations',templateShareable:true}"
                      noData="{i18n_erd>@associationsNoDataText}"
                    >
                      <table:extension>
                        <Toolbar>
                          <content>
                            <ToolbarSpacer />
                            <MenuButton
                              id="addAssociationMenu"
                              icon="sap-icon://add"
                              tooltip="{i18n>@createAssociation}"
                              type="Transparent"
                              enabled="{parts:[{path:'galileiModel>/'},{path:'workbenchEnv>/canCreateOrUpdateModel'}],formatter:'.addAssociationFormatter'}"
                            >
                              <menu>
                                <Menu itemSelected=".onCreateAssociation">
                                  <MenuItem
                                    id="addAssociation"
                                    text="{i18n>@txtAddAssociation}"
                                    key="AddAssociation"
                                  />
                                  <MenuItem
                                    id="addHierarchyAssociation"
                                    text="{i18n>@txtAddHierarchyAssociation}"
                                    key="AddHierarchyAssociation"
                                    enabled="{parts:[{path:'galileiModel>/allAssociations'},
                                    {path:'galileiModel>/isDimension'},
                                    {value:'HierarchyAssociation'},{path:'galileiModel>/hierarchies'}],formatter:'.addHierarchyAssociationEnableFormatter'}"
                                  />
                                  <MenuItem
                                    id="addHierarchyWithDirectoryAssociation"
                                    text="{i18n>@hierarchyWithDirectoryAssociation}"
                                    key="AddHierarchyWithDirectoryAssociation"
                                    enabled="{parts:[{path:'galileiModel>/allAssociations'},
                                    {path:'galileiModel>/isDimension'},
                                    {value:'HierarchyWithDirectoryAssociation'},{path:'galileiModel>/hierarchies'}],formatter:'.addHierarchyAssociationEnableFormatter'}"
                                  />
                                  <MenuItem
                                    id="addTextAssociation"
                                    text="{i18n>@txtAddTextAssociation}"
                                    key="AddTextAssociation"
                                  />
                                </Menu>
                              </menu>
                            </MenuButton>
                            <Button
                              id="deleteAssociationButton"
                              tooltip="{i18n_erd>@deleteAssociation}"
                              icon="sap-icon://delete"
                              press="onPressDeleteAssociation"
                              type="Transparent"
                              enabled="{parts:[{path:'galileiModel>/'},{path:'galileiModel>/associationSelectedCount'},{path:'workbenchEnv>/canCreateOrUpdateModel'}],formatter:'.deleteAssociationFormatter'}"
                            />
                            <SearchField
                              id="searchAssociationsButton"
                              placeholder="{i18n_erd>@search}"
                              liveChange="onFilterAssociations"
                              value="{UiState>/associations/search}"
                              width="15rem"
                            />
                          </content>
                        </Toolbar>
                      </table:extension>
                      <table:columns>
                        <table:Column
                          width="20%"
                          id="bnameColumn"
                          filterProperty="label"
                        >
                          <Text text="{i18n_erd>@label}" />
                          <table:template>
                            <HBox
                              width="100%"
                              alignItems="Start"
                              justifyContent="Start"
                            >
                              <items>
                                <core:Icon
                                  src="sap-icon://sac/association"
                                  class="sapUiTinyMarginBeginEnd"
                                />
                                <Text text="{galileiModel>label}" />
                              </items>
                            </HBox>
                          </table:template>
                        </table:Column>
                        <table:Column
                          width="20%"
                          id="technicalColumn"
                          autoResizable="true"
                          filterProperty="technicalName"
                        >
                          <Text text="{i18n_erd>@name}" />
                          <table:template>
                            <Text text="{galileiModel>name}" />
                          </table:template>
                        </table:Column>
                        <table:Column
                          width="20%"
                          id="associationType"
                          autoResizable="true"
                        >
                          <Text text="{i18n_erd>@associationType}" />
                          <table:template>
                            <Text text="{
                                parts: [
                                    {path:'galileiModel>isHierarchy'},
                                    {path:'galileiModel>isText'},
                                    {path:'galileiModel>source/isDimension'},
                                    {path:'galileiModel>isHierarchyWithDirectory'}
                                ],
                                formatter:'.associationTypeFormatter'
                              }" />
                          </table:template>
                        </table:Column>
                        <table:Column
                          width="20%"
                          id="targetEntity"
                          autoResizable="true"
                        >
                          <Text text="{i18n_erd>@targetEntity}" />
                          <table:template>
                            <HBox>
                              <dol:DWCObjectLink
                                showSimpleTooltip="false"
                                text="{path:'galileiModel>target',formatter:'.targetEntityLinkTextFormatter'}"
                                target="_blank"
                                technicalName="{path:'galileiModel>target',formatter:'.targetEntityTechnicalNameFormatter'}"
                                businessName="{path:'galileiModel>target',formatter:'.targetEntityBusinessNameFormatter'}"
                                spaceName="{path:'galileiModel>target',formatter:'.targetEntitySpaceNameFormatter'}"
                              />
                            </HBox>
                          </table:template>
                        </table:Column>
                        <table:Column
                          width="40%"
                          id="mappingColumn"
                          autoResizable="true"
                        >
                          <Text text="{i18n_erd>@mapping}" />
                          <table:template>
                            <HBox justifyContent="SpaceBetween">
                              <HBox><Text
                                  text="{path:'galileiModel>mappings',formatter:'.associationMappingFormatter'}" />
                                <Link
                                  visible="{path:'galileiModel>mappings',formatter:'.moreTextVisibleFormatter'}"
                                  text="{path:'galileiModel>mappings',formatter:'.moreTextFormatter'}"
                                  press="onTableAssociationEdit"
                                />
                              </HBox>
                              <HBox>
                                <Button
                                  id="editAssociationsBtn"
                                  icon="sap-icon://slim-arrow-right"
                                  tooltip="{i18n_erd>@editAssociations}"
                                  type="Transparent"
                                  press="onTableAssociationEdit"
                                ></Button>
                              </HBox>
                            </HBox>
                          </table:template>
                        </table:Column>
                      </table:columns>
                    </table:Table>
                  </VBox>
                </VBox>
              </Page>
              <Page
                showHeader="true"
                showFooter="false"
                class="associationDetailsPage"
                id="associationDetailsPage"
                showNavButton="true"
                title="{header>/displayName}"
                titleAlignment="Start"
                navButtonPress="onBackToFields"
              >
                <VBox
                  id="vBoxExContainer"
                  renderType="Bare"
                >
                  <core:Fragment
                    class="associationDetailsContainer"
                    fragmentName="sap.cdw.components.ermodeler.properties.AssociationProperties"
                    type="XML"
                  />
                </VBox>
              </Page>
            </NavContainer>
          </uxap:ObjectPageSubSection>
        </uxap:subSections>
      </uxap:ObjectPageSection>

    <!-- BUSINESS PURPOSE -->
      <uxap:ObjectPageSection
        id="businessPurposeSection"
        title="{i18n_erd>@businessHeader}"
        titleUppercase="false"
      >
        <uxap:subSections>
          <uxap:ObjectPageSubSection>
            <form:SimpleForm
              editable="true"
              class="generalForm"
              layout="ResponsiveGridLayout"
              labelSpanXL="12"
              labelSpanL="12"
              labelSpanM="12"
              labelSpanS="12"
              adjustLabelSpan="true"
              columnsXL="1"
              columnsL="1"
              columnsM="1"
              emptySpanXL="6"
              emptySpanL="4"
              emptySpanM="2"
              singleContainerFullSize="true"
            >
              <form:content>
                <Label text="{i18n_erd>@description}" />
                <TextArea
                  id="busdef"
                  value="{galileiModel>/businessDefinition}"
                  rows="5"
                  enabled="{galileiModel>/businessDefinition__available/status}"
                  tooltip="{galileiModel>/businessDefinition__available/messages/0}"
                ></TextArea>

                <Label text="{i18n_erd>@purpose}"></Label>
                <Input
                  id="buspurp"
                  value="{galileiModel>/businessDefinitionPurpose}"
                  enabled="{galileiModel>/businessDefinitionPurpose__available/status}"
                  tooltip="{galileiModel>/businessDefinitionPurpose__available/messages/0}"
                />

                <Label text="{i18n_erd>@businessContactPers}"></Label>
                <Input
                  id="buscont"
                  value="{galileiModel>/businessDefinitionContact}"
                  enabled="{galileiModel>/businessDefinitionContact__available/status}"
                  tooltip="{galileiModel>/businessDefinitionContact__available/messages/0}"
                />

                <Label text="{i18n_erd>@responsibleTeam}"></Label>
                <Input
                  id="responsible"
                  type="Text"
                  value="{galileiModel>/businessDefinitionResponsibleTeam}"
                  showSuggestion="true"
                  valueHelpRequest="handleValueHelp"
                  showValueHelp="false"
                  suggestionItems="{teamData>/teams}"
                  enabled="{galileiModel>/businessDefinitionResponsibleTeam__available/status}"
                  tooltip="{galileiModel>/businessDefinitionResponsibleTeam__available/messages/0}"
                >
                <suggestionItems>
                  <core:Item text="{galileiModel>text}" />
                </suggestionItems>
                </Input>

                <Label text="{i18n_erd>@tags}"></Label>
                <MultiInput
                  id="tags"
                  tokens="{galileiModel>/businessDefinitionTags}"
                  tokenUpdate="onTagTokenUpdate"
                  showValueHelp="false"
                  enabled="{galileiModel>/businessDefinitionTags__available/status}"
                  tooltip="{galileiModel>/businessDefinitionTags__available/messages/0}"
                >
                  <tokens>
                    <Token
                      key="{galileiModel>key}"
                      text="{galileiModel>text}"
                    />
                  </tokens>
                </MultiInput>
              </form:content>
            </form:SimpleForm>
          </uxap:ObjectPageSubSection>
        </uxap:subSections>
      </uxap:ObjectPageSection>

    <!-- Steampunk Services -->
      <uxap:ObjectPageSection
        id="steampunkServicesSection"
        title="{i18n_erd>bws_header}"
        titleUppercase="false"
        visible="{
          parts: [
          {path:'replicationModel>/federationOnly'},
          {path:'replicationModel>/dataAccess'},
          {path:'galileiModel>/parameters'},
          {path:'galileiModel>/isDeltaTable'},
          {path:'galileiModel>/isRemote'},
          {path:'galileiModel>/fileStorage'}
          ],
          formatter:'.tabServicesVisibilityFormatter'
        }"
      >
        <uxap:subSections>
          <uxap:ObjectPageSubSection>
            <core:Fragment
              fragmentName="sap.cdw.components.ermodeler.view.TableServices"
              type="XML"
            />
          </uxap:ObjectPageSubSection>
        </uxap:subSections>
      </uxap:ObjectPageSection>

    <!-- Partitions -->
      <uxap:ObjectPageSection
        id="partitionSection"
        title="{parts:['galileiModel>/partitions', 'galileiModel>/fileStorage'], formatter: '.partitionsTitleFormatter' }"
        titleUppercase="false"
        visible="{path:'galileiModel>/isLocal', formatter: '.partitionsSectionVisibleFormatter' }" >
        <uxap:subSections>
          <uxap:ObjectPageSubSection>
            <core:Fragment
              class="filterList"
              fragmentName="sap.cdw.components.tableeditor.view.Partitions"
              type="XML"
            />
          </uxap:ObjectPageSubSection>
        </uxap:subSections>
      </uxap:ObjectPageSection>

    <!-- Dependent Objects -->
      <uxap:ObjectPageSection
        id="dependentObjectSection"
        title="{
          parts: [
              {path:'i18n_erd>dependentObjectList'},
              {path:'header>/dependentObjects/items'},
              {path:'header>/dependentObjects/numberOfItemsWithoutPermissionToView'},
              {path:'workbenchEnv>/deltaReadOnly'}
          ],
          formatter:'.collectionHeaderTextBusyFormatter'
        }"
        titleUppercase="false"
      >
        <uxap:subSections>
          <uxap:ObjectPageSubSection>
            <core:Fragment
              fragmentName="sap.cdw.components.ermodeler.view.DependentObjectTable"
              type="XML"
            />
          </uxap:ObjectPageSubSection>
        </uxap:subSections>
      </uxap:ObjectPageSection>
    </uxap:sections>
  </uxap:ObjectPageLayout>
  <mvc:XMLView
    id="remoteTableScheduleReplication"
    viewName="sap.cdw.components.taskscheduler.view.TaskSchedule"
    class="remoteTableScheduleReplicationClass"
  />
</mvc:View>
