<core:FragmentDefinition
  xmlns:core="sap.ui.core"
  xmlns:mvc="sap.ui.core.mvc"
  xmlns:l="sap.ui.layout"
  xmlns="sap.m"
  xmlns:t="sap.ui.table"
>
 <VBox>
    <t:Table
      id="inputParametersTables"
      rows="{galileiModel>/parameters}"
      class="sapUiTinyMarginBegin"
      alternateRowColors="true"
      selectionMode="None"
      enableBusyIndicator="true"
      showNoData="true"
      noDataText="{i18n_erd>NO_DATA}"
      width="100%"
      visibleRowCount="{path:'galileiModel>/parameters',formatter:'.minVisibleRowCountParams'}"
    >
     <t:columns>
      <t:Column id="parameterName" width="16%">
        <Label
         text="{i18n_erd>@parameterNameLabel}"
        />
        <t:template>
          <Text text="{galileiModel>name}"/>
        </t:template>
      </t:Column>
      <t:Column id="parameterDataType" width="16%">
        <Label
          text="{i18n_erd>@parameterDataTypeLabel}"
        />
        <t:template>
        <Text text="{galileiModel>displayType}"/>
        </t:template>
      </t:Column>
      <t:Column id="parameterDefaultValue" width="60%" visible="true">
        <Label
          text="{i18n_erd>@parameterDefaultValueLabel}"
        />
        <t:template>
          <VBox>
          <Input
            id="defaultInputTextId"
            type="Text"
            placeholder="{path:'galileiModel>displayType', formatter:'.placeHolderVisibilityFormatter'}"
            valueState="{ path:'galileiModel>name', formatter:'.valueStateVisibilityFormatter'}"
            valueStateText="{galileiModel>/defaultValueError}"
            liveChange=".onDefaultValueChange($event, ${galileiModel>displayType},  ${galileiModel>name})"
            value="{galileiModel>defaultValue}"
            editable="{parts: ['galileiModel>/isDeltaTable', 'galileiModel>/isRemote'], formatter:'.defaultValueFormatter'}"
            visible="{path:'galileiModel>dataType',formatter:'.stringVisibilityFormatter'}"
          />
          <Input
            id="defaultInputNumId"
            type="Number"
            placeholder="{path:'galileiModel>displayType', formatter:'.placeHolderVisibilityFormatter'}"
            valueState="{ path:'galileiModel>name', formatter:'.valueStateVisibilityFormatter'}"
            valueStateText="{galileiModel>/defaultValueError}"
            liveChange=".onDefaultValueChange($event, ${galileiModel>displayType},  ${galileiModel>name})"
            value="{galileiModel>defaultValue}"
            editable="{parts: ['galileiModel>/isDeltaTable', 'galileiModel>/isRemote'], formatter:'.defaultValueFormatter'}"
            visible="{path:'galileiModel>dataType',formatter:'.numericVisibilityFormatter'}"
          />
          <Input
            id="defaultInputDecimalId"
            value="{galileiModel>defaultValue}"
            type="Number"
            valueLiveUpdate="true"
            placeholder="{path:'galileiModel>displayType', formatter:'.placeHolderVisibilityFormatter'}"
            visible="{path:'galileiModel>dataType',formatter:'.decimalInputVisibility'}"
            change="validateDecimal"
            liveChange="validateDecimalLiveChange"
            valueState="{ path:'galileiModel>name', formatter:'.valueStateVisibilityFormatter'}"
            valueStateText=""
          />
          <DatePicker
            id="defaultInputDateId"
            visible="{= ${galileiModel>dataType} === 'cds.Date' ? true : false}"
            valueFormat="yyyy-MM-dd"
            placeholder="{path:'galileiModel>dataType', formatter:'.placeHolderVisibilityFormatter'}"
            value="{galileiModel>defaultValue}"
            enabled="{parts: ['galileiModel>/isDeltaTable', 'galileiModel>/isRemote'], formatter:'.defaultValueFormatter'}"
            editable="false"
          />
          <DateTimePicker
              id="datetimeDefaultInputId"
              placeholder="{path:'galileiModel>dataType', formatter:'.placeHolderVisibilityFormatter'}"
              value="{galileiModel>defaultValue}"
              enabled="{parts: ['galileiModel>/isDeltaTable', 'galileiModel>/isRemote'], formatter:'.defaultValueFormatter'}"
              valueFormat="yyyy-MM-dd HH:mm:ss"
              visible="{= (${galileiModel>dataType} === 'cds.DateTime' || ${galileiModel>dataType} === 'cds.Timestamp') ? true : false}"
              editable="false"
          />
          <TimePicker
            id="timeDefaultInputId"
            placeholder="{path:'galileiModel>dataType', formatter:'.placeHolderVisibilityFormatter'}"
            value="{galileiModel>defaultValue}"
            enabled="{parts: ['galileiModel>/isDeltaTable', 'galileiModel>/isRemote'], formatter:'.defaultValueFormatter'}"
            visible="{= ${galileiModel>dataType} === 'cds.Time' ? true : false}"
            valueFormat="HH:mm:ss"
            width="100%"
            editable="false"
          />
          <Select
              selectedKey="{parts:['galileiModel>defaultValue'], formatter:'.booleanKeyFormatter'}"
              placeholder="{galileiModel>displayType}"
              change="validateBoolean"
              visible="{= ${galileiModel>displayType} === 'Boolean'}"
              width="500%"
          >
              <items>
                <core:ListItem
                  text="{i18n>@none}"
                  key=""
                />
                <core:ListItem
                  text="{i18n>@param_true}"
                  key="true"
                />
                <core:ListItem
                  text="{i18n>@param_false}"
                  key="false"
                />
              </items>
          </Select>
        </VBox>
        </t:template>
      </t:Column>
     </t:columns>
    </t:Table>
 </VBox>
</core:FragmentDefinition>
