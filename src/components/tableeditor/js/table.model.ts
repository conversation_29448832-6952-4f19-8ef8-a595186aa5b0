/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */
import { SupportedFeaturesService } from "../../commonmodel/api/SupportedFeaturesService";
import * as sharedModelProperties from "../../commonmodel/model/sharedDefinitions/Properties";

sap.galilei.namespace("sap.cdw.tableEditor", function () {
  "use strict";

  const skylineERModeler = !!SupportedFeaturesService.getInstance().isSkylineERModelerEnabled();
  // if new Skyline ERModeler is enabled (the old ERModel will not be loaded, therefore table model should not depends on ERModel)
  const parent = skylineERModeler ? "sap.cdw.commonmodel.Model" : "sap.cdw.ermodeler.Model";
  const modelExtraProperties = !skylineERModeler
    ? {}
    : {
        // Properties for ORD info
        ...sharedModelProperties.ordProperties,
        // Indicates that the model is for Data Layer.
        isDataLayer: {
          dataType: sap.galilei.model.dataTypes.gBool,
          get: function () {
            return true;
          },
        },
      };
  const modelExtraMethods = !skylineERModeler
    ? {}
    : {
        // Validate the model
        validate: async function (): Promise<any> {
          return sap.cdw.ermodeler.Validation.validateModel(this);
        },
      };

  const modelDefinition = {
    contents: {
      "sap.cdw.tableEditor": {
        classDefinition: "sap.galilei.model.Package",
        displayName: "Table Model",
        namespaceName: "sap.cdw.tableEditor",
        classifiers: {
          Model: {
            displayName: "Table Model",
            parent: parent,
            properties: {
              ...modelExtraProperties,
              dataCategory: {
                dataType: sap.galilei.model.dataTypes.gString,
                get: function () {
                  return this.table?.dataCategory;
                },
              },
              isNew: {
                dataType: sap.galilei.model.dataTypes.gBool,
                isInternal: true,
                defaultValue: false,
                get: function () {
                  return this.table?.isNew;
                },
              },
              isRemote: {
                dataType: sap.galilei.model.dataTypes.gBool,
                isCached: false,
                get: function () {
                  return this.table?.isRemote;
                },
              },
              isLTF: {
                dataType: sap.galilei.model.dataTypes.gBool,
                isCached: false,
                get: function () {
                  return this.table?.isLTF;
                },
              },
              isDataTransportAllowed: {
                dataType: sap.galilei.model.dataTypes.gBool,
                isCached: false,
                get: function () {
                  return this.table?.isDataTransportAllowed;
                },
              },
              hasAIChange: {
                dataType: sap.galilei.model.dataTypes.gBool,
                isCached: false,
                get: function () {
                  return this.table?.hasAIChange;
                },
              },
              contentOwner: {
                dataType: sap.galilei.model.dataTypes.gBool,
                isCached: false,
                get: function () {
                  return this.table?.contentOwner;
                },
              },
              isLocalSchema: {
                dataType: sap.galilei.model.dataTypes.gBool,
                get: function () {
                  return this.table?.isLocalSchema;
                },
              },
              isRemoteDetailsLoaded: {
                dataType: sap.galilei.model.dataTypes.gBool,
                get: function () {
                  return this.table?.isRemoteDetailsLoaded;
                },
              },
              isCrossSpace: {
                dataType: sap.galilei.model.dataTypes.gBool,
                isCached: false,
                get: function () {
                  return this.table?.isCrossSpace;
                },
              },
              "#objectStatus": {
                dataType: sap.galilei.model.dataTypes.gInteger,
                isInternal: false,
                isCached: false,
                get: function () {
                  return this.table && this.table["#objectStatus"];
                },
              },
              deploymentDate: {
                dataType: sap.galilei.model.dataTypes.gInteger,
                isInternal: false,
                get: function () {
                  return this.table?.deploymentDate;
                },
              },
              deploymentStatus: {
                isCached: false,
                get: function () {
                  return this.table?.deploymentStatus;
                },
              },
              deploymentErrorCode: {
                isCached: false,
                get: function () {
                  return this.table?.deploymentErrorCode;
                },
              },
              deploymentErrorDescription: {
                isCached: false,
                get: function () {
                  return this.table?.deploymentErrorDescription;
                },
              },
              "Model.skipEntitiesUniquenessValidation": {
                name: "skipEntitiesUniquenessValidation",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: true,
              },
              canDeploy: {
                dataType: sap.galilei.model.dataTypes.gBool,
                isInternal: true,
                isCached: false,
                defaultValue: true,
                get: function () {
                  return this.table?.canDeploy;
                },
              },
              location: {
                dataType: sap.galilei.model.dataTypes.gString,
                isInternal: true,
                isCached: false,
                get: function () {
                  return this.table?.location;
                },
              },
              remoteSourceName: {
                dataType: sap.galilei.model.dataTypes.gString,
                isInternal: true,
                isCached: false,
                get: function () {
                  return this.table?.remoteSourceName;
                },
              },
              partitioningExists: {
                dataType: sap.galilei.model.dataTypes.gBool,
                isInternal: true,
                isCached: false,
                defaultValue: false,
                get: function () {
                  return this.table?.partitioningExists;
                },
              },
              statisticsExists: {
                dataType: sap.galilei.model.dataTypes.gBool,
                isInternal: true,
                isCached: false,
                defaultValue: false,
                get: function () {
                  return this.table?.statisticsExists;
                },
              },
              adapter: {
                dataType: sap.galilei.model.dataTypes.gString,
                isInternal: true,
                isCached: false,
                get: function () {
                  return this.table?.adapter;
                },
              },
              changeManagementInfo: {
                dataType: sap.galilei.model.dataTypes.gBlob,
                isInternal: true,
              },
              validationMessages: {
                dataType: sap.galilei.model.dataTypes.gBlob,
                isInternal: true,
              },
              hasPendingError: {
                dataType: sap.galilei.model.dataTypes.gBool,
                isInternal: true,
                get: function () {
                  return this.table?.hasPendingError;
                },
              },
              numberOfRetentionDays: {
                dataType: sap.galilei.model.dataTypes.gInteger,
                defaultValue: 90,
              },
            },
            references: {
              "Model.table": {
                name: "table",
                contentType: "sap.cdw.commonmodel.Table",
                isMany: false,
                get: function () {
                  return this.entities.get(0);
                },
              },
              "Model.targetEntities": {
                name: "targetEntities",
                contentType: "sap.cdw.commonmodel.Table",
                isMany: true,
                isComputed: true,
                isContainment: true,
                isVolatile: true,
                isInternal: true,
                isSupportNotification: true,
              },
            },
            methods: {
              createTargetEntity: function (sClassName, oParam, oContainer) {
                const oClass = sap.galilei.model.getClass(sClassName),
                  oResource = oContainer && oContainer?.resource;
                let oNewObject;
                if (oClass && oResource) {
                  oNewObject = oClass.create(oResource, oParam);
                  this.targetEntities.push(oNewObject);
                }
                return oNewObject;
              },
              ...modelExtraMethods,
            },
          },
        },
      },
    },
  };

  const resource = new sap.galilei.model.Resource("sap.cdw.tableEditor.model");
  const reader = new sap.galilei.model.JSONReader();
  reader.load(resource, modelDefinition);
});
