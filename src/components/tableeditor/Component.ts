/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 * // FILEOWNER: [tableeditor]
 */

import { State } from "@sap/dwc-circuit-breaker";
import {
  IBottomViewsContent,
  ICanPreviewData,
  IDetailsConfiguration,
  IEditorDocument,
  IPredeployResult,
  ISaveOptions,
  SidepanelMode,
  ToolbarSection,
} from "../abstractbuilder/api";
import {
  isBWPCEPushEnabled,
  isGenAISemanticEnabled,
  isSparkSelectionVacuumEnabled,
  isTableDeleteDataEnabled,
  isTableRestoreEnabled,
  isTableTasksUseActiveRecords,
} from "../abstractbuilder/commonservices/FeatureFlagCheck";
import * as RepositoryUtils from "../abstractbuilder/utility/RepositoryUtils";
import { smartExtendComponent } from "../basecomponent/BaseComponent";
import { GenAIService } from "../commonmodel/api/GenAIService";
import { SupportedFeaturesService } from "../commonmodel/api/SupportedFeaturesService";
import { CsnAnnotations, DataWarehouse, EndUserText } from "../commonmodel/csn/csnAnnotations";
import {
  cloneJson,
  ensureBusinessName,
  getEntityAndContextNameFromCSN,
  getEntityNameFromCSN,
  getObjectNamesFromCSN,
} from "../commonmodel/csn/csnUtils";
import {
  ABAPType,
  CDSDataType,
  DataCategory,
  DataTypeDefinitions,
  HANADataTypeDefinitions,
} from "../commonmodel/model/types/cds.types";
import {
  StorageType,
  getListOfConsumedInputParameters,
  isORDTable,
  updateAlreadyAvailableIPValues,
} from "../commonmodel/utility/CommonUtils";
import { GModelHandler } from "../commonmodel/utility/GModelHandler";
import {
  AbstractDataBuilderEditorComponent,
  AbstractDataBuilderEditorComponentClass,
} from "../databuilder/AbstractDataBuilderEditorComponent";
import { Editor, Section, dataAccessTypes } from "../databuilder/utility/Constants";
import {
  getEndpointUrlOfLastVersion,
  openDatabuilderValidationsPopoverBy,
  openVersionPageIncurrentTab,
} from "../databuilder/utility/DatabuilderHelper";
import { getChildren } from "../datasourcebrowser/utility/DatasourceUtility";
import { editCSNAnnotations } from "../ermodeler/js/utility/CommonUtils";
import { checkIfTableHasData } from "../ermodeler/js/utility/sharedFunctions";
import { ToolName } from "../reuse/utility/Constants";
import { getDeltaTableOnly } from "../reuse/utility/DeltaUtil";
import { getLocalTableDeleteTask } from "../reuse/utility/LocalTableDeleteDataUtil";
import { MessageHandler } from "../reuse/utility/MessageHandler";
import { ContentType, HttpMethod, ServiceCall } from "../reuse/utility/ServiceCall";
import { ObjectStatus } from "../reuse/utility/Types";
import { showDialog } from "../reuse/utility/UIHelper";
import { isCircuitBreakerYellowStateEnabled } from "../shell/featureState/CircuitBreakerYellowStateFeatureState";
import { ShellContainer } from "../shell/utility/Container";
import { Repo } from "../shell/utility/Repo";
import { ObjectNameDisplay } from "../userSettings/utility/Constants";
import { TableEditorClass } from "./controller/TableEditor.controller";
export class TableEditorComponentClass extends AbstractDataBuilderEditorComponentClass {
  private model: sap.cdw.tableEditor.Model = undefined;
  private table: sap.cdw.commonmodel.Table;
  private editDataButton: sap.m.Button;
  private toolbarStateBeforeEditData: { canCreateOrUpdateModel?; canShareModel?; deployButton? } = {};
  private baseI18nModel: sap.ui.model.resource.ResourceModel;
  private customValidationButton: sap.m.Button;
  private tableResourceModel: sap.ui.model.resource.ResourceModel;
  private partitioningInfo: any;

  /**
   * @override
   */
  public init(...parameters): void {
    super.init.apply(this, parameters);
    const bundleName = require("../abstractbuilder/i18n/i18n.properties");
    this.baseI18nModel = new sap.ui.model.resource.ResourceModel({
      bundleName: bundleName,
    });
    const tablebundleName = require("./i18n/i18n.properties");
    this.tableResourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: tablebundleName,
    });
  }

  /**
   * @inheritdoc
   * @implements
   */
  public getModelTitle(): string {
    const controller = this.controller();
    return controller.localizeText("modelNameTable");
  }

  /**
   * @override
   */
  public getHelpScreenId(): string {
    return "tablemodeler";
  }

  /**
   * @override
   */
  public getBottomViewsContent(): IBottomViewsContent {
    const bottomViewsConfig = super.getBottomViewsContent();
    const workbench = this.getWorkbench();
    bottomViewsConfig.views.push({
      id: "dataValidation",
      title: "Data Validation",
      viewName: require("../databuilder/view/DataValidation.view.xml"),
      toolbarItems: [
        new sap.m.Button("dummy", {
          icon: "sap-icon://accessibility",
          tooltip: "{i18n>close}",
          press: () => {
            workbench.getBottomDetailsManager().updateTabTitle("dataValidation", "MyNewTitle");
          },
          type: sap.m.ButtonType.Transparent,
        }),
        new sap.m.Button("backToPreview", {
          icon: "sap-icon://arrow-left",
          tooltip: "{i18n>close}",
          press: () => {
            workbench.getBottomDetailsManager().selectTab("dataPreview");
          },
          type: sap.m.ButtonType.Transparent,
        }),
      ],
    });

    bottomViewsConfig.views.push({
      id: "dataValidation2",
      title: "Data Validation 2",
      viewName: require("../databuilder/view/DataValidation.view.xml"),
      toolbarItems: [
        new sap.m.Button("closeFirst", {
          icon: "sap-icon://inspect-down",
          tooltip: "{i18n>close}",
          press: () => {
            workbench.getBottomDetailsManager().getTab("dataValidation").setVisible(false);
          },
          type: sap.m.ButtonType.Transparent,
        }),
      ],
    });
    return bottomViewsConfig;
  }

  /**
   * @override
   */
  public onChangeObjectNameDisplay(objectNameDisplay: ObjectNameDisplay) {
    const model = this.getGalileiModelSync();
    if (model) {
      model.resource.applyUndoableAction(
        () => (model.objectNameDisplay = objectNameDisplay),
        "ChangeObjectNameDisplay",
        /* protectedFromundo */ true
      );
    }
    // super.onChangeObjectNameDisplay(objectNameDisplay); // this refreshes the property panel and needs to be done after update of galilei model
  }

  public async setPackageInfo(packageValue: string, pendingErrors: string) {
    const model = await this.getGalileiModel();
    const table = model?.table;
    // use protectedFromUndo in applyUndoableAction to avoid model change to dirty status
    table?.resource?.applyUndoableAction(
      function () {
        table.packageValue = packageValue;
        table.packageStatus = pendingErrors;
      },
      "update packageValue and packageStatus",
      /* protectedFromUndo */ true
    );
  }

  public appendPackageMetaInfoToDocument(document: IEditorDocument): IEditorDocument {
    const csn = document?.csn;
    const oGalileiModel = this.getGalileiModelSync();
    const packageValue = oGalileiModel?.table?.packageValue;
    if (csn && packageValue) {
      // only append package info for the editor that implement this
      if (packageValue !== "_NONE_KEY_PACKAGE_") {
        const modelName =
          Object.keys(document.csn.definitions).length === 1 ? Object.keys(document.csn.definitions)[0] : undefined;
        if (modelName) {
          // eslint-disable-next-line dot-notation
          if (csn.definitions[modelName]["_meta"]) {
            csn.definitions[modelName]["_meta"]["#repositoryPackage"] = packageValue;
          } else {
            csn.definitions[modelName]["_meta"] = {
              "#repositoryPackage": packageValue,
            };
          }
        }
      }
    }

    return document;
  }

  /**
   * @override
   */
  public async setContextObject(
    spaceId: string,
    modelId: string,
    loadingData?: any,
    isActive: boolean = true
  ): Promise<void> {
    super.setContextObject(spaceId, modelId, loadingData);
    this.controller().getView().setBusy(true);
    const workbench = this.getWorkbench();
    if (isActive) {
      const model = loadingData?.objectModel?.container;
      if (!model?.isSkylineERModel || isGenAISemanticEnabled()) {
        (sap.cdw as any).loadOldERMetaModel?.();
      }
      if (this.getWorkbench().getCurrentEditorOption() === "editdata") {
        workbench.switchPreviewFullscreenMode(true);
      }
      this.controller()?.setContextObject(spaceId, modelId);
      workbench.workbenchModel.setProperty("/activeSection", Section.DATAVIEW);
      if (!this.isObjectPageMode()) {
        workbench.onToggleSidepanel(SidepanelMode.hidden);
      }
      if (loadingData && !loadingData.objectModel && (loadingData.csn === null || loadingData.csn === undefined)) {
        let oEntityRepositoryObject = await RepositoryUtils.getRepositoryObject(spaceId, modelId, {
          details: ["id", "name", "csn"],
        });
        oEntityRepositoryObject = {
          csn: oEntityRepositoryObject.csn,
          file: oEntityRepositoryObject,
        };
        await this.openTableEditor(oEntityRepositoryObject);
      } else {
        await this.openTableEditor(loadingData);
      }
    }
    this.controller().getView().setBusy(false);
  }

  /**
   * @override
   */
  public appendExtraEditorSettingsToDocument(galileiModel: any, document: any) {
    const entity = galileiModel?.table;
    if (!SupportedFeaturesService.getInstance().isHWDSupportCompounding() && entity && document) {
      // Save hierarchyNameColumn in to editorSettings
      const hierarchyNameColumn = entity.hierarchyNameColumn?.name;
      GModelHandler.writeExtraEditorSettings(document, entity, { hierarchyNameColumn });
    }
  }

  /**
   * @override
   */
  public postProcessEditorSettings(galileiModel: any, loadingData: any) {
    const entity = galileiModel?.table;
    const resource = galileiModel?.resource;
    if (!SupportedFeaturesService.getInstance().isHWDSupportCompounding() && entity && loadingData && resource) {
      const { hierarchyNameColumn } = GModelHandler.readExtraEditorSettings(loadingData, entity);
      resource.applyUndoableAction(
        () => {
          entity.hierarchyNameColumn = hierarchyNameColumn
            ? entity.dimensionElements?.find((e) => e.name === hierarchyNameColumn)
            : undefined;
        },
        "Set hierarchyNameColumn when loading",
        /* Do not allow undo*/ true
      );
    }
  }

  /**
   * @override
   */
  public isBrowserViewAvailable(): boolean {
    return false;
  }

  /**
   * @override
   */
  public isObjectStatusIconVisible(): boolean {
    return true;
  }

  /**
   * @override
   */
  public isShareEntityVisible(): boolean {
    return !this.isEditDataMode();
  }

  /**
   * @override
   */
  public isAIMenuButtonVisible(): boolean {
    return true;
  }

  /**
   * @override
   */
  public isDetailsVisible(): boolean {
    return false;
  }

  /**
   * @override
   */
  isValidationPopoverVisible(): boolean {
    if (this.getWorkbench().getWorkbenchEnvModel().getProperty("/panels/bottom/fullscreen")) {
      return false;
    } else {
      return true;
    }
  }

  /**
   * @override
   */
  public isUploadAvailable(
    canCreateOrUpdateModel: boolean,
    hasConsumptionUpdatePrivilege: boolean,
    isRemote?: boolean,
    isLocalSchema?: boolean,
    isCrossSpace?: boolean,
    deploymentDate?: string,
    isModelTimeDimension?: boolean,
    isCustomerDPTenantAndSAPSpace?: boolean,
    isLTF?: boolean
  ) {
    let privilege = canCreateOrUpdateModel;
    const workbench = this.getWorkbench();
    const envModel = workbench.getWorkbenchEnvModel();
    const isDeltaReadonly = envModel.getProperty("/deltaReadOnly");
    if (isLTF) {
      return false;
    }

    if (isDeltaReadonly) {
      return false;
    }

    if (!isCustomerDPTenantAndSAPSpace) {
      privilege = hasConsumptionUpdatePrivilege && !isModelTimeDimension;
      if (privilege && !isRemote && !isLocalSchema && !isCrossSpace && deploymentDate && !this.isEditDataMode()) {
        return true;
      }
    }
    return false;
  }

  /**
   * @inheritdoc
   * @override
   */
  public isDeleteDataVisible(
    canCreateOrUpdateModel: boolean,
    hasConsumptionUpdatePrivilege: boolean,
    isRemote?: boolean,
    isLocalSchema?: boolean,
    isCrossSpace?: boolean,
    deploymentDate?: string,
    isModelTimeDimension?: boolean,
    isCustomerDPTenantAndSAPSpace?: boolean,
    isDataIntegrationUpdate?: boolean,
    isDwcConsumptionUpdate?: boolean
  ): boolean {
    const workbench = this.getWorkbench();
    const envModel = workbench.getWorkbenchEnvModel();
    const isDeltaReadonly = envModel.getProperty("/deltaReadOnly");
    if (isDeltaReadonly) {
      return false;
    }
    if (!(isDataIntegrationUpdate || isDwcConsumptionUpdate)) {
      return false;
    }
    let privilege = canCreateOrUpdateModel;
    if (!isCustomerDPTenantAndSAPSpace) {
      privilege = hasConsumptionUpdatePrivilege && !isModelTimeDimension;
      return !!(privilege && !isRemote && !isLocalSchema && !isCrossSpace && deploymentDate && !this.isEditDataMode());
    }
    return false;
  }

  /**
   * @inheritdoc
   * @override
   */
  public isRefreshVisible(
    canCreateOrUpdateModel: boolean,
    isRemote?: boolean,
    isLocalSchema?: boolean,
    isCrossSpace?: boolean,
    location?: string
  ): boolean {
    // visible for both SDI and SDA based connections
    if (canCreateOrUpdateModel && (isRemote || isLocalSchema)) {
      return true;
    }
    return false;
  }

  /**
   * @inheritdoc
   * @override
   */
  public isRefreshEnabled(isRemote?: boolean, isLocalSchema?: boolean, isRemoteDetailsLoaded?: boolean): boolean {
    // enabled for both SDI and SDA based connections
    if ((isRemote && isRemoteDetailsLoaded) || isLocalSchema) {
      return true;
    }
    return false;
  }

  /**
   * @inheritdoc
   * @override
   */
  public isShowHierarchiesButtonVisible(dataCategory: DataCategory, isLTF?: boolean): boolean {
    if (isLTF) {
      return false;
    }

    return !this.isEditDataMode();
  }

  /**
   * @inheritdoc
   * @override
   */
  public isShowHierarchiesCount(allHierarchies: any): number {
    return allHierarchies && allHierarchies.length > 0 ? allHierarchies.length : undefined;
  }

  /**
   * @inheritdoc
   * @override
   */
  public isEnableHierarchiesButton(dataCategory: DataCategory): boolean {
    return dataCategory === DataCategory.DIMENSION;
  }

  /**
   * @inheritdoc
   * @implements
   */
  customSave(): boolean {
    if (this.isEditDataMode()) {
      const workbench = this.getWorkbench();
      const previewController = workbench.getCurrentCustomDetailsController();
      previewController?.onFullscreenModeSave && previewController.onFullscreenModeSave();
      return true;
    }
    return false;
  }

  /**
   * @override
   */
  public customSaveDeploySuccessMessage(deploy: boolean, currentMessage: string, saveAs?: boolean): string {
    const workbench = this.getWorkbench();
    if (!deploy) {
      if (isTableRestoreEnabled() && this.getWorkbench()?.isInVersionRestorePage && !saveAs) {
        return workbench.getText("modelRestoredSuccessful", [workbench?.versionId]);
      }
      return workbench.getText("tableSavedSuccessful");
    }
    return currentMessage;
  }

  /**
   * @override
   */
  public customSaveDeployInvalidNameMessage(currentMessage: string): string {
    const workbench = this.getWorkbench();
    return workbench.getText("enterValidTableName");
  }

  /**
   * @override
   */
  public customSaveDeployEmptyNameMessage(currentMessage: string): string {
    const workbench = this.getWorkbench();
    return workbench.getText("enterTableName");
  }

  /**
   * @override
   */
  public async getGalileiModel(): Promise<any> {
    return this.getGalileiModelSync();
  }

  /**
   * @override
   */
  public getAIEntity() {
    return this.getTableEntity();
  }

  /**
   * @override
   */
  public getGalileiModelSync(): any {
    return this.model;
  }

  public backToMainPage(isVisible) {
    if ((this.controller()?.getView()?.byId("associationDetailsPage") as sap.m.Page)?.["isActive"]()) {
      this.controller().setListPageVisible(isVisible);
    }
  }

  public setDefaultSectionAsGeneral() {
    if (
      (this.controller()?.getView()?.byId("tableEditorPage") as sap.uxap.ObjectPageLayout)?.getSelectedSection() !==
      "tableEditor--generalSection"
    ) {
      (this.controller()?.getView()?.byId("tableEditorPage") as sap.uxap.ObjectPageLayout).scrollToSection(
        "tableEditor--generalSection",
        100
      );
    }
  }

  /**
   * @override
   * @param options save options
   */
  public async onDeploy(galileiModel: any, options: ISaveOptions): Promise<any> {
    this.backToMainPage(true);
    if (
      this.isUploadAvailable(
        false /* canCreateOrUpdateModel*/,
        false /* hasConsumptionUpdatePrivilege*/,
        galileiModel.table && galileiModel.table.isRemote,
        galileiModel.table && galileiModel.table.isLocalSchema,
        galileiModel.table && galileiModel.table.isCrossSpace
      )
    ) {
      await this.deployTable(options);
    } else {
      await super.onDeploy(galileiModel, options);
    }
  }

  public onPostDeploymentStatusUpdated(model): void {
    model.validate();
  }

  /**
   * @override
   */
  public canPreviewData(editorDocument: any): ICanPreviewData {
    const workbench = this.getWorkbench();
    const featureFlags = (sap.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel).getData();
    if (!workbench?.canPreviewCSNData()) {
      const consumptionPrivileges = ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_CONSUMPTION");
      if (consumptionPrivileges?.read !== false) {
        return {
          canPreviewData: true,
          previewByObjectName: true,
        };
      }
      return {
        canPreviewData: false,
        message: workbench.getText("txtNoPrivilegesViewData"),
      };
    }
    return { canPreviewData: true };
  }

  /**
   * @override
   */
  public async onPredeploy(galileiModel: any): Promise<IPredeployResult> {
    for (
      let i = 0;
      i < (galileiModel && galileiModel.table && galileiModel.table.elements && galileiModel.table.elements.length);
      i++
    ) {
      const element = galileiModel.table.elements.get(i);
      if (element) {
        delete element.isNew; // Remove temperary isNew after deploy, then tech name of element is disabled
      }
    }
    if (galileiModel.table.isRemote) {
      // For undeployed tables, do not prevent the deploy
      if (!this.isSDIbasedConnection(galileiModel.table.location)) {
        if (
          galileiModel.table?.partitionedColumn &&
          !galileiModel.table.csn.elements[galileiModel.table?.partitionedColumn]?.key &&
          galileiModel.table.remoteFilter?.length > 0
        ) {
          this.showDeploymentErrorDialog(0);
          return {
            abort: true,
          };
        } else if (
          (!galileiModel.table?.partitionedColumn ||
            galileiModel.table.csn.elements[galileiModel.table?.partitionedColumn]?.key) &&
          galileiModel.table.remoteFilter?.length > 0
        ) {
          this.showDeploymentErrorDialog(1);
          return {
            abort: true,
          };
        } else if (
          galileiModel.table?.partitionedColumn &&
          !galileiModel.table.csn.elements[galileiModel.table?.partitionedColumn]?.key &&
          galileiModel.table.remoteFilter?.length === 0
        ) {
          this.showDeploymentErrorDialog(2);
          return {
            abort: true,
          };
        }
      }
      if (
        galileiModel.table.deploymentStatus !== 0 &&
        (galileiModel?.canDeploy === false || galileiModel?.table?.canDeploy === false)
      ) {
        this.showErrorDialog();
        return {
          abort: true,
        };
      } else {
        const workbench = this.getWorkbench();
        try {
          if (galileiModel.table.deploymentStatus !== 0) {
            const response = (await ServiceCall.get(
              getEndpointUrlOfLastVersion(workbench.spaceName, this.table.name)
            )) as any;
            if (response.data) {
              let content;
              if (Array.isArray(response.data)) {
                content = response.data[0]?.repairedContent
                  ? response.data[0]?.repairedContent
                  : response.data[0]?.content;
              } else {
                content = response.data?.repairedContent ? response.data?.repairedContent : response.data?.content;
              }
              if (content) {
                const replicationModel = this.getEditorControl()?.getModel("replicationModel");
                const currentDataAccess = replicationModel?.getProperty("/dataAccess");
                const replicationType = replicationModel?.getProperty("/replicationType");
                const csn = JSON.parse(content);
                const deployedEntity = csn?.definitions ? csn.definitions[this.table.name] : undefined;
                const deployedElements = deployedEntity.elements;
                const isChanged = sap.cdw.tableEditor.ModelImpl.checkIfRemoteTableChanged(
                  this.table,
                  deployedElements,
                  true
                );
                let hasFilterOrAssociationChanged = false;
                hasFilterOrAssociationChanged = sap.cdw.tableEditor.ModelImpl.checkIfFiltersOrAssociationsChanged(
                  this.table,
                  deployedEntity
                );
                if (isChanged || hasFilterOrAssociationChanged) {
                  const type = this.getDataAccess(currentDataAccess);
                  /* Allow deployment only when the data access is remote and replication type is undefined.
                   If replication type is set, there is a schedule defined */
                  if (type === StorageType.REMOTE && (replicationType === undefined || replicationType === null)) {
                    galileiModel.table.canDeploy = true;
                  } else {
                    galileiModel.table.canDeploy = false;
                  }
                  if (!galileiModel.table.canDeploy) {
                    this.showErrorDialog();
                    return {
                      abort: true,
                    };
                  }
                }
              }
            }
          }
        } catch (err) {
          return {
            abort: false,
          };
        }
      }
    }
    return {
      abort: false,
    };
  }

  /** Error dialog to display information that deployment
   * cannot be proceeded until the data is removed
   */
  public showErrorDialog() {
    const workbench = this.getWorkbench();
    const self = this;
    sap.m.MessageBox.show(this.tableResourceModel.getProperty("deployErrorForReplicatedTables"), {
      icon: sap.m.MessageBox.Icon.ERROR,
      id: "deployErrorMessageBox",
      title: this.tableResourceModel.getProperty("titError"),
      actions: [sap.m.MessageBox.Action.CLOSE],
      initialFocus: sap.m.MessageBox.Action.CLOSE,
      onClose: null,
    });
  }

  /**
   * @override
   */
  public onValidationPopoverButtonPress(event: IEvent<sap.m.Button, {}>) {
    const databuilderWbcontroller = this.getWorkbench();
    openDatabuilderValidationsPopoverBy(this.model, event.getSource(), [
      {
        model: databuilderWbcontroller.erResourceModel,
        name: "i18n_erd",
      },
    ]);
  }

  /**
   * @override
   */
  public isLeftContentResizable(leftPanelVisible: boolean): boolean {
    return false;
  }

  /**
   * @override
   */
  public isDeleteDataEnabled(
    isRemote: boolean | undefined,
    isLocalSchema: boolean | undefined,
    deploymentDate: string | undefined
  ): boolean {
    return !!(!isRemote && !isLocalSchema && deploymentDate);
  }

  /**
   * @override
   */
  public onModelDateUpdated(): void {
    const workbench = this.getWorkbench();
    const toolbarModel = workbench.getToolbarModel();
    const controller = this.controller();
    controller.setObjectModel(this.getTableEntity());
    toolbarModel.refresh(true);
    if ((controller?.byId("associationDetailsPage") as sap.m.Page)?.["isActive"]()) {
      controller.setListPageVisible(true);
    }
    if ((this.getTableModel() as any).deploymentStatus === 1) {
      if (workbench.isPreviewdataActive()) {
        const previewController = workbench?.getCurrentCustomDetailsController();
        previewController?.reloadPreviewMetadata && previewController.reloadPreviewMetadata();
      }
    }
  }

  public async openTableEditor(loadingData?: { csn: ICsn; file: any; objectModel: any }) {
    this.createTableModel(loadingData);
    try {
      if (loadingData && loadingData.csn && loadingData.file) {
        let fnCallback;
        const controller = this.controller();
        controller.getView().setBusy(true);
        const associationsList = controller?.getView().byId("associationsList") as sap.ui.table.Table;
        fnCallback = () => {
          associationsList?.getBinding("rows")?.getModel()?.refresh(true);
          associationsList?.setBusy(false);
        };
        associationsList?.setBusy(true);

        if (isTableRestoreEnabled() && loadingData?.file?.isVersioningRestorePage) {
          loadingData.csn = loadingData?.file?.versionDetails?.csn;
        }
        let csn = loadingData.csn;

        const isTableWithDeltaCapture = Object.keys(loadingData.csn.definitions).length == 2; // Determines it is an active records view
        // Block accessing delta table from url by making the editor read only
        let deltaReadOnly = false;
        const workbench = this.getWorkbench();
        const envModel = workbench.getWorkbenchEnvModel();
        envModel.setProperty("/deltaReadOnly", deltaReadOnly);
        if (isTableWithDeltaCapture && loadingData.file?.name?.includes("_Delta")) {
          const entityName = loadingData.file?.name;
          const index = loadingData.file.name.lastIndexOf("_Delta");
          const activeName = loadingData.file.name.substring(0, index);
          if (
            loadingData.csn.definitions[entityName]?.["@DataWarehouse.enclosingObject"] &&
            loadingData.csn.definitions[activeName]?.["@DataWarehouse.delta"]
          ) {
            deltaReadOnly = true;
            envModel.setProperty("/canCreateOrUpdateModel", false);
            envModel.setProperty("/deltaReadOnly", deltaReadOnly);
          }
          let clonecsn = cloneJson(loadingData.csn);
          const result = getDeltaTableOnly(clonecsn);
          csn = result.csn;
          this["deltaActiveViewName"] = entityName;
        }

        if (isTableWithDeltaCapture && !deltaReadOnly) {
          let clonecsn = cloneJson(loadingData.csn);
          const result = getDeltaTableOnly(clonecsn);
          csn = result.csn;
          this["deltaActiveViewName"] = result.tableName;
        }
        // If the content import owner is BW. editor will be in read only since it is BW object
        if (
          isBWPCEPushEnabled() &&
          loadingData?.csn?.definitions[loadingData.file?.name]?.["@DataWarehouse.contentImport.owner"] === "BW"
        ) {
          envModel.setProperty("/canCreateOrUpdateModel", false);
          envModel.setProperty("/canDeployModel", true);
        }
        envModel.updateBindings(true);
        await this.updateTableEntity(csn, loadingData.file, fnCallback);
        controller.getView().setBusy(false);
      }

      // Load simple types and update base type for simple types and elements
      await this.loadAndUpdateSimpleTypes(this.model, !this.isObjectPageMode());
    } catch (e) {
      // may happen if backend calls failed.
      // see DW101-10163 Resilience Testing: "has data" requested within Meta Data Handling
      if (console.error) {
        console.error(e);
      }
    }

    const tableEditorController = this.controller();
    const table = this.getTableEntity();
    if (table && tableEditorController) {
      if (!table.name && !table.label) {
        const count = 1;
        const name = `${table.classDefinition.displayName}_${count}`;
        const label = `${table.classDefinition.displayName} ${count}`;
        table.name = name;
        table.label = label;
        if (this.model) {
          this.model.validate();
        }
      }
      tableEditorController.setObjectModel(table);
    }
    this.extendWorkbenchToolbar();
    if (this.getWorkbench().getCurrentEditorOption() === "editdata") {
      this.onEditDataPressed();
    }
    this.backToMainPage(true);
    this.setDefaultSectionAsGeneral();
    if (!this.isObjectPageMode()) {
      this.clearUndoRedo();
      if (loadingData && this.model) {
        this.model.validate();
      }
    }
    this["validationResult"] = undefined;
    const hasPendingErrors =
      loadingData?.file?.hasPendingError === "BATCHBREAKING" ||
      loadingData?.file?.hasPendingError === "BATCHNONBREAKING";
    table.hasPendingError = hasPendingErrors;
    const canCreateOrUpdateModel = this.getWorkbench()?.getWorkbenchEnvModel()?.getData()?.canCreateOrUpdateModel;
    if (hasPendingErrors && table.isRemote && canCreateOrUpdateModel) {
      const result = await this.openRefreshValidationDialog();
      this.applyValidationMessages(result);
    }
    this.getWorkbench()?.setDirtyShell(this.isDirty());
  }

  /**
   * @override
   */
  public async onExportCSN(spaceId: string, clearCache: boolean): Promise<any> {
    const table = this.getTableEntity();
    const workbench = this.getWorkbench();
    const message = workbench.getText("exportingCompleted");
    return await this.exportDocumentAsCSN.call(this, spaceId, table, await this.getGalileiModel(), clearCache, message);
  }

  /**
   * @override
   */
  public onEditAnnotations(): void {
    const table = this.getTableEntity();
    editCSNAnnotations(table);
  }

  public async getDocument(name: string, model?: any, options?: any): Promise<IEditorDocument> {
    model = model || this.getModel();
    const table = model && this.getTableEntity();
    if (model && table && sap.cdw.ermodeler && sap.cdw.ermodeler.ErModelToCsn) {
      ensureBusinessName(table);
      // TODO: Add Namespace
      return { csn: await sap.cdw.ermodeler.ErModelToCsn.getCSN(name, model, this.getCSNOptions(table, options)) };
    } else {
      Promise.reject(new Error("Cannot find erModelToCsn."));
    }
  }

  public async onDataPreview() {
    const table = this.getTableEntity();
    const genericWorkbench = this.getWorkbench();

    if (table && genericWorkbench) {
      const isValid =
        !table.aggregatedValidations || (table.aggregatedValidations && table.aggregatedValidations.status !== "error");
      // Show datapreview
      genericWorkbench.showDetails();
      table.fromRepositoryTree = true;
      // Loads metadata.js
      if (!sap.cdw.querybuilder.ViewModelToCsn.metadata) {
        sap.cdw.querybuilder.ViewModelToCsn.metadata = require("../../services/metadata");
      }
      //remote table should not support input parameters for a delta table
      if (
        table &&
        ((table?.isRemote && !table.isDeltaTable) || table?.isLocalSchema) &&
        table?.parameters &&
        table.parameters?.length > 0
      ) {
        const consumedParameters = getListOfConsumedInputParameters(table, table);
        const activeEditor = this.getWorkbench()?.getActiveEditor()?.getId();
        const finallyConsumedParams = updateAlreadyAvailableIPValues(
          consumedParameters,
          genericWorkbench.workbenchModel,
          undefined,
          activeEditor
        );
        genericWorkbench.showIPPopUPForDataPreview(table, finallyConsumedParams);
      } else {
        const csn = await sap.cdw.querybuilder.ViewModelToCsn.getCSN(table.name, table.container, {
          rootObject: table, // { "name": previewName, "fromRepositoryTree": true },
          dataPreview: true,
        });
        const objectStatus = table["#objectStatus"];
        let objectStatusNumber;
        if (typeof objectStatus === "number") {
          objectStatusNumber = objectStatus;
        } else if (typeof objectStatus === "undefined") {
          objectStatusNumber = ObjectStatus.hasNoObjectStatus;
        } else {
          objectStatusNumber = Number(objectStatus);
        }
        genericWorkbench.previewDataForObject(table, csn, objectStatusNumber === ObjectStatus.notDeployed);
      }
      delete table.fromRepositoryTree;
      if (isValid) {
        return;
      } else {
        genericWorkbench.closeSidepanelPreviewData();
        genericWorkbench.workbenchModel.setProperty("/detailsPage/isCurrentTablePreviewValid", false);
      }
    }
  }
  /**
   * @override
   */
  public isUndoRedoAvailable(): boolean {
    return true;
  }

  public openRefreshValidationDialog() {
    return new Promise<any>(async (resolve, reject) => {
      let valDialog = sap.ui.getCore().byId("refreshValidationDialog") as sap.m.Dialog;
      if (!valDialog) {
        const refreshValDlg = require("./view/RefreshValidationDialog.fragment.xml");
        valDialog = sap.ui.xmlfragment(refreshValDlg, this.controller()) as sap.m.Dialog;
      }
      this.controller().getView().addDependent(valDialog);
      const table = this.getTableEntity();
      valDialog.setBusy(true);
      const galileiModel = new sap.galilei.ui5.GalileiModel(table);
      valDialog.setModel(galileiModel, "oModel");
      valDialog.setModel(this.tableResourceModel, "i18n");
      const workbench = this.getWorkbench();
      workbench.setBusy(true, this.tableResourceModel.getResourceBundle().getText("importProgressmsg", [table.name]));
      const self = this;
      const { space } = workbench.getSpaceAndModel();
      await this.requestValidateService(space, table.name);
      if (this["validationResult"]) {
        if (
          this["validationResult"]?.validationResult?.errors?.length === 0 &&
          this["validationResult"]?.validationResult?.warnings?.length === 0
        ) {
          sap.m.MessageToast.show(this.tableResourceModel.getProperty("txttableuptodate"));
          workbench.setBusy(false);
          valDialog.setBusy(false);
        } else {
          if (this["validationResult"].csnRemote[table.name][table.name]) {
            const elements = this["validationResult"].csnRemote[table.name][table.name].elements;
            const keys = Object.keys(elements);
            let keysOrderedElements = [];
            if (table.deploymentStatus === 0) {
              keysOrderedElements = table.orderedElements.map((o) => {
                return o.name;
              });
            }
            const newElementsList = [];
            keys.forEach((key) => {
              const element = elements[key];
              let isNewCol = false;
              if (table.deploymentStatus !== 0) {
                if (element["@Temp.isNew"]) {
                  isNewCol = true;
                }
              } else if (!keysOrderedElements?.includes(key)) {
                isNewCol = true;
              }
              if (isNewCol) {
                newElementsList.push({
                  name: key,
                  type: element.type,
                  displayType: sap.cdw.commonmodel.ObjectImpl.computeDisplayType(element, element.type),
                  label: element[EndUserText.label],
                  dataType: element.type,
                  nativeDataType: element[DataWarehouse.native_dataType],
                  allowedExpressions: element[DataWarehouse.filter_allowed_Expressions],
                  filterEnabled: element[DataWarehouse.filter_enabled],
                  length: element.length ? element.length : 0,
                  precision: element.precision ? element.precision : 0,
                  scale: element.scale ? element.scale : 0,
                  isKey: element.key ? element.key : false,
                  notNull: element.notNull ? element.notNull : false,
                });
              }
            });

            galileiModel.setProperty("/isNewColTableVisible", newElementsList.length > 0);
            galileiModel.setProperty("/newElementsList", newElementsList);

            workbench.setBusy(false);
            valDialog.setBusy(false);

            const endButton = new sap.m.Button({
              text: this.tableResourceModel.getResourceBundle().getText("canceltext"),
              press: () => {
                valDialog.close();
                valDialog.destroy();
                const selectedNewObjects = [];
                resolve({
                  selectedNewObjects: selectedNewObjects,
                  isAbort: true,
                  validationResults: this["validationResult"],
                });
              },
            });

            const beginButton = new sap.m.Button({
              text: this.tableResourceModel.getResourceBundle().getText("txt_apply_refresh"),
              press: () => {
                const newRefreshTable = sap.ui.getCore().byId("newRefreshTable") as sap.m.Table;
                let selectedItems = newRefreshTable.getSelectedItems();
                selectedItems = selectedItems.map((item) => item.getBindingContext("oModel").getObject());
                const selectedItemsNames = selectedItems?.map((o) => o["name"]);
                const keyCols = newElementsList?.filter((el) => el.isKey);
                let isAllKeyColsSelected = true;
                for (let i = 0; i < keyCols?.length; i++) {
                  const colName = keyCols[i]?.name;
                  if (!selectedItemsNames?.includes(colName)) {
                    isAllKeyColsSelected = false;
                    break;
                  }
                }
                if (isAllKeyColsSelected) {
                  const selectedNewObjects = selectedItems || [];
                  const resp = {
                    selectedNewObjects: selectedNewObjects,
                    isAbort: false,
                    validationResults: this["validationResult"],
                  };
                  valDialog.close();
                  valDialog.destroy();
                  resolve(resp);
                } else {
                  let errDialog = sap.ui.getCore().byId("errorConfirmDialog") as sap.m.Dialog;
                  if (!errDialog) {
                    const errorDlg = require("./view/ErrorDialog.fragment.xml");
                    errDialog = sap.ui.xmlfragment(errorDlg, this.controller()) as sap.m.Dialog;
                  }
                  this.controller().getView().addDependent(errDialog);
                  errDialog.setModel(this.tableResourceModel, "i18n");
                  const closeBtn = new sap.m.Button({
                    text: this.tableResourceModel.getResourceBundle().getText("txt_refresh_cancel"),
                    press: () => {
                      errDialog.close();
                      errDialog.destroy();
                    },
                  });
                  errDialog.addButton(closeBtn);
                  errDialog.open();
                }
              },
              type: sap.m.ButtonType.Emphasized,
            });
            valDialog.addButton(beginButton);
            valDialog.addButton(endButton);

            valDialog.setEscapeHandler(() => {
              valDialog.destroy();
              resolve({ selectedNewObjects: [], isAbort: true });
            });
            valDialog.open();
          } else {
            let errDialog = sap.ui.getCore().byId("errorConfirmDialog") as sap.m.Dialog;
            if (!errDialog) {
              const errorDlg = require("./view/ErrorDialog.fragment.xml");
              errDialog = sap.ui.xmlfragment(errorDlg, this.controller()) as sap.m.Dialog;
            }
            this.controller().getView().addDependent(errDialog);
            errDialog.setModel(this.tableResourceModel, "i18n");
            const errorMsgText = sap.ui.getCore().byId("errorDetailsText") as sap.m.Text;
            errorMsgText.setText(this.tableResourceModel.getResourceBundle().getText("txt_table_dropped"));
            const closeBtn = new sap.m.Button({
              text: this.tableResourceModel.getResourceBundle().getText("txt_refresh_cancel"),
              press: () => {
                errDialog.close();
                errDialog.destroy();
                const galileiModel = this.controller()
                  .getView()
                  ?.getModel("galileiModel") as sap.galilei.ui5.GalileiModel;
                galileiModel.getData().hasTableDropped = true;
                galileiModel.getData().validate();
              },
            });
            errDialog.addButton(closeBtn);
            errDialog.open();
          }
        }
      }
      workbench.setBusy(false);
    });
  }

  public async applyValidationMessages(result) {
    const workbench = this.getWorkbench();
    const table = this.getTableEntity();
    const spaceGUID = await workbench.getSpaceGUID();
    const remoteCsnElements = result.validationResults.csnRemote[table.name][table.name].elements;
    const colsList = await sap.cdw.tableEditor.ModelImpl.computeExcludeandNewColumnList(
      table,
      spaceGUID,
      this.model,
      remoteCsnElements
    );
    let excludedColumns = [];
    const self = this;
    if (!result.isAbort) {
      excludedColumns = colsList?.excludedList;
      // Get details about partitioning
      if (table.partitioningExists) {
        this.partitioningInfo = await this.getPartitionInfo(workbench.spaceName, table.name);
      }
      if (this.model?.table) {
        this.model.table.resource.applyUndoableAction(
          function () {
            this.model.table.isRefreshApplied = true;
            this.model.table.isRefreshApplyOrCancelTaken = true;
            this.model.bDirty = true;
          },
          "",
          true
        );
      }
      workbench.setDirtyShell(this.isDirty());
      workbench.setBusy(true, this.tableResourceModel.getResourceBundle().getText("importProgressmsg", [table.name]));
      let response;
      const partitionedColumn = this.partitioningInfo?.column;
      const spaceType = this.getEditorControl()?.getModel("replicationModel")?.getData()?.spaceType;
      response = await sap.cdw.tableEditor.ModelImpl.updateRemoteTableChangesWithValidation(
        table,
        result.validationResults,
        excludedColumns,
        partitionedColumn,
        result.selectedNewObjects,
        spaceType
      );
      if (response) {
        MessageHandler.success(this.tableResourceModel.getProperty("importCompleted"));
        if (this.model.table.isRemote) {
          const replicationModel = this.getEditorControl()?.getModel("replicationModel");
          const currentDataAccess = replicationModel?.getProperty("/dataAccess");
          const replicationType = replicationModel?.getProperty("/replicationType");
          const type = this.getDataAccess(currentDataAccess);
          // If table is not deployed,data access is undefined , so should not set candeploy to false
          let isAllowed = false;
          if (table.deploymentStatus === 0 && type === undefined) {
            isAllowed = true;
          }
          // If replication type is set, there is a schedule defined, so block deployment
          if (
            (type === StorageType.REMOTE || isAllowed) &&
            (replicationType === undefined || replicationType === null)
          ) {
            table.canDeploy = true;
          } else {
            table.canDeploy = false;
          }
        }
        this.model.validate();
      } else {
        MessageHandler.success(this.tableResourceModel.getProperty("importFailed"));
      }
      workbench.setBusy(false);
      return Promise.resolve(true);
    } else {
      const response = await sap.cdw.tableEditor.ModelImpl.updateValidationMessages(table, result.validationResults);
      if (this.model?.table) {
        this.model.table.resource.applyUndoableAction(
          function () {
            self.model.table.isRefreshApplyOrCancelTaken = true;
          },
          "",
          true
        );
      }
      if (response) {
        this.model.validate();
        return Promise.reject(true);
      }
    }
  }

  public async requestValidateService(spaceId, tableName) {
    await ServiceCall.request<any>({
      url: `remoteobject/${spaceId}/validate/${encodeURIComponent(tableName)}`,
      type: HttpMethod.POST,
      contentType: ContentType.APPLICATION_JSON,
    }).then(
      (oResponse) => {
        this["validationResult"] = oResponse.data;
      },
      (oResponse) => {
        const errorCode =
          oResponse[0] &&
          oResponse[0].responseJSON &&
          oResponse[0].responseJSON.details &&
          oResponse[0].responseJSON.details.code;
        if (errorCode === 5921) {
          const message = this.tableResourceModel.getResourceBundle().getText("connectionIsUnavailable");
          MessageHandler.exception({ exception: oResponse, message: message });
        } else {
          MessageHandler.exception(oResponse.error);
        }
        return false;
      }
    );
  }

  /**
   * @override
   */
  public onUndo() {
    if (this.model) {
      if (this.isObjectPageMode()) {
        // in object page mode undo is handled by main editor
        this.getWorkbench().getActiveEditor().onUndo();
      } else {
        this.model.resource.undo();
      }
      this.model.validate();
      const controller = this.controller();
      if (controller) {
        controller.clearUiState();
      }
      if (this.isObjectPageMode()) {
        const table = this.getTableEntity();
        if (!table.isAlive) {
          // means that the table has been deleted
          this.getWorkbench().getPropertiesController().closeObjectPage();
        }
      }
    }
  }

  /**
   * @override
   */
  public onRedo() {
    if (this.model) {
      if (this.isObjectPageMode()) {
        // in object page mode redo is handled by main editor
        this.getWorkbench().getActiveEditor().onRedo();
      } else {
        this.model.resource.redo();
      }
      this.model.validate();
      const controller = this.controller();
      if (controller) {
        controller.clearUiState();
      }
      if (this.objectPageMode) {
        const table = this.getTableEntity();
        if (!table.isAlive) {
          // means that the table has been deleted
          this.getWorkbench().onToggleSidepanel(SidepanelMode.normal);
        }
      }
    }
  }

  /**
   * @inheritdoc
   * @override
   */
  public isEditExportVisible(): boolean {
    return !this.isObjectPageMode();
  }

  public getTableEntity() {
    if (this.table) {
      return this.table;
    }
    return this.model && this.model.table;
  }

  public getTableModel() {
    if (this.model) {
      return this.model;
    }
  }

  public controller(): TableEditorClass {
    return super.controller() as TableEditorClass;
  }

  /**
   * @override
   */
  public getDetailsConfiguration(): IDetailsConfiguration {
    const tableModel = this.getTableEntity();
    const activeEditor = this.getWorkbench()?.getActiveEditor()?.getId();
    const featureFlags = (sap.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel).getData();
    const isOpenSQLSchema = tableModel.csn?.["@DataWarehouse.external.schema"];
    if (
      (tableModel.isLocal || tableModel.isRemote || isOpenSQLSchema) &&
      activeEditor &&
      activeEditor === "TABLEEDITOR"
    ) {
      return {
        getViewName: (selectedObject) => require("../abstractbuilder/view/mdmPreviewEditor.view.xml"),
        useDefault: {
          useAlwaysDefault: false,
        },
      };
    } else {
      return {
        useDefault: {
          useAlwaysDefault: true,
        },
      };
    }
  }

  /**
   * @inheritdoc
   * @implements
   * @memberof TableEditorComponentClass
   */
  public onToggleDetails(): void {
    const workbench = this.getWorkbench();
    if (workbench.isPreviewdataActive()) {
      const customDetails = workbench.getCurrentCustomDetailsController();
      if (customDetails && customDetails.getIsInitialized()) {
        customDetails.setIsInitialized(false);
      }
      this.onDataPreview();
    }
  }

  public onOpenHierarchy(): void {
    const workbench = this.getWorkbench();
    const table = this.getTableEntity();
    const galileiModel = new sap.galilei.ui5.GalileiModel(table);

    const viewName = require("../ermodeler/view/Hierarchy.view.xml");
    const bundleName = require("../ermodeler/i18n/i18n.properties");
    const i18nModel = new sap.ui.model.resource.ResourceModel({
      bundleName: bundleName,
    });
    const sViewId = "hierarchyDialogView";
    const sDialogId = "hierarchyDialog";
    const view =
      (workbench.byId(sViewId) as sap.ui.core.mvc.View) ||
      sap.ui.view({
        id: workbench.createId(sViewId),
        type: sap.ui.core.mvc.ViewType.XML,
        viewName: viewName,
      });
    view.setModel(i18nModel, "i18n_erd");
    view.setModel(galileiModel, "galileiModel");
    const dialog =
      (workbench.byId(sDialogId) as sap.m.Dialog) ||
      new sap.m.Dialog({
        id: workbench.createId(sDialogId),
        title: i18nModel.getProperty("@hierarchyDialog"),
        type: "Standard",
        content: [view],
        contentWidth: "1000px",
        contentHeight: "400px",
        endButton: new sap.m.Button({
          text: i18nModel.getProperty("@close"),
          press: function () {
            dialog.close();
          },
          type: sap.m.ButtonType.Emphasized,
        }),
      }).addStyleClass("hierarchyDialogSplitterHeight");

    dialog.open();
  }

  public onPostModelNameChanged(galileiModel: any, modelNames: any, saveAs?) {
    // For Table Editor, set table name as model name
    const oTable = galileiModel.entities.get(0);
    if (oTable && oTable.isNew === false && !saveAs) {
      return;
    } else if (oTable) {
      if (oTable.name !== modelNames.technicalName) {
        oTable.name = modelNames.technicalName;
      }
      if (oTable.label !== modelNames.businessName) {
        oTable.label = modelNames.businessName;
      }
    }
  }

  public async onDeleteTableData(): Promise<any> {
    const workbench = this.getWorkbench();
    const { space } = workbench.getSpaceAndModel();
    const self = this;
    const model = this.getGalileiModel();
    const table = this.getTableEntity();
    if (table.hasData || table.isLTF) {
      if (!(await this.confirmOnDeleteTableDataHouseKeeping(table))) {
        return Promise.reject(false);
      }
      let tableName = table.name;
      let newSparkSetting;
      if (isSparkSelectionVacuumEnabled()) {
        if (this.controller().getView().getModel("galileiModel").getProperty("/newSparkDefinition")) {
          newSparkSetting = this.controller().getView().getModel("galileiModel").getProperty("/newSparkSetting");
        }
      }
      const options = {
        isDeltaTable: table.isDeltaTable,
        isFileStorage: table.isLTF,
        isPermanentDeletion: this["deleteSelectionResponse"].isPermanentDeletion,
        isMarkForDeletion: this["deleteSelectionResponse"].isLogicalDeletion,
        isHousekeepingDelete: this["deleteSelectionResponse"].isHouseKeepingDelete,
        retentionTimeInDays: this.controller().getView().getModel("galileiModel").getProperty("/numberOfRetentionDays"),
        newSparkSetting: newSparkSetting,
      };
      self["deleteOptions"] = options;
      const features = {
        isTableDeleteDataEnabled: isTableDeleteDataEnabled(),
        isTableTasksUseActiveRecords: isTableTasksUseActiveRecords(),
      };
      if (!features.isTableTasksUseActiveRecords) {
        if (table.isDeltaTable || table?.fileStorage) {
          tableName = table.deltaTableName; // To be removed after DWCO_LOCAL_TABLE_TASKS_ACTIVE_RECORDS is ON because all the object id will be active enity name
        }
        if (options.isFileStorage && options.isHousekeepingDelete) {
          tableName = table.name; // To be removed after DWCO_LOCAL_TABLE_TASKS_ACTIVE_RECORDS is ON because all the object id will be active enity name
        }
      }
      const reqBody = getLocalTableDeleteTask(space, tableName, options, features);
      // FIXME WORKBENCH messages should be moved to editor component from worbench component!
      const sURL = "tf/directexecute";
      workbench.setBusy(true);
      ServiceCall.post(sURL, { contentType: ContentType.APPLICATION_JSON }, true, JSON.stringify(reqBody))
        .then(
          (response) => {
            // hasData to be set after the delete notification success
          },
          (error) => {
            MessageHandler.error(workbench.getText("dataDeletionFailed"));
          }
        )
        .finally(() => {
          if (workbench.workbenchModel.getProperty("/panels/bottom/visible")) {
            this.onDataPreview();
          }
          workbench.setBusy(false);
          sap.m.MessageToast.show(workbench.getText("deletionStart", [table.name]));
        });
    } else {
      sap.m.MessageBox.show(this.tableResourceModel.getResourceBundle().getText("txt_norecords"), {
        icon: sap.m.MessageBox.Icon.INFORMATION,
        id: "dataNotExistsInfo",
        title: this.tableResourceModel.getProperty("tit_norecords"),
        actions: [sap.m.MessageBox.Action.OK],
        initialFocus: sap.m.MessageBox.Action.OK,
        onClose: null,
      });
    }
  }

  public async onRefreshTable(): Promise<any> {
    const table = this.getTableEntity();
    const workbench = this.getWorkbench();
    if (table.isRemote) {
      if (this.model?.table?.isRefreshApplied) {
        sap.m.MessageBox.show(
          this.tableResourceModel.getResourceBundle().getText("txt_tableRefreshApplied", [table.name]),
          {
            icon: sap.m.MessageBox.Icon.INFORMATION,
            id: "refreshApplied",
            title: this.tableResourceModel.getProperty("titInfo"),
            actions: [sap.m.MessageBox.Action.OK],
            initialFocus: sap.m.MessageBox.Action.OK,
            onClose: null,
          }
        );
      } else {
        const result = await this.openRefreshValidationDialog();
        this.applyValidationMessages(result);
        return Promise.resolve(true);
      }
    } else {
      const spaceGUID = await workbench.getSpaceGUID();
      try {
        workbench.setBusy(true, this.tableResourceModel.getResourceBundle().getText("importProcessing"));
        let csnElements = null;
        let csnParams;
        const path = [
          {
            id: table?.csn?.["@DataWarehouse.external.schema"],
            type: "schema",
          },
        ];

        const aChildren = await getChildren(
          path,
          workbench.spaceName,
          workbench.getActiveEditor().getBrowserOptions().editorCapabilities[0]
        );
        let currTable = null;
        aChildren.forEach((child) => {
          if (child.name === table?.csn?.["@DataWarehouse.external.entity"]) {
            currTable = child;
          }
        });
        if (currTable) {
          csnElements =
            currTable.definitions[table?.csn?.["@DataWarehouse.external.schema"] + "." + currTable.name].elements;
          csnParams =
            currTable.definitions[table?.csn?.["@DataWarehouse.external.schema"] + "." + currTable.name].params;
        } else {
          workbench.setBusy(false);
          MessageHandler.uiError(
            this.tableResourceModel.getResourceBundle().getText("errorMsgTableNotExist", [table.name]),
            "Error"
          );
          return Promise.reject(false);
        }
        const spaceType = this.getEditorControl()?.getModel("replicationModel")?.getData()?.spaceType;
        const isChanged = sap.cdw.tableEditor.ModelImpl.checkIfRemoteTableChanged(
          table,
          csnElements,
          false,
          spaceType,
          csnParams
        );
        workbench.setBusy(false);
        if (isChanged) {
          let colsList;
          let excludedColumns = [];
          if (!table.isLocalSchema) {
            colsList = await sap.cdw.tableEditor.ModelImpl.computeExcludeandNewColumnList(
              table,
              spaceGUID,
              this.model,
              csnElements
            );
          }
          const result = await this.openRefreshConfirmDialog(colsList?.newList);
          if (!result.isAbort) {
            excludedColumns = colsList?.excludedList;
            // Get details about partitioning
            if (table.partitioningExists) {
              this.partitioningInfo = await this.getPartitionInfo(workbench.spaceName, table.name);
            }
            if (this.model?.table) {
              this.model.table.resource.applyUndoableAction(
                function () {
                  this.model.table.isRefresh = true;
                  this.model.bDirty = true;
                },
                "",
                true
              );
            }
            workbench.setDirtyShell(this.isDirty());
            workbench.setBusy(
              true,
              this.tableResourceModel.getResourceBundle().getText("importProgressmsg", [table.name])
            );
            let response;
            if (!table.isLocalSchema) {
              const partitionedColumn = this.partitioningInfo?.column;
              response = await sap.cdw.tableEditor.ModelImpl.updateRemoteTableChanges(
                table,
                csnElements,
                excludedColumns,
                partitionedColumn,
                result.selectedNewObjects,
                spaceType,
                undefined,
                undefined,
                csnParams
              );
            } else {
              response = await sap.cdw.tableEditor.ModelImpl.updateRemoteTableChanges(
                table,
                csnElements,
                undefined,
                undefined,
                undefined,
                undefined,
                undefined,
                undefined,
                csnParams
              );
            }
            if (response) {
              MessageHandler.success(this.tableResourceModel.getProperty("importCompleted"));
              if (this.model.table.isRemote) {
                const replicationModel = this.getEditorControl()?.getModel("replicationModel");
                const currentDataAccess = replicationModel?.getProperty("/dataAccess");
                const replicationType = replicationModel?.getProperty("/replicationType");
                const type = this.getDataAccess(currentDataAccess);
                // If table is not deployed,data access is undefined , so should not set candeploy to false
                let isAllowed = false;
                if (table.deploymentStatus === 0 && type === undefined) {
                  isAllowed = true;
                }
                // If replication type is set, there is a schedule defined, so block deployment
                if (
                  (type === StorageType.REMOTE || isAllowed) &&
                  (replicationType === undefined || replicationType === null)
                ) {
                  table.canDeploy = true;
                } else {
                  table.canDeploy = false;
                }
              }
              this.model.validate();
            } else {
              MessageHandler.success(this.tableResourceModel.getProperty("importFailed"));
            }
            workbench.setBusy(false);
            return Promise.resolve(true);
          }
        } else {
          sap.m.MessageToast.show(this.tableResourceModel.getProperty("txttableuptodate"));
          return Promise.resolve(true);
        }
      } catch (err) {
        workbench.setBusy(false);
        if (err !== false) {
          const errorCode =
            err[0] && err[0].responseJSON && err[0].responseJSON.details && err[0].responseJSON.details.code;
          if (errorCode === 476) {
            MessageHandler.exception({
              exception: err,
              messageBundleId: "sap.cdw.components.tableeditor.i18n.i18n",
              messageKey: "errorMsgForTableNotExist",
              messageParams: [table.name],
            });
          } else {
            MessageHandler.exception({ exception: err });
          }
        }
        return Promise.reject(false);
      }
    }
  }

  public async getPartitionInfo(spaceName, tableName) {
    const sUrlStart = "partitioning/" + spaceName + "/remoteTables/" + encodeURIComponent(tableName);
    try {
      const oDetails = await ServiceCall.request<[]>({
        url: sUrlStart,
        type: HttpMethod.GET,
        contentType: ContentType.APPLICATION_JSON,
      });
      this.partitioningInfo = oDetails.data;
    } catch (error) {
      this.partitioningInfo = null;
    }
    return this.partitioningInfo;
  }

  public getDataAccess(dataAccess) {
    let dataAccessType;
    if (dataAccess === dataAccessTypes.REMOTE) {
      dataAccessType = "REMOTE";
    } else if (
      dataAccess === dataAccessTypes.REALTIME_REPLICATION ||
      dataAccess === dataAccessTypes.SNAPSHOT_REPLICATION
    ) {
      dataAccessType = "REPLICATED";
    }
    return dataAccessType;
  }

  public onUploadData(): void {
    const workbench = this.getWorkbench();
    const tableName = this.getTableEntity().name;
    const objectStatus = workbench.getToolbarModel().getProperty("/#objectStatus");
    if (objectStatus !== undefined && Number(objectStatus) !== ObjectStatus.deployed) {
      MessageHandler.error(this.baseI18nModel.getProperty("errorUndeployed"));
      return;
    }
    const self = this;
    Repo.getModelDetails(workbench.spaceId, tableName, ["csn"]).then((ioCsn) => {
      const viewData = {
        existingTable: ioCsn,
        spaceName: workbench.spaceId,
        spaceId: workbench.modelId,
      };
      showDialog(
        require("../fileupload/views/ImportFile.view.xml"),
        workbench.getText("uploadFile"),
        workbench.getText("import"),
        workbench.getText("cancel"),
        viewData
      )
        .then(() => {
          MessageHandler.success(workbench.getText("fileUploadDone"));
          self.model.table.resource.applyUndoableAction(
            function () {
              self.model.table.hasData = true;
            },
            "Set hasdata after upload",
            true
          );

          if (workbench.workbenchModel.getProperty("/panels/bottom/visible")) {
            this.onDataPreview();
          }
        })
        .catch(() => {
          return;
        });
    });
  }

  /**
   * @override
   */
  public notifySaveFinished(isSaveSuccessful: boolean) {
    if (isSaveSuccessful) {
      // reset toolbar dirty
      this.getWorkbench().getWorkbenchEnvModel().setProperty("/toolbar/isTableEditorDirty", false);
      // refresh dependent object list
      // sap.ui.getCore().getEventBus().publish("DEPENDENT_OBJECT_LIST", "REFRESH", {});   // not needed, see setObjectModel
      // clearing Undo and Redo stack options
      this.model.resource.clearUndoStack();
      if (this.model) {
        this.getWorkbench().getWorkbenchEnvModel().setProperty("/toolbar/canUndo", this.model.resource.canUndo());
        this.getWorkbench().getWorkbenchEnvModel().setProperty("/toolbar/canRedo", this.model.resource.canRedo());
      }
    }
    this.backToMainPage(true);
    /*  const entity = this.getTableEntity();
    if (isGenAISemanticEnabled() && entity.hasAIChange) {
      resetAIProperties(entity);
    } */
  }

  /**
   * @override
   */
  public getAlternativeModelNames(galileiModel: any) {
    const oTable = galileiModel.entities.get(0);
    return {
      businessName: oTable.label,
      technicalName: oTable.name,
    };
  }

  /**
   * @override
   */
  public sendValidationMessagesToBackend(): boolean {
    return true;
  }

  /**
   * @override
   */
  async onEnterDetailsFullscreenMode(): Promise<void> {
    if (this.isDataPreviewEditVisible()) {
      this.editDataButton?.setText(this.baseI18nModel.getProperty("closeDataEditor"));
      this.editDataButton?.setTooltip(this.baseI18nModel.getProperty("closeDataEditor"));
      const workbench = this.getWorkbench();
      const envModel = workbench.getWorkbenchEnvModel();

      // store env model propeerties
      this.toolbarStateBeforeEditData.canShareModel = envModel.getProperty("/canShareModel");
      this.toolbarStateBeforeEditData.canCreateOrUpdateModel = envModel.getProperty("/canCreateOrUpdateModel");
      this.toolbarStateBeforeEditData.deployButton = envModel.getProperty("/deployButton");

      envModel.setProperty("/viewGroupHidden", true);
      envModel.setProperty("/editGroupHidden", true);
      envModel.setProperty("/deployButton", false);
      envModel.setProperty("/canShareModel", false);
      envModel.setProperty("/canCreateOrUpdateModel", true);

      await this.waitForCustomDetailsController();
      const previewController = workbench.getCurrentCustomDetailsController();
      previewController?.onEnterDetailsFullscreenMode && previewController.onEnterDetailsFullscreenMode();
      workbench.canRunSaveHandler(false);
    }
  }

  /**
   * @override
   */
  async onExitDetailsFullscreenMode(): Promise<void> {
    if (this.isDataPreviewEditVisible()) {
      this.editDataButton?.setText(this.baseI18nModel.getProperty("openDataEditor"));
      this.editDataButton?.setTooltip(this.baseI18nModel.getProperty("openDataEditor"));
      const workbench = this.getWorkbench();
      const envModel = workbench.getWorkbenchEnvModel();
      envModel.setProperty("/viewGroupHidden", false);
      envModel.setProperty("/editGroupHidden", false);

      envModel.setProperty("/deployButton", this.toolbarStateBeforeEditData?.deployButton);
      envModel.setProperty("/canShareModel", this.toolbarStateBeforeEditData?.canShareModel);
      envModel.setProperty("/canCreateOrUpdateModel", this.toolbarStateBeforeEditData?.canCreateOrUpdateModel);

      const previewController = workbench.getCurrentCustomDetailsController();
      previewController?.onEnterDetailsFullscreenMode && previewController.onExitDetailsFullscreenMode();
      workbench.setDirtyShell(this.isDirty());
      workbench.canRunSaveHandler(true);

      // set hasdata information on switch from data editor
      if (this.table?.deploymentStatus !== 0) {
        const { space, model } = workbench.getSpaceAndModel();
        const self = this;
        await this.checkIfTableHasData(space, model).then((response) => {
          self.model.table.resource.applyUndoableAction(
            function () {
              self.model.table.hasData = response;
            },
            "Set hasdata while switching between data editor and table editor",
            true
          );
        });
      }
    }
  }

  /**
   * @override
   */
  public isDeploySupported() {
    return !this.isEditDataMode();
  }

  /**
   * @override
   */
  public isDirty(): boolean {
    if (this.isEditDataMode()) {
      const previewController = this.getWorkbench().getCurrentCustomDetailsController();
      if (previewController?.isModelSaved) {
        return !previewController.isModelSaved();
      }
    }
    let isDirty = this.getWorkbench().getWorkbenchEnvModel().getProperty("/toolbar/isTableEditorDirty");
    const oGalileiModel = this.getGalileiModelSync && this.getGalileiModelSync();
    if (oGalileiModel) {
      if (oGalileiModel.bDirty) {
        isDirty = oGalileiModel.bDirty;
      } else {
        isDirty = oGalileiModel.resource ? oGalileiModel.resource.canUndo() : false;
      }
    }
    this.getWorkbench().getWorkbenchEnvModel().setProperty("/toolbar/isTableEditorDirty", isDirty);
    return isDirty;
  }

  /**
   * @override
   */
  public getGalileiModelListener(resource: any) {
    const mappingChangeHandler = (target: any, propertyName: string) => {
      const node = target?.getAttribute("_galileiObject");
      // detect if compoundKeySequence is changed
      if (node?.qualifiedClassName === "sap.cdw.commonmodel.Table" && propertyName === "compoundKeySequence") {
        this.getWorkbench()["resourceChanged"](resource);
      }
    };
    return {
      orderedReferenceRemoved: mappingChangeHandler,
      orderedReferenceAdded: mappingChangeHandler,
      atomicReferenceSet: mappingChangeHandler,
    };
  }
  public getObjectStatusNumber() {
    const objectStatus = this.getWorkbench().getToolbarModel().getProperty("/#objectStatus");
    let objectStatusNumber;
    if (typeof objectStatus === "number") {
      objectStatusNumber = objectStatus;
    } else if (typeof objectStatus === "undefined") {
      objectStatusNumber = ObjectStatus.hasNoObjectStatus;
    } else {
      objectStatusNumber = Number(objectStatus);
    }
    return objectStatusNumber;
  }

  private navigateToMonitor() {
    const objectStatusNumber = this.getObjectStatusNumber();
    if (objectStatusNumber !== ObjectStatus.deployed) {
      MessageHandler.uiError(this.baseI18nModel.getProperty("editEnableDeploy"));
      return;
    }

    const featureFlags = (sap.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel).getData();

    const sapMLibrary = sap.ui.require("sap/m/library");
    const { space, model } = this.getWorkbench().getSpaceAndModel();
    const dataBuilderNavURL = `#/dataintegration&/di/${encodeURIComponent(space)}/localTableMonitor${
      this.table?.fileStorage
        ? `/${encodeURIComponent(model)}`
        : this.table?.fileStorage
        ? ``
        : `/${encodeURIComponent(model)}`
    }`;
    sapMLibrary.URLHelper.redirect(dataBuilderNavURL, false);
  }

  private isLocalTableMonitorEnabled(hanaState: State, ProvisioningState: State) {
    return (
      (hanaState === State.Green || (hanaState === State.Yellow && isCircuitBreakerYellowStateEnabled())) &&
      (ProvisioningState === State.Green ||
        (ProvisioningState === State.Yellow && isCircuitBreakerYellowStateEnabled())) &&
      !this.getWorkbench().workbenchModel.getProperty("/isVersioningReadOnlyMode")
    );
  }

  private isDataPreviewEditEnabled(hanaState: State, ProvisioningState: State) {
    this.editDataButton.setTooltip(this.baseI18nModel.getProperty("openDataEditor"));
    return (
      (hanaState === State.Green || hanaState === State.Yellow) &&
      (ProvisioningState === State.Green || ProvisioningState === State.Yellow) &&
      !this.getWorkbench().workbenchModel.getProperty("/isVersioningReadOnlyMode")
    );
  }

  private isDataPreviewEditVisible() {
    let hasPrivilage = false;
    let activities: any = {};
    const isSDPEnabled = sap.ui.getCore().getModel("featureflags").getProperty("/DWC_DUMMY_SPACE_PERMISSIONS");
    if (!isSDPEnabled) {
      activities = ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_CONSUMPTION");
    } else {
      activities = sap.ui.getCore().getModel("privilege").getProperty("/DWC_CONSUMPTION");
    }
    hasPrivilage = activities && activities.execute && activities.update;
    const tableModel = this.getTableEntity();
    const isTableLocal = tableModel?.isLocal;

    const isCustomerDPTenantAndSAPSpace = this.getWorkbench()
      .getWorkbenchEnvModel()
      .getProperty("/isCustomerDPTenantAndSAPSpace");
    const spaceAccessInfo = this.getWorkbench().getWorkbenchEnvModel().getProperty("/spaceAccessInfo");
    const canAccessSpace =
      !isCustomerDPTenantAndSAPSpace || !spaceAccessInfo || spaceAccessInfo["#writeContentAccess"] !== false;
    const isLockedSpace = spaceAccessInfo && spaceAccessInfo.status === "locked";
    const isTableEditable =
      canAccessSpace && !isLockedSpace && !!(tableModel?.csn && tableModel?.csn["@readonly"] !== true);

    return hasPrivilage && isTableLocal && isTableEditable && !this.table?.fileStorage;
  }

  private isEditDataMode() {
    return this.editDataButton?.getText() === this.baseI18nModel.getProperty("closeDataEditor");
  }

  private async waitForCustomDetailsController(): Promise<void> {
    const workbench = this.getWorkbench();
    const customDetails = workbench.getCurrentCustomDetailsController();
    if (!customDetails || (customDetails && !customDetails.getIsInitialized())) {
      await new Promise((resolve) => setTimeout(resolve, 50));
      return this.waitForCustomDetailsController();
    }
    return Promise.resolve();
  }

  private extendWorkbenchToolbar() {
    const workbench = this.getWorkbench();
    const featureFlags = (sap.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel).getData();

    this["navigateToMinotorButton"] = new sap.m.Button({
      id: workbench.createId("navigateToLocalTableMonitoring"),
      tooltip: this.baseI18nModel.getProperty("localTableMonitorTxt"),
      icon: "sap-icon://sac/simulate",
      type: sap.m.ButtonType.Transparent,
      press: this.navigateToMonitor.bind(this),
      visible: !!this.getTableEntity().isLocal, // only visible for local tables
      enabled: {
        parts: [{ path: "circuitbreaker>/DataHANA" }, { path: "circuitbreaker>/DataHANAProvisioningState" }],
        formatter: this.isLocalTableMonitorEnabled.bind(this),
      },
    });
    this["navigateToMinotorButton"].addStyleClass("sapUiTinyMarginEnd");
    // add button to toolbar using this user can navigate to local table monitoring
    workbench.extendToolbar(this["navigateToMinotorButton"], { section: ToolbarSection.rightButtons, position: 0 });
    if (this.isDataPreviewEditVisible() && !workbench.isObjectPageActive()) {
      this.toolbarStateBeforeEditData = {};

      this.editDataButton = new sap.m.Button({
        id: workbench.createId("mdm-editDataToggle"),
        text: this.baseI18nModel.getProperty("openDataEditor"),
        type: sap.m.ButtonType.Transparent,
        enabled: {
          parts: [{ path: "circuitbreaker>/DataHANA" }, { path: "circuitbreaker>/DataHANAProvisioningState" }],
          formatter: this.isDataPreviewEditEnabled.bind(this),
        },
      });
      this.editDataButton.addCustomData(
        new sap.ui.core.CustomData({
          key: "actionId",
          writeToDom: false,
          value: `${ToolName.DataBuilder}/${Editor.TABLEEDITOR}/editData`,
        })
      );
      this.customValidationButton = new sap.m.Button({
        id: "mdm-customValidationButton",
        icon: "sap-icon://validate",
        tooltip: "{i18n>validationStatus}",
        visible: {
          parts: [{ path: "workbenchEnv>/panels/bottom/fullscreen" }],
          formatter: this.formatterCustomValidationVisible.bind(this),
        },
        press: this.onCustomValidationPress.bind(this),
        type: {
          path: "workbenchEnv>/customPreviewValidation",
          formatter: this.formatterCustomValidationStatusType.bind(this),
        },
        text: {
          path: "workbenchEnv>/customPreviewValidation",
          formatter: this.formatterCustomValidationCount.bind(this),
        },
      });
      this.customValidationButton.addStyleClass("toolbarButtonTransparentBackground");
      this.editDataButton.attachPress(this.onEditDataPressed.bind(this));
      workbench.extendToolbar(this.editDataButton, {
        section: ToolbarSection.rightButtons,
        position: 1,
      });
      workbench.extendToolbar(this.customValidationButton, { section: ToolbarSection.rightButtons, position: 2 });
    }
  }

  private formatterCustomValidationVisible(isFullScreen: boolean): boolean {
    if (isFullScreen) {
      return true;
    } else {
      return false;
    }
  }

  private onCustomValidationPress(event: IEvent<sap.m.Button, {}>) {
    if (this.isEditDataMode()) {
      const previewController = this.getWorkbench().getCurrentCustomDetailsController();
      previewController.onValidationPopoverPress(event);
    }
  }

  private formatterCustomValidationStatusType(validation: []): sap.m.Button {
    if (this.isEditDataMode()) {
      const previewController = this.getWorkbench().getCurrentCustomDetailsController();
      const button = this.byId("mdm-customValidationButton") as sap.m.Button;
      const validationStatus = previewController.getValidationStatusType(validation, button);
      return validationStatus;
    }
  }

  private formatterCustomValidationCount(validation: []): number {
    if (this.isEditDataMode()) {
      const previewController = this.getWorkbench().getCurrentCustomDetailsController();
      const validationCount = previewController.getValidationsCount(validation);
      return validationCount;
    }
  }

  private onEditDataPressed() {
    const workbench = this.getWorkbench();
    const objectStatus = workbench.getToolbarModel().getProperty("/#objectStatus");
    const isTableEditorDirty = workbench.getWorkbenchEnvModel().getProperty("/toolbar/isTableEditorDirty");
    const tableModel = this.getTableEntity();
    let invalidTable = false;
    if (this.getWorkbench().getCurrentEditorOption() === "editdata") {
      const invalidEdit = this.isInvalidTableEditData(tableModel, objectStatus, true);
      invalidTable = invalidEdit ? invalidEdit : this.isInvalidTable(isTableEditorDirty, tableModel, objectStatus);
      if (invalidTable) {
        workbench.updateCurrentEditorOption("editdata", true);
        workbench.switchPreviewFullscreenMode(false);
        return;
      }
    } else {
      invalidTable = this.isInvalidTable(isTableEditorDirty, tableModel, objectStatus);
    }
    if (this.isEditDataMode()) {
      workbench.updateCurrentEditorOption("editdata", true);
      workbench.exitDetailsFullscreenMode();
      workbench.showSaveAsButton();
    } else if (!invalidTable) {
      if (this.getWorkbench().getCurrentEditorOption() !== "editdata") {
        workbench.updateCurrentEditorOption("editdata");
      }
      workbench.enterDetailsFullscreenMode();
      workbench.hideSaveAsButton();
    }
  }

  private isInvalidTableEditData(tableModel, objectStatus, isDataTypeRestriction) {
    let hasPrivilage = false;
    let activities: any = {};
    const isSDPEnabled = sap.ui.getCore().getModel("featureflags").getProperty("/DWC_DUMMY_SPACE_PERMISSIONS");
    if (!isSDPEnabled) {
      activities = ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_CONSUMPTION");
    } else {
      activities = sap.ui.getCore().getModel("privilege").getProperty("/DWC_CONSUMPTION");
    }
    hasPrivilage = activities && activities.execute && activities.update;
    if (!hasPrivilage) {
      MessageHandler.info(this.baseI18nModel.getProperty("editNoRights"));
      return true;
    }
    if (!tableModel?.isLocal) {
      MessageHandler.error(this.baseI18nModel.getProperty("editEnableLocal"));
      return true;
    }
    if (!!(tableModel?.csn && tableModel?.csn["@readonly"] === true)) {
      MessageHandler.error(this.baseI18nModel.getProperty("editDisableTimeTable"));
      return true;
    }
    const isCustomerDPTenantAndSAPSpace = this.getWorkbench()
      .getWorkbenchEnvModel()
      .getProperty("/isCustomerDPTenantAndSAPSpace");
    const spaceAccessInfo = this.getWorkbench().getWorkbenchEnvModel().getProperty("/spaceAccessInfo");
    const canAccessSpace =
      !isCustomerDPTenantAndSAPSpace || !spaceAccessInfo || spaceAccessInfo["#writeContentAccess"] !== false;
    if (!canAccessSpace) {
      MessageHandler.info(this.baseI18nModel.getProperty("editDisableRestrictedTable"));
      return true;
    }
    const isLockedSpace = spaceAccessInfo && spaceAccessInfo.status === "locked";
    if (isLockedSpace) {
      MessageHandler.info(this.baseI18nModel.getProperty("editDisableLockedSpace"));
      return true;
    }
    if (!isDataTypeRestriction) {
      return this.checkKeyAndDeployment(tableModel, objectStatus);
    }

    return false;
  }

  private isInvalidTable(isTableEditorDirty, tableModel, objectStatus) {
    if (isTableEditorDirty) {
      MessageHandler.error(this.baseI18nModel.getProperty("errorUnsavedChange"));
      return true;
    }
    if (tableModel?.csn?.elements) {
      const dimensionElements = Object.entries(tableModel.csn.elements).map(
        ([dimension, dimensionElement]) => dimensionElement
      ) as any;
      let invalidKeyType = [];
      let invalidNotNullType = [];
      const restrictedKeyTypes = [CDSDataType.BINARY, CDSDataType.HANA_BINARY];
      const restrictedNotNullTypes = [
        CDSDataType.BINARY,
        CDSDataType.HANA_BINARY,
        CDSDataType.LARGE_BINARY,
        CDSDataType.HANA_ST_GEOMETRY,
        CDSDataType.HANA_ST_POINT,
        ABAPType.GEOM_EWKB,
        ABAPType.RAWSTRING,
      ];
      for (let i = 0; i < dimensionElements.length; i++) {
        if (restrictedKeyTypes.includes(dimensionElements[i].type) && dimensionElements[i].key) {
          invalidKeyType.push(dimensionElements[i].type);
        }
        if (restrictedNotNullTypes.includes(dimensionElements[i].type) && dimensionElements[i].notNull) {
          invalidNotNullType.push(dimensionElements[i].type);
        }
      }
      invalidKeyType = invalidKeyType.filter((type, invalidKey, arr) => arr.indexOf(type) === invalidKey);
      invalidNotNullType = invalidNotNullType.filter(
        (type, invalidNotNull, arr) => arr.indexOf(type) === invalidNotNull
      );
      const invalidKeyDisplayName = [];
      const invalidNotNullDisplayName = [];
      if (invalidKeyType.length > 0) {
        invalidKeyType.forEach((datatype) => {
          invalidKeyDisplayName.push(
            `"${DataTypeDefinitions[datatype]?.text || HANADataTypeDefinitions[datatype]?.text || datatype}"`
          );
        });
        if (invalidKeyDisplayName.length === 2) {
          MessageHandler.error(
            this.baseI18nModel
              .getResourceBundle()
              .getText("invalidKey", [
                invalidKeyDisplayName.join(" " + this.baseI18nModel.getResourceBundle().getText("and") + " "),
              ])
          );
        } else {
          MessageHandler.error(
            this.baseI18nModel.getResourceBundle().getText("invalidKey", [invalidKeyDisplayName.join(", ")])
          );
        }
        return true;
      }
      if (invalidNotNullType.length > 0) {
        invalidNotNullType.forEach((datatype) => {
          invalidNotNullDisplayName.push(
            `"${DataTypeDefinitions[datatype]?.text || HANADataTypeDefinitions[datatype]?.text || datatype}"`
          );
        });
        if (invalidNotNullDisplayName.length === 2) {
          MessageHandler.error(
            this.baseI18nModel
              .getResourceBundle()
              .getText("invalidNotNull", [
                invalidNotNullDisplayName.join(" " + this.baseI18nModel.getResourceBundle().getText("and") + " "),
              ])
          );
        } else {
          MessageHandler.error(
            this.baseI18nModel.getResourceBundle().getText("invalidNotNull", [invalidNotNullDisplayName.join(", ")])
          );
        }
        return true;
      }
    }

    return this.checkKeyAndDeployment(tableModel, objectStatus);
  }

  private checkKeyAndDeployment(tableModel, objectStatus) {
    if (tableModel?.csn?.elements) {
      const dimensionElements = Object.entries(tableModel.csn.elements).map(
        ([dimension, dimensionElement]) => dimensionElement
      ) as any;
      // eslint-disable-next-line dot-notation
      if (!dimensionElements.some((dimensionElement) => dimensionElement["key"])) {
        MessageHandler.error(this.baseI18nModel.getProperty("errorMissingKey"));
        return true;
      }
    }
    let objectStatusNumber;
    if (typeof objectStatus === "number") {
      objectStatusNumber = objectStatus;
    } else if (typeof objectStatus === "undefined") {
      objectStatusNumber = ObjectStatus.hasNoObjectStatus;
    } else {
      objectStatusNumber = Number(objectStatus);
    }
    if (objectStatusNumber !== ObjectStatus.deployed) {
      MessageHandler.error(this.baseI18nModel.getProperty("errorUndeployed"));
      return true;
    }
    return false;
  }

  private async confirmOnDeleteTableDataHouseKeeping(model: any): Promise<boolean> {
    this["deleteSelectionResponse"] = {
      isPermanentDeletion: false,
      isLogicalDeletion: false,
      isHouseKeepingDelete: false,
    };
    const fragmentId = require("./view/deleteDialogForDelta.fragment.xml");
    return new Promise<boolean>((resolve, reject) => {
      sap.ui.core.Fragment.load({
        controller: this.controller(),
        id: "idDeleteRecordsMessageView",
        name: fragmentId,
        type: "XML",
      }).then((newFragment: sap.ui.core.Control) => {
        this["newFragment"] = newFragment;
        this.controller().getView().addDependent(this["newFragment"]);
        this.controller().getView().getModel("galileiModel").setProperty("/numberOfRetentionDays", 90);
        this["newFragment"].open();
        const oButtonDel = this["newFragment"].getButtons()[0] as sap.m.Button;
        const oButtonCancel = newFragment["getButtons"]()[1] as sap.m.Button;
        const oDeleteAllRadioButton = sap.ui.core.Fragment.byId("idDeleteRecordsMessageView", "deleteAllRB");
        const oDeleteLogicalRadioButton = sap.ui.core.Fragment.byId("idDeleteRecordsMessageView", "deleteAllLogicalRB");
        const oRetentionRadioButton = sap.ui.core.Fragment.byId("idDeleteRecordsMessageView", "deleteAllRetentionB");
        const oDeleteLogicalLTFRadioButton = sap.ui.core.Fragment.byId(
          "idDeleteRecordsMessageView",
          "deleteAllLogicalLTFRB"
        );
        const oDeleteRetentionLTFRadioButton = sap.ui.core.Fragment.byId(
          "idDeleteRecordsMessageView",
          "deleteAllRetentionLTFRB"
        );
        const oStepInputId = sap.ui.core.Fragment.byId("idDeleteRecordsMessageView", "stepInputId");
        const oStepInputIdLTF = sap.ui.core.Fragment.byId("idDeleteRecordsMessageView", "stepInputIdLTF");
        const oRadioButtonDefaultSpark = sap.ui.core.Fragment.byId(
          "idDeleteRecordsMessageView",
          "defaultSettingRadioGrp"
        );
        const oRadioButtonnewSparkSetting = sap.ui.core.Fragment.byId(
          "idDeleteRecordsMessageView",
          "defineNewSettingRadioGrp"
        );
        const defaultSparkSetting = this.controller()
          .getView()
          .getModel("galileiModel")
          .getProperty("/truncateDefaultSparkSetting");
        this.controller().getView().getModel("galileiModel").setProperty("/defaultSparkSetting", defaultSparkSetting);
        this.controller().getView().getModel("galileiModel").setProperty("/newSparkSetting", defaultSparkSetting);
        const oDefaultSparkInput = sap.ui.core.Fragment.byId("idDeleteRecordsMessageView", "defaultSparkInput");
        const selectNewSparkSetting = sap.ui.core.Fragment.byId("idDeleteRecordsMessageView", "selectNewSparkSetting");
        this.controller().getView().getModel("galileiModel").setProperty("/newSparkDefinition", false);
        if (this.controller()?.getView()?.getModel("galileiModel")?.getData()?.hasData === false) {
          oStepInputIdLTF["setEnabled"](true);
        }
        oButtonDel.attachPress(() => {
          const isLTF = this.controller().getView().getModel("galileiModel").getData().isLTF;
          const hasData = this.controller().getView().getModel("galileiModel").getData().hasData;
          this["deleteSelectionResponse"] = {
            isPermanentDeletion: !isLTF && oDeleteAllRadioButton?.["getSelected"]() ? true : false,
            isLogicalDeletion:
              oDeleteLogicalRadioButton?.["getSelected"]() ||
              (isLTF && oDeleteLogicalLTFRadioButton?.["getSelected"]()),
            isHouseKeepingDelete:
              oRetentionRadioButton?.["getSelected"]() ||
              (isLTF && oDeleteRetentionLTFRadioButton?.["getSelected"]()) ||
              (isLTF && !hasData),
          };
          this["newFragment"].close();
          this["newFragment"].destroy();
          return resolve(true);
        });
        oButtonCancel.attachPress(() => {
          this["newFragment"].close();
          this["newFragment"].destroy();
          return reject(false);
        });
        oDeleteAllRadioButton?.["attachSelect"](() => {
          if (oDeleteAllRadioButton["getSelected"]() === true) {
            oStepInputId["setEnabled"](false);
          }
        });
        oDeleteLogicalRadioButton?.["attachSelect"](() => {
          if (oDeleteLogicalRadioButton["getSelected"]() === true) {
            oStepInputId["setEnabled"](false);
          }
        });
        oRetentionRadioButton?.["attachSelect"](() => {
          if (oRetentionRadioButton["getSelected"]() === true) {
            oStepInputId["setEnabled"](true);
          }
        });
        oDeleteLogicalLTFRadioButton?.["attachSelect"](() => {
          if (oDeleteLogicalRadioButton["getSelected"]() === true) {
            oStepInputIdLTF["setEnabled"](false);
            oDefaultSparkInput?.["setValue"](
              this.controller().getView().getModel("galileiModel").getProperty("/truncateDefaultSparkSetting")
            );
          }
        });
        oDeleteRetentionLTFRadioButton?.["attachSelect"](() => {
          if (oDeleteRetentionLTFRadioButton["getSelected"]() === true) {
            oStepInputIdLTF["setEnabled"](true);
            oDefaultSparkInput?.["setValue"](
              this.controller().getView().getModel("galileiModel").getProperty("/vacuumDefaultSparkSetting")
            );
          }
        });

        oStepInputId?.["attachChange"](() => {
          if (oStepInputId["getValue"]() > 999) {
            oStepInputId["setValue"](999);
          } else if (oStepInputId["getValue"]() < 0) {
            oStepInputId["setValue"](0);
          }
        });
        oStepInputIdLTF?.["attachChange"](() => {
          if (oStepInputIdLTF["getValue"]() > 999) {
            oStepInputIdLTF["setValue"](999);
          } else if (oStepInputIdLTF["getValue"]() < 7) {
            oStepInputIdLTF["setValue"](7);
          }
        });
        oRadioButtonDefaultSpark?.["attachSelect"](() => {
          if (oRadioButtonDefaultSpark["getSelectedIndex"]() === 0) {
            oRadioButtonDefaultSpark["setSelectedIndex"](0);
            oRadioButtonnewSparkSetting["setSelectedIndex"](-1);
            selectNewSparkSetting["setEnabled"](false);
            this.controller().getView().getModel("galileiModel").setProperty("/newSparkDefinition", false);
          }
        });
        oRadioButtonnewSparkSetting?.["attachSelect"](() => {
          if (oRadioButtonnewSparkSetting["getSelectedIndex"]() === 0) {
            oRadioButtonnewSparkSetting["setSelectedIndex"](0);
            oRadioButtonDefaultSpark["setSelectedIndex"](-1);
            selectNewSparkSetting["setEnabled"](true);
            this.controller().getView().getModel("galileiModel").setProperty("/newSparkDefinition", true);
          }
        });
      });
    });
  }

  public openRefreshConfirmDialog(newElementsList): Promise<any> {
    return new Promise<any>(async (resolve, reject) => {
      let dialog = sap.ui.getCore().byId("refreshConfirmDialog") as sap.m.Dialog;
      if (!dialog) {
        const refreshDlg = require("./view/RefreshConfirmDialog.fragment.xml");
        dialog = sap.ui.xmlfragment(refreshDlg, this.controller()) as sap.m.Dialog;
      }
      this.controller().getView().addDependent(dialog);
      const table = this.getTableEntity();
      const galileiModel = new sap.galilei.ui5.GalileiModel(table);
      galileiModel.setProperty("/newElementsList", newElementsList);
      const hasNewElements = newElementsList?.length > 0;
      galileiModel.setProperty("/isNewDialog", hasNewElements);
      dialog.setModel(galileiModel, "oModel");
      dialog.setModel(this.tableResourceModel, "i18n");
      const replicationModel = this.getEditorControl()?.getModel("replicationModel");
      const dataAccessMode = this.getDataAccess(replicationModel.getData().dataAccess);
      replicationModel.setProperty("/dataAccessMode", dataAccessMode);
      dialog.setModel(replicationModel, "replicationModel");
      const keyCols = newElementsList?.filter((el) => el.isKey);
      const endButton = new sap.m.Button({
        text: this.tableResourceModel.getResourceBundle().getText("canceltext"),
        press: () => {
          dialog.close();
          dialog.destroy();
          const selectedNewObjects = [];
          resolve({ selectedNewObjects: selectedNewObjects, isAbort: true });
        },
      });
      const beginButton = new sap.m.Button({
        text: this.tableResourceModel.getResourceBundle().getText("confirmRefreshButton"),
        id: "refreshConfirmButtonId",
        press: () => {
          const newObjectsTable = sap.ui.getCore().byId("newObjectsTable") as sap.m.Table;
          let selectedItems = newObjectsTable.getSelectedItems();
          selectedItems = selectedItems.map((item) => item.getBindingContext("oModel").getObject());
          const selectedItemsNames = selectedItems?.map((o) => o["name"]);
          let isAllKeyColsSelected = true;
          for (let i = 0; i < keyCols?.length; i++) {
            const colName = keyCols[i]?.name;
            if (!selectedItemsNames?.includes(colName)) {
              isAllKeyColsSelected = false;
              break;
            }
          }
          if (isAllKeyColsSelected) {
            const selectedNewObjects = selectedItems || [];
            const resp = { selectedNewObjects: selectedNewObjects, isAbort: false };
            dialog.close();
            dialog.destroy();
            resolve(resp);
          } else {
            let errDialog = sap.ui.getCore().byId("errorConfirmDialog") as sap.m.Dialog;
            if (!errDialog) {
              const errorDlg = require("./view/ErrorDialog.fragment.xml");
              errDialog = sap.ui.xmlfragment(errorDlg, this.controller()) as sap.m.Dialog;
            }
            this.controller().getView().addDependent(errDialog);
            errDialog.setModel(this.tableResourceModel, "i18n");
            const closeBtn = new sap.m.Button({
              text: this.tableResourceModel.getResourceBundle().getText("txt_close"),
              press: () => {
                errDialog.close();
                errDialog.destroy();
              },
            });
            errDialog.addButton(closeBtn);
            errDialog.open();
          }
        },
        type: sap.m.ButtonType.Emphasized,
      });
      dialog.addButton(beginButton);
      dialog.addButton(endButton);

      dialog.setEscapeHandler(() => {
        dialog.destroy();
        resolve({ selectedNewObjects: [], isAbort: true });
      });
      dialog.open();
    });
  }

  private get resourceId() {
    return "UniqueTableEditorResource";
  }

  private async updateTableEntity(csn: ICsn, file?: RepositoryUtils.IObjectFileInfo, fnCallback?) {
    let tableKey = file && file.name !== undefined ? file.name : getEntityNameFromCSN(csn.definitions);
    if (tableKey) {
      const table = this.getTableEntity();
      table.name = tableKey;
      if (csn?.definitions && this["deltaActiveViewName"]) {
        tableKey = Object.keys(csn.definitions)?.[0];
        table.deltaTableName = tableKey;
        //    table.label = this.deltaActiveViewName
        table.name = this["deltaActiveViewName"] || tableKey;
        this["deltaActiveViewName"] = "";
      }
      const erCsnToModel = sap.cdw.ermodeler.ErCsnToModel.getInstance(true);
      erCsnToModel.updateObjectFromCsn(table, csn.definitions[tableKey], undefined, undefined, fnCallback);
      if (isORDTable(table)) {
        // Update ord info on local table
        erCsnToModel.updateOrdFromCsnFile(table, file);
      }
      RepositoryUtils.updateObjectFileInfo(table, file);
      if (isTableRestoreEnabled() && file?.isVersioningRestorePage) {
        table.repositoryCSN = file?.currentDetails?.csn;
      } else {
        table.repositoryCSN = csn;
      }
      const workbench = this.getWorkbench();
      const spaceName = workbench.spaceName;

      // Support context/space if the table is a cross space table
      const { entityName, spaceContextName } = getEntityAndContextNameFromCSN(csn.definitions);
      if (spaceContextName) {
        erCsnToModel.createContextAndAttachToObject(
          this.model,
          table,
          spaceContextName,
          csn.definitions[spaceContextName],
          /* bForce*/ true
        );
      }

      if (!spaceContextName && !table.isNew && !table.isRemote && !table.isLocalSchema) {
        // Update table has data information
        // do not run this for remote tables, as there data management should be done with remote table monitor
        table.hasData = await checkIfTableHasData(spaceName, table.name);
      }

      //check if Restore possible for the thcurrent version
      if (isTableRestoreEnabled() && table?.isLocal && file?.isVersioningRestorePage) {
        await this.preCheckIfRestorePossible(this.model, file?.versionDetails, file?.currentDetails);
      }

      if (isTableRestoreEnabled() && file?.isVersioningRestorePage) {
        sap.cdw.tableEditor.ModelImpl.updateLocalTableVersionChanges(
          table,
          file?.currentDetails?.csn?.definitions[tableKey]?.elements,
          file?.versionDetails?.csn?.definitions[tableKey]?.elements,
          this.model
        );
      }
      // Support contexts/spaces for cross space entities used as target of unresolved associations
      const contextNames = getObjectNamesFromCSN(csn.definitions, "context");
      for (const contextName of contextNames) {
        if (contextName === spaceContextName) {
          continue;
        }
        erCsnToModel.importContextFromCsn(this.model, contextName, csn.definitions[contextName]);
      }
    }
  }

  private createTableModel(loadingData?: { csn: ICsn; file: any; objectModel: any }) {
    const resourceId = this.resourceId;
    let name = loadingData && loadingData.file && loadingData.file.name;
    if (!name && loadingData && loadingData.objectModel) {
      name = loadingData.objectModel.technicalName;
    }
    if (!this.isObjectPageMode()) {
      // case of main editor
      if (this.model) {
        this.model.resource.clear();
        this.model = undefined;
      }
      const resource = new sap.galilei.model.Resource(resourceId);
      if (loadingData) {
        resource.isLoading = true;
      }
      this.model = new sap.cdw.tableEditor.Model(resource, {
        name: name || "",
        isNew: !name,
      });
      resource.model = this.model;
      this.table = sap.cdw.ermodeler.ModelImpl.createObject(
        sap.cdw.commonmodel.ModelImpl.OBJECT_TABLE,
        {
          name: name || "",
          displayName: name || "",
        },
        this.model
      );
    } else {
      // case of objectPage (sidepanel) editor
      this.table = loadingData.objectModel;
      this.model = loadingData.objectModel.resource.model;
    }
    this.getWorkbench().getToolbarModel().setData(this.getTableModel());
  }

  private async loadAndUpdateSimpleTypes(model: any, bClearUndo = true) {
    return new Promise((resolve, reject) => {
      sap.cdw.commonmodel.ModelImpl.getSimpeTypesAndContexts(model)
        .then(() => {
          sap.cdw.commonmodel.ObjectImpl.attachObjectsToBaseTypes(model);
          if (bClearUndo) {
            this.clearUndoRedo();
          }
          resolve({});
        })
        .catch(() => {
          reject();
        });
    });
  }

  private getCSNOptions(table: any, options: any = {}) {
    return {
      ...options,
      rootObject: table,
      creator: "Table Editor",
      documentKind: "sap.dwc.entity",
      serializeModel: false,
    };
  }

  private clearUndoRedo() {
    if (this.model) {
      this.model.resource.clearListOfActions();
      this.model.resource.clearUndoStack();
      this.model.resource.isLoading = false;
    }
  }

  public onPostInitSaveDialog(saveDialog: sap.m.Dialog): void {
    const table = this.table as any;
    const isDeltaTable = table?.isDeltaTable;
    if (isDeltaTable) {
      const deltaTableName = table?.deltaTableName;
      const workbench = this.getWorkbench();
      const deltaTableCaptureLabel = workbench.getText("deltatablecapture");
      saveDialog["setAdditionalFieldLabel"](deltaTableCaptureLabel);
      saveDialog["setAdditionalFieldValue"](deltaTableName);
      saveDialog["setAdditionalFieldVisibility"](true);
      saveDialog["setAdditionalFieldEnablement"](false);
    }
  }

  /**
   * - First try to deploy
   * - Show truncate dialog if deployment fails to enable change management
   * in backend step by step
   *
   * @param {ISaveOptions} [options={}]
   */
  private async deployTable(options: ISaveOptions = {}): Promise<any> {
    const self = this;
    const workbench = this.getWorkbench();
    options = { ...options };
    options.deploy = true;
    options.displayValidationErrors = false;

    const aPopOver = sap.ui.getCore().byId("errorPopover");
    if (aPopOver) {
      aPopOver.close();
    }
    // eslint-disable-next-line @typescript-eslint/no-misused-promises
    return new Promise<void>(async function (resolve, reject) {
      workbench
        .doSave(options)
        .then(() => {
          resolve();
        })
        .catch(async (err) => {
          const { space, model } = workbench.getSpaceAndModel();
          let hasData = false;
          await self
            .checkIfTableHasData(space, model)
            .then((response) => {
              hasData = response;
            })
            .catch(() => {
              hasData = false;
            });
          if (hasData) {
            workbench.setBusy(false);
            self.displayTruncateTableDataDialog(async () => {
              try {
                workbench.setBusy(true, workbench.getText("deploying"));
                options.displayValidationErrors = true; // show validation errors
                await workbench.doSave(options);
                resolve();
              } catch (err) {
                reject();
              }
            });
          } else {
            workbench.displayErrors(/* deploy*/ true, err);
            reject();
          }
        });
    });
  }

  private truncateTableData(spaceId: string, tableName: string) {
    return ServiceCall.request<any>({
      url: `data/${spaceId}/tables/${tableName}`,
      type: HttpMethod.DELETE,
      contentType: ContentType.APPLICATION_JSON,
    }).then((p) => p.data);
  }

  private checkIfTableHasData(spaceId: string, tableName: string): Promise<boolean> {
    return ServiceCall.request<any>({
      url: `data/${spaceId}/tables/${tableName}/hasdata`,
      type: HttpMethod.GET,
      contentType: ContentType.APPLICATION_JSON,
    }).then((p) => p.data.hasData);
  }

  private async displayTruncateTableDataDialog(callback: () => Promise<void>) {
    const workbench = this.getWorkbench();
    const { space, model } = workbench.getSpaceAndModel();
    const dialog = new sap.m.Dialog({
      title: workbench.getText("truncateDialogTitle"),
      type: "Message",
      content: new sap.m.Text({
        text: workbench.getText("truncateDialogContent"),
      }),
      beginButton: new sap.m.Button({
        text: workbench.getText("truncateButton"),
        press: async () => {
          dialog.close();

          workbench.setBusy(true, workbench.getText("truncateInProgress"));
          await this.truncateTableData(space, model);
          workbench.setBusy(false);

          callback();
        },
      }),
      endButton: new sap.m.Button({
        text: workbench.getText("cancel"),
        press: () => dialog.close(),
      }),
      afterClose: () => dialog.destroy(),
    });

    dialog.open();
  }

  public async generateSemantics(configuration) {
    const table = this.getTableEntity();
    const self = this;
    const config = {
      enrichSemanticUsage: configuration.selectSemanticUsage.selected,
      enrichAttributes: configuration.identifyColumns.selected,
      enrichKeys: configuration.identifyColumns.selected, // It should share the same config as enrichAttributes
      overwriteExistingAttributes: !configuration.notOverride.selected,
    };
    await GenAIService.getInstance().enrichSemantics(
      self.editorController?.spaceName,
      {
        csnDocument: table.repositoryCSN,
        config: config,
        entitiesToEnrich: [table.name],
        useFake: false,
      },
      self.openProgressDialog.bind(self)
    );
  }

  public getAIModel() {
    return this.model;
  }

  public showDeploymentErrorDialog(typeOfError) {
    const workbench = this.getWorkbench();
    const self = this;
    if (typeOfError === 0) {
      sap.m.MessageBox.show(this.tableResourceModel.getProperty("deployErrorForPartitionFiltersNotSupported"), {
        icon: sap.m.MessageBox.Icon.ERROR,
        id: "deployErrorMessageBox",
        title: this.tableResourceModel.getProperty("titError"),
        actions: [sap.m.MessageBox.Action.CLOSE],
        initialFocus: sap.m.MessageBox.Action.CLOSE,
        onClose: null,
      });
    } else if (typeOfError === 1) {
      sap.m.MessageBox.show(this.tableResourceModel.getProperty("deployErrorForFiltersNotSupported"), {
        icon: sap.m.MessageBox.Icon.ERROR,
        id: "deployErrorMessageBox",
        title: this.tableResourceModel.getProperty("titError"),
        actions: [sap.m.MessageBox.Action.CLOSE],
        initialFocus: sap.m.MessageBox.Action.CLOSE,
        onClose: null,
      });
    } else {
      sap.m.MessageBox.show(this.tableResourceModel.getProperty("deployErrorForPartitionNotSupported"), {
        icon: sap.m.MessageBox.Icon.ERROR,
        id: "deployErrorMessageBox",
        title: this.tableResourceModel.getProperty("titError"),
        actions: [sap.m.MessageBox.Action.CLOSE],
        initialFocus: sap.m.MessageBox.Action.CLOSE,
        onClose: null,
      });
    }
  }
  public isSDIbasedConnection(location) {
    return ["agent", "dpserver"].includes(location);
  }

  public showRestoreNotSupportedLtf() {
    sap.m.MessageBox.show(this.tableResourceModel.getProperty("restoreErrorLtf"), {
      icon: sap.m.MessageBox.Icon.ERROR,
      id: "restoreNotSupportedLtf",
      title: this.tableResourceModel.getProperty("Error"),
      actions: [sap.m.MessageBox.Action.CLOSE],
      initialFocus: sap.m.MessageBox.Action.CLOSE,
      onClose: null,
    });
  }

  public showRestoreNotSupportedHana() {
    sap.m.MessageBox.show(this.tableResourceModel.getProperty("restoreErrorHana"), {
      icon: sap.m.MessageBox.Icon.ERROR,
      id: "restoreNotSupportedLtf",
      title: this.tableResourceModel.getProperty("Error"),
      actions: [sap.m.MessageBox.Action.CLOSE],
      initialFocus: sap.m.MessageBox.Action.CLOSE,
      onClose: null,
    });
  }

  public async preCheckIfRestorePossible(model, versionDetails, currentDetails) {
    if (model?.table?.isLocal === true && model?.table?.hasData === true) {
      let versionPartition;
      let currentPartition;
      if (model?.table?.isDeltaTable) {
        versionPartition =
          versionDetails?.csn?.definitions[this.modelId + "_Delta"][CsnAnnotations.DataWarehouse.partition];
        currentPartition =
          currentDetails?.csn?.definitions[this.modelId + "_Delta"][CsnAnnotations.DataWarehouse.partition];
      } else {
        versionPartition = versionDetails?.csn?.definitions[this.modelId][CsnAnnotations.DataWarehouse.partition];
        currentPartition = currentDetails?.csn?.definitions[this.modelId][CsnAnnotations.DataWarehouse.partition];
      }

      if (!(versionPartition === undefined && currentPartition === undefined)) {
        if (versionPartition !== undefined && currentPartition !== undefined) {
          let isdifferentPartitionSetting = false;
          if (
            versionPartition?.elements[0]["="] === currentPartition?.elements[0]["="] &&
            versionPartition?.ranges?.length === currentPartition?.ranges?.length
          ) {
            versionPartition?.ranges?.forEach((range, index) => {
              if (
                range?.low !== currentPartition?.ranges[index]?.low ||
                range?.high !== currentPartition?.ranges[index]?.high
              ) {
                isdifferentPartitionSetting = true;
              }
            });
          }
          if (
            versionPartition?.elements[0]["="] !== currentPartition?.elements[0]["="] ||
            versionPartition?.ranges?.length !== currentPartition?.ranges?.length ||
            isdifferentPartitionSetting
          ) {
            this.showRestoreNotSupportedHana();
            openVersionPageIncurrentTab(this.modelId, this.spaceId, this.getWorkbench().versionId);
          }
        } else {
          this.showRestoreNotSupportedHana();
          openVersionPageIncurrentTab(this.modelId, this.spaceId, this.getWorkbench().versionId);
        }
      }
    }
  }
}

export const TableEditorComponent = smartExtendComponent(
  AbstractDataBuilderEditorComponent,
  "sap.cdw.components.tableeditor.Component",
  TableEditorComponentClass as any
);

sap.ui.define("sap/cdw/components/tableeditor/Component", [], function () {
  require("../commonui/utility/GalileiLoader");
  require("../commonmodel/utility/CommonModelLoader");
  require("../commonmodel/csn/csnToModel");
  require("../ermodeler/js/model/er.modelImpl.ts");
  require("../ermodeler/js/model/er.objectImpl");
  require("../ermodeler/js/model/er.validation");
  require("../ermodeler/js/model/er.model");

  require("../ermodeler/js/transform/erModelToCsn");
  require("../ermodeler/js/transform/erCsnToModel");
  require("./js/table.model");
  require("./js/table.modelImpl.ts");

  require("../csnquerybuilder/js/model/modelImpl");
  require("../csnquerybuilder/js/model/nodeImpl.ts");
  require("../csnquerybuilder/js/model/validation");
  require("../csnquerybuilder/js/model/model");

  return TableEditorComponent;
});
