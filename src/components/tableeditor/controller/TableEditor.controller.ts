/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

/* eslint-disable id-blacklist */
import { ShellNavigationService } from "@sap/orca-shell";
import { cloneDeep } from "lodash";
import { AdaptionLocationType, ReplicationStatus } from "../../../../shared/remoteTables/types";
import { PartitionType } from "../../../../shared/table/types";
import { IWorkbenchController } from "../../abstractbuilder/api";
import {
  isBWPCEPushEnabled,
  isDeltaReadAPIEnabled,
  isGenAISemanticEnabled,
  isHashPartitioningEnabled,
  isModelingAnnotatePartitionsEnabled,
  isSparkSelectionVacuumEnabled,
} from "../../abstractbuilder/commonservices/FeatureFlagCheck";
import {
  getCurrentOrCrossSpaceObjectByName,
  getObjectFilesProperties,
  updateObjectFileInfo,
} from "../../abstractbuilder/utility/RepositoryUtils";
import { smartExtend } from "../../basecomponent/controller/BaseController.controller";
import { SupportedFeaturesService } from "../../commonmodel/api/SupportedFeaturesService";
import { DataWarehouse } from "../../commonmodel/csn/csnAnnotations";
import { findInCollectionByName, findInCollectionByNewNameOrName } from "../../commonmodel/csn/csnUtils";
import { CommonQualifiedClassNames } from "../../commonmodel/model/const/model.const";
import {
  AggregationTypes,
  CDSDataType,
  DataCategory,
  DimensionType,
  SemanticElementType,
  SemanticType,
  getElementTypeForMeasure,
} from "../../commonmodel/model/types/cds.types";
import { StorageType } from "../../commonmodel/utility/CommonUtils";
import { HierarchyValidation, isParentchildHierarchy } from "../../commonmodel/utility/HierarchyUtils";
import { addHierarchyAssociationEnableFormatter } from "../../commonmodel/utility/HierarchyWithDirectoryUtils";
import { changeLabelColumn } from "../../commonui/utility/AssociationUtils";
import { decInputVisibility, intInputVisibility, stringInputVisibility } from "../../commonui/utility/CommonUtils";
import {
  SupportedFilterDataTypes,
  SupportedPartitionDataTypes,
  SupportedPartitionDataTypesLTF,
  getFilterDataTypes,
  getItemType,
} from "../../commonui/utility/Formatters";
import { NameUsage } from "../../commonui/utility/NameInputValidator";
import { Editor, PrivilegeTypes } from "../../databuilder/utility/Constants";
import { checkDIPrivilegeByType, getLinkText } from "../../databuilder/utility/DatabuilderHelper";
import { getDSTypeModel, getDimensionTypeData } from "../../ermodeler/js/statics/const/er.model";
import * as commonUtils from "../../ermodeler/js/utility/CommonUtils";
import { validateDecimalInput } from "../../ermodeler/js/utility/CommonUtils";
import { getMappings } from "../../ermodeler/js/utility/DependencyUtil";
import { ReplicationStatusUtil } from "../../ermodeler/js/utility/ReplicationStatusUtil";
import * as commonFormatter from "../../ermodeler/js/utility/commonFormatter";
import {
  dateFormatter,
  getReplicationOptionsVisibility,
  getScheduleMenuVisibility,
  hanaStateFormatter,
  objectStatusIconFormatter,
  objectStatusTextFormatter,
  objectStatusTooltipFormatter,
  objectStatusVisibleFormatter,
  openDataTypePopoverHelper,
  remoteTableNameFormatter,
  revertObjectStatusIconFormatter,
  revertObjectStatusTextFormatter,
  revertObjectStatusVisible,
  selectWithEmptyEntrySetter,
  semanticTypeEmpty,
  sourceObjectLabel,
  unitColumnEnabled,
  unitColumnValueState,
  unitColumnValueStateText,
  updateTagToken,
} from "../../ermodeler/js/utility/sharedFunctions";
import { CommonProperties, Pages } from "../../ermodeler/properties/CommonProperties.controller";
import { CreateStatistics, DeleteStatistics } from "../../monitorUtil/util/StatisticsUtil";
import { notificationsEventChannel, notificationsFetched } from "../../notifications/utility/Types";
import { RemoteTableServiceUtil } from "../../remotetablemonitor/utility/RemoteTableServiceUtil";
import { EditorCapabilities, ImportRemoteService } from "../../reuse/importremotetable/api/ImportRemoteService";
import { revertElementAIChange } from "../../reuse/utility/DeltaUtil";
import { ExtensionPoint } from "../../reuse/utility/ExtensionPoint";
import { Format } from "../../reuse/utility/Format";
import { MessageHandler } from "../../reuse/utility/MessageHandler";
import { NamingHelper } from "../../reuse/utility/NamingHelper";
import { getBusinessName } from "../../reuse/utility/RepoHelper";
import { ContentType, DataType, HttpMethod, ServiceCall } from "../../reuse/utility/ServiceCall";
import { ObjectStatus } from "../../reuse/utility/Types";
import { Logger } from "../../reuse/utility/UIHelper";
import { ShellContainer } from "../../shell/utility/Container";
import { Crud } from "../../shell/utility/Crud";
import { Repo } from "../../shell/utility/Repo";
import { DWCFeature, EventType } from "../../shell/utility/ShellUsageCollectionService";
import { UISpaceCapabilities } from "../../shell/utility/UISpaceCapabilities";
import { User } from "../../shell/utility/User";
import { fetchApplicationId } from "../../taskchainmodeler/js/utils";
import { Activity, ApplicationId, dataAccessType } from "../../tasklog/utility/Constants";
import { ITTaskScheduleController, ITaskScheduleRequest } from "../../taskscheduler/controller/TaskSchedule.controller";
import { getTaskScheduer, recordAction } from "../../taskscheduler/utility/ScheduleUtil";
import { ObjectNameDisplay } from "../../userSettings/utility/Constants";
import { ContentOwner, fetchDefaultSparkSettingForDelete } from "../util/tableEditorUtil";

export class TableEditorClass extends CommonProperties {
  public formatters = {
    ...commonFormatter,
    semanticTypeEmpty,
    unitColumnEnabled,
    unitColumnValueState,
    unitColumnValueStateText,
    getScheduleMenuVisibility,
    getReplicationOptionsVisibility,
    columnsAndAttributesNoDataText(
      dataCategory: DataCategory,
      search: string,
      noDataForFilter: string,
      noAttributesEditMode: string,
      noColumnsEditMode: string
    ) {
      if (search) {
        return noDataForFilter;
      }

      switch (dataCategory) {
        case DataCategory.FACT:
        case DataCategory.SQLFACT:
        case DataCategory.DIMENSION:
          return noAttributesEditMode;
        case DataCategory.DATASET:
        default:
          return noColumnsEditMode;
      }
    },
    objectStatusIconFormatter,
    objectStatusIconColorFormatter: Format.objectStatusIconColorFormatter.bind(this),
    objectStatusTextFormatter,
    objectStatusTooltipFormatter,
    objectStatusVisibleFormatter,
    dateFormatter,
    revertObjectStatusIconFormatter,
    revertObjectStatusTextFormatter,
    revertObjectStatusVisible,
    remoteTableNameFormatter,
    sourceObjectLabel,
    hanaStateFormatter,
    formatKeyValue: (isSelected) => isSelected,
  };
  protected dataTypePopoverContent: sap.ui.core.XMLComposite;
  protected dataTypePopover: sap.m.Popover;
  private tableResourceModel: sap.ui.model.resource.ResourceModel;
  private dependentObjectsLoaded: boolean;
  private updatedElements: any;
  private extensionPoint: ExtensionPoint;
  private serviceUtil: any;
  private remoteTableFormatter: any;
  private partitioningData: any;
  private newRemoteColumnDialog: sap.m.Dialog;
  private filterableColumns: any;
  public container: IWorkbenchController;
  public defaultValueErrorParams: any;
  private fiscalTimeSettingsDialog: sap.m.Dialog;
  formatter: any; // added for error pop over
  restoreDeployment: any;
  isReusableTaskScheduleFFEnabled: boolean;
  isAdoptionOfTaskSchedulerEnabled: boolean;
  messageBoxInstance: boolean;

  public onInit() {
    this.onDefaultInit();
    require("../css/style.css");
    const bundleName = require("../i18n/i18n.properties");
    this.tableResourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: bundleName,
    });
    this.isReusableTaskScheduleFFEnabled = sap.ui
      .getCore()
      .getModel("featureflags")
      .getProperty("/DWCO_REUSABLE_TASK_SCHEDULING_UI");
    this.isAdoptionOfTaskSchedulerEnabled = sap.ui
      .getCore()
      .getModel("featureflags")
      .getProperty("/DWCO_REUSABLE_TASK_SCHEDULER_ADOPTION");
    if (this.isAdoptionOfTaskSchedulerEnabled) {
      const fragmentName = require("../../taskscheduler/view/TaskScheduleAuth.view.xml");
    }

    this.getView().setModel(this.tableResourceModel, "i18n_table");
    const i18nTaskModel = new sap.ui.model.resource.ResourceModel({
      bundleName: require("../../taskscheduler/i18n/i18n.properties"),
    });
    this.getView().setModel(i18nTaskModel, "i18n_task");
    const replicationModel = new sap.ui.model.json.JSONModel({
      hasTableReplicationPrivilege: false,
      hasSchedulingReplicationPrivilege: false,
    });
    this.getView().setModel(replicationModel, "replicationModel");
    // Fetch notification for updating deployment status
    sap.ui
      .getCore()
      .getEventBus()
      .subscribe(notificationsEventChannel, notificationsFetched, this.updateDeploymentStatus.bind(this), this);
    sap.ui.getCore().getEventBus().subscribe("modelChanged", "elementChanged", this.onRefreshModel.bind(this));
    const panel = this.getView().byId("filterPanel") as any;
    if (panel) {
      const includeOp = panel.getIncludeOperations().filter((item) => item === "EQ" || item === "BT");
      getFilterDataTypes().forEach((type) => {
        panel.setIncludeOperations(includeOp, type);
      });
    }
    this["erdResourceBundle"] = this.oResourceModel.getResourceBundle();
  }

  public onAfterRendering() {
    this.addTagsMultiInputValidator();
    this.dataTypePopoverContent = undefined;
    this.dataTypePopover = undefined;

    const dateControl = this.byId("dateDefaultVal");
    dateControl.addEventDelegate({
      onAfterRendering: function (oEvent) {
        $("#" + oEvent.srcControl.getId() + "-inner").prop("readonly", true);
      },
    });
    const timeControl = this.byId("timeDefaultVal");
    timeControl.addEventDelegate({
      onAfterRendering: function (oEvent) {
        $("#" + oEvent.srcControl.getId() + "-inner").prop("readonly", true);
      },
    });
    const datetimeControl = this.byId("timestampDefaultVal");
    datetimeControl.addEventDelegate({
      onAfterRendering: function (oEvent) {
        $("#" + oEvent.srcControl.getId() + "-inner").prop("readonly", true);
      },
    });
    this.getSpaceType();
  }

  private getSpaceType() {
    Repo.getSpaceDetails(this.spaceName, ["spaceType", "status"]).then((spaceObj) => {
      const isSpaceUnlocked = spaceObj.status !== "locked";
      (this.getView().getModel("replicationModel") as sap.ui.model.json.JSONModel).setProperty(
        "/isSpaceUnlocked",
        isSpaceUnlocked
      );
      (this.getView().getModel("replicationModel") as sap.ui.model.json.JSONModel).setProperty(
        "/spaceType",
        spaceObj.spaceType
      );
    });
  }

  private getSourceObjectName(featureFlag, galileiModel) {
    if (galileiModel.isLocalSchema) {
      if (featureFlag) {
        return `${galileiModel.localSchema.schema}.${galileiModel.localSchema.table}`;
      }
      return `${galileiModel.localSchema.schema}`;
    }
    return "";
  }

  private getTableIcon(galileiModel) {
    if (galileiModel.isRemote && galileiModel.isTable) {
      return "sap-icon://sac/live-table";
    }
    return "sap-icon://sac/table";
  }

  public displayPackageSelector(packages) {
    // packages list has at least "none" element, so when the length equals one that means no other package can be selected
    // at this scenario we should hide this package selector
    if (packages?.length > 1) {
      return true;
    } else {
      return false;
    }
  }

  public packageSelectionChange(event) {
    const object = this.getGalileiModel() && this.getGalileiModel().getData();
    object.validate();
  }

  private sharedReplicaMessageText(hasReplica, hasSourceSharing, hasSourceSharingWithReplica) {
    return this.remoteTableFormatter?.sharedReplicaMessageText(
      hasReplica,
      hasSourceSharing,
      hasSourceSharingWithReplica
    );
  }

  private sharedReplicaMessageType(hasReplica, hasSourceSharing, hasSourceSharingWithReplica) {
    return this.remoteTableFormatter?.sharedReplicaMessageType(
      hasReplica,
      hasSourceSharing,
      hasSourceSharingWithReplica
    );
  }

  private handleSharedTableDetailsPress() {
    this.serviceUtil?.handleSharedTableDetailsPress(this.modelName, this.spaceName, this.getView());
  }

  private sharedReplicaMenuEnablementFormatter(
    enableCtrlInput,
    hasReplica,
    hasSourceSharing,
    hasSourceSharingWithReplica,
    featureflags
  ) {
    const sourceSharingObj = {
      hasReplica,
      hasSourceSharing,
      hasSourceSharingWithReplica,
    };
    const menuCheckValue = this.remoteTableFormatter?.sharedReplicaMenuCheck(sourceSharingObj);
    return menuCheckValue ? enableCtrlInput : menuCheckValue;
  }

  private loadSnapshotMenuEnablementFormatter(
    enableCtrlInput,
    hasReplica,
    hasSourceSharing,
    hasSourceSharingWithReplica,
    location,
    dataAccess,
    featureflags
  ) {
    return (
      this.sharedReplicaMenuEnablementFormatter(
        enableCtrlInput,
        hasReplica,
        hasSourceSharing,
        hasSourceSharingWithReplica,
        featureflags
      ) && !(location === AdaptionLocationType.DataIntelligence && dataAccess === dataAccessType.REALTIME_REPLICATION)
    );
  }

  private async setupRemoteTableReplicationModel() {
    checkDIPrivilegeByType(this.spaceName, PrivilegeTypes.EXECUTE).then((hasSchedulingReplicationPrivilege) => {
      (this.getView().getModel("replicationModel") as sap.ui.model.json.JSONModel).setProperty(
        "/hasSchedulingReplicationPrivilege",
        hasSchedulingReplicationPrivilege
      );
    });
    checkDIPrivilegeByType(this.spaceName, PrivilegeTypes.UPDATE).then((hasTableReplicationPrivilege) => {
      (this.getView().getModel("replicationModel") as sap.ui.model.json.JSONModel).setProperty(
        "/hasTableReplicationPrivilege",
        hasTableReplicationPrivilege
      );
    });
    this.getSpaceType();
  }

  public setListPageVisible(isVisible) {
    if (isVisible) {
      super.switchPage(Pages.ListPage);
    }
  }

  /**
   * Localize given text id & parameters
   * @param text property id
   * @param parameters property parameters
   */
  public localizeText(text: string, parameters: any[] = []): string {
    return this.tableResourceModel.getResourceBundle().getText(text, parameters);
  }

  public onRefreshModel(channel, event, data) {
    const oJoinPropertiesView = (this.getView() as sap.ui.core.mvc.XMLView).byId(
      "joinPropertiesDialogView"
    ) as sap.ui.core.mvc.XMLView;
    if ((oJoinPropertiesView?.getModel() as sap.ui.model.json.JSONModel)?.getData()?.tableA?.entity) {
      (oJoinPropertiesView.getModel() as sap.ui.model.json.JSONModel).getData().tableA.orderedElements = (
        oJoinPropertiesView?.getModel() as sap.ui.model.json.JSONModel
      )
        .getData()
        ?.tableA?.entity.orderedElements.filter((el) => el.isCDCColumn !== true);
      (oJoinPropertiesView.getModel() as sap.ui.model.json.JSONModel).updateBindings(true);
    }
    const oEntity = (this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel)?.getData();
    this.handleAssociations(oEntity);
    this.handleFiltersOnModelchange(oEntity, this.restoreDeployment);
  }

  public handleAssociations(oEntity) {
    if (oEntity) {
      const associationList = oEntity?.unresolvedAssociations;
      const orderedElements = oEntity?.orderedElements;
      if (associationList && associationList.length > 0) {
        let isAssociationImpacted = false;
        associationList.forEach((oAssociation) => {
          let isMappingDeleted = false;
          if (oAssociation.mappings) {
            const indicesToRemove = [];
            oAssociation.mappings.forEach((map, mapIndex) => {
              if (map.ref && map.ref.length === 1) {
                const found = orderedElements.filter((o) => o.name === map.ref[0]);
                if (found?.length === 0) {
                  if (mapIndex - 1 > 0) {
                    indicesToRemove.push({ from: mapIndex - 1, to: 4 });
                  } else {
                    indicesToRemove.push({ from: mapIndex, to: 3 });
                  }
                }
              }
            });
            for (let i = indicesToRemove.length - 1; i >= 0; i--) {
              oAssociation.mappings?.splice(indicesToRemove[i].from, indicesToRemove[i].to);
              isMappingDeleted = true;
              isAssociationImpacted = true;
            }
          }
          // Delete association when there are no mappings to have consistent behavior with query builder
          if (isMappingDeleted) {
            isAssociationImpacted = true;
            oEntity.changeManagementInfo?.associationImpactedObjects?.push({ name: oAssociation.name });
            if (oAssociation.mappings?.length === 0) {
              let associationToDel;
              oEntity.unresolvedAssociations.forEach(function (o, index) {
                if (o.name === oAssociation.name) {
                  associationToDel = index;
                }
              });
              oEntity.resource.model?.unresolvedAssociations?.splice(associationToDel, 1);
            }
          }
        });
        if (isAssociationImpacted) {
          const model: sap.galilei.ui5.GalileiModel = this?.getView()?.getModel("galileiModel") as any;
          model?.updateBindings(true);
        }
      }
    }
  }

  public getStatisticsMenuVisibility(federationOnly, dataAccess) {
    if (federationOnly !== false) {
      return true;
    }
    if (federationOnly && dataAccess === "REMOTE") {
      return true;
    }
    return false;
  }

  public createStatisticsMenuEnabledFormatter(adapter) {
    const unsupportedAdapter = ["ABAPAdapter", "CloudDataIntegrationAdapter", "CDI::CDI"];
    if (unsupportedAdapter.includes(adapter)) {
      return false;
    }
    return true;
  }

  // Partitions Section Code
  public partitionsSectionVisibleFormatter(isLocal) {
    return isLocal;
  }

  public keyTextFormatter(isKey) {
    if (isKey) {
      return this.tableResourceModel.getResourceBundle().getText("key");
    }
    return "";
  }

  public partitionsTitleFormatter(partitions, isLTF) {
    const erdResourceBundle = this.oResourceModel.getResourceBundle();
    if (isLTF) {
      return erdResourceBundle.getText("partition_headerLtf", [partitions?.length || 0]);
    }
    let length = 0;
    if (partitions.length > 0) {
      const type = partitions[0]?.partitionType;
      if (type === PartitionType.RANGE) {
        length = partitions?.[0]?.ranges?.length;
      } else if (type === PartitionType.HASH) {
        length = partitions?.[0]?.noOfHashPartitions;
      }
    }
    return erdResourceBundle.getText("partition_header", [length]);
  }

  public parametersTitleFormatter(parameters) {
    const erdResourceBundle = this.oResourceModel.getResourceBundle();
    return erdResourceBundle.getText("@InputParameterTitle", [parameters?.length || 0]);
  }

  public showWarningNoKeyDefined(): Promise<boolean> {
    return new Promise<boolean>((resolve, reject) => {
      const continueText = this.getI18nTableText("txt_continueAnyway");
      sap.m.MessageBox.warning(this.getI18nTableText("warn_noKeys_defined"), {
        id: "warnNoKeyDefined",
        icon: sap.m.MessageBox.Icon.WARNING,
        //title: this.localizeText("@remove", []),
        actions: [continueText, sap.m.MessageBox.Action.CLOSE],
        onClose: (action: sap.m.MessageBox.Action) => {
          if (action === continueText) {
            resolve(true);
          } else if (action === sap.m.MessageBox.Action.CLOSE) {
            reject(false);
          }
        },
      });
    });
  }

  public msgStripPartitioningFormatter(partitions, hasNoKeyWithPartitions) {
    const partitionsExists = partitions?.length > 0 ? true : false;
    if (partitionsExists) {
      if (hasNoKeyWithPartitions) {
        return this.getI18nTableText("msg_partition_withoutKeys");
      } else {
        return this.getI18nTableText("msg_partition_withKeys");
      }
    }
    return "";
  }

  public msgStripPartitioningVisibleFormatter(partitions, fileStorage) {
    const partitionsExists = partitions?.length > 0 ? true : false;
    if (fileStorage) {
      return false;
    }
    if (partitionsExists) {
      return true;
    }
    return false;
  }

  public async preCheckConditionsForPartitionDialog() {
    let canProceed = true;
    const tableHasKey = this.getTableHasPrimaryKey();
    const partitionExists = this.getGalileiModel()?.getData()?.partitions?.length > 0 ? true : false;
    const elements = this.getGalileiModel()?.getData()?.orderedElements;
    // If there are no columns in the table
    if (elements.length <= 0) {
      const addAtleastoneCol = this.getI18nTableText("txt_addAtleastoneCol", [this.getGalileiModel().getData().name]);
      sap.m.MessageBox.error(addAtleastoneCol, {
        id: "addAtleastOnecolErrorDialog",
      });
      canProceed = false;
      return canProceed;
    } else if (this.getPossiblePartitioningColumnsList()?.length === 0) {
      if (
        this.getGalileiModel()?.getData()?.fileStorage &&
        this.getPossiblePartitioningColumnsListForLtf()?.length > 0
      ) {
        return canProceed;
      }
      // If there are no valid supported datatypes which can be used for partitioning
      const noValidPartitionColExists = this.getI18nTableText("txt_no_valid_partitionCol");
      sap.m.MessageBox.error(noValidPartitionColExists, {
        id: "addAtleastOnecolErrorDialog",
      });
      canProceed = false;
      return canProceed;
    }
    // If the table does not contain key columns, show warning that key cannot be defined again
    else if (!tableHasKey && !partitionExists) {
      if (this.getGalileiModel()?.getData()?.fileStorage) {
        canProceed = true;
      } else {
        const userAction = await this.showWarningNoKeyDefined();
        if (userAction === true) {
          canProceed = true;
        } else {
          canProceed = false;
        }
      }
    }
    return canProceed;
  }

  public getPossiblePartitioningColumnsListForLtf() {
    const elements = this.getGalileiModel()?.getData()?.orderedElements;
    return elements.filter((elm) => {
      return elm.isCDCColumn !== true && SupportedPartitionDataTypesLTF.includes(elm.dataType);
    });
  }

  public async openPartitionsDialog(oEvent?: any) {
    const isOk = await this.preCheckConditionsForPartitionDialog();
    if (isOk) {
      if (this.getGalileiModel()?.getData()?.fileStorage) {
        let popUp = sap.ui.getCore().byId("ltfPartitionPopup") as sap.m.p13n.Popup;
        if (!popUp) {
          const ltfPartitionDlg = require("../view/LTFPartitionsDialog.fragment.xml");
          popUp = sap.ui.xmlfragment(ltfPartitionDlg, this) as sap.m.p13n.Popup;
        }
        this.getView().addDependent(popUp);
        const selectionPanel = sap.ui.getCore().byId("columnsPanelId") as sap.m.p13n.SelectionPanel;
        const possiblePartitionColumns = this.getPossiblePartitioningColumnsListForLtf();
        let partitionedColumns;
        partitionedColumns = this.getPartitionedColumn();
        let columns = [];
        possiblePartitionColumns?.forEach((column) => {
          columns.push({
            name: column.name,
            label: column.name,
            visible: partitionedColumns.includes(column.name),
          });
        });
        selectionPanel.setP13nData(columns);
        popUp.open(oEvent.getSource());
        popUp.attachClose(() => {
          this.onCloseP13Popup(oEvent);
        });
      } else {
        let dialog = sap.ui.getCore().byId("partitionDialog") as sap.m.Dialog;
        if (!dialog) {
          const partitionsDlg = require("../view/PartitionsDialog.fragment.xml");
          dialog = sap.ui.xmlfragment(partitionsDlg, this) as sap.m.Dialog;
        }
        this.getView().addDependent(dialog);
        this["partitionDialogModel"] = new sap.ui.model.json.JSONModel();
        const possiblePartitionColumns = this.getPossiblePartitioningColumnsList();
        let partitionedColumn;
        partitionedColumn = this.getPartitionedColumn();
        if (!partitionedColumn) {
          partitionedColumn = possiblePartitionColumns?.[0]?.name;
        }
        const modelData = this.getGalileiModel().getData();
        const partitionDlgData = {
          dataCategory: this.getGalileiModel().getData()?.dataCategory,
          ranges: this.getRangesForPartitions(),
          partitionedColumn: partitionedColumn, // for range partition
          hashedColumns: this.getHashedColumns(),
          partitioningColumnsList: possiblePartitionColumns, // for both range & hash
          partitionType: modelData?.partitions?.length > 0 ? modelData?.partitions.get(0)?.partitionType : "RANGE", // determines range or hash
          partitionTypeList: this.getPartitionTypeList(),
          noOfHashPartitions: modelData?.partitions?.length > 0 ? modelData?.partitions.get(0)?.noOfHashPartitions : 10,
          noOfHashPartitionValueState: sap.ui.core.ValueState.None,
          noOfHashPartitionValueStateText: "",
        };
        this["partitionDialogModel"].setData(partitionDlgData);
        this["initialRanges"] = cloneDeep(partitionDlgData.ranges);
        this["initialPartitionColumn"] = partitionDlgData.partitionedColumn;
        dialog.setModel(this["partitionDialogModel"], "partitionDialogModel");
        dialog.setEscapeHandler(() => {
          this.closeAndDestroyDlg(dialog);
        });
        dialog.open();
        // validates the lower and higher values if imported csn contains issues
        if (this.getGalileiModel()?.getProperty("/partitions")?.length > 0) {
          this.validateAllBounds();
          this["partitionDialogModel"].updateBindings(true);
        } else {
          this.setSaveButtonEnabled(false);
        }
      }
    }
  }

  public partitionDlgTableRangesVisibleFormatter(partitionType) {
    if (isHashPartitioningEnabled() && partitionType === PartitionType.HASH) {
      return false;
    }
    return true;
  }

  public onCloseP13Popup(oEvnt) {
    if (oEvnt.getParameter("reason") === "Ok") {
      const columnsData = sap.ui.getCore().byId("columnsPanelId") as sap.m.p13n.SelectionPanel;
      const partitionedColumns = columnsData.getP13nData(true);
      const entity = this.getGalileiModel().getData();
      const partitions = this.getGalileiModel().getData().partitions;
      partitions.deleteAll();
      let count = 0;
      partitionedColumns.forEach((column) => {
        if (column.visible) {
          count++;
          let oPartitions;
          entity.resource.model.resource.applyUndoableAction(() => {
            const oClass = sap.galilei.model.getClass("sap.cdw.commonmodel.Partitions");
            oPartitions = oClass.create(entity.resource, {
              id: column?.name,
            });
          });
          this?.getView()?.getModel("galileiModel").getData().partitions?.push(oPartitions);
        }
      });
      if (count !== 0 && count === this.getPossiblePartitioningColumnsListForLtf().length && !this.messageBoxInstance) {
        this.messageBoxInstance = true;
        sap.m.MessageBox.show(
          this.tableResourceModel.getResourceBundle().getText("allColumsSelectedPartitionTextNew"),
          {
            icon: sap.m.MessageBox.Icon.ERROR,
            title: this.tableResourceModel.getProperty("titError"),
            actions: [sap.m.MessageBox.Action.CLOSE],
            initialFocus: sap.m.MessageBox.Action.CLOSE,
            onClose: () => {
              this.openPartitionsDialog(oEvnt);
              this.messageBoxInstance = false;
            },
          }
        );
      }
    }
  }

  public onEditPartitions(oEvnt) {
    this.openPartitionsDialog(oEvnt);
  }

  public async onDeletePartition(oEvent) {
    const userAction = await this.confirmDeletion();
    if (userAction === true) {
      if (this.getGalileiModel()?.getData()?.fileStorage) {
        const partitions = this.getGalileiModel().getProperty("/partitions");
        partitions?.forEach((partition) => {
          partition?.deleteObject();
        });
        this.getGalileiModel().updateBindings(true);
      } else {
        const partitions = this.getGalileiModel().getProperty("/partitions");
        partitions?.[0]?.deleteObject();
        this.getGalileiModel().updateBindings(true);
      }
    }
  }

  public confirmDeletion(): Promise<boolean> {
    return new Promise<boolean>((resolve, reject) => {
      const delText = this.getI18nTableText("txt_delete");
      sap.m.MessageBox.confirm(this.getI18nTableText("txt_delete_partitions"), {
        id: "confirmDelPartitionDlg",
        icon: sap.m.MessageBox.Icon.WARNING,
        title: this.getI18nTableText("txt_delete"),
        actions: [delText, sap.m.MessageBox.Action.CANCEL],
        onClose: (action: sap.m.MessageBox.Action) => {
          if (action === delText) {
            resolve(true);
          } else if (action === sap.m.MessageBox.Action.CANCEL) {
            reject(false);
          }
        },
      });
    });
  }

  public editPartitionEnabledFormatter(hasData) {
    if (hasData) {
      return false;
    }
    return true;
  }

  public onSavePartition() {
    let dialog = sap.ui.getCore().byId("partitionDialog") as sap.m.Dialog;
    const entity = this.getGalileiModel().getData();
    const partitionData = this["partitionDialogModel"].getData();
    const type = partitionData.partitionType;
    const partitions = this.getGalileiModel().getData().partitions;
    this.closeAndDestroyDlg(dialog);
    const columnDetails = this.getPartitionColumnDetails(partitionData?.partitionedColumn);
    // Create partition class

    entity.resource.model.resource.applyUndoableAction(() => {
      partitions.deleteAll();
      const oClass = sap.galilei.model.getClass("sap.cdw.commonmodel.Partitions");
      const oPartitions = oClass.create(entity.resource, {
        id: partitionData?.partitionedColumn,
        partitionDatatype: columnDetails?.primitiveDataType,
        partitionType: type,
      });
      if (isHashPartitioningEnabled() && type === PartitionType.HASH) {
        partitionData.hashedColumns.forEach((hashedColumnName) => {
          const element = findInCollectionByName(this.getGalileiModel().getData().elements, hashedColumnName);
          oPartitions?.hashedColumns.push(element);
        });
        oPartitions.noOfHashPartitions = partitionData.noOfHashPartitions;
      }
      if (type === PartitionType.RANGE) {
        // Create Ranges collection
        const oRangeClass = sap.galilei.model.getClass("sap.cdw.commonmodel.PartitionRanges");
        partitionData.ranges.forEach((range) => {
          const rangeDef = oRangeClass.create(oPartitions.resource, {
            partitionId: range.partitionId,
            low: range?.lowVal?.value,
            high: range?.highVal?.value,
          });
          // Add ranges to partitions
          oPartitions?.ranges.push(rangeDef);
        });
      }
      this?.getView()?.getModel("galileiModel").getData().partitions.push(oPartitions);
      this.getGalileiModel()?.updateBindings(true);
    });
  }

  public createPartitionBtnVisibleFormatter(annotatePartitionFF, partitions) {
    if (annotatePartitionFF) {
      return false;
    }
    if (partitions.length > 0) {
      return false;
    }
    return true;
  }

  public noPartitionsIllustratedMessageVisibleFormatter(
    annotatePartitionFF,
    partitions,
    allowExtensions,
    partitionsFromExtension
  ) {
    if (!isModelingAnnotatePartitionsEnabled()) {
      return annotatePartitionFF && partitions.length === 0;
    }

    if (annotatePartitionFF && partitions.length === 0) {
      // If not in extensions mode, always show illustrated message
      if (!allowExtensions || !partitionsFromExtension) {
        return true;
      }

      // If in extensions mode, check if there are actual partitions in the main table definition
      const galileiModel = this.getGalileiModel();
      const entity = galileiModel?.getData();
      if (entity) {
        // Check if there are actual partitions stored in the table (not from extensions)
        const actualPartitionInfo = (entity as any).actualPartitionInfo;
        return !actualPartitionInfo || Object.keys(actualPartitionInfo || {}).length === 0;
      }

      return true;
    }

    return false;
  }

  public partitionEditDeleteEnabledFormatter(allowExtensions, hasData, canCreateOrUpdateModel) {
    if (!isModelingAnnotatePartitionsEnabled()) {
      // Fall back to original behavior when feature flag is off
      return !hasData && canCreateOrUpdateModel;
    }

    // Enable edit/delete when allowExtensions is true, regardless of other conditions
    if (allowExtensions) {
      return canCreateOrUpdateModel;
    }
    // Original logic: disable when data exists
    return !hasData && canCreateOrUpdateModel;
  }

  public partitionCreateEnabledFormatter(allowExtensions, hasData, canCreateOrUpdateModel, partitions) {
    if (!isModelingAnnotatePartitionsEnabled()) {
      // Fall back to original behavior when feature flag is off
      return !hasData && canCreateOrUpdateModel && partitions.length === 0;
    }

    // Enable create when allowExtensions is true and no partitions exist
    if (allowExtensions) {
      return canCreateOrUpdateModel && partitions.length === 0;
    }
    // Original logic: disable when data exists
    return !hasData && canCreateOrUpdateModel && partitions.length === 0;
  }

  public resetPartitionsVisibleFormatter(allowExtensions, partitionsFromExtension) {
    if (!isModelingAnnotatePartitionsEnabled()) {
      return false; // Hide reset button when feature flag is off
    }
    return allowExtensions && partitionsFromExtension;
  }

  public onResetPartitions() {
    const { isModelingAnnotatePartitionsEnabled } = require("../../abstractbuilder/commonservices/FeatureFlagCheck");
    if (!isModelingAnnotatePartitionsEnabled()) return;

    const galileiModel = this.getGalileiModel();
    const entity = galileiModel.getData();

    sap.m.MessageBox.confirm(this.getI18nTableText("resetPartitionsConfirmation"), {
      onClose: (action) => {
        if (action === sap.m.MessageBox.Action.OK) {
          entity.resource.model.resource.applyUndoableAction(() => {
            entity.partitions?.deleteAll();
            const actualPartitionInfo = (entity as any).actualPartitionInfo;
            if (actualPartitionInfo) {
              this.recreatePartitionsFromInfo(actualPartitionInfo, entity);
            }
            (entity as any).partitionsFromExtension = false;
            galileiModel.updateBindings(true);
            sap.m.MessageToast.show(this.getI18nTableText("resetPartitionsSuccess"));
          });
        }
      },
    });
  }

  private recreatePartitionsFromInfo(partitionInfo: any, entity: any) {
    if (!partitionInfo) return;
    const type = partitionInfo.by?.["#"];

    if (type === "RANGE") {
      const partition = this.getPartitionsFromCSN(
        { type, id: partitionInfo.elements?.[0]?.["="], ranges: partitionInfo.ranges },
        entity
      );
      entity.partitions.push(partition);
    } else if (type === "HASH") {
      const oClass = sap.galilei.model.getClass("sap.cdw.commonmodel.Partitions");
      const oHashPartition = oClass.create(entity.resource, {
        partitionType: type,
        noOfHashPartitions: partitionInfo.numberOfPartitions,
      });
      partitionInfo.elements
        .map((element) => element["="])
        .forEach((name) => {
          const element = entity.elements?.find((item) => item.name === name);
          if (element) oHashPartition.hashedColumns.push(element);
        });
      entity.partitions.push(oHashPartition);
    } else if (type === "COLUMNS") {
      partitionInfo?.elements?.forEach((element) => {
        const oClass = sap.galilei.model.getClass("sap.cdw.commonmodel.Partitions");
        entity.partitions.push(oClass.create(entity.resource, { id: element["="] }));
      });
    }
  }

  private getPartitionsFromCSN(oParams: any, entity: any) {
    const element = entity?.orderedElements?.find((elem) => elem.name === oParams.id);
    const oClass = sap.galilei.model.getClass("sap.cdw.commonmodel.Partitions");
    const oPartitions = oClass.create(entity.resource, {
      id: oParams.id,
      partitionDatatype: element.primitiveDataType,
      partitionType: oParams.type,
    });
    const oRangeClass = sap.galilei.model.getClass("sap.cdw.commonmodel.PartitionRanges");
    oParams.ranges.forEach((range, index) => {
      oPartitions.ranges.push(
        oRangeClass.create(oPartitions.resource, { partitionId: index + 1, low: range?.low, high: range?.high })
      );
    });
    return oPartitions;
  }

  public columnLabelFormatter(dataCategory) {
    if (dataCategory === DataCategory.DATASET) {
      return this.tableResourceModel.getResourceBundle().getText("columnLbl");
    }
    return this.tableResourceModel.getResourceBundle().getText("attributeLbl");
  }

  public partitionTypeFormatter(partitionType) {
    if (partitionType === PartitionType.RANGE) {
      return "Range";
    } else if (partitionType === PartitionType.HASH) {
      return "Hash";
    }
  }

  public hashColumnInfoFormatter(hashedColumns) {
    const names = hashedColumns.map((hashCol) => hashCol.name);
    return names.join(", ");
  }
  public partitionsDefinitionVisibilityFormatter(partitions, isLTF) {
    if (isLTF) {
      return false;
    }
    return partitions.length > 0 ? true : false;
  }

  public partitionRangeVisibleFormatter(partitions, partitionType, isLTF) {
    if (isLTF) {
      return false;
    }
    if (isHashPartitioningEnabled()) {
      return partitionType === PartitionType.RANGE && partitions?.length > 0;
    }
    return partitions.length > 0 ? true : false;
  }

  public partitionHashVisibleFormatter(partitionType) {
    if (isHashPartitioningEnabled()) {
      return partitionType === PartitionType.HASH;
    }
    return false;
  }

  public partitionColumnsVisibleFormatter(partitions, isLTF) {
    if (isLTF && partitions?.length > 0) {
      return true;
    }
    return false;
  }

  public onCancelPartitionDlg() {
    let dialog = sap.ui.getCore().byId("partitionDialog") as sap.m.Dialog;
    if (this.checkforUnsaved()) {
      sap.m.MessageBox.confirm(this.getI18nTableText("unsavedDataTxt"), {
        onClose: async (action) => {
          if (action === sap.m.MessageBox.Action.OK) {
            if (dialog) {
              dialog.close();
            }
          }
        },
      });
    } else {
      if (dialog) {
        dialog.close();
      }
    }
  }

  public checkforUnsaved() {
    let hasChanges = false;
    const currentColumn = this["partitionDialogModel"].getData()?.partitionedColumn;
    const currentRanges = cloneDeep(this["partitionDialogModel"].getData()?.ranges);
    if (this["initialPartitionColumn"] !== currentColumn) {
      hasChanges = true;
    } else if (this["initialRanges"]?.length !== currentRanges?.length) {
      hasChanges = true;
    } else {
      currentRanges.forEach((range) => {
        delete range.lowVal?.valueState;
        delete range.highVal?.valueState;
        delete range.lowVal?.valueStateText;
        delete range.highVal?.valueStateText;
      });
      if (JSON.stringify(this["initialRanges"]) !== JSON.stringify(currentRanges)) {
        hasChanges = true;
      }
    }
    return hasChanges;
  }

  public closeAndDestroyDlg(dialog) {
    dialog.close();
  }

  public getPartitionedColumn() {
    const partitions = this.getGalileiModel().getData().partitions;
    if (this.getGalileiModel().getData().fileStorage) {
      const partitionedColumns = [];
      partitions.forEach((partition) => {
        partitionedColumns.push(partition.id);
      });
      return partitionedColumns;
    } else {
      return partitions?.get(0)?.id;
    }
  }

  public getHashedColumns() {
    let hashedCols = [];
    if (isHashPartitioningEnabled()) {
      const partitions = this.getGalileiModel().getData()?.partitions;
      if (partitions.length > 0 && partitions.get(0)?.partitionType === PartitionType.HASH) {
        hashedCols = partitions.get(0).hashedColumns.map((col) => col.name);
      }
    }
    return hashedCols;
  }

  public getPossiblePartitioningColumnsList() {
    const entity = this.getGalileiModel().getData();
    const tableHasKey = this.getTableHasPrimaryKey(entity);
    const possiblePartitionColumns = entity.orderedElements?.filter((element) => {
      if (tableHasKey) {
        if (element.isKey === true && SupportedPartitionDataTypes.includes(element.dataType)) {
          return element;
        }
      } else if (SupportedPartitionDataTypes.includes(element.dataType)) {
        return element;
      }
    });
    return possiblePartitionColumns;
  }

  public getPartitionTypeList() {
    return [
      { key: "RANGE", text: "Range" },
      { key: "HASH", text: "Hash" },
    ];
  }

  public getTableHasPrimaryKey(entity?) {
    let hasKey = false;
    let elements;
    if (entity) {
      elements = entity?.orderedElements;
    } else {
      elements = this.getGalileiModel()?.getData()?.orderedElements;
    }
    for (let i = 0; i < elements?.length; i++) {
      const elem = elements[i];
      if (elem?.isKey === true) {
        hasKey = true;
        break;
      }
    }
    return hasKey;
  }

  public getRangesForPartitions() {
    const ranges = [];
    const partitions = this.getGalileiModel().getData().partitions;
    if (partitions?.length > 0 && partitions.get(0)?.partitionType === "RANGE") {
      const partitionRanges = partitions.get(0)?.ranges;
      if (partitionRanges?.length > 0) {
        partitionRanges.forEach((range) => {
          const rangeObj = {
            partitionId: range.partitionId,
            lowVal: {
              value: range.low,
              editable: true,
            },
            highVal: {
              value: range.high,
              editable: partitionRanges?.length === range?.partitionId || partitionRanges?.length === 1 ? true : false,
            },
          };
          ranges.push(rangeObj);
        });
      } else {
        ranges.push(this.addNewRange());
      }
    } else {
      ranges.push(this.addNewRange());
    }
    return ranges;
  }

  public addNewRange(index?) {
    let rangeData = this["partitionDialogModel"].getProperty("/ranges");
    const newRange = {
      partitionId: index || 1,
      lowVal: {
        value: "",
        editable: true,
      },
      highVal: {
        value: "",
        editable: false,
      },
    };
    if (
      newRange.partitionId === 1 ||
      rangeData?.length === newRange.partitionId ||
      rangeData?.length === 1 ||
      rangeData === undefined
    ) {
      newRange.highVal.editable = true;
    }
    return newRange;
  }

  public onPartitionColumnChange(oEvent) {
    let rangeData = this["partitionDialogModel"].getProperty("/ranges");
    const previousKey = oEvent.getParameter("previousSelectedItem")?.getKey();
    if (rangeData?.length >= 1 && (rangeData[0]?.lowVal?.value !== "" || rangeData[0]?.highVal?.value !== "")) {
      sap.m.MessageBox.confirm(this.getI18nTableText("columnChangeConfirmationTxt"), {
        actions: [this.getI18nTableText("confirmText"), sap.m.MessageBox.Action.CANCEL],
        onClose: (action) => {
          if (action === this.getI18nTableText("confirmText")) {
            rangeData = [];
            this["partitionDialogModel"].setProperty("/ranges", rangeData);
            rangeData.push(this.addNewRange());
            this["partitionDialogModel"].setProperty("/ranges", rangeData);
            this.setSaveButtonEnabled(false);
          } else {
            this["partitionDialogModel"].setProperty("/partitionedColumn", previousKey);
          }
          this["partitionDialogModel"].updateBindings(true);
        },
      });
    }
  }

  public reviewAIVisibleAttributes(object) {
    return !!(isGenAISemanticEnabled() && object?.hasAIChangeOnAttributes === true);
  }

  public reviewAIVisibleMeasures(object) {
    return !!(isGenAISemanticEnabled() && object?.hasAIChangeOnMeasures === true);
  }

  public addRange(oEvent) {
    const source = oEvent.getSource();
    const currRange = source.getBindingContext("partitionDialogModel").getObject();
    const rangeData = this["partitionDialogModel"].getProperty("/ranges");
    const newPartition = {
      partitionId: currRange.partitionId + 1,
      lowVal: {
        value: currRange?.highVal?.value,
        editable: currRange.highVal?.value ? false : true,
      },
      highVal: {
        value: "",
        editable: true,
      },
    };
    currRange.highVal.editable = false;
    // disable editing on high if it is not last partition
    if (rangeData[currRange.partitionId]) {
      newPartition.highVal.editable = false;
    }
    rangeData.splice(currRange.partitionId, 0, newPartition);
    this.updateRange(rangeData);
    this.validateAllBounds();
  }

  private updateRange(rangeData) {
    this.resetRange(rangeData);
    this["partitionDialogModel"].setProperty("/ranges", rangeData);
    this["partitionDialogModel"].updateBindings(true);
  }

  private resetRange(rangeData) {
    if (rangeData) {
      let num = 1;
      let prevRange;
      rangeData.forEach((elem) => {
        elem.partitionId = num;
        num = num + 1;
        if (elem.partitionId != 1 && prevRange) {
          elem.lowVal.value = prevRange?.highVal?.value;
        }
        prevRange = elem;
      });
      const lastIndex = rangeData?.length - 1;
      if (rangeData[lastIndex]) {
        rangeData[lastIndex].highVal.editable = true;
      }
    }
  }

  public onLowValueChangeLive(oEvent) {
    const source = oEvent.getSource();
    const currRange = source.getBindingContext("partitionDialogModel").getObject();
    const currValue = oEvent.getParameter("value");
    const rangeData = this["partitionDialogModel"].getProperty("/ranges");
    const previousRange = rangeData[currRange.partitionId - 2];
    // update previous range high value same as current range low value
    if (previousRange) {
      previousRange.highVal.value = currValue;
      this.onChange(currValue, previousRange, true);
    }
    this.onChange(currValue, currRange, false);
  }

  public onHighValueChangeLive(event) {
    const input: sap.m.Input = event.getSource() as sap.m.Input;
    const currRange = input.getBindingContext("partitionDialogModel").getObject();
    const currValue = event.getParameter("value") as number;
    currRange.highVal.value = currValue;
    this.onChange(currValue, currRange, true);
  }

  public onPartitionValueChange(event, isHighValue: boolean) {
    const input: sap.m.Input = event.getSource() as sap.m.Input;
    const currRange = input.getBindingContext("partitionDialogModel").getObject();
    const currValue = event.getParameter("value");
    this.onChange(currValue, currRange, isHighValue, true, input);
  }

  public onChange(currValue, currRange, isHighValue: boolean, isChange?: boolean, control?: any) {
    let currLowValue, currHighValue, rangeData, previousRange, prevLowValue;
    let inputName;
    if (isHighValue) {
      inputName = "highVal";
      currHighValue = currValue;
      currLowValue = currRange.lowVal.value;
    } else {
      inputName = "lowVal";
      currLowValue = currValue;
      currHighValue = currRange.highVal.value;
      rangeData = this["partitionDialogModel"].getProperty("/ranges");
      previousRange = rangeData[currRange.partitionId - 2];
      prevLowValue = previousRange ? previousRange.lowVal.value : null;
    }

    let dataTypeValid = true;
    let isSaveEnabled = true;
    let columnName = this["partitionDialogModel"].getProperty("/partitionedColumn");
    if (columnName === "") {
      const combo = this.byId("columnCombo") as sap.m.Select;
      columnName = combo?.getSelectedKey();
    }
    if (isHashPartitioningEnabled() && columnName === "") {
      const combo = this.byId("columnComboNew") as sap.m.Select;
      columnName = combo?.getSelectedKey();
    }
    if (!this["partitionColumnDetails"] || this.getPartitionColumnDetails?.name !== columnName) {
      this["partitionColumnDetails"] = this.getPartitionColumnDetails(columnName);
    }
    const validationMsg = this.validateBounds(this["partitionColumnDetails"], currValue, control, isChange);
    let valMessageAnother;
    if (!isHighValue) {
      valMessageAnother = this.validateBounds(this["partitionColumnDetails"], currHighValue, undefined, isChange);
    } else {
      valMessageAnother = this.validateBounds(this["partitionColumnDetails"], currLowValue, undefined, isChange);
    }

    // Check for empty values
    let hasError = false;
    if (currLowValue === "" || currHighValue === "") {
      currRange[inputName].valueState = sap.ui.core.ValueState.Error;
      currRange[inputName].valueStateText = this.getI18nTableText("emptyBoundValuesErrorMsg");
      isSaveEnabled = false;
      hasError = true;
    }
    if (validationMsg !== "") {
      currRange[inputName].valueState = sap.ui.core.ValueState.Error;
      currRange[inputName].valueStateText = validationMsg;
      isSaveEnabled = false;
      dataTypeValid = false;
      hasError = true;
    }
    if (valMessageAnother !== "") {
      if (!isHighValue) {
        currRange["highVal"].valueState = sap.ui.core.ValueState.Error;
        currRange.highVal.valueStateText = valMessageAnother;
        isSaveEnabled = false;
        dataTypeValid = false;
        hasError = true;
      } else {
        currRange["lowVal"].valueState = sap.ui.core.ValueState.Error;
        currRange.lowVal.valueStateText = valMessageAnother;
        isSaveEnabled = false;
        dataTypeValid = false;
        hasError = true;
      }
    } else {
      // check validation type - if cds.string do string like validation
      // else do number like validation
      const type = this["partitionColumnDetails"]?.dataType;
      if (type === CDSDataType.DATE || type === CDSDataType.DATETIME || type === CDSDataType.TIMESTAMP) {
        currLowValue = Date.parse(currLowValue);
        currHighValue = Date.parse(currHighValue);
      } else if (type !== CDSDataType.STRING) {
        currLowValue = +currLowValue;
        currHighValue = +currHighValue;
        if (prevLowValue) {
          prevLowValue = +prevLowValue;
        }
      }
      // to check if lower bound larger than higher bound
      if (currLowValue >= currHighValue && !hasError) {
        currRange[inputName].valueState = sap.ui.core.ValueState.Error;
        if (type !== CDSDataType.STRING) {
          currRange[inputName].valueStateText = isHighValue
            ? this.getI18nTableText("higherbounderrormsg")
            : this.getI18nTableText("lowerbounderrormsg");
        } else {
          currRange[inputName].valueStateText = isHighValue
            ? this.getI18nTableText("higherbounderrormsgforString", [currLowValue, currHighValue])
            : this.getI18nTableText("lowerbounderrormsgforString", [currLowValue, currHighValue]);
        }
        isSaveEnabled = false;
        hasError = true;
      }
      if (prevLowValue && currLowValue <= prevLowValue && !hasError) {
        currRange[inputName].valueState = sap.ui.core.ValueState.Error;
        currRange[inputName].valueStateText = this.getText("lowerboundoverlaperrormsg");
        isSaveEnabled = false;
        hasError = true;
      }
      if (!hasError) {
        currRange.highVal.valueState = sap.ui.core.ValueState.None;
        currRange.highVal.valueStateText = "";
        currRange.lowVal.valueState = sap.ui.core.ValueState.None;
        currRange.lowVal.valueStateText = "";
      }
    }
    this.setSaveButtonEnabled(isSaveEnabled);
    if (dataTypeValid) {
      this.validateAllBounds();
    }
    this["partitionDialogModel"].updateBindings(true);
  }

  public getPartitionColumnDetails(columnName) {
    return (
      this["partitionDialogModel"]
        .getProperty("/partitioningColumnsList")
        .filter((elem) => elem.name === columnName)?.[0] || ""
    );
  }

  public partitionDlgRangeDefVisibilityFormatter(partitionType) {
    if (isHashPartitioningEnabled() && partitionType === PartitionType.RANGE) {
      return true;
    }
    return false;
  }

  public partitionDlgHashDefVisibilityFormatter(partitionType) {
    if (isHashPartitioningEnabled() && partitionType === PartitionType.HASH) {
      return true;
    }
    return false;
  }

  public onPartitionTypeChange(oEvent) {
    const partitionType = oEvent.getSource().getSelectedKey();
    this["partitionDialogModel"].setProperty("/partitionType", partitionType);
    const previousKey = oEvent.getParameter("previousSelectedItem")?.getKey();
    if (partitionType === PartitionType.HASH) {
      let rangeData = this["partitionDialogModel"].getProperty("/ranges");
      if (rangeData?.length >= 1 && (rangeData[0]?.lowVal?.value !== "" || rangeData[0]?.highVal?.value !== "")) {
        sap.m.MessageBox.confirm(this.getI18nTableText("partitionTypeChangeConfirmationTxt"), {
          actions: [this.getI18nTableText("confirmText"), sap.m.MessageBox.Action.CANCEL],
          onClose: (action) => {
            if (action === this.getI18nTableText("confirmText")) {
              rangeData = [];
              this["partitionDialogModel"].setProperty("/ranges", rangeData);
              rangeData.push(this.addNewRange());
              this["partitionDialogModel"].setProperty("/ranges", rangeData);
              this["partitionDialogModel"].setProperty("/partitionType", partitionType);
              this.setSaveButtonEnabled(false);
            } else {
              this["partitionDialogModel"].setProperty("/partitionType", previousKey);
            }
          },
        });
      }
    } else if (partitionType === PartitionType.RANGE) {
      const hashedColumns = this["partitionDialogModel"].getProperty("/hashedColumns");
      if (hashedColumns.length > 0) {
        sap.m.MessageBox.confirm(this.getI18nTableText("partitionTypeChangeConfirmationTxt"), {
          actions: [this.getI18nTableText("confirmText"), sap.m.MessageBox.Action.CANCEL],
          onClose: (action) => {
            if (action === this.getI18nTableText("confirmText")) {
              this["partitionDialogModel"].setProperty("/hashedColumns", []);
              this["partitionDialogModel"].setProperty("/partitionType", partitionType);
              this.setSaveButtonEnabled(false);
            } else {
              this["partitionDialogModel"].setProperty("/partitionType", previousKey);
            }
          },
        });
      }
    }
    this["partitionDialogModel"].updateBindings(true);
  }

  private setSaveButtonEnabled(isEnabled) {
    if (!this["saveButton"]) {
      this["saveButton"] = sap.ui.getCore().byId("saveData") as sap.m.Button;
    }
    this["saveButton"]?.setEnabled(isEnabled);
    this["partitionDialogModel"].updateBindings(true);
  }

  private validateAllBounds() {
    let rangeData = this["partitionDialogModel"].getProperty("/ranges");
    let column = this["partitionDialogModel"].getProperty("/partitionedColumn");
    if (column === "") {
      const combo = this.byId("columnCombo") as sap.m.Select;
      column = combo?.getSelectedKey();
    }
    if (isHashPartitioningEnabled() && column === "") {
      const combo = this.byId("columnComboNew") as sap.m.Select;
      column = combo?.getSelectedKey();
    }
    if (!this["partitionColumnDetails"] || this.getPartitionColumnDetails?.name !== column) {
      this["partitionColumnDetails"] = this.getPartitionColumnDetails(column);
    }
    const type = this["partitionColumnDetails"]?.dataType;
    let currLowValue, currHighValue;
    for (let i = 0; i < rangeData.length; i++) {
      const elem = rangeData[i];
      currLowValue = elem.lowVal.value;
      currHighValue = elem.highVal.value;
      if (currLowValue !== "" || currHighValue !== "") {
        let validationMsgLow, validationMsgHigh;
        if (currLowValue !== "") {
          validationMsgLow = this.validateBounds(this["partitionColumnDetails"], currLowValue);
        }
        if (currHighValue !== "") {
          validationMsgHigh = this.validateBounds(this["partitionColumnDetails"], currHighValue);
        }
        if (validationMsgLow !== "") {
          elem.lowVal.valueState = sap.ui.core.ValueState.Error;
          elem.lowVal.valueStateText = validationMsgLow;
          continue;
        }
        if (validationMsgHigh !== "") {
          elem.highVal.valueState = sap.ui.core.ValueState.Error;
          elem.highVal.valueStateText = validationMsgHigh;
          continue;
        }
        if (type !== CDSDataType.STRING) {
          currLowValue = +currLowValue;
          currHighValue = +currHighValue;
        }
        if (currLowValue < currHighValue) {
          const index = rangeData.indexOf(elem);
          let prevLowValue = index > 0 ? rangeData[index - 1].lowVal.value : undefined;
          if (type !== CDSDataType.STRING && prevLowValue) {
            prevLowValue = +prevLowValue;
          }
          if (!prevLowValue || (prevLowValue && currLowValue > prevLowValue)) {
            elem.lowVal.valueState = sap.ui.core.ValueState.None;
            elem.lowVal.valueStateText = "";
            elem.highVal.valueState = sap.ui.core.ValueState.None;
            elem.highVal.valueStateText = "";
          }
          continue;
        } else {
          elem.lowVal.valueState = sap.ui.core.ValueState.Error;
          elem.lowVal.valueStateText =
            type !== CDSDataType.STRING
              ? this.getI18nTableText("lowerbounderrormsg")
              : this.getI18nTableText("lowerbounderrormsgforString", [elem.lowVal.value, elem.highVal.value]);
          continue;
        }
      } else if (currLowValue === "" || currHighValue === "") {
        if (currLowValue === "") {
          elem.lowVal.valueState = sap.ui.core.ValueState.Error;
          elem.lowVal.valueStateText = this.getI18nTableText("emptyBoundValuesErrorMsg");
        } else {
          elem.highVal.valueState = sap.ui.core.ValueState.Error;
          elem.highVal.valueStateText = this.getI18nTableText("emptyBoundValuesErrorMsg");
        }
        continue;
      } else {
        // enable for string values where there is no scope of comparison. -- check
        continue;
      }
    }
    let enableSave = true;
    for (let i = 0; i < rangeData.length; i++) {
      const elem = rangeData[i];
      if (elem.lowVal.valueState === "Error" || elem.highVal.valueState === "Error") {
        enableSave = false;
        break;
      }
    }
    this.setSaveButtonEnabled(enableSave);
    if (rangeData?.length === 0 || column === "") {
      this.setSaveButtonEnabled(false);
    }
    this["partitionDialogModel"].updateBindings(true);
  }

  public onSelectHashColumns(oEvent) {
    const hashedColumns = oEvent.getSource().getSelectedKeys();
    this["partitionDialogModel"].setProperty("/hashedColumns", hashedColumns);
    this.validateHashPartition();
  }

  public onNumberOfHashPartitionLiveChange(oEvent) {
    const value = oEvent.getParameter("value") as number;
    if (value.toString().match(/^([+]?[1-9]\d*)$/g) !== null) {
      this["partitionDialogModel"].setProperty("/noOfHashPartitionValueState", sap.ui.core.ValueState.None);
      this["partitionDialogModel"].setProperty("/noOfHashPartitionValueStateText", "");
    } else {
      this["partitionDialogModel"].setProperty("/noOfHashPartitionValueState", sap.ui.core.ValueState.Error);
      this["partitionDialogModel"].setProperty(
        "/noOfHashPartitionValueStateText",
        this.getI18nTableText("txtPositiveInteger")
      );
    }
    this["partitionDialogModel"].setProperty("/noOfHashPartitions", value);
    this.validateHashPartition();
  }

  /**
   * Revert AI changes on element
   * @param event
   */
  public onRevertElementAIChange(event: IEvent<sap.m.Button, {}>) {
    const selectedElement = event.getSource().getBindingContext("galileiModel").getObject();
    revertElementAIChange(selectedElement);
  }

  public validateHashPartition() {
    let enable = false;
    const hashedColumns = this["partitionDialogModel"].getProperty("/hashedColumns");
    const noOfHashpartitions = this["partitionDialogModel"].getProperty("/noOfHashPartitions");
    const noOfHashpartitionsValueState = this["partitionDialogModel"].getProperty("/noOfHashPartitionValueState");
    if (hashedColumns.length > 0 && noOfHashpartitions > 0 && noOfHashpartitionsValueState !== "Error") {
      enable = true;
    }
    this.setSaveButtonEnabled(enable);
  }

  public deleteRange(oEvent) {
    const source = oEvent.getSource();
    const currId = source.getBindingContext("partitionDialogModel").getObject().partitionId;
    const rangeData = this["partitionDialogModel"].getProperty("/ranges");
    rangeData.splice(currId - 1, 1);
    this.resetRange(rangeData);
    this["partitionDialogModel"].setProperty("/ranges", rangeData);

    this.validateAllBounds();
    if (rangeData?.length === 0) {
      const newRange = this.addNewRange();
      this["partitionDialogModel"].setProperty("/ranges", [newRange]);
    }
    this["partitionDialogModel"].updateBindings(true);
  }

  private partitionTextFormatter(id: string) {
    return "Partition " + id;
  }

  public navigateMenuFormatter(deploymentStatus) {
    if (deploymentStatus == 0) {
      return false;
    }
    return true;
  }

  public deleteStatisticsMenuEnabledFormatter(statisticsType) {
    if (statisticsType !== undefined) {
      return true;
    }
    return false;
  }

  public tabServicesVisibilityFormatter(federationOnly, dataAccess, params, isDelta, isRemote, isLTF) {
    return false;
  }

  public onCreateStatistics(oEvent) {
    const galileiModel = this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel;
    const table = galileiModel.getData();
    const statisticsType = (this.getView().getModel("replicationModel") as any)?.getData()?.statisticsType;
    const isParameterAbapCdsViews = sap.ui
      .getCore()
      .getModel("featureflags")
      .getProperty("/DWCO_REMOTE_TABLE_PARAMETER_SUPPORT_ABAP_CDS_VIEWS");
    let statisticsLimitedToRecordCount = false;
    if (isParameterAbapCdsViews) {
      statisticsLimitedToRecordCount = (this.getView().getModel("replicationModel") as any)?.getData()
        ?.statisticsLimitedToRecordCount;
    }
    CreateStatistics(table.name, this.getSpaceName(), this.getView(), statisticsType, statisticsLimitedToRecordCount);
  }

  public async onCompleteRefresh(): Promise<void> {
    await this.refreshReplicationModel();
    this.getView().setBusy(false);
  }

  public onDeleteStatistics(oEvent) {
    const galileiModel = this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel;
    const table = galileiModel.getData();
    DeleteStatistics(table.name, this.getSpaceName(), this.getView());
  }

  public updateDeploymentStatus(channel, event, data) {
    const model = this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel;
    const object = model.getData();
    if (object["#objectStatus"] == ObjectStatus.pending) {
      // Clear all objects' cache
      Crud.get().clearCache([
        { type: Repo.space, name: this.getSpaceName() },
        { type: Repo.model, name: object.name },
      ]);

      getObjectFilesProperties(this.getSpaceName(), [object.name]).then((properties) => {
        if (properties) {
          properties.forEach((prop) => {
            if (prop) {
              updateObjectFileInfo(object, prop);
            }
          });
        }
      });
    }
  }

  public onAddPCElm(event: IEvent<sap.m.Button, {}>) {
    const galileiModel = this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel;
    const model = galileiModel.getData().resource.model;
    const sourceEntity = galileiModel.getData();
    const firstElement = sourceEntity.elements.get(0);
    const parentElement = firstElement;
    const childElement = firstElement;
    const hierarchy = sourceEntity.hierarchies.get(0);
    model.resource.applyUndoableAction(function () {
      if (hierarchy) {
        hierarchy.parentElement?.push(parentElement);
        hierarchy.childElement?.push(childElement);
      }
    }, "Change Hierarchy Parent and Child Element");
    const parentChildModel = this.getParentChildModel(hierarchy);
    HierarchyValidation.validateHierarchyModel(parentChildModel);
    this.getView().getModel("hierarchyUiModel").setProperty("/parentElements", parentChildModel);
    this.getView().getModel("hierarchyUiModel").setProperty("/childElements", parentChildModel);
    if (parentChildModel?.length <= 1) {
      this.getView().getModel("hierarchyUiModel").setProperty("/listItemDltMode", undefined);
    } else {
      this.getView().getModel("hierarchyUiModel").setProperty("/listItemDltMode", "Delete");
    }
  }

  public onDeletePCElm(oEvent) {
    const galileiModel = this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel;
    const model = galileiModel.getData().resource.model;
    const sourceEntity = galileiModel.getData();
    const hierarchy = sourceEntity.hierarchies.get(0);
    const sPath = oEvent.getParameters().listItem.getBindingContextPath();
    const itemIndex = sPath.split("/")[2];
    model.resource.applyUndoableAction(function () {
      const childEle = hierarchy?.childElement?.get(Number(itemIndex));
      childEle && hierarchy?.childElement?.removeAt(Number(itemIndex));
      const parentEle = hierarchy?.parentElement?.get(Number(itemIndex));
      parentEle && hierarchy?.parentElement?.removeAt(Number(itemIndex));
    }, "Delete Hierarchy Parent and Child Element");
    const parentChildModel = this.getParentChildModel(hierarchy);
    HierarchyValidation.validateHierarchyModel(parentChildModel);
    this.getView().getModel("hierarchyUiModel").setProperty("/parentElements", parentChildModel);
    this.getView().getModel("hierarchyUiModel").setProperty("/childElements", parentChildModel);
    if (parentChildModel?.length <= 1) {
      this.getView().getModel("hierarchyUiModel").setProperty("/listItemDltMode", undefined);
    } else {
      this.getView().getModel("hierarchyUiModel").setProperty("/listItemDltMode", "Delete");
    }
  }

  protected getParentChildModel(hierarchy) {
    const parentElements = hierarchy?.parentElement?.toArray();
    const childElements = hierarchy?.childElement?.toArray();
    const parentElementCount = hierarchy?.parentElement?.length ? hierarchy?.parentElement?.length : 1;
    const childElementCount = hierarchy?.childElement?.length ? hierarchy?.childElement?.length : 1;
    const parentChildCount = parentElementCount > childElementCount ? parentElementCount : childElementCount;
    const parentChildModel = [];
    for (let count = 0; count < parentChildCount; count++) {
      parentChildModel[count] = {};
      parentChildModel[count].parent = parentElements && parentElements[count];
      parentChildModel[count].child = childElements && childElements[count];
    }
    return parentChildModel;
  }

  public handleFiltersOnModelchange(oEntity, restoreDeployment) {
    // Handling deletion of columns
    if (oEntity) {
      const remoteFilter = oEntity?.remoteFilter;

      this.filterableColumns = this.getFilterableColumns();
      if (restoreDeployment) {
        if (oEntity.remoteFilter.length > 0) {
          const self = this;
          oEntity.resource.applyUndoableAction(function () {
            oEntity.remoteFilter?.forEach((filter) => {
              filter.filterableColumns = self.filterableColumns;
            });
          });
        }
      }

      remoteFilter?.forEach((filter) => {
        const foundElement = this.filterableColumns.filter((o) => o.name === filter.name);
        if (foundElement?.length === 0) {
          filter.deleteObject();
        } else if (this.filterableColumns?.length > 0) {
          if (foundElement?.length > 0 && foundElement[0]?.dataType !== filter.dataType) {
            filter.dataType = foundElement[0].dataType;
            filter.length = foundElement[0].length;
            filter.scale = foundElement[0].scale;
            filter.precision = foundElement[0].precision;
          }
        }
      });
      if (
        remoteFilter?.length === 0 &&
        (this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel)?.getData()
      ) {
        (this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel).getData().filterPanelVisible = false;
      }
    }
  }

  public onChangeSemanticType(event: IEvent<sap.m.Select, {}>) {
    selectWithEmptyEntrySetter(event, SemanticType.EMPTY);
  }

  public onChangeLabelColumn(event: IEvent<sap.m.Select, {}>) {
    changeLabelColumn.bind(this)(event);
  }

  public onChangeDefaultAggregation(event: IEvent<sap.m.Select, {}>) {
    const featureflags = sap.ui.getCore().getModel("featureflags");
    if (featureflags?.getProperty("/DWCO_MODELING_AM_LIFECYCLE_HANDLING_STATUS")) {
      const galileiModel = this.getView().getModel("galileiModel");
      const entity = galileiModel && galileiModel.getProperty("/");
      sap.cdw.commonmodel.Validation.validateEntity(entity);
    }
  }

  public onChangeMeasureUnitColumn(event: IEvent<sap.m.Select, {}>) {
    const select = event.getSource();
    const galileiModel = this.getView().getModel("galileiModel");
    const entity = galileiModel && galileiModel.getProperty("/");
    const selectedElementId = (event as any)?.getParameter?.("selectedItem")?.getKey() || select?.getSelectedKey?.();
    const currentElementId = select && select.data("objectId");
    const selectedElement = entity && entity.elements.selectObject({ objectId: selectedElementId });
    const currentElement = entity && entity.elements.selectObject({ objectId: currentElementId });
    if (selectedElement && currentElement) {
      currentElement.unitTypeElement = selectedElement;
    } else if (currentElement) {
      currentElement.unitTypeElement = undefined;
    }
    currentElement?.validate();
  }

  public async setObjectModel(galileiObject) {
    super.setHeaderModel(galileiObject);
    const galileiModel = this.createGalileiModel(galileiObject);
    this.clearUiState();
    this.createUiStateModel(galileiModel);
    galileiModel.setProperty("/fileStorage", false);
    this.getEditorComponent().onChangeObjectNameDisplay(User.getInstance().getObjectNameDisplay() as ObjectNameDisplay);
    const supportedFeaturesService = SupportedFeaturesService.getInstance();
    galileiModel.setProperty(
      "/packageValue",
      galileiObject.packageValue ? galileiObject.packageValue : "_NONE_KEY_PACKAGE_"
    );
    galileiModel.setProperty("/packageStatus", galileiObject.packageStatus ? galileiObject.packageStatus : "");
    if (supportedFeaturesService.isFiscalTimeDimensionEnabled()) {
      this.getGalileiModel().setProperty("/dimensionTypeList", getDimensionTypeData());
    }
    this.createDataTypeModel(galileiObject.isHierarchy);

    this.createHierarchyModel(galileiObject);

    this.setupHierarchyWithDirectoryUI(); // The function itself has already had FF check
    this.getView().setBusy(true);
    if (galileiModel?.getData()?.deploymentStatus !== 0 && galileiModel?.getData()?.isLTF) {
      galileiModel.setProperty("/fileStorage", true);
    } else {
      if (UISpaceCapabilities.get().hasCapability(super.getSpaceName(), "hdlfStorage")) {
        galileiModel.setProperty("/fileStorage", true);
        galileiModel.getData().isLTF = true;
      }
      if (galileiModel.getData()?.deploymentStatus === 0 && galileiModel.getData()?.fileStorage) {
        this.onDeltaCaptureChange(undefined);
      }
    }
    if (isSparkSelectionVacuumEnabled() && galileiModel?.getData()?.isLTF) {
      this.SetAppModel(galileiObject);
    }
    if (galileiObject.isRemote) {
      this.setupRemoteTableReplicationModel();
      // For undeployed tables, remote source name is required for refresh functionality
      if (
        galileiObject.isUserNotAuthorized ||
        galileiObject.deploymentStatus === 0 ||
        galileiObject.isRemoteTableRequestFailed
      ) {
        await this.getRemoteSourceInfo(this.spaceGUID);
      }
      this.getView().setBusy(true);
      const replicationStatusModel = await ReplicationStatusUtil.createReplicationServiceStateModel(
        galileiModel,
        this.getSpaceName()
      );
      this.getView().setBusy(false);
      this.setReplicationStatusModel(replicationStatusModel);
      galileiModel.getData().isRemoteDetailsLoaded = true;
      const remoteFilter = galileiObject.remoteFilter;
      remoteFilter.forEach((element) => {
        this.validateFilterItem(element.value1, element, false, false, true);
        if (element?.value2) {
          this.validateFilterItem(element.value2, element, true, false, true);
        }
      });
      this.filterableColumns = this.getFilterableColumns();
      if (remoteFilter.length > 0) {
        const self = this;
        galileiObject.resource.applyUndoableAction(
          function () {
            remoteFilter?.forEach((filter) => {
              filter.filterableColumns = self.filterableColumns;
            });
          },
          "set filterable columns",
          /* protectedFromUndo */ true
        );
      }
    }
    // dependent object data
    const selectedSection = (
      this.getView().byId("tableEditor--tableEditorPage") as sap.uxap.ObjectPageLayout
    )?.getSelectedSection();
    this.modelObjectForDependentObjects = galileiObject; // ToDo, try to get rid of property modelObjectForDependentObjects
    if (selectedSection === "tableEditor--dependentObjectSection") {
      sap.ui.getCore().getEventBus().publish("DEPENDENT_OBJECT_LIST", "REFRESH", {});
    } else {
      this.dependentObjectsLoaded = false;
      this.resetDepObjSection();
    }
    this.getView().setBusy(false);
    const oDataBuilderWorkbenchController = (
      sap.ui.getCore().byId("shellMainContent---databuilderComponent---databuilderWorkbench") as sap.ui.core.mvc.View
    ).getController() as IWorkbenchController;
    oDataBuilderWorkbenchController.getToolbarModel().setData(this.getGalileiModel().getData());
    this.getGalileiModel().updateBindings(true);
  }

  public genAIControlVisibilityFormatter(model) {
    if (model.container.isSkylineERModel) {
      return false;
    }
    return isGenAISemanticEnabled();
  }

  public oldControlVisibilityFormatter(model) {
    return !this.genAIControlVisibilityFormatter(model);
  }

  private async SetAppModel(galileiObject: any) {
    await fetchDefaultSparkSettingForDelete().then((defaultSparkSettings) => {
      defaultSparkSettings.forEach((item) => {
        if (item.activity === "VACUUM_FILES") {
          this.getView().getModel("galileiModel").setProperty("/vacuumDefaultSparkSetting", item.defaultSparkAppIndex);
        } else if (item.activity === "TRUNCATE_FILES") {
          this.getView()
            .getModel("galileiModel")
            .setProperty("/truncateDefaultSparkSetting", item.defaultSparkAppIndex);
          this.getView().getModel("galileiModel").setProperty("/defaultSparkSetting", item.defaultSparkAppIndex);
        }
      });
    });
    if (!this["appData"]) {
      const sparkSettings = await fetchApplicationId(super.getSpaceName());
      const oApplicationInfo = [];
      sparkSettings?.spark?.applications?.forEach((app) => {
        oApplicationInfo.push({
          index: app.identifier.index,
          label: app.identifier.label,
          maxCore: app.calculated["spark.cores.max"],
          maxMemory: app.calculated["spark.memory.max"],
        });
      });
      this["appData"] = oApplicationInfo;
    }
    this.getView().getModel("galileiModel")?.setProperty("/appData", this["appData"]);
    this.getView().getModel("galileiModel")?.setProperty("/isSelectEditable", false);
  }

  public appDetailsListFormatter(maxCore: any, maxMemory: any) {
    const numericValue = parseInt(maxMemory.toString().replace(/g/i, ""), 10);
    return `${numericValue} GiB + ${maxCore} vCPU`;
  }

  private createHierarchyModel(galileiObject: any) {
    const hierarchyUiModel = new sap.ui.model.json.JSONModel({});
    this.getView().setModel(hierarchyUiModel, "hierarchyUiModel");
    if (galileiObject?.dataCategory === DataCategory.HIERARCHY) {
      const hierarchy = galileiObject?.hierarchies?.get(0);
      if (isParentchildHierarchy(hierarchy)) {
        const parentChildModel = hierarchy && this.getParentChildModel(hierarchy);
        HierarchyValidation.validateHierarchyModel(parentChildModel);
        this.getView().getModel("hierarchyUiModel").setProperty("/parentElements", parentChildModel);
        this.getView().getModel("hierarchyUiModel").setProperty("/childElements", parentChildModel);
        if (parentChildModel?.length <= 1) {
          this.getView().getModel("hierarchyUiModel").setProperty("/listItemDltMode", undefined);
        } else {
          this.getView().getModel("hierarchyUiModel").setProperty("/listItemDltMode", "Delete");
        }
      }
    }
  }

  public onSectionChange(event) {
    if (event.getParameter("section")?.getId()?.includes("--dependentObjectSection")) {
      if (!this.dependentObjectsLoaded) {
        // Prepare dependent object data
        const galileiObject = this.modelObjectForDependentObjects;
        let result = [];
        const dependentObjectSection = this.getView().byId("dependentObjectSection") as sap.uxap.ObjectPageSection;
        dependentObjectSection.setBusy(true);
        this.prepareDependentObjectListData(galileiObject, this.getView().getModel("header"), false).then((result) => {
          // only load on demand, see DW101-802
          if (galileiObject.isDeltaTable && galileiObject.deltaTableName !== undefined) {
            const deltaObject = {
              classDefinition: {
                name: "Table",
              },
              name: galileiObject.deltaTableName,
              label: galileiObject.deltaTableName,
              qualifiedName: galileiObject.deltaTableName,
              "#objectStatus": galileiObject["#objectStatus"],
            };
            this.prepareDependentObjectListData(deltaObject, this.getView().getModel("header"), true).then((res1) => {
              const items = res1?.items?.filter((item) => item.name !== galileiObject.name);
              if (items) {
                items.forEach((elm) => {
                  result?.items.push(elm);
                });
              }
              if (res1?.inaccessibleDependencies) {
                res1.inaccessibleDependencies.forEach((elm) => {
                  result?.inaccessibleDependencies.push(elm);
                });
              }
              const finalDependencies = {
                inaccessibleDependencies: result?.inaccessibleDependencies,
                items: result?.items,
                numberOfItemsWithoutPermissionToView:
                  result.numberOfItemsWithoutPermissionToView + res1.numberOfItemsWithoutPermissionToView,
              };
              this.getView().getModel("header").setProperty("/dependentObjects", finalDependencies);
              this.dependentObjectsLoaded = true;
              dependentObjectSection.setBusy(false);
            });
          } else {
            this.getView().getModel("header").setProperty("/dependentObjects", result);
            this.dependentObjectsLoaded = true;
            dependentObjectSection.setBusy(false);
          }
        });
      }
    }
  }

  public onTechnicalNameChange(event) {
    const controls = this.getControlsFromEvent(event);
    const siblings = controls.isTableColumn ? this.getColumnSiblings(controls.object.container) : [];
    const commonBundle = this.oResourceCommonModel.getResourceBundle();
    const emptyMsg = controls.isTableColumn ? "VAL_TABLE_EMPTY_ELT" : "VAL_TABLE_NO_NAME";
    const nameValidator = NamingHelper.getNameInputValidator();
    const nameUsage = controls.isTableColumn ? NameUsage.element : NameUsage.entity;

    nameValidator.onTechnicalNameChanged(
      controls.object,
      controls.technicalInput,
      controls.technicalNameProperty,
      {
        duplicatedTechnicalName: commonBundle.getText("VAL_TABLE_DUPLICATED_ELT"),
        emptyTechnicalName: commonBundle.getText(emptyMsg),
      },
      siblings,
      nameUsage
    );
  }

  public onTechnicalNameSubmit(event) {
    const controls = this.getControlsFromEvent(event);
    const siblings = controls.isTableColumn ? this.getColumnSiblings(controls.object.container) : [];
    const commonBundle = this.oResourceCommonModel.getResourceBundle();
    const emptyMsg = controls.isTableColumn ? "VAL_TABLE_EMPTY_ELT" : "VAL_TABLE_NO_NAME";
    const nameValidator = NamingHelper.getNameInputValidator();
    const nameUsage = controls.isTableColumn ? NameUsage.element : NameUsage.entity;

    nameValidator.onTechnicalNameSubmit(
      controls.object,
      controls.technicalInput,
      controls.technicalNameProperty,
      {
        duplicatedTechnicalName: commonBundle.getText("VAL_TABLE_DUPLICATED_ELT"),
        emptyTechnicalName: commonBundle.getText(emptyMsg),
      },
      siblings,
      undefined,
      nameUsage
    );
  }

  public onBusinessNameChange(event) {
    const controls = this.getControlsFromEvent(event);
    const siblings = controls.isTableColumn ? this.getColumnSiblings(controls.object.container) : [];
    const commonBundle = this.oResourceCommonModel.getResourceBundle();
    const emptyMsg = controls.isTableColumn ? "VAL_TABLE_EMPTY_ELT" : "VAL_TABLE_NO_BUS_NAME";
    const nameValidator = NamingHelper.getNameInputValidator();
    const nameUsage = controls.isTableColumn ? NameUsage.element : NameUsage.entity;
    nameValidator.onLabelChanged(
      controls.businessInput,
      controls.technicalInput,
      controls.object,
      controls.technicalNameProperty,
      {
        duplicatedLabel: commonBundle.getText("VAL_TABLE_DUPLICATED_ELT_BUSINESS"),
        emptyLabel: commonBundle.getText(emptyMsg),
      },
      siblings,
      nameUsage
    );
  }

  public onBusinessNameSubmit(event) {
    const controls = this.getControlsFromEvent(event);
    const siblings = controls.isTableColumn ? this.getColumnSiblings(controls.object.container) : [];
    const commonBundle = this.oResourceCommonModel.getResourceBundle();
    const emptyMsg = controls.isTableColumn ? "VAL_TABLE_EMPTY_ELT" : "VAL_TABLE_NO_BUS_NAME";
    const nameValidator = NamingHelper.getNameInputValidator();
    const nameUsage = controls.isTableColumn ? NameUsage.element : NameUsage.entity;

    nameValidator.onLabelSubmit(
      controls.object,
      controls.businessInput,
      controls.technicalInput,
      controls.technicalNameProperty,
      {
        duplicatedLabel: commonBundle.getText("VAL_TABLE_DUPLICATED_ELT_BUSINESS"),
        emptyLabel: commonBundle.getText(emptyMsg),
      },
      siblings,
      nameUsage
    );
  }

  public async openRevertDeploymentDialog() {
    let openRevertDlg = true;
    if (this.getGalileiModel().getData().isRefreshApplyOrCancelTaken) {
      const result = await this.showRestoreRefreshDialog();
      if (!result) {
        openRevertDlg = false;
      }
    }
    if (openRevertDlg) {
      const entity = this.getGalileiModel().getData();
      this.restoreDeployment = true;
      commonUtils.openRevertDeploymentDialog(entity);
    }
  }

  public showRestoreRefreshDialog(): Promise<boolean> {
    const continueText = this.getI18nTableText("txt_continue");
    return new Promise<boolean>((resolve, reject) => {
      sap.m.MessageBox.show(this.localizeText("txt_confirmrestoreRefresh"), {
        id: "confirmRestoreRefresh",
        icon: sap.m.MessageBox.Icon.QUESTION,
        title: this.localizeText("txt_restoreDeployedVersionNew"),
        actions: [continueText, sap.m.MessageBox.Action.CLOSE],
        onClose: (action: sap.m.MessageBox.Action) => {
          if (action === continueText) {
            resolve(true);
          } else if (action === sap.m.MessageBox.Action.CLOSE) {
            reject(false);
          }
        },
      });
    });
  }

  public onAssociationRowSelection(event: sap.ui.base.Event) {
    const oModel = this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel;
    const selectedItemslength = (event.getSource() as sap.ui.table.Table).getSelectedIndices().length;
    oModel.setProperty("/associationSelectedCount", selectedItemslength);
  }

  public onCreateAssociation(event): void {
    this.onPressCreateAssociation(event);
    this.canDeployFilters();
  }

  public onAddAttributePress() {
    const entity = this.getGalileiModel().getData();
    const model = entity.resource.model;
    if (SupportedFeaturesService.getInstance().isSkylineERModelerEnabled() && model.isSkylineERModel) {
      model.processingManager.runModificationProcess({
        id: "createElement",
        description: "Add element",
        process: () => {
          const ele = entity.createElement();
          ele.isNew = true;
        },
      });
    } else {
      entity.createElement();
      const element = entity.elements.get(entity.elements.length - 1);
      element.isNew = true;
    }
    const compoundKeyButton = this.getView().byId("CompoundKeyButton") as sap.m.Button;
    if (entity.elementCount > 1) {
      compoundKeyButton.setEnabled(true);
    } else {
      compoundKeyButton.setEnabled(false);
    }
  }

  public async onDeleteSelectedRows(table) {
    this.getView().setBusy(true);
    let selectedElements = this.getSelectedElements(table);
    try {
      selectedElements = this.onCDCColumnsDelete(selectedElements);
      await this.deleteSelectedElementsOfTable(selectedElements);
    } finally {
      this.getView().setBusy(false);
    }
  }

  public getCDCColumns(elements) {
    return elements.filter((el) => el.isCDCColumn === true);
  }

  public async onDeleteSelectedAttributes() {
    await this.onDeleteSelectedRows(this.getAttributesTable());

    const entity = this.getGalileiModel().getData();
    const compoundKeyButton = this.getView().byId("CompoundKeyButton") as sap.m.Button;
    if (entity.elementCount > 1) {
      compoundKeyButton.setEnabled(true);
    } else {
      compoundKeyButton.setEnabled(false);
    }
  }

  public async onDeleteSelectedMeasures() {
    await this.onDeleteSelectedRows(this.getMeasuresTable());
  }

  public onAddMeasurePress() {
    const entity = this.getGalileiModel().getData();
    entity.createElement({
      isMeasure: true,
      isDimension: false,
      defaultAggregation: AggregationTypes.SUM,
      dataType: CDSDataType.INTEGER,
    });
    const element = entity.elements.get(entity.elements.length - 1);
    element.isNew = true;
  }

  public onFilterAttributes(event: IEvent<sap.m.SearchField, {}>) {
    const table = this.getAttributesTable();
    const query = event.getSource().getValue().trim();

    this.filterTable(table, query);
    this.getUiStateModel().setProperty("/attributes/search", query);
  }

  public onFilterAssociations(event: IEvent<sap.m.SearchField, {}>) {
    const table = this.getAssociationsTable();
    const query = event.getSource().getValue().trim();
    this.filterTable(table, query);
    this.getUiStateModel().setProperty("/associations/search", query);
  }

  public onFilterMeasures(event: IEvent<sap.m.SearchField, {}>) {
    const table = this.getMeasuresTable();
    const query = event.getSource().getValue().trim();

    this.filterTable(table, query);
    this.getUiStateModel().setProperty("/measures/search", query);
  }

  public onAttributesRowSelectionChange(
    event: IEvent<sap.ui.table.Table, { rowIndices: number[]; userInteraction: boolean }>
  ) {
    const attributesTable = event.getSource();
    const uiStateModel = this.getUiStateModel();
    uiStateModel.setProperty("/attributes/selection", this.isRowSelected(attributesTable));

    if (event.getParameter("userInteraction")) {
      const measuresTable = this.getMeasuresTable();
      if (measuresTable) {
        measuresTable.clearSelection();
      }
      uiStateModel.refresh(true);
    }

    if (this.getGalileiModel()?.getData()?.fileStorage) {
      if (this.getGalileiModel().getData().hasData) {
        uiStateModel.setProperty("/attributes/selection", false);
        let count = 0;
        attributesTable.getSelectedIndices().forEach((index) => {
          const element = this.getGalileiModel().getData().elements.get(index);
          if (element.isNew) {
            count++;
          }
        });
        if (count == attributesTable.getSelectedIndices().length) {
          uiStateModel.setProperty("/attributes/selection", true);
        }
      } else {
        let selectedCdcColumn = false;
        attributesTable.getSelectedIndices().forEach((index) => {
          const element = this.getGalileiModel().getData().elements.get(index);
          if (element.isCDCColumn) {
            selectedCdcColumn = true;
          }
        });
        if (selectedCdcColumn) {
          uiStateModel.setProperty("/attributes/selection", false);
        }
      }
    }
  }

  public onMeasuresRowSelectionChange(
    event: IEvent<sap.ui.table.Table, { rowIndices: number[]; userInteraction: boolean }>
  ) {
    const measuresTable = event.getSource();
    const uiStateModel = this.getUiStateModel();
    uiStateModel.setProperty("/measures/selection", this.isRowSelected(measuresTable));

    if (event.getParameter("userInteraction")) {
      const attributesTable = this.getAttributesTable();
      if (attributesTable) {
        attributesTable.clearSelection();
      }
      uiStateModel.refresh(true);
    }
  }

  public onMeasureTableDragEnter(
    event: IEvent<sap.ui.core.dnd.DropInfo, { dragSession: sap.ui.core.dnd.DragSession }>
  ) {
    const dragSession = event.getParameter("dragSession");
    const element = this.getElementFromDragSession(dragSession);

    if (element.isMeasure) {
      return;
    }

    const isMeasureTableDropAllowed = this.isMeasureTableDropAllowed(element);
    dragSession.getDropInfo().setDropEffect(isMeasureTableDropAllowed ? "Move" : "None");

    if (!isMeasureTableDropAllowed) {
      const uiStateModel = this.getUiStateModel();
      uiStateModel.setProperty("/measures/attributeToMeasureDisallowed", true);
    }
  }

  public onTagTokenUpdate(event: IEvent<sap.m.MultiInput, sap.m.IFireTokenUpdateParameters>): void {
    const entity = this.getGalileiModel().getData();
    const addedTokens = event.getParameter("addedTokens");
    const removedTokens = event.getParameter("removedTokens");
    const updateType = event.getParameter("type");

    updateTagToken(entity, updateType, addedTokens, removedTokens);
  }

  public onAttributeDragEnd() {
    const uiStateModel = this.getUiStateModel();
    uiStateModel.setProperty("/measures/attributeToMeasureDisallowed", false);
  }

  public onChangeChildElm(event) {
    const entity = (this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel).getData();
    const model = entity.resource.model;
    const selectedItem = event.getParameter("selectedItem");
    const originalChildElement = model.resource.getObject(selectedItem.data("objectId"));
    const hierarchy = entity.hierarchies?.get(0);
    const sItemId = event.getSource().getId();
    const sItemIndex = sItemId.charAt(sItemId.length - 1);
    const self = this;
    if (hierarchy) {
      model.resource.applyUndoableAction(function () {
        const childEle = hierarchy?.childElement?.get(Number(sItemIndex));
        childEle && hierarchy?.childElement?.removeAt(Number(sItemIndex));
        hierarchy?.childElement?.insert(Number(sItemIndex), originalChildElement);
        const parentChildModel = self.getParentChildModel(hierarchy);
        self.getView().getModel("hierarchyUiModel").setProperty("/parentElements", parentChildModel);
        self.getView().getModel("hierarchyUiModel").setProperty("/childElements", parentChildModel);
        if (parentChildModel?.length <= 1) {
          self.getView().getModel("hierarchyUiModel").setProperty("/listItemDltMode", undefined);
        } else {
          self.getView().getModel("hierarchyUiModel").setProperty("/listItemDltMode", "Delete");
        }
      }, "Change Hierarchy Child Element");
    }
  }

  public onChangeParentElm(event) {
    const entity = (this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel).getData();
    const model = entity.resource.model;
    const selectedItem = event.getParameter("selectedItem");
    const originalParentElement = model.resource.getObject(selectedItem.data("objectId"));
    const hierarchy = entity.hierarchies?.get(0);
    const sItemId = event.getSource().getId();
    const sItemIndex = sItemId.charAt(sItemId.length - 1);
    const self = this;
    if (hierarchy) {
      model.resource.applyUndoableAction(function () {
        const parentEle = hierarchy?.parentElement?.get(Number(sItemIndex));
        parentEle && hierarchy?.parentElement?.removeAt(Number(sItemIndex));
        hierarchy?.parentElement?.insert(Number(sItemIndex), originalParentElement);
        const parentChildModel = self.getParentChildModel(hierarchy);
        self.getView().getModel("hierarchyUiModel").setProperty("/parentElements", parentChildModel);
        self.getView().getModel("hierarchyUiModel").setProperty("/childElements", parentChildModel);
        if (parentChildModel?.length <= 1) {
          self.getView().getModel("hierarchyUiModel").setProperty("/listItemDltMode", undefined);
        } else {
          self.getView().getModel("hierarchyUiModel").setProperty("/listItemDltMode", "Delete");
        }
      }, "Change Hierarchy parent Element");
    }
  }

  public onDragStart(oEvent) {
    const oDraggedRow = oEvent.getParameter("target");
    const oDragSession = oEvent.getParameter("dragSession");
    // Set the rowcontext during drag because when scroll the table binding context changes
    oDragSession.setComplexData("draggedRowContext", oDraggedRow.getBindingContext("galileiModel"));
  }

  public onChangeAttributeToMeasurePress() {
    if (this.isMeasureTableDropAllowed()) {
      const selectedElements = this.getSelectedElements(this.getAttributesTable());
      this.changeSelectedElementsSemanticElementType(SemanticElementType.MEASURE, selectedElements);
    }
  }

  public onChangeMeasureToAttributePress() {
    const selectedElements = this.getSelectedElements(this.getMeasuresTable());
    this.changeSelectedElementsSemanticElementType(SemanticElementType.DIMENSION, selectedElements);
  }

  public onAttributesTableDrop(
    event: IEvent<
      sap.ui.core.dnd.DropInfo,
      {
        dragSession: sap.ui.core.dnd.DragSession;
        draggedControl: sap.ui.table.Row;
        droppedControl: sap.ui.table.Row;
        dropPosition: "Before" | "On" | "After";
      }
    >
  ) {
    const dragSession = event.getParameter("dragSession");
    const draggedBindingContext = dragSession.getComplexData("draggedRowContext");
    const draggedElement = draggedBindingContext.getProperty("");
    const droppedElement =
      event.getParameter("droppedControl") &&
      event.getParameter("droppedControl").getBindingContext("galileiModel") &&
      event.getParameter("droppedControl").getBindingContext("galileiModel").getProperty("");
    const dropPosition = event.getParameter("dropPosition");

    this.getGalileiModel()
      .getData()
      .resource.applyUndoableAction(() => {
        if (draggedElement && droppedElement) {
          this.reorderElement(draggedElement, droppedElement, dropPosition);
        }

        if (draggedElement && draggedElement.isMeasure) {
          this.changeSelectedElementsSemanticElementType(SemanticElementType.DIMENSION, [draggedElement]);
        }
      });
  }

  public onMeasuresTableDrop(
    event: IEvent<
      sap.ui.core.dnd.DropInfo,
      {
        dragSession: sap.ui.core.dnd.DragSession;
        draggedControl: sap.ui.table.Row;
        droppedControl: sap.ui.table.Row;
        dropPosition: "Before" | "On" | "After";
      }
    >
  ) {
    const dragSession = event.getParameter("dragSession");
    const draggedBindingContext = dragSession.getComplexData("draggedRowContext");
    const draggedElement = draggedBindingContext.getProperty("");
    const droppedElement =
      event.getParameter("droppedControl") &&
      event.getParameter("droppedControl").getBindingContext("galileiModel") &&
      event.getParameter("droppedControl").getBindingContext("galileiModel").getProperty("");
    const dropPosition = event.getParameter("dropPosition");

    this.getGalileiModel()
      .getData()
      .resource.applyUndoableAction(() => {
        if (draggedElement && droppedElement) {
          this.reorderElement(draggedElement, droppedElement, dropPosition);
        }

        if (draggedElement && !draggedElement.isMeasure) {
          this.changeSelectedElementsSemanticElementType(SemanticElementType.MEASURE, [draggedElement]);
        }
      });
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public reorderElement(reorderElement, referenceElement, position: "Before" | "On" | "After") {
    if (reorderElement === referenceElement) {
      return;
    }
    sap.cdw.querybuilder.NodeImpl.reorderElementByDrop(
      reorderElement.container,
      reorderElement,
      referenceElement,
      position
    );
  }

  public elementsTitleFormatter(dataCategory: DataCategory, elements: any[]) {
    const erdResourceBundle = this.oResourceModel.getResourceBundle();
    const tableResourceBundle = this.tableResourceModel.getResourceBundle();

    const amount = elements ? elements.length : 0;

    switch (dataCategory) {
      case DataCategory.DIMENSION:
      case DataCategory.HIERARCHY:
      case DataCategory.HIERARCHY_WITH_DIRECTORY:
      case DataCategory.TEXT:
        return erdResourceBundle.getText("@attributesWithAmount", [amount + ""]);
      case DataCategory.FACT:
      case DataCategory.SQLFACT:
      case DataCategory.CUBE:
        return tableResourceBundle.getText("attributesAndMeasuresAmountTitle", [amount + ""]);
      case DataCategory.DATASET:
      default:
        return erdResourceBundle.getText("@columnsWithAmount", [amount + ""]);
    }
  }

  public changeToMeasureButtonEnabledFormatter(selection: boolean) {
    return selection && this.isMeasureTableDropAllowed();
  }

  public filterTitleFormatter(remoteFilter) {
    const length = remoteFilter.length ?? 0;
    return this.getI18nTableText("filterTitle", [length || 0]);
  }

  public filterCreateVisibleFormatter(remoteFilter, filterPanelVisible) {
    if (remoteFilter?.length > 0 || (filterPanelVisible !== undefined && filterPanelVisible === true)) {
      return false;
    }
    return true;
  }

  public createEnabledFormatter(canCreateorUpdateModel, adapter, location) {
    if (
      ((adapter === "ABAPAdapter" || adapter === "CloudDataIntegrationAdapter") && !this.isFilterAllowedForABAPCDI()) ||
      !this.isSDIbasedConnection(location)
    ) {
      return false;
    } else if (canCreateorUpdateModel) {
      return true;
    } else {
      return false;
    }
  }

  // Reused for new filter
  public onCreatePress() {
    const model: sap.galilei.ui5.GalileiModel = this?.getView()?.getModel(
      "galileiModel"
    ) as sap.galilei.ui5.GalileiModel;
    const table = this.getGalileiModel()?.getData();
    if (this.filterableColumns?.length > 0) {
      table.filterPanelVisible = true;
      this.createFilterRow(table);
    } else {
      const strip = this.getView().byId("msgNewStrip") as sap.m.MessageStrip;
      strip.setVisible(true);
      strip.setText(this.getI18nText("i18n_erd", "FILTER_NO_FILTER_COLUMN", []));
      model.setProperty("/filterPanelVisible", false);
    }
  }

  public getFilterableColumns() {
    const filterableCols = [];
    if (filterableCols.length === 0) {
      const entity = this.getGalileiModel().getData();
      entity?.orderedElements?.filter(function (element) {
        let isFilterableCol = false;
        if (sap.cdw.commonmodel.ObjectImpl.isCDIadapter(entity)) {
          if (sap.cdw.commonmodel.ObjectImpl.isFilterableColumn(element)) {
            isFilterableCol = true;
          }
        } else {
          if (SupportedFilterDataTypes.includes(element.dataType)) {
            isFilterableCol = true;
          }
        }
        if (isFilterableCol) {
          filterableCols.push({
            name: element?.name,
            label: element?.label,
            type: getItemType(element?.dataType),
            dataType: element?.dataType,
            possibleFilterOperations: element?.allowedFilterOperationsRef,
            nullable: element?.isNotNull,
            columnKey: element?.name,
            length: element?.length,
            precision: element?.precision,
            scale: element?.scale,
            maxLength: element?.length,
          });
        }
      });
      if (filterableCols.length > 0) {
        this.filterableColumns = filterableCols;
      }
    }
    return filterableCols;
  }

  // New filter methods
  public operatorEnabledFormatter(possibleOperations) {
    if (possibleOperations.length <= 1) {
      return false;
    }
    return true;
  }

  public filterListVisibleFormatter(filterPanelVisible, remoteFilter) {
    if (filterPanelVisible || remoteFilter?.length > 0) {
      return true;
    }
    return false;
  }

  public isFilterAllowedForABAPCDI() {
    const elements = this.getGalileiModel().getData().orderedElements;
    let isAllowed = false;
    for (let i = 0; i < elements.length; i++) {
      const elem = elements[i];
      if (elem?.allowedExpressions?.length > 0 || elem.filterEnabled === false) {
        isAllowed = true;
        break;
      }
    }
    return isAllowed;
  }

  public infoNewTextFormatter(adapter, location) {
    let message = "";
    if ((adapter === "ABAPAdapter" || adapter === "CloudDataIntegrationAdapter") && !this.isFilterAllowedForABAPCDI()) {
      message = this.getI18nTableText("@upgradedpagent");
    } else if (location !== undefined && !this.isSDIbasedConnection(location)) {
      message = this.getI18nText("i18n_erd", "FILTER_NOT_SUPPORTED_SDA");
    }
    return message;
  }

  public infoNewVisibleFormatter(canCreateOrUpdateModel, adapter, location) {
    if (
      canCreateOrUpdateModel &&
      (((adapter === "ABAPAdapter" || adapter === "CloudDataIntegrationAdapter") &&
        !this.isFilterAllowedForABAPCDI()) ||
        (location !== undefined && !this.isSDIbasedConnection(location)))
    ) {
      return true;
    } else if (adapter === undefined && location === undefined) {
      return false;
    }
    return false;
  }

  public partitionNoDataMsgStripVisibilityFormatter(hasData, contentOwner) {
    if (isBWPCEPushEnabled() && contentOwner === "BW") {
      return false;
    }
    return !!hasData;
  }

  /**
   * Handles deletion of row
   * @param event
   */
  handleDeleteRow(event) {
    const oModel = this.getView().getModel("galileiModel");
    const selectedListItem = event.getSource().getBindingContext("galileiModel").getObject();
    const index = this.getSelectedValueItemIndex(event);
    const filterObjects = oModel.getProperty("/remoteFilter");
    if (index === 0 && filterObjects && filterObjects.length === 1) {
      (oModel as sap.ui.model.json.JSONModel).getData().filterPanelVisible = false;
    }
    this.clearErrors(selectedListItem);
    filterObjects[index].deleteObject();
    this.getGalileiModel().getData().hasFilterChanged = true;
    oModel["updateBindings"](true);
    this.canDeployFilters();
    this.getGalileiModel().getData().validate();
  }

  /**
   * Gets selected value item index
   * @param event
   * @returns
   */
  getSelectedValueItemIndex(event) {
    let index = 0;
    if (event) {
      const model = event.getSource().getModel("galileiModel");
      const filters = model.getData().remoteFilter;
      const selectedValueItem = event.getSource().getBindingContext("galileiModel").getObject();
      index = filters.indexOf(selectedValueItem);
    }
    return index;
  }

  /**
   * Handler for filter column change
   * @param event
   */
  onFilterColumnChange(event) {
    const selectedListItem = event.getSource().getBindingContext("galileiModel").getObject();
    const selectedKey = event.getParameter("selectedItem").getKey();
    const filterColDetails = this.filterableColumns.filter((o) => o.name === selectedKey);
    if (selectedListItem) {
      selectedListItem.value1 = "";
      selectedListItem.value2 = "";
      selectedListItem.possibleFilterOperations = filterColDetails[0].possibleFilterOperations;
      selectedListItem.dataType = filterColDetails[0].dataType;
      selectedListItem.length = filterColDetails[0].length;
      selectedListItem.precision = filterColDetails[0].precision;
      selectedListItem.scale = filterColDetails[0].scale;
    }
    this.clearErrors(selectedListItem);
    this.getGalileiModel().getData().hasFilterChanged = true;
    this.validateFilters(selectedListItem);
  }

  public clearErrors(selectedItem) {
    if (selectedItem.lowValueState === "Error") {
      selectedItem.lowValueState = sap.ui.core.ValueState.None;
      selectedItem.lowValueStateText = "";
    }
    if (selectedItem.highValueState === "Error") {
      selectedItem.highValueState = sap.ui.core.ValueState.None;
      selectedItem.highValueStateText = "";
    }
  }

  public validateFilters(selectedItem?) {
    const remoteFilters = this.getGalileiModel().getData().remoteFilter;
    let hasErrors = false;
    if (selectedItem) {
      this.validateFilterUnique(selectedItem);
    }
    if (remoteFilters?.length > 0) {
      for (let i = 0; i < remoteFilters.length; i++) {
        const filter = remoteFilters.get(i);
        if (
          filter.highValueState === sap.ui.core.ValueState.Error ||
          filter.lowValueState === sap.ui.core.ValueState.Error
        ) {
          hasErrors = true;
          break;
        }
      }
    }
    if (this.getGalileiModel().getData().changeManagementInfo !== undefined) {
      this.getGalileiModel().getData().changeManagementInfo.filterHasErrors = hasErrors;
    } else {
      this.getGalileiModel().getData().changeManagementInfo = {
        filterHasErrors: hasErrors,
      };
    }

    this.canDeployFilters();
    this.getGalileiModel().getData().validate();
  }

  public validateFilterUnique(selectedListItem) {
    const remoteFilters = this.getGalileiModel().getData().remoteFilter;
    const isDuplicateExists = remoteFilters
      ?.toArray()
      .filter((o) => o?.generatedKey === selectedListItem?.generatedKey && o?.objectId !== selectedListItem?.objectId);
    const hasErrorAlready =
      selectedListItem.lowValueState === sap.ui.core.ValueState.Error ||
      selectedListItem.highValueState === sap.ui.core.ValueState.Error
        ? true
        : false;
    if (isDuplicateExists.length > 0) {
      selectedListItem.isDuplicated = true;
    } else {
      selectedListItem.isDuplicated = false;
    }
    if (selectedListItem.isDuplicated) {
      selectedListItem.lowValueState = sap.ui.core.ValueState.Error;
      selectedListItem.lowValueStateText = this.getI18nTableText("VAL_DUPLICATE_FILTER");
    } else if (!selectedListItem.isDuplicated && !hasErrorAlready) {
      selectedListItem.lowValueState = sap.ui.core.ValueState.None;
      selectedListItem.lowValueStateText = "";
    }
  }

  public canDeployFilters() {
    const replicationModel = this.getView().getModel("replicationModel");
    const type = replicationModel?.getProperty("/dataAccess");
    const model = this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel;
    const object = model.getData();
    const replicationType = replicationModel?.getProperty("/replicationType");
    if (
      (type === undefined || type === StorageType.REMOTE) &&
      (replicationType === undefined || replicationType === null)
    ) {
      this.getGalileiModel().getData().canDeploy = true;
    } else {
      this.getGalileiModel().getData().canDeploy = false;
    }
  }

  public handleAddFilterRow() {
    const entity = this.getGalileiModel().getData();
    this.createFilterRow(entity);
  }

  public createFilterRow(entity) {
    const oClass = sap.galilei.model.getClass("sap.cdw.commonmodel.RemoteTableFilterItemNew");
    const initialSelectedColumn = this.filterableColumns?.[0];
    if (oClass) {
      const oRemoteTableFilter = oClass.create(entity.resource, {
        operation: "",
        value1: "",
        value2: "",
        possibleFilterOperations: initialSelectedColumn.possibleFilterOperations,
        name: initialSelectedColumn.name,
        dataType: initialSelectedColumn.dataType,
        length: initialSelectedColumn.length,
        precision: initialSelectedColumn.precision,
        scale: initialSelectedColumn.scale,
        filterableColumns: this.filterableColumns,
      });
      this.getGalileiModel().getData().remoteFilter?.push(oRemoteTableFilter);
      this.validateFilters();
    }
    this.getGalileiModel().getData().hasFilterChanged = true;
  }

  onOperatorChange(event) {
    const selectedKey = event.getParameter("selectedItem").getKey();
    const selectedListItem = event.getSource().getBindingContext("galileiModel").getObject();
    selectedListItem.value1 = "";
    selectedListItem.value2 = "";
    if (selectedKey === "EQ") {
      selectedListItem.highValueState = sap.ui.core.ValueState.None;
      selectedListItem.highValueStateText = "";
    }
    selectedListItem.lowValueState = sap.ui.core.ValueState.None;
    selectedListItem.lowValueStateText = "";
    this.validateFilters(selectedListItem);
    this.getGalileiModel().getData().hasFilterChanged = true;
  }

  public onValueChange(oEvent, isHigh, isChange?) {
    const oInput = oEvent.getSource();
    const selectedListItem = oEvent.getSource().getBindingContext("galileiModel").getObject();
    if (isChange === false) {
      if (selectedListItem.dataType !== CDSDataType.DECIMAL) {
        this.validateFilterItem(oInput, selectedListItem, isHigh, isChange);
      }
    } else {
      this.validateFilterItem(oInput, selectedListItem, isHigh, isChange);
    }
    if (isChange) {
      this.validateFilters(selectedListItem);
    }
    this.getGalileiModel().getData().hasFilterChanged = true;
  }

  public validateFilterItem(inputField, selectedListItem, isHigh, isChange?, onReOpen?) {
    let value;
    if (onReOpen) {
      value = inputField;
    } else {
      value = inputField?.getValue();
    }
    const type = selectedListItem.dataType;
    let hasError, isLowHighValid;
    // Validate Empty Filter Values - check if needed?
    if (!value || value.length === 0) {
      this.getGalileiModel()
        .getData()
        .resource.applyUndoableAction(
          () => {
            if (!isHigh) {
              selectedListItem.lowValueState = sap.ui.core.ValueState.Error;
              selectedListItem.lowValueStateText = this.getI18nTableText("@emptyFilterValue");
            } else {
              selectedListItem.highValueState = sap.ui.core.ValueState.Error;
              selectedListItem.highValueStateText = this.getI18nTableText("@emptyFilterValue");
            }
          },
          "Validate Filter Item",
          true
        );
      hasError = true;
    }

    // Validate upper and lower bounds if operator is "Between"
    if (selectedListItem.operation === "BT") {
      let lowVal = !isHigh ? value : selectedListItem.value1;
      let highVal = isHigh ? value : selectedListItem.value2;
      if (type === CDSDataType.DATE || type === CDSDataType.DATETIME || type === CDSDataType.TIMESTAMP) {
        lowVal = new Date(lowVal);
        lowVal = Date.parse(lowVal);
        highVal = new Date(highVal);
        highVal = Date.parse(highVal);
      } else if (
        type !== CDSDataType.STRING &&
        type !== CDSDataType.DOUBLE &&
        type !== CDSDataType.DECIMAL &&
        type !== CDSDataType.DECIMAL_FLOAT
      ) {
        lowVal = parseInt(lowVal, 10);
        highVal = parseInt(highVal, 10);
      }
      if (type === CDSDataType.STRING || (!isNaN(lowVal) && !isNaN(highVal))) {
        if (lowVal >= highVal) {
          this.getGalileiModel()
            .getData()
            .resource.applyUndoableAction(
              () => {
                if (!isHigh) {
                  selectedListItem.lowValueState = sap.ui.core.ValueState.Error;
                  selectedListItem.lowValueStateText =
                    type !== CDSDataType.STRING
                      ? this.getI18nTableText("lowerbounderrormsg")
                      : this.getI18nTableText("lowerbounderrormsgforString", [lowVal, highVal]);
                } else {
                  selectedListItem.highValueState = sap.ui.core.ValueState.Error;
                  selectedListItem.highValueStateText =
                    type !== CDSDataType.STRING
                      ? this.getI18nTableText("higherbounderrormsg")
                      : this.getI18nTableText("higherbounderrormsgforString", [lowVal, highVal]);
                }
              },
              "Validate Filter Item",
              true
            );
          hasError = true;
        } else {
          isLowHighValid = true;
        }
      } else {
        this.getGalileiModel()
          .getData()
          .resource.applyUndoableAction(
            () => {
              if (isNaN(lowVal) && type !== CDSDataType.STRING) {
                selectedListItem.lowValueState = sap.ui.core.ValueState.Error;
                selectedListItem.lowValueStateText = this.getI18nTableText("@emptyFilterValue");
              } else if (isNaN(highVal) && type !== CDSDataType.STRING) {
                selectedListItem.highValueState = sap.ui.core.ValueState.Error;
                selectedListItem.highValueStateText = this.getI18nTableText("@emptyFilterValue");
              }
            },
            "Validate Filter Item",
            true
          );
        hasError = true;
      }
    }

    // ValidateBounds
    const validationMsg = this.validateBounds(selectedListItem, value, inputField, isChange);
    if (validationMsg) {
      this.getGalileiModel()
        .getData()
        .resource.applyUndoableAction(
          () => {
            if (!isHigh) {
              selectedListItem.lowValueState = sap.ui.core.ValueState.Error;
              selectedListItem.lowValueStateText = validationMsg;
            } else {
              selectedListItem.highValueState = sap.ui.core.ValueState.Error;
              selectedListItem.highValueStateText = validationMsg;
            }
          },
          "Validate Filter Item",
          true
        );

      hasError = true;
    }
    if (hasError && onReOpen) {
      this.validateFilters(selectedListItem);
    }
    if (!hasError) {
      this.getGalileiModel()
        .getData()
        .resource.applyUndoableAction(
          () => {
            if (!isHigh) {
              selectedListItem.lowValueState = sap.ui.core.ValueState.None;
              selectedListItem.lowValueStateText = "";
            } else {
              selectedListItem.highValueState = sap.ui.core.ValueState.None;
              selectedListItem.highValueStateText = "";
            }
          },
          "Validate Filter Item",
          true
        );
    }
    if (isLowHighValid && validationMsg === "") {
      let valid;
      this.getGalileiModel()
        .getData()
        .resource.applyUndoableAction(
          () => {
            if (!isHigh && selectedListItem.highValueState !== sap.ui.core.ValueState.None) {
              valid = this.validateBounds(selectedListItem, selectedListItem.value2);
            } else if (selectedListItem.lowValueState !== sap.ui.core.ValueState.None) {
              valid = this.validateBounds(selectedListItem, selectedListItem.value1);
            }
            if (!isHigh && valid === "") {
              selectedListItem.highValueState = sap.ui.core.ValueState.None;
              selectedListItem.highValueStateText = "";
            } else if (isHigh && valid === "") {
              selectedListItem.lowValueState = sap.ui.core.ValueState.None;
              selectedListItem.lowValueStateText = "";
            }
          },
          "Validate Filter Item",
          true
        );
    }
  }

  /**
   * Low value visibility formatter
   * @param datatype
   * @returns
   */
  public lowGenericVisibilityFormatter(dataType) {
    let isVisible = true;
    if (
      dataType &&
      [CDSDataType.DATE, CDSDataType.DATETIME, CDSDataType.TIME, CDSDataType.TIMESTAMP].includes(dataType)
    ) {
      isVisible = false;
    }
    return isVisible;
  }

  /**
   * High value visibility formatter
   * @param datatype
   * @returns
   */
  public highGenericVisibilityFormatter(operation, dataType) {
    let isVisible = false;
    if (
      operation === "BT" &&
      dataType &&
      ![CDSDataType.DATE, CDSDataType.DATETIME, CDSDataType.TIME, CDSDataType.TIMESTAMP].includes(dataType)
    ) {
      isVisible = true;
    }
    return isVisible;
  }

  public highDateInputVisibilityFormatter(operation, dataType) {
    let isVisible = false;
    if (operation === "BT" && dataType && dataType === CDSDataType.DATE) {
      isVisible = true;
    }
    return isVisible;
  }
  public highDateTimeVisibilityFormatter(operation, dataType) {
    let isVisible = false;
    if (operation === "BT" && dataType && (dataType === CDSDataType.DATETIME || dataType === CDSDataType.TIMESTAMP)) {
      isVisible = true;
    }
    return isVisible;
  }

  public highTimeVisibilityFormatter(operation, dataType) {
    let isVisible = false;
    if (operation === "BT" && dataType && dataType === CDSDataType.TIME) {
      isVisible = true;
    }
    return isVisible;
  }

  public widthFormatter(operation) {
    if (operation === "BT") {
      return "10rem";
    }
    return "20.5rem";
  }

  private validateBounds(selectedItem: any, defaultVal: any, oControl?, isChange?) {
    let message = "";
    const name = selectedItem.name;
    const type = selectedItem.dataType;
    switch (type) {
      case CDSDataType.STRING:
      case CDSDataType.LARGE_STRING:
      case CDSDataType.HANA_NCHAR:
        if (defaultVal.length > selectedItem.length) {
          message = this.getI18nTableText("VAL_LENGTH_EXCEED", selectedItem.length);
        }
        break;
      case CDSDataType.HANA_TINYINT:
        if (this.checkDefaultInteger(defaultVal)) {
          if (!(Number(defaultVal) >= 0 && Number(defaultVal) <= 255)) {
            message = this.getI18nTableText("VAL_DEFAULT_RANGE_EXCEED_TINYINT", [name]);
          }
        } else {
          message = this.getI18nTableText("VAL_ENTER_VALID_INT");
        }
        break;
      case CDSDataType.HANA_SMALLINT:
        if (this.checkDefaultInteger(defaultVal)) {
          if (!(Number(defaultVal) >= -32768 && Number(defaultVal) <= 32767)) {
            message = this.getI18nTableText("VAL_DEFAULT_RANGE_EXCEED_SMALLINT", [name]);
          }
        } else {
          message = this.getI18nTableText("VAL_ENTER_VALID_INT");
        }
        break;
      case CDSDataType.INTEGER:
        if (this.checkDefaultInteger(defaultVal)) {
          if (!(Number(defaultVal) >= -2147483648 && Number(defaultVal) <= 2147483647)) {
            message = this.getI18nTableText("VAL_DEFAULT_RANGE_EXCEED_INT", [name]);
          }
        } else {
          message = this.getI18nTableText("VAL_ENTER_VALID_INT");
        }
        break;
      case CDSDataType.INTEGER64:
        if (this.checkDefaultInteger(defaultVal)) {
          if (!(defaultVal >= -9223372036854775808 && defaultVal <= 9223372036854775807)) {
            message = this.getI18nTableText("VAL_DEFAULT_RANGE_EXCEED_BIGINT", [name]);
          }
        } else {
          message = this.getI18nTableText("VAL_ENTER_VALID_INT");
        }
        break;
      case CDSDataType.BINARY:
        break;
      case CDSDataType.DECIMAL:
        if (isChange) {
          if (defaultVal.toString().match(/^[+]?[0-9]\d*(\.\d+)?$/g) !== null) {
            if (defaultVal !== "") {
              const oObject = {
                dataType: CDSDataType.DECIMAL,
                precision: selectedItem.precision,
                scale: selectedItem.scale,
                default: "",
              };
              commonUtils.tableDecimalHandle(oObject, defaultVal, false);
              const convertedValue = oObject.default;
              oControl?.setValue(convertedValue);
              if (convertedValue.length - 1 > selectedItem.precision) {
                message = this.getI18nTableText("VAL_ENTER_VALID_DECIMAL", [
                  selectedItem.precision,
                  selectedItem.scale,
                ]);
              }
            }
          } else {
            message = this.getI18nTableText("VAL_ENTER_VALID_DECIMAL_VALUE");
          }
        }
        break;
      case CDSDataType.DATE:
      case CDSDataType.DATETIME:
      case CDSDataType.TIME:
      case CDSDataType.TIMESTAMP:
        if (isChange) {
          if (defaultVal.indexOf("-") === 0) {
            message = this.getI18nTableText("VAL_ENTER_VALID_DATE");
          }
        }
        break;
      default:
        if (isNaN(defaultVal)) {
          message = this.getI18nTableText("VAL_ENTER_VALID_NUMBER");
        }
        break;
    }
    return message;
  }

  public checkDefaultInteger(defaultVal: number) {
    if (defaultVal.toString().match(/^([+-]?[1-9]\d*|0)$/g) !== null) {
      return true;
    } else {
      return false;
    }
  }

  public getI18nTableText(text: string, params?: string[]) {
    const oBundle = (this.getView().getModel("i18n_table") as sap.ui.model.resource.ResourceModel).getResourceBundle();
    return oBundle.getText(text, params);
  }

  public isEnableCompoundKeyColumn(canCreateOrUpdateModel, dimensionElements, hierarchies, dataCategory) {
    const keyElements = dimensionElements.filter((dimensionElement) => dimensionElement.isKey);
    if (canCreateOrUpdateModel && keyElements?.length > 1) {
      return true;
    }
    return false;
  }

  // Projection methods -reused
  public excludeColumnBtnFormatter(canCreateOrUpdateModel, isRemote, location, projectionSupported?, isSelected?) {
    if (
      canCreateOrUpdateModel &&
      isRemote &&
      (this.isSDIbasedConnection(location) || projectionSupported) &&
      (isSelected === undefined || isSelected)
    ) {
      return true;
    }
    return false;
  }

  public async onExcludeColumns(oEvent) {
    const errorList = [];
    const oBundle = (this.getView().getModel("i18n_table") as sap.ui.model.resource.ResourceModel).getResourceBundle();
    const table = this.getAttributesTable();
    const selectedIndices = table.getSelectedIndices();
    this.setScreenBusy(true);
    let partitionData;
    if (!this.getGalileiModel().getData()?.isUserNotAuthorized) {
      partitionData = await this.getPartitionData();
    }
    /*  if (partitionData === null) {
       const header = (this.getView().getModel("header") as sap.ui.model.json.JSONModel).getData();
       errorList.push({ column: header.technicalName, error: oBundle.getText("partitionFetchErrorTxt") });
     } */
    for (const index of selectedIndices) {
      const context = table.getContextByIndex(index);
      const columnObject = context.getObject();
      if (this.isKeyColumn(columnObject)) {
        errorList.push({ column: columnObject.name, error: oBundle.getText("keyColErrorTxt") });
      }
      if (this.isColumnInAssociation(columnObject)) {
        errorList.push({ column: columnObject.name, error: oBundle.getText("associationColErrorTxt") });
      }
      if (partitionData !== null && partitionData !== undefined && partitionData.column === columnObject.name) {
        errorList.push({ column: columnObject.name, error: oBundle.getText("partitionColErrorTxt") });
      }
      const isColInFilter = this.isColumnInFilter(columnObject);
      if (isColInFilter) {
        errorList.push({ column: columnObject.name, error: oBundle.getText("filterColErrorTxt") });
      }
      const isColInHier = this.isColumnInHierarchy(columnObject);
      if (isColInHier) {
        errorList.push({ column: columnObject.name, error: oBundle.getText("hierColErrorTxt") });
      }
    }
    if (errorList.length === 0) {
      let hasChanged = false;
      for (const index of selectedIndices) {
        const columnObject = table.getContextByIndex(index)?.getObject();
        const foundIndex = this.filterableColumns?.findIndex((o) => o.name === columnObject.name);
        if (foundIndex !== -1) {
          hasChanged = true;
          this.filterableColumns.splice(foundIndex, 1);
        }
      }
      await this.onDeleteSelectedRows(this.getAttributesTable());
      if (hasChanged) {
        const model: sap.galilei.ui5.GalileiModel = this?.getView()?.getModel("galileiModel") as any;
        model?.updateBindings(true);
      }
      this.getGalileiModel().setProperty("/isProjection", true);
      this.canDeployFilters();
    } else {
      let errorMsg = oBundle.getText("deleteRemoteColErrorTxt") + "\n\n";
      errorList.forEach((err, index) => {
        let msg = index + 1 + ". ";
        msg += err.column + ": " + err.error;
        errorMsg += msg + "\n";
      });
      sap.m.MessageBox.error(errorMsg, {
        id: "colDelErrorDialog",
      });
    }
    this.setScreenBusy(false);
  }

  public async openAddRemoteTableColumnDialog(isRefresh: boolean): Promise<any> {
    // eslint-disable-next-line @typescript-eslint/no-misused-promises
    return new Promise<any>(async (resolve, reject) => {
      const galileiModel = this.getGalileiModel();
      const viewName = require("../view/RemoteTableColumns.view.xml");
      const bundleName = require("../i18n/i18n.properties");
      const i18nModel = new sap.ui.model.resource.ResourceModel({
        bundleName: bundleName,
      });
      const selectedItemsModel = new sap.ui.model.json.JSONModel({});
      const sDialogId = "addColumnsDialog";
      const view = sap.ui.view({
        type: sap.ui.core.mvc.ViewType.XML,
        viewName: viewName,
        id: "columnsListView",
      });
      view.setModel(i18nModel, "i18n");
      view.setModel(galileiModel, "galileiModel");
      view.setModel(selectedItemsModel, "selectedItemsModel");
      const spaceID = await this.getSpaceGUID();
      let colsList;
      try {
        colsList = await sap.cdw.tableEditor.ModelImpl.computeExcludeandNewColumnList(
          galileiModel.getData(),
          spaceID,
          galileiModel
        );
      } catch {
        colsList = {
          excludedList: [],
          newList: [],
        };
      }
      view.setModel(colsList.excludedList, "excludedList");
      view.setModel(colsList.newList, "newList");
      (view.getModel() as sap.ui.model.json.JSONModel).setProperty("/isRefreshFlow", isRefresh);
      const endButton = new sap.m.Button({
        text: isRefresh ? i18nModel.getProperty("ignoreText") : i18nModel.getProperty("canceltext"),
        press: () => {
          this.newRemoteColumnDialog.close();
          this.newRemoteColumnDialog.destroy();
          resolve({ excludedColList: [], newColsList: [] });
        },
      });
      const beginButton = new sap.m.Button({
        text: isRefresh ? i18nModel.getProperty("updateText") : i18nModel.getProperty("okText"),
        press: () => {
          const excludedColList = selectedItemsModel.getProperty("/addExcludeList") || [];
          const newColsList = selectedItemsModel.getProperty("/addNewColList") || [];
          const resp = { excludedColList, newColsList };
          this.newRemoteColumnDialog.close();
          this.newRemoteColumnDialog.destroy();
          resolve(resp);
        },
        type: sap.m.ButtonType.Emphasized,
      });
      this.newRemoteColumnDialog = new sap.m.Dialog({
        id: this.getView().createId(sDialogId),
        title: isRefresh ? i18nModel.getProperty("updateColumnText") : i18nModel.getProperty("addColumnText"),
        type: "Standard",
        content: view,
        contentWidth: "500px",
        contentHeight: "400px",
        buttons: [beginButton, endButton],
      });
      this.newRemoteColumnDialog.setEscapeHandler(() => {
        this.newRemoteColumnDialog.destroy();
        resolve({ excludedColList: [], newColsList: [] });
      });
      this.getView().addDependent(this.newRemoteColumnDialog);
      this.newRemoteColumnDialog.open();
    });
  }

  public async onAddRemoteTableColumn() {
    const galileiModel = this.getGalileiModel();
    this.setScreenBusy(true);
    const self = this;
    const columns = await this.openAddRemoteTableColumnDialog(false);
    const excludedColList = columns.excludedColList;
    const newColsList = columns.newColsList;
    galileiModel.setProperty("/isProjection", true);
    excludedColList.forEach((column) => {
      if (!this.isColumnPresent(column)) {
        galileiModel.getData().resource.applyUndoableAction(
          function () {
            galileiModel.getData().createElement(
              {
                label: column.label,
                name: column.name,
                dataType: column.type,
                nativeDataType: column[DataWarehouse.native_dataType],
                allowedExpressions: column[DataWarehouse.filter_allowed_Expressions],
                filterEnabled: column[DataWarehouse.filter_enabled],
                length: column.length ? column.length : 0,
                precision: column.precision ? column.precision : 0,
                scale: column.scale ? column.scale : 0,
                isKey: column.key ? column.key : false,
                isNotNull: column.notNull ? column.notNull : false,
                indexOrder: column.index,
              },
              column.index
            );
          },
          "Create Element",
          false
        );
        self.canDeployFilters();
      }
    });
    newColsList.forEach((column) => {
      if (!this.isColumnPresent(column)) {
        galileiModel.getData().resource.applyUndoableAction(
          function () {
            galileiModel.getData().createElement(
              {
                label: column.label,
                name: column.name,
                dataType: column.type,
                nativeDataType: column[DataWarehouse.native_dataType],
                allowedExpressions: column[DataWarehouse.filter_allowed_Expressions],
                filterEnabled: column[DataWarehouse.filter_enabled],
                length: column.length ? column.length : 0,
                precision: column.precision ? column.precision : 0,
                scale: column.scale ? column.scale : 0,
                isKey: column.key ? column.key : false,
                isNotNull: column.notNull ? column.notNull : false,
                indexOrder: column.index,
              },
              column.index
            );
            self.canDeployFilters();
          },
          "Create Element",
          false
        );
      }
    });
    this.setScreenBusy(false);
  }

  isColumnPresent(column): boolean {
    let isPresent = false;
    const galileiModel = this.getGalileiModel();
    const currentElements = galileiModel.getData().elements;
    currentElements.forEach((element) => {
      isPresent = element.name === column.name ? true : isPresent;
    });
    return isPresent;
  }

  public isKeyColumn(columnObject) {
    return columnObject.isKey;
  }

  public isColumnInAssociation(columnObject) {
    let isAssociated = false;
    const oGalieliModel = this.getGalileiModel().getData();
    const associationList = oGalieliModel.allAssociations;
    if (associationList && associationList.length > 0) {
      associationList.forEach((element) => {
        if (element.mappings) {
          element.mappings.forEach((map) => {
            if (map.ref) {
              map.ref.forEach((ref) => {
                if (ref === columnObject.name) {
                  isAssociated = true;
                }
              });
            } else if (map?.qualifiedClassName === "sap.cdw.commonmodel.ElementMapping") {
              if (map?.source?.name === columnObject.name || map?.target?.name === columnObject.name) {
                isAssociated = true;
              }
            }
          });
        }
      });
    }
    return isAssociated;
  }

  public async getPartitionData() {
    const sUrlStart = "partitioning/" + this.spaceName + "/remoteTables/" + encodeURIComponent(this.modelName);
    try {
      const oDetails = await ServiceCall.request<[]>({
        url: sUrlStart,
        type: HttpMethod.GET,
        contentType: ContentType.APPLICATION_JSON,
      });
      this.partitioningData = oDetails.data;
    } catch (error) {
      this.partitioningData = null;
    }
    return this.partitioningData;
  }

  public async getRemoteSourceInfo(spaceGUID) {
    const connectionName = this.getGalileiModel().getData().remote?.connection;
    let resp;
    const sUrlStart =
      "/repository/remotes?space_ids=" +
      spaceGUID +
      "&filters=name:" +
      encodeURIComponent(connectionName) +
      "&inSpaceManagement=true&details=remoteSourceName,adapter,location";
    try {
      const oDetails = await ServiceCall.request<[]>({
        url: sUrlStart,
        type: HttpMethod.GET,
        contentType: ContentType.APPLICATION_JSON,
      });
      resp = oDetails.data;
      this.getGalileiModel().setProperty("/location", resp?.results?.[0]?.location);
      this.getGalileiModel().setProperty("/adapter", resp?.results?.[0]?.adapter);
      this.getGalileiModel().setProperty("/remoteSourceName", resp?.results?.[0]?.remoteSourceName);
      const oDataBuilderWorkbenchController = (
        sap.ui.getCore().byId("shellMainContent---databuilderComponent---databuilderWorkbench") as sap.ui.core.mvc.View
      ).getController() as IWorkbenchController;
      oDataBuilderWorkbenchController.getToolbarModel().setData(this.getGalileiModel().getData());
    } catch (error) {
      Logger.logError(error);
    }
  }

  public isColumnInFilter(columnObject) {
    const oGalieliModel = this.getGalileiModel().getData();
    let isColumnInFilter = false;
    const remoteFilter = oGalieliModel.remoteFilter;
    if (remoteFilter && remoteFilter.length > 0) {
      for (let i = 0; i < remoteFilter.length; i++) {
        const filter = remoteFilter.get(i);
        if (filter.name === columnObject.name) {
          isColumnInFilter = true;
          break;
        }
      }
    }
    return isColumnInFilter;
  }

  public isColumnInHierarchy(columnObject) {
    const oGalieliModel = this.getGalileiModel().getData();
    let isColumnInHier = false;
    const allHierarchies = oGalieliModel.allHierarchies;
    if (allHierarchies && allHierarchies.length > 0) {
      allHierarchies.forEach((hier) => {
        if (hier.qualifiedClassName === CommonQualifiedClassNames.PARENT_CHILD_HIERARCHY) {
          const parent = hier.parentElement.get(0);
          const child = hier.childElement.get(0);
          if (parent.newName === columnObject.name || child.newName === columnObject.name) {
            isColumnInHier = true;
          }
        } else if (hier.qualifiedClassName === CommonQualifiedClassNames.LEVEL_BASED_HIERARCHY) {
          const orderedLevels = hier.orderedLevels;
          orderedLevels.forEach((level) => {
            const element = level.element;
            if (element.newName === columnObject.name) {
              isColumnInHier = true;
            }
          });
        }
      });
    }
    return isColumnInHier;
  }

  public isSDIbasedConnection(location) {
    return ["agent", "dpserver"].includes(location);
  }

  public associationMappingFormatter(mappings) {
    let mappingObj;
    let mappingText = "";
    if (mappings) {
      mappingObj = this.getMappingsObj(mappings);
      for (let i = 0; i < mappingObj?.length; i++) {
        if (i !== 0) {
          mappingText = mappingText + ", ";
        }
        if (i === 2) {
          break;
        }
        const sourceName =
          typeof mappingObj[i]?.source === "string"
            ? mappingObj[i]?.source
            : mappingObj[i]?.source?.label || mappingObj[i].source?.displayName || mappingObj[i].source?.name;
        const targetName =
          typeof mappingObj[i]?.target === "string"
            ? mappingObj[i]?.target
            : mappingObj[i]?.target?.label || mappingObj[i].target?.displayName || mappingObj[i].target?.name;
        mappingText = mappingText + sourceName + " = " + targetName;
      }
    }
    return mappingText + " ";
  }

  public async onTableAssociationEdit(oEvent) {
    const model: sap.galilei.ui5.GalileiModel = this.getView().getModel("galileiModel") as sap.galilei.ui5.GalileiModel;
    const bindingPath: string = (oEvent.getSource() as sap.m.Button).getBindingContext("galileiModel").getPath();
    let oSelectedElement = bindingPath && model.getProperty(bindingPath);
    if (oSelectedElement?.classDefinition === undefined) {
      let oTargetRepoObject;
      const associationsList = this.getAssociationsTable();
      associationsList?.setBusy(true);
      // Avoid to get all shared objects
      oTargetRepoObject = await getCurrentOrCrossSpaceObjectByName(
        this.spaceName,
        oSelectedElement.target,
        undefined,
        oSelectedElement
      );
      associationsList?.setBusy(false);
      if (oTargetRepoObject !== undefined) {
        oSelectedElement = sap.cdw.ermodeler.ModelImpl.processAssociations(
          model,
          undefined /* target*/,
          oTargetRepoObject,
          { isUnresolved: true, name: oSelectedElement.name },
          undefined,
          undefined,
          true /* isDisableUndo */
        );
      } else {
        const errorInfoText = this.getI18nText("i18n_erd", "TARGET_NOT_EXIST");
        sap.m.MessageToast.show(errorInfoText);
      }
    }
    model.setProperty("/associationSelected", true);
    if (oSelectedElement) {
      model.setProperty("/selectedAssociation", oSelectedElement);
      super.switchPage(Pages.DetailsPage);
    } else {
      model.setProperty("/selectedAssociation", "");
    }
  }

  public getMappingsObj(mappings) {
    let mappingObj;
    if (mappings?.length > 0) {
      if (mappings[0]?.qualifiedClassName === "sap.cdw.commonmodel.ElementMapping") {
        mappingObj = mappings;
      } else {
        const mappingInfo = getMappings({ on: mappings });
        mappingObj = mappingInfo.mappings;
      }
    }
    return mappingObj;
  }

  public addHierarchyAssociationEnableFormatter(allAssociations, isDimension, usage, hierarchies?) {
    return addHierarchyAssociationEnableFormatter(allAssociations, isDimension, usage, hierarchies);
  }

  public async associationTypeFormatter(isHierarchy, isText, isDimension, isHierarchyWithDirectory) {
    if (isText) {
      return this.getI18nText("i18n_erd", "@textAssociation");
    } else if (isHierarchy && isDimension) {
      return this.getI18nText("i18n_erd", "@hierarchyAssociation");
    } else if (isHierarchyWithDirectory && isDimension) {
      return this.getI18nText("i18n_erd", "@hierarchyWithDirectoryAssociation");
    } else {
      return this.getI18nText("i18n_erd", "@association");
    }
  }

  public targetEntityFormatter(target) {
    if (typeof target === "string") {
      return target;
    } else {
      return target.label;
    }
  }

  public async targetEntityTechnicalNameFormatter(target) {
    if (typeof target === "string") {
      const spaceName = await this.targetEntitySpaceNameFormatter(target, {} as any);
      if (spaceName !== undefined) {
        // cross space
        target = target.substring(target.indexOf(".") + 1);
      }
      return target; // Association is not solved
    } else {
      return target?.crossSpaceEntityName || target?.name; // Association is solved
    }
  }

  public async targetEntitySpaceNameFormatter(
    target,
    space = {
      businessName: undefined,
      entityName: undefined,
      entityBusinessName: undefined,
      entityDisplayName: undefined,
    }
  ) {
    let spaceName;
    if (typeof target === "string") {
      if (target.includes(".")) {
        // It may be cross space
        spaceName = target.split(".")[0];
        const entity = (this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel)?.getData();
        const model = entity?.resource?.model;
        spaceName = model?._spaces?.[spaceName] ? spaceName : undefined;
        space.businessName = model?._spaces?.[spaceName];
        space.entityName = target.substring(target.indexOf(".") + 1);
        space.entityBusinessName = model?._sharedObjects?.[space.entityName];
        space.entityDisplayName = NamingHelper.getDisplayName(space.entityName, space.entityBusinessName);
      }
    } else {
      // Association is solved
      spaceName = target?.crossSpaceName;
    }

    return spaceName;
  }

  public async targetEntityLinkTextFormatter(target) {
    const space = {
      businessName: undefined,
      entityName: undefined,
      entityBusinessName: undefined,
      entityDisplayName: undefined,
    };
    if (typeof target === "string") {
      let linkText;
      const spaceName = await this.targetEntitySpaceNameFormatter(target, space);
      if (spaceName) {
        // cross space
        linkText = await getLinkText({
          crossSpaceName: spaceName,
          crossSpaceBusinessName: space.businessName,
          isCrossSpace: true,
          displayName: space.entityDisplayName,
        });
      } else {
        const label = await this.targetEntityBusinessNameFormatter(target);
        const name = await this.targetEntityTechnicalNameFormatter(target);
        linkText = label;
        const entity = (this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel)?.getData();
        const objectNameDisplay =
          entity.rootContainer.objectNameDisplay || entity.resource?.model?.rootContainer?.objectNameDisplay;
        switch (objectNameDisplay) {
          case ObjectNameDisplay.businessName:
            linkText = label;
            break;
          default:
            linkText = name;
        }
      }
      return linkText;
    } else {
      return await getLinkText(target);
    }
  }

  public async targetEntityBusinessNameFormatter(target) {
    if (typeof target === "string") {
      const spaceName = await this.targetEntitySpaceNameFormatter(target, {} as any);
      if (spaceName !== undefined) {
        // cross space
        target = target.substring(target.indexOf(".") + 1);
        const entity = (this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel)?.getData();
        const model = entity?.resource?.model;
        const entityBusinessName = model?._sharedObjects?.[target];
        return entityBusinessName || target; // when Association is not solved
      }

      return await getBusinessName(this.spaceName, target); // When Association is not solved, need get business name, but actually no additional service call
    } else {
      return target?.label;
    }
  }

  public moreTextFormatter(mappings) {
    const mappingObj = this.getMappingsObj(mappings);
    if (mappingObj?.length > 2) {
      const length = mappingObj.length - 2;
      const moreText = this.getI18nText("i18n_erd", "txtMore", [length.toString()]);
      return moreText;
    }
  }

  public moreTextVisibleFormatter(mappings) {
    const mappingObj = this.getMappingsObj(mappings);
    if (mappingObj?.length > 2) {
      return true;
    }
    return false;
  }

  public changeToMeasureButtonTooltipFormatter(
    selection: boolean,
    noSelectionLabel: string,
    dropNotAllowedLabel: string,
    changeLabel: string
  ) {
    if (!selection) {
      return noSelectionLabel;
    }

    if (!this.isMeasureTableDropAllowed()) {
      return dropNotAllowedLabel;
    }

    return changeLabel;
  }

  public refreshTooltipFormatter(federationOnly) {
    if (federationOnly) {
      return this.localizeText("refreshfederated");
    }
    return this.localizeText("refreshReplication");
  }

  public clearUiState() {
    this.createUiStateModel(this.getGalileiModel());
    this.clearSelection();
  }

  public openDataTypePopover(event: IEvent<sap.m.Token, { id: string }>): void {
    openDataTypePopoverHelper(this, event);
  }

  public createUiStateModel(_galileiModel: sap.galilei.ui5.GalileiModel) {
    const uiStateModel = new sap.ui.model.json.JSONModel({
      attributes: {
        selection: false,
        search: "",
      },
      measures: {
        selection: false,
        search: "",
        attributeToMeasureDisallowed: false,
      },
      associations: {
        selection: false,
        search: "",
      },
    });

    this.getView().setModel(uiStateModel, "UiState");
    return uiStateModel;
  }

  public async setReplicationStatusModel(model: sap.ui.model.json.JSONModel) {
    const oGalieliModel = this.getGalileiModel().getData();
    oGalieliModel.resource.applyUndoableAction(
      function () {
        oGalieliModel.partitioningExists = model.getData().partitioningExists;
        oGalieliModel.statisticsExists = model.getData().statisticsExists;
        if (oGalieliModel.remoteSourceName === undefined) {
          oGalieliModel.remoteSourceName = model.getData().remoteSourceName;
        }
        if (oGalieliModel.location === undefined) {
          oGalieliModel.location = model.getData().location;
        }
        if (oGalieliModel.adapter === undefined) {
          oGalieliModel.adapter = model.getData().adapter;
        }
      },
      "Replication values set",
      true
    );
    const oReplicationModel = this.getView().getModel("replicationModel") as sap.ui.model.json.JSONModel;
    const featureFlags = sap.ui.getCore().getModel("featureflags");
    const user = User.getInstance();
    const displayName = user.getObjectNameDisplay();
    const oMonitoringDetails = model.getData();
    let connectionName = oMonitoringDetails?.connectionName;
    if (displayName === "businessName" && !!oMonitoringDetails?.businessNameConnection) {
      connectionName = oMonitoringDetails.businessNameConnection;
    }
    let scheduleInfo = (await this.getScheduleInfo(this.modelName)) as any;
    let scheduled = false;
    if (!!scheduleInfo && scheduleInfo?.[0]) {
      scheduled = true;
      oMonitoringDetails.nextSchedule = scheduleInfo?.[0]?.nextRun;
    }
    oReplicationModel.setProperty("/scheduled", scheduled);
    oReplicationModel.setProperty("/nextSchedule", oMonitoringDetails?.nextSchedule);
    oReplicationModel.setProperty("/scheduleActivationStatus", scheduleInfo?.[0]?.activationStatus);
    const extensionMetatada = require("../extensions.json");
    this.extensionPoint = ExtensionPoint.create(extensionMetatada);
    const exts = await this.extensionPoint.findRoute("remoteTableEditorReplication");
    if (exts !== undefined) {
      this.serviceUtil = exts.getExtension("service");
      this.remoteTableFormatter = exts.getExtension("formatter");
      if (oReplicationModel) {
        if (scheduled) {
          oReplicationModel.setProperty("/canCreateSchedule", false);
          oReplicationModel.setProperty("/canEditSchedule", true);
          oReplicationModel.setProperty("/canDeleteSchedule", true);
        } else {
          oReplicationModel.setProperty("/canCreateSchedule", true);
          oReplicationModel.setProperty("/canEditSchedule", false);
          oReplicationModel.setProperty("/canDeleteSchedule", false);
        }
        oReplicationModel.setProperty("/connectionName", connectionName);
        oReplicationModel.setProperty("/adapter", oMonitoringDetails?.adapter);
        oReplicationModel.setProperty("/location", oMonitoringDetails?.location);

        oGalieliModel.deployedAdapter = oMonitoringDetails?.adapter;
        oGalieliModel.deployedLocation = oMonitoringDetails?.location;
        oGalieliModel.deployedConnection = connectionName;
        oGalieliModel.deployedTable = oGalieliModel.remote?.table;

        oReplicationModel.setProperty("/replicationState", oMonitoringDetails?.replicationState);
        oReplicationModel.setProperty("/replicationType", oMonitoringDetails?.replicationType);
        oReplicationModel.setProperty("/taskState", oMonitoringDetails?.taskState);
        oReplicationModel.setProperty("/replicationError", oMonitoringDetails?.replicationError);
        oReplicationModel.setProperty("/latestUpdate", oMonitoringDetails?.latestUpdate);
        oReplicationModel.setProperty("/dataAccess", oMonitoringDetails?.dataAccess);
        oReplicationModel.setProperty("/federationOnly", oMonitoringDetails?.federationOnly);
        oReplicationModel.setProperty("/hasReplica", oMonitoringDetails?.hasReplica);
        oReplicationModel.setProperty("/hasSourceSharing", oMonitoringDetails?.hasSourceSharing);
        oReplicationModel.setProperty("/hasSourceSharingWithReplica", oMonitoringDetails?.hasSourceSharingWithReplica);
        oReplicationModel.setProperty("/errorDetails", oMonitoringDetails?.errorDetails);
        oReplicationModel.setProperty("/tableName", this.modelName);
        oReplicationModel.setProperty("/spaceName", this.spaceName);
        oReplicationModel.setProperty("/remoteSource", oMonitoringDetails?.remoteSourceName);
        oReplicationModel.setProperty("/remoteSourceName", oMonitoringDetails?.remoteSourceName);
        oReplicationModel.setProperty("/projectionSupported", oMonitoringDetails?.projectionSupported);
        oReplicationModel.setProperty("/statisticsType", oMonitoringDetails?.statisticsType);
        oReplicationModel.setProperty("/statisticsLatestUpdate", oMonitoringDetails?.statisticsLatestUpdate);
        oReplicationModel.setProperty("/usedInTaskChain", oMonitoringDetails?.usedInTaskChain);
        oReplicationModel.setProperty("/subscriptionPrefix", oMonitoringDetails?.subscriptionPrefix);
        oReplicationModel.setProperty("/subscriptionRemote", oMonitoringDetails?.subscriptionRemote);
        oReplicationModel.setProperty("/subscriptionState", oMonitoringDetails?.subscriptionState);
        oReplicationModel.setProperty("/replicationStatus", oMonitoringDetails?.replicationStatus);
        oReplicationModel.setProperty("/connectionPaused", oMonitoringDetails?.connectionPaused);
        oReplicationModel.setProperty("/dpAgentState", oMonitoringDetails?.dpAgentState);
        const isParameterAbapCdsViews = sap.ui
          .getCore()
          .getModel("featureflags")
          .getProperty("/DWCO_REMOTE_TABLE_PARAMETER_SUPPORT_ABAP_CDS_VIEWS");
        if (isParameterAbapCdsViews) {
          oReplicationModel.setProperty(
            "/statisticsLimitedToRecordCount",
            oMonitoringDetails?.statisticsLimitedToRecordCount
          );
        }
        this.serviceUtil?.blueGreenMenuBtnStates(oMonitoringDetails, 0, undefined, scheduled, this.getView());
      }
    }
  }

  public getRefreshFrequencyText(scheduled, activationStatus) {
    if (scheduled) {
      if (activationStatus === "ENABLED") {
        return this.getI18nTableText("scheduled");
      } else if (activationStatus === "DISABLED") {
        return this.getI18nTableText("paused");
      }
    } else {
      return this.getI18nText("i18n_erd", "emptyCell");
    }
  }

  public replMessageVisibilityFormatter(isImprovementEnable, scheduled, activationStatus, usedInTaskChain, dataAccess) {
    if (
      isImprovementEnable &&
      ((scheduled && activationStatus === "ENABLED") || usedInTaskChain) &&
      dataAccess === "REALTIME_REPLICATION"
    ) {
      return true;
    }
    return false;
  }

  public getScheduleInfo(entityName) {
    const applicationId = ApplicationId.REMOTE_TABLES;
    return new Promise((resolve, reject) => {
      const sUrl = `tf/${this.spaceName}/schedules?applicationId=${applicationId}&objectId=${entityName}`;
      ServiceCall.request<any>({
        url: sUrl,
        type: HttpMethod.GET,
        contentType: ContentType.APPLICATION_JSON,
        dataType: DataType.JSON,
      }).then(
        (oResponse) => {
          const schedules = oResponse.data;
          resolve(schedules);
        },
        (error) => reject(error)
      );
    });
  }

  public enableDragAndDrop(entity) {
    const result = this.formatters.isCanChangeColumnsOrder(entity, Editor.TABLEEDITOR);
    return result;
  }

  private getControlsFromEvent(event) {
    const input = event.getSource() as sap.m.Input;
    const id = input.getId();
    const isTable = !id.includes("column");
    const iBusiness = !!id.includes("usinessName");
    const businessInput = iBusiness && input;
    let technicalInput: sap.m.Input = !iBusiness && input;

    let object;
    let technicalNameProperty;
    if (isTable) {
      // case of table
      object = this.getGalileiModel() && this.getGalileiModel().getData();
      if (!technicalInput) {
        technicalInput = this.getView().byId("technicalNameInput") as sap.m.Input;
      }
      technicalNameProperty = "technicalName";
    } else {
      // case of column
      const galileiModel = this.getGalileiModel();
      const sPath = event.getSource().getBindingContext("galileiModel").getPath();
      object = galileiModel.getProperty(sPath);
      if (!technicalInput) {
        const businessNameInputId = event.getParameter("id");
        const index = businessNameInputId.indexOf("clone");
        let technicalNameInputId;
        const busIndex = businessNameInputId.indexOf("columnBusinessName");
        const prefix = businessNameInputId.substring(0, businessNameInputId.indexOf("--"));
        const colTableIndex = Number(businessNameInputId.substring(index + 5)) + 1;
        if (busIndex > -1) {
          technicalNameInputId = `${prefix}--columnTechnicalName-__clone${colTableIndex}`;
        } else {
          technicalNameInputId = `${prefix}--columnmeasureTechnicalName-__clone${colTableIndex}`;
        }
        technicalInput = technicalNameInputId && (this.getView().byId(technicalNameInputId) as sap.m.Input);
      }
      technicalNameProperty = "newName";
    }
    return {
      isTableColumn: !isTable,
      object: object,
      businessInput: businessInput,
      technicalInput: technicalInput,
      technicalNameProperty: technicalNameProperty,
    };
  }

  private getSelectedElements(table): any[] {
    const selectedElements = [];
    const selectedIndices = table.getSelectedIndices();

    selectedIndices.forEach((j) => {
      const context = table.getContextByIndex(j);
      selectedElements.push(context.getModel().getProperty(context.getPath()));
    });
    return selectedElements;
  }

  private createDataTypeModel(isHierarchy?) {
    const dataTypeModel = getDSTypeModel(isHierarchy, super.getSpaceName());
    this.getView().setModel(dataTypeModel, "DSTypeModel");
    return dataTypeModel;
  }

  private getUiStateModel() {
    return this.getView().getModel("UiState") as sap.ui.model.json.JSONModel;
  }

  private addTagsMultiInputValidator() {
    const tagsMultiInput = this.getView().byId("tags") as sap.m.MultiInput;
    sap.ui.require(["sap/m/Token"], (Token) => {
      tagsMultiInput.addValidator((args) => new Token({ key: args.text, text: args.text }));
    });
  }

  private async deleteSelectedElementsOfTable(selectedElements: any[]) {
    const bSuccess = await this.getGalileiModel().getData().deleteSelectedElements(selectedElements);
    if (bSuccess) {
      this.clearSelection();
    }
  }

  private filterTable(table: sap.ui.table.Table, query: string) {
    let nameFilter;
    if (query.length > 0) {
      nameFilter = new sap.ui.model.Filter({
        filters: [
          new sap.ui.model.Filter({
            path: "name",
            operator: sap.ui.model.FilterOperator.Contains,
            value1: query,
          }),
          new sap.ui.model.Filter({
            path: "label",
            operator: sap.ui.model.FilterOperator.Contains,
            value1: query,
          }),
        ],
        and: false,
      });
    }
    (table.getBinding("rows") as sap.ui.model.json.JSONListBinding).filter(nameFilter ? [nameFilter] : []);
  }

  private isRowSelected(table: sap.ui.table.Table) {
    return table.getSelectedIndices().length > 0;
  }

  private getAttributesTable() {
    return this.getView().byId("attributesTable") as sap.ui.table.Table;
  }

  private getMeasuresTable() {
    return this.getView().byId("measuresTable") as sap.ui.table.Table;
  }

  private getAssociationsTable() {
    return this.getView().byId("associationsList") as sap.ui.table.Table;
  }

  private changeSelectedElementsSemanticElementType(semanticElementType: SemanticElementType, elements: any[]) {
    const changeToMeasure = semanticElementType === SemanticElementType.MEASURE;
    const entity = this.getGalileiModel().getData();

    if (changeToMeasure) {
      entity.changeAttributesToMeasures(elements);
    } else {
      entity.changeMeasuresToAttributes(elements);
    }
    this.clearSelection();
  }

  private isMeasureTableDropAllowed(draggedElement?: any) {
    const attributesTable = this.getAttributesTable();
    const resource = this.getGalileiModel().getProperty("/resource");

    let isDropAllowed = true;
    attributesTable
      .getSelectedIndices()
      .reverse()
      .forEach((index) => {
        const objectId = attributesTable.getContextByIndex(index).getProperty("objectId");
        const element = resource.getObject(objectId);
        const isElementForMeasure = getElementTypeForMeasure(element) === "measure";
        if (!isElementForMeasure && isDropAllowed) {
          isDropAllowed = false;
        }
      });

    if (draggedElement && isDropAllowed) {
      isDropAllowed = getElementTypeForMeasure(draggedElement) === "measure";
    }

    return isDropAllowed;
  }

  private getElementFromDragSession(dragSession: sap.ui.core.dnd.DragSession) {
    const draggedControl = dragSession.getDragControl() as sap.ui.table.Row;
    return (
      draggedControl.getBindingContext("galileiModel") &&
      draggedControl.getBindingContext("galileiModel").getProperty("")
    );
  }

  private clearSelection() {
    const measuresTable = this.getMeasuresTable();
    if (measuresTable) {
      measuresTable.clearSelection();
    }

    const attributesTable = this.getAttributesTable();
    if (attributesTable) {
      attributesTable.clearSelection();
    }

    const associationsTable = this.getAssociationsTable();
    if (associationsTable) {
      associationsTable.clearSelection();
    }
  }

  private async updateKeyColumn(oEvent) {
    const sourceElement = oEvent.getSource() as sap.m.CheckBox;
    const selectedElement = sourceElement.getBindingContext("galileiModel").getObject();
    const model = this.getGalileiModel();
    const galileiModel = model.getData();
    const representativeKeyElement = galileiModel?.representativeKey;
    if (selectedElement === representativeKeyElement && !sourceElement.getSelected()) {
      await this.removeConfirmDialog(selectedElement.name)
        .then(() => {
          this.updateKeyValue(selectedElement, sourceElement.getSelected());
          this.updateTableServiceModel();
        })
        .catch(() => {
          sourceElement.setSelected(true);
        });
    } else {
      this.updateKeyValue(selectedElement, sourceElement.getSelected());
      this.updateTableServiceModel();
    }
  }

  private updateKeyValue(selectedElement, isSelected) {
    const galileiModel = this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel;
    const model = galileiModel.getData().resource.model;
    model.resource.applyUndoableAction(function () {
      selectedElement?.set("isKey", isSelected);
    }, "Update key value");
  }

  private updateTableServiceModel() {
    // ####################
    //   Partitioning
    // ####################
    // determine element list
    const aElements = this.getView().getModel("galileiModel").getProperty("/dimensionElements");

    const compoundKeyButton = this.getView().byId("CompoundKeyButton") as sap.m.Button;
    const keyColumns = aElements?.filter((ele) => ele.isKey);
    if (keyColumns?.length > 1) {
      compoundKeyButton.setEnabled(true);
    } else {
      compoundKeyButton.setEnabled(false);
    }

    // filter to only have date types which are key
    const aModelElements = aElements
      .filter((oCurrentElement) => oCurrentElement.dataType === "cds.Date" && oCurrentElement.isKey === true)
      .map((oCurrentValue) => ({
        key: oCurrentValue.name,
        text: oCurrentValue.displayName,
      }));

    // add an empty element to the beginning
    aModelElements.unshift({
      key: undefined,
      text: "",
    });

    const oModel = new sap.ui.model.json.JSONModel({
      availablePartitioningFields: aModelElements,
    });

    this.getView().setModel(oModel, "tableServiceModel");
  }

  private removeConfirmDialog(columnName): Promise<any> {
    return new Promise<void>((resolve, reject) => {
      sap.m.MessageBox.show(this.localizeText("@removeRepresentativeKey", [columnName]), {
        id: "removeRepresentativeKey",
        icon: sap.m.MessageBox.Icon.WARNING,
        title: this.localizeText("@remove", []),
        actions: [sap.m.MessageBox.Action.OK, sap.m.MessageBox.Action.CLOSE],
        onClose: (action: sap.m.MessageBox.Action) => {
          if (action === sap.m.MessageBox.Action.OK) {
            resolve();
          } else if (action === sap.m.MessageBox.Action.CLOSE) {
            reject();
          }
        },
      });
    });
  }

  private updateNullProperty(oEvent) {
    const sourceElement = oEvent.getSource() as sap.m.CheckBox;
    const selectedElement = sourceElement.getBindingContext("galileiModel").getObject();
    commonUtils.updateTableNullProperty(sourceElement, selectedElement);
  }

  private stringInputVisibility(dataType: string) {
    const inputVisibility = commonUtils.stringInputVisibility(dataType);
    return inputVisibility;
  }

  private intInputVisibility(dataType: string) {
    const inputVisibility = commonUtils.intInputVisibility(dataType);
    return inputVisibility;
  }

  private decInputVisibility(dataType: string) {
    const inputVisibility = commonUtils.decInputVisibility(dataType);
    return inputVisibility;
  }

  private enableTextBox(
    dataType: string,
    isKey: boolean,
    writeAcess: boolean,
    isRemoteTable: boolean,
    isLocalSchema: boolean,
    isCrossSpace: boolean,
    isCDCColumn: boolean
  ) {
    const enabledInput = commonUtils.enableTextBox(dataType, isKey, isCDCColumn);
    if (enabledInput && writeAcess && isRemoteTable !== true && isLocalSchema !== true && isCrossSpace !== true) {
      return true;
    } else {
      return false;
    }
  }

  private onChangeBoolean(event: IEvent<sap.m.Select, {}>) {
    const select = event.getSource();
    const selectedElementKey = (event as any).getParameter("selectedItem").getKey();
    const currentElement = select.getBindingContext("galileiModel").getObject();
    commonUtils.tableBooleanHandle(currentElement, selectedElementKey);
  }

  private handleDateChange(event): void {
    commonUtils.tableDateTimeHandle(event);
  }

  private selectDateItem(event: sap.ui.base.Event): void {
    commonUtils.tableDateSelection(event);
  }

  private selectTimeItem(event: sap.ui.base.Event): void {
    commonUtils.tableTimeSelection(event);
  }

  private selectTimestampItem(event: sap.ui.base.Event): void {
    commonUtils.tableDateTimeSelection(event);
  }

  private dateSelectVisibility(defaultVal: string) {
    const dateVisible = commonUtils.dateSelectVisibility(defaultVal);
    return dateVisible;
  }

  private dateinputVisibility(defaultVal: string) {
    const dateinputVisible = commonUtils.dateinputVisibility(defaultVal);
    return dateinputVisible;
  }

  private toRemoteTableMonitorPress(): void {
    // record action for usage tracking
    ShellContainer.get().getUsageCollectionService().recordAction({
      action: "toRemoteTableMonitorPress",
      feature: "tableEditor",
      eventtype: "click",
    });

    const params = {
      spaceId: this.spaceName,
      objectId: this.modelName,
      routeTo: "remoteTableTaskLog",
    };
    ShellNavigationService.toExternal({
      target: {
        semanticObject: "dataintegration",
      },
      params,
    });
  }

  private toRemoteTableStatisticsMonitorPress(): void {
    // record action for usage tracking
    ShellContainer.get().getUsageCollectionService().recordAction({
      action: "GoToRemoteTableStatisticsMonitor",
      feature: "tableEditor",
      eventtype: "click",
    });

    const params = {
      spaceId: this.spaceName,
      objectId: this.modelName,
      routeTo: "remoteTableStatisticsMonitorLog",
    };
    ShellNavigationService.toExternal({
      target: {
        semanticObject: "dataintegration",
      },
      params,
    });
  }

  private getTextRepStatusRealTime(sReplicationStatus, sReplicationType, sTaskState) {
    const replicationStatus = this.remoteTableFormatter?.getTextRepStatusRealTime(
      sReplicationStatus,
      sReplicationType,
      sTaskState
    );
    return replicationStatus ? replicationStatus : this.getI18nText("i18n_erd", "emptyCell");
  }

  private getTextRepStatusRealTimeTooltip(sReplicationStatus, sReplicationType, sTaskState, sReplicationError) {
    return this.remoteTableFormatter?.getTextRepStatusRealTimeTooltip(
      sReplicationStatus,
      sReplicationType,
      sTaskState,
      sReplicationError
    );
  }

  private getSemanticColorValue(sReplicationStatus, sTaskState) {
    return this.remoteTableFormatter?.getSemanticColorValue(sReplicationStatus, sTaskState);
  }

  private formatDateAndTime(latestUpdate) {
    const formattedDate = this.formatDateTime(latestUpdate);
    return formattedDate ? formattedDate : this.getI18nText("i18n_erd", "emptyCell");
  }

  private getStatisticsType(type) {
    return type !== undefined ? type : this.getI18nText("i18n_erd", "emptyCell");
  }

  private getActiveStatus(sReplicationStatus: string, sReplicationType: string, sTaskState: string) {
    if (
      this.remoteTableFormatter?.getTextRepStatusRealTime(sReplicationStatus, sReplicationType, sTaskState) === "Error"
    ) {
      return true;
    }
    return false;
  }

  private handleErrorPopover(oEvent) {
    const src = oEvent.getSource();
    this.formatter = this.remoteTableFormatter;
    const remoteTableSelected = this.getView().getModel("replicationModel").getData();
    if (remoteTableSelected.replicationState === ReplicationStatus.ERROR && remoteTableSelected.errorDetails) {
      const errorFragment = this.remoteTableFormatter.getPopover();
      const errorDetailsPopover = sap.ui.xmlfragment("", errorFragment, this) as sap.m.Popover;
      errorDetailsPopover.setModel(this.remoteTableFormatter.getModelForPopOver(remoteTableSelected, true));
      this.getView().addDependent(errorDetailsPopover);
      errorDetailsPopover.openBy(src, true);
    }
  }

  private refreshFrequencyStateValue(scheduled, activationStatus) {
    if (scheduled && activationStatus === "ENABLED") {
      return "Success";
    }
    return "None";
  }

  private editorDataAccessFormatter(replicationModeldataAccess) {
    return this.remoteTableFormatter?.getDataAccessText(replicationModeldataAccess);
  }

  private forceStatusPackageSelectorTable(permissions: any, forceStatus, availableStatus) {
    if (!(permissions?.update || permissions?.create) || !availableStatus) {
      return "disabled";
    }
    return forceStatus ?? "byOthers";
  }

  async initScheduleDialog() {
    if (!this["newScheduleDialog"]) {
      this["newScheduleDialog"] = await getTaskScheduer("remoteTableEditorTaskScheduler");
    }
  }

  /**
   * Create schedule replication method
   */
  private async onCreateScheduleReplication(): Promise<void> {
    ShellContainer.get().getUsageCollectionService().recordAction({
      action: "onCreateScheduleReplication",
      feature: DWCFeature.DATA_BUILDER,
      eventtype: "click",
    });
    const oDataBuilderWorkbenchController = (
      sap.ui.getCore().byId("shellMainContent---databuilderComponent---databuilderWorkbench") as sap.ui.core.mvc.View
    ).getController() as IWorkbenchController;
    const oTableResourceBundle = (
      this.getView().getModel("i18n_table") as sap.ui.model.resource.ResourceModel
    ).getResourceBundle();
    oDataBuilderWorkbenchController.setBusy(true);
    const handleBackNavigation = () => {
      oDataBuilderWorkbenchController.setBusy(false);
      window.removeEventListener("popstate", handleBackNavigation); // Cleanup
    };
    window.addEventListener("popstate", handleBackNavigation);
    const data: ITaskScheduleRequest = {
      objectId: this.modelName,
      applicationId: ApplicationId.REMOTE_TABLES,
      activity: Activity.REPLICATE,
      description: oTableResourceBundle.getText("createScheduleReplicationDescription"),
      activationStatus: "ENABLED",
      dataAccess: this.getView()?.getModel("replicationModel")?.getProperty("/dataAccess"),
    };
    let scheduleDialog;
    if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
      recordAction(`createTaskSchedule: ${data.applicationId}`, "taskSchedule", "onCreate");
    } else {
      scheduleDialog = (
        this.getView().byId("remoteTableScheduleReplication") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    const oTaskResourceBundle = (
      this.getView().getModel("i18n_task") as sap.ui.model.resource.ResourceModel
    ).getResourceBundle();
    scheduleDialog.createTaskSchedule(
      data,
      this.spaceName,
      ApplicationId.REMOTE_TABLES,
      () => {
        if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
        }
        const msg = oTaskResourceBundle.getText("createScheduleSuccess");
        oDataBuilderWorkbenchController.setBusy(false);
        MessageHandler.success(msg);
        this.refreshReplicationModel();
      },
      (error) => {
        if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
          MessageHandler.exception({
            exception: error.error,
            message: error.message,
          });
        }
        oDataBuilderWorkbenchController.setBusy(false);
      },
      () => {
        oDataBuilderWorkbenchController.setBusy(false);
      }
    );
  }

  /**
   * Edit schedule replication method
   */
  private async onEditScheduleReplication(): Promise<void> {
    ShellContainer.get().getUsageCollectionService().recordAction({
      action: "onEditScheduleReplication",
      feature: DWCFeature.DATA_BUILDER,
      eventtype: "click",
    });
    const oDataBuilderWorkbenchController = (
      sap.ui.getCore().byId("shellMainContent---databuilderComponent---databuilderWorkbench") as sap.ui.core.mvc.View
    ).getController() as IWorkbenchController;
    const oTaskResourceBundle = (
      this.getView().getModel("i18n_task") as sap.ui.model.resource.ResourceModel
    ).getResourceBundle();
    const oTableResourceBundle = (
      this.getView().getModel("i18n_table") as sap.ui.model.resource.ResourceModel
    ).getResourceBundle();
    oDataBuilderWorkbenchController.setBusy(true);
    const data: ITaskScheduleRequest = {
      objectId: this.modelName,
      applicationId: ApplicationId.REMOTE_TABLES,
      activity: Activity.REPLICATE,
      description: oTableResourceBundle.getText("editScheduleReplicationDescription"),
      activationStatus: "ENABLED",
      dataAccess: this.getView()?.getModel("replicationModel")?.getProperty("/dataAccess"),
    };
    let scheduleDialog;
    if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
      recordAction(`editTaskSchedule: ${data.applicationId}`, "taskSchedule", "onEdit");
    } else {
      scheduleDialog = (
        this.getView().byId("remoteTableScheduleReplication") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    scheduleDialog.changeTaskSchedule(
      data,
      this.spaceName,
      ApplicationId.REMOTE_TABLES,
      () => {
        if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
        }
        const msg = oTaskResourceBundle.getText("updateScheduleSuccess");
        oDataBuilderWorkbenchController.setBusy(false);
        MessageHandler.success(msg);
        this.refreshReplicationModel();
      },
      (error) => {
        if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
          MessageHandler.exception({
            exception: error.error,
            message: error.message,
          });
        }
        oDataBuilderWorkbenchController.setBusy(false);
      },
      () => {
        oDataBuilderWorkbenchController.setBusy(false);
      }
    );
  }

  // Handle popover on click of view details
  public async handleReplicationChangeViewDetails(oEvent) {
    const source = oEvent.getSource();
    const entity = this.getView().getModel("galileiModel") as any;
    await this.setPropsForTable(entity);
    await this.serviceUtil.getReplicationMessage(source, this.getView(), entity.getData());
  }

  public async openImpactLineageDialog() {
    const entity = this.getView()?.getModel("galileiModel") as any;
    await this.setPropsForTable(entity);
    await this.serviceUtil.showImpactAnalysisDialog(entity.getData());
  }

  public async setPropsForTable(entity) {
    /*  const dirtyText = entity?.container?.isDirty() ? entity.container?.toolAdapter?.currentBreadcrumb?.text
    : "";
    entity.setProperty("/dirtyText",dirtyText); */
    const scheduled = this.getView().getModel("replicationModel")?.getProperty("/scheduled");
    let scheduleList;

    if (scheduled) {
      let scheduleDialog;
      if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
        await this.initScheduleDialog();
        scheduleDialog = this["newScheduleDialog"];
      } else {
        scheduleDialog = (
          this.getView().byId("remoteTableScheduleReplication") as sap.ui.core.mvc.View
        ).getController() as ITTaskScheduleController;
      }
      scheduleList = await scheduleDialog?.getTaskScheduleList(this.spaceName, ApplicationId.REMOTE_TABLES);
      const nextSchedule = this.getView().getModel("replicationModel")?.getProperty("/nextSchedule");
      const nextScheduleRun = this.remoteTableFormatter.formatNextSchedule(
        nextSchedule,
        true,
        entity.name,
        scheduleList
      );
      entity.setProperty("/nextSchedule", nextSchedule);
      entity.setProperty("/nextScheduleRun", nextScheduleRun);
    }

    entity.setProperty("/scheduled", scheduled);
    entity.setProperty(
      "/usedInTaskChain",
      this.getView().getModel("replicationModel")?.getProperty("/usedInTaskChain")
    );
    entity.setProperty("/spaceName", this.spaceName);
  }

  /**
   * Delete schedule replication method
   */
  private async onDeleteScheduleReplication(): Promise<void> {
    ShellContainer.get().getUsageCollectionService().recordAction({
      action: "onDeleteScheduleReplication",
      feature: DWCFeature.DATA_BUILDER,
      eventtype: "click",
    });
    const oDataBuilderWorkbenchController = (
      sap.ui.getCore().byId("shellMainContent---databuilderComponent---databuilderWorkbench") as sap.ui.core.mvc.View
    ).getController() as IWorkbenchController;
    const oTableResourceBundle = (
      this.getView().getModel("i18n_table") as sap.ui.model.resource.ResourceModel
    ).getResourceBundle();
    oDataBuilderWorkbenchController.setBusy(true);
    const data: ITaskScheduleRequest = {
      objectId: this.modelName,
      applicationId: ApplicationId.REMOTE_TABLES,
      activity: Activity.REPLICATE,
      description: oTableResourceBundle.getText("deleteScheduleReplicationDescription"),
      activationStatus: "ENABLED",
    };
    let scheduleDialog;
    if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
    } else {
      scheduleDialog = (
        this.getView().byId("remoteTableScheduleReplication") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    const oResourceBundle = (
      this.getView().getModel("i18n_task") as sap.ui.model.resource.ResourceModel
    ).getResourceBundle();
    scheduleDialog.deleteSchedule(
      data,
      this.spaceName,
      ApplicationId.REMOTE_TABLES,
      () => {
        const msg = oResourceBundle.getText("deleteScheduleSuccess");
        oDataBuilderWorkbenchController.setBusy(false);
        MessageHandler.success(msg);
        this.refreshReplicationModel();
      },
      () => {
        oDataBuilderWorkbenchController.setBusy(false);
      },
      () => {
        oDataBuilderWorkbenchController.setBusy(false);
      }
    );
  }

  /**
   * load replication new snapshot
   */
  private async loadNewSnapshot() {
    ShellContainer.get().getUsageCollectionService().recordAction({
      action: "loadNewSnapshot",
      feature: DWCFeature.DATA_BUILDER,
      eventtype: "click",
    });
    const displayName = await this.getDisplayName();
    const remoteTable = this.getView().getModel("replicationModel").getData();
    await this.serviceUtil.loadNewSnapshot(this.spaceName, this.modelName, this.getView(), displayName, remoteTable);
    this.refreshReplicationModel();
  }

  private async getDisplayName() {
    const user = await User.getInstance();
    const setting = user.getObjectNameDisplay();
    let displayName = this.modelName;
    if (setting == "businessName") {
      displayName = this.getGalileiModel()?.getData()?.label;
    }
    return displayName;
  }

  /**
   * Remove replicated Data
   */
  private async removeReplicatedData() {
    ShellContainer.get().getUsageCollectionService().recordAction({
      action: "removeReplicatedData",
      feature: DWCFeature.DATA_BUILDER,
      eventtype: "click",
    });
    const displayName = await this.getDisplayName();
    const remoteTable = this.getView().getModel("replicationModel").getData();
    await this.serviceUtil.removeReplicatedData(
      this.spaceName,
      this.modelName,
      this.getView(),
      displayName,
      remoteTable
    );
    this.refreshReplicationModel();
  }

  /**
   * Enable real time replication
   */
  private async enableRealTimeReplication() {
    ShellContainer.get().getUsageCollectionService().recordAction({
      action: "enableRealTimeReplication",
      feature: DWCFeature.DATA_BUILDER,
      eventtype: "click",
    });
    const displayName = await this.getDisplayName();
    const dataAccess = this.getView().getModel("replicationModel").getData().dataAccess;
    const usedInTaskChain = this.getView().getModel("replicationModel").getData().usedInTaskChain;
    const model = this.getView().getModel("replicationModel").getData();
    await this.serviceUtil.enableRealTimeReplication(
      this.spaceName,
      model,
      this.getView(),
      displayName,
      dataAccess,
      usedInTaskChain
    );
    this.refreshReplicationModel();
  }

  /**
   * Disable real time replication
   */
  private async disableRealTimeReplication() {
    ShellContainer.get().getUsageCollectionService().recordAction({
      action: "disableRealTimeReplication",
      feature: DWCFeature.DATA_BUILDER,
      eventtype: "click",
    });
    const displayName = await this.getDisplayName();
    const remoteTable = this.getView().getModel("replicationModel").getData();
    const isFvt = remoteTable.location === "indexserver";
    const warnSubscription = await RemoteTableServiceUtil.warnBeforeDropSubscription(remoteTable, this.getView());
    await this.serviceUtil.disableRealTimeReplication(
      this.spaceName,
      remoteTable,
      this.getView(),
      displayName,
      isFvt,
      warnSubscription
    );
    this.refreshReplicationModel();
  }

  /**
   * Refresh replication model on replication and scheduling actions
   */
  private async refreshReplicationStatus() {
    this.getView().setBusy(true);
    await this.refreshReplicationModel();
    MessageHandler.success(this.localizeText("refreshedSuccessfully"));
    this.getView().setBusy(false);
  }

  public async onDeltaCaptureChange(oEvent) {
    const entity = this.getGalileiModel().getData();
    let state;
    if (oEvent) {
      state = oEvent.getParameter("state");
    } else {
      if (entity?.fileStorage) {
        state = true;
      }
    }
    if (entity?.fileStorage) {
      let cdcColumnFlag = false;
      if (entity?.fileStorage && entity?.deploymentStatus !== 0) {
        cdcColumnFlag = true;
      }
      entity?.elements.forEach((element) => {
        if (element?.isCDCColumn) {
          cdcColumnFlag = true;
        }
      });
      if (!cdcColumnFlag) {
        this.onChangeStateCDCColumns(entity, state);
        cdcColumnFlag = true;
      }
    } else {
      this.onChangeStateCDCColumns(entity, state);
    }
    if (!state) {
      const cdcCols = this.getCDCColumns(entity.elements);
      await this.deleteSelectedElementsOfTable(cdcCols);
    }
  }

  public onDeltaOutboundChange(oEvent) {
    const entity = this.getGalileiModel().getData();
    let state;
    if (oEvent) {
      state = oEvent.getParameter("state");
      this.onChangeOfOutboundColumns(entity, state);
    }
    ShellContainer.get().getUsageCollectionService().recordAction({
      action: "DeltaOutbound",
      feature: DWCFeature.DATA_BUILDER,
      eventtype: EventType.CLICK,
    });
    this.getGalileiModel().updateBindings(true);
  }

  public deltaCaptureStateFormatter(deltaTable, fileStorage, contentOwner, isNew) {
    if (isBWPCEPushEnabled() && contentOwner === "BW" && deltaTable === undefined) {
      return false;
    }
    if ((fileStorage && isNew) || deltaTable?.type !== undefined) {
      return true;
    }
    return false;
  }

  public deltaCaptureEnableFormatter(deploymentDate, isLocal, fileStorage) {
    if (fileStorage) {
      return false;
    }
    const isVersioningReadOnly = this.getView().getModel("workbenchEnv").getProperty("/isVersioningReadOnlyMode");
    if (isVersioningReadOnly) {
      return false;
    }
    if (isLocal && !deploymentDate) {
      return true;
    }
    return false;
  }

  public deltaTableNameVisibilityFormatter(isLocal, isDeltaTable, isRemote, deltaReadOnly) {
    if (isDeltaReadAPIEnabled() || deltaReadOnly) {
      return false;
    }
    if ((isLocal || isRemote) && isDeltaTable) {
      return true;
    }
    return false;
  }

  public deltaCaptureVisibilityFormatter(isLocal, isDeltaTable, isRemote) {
    if (!isDeltaReadAPIEnabled() && (isLocal || (isRemote && isDeltaTable))) {
      return true;
    }
    return false;
  }

  public msgStripVisibilityFormatter(deploymentDate, isLTF) {
    if (deploymentDate || isLTF) {
      return false;
    }
    return true;
  }

  public deltaTableNameNewVisibilityFormatter(isLocal, isDeltaTable, isRemote, deltaReadOnly, contentOwner) {
    if (deltaReadOnly) {
      return false;
    }
    if ((isDeltaReadAPIEnabled() || isBWPCEPushEnabled()) && (isLocal || isRemote) && isDeltaTable) {
      return true;
    }
    return false;
  }

  public deltaCaptureNewVisibilityFormatter(isLocal, isDeltaTable, isRemote) {
    if (isDeltaReadAPIEnabled() && (isLocal || (isRemote && isDeltaTable))) {
      return true;
    }
    return false;
  }

  public deltaOutboundStateFormatter(deltaOutbound) {
    if (deltaOutbound) {
      return true;
    }
    return false;
  }

  public deltaOutboundVisibilityFormatter(isLocal, isDeltaTable, isRemote, isLTF, deltaReadOnly) {
    if (deltaReadOnly || isLTF) {
      return false;
    }
    if (isDeltaReadAPIEnabled() && (isLocal || isRemote) && isDeltaTable) {
      return true;
    }
    return false;
  }

  public deltaOutboundTableNameVisibilityFormatter(isDeltaTable, isDeltaOutboundOn) {
    if (isDeltaReadAPIEnabled() && isDeltaTable && isDeltaOutboundOn) {
      return true;
    }
    return false;
  }

  public DefaultValueColumnVisibilityFormatter(isRemote, isLTF) {
    if (isLTF) {
      return false;
    }
    if (isRemote) {
      return false;
    }
    return true;
  }

  public DefaultValueMeasureVisibilityFormatter(isLTF) {
    if (isLTF) {
      return false;
    }
    return true;
  }

  public showReadOnlyPopover(oEvent) {
    const oPopover = sap.ui.getCore().byId("readonlyPopover") as sap.m.Popover;
    if (oPopover && oPopover.getVisible()) {
      oPopover.close();
      return;
    }
    if (!oPopover) {
      const contentOwner = this.getGalileiModel().getData()?.contentOwner;
      let ownerText;
      if (contentOwner === "BW") {
        ownerText = ContentOwner.BW;
      }
      const text = this.getI18nTableText("readOnlyInfo", [ownerText]);
      const popover = new sap.m.Popover("readonlyPopover", {
        showHeader: false,
        content: [new sap.m.Text({ text: text }).addStyleClass("sapUiTinyMarginTop sapUiTinyMarginBeginEnd")],
      });
      popover.openBy(oEvent.getSource(), true);
      popover.attachAfterClose(function () {
        this.destroy();
      });
    }
  }

  private async refreshReplicationModel() {
    if (this.serviceUtil) {
      const sUrl =
        "monitor/" +
        this.spaceName +
        "/remoteTables/" +
        encodeURIComponent(this.modelName) +
        "?includeDiskSize=true&includeMemorySize=true&includeTaskStatus=true&includeProjectionInfo=true&includeFederationOnly=true";

      try {
        this.getView().setBusy(true);
        const oResponse: any = await ServiceCall.request<any>({
          url: sUrl,
          type: HttpMethod.GET,
          contentType: ContentType.APPLICATION_JSON,
        });
        const oMonitoringDetails = oResponse.data;
        let scheduled = false;
        const oReplicationModel = this.getView().getModel("replicationModel") as sap.ui.model.json.JSONModel;
        let scheduleInfo = (await this.getScheduleInfo(this.modelName)) as any;
        if (!!scheduleInfo && scheduleInfo?.[0]) {
          scheduled = true;
          oMonitoringDetails.nextSchedule = scheduleInfo?.[0]?.nextRun;
        }
        oReplicationModel.setProperty("/scheduled", scheduled);
        oReplicationModel.setProperty("/scheduleActivationStatus", scheduleInfo?.[0]?.activationStatus);
        if (scheduled) {
          oReplicationModel.setProperty("/canCreateSchedule", false);
          oReplicationModel.setProperty("/canEditSchedule", true);
          oReplicationModel.setProperty("/canDeleteSchedule", true);
        } else {
          oReplicationModel.setProperty("/canCreateSchedule", true);
          oReplicationModel.setProperty("/canEditSchedule", false);
          oReplicationModel.setProperty("/canDeleteSchedule", false);
        }
        oReplicationModel.setProperty("/nextSchedule", oMonitoringDetails?.nextSchedule);
        oReplicationModel.setProperty("/adapter", oMonitoringDetails?.adapter);
        oReplicationModel.setProperty("/replicationState", oMonitoringDetails?.replicationState);
        oReplicationModel.setProperty("/replicationType", oMonitoringDetails?.replicationType);
        oReplicationModel.setProperty("/taskState", oMonitoringDetails?.taskState);
        oReplicationModel.setProperty("/replicationError", oMonitoringDetails?.replicationError);
        oReplicationModel.setProperty("/latestUpdate", oMonitoringDetails?.latestUpdate);
        oReplicationModel.setProperty("/dataAccess", oMonitoringDetails?.dataAccess);
        oReplicationModel.setProperty("/hasReplica", oMonitoringDetails?.hasReplica);
        oReplicationModel.setProperty("/hasSourceSharing", oMonitoringDetails?.hasSourceSharing);
        oReplicationModel.setProperty("/hasSourceSharingWithReplica", oMonitoringDetails?.hasSourceSharingWithReplica);
        oReplicationModel.setProperty("/errorDetails", oMonitoringDetails?.errorDetails);
        oReplicationModel.setProperty("/tableName", this.modelName);
        oReplicationModel.setProperty("/spaceName", this.spaceName);
        oReplicationModel.setProperty("/statisticsType", oMonitoringDetails?.statisticsType);
        oReplicationModel.setProperty("/statisticsLatestUpdate", oMonitoringDetails?.statisticsLatestUpdate);
        oReplicationModel.setProperty("/federationOnly", oMonitoringDetails?.federationOnly);
        oReplicationModel.setProperty("/remoteSource", oMonitoringDetails?.remoteSourceName);
        oReplicationModel.setProperty("/remoteSourceName", oMonitoringDetails?.remoteSourceName);
        oReplicationModel.setProperty("/usedInTaskChain", oMonitoringDetails?.usedInTaskChain);
        oReplicationModel.setProperty("/subscriptionPrefix", oMonitoringDetails?.subscriptionPrefix);
        oReplicationModel.setProperty("/subscriptionRemote", oMonitoringDetails?.subscriptionRemote);
        oReplicationModel.setProperty("/subscriptionState", oMonitoringDetails?.subscriptionState);
        oReplicationModel.setProperty("/replicationStatus", oMonitoringDetails?.replicationStatus);
        oReplicationModel.setProperty("/connectionPaused", oMonitoringDetails?.connectionPaused);
        oReplicationModel.setProperty("/dpAgentState", oMonitoringDetails?.dpAgentState);
        this.serviceUtil.blueGreenMenuBtnStates(oMonitoringDetails, 0, undefined, scheduled, this.getView());
        if (
          oMonitoringDetails?.dataAccess === StorageType.REMOTE &&
          (oMonitoringDetails?.replicationType === undefined || oMonitoringDetails?.replicationType === null) &&
          !this.getGalileiModel()?.getData()?.canDeploy
        ) {
          this.getGalileiModel().getData().canDeploy = true;
        }
        this.getView().setBusy(false);
      } catch (err) {
        Logger.logError(err);
      }
    }
  }

  public navigateToAnalyticModel() {
    const dataBuilderWorkbench = this.getDatabuilderWorkbench();
    const technicalName = (this.getView().byId("technicalNameInput") as sap.m.Input).getValue();
    const oRouter = sap.ui.core.UIComponent.getRouterFor(dataBuilderWorkbench);
    const option = encodeURIComponent(`convertFromADS=${technicalName}`);
    const folderAssignment = dataBuilderWorkbench.getFolderAssignment();
    oRouter.navTo("editor", {
      spaceId: dataBuilderWorkbench.spaceId,
      model: "-newAnalyticModel",
      option,
      folderAssignment,
    });
  }

  /**
   * Reset section (dependent object list)
   */
  private resetDepObjSection() {
    const oModel = this.getGalileiModel();
    if (
      oModel &&
      typeof oModel.getProperty === "function" &&
      typeof oModel.getProperty("/dependentObjects") !== "undefined"
    ) {
      oModel.setProperty("/dependentObjects", undefined);
    }
  }

  public minVisibleRowCountParams(parameters) {
    return parameters?.length;
  }

  // public updateTableProperties(newRemoteSource, newRemoteTable){

  // }
  public async onRemoteTableSourceChange() {
    const connectionName = this.getGalileiModel().getData().remote?.connection;
    const remoteTableName = this.getGalileiModel().getData().remote?.table;
    const table = this.getGalileiModel().getData();
    const tableView = this.getView();
    const oParam = {
      spaceName: this.spaceName,
      spaceGUID: this.getSpaceGUID(),
      service: ImportRemoteService.getInstance(),
      spaceType: (this.getView() as any)?.getModel("replicationModel")?.getData().spaceType,
      spaceStatus: (this.getView() as any)?.getModel("replicationModel")?.getData().spaceAccessInfo?.status,
      browserOptions: {
        editorCapabilities: [EditorCapabilities.REMOTES_CAPABILITIES_SDI],
        supportIntelligentLookup: false,
      },
      remoteSourceChange: true,
      connection: connectionName,
      remoteTable: remoteTableName,
      table: table,
      galileiModel: this.getGalileiModel(),
      replicationModel: this.getView().getModel("replicationModel"),
      tableView: tableView,
    };
    const wizardView = await sap.ui.core.mvc.View.create({
      type: sap.ui.core.mvc.ViewType.XML,
      id: "importWizardView",
      viewName: require("../../reuse/importremotetable/view/ImportRemoteTableWizard.view.xml"),
      viewData: oParam,
    });
    const controller = wizardView.getController() as any;
    controller.setService(oParam.service);
    controller.setSpaceName(oParam.spaceName);
    controller.setSpaceGUID(oParam.spaceGUID);
    controller.setSpaceType(oParam.spaceType);
    controller.setSpaceStatus(oParam.spaceStatus);
    controller.setBrowserOptions(oParam.browserOptions);
    controller.setRemoteSourceChange(oParam.remoteSourceChange);
    controller.setCurrentConnection(oParam?.connection);
    controller.setCurrentRemoteTable(oParam?.remoteTable);
    controller.setTable(oParam?.table);
    controller.setGalileiModel(oParam?.galileiModel);
    controller.setReplicationModel(oParam?.replicationModel);
    controller.setTableView(oParam?.tableView);
    const wizardDialog = wizardView.getContent()[0] as sap.m.Dialog;
    wizardDialog.attachBeforeOpen(controller.onDialogBeforeOpen, controller);
    wizardDialog.open();
  }

  public typeVisibilityFormatter(displayType) {
    if (displayType.includes("Integer")) {
      return "Number";
    } else {
      return "Text";
    }
  }

  public valueStateVisibilityFormatter(name) {
    return sap.ui.core.ValueState.None;
  }

  public getDefaultValueMaxLength(displayType) {
    const match = displayType.match(/^([^(]+)\(([^)]+)\)$/);
    let maxLength;
    if (match && match.length === 3) {
      maxLength = match[2].trim();
    }
    return maxLength;
  }

  public onDefaultValueChange(oEvent, displayType, paramName) {
    this.defaultValueErrorParams = [];
    const inputText = oEvent.getParameter("value");
    const inputId = oEvent.getParameter("id");
    const oInput = this.getView().byId(inputId) as sap.m.Input;
    const maxLength = this.getDefaultValueMaxLength(displayType);
    const oModel = this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel;
    if (inputText && inputText.length > Number(maxLength)) {
      oInput.setValueState(sap.ui.core.ValueState.Error);
      const erdResourceBundle = this.oResourceModel.getResourceBundle();
      const valueStateText = erdResourceBundle.getText("defaultValueText", maxLength);
      oModel.setProperty("/defaultValueError", valueStateText);
      this.defaultValueErrorParams.push(paramName);
      this.getGalileiModel().getData().hasDefaultValueError = true;
      this.getGalileiModel().getData().hasDefaultValueErrorParams = this.defaultValueErrorParams;
      this.getGalileiModel().getData().validate();
    } else {
      oInput.setValueState(sap.ui.core.ValueState.None);
      oModel.setProperty("/defaultValueError", "");
      this.getGalileiModel().getData().hasDefaultValueError = false;
      this.getGalileiModel().getData().validate();
    }
  }

  public defaultValueFormatter(isDelta, isRemote) {
    if (isDelta && isRemote) {
      return false;
    }
    return true;
  }

  public decimalInputVisibility(dataType) {
    if (decInputVisibility(dataType)) {
      return true;
    }
    return false;
  }

  public numericVisibilityFormatter(dataType) {
    if (intInputVisibility(dataType)) {
      return true;
    }
    return false;
  }

  public stringVisibilityFormatter(dataType) {
    if (stringInputVisibility(dataType)) {
      return true;
    }
    return false;
  }

  public validateDecimal(oEvent): any {
    const bindingContextObject = oEvent.getSource()?.getBindingContext("galileiModel")?.getObject();
    const type = bindingContextObject?.dataType;
    if (type === CDSDataType.DECIMAL) {
      const precision = bindingContextObject?.precision;
      const scale = bindingContextObject?.scale;
      if (precision && scale) {
        const errorText = (this.getView().getModel("i18n_erd") as sap.ui.model.resource.ResourceModel)
          .getResourceBundle()
          .getText("VAL_ENTER_VALID_DECIMAL_GEN", [precision, scale]);
        validateDecimalInput(oEvent, parseInt(precision, 10), parseInt(scale, 10), errorText);
      }
    }
  }

  public validateDecimalLiveChange(oEvent): any {
    const bindingContextObject = oEvent.getSource()?.getBindingContext("galileiModel")?.getObject();
    const type = bindingContextObject?.dataType;
    const paramName = bindingContextObject?.name;
    if (type === CDSDataType.DECIMAL) {
      const precision = bindingContextObject?.precision;
      const scale = bindingContextObject?.scale;
      if (precision && scale) {
        const errorText = (this.getView().getModel("i18n_erd") as sap.ui.model.resource.ResourceModel)
          .getResourceBundle()
          .getText("VAL_ENTER_VALID_DECIMAL_GEN", [precision, scale]);
        validateDecimalInput(oEvent, parseInt(precision, 10), parseInt(scale, 10), errorText, true);
      }
    }
    this.defaultValueErrorParams = [];
    const oControl = oEvent.getSource() as sap.m.Input;
    if (oControl.getValueState() === sap.ui.core.ValueState.Error) {
      this.defaultValueErrorParams.push(paramName);
      this.getGalileiModel().getData().hasDefaultValueError = true;
      this.getGalileiModel().getData().hasDefaultValueErrorParams = this.defaultValueErrorParams;
      this.getGalileiModel().getData().validate();
    } else {
      this.getGalileiModel().getData().hasDefaultValueError = false;
      this.getGalileiModel().getData().validate();
    }
  }

  public isKeyEnabledFormatter(
    isCDCColumn,
    isPartitionedColumn,
    hasData,
    fileStorage,
    noKey,
    availableStatus,
    isRemote,
    isNew
  ) {
    if (fileStorage && hasData && isNew && !isCDCColumn) {
      return false;
    }
    if (fileStorage && (hasData || isCDCColumn)) {
      return false;
    }
    if (!fileStorage && (isCDCColumn || isPartitionedColumn || noKey)) {
      return false;
    }
    return availableStatus && !isRemote;
  }

  public dataTypeButtonEnabledFormatter(
    isCDCColumn,
    isPartitionedColumn,
    hasData,
    fileStorage,
    availableStatus,
    isNew
  ) {
    if (fileStorage && hasData) {
      if (isNew && !isCDCColumn) {
        return true;
      }
      return false;
    }
    if (isCDCColumn || isPartitionedColumn) {
      return false;
    }
    return availableStatus;
  }

  public onStorageTypeChange(oEvnt) {
    this.getGalileiModel().getData().isPinToMemoryEnabled = !this.getGalileiModel().getData().isPinToMemoryEnabled;
  }

  public placeHolderVisibilityFormatter(dataType) {
    if (dataType === "Date") {
      return this.tableResourceModel.getResourceBundle().getText("@dateFormatFilter");
    } else if (dataType === "Time") {
      return this.tableResourceModel.getResourceBundle().getText("@timeFormatFilter");
    } else if (dataType === "Timestamp") {
      return this.tableResourceModel.getResourceBundle().getText("@dateTimeFormatFilterNew");
    } else if (dataType === "DateTime") {
      return this.tableResourceModel.getResourceBundle().getText("@dateTimeFormatFilterNew");
    } else {
      return dataType;
    }
  }

  public isNotNullEnabledFormatter(
    isCDCColumn,
    hasData,
    fileStorage,
    status,
    isKey,
    isRemote,
    isLocalSchema,
    isCrossSpace
  ) {
    if (fileStorage && hasData) {
      return false;
    }
    if (isCDCColumn) {
      return false;
    }
    return status && !isKey && !isRemote && !isLocalSchema && !isCrossSpace;
  }

  public labelColumnEnabledFormatter(isCDCColumn, fileStorage, hasData, status, canCreateOrUpdateModel) {
    if (fileStorage && hasData) {
      return false;
    }
    if (isCDCColumn) {
      return false;
    }
    return canCreateOrUpdateModel === true && status;
  }

  public storageTypeFormatter(isPinToMemoryEnabled) {
    return isPinToMemoryEnabled ? "2" : "1";
  }

  public createAnalyticModelLinkEnabledFormatter(canUndo, canCreateOrUpdateModel, spaceType, isVersionReadOnly) {
    if (
      UISpaceCapabilities.get().hasCapability(
        this.getView()?.getModel("workbenchEnv")?.getData()?.spaceData?.spaceId,
        "hdlfStorage"
      )
    ) {
      return false;
    }
    return !canUndo && canCreateOrUpdateModel === true && spaceType !== "abapbridge" && !isVersionReadOnly;
  }

  public deleteOldTextVisiblityFormatter(isLTF, isDeltaTable) {
    if (!isLTF && !isDeltaTable) {
      return true;
    }
    return false;
  }
  public deltaCaptureLabelFormatter(isFileStorage) {
    if (isFileStorage && !isBWPCEPushEnabled()) {
      return this["erdResourceBundle"].getText("requiredDeltaCaptureLabel");
    }
    return this["erdResourceBundle"].getText("lblDelta");
  }

  public booleanKeyFormatter(defaultValue) {
    if (defaultValue === true || defaultValue === "true") {
      return "true";
    } else if (defaultValue === false || defaultValue === "false") {
      return "false";
    }
    return "";
  }

  public validateBoolean(oEvent) {
    const bindingContextObject = oEvent.getSource()?.getBindingContext("galileiModel")?.getObject();
    const type = bindingContextObject?.dataType;
    if (type === CDSDataType.BOOLEAN) {
      const selectedKey = oEvent.getSource().getProperty("selectedKey");
      bindingContextObject.defaultValue = selectedKey; // we cannot use: selectedKey="{viewData>defaultValue}", because it will set other input controls to empty
    }
  }
  private ftDialogColumnOptionsGetter() {
    return new Promise((resolve) => {
      const object = this.getGalileiModel() && this.getGalileiModel().getData();
      const elements = object.elements.toArray();
      const result = [];
      elements.forEach((item) => {
        const isValidString =
          item.dataType === "cds.String" && item.length === 8 && item.semanticType === SemanticType.DATE;
        if (isValidString || item.dataType === "cds.Date") {
          result.push({
            key: item.newName || item.name,
            label: item.displayName,
          });
        }
      });
      resolve({
        startColList: result,
        endColList: result,
      });
    });
  }

  private onDimensionTypeSettingsClicked() {
    const galileiModel = this.getGalileiModel();
    const entity = galileiModel.getData();
    const resourceBundle = this.oResourceModel.getResourceBundle();
    const dialogTitle = resourceBundle.getText("VAL_FISCAL_DIALOG_TITLE");
    const dialogMsg = resourceBundle.getText("VAL_FISCAL_DIALOG_MESSAGE_STRIP");
    const dialogStartLabel = resourceBundle.getText("VAL_FISCAL_DIALOG_START_LABEL");
    const dialogEndLabel = resourceBundle.getText("VAL_FISCAL_DIALOG_END_LABEL");
    sap.ui.require(
      ["sap/skyline/tools/fiscaltimesettingsdialog/FiscalTimeSettingsDialog"],
      (FiscalTimeSettingsDialog: any) => {
        this.fiscalTimeSettingsDialog = sap.ui.getCore().byId("ftDialog");
        if (!this.fiscalTimeSettingsDialog) {
          this.fiscalTimeSettingsDialog = new FiscalTimeSettingsDialog("ftDialog", {
            dialogTitle: dialogTitle,
            messageStripContent: dialogMsg,
            columnsLabel: { begin: dialogStartLabel, end: dialogEndLabel },
            columnOptionsGetter: this.ftDialogColumnOptionsGetter.bind(this),
            selectedStartCol: entity.fiscalTimeSettingsStart?.newName || entity.fiscalTimeSettingsStart?.name,
            selectedEndCol: entity.fiscalTimeSettingsEnd?.newName || entity.fiscalTimeSettingsEnd?.name,
            cancel: () => {
              this.fiscalTimeSettingsDialog.close();
              this.fiscalTimeSettingsDialog.destroy();
            },
          } as any).addStyleClass("sapUiPopupWithPadding");
          this.fiscalTimeSettingsDialog.open();
        } else {
          this.fiscalTimeSettingsDialog.open();
        }
        this.fiscalTimeSettingsDialog.attachEvent("columnsSelect", function (params) {
          entity.fiscalTimeSettingsStart = findInCollectionByNewNameOrName(
            entity?.elements,
            params.getParameter("selectedStartCol")
          );
          entity.fiscalTimeSettingsEnd = findInCollectionByNewNameOrName(
            entity?.elements,
            params.getParameter("selectedEndCol")
          );
          this.destroy();
        });
      }
    );
  }

  private onChangeDimensionType(oEvent) {
    const dimensionType = oEvent.getSource().getSelectedKey();
    const galileiModel = this.getGalileiModel();
    const entity = galileiModel.getData();
    if (dimensionType !== DimensionType.FISCAL_TIME) {
      this.fiscalTimeSettingsDialog?.["clearSelection"]();
      entity.fiscalTimeSettingsStart = undefined;
      entity.fiscalTimeSettingsEnd = undefined;
    }
  }

  public dimensionTypeVisibleFormatter(semanticType) {
    const isFiscalTimeDimensionEnabled = SupportedFeaturesService.getInstance().isFiscalTimeDimensionEnabled();
    return isFiscalTimeDimensionEnabled && semanticType === DataCategory.DIMENSION;
  }
}

export const TableEditorController = smartExtend(
  CommonProperties,
  "sap.cdw.components.tableeditor.controller.TableEditor",
  TableEditorClass
);

sap.ui.define("sap/cdw/components/tableeditor/controller/TableEditor.controller", [], () => TableEditorController);
