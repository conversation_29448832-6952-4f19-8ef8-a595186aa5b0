#~~~~~~~~~~~ DataSuite Welcome Card ~~~~~~~~~~~~~~~~~~~
#XTIT: Title of the Welcome Card
welcomeCardTitle=SAP Datasphere 시작
#XMSG: Welcome Text shown after Title
dataSuiteCardText=SAP Datasphere는 조직 내 미션 크리티컬한 데이터를 고유하게 통합하는 비즈니스 데이터 패브릭 아키텍처를 지원하여 비즈니스 전문가로 하여금 가장 영향력있는 결정을 내릴 수 있도록 합니다. 이전에 분리되어 있던 기능을 통합 서비스로 결합하여, SAP 데이터와 타사 데이터를 아우르는 데이터 통합, 카탈로그 작성, 의미 구조에 따른 모델링, 데이터 웨어하우징, 워크로드 가상화를 실현합니다.
#XBUT Text of the Learn More Button
dataSuiteLearnMoreButtonText=자세한 정보

#XTXT: ARIA label for blog post cards within the SAP Datasphere Blogs card on the homepage.
#This ARIA label is read out loud by screenreaders for accessibility support. This is the only usage of the text, it is not visible anywhere.
# {0} will be replaced by the position (1 to 10) of the blog post cards, since we have in total 10 blog post cards.
newsfeedCardAriaLabel=블로그 게시물 {0}

#XTXT: ARIA label for the card title on the homepage.
# This ARIA label is read out loud by screenreaders for accessibility support. This is the only usage of the text, it is not visible anywhere.
# {0} will be replaced by the title of the card.
cardTitleAriaLabel=카드 {0}

#XTOL: Tooltip for avatar images in the newsfeed (is also used as an alternative text for screen readers or in case the image is unavailable)
newsfeedAvatarTooltip=피드 항목 작성자 아바타

#XTIT: Infomation popup for missing authorizations
MissingAuth=권한 누락

#XMSG: Information for missing authorizations
MissingAuthInfoText=지금은 이 기능에 액세스할 수 없는 것 같습니다. 관리자에게 문의하여 지원을 받으십시오.

#XMSG: Popup message when idp configuration was successfull
idpSuccessMessage=SAML 계정을 확인했습니다. 이 브라우저 윈도우를 닫고 설정 프로세스를 계속하십시오.

#XMSG: Text for manage settings
manageSettings=설정 관리

#XMSG: Popup message for manage settings
manageSettingsMessage=개인에게 맞춰진 경험을 제공하기 위해 사용자가 오브젝트를 검색하고 여는 시기를 기록합니다.\n\n언제든지 데이터를 지우거나 계정 설정 대화 상자에서 개인 설정을 해제할 수 있습니다.

#~~~~~~~~~~~ Texts for the SAP Datasphere Homepage ~~~~~~~~~~~~~~~~
#XTIT: Welcoming title for the homepage. {0} will be replaced with the first name of the user, {1} will be replaced with the last name of the user.
objectPageHeaderTitle={0} {1} 님, 안녕하십니까?
#XFLD: Label for switch to toggle auto refresh of the data in the homepage cards
autoRefresh=데이터 자동 새로 고침
#XMSG: Message toast text, for when the auto refresh switch is turned on and the immediate refresh of all currently visible cards' data is done.
dataRefreshDone=카드 새로 고침
#BUT: Button to customize the homepage
customize=커스터마이즈
#XFLD: Title for the placeholder in case no cards is selected and the homepage is empty
noCardsPlaceholderTitle=홈 커스터마이즈
#XFLD: Description for the placeholder in case no cards is selected and the homepage is empty
noCardsPlaceholderDescription=권한에 따라 카드 추가 시작
#XFLD: Sub-header for cards in unified homepage
recentlyStarted=최근 시작 - 상위 5개
#XFLD: Sub-header for Data Builder and Business Builder card if Activity Tracking is enabled
recentlyAccessed=최근 액세스 - 상위 5개
#XFLD: Sub-header for Data Builder and Business Builder card if Activity Tracking is disabled
recentlyChanged=최근 변경 - 상위 5개
#XFLD: Card header title for data integration tasks card
dataIntegrationTasksCardTitle=데이터 통합 태스크
#XFLD: Card header title for newsfeed/SAP Datasphere resources card
newsfeedCardTitle=SAP Datasphere 블로그
#XFLD: Card header subtitle for newsfeed/SAP Datasphere resources card
newsfeedCardSubtitle=최근 게시물 - 상위 10개
#XTIT: Title for "no data" screen in newsfeed/SAP Datasphere resources card
noBlogEntryTitle=최근 게시물 없음
#XTIT: Title for "no data" screen in newsfeed/SAP Datasphere resources card
noBlogEntryDescription=SAP 블로그를 검색하여 또 다른 흥미로운 게시물 찾아보기
#XTIT: Title for "no data" screen in data integration card
noRecentTasksTitle=최근 태스크 없음
#XTXT: Explanatory text for "no data" screen on data integration card
noRecentTasksText=해당 항목이 있을 경우, 여기에서 볼 수 있음
#XTIT: Title for the illustrated message in case of data load error on any card
dataFetchErrorTitle=데이터를 로드할 수 없습니다.
#XTIT: Title for the illustrated message in case of data load error on any card caused by the circuit breaker having a HanaState = red (unvailable)
circuitBreakerDataFetchErrorTitle=런타임 데이터베이스를 사용할 수 없습니다.
#XTIT: Title for the illustrated message during the tenant upgrade process
tenantUpgradeInProgressTitle=SAP Datasphere에 업데이트를 제공합니다.
#XFLD: Descriptions for the illustrated message in case of data load error on any card
dataFetchErrorDescription=이 문제가 지속될 경우 상관 관계 ID {0}을(를) 첨부하여 고객 지원에 문의하십시오.
dataFetchErrorDescriptionNoCorrelationId=이 문제가 지속될 경우 고객 지원에 문의하십시오.
#XFLD: Description for the illustrated message during the tenant upgrade process
tenantUpgradeInProgressDescription=잠시 후 {0}을(를) 클릭해 보십시오.
#XBTN: Button text for action on data load error
onErrorAction=다시 로드
#XFLD: Card header title for quick actions card
quickActionsCardTitle=빠른 액션
#XFLD: Tooltip to edit the selected quick actions
editQuickActionsTooltip=빠른 액션 편집
#XFLD: No Data Title for Quick Actions Card in case no Quick Action is selected
noQuickActionSelectedTitle=선택한 빠른 액션 없음
#XFLD: No Data Description for Quick Actions Card in case no Quick Action is selected
noQuickActionSelectedDescription=빠른 액션을 추가하여 여기에 표시
#XFLD: No Data Button Text for Quick Actions Card in case no Quick Action is selected
noQuickActionSelectedButton=빠른 액션 추가
#XFLD: Title for the dialog to edit the selected quick actions
editQuickActionsDialogTitle=빠른 액션 편집
#XTIT: Header text for edit quick actions dialog
editQuickActions=빠른 액션 편집
#XBTN: Button text for clearing the selected quick actions in the edit quick actions dialog
clearSelectedQuickActions=선택 지우기
#XMSG: Info message for quick actions dialog to tell the user how much quick actions they can select. {0} will represent the current number of selected quick actions
quickActionsLimit=최대 4개의 빠른 액션 중 {0}개를 선택했습니다.
#XBTN: OK button text for edit quick actions dialog
okButton=확인
#XBTN: Cancel button text for edit quick actions dialog
cancelButton=취소
#XTXT: Quick action to create a space
createSpace=공간 생성
#XTXT: Quick action to create a table
createTable=테이블 생성
#XTXT: Quick action to create a graphical view
createGraphicalView=그래픽 뷰 생성
#XTXT: Quick action to create a SQL view
createSqlView=SQL 뷰 생성
#XTXT: Quick action to create an E/R model
createErModel=엔티티 - 관계 모델 생성
#XTXT: Quick action to create a analytic model
createAnalyticModel=분석 모델 생성
#XTXT: Quick action to create a data flow
createDataFlow=데이터 흐름 생성
#XTXT: Quick action to create a replication flow
createReplicationFlow=복제 흐름 생성
#XTXT: Quick action to create a replication flow
createTransformationFlow=변환 흐름 생성
#XTXT: Quick action to create a intelligent lookup
createIntelligentLookup=인텔리전트 조회 생성
#XTXT: Quick action to create a currency conversion views
createCurrencyConversionViews=통화 환산 뷰 생성
#XTXT: Quick action to create a unit conversion views
createUnitConversionViews=단위 환산 뷰 생성
#XTXT: Quick action to create a task chain
createTaskChain=태스크 체인 생성
#XTXT: Quick action to create a dimension
createDimension=차원 생성
#XTXT: Quick action to create a fact
createFact=팩트 생성
#XTXT: Quick action to create a fact model
createFactModel=팩트 모델 생성
#XTXT: Quick action to create a consumption model
createConsumptionModel=사용 모델 생성
#XTXT: Quick action to create a data access control
createDataAccessControl=데이터 액세스 제어 생성
#XTXT: Quick action to create a authorization scenario
createAuthorizationScenario=권한 부여 시나리오 생성
#XBTN: Button text to navigate from task logs card to the data integration monitor
openDataIntegrationMonitor=데이터 통합 모니터 열기
#XMSG: Error message when clicking a task log in the Data Integration Tasks card of a space, for which the user misses the DWC_INTEGRATOR.Read privilege.
missingDataIntegrationReadPrivileges=이 영역에 액세스하려면 {0} 공간에 대한 데이터 통합 읽기 권한을 얻어야 합니다.
#XBTN: Button text to navigate from the newsfeed/SAP Datasphere resources card to the SAP Datasphere blog
openBlog=블로그 열기
#XFLD: The next run label of a schedule task in the task logs card, {0} is a date or time
nextRun=다음 실행: {0}
#XFLD: Completed status text for a scheduled task in the task logs card
COMPLETED=완료됨
#XFLD: Running status text for a scheduled task in the task logs card
RUNNING=실행 중
#XFLD: Failed status text for a scheduled task in the task logs card
FAILED=실패
#XFLD: Not triggered status text for a scheduled task in the task logs card
NOT_TRIGGERED=트리거되지 않음
#XFLD: Description text for filter toggle to only display list entries with an error state
showOnlyErrors=오류만 표시
#XFLD: Card header title for spaces card
spacesCardTitle=공간
#XFLD: Card header subtitle for spaces card
spacesCardSubtitle=저장소/상태 - 상위 5개
#XFLD: Table Header "Name"
thName=이름
#XFLD: Table Header "Priority"
thPriority=우선순위
#XFLD: Table Header "Storage"
thStorage=저장소
#XFLD: Table Header "Status"
thStatus=상태
#XFLD: Table Header "Space"
thSpace=공간
#XFLD: Table Header "Type"
thType=유형
#XFLD: Table Header "Last Changed"
thLastChanged=최종 변경
#XTOL: Tooltip for create space button
tolCreateSpace=공간 생성
#XFLD: Space status "unknown"
unknown=알 수 없음
#XFLD: Space status "active"
active=활성
#XFLD: Space status "cold"
cold=콜드
#XFLD: Space status "ok"
ok=작업 중
#XFLD: Space status "critical"
critical=심각
#XFLD: Space status "locked"
locked=잠김
#XTOL: Tooltip - locked due to memory
lockedSpaceTooltipMemory=잠긴 공간: 메모리 쿼터 소진됨
#XTOL: Tooltip - locked due to storage
lockedSpaceTooltipStorage=잠긴 공간: 디스크 쿼터 소진됨
#XTOL: Tooltip - locked due to storage and memory
lockedSpaceTooltipAll=잠긴 공간: 디스크 쿼터 및 메모리 쿼터 소진됨
#XTOL: Tooltip - locked due to auditlog quota
lockedSpaceTooltipAuditlog=잠긴 공간: 감사 로그 쿼터 소진됨
#XTOL: Tooltip - locked manually
lockedSpaceTooltipManual=잠긴 공간: 수동으로 잠김
#XTOL: Critical space tooltip
criticalSpaceTooltip=남아 있는 여유 저장 공간이 심각하게 작음(10% 미만)
#XTOL: Green space tooltip
workingSpaceTooltip=사용된 저장 공간이 일반 범위 안에 속함(최대 90%)
#XTOL: Cold space tooltip
coldSpaceTooltip=5% 미만의 저장 공간 사용됨
#XFLD: Used {used} {byte-unit} of {assigned} {byte unit} - for disk and memory
memStorageUsage={2} {3} 중 {0} {1} 사용됨
#XFLD: Used {used} {byte-unit} - for disk and memory
memStorageUsageNoQuota=사용된 {0} {1}
#XFLD: Open Space Management
openSpaceManagement=공간 관리 열기
#XMSG: Error message when clicking a space in the spaces card, for which user misses the DWC_SPACES.Read privilege.
missingSpacesReadPrivileges=이 영역에 액세스하려면 {0} 공간에 대한 공간 읽기 권한을 얻어야 합니다.
#XTIT: Title text for "no spaces" on the spaces card
noSpacesTitle=공간 없음
#XFLD: Eplanatory texts with hints for "no spaces" on the spaces card
noSpacesTextWithCreate=어떤 공간에도 지정되어 있지 않습니다. 공간을 생성하거나 관리자에게 문의하십시오.
noSpacesTextNoCreate=어떤 공간에도 지정되어 있지 않습니다. 관리자에게 문의하십시오.
#XFLD: Card header title for data builder card
dataBuilderCardTitle=데이터 빌더
#XFLD: Bottom link text for data builder card
bottomLinkTextForDataBuilderCard=데이터 빌더 열기
#XFLD Menu Item "Table" for the data builder card
dataBuilderCardMenuItemTable=테이블
#XFLD Menu Item "Graphical View" for the data builder card
dataBuilderCardMenuItemGraphicalView=그래픽 뷰
#XFLD Menu Item "SQL View" for the data builder card
dataBuilderCardMenuItemSqlView=SQL 뷰
#XFLD Menu Item "Entity - Relationship Model" for the data builder card
dataBuilderCardMenuItemEntityRelationshipModel=엔티티 - 관계 모델
#XFLD Menu Item "Analytic Model" for the data builder card
dataBuilderCardMenuItemAnalyticModel=분석 모델
#XFLD Menu Item "Data Access Control" for the data builder card
dataBuilderCardMenuItemDataAccessControl=데이터 액세스 제어
#XFLD Menu Item "Data Flow" for the data builder card
dataBuilderCardMenuItemDataFlow=데이터 흐름
#XFLD Menu Item "Replication Flow" for the data builder card
dataBuilderCardMenuItemReplicationFlow=복제 흐름
#XFLD Menu Item "Transformation Flow" for the data builder card
dataBuilderCardMenuItemTransformationFlow=변환 흐름
#XFLD Menu Item "Intelligent Lookup for the data builder card
dataBuilderCardMenuItemIntelligentLookup=인텔리전트 조회
#XFLD Menu Item "Currency Conversion Views" for the data builder card
dataBuilderCardMenuItemCurrencyConversionViews=통화 환산 뷰
#XFLD Menu Item "Unit Conversion Views" for the data builder card
dataBuilderCardMenuItemUnitConversionViews=단위 환산 뷰
#XFLD Menu Item "Task Chain" for the data builder card
dataBuilderCardMenuItemTaskChain=태스크 체인
#XFLD: Card header title for business builder card
businessBuilderCardTitle=비즈니스 빌더
#XFLD: Bottom link text for data builder card
bottomLinkTextForBusinessBuilderCard=비즈니스 빌더 열기
#XTOL: Tooltip to create a new business builder object
createBusinessBuilderObjectTooltip=비즈니스 빌더 생성 오브젝트
#XTOL: Tooltip to create a new data builder object
createDataBuilderObjectTooltip=데이터 빌더 생성 오브젝트
#XFLD: Menu Item "Dimension" for business builder card
businessBuilderCardMenuItemDimension=차원
#XFLD: Menu Item "Fact" for business builder card
businessBuilderCardMenuItemFact=팩트
#XFLD: Menu Item "Fact Model" for business builder card
businessBuilderCardMenuItemFactModel=팩트 모델
#XFLD: Menu Item "Consumption Model" for business builder card
businessBuilderCardMenuItemConsumptionModel=사용 모델
#XFLD: Menu Item "Authorization Scenario" for business builder card
businessBuilderCardMenuItemAuthorizationScenario=권한 부여 시나리오
#XTIT: Title for "no data" screen on business assets and data assets cards
noRecentObjectsTitle=최근 오브젝트 없음
#XTXT Explanatory text for "no data" screen on business assets and data assets cards
noRecentObjectsText=해당 항목이 있을 경우, 여기에서 볼 수 있음
#XTIT Title for "no data" screen on cards with onlyErrors filter active
noRecentErrorsTitle=최근 오류 없음
#~~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~ DataSuite ~~~~~~~~~~~~~~~~~~~
#XTIT: Title of the Data Suite Dialog itself
dataSuiteDialogTitle=SAP Datasphere 시작!
#XTXT Text after the Announcement Video if Unified Homepage Feature Flag is active
welcomeDialogVideoDescription=이 동영상을 참조하여 SAP Datasphere를 시작해 보십시오! 가장 중요한 앱들을 소개하는 개요를 볼 수 있으며, SAP Datasphere에서 처음으로 그래픽 뷰와 분석 모델을 생성하기 위한 첫 단계를 살펴볼 수 있습니다. 이렇게 생성한 그래픽 뷰와 분석 모델을 SAP Analytics Cloud 스토리에서 사용할 수 있습니다.
#XMSG More learning resources for Data Suite. Be aware that %%0 and %%1 are indexes that are replaced by links!
dataSuiteDialogLearnMore=여기에 대해 더 자세히 알아보려면 sap.com의 %%0을(를) 방문하거나 %%1 가이드를 참조하십시오.
#XBUT Learn More link text (index %%0)
dataSuiteDialogLearnMoreLink=SAP Datasphere
#XBUT Getting Started Link Text (index %%1)
dataSuiteDialogGettingStartedLink=시작
#XBUT Close button of the Data Suite Dialog
dataSuiteDialogCloseButton=닫기
