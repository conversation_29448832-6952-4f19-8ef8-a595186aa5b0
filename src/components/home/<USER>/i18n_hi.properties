#~~~~~~~~~~~ DataSuite Welcome Card ~~~~~~~~~~~~~~~~~~~
#XTIT: Title of the Welcome Card
welcomeCardTitle=SAP Datasphere के लिए स्वागत है
#XMSG: Welcome Text shown after Title
dataSuiteCardText=SAP Datasphere एक व्यवसायिक डेटा फेब्रिक आर्किटेक्चर को सक्षम बनाता है जो विशिष्ट रूप से पूरे संगठन में मिशन-महत्वपूर्ण डेटा को सुसंगत बनाता है, व्यावसायिक विशेषज्ञों को सबसे प्रभावशाली निर्णय लेने के लिए मुक्त करता है. SAP और non-SAP डेटा में डेटा एकीकरण, कैटलॉगिंग, सिमेंटिक मॉडलिंग, डेटा वेयरहाउसिंग और आभाषीकरण वर्कलोड के लिए एक एकीकृत सेवा में पहले की असतत क्षमताओं को जोड़ती है.
#XBUT Text of the Learn More Button
dataSuiteLearnMoreButtonText=अधिक सीखें

#XTXT: ARIA label for blog post cards within the SAP Datasphere Blogs card on the homepage.
#This ARIA label is read out loud by screenreaders for accessibility support. This is the only usage of the text, it is not visible anywhere.
# {0} will be replaced by the position (1 to 10) of the blog post cards, since we have in total 10 blog post cards.
newsfeedCardAriaLabel=ब्लॉग पोस्ट {0}

#XTXT: ARIA label for the card title on the homepage.
# This ARIA label is read out loud by screenreaders for accessibility support. This is the only usage of the text, it is not visible anywhere.
# {0} will be replaced by the title of the card.
cardTitleAriaLabel=कार्ड {0}

#XTOL: Tooltip for avatar images in the newsfeed (is also used as an alternative text for screen readers or in case the image is unavailable)
newsfeedAvatarTooltip=फ़ीड आइटम लेखक अवतार

#XTIT: Infomation popup for missing authorizations
MissingAuth=प्राधिकरण गुम है

#XMSG: Information for missing authorizations
MissingAuthInfoText=ऐसा लगता है कि आप अभी इस सुविधा का उपयोग नहीं कर सकते हैं. इसके लिए आपकी सहायता के लिए कृपया अपने व्यवस्थापक से संपर्क करें.

#XMSG: Popup message when idp configuration was successfull
idpSuccessMessage=आपका SAML खाता सत्यापन सफ़ल रहा. कृपया इस ब्राउज़र विंडो को बंद करें और सेटअप प्रक्रिया जारी रखें.

#XMSG: Text for manage settings
manageSettings=सेटिंग प्रबंधित करें

#XMSG: Popup message for manage settings
manageSettingsMessage=जब आप ऑब्जेक्ट को खोजते और खोलते हैं तो हम आपको व्यक्तिगत अनुभव प्रदान कराने के लिए उसे रिकॉर्ड करते हैं.\n\nआप किसी भी समय अपने खाता सेटिंग डायलॉग में अपना डेटा साफ कर सकते हैं या निजीकरण बंद कर सकते हैं.

#~~~~~~~~~~~ Texts for the SAP Datasphere Homepage ~~~~~~~~~~~~~~~~
#XTIT: Welcoming title for the homepage. {0} will be replaced with the first name of the user, {1} will be replaced with the last name of the user.
objectPageHeaderTitle=नमस्कार, !{0} {1}
#XFLD: Label for switch to toggle auto refresh of the data in the homepage cards
autoRefresh=ऑटो रिफ्रेश डेटा
#XMSG: Message toast text, for when the auto refresh switch is turned on and the immediate refresh of all currently visible cards' data is done.
dataRefreshDone=कार्ड रीफ़्रेश किए गए
#BUT: Button to customize the homepage
customize=अनुकूलित
#XFLD: Title for the placeholder in case no cards is selected and the homepage is empty
noCardsPlaceholderTitle=आइए आपके घर को अनुकूलित करें
#XFLD: Description for the placeholder in case no cards is selected and the homepage is empty
noCardsPlaceholderDescription=अपने विशेषाधिकारों के आधार पर कार्ड जोड़ना प्रारंभ करें
#XFLD: Sub-header for cards in unified homepage
recentlyStarted=हाल ही में प्रारंभ हुआ - शीर्ष 5
#XFLD: Sub-header for Data Builder and Business Builder card if Activity Tracking is enabled
recentlyAccessed=हाल ही में पहुंचा गया - शीर्ष 5
#XFLD: Sub-header for Data Builder and Business Builder card if Activity Tracking is disabled
recentlyChanged=हाल ही में परिवर्तित किया गया - शीर्ष 5
#XFLD: Card header title for data integration tasks card
dataIntegrationTasksCardTitle=डेटा एकीकरण कार्य
#XFLD: Card header title for newsfeed/SAP Datasphere resources card
newsfeedCardTitle=SAP Datasphere ब्लॉग
#XFLD: Card header subtitle for newsfeed/SAP Datasphere resources card
newsfeedCardSubtitle=हाल ही के पोस्ट - शीर्ष 10
#XTIT: Title for "no data" screen in newsfeed/SAP Datasphere resources card
noBlogEntryTitle=कोई हाल का पोस्ट नहीं
#XTIT: Title for "no data" screen in newsfeed/SAP Datasphere resources card
noBlogEntryDescription=अन्य रोचक पोस्ट ढूंढने के लिए SAP ब्लॉग ब्राउज़ करें.
#XTIT: Title for "no data" screen in data integration card
noRecentTasksTitle=आपके पास हाल का कोई कार्य नहीं है
#XTXT: Explanatory text for "no data" screen on data integration card
noRecentTasksText=जब होंगे, आप उन्हें यहां देखेंगे.
#XTIT: Title for the illustrated message in case of data load error on any card
dataFetchErrorTitle=डेटा लोड करने में असमर्थ.
#XTIT: Title for the illustrated message in case of data load error on any card caused by the circuit breaker having a HanaState = red (unvailable)
circuitBreakerDataFetchErrorTitle=रन-टाइम डेटाबेस उपलब्ध नहीं है.
#XTIT: Title for the illustrated message during the tenant upgrade process
tenantUpgradeInProgressTitle=हम SAP Datasphere से अपडेट लाते हैं
#XFLD: Descriptions for the illustrated message in case of data load error on any card
dataFetchErrorDescription=यदि यह समस्या बनी रहती है, तो ग्राहक सहायता से संपर्क करें और सहसंबंध ID अनुलग्नक करें: {0}
dataFetchErrorDescriptionNoCorrelationId=यदि यह समस्या बनी रहती है, तो ग्राहक सहायता से संपर्क करें
#XFLD: Description for the illustrated message during the tenant upgrade process
tenantUpgradeInProgressDescription=कृपया जल्द ही {0} को क्लिक करने का प्रयास करें
#XBTN: Button text for action on data load error
onErrorAction=रिलोड करें
#XFLD: Card header title for quick actions card
quickActionsCardTitle=त्वरित क्रियाएं
#XFLD: Tooltip to edit the selected quick actions
editQuickActionsTooltip=त्वरित कार्रवाइयां संपादित करें
#XFLD: No Data Title for Quick Actions Card in case no Quick Action is selected
noQuickActionSelectedTitle=कोई त्वरित कार्रवाई चयनित नहीं
#XFLD: No Data Description for Quick Actions Card in case no Quick Action is selected
noQuickActionSelectedDescription=उन्हें यहां प्रदर्शित करने के लिए त्वरित कार्रवाइयां जोड़ें.
#XFLD: No Data Button Text for Quick Actions Card in case no Quick Action is selected
noQuickActionSelectedButton=त्वरित कार्रवाइयां जोड़ें
#XFLD: Title for the dialog to edit the selected quick actions
editQuickActionsDialogTitle=त्वरित कार्रवाइयां संपादित करें
#XTIT: Header text for edit quick actions dialog
editQuickActions=त्वरित कार्रवाइयां संपादित करें
#XBTN: Button text for clearing the selected quick actions in the edit quick actions dialog
clearSelectedQuickActions=चयन साफ़ करें
#XMSG: Info message for quick actions dialog to tell the user how much quick actions they can select. {0} will represent the current number of selected quick actions
quickActionsLimit=आपने अधिकतम 4 त्वरित कार्रवाइयों {0} में से एक का चयन किया है.
#XBTN: OK button text for edit quick actions dialog
okButton=ठीक
#XBTN: Cancel button text for edit quick actions dialog
cancelButton=रद्द करें
#XTXT: Quick action to create a space
createSpace=स्पेस बनाएं
#XTXT: Quick action to create a table
createTable=तालिका बनाएं
#XTXT: Quick action to create a graphical view
createGraphicalView=ग्राफ़िकल दृश्य बनाएं
#XTXT: Quick action to create a SQL view
createSqlView=SQL दृश्य बनाएं
#XTXT: Quick action to create an E/R model
createErModel=निकाय-संबंधी मॉडल बनाएं
#XTXT: Quick action to create a analytic model
createAnalyticModel=विश्लेषक मॉडल बनाएं
#XTXT: Quick action to create a data flow
createDataFlow=डेटा फ़्लो बनाएं
#XTXT: Quick action to create a replication flow
createReplicationFlow=प्रतिकृति प्रवाह बनाएं
#XTXT: Quick action to create a replication flow
createTransformationFlow=रूपांतरण प्रवाह बनाएं
#XTXT: Quick action to create a intelligent lookup
createIntelligentLookup=बुद्धिमान लुकअप बनाएं
#XTXT: Quick action to create a currency conversion views
createCurrencyConversionViews=मुद्रा रूपांतरण दृश्य बनाएं
#XTXT: Quick action to create a unit conversion views
createUnitConversionViews=इकाई रूपांतरण दृश्य बनाएं
#XTXT: Quick action to create a task chain
createTaskChain=कार्य श्रृंखला बनाएं
#XTXT: Quick action to create a dimension
createDimension=आयाम बनाएं
#XTXT: Quick action to create a fact
createFact=तथ्य बनाएं
#XTXT: Quick action to create a fact model
createFactModel=तथ्य मॉडल बनाएं
#XTXT: Quick action to create a consumption model
createConsumptionModel=उपभोग मॉडल बनाएं
#XTXT: Quick action to create a data access control
createDataAccessControl=डेटा पहुंच नियंत्रण बनाएं
#XTXT: Quick action to create a authorization scenario
createAuthorizationScenario=प्राधिकरण परिदृश्य बनाएं
#XBTN: Button text to navigate from task logs card to the data integration monitor
openDataIntegrationMonitor=डेटा एकीकरण मॉनिटर में खोलें
#XMSG: Error message when clicking a task log in the Data Integration Tasks card of a space, for which the user misses the DWC_INTEGRATOR.Read privilege.
missingDataIntegrationReadPrivileges=आपको इस क्षेत्र तक पहुंचने के लिए, स्थान {0} के लिए डेटा एकीकरण पठन विशेषाधिकार प्राप्त करना होगा.
#XBTN: Button text to navigate from the newsfeed/SAP Datasphere resources card to the SAP Datasphere blog
openBlog=ब्लॉग खोले
#XFLD: The next run label of a schedule task in the task logs card, {0} is a date or time
nextRun=अगला रन: {0}
#XFLD: Completed status text for a scheduled task in the task logs card
COMPLETED=पूरा हुआ
#XFLD: Running status text for a scheduled task in the task logs card
RUNNING=रन हो रहा है
#XFLD: Failed status text for a scheduled task in the task logs card
FAILED=विफल
#XFLD: Not triggered status text for a scheduled task in the task logs card
NOT_TRIGGERED=ट्रिगर नहीं किया गया
#XFLD: Description text for filter toggle to only display list entries with an error state
showOnlyErrors=केवल त्रुटियां दिखाएं
#XFLD: Card header title for spaces card
spacesCardTitle=स्पेस
#XFLD: Card header subtitle for spaces card
spacesCardSubtitle=संग्रहण / स्थिति - शीर्ष 5
#XFLD: Table Header "Name"
thName=नाम
#XFLD: Table Header "Priority"
thPriority=वरीयता
#XFLD: Table Header "Storage"
thStorage=संग्रहण
#XFLD: Table Header "Status"
thStatus=स्थिति
#XFLD: Table Header "Space"
thSpace=स्पेस
#XFLD: Table Header "Type"
thType=प्रकार
#XFLD: Table Header "Last Changed"
thLastChanged=अंतिम बार परिवर्तित
#XTOL: Tooltip for create space button
tolCreateSpace=स्पेस बनाएं
#XFLD: Space status "unknown"
unknown=अज्ञात
#XFLD: Space status "active"
active=सक्रिय करें
#XFLD: Space status "cold"
cold=ठंडा
#XFLD: Space status "ok"
ok=कार्य
#XFLD: Space status "critical"
critical=महत्वपूर्ण
#XFLD: Space status "locked"
locked=लॉक किया हुआ
#XTOL: Tooltip - locked due to memory
lockedSpaceTooltipMemory=लॉक किया गया स्थान: स्मृति कोटा समाप्त हो गया
#XTOL: Tooltip - locked due to storage
lockedSpaceTooltipStorage=लॉक किया गया स्थान: डिस्क कोटा समाप्त हो गया
#XTOL: Tooltip - locked due to storage and memory
lockedSpaceTooltipAll=लॉक किया गया स्थान: डिस्क और स्मृति कोटा समाप्त हो गया
#XTOL: Tooltip - locked due to auditlog quota
lockedSpaceTooltipAuditlog=लॉक किया गया स्थान: ऑडिट लॉग कोटा समाप्त हो गया
#XTOL: Tooltip - locked manually
lockedSpaceTooltipManual=लॉक किया गया स्थान: मैन्युअल रूप से लॉक किया गया
#XTOL: Critical space tooltip
criticalSpaceTooltip=शेष निःशुल्क संग्रहण अत्यंत कम (10% से नीचे) है
#XTOL: Green space tooltip
workingSpaceTooltip=प्रयुक्त संग्रहण सामान्य सीमा (90% तक) में है
#XTOL: Cold space tooltip
coldSpaceTooltip=5% से कम संग्रहण का उपयोग किया गया
#XFLD: Used {used} {byte-unit} of {assigned} {byte unit} - for disk and memory
memStorageUsage=का उपयोग {0} {1} किया गया {2} {3}
#XFLD: Used {used} {byte-unit} - for disk and memory
memStorageUsageNoQuota=प्रयुक्त {0} {1}
#XFLD: Open Space Management
openSpaceManagement=मुक्त स्थान प्रबंधन
#XMSG: Error message when clicking a space in the spaces card, for which user misses the DWC_SPACES.Read privilege.
missingSpacesReadPrivileges=आपको इस क्षेत्र तक पहुंचने के लिए, स्थान {0} के लिए स्थान पठन विशेषाधिकार प्राप्त करना होगा.
#XTIT: Title text for "no spaces" on the spaces card
noSpacesTitle=कोई स्पेस नहीं मिला
#XFLD: Eplanatory texts with hints for "no spaces" on the spaces card
noSpacesTextWithCreate=आपको कोई स्थान असाइन नहीं किया गया है. एक बनाएं या अपने व्यवस्थापक से संपर्क करें.
noSpacesTextNoCreate=आपको कोई स्थान असाइन नहीं किया गया है. अपने व्यवस्थापक से संपर्क करें.
#XFLD: Card header title for data builder card
dataBuilderCardTitle=डेटा बिल्डर
#XFLD: Bottom link text for data builder card
bottomLinkTextForDataBuilderCard=डेटा बिल्डर में खोलें
#XFLD Menu Item "Table" for the data builder card
dataBuilderCardMenuItemTable=तालिका
#XFLD Menu Item "Graphical View" for the data builder card
dataBuilderCardMenuItemGraphicalView=ग्राफ़िकल दृश्य
#XFLD Menu Item "SQL View" for the data builder card
dataBuilderCardMenuItemSqlView=SQL व्यू
#XFLD Menu Item "Entity - Relationship Model" for the data builder card
dataBuilderCardMenuItemEntityRelationshipModel=निकाय - संबंध मॉडल
#XFLD Menu Item "Analytic Model" for the data builder card
dataBuilderCardMenuItemAnalyticModel=विश्लेषिकी मॉडल
#XFLD Menu Item "Data Access Control" for the data builder card
dataBuilderCardMenuItemDataAccessControl=डेटा पहुंच नियंत्रण
#XFLD Menu Item "Data Flow" for the data builder card
dataBuilderCardMenuItemDataFlow=डेटा फ़्लो
#XFLD Menu Item "Replication Flow" for the data builder card
dataBuilderCardMenuItemReplicationFlow=प्रतिकृति प्रवाह
#XFLD Menu Item "Transformation Flow" for the data builder card
dataBuilderCardMenuItemTransformationFlow=रूपांतरण प्रवाह
#XFLD Menu Item "Intelligent Lookup for the data builder card
dataBuilderCardMenuItemIntelligentLookup=इंटेलिजेंट लुकअप
#XFLD Menu Item "Currency Conversion Views" for the data builder card
dataBuilderCardMenuItemCurrencyConversionViews=मुद्रा रूपांतरण दृश्य
#XFLD Menu Item "Unit Conversion Views" for the data builder card
dataBuilderCardMenuItemUnitConversionViews=इकाई रूपांतरण दृश्य 
#XFLD Menu Item "Task Chain" for the data builder card
dataBuilderCardMenuItemTaskChain=कार्य श्रृंखला
#XFLD: Card header title for business builder card
businessBuilderCardTitle=व्यवसाय बिल्डर
#XFLD: Bottom link text for data builder card
bottomLinkTextForBusinessBuilderCard=व्यवसाय बिल्डर खोले
#XTOL: Tooltip to create a new business builder object
createBusinessBuilderObjectTooltip=व्यवसाय बिल्डर ऑब्जेक्ट बनाएं
#XTOL: Tooltip to create a new data builder object
createDataBuilderObjectTooltip=डेटा बिल्डर ऑब्जेक्ट बनाएं
#XFLD: Menu Item "Dimension" for business builder card
businessBuilderCardMenuItemDimension=आयाम
#XFLD: Menu Item "Fact" for business builder card
businessBuilderCardMenuItemFact=तथ्य
#XFLD: Menu Item "Fact Model" for business builder card
businessBuilderCardMenuItemFactModel=तथ्य मॉडल
#XFLD: Menu Item "Consumption Model" for business builder card
businessBuilderCardMenuItemConsumptionModel=उपभोग मॉडल
#XFLD: Menu Item "Authorization Scenario" for business builder card
businessBuilderCardMenuItemAuthorizationScenario=प्राधिकरण परिदृश्य
#XTIT: Title for "no data" screen on business assets and data assets cards
noRecentObjectsTitle=आपके पास हाल का कोई ऑब्जेक्ट नहीं है
#XTXT Explanatory text for "no data" screen on business assets and data assets cards
noRecentObjectsText=जब होंगे, आप उन्हें यहां देखेंगे.
#XTIT Title for "no data" screen on cards with onlyErrors filter active
noRecentErrorsTitle=आपकी हाल की कोई त्रुटि नहीं है
#~~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~ DataSuite ~~~~~~~~~~~~~~~~~~~
#XTIT: Title of the Data Suite Dialog itself
dataSuiteDialogTitle=SAP Datasphere के लिए स्वागत है!
#XTXT Text after the Announcement Video if Unified Homepage Feature Flag is active
welcomeDialogVideoDescription=यह वीडियो आपको SAP डेटास्फेयर से प्रारंभ कराता है! यह आपको अपना पहला ग्राफ़िकल दृश्य और एक विश्लेषणात्मक मॉडल बनाने के लिए सबसे महत्वपूर्ण एप्लीकेशन और SAP डेटास्फेयर में उठाए जाने वाले पहले चरण का ओवरव्यू देता है, जिसे आप SAP एनालिटिक्स क्लाउड स्टोरी में उपभोग कर सकते हैं.
#XMSG More learning resources for Data Suite. Be aware that %%0 and %%1 are indexes that are replaced by links!
dataSuiteDialogLearnMore=अधिक जानने के लिए %%0 पर sap.com पर जाएं या %%1 मार्गदर्शिका पर जाएं.
#XBUT Learn More link text (index %%0)
dataSuiteDialogLearnMoreLink=SAP Datasphere
#XBUT Getting Started Link Text (index %%1)
dataSuiteDialogGettingStartedLink=शुरू करना
#XBUT Close button of the Data Suite Dialog
dataSuiteDialogCloseButton=बंद करें
