#~~~~~~~~~~~ DataSuite Welcome Card ~~~~~~~~~~~~~~~~~~~
#XTIT: Title of the Welcome Card
welcomeCardTitle=Dobro došli u SAP Datasphere
#XMSG: Welcome Text shown after Title
dataSuiteCardText=SAP Datasphere obezbeđuje arhitekturu sastava poslovnih podataka koja na jedinstven način usklađuje ključne podatke u celoj organizaciji i daje slobodu poslovnim stručnjacima da donose odluke sa najupečatljivijim rezultatima. SAP Datasphere kombinuje prethodne diskretne mogućnosti u jedinstvenu uslugu za integraciju podataka, katalogizaciju, semantičko modeliranje, skladištenje podataka i virtuelizaciju radnih opterećenja za sve SAP podatke i eksterne podatke.
#XBUT Text of the Learn More Button
dataSuiteLearnMoreButtonText=Saznajte više

#XTXT: ARIA label for blog post cards within the SAP Datasphere Blogs card on the homepage.
#This ARIA label is read out loud by screenreaders for accessibility support. This is the only usage of the text, it is not visible anywhere.
# {0} will be replaced by the position (1 to 10) of the blog post cards, since we have in total 10 blog post cards.
newsfeedCardAriaLabel=Objava na blogu {0}

#XTXT: ARIA label for the card title on the homepage.
# This ARIA label is read out loud by screenreaders for accessibility support. This is the only usage of the text, it is not visible anywhere.
# {0} will be replaced by the title of the card.
cardTitleAriaLabel=Kartica {0}

#XTOL: Tooltip for avatar images in the newsfeed (is also used as an alternative text for screen readers or in case the image is unavailable)
newsfeedAvatarTooltip=Avatar autora stavke feed-a

#XTIT: Infomation popup for missing authorizations
MissingAuth=Nedostaje ovlašćenje

#XMSG: Information for missing authorizations
MissingAuthInfoText=Izgleda da trenutno ne možete da pristupite ovoj funkciji. Obratite se administratoru da vam pomogne u tome.

#XMSG: Popup message when idp configuration was successfull
idpSuccessMessage=Vaša SAML verifikacija naloga je uspešna. Zatvorite ovaj prozor pretraživača i nastavite proces konfiguracije.

#XMSG: Text for manage settings
manageSettings=Upravljaj podešavanjima

#XMSG: Popup message for manage settings
manageSettingsMessage=Vodimo evidenciju o vašem traženju i otvaranju objekata kako bismo vam pružili personalizovano iskustvo.\n\nMožete izbrisati svoje podatke u bilo kom trenutku ili možete isključiti personalizaciju u dijalogu naloga Podešavanja.

#~~~~~~~~~~~ Texts for the SAP Datasphere Homepage ~~~~~~~~~~~~~~~~
#XTIT: Welcoming title for the homepage. {0} will be replaced with the first name of the user, {1} will be replaced with the last name of the user.
objectPageHeaderTitle=Zdravo, {0} {1}
#XFLD: Label for switch to toggle auto refresh of the data in the homepage cards
autoRefresh=Automatski osveži podatke
#XMSG: Message toast text, for when the auto refresh switch is turned on and the immediate refresh of all currently visible cards' data is done.
dataRefreshDone=Kartice osvežene
#BUT: Button to customize the homepage
customize=Prilagodi
#XFLD: Title for the placeholder in case no cards is selected and the homepage is empty
noCardsPlaceholderTitle=Prilagodimo vašu početnu stranicu
#XFLD: Description for the placeholder in case no cards is selected and the homepage is empty
noCardsPlaceholderDescription=Pokrenite dodavanje kartica na osnovu vaših ovlašćenja
#XFLD: Sub-header for cards in unified homepage
recentlyStarted=Nedavno pokrenuto - prvih 5
#XFLD: Sub-header for Data Builder and Business Builder card if Activity Tracking is enabled
recentlyAccessed=Nedavno ostvaren pristup - prvih 5
#XFLD: Sub-header for Data Builder and Business Builder card if Activity Tracking is disabled
recentlyChanged=Nedavno promenjeno - prvih 5
#XFLD: Card header title for data integration tasks card
dataIntegrationTasksCardTitle=Zadaci integracije podataka
#XFLD: Card header title for newsfeed/SAP Datasphere resources card
newsfeedCardTitle=Blog SAP Datasphere
#XFLD: Card header subtitle for newsfeed/SAP Datasphere resources card
newsfeedCardSubtitle=Nedavne objave - prvih 10
#XTIT: Title for "no data" screen in newsfeed/SAP Datasphere resources card
noBlogEntryTitle=Nema nedavnih objava
#XTIT: Title for "no data" screen in newsfeed/SAP Datasphere resources card
noBlogEntryDescription=Pretražite blog kompanije SAP da biste našli druge zanimljive objave.
#XTIT: Title for "no data" screen in data integration card
noRecentTasksTitle=Nemate nedavne zadatke.
#XTXT: Explanatory text for "no data" screen on data integration card
noRecentTasksText=Kada ih bude, videćete ih ovde.
#XTIT: Title for the illustrated message in case of data load error on any card
dataFetchErrorTitle=Nije moguće učitati podatke.
#XTIT: Title for the illustrated message in case of data load error on any card caused by the circuit breaker having a HanaState = red (unvailable)
circuitBreakerDataFetchErrorTitle=Baza podataka vremena izvođenja nije dostupna.
#XTIT: Title for the illustrated message during the tenant upgrade process
tenantUpgradeInProgressTitle=Uvodimo ažuriranja za SAP Datasphere
#XFLD: Descriptions for the illustrated message in case of data load error on any card
dataFetchErrorDescription=Ako ovaj problem nastavi da se pojavljuje, obratite se korisničkoj podršci i priložite ID korelacije: {0}
dataFetchErrorDescriptionNoCorrelationId=Ako se ovaj problem nastavi, obratite se korisničkoj podršci
#XFLD: Description for the illustrated message during the tenant upgrade process
tenantUpgradeInProgressDescription=Pokušajte uskoro da kliknete na {0}
#XBTN: Button text for action on data load error
onErrorAction=Ponovo učitaj
#XFLD: Card header title for quick actions card
quickActionsCardTitle=Brze radnje
#XFLD: Tooltip to edit the selected quick actions
editQuickActionsTooltip=Uredi brze radnje
#XFLD: No Data Title for Quick Actions Card in case no Quick Action is selected
noQuickActionSelectedTitle=Nisu odabrane brze radnje
#XFLD: No Data Description for Quick Actions Card in case no Quick Action is selected
noQuickActionSelectedDescription=Dodajte brze radnje da biste ih prikazali ovde.
#XFLD: No Data Button Text for Quick Actions Card in case no Quick Action is selected
noQuickActionSelectedButton=Dodaj brze radnje
#XFLD: Title for the dialog to edit the selected quick actions
editQuickActionsDialogTitle=Uredi brze radnje
#XTIT: Header text for edit quick actions dialog
editQuickActions=Uredi brze radnje
#XBTN: Button text for clearing the selected quick actions in the edit quick actions dialog
clearSelectedQuickActions=Ponovo postavi odabir
#XMSG: Info message for quick actions dialog to tell the user how much quick actions they can select. {0} will represent the current number of selected quick actions
quickActionsLimit=Odabrali ste {0} od maksimalno 4 brze radnje.
#XBTN: OK button text for edit quick actions dialog
okButton=OK
#XBTN: Cancel button text for edit quick actions dialog
cancelButton=Odustani
#XTXT: Quick action to create a space
createSpace=Kreiraj prostor
#XTXT: Quick action to create a table
createTable=Kreiraj tabelu
#XTXT: Quick action to create a graphical view
createGraphicalView=Kreiraj grafički prikaz
#XTXT: Quick action to create a SQL view
createSqlView=Kreiraj SQL prikaz
#XTXT: Quick action to create an E/R model
createErModel=Kreiraj entitet - model odnosa
#XTXT: Quick action to create a analytic model
createAnalyticModel=Kreiraj analitički model
#XTXT: Quick action to create a data flow
createDataFlow=Kreiraj tok podataka
#XTXT: Quick action to create a replication flow
createReplicationFlow=Kreiraj tok replikacije
#XTXT: Quick action to create a replication flow
createTransformationFlow=Kreiraj tok transformacije
#XTXT: Quick action to create a intelligent lookup
createIntelligentLookup=Kreiraj pametno traženje
#XTXT: Quick action to create a currency conversion views
createCurrencyConversionViews=Kreiraj poglede konverzije valute
#XTXT: Quick action to create a unit conversion views
createUnitConversionViews=Kreiraj poglede konverzije jedinice
#XTXT: Quick action to create a task chain
createTaskChain=Kreiraj lanac zadataka
#XTXT: Quick action to create a dimension
createDimension=Kreiraj dimenziju
#XTXT: Quick action to create a fact
createFact=Kreiraj činjenicu
#XTXT: Quick action to create a fact model
createFactModel=Kreiraj model činjenica
#XTXT: Quick action to create a consumption model
createConsumptionModel=Kreiraj model potrošnje
#XTXT: Quick action to create a data access control
createDataAccessControl=Kreiraj kontrolu pristupa podacima
#XTXT: Quick action to create a authorization scenario
createAuthorizationScenario=Kreiraj scenario ovlašćenja
#XBTN: Button text to navigate from task logs card to the data integration monitor
openDataIntegrationMonitor=Otvori monitor integracije podataka
#XMSG: Error message when clicking a task log in the Data Integration Tasks card of a space, for which the user misses the DWC_INTEGRATOR.Read privilege.
missingDataIntegrationReadPrivileges=Morate pozvati ovlašćenje za čitanje integracije podataka za prostor {0} za pristup ovoj oblasti.
#XBTN: Button text to navigate from the newsfeed/SAP Datasphere resources card to the SAP Datasphere blog
openBlog=Otvori blog
#XFLD: The next run label of a schedule task in the task logs card, {0} is a date or time
nextRun=Sledeće izvođenje: {0}
#XFLD: Completed status text for a scheduled task in the task logs card
COMPLETED=Završeno
#XFLD: Running status text for a scheduled task in the task logs card
RUNNING=Izvodi se
#XFLD: Failed status text for a scheduled task in the task logs card
FAILED=Nije uspelo
#XFLD: Not triggered status text for a scheduled task in the task logs card
NOT_TRIGGERED=Nije pokrenuto
#XFLD: Description text for filter toggle to only display list entries with an error state
showOnlyErrors=Pokaži samo greške
#XFLD: Card header title for spaces card
spacesCardTitle=Prostori
#XFLD: Card header subtitle for spaces card
spacesCardSubtitle=Skladište / status - prvih 5
#XFLD: Table Header "Name"
thName=Naziv
#XFLD: Table Header "Priority"
thPriority=Prioritet
#XFLD: Table Header "Storage"
thStorage=Skladište
#XFLD: Table Header "Status"
thStatus=Status
#XFLD: Table Header "Space"
thSpace=Prostor
#XFLD: Table Header "Type"
thType=Tip
#XFLD: Table Header "Last Changed"
thLastChanged=Poslednji put promenjeno
#XTOL: Tooltip for create space button
tolCreateSpace=Kreiraj prostor
#XFLD: Space status "unknown"
unknown=Nepoznato
#XFLD: Space status "active"
active=Aktivno
#XFLD: Space status "cold"
cold=Hladno
#XFLD: Space status "ok"
ok=Obrada u toku
#XFLD: Space status "critical"
critical=Kritično
#XFLD: Space status "locked"
locked=Zaključano
#XTOL: Tooltip - locked due to memory
lockedSpaceTooltipMemory=Zaključan prostor: kvota memorije iskorištena
#XTOL: Tooltip - locked due to storage
lockedSpaceTooltipStorage=Zaključan prostor: kvota diska iskorištena
#XTOL: Tooltip - locked due to storage and memory
lockedSpaceTooltipAll=Zaključan prostor: kvota diska i memorije iskorištena
#XTOL: Tooltip - locked due to auditlog quota
lockedSpaceTooltipAuditlog=Zaključan prostor: kvota protokola revizije iskorištena
#XTOL: Tooltip - locked manually
lockedSpaceTooltipManual=Zaključan prostor: ručno zaključano
#XTOL: Critical space tooltip
criticalSpaceTooltip=Preostali slobodan prostor za skladištenje na kritično niskom nivou (ispod 10%)
#XTOL: Green space tooltip
workingSpaceTooltip=Iskorišteni prostor za skladištenje je u normalnom opsegu (do 90%)
#XTOL: Cold space tooltip
coldSpaceTooltip=Manje od 5% prostora za skladištenje iskorišteno
#XFLD: Used {used} {byte-unit} of {assigned} {byte unit} - for disk and memory
memStorageUsage=Iskorišteno {0} {1} od {2} {3}
#XFLD: Used {used} {byte-unit} - for disk and memory
memStorageUsageNoQuota=Iskorišteno {0} {1}
#XFLD: Open Space Management
openSpaceManagement=Otvori upravljanje prostorom
#XMSG: Error message when clicking a space in the spaces card, for which user misses the DWC_SPACES.Read privilege.
missingSpacesReadPrivileges=Morate pozvati ovlašćenje za čitanje prostora za prostor {0} za pristup ovoj oblasti.
#XTIT: Title text for "no spaces" on the spaces card
noSpacesTitle=Prostori nisu nađeni
#XFLD: Eplanatory texts with hints for "no spaces" on the spaces card
noSpacesTextWithCreate=Niste dodeljeni nijednom prostoru. Kreirajte prostor ili se obratite administratoru.
noSpacesTextNoCreate=Niste dodeljeni nijednom prostoru. Obratite se administratoru.
#XFLD: Card header title for data builder card
dataBuilderCardTitle=Generator podataka
#XFLD: Bottom link text for data builder card
bottomLinkTextForDataBuilderCard=Otvori generator podataka
#XFLD Menu Item "Table" for the data builder card
dataBuilderCardMenuItemTable=Tabela
#XFLD Menu Item "Graphical View" for the data builder card
dataBuilderCardMenuItemGraphicalView=Grafički pogled
#XFLD Menu Item "SQL View" for the data builder card
dataBuilderCardMenuItemSqlView=Pogled SQL
#XFLD Menu Item "Entity - Relationship Model" for the data builder card
dataBuilderCardMenuItemEntityRelationshipModel=Model odnosa entiteta
#XFLD Menu Item "Analytic Model" for the data builder card
dataBuilderCardMenuItemAnalyticModel=Analitički model
#XFLD Menu Item "Data Access Control" for the data builder card
dataBuilderCardMenuItemDataAccessControl=Kontrola pristupa podacima
#XFLD Menu Item "Data Flow" for the data builder card
dataBuilderCardMenuItemDataFlow=Tok podataka
#XFLD Menu Item "Replication Flow" for the data builder card
dataBuilderCardMenuItemReplicationFlow=Tok replikacije
#XFLD Menu Item "Transformation Flow" for the data builder card
dataBuilderCardMenuItemTransformationFlow=Tok transformacije
#XFLD Menu Item "Intelligent Lookup for the data builder card
dataBuilderCardMenuItemIntelligentLookup=Pametno traženje
#XFLD Menu Item "Currency Conversion Views" for the data builder card
dataBuilderCardMenuItemCurrencyConversionViews=Pogledi konverzije valute
#XFLD Menu Item "Unit Conversion Views" for the data builder card
dataBuilderCardMenuItemUnitConversionViews=Pogledi konverzije jedinice
#XFLD Menu Item "Task Chain" for the data builder card
dataBuilderCardMenuItemTaskChain=Lanac zadataka
#XFLD: Card header title for business builder card
businessBuilderCardTitle=Generator poslovanja
#XFLD: Bottom link text for data builder card
bottomLinkTextForBusinessBuilderCard=Otvori generator poslovanja
#XTOL: Tooltip to create a new business builder object
createBusinessBuilderObjectTooltip=Kreirajte objekat generatora poslovanja
#XTOL: Tooltip to create a new data builder object
createDataBuilderObjectTooltip=Kreirajte objekat generatora podataka
#XFLD: Menu Item "Dimension" for business builder card
businessBuilderCardMenuItemDimension=Dimenzija
#XFLD: Menu Item "Fact" for business builder card
businessBuilderCardMenuItemFact=Činjenica
#XFLD: Menu Item "Fact Model" for business builder card
businessBuilderCardMenuItemFactModel=Model činjenica
#XFLD: Menu Item "Consumption Model" for business builder card
businessBuilderCardMenuItemConsumptionModel=Model potrošnje
#XFLD: Menu Item "Authorization Scenario" for business builder card
businessBuilderCardMenuItemAuthorizationScenario=Scenario ovlašćenja
#XTIT: Title for "no data" screen on business assets and data assets cards
noRecentObjectsTitle=Nemate nedavne objekte
#XTXT Explanatory text for "no data" screen on business assets and data assets cards
noRecentObjectsText=Kada ih bude, videćete ih ovde.
#XTIT Title for "no data" screen on cards with onlyErrors filter active
noRecentErrorsTitle=Nemate nedavne greške
#~~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~ DataSuite ~~~~~~~~~~~~~~~~~~~
#XTIT: Title of the Data Suite Dialog itself
dataSuiteDialogTitle=Dobro došli u SAP Datasphere!
#XTXT Text after the Announcement Video if Unified Homepage Feature Flag is active
welcomeDialogVideoDescription=Ovaj video vam pruža uvod u SAP Datasphere! Daje vam pregled najvažnijih aplikacija i prve korake za upotrebu SAP Datasphere za kreiranje vašeg prvog grafičkog prikaza i analitičkog modela koje zatim možete da koristite u priči SAP Analytics Cloud.
#XMSG More learning resources for Data Suite. Be aware that %%0 and %%1 are indexes that are replaced by links!
dataSuiteDialogLearnMore=Da biste saznali više, posetite %%0 na lokaciji sap.com ili pogledajte vodič %%1.
#XBUT Learn More link text (index %%0)
dataSuiteDialogLearnMoreLink=SAP Datasphere
#XBUT Getting Started Link Text (index %%1)
dataSuiteDialogGettingStartedLink=Početak rada
#XBUT Close button of the Data Suite Dialog
dataSuiteDialogCloseButton=Zatvori
