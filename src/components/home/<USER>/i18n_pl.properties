#~~~~~~~~~~~ DataSuite Welcome Card ~~~~~~~~~~~~~~~~~~~
#XTIT: Title of the Welcome Card
welcomeCardTitle=Witamy w SAP Datasphere
#XMSG: Welcome Text shown after Title
dataSuiteCardText=SAP Datasphere zapewnia architekturę danych biznesowych, która w sposób unikalny harmonizuje dane krytyczne dla misji w ramach całej organizacji, umo<PERSON><PERSON>wi<PERSON><PERSON>c ekspertom biznesowym podejmowanie optymalnych decyzji. Ofer<PERSON><PERSON> funk<PERSON>, które dotąd były dostęp<PERSON> oso<PERSON>no, w ramach ujednoliconej usługi pozwalającej na integrację danych, katal<PERSON><PERSON>ie, model<PERSON><PERSON> semanty<PERSON>, tworzenie hurtowni danych oraz wirtualizację obciążeń dla danych pochodzących z SAP oraz innych źródeł.
#XBUT Text of the Learn More Button
dataSuiteLearnMoreButtonText=Dow<PERSON><PERSON> się więcej

#XTXT: ARIA label for blog post cards within the SAP Datasphere Blogs card on the homepage.
#This ARIA label is read out loud by screenreaders for accessibility support. This is the only usage of the text, it is not visible anywhere.
# {0} will be replaced by the position (1 to 10) of the blog post cards, since we have in total 10 blog post cards.
newsfeedCardAriaLabel=Wpis na blogu {0}

#XTXT: ARIA label for the card title on the homepage.
# This ARIA label is read out loud by screenreaders for accessibility support. This is the only usage of the text, it is not visible anywhere.
# {0} will be replaced by the title of the card.
cardTitleAriaLabel=Karta {0}

#XTOL: Tooltip for avatar images in the newsfeed (is also used as an alternative text for screen readers or in case the image is unavailable)
newsfeedAvatarTooltip=Awatar autora elementu kanału

#XTIT: Infomation popup for missing authorizations
MissingAuth=Brak uprawnień

#XMSG: Information for missing authorizations
MissingAuthInfoText=Wygląda na to, że nie masz obecnie dostępu do tej funkcji. Aby uzyskać pomoc, skontaktuj się z administratorem.

#XMSG: Popup message when idp configuration was successfull
idpSuccessMessage=Weryfikacja konta SAML powiodła się. Zamknij to okno przeglądarki i kontynuuj proces konfiguracji.

#XMSG: Text for manage settings
manageSettings=Zarządzaj ustawieniami

#XMSG: Popup message for manage settings
manageSettingsMessage=Rejestrujemy wyszukiwanie otwartych obiektów przez użytkowników, aby zapewnić im spersonalizowane środowisko.\n\nMożesz w każdej chwili wyczyścić dane lub wyłączyć personalizację w oknie dialogowym Ustawienia na swoim koncie.

#~~~~~~~~~~~ Texts for the SAP Datasphere Homepage ~~~~~~~~~~~~~~~~
#XTIT: Welcoming title for the homepage. {0} will be replaced with the first name of the user, {1} will be replaced with the last name of the user.
objectPageHeaderTitle=Witaj, {0} {1}
#XFLD: Label for switch to toggle auto refresh of the data in the homepage cards
autoRefresh=Automatyczne odświeżanie danych
#XMSG: Message toast text, for when the auto refresh switch is turned on and the immediate refresh of all currently visible cards' data is done.
dataRefreshDone=Odświeżono karty
#BUT: Button to customize the homepage
customize=Dostosuj
#XFLD: Title for the placeholder in case no cards is selected and the homepage is empty
noCardsPlaceholderTitle=Dostosujmy stronę główną
#XFLD: Description for the placeholder in case no cards is selected and the homepage is empty
noCardsPlaceholderDescription=Zacznij dodawanie kart zgodnie ze swoimi uprawnieniami
#XFLD: Sub-header for cards in unified homepage
recentlyStarted=Ostatnio rozpoczęte - 5 pierwszych
#XFLD: Sub-header for Data Builder and Business Builder card if Activity Tracking is enabled
recentlyAccessed=Ostatnio otwarte - 5 pierwszych
#XFLD: Sub-header for Data Builder and Business Builder card if Activity Tracking is disabled
recentlyChanged=Ostatnio zmienione - 5 pierwszych
#XFLD: Card header title for data integration tasks card
dataIntegrationTasksCardTitle=Zadania integracji danych
#XFLD: Card header title for newsfeed/SAP Datasphere resources card
newsfeedCardTitle=Blog SAP Datasphere
#XFLD: Card header subtitle for newsfeed/SAP Datasphere resources card
newsfeedCardSubtitle=Ostatnie wpisy - 10 pierwszych
#XTIT: Title for "no data" screen in newsfeed/SAP Datasphere resources card
noBlogEntryTitle=Brak ostatnich wpisów
#XTIT: Title for "no data" screen in newsfeed/SAP Datasphere resources card
noBlogEntryDescription=Na blogu SAP znajdziesz inne interesujące wpisy.
#XTIT: Title for "no data" screen in data integration card
noRecentTasksTitle=Nie masz żadnych ostatnich zadań.
#XTXT: Explanatory text for "no data" screen on data integration card
noRecentTasksText=Jeśli się pojawią, wyświetlą się w tym miejscu.
#XTIT: Title for the illustrated message in case of data load error on any card
dataFetchErrorTitle=Nie można wczytać danych.
#XTIT: Title for the illustrated message in case of data load error on any card caused by the circuit breaker having a HanaState = red (unvailable)
circuitBreakerDataFetchErrorTitle=Baza danych czasu wykonania nie jest dostępna.
#XTIT: Title for the illustrated message during the tenant upgrade process
tenantUpgradeInProgressTitle=Trwa aktualizacja SAP Datasphere
#XFLD: Descriptions for the illustrated message in case of data load error on any card
dataFetchErrorDescription=Jeśli ten błąd będzie się powtarzał, skontaktuj się z pomocą techniczną i załącz ID korelacji: {0}
dataFetchErrorDescriptionNoCorrelationId=Jeśli ten błąd będzie się powtarzał, skontaktuj się z pomocą techniczną
#XFLD: Description for the illustrated message during the tenant upgrade process
tenantUpgradeInProgressDescription=Spróbuj krótko nacisnąć {0}
#XBTN: Button text for action on data load error
onErrorAction=Wczytaj ponownie
#XFLD: Card header title for quick actions card
quickActionsCardTitle=Szybkie czynności
#XFLD: Tooltip to edit the selected quick actions
editQuickActionsTooltip=Edytuj szybkie czynności
#XFLD: No Data Title for Quick Actions Card in case no Quick Action is selected
noQuickActionSelectedTitle=Nie wybrano szybkiej czynności
#XFLD: No Data Description for Quick Actions Card in case no Quick Action is selected
noQuickActionSelectedDescription=Dodaj szybkie czynności, które wyświetlą się w tym miejscu.
#XFLD: No Data Button Text for Quick Actions Card in case no Quick Action is selected
noQuickActionSelectedButton=Dodaj szybkie czynności
#XFLD: Title for the dialog to edit the selected quick actions
editQuickActionsDialogTitle=Edytuj szybkie czynności
#XTIT: Header text for edit quick actions dialog
editQuickActions=Edytuj szybkie czynności
#XBTN: Button text for clearing the selected quick actions in the edit quick actions dialog
clearSelectedQuickActions=Usuń wybór
#XMSG: Info message for quick actions dialog to tell the user how much quick actions they can select. {0} will represent the current number of selected quick actions
quickActionsLimit=Wybrano {0} z maksymalnie 4 szybkich czynności.
#XBTN: OK button text for edit quick actions dialog
okButton=OK
#XBTN: Cancel button text for edit quick actions dialog
cancelButton=Anuluj
#XTXT: Quick action to create a space
createSpace=Utwórz przestrzeń
#XTXT: Quick action to create a table
createTable=Utwórz tabelę
#XTXT: Quick action to create a graphical view
createGraphicalView=Utwórz widok graficzny
#XTXT: Quick action to create a SQL view
createSqlView=Utwórz widok SQL
#XTXT: Quick action to create an E/R model
createErModel=Utwórz diagram związków encji
#XTXT: Quick action to create a analytic model
createAnalyticModel=Utwórz model analityczny
#XTXT: Quick action to create a data flow
createDataFlow=Utwórz przepływ danych
#XTXT: Quick action to create a replication flow
createReplicationFlow=Utwórz przepływ replikacji
#XTXT: Quick action to create a replication flow
createTransformationFlow=Utwórz przepływ transformacji
#XTXT: Quick action to create a intelligent lookup
createIntelligentLookup=Utwórz inteligentne wyszukiwanie
#XTXT: Quick action to create a currency conversion views
createCurrencyConversionViews=Utwórz widoki przeliczania waluty
#XTXT: Quick action to create a unit conversion views
createUnitConversionViews=Utwórz widoki przeliczania jednostek
#XTXT: Quick action to create a task chain
createTaskChain=Utwórz łańcuch zadań
#XTXT: Quick action to create a dimension
createDimension=Utwórz wymiar
#XTXT: Quick action to create a fact
createFact=Utwórz fakt
#XTXT: Quick action to create a fact model
createFactModel=Utwórz model faktów
#XTXT: Quick action to create a consumption model
createConsumptionModel=Utwórz model wykorzystania
#XTXT: Quick action to create a data access control
createDataAccessControl=Utwórz kontrolę dostępu do danych
#XTXT: Quick action to create a authorization scenario
createAuthorizationScenario=Utwórz scenariusz uprawnień
#XBTN: Button text to navigate from task logs card to the data integration monitor
openDataIntegrationMonitor=Otwórz Monitor integracji danych
#XMSG: Error message when clicking a task log in the Data Integration Tasks card of a space, for which the user misses the DWC_INTEGRATOR.Read privilege.
missingDataIntegrationReadPrivileges=Musisz mieć uprawnienia do odczytu integracji danych dla przestrzeni {0}, aby mieć dostęp do tego obszaru.
#XBTN: Button text to navigate from the newsfeed/SAP Datasphere resources card to the SAP Datasphere blog
openBlog=Otwórz blog
#XFLD: The next run label of a schedule task in the task logs card, {0} is a date or time
nextRun=Następny przebieg: {0}
#XFLD: Completed status text for a scheduled task in the task logs card
COMPLETED=Zakończone
#XFLD: Running status text for a scheduled task in the task logs card
RUNNING=Aktywne
#XFLD: Failed status text for a scheduled task in the task logs card
FAILED=Niepowodzenie
#XFLD: Not triggered status text for a scheduled task in the task logs card
NOT_TRIGGERED=Niewyzwolone
#XFLD: Description text for filter toggle to only display list entries with an error state
showOnlyErrors=Pokaż tylko błędy
#XFLD: Card header title for spaces card
spacesCardTitle=Przestrzenie
#XFLD: Card header subtitle for spaces card
spacesCardSubtitle=Pamięć/status - 5 pierwszych
#XFLD: Table Header "Name"
thName=Nazwa
#XFLD: Table Header "Priority"
thPriority=Priorytet
#XFLD: Table Header "Storage"
thStorage=Pamięć
#XFLD: Table Header "Status"
thStatus=Status
#XFLD: Table Header "Space"
thSpace=Przestrzeń
#XFLD: Table Header "Type"
thType=Typ
#XFLD: Table Header "Last Changed"
thLastChanged=Ostatnia zmiana
#XTOL: Tooltip for create space button
tolCreateSpace=Utwórz przestrzeń
#XFLD: Space status "unknown"
unknown=Nieznane
#XFLD: Space status "active"
active=Aktywne
#XFLD: Space status "cold"
cold=Zimne
#XFLD: Space status "ok"
ok=Przetwarzanie
#XFLD: Space status "critical"
critical=Krytyczne
#XFLD: Space status "locked"
locked=Zablokowane
#XTOL: Tooltip - locked due to memory
lockedSpaceTooltipMemory=Przestrzeń zablokowana: Wyczerpano limit pamięci
#XTOL: Tooltip - locked due to storage
lockedSpaceTooltipStorage=Przestrzeń zablokowana: Wyczerpano limit dysku
#XTOL: Tooltip - locked due to storage and memory
lockedSpaceTooltipAll=Przestrzeń zablokowana: Wyczerpano limit pamięci i dysku
#XTOL: Tooltip - locked due to auditlog quota
lockedSpaceTooltipAuditlog=Przestrzeń zablokowana: Wyczerpano limit dziennika audytu
#XTOL: Tooltip - locked manually
lockedSpaceTooltipManual=Przestrzeń zablokowana: Zablokowano ręcznie
#XTOL: Critical space tooltip
criticalSpaceTooltip=Pozostała ilość wolnej pamięci jest krytycznie mała (poniżej 10%)
#XTOL: Green space tooltip
workingSpaceTooltip=Wykorzystanie pamięci mieści się w normalnym zakresie (do 90%)
#XTOL: Cold space tooltip
coldSpaceTooltip=Wykorzystano mniej niż 5% pamięci
#XFLD: Used {used} {byte-unit} of {assigned} {byte unit} - for disk and memory
memStorageUsage=Wykorzystano {0} {1} z {2} {3}
#XFLD: Used {used} {byte-unit} - for disk and memory
memStorageUsageNoQuota=Wykorzystano {0} {1}
#XFLD: Open Space Management
openSpaceManagement=Otwórz Zarządzanie przestrzenią
#XMSG: Error message when clicking a space in the spaces card, for which user misses the DWC_SPACES.Read privilege.
missingSpacesReadPrivileges=Musisz mieć uprawnienia do odczytu dla przestrzeni {0}, aby mieć dostęp do tego obszaru.
#XTIT: Title text for "no spaces" on the spaces card
noSpacesTitle=Nie znaleziono przestrzeni
#XFLD: Eplanatory texts with hints for "no spaces" on the spaces card
noSpacesTextWithCreate=Nie masz żadnych przypisanych przestrzeni. Utwórz przestrzeń lub skontaktuj się z administratorem.
noSpacesTextNoCreate=Nie masz żadnych przypisanych przestrzeni. Skontaktuj się z administratorem.
#XFLD: Card header title for data builder card
dataBuilderCardTitle=Edytor danych
#XFLD: Bottom link text for data builder card
bottomLinkTextForDataBuilderCard=Otwórz edytor danych
#XFLD Menu Item "Table" for the data builder card
dataBuilderCardMenuItemTable=Tabela
#XFLD Menu Item "Graphical View" for the data builder card
dataBuilderCardMenuItemGraphicalView=Widok graficzny
#XFLD Menu Item "SQL View" for the data builder card
dataBuilderCardMenuItemSqlView=Widok SQL
#XFLD Menu Item "Entity - Relationship Model" for the data builder card
dataBuilderCardMenuItemEntityRelationshipModel=Diagram związków encji
#XFLD Menu Item "Analytic Model" for the data builder card
dataBuilderCardMenuItemAnalyticModel=Model analityczny
#XFLD Menu Item "Data Access Control" for the data builder card
dataBuilderCardMenuItemDataAccessControl=Kontrola dostępu do danych
#XFLD Menu Item "Data Flow" for the data builder card
dataBuilderCardMenuItemDataFlow=Przepływ danych
#XFLD Menu Item "Replication Flow" for the data builder card
dataBuilderCardMenuItemReplicationFlow=Przepływ replikacji
#XFLD Menu Item "Transformation Flow" for the data builder card
dataBuilderCardMenuItemTransformationFlow=Przepływ transformacji
#XFLD Menu Item "Intelligent Lookup for the data builder card
dataBuilderCardMenuItemIntelligentLookup=Inteligentne wyszukiwanie
#XFLD Menu Item "Currency Conversion Views" for the data builder card
dataBuilderCardMenuItemCurrencyConversionViews=Widoki przeliczania waluty
#XFLD Menu Item "Unit Conversion Views" for the data builder card
dataBuilderCardMenuItemUnitConversionViews=Widoki przeliczania jednostek
#XFLD Menu Item "Task Chain" for the data builder card
dataBuilderCardMenuItemTaskChain=Łańcuch zadań
#XFLD: Card header title for business builder card
businessBuilderCardTitle=Edytor biznesowy
#XFLD: Bottom link text for data builder card
bottomLinkTextForBusinessBuilderCard=Otwórz edytor biznesowy
#XTOL: Tooltip to create a new business builder object
createBusinessBuilderObjectTooltip=Utwórz obiekt edytora biznesowego
#XTOL: Tooltip to create a new data builder object
createDataBuilderObjectTooltip=Utwórz obiekt edytora danych
#XFLD: Menu Item "Dimension" for business builder card
businessBuilderCardMenuItemDimension=Wymiar
#XFLD: Menu Item "Fact" for business builder card
businessBuilderCardMenuItemFact=Fakt
#XFLD: Menu Item "Fact Model" for business builder card
businessBuilderCardMenuItemFactModel=Model faktów
#XFLD: Menu Item "Consumption Model" for business builder card
businessBuilderCardMenuItemConsumptionModel=Model wykorzystania
#XFLD: Menu Item "Authorization Scenario" for business builder card
businessBuilderCardMenuItemAuthorizationScenario=Scenariusz uprawnień
#XTIT: Title for "no data" screen on business assets and data assets cards
noRecentObjectsTitle=Nie masz żadnych ostatnich obiektów
#XTXT Explanatory text for "no data" screen on business assets and data assets cards
noRecentObjectsText=Jeśli się pojawią, wyświetlą się w tym miejscu.
#XTIT Title for "no data" screen on cards with onlyErrors filter active
noRecentErrorsTitle=Nie masz żadnych ostatnich błędów
#~~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~ DataSuite ~~~~~~~~~~~~~~~~~~~
#XTIT: Title of the Data Suite Dialog itself
dataSuiteDialogTitle=Witamy w SAP Datasphere!
#XTXT Text after the Announcement Video if Unified Homepage Feature Flag is active
welcomeDialogVideoDescription=Ten film ułatwi Ci rozpoczęcie korzystania z SAP Datasphere! Znajdziesz w nim przegląd najważniejszych aplikacji oraz opis pierwszych kroków, które należy wykonać w SAP Datasphere, aby utworzyć pierwszy widok graficzny, a także model analityczny który możesz wykorzystać w raporcie SAP Analytics Cloud.
#XMSG More learning resources for Data Suite. Be aware that %%0 and %%1 are indexes that are replaced by links!
dataSuiteDialogLearnMore=Aby dowiedzieć się więcej, odwiedź %%0 na stronie sap.com lub przejdź do przewodnika %%1.
#XBUT Learn More link text (index %%0)
dataSuiteDialogLearnMoreLink=SAP Datasphere
#XBUT Getting Started Link Text (index %%1)
dataSuiteDialogGettingStartedLink=Pierwsze kroki
#XBUT Close button of the Data Suite Dialog
dataSuiteDialogCloseButton=Zamknij
