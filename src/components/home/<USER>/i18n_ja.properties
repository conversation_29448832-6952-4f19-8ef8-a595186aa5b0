#~~~~~~~~~~~ DataSuite Welcome Card ~~~~~~~~~~~~~~~~~~~
#XTIT: Title of the Welcome Card
welcomeCardTitle=SAP Datasphere へようこそ
#XMSG: Welcome Text shown after Title
dataSuiteCardText=SAP Datasphere では、組織全体でミッションクリティカルなデータを一意に統合するビジネスデータファブリックアーキテクチャが実現され、ビジネスエキスパートが最も影響力のある意思決定を行うことができます。以前は個別だった機能が、SAP データおよび非 SAP データ全体でのデータ統合、カタログ化、セマンティックモデル化、データウェアハウス化、ワークロードの仮想化のための統合サービスにまとめられています。
#XBUT Text of the Learn More Button
dataSuiteLearnMoreButtonText=詳細表示

#XTXT: ARIA label for blog post cards within the SAP Datasphere Blogs card on the homepage.
#This ARIA label is read out loud by screenreaders for accessibility support. This is the only usage of the text, it is not visible anywhere.
# {0} will be replaced by the position (1 to 10) of the blog post cards, since we have in total 10 blog post cards.
newsfeedCardAriaLabel=ブログ投稿 {0}

#XTXT: ARIA label for the card title on the homepage.
# This ARIA label is read out loud by screenreaders for accessibility support. This is the only usage of the text, it is not visible anywhere.
# {0} will be replaced by the title of the card.
cardTitleAriaLabel=カード {0}

#XTOL: Tooltip for avatar images in the newsfeed (is also used as an alternative text for screen readers or in case the image is unavailable)
newsfeedAvatarTooltip=フィードアイテム作成者のアバター

#XTIT: Infomation popup for missing authorizations
MissingAuth=権限がありません

#XMSG: Information for missing authorizations
MissingAuthInfoText=現在この機能にはアクセスできないようです。管理者に連絡してサポートを依頼してください。

#XMSG: Popup message when idp configuration was successfull
idpSuccessMessage=SAML アカウント検証が正常終了しました。このブラウザウィンドウを閉じて、設定プロセスを続行してください。

#XMSG: Text for manage settings
manageSettings=設定管理

#XMSG: Popup message for manage settings
manageSettingsMessage=パーソナライズされたエクスペリエンスを提供するために、ユーザによるオブジェクトの検索状況やオブジェクトを開いた時期が記録されます。\n\nユーザはいつでも記録済データをクリアするか、アカウント設定ダイアログでパーソナライゼーションをオフにすることができます。

#~~~~~~~~~~~ Texts for the SAP Datasphere Homepage ~~~~~~~~~~~~~~~~
#XTIT: Welcoming title for the homepage. {0} will be replaced with the first name of the user, {1} will be replaced with the last name of the user.
objectPageHeaderTitle=こんにちは {1} {0} さん
#XFLD: Label for switch to toggle auto refresh of the data in the homepage cards
autoRefresh=データの自動リフレッシュ
#XMSG: Message toast text, for when the auto refresh switch is turned on and the immediate refresh of all currently visible cards' data is done.
dataRefreshDone=カードがリフレッシュされました
#BUT: Button to customize the homepage
customize=カスタマイズ
#XFLD: Title for the placeholder in case no cards is selected and the homepage is empty
noCardsPlaceholderTitle=ホーム画面のカスタマイズを行う
#XFLD: Description for the placeholder in case no cards is selected and the homepage is empty
noCardsPlaceholderDescription=自分の権限に基づいてカードの追加を開始する
#XFLD: Sub-header for cards in unified homepage
recentlyStarted=最近の開始 - 上位 5 件
#XFLD: Sub-header for Data Builder and Business Builder card if Activity Tracking is enabled
recentlyAccessed=最近のアクセス - 上位 5 件
#XFLD: Sub-header for Data Builder and Business Builder card if Activity Tracking is disabled
recentlyChanged=最近の変更 - 上位 5 件
#XFLD: Card header title for data integration tasks card
dataIntegrationTasksCardTitle=データ統合タスク
#XFLD: Card header title for newsfeed/SAP Datasphere resources card
newsfeedCardTitle=SAP Datasphere ブログ
#XFLD: Card header subtitle for newsfeed/SAP Datasphere resources card
newsfeedCardSubtitle=最近の投稿 - 上位 10 件
#XTIT: Title for "no data" screen in newsfeed/SAP Datasphere resources card
noBlogEntryTitle=最近の投稿はありません
#XTIT: Title for "no data" screen in newsfeed/SAP Datasphere resources card
noBlogEntryDescription=興味深い他の投稿を探すために SAP ブログを閲覧します。
#XTIT: Title for "no data" screen in data integration card
noRecentTasksTitle=最新のタスクはありません
#XTXT: Explanatory text for "no data" screen on data integration card
noRecentTasksText=存在する場合、ここに表示されます。
#XTIT: Title for the illustrated message in case of data load error on any card
dataFetchErrorTitle=データをロードできません。
#XTIT: Title for the illustrated message in case of data load error on any card caused by the circuit breaker having a HanaState = red (unvailable)
circuitBreakerDataFetchErrorTitle=実行時データベースを利用できません。
#XTIT: Title for the illustrated message during the tenant upgrade process
tenantUpgradeInProgressTitle=SAP Datasphere を更新しています
#XFLD: Descriptions for the illustrated message in case of data load error on any card
dataFetchErrorDescription=この問題が繰り返し発生する場合は、相関 ID: {0} を添えてカスタマサポートに連絡してください。
dataFetchErrorDescriptionNoCorrelationId=この問題が繰り返し発生する場合は、カスタマサポートに連絡してください。
#XFLD: Description for the illustrated message during the tenant upgrade process
tenantUpgradeInProgressDescription={0} をすぐにクリックしてください
#XBTN: Button text for action on data load error
onErrorAction=リロード
#XFLD: Card header title for quick actions card
quickActionsCardTitle=クイックアクション
#XFLD: Tooltip to edit the selected quick actions
editQuickActionsTooltip=クイックアクション編集
#XFLD: No Data Title for Quick Actions Card in case no Quick Action is selected
noQuickActionSelectedTitle=クイックアクションが選択されていません
#XFLD: No Data Description for Quick Actions Card in case no Quick Action is selected
noQuickActionSelectedDescription=クイックアクションを追加するとここに表示されます。
#XFLD: No Data Button Text for Quick Actions Card in case no Quick Action is selected
noQuickActionSelectedButton=クイックアクションの追加
#XFLD: Title for the dialog to edit the selected quick actions
editQuickActionsDialogTitle=クイックアクション編集
#XTIT: Header text for edit quick actions dialog
editQuickActions=クイックアクション編集
#XBTN: Button text for clearing the selected quick actions in the edit quick actions dialog
clearSelectedQuickActions=選択をクリア
#XMSG: Info message for quick actions dialog to tell the user how much quick actions they can select. {0} will represent the current number of selected quick actions
quickActionsLimit=最大 4 件のクイックアクションから {0} 件を選択しました。
#XBTN: OK button text for edit quick actions dialog
okButton=OK
#XBTN: Cancel button text for edit quick actions dialog
cancelButton=キャンセル
#XTXT: Quick action to create a space
createSpace=スペースの作成
#XTXT: Quick action to create a table
createTable=テーブルの作成
#XTXT: Quick action to create a graphical view
createGraphicalView=グラフィックビューの作成
#XTXT: Quick action to create a SQL view
createSqlView=SQL ビューの作成
#XTXT: Quick action to create an E/R model
createErModel=ER モデルの作成
#XTXT: Quick action to create a analytic model
createAnalyticModel=分析モデルの作成
#XTXT: Quick action to create a data flow
createDataFlow=データフローの作成
#XTXT: Quick action to create a replication flow
createReplicationFlow=複製フローの作成
#XTXT: Quick action to create a replication flow
createTransformationFlow=変換フローの作成
#XTXT: Quick action to create a intelligent lookup
createIntelligentLookup=インテリジェントルックアップの作成
#XTXT: Quick action to create a currency conversion views
createCurrencyConversionViews=通貨換算ビューの作成
#XTXT: Quick action to create a unit conversion views
createUnitConversionViews=単位変換ビューの作成
#XTXT: Quick action to create a task chain
createTaskChain=タスクチェーンの作成
#XTXT: Quick action to create a dimension
createDimension=ディメンションの作成
#XTXT: Quick action to create a fact
createFact=ファクトの作成
#XTXT: Quick action to create a fact model
createFactModel=ファクトモデルの作成
#XTXT: Quick action to create a consumption model
createConsumptionModel=利用モデルの作成
#XTXT: Quick action to create a data access control
createDataAccessControl=データアクセス制御の作成
#XTXT: Quick action to create a authorization scenario
createAuthorizationScenario=権限シナリオの作成
#XBTN: Button text to navigate from task logs card to the data integration monitor
openDataIntegrationMonitor=データ統合モニタを開く
#XMSG: Error message when clicking a task log in the Data Integration Tasks card of a space, for which the user misses the DWC_INTEGRATOR.Read privilege.
missingDataIntegrationReadPrivileges=この領域にアクセスするには、スペース {0} に対するデータ統合の読み込み権限を取得する必要があります。
#XBTN: Button text to navigate from the newsfeed/SAP Datasphere resources card to the SAP Datasphere blog
openBlog=ブログを開く
#XFLD: The next run label of a schedule task in the task logs card, {0} is a date or time
nextRun=次回の実行: {0}
#XFLD: Completed status text for a scheduled task in the task logs card
COMPLETED=完了
#XFLD: Running status text for a scheduled task in the task logs card
RUNNING=実行中
#XFLD: Failed status text for a scheduled task in the task logs card
FAILED=失敗
#XFLD: Not triggered status text for a scheduled task in the task logs card
NOT_TRIGGERED=トリガ未了
#XFLD: Description text for filter toggle to only display list entries with an error state
showOnlyErrors=エラーのみ表示
#XFLD: Card header title for spaces card
spacesCardTitle=スペース
#XFLD: Card header subtitle for spaces card
spacesCardSubtitle=ストレージ/ステータス - 上位 5 件
#XFLD: Table Header "Name"
thName=名前
#XFLD: Table Header "Priority"
thPriority=優先度
#XFLD: Table Header "Storage"
thStorage=ストレージ
#XFLD: Table Header "Status"
thStatus=ステータス
#XFLD: Table Header "Space"
thSpace=スペース
#XFLD: Table Header "Type"
thType=タイプ
#XFLD: Table Header "Last Changed"
thLastChanged=最終変更
#XTOL: Tooltip for create space button
tolCreateSpace=スペースの作成
#XFLD: Space status "unknown"
unknown=不明
#XFLD: Space status "active"
active=有効
#XFLD: Space status "cold"
cold=コールド
#XFLD: Space status "ok"
ok=正常
#XFLD: Space status "critical"
critical=クリティカル
#XFLD: Space status "locked"
locked=ロック済み
#XTOL: Tooltip - locked due to memory
lockedSpaceTooltipMemory=ロック済みのスペース: メモリの割り当てがすべて使用されました
#XTOL: Tooltip - locked due to storage
lockedSpaceTooltipStorage=ロック済みのスペース: ディスクの割り当てがすべて使用されました
#XTOL: Tooltip - locked due to storage and memory
lockedSpaceTooltipAll=ロック済みのスペース: ディスクおよびメモリの割り当てがすべて使用されました
#XTOL: Tooltip - locked due to auditlog quota
lockedSpaceTooltipAuditlog=ロック済みのスペース: 監査ログの割り当てがすべて使用されました
#XTOL: Tooltip - locked manually
lockedSpaceTooltipManual=ロック済みのスペース: マニュアルでロックされました
#XTOL: Critical space tooltip
criticalSpaceTooltip=ストレージに残っている空き容量が非常に少なくなっています (10% 未満)
#XTOL: Green space tooltip
workingSpaceTooltip=使用済みのストレージ容量は通常の範囲内です (90% 以下)
#XTOL: Cold space tooltip
coldSpaceTooltip=使用されているストレージは 5% 未満です
#XFLD: Used {used} {byte-unit} of {assigned} {byte unit} - for disk and memory
memStorageUsage={2} {3} 中 {0} {1} を使用済み
#XFLD: Used {used} {byte-unit} - for disk and memory
memStorageUsageNoQuota={0} {1} を使用済み
#XFLD: Open Space Management
openSpaceManagement=スペース管理を開く
#XMSG: Error message when clicking a space in the spaces card, for which user misses the DWC_SPACES.Read privilege.
missingSpacesReadPrivileges=この領域にアクセスするには、スペース {0} に対するスペースの読み込み権限を取得する必要があります。
#XTIT: Title text for "no spaces" on the spaces card
noSpacesTitle=スペースが見つかりません
#XFLD: Eplanatory texts with hints for "no spaces" on the spaces card
noSpacesTextWithCreate=割り当てられているスペースがありません。スペースを作成するか、または管理者に連絡してください。
noSpacesTextNoCreate=割り当てられているスペースがありません。管理者に連絡してください。
#XFLD: Card header title for data builder card
dataBuilderCardTitle=データビルダ
#XFLD: Bottom link text for data builder card
bottomLinkTextForDataBuilderCard=データビルダを開く
#XFLD Menu Item "Table" for the data builder card
dataBuilderCardMenuItemTable=テーブル
#XFLD Menu Item "Graphical View" for the data builder card
dataBuilderCardMenuItemGraphicalView=グラフィックビュー
#XFLD Menu Item "SQL View" for the data builder card
dataBuilderCardMenuItemSqlView=SQL ビュー
#XFLD Menu Item "Entity - Relationship Model" for the data builder card
dataBuilderCardMenuItemEntityRelationshipModel=ER モデル
#XFLD Menu Item "Analytic Model" for the data builder card
dataBuilderCardMenuItemAnalyticModel=分析モデル
#XFLD Menu Item "Data Access Control" for the data builder card
dataBuilderCardMenuItemDataAccessControl=データアクセス制御
#XFLD Menu Item "Data Flow" for the data builder card
dataBuilderCardMenuItemDataFlow=データフロー
#XFLD Menu Item "Replication Flow" for the data builder card
dataBuilderCardMenuItemReplicationFlow=複製フロー
#XFLD Menu Item "Transformation Flow" for the data builder card
dataBuilderCardMenuItemTransformationFlow=変換フロー
#XFLD Menu Item "Intelligent Lookup for the data builder card
dataBuilderCardMenuItemIntelligentLookup=インテリジェントルックアップ
#XFLD Menu Item "Currency Conversion Views" for the data builder card
dataBuilderCardMenuItemCurrencyConversionViews=通貨換算ビュー
#XFLD Menu Item "Unit Conversion Views" for the data builder card
dataBuilderCardMenuItemUnitConversionViews=単位変換ビュー
#XFLD Menu Item "Task Chain" for the data builder card
dataBuilderCardMenuItemTaskChain=タスクチェーン
#XFLD: Card header title for business builder card
businessBuilderCardTitle=ビジネスビルダ
#XFLD: Bottom link text for data builder card
bottomLinkTextForBusinessBuilderCard=ビジネスビルダを開く
#XTOL: Tooltip to create a new business builder object
createBusinessBuilderObjectTooltip=ビジネスビルダオブジェクトを作成
#XTOL: Tooltip to create a new data builder object
createDataBuilderObjectTooltip=データビルダオブジェクトを作成
#XFLD: Menu Item "Dimension" for business builder card
businessBuilderCardMenuItemDimension=ディメンション
#XFLD: Menu Item "Fact" for business builder card
businessBuilderCardMenuItemFact=ファクト
#XFLD: Menu Item "Fact Model" for business builder card
businessBuilderCardMenuItemFactModel=ファクトモデル
#XFLD: Menu Item "Consumption Model" for business builder card
businessBuilderCardMenuItemConsumptionModel=利用モデル
#XFLD: Menu Item "Authorization Scenario" for business builder card
businessBuilderCardMenuItemAuthorizationScenario=権限シナリオ
#XTIT: Title for "no data" screen on business assets and data assets cards
noRecentObjectsTitle=最新のオブジェクトはありません
#XTXT Explanatory text for "no data" screen on business assets and data assets cards
noRecentObjectsText=存在する場合、ここに表示されます。
#XTIT Title for "no data" screen on cards with onlyErrors filter active
noRecentErrorsTitle=最新のエラーはありません
#~~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~ DataSuite ~~~~~~~~~~~~~~~~~~~
#XTIT: Title of the Data Suite Dialog itself
dataSuiteDialogTitle=SAP Datasphere へようこそ
#XTXT Text after the Announcement Video if Unified Homepage Feature Flag is active
welcomeDialogVideoDescription=このビデオで SAP Datasphere を始めましょう。ビデオでは最も重要なアプリの概要と、SAP Datasphere で最初のグラフィックビューを作成し、SAP Analytics Cloud のストーリーで利用できる分析モデルを作成するための第一歩を開始することができます。
#XMSG More learning resources for Data Suite. Be aware that %%0 and %%1 are indexes that are replaced by links!
dataSuiteDialogLearnMore=詳細については、sap.com の %%0 または %%1 ガイドを参照してください。
#XBUT Learn More link text (index %%0)
dataSuiteDialogLearnMoreLink=SAP Datasphere
#XBUT Getting Started Link Text (index %%1)
dataSuiteDialogGettingStartedLink=入門
#XBUT Close button of the Data Suite Dialog
dataSuiteDialogCloseButton=閉じる
