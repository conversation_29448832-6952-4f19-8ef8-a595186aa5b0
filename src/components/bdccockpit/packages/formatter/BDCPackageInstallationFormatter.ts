/** @format */

import { BDCPackageType } from "../../../../../shared/bdccockpit/Enums";
import { BDCSystem, SystemInfo } from "../../../../../shared/bdccockpit/Types";
import { PackageInstallationDialogClass } from "../controller/dialog/PackageInstallationDialog.controller";

export class BDCPackageInstallationFormatter {
  public static shouldBeVisible(
    this: PackageInstallationDialogClass,
    id: string,
    packageData: sap.ui.model.Model
  ): boolean {
    const { DWCO_BDC_GA } = sap.ui.getCore().getModel("featureflags").getData();
    const type = packageData?.["type"];
    const idVisibilityMap: Record<string, boolean> = {
      installLocationLabel: !!DWCO_BDC_GA && type && type === BDCPackageType.INSIGHT_APPLICATION,
      installLocationSelect: !!DWCO_BDC_GA && type && type === BDCPackageType.INSIGHT_APPLICATION,
    };
    return idVisibilityMap[id] ?? false;
  }

  public static shouldBeEnabled(this: PackageInstallationDialogClass, id: string): boolean {
    const idEnabledMap: Record<string, boolean> = {};
    return idEnabledMap[id];
  }

  public static getDialogMessageStripText(this: PackageInstallationDialogClass, type: BDCPackageType) {
    return type === BDCPackageType.INSIGHT_APPLICATION
      ? this.getText("messageDetails")
      : this.getText("activationMessageDetails");
  }

  public static getSourceSelectText(this: PackageInstallationDialogClass, systemData: BDCSystem) {
    return `${systemData?.name} ${systemData?.version ? `(System Version: ${systemData?.version})` : ""}`;
  }

  public static isSourceSelectEnabled(this: PackageInstallationDialogClass, systemData: SystemInfo | BDCSystem) {
    return (
      (systemData as SystemInfo)?.isInstallable || (systemData as BDCSystem)?.productsCompatibility?.compatible > 0
    );
  }

  public static getUninstallDialogMessage(this: PackageInstallationDialogClass, type: BDCPackageType) {
    return type === BDCPackageType.INSIGHT_APPLICATION
      ? this.getText("uninstallationDialogMessage")
      : this.getText("deactivationDialogMessage");
  }

  public static getUninstallDialogLearnMoreLink(this: PackageInstallationDialogClass, type: BDCPackageType) {
    return type === BDCPackageType.INSIGHT_APPLICATION
      ? "https://help.sap.com/docs/SAP_BUSINESS_DATA_CLOUD/f7acf8c9dad54e99b5ce5ebc633ed8e1/71222509a306408a9d144de4a4664881.html"
      : "https://help.sap.com/docs/SAP_BUSINESS_DATA_CLOUD/f7acf8c9dad54e99b5ce5ebc633ed8e1/b0c96bdf43204f46b9a195c1b28ede3b.html";
  }

  public static getStringWithTranslatedPlaceholder(
    this: PackageInstallationDialogClass,
    type: BDCPackageType,
    i18nKey: string
  ) {
    const isIntelligentApplication = type === BDCPackageType.INSIGHT_APPLICATION;
    return this.getText(i18nKey).replace("{0}", this.getText(isIntelligentApplication ? "uninstall" : "deactivate"));
  }
}
