
#XBCB: Breadcrumb
remoteTableMonitorBreadcrumb={spaceId}


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Count for Tables in table Toolbar
txtTableCount=Віддалені таблиці ({0})
#XBUT: Button to start replication
startReplication=Почати реплікацію
#XBUT: Button to stop replication
stopReplication=Зупинити реплікацію
#XFLD: Placeholder for Search field
txtSearch=Пошук
#XBUT: Tooltip for refresh button
txtRefresh=Оновити

#XFLD: Label for table column
txtConnection=З’єднання
#XFLD: Label for table column
txtConnectionLabel=З'єднання (бізнес-ім'я)
#XFLD: Label for table column
txtConnectionLabelTech=З'єднання (технічне ім'я)
#XFLD: Label for table column
txtTable=Таблиця
#XFLD: Label for table column
txtTableLabel=Бізнес-ім'я
#XFLD: Label for table column
txtTableLabelTech=Технічне ім'я
#XFLD: Label for table column
txtTableLabelNew=Об'єкт (бізнес-ім'я)
#XFLD: Label for table column
txtTableLabelTechNew=Об'єкт (технічне ім'я)
#XFLD: (Alternative) Label for table column "table"
txtTechnicalName=Технічне ім'я
#XFLD: Label for table column
txtAccessType=Доступ до даних
#XBUT: label for action button
loadNewSnapshot=Завантажити новий знімок
#XBUT: Tooltip for refresh button
changeRefreshFrequency=Змінити частоту оновлення
#XFLD: Label for table column
replicationType=Тип реплікацї
#XFLD: Label for table column
txtRefreshFrequency=Частота оновлення
#XFLD: Label for table column
txtRefreshFrequencyLabel=Частота
#XFLD: Label for table column
txtRefreshFrequencyLabelNew=Запланована частота
#XFLD: Label for table column
txtReplicationStatus=Статус
#XFLD: Label for table column
txtNumberOfRecords=Кількість записів
#XFLD: Label for table column
txtLatestChange=Найостанніше змінення (джерело)
#XFLD: Label for table column
txtLatestChangeNew=Останнє змінення (джерело)
#XFLD: Label for table column
txtLatestUpdate=Найостанніше оновлення
#XFLD: Label for table column
txtLatestUpdateNew=Останнє оновлення
#XFLD: Label for table column
txtInMemorySizeReplicaTableMB=Пам'ять, використана для сховища (МіБ)
#XFLD: Label for table column
txtInMemorySizeReplicaTableMBNeww=Розмір у пам'яті (МіБ)
#XFLD: Label for table column
txtInMemorySizeReplicaTableMBHeader=Розмір у пам'яті
#XFLD: Label for log details column
txtRemoteTableLogs=Журнали віддаленої таблиці
#XFLD: Label for log details column
txtRemoteTableLogsNew=Подробиці
#XFLD: Label for table column
txtDiskSizeReplicaTableInMB=Диск, використаний для сховища (МіБ)
#XFLD: Label for table column
txtDiskSizeReplicaTableInMBNeww=Розмір на диску (МіБ)
#XFLD: Label for table column
txtDiskSizeReplicaTableInMBHeader=Розмір на диску
#XFLD: Label for table column
txtInMemorySizeReplicaTableMBLabel=Пам'ять, використана для сховища
#XFLD: Label for table column
txtDiskSizeReplicaTableInMBLabel=Диск, використаний для сховища
#XFLD: Label for schedule owner column
txtScheduleOwner=Власник розкладу
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Указує, хто є власником розкладу
#XMSG: Error message for unknown replication type that gets logged in Kibana
unknownReplicationTypeError=Тип реплікації "{0}" не підтримується.
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Не застосовується

#XTIT: Title for Refresh Frequency Dialog which shows the table name
titleRefreshFrequencyDialog={0}
#XFLD: text inside Refresh Frequency Dialog
refreshFrequency=Частота оновлення
#XBUT: label for radio button in Refresh Frequency Dialog
none=Немає
#XBUT: label for radio button Refresh Frequency Dialog
realtime=Реальний час
#XMSG: info message in Refresh Frequency Dialog that real-time is not supported
infoDisableRealtimeNotSupported=Вимкнення реплікації даних у режимі реального часу для цієї віддаленої таблиці не підтримується.<br><br>Додаткову довідку див. <a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/82380e6f01c84fac898a285ac40a1e50.html?locale=en-US" >тут</a>.
txt_lnk_fabricVirtualTable=Віртуальна таблиця фабрики
#XMSG: info message that real-time check failed
infoRealtimeReplicationCheckFailed=Не вдалося перевірити реплікацію в режимі реального часу.
#XMSG: info message that real-time is supported
infoRealtimeReplicationSupported=Реплікація в режимі реального часу підтримується.
#XMSG: busy indicator for real-time replication check
infoRealtimeReplicationCheckIndicator=Перевірка реплікації в режимі реального часу...
#XMSG: info message in Refresh Frequency Dialog that real-time support requires re-deployment
infoRealtimeSupportReqReDeployment=Слід повторно розгорнути таблицю, якщо для цієї таблиці потрібно використовувати реплікацію в режимі реального часу.
#XMSG: info message in Refresh Frequency Dialog that real-time support requires adding of primary key
infoRealtimeSupportReqPrimKey=Слід додати первинний ключ, якщо для цієї таблиці потрібно використовувати реплікацію в режимі реального часу.
#XMSG: info message in Refresh Frequency Dialog that real-time support requires re-deployment and adding of primary key
infoRealtimeSupportReqReDeplAndPrimKey=Слід повторно розгорнути таблицю й додати первинний ключ, якщо для цієї таблиці потрібно використовувати реплікацію в режимі реального часу.
#XBUT: Button to Close Schedule Settings in Refresh Frequency Dialog if nothing was changed
close=Закрити
#XBUT: Button to Cancel Schedule Settings in Refresh Frequency Dialog
cancel=Скасувати
#XMSG For Scheduled text
scheduled=Заплановано
#XMSG For Scheduled text
paused=Призупинено
#XFLD: text for values shown for Ascending sort order
SortInAsc=Сортувати за зростанням
#XFLD: text for values shown for Descending sort order
SortInDesc=Сортувати за спаданням

#XFLD: text for values shown in column Replication Status
txtOff=Вимк.
#XFLD: text for values shown in column Replication Status
txtInitializing=Ініціалізація
#XFLD: text for values shown in column Replication Status
txtRunning=Виконується
#XFLD: text for values shown in column Replication Status
txtActive=Активний
#XFLD: text for values shown in column Replication Status
txtAvailable=Доступний
#XFLD: text for values shown in column Replication Status
txtError=Помилка
#XFLD: text for values shown in column Replication Status
txtPaused=На паузі
#XFLD: text for values shown in column Replication Status
txtDisconnected=Роз’єднано
#XMSG: error message for reading data from backend
txtReadBackendError=Під час читання з бекенду сталася помилка.

#XMSG: error message while stopping the replication
stopReplicationError=Під час зупинки реплікації таблиці "{0}" сталася помилка.
#XMSG: error message while stopping the replication
disableRealTimeReplicationError=Під час вимкнення реплікації даних у режимі реального часу для таблиці "{0}" сталася помилка.

#XMSG: success message for starting replication the first time
startReplicationSuccess=Виконується реплікація таблиці "{0}".
#XMSG: success message for loading new snapshot
loadNewSnapshotSuccess=Оновлення таблиці "{0}" останніми змінами.
#XMSG: success message for stopping replication
stopReplicationSuccess=Видалення реплікованих даних для таблиці "{0}".
#XMSG: success message for stopping replication
disableRealTimeReplicationSuccess=Триває вимкнення реплікації даних у режимі реального часу для таблиці "{0}".
#XBUT: label for combobox
txtReplicated=Репліковано
#XBUT: label for combobox
txtRemote=Віддалено

#XBUT: label for remote data access
REMOTE=Віддалений
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Реплікований (реальний час)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Реплікований (знімок)

#XBUT: label for radio button
txtNone=Немає
#XBUT: label for radio button
txtRealtime=Реальний час
#XMSG: error message during start/stop replication of a table (backend error)
ERROR_EXECUTING_REPLICATION=Під час реплікації таблиці "{0}" сталася помилка.
#XMSG: Error when illegal navigation is invoked
illegalNavigation=Наразі ви не член "{0}". Виберіть серед наведених далі просторів.
#XMSG: Error when user is not authorized to schedule tasks
authorizeWarn=Щоб запланувати завдання, потрібно авторизувати службу. <a href="">Авторизувати</a>
#XFLD: Message to schedule task
scheduleText=Запланувати реплікацію
#XFLD: Message to schedule task
scheduleTextLabel=Запланувати
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Створити розклад знімків
#XBUT: Drop down menu button to create a schedule
createScheduleNewLabel=Створити розклад
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Змінити розклад
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Видалити розклад
#XBUT: Label for scheduled replication
scheduledTxt=Заплановано
#XFLD: Label for table column
txtNextSchedule=Наступний запуск
#XFLD: Label for table column
txtNextScheduleNew=Наступний прогін за розкладом
#XFLD: Label for frequency column
everyLabel=Кожні
#XFLD: Plural Recurrence text for Hour
hoursLabel=Години
#XFLD: Plural Recurrence text for Day
daysLabel=Дні
#XFLD: Plural Recurrence text for Month
monthsLabel=Місяці
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Хвилини
#XFLD: Message for replicate table action
replicateTableText=Реплікація таблиці
#XFLD: Message for replicate table action
replicateTableLabelText=Реплікація даних
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Завантажити новий знімок
#XBUT: Drop down menu button to start data replication
startDataReplicationLabel=Почати реплікацію даних
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Видалити репліковані дані
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Увімкнути доступ у режимі реального часу
#XBUT: Drop down menu button to enable real time replication
enableRealTimeDataReplicationLabel=Увімкнути реплікацію даних у режимі реального часу
#XBUT: Drop down menu button to disable real time replication
disableRealTimeReplicationLabel=Вимкнути реплікацію даних у режимі реального часу
#XBUT: Drop down menu button to navigate to space connection list page
viewDocumentation=Переглянути документацію
#XFLD: Documentation Link for real time replication
goToConnectionsList=Перейти до спсику з’єднань
#XMSG: Warning message for removing the persisted data
RemoveReplication_Confirm_Msg=Справді змінити доступ до даних і видалити репліковані дані таблиці "{0}"?
#XMSG: error message for replication not started
txtReplicationStartError=Не вдалося запустити реплікацію, оскільки поточні репліковані дані не було видалено.
#XMSG: busy indicator for loading new snapshot
infoLoadingNewSnapshotIndicator=Завантаження нового знімка...
#XMSG: busy indicator for stopping real-time replication
infoStopRealtimeReplicationIndicator=Перш ніж завантажувати новий знімок, він зупиняє реплікацію в режимі реального часу.
#XMSG: busy indicator for starting batch replication
infoStartBatchReplicationIndicator=Починається пакетна реплікація...
#XMSG: message for real-time replication not supported
REALTIME_REPLICATION_NOT_SUPPORTED=Реплікація в реальному часі для "{0}" не підтримується
#XMSG: message for replication not supported (snapshot and real time replication)
REPLICATION_NOT_SUPPORTED=Дані для "{0}" не можна реплікувати: {1}.
#XMSG: message for replication not supported due to unsupported data type (snapshot and real time replication)
REPLICATION_NOT_SUPPORTED_UNSUPPORTED_DATA_TYPE=Дані для "{0}" не можна реплікувати, оскільки вони містять непідтримуваний тип даних.
#XMSG: message for replication not supported due to an issue with RMS
REPLICATION_NOT_SUPPORTED_RMS_ISSUE=Дані для "{0}" не можна реплікувати, оскільки вони не можуть підключитися до служби керування реплікацією: {1}.
#XMSG: message for conflicting task
Task_Already_Running=Конфліктне завдання вже виконується для таблиці "{0}".
#XMSG: message for Remote Source not found
REMOTE_SOURCE_NOT_FOUND=Віддалене джерело не знайдено.
#XMSG: message for Replication type missing
REPLICATION_TYPE_MISSING=Бракує типу реплікації.
#XMSG: message for run missing for cancellation
CANCEL_MISSING_RUN=Бракує прогону для скасування віддаленої таблиці "{0}".
#XMSG: message for failure to cancel the replication because it was already completed
CANCEL_COMPLETED_REPLICATION_RUN=Не вдалося скасувати процес реплікації таблиці "{0}", оскільки його вже завершено.
#XMSG: message for failure to cancel the replication because it was not started yet.
CANCEL_UNREACHABLE_REPLICATION_RUN=Не вдалося скасувати процес реплікації таблиці, оскільки реплікацію даних для таблиці "{0}" ще не розпочато.
#XMSG: message for unexpected replication status
UNEXPECTED_REPLICATION_STATUS=Неочікуваний статус реплікації для віддаленої таблиці "{0}".
#XMSG: message for failure to remove replicated data because the replication had been canceled.
REMOVE_CANCELED_REPLICATION_RUN=Не вдалося вилучити репліковані дані. Процес реплікації таблиці "{0}" уже скасовано.

#XFLD: Message for remote table replication logs
CANCELING_REPLICATION_TASK=Скасування процесу реплікації для віддаленої таблиці "{1}"...
#XFLD: Message for remote table replication logs
CANCELING_REPLICATION_TASK_SUCCESS=Для віддаленої таблиці "{1}" скасовано реплікацію.
#XFLD: Message for remote table replication logs
CANCELING_REPLICATION_TASK_FAILED=Не вдалося скасувати запущену реплікацію для віддаленої таблиці "{1}".
#XFLD: Message for remote table replication logs
DISABLING_REALTIME_TASK_SUCCESS=Реплікацію даних у режимі реального часу вимкнуто для віддаленої таблиці "{1}".
#XFLD: Message for remote table replication logs
DISABLING_REALTIME_TASK_FAILED=Не вдалося вимкнути реплікацію даних у режимі реального часу для віддаленої таблиці "{1}".
#XFLD: Message for remote table replication logs
CANCEL_REPLICATION_TASK=Дію реплікації скасовано запитом на таблицю "{1}"
#XMSG: Message for remote table replication logs
SWITCH_FROM_BATCH_TO_REALTIME=Змінення типу віддаленої таблиці "{1}" з пакетної реплікації на реплікацію в реальному часі.
#XMSG: Message for remote table replication logs
SWITCH_FROM_REALTIME_TO_BATCH=Змінення типу реплікації віддаленої таблиці "{1}" з реплікації в реальному часі на пакетну реплікацію.
#XFLD: Message for remote table replication logs
START_REALTIME_REPLICATION=Реплікація (у реальному часі) даних для віддаленої таблиці "{1}".
#XMSG: Message for remote table replication logs
DROP_SUBSCRIPTION_WARNING=Об'єкти, згенеровані для захоплення змін у даних у віддаленому джерелі, потрібно видалити вручну.
#XFLD: Message for remote table replication logs
START_BATCH_REPLICATION=Реплікація (пакетна) даних для віддаленої таблиці "{1}".
#XFLD: Message for remote table replication logs
STOP_REPLICATION=Вилучення реплікованих даних для віддаленої таблиці "{1}"
#XFLD: Message for remote table replication logs
DISABLE_REALTIME=Триває вимкнення реплікації даних у режимі реального часу для віддаленої таблиці "{1}".
#XFLD: Message for remote table replication logs
START_BATCH_REPLICATION_BLOCKED=Заміну (пакетних) даних для віддаленої таблиці "{1}" наразі заблоковано.
#XFLD: Message for remote table replication logs
START_BATCH_REPLICATION_SUCCESS=Дані репліковано (пакетно) для віддаленої таблиці {1}.
#XFLD: Message for remote table replication logs
STOP_REPLICATION_BLOCKED=Вилучення реплікованих даних для віддаленої таблиці "{1}" наразі заблоковано.
#XFLD: Message for remote table replication logs
START_REALTIME_REPLICATION_SUCCESS=Дані репліковано (у реальному часі) для віддаленої таблиці "{1}".
#XFLD: Message for remote table replication logs
STOP_REPLICATION_SUCCESS=Репліковані дані вилучено, а доступ до віддалених даних для віддаленої таблиці "{1}" успішно відновлено.
#XFLD: Message for remote table replication logs
START_BATCH_REPLICATION_FAILED=Не вдалося виконати реплікацію (пакетну) даних для віддаленої таблиці "{1}".
#XFLD: Message for remote table replication logs
START_REALTIME_REPLICATION_FAILED=Не вдалося виконати реплікацію (у реальному часі) даних для віддаленої таблиці "{1}".
#XFLD: Message for remote table replication logs
STOP_REPLICATION_FAILED=Не вдалося вилучити репліковані дані для віддаленої таблиці "{1}".
#XFLD: Message for remote table replication logs
TABLE_REPLICATION_FAILED=Не вдалося виконати реплікацію даних для віддаленої таблиці "{1}".
#XFLD: Message for remote table replication logs
START_REPLICATION_RECORD_COUNT_SUCCESS=Записи "{3}" репліковано для таблиці "{1}".
#XFLD: Message for remote table replication logs including number of partitions
START_REPLICATION_RECORD_COUNT_PARTITIONS_SUCCESS=Репліковано записи ({3}) для таблиці "{1}" у {4} розділах.
#XFLD: Message for successful replication of a single partition
START_REPLICATION_SINGLE_PARTITION_SUCCESS=Репліковано записи ({0}) у розділ для значень "{1}" <= {2} < "{3}".
#XFLD: Message for successful replication of a "others" partition
START_REPLICATION_OTHERS_PARTITION_SUCCESS=Репліковано записи ({0}) в "інші" розділи.
#XFLD: Message for pre-query returning no value for partitioning
PRE_QUERY_PARTITIONS_NO_VALUE=Попередній запит, який використовується для узгодження розділів із джерелом даних, не повернув жодного значення.
#XFLD: Message for pre-query of partitioning
PRE_QUERY_PARTITIONS=Попередній запит, який використовується для узгодження {1} розділів із джерелом даних, повернув таку кількість значень: {0}.
#XFLD: Message for remote table replication logs
START_REPLICATION_RECORD_COUNT_FAILED=Не вдалося полічити записи в таблиці "{1}".
#XFLD: Message for remote table replication logs
STOP_REPLICATION_RECORD_COUNT_SUCCESS=Репліковані записи "{3}" вилучено для віддаленої таблиці "{1}".
#XFLD: Message for remote table replication logs
DISABLE_REALTIME_RECORD_COUNT_SUCCESS=Репліковані записи "{3}" залишено для віддаленої таблиці "{1}".
#XFLD: Message for remote table replication logs
STOP_REPLICATION_RECORD_COUNT_FAILED=Не вдалося полічити записи в таблиці "{1}".
#XFLD: Cancel Run label
TEXT_CancelRun=Скасувати прогін
#XMSG: Cancel Run warning Message
Msg_CancelRun=Справді скасувати поточний прогін?
#XMSG: Re-start real-time replication action
txtPausedRealTimeReplStatusPopover=Щоб дозволити доступ в реальному часі,\nперезапустіть реплікацію для\nз’єднання {0}
#XFLD: Message for remote table replication logs
SUBSCRIPTION_APPLIED=Успішне початкове передавання даних для реплікації віддаленої таблиці "{1}" в режимі реального часу. Тепер процес реплікації в режимі реального часу автоматично додасть дельта-дані до віддаленої таблиці "{1}".
#XFLD: Message for remote table replication logs
REPLICATION_FILTER_CONDITION=Таблицю "{1}" репліковано з такою умовою фільтра: "{5}".
#XFLD: Message for remote table replication logs
START_REPLICATION_SYNC=Підготовка до реплікації даних у синхронному режимі
#XFLD: Message for remote table replication logs
START_REPL_REFRESH_SYNC=Підготовка до оновлення даних у синхронному режимі
#XFLD: Message for remote table replication logs
START_REPLICATION_ASYNC=Підготовка до реплікації даних в асинхронному режимі
#XFLD: Message for remote table replication logs
START_REPL_REFRESH_ASYNC=Підготовка до оновлення даних в асинхронному режимі
#XFLD: Message for remote table replication logs
DELEGATE_TO_HANA_ASYNC=Делегування ІД виконання завдання {0} до SAP HANA.
#XFLD: Message for remote table replication logs
SUBSCRIBED_TO_SRC_DATA_CHG=Підписано на зміни даних вихідної системи.
#XFLD: Message for remote table replication logs
START_INITIAL_LOAD=Вставлення даних у таблицю реплік.
#XFLD: Message for remote table replication logs
START_INITIAL_LOAD_CDI=Вставлення даних у таблицю реплік.
#XFLD: Message for remote table replication logs
APPLYING_DATA_CHANGES=Застосування змін даних до таблиці реплік.
#XFLD: Message for remote table replication logs
SWITCHED_DATA_ACCESS_BATCH=Перемикання доступу до даних (пакетного) на нову репліку.
#XFLD: Message for remote table replication logs
SWITCHED_DATA_ACCESS_REALTIME=Перемикання доступу до даних (у реальному часі) на нову репліку.
#XMSG: Real-time replication has been enabled for remote table
REALTIME_REPLICATION_ENABLED=Під час наступного прогону цього ланцюжка завдань буде завантажено новий знімок, а поточний режим реплікації в реальному часі буде змінено на режим пакетної реплікації. Дані більше не оновлюватимуться в реальному часі.
#XMSG: Remote table is only available in federation mode.
FEDERATION_ONLY=Таблиця "{0}" доступна лише у віддаленому режимі.
#XMSG: Remote table replication blocked as space is locked.
START_REMOTE_TABLE_REPLICATION_LOCKED_SPACE_ERROR=Віддалену таблицю "{1}" не можна реплікувати, оскільки простір "{2}" заблоковано.
##XFLD: Message for missing authorizations for remote table replication
REMOTE_TABLE_REPLICATION_MISSING_AUTH=Бракує повноважень для реплікації віддаленої таблиці.
#XFLD: Message for out of memory error
RESOURCE_LIMIT_ERROR_OUT_OF_MEMORY=Не вдалося виконати завдання через помилку нестачі пам'яті в базі даних SAP HANA.
#XFLD: Message for Admission Control Rejection
RESOURCE_LIMIT_ERROR_ADMISSION_CONTROL=Не вдалося виконати завдання через відхилення контролю допуску SAP HANA.
#XFLD: Message for SAP HANA back pressure problem
RESOURCE_LIMIT_ERROR_HANA_BACK_PRESSURE=Не вдалося виконати завдання через завелику кількість активних з'єднань SAP HANA.

#XFLD: Error while accessing pertitioning for a remote table
missingPrivilegeInRemoteTables=Недостатньо дозволів для виконання поділу віддаленої таблиці "{0}"

#MSG: Error header for realtime replication task
errorHeaderInitRealtime=Під час початкового завантаження для реплікації даних у реальному часі сталася помилка.
#MSG: Error header for realtime replication subscription
errorHeaderLoadRealtime=Під час реплікації в реальному часі сталася помилка.
#MSG: Error header for snapshot replication task
errorHeaderLoadSnapshot=Під час реплікації знімка сталася помилка.
#MSG: Error details for replication task "starting" phase (remote-table name, date, time)
errorDetailsTaskStarting={1} о {2} сталася внутрішня помилка під час запуску реплікації даних для віддаленої таблиці "{0}".
#MSG: Error details for replication task "preparing" phase (remote-table name, date, time, SQL error code)
errorDetailsTaskPreparingStage={1} о {2} сталася помилка SQL (код помилки: {3}) під час підготовки реплікації даних для віддаленої таблиці "{0}".
#MSG: Error details for replication task (remote-table name, date, time, SQL error code)
errorDetailsTaskGeneral={1} о {2} сталася помилка SQL (код помилки: {3}) під час реплікації даних для віддаленої таблиці "{0}".
#MSG: Error details for replication task (remote-table name, date, time)
errorDetailsTaskNoCode={1} о {2} під час запуску реплікації даних для віддаленої таблиці "{0}" сталася внутрішня помилка.
#MSG: Error details for replication task (remote-table name)
errorDetailsTaskFallback=Під час запуску реплікації даних для віддаленої таблиці "{0}" сталася внутрішня помилка.
#MSG: Error details for replication task "finalizing" phase (remote-table name, date, time, SQL error code)
errorDetailsTaskFinalizingStage={1} о {2} сталася помилка SQL (код помилки: {3}) під час завершення реплікації даних для віддаленої таблиці "{0}".
#MSG: Error details for unspecific real-time error (remote-table name, date, time)
errorDetailsRealtime={1} о {2} сталася помилка, яка перешкоджає реплікації даних віддаленої таблиці "{0}" в реальному часі.
#MSG: Error details for unspecific real-time error (remote-table name)
errorDetailsRealtimeFallback=Сталася помилка, яка перешкоджає реплікації даних віддаленої таблиці "{0}" в реальному часі.
#MSG: Error details for FVT-RMS real-time error (remote-table name) with Auto Retry option
errorDetailsRealtimeFallbackAutoRetry=Помилка блокує реплікацію в реальному часі віддаленої таблиці "{0}". Клацніть посилання нижче, щоб переглянути детальне повідомлення про помилку.\nСистема протягом тижня періодично намагається повторно активувати реплікацію в реальному часі. Якщо ви усунете помилку, ви можете клацнути "Повторити", щоб повторно активувати її вручну.
#MSG: Error details for connection-level real-time exception (remote-table name, date, time, exception id, connection name)
errorDetailsRealtimeConnection={1} о {2} у з''єднанні "{4}" сталася помилка з ідентифікатором {3}, яка перешкоджає реплікації даних віддаленої таблиці "{0}" в реальному часі.
#MSG: Error details for table-level real-time exception (remote-table name, date, time, exception id, other remote-table name)
errorDetailsRealtimeTable={1} о {2} під час реплікації віддаленої таблиці "{4}" в реальному часі сталася помилка з ідентифікатором {3}, яка перешкоджає реплікації даних віддаленої таблиці "{0}" в реальному часі.
#MSG: Advice on error to view details
errorAdvice=Скористайтеся посиланням нижче, щоб переглянути докладне повідомлення про помилку.
#MSG: Advice on error to view details and check logs (remote-table name)
errorAdviceLogs=Скористайтеся посиланням нижче, щоб переглянути докладне повідомлення про помилку та перевірити журнали завдань реплікації віддаленої таблиці "{0}".
#MSG: Advice on error to view details and validate connection (connection name)
errorAdviceConnection=Скористайтеся посиланням нижче, щоб переглянути докладне повідомлення про помилку та перевірити з''єднання "{0}".
#MSG: Advice on error to view details, validate connection, and check DP agent (connection name, agent name)
errorAdviceConnectionAgent=Скористайтеся посиланням нижче, щоб переглянути докладне повідомлення про помилку та перевірити з''єднання "{0}", а також, щоб перевірити журнали агента підготовки даних "{1}".
#MSG: Advice on error to view details and check monitor details (remote-table name)
errorAdviceTable=Скористайтеся посиланням нижче, щоб переглянути докладне повідомлення про помилку та перевірити дані моніторингу віддаленої таблиці "{0}".
#MSG: Advice on error to view details, check monitor details, and check DP agent (remote-table name, agent name)
errorAdviceTableAgent=Скористайтеся посиланням нижче, щоб переглянути докладне повідомлення про помилку та перевірити дані моніторингу "{0}", а також, щоб перевірити журнали агента підготовки даних "{1}".
#MSG: Advice to retry after the error is corrected
errorAdviceRetry=Після виправлення помилки скористайтеся дією "Повторити", щоб продовжити реплікацію в реальному часі.
#XFLD Label Go to connections link
goToConnections=Перейти до з’єднань
#XFLD Label Go to Details link
goToDetails=Перейти до подробиць
#XFLD Label Go to configuration link
goToDpAgents=Перейти до конфігурації
#XFLD Label go to other object details link
goToOtherDetails=Перейти до подробиць {0}
#XFLD Label Retry link
retry=Повторити спробу
#XFLD Show more
showMoreText=Показати більше...

#XFLD: text retry
Retry=Підтвердити
#XMG: Retry confirmation text
retryConfirmationTxt=Остання реплікація в режимі реального часу завершилася з помилкою. \n Переконайтеся, що помилку виправлено й що реплікацію в режимі реального часу можна перезапустити.
#XMG: Retry success text
retrySuccess=Процес успішно розпочато повторно.
#XMG: Retry fail text
retryFail=Не вдалося повторити процес.
#XMG: Retry fail text
RETRY_FAILED_ERROR=Не вдалося повторно виконати через помилку.

#XMSG: Message for create statistics in remote table
START_CREATE_STATISTICS=Створення статистики типу {1} для {0}.
#XMSG: Message for start statistics
CREATE_STATISTICS_SUCCESS=Статистику типу {1} для {0} успішно створено.
#XMSG: Message for error in creating statistics
CREATE_STATISTICS_FAIL=Не вдалося створити статистику типу {1} для {0}.

#XMSG: Message for starting refresh statistics
START_REFRESH_STATISTICS=Оновлення статистики для {0}.
#XMSG: Success message for refresh statistics
REFRESH_STATISTICS_SUCCESS=Статистику типу {1} для {0} успішно оновлено.
#XMSG: Error message for refresh statistics
REFRESH_STATISTICS_FAIL=Не вдалося оновити статистику типу {1} для {0}.

#XMGS: Message for Start changing the statistics type
START_ALTER_STATISTICS=Змінення типу статистики на {1} для {0}.
#XMSG: Success message for altering statistics type
ALTER_STATISTICS_SUCCESS=Статистику типу {1} для {0} успішно змінено.
#XMSG: Error message for altering statistics type
ALTER_STATISTICS_FAIL=Не вдалося змінити статистику типу {1} для {0}.

#XMSG: Message for starting to drop statistics from remote table
START_DROP_STATISTICS=Скидання статистики для {0}.
#XMSG: Success message for drop statistics
DROP_STATISTICS_SUCCESS=Статистику для {0} успішно видалено.
#XMSG: Error message for drop statistics
DROP_STATISTICS_FAIL=Не вдалося скинути статистику для {0}.

#XMSG: Log to let know that partitioning has been removed automaticaly
PARTITIONING_AUTO_DELETE=Інформацію про розділення автоматично вилучено, оскільки розділеного стовпчика більше не існує в таблиці "{1}".
#XMSG: Log to let know that real-time replication does not support partitioning
REPLICATION_REAL_TIME_PARTITION=Розділи не можна використовувати для реплікації в реальному часі.
#XMSG: Log to let know that CDI real-time replication should use at least dpagent version 2.6.1
REPLICATION_REAL_TIME_CDI=Не рекомендовано використовувати DPAgent версії ({4}), що передує 2.6.1, для реплікації в реальному часі через адаптер CDI.
#XMSG: Log to let know that CDI real-time replication should use at least dpagent version 2.6.1
REPLICATION_REAL_TIME_CDI_NO_VERSION=Не вдалося зчитати версію DPAgent. Не рекомендовано використовувати версію, що передує 2.6.1, для реплікації в реальному часі через адаптер CDI.

#XMSG: log warning about multiple remote tables pointing to the same source and being replicated (fvt)
REPLICATION_REPLICA_SHARING=Віддалена таблиця {1} вказує на ту саму вихідну таблицю, що й інші віддалені таблиці, і для цієї вихідної таблиці доступні інші реплікації. Рекомендується зберегти лише одну реплікацію.

#XMSG: log warning about retring running (fvt)
REPLICATION_REAL_TIME_AUTO_RETRY=Реплікацію в реальному часі заблоковано, і ми намагаємося повторно активувати її.
REPLICATION_REAL_TIME_FIRST_AUTO_RETRY=Повторна спроба реплікації в реальному часі.
REPLICATION_REAL_TIME_SUCCESSFUL_AUTO_RETRY=Реплікацію в реальному часі повторно активовано.
REPLICATION_REAL_TIME_LAST_AUTO_RETRY=Не вдалося повторно активувати реплікацію в реальному часі.

partitionButton=Розділ
OBJECT_NAME=Вибрана таблиця
Column=Стовпчик
Parallel_Process=Паралельні процеси
Partitions=Розділи
Add_Partitions=Додати розділи
btnSave=Зберегти
btnClear=Очистити
btnRestore=Відновити
btnCancel=Скасувати
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtConnectionTooltip=Показує ім’я з’єднання, до якого належить віддалена таблиця
#XFLD: tooltip for table column
txtTableTooltip=Показує ім’я віддаленої таблиці
#XFLD: tooltip for table column
txtDataAccessTooltip=Показує, як зараз здійснюється доступ до даних
#XFLD: tooltip for table column
txtRefreshFreqTooltip=Показує, як часто виконується реплікація даних. Значення тут показує передбачувану частоту, яка, залежно від стану віддаленої таблиці, можливо, ще не досягнута
#XFLD: tooltip for table column
txtStatusTooltip=Показує статус віддаленої таблиці
#XFLD: tooltip for table column
txtLatestChangeTooltip=Показує час останньої зміни даних у джерелі.
#XFLD: tooltip for table column
txtNumberOfRecordsTooltip=Показує кількість записів у таблиці.
#XFLD: tooltip for table column
txtLatestUpdateTooltip=Показує, коли дані востаннє було успішно оновлено.
#XFLD: tooltip for table column
txtNextRunTooltip=Якщо для віддаленої таблиці встановлено розклад, подивіться, на коли заплановано наступний запуск.
#XFLD: tooltip for table column
txtInMemorySizeTooltip=Відстежує, яку кількість пам’яті споживає ваша віддалена таблиця
#XFLD: tooltip for table column
txtDiskSizeTooltip=Відстежує, яку кількість дискового простору споживає ваша віддалена таблиця
#XMSG: Expired text
txtExpired=Прострочено
#XMSG: Open BW4 Cockpit button text
openBw4CockpitTxt=Відкрити пульт керування мосту SAP BW

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Об''єкт "{0}" не можна додати до ланцюжка завдань.
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE_REMOTETABLE=Об''єкт "{0}": тип реплікації буде змінено.

#XMSG: Info message when there is shared replica table
sharedReplicaInfoText=Віддалена таблиця вказує на ту саму вихідну таблицю, що й інші віддалені таблиці.
sharedReplicaWarningText=Ця віддалена таблиця вказує на ту саму вихідну таблицю, що й інші віддалені таблиці, які вже ввімкнули або запланували реплікацію. Ви не можете ввімкнути або запланувати реплікацію.
sharedReplicaErrorText=Реплікацію не ввімкнено. Віддалена таблиця вказує на ту саму вихідну таблицю, що й інші віддалені таблиці, для яких реплікацію вже ввімкнено або заплановано. Використовуйте репліковані дані цих таблиць чи видаліть відповідний розклад і репліковані дані, щоб дозволити планування розкладу та реплікацію для віддаленої таблиці. Якщо таблиця, дані якої було репліковано, знаходиться в іншому просторі, потрібно буде впорядкувати їх для спільного використання в цьому просторі.

#XBTN: Button to close the dialog
cancelText=Скасувати
okText=Усе одно продовжити

#XMSG: warning message to show the change from realtime replication to batch
warn_realtime=Наразі реплікація даних відбувається в режимі реального часу. Якщо завантажити новий знімок, тип реплікації буде змінено на пакетний, а дані більше не оновлюватимуться в реальному часі.
warn_changeto_batch=У ланцюжку завдань використовується віддалена таблиця. Під час наступного прогону ланцюжка завдань тип доступу до даних віддаленої таблиці буде змінено, і дані більше не вивантажуватимуться в реальному часі.
txt_lnk_impactAnalysis=Аналіз впливу та походження даних

#XMSG: Replication will change
txt_replication_change=Тип реплікації буде змінено.
txt_repl_viewdetails=Переглянути подробиці
msg_repl_viewdet_schedule_content=За визначеним розкладом прогін зі створенням нового знімка відбудеться {0}. У наслідок цього режим реплікації буде змінено з реплікації в реальному часі на пакетну реплікацію, а дані більше не оновлюватимуться в реальному часі.
msg_repl_viewdet_taskchain_content=Наразі реплікація даних відбувається в режимі реального часу. У ланцюжку завдань використовується віддалена таблиця. Під час наступного прогону ланцюжка завдань тип реплікації буде змінено на пакетний, і дані більше не оновлюватимуться в реальному часі.

#XFLD: dialog section
Background=Передумова:
#XFLD: dialog section
Cause=Причина:
#XFLD: dialog section
Solution=Рішення:
#XFLD: dialog section
Warning=Застереження

dropSubscriptionCancelRealTime=Ви збираєтеся скасувати активацію реплікації в режимі реального часу для віддаленої таблиці "{1}".
#MSG: Before drop HANA subscription, prompt the user with warning
dropSubscriptionSwitch=Ви збираєтеся змінити тип реплікації з реплікації в реальному часі на пакетну для віддаленої таблиці "{1}".
#MSG: Before drop HANA subscription, repeat the warning in the task log
dropSubscriptionSwitched=Тип реплікації для віддаленої таблиці "{1}" буде змінено з реплікації в реальному часі на пакетну реплікацію.
#MSG: Before drop HANA subscription, prompt the user with warning
dropSubscriptionRemove=Ви збираєтеся вилучити репліковані дані з віддаленої таблиці "{1}", для якої активовано доступ до даних у реальному часі.
#MSG: Before drop HANA subscription, prompt the user with warning
dropSubscriptionDisableRealTime=Ви збираєтеся вимкнути реплікацію даних у режимі реального часу з віддаленої таблиці "{1}".
#MSG: Before drop HANA subscription, repeat the warning in the task log
dropSubscriptionRemoved=Репліковані дані буде вилучено з віддаленої таблиці "{1}", для якої активовано доступ до даних у реальному часі.
#MSG: Before drop HANA subscription, explain about database triggers
dropSubscriptionTrigger=Активна віддалена підписка з віддаленої таблиці "{1}" у вашій інстанції SAP HANA Cloud згенерувала тригери та тіньову таблицю для захоплення змін у даних у віддаленій базі даних.
#MSG: Before drop HANA subscription, explain about ABAP subscription
dropSubscriptioninAbap=Активна віддалена підписка з віддаленої таблиці "{1}" у вашій інстанції SAP HANA Cloud створила підписку для захоплення змін у даних у віддаленій системі ABAP.
#MSG: Before drop HANA subscription, tell that connection has exceptions
dropSubscriptionHasExceptions=Наразі ця реплікація в реальному часі має винятки на рівні з'єднання.
#MSG: Before drop HANA subscription, tell that connection is paused
dropSubscriptionIsPaused=Наразі цю реплікацію в реальному часі призупинено на рівні з'єднання.
#MSG: Before drop HANA subscription, tell that DP Agent is disconnected
dropSubscriptionIsDisconnected=Наразі агент підготовки даних від'єднано.
#MSG: Before drop HANA subscription, tell that the connection is invalid
connectionIsInvalid=Наразі вихідна система недоступна.
#MSG: Before drop HANA subscription, tell that only target side is deleted
dropSubscriptionImpact=Якщо продовжити, буде видалено тільки віддалену підписку на цільовій стороні.
#TODO: Same with "would"?
#MSG: Before drop HANA subscription, tell that database triggers must be deleted manually
dropSubscriptionImpactTrigger=Вам потрібно вручну видалити тіньову таблицю та тригери на вихідній стороні з віддаленого об''єкта {6} з унікальним префіксом системного об''єкта "{5}", як описано в нотатці SAP 3307334.
#TODO: Same with "would"?
#MSG: Before drop HANA subscription, tell that ABAP subscription must be deleted manually
dropSubscriptionImpactOnAbap=Вам потрібно вручну видалити підписку на основі ABAP на вихідній стороні "{6}", як описано в нотатці SAP 3307334.
#TODO: Same with "would"?
#MSG: Before drop HANA subscription, recommend to process all exceptions
dropSubscriptionProcess=Тому радимо обробити всі винятки для цього з'єднання, перш ніж продовжити.
#TODO: Same with "try it again"?
#MSG: Before drop HANA subscription, recommend to resume real-time replication
dropSubscriptionResume=Тому радимо перезапустити реплікацію в реальному часі на рівні з'єднання, перш ніж продовжити.
#TODO: Same with "try it again"?
#MSG: Before drop HANA subscription, recommend to reconnect the DP agent
dropSubscriptionReconnect=Тому радимо повторно під'єднати агент підготовки даних, перш ніж продовжити.
#TODO: Same with "try it again"?
#MSG: Before drop HANA subscription, recommend to directly trigger the same activity again
dropSubscriptionIgnore=Ви можете ігнорувати цю помилку у випадку, коли запускаєте ту саму дію безпосередньо.
#XFLD: text proceed
continueAnyway=Усе одно продовжити
#XMSG: Warning text when performing mass operation of removing replicated data.
massReplicationRemoveWarnText=Справді змінити доступ до даних на віддалений і вилучити репліковані дані?
#XMSG: warning text when performing mass operation of removing replication on Partial set of selected tables
massReplicationPartialRemoveWarnText=Вибрані віддалені таблиці ({0} з {1}) отримали репліковані дані, які не можна вилучити. \nСправді змінити доступ до даних на віддалений і вилучити репліковані дані?
#XMSG: error message while mass stopping the replication
stopMassReplicationError=Схоже, під час зупинки реплікації таблиці сталася помилка.
#XMSG: success message for stopping replication
stopMassReplicationSuccess=Триває вилучення реплікованих даних для вибраних таблиць.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Призначити мені розклад
#XBUT: Pause schedule menu label
pauseScheduleLabel=Призупинити розклад
#XBUT: Resume schedule menu label
resumeScheduleLabel=Відновити розклад
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Під час вилучення розкладів сталася помилка.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Під час призначення розкладів сталася помилка.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Під час призупинення розкладів сталася помилка.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Під час відновлення розкладів сталася помилка.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Видалення розкладів ({0})
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Змінення власника розкладів ({0})
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Призупинення розкладів ({0})
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Відновлення розкладів ({0})

#XMSG: Message for starting refresh statistics
VALIDATING_REMOTE_TABLES=Перевірка віддалених таблиць ({0})
#XMSG: <table name which was validated>: not changed
NOT_CHANGED={0}: не змінено
#XMSG: <table name which was validated>: changed
CHANGED={0}: змінено
#XMSG: No changes detected
NO_CHANGES_DETECTED=Змін не виявлено
#XMSG Updating repository
UPDATING_REPOSITORY=Оновлення репозиторію
#XMSG Repository updated
REPOSITORY_UPDATED=Репозиторій оновлено
#XMSG Remote Tables validated
REMOTE_TABLES_VALIDATED=Віддалені таблиці перевірено
#XMSG Reading remote tables from repository
READING_REMOTE_TABLES_FROM_REPOSITORY=Зчитування віддалених таблиць із репозиторію
#XMSG {} remote tables read from repository
REMOTE_TABLES_READ_FROM_REPOSITORY=Віддалені таблиці ({0}) зчитано з репозиторію
#XMSG Reading {} from remote source
READING_FROM_REMOTE_SOURCE=Зчитування ресурсу ''{0}'' з віддаленого джерела
#XMSG {} read from remote source
READ_FROM_REMOTE_SOURCE={0} зчитано з віддаленого джерела
#XMSG Remote table {} not found in remote source
REMOTE_TABLE_NOT_FOUND_IN_REMOTE_SOURCE=Віддалену таблицю ''{0}'' не знайдено у віддаленому джерелі
#XMSG Error during execution
ERROR_DURING_EXECUTION=Під час виконання сталася помилка: {0}

#XMSG: error message when none of the selected tables have passed validation to remove replication.
massRemoveRepAllSelectionError=Вибрані таблиці неприпустимі для масового вилучення реплікації через помилки або попередження. \nВиберіть окрему таблицю і спробуйте вилучити реплікацію.
#XBUT: Select Columns Button
selectColumnsBtn=Вибрати стовпчики
#XFLD: Refresh tooltip
TEXT_REFRESH=Оновити
#XFLD: Select Columns tooltip
text_selectColumns=Вибрати стовпчики
#MSG: Error message when source system is disconnected/agent is unreachable
startDataReplication=Почати реплікацію даних не можна.
#MSG: Error message when source system is disconnected/agent is unreachable
removeReplicatedData=Вилучити репліковані дані не можна.
#MSG: Error message when source system is disconnected/agent is unreachable
enableRealTimeReplication=Увімкнути реплікацію даних у режимі реального часу не можна.
#MSG: Error message when source system is disconnected/agent is unreachable
disableRealTimeReplication=Вимкнути реплікацію даних у режимі реального часу не можна.
#MSG: Before starting replication of data, recommend to disable real-time replication or reconnect the agent
disableRealTimeReconnectAgentDataReplication=Вимкніть реплікацію даних у реальному часі або під'єднайте агент підготовки даних повторно, щоб почати реплікацію даних.
#MSG: Before starting replication of data, recommend to disable real-time replication or reconnect the source system
disableRealTimeReconnectSourceDataReplication=Вимкніть реплікацію даних у реальному часі або під'єднайте вихідну систему повторно, щоб почати реплікацію даних.
#MSG: Before starting replication of data, recommend to reconnect the agent
reconnectAgentStartReplication=Під'єднайте агент підготовки даних повторно, щоб почати реплікацію даних.
#MSG: Before starting replication of data, we recommend reconnecting the source system.
reconnectSourceStartReplication=Під'єднайте вихідну систему повторно, щоб почати реплікацію даних.
#MSG: Before removing replicated data, recommend to reconnect the agent
reconnectAgentRemoveReplicatedData=Під'єднайте агент підготовки даних повторно, щоб вилучити репліковані дані.
#MSG: Before removing replicated data, recommend to reconnect the source
reconnectSourceRemoveReplicatedData=Під'єднайте вихідну систему повторно, щоб вилучити репліковані дані.
#MSG: Before enabling Real-Time replication, recommend to reconnect the agent
reconnectAgentEnableRealTimeReplication=Під'єднайте агент підготовки даних повторно, щоб активувати реплікацію даних у режимі реального часу.
#MSG: Before enabling Real-Time replication, recommend to reconnect the source
reconnectSourceEnableRealTimeReplication=Під'єднайте вихідну систему повторно, щоб активувати реплікацію даних у режимі реального часу.
#XMSG: error message while mass stopping the replication
massDisableAllSelectionError=Для вибраних таблиць не можна вимкнути реплікацію даних у режимі реального часу.<br><br>Додаткову довідку див. <a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/82380e6f01c84fac898a285ac40a1e50.html?locale=en-US" >тут</a>.
#XMSG: error message while mass stopping the replication
massDisableConfirmation=Вибрані об'єкти використовують реплікацію даних у режимі реального часу.<br><br>Ці об'єкти мають згенеровані тригери та тіньові таблиці для захоплення дельта-змін у віддаленій базі даних. Такі об'єкти потрібно видалити вручну. Щоб отримати докладні інструкції, перегляньте журнал окремо для кожної віддаленої таблиці. <br><br> Справді вимкнути?
#XMSG: error message while mass stopping the replication
stopMassDisableError=Під час вимкнення реплікації даних у режимі реального часу сталася помилка.
#XMSG: success message for stopping replication
stopMassDisableSuccess=Триває вимкнення реплікації даних у режимі реального часу для вибраних таблиць.
#XMSG: error message while mass stopping the replication
massDisablePartialRemoveWarnText=Вибрані об'єкти використовують реплікацію даних у режимі реального часу. <br><br>Деякі об'єкти мають згенеровані тригери та тіньові таблиці для захоплення дельта-змін у віддаленій базі даних. Такі об'єкти потрібно видалити вручну. Щоб отримати докладні інструкції, перегляньте журнал окремо для кожної віддаленої таблиці. <br><br> Справді вимкнути?
#XMSG: error message while mass stopping the replication
massDisableFewTablesConfirmation=Реплікацію даних у режимі реального часу можна вимкнути тільки для деяких із вибраних об''єктів ({0} з {1}).<br><br>Додаткову довідку див. <a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/82380e6f01c84fac898a285ac40a1e50.html?locale=en-US" >тут</a>.<br><br>Справді вимкнути?
#XMSG: error message while mass stopping the replication
massDisableWithWarningFewTablesConfirmation=Реплікацію даних у режимі реального часу можна вимкнути тільки для деяких із вибраних об''єктів ({0} з {1}).<br><br>Додаткову довідку див. <a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/82380e6f01c84fac898a285ac40a1e50.html?locale=en-US" >тут</a>.<br><br>Деякі з об''єктів мають згенеровані тригери та тіньові таблиці для захоплення дельта-змін у віддаленій базі даних. Такі об''єкти потрібно видалити вручну. Щоб отримати докладні інструкції, перегляньте журнал окремо для кожної віддаленої таблиці.<br><br>Справді вимкнути?
#XLBL Disable button text
disable=Вимкнути
#XSM Disable confirmation
massDisableAllWithoutWarning=Справді вимкнути реплікацію даних у режимі реального часу для всіх вибраних таблиць?

#MSG: Error message when source system is disconnected/agent is unreachable
BATCH_REPLICATION_NOT_SUPPORTED_AGENT_DISCONNECTED=Не вдалося розпочати реплікацію даних, оскільки агент надання даних недоступний. Під'єднайте агент надання даних повторно й спробуйте знову.
#MSG: Error message when source system is disconnected/agent is unreachable
BATCH_REPLICATION_NOT_SUPPORTED_AGENT_DISCONNECTED_FROM_REALTIME=Не вдалося розпочати реплікацію даних, оскільки агент надання даних недоступний. Вимкніть реплікацію даних у реальному часі або під'єднайте агент надання даних повторно й спробуйте знову.
#MSG: Error message when source system is disconnected/agent is unreachable
REALTIME_REPLICATION_NOT_SUPPORTED_AGENT_DISCONNECTED=Не вдалося активувати реплікацію даних у реальному часі, оскільки агент надання даних недоступний. Під'єднайте агент надання даних повторно й спробуйте знову.
#MSG: Error message when source system is disconnected/agent is unreachable
REMOVE_REPLICATED_DATA_NOT_SUPPORTED_AGENT_DISCONNECTED=Не вдалося вилучити репліковані дані, оскільки агент надання даних недоступний. Під'єднайте агент надання даних повторно й спробуйте знову.
#MSG: Error message when source system is disconnected/agent is unreachable
BATCH_REPLICATION_NOT_SUPPORTED_SOURCE_UNREACHABLE=Не вдалося розпочати реплікацію даних, оскільки вихідна система недоступна. Під'єднайте вихідну систему повторно й спробуйте знову.
#MSG: Error message when source system is disconnected/agent is unreachable
BATCH_REPLICATION_NOT_SUPPORTED_SOURCE_UNREACHABLE_FROM_REALTIME=Не вдалося розпочати реплікацію даних, оскільки вихідна система недоступна. Вимкніть реплікацію даних у реальному часі або під'єднайте вихідну систему повторно й спробуйте знову.
#MSG: Error message when source system is disconnected/agent is unreachable
REALTIME_REPLICATION_NOT_SUPPORTED_SOURCE_UNREACHABLE=Не вдалося активувати реплікацію даних у реальному часі, оскільки вихідна система недоступна. Під'єднайте вихідну систему повторно й спробуйте знову.
#MSG: Error message when source system is disconnected/agent is unreachable
REMOVE_REPLICATED_DATA_NOT_SUPPORTED_SOURCE_UNREACHABLE=Не вдалося вилучити репліковані дані, оскільки вихідна система недоступна. Під'єднайте вихідну систему повторно й спробуйте знову.
