
#XBCB: Breadcrumb
remoteTableMonitorBreadcrumb={spaceId}


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Count for Tables in table Toolbar
txtTableCount=Дистанционные таблицы ({0})
#XBUT: Button to start replication
startReplication=Начать тиражирование
#XBUT: Button to stop replication
stopReplication=Остановить тиражирование
#XFLD: Placeholder for Search field
txtSearch=Поиск
#XBUT: Tooltip for refresh button
txtRefresh=Обновить

#XFLD: Label for table column
txtConnection=Соединение
#XFLD: Label for table column
txtConnectionLabel=Соединение (бизнес-имя)
#XFLD: Label for table column
txtConnectionLabelTech=Соединение (техническое имя)
#XFLD: Label for table column
txtTable=Таблица
#XFLD: Label for table column
txtTableLabel=Бизнес-имя
#XFLD: Label for table column
txtTableLabelTech=Техническое имя
#XFLD: Label for table column
txtTableLabelNew=Объект (бизнес-имя)
#XFLD: Label for table column
txtTableLabelTechNew=Объект (техническое имя)
#XFLD: (Alternative) Label for table column "table"
txtTechnicalName=Техническое имя
#XFLD: Label for table column
txtAccessType=Доступ к данным
#XBUT: label for action button
loadNewSnapshot=Загрузить новый мгновенный снимок
#XBUT: Tooltip for refresh button
changeRefreshFrequency=Изменить частоту обновления
#XFLD: Label for table column
replicationType=Тип тиражирования
#XFLD: Label for table column
txtRefreshFrequency=Частота обновления
#XFLD: Label for table column
txtRefreshFrequencyLabel=Частота
#XFLD: Label for table column
txtRefreshFrequencyLabelNew=Плановая периодичность
#XFLD: Label for table column
txtReplicationStatus=Статус
#XFLD: Label for table column
txtNumberOfRecords=Число записей
#XFLD: Label for table column
txtLatestChange=Последнее изменение (источник)
#XFLD: Label for table column
txtLatestChangeNew=Последнее изменение (источник)
#XFLD: Label for table column
txtLatestUpdate=Последнее обновление
#XFLD: Label for table column
txtLatestUpdateNew=Последнее обновление
#XFLD: Label for table column
txtInMemorySizeReplicaTableMB=Использование памяти для данных (МиБ)
#XFLD: Label for table column
txtInMemorySizeReplicaTableMBNeww=Размер in-memory (МиБ)
#XFLD: Label for table column
txtInMemorySizeReplicaTableMBHeader=Размер in-memory
#XFLD: Label for log details column
txtRemoteTableLogs=Журналы дистанционных таблиц
#XFLD: Label for log details column
txtRemoteTableLogsNew=Сведения
#XFLD: Label for table column
txtDiskSizeReplicaTableInMB=Использование диска для данных (МиБ)
#XFLD: Label for table column
txtDiskSizeReplicaTableInMBNeww=Размер на диске (МиБ)
#XFLD: Label for table column
txtDiskSizeReplicaTableInMBHeader=Размер на диске
#XFLD: Label for table column
txtInMemorySizeReplicaTableMBLabel=Использование памяти для данных
#XFLD: Label for table column
txtDiskSizeReplicaTableInMBLabel=Использование диска для данных
#XFLD: Label for schedule owner column
txtScheduleOwner=Владелец планирования
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Показывает, кто является владельцем планирования
#XMSG: Error message for unknown replication type that gets logged in Kibana
unknownReplicationTypeError=Тип тиражирования "{0}" не поддерживается.
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Неприменимо

#XTIT: Title for Refresh Frequency Dialog which shows the table name
titleRefreshFrequencyDialog={0}
#XFLD: text inside Refresh Frequency Dialog
refreshFrequency=Частота обновления
#XBUT: label for radio button in Refresh Frequency Dialog
none=Нет
#XBUT: label for radio button Refresh Frequency Dialog
realtime=В реальном времени
#XMSG: info message in Refresh Frequency Dialog that real-time is not supported
infoDisableRealtimeNotSupported=Деактивация тиражирования в реальном времени для этой дистанционной таблицы не поддерживается.<br><br>Получите дополнительную справку <a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/82380e6f01c84fac898a285ac40a1e50.html?locale=en-US" >здесь</a>.
txt_lnk_fabricVirtualTable=Виртуальная таблица фабрики
#XMSG: info message that real-time check failed
infoRealtimeReplicationCheckFailed=Проверка тиражирования в реальном времени не выполнена.
#XMSG: info message that real-time is supported
infoRealtimeReplicationSupported=Поддерживается тиражирование в реальном времени.
#XMSG: busy indicator for real-time replication check
infoRealtimeReplicationCheckIndicator=Выполняется проверка тиражирования в реальном времени...
#XMSG: info message in Refresh Frequency Dialog that real-time support requires re-deployment
infoRealtimeSupportReqReDeployment=Для тиражирования этой таблицы в реальном времени требуется повторная развертка.
#XMSG: info message in Refresh Frequency Dialog that real-time support requires adding of primary key
infoRealtimeSupportReqPrimKey=Для тиражирования этой таблицы в реальном времени требуется добавить первичный ключ.
#XMSG: info message in Refresh Frequency Dialog that real-time support requires re-deployment and adding of primary key
infoRealtimeSupportReqReDeplAndPrimKey=Для тиражирования этой таблицы в реальном времени требуется повторная развертка таблицы и добавление первичного ключа.
#XBUT: Button to Close Schedule Settings in Refresh Frequency Dialog if nothing was changed
close=Закрыть
#XBUT: Button to Cancel Schedule Settings in Refresh Frequency Dialog
cancel=Отменить
#XMSG For Scheduled text
scheduled=Запланировано
#XMSG For Scheduled text
paused=Приостановлено
#XFLD: text for values shown for Ascending sort order
SortInAsc=Сортировать по восходящей
#XFLD: text for values shown for Descending sort order
SortInDesc=Сортировать по нисходящей

#XFLD: text for values shown in column Replication Status
txtOff=Выкл.
#XFLD: text for values shown in column Replication Status
txtInitializing=Инициализация
#XFLD: text for values shown in column Replication Status
txtRunning=Выполняется
#XFLD: text for values shown in column Replication Status
txtActive=Активно
#XFLD: text for values shown in column Replication Status
txtAvailable=Доступно
#XFLD: text for values shown in column Replication Status
txtError=Ошибка
#XFLD: text for values shown in column Replication Status
txtPaused=Приостановлено
#XFLD: text for values shown in column Replication Status
txtDisconnected=Разъединено
#XMSG: error message for reading data from backend
txtReadBackendError=Произошла ошибка при считывании из бэкэнда.

#XMSG: error message while stopping the replication
stopReplicationError=Произошла ошибка при остановке тиражирования таблицы "{0}".
#XMSG: error message while stopping the replication
disableRealTimeReplicationError=Произошла ошибка при деактивации тиражирования данных в реальном времени для таблицы "{0}".

#XMSG: success message for starting replication the first time
startReplicationSuccess=Тиражируем таблицу "{0}".
#XMSG: success message for loading new snapshot
loadNewSnapshotSuccess=Таблица "{0}" обновляется последними изменениями.
#XMSG: success message for stopping replication
stopReplicationSuccess=Тиражированные данные таблицы "{0}" удаляются.
#XMSG: success message for stopping replication
disableRealTimeReplicationSuccess=Деактивируем тиражирование данных в реальном времени для таблицы "{0}".
#XBUT: label for combobox
txtReplicated=Тиражировано
#XBUT: label for combobox
txtRemote=Дистанционно

#XBUT: label for remote data access
REMOTE=Дистанционно
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Тиражировано (в реальном времени)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Тиражировано (мгновенный снимок)

#XBUT: label for radio button
txtNone=Нет
#XBUT: label for radio button
txtRealtime=В реальном времени
#XMSG: error message during start/stop replication of a table (backend error)
ERROR_EXECUTING_REPLICATION=Ошибка при тиражировании таблицы "{0}".
#XMSG: Error when illegal navigation is invoked
illegalNavigation=На данный момент вы не являетесь участником "{0}". Выберите одно из приведенных ниже пространств.
#XMSG: Error when user is not authorized to schedule tasks
authorizeWarn=Вам нужно авторизовать сервис для планирования задач. <a href="">Авторизовать</a>
#XFLD: Message to schedule task
scheduleText=Запланировать тиражирование
#XFLD: Message to schedule task
scheduleTextLabel=Планирование
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Создать мгновенный снимок планирования
#XBUT: Drop down menu button to create a schedule
createScheduleNewLabel=Создать планирование
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Редактировать планирование
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Удалить планирование
#XBUT: Label for scheduled replication
scheduledTxt=Запланировано
#XFLD: Label for table column
txtNextSchedule=Следующее выполнение
#XFLD: Label for table column
txtNextScheduleNew=Запланированный следующий прогон
#XFLD: Label for frequency column
everyLabel=Кажд.
#XFLD: Plural Recurrence text for Hour
hoursLabel=ч
#XFLD: Plural Recurrence text for Day
daysLabel=дн.
#XFLD: Plural Recurrence text for Month
monthsLabel=мес.
#XFLD: Plural Recurrence text for Minutes
minutesLabel=мин
#XFLD: Message for replicate table action
replicateTableText=Тиражирование таблицы
#XFLD: Message for replicate table action
replicateTableLabelText=Тиражирование данных
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Загрузить новый мгновенный снимок
#XBUT: Drop down menu button to start data replication
startDataReplicationLabel=Запустить тиражирование данных
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Удалить тиражированные данные
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Активировать доступ в реальном времени
#XBUT: Drop down menu button to enable real time replication
enableRealTimeDataReplicationLabel=Активировать тиражирование данных в реальном времени
#XBUT: Drop down menu button to disable real time replication
disableRealTimeReplicationLabel=Деактивировать тиражирование данных в реальном времени
#XBUT: Drop down menu button to navigate to space connection list page
viewDocumentation=Просмотреть документацию
#XFLD: Documentation Link for real time replication
goToConnectionsList=Перейти к списку соединений
#XMSG: Warning message for removing the persisted data
RemoveReplication_Confirm_Msg=Изменить доступ к данным и удалить тиражированные данные таблицы "{0}"?
#XMSG: error message for replication not started
txtReplicationStartError=Не удалось запустить тиражирование, поскольку не удалены актуальные тиражированные данные.
#XMSG: busy indicator for loading new snapshot
infoLoadingNewSnapshotIndicator=Загружаем новый мгновенный снимок...
#XMSG: busy indicator for stopping real-time replication
infoStopRealtimeReplicationIndicator=Перед загрузкой нового мгновенного снимка останавливается тиражирование в реальном времени.
#XMSG: busy indicator for starting batch replication
infoStartBatchReplicationIndicator=Запускаем фоновое тиражирование...
#XMSG: message for real-time replication not supported
REALTIME_REPLICATION_NOT_SUPPORTED=Тиражирование в реальном времени не поддерживается для "{0}"
#XMSG: message for replication not supported (snapshot and real time replication)
REPLICATION_NOT_SUPPORTED=Невозможно тиражировать данные для "{0}": {1}.
#XMSG: message for replication not supported due to unsupported data type (snapshot and real time replication)
REPLICATION_NOT_SUPPORTED_UNSUPPORTED_DATA_TYPE=Невозможно тиражировать данные для "{0}", поскольку они содержат не поддерживаемый тип данных.
#XMSG: message for replication not supported due to an issue with RMS
REPLICATION_NOT_SUPPORTED_RMS_ISSUE=Невозможно тиражировать данные для "{0}", так как невозможно установить соединение с сервисом управления тиражированием: {1}.
#XMSG: message for conflicting task
Task_Already_Running=Для таблицы ''{0}'' уже выполняется конфликтующая задача.
#XMSG: message for Remote Source not found
REMOTE_SOURCE_NOT_FOUND=Дистанционный источник не найден.
#XMSG: message for Replication type missing
REPLICATION_TYPE_MISSING=Тип тиражирования отсутствует.
#XMSG: message for run missing for cancellation
CANCEL_MISSING_RUN=Отсутствует прогон отмены для дистанционной таблицы "{0}".
#XMSG: message for failure to cancel the replication because it was already completed
CANCEL_COMPLETED_REPLICATION_RUN=Не удалось отменить процесс тиражирования таблицы "{0}", так как он уже завершен.
#XMSG: message for failure to cancel the replication because it was not started yet.
CANCEL_UNREACHABLE_REPLICATION_RUN=Не удалось отменить процесс тиражирования таблицы, так как тиражирование данных для таблицы "{0}" еще не начато.
#XMSG: message for unexpected replication status
UNEXPECTED_REPLICATION_STATUS=Неожиданный статус тиражирования для дистанционной таблицы "{0}".
#XMSG: message for failure to remove replicated data because the replication had been canceled.
REMOVE_CANCELED_REPLICATION_RUN=Не получилось удалить тиражированные данные. Процесс тиражирования таблицы "{0}" уже отменен

#XFLD: Message for remote table replication logs
CANCELING_REPLICATION_TASK=Отмена выполняемого тиражирования для дистанционной таблицы "{1}"...
#XFLD: Message for remote table replication logs
CANCELING_REPLICATION_TASK_SUCCESS=Выполняемое тиражирование отменено для дистанционной таблицы "{1}".
#XFLD: Message for remote table replication logs
CANCELING_REPLICATION_TASK_FAILED=Не удалось отменить выполняемое тиражирование для дистанционной таблицы "{1}".
#XFLD: Message for remote table replication logs
DISABLING_REALTIME_TASK_SUCCESS=Тиражирование данных в реальном времени деактивировано для дистанционной таблицы "{1}".
#XFLD: Message for remote table replication logs
DISABLING_REALTIME_TASK_FAILED=Не удалось деактивировать тиражирование данных в реальном времени для дистанционной таблицы "{1}".
#XFLD: Message for remote table replication logs
CANCEL_REPLICATION_TASK=Операция тиражирования отменена по запросу для таблицы "{1}"
#XMSG: Message for remote table replication logs
SWITCH_FROM_BATCH_TO_REALTIME=Переключение типа тиражирования для дистанционной таблицы "{1}" с фонового на тиражирование в реальном времени.
#XMSG: Message for remote table replication logs
SWITCH_FROM_REALTIME_TO_BATCH=Переключение типа тиражирования для дистанционной таблицы "{1}" с тиражирования в реальном времени на фоновое.
#XFLD: Message for remote table replication logs
START_REALTIME_REPLICATION=Тиражирование (в реальном времени) данных для дистанционной таблицы "{1}".
#XMSG: Message for remote table replication logs
DROP_SUBSCRIPTION_WARNING=Необходимо вручную удалить объекты, сгенерированные для записи изменений данных в дистанционном источнике.
#XFLD: Message for remote table replication logs
START_BATCH_REPLICATION=Тиражирование (фоновое) данных для дистанционной таблицы "{1}".
#XFLD: Message for remote table replication logs
STOP_REPLICATION=Удаление тиражированных данных для дистанционной таблицы "{1}"
#XFLD: Message for remote table replication logs
DISABLE_REALTIME=Деактивация тиражирования данных в реальном времени для дистанционной таблицы "{1}"
#XFLD: Message for remote table replication logs
START_BATCH_REPLICATION_BLOCKED=Тиражирование (фоновое) данных для дистанционной таблицы "{1}" сейчас блокировано.
#XFLD: Message for remote table replication logs
START_BATCH_REPLICATION_SUCCESS=Данные тиражируются (в фоне) для дистанционной таблицы "{1}".
#XFLD: Message for remote table replication logs
STOP_REPLICATION_BLOCKED=Удаление тиражированных данных для дистанционной таблицы "{1}" сейчас блокировано.
#XFLD: Message for remote table replication logs
START_REALTIME_REPLICATION_SUCCESS=Данные тиражируются (в реальном времени) для дистанционной таблицы "{1}".
#XFLD: Message for remote table replication logs
STOP_REPLICATION_SUCCESS=Тиражированные данные удалены, и дистанционный доступ к данным восстановлен для дистанционной таблицы "{1}".
#XFLD: Message for remote table replication logs
START_BATCH_REPLICATION_FAILED=Ошибка тиражирования (фонового) данных для дистанционной таблицы "{1}".
#XFLD: Message for remote table replication logs
START_REALTIME_REPLICATION_FAILED=Ошибка тиражирования (в реальном времени) данных для дистанционной таблицы "{1}".
#XFLD: Message for remote table replication logs
STOP_REPLICATION_FAILED=Ошибка удаления тиражированных данных для дистанционной таблицы "{1}".
#XFLD: Message for remote table replication logs
TABLE_REPLICATION_FAILED=Ошибка тиражирования данных для дистанционной таблицы "{1}".
#XFLD: Message for remote table replication logs
START_REPLICATION_RECORD_COUNT_SUCCESS="{3}" записи(ей) тиражировано для таблицы "{1}".
#XFLD: Message for remote table replication logs including number of partitions
START_REPLICATION_RECORD_COUNT_PARTITIONS_SUCCESS={3} записи(ей) тиражировано для таблицы "{1}" в {4} разделах.
#XFLD: Message for successful replication of a single partition
START_REPLICATION_SINGLE_PARTITION_SUCCESS={0} записи(ей) тиражировано в раздел для значений "{1}" <= {2} < "{3}".
#XFLD: Message for successful replication of a "others" partition
START_REPLICATION_OTHERS_PARTITION_SUCCESS={0} записи(ей) тиражировано в раздел "Прочее".
#XFLD: Message for pre-query returning no value for partitioning
PRE_QUERY_PARTITIONS_NO_VALUE=Предварительный запрос, используемый для сопоставления разделов с источником данных, не вернул значение.
#XFLD: Message for pre-query of partitioning
PRE_QUERY_PARTITIONS=Предварительный запрос, используемый для сопоставления разделов с источником данных, вернул {0} знач. для распределения по {1} разделам.
#XFLD: Message for remote table replication logs
START_REPLICATION_RECORD_COUNT_FAILED=Не удалось подсчитать записи в таблице "{1}".
#XFLD: Message for remote table replication logs
STOP_REPLICATION_RECORD_COUNT_SUCCESS="{3}" тиражированных записи(ей) удалено для дистанционной таблицы "{1}".
#XFLD: Message for remote table replication logs
DISABLE_REALTIME_RECORD_COUNT_SUCCESS="{3}" тиражированных записи(ей) оставлено для дистанционной таблицы "{1}".
#XFLD: Message for remote table replication logs
STOP_REPLICATION_RECORD_COUNT_FAILED=Не удалось подсчитать записи в таблице "{1}".
#XFLD: Cancel Run label
TEXT_CancelRun=Отменить прогон
#XMSG: Cancel Run warning Message
Msg_CancelRun=Действительно отменить текущий прогон?
#XMSG: Re-start real-time replication action
txtPausedRealTimeReplStatusPopover=Чтобы предоставить доступ в реальном времени,\nперезапустите тиражирование для\nсоединения {0}
#XFLD: Message for remote table replication logs
SUBSCRIPTION_APPLIED=Успешный начальный перенос данных для тиражирования дистанционной таблицы "{1}" в реальном времени. Теперь тиражирование в реальном времени будет автоматически добавлять данные дельты в дистанционную таблицу "{1}".
#XFLD: Message for remote table replication logs
REPLICATION_FILTER_CONDITION=Таблица "{1}" тиражирована со следующим условием фильтрации: "{5}".
#XFLD: Message for remote table replication logs
START_REPLICATION_SYNC=Подготовка к тиражированию данных в синхронном режиме
#XFLD: Message for remote table replication logs
START_REPL_REFRESH_SYNC=Подготовка к обновлению данных в синхронном режиме.
#XFLD: Message for remote table replication logs
START_REPLICATION_ASYNC=Подготовка к тиражированию данных в асинхронном режиме.
#XFLD: Message for remote table replication logs
START_REPL_REFRESH_ASYNC=Подготовка к обновлению данных в асинхронном режиме.
#XFLD: Message for remote table replication logs
DELEGATE_TO_HANA_ASYNC=Делегирование ид. {0} выполнения задачи SAP HANA.
#XFLD: Message for remote table replication logs
SUBSCRIBED_TO_SRC_DATA_CHG=Выполнена подписка на изменения данных исходной системы.
#XFLD: Message for remote table replication logs
START_INITIAL_LOAD=Вставка данных в таблицу тиражирования.
#XFLD: Message for remote table replication logs
START_INITIAL_LOAD_CDI=Вставка данных в таблицу тиражирования.
#XFLD: Message for remote table replication logs
APPLYING_DATA_CHANGES=Применение изменений данных к таблице тиражирования.
#XFLD: Message for remote table replication logs
SWITCHED_DATA_ACCESS_BATCH=Переключение доступа к данным (в фоновом режиме) на новую таблицу тиражирования.
#XFLD: Message for remote table replication logs
SWITCHED_DATA_ACCESS_REALTIME=Переключение доступа к данным (в реальном времени) на новую таблицу тиражирования.
#XMSG: Real-time replication has been enabled for remote table
REALTIME_REPLICATION_ENABLED=Следующий прогон этой цепочки задач загрузит новый мгновенный снимок и изменит текущий режим тиражирования в реальном времени на фоновый режим тиражирования. Данные больше не будут обновляться в реальном времени.
#XMSG: Remote table is only available in federation mode.
FEDERATION_ONLY="{0}": доступно только в дистанционном режиме
#XMSG: Remote table replication blocked as space is locked.
START_REMOTE_TABLE_REPLICATION_LOCKED_SPACE_ERROR=Невозможно тиражировать дистанционную таблицу "{1}", так как пространство "{2}" блокировано.
##XFLD: Message for missing authorizations for remote table replication
REMOTE_TABLE_REPLICATION_MISSING_AUTH=Нет полномочий на тиражирование дистанционной таблицы.
#XFLD: Message for out of memory error
RESOURCE_LIMIT_ERROR_OUT_OF_MEMORY=Задача не выполнена из-за нехватки памяти в базе данных SAP HANA.
#XFLD: Message for Admission Control Rejection
RESOURCE_LIMIT_ERROR_ADMISSION_CONTROL=Задача не выполнена из-за отклонения в контроле допуска SAP HANA.
#XFLD: Message for SAP HANA back pressure problem
RESOURCE_LIMIT_ERROR_HANA_BACK_PRESSURE=Задача не выполнена, так как слишком много активных соединений SAP HANA.

#XFLD: Error while accessing pertitioning for a remote table
missingPrivilegeInRemoteTables=Нет полномочий на выполнение разделения для дистанционной таблицы "{0}"

#MSG: Error header for realtime replication task
errorHeaderInitRealtime=Ошибка при начальной загрузке для тиражирования данных в реальном времени.
#MSG: Error header for realtime replication subscription
errorHeaderLoadRealtime=Ошибка при тиражировании в реальном времени.
#MSG: Error header for snapshot replication task
errorHeaderLoadSnapshot=Ошибка при тиражировании мгновенного снимка.
#MSG: Error details for replication task "starting" phase (remote-table name, date, time)
errorDetailsTaskStarting={1} в {2} произошла внутренняя ошибка при запуске тиражирования данных для дистанционной таблицы "{0}".
#MSG: Error details for replication task "preparing" phase (remote-table name, date, time, SQL error code)
errorDetailsTaskPreparingStage={1} в {2} произошла ошибка SQL ({3}) при подготовке тиражирования данных для дистанционной таблицы "{0}".
#MSG: Error details for replication task (remote-table name, date, time, SQL error code)
errorDetailsTaskGeneral={1} в {2} произошла ошибка SQL ({3}) при тиражировании данных для дистанционной таблицы "{0}".
#MSG: Error details for replication task (remote-table name, date, time)
errorDetailsTaskNoCode={1} в {2} произошла внутренняя ошибка при тиражировании данных для дистанционной таблицы "{0}".
#MSG: Error details for replication task (remote-table name)
errorDetailsTaskFallback=Внутренняя ошибка при тиражировании данных для дистанционной таблицы "{0}".
#MSG: Error details for replication task "finalizing" phase (remote-table name, date, time, SQL error code)
errorDetailsTaskFinalizingStage={1} в {2} произошла ошибка SQL ({3}) при завершении тиражирования данных для дистанционной таблицы "{0}".
#MSG: Error details for unspecific real-time error (remote-table name, date, time)
errorDetailsRealtime={1} в {2} произошла ошибка, препятствующая тиражированию данных дистанционной таблицы "{0}" в реальном времени.
#MSG: Error details for unspecific real-time error (remote-table name)
errorDetailsRealtimeFallback=Произошла ошибка, препятствующая тиражированию данных дистанционной таблицы "{0}" в реальном времени.
#MSG: Error details for FVT-RMS real-time error (remote-table name) with Auto Retry option
errorDetailsRealtimeFallbackAutoRetry=Ошибка препятствует тиражированию в реальном времени дистанционной таблицы "{0}". Перейдите по ссылке ниже, чтобы просмотреть подробное сообщение об ошибке.\nВ течение недели система будет пытаться повторно активировать тиражирование в реальном времени. Если вы устранили ошибку, нажмите "Повторить" для повторной активации вручную.
#MSG: Error details for connection-level real-time exception (remote-table name, date, time, exception id, connection name)
errorDetailsRealtimeConnection=Ошибка с ид. {3} произошла в соединении "{4}" {1} в {2} и препятствует тиражированию данных дистанционной таблицы "{0}" в реальном времени.
#MSG: Error details for table-level real-time exception (remote-table name, date, time, exception id, other remote-table name)
errorDetailsRealtimeTable=Ошибка с ид. {3} произошла при тиражировании дистанционной таблицы "{4}" в реальном времени {1} в {2} и препятствует тиражированию данных дистанционной таблицы "{0}" в реальном времени.
#MSG: Advice on error to view details
errorAdvice=Чтобы подробнее посмотреть сообщение об ошибке, пройдите по ссылке ниже.
#MSG: Advice on error to view details and check logs (remote-table name)
errorAdviceLogs=Чтобы подробнее посмотреть сообщение об ошибке и проверить журналы задачи тиражирования дистанционной таблицы "{0}", перейдите по ссылке ниже.
#MSG: Advice on error to view details and validate connection (connection name)
errorAdviceConnection=Чтобы подробнее посмотреть сообщение об ошибке и проверить соединение "{0}", перейдите по ссылке ниже.
#MSG: Advice on error to view details, validate connection, and check DP agent (connection name, agent name)
errorAdviceConnectionAgent=Чтобы подробнее посмотреть сообщение об ошибке, проверить соединение "{0}" и журналы агента DP "{1}", перейдите по ссылке ниже.
#MSG: Advice on error to view details and check monitor details (remote-table name)
errorAdviceTable=Чтобы подробнее посмотреть сообщение об ошибке и проверить сведения о мониторинге дистанционной таблицы "{0}", перейдите по ссылке ниже.
#MSG: Advice on error to view details, check monitor details, and check DP agent (remote-table name, agent name)
errorAdviceTableAgent=Чтобы подробнее посмотреть сообщение об ошибке, проверить сведения о мониторинге дистанционной таблицы "{0}" и журналы агента DP "{1}", перейдите по ссылке ниже.
#MSG: Advice to retry after the error is corrected
errorAdviceRetry=Выполните действие "Повторить" для продолжения тиражирования в реальном времени только после исправления ошибки.
#XFLD Label Go to connections link
goToConnections=Перейти к соединениям
#XFLD Label Go to Details link
goToDetails=Перейти к сведениям
#XFLD Label Go to configuration link
goToDpAgents=Перейти к конфигурации
#XFLD Label go to other object details link
goToOtherDetails=Перейти к сведениям о {0}
#XFLD Label Retry link
retry=Повторить
#XFLD Show more
showMoreText=Показать больше...

#XFLD: text retry
Retry=Подтвердить
#XMG: Retry confirmation text
retryConfirmationTxt=Последнее тиражирование в реальном времени завершилось ошибкой.\n Подтвердите, что ошибка исправлена и тиражирование в реальном времени может быть перезапущено.
#XMG: Retry success text
retrySuccess=Процесс успешно перезапущен.
#XMG: Retry fail text
retryFail=Не удалось перезапустить процесс.
#XMG: Retry fail text
RETRY_FAILED_ERROR=Ошибка перезапуска процесса.

#XMSG: Message for create statistics in remote table
START_CREATE_STATISTICS=Создание статистики типа {1} для {0}.
#XMSG: Message for start statistics
CREATE_STATISTICS_SUCCESS=Статистика типа {1} для {0} успешно создана.
#XMSG: Message for error in creating statistics
CREATE_STATISTICS_FAIL=Не удалось создать статистику типа {1} для {0}.

#XMSG: Message for starting refresh statistics
START_REFRESH_STATISTICS=Обновление статистики для {0}.
#XMSG: Success message for refresh statistics
REFRESH_STATISTICS_SUCCESS=Статистика типа {1} для {0} успешно обновлена.
#XMSG: Error message for refresh statistics
REFRESH_STATISTICS_FAIL=Не удалось обновить статистику типа {1} для {0}.

#XMGS: Message for Start changing the statistics type
START_ALTER_STATISTICS=Изменение типа статистики на {1} для {0}.
#XMSG: Success message for altering statistics type
ALTER_STATISTICS_SUCCESS=Статистика типа {1} для {0} успешно изменена.
#XMSG: Error message for altering statistics type
ALTER_STATISTICS_FAIL=Не удалось изменить статистику типа {1} для {0}.

#XMSG: Message for starting to drop statistics from remote table
START_DROP_STATISTICS=Сброс статистики для {0}.
#XMSG: Success message for drop statistics
DROP_STATISTICS_SUCCESS=Статистика для {0} успешно сброшена.
#XMSG: Error message for drop statistics
DROP_STATISTICS_FAIL=Не удалось сбросить статистику для {0}.

#XMSG: Log to let know that partitioning has been removed automaticaly
PARTITIONING_AUTO_DELETE=Информация о разделении автоматически удалена, так как разделенного столбца больше нет в таблице "{1}".
#XMSG: Log to let know that real-time replication does not support partitioning
REPLICATION_REAL_TIME_PARTITION=Разделы нельзя использовать для тиражирования в реальном времени.
#XMSG: Log to let know that CDI real-time replication should use at least dpagent version 2.6.1
REPLICATION_REAL_TIME_CDI=Версия агента DP ({4}) ниже 2.6.1 не рекомендуется для тиражирования в реальном времени через адаптер CDI.
#XMSG: Log to let know that CDI real-time replication should use at least dpagent version 2.6.1
REPLICATION_REAL_TIME_CDI_NO_VERSION=Версия агента DP не считана. Версия ниже 2.6.1 не рекомендуется для тиражирования в реальном времени через адаптер CDI.

#XMSG: log warning about multiple remote tables pointing to the same source and being replicated (fvt)
REPLICATION_REPLICA_SHARING=Дистанционная таблица {1} указывает на ту же исходную таблицу, что и другие дистанционные таблицы, и для этой исходной таблицы есть другие тиражирования. Рекомендуется оставить только одно тиражирование.

#XMSG: log warning about retring running (fvt)
REPLICATION_REAL_TIME_AUTO_RETRY=Тиражирование в реальном времени блокировано, пытаемся повторно активировать.
REPLICATION_REAL_TIME_FIRST_AUTO_RETRY=Повторяем попытку тиражирования в реальном времени.
REPLICATION_REAL_TIME_SUCCESSFUL_AUTO_RETRY=При повторной попытке удалось активировать тиражирование в реальном времени.
REPLICATION_REAL_TIME_LAST_AUTO_RETRY=При повторной попытке не удалось активировать тиражирование в реальном времени.

partitionButton=Раздел
OBJECT_NAME=Выбранная таблица
Column=Столбец
Parallel_Process=Параллельные процессы
Partitions=Разделы
Add_Partitions=Добавить разделы
btnSave=Сохранить
btnClear=Очистить
btnRestore=Восстановить
btnCancel=Отменить
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtConnectionTooltip=Указывает имя соединения, к которому относится дистанционная таблица
#XFLD: tooltip for table column
txtTableTooltip=Указывает имя дистанционной таблицы
#XFLD: tooltip for table column
txtDataAccessTooltip=Показывает, как осуществляется доступ к данным
#XFLD: tooltip for table column
txtRefreshFreqTooltip=Указывает, как часто тиражируются данные. Это значение определяет целевую частоту, которая, возможно, еще не достигнута в зависимости от статуса дистанционной таблицы
#XFLD: tooltip for table column
txtStatusTooltip=Указывает статус дистанционной таблицы
#XFLD: tooltip for table column
txtLatestChangeTooltip=Указывает последнее изменение данных в источнике.
#XFLD: tooltip for table column
txtNumberOfRecordsTooltip=Указывает число записей в таблице.
#XFLD: tooltip for table column
txtLatestUpdateTooltip=Указывает последнее обновление данных.
#XFLD: tooltip for table column
txtNextRunTooltip=Если для дистанционной таблицы настроено планирование, посмотрите, когда запланирован следующий прогон
#XFLD: tooltip for table column
txtInMemorySizeTooltip=Отслеживание использования памяти дистанционной таблицей
#XFLD: tooltip for table column
txtDiskSizeTooltip=Отслеживание использования места на диске дистанционной таблицей
#XMSG: Expired text
txtExpired=Истекло
#XMSG: Open BW4 Cockpit button text
openBw4CockpitTxt=Открыть пульт управления мостом SAP BW

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Объект "{0}" невозможно добавить в цепочку задач.
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE_REMOTETABLE=Объект "{0}": тип тиражирования будет изменен.

#XMSG: Info message when there is shared replica table
sharedReplicaInfoText=Дистанционная таблица указывает на ту же исходную таблицу, что и другие дистанционные таблицы.
sharedReplicaWarningText=Эта дистанционная таблица указывает на ту же исходную таблицу, что и другие дистанционные таблицы с уже активированным или запланированным тиражированием. Невозможно активировать тиражирование или планирование.
sharedReplicaErrorText=Тиражирование не включено. Дистанционная таблица указывает на ту же исходную таблицу, что и другие дистанционные таблицы, для которых включено или запланировано тиражирование. Используйте тиражированные данные этих таблиц либо удалите их планирование и тиражированные данные, чтобы разрешить планирование и тиражирование дистанционной таблицы. Если таблица с тиражированными данными находится в другом пространстве, потребуется организовать для нее совместное использование в этом пространстве.

#XBTN: Button to close the dialog
cancelText=Отменить
okText=Все равно продолжить

#XMSG: warning message to show the change from realtime replication to batch
warn_realtime=Сейчас данные тиражируются в режиме реального времени. При загрузке нового мгновенного снимка тип тиражирования будет изменен на фоновое, и данные больше не будут обновляться в реальном времени.
warn_changeto_batch=Дистанционная таблица используется в цепочках задач. Следующий прогон цепочки задач изменит тип доступа к данным дистанционной таблицы, и данные больше не будут загружаться в реальном времени.
txt_lnk_impactAnalysis=Анализ зависимостей и происхождения

#XMSG: Replication will change
txt_replication_change=Тип тиражирования будет изменен.
txt_repl_viewdetails=Просмотреть сведения
msg_repl_viewdet_schedule_content=Определенное планирование выполнит новый мгновенный снимок {0}. Это изменит режим тиражирования с тиражирования в реальном времени на фоновое, и данные больше не будут обновляться в реальном времени.
msg_repl_viewdet_taskchain_content=Сейчас данные тиражируются в реальном времени. Дистанционная таблица используется в цепочках задач. Следующий прогон цепочки задач изменит тип тиражирования на фоновое, и данные больше не будут обновляться в реальном времени.

#XFLD: dialog section
Background=Фон:
#XFLD: dialog section
Cause=Причина:
#XFLD: dialog section
Solution=Решение:
#XFLD: dialog section
Warning=Предупреждение

dropSubscriptionCancelRealTime=Вы собираетесь отменить активацию тиражирования в реальном времени для дистанционной таблицы "{1}".
#MSG: Before drop HANA subscription, prompt the user with warning
dropSubscriptionSwitch=Вы собираетесь переключить тип тиражирования с реального времени на фоновое для дистанционной таблицы "{1}".
#MSG: Before drop HANA subscription, repeat the warning in the task log
dropSubscriptionSwitched=Дистанционная таблица "{1}" будет переключена с тиражирования в реальном времени на фоновое тиражирование.
#MSG: Before drop HANA subscription, prompt the user with warning
dropSubscriptionRemove=Вы собираетесь удалить тиражированные данные из дистанционной таблицы "{1}", для которой активирован доступ к данным в реальном времени.
#MSG: Before drop HANA subscription, prompt the user with warning
dropSubscriptionDisableRealTime=Вы собираетесь деактивировать тиражирование данных в реальном времени для дистанционной таблицы "{1}".
#MSG: Before drop HANA subscription, repeat the warning in the task log
dropSubscriptionRemoved=Тиражированные данные будут удалены из дистанционной таблицы "{1}", для которой активирован доступ к данным в реальном времени.
#MSG: Before drop HANA subscription, explain about database triggers
dropSubscriptionTrigger=Активная дистанционная подписка для дистанционной таблицы "{1}" в вашем экземпляре SAP HANA Cloud сгенерировала триггеры и теневую таблицу для записи изменений данных в дистанционной базе данных.
#MSG: Before drop HANA subscription, explain about ABAP subscription
dropSubscriptioninAbap=Активная дистанционная подписка для дистанционной таблицы "{1}" в вашем экземпляре SAP HANA Cloud создала подписку для записи изменений данных в дистанционной базе данных.
#MSG: Before drop HANA subscription, tell that connection has exceptions
dropSubscriptionHasExceptions=Сейчас это тиражирование в реальном времени имеет особые ситуации на уровне соединения.
#MSG: Before drop HANA subscription, tell that connection is paused
dropSubscriptionIsPaused=Сейчас это тиражирование в реальном времени приостановлено на уровне соединения.
#MSG: Before drop HANA subscription, tell that DP Agent is disconnected
dropSubscriptionIsDisconnected=Агент провизионирования данных сейчас отсоединен.
#MSG: Before drop HANA subscription, tell that the connection is invalid
connectionIsInvalid=Исходная система сейчас недоступна.
#MSG: Before drop HANA subscription, tell that only target side is deleted
dropSubscriptionImpact=При продолжении будет удалена только дистанционная подписка на стороне цели.
#TODO: Same with "would"?
#MSG: Before drop HANA subscription, tell that database triggers must be deleted manually
dropSubscriptionImpactTrigger=Вам потребуется вручную удалить триггеры на стороне источника и теневую таблицу для дистанционного объекта {6} с уникальным префиксом системного объекта "{5}", как описано в SAP-ноте 3307334.
#TODO: Same with "would"?
#MSG: Before drop HANA subscription, tell that ABAP subscription must be deleted manually
dropSubscriptionImpactOnAbap=Вам потребуется вручную удалить подписку на базе ABAP на стороне источника "{6}", как описано в SAP-ноте 3307334.
#TODO: Same with "would"?
#MSG: Before drop HANA subscription, recommend to process all exceptions
dropSubscriptionProcess=Поэтому рекомендуем обработать все особые ситуации для этого соединения перед продолжением.
#TODO: Same with "try it again"?
#MSG: Before drop HANA subscription, recommend to resume real-time replication
dropSubscriptionResume=Поэтому рекомендуем перезапустить тиражирование в реальном времени на уровне соединения перед продолжением.
#TODO: Same with "try it again"?
#MSG: Before drop HANA subscription, recommend to reconnect the DP agent
dropSubscriptionReconnect=Поэтому рекомендуем переподключить агент провизионирования данных перед продолжением.
#TODO: Same with "try it again"?
#MSG: Before drop HANA subscription, recommend to directly trigger the same activity again
dropSubscriptionIgnore=Вы можете игнорировать эту ошибку при выполнении данной операции напрямую.
#XFLD: text proceed
continueAnyway=Все равно продолжить
#XMSG: Warning text when performing mass operation of removing replicated data.
massReplicationRemoveWarnText=Изменить доступ к данным на дистанционный и удалить тиражированные данные?
#XMSG: warning text when performing mass operation of removing replication on Partial set of selected tables
massReplicationPartialRemoveWarnText={0} из {1} выбранных дистанционных таблиц имеют тиражированные данные. \nИзменить доступ к данным на дистанционный и удалить тиражированные данные?
#XMSG: error message while mass stopping the replication
stopMassReplicationError=Вероятно, произошла ошибка при остановке тиражирования таблицы.
#XMSG: success message for stopping replication
stopMassReplicationSuccess=Мы удаляем тиражированные данные для выбранных таблиц.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Присвоить планирование мне
#XBUT: Pause schedule menu label
pauseScheduleLabel=Приостановить планирование
#XBUT: Resume schedule menu label
resumeScheduleLabel=Возобновить планирование
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Произошла ошибка при удалении планирования.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Произошла ошибка при присвоении планирования.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Произошла ошибка при приостановке планирования.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Произошла ошибка при возобновлении планирования.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Удаляем планирования ({0})
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Изменяем владельца планирований {0}
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Приостанавливаем планирования {0}
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Возобновляем планирования {0}

#XMSG: Message for starting refresh statistics
VALIDATING_REMOTE_TABLES=Проверяем {0} дистанционных таблиц
#XMSG: <table name which was validated>: not changed
NOT_CHANGED={0}: не изменено
#XMSG: <table name which was validated>: changed
CHANGED={0}: изменено
#XMSG: No changes detected
NO_CHANGES_DETECTED=Изменения не обнаружены
#XMSG Updating repository
UPDATING_REPOSITORY=Обновляем репозитарий
#XMSG Repository updated
REPOSITORY_UPDATED=Репозитарий обновлен
#XMSG Remote Tables validated
REMOTE_TABLES_VALIDATED=Дистанционные таблицы проверены
#XMSG Reading remote tables from repository
READING_REMOTE_TABLES_FROM_REPOSITORY=Считываем дистанционные таблицы из репозитария
#XMSG {} remote tables read from repository
REMOTE_TABLES_READ_FROM_REPOSITORY={0} дистанционных таблиц(ы) считано из репозитария
#XMSG Reading {} from remote source
READING_FROM_REMOTE_SOURCE=Считываем {0} из дистанционного источника
#XMSG {} read from remote source
READ_FROM_REMOTE_SOURCE={0} считано из дистанционного источника
#XMSG Remote table {} not found in remote source
REMOTE_TABLE_NOT_FOUND_IN_REMOTE_SOURCE=Дистанционная таблица {0} не найдена в дистанционном источнике
#XMSG Error during execution
ERROR_DURING_EXECUTION=Ошибка при выполнении: {0}

#XMSG: error message when none of the selected tables have passed validation to remove replication.
massRemoveRepAllSelectionError=Выбранные таблицы не поддерживают массовое удаление тиражирования из-за ошибок или предупреждений. \nВыберите одну таблицу и попробуйте удалить тиражирование.
#XBUT: Select Columns Button
selectColumnsBtn=Выбрать столбцы
#XFLD: Refresh tooltip
TEXT_REFRESH=Обновить
#XFLD: Select Columns tooltip
text_selectColumns=Выбрать столбцы
#MSG: Error message when source system is disconnected/agent is unreachable
startDataReplication=Невозможно запустить тиражирование данных.
#MSG: Error message when source system is disconnected/agent is unreachable
removeReplicatedData=Невозможно удалить тиражированные данные.
#MSG: Error message when source system is disconnected/agent is unreachable
enableRealTimeReplication=Невозможно активировать тиражирование данных в реальном времени.
#MSG: Error message when source system is disconnected/agent is unreachable
disableRealTimeReplication=Невозможно деактивировать тиражирование данных в реальном времени.
#MSG: Before starting replication of data, recommend to disable real-time replication or reconnect the agent
disableRealTimeReconnectAgentDataReplication=Деактивируйте тиражирование данных в реальном времени или переподключите агент провизионирования данных, чтобы запустить тиражирование данных.
#MSG: Before starting replication of data, recommend to disable real-time replication or reconnect the source system
disableRealTimeReconnectSourceDataReplication=Деактивируйте тиражирование данных в реальном времени или переподключите исходную систему, чтобы запустить тиражирование данных.
#MSG: Before starting replication of data, recommend to reconnect the agent
reconnectAgentStartReplication=Переподключите агент провизионирования данных, чтобы запустить тиражирование данных.
#MSG: Before starting replication of data, we recommend reconnecting the source system.
reconnectSourceStartReplication=Переподключите исходную систему, чтобы запустить тиражирование данных.
#MSG: Before removing replicated data, recommend to reconnect the agent
reconnectAgentRemoveReplicatedData=Переподключите агент  провизионирования данных, чтобы удалить тиражированные данные.
#MSG: Before removing replicated data, recommend to reconnect the source
reconnectSourceRemoveReplicatedData=Переподключите исходную систему, чтобы удалить тиражированные данные.
#MSG: Before enabling Real-Time replication, recommend to reconnect the agent
reconnectAgentEnableRealTimeReplication=Переподключите агент провизионирования данных, чтобы активировать тиражирование данных в реальном времени.
#MSG: Before enabling Real-Time replication, recommend to reconnect the source
reconnectSourceEnableRealTimeReplication=Переподключите исходную систему, чтобы активировать тиражирование данных в реальном времени.
#XMSG: error message while mass stopping the replication
massDisableAllSelectionError=Тиражирование данных в реальном времени невозможно деактивировать для выбранных таблиц.<br><br>Справку см. <a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/82380e6f01c84fac898a285ac40a1e50.html?locale=en-US" >здесь</a>.
#XMSG: error message while mass stopping the replication
massDisableConfirmation=Выбранные объекты используют тиражирование данных в реальном времени.<br><br>Объекты имеют сгенерированные триггеры и теневую таблицу для записи дельта-изменений в дистанционной базе данных. Эти объекты должны удаляться вручную. Для подробных инструкций см. журнал для каждой дистанционной таблицы в отдельности. <br><br> Действительно деактивировать?
#XMSG: error message while mass stopping the replication
stopMassDisableError=Произошла ошибка при деактивации тиражирования данных в реальном времени.
#XMSG: success message for stopping replication
stopMassDisableSuccess=Деактивируем тиражирование данных в реальном времени для выбранных таблиц.
#XMSG: error message while mass stopping the replication
massDisablePartialRemoveWarnText=Выбранные объекты используют тиражирование данных в реальном времени.<br><br>Некоторые из объектов имеют сгенерированные триггеры и теневую таблицу для записи дельта-изменений в дистанционной базе данных. Эти объекты должны удаляться вручную. Для подробных инструкций см. журнал для каждой дистанционной таблицы в отдельности. <br><br> Действительно деактивировать?
#XMSG: error message while mass stopping the replication
massDisableFewTablesConfirmation=Тиражирование данных в реальном времени можно деактивировать только для {0} из {1} выбранных объектов.<br><br>Справку см. <a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/82380e6f01c84fac898a285ac40a1e50.html?locale=en-US" >здесь</a>.<br><br>Действительно деактивировать?
#XMSG: error message while mass stopping the replication
massDisableWithWarningFewTablesConfirmation=Тиражирование данных в реальном времени можно деактивировать только для {0} из {1} выбранных объектов.<br><br>Справку см. <a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/82380e6f01c84fac898a285ac40a1e50.html?locale=en-US" >здесь</a>.<br><br>Некоторые из объектов имеют сгенерированные триггеры и теневую таблицу для записи дельта-изменений в дистанционной базе данных. Эти объекты должны удаляться вручную. Для подробных инструкций см. журнал для каждой дистанционной таблицы в отдельности.<br><br>Действительно деактивировать?
#XLBL Disable button text
disable=Деактивировать
#XSM Disable confirmation
massDisableAllWithoutWarning=Действительно деактивировать тиражирование данных в реальном времени для всех выбранных таблиц?

#MSG: Error message when source system is disconnected/agent is unreachable
BATCH_REPLICATION_NOT_SUPPORTED_AGENT_DISCONNECTED=Тиражирование данных не может начаться, так как агент провизионирования данных недоступен. Переподключите агент провизионирования данных и повторите попытку.
#MSG: Error message when source system is disconnected/agent is unreachable
BATCH_REPLICATION_NOT_SUPPORTED_AGENT_DISCONNECTED_FROM_REALTIME=Тиражирование данных не может начаться, так как агент провизионирования данных недоступен. Деактивируйте тиражирование данных в реальном времени или переподключите агент провизионирования данных и повторите попытку
#MSG: Error message when source system is disconnected/agent is unreachable
REALTIME_REPLICATION_NOT_SUPPORTED_AGENT_DISCONNECTED=Тиражирование данных в реальном времени невозможно активировать, так как агент провизионирования данных недоступен. Переподключите агент провизионирования данных и повторите попытку.
#MSG: Error message when source system is disconnected/agent is unreachable
REMOVE_REPLICATED_DATA_NOT_SUPPORTED_AGENT_DISCONNECTED=Тиражированные данные нельзя удалить, так как агент провизионирования данных недоступен. Переподключите агент провизионирования данных и повторите попытку.
#MSG: Error message when source system is disconnected/agent is unreachable
BATCH_REPLICATION_NOT_SUPPORTED_SOURCE_UNREACHABLE=Тиражирование данных не может начаться, так как недоступна исходная система. Переподключите исходную систему и повторите попытку.
#MSG: Error message when source system is disconnected/agent is unreachable
BATCH_REPLICATION_NOT_SUPPORTED_SOURCE_UNREACHABLE_FROM_REALTIME=Тиражирование данных не может начаться, так как недоступна исходная система. Деактивируйте тиражирование данных в реальном времени или переподключите исходную систему и повторите попытку.
#MSG: Error message when source system is disconnected/agent is unreachable
REALTIME_REPLICATION_NOT_SUPPORTED_SOURCE_UNREACHABLE=Тиражирование данных в реальном времени невозможно активировать, так как недоступна исходная система. Переподключите исходную систему и повторите попытку.
#MSG: Error message when source system is disconnected/agent is unreachable
REMOVE_REPLICATED_DATA_NOT_SUPPORTED_SOURCE_UNREACHABLE=Тиражированные данные нельзя удалить, так как недоступна исходная система. Переподключите исходную систему и повторите попытку.
