
#XBCB: Breadcrumb
remoteTableMonitorBreadcrumb={spaceId}


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Content ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Count for Tables in table Toolbar
txtTableCount=Fjärrtabeller ({0})
#XBUT: Button to start replication
startReplication=Starta replikering
#XBUT: Button to stop replication
stopReplication=Stoppa replikering
#XFLD: Placeholder for Search field
txtSearch=Sök
#XBUT: Tooltip for refresh button
txtRefresh=Uppdatera

#XFLD: Label for table column
txtConnection=Anslutning
#XFLD: Label for table column
txtConnectionLabel=Anslutning (affärsnamn)
#XFLD: Label for table column
txtConnectionLabelTech=Anslutning (tekniskt namn)
#XFLD: Label for table column
txtTable=Tabell
#XFLD: Label for table column
txtTableLabel=Affärsnamn
#XFLD: Label for table column
txtTableLabelTech=Tekniskt namn
#XFLD: Label for table column
txtTableLabelNew=Objekt (affärsnamn)
#XFLD: Label for table column
txtTableLabelTechNew=Objekt (tekniskt namn)
#XFLD: (Alternative) Label for table column "table"
txtTechnicalName=Tekniskt namn
#XFLD: Label for table column
txtAccessType=Dataåtkomst
#XBUT: label for action button
loadNewSnapshot=Läs in ny snapshot
#XBUT: Tooltip for refresh button
changeRefreshFrequency=Ändra uppdateringsfrekvens
#XFLD: Label for table column
replicationType=Replikeringstyp
#XFLD: Label for table column
txtRefreshFrequency=Uppdateringsfrekvens
#XFLD: Label for table column
txtRefreshFrequencyLabel=Frekvens
#XFLD: Label for table column
txtRefreshFrequencyLabelNew=Inplanerad frekvens
#XFLD: Label for table column
txtReplicationStatus=Status
#XFLD: Label for table column
txtNumberOfRecords=Antal poster
#XFLD: Label for table column
txtLatestChange=Senaste ändring (källa)
#XFLD: Label for table column
txtLatestChangeNew=Senaste ändring (källa)
#XFLD: Label for table column
txtLatestUpdate=Senaste uppdatering
#XFLD: Label for table column
txtLatestUpdateNew=Senaste uppdatering
#XFLD: Label for table column
txtInMemorySizeReplicaTableMB=Minne använt för lagring (MiB)
#XFLD: Label for table column
txtInMemorySizeReplicaTableMBNeww=Storlek för minnesbaserad (MiB)
#XFLD: Label for table column
txtInMemorySizeReplicaTableMBHeader=Storlek för minnesbaserad
#XFLD: Label for log details column
txtRemoteTableLogs=Fjärrtabelloggar
#XFLD: Label for log details column
txtRemoteTableLogsNew=Detaljer
#XFLD: Label for table column
txtDiskSizeReplicaTableInMB=Disk använd för lagring (MiB)
#XFLD: Label for table column
txtDiskSizeReplicaTableInMBNeww=Storlek på disk (MiB)
#XFLD: Label for table column
txtDiskSizeReplicaTableInMBHeader=Storlek på disk
#XFLD: Label for table column
txtInMemorySizeReplicaTableMBLabel=Minne använt för lagring
#XFLD: Label for table column
txtDiskSizeReplicaTableInMBLabel=Disk använd för lagring
#XFLD: Label for schedule owner column
txtScheduleOwner=Schemaägare
#XFLD: Label for schedule owner tooltip
txtScheduleOwnerToolTip=Visar vem som äger schemat
#XMSG: Error message for unknown replication type that gets logged in Kibana
unknownReplicationTypeError=Replikeringstyp "{0}" medges ej.
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Ej tillämplig

#XTIT: Title for Refresh Frequency Dialog which shows the table name
titleRefreshFrequencyDialog={0}
#XFLD: text inside Refresh Frequency Dialog
refreshFrequency=Uppdateringsfrekvens
#XBUT: label for radio button in Refresh Frequency Dialog
none=Ingen
#XBUT: label for radio button Refresh Frequency Dialog
realtime=Realtid
#XMSG: info message in Refresh Frequency Dialog that real-time is not supported
infoDisableRealtimeNotSupported=Inaktivering av realtidsdatareplikering medges ej för denna fjärrtabell.<br><br>Mer hjälp hittar du <a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/82380e6f01c84fac898a285ac40a1e50.html?locale=en-US" >här</a>.
txt_lnk_fabricVirtualTable=Fabric Virtual Table
#XMSG: info message that real-time check failed
infoRealtimeReplicationCheckFailed=Kontroll av realtidsreplikering misslyckades.
#XMSG: info message that real-time is supported
infoRealtimeReplicationSupported=Realtidsreplikering medges.
#XMSG: busy indicator for real-time replication check
infoRealtimeReplicationCheckIndicator=Kontrollerar realtidsreplikering...
#XMSG: info message in Refresh Frequency Dialog that real-time support requires re-deployment
infoRealtimeSupportReqReDeployment=Du måste distribuera om tabellen om du vill använda realtidsreplikering för denna tabell.
#XMSG: info message in Refresh Frequency Dialog that real-time support requires adding of primary key
infoRealtimeSupportReqPrimKey=Du måste lägga till en primärnyckel om du vill använda realtidsreplikering för denna tabell.
#XMSG: info message in Refresh Frequency Dialog that real-time support requires re-deployment and adding of primary key
infoRealtimeSupportReqReDeplAndPrimKey=Du måste distribuera om tabellen och lägga till en primärnyckel om du vill använda realtidsreplikering för denna tabell.
#XBUT: Button to Close Schedule Settings in Refresh Frequency Dialog if nothing was changed
close=Stäng
#XBUT: Button to Cancel Schedule Settings in Refresh Frequency Dialog
cancel=Avbryt
#XMSG For Scheduled text
scheduled=Inplanerad
#XMSG For Scheduled text
paused=Pausad
#XFLD: text for values shown for Ascending sort order
SortInAsc=Sortera stigande
#XFLD: text for values shown for Descending sort order
SortInDesc=Sortera fallande

#XFLD: text for values shown in column Replication Status
txtOff=Av
#XFLD: text for values shown in column Replication Status
txtInitializing=Initialiserar
#XFLD: text for values shown in column Replication Status
txtRunning=Körs
#XFLD: text for values shown in column Replication Status
txtActive=Aktiv
#XFLD: text for values shown in column Replication Status
txtAvailable=Tillgänglig
#XFLD: text for values shown in column Replication Status
txtError=Fel
#XFLD: text for values shown in column Replication Status
txtPaused=Pausad
#XFLD: text for values shown in column Replication Status
txtDisconnected=Frånkopplad
#XMSG: error message for reading data from backend
txtReadBackendError=Ett fel inträffade vid läsning från backend.

#XMSG: error message while stopping the replication
stopReplicationError=Ett fel inträffade vid stopp av replikering av tabell "{0}".
#XMSG: error message while stopping the replication
disableRealTimeReplicationError=Ett fel inträffade vid inaktivering av realtidsdatareplikering för tabell "{0}".

#XMSG: success message for starting replication the first time
startReplicationSuccess=Vi replikerar tabell "{0}".
#XMSG: success message for loading new snapshot
loadNewSnapshotSuccess=Vi uppdaterar tabell "{0}" med de senaste ändringarna.
#XMSG: success message for stopping replication
stopReplicationSuccess=Vi flyttar replikerade data för tabell "{0}".
#XMSG: success message for stopping replication
disableRealTimeReplicationSuccess=Vi inaktiverar realtidsdatareplikering för tabell "{0}".
#XBUT: label for combobox
txtReplicated=Replikerad
#XBUT: label for combobox
txtRemote=Fjärr

#XBUT: label for remote data access
REMOTE=Fjärr
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Replikerad (realtid)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Replikerad (snapshot)

#XBUT: label for radio button
txtNone=Ingen
#XBUT: label for radio button
txtRealtime=Realtid
#XMSG: error message during start/stop replication of a table (backend error)
ERROR_EXECUTING_REPLICATION=Ett fel inträffade vid replikering av tabell "{0}".
#XMSG: Error when illegal navigation is invoked
illegalNavigation=Du är för närvarande inte medlem i "{0}". Välj bland följande utrymmen.
#XMSG: Error when user is not authorized to schedule tasks
authorizeWarn=Du måste auktorisera tjänsten för att planera in uppgifter. <a href="">Auktorisera</a>
#XFLD: Message to schedule task
scheduleText=Replikering av schema
#XFLD: Message to schedule task
scheduleTextLabel=Planera in
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Skapa snapshot-schema
#XBUT: Drop down menu button to create a schedule
createScheduleNewLabel=Skapa schema
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Redigera schema
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Radera schema
#XBUT: Label for scheduled replication
scheduledTxt=Inplanerad
#XFLD: Label for table column
txtNextSchedule=Nästa körning
#XFLD: Label for table column
txtNextScheduleNew=Inplanerad nästa körning
#XFLD: Label for frequency column
everyLabel=Varje
#XFLD: Plural Recurrence text for Hour
hoursLabel=Timmar
#XFLD: Plural Recurrence text for Day
daysLabel=Dagar
#XFLD: Plural Recurrence text for Month
monthsLabel=Månader
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuter
#XFLD: Message for replicate table action
replicateTableText=Tabellreplikering
#XFLD: Message for replicate table action
replicateTableLabelText=Datareplikering
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Läs in ny snapshot
#XBUT: Drop down menu button to start data replication
startDataReplicationLabel=Starta datareplikering
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Ta bort replikerade data
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Aktivera realtidsåtkomst
#XBUT: Drop down menu button to enable real time replication
enableRealTimeDataReplicationLabel=Aktivera realtidsdatareplikering
#XBUT: Drop down menu button to disable real time replication
disableRealTimeReplicationLabel=Inaktivera realtidsdatareplikering
#XBUT: Drop down menu button to navigate to space connection list page
viewDocumentation=Visa dokumentation
#XFLD: Documentation Link for real time replication
goToConnectionsList=Gå till anslutningslista
#XMSG: Warning message for removing the persisted data
RemoveReplication_Confirm_Msg=Vill du ändra dataåtkomst och radera replikerade data för tabell "{0}"?
#XMSG: error message for replication not started
txtReplicationStartError=Replikering kunde inte startas eftersom aktuella replikerade data inte har tagits bort.
#XMSG: busy indicator for loading new snapshot
infoLoadingNewSnapshotIndicator=Läser in ny snapshot...
#XMSG: busy indicator for stopping real-time replication
infoStopRealtimeReplicationIndicator=Realtidsreplikeringen stoppas innan ny snapshot läses in.
#XMSG: busy indicator for starting batch replication
infoStartBatchReplicationIndicator=Startar bakgrundsreplikering...
#XMSG: message for real-time replication not supported
REALTIME_REPLICATION_NOT_SUPPORTED=Realtidsreplikering medges ej för "{0}".
#XMSG: message for replication not supported (snapshot and real time replication)
REPLICATION_NOT_SUPPORTED=Data för "{0}" kan inte replikeras: {1}.
#XMSG: message for replication not supported due to unsupported data type (snapshot and real time replication)
REPLICATION_NOT_SUPPORTED_UNSUPPORTED_DATA_TYPE=Data för "{0}" kan inte replikeras eftersom de innehåller otillåten datatyp.
#XMSG: message for replication not supported due to an issue with RMS
REPLICATION_NOT_SUPPORTED_RMS_ISSUE=Data för "{0}" kan inte replikeras eftersom de inte kan ansluta till replikeringshanteringstjänsten: {1}.
#XMSG: message for conflicting task
Task_Already_Running=Uppgift i konflikt körs redan för tabell "{0}".
#XMSG: message for Remote Source not found
REMOTE_SOURCE_NOT_FOUND=Fjärrkälla hittades inte.
#XMSG: message for Replication type missing
REPLICATION_TYPE_MISSING=Replikeringstyp saknas.
#XMSG: message for run missing for cancellation
CANCEL_MISSING_RUN=Körning saknas för avbrytande för fjärrtabell "{0}".
#XMSG: message for failure to cancel the replication because it was already completed
CANCEL_COMPLETED_REPLICATION_RUN=Process för replikering av tabell "{0}" kunde inte avbrytas eftersom den redan har slutförts.
#XMSG: message for failure to cancel the replication because it was not started yet.
CANCEL_UNREACHABLE_REPLICATION_RUN=Process för replikering av tabell kunde inte avbrytas eftersom datareplikering för tabell "{0}" inte har startat än.
#XMSG: message for unexpected replication status
UNEXPECTED_REPLICATION_STATUS=Oväntad replikeringsstatus för fjärrtabell "{0}".
#XMSG: message for failure to remove replicated data because the replication had been canceled.
REMOVE_CANCELED_REPLICATION_RUN=Replikerade data kunde inte tas bort. Processen för replikering av tabell "{0}" hade redan avbrutits

#XFLD: Message for remote table replication logs
CANCELING_REPLICATION_TASK=Avbryter körande replikering för fjärrtabell "{1}"...
#XFLD: Message for remote table replication logs
CANCELING_REPLICATION_TASK_SUCCESS=Körande replikering avbruten för fjärrtabell "{1}".
#XFLD: Message for remote table replication logs
CANCELING_REPLICATION_TASK_FAILED=Körande replikering kan inte avbrytas för fjärrtabell "{1}".
#XFLD: Message for remote table replication logs
DISABLING_REALTIME_TASK_SUCCESS=Realtidsdatareplikering har inaktiverats för fjärrtabell "{1}".
#XFLD: Message for remote table replication logs
DISABLING_REALTIME_TASK_FAILED=Realtidsdatareplikering kan inte inaktiveras för fjärrtabell "{1}".
#XFLD: Message for remote table replication logs
CANCEL_REPLICATION_TASK=Replikeringsåtgärd avbruten av begäran för tabell "{1}"
#XMSG: Message for remote table replication logs
SWITCH_FROM_BATCH_TO_REALTIME=Växlar replikeringstyp för fjärrtabell "{1}" från bakgrundsreplikering till realtidsreplikering.
#XMSG: Message for remote table replication logs
SWITCH_FROM_REALTIME_TO_BATCH=Växlar replikeringstyp för fjärrtabell "{1}" från realtidsreplikering till bakgrundsreplikering.
#XFLD: Message for remote table replication logs
START_REALTIME_REPLICATION=Replikerar (realtids)data för fjärrtabell "{1}".
#XMSG: Message for remote table replication logs
DROP_SUBSCRIPTION_WARNING=Du måste manuellt radera objekten som har genererats för registrering av dataändringar i fjärrkällan.
#XFLD: Message for remote table replication logs
START_BATCH_REPLICATION=Replikerar (bakgrunds)data för fjärrtabell "{1}".
#XFLD: Message for remote table replication logs
STOP_REPLICATION=Tar bort replikerade data för fjärrtabell "{1}"
#XFLD: Message for remote table replication logs
DISABLE_REALTIME=Inaktiverar realtidsdatareplikering för fjärrtabell "{1}"
#XFLD: Message for remote table replication logs
START_BATCH_REPLICATION_BLOCKED=Replikering av (bakgrunds)data för fjärrtabell "{1}" är för närvarande spärrad.
#XFLD: Message for remote table replication logs
START_BATCH_REPLICATION_SUCCESS=(Bakgrunds)data har replikerats för fjärrtabell "{1}" .
#XFLD: Message for remote table replication logs
STOP_REPLICATION_BLOCKED=Borttagning av replikerade data för fjärrtabell "{1}" är för närvarande spärrad.
#XFLD: Message for remote table replication logs
START_REALTIME_REPLICATION_SUCCESS=(Realtids)data har replikerats för fjärrtabell "{1}".
#XFLD: Message for remote table replication logs
STOP_REPLICATION_SUCCESS=Replikerade data har tagits bort och fjärrdataåtkomst har återställts för fjärrtabell "{1}".
#XFLD: Message for remote table replication logs
START_BATCH_REPLICATION_FAILED=Bakgrunds(data) kan inte replikeras för fjärrtabell "{1}".
#XFLD: Message for remote table replication logs
START_REALTIME_REPLICATION_FAILED=(Realtids)data kan inte replikeras för fjärrtabell "{1}".
#XFLD: Message for remote table replication logs
STOP_REPLICATION_FAILED=Replikerade data för fjärrtabell "{1}" kan inte tas bort.
#XFLD: Message for remote table replication logs
TABLE_REPLICATION_FAILED=Data kan inte replikeras för fjärrtabell "{1}".
#XFLD: Message for remote table replication logs
START_REPLICATION_RECORD_COUNT_SUCCESS="{3}" poster har replikerats för tabell "{1}".
#XFLD: Message for remote table replication logs including number of partitions
START_REPLICATION_RECORD_COUNT_PARTITIONS_SUCCESS={3} poster har replikerats för tabell "{1}" i {4} partitioner.
#XFLD: Message for successful replication of a single partition
START_REPLICATION_SINGLE_PARTITION_SUCCESS={0} poster har replikerats till partition för värden "{1}" <= {2} < "{3}".
#XFLD: Message for successful replication of a "others" partition
START_REPLICATION_OTHERS_PARTITION_SUCCESS={0} poster har replikerats till partition "andra".
#XFLD: Message for pre-query returning no value for partitioning
PRE_QUERY_PARTITIONS_NO_VALUE=Pre-query som används för att matcha partitioner med datakällan har inte returnerat något värde.
#XFLD: Message for pre-query of partitioning
PRE_QUERY_PARTITIONS=Pre-query som används för att matcha partitioner med datakällan har returnerat {0} värden som ska allokeras till {1} partitioner.
#XFLD: Message for remote table replication logs
START_REPLICATION_RECORD_COUNT_FAILED=Poster i tabell  "{1}" kunde inte räknas.
#XFLD: Message for remote table replication logs
STOP_REPLICATION_RECORD_COUNT_SUCCESS="{3}" replikerade poster har tagits bort för fjärrtabell "{1}".
#XFLD: Message for remote table replication logs
DISABLE_REALTIME_RECORD_COUNT_SUCCESS="{3}" replikerade poster har behållits för fjärrtabell "{1}".
#XFLD: Message for remote table replication logs
STOP_REPLICATION_RECORD_COUNT_FAILED=Poster i tabell  "{1}" kunde inte räknas.
#XFLD: Cancel Run label
TEXT_CancelRun=Avbryt körning
#XMSG: Cancel Run warning Message
Msg_CancelRun=Vill du avbryta aktuell körning?
#XMSG: Re-start real-time replication action
txtPausedRealTimeReplStatusPopover=För realtidsåtkomst,\nstarta om replikeringen för\nanslutningen {0}
#XFLD: Message for remote table replication logs
SUBSCRIPTION_APPLIED=Initial dataöverföring för realtidsreplikering för fjärrtabell "{1}" lyckades. Realtidsreplikering lägger nu automatiskt till deltadata till fjärrtabell "{1}".
#XFLD: Message for remote table replication logs
REPLICATION_FILTER_CONDITION=Tabell "{1}" replikerade med följande filtervillkor: "{5}".
#XFLD: Message for remote table replication logs
START_REPLICATION_SYNC=Förbereder replikering av data i synkront läge.
#XFLD: Message for remote table replication logs
START_REPL_REFRESH_SYNC=Förbereder uppdatering av data i synkront läge.
#XFLD: Message for remote table replication logs
START_REPLICATION_ASYNC=Förbereder replikering av data i asynkront läge.
#XFLD: Message for remote table replication logs
START_REPL_REFRESH_ASYNC=Förbereder uppdatering av data i asynkront läge.
#XFLD: Message for remote table replication logs
DELEGATE_TO_HANA_ASYNC=Delegerar uppgiftsutförande-ID {0} till SAP HANA.
#XFLD: Message for remote table replication logs
SUBSCRIBED_TO_SRC_DATA_CHG=Abonnerar på ändringar av källsystemsdata.
#XFLD: Message for remote table replication logs
START_INITIAL_LOAD=Infogar data i replikeringstabell.
#XFLD: Message for remote table replication logs
START_INITIAL_LOAD_CDI=Infogar data i replikeringstabell.
#XFLD: Message for remote table replication logs
APPLYING_DATA_CHANGES=Tillämpar dataändringar på replikeringstabell.
#XFLD: Message for remote table replication logs
SWITCHED_DATA_ACCESS_BATCH=Växlar dataåtkomst (bakgrund) till ny replikering.
#XFLD: Message for remote table replication logs
SWITCHED_DATA_ACCESS_REALTIME=Växlar dataåtkomst (realtid) till ny replikering.
#XMSG: Real-time replication has been enabled for remote table
REALTIME_REPLICATION_ENABLED=Nästa körning av den här uppgiftskedjan läser in en ny snapshot och ändrar aktuellt realtidsreplikeringsläge till ett bakgrundsreplikeringsläge. Data uppdateras inte längre i realtid.
#XMSG: Remote table is only available in federation mode.
FEDERATION_ONLY="{0}" är endast tillgänglig i fjärrläge
#XMSG: Remote table replication blocked as space is locked.
START_REMOTE_TABLE_REPLICATION_LOCKED_SPACE_ERROR=Fjärrtabell "{1}" kan inte replikeras eftersom utrymme "{2}" är spärrat.
##XFLD: Message for missing authorizations for remote table replication
REMOTE_TABLE_REPLICATION_MISSING_AUTH=Behörighet saknas för replikering av fjärrtabell.
#XFLD: Message for out of memory error
RESOURCE_LIMIT_ERROR_OUT_OF_MEMORY=Uppgiften misslyckades på grund av ett minnesbristfel i SAP HANA-databasen.
#XFLD: Message for Admission Control Rejection
RESOURCE_LIMIT_ERROR_ADMISSION_CONTROL=Uppgiften misslyckades på grund av avvisning av åtkomstkontroll för SAP HANA.
#XFLD: Message for SAP HANA back pressure problem
RESOURCE_LIMIT_ERROR_HANA_BACK_PRESSURE=Uppgiften misslyckades på grund av för många aktiva SAP HANA-anslutningar.

#XFLD: Error while accessing pertitioning for a remote table
missingPrivilegeInRemoteTables=Otillräcklig behörighet för att utföra partitionering för fjärrtabell "{0}"

#MSG: Error header for realtime replication task
errorHeaderInitRealtime=Ett fel inträffade under initial dataöverföring för realtidsdatareplikering.
#MSG: Error header for realtime replication subscription
errorHeaderLoadRealtime=Ett fel inträffade under realtidsreplikering.
#MSG: Error header for snapshot replication task
errorHeaderLoadSnapshot=Ett fel inträffade under snapshot-replikering.
#MSG: Error details for replication task "starting" phase (remote-table name, date, time)
errorDetailsTaskStarting=Den {1} kl. {2} inträffade ett internt fel vid start av datareplikering för fjärrtabell "{0}".
#MSG: Error details for replication task "preparing" phase (remote-table name, date, time, SQL error code)
errorDetailsTaskPreparingStage=Den {1} kl. {2} genererades en SQL-felkod ({3}) vid förberedelse av datareplikering för fjärrtabell "{0}".
#MSG: Error details for replication task (remote-table name, date, time, SQL error code)
errorDetailsTaskGeneral=Den {1} kl. {2} genererades en SQL-felkod ({3}) vid replikering av data för fjärrtabell "{0}".
#MSG: Error details for replication task (remote-table name, date, time)
errorDetailsTaskNoCode=Den {1} kl. {2} inträffade ett internt fel vid replikering av data för fjärrtabell "{0}".
#MSG: Error details for replication task (remote-table name)
errorDetailsTaskFallback=Ett internt fel inträffade vid replikering av data för fjärrtabell "{0}".
#MSG: Error details for replication task "finalizing" phase (remote-table name, date, time, SQL error code)
errorDetailsTaskFinalizingStage=Den {1} kl. {2} genererades en SQL-felkod ({3}) vid slutförande av datareplikering för fjärrtabell "{0}".
#MSG: Error details for unspecific real-time error (remote-table name, date, time)
errorDetailsRealtime=Ett fel inträffade den {1} kl. {2} som förhindrar att data för fjärrtabell "{0}" replikeras i realtid.
#MSG: Error details for unspecific real-time error (remote-table name)
errorDetailsRealtimeFallback=Ett fel inträffade som förhindrar att data för fjärrtabell "{0}" replikeras i realtid.
#MSG: Error details for FVT-RMS real-time error (remote-table name) with Auto Retry option
errorDetailsRealtimeFallbackAutoRetry=Ett fel spärrar realtidsreplikering av fjärrtabell "{0}". Klicka på länken nedan för att visa detaljerat felmeddelande.\nSystemet försöker regelbundet i en vecka att aktivera realtidsreplikeringen på nytt. Om du åtgärdar felet kan du klicka på Försök igen för att manuellt aktivera den.
#MSG: Error details for connection-level real-time exception (remote-table name, date, time, exception id, connection name)
errorDetailsRealtimeConnection=Fel-ID {3} inträffade i anslutning "{4}" den {1} kl. {2} som förhindrar data i fjärrtabell "{0}" att replikeras i realtid.
#MSG: Error details for table-level real-time exception (remote-table name, date, time, exception id, other remote-table name)
errorDetailsRealtimeTable=Fel-ID {3} inträffade under realtidsreplikering av fjärrtabell "{4}" den {1} kl. {2} som förhindrar data i fjärrtabell "{0}" att replikeras i realtid.
#MSG: Advice on error to view details
errorAdvice=Använd länken nedan för att visa detaljerat felmeddelande.
#MSG: Advice on error to view details and check logs (remote-table name)
errorAdviceLogs=Använd länkarna nedan för att visa detaljerat felmeddelande och kontrollera replikeringsuppgiftsprotokollen för fjärrtabell "{0}".
#MSG: Advice on error to view details and validate connection (connection name)
errorAdviceConnection=Använd länkarna nedan för att visa detaljerat felmeddelande och validera anslutning "{0}".
#MSG: Advice on error to view details, validate connection, and check DP agent (connection name, agent name)
errorAdviceConnectionAgent=Använd länkarna nedan för att visa detaljerat felmeddelande, validera anslutning "{0}" och kontrollera protokoll för datahämtningsagent "{1}".
#MSG: Advice on error to view details and check monitor details (remote-table name)
errorAdviceTable=Använd länkarna nedan för att visa detaljerat felmeddelande och kontrollera monitordetaljer för fjärrtabell "{0}".
#MSG: Advice on error to view details, check monitor details, and check DP agent (remote-table name, agent name)
errorAdviceTableAgent=Använd länkarna nedan för att visa detaljerat felmeddelande, kontrollera monitordetaljer för fjärrtabell "{0}" och kontrollera protokoll för datahämtningsagent "{1}".
#MSG: Advice to retry after the error is corrected
errorAdviceRetry=När felet har korrigerats kan du använda åtgärden "Försök igen" för att fortsätta med realtidsreplikering.
#XFLD Label Go to connections link
goToConnections=Gå till Anslutningar
#XFLD Label Go to Details link
goToDetails=Gå till Detaljer
#XFLD Label Go to configuration link
goToDpAgents=Gå till Konfiguration
#XFLD Label go to other object details link
goToOtherDetails=Gå till Detaljer för {0}
#XFLD Label Retry link
retry=Försök igen
#XFLD Show more
showMoreText=Visa mer...

#XFLD: text retry
Retry=Bekräfta
#XMG: Retry confirmation text
retryConfirmationTxt=Den senaste realtidsreplikeringen avslutades med ett fel.\n Bekräfta att felet har korrigerats och att realtidsreplikering kan startas om.
#XMG: Retry success text
retrySuccess=Upprepningsförsök för process har initierats.
#XMG: Retry fail text
retryFail=Upprepningsförsök för process misslyckades.
#XMG: Retry fail text
RETRY_FAILED_ERROR=Upprepningsförsök för process misslyckades med ett fel.

#XMSG: Message for create statistics in remote table
START_CREATE_STATISTICS=Skapar statistik av typen {1} för {0}.
#XMSG: Message for start statistics
CREATE_STATISTICS_SUCCESS=Statistik av typen {1} har skapats för {0}.
#XMSG: Message for error in creating statistics
CREATE_STATISTICS_FAIL=Statistik av typen {1} för {0} kunde inte skapas.

#XMSG: Message for starting refresh statistics
START_REFRESH_STATISTICS=Uppdaterar statistik för {0}.
#XMSG: Success message for refresh statistics
REFRESH_STATISTICS_SUCCESS=Statistik av typen {1} för {0} har uppdaterats.
#XMSG: Error message for refresh statistics
REFRESH_STATISTICS_FAIL=Statistik av typen {1} för {0} kunde inte uppdateras.

#XMGS: Message for Start changing the statistics type
START_ALTER_STATISTICS=Ändrar statistiktyp till {1} för {0}.
#XMSG: Success message for altering statistics type
ALTER_STATISTICS_SUCCESS=Statistik av typen {1} har ändrats för {0}.
#XMSG: Error message for altering statistics type
ALTER_STATISTICS_FAIL=Statistik av typen {1} för {0} kunde inte ändras.

#XMSG: Message for starting to drop statistics from remote table
START_DROP_STATISTICS=Raderar statistik för {0}.
#XMSG: Success message for drop statistics
DROP_STATISTICS_SUCCESS=Statistik för {0} har raderats.
#XMSG: Error message for drop statistics
DROP_STATISTICS_FAIL=Statistik för {0} kunde inte raderas.

#XMSG: Log to let know that partitioning has been removed automaticaly
PARTITIONING_AUTO_DELETE=Partitioneringsinformation har tagits bort automatiskt eftersom partitionerad kolumn inte längre finns i tabell "{1}".
#XMSG: Log to let know that real-time replication does not support partitioning
REPLICATION_REAL_TIME_PARTITION=Partitioner kan inte användas för realtidsreplikering.
#XMSG: Log to let know that CDI real-time replication should use at least dpagent version 2.6.1
REPLICATION_REAL_TIME_CDI=Tidigare version än 2.6.1 för datahämtningsagent ({4}) rekommenderas inte för realtidsreplikering via CDI-adapter.
#XMSG: Log to let know that CDI real-time replication should use at least dpagent version 2.6.1
REPLICATION_REAL_TIME_CDI_NO_VERSION=Version för datahämtningsagent kunde inte läsas. Tidigare version än 2.6.1 rekommenderas inte för realtidsreplikering via CDI-adapter.

#XMSG: log warning about multiple remote tables pointing to the same source and being replicated (fvt)
REPLICATION_REPLICA_SHARING=Fjärrtabellen {1} hänvisar till samma källtabell som andra fjärrtabeller och det finns andra replikeringar tillgängliga för den källtabellen. Vi rekommenderar att endast behålla en replikering.

#XMSG: log warning about retring running (fvt)
REPLICATION_REAL_TIME_AUTO_RETRY=Realtidsreplikering har spärrats och vi försöker aktivera den på nytt.
REPLICATION_REAL_TIME_FIRST_AUTO_RETRY=Försöker realtidsreplikering igen.
REPLICATION_REAL_TIME_SUCCESSFUL_AUTO_RETRY=Det nya försöket har aktiverat realtidsreplikering på nytt.
REPLICATION_REAL_TIME_LAST_AUTO_RETRY=Det nya försöket kunde inte aktivera realtidsreplikering på nytt.

partitionButton=Partition
OBJECT_NAME=Vald tabell
Column=Kolumn
Parallel_Process=Parallella processer
Partitions=Partitioner
Add_Partitions=Lägg till partitioner
btnSave=Spara
btnClear=Rensa
btnRestore=Återställ
btnCancel=Avbryt
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Column header tooltip ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: tooltip for table column
txtConnectionTooltip=Visar namn på anslutning som fjärrtabell tillhör
#XFLD: tooltip for table column
txtTableTooltip=Visar namn på fjärrtabell
#XFLD: tooltip for table column
txtDataAccessTooltip=Visar hur data för närvarande nås
#XFLD: tooltip for table column
txtRefreshFreqTooltip=Visar hur ofta data replikeras. Värdet visar avsedd frekvens som kanske inte har uppnåtts ännu beroende på status för fjärrtabell
#XFLD: tooltip for table column
txtStatusTooltip=Visar status för fjärrtabell
#XFLD: tooltip for table column
txtLatestChangeTooltip=Visar senaste gången data ändrades i källan.
#XFLD: tooltip for table column
txtNumberOfRecordsTooltip=Visar antalet poster i tabellen.
#XFLD: tooltip for table column
txtLatestUpdateTooltip=Visar senaste gången data uppdaterades.
#XFLD: tooltip for table column
txtNextRunTooltip=Se när nästa körning är inplanerad om schema finns för fjärrtabell
#XFLD: tooltip for table column
txtInMemorySizeTooltip=Spåra hur mycket utrymme fjärrtabellen använder i minnet
#XFLD: tooltip for table column
txtDiskSizeTooltip=Spåra hur mycket utrymme fjärrtabellen använder på disken
#XMSG: Expired text
txtExpired=Utgången
#XMSG: Open BW4 Cockpit button text
openBw4CockpitTxt=Öppna cockpit för SAP BW Bridge

#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objekt "{0}" kan inte läggas till i uppgiftskedja.
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE_REMOTETABLE=Objekt "{0}": Replikeringstyp ändras.

#XMSG: Info message when there is shared replica table
sharedReplicaInfoText=Fjärrtabellen hänvisar till samma källtabell som andra fjärrtabeller.
sharedReplicaWarningText=Den här fjärrtabellen hänvisar till samma källtabell som andra fjärrtabeller där replikering redan är aktiverad eller schemalagd. Du kan inte aktivera replikering eller schemaläggning.
sharedReplicaErrorText=Replikering har inte aktiverats. Fjärrtabellen hänvisar till samma källtabell som andra fjärrtabeller som redan har aktiverat eller schemalagt replikering. Använd antingen replikerade data från dessa tabeller eller ta bort deras schema och replikerade data för att tillåta schemaläggning och replikering för fjärrtabellen. Om tabellen som har replikerat data finns i ett annat utrymme måste du se till att den delas till detta utrymme.

#XBTN: Button to close the dialog
cancelText=Avbryt
okText=Fortsätt ändå

#XMSG: warning message to show the change from realtime replication to batch
warn_realtime=Data replikeras för närvarande i realtidsläge. Om en ny snapshot läses in ändras replikeringstypen till bakgrundsreplikering, och data uppdateras inte längre i realtid.
warn_changeto_batch=Fjärrtabellen används i uppgiftskedjor. Nästa körning av en uppgiftskedja ändrar dataåtkomsttypen för fjärrtabellen och data uppdateras inte längre i realtid.
txt_lnk_impactAnalysis=Effekt- och ursprungsanalys

#XMSG: Replication will change
txt_replication_change=Replikeringstyp ändras.
txt_repl_viewdetails=Visa detaljer
msg_repl_viewdet_schedule_content=Ett definierat schema kör en ny snapshot den {0}. Detta ändrar replikeringsläget från realtidsreplikering till bakgrundsreplikering, och data uppdateras inte längre i realtid.
msg_repl_viewdet_taskchain_content=Data replikeras för närvarande i realtidsläge. Fjärrtabellen används i uppgiftskedjor. Nästa körning av en uppgiftskedja ändrar replikeringstypen till bakgrundsreplikering, och data uppdateras inte längre i realtid.

#XFLD: dialog section
Background=Bakgrund:
#XFLD: dialog section
Cause=Orsak:
#XFLD: dialog section
Solution=Lösning:
#XFLD: dialog section
Warning=Varning

dropSubscriptionCancelRealTime=Du är på väg att avbryta aktivering av realtidsdatareplikering för fjärrtabell "{1}".
#MSG: Before drop HANA subscription, prompt the user with warning
dropSubscriptionSwitch=Du är på väg att ändra replikeringstyp från realtidsreplikering till bakgrundsreplikering för fjärrtabell "{1}".
#MSG: Before drop HANA subscription, repeat the warning in the task log
dropSubscriptionSwitched=Replikeringstyp för fjärrtabell "{1}" kommer att ändras från realtidsreplikering till bakgrundsreplikering.
#MSG: Before drop HANA subscription, prompt the user with warning
dropSubscriptionRemove=Du är på väg att ta bort replikerade data från fjärrtabell "{1}", för vilken dataåtkomst i realtid är aktiverat.
#MSG: Before drop HANA subscription, prompt the user with warning
dropSubscriptionDisableRealTime=Du är på väg att inaktivera realtidsdatareplikering från fjärrtabell "{1}".
#MSG: Before drop HANA subscription, repeat the warning in the task log
dropSubscriptionRemoved=Replikerade data kommer att tas bort från fjärrtabell "{1}" för vilken realtidsdataåtkomst är aktiverad.
#MSG: Before drop HANA subscription, explain about database triggers
dropSubscriptionTrigger=Ett aktivt fjärrabonnemang för fjärrtabell "{1}" i din SAP HANA Cloud-instans har genererat triggrar och en skuggtabell för registrering av dataändringar i din fjärrdatabas.
#MSG: Before drop HANA subscription, explain about ABAP subscription
dropSubscriptioninAbap=Ett aktivt fjärrabonnemang för fjärrtabell "{1}" i din SAP HANA Cloud-instans har skapat ett abonnemang för registrering av dataändringar i ditt ABAP-fjärrsystem.
#MSG: Before drop HANA subscription, tell that connection has exceptions
dropSubscriptionHasExceptions=Denna realtidsreplikering har för närvarande undantag på anslutningsnivå.
#MSG: Before drop HANA subscription, tell that connection is paused
dropSubscriptionIsPaused=Denna realtidsreplikering är för närvarande pausad på anslutningsnivå.
#MSG: Before drop HANA subscription, tell that DP Agent is disconnected
dropSubscriptionIsDisconnected=Datahämtningsagenten är för närvarande frånkopplad.
#MSG: Before drop HANA subscription, tell that the connection is invalid
connectionIsInvalid=Källsystemet för närvarande inte tillgängligt.
#MSG: Before drop HANA subscription, tell that only target side is deleted
dropSubscriptionImpact=Om du fortsätter kommer endast fjärrabonnemang på målsidan att raderas.
#TODO: Same with "would"?
#MSG: Before drop HANA subscription, tell that database triggers must be deleted manually
dropSubscriptionImpactTrigger=Du måste manuellt radera triggrar och skuggtabell för fjärrobjekt {6} på källsidan med det unika systemobjektsprefixet "{5}" enligt beskrivningen i SAP-not 3307334.
#TODO: Same with "would"?
#MSG: Before drop HANA subscription, tell that ABAP subscription must be deleted manually
dropSubscriptionImpactOnAbap=Du måste manuellt radera ABAP-baserat abonnemang "{6}" på källsidan enligt beskrivningen i SAP-not 3307334.
#TODO: Same with "would"?
#MSG: Before drop HANA subscription, recommend to process all exceptions
dropSubscriptionProcess=Vi rekommenderar därför att du bearbetar alla undantag för den här anslutningen innan du fortsätter.
#TODO: Same with "try it again"?
#MSG: Before drop HANA subscription, recommend to resume real-time replication
dropSubscriptionResume=Vi rekommenderar därför att du startar om realtidsreplikering på anslutningsnivå innan du fortsätter.
#TODO: Same with "try it again"?
#MSG: Before drop HANA subscription, recommend to reconnect the DP agent
dropSubscriptionReconnect=Vi rekommenderar därför att du återansluter datahämtningsagenten innan du fortsätter.
#TODO: Same with "try it again"?
#MSG: Before drop HANA subscription, recommend to directly trigger the same activity again
dropSubscriptionIgnore=Du kan ignorera det här felet när samma åtgärd körs direkt.
#XFLD: text proceed
continueAnyway=Fortsätt ändå
#XMSG: Warning text when performing mass operation of removing replicated data.
massReplicationRemoveWarnText=Vill du ändra dataåtkomst till fjärr och ta bort replikerade data?
#XMSG: warning text when performing mass operation of removing replication on Partial set of selected tables
massReplicationPartialRemoveWarnText={0} av {1} valda fjärrtabeller har replikerade data som kan tas bort. \nVill du ändra dataåtkomst till fjärr och ta bort replikerade data?
#XMSG: error message while mass stopping the replication
stopMassReplicationError=Ett fel ser ut att ha inträffat vid stopp av replikering av tabell.
#XMSG: success message for stopping replication
stopMassReplicationSuccess=Vi tar bort replikerade data för valda tabeller.

#XBUT: Assign schedule menu button label
assignScheduleLabel=Allokera schema till mig
#XBUT: Pause schedule menu label
pauseScheduleLabel=Pausa schema
#XBUT: Resume schedule menu label
resumeScheduleLabel=Återuppta schema
#XMSG: Failure message for Mass Schedule Delete
errorMassRemoveScheduleTxt=Ett fel inträffade när scheman skulle tas bort.
#XMSG: Failure message for Mass Owner Change
errorMassScheduleOwnerChangeTxt=Ett fel inträffade när scheman skulle allokeras.
#XMSG: Failure message for Mass Pause Schedule
errorMassPauseScheduleTxt=Ett fel inträffade när scheman skulle pausas.
#XMSG: Failure message for Mass Resume Schedule
errorMassResumeScheduleTxt=Ett fel inträffade när scheman skulle återupptas.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Raderar {0} scheman
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Ändrar ägare för {0} scheman
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Pausar {0} scheman
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Återupptar {0} scheman

#XMSG: Message for starting refresh statistics
VALIDATING_REMOTE_TABLES=Validerar {0} fjärrtabeller
#XMSG: <table name which was validated>: not changed
NOT_CHANGED={0}: Ej ändrad
#XMSG: <table name which was validated>: changed
CHANGED={0}: Ändrad
#XMSG: No changes detected
NO_CHANGES_DETECTED=Inga ändringar har registrerats
#XMSG Updating repository
UPDATING_REPOSITORY=Uppdaterar repository
#XMSG Repository updated
REPOSITORY_UPDATED=Repository har uppdaterats
#XMSG Remote Tables validated
REMOTE_TABLES_VALIDATED=Fjärrtabeller har validerats
#XMSG Reading remote tables from repository
READING_REMOTE_TABLES_FROM_REPOSITORY=Läser fjärrtabeller från repository
#XMSG {} remote tables read from repository
REMOTE_TABLES_READ_FROM_REPOSITORY={0} fjärrtabeller har lästs från repository
#XMSG Reading {} from remote source
READING_FROM_REMOTE_SOURCE=Läser {0} från fjärrkälla
#XMSG {} read from remote source
READ_FROM_REMOTE_SOURCE={0} har lästs från fjärrkälla
#XMSG Remote table {} not found in remote source
REMOTE_TABLE_NOT_FOUND_IN_REMOTE_SOURCE=Fjärrtabell {0} hittades ej i fjärrkälla
#XMSG Error during execution
ERROR_DURING_EXECUTION=Fel vid utförande: {0}

#XMSG: error message when none of the selected tables have passed validation to remove replication.
massRemoveRepAllSelectionError=Valda tabeller är inte valbara för massreplikeringsborttagande på grund av fel eller varningar. \nVälj en enskild tabell och försök ta bort replikering.
#XBUT: Select Columns Button
selectColumnsBtn=Välj kolumner
#XFLD: Refresh tooltip
TEXT_REFRESH=Uppdatera
#XFLD: Select Columns tooltip
text_selectColumns=Välj kolumner
#MSG: Error message when source system is disconnected/agent is unreachable
startDataReplication=Datareplikering kan inte startas.
#MSG: Error message when source system is disconnected/agent is unreachable
removeReplicatedData=Replikerade data kan inte tas bort.
#MSG: Error message when source system is disconnected/agent is unreachable
enableRealTimeReplication=Realtidsdatareplikering kan inte aktiveras.
#MSG: Error message when source system is disconnected/agent is unreachable
disableRealTimeReplication=Realtidsdatareplikering kan inte inaktiveras.
#MSG: Before starting replication of data, recommend to disable real-time replication or reconnect the agent
disableRealTimeReconnectAgentDataReplication=Inaktivera realtidsdatareplikering eller återanslut datahämtningsagenten för att starta datareplikering.
#MSG: Before starting replication of data, recommend to disable real-time replication or reconnect the source system
disableRealTimeReconnectSourceDataReplication=Inaktivera realtidsdatareplikering eller återanslut källsystemet för att starta datareplikering.
#MSG: Before starting replication of data, recommend to reconnect the agent
reconnectAgentStartReplication=Återanslut datahämtningsagenten för att starta datareplikering.
#MSG: Before starting replication of data, we recommend reconnecting the source system.
reconnectSourceStartReplication=Återanslut källsystemet för att starta datareplikering.
#MSG: Before removing replicated data, recommend to reconnect the agent
reconnectAgentRemoveReplicatedData=Återanslut datahämtningsagenten för att ta bort replikerade data.
#MSG: Before removing replicated data, recommend to reconnect the source
reconnectSourceRemoveReplicatedData=Återanslut källsystemet för att ta bort replikerade data.
#MSG: Before enabling Real-Time replication, recommend to reconnect the agent
reconnectAgentEnableRealTimeReplication=Återanslut datahämtningsagenten för att aktivera realtidsdatareplikering.
#MSG: Before enabling Real-Time replication, recommend to reconnect the source
reconnectSourceEnableRealTimeReplication=Återanslut källsystemet för att aktivera realtidsdatareplikering.
#XMSG: error message while mass stopping the replication
massDisableAllSelectionError=Realtidsdatareplikering kan inte inaktiveras för valda tabeller.<br><br>Mer hjälp hittar du <a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/82380e6f01c84fac898a285ac40a1e50.html?locale=en-US" >här</a>.
#XMSG: error message while mass stopping the replication
massDisableConfirmation=Valda objekt använder realtidsdatareplikering.<br><br>Objekten har genererat triggrar och en skuggtabell för att fånga upp deltaändringar i din fjärrdatabas. Dessa objekt måste raderas manuellt. Se protokollet för varje fjärrtabell separat för att se detaljerade instruktioner. <br><br> Vill du inaktivera?
#XMSG: error message while mass stopping the replication
stopMassDisableError=Ett fel inträffade vid inaktivering av realtidsdatareplikering.
#XMSG: success message for stopping replication
stopMassDisableSuccess=Vi inaktiverar realtidsdatareplikering för valda tabeller.
#XMSG: error message while mass stopping the replication
massDisablePartialRemoveWarnText=Valda objekt använder realtidsdatareplikering.<br><br>Några av objekten har genererat triggrar och en skuggtabell för att fånga upp deltaändringar i din fjärrdatabas. Dessa objekt måste raderas manuellt. Se protokollet för varje fjärrtabell separat för att se detaljerade instruktioner. <br><br> Vill du inaktivera?
#XMSG: error message while mass stopping the replication
massDisableFewTablesConfirmation=Realtidsdatareplikering kan endast inaktiveras för {0} av de {1} valda objekten.<br><br>Du hittar mer hjälp <a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/82380e6f01c84fac898a285ac40a1e50.html?locale=en-US" >här</a>.<br><br>Vill du inaktivera?
#XMSG: error message while mass stopping the replication
massDisableWithWarningFewTablesConfirmation=Realtidsdatareplikering kan endast inaktiveras för {0} av de {1} valda objekten.<br><br>Mer hjälp hittar du <a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/82380e6f01c84fac898a285ac40a1e50.html?locale=en-US" >här</a>.<br><br>Några av objekten har genererat triggrar och en skuggtabell för att fånga upp deltaändringar i din fjärrdatabas. Dessa objekt måste raderas manuellt. Se protokollet för varje fjärrtabell separat för att se detaljerade instruktioner.<br><br>Vill du inaktivera?
#XLBL Disable button text
disable=Inaktivera
#XSM Disable confirmation
massDisableAllWithoutWarning=Vill du inaktivera realtidsdatareplikering för alla valda tabeller?

#MSG: Error message when source system is disconnected/agent is unreachable
BATCH_REPLICATION_NOT_SUPPORTED_AGENT_DISCONNECTED=Datareplikering kan inte startas eftersom datahämtningsagenten är otillgänglig. Återanslut datahämtningsagenten och försök igen.
#MSG: Error message when source system is disconnected/agent is unreachable
BATCH_REPLICATION_NOT_SUPPORTED_AGENT_DISCONNECTED_FROM_REALTIME=Datareplikering kan inte startas eftersom datahämtningsagenten är otillgänglig. Inaktivera realtidsdatareplikering eller återanslut datahämtningsagenten och försök igen.
#MSG: Error message when source system is disconnected/agent is unreachable
REALTIME_REPLICATION_NOT_SUPPORTED_AGENT_DISCONNECTED=Realtidsdatareplikering kan inte aktiveras eftersom datahämtningsagenten är otillgänglig. Återanslut datahämtningsagenten och försök igen.
#MSG: Error message when source system is disconnected/agent is unreachable
REMOVE_REPLICATED_DATA_NOT_SUPPORTED_AGENT_DISCONNECTED=Replikerade data kan inte tas bort eftersom datahämtningsagenten är otillgänglig. Återanslut datahämtningsagenten och försök igen.
#MSG: Error message when source system is disconnected/agent is unreachable
BATCH_REPLICATION_NOT_SUPPORTED_SOURCE_UNREACHABLE=Datareplikering kan inte startas eftersom källsystemet är otillgängligt. Återanslut källsystemet och försök igen.
#MSG: Error message when source system is disconnected/agent is unreachable
BATCH_REPLICATION_NOT_SUPPORTED_SOURCE_UNREACHABLE_FROM_REALTIME=Datareplikering kan inte startas eftersom källsystemet är otillgängligt. Inaktivera realtidsdatareplikering eller återanslut källsystemet och försök igen.
#MSG: Error message when source system is disconnected/agent is unreachable
REALTIME_REPLICATION_NOT_SUPPORTED_SOURCE_UNREACHABLE=Realtidsdatareplikering kan inte aktiveras eftersom källsystemet är otillgängligt. Återanslut källsystemet och försök igen.
#MSG: Error message when source system is disconnected/agent is unreachable
REMOVE_REPLICATED_DATA_NOT_SUPPORTED_SOURCE_UNREACHABLE=Replikerade data kan inte tas bort eftersom källsystemet är otillgängligt. Återanslut källsystemet och försök igen.
