/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { State } from "@sap/dwc-circuit-breaker";
import { ShellNavigationService } from "@sap/orca-shell";
// import { getDataAccessText, getSemanticColorValue, getTextRefreshFrequency, getTextRepStatusRealTime, getTextRepStatusRealTimeTooltip, RemoteTableMonitorFormatter } from "../utility/RemoteTableMonitorFormatter";
import * as Shared from "../../../../shared/remoteTables/types";
import { ReplicationStatus } from "../../../../shared/remoteTables/types";
import { isDiMonitorImprovementsEnabled } from "../../abstractbuilder/commonservices/FeatureFlagCheck";
import {
  BaseController,
  BaseControllerClass,
  smartExtend,
} from "../../basecomponent/controller/BaseController.controller";
import { DataIntegrationComponentClass } from "../../dataintegration/Component";
import { TablePersonalizationClass } from "../../dataintegration/utility/TablePersonalization.controller";
import { MessageHandler } from "../../reuse/utility/MessageHandler";
import { ContentType, HttpMethod, IAjaxPayload, ServiceCall } from "../../reuse/utility/ServiceCall";
import { UI5Require } from "../../reuse/utility/UIHelper";
import { isCircuitBreakerYellowStateEnabled } from "../../shell/featureState/CircuitBreakerYellowStateFeatureState";
import { ShellContainer } from "../../shell/utility/Container";
import { Repo } from "../../shell/utility/Repo";
import { User } from "../../shell/utility/User";
import { setHelpScreenId } from "../../shell/utility/WebAssistantHelper";
import { Activity, ApplicationId } from "../../tasklog/utility/Constants";
import {
  ITTaskScheduleController,
  ITaskScheduleMassRequest,
  ITaskScheduleRequest,
  MassOps,
} from "../../taskscheduler/controller/TaskSchedule.controller";
import { getTaskScheduer, openSchedulePopover, recordAction } from "../../taskscheduler/utility/ScheduleUtil";
import { PreferenceChanges } from "../../userSettings/utility/Constants";
import { RemoteTableMonitorFormatter } from "../utility/RemoteTableMonitorFormatter";
import { RemoteTableServiceUtil } from "../utility/RemoteTableServiceUtil";

export class RemoteTableClass extends BaseControllerClass {
  private router: sap.m.routing.Router;
  private spaceId: string;
  private previousSpaceId: string;
  private view: sap.ui.core.mvc.View;
  private sTableHeader: string;
  private oGlobalFilter: sap.ui.model.Filter;
  private tableSearchValue: string = "";
  private oTable: sap.ui.table.Table;
  private oSortOrder: sap.ui.table.SortOrder;
  private selectedTable: string;
  // private schedulesList: any;
  dataIntegrationComponent: DataIntegrationComponentClass;
  private replicationSettingModel: sap.ui.model.json.JSONModel;
  private realTimeReplStatusPopover: any;
  realTimeReplStausModel: sap.ui.model.json.JSONModel;
  private formatter = RemoteTableMonitorFormatter;
  tablePersoController: sap.ui.table.TablePersoController;
  partitionDialog: any;
  private filtersList: {};
  private sortObject: any;
  private selectedRowIndex: any;
  displayName: string;
  user: User;
  selectedRowsSnapshot = [];
  selectedRowMap = {};
  isReusableTaskScheduleFFEnabled: boolean;

  public onInit(): void {
    require("../css/style.css");
    super.onInit();
    this.view = this.getView();

    this.getView().setModel(sap.ui.getCore().getModel("featureflags"), "featureflags");

    const i18nModel = new sap.ui.model.resource.ResourceModel({
      bundleName: require("../i18n/i18n.properties"),
    });
    const i18nModelTask = new sap.ui.model.resource.ResourceModel({
      bundleName: require("../../taskscheduler/i18n/i18n.properties"),
    });

    const isSDPEnabled = sap.ui.getCore().getModel("featureflags").getProperty("/DWC_DUMMY_SPACE_PERMISSIONS");
    this.isReusableTaskScheduleFFEnabled = sap.ui
      .getCore()
      .getModel("featureflags")
      .getProperty("/DWCO_REUSABLE_TASK_SCHEDULING_UI");
    const oTable = this.view.byId("idRemoteTablesTable");
    if (isDiMonitorImprovementsEnabled()) {
      oTable["getColumns"]()[5].destroy();
      oTable["getColumns"]()[6].destroy();
      oTable["getColumns"]()[6].destroy();
    } else {
      oTable["getColumns"]()[4].destroy();
      oTable["getColumns"]()[11].destroy();
      oTable["getColumns"]()[11].destroy();
    }

    this.view.setModel(i18nModel, "i18n");
    this.view.setModel(i18nModelTask, "i18n_task");

    // default model which contains data regarding remote table infos and performance
    this.view.setModel(new sap.ui.model.json.JSONModel({}));

    // TODO: Remove privRemoteConnModel & privSpaceFile with FF DWC_DUMMY_SPACE_PERMISSIONS removal user story
    this.view.setModel(new sap.ui.model.json.JSONModel({}), "privRemoteConnModel");
    this.view.setModel(new sap.ui.model.json.JSONModel({}), "privSpaceFile");

    this.realTimeReplStausModel = new sap.ui.model.json.JSONModel();
    this.view.setModel(this.realTimeReplStausModel, "realTimeReplStausModel");

    this.replicationSettingModel = new sap.ui.model.json.JSONModel();
    this.view.setModel(this.replicationSettingModel, "replicationModel");

    this.view.setModel(new sap.ui.model.json.JSONModel({}), "bwModel");
    this.view.setModel(new sap.ui.model.json.JSONModel({}), "refreshFreqDialogModel");
    (this.view.getModel("bwModel") as sap.ui.model.json.JSONModel).setProperty("/spaceType", "");
    (this.view.getModel("bwModel") as sap.ui.model.json.JSONModel).setProperty("/bw4CockpitURL", "");
    (this.view.getModel("bwModel") as sap.ui.model.json.JSONModel).setProperty("/bw4URLAvailable", false);
    this.dataIntegrationComponent = this.getOwnerComponent() as DataIntegrationComponentClass;
    this.getView().setModel(
      new sap.ui.model.json.JSONModel({
        status: [],
      }),
      "statusModel"
    );

    this.router = sap.ui.core.UIComponent.getRouterFor(this) as sap.m.routing.Router;
    sap.ui
      .getCore()
      .getEventBus()
      .subscribe("userPreferences", "change", (_, __, preferenceChanges: PreferenceChanges) => {
        if (!preferenceChanges.changes.DWC_OBJECT_NAME_DISPLAY) {
          return;
        }
        const objectNameDisplay = this.displayName;
        if (objectNameDisplay !== preferenceChanges.changes.DWC_OBJECT_NAME_DISPLAY.newValue) {
          this.displayName = preferenceChanges.changes.DWC_OBJECT_NAME_DISPLAY.newValue;
          this.onRefreshTable();
        }
      });

    this.dataIntegrationComponent.attachEvent("spaceChanged", async (event: sap.ui.base.Event) => {
      if ((event.getSource() as any).getActiveRouteName() === "remoteTableMonitor") {
        setHelpScreenId("remoteTableMonitor");
        this.previousSpaceId = this.spaceId;
        this.spaceId = event.getParameters().spaceId;

        /** Below model properties are set for scheduling **/
        (this.view.getModel("replicationModel") as sap.ui.model.json.JSONModel).setProperty("/newSchedule", false);
        (this.view.getModel("replicationModel") as sap.ui.model.json.JSONModel).setProperty("/editSchedule", false);
        (this.view.getModel("replicationModel") as sap.ui.model.json.JSONModel).setProperty("/deleteSchedule", false);

        /** Below model properties are set for remote table replication **/
        (this.view.getModel("replicationModel") as sap.ui.model.json.JSONModel).setProperty("/loadNewSnapshot", false);
        (this.view.getModel("replicationModel") as sap.ui.model.json.JSONModel).setProperty(
          "/removeReplicatedData",
          false
        );
        (this.view.getModel("replicationModel") as sap.ui.model.json.JSONModel).setProperty(
          "/enableRealTimeAccess",
          false
        );

        if (!isSDPEnabled) {
          /** Below model properties are set for remote table action privilege **/
          (this.view.getModel("privRemoteConnModel") as sap.ui.model.json.JSONModel).setProperty(
            "/read",
            ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_DATAINTEGRATION").read
          );
          (this.view.getModel("privRemoteConnModel") as sap.ui.model.json.JSONModel).setProperty(
            "/update",
            ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_DATAINTEGRATION").update
          );
          (this.view.getModel("privRemoteConnModel") as sap.ui.model.json.JSONModel).setProperty(
            "/execute",
            ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_DATAINTEGRATION").execute
          );
          (this.view.getModel("privRemoteConnModel") as sap.ui.model.json.JSONModel).setProperty(
            "/remoteConnectionRead",
            ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_REMOTECONNECTION").read
          );
          /** Privileges for SpaceFile */
          const spacePriv = ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_SPACEFILE");
          (this.view.getModel("privSpaceFile") as sap.ui.model.json.JSONModel).setProperty("/create", spacePriv.create);
          (this.view.getModel("privSpaceFile") as sap.ui.model.json.JSONModel).setProperty("/update", spacePriv.update);
          (this.view.getModel("privSpaceFile") as sap.ui.model.json.JSONModel).setProperty("/delete", spacePriv.delete);
        }

        (this.view.getModel("bwModel") as sap.ui.model.json.JSONModel).setProperty("/bw4URLAvailable", false);

        this.view.setBusyIndicatorDelay(0);
        this.view.setBusy(true);
        this.user = await User.getInstance();
        this.displayName = this.user.getObjectNameDisplay();
        this["resourceBundle"] = (
          this.view.getModel("i18n") as sap.ui.model.resource.ResourceModel
        ).getResourceBundle();
        const oTable = this.getView().byId("idRemoteTablesTable");
        if (isDiMonitorImprovementsEnabled()) {
          if (this.displayName === "businessName") {
            this.view.byId("connectionColumn")["setLabel"](this["resourceBundle"].getText("txtConnectionLabel"));
            this.view.byId("tableNameColumn")["setLabel"](this["resourceBundle"].getText("txtTableLabelNew"));
          } else {
            this.view.byId("connectionColumn")["setLabel"](this["resourceBundle"].getText("txtConnectionLabelTech"));
            this.view.byId("tableNameColumn")["setLabel"](this["resourceBundle"].getText("txtTableLabelTechNew"));
          }
        } else {
          if (this.displayName === "businessName") {
            this.view.byId("connectionColumn")["setLabel"](this["resourceBundle"].getText("txtConnectionLabel"));
            this.view.byId("tableNameColumn")["setLabel"](this["resourceBundle"].getText("txtTableLabel"));
          } else {
            this.view.byId("connectionColumn")["setLabel"](this["resourceBundle"].getText("txtConnectionLabelTech"));
            this.view.byId("tableNameColumn")["setLabel"](this["resourceBundle"].getText("txtTableLabelTech"));
          }
        }

        if (this.spaceId) {
          const circuitBreakerModel = sap.ui.getCore().getModel("circuitbreaker");
          const hanaState = circuitBreakerModel?.getProperty("/DataHANA");
          const isHanaDown: boolean =
            hanaState === State.Red || (hanaState === State.Yellow && !isCircuitBreakerYellowStateEnabled());
          const dataHANAProvisioningState = circuitBreakerModel?.getProperty("/DataHANAProvisioningState");
          const isHanaUpgradeInProgress: boolean =
            dataHANAProvisioningState === State.Red ||
            (dataHANAProvisioningState === State.Yellow && !isCircuitBreakerYellowStateEnabled());
          if (isHanaDown || isHanaUpgradeInProgress) {
            const sTableHeader = (this.view.getModel("i18n") as sap.ui.model.resource.ResourceModel)
              .getResourceBundle()
              .getText("txtTableCount", ["0"]);
            this.view.getModel().setProperty("/tableHeader", sTableHeader);
            this.view.getModel().setProperty("/totalDiskSize", "--");
            this.view.getModel().setProperty("/totalMemorySize", "--");
            this.view.getModel().setProperty("/", []);
            this.view.setBusy(false);
          } else {
            // pattern: /monitor/:spaceId/remoteTables
            let scheduleDialog;
            if (this.isReusableTaskScheduleFFEnabled) {
              await this.initScheduleDialog();
              scheduleDialog = this["newScheduleDialog"];
            } else {
              scheduleDialog = (
                this.getView().byId("scheduleDialog") as sap.ui.core.mvc.View
              ).getController() as ITTaskScheduleController;
            }
            const scheduleList = await scheduleDialog.getTaskScheduleList(this.spaceId, ApplicationId.REMOTE_TABLES);
            let sUrl = "monitor/" + this.spaceId + "/remoteTables";
            if (this.displayName === "businessName") {
              sUrl = "monitor/" + this.spaceId + "/remoteTables?includeBusinessNames=true";
            }
            (this.view.getModel("replicationModel") as sap.ui.model.json.JSONModel).setProperty(
              "/scheduleList",
              scheduleList
            );
            ServiceCall.request<any>({
              url: sUrl,
              type: HttpMethod.GET,
              contentType: ContentType.APPLICATION_JSON,
            })
              .then(async (oResponse) => {
                // data received
                // => put into model
                this.view.setBusy(false);
                await this.processResponse(oResponse);
                this.sTableHeader = this.getText("txtTableCount", [oResponse.data.tables.length.toString()]);

                this.oTable = this.byId("idRemoteTablesTable") as sap.ui.table.Table;
                this.oSortOrder = sap.ui.table.SortOrder.Ascending;
                const oTotal = this.calculateTotalDiskSize(oResponse.data.tables);
                // sort by column tableName
                // this.oTable.sort(this.oTable.getColumns()[1], this.oSortOrder, false);
                (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/", oResponse.data);
                (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/tableHeader", this.sTableHeader);
                (this.getView().getModel() as sap.ui.model.json.JSONModel).setProperty(
                  "/totalDiskSize",
                  oTotal.totalDiskSize.toFixed(2)
                );
                (this.getView().getModel() as sap.ui.model.json.JSONModel).setProperty(
                  "/totalMemorySize",
                  oTotal.totalMemorySize.toFixed(2)
                );
                this.loadSpaceInfo();
                if (this.tableSearchValue && this.tableSearchValue.length > 0) {
                  this.getFilters(this.tableSearchValue);
                }
                if (this.filtersList) {
                  const table = this.oTable;
                  const filterList = this.filtersList;
                  Object.keys(this.filtersList).forEach(function (column) {
                    const col = table.getColumns().filter((e) => e.hasOwnProperty("sId") && e.getId() === column);
                    table.filter(col[0], filterList[column]);
                  });
                }

                if (this.sortObject) {
                  this.oTable.sort(this.sortObject?.column, this.sortObject?.sortOrder, false);
                } else {
                  this.oTable.sort(this.oTable.getColumns()[1], this.oSortOrder, false);
                }

                this.oTable.sort(
                  this.getView().byId("replicationStatusColumnNew") as sap.ui.table.Column,
                  sap.ui.table.SortOrder.Descending,
                  false
                );
                this.clearSelection();
              })
              .catch((oResponse) => {
                // send a generic error message to the UI
                this.view.setBusy(false);
                if (oResponse && oResponse[0]) {
                  if (oResponse[0].status === 401) {
                    // Navigate to dataintegration
                    ShellNavigationService.toExternal({
                      target: {
                        semanticObject: "dataintegration",
                      },
                    });
                    setTimeout(() => {
                      MessageHandler.error(
                        (this.view.getModel("i18n") as sap.ui.model.resource.ResourceModel)
                          .getResourceBundle()
                          .getText("illegalNavigation", this.spaceId as any)
                      );
                    }, 500);
                  } else {
                    MessageHandler.exception({
                      exception: oResponse,
                      message: (this.view.getModel("i18n") as sap.ui.model.resource.ResourceModel)
                        .getResourceBundle()
                        .getText("txtReadBackendError"),
                    });
                    // log a more specific error message in Kibana with more details
                    this.logError(oResponse[0].status + oResponse[0].responseText);
                  }
                }
              });
          }
        }

        if (this.previousSpaceId !== this.spaceId && this.previousSpaceId !== undefined) {
          this.clearSearchField();
          this.clearFilter(this.oTable);
        }
      }
    });

    this.registerForColumnSettings();
    // create personalization controller
    UI5Require<typeof sap.ui.table.TablePersoController>("sap/ui/table/TablePersoController").then((TPC) => {
      this.tablePersoController = new TPC("tablePersonalizationDialogId", {
        table: this.getTable(),
        persoService: new TablePersonalizationClass("TPC"),
      });
    });

    // create Statement Editor
    UI5Require<typeof sap.ui.codeeditor.CodeEditor>("sap/ui/codeeditor/CodeEditor").then((Ce) => {
      this["codeEditor"] = new Ce("codeEditorIdRM", {
        type: "sql",
        maxLines: 15,
        height: "auto",
        lineNumbers: false,
        editable: false,
      } as any); // Sorry, the reuse sap.ui.codeeditor.CodeEditor doesn't have the correct constructor yet....
    });
  }

  protected registerForColumnSettings() {
    sap.ui.getCore().getEventBus().subscribe("personalizationsDone", "change", this.onAfterTablePersonalization, this);
  }

  public onAfterTablePersonalization() {
    const selectedRows = this.getSelectedTables();
    if (selectedRows.length > 0) {
      this.clearSelection();
    }
  }

  async processResponse(oResponse: IAjaxPayload<any>) {
    // add dataAccess to UI model
    let scheduleDialog;
    if (this.isReusableTaskScheduleFFEnabled) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
    } else {
      scheduleDialog = (
        this.getView().byId("scheduleDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    const scheduleList = await scheduleDialog.getTaskScheduleList(this.spaceId, ApplicationId.REMOTE_TABLES);
    (this.view.getModel("replicationModel") as sap.ui.model.json.JSONModel).setProperty("/scheduleList", scheduleList);
    oResponse?.data?.tables?.forEach((item) => {
      let schedule;
      schedule = scheduleList?.find((val) => val.objectId === item.tableName);
      if (!!schedule) {
        item.scheduled = true;
        item.nextSchedule = schedule?.nextRun;
      }
      item.displayName = item.tableName;
      item.connectionName =
        item.connectionName && item.connectionName !== "" ? item.connectionName : item.remoteSourceName;
      if (this.displayName === "businessName") {
        item.displayName = item.businessName && item.businessName !== "" ? item.businessName : item.tableName;
        item.displayConnectionName =
          item.businessNameConnection && item.businessNameConnection !== ""
            ? item.businessNameConnection
            : item.connectionName;
      } else {
        item.displayConnectionName = item.connectionName;
      }
      item.formattedlatestUpdate = item.latestUpdate ? this.formatDateTime(item.latestUpdate) : "";
      item.formattedLatestChange = item.latestChangeSource ? this.formatDateTime(item.latestChangeSource) : "";
      const bundleName = require("../i18n/i18n.properties");
      const i18nModel = new sap.ui.model.resource.ResourceModel({ bundleName });
      const oBundle = i18nModel.getResourceBundle();
      if (schedule !== undefined && schedule.activationStatus === "DISABLED" && item.scheduled === true) {
        item.isPaused = true;
      } else if (schedule !== undefined && schedule.activationStatus === "ENABLED" && item.scheduled === true) {
        item.isPaused = false;
      }
      if (item.isPaused) {
        item.formattedRefreshFrequency = oBundle.getText("paused");
      } else {
        item.formattedRefreshFrequency = this.formatter.getTextRefreshFrequency(
          item.replicationStatus,
          item.replicationType,
          item.scheduled
        );
      }
      item.formattedNextSchedule = this.formatter.formatNextSchedule(
        item.nextSchedule,
        item.scheduled,
        item.tableName,
        scheduleList
      );
      item.formattedReplicationStatus = this.formatter.getTextRepStatusRealTime(
        item.replicationStatus,
        item.replicationType,
        item.taskState
      );

      item.sortedReplicationStatus = this.formatter.getSortedTextRepStatus(
        item.replicationStatus,
        item.replicationType,
        item.taskState
      );

      item.formattedinMemorySizeReplicaTableMB = [undefined, null].includes(item.inMemorySizeReplicaTableMB)
        ? (this.view.getModel("i18n") as sap.ui.model.resource.ResourceModel)
            .getResourceBundle()
            .getText("NOT_APPLICABLE")
        : this.formatNumber(item.inMemorySizeReplicaTableMB);
      item.formatteddiskSizeReplicaTableInMB = this.formatNumber(item.diskSizeReplicaTableInMB);
      item.formattedDataAccess = this.formatter.getDataAccessText(item.dataAccess);
      item.formattedNumberOfRecords = this.formatNumber(item.numberOfRecords);
      item.nextRunforSort =
        item.formattedNextSchedule === this.getText("txtExpired")
          ? Date.parse("Thu Jan 01 1000 00:00:00 GMT+0530 (India Standard Time)")
          : !!item.nextSchedule
          ? Date.parse(item.nextSchedule)
          : Date.parse("Thu Jan 01 999 00:00:00 GMT+0530 (India Standard Time)");

      item.scheduleOwner = "";
      if (schedule) {
        item.scheduleOwner = schedule?.owner;
      }
    });
  }

  public onAfterRendering(): void {
    this.dataIntegrationComponent.registerHanaStateChangeHandler(
      () => {
        this.onRefreshTable();
      },
      () => {
        this.onRefreshTable();
      }
    );
  }

  public fireOnFilterColumn(event): void {
    const searchText = this.getView().byId("searchTablesInput");
    searchText["setValue"]("");
    if (!this.filtersList) {
      this.filtersList = {};
    }
    const filterColumn = event?.getParameter("column")?.getId();
    const filterValue = event?.getParameter("value");
    this.filtersList[filterColumn] = filterValue;
    setTimeout(() => {
      const oElement: any = this.view.byId("idRemoteTablesTable");
      const oBinding: any = oElement.getBinding("rows");
      const RecordLength = oBinding.getLength().toString();
      this.sTableHeader = this.view.getModel("i18n").getResourceBundle().getText("txtTableCount", [RecordLength]);
      (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/tableHeader", this.sTableHeader);
      this.restoreSelectedRows();
    });
  }

  public fireOnSort(event) {
    if (!this.sortObject) {
      this.sortObject = {};
    }
    this.sortObject.column = event?.getParameter("column");
    this.sortObject.sortOrder = event?.getParameter("sortOrder");
    setTimeout(() => {
      this.restoreSelectedRows();
    });
  }

  private clearSearchField() {
    const searchField = this.view.byId("searchTablesInput") as sap.m.SearchField;
    searchField["clear"]();
  }

  private clearFilter(oTable) {
    const aColumns = oTable.getColumns();
    for (var i = 0; i < aColumns.length; i++) {
      oTable.filter(aColumns[i], null);
    }
  }

  captureSelectedRowsSnapshot(oEvent) {
    const oTable = this.byId("idRemoteTablesTable") as sap.ui.table.Table;
    let selectedIndices = oTable.getSelectedIndices();
    if (selectedIndices.length === 0 && oEvent.getParameter("rowIndex") !== -1) {
      selectedIndices = oEvent.getParameter("rowIndices");
    }
    this.selectedRowMap = {};
    if (selectedIndices.length > 0) {
      for (const i of selectedIndices) {
        const path = oTable.getContextByIndex(i)?.getPath();
        const selectedTable = this.view.getModel().getProperty(path);
        // update selectedrowmap
        // check if key exists
        if (!this.selectedRowMap[i]) {
          this.selectedRowMap[i] = selectedTable;
        }
      }
    }
  }

  // captureSelectedRowsSnapshot(oEvent: sap.ui.base.Event) {
  //   const oTable = this.byId("idRemoteTablesTable") as sap.ui.table.Table;
  //   const isSelectAll = oEvent.getParameter("selectAll");
  //   const rowIndex = oEvent.getParameter("rowIndex");
  //   const rowIndices = oEvent.getParameter("rowIndices");
  //   const userInteraction = oEvent.getParameter("userInteraction");
  //   if (isSelectAll) {
  //     for (const i of rowIndices) {
  //       const path = oTable.getContextByIndex(i)?.getPath();
  //       const selectedTable = this.view.getModel().getProperty(path);
  //       this.selectedRowsSnapshot.push(selectedTable);
  //     }
  //   } else if (rowIndex === -1 && rowIndices.length > 0 && userInteraction) {
  //     // deselect all clicked
  //     for (const i of rowIndices) {
  //       const path = oTable.getContextByIndex(i)?.getPath();
  //       const selectedTable = this.view.getModel().getProperty(path);
  //       const existingTableIndex = this.selectedRowsSnapshot.findIndex(
  //         (table) => table.tableName === selectedTable.tableName
  //       );
  //       if (existingTableIndex > -1) {
  //         this.selectedRowsSnapshot.splice(existingTableIndex, 1);
  //       }
  //     }
  //   } else if (userInteraction) {
  //     const selectedPath = oEvent.getParameter("rowContext")?.getPath();
  //     if (!!selectedPath) {
  //       const selectedTable = this.view.getModel().getProperty(selectedPath);
  //       const existingTableIndex = this.selectedRowsSnapshot.findIndex(
  //         (table) => table.tableName === selectedTable.tableName
  //       );
  //       if (existingTableIndex > -1) {
  //         this.selectedRowsSnapshot.splice(existingTableIndex, 1);
  //       } else {
  //         this.selectedRowsSnapshot.push(selectedTable);
  //       }
  //     }
  //   }
  // }

  restoreSelectedRows() {
    const oTable: any = this.byId("idRemoteTablesTable") as sap.ui.table.Table;
    const oBinding: any = oTable.getBinding("rows");
    const filteredIndices = oBinding.aIndices;
    const tablesList = this.getView().getModel().getData();
    const selectedTables = this.selectedRowsSnapshot.map((table) => table.tableName);
    filteredIndices.forEach((filteredIndex, index) => {
      const tableName = tablesList.tables[filteredIndex]?.tableName;
      if (selectedTables.includes(tableName)) {
        oTable.addSelectionInterval(index, index);
      }
    });
  }

  private loadSpaceInfo() {
    Repo.getSpaceDetails(this.spaceId, ["spaceType"]).then((spaceObj) => {
      (this.view.getModel("bwModel") as sap.ui.model.json.JSONModel).setProperty("/spaceType", spaceObj.spaceType);
      if (spaceObj.spaceType === "abapbridge") {
        const bwUrl = "monitor/" + this.spaceId + "/remotetables/bwbridgeurl";
        ServiceCall.request<any>({
          url: bwUrl,
          type: HttpMethod.GET,
          contentType: ContentType.APPLICATION_JSON,
        }).then((oRes) => {
          const cockpitURL = oRes.data.url + "/sap/bc/ui2/flp#Shell-home";
          (this.view.getModel("bwModel") as sap.ui.model.json.JSONModel).setProperty("/bw4CockpitURL", cockpitURL);
          (this.view.getModel("bwModel") as sap.ui.model.json.JSONModel).setProperty("/bw4URLAvailable", true);
        });
      }
    });
  }

  public LoadReplicationLogs(oEvent: IEvent<any, any>): void {
    const oItem = oEvent.getSource();
    const oBinding = oItem.getBindingContext();
    this.selectedRowIndex = oEvent?.getParameter("row")?.getIndex();
    const sName = oBinding?.getObject("tableName");
    if (oEvent.getSource()) {
      this.router.navTo("remoteTableTaskLog", {
        spaceId: this.spaceId,
        objectId: encodeURIComponent(sName),
      });
    }
  }

  public onCompleteRefresh() {
    this.onRefreshTable();
  }

  public async onRefreshTable(): Promise<void> {
    const oTable = this.byId("idRemoteTablesTable") as sap.ui.table.Table;
    const circuitBreakerModel = sap.ui.getCore().getModel("circuitbreaker");
    const hanaState = circuitBreakerModel?.getProperty("/DataHANA");
    const isHanaDown: boolean =
      hanaState === State.Red || (hanaState === State.Yellow && !isCircuitBreakerYellowStateEnabled());
    const dataHANAProvisioningState = circuitBreakerModel?.getProperty("/DataHANAProvisioningState");
    const isHanaUpgradeInProgress: boolean =
      dataHANAProvisioningState === State.Red ||
      (dataHANAProvisioningState === State.Yellow && !isCircuitBreakerYellowStateEnabled());
    let scheduleDialog;
    if (this.isReusableTaskScheduleFFEnabled) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
    } else {
      scheduleDialog = (
        this.getView().byId("scheduleDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    this.view.setBusyIndicatorDelay(0);
    this.view.setBusy(true);
    if (isHanaDown || isHanaUpgradeInProgress) {
      const sTableHeader = this.view.getModel("i18n").getResourceBundle().getText("txtTableCount", ["0"]);
      this.view.getModel().setProperty("/tableHeader", sTableHeader);
      this.view.getModel().setProperty("/totalDiskSize", "--");
      this.view.getModel().setProperty("/totalMemorySize", "--");
      this.view.getModel().setProperty("/", []);
      this.view.setBusy(false);
    } else {
      let sUrl = "monitor/" + this.spaceId + "/remoteTables";
      if (this.displayName === "businessName") {
        sUrl = "monitor/" + this.spaceId + "/remoteTables?includeBusinessNames=true";
      }

      // read data from backend into default model this.view.getModel()
      await scheduleDialog.refreshTaskScheduleList(this.spaceId, ApplicationId.REMOTE_TABLES);
      await this.loadData(sUrl);
      this.clearSelection();
      this.view.setBusy(false);
    }
    if (isDiMonitorImprovementsEnabled()) {
      if (this.displayName === "businessName") {
        this.view.byId("connectionColumn")["setLabel"](this["resourceBundle"].getText("txtConnectionLabel"));
        this.view.byId("tableNameColumn")["setLabel"](this["resourceBundle"].getText("txtTableLabelNew"));
      } else {
        this.view.byId("connectionColumn")["setLabel"](this["resourceBundle"].getText("txtConnectionLabelTech"));
        this.view.byId("tableNameColumn")["setLabel"](this["resourceBundle"].getText("txtTableLabelTechNew"));
      }
    } else {
      if (this.displayName === "businessName") {
        this.view.byId("connectionColumn")["setLabel"](this["resourceBundle"].getText("txtConnectionLabel"));
        this.view.byId("tableNameColumn")["setLabel"](this["resourceBundle"].getText("txtTableLabel"));
      } else {
        this.view.byId("connectionColumn")["setLabel"](this["resourceBundle"].getText("txtConnectionLabelTech"));
        this.view.byId("tableNameColumn")["setLabel"](this["resourceBundle"].getText("txtTableLabelTech"));
      }
    }
  }

  public async loadNewSnapshot(): Promise<void> {
    let selectedTable, sTable, displayName;
    selectedTable = this.getSelectedTables()[0];
    sTable = selectedTable.tableName;
    displayName = selectedTable.displayName;

    /**  clear line selection (as the order of the rows might have changed during the refresh()) */
    this.clearSelection();
    if (!this.formatter.sharedReplicaMenuCheck(selectedTable)) {
      RemoteTableServiceUtil.showSharedReplicaError();
    } else {
      RemoteTableServiceUtil.loadNewSnapshot(this.spaceId, sTable, this.view, displayName, selectedTable);
    }
  }

  public async removeReplicatedData(): Promise<void> {
    const selectedTables = this.getSelectedTables();
    if (selectedTables.length > 1) {
      this.view.setBusy(true);
      await RemoteTableServiceUtil.removeMassReplicatedData(this.spaceId, selectedTables, this.view);
      this.view.setBusy(false);
    } else {
      let remoteTable;
      remoteTable = this.getSelectedTables()[0];
      const sTable = remoteTable.tableName;
      const displayName = remoteTable.displayName;
      if (!this.formatter.sharedReplicaMenuCheck(remoteTable)) {
        RemoteTableServiceUtil.showSharedReplicaError();
      } else {
        RemoteTableServiceUtil.removeReplicatedData(this.spaceId, sTable, this.view, displayName, remoteTable);
      }
    }
  }

  public clearSelection() {
    const table = this.getView().byId("idRemoteTablesTable") as sap.ui.table.Table;

    this.getView().getModel("replicationModel").setProperty("/menuButtonsEnabled", false);

    this.selectedRowsSnapshot = [];
    table.clearSelection();
  }

  public async enableRealTimeReplication(): Promise<void> {
    let remoteTable;
    remoteTable = this.getSelectedTables()[0];
    const displayName = remoteTable.displayName;
    if (!this.formatter.sharedReplicaMenuCheck(remoteTable)) {
      RemoteTableServiceUtil.showSharedReplicaError();
    } else {
      const dataAccess = remoteTable.dataAccess;
      const usedInTaskChain = remoteTable.usedInTaskChain;
      RemoteTableServiceUtil.enableRealTimeReplication(
        this.spaceId,
        remoteTable,
        this.view,
        displayName,
        dataAccess,
        usedInTaskChain
      );
    }
  }

  public async disableRealTimeReplication(): Promise<void> {
    this.view.setBusy(true);

    const selectedTables = this.getSelectedTables();
    if (selectedTables.length > 1) {
      await RemoteTableServiceUtil.massDisableRealTimeReplication(this.spaceId, selectedTables, this.view);
    } else {
      let remoteTable;
      remoteTable = this.getSelectedTables()[0];
      const displayName = remoteTable.displayName;
      const isFvt = remoteTable.location === "indexserver";
      const warnSubscription = await RemoteTableServiceUtil.warnBeforeDropSubscription(remoteTable, this.view);
      await RemoteTableServiceUtil.disableRealTimeReplication(
        this.spaceId,
        remoteTable,
        this.view,
        displayName,
        isFvt,
        warnSubscription
      );
      this.view.setBusy(false);
    }
  }

  /**
   * Helper to set the busy indicator on this view
   * @param {boolean} isBusy - bool val to set or deactivate the busy ind.
   */
  public setBusy(isBusy: boolean, sTitle?: string, sText?: string) {
    const busyDialog = (sap.ui.getCore().byId("dataIntegrationBusyIndicator") ||
      new sap.m.BusyDialog("dataIntegrationBusyIndicator", {
        showCancelButton: false,
      })) as sap.m.BusyDialog;
    busyDialog.setTitle(sTitle || "Processing");
    busyDialog.setText(sText || "Loading");
    if (isBusy) {
      busyDialog.open();
    } else {
      busyDialog.close();
    }
  }

  public async openImpactLineageDialog() {
    const remoteTable = this.getSelectedTables()[0];
    RemoteTableServiceUtil.showImpactAnalysisDialog(remoteTable);
  }

  public onSearchField(oEvent: sap.ui.base.Event): void {
    this.tableSearchValue = oEvent.getParameters().newValue;
    const table = this.getView().byId("idRemoteTablesTable") as sap.ui.table.Table;
    const columns: any[] = table?.getColumns();
    columns?.forEach((col) => {
      col.setFilterValue("");
      col.setFiltered(false);
    });
    this.getFilters(this.tableSearchValue);
    if (this.tableSearchValue === "" && this.filtersList) {
      const filterList = this.filtersList;
      Object.keys(this.filtersList).forEach(function (column) {
        const col = table.getColumns().filter((e) => e.hasOwnProperty("sId") && e.getId() === column);
        table.filter(col[0], filterList[column]);
      });
    }
    this.restoreSelectedRows();
  }

  private getFilters(searchValue: string): void {
    const self = this;
    const oBinding = this.byId("idRemoteTablesTable").getBinding("rows") as sap.ui.model.json.JSONListBinding;
    this.oGlobalFilter = new sap.ui.model.Filter({
      filters: [],
      and: true,
    });
    if (searchValue && searchValue.length >= 1) {
      this.oGlobalFilter = new sap.ui.model.Filter({
        filters: [
          new sap.ui.model.Filter("connectionName", sap.ui.model.FilterOperator.Contains, searchValue),
          new sap.ui.model.Filter("displayName", sap.ui.model.FilterOperator.Contains, searchValue),
          new sap.ui.model.Filter("replicationType", sap.ui.model.FilterOperator.Contains, searchValue),
          new sap.ui.model.Filter("formattedReplicationStatus", sap.ui.model.FilterOperator.Contains, searchValue),
          new sap.ui.model.Filter("formattedDataAccess", sap.ui.model.FilterOperator.Contains, searchValue),
        ],
        and: false,
      });
    }

    oBinding.filter([this.oGlobalFilter], null);
    const filteredTableRecordLength = oBinding.getLength().toString();
    this.sTableHeader = (this.view.getModel("i18n") as sap.ui.model.resource.ResourceModel)
      .getResourceBundle()
      .getText("txtTableCount", [filteredTableRecordLength]);
    (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/tableHeader", this.sTableHeader);
  }

  // auxiliary method to be used instead of method loadData on default model so that ServiceCall API is used for http GET
  public async loadData<T>(sUrl: string): Promise<any> {
    return ServiceCall.request<any>({
      url: sUrl,
      type: HttpMethod.GET,
      contentType: ContentType.APPLICATION_JSON,
    })
      .then(async (response) => {
        this.view.setBusy(false);
        let oResponse;
        let filteredResults;
        await this.processResponse(response);
        filteredResults = response?.data?.tables?.filter((item) => item.federationOnly !== true);
        oResponse = {
          data: {
            tables: filteredResults,
          },
        };
        (this.view.getModel() as sap.ui.model.json.JSONModel).setData(oResponse.data); // setData automatically triggers a model refresh
        const oTotal = this.calculateTotalDiskSize(oResponse.data.tables);
        if (this.oGlobalFilter && this.oGlobalFilter["aFilters"] && this.oGlobalFilter["aFilters"].length > 0) {
          const oBinding = this.byId("idRemoteTablesTable").getBinding("rows") as sap.ui.model.json.JSONListBinding;
          const filteredTableRecordLength = oBinding.getLength().toString();
          this.sTableHeader = (this.view.getModel("i18n") as sap.ui.model.resource.ResourceModel)
            .getResourceBundle()
            .getText("txtTableCount", [filteredTableRecordLength]);
        } else {
          this.sTableHeader = (this.view.getModel("i18n") as sap.ui.model.resource.ResourceModel)
            .getResourceBundle()
            .getText("txtTableCount", [oResponse.data.tables.length]);
        }
        (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/tableHeader", this.sTableHeader);
        (this.getView().getModel() as sap.ui.model.json.JSONModel).setProperty(
          "/totalDiskSize",
          oTotal.totalDiskSize.toFixed(2)
        );
        (this.getView().getModel() as sap.ui.model.json.JSONModel).setProperty(
          "/totalMemorySize",
          oTotal.totalMemorySize.toFixed(2)
        );

        (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/", oResponse.data);
      })
      .catch((error) => {
        this.view.setBusy(false);
        MessageHandler.exception({
          exception: error,
          message: (this.view.getModel("i18n") as sap.ui.model.resource.ResourceModel)
            .getResourceBundle()
            .getText("txtReadBackendError"),
        });
        this.logError(error.message || error);
      });
  }

  async openSchedule(oEvent: any, tableName: string, dataAccess: any): Promise<void> {
    const link = oEvent.getSource();
    const applicationId = ApplicationId.REMOTE_TABLES;
    const data: ITaskScheduleRequest = {
      objectId: tableName,
      applicationId: ApplicationId.REMOTE_TABLES,
      activity: Activity.REPLICATE,
      description: "Remote Table Monitoring",
      activationStatus: "ENABLED",
      dataAccess: dataAccess,
    };
    const isSpaceLocked = this.getView().getModel("diPageModel")?.getProperty("/isSpaceLocked");
    if (this.isReusableTaskScheduleFFEnabled) {
      await this.initScheduleDialog();
      if (isDiMonitorImprovementsEnabled()) {
        await openSchedulePopover(
          link,
          tableName,
          applicationId,
          this.spaceId,
          this["newScheduleDialog"],
          data,
          this,
          this.onRefreshTable.bind(this),
          isSpaceLocked
        );
      } else {
        await openSchedulePopover(link, tableName, applicationId, this.spaceId, this["newScheduleDialog"]);
      }
    } else {
      const scheduleDialog = (
        this.getView().byId("scheduleDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
      scheduleDialog.openSchedulePopover(link, tableName, applicationId, this.spaceId);
    }
  }

  async initScheduleDialog() {
    if (!this["newScheduleDialog"]) {
      this["newScheduleDialog"] = await getTaskScheduer("remoteTableTaskScheduler");
    }
  }

  async onCreateScheduleReplication(): Promise<void> {
    const selectedTable = this.getSelectedTables()[0] as any;
    const dataAccess = selectedTable.dataAccess;
    if (!this.formatter.sharedReplicaMenuCheck(selectedTable)) {
      RemoteTableServiceUtil.showSharedReplicaError();
    } else {
      const data: ITaskScheduleRequest = {
        objectId: this.selectedTable,
        applicationId: ApplicationId.REMOTE_TABLES,
        activity: Activity.REPLICATE,
        description: "Remote Table Monitoring",
        activationStatus: "ENABLED",
        dataAccess: dataAccess,
      };
      let scheduleDialog;
      const remoteTable = this.getView().byId("idRemoteTablesTable") as sap.ui.core.mvc.View as any;
      const selectedIndex = remoteTable.getSelectedIndex();
      const oRessourceBundle = (
        this.view.getModel("i18n_task") as sap.ui.model.resource.ResourceModel
      ).getResourceBundle();
      this.view.setBusy(true);

      // add feature flag check
      if (this.isReusableTaskScheduleFFEnabled) {
        await this.initScheduleDialog();
        scheduleDialog = this["newScheduleDialog"];
        recordAction(`createTaskSchedule: ${data.applicationId}`, "taskSchedule", "onCreate");
      } else {
        scheduleDialog = (
          this.getView().byId("scheduleDialog") as sap.ui.core.mvc.View
        ).getController() as ITTaskScheduleController;
      }
      scheduleDialog.createTaskSchedule(
        data,
        this.spaceId,
        ApplicationId.REMOTE_TABLES,
        () => {
          if (this.isReusableTaskScheduleFFEnabled) {
            recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
          }
          const msg = oRessourceBundle.getText("createScheduleSuccess");
          this.onRefreshTable().then(() => {
            sap.m.MessageToast.show(msg);
          });
        },
        (err) => {
          if (this.isReusableTaskScheduleFFEnabled) {
            recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
            MessageHandler.exception({
              exception: err.error,
              message: err.message,
            });
          }
          this.view.setBusy(false);
        },
        () => {
          this.view.setBusy(false);
        }
      );
    }
  }

  async onChangeScheduleReplication(): Promise<void> {
    const selectedTable = this.getSelectedTables()[0] as any;
    const dataAccess = selectedTable.dataAccess;
    if (!this.formatter.sharedReplicaMenuCheck(selectedTable)) {
      RemoteTableServiceUtil.showSharedReplicaError();
    } else {
      const data: ITaskScheduleRequest = {
        objectId: this.selectedTable,
        applicationId: ApplicationId.REMOTE_TABLES,
        activity: Activity.REPLICATE,
        description: "Remote Table Monitoring",
        activationStatus: "ENABLED",
        dataAccess: dataAccess,
      };
      let scheduleDialog;
      const remoteTable = this.getView().byId("idRemoteTablesTable") as sap.ui.core.mvc.View as any;
      const selectedIndex = remoteTable.getSelectedIndex();
      const oRessourceBundle = (
        this.view.getModel("i18n_task") as sap.ui.model.resource.ResourceModel
      ).getResourceBundle();
      this.view.setBusy(true);
      // add feature flag check
      if (this.isReusableTaskScheduleFFEnabled) {
        await this.initScheduleDialog();
        scheduleDialog = this["newScheduleDialog"];
        recordAction(`editTaskSchedule: ${data.applicationId}`, "taskSchedule", "onEdit");
      } else {
        scheduleDialog = (
          this.getView().byId("scheduleDialog") as sap.ui.core.mvc.View
        ).getController() as ITTaskScheduleController;
      }

      scheduleDialog.changeTaskSchedule(
        data,
        this.spaceId,
        ApplicationId.REMOTE_TABLES,
        () => {
          if (this.isReusableTaskScheduleFFEnabled) {
            recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "edit");
          }
          const msg = oRessourceBundle.getText("updateScheduleSuccess");
          this.onRefreshTable().then(() => {
            sap.m.MessageToast.show(msg);
          });
        },
        (err) => {
          if (this.isReusableTaskScheduleFFEnabled) {
            recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "edit");
            MessageHandler.exception({
              exception: err.error,
              message: err.message,
            });
          }
          this.view.setBusy(false);
        },
        () => {
          this.view.setBusy(false);
        }
      );
    }
  }

  async onMassOperations(operation: MassOps) {
    const oTable = this.byId("idRemoteTablesTable") as sap.ui.table.Table;
    const oResourceBundle = this.view.getModel("i18n").getResourceBundle();
    let errorTxt, successTxt;
    let scheduleDialog;
    if (this.isReusableTaskScheduleFFEnabled) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
    } else {
      scheduleDialog = (
        this.getView().byId("scheduleDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    let operationMethod;
    const model = this.view.getModel("replicationModel");
    switch (operation) {
      case MassOps.DELETE:
        operationMethod = (...args) => {
          const count = model.getProperty("/deleteCount");
          errorTxt = oResourceBundle.getText("errorMassRemoveScheduleTxt");
          successTxt = oResourceBundle.getText("massDeleteStarted", [count]);
          // eslint-disable-next-line prefer-spread
          scheduleDialog.deleteMultiSchedules.apply(scheduleDialog, args);
        };
        break;
      case MassOps.CHANGE_OWNER:
        operationMethod = (...args) => {
          const count = model.getProperty("/changeOwnerCount");
          errorTxt = oResourceBundle.getText("errorMassScheduleOwnerChangeTxt");
          successTxt = oResourceBundle.getText("massAssignStarted", [count]);
          // eslint-disable-next-line prefer-spread
          scheduleDialog.changeOwnerMultiSchedules.apply(scheduleDialog, args);
        };
        break;
      case MassOps.PAUSE:
        operationMethod = (...args) => {
          const count = model.getProperty("/pauseCount");
          errorTxt = oResourceBundle.getText("errorMassPauseScheduleTxt");
          successTxt = oResourceBundle.getText("massPauseStarted", [count]);
          // eslint-disable-next-line prefer-spread
          scheduleDialog.pauseMultiSchedules.apply(scheduleDialog, args);
        };
        break;
      case MassOps.RESUME:
        operationMethod = (...args) => {
          const count = model.getProperty("/resumeCount");
          errorTxt = oResourceBundle.getText("errorMassResumeScheduleTxt");
          successTxt = oResourceBundle.getText("massResumeStarted", [count]);
          // eslint-disable-next-line prefer-spread
          scheduleDialog.resumeMultiSchedules.apply(scheduleDialog, args);
        };
        break;
    }
    if (operationMethod) {
      const selectedTables = this.getSelectedTables();
      const selectedTableNames = selectedTables.map((table: { tableName: string }) => table.tableName);
      const data: ITaskScheduleMassRequest = {
        objectIdList: selectedTableNames,
      };
      this.view.setBusy(true);
      operationMethod(
        data,
        this.spaceId,
        ApplicationId.REMOTE_TABLES,
        () => {
          sap.m.MessageToast.show(successTxt);
          this.onRefreshTable().then(() => {
            this.view.setBusy(false);
          });
        },
        (error) => {
          MessageHandler.exception({
            exception: error,
            message: errorTxt,
          });
          this.view.setBusy(false);
        },
        () => {
          this.view.setBusy(false);
        }
      );
    }
  }

  onMassDeleteOps() {
    this.onMassOperations(MassOps.DELETE);
  }

  onMassChangeOwner() {
    this.onMassOperations(MassOps.CHANGE_OWNER);
  }

  onMassSchedulesPause() {
    this.onMassOperations(MassOps.PAUSE);
  }

  onMassSchedulesResume() {
    this.onMassOperations(MassOps.RESUME);
  }

  async onDeleteReplication(): Promise<void> {
    this.onMassDeleteOps();
  }

  getSelectedTables() {
    // return values of map
    return Object.values(this.selectedRowMap);
  }

  async onRemoteTableMultiRowSelect(oTable: sap.ui.table.Table, view: any) {
    const selectedTables = this.getSelectedTables();
    RemoteTableServiceUtil.handleRemoteTableMultiSelectButtonStates(selectedTables, view);
  }

  async remoteTableRowChange(oEvent: any): Promise<any> {
    const model = this.view.getModel("replicationModel");
    const oTable = this.getView().byId("idRemoteTablesTable") as sap.ui.table.Table;
    this.captureSelectedRowsSnapshot(oEvent);
    const selectedIndices = this.getSelectedTables();
    let scheduleDialog;
    if (this.isReusableTaskScheduleFFEnabled) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
    } else {
      scheduleDialog = (
        this.getView().byId("scheduleDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    if (selectedIndices?.length === 0) {
      (this.view.getModel("replicationModel") as sap.ui.model.json.JSONModel).setProperty("/menuButtonsEnabled", false);
    } else if (selectedIndices?.length > 0) {
      (this.view.getModel("replicationModel") as sap.ui.model.json.JSONModel).setProperty("/menuButtonsEnabled", true);
    }
    if (selectedIndices.length > 1) {
      this.onRemoteTableMultiRowSelect(oTable, this.view);
    } else {
      const selectedTables = this.getSelectedTables() as any;
      const tableName = selectedTables[0]?.tableName;
      const repStatus = selectedTables[0]?.replicationStatus;
      const isScheduled = selectedTables[0]?.scheduled;
      this.selectedTable = tableName;
      const scheduleList = await scheduleDialog.getTaskScheduleList(this.spaceId, ApplicationId.REMOTE_TABLES);
      const selectedTableSchedule = scheduleList?.find((obj) => obj.objectId === this.selectedTable);
      if (
        selectedTables.length === 0 ||
        (selectedTableSchedule === undefined &&
          (repStatus === Shared.ReplicationStatus.INITIALIZING || repStatus === Shared.ReplicationStatus.DISCONNECTED))
      ) {
        model.setProperty("/newSchedule", false);
        model.setProperty("/editSchedule", false);
        model.setProperty("/deleteSchedule", false);
        model.setProperty("/loadNewSnapshot", false);
        model.setProperty("/enableRealTimeAccess", false);
        model.setProperty("/removeReplicatedData", false);
        this.view.getModel("replicationModel").setProperty("/deleteMassSchedules", false);
        this.view.getModel("replicationModel").setProperty("/assignSchedule", false);
        this.view.getModel("replicationModel").setProperty("/pauseSchedule", false);
        this.view.getModel("replicationModel").setProperty("/resumeSchedule", false);
      } else {
        if (selectedTableSchedule !== undefined && isScheduled) {
          model.setProperty("/newSchedule", false);
          model.setProperty("/editSchedule", true);
          model.setProperty("/deleteSchedule", true);
        } else {
          model.setProperty("/newSchedule", true);
          model.setProperty("/editSchedule", false);
          model.setProperty("/deleteSchedule", false);
        }
      }
      /** Below are table replication model peroperties */
      if (selectedTables.length === 1) {
        const index = selectedIndices.length > 0 ? selectedIndices[0] : -1;
        const selectedRowData = selectedTables[0];
        RemoteTableServiceUtil.blueGreenMenuBtnStates(
          selectedRowData,
          index,
          selectedTableSchedule,
          isScheduled,
          this.view
        );
      }
    }

    await this.handleMassOpsButtonStatus(oTable, scheduleDialog);
  }

  private async handleMassOpsButtonStatus(oTable: sap.ui.table.Table, scheduleDialog) {
    const selectedTables = this.getSelectedTables();
    const selectedObjects = selectedTables.map((table: { tableName: string }) => table.tableName);
    const massOpsButtonsStatus = await scheduleDialog.getMassOpsButtonEnablement(
      selectedObjects,
      this.spaceId,
      ApplicationId.REMOTE_TABLES
    );
    const isCreateEnabled = selectedTables.length === 1 && massOpsButtonsStatus.delete === false;
    const isEditEnabled = selectedTables.length === 1 && massOpsButtonsStatus.delete;
    this.view.getModel("replicationModel").setProperty("/newSchedule", isCreateEnabled);
    this.view.getModel("replicationModel").setProperty("/editSchedule", isEditEnabled);
    this.view.getModel("replicationModel").setProperty("/deleteMassSchedules", massOpsButtonsStatus.delete);
    this.view.getModel("replicationModel").setProperty("/assignSchedule", massOpsButtonsStatus.changeOwner);
    this.view.getModel("replicationModel").setProperty("/pauseSchedule", massOpsButtonsStatus.pause);
    this.view.getModel("replicationModel").setProperty("/resumeSchedule", massOpsButtonsStatus.resume);
    this.view.getModel("replicationModel").setProperty("/deleteCount", massOpsButtonsStatus.deleteCount);
    this.view.getModel("replicationModel").setProperty("/changeOwnerCount", massOpsButtonsStatus.changeOwnerCount);
    this.view.getModel("replicationModel").setProperty("/pauseCount", massOpsButtonsStatus.pauseCount);
    this.view.getModel("replicationModel").setProperty("/resumeCount", massOpsButtonsStatus.resumeCount);
  }

  public onCellClick(event) {
    const sourceControl = event.getSource();
    let remoteTableSelected;
    const rowBindingContext = event.getParameter("rowBindingContext");
    if (event.getId() === "press") {
      remoteTableSelected = sourceControl.getParent().getBindingContext().getObject();
    }
    let colName = "replicationStatusColumnNew";

    if ((event.getParameter("columnId")?.includes(colName) && rowBindingContext !== null) || remoteTableSelected) {
      if (rowBindingContext) {
        const rowTablePath = rowBindingContext.getPath().split("/tables/")[1];
        const oData = rowBindingContext.getModel().getData();
        remoteTableSelected = oData && oData.tables && oData.tables[rowTablePath];
      }
      const connectionName = remoteTableSelected.connectionName;
      const replicationStatus = remoteTableSelected && remoteTableSelected.replicationStatus;
      const replicationType = remoteTableSelected && remoteTableSelected.replicationType;
      const taskState = remoteTableSelected && remoteTableSelected.taskState;
      const realTimeReplicationStatus = this.formatter.getTextRepStatusRealTime(
        replicationStatus,
        replicationType,
        taskState
      );
      (this.view.getModel("realTimeReplStausModel") as sap.ui.model.json.JSONModel).setProperty(
        "/remoteSourceName",
        connectionName
      );
      if (realTimeReplicationStatus === "Paused") {
        const oFragment = require("../view/realTimeReplStatusPopover.fragment.xml");
        if (!this.realTimeReplStatusPopover) {
          this.realTimeReplStatusPopover = sap.ui.xmlfragment(
            this.getView().getId() + "--realTimeReplStatus",
            oFragment,
            this
          ) as sap.m.Popover;
          this.view.addDependent(this.realTimeReplStatusPopover);
        }
        this.realTimeReplStatusPopover.openBy(sourceControl, false);
      } else if (replicationStatus === ReplicationStatus.ERROR && remoteTableSelected.errorDetails) {
        const errorFragment = require("../view/errorDetailsPopover.fragment.xml");
        const errorDetailsPopover = sap.ui.xmlfragment("", errorFragment, this) as sap.m.Popover;
        errorDetailsPopover.setModel(this.formatter.getModelForPopOver(remoteTableSelected, false));
        this.getView().addDependent(errorDetailsPopover);
        errorDetailsPopover.openBy(sourceControl, true);
      } else if (this.realTimeReplStatusPopover) {
        this.realTimeReplStatusPopover.close();
      }
    } else if (this.realTimeReplStatusPopover) {
      this.realTimeReplStatusPopover.close();
    }
  }
  public getPausedStatusPopovertext(source) {
    return (this.view.getModel("i18n") as sap.ui.model.resource.ResourceModel)
      .getResourceBundle()
      .getText("txtPausedRealTimeReplStatusPopover", [source]);
  }

  public async navigateToSpaceConnectionsList() {
    const target: any = {
      semanticObject: "connections",
      action: "",
    };
    const params: any = {
      spaceId: this.spaceId,
    };
    ShellNavigationService.toExternal({ target, params });
  }

  /**
   * Function to navigate to BW4Cockpit application in a new Tab
   */
  public openBW4Cockpit() {
    const sURL = this.view.getModel("bwModel").getProperty("/bw4CockpitURL");
    window.open(sURL, "_blank", "noopener");
  }

  private calculateTotalDiskSize(tables): any {
    let totalDiskSize = 0;
    let totalMemorySize = 0;
    const obj = {
      totalDiskSize: totalDiskSize,
      totalMemorySize: totalMemorySize,
    };
    for (let i = 0; i < tables.length; i++) {
      obj.totalDiskSize = totalDiskSize += Number(tables[i].diskSizeReplicaTableInMB);
      obj.totalMemorySize = totalMemorySize += Number(tables[i].inMemorySizeReplicaTableMB ?? 0); // use 0 if inMemorySizeReplicaTableMB is undefined
    }
    return obj;
  }

  public onPersoButtonPress() {
    this.tablePersoController.openDialog();
  }

  private getTable(): sap.m.Table {
    return this.byId("idRemoteTablesTable") as sap.m.Table;
  }

  public getScheduledText(tableName, formattedRefreshFrequency, isPaused, scheduleList) {
    if (isDiMonitorImprovementsEnabled() && !isPaused && scheduleList !== undefined) {
      const schedule = scheduleList?.find((obj) => obj.objectId === tableName);
      if (schedule) {
        return schedule?.cron !== undefined
          ? schedule?.cron
          : this.getText("everyLabel") +
              " " +
              schedule?.frequency?.interval +
              " " +
              this.getFrequencyText(schedule?.frequency?.type);
      } else {
        return formattedRefreshFrequency;
      }
    } else {
      return formattedRefreshFrequency;
    }
  }

  public getFrequencyText(type) {
    switch (type) {
      case "MINUTES":
        return this.getText("minutesLabel");
      case "HOURLY":
        return this.getText("hoursLabel");
      case "DAILY":
        return this.getText("daysLabel");
      case "WEEKLY":
        return this.getText("weeksLabel");
      case "MONTHLY":
        return this.getText("monthsLabel");
      default:
        return "";
    }
  }
}

export const RemoteTables = smartExtend(
  BaseController,
  "sap.cdw.components.remotetablemonitor.controller.RemoteTableMonitor",
  RemoteTableClass
);

sap.ui.define("sap/cdw/components/remotetablemonitor/controller/RemoteTableMonitor.controller", [], function () {
  return RemoteTables;
});
