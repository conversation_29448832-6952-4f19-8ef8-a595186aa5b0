/** @format */

import { isNotUndefined } from "@sap/deepsea-utils";
import { State } from "@sap/dwc-circuit-breaker";
import { isUndefined } from "lodash";
import { DataAccessType } from "../../../../shared/remoteTables/types";
import { isCircuitBreakerYellowStateEnabled } from "../../shell/featureState/CircuitBreakerYellowStateFeatureState";
import { Activity, dataAccessType } from "./Constants";

const unsupportedAdapter = ["ABAPAdapter", "CloudDataIntegrationAdapter", "CDI::CDI"];

/**
 * export const releaseLockVisibilityformatter = function (routeName: string, privlege: boolean) {
  if (privlege && (routeName === "viewMonitorTaskLog" || routeName === "viewMonitorTaskLogDetails")) {
    return true;
  }
  return false;
}; */

// TODO: Refactor this during DWC_DUMMY_SPACE_PERMISSIONS FF removal. Remove 'privilege', 'isSDPEnabled' param from function and xml file

export const runDetailsCancelRunVisibilityformatter = function (
  routeName: string,
  privilege: boolean,
  isSDPEnabled?: boolean,
  updatePrivilege?: boolean,
  canCreateOrUpdateMonitoringUI?: boolean
): boolean {
  if (isSDPEnabled) {
    if (
      updatePrivilege &&
      canCreateOrUpdateMonitoringUI &&
      (routeName === "remoteTableTaskLog" || routeName === "remoteTableTaskLogDetails")
    ) {
      return true;
    } else if (
      updatePrivilege &&
      canCreateOrUpdateMonitoringUI &&
      (routeName === "viewMonitorTaskLog" || routeName === "viewMonitorTaskLogDetails")
    ) {
      return true;
    } else if (
      updatePrivilege &&
      canCreateOrUpdateMonitoringUI &&
      (routeName === "taskChainMonitorLog" || routeName === "taskChainMonitorLogDetails")
    ) {
      const isChainCancelEnabled = sap.ui
        .getCore()
        ?.getModel("featureflags")
        .getProperty("/DWCO_TASK_FRAMEWORK_CANCEL_CHAIN");
      if (isChainCancelEnabled) {
        return true;
      }
    }
    return false;
  } else {
    if (privilege && (routeName === "remoteTableTaskLog" || routeName === "remoteTableTaskLogDetails")) {
      return true;
    } else if (privilege && (routeName === "viewMonitorTaskLog" || routeName === "viewMonitorTaskLogDetails")) {
      return true;
    } else if (privilege && (routeName === "taskChainMonitorLog" || routeName === "taskChainMonitorLogDetails")) {
      return true;
    }
    return false;
  }
};

export const releaseLockEnableformatter = function (lock: boolean): boolean {
  return lock;
};

// TODO: Refactor this during DWC_DUMMY_SPACE_PERMISSIONS FF removal. Remove 'privilege', 'isSDPEnabled' param from function and xml file
export const retryVisibilityformatter = function (
  showError: boolean,
  privlege: boolean,
  isSDPEnabled?: boolean,
  updatePrivilege?: boolean,
  canCreateOrUpdateMonitoringUI?: boolean
): boolean {
  if (isSDPEnabled) {
    if (updatePrivilege && canCreateOrUpdateMonitoringUI && showError) {
      return true;
    }
    return false;
  } else {
    if (privlege && showError) {
      return true;
    }
    return false;
  }
};

// TODO: Refactor this during DWC_DUMMY_SPACE_PERMISSIONS FF removal. Remove 'privilege', 'isSDPEnabled' param from function and xml file
export const executeTaskChainMenuFormatter = function (
  routeName: string,
  privilege: boolean,
  isSDPEnabled?: boolean,
  updatePrivilege?: boolean,
  canCreateOrUpdateMonitoringUI?: boolean
) {
  if (isSDPEnabled) {
    if (
      updatePrivilege &&
      canCreateOrUpdateMonitoringUI &&
      (routeName === "taskChainMonitorLog" || routeName === "taskChainMonitorLogDetails")
    ) {
      return true;
    }
    return false;
  } else {
    if (privilege && (routeName === "taskChainMonitorLog" || routeName === "taskChainMonitorLogDetails")) {
      return true;
    }
    return false;
  }
};

// TODO: Refactor this during DWC_DUMMY_SPACE_PERMISSIONS FF removal. Remove 'privilege', 'isSDPEnabled' param from function and xml file
export const executeRetryLatestRunFormatter = function (
  routeName: string,
  privilege: boolean,
  status: string,
  subStatus: string,
  selectedLogId?: number,
  latestLogId?: number,
  isSDPEnabled?: boolean,
  updatePrivilege?: boolean,
  canCreateOrUpdateMonitoringUI?: boolean
) {
  const hasLatestRunFailed = status?.toLowerCase() === "failed" && subStatus !== "FAIL_CONSENT_NOT_AVAILABLE";
  if (isSDPEnabled) {
    // Decouple the privilege
    if (
      updatePrivilege &&
      canCreateOrUpdateMonitoringUI &&
      (routeName === "taskChainMonitorLog" || routeName === "taskChainMonitorLogDetails") &&
      hasLatestRunFailed &&
      (isUndefined(selectedLogId) || (isNotUndefined(selectedLogId) && selectedLogId === latestLogId))
    ) {
      return true;
    }
    return false;
  } else {
    if (
      privilege &&
      (routeName === "taskChainMonitorLog" || routeName === "taskChainMonitorLogDetails") &&
      hasLatestRunFailed &&
      (isUndefined(selectedLogId) || (isNotUndefined(selectedLogId) && selectedLogId === latestLogId))
    ) {
      return true;
    }
    return false;
  }
};

export const executeRetryLatestRunMenuFormatter = function (
  routeName: string,
  privilege: boolean,
  status: string,
  subStatus: string,
  isSDPEnabled?: boolean,
  updatePrivilege?: boolean,
  canCreateOrUpdateMonitoringUI?: boolean
) {
  const hasLatestRunFailed = status?.toLowerCase() === "failed" && subStatus !== "FAIL_CONSENT_NOT_AVAILABLE";
  if (isSDPEnabled) {
    // Decouple the privilege
    if (
      updatePrivilege &&
      canCreateOrUpdateMonitoringUI &&
      (routeName === "taskChainMonitorLog" || routeName === "taskChainMonitorLogDetails") &&
      hasLatestRunFailed
    ) {
      return true;
    }
    return false;
  } else {
    if (
      privilege &&
      (routeName === "taskChainMonitorLog" || routeName === "taskChainMonitorLogDetails") &&
      hasLatestRunFailed
    ) {
      return true;
    }
    return false;
  }
};

export const partitionVisibilityFormatter = function (
  partitionFF: boolean,
  routeName: string,
  partitionSupported: boolean
): boolean {
  if (
    partitionFF &&
    (routeName === "remoteTableTaskLog" || routeName === "remoteTableTaskLogDetails") &&
    partitionSupported
  ) {
    return true;
  }
  return false;
};

// TODO: Refactor this during DWC_DUMMY_SPACE_PERMISSIONS FF removal. Remove 'privilegeModel', 'isSDPEnabled' param from function and xml file
export const partitionAvailableVisibility = function (
  privilegeModel,
  detailsModel,
  isSDPEnabled?: boolean,
  updatePrivilege?: boolean,
  canCreateOrUpdateMonitoringUI?: boolean
) {
  if (isSDPEnabled) {
    if (
      privilegeModel.isRemoteTable &&
      updatePrivilege &&
      canCreateOrUpdateMonitoringUI &&
      detailsModel?.partitioningSupported &&
      detailsModel?.partitioningExists
    ) {
      return true;
    } else if (privilegeModel.isViewMonitor && detailsModel?.partitioningExists) {
      return true;
    }
    return false;
  } else {
    if (
      privilegeModel.isRemoteTable &&
      privilegeModel.update &&
      detailsModel?.partitioningSupported &&
      detailsModel?.partitioningExists
    ) {
      return true;
    } else if (privilegeModel.isViewMonitor && detailsModel?.partitioningExists) {
      return true;
    }
    return false;
  }
};

export const runDetailsCancelRunEnableformatter = function (logsModel, routeName): boolean {
  if (logsModel && logsModel.length > 0) {
    if (
      routeName === "remoteTableTaskLog" ||
      routeName === "remoteTableTaskLogDetails" ||
      routeName === "viewMonitorTaskLog" ||
      routeName === "viewMonitorTaskLogDetails" ||
      routeName === "taskChainMonitorLog" ||
      routeName === "taskChainMonitorLogDetails"
    ) {
      if (routeName === "taskChainMonitorLog" || routeName === "taskChainMonitorLogDetails") {
        const isChainCancelEnabled = sap.ui
          .getCore()
          ?.getModel("featureflags")
          .getProperty("/DWCO_TASK_FRAMEWORK_CANCEL_CHAIN");
        if (!isChainCancelEnabled) {
          return false;
        }
      }
      const latestRunningTaskDetails = logsModel?.find((i) => i.status === "RUNNING");
      if (
        [
          Activity.PERSIST,
          Activity.EXECUTE_VIEW_ANALYZER,
          Activity.REPLICATE,
          Activity.ENABLE_REALTIME,
          Activity.RUN_CHAIN,
        ].includes(latestRunningTaskDetails?.activity)
      ) {
        return true;
      } else {
        return false;
      }
    }
  }
  return false;
};

export const createStatisticsVisibilityFormatter = function (dataAccess, adapter) {
  if (dataAccess === DataAccessType.REMOTE && !unsupportedAdapter.includes(adapter)) {
    return true;
  }
  return false;
};

export const deleteStatisticsVisibilityFormatter = function (statisticsType, dataAccess, adapter) {
  if (dataAccess !== dataAccessType.REMOTE) {
    return false;
  } else if (!!statisticsType && statisticsType !== "---" && !unsupportedAdapter.includes(adapter)) {
    return true;
  }
  return false;
};

export const hanaStateFormatter = function (hanaState: State) {
  // On FF removal of isCircuitBreakerYellowStateEnabled be sure to keep boolean logic consistent
  if (hanaState === State.Red || (hanaState === State.Yellow && !isCircuitBreakerYellowStateEnabled())) {
    return State.Red;
  }
  return State.Green;
};

export const navToTableFormatter = function () {
  const spaceId = this.spaceId;
  const objectId = this.srcObjectId;
  if (!spaceId || !objectId) {
    return "#"; // fallback
  }
  const shellHash = "dataintegration&/di";
  const targetHash = `${encodeURIComponent(spaceId)}/remoteTableMonitor/${encodeURIComponent(objectId)}`;
  return `#/${shellHash}/${targetHash}`;
};
