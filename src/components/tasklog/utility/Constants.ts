/**
 * Copyright 2020 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */
export enum monitoringObjectType {
  REMOTETABLE = "remoteTables",
  VIEW = "persistedViews",
  TASKCHAIN = "taskchain",
  REMOTETABLESTATISTICS = "statistics",
}

export enum ExecutionType {
  DIRECT = "DIRECT",
  SCHEDULED = "SCHEDULED",
}

export enum JobExecutionType {
  DEFAULT = "D",
  SYNCHRONOUS = "S",
  ASYNCHRONOUS = "A",
}

export enum SpaceRequirement {
  NO_SPACE_REQUIRED = 0,
  SPACE_REQUIRED = 1,
}

export enum ApplicationId {
  REMOTE_TABLES = "REMOTE_TABLES",
  VIEWS = "VIEWS",
  BUSINESS_BUILDER = "BUSINESS_BUILDER",
  DATA_FLOWS = "DATA_FLOWS",
  DP_AGENTS = "DP_AGENTS",
  TASK_CHAINS = "TASK_CHAINS",
  SPACE = "SPACE",
  ELASTIC_COMPUTE_NODE = "ELASTIC_COMPUTE_NODE",
  LOCAL_TABLE = "LOCAL_TABLE",
  LOCAL_TABLE_VARIANT = "LOCAL_TABLE_VARIANT",
  INTELLIGENT_LOOKUP = "INTELLIGENT_LOOKUP",
  TRANSFORMATION_FLOWS = "TRANSFORMATION_FLOWS",
  REPLICATION_FLOWS = "REPLICATION_FLOWS",
  BW_PROCESS_CHAIN = "BW_PROCESS_CHAIN",
  SQL_SCRIPT_PROCEDURE = "SQL_SCRIPT_PROCEDURE",
  API = "API",
  NOTIFICATION = "NOTIFICATION",
}

export enum Activity {
  REPLICATE = "REPLICATE",
  ENABLE_REALTIME = "ENABLE_REALTIME",
  REMOVE_REPLICATED_DATA = "REMOVE_REPLICATED_DATA",
  REMOVE_PERSISTED_DATA = "REMOVE_PERSISTED_DATA",
  PERSIST = "PERSIST",
  EXECUTE = "EXECUTE",
  RUN_CHAIN = "RUN_CHAIN",
  TASKLOG_CLEANUP = "TASKLOG_CLEANUP",
  MODEL_IMPORT = "MODEL_IMPORT",
  CANCEL_REPLICATION = "CANCEL_REPLICATION",
  DPAGENT_STATUS_NOTIFICATION = "DPAGENT_STATUS_NOTIFICATION",
  CREATE_STATISTICS = "CREATE_STATISTICS",
  DROP_STATISTICS = "DROP_STATISTICS",
  REFRESH_STATISTICS = "REFRESH_STATISTICS",
  ALTER_STATISTICS = "ALTER_STATISTICS",
  CANCEL_PERSISTENCY = "CANCEL_PERSISTENCY",
  EXECUTE_VIEW_ANALYZER = "EXECUTE_VIEW_ANALYZER",
  CANCEL_VIEW_ANALYZER = "CANCEL_VIEW_ANALYZER",
  ADD = "ADD",
  REMOVE = "REMOVE",
  CREATE_REPLICA = "CREATE_REPLICA",
  DROP_REPLICA = "DROP_REPLICA",
  ROUTE_COMPUTE_SERVER = "ROUTE_COMPUTE_SERVER",
  ROUTE_COORDINATOR = "ROUTE_COORDINATOR",
  GENERATE_START_CHAIN = "GENERATE_START_CHAIN",
  GENERATE_STOP_CHAIN = "GENERATE_STOP_CHAIN",
  VALIDATE = "VALIDATE",
  DELETE_DATA = "DELETE_DATA",
  REMOVE_DELETED_RECORDS = "REMOVE_DELETED_RECORDS",
  RESET_WATERMARKS = "RESET_WATERMARKS",
  CANCEL = "CANCEL",
  RUN_PERMANENT = "RUN_PERMANENT",
  RUN_BW_CHAIN = "RUN_BW_CHAIN",
  RUN_SQL_SCRIPT_PROCEDURE = "RUN",
  RUN_PERMANENT_TECHNICAL = "RUN_PERMANENT_TECHNICAL",
  SIMULATE_RUN = "SIMULATE_RUN",
  VACUUM_FILES = "VACUUM_FILES",
  MERGE_FILES = "MERGE_FILES",
  OPTIMIZE_FILES = "OPTIMIZE_FILES",
  TRUNCATE_FILES = "TRUNCATE_FILES",
  ANALYZE_PERFORMANCE = "ANALYZE_PERFORMANCE",
  CANCEL_ANALYZE_PERFORMANCE = "CANCEL_ANALYZE_PERFORMANCE",
}
export enum TaskStatus {
  COMPLETED = "COMPLETED",
  RUNNING = "RUNNING",
  FAILED = "FAILED",
  NOT_TRIGGERED = "NOT_TRIGGERED",
  COMPLETE = "COMPLETE",
}
export enum Severity {
  INFO = "INFO",
  SUCCESS = "SUCCESS",
  WARNING = "WARNING",
  ERROR = "ERROR",
}

export interface IMessage {
  isJsonDataAvailable?: boolean;
  messageBundleId: string;
  messageBundleKey: string;
  messageNumber: number;
  parameterValues: string[];
  severity: Severity;
  text?: string;
  timestamp: string;
  details?: string;
}

export interface ITaskLogIncomplete {
  messages: IMessage[];
  logId: string;
  status: string;
}

export interface ITaskLogResponse {
  logs: ITaskLogIncomplete[];
  locks: [];
}

export const MAP_MESSAGEBUNDLE_IDS = {
  "sap.dwc.remoteTable": "sap.cdw.components.remotetablemonitor.i18n.i18n",
  "sap.dwc.persistedView": "sap.cdw.components.viewmonitor.i18n.i18n",
  "sap.cdw.c4sbuilder.importcsn.i18n": "sap.cdw.components.c4sbuilder.importcsn.i18n",
  "sap.dwc.taskFramework": "sap.cdw.components.taskchainmonitor.i18n.i18n",
  "sap.dwc.dataflowTaskLogs": "sap.cdw.components.flowmonitor.i18n.i18n",
  "sap.dwc.viewAnalyzer": "sap.cdw.components.viewmonitor.i18n.i18n",
  "sap.dwc.elasticComputeNode": "sap.cdw.components.tasklog.i18n.i18n",
  "sap.dwc.ilTaskLogs": "sap.cdw.components.intelligentlookup.i18n.i18n",
  "sap.dwc.localTable": "sap.cdw.components.tasklog.i18n.i18n",
  "sap.dwc.bwProcessChainLogs": "sap.cdw.components.taskchainmonitor.i18n.i18n",
  "sap.dwc.apiTaskLogs": "sap.cdw.components.taskchainmonitor.i18n.i18n",
  "sap.dwc.notificationTaskLogs": "sap.cdw.components.taskchainmonitor.i18n.i18n",
};

export enum persistencyStatus {
  Initialize = "I",
  None = "D",
  Available = "A",
  Error = "E",
}

export enum dataAccessType {
  REMOTE = "REMOTE",
  REALTIME_REPLICATION = "REALTIME_REPLICATION",
  SNAPSHOT_REPLICATION = "SNAPSHOT_REPLICATION",
}

export const taskLogRoutes = [
  "remoteTableTaskLog",
  "remoteTableTaskLogDetails",
  "viewMonitorTaskLog",
  "viewMonitorTaskLogDetails",
  "taskChainMonitorLog",
  "taskChainMonitorLogDetails",
  "logDetails",
  "remoteTableStatisticsMonitorLog",
  "remoteTableStatisticsMonitorLogDetails",
  "ecntaskchainmonitoring",
  "ecnlogsmonitoring",
  "logsmonitoring",
];

export const objectDetailsRoutes = [
  "remoteTableTaskLog",
  "remoteTableTaskLogDetails",
  "viewMonitorTaskLog",
  "viewMonitorTaskLogDetails",
  "taskChainMonitorLog",
  "taskChainMonitorLogDetails",
  "remoteTableStatisticsMonitorLog",
  "remoteTableStatisticsMonitorLogDetails",
];

export const metrics_value = {
  EXECUTION_MODE: ["performanceOptimized", "memoryOptimized"],
};
