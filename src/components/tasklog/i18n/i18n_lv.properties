
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Detalizēta informācija par žurnālu
#XFLD: Header
TASK_LOGS=<PERSON>z<PERSON><PERSON><PERSON> žurnāli ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Izpildes ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Skatīt detalizētu informāciju
#XFLD: Button text
STOP=Apturēt izpildi
#XFLD: Label text
RUN_START=Pēdējās izpildes sākums
#XFLD: Label text
RUN_END=Pēdējās izpildes beigas
#XFLD: Label text
RUNTIME=Ilgums
#XTIT: Count for Messages
txtDetailMessages=Ziņojumi ({0})
#XFLD: Label text
TIME=Laikspiedols
#XFLD: Label text
MESSAGE=Ziņojums
#XFLD: Label text
TASK_STATUS=Kategorija
#XFLD: Label text
TASK_ACTIVITY=Darbība
#XFLD: Label text
RUN_START_DETAILS=Sākums
#XFLD: Label text
RUN_END_DETAILS=Beigas
#XFLD: Label text
LOGS=Izpildes
#XFLD: Label text
STATUS=Statuss
#XFLD: Label text
RUN_STATUS=Izpildes statuss
#XFLD: Label text
Runtime=Ilgums
#XFLD: Label text
RuntimeTooltip=Ilgums (hh : mm : ss)
#XFLD: Label text
TRIGGEREDBY=Izraisīja:
#XFLD: Label text
TRIGGEREDBYNew=Izpildīja
#XFLD: Label text
TRIGGEREDBYNewImp=Izpildi sāka
#XFLD: Label text
EXECUTIONTYPE=Izpildes tips
#XFLD: Label text
EXECUTIONTYPENew=Izpildes tips
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Augstāka līmeņa ķēdes vieta
#XFLD: Refresh tooltip
TEXT_REFRESH=Atsvaidzināt
#XFLD: view Details link
VIEW_ERROR_DETAILS=Skatīt detalizētu informāciju
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Lejupielādēt detalizēto papildinformāciju
#XMSG: Download completed
downloadStarted=Lejupielāde sākta
#XMSG: Error while downloading content
errorInDownload=Lejupielādes laikā radās kļūda.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Skatīt detalizētu informāciju
#XBTN: cancel button of task details dialog
TXT_CANCEL=Atcelt
#XBTN: back button from task details
TXT_BACK=Atpakaļ
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Uzdevums ir pabeigts
#XFLD: Log message with failed status
MSG_LOG_FAILED=Uzdevums neizdevās
#XFLD: Master and detail table with no data
No_Data=Nav datu
#XFLD: Retry tooltip
TEXT_RETRY=Mēģināt vēlreiz
#XFLD: Cancel Run label
TEXT_CancelRun=Atcelt izpildi
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Notīrīt neizdevušos ielādi
#XMSG:button copy sql statement
txtSQLStatement=Kopēt SQL priekšrakstu
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Atvērt attālo vaicājumu pārraugu
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=Lai atvērtu attālos SQL priekšrakstus, noklikšķiniet uz “Atvērt attālo vaicājumu pārraugu”.
#XMSG:button ok
txtOk=Labi
#XMSG: button close
txtClose=Aizv.
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=Izpildes darbības atcelšana objektam "{0}” ir sākta.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=Izpildes darbības atcelšana objektam "{0}” neizdevās.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=Izpildes darbības atcelšana objektam "{0}” vairs nav iespējama, jo replicēšanas statuss ir mainīts.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Nevienam uzdevumu žurnālam nav statusa Tiek izpildīts.
#XMSG: message for conflicting task
Task_Already_Running=Konfliktējošais uzdevums jau tiek izpildīts objektam "{0}".
#XFLD: Label for no task log with running state title
actionInfo=Informācija par darbību
#XMSG Copied to clipboard
copiedToClip=Kopēts uz starpliktuvi
#XFLD copy
Copy=Kopēt
#XFLD copy correlation ID
CopyCorrelationID=Kopēt korelācijas ID
#XFLD Close
Close=Aizvērt
#XFLD: show more Label
txtShowMore=Rādīt vairāk
#XFLD: message Label
messageLabel=Ziņojums:
#XFLD: details Label
detailsLabel=Detalizēta informācija:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=SQL \r\n priekšraksta izpildīšana:
#XFLD:statementId Label
statementIdLabel=Priekšraksta ID:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Attālo \r\n SQL priekšrakstu skaits:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Priekšrakstu skaits
#XFLD: Space Label
txtSpaces=Vieta
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Neautorizētas vietas
#XFLD: Privilege Error Text
txtPrivilegeError=Jums nav pietiekamu privilēģiju, lai skatītu šos datus.
#XFLD: Label for Object Header
DATA_ACCESS=Datu piekļuve
#XFLD: Label for Object Header
SCHEDULE=Grafiks
#XFLD: Label for Object Header
DETAILS=Detalizēta informācija
#XFLD: Label for Object Header
LATEST_UPDATE=Jaunākais atjauninājums
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Pēdējās izmaiņas (avots)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Atsvaidzināšanas biežums
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Ieplānotais biežums
#XFLD: Label for Object Header
NEXT_RUN=Nākamā izpilde
#XFLD: Label for Object Header
CONNECTION=Savienojums
#XFLD: Label for Object Header
DP_AGENT=DP aģents
#XFLD: Label for Object Header
USED_IN_MEMORY=Krātuvei izmantotā atmiņa (MiB)
#XFLD: Label for Object Header
USED_DISK=Krātuvei izmantotais disks (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Lielums atmiņā (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Lielums uz diska (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Ierakstu skaits

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Iestatīt kā neizdevušos
SET_TO_FAILED_ERR=Šis izdevums tika izpildīts, taču lietotājs iestatīja š;i uzdevuma statusu kā NEIZDEVĀS.
#XFLD: Label for stopped failed
FAILLOCKED=Izpilde jau notiek
#XFLD: sub status STOPPED
STOPPED=Apturēts
STOPPED_ERR=Šis uzdevums tika apturēts, taču atrite netika veikta.
#XFLD: sub status CANCELLED
CANCELLED=Atcelts
CANCELLED_ERR=Šī uzdevuma izpilde tika atcelta pēc tam, kad tā tika sākta. Šajā gadījumā tika veikta datu atrite, un tie tika atjaunoti stāvoklī, kāds bija pirms uzdevuma izpilde tika sākotnēji izraisīta.
#XFLD: sub status LOCKED
LOCKED=Bloķēts
LOCKED_ERR=Tas pats uzdevums jau tika izpildīts, tāpēc šo uzdevumu nevar izpildīt paralēli ar esošā uzdevuma izpildi.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Uzdevuma izņēmums
TASK_EXCEPTION_ERR=Šim uzdevumam izpildes laikā radās nenoteikta kļūda.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Uzdevuma izpildes izņēmums
TASK_EXECUTE_EXCEPTION_ERR=Šim uzdevumam izpildes laikā radās kļūda.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Neautorizēts
UNAUTHORIZED_ERR=Šo lietotāju nevarēja autentificēt, tas ir bloķēts vai izdzēsts.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Aizliegts
FORBIDDEN_ERR=Piešķirtajam lietotājam nav privilēģiju, kas nepieciešamas šī uzdevuma izpildei.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Nav izraisīts
FAIL_NOT_TRIGGERED_ERR=Šo uzdevumu nevarēja izpildīt sistēmas dīkstāves dēļ, vai dažas datu bāzes sistēmas daļas plānotās izpildes laikā nav pieejamas. Gaidiet nākamo ieplānoto izpildes laiku vai pārplānojiet šo darbu.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Grafiks atcelts
SCHEDULE_CANCELLED_ERR=Šo uzdevumu nevarēja izpildīt iekšējās kļūdas dēļ. Sazinieties ar SAP atbalstu un norādiet viņiem korelācijas ID un laikspiedolu no šī uzdevuma žurnāla detalizētās informācijas.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Notiek iepriekšējā izpilde
SUCCESS_SKIPPED_ERR=Šī uzdevuma izpilde netika izraisīta, jo joprojām notiek tā paša uzdevuma iepriekšējā izpilde.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Trūkst īpašnieka
FAIL_OWNER_MISSING_ERR=Šo uzdevumu nevarēja izpildīt, jo tam nav piesaistīta sistēmas lietotāja. Piesaistiet darbam lietotāju ar īpašnieka tiesībām.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Piekrišana nav pieejama
FAIL_CONSENT_NOT_AVAILABLE_ERR=Jūr neesat pilnvarojis SAP izpildīt uzdevumu ķēdes vai ieplānot datu integrācijas uzdevumus jūsu vārdā. Atlasiet nodrošināto iespēju, lai sniegtu savu piekrišanu.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Piekrišanas derīgums ir beidzies
FAIL_CONSENT_EXPIRED_ERR=Autorizācijas, kas ļauj SAP izpildīt uzdevumu ķēdes vai ieplānot datu integrācijas uzdevumus jūsu vārdā, derīgums ir beidzies.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Piekrišana ir atzīta par nederīgu
FAIL_CONSENT_INVALIDATED_ERR=Šo uzdevumu nevarēja izpildīt, kas parasti ir nomnieka identitātes nodrošinātāja konfigurācijas izmaiņu dēļ. Tādā gadījumā ietekmētā lietotāja vārdā nevar izpildīt vai ieplānot nevienu jaunu uzdevumu. Ja piešķirtais lietotājs joprojām pastāv jaunajā IdP, atsauciet ieplānošanas piekrišanu un tad piešķiriet to vēlreiz. Ja piešķirtais lietotājs vairs nepastāv, piešķiriet jaunu uzdevumu īpašnieku un sniedziet nepieciešamo uzdevumu ieplānošanas piekrišanu. Papildinformāciju skatiet šādā SAP piezīmē: https://launchpad.support.sap.com/#/notes/3089828 for more information.
TASK_EXECUTOR_ERROR=Uzdevumu izpildītājs
TASK_EXECUTOR_ERROR_ERR=Šim uzdevumam radās iekšēja kļūda, visdrīzāk tas notika izpildes sagatavošanas soļu laikā, un uzdevumu nevarēja sākt.
PREREQ_NOT_MET=Priekšnoteikums nav izpildīts
PREREQ_NOT_MET_ERR=Šo uzdevumu nevar izpildīt, jo tā definīcijā ir problēmas. Piemēram, šis objekts nav izvietots, uzdevumu ķēde satur cirkulāru loģiku vai skata SQL ir nederīga.
RESOURCE_LIMIT_ERROR=Resursu limita kļūda
RESOURCE_LIMIT_ERROR_ERR=Uzdevumu pašlaik nevar veikt, jo nepietika resursu vai tie bija aizņemti.
FAIL_CONSENT_REFUSED_BY_UMS=Piekrišana atteikta
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Šo uzdevumu nevarēja izpildīt, ieplānotajās izpildēs vai uzdevumu ķēdēs, jo nomniekam ir mainīta lietotāja identitātes nodrošinātāja konfigurācija. Plašāku informāciju skatiet šādā SAP piezīmē: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Ieplānots
#XFLD: status text
SCHEDULEDNew=Pastāvīgs
#XFLD: status text
PAUSED=Apturēts
#XFLD: status text
DIRECT=Tieši
#XFLD: status text
MANUAL=Manuāls
#XFLD: status text
DIRECTNew=Vienkāršs
#XFLD: status text
COMPLETED=Pabeigts
#XFLD: status text
FAILED=Neizdevās
#XFLD: status text
RUNNING=Tiek izpildīts
#XFLD: status text
none=Nav
#XFLD: status text
realtime=Reāllaika
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Apakšuzdevums
#XFLD: New Data available in the file
NEW_DATA=Jauni dati

#XFLD: text for values shown in column Replication Status
txtOff=Izslēgt
#XFLD: text for values shown in column Replication Status
txtInitializing=Inicializēšana
#XFLD: text for values shown in column Replication Status
txtLoading=Ielāde
#XFLD: text for values shown in column Replication Status
txtActive=Aktīvs
#XFLD: text for values shown in column Replication Status
txtAvailable=Pieejams
#XFLD: text for values shown in column Replication Status
txtError=Kļūda
#XFLD: text for values shown in column Replication Status
txtPaused=Apturēts
#XFLD: text for values shown in column Replication Status
txtDisconnected=Atvienots
#XFLD: text for partially Persisted views
partiallyPersisted=Daļēji pastāvīgs

#XFLD: activity text
REPLICATE=Replicēt
#XFLD: activity text
REMOVE_REPLICATED_DATA=Noņemt replicētos datus
#XFLD: activity text
DISABLE_REALTIME=Atspējot reāllaika datu replicēšanu
#XFLD: activity text
REMOVE_PERSISTED_DATA=Noņemt esošos datus
#XFLD: activity text
PERSIST=Pastāvēt
#XFLD: activity text
EXECUTE=Izpildīt
#XFLD: activity text
TASKLOG_CLEANUP=Tacklog_Cleanup
#XFLD: activity text
CANCEL_REPLICATION=Atcelt replicēšanu
#XFLD: activity text
MODEL_IMPORT=Modeļa importēšana
#XFLD: activity text
NONE=Nav
#XFLD: activity text
CANCEL_PERSISTENCY=Atcelt pastāvīgumu
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Analizēt skatu
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Atcelt skatu analizētāju

#XFLD: severity text
INFORMATION=Informācija
#XFLD: severity text
SUCCESS=Izdevās
#XFLD: severity text
WARNING=Brīdinājums
#XFLD: severity text
ERROR=Kļūda
#XFLD: text for values shown for Ascending sort order
SortInAsc=Kārtot augošā secībā
#XFLD: text for values shown for Descending sort order
SortInDesc=Kārtot dilstošā secībā
#XFLD: filter text for task log columns
Filter=Filtrēt
#XFLD: object text for task log columns
Object=Objekts
#XFLD: space text for task log columns
crossSpace=Vieta

#XBUT: label for remote data access
REMOTE=Attāls
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Replicēts (reāllaika)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Replicēts (momentuzņēmums)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=Reāllaika replicēšana ir bloķēta kļūdas dēļ. Kad kļūda ir izlabota, varat izmantot darbību “Mēģināt vēlreiz”, lai turpinātu reāllaika replicēšanu.
ERROR_MSG=Reāllaika replicēšana ir bloķēta kļūdas dēļ.
RETRY_FAILED_ERROR=Atkārtotas mēģināšanas process neizdevās ar kļūdu.
LOG_INFO_DETAILS=Kad dati tiek replicēti reāllaika režīmā, žurnāli netiek ģenerēti. Parādītie žurnāli ir saistīti ar iepriekšējām darbībām.

#XBUT: Partitioning label
partitionMenuText=Nodalījumu veidošana
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Izveidot nodalījumu
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Rediģēt nodalījumu
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Dzēst nodalījumu
#XFLD: Initial text
InitialPartitionText=Definējiet nodalījumus pēc konkrētiem kritērijiem, lai sadalītu lielas datu kopas mazākās kopās.
DefinePartition=Definēt nodalījumus
#XFLD: Message text
partitionChangedInfo=Nodalījuma definīcija tika mainīta kopš pēdējās replicēšanas. Izmaiņas tiks piemērotas nākamajai datu ielādei.
#XFLD: Message text
REAL_TIME_WARNING=Nodalīšana tiek piemērota, tikai ielādējot jaunu momentuzņēmumu. Tā nav piemērota replicēšanai reāllaikā.
#XFLD: Message text
loadSelectedPartitions=Sāka pastāvīgos datus ''{0}''’ atlasītajiem nodalījumiem
#XFLD: Message text
loadSelectedPartitionsError=Neizdevās padarīt pastāvīgus datus ''{0}''’ atlasītajiem nodalījumiem
#XFLD: Message text
viewpartitionChangedInfo=Kopš pēdējās pastāvīguma izpildes nodalījuma definīcija ir mainīta. Lai lietotu izmaiņas, nākamā datu ielāde būs pilns momentuzņēmums, ieskaitot bloķētos nodalījumus. Kad šī pilnā ielāde būs pabeigta, jūs varēsit izpildīt atsevišķu nodalījumu datus.
#XFLD: Message text
viewpartitionChangedInfoLocked=Kopš pēdējās pastāvīguma izpildes nodalījuma definīcija ir mainīta. Lai lietotu izmaiņas, nākamā datu ielāde būs pilns momentuzņēmums, izņemot bloķēto un nemainīto nodalījumu diapazonus. Kad šī ielāde būs pabeigta, jūs atkal varēsiet izpildīt darbību “Ielādēt atlasītos nodalījumus”.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Tabulas replicēšana
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Ieplānot replicēšanu
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Izveidot grafiku
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Rediģēt grafiku
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Dzēst grafiku
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Ielādēt jaunu momentuzņēmumu
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Sākt datu replicēšanu
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Noņemt replicētos datus
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Iespējot piekļuvi reāllaikā
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Iespējot reāllaika datu replicēšanu
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Atspējot reāllaika datu replicēšanu
#XFLD: Message for replicate table action
replicateTableText=Tabulas replicēšana
#XFLD: Message for replicate table action
replicateTableTextNew=Datu replicēšana
#XFLD: Message to schedule task
scheduleText=Grafiks
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Skatīt pastāvīgumu
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Datu ilgtspēja
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Ielādēt jaunu momentuzņēmumu
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Sākt datu ilgtspēju
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Noņemt esošos datus
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Apstrāde pa nodalījumiem
#XBUT: Label for scheduled replication
scheduledTxt=Ieplānots
#XBUT: Label for statistics button
statisticsTxt=Statistika
#XBUT: Label for create statistics
createStatsTxt=Izveidot statistiku
#XBUT: Label for edit statistics
editStatsTxt=Rediģēt statistiku
#XBUT: Label for refresh statistics
refreshStatsTxt=Atsvaidzināt statistiku
#XBUT: Label for delete statistics
dropStatsTxt=Dzēst statistiku
#XMSG: Create statistics success message
statsSuccessTxt=Tiek sākta {0} veida statistikas izveide šim: {1}.
#XMSG: Edit statistics success message
statsEditSuccessTxt=Tiek sākta statistikas veida mainīšana uz {0} šim: {1}.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Tiek sākta statistikas atsvaidzināšana šim: {0}.
#XMSG: Drop statistics success message
statsDropSuccessTxt=Statistika ir sekmīgi izdzēsta šim: {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=Kļūda, veidojot statistiku
#XMSG: Edit statistics error message
statsEditErrorTxt=Kļūda, mainot statistiku
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Kļūda, atsvaidzinot statistiku
#XMSG: Drop statistics error message
statsDropErrorTxt=Kļūda, dzēšot statistiku
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Vai tiešām vēlaties nomest datu statistiku?
startPersistencyAdvisorLabel=Startēt skata analizatoru

#Partition related texts
#XFLD: Label for Column
column=Kolonna
#XFLD: Label for No of Partition
noOfPartitions=Nodalījumu skaits
#XFLD: Label for Column
noOfParallelProcess=Paralēlo procesu skaits
#XFLD: Label text
noOfLockedPartition=Bloķēto nodalījumu skaits
#XFLD: Label for Partition
PARTITION=Nodalījumi
#XFLD: Label for Column
AVAILABLE=Pieejams
#XFLD: Statistics Label
statsLabel=Statistika
#XFLD: Label text
COLUMN=Kolonna:
#XFLD: Label text
PARALLEL_PROCESSES=Paralēlie procesi:
#XFLD: Label text
Partition_Range=Nodalījumu diapazons
#XFLD: Label text
Name=Nosaukums
#XFLD: Label text
Locked=Bloķēts
#XFLD: Label text
Others=CITI
#XFLD: Label text
Delete=Dzēst
#XFLD: Label text
LoadData=Ielādēt atlasītos nodalījumus
#XFLD: Label text
LoadSelectedData=Ielādēt atlasītos nodalījumus
#XFLD: Confirmation text
LoadNewPersistenceConfirm=Tādējādi tiks ielādēts jauns momentuzņēmums visiem atbloķētajiem un mainītajiem nodalījumiem, ne tikai jūsu atlasītajiem. Vai vēlaties turpināt?
#XFLD: Label text
Continue=Turpināt

#XFLD: Label text
PARTITIONS=Nodalījumi
#XFLD: Label text
ADD_PARTITIONS=+ Pievienot nodalījumu
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Pievienot nodalījumu
#XFLD: Label text
deleteRange=Dzēst nodalījumu
#XFLD: Label text
LOW_PLACE_HOLDER=Ievadīt nelielu vērtību
#XFLD: Label text
HIGH_PLACE_HOLDER=Ievadīt lielu vērtību
#XFLD: tooltip text
lockedTooltip=Bloķēt nodalījumu pēc sākotnējās ielādes

#XFLD: Button text
Edit=Rediģēt
#XFLD: Button text
CANCEL=Atcelt

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Pēdējās statistikas atjaunināšana
#XFLD: Statistics Fields
STATISTICS=Statistika

#XFLD:Retry label
TEXT_Retry=Mēģināt vēlreiz
#XFLD:Retry label
TEXT_Retry_tooltip=Mēģināt vēlreiz reāllaika replicēšanu pēc tam, kad kļūda ir novērsta.
#XFLD: text retry
Retry=Apstiprināt
#XMG: Retry confirmation text
retryConfirmationTxt=Pēdējā reāllaika replicēšana tika pārtraukta ar kļūdu.\n Apstipriniet, ka kļūda ir izlabota un ka reāllaika replicēšanu var sākt vēlreiz.
#XMG: Retry success text
retrySuccess=Atkārtotas mēģināšanas process ir sekmīgi iniciēts.
#XMG: Retry fail text
retryFail=Atkārtotas mēģināšanas process neizdevās.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Izveidot statistiku
#XMSG: activity message for edit statistics
DROP_STATISTICS=Dzēst statistiku
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Atsvaidzināt statistiku
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Rediģēt statistiku
#XMSG: Task log message started task
taskStarted=Uzdevums {0} ir sākts.
#XMSG: Task log message for finished task
taskFinished=Uzdevums {0} beidzās ar statusu {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=Uzdevums {0} beidzās plkst. {2} ar statusu {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=Uzdevumam {0} ir ievades parametri.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=Uzdevums {0} beidzās ar neparedzētu kļūdu. Uzdevuma statuss ir iestatīts kā {1}.
#XMSG: Task log message for failed task
failedToEnd=Neizdevās iestatīt statusu kā {0}, vai neizdevās noņemt bloķēšanu.
#XMSG: Task log message
lockNotFound=Mēs nevaram pabeigt procesu, jo trūkst bloķēšanas: uzdevums, iespējams, ir atcelts.
#XMSG: Task log message failed task
failedOverwrite=Uzdevumu {0} jau ir bloķējis {1}. Tāpēc tas neizdevās ar šādu kļūdu: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Šī uzdevuma bloķēšanu pārņēma cits uzdevums.
#XMSG: Task log message failed takeover
failedTakeover=Neizdevās pārņemt esošo uzdevumu.
#XMSG: Task log message successful takeover
successTakeover=Atlikusī bloķēšana ir jāatbrīvo. Šim uzdevumam ir iestatīta jauna bloķēšana.
#XMSG: Tasklog Dialog Details
txtDetails=Izpildes laikā apstrādātos attālos priekšrakstus var attēlot, atverot attālo vaicājumu pārraugu - nodalījumiem paredzēto ziņojumu detalizētajā informācijā.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=Attālie SQL priekšraksti datubāzē ir izdzēsti, tos nevar attēlot.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Attālos vaicājumus, kuriem ir citām vietām piešķirti savienojumi, nevar attēlot. Dodieties uz attālo vaicājumu pārraugu un lietojiet priekšraksta ID, lai tos atfiltrētu.
#XMSG: Task log message for parallel check error
parallelCheckError=Šo uzdevumu nevar apstrādāt, jo tiek izpildīts cits uzdevums un tas jau bloķē šo uzdevumu.
#XMSG: Task log message for parallel running task
parallelTaskRunning=Konfliktējošais uzdevums jau tiek izpildīts.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Statuss {0} izpilde laikā ar korelācijas ID {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=Piešķirtajam lietotājam nav privilēģiju, kas nepieciešamas šī uzdevuma izpildei.

#XBUT: Label for open in Editor
openInEditor=Atvērt redaktorā
#XBUT: Label for open in Editor
openInEditorNew=Atvērt datu veidotājā
#XFLD:Run deails label
runDetails=Detalizēta informācija par izpildi
#XFLD: Label for Logs
Logs=Žurnāli
#XFLD: Label for Settings
Settings=Iestatījumi
#XFLD: Label for Save button
Save=Saglabāt
#XFLD: Label for Standard
Standard_PO=Optimizēts veiktspējai (ieteicams)
#XFLD: Label for Hana low memory processing
HLMP_MO=Optimizēts atmiņai
#XFLD: Label for execution mode
ExecutionMode=Izpildes režīms
#XFLD: Label for job execution
jobExecution=Apstrādes režīms
#XFLD: Label for Synchronous
syncExec=Sinhroni
#XFLD: Label for Asynchronous
asyncExec=Asinhroni
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Izmantot noklusējumu (asinhroni, var mainīties nākotnē)
#XMSG: Save settings success
saveSettingsSuccess=SAP HANA izpildes režīms ir mainīts.
#XMSG: Save settings failure
saveSettingsFailed=SAP HANA izpildes režīma maiņa neizdevās.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Darba izpilde ir mainīta.
#XMSG: Job Execution change failed
jobExecSettingFailed=Darba izpildes maiņa neizdevās.
#XMSG: Text for Type
typeTxt=Tips
#XMSG: Text for Monitor
monitorTxt=Pārraudzīt
#XMSG: Text for activity
activityTxt=Darbība
#XMSG: Text for metrics
metricsTxt=Metrika
#XTXT: Text for Task chain key
TASK_CHAINS=Uzdevumu ķēde
#XTXT: Text for View Key
VIEWS=Skats
#XTXT: Text for remote table key
REMOTE_TABLES=Attālā tabula
#XTXT: Text for Space key
SPACE=Vieta
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Elastīgās aprēķināšanas mezgls
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Replicēšanas plūsma
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Intelektiskā uzmeklēšana
#XTXT: Text for Local Table
LOCAL_TABLE=Lokālā tabula
#XTXT: Text for Data flow key
DATA_FLOWS=Datu plūsma
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=SQL skripta procedūra
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=BW procesu ķēde
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Skatīt pārraugā
#XTXT: Task List header text
taskListHeader=Uzdevumu saraksts ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Datu plūsmas vēsturisko izpilžu metriku nevar izgūt.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=Pabeigtas izpildes detalizētā informācija pašlaik netiek ielādēta. Mēģiniet atsvaidzināt.
#XFLD: Label text for the Metrices table header
metricesColLabel=Operatora etiķete
#XFLD: Label text for the Metrices table header
metricesType=Tips
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Ierakstu skaits
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Uzpildīt uzdevumu ķēdi
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Uzdevumu ķēdes izpilde ir sākta.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Uzdevumu ķēdes izpilde tika sākta šim: {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Neizdevās izpildīt uzdevumu ķēdi.
#XTXT: Execute button label
runLabel=Izpildīt
#XTXT: Execute button label
runLabelNew=Sākt izpildi
#XMSG: Filter Object header
chainsFilteredTableHeader=Filtrēts pēc objekta: {0}
#XFLD: Parent task chain label
parentChainLabel=Vecākuzdevumu ķēde:
#XFLD: Parent task chain unauthorized
Unauthorized=Nav autorizēts skatīt
#XFLD: Parent task chain label
parentTaskLabel=Vecākuzdevums:
#XTXT: Task status
NOT_TRIGGERED=Nav izraisīts
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Ieiet pilnekrāna režīmā
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Iziet no pilnekrāna režīma
#XTXT: Close Task log details right panel
closeRightColumn=Aizvērt sadaļu
#XTXT: Sort Text
sortTxt=Kārtot
#XTXT: Filter Text
filterTxt=Filtrēt
#XTXT: Filter by text to show list of filters applied
filterByTxt=Filtrēt pēc
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Vairāk nekā 5 minūtes
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Vairāk nekā 15 minūtes
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Vairāk nekā 1 stunda
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Pēdējā stunda
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Pēdējās 24 stundas
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Pēdējais mēnesis
#XTXT: Messages title text
messagesText=Ziņojumi

#XTXT Statistics information message
statisticsInfo=Statistiku nevar izveidot attālajam tabulām ar datu piekļuvi “Replicēta”. Lai izveidotu statistiku, noņemiet replicētos datus attālo tabulu detalizētās informācijas pārraugā.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Doties uz attālo tabulu detalizētās informācijas pārraugu

#XTXT: Repair latest failed run label
retryRunLabel=Vēlreiz mēģināt pēdējo izpildi
#XTXT: Repair failed run label
retryRun=Vēlreiz mēģināt izpildi
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Uzdevumu ķēdes atkārtotas mēģināšanas izpilde ir sākta.
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Uzdevumu ķēdes atkārtotas mēģināšanas izpilde neizdevās.
#XMSG: Task chain child elements name
taskChainRetryChildObject=Uzdevums {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Jauns uzdevums
#XFLD Analyzed View
analyzedView=Analizēts skats
#XFLD Metrics
Metrics=Metrika
#XFLD Partition Metrics
PartitionMetrics=Nodalījuma metrika
#XFLD Entities
Messages=Uzdevumu žurnāls
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Vēl nav definēts neviens nodalījums.
#XTXT: Description message for empty partition data
partitionEmptyDescText=Izveidojiet nodalījumus, norādot kritērijus, lai sadalītu lielākus datu apjomus mazākās, vairāk pārvaldāmās daļās.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Vēl nav pieejams neviens žurnāls
#XTXT: Description message for empty runs data
runsEmptyDescText=Ja sāksit jaunu darbību (ielādēsit jaunu momentuzņēmumu, startēsit skatu analizatoru u.c.), šeit redzēsit saistītos žurnālus.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=Elastīgās aprēķināšanas mezgla {0} konfigurācija nav atbilstoši uzturēta. Pārbaudiet savu definīciju.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Process elastīgās aprēķināšanas mezgla {0} pievienošanai neizdevās.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Ir sākta avota objekta “{0}”.“{1}” dublikāta izveide un aktivizācija elastīgajā aprēķināšanas mezglā {2}.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Ir sākta avota objekta "{0}"."{1}” dublikāta noņemšana elastīgajā aprēķināšanas mezglā {2}.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Avota objekta "{0}"."{1}” dublikāta izveide un aktivizācija neizdevās.
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Avota objekta "{0}"."{1}” dublikāta noņemšana neizdevās.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Ir sākusies vietas {0} analītisko vaicājumu skaitļošanas maršrutēšana uz elastīgās aprēķināšanas mezglu {1}.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Vietas {0} analītisku vaicājumu skaitļošanas maršrutēšana uz atbilstošu elastīgās aprēķināšanas mezglu neizdevās.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Ir sākusies vietas {0} analītisko vaicājumu skaitļošanas pārmaršrutēšana atpakaļ no elastīgās aprēķināšanas mezgla {1}.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Vietas {0} analītisko vaicājumu skaitļošanas pārmaršrutēšana atpakaļ uz koordinatoru neizdevās.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Ir izraisīta uzdevumu ķēde {0} uz atbilstošo elastīgās aprēķināšanas mezglu {1}.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Elastīgās aprēķināšanas mezgla {0} uzdevumu ķēdes ģenerēšana neizdevās.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=Ir sākusies elastīgā aprēķināšanas mezgla {0} nodrošināšana ar doto lieluma noteikšanas plānu: vCPU: {1}, atmiņa (GiB): {2} un krātuves lielums (GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=Elastīgais aprēķināšanas mezgls {0} jau ir nodrošināts.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=Elastīgajam aprēķināšanas mezglam {0} nodrošināšana jau ir atcelta.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=Darbība nav atļauta. Lūdzu, apturiet elastīgo skaitļošanas mezglu {0} un to restartējiet, lai atjauninātu lieluma mainīšanas plānu.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Process elastīgās aprēķināšanas mezgla {0} noņemšanai neizdevās.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=Pašreizējai elastīgās aprēķināšanas mezgla {0} izpildei ir iestājies taimauts.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=Elastīgās aprēķināšanas mezgla {0} statusa pārbaude ir apstrādē...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=Elastīgās aprēķināšanas mezgla {0} uzdevumu ķēdes ģenerēšana ir bloķēta, tāpēc ķēde {1} var joprojām darboties.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=Avota objekts "{0}"."{1}” ir replicēts kā skata "{2}"."{3}” atkarība.
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Nevar replicēt tabulu "{0}"."{1}”, jo rindas tabulas replicēšana ir novecojusi.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Pārsniegts maksimālais elastīgās aprēķināšanas mezglu skaits vienai SAP HANA Cloud instancei.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=Izpildes operācija elastīgās aprēķināšanas mezglam {0} joprojām notiek.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Tehnisku problēmu dēļ dublikāta dzēšana avota tabulai {0} ir apturēta. Lūdzu, vēlāk mēģiniet vēlreiz.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=Tehnisku problēmu dēļ dublikāta veidošana avota tabulai {0} ir apturēta. Lūdzu, vēlāk mēģiniet vēlreiz.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Nevar sākt elastīgās aprēķināšanas mezglu, jo ir sasniegts lietojuma limits vai vēl nav piešķirta neviens skaitļošanas blokstunda.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Notiek uzdevumu ķēdes ielāde un sagatavošanās kopā {0} uzdevumu izpildei, kas ir daļa no šīs ķēdes.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Konfliktējošais uzdevums jau tiek izpildīts
#XMSG: Replication will change
txt_replication_change=Replicēšanas tips tiks mainīts.
txt_repl_viewdetails=Skatīt detalizētu informāciju

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Šķiet, ka radusies kļūda ar pēdējo izpildi, jo iepriekšējā uzdevuma izpilde neizdevās pirms plāna ģenerēšanas.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=Vieta “{0}” ir bloķēta.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=Aktivitātei {0} ir nepieciešams, lai būtu izvietota lokālā tabula {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=Aktivitātei {0} ir nepieciešams, lai “Delta tveršana” būtu ieslēgta lokālajai tabulai {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=Aktivitātei {0} ir nepieciešams, lai lokālā tabula {1} glabātu datus objektu krātuvē.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=Aktivitātei {0} ir nepieciešams, lai lokālā tabula {1} glabātu datus datu bāzē.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Sāk noņemt izdzēstos ierakstus lokālajai tabulai {0}.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Sāk dzēst visus ierakstus lokālajai tabulai {0}.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Sāk dzēst ierakstus lokālajai tabulai {0} saskaņā ar filtra nosacījumu {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Noņemot izdzēstos ierakstus lokālajai tabulai {0}, radās kļūda.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Dzēšot visus ierakstus lokālajai tabulai {0}, radās kļūda.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Dzēš visus pilnībā apstrādātos ierakstus ar izmaiņu tipu “Dzēsts”, kas ir vecāki par {0} dienām.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Tiek dzēsti visi pilnīgi apstrādātie ieraksti ar izmaiņu tipu "Dzēsts", kuri ir vecāki par {0}.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} ieraksti ir izdzēsti.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} ieraksti ir atzīmēti dzēšanai.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=Izdzēsto ierakstu noņemšana lokālajai tabulai "{0}" ir pabeigta.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Visu ierakstu dzēšana lokālajai tabulai "{0}" ir pabeigta.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Sāk atzīmēt ierakstus kā “Izdzēsts” lokālajai tabulai {0}.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Sāk atzīmēt ierakstus kā “Izdzēsts” lokālajai tabulai {0} saskaņā ar filtra nosacījumu {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Ierakstu atzīmēšana kā “Izdzēsts” lokālajai tabulai {0} ir pabeigta.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Datu izmaiņas pašlaik tiek ielādētas tabulā {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Datu izmaiņas pašlaik ir ielādētas tabulā {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Datu izmaiņas ir apstrādātas un izdzēstas no tabulas {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Detalizēta informācija par savienojumu.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Sāciet lokālās tabulas (faila) optimizēšanu.
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Optimizējot lokālo tabulu (failu), radās kļūda. 
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Radās kļūda. Lokālās tabulas (faila) optimizācija tika apturēta. 
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Lokālās tabulas (faila) optimizācija...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=Lokālā tabula (fails) tika optimizēta.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=Lokālā tabula (fails) ir optimizēta.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=Tabula ir optimizēta ar Z secības kolonnām: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Radās kļūda. Lokālās tabulas (faila) saīsināšana tika apturēta. 
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Lokālās tabulas (faila) saīsināšana...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=Ienākošā bufera atrašanās vieta lokālajai tabulai (failam) ir atmesta.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Notiek lokālās tabulas (faila) tīrīšana (izdzēsti visi pilnībā apstrādātie ieraksti).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Iztīrot lokālo tabulu (failu), radās kļūda. 
#XMSG: Task log message
LTF_VACUUM_STOPPED=Radās kļūda. Lokālās tabulas (faila) tīrīšana tika apturēta.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Notiek lokālās tabulas (faila) tīrīšana...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=Tīrīšana ir pabeigta.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Tiek dzēsti visi pilnīgi apstrādātie ieraksti, kuri ir vecāki par {0} dienām.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Tiek dzēsti visi pilnīgi apstrādātie ieraksti, kuri ir vecāki par {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Tiek sākta jaunu ierakstu sapludināšana ar lokālo tabulu (failu).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Tiek sākta jaunu ierakstu sapludināšana ar lokālo tabulu (failu), izmantojot iestatījumu "Sapludināt datus automātiski".
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Tiek sākta jaunu ierakstu sapludināšana ar lokālo tabulu (failu). Šis uzdevums tika uzsākts, izmantojot API Merge Request.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Jaunu ierakstu sapludināšana ar lokālo tabulu (failu).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Sapludinot jaunus ierakstus ar lokālo tabulu (failu), radās kļūda. 
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=Lokālā tabula (fails) ir sapludināta.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Radās kļūda. Lokālās tabulas (faila) sapludināšana tika apturēta. 
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=Lokālās tabulas (faila) sapludināšana neizdevās kļūdas dēļ, taču operācija bija daļēji sekmīga un noteikti dati jau ir sapludināti.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Radās taimauta kļūda. Darbības {0} izpilde notiek {1} stundas.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=Asinhrono izpildi nevarēja sākt augstas sistēmas noslodzes dēļ. Atveriet sadaļu “Sistēmas pārraugs” un pārbaudiet izpildē esošos uzdevumus.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=Asinhronā izpilde tika atcelta.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION={0} uzdevuma izpilde iekļāvās atmiņas robežās no {1} līdz {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID={0} uzdevuma izpilde ar resursa ID {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=Atrašana un aizstāšana ir sākusies lokālajā tabulā (failā).
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Atrašana un aizstāšana ir pabeigta lokālajā tabulā (failā).
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Atrašana un aizstāšana neizdevās lokālajā tabulā (failā).

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Radās kļūda. Statistikas atjaunināšana lokālajai tabulai (failam) ir apturēta.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=Uzdevums neizdevās, jo SAP HANA datu bāzē radās vietas atmiņā trūkuma kļūda.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=Uzdevums neizdevās SAP HANA ieejas vadības noraidījuma dēļ.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=Uzdevums neizdevās, jo bija pārāk daudz aktīvu SAP HANA savienojumu.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=Operāciju “Mēģināt vēlreiz” nevarēja veikt, jo atkārtoti mēģinājumi ir atļauti tikai ligzdotu uzdevumu ķēdes vecākelementam.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Neizdevušos bērnobjektu uzdevumu žurnāli vairs nav pieejami, lai turpinātu atkārtotu mēģināšanu.


####Metrics Labels

performanceOptimized=Optimizēts veiktspējai
memoryOptimized=Optimizēts atmiņai

JOB_EXECUTION=Darba izpilde
EXECUTION_MODE=Izpildes režīms
NUMBER_OF_RECORDS_OVERALL=Pastāvīgo ierakstu skaits
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=No attālā avota nolasīto ierakstu skaits
RUNTIME_MS_REMOTE_EXECUTION_TIME=Attālā avota apstrādāšanas laiks
MEMORY_CONSUMPTION_GIB=SAP HANA maksimālā atmiņa
NUMBER_OF_PARTITIONS=Nodalījumu skaits
MEMORY_CONSUMPTION_GIB_OVERALL=SAP HANA maksimālā atmiņa
NUMBER_OF_PARTITIONS_LOCKED=Bloķēto nodalījumu skaits
PARTITIONING_COLUMN=Nodalīšanas kolonna
HANA_PEAK_CPU_TIME=SAP HANA kopējais CPU laiks
USED_IN_DISK=Izmantotā krātuve
INPUT_PARAMETER_PARAMETER_VALUE=Ievades parametrs
INPUT_PARAMETER=Ievades parametrs
ECN_ID=Elastīgās aprēķināšanas mezgla nosaukums

DAC=Datu piekļuves vadības
YES=Jā
NO=Nē
noofrecords=Ierakstu skaits
partitionpeakmemory=SAP HANA maksimālā atmiņa
value=Vērtība
metricsTitle=Metrika ({0})
partitionmetricsTitle=Nodalījumi ({0})
partitionLabel=Nodalījums
OthersNotNull=Vērtības, kas nav iekļautas diapazonos
OthersNull=Null vērtības
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Pēdējai datu pastāvīguma izpildei izmantotie iestatījumi:
#XMSG: Message for input parameter name
inputParameterLabel=Ievades parametrs
#XMSG: Message for input parameter value
inputParameterValueLabel=Vērtība
#XMSG: Message for persisted data
inputParameterPersistedLabel=Padarīts pastāvīgs
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Dzēst datus
REMOVE_DELETED_RECORDS=Noņemt dzēstos ierakstus
MERGE_FILES=Sapludināt failus
OPTIMIZE_FILES=Optimizēt failus
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Skatīt SAP BW tilta pārraugā

ANALYZE_PERFORMANCE=Analizēt veiktspēju
CANCEL_ANALYZE_PERFORMANCE=Atcelt veiktspējas analizēšanu

#XFLD: Label for frequency column
everyLabel=Ik pēc
#XFLD: Plural Recurrence text for Hour
hoursLabel=Stundām
#XFLD: Plural Recurrence text for Day
daysLabel=Dienām
#XFLD: Plural Recurrence text for Month
monthsLabel=Mēnešiem
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minūtēm

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Skatīt pastāvīguma problēmu novēršanas rokasgrāmatu</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Procesam nepietiek atmiņas. Nevar nodrošināt pastāvīgus datus skatam "{0}". Papildinformāciju par kļūdām, kas saistītas ar nepietiekamu atmiņu, skatiet palīdzības portālā. apskatiet skatu analizētāju, lai analizētu atmiņas patēriņa sarežģītības skatu.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Nav attiecināms
OPEN_BRACKET=(
CLOSE_BRACKET=)
