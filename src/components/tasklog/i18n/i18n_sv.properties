
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Protokolldetaljer
#XFLD: Header
TASK_LOGS=Uppgiftsprotokoll ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Körningar ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Visa detaljer
#XFLD: Button text
STOP=Stoppa körning
#XFLD: Label text
RUN_START=Start för senaste körning
#XFLD: Label text
RUN_END=Slut på senaste körning
#XFLD: Label text
RUNTIME=Tidslängd
#XTIT: Count for Messages
txtDetailMessages=Meddelanden ({0})
#XFLD: Label text
TIME=Tidsstämpel
#XFLD: Label text
MESSAGE=Meddelande
#XFLD: Label text
TASK_STATUS=Kategori
#XFLD: Label text
TASK_ACTIVITY=Aktivitet
#XFLD: Label text
RUN_START_DETAILS=Start
#XFLD: Label text
RUN_END_DETAILS=Slut
#XFLD: Label text
LOGS=Körningar
#XFLD: Label text
STATUS=Status
#XFLD: Label text
RUN_STATUS=Körningsstatus
#XFLD: Label text
Runtime=Tidslängd
#XFLD: Label text
RuntimeTooltip=Tidslängd (hh : mm : ss)
#XFLD: Label text
TRIGGEREDBY=Initiering av
#XFLD: Label text
TRIGGEREDBYNew=Körning av
#XFLD: Label text
TRIGGEREDBYNewImp=Körning startad av
#XFLD: Label text
EXECUTIONTYPE=Körningstyp
#XFLD: Label text
EXECUTIONTYPENew=Körningstyp
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Utrymme för överordnad kedja
#XFLD: Refresh tooltip
TEXT_REFRESH=Uppdatera
#XFLD: view Details link
VIEW_ERROR_DETAILS=Visa detaljer
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Läs ned ytterligare detaljer
#XMSG: Download completed
downloadStarted=Nedläsning har startat
#XMSG: Error while downloading content
errorInDownload=Ett fel inträffade vid nedläsning.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Visa detaljer
#XBTN: cancel button of task details dialog
TXT_CANCEL=Avbryt
#XBTN: back button from task details
TXT_BACK=Tillbaka
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Uppgift slutförd
#XFLD: Log message with failed status
MSG_LOG_FAILED=Uppgift misslyckades
#XFLD: Master and detail table with no data
No_Data=Inga data
#XFLD: Retry tooltip
TEXT_RETRY=Försök igen
#XFLD: Cancel Run label
TEXT_CancelRun=Avbryt körning
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Rensa misslyckad inläsning
#XMSG:button copy sql statement
txtSQLStatement=Kopiera SQL-instruktion
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Öppna fjärrfrågemonitor
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=Visa fjärr-SQL-instruktioner genom att klicka på "Öppna fjärrfrågemonitor".
#XMSG:button ok
txtOk=OK
#XMSG: button close
txtClose=Stäng
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=Annullering av körningsåtgärd för objekt "{0}" har startat.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=Annullering av körningsåtgärd för objekt "{0}" misslyckades.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=Annullering av körningsåtgärd för objekt "{0}" är inte längre möjlig då replikeringsstatus har ändrats.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Inga uppgiftsprotokoll har status Körs.
#XMSG: message for conflicting task
Task_Already_Running=Uppgift i konflikt körs redan för objekt "{0}".
#XFLD: Label for no task log with running state title
actionInfo=Åtgärdsinformation
#XMSG Copied to clipboard
copiedToClip=Kopierad till urklipp
#XFLD copy
Copy=Kopiera
#XFLD copy correlation ID
CopyCorrelationID=Kopiera korrelations-ID
#XFLD Close
Close=Stäng
#XFLD: show more Label
txtShowMore=Visa mer
#XFLD: message Label
messageLabel=Meddelande:
#XFLD: details Label
detailsLabel=Detaljer:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Körnings-SQL-instruktion:
#XFLD:statementId Label
statementIdLabel=Instruktions-ID:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Antal fjärr-SQL-instruktioner:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Antal instruktioner
#XFLD: Space Label
txtSpaces=Utrymme
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Utrymmen utan behörighet
#XFLD: Privilege Error Text
txtPrivilegeError=Du har inte tillräcklig behörighet för att visa dessa data.
#XFLD: Label for Object Header
DATA_ACCESS=Dataåtkomst
#XFLD: Label for Object Header
SCHEDULE=Schema
#XFLD: Label for Object Header
DETAILS=Detaljer
#XFLD: Label for Object Header
LATEST_UPDATE=Senaste uppdatering
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Senaste ändring (källa)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Uppdateringsfrekvens
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Inplanerad frekvens
#XFLD: Label for Object Header
NEXT_RUN=Nästa körning
#XFLD: Label for Object Header
CONNECTION=Anslutning
#XFLD: Label for Object Header
DP_AGENT=Datahämtningsagent
#XFLD: Label for Object Header
USED_IN_MEMORY=Minne använt för lagring (MiB)
#XFLD: Label for Object Header
USED_DISK=Disk använd för lagring (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Storlek för minnesbaserad (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Storlek på disk (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Antal poster

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Sätt till Misslyckades
SET_TO_FAILED_ERR=Den här uppgiften kördes men användaren satte uppgiftens status till MISSLYCKAD.
#XFLD: Label for stopped failed
FAILLOCKED=Körning pågår redan
#XFLD: sub status STOPPED
STOPPED=Stoppad
STOPPED_ERR=Den här uppgiften stoppades, men ingen rollback utfördes.
#XFLD: sub status CANCELLED
CANCELLED=Avbruten
CANCELLED_ERR=Den här uppgiftskörningen avbröts efter att den hade startat. I det här fallet återställdes data till den status som fanns innan uppgiftskörningen initierades.
#XFLD: sub status LOCKED
LOCKED=Spärrad
LOCKED_ERR=Samma uppgift kördes redan. Det här uppgiftsjobbet kan inte köras parallellt med utförandet av en befintlig uppgift.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Uppgiftsundantag
TASK_EXCEPTION_ERR=Ett ospecificerat fel inträffade vid utförande av den här uppgiften.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Undantag för uppgiftsutförande
TASK_EXECUTE_EXCEPTION_ERR=Ett fel inträffade vid utförande av den här uppgiften.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Obehörig
UNAUTHORIZED_ERR=Användaren kunde inte autentiseras, har blivit spärrad eller raderad.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Förbjuden
FORBIDDEN_ERR=Allokerad användare har inte behörighet att utföra den här uppgiften.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Ej initierad
FAIL_NOT_TRIGGERED_ERR=Det här uppgiftsjobbet kunde inte utföras på grund av ett systemavbrott eller att en del av databassystemet inte var tillgänglig vid tidpunkten för planerat utförande. Vänta till nästa planerade utförandetid eller omplanera jobbet.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Schema har annullerats
SCHEDULE_CANCELLED_ERR=Det här uppgiftsjobbet kunde inte utföras på grund av ett internt fel. Kontakta SAP-support och tillhandahåll korrelations-ID och tidsstämpel från protokolldetaljerna för uppgiftsjobbet.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Föregående körning pågår
SUCCESS_SKIPPED_ERR=Utförande av den här uppgiften har inte initierats eftersom en tidigare körning av samma uppgift fortfarande pågår.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Ägare saknas
FAIL_OWNER_MISSING_ERR=Det här uppgiftsjobbet kunde inte utföras eftersom det inte har någon allokerad systemanvändare. Allokera en ansvarig användare till jobbet.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Samtycke ej tillgängligt
FAIL_CONSENT_NOT_AVAILABLE_ERR=Du har inte auktoriserat SAP att köra uppgiftskedjor eller planera in dataintegreringsuppgifter i ditt ställe. Välj alternativet för att ge ditt samtycke.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Samtycke har gått ut
FAIL_CONSENT_EXPIRED_ERR=Auktoriseringen som ger SAP tillåtelse att köra uppgiftskedjor eller planera in dataintegreringsuppgifter i ditt ställe har gått ut. Välj alternativet för att förnya ditt samtycke.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Samtycke har ogiltigförklarats
FAIL_CONSENT_INVALIDATED_ERR=Den här uppgiften kunde inte utföras, vanligtvis på grund av en ändring i konfigurationen för identitetsleverantör för tenanten. I sånt fall kan inga nya uppgiftsjobb köras eller planeras in i den berörda användarens namn. Återkalla och ge samtycke på nytt för inplanering om den allokerade användaren fortfarande finns i den nya IdP. Allokera en ny ägare till uppgiftsjobb och ge det samtycke som krävs för inplanering av uppgift om den allokerade användaren inte längre finns. Se följande SAP-not för mer information: https://launchpad.support.sap.com/#/notes/3089828.
TASK_EXECUTOR_ERROR=Uppgiftsutförare
TASK_EXECUTOR_ERROR_ERR=Ett internt fel inträffade för den här uppgiften, sannolikt under förberedelsestegen för utförande, och uppgiften kunde inte startas.
PREREQ_NOT_MET=Förutsättning uppfylls inte
PREREQ_NOT_MET_ERR=Uppgiften kunde inte köras på grund av problem i dess definition. Till exempel kan objektet inte distribueras, en uppgiftskedja innehåller cirkulär logik eller en vys SQL är ogiltig.
RESOURCE_LIMIT_ERROR=Resursgränsfel
RESOURCE_LIMIT_ERROR_ERR=Det går inte att utföra uppgiften för närvarande eftersom det inte finns tillräckliga resurser eller så är de upptagna.
FAIL_CONSENT_REFUSED_BY_UMS=Samtycke har ej lämnats
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Uppgiften kunde inte utföras, varken i inplanerade körningar eller uppgiftskedjor, på grund av en ändring i användarens identitetsleverantörskonfiguration i tenanten. För mer information, se följande SAP-not: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Inplanerad
#XFLD: status text
SCHEDULEDNew=Permanent
#XFLD: status text
PAUSED=Pausad
#XFLD: status text
DIRECT=Direkt
#XFLD: status text
MANUAL=Manuell
#XFLD: status text
DIRECTNew=Enkel
#XFLD: status text
COMPLETED=Slutförd
#XFLD: status text
FAILED=Misslyckades
#XFLD: status text
RUNNING=Körs
#XFLD: status text
none=Ingen
#XFLD: status text
realtime=Realtid
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Underordnad uppgift
#XFLD: New Data available in the file
NEW_DATA=Nya data

#XFLD: text for values shown in column Replication Status
txtOff=Av
#XFLD: text for values shown in column Replication Status
txtInitializing=Initialiserar
#XFLD: text for values shown in column Replication Status
txtLoading=Läser in
#XFLD: text for values shown in column Replication Status
txtActive=Aktiv
#XFLD: text for values shown in column Replication Status
txtAvailable=Tillgänglig
#XFLD: text for values shown in column Replication Status
txtError=Fel
#XFLD: text for values shown in column Replication Status
txtPaused=Pausad
#XFLD: text for values shown in column Replication Status
txtDisconnected=Frånkopplad
#XFLD: text for partially Persisted views
partiallyPersisted=Delvis persisterade

#XFLD: activity text
REPLICATE=Replikera
#XFLD: activity text
REMOVE_REPLICATED_DATA=Ta bort replikerade data
#XFLD: activity text
DISABLE_REALTIME=Inaktivera realtidsdatareplikering
#XFLD: activity text
REMOVE_PERSISTED_DATA=Ta bort persisterade data
#XFLD: activity text
PERSIST=Persistera
#XFLD: activity text
EXECUTE=Utför
#XFLD: activity text
TASKLOG_CLEANUP=Tacklog_Cleanup
#XFLD: activity text
CANCEL_REPLICATION=Avbryt replikering
#XFLD: activity text
MODEL_IMPORT=Modellimport
#XFLD: activity text
NONE=Ingen
#XFLD: activity text
CANCEL_PERSISTENCY=Avbryt persistens
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Analysera vy
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Avbryt vyanalys

#XFLD: severity text
INFORMATION=Information
#XFLD: severity text
SUCCESS=OK
#XFLD: severity text
WARNING=Varning
#XFLD: severity text
ERROR=Fel
#XFLD: text for values shown for Ascending sort order
SortInAsc=Sortera stigande
#XFLD: text for values shown for Descending sort order
SortInDesc=Sortera fallande
#XFLD: filter text for task log columns
Filter=Filtrera
#XFLD: object text for task log columns
Object=Objekt
#XFLD: space text for task log columns
crossSpace=Utrymme

#XBUT: label for remote data access
REMOTE=Fjärr
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Replikerad (realtid)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Replikerad (snapshot)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=Realtidsreplikering har spärrats på grund av ett fel. När felet har korrigerats kan du använda åtgärden "Försök igen" för att fortsätta med realtidsreplikering.
ERROR_MSG=Realtidsreplikering har spärrats på grund av ett fel.
RETRY_FAILED_ERROR=Upprepningsförsök för process misslyckades med ett fel.
LOG_INFO_DETAILS=Inga protokoll genereras när data replikeras i realtidsläge. De protokoll som visas gäller tidigare aktiviteter.

#XBUT: Partitioning label
partitionMenuText=Partitionering
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Skapa partition
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Redigera partition
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Radera partition
#XFLD: Initial text
InitialPartitionText=Definiera partitioner genom att ange kriterier för indelning av stora dataset i mindre set.
DefinePartition=Definiera partitioner
#XFLD: Message text
partitionChangedInfo=Definition för partition har ändrats sedan senaste replikering. Ändringar tillämpas på nästa datainläsning.
#XFLD: Message text
REAL_TIME_WARNING=Partitionering tillämpas endast vid inläsning av ny snapshot. Den tillämpas inte för realtidsreplikering.
#XFLD: Message text
loadSelectedPartitions=Persistering av data har startat för valda partitioner för "{0}"
#XFLD: Message text
loadSelectedPartitionsError=Data för valda partitioner för "{0}" kunde inte persisteras
#XFLD: Message text
viewpartitionChangedInfo=Partitionsdefinitionen har ändrats sedan den senaste persistenskörningen. Nästa datainläsning blir en fullständig snapshot inklusive spärrade partioner för att tillämpa ändringarna. När den fullständiga inläsningen har slutförts kan data köras för enskilda partitioner.
#XFLD: Message text
viewpartitionChangedInfoLocked=Partitionsdefinitionen har ändrats sedan den senaste persistenskörningen. Nästa datainläsning blir en fullständig snapshot, förutom spärrade och oförändrade partitionsintervall, för att tillämpa ändringarna. När den inläsningen har slutförts kan du läsa in valda partitioner igen.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Tabellreplikering
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Replikering av schema
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Skapa schema
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Redigera schema
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Radera schema
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Läs in ny snapshot
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Starta datareplikering
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Ta bort replikerade data
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Aktivera realtidsåtkomst
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Aktivera realtidsdatareplikering
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Inaktivera realtidsdatareplikering
#XFLD: Message for replicate table action
replicateTableText=Tabellreplikering
#XFLD: Message for replicate table action
replicateTableTextNew=Datareplikering
#XFLD: Message to schedule task
scheduleText=Schema
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Vypersistens
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Datapersistens
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Läs in ny snapshot
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Starta datapersistens
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Ta bort persisterade data
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Partitionerad bearbetning
#XBUT: Label for scheduled replication
scheduledTxt=Inplanerad
#XBUT: Label for statistics button
statisticsTxt=Statistik
#XBUT: Label for create statistics
createStatsTxt=Skapa statistik
#XBUT: Label for edit statistics
editStatsTxt=Redigera statistik
#XBUT: Label for refresh statistics
refreshStatsTxt=Uppdatera statistik
#XBUT: Label for delete statistics
dropStatsTxt=Radera statistik
#XMSG: Create statistics success message
statsSuccessTxt=Uppläggning av statistik av typen {0} för {1} har startat.
#XMSG: Edit statistics success message
statsEditSuccessTxt=Statistiktyp har börjat ändras till {0} för {1}.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Statistik för {0} har börjat uppdateras.
#XMSG: Drop statistics success message
statsDropSuccessTxt=Statistik har raderats för {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=Fel vid uppläggning av statistik
#XMSG: Edit statistics error message
statsEditErrorTxt=Fel vid ändring av statistik
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Fel vid uppdatering av statistik
#XMSG: Drop statistics error message
statsDropErrorTxt=Fel vid radering av statistik
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Ska datastatistik raderas?
startPersistencyAdvisorLabel=Starta vyanalys

#Partition related texts
#XFLD: Label for Column
column=Kolumn
#XFLD: Label for No of Partition
noOfPartitions=Antal partitioner
#XFLD: Label for Column
noOfParallelProcess=Antal parallella processer
#XFLD: Label text
noOfLockedPartition=Antal spärrade partitioner
#XFLD: Label for Partition
PARTITION=Partitioner
#XFLD: Label for Column
AVAILABLE=Tillgängliga
#XFLD: Statistics Label
statsLabel=Statistik
#XFLD: Label text
COLUMN=Kolumn:
#XFLD: Label text
PARALLEL_PROCESSES=Parallella processer:
#XFLD: Label text
Partition_Range=Partitionsintervall
#XFLD: Label text
Name=Namn
#XFLD: Label text
Locked=Spärrad
#XFLD: Label text
Others=ÖVRIGA
#XFLD: Label text
Delete=Radera
#XFLD: Label text
LoadData=Läs in valda partitioner
#XFLD: Label text
LoadSelectedData=Läs in valda partitioner
#XFLD: Confirmation text
LoadNewPersistenceConfirm=Detta läser in en ny snapshot för alla partitioner med hävd spärr och ändringar, inte bara de du har valt. Vill du fortsätta?
#XFLD: Label text
Continue=Fortsätt

#XFLD: Label text
PARTITIONS=Partitioner
#XFLD: Label text
ADD_PARTITIONS=+ Lägg till partition
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Lägg till partition
#XFLD: Label text
deleteRange=Radera partition
#XFLD: Label text
LOW_PLACE_HOLDER=Ange lågt värde
#XFLD: Label text
HIGH_PLACE_HOLDER=Ange högt värde
#XFLD: tooltip text
lockedTooltip=Spärra partition efter initial dataöverföring

#XFLD: Button text
Edit=Redigera
#XFLD: Button text
CANCEL=Avbryt

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Senaste statistikuppdatering
#XFLD: Statistics Fields
STATISTICS=Statistik

#XFLD:Retry label
TEXT_Retry=Försök igen
#XFLD:Retry label
TEXT_Retry_tooltip=Försök igen med realtidsreplikering när felet har åtgärdats.
#XFLD: text retry
Retry=Bekräfta
#XMG: Retry confirmation text
retryConfirmationTxt=Den senaste realtidsreplikeringen avslutades med ett fel.\n Bekräfta att felet har korrigerats och att realtidsreplikering kan startas om.
#XMG: Retry success text
retrySuccess=Upprepningsförsök för process har initierats.
#XMG: Retry fail text
retryFail=Upprepningsförsök för process misslyckades.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Skapa statistik
#XMSG: activity message for edit statistics
DROP_STATISTICS=Radera statistik
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Uppdatera statistik
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Redigera statistik
#XMSG: Task log message started task
taskStarted=Uppgift {0} har startat.
#XMSG: Task log message for finished task
taskFinished=Uppgift {0} avslutades med status {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=Uppgift {0} avslutades kl. {2} med status {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=Uppgift ({0}) har inparametrar.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=Uppgift {0} avslutades med ett oväntat fel. Uppgiftsstatus har satts till {1}.
#XMSG: Task log message for failed task
failedToEnd=Status kunde inte sättas till {0} eller spärr kunde inte tas bort.
#XMSG: Task log message
lockNotFound=Process kan inte slutföras eftersom spärr saknas: Uppgift kan ha avbrutits.
#XMSG: Task log message failed task
failedOverwrite=Uppgift {0} har redan spärrats av {1}. Därför misslyckades den med följande fel: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Spärr av denna uppgift har övertagits av en annan uppgift.
#XMSG: Task log message failed takeover
failedTakeover=En befintlig uppgift kunde inte övertas.
#XMSG: Task log message successful takeover
successTakeover=Kvarlämnad spärr måste frisläppas. Ny spärr för denna uppgift har ställs in.
#XMSG: Tasklog Dialog Details
txtDetails=Fjärrinstruktionerna som bearbetas under körningen kan visas genom att öppna fjärrfrågemonitorn i detaljerna för de partitionsspecifika meddelandena.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=Fjärr-SQL-instruktioner har raderats från databasen och kan inte visas.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Fjärrfrågor som har anslutningar allokerade till andra utrymmen kan inte visas. Gå till Fjärrfrågemonitor och använd instruktions-ID för att filtrera dem.
#XMSG: Task log message for parallel check error
parallelCheckError=Uppgift kan inte bearbetas eftersom en annan uppgift körs och redan spärrar denna uppgift.
#XMSG: Task log message for parallel running task
parallelTaskRunning=Uppgift i konflikt körs redan.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} vid körning med korrelations-ID {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=Allokerad användare har inte behörighet att utföra den här uppgiften.

#XBUT: Label for open in Editor
openInEditor=Öppna i editor
#XBUT: Label for open in Editor
openInEditorNew=Öppna i Data Builder
#XFLD:Run deails label
runDetails=Körningsdetaljer
#XFLD: Label for Logs
Logs=Protokoll
#XFLD: Label for Settings
Settings=Inställningar
#XFLD: Label for Save button
Save=Spara
#XFLD: Label for Standard
Standard_PO=Prestandaoptimerad (rekommenderas)
#XFLD: Label for Hana low memory processing
HLMP_MO=Minnesoptimerad
#XFLD: Label for execution mode
ExecutionMode=Körningsläge
#XFLD: Label for job execution
jobExecution=Bearbetningsläge
#XFLD: Label for Synchronous
syncExec=Synkron
#XFLD: Label for Asynchronous
asyncExec=Asynkron
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Använd standard (Asynkron, kan ändras i framtiden)
#XMSG: Save settings success
saveSettingsSuccess=Körningsläge för SAP HANA har ändrats.
#XMSG: Save settings failure
saveSettingsFailed=Körningsläge för SAP HANA kunde inte ändras.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Jobbutförande har ändrats.
#XMSG: Job Execution change failed
jobExecSettingFailed=Jobbutförande kunde inte ändras.
#XMSG: Text for Type
typeTxt=Typ
#XMSG: Text for Monitor
monitorTxt=Monitor
#XMSG: Text for activity
activityTxt=Aktivitet
#XMSG: Text for metrics
metricsTxt=Mätetal
#XTXT: Text for Task chain key
TASK_CHAINS=Uppgiftskedja
#XTXT: Text for View Key
VIEWS=Vy
#XTXT: Text for remote table key
REMOTE_TABLES=Fjärrtabell
#XTXT: Text for Space key
SPACE=Utrymme
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Elastisk beräkningsnod
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Replikeringsflöde
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Intelligent sökning
#XTXT: Text for Local Table
LOCAL_TABLE=Lokal tabell
#XTXT: Text for Data flow key
DATA_FLOWS=Dataflöde
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=SQL-skriptprocedur
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=BW-processkedja
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Visa i monitor
#XTXT: Task List header text
taskListHeader=Uppgiftslista ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Mätetal för tidigare körningar av dataflöde kan inte hämtas.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=Detalj för fullständig körning läses inte in. Pröva att uppdatera.
#XFLD: Label text for the Metrices table header
metricesColLabel=Operatoretikett
#XFLD: Label text for the Metrices table header
metricesType=Typ
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Antal poster
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Kör uppgiftskedja
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Körning av uppgiftskedja har startat.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Körning av uppgiftskedja har startat för {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Uppgiftskedja kunde inte köras.
#XTXT: Execute button label
runLabel=Kör
#XTXT: Execute button label
runLabelNew=Starta körning
#XMSG: Filter Object header
chainsFilteredTableHeader=Filtrering efter objekt: {0}
#XFLD: Parent task chain label
parentChainLabel=Överordnad uppgiftskedja:
#XFLD: Parent task chain unauthorized
Unauthorized=Ej behörig att visa
#XFLD: Parent task chain label
parentTaskLabel=Överordnad uppgift:
#XTXT: Task status
NOT_TRIGGERED=Ej initierad
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Helskärmsläge
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Avsluta helskärmsläge
#XTXT: Close Task log details right panel
closeRightColumn=Stäng avsnitt
#XTXT: Sort Text
sortTxt=Sortera
#XTXT: Filter Text
filterTxt=Filtrera
#XTXT: Filter by text to show list of filters applied
filterByTxt=Filtrera efter
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Mer än 5 minuter
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Mer än 15 minuter
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Mer än 1 timme
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Senaste timmen
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Senaste 24 timmarna
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Senaste månaden
#XTXT: Messages title text
messagesText=Meddelanden

#XTXT Statistics information message
statisticsInfo=Det går inte att skapa statistik för fjärrtabeller med dataåtkomst "Replikerad". Ta bort replikerade data i detaljmonitor för fjärrtabeller för att skapa statistik.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Gå till detaljmonitor för fjärrtabell

#XTXT: Repair latest failed run label
retryRunLabel=Upprepa senaste körning
#XTXT: Repair failed run label
retryRun=Upprepa körning
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Upprepning av körning av uppgiftskedja har startat.
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Upprepning av körning av uppgiftskedja har misslyckats.
#XMSG: Task chain child elements name
taskChainRetryChildObject=Uppgift {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Ny uppgift
#XFLD Analyzed View
analyzedView=Analyserad vy
#XFLD Metrics
Metrics=Mätetal
#XFLD Partition Metrics
PartitionMetrics=Partitionsmätetal
#XFLD Entities
Messages=Uppgiftsprotokoll
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Inga partitioner har definierats ännu
#XTXT: Description message for empty partition data
partitionEmptyDescText=Skapa partitioner genom att ange kriterier för att dela in stora datavolymer i mindre, mer lätthanterliga bitar.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Inga protokoll finns tillgängliga än
#XTXT: Description message for empty runs data
runsEmptyDescText=När du startar en ny aktivitet (läser in en ny snapshot, startar vyanalys...) ser du relaterade protokoll här.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=Konfiguration för elastisk beräkningsnod {0} underhålls inte. Kontrollera din definition.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Process för att lägga till elastisk beräkningsnod {0} misslyckades.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Uppläggning och aktivering av replikering för källobjekt "{0}"."{1}" i elastisk beräkningsnod {2} har startat.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Borttagning av replikering för källobjekt "{0}"."{1}" i elastisk beräkningsnod {2} har startat.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Uppläggning och aktivering av replikering för källobjekt "{0}"."{1}" misslyckades.
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Borttagning av replikering för källobjekt "{0}"."{1}" misslyckades.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Dirigering av beräkning av analytiska queries för utrymme {0} till elastisk beräkningsnod {1} har startat.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Dirigering av beräkning av analytiska queries för utrymme {0} till motsvarande elastisk beräkningsnod misslyckades.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Omdirigering av beräkning av analytiska queries för utrymme {0} tillbaka från elastisk beräkningsnod {1} har startat.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Omdirigering av beräkning av analytiska queries för utrymme {0} tillbaka till koordinator misslyckades.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Uppgiftskedja {0} till motsvarande elastisk beräkningsnod {1} har initierats.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Uppgiftskedja kunde inte genereras för elastisk beräkningsnod {0}.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=Datahämtning för elastisk beräkningsnod {0} har startat med angiven dimensioneringsplan: vCPU: {1}, minne (GiB): {2} och lagringsstorlek (GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=Elastisk beräkningsnod {0} har redan tillhandahållits.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=Tillhandahållande av elastisk beräkningsnod {0} har redan ångrats.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=Operation tillåts inte. Stoppa elastisk beräkningsnod {0} och starta om den för att uppdatera dimensioneringsplan.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Process för att ta bort elastisk beräkningsnod {0} misslyckades.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=Timeout för aktuell körning för elastisk beräkningsnod {0}.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=Status för elastisk beräkningsnod {0} kontrolleras...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=Generering av uppgiftskedja för elastisk beräkningsnod {0} spärras. Kedja {1} kanske därför fortfarande körs.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=Källobjekt "{0}"."{1}" replikeras som beroende för vy "{2}"."{3}".
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Tabell "{0}"."{1}" kunde inte replikeras eftersom replikering av radtabell är inaktuell.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Maximum för elastisk beräkningsnod per SAP HANA Cloud-instans har överskridits.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=Operation som körs för elastisk beräkningsnod {0} pågår fortfarande.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=På grund av tekniska problem har radering av replikering av källtabell {0} stoppats. Försök igen senare.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=På grund av tekniska problem har uppläggning av replikering av källtabell {0} stoppats. Försök igen senare.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Det går inte att starta en elastisk beräkningsnod på grund av att användningsgränsen har uppnåtts eller inga beräkningsblocktimmar har allokerats än.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Läser in uppgiftskedja och förbereder körning av totalt {0} uppgifter som ingår i denna kedja.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Uppgift i konflikt körs redan
#XMSG: Replication will change
txt_replication_change=Replikeringstyp ändras.
txt_repl_viewdetails=Visa detaljer

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Ett fel ser ut att ha inträffat med att upprepa den senaste körningen eftersom den tidigare uppgiftskörningen misslyckades innan planen kunde genereras.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=Utrymme "{0}" är spärrat.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=Aktivitet {0} kräver att lokal tabell {1} distribueras.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=Aktivitet {0} kräver att deltaregistrering aktiveras för lokal tabell {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=Aktivitet {0} kräver att lokal tabell {1} lagrar data i objektlagring.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=Aktivitet {0} kräver att lokal tabell {1} lagrar data i databasen.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Börjar ta bort raderade poster för lokal tabell {0}.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Börjar radera alla poster för lokal tabell {0}.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Börjar radera alla poster för lokal tabell {0} enligt filtervillkor {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Ett fel inträffade vid borttagning av raderade poster för lokal tabell {0}.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Ett fel inträffade vid radering av alla poster för lokal tabell {0}.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Raderar alla fullständigt bearbetade poster med ändringstyp "Raderad" som är äldre än {0} dagar.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Raderar alla fullständigt bearbetade poster med ändringstyp "Raderad" som är äldre än {0}.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} poster har raderats.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} poster har markerats för radering.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=Borttagning av raderade poster för lokal tabell {0} har slutförts.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Radering av alla poster för lokal tabell {0} har slutförts.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Börjar markera alla poster som "Raderad" för lokal tabell {0}.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Börjar markera alla poster som "Raderad" för lokal tabell {0} enligt filtervillkor {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Markering av poster som "Raderad" för lokal tabell {0} har slutförts.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Dataändringar läses tillfälligt in till tabell {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Dataändringar har tillfälligt lästs in till tabell {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Dataändringar bearbetas och raderas från tabell {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Anslutningsdetaljer.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Börjar optimera lokal tabell (fil).
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Ett fel uppstod vid optimering av lokal tabell (fil).
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Ett fel uppstod. Optimering av lokal tabell (fil) har stoppats.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Optimerar lokal tabell (fil)...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=Lokal tabell (fil) har optimerats.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=Lokal tabell (fil) är optimerad.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=Tabellen har optimerats med Z-ordningskolumner: {0}

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Ett fel uppstod. Trunkering av lokal tabell (fil) har stoppats.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Trunkerar lokal tabell (fil)...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=Inkommande buffertplats för lokal tabell (fil) har raderats.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Börjar dammsuga (radera alla fullständigt bearbetade poster) i lokal tabell (fil).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Ett fel uppstod vid dammsugning av lokal tabell (fil).
#XMSG: Task log message
LTF_VACUUM_STOPPED=Ett fel uppstod. Dammsugning av lokal tabell (fil) har stoppats.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Dammsuger lokal tabell (fil)...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=Dammsugning slutförd.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Raderar alla fullständigt bearbetade poster som är äldre än {0} dagar.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Raderar alla fullständigt bearbetade poster som är äldre än {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Börjar slå samman nya poster med lokal tabell (fil).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Startar sammanslagning av nya poster med lokal tabell (fil) med inställningen "Slå samman data automatiskt".
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Startar sammanslagning av nya poster med lokal tabell (fil). Uppgiften initierades via API-sammanslagningsbegäran.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Slår samman nya poster med lokal tabell (fil).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Ett fel uppstod vid sammanslagning av nya poster med lokal tabell (fil).
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=Lokal tabell (fil) har slagits samman.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Ett fel uppstod. Sammanslagning av lokal tabell (fil) har stoppats.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=Sammanslagning av lokal tabell (fil) har misslyckats på grund av ett fel, men operationen lyckades delvis och några data har redan slagits samman.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Ett timeout-fel inträffade. Aktiviteten {0} har körts i {1} timmar.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=Asynkront utförande kunde inte startas på grund av hög systembelastning. Öppna "Systemmonitor" och kontrollera uppgifterna som körs.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=Asynkront utförande har avbrutits.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=Uppgift {0} kördes inom minnesbegränsningarna för {1} och {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID={0}-uppgiften kördes med resurs-ID {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=Sök och ersätt har startats i lokal tabell (fil).
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Sök och ersätt har slutförts i lokal tabell (fil).
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Sök och ersätt har misslyckats i lokal tabell (fil).

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Ett fel uppstod. Uppdatering av statistik för lokal tabell (fil) har stoppats.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=Uppgiften misslyckades på grund av ett minnesbristfel i SAP HANA-databasen.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=Uppgiften misslyckades på grund av avvisning av åtkomstkontroll för SAP HANA.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=Uppgiften misslyckades på grund av för många aktiva SAP HANA-anslutningar.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=Operationen Försök igen kunde inte utföras eftersom nya försök endast tillåts i överordnat element för en kapslad uppgiftskedja.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Protokoll för misslyckade underordnade uppgifter är inte längre tillgängliga för upprepningsförsök för att fortsätta.


####Metrics Labels

performanceOptimized=Prestandaoptimerad
memoryOptimized=Minnesoptimerad

JOB_EXECUTION=Jobbutförande
EXECUTION_MODE=Körningsläge
NUMBER_OF_RECORDS_OVERALL=Antal persisterade poster
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Antal poster lästa från fjärrkälla
RUNTIME_MS_REMOTE_EXECUTION_TIME=Bearbetningstid för fjärrkälla
MEMORY_CONSUMPTION_GIB=Maximalt minne för SAP HANA
NUMBER_OF_PARTITIONS=Antal partitioner
MEMORY_CONSUMPTION_GIB_OVERALL=Maximalt minne för SAP HANA
NUMBER_OF_PARTITIONS_LOCKED=Antal spärrade partitioner
PARTITIONING_COLUMN=Partitioneringskolumn
HANA_PEAK_CPU_TIME=Total CPU-tid för SAP HANA
USED_IN_DISK=Använt lagringsutrymme
INPUT_PARAMETER_PARAMETER_VALUE=Inparameter
INPUT_PARAMETER=Inparameter
ECN_ID=Namn på elastisk beräkningsnod

DAC=Dataåtkomstkontroller
YES=Ja
NO=Nej
noofrecords=Antal poster
partitionpeakmemory=Maximalt minne för SAP HANA
value=Värde
metricsTitle=Mätetal ({0})
partitionmetricsTitle=Partitioner ({0})
partitionLabel=Partition
OthersNotNull=Värden ej inkluderade i intervall
OthersNull=Null-värden
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Inställningar som användes för senaste datapersistenskörning:
#XMSG: Message for input parameter name
inputParameterLabel=Inparameter
#XMSG: Message for input parameter value
inputParameterValueLabel=Värde
#XMSG: Message for persisted data
inputParameterPersistedLabel=Persisterad kl.
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Radera data
REMOVE_DELETED_RECORDS=Ta bort raderade poster
MERGE_FILES=Slå samman filer
OPTIMIZE_FILES=Optimera filer
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Visa i SAP BW Bridge Monitor

ANALYZE_PERFORMANCE=Analysera prestanda
CANCEL_ANALYZE_PERFORMANCE=Avbryt analysering av prestanda

#XFLD: Label for frequency column
everyLabel=Varje
#XFLD: Plural Recurrence text for Hour
hoursLabel=Timmar
#XFLD: Plural Recurrence text for Day
daysLabel=Dagar
#XFLD: Plural Recurrence text for Month
monthsLabel=Månader
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuter

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Visa felsökningsguide för persistens</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Slut på minne för process. Data kunde inte persisteras för vy "{0}". Gå till Help Portal för mer information om minnesbristfel. Överväg att kontrollera Vyanalys för att analysera vyn för minnesförbrukningskomplexitet.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Ej tillämpligt
OPEN_BRACKET=(
CLOSE_BRACKET=)
