
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Detalhes de log
#XFLD: Header
TASK_LOGS=Logs de tarefas ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Execuções ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Exibir detalhes
#XFLD: Button text
STOP=Interromper execução
#XFLD: Label text
RUN_START=Início da última execução
#XFLD: Label text
RUN_END=Término da última execução
#XFLD: Label text
RUNTIME=Duração
#XTIT: Count for Messages
txtDetailMessages=Mensagens ({0})
#XFLD: Label text
TIME=Registro da hora
#XFLD: Label text
MESSAGE=Mensagem
#XFLD: Label text
TASK_STATUS=Categoria
#XFLD: Label text
TASK_ACTIVITY=Atividade
#XFLD: Label text
RUN_START_DETAILS=Início
#XFLD: Label text
RUN_END_DETAILS=Término
#XFLD: Label text
LOGS=Execuções
#XFLD: Label text
STATUS=Status
#XFLD: Label text
RUN_STATUS=Status da execução
#XFLD: Label text
Runtime=Duração
#XFLD: Label text
RuntimeTooltip=Duração (hh : mm : ss)
#XFLD: Label text
TRIGGEREDBY=Acionado por
#XFLD: Label text
TRIGGEREDBYNew=Executado por
#XFLD: Label text
TRIGGEREDBYNewImp=Execução iniciada por
#XFLD: Label text
EXECUTIONTYPE=Tipo de execução
#XFLD: Label text
EXECUTIONTYPENew=Tipo de execução
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Área da cadeia pai
#XFLD: Refresh tooltip
TEXT_REFRESH=Atualizar
#XFLD: view Details link
VIEW_ERROR_DETAILS=Exibir detalhes
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Baixar detalhes adicionais
#XMSG: Download completed
downloadStarted=Download iniciado
#XMSG: Error while downloading content
errorInDownload=Ocorreu um erro durante o download.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Exibir detalhes
#XBTN: cancel button of task details dialog
TXT_CANCEL=Cancelar
#XBTN: back button from task details
TXT_BACK=Voltar
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Tarefa concluída
#XFLD: Log message with failed status
MSG_LOG_FAILED=Falha na tarefa
#XFLD: Master and detail table with no data
No_Data=Sem dados
#XFLD: Retry tooltip
TEXT_RETRY=Tentar novamente
#XFLD: Cancel Run label
TEXT_CancelRun=Cancelar execução
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Falha ao carregar limpeza
#XMSG:button copy sql statement
txtSQLStatement=Copiar instrução SQL
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Abrir Monitor de consulta remota
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=Para exibir as instruções SQL remotas, clique em 'Abrir Monitor de consulta remota'.
#XMSG:button ok
txtOk=OK
#XMSG: button close
txtClose=Fechar
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=Ação Cancelar execução para objeto "{0}" iniciada.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=Ação Cancelar execução para objeto "{0}" falhou.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=A ação Cancelar execução para o objeto "{0}" não é mais possível porque o status de replicação foi alterado.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Nenhum log de tarefa tem status Em execução.
#XMSG: message for conflicting task
Task_Already_Running=Uma tarefa em conflito já está em execução para o objeto "{0}".
#XFLD: Label for no task log with running state title
actionInfo=Informações da ação
#XMSG Copied to clipboard
copiedToClip=Copiado para clipboard
#XFLD copy
Copy=Copiar
#XFLD copy correlation ID
CopyCorrelationID=Copiar ID de correlação
#XFLD Close
Close=Fechar
#XFLD: show more Label
txtShowMore=Mostrar mais
#XFLD: message Label
messageLabel=Mensagem:
#XFLD: details Label
detailsLabel=Detalhes:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Executando instrução \r\n SQL:
#XFLD:statementId Label
statementIdLabel=ID da instrução:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=N° de instruções SQL\r\n remotas:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Nº de instruções
#XFLD: Space Label
txtSpaces=Área
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Áreas não autorizadas
#XFLD: Privilege Error Text
txtPrivilegeError=Você não tem privilégios suficientes para ver esses dados.
#XFLD: Label for Object Header
DATA_ACCESS=Acesso aos dados
#XFLD: Label for Object Header
SCHEDULE=Programar
#XFLD: Label for Object Header
DETAILS=Detalhes
#XFLD: Label for Object Header
LATEST_UPDATE=Última atualização
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Última alteração (origem)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Frequência de atualização
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Frequência programada
#XFLD: Label for Object Header
NEXT_RUN=Execução seguinte
#XFLD: Label for Object Header
CONNECTION=Conexão
#XFLD: Label for Object Header
DP_AGENT=Agente PD
#XFLD: Label for Object Header
USED_IN_MEMORY=Memória usada para armazenamento (MiB)
#XFLD: Label for Object Header
USED_DISK=Disco usado para armazenamento (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Tamanho in-memory (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Tamanho no disco (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Número de registros

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Definir como Com falha
SET_TO_FAILED_ERR=Esta tarefa estava em execução mas o usuário definiu o status da tarefa como COM FALHA.
#XFLD: Label for stopped failed
FAILLOCKED=A execução já está em andamento
#XFLD: sub status STOPPED
STOPPED=Interrompida
STOPPED_ERR=Esta tarefa foi interrompida, mas nenhum rollback foi realizado.
#XFLD: sub status CANCELLED
CANCELLED=Cancelado
CANCELLED_ERR=Esta execução da tarefa foi cancelada após ter sido iniciada. Nesse caso, o rollback dos dados é realizado e eles são restaurados para o estado em que existiam antes de a execução da tarefa ter sido inicialmente acionada.
#XFLD: sub status LOCKED
LOCKED=Bloqueada
LOCKED_ERR=A mesma tarefa já estav em execução, então este job da tarefa não pode ser executado em paralelo com uma execução da tarefa existente.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Exceção da tarefa
TASK_EXCEPTION_ERR=Esta tarefa encontrou um erro não especificado durante a execução.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Exceção de executar tarefa
TASK_EXECUTE_EXCEPTION_ERR=Esta tarefa encontrou um erro durante a execução.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Não autorizada
UNAUTHORIZED_ERR=O usuário não pôde ser autenticado, foi bloqueado ou excluído.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Proibida
FORBIDDEN_ERR=O usuário atribuído não tem os privilégios necessários para executar esta tarefa.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Não acionada
FAIL_NOT_TRIGGERED_ERR=Este job de tarefa não pôde ser executado devido a uma falha no sistema ou porque alguma parte do sistema de banco de dados não estava disponível no momento da execução planejada. Aguarde o próximo momento da execução do job programado ou programe novamente o job.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Programação cancelada
SCHEDULE_CANCELLED_ERR=Este job de tarefa não pôde ser executado devido a um erro interno. Entre em contato com o suporte da SAP e forneça o ID de correlação e o registro da hora destas informações de log do job de tarefa.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Execução anterior em andamento
SUCCESS_SKIPPED_ERR=Esta execução da tarefa não foi acionada porque uma execução anterior da mesma tarefa ainda está em andamento.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Proprietário ausente
FAIL_OWNER_MISSING_ERR=Não foi possível executar este job de tarefa porque ele não tem um usuário de sistema atribuído. Atribua um usuário responsável ao job.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Consentimento não disponível
FAIL_CONSENT_NOT_AVAILABLE_ERR=Você não autorizou a SAP a executar cadeias de tarefas ou programar tarefas de integração de dados em seu nome. Selecione a opção fornecida para dar o seu consentimento.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Consentimento expirado
FAIL_CONSENT_EXPIRED_ERR=A autorização que permite que a SAP execute cadeias de tarefas ou programe tarefas de integração de dados em seu nome expirou. Selecione a opção fornecida para renovar seu consentimento.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Consentimento invalidado
FAIL_CONSENT_INVALIDATED_ERR=Não foi possível executar esta tarefa, normalmente em decorrência de uma modificação na configuração do Provedor de identidade do locatário. Nesse caso, nenhuma nova tarefa pode ser executada ou programada novamente em nome do usuário afetado. Se o usuário atribuído ainda existir no novo IdP, revoque o consentimento da programação e conceda-o novamente. Se o usuário atribuído não existir mais, atribua um novo propriet´rio do job de tarefas e forneça o consentimento de programação de tarefas necessário. Consulte a seguinte nota SAP: https://launchpad.support.sap.com/#/notes/3089828 para mais informações.
TASK_EXECUTOR_ERROR=Executor da tarefa
TASK_EXECUTOR_ERROR_ERR=Esta tarefa encontrou um erro interno, provavelmente durante as etapas de preparação para execução, e a tarefa não pôde ser iniciada.
PREREQ_NOT_MET=Pré-requisito não atendido
PREREQ_NOT_MET_ERR=Não foi possível executar essa tarefa devido a problemas na sua definição. Por exemplo, o objeto não foi implementado, uma cadeia de tarefas contém lógica circular ou o SQL de uma visão é inválido.
RESOURCE_LIMIT_ERROR=Erro de limite de recursos
RESOURCE_LIMIT_ERROR_ERR=Atualmente não é possível executar a tarefa porque não há recursos suficientes disponíveis ou eles estão ocupados.
FAIL_CONSENT_REFUSED_BY_UMS=Consentimento recusado
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Não foi possível executar esta tarefa nas execuções programadas ou cadeias de tarefas devido a uma alteração em uma configuração do provedor de identidade do usuário no locatário. Para mais informações, consulte a seguinte nota SAP: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Programado
#XFLD: status text
SCHEDULEDNew=Permanente
#XFLD: status text
PAUSED=Pausado
#XFLD: status text
DIRECT=Direto
#XFLD: status text
MANUAL=Manual
#XFLD: status text
DIRECTNew=Simples
#XFLD: status text
COMPLETED=Concluído
#XFLD: status text
FAILED=Com falha
#XFLD: status text
RUNNING=Em execução
#XFLD: status text
none=Nenhum
#XFLD: status text
realtime=Em tempo real
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Subtarefa
#XFLD: New Data available in the file
NEW_DATA=Novo dados

#XFLD: text for values shown in column Replication Status
txtOff=Desativado
#XFLD: text for values shown in column Replication Status
txtInitializing=Iniciando
#XFLD: text for values shown in column Replication Status
txtLoading=Carregando
#XFLD: text for values shown in column Replication Status
txtActive=Ativo
#XFLD: text for values shown in column Replication Status
txtAvailable=Disponível
#XFLD: text for values shown in column Replication Status
txtError=Erro
#XFLD: text for values shown in column Replication Status
txtPaused=Pausado
#XFLD: text for values shown in column Replication Status
txtDisconnected=Desconectado
#XFLD: text for partially Persisted views
partiallyPersisted=Persistido parcialmente

#XFLD: activity text
REPLICATE=Replicar
#XFLD: activity text
REMOVE_REPLICATED_DATA=Remover dados replicados
#XFLD: activity text
DISABLE_REALTIME=Desativar replicação de dados em tempo real
#XFLD: activity text
REMOVE_PERSISTED_DATA=Remover dados persistidos
#XFLD: activity text
PERSIST=Persistir
#XFLD: activity text
EXECUTE=Executar
#XFLD: activity text
TASKLOG_CLEANUP=Tacklog_Cleanup
#XFLD: activity text
CANCEL_REPLICATION=Cancelar replicação
#XFLD: activity text
MODEL_IMPORT=Importação de modelo
#XFLD: activity text
NONE=Nenhum
#XFLD: activity text
CANCEL_PERSISTENCY=Cancelar persistência
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Analisar visão
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Cancelar Analisador de visões

#XFLD: severity text
INFORMATION=Informações
#XFLD: severity text
SUCCESS=Com sucesso
#XFLD: severity text
WARNING=Aviso
#XFLD: severity text
ERROR=Erro
#XFLD: text for values shown for Ascending sort order
SortInAsc=Organizar em ordem crescente
#XFLD: text for values shown for Descending sort order
SortInDesc=Organizar em ordem decrescente
#XFLD: filter text for task log columns
Filter=Filtro
#XFLD: object text for task log columns
Object=Objeto
#XFLD: space text for task log columns
crossSpace=Área

#XBUT: label for remote data access
REMOTE=Remoto
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Replicado (tempo real)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Replicado (instantâneo)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=A replicação em tempo real está bloqueada devido a um erro. Depois que o erro for corrigido, você poderá usar a ação “Tentar novamente” para continuar a replicação em tempo real.
ERROR_MSG=A replicação em tempo real está bloqueada devido a um erro.
RETRY_FAILED_ERROR=Falha com um erro ao tentar o processo novamente.
LOG_INFO_DETAILS=Nenhum log é gerado quando os dados são replicados em modo de tempo real. Os logs exibidos estão relacionados a ações anteriores.

#XBUT: Partitioning label
partitionMenuText=Particionamento
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Criar partição
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Editar partição
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Excluir partição
#XFLD: Initial text
InitialPartitionText=Defina as partições especificando os critérios de divisão de grandes conjuntos de dados em conjuntos menores.
DefinePartition=Definir partições
#XFLD: Message text
partitionChangedInfo=A definição de partição foi alterada desde a última replicação. As alterações serão aplicadas no próximo carregamento de dados.
#XFLD: Message text
REAL_TIME_WARNING=O particionamento só é aplicado quando um novo instantâneo é carregado. Ele não é aplicado na replicação em tempo real.
#XFLD: Message text
loadSelectedPartitions=Persistência de dados iniciada para as partições selecionadas de "{0}"
#XFLD: Message text
loadSelectedPartitionsError=Falha ao persistir dados para as partições selecionadas de "{0}"
#XFLD: Message text
viewpartitionChangedInfo=A definição da partição foi alterada desde a execução da última persistência. Para aplicar as alterações, o próximo carregamento de dados será um instantâneo completo que inclui partições bloqueadas. Depois que esse carregamento completo for concluído, você poderá executar dados para partições individuais.
#XFLD: Message text
viewpartitionChangedInfoLocked=A definição da partição foi alterada desde a execução da última persistência. Para aplicar as alterações, o próximo carregamento de dados será um instantâneo completo, exceto para intervalos de partição bloqueados e não alterados. Depois que esse carregamento for concluído, você poderá carregar partições selecionadas novamente.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Replicação de tabela
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Programar replicação
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Criar programação
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Editar programação
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Excluir programação
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Carregar novo instantâneo
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Iniciar replicação de dados
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Remover dados replicados
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Ativar acesso em tempo real
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Ativar replicação de dados em tempo real
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Desativar replicação de dados em tempo real
#XFLD: Message for replicate table action
replicateTableText=Replicação de tabela
#XFLD: Message for replicate table action
replicateTableTextNew=Replicação de dados
#XFLD: Message to schedule task
scheduleText=Programar
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Exibir persistência
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Persistência de dados
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Carregar novo instantâneo
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Iniciar persistência de dados
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Remover dados persistidos
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Processamento particionado
#XBUT: Label for scheduled replication
scheduledTxt=Programado
#XBUT: Label for statistics button
statisticsTxt=Estatística
#XBUT: Label for create statistics
createStatsTxt=Criar estatística
#XBUT: Label for edit statistics
editStatsTxt=Editar estatística
#XBUT: Label for refresh statistics
refreshStatsTxt=Atualizar estatística
#XBUT: Label for delete statistics
dropStatsTxt=Excluir estatística
#XMSG: Create statistics success message
statsSuccessTxt=Criação de estatística do tipo {0} para {1} iniciada.
#XMSG: Edit statistics success message
statsEditSuccessTxt=Alteração de tipo de estatística para {0} de {1} iniciada.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Atualização de estatística para {0} iniciada.
#XMSG: Drop statistics success message
statsDropSuccessTxt=Estatística de {0}excluída com sucesso
#XMSG: Create statistics error message
statsCreateErrorTxt=Erro ao criar estatística
#XMSG: Edit statistics error message
statsEditErrorTxt=Erro ao alterar estatística
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Erro ao atualizar estatística
#XMSG: Drop statistics error message
statsDropErrorTxt=Erro ao excluir estatística
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Tem certeza de que deseja apagar a estatística de dados?
startPersistencyAdvisorLabel=Iniciar analisador de visões

#Partition related texts
#XFLD: Label for Column
column=Coluna
#XFLD: Label for No of Partition
noOfPartitions=Nº de partições
#XFLD: Label for Column
noOfParallelProcess=Nº de processos paralelos
#XFLD: Label text
noOfLockedPartition=Nº de partições bloqueadas
#XFLD: Label for Partition
PARTITION=Partições
#XFLD: Label for Column
AVAILABLE=Disponível
#XFLD: Statistics Label
statsLabel=Estatística
#XFLD: Label text
COLUMN=Coluna:
#XFLD: Label text
PARALLEL_PROCESSES=Processos paralelos:
#XFLD: Label text
Partition_Range=Intervalo de partição
#XFLD: Label text
Name=Nome
#XFLD: Label text
Locked=Bloqueado
#XFLD: Label text
Others=OUTROS
#XFLD: Label text
Delete=Excluir
#XFLD: Label text
LoadData=Carregar partições selecionadas
#XFLD: Label text
LoadSelectedData=Carregar partições selecionadas
#XFLD: Confirmation text
LoadNewPersistenceConfirm=Isso carregará um novo instantâneo para todas as partições desbloqueadas e alteradas, não apenas para aquelas você selecionou. Continuar?
#XFLD: Label text
Continue=Continuar

#XFLD: Label text
PARTITIONS=Partições
#XFLD: Label text
ADD_PARTITIONS=+ Adicionar partição
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Adicionar partição
#XFLD: Label text
deleteRange=Excluir partição
#XFLD: Label text
LOW_PLACE_HOLDER=Inserir valor inferior
#XFLD: Label text
HIGH_PLACE_HOLDER=Inserir valor superior
#XFLD: tooltip text
lockedTooltip=Bloquear partição após carregamento inicial

#XFLD: Button text
Edit=Editar
#XFLD: Button text
CANCEL=Cancelar

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Última atualização das estatísticas
#XFLD: Statistics Fields
STATISTICS=Estatística

#XFLD:Retry label
TEXT_Retry=Tentar novamente
#XFLD:Retry label
TEXT_Retry_tooltip=Tentar replicação em tempo real novamente depois que o erro for resolvido.
#XFLD: text retry
Retry=Confirmar
#XMG: Retry confirmation text
retryConfirmationTxt=A última replicação em tempo real foi encerrada com um erro.\n Verifique se o erro foi corrigido e se a replicação em tempo real pode ser reiniciada.
#XMG: Retry success text
retrySuccess=Nova tentativa do processo iniciada com sucesso
#XMG: Retry fail text
retryFail=Falha ao tentar o processo novamente.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Criar estatística
#XMSG: activity message for edit statistics
DROP_STATISTICS=Excluir estatística
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Atualizar estatística
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Editar estatística
#XMSG: Task log message started task
taskStarted=A tarefa {0} foi iniciada.
#XMSG: Task log message for finished task
taskFinished=A tarefa {0} foi encerrada com status {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=Tarefa {0} encerrada em {2} com status {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=A tarefa {0} tem parâmetros de entrada.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=A tarefa {0} foi encerrada com um erro inesperado. O status da tarefa foi definido como {1}.
#XMSG: Task log message for failed task
failedToEnd=Falha ao definir o status como {0} ou falha ao remover o bloqueio.
#XMSG: Task log message
lockNotFound=Não é possível finalizar o processo porque não há bloqueio: a tarefa pode ter sido cancelada.
#XMSG: Task log message failed task
failedOverwrite=A tarefa {0} já está bloqueada por {1}. Portanto, falhou com o seguinte erro: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=O controle do bloqueio desta tarefa foi tomado por outra tarefa.
#XMSG: Task log message failed takeover
failedTakeover=Falha ao assumir uma tarefa existente.
#XMSG: Task log message successful takeover
successTakeover=O bloqueio teve que ser liberado. O novo bloqueio para esta tarefa foi definido.
#XMSG: Tasklog Dialog Details
txtDetails=Para exibir as instruções remotas processadas durante a execução, abra o Monitor de consulta remota nos detalhes das mensagens específicas da partição.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=As instruções SQL remotas foram excluídas do banco de dados e não podem ser exibidas.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Consultas remotas que têm conexões atribuídas a outras áreas não podem ser exibidas. Vá para o Monitor de consulta remota e use o ID da instrução para filtrá-las.
#XMSG: Task log message for parallel check error
parallelCheckError=Não é possível processar a tarefa porque outra tarefa está em execução e já está bloqueando essa tarefa.
#XMSG: Task log message for parallel running task
parallelTaskRunning=Uma tarefa em conflito já está em execução.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} durante execução com ID de correlação {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=O usuário atribuído não tem os privilégios necessários para executar esta tarefa.

#XBUT: Label for open in Editor
openInEditor=Abrir no editor
#XBUT: Label for open in Editor
openInEditorNew=Abrir no Gerador de dados
#XFLD:Run deails label
runDetails=Detalhes de execução
#XFLD: Label for Logs
Logs=Logs
#XFLD: Label for Settings
Settings=Configurações
#XFLD: Label for Save button
Save=Salvar
#XFLD: Label for Standard
Standard_PO=Otimizado para desempenho (recomendado)
#XFLD: Label for Hana low memory processing
HLMP_MO=Otimizado para memória
#XFLD: Label for execution mode
ExecutionMode=Modo de execução
#XFLD: Label for job execution
jobExecution=Modo de processamento
#XFLD: Label for Synchronous
syncExec=Síncrono
#XFLD: Label for Asynchronous
asyncExec=Assíncrono
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Usar padrão (assíncrono, pode ser alterado no futuro)
#XMSG: Save settings success
saveSettingsSuccess=O modo de execução do SAP HANA foi alterado com sucesso.
#XMSG: Save settings failure
saveSettingsFailed=Falha ao alterar o modo de execução do SAP HANA.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Execução de job alterada.
#XMSG: Job Execution change failed
jobExecSettingFailed=Falha ao alterar execução de job.
#XMSG: Text for Type
typeTxt=Tipo
#XMSG: Text for Monitor
monitorTxt=Monitorar
#XMSG: Text for activity
activityTxt=Atividade
#XMSG: Text for metrics
metricsTxt=Métricas
#XTXT: Text for Task chain key
TASK_CHAINS=Cadeia de tarefas
#XTXT: Text for View Key
VIEWS=Visão
#XTXT: Text for remote table key
REMOTE_TABLES=Tabela remota
#XTXT: Text for Space key
SPACE=Área
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Nó de computação elástica
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Fluxo de replicação
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Pesquisa inteligente
#XTXT: Text for Local Table
LOCAL_TABLE=Tabela local
#XTXT: Text for Data flow key
DATA_FLOWS=Fluxo de dados
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=Procedimento de script SQL
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=Cadeia de processos BW
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Exibir no monitor
#XTXT: Task List header text
taskListHeader=Lista de tarefas ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Não é possível recuperar as métricas de execuções históricas de um fluxo de dados.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=O detalhe completo da execução não está carregando no momento. Tente atualizar a página.
#XFLD: Label text for the Metrices table header
metricesColLabel=Etiqueta de operador
#XFLD: Label text for the Metrices table header
metricesType=Tipo
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Contagem de registros
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Executar a cadeia de tarefas
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Execução da cadeia de tarefas iniciada.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Execução da cadeia de tarefas iniciada para {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Falha ao executar a cadeia de tarefas.
#XTXT: Execute button label
runLabel=Executar
#XTXT: Execute button label
runLabelNew=Iniciar execução
#XMSG: Filter Object header
chainsFilteredTableHeader=Filtrado por objeto: {0}
#XFLD: Parent task chain label
parentChainLabel=Cadeia de tarefas pai:
#XFLD: Parent task chain unauthorized
Unauthorized=Não autorizado para visualização
#XFLD: Parent task chain label
parentTaskLabel=Tarefa pai:
#XTXT: Task status
NOT_TRIGGERED=Não acionado
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Entrar no modo de tela inteira
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Sair do modo de tela inteira
#XTXT: Close Task log details right panel
closeRightColumn=Fechar seção
#XTXT: Sort Text
sortTxt=Organizar
#XTXT: Filter Text
filterTxt=Filtrar
#XTXT: Filter by text to show list of filters applied
filterByTxt=Filtrar por
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Mais do que 5 minutos
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Mais do que 15 minutos
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Mais do que 1 hora
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Última hora
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Últimas 24 horas
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Último mês
#XTXT: Messages title text
messagesText=Mensagens

#XTXT Statistics information message
statisticsInfo=As estatísticas não podem ser criadas para tabelas remotas com o acesso aos dados "Replicado"'. Para criar estatísticas, remova os dados replicados no Monitor de detalhes de tabelas remotas.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Ir para Monitor de detalhes de tabelas remotas

#XTXT: Repair latest failed run label
retryRunLabel=Repetir última execução
#XTXT: Repair failed run label
retryRun=Repetir execução
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Nova tentativa de execução da cadeia de tarefas iniciada
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Falha na nova tentativa de execução da cadeia de tarefas
#XMSG: Task chain child elements name
taskChainRetryChildObject=Tarefa {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Nova tarefa
#XFLD Analyzed View
analyzedView=Visão analisada
#XFLD Metrics
Metrics=Métricas
#XFLD Partition Metrics
PartitionMetrics=Métricas de partição
#XFLD Entities
Messages=Log de tarefas
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Ainda não foi definida nenhuma partição
#XTXT: Description message for empty partition data
partitionEmptyDescText=Para criar partições, especifique critérios para dividir grandes volumes de dados em partes menores e mais gerenciáveis.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Ainda não há logs disponíveis
#XTXT: Description message for empty runs data
runsEmptyDescText=Quando iniciar uma nova atividade (carregar um novo instantâneo, iniciar o analisador de visões), você verá os logs relacionados aqui.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=A configuração de nó de computação elástica {0} não foi especificada de forma adequada. Verifique sua definição.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Falha no processo para adicionar o nó de computação elástica {0}.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=A criação e a ativação da réplica para o objeto de origem "{0}"."{1}" no nó de computação elástica {2} foram iniciadas.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=A remoção da réplica para o objeto de origem "{0}"."{1}" do nó de computação elástica {2} foi iniciada.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Falha na criação e ativação da réplica para o objeto de origem "{0}"."{1}".
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Falha ao remover réplica para o objeto de origem "{0}"."{1}".
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Roteamento de computação de consultas analíticas da área {0} para o nó de computação elástica {1} iniciado.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Falha no roteamento de computação de consultas analíticas da área {0} para o nó de computação elástica correspondente.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Redirecionamento de volta da computação de consultas analíticas da área {0} a partir do nó de computação elástica {1} iniciado.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Falha no redirecionamento de volta da computação de consultas analíticas da área {0} para coordenador.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Cadeia de tarefas {0} para nó de computação elástica {1} correspondente acionada.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Falha ao gerar cadeia de tarefas para o nó de computação elástica {0}.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=O provisionamento do nó de computação elástica {0} foi iniciado com o plano de dimensionamento fornecido: vCPUs: {1}, memória (GiB): {2} e tamanho de armazenamento (GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=O nó de computação elástica {0} já foi provisionado.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=O nó de computação elástica {0} já foi desprovisionado.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=A operação não é permitida. Interrompa o nó de computação elástica {0} e reinicie-o para atualizar o plano de dimensionamento.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Falha no processo de remoção do nó de computação elástica {0}.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=A execução atual do nó de computação elástica {0} expirou.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=A verificação do status do nó de computação elástica {0} está em andamento...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=A geração da cadeia de tarefas do nó de computação elástica {0} está bloqueada, a cadeia {1} pode ainda estar em execução.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=O objeto de origem "{0}"."{1}" foi replicado como dependência da visão "{2}"."{3}".
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Não é possível replicar a tabela "{0}"."{1}", a replicação da tabela de linhas foi descontinuada.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Nº máximo de nós de computação elástica por instância do SAP HANA Cloud excedido.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=A execução da operação para o nó de computação elástica {0} ainda está em andamento.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Devido a problemas técnicos, a exclusão para a réplica da tabela de origem {0} foi interrompida. Tente novamente mais tarde.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=Devido a problemas técnicos, a criação da réplica para a tabela de origem {0} foi interrompida. Tente novamente mais tarde.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Não é possível criar um nó de computação elástica porque o limite de utilização foi alcançado ou porque nenhuma hora de bloco de computação foi alocada ainda.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Carregando cadeia de tarefas e preparando para executar um total de {0} tarefas que fazem parte dessa cadeia.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Uma tarefa em conflito já está em execução
#XMSG: Replication will change
txt_replication_change=O tipo de replicação será alterado.
txt_repl_viewdetails=Exibir detalhes

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Aparentemente ocorreu um erro ao tentar novamente a última execução porque houve falha na execução da tarefa anterior, antes da geração do plano ser possível.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=A área "{0}" está bloqueada.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=A atividade {0} exige que a tabela local {1} seja implementada.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=A atividade {0} exige que a captura de delta seja ativada para a tabela local {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=A atividade {0} exige que a tabela local {1} armazene dados no repositório de objetos.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=A atividade {0} exige que a tabela local {1} armazene dados no banco de dados.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Iniciando a remoção de registros excluídos da tabela local {0}.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Iniciando a exclusão de todos os registros da tabela local {0}.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Iniciando a exclusão dos registros para a tabela local {0} de acordo com a condição de filtro {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Erro ao remover os registros excluídos da tabela local {0}.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Erro ao excluir todos os registros da tabela local {0}.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Excluir todos os registros totalmente processados com tipo de alteração "Excluído" mais antigos que {0} dias.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Excluir todos os registros totalmente processados com tipo de alteração "Excluído" mais antigos que {0}.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} registros foram excluídos.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} registros foram marcados para exclusão.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=Remoção de registros excluídos da tabela local {0} concluída.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Exclusão de todos os registros da tabela local {0} concluída.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Iniciando a marcação de registros como "Excluído" para a tabela local {0}.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Iniciando a marcação de registros como "Excluído" para a tabela local {0} de acordo com a condição de filtro {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Marcação de registros como "Excluído" para a tabela local {0} concluída.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Alterações de dados carregando temporariamente na tabela {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Alterações de dados carregadas temporariamente na tabela {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Alterações de dados processadas e excluídas da tabela {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Detalhes da conexão.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Iniciando otimização da tabela local (arquivo).
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Ocorreu um erro ao otimizar a tabela local (arquivo).
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Ocorreu um erro. A otimização da tabela local (arquivo) foi interrompida.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Otimizando tabela local (arquivo)...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=A tabela local (arquivo) foi otimizada.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=Tabela local (arquivo) otimizada.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=A tabela está otimizada com colunas z-order: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Ocorreu um erro. O truncamento da tabela local (arquivo) foi interrompido.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Truncando tabela local (arquivo)...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=A localização do buffer de entrada para tabela local (arquivo) foi apagada.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Iniciando limpeza (exclusão de todos os registros processados completamente) da tabela local (arquivo).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Ocorreu um erro ao limpar a tabela local (arquivo).
#XMSG: Task log message
LTF_VACUUM_STOPPED=Ocorreu um erro. A limpeza da tabela local (arquivo) foi interrompida.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Limpando tabela local (arquivo)...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=A limpeza foi concluída.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Excluir todos os registros totalmente processados mais antigos que {0} dias.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Excluir todos os registros totalmente processados mais antigos que {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Iniciando mesclagem de novos registros com tabela local (arquivo).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Iniciando mesclagem de novos registros com tabela local (arquivo) usando a configuração "Mesclar dados automaticamente".
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Iniciando mesclagem de novos registros com tabela local (arquivo). Essa tarefa foi iniciada por meio da API de solicitação de mesclagem.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Mesclando novos registros com tabela local (arquivo).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Ocorreu um erro ao mesclar novos registros com tabela local (arquivo).
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=Tabela local (arquivo) mesclada.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Ocorreu um erro. A mesclagem da tabela local (arquivo) foi interrompida.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=Ocorreu falha na mesclagem de tabela local (arquivo) devido a um erro, mas a operação foi executada com sucesso parcialmente e alguns dados já foram mesclados.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Ocorreu um erro de tempo-limite. A atividade {0} permaneceu em execução por {1} horas.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=Não foi possível iniciar a execução assíncrona devido a uma alta carga do sistema. Abra o "Monitor do sistema" e verifique as tarefas em execução.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=Execução assíncrona cancelada.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=A tarefa {0} foi executada dentro dos limites de memória de {1} e {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=A tarefa {0} foi executada com o ID do recurso {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=Localizar e substituir iniciado na tabela local (arquivo)
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Localizar e substituir concluído na tabela local (arquivo)
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Falha ao localizar e substituir na tabela local (arquivo)

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Ocorreu um erro. A atualização das estatísticas da tabela local (arquivo) foi interrompida.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=Falha na tarefa, erro de falta de memória no banco de dados SAP HANA.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=Falha na tarefa, rejeição do controle de admissão do SAP HANA.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=Falha na tarefa, há muitas conexões do SAP HANA ativas.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=Não foi possível realizar a operação Repetir porque repetições só são permitidas no pai da cadeia de tarefas aninhada.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Os logs das tarefas-filhas com falha não estão mais disponíveis para Nova tentativa.


####Metrics Labels

performanceOptimized=Otimizado para desempenho
memoryOptimized=Otimizado para memória

JOB_EXECUTION=Execução de job
EXECUTION_MODE=Modo de execução
NUMBER_OF_RECORDS_OVERALL=Número de registros persistidos
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Número de registros lidos da fonte remota
RUNTIME_MS_REMOTE_EXECUTION_TIME=Tempo de processamento da fonte remota
MEMORY_CONSUMPTION_GIB=Pico de memória do SAP HANA
NUMBER_OF_PARTITIONS=Número de partições
MEMORY_CONSUMPTION_GIB_OVERALL=Pico de memória do SAP HANA
NUMBER_OF_PARTITIONS_LOCKED=Número de partições bloqueadas
PARTITIONING_COLUMN=Coluna de particionamento
HANA_PEAK_CPU_TIME=Tempo total de CPU do SAP HANA
USED_IN_DISK=Armazenamento usado
INPUT_PARAMETER_PARAMETER_VALUE=Parâmetro de entrada
INPUT_PARAMETER=Parâmetro de entrada
ECN_ID=Nome do nó de computação elástica

DAC=Controles de acesso aos dados
YES=Sim
NO=Não
noofrecords=Número de registros
partitionpeakmemory=Pico de memória do SAP HANA
value=Valor
metricsTitle=Métricas ({0})
partitionmetricsTitle=Partições ({0})
partitionLabel=Partição
OthersNotNull=Valores não incluídos nos intervalos
OthersNull=Valores nulos
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Configurações usadas para última execução de persistência de dados:
#XMSG: Message for input parameter name
inputParameterLabel=Parâmetro de entrada
#XMSG: Message for input parameter value
inputParameterValueLabel=Valor
#XMSG: Message for persisted data
inputParameterPersistedLabel=Persistido às
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Excluir dados
REMOVE_DELETED_RECORDS=Remover registros excluídos
MERGE_FILES=Mesclar arquivos
OPTIMIZE_FILES=Otimizar arquivos
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Exibir no Monitor da ponte do SAP BW

ANALYZE_PERFORMANCE=Analisar desempenho
CANCEL_ANALYZE_PERFORMANCE=Cancelar análise de desempenho

#XFLD: Label for frequency column
everyLabel=A cada
#XFLD: Plural Recurrence text for Hour
hoursLabel=Horas
#XFLD: Plural Recurrence text for Day
daysLabel=Dias
#XFLD: Plural Recurrence text for Month
monthsLabel=Meses
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutos

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Guia de solução de problemas de persistência de visão</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Memória insuficiente para processo. Não é possível persistir os dados para a visão "{0}". Consulte o Help Portal para obter mais informações sobre erros de memória insuficiente. Considere verificar o Analisador de visões para analisar a complexidade da visão em termos de consumo de memória.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Não aplicável
OPEN_BRACKET=(
CLOSE_BRACKET=)
