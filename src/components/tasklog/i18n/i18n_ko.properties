
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=로그 세부사항
#XFLD: Header
TASK_LOGS=태스크 로그({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=실행({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=세부사항 보기
#XFLD: Button text
STOP=실행 중지
#XFLD: Label text
RUN_START=최종 실행 시작
#XFLD: Label text
RUN_END=최종 실행 종료
#XFLD: Label text
RUNTIME=기간
#XTIT: Count for Messages
txtDetailMessages=메시지({0})
#XFLD: Label text
TIME=타임스탬프
#XFLD: Label text
MESSAGE=메시지
#XFLD: Label text
TASK_STATUS=범주
#XFLD: Label text
TASK_ACTIVITY=액티비티
#XFLD: Label text
RUN_START_DETAILS=시작
#XFLD: Label text
RUN_END_DETAILS=종료
#XFLD: Label text
LOGS=실행
#XFLD: Label text
STATUS=상태
#XFLD: Label text
RUN_STATUS=실행 상태
#XFLD: Label text
Runtime=기간
#XFLD: Label text
RuntimeTooltip=기간(hh : mm : ss)
#XFLD: Label text
TRIGGEREDBY=트리거한 사용자
#XFLD: Label text
TRIGGEREDBYNew=실행자
#XFLD: Label text
TRIGGEREDBYNewImp=실행 개시자
#XFLD: Label text
EXECUTIONTYPE=실행 유형
#XFLD: Label text
EXECUTIONTYPENew=실행 유형
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=상위 체인 공간
#XFLD: Refresh tooltip
TEXT_REFRESH=새로 고침
#XFLD: view Details link
VIEW_ERROR_DETAILS=세부사항 보기
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=추가 세부사항 다운로드
#XMSG: Download completed
downloadStarted=다운로드 시작됨
#XMSG: Error while downloading content
errorInDownload=다운로드하는 동안 오류가 발생했습니다.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=세부사항 보기
#XBTN: cancel button of task details dialog
TXT_CANCEL=취소
#XBTN: back button from task details
TXT_BACK=뒤로
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=태스크 완료
#XFLD: Log message with failed status
MSG_LOG_FAILED=태스크 실패
#XFLD: Master and detail table with no data
No_Data=데이터 없음
#XFLD: Retry tooltip
TEXT_RETRY=재시도
#XFLD: Cancel Run label
TEXT_CancelRun=실행 취소
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=실패 로드 정리
#XMSG:button copy sql statement
txtSQLStatement=SQL 문 복사
#XMSG:button open remote query monitor
txtOpenQueryMonitor=원격 쿼리 모니터 열기
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=원격 SQL 문을 조회하려면 "원격 쿼리 모니터 열기"를 클릭하십시오.
#XMSG:button ok
txtOk=확인
#XMSG: button close
txtClose=닫기
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=오브젝트 "{0}"에 대한 실행 취소 액션이 시작되었습니다.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=오브젝트 "{0}"에 대한 실행 취소 액션에 실패했습니다.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=복제 상태가 변경되어 오브젝트 "{0}"에 대한 실행 취소 액션을 더 이상 수행할 수 없습니다.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=태스크 로그의 상태가 실행 중이 아닙니다.
#XMSG: message for conflicting task
Task_Already_Running="{0}" 오브젝트에 대한 충돌 태스크가 이미 실행 중입니다.
#XFLD: Label for no task log with running state title
actionInfo=액션 정보
#XMSG Copied to clipboard
copiedToClip=클립보드에 복사됨
#XFLD copy
Copy=복사
#XFLD copy correlation ID
CopyCorrelationID=상관 관계 ID 복사
#XFLD Close
Close=닫기
#XFLD: show more Label
txtShowMore=더 보기
#XFLD: message Label
messageLabel=메시지:
#XFLD: details Label
detailsLabel=세부사항:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=SQL \r\n 문 실행 중:
#XFLD:statementId Label
statementIdLabel=명령문 ID:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=원격 \r\n SQL 문 수:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=명령문 수
#XFLD: Space Label
txtSpaces=공간
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=승인되지 않은 공간
#XFLD: Privilege Error Text
txtPrivilegeError=이 데이터를 볼 수 있는 충분한 권한이 없습니다.
#XFLD: Label for Object Header
DATA_ACCESS=데이터 액세스
#XFLD: Label for Object Header
SCHEDULE=일정
#XFLD: Label for Object Header
DETAILS=세부사항
#XFLD: Label for Object Header
LATEST_UPDATE=최신 업데이트
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=최신 변경(소스)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=새로 고침 주기
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=예정된 주기
#XFLD: Label for Object Header
NEXT_RUN=다음 실행
#XFLD: Label for Object Header
CONNECTION=연결
#XFLD: Label for Object Header
DP_AGENT=DP 에이전트
#XFLD: Label for Object Header
USED_IN_MEMORY=저장소에 사용된 메모리(MiB)
#XFLD: Label for Object Header
USED_DISK=저장소에 사용된 디스크(MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=인메모리 크기(MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=디스크의 크기(MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=레코드 수

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=실패로 설정
SET_TO_FAILED_ERR=이 태스크는 실행 중이지만 사용자가 이 태스크의 상태를 실패(FAILED)로 설정했습니다.
#XFLD: Label for stopped failed
FAILLOCKED=실행이 이미 진행 중임
#XFLD: sub status STOPPED
STOPPED=중지됨
STOPPED_ERR=이 태스크는 중지되었지만 롤백이 수행되지 않았습니다.
#XFLD: sub status CANCELLED
CANCELLED=취소
CANCELLED_ERR=이 태스크 실행은 시작된 후 취소되었습니다. 이 경우 데이터가 롤백되어 태스크 실행이 처음 트리거되기 전의 상태로 복원되었습니다.
#XFLD: sub status LOCKED
LOCKED=잠김
LOCKED_ERR=동일한 태스크가 이미 실행 중이었으므로 이 태스크 작업은 기존 태스크의 실행과 병렬로 실행할 수 없습니다.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=태스크 예외
TASK_EXCEPTION_ERR=이 태스크를 실행하는 동안 알 수 없는 오류가 발생했습니다.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=태스크 실행 예외
TASK_EXECUTE_EXCEPTION_ERR=이 태스크를 실행하는 동안 오류가 발생했습니다.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=권한 없음
UNAUTHORIZED_ERR=사용자를 인증할 수 없거나 잠겨 있거나 삭제되었습니다.
#XFLD: sub status FORBIDDEN
FORBIDDEN=금지됨
FORBIDDEN_ERR=지정된 사용자는 이 태스크를 실행하는 데 필요한 권한이 없습니다.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=트리거되지 않음
FAIL_NOT_TRIGGERED_ERR=계획된 실행 시간에 시스템 중단 또는 데이터베이스 시스템의 일부를 사용할 수 없기 때문에 이 태스크를 실행할 수 없습니다. 예약된 다음 작업 실행 시간을 기다리거나 작업 일정을 다시 조정하십시오.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=일정 취소됨
SCHEDULE_CANCELLED_ERR=내부 오류로 인해 이 태스크 작업을 실행할 수 없습니다. SAP Support에 문의하여 이 태스크 작업의 로그 세부사항 정보에서 상관 관계 ID와 타임스탬프를 제공하십시오.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=이전 실행 진행 중
SUCCESS_SKIPPED_ERR=동일한 태스크의 이전 실행이 아직 진행 중이기 때문에 이 태스크의 실행이 트리거되지 않았습니다.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=소유자 누락
FAIL_OWNER_MISSING_ERR=이 태스크 작업은 지정된 시스템 사용자가 없기 때문에 실행할 수 없습니다. 작업에 소유자(사용자)를 지정하십시오.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=동의 사용 불가
FAIL_CONSENT_NOT_AVAILABLE_ERR=사용자를 대신하여 태스크 체인을 실행하거나 데이터 통합 태스크를 예약하도록 SAP에 권한을 부여하지 않았습니다. 제공된 옵션을 선택하여 동의합니다.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=동의 만료
FAIL_CONSENT_EXPIRED_ERR=SAP에서 사용자를 대신하여 태스크 체인을 실행하거나 데이터 통합 태스크를 예약할 수 있는 권한이 만료되었습니다. 동의를 갱신하기 위해 제공된 옵션을 선택합니다.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=동의 무효
FAIL_CONSENT_INVALIDATED_ERR=일반적으로 테넌트의 ID 프로바이더 구성 변경으로 인해 이 태스크를 실행할 수 없습니다. 이 경우 영향을 받는 사용자의 이름으로 새 태스크 작업을 실행하거나 예약할 수 없습니다. 지정된 사용자가 새 IdP에 여전히 존재하는 경우 일정 동의를 취소한 다음 다시 부여합니다. 지정된 사용자가 더 이상 존재하지 않으면 새 태스크 소유자를 지정하고 필요한 태스크 예약 동의를 제공합니다. 자세한 내용은 다음 SAP Note를 참조하십시오. https://launchpad.support.sap.com/#/notes/3089828
TASK_EXECUTOR_ERROR=태스크 실행자
TASK_EXECUTOR_ERROR_ERR=이 태스크는 실행 준비 단계에서 내부 오류가 발생하여 태스크를 시작할 수 없습니다.
PREREQ_NOT_MET=선행 조건이 충족되지 않음
PREREQ_NOT_MET_ERR=정의 문제로 인해 이 태스크를 실행할 수 없습니다. 예를 들어, 오브젝트가 배포되지 않았거나, 태스크 체인에 순환 논리가 포함되었거나, 뷰의 SQL이 유효하지 않은 경우입니다.
RESOURCE_LIMIT_ERROR=리소스 제한 오류
RESOURCE_LIMIT_ERROR_ERR=충분한 리소스가 없거나 리소스가 사용 중이므로 현재 태스크를 수행할 수 없습니다.
FAIL_CONSENT_REFUSED_BY_UMS=동의 거부됨
FAIL_CONSENT_REFUSED_BY_UMS_ERR=테넌트에서 사용자의 ID 프로바이더 구성이 변경되었기 때문에 예정된 실행이나 태스크 체인에서 이 태스크를 실행할 수 없습니다. 자세한 내용은 SAP Note(https://launchpad.support.sap.com/#/notes/3120806)를 참조하십시오.
#XFLD: status text
SCHEDULED=예정됨
#XFLD: status text
SCHEDULEDNew=영구
#XFLD: status text
PAUSED=일시 중지됨
#XFLD: status text
DIRECT=직접
#XFLD: status text
MANUAL=수동
#XFLD: status text
DIRECTNew=단순
#XFLD: status text
COMPLETED=완료됨
#XFLD: status text
FAILED=실패
#XFLD: status text
RUNNING=실행 중
#XFLD: status text
none=없음
#XFLD: status text
realtime=실시간
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=하위 태스크
#XFLD: New Data available in the file
NEW_DATA=신규 데이터

#XFLD: text for values shown in column Replication Status
txtOff=꺼짐
#XFLD: text for values shown in column Replication Status
txtInitializing=초기화 중
#XFLD: text for values shown in column Replication Status
txtLoading=로드하는 중
#XFLD: text for values shown in column Replication Status
txtActive=활성
#XFLD: text for values shown in column Replication Status
txtAvailable=사용 가능
#XFLD: text for values shown in column Replication Status
txtError=오류
#XFLD: text for values shown in column Replication Status
txtPaused=일시 중지됨
#XFLD: text for values shown in column Replication Status
txtDisconnected=연결 끊김
#XFLD: text for partially Persisted views
partiallyPersisted=부분 지속됨

#XFLD: activity text
REPLICATE=복제
#XFLD: activity text
REMOVE_REPLICATED_DATA=복제된 데이터 제거
#XFLD: activity text
DISABLE_REALTIME=실시간 데이터 복제 비활성화
#XFLD: activity text
REMOVE_PERSISTED_DATA=지속 데이터 제거
#XFLD: activity text
PERSIST=지속
#XFLD: activity text
EXECUTE=실행
#XFLD: activity text
TASKLOG_CLEANUP=Tacklog_Cleanup
#XFLD: activity text
CANCEL_REPLICATION=복제 취소
#XFLD: activity text
MODEL_IMPORT=모델 임포트
#XFLD: activity text
NONE=없음
#XFLD: activity text
CANCEL_PERSISTENCY=지속성 취소
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=뷰 분석
#XFLD: activity text
CANCEL_VIEW_ANALYZER=View Analyzer 취소

#XFLD: severity text
INFORMATION=정보
#XFLD: severity text
SUCCESS=성공
#XFLD: severity text
WARNING=경고
#XFLD: severity text
ERROR=오류
#XFLD: text for values shown for Ascending sort order
SortInAsc=오름차순 정렬
#XFLD: text for values shown for Descending sort order
SortInDesc=내림차순 정렬
#XFLD: filter text for task log columns
Filter=필터
#XFLD: object text for task log columns
Object=오브젝트
#XFLD: space text for task log columns
crossSpace=공간

#XBUT: label for remote data access
REMOTE=원격
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=복제됨(실시간)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=복제됨(스냅샷)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=오류로 인해 실시간 복제가 차단되었습니다. 오류가 수정되면 "재시도" 액션을 사용하여 실시간 복제를 계속할 수 있습니다.
ERROR_MSG=오류로 인해 실시간 복제가 차단되었습니다.
RETRY_FAILED_ERROR=오류로 인해 재시도 프로세스에 실패했습니다.
LOG_INFO_DETAILS=실시간 모드에서 데이터가 복제될 때 로그가 생성되지 않습니다. 표시된 로그는 이전 액션과 관련된 로그입니다.

#XBUT: Partitioning label
partitionMenuText=분할
#XBUT: Drop down menu button to create a partition
createPartitionLabel=파티션 생성
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=파티션 편집
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=파티션 삭제
#XFLD: Initial text
InitialPartitionText=큰 데이터세트를 작은 세트로 분할하는 기준을 지정하여 파티션을 정의합니다.
DefinePartition=파티션 정의
#XFLD: Message text
partitionChangedInfo=마지막 복제 이후 파티션 정의가 변경되었습니다. 다음 데이터 로드 시 변경사항이 적용됩니다.
#XFLD: Message text
REAL_TIME_WARNING=새로운 스냅샷을 로드할 때만 파티션이 적용됩니다. 실시간 복제에서는 적용되지 않습니다.
#XFLD: Message text
loadSelectedPartitions=선택한 "{0}"의 파티션에 대한 데이터 유지를 시작했습니다.
#XFLD: Message text
loadSelectedPartitionsError=선택한 "{0}"의 파티션에 대한 데이터 유지에 실패했습니다.
#XFLD: Message text
viewpartitionChangedInfo=마지막 지속성 실행 이후 파티션 정의가 변경되었습니다. 변경사항을 적용하기 위해 다음 데이터 로드는 잠긴 파티션을 포함한 전체 스냅샷이 됩니다. 이 전체 로드가 완료되면 단일 파티션에 대한 데이터 실행을 수행할 수 있습니다.
#XFLD: Message text
viewpartitionChangedInfoLocked=마지막 지속성 실행 이후 파티션 정의가 변경되었습니다. 변경사항을 적용하기 위해 다음 데이터 로드는 잠긴 파티션 범위와 변경되지 않은 파티션 범위를 제외하고 전체 스냅샷이 됩니다. 이 로드가 완료되면 선택한 파티션 로드를 다시 실행할 수 있습니다.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=테이블 복제
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=복제 일정
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=일정 생성
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=일정 편집
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=일정 삭제
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=신규 스냅샷 로드
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=데이터 복제 시작
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=복제된 데이터 제거
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=실시간 액세스 사용
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=실시간 데이터 복제 활성화
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=실시간 데이터 복제 비활성화
#XFLD: Message for replicate table action
replicateTableText=테이블 복제
#XFLD: Message for replicate table action
replicateTableTextNew=데이터 복제
#XFLD: Message to schedule task
scheduleText=일정
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=뷰 지속성
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=데이터 지속성
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=신규 스냅샷 로드
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=데이터 지속성 시작
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=지속 데이터 제거
#XFLD: Partitioned Processign
EnablePartitionedProcessing=분할된 처리
#XBUT: Label for scheduled replication
scheduledTxt=예정됨
#XBUT: Label for statistics button
statisticsTxt=통계
#XBUT: Label for create statistics
createStatsTxt=통계 생성
#XBUT: Label for edit statistics
editStatsTxt=통계 편집
#XBUT: Label for refresh statistics
refreshStatsTxt=통계 새로 고침
#XBUT: Label for delete statistics
dropStatsTxt=통계 삭제
#XMSG: Create statistics success message
statsSuccessTxt={1}에 대한 {0} 유형의 통계 생성이 시작되었습니다.
#XMSG: Edit statistics success message
statsEditSuccessTxt={1}에 대한 통계 유형을 {0}(으)로 변경하기 시작했습니다.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt={0} 통계의 새로 고침이 시작되었습니다.
#XMSG: Drop statistics success message
statsDropSuccessTxt={0}에 대한 통계가 삭제되었습니다.
#XMSG: Create statistics error message
statsCreateErrorTxt=통계 생성 중 오류가 발생했습니다.
#XMSG: Edit statistics error message
statsEditErrorTxt=통계 변경 중 오류가 발생했습니다.
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=통계 새로 고침 중 오류가 발생했습니다.
#XMSG: Drop statistics error message
statsDropErrorTxt=통계 삭제 중 오류가 발생했습니다.
#XMG: Warning text for deleting statistics
statsDelWarnTxt=데이터 통계를 삭제하시겠습니까?
startPersistencyAdvisorLabel=View Analyzer 시작

#Partition related texts
#XFLD: Label for Column
column=열
#XFLD: Label for No of Partition
noOfPartitions=파티션 수
#XFLD: Label for Column
noOfParallelProcess=병렬 프로세스 수
#XFLD: Label text
noOfLockedPartition=잠긴 파티션 수
#XFLD: Label for Partition
PARTITION=파티션
#XFLD: Label for Column
AVAILABLE=사용 가능
#XFLD: Statistics Label
statsLabel=통계
#XFLD: Label text
COLUMN=열:
#XFLD: Label text
PARALLEL_PROCESSES=병렬 프로세스:
#XFLD: Label text
Partition_Range=파티션 범위
#XFLD: Label text
Name=이름
#XFLD: Label text
Locked=잠김
#XFLD: Label text
Others=기타
#XFLD: Label text
Delete=삭제
#XFLD: Label text
LoadData=선택한 파티션 로드
#XFLD: Label text
LoadSelectedData=선택한 파티션 로드
#XFLD: Confirmation text
LoadNewPersistenceConfirm=이렇게 하면 선택한 파티션뿐만 아니라 잠금 해제되고 변경된 모든 파티션에 대한 신규 스냅샷이 로드됩니다. 계속하시겠습니까?
#XFLD: Label text
Continue=계속

#XFLD: Label text
PARTITIONS=파티션
#XFLD: Label text
ADD_PARTITIONS=+ 파티션 추가
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=파티션 추가
#XFLD: Label text
deleteRange=파티션 삭제
#XFLD: Label text
LOW_PLACE_HOLDER=하한 값 입력
#XFLD: Label text
HIGH_PLACE_HOLDER=상한 값 입력
#XFLD: tooltip text
lockedTooltip=초기 로드 후 파티션 잠금

#XFLD: Button text
Edit=편집
#XFLD: Button text
CANCEL=취소

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=마지막 통계 업데이트
#XFLD: Statistics Fields
STATISTICS=통계

#XFLD:Retry label
TEXT_Retry=재시도
#XFLD:Retry label
TEXT_Retry_tooltip=오류 해결 후 실시간 복제 재시도
#XFLD: text retry
Retry=확인
#XMG: Retry confirmation text
retryConfirmationTxt=오류로 인해 최종 실시간 복제가 종료되었습니다.\n 오류가 수정되었으며 실시간 복제를 재시작할 수 있는지 확인하십시오.
#XMG: Retry success text
retrySuccess=재시도 프로세스가 시작되었습니다.
#XMG: Retry fail text
retryFail=재시도 프로세스에 실패했습니다.
#XMSG: activity message for create statistics
CREATE_STATISTICS=통계 생성
#XMSG: activity message for edit statistics
DROP_STATISTICS=통계 삭제
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=통계 새로 고침
#XMSG: activity message for edit statistics
ALTER_STATISTICS=통계 편집
#XMSG: Task log message started task
taskStarted=태스크 {0}이(가) 시작되었습니다.
#XMSG: Task log message for finished task
taskFinished=태스크 {0}이(가) {1} 상태로 종료되었습니다.
#XMSG: Task log message for finished task with end time
taskFinishedAt=태스크 {0}이(가) {2}에 {1} 상태로 종료되었습니다.
#XMSG: Task {0} has input parameters
taskHasInputParameters={0} 태스크에 입력 매개변수가 있습니다.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=태스크 {0}이(가) 종료되었지만 예상치 못한 오류가 발생했습니다. 태스크 상태가 {1}(으)로 설정되었습니다.
#XMSG: Task log message for failed task
failedToEnd=상태를 {0}(으)로 설정하지 못했거나 잠금을 제거하지 못했습니다.
#XMSG: Task log message
lockNotFound=잠금이 누락되어서 프로세스를 완료할 수 없음: 태스크가 취소되었을 수 있습니다.
#XMSG: Task log message failed task
failedOverwrite=태스크 {0}은(는) 이미 {1}에 의해 잠겨 있습니다. 따라서 {2} 오류와 함께 실패했습니다.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=이 태스크의 잠금이 다른 태스크에게 인수되었습니다.
#XMSG: Task log message failed takeover
failedTakeover=기존 태스크를 가져오지 못했습니다.
#XMSG: Task log message successful takeover
successTakeover=남은 잠금을 릴리스해야 합니다. 이 태스크에 대한 신규 잠금이 설정되었습니다.
#XMSG: Tasklog Dialog Details
txtDetails=실행 중에 처리된 원격 명령문은 원격 쿼리 모니터를 열어 파티션별 메시지의 세부사항에 표시할 수 있습니다.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=원격 SQL 문이 데이터베이스에서 삭제되어 표시할 수 없습니다.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=다른 연결이 공간에 지정되어 있는 원격 쿼리는 표시할 수 없습니다. ‘원격 쿼리 모니터’로 이동하여 해당 명령문 ID를 사용하여 필터링하십시오.
#XMSG: Task log message for parallel check error
parallelCheckError=다른 태스크가 실행되고 있으며 이미 이 태스크를 보류하고 있으므로 이 태스크가 처리될 수 없습니다.
#XMSG: Task log message for parallel running task
parallelTaskRunning=충돌 태스크가 이미 실행 중입니다.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=상관 관계 ID {1}(으)로 실행하는 동안 {0} 상태입니다.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=지정된 사용자는 이 태스크를 실행하는 데 필요한 권한이 없습니다.

#XBUT: Label for open in Editor
openInEditor=편집기에서 열기
#XBUT: Label for open in Editor
openInEditorNew=데이터 빌더에서 열기
#XFLD:Run deails label
runDetails=실행 세부사항
#XFLD: Label for Logs
Logs=로그
#XFLD: Label for Settings
Settings=설정
#XFLD: Label for Save button
Save=저장
#XFLD: Label for Standard
Standard_PO=성능 최적화됨(권장)
#XFLD: Label for Hana low memory processing
HLMP_MO=메모리 최적화됨
#XFLD: Label for execution mode
ExecutionMode=실행 모드
#XFLD: Label for job execution
jobExecution=처리 모드
#XFLD: Label for Synchronous
syncExec=동기
#XFLD: Label for Asynchronous
asyncExec=비동기
#XFLD: Label for default asynchronous execution
defaultAsyncExec=기본값 사용(비동기, 나중에 변경 가능)
#XMSG: Save settings success
saveSettingsSuccess=SAP HANA 실행 모드가 변경되었습니다.
#XMSG: Save settings failure
saveSettingsFailed=SAP HANA 실행 모드가 변경되지 않았습니다.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=작업 실행이 변경되었습니다.
#XMSG: Job Execution change failed
jobExecSettingFailed=작업 실행이 변경되지 않았습니다.
#XMSG: Text for Type
typeTxt=유형
#XMSG: Text for Monitor
monitorTxt=모니터
#XMSG: Text for activity
activityTxt=액티비티
#XMSG: Text for metrics
metricsTxt=메트릭
#XTXT: Text for Task chain key
TASK_CHAINS=태스크 체인
#XTXT: Text for View Key
VIEWS=보기
#XTXT: Text for remote table key
REMOTE_TABLES=원격 테이블
#XTXT: Text for Space key
SPACE=공간
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=탄력적 계산 노드
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=복제 흐름
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=인텔리전트 조회
#XTXT: Text for Local Table
LOCAL_TABLE=로컬 테이블
#XTXT: Text for Data flow key
DATA_FLOWS=데이터 흐름
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=SQL 스크립트 절차
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=BW 프로세스 체인
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=모니터에서 보기
#XTXT: Task List header text
taskListHeader=태스크 리스트({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=과거에 실행된 데이터 흐름의 메트릭을 가져올 수 없습니다.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=현재 전체 실행 세부사항이 로드되지 않고 있습니다. 새로 고치십시오.
#XFLD: Label text for the Metrices table header
metricesColLabel=연산자 레이블
#XFLD: Label text for the Metrices table header
metricesType=유형
#XFLD: Label text for the Metrices table header
metricesRecordLabel=레코드 개수
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=태스크 체인 실행
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=태스크 체인 실행이 시작되었습니다.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg={0}에 대한 태스크 체인 실행이 시작되었습니다.
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=태스크 체인을 실행하지 못했습니다.
#XTXT: Execute button label
runLabel=실행
#XTXT: Execute button label
runLabelNew=실행 시작
#XMSG: Filter Object header
chainsFilteredTableHeader=오브젝트별 필터링: {0}
#XFLD: Parent task chain label
parentChainLabel=상위 태스크 체인:
#XFLD: Parent task chain unauthorized
Unauthorized=조회할 권한 없음
#XFLD: Parent task chain label
parentTaskLabel=상위 태스크:
#XTXT: Task status
NOT_TRIGGERED=트리거되지 않음
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=전체 화면 모드로 전환
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=전체 화면 모드 종료
#XTXT: Close Task log details right panel
closeRightColumn=섹션 닫기
#XTXT: Sort Text
sortTxt=정렬
#XTXT: Filter Text
filterTxt=필터
#XTXT: Filter by text to show list of filters applied
filterByTxt=필터링 기준
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=5분 초과
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=15분 초과
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=1시간 초과
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=지난 1시간
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=지난 24시간
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=지난 달
#XTXT: Messages title text
messagesText=메시지

#XTXT Statistics information message
statisticsInfo=데이터 액세스 "복제됨"이 있는 원격 테이블에 대한 통계를 생성할 수 없습니다. 통계를 생성하려면 원격 테이블 세부사항 모니터에서 복제된 데이터를 제거하십시오.
#XFLD
GO_TO_REMOTETABLE_DETAILS=원격 테이블 세부사항 모니터로 이동

#XTXT: Repair latest failed run label
retryRunLabel=최근 실행 재시도
#XTXT: Repair failed run label
retryRun=재시도 실행
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=태스크 체인 재시도 실행이 시작되었습니다.
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=태스크 체인 재시도 실행이 실패했습니다.
#XMSG: Task chain child elements name
taskChainRetryChildObject=태스크 {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=신규 태스크
#XFLD Analyzed View
analyzedView=분석된 뷰
#XFLD Metrics
Metrics=메트릭
#XFLD Partition Metrics
PartitionMetrics=파티션 매트릭
#XFLD Entities
Messages=태스크 로그
#XTXT: Title Message for empty partition data
partitionEmptyTitle=아직 정의된 파티션이 없습니다.
#XTXT: Description message for empty partition data
partitionEmptyDescText=큰 데이터 볼륨을 보다 관리하기 편한 작은 부분으로 분할하려면 기준을 지정하여 파티션을 생성하십시오.

#XTXT: Title Message for empty runs data
runsEmptyTitle=아직 사용할 수 있는 로그가 없습니다.
#XTXT: Description message for empty runs data
runsEmptyDescText=신규 액티비티를 시작할 때(신규 스냅샷 로드, View Analyzer 시작...) 여기에서 관련 로그를 볼 수 있습니다.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=탄력적 계산 노드 {0} 구성이 적절하게 설정되지 않았습니다. 정의를 확인하십시오.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=탄력적 계산 노드 {0} 추가 프로세스에 실패했습니다.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=탄력적 계산 노드 {2}에서 소스 오브젝트 "{0}"."{1}"에 대한 복제의 생성 및 활성화가 시작되었습니다.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=탄력적 계산 노드 {2}에서 소스 오브젝트 "{0}"."{1}"에 대한 복제의 제거가 시작되었습니다.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=소스 오브젝트 "{0}"."{1}"에 대한 복제의 생성 및 활성화에 실패했습니다.
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=소스 오브젝트 "{0}"."{1}"에 대한 복제의 제거에 실패했습니다.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=공간 {0}의 분석 쿼리 계산을 탄력적 계산 노드 {1}(으)로 라우팅하는 작업이 시작되었습니다.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=공간 {0}의 분석 쿼리 계산을 해당 탄력적 계산 노드로 라우팅하는 작업에 실패했습니다.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=공간 {0}의 분석 쿼리 계산을 탄력적 계산 노드 {1}에서 다시 라우팅하는 작업이 시작되었습니다.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=공간 {0}의 분석 쿼리 계산을 코디네이터로 다시 라우팅하는 작업에 실패했습니다.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=해당 탄력적 계산 노드 {1}에 대한 태스크 체인 {0}이(가) 트리거되었습니다.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=탄력적 계산 노드 {0}을(를) 위한 태스크 체인 생성에 실패했습니다.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=다음과 같은 사이징 계획으로 탄력적 계산 노드 {0}의 프로비저닝이 시작되었습니다(vCPU: {1}, 메모리(GiB): {2} 저장 공간(GiB): {3}).
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=탄력적 계산 노드 {0}이(가) 이미 프로비저닝되었습니다.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=탄력적 계산 노드 {0}의 프로비저닝이 이미 해제되었습니다.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=작업이 허용되지 않습니다. 탄력적 계산 노드 {0}을(를) 중지하고 재시작하여 사이징 계획을 업데이트하십시오.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=탄력적 계산 노드 {0} 제거 프로세스에 실패했습니다.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=탄력적 계산 노드 {0}의 현재 실행 시간이 초과되었습니다.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=탄력적 계산 노드 {0}의 상태를 점검하고 있습니다.
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=탄력적 계산 노드 {0}의 태스크 체인 생성이 잠겼습니다. {1} 체인이 아직 실행되고 있을 수 있습니다.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=소스 오브젝트 "{0}"."{1}"이(가) 뷰 "{2}"."{3}"의 종속성으로 복제되었습니다.
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=행 테이블 복제가 추천되지 않아 테이블 "{0}"."{1}"을(를) 복제할 수 없습니다.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=SAP HANA Cloud 인스턴스당 최대 탄력적 계산 노드를 초과했습니다.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=탄력적 계산 노드 {0}에 대한 작업이 계속 실행 중입니다.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=기술적인 문제로 인해 소스 테이블 {0}의 복제본 삭제가 중지되었습니다. 나중에 다시 시도하십시오.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=기술적인 문제로 인해 소스 테이블 {0}의 복제본 생성이 중지되었습니다. 나중에 다시 시도하십시오.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=사용량 한도에 도달했거나 컴퓨팅 블록 시간이 아직 할당되지 않았기 때문에 탄력적 계산 노드를 시작할 수 없습니다.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=태스크 체인을 로드하고 이 체인의 일부인 총 {0}개의 태스크 실행을 준비하고 있습니다.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=충돌 태스크가 이미 실행 중입니다.
#XMSG: Replication will change
txt_replication_change=복제 유형이 변경됩니다.
txt_repl_viewdetails=세부사항 보기

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=계획 생성 전에 이전 태스크 실행에 실패했으므로 최근 실행 재시도 시 오류가 발행한 것 같습니다.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=공간 "{0}"이(가) 잠겨 있습니다.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=액티비티 {0}을(를) 수행하려면 로컬 테이블 {1}을(를) 배포해야 합니다.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=액티비티 {0}을(를) 수행하려면 로컬 테이블 {1}에 대한 델타 캡처가 켜져 있어야 합니다. 
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=액티비티 {0}을(를) 수행하려면 로컬 테이블 {1}이(가) 오브젝트 저장소에 데이터를 저장해야 합니다.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=액티비티 {0}을(를) 수행하려면 로컬 테이블 {1}이(가) 데이터베이스에 데이터를 저장해야 합니다.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=로컬 테이블 {0}의 삭제된 레코드 제거를 시작합니다.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=로컬 테이블 {0}의 모든 레코드 삭제를 시작합니다.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=필터 조건 {1}에 따라 로컬 테이블 {0}의 레코드 삭제를 시작합니다.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=로컬 테이블 {0}의 삭제된 레코드를 제거하는 중에 오류가 발생했습니다.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=로컬 테이블 {0}의 모든 레코드를 삭제하는 중에 오류가 발생했습니다.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=변경 유형이 "삭제됨"이며 {0}일보다 오래되고 처리가 완료된 모든 레코드를 삭제합니다.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=변경 유형이 "삭제됨"이며 {0}보다 오래되고 완전히 처리된 모든 레코드를 삭제합니다.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0}개의 레코드가 삭제되었습니다.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS=삭제를 위해 {0}개의 레코드가 표시되었습니다.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=로컬 테이블 {0}의 삭제된 레코드가 모두 제거되었습니다.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=로컬 테이블 {0}의 모든 레코드 삭제가 완료되었습니다.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=로컬 테이블 {0}에서 레코드를 "삭제됨"으로 표시하는 작업을 시작합니다.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=필터 조건 {1}에 따라 로컬 테이블 {0}의 레코드를 "삭제됨"으로 표시하는 작업을 시작합니다.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=로컬 테이블 {0}에서 레코드를 "삭제됨"으로 표시하는 작업이 완료되었습니다.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=데이터 변경 내용이 임시로 {0} 테이블에 로드됩니다.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=데이터 변경 내용이 임시로 {0} 테이블에 로드되었습니다.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=데이터 변경 내용이 처리되어 {0} 테이블에서 삭제되었습니다.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=연결 세부사항입니다.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=로컬 테이블(파일) 최적화를 시작하는 중입니다.
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=로컬 테이블(파일)을 최적화하는 동안 오류가 발생했습니다.
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=오류가 발생했습니다. 로컬 테이블(파일) 최적화가 중지되었습니다.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=로컬 테이블(파일) 최적화 중...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=로컬 테이블(파일)이 최적화되었습니다.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=로컬 테이블(파일)이 최적화되었습니다.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=테이블이 Z-Order 열 {0}(으)로 최적화되었습니다.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=오류가 발생했습니다. 로컬 테이블(파일) 자르기가 중지되었습니다.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=로컬 테이블(파일)을 자르는 중...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=로컬 테이블(파일)을 위한 인바운드 버퍼 장소가 삭제되었습니다.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=로컬 테이블(파일) 비우기(완전히 처리된 레코드 모두 삭제)를 시작하는 중입니다.
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=로컬 테이블(파일)을 비우는 동안 오류가 발생했습니다.
#XMSG: Task log message
LTF_VACUUM_STOPPED=오류가 발생했습니다. 로컬 테이블(파일) 비우기가 중지되었습니다.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=로컬 테이블(파일)을 비우는 중...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=비우기가 완료되었습니다.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO={0}일보다 오래되고 완전히 처리된 모든 레코드를 삭제하는 중입니다.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING={0}보다 오래되고 완전히 처리된 모든 레코드를 삭제하는 중입니다.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=로컬 테이블(파일)에 신규 레코드를 병합하는 작업을 시작하는 중입니다.
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING="데이터 자동 병합" 설정을 사용하여 새로운 레코드를 로컬 테이블 (파일)과 병합하는 작업을 시작합니다.
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=로컬 테이블(파일)을 통해 신규 레코드를 병합하는 작업을 시작합니다. 이 태스크는 API 병합 요청을 통해  시작되었습니다.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=로컬 테이블(파일)에 신규 레코드를 병합하는 중입니다.
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=로컬 테이블(파일)에 신규 레코드를 병합하는 동안 오류가 발생했습니다.
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=로컬 테이블(파일)이 병합되었습니다.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=오류가 발생했습니다. 로컬 테이블(파일) 병합이 중지되었습니다.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=오류로 인해 로컬 테이블(파일) 병합에 실패했지만, 작업이 부분적으로 성공했으며 일부 데이터는 이미 병합되었습니다.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=시간 초과 오류가 발생했습니다. 액티비티 {0}이(가)  {1}시간 동안 실행되었습니다.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=시스템 고부하로 인해 비동기 실행을 시작할 수 없습니다. "시스템 모니터"를 열고 실행 중인 태스크를 점검하십시오.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=비동기 실행이 취소되었습니다.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION={1} 및 {2}의 메모리 한도 내에서 {0} 태스크가 실행되었습니다.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID={0} 태스크가 리소스 ID {1}(으)로 실행되었습니다.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=로컬 테이블(파일)에서 찾기 및 바꾸기가 시작되었습니다.
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=로컬 테이블(파일)에서 찾기 및 바꾸기가 완료되었습니다.
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=로컬 테이블(파일)에서 찾기 및 바꾸기에 실패했습니다.

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=오류가 발생했습니다. 로컬 테이블(파일) 통계 업데이트가 중지되었습니다.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=SAP HANA 데이터베이스의 메모리 부족 오류로 인해 태스크에 실패했습니다.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=SAP HANA 허용 제어 거부로 인해 태스크에 실패했습니다.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=활성 SAP HANA 연결이 너무 많아서 태스크에 실패했습니다.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=재시도는 중첩된 태스크 체인의 상위에서만 허용되므로 재시도 작업을 수행할 수 없습니다.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=실패한 하위 태스크의 로그가 더 이상 진행을 위한 재시도에 사용될 수 없습니다.


####Metrics Labels

performanceOptimized=성능 최적화됨
memoryOptimized=메모리 최적화됨

JOB_EXECUTION=작업 실행
EXECUTION_MODE=실행 모드
NUMBER_OF_RECORDS_OVERALL=지속되는 레코드 수
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=원격 소스에서 읽은 레코드 수
RUNTIME_MS_REMOTE_EXECUTION_TIME=원격 소스 처리 시간
MEMORY_CONSUMPTION_GIB=SAP HANA 최고 메모리
NUMBER_OF_PARTITIONS=파티션 수
MEMORY_CONSUMPTION_GIB_OVERALL=SAP HANA 최고 메모리
NUMBER_OF_PARTITIONS_LOCKED=잠긴 파티션 수
PARTITIONING_COLUMN=분할 열
HANA_PEAK_CPU_TIME=SAP HANA 총 CPU 시간
USED_IN_DISK=사용된 저장소
INPUT_PARAMETER_PARAMETER_VALUE=입력 매개변수
INPUT_PARAMETER=입력 매개변수
ECN_ID=탄력적 계산 노드 이름

DAC=데이터 액세스 제어
YES=예
NO=아니오
noofrecords=레코드 수
partitionpeakmemory=SAP HANA 최고 메모리
value=값
metricsTitle=메트릭({0})
partitionmetricsTitle=파티션({0})
partitionLabel=파티션
OthersNotNull=범위에 포함되지 않은 값
OthersNull=NULL 값
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=마지막 데이터 지속성 실행에 사용된 설정:
#XMSG: Message for input parameter name
inputParameterLabel=입력 매개변수
#XMSG: Message for input parameter value
inputParameterValueLabel=값
#XMSG: Message for persisted data
inputParameterPersistedLabel=지속된 시간
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=데이터 삭제
REMOVE_DELETED_RECORDS=삭제된 레코드 제거
MERGE_FILES=파일 병합
OPTIMIZE_FILES=파일 최적화
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=SAP BW 브리지 모니터에서 보기

ANALYZE_PERFORMANCE=성능 분석
CANCEL_ANALYZE_PERFORMANCE=성능 분석 취소

#XFLD: Label for frequency column
everyLabel=간격
#XFLD: Plural Recurrence text for Hour
hoursLabel=시간
#XFLD: Plural Recurrence text for Day
daysLabel=일
#XFLD: Plural Recurrence text for Month
monthsLabel=개월
#XFLD: Plural Recurrence text for Minutes
minutesLabel=분

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">뷰 지속성 문제 해결 가이드</a>
#XTXT TEXT for view persistency guide link
OOMMessage=프로세스 메모리 부족 상태입니다. 뷰 "{0}"의 데이터를 지속할 수 없습니다. 메모리 부족 오류에 대한 자세한 내용은 Help Portal에서 확인하십시오. View Analyzer를 점검하여 메모리 사용 복잡성에 대한 뷰를 분석해 보십시오.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=해당 없음
OPEN_BRACKET=(
CLOSE_BRACKET=)
