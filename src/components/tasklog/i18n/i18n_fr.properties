
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Détails relatifs au journal
#XFLD: Header
TASK_LOGS=Journaux des tâches ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Exécutions ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Afficher les détails
#XFLD: Button text
STOP=Arrêter l'exécution
#XFLD: Label text
RUN_START=Début de la dernière exécution
#XFLD: Label text
RUN_END=Fin de la dernière exécution
#XFLD: Label text
RUNTIME=Durée
#XTIT: Count for Messages
txtDetailMessages=Messages ({0})
#XFLD: Label text
TIME=Horodatage
#XFLD: Label text
MESSAGE=Message
#XFLD: Label text
TASK_STATUS=Catégorie
#XFLD: Label text
TASK_ACTIVITY=Activité
#XFLD: Label text
RUN_START_DETAILS=Début
#XFLD: Label text
RUN_END_DETAILS=Fin
#XFLD: Label text
LOGS=Exécutions
#XFLD: Label text
STATUS=Statut
#XFLD: Label text
RUN_STATUS=Statut d'exécution
#XFLD: Label text
Runtime=Durée
#XFLD: Label text
RuntimeTooltip=Durée (hh : mm : ss)
#XFLD: Label text
TRIGGEREDBY=Auteur du déclenchement
#XFLD: Label text
TRIGGEREDBYNew=Auteur de l'exécution
#XFLD: Label text
TRIGGEREDBYNewImp=Exécution lancée par
#XFLD: Label text
EXECUTIONTYPE=Type d'exécution
#XFLD: Label text
EXECUTIONTYPENew=Type d'exécution
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Espace de la chaîne parente
#XFLD: Refresh tooltip
TEXT_REFRESH=Actualiser
#XFLD: view Details link
VIEW_ERROR_DETAILS=Afficher les détails
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Télécharger les détails supplémentaires
#XMSG: Download completed
downloadStarted=Le téléchargement a commencé.
#XMSG: Error while downloading content
errorInDownload=Une erreur s'est produite lors du téléchargement.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Afficher les détails
#XBTN: cancel button of task details dialog
TXT_CANCEL=Annuler
#XBTN: back button from task details
TXT_BACK=Retour
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Tâche terminée
#XFLD: Log message with failed status
MSG_LOG_FAILED=Échec de la tâche
#XFLD: Master and detail table with no data
No_Data=Aucune donnée
#XFLD: Retry tooltip
TEXT_RETRY=Réessayer
#XFLD: Cancel Run label
TEXT_CancelRun=Annuler l'exécution
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Nettoyer le chargement échoué
#XMSG:button copy sql statement
txtSQLStatement=Copier l'instruction SQL
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Ouvrir le moniteur de requêtes à distance
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=Pour afficher les instructions SQL à distance, cliquez sur "Ouvrir le moniteur de requêtes à distance".
#XMSG:button ok
txtOk=Ok
#XMSG: button close
txtClose=Fermer
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=L''action Annuler l''exécution a été lancée pour l''objet ''{0}''.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=L''action Annuler l''exécution a échoué pour l''objet ''{0}''.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=L''action Annuler l''exécution n''est plus possible pour l''objet ''{0}'' car le statut de réplication a changé.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Aucun journal des tâches n'a le statut En cours d'exécution.
#XMSG: message for conflicting task
Task_Already_Running=Une tâche en conflit est déjà en cours d''exécution pour l''objet ''{0}''.
#XFLD: Label for no task log with running state title
actionInfo=Informations sur l'action
#XMSG Copied to clipboard
copiedToClip=Copié dans le presse-papiers
#XFLD copy
Copy=Copier
#XFLD copy correlation ID
CopyCorrelationID=Copier l’ID de corrélation
#XFLD Close
Close=Fermer
#XFLD: show more Label
txtShowMore=Afficher plus
#XFLD: message Label
messageLabel=Message :
#XFLD: details Label
detailsLabel=Détails :
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Exécution de l'instruction \r\n SQL :
#XFLD:statementId Label
statementIdLabel=ID d'instruction :
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Nombre d'instructions \r\n SQL à distance :
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Nombre d'instructions
#XFLD: Space Label
txtSpaces=Espace
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Espaces non autorisés
#XFLD: Privilege Error Text
txtPrivilegeError=Vous n'avez pas suffisamment de droits pour afficher ces données.
#XFLD: Label for Object Header
DATA_ACCESS=Accès aux données
#XFLD: Label for Object Header
SCHEDULE=Planifier
#XFLD: Label for Object Header
DETAILS=Détails
#XFLD: Label for Object Header
LATEST_UPDATE=Dernière mise à jour
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Dernière modification (source)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Fréquence d'actualisation
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Fréquence planifiée
#XFLD: Label for Object Header
NEXT_RUN=Prochaine exécution
#XFLD: Label for Object Header
CONNECTION=Connexion
#XFLD: Label for Object Header
DP_AGENT=Agent de mise à disposition de données
#XFLD: Label for Object Header
USED_IN_MEMORY=Mémoire utilisée pour le stockage (Mio)
#XFLD: Label for Object Header
USED_DISK=Disque utilisé pour le stockage (Mio)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Espace en mémoire (Mio) 
#XFLD: Label for Object Header
USED_DISK_NEW=Espace sur disque (Mio)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Nombre d'enregistrements

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Défini sur Échec
SET_TO_FAILED_ERR=Cette tâche était en cours d'exécution, mais l'utilisateur a défini son statut sur ÉCHEC.
#XFLD: Label for stopped failed
FAILLOCKED=Exécution déjà en cours
#XFLD: sub status STOPPED
STOPPED=Arrêté
STOPPED_ERR=Cette tâche a été arrêtée, mais aucun rollback n'a été effectué.
#XFLD: sub status CANCELLED
CANCELLED=Annulé
CANCELLED_ERR=Cette exécution de tâche a été annulée après avoir commencé. Les modifications ont été rejetées et les données restaurées ; elles ont retrouvé l'état qu'elles avaient avant que l'exécution de la tâche soit initialement déclenchée.
#XFLD: sub status LOCKED
LOCKED=Bloqué
LOCKED_ERR=La même tâche était déjà en cours d'exécution. Le job de tâche ne pouvait pas être exécuté en parallèle d'une tâche existante.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Exception de tâche
TASK_EXCEPTION_ERR=Cette tâche a rencontré une erreur non spécifiée durant l'exécution.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Exception d'exécution de la tâche
TASK_EXECUTE_EXCEPTION_ERR=Cette tâche a rencontré une erreur durant l'exécution.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Non autorisé
UNAUTHORIZED_ERR=L'utilisateur n'a pas pu être authentifié. Il a été bloqué ou supprimé.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Interdit
FORBIDDEN_ERR=L'utilisateur affecté n'a pas les droits nécessaires à l'exécution de cette tâche.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Non déclenché
FAIL_NOT_TRIGGERED_ERR=Ce job de tâche n'a pas pu être exécuté en raison d'un arrêt du système ou de l'inaccessibilité d'une partie du système de base de données au moment de l'exécution planifiée. Patientez jusqu'à la prochaine exécution planifiée du job ou replanifiez-le.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Planification annulée
SCHEDULE_CANCELLED_ERR=Ce job de tâche n'a pas pu être exécuté en raison d'une erreur interne. Contactez le support SAP et fournissez-leur l'ID de corrélation ainsi que la date/l'heure à partir des informations détaillées du journal de ce job de tâche.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Exécution précédente en cours
SUCCESS_SKIPPED_ERR=L'exécution de cette tâche n'a pas été déclenchée, car une exécution précédente de cette même tâche est toujours en cours de progression.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Propriétaire manquant
FAIL_OWNER_MISSING_ERR=Ce job de tâche n'a pas pu être exécuté, car aucun utilisateur système ne lui a été affecté. Affectez un utilisateur propriétaire au job.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Consentement indisponible
FAIL_CONSENT_NOT_AVAILABLE_ERR=Vous n'avez pas autorisé SAP à exécuter des chaînes de tâches ou à planifier des tâches d'intégration de données en votre nom. Sélectionnez l'option indiquée pour donner votre consentement.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Consentement expiré
FAIL_CONSENT_EXPIRED_ERR=L'autorisation permettant à SAP d'exécuter des chaînes de tâches et de planifier des tâches d'intégration de données en votre nom a expiré. Sélectionnez l'option indiquée pour renouveler votre consentement.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Consentement invalidé
FAIL_CONSENT_INVALIDATED_ERR=Cette tâche n'a pas pu être exécutée. Cela arrive généralement suite à une modification dans la configuration du fournisseur d'identités du locataire. Dans ce cas de figure, aucun job de tâche ne peut être exécuté ou planifié au nom de l'utilisateur affecté. Si l'utilisateur affecté existe encore dans le nouveau fournisseur d'identités, révoquez le consentement relatif à la planification et accordez-le à nouveau. Si l'utilisateur affecté n'existe plus, affectez un nouveau propriétaire de job de tâche et fournissez le consentement requis pour la planification de la tâche. Pour en savoir plus, référez-vous à la note SAP suivante : https://launchpad.support.sap.com/#/notes/3089828.
TASK_EXECUTOR_ERROR=Exécuteur de tâche
TASK_EXECUTOR_ERROR_ERR=Cette tâche a rencontré une erreur interne. Cette erreur s'est probablement produite pendant les étapes de préparation de l'exécution et la tâche n'a pas pu être lancée.
PREREQ_NOT_MET=La condition préalable n'est pas remplie.
PREREQ_NOT_MET_ERR=Cette tâche n'a pas pu être exécutée en raison de problèmes dans sa définition. Par exemple, l'objet n'est pas déployé, une chaîne de tâches contient une logique circulaire ou une vue SQL n'est pas valide.
RESOURCE_LIMIT_ERROR=Erreur liée à la limite des ressources
RESOURCE_LIMIT_ERROR_ERR=Le système ne peut actuellement pas exécuter la tâche, car les ressources nécessaires sont indisponibles ou occupées.
FAIL_CONSENT_REFUSED_BY_UMS=Consentement refusé
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Cette tâche n'a pas pu être exécutée dans les exécutions planifiées ou les chaînes de tâches en raison d'un changement dans la configuration du fournisseur d'identités d'un utilisateur sur le locataire. Pour plus d'informations, voir la note SAP suivante : https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Planifié
#XFLD: status text
SCHEDULEDNew=Permanent
#XFLD: status text
PAUSED=Suspendu
#XFLD: status text
DIRECT=Direct
#XFLD: status text
MANUAL=Manuel
#XFLD: status text
DIRECTNew=Simple
#XFLD: status text
COMPLETED=Terminé
#XFLD: status text
FAILED=Échec
#XFLD: status text
RUNNING=En cours d'exécution
#XFLD: status text
none=Néant
#XFLD: status text
realtime=En temps réel
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Sous-tâche
#XFLD: New Data available in the file
NEW_DATA=Nouvelles données

#XFLD: text for values shown in column Replication Status
txtOff=Désactivé
#XFLD: text for values shown in column Replication Status
txtInitializing=Initialisation
#XFLD: text for values shown in column Replication Status
txtLoading=Chargement en cours
#XFLD: text for values shown in column Replication Status
txtActive=Activé
#XFLD: text for values shown in column Replication Status
txtAvailable=Disponible
#XFLD: text for values shown in column Replication Status
txtError=Erreur
#XFLD: text for values shown in column Replication Status
txtPaused=Suspendu
#XFLD: text for values shown in column Replication Status
txtDisconnected=Déconnecté
#XFLD: text for partially Persisted views
partiallyPersisted=Partiellement rendu(e) persistant(e)

#XFLD: activity text
REPLICATE=Répliquer
#XFLD: activity text
REMOVE_REPLICATED_DATA=Retirer les données répliquées
#XFLD: activity text
DISABLE_REALTIME=Désactiver la réplication des données en temps réel
#XFLD: activity text
REMOVE_PERSISTED_DATA=Retirer les données rendues persistantes
#XFLD: activity text
PERSIST=Rendre persistant
#XFLD: activity text
EXECUTE=Exécuter
#XFLD: activity text
TASKLOG_CLEANUP=Nettoyage du journal des tâches
#XFLD: activity text
CANCEL_REPLICATION=Annuler la réplication
#XFLD: activity text
MODEL_IMPORT=Import de modèle
#XFLD: activity text
NONE=Néant
#XFLD: activity text
CANCEL_PERSISTENCY=Annuler la persistance
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Analyser la vue
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Annuler l'analyseur de vues

#XFLD: severity text
INFORMATION=Informations
#XFLD: severity text
SUCCESS=Réussite
#XFLD: severity text
WARNING=Avertissement
#XFLD: severity text
ERROR=Erreur
#XFLD: text for values shown for Ascending sort order
SortInAsc=Trier par ordre croissant
#XFLD: text for values shown for Descending sort order
SortInDesc=Trier par ordre décroissant
#XFLD: filter text for task log columns
Filter=Filtrer
#XFLD: object text for task log columns
Object=Objet
#XFLD: space text for task log columns
crossSpace=Espace

#XBUT: label for remote data access
REMOTE=Distant
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Répliqué (en temps réel)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Répliqué (instantané)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=La réplication en temps réel est bloquée à cause d'une erreur. Une fois l'erreur corrigée, vous pouvez utiliser l'action "Réessayer" pour poursuivre la réplication en temps réel.
ERROR_MSG=La réplication en temps réel est bloquée à cause d'une erreur.
RETRY_FAILED_ERROR=Le processus de nouvel essai a échoué avec une erreur.
LOG_INFO_DETAILS=Aucun journal n'est généré lorsque les données sont répliquées en temps réel. Les journaux affichés correspondent aux actions précédentes.

#XBUT: Partitioning label
partitionMenuText=Partitionnement
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Créer une partition
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Modifier la partition
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Supprimer la partition
#XFLD: Initial text
InitialPartitionText=Définissez des partitions en spécifiant des critères pour diviser de grands jeux de données en jeux plus petits.
DefinePartition=Définir des partitions
#XFLD: Message text
partitionChangedInfo=La définition de la partition a été modifiée depuis la dernière réplication. Les modifications seront appliquées sur le prochain chargement de données.
#XFLD: Message text
REAL_TIME_WARNING=Le partitionnement s'applique uniquement lors du chargement d'un nouvel instantané. Il ne s'applique pas pour la réplication en temps réel.
#XFLD: Message text
loadSelectedPartitions=Le processus visant à rendre des données persistantes pour les partitions sélectionnées de "{0}" a commencé.
#XFLD: Message text
loadSelectedPartitionsError=Le processus visant à rendre des données persistantes pour les partitions sélectionnées de "{0}" a échoué.
#XFLD: Message text
viewpartitionChangedInfo=La définition de la partition a été modifiée depuis le dernier cycle de persistance. Pour appliquer ces modifications, le prochain chargement de données sera un instantané complet comprenant les partitions bloquées. Une fois ce chargement complet terminé, vous pourrez exécuter des données pour les partitions uniques.
#XFLD: Message text
viewpartitionChangedInfoLocked=La définition de la partition a été modifiée depuis le dernier cycle de persistance. Pour appliquer ces modifications, le prochain chargement de données sera un instantané complet dont seules les plages de partitions bloquées et non modifiées seront exclues. Une fois ce chargement terminé, vous pourrez à nouveau exécuter l'action Charger les partitions sélectionnées.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Réplication de table
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Planifier une réplication
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Créer une planification
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Modifier la planification
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Supprimer la planification
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Charger un nouvel instantané
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Lancer la réplication de données
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Retirer les données répliquées
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Activer l'accès en temps réel
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Activer la réplication des données en temps réel
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Désactiver la réplication des données en temps réel
#XFLD: Message for replicate table action
replicateTableText=Réplication de table
#XFLD: Message for replicate table action
replicateTableTextNew=Réplication de données
#XFLD: Message to schedule task
scheduleText=Planifier
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Persistance des vues
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Persistance des données
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Charger un nouvel instantané
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Lancer la persistance des données
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Retirer les données rendues persistantes
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Traitement partitionné
#XBUT: Label for scheduled replication
scheduledTxt=Planifiée
#XBUT: Label for statistics button
statisticsTxt=Statistiques
#XBUT: Label for create statistics
createStatsTxt=Créer des statistiques
#XBUT: Label for edit statistics
editStatsTxt=Modifier les statistiques
#XBUT: Label for refresh statistics
refreshStatsTxt=Actualiser les statistiques
#XBUT: Label for delete statistics
dropStatsTxt=Supprimer les statistiques
#XMSG: Create statistics success message
statsSuccessTxt=La création de statistiques de type {0} pour {1} a été lancée.
#XMSG: Edit statistics success message
statsEditSuccessTxt=La modification du type de statistiques en {0} pour {1} a été lancée.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=L''actualisation des statistiques pour {0} a été lancée.
#XMSG: Drop statistics success message
statsDropSuccessTxt=Statistiques correctement supprimées pour {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=Erreur lors de la création des statistiques
#XMSG: Edit statistics error message
statsEditErrorTxt=Erreur lors de la modification des statistiques
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Erreur lors de l'actualisation des statistiques
#XMSG: Drop statistics error message
statsDropErrorTxt=Erreur lors de la suppression des statistiques
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Voulez-vous vraiment supprimer les statistiques de données ?
startPersistencyAdvisorLabel=Lancer l'analyseur de vues

#Partition related texts
#XFLD: Label for Column
column=Colonne
#XFLD: Label for No of Partition
noOfPartitions=Nombre de partitions
#XFLD: Label for Column
noOfParallelProcess=Nombre de processus parallèles
#XFLD: Label text
noOfLockedPartition=Nombre de partitions bloquées
#XFLD: Label for Partition
PARTITION=Partitions
#XFLD: Label for Column
AVAILABLE=Disponible
#XFLD: Statistics Label
statsLabel=Statistiques
#XFLD: Label text
COLUMN=Colonne :
#XFLD: Label text
PARALLEL_PROCESSES=Processus parallèles :
#XFLD: Label text
Partition_Range=Plage de partitions
#XFLD: Label text
Name=Nom
#XFLD: Label text
Locked=Bloqué
#XFLD: Label text
Others=AUTRES
#XFLD: Label text
Delete=Supprimer
#XFLD: Label text
LoadData=Charger les partitions sélectionnées
#XFLD: Label text
LoadSelectedData=Charger les partitions sélectionnées
#XFLD: Confirmation text
LoadNewPersistenceConfirm=Cette action va entraîner le chargement d'un nouvel instantané pour toutes les partitions débloquées et modifiées, et pas seulement celles que vous avez sélectionnées. Voulez-vous poursuivre ?
#XFLD: Label text
Continue=Poursuivre

#XFLD: Label text
PARTITIONS=Partitions
#XFLD: Label text
ADD_PARTITIONS=+ Ajouter une partition
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Ajouter une partition
#XFLD: Label text
deleteRange=Supprimer la partition
#XFLD: Label text
LOW_PLACE_HOLDER=Saisir la valeur inférieure
#XFLD: Label text
HIGH_PLACE_HOLDER=Saisir la valeur supérieure
#XFLD: tooltip text
lockedTooltip=Bloquer la partition après le chargement initial

#XFLD: Button text
Edit=Modifier
#XFLD: Button text
CANCEL=Annuler

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Dernière mise à jour des statistiques
#XFLD: Statistics Fields
STATISTICS=Statistiques

#XFLD:Retry label
TEXT_Retry=Réessayer
#XFLD:Retry label
TEXT_Retry_tooltip=Réessayez la réplication en temps réel une fois l'erreur résolue.
#XFLD: text retry
Retry=Confirmer
#XMG: Retry confirmation text
retryConfirmationTxt=La dernière réplication en temps réel s'est terminée par une erreur.\n Confirmez que l'erreur est corrigée et que la réplication en temps réel peut être relancée.
#XMG: Retry success text
retrySuccess=Le processus de nouvel essai a été correctement lancé.
#XMG: Retry fail text
retryFail=Le processus de nouvel essai a échoué.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Créer des statistiques
#XMSG: activity message for edit statistics
DROP_STATISTICS=Supprimer les statistiques
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Actualiser les statistiques
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Modifier les statistiques
#XMSG: Task log message started task
taskStarted=La tâche {0} a été lancée.
#XMSG: Task log message for finished task
taskFinished=La tâche {0} s''est terminée avec le statut {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=La tâche {0} s''est terminée à {2} avec le statut {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=La tâche {0} a des paramètres d''entrée.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=La tâche {0} s''est terminée avec une erreur inattendue. Le statut de la tâche a été défini sur {1}.
#XMSG: Task log message for failed task
failedToEnd=Échec de la définition du statut sur {0} ou échec du retrait du blocage.
#XMSG: Task log message
lockNotFound=Nous ne pouvons pas finaliser le processus car le blocage est manquant : la tâche a peut-être été annulée.
#XMSG: Task log message failed task
failedOverwrite=La tâche {0} est déjà bloquée par {1}. Par conséquent, elle a échoué avec l''erreur suivante : {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Le blocage de cette tâche a été repris par une autre tâche.
#XMSG: Task log message failed takeover
failedTakeover=Échec de la reprise d'une tâche existante.
#XMSG: Task log message successful takeover
successTakeover=Le blocage restant a dû être libéré. Le nouveau blocage pour cette tâche est défini.
#XMSG: Tasklog Dialog Details
txtDetails=Il est possible d'afficher les instructions à distance traitées lors de l'exécution en ouvrant le moniteur de requêtes à distance dans les détails des messages propres à la partition.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=Les instructions SQL à distance ont été supprimées de la base de données et ne peuvent pas être affichées.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Impossible d'afficher les requêtes à distance qui ont des connexions affectées à d'autres espaces. Accédez au moniteur de requêtes à distance et utilisez l'ID d'instruction pour les filtrer.
#XMSG: Task log message for parallel check error
parallelCheckError=La tâche n'a pas pu être traitée car une autre tâche est en cours d'exécution et bloque déjà cette tâche.
#XMSG: Task log message for parallel running task
parallelTaskRunning=Une tâche en conflit est déjà en cours d'exécution.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Statut {0} pendant l''exécution avec l''ID de corrélation {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=L'utilisateur affecté n'a pas les droits nécessaires à l'exécution de cette tâche.

#XBUT: Label for open in Editor
openInEditor=Ouvrir dans l'éditeur
#XBUT: Label for open in Editor
openInEditorNew=Ouvrir dans le Data Builder
#XFLD:Run deails label
runDetails=Détails de l'exécution
#XFLD: Label for Logs
Logs=Journaux
#XFLD: Label for Settings
Settings=Paramètres
#XFLD: Label for Save button
Save=Sauvegarder
#XFLD: Label for Standard
Standard_PO=Optimisé pour les performances (recommandé)
#XFLD: Label for Hana low memory processing
HLMP_MO=Optimisé pour la mémoire
#XFLD: Label for execution mode
ExecutionMode=Mode d'exécution
#XFLD: Label for job execution
jobExecution=Mode de traitement
#XFLD: Label for Synchronous
syncExec=Synchrone
#XFLD: Label for Asynchronous
asyncExec=Asynchrone
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Utiliser la valeur par défaut (asynchrone, peut changer dans le futur)
#XMSG: Save settings success
saveSettingsSuccess=Le mode d'exécution SAP HANA a été modifié.
#XMSG: Save settings failure
saveSettingsFailed=La modification du mode d'exécution SAP HANA a échoué.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=L'exécution du job a été modifiée.
#XMSG: Job Execution change failed
jobExecSettingFailed=La modification de l'exécution du job a échoué.
#XMSG: Text for Type
typeTxt=Type
#XMSG: Text for Monitor
monitorTxt=Suivi
#XMSG: Text for activity
activityTxt=Activité
#XMSG: Text for metrics
metricsTxt=Métriques
#XTXT: Text for Task chain key
TASK_CHAINS=Chaîne de tâches
#XTXT: Text for View Key
VIEWS=Vue
#XTXT: Text for remote table key
REMOTE_TABLES=Table distante
#XTXT: Text for Space key
SPACE=Espace
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Nœud de calcul flexible
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Flux de réplication
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Recherche intelligente
#XTXT: Text for Local Table
LOCAL_TABLE=Table locale
#XTXT: Text for Data flow key
DATA_FLOWS=Flux de données
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=Procédure SQLScript
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=Chaîne de processus BW
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Afficher sur le moniteur
#XTXT: Task List header text
taskListHeader=Liste des tâches ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Le système ne peut pas récupérer les métriques des exécutions historiques d'un flux de données.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=Le chargement des détails complets de l'exécution n'a pas lieu pour l'instant. Essayez d'actualiser.
#XFLD: Label text for the Metrices table header
metricesColLabel=Étiquette d'opérateur
#XFLD: Label text for the Metrices table header
metricesType=Type
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Nombre d'enregistrements
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Exécuter la chaîne de tâches
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Lancement de l'exécution de la chaîne de tâches
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Lancement de l''exécution de la chaîne de tâches pour {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Échec de l'exécution de la chaîne de tâches
#XTXT: Execute button label
runLabel=Exécuter
#XTXT: Execute button label
runLabelNew=Lancer l'exécution
#XMSG: Filter Object header
chainsFilteredTableHeader=Filtré par objet : {0}
#XFLD: Parent task chain label
parentChainLabel=Chaîne de tâches parent :
#XFLD: Parent task chain unauthorized
Unauthorized=Affichage non autorisé
#XFLD: Parent task chain label
parentTaskLabel=Tâche parent :
#XTXT: Task status
NOT_TRIGGERED=Non déclenché
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Passer en mode plein écran
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Quitter le mode plein écran
#XTXT: Close Task log details right panel
closeRightColumn=Fermer la section
#XTXT: Sort Text
sortTxt=Trier
#XTXT: Filter Text
filterTxt=Filtrer
#XTXT: Filter by text to show list of filters applied
filterByTxt=Filtrer par
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Plus de 5 minutes
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Plus de 15 minutes
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Plus d'1 heure
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=La dernière heure
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Ces dernières 24 heures
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Le mois dernier
#XTXT: Messages title text
messagesText=Messages

#XTXT Statistics information message
statisticsInfo=Impossible de créer des statistiques pour les tables distantes avec accès aux données de type ''Répliqué''. Pour créer des statistiques, retirez les données répliquées dans le moniteur des détails de tables distantes.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Accéder au moniteur des détails de tables distantes

#XTXT: Repair latest failed run label
retryRunLabel=Réessayer la dernière exécution
#XTXT: Repair failed run label
retryRun=Réessayer l'exécution
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=La nouvelle tentative d'exécution de la chaîne de tâches a commencé.
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=La nouvelle tentative d'exécution de la chaîne de tâches a échoué.
#XMSG: Task chain child elements name
taskChainRetryChildObject=Tâche {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Nouvelle tâche
#XFLD Analyzed View
analyzedView=Vue analysée
#XFLD Metrics
Metrics=Métriques
#XFLD Partition Metrics
PartitionMetrics=Métriques de la partition
#XFLD Entities
Messages=Journal des tâches
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Aucune partition n'a été définie pour le moment.
#XTXT: Description message for empty partition data
partitionEmptyDescText=Créez des partitions en indiquant des critères pour décomposer de larges volumes de données en parties plus petites et plus gérables.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Aucun journal disponible pour l'instant
#XTXT: Description message for empty runs data
runsEmptyDescText=Lorsque vous lancerez une nouvelle activité (charger un nouvel instantané, lancer l'analyseur de vues, etc.), les journaux associés s'afficheront ici.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=La configuration du nœud de calcul flexible {0} n''est pas gérée en conséquence. Vérifiez votre définition.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Le processus d''ajout du nœud de calcul flexible {0} a échoué.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=La création et l''activation d''une réplique pour l''objet source ''{0}''.''{1}'' dans le nœud de calcul flexible {2} a commencé.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Le retrait de la réplique pour l''objet source ''{0}''.''{1}'' dans le nœud de calcul flexible {2} a commencé.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=La création et l''activation de la réplique pour l''objet source ''{0}''.''{1}'' a échoué.
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Le retrait de la réplique pour l''objet source ''{0}''.''{1}'' a échoué.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Le routage du calcul des requêtes analytiques de l''espace {0} vers le nœud de calcul flexible {1} a été lancé.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Le routage du calcul des requêtes analytiques de l''espace {0} vers le nœud de calcul flexible a échoué.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Le reroutage du calcul des requêtes analytiques de l''espace {0} depuis le nœud de calcul flexible {1} a été lancé.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Le reroutage du calcul des requêtes analytiques de l''espace {0} vers le coordinateur a échoué.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=La chaîne de tâches {0} pour le nœud de calcul flexible {1} a été déclenchée.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=La génération de la chaîne de tâches pour le nœud de calcul flexible {0} a échoué.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=La mise à disposition du nœud de calcul flexible {0} a commencé avec le plan de dimensionnement indiqué : vCPU : {1}, mémoire (Gio) : {2}, taille du stockage (Gio) : {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=Le nœud de calcul flexible {0} a déjà été mis à disposition.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=La mise à disposition du nœud de calcul flexible {0} a déjà été annulée.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=L''opération n''est pas autorisée. Arrêtez le nœud de calcul flexible {0} et relancez-le pour mettre à jour le plan de dimensionnement.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Le processus de retrait du nœud de calcul flexible {0} a échoué.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=L''exécution actuelle du nœud de calcul flexible {0} a expiré.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=La vérification du statut du nœud de calcul flexible {0} est en cours...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=La génération de la chaîne de tâches pour le nœud de calcul flexible {0} est bloquée, la chaîne {1} est donc peut-être encore en cours d''exécution.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=L''objet source ''{0}''.''{1}'' est répliqué en tant que dépendance de la vue ''{2}''.''{3}''.
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Impossible de répliquer la table ''{0}''.''{1}'' car la réplication de la table de lignes est obsolète.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Le nombre maximum de nœuds de calcul flexibles autorisé par instance SAP HANA Cloud a été dépassé.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=L''opération d''exécution du nœud de calcul flexible {0} est toujours en cours.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=En raison de problèmes techniques, la suppression de la réplique pour la table source {0} a été arrêtée. Veuillez réessayer ultérieurement.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=En raison de problèmes techniques, la création de la réplique pour la table source {0} a été arrêtée. Veuillez réessayer ultérieurement.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Impossible de lancer un nœud de calcul flexible car la limite d'utilisation a été atteinte ou parce qu'aucun temps de calcul par bloc n'a été attribué pour l'instant.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Chargement de la chaîne de tâches en cours et préparation de l''exécution de {0} tâches appartenant à cette chaîne.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Une tâche en conflit est déjà en cours d'exécution.
#XMSG: Replication will change
txt_replication_change=Le type de réplication sera modifié.
txt_repl_viewdetails=Afficher les détails

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Il semblerait qu'une erreur se soit produite lors de la dernière tentative d’exécution. En effet, l'exécution de la tâche précédente a échoué avant la génération du plan.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=L''espace "{0}" est bloqué.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=L''activité {0} requiert le déploiement de la table locale {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=L''activité {0} requiert l''activation de la capture delta pour la table locale {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=L''activité {0} requiert le stockage des données par la table locale {1} dans l''archive objets.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=L''activité {0} requiert le stockage des données par la table locale {1} dans la base de données.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Lancement du retrait des enregistrements supprimés pour la table locale {0}
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Lancement de la suppression de tous les enregistrements pour la table locale {0}
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Lancement de la suppression des enregistrements pour la table locale {0} en fonction de la condition de filtre {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Une erreur s''est produite lors du retrait des enregistrements supprimés pour la table locale {0}.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Une erreur s''est produite lors de la suppression de tous les enregistrements pour la table locale {0}.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Suppression de tous les enregistrements totalement traités avec le type de modification "Supprimé" de plus de {0} jours.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Suppression de tous les enregistrements totalement traités avec le type de modification "Supprimé" de plus de {0}.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} enregistrements ont été supprimés.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} enregistrements ont été marqués pour la suppression.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=Le retrait des enregistrements supprimés pour la table locale {0} est terminé.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=La suppression de tous les enregistrements pour la table locale {0} est terminée.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Lancement du marquage d''enregistrements comme "supprimés" pour la table locale {0}
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Lancement du marquage d''enregistrements comme "supprimés" pour la table locale {0} en fonction de la condition de filtre {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Le marquage d''enregistrements comme "supprimés" pour la table locale {0} est terminé.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Les modifications de données sont temporairement en cours de chargement dans la table {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Les modifications de données sont temporairement chargées dans la table {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Les modifications de données sont traitées et supprimées de la table {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Détails de connexion.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Lancement de l'optimisation de la table locale (fichier).
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Une erreur est survenue lors de l'optimisation de la table locale (fichier).
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Une erreur est survenue. L'optimisation de la table locale (fichier) a été arrêtée.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Optimisation de la table locale (fichier)...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=La table locale (fichier) a été optimisée.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=La table locale (fichier) est optimisée.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=La table est optimisée avec un ordre des colonnes en Z : {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Une erreur est survenue. La troncation de la table locale (fichier) a été arrêtée.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Troncation de la table locale (fichier)...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=L'emplacement de la mémoire tampon entrante pour la table locale (fichier) a été déplacé.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Lancement du nettoyage (suppression de tous les enregistrements totalement traités) de la table locale (fichier).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Une erreur est survenue lors du nettoyage de la table locale (fichier).
#XMSG: Task log message
LTF_VACUUM_STOPPED=Une erreur est survenue. Le nettoyage de la table locale (fichier) a été arrêté.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Nettoyage de la table locale (fichier)...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=Le nettoyage est terminé.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Suppression de tous les enregistrements totalement traités de plus de {0} jours.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Suppression de tous les enregistrements totalement traités de plus de {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Lancement de la fusion de nouveaux enregistrements avec la table locale (fichier).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Lancement de la fusion de nouveaux enregistrements avec la table locale (fichier) à l'aide du paramètre "Fusionner les données automatiquement".
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Lancement de la fusion de nouveaux enregistrements avec la table locale (fichier). Cette tâche a été initiée via l’API Demande de fusion.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Fusion de nouveaux enregistrements avec la table locale (fichier).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Une erreur est survenue lors de la fusion de nouveaux enregistrements avec la table locale (fichier).
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=La table locale (fichier) est fusionnée.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Une erreur est survenue. La fusion de la table locale (fichier) a été arrêtée.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=La fusion de la table locale (fichier) a échoué en raison d'une erreur, mais l'opération a été partiellement réussie et certaines données ont déjà été fusionnées.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Une erreur de délai d''expiration s''est produite. L''activité {0} s''exécute depuis {1} heures.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=L'exécution asynchrone n'a pas pu démarrer en raison d'une charge système élevée. Ouvrez le "Moniteur du système" et vérifiez les tâches en cours d'exécution.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=L'exécution asynchrone a été annulée.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=Exécution de la tâche {0} dans les limites de mémoire de {1} et {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=Exécution de la tâche {0} avec l''ID de ressource {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=La tâche Rechercher et remplacer s'est lancée dans la table locale (fichier).
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=La tâche Rechercher et remplacer s'est terminée dans la table locale (fichier).
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=La tâche Rechercher et remplacer a échoué dans la table locale (fichier).

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Une erreur s'est produite. La mise à jour des statistiques de la table locale (fichier) a été arrêtée.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=La tâche a échoué en raison d'une erreur de capacité mémoire saturée dans la base de données SAP HANA.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=La tâche a échoué en raison d'un rejet de contrôle d'accès pour SAP HANA.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=La tâche a échoué en raison d'un nombre trop élevé de connexions SAP HANA actives.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=Impossible d'exécuter l'opération Réessayer, car le système n'autorise les nouveaux essais que sur le parent d'une chaîne de tâches imbriquée.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Les journaux des tâches enfant ayant échoué ne sont plus disponibles pour Réessayer pour poursuivre.


####Metrics Labels

performanceOptimized=Optimisé pour les performances
memoryOptimized=Optimisé pour la mémoire

JOB_EXECUTION=Exécution du job
EXECUTION_MODE=Mode d'exécution
NUMBER_OF_RECORDS_OVERALL=Nombre d'enregistrements rendus persistants
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Nombre d'enregistrements lus à partir la source distante
RUNTIME_MS_REMOTE_EXECUTION_TIME=Temps de traitement de source distante
MEMORY_CONSUMPTION_GIB=Pic de mémoire - SAP HANA
NUMBER_OF_PARTITIONS=Nombre de partitions
MEMORY_CONSUMPTION_GIB_OVERALL=Pic de mémoire - SAP HANA
NUMBER_OF_PARTITIONS_LOCKED=Nombre de partitions bloquées
PARTITIONING_COLUMN=Colonne de partitionnement
HANA_PEAK_CPU_TIME=Temps processeur total - SAP HANA
USED_IN_DISK=Stockage utilisé
INPUT_PARAMETER_PARAMETER_VALUE=Paramètre d'entrée
INPUT_PARAMETER=Paramètre d'entrée
ECN_ID=Nom du nœud de calcul flexible

DAC=Contrôles de l'accès aux données
YES=Oui
NO=Non
noofrecords=Nombre d'enregistrements
partitionpeakmemory=Pic de mémoire - SAP HANA
value=Valeur
metricsTitle=Métriques ({0})
partitionmetricsTitle=Partitions ({0})
partitionLabel=Partition
OthersNotNull=Valeurs non incluses dans les plages
OthersNull=Valeurs nulles
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Paramètres utilisés pour la dernière exécution d'une persistance de données :
#XMSG: Message for input parameter name
inputParameterLabel=Paramètre d'entrée
#XMSG: Message for input parameter value
inputParameterValueLabel=Valeur
#XMSG: Message for persisted data
inputParameterPersistedLabel=Date/Heure de la persistance
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Supprimer les données
REMOVE_DELETED_RECORDS=Retirer les enregistrements supprimés
MERGE_FILES=Fusionner les fichiers
OPTIMIZE_FILES=Optimiser les fichiers
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Afficher dans le moniteur du pont vers SAP BW

ANALYZE_PERFORMANCE=Analyser la performance
CANCEL_ANALYZE_PERFORMANCE=Annuler l'analyse de la performance

#XFLD: Label for frequency column
everyLabel=Tous les/Toutes les
#XFLD: Plural Recurrence text for Hour
hoursLabel=Heures
#XFLD: Plural Recurrence text for Day
daysLabel=Jours
#XFLD: Plural Recurrence text for Month
monthsLabel=Mois
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutes

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Guide de correction des erreurs de persistance des vues</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Mémoire insuffisante pour le processus. Impossible de rendre les données persistantes pour la vue "{0}". Consultez le SAP Help Portal pour en savoir plus sur les erreurs de mémoire insuffisante. Vous pouvez utiliser l''analyseur de vues pour analyser la vue et en savoir plus sur la complexité de l''utilisation de la mémoire.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Non applicable
OPEN_BRACKET=(
CLOSE_BRACKET=)
