
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=פרטי יומן
#XFLD: Header
TASK_LOGS=יומני משימות ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=הפעלות ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=הצג פרטים
#XFLD: Button text
STOP=הפסק הפעלה
#XFLD: Label text
RUN_START=תחילת הפעלה אחרונה
#XFLD: Label text
RUN_END=סיום הפעלה אחרונה
#XFLD: Label text
RUNTIME=משך זמן
#XTIT: Count for Messages
txtDetailMessages=הודעות ({0})
#XFLD: Label text
TIME=חותמת זמן
#XFLD: Label text
MESSAGE=הודעה
#XFLD: Label text
TASK_STATUS=קטגוריה
#XFLD: Label text
TASK_ACTIVITY=פעילות
#XFLD: Label text
RUN_START_DETAILS=התחלה
#XFLD: Label text
RUN_END_DETAILS=סיום
#XFLD: Label text
LOGS=הפעלות
#XFLD: Label text
STATUS=סטאטוס
#XFLD: Label text
RUN_STATUS=סטאטוס הפעלה
#XFLD: Label text
Runtime=משך זמן
#XFLD: Label text
RuntimeTooltip=משך זמן (hh : mm : ss)
#XFLD: Label text
TRIGGEREDBY=הופעל על-ידי
#XFLD: Label text
TRIGGEREDBYNew=הרצה על-ידי
#XFLD: Label text
TRIGGEREDBYNewImp=ההפעלה התחילה על ידי
#XFLD: Label text
EXECUTIONTYPE=סוג ביצוע
#XFLD: Label text
EXECUTIONTYPENew=סוג הפעלה
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=מרחב שרשרת אב
#XFLD: Refresh tooltip
TEXT_REFRESH=רענן
#XFLD: view Details link
VIEW_ERROR_DETAILS=הצג פרטים
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=הורד פרטים נוספים
#XMSG: Download completed
downloadStarted=ההורדה החלה
#XMSG: Error while downloading content
errorInDownload=אירעה שגיאה במהלך ההורדה.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=הצג פרטים
#XBTN: cancel button of task details dialog
TXT_CANCEL=בטל
#XBTN: back button from task details
TXT_BACK=חזור
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=המשימה הושלמה
#XFLD: Log message with failed status
MSG_LOG_FAILED=המשימה נכשלה
#XFLD: Master and detail table with no data
No_Data=אין נתונים
#XFLD: Retry tooltip
TEXT_RETRY=ניסיון נוסף
#XFLD: Cancel Run label
TEXT_CancelRun=בטל את הריצה
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=נקה טעינה שנכשלה
#XMSG:button copy sql statement
txtSQLStatement=העתק משפט SQL
#XMSG:button open remote query monitor
txtOpenQueryMonitor=פתח מעקב אחר שאילתה מרוחקת
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=כדי להציג את משפטי ה-SQL המרוחקים, לחץ על "פתח מעקב שאילתה מרחוק".
#XMSG:button ok
txtOk=Ok
#XMSG: button close
txtClose=סגור
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=פעולת הפעלה של ביטול עבור האובייקט "{0}" התחילה.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=פעולת הפעלה של ביטול עבור האובייקט "{0}" נכשלה.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=ביטול פעולת הפעלה עבור אובייקט "{0}" לא אפשרי עוד כי סטאטוס השכפול השתנה.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=אין יומני משימות עם הסטאטוס "פועל"
#XMSG: message for conflicting task
Task_Already_Running=משימה מתנגשת כבר פועלת עבור האובייקט "{0}".
#XFLD: Label for no task log with running state title
actionInfo=מידע על הפעולה
#XMSG Copied to clipboard
copiedToClip=הועתק אל לוח העריכה
#XFLD copy
Copy=העתק
#XFLD copy correlation ID
CopyCorrelationID=העתק זיהוי קורלציה
#XFLD Close
Close=סגור
#XFLD: show more Label
txtShowMore=הצג עוד
#XFLD: message Label
messageLabel=הודעה:
#XFLD: details Label
detailsLabel=פרטים:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=ביצוע משפט SQL \r\n:
#XFLD:statementId Label
statementIdLabel=זיהוי משפט:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=מספר של משפטי SQL \r\n מרוחקים:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=מספר דוחות
#XFLD: Space Label
txtSpaces=מרחב
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=מרחבים לא מורשים
#XFLD: Privilege Error Text
txtPrivilegeError=אין לך הרשאות מספיקות להצגת נתונים אלה.
#XFLD: Label for Object Header
DATA_ACCESS=גישה לנתונים
#XFLD: Label for Object Header
SCHEDULE=תזמן
#XFLD: Label for Object Header
DETAILS=פרטים
#XFLD: Label for Object Header
LATEST_UPDATE=עדכון אחרון
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=שינוי אחרון (מקור)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=תדירות רענון
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=תדירות מתוזמנת
#XFLD: Label for Object Header
NEXT_RUN=ההפעלה הבאה
#XFLD: Label for Object Header
CONNECTION=חיבור
#XFLD: Label for Object Header
DP_AGENT=סוכן DP
#XFLD: Label for Object Header
USED_IN_MEMORY=זיכרון בשימוש לאחסון (MiB)
#XFLD: Label for Object Header
USED_DISK=דיסק בשימוש לאחסון (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=שטח זיכרון פנימי (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=שטח זיכרון בדיסק (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=מספר רשומות

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=הגדר כ'נכשל'
SET_TO_FAILED_ERR=משימה זו פעלה אך המשתמש הגדיר את הסטטוס של משימה זו ל-FAILED.
#XFLD: Label for stopped failed
FAILLOCKED=ההפעלה כבר בתהליך
#XFLD: sub status STOPPED
STOPPED=נעצר
STOPPED_ERR=משימה זו נעצרה, אך לא בוצעה הסגה.
#XFLD: sub status CANCELLED
CANCELLED=בוטל
CANCELLED_ERR=הפעלת המשימה בוטלה, לאחר שהיא התחילה. במקרה זה, הנתונים הוסגו ושוחזרו למצב הקיים לפני ההפעלה הראשונית של הפעלת המשימה.
#XFLD: sub status LOCKED
LOCKED=נעול
LOCKED_ERR=אותה משימה כבר הופעלה, כך שלא ניתן להפעיל עבודת משימה זו במקביל לביצוע משימה קיימת.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=ביצוע משימה
TASK_EXCEPTION_ERR=משימה זו נתקלה בשגיאה לא מוגדרת במהלך הביצוע.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=חריגה בביצוע משימה
TASK_EXECUTE_EXCEPTION_ERR=משימה זו נתקלה בשגיאה במהלך הביצוע.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=לא מורשה
UNAUTHORIZED_ERR=לא ניתן היה לאמת את המשתמש, הוא ננעל או נמחק.
#XFLD: sub status FORBIDDEN
FORBIDDEN=אסור
FORBIDDEN_ERR=למשתמש שהוקצה אין את ההרשאות הדרושות לביצוע משימה זו.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=לא הופעל
FAIL_NOT_TRIGGERED_ERR=לא ניתן היה לבצע עבודת משימה זו בגלל הפסקת מערכת או שחלק ממערכת בסיס הנתונים לא היה זמין בזמן הביצוע המתוכנן. המתן לזמן ביצוע העבודה המתוכנן הבא או תזמן מחדש את העבודה.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=תזמון בוטל
SCHEDULE_CANCELLED_ERR=לא ניתן היה לבצע עבודת משימה זו עקב שגיאה פנימית. צור קשר עם התמיכה של SAP וספק להם את זיהוי המתאם וחותמת הזמן מתוך פרטי יומן העבודה של משימה זו.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=הפעלה קודמת בתהליך
SUCCESS_SKIPPED_ERR=ביצוע משימה זו לא הופעל מכיוון שהפעלה קודמת של אותה משימה עדיין מתבצעת.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=חסרים בעלים
FAIL_OWNER_MISSING_ERR=לא ניתן היה לבצע עבודת משימה זו מכיוון שאין לה משתמש מערכת שהוקצה לה. הקצה משתמש אחראי לעבודה.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=הסכמה לא זמינה
FAIL_CONSENT_NOT_AVAILABLE_ERR=לא אישרת ל-SAP להפעיל שרשראות משימות או לתזמן משימות שילוב נתונים בשמך. בחר באפשרות שניתנה כדי לתת את הסכמתך.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=הסכמה פגה תוקף
FAIL_CONSENT_EXPIRED_ERR=פג תוקף ההרשאה המאפשרת ל-SAP להפעיל שרשראות משימות או לתזמן משימות שילוב נתונים בשמך. בחר באפשרות שסופקה כדי לחדש את הסכמתך.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=אימות ההסכמה בוטל
FAIL_CONSENT_INVALIDATED_ERR=לא ניתן היה לבצע משימה זו, בדרך כלל עקב שינוי בתצורת ספק הזהות של הדייר. במקרה זה, לא ניתן להפעיל או לתזמן עבודות משימה חדשות בשם המשתמש המושפע. אם המשתמש המוקצה עדיין קיים ב-IdP החדש, בטל את הסכמת התזמון ולאחר מכן הענק אותה שוב. אם המשתמש שהוקצה כבר לא קיים, הקצה בעלים חדשים של עבודת משימה וספק את ההסכמה הנדרשת לתזמון המשימה. עיין בהערת SAP הבאה כדי לקבל מידע נוסף: https://launchpad.support.sap.com/#/notes/3089828.
TASK_EXECUTOR_ERROR=מבצע משימה
TASK_EXECUTOR_ERROR_ERR=משימה זו נתקלה בשגיאה פנימית, ככל הנראה במהלך שלבי ההכנה לביצוע, ולא ניתן היה להתחיל את המשימה.
PREREQ_NOT_MET=לא התקיימה עמידה בדרישה מקדימה
PREREQ_NOT_MET_ERR=לא ניתן היה להפעיל את המשימה הזו עקב בעיות בהגדרה שלה. לדוגמה, האובייקט לא נפרס, שרשרת משימות מכילה לוגיקה מעגלית או ש-SQL של תצוגה לא חוקי.
RESOURCE_LIMIT_ERROR=שגיאת גבול משאב
RESOURCE_LIMIT_ERROR_ERR=כרגע לא ניתן לבצע משימה מכיוון שלא היו מספיק משאבים זמינים או שהיו תפוסים.
FAIL_CONSENT_REFUSED_BY_UMS=ההסכמה נדחתה
FAIL_CONSENT_REFUSED_BY_UMS_ERR=לא ניתן היה לבצע משימה זו בהפעלות מתוזמנות או בשרשרות משימות בגלל שינוי בתצורת ספק הזהויות בדייר. למידע נוסף, ראה את הודעת SAP הבאה: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=מתוזמן
#XFLD: status text
SCHEDULEDNew=קבוע
#XFLD: status text
PAUSED=מושהה
#XFLD: status text
DIRECT=ישיר
#XFLD: status text
MANUAL=ידני
#XFLD: status text
DIRECTNew=פשוט
#XFLD: status text
COMPLETED=הושלם
#XFLD: status text
FAILED=נכשל
#XFLD: status text
RUNNING=פועל
#XFLD: status text
none=ללא
#XFLD: status text
realtime=זמן אמת
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=משימת משנה
#XFLD: New Data available in the file
NEW_DATA=נתונים חדשים

#XFLD: text for values shown in column Replication Status
txtOff=כבוי
#XFLD: text for values shown in column Replication Status
txtInitializing=מאתחל
#XFLD: text for values shown in column Replication Status
txtLoading=טוען
#XFLD: text for values shown in column Replication Status
txtActive=פעילה
#XFLD: text for values shown in column Replication Status
txtAvailable=זמין
#XFLD: text for values shown in column Replication Status
txtError=שגיאה
#XFLD: text for values shown in column Replication Status
txtPaused=מושהה
#XFLD: text for values shown in column Replication Status
txtDisconnected=לא מחובר
#XFLD: text for partially Persisted views
partiallyPersisted=אוחסן באחסון קבוע באופן חלקי

#XFLD: activity text
REPLICATE=שכפל
#XFLD: activity text
REMOVE_REPLICATED_DATA=הסר נתונים משוכפלים
#XFLD: activity text
DISABLE_REALTIME=השבת שכפול נתונים בזמן אמת
#XFLD: activity text
REMOVE_PERSISTED_DATA=הסר נתונים עקביים
#XFLD: activity text
PERSIST=קבוע
#XFLD: activity text
EXECUTE=בצע
#XFLD: activity text
TASKLOG_CLEANUP=Tacklog_Cleanup
#XFLD: activity text
CANCEL_REPLICATION=בטל שכפול
#XFLD: activity text
MODEL_IMPORT=ייבוא דגם
#XFLD: activity text
NONE=ללא
#XFLD: activity text
CANCEL_PERSISTENCY=בטל אחסון קבוע
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=תצוגת ניתוח
#XFLD: activity text
CANCEL_VIEW_ANALYZER=בטל מנתח תצוגות

#XFLD: severity text
INFORMATION=מידע
#XFLD: severity text
SUCCESS=הצלחה
#XFLD: severity text
WARNING=אזהרה
#XFLD: severity text
ERROR=שגיאה
#XFLD: text for values shown for Ascending sort order
SortInAsc=מיין בסדר עולה
#XFLD: text for values shown for Descending sort order
SortInDesc=מיין בסדר יורד
#XFLD: filter text for task log columns
Filter=מסנן
#XFLD: object text for task log columns
Object=אובייקט
#XFLD: space text for task log columns
crossSpace=מרחב

#XBUT: label for remote data access
REMOTE=מרוחק
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=שוכפל (זמן אמת)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=שוכפל (תמונת מצב)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=שכפול בזמן אמת חסום בגלל שגיאה. לאחר תיקון השגיאה, ניתן להשתמש בפעולה 'נסה שוב' כדי להמשיך בשכפול בזמן אמת.
ERROR_MSG=שכפול בזמן אמת חסום בגלל שגיאה.
RETRY_FAILED_ERROR=תהליך ניסיון נוסף נכשל עם שגיאה.
LOG_INFO_DETAILS=לא נוצרו יומנים בעת שכפול היומנים במצב זמן אמת. היומנים המוצגים מקושרים לפעולות הקודמות.

#XBUT: Partitioning label
partitionMenuText=חלוקה למחיצות
#XBUT: Drop down menu button to create a partition
createPartitionLabel=צור מחיצה
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=ערוך מחיצה
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=מחק מחיצה
#XFLD: Initial text
InitialPartitionText=הגדר מחיצות באמצעות ציון קריטריון לחלק סטים גדולים של נתונים לסטים קטנים יותר.
DefinePartition=הגדר מחיצות
#XFLD: Message text
partitionChangedInfo=הגדרת  המחיצה שונתה מאז השכפול האחרון. שינויים יוחלו על טעינת הנתונים הבאה.
#XFLD: Message text
REAL_TIME_WARNING=חלוקה למחיצות מוחלת רק בעת טעינה של תמונתמצב חדשה. היא לא מוחלת עבור שכפול בזמן אמת.
#XFLD: Message text
loadSelectedPartitions=החלה העברת נתונים לאחסון קבוע עבור מחיצות ''{0}'' נבחרות
#XFLD: Message text
loadSelectedPartitionsError=העברת נתונים לאחסון קבוע עבור מחיצות ''{0}'' נבחרות נכשלה
#XFLD: Message text
viewpartitionChangedInfo=הגדרת המחיצה שונתה מאז הפעלת האחסון הקבוע האחרונה. כדי להחיל את השינויים, טעינת הנתונים הבאה תהיה תמונת מצב מלאה, כולל מחיצות נעולות. ברגע שהטעינה המלאה הזו תסתיים, תוכל להפעיל נתונים עבור מחיצות יחידות.
#XFLD: Message text
viewpartitionChangedInfoLocked=הגדרת המחיצה שונתה מאז הפעלת האחסון הקבוע האחרונה. כדי להחיל את השינויים, טעינת הנתונים הבאה תהיה תמונת מצב מלאה, מלבד טווחי חלוקה למחיצות נעולים או טווחי חלוקה למחיצות שלא שונו. ברגע שהטעינה הזו תסתיים, תוכל לטעון את המחיצות הנבחרות שוב.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=שכפול טבלה
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=תזמן שכפול
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=צור תזמון
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=ערוך תזמון
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=מחק תזמון
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=טען תמונת מצב חדשה
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=התחל שכפול נתונים
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=הסר נתונים משוכפלים
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=הפעל גישה בזמן אמת
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=הפעל שכפול נתונים בזמן אמת
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=השבת שכפול נתונים בזמן אמת
#XFLD: Message for replicate table action
replicateTableText=שכפול טבלה
#XFLD: Message for replicate table action
replicateTableTextNew=שכפול נתונים
#XFLD: Message to schedule task
scheduleText=תזמן
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=תצוגת אחסון קבוע
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=נתונים באחסון קבוע
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=טען תמונת מצב חדשה
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=התחל נתונים באחסון קבוע
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=הסר נתונים עקביים
#XFLD: Partitioned Processign
EnablePartitionedProcessing=עיבוד חלוקה למחיצות
#XBUT: Label for scheduled replication
scheduledTxt=מתוזמן
#XBUT: Label for statistics button
statisticsTxt=סטטיסטיקות
#XBUT: Label for create statistics
createStatsTxt=צור סטטיסטיקות
#XBUT: Label for edit statistics
editStatsTxt=ערוך סטטיסטיקות
#XBUT: Label for refresh statistics
refreshStatsTxt=רענן סטטיסטיקות
#XBUT: Label for delete statistics
dropStatsTxt=מחק סטטיסטיקות
#XMSG: Create statistics success message
statsSuccessTxt=יצירת סטטיסטיקות מסוג {0} עבור {1} התחילה.
#XMSG: Edit statistics success message
statsEditSuccessTxt=שינוי סוג סטטיסטיקות אל {0} עבור {1} התחיל.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=רענון  סטטיסטיקות עבור {0} התחיל.
#XMSG: Drop statistics success message
statsDropSuccessTxt=סטטיסטיקות נמחקו בהצלחה עבור {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=שגיאה בעת יצירת סטטיסטיקות
#XMSG: Edit statistics error message
statsEditErrorTxt=שגיאה בעת שינוי סטטיסטיקות
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=שגיאה בעת רענון סטטיסטיקות
#XMSG: Drop statistics error message
statsDropErrorTxt=שגיאה בעת מחיקת סטטיסטיקות
#XMG: Warning text for deleting statistics
statsDelWarnTxt=האם אתה בטוח שברצונך למחוק את סטטיסטיקות הנתונים?
startPersistencyAdvisorLabel=הפעל מנתח תצוגות

#Partition related texts
#XFLD: Label for Column
column=עמודה
#XFLD: Label for No of Partition
noOfPartitions=מספר המחיצות
#XFLD: Label for Column
noOfParallelProcess=מספר העיבודים במקביל
#XFLD: Label text
noOfLockedPartition=מספר של מחיצות נעולות
#XFLD: Label for Partition
PARTITION=מחיצות
#XFLD: Label for Column
AVAILABLE=זמין
#XFLD: Statistics Label
statsLabel=סטטיסטיקות
#XFLD: Label text
COLUMN=עמודה:
#XFLD: Label text
PARALLEL_PROCESSES=תהליכים מקבילים:
#XFLD: Label text
Partition_Range=טווח מחיצה
#XFLD: Label text
Name=שם
#XFLD: Label text
Locked=נעול
#XFLD: Label text
Others=OTHERS
#XFLD: Label text
Delete=מחק
#XFLD: Label text
LoadData=טען מחיצות נבחרות
#XFLD: Label text
LoadSelectedData=טען מחיצות נבחרות
#XFLD: Confirmation text
LoadNewPersistenceConfirm=פעולה זו תטען תמונת מצב חדשה עבור כל המחיצות שלא נעולות והשתנו, לא רק לאלו שבחרת. האם אתה רוצה להמשיך?
#XFLD: Label text
Continue=המשך

#XFLD: Label text
PARTITIONS=מחיצות
#XFLD: Label text
ADD_PARTITIONS=+ הוסף מחיצה
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=הוסף מחיצות
#XFLD: Label text
deleteRange=מחק מחיצה
#XFLD: Label text
LOW_PLACE_HOLDER=הזן ערך נמוך
#XFLD: Label text
HIGH_PLACE_HOLDER=הזן ערך גבוה
#XFLD: tooltip text
lockedTooltip=נעל מחיצה לאחר טעינה התחלתית

#XFLD: Button text
Edit=ערוך
#XFLD: Button text
CANCEL=בטל

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=עדכון סטטיסטיקה אחרון
#XFLD: Statistics Fields
STATISTICS=סטטיסטיקות

#XFLD:Retry label
TEXT_Retry=נסה שוב
#XFLD:Retry label
TEXT_Retry_tooltip=נסה שוב שכפול בזמן אמת לאחר שהשגיאה תיפתר.
#XFLD: text retry
Retry=אשר
#XMG: Retry confirmation text
retryConfirmationTxt=השכפול האחרון בזמן אמת הופסק עם שגיאה. \n אשר שהשגיאה תוקנה וניתן להתחיל מחדש בשכפול בזמן אמת.
#XMG: Retry success text
retrySuccess=תהליך 'נסה שוב' התחיל בהצלחה.
#XMG: Retry fail text
retryFail=תהליך 'נסה שוב' נכשל.
#XMSG: activity message for create statistics
CREATE_STATISTICS=צור סטטיסטיקות
#XMSG: activity message for edit statistics
DROP_STATISTICS=מחק סטטיסטיקות
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=רענן סטטיסטיקות
#XMSG: activity message for edit statistics
ALTER_STATISTICS=ערוך סטטיסטיקות
#XMSG: Task log message started task
taskStarted=משימה {0} התחילה.
#XMSG: Task log message for finished task
taskFinished=משימה {0} הסתיימה בסטאטוס {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=משימה {0} הסתיימה בשעה {2} עם סטאטוס {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=משימה {0} מכילה פרמטרי קלט
#XMSG: Task log message for unexpected error
unexpectedExecutionError=משימה {0} הסתיימה עם שגיאה לא צפויה. סטאטוס המשימה נקבע להיות {1}.
#XMSG: Task log message for failed task
failedToEnd=קביעת הסטאטוס {0} נכשלה או שהסרת הנעילה נכשלה.
#XMSG: Task log message
lockNotFound=לא ניתן לסיים את התהליך כיוון שהנעילה חסרה: ייתכן שהמשימה בוטלה.
#XMSG: Task log message failed task
failedOverwrite=המשימה {0} כבר ננעלה על-ידי {1}. לכן, היא נכשלה עם השגיאה הבאה: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=נעילה של משימה זו נשלטת בידי משימה אחרת.
#XMSG: Task log message failed takeover
failedTakeover=השתלטות על המשימה הקיימת נכשלה.
#XMSG: Task log message successful takeover
successTakeover=היה צורך לשחרר נעילה שנשארה. הנעילה החדשה עבור משימה זו נקבעה.
#XMSG: Tasklog Dialog Details
txtDetails=ניתן להציג את ההצהרות המרוחקות שעובדו במהלך הריצה על-ידי פתיחת צג השאילתות המרוחק, בפרטי ההודעות הספציפיות למחיצות.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=משפטי SQL מרוחקים נמחקו מבסיס הנתונים ולא ניתן להציגם.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=לא ניתן להציג שאילתות מרוחקות שהוקצו להן חיבורים למרחבים אחרים. עבור למעקב שאילתות מרוחק והשתמש בזיהוי המשפט כדי לסנן אותם.
#XMSG: Task log message for parallel check error
parallelCheckError=לא ניתן לעבד את המשימה כיוון שמשימה אחרת פועלת וחוסמת את המשימה הזו.
#XMSG: Task log message for parallel running task
parallelTaskRunning=משימה מתנגשת כבר פועלת.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=סטאטוס {0} במהלך הפעלה עם זיהוי מתאם {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=למשתמש שהוקצה אין את ההרשאות הדרושות לביצוע משימה זו.

#XBUT: Label for open in Editor
openInEditor=פתח בעורך
#XBUT: Label for open in Editor
openInEditorNew=פתח בבונה מודל הנתונים
#XFLD:Run deails label
runDetails=פרטי הפעלה
#XFLD: Label for Logs
Logs=יומנים
#XFLD: Label for Settings
Settings=הגדרות
#XFLD: Label for Save button
Save=שמור
#XFLD: Label for Standard
Standard_PO=מותאם לביצועים (מומלץ)
#XFLD: Label for Hana low memory processing
HLMP_MO=מותאם לזיכרון
#XFLD: Label for execution mode
ExecutionMode=מצב הפעלה
#XFLD: Label for job execution
jobExecution=מצב עיבוד
#XFLD: Label for Synchronous
syncExec=סנכרוני
#XFLD: Label for Asynchronous
asyncExec=א-סנכרוני
#XFLD: Label for default asynchronous execution
defaultAsyncExec=השתמש בברירת מחדל (א-סנכרוני, עשוי להשתנות בעתיד)
#XMSG: Save settings success
saveSettingsSuccess=מצב ביצוע של SAP HANA שונה.
#XMSG: Save settings failure
saveSettingsFailed=מצב ביצוע של SAP HANA נכשל.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=ביצוע עבודה שונה.
#XMSG: Job Execution change failed
jobExecSettingFailed=ביצוע עבודה נכשל.
#XMSG: Text for Type
typeTxt=סוג
#XMSG: Text for Monitor
monitorTxt=מעקב
#XMSG: Text for activity
activityTxt=פעילות
#XMSG: Text for metrics
metricsTxt=מדדים
#XTXT: Text for Task chain key
TASK_CHAINS=שרשרת משימות
#XTXT: Text for View Key
VIEWS=תצוגה
#XTXT: Text for remote table key
REMOTE_TABLES=טבלה מרוחקת
#XTXT: Text for Space key
SPACE=מרחב
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=צומת חישוב גמיש
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=תזרים שכפול
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=חיפוש חכם
#XTXT: Text for Local Table
LOCAL_TABLE=טבלה מקומית
#XTXT: Text for Data flow key
DATA_FLOWS=תזרים נתונים
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=הליך של SQL Script
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=שרשרת תהליכי BW
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=הצג במסך
#XTXT: Task List header text
taskListHeader=רשימת משימות ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=לא ניתן לאחזר מדדים עבור ריצות היסטוריות של תזרים נתונים.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=פרטים של הפעלת השלמה לא נטענו. נסה לרענן.
#XFLD: Label text for the Metrices table header
metricesColLabel=תווית אופרטור
#XFLD: Label text for the Metrices table header
metricesType=סוג
#XFLD: Label text for the Metrices table header
metricesRecordLabel=ספירת רשומות
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=הפעל את שרשרת המשימות
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=הפעלת שרשרת המשימות החלה.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=הפעלת שרשרת המשימות החלה עבור {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=הפעלת שרשרת המשימות נכשלה.
#XTXT: Execute button label
runLabel=הפעל
#XTXT: Execute button label
runLabelNew=התחל הפעלה
#XMSG: Filter Object header
chainsFilteredTableHeader=סונן על-ידי אובייקט: {0}
#XFLD: Parent task chain label
parentChainLabel=שרשרת משימות אב:
#XFLD: Parent task chain unauthorized
Unauthorized=אין הרשאה לתצוגה
#XFLD: Parent task chain label
parentTaskLabel=משימת אב:
#XTXT: Task status
NOT_TRIGGERED=לא הופעל
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=היכנס למצב מסך מלא
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=צא ממצב מסך מלא
#XTXT: Close Task log details right panel
closeRightColumn=סגור מקטע
#XTXT: Sort Text
sortTxt=מיין
#XTXT: Filter Text
filterTxt=סנן
#XTXT: Filter by text to show list of filters applied
filterByTxt=סנן לפי
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=יותר מ-5 דקות
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=יותר מ-15 דקות
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=יותר משעה
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=בשעה האחרונה
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=ב-24 השעות האחרונות
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=חודש אחרון
#XTXT: Messages title text
messagesText=הודעות

#XTXT Statistics information message
statisticsInfo=לא ניתן ליצור נתונים סטטיסטיים עבור טבלאות מרוחקות עם גישה לנתונים ''משוכפלת''. כדי ליצור נתונים סטטיסטיים, הסר את הנתונים המשוכפלים ב'מעקב אחר פרטי טבלה מרוחקת'.
#XFLD
GO_TO_REMOTETABLE_DETAILS=עבור ל'מעקב אחר פרטי טבלה מרוחקת

#XTXT: Repair latest failed run label
retryRunLabel=נסה שוב הפעלה אחרונה
#XTXT: Repair failed run label
retryRun=נסה שוב הפעלה
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=הפעלת ניסיון נוסף של שרשרת המשימות החלה
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=הפעלת ניסיון נוסף של שרשרת המשימות נכשלה
#XMSG: Task chain child elements name
taskChainRetryChildObject=משימה {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=משימה חדשה
#XFLD Analyzed View
analyzedView=תצוגה מנותחת
#XFLD Metrics
Metrics=מדדים
#XFLD Partition Metrics
PartitionMetrics=מדדי מחיצה
#XFLD Entities
Messages=יומן משימות
#XTXT: Title Message for empty partition data
partitionEmptyTitle=טרם הוגדרו מחיצות.
#XTXT: Description message for empty partition data
partitionEmptyDescText=צור מחיצות על-ידי ציון קריטריונים כדי לפצל נפחי נתונים גדולים לחלקים קטנים ויותר ניתנים לניהול.

#XTXT: Title Message for empty runs data
runsEmptyTitle=אין יומנים זמינים עדיין
#XTXT: Description message for empty runs data
runsEmptyDescText=בעת התחלת פעילות חדשה (טעינת תמונת מצב חדשה, התחלת מנתח תצוגה...) תראה את היומנים הקשורים כאן.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=התצורה של צומת החישוב הגמיש {0} לא מתוחזקת בהתאם. בדוק את ההגדרה שלך.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=התהליך להוספת צומת חישוב גמיש {0} נכשל.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=יצירה והפעלה של העתק עבור אובייקט המקור ''{0}''.''{1}'' בצומת חישוב גמיש {2} החלה.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=הסרת העתק של אובייקט המקור ''{0}''.''{1}'' מצומת חישוב גמיש {2} החלה.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=יצירה והפעלה של העתק עבור אובייקט המקור ''{0}''.''{1}'' נכשלו.
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=הסרת העתק עבור טבלה ''{0}''.''{1}'' נכשלה.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=ניתוב חישוב שאילתות אנליטיות של המרחב {0} לצומת חישוב גמיש {1} החל.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=ניתוב חישוב שאילתות אנליטיות של המרחב {0} לצומת חישוב גמיש המתאים נכשל.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=ניתוב מחדש של חישוב שאילתות אנליטיות של המרחב {0} בחזרה לצומת חישוב גמיש {1} החל.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=ניתוב מחדש של חישוב שאילתות אנליטיות של המרחב {0} בחזרה למתאם נכשל.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=שרשרת משימות {0} לצומת חישוב גמיש {1} הופעלה.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=יצירת שרשרת משימות עבור צומת חישוב גמיש {0} נכשלה.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=האספקה של צומת חישוב גמיש {0} החלה עם תוכנית הגודל הבאה: vCPUs: {1}, זיכרון (GiB): {2} וגודל אחסון (GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=צומת חישוב גמיש {0} כבר סופק.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=צומת חישוב גמיש {0} כבר בוטל.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=הפעולה אסורה. הפסק את צומת החישוב האלסטי {0} ואתחל אותו כדי לעדכן את תוכנית הסידור לפי הגודל.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=התהליך להסרת צומת חישוב גמיש {0} נכשל.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=נגמר הזמן של ההפעלה הנוכחית של צומת החישוב הגמיש {0}.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=בדיקת הסטאטוס של צומת החישוב הגמיש {0} נמצאת בתהליך...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=יצירת שרשרת משימות עבור צומת החישוב הגמיש {0} נעולה, לכן ייתכן שהשרשרת {1} עדיין פועלת.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=אובייקט המקור ''{0}''.''{1}'' משוכפל כתלות בתצוגה ''''{2}''.''{3}''.
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=לא ניתן לשכפל את הטבלה "{0}". "{1}" כיוון שהשכפול של טבלת שורות הוצא משימוש.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=חריגה מצומת מחשוב גמיש לכל מופע ענן SAP HANA.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=פעולת ההפעלה של צומת המחשוב הגמיש {0} עדיין בתהליך.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=עקב בעיות טכניות, מחיקת העותק עבור טבלת המקור {0} נעצרה. נסה שוב מאוחר יותר.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=עקב בעיות טכניות, יצירת העותק עבור טבלת המקור {0} נעצרה. נסה שוב מאוחר יותר.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=לא ניתן להתחיל צומת חישוב גמישה מכיוון שהגבלת השימוש הושגה או שטרם הוקצו שעות חסימה לחישוב.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=טוען שרשרת משימות ומתכונן להפעלה כוללת של {0} משימות שהן חלק מהשרשרת.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=משימה מתנגשת כבר פועלת.
#XMSG: Replication will change
txt_replication_change=סוג השכפול ישתנה.
txt_repl_viewdetails=הצג פרטים

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=נראה שאירעה שגיאה בניסיון מחדש של ההפעלה האחרונה כיוון שהפעלת המשימה הקודמת נכשלה לפני יצירת התוכנית.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=מרחב "{0}" נעול

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=הפעולה {0} דורשת את פריסת הטבלה המקומית {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=הפעילות {0} דורשת הפעלה של לכידת דלתא עבור הטבלה המקומית {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=הפעילות {0} דורשת שהטבלה המקומית {1} תאחסן נתונים באחסון האובייקט.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=הפעילות {0} דורשת שהטבלה המקומית {1} תאחסן נתונים בבסיס הנתונים.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=מתחיל בהסרת רשומות שנמחקו עבור טבלה מקומית {0}.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=מתחיל במחיקת כל הרשומות עבור טבלה מקומית {0}.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=מתחיל במחיקת כל הרשומות עבור טבלה מקומית {0} בהתאם לתנאי המסנן {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=אירעה שגיאה במהלך הסרת רשומות שנמחקו עבור טבלה מקומית {0}.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=אירעה שגיאה במהלך מחיקת כל הרשומות עבור טבלה מקומית {0}.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=מוחק את כל הרשומות שעובדו במלואן עם סוג השינוי ''נמחק'' שישנות יותר מ-{0} ימים.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=מוחק את כל הרשומות שעובדו במלואן עם סוג השינוי ''נמחק'' שישנות יותר מ-{0}.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} רשומות נמחקו.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} רשומות סומנו למחיקה.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=הסרת רשומות שנמחקו עבור הטבלה המקומית {0} הושלמה.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=מחיקת כל הרשומות עבור הטבלה המקומית {0} הושלמה.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=מתחיל לסמן רשומות כ''נמחק'' עבור טבלה מקומית {0}.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=מתחיל לסמן רשומות כ"נמחק" עבור טבלה מקומית {0} בהתאם לתנאי המסנן {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=מסמן רשומות כ''נמחק'' עבור הטבלה המקומית {0} הושלמה.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=שינויים בנתונים נטענים באופן זמני בטבלה {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=שינויים בנתונים נטענו באופן זמני בטבלה {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=שינויים בנתונים עובדו ונמחקו מהטבלה {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=פרטי חיבור.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=מתחיל מיטוב טבלה מקומית (קובץ).
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=התרחשה שגיאה בעת מיטוב טבלה מקומית (קובץ).
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=התרחשה שגיאה. מיטוב טבלה מקומית (קובץ) נעצר.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=ממטב טבלה מקומית (קובץ)...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=טבלה מקומית (קןבץ) מוטבה.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=טבלה מקומית (קןבץ) ממוטבת.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=הטבלה ממוטבת עם עמודות הזמנת-Z: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=התרחשה שגיאה. קטיעת טבלה מקומית (קובץ) נעצרה.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=קטיעת טבלה מקומית (קובץ)...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=מיקום מאגר נכנס עבור טבלה מקומית (קובץ) שוחרר.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=מתחיל שאיבת (מחיקת כל הרשומות שעובדו במלואן) טבלה מקומית (קובץ).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=התרחשה שגיאה בעת שאיבת טבלה מקומית (קובץ).
#XMSG: Task log message
LTF_VACUUM_STOPPED=התרחשה שגיאה. שאיבת טבלה מקומית (קובץ) נעצרה.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=שאיבת טבלה מקומית (קובץ)...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=השאיבה הושלמה.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=מוחק את כל הרשומות שעובדו במלואן שישנות יותר מ-{0} ימים.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=מוחק את כל הרשומות שעובדו במלואן שישנות יותר מ-{0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=מתחיל מיזוג רשומות חדשות עם טבלה מקומית (קובץ).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=מתחיל למזג רשומות חדשות עם טבלה מקומית (קובץ) באמצעות ההגדרה "מזג נתונים אוטומטית".
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=מתחיל למזג רשומות חדשות עם טבלה מקומית (קובץ). משימה זו בוצעה באמצעות בקשת המיזוג של API.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=ממזג רשומות חדשות עם טבלה מקומית (קובץ).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=התרחשה שגיאה בעת מיזוג רשומות חדשות עם טבלה מקומית (קובץ).
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=טבלה מקומית (קןבץ) מוזגה.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=התרחשה שגיאה. מיזוג טבלה מקומית (קובץ) נעצר.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=המיזוג של טבלה מקומית (קובץ) בשל שגיאה, אך הפעולה הצליחה חלקית, ונתונים מסוימים כבר מוזגו.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=אירעה שגיאת Timeout. הפעילות {0} רצה במשך {1} שעות.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=לא ניתן להתחיל את הביצוע האסינכרוני בשל עומס מערכת גבוה. פתח את "מעקב המערכת" ובדוק את המשימות המופעלות.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=הביצוע האסינכרוני בוטל.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=המשימה {0} הופעלה במגבלות הזיכרון של {1} ו-{2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=משימה {0} הופעלה עם זיהוי משאב {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=פונקציית 'חפש והחלף' החלה בטבלה מקומית (קובץ).
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=פונקציית 'חפש והחלף' הושלמה בטבלה מקומית (קובץ).
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=פונקציית 'חפש והחלף' נכשלה בטבלה מקומית (קובץ).

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=התרחשה שגיאה. עדכון סטטיסטיקה עבור טבלה מקומית (קובץ) נעצר.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=המשימה נכשלה בגלל שגיאת זיכרון חסר בבסיס הנתונים של SAP HANA.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=המשימה נכשלה בגלל דחיית בקרת קבלה של SAP HANA.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=המשימה נכשלה בגלל יותר מדי חיבורי SAP HANA פעילים.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=לא ניתן היה לבצע את פעולת הניסיון החוזר מכיוון שניסיונות חוזרים מותרים ברמת האב של שרשרת משימות מקוננת.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=יומנים ממשימות בנות שנכשלו אינם זמינים יותר לניסיון חוזר כדי להמשיך.


####Metrics Labels

performanceOptimized=מותאם לביצועים
memoryOptimized=מותאם לזיכרון

JOB_EXECUTION=ביצוע עבודה
EXECUTION_MODE=מצב הפעלה
NUMBER_OF_RECORDS_OVERALL=מספר רשומות באחסון קבוע
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=מספר הרשומות שנקראו ממקור מרוחק
RUNTIME_MS_REMOTE_EXECUTION_TIME=שעת עיבוד מקור מרוחק
MEMORY_CONSUMPTION_GIB=שיא שימוש בזיכרון - SAP HANA
NUMBER_OF_PARTITIONS=מספר המחיצות
MEMORY_CONSUMPTION_GIB_OVERALL=שיא שימוש בזיכרון - SAP HANA
NUMBER_OF_PARTITIONS_LOCKED=מספר המחיצות הנעולות
PARTITIONING_COLUMN=עמודת חלוקה למחיצות
HANA_PEAK_CPU_TIME=זמן כולל SAP HANA  של CPU 
USED_IN_DISK=אחסון שנמצא בשימוש
INPUT_PARAMETER_PARAMETER_VALUE=פרמטר קלט
INPUT_PARAMETER=פרמטר קלט
ECN_ID=שם צומת חישוב גמיש

DAC=בקרי גישה לנתונים
YES=כן
NO=לא
noofrecords=מספר רשומות
partitionpeakmemory=שיא שימוש בזיכרון - SAP HANA
value=ערך
metricsTitle=מדדים ({0})
partitionmetricsTitle=מחיצות ({0})
partitionLabel=מחיצה
OthersNotNull=ערכים שאינם כלולים בטווחים
OthersNull=ערכים ריקים
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=הגדרות בשימוש עבור הפעלת אחסון נתונים קבוע אחרונה:
#XMSG: Message for input parameter name
inputParameterLabel=פרמטר קלט
#XMSG: Message for input parameter value
inputParameterValueLabel=ערך
#XMSG: Message for persisted data
inputParameterPersistedLabel=אוחסן באחסון קבוע בשעה
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=מחק נתונים
REMOVE_DELETED_RECORDS=הסר רשומות מחוקות
MERGE_FILES=מזג קבצים
OPTIMIZE_FILES=מטב קבצים
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=הצג במעקב SAP BW Bridge

ANALYZE_PERFORMANCE=נתח ביצועים
CANCEL_ANALYZE_PERFORMANCE=בטל ניתוח ביצועים

#XFLD: Label for frequency column
everyLabel=כל
#XFLD: Plural Recurrence text for Hour
hoursLabel=שעות
#XFLD: Plural Recurrence text for Day
daysLabel=ימים
#XFLD: Plural Recurrence text for Month
monthsLabel=חודשים
#XFLD: Plural Recurrence text for Minutes
minutesLabel=דקות

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">הצג את המדריך לפתרון בעיות של אחסון קבוע</a>
#XTXT TEXT for view persistency guide link
OOMMessage=אין זיכרון לתהליך. לא ניתן לאחסן את הנתונים באחסון קבוע עבור תצוגה "{0}". עיין בפורטל העזרה למידע נוסף על שגיאות של חוסר בזיכרון. שקול לבדוק את ''מנתח תצוגה'' כדי לנתח את התצוגה עבור מורכבות צריכת הזיכרון.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=לא ישים
OPEN_BRACKET=(
CLOSE_BRACKET=)
