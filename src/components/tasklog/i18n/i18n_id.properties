
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Rincian Log
#XFLD: Header
TASK_LOGS=Log Tugas ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Dieksekusi ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Lihat Rincian
#XFLD: Button text
STOP=Hentikan Eksekusi
#XFLD: Label text
RUN_START=Waktu Mulai Eksekusi Terakhir
#XFLD: Label text
RUN_END=Waktu Selesai Eksekusi Terakhir
#XFLD: Label text
RUNTIME=Durasi
#XTIT: Count for Messages
txtDetailMessages=Pesan ({0})
#XFLD: Label text
TIME=Cap Waktu
#XFLD: Label text
MESSAGE=Pesan
#XFLD: Label text
TASK_STATUS=Kategori
#XFLD: Label text
TASK_ACTIVITY=Aktivitas
#XFLD: Label text
RUN_START_DETAILS=Mulai
#XFLD: Label text
RUN_END_DETAILS=Berakhir
#XFLD: Label text
LOGS=Dieksekusi
#XFLD: Label text
STATUS=Status
#XFLD: Label text
RUN_STATUS=Status Eksekusi
#XFLD: Label text
Runtime=Durasi
#XFLD: Label text
RuntimeTooltip=Durasi (jj : mm : dd)
#XFLD: Label text
TRIGGEREDBY=Dipicu oleh
#XFLD: Label text
TRIGGEREDBYNew=Dieksekusi oleh
#XFLD: Label text
TRIGGEREDBYNewImp=Eksekusi Dimulai Oleh
#XFLD: Label text
EXECUTIONTYPE=Tipe Pelaksanaan
#XFLD: Label text
EXECUTIONTYPENew=Tipe Eksekusi
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Ruang Rantai Induk
#XFLD: Refresh tooltip
TEXT_REFRESH=Segarkan
#XFLD: view Details link
VIEW_ERROR_DETAILS=Lihat Rincian
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Unduh Rincian Tambahan
#XMSG: Download completed
downloadStarted=Pengunduhan Dimulai
#XMSG: Error while downloading content
errorInDownload=Terjadi kesalahan saat mengunduh.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Lihat Rincian
#XBTN: cancel button of task details dialog
TXT_CANCEL=Batalkan
#XBTN: back button from task details
TXT_BACK=Kembali
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Tugas Selesai
#XFLD: Log message with failed status
MSG_LOG_FAILED=Tugas Gagal
#XFLD: Master and detail table with no data
No_Data=Tidak Ada Data
#XFLD: Retry tooltip
TEXT_RETRY=Coba lagi
#XFLD: Cancel Run label
TEXT_CancelRun=Batalkan Eksekusi
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Bersihkan Muatan yang Gagal
#XMSG:button copy sql statement
txtSQLStatement=Salin Pernyataan SQL
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Buka Pemantau Kueri Jarak Jauh
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=Untuk menampilkan pernyataan SQL jarak jauh, klik "Buka Pemantau Kueri Jarak Jauh".
#XMSG:button ok
txtOk=Oke
#XMSG: button close
txtClose=Tutup
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=Pembatalan tindakan yang dieksekusi untuk objek "{0}" telah dimulai.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=Pembatalan tindakan yang dieksekusi untuk objek "{0}" telah gagal.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=Pembatalan tindakan yang dieksekusi untuk objek "{0}" tidak dapat dilakukan lagi karena status replikasi telah berubah.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Tidak ada log tugas yang memiliki status Sedang Dieksekusi.
#XMSG: message for conflicting task
Task_Already_Running=Tugas yang bertentangan sudah dieksekusi untuk objek ''{0}''.
#XFLD: Label for no task log with running state title
actionInfo=Informasi Tindakan
#XMSG Copied to clipboard
copiedToClip=Disalin ke Clipboard
#XFLD copy
Copy=Salin
#XFLD copy correlation ID
CopyCorrelationID=Salin ID Korelasi
#XFLD Close
Close=Tutup
#XFLD: show more Label
txtShowMore=Tampilkan Selengkapnya
#XFLD: message Label
messageLabel=Pesan:
#XFLD: details Label
detailsLabel=Rincian:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Menjalankan Pernyataan \r\n SQL:
#XFLD:statementId Label
statementIdLabel=ID Pernyataan:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Jumlah Pernyataan SQL \r\n Jarak Jauh:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Jumlah Pernyataan
#XFLD: Space Label
txtSpaces=Ruang
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Ruang Tidak Diotorisasi
#XFLD: Privilege Error Text
txtPrivilegeError=Anda tidak memiliki hak istimewa yang mencukupi untuk menampilkan data ini.
#XFLD: Label for Object Header
DATA_ACCESS=Akses Data
#XFLD: Label for Object Header
SCHEDULE=Jadwal
#XFLD: Label for Object Header
DETAILS=Rincian
#XFLD: Label for Object Header
LATEST_UPDATE=Pembaruan Terakhir
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Perubahan Terakhir (Sumber)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Frekuensi Penyegaran
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Frekuensi yang Dijadwalkan
#XFLD: Label for Object Header
NEXT_RUN=Eksekusi Selanjutnya
#XFLD: Label for Object Header
CONNECTION=Koneksi
#XFLD: Label for Object Header
DP_AGENT=Agen DP
#XFLD: Label for Object Header
USED_IN_MEMORY=Memori yang Digunakan untuk Penyimpanan (MiB)
#XFLD: Label for Object Header
USED_DISK=Disk yang Digunakan untuk Penyimpanan (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Ukuran dalam Memori (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Ukuran pada Disk (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Jumlah Catatan

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Tetapkan Ke Gagal
SET_TO_FAILED_ERR=Tugas ini dieksekusi tetapi pengguna menetapkan status tugas ini ke GAGAL.
#XFLD: Label for stopped failed
FAILLOCKED=Eksekusi Sedang Diproses
#XFLD: sub status STOPPED
STOPPED=Dihentikan
STOPPED_ERR=Tugas ini dihentikan, tetapi tidak ada rollback yang dilakukan.
#XFLD: sub status CANCELLED
CANCELLED=Dibatalkan
CANCELLED_ERR=Eksekusi tugas ini dibatalkan, setelah dimulai. Dalam hal ini, data di-roll back dan dipulihkan ke status yang ada sebelum eksekusi tugas tersebut awalnya dipicu.
#XFLD: sub status LOCKED
LOCKED=Terkunci
LOCKED_ERR=Tugas yang sama sudah dieksekusi, jadi pekerjaan tugas ini tidak dapat dieksekusi secara paralel dengan pelaksanaan tugas yang ada.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Kesalahan Tugas
TASK_EXCEPTION_ERR=Tugas ini mengalami kesalahan yang tidak spesifik selama pelaksanaan.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Kesalahan Pelaksanaan Tugas
TASK_EXECUTE_EXCEPTION_ERR=Tugas ini mengalami kesalahan selama pelaksanaan.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Tidak Diotorisasi
UNAUTHORIZED_ERR=Pengguna tidak dapat diautentikasi, telah dikunci, atau dihapus permanen.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Dilarang
FORBIDDEN_ERR=Pengguna yang ditetapkan tidak memiliki hak istimewa yang diperlukan untuk menjalankan tugas ini.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Tidak Dipicu
FAIL_NOT_TRIGGERED_ERR=Pekerjaan tugas ini tidak dapat dijalankan karena gangguan sistem atau beberapa bagian dari sistem basis data tidak tersedia pada saat pelaksanaan yang direncanakan. Tunggu waktu pelaksanaan pekerjaan terjadwal berikutnya atau jadwalkan ulang pekerjaan.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Jadwal Dibatalkan
SCHEDULE_CANCELLED_ERR=Pekerjaan tugas ini tidak dapat dijalankan karena kesalahan internal. Hubungi Dukungan SAP dan berikan mereka id korelasi dan cap waktu dari informasi rincian log pekerjaan tugas ini.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Eksekusi Sebelumnya Sedang Diproses
SUCCESS_SKIPPED_ERR=Pelaksanaan tugas ini belum dipicu karena eksekusi sebelumnya dari tugas yang sama sedang diproses.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Pemilik Tidak Ditemukan
FAIL_OWNER_MISSING_ERR=Pekerjaan tugas ini tidak dapat dijalankan karena tidak memiliki pengguna sistem yang ditetapkan. Tetapkan pengguna pemilik untuk pekerjaan tersebut.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Persetujuan Tidak Tersedia
FAIL_CONSENT_NOT_AVAILABLE_ERR=Anda belum mengizinkan SAP untuk mengeksekusi rantai tugas atau menjadwalkan tugas integrasi data atas nama Anda. Pilih opsi yang disediakan untuk memberikan persetujuan Anda.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Persetujuan Kedaluwarsa
FAIL_CONSENT_EXPIRED_ERR=Otorisasi yang mengizinkan SAP untuk mengeksekusi rantai tugas atau menjadwalkan tugas integrasi data atas nama Anda telah kedaluwarsa. Pilih opsi yang disediakan untuk memperbarui persetujuan Anda.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Persetujuan Tidak Divalidasi
FAIL_CONSENT_INVALIDATED_ERR=Tugas ini tidak dapat dijalankan, biasanya terjadi karena perubahan konfigurasi Penyedia Identitas penyewa. Dalam hal ini, tidak ada pekerjaan tugas baru yang dapat dieksekusi atau dijadwalkan atas nama pengguna yang terdampak. Jika pengguna yang ditetapkan masih ada di IdP baru, cabut persetujuan penjadwalan, lalu berikan lagi. Jika pengguna yang ditetapkan tidak ada lagi, tetapkan pemilik pekerjaan tugas baru dan berikan persetujuan penjadwalan tugas yang diperlukan. Lihat catatan SAP berikut: https://launchpad.support.sap.com/#/notes/3089828 untuk informasi selengkapnya.
TASK_EXECUTOR_ERROR=Pelaksana Tugas
TASK_EXECUTOR_ERROR_ERR=Tugas ini mengalami kesalahan internal, kemungkinan selama langkah persiapan untuk pelaksanaan, dan tugas tidak dapat dimulai.
PREREQ_NOT_MET=Prasyarat tidak terpenuhi
PREREQ_NOT_MET_ERR=Tugas ini tidak dapat dieksekusi karena terdapat masalah dengan definisinya. Misalnya, objek tidak disebarkan, rantai tugas mengandung logika tak terbatas, atau SQL dalam suatu tampilan tidak valid.
RESOURCE_LIMIT_ERROR=Kesalahan pada Batas Sumber Daya
RESOURCE_LIMIT_ERROR_ERR=Saat ini tidak dapat mengerjakan tugas karena sumber daya yang mencukupi tidak tersedia atau sedang sibuk.
FAIL_CONSENT_REFUSED_BY_UMS=Persetujuan Ditolak
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Tugas ini tidak dapat dijalankan, dalam eksekusi atau rantai tugas yang dijadwalkan, karena terdapat perubahan dalam konfigurasi Penyedia Identitas pengguna pada penyewa. Untuk informasi selengkapnya, lihat catatan SAP berikut: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Dijadwalkan
#XFLD: status text
SCHEDULEDNew=Permanen
#XFLD: status text
PAUSED=Dijeda
#XFLD: status text
DIRECT=Langsung
#XFLD: status text
MANUAL=Manual
#XFLD: status text
DIRECTNew=Sederhana
#XFLD: status text
COMPLETED=Selesai
#XFLD: status text
FAILED=Gagal
#XFLD: status text
RUNNING=Sedang Dieksekusi
#XFLD: status text
none=Tidak Ada
#XFLD: status text
realtime=Waktu Nyata
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Subtugas
#XFLD: New Data available in the file
NEW_DATA=Data Baru

#XFLD: text for values shown in column Replication Status
txtOff=Nonaktif
#XFLD: text for values shown in column Replication Status
txtInitializing=Memulai
#XFLD: text for values shown in column Replication Status
txtLoading=Memuat
#XFLD: text for values shown in column Replication Status
txtActive=Aktif
#XFLD: text for values shown in column Replication Status
txtAvailable=Tersedia
#XFLD: text for values shown in column Replication Status
txtError=Kesalahan
#XFLD: text for values shown in column Replication Status
txtPaused=Dijeda
#XFLD: text for values shown in column Replication Status
txtDisconnected=Terputus
#XFLD: text for partially Persisted views
partiallyPersisted=Persisten Sebagian

#XFLD: activity text
REPLICATE=Replikasi
#XFLD: activity text
REMOVE_REPLICATED_DATA=Hapus Data yang Direplikasi
#XFLD: activity text
DISABLE_REALTIME=Nonaktifkan Replikasi Data Waktu Nyata
#XFLD: activity text
REMOVE_PERSISTED_DATA=Hapus Data Persisten
#XFLD: activity text
PERSIST=Persisten
#XFLD: activity text
EXECUTE=Jalankan
#XFLD: activity text
TASKLOG_CLEANUP=Pembersihan_Tacklog
#XFLD: activity text
CANCEL_REPLICATION=Batalkan Replikasi
#XFLD: activity text
MODEL_IMPORT=Impor Model
#XFLD: activity text
NONE=Tidak Ada
#XFLD: activity text
CANCEL_PERSISTENCY=Batalkan Persistensi
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Analisis Tampilan
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Batalkan Penganalisis Tampilan

#XFLD: severity text
INFORMATION=Informasi
#XFLD: severity text
SUCCESS=Berhasil
#XFLD: severity text
WARNING=Peringatan
#XFLD: severity text
ERROR=Kesalahan
#XFLD: text for values shown for Ascending sort order
SortInAsc=Urutkan Naik
#XFLD: text for values shown for Descending sort order
SortInDesc=Urutkan Menurun
#XFLD: filter text for task log columns
Filter=Filter
#XFLD: object text for task log columns
Object=Objek
#XFLD: space text for task log columns
crossSpace=Ruang

#XBUT: label for remote data access
REMOTE=Jarak Jauh
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Direplikasi (Waktu Nyata)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Direplikasi (Snapshot)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=Replikasi waktu nyata diblokir karena kesalahan. Setelah kesalahan diperbaiki, Anda dapat menggunakan tindakan “Coba lagi” untuk melanjutkan replikasi waktu nyata.
ERROR_MSG=Replikasi waktu nyata diblokir karena kesalahan.
RETRY_FAILED_ERROR=Proses coba lagi gagal dengan kesalahan.
LOG_INFO_DETAILS=Tidak ada log yang dibuat saat data direplikasi dalam mode waktu nyata. Log yang ditampilkan terkait dengan tindakan sebelumnya.

#XBUT: Partitioning label
partitionMenuText=Membuat Partisi
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Buat Partisi
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Edit Partisi
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Hapus Permanen Partisi
#XFLD: Initial text
InitialPartitionText=Tentukan partisi dengan menentukan kriteria untuk membagi himpunan data yang besar ke himpunan yang lebih kecil.
DefinePartition=Tentukan Partisi
#XFLD: Message text
partitionChangedInfo=Definisi partisi telah berubah sejak replikasi terakhir. Perubahan akan diterapkan pada muatan data berikutnya.
#XFLD: Message text
REAL_TIME_WARNING=Pembuatan partisi hanya diterapkan saat memuat snapshot baru. Pembuatan partisi tidak diterapkan untuk replikasi waktu nyata.
#XFLD: Message text
loadSelectedPartitions=Mulai mempersistensi data untuk partisi yang dipilih dari ''{0}''
#XFLD: Message text
loadSelectedPartitionsError=Gagal mempersistensi data untuk partisi yang dipilih dari ''{0}''
#XFLD: Message text
viewpartitionChangedInfo=Definisi partisi telah berubah sejak eksekusi persistensi terakhir. Untuk menerapkan perubahan, muatan data selanjutnya akan berupa snapshot penuh yang mencakup partisi yang dikunci. Setelah muatan penuh ini selesai, Anda akan dapat mengeksekusi data untuk partisi tunggal.
#XFLD: Message text
viewpartitionChangedInfoLocked=Definisi partisi telah berubah sejak eksekusi persistensi terakhir. Untuk menerapkan perubahan, muatan data selanjutnya akan berupa snapshot penuh, kecuali untuk rentang partisi yang dikunci dan tidak diubah. Setelah muatan ini selesai, Anda akan dapat Memuat Partisi yang Dipilih lagi.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Replikasi Tabel
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Jadwalkan Replikasi
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Buat Jadwal
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Edit Jadwal
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Hapus Permanen Jadwal
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Muat Snapshot Baru
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Mulai Replikasi Data
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Hapus Data yang Direplikasi
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Aktifkan Akses Waktu Nyata
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Aktifkan Replikasi Data Waktu Nyata
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Nonaktifkan Replikasi Data Waktu Nyata
#XFLD: Message for replicate table action
replicateTableText=Replikasi Tabel
#XFLD: Message for replicate table action
replicateTableTextNew=Replikasi Data
#XFLD: Message to schedule task
scheduleText=Jadwalkan
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Tampilkan Persistensi
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Persistensi Data
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Muat Snapshot Baru
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Mulai Persistensi Data
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Hapus Data Persisten
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Pemrosesan yang Dipartisi
#XBUT: Label for scheduled replication
scheduledTxt=Dijadwalkan
#XBUT: Label for statistics button
statisticsTxt=Statistik
#XBUT: Label for create statistics
createStatsTxt=Buat Statistik
#XBUT: Label for edit statistics
editStatsTxt=Edit Statistik
#XBUT: Label for refresh statistics
refreshStatsTxt=Segarkan Statistik
#XBUT: Label for delete statistics
dropStatsTxt=Hapus Permanen Statistik
#XMSG: Create statistics success message
statsSuccessTxt=Mulai pembuatan statistik dengan tipe {0} untuk {1}.
#XMSG: Edit statistics success message
statsEditSuccessTxt=Mulai pengubahan tipe statistik menjadi {0} untuk {1}.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Mulai penyegaran statistik untuk {0}.
#XMSG: Drop statistics success message
statsDropSuccessTxt=Statistik berhasil dihapus permanen untuk {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=Kesalahan saat membuat statistik
#XMSG: Edit statistics error message
statsEditErrorTxt=Kesalahan saat mengubah statistik
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Kesalahan saat menyegarkan statistik
#XMSG: Drop statistics error message
statsDropErrorTxt=Kesalahan saat menghapus Statistik
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Apakah Anda yakin ingin menghapus statistik data ini?
startPersistencyAdvisorLabel=Mulai Penganalisis Tampilan

#Partition related texts
#XFLD: Label for Column
column=Kolom
#XFLD: Label for No of Partition
noOfPartitions=Jumlah Partisi
#XFLD: Label for Column
noOfParallelProcess=Jumlah Proses Paralel
#XFLD: Label text
noOfLockedPartition=No. Partisi yang Dikunci
#XFLD: Label for Partition
PARTITION=Partisi
#XFLD: Label for Column
AVAILABLE=Tersedia
#XFLD: Statistics Label
statsLabel=Statistik
#XFLD: Label text
COLUMN=Kolom:
#XFLD: Label text
PARALLEL_PROCESSES=Proses Paralel:
#XFLD: Label text
Partition_Range=Rentang Partisi
#XFLD: Label text
Name=Nama
#XFLD: Label text
Locked=Terkunci
#XFLD: Label text
Others=LAINNYA
#XFLD: Label text
Delete=Hapus Permanen
#XFLD: Label text
LoadData=Muat Partisi yang Dipilih
#XFLD: Label text
LoadSelectedData=Muat Partisi yang Dipilih
#XFLD: Confirmation text
LoadNewPersistenceConfirm=Hal ini akan memuat snapshot baru untuk semua partisi yang tidak dikunci dan diubah, tidak hanya partisi yang Anda pilih. Apakah Anda ingin melanjutkan?
#XFLD: Label text
Continue=Lanjutkan

#XFLD: Label text
PARTITIONS=Partisi
#XFLD: Label text
ADD_PARTITIONS=+ Tambahkan Partisi
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Tambahkan Partisi
#XFLD: Label text
deleteRange=Hapus Permanen Partisi
#XFLD: Label text
LOW_PLACE_HOLDER=Masukkan nilai rendah
#XFLD: Label text
HIGH_PLACE_HOLDER=Masukkan nilai tinggi
#XFLD: tooltip text
lockedTooltip=Kunci partisi setelah muatan awal

#XFLD: Button text
Edit=Edit
#XFLD: Button text
CANCEL=Batalkan

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Pembaruan Statistik Terakhir
#XFLD: Statistics Fields
STATISTICS=Statistik

#XFLD:Retry label
TEXT_Retry=Coba lagi
#XFLD:Retry label
TEXT_Retry_tooltip=Coba lagi replikasi waktu nyata setelah kesalahan diperbaiki.
#XFLD: text retry
Retry=Konfirmasi
#XMG: Retry confirmation text
retryConfirmationTxt=Replikasi waktu nyata terakhir diakhiri dengan kesalahan.\n Pastikan bahwa kesalahan telah diperbaiki dan replikasi waktu nyata dapat dimulai ulang.
#XMG: Retry success text
retrySuccess=Proses coba lagi berhasil dimulai.
#XMG: Retry fail text
retryFail=Proses coba lagi gagal.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Buat Statistik
#XMSG: activity message for edit statistics
DROP_STATISTICS=Hapus Permanen Statistik
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Segarkan Statistik
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Edit Statistik
#XMSG: Task log message started task
taskStarted=Tugas {0} telah dimulai.
#XMSG: Task log message for finished task
taskFinished=Tugas {0} berakhir dengan status {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=Tugas {0} berakhir di {2} dengan status {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=Tugas {0} memiliki parameter input.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=Tugas {0} berakhir dengan kesalahan yang tidak terduga. Status tugas telah ditetapkan ke {1}.
#XMSG: Task log message for failed task
failedToEnd=Gagal menetapkan status ke {0} atau gagal menghapus kunci.
#XMSG: Task log message
lockNotFound=Kami tidak dapat menyelesaikan proses karena kunci tidak ditemukan: tugas mungkin telah dibatalkan.
#XMSG: Task log message failed task
failedOverwrite=Tugas {0} telah dikunci oleh {1}. Oleh karena itu, tugas tersebut gagal dengan kesalahan berikut: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Kunci tugas ini diambil alih oleh tugas lain.
#XMSG: Task log message failed takeover
failedTakeover=Gagal mengambil alih tugas yang sudah ada.
#XMSG: Task log message successful takeover
successTakeover=Kunci lama harus dirilis. Kunci baru ditetapkan untuk tugas ini.
#XMSG: Tasklog Dialog Details
txtDetails=Pernyataan jarak jauh yang diproses selama eksekusi dapat ditampilkan dengan membuka pemantau kueri jarak jauh, di rincian pesan khusus partisi.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=Pernyataan SQL jarak jauh telah dihapus permanen dari basis data dan tidak dapat ditampilkan.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Kueri jarak jauh yang memiliki koneksi yang ditetapkan ke ruang lain tidak dapat ditampilkan. Masuk ke Pemantau Kueri Jarak Jauh dan gunakan ID pernyataan untuk memfilternya.
#XMSG: Task log message for parallel check error
parallelCheckError=Tugas tidak dapat diproses karena tugas lain sedang dieksekusi dan telah memblokir tugas ini.
#XMSG: Task log message for parallel running task
parallelTaskRunning=Tugas yang bertentangan sudah dieksekusi.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} selama dieksekusi dengan ID korelasi {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=Pengguna yang ditetapkan tidak memiliki hak istimewa yang diperlukan untuk menjalankan tugas ini.

#XBUT: Label for open in Editor
openInEditor=Buka di Editor
#XBUT: Label for open in Editor
openInEditorNew=Buka di Data Builder
#XFLD:Run deails label
runDetails=Rincian Eksekusi
#XFLD: Label for Logs
Logs=Log
#XFLD: Label for Settings
Settings=Pengaturan
#XFLD: Label for Save button
Save=Simpan
#XFLD: Label for Standard
Standard_PO=Kinerja yang Dioptimalkan (Disarankan)
#XFLD: Label for Hana low memory processing
HLMP_MO=Memori yang Dioptimalkan
#XFLD: Label for execution mode
ExecutionMode=Mode Eksekusi
#XFLD: Label for job execution
jobExecution=Mode Pemrosesan
#XFLD: Label for Synchronous
syncExec=Sinkron
#XFLD: Label for Asynchronous
asyncExec=Asinkron
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Gunakan Default (Asinkron, dapat berubah di masa mendatang)
#XMSG: Save settings success
saveSettingsSuccess=Mode Pelaksanaan SAP HANA diubah.
#XMSG: Save settings failure
saveSettingsFailed=Perubahan Mode Pelaksanaan SAP HANA gagal.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Pelaksanaan Pekerjaan diubah.
#XMSG: Job Execution change failed
jobExecSettingFailed=Perubahan Pelaksanaan Pekerjaan gagal.
#XMSG: Text for Type
typeTxt=Tipe
#XMSG: Text for Monitor
monitorTxt=Pemantau
#XMSG: Text for activity
activityTxt=Aktivitas
#XMSG: Text for metrics
metricsTxt=Metrik
#XTXT: Text for Task chain key
TASK_CHAINS=Rantai Tugas
#XTXT: Text for View Key
VIEWS=Tampilan
#XTXT: Text for remote table key
REMOTE_TABLES=Tabel Jarak Jauh
#XTXT: Text for Space key
SPACE=Ruang
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Node Komputasi Elastis
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Aliran Replikasi
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Pencarian Cerdas
#XTXT: Text for Local Table
LOCAL_TABLE=Tabel Lokal
#XTXT: Text for Data flow key
DATA_FLOWS=Aliran Data
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=Prosedur Skrip SQL
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=Rantai Proses BW
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Lihat di Pemantau
#XTXT: Task List header text
taskListHeader=Daftar Tugas ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Metrik untuk eksekusi aliran data historis tidak dapat diambil.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=Rincian eksekusi lengkap saat ini tidak sedang memuat. Coba segarkan.
#XFLD: Label text for the Metrices table header
metricesColLabel=Label Operator
#XFLD: Label text for the Metrices table header
metricesType=Tipe
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Jumlah Catatan
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Eksekusi Rantai Tugas
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Eksekusi rantai tugas telah dimulai.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Eksekusi rantai tugas telah dimulai untuk {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Gagal mengeksekusi rantai tugas.
#XTXT: Execute button label
runLabel=Eksekusi
#XTXT: Execute button label
runLabelNew=Mulai Eksekusi
#XMSG: Filter Object header
chainsFilteredTableHeader=Filter menurut Objek: {0}
#XFLD: Parent task chain label
parentChainLabel=Rantai Tugas Induk:
#XFLD: Parent task chain unauthorized
Unauthorized=Tidak Diizinkan untuk Melihat
#XFLD: Parent task chain label
parentTaskLabel=Tugas Induk:
#XTXT: Task status
NOT_TRIGGERED=Tidak Dipicu
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Masuk Mode Layar Penuh
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Keluar Mode Layar Penuh
#XTXT: Close Task log details right panel
closeRightColumn=Tutup Bagian
#XTXT: Sort Text
sortTxt=Pengurutan
#XTXT: Filter Text
filterTxt=Filter
#XTXT: Filter by text to show list of filters applied
filterByTxt=Filter berdasarkan
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Lebih dari 5 Menit
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Lebih dari 15 Menit
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Lebih dari 1 Jam
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Jam Terakhir
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=24 Jam Terakhir
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Bulan Terakhir
#XTXT: Messages title text
messagesText=Pesan

#XTXT Statistics information message
statisticsInfo=Statistik tidak dapat dibuat untuk tabel jarak jauh dengan akses data ''Direplikasi''. Untuk membuat statistik, hapus data yang direplikasi di Pemantau Rincian Tabel Jarak Jauh.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Masuk ke Pemantau Rincian Tabel Jarak Jauh

#XTXT: Repair latest failed run label
retryRunLabel=Coba Ulang Eksekusi Terakhir
#XTXT: Repair failed run label
retryRun=Coba Ulang Eksekusi
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Eksekusi coba ulang rantai tugas telah dimulai
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Eksekusi coba ulang rantai tugas telah gagal
#XMSG: Task chain child elements name
taskChainRetryChildObject=Tugas {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Tugas Baru
#XFLD Analyzed View
analyzedView=Tampilan yang Dianalisis
#XFLD Metrics
Metrics=Metrik
#XFLD Partition Metrics
PartitionMetrics=Metrik Partisi
#XFLD Entities
Messages=Log Tugas
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Belum ada partisi yang ditentukan
#XTXT: Description message for empty partition data
partitionEmptyDescText=Buat partisi dengan menentukan kriteria untuk membagi volume data yang lebih besar menjadi bagian yang lebih kecil dan lebih mudah dikelola.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Belum ada log yang tersedia
#XTXT: Description message for empty runs data
runsEmptyDescText=Saat Anda memulai aktivitas baru (Muat snapshot baru, mulai penganalisis tampilan...), Anda akan melihat log terkait di sini.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=Konfigurasi node komputasi elastis {0} tidak dipertahankan dengan benar. Silakan periksa definisi Anda.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Proses menambahkan node komputasi elastis {0} gagal.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Pembuatan dan aktivasi replika untuk objek sumber ''{0}''.''{1}'' di node komputasi elastis {2}, telah dimulai.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Penghapusan replika untuk objek sumber ''{0}''.''{1}'' dari node komputasi elastis {2}, telah dimulai.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Pembuatan dan aktivasi replika untuk objek sumber ''{0}''.''{1}'', telah gagal.
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Penghapusan replika untuk objek sumber ''{0}''.''{1}'', telah gagal.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Merutekan komputasi kueri analitik dari ruang {0} ke node komputasi elastis {1} telah dimulai.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Gagal merutekan komputasi kueri analitik dari ruang {0} ke node komputasi elastis terkait.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Merutekan ulang komputasi kueri analitik dari ruang {0} kembali dari node komputasi elastis {1} telah dimulai.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Gagal merutekan ulang komputasi kueri analitik dari ruang {0} kembali ke koordinator.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Rantai tugas {0} untuk node komputasi elastis {1} terkait telah dipicu.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Pembuatan rantai tugas untuk node komputasi elastis {0} telah gagal.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=Penyediaan node komputasi elastis {0} telah dimulai dengan rencana ukuran yang ditentukan: vCPU: {1}, memori (GiB): {2}, dan ukuran penyimpanan (GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=Node komputasi elastis {0} telah disediakan.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=Node komputasi elastis {0} telah dicabut.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=Operasi tidak diizinkan. Silakan hentikan node komputasi elastis {0} dan mulai ulang untuk memperbarui rencana ukuran.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Proses untuk menghapus node komputasi elastis {0} gagal.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=Eksekusi node komputasi elastis {0} saat ini habis waktunya.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=Memeriksa status node komputasi elastis {0} sedang diproses...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=Pembuatan rantai tugas untuk node komputasi elastis {0} dikunci, sehingga rantai {1} mungkin masih dieksekusi.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=Objek sumber ''{0}''.''{1}'' direplikasi sebagai dependensi tampilan ''{2}''.''{3}''.
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Tidak dapat mereplikasi tabel ''{0}''.''{1}'', karena replikasi tabel baris tidak digunakan lagi.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Node komputasi elastis maksimum per instance SAP HANA Cloud melebihi batas.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=Operasi yang dieksekusi untuk node komputasi elastis {0} masih berlangsung.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Penghapusan replika untuk tabel sumber {0} dihentikan karena ada masalah teknis. Silakan coba lagi nanti.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=Pembuatan replika untuk tabel sumber {0} dihentikan karena ada masalah teknis. Silakan coba lagi nanti.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Tidak dapat memulai node komputasi elastis karena batas penggunaan telah tercapai atau karena belum ada blok jam komputasi yang dialokasikan.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Memuat rantai tugas dan menyiapkan untuk mengeksekusi jumlah {0} tugas yang menjadi bagian rantai ini.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Tugas yang bertentangan sudah dieksekusi
#XMSG: Replication will change
txt_replication_change=Tipe replikasi akan diubah.
txt_repl_viewdetails=Lihat Rincian

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Tampaknya ada kesalahan saat mencoba kembali mengeksekusi tugas yang terbaru karena eksekusi tugas sebelumnya gagal sebelum rencana dapat dibuat.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=Ruang "{0}" dikunci.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=Aktivitas {0} memerlukan penyebaran tabel lokal {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=Aktivitas {0} memerlukan Pemindaian Delta diaktifkan untuk tabel lokal {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=Aktivitas {0} memerlukan tabel lokal {1} untuk menyimpan data di penyimpanan objek.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=Aktivitas {0} memerlukan tabel lokal {1} untuk menyimpan data di basis data.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Mulai menghapus catatan yang telah dihapus permanen untuk tabel lokal {0}.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Mulai menghapus permanen semua catatan untuk tabel lokal {0}.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Mulai menghapus permanen catatan untuk tabel lokal {0} berdasarkan kondisi filter {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Kesalahan terjadi saat menghapus catatan yang telah dihapus permanen untuk tabel lokal {0}.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Kesalahan terjadi saat menghapus permanen semua catatan untuk tabel lokal {0}.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Menghapus permanen semua catatan yang diproses sepenuhnya dengan Tipe Perubahan ''’Dihapus Permanen'', yang lebih lama dari {0} hari.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Menghapus permanen semua catatan yang diproses sepenuhnya dengan Tipe Perubahan ''’Dihapus Permanen", yang lebih lama dari {0}.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} catatan sudah dihapus permanen.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} catatan ditandai untuk dihapus permanen.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=Penghapusan catatan yang telah dihapus permanen untuk tabel lokal {0} telah selesai.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Penghapusan permanen semua catatan untuk tabel lokal {0} telah selesai.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Mulai menandai catatan sebagai "Dihapus Permanen" untuk tabel lokal {0}.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Mulai menandai catatan sebagai "Dihapus Permanen" untuk tabel lokal {0} berdasarkan kondisi filter {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Menandai catatan sebagai "Dihapus Permanen" untuk tabel lokal {0} telah selesai.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Perubahan data untuk sementara waktu sedang dimuat ke dalam tabel {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Perubahan data untuk sementara waktu dimuat ke dalam tabel {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Perubahan data telah diproses dan dihapus permanen dari tabel {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Rincian koneksi.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Mulai mengoptimalkan Tabel Lokal (File).
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Terjadi kesalahan saat mengoptimalkan Tabel Lokal (File).
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Terjadi kesalahan. Pengoptimalan Tabel Lokal (File) telah dihentikan.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Mengoptimalkan Tabel Lokal (File)...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=Tabel Lokal (File) telah dioptimalkan.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=Tabel Lokal (File) dioptimalkan.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=Tabel telah dioptimalkan dengan kolom Z-Order: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Terjadi kesalahan. Pengosongan Tabel Lokal (File) telah dihentikan.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Mengosongkan Tabel Lokal (File)...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=Lokasi buffer inbound untuk Tabel Lokal (File) telah dihilangkan.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Mulai membersihkan (menghapus permanen semua catatan yang telah diproses sepenuhnya) Tabel Lokal (File)
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Terjadi kesalahan saat membersihkan Tabel Lokal (File).
#XMSG: Task log message
LTF_VACUUM_STOPPED=Terjadi kesalahan. Pembersihan Tabel Lokal (File) telah dihentikan.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Membersihkan Tabel Lokal (File)...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=Pembersihan selesai.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Menghapus permanen semua catatan yang diproses sepenuhnya yang lebih lama dari {0} hari.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Menghapus permanen semua catatan yang diproses sepenuhnya yang lebih lama dari {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Mulai menggabungkan catatan baru dengan Tabel Lokal (File).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Proses penggabungan catatan baru dengan Tabel Lokal (File) telah dimulai menggunakan pengaturan "Gabungkan Data Secara Otomatis".
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Memulai penggabungan catatan baru dengan Tabel Lokal (File). Tugas ini diinisiasi melalui Permintaan Penggabungan API.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Menggabungkan catatan baru dengan Tabel Lokal (File).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Terjadi kesalahan saat menggabungkan catatan baru dengan Tabel Lokal (File).
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=Tabel Lokal (File) digabungkan.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Terjadi kesalahan. Penggabungan Tabel Lokal (File) telah dihentikan.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=Penggabungan Tabel Lokal (File) gagal karena terjadi kesalahan, tetapi operasi ini berhasil sebagian dan beberapa data telah digabungkan.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Terjadi kesalahan pada batas waktu. Aktivitas {0} telah dieksekusi selama {1} jam.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=Pelaksanaan asinkron tidak dapat dimulai karena adanya beban sistem yang tinggi. Buka "Pemantau Sistem" dan periksa tugas yang dieksekusi.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=Pelaksanaan asinkron telah dibatalkan.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=Tugas {0} dieksekusi dalam batasan memori antara {1} dan {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=Tugas {0} telah dieksekusi dengan ID sumber daya {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=Operasi temukan dan ganti dalam Tabel Lokal (File) telah dimulai.
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Operasi temukan dan ganti dalam Tabel Lokal (File) telah selesai.
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Operasi temukan dan ganti dalam Tabel Lokal (File) gagal.

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Terjadi kesalahan. Pembaruan statistik untuk Tabel Lokal (File) telah dihentikan.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=Tugas gagal karena kesalahan kehabisan memori pada basis data SAP HANA.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=Tugas gagal karena adanya Penolakan Kontrol Penerimaan SAP HANA.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=Tugas gagal karena terlalu banyak koneksi SAP HANA yang aktif.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=Operasi Coba Lagi tidak dapat dilakukan karena percobaan ulang hanya diizinkan pada induk dari rantai tugas bertingkat.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Catatan tugas turunan yang gagal sudah tidak lagi tersedia untuk memproses Coba Lagi.


####Metrics Labels

performanceOptimized=Kinerja yang Dioptimalkan
memoryOptimized=Memori yang Dioptimalkan

JOB_EXECUTION=Pelaksanaan Pekerjaan
EXECUTION_MODE=Mode Eksekusi
NUMBER_OF_RECORDS_OVERALL=Jumlah Catatan yang Disimpan
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Jumlah Catatan yang dibaca dari Sumber Jarak Jauh
RUNTIME_MS_REMOTE_EXECUTION_TIME=Waktu Pemrosesan Sumber Jarak Jauh
MEMORY_CONSUMPTION_GIB=Memori Puncak SAP HANA
NUMBER_OF_PARTITIONS=Jumlah Partisi
MEMORY_CONSUMPTION_GIB_OVERALL=Memori Puncak SAP HANA
NUMBER_OF_PARTITIONS_LOCKED=Jumlah Partisi yang Dikunci
PARTITIONING_COLUMN=Kolom Pembuatan Partisi
HANA_PEAK_CPU_TIME=Total Waktu CPU SAP HANA
USED_IN_DISK=Penyimpanan yang Digunakan
INPUT_PARAMETER_PARAMETER_VALUE=Parameter Input
INPUT_PARAMETER=Parameter Input
ECN_ID=Nama Node Komputasi Elastis

DAC=Kontrol Akses Data
YES=Ya
NO=Tidak
noofrecords=Jumlah Catatan
partitionpeakmemory=Memori Puncak SAP HANA
value=Nilai
metricsTitle=Metrik ({0})
partitionmetricsTitle=Partisi ({0})
partitionLabel=Partisi
OthersNotNull=Nilai yang tidak termasuk dalam rentang
OthersNull=Nilai yang kosong
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Pengaturan yang digunakan untuk mengeksekusi persistensi data terakhir:
#XMSG: Message for input parameter name
inputParameterLabel=Parameter Input
#XMSG: Message for input parameter value
inputParameterValueLabel=Nilai
#XMSG: Message for persisted data
inputParameterPersistedLabel=Persisten Pada
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Hapus Permanen Data
REMOVE_DELETED_RECORDS=Hapus Catatan yang Telah Dihapus Permanen
MERGE_FILES=Gabungkan File
OPTIMIZE_FILES=Optimalkan File
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Lihat di Pemantau SAP BW Bridge

ANALYZE_PERFORMANCE=Analisis Kinerja
CANCEL_ANALYZE_PERFORMANCE=Batalkan Analisis Kinerja

#XFLD: Label for frequency column
everyLabel=Setiap
#XFLD: Plural Recurrence text for Hour
hoursLabel=Jam
#XFLD: Plural Recurrence text for Day
daysLabel=Hari
#XFLD: Plural Recurrence text for Month
monthsLabel=Bulan
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Menit

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Panduan pemecahan masalah terkait Persistensi Tampilan</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Memori tidak mencukupi. Data untuk tampilan "{0}" tidak dapat dipersistensi. Buka Help Portal untuk informasi selengkapnya mengenai kesalahan memori yang tidak mencukupi. Pertimbangkan untuk menggunakan Penganalisis Tampilan guna menganalisis tingkat penggunaan memori pada tampilan tersebut.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Tidak Berlaku
OPEN_BRACKET=(
CLOSE_BRACKET=)
