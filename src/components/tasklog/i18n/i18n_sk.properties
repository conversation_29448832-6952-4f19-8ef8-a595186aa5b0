
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Detaily protokolu
#XFLD: Header
TASK_LOGS=Protokoly úloh ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Chody ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Zobraziť detaily
#XFLD: Button text
STOP=Zastaviť chod
#XFLD: Label text
RUN_START=Začiatok posledného chodu
#XFLD: Label text
RUN_END=Koniec posledného chodu
#XFLD: Label text
RUNTIME=Trvanie
#XTIT: Count for Messages
txtDetailMessages=Hlásenia ({0})
#XFLD: Label text
TIME=Časová známka
#XFLD: Label text
MESSAGE=Hlásenie
#XFLD: Label text
TASK_STATUS=Kategória
#XFLD: Label text
TASK_ACTIVITY=Aktivita
#XFLD: Label text
RUN_START_DETAILS=Začiatok
#XFLD: Label text
RUN_END_DETAILS=Koniec
#XFLD: Label text
LOGS=Chody
#XFLD: Label text
STATUS=Status
#XFLD: Label text
RUN_STATUS=Status chodu
#XFLD: Label text
Runtime=Trvanie
#XFLD: Label text
RuntimeTooltip=Trvanie (hh : mm : ss)
#XFLD: Label text
TRIGGEREDBY=Spustil
#XFLD: Label text
TRIGGEREDBYNew=Spustil/a
#XFLD: Label text
TRIGGEREDBYNewImp=Chod spustil
#XFLD: Label text
EXECUTIONTYPE=Typ vykonania
#XFLD: Label text
EXECUTIONTYPENew=Typ chodu
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Priestor nadradeného reťazca
#XFLD: Refresh tooltip
TEXT_REFRESH=Obnoviť
#XFLD: view Details link
VIEW_ERROR_DETAILS=Zobraziť detaily
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Prevziať ďalšie detaily
#XMSG: Download completed
downloadStarted=Prevziať spustené
#XMSG: Error while downloading content
errorInDownload=Počas preberania sa vyskytla chyba.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Zobraziť detaily
#XBTN: cancel button of task details dialog
TXT_CANCEL=Zrušiť
#XBTN: back button from task details
TXT_BACK=Späť
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Úloha dokončená
#XFLD: Log message with failed status
MSG_LOG_FAILED=Úloha zlyhala
#XFLD: Master and detail table with no data
No_Data=Žiadne údaje
#XFLD: Retry tooltip
TEXT_RETRY=Znova
#XFLD: Cancel Run label
TEXT_CancelRun=Zrušiť chod
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Nepodarilo sa načítať očistenie
#XMSG:button copy sql statement
txtSQLStatement=Skopírovať príkaz SQL
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Otvoriť monitor vzdialených dotazov
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=Ak chcete zobraziť vzdialené príkazy SQL, kliknite na „Otvoriť monitor vzdialených dotazov“.
#XMSG:button ok
txtOk=OK
#XMSG: button close
txtClose=Zavrieť
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=Akcia zrušenia chodu pre objekt „{0}“ bola spustená.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=Akcia zrušenia chodu pre objekt „{0}“ zlyhala.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=Akcia zrušenia chodu pre objekt „{0}“ už nie je možná, pretože sa zmenil status replikácie.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Žiadne protokoly úloh nemajú status Prebieha
#XMSG: message for conflicting task
Task_Already_Running=Konfliktná úloha už prebieha pre objekt „{0}“.
#XFLD: Label for no task log with running state title
actionInfo=Informácie o akcii
#XMSG Copied to clipboard
copiedToClip=Skopírované do schránky
#XFLD copy
Copy=Kopírovať
#XFLD copy correlation ID
CopyCorrelationID=Kopírovať ID korelácie
#XFLD Close
Close=Zavrieť
#XFLD: show more Label
txtShowMore=Zobraziť viac
#XFLD: message Label
messageLabel=Hlásenie:
#XFLD: details Label
detailsLabel=Detaily:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Vykonávanie príkazu SQL \r\n:
#XFLD:statementId Label
statementIdLabel=ID príkazu:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Počet vzdialených príkazov SQL \r\n:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Počet príkazov
#XFLD: Space Label
txtSpaces=Priestor
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Neautorizované priestory
#XFLD: Privilege Error Text
txtPrivilegeError=Nemáte dostatočné oprávnenia na zobrazenie ukážky týchto údajov.
#XFLD: Label for Object Header
DATA_ACCESS=Prístup k údajom
#XFLD: Label for Object Header
SCHEDULE=Naplánovať
#XFLD: Label for Object Header
DETAILS=Detaily
#XFLD: Label for Object Header
LATEST_UPDATE=Najnovšia aktualizácia
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Posledná zmena (zdroj)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Frekvencia obnovenia
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Plánovaná frekvencia
#XFLD: Label for Object Header
NEXT_RUN=Ďalší chod
#XFLD: Label for Object Header
CONNECTION=Pripojenie
#XFLD: Label for Object Header
DP_AGENT=Agent poskytovania údajov
#XFLD: Label for Object Header
USED_IN_MEMORY=Pamäť používaná na ukladanie (MiB)
#XFLD: Label for Object Header
USED_DISK=Disk používaný na ukladanie (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Vnútorná pamäť (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Veľkosť na disku (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Počet záznamov

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Nastaviť ako neúspešné
SET_TO_FAILED_ERR=Táto úloha bola spustený, ale používateľ nastavil jej status na NEÚSPEŠNÉ.
#XFLD: Label for stopped failed
FAILLOCKED=Spustenie už prebieha
#XFLD: sub status STOPPED
STOPPED=Zastavené
STOPPED_ERR=Táto úloha bola zastavený, ale nebol vykonaný rollback.
#XFLD: sub status CANCELLED
CANCELLED=Zrušené
CANCELLED_ERR=Táto úloha bola zrušená po spustení. V takom prípade vykonajte rollback údajov a obnovenie stavu, ktorý existoval pred iniciálnym spustením chodu úlohy.
#XFLD: sub status LOCKED
LOCKED=Blokované
LOCKED_ERR=Rovnaká úloha už prebiehala, preto túto úlohu nie je možné paralelne vykonať s existujúcim vykonaním úlohy.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Výnimka úlohy
TASK_EXCEPTION_ERR=Pri tejto úlohe sa vyskytla nešpecifikovaná chyba počas vykonania.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Výnimka vykonania úlohy
TASK_EXECUTE_EXCEPTION_ERR=Pri tejto úlohe sa vyskytla chyba počas vykonania.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Neoprávnené
UNAUTHORIZED_ERR=Používateľ nebol oprávnený, bol blokovaný alebo odstránený.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Zakázané
FORBIDDEN_ERR=Priradený používateľ nemá potrebné oprávnenia na vykonanie tejto úlohy.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Nespustené
FAIL_NOT_TRIGGERED_ERR=Túto úlohu nebolo možné vykonať z dôvodu výpadku systému a nedostupnosti časti databázového systému v čase plánovaného vykonania. Počkajte do ďalšieho plánovaného času vykonania úlohy alebo úlohu preplánujte.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Plánovanie zrušené
SCHEDULE_CANCELLED_ERR=Túto úlohu nebolo možné vykonať z dôvodu internej chyby. Kontaktujte podporu SAP Support a poskytnite im ID korelácie a vyznačenie času z detailných informácií protokolu tejto úlohy.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Predchádzajúci chod prebieha
SUCCESS_SKIPPED_ERR=Vykonanie tejto úlohy nebolo spustené, pretože ešte prebieha predchádzajúci chod rovnakej úlohy.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Chýba vlastník
FAIL_OWNER_MISSING_ERR=Túto úlohu nebolo možné vykonať, pretože nemá priradeného systémového používateľa. Priraďte vlastníka k úlohe.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Súhlas nie je k dispozícii
FAIL_CONSENT_NOT_AVAILABLE_ERR=Nepovolili ste spoločnosti SAP spustiť reťazce úloh alebo naplánovať vo vašom mene úlohy integrácie údajov. Vyberte zadanú možnosť a poskytnite svoj súhlas.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Platnosť súhlasu uplynula
FAIL_CONSENT_EXPIRED_ERR=Oprávnenie, ktoré umožňuje spoločnosti SAP spustiť reťazce úloh alebo naplánovať úlohy integrácie údajov vo vašom mene, exspirovalo. Vyberte zadanú možnosť na obnovenie súhlasu.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Platnosť súhlasu zrušená
FAIL_CONSENT_INVALIDATED_ERR=Túto úlohu nebolo možné vykonať. Obvykle je to z dôvodz zmeny v konfigurácii poskytovateľa identít nájomcu. V takom prípade nie je možné vykonať žiadne nové úlohy alebo naplánovať ich v mene ovplyvneného používateľa. Ak ešte v novom poskytovateľovi identít existuje priradený používateľ, odvolajte súhlas s plánovaním a potom ho znovu udeľte. Ak priradený používateľ už neexistuje, priraďte nového vlastníka úlohy a poskytnite požadovaný súhlas s plánovaním úlohy. Ďalšie informácie získate v nasledujúcej informácii SAP: https://launchpad.support.sap.com/#/notes/3089828.
TASK_EXECUTOR_ERROR=Vykonávateľ úlohy
TASK_EXECUTOR_ERROR_ERR=Pri tejto úlohe sa vyskytla interná chyba, pravdepodobne počas prípravných krokov na vykonanie, a úlohu nebolo možné spustiť.
PREREQ_NOT_MET=Predpoklad nesplnený
PREREQ_NOT_MET_ERR=Túto úlohu nebolo možné spustiť z dôvodu problémov v jej definícii. Napríklad objekt nie je nasadený, reťazec úloh obsahuje kruhovú logiku alebo je SQL zobrazenia neplatný.
RESOURCE_LIMIT_ERROR=Chyba limitu zdroja
RESOURCE_LIMIT_ERROR_ERR=Momentálne nie je možné vykonať úlohu, pretože neboli k dispozícii dostatočné zdroje alebo boli zaneprázdnené.
FAIL_CONSENT_REFUSED_BY_UMS=Súhlas odmietnutý
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Túto úlohu nebolo možné vykonať v plánovaných spusteniach alebo reťazcoch úloh z dôvodu zmeny v konfigurácii poskytovateľa identity používateľa v kliente. Ďalšie informácie nájdete v nasledujúcej poznámke SAP: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Naplánované
#XFLD: status text
SCHEDULEDNew=Trvalé
#XFLD: status text
PAUSED=Pozastavené
#XFLD: status text
DIRECT=Priame
#XFLD: status text
MANUAL=Manuálne
#XFLD: status text
DIRECTNew=Jednoduché
#XFLD: status text
COMPLETED=Dokončené
#XFLD: status text
FAILED=Neúspešné
#XFLD: status text
RUNNING=Spustené
#XFLD: status text
none=Žiadne
#XFLD: status text
realtime=V reálnom čase
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Čiastková úloha
#XFLD: New Data available in the file
NEW_DATA=Nové údaje

#XFLD: text for values shown in column Replication Status
txtOff=Vypnuté
#XFLD: text for values shown in column Replication Status
txtInitializing=Inicializuje sa
#XFLD: text for values shown in column Replication Status
txtLoading=Načítava sa
#XFLD: text for values shown in column Replication Status
txtActive=Aktívne
#XFLD: text for values shown in column Replication Status
txtAvailable=K dispozícii
#XFLD: text for values shown in column Replication Status
txtError=Chyba
#XFLD: text for values shown in column Replication Status
txtPaused=Pozastavené
#XFLD: text for values shown in column Replication Status
txtDisconnected=Odpojené
#XFLD: text for partially Persisted views
partiallyPersisted=Vytvorená čiastočná perzistencia

#XFLD: activity text
REPLICATE=Replikovať
#XFLD: activity text
REMOVE_REPLICATED_DATA=Odstrániť replikované údaje
#XFLD: activity text
DISABLE_REALTIME=Deaktivovať replikáciu údajov v reálnom čase
#XFLD: activity text
REMOVE_PERSISTED_DATA=Odstrániť perzistentné údaje
#XFLD: activity text
PERSIST=Trvalo uložiť
#XFLD: activity text
EXECUTE=Vykonať
#XFLD: activity text
TASKLOG_CLEANUP=Očistenie_tacklog
#XFLD: activity text
CANCEL_REPLICATION=Zrušiť replikáciu
#XFLD: activity text
MODEL_IMPORT=Import modelu
#XFLD: activity text
NONE=Žiadne
#XFLD: activity text
CANCEL_PERSISTENCY=Zrušiť perzistenciu
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Analyzovať zobrazenie
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Zrušiť analyzátor zobrazenia

#XFLD: severity text
INFORMATION=Informácie
#XFLD: severity text
SUCCESS=Úspech
#XFLD: severity text
WARNING=Upozornenie
#XFLD: severity text
ERROR=Chyba
#XFLD: text for values shown for Ascending sort order
SortInAsc=Triediť vzostupne
#XFLD: text for values shown for Descending sort order
SortInDesc=Triediť zostupne
#XFLD: filter text for task log columns
Filter=Filtrovať
#XFLD: object text for task log columns
Object=Objekt
#XFLD: space text for task log columns
crossSpace=Priestor

#XBUT: label for remote data access
REMOTE=Vzdialené
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Replikované (v reálnom čase)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Replikované (snímka)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=Replikácia v reálnom čase je z dôvodu chyby blokovaná. Po oprave chyby môžete použiť akciu „Znova“ a pokračovať v replikácii v reálnom čase.
ERROR_MSG=Replikácia v reálnom čase je z dôvodu chyby blokovaná.
RETRY_FAILED_ERROR=Opakovanie procesu zlyhalo s chybou.
LOG_INFO_DETAILS=Nevygenerované žiadne protokoly, keď sú údaje replikované v režime v reálnom čase. Zobrazené protokoly sa vzťahujú na predchádzajúce akcie.

#XBUT: Partitioning label
partitionMenuText=Prebieha segmentácia
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Vytvoriť segment
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Upraviť segment
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Odstrániť segment
#XFLD: Initial text
InitialPartitionText=Definujte segmenty zadaním kritérií na rozdelenie veľkých množín údajov na menšie množiny.
DefinePartition=Definovať segmenty
#XFLD: Message text
partitionChangedInfo=Definícia segmentu sa od poslednej replikácie zmenila. Zmeny sa použijú pri ďalšom načítaní údajov.
#XFLD: Message text
REAL_TIME_WARNING=Segmentácia sa použije len pri načítavaní novej snímky. Nepoužije sa pri replikácii v reálnom čase.
#XFLD: Message text
loadSelectedPartitions=Spustená perzistencia údajov pre vybrané segmenty ''{0}''
#XFLD: Message text
loadSelectedPartitionsError=Neúspešná perzistencia údajov pre vybrané segmenty ''{0}''
#XFLD: Message text
viewpartitionChangedInfo=Definícia segmentácie sa od posledného chodu perzistencie zmenila. Pre aplikovanie zmien, nasledujúce zavedenie údajov bude plná snímka vrátane blokovaných segmentov. Ak je už toto plné zavedenie dokončené, budete môcť spustiť údaje pre jednotlivé segmenty.
#XFLD: Message text
viewpartitionChangedInfoLocked=Definícia segmentácie sa od posledného chodu perzistencie zmenila. Pre prevzatie zmien bude, nasledujúce načítanie údajov plná snímka okrem rozsahov blokovaných a nezmenených segmentov. Po dokončení tohto načítania budete môcť znova Načítať vybraté segmenty.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Replikácia tabuľky
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Replikácia plánu
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Vytvoriť plán
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Upraviť plán
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Odstrániť plán
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Načítať novú snímku
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Spustiť replikáciu údajov
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Odstrániť replikované údaje
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Aktivovať prístup v reálnom čase
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Aktivovať replikáciu údajov v reálnom čase
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Deaktivovať replikáciu údajov v reálnom čase
#XFLD: Message for replicate table action
replicateTableText=Replikácia tabuľky
#XFLD: Message for replicate table action
replicateTableTextNew=Replikácia údajov
#XFLD: Message to schedule task
scheduleText=Naplánovať
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Perzistencia zobrazenia
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Perzistencia údajov
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Načítať novú snímku
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Spustiť vytvorenie perzistencie údajov
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Odstrániť perzistentné údaje
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Segmentované spracovanie
#XBUT: Label for scheduled replication
scheduledTxt=Naplánované
#XBUT: Label for statistics button
statisticsTxt=Štatistika
#XBUT: Label for create statistics
createStatsTxt=Vytvoriť štatistiku
#XBUT: Label for edit statistics
editStatsTxt=Upraviť štatistiku
#XBUT: Label for refresh statistics
refreshStatsTxt=Obnoviť štatistiku
#XBUT: Label for delete statistics
dropStatsTxt=Odstrániť štatistiku
#XMSG: Create statistics success message
statsSuccessTxt=Spustené vytváranie štatistiky typu {0} pre {1}.
#XMSG: Edit statistics success message
statsEditSuccessTxt=Spustená zmena typu štatistiky na {0} pre {1}.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Spustené obnovenie štatistiky pre {0}.
#XMSG: Drop statistics success message
statsDropSuccessTxt=Štatistika úspešne odstránená pre {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=Chyba počas vytvárania štatistiky
#XMSG: Edit statistics error message
statsEditErrorTxt=Chyba počas zmeny štatistiky
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Chyba počas obnovenia štatistiky
#XMSG: Drop statistics error message
statsDropErrorTxt=Chyba počas odstraňovania štatistiky
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Naozaj chcete odstrániť štatistiku údajov?
startPersistencyAdvisorLabel=Spustiť analyzátor zobrazenia View Analyzer

#Partition related texts
#XFLD: Label for Column
column=Stĺpec
#XFLD: Label for No of Partition
noOfPartitions=Počet segmentov
#XFLD: Label for Column
noOfParallelProcess=Počet paralelných procesov
#XFLD: Label text
noOfLockedPartition=Počet blokovaných segmentov
#XFLD: Label for Partition
PARTITION=Segmenty
#XFLD: Label for Column
AVAILABLE=K dispozícii
#XFLD: Statistics Label
statsLabel=Štatistika
#XFLD: Label text
COLUMN=Stĺpec:
#XFLD: Label text
PARALLEL_PROCESSES=Paralelné procesy:
#XFLD: Label text
Partition_Range=Rozsah partície
#XFLD: Label text
Name=Názov
#XFLD: Label text
Locked=Blokované
#XFLD: Label text
Others=OSTATNÉ
#XFLD: Label text
Delete=Odstrániť
#XFLD: Label text
LoadData=Načítať vybraté segmenty
#XFLD: Label text
LoadSelectedData=Načítať vybraté segmenty
#XFLD: Confirmation text
LoadNewPersistenceConfirm=Týmto sa zavedie nová snímka pre všetky odblokované a zmenené segmenty, nielen pre tie, ktoré ste vybrali. Chcete pokračovať?
#XFLD: Label text
Continue=Pokračovať

#XFLD: Label text
PARTITIONS=Segmenty
#XFLD: Label text
ADD_PARTITIONS=+ Pridať segment
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Pridať segment
#XFLD: Label text
deleteRange=Odstrániť segment
#XFLD: Label text
LOW_PLACE_HOLDER=Zadať nízku hodnotu
#XFLD: Label text
HIGH_PLACE_HOLDER=Zadať vysokú hodnotu
#XFLD: tooltip text
lockedTooltip=Blokovať segment po prevzatí prvotných dát

#XFLD: Button text
Edit=Upraviť
#XFLD: Button text
CANCEL=Zrušiť

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Posledná aktualizácia štatistiky
#XFLD: Statistics Fields
STATISTICS=Štatistika

#XFLD:Retry label
TEXT_Retry=Znova
#XFLD:Retry label
TEXT_Retry_tooltip=Zopakujte replikáciu v reálnom čase po vyriešení chyby.
#XFLD: text retry
Retry=Potvrdiť
#XMG: Retry confirmation text
retryConfirmationTxt=Posledná replikácia v reálnom čase bola ukončená s chybou.\n Potvrďte, že chyba bola opravená a replikácia v reálnom čase sa má reštartovať.
#XMG: Retry success text
retrySuccess=Proces opakovania bol úspešne iniciovaný.
#XMG: Retry fail text
retryFail=Proces opakovania zlyhal.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Vytvoriť štatistiku
#XMSG: activity message for edit statistics
DROP_STATISTICS=Odstrániť štatistiku
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Obnoviť štatistiku
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Upraviť štatistiku
#XMSG: Task log message started task
taskStarted=Úloha {0} bola spustená.
#XMSG: Task log message for finished task
taskFinished=Úloha {0} skončila so statusom {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=Úloha {0} skončila o {2} so statusom {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=Úloha {0} má vstupné parametre
#XMSG: Task log message for unexpected error
unexpectedExecutionError=Úloha {0} skončila s neočakávanou chybou. Status úlohy bol nastavený na {1}.
#XMSG: Task log message for failed task
failedToEnd=Nepodarilo sa nastaviť status na {0} alebo sa nepodarilo odstrániť blokovanie.
#XMSG: Task log message
lockNotFound=Nemôžeme finalizovať proces, pretože chýba blokovanie: Je možné, že úloha bola zrušená.
#XMSG: Task log message failed task
failedOverwrite=Úlohu {0} už blokuje {1}. Preto zlyhala s nasledujúcou chybou: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Blokovanie tejto úlohy bolo prevzaté inou úlohou.
#XMSG: Task log message failed takeover
failedTakeover=Existujúcu úlohu sa nepodarilo prevziať.
#XMSG: Task log message successful takeover
successTakeover=Zostávajúce blokovanie bolo potrebné uvoľniť. Je nastavené nové blokovanie pre túto úlohu.
#XMSG: Tasklog Dialog Details
txtDetails=Vzdialené príkazy spracované počas chodu je možné zobraziť otvorením monitoru vzdialeného dotazu, v detailoch správ špecifických pre segment.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=Vzdialené príkazy SQL boli odstránené z databázy a nie je možné ich zobraziť.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Vzdialené dotazy, ktoré majú pripojenia priradené k iným priestorom, nie je možné zobraziť. Prejdite do nástroja Monitor vzdialených dotazov a pomocou ID príkazu ich filtrujte.
#XMSG: Task log message for parallel check error
parallelCheckError=Úloha sa nemôže spracovať, pretože prebieha iná úloha, ktorá už blokuje túto úlohu.
#XMSG: Task log message for parallel running task
parallelTaskRunning=Konfliktná úloha ešte prebieha.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} počas chodu s ID korelácie {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=Priradený používateľ nemá potrebné oprávnenia na vykonanie tejto úlohy.

#XBUT: Label for open in Editor
openInEditor=Otvoriť v editore
#XBUT: Label for open in Editor
openInEditorNew=Otvoriť v zostavovači údajov
#XFLD:Run deails label
runDetails=Detaily chodu
#XFLD: Label for Logs
Logs=Protokoly
#XFLD: Label for Settings
Settings=Nastavenia
#XFLD: Label for Save button
Save=Uložiť
#XFLD: Label for Standard
Standard_PO=Optimalizovaný výkon (odporúčané)
#XFLD: Label for Hana low memory processing
HLMP_MO=Optimalizované pre pamäť
#XFLD: Label for execution mode
ExecutionMode=Režim chodu
#XFLD: Label for job execution
jobExecution=Režim spracovania
#XFLD: Label for Synchronous
syncExec=Synchrónne
#XFLD: Label for Asynchronous
asyncExec=Asynchrónne
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Použiť predvoľbu (asynchrónne, môže sa v budúcnosti zmeniť)
#XMSG: Save settings success
saveSettingsSuccess=Režim vykonania SAP HANA bol úspešne zmenený.
#XMSG: Save settings failure
saveSettingsFailed=Zmena režimu vykonania SAP HANA bola neúspešná.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Vykonanie úlohy bolo zmenené.
#XMSG: Job Execution change failed
jobExecSettingFailed=Zmena vykonania úlohy bola lneúspešná.
#XMSG: Text for Type
typeTxt=Typ
#XMSG: Text for Monitor
monitorTxt=Monitorovať
#XMSG: Text for activity
activityTxt=Aktivita
#XMSG: Text for metrics
metricsTxt=Metriky
#XTXT: Text for Task chain key
TASK_CHAINS=Reťazec úloh
#XTXT: Text for View Key
VIEWS=Zobrazenie
#XTXT: Text for remote table key
REMOTE_TABLES=Vzdialená tabuľka
#XTXT: Text for Space key
SPACE=Priestor
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Elastický výpočtový uzol
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Replikačný tok
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Inteligentné vyhľadávanie
#XTXT: Text for Local Table
LOCAL_TABLE=Lokálna tabuľka
#XTXT: Text for Data flow key
DATA_FLOWS=Tok údajov
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=Postup skriptu SQL
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=Procesný reťazec BW
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Zobraziť v monitore
#XTXT: Task List header text
taskListHeader=Zoznam úloh ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Metriku pre historické chody toku údajov nie je možné načítať.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=Detaily chodu dokončenia sa momentálne nenačítavajú. Skúste aktualizáciu.
#XFLD: Label text for the Metrices table header
metricesColLabel=Označenie operátora
#XFLD: Label text for the Metrices table header
metricesType=Typ
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Počet záznamov
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Spustite reťazec úloh
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Chod reťazca úloh sa spustil.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Chod reťazca úloh sa spustil pre {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Nepodarilo sa spustiť reťazec úloh.
#XTXT: Execute button label
runLabel=Spustiť
#XTXT: Execute button label
runLabelNew=Spustiť chod
#XMSG: Filter Object header
chainsFilteredTableHeader=Filtrované podľa objektu: {0}
#XFLD: Parent task chain label
parentChainLabel=Nadradený reťazec úloh:
#XFLD: Parent task chain unauthorized
Unauthorized=Bez oprávnenia na zobrazenie
#XFLD: Parent task chain label
parentTaskLabel=Nadradená úloha:
#XTXT: Task status
NOT_TRIGGERED=Nespustené
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Prejsť do režimu celej obrazovky
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Ukončiť režim celej obrazovky
#XTXT: Close Task log details right panel
closeRightColumn=Zavrieť výber
#XTXT: Sort Text
sortTxt=Triediť
#XTXT: Filter Text
filterTxt=Filtrovať
#XTXT: Filter by text to show list of filters applied
filterByTxt=Filtrovať podľa
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Viac ako 5 minút
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Viac ako 15 minút
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Viac ako 1 hodina
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Posledná hodina
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Posledných 24 hodín
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Posledný mesiac
#XTXT: Messages title text
messagesText=Hlásenia

#XTXT Statistics information message
statisticsInfo=Štatistiku nie je možné vytvoriť pre vzdialené tabuľky s prístupom k údajom „Replikované“. Ak chcete vytvoriť štatistiku, odstráňte replikované údaje v monitore detailov vzdialených tabuliek.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Prejsť do monitora dettailov vzdialených tabuliek

#XTXT: Repair latest failed run label
retryRunLabel=Zopakovať najnovší chod
#XTXT: Repair failed run label
retryRun=Zopakovať chod
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Chod opakovania reťazca úloh bol spustený
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Chod opakovania reťazca úloh zlyhal
#XMSG: Task chain child elements name
taskChainRetryChildObject=Úloha {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Nová úloha
#XFLD Analyzed View
analyzedView=Analyzované zobrazenie
#XFLD Metrics
Metrics=Metriky
#XFLD Partition Metrics
PartitionMetrics=Metriky segmentov
#XFLD Entities
Messages=Protokol úlohy
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Neboli ešte definované žiadne segmenty.
#XTXT: Description message for empty partition data
partitionEmptyDescText=Vytvorte segmenty zadaním kritérií na rozdelenie väčších objemov údajov na menšie, lepšie spravovateľné časti.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Zatiaľ nie sú k dispozícii žiadne protokoly
#XTXT: Description message for empty runs data
runsEmptyDescText=Keď spustíte novú aktivitu (Načítať novú snímku, spustiť analyzátor zobrazenia...), zobrazia sa tu súvisiace protokoly.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=Konfigurácia elastického výpočtového uzla {0} sa zodpovedajúcim spôsobom neudržiava. Skontrolujte svoju definíciu.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Proces na pridanie elastického výpočtového uzla {0} zlyhal.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Vytvorenie a aktivácia repliky pre zdrojový objekt ''{0}''.''{1}'' v elastickom výpočtovom uzle {2} bolo spustené.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Odstránenie repliky pre zdrojový objekt ''{0}''.''{1}'' z elastického výpočtového uzla {2} bolo spustené.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Vytvorenie a aktivácia repliky pre zdrojový objekt ''{0}''.''{1}'' zlyhali.
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Odstránenie repliky pre zdrojový objekt ''{0}''.''{1}''‘ zlyhalo.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Začalo sa smerovanie výpočtu  analytických dotazov priestoru {0} do elastického výpočtového uzla {1}.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Smerovanie výpočtu analytických dotazov priestoru {0} do zodpovedajúceho elastického výpočtového uzla zlyhalo.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Začalo sa presmerovanie výpočtu priestoru {0} analytických dotazov do elastického výpočtového uzla {1}.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Presmerovanie výpočtu analytických dotazov priestoru {0} späť na koordinátora zlyhalo.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Bol spustený reťazec úloh {0} k príslušnému elastickému výpočtovému uzlu {1}.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Generovanie reťazca úloh pre elastický výpočtový uzol {0} zlyhalo.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=Bolo spustené poskytovanie elastického výpočtového uzla {0} s daným veľkostným plánom: vCPU: {1}, veľkosť pamäte (GiB): {2} a veľkosťou ukladacieho priestoru(GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=Elastický výpočtový uzol {0} už bol poskytnutý.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=Poskytnutie elastického výpočtového uzla {0} už bolo zrušené.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=Operácia nie je povolená. Zastavte elastický výpočtový uzol {0} a reštartujte ho, aby ste aktualizovali plán veľkosti.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Proces na odstránenie elastického výpočtového uzla {0} zlyhal.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=Časový limit aktuálneho chodu elastického výpočtového uzla {0} vypršal.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=Kontrola statusu elastického výpočtového uzla {0} práve prebieha...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=Generovanie reťazca úloh pre elastický výpočtový uzol {0} je blokované, a tak môže reťazec {1} stále prebiehať.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=Zdrojový objekt ''{0}''.''{1}'' je replikovaný ako závislosť zobrazenia ''{2}''.''{3}''.
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Nie je možné replikovať tabuľku ''‘{0}''.''‘{1}'', pretože replikácia riadkovej tabuľky je zastaraná.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Je prekročený maximálny elastický výpočtový uzol na inštanciu SAP HANA Cloud.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=Operácia spustenia elastického výpočtového uzla {0} stále prebieha.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Z dôvodu technických problémov bolo odstraňovanie repliky pre zdrojovú tabuľku {0} zastavené. Skúste neskôr.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=Z dôvodu technických problémov bolo vytvorenie repliky pre zdrojovú tabuľku {0} zastavené. Skúste neskôr.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Nie je možné spustiť elastický výpočtový uzol, pretože bol dosiahnutý limit používania alebo ešte neboli pridelené žiadne výpočtové blokové hodiny.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Načítava sa reťazec úloh a pripravuje sa spustenie celkového počtu {0} úloh, ktoré sú súčasťou tohto reťazca.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Konfliktná úloha už prebieha
#XMSG: Replication will change
txt_replication_change=Typ replikácie bude zmenený.
txt_repl_viewdetails=Zobraziť detaily

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Zdá sa, že pri zopakovaní posledného chodu sa vyskytla chyba, pretože predchádzajúce spustenie úlohy zlyhalo pred vygenerovaním plánu.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=Priestor "{0}" je blokovaný.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=Aktivita {0} vyžaduje nasadenie lokálnej tabuľky {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=Aktivita {0} vyžaduje, aby bola pre lokálnu tabuľku zapnutá funkcia Delta Capture {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=Aktivita {0} vyžaduje, aby lokálna tabuľka {1} ukladala údaje do úložiska objektov.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=Aktivita {0} vyžaduje, aby lokálna tabuľka {1} ukladala údaje do databázy.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Začínajú sa odstraňovať vymazané záznamy pre lokálnu tabuľku {0}.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Začínajú sa odstraňovať všetky záznamy pre lokálnu tabuľku {0}.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Začínajú sa odstraňovať záznamy pre lokálnu tabuľku {0} podľa podmienky filtra {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Pri odstraňovaní vymazaných záznamov pre lokálnu tabuľku {0} sa vyskytla chyba.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Pri odstraňovaní všetkých záznamov pre lokálnu tabuľku {0} sa vyskytla chyba.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Odstránenie všetkých úplne spracovaných záznamov s typom zmeny ''Odstránené'', ktoré sú staršie ako {0} dní.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Odstránenie všetkých úplne spracovaných záznamov s typom zmeny "Odstránené", ktoré sú staršie ako {0} dní.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} záznamov bolo odstránených.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} záznamov bolo označených na odstránenie.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=Odstránenie vymazaných záznamov pre lokálnu tabuľku {0} je dokončené.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Odstránenie všetkých záznamov pre lokálnu tabuľku {0} je dokončené.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Začínajú sa označovať záznamy ako „odstránené“ pre lokálnu tabuľku {0}.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Začínajú sa označovať záznamy ako "Odstránené" pre lokálnu tabuľku {0} podľa podmienky filtra {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Označenie záznamov ako "Odstránené" pre lokálnu tabuľku {0} je dokončené.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Zmeny údajov sa dočasne načítavajú do tabuľky {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Zmeny údajov sa dočasne načítané do tabuľky {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Zmeny údajov sa spracujú a odstránia z tabuľky {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Detaily pripojenia.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Začína sa optimalizovať lokálna tabuľka (súbor).
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Pri optimalizácii lokálnej tabuľky (súboru) sa vyskytla chyba.
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Vyskytla sa chyba. Optimalizácia lokálnej tabuľky (súboru) bola zastavená.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Optimalizuje sa lokálna tabuľka (súbor)...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=Lokálna tabuľka (súbor) bola optimalizovaná.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=Lokálna tabuľka (súbor) bola optimalizovaná.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=Tabuľka je optimalizovaná pomocou stĺpcov v poradí Z: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Vyskytla sa chyba. Skracovanie lokálnej tabuľky (súboru) bolo zastavené.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Skracuje sa lokálna tabuľka (súbor)...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=Miesto vstupného buffera pre lokálnu tabuľku (súbor) bolo zrušené.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Spustenie vymazania (odstránenie všetkých plne spracovaných záznamov) Lokálna tabuľka (Súbor).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Pri výmaze lokálnej tabuľky (súboru) sa vyskytla chyba.
#XMSG: Task log message
LTF_VACUUM_STOPPED=Vyskytla sa chyba. Vymazanie lokálnej tabuľky (súboru) bolo zastavené.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Vymazáva sa sa lokálna tabuľka (súbor)...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=Výmaz bol dokončený.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Odstránenie všetkých úplne spracovaných záznamov, ktoré sú staršie ako {0} dní.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Odstránenie všetkých úplne spracovaných záznamov, ktoré sú staršie ako {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Spúšťa sa zlúčenie nových záznamov s lokálnou tabuľkou (súbor).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Spustenie zlučovania nových záznamov s lokálnou tabuľkou (súborom) pomocou nastavenia "Zlúčiť údaje automaticky".
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Spustenie zlučovania nových záznamov s lokálnou tabuľkou (súbor). Táto úloha bola spustená prostredníctvom požiadavky na zlúčenie rozhrania API.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Zlúčenie nových záznamov s lokálnou tabuľkou (súbor).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Pri zlučovaní nových záznamov s lokálnou tabuľkou (súbor) sa vyskytla chyba.
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=Lokálna tabuľka (súbor) je zlúčená.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Vyskytla sa chyba. Zlúčenie lokálnej tabuľky (súboru) bolo zastavené.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=Zlúčenie lokálnej tabuľky (súboru) zlyhalo z dôvodu chyby, ale operácia bola čiastočne úspešná a niektoré údaje už boli zlúčené.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Vyskytla sa chyba časového limitu. Aktivita {0} prebieha už {1} hodín.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=Asynchrónne vykonávanie nebolo možné spustiť z dôvodu vysokého zaťaženia systému. Otvorte ''System Monitor'' a skontrolujte spustené úlohy.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=Asynchrónne vykonávanie bolo zrušené.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=Úloha {0} spustená v rámci pamäťových limitov {1} a {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=Úloha {0} bola spustená s ID zdroja {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=Hľadanie a nahradenie sa začalo v lokálnej tabuľke (súbor).
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Hľadanie a nahradenie bolo dokončené v lokálnej tabuľke (súbor).
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Hľadanie a nahradenie zlyhalo v lokálnej tabuľke (súbor).

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Vyskytla sa chyba. Aktualizácia štatistík pre lokálnu tabuľku (súbor) bola zastavená.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=Úloha zlyhala z dôvodu nedostatku pamäte v databáze SAP HANA.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=Úloha zlyhala z dôvodu zamietnutia kontroly prístupu SAP HANA.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=Úloha zlyhala z dôvodu príliš veľkého počtu aktívnych pripojení SAP HANA.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=Operáciu Opakovať nebolo možné vykonať, pretože opakované pokusy sú povolené len pre nadradený objekt vnoreného reťazca úloh.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Protokoly neúspešných podradených úloh už nie sú k dispozícii, aby ste mohli pokračovať.


####Metrics Labels

performanceOptimized=Optimalizovaný výkon
memoryOptimized=Optimalizované pre pamäť

JOB_EXECUTION=Vykonanie úlohy
EXECUTION_MODE=Režim chodu
NUMBER_OF_RECORDS_OVERALL=Počet perzistentných záznamov
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Počet načítaných záznamov zo vzdialeného zdroja
RUNTIME_MS_REMOTE_EXECUTION_TIME=Čas vzdialeného spracovania zdroja
MEMORY_CONSUMPTION_GIB=Maximálna pamäť SAP HANA
NUMBER_OF_PARTITIONS=Počet segmentov
MEMORY_CONSUMPTION_GIB_OVERALL=Maximálna pamäť SAP HANA
NUMBER_OF_PARTITIONS_LOCKED=Počet blokovaných segmentov
PARTITIONING_COLUMN=Stĺpec segmentovania
HANA_PEAK_CPU_TIME=Celkový čas CPU SAP HANA
USED_IN_DISK=Použitý ukladací priestor
INPUT_PARAMETER_PARAMETER_VALUE=Vstupný parameter
INPUT_PARAMETER=Vstupný parameter
ECN_ID=Názov pre elastický výpočtový uzol

DAC=Riadenia prístupu k údajom
YES=Áno
NO=Nie
noofrecords=Počet záznamov
partitionpeakmemory=Maximálna pamäť SAP HANA
value=Hodnota
metricsTitle=Metriky ({0})
partitionmetricsTitle=Segmenty ({0})
partitionLabel=Segment
OthersNotNull=Hodnoty nie sú zahrnuté v rozsahoch
OthersNull=Nulové hodnoty
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Nastavenia použité pri poslednom spustení uchovávania údajov:
#XMSG: Message for input parameter name
inputParameterLabel=Vstupný parameter
#XMSG: Message for input parameter value
inputParameterValueLabel=Hodnota
#XMSG: Message for persisted data
inputParameterPersistedLabel=Vytvorená perzistencia o
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Odstrániť údaje
REMOVE_DELETED_RECORDS=Odstrániť vymazané záznamy
MERGE_FILES=Zlúčiť súbory
OPTIMIZE_FILES=Optimalizovať súbory
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Zobraziť v SAP BW Bridge Monitor

ANALYZE_PERFORMANCE=Analyzovať výkon
CANCEL_ANALYZE_PERFORMANCE=Zrušiť analýzu výkonu

#XFLD: Label for frequency column
everyLabel=Každých
#XFLD: Plural Recurrence text for Hour
hoursLabel=Hodiny
#XFLD: Plural Recurrence text for Day
daysLabel=Dni
#XFLD: Plural Recurrence text for Month
monthsLabel=Mesiace
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minúty

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Zobraziť príručku na riešenie problémov s perzistenciou</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Proces má nedostatok pamäte. Nie je možné spracovať údaje pre zobrazenie "{0}". Ďalšie informácie o chybách súvisiacich s nedostatkom pamäte nájdete na portáli nápovede. Ak chcete analyzovať zobrazenie zložitosti spotreby pamäte, zvážte začiarknutie možnosti View Analyzer.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Nerelevantné
OPEN_BRACKET=(
CLOSE_BRACKET=)
