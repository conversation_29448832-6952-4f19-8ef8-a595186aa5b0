
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Detaily protokolu
#XFLD: Header
TASK_LOGS=Protokoly úloh ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Běhy ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Zobrazit detaily
#XFLD: Button text
STOP=Zastavit běh
#XFLD: Label text
RUN_START=Začátek posledního běhu
#XFLD: Label text
RUN_END=Konec posledního běhu
#XFLD: Label text
RUNTIME=Trvání
#XTIT: Count for Messages
txtDetailMessages=Zprávy ({0})
#XFLD: Label text
TIME=Časový záznam
#XFLD: Label text
MESSAGE=Zpráva
#XFLD: Label text
TASK_STATUS=Kategorie
#XFLD: Label text
TASK_ACTIVITY=Činnost
#XFLD: Label text
RUN_START_DETAILS=Spustit
#XFLD: Label text
RUN_END_DETAILS=Konec
#XFLD: Label text
LOGS=Běhy
#XFLD: Label text
STATUS=Status
#XFLD: Label text
RUN_STATUS=Status běhu
#XFLD: Label text
Runtime=Trvání
#XFLD: Label text
RuntimeTooltip=Trvání (hh : mm : ss)
#XFLD: Label text
TRIGGEREDBY=Spustil
#XFLD: Label text
TRIGGEREDBYNew=Spustil
#XFLD: Label text
TRIGGEREDBYNewImp=Běh spustil
#XFLD: Label text
EXECUTIONTYPE=Typ provedení
#XFLD: Label text
EXECUTIONTYPENew=Typ běhu
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Prostor nadřazeného řetězce
#XFLD: Refresh tooltip
TEXT_REFRESH=Aktualizovat
#XFLD: view Details link
VIEW_ERROR_DETAILS=Zobrazit detaily
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Stáhnout doplňkové detaily
#XMSG: Download completed
downloadStarted=Stahování zahájeno
#XMSG: Error while downloading content
errorInDownload=Při stahování došlo k chybě.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Zobrazit detaily
#XBTN: cancel button of task details dialog
TXT_CANCEL=Zrušit
#XBTN: back button from task details
TXT_BACK=Zpět
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Úloha dokončena
#XFLD: Log message with failed status
MSG_LOG_FAILED=Úloha se nezdařila
#XFLD: Master and detail table with no data
No_Data=Žádná data
#XFLD: Retry tooltip
TEXT_RETRY=Opakovat
#XFLD: Cancel Run label
TEXT_CancelRun=Zrušit běh
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Vyčistit neúspěšné načtení
#XMSG:button copy sql statement
txtSQLStatement=Kopírovat příkaz SQL
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Otevřít monitor vzdálených dotazů
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=Chcete-li zobrazit vzdálené příkazy SQL, klikněte na „Otevřít monitor vzdálených dotazů“.
#XMSG:button ok
txtOk=OK
#XMSG: button close
txtClose=Zavřít
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=Akce zrušení běhu pro objekt ''{0}'' byla spuštěna.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=Akce zrušení běhu pro objekt ''{0}'' se nezdařila.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=Akce zrušení běhu pro objekt ''{0}'' již není možná, protože status replikace se změnil.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Žádné protokoly úlohy nemají status Probíhá.
#XMSG: message for conflicting task
Task_Already_Running=Konfliktní úloha je již spuštěna pro objekt ''{0}''.
#XFLD: Label for no task log with running state title
actionInfo=Informace o akci
#XMSG Copied to clipboard
copiedToClip=Zkopírováno do schránky
#XFLD copy
Copy=Kopírovat
#XFLD copy correlation ID
CopyCorrelationID=Kopírovat ID korelace
#XFLD Close
Close=Zavřít
#XFLD: show more Label
txtShowMore=Zobrazit více
#XFLD: message Label
messageLabel=Zpráva:
#XFLD: details Label
detailsLabel=Detaily:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Provádění SQL \r\n Příkaz:
#XFLD:statementId Label
statementIdLabel=ID příkazu:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Počet vzdálených \r\n příkazů SQL:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Počet příkazů
#XFLD: Space Label
txtSpaces=Prostor
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Neautorizované prostoru
#XFLD: Privilege Error Text
txtPrivilegeError=Nemáte dostatečné oprávnění pro náhled na tato data.
#XFLD: Label for Object Header
DATA_ACCESS=Přístup k datům
#XFLD: Label for Object Header
SCHEDULE=Naplánovat
#XFLD: Label for Object Header
DETAILS=Detaily
#XFLD: Label for Object Header
LATEST_UPDATE=Poslední aktualizace
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Poslední změna (zdroj)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Frekvence aktualizací
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Naplánovaná frekvence
#XFLD: Label for Object Header
NEXT_RUN=Příští běh
#XFLD: Label for Object Header
CONNECTION=Připojení
#XFLD: Label for Object Header
DP_AGENT=Agent DP
#XFLD: Label for Object Header
USED_IN_MEMORY=Paměť využitá pro úložiště (MiB)
#XFLD: Label for Object Header
USED_DISK=Disk využitý pro úložiště (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Velikost vnitřní paměti (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Prostor na disku (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Počet záznamů

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Nastavit na Neúspěšné
SET_TO_FAILED_ERR=Tato úloha probíhala, ale uživatel nastavil její status na NEÚSPĚŠNÉ.
#XFLD: Label for stopped failed
FAILLOCKED=Běh již probíhá
#XFLD: sub status STOPPED
STOPPED=Zastaveno
STOPPED_ERR=Tato úloha byla zastavena, ale nebyla znovu zavedena.
#XFLD: sub status CANCELLED
CANCELLED=Zrušeno
CANCELLED_ERR=Tento běh úlohy byl zrušen po zahájení. V takovém případě byla data znovu zavedena a obnovena na stav, který existoval, než byl běh úlohy iniciálně spuštěn.
#XFLD: sub status LOCKED
LOCKED=Blokováno
LOCKED_ERR=Stejná úloha již probíhala, takže tento job úlohy nelze spustit paralelně s existujícím prováděním úlohy.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Výjimka úlohy
TASK_EXCEPTION_ERR=Při provádění této úlohy došlo k nespecifikované chybě.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Výjimka provedení úlohy
TASK_EXECUTE_EXCEPTION_ERR=Při provádění této úlohy došlo k chybě.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Neautorizováno
UNAUTHORIZED_ERR=Uživatel nemohl být autentizován, byl blokován nebo odstraněn.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Zakázáno
FORBIDDEN_ERR=Přiřazený uživatel nemá potřebná oprávnění k provedení této úlohy.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Nespuštěno
FAIL_NOT_TRIGGERED_ERR=Tento job úlohy nebylo možné provést kvůli výpadku systému nebo proto, že nějaká část databázového systému není k času plánovaného provedení dostupná. Počkejte na příští provedení naplánovaného jobu nebo job přeplánujte.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Naplánování zrušeno
SCHEDULE_CANCELLED_ERR=Tento job úlohy nebylo možné provést kvůli interní chybě. Kontaktujte podporu poskytovanou společností SAP a poskytněte jí ID korelace a časový záznam z detailních informací protokolu jobu této úlohy.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Předchozí běh probíhá
SUCCESS_SKIPPED_ERR=Toto provedení úlohy nebylo spuštěno, protože předchozí běh stejné úlohy stále probíhá.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Chybí vlastník
FAIL_OWNER_MISSING_ERR=Tento ob úlohy nebylo možné provést, protože nemá přiřazeného systémového uživatele. Přiřaďte k jobu odpovědného uživatele.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Souhlas není k dispozici
FAIL_CONSENT_NOT_AVAILABLE_ERR=Neposkytli jste oprávnění společnosti SAP ke spuštění řetězce úloh nebo k naplánování úloh integrace dat ve vašem zastoupení. Vyberte poskytnutou možnost k udělení vašeho souhlasu.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Platnost souhlasu vypršela
FAIL_CONSENT_EXPIRED_ERR=Platnost oprávnění, které umožňuje společnosti SAP spouštět řetězce úloh nebo plánovat úlohy integrace dat ve vašem zastoupení, vypršela. Vyberte poskytnutou možnost k obnovení vašeho souhlasu.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Souhlas zneplatněn
FAIL_CONSENT_INVALIDATED_ERR=Tuto úlohu nebylo možné provést, v typickém případě kvůli změně konfigurace poskytovatele identity tohoto tenanta. V takovém případě nelze spustit nebo naplánovat žádné nové joby úlohy jménem postiženého uživatele. Pokud přiřazený uživatel stále existuje v novém poskytovateli identity, odeberte souhlas s naplánováním a potom ho zase udělte. Když přiřazený uživatel již neexistuje, přiřaďte nového vlastníka jobu úlohy a poskytněte požadovaný souhlas s naplánováním úlohy. Pro další informace viz následující pokyn SAP: https://launchpad.support.sap.com/#/notes/3089828.
TASK_EXECUTOR_ERROR=Vykonavatel úlohy
TASK_EXECUTOR_ERROR_ERR=Tato úloha narazila na interní chybu, pravděpodobně při přípravných krocích provedení, a úlohu nebylo možné spustit.
PREREQ_NOT_MET=Předpoklady nesplněny
PREREQ_NOT_MET_ERR=Tuto úlohu nebylo možné spustit kvůli problémům v její definici. Například objekt není nasazen, řetězec úloh obsahuje kruhovou logiku nebo je SQL zobrazení neplatné.
RESOURCE_LIMIT_ERROR=Chyba limitu zdroje
RESOURCE_LIMIT_ERROR_ERR=Úlohu momentálně nelze provést, protože nebyly k dispozici dostatečné zdroje nebo byly zaneprázdněny.
FAIL_CONSENT_REFUSED_BY_UMS=Souhlas odmítnut
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Tuto úlohu nebylo možné provést v naplánovaných spuštěních nebo v řetězcích úloh z důvodu změny v konfiguraci poskytovatele identity uživatele na tenantovi. Další informace naleznete v následujícím pokynu SAP:  https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Naplánováno
#XFLD: status text
SCHEDULEDNew=Permanentní
#XFLD: status text
PAUSED=Pozastaveno
#XFLD: status text
DIRECT=Přímo
#XFLD: status text
MANUAL=Manuální
#XFLD: status text
DIRECTNew=Jednoduché
#XFLD: status text
COMPLETED=Dokončeno
#XFLD: status text
FAILED=Neúspěšné
#XFLD: status text
RUNNING=Probíhá
#XFLD: status text
none=Žádné
#XFLD: status text
realtime=V reálném čase
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Dílčí úloha
#XFLD: New Data available in the file
NEW_DATA=Nová data

#XFLD: text for values shown in column Replication Status
txtOff=Vypnuto
#XFLD: text for values shown in column Replication Status
txtInitializing=Inicializace
#XFLD: text for values shown in column Replication Status
txtLoading=Načítání
#XFLD: text for values shown in column Replication Status
txtActive=Aktivní
#XFLD: text for values shown in column Replication Status
txtAvailable=Dostupné
#XFLD: text for values shown in column Replication Status
txtError=Chyba
#XFLD: text for values shown in column Replication Status
txtPaused=Pozastaveno
#XFLD: text for values shown in column Replication Status
txtDisconnected=Odpojeno
#XFLD: text for partially Persisted views
partiallyPersisted=Částečně trvale uloženo

#XFLD: activity text
REPLICATE=Replikovat
#XFLD: activity text
REMOVE_REPLICATED_DATA=Odebrat replikovaná data
#XFLD: activity text
DISABLE_REALTIME=Zakázat replikaci dat v reálném čase
#XFLD: activity text
REMOVE_PERSISTED_DATA=Odebrat perzistentní data
#XFLD: activity text
PERSIST=Trvale uložit
#XFLD: activity text
EXECUTE=Provést
#XFLD: activity text
TASKLOG_CLEANUP=Tacklog_Cleanup
#XFLD: activity text
CANCEL_REPLICATION=Zrušit replikaci
#XFLD: activity text
MODEL_IMPORT=Import modelu
#XFLD: activity text
NONE=Žádné
#XFLD: activity text
CANCEL_PERSISTENCY=Zrušit perzistenci
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Analyzovat pohled
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Zrušit analyzátor pohledu

#XFLD: severity text
INFORMATION=Informace
#XFLD: severity text
SUCCESS=Úspěch
#XFLD: severity text
WARNING=Upozornění
#XFLD: severity text
ERROR=Chyba
#XFLD: text for values shown for Ascending sort order
SortInAsc=Třídit vzestupně
#XFLD: text for values shown for Descending sort order
SortInDesc=Třídit sestupně
#XFLD: filter text for task log columns
Filter=Filtr
#XFLD: object text for task log columns
Object=Objekt
#XFLD: space text for task log columns
crossSpace=Prostor

#XBUT: label for remote data access
REMOTE=Vzdálené
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Replikováno (reálný čas)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Replikováno (snímek)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=Replikace v reálném čase je blokována kvůli chybě. Po opravení chyby můžete pro pokračování replikace v reálném čase použít akci „Opakovat“.
ERROR_MSG=Replikace v reálném čase je blokována kvůli chybě.
RETRY_FAILED_ERROR=Opakování procesu neúspěšné s chybou.
LOG_INFO_DETAILS=Při replikování dat v režimu v reálném čase nejsou vygenerovány žádné protokoly. Zobrazené protokoly se vztahují k předchozím akcím.

#XBUT: Partitioning label
partitionMenuText=Dělení
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Vytvořit oddíl
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Upravit oddíl
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Odstranit oddíl
#XFLD: Initial text
InitialPartitionText=Definovat oddíly zadáním kritérií pro rozdělení velkých množin dat na menší množiny.
DefinePartition=Definovat oddíly
#XFLD: Message text
partitionChangedInfo=Definice oddílu se od poslední replikace změnila. Změny se použijí při příštím načtení dat.
#XFLD: Message text
REAL_TIME_WARNING=Segmentování se použije jen při načítání nového snímku. Nepoužije se pro replikaci v reálném čase.
#XFLD: Message text
loadSelectedPartitions=Zahájena perzistence dat pro vybrané oddíly ''{0}''‘
#XFLD: Message text
loadSelectedPartitionsError=Perzistence dat pro vybrané oddíly ''{0}''‘ neúspěšná
#XFLD: Message text
viewpartitionChangedInfo=Definice oddílu se od posledního běhu perzistence změnila. Aby se změny použily, bude následující zavedení dat úplný snímek včetně blokovaných oddílů. Po dokončení úplného zavedení budete moci spustit data pro jednotlivé oddíly.
#XFLD: Message text
viewpartitionChangedInfoLocked=Definice oddílu se od posledního běhu perzistence změnila. Aby se změny použily, bude následující zavedení dat úplný snímek vyjímaje blokované a nezměněné rozsahy oddílů. Po dokončení tohoto zavedení budete moci zavést vybrané oddíly znovu.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Replikace tabulky
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Naplánovat replikaci
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Vytvořit časový plán
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Upravit plán
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Odstranit plán
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Načíst nový snímek
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Spustit replikaci dat
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Odebrat replikovaná data
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Povolit přístup v reálném čase
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Povolit replikaci dat v reálném čase
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Zakázat replikaci dat v reálném čase
#XFLD: Message for replicate table action
replicateTableText=Replikace tabulky
#XFLD: Message for replicate table action
replicateTableTextNew=Replikace dat
#XFLD: Message to schedule task
scheduleText=Naplánovat
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Zobrazit perzistenci
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Perzistence dat
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Načíst nový snímek
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Spustit perzistenci dat
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Odebrat perzistentní data
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Rozdělené zpracování
#XBUT: Label for scheduled replication
scheduledTxt=Naplánováno
#XBUT: Label for statistics button
statisticsTxt=Statistika
#XBUT: Label for create statistics
createStatsTxt=Vytvořit statistiku
#XBUT: Label for edit statistics
editStatsTxt=Upravit statistiku
#XBUT: Label for refresh statistics
refreshStatsTxt=Aktualizovat statistiku
#XBUT: Label for delete statistics
dropStatsTxt=Odstranit statistiku
#XMSG: Create statistics success message
statsSuccessTxt=Spuštěno vytváření statistiky typu {0} pro {1}.
#XMSG: Edit statistics success message
statsEditSuccessTxt=Spuštěna změna typu statistiky na {0} pro {1}
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Spuštěna aktualizace statistiky pro {0}.
#XMSG: Drop statistics success message
statsDropSuccessTxt=Statistika úspěšně odstraněna pro {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=Chyba při vytváření statistiky
#XMSG: Edit statistics error message
statsEditErrorTxt=Chyba při změně statistiky
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Chyba při aktualizaci statistiky
#XMSG: Drop statistics error message
statsDropErrorTxt=Chyba při odstraňování statistiky
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Opravdu chcete odstranit statistiku dat?
startPersistencyAdvisorLabel=Spustit analyzátor pohledu

#Partition related texts
#XFLD: Label for Column
column=Sloupec
#XFLD: Label for No of Partition
noOfPartitions=Počet oddílů
#XFLD: Label for Column
noOfParallelProcess=Počet paralelních procesů
#XFLD: Label text
noOfLockedPartition=Počet blokovaných oddílů
#XFLD: Label for Partition
PARTITION=Oddíly
#XFLD: Label for Column
AVAILABLE=Dostupné
#XFLD: Statistics Label
statsLabel=Statistika
#XFLD: Label text
COLUMN=Sloupec:
#XFLD: Label text
PARALLEL_PROCESSES=Paralelní procesy:
#XFLD: Label text
Partition_Range=Rozsah oddílu
#XFLD: Label text
Name=Název
#XFLD: Label text
Locked=Blokováno
#XFLD: Label text
Others=JINÉ
#XFLD: Label text
Delete=Odstranit
#XFLD: Label text
LoadData=Načíst vybrané oddíly
#XFLD: Label text
LoadSelectedData=Načíst vybrané oddíly
#XFLD: Confirmation text
LoadNewPersistenceConfirm=To bude načítat nový snímek pro všechny odemčené a změněné oddíly, nejen pro ty, které jste vybrali. Chcete pokračovat?
#XFLD: Label text
Continue=Dále

#XFLD: Label text
PARTITIONS=Oddíly
#XFLD: Label text
ADD_PARTITIONS=+ Přidat oddíl
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Přidat oddíl
#XFLD: Label text
deleteRange=Odstranit oddíl
#XFLD: Label text
LOW_PLACE_HOLDER=Zadat nízkou hodnotu
#XFLD: Label text
HIGH_PLACE_HOLDER=Zadat vysokou hodnotu
#XFLD: tooltip text
lockedTooltip=Blokovat oddíl po iniciálním zavedení

#XFLD: Button text
Edit=Upravit
#XFLD: Button text
CANCEL=Zrušit

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Poslední aktualizace statistiky
#XFLD: Statistics Fields
STATISTICS=Statistika

#XFLD:Retry label
TEXT_Retry=Opakovat
#XFLD:Retry label
TEXT_Retry_tooltip=Opakovat replikaci v reálném čase po vyřešení chyby.
#XFLD: text retry
Retry=Potvrdit
#XMG: Retry confirmation text
retryConfirmationTxt=Poslední replikace v reálném čase byla ukončena s chybou.\n Potvrďte, že chyba byla opravena a že replikaci v reálném čase je možné restartovat.
#XMG: Retry success text
retrySuccess=Proces opakování byl úspěšně iniciován.
#XMG: Retry fail text
retryFail=Proces opakování se nezdařil.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Vytvořit statistiku
#XMSG: activity message for edit statistics
DROP_STATISTICS=Odstranit statistiku
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Aktualizovat statistiku
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Upravit statistiku
#XMSG: Task log message started task
taskStarted=Úloha {0} byla zahájena.
#XMSG: Task log message for finished task
taskFinished=Úloha {0} skončila se statusem {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=Úloha {0} skončila v {2} se statusem {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=Úloha {0} má vstupní parametry.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=Úloha {0} skončila s neočekávanou chybou. Status úlohy byl nastaven na {1}.
#XMSG: Task log message for failed task
failedToEnd=Nezdařilo se nastavit status na {0} nebo se nezdařilo odebrat blokování.
#XMSG: Task log message
lockNotFound=Nemůžeme dokončit proces, protože chybí blokování: možná byla úloha zrušena.
#XMSG: Task log message failed task
failedOverwrite=Úlohu {0} již blokuje {1}. Proto byla neúspěšná s následující chybou: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Blokování této úlohy bylo převzato jinou úlohou.
#XMSG: Task log message failed takeover
failedTakeover=Nezdařilo se převzít existující úlohu.
#XMSG: Task log message successful takeover
successTakeover=Zbylé blokování muselo být uvolněno. Pro tuto úlohu je nastaveno nové blokování
#XMSG: Tasklog Dialog Details
txtDetails=Vzdálené příkazy zpracované při běhu lze zobrazit otevřením monitoru vzdálených dotazů v detailech zpráv specifických pro oddíl.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=Vzdálené příkazy SQL byly odstraněny z databáze a nelze je zobrazit.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Vzdálené dotazy, které mají přiřazena připojení k jiným prostorům, nelze zobrazit. Přejděte k monitoru vzdálených dotazů a použijte ID příkazu k jejich filtrování.
#XMSG: Task log message for parallel check error
parallelCheckError=Úlohu nelze zpracovat, protože probíhá jiná úloha a již tuto úlohu blokuje.
#XMSG: Task log message for parallel running task
parallelTaskRunning=Konfliktní úloha je již spuštěna.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} při běhu s ID korelace {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=Přiřazený uživatel nemá potřebná oprávnění k provedení této úlohy.

#XBUT: Label for open in Editor
openInEditor=Otevřít v editoru
#XBUT: Label for open in Editor
openInEditorNew=Otevřít v editoru dat
#XFLD:Run deails label
runDetails=Detaily běhu
#XFLD: Label for Logs
Logs=Protokoly
#XFLD: Label for Settings
Settings=Nastavení
#XFLD: Label for Save button
Save=Uložit
#XFLD: Label for Standard
Standard_PO=Výkon optimalizován (doporučeno)
#XFLD: Label for Hana low memory processing
HLMP_MO=Paměť optimalizována
#XFLD: Label for execution mode
ExecutionMode=Režim běhu
#XFLD: Label for job execution
jobExecution=Režim zpracování
#XFLD: Label for Synchronous
syncExec=Synchronní
#XFLD: Label for Asynchronous
asyncExec=Asynchronní
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Použít standardní (asynchronní, v budoucnu se může změnit)
#XMSG: Save settings success
saveSettingsSuccess=Prováděcí režim SAP HANA byl změněn.
#XMSG: Save settings failure
saveSettingsFailed=Změna prováděcího režimu SAP HANA se nezdařila.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Provedení jobu změněno
#XMSG: Job Execution change failed
jobExecSettingFailed=Změna provedení jobu neúspěšná
#XMSG: Text for Type
typeTxt=Typ
#XMSG: Text for Monitor
monitorTxt=Monitorovat
#XMSG: Text for activity
activityTxt=Činnost
#XMSG: Text for metrics
metricsTxt=Metriky
#XTXT: Text for Task chain key
TASK_CHAINS=Řetězec úloh
#XTXT: Text for View Key
VIEWS=Zobrazit
#XTXT: Text for remote table key
REMOTE_TABLES=Vzdálená tabulka
#XTXT: Text for Space key
SPACE=Prostor
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Elastický výpočetní uzel
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Replikační tok
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Inteligentní vyhledání
#XTXT: Text for Local Table
LOCAL_TABLE=Lokální tabulka
#XTXT: Text for Data flow key
DATA_FLOWS=Datový tok
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=Postup skriptu SQL
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=Řetězec procesů BW
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Zobrazit v monitoru
#XTXT: Task List header text
taskListHeader=Seznam úloh ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Metriky pro historické běhy toku dat nelze načíst.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=Detail kompletního běhu se momentálně nenačítá. Zkuste aktualizovat.
#XFLD: Label text for the Metrices table header
metricesColLabel=Popisek operátoru
#XFLD: Label text for the Metrices table header
metricesType=Typ
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Počet záznamů
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Spustit řetězec úloh
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Běh řetězce úloh spuštěn.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Běh řetězce úloh spuštěn pro {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Nezdařilo se spustit řetězec úloh.
#XTXT: Execute button label
runLabel=Spustit
#XTXT: Execute button label
runLabelNew=Spustit běh
#XMSG: Filter Object header
chainsFilteredTableHeader=Filtrováno podle objektu: {0}
#XFLD: Parent task chain label
parentChainLabel=Nadřazený řetězec úloh:
#XFLD: Parent task chain unauthorized
Unauthorized=Chybí oprávnění k zobrazení
#XFLD: Parent task chain label
parentTaskLabel=Nadřazená úloha:
#XTXT: Task status
NOT_TRIGGERED=Nespuštěn
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Zadat režim celé obrazovky
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Ukončit režim celé obrazovky
#XTXT: Close Task log details right panel
closeRightColumn=Zavřít sekci
#XTXT: Sort Text
sortTxt=Třídit
#XTXT: Filter Text
filterTxt=Filtr
#XTXT: Filter by text to show list of filters applied
filterByTxt=Filtrovat podle
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Více než 5 minut
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Více než 15 minut
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Více než 1 hodinu
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Poslední hodina
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Posledních 24 hodin
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Poslední měsíc
#XTXT: Messages title text
messagesText=Zprávy

#XTXT Statistics information message
statisticsInfo=Statistiku nelze vytvořit pro vzdálené tabulky s přístupem k datům „Replikováno“. Pro vytvoření statistiky odeberte replikovaná data z monitoru detailů vzdálených tabulek.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Přejděte k monitoru detailů vzdálené tabulky

#XTXT: Repair latest failed run label
retryRunLabel=Opakujte poslední běh
#XTXT: Repair failed run label
retryRun=Opakujte běh
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Opakovaný běh řetězce úloh spuštěn
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Opakovaný běh řetězce úloh se nezdařil
#XMSG: Task chain child elements name
taskChainRetryChildObject=Úloha {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Nová úloha
#XFLD Analyzed View
analyzedView=Analyzovaný pohled
#XFLD Metrics
Metrics=Metriky
#XFLD Partition Metrics
PartitionMetrics=Metriky oddílů
#XFLD Entities
Messages=Protokol úloh
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Žádné oddíly dosud nebyly definovány
#XTXT: Description message for empty partition data
partitionEmptyDescText=Vytvořte oddíly zadáním kritérií pro rozdělení velkých objemů dat na menší, lépe spravovatelné části.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Dosud nejsou dostupné žádné protokoly
#XTXT: Description message for empty runs data
runsEmptyDescText=Když začnete novou činnost (načíst nový snímek, spustit analyzátor pohledu...), uvidíte zde související protokoly.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=Konfigurace elastického výpočetního uzlu {0} není odpovídajícím způsobem upravena. Zkontrolujte vaši definici.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Proces přidání elastického výpočetního uzlu {0} neúspěšný.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Vytvoření a aktivace repliky pro zdrojový objekt ''{0}''.''{1}'' bylo v elastickém výpočetním uzlu {2} spuštěno.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Odebrání repliky pro zdrojový objekt ''{0}''.''{1}'' z elastického výpočetního uzlu {2} bylo spuštěno.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Vytvoření a aktivace repliky pro zdrojový objekt ''{0}''.''{1}'' se nezdařila.
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Odebrání repliky pro tabulku ''{0}''.''{1}'' neúspěšné.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Směrování výpočtu analytických dotazů prostoru {0} pro elastický výpočetní uzel {1} spuštěno.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Směrování výpočtu analytických dotazů prostoru {0} pro příslušný elastický výpočetní uzel neúspěšné.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Přesměrování výpočtu analytických dotazů prostoru {0} zpět z elastického výpočetního uzlu {1} spuštěno.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Přesměrování výpočtu analytických dotazů prostoru {0} zpět na koordinátora se nezdařilo.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Řetězec úloh {0} pro odpovídající elastický výpočetní uzel {1} byl spuštěn.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Generování řetězce úloh pro elastický výpočetní uzel {0} se nezdařilo.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=Poskytování elastického výpočetního uzlu {0} s daným plánem dimenzování vCPUs: {1}, velikost paměti(GiB): {2} a velikost úložiště(GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=Elastický výpočetní uzel {0} byl již poskytnut.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=Poskytnutí elastického výpočetního uzlu {0} již bylo zrušeno.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=Operace není dovolena. Zastavte elastický výpočetní uzel {0} a restartujte jej, aby se aktualizoval plán velikostí.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Proces odebrání elastického výpočetního uzlu {0} neúspěšný.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=Aktuální doba běhu elastického výpočetního uzlu {0} vypršela.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=Kontrola statusu elastického výpočetního uzlu {0} probíhá...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=Generování řetězce úloh pro elastický výpočetní uzel {0} je blokováno, takže řetězec {1} může stále běžet.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=Zdrojový objekt "{0}"."{1}" je replikován jako závislost pohledu "{2}"."{3}".
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Tabulku ''{0}''.''{1}''‘ nelze replikovat, protože replikace řádkové tabulky je zastaralá.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Maximální počet elastických výpočetních uzlů na instanci SAP HANA Cloud byl překročen.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=Spouštění operace pro elastický výpočetní uzel {0} stále probíhá.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Kvůli technickým problémům bylo odstraňování repliky zdrojové tabulky {0} zastaveno. Zkuste to znovu později.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=Kvůli technickým problémům bylo vytváření repliky zdrojové tabulky {0} zastaveno. Zkuste to znovu později.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Nelze spustit elastický výpočetní uzel, protože bylo dosaženo limitu využití nebo ještě nebyly přiděleny žádné výpočetní blokové hodiny.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Načítání řetězce úloh a příprava na běh celkem {0} úloh, které jsou součástí toho řetězce.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Konfliktní úloha je již spuštěna
#XMSG: Replication will change
txt_replication_change=Typ replikace se změní.
txt_repl_viewdetails=Zobrazit detaily

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Zdá se, že došlo k chybě při opakování posledního spuštění, protože předchozí spuštění úlohy se nezdařilo, než bylo možné vygenerovat plán.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=Prostor "{0}" je blokovaný.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=Činnost {0} vyžaduje nasazení lokální tabulky {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=Činnost {0} vyžaduje, aby bylo pro lokální tabulku {1} zapnuto zaznamenání delta.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=Činnost {0} vyžaduje, aby lokální tabulka {1} ukládala data do úložiště dat.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=Činnost {0} vyžaduje, aby lokální tabulka {1} ukládala data do databáze.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Začínají se odebírat odstraněné záznamy z místní tabulky {0}.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Začínají se odstraňovat všechny záznamy z místní tabulky {0}.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Začínají se odstraňovat záznamy z místní tabulky {0} podle podmínky filtru {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Při odebírání odstraněných záznamů z místní tabulky {0} došlo k chybě.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Při odstraňování všech záznamů z místní tabulky {0} došlo k chybě.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Odstranit všechny kompletně zpracované záznamy s typem změny „Odstraněno“, které jsou starší než {0} dny.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Odstranit všechny kompletně zpracované záznamy s typem změny „Odstraněno“, které jsou starší než {0}.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} záznamy byly odstraněny.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} záznamů bylo označeno k odstranění.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=Odebrání odstraněných záznamů z místní tabulky {0} je dokončeno.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Odstranění všech záznamů z místní tabulky {0} je dokončeno.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Začínají se označovat záznamy jako "Odstraněné" pro místní tabulku {0}.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Začínají se označovat jako "Odstraněné" záznamy v místní tabulce {0} podle podmínky filtru {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Označování záznamů jako "Odstraněné" pro místní tabulku {0} je dokončeno.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Změny dat se dočasně načítají do tabulky {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Změny dat jsou dočasně načteny do tabulky {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Změny dat jsou zpracovány a odstraněny z tabulky {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Detaily připojení.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Zahájení optimalizace lokální tabulky (souboru).
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Při aktualizaci lokální tabulky (souboru) došlo k chybě.
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Došlo k chybě. Optimalizace lokální tabulky (souboru) byla zastavena.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Optimalizace lokální tabulky (soubor)...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=Lokální tabulka (soubor) byla optimalizována.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=Lokální tabulka (soubor) je optimalizována.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=Tabulka je optimalizována se sloupci Z-objednávky: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Došlo k chybě. Oříznutí lokální tabulky (souboru) bylo zastaveno.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Oříznutí lokální tabulky (soubor)...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=Místo vstupního bufferu pro lokální tabulku (soubor) bylo přetaženo.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Zahájení vyprázdnění (odstranění všech kompletně zpracovaných záznamů) lokální tabulky (souboru).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Při vyprazdňování lokální tabulky (souboru) došlo k chybě.
#XMSG: Task log message
LTF_VACUUM_STOPPED=Došlo k chybě. Vyprazdňování lokální tabulky (souboru) bylo zastaveno.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Vyprázdnění lokální tabulky (soubor)...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=Vyprázdnění je kompletní.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Odstranění všech kompletně zpracovaných záznamů, které jsou starší než {0} dny.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Odstranění všech kompletně zpracovaných záznamů, které jsou starší než {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Zahájení slučování nových záznamů s lokální tabulkou (soubor).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Zahájení slučování nových záznamů s místní tabulkou (souborem) pomocí nastavení „Sloučit data automaticky“.
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Zahájení slučování nových záznamů s lokální tabulkou (souborem). Tato úloha byla iniciována požadavkem sloučení API.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Slučování nových záznamů s lokální tabulkou (soubor).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Při slučování nových záznamů s lokální tabulkou (soubor) došlo k chybě.
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=Lokální tabulka (soubor) je sloučena.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Došlo k chybě. Slučování lokální tabulky (souboru) bylo zastaveno.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=Sloučení lokální tabulky (souboru) se nezdařilo kvůli chybě, ale operace byla částečně úspěšná a některá data již byla sloučena.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Došlo k chybě překročení času. Činnost {0} probíhala {1} hodin(y).
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=Asynchronní provedení nebylo možné zahájit kvůli velkému zatížení systému.  Otevřete ''Monitor systému' a zkontrolujte probíhající úlohy.'
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=Asynchronní provádění bylo zrušeno.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=Úloha {0} probíhala v rámci limitů paměti {1} a {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=Úloha {0} byla spuštěna s ID zdroje {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=Hledání a nahrazení bylo zahájeno v lokální tabulce (souboru).
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Hledání a nahrazení bylo dokončeno v lokální tabulce (souboru).
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Hledání a nahrazení bylo neúspěšné v lokální tabulce (souboru).

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Došlo k chybě. Aktualizace statistiky lokální tabulky (souboru) byla zastavena.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=Úloha se nezdařila kvůli chybě nedostatku paměti v databázi SAP HANA.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=Úloha se nezdařila z důvodu odmítnutí řízení přístupu SAP HANA.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=Úloha se nezdařila z důvodu příliš mnoha aktivních připojení SAP HANA.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=Operaci Opakovat nelze provést, protože opakování je povoleno pouze na nadřazeném řetězci vnořených úloh.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Protokoly neúspěšných podřízených úloh již nejsou k dispozici, abyste mohli pokračovat znovu.


####Metrics Labels

performanceOptimized=Výkon optimalizován
memoryOptimized=Paměť optimalizována

JOB_EXECUTION=Provedení jobu
EXECUTION_MODE=Režim běhu
NUMBER_OF_RECORDS_OVERALL=Počet perzistentních záznamů
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Počet záznamů načtených ze vzdáleného zdroje
RUNTIME_MS_REMOTE_EXECUTION_TIME=Čas zpracování vzdáleného zdroje
MEMORY_CONSUMPTION_GIB=Paměťová špička SAP HANA
NUMBER_OF_PARTITIONS=Počet oddílů
MEMORY_CONSUMPTION_GIB_OVERALL=Paměťová špička SAP HANA
NUMBER_OF_PARTITIONS_LOCKED=Počet blokovaných oddílů
PARTITIONING_COLUMN=Segmentační sloupec
HANA_PEAK_CPU_TIME=Celkový čas CPU SAP HANA
USED_IN_DISK=Využité úložiště
INPUT_PARAMETER_PARAMETER_VALUE=Vstupní parametr
INPUT_PARAMETER=Vstupní parametr
ECN_ID=Název elastického výpočetního uzlu

DAC=Řízení přístupu k datům
YES=Ano
NO=Ne
noofrecords=Počet záznamů
partitionpeakmemory=Paměťová špička SAP HANA
value=Hodnota
metricsTitle=Metriky ({0})
partitionmetricsTitle=Oddíly ({0})
partitionLabel=Oddíl
OthersNotNull=Hodnoty nejsou v rozsahu zahrnuty
OthersNull=Hodnoty null
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Nastavení použitá pro poslední běh perzistence dat:
#XMSG: Message for input parameter name
inputParameterLabel=Vstupní parametr
#XMSG: Message for input parameter value
inputParameterValueLabel=Hodnota
#XMSG: Message for persisted data
inputParameterPersistedLabel=Trvale uloženo v
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Odstranit data
REMOVE_DELETED_RECORDS=Odebrat odstraněné záznamy
MERGE_FILES=Sloučit soubory
OPTIMIZE_FILES=Optimalizovat soubory
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Zobrazit v monitoru SAP BW Bridge

ANALYZE_PERFORMANCE=Analyzovat výkon
CANCEL_ANALYZE_PERFORMANCE=Zrušit analýzu výkonu

#XFLD: Label for frequency column
everyLabel=Každé
#XFLD: Plural Recurrence text for Hour
hoursLabel=Hodiny
#XFLD: Plural Recurrence text for Day
daysLabel=Dny
#XFLD: Plural Recurrence text for Month
monthsLabel=Měsíce
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuty

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Zobrazit příručku pro řešení problémů s perzistencí</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Nedostatek paměti pro proces. Nelze trvale uložit data pro pohled "{0}". Další informace o chybách z nedostatku paměti viz portál nápovědy. Zvažte kontrolu analyzátoru pohledu, abyste pohled komplexně analyzovali ohledně spotřeby paměti.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Nerelevantní
OPEN_BRACKET=(
CLOSE_BRACKET=)
