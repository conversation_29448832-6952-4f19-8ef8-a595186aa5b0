
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=ログの詳細
#XFLD: Header
TASK_LOGS=タスクログ ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=実行 ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=詳細表示
#XFLD: Button text
STOP=実行停止
#XFLD: Label text
RUN_START=最終実行の開始
#XFLD: Label text
RUN_END=最終実行の終了
#XFLD: Label text
RUNTIME=期間
#XTIT: Count for Messages
txtDetailMessages=メッセージ ({0})
#XFLD: Label text
TIME=タイムスタンプ
#XFLD: Label text
MESSAGE=メッセージ
#XFLD: Label text
TASK_STATUS=カテゴリ
#XFLD: Label text
TASK_ACTIVITY=アクティビティ
#XFLD: Label text
RUN_START_DETAILS=開始
#XFLD: Label text
RUN_END_DETAILS=終了
#XFLD: Label text
LOGS=実行
#XFLD: Label text
STATUS=ステータス
#XFLD: Label text
RUN_STATUS=実行ステータス
#XFLD: Label text
Runtime=期間
#XFLD: Label text
RuntimeTooltip=期間 (hh : mm : ss)
#XFLD: Label text
TRIGGEREDBY=トリガ要因
#XFLD: Label text
TRIGGEREDBYNew=実行者
#XFLD: Label text
TRIGGEREDBYNewImp=実行開始者
#XFLD: Label text
EXECUTIONTYPE=実行形式
#XFLD: Label text
EXECUTIONTYPENew=実行タイプ
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=上位チェーンスペース
#XFLD: Refresh tooltip
TEXT_REFRESH=リフレッシュ
#XFLD: view Details link
VIEW_ERROR_DETAILS=詳細表示
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=追加の詳細をダウンロード
#XMSG: Download completed
downloadStarted=ダウンロードを開始しました
#XMSG: Error while downloading content
errorInDownload=ダウンロード中にエラーが発生しました。
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=詳細表示
#XBTN: cancel button of task details dialog
TXT_CANCEL=キャンセル
#XBTN: back button from task details
TXT_BACK=戻る
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=タスク完了
#XFLD: Log message with failed status
MSG_LOG_FAILED=タスク失敗
#XFLD: Master and detail table with no data
No_Data=データなし
#XFLD: Retry tooltip
TEXT_RETRY=再試行
#XFLD: Cancel Run label
TEXT_CancelRun=実行をキャンセル
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=失敗したロードをクリーンアップ
#XMSG:button copy sql statement
txtSQLStatement=SQL 文をコピー
#XMSG:button open remote query monitor
txtOpenQueryMonitor=リモートクエリモニタを開く
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=リモート SQL 文を表示するには、"リモートクエリモニタを開く" をクリックします。
#XMSG:button ok
txtOk=OK
#XMSG: button close
txtClose=閉じる
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=オブジェクト "{0}" に対する実行アクションのキャンセルが開始されました。
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=オブジェクト "{0}" に対する実行アクションのキャンセルに失敗しました。
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=複製ステータスが変わったため、オブジェクト "{0}" に対する実行アクションをキャンセルできなくなりました。
#XMSG: Info message for no Running state logId
noTaskWithRunningState=どのタスクログのステータスも "実行中" ではありません。
#XMSG: message for conflicting task
Task_Already_Running=競合タスクがオブジェクト "{0}" に対してすでに実行されています。
#XFLD: Label for no task log with running state title
actionInfo=アクション情報
#XMSG Copied to clipboard
copiedToClip=クリップボードにコピー
#XFLD copy
Copy=コピー
#XFLD copy correlation ID
CopyCorrelationID=相関 ID をコピー
#XFLD Close
Close=閉じる
#XFLD: show more Label
txtShowMore=表示を増やす
#XFLD: message Label
messageLabel=メッセージ:
#XFLD: details Label
detailsLabel=詳細:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=実行 SQL \r\n文:
#XFLD:statementId Label
statementIdLabel=文 ID:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=リモート \r\nSQL 文の数:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=文の数
#XFLD: Space Label
txtSpaces=スペース
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=権限のないスペース
#XFLD: Privilege Error Text
txtPrivilegeError=このデータを表示するために十分な権限を持っていません
#XFLD: Label for Object Header
DATA_ACCESS=データアクセス
#XFLD: Label for Object Header
SCHEDULE=スケジュール
#XFLD: Label for Object Header
DETAILS=詳細
#XFLD: Label for Object Header
LATEST_UPDATE=最終更新
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=最終変更 (ソース)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=リフレッシュ頻度
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=スケジュールされた頻度
#XFLD: Label for Object Header
NEXT_RUN=次回の実行
#XFLD: Label for Object Header
CONNECTION=接続
#XFLD: Label for Object Header
DP_AGENT=DP エージェント
#XFLD: Label for Object Header
USED_IN_MEMORY=ストレージに使用されているメモリ (MiB)
#XFLD: Label for Object Header
USED_DISK=ストレージに使用されているディスク (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=インメモリサイズ (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=ディスク上のサイズ (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=レコード数

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=失敗に設定
SET_TO_FAILED_ERR=このタスクは実行されていましたが、ユーザがこのタスクのステータスを FAILED (失敗) に設定しました。
#XFLD: Label for stopped failed
FAILLOCKED=すでに実行中
#XFLD: sub status STOPPED
STOPPED=停止
STOPPED_ERR=このタスクは停止されましたが、ロールバックは実行されませんでした。
#XFLD: sub status CANCELLED
CANCELLED=キャンセル
CANCELLED_ERR=このタスクの実行は開始された後、キャンセルされました。その際、データはロールバックされて、タスク実行が最初にトリガされた前の状態に復元されました。
#XFLD: sub status LOCKED
LOCKED=ロック済み
LOCKED_ERR=同じタスクがすでに実行されていたため、このタスクジョブは既存タスクの実行と並列的には実行できません。
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=タスク例外
TASK_EXCEPTION_ERR=このタスクの実行中に不明なエラーが発生しました。
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=タスク実行例外
TASK_EXECUTE_EXCEPTION_ERR=このタスクの実行中にエラーが発生しました。
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=非認可
UNAUTHORIZED_ERR=ユーザを認可できませんでした。ユーザはロックされているか、削除されています。
#XFLD: sub status FORBIDDEN
FORBIDDEN=禁止
FORBIDDEN_ERR=指名されたユーザは、このタスクを実行するのに必要な権限を持っていません。
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=トリガ未了
FAIL_NOT_TRIGGERED_ERR=計画した実行時間にデータベースシステムの一部分が利用できないか、システムの停電が原因で、このタスクジョブを実行できませんでした。スケジュール済みの次回のジョブ実行時間まで待つか、ジョブを再スケジュールしてください。
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=スケジュールキャンセル済み
SCHEDULE_CANCELLED_ERR=内部エラーが原因で、このタスクジョブを実行できませんでした。SAP サポートに連絡して、このタスクジョブのログ詳細情報から相関 ID とタイムスタンプを伝えてください。
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=正常終了スキップ済み
SUCCESS_SKIPPED_ERR=同じタスクの以前の実行がまだ進行中であるため、このタスクの実行はトリガされませんでした。
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=オーナー不在
FAIL_OWNER_MISSING_ERR=システムユーザが割り当てられていないため、このタスクジョブを実行できませんでした。オーナーユーザをジョブに割り当ててください。
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=同意利用不可
FAIL_CONSENT_NOT_AVAILABLE_ERR=タスクチェーンまたはスケジュールデータ統合タスクをユーザの代理として実行する権限を SAP に付与していません。ユーザの同意を示すためのオプションを選択してください。
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=同意期限切れ
FAIL_CONSENT_EXPIRED_ERR=タスクチェーンまたはスケジュールデータ統合タスクをユーザの代理として SAP に実行させる権限の有効期限が切れました。同意を更新するためのオプションを選択してください。
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=同意が無効
FAIL_CONSENT_INVALIDATED_ERR=テナントのアイデンティティプロバイダ (IdP) 設定の変更を一般的な理由として、このタスクを実行できませんでした。この場合、影響を受けたユーザの名前で、新しいタスクジョブを実行またはスケジュールすることはできません。割り当て済みのユーザが新しい IdP にまだ存在している場合は、スケジュール同意を取り消してから、もう一度付与し直してください。割り当て済みのユーザがもう存在しない場合は、新しいタスクジョブオーナーを割り当て、必要なタスクスケジュール同意を付与してください。詳細については、SAP ノート: https://launchpad.support.sap.com/#/notes/3089828 for more information を参照してください。
TASK_EXECUTOR_ERROR=タスク実行者
TASK_EXECUTOR_ERROR_ERR=実行の準備ステップ中との場合と同様に、このタスクで内部エラーが発生したため、タスを開始できませんでした。
PREREQ_NOT_MET=前提条件が一致しません
PREREQ_NOT_MET_ERR=定義に問題がある (オブジェクトがデプロイされていない、タスクチェーンに循環ロジックが含まれている、またはビューの SQL が無効など) ため、このタスクを実行できませんでした。
RESOURCE_LIMIT_ERROR=リソース制限エラー
RESOURCE_LIMIT_ERROR_ERR=十分なリソースを利用できなかったか、十分なリソースがビジーであったため、現在タスクを実行できません。
FAIL_CONSENT_REFUSED_BY_UMS=同意拒否済み
FAIL_CONSENT_REFUSED_BY_UMS_ERR=テナントでユーザのアイデンティティプロバイダ設定が変更されたため、スケジュールされた実行またはタスクチェーンでこのタスクを実行できませんでした。詳細については、SAP ノート: https://launchpad.support.sap.com/#/notes/3120806 を参照してください。
#XFLD: status text
SCHEDULED=スケジュール済み
#XFLD: status text
SCHEDULEDNew=永続
#XFLD: status text
PAUSED=一時停止
#XFLD: status text
DIRECT=直接
#XFLD: status text
MANUAL=マニュアル
#XFLD: status text
DIRECTNew=簡易
#XFLD: status text
COMPLETED=完了
#XFLD: status text
FAILED=失敗
#XFLD: status text
RUNNING=実行中
#XFLD: status text
none=なし
#XFLD: status text
realtime=リアルタイム
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=サブタスク
#XFLD: New Data available in the file
NEW_DATA=新規データ

#XFLD: text for values shown in column Replication Status
txtOff=オフ
#XFLD: text for values shown in column Replication Status
txtInitializing=初期化中
#XFLD: text for values shown in column Replication Status
txtLoading=ロード中
#XFLD: text for values shown in column Replication Status
txtActive=有効
#XFLD: text for values shown in column Replication Status
txtAvailable=利用可能
#XFLD: text for values shown in column Replication Status
txtError=エラー
#XFLD: text for values shown in column Replication Status
txtPaused=一時停止
#XFLD: text for values shown in column Replication Status
txtDisconnected=接続解除済み
#XFLD: text for partially Persisted views
partiallyPersisted=部分的に永続化済み

#XFLD: activity text
REPLICATE=複製
#XFLD: activity text
REMOVE_REPLICATED_DATA=複製されたデータの削除
#XFLD: activity text
DISABLE_REALTIME=リアルタイムデータ複製を無効化
#XFLD: activity text
REMOVE_PERSISTED_DATA=永続化されたデータを削除
#XFLD: activity text
PERSIST=永続
#XFLD: activity text
EXECUTE=実行
#XFLD: activity text
TASKLOG_CLEANUP=Tacklog_Cleanup
#XFLD: activity text
CANCEL_REPLICATION=複製をキャンセル
#XFLD: activity text
MODEL_IMPORT=モデルのインポート
#XFLD: activity text
NONE=なし
#XFLD: activity text
CANCEL_PERSISTENCY=永続性を取り消す
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=分析ビュー
#XFLD: activity text
CANCEL_VIEW_ANALYZER=ビューアナライザのキャンセル

#XFLD: severity text
INFORMATION=情報
#XFLD: severity text
SUCCESS=正常終了
#XFLD: severity text
WARNING=警告
#XFLD: severity text
ERROR=エラー
#XFLD: text for values shown for Ascending sort order
SortInAsc=昇順ソート
#XFLD: text for values shown for Descending sort order
SortInDesc=降順ソート
#XFLD: filter text for task log columns
Filter=フィルタ
#XFLD: object text for task log columns
Object=オブジェクト
#XFLD: space text for task log columns
crossSpace=スペース

#XBUT: label for remote data access
REMOTE=リモート
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=複製済 (リアルタイム)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=複製済 (スナップショット)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=エラーが原因でリアルタイム複製がブロックされています。エラーの修正が完了したら、"再試行" アクションを使用してリアルタイム複製を続行できます。
ERROR_MSG=エラーが原因でリアルタイム複製がブロックされています。
RETRY_FAILED_ERROR=再試行プロセスがエラーで失敗しました。
LOG_INFO_DETAILS=データがリアルタイムモードで複製されるときには、ログは生成されません。表示されたログは以前のアクションに関連するものです。

#XBUT: Partitioning label
partitionMenuText=分割
#XBUT: Drop down menu button to create a partition
createPartitionLabel=パーティションを作成
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=パーティションを編集
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=パーティションを削除
#XFLD: Initial text
InitialPartitionText=大きいデータセットを小さいデータセットに分割する基準を指定することで、パーティションを定義してください。
DefinePartition=パーティションを定義
#XFLD: Message text
partitionChangedInfo=最後の複製以降にパーティション定義が変更されました。この変更は次回のデータロード時に適用されます。
#XFLD: Message text
REAL_TIME_WARNING=パーティショニングは新規スナップショットのロード時のみに適用され、リアルタイム複製には適用されません。
#XFLD: Message text
loadSelectedPartitions="{0}" で選択したパーティションに対してデータの永続化を開始しました。
#XFLD: Message text
loadSelectedPartitionsError="{0}" で選択したパーティションに対するデータの永続化が失敗しました。
#XFLD: Message text
viewpartitionChangedInfo=最後の永続化実行以降にパーティション定義が変更されました。変更を適用するために、次回のデータロードは完全スナップショットとなります (ロックされたパーティションを含む)。この完全ロードが完了すると、個別パーティションに対してデータを実行することができます。
#XFLD: Message text
viewpartitionChangedInfoLocked=最後の永続化実行以降にパーティション定義が変更されました。変更を適用するために、次回のデータロードは完全スナップショットとなります (ロックされた未変更のパーティション範囲を除く)。このロードが完了すると、選択したパーティションをもう一度ロードできるようになります。
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=テーブルの複製
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=複製をスケジュール
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=スケジュールを作成
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=スケジュールを編集
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=スケジュールを削除
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=新規スナップショットをロード
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=データ複製を開始
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=複製されたデータを削除
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=リアルタイムアクセスを有効化
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=リアルタイムデータ複製を有効化
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=リアルタイムデータ複製を無効化
#XFLD: Message for replicate table action
replicateTableText=テーブルの複製
#XFLD: Message for replicate table action
replicateTableTextNew=データ複製
#XFLD: Message to schedule task
scheduleText=スケジュール
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=ビュー永続性
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=データ永続性
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=新規スナップショットをロード
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=データ永続性を開始
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=永続化されたデータを削除
#XFLD: Partitioned Processign
EnablePartitionedProcessing=分割された処理
#XBUT: Label for scheduled replication
scheduledTxt=スケジュール済み
#XBUT: Label for statistics button
statisticsTxt=統計
#XBUT: Label for create statistics
createStatsTxt=統計を作成
#XBUT: Label for edit statistics
editStatsTxt=統計を編集
#XBUT: Label for refresh statistics
refreshStatsTxt=統計をリフレッシュ
#XBUT: Label for delete statistics
dropStatsTxt=統計を削除
#XMSG: Create statistics success message
statsSuccessTxt={1} に対して {0} タイプの統計の作成を開始します。
#XMSG: Edit statistics success message
statsEditSuccessTxt={1} の統計タイプを {0} に変更する作業を開始します。
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt={0} に対して統計のリフレッシュを開始します。
#XMSG: Drop statistics success message
statsDropSuccessTxt={0} の統計が正常に削除されました
#XMSG: Create statistics error message
statsCreateErrorTxt=統計作成時のエラー
#XMSG: Edit statistics error message
statsEditErrorTxt=統計変更時のエラー
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=統計リフレッシュ時のエラー
#XMSG: Drop statistics error message
statsDropErrorTxt=統計削除時のエラー
#XMG: Warning text for deleting statistics
statsDelWarnTxt=データ統計を削除してもよろしいですか?
startPersistencyAdvisorLabel=ビューアナライザを開始

#Partition related texts
#XFLD: Label for Column
column=列
#XFLD: Label for No of Partition
noOfPartitions=パーティションの数
#XFLD: Label for Column
noOfParallelProcess=並列処理の数
#XFLD: Label text
noOfLockedPartition=ロックされたパーティションの数
#XFLD: Label for Partition
PARTITION=パーティション
#XFLD: Label for Column
AVAILABLE=利用可能
#XFLD: Statistics Label
statsLabel=統計
#XFLD: Label text
COLUMN=列:
#XFLD: Label text
PARALLEL_PROCESSES=並列プロセス:
#XFLD: Label text
Partition_Range=パーティション範囲
#XFLD: Label text
Name=名前
#XFLD: Label text
Locked=ロック済み
#XFLD: Label text
Others=その他
#XFLD: Label text
Delete=削除
#XFLD: Label text
LoadData=選択したパーティションをロード
#XFLD: Label text
LoadSelectedData=選択したパーティションをロード
#XFLD: Confirmation text
LoadNewPersistenceConfirm=これにより、選択したロック解除パーティションおよび変更済みパーティションだけでなく、すべてのロック解除パーティションおよび変更済みパーティションの新しいスナップショットがロードされます。続行しますか?
#XFLD: Label text
Continue=続行

#XFLD: Label text
PARTITIONS=パーティション
#XFLD: Label text
ADD_PARTITIONS=+ パーティションを追加
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=パーティションを追加
#XFLD: Label text
deleteRange=パーティションを削除
#XFLD: Label text
LOW_PLACE_HOLDER=下限値を入力
#XFLD: Label text
HIGH_PLACE_HOLDER=上限値を入力
#XFLD: tooltip text
lockedTooltip=初期ロード後にパーティションをロックする

#XFLD: Button text
Edit=編集
#XFLD: Button text
CANCEL=キャンセル

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=最後の統計更新
#XFLD: Statistics Fields
STATISTICS=統計

#XFLD:Retry label
TEXT_Retry=再試行
#XFLD:Retry label
TEXT_Retry_tooltip=エラーの修正が完了したら、リアルタイム複製をもう一度実行してください。
#XFLD: text retry
Retry=確認
#XMG: Retry confirmation text
retryConfirmationTxt=最後のリアルタイム複製がエラーで終了しました。\nエラーが修正されたこと、およびリアルタイム複製を再開できることを確認してください。
#XMG: Retry success text
retrySuccess=再試行プロセスが正常に開始されました。
#XMG: Retry fail text
retryFail=再試行プロセスが失敗しました。
#XMSG: activity message for create statistics
CREATE_STATISTICS=統計を作成
#XMSG: activity message for edit statistics
DROP_STATISTICS=統計を削除
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=統計をリフレッシュ
#XMSG: activity message for edit statistics
ALTER_STATISTICS=統計を編集
#XMSG: Task log message started task
taskStarted=タスク {0} が開始されました。
#XMSG: Task log message for finished task
taskFinished=タスク {0} がステータス {1} で終了しました。
#XMSG: Task log message for finished task with end time
taskFinishedAt=タスク {0} が {2} においてステータス {1} で終了しました。
#XMSG: Task {0} has input parameters
taskHasInputParameters=タスク {0} に入力パラメータがあります。
#XMSG: Task log message for unexpected error
unexpectedExecutionError=タスク {0} が予期しないエラーで終了しました。タスクのステータスは {1} に設定されました。
#XMSG: Task log message for failed task
failedToEnd=ステータスを {0} に設定できなかったか、ロックの解除に失敗しました。
#XMSG: Task log message
lockNotFound=ロックが見つからないため、プロセスを終了できません。タスクがキャンセルされた可能性があります。
#XMSG: Task log message failed task
failedOverwrite=タスク {0} はすでに {1} によってロックされているため、次のエラーで失敗しました: {2}。
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=このタスクのロックは、別のタスクによって引き継がれました。
#XMSG: Task log message failed takeover
failedTakeover=既存のタスクを引き継ぎできませんでした。
#XMSG: Task log message successful takeover
successTakeover=ロックを解除する必要がありました。このタスクに対する新しいロックが設定されています。
#XMSG: Tasklog Dialog Details
txtDetails=実行中に処理されるリモート文は、リモートクエリモニタを開くことで、パーティション固有メッセージの詳細に表示することができます。
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=リモート SQL 文がデータベースから削除されたため、表示できません。
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=他のスペースに割り当てられている接続があるリモートクエリを表示できません。リモートクエリモニタに移動し、文 ID を使用してそれらのリモートクエリをフィルタリングしてください。
#XMSG: Task log message for parallel check error
parallelCheckError=別のタスクが実行されており、すでにこのタスクをロックしているため、このタスクは処理できません。
#XMSG: Task log message for parallel running task
parallelTaskRunning=競合タスクがすでに実行されています。
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=相関 ID {1} で実行中のステータス {0}。
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=指名されたユーザは、このタスクを実行するのに必要な権限を持っていません。

#XBUT: Label for open in Editor
openInEditor=エディタで開く
#XBUT: Label for open in Editor
openInEditorNew=データビルダで開く
#XFLD:Run deails label
runDetails=実行詳細
#XFLD: Label for Logs
Logs=ログ
#XFLD: Label for Settings
Settings=設定
#XFLD: Label for Save button
Save=保存
#XFLD: Label for Standard
Standard_PO=パフォーマンス最適化 (推奨)
#XFLD: Label for Hana low memory processing
HLMP_MO=メモリ最適化
#XFLD: Label for execution mode
ExecutionMode=実行モード
#XFLD: Label for job execution
jobExecution=処理モード
#XFLD: Label for Synchronous
syncExec=同期
#XFLD: Label for Asynchronous
asyncExec=非同期
#XFLD: Label for default asynchronous execution
defaultAsyncExec=デフォルトを使用 (非同期、今後変更される可能性あり)
#XMSG: Save settings success
saveSettingsSuccess=SAP HANA 実行モードが変更されました。
#XMSG: Save settings failure
saveSettingsFailed=SAP HANA 実行モードを変更できませんでした。
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=ジョブ実行が変更されました。
#XMSG: Job Execution change failed
jobExecSettingFailed=ジョブ実行を変更できませんでした。
#XMSG: Text for Type
typeTxt=タイプ
#XMSG: Text for Monitor
monitorTxt=監視
#XMSG: Text for activity
activityTxt=アクティビティ
#XMSG: Text for metrics
metricsTxt=メトリクス
#XTXT: Text for Task chain key
TASK_CHAINS=タスクチェーン
#XTXT: Text for View Key
VIEWS=表示
#XTXT: Text for remote table key
REMOTE_TABLES=リモートテーブル
#XTXT: Text for Space key
SPACE=スペース
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Elastic Compute ノード
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=複製フロー
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=インテリジェントルックアップ
#XTXT: Text for Local Table
LOCAL_TABLE=ローカルテーブル
#XTXT: Text for Data flow key
DATA_FLOWS=データフロー
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=SQLScript プロシージャ
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=BW プロセスチェーン
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=モニタで表示
#XTXT: Task List header text
taskListHeader=タスクリスト ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=データフローの過去の実行に関するメトリクスを取得できません。
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=現在のところ、完了した実行の詳細がロードされていません。リフレッシュを実行してみてください。
#XFLD: Label text for the Metrices table header
metricesColLabel=演算子ラベル
#XFLD: Label text for the Metrices table header
metricesType=タイプ
#XFLD: Label text for the Metrices table header
metricesRecordLabel=レコード数
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=タスクチェーンを実行
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=タスクチェーンの実行が開始されました。
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg={0} のタスクチェーンの実行が開始されました。
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=タスクチェーンを実行できませんでした。
#XTXT: Execute button label
runLabel=実行
#XTXT: Execute button label
runLabelNew=実行を開始
#XMSG: Filter Object header
chainsFilteredTableHeader=オブジェクトによってフィルタリング済: {0}
#XFLD: Parent task chain label
parentChainLabel=上位タスクチェーン: 
#XFLD: Parent task chain unauthorized
Unauthorized=表示権限なし
#XFLD: Parent task chain label
parentTaskLabel=上位タスク: 
#XTXT: Task status
NOT_TRIGGERED=トリガ未了
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=全画面モードを開始
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=全画面モードを終了
#XTXT: Close Task log details right panel
closeRightColumn=セクションを閉じる
#XTXT: Sort Text
sortTxt=ソート
#XTXT: Filter Text
filterTxt=フィルタ
#XTXT: Filter by text to show list of filters applied
filterByTxt=フィルタ基準
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=5 分超
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=15 分超
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=1 時間超
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=過去 1 時間
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=過去 24 時間
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=先月
#XTXT: Messages title text
messagesText=メッセージ

#XTXT Statistics information message
statisticsInfo=データアクセスが "複製済み" であるリモートテーブルに対して統計を生成できません。統計を生成するには、複製済みのデータをリモートテーブル詳細モニタで削除してください。
#XFLD
GO_TO_REMOTETABLE_DETAILS=リモートテーブル詳細モニタに移動

#XTXT: Repair latest failed run label
retryRunLabel=最新実行を再試行
#XTXT: Repair failed run label
retryRun=実行を再試行
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=タスクチェーンの再試行実行が開始されました。
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=タスクチェーンの再試行実行が失敗しました。
#XMSG: Task chain child elements name
taskChainRetryChildObject=タスク {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=新規タスク
#XFLD Analyzed View
analyzedView=分析されたビュー
#XFLD Metrics
Metrics=メトリクス
#XFLD Partition Metrics
PartitionMetrics=パーティションメトリクス
#XFLD Entities
Messages=タスクログ
#XTXT: Title Message for empty partition data
partitionEmptyTitle=パーティションがまだ定義されていません。
#XTXT: Description message for empty partition data
partitionEmptyDescText=大きなデータボリュームを管理しやすい小さな部分に分割する基準を指定することで、パーティションを作成してください。

#XTXT: Title Message for empty runs data
runsEmptyTitle=まだログを利用できません
#XTXT: Description message for empty runs data
runsEmptyDescText=新しいアクティビティを開始 (新しいスナップショットをロード、ビューアナライザを開始...) すると、関連するログがここに表示されます。


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=Elastic Compute ノード {0} の設定は適宜更新されません。定義を確認してください。
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Elastic Compute ノード {0} を追加するプロセスが失敗しました。
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Elastic Compute ノード {2} でソースオブジェクト "{0}"."{1}" のレプリカの作成および有効化が開始されました。
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Elastic Compute ノード {2} からのソースオブジェクト "{0}"."{1}" のレプリカの削除が開始されました。
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=ソースオブジェクト "{0}"."{1}" のレプリカの作成および有効化に失敗しました。
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=ソースオブジェクト "{0}"."{1}" のレプリカの削除に失敗しました。
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=スペース {0} の分析クエリ計算の、Elastic Compute ノード {1} へのルーティングが開始されました。
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=スペース {0} の分析クエリ計算の、対応する Elastic Compute ノードへのルーティングが失敗しました。
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=スペース {0} の分析クエリ計算の、Elastic Compute ノード {1} からのリルーティングが開始されました。
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=スペース {0} の分析クエリ計算の、コーディネータへのリルーティングが失敗しました。
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=対応する Elastic Compute ノード {0} に対するタスクチェーン {1} がトリガされました。
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Elastic Compute ノード {0} に対するタスクチェーンの生成が失敗しました。
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=指定されたサイジング計画の Elastic Compute ノード {0} のプロビジョニングが開始されました: {1}、メモリ (GiB): {2}、ストレージサイズ (GiB): {3}。
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=Elastic Compute ノード {0} はすでにプロビジョニングされています。
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=Elastic Compute ノード {0} はすでにプロビジョニング解除されました。
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=演算は許可されません。Elastic Compute ノード {0} を停止し、再開して、サイジング計画を更新してください。
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Elastic Compute ノード {0} を削除するプロセスが失敗しました。
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=Elastic Compute ノード {0} の現在の実行がタイムアウトしました。
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=Elastic Compute ノード {0} のステータスチェックが進行中...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=チェーン {1} はまだ実行中である可能性があるため、Elastic Compute ノード {0} のタスクチェーン生成がロックされました。
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=ソースオブジェクト "{0}"."{1}" はビュー "{2}"."{3}" の依存関係として複製されます。
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=行テーブルの複製が非推奨になっているため、テーブル "{0}"."{1}" の複製はできません。
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=SAP HANA Cloud インスタンスごとの最大 Elastic Compute ノードを超えています。
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=Elastic Compute ノード {0} の実行操作がまだ進行中です。
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=技術的な問題のため、ソーステーブル {0} のレプリカの削除が停止しました。後でもう一度実行してください。
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=技術的な問題のため、ソーステーブル {0} のレプリカの作成が停止しました。後でもう一度実行してください。
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=使用量の上限に達したため、または計算ブロック時間がまだ割り当てられていないため、Elastic Compute ノードを起動できません。
#XMSG: Task log message for chain preparation
chainLoadFromRepository=タスクチェーンをロードするとともに、そのタスクチェーンの一部である合計 {0} 個のタスクを実行する準備をしています。
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=競合タスクがすでに実行されています
#XMSG: Replication will change
txt_replication_change=複製タイプが変更されます。
txt_repl_viewdetails=詳細表示

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=計画の生成前に先行タスクの実行に失敗したため、最新実行の再試行でエラーが発生したようです。

#general messages
EXECUTION_ERROR_LOCKED_SPACE=スペース "{0}" はロックされています。

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=アクティビティ {0} では、ローカルテーブル {1} をデプロイする必要があります。
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=アクティビティ {0} では、ローカルテーブル {1} に対してデルタキャプチャをオンに切り替える必要があります。
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=アクティビティ {0} では、ローカルテーブル {1} からオブジェクトストアにデータが保存される必要があります。
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=アクティビティ {0} では、ローカルテーブル {1} からデータベースにデータが保存される必要があります。

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=ローカルテーブル {0} の削除済みレコードの除外を開始しています。
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=ローカルテーブル {0} のすべてのレコードの削除を開始しています。
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=フィルタ条件 {1} に従ってローカルテーブル {0} のレコードの削除を開始しています。
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=ローカルテーブル {0} の削除済みレコードの除外中にエラーが発生しました。
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=ローカルテーブル {0} のすべてのレコードの削除中にエラーが発生しました。
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO={0} 日よりも古い、変更タイプが "削除済み" の完全に処理されたレコードをすべて削除しています
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING={0} よりも古い、変更タイプが "削除済み" の完全に処理されたレコードをすべて削除しています。
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} 件のレコードが削除されました。
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} 件のレコードが削除に設定されました。
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=ローカルテーブル {0} の削除済みレコードの除外が完了しました。
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=ローカルテーブル {0} のすべてのレコードの削除が完了しました。
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=ローカルテーブル {0} のレコードの "削除済み" としての設定を開始しています。
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=フィルタ条件 {1} に従ってローカルテーブル {0} のレコードの "削除済み" としての設定を開始しています。
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=ローカルテーブル {0} のレコードの "削除済み" としての設定が完了しました。

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=データ変更は一次的にテーブル {0} にロードされます。
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=データ変更は一次的にテーブル {0} にロードされました。
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=データ変更が処理され、テーブル {0} から削除されました。

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=接続詳細です。

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=ローカルテーブル (ファイル) の最適化を開始しています。
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=ローカルテーブル (ファイル) の最適化中にエラーが発生しました。
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=エラーが発生しました。ローカルテーブル (ファイル) の最適化が停止されました。
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=ローカルテーブル (ファイル) を最適化しています...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=ローカルテーブル (ファイル) が最適化されました。
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=ローカルテーブル (ファイル) は最適化されています。
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=テーブルは Z オーダー列で最適化されています: {0}。

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=エラーが発生しました。ローカルテーブル (ファイル) の切り詰めが停止されました。
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=ローカルテーブル (ファイル) を切り詰めています...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=ローカルテーブル (ファイル) の受信バッファの場所が削除されました。

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=ローカルテーブル (ファイル) のバキューム (完全に処理されたすべてのレコードの削除) を開始しています。
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=ローカルテーブル (ファイル) のバキューム中にエラーが発生しました。
#XMSG: Task log message
LTF_VACUUM_STOPPED=エラーが発生しました。ローカルテーブル (ファイル) のバキュームが停止されました。
#XMSG: Task log message
LTF_VACUUM_PROCESSING=ローカルテーブル (ファイル) をバキュームしています...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=バキュームが完了しました。
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO={0} 日よりも古い、完全に処理されたレコードをすべて削除しています
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING={0} よりも古い、完全に処理されたレコードをすべて削除しています

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=新規レコードとローカルテーブル (ファイル) のマージを開始しています。
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING="データを自動的にマージ" 設定を使用して、ローカルテーブル (ファイル) と新規レコードとのマージを開始しています。
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=新規レコードとローカルテーブル (ファイル) のマージを開始しています。このタスクは、API マージ要求によって開始されました。
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=新規レコードとローカルテーブル (ファイル) をマージしています。
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=新規レコードとローカルテーブル (ファイル) のマージ中にエラーが発生しました。
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=ローカルテーブル (ファイル) はマージされています。
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=エラーが発生しました。ローカルテーブル (ファイル) のマージが停止されました。
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=エラーによってローカルテーブル (ファイル) のマージが失敗しましたが、操作は部分的に成功しており、一部のデータはすでにマージされています。
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=タイムアウトエラーが発生しました。アクティビティ {0} が {1} 時間実行されていました。
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=システム負荷が高いため、非同期実行を開始できませんでした。''システムモニタ'' を開き、実行中のタスクを確認してください。
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=非同期実行がキャンセルされました。
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION={0} タスクは {1} および {2} のメモリ制限内で実行されました。
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID={0} タスクがリソース ID {1} で実行されました。

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=ローカルテーブル (ファイル) で検索と置換が開始されました。
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=ローカルテーブル (ファイル) で検索と置換が完了しました。
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=ローカルテーブル (ファイル) で検索と置換が失敗しました。

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=エラーが発生しました。ローカルテーブル (ファイル) の統計の更新が停止されました。

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=SAP HANA データベースのメモリ不足エラーにより、タスクが失敗しました。
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=SAP HANA の受付制御の却下により、タスクが失敗しました。
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=有効な SAP HANA 接続が多すぎるため、タスクが失敗しました。

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=再試行はネストされたタスクチェーンの上位でのみ可能であるため、再試行操作を実行できませんでした。
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=失敗した下位タスクのログは再試行の続行に利用できなくなりました。


####Metrics Labels

performanceOptimized=パフォーマンス最適化
memoryOptimized=メモリ最適化

JOB_EXECUTION=ジョブ実行
EXECUTION_MODE=実行モード
NUMBER_OF_RECORDS_OVERALL=永続化レコード数
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=リモートソースから読み込まれるレコード数
RUNTIME_MS_REMOTE_EXECUTION_TIME=リモートソース処理時間
MEMORY_CONSUMPTION_GIB=SAP HAMA ピーク時メモリ
NUMBER_OF_PARTITIONS=パーティションの数
MEMORY_CONSUMPTION_GIB_OVERALL=SAP HAMA ピーク時メモリ
NUMBER_OF_PARTITIONS_LOCKED=ロックされたパーティションの数
PARTITIONING_COLUMN=パーティショニング列
HANA_PEAK_CPU_TIME=SAP HANA 合計 CPU 時間
USED_IN_DISK=使用済みのストレージ
INPUT_PARAMETER_PARAMETER_VALUE=入力パラメータ
INPUT_PARAMETER=入力パラメータ
ECN_ID=Elastic Compute ノード名

DAC=データアクセス制御
YES=はい
NO=いいえ
noofrecords=レコード数
partitionpeakmemory=SAP HAMA ピーク時メモリ
value=値
metricsTitle=メトリクス ({0})
partitionmetricsTitle=パーティション ({0})
partitionLabel=パーティション
OthersNotNull=範囲に含まれない値
OthersNull=Null 値
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=最後のデータ永続性実行に使用された設定
#XMSG: Message for input parameter name
inputParameterLabel=入力パラメータ
#XMSG: Message for input parameter value
inputParameterValueLabel=値
#XMSG: Message for persisted data
inputParameterPersistedLabel=永続化時刻
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=データを削除
REMOVE_DELETED_RECORDS=削除されたレコードを除外
MERGE_FILES=ファイルをマージ
OPTIMIZE_FILES=ファイルを最適化
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=SAP BW ブリッジモニタで表示

ANALYZE_PERFORMANCE=パフォーマンスを分析
CANCEL_ANALYZE_PERFORMANCE="パフォーマンスを分析" をキャンセル

#XFLD: Label for frequency column
everyLabel=間隔
#XFLD: Plural Recurrence text for Hour
hoursLabel=時間
#XFLD: Plural Recurrence text for Day
daysLabel=日
#XFLD: Plural Recurrence text for Month
monthsLabel=月
#XFLD: Plural Recurrence text for Minutes
minutesLabel=分

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">永続性トラブルシューティングガイドの表示</a>
#XTXT TEXT for view persistency guide link
OOMMessage=プロセスがメモリ不足です。ビュー "{0}" のデータを永続化できません。メモリ不足エラーの詳細については、ヘルプポータルを参照してください。ビューアナライザをチェックしてメモリ消費の複雑度についてビューを分析することを検討してください。

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=適用なし
OPEN_BRACKET=(
CLOSE_BRACKET=)
