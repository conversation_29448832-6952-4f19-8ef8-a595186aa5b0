
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Detalles de log
#XFLD: Header
TASK_LOGS=Logs de tarea ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Ejecuciones ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Ver detalles
#XFLD: Button text
STOP=Detener ejecución
#XFLD: Label text
RUN_START=Inicio de última ejecución
#XFLD: Label text
RUN_END=Fin de última ejecución
#XFLD: Label text
RUNTIME=Duración
#XTIT: Count for Messages
txtDetailMessages=Mensajes ({0})
#XFLD: Label text
TIME=Cronomarcador
#XFLD: Label text
MESSAGE=Mensaje
#XFLD: Label text
TASK_STATUS=Categoría
#XFLD: Label text
TASK_ACTIVITY=Actividad
#XFLD: Label text
RUN_START_DETAILS=Inicio
#XFLD: Label text
RUN_END_DETAILS=Fin
#XFLD: Label text
LOGS=Ejecuciones
#XFLD: Label text
STATUS=Estado
#XFLD: Label text
RUN_STATUS=Estado de ejecución
#XFLD: Label text
Runtime=Duración
#XFLD: Label text
RuntimeTooltip=Duración (hh:mm:ss)
#XFLD: Label text
TRIGGEREDBY=Desencadenado por
#XFLD: Label text
TRIGGEREDBYNew=Autor de la ejecución
#XFLD: Label text
TRIGGEREDBYNewImp=Autor del inicio de la ejecución
#XFLD: Label text
EXECUTIONTYPE=Tipo de ejecución
#XFLD: Label text
EXECUTIONTYPENew=Tipo de ejecución
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Espacio de cadena superior
#XFLD: Refresh tooltip
TEXT_REFRESH=Actualizar
#XFLD: view Details link
VIEW_ERROR_DETAILS=Ver detalles
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Descargar detalles adicionales
#XMSG: Download completed
downloadStarted=Se ha iniciado la descarga
#XMSG: Error while downloading content
errorInDownload=Se ha producido un error al descargar.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Ver detalles
#XBTN: cancel button of task details dialog
TXT_CANCEL=Cancelar
#XBTN: back button from task details
TXT_BACK=Atrás
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Tarea completada
#XFLD: Log message with failed status
MSG_LOG_FAILED=Tarea fallida
#XFLD: Master and detail table with no data
No_Data=Sin datos
#XFLD: Retry tooltip
TEXT_RETRY=Reintentar
#XFLD: Cancel Run label
TEXT_CancelRun=Cancelar ejecución
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=No se ha podido cargar la limpieza
#XMSG:button copy sql statement
txtSQLStatement=Copiar sentencia SQL
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Abrir monitor de consultas remotas
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=Para visualizar las sentencias SQL remotas, haga clic en "Abrir monitor de consultas remotas".
#XMSG:button ok
txtOk=OK
#XMSG: button close
txtClose=Cerrar
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=Se ha iniciado la cancelación de la acción de ejecución para el objeto ''{0}''.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=No se ha podido cancelar la acción de ejecución para el objeto ''{0}''.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=No es posible cancelar la acción de ejecución para el objeto ''{0}'' porque el estado de replicación ha cambiado.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=No hay logs de tareas con el estado En ejecución.
#XMSG: message for conflicting task
Task_Already_Running=Ya hay en ejecución una tarea en conflicto para el objeto "{0}".
#XFLD: Label for no task log with running state title
actionInfo=Información de la acción
#XMSG Copied to clipboard
copiedToClip=Copiado en el portapapeles
#XFLD copy
Copy=Copiar
#XFLD copy correlation ID
CopyCorrelationID=Copiar ID de correlación
#XFLD Close
Close=Cerrar
#XFLD: show more Label
txtShowMore=Mostrar más
#XFLD: message Label
messageLabel=Mensaje:
#XFLD: details Label
detailsLabel=Detalles:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Ejecutando sentencia SQL:
#XFLD:statementId Label
statementIdLabel=ID de sentencia:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Número de sentencias SQL remotas:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Número de sentencias
#XFLD: Space Label
txtSpaces=Espacio
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Espacios no autorizados
#XFLD: Privilege Error Text
txtPrivilegeError=No tiene autorizaciones suficientes para ver estos datos.
#XFLD: Label for Object Header
DATA_ACCESS=Acceso a datos
#XFLD: Label for Object Header
SCHEDULE=Programar
#XFLD: Label for Object Header
DETAILS=Detalles
#XFLD: Label for Object Header
LATEST_UPDATE=Última actualización
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Última modificación (fuente)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Frecuencia de actualización
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Frecuencia programada
#XFLD: Label for Object Header
NEXT_RUN=Siguiente ejecución
#XFLD: Label for Object Header
CONNECTION=Conexión
#XFLD: Label for Object Header
DP_AGENT=Agente de AD
#XFLD: Label for Object Header
USED_IN_MEMORY=Memoria utilizada para el almacenamiento (MiB)
#XFLD: Label for Object Header
USED_DISK=Disco utilizado para el almacenamiento (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Tamaño en memoria (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Tamaño en disco (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Número de registros

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Definir como fallida
SET_TO_FAILED_ERR=Esta tarea se estaba ejecutando, pero el usuario la ha definido como FALLIDA.
#XFLD: Label for stopped failed
FAILLOCKED=La ejecución ya está en curso
#XFLD: sub status STOPPED
STOPPED=Parada
STOPPED_ERR=Esta tarea se ha detenido, pero no se ha revertido.
#XFLD: sub status CANCELLED
CANCELLED=Cancelada
CANCELLED_ERR=Se ha cancelado la ejecución de esta tarea una vez iniciada. En este caso, los datos se han revertido y restaurado al estado que tenían antes de iniciar la ejecución de la tarea.
#XFLD: sub status LOCKED
LOCKED=Bloqueada
LOCKED_ERR=Ya hay en ejecución una tarea igual, por lo que no se puede ejecutar esta tarea en paralelo.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Excepción de tarea
TASK_EXCEPTION_ERR=Se ha producido un error indefinido durante la ejecución de esta tarea.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Excepción de ejecución de tarea
TASK_EXECUTE_EXCEPTION_ERR=Se ha producido un error durante la ejecución de esta tarea.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=No autorizado
UNAUTHORIZED_ERR=No se ha podido autenticar al usuario, o bien se ha bloqueado o eliminado.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Prohibido
FORBIDDEN_ERR=El usuario asignado no tiene las autorizaciones necesarias para ejecutar esta tarea.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=No desencadenada
FAIL_NOT_TRIGGERED_ERR=No se ha podido ejecutar esta tarea por un corte de suministro del sistema o porque parte del sistema de la base de datos no estaba disponible en el momento de la ejecución planificada. Espere a la siguiente hora de ejecución programada para la tarea o vuelva a programarla.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Programación cancelada
SCHEDULE_CANCELLED_ERR=No se ha podido ejecutar la tarea por un error interno. Póngase en contacto con el soporte de SAP y facilíteles el ID de la correlación y el cronomarcador que encontrará en la información detallada del log de la tarea.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Ejecución anterior en curso
SUCCESS_SKIPPED_ERR=No se ha iniciado la ejecución de esta tarea porque hay en curso aún una ejecución anterior de la misma tarea.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Falta el propietario
FAIL_OWNER_MISSING_ERR=No se ha podido ejecutar esta tarea porque no tiene asignado ningún usuario de sistema. Asigne un usuario responsable a la tarea.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Sin consentimiento
FAIL_CONSENT_NOT_AVAILABLE_ERR=No ha autorizado a SAP a ejecutar cadenas de tareas ni a programar tareas de integración de datos en su nombre. Seleccione la opción correspondiente para dar su consentimiento.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Consentimiento vencido
FAIL_CONSENT_EXPIRED_ERR=La autorización que permite a SAP ejecutar cadenas de tareas o programar tareas de integración de datos en su nombre ha vencido. Seleccione la opción correspondiente para renovar su consentimiento.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Consentimiento anulado
FAIL_CONSENT_INVALIDATED_ERR=No se ha podido ejecutar esta tarea por un cambio en la configuración del proveedor de identidades (IdP) del arrendatario. En este caso, no se podrán ejecutar ni programar tareas nuevas en nombre del usuario en cuestión. Si el usuario asignado sigue existiendo en el nuevo IdP, revoque el consentimiento de programación y vuelva a concederlo. Si el usuario asignado ya no existe, asigne un nuevo propietario de la tarea y conceda su consentimiento para programar la tarea como se requiere. Para obtener más información, consulte la siguiente Nota SAP: https://launchpad.support.sap.com/#/notes/3089828.
TASK_EXECUTOR_ERROR=Ejecutor de la tarea
TASK_EXECUTOR_ERROR_ERR=No se ha podido iniciar esta tarea porque se ha producido un error interno, probablemente durante los pasos de preparación de la ejecución.
PREREQ_NOT_MET=Requisito previo no cumplido
PREREQ_NOT_MET_ERR=No se ha podido ejecutar esta tarea debido a errores en su definición (por ejemplo, el objeto no está desplegado, hay una cadena de tareas que contiene una lógica circular o el SQL de una vista no es válido).
RESOURCE_LIMIT_ERROR=Error de límite de recurso
RESOURCE_LIMIT_ERROR_ERR=En estos momentos no se puede realizar la tarea porque no hay suficientes recursos o están ocupados.
FAIL_CONSENT_REFUSED_BY_UMS=Consentimiento rechazado
FAIL_CONSENT_REFUSED_BY_UMS_ERR=No se ha podido ejecutar esta tarea (en ejecuciones programadas o en cadenas de tareas) debido a una modificación en la configuración del proveedor de identidad de un usuario del arrendatario. Para obtener más información, consulte la siguiente nota SAP: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Programado
#XFLD: status text
SCHEDULEDNew=Permanente
#XFLD: status text
PAUSED=Interrumpida
#XFLD: status text
DIRECT=Directo
#XFLD: status text
MANUAL=Manual
#XFLD: status text
DIRECTNew=Simple
#XFLD: status text
COMPLETED=Finalizado
#XFLD: status text
FAILED=Error
#XFLD: status text
RUNNING=En ejecución
#XFLD: status text
none=Ninguno
#XFLD: status text
realtime=En tiempo real
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Subtarea
#XFLD: New Data available in the file
NEW_DATA=Datos nuevos

#XFLD: text for values shown in column Replication Status
txtOff=Desactivada
#XFLD: text for values shown in column Replication Status
txtInitializing=Inicializando
#XFLD: text for values shown in column Replication Status
txtLoading=Cargando
#XFLD: text for values shown in column Replication Status
txtActive=Activa
#XFLD: text for values shown in column Replication Status
txtAvailable=Disponible
#XFLD: text for values shown in column Replication Status
txtError=Error
#XFLD: text for values shown in column Replication Status
txtPaused=En pausa
#XFLD: text for values shown in column Replication Status
txtDisconnected=Desconectada
#XFLD: text for partially Persisted views
partiallyPersisted=Guardado de forma persistente parcialmente

#XFLD: activity text
REPLICATE=Replicar
#XFLD: activity text
REMOVE_REPLICATED_DATA=Quitar datos replicados
#XFLD: activity text
DISABLE_REALTIME=Desactivar replicación de datos en tiempo real
#XFLD: activity text
REMOVE_PERSISTED_DATA=Quitar datos guardados de forma persistente
#XFLD: activity text
PERSIST=Guardar de forma persistente
#XFLD: activity text
EXECUTE=Ejecutar
#XFLD: activity text
TASKLOG_CLEANUP=Tacklog_Cleanup
#XFLD: activity text
CANCEL_REPLICATION=Cancelar replicación
#XFLD: activity text
MODEL_IMPORT=Importación de modelo
#XFLD: activity text
NONE=Ninguno
#XFLD: activity text
CANCEL_PERSISTENCY=Cancelar persistencia
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Analizar vista
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Cancelar analizador de vistas

#XFLD: severity text
INFORMATION=Información
#XFLD: severity text
SUCCESS=Correcto
#XFLD: severity text
WARNING=Advertencia
#XFLD: severity text
ERROR=Error
#XFLD: text for values shown for Ascending sort order
SortInAsc=Orden ascendente
#XFLD: text for values shown for Descending sort order
SortInDesc=Orden descendente
#XFLD: filter text for task log columns
Filter=Filtro
#XFLD: object text for task log columns
Object=Objeto
#XFLD: space text for task log columns
crossSpace=Espacio

#XBUT: label for remote data access
REMOTE=Remoto
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Replicado (en tiempo real)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Replicado (instantánea)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=La replicación en tiempo real está bloqueada debido a un error. Una vez que se haya corregido, puede utilizar la acción "Reintentar" para continuar con la replicación en tiempo real.
ERROR_MSG=La replicación en tiempo real está bloqueada debido a un error.
RETRY_FAILED_ERROR=Se ha producido un error durante el proceso de reintento.
LOG_INFO_DETAILS=No se generan logs si se replican los datos en el modo de tiempo real. Los logs visualizados están relacionados con acciones previas.

#XBUT: Partitioning label
partitionMenuText=Partición
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Crear partición
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Editar partición
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Eliminar partición
#XFLD: Initial text
InitialPartitionText=Defina particiones especificando criterios para dividir conjuntos de datos grandes en conjuntos más pequeños.
DefinePartition=Definir particiones
#XFLD: Message text
partitionChangedInfo=La definición de la partición ha cambiado desde la última replicación. Las modificaciones se aplicarán en la próxima carga de datos.
#XFLD: Message text
REAL_TIME_WARNING=La partición solo se aplica al cargar una instantánea nueva. No se aplica a la replicación en tiempo real.
#XFLD: Message text
loadSelectedPartitions=Se ha empezado a guardar de forma persistente los datos para las particiones seleccionadas de ''{0}''
#XFLD: Message text
loadSelectedPartitionsError=No se ha podido guardar de forma persistente los datos para las particiones seleccionadas de ''{0}''
#XFLD: Message text
viewpartitionChangedInfo=La definición de la partición ha cambiado desde la última ejecución de la persistencia. Para aplicar las modificaciones, la próxima carga de datos será una instantánea completa que incluirá las particiones bloqueadas. Una vez finalizada la carga completa, podrá ejecutar datos para particiones individuales.
#XFLD: Message text
viewpartitionChangedInfoLocked=La definición de la partición ha cambiado desde la última ejecución de la persistencia. Para aplicar las modificaciones, la próxima carga de datos será una instantánea completa, a excepción de los rangos de partición bloqueados y no modificados. Una vez finalizada esta carga, podrá volver a cargar las particiones seleccionadas.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Replicación de tabla
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Programar replicación
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Crear programación
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Editar programación
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Eliminar programación
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Cargar nueva instantánea
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Iniciar replicación de datos
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Quitar datos replicados
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Activar acceso en tiempo real
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Activar replicación de datos en tiempo real
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Desactivar replicación de datos en tiempo real
#XFLD: Message for replicate table action
replicateTableText=Replicación de tabla
#XFLD: Message for replicate table action
replicateTableTextNew=Replicación de datos
#XFLD: Message to schedule task
scheduleText=Programar
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Persistencia de vista
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Persistencia de datos
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Cargar nueva instantánea
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Iniciar persistencia de datos
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Quitar datos guardados de forma persistente
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Procesamiento con particiones
#XBUT: Label for scheduled replication
scheduledTxt=Programada
#XBUT: Label for statistics button
statisticsTxt=Estadísticas
#XBUT: Label for create statistics
createStatsTxt=Crear estadísticas
#XBUT: Label for edit statistics
editStatsTxt=Editar estadísticas
#XBUT: Label for refresh statistics
refreshStatsTxt=Actualizar estadísticas
#XBUT: Label for delete statistics
dropStatsTxt=Eliminar estadísticas
#XMSG: Create statistics success message
statsSuccessTxt=Se ha empezado a crear estadísticas de la clase {0} para {1}.
#XMSG: Edit statistics success message
statsEditSuccessTxt=Se ha empezado a modificar la clase de estadística a {0} para {1}.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Se ha empezado a actualizar las estadísticas para {0}.
#XMSG: Drop statistics success message
statsDropSuccessTxt=Las estadísticas se han eliminado correctamente para {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=Error al crear las estadísticas
#XMSG: Edit statistics error message
statsEditErrorTxt=Error al modificar las estadísticas
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Error al actualizar las estadísticas
#XMSG: Drop statistics error message
statsDropErrorTxt=Error al eliminar las estadísticas
#XMG: Warning text for deleting statistics
statsDelWarnTxt=¿Seguro que desea eliminar las estadísticas de datos?
startPersistencyAdvisorLabel=Iniciar el Analizador de vistas

#Partition related texts
#XFLD: Label for Column
column=Columna
#XFLD: Label for No of Partition
noOfPartitions=Número de particiones
#XFLD: Label for Column
noOfParallelProcess=Número de procesos paralelos
#XFLD: Label text
noOfLockedPartition=Número de particiones bloqueadas
#XFLD: Label for Partition
PARTITION=Particiones
#XFLD: Label for Column
AVAILABLE=Disponible
#XFLD: Statistics Label
statsLabel=Estadísticas
#XFLD: Label text
COLUMN=Columna:
#XFLD: Label text
PARALLEL_PROCESSES=Procesos paralelos:
#XFLD: Label text
Partition_Range=Rango de partición
#XFLD: Label text
Name=Nombre
#XFLD: Label text
Locked=Bloqueado
#XFLD: Label text
Others=OTROS
#XFLD: Label text
Delete=Eliminar
#XFLD: Label text
LoadData=Cargar particiones seleccionadas
#XFLD: Label text
LoadSelectedData=Cargar particiones seleccionadas
#XFLD: Confirmation text
LoadNewPersistenceConfirm=Esto cargará una nueva instantánea para todas las particiones desbloqueadas y modificadas, no solo las que ha seleccionado. ¿Desea continuar?
#XFLD: Label text
Continue=Continuar

#XFLD: Label text
PARTITIONS=Particiones
#XFLD: Label text
ADD_PARTITIONS=+ Añadir partición
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Añadir partición
#XFLD: Label text
deleteRange=Eliminar partición
#XFLD: Label text
LOW_PLACE_HOLDER=Introducir valor inferior
#XFLD: Label text
HIGH_PLACE_HOLDER=Introducir valor superior
#XFLD: tooltip text
lockedTooltip=Bloquear partición después de la carga inicial

#XFLD: Button text
Edit=Editar
#XFLD: Button text
CANCEL=Cancelar

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Última actualización de las estadísticas
#XFLD: Statistics Fields
STATISTICS=Estadísticas

#XFLD:Retry label
TEXT_Retry=Reintentar
#XFLD:Retry label
TEXT_Retry_tooltip=Vuelva a intentar la replicación en tiempo real después de que se resuelva el error.
#XFLD: text retry
Retry=Confirmar
#XMG: Retry confirmation text
retryConfirmationTxt=La última replicación en tiempo real terminó con un error.\n Confirme que el error se ha corregido y que la replicación en tiempo real se puede reiniciar.
#XMG: Retry success text
retrySuccess=El proceso de reintento se ha iniciado correctamente.
#XMG: Retry fail text
retryFail=Proceso de reintento fallido.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Crear estadísticas
#XMSG: activity message for edit statistics
DROP_STATISTICS=Eliminar estadísticas
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Actualizar estadísticas
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Editar estadísticas  
#XMSG: Task log message started task
taskStarted=Se ha iniciado la tarea {0}.
#XMSG: Task log message for finished task
taskFinished=La tarea {0} ha finalizado con el estado {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=La tarea {0} finalizó a las {2} con el estado {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=La tarea {0} tiene parámetros de entrada.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=La tarea {0} ha finalizado con un error inesperado. Se ha definido el estado {1} para la tarea.
#XMSG: Task log message for failed task
failedToEnd=No se ha podido definir el estado {0} o no se ha podido quitar el bloqueo.
#XMSG: Task log message
lockNotFound=No se puede finalizar el proceso porque falta el bloqueo: puede que se haya cancelado la tarea.
#XMSG: Task log message failed task
failedOverwrite=La tarea {0} ya ha sido bloqueada por {1}. En consecuencia, se ha producido el siguiente error: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Otra tarea ha asumido el bloqueo de esta tarea.
#XMSG: Task log message failed takeover
failedTakeover=No se ha podido asumir una tarea existente.
#XMSG: Task log message successful takeover
successTakeover=Ha sido necesario liberar el bloqueo izquierdo. Se establece el nuevo bloqueo para esta tarea.
#XMSG: Tasklog Dialog Details
txtDetails=Las sentencias remotas procesadas durante la ejecución se pueden mostrar abriendo el monitor de consultas remotas, en los detalles de los mensajes específicos de la partición.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=Se han eliminado sentencias SQL remotas de la base de datos y no se pueden visualizar.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=No pueden visualizarse las consultas remotas que tienen conexiones asignadas a otros espacios. Vaya al monitor de consultas remotas y utilice el ID de sentencia para filtrarlas.
#XMSG: Task log message for parallel check error
parallelCheckError=No se puede procesar la tarea porque hay otra tarea en ejecución y ya está bloqueándola.
#XMSG: Task log message for parallel running task
parallelTaskRunning=Ya hay una tarea en conflicto en ejecución.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Se ha dado el estado {0} durante la ejecución con el ID de correlación {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=El usuario asignado no tiene las autorizaciones necesarias para ejecutar esta tarea.

#XBUT: Label for open in Editor
openInEditor=Abrir en editor
#XBUT: Label for open in Editor
openInEditorNew=Abrir en el Generador de datos
#XFLD:Run deails label
runDetails=Detalles de ejecución
#XFLD: Label for Logs
Logs=Registros
#XFLD: Label for Settings
Settings=Opciones
#XFLD: Label for Save button
Save=Guardar
#XFLD: Label for Standard
Standard_PO=Optimización del rendimiento (recomendado)
#XFLD: Label for Hana low memory processing
HLMP_MO=Optimización de la memoria
#XFLD: Label for execution mode
ExecutionMode=Modo de ejecución
#XFLD: Label for job execution
jobExecution=Modo de procesamiento
#XFLD: Label for Synchronous
syncExec=Sincrónico
#XFLD: Label for Asynchronous
asyncExec=Asincrónico
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Usar predeterminado (asincrónico, puede cambiar en el futuro)
#XMSG: Save settings success
saveSettingsSuccess=El modo de ejecución SAP HANA se ha modificado correctamente.
#XMSG: Save settings failure
saveSettingsFailed=No se ha podido modificar el modo de ejecución SAP HANA.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Se ha modificado la ejecución de tarea.
#XMSG: Job Execution change failed
jobExecSettingFailed=No se ha podido modificar la ejecución de tarea.
#XMSG: Text for Type
typeTxt=Tipo
#XMSG: Text for Monitor
monitorTxt=Monitor
#XMSG: Text for activity
activityTxt=Actividad
#XMSG: Text for metrics
metricsTxt=Métrica
#XTXT: Text for Task chain key
TASK_CHAINS=Cadena de tareas
#XTXT: Text for View Key
VIEWS=Vista
#XTXT: Text for remote table key
REMOTE_TABLES=Tabla remota
#XTXT: Text for Space key
SPACE=Espacio
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Nodo de computación elástico
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Flujo de replicación
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Búsqueda inteligente
#XTXT: Text for Local Table
LOCAL_TABLE=Tabla local
#XTXT: Text for Data flow key
DATA_FLOWS=Flujo de datos
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=Procedimiento de script SQL
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=Cadena de procesos BW
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Ver en el monitor
#XTXT: Task List header text
taskListHeader=Lista de tareas ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=No se pueden recuperar las métricas de las ejecuciones históricas de un flujo de datos.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=El detalle de la ejecución completa no se está cargando en estos momentos. Intente actualizar.
#XFLD: Label text for the Metrices table header
metricesColLabel=Etiqueta de operador
#XFLD: Label text for the Metrices table header
metricesType=Tipo
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Recuento de registros
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Ejecutar la cadena de tareas
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Se ha iniciado la ejecución de la cadena de tareas.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Se ha iniciado la ejecución de la cadena de tareas de {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=No se ha podido ejecutar la cadena de tareas.
#XTXT: Execute button label
runLabel=Ejecutar
#XTXT: Execute button label
runLabelNew=Iniciar ejecución
#XMSG: Filter Object header
chainsFilteredTableHeader=Filtrado por objeto: {0}
#XFLD: Parent task chain label
parentChainLabel=Cadena de tareas superiores:
#XFLD: Parent task chain unauthorized
Unauthorized=Sin autorización de visualización
#XFLD: Parent task chain label
parentTaskLabel=Tarea superior:
#XTXT: Task status
NOT_TRIGGERED=No desencadenada
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Acceder al modo de pantalla completa
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Salir del modo de pantalla completa
#XTXT: Close Task log details right panel
closeRightColumn=Cerrar sección
#XTXT: Sort Text
sortTxt=Ordenar
#XTXT: Filter Text
filterTxt=Filtrar
#XTXT: Filter by text to show list of filters applied
filterByTxt=Filtrar por
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Más de 5 minutos
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Más de 15 minutos
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Más de 1 hora
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Última hora
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Últimas 24 horas
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Último mes
#XTXT: Messages title text
messagesText=Mensajes

#XTXT Statistics information message
statisticsInfo=No se pueden crear estadísticas para tablas remotas con acceso a datos ''replicados''. Para crear estadísticas, quite los datos replicados en el monitor de detalles de tablas remotas.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Ir al monitor de detalles de tablas remotas

#XTXT: Repair latest failed run label
retryRunLabel=Reintentar última ejecución
#XTXT: Repair failed run label
retryRun=Reintentar ejecución
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Se ha iniciado el reintento de la ejecución de la cadena de tareas.
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=No se ha podido reintentar la ejecución de la cadena de tareas.
#XMSG: Task chain child elements name
taskChainRetryChildObject=Tarea {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Nueva tarea
#XFLD Analyzed View
analyzedView=Vista analizada
#XFLD Metrics
Metrics=Métricas
#XFLD Partition Metrics
PartitionMetrics=Métricas de partición
#XFLD Entities
Messages=Log de tareas
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Aún no se ha definido ninguna partición
#XTXT: Description message for empty partition data
partitionEmptyDescText=Cree particiones especificando criterios para dividir volúmenes de datos más grandes en partes más pequeñas y manejables.

#XTXT: Title Message for empty runs data
runsEmptyTitle=No hay logs disponibles todavía
#XTXT: Description message for empty runs data
runsEmptyDescText=Al iniciar una nueva actividad (cargar una nueva instantánea, iniciar el Analizador de vistas...), verá aquí los logs relacionados.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=La configuración del nodo de cálculo elástico {0} no está actualizada en consecuencia. Compruebe la definición.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Se ha producido un error en el proceso para añadir el nodo de computación elástico {0}.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Se ha empezado a crear y activar la réplica para el objeto fuente "{0}"."{1}" en el nodo de computación elástico {2}.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Se ha empezado a quitar la réplica para el objeto fuente "{0}"."{1}" del nodo de computación elástico {2}.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=No se ha podido crear y activar la réplica del objeto fuente "{0}"."{1}".
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=No se ha podido quitar la réplica para el objeto fuente "{0}"."{1}".
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Se ha iniciado la ruta de la computación de consultas de análisis del espacio {0} al nodo de computación elástico {1}.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Error de enrutamiento de la computación de consultas de análisis del espacio {0} al nodo de computación elástico.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Se ha vuelto a iniciar el enrutamiento de la computación de consultas de análisis del espacio {0} al nodo de computación elástico {1}.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Se ha producido un error al volver a enrutar la computación de consultas de análisis del espacio {0} al coordinador.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Se ha iniciado la cadena de tareas {0} al nodo de computación elástico correspondiente {1}.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=No se ha podido generar la cadena de tareas para el nodo de computación elástico {0}.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=Se ha iniciado el aprovisionamiento del nodo de computación elástico {0} con el siguiente plan de dimensionamiento: vCPUs: {1}; memoria (GiB): {2}; y tamaño de almacenamiento (GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=Ya se ha aprovisionado el nodo de computación elástico {0}.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=Ya se ha desaprovisionado el nodo de computación elástico {0}.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=La operación no está permitida. Detenga el nodo de cálculo elástico {0} y reinícielo para actualizar el plan de dimensionamiento.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Se ha producido un error en el proceso para quitar el nodo de computación elástico {0}.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=Se ha agotado el tiempo de espera para la ejecución actual del nodo de computación elástico {0}.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=Se está comprobando el estado del nodo de computación elástico {0}...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=La generación de la cadena de tareas está bloqueada para el nodo de computación elástico {0}, por lo tanto, es posible que la cadena {1} continúe en ejecución.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=El objeto fuente "{0}"."{1}" se replica como dependencia de la vista "{2}"."{3}".
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=No se puede replicar la tabla "{0}"."{1}", ya que la replicación de la tabla de filas está obsoleta.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Se ha superado la cantidad máxima de nodos de cálculo flexible por cada instancia de SAP HANA Cloud.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=La operación en ejecución para el nodo de computación elástico {0} aún está en curso.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Por problemas técnicos, se ha detenido la eliminación de la réplica de la tabla de origen {0}. Vuelva a intentarlo más tarde.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=Por problemas técnicos, se ha detenido la creación de la réplica de la tabla de origen {0}. Vuelva a intentarlo más tarde.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=No se puede iniciar un nodo de cálculo flexible porque se ha alcanzado el límite de uso o porque todavía no se han asignado horas de bloque de cálculo.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Se está cargando la cadena de tareas y preparando la ejecución de un total de {0} tareas que forman parte de esta cadena.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Ya hay una tarea en conflicto en ejecución
#XMSG: Replication will change
txt_replication_change=Se cambiará el tipo de replicación.
txt_repl_viewdetails=Ver detalles

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Parece que se ha producido un error con la ejecución del último reintento, ya que no se pudo ejecutar la tarea anterior antes de que se hubiese podido generar el plan.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=El espacio "{0}" está bloqueado.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=La actividad {0} requiere el despliegue de la tabla local {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=La actividad {0} requiere que Captura delta esté activado para la tabla local {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=La actividad {0} requiere que la tabla local {1} almacene datos en el almacén de objetos.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=La actividad {0} requiere que la tabla local {1} almacene datos en la base de datos.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Se están empezando a quitar los registros eliminados de la tabla local {0}.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Se están empezando a eliminar todos los registros de la tabla local {0}.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Se están empezando a eliminar registros de la tabla local {0} según la condición del filtro {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Se ha producido un error al quitar los registros eliminados de la tabla local {0}.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Se ha producido un error al eliminar todos los registros de la tabla local {0}.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Se van a eliminar todos los registros completamente procesados con el tipo de modificación "Eliminado", que tienen más de {0} días.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Se van a eliminar todos los registros completamente procesados con el tipo de modificación "Eliminado", que tienen más de {0} .
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS=Se han eliminado {0} registros.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} registros se ha marcado para su eliminación.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=Se han quitado los registros eliminados de la tabla local {0}.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Se han eliminado todos los registros de la tabla local {0}.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Se están empezando a marcar todos los registros como "Eliminados" para la tabla local {0}.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Se están empezando a marcar los registros como "Eliminados" de la tabla local {0} según la condición del filtro {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Se ha completado el marcado de los registros como "Eliminados" para la tabla local {0}.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Las modificaciones de datos se están cargando temporalmente en la tabla {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Las modificaciones de datos se han cargado temporalmente en la tabla {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Las modificaciones de datos se procesan y eliminan de la tabla {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Detalles de la conexión.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Se está empezando a optimizar la tabla local (archivo).
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Se ha producido un error al optimizar la tabla local (archivo).
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Se ha producido un error. Se ha detenido la optimización de la tabla local (archivo).
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Optimizando la tabla local (archivo)...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=La tabla local (archivo) se ha optimizado.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=La tabla local (archivo) está optimizada.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=La tabla está optimizada con las columnas de orden Z: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Se ha producido un error. Se ha detenido la división de la tabla local (archivo).
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Dividiendo la tabla local (archivo)...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=Se ha eliminado la ubicación de la memoria intermedia de entrada de la tabla local (archivo).

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Se está empezando a vaciar (eliminar todos los registros procesados totalmente) la tabla local (archivo).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Se ha producido un error al vaciar la tabla local (archivo).
#XMSG: Task log message
LTF_VACUUM_STOPPED=Se ha producido un error. Se ha detenido el vaciado de la tabla local (archivo).
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Vaciando la tabla local (archivo)...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=El vaciado está completado.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Se van a eliminar todos los registros completamente procesados que tienen más de {0} días.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Se van a eliminar todos los registros completamente procesados que tienen más de {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Se está empezando a combinar nuevos registros con la tabla local (archivo).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Se están empezando a fusionar nuevos registros con la tabla local (archivo) usando la configuración "Fusionar datos automáticamente".
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Se están empezando a fusionar nuevos registros con la tabla local (archivo). Esta tarea se inició mediante la solicitud de fusión de la API.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Se está empezando a combinar nuevos registros con la tabla local (archivo).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Se ha producido un error al combinar nuevos registros con la tabla local (archivo).
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=La tabla local (archivo) está combinada.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Se ha producido un error. Se ha detenido la división de la tabla local (archivo).
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=No se ha podido realizar la fusión de la tabla local (archivo) debido a un error, pero la operación se ha efectuado parcialmente y ya se han fusionado algunos datos.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Se ha producido un error de tiempo de espera. La actividad {0} se ha estado ejecutando durante {1} horas.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=La ejecución asíncrona no se pudo empezar debido a una alta carga del sistema. Abra el "Monitor del sistema" y compruebe las tareas en ejecución.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=La ejecución asíncrona se ha cancelado.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=Ejecución de tarea {0} dentro de los límites de memoria de  {1} y {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=La tarea {0} se ha ejecutado con el ID de recurso {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=Se ha iniciado la búsqueda y sustitución en la tabla local (archivo).
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Ha finalizado la búsqueda y sustitución en la tabla local (archivo).
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Error de búsqueda y sustitución en la tabla local (archivo).

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Se ha producido un error. Se ha detenido la actualización de las estadísticas de la tabla local (archivo).

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=La tarea ha fallado debido a un error de falta de memoria en la base de datos de SAP HANA.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=La tarea ha fallado debido a un rechazo del control de admisión de SAP HANA.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=La tarea ha fallado debido a demasiadas conexiones activas de SAP HANA.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=La operación Reintentar no se ha podido llevar a cabo porque solo se permiten reintentos en el elemento superior de una cadena de tareas anidadas.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Los logs de tareas inferiores que no se han podido realizar ya no están disponibles para el reintento.


####Metrics Labels

performanceOptimized=Optimizado para el rendimiento
memoryOptimized=Optimizado para la memoria

JOB_EXECUTION=Ejecución de tarea
EXECUTION_MODE=Modo de ejecución
NUMBER_OF_RECORDS_OVERALL=Número de registros de forma persistente
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Número de registros leídos de la fuente remota
RUNTIME_MS_REMOTE_EXECUTION_TIME=Tiempo de procesamiento de fuente remota
MEMORY_CONSUMPTION_GIB=Memoria máxima de SAP HANA
NUMBER_OF_PARTITIONS=Número de particiones
MEMORY_CONSUMPTION_GIB_OVERALL=Memoria máxima de SAP HANA
NUMBER_OF_PARTITIONS_LOCKED=Número de particiones bloqueadas
PARTITIONING_COLUMN=Columna de partición
HANA_PEAK_CPU_TIME=Tiempo total de CPU de SAP HANA
USED_IN_DISK=Almacenamiento utilizado
INPUT_PARAMETER_PARAMETER_VALUE=Parámetro de entrada
INPUT_PARAMETER=Parámetro de entrada
ECN_ID=Nombre de nodo de cálculo flexible

DAC=Controles de acceso a datos
YES=Sí
NO=No
noofrecords=Número de registros
partitionpeakmemory=Memoria máxima de SAP HANA
value=Valor
metricsTitle=Métricas ({0})
partitionmetricsTitle=Particiones ({0})
partitionLabel=Partición
OthersNotNull=Valores no incluidos en los rangos
OthersNull=Valores nulos
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Opciones utilizadas para la última ejecución de persistencia de datos:
#XMSG: Message for input parameter name
inputParameterLabel=Parámetro de entrada
#XMSG: Message for input parameter value
inputParameterValueLabel=Valor
#XMSG: Message for persisted data
inputParameterPersistedLabel=Hora de persistencia
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Eliminar datos
REMOVE_DELETED_RECORDS=Quitar registros eliminados
MERGE_FILES=Fusionar archivos
OPTIMIZE_FILES=Optimizar archivos
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Ver en el monitor de puente de SAP BW

ANALYZE_PERFORMANCE=Analizar rendimiento
CANCEL_ANALYZE_PERFORMANCE=Cancelar Analizar rendimiento

#XFLD: Label for frequency column
everyLabel=Cada
#XFLD: Plural Recurrence text for Hour
hoursLabel=horas
#XFLD: Plural Recurrence text for Day
daysLabel=días
#XFLD: Plural Recurrence text for Month
monthsLabel=meses
#XFLD: Plural Recurrence text for Minutes
minutesLabel=minutos

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Guía de resolución de problemas de persistencias de vistas</a>
#XTXT TEXT for view persistency guide link
OOMMessage=El proceso se ha quedado sin memoria. No se pueden persistir los datos de la vista "{0}". Consulte el Portal de ayuda para obtener más información sobre los errores de memoria insuficiente. También puede consultar "Analizador de vistas" para analizar la vista en cuanto a la complejidad del consumo de memoria.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=No aplicable
OPEN_BRACKET=(
CLOSE_BRACKET=)
