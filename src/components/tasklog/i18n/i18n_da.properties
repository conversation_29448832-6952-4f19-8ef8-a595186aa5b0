
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Logdetaljer
#XFLD: Header
TASK_LOGS=Opgavelogge ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Kørsler ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Vis detaljer
#XFLD: Button text
STOP=Stop kørsel
#XFLD: Label text
RUN_START=Start for seneste kørsel
#XFLD: Label text
RUN_END=Slut for seneste kørsel
#XFLD: Label text
RUNTIME=Varighed
#XTIT: Count for Messages
txtDetailMessages=Meddelelser ({0})
#XFLD: Label text
TIME=Tidsstempel
#XFLD: Label text
MESSAGE=Meddelelse
#XFLD: Label text
TASK_STATUS=Kategori
#XFLD: Label text
TASK_ACTIVITY=Aktivitet
#XFLD: Label text
RUN_START_DETAILS=Start
#XFLD: Label text
RUN_END_DETAILS=Slut
#XFLD: Label text
LOGS=Kørsler
#XFLD: Label text
STATUS=Status
#XFLD: Label text
RUN_STATUS=Kørselsstatus
#XFLD: Label text
Runtime=Varighed
#XFLD: Label text
RuntimeTooltip=Varighed (tt : mm : ss)
#XFLD: Label text
TRIGGEREDBY=Udløst af
#XFLD: Label text
TRIGGEREDBYNew=Kørt af
#XFLD: Label text
TRIGGEREDBYNewImp=Kørsel startet af
#XFLD: Label text
EXECUTIONTYPE=Udførelsestype
#XFLD: Label text
EXECUTIONTYPENew=Kørselstype
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Space for overordnet kæde
#XFLD: Refresh tooltip
TEXT_REFRESH=Opdater
#XFLD: view Details link
VIEW_ERROR_DETAILS=Vis detaljer
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Download yderligere detaljer
#XMSG: Download completed
downloadStarted=Download startet
#XMSG: Error while downloading content
errorInDownload=Der opstod en fejl under download.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Vis detaljer
#XBTN: cancel button of task details dialog
TXT_CANCEL=Annuller
#XBTN: back button from task details
TXT_BACK=Tilbage
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Opgave afsluttet
#XFLD: Log message with failed status
MSG_LOG_FAILED=Opgave mislykkedes
#XFLD: Master and detail table with no data
No_Data=Ingen data
#XFLD: Retry tooltip
TEXT_RETRY=Forsøg igen
#XFLD: Cancel Run label
TEXT_CancelRun=Annuller kørsel
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Oprydning mislykkedes ved indlæsning
#XMSG:button copy sql statement
txtSQLStatement=Kopier SQL-sætning
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Åbn monitor for eksterne forespørgsler
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=Klik på "Åbn monitor for eksterne forespørgsler" for at vise de eksterne SQL-sætninger.
#XMSG:button ok
txtOk=OK
#XMSG: button close
txtClose=Luk
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=Handlingen Annuller kørsel for objekt "{0}" er startet.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=Handlingen Annuller kørsel for objekt "{0}" kan ikke udføres.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=Handlingen Annuller kørsel for objekt "{0}" er ikke længere mulig, fordi replikeringsstatus har ændret sig.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Ingen opgavelogge har statussen Kører.
#XMSG: message for conflicting task
Task_Already_Running=En opgave med konflikt kører allerede for objektet "{0}".
#XFLD: Label for no task log with running state title
actionInfo=Info om handling
#XMSG Copied to clipboard
copiedToClip=Kopieret til udklipsholder
#XFLD copy
Copy=Kopier
#XFLD copy correlation ID
CopyCorrelationID=Kopier korrelations-id
#XFLD Close
Close=Luk
#XFLD: show more Label
txtShowMore=Vis mere
#XFLD: message Label
messageLabel=Meddelelse:
#XFLD: details Label
detailsLabel=Detaljer:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Udfører SQL-\r\nsætning:
#XFLD:statementId Label
statementIdLabel=Sætnings-id:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Antal eksterne \r\n SQL-sætninger:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Antal sætninger
#XFLD: Space Label
txtSpaces=Space
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Ikke-godkendte spaces
#XFLD: Privilege Error Text
txtPrivilegeError=Du har ikke tilstrækkelige rettigheder til at vise disse data.
#XFLD: Label for Object Header
DATA_ACCESS=Dataadgang
#XFLD: Label for Object Header
SCHEDULE=Tidsplan
#XFLD: Label for Object Header
DETAILS=Detaljer
#XFLD: Label for Object Header
LATEST_UPDATE=Sidste opdatering
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Seneste ændring (kilde)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Opdateringsfrekvens
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Planlagt frekvens
#XFLD: Label for Object Header
NEXT_RUN=Næste kørsel
#XFLD: Label for Object Header
CONNECTION=Forbindelse
#XFLD: Label for Object Header
DP_AGENT=DP-agent
#XFLD: Label for Object Header
USED_IN_MEMORY=Hukommelse brugt til lager (MiB)
#XFLD: Label for Object Header
USED_DISK=Disk brugt til lager (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Størrelse in-memory (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Størrelse på disk (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Antal poster

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Indstillet til Mislykkedes
SET_TO_FAILED_ERR=Denne opgave blev kørt, men brugeren har indstillet denne opgaves status til MISLYKKEDES.
#XFLD: Label for stopped failed
FAILLOCKED=Kørsel er allerede i gang
#XFLD: sub status STOPPED
STOPPED=Stoppet
STOPPED_ERR=Denne opgave blev stoppet, men der blev ikke udført nogen tilbagerulning
#XFLD: sub status CANCELLED
CANCELLED=Annulleret
CANCELLED_ERR=Denne opgavekørsel blev annulleret, efter den blev startet. I dette tilfælde blev dataene rullet tilbage og gendannet til den tilstand, som fandtes, før opgavekørslen først blev udløst.
#XFLD: sub status LOCKED
LOCKED=Låst
LOCKED_ERR=Den samme opgave kørte allerede, så dette opgavejob kan ikke køres parallelt med en eksisterende opgaves udførelse.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Undtagelse for opgave
TASK_EXCEPTION_ERR=Denne opgave stødte på en ikke-angivet fejl under udførelsen.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Undtagelse for udførelse af opgave
TASK_EXECUTE_EXCEPTION_ERR=Denne opgave stødte på en fejl under udførelsen.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Ikke-autoriseret
UNAUTHORIZED_ERR=Brugeren kunne ikke autentificeres, er låst eller slettet.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Forbudt
FORBIDDEN_ERR=Den tildelte bruger har ikke de nødvendige rettigheder til at udføre denne opgave.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Ikke udløst
FAIL_NOT_TRIGGERED_ERR=Dette opgavejob kunne ikke udføres pga. en systemafbrydelse eller fordi, en del af databasesystemet ikke var tilgængelig på tidspunktet for den planlagte udførelse. Vent på det næste udførelsestidspunkt for det planlagte job, eller planlæg jobbet igen.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Tidsplan annulleret
SCHEDULE_CANCELLED_ERR=Dette opgavejob kunne ikke udføres pga. en intern fejl. Kontakt SAP-support, og giv dem korrelations-id og tidsstempel fra dette opgavejobs oplysninger om logdetaljer.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Tidligere kørsel i gang
SUCCESS_SKIPPED_ERR=Denne opgaves udførelse er ikke udløst, da en tidligere kørsel af den samme opgave stadig er under udførelse.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Ejer mangler
FAIL_OWNER_MISSING_ERR=Dette opgavejob kunne ikke udføres, fordi det ikke har en tildelt systembruger. Tildel en ejerbruger til jobbet.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Samtykke ikke tilgængeligt
FAIL_CONSENT_NOT_AVAILABLE_ERR=Du har ikke givet SAP autorisation til at køre opgavekæder eller til at planlægge dataintegrationsopgaver på dine vegne. Vælg den angivne valgmulighed for at give dit samtykke.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Samtykke udløbet
FAIL_CONSENT_EXPIRED_ERR=Den autorisation, der tillader SAP at køre opgavekæder eller planlægge dataintegrationsopgaver på dine vegne er udløbet. Vælg den angivne valgmulighed for at forny dit samtykke.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Samtykke gjort ugyldigt
FAIL_CONSENT_INVALIDATED_ERR=Denne opgave kunne ikke udføres, typisk pga. en ændring i tenantens konfiguration af identitetsudbyder. I tilfælde heraf kan ingen nye opgavejobs køres eller planlægges i den berørte brugers navn. Hvis den tildelte bruger stadig findes i den nye identitetsudbyder, skal du tilbagekalde samtykket til planlægning og derefter give det igen. Hvis den tildelte bruger ikke længere findes, skal du tildele en ny opgavejobejer og give det nødvendige samtykke til opgaveplanlægning. Se følgende SAP-note: https://launchpad.support.sap.com/#/notes/3089828 for at få yderligere oplysninger.
TASK_EXECUTOR_ERROR=Opgaveeksekvering
TASK_EXECUTOR_ERROR_ERR=Denne opgave stødte på en intern fejl, højst sandsynligt under forberedelsestrin til udførelse, og opgaven kunne ikke startes.
PREREQ_NOT_MET=Forudsætning ikke mødt
PREREQ_NOT_MET_ERR=Denne opgave kunne ikke køres pga. problemer i dens definition. F.eks. at objektet ikke er implementeret, en opgavekæde indeholder cirkulær logik, eller en visnings SQL er ugyldig.
RESOURCE_LIMIT_ERROR=Ressourcegrænsefejl
RESOURCE_LIMIT_ERROR_ERR=Kan p.t. ikke udføre opgaven, fordi der ikke var tilstrækkeligt tilgængelige ressourcer, eller de var optagede.
FAIL_CONSENT_REFUSED_BY_UMS=Samtykke afvist
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Denne opgave kunne ikke udføres i planlagte kørsler eller opgavekæder på grund af en ændring i en brugers Identity Provider-konfiguration på tenanten. Se følgende SAP-note for at få flere oplysninger: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Planlagt
#XFLD: status text
SCHEDULEDNew=Permanent
#XFLD: status text
PAUSED=Sat på pause
#XFLD: status text
DIRECT=Direkte
#XFLD: status text
MANUAL=Manuel
#XFLD: status text
DIRECTNew=Simpel
#XFLD: status text
COMPLETED=Fuldført
#XFLD: status text
FAILED=Mislykkedes
#XFLD: status text
RUNNING=Kører
#XFLD: status text
none=Ingen
#XFLD: status text
realtime=Realtid
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Underopgave
#XFLD: New Data available in the file
NEW_DATA=Nye data

#XFLD: text for values shown in column Replication Status
txtOff=Fra
#XFLD: text for values shown in column Replication Status
txtInitializing=Initialiserer
#XFLD: text for values shown in column Replication Status
txtLoading=Indlæser
#XFLD: text for values shown in column Replication Status
txtActive=Aktiv
#XFLD: text for values shown in column Replication Status
txtAvailable=Tilgængelig
#XFLD: text for values shown in column Replication Status
txtError=Fejl
#XFLD: text for values shown in column Replication Status
txtPaused=Sat på pause
#XFLD: text for values shown in column Replication Status
txtDisconnected=Forbindelse afbrudt
#XFLD: text for partially Persisted views
partiallyPersisted=Delvist persisteret

#XFLD: activity text
REPLICATE=Repliker
#XFLD: activity text
REMOVE_REPLICATED_DATA=Fjern replikerede data
#XFLD: activity text
DISABLE_REALTIME=Deaktiver datareplikering i realtid
#XFLD: activity text
REMOVE_PERSISTED_DATA=Fjern persisterede data
#XFLD: activity text
PERSIST=Persister
#XFLD: activity text
EXECUTE=Udfør
#XFLD: activity text
TASKLOG_CLEANUP=Oprydning_af_opgavelog
#XFLD: activity text
CANCEL_REPLICATION=Annuller replikering
#XFLD: activity text
MODEL_IMPORT=Modelimport
#XFLD: activity text
NONE=Ingen
#XFLD: activity text
CANCEL_PERSISTENCY=Annuller persistens
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Analyser visning
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Annuller visningsanalyse

#XFLD: severity text
INFORMATION=Oplysninger
#XFLD: severity text
SUCCESS=Resultat
#XFLD: severity text
WARNING=Advarsel
#XFLD: severity text
ERROR=Fejl
#XFLD: text for values shown for Ascending sort order
SortInAsc=Sorter stigende
#XFLD: text for values shown for Descending sort order
SortInDesc=Sorter faldende
#XFLD: filter text for task log columns
Filter=Filtrér
#XFLD: object text for task log columns
Object=Objekt
#XFLD: space text for task log columns
crossSpace=Space

#XBUT: label for remote data access
REMOTE=Ekstern
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Replikeret (realtid)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Replikeret (snapshot)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=Realtidsreplikeringen er blokeret på grund af en fejl. Når fejlen er afhjulpet, kan du bruge handlingen "Forsøg igen" til at fortsætte med realtidsreplikering.
ERROR_MSG=Realtidsreplikeringen er blokeret på grund af en fejl.
RETRY_FAILED_ERROR=Nyt forsøg mislykkedes med en fejl.
LOG_INFO_DETAILS=Der genereres ikke logge, når data replikeres i realtidstilstand. De viste logge er relateret til tidligere handlinger.

#XBUT: Partitioning label
partitionMenuText=Partitionering
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Opret partition
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Rediger partition
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Slet partition
#XFLD: Initial text
InitialPartitionText=Definer partitioner ved at angive kriterier for opdeling af store datasæt til mindre datasæt.
DefinePartition=Definer partitioner
#XFLD: Message text
partitionChangedInfo=Partitionsdefinitionen er ændret siden den seneste replikering. Ændringerne anvendes til næste dataload.
#XFLD: Message text
REAL_TIME_WARNING=Partitionering anvendes kun ved indlæsning af et nyt snapshot. Partitionering anvendes ikke til realtidsreplikering.
#XFLD: Message text
loadSelectedPartitions=Begyndte at persistere data for de valgte partitioner af "{0}"
#XFLD: Message text
loadSelectedPartitionsError=Data kunne ikke persisteres for de valgte partitioner af "{0}"
#XFLD: Message text
viewpartitionChangedInfo=Partitionsdefinitionen er ændret siden den sidste persistenskørsel. For at anvende ændringerne vil den næste dataindlæsning være et fuldt snapshot, herunder låste partitioner. Når denne fulde indlæsning er færdig, kan du køre data for enkelte partitioner.
#XFLD: Message text
viewpartitionChangedInfoLocked=Partitionsdefinitionen er ændret siden den sidste persistenskørsel. For at anvende ændringerne vil den næste dataindlæsning være et fuldt snapshot, undtagen låste og uændrede partitionsintervaller. Når denne indlæsning er færdig, kan du indlæse valgte partitioner igen.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Tabelreplikering
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Planlæg replikering
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Opret tidsplan
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Rediger tidsplan
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Slet tidsplan
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Indlæs nyt snapshot
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Start datareplikering
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Fjern replikerede data
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Aktiver adgang i realtid
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Aktiver datareplikering i realtid
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Deaktiver datareplikering i realtid
#XFLD: Message for replicate table action
replicateTableText=Tabelreplikering
#XFLD: Message for replicate table action
replicateTableTextNew=Datareplikering
#XFLD: Message to schedule task
scheduleText=Planlæg
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Visningspersistens
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Datapersistens
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Indlæs nyt snapshot
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Start datapersistens
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Fjern persisterede data
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Partitioneret behandling
#XBUT: Label for scheduled replication
scheduledTxt=Planlagt
#XBUT: Label for statistics button
statisticsTxt=Statistik
#XBUT: Label for create statistics
createStatsTxt=Opret statistik
#XBUT: Label for edit statistics
editStatsTxt=Rediger statistik
#XBUT: Label for refresh statistics
refreshStatsTxt=Opdater statistik
#XBUT: Label for delete statistics
dropStatsTxt=Slet statistik
#XMSG: Create statistics success message
statsSuccessTxt=Startede oprettelse af statistik af type {0} for {1}.
#XMSG: Edit statistics success message
statsEditSuccessTxt=Startede ændring af statistiktype til {0} for {1}.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Startede opdatering af statistik for {0}.
#XMSG: Drop statistics success message
statsDropSuccessTxt=Statistik slettet for {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=Fejl under oprettelse af statistik
#XMSG: Edit statistics error message
statsEditErrorTxt=Fejl under ændring af statistik
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Fejl under opdatering af statistik
#XMSG: Drop statistics error message
statsDropErrorTxt=Fejl under sletning af statistik
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Er du sikker på, at du vil fjerne datastatistikker?
startPersistencyAdvisorLabel=Start visningsanalyse

#Partition related texts
#XFLD: Label for Column
column=Kolonne
#XFLD: Label for No of Partition
noOfPartitions=Antal partitioner
#XFLD: Label for Column
noOfParallelProcess=Antal parallelle processer
#XFLD: Label text
noOfLockedPartition=Antal låste partitioner
#XFLD: Label for Partition
PARTITION=Partitioner
#XFLD: Label for Column
AVAILABLE=Tilgængelig
#XFLD: Statistics Label
statsLabel=Statistik
#XFLD: Label text
COLUMN=Kolonne:
#XFLD: Label text
PARALLEL_PROCESSES=Parallelle processer:
#XFLD: Label text
Partition_Range=Partitionsinterval
#XFLD: Label text
Name=Navn
#XFLD: Label text
Locked=Spærret
#XFLD: Label text
Others=ØVRIGE
#XFLD: Label text
Delete=Slet
#XFLD: Label text
LoadData=Indlæs valgte partitioner
#XFLD: Label text
LoadSelectedData=Indlæs valgte partitioner
#XFLD: Confirmation text
LoadNewPersistenceConfirm=Dette indlæser et nyt snapshot for alle oplåste og ændrede partitioner – ikke blot dem, du har valgt. Vil du fortsætte?
#XFLD: Label text
Continue=Fortsæt

#XFLD: Label text
PARTITIONS=Partitioner
#XFLD: Label text
ADD_PARTITIONS=+ tilføj partition
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Tilføj partition
#XFLD: Label text
deleteRange=Slet partition
#XFLD: Label text
LOW_PLACE_HOLDER=Indtast lav værdi
#XFLD: Label text
HIGH_PLACE_HOLDER=Indtast høj værdi
#XFLD: tooltip text
lockedTooltip=Spær partition efter første indlæsning

#XFLD: Button text
Edit=Rediger
#XFLD: Button text
CANCEL=Annuller

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Seneste opdatering af statistik
#XFLD: Statistics Fields
STATISTICS=Statistik

#XFLD:Retry label
TEXT_Retry=Forsøg igen
#XFLD:Retry label
TEXT_Retry_tooltip=Forsøg igen med realtidsreplikering, når fejlen er løst.
#XFLD: text retry
Retry=Bekræft
#XMG: Retry confirmation text
retryConfirmationTxt=Den sidste realtidsreplikering blev afbrudt med en fejl.\n Bekræft, at fejlen er afhjulpet, og at realtidsreplikering kan startes igen.
#XMG: Retry success text
retrySuccess=Nyt forsøg igangsat uden fejl.
#XMG: Retry fail text
retryFail=Nyt forsøg mislykkedes.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Opret statistik
#XMSG: activity message for edit statistics
DROP_STATISTICS=Slet statistik
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Opdater statistik
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Rediger statistik
#XMSG: Task log message started task
taskStarted=Opgaven {0} er startet.
#XMSG: Task log message for finished task
taskFinished=Opgaven {0} sluttede med status {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=Opgave {0} sluttede kl. {2} med status {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=Opgave {0} har inputparametre.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=Opgaven {0} sluttede med en uventet fejl. Opgavens status er angivet til {1}.
#XMSG: Task log message for failed task
failedToEnd=Kunne ikke indstille status til {0} eller kunne ikke fjerne låsen.
#XMSG: Task log message
lockNotFound=Vi kan ikke fuldføre processen, da låsen mangler: Opgaven kan være annulleret.
#XMSG: Task log message failed task
failedOverwrite=Opgaven {0} er allerede låst af {1}. Den mislykkedes derfor med følgende fejl: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Spærring af denne opgave blev overtaget af en anden opgave.
#XMSG: Task log message failed takeover
failedTakeover=Kunne ikke overtage en eksisterende opgave.
#XMSG: Task log message successful takeover
successTakeover=Den resterende spærre måtte frigives. Den nye spærre for denne opgave er angivet.
#XMSG: Tasklog Dialog Details
txtDetails=De under kørslen behandlede eksterne sætninger kan vises ved at åbne monitoren for eksterne forespørgsler i detaljerne for de partitionsspecifikke meddelelser.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=Eksterne SQL-sætninger er slettet fra databasen og kan ikke vises.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Eksterne queries, der har forbindelser tildelt til andre spaces kan ikke vises. Gå til monitoren for eksterne forespørgsler, og brug sætnings-id'et til at filtrere dem.
#XMSG: Task log message for parallel check error
parallelCheckError=Opgaven kan ikke behandles, fordi en anden opgave kører og spærrer allerede for opgaven.
#XMSG: Task log message for parallel running task
parallelTaskRunning=En opgave med konflikt kører allerede.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} under kørsel med korrelations-id {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=Den tildelte bruger har ikke de nødvendige rettigheder til at udføre denne opgave.

#XBUT: Label for open in Editor
openInEditor=Åbn i editor
#XBUT: Label for open in Editor
openInEditorNew=Åbn i datagenerator
#XFLD:Run deails label
runDetails=Kørselsdetaljer
#XFLD: Label for Logs
Logs=Logge
#XFLD: Label for Settings
Settings=Indstillinger
#XFLD: Label for Save button
Save=Gem
#XFLD: Label for Standard
Standard_PO=Optimeret til ydeevne (anbefalet)
#XFLD: Label for Hana low memory processing
HLMP_MO=Optimeret til hukommelse
#XFLD: Label for execution mode
ExecutionMode=Kørselstilstand
#XFLD: Label for job execution
jobExecution=Behandlingstilstand
#XFLD: Label for Synchronous
syncExec=Synkron
#XFLD: Label for Asynchronous
asyncExec=Asynkron
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Anvend standard (asynkron, kan ændres i fremtiden)
#XMSG: Save settings success
saveSettingsSuccess=SAP HANA-udførelsestilstand er ændret.
#XMSG: Save settings failure
saveSettingsFailed=Ændring af SAP HANA-udførelsestilstand mislykkedes.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Jobudførelse er ændret.
#XMSG: Job Execution change failed
jobExecSettingFailed=Ændring af jobudførelse mislykkedes.
#XMSG: Text for Type
typeTxt=Type
#XMSG: Text for Monitor
monitorTxt=Overvåg
#XMSG: Text for activity
activityTxt=Aktivitet
#XMSG: Text for metrics
metricsTxt=Metrikker
#XTXT: Text for Task chain key
TASK_CHAINS=Opgavekæde
#XTXT: Text for View Key
VIEWS=Visning
#XTXT: Text for remote table key
REMOTE_TABLES=Ekstern tabel
#XTXT: Text for Space key
SPACE=Space
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Elastisk beregningsnode
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Replikeringsflow
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Intelligent opslag
#XTXT: Text for Local Table
LOCAL_TABLE=Lokal tabel
#XTXT: Text for Data flow key
DATA_FLOWS=Dataflow
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=SQL Script-procedure
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=BW-proceskæde
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Vis i monitor
#XTXT: Task List header text
taskListHeader=Opgaveliste ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Metrikker for historiske kørsler af et dataflow kan ikke hentes.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=Fuldstændige kørselsdetaljer indlæses ikke i øjeblikket. Prøv at opdatere.
#XFLD: Label text for the Metrices table header
metricesColLabel=Operatoretiket
#XFLD: Label text for the Metrices table header
metricesType=Type
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Antal poster
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Kør opgavekæden
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Opgavekæden er startet.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Kørslen af opgavekæden er startet for {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Kørsel af opgavekæden mislykkedes.
#XTXT: Execute button label
runLabel=Kør
#XTXT: Execute button label
runLabelNew=Start kørsel
#XMSG: Filter Object header
chainsFilteredTableHeader=Filtreret efter objekt: {0}
#XFLD: Parent task chain label
parentChainLabel=Overordnet opgavekæde:
#XFLD: Parent task chain unauthorized
Unauthorized=Ikke autoriseret til at vise
#XFLD: Parent task chain label
parentTaskLabel=Overordnet opgave:
#XTXT: Task status
NOT_TRIGGERED=Ikke udløst
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Start fuldskærmstilstand
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Afslut fuldskærmstilstand
#XTXT: Close Task log details right panel
closeRightColumn=Luk afsnit
#XTXT: Sort Text
sortTxt=Sortér
#XTXT: Filter Text
filterTxt=Filtrer
#XTXT: Filter by text to show list of filters applied
filterByTxt=Filtrer efter
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Mere end 5 minutter
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Mere end 15 minutter
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Mere end 1 time
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Sidste time
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Sidste 24 timer
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Sidste måned
#XTXT: Messages title text
messagesText=Meddelelser

#XTXT Statistics information message
statisticsInfo=Der kan ikke oprettes statistik for eksterne tabeller med dataadgang "Replikeret". Der oprettes statistik ved at fjerne de replikerede data i Monitor for detaljer om eksterne tabeller.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Gå til Monitor for detaljer om ekstern tabel

#XTXT: Repair latest failed run label
retryRunLabel=Forsøg seneste kørsel igen
#XTXT: Repair failed run label
retryRun=Forsøg kørsel igen
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Opgavekæden med forsøg kørsel er startet
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Opgavekæden med forsøg kørsel er mislykket
#XMSG: Task chain child elements name
taskChainRetryChildObject=Opgave {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Ny opgave
#XFLD Analyzed View
analyzedView=Analyseret visning
#XFLD Metrics
Metrics=Metrikker
#XFLD Partition Metrics
PartitionMetrics=Partitionsmetrikker
#XFLD Entities
Messages=Opgavelog
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Der er endnu ikke defineret partitioner
#XTXT: Description message for empty partition data
partitionEmptyDescText=Opret partitioner ved at specificere kriterier for at opdele store datamængder i mindre dele, der er nemmere at håndtere.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Ingen logfiler er tilgængelige endnu
#XTXT: Description message for empty runs data
runsEmptyDescText=Når du starter en ny aktivitet (indlæser et nyt snapshot, starter en visningsanalyse...), vises relaterede logfiler her.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=Konfigurationen for den elastiske beregningsnode {0} er ikke vedligeholdt tilsvarende. Kontroller din definition.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Kunne ikke gennemføre processen for tilføjelse af den elastiske beregningsnode {0}.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Oprettelse og aktivering af replikat for kildeobjektet "{0}"."{1}" i den elastiske beregningsnode {2} er startet.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Fjernelse af replikat for kildeobjektet "{0}"."{1}" fra den elastiske beregningsnode {2} er startet.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Kunne ikke oprette og aktivere replikatet for kildeobjektet "{0}"."{1}".
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Kunne ikke fjerne replikatet for kildeobjektet "{0}"."{1}".
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Routing af beregning af analytiske forespørgsler for spacet {0} til den elastiske beregningsnode {1} er startet.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Kunne ikke route beregning af analytiske forespørgsler for spacet {0} til den tilsvarende elastiske beregningsnode.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Omdirigering af beregning af analytiske forespørgsler for spacet {0} tilbage fra den elastiske beregningsnode {1} er startet.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Kunne ikke omdirigere beregning af analytiske forespørgsler for spacet {0} tilbage til koordinatoren.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Opgavekæden {0} til den tilsvarende elastiske beregningsnode {1} blev udløst.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Kunne ikke generere opgavekæde for den elastiske beregningsnode {0}.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=Provisioneringen af den elastiske beregningsnode {0} er startet med dimensioneringsplanen: vCPU''er: {1}, hukommelse (GiB): {2} og lagerstørrelse (GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=Den elastiske beregningsnode {0} er allerede provisioneret.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=Den elastiske beregningsnode {0} er allerede deprovisioneret.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=Operationen er ikke tilladt. Stop den elastiske beregningsnode {0}, og genstart den for at opdatere dimensioneringsplanen.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Kunne ikke fjerne den elastiske beregningsnode {0}.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=Den aktuelle kørsel af den elastiske beregningsnode {0} gik i timeout.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=Kontrol af status for den elastiske beregningsnode {0} udføres...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=Generering af opgavekæde for den elastiske beregningsnode {0} er låst, og derfor kører kæden {1} muligvis stadig.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=Kildeobjektet "{0}"."{1}" er replikeret som afhængighed af visningen "{2}"."{3}"
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Kunne ikke replikere tabellen "{0}"."{1}", da replikering af rækketabel er udfaset.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Maksimum elastisk beregningsnode pr. SAP HANA Cloud-instans er overskredet.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=Operationen for den elastiske beregningsnode {0} kører stadig.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Grundet tekniske problemer er sletningen af replikaen for kildetabellen {0} er stoppet. Prøv igen senere.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=Grundet tekniske problemer er oprettelsen af replikaen for kildetabellen {0} er stoppet. Prøv igen senere.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Kan ikke starte en elastisk beregningsnode, da grænsen for anvendelse er nået, eller da der endnu ikke er tildelt beregningsbloktimer.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Indlæsning af opgavekæde og forberedelse til kørsel af i alt {0} opgaver, der er del af denne kæde.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=En modstridende opgave kører allerede
#XMSG: Replication will change
txt_replication_change=Replikeringstype ændres.
txt_repl_viewdetails=Vis detaljer

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Det ser ud til, at der var en fejl med at forsøge den seneste kørsel igen, da den tidligere opgavekørsel mislykkedes, før planen kunne genereres.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=Spacet "{0}" er låst.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=Aktiviteten {0} kræver, at den lokale tabel {1} implementeres.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=Aktiviteten {0} kræver, at deltaregistrering er slået til for den lokale tabel {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=Aktiviteten {0} kræver, at den lokale tabel {1} skal lagre data i Object Store.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=Aktiviteten {0} kræver, at den lokale tabel {1} skal lagre data i databasen.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Begynder at fjerne slettede poster for den lokale tabel {0}.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Begynder at slette alle poster for den lokale tabel {0}.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Begynder at slette poster for den lokale tabel {0} i henhold til filterbetingelsen {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Der opstod en fejl under fjernelse af slettede poster for den lokale tabel {0}.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Der opstod en fejl under sletning af alle poster for den lokale tabel {0}.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Sletter alle fuldt behandlede poster med ændringstype "Slettet", der er ældre end {0} dage.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Sletter alle fuldt behandlede poster med ændringstype "Slettet", der er ældre end {0}.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} poster er slettet.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} poster er markeret til sletning.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=Fjernelse af slettede poster for den lokale tabel {0} er fuldført.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Sletning af alle poster for den lokale tabel {0} er fuldført.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Begynder at markere poster som "slettet" for den lokale tabel {0}.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Begynder at markere poster som "Slettet" for den lokale tabel {0} i henhold til filterbetingelsen {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Markering af poster som "slettet" for den lokale tabel {0} er udført.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Dataændringer indlæses midlertidigt i tabellen {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Dataændringer indlæses midlertidigt i tabellen {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Dataændringer behandles og slettes fra tabellen {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Forbindelsesdetaljer.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Starter optimering af lokal tabel (fil).
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Der opstod en fejl ved optimering af lokal tabel (fil).
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Der opstod en fejl. Optimering af lokal tabel (fil) er stoppet.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Optimerer lokal tabel (fil)...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=Lokal tabel (fil) er optimeret.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=Lokal tabel (fil) er optimeret.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=Tabellen er optimeret med kolonnerne med z-rækkefølge: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Der opstod en fejl. Afkortning af lokal tabel (fil) er stoppet.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Afkorter lokal tabel (fil)...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=Indgående bufferplacering for lokal tabel (fil) er droppet.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Starter støvsugning (slet alle fuldt behandlede poster) af lokal tabel (fil).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Der opstod en fejl ved støvsugning af lokal tabel (fil).
#XMSG: Task log message
LTF_VACUUM_STOPPED=Der opstod en fejl. Støvsugning af lokal tabel (fil) er stoppet.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Støvsuger lokal tabel (fil)...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=Støvsugning er udført.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Sletter alle fuldt behandlede poster, der er ældre end {0} dage.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Sletter alle fuldt behandlede poster, der er ældre end {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Starter fletning af nye poster med lokal tabel (fil).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Starter fletning af nye poster med lokal tabel (fil) ved hjælp af indstillingen "Flet data automatisk".
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Starter fletning af nye dataposter med lokal tabel (fil). Denne opgave blev indledt via API-fletteanmodningen.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Fletter nye poster med lokal tabel (fil).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Der opstod en fejl ved fletning af nye poster med lokal tabel (fil).
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=Lokal tabel (fil) er flettet.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Der opstod en fejl. Fletning af lokal tabel (fil) er stoppet.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=Fletning af lokal tabel (fil) er mislykket på grund af en fejl, men handlingen blev delvist udført, og nogle data er allerede flettet.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Der opstod en timeout-fejl. Aktiviteten {0} har kørt i {1} timer.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=Den asynkrone udførelse kunne ikke starte på grund af en høj systembelastning. Åbn "Systemmonitor", og kontroller de aktive opgaver.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=Den asynkrone udførelse er annulleret.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION={0}-opgaven kørte inden for hukommelsesgrænserne {1} og {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID={0}-opgaven kørte med ressource-id''et {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=Find og erstat er startet i lokal tabel (fil).
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Find og erstat blev udført i lokal tabel (fil).
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Find og erstat blev ikke udført i lokal tabel (fil).

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Der opstod en fejl. Opdatering af statistikker for lokal tabel (fil) er stoppet.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=Opgaven blev ikke udført på grund af manglende hukommelse i SAP HANA-databasen.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=Opgaven blev ikke udført på grund af en afvisning af SAP HANA-adgangskontrol.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=Opgaven blev ikke udført på grund af for mange aktive SAP HANA-forbindelser.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=Gentagelseshandlingen kunne ikke udføres, fordi nye forsøg kun er tilladt på den overordnede i en indlejret opgavekæde.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Logfiler for de underordnede opgaver, der ikke blev udført, er ikke længere tilgængelige for Forsøg igen.


####Metrics Labels

performanceOptimized=Optimeret til ydeevne
memoryOptimized=Optimeret til hukommelse

JOB_EXECUTION=Jobudførelse
EXECUTION_MODE=Kørselstilstand
NUMBER_OF_RECORDS_OVERALL=Antal persisterede poster
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Antal poster fra den eksterne kilde
RUNTIME_MS_REMOTE_EXECUTION_TIME=Behandlingstid for ekstern kilde
MEMORY_CONSUMPTION_GIB=Maksimal hukommelse for SAP HANA
NUMBER_OF_PARTITIONS=Antal partitioner
MEMORY_CONSUMPTION_GIB_OVERALL=Maksimal hukommelse for SAP HANA
NUMBER_OF_PARTITIONS_LOCKED=Antal låste partitioner
PARTITIONING_COLUMN=Partitioneringskolonne
HANA_PEAK_CPU_TIME=SAP HANA-CPU-tid i alt
USED_IN_DISK=Anvendt lagerplads
INPUT_PARAMETER_PARAMETER_VALUE=Inputparameter
INPUT_PARAMETER=Inputparameter
ECN_ID=Navn på elastisk beregningsnode

DAC=Dataadgangskontroller
YES=Ja
NO=Nej
noofrecords=Antal poster
partitionpeakmemory=Maksimal hukommelse for SAP HANA
value=Værdi
metricsTitle=Metrikker ({0})
partitionmetricsTitle=Partitioner ({0})
partitionLabel=Partition
OthersNotNull=Værdier ikke inkluderet i intervaller
OthersNull=Null-værdier
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Indstillinger anvendt ved seneste persistenskørsel:
#XMSG: Message for input parameter name
inputParameterLabel=Inputparameter
#XMSG: Message for input parameter value
inputParameterValueLabel=Værdi
#XMSG: Message for persisted data
inputParameterPersistedLabel=Persisteret kl.
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Slet data
REMOVE_DELETED_RECORDS=Fjern slettede dataposter
MERGE_FILES=Flet filer
OPTIMIZE_FILES=Optimer filer
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Vis i SAP BW Bridge Monitor

ANALYZE_PERFORMANCE=Analyser ydeevne
CANCEL_ANALYZE_PERFORMANCE=Annuller analyse af ydeevne

#XFLD: Label for frequency column
everyLabel=Hver
#XFLD: Plural Recurrence text for Hour
hoursLabel=Timer
#XFLD: Plural Recurrence text for Day
daysLabel=Dage
#XFLD: Plural Recurrence text for Month
monthsLabel=Måneder
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutter

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Vis guide til fejlfinding af persistens</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Proces løbet tør for hukommelse. Kan ikke persistere data for visningen "{0}". Se Help Portal for at få flere oplysninger om fejl i forbindelse med at løbe tør for hukommelse. Overvej at tjekke Visningsanalyse for at analysere visningen for kompleksitet i hukommelsesforbrug.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Ikke relevant
OPEN_BRACKET=(
CLOSE_BRACKET=)
