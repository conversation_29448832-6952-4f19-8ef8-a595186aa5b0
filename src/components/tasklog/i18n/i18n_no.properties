
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Protokolldetaljer
#XFLD: Header
TASK_LOGS=Oppgaveprotokoller ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Kjøringer ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Vis detaljer
#XFLD: Button text
STOP=Stopp kjøring
#XFLD: Label text
RUN_START=Start forrige kjøring
#XFLD: Label text
RUN_END=Slutt forrige kjøring
#XFLD: Label text
RUNTIME=Varighet
#XTIT: Count for Messages
txtDetailMessages=Meldinger ({0})
#XFLD: Label text
TIME=Tidsstempel
#XFLD: Label text
MESSAGE=Melding
#XFLD: Label text
TASK_STATUS=Kategori
#XFLD: Label text
TASK_ACTIVITY=Aktivitet
#XFLD: Label text
RUN_START_DETAILS=Start
#XFLD: Label text
RUN_END_DETAILS=Slutt
#XFLD: Label text
LOGS=Kjøringer
#XFLD: Label text
STATUS=Status
#XFLD: Label text
RUN_STATUS=Kjøringsstatus
#XFLD: Label text
Runtime=Varighet
#XFLD: Label text
RuntimeTooltip=Varighet (hh: mm: ss)
#XFLD: Label text
TRIGGEREDBY=Utløst av
#XFLD: Label text
TRIGGEREDBYNew=Kjørt av
#XFLD: Label text
TRIGGEREDBYNewImp=Kjøring startet av
#XFLD: Label text
EXECUTIONTYPE=Utføringstype
#XFLD: Label text
EXECUTIONTYPENew=Kjøringstype
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Overordnet kjederom
#XFLD: Refresh tooltip
TEXT_REFRESH=Oppdater
#XFLD: view Details link
VIEW_ERROR_DETAILS=Vis detaljer
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Last ned tilleggsdetaljer
#XMSG: Download completed
downloadStarted=Nedlasting startet
#XMSG: Error while downloading content
errorInDownload=Det oppstod en feil under nedlasting.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Vis detaljer
#XBTN: cancel button of task details dialog
TXT_CANCEL=Avbryt
#XBTN: back button from task details
TXT_BACK=Tilbake
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Oppgaven er fullført
#XFLD: Log message with failed status
MSG_LOG_FAILED=Oppgaven mislyktes
#XFLD: Master and detail table with no data
No_Data=Ingen data
#XFLD: Retry tooltip
TEXT_RETRY=Prøv på nytt
#XFLD: Cancel Run label
TEXT_CancelRun=Avbryt kjøring
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Rydd opp i mislykket lasting
#XMSG:button copy sql statement
txtSQLStatement=Kopier SQL-setning
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Åpne fjernspørringsmonitor
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=Klikk på "Åpne fjernspørringsmonitor" for å vise fjern-SQL-setningene.
#XMSG:button ok
txtOk=OK
#XMSG: button close
txtClose=Lukk
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=Aktiviteten "Avbryt kjøring" for objektet "{0}" er startet.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=Aktiviteten "Avbryt kjøring" for objektet "{0}" mislyktes.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=Aktiviteten "Avbryt kjøring" for objektet "{0}" er ikke lenger mulig fordi replikeringsstatusen er endret.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Ingen oppgaveprotokoller har status "kjører".
#XMSG: message for conflicting task
Task_Already_Running=En oppgave i konflikt kjøres allerede for objektet "{0}".
#XFLD: Label for no task log with running state title
actionInfo=Aktivitetsinfo
#XMSG Copied to clipboard
copiedToClip=Kopiert til utklippstavle
#XFLD copy
Copy=Kopier
#XFLD copy correlation ID
CopyCorrelationID=Kopier korrelasjons-ID
#XFLD Close
Close=Lukk
#XFLD: show more Label
txtShowMore=Vis mer
#XFLD: message Label
messageLabel=Melding:
#XFLD: details Label
detailsLabel=Detaljer:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Utfører SQL\r\n-setning:
#XFLD:statementId Label
statementIdLabel=Setnings-ID:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Antall fjern\r\n-SQL-setninger:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Antall setninger
#XFLD: Space Label
txtSpaces=Rom
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Uautoriserte rom
#XFLD: Privilege Error Text
txtPrivilegeError=Du har ikke de nødvendige autorisasjonene til å vise disse dataene.
#XFLD: Label for Object Header
DATA_ACCESS=Datatilgang
#XFLD: Label for Object Header
SCHEDULE=Tidsplan
#XFLD: Label for Object Header
DETAILS=Detaljer
#XFLD: Label for Object Header
LATEST_UPDATE=Siste oppdatering
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Siste endring (kilde)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Oppdateringsfrekvens
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Planlagt frekvens
#XFLD: Label for Object Header
NEXT_RUN=Neste kjøring
#XFLD: Label for Object Header
CONNECTION=Forbindelse
#XFLD: Label for Object Header
DP_AGENT=DP-agent
#XFLD: Label for Object Header
USED_IN_MEMORY=Minne brukt for lagring (MiB)
#XFLD: Label for Object Header
USED_DISK=Disk brukt for lagring (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Størrelse på in-memory (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Størrelse på disk (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Antall poster

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Satt til "mislykket"
SET_TO_FAILED_ERR=Denne oppgaven ble kjørt, men brukeren satt oppgavens status til MISLYKKET.
#XFLD: Label for stopped failed
FAILLOCKED=Kjøring pågår allerede
#XFLD: sub status STOPPED
STOPPED=Stoppet
STOPPED_ERR=Denne oppgaven ble stoppet men tilbakerulling ble ikke utført.
#XFLD: sub status CANCELLED
CANCELLED=Avbrutt
CANCELLED_ERR=Denne oppgaven ble annullert etter at den ble startet. I dette tilfellet ble dataene rullet tilbake og gjenopprettet til tilstanden slik den var før oppgavekjøringen først ble utløst.
#XFLD: sub status LOCKED
LOCKED=Sperret
LOCKED_ERR=Den samme oppgaven kjører allerede, så denne oppgavejobben kan ikke kjøres parallelt med utføringen av en eksisterende oppgave.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Unntak for oppgave
TASK_EXCEPTION_ERR=Det har oppstått en uventet feil under utføringen av denne oppgaven.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Unntak for oppgaveutføring
TASK_EXECUTE_EXCEPTION_ERR=Det oppsto en feil under utføring av denne oppgaven.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Uautorisert
UNAUTHORIZED_ERR=Brukeren kan ikke autentiseres, er sperret eller slettet.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Forbudt
FORBIDDEN_ERR=Den tilordnede brukeren har ikke de nødvendige autorisasjonene for utføring av denne oppgaven.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Ikke utløst
FAIL_NOT_TRIGGERED_ERR=Denne oppgavejobben kan ikke utføres pga. systemavbrudd eller fordi deler av databasesystemet ikke er tilgjengelig ved tidspunktet for den planlagte utføringen. Vent til det neste planlagte tidspunktet for jobbutføringen eller planlegg jobben på nytt.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Tidsplan avbrutt
SCHEDULE_CANCELLED_ERR=Denne oppgavejobben kan ikke utføres pga. en intern feil. Kontakt SAP-brukerstøtte og oppgi en korrelasjons-ID og et tidsstempel fra detaljinformasjonen i oppgavejobbens protokoll.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Foregående kjøring pågår
SUCCESS_SKIPPED_ERR=Utføringen av denne oppgaven er ikke utløst fordi en tidligere kjøring av den samme oppgaven pågår fortsatt.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Eier mangler
FAIL_OWNER_MISSING_ERR=Denne oppgavejobben kan ikke utføres fordi den ikke har en tilordnet systembruker. Tilordne en ansvarlig bruker til jobben.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Samtykke ikke tilgjengelig
FAIL_CONSENT_NOT_AVAILABLE_ERR=Du har ikke gitt SAP autorisasjon til å utføre oppgavekjeder eller planlegge dataintegrasjonsoppgaver på dine vegne. Velg det anviste alternativet for å gi ditt samtykke.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Samtykke utløpt
FAIL_CONSENT_EXPIRED_ERR=Autorisasjonen som tillater at SAP kjører oppgavekjeder eller planlegger dataintegrasjonsoppgaver på dine vegne, er utløpt. Velg det anviste alternativet for å fornye samtykket.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Samtykke ugyldiggjort
FAIL_CONSENT_INVALIDATED_ERR=Denne oppgaven kan ikke utføres, vanligvis pga. en endring i konfigurasjonen av identitetsleverandøren for tenanten. Hvis dette er tilfellet, kan nye oppgavejobber ikke kjøres eller planlegges i navnet til den berørte brukeren. Hvis den tilordnede brukeren fortsatt finnes i den nye IdP-en, trekker du planleggingssamtykket tilbake og gir samtykke på nytt. Hvis den tilordnede brukeren ikke lenger finnes, tilordner du en ny oppgavejobbeier og gir det nødvendige samtykket for oppgaveplanlegging. Se følgende SAP-merknad: https://launchpad.support.sap.com/#/notes/3089828 for mer informasjon.
TASK_EXECUTOR_ERROR=Oppgaveutføring
TASK_EXECUTOR_ERROR_ERR=Det oppsto en intern feil for denne oppgaven, sannsynligvis under forberedelsestrinnene for utføringen, og oppgaven kunne ikke startes.
PREREQ_NOT_MET=Krav er ikke oppfylt
PREREQ_NOT_MET_ERR=Denne oppgaven kan ikke kjøres på grunn av problemer i definisjonen. Objektet er for eksempel ikke distribuert, en oppgavekjede inneholder sirkulær logikk eller SQL-en for en visning er ugyldig.
RESOURCE_LIMIT_ERROR=Ressursgrensefeil
RESOURCE_LIMIT_ERROR_ERR=Kan ikke utføre oppgave fordi tilstrekkelige ressurser ikke er tilgjengelig eller er opptatt.
FAIL_CONSENT_REFUSED_BY_UMS=Samtykke avvist
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Denne oppgaven kan ikke utføres i planlagte kjøringer eller oppgavekjeder pga. en endring i konfigurasjonen av brukerens identitetsleverandør på tenanten. For mer informasjon, se følgende SAP-merknad: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Planlagt
#XFLD: status text
SCHEDULEDNew=Permanent
#XFLD: status text
PAUSED=Satt på pause
#XFLD: status text
DIRECT=Direkte
#XFLD: status text
MANUAL=Manuell
#XFLD: status text
DIRECTNew=Enkel
#XFLD: status text
COMPLETED=Fullført
#XFLD: status text
FAILED=Mislykket
#XFLD: status text
RUNNING=Kjører
#XFLD: status text
none=Ingen
#XFLD: status text
realtime=Sanntid
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Underoppgave
#XFLD: New Data available in the file
NEW_DATA=Nye data

#XFLD: text for values shown in column Replication Status
txtOff=Av
#XFLD: text for values shown in column Replication Status
txtInitializing=Initialiserer
#XFLD: text for values shown in column Replication Status
txtLoading=Laster
#XFLD: text for values shown in column Replication Status
txtActive=Aktiv
#XFLD: text for values shown in column Replication Status
txtAvailable=Tilgjengelig
#XFLD: text for values shown in column Replication Status
txtError=Feil
#XFLD: text for values shown in column Replication Status
txtPaused=Satt på pause
#XFLD: text for values shown in column Replication Status
txtDisconnected=Frakoblet
#XFLD: text for partially Persisted views
partiallyPersisted=Delvis persistert

#XFLD: activity text
REPLICATE=Replikert
#XFLD: activity text
REMOVE_REPLICATED_DATA=Fjern replikerte data
#XFLD: activity text
DISABLE_REALTIME=Deaktiver datareplikering i sanntid
#XFLD: activity text
REMOVE_PERSISTED_DATA=Fjern persisterte data
#XFLD: activity text
PERSIST=Persistere
#XFLD: activity text
EXECUTE=Utfør
#XFLD: activity text
TASKLOG_CLEANUP=Opprydding av oppgaveprotokoll
#XFLD: activity text
CANCEL_REPLICATION=Avbryt replikering
#XFLD: activity text
MODEL_IMPORT=Modellimport
#XFLD: activity text
NONE=Ingen
#XFLD: activity text
CANCEL_PERSISTENCY=Avbryt persistens
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Analyser visning
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Avbryt visningsanalyse

#XFLD: severity text
INFORMATION=Informasjon
#XFLD: severity text
SUCCESS=Vellykket
#XFLD: severity text
WARNING=Advarsel
#XFLD: severity text
ERROR=Feil
#XFLD: text for values shown for Ascending sort order
SortInAsc=Stigende sortering
#XFLD: text for values shown for Descending sort order
SortInDesc=Synkende sortering
#XFLD: filter text for task log columns
Filter=Filter
#XFLD: object text for task log columns
Object=Objekt
#XFLD: space text for task log columns
crossSpace=Rom

#XBUT: label for remote data access
REMOTE=Fjern
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Replikert (sanntid)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Replikert (øyeblikksbilde)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=Replikering i sanntid er sperret på grunn av en feil. Når feilen er korrigert, kan du velge "Prøv på nytt" for å fortsette med replikering i sanntid.
ERROR_MSG=Replikering i sanntid er sperret på grunn av en feil.
RETRY_FAILED_ERROR=Nytt forsøk mislyktes med feil.
LOG_INFO_DETAILS=Ingen protokoller genereres når data blir replikert i sanntidsmodus. Protokollene som vises, gjelder tidligere aktiviteter.

#XBUT: Partitioning label
partitionMenuText=Partisjonering
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Opprett partisjon
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Rediger partisjon
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Slett partisjon
#XFLD: Initial text
InitialPartitionText=Definer partisjoner ved å spesifisere kriterier for å dele datasett i mindre sett.
DefinePartition=Definer partisjoner
#XFLD: Message text
partitionChangedInfo=Persistensdefinisjonen er endret siden siste replikering. Endringene aktiveres etter neste datalasting.
#XFLD: Message text
REAL_TIME_WARNING=Partisjonering brukes bare når et nytt øyeblikksbilde lastes inn. Den brukes ikke for sanntidsreplikering.
#XFLD: Message text
loadSelectedPartitions=Persistering av data er startet for de valgte partisjonene for ''{0}''
#XFLD: Message text
loadSelectedPartitionsError=Persistering av data mislyktes for de valgte partisjonene for ''{0}''
#XFLD: Message text
viewpartitionChangedInfo=Partisjonsdefinisjonen er endret siden siste persistenskjøring. For å ta i bruk endringene vil den neste datalastingen være et fullstendig øyeblikksbilde, inkludert sperrede partisjoner. Når denne fullstendige lastingen er fullført, kan du kjøre data for enkeltpartisjoner.
#XFLD: Message text
viewpartitionChangedInfoLocked=Partisjonsdefinisjonen er endret siden siste persistenskjøring. For å ta i bruk endringene vil den neste datalastingen være et fullstendig øyeblikksbilde, unntatt for sperrede og uendrede partisjonsintervaller. Når denne lastingen er ferdig, kan du laste valgte partisjoner på nytt.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Tabellreplikering
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Planlegg replikering
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Opprett tidsplan
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Rediger tidsplan
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Slett tidsplan
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Last nytt øyeblikksbilde
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Start datareplikering
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Fjern replikerte data
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Aktiver sanntidstilgang
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Aktiver datareplikering i sanntid
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Deaktiver datareplikering i sanntid
#XFLD: Message for replicate table action
replicateTableText=Tabellreplikering
#XFLD: Message for replicate table action
replicateTableTextNew=Datareplikering
#XFLD: Message to schedule task
scheduleText=Tidsplan
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Visningspersistens
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Datapersistens
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Last nytt øyeblikksbilde
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Start datapersistens
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Fjern persisterte data
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Oppdelt behandling
#XBUT: Label for scheduled replication
scheduledTxt=Planlagt
#XBUT: Label for statistics button
statisticsTxt=Statistikk
#XBUT: Label for create statistics
createStatsTxt=Opprett statistikk
#XBUT: Label for edit statistics
editStatsTxt=Rediger statistikk
#XBUT: Label for refresh statistics
refreshStatsTxt=Oppdater statistikk
#XBUT: Label for delete statistics
dropStatsTxt=Slett statistikk
#XMSG: Create statistics success message
statsSuccessTxt=Startet oppretting av statistikk av typen {0} for {1}.
#XMSG: Edit statistics success message
statsEditSuccessTxt=Startet endring av statistikktype til {0} for {1}.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Startet oppdatering av statistikk for {0}.
#XMSG: Drop statistics success message
statsDropSuccessTxt=Statistikk slettet for {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=Feil ved oppretting av statistikk
#XMSG: Edit statistics error message
statsEditErrorTxt=Feil ved endring av statistikk
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Feil ved oppdatering av statistikk
#XMSG: Drop statistics error message
statsDropErrorTxt=Feil ved sletting av statistikk
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Er du sikker på at du vil utelate datastatistikken?
startPersistencyAdvisorLabel=Start visningsanalyse

#Partition related texts
#XFLD: Label for Column
column=Kolonne
#XFLD: Label for No of Partition
noOfPartitions=Antall partisjoner
#XFLD: Label for Column
noOfParallelProcess=Antall parallelle prosesser
#XFLD: Label text
noOfLockedPartition=Antall sperrede partisjoner
#XFLD: Label for Partition
PARTITION=Partisjoner
#XFLD: Label for Column
AVAILABLE=Tilgjengelig
#XFLD: Statistics Label
statsLabel=Statistikk
#XFLD: Label text
COLUMN=Kolonne:
#XFLD: Label text
PARALLEL_PROCESSES=Parallelle prosesser:
#XFLD: Label text
Partition_Range=Partisjonsintervall
#XFLD: Label text
Name=Navn
#XFLD: Label text
Locked=Sperret
#XFLD: Label text
Others=ANDRE
#XFLD: Label text
Delete=Slett
#XFLD: Label text
LoadData=Last valgte partisjoner
#XFLD: Label text
LoadSelectedData=Last valgte partisjoner
#XFLD: Confirmation text
LoadNewPersistenceConfirm=Dette vil laste inn et nytt øyeblikksbilde for alle ikke-sperrede og endrede partisjoner, ikke bare for de du har valgt. Vil du fortsette?
#XFLD: Label text
Continue=Fortsett

#XFLD: Label text
PARTITIONS=Partisjoner
#XFLD: Label text
ADD_PARTITIONS=+ Legg til partisjon
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Legg til partisjon
#XFLD: Label text
deleteRange=Slett partisjon
#XFLD: Label text
LOW_PLACE_HOLDER=Oppgi lav verdi
#XFLD: Label text
HIGH_PLACE_HOLDER=Oppgi høy verdi
#XFLD: tooltip text
lockedTooltip=Sperr partisjon etter første last

#XFLD: Button text
Edit=Rediger
#XFLD: Button text
CANCEL=Avbryt

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Siste oppdatering av statistikk
#XFLD: Statistics Fields
STATISTICS=Statistikk

#XFLD:Retry label
TEXT_Retry=Prøv på nytt
#XFLD:Retry label
TEXT_Retry_tooltip=Prøv sanntidsreplikering på nytt etter at feil er rettet.
#XFLD: text retry
Retry=Bekreft
#XMG: Retry confirmation text
retryConfirmationTxt=Den siste replikeringen i sanntid ble avbrutt med en feil.\n Bekreft at feilen er korrigert, og at sanntidsreplikering kan startes på nytt.
#XMG: Retry success text
retrySuccess=Nytt forsøk er initiert.
#XMG: Retry fail text
retryFail=Nytt forsøk mislyktes.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Opprett statistikk
#XMSG: activity message for edit statistics
DROP_STATISTICS=Slett statistikk
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Oppdater statistikk
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Rediger statistikk
#XMSG: Task log message started task
taskStarted=Oppgave {0} har startet.
#XMSG: Task log message for finished task
taskFinished=Oppgave {0} ble avsluttet med status {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=Oppgave {0} ble avsluttet kl. {2} med status {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=Oppgave ({0}) har inndataparametere.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=Oppgave {0} ble avsluttet med en uventet feil. Oppgavestatusen er satt til {1}.
#XMSG: Task log message for failed task
failedToEnd=Kan ikke sette statusen til {0} eller kan ikke fjerne sperren.
#XMSG: Task log message
lockNotFound=Vi kan ikke fullføre prosessen fordi sperren mangler: Oppgaven kan ha blitt annullert.
#XMSG: Task log message failed task
failedOverwrite=Oppgave {0} er allerede sperret av {1}. Derfor mislyktes den med følgende feil: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=En annen oppgave har overtatt sperringen av denne oppgaven.
#XMSG: Task log message failed takeover
failedTakeover=Kan ikke overta en eksisterende oppgave.
#XMSG: Task log message successful takeover
successTakeover=Det var nødvendig å oppheve resterende sperre. Ny sperre er fastsatt for denne oppgaven.
#XMSG: Tasklog Dialog Details
txtDetails=Du kan se hvilke eksterne setninger som er behandlet under kjøringen, ved å åpne fjernspørringsmonitoren og vise detaljene for partisjonsspesifikke meldinger.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=Fjern-SQL-setningene er slettet fra databasen og kan ikke vises.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Fjernspørringer med koblinger som er tilordnet til andre rom, kan ikke vises. Gå til fjernspørringsmonitoren og bruk setnings-ID-en til å filtrere dem.
#XMSG: Task log message for parallel check error
parallelCheckError=Oppgaven kan ikke behandles fordi en annen oppgave kjøres, og den sperrer allerede denne oppgaven.
#XMSG: Task log message for parallel running task
parallelTaskRunning=En oppgave i konflikt kjøres allerede.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} under kjøring med korrelasjons-ID {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=Den tilordnede brukeren har ikke de nødvendige autorisasjonene for utføring av denne oppgaven.

#XBUT: Label for open in Editor
openInEditor=Åpne i redigeringsprogram
#XBUT: Label for open in Editor
openInEditorNew=Åpne i databygger
#XFLD:Run deails label
runDetails=Kjøringsdetaljer
#XFLD: Label for Logs
Logs=Protokoller
#XFLD: Label for Settings
Settings=Innstillinger
#XFLD: Label for Save button
Save=Lagre
#XFLD: Label for Standard
Standard_PO=Ytelsesoptimert (anbefalt)
#XFLD: Label for Hana low memory processing
HLMP_MO=Minneoptimert
#XFLD: Label for execution mode
ExecutionMode=Kjøringsmodus
#XFLD: Label for job execution
jobExecution=Behandlingsmodus
#XFLD: Label for Synchronous
syncExec=Synkront
#XFLD: Label for Asynchronous
asyncExec=Asynkront
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Bruk standard (asynkront, kan bli endret i fremtiden)
#XMSG: Save settings success
saveSettingsSuccess=SAP HANA-utføringsmodus er endret.
#XMSG: Save settings failure
saveSettingsFailed=Endring av SAP HANA-utføringsmodus mislyktes.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Jobbutføring er endret.
#XMSG: Job Execution change failed
jobExecSettingFailed=Endring av jobbutføring mislyktes.
#XMSG: Text for Type
typeTxt=Type
#XMSG: Text for Monitor
monitorTxt=Overvåk
#XMSG: Text for activity
activityTxt=Aktivitet
#XMSG: Text for metrics
metricsTxt=Målinger
#XTXT: Text for Task chain key
TASK_CHAINS=Oppgavekjede
#XTXT: Text for View Key
VIEWS=Vis
#XTXT: Text for remote table key
REMOTE_TABLES=Fjerntabell
#XTXT: Text for Space key
SPACE=Rom
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Elastisk beregningsnode
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Replikeringsflyt
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Intelligent søk
#XTXT: Text for Local Table
LOCAL_TABLE=Lokal tabell
#XTXT: Text for Data flow key
DATA_FLOWS=Dataflyt
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=SQL Script-prosedyre
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=BW-prosesskjede
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Vis i monitor
#XTXT: Task List header text
taskListHeader=Oppgaveliste ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Kan ikke hente målinger for historiske kjøringer av en dataflyt.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=Fullstendige kjøringsdetaljer laster ikke for øyeblikket. Prøv å oppdatere.
#XFLD: Label text for the Metrices table header
metricesColLabel=Operatoretikett
#XFLD: Label text for the Metrices table header
metricesType=Type
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Antall poster
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Kjør oppgavekjeden
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Kjøringen av oppgavekjeden er startet.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Kjøring av oppgavekjeden er startet for {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Kan ikke kjøre oppgavekjeden.
#XTXT: Execute button label
runLabel=Kjør
#XTXT: Execute button label
runLabelNew=Start kjøring
#XMSG: Filter Object header
chainsFilteredTableHeader=Filtrert etter objekt: {0}
#XFLD: Parent task chain label
parentChainLabel=Overordnet oppgavekjede:
#XFLD: Parent task chain unauthorized
Unauthorized=Ikke autorisert for visning
#XFLD: Parent task chain label
parentTaskLabel=Overordnet oppgave:
#XTXT: Task status
NOT_TRIGGERED=Ikke utløst
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Åpne fullskjermvisning
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Avslutt fullskjermvisning
#XTXT: Close Task log details right panel
closeRightColumn=Lukk delen
#XTXT: Sort Text
sortTxt=Sorter
#XTXT: Filter Text
filterTxt=Filtrer
#XTXT: Filter by text to show list of filters applied
filterByTxt=Filtrer etter
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Mer enn 5 minutter
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Mer enn 15 minutter
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Men enn 1 time
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Siste timen
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Siste 24 timer
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Siste måneden
#XTXT: Messages title text
messagesText=Meldinger

#XTXT Statistics information message
statisticsInfo=Kan ikke opprette statistikk for fjerntabeller med datatilgang "Replikert". Fjern de replikerte dataene i detaljmonitoren for fjerntabeller for å opprette statistikk.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Gå til monitor for fjerntabelldetaljer

#XTXT: Repair latest failed run label
retryRunLabel=Forsøk siste kjøring på nytt
#XTXT: Repair failed run label
retryRun=Forsøk kjøring på nytt
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Nytt forsøk på kjøring av oppgavekjede er startet
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Nytt forsøk på kjøring av oppgavekjede mislyktes
#XMSG: Task chain child elements name
taskChainRetryChildObject=Oppgave {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Ny oppgave
#XFLD Analyzed View
analyzedView=Analysert visning
#XFLD Metrics
Metrics=Målinger
#XFLD Partition Metrics
PartitionMetrics=Partisjonsmåltall
#XFLD Entities
Messages=Oppgaveprotokoll
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Ingen partisjoner er definert ennå.
#XTXT: Description message for empty partition data
partitionEmptyDescText=Opprett partisjoner ved å oppgi kriterier for å dele opp store datavolumer i mindre, mer håndterbare deler.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Ingen protokoller er tilgjengelige ennå
#XTXT: Description message for empty runs data
runsEmptyDescText=Når du starter en ny aktivitet (laster et nytt øyeblikksbilde, starter visningsanalyse...) vises relaterte protokoller her.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=Konfigurasjonen av den elastiske beregningsnoden {0} er ikke vedlikeholdt tilsvarende. Kontroller definisjonen din.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Prosess mislyktes for å legge til den elastiske beregningsnoden {0}.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Oppretting og aktivering av replika for kildeobjektet ''{0}''.''{1}'' i elastisk beregningsnode {2} har startet.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Fjerning av replika for kildeobjektet ''{0}''.''{1}'' fra elastisk beregningsnode {2} har startet.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Oppretting og aktivering av replika for kildeobjektet ''{0}''.''{1}'' mislyktes.
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Fjerning av replika for kildeobjektet ''{0}''.''{1}'' mislyktes.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Ruting for beregning av analytiske spørringer for rommet {0} til den elastiske beregningsnoden {1}, er startet.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Kan ikke rute beregningen av analytiske spørringer for rommet {0} til den tilsvarende elastiske beregningsnoden.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Omdirigering av beregningen av analytiske spørringer for rommet {0} tilbake fra den elastiske beregningsnoden {1}, er startet.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Kan ikke omdirigere beregningen av analytiske spørringer for rommet {0}, tilbake til koordinator.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Oppgavekjede {0} er utløst for den tilsvarende elastiske beregningsnoden {1}.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Generering av oppgavekjede for elastisk beregningsnode {0} mislyktes.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=Klargjøring av elastisk beregningsnode {0} med oppgitt størrelse har startet: vCPU-er: {1}, minne (GiB): {2} og lagringsplass (GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=Elastisk beregningsnode {0} er allerede klargjort.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=Fordeling av elastisk beregningsnode {0} er allerede fjernet.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=Operasjonen er ikke tillatt. Stopp den elastiske beregningsnoden {0} og start den på nytt for å oppdatere dimensjoneringsplanen.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Prosess for å fjerne den elastiske beregningsnoden {0} mislyktes.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=Denne kjøringen av den elastiske beregningsnoden {0} fikk tidsavbrudd.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=Kontrollerer status og at den elastiske beregningsnoden {0} er under behandling ...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=Generering av oppgavekjede for den elastiske beregningsnoden {0} er sperret, derfor kjører kanskje kjeden {1} fortsatt.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=Kildeobjektet ''{0}''.''{1}'' er replikert som avhengighet for visningen ''{2}''.''{3}''.
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Kan ikke replikere tabellen ''{0}''.''{1}'' fordi replikering av radtabell er foreldet.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Maksimum elastisk beregningsnode per SAP HANA Cloud-instans er overskredet.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=Operasjon for elastisk beregningsnode {0} pågår fremdeles.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Sletting av replika for kildetabell {0} ble stoppet pga. tekniske problemer. Prøv igjen senere.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=Oppretting av replika for kildetabell {0} ble stoppet pga. tekniske problemer. Prøv igjen senere.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Kan ikke starte en elastisk beregningsnode siden bruksgrensen er nådd eller ingen beregnede blokktimer er fordelt ennå.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Laster oppgavekjede og forbereder kjøring av totalt {0} oppgaver som er del av denne kjeden.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=En oppgave i konflikt kjøres allerede
#XMSG: Replication will change
txt_replication_change=Replikeringstype blir endret.
txt_repl_viewdetails=Vis detaljer

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Det ser ut til at det oppstod en feil da siste kjøring ble forsøkt på nytt. Årsaken er at den forrige oppgavekjøringen mislyktes før planen kunne generes.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=Rommet "{0}" er sperret.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=Aktivitet {0} krever at den lokale tabellen {1} skal distribueres.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=Aktivitet {0} krever at deltaregistrering må være aktivert for den lokale tabellen {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=Aktivitet {0} krever at den lokale tabellen {1} skal lagre data i Object Store.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=Aktivitet {0} krever at den lokale tabellen {1} skal lagre data i databasen.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Starter å fjerne de slettede postene fra den lokale tabellen {0}.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Starter å fjerne alle poster fra den lokale tabellen {0}.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Starter å slette poster fra den lokale tabellen {0} i henhold til filterbetingelsen {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Det oppsto en feil ved fjerning av slettede poster for den lokale tabellen {0}.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Det oppsto en feil ved sletting av alle poster for den lokale tabellen {0}.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Sletter alle ferdigbehandlede poster med endringstypen "Slettet", som er eldre enn {0} dager.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Sletter alle ferdigbehandlede poster med endringstypen "Slettet", som er eldre enn {0} dager.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} poster er slettet.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} poster er merket for sletting.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=Fjerning av slettede poster for den lokale tabellen {0} er fullført.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Sletting av alle poster for lokal tabell {0} er fullført.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Starter å merke poster som "Slettet" for den lokale tabellen {0}.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Starter å merke poster som "Slettet" for den lokale tabellen {0} i henhold til filterbetingelsen {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Merking av poster som "Slettet" for den lokale tabellen {0} er fullført.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Dataendringer lastes inn midlertidig i tabellen {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Dataendringer lastes inn midlertidig i tabellen {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Dataendringer behandles og slettes fra tabellen {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Forbindelsesdetaljer.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Begynner å optimere lokal tabell (fil).
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Det oppstod en feil ved optimering av lokal tabell (fil).
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Det oppstod en feil. Optimering av  lokal tabell (fil) er stoppet.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Optimerer lokal tabell (fil)...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=Lokal tabell (fil) er optimert.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=Lokal tabell (fil) er optimert.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=Tabellen er optimert med Z-Order-kolonnene: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Det oppstod en feil. Avkorting av lokal tabell (fil) er stoppet.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Avkortet lokal tabell (fil)...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=Lokalisering for inngående buffer for lokal tabell (fil) er brutt.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Begynner vakuum (sletting av alle fullstendig behandlede poster) av lokal tabell (fil).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Det oppstod en feil ved vakuum av lokal tabell (fil).
#XMSG: Task log message
LTF_VACUUM_STOPPED=Det oppstod en feil. Vakuum av  lokal tabell (fil) er stoppet.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Vakuum av lokal tabell (fil)...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=Vakuum er fullført.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Sletter alle ferdigbehandlede poster som er eldre enn {0} dager.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Sletter alle ferdigbehandlede poster som er eldre enn {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Begynner å slå nye poster sammen med lokal tabell (fil).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Begynner å slå sammen nye poster med lokal tabell (fil) med innstillingen "Slå sammen data automatisk".
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Begynner å slå sammen nye poster med lokal tabell (fil). Denne oppgaven ble startet via forespørselen om API-sammenslåing.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Slår nye poster sammen med lokal tabell (fil).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Det oppstod en feil da nye poster ble slått sammen med lokal tabell (fil).
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=Lokal tabell (fil) er slått sammen.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Det oppstod en feil. Sammenslåing av  lokal tabell (fil) er stoppet.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=Sammenslåing av den lokale tabellen (fil) mislyktes på grunn av en feil, men operasjonen ble delvis utført, og noen data er allerede slått sammen.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Det oppsto en tidsavbruddsfeil. Aktiviteten {0} har kjørt i {1} timer.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=Kan ikke starte den asynkrone utføringen på grunn av høy systembelastning. Åpne ''Systemonitor'' og kontroller kjørestatusen.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=Den asynkrone utføringen er avbrutt.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=Oppgaven {0} ble kjørt innenfor minnegrensene til {1} og {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=Oppgaven {0} ble kjørt med ressurs-ID {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=Finn og erstatt har startet i lokal tabell (fil).
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Finn og erstatt er avsluttet i lokal tabell (fil).
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Finn og erstatt mislyktes i lokal tabell (fil).

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Det oppsto en feil. Oppdatering av statistikk for lokal tabell (fil) er stoppet.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=Oppgaven mislyktes fordi det er for lite minne i SAP HANA-databasen.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=Oppgaven mislyktes på grunn av avvisning av SAP HANA-tilgangskontroll.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=Oppgaven mislyktes på grunn av for mange aktive SAP HANA-forbindelser.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=Operasjonen "prøv på nytt" kan ikke utføres fordi å prøve på nytt bare er tillatt på overordnet nivå for en nestet oppgavekjede.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Protokoller for mislykkede underordnede oppgaver er ikke lenger tilgjengelig for Gjenforsøk for å kunne fortsette.


####Metrics Labels

performanceOptimized=Ytelsesoptimert
memoryOptimized=Minneoptimert

JOB_EXECUTION=Jobbutføring
EXECUTION_MODE=Kjøringsmodus
NUMBER_OF_RECORDS_OVERALL=Antall persisterte poster
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Antall poster lest fra fjernkilde
RUNTIME_MS_REMOTE_EXECUTION_TIME=Behandlingstid for fjernkilde
MEMORY_CONSUMPTION_GIB=Høyeste minnebruk for SAP HANA
NUMBER_OF_PARTITIONS=Antall partisjoner
MEMORY_CONSUMPTION_GIB_OVERALL=Høyeste minnebruk for SAP HANA
NUMBER_OF_PARTITIONS_LOCKED=Antall sperrede partisjoner
PARTITIONING_COLUMN=Partisjoneringskolonne
HANA_PEAK_CPU_TIME=Samlet CPU-tid for SAP HANA
USED_IN_DISK=Brukt lagringsplass
INPUT_PARAMETER_PARAMETER_VALUE=Inndataparameter
INPUT_PARAMETER=Inndataparameter
ECN_ID=Navn på elastisk beregningsnode

DAC=Datatilgangskontroller
YES=Ja
NO=Nei
noofrecords=Antall poster
partitionpeakmemory=Høyeste minnebruk for SAP HANA
value=Verdi
metricsTitle=Metrikker ({0})
partitionmetricsTitle=Partisjoner ({0})
partitionLabel=Partisjon
OthersNotNull=Verdier ikke inkludert i områder
OthersNull=Nullverdier
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Innstillinger brukt for siste datapersistenskjøring:
#XMSG: Message for input parameter name
inputParameterLabel=Inndataparameter
#XMSG: Message for input parameter value
inputParameterValueLabel=Verdi
#XMSG: Message for persisted data
inputParameterPersistedLabel=Persistert kl.
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Slett data
REMOVE_DELETED_RECORDS=Fjern slettede poster
MERGE_FILES=Slå sammen filer
OPTIMIZE_FILES=Optimer filer
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Vis detaljer i SAP BW-bromonitor

ANALYZE_PERFORMANCE=Analyser ytelse
CANCEL_ANALYZE_PERFORMANCE=Avbryt ytelsesanalyse

#XFLD: Label for frequency column
everyLabel=Hver
#XFLD: Plural Recurrence text for Hour
hoursLabel=Timer
#XFLD: Plural Recurrence text for Day
daysLabel=Dager
#XFLD: Plural Recurrence text for Month
monthsLabel=Måneder
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutter

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Vis feilsøkingsveiledning for persistens</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Prosess har ikke nok minne. Kan ikke persistere data for visningen "{0}". Se Help Portal for mer informasjon om feil på grunn av ikke nok minne. Vurder å sjekke visningsanalysen for å analysere visningen for kompleksitet i minneforbruk.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Ikke relevant
OPEN_BRACKET=(
CLOSE_BRACKET=)
