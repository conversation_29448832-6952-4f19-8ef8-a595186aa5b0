
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=日志详细信息
#XFLD: Header
TASK_LOGS=任务日志（{0}）

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=运行（{0}）
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=查看详细信息
#XFLD: Button text
STOP=停止运行
#XFLD: Label text
RUN_START=上次运行开始
#XFLD: Label text
RUN_END=上次运行结束
#XFLD: Label text
RUNTIME=持续时间
#XTIT: Count for Messages
txtDetailMessages=消息（{0}）
#XFLD: Label text
TIME=时间戳
#XFLD: Label text
MESSAGE=消息
#XFLD: Label text
TASK_STATUS=类别
#XFLD: Label text
TASK_ACTIVITY=活动
#XFLD: Label text
RUN_START_DETAILS=开始
#XFLD: Label text
RUN_END_DETAILS=结束
#XFLD: Label text
LOGS=运行
#XFLD: Label text
STATUS=状态
#XFLD: Label text
RUN_STATUS=运行状态
#XFLD: Label text
Runtime=持续时间
#XFLD: Label text
RuntimeTooltip=持续时间（时:分:秒）
#XFLD: Label text
TRIGGEREDBY=触发者
#XFLD: Label text
TRIGGEREDBYNew=运行者
#XFLD: Label text
TRIGGEREDBYNewImp=运行启动者
#XFLD: Label text
EXECUTIONTYPE=执行类型
#XFLD: Label text
EXECUTIONTYPENew=运行类型
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=父链空间
#XFLD: Refresh tooltip
TEXT_REFRESH=刷新
#XFLD: view Details link
VIEW_ERROR_DETAILS=查看详细信息
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=下载其他详细信息
#XMSG: Download completed
downloadStarted=已开始下载
#XMSG: Error while downloading content
errorInDownload=下载时出错。
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=查看详细信息
#XBTN: cancel button of task details dialog
TXT_CANCEL=取消
#XBTN: back button from task details
TXT_BACK=返回
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=任务已完成
#XFLD: Log message with failed status
MSG_LOG_FAILED=任务失败
#XFLD: Master and detail table with no data
No_Data=无数据
#XFLD: Retry tooltip
TEXT_RETRY=重试
#XFLD: Cancel Run label
TEXT_CancelRun=取消运行
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=清理失败的加载
#XMSG:button copy sql statement
txtSQLStatement=复制 SQL 语句
#XMSG:button open remote query monitor
txtOpenQueryMonitor=打开远程查询监控器
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=要显示远程 SQL 语句，请点击 "打开远程查询监控器"。
#XMSG:button ok
txtOk=确定
#XMSG: button close
txtClose=关闭
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=对象 "{0}" 的取消运行操作已经开始。
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=对象 "{0}" 的取消运行操作失败。
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=复制状态已经更改，不能再执行对象 "{0}" 的取消运行操作。
#XMSG: Info message for no Running state logId
noTaskWithRunningState=没有任务日志处于“运行中”状态。
#XMSG: message for conflicting task
Task_Already_Running=对象 "{0}" 已经在运行冲突任务。
#XFLD: Label for no task log with running state title
actionInfo=操作信息
#XMSG Copied to clipboard
copiedToClip=已复制到剪贴板
#XFLD copy
Copy=复制
#XFLD copy correlation ID
CopyCorrelationID=复制相关性 ID
#XFLD Close
Close=关闭
#XFLD: show more Label
txtShowMore=显示更多
#XFLD: message Label
messageLabel=消息：
#XFLD: details Label
detailsLabel=详细信息：
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=执行 SQL \r\n 语句：
#XFLD:statementId Label
statementIdLabel=语句 ID：
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=远程 \r\n SQL 语句数：
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=语句数
#XFLD: Space Label
txtSpaces=空间
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=没有获得授权的空间
#XFLD: Privilege Error Text
txtPrivilegeError=你没有足够的权限查看这个数据。
#XFLD: Label for Object Header
DATA_ACCESS=数据访问
#XFLD: Label for Object Header
SCHEDULE=计划
#XFLD: Label for Object Header
DETAILS=详细信息
#XFLD: Label for Object Header
LATEST_UPDATE=最新更新
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=最新更改（源）
#XFLD: Label for Object Header
REFRESH_FREQUENCY=刷新频率
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=计划频率
#XFLD: Label for Object Header
NEXT_RUN=下次运行
#XFLD: Label for Object Header
CONNECTION=连接
#XFLD: Label for Object Header
DP_AGENT=数据预配代理
#XFLD: Label for Object Header
USED_IN_MEMORY=已用内存存储 (MiB)
#XFLD: Label for Object Header
USED_DISK=已用磁盘存储 (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=内存大小 (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=磁盘存储大小 (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=记录数

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=设为失败
SET_TO_FAILED_ERR=此任务正在运行，但用户将此任务的状态设为“失败”。
#XFLD: Label for stopped failed
FAILLOCKED=已在运行中
#XFLD: sub status STOPPED
STOPPED=已停止
STOPPED_ERR=此任务已停止，但未执行回滚。
#XFLD: sub status CANCELLED
CANCELLED=已取消
CANCELLED_ERR=这个任务运行在开始后被取消。这种情况下，数据回滚并还原到最初触发任务运行前存在的状态。
#XFLD: sub status LOCKED
LOCKED=已锁定
LOCKED_ERR=相同的任务已在运行，因此这个任务作业无法与现有任务的执行并行运行。
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=任务异常
TASK_EXCEPTION_ERR=这个任务在执行期间遇到未指明的错误。
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=任务执行异常
TASK_EXECUTE_EXCEPTION_ERR=这个任务在执行期间遇到错误。
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=未授权
UNAUTHORIZED_ERR=用户无法进行身份验证、已锁定或已删除。
#XFLD: sub status FORBIDDEN
FORBIDDEN=已禁止
FORBIDDEN_ERR=分配的用户没有执行这个任务所需的特许权限。
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=未触发
FAIL_NOT_TRIGGERED_ERR=由于系统运行中断或数据库系统的某些部分在计划执行期间不可用，无法执行这个任务作业。请等待下次计划作业执行或重新计划作业。
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=计划已取消
SCHEDULE_CANCELLED_ERR=由于内部错误，无法执行这个任务作业。请联系 SAP 支持人员，向其提供这个任务作业的日志详细信息中的相关性 ID 和时间戳。
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=先前运行正在进行
SUCCESS_SKIPPED_ERR=由于相同任务的先前运行仍在进行，未触发这个任务的执行。
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=缺少所有者
FAIL_OWNER_MISSING_ERR=由于没有分配系统用户，这个任务作业无法执行。为这个作业分配所有者用户。
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=未表明同意
FAIL_CONSENT_NOT_AVAILABLE_ERR=你没有授权 SAP 代替你运行任务链或计划数据集成任务。请选择提供的选项表明同意。
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=同意已过期
FAIL_CONSENT_EXPIRED_ERR=允许 SAP 代替你运行任务链或计划数据集成任务的权限已过期。请选择提供的选项表明继续同意。
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=同意已失效
FAIL_CONSENT_INVALIDATED_ERR=这个任务无法执行，通常是因为租户的身份提供者配置发生更改。在这种情况下，无法以受影响用户的名义运行或计划新的任务作业。如果分配的用户在新的身份提供者中仍然存在，则撤销计划同意，然后再次表明同意。如果分配的用户不再存在，则分配新的任务作业所有者，并对所需的任务计划表明同意。请参阅以下 SAP Note：https://launchpad.support.sap.com/#/notes/3089828，了解更多信息。
TASK_EXECUTOR_ERROR=任务执行程序
TASK_EXECUTOR_ERROR_ERR=任务很可能在执行准备步骤遇到内部错误，无法开始这个任务。
PREREQ_NOT_MET=没有满足先决条件
PREREQ_NOT_MET_ERR=这个任务不能运行，因为任务定义有问题。比如，没有部署对象，任务链包含循环逻辑，或者视图的 SQL 无效。
RESOURCE_LIMIT_ERROR=资源限制错误
RESOURCE_LIMIT_ERROR_ERR=目前不能执行任务，因为没有足够的资源或者资源被占用。
FAIL_CONSENT_REFUSED_BY_UMS=同意被拒绝
FAIL_CONSENT_REFUSED_BY_UMS_ERR=由于租户上用户的身份提供者配置发生更改，因此没能在计划运行或任务链中执行这项任务。有关更多信息，请参阅以下 SAP Note：https://launchpad.support.sap.com/#/notes/3120806。
#XFLD: status text
SCHEDULED=已计划
#XFLD: status text
SCHEDULEDNew=永久
#XFLD: status text
PAUSED=已暂停
#XFLD: status text
DIRECT=直接
#XFLD: status text
MANUAL=手动
#XFLD: status text
DIRECTNew=简单
#XFLD: status text
COMPLETED=已完成
#XFLD: status text
FAILED=失败
#XFLD: status text
RUNNING=运行中
#XFLD: status text
none=无
#XFLD: status text
realtime=实时
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=子任务
#XFLD: New Data available in the file
NEW_DATA=新数据

#XFLD: text for values shown in column Replication Status
txtOff=禁用
#XFLD: text for values shown in column Replication Status
txtInitializing=正在初始化
#XFLD: text for values shown in column Replication Status
txtLoading=正在加载
#XFLD: text for values shown in column Replication Status
txtActive=活动
#XFLD: text for values shown in column Replication Status
txtAvailable=可用
#XFLD: text for values shown in column Replication Status
txtError=错误
#XFLD: text for values shown in column Replication Status
txtPaused=已暂停
#XFLD: text for values shown in column Replication Status
txtDisconnected=已断开连接
#XFLD: text for partially Persisted views
partiallyPersisted=部分持久化

#XFLD: activity text
REPLICATE=复制
#XFLD: activity text
REMOVE_REPLICATED_DATA=移除复制的数据
#XFLD: activity text
DISABLE_REALTIME=禁用实时数据复制
#XFLD: activity text
REMOVE_PERSISTED_DATA=移除持久数据
#XFLD: activity text
PERSIST=持续
#XFLD: activity text
EXECUTE=执行
#XFLD: activity text
TASKLOG_CLEANUP=Tacklog_Cleanup
#XFLD: activity text
CANCEL_REPLICATION=取消复制
#XFLD: activity text
MODEL_IMPORT=模型导入
#XFLD: activity text
NONE=无
#XFLD: activity text
CANCEL_PERSISTENCY=取消持久化
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=分析视图
#XFLD: activity text
CANCEL_VIEW_ANALYZER=取消视图分析器

#XFLD: severity text
INFORMATION=信息
#XFLD: severity text
SUCCESS=成功
#XFLD: severity text
WARNING=警告
#XFLD: severity text
ERROR=错误
#XFLD: text for values shown for Ascending sort order
SortInAsc=升序排序
#XFLD: text for values shown for Descending sort order
SortInDesc=降序排序
#XFLD: filter text for task log columns
Filter=筛选
#XFLD: object text for task log columns
Object=对象
#XFLD: space text for task log columns
crossSpace=空间

#XBUT: label for remote data access
REMOTE=远程
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=已复制（实时）
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=已复制（快照）

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=由于错误，实时复制已被阻止。更正错误后，可使用“重试”操作继续实时复制。
ERROR_MSG=由于错误，实时复制已被阻止。
RETRY_FAILED_ERROR=重试流程失败，出现错误。
LOG_INFO_DETAILS=在实时模式下复制数据时未生成日志。显示的日志与先前操作相关。

#XBUT: Partitioning label
partitionMenuText=分区
#XBUT: Drop down menu button to create a partition
createPartitionLabel=创建分区
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=编辑分区
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=删除分区
#XFLD: Initial text
InitialPartitionText=通过指定条件将大型数据集划分为较小的数据集来定义分区。
DefinePartition=定义分区
#XFLD: Message text
partitionChangedInfo=分区定义在上次复制后已更改。这些更改将在下次数据加载应用。
#XFLD: Message text
REAL_TIME_WARNING=仅在加载新快照时应用分区。不会应用于实时复制。
#XFLD: Message text
loadSelectedPartitions=已经开始持久化 "{0}" 的选定分区的数据
#XFLD: Message text
loadSelectedPartitionsError=持久化 "{0}" 的选定分区的数据失败
#XFLD: Message text
viewpartitionChangedInfo=分区定义在上次持久化运行后已更改。为应用这些更改，下次数据加载将是包含已锁定分区的完整快照。完整加载完成后，即可运行单个分区的数据。
#XFLD: Message text
viewpartitionChangedInfoLocked=分区定义在上次持久化运行后已更改。为应用这些更改，下次数据加载将是除已锁定和未更改分区范围以外的完整快照。加载完成后，即可再次加载选择的分区。
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=表复制
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=计划复制
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=创建计划
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=编辑计划
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=删除计划
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=加载新快照
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=开始数据复制
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=移除复制的数据
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=启用实时访问
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=启用实时数据复制
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=禁用实时数据复制
#XFLD: Message for replicate table action
replicateTableText=表复制
#XFLD: Message for replicate table action
replicateTableTextNew=数据复制
#XFLD: Message to schedule task
scheduleText=计划
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=视图持久性
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=视图持久化
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=加载新快照
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=开始数据持久化
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=移除持久数据
#XFLD: Partitioned Processign
EnablePartitionedProcessing=已分区处理
#XBUT: Label for scheduled replication
scheduledTxt=已计划
#XBUT: Label for statistics button
statisticsTxt=统计信息
#XBUT: Label for create statistics
createStatsTxt=创建统计信息
#XBUT: Label for edit statistics
editStatsTxt=编辑统计信息
#XBUT: Label for refresh statistics
refreshStatsTxt=刷新统计信息
#XBUT: Label for delete statistics
dropStatsTxt=删除统计信息
#XMSG: Create statistics success message
statsSuccessTxt=已开始为 {1} 创建 {0} 类型的统计信息。
#XMSG: Edit statistics success message
statsEditSuccessTxt=已开始为 {1} 更改统计信息类型为 {0}。
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=已开始为 {0} 刷新统计信息。
#XMSG: Drop statistics success message
statsDropSuccessTxt=已为 {0} 成功删除统计信息
#XMSG: Create statistics error message
statsCreateErrorTxt=创建统计信息时出错
#XMSG: Edit statistics error message
statsEditErrorTxt=更改统计信息时出错
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=刷新统计信息时出错
#XMSG: Drop statistics error message
statsDropErrorTxt=删除统计信息时出错
#XMG: Warning text for deleting statistics
statsDelWarnTxt=是否确定要删除数据统计？
startPersistencyAdvisorLabel=启动视图分析器

#Partition related texts
#XFLD: Label for Column
column=列
#XFLD: Label for No of Partition
noOfPartitions=分区数
#XFLD: Label for Column
noOfParallelProcess=并行流程数
#XFLD: Label text
noOfLockedPartition=已锁定分区数
#XFLD: Label for Partition
PARTITION=分区
#XFLD: Label for Column
AVAILABLE=可用
#XFLD: Statistics Label
statsLabel=统计信息
#XFLD: Label text
COLUMN=列：
#XFLD: Label text
PARALLEL_PROCESSES=并行流程：
#XFLD: Label text
Partition_Range=分区范围
#XFLD: Label text
Name=名称
#XFLD: Label text
Locked=已锁定
#XFLD: Label text
Others=其他
#XFLD: Label text
Delete=删除
#XFLD: Label text
LoadData=加载选定分区
#XFLD: Label text
LoadSelectedData=加载选定分区
#XFLD: Confirmation text
LoadNewPersistenceConfirm=此操作将加载所有已解锁和已更改分区的新快照，而不仅是选定分区的快照。是否继续？
#XFLD: Label text
Continue=继续

#XFLD: Label text
PARTITIONS=分区
#XFLD: Label text
ADD_PARTITIONS=+ 添加分区
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=添加分区
#XFLD: Label text
deleteRange=删除分区
#XFLD: Label text
LOW_PLACE_HOLDER=输入下限值
#XFLD: Label text
HIGH_PLACE_HOLDER=输入上限值
#XFLD: tooltip text
lockedTooltip=初始加载后锁定分区

#XFLD: Button text
Edit=编辑
#XFLD: Button text
CANCEL=取消

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=上次统计更新
#XFLD: Statistics Fields
STATISTICS=统计信息

#XFLD:Retry label
TEXT_Retry=重试
#XFLD:Retry label
TEXT_Retry_tooltip=解决错误后重试实时复制。
#XFLD: text retry
Retry=确认
#XMG: Retry confirmation text
retryConfirmationTxt=最后的实时复制终止，出现错误。\n确认此错误已更正，并可重新开始实时复制。
#XMG: Retry success text
retrySuccess=已成功发起重试流程。
#XMG: Retry fail text
retryFail=重试流程失败。
#XMSG: activity message for create statistics
CREATE_STATISTICS=创建统计信息
#XMSG: activity message for edit statistics
DROP_STATISTICS=删除统计信息
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=刷新统计信息
#XMSG: activity message for edit statistics
ALTER_STATISTICS=编辑统计信息
#XMSG: Task log message started task
taskStarted=任务{0}已开始。
#XMSG: Task log message for finished task
taskFinished=任务{0}已结束，状态为{1}。
#XMSG: Task log message for finished task with end time
taskFinishedAt=任务{0}在{2}结束，状态为：{1}。
#XMSG: Task {0} has input parameters
taskHasInputParameters=任务 {0} 具有输入参数。
#XMSG: Task log message for unexpected error
unexpectedExecutionError=任务{0}已结束，出现意外错误。任务状态已设置为{1}。
#XMSG: Task log message for failed task
failedToEnd=无法设置状态为{0}，或无法解除锁定。
#XMSG: Task log message
lockNotFound=由于缺少锁定，我们无法完成此流程：任务可能已取消。
#XMSG: Task log message failed task
failedOverwrite=任务{0}已被{1}锁定。因此，任务失败，出现以下错误：{2}。
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=此任务的锁定已被其他任务取代。
#XMSG: Task log message failed takeover
failedTakeover=无法接管现有任务。
#XMSG: Task log message successful takeover
successTakeover=必须解除遗留的锁定。已为此任务设置新锁定。
#XMSG: Tasklog Dialog Details
txtDetails=打开远程查询监控器，在分区特定消息的详细信息中，可显示运行期间处理的远程语句。
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=远程 SQL 语句已从数据库中删除，无法显示。
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=连接已分配到其他空间的远程查询无法显示。请转到“远程查询监控器”，使用语句 ID 进行筛选。
#XMSG: Task log message for parallel check error
parallelCheckError=其他任务正在运行，此任务已被阻止，因此无法处理。
#XMSG: Task log message for parallel running task
parallelTaskRunning=已在运行冲突任务。
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=运行期间出现状态{0}，相关性 ID {1}。
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=分配的用户没有执行此任务所需的特许权限。

#XBUT: Label for open in Editor
openInEditor=在编辑器中打开
#XBUT: Label for open in Editor
openInEditorNew=在数据模型构建器中打开
#XFLD:Run deails label
runDetails=运行详细信息
#XFLD: Label for Logs
Logs=日志
#XFLD: Label for Settings
Settings=设置
#XFLD: Label for Save button
Save=保存
#XFLD: Label for Standard
Standard_PO=性能优化（推荐）
#XFLD: Label for Hana low memory processing
HLMP_MO=内存优化
#XFLD: Label for execution mode
ExecutionMode=运行模式
#XFLD: Label for job execution
jobExecution=处理模式
#XFLD: Label for Synchronous
syncExec=同步
#XFLD: Label for Asynchronous
asyncExec=异步
#XFLD: Label for default asynchronous execution
defaultAsyncExec=使用默认值（异步，未来可能更改）
#XMSG: Save settings success
saveSettingsSuccess=SAP HANA 执行模式已更改。
#XMSG: Save settings failure
saveSettingsFailed=SAP HANA 执行模式更改失败。
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=作业执行已更改。
#XMSG: Job Execution change failed
jobExecSettingFailed=作业执行更改失败。
#XMSG: Text for Type
typeTxt=类型
#XMSG: Text for Monitor
monitorTxt=监控器
#XMSG: Text for activity
activityTxt=活动
#XMSG: Text for metrics
metricsTxt=指标
#XTXT: Text for Task chain key
TASK_CHAINS=任务链
#XTXT: Text for View Key
VIEWS=视图
#XTXT: Text for remote table key
REMOTE_TABLES=远程表
#XTXT: Text for Space key
SPACE=空间
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=弹性计算节点
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=复制流
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=智能查找
#XTXT: Text for Local Table
LOCAL_TABLE=本地表
#XTXT: Text for Data flow key
DATA_FLOWS=数据流
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=SQL 脚本程序
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=BW 流程链
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=在监控器中查看
#XTXT: Task List header text
taskListHeader=任务列表（{0}）
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=无法获取数据流历史运行的指标。
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=当前没有在加载完整的运行详细信息。请尝试刷新。
#XFLD: Label text for the Metrices table header
metricesColLabel=运算符标签
#XFLD: Label text for the Metrices table header
metricesType=类型
#XFLD: Label text for the Metrices table header
metricesRecordLabel=记录计数
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=运行任务链
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=任务链运行已开始。
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg={0} 的任务链运行已经开始。
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=无法运行任务链。
#XTXT: Execute button label
runLabel=运行
#XTXT: Execute button label
runLabelNew=开始运行
#XMSG: Filter Object header
chainsFilteredTableHeader=已按对象筛选：{0}
#XFLD: Parent task chain label
parentChainLabel=父任务链：
#XFLD: Parent task chain unauthorized
Unauthorized=没有查看权限
#XFLD: Parent task chain label
parentTaskLabel=父任务：
#XTXT: Task status
NOT_TRIGGERED=未触发
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=进入全屏模式
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=退出全屏模式
#XTXT: Close Task log details right panel
closeRightColumn=关闭此部分
#XTXT: Sort Text
sortTxt=排序
#XTXT: Filter Text
filterTxt=筛选
#XTXT: Filter by text to show list of filters applied
filterByTxt=筛选条件
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=超过 5 分钟
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=超过 15 分钟
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=超过 1 小时
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=过去 1 小时
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=过去 24 小时
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=上个月
#XTXT: Messages title text
messagesText=消息

#XTXT Statistics information message
statisticsInfo=不能为具有数据访问 "已复制" 的远程表创建统计信息。要创建统计信息，请在远程表详细信息监控器中移除复制的数据。
#XFLD
GO_TO_REMOTETABLE_DETAILS=转到远程表详细信息监控器

#XTXT: Repair latest failed run label
retryRunLabel=重试最新运行
#XTXT: Repair failed run label
retryRun=重试运行
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=任务链运行重试已开始
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=任务链运行重试失败
#XMSG: Task chain child elements name
taskChainRetryChildObject=任务 {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=新任务
#XFLD Analyzed View
analyzedView=分析视图
#XFLD Metrics
Metrics=指标
#XFLD Partition Metrics
PartitionMetrics=分区指标
#XFLD Entities
Messages=任务日志
#XTXT: Title Message for empty partition data
partitionEmptyTitle=还没有定义分区
#XTXT: Description message for empty partition data
partitionEmptyDescText=通过指定条件，将较大的数据量拆分为更易管理的较小部分，创建分区。

#XTXT: Title Message for empty runs data
runsEmptyTitle=还没有可用日志
#XTXT: Description message for empty runs data
runsEmptyDescText=启动新活动（加载新快照、启动视图分析器等）时，这里将显示相关日志。


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=没有相应地维护弹性计算节点 {0} 配置。请检查你的定义。
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=添加弹性计算节点 {0} 的流程失败。
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=已经开始在弹性计算节点 {2} 创建并激活源对象 "{0}"."{1}" 的副本。
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=已经开始从弹性计算节点 {2} 移除源对象 "{0}"."{1}" 的副本。
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=创建并激活源对象 "{0}"."{1}" 的副本失败。
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=移除源对象 "{0}"."{1}” 的副本失败。
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=已启动将空间 {0} 的分析查询计算路由到弹性计算节点 {1}。
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=将空间 {0} 的分析查询计算路由到相应的弹性计算节点失败。
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=已启动将空间 {0} 的分析查询计算路由回弹性计算节点 {1}。
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=将空间 {0} 的分析查询计算路由回协调器失败。
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=相应弹性计算节点 {1} 的任务链 {0} 已触发。
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=无法为弹性计算节点 {0} 生成任务链。
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=已开始使用给定的选型计划预配弹性计算节点 {0}：vCPU 数：{1}，内存 (GiB)：{2}，存储空间大小 (GiB)：{3}。
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=已预配弹性计算节点 {0}。
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=已取消预配弹性计算节点 {0}。
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=不允许执行此操作。请停止弹性计算节点 {0} 并重启，以便更新大小调整计划。
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=移除弹性计算节点 {0} 的流程失败。
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=弹性计算节点 {0} 的当前运行超时。
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=正在检查弹性计算节点 {0} 的状态...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=弹性计算节点 {0} 的任务链生成已锁定，因此任务链 {1} 可能仍在运行。
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=已经将源对象 "{0}"."{1}" 复制为视图 "{2}"."{3}" 的依赖项。
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=没能复制表 "{0}"."{1}"，因为复制行表功能已经弃用。
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=超过了每个 SAP HANA Cloud 实例的最大弹性计算节点。
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=弹性计算节点 {0} 的运行操作仍在进行中。
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=由于技术问题，删除源表 {0} 的副本的操作已停止。 请稍后再试。
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=由于技术问题，创建源表 {0} 的副本的操作已停止。 请稍后再试。
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=没能启动弹性计算节点，已经达到使用限制或者还没有分配计算区块时数。
#XMSG: Task log message for chain preparation
chainLoadFromRepository=正在加载任务链并准备运行此链中所含的全部 {0} 项任务。
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=冲突任务已在运行
#XMSG: Replication will change
txt_replication_change=复制类型将更改。
txt_repl_viewdetails=查看详细信息

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=重试最新运行时出错，因为先前的任务运行在生成计划之前就已失败。

#general messages
EXECUTION_ERROR_LOCKED_SPACE=空间 "{0}" 已锁定。

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=活动 {0} 需要部署本地表 {1}。
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=活动 {0} 需要为本地表 {1} 启用增量捕获。
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=活动 {0} 需要本地表 {1} 将数据存储在对象存储中。
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=活动 {0} 需要本地表 {1} 将数据存储在数据库中。

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=开始移除本地表 {0} 中的已删除记录。
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=开始删除本地表 {0} 中的所有记录。
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=开始根据筛选条件 {1} 删除本地表 {0} 中的记录。
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=从本地表 {0} 中移除已删除记录时出错。
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=删除本地表 {0} 中的所有记录时出错。
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=正在删除所有已完全处理并符合以下条件的记录：更改类型为 "已删除" 且时间已经超过 {0} 天。
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=正在删除所有已完全处理并符合以下条件的记录：更改类型为 "已删除" 且时间早于 {0}。
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS=已删除 {0} 条记录。
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS=已将 {0} 条记录标记为需要删除。
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=移除本地表 {0} 中已删除记录的操作已完成。
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=删除本地表 {0} 中所有记录的操作已完成。
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=开始将本地表 {0} 的记录标记为 "已删除"。
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=开始根据筛选条件 {1} 将本地表 {0} 中的记录标记为 "已删除"。
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=将本地表  {0} 中的记录标记为 "已删除" 的操作已完成。

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=正在将数据变更内容临时加载到表 {0} 中。
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=数据变更内容已暂时加载到表 {0} 中。
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=已在表 {0} 中处理并删除数据变更内容。

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=连接详细信息。

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=开始优化本地表（文件）。
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=优化本地表（文件）时出错。
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=出现错误。已停止优化本地表（文件）。
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=正在优化本地表（文件）...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=已优化本地表（文件）。
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=完成本地表（文件）优化。
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=此表通过 Z-Order 列进行了优化：{0}。

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=出现错误。已停止截断本地表（文件）。
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=正在截断本地表（文件）...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=本地表（文件）的入站缓冲区位置已被删除。

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=开始清理（删除所有已完全处理的记录）本地表（文件）。
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=清理本地表（文件）时出错。
#XMSG: Task log message
LTF_VACUUM_STOPPED=出现错误。已停止清理本地表（文件）。
#XMSG: Task log message
LTF_VACUUM_PROCESSING=正在清理本地表（文件）...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=完成清理。
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=正在删除所有已完全处理且时间已经超过 {0} 天的记录。
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=正在删除所有已完全处理且时间早于 {0} 的记录。

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=开始合并新记录与本地表（文件）。
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=开始使用 "自动合并数据" 设置将新记录与本地表（文件）合并。
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=开始合并新记录与本地表（文件）。这项任务由 API 合并请求启动。
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=正在合并新记录与本地表（文件）。
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=合并新记录与本地表（文件）时出错。
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=已合并本地表（文件）。
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=出现错误。已停止合并本地表（文件）。
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=本地表（文件）的合并因出错导致失败，但部分操作已成功执行，一些数据已完成合并。
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=发生超时错误。活动 {0} 已经持续运行 {1} 个小时。
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=系统负载过高，异步执行没能启动。请打开 "系统监控器" 并检查正在运行的任务。
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=异步执行已取消。
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION={0} 任务在 {1} 和 {2} 的内存限制范围内运行。
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=任务 {0} 使用资源 ID {1} 运行。

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=已在本地表（文件）中启动查找和替换。
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=已在本地表（文件）中完成查找和替换。
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=没能在本地表（文件）中进行查找和替换。

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=出现错误。已停止更新本地表（文件）的统计信息。

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=SAP HANA 数据库内存不足，任务失败。
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=SAP HANA 许可控制被拒，任务失败。
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=活动的 SAP HANA 连接过多，任务失败。

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=没能执行重试操作，只支持在嵌套任务链的父级上执行重试。
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=失败的子任务的日志不再可用，无法继续重试。


####Metrics Labels

performanceOptimized=性能优化
memoryOptimized=内存优化

JOB_EXECUTION=作业执行
EXECUTION_MODE=运行模式
NUMBER_OF_RECORDS_OVERALL=持久记录数
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=从远程源中读取的记录数
RUNTIME_MS_REMOTE_EXECUTION_TIME=远程源处理时间
MEMORY_CONSUMPTION_GIB=SAP HANA 峰值内存
NUMBER_OF_PARTITIONS=分区数
MEMORY_CONSUMPTION_GIB_OVERALL=SAP HANA 峰值内存
NUMBER_OF_PARTITIONS_LOCKED=已锁定分区数
PARTITIONING_COLUMN=分区列
HANA_PEAK_CPU_TIME=SAP HANA CPU 使用总时长
USED_IN_DISK=已用存储空间
INPUT_PARAMETER_PARAMETER_VALUE=输入参数
INPUT_PARAMETER=输入参数
ECN_ID=弹性计算节点名称

DAC=数据访问控制
YES=是
NO=否
noofrecords=记录数
partitionpeakmemory=SAP HANA 峰值内存
value=值
metricsTitle=指标（{0}）
partitionmetricsTitle=分区 ({0})
partitionLabel=分区
OthersNotNull=未包含在范围内的值
OthersNull=Null 值
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=上次数据持久化运行使用的设置：
#XMSG: Message for input parameter name
inputParameterLabel=输入参数
#XMSG: Message for input parameter value
inputParameterValueLabel=值
#XMSG: Message for persisted data
inputParameterPersistedLabel=已持久化
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=删除数据
REMOVE_DELETED_RECORDS=移除删除的记录
MERGE_FILES=合并文件
OPTIMIZE_FILES=优化文件
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=SAP BW 网桥监控器中的视图

ANALYZE_PERFORMANCE=分析性能
CANCEL_ANALYZE_PERFORMANCE=取消分析性能

#XFLD: Label for frequency column
everyLabel=每
#XFLD: Plural Recurrence text for Hour
hoursLabel=小时
#XFLD: Plural Recurrence text for Day
daysLabel=天
#XFLD: Plural Recurrence text for Month
monthsLabel=个月
#XFLD: Plural Recurrence text for Minutes
minutesLabel=分钟

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT"> 视图持久性故障排除指南 </a>
#XTXT TEXT for view persistency guide link
OOMMessage=进程内存不足。没能为视图 "{0}" 持久化数据。请访问 SAP Help Portal，获取有关内存不足错误的更多信息。建议使用视图分析器分析视图的内存消耗复杂度。

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=不适用
OPEN_BRACKET=（
CLOSE_BRACKET=）
