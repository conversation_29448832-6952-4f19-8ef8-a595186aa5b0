
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=[[[Ļŏğ Ďēţąįĺş∙∙∙∙∙∙∙∙]]]
#XFLD: Header
TASK_LOGS=[[[Ţąşķ Ļŏğş ({0})]]]

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=[[[Řűŋş ({0})]]]
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=[[[Ʋįēŵ Ďēţąįĺş∙∙∙∙∙∙∙]]]
#XFLD: Button text
STOP=[[[Ŝţŏρ Řűŋ∙∙∙∙∙∙]]]
#XFLD: Label text
RUN_START=[[[Ļąşţ Řűŋ Ŝţąŗţ∙∙∙∙∙]]]
#XFLD: Label text
RUN_END=[[[Ļąşţ Řűŋ Ĕŋƌ∙∙∙∙∙∙∙]]]
#XFLD: Label text
RUNTIME=[[[Ďűŗąţįŏŋ∙∙∙∙∙∙]]]
#XTIT: Count for Messages
txtDetailMessages=[[[Μēşşąğēş ({0})]]]
#XFLD: Label text
TIME=[[[Ţįɱēşţąɱρ∙∙∙∙∙]]]
#XFLD: Label text
MESSAGE=[[[Μēşşąğē∙∙∙∙∙∙∙]]]
#XFLD: Label text
TASK_STATUS=[[[Ĉąţēğŏŗŷ∙∙∙∙∙∙]]]
#XFLD: Label text
TASK_ACTIVITY=[[[Āċţįʋįţŷ∙∙∙∙∙∙]]]
#XFLD: Label text
RUN_START_DETAILS=[[[Ŝţąŗţ∙∙∙∙∙∙∙∙∙]]]
#XFLD: Label text
RUN_END_DETAILS=[[[Ĕŋƌ∙]]]
#XFLD: Label text
LOGS=[[[Řűŋş]]]
#XFLD: Label text
STATUS=[[[Ŝţąţűş∙∙∙∙∙∙∙∙]]]
#XFLD: Label text
RUN_STATUS=[[[Řűŋ Ŝţąţűş∙∙∙∙]]]
#XFLD: Label text
Runtime=[[[Ďűŗąţįŏŋ∙∙∙∙∙∙]]]
#XFLD: Label text
RuntimeTooltip=[[[Ďűŗąţįŏŋ (ĥĥ : ɱɱ : şş)∙∙∙∙∙∙]]]
#XFLD: Label text
TRIGGEREDBY=[[[Ţŗįğğēŗēƌ ƃŷ∙∙∙∙∙∙∙]]]
#XFLD: Label text
TRIGGEREDBYNew=[[[Řűŋ Ɓŷ∙∙∙∙∙∙∙∙]]]
#XFLD: Label text
TRIGGEREDBYNewImp=[[[Řűŋ Ŝţąŗţēƌ Ɓŷ∙∙∙∙∙]]]
#XFLD: Label text
EXECUTIONTYPE=[[[Ĕχēċűţįŏŋ Ţŷρē∙∙∙∙∙]]]
#XFLD: Label text
EXECUTIONTYPENew=[[[Řűŋ Ţŷρē∙∙∙∙∙∙]]]
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=[[[Ƥąŗēŋţ Ĉĥąįŋ Ŝρąċē∙∙∙∙∙∙]]]
#XFLD: Refresh tooltip
TEXT_REFRESH=[[[Řēƒŗēşĥ∙∙∙∙∙∙∙]]]
#XFLD: view Details link
VIEW_ERROR_DETAILS=[[[Ʋįēŵ Ďēţąįĺş∙∙∙∙∙∙∙]]]
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=[[[Ďŏŵŋĺŏąƌ Āƌƌįţįŏŋąĺ Ďēţąįĺş∙∙∙∙∙∙∙∙]]]
#XMSG: Download completed
downloadStarted=[[[Ďŏŵŋĺŏąƌ Ŝţąŗţēƌ∙∙∙∙∙∙∙∙]]]
#XMSG: Error while downloading content
errorInDownload=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ƌŏŵŋĺŏąƌįŋğ.∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=[[[Ʋįēŵ Ďēţąįĺş∙∙∙∙∙∙∙]]]
#XBTN: cancel button of task details dialog
TXT_CANCEL=[[[Ĉąŋċēĺ∙∙∙∙∙∙∙∙]]]
#XBTN: back button from task details
TXT_BACK=[[[Ɓąċķ]]]
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=[[[Ţąşķ Ĉŏɱρĺēţēƌ∙∙∙∙∙]]]
#XFLD: Log message with failed status
MSG_LOG_FAILED=[[[Ţąşķ Ƒąįĺēƌ∙∙∙∙∙∙∙∙]]]
#XFLD: Master and detail table with no data
No_Data=[[[Ńŏ Ďąţą∙∙∙∙∙∙∙]]]
#XFLD: Retry tooltip
TEXT_RETRY=[[[Řēţŗŷ∙∙∙∙∙∙∙∙∙]]]
#XFLD: Cancel Run label
TEXT_CancelRun=[[[Ĉąŋċēĺ Řűŋ∙∙∙∙]]]
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=[[[Ĉĺēąŋűρ Ƒąįĺēƌ Ļŏąƌ∙∙∙∙∙]]]
#XMSG:button copy sql statement
txtSQLStatement=[[[Ĉŏρŷ ŜǬĻ Ŝţąţēɱēŋţ∙∙∙∙∙∙]]]
#XMSG:button open remote query monitor
txtOpenQueryMonitor=[[[Ŏρēŋ Řēɱŏţē Ǭűēŗŷ Μŏŋįţŏŗ∙∙∙∙∙∙∙]]]
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=[[[Ţŏ ƌįşρĺąŷ ţĥē ŗēɱŏţē ŜǬĻ şţąţēɱēŋţş, ċĺįċķ ŏŋ "Ŏρēŋ Řēɱŏţē Ǭűēŗŷ Μŏŋįţŏŗ".∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG:button ok
txtOk=[[[Ŏķ∙∙]]]
#XMSG: button close
txtClose=[[[Ĉĺŏşē∙∙∙∙∙∙∙∙∙]]]
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=[[[Ĉąŋċēĺ ŗűŋ ąċţįŏŋ ƒŏŗ ŏƃĵēċţ "{0}" ĥąş ƃēēŋ şţąŗţēƌ.]]]
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=[[[Ĉąŋċēĺ ŗűŋ ąċţįŏŋ ƒŏŗ ŏƃĵēċţ "{0}" įş ƒąįĺēƌ.]]]
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=[[[Ĉąŋċēĺ ŗűŋ ąċţįŏŋ ƒŏŗ ŏƃĵēċţ "{0}" įş ŋŏ ĺŏŋğēŗ ρŏşşįƃĺē ƃēċąűşē ţĥē ŗēρĺįċąţįŏŋ şţąţűş ĥąş ċĥąŋğēƌ.]]]
#XMSG: Info message for no Running state logId
noTaskWithRunningState=[[[Ńŏ ţąşķĺŏğş ĥąʋē şţąţűş Řűŋŋįŋğ.∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: message for conflicting task
Task_Already_Running=[[[Ā ċŏŋƒĺįċţįŋğ ţąşķ įş ąĺŗēąƌŷ ŗűŋŋįŋğ ƒŏŗ ţĥē ŏƃĵēċţ "{0}".]]]
#XFLD: Label for no task log with running state title
actionInfo=[[[Āċţįŏŋ Ĭŋƒŏ∙∙∙∙∙∙∙∙]]]
#XMSG Copied to clipboard
copiedToClip=[[[Ĉŏρįēƌ ţŏ Ĉĺįρƃŏąŗƌ∙∙∙∙∙]]]
#XFLD copy
Copy=[[[Ĉŏρŷ]]]
#XFLD copy correlation ID
CopyCorrelationID=[[[Ĉŏρŷ Ĉŏŗŗēĺąţįŏŋ ĬĎ∙∙∙∙∙]]]
#XFLD Close
Close=[[[Ĉĺŏşē∙∙∙∙∙∙∙∙∙]]]
#XFLD: show more Label
txtShowMore=[[[Ŝĥŏŵ Μŏŗē∙∙∙∙∙]]]
#XFLD: message Label
messageLabel=[[[Μēşşąğē:∙∙∙∙∙∙]]]
#XFLD: details Label
detailsLabel=[[[Ďēţąįĺş:∙∙∙∙∙∙]]]
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=[[[Ĕχēċűţįŋğ ŜǬĻ \\u0157\\u014B Ŝţąţēɱēŋţ:∙∙∙∙∙∙∙∙∙]]]
#XFLD:statementId Label
statementIdLabel=[[[Ŝţąţēɱēŋţ ĬĎ:∙∙∙∙∙∙]]]
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=[[[Ńűɱƃēŗ ŏƒ Řēɱŏţē \\u0157\\u014B ŜǬĻ Ŝţąţēɱēŋţş:∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=[[[Ńűɱƃēŗ ŏƒ Ŝţąţēɱēŋţş∙∙∙∙]]]
#XFLD: Space Label
txtSpaces=[[[Ŝρąċē∙∙∙∙∙∙∙∙∙]]]
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=[[[Ůŋąűţĥŏŗįžēƌ Ŝρąċēş∙∙∙∙∙]]]
#XFLD: Privilege Error Text
txtPrivilegeError=[[[Ŷŏű ƌŏ ŋŏţ ĥąʋē şűƒƒįċįēŋţ ρŗįʋįĺēğēş ţŏ ʋįēŵ ţĥįş ƌąţą.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Label for Object Header
DATA_ACCESS=[[[Ďąţą Āċċēşş∙∙∙∙∙∙∙∙]]]
#XFLD: Label for Object Header
SCHEDULE=[[[Ŝċĥēƌűĺē∙∙∙∙∙∙]]]
#XFLD: Label for Object Header
DETAILS=[[[Ďēţąįĺş∙∙∙∙∙∙∙]]]
#XFLD: Label for Object Header
LATEST_UPDATE=[[[Ļąţēşţ Ůρƌąţē∙∙∙∙∙∙]]]
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=[[[Ļąţēşţ Ĉĥąŋğē (Ŝŏűŗċē)∙∙∙∙∙]]]
#XFLD: Label for Object Header
REFRESH_FREQUENCY=[[[Řēƒŗēşĥ Ƒŗēƣűēŋċŷ∙∙∙∙∙∙∙]]]
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=[[[Ŝċĥēƌűĺēƌ Ƒŗēƣűēŋċŷ∙∙∙∙∙]]]
#XFLD: Label for Object Header
NEXT_RUN=[[[Ńēχţ Řűŋ∙∙∙∙∙∙]]]
#XFLD: Label for Object Header
CONNECTION=[[[Ĉŏŋŋēċţįŏŋ∙∙∙∙]]]
#XFLD: Label for Object Header
DP_AGENT=[[[ĎƤ Āğēŋţ∙∙∙∙∙∙]]]
#XFLD: Label for Object Header
USED_IN_MEMORY=[[[Μēɱŏŗŷ Ůşēƌ ƒŏŗ Ŝţŏŗąğē (ΜįƁ)∙∙∙∙∙∙∙∙∙]]]
#XFLD: Label for Object Header
USED_DISK=[[[Ďįşķ Ůşēƌ ƒŏŗ Ŝţŏŗąğē (ΜįƁ)∙∙∙∙∙∙∙∙]]]
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=[[[Ŝįžē įŋ-Μēɱŏŗŷ (ΜįƁ)∙∙∙∙]]]
#XFLD: Label for Object Header
USED_DISK_NEW=[[[Ŝįžē ŏŋ Ďįşķ (ΜįƁ)∙∙∙∙∙∙]]]
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=[[[Ńűɱƃēŗ ŏƒ Řēċŏŗƌş∙∙∙∙∙∙∙]]]

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=[[[Ŝēţ Ţŏ Ƒąįĺēƌ∙∙∙∙∙∙]]]
SET_TO_FAILED_ERR=[[[Ţĥįş ţąşķ ŵąş ŗűŋŋįŋğ ƃűţ ţĥē űşēŗ şēţ ţĥįş ţąşķ`ş şţąţűş ţŏ ƑĀĬĻĔĎ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Label for stopped failed
FAILLOCKED=[[[Řűŋ Āĺŗēąƌŷ įŋ Ƥŗŏğŗēşş∙∙∙∙∙∙]]]
#XFLD: sub status STOPPED
STOPPED=[[[Ŝţŏρρēƌ∙∙∙∙∙∙∙]]]
STOPPED_ERR=[[[Ţĥįş ţąşķ ŵąş şţŏρρēƌ, ƃűţ ŋŏ ŗŏĺĺƃąċķ ŵąş ρēŗƒŏŗɱēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: sub status CANCELLED
CANCELLED=[[[Ĉąŋċēĺēƌ∙∙∙∙∙∙]]]
CANCELLED_ERR=[[[Ţĥįş ţąşķ ŗűŋ ŵąş ċąŋċēĺēƌ, ąƒţēŗ įţ ĥąƌ şţąŗţēƌ. Ĭŋ ţĥįş ċąşē, ţĥē ƌąţą ŵąş ŗŏĺĺēƌ ƃąċķ ąŋƌ ŗēşţŏŗēƌ ţŏ ţĥē şţąţē ţĥąţ ēχįşţēƌ ƃēƒŏŗē ţĥē ţąşķ ŗűŋ ŵąş įŋįţįąĺĺŷ ţŗįğğēŗēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: sub status LOCKED
LOCKED=[[[Ļŏċķēƌ∙∙∙∙∙∙∙∙]]]
LOCKED_ERR=[[[Ţĥē şąɱē ţąşķ ŵąş ąĺŗēąƌŷ ŗűŋŋįŋğ, şŏ ţĥįş ţąşķ ĵŏƃ ċąŋŋŏţ ƃē ŗűŋ įŋ ρąŗąĺĺēĺ ŵįţĥ ąŋ ēχįşţįŋğ ţąşķ`ş ēχēċűţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=[[[Ţąşķ Ĕχċēρţįŏŋ∙∙∙∙∙]]]
TASK_EXCEPTION_ERR=[[[Ţĥįş ţąşķ ēŋċŏűŋţēŗēƌ ąŋ űŋşρēċįƒįēƌ ēŗŗŏŗ ƌűŗįŋğ ēχēċűţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=[[[Ţąşķ Ĕχēċűţē Ĕχċēρţįŏŋ∙∙∙∙∙]]]
TASK_EXECUTE_EXCEPTION_ERR=[[[Ţĥįş ţąşķ ēŋċŏűŋţēŗēƌ ąŋ ēŗŗŏŗ ƌűŗįŋğ ēχēċűţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=[[[Ůŋąűţĥŏŗįžēƌ∙∙∙∙∙∙∙]]]
UNAUTHORIZED_ERR=[[[Ţĥē űşēŗ ċŏűĺƌ ŋŏţ ƃē ąűţĥēŋţįċąţēƌ, ĥąş ƃēēŋ ĺŏċķēƌ, ŏŗ ƌēĺēţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: sub status FORBIDDEN
FORBIDDEN=[[[Ƒŏŗƃįƌƌēŋ∙∙∙∙∙]]]
FORBIDDEN_ERR=[[[Ţĥē ąşşįğŋēƌ űşēŗ ƌŏēş ŋŏţ ĥąʋē ţĥē ρŗįʋįĺēğēş ŋēċēşşąŗŷ ţŏ ēχēċűţē ţĥįş ţąşķ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=[[[Ńŏţ Ţŗįğğēŗēƌ∙∙∙∙∙∙]]]
FAIL_NOT_TRIGGERED_ERR=[[[Ţĥįş ţąşķ ĵŏƃ ċŏűĺƌ ŋŏţ ƃē ēχēċűţēƌ ƌűē ţŏ ą şŷşţēɱ ŏűţąğē ŏŗ şŏɱē ρąŗţ ŏƒ ţĥē ƌąţąƃąşē şŷşţēɱ ŋŏţ ƃēįŋğ ąʋąįĺąƃĺē ąţ ţĥē ţįɱē ŏƒ ţĥē ρĺąŋŋēƌ ēχēċűţįŏŋ. Ŵąįţ ƒŏŗ ţĥē ŋēχţ şċĥēƌűĺēƌ ĵŏƃ ēχēċűţįŏŋ ţįɱē ŏŗ ŗēşċĥēƌűĺē ţĥē ĵŏƃ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=[[[Ŝċĥēƌűĺē Ĉąŋċēĺēƌ∙∙∙∙∙∙∙]]]
SCHEDULE_CANCELLED_ERR=[[[Ţĥįş ţąşķ ĵŏƃ ċŏűĺƌ ŋŏţ ƃē ēχēċűţēƌ ƌűē ţŏ ąŋ įŋţēŗŋąĺ ēŗŗŏŗ. Ĉŏŋţąċţ ŜĀƤ Ŝűρρŏŗţ ąŋƌ ρŗŏʋįƌē ţĥēɱ ŵįţĥ ţĥē ċŏŗŗēĺąţįŏŋ įƌ ąŋƌ ţįɱēşţąɱρ ƒŗŏɱ ţĥįş ţąşķ ĵŏƃ`ş ĺŏğ ƌēţąįĺ įŋƒŏŗɱąţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=[[[Ƥŗēʋįŏűş Řűŋ įŋ Ƥŗŏğŗēşş∙∙∙∙∙∙]]]
SUCCESS_SKIPPED_ERR=[[[Ţĥįş ţąşķ`ş ēχēċűţįŏŋ ĥąş ŋŏţ ƃēēŋ ţŗįğğēŗēƌ ƃēċąűşē ą ρŗēʋįŏűş ŗűŋ ŏƒ ţĥē şąɱē ţąşķ įş şţįĺĺ įŋ ρŗŏğŗēşş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=[[[Ŏŵŋēŗ Μįşşįŋğ∙∙∙∙∙∙]]]
FAIL_OWNER_MISSING_ERR=[[[Ţĥįş ţąşķ ĵŏƃ ċŏűĺƌ ŋŏţ ƃē ēχēċűţēƌ ƃēċąűşē įţ ƌŏēş ŋŏţ ĥąʋē ąŋ ąşşįğŋēƌ şŷşţēɱ űşēŗ. Āşşįğŋ ąŋ ŏŵŋēŗ űşēŗ ţŏ ţĥē ĵŏƃ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=[[[Ĉŏŋşēŋţ Ńŏţ Āʋąįĺąƃĺē∙∙∙∙∙]]]
FAIL_CONSENT_NOT_AVAILABLE_ERR=[[[Ŷŏű ĥąʋē ŋŏţ ąűţĥŏŗįžēƌ ŜĀƤ ţŏ ŗűŋ ţąşķ ċĥąįŋş ŏŗ şċĥēƌűĺē ƌąţą įŋţēğŗąţįŏŋ ţąşķş ŏŋ ŷŏűŗ ƃēĥąĺƒ. Ŝēĺēċţ ţĥē ŏρţįŏŋ ρŗŏʋįƌēƌ ţŏ ğįʋē ŷŏűŗ ċŏŋşēŋţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=[[[Ĉŏŋşēŋţ Ĕχρįŗēƌ∙∙∙∙]]]
FAIL_CONSENT_EXPIRED_ERR=[[[Ţĥē ąűţĥŏŗįžąţįŏŋ ţĥąţ ąĺĺŏŵş ŜĀƤ ţŏ ŗűŋ ţąşķ ċĥąįŋş ŏŗ şċĥēƌűĺē ƌąţą įŋţēğŗąţįŏŋ ţąşķş ŏŋ ŷŏűŗ ƃēĥąĺƒ ĥąş ēχρįŗēƌ. Ŝēĺēċţ ţĥē ŏρţįŏŋ ρŗŏʋįƌēƌ ţŏ ŗēŋēŵ ŷŏűŗ ċŏŋşēŋţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=[[[Ĉŏŋşēŋţ Ĭŋʋąĺįƌąţēƌ∙∙∙∙∙]]]
FAIL_CONSENT_INVALIDATED_ERR=[[[Ţĥįş ţąşķ ċŏűĺƌ ŋŏţ ƃē ēχēċűţēƌ, ţŷρįċąĺĺŷ ƌűē ţŏ ą ċĥąŋğē įŋ ţĥē Ĭƌēŋţįţŷ Ƥŗŏʋįƌēŗ ċŏŋƒįğűŗąţįŏŋ ŏƒ ţĥē ţēŋąŋţ. Ĭŋ ţĥąţ ċąşē, ŋŏ ŋēŵ ţąşķ ĵŏƃş ċąŋ ƃē ŗűŋ ŏŗ şċĥēƌűĺēƌ įŋ ţĥē ŋąɱē ŏƒ ţĥē ąƒƒēċţēƌ űşēŗ. Ĭƒ ţĥē ąşşįğŋēƌ űşēŗ şţįĺĺ ēχįşţş įŋ ţĥē ŋēŵ ĬƌƤ, ŗēʋŏķē ţĥē şċĥēƌűĺįŋğ ċŏŋşēŋţ ąŋƌ ţĥēŋ ğŗąŋţ įţ ąğąįŋ. Ĭƒ ţĥē ąşşįğŋēƌ űşēŗ ŋŏ ĺŏŋğēŗ ēχįşţş, ąşşįğŋ ą ŋēŵ ţąşķ ĵŏƃ ŏŵŋēŗ ąŋƌ ρŗŏʋįƌē ţĥē ŗēƣűįŗēƌ ţąşķ şċĥēƌűĺįŋğ ċŏŋşēŋţ. Ŝēē ţĥē ƒŏĺĺŏŵįŋğ ŜĀƤ ŋŏţē: ĥţţρş://ĺąűŋċĥρąƌ.şűρρŏŗţ.şąρ.ċŏɱ/#/ŋŏţēş/3089828 ƒŏŗ ɱŏŗē įŋƒŏŗɱąţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
TASK_EXECUTOR_ERROR=[[[Ţąşķ Ĕχēċűţŏŗ∙∙∙∙∙∙]]]
TASK_EXECUTOR_ERROR_ERR=[[[Ţĥįş ţąşķ ēŋċŏűŋţēŗēƌ ąŋ įŋţēŗŋąĺ ēŗŗŏŗ, ĺįķēĺŷ ƌűŗįŋğ ρŗēρąŗąţįŏŋ şţēρş ƒŏŗ ēχēċűţįŏŋ, ąŋƌ ţĥē ţąşķ ċŏűĺƌ ŋŏţ ƃē şţąŗţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
PREREQ_NOT_MET=[[[Ƥŗēŗēƣűįşįţē ŋŏţ ɱēţ∙∙∙∙]]]
PREREQ_NOT_MET_ERR=[[[Ţĥįş ţąşķ ċŏűĺƌ ŋŏţ ƃē ŗűŋ ƃēċąűşē ŏƒ įşşűēş įŋ įţş ƌēƒįŋįţįŏŋ. Ƒŏŗ ēχąɱρĺē, ţĥē ŏƃĵēċţ įş ŋŏţ ƌēρĺŏŷēƌ, ą ţąşķ ċĥąįŋ ċŏŋţąįŋş ċįŗċűĺąŗ ĺŏğįċ, ŏŗ ą ʋįēŵ’ş ŜǬĻ įş įŋʋąĺįƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
RESOURCE_LIMIT_ERROR=[[[Řēşŏűŗċē Ļįɱįţ Ĕŗŗŏŗ∙∙∙∙]]]
RESOURCE_LIMIT_ERROR_ERR=[[[Ĉűŗŗēŋţĺŷ űŋąƃĺē ţŏ ρēŗƒŏŗɱ ţąşķ ƃēċąűşē şűƒƒįċįēŋţ ŗēşŏűŗċēş ŵēŗē ŋŏţ ąʋąįĺąƃĺē ŏŗ ŵēŗē ƃűşŷ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
FAIL_CONSENT_REFUSED_BY_UMS=[[[Ĉŏŋşēŋţ Řēƒűşēƌ∙∙∙∙]]]
FAIL_CONSENT_REFUSED_BY_UMS_ERR=[[[Ţĥįş ţąşķ ċŏűĺƌ ŋŏţ ƃē ēχēċűţēƌ, įŋ şċĥēƌűĺēƌ ŗűŋş ŏŗ ţąşķ ċĥąįŋş, ƃēċąűşē ŏƒ ą ċĥąŋğē įŋ ą űşēŗ’ş Ĭƌēŋţįţŷ Ƥŗŏʋįƌēŗ ċŏŋƒįğűŗąţįŏŋ ŏŋ ţĥē ţēŋąŋţ. Ƒŏŗ ɱŏŗē įŋƒŏŗɱąţįŏŋ, şēē ţĥē ƒŏĺĺŏŵįŋğ ŜĀƤ ŋŏţē: ĥţţρş://ĺąűŋċĥρąƌ.şűρρŏŗţ.şąρ.ċŏɱ/#/ŋŏţēş/3120806.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: status text
SCHEDULED=[[[Ŝċĥēƌűĺēƌ∙∙∙∙∙]]]
#XFLD: status text
SCHEDULEDNew=[[[Ƥēŗɱąŋēŋţ∙∙∙∙∙]]]
#XFLD: status text
PAUSED=[[[Ƥąűşēƌ∙∙∙∙∙∙∙∙]]]
#XFLD: status text
DIRECT=[[[Ďįŗēċţ∙∙∙∙∙∙∙∙]]]
#XFLD: status text
MANUAL=[[[Μąŋűąĺ∙∙∙∙∙∙∙∙]]]
#XFLD: status text
DIRECTNew=[[[Ŝįɱρĺē∙∙∙∙∙∙∙∙]]]
#XFLD: status text
COMPLETED=[[[Ĉŏɱρĺēţēƌ∙∙∙∙∙]]]
#XFLD: status text
FAILED=[[[Ƒąįĺēƌ∙∙∙∙∙∙∙∙]]]
#XFLD: status text
RUNNING=[[[Řűŋŋįŋğ∙∙∙∙∙∙∙]]]
#XFLD: status text
none=[[[Ńŏŋē]]]
#XFLD: status text
realtime=[[[Řēąĺ-Ţįɱē∙∙∙∙∙]]]
#XFLD: placeholder for empty cell
emptyCell=[[[---∙]]]
#XFLD: sub task
SUB_TASK=[[[Ŝűƃ-Ţąşķ∙∙∙∙∙∙]]]
#XFLD: New Data available in the file
NEW_DATA=[[[Ńēŵ Ďąţą∙∙∙∙∙∙]]]

#XFLD: text for values shown in column Replication Status
txtOff=[[[Ŏƒƒ∙]]]
#XFLD: text for values shown in column Replication Status
txtInitializing=[[[Ĭŋįţįąĺįžįŋğ∙∙∙∙∙∙∙]]]
#XFLD: text for values shown in column Replication Status
txtLoading=[[[Ļŏąƌįŋğ∙∙∙∙∙∙∙]]]
#XFLD: text for values shown in column Replication Status
txtActive=[[[Āċţįʋē∙∙∙∙∙∙∙∙]]]
#XFLD: text for values shown in column Replication Status
txtAvailable=[[[Āʋąįĺąƃĺē∙∙∙∙∙]]]
#XFLD: text for values shown in column Replication Status
txtError=[[[Ĕŗŗŏŗ∙∙∙∙∙∙∙∙∙]]]
#XFLD: text for values shown in column Replication Status
txtPaused=[[[Ƥąűşēƌ∙∙∙∙∙∙∙∙]]]
#XFLD: text for values shown in column Replication Status
txtDisconnected=[[[Ďįşċŏŋŋēċţēƌ∙∙∙∙∙∙∙]]]
#XFLD: text for partially Persisted views
partiallyPersisted=[[[Ƥąŗţįąĺĺŷ Ƥēŗşįşţēƌ∙∙∙∙∙]]]

#XFLD: activity text
REPLICATE=[[[Řēρĺįċąţē∙∙∙∙∙]]]
#XFLD: activity text
REMOVE_REPLICATED_DATA=[[[Řēɱŏʋē Řēρĺįċąţēƌ Ďąţą∙∙∙∙∙]]]
#XFLD: activity text
DISABLE_REALTIME=[[[Ďįşąƃĺē Řēąĺ-Ţįɱē Ďąţą Řēρĺįċąţįŏŋ∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: activity text
REMOVE_PERSISTED_DATA=[[[Řēɱŏʋē Ƥēŗşįşţēƌ Ďąţą∙∙∙∙∙]]]
#XFLD: activity text
PERSIST=[[[Ƥēŗşįşţ∙∙∙∙∙∙∙]]]
#XFLD: activity text
EXECUTE=[[[Ĕχēċűţē∙∙∙∙∙∙∙]]]
#XFLD: activity text
TASKLOG_CLEANUP=[[[Ţąċķĺŏğ_Ĉĺēąŋűρ∙∙∙∙]]]
#XFLD: activity text
CANCEL_REPLICATION=[[[Ĉąŋċēĺ Řēρĺįċąţįŏŋ∙∙∙∙∙∙]]]
#XFLD: activity text
MODEL_IMPORT=[[[Μŏƌēĺ Ĭɱρŏŗţ∙∙∙∙∙∙∙]]]
#XFLD: activity text
NONE=[[[Ńŏŋē]]]
#XFLD: activity text
CANCEL_PERSISTENCY=[[[Ĉąŋċēĺ Ƥēŗşįşţēŋċŷ∙∙∙∙∙∙]]]
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=[[[Āŋąĺŷžē Ʋįēŵ∙∙∙∙∙∙∙]]]
#XFLD: activity text
CANCEL_VIEW_ANALYZER=[[[Ĉąŋċēĺ Ʋįēŵ Āŋąĺŷžēŗ∙∙∙∙]]]

#XFLD: severity text
INFORMATION=[[[Ĭŋƒŏŗɱąţįŏŋ∙∙∙∙∙∙∙∙]]]
#XFLD: severity text
SUCCESS=[[[Ŝűċċēşş∙∙∙∙∙∙∙]]]
#XFLD: severity text
WARNING=[[[Ŵąŗŋįŋğ∙∙∙∙∙∙∙]]]
#XFLD: severity text
ERROR=[[[Ĕŗŗŏŗ∙∙∙∙∙∙∙∙∙]]]
#XFLD: text for values shown for Ascending sort order
SortInAsc=[[[Ŝŏŗţ Āşċēŋƌįŋğ∙∙∙∙∙]]]
#XFLD: text for values shown for Descending sort order
SortInDesc=[[[Ŝŏŗţ Ďēşċēŋƌįŋğ∙∙∙∙]]]
#XFLD: filter text for task log columns
Filter=[[[Ƒįĺţēŗ∙∙∙∙∙∙∙∙]]]
#XFLD: object text for task log columns
Object=[[[Ŏƃĵēċţ∙∙∙∙∙∙∙∙]]]
#XFLD: space text for task log columns
crossSpace=[[[Ŝρąċē∙∙∙∙∙∙∙∙∙]]]

#XBUT: label for remote data access
REMOTE=[[[Řēɱŏţē∙∙∙∙∙∙∙∙]]]
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=[[[Řēρĺįċąţēƌ (Řēąĺ-Ţįɱē)∙∙∙∙∙]]]
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=[[[Řēρĺįċąţēƌ (Ŝŋąρşĥŏţ)∙∙∙∙∙]]]

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=[[[Řēąĺ-ţįɱē ŗēρĺįċąţįŏŋ įş ƃĺŏċķēƌ ƃēċąűşē ŏƒ ąŋ ēŗŗŏŗ. Ŏŋċē ţĥē ēŗŗŏŗ įş ċŏŗŗēċţēƌ, ŷŏű ċąŋ űşē ţĥē “Řēţŗŷ” ąċţįŏŋ ţŏ ċŏŋţįŋűē ŵįţĥ ŗēąĺ-ţįɱē ŗēρĺįċąţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
ERROR_MSG=[[[Řēąĺ-ţįɱē ŗēρĺįċąţįŏŋ įş ƃĺŏċķēƌ ƃēċąűşē ŏƒ ąŋ ēŗŗŏŗ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
RETRY_FAILED_ERROR=[[[Řēţŗŷ ρŗŏċēşş ƒąįĺēƌ ŵįţĥ ąŋ ēŗŗŏŗ.∙∙∙∙∙∙∙∙∙∙∙∙]]]
LOG_INFO_DETAILS=[[[Ńŏ ĺŏğş ąŗē ğēŋēŗąţēƌ ŵĥēŋ ƌąţą įş ŗēρĺįċąţēƌ įŋ ŗēąĺ-ţįɱē ɱŏƌē. Ţĥē ƌįşρĺąŷēƌ ĺŏğş ŗēĺąţē ţŏ ρŗēʋįŏűş ąċţįŏŋş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XBUT: Partitioning label
partitionMenuText=[[[Ƥąŗţįţįŏŋįŋğ∙∙∙∙∙∙∙]]]
#XBUT: Drop down menu button to create a partition
createPartitionLabel=[[[Ĉŗēąţē Ƥąŗţįţįŏŋ∙∙∙∙∙∙∙∙]]]
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=[[[Ĕƌįţ Ƥąŗţįţįŏŋ∙∙∙∙∙]]]
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=[[[Ďēĺēţē Ƥąŗţįţįŏŋ∙∙∙∙∙∙∙∙]]]
#XFLD: Initial text
InitialPartitionText=[[[Ďēƒįŋē ρąŗţįţįŏŋş ƃŷ şρēċįƒŷįŋğ ċŗįţēŗįą ţŏ ƌįʋįƌē ĺąŗğē ƌąţąşēţş ţŏ şɱąĺĺēŗ şēţş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
DefinePartition=[[[Ďēƒįŋē Ƥąŗţįţįŏŋş∙∙∙∙∙∙∙]]]
#XFLD: Message text
partitionChangedInfo=[[[Ţĥē ρąŗţįţįŏŋ ƌēƒįŋįţįŏŋ ĥąş ċĥąŋğēƌ şįŋċē ţĥē ĺąşţ ŗēρĺįċąţįŏŋ. Ĉĥąŋğēş ŵįĺĺ ƃē ąρρĺįēƌ ŏŋ ţĥē ŋēχţ ƌąţą ĺŏąƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Message text
REAL_TIME_WARNING=[[[Ƥąŗţįţįŏŋįŋğ įş ŏŋĺŷ ąρρĺįēƌ ŵĥēŋ ĺŏąƌįŋğ ą ŋēŵ şŋąρşĥŏţ. Ĭţ įş ŋŏţ ąρρĺįēƌ ƒŏŗ ŗēąĺ-ţįɱē ŗēρĺįċąţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Message text
loadSelectedPartitions=[[[Ŝţąŗţēƌ ρēŗşįşţįŋğ ƌąţą ƒŏŗ ţĥē şēĺēċţēƌ ρąŗţįţįŏŋş ŏƒ "{0}"]]]
#XFLD: Message text
loadSelectedPartitionsError=[[[Ƒąįĺēƌ ţŏ ρēŗşįşţ ƌąţą ƒŏŗ ţĥē şēĺēċţēƌ ρąŗţįţįŏŋş ŏƒ "{0}"]]]
#XFLD: Message text
viewpartitionChangedInfo=[[[Ƥąŗţįţįŏŋ ƌēƒįŋįţįŏŋ ĥąş ċĥąŋğēƌ şįŋċē ţĥē ĺąşţ ρēŗşįşţēŋċŷ ŗűŋ. Ţŏ ąρρĺŷ ţĥē ċĥąŋğēş, ţĥē ŋēχţ ƌąţą ĺŏąƌ ŵįĺĺ ƃē ą ƒűĺĺ şŋąρşĥŏţ įŋċĺűƌįŋğ ĺŏċķēƌ ρąŗţįţįŏŋş. Ŏŋċē ţĥįş ƒűĺĺ ĺŏąƌ įş ƒįŋįşĥēƌ, ŷŏű ŵįĺĺ ƃē ąƃĺē ţŏ ŗűŋ ƌąţą ƒŏŗ şįŋğĺē ρąŗţįţįŏŋş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Message text
viewpartitionChangedInfoLocked=[[[Ƥąŗţįţįŏŋ ƌēƒįŋįţįŏŋ ĥąş ċĥąŋğēƌ şįŋċē ţĥē ĺąşţ ρēŗşįşţēŋċŷ ŗűŋ. Ţŏ ąρρĺŷ ţĥē ċĥąŋğēş, ţĥē ŋēχţ ƌąţą ĺŏąƌ ŵįĺĺ ƃē ą ƒűĺĺ şŋąρşĥŏţ, ēχċēρţ ƒŏŗ ĺŏċķēƌ ąŋƌ űŋċĥąŋğēƌ ρąŗţįţįŏŋ ŗąŋğēş. Ŏŋċē ţĥįş ĺŏąƌ įş ƒįŋįşĥēƌ, ŷŏű ŵįĺĺ ƃē ąƃĺē ţŏ Ļŏąƌ Ŝēĺēċţēƌ Ƥąŗţįţįŏŋş ąğąįŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=[[[Ţąƃĺē Řēρĺįċąţįŏŋ∙∙∙∙∙∙∙]]]
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=[[[Ŝċĥēƌűĺē Řēρĺįċąţįŏŋ∙∙∙∙]]]
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=[[[Ĉŗēąţē Ŝċĥēƌűĺē∙∙∙∙]]]
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=[[[Ĕƌįţ Ŝċĥēƌűĺē∙∙∙∙∙∙]]]
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=[[[Ďēĺēţē Ŝċĥēƌűĺē∙∙∙∙]]]
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=[[[Ļŏąƌ Ńēŵ Ŝŋąρşĥŏţ∙∙∙∙∙∙∙]]]
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=[[[Ŝţąŗţ Ďąţą Řēρĺįċąţįŏŋ∙∙∙∙∙]]]
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=[[[Řēɱŏʋē Řēρĺįċąţēƌ Ďąţą∙∙∙∙∙]]]
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=[[[Ĕŋąƃĺē Řēąĺ-Ţįɱē Āċċēşş∙∙∙∙∙∙]]]
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=[[[Ĕŋąƃĺē Řēąĺ-Ţįɱē Ďąţą Řēρĺįċąţįŏŋ∙∙∙∙∙∙∙∙∙∙∙]]]
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=[[[Ďįşąƃĺē Řēąĺ-Ţįɱē Ďąţą Řēρĺįċąţįŏŋ∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Message for replicate table action
replicateTableText=[[[Ţąƃĺē Řēρĺįċąţįŏŋ∙∙∙∙∙∙∙]]]
#XFLD: Message for replicate table action
replicateTableTextNew=[[[Ďąţą Řēρĺįċąţįŏŋ∙∙∙∙∙∙∙∙]]]
#XFLD: Message to schedule task
scheduleText=[[[Ŝċĥēƌűĺē∙∙∙∙∙∙]]]
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=[[[Ʋįēŵ Ƥēŗşįşţēŋċŷ∙∙∙∙∙∙∙∙]]]
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=[[[Ďąţą Ƥēŗşįşţēŋċē∙∙∙∙∙∙∙∙]]]
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=[[[Ļŏąƌ Ńēŵ Ŝŋąρşĥŏţ∙∙∙∙∙∙∙]]]
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=[[[Ŝţąŗţ Ďąţą Ƥēŗşįşţēŋċē∙∙∙∙∙]]]
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=[[[Řēɱŏʋē Ƥēŗşįşţēƌ Ďąţą∙∙∙∙∙]]]
#XFLD: Partitioned Processign
EnablePartitionedProcessing=[[[Ƥąŗţįţįŏŋēƌ Ƥŗŏċēşşįŋğ∙∙∙∙∙]]]
#XBUT: Label for scheduled replication
scheduledTxt=[[[Ŝċĥēƌűĺēƌ∙∙∙∙∙]]]
#XBUT: Label for statistics button
statisticsTxt=[[[Ŝţąţįşţįċş∙∙∙∙]]]
#XBUT: Label for create statistics
createStatsTxt=[[[Ĉŗēąţē Ŝţąţįşţįċş∙∙∙∙∙∙∙]]]
#XBUT: Label for edit statistics
editStatsTxt=[[[Ĕƌįţ Ŝţąţįşţįċş∙∙∙∙]]]
#XBUT: Label for refresh statistics
refreshStatsTxt=[[[Řēƒŗēşĥ Ŝţąţįşţįċş∙∙∙∙∙∙]]]
#XBUT: Label for delete statistics
dropStatsTxt=[[[Ďēĺēţē Ŝţąţįşţįċş∙∙∙∙∙∙∙]]]
#XMSG: Create statistics success message
statsSuccessTxt=[[[Ŝţąŗţēƌ ċŗēąţįŋğ şţąţįşţįċş ŏƒ ţŷρē {0} ƒŏŗ {1}.]]]
#XMSG: Edit statistics success message
statsEditSuccessTxt=[[[Ŝţąŗţēƌ ċĥąŋğįŋğ şţąţįşţįċş ţŷρē ţŏ {0} ƒŏŗ {1}.]]]
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=[[[Ŝţąŗţēƌ ŗēƒŗēşĥįŋğ şţąţįşţįċş ƒŏŗ {0}.]]]
#XMSG: Drop statistics success message
statsDropSuccessTxt=[[[Ŝţąţįşţįċş şűċċēşşƒűĺĺŷ ƌēĺēţēƌ ƒŏŗ {0}]]]
#XMSG: Create statistics error message
statsCreateErrorTxt=[[[Ĕŗŗŏŗ ŵĥēŋ ċŗēąţįŋğ şţąţįşţįċş∙∙∙∙∙∙∙∙∙]]]
#XMSG: Edit statistics error message
statsEditErrorTxt=[[[Ĕŗŗŏŗ ŵĥēŋ ċĥąŋğįŋğ şţąţįşţįċş∙∙∙∙∙∙∙∙∙]]]
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=[[[Ĕŗŗŏŗ ŵĥēŋ ŗēƒŗēşĥįŋğ şţąţįşţįċş∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Drop statistics error message
statsDropErrorTxt=[[[Ĕŗŗŏŗ ŵĥēŋ ƌēĺēţįŋğ Ŝţąţįşţįċş∙∙∙∙∙∙∙∙∙]]]
#XMG: Warning text for deleting statistics
statsDelWarnTxt=[[[Āŗē ŷŏű şűŗē ŷŏű ŵąŋţ ţŏ ƌŗŏρ ţĥē ƌąţą şţąţįşţįċş?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
startPersistencyAdvisorLabel=[[[Ŝţąŗţ Ʋįēŵ Āŋąĺŷžēŗ∙∙∙∙∙]]]

#Partition related texts
#XFLD: Label for Column
column=[[[Ĉŏĺűɱŋ∙∙∙∙∙∙∙∙]]]
#XFLD: Label for No of Partition
noOfPartitions=[[[Ńŏ. ŏƒ Ƥąŗţįţįŏŋş∙∙∙∙∙∙∙]]]
#XFLD: Label for Column
noOfParallelProcess=[[[Ńŏ. ŏƒ Ƥąŗąĺĺēĺ Ƥŗŏċēşşēş∙∙∙∙∙∙∙]]]
#XFLD: Label text
noOfLockedPartition=[[[Ńŏ. ŏƒ Ļŏċķēƌ Ƥąŗţįţįŏŋş∙∙∙∙∙∙]]]
#XFLD: Label for Partition
PARTITION=[[[Ƥąŗţįţįŏŋş∙∙∙∙]]]
#XFLD: Label for Column
AVAILABLE=[[[Āʋąįĺąƃĺē∙∙∙∙∙]]]
#XFLD: Statistics Label
statsLabel=[[[Ŝţąţįşţįċş∙∙∙∙]]]
#XFLD: Label text
COLUMN=[[[Ĉŏĺűɱŋ:∙∙∙∙∙∙∙]]]
#XFLD: Label text
PARALLEL_PROCESSES=[[[Ƥąŗąĺĺēĺ Ƥŗŏċēşşēş:∙∙∙∙∙]]]
#XFLD: Label text
Partition_Range=[[[Ƥąŗţįţįŏŋ Řąŋğē∙∙∙∙]]]
#XFLD: Label text
Name=[[[Ńąɱē]]]
#XFLD: Label text
Locked=[[[Ļŏċķēƌ∙∙∙∙∙∙∙∙]]]
#XFLD: Label text
Others=[[[ŎŢĤĔŘŜ∙∙∙∙∙∙∙∙]]]
#XFLD: Label text
Delete=[[[Ďēĺēţē∙∙∙∙∙∙∙∙]]]
#XFLD: Label text
LoadData=[[[Ļŏąƌ Ŝēĺēċţēƌ Ƥąŗţįţįŏŋş∙∙∙∙∙∙]]]
#XFLD: Label text
LoadSelectedData=[[[Ļŏąƌ Ŝēĺēċţēƌ Ƥąŗţįţįŏŋş∙∙∙∙∙∙]]]
#XFLD: Confirmation text
LoadNewPersistenceConfirm=[[[Ţĥįş ŵįĺĺ ĺŏąƌ ą ŋēŵ şŋąρşĥŏţ ƒŏŗ ąĺĺ űŋĺŏċķēƌ ąŋƌ ċĥąŋğēƌ ρąŗţįţįŏŋş, ŋŏţ ŏŋĺŷ ţĥē ŏŋēş ŷŏű şēĺēċţēƌ. Ďŏ ŷŏű ŵąŋţ ţŏ ċŏŋţįŋűē?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Label text
Continue=[[[Ĉŏŋţįŋűē∙∙∙∙∙∙]]]

#XFLD: Label text
PARTITIONS=[[[Ƥąŗţįţįŏŋş∙∙∙∙]]]
#XFLD: Label text
ADD_PARTITIONS=[[[+ Āƌƌ Ƥąŗţįţįŏŋ∙∙∙∙]]]
#XFLD: Label text
GTT=[[[>=∙∙]]]
#XFLD: Label text
LT=[[[<∙∙∙]]]
#XFLD: Label text
AddRange=[[[Āƌƌ Ƥąŗţįţįŏŋ∙∙∙∙∙∙]]]
#XFLD: Label text
deleteRange=[[[Ďēĺēţē Ƥąŗţįţįŏŋ∙∙∙∙∙∙∙∙]]]
#XFLD: Label text
LOW_PLACE_HOLDER=[[[Ĕŋţēŗ ĺŏŵ ʋąĺűē∙∙∙∙]]]
#XFLD: Label text
HIGH_PLACE_HOLDER=[[[Ĕŋţēŗ ĥįğĥ ʋąĺűē∙∙∙∙∙∙∙∙]]]
#XFLD: tooltip text
lockedTooltip=[[[Ļŏċķ ρąŗţįţįŏŋ ąƒţēŗ įŋįţįąĺ ĺŏąƌ∙∙∙∙∙∙∙∙∙∙∙]]]

#XFLD: Button text
Edit=[[[Ĕƌįţ]]]
#XFLD: Button text
CANCEL=[[[Ĉąŋċēĺ∙∙∙∙∙∙∙∙]]]

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=[[[Ļąşţ Ŝţąţįşţįċş Ůρƌąţē∙∙∙∙∙]]]
#XFLD: Statistics Fields
STATISTICS=[[[Ŝţąţįşţįċş∙∙∙∙]]]

#XFLD:Retry label
TEXT_Retry=[[[Řēţŗŷ∙∙∙∙∙∙∙∙∙]]]
#XFLD:Retry label
TEXT_Retry_tooltip=[[[Řēţŗŷ ŗēąĺ-ţįɱē ŗēρĺįċąţįŏŋ ąƒţēŗ ēŗŗŏŗ įş şŏĺʋēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: text retry
Retry=[[[Ĉŏŋƒįŗɱ∙∙∙∙∙∙∙]]]
#XMG: Retry confirmation text
retryConfirmationTxt=[[[Ţĥē ĺąşţ ŗēąĺ-ţįɱē ŗēρĺįċąţįŏŋ ţēŗɱįŋąţēƌ ŵįţĥ ąŋ ēŗŗŏŗ.\\u014B Ĉŏŋƒįŗɱ ţĥąţ ţĥē ēŗŗŏŗ įş ċŏŗŗēċţēƌ ąŋƌ ţĥąţ ŗēąĺ-ţįɱē ŗēρĺįċąţįŏŋ ċąŋ ƃē ŗēşţąŗţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMG: Retry success text
retrySuccess=[[[Řēţŗŷ ρŗŏċēşş şűċċēşşƒűĺĺŷ įŋįţįąţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMG: Retry fail text
retryFail=[[[Řēţŗŷ ρŗŏċēşş ƒąįĺēƌ.∙∙∙∙∙]]]
#XMSG: activity message for create statistics
CREATE_STATISTICS=[[[Ĉŗēąţē Ŝţąţįşţįċş∙∙∙∙∙∙∙]]]
#XMSG: activity message for edit statistics
DROP_STATISTICS=[[[Ďēĺēţē Ŝţąţįşţįċş∙∙∙∙∙∙∙]]]
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=[[[Řēƒŗēşĥ Ŝţąţįşţįċş∙∙∙∙∙∙]]]
#XMSG: activity message for edit statistics
ALTER_STATISTICS=[[[Ĕƌįţ Ŝţąţįşţįċş∙∙∙∙]]]
#XMSG: Task log message started task
taskStarted=[[[Ţĥē ţąşķ {0} ĥąş şţąŗţēƌ.]]]
#XMSG: Task log message for finished task
taskFinished=[[[Ţĥē ţąşķ {0} ēŋƌēƌ ŵįţĥ şţąţűş {1}.]]]
#XMSG: Task log message for finished task with end time
taskFinishedAt=[[[Ţąşķ {0} ēŋƌēƌ ąţ {2} ŵįţĥ şţąţűş {1}.]]]
#XMSG: Task {0} has input parameters
taskHasInputParameters=[[[Ţąşķ {0} ĥąş įŋρűţ ρąŗąɱēţēŗş.]]]
#XMSG: Task log message for unexpected error
unexpectedExecutionError=[[[Ţĥē ţąşķ {0} ēŋƌēƌ ŵįţĥ ąŋ űŋēχρēċţēƌ ēŗŗŏŗ. Ţĥē ţąşķ şţąţűş ĥąş ƃēēŋ şēţ ţŏ {1}.]]]
#XMSG: Task log message for failed task
failedToEnd=[[[Ƒąįĺēƌ ţŏ şēţ şţąţűş ţŏ {0} ŏŗ ƒąįĺēƌ ţŏ ŗēɱŏʋē ţĥē ĺŏċķ.]]]
#XMSG: Task log message
lockNotFound=[[[Ŵē ċąŋ’ţ ƒįŋąĺįžē ţĥē ρŗŏċēşş ąş ţĥē ĺŏċķ įş ɱįşşįŋğ: ţĥē ţąşķ ɱįğĥţ ĥąʋē ƃēēŋ ċąŋċēĺēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message failed task
failedOverwrite=[[[Ţĥē ţąşķ {0} įş ąĺŗēąƌŷ ĺŏċķēƌ ƃŷ {1}. Ţĥēŗēƒŏŗē, įţ ƒąįĺēƌ ŵįţĥ ţĥē ƒŏĺĺŏŵįŋğ ēŗŗŏŗ: {2}.]]]
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=[[[Ļŏċķ ŏƒ ţĥįş ţąşķ ŵąş ţąķēŋ ŏʋēŗ ƃŷ ąŋŏţĥēŗ ţąşķ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message failed takeover
failedTakeover=[[[Ƒąįĺēƌ ţŏ ţąķē ŏʋēŗ ąŋ ēχįşţįŋğ ţąşķ.∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message successful takeover
successTakeover=[[[Ļēƒţ ŏʋēŗ ĺŏċķ ĥąƌ ţŏ ƃē ŗēĺēąşēƌ. Ţĥē ŋēŵ ĺŏċķ ƒŏŗ ţĥįş ţąşķ įş şēţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Tasklog Dialog Details
txtDetails=[[[Ţĥē ŗēɱŏţē şţąţēɱēŋţş ρŗŏċēşşēƌ ƌűŗįŋğ ţĥē ŗűŋ ċąŋ ƃē ƌįşρĺąŷēƌ ƃŷ ŏρēŋįŋğ ţĥē ŗēɱŏţē ƣűēŗŷ ɱŏŋįţŏŗ, įŋ ţĥē ƌēţąįĺş ŏƒ ţĥē ρąŗţįţįŏŋ-şρēċįƒįċ ɱēşşąğēş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=[[[Řēɱŏţē ŜǬĻ şţąţēɱēŋţş ĥąʋē ƃēēŋ ƌēĺēţēƌ ƒŗŏɱ ţĥē ƌąţąƃąşē ąŋƌ ċąŋŋŏţ ƃē ƌįşρĺąŷēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=[[[Řēɱŏţē ƣűēŗįēş ţĥąţ ĥąʋē ċŏŋŋēċţįŏŋş ąşşįğŋēƌ ţŏ ŏţĥēŗ şρąċēş ċąŋ’ţ ƃē ƌįşρĺąŷēƌ. Ģŏ ţŏ ţĥē Řēɱŏţē Ǭűēŗŷ Μŏŋįţŏŗ ąŋƌ űşē ţĥē şţąţēɱēŋţ ĬĎ ţŏ ƒįĺţēŗ ţĥēɱ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message for parallel check error
parallelCheckError=[[[Ţĥē ţąşķ ċąŋ’ţ ƃē ρŗŏċēşşēƌ ƃēċąűşē ąŋŏţĥēŗ ţąşķ įş ŗűŋŋįŋğ ąŋƌ ąĺŗēąƌŷ ƃĺŏċķįŋğ ţĥįş ţąşķ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message for parallel running task
parallelTaskRunning=[[[Ā ċŏŋƒĺįċţįŋğ ţąşķ įş ąĺŗēąƌŷ ŗűŋŋįŋğ.∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=[[[Ŝţąţűş {0} ƌűŗįŋğ ŗűŋ ŵįţĥ ċŏŗŗēĺąţįŏŋ ĬĎ {1}.]]]
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=[[[Ţĥē ąşşįğŋēƌ űşēŗ ƌŏēş ŋŏţ ĥąʋē ţĥē ρŗįʋįĺēğēş ŋēċēşşąŗŷ ţŏ ēχēċűţē ţĥįş ţąşķ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XBUT: Label for open in Editor
openInEditor=[[[Ŏρēŋ įŋ Ĕƌįţŏŗ∙∙∙∙∙]]]
#XBUT: Label for open in Editor
openInEditorNew=[[[Ŏρēŋ įŋ Ďąţą Ɓűįĺƌēŗ∙∙∙∙]]]
#XFLD:Run deails label
runDetails=[[[Řűŋ Ďēţąįĺş∙∙∙∙∙∙∙∙]]]
#XFLD: Label for Logs
Logs=[[[Ļŏğş]]]
#XFLD: Label for Settings
Settings=[[[Ŝēţţįŋğş∙∙∙∙∙∙]]]
#XFLD: Label for Save button
Save=[[[Ŝąʋē]]]
#XFLD: Label for Standard
Standard_PO=[[[Ƥēŗƒŏŗɱąŋċē-Ŏρţįɱįžēƌ (Řēċŏɱɱēŋƌēƌ)∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Label for Hana low memory processing
HLMP_MO=[[[Μēɱŏŗŷ-Ŏρţįɱįžēƌ∙∙∙∙∙∙∙∙]]]
#XFLD: Label for execution mode
ExecutionMode=[[[Řűŋ Μŏƌē∙∙∙∙∙∙]]]
#XFLD: Label for job execution
jobExecution=[[[Ƥŗŏċēşşįŋğ Μŏƌē∙∙∙∙]]]
#XFLD: Label for Synchronous
syncExec=[[[Ŝŷŋċĥŗŏŋŏűş∙∙∙∙∙∙∙∙]]]
#XFLD: Label for Asynchronous
asyncExec=[[[Āşŷŋċĥŗŏŋŏűş∙∙∙∙∙∙∙]]]
#XFLD: Label for default asynchronous execution
defaultAsyncExec=[[[Ůşē Ďēƒąűĺţ (Āşŷŋċĥŗŏŋŏűş, ɱąŷ ċĥąŋğē įŋ ţĥē ƒűţűŗē)∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Save settings success
saveSettingsSuccess=[[[ŜĀƤ ĤĀŃĀ Ĕχēċűţįŏŋ Μŏƌē ċĥąŋğēƌ.∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Save settings failure
saveSettingsFailed=[[[ŜĀƤ ĤĀŃĀ Ĕχēċűţįŏŋ Μŏƌē ċĥąŋğē ƒąįĺēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=[[[Ĵŏƃ Ĕχēċűţįŏŋ ċĥąŋğēƌ.∙∙∙∙∙]]]
#XMSG: Job Execution change failed
jobExecSettingFailed=[[[Ĵŏƃ Ĕχēċűţįŏŋ ċĥąŋğē ƒąįĺēƌ.∙∙∙∙∙∙∙∙]]]
#XMSG: Text for Type
typeTxt=[[[Ţŷρē]]]
#XMSG: Text for Monitor
monitorTxt=[[[Μŏŋįţŏŗ∙∙∙∙∙∙∙]]]
#XMSG: Text for activity
activityTxt=[[[Āċţįʋįţŷ∙∙∙∙∙∙]]]
#XMSG: Text for metrics
metricsTxt=[[[Μēţŗįċş∙∙∙∙∙∙∙]]]
#XTXT: Text for Task chain key
TASK_CHAINS=[[[Ţąşķ Ĉĥąįŋ∙∙∙∙]]]
#XTXT: Text for View Key
VIEWS=[[[Ʋįēŵ]]]
#XTXT: Text for remote table key
REMOTE_TABLES=[[[Řēɱŏţē Ţąƃĺē∙∙∙∙∙∙∙]]]
#XTXT: Text for Space key
SPACE=[[[Ŝρąċē∙∙∙∙∙∙∙∙∙]]]
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=[[[Ĕĺąşţįċ Ĉŏɱρűţē Ńŏƌē∙∙∙∙]]]
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=[[[Řēρĺįċąţįŏŋ Ƒĺŏŵ∙∙∙∙∙∙∙∙]]]
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=[[[Ĭŋţēĺĺįğēŋţ Ļŏŏķűρ∙∙∙∙∙∙]]]
#XTXT: Text for Local Table
LOCAL_TABLE=[[[Ļŏċąĺ Ţąƃĺē∙∙∙∙∙∙∙∙]]]
#XTXT: Text for Data flow key
DATA_FLOWS=[[[Ďąţą Ƒĺŏŵ∙∙∙∙∙]]]
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=[[[ŜǬĻ Ŝċŗįρţ Ƥŗŏċēƌűŗē∙∙∙∙]]]
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=[[[ƁŴ Ƥŗŏċēşş Ĉĥąįŋ∙∙∙∙∙∙∙∙]]]
#XTXT: Text for API
API=[[[ĀƤĬ∙]]]
#XTXT: View in Monitor Link text
viewInMonitorTxt=[[[Ʋįēŵ įŋ Μŏŋįţŏŗ∙∙∙∙]]]
#XTXT: Task List header text
taskListHeader=[[[Ţąşķ Ļįşţ ({0})]]]
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=[[[Μēţŗįċş ƒŏŗ ĥįşţŏŗįċąĺ ŗűŋş ŏƒ ą ƌąţą ƒĺŏŵ ċąŋ’ţ ƃē ŗēţŗįēʋēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=[[[Ĉŏɱρĺēţē ŗűŋ ƌēţąįĺ įşŋ’ţ ĺŏąƌįŋğ ąţ ţĥē ɱŏɱēŋţ. Ţŗŷ ţŏ ŗēƒŗēşĥ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Label text for the Metrices table header
metricesColLabel=[[[Ŏρēŗąţŏŗ Ļąƃēĺ∙∙∙∙∙]]]
#XFLD: Label text for the Metrices table header
metricesType=[[[Ţŷρē]]]
#XFLD: Label text for the Metrices table header
metricesRecordLabel=[[[Řēċŏŗƌ Ĉŏűŋţ∙∙∙∙∙∙∙]]]
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=[[[Řűŋ ţĥē Ţąşķ Ĉĥąįŋ∙∙∙∙∙∙]]]
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=[[[Ţąşķ ċĥąįŋ ŗűŋ ĥąş şţąŗţēƌ.∙∙∙∙∙∙∙∙]]]
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=[[[Ţąşķ ċĥąįŋ ŗűŋ ĥąş şţąŗţēƌ ƒŏŗ {0}]]]
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=[[[Ƒąįĺēƌ ţŏ ŗűŋ ţĥē ţąşķ ċĥąįŋ.∙∙∙∙∙∙∙∙∙]]]
#XTXT: Execute button label
runLabel=[[[Řűŋ∙]]]
#XTXT: Execute button label
runLabelNew=[[[Ŝţąŗţ Řűŋ∙∙∙∙∙]]]
#XMSG: Filter Object header
chainsFilteredTableHeader=[[[Ƒįĺţēŗēƌ ƃŷ Ŏƃĵēċţ: {0}]]]
#XFLD: Parent task chain label
parentChainLabel=[[[Ƥąŗēŋţ Ţąşķ Ĉĥąįŋ:∙∙∙∙∙∙]]]
#XFLD: Parent task chain unauthorized
Unauthorized=[[[Ńŏţ Āűţĥŏŗįžēƌ ţŏ Ʋįēŵ∙∙∙∙∙]]]
#XFLD: Parent task chain label
parentTaskLabel=[[[Ƥąŗēŋţ Ţąşķ:∙∙∙∙∙∙∙]]]
#XTXT: Task status
NOT_TRIGGERED=[[[Ńŏţ Ţŗįğğēŗēƌ∙∙∙∙∙∙]]]
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=[[[Ĕŋţēŗ Ƒűĺĺ Ŝċŗēēŋ Μŏƌē∙∙∙∙∙]]]
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=[[[Ĕχįţ Ƒűĺĺ Ŝċŗēēŋ Μŏƌē∙∙∙∙∙]]]
#XTXT: Close Task log details right panel
closeRightColumn=[[[Ĉĺŏşē Ŝēċţįŏŋ∙∙∙∙∙∙]]]
#XTXT: Sort Text
sortTxt=[[[Ŝŏŗţ]]]
#XTXT: Filter Text
filterTxt=[[[Ƒįĺţēŗ∙∙∙∙∙∙∙∙]]]
#XTXT: Filter by text to show list of filters applied
filterByTxt=[[[Ƒįĺţēŗ ƃŷ∙∙∙∙∙]]]
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=[[[Μŏŗē ţĥąŋ 5 Μįŋűţēş∙∙∙∙∙]]]
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=[[[Μŏŗē ţĥąŋ 15 Μįŋűţēş∙∙∙∙]]]
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=[[[Μŏŗē ţĥąŋ 1 Ĥŏűŗ∙∙∙∙∙∙∙∙]]]
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=[[[Ļąşţ Ĥŏűŗ∙∙∙∙∙]]]
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=[[[Ļąşţ 24 Ĥŏűŗş∙∙∙∙∙∙]]]
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=[[[Ļąşţ Μŏŋţĥ∙∙∙∙]]]
#XTXT: Messages title text
messagesText=[[[Μēşşąğēş∙∙∙∙∙∙]]]

#XTXT Statistics information message
statisticsInfo=[[[Ŝţąţįşţįċş ċąŋŋŏţ ƃē ċŗēąţēƌ ƒŏŗ ŗēɱŏţē ţąƃĺēş ŵįţĥ ƌąţą ąċċēşş "Řēρĺįċąţēƌ". Ţŏ ċŗēąţē şţąţįşţįċş, ŗēɱŏʋē ţĥē ŗēρĺįċąţēƌ ƌąţą įŋ ţĥē Řēɱŏţē Ţąƃĺēş Ďēţąįĺş Μŏŋįţŏŗ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD
GO_TO_REMOTETABLE_DETAILS=[[[Ģŏ ţŏ Řēɱŏţē Ţąƃĺē Ďēţąįĺş Μŏŋįţŏŗ∙∙∙∙∙∙∙∙∙∙∙]]]

#XTXT: Repair latest failed run label
retryRunLabel=[[[Řēţŗŷ Ļąţēşţ Řűŋ∙∙∙∙∙∙∙∙]]]
#XTXT: Repair failed run label
retryRun=[[[Řēţŗŷ Řűŋ∙∙∙∙∙]]]
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=[[[Ţąşķ ċĥąįŋ ŗēţŗŷ ŗűŋ ĥąş şţąŗţēƌ∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=[[[Ţąşķ ċĥąįŋ ŗēţŗŷ ŗűŋ ĥąş ƒąįĺēƌ∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task chain child elements name
taskChainRetryChildObject=[[[Ţąşķ {0}]]]
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=[[[Ńēŵ Ţąşķ∙∙∙∙∙∙]]]
#XFLD Analyzed View
analyzedView=[[[Āŋąĺŷžēƌ Ʋįēŵ∙∙∙∙∙∙]]]
#XFLD Metrics
Metrics=[[[Μēţŗįċş∙∙∙∙∙∙∙]]]
#XFLD Partition Metrics
PartitionMetrics=[[[Ƥąŗţįţįŏŋ Μēţŗįċş∙∙∙∙∙∙∙]]]
#XFLD Entities
Messages=[[[Ţąşķ Ļŏğ∙∙∙∙∙∙]]]
#XTXT: Title Message for empty partition data
partitionEmptyTitle=[[[Ńŏ ρąŗţįţįŏŋş ĥąʋē ƃēēŋ ƌēƒįŋēƌ ŷēţ∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTXT: Description message for empty partition data
partitionEmptyDescText=[[[Ĉŗēąţē ρąŗţįţįŏŋş ƃŷ şρēċįƒŷįŋğ ċŗįţēŗįą ţŏ ƃŗēąķ ĺąŗğēŗ ƌąţą ʋŏĺűɱēş įŋţŏ şɱąĺĺēŗ, ɱŏŗē ɱąŋąğēƃĺē ρąŗţş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XTXT: Title Message for empty runs data
runsEmptyTitle=[[[Ńŏ ĺŏğş ąŗē ąʋąįĺąƃĺē ŷēţ∙∙∙∙∙∙∙]]]
#XTXT: Description message for empty runs data
runsEmptyDescText=[[[Ŵĥēŋ ŷŏű şţąŗţ ą ŋēŵ ąċţįʋįţŷ (Ļŏąƌ ą ŋēŵ şŋąρşĥŏţ, şţąŗţ ʋįēŵ ąŋąĺŷžēŗ...) ŷŏű ŵįĺĺ şēē ŗēĺąţēƌ ĺŏğş ĥēŗē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=[[[Ţĥē ēĺąşţįċ ċŏɱρűţē ŋŏƌē {0} ċŏŋƒįğűŗąţįŏŋ, įş ŋŏţ ɱąįŋţąįŋēƌ ąċċŏŗƌįŋğĺŷ. Ƥĺēąşē ċĥēċķ ŷŏűŗ ƌēƒįŋįţįŏŋ.]]]
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=[[[Ƥŗŏċēşş ţŏ ąƌƌ ţĥē ēĺąşţįċ ċŏɱρűţē ŋŏƌē {0} ƒąįĺēƌ.]]]
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=[[[Ĉŗēąţįŏŋ ąŋƌ ąċţįʋąţįŏŋ ŏƒ ŗēρĺįċą, ƒŏŗ ţĥē şŏűŗċē ŏƃĵēċţ "{0}"."{1}" įŋ ţĥē ēĺąşţįċ ċŏɱρűţē ŋŏƌē {2}, ĥąş ƃēēŋ şţąŗţēƌ.]]]
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=[[[Řēɱŏʋįŋğ ŗēρĺįċą, ƒŏŗ ţĥē şŏűŗċē ŏƃĵēċţ "{0}"."{1}" ƒŗŏɱ ţĥē ēĺąşţįċ ċŏɱρűţē ŋŏƌē {2}, ĥąş ƃēēŋ şţąŗţēƌ.]]]
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=[[[Ĉŗēąţįŏŋ ąŋƌ ąċţįʋąţįŏŋ ŏƒ ţĥē ŗēρĺįċą, ƒŏŗ ţĥē şŏűŗċē ŏƃĵēċţ "{0}"."{1}", ĥąş ƒąįĺēƌ.]]]
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=[[[Řēɱŏʋįŋğ ŗēρĺįċą, ƒŏŗ ţĥē şŏűŗċē ŏƃĵēċţ "{0}"."{1}", ĥąş ƒąįĺēƌ.]]]
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=[[[Řŏűţįŋğ ţĥē ąŋąĺŷţįċ ƣűēŗįēş ċŏɱρűţąţįŏŋ ŏƒ ţĥē şρąċē {0} ţŏ ţĥē ēĺąşţįċ ċŏɱρűţē ŋŏƌē {1} şţąŗţēƌ.]]]
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=[[[Řŏűţįŋğ ţĥē ąŋąĺŷţįċ ƣűēŗįēş ċŏɱρűţąţįŏŋ ŏƒ ţĥē şρąċē {0} ţŏ ţĥē ċŏŗŗēşρŏŋƌįŋğ ēĺąşţįċ ċŏɱρűţē ŋŏƌē ƒąįĺēƌ.]]]
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=[[[Řēŗŏűţįŋğ ţĥē ąŋąĺŷţįċ ƣűēŗįēş ċŏɱρűţąţįŏŋ ŏƒ ţĥē şρąċē {0} ƃąċķ ƒŗŏɱ ţĥē ēĺąşţįċ ċŏɱρűţē ŋŏƌē {1} şţąŗţēƌ.]]]
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=[[[Řēŗŏűţįŋğ ţĥē ąŋąĺŷţįċ ƣűēŗįēş ċŏɱρűţąţįŏŋ ŏƒ ţĥē şρąċē {0} ƃąċķ ţŏ ċŏŏŗƌįŋąţŏŗ ƒąįĺēƌ.]]]
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=[[[Ţąşķ ċĥąįŋ {0} ţŏ ţĥē ċŏŗŗēşρŏŋƌįŋğ ēĺąşţįċ ċŏɱρűţē ŋŏƌē {1} ĥąş ƃēēŋ ţŗįğğēŗēƌ.]]]
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=[[[Ģēŋēŗąţįŏŋ ŏƒ ţĥē ţąşķ ċĥąįŋ ƒŏŗ ţĥē ēĺąşţįċ ċŏɱρűţē ŋŏƌē {0} ĥąş ƒąįĺēƌ.]]]
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=[[[Ţĥē ρŗŏʋįşįŏŋįŋğ ŏƒ ţĥē ēĺąşţįċ ċŏɱρűţē ŋŏƌē {0} ĥąş şţąŗţēƌ ŵįţĥ ţĥē ğįʋēŋ şįžįŋğ ρĺąŋ: ʋĈƤŮş: {1}, ɱēɱŏŗŷ (ĢįƁ): {2}, ąŋƌ şţŏŗąğē şįžē(ĢįƁ): {3}.]]]
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=[[[Ţĥē ēĺąşţįċ ċŏɱρűţē ŋŏƌē {0} ĥąş ąĺŗēąƌŷ ƃēēŋ ρŗŏʋįşįŏŋēƌ.]]]
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=[[[Ţĥē ēĺąşţįċ ċŏɱρűţē ŋŏƌē {0} ĥąş ąĺŗēąƌŷ ƃēēŋ ƌēρŗŏʋįşįŏŋēƌ.]]]
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=[[[Ţĥē ŏρēŗąţįŏŋ įş ŋŏţ ąĺĺŏŵēƌ. Ƥĺēąşē şţŏρ ţĥē ēĺąşţįċ ċŏɱρűţē ŋŏƌē {0} ąŋƌ ŗēşţąŗţ įţ ţŏ űρƌąţē ţĥē şįžįŋğ ρĺąŋ.]]]
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=[[[Ƥŗŏċēşş ţŏ ŗēɱŏʋē ţĥē ēĺąşţįċ ċŏɱρűţē ŋŏƌē {0} ƒąįĺēƌ.]]]
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=[[[Ţĥē ċűŗŗēŋţ ŗűŋ ŏƒ ţĥē ēĺąşţįċ ċŏɱρűţē ŋŏƌē {0} ţįɱēƌ ŏűţ.]]]
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=[[[Ĉĥēċķįŋğ ţĥē şţąţűş ŏƒ ţĥē ēĺąşţįċ ċŏɱρűţē ŋŏƌē {0} įş įŋ ρŗŏğŗēşş...]]]
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=[[[Ţąşķ ċĥąįŋ ğēŋēŗąţįŏŋ ƒŏŗ ţĥē ēĺąşţįċ ċŏɱρűţē ŋŏƌē {0} įş ĺŏċķēƌ, ĥēŋċē ţĥē ċĥąįŋ {1} ɱįğĥţ şţįĺĺ ƃē ŗűŋŋįŋğ.]]]
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=[[[Ţĥē şŏűŗċē ŏƃĵēċţ "{0}"."{1}" įş ŗēρĺįċąţēƌ ąş ƌēρēŋƌēŋċŷ ŏƒ ţĥē ʋįēŵ "{2}"."{3}".]]]
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=[[[Ůŋąƃĺē ţŏ ŗēρĺįċąţē ţĥē ţąƃĺē "{0}"."{1}", şįŋċē ţĥē ŗēρĺįċąţįŏŋ ŏƒ ŗŏŵ ţąƃĺē įş ƌēρŗēċąţēƌ.]]]
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=[[[Μąχįɱűɱ ēĺąşţįċ ċŏɱρűţē ŋŏƌē ρēŗ ŜĀƤ ĤĀŃĀ Ĉĺŏűƌ įŋşţąŋċē įş ēχċēēƌēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=[[[Řűŋŋįŋğ ŏρēŗąţįŏŋ ƒŏŗ ţĥē ēĺąşţįċ ċŏɱρűţē ŋŏƌē {0} įş şţįĺĺ įŋ ρŗŏğŗēşş.]]]
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=[[[Ďűē ţŏ ţēċĥŋįċąĺ įşşűēş, ƌēĺēţįŋğ ţĥē ŗēρĺįċą ƒŏŗ ţĥē şŏűŗċē ţąƃĺē {0} ĥąş ƃēēŋ şţŏρρēƌ. Ƥĺēąşē ţŗŷ ąğąįŋ ĺąţēŗ.]]]
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=[[[Ďűē ţŏ ţēċĥŋįċąĺ įşşűēş, ċŗēąţįŋğ ţĥē ŗēρĺįċą ƒŏŗ ţĥē şŏűŗċē ţąƃĺē {0} ĥąş ƃēēŋ şţŏρρēƌ. Ƥĺēąşē ţŗŷ ąğąįŋ ĺąţēŗ.]]]
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=[[[Ĉąŋŋŏţ şţąŗţ ąŋ ēĺąşţįċ ċŏɱρűţē ŋŏƌē ąş ţĥē űşąğē ĺįɱįţ ĥąş ƃēēŋ ŗēąċĥēƌ ŏŗ ąş ŋŏ ċŏɱρűţē ƃĺŏċķ-ĥŏűŗş ĥąʋē ƃēēŋ ąĺĺŏċąţēƌ ŷēţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message for chain preparation
chainLoadFromRepository=[[[Ļŏąƌįŋğ ţąşķ ċĥąįŋ ąŋƌ ρŗēρąŗįŋğ ţŏ ŗűŋ ą ţŏţąĺ ŏƒ {0} ţąşķş ţĥąţ ąŗē ρąŗţ ŏƒ ţĥįş ċĥąįŋ.]]]
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=[[[Ā ċŏŋƒĺįċţįŋğ ţąşķ įş ąĺŗēąƌŷ ŗűŋŋįŋğ∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Replication will change
txt_replication_change=[[[Řēρĺįċąţįŏŋ ţŷρē ŵįĺĺ ƃē ċĥąŋğēƌ.∙∙∙∙∙∙∙∙∙∙∙]]]
txt_repl_viewdetails=[[[Ʋįēŵ Ďēţąįĺş∙∙∙∙∙∙∙]]]

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=[[[Ĭţ ĺŏŏķş ĺįķē ţĥēŗē ŵąş ąŋ ēŗŗŏŗ ŵįţĥ ŗēţŗŷ ĺąţēşţ ŗűŋ ąş ţĥē ρŗēʋįŏűş ţąşķ ŗűŋ ƒąįĺēƌ ƃēƒŏŗē ţĥē ρĺąŋ ċŏűĺƌ ƃē ğēŋēŗąţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#general messages
EXECUTION_ERROR_LOCKED_SPACE=[[[Ŝρąċē "{0}" įş ĺŏċķēƌ.]]]

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=[[[Ţĥē ąċţįʋįţŷ {0} ŗēƣűįŗēş ţĥē ĺŏċąĺ ţąƃĺē {1} ţŏ ƃē ƌēρĺŏŷēƌ.]]]
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=[[[Ţĥē ąċţįʋįţŷ {0} ŗēƣűįŗēş Ďēĺţą Ĉąρţűŗē ţŏ ƃē şŵįţċĥēƌ ŏŋ ƒŏŗ ţĥē ĺŏċąĺ ţąƃĺē {1}.]]]
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=[[[Ţĥē ąċţįʋįţŷ {0} ŗēƣűįŗēş ţĥē ĺŏċąĺ ţąƃĺē {1} ţŏ şţŏŗē ƌąţą įŋ ţĥē ŏƃĵēċţ şţŏŗē.]]]
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=[[[Ţĥē ąċţįʋįţŷ {0} ŗēƣűįŗēş ţĥē ĺŏċąĺ ţąƃĺē {1} ţŏ şţŏŗē ƌąţą įŋ ţĥē ƌąţąƃąşē.]]]

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=[[[Ŝţąŗţįŋğ ţŏ ŗēɱŏʋē ƌēĺēţēƌ ŗēċŏŗƌş ƒŏŗ ţĥē ĺŏċąĺ ţąƃĺē {0}.]]]
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=[[[Ŝţąŗţįŋğ ţŏ ƌēĺēţē ąĺĺ ŗēċŏŗƌş ƒŏŗ ţĥē ĺŏċąĺ ţąƃĺē {0}.]]]
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=[[[Ŝţąŗţįŋğ ţŏ ƌēĺēţē ŗēċŏŗƌş ƒŏŗ ţĥē ĺŏċąĺ ţąƃĺē {0} ąċċŏŗƌįŋğ ţŏ ţĥē ƒįĺţēŗ ċŏŋƌįţįŏŋ {1}.]]]
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ŗēɱŏʋįŋğ ƌēĺēţēƌ ŗēċŏŗƌş ƒŏŗ ţĥē ĺŏċąĺ ţąƃĺē {0}.]]]
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ƌēĺēţįŋğ ąĺĺ ŗēċŏŗƌş ƒŏŗ ţĥē ĺŏċąĺ ţąƃĺē {0}.]]]
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=[[[Ďēĺēţįŋğ ąĺĺ ƒűĺĺŷ-ρŗŏċēşşēƌ ŗēċŏŗƌş ŵįţĥ Ĉĥąŋğē Ţŷρē "Ďēĺēţēƌ", ŵĥįċĥ ąŗē ŏĺƌēŗ ţĥąŋ {0} ƌąŷş.]]]
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=[[[Ďēĺēţįŋğ ąĺĺ ƒűĺĺŷ-ρŗŏċēşşēƌ ŗēċŏŗƌş ŵįţĥ Ĉĥąŋğē Ţŷρē "Ďēĺēţēƌ", ŵĥįċĥ ąŗē ŏĺƌēŗ ţĥąŋ {0}.]]]
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS=[[[{0} ŗēċŏŗƌş ĥąʋē ƃēēŋ ƌēĺēţēƌ.]]]
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS=[[[{0} ŗēċŏŗƌş ĥąʋē ƃēēŋ ɱąŗķēƌ ƒŏŗ ƌēĺēţįŏŋ.]]]
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=[[[Řēɱŏʋįŋğ ƌēĺēţēƌ ŗēċŏŗƌş ƒŏŗ ţĥē ĺŏċąĺ ţąƃĺē {0} įş ċŏɱρĺēţēƌ.]]]
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=[[[Ďēĺēţįŋğ ąĺĺ ŗēċŏŗƌş ƒŏŗ ĺŏċąĺ ţąƃĺē {0} įş ċŏɱρĺēţēƌ.]]]
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=[[[Ŝţąŗţįŋğ ţŏ ɱąŗķ ŗēċŏŗƌş ąş "Ďēĺēţēƌ" ƒŏŗ ţĥē ĺŏċąĺ ţąƃĺē {0}.]]]
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=[[[Ŝţąŗţįŋğ ţŏ ɱąŗķ ŗēċŏŗƌş ąş "Ďēĺēţēƌ" ƒŏŗ ţĥē ĺŏċąĺ ţąƃĺē {0} ąċċŏŗƌįŋğ ţŏ ţĥē ƒįĺţēŗ ċŏŋƌįţįŏŋ {1}.]]]
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=[[[Μąŗķįŋğ ŗēċŏŗƌş ąş "Ďēĺēţēƌ" ƒŏŗ ţĥē ĺŏċąĺ ţąƃĺē {0} įş ċŏɱρĺēţēƌ.]]]

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=[[[Ďąţą ċĥąŋğēş ąŗē ţēɱρŏŗąŗįĺŷ ĺŏąƌįŋğ įŋţŏ ţĥē ţąƃĺē {0}.]]]
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=[[[Ďąţą ċĥąŋğēş ąŗē ţēɱρŏŗąŗįĺŷ ĺŏąƌēƌ įŋţŏ ţĥē ţąƃĺē {0}.]]]
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=[[[Ďąţą ċĥąŋğēş ąŗē ρŗŏċēşşēƌ ąŋƌ ƌēĺēţēƌ ƒŗŏɱ ţĥē ţąƃĺē {0}.]]]

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=[[[Ĉŏŋŋēċţįŏŋ ƌēţąįĺş.∙∙∙∙∙]]]

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=[[[Ŝţąŗţįŋğ ţŏ ŏρţįɱįžē Ļŏċąĺ Ţąƃĺē (Ƒįĺē).∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ŏρţįɱįžįŋğ Ļŏċąĺ Ţąƃĺē (Ƒįĺē).∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ. Ŏρţįɱįžįŋğ Ļŏċąĺ Ţąƃĺē (Ƒįĺē) ĥąş ƃēēŋ şţŏρρēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=[[[Ŏρţįɱįžįŋğ Ļŏċąĺ Ţąƃĺē (Ƒįĺē)...∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=[[[Ļŏċąĺ Ţąƃĺē (Ƒįĺē) ĥąş ƃēēŋ ŏρţįɱįžēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=[[[Ļŏċąĺ Ţąƃĺē (Ƒįĺē) įş ŏρţįɱįžēƌ.∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=[[[Ţĥē ţąƃĺē įş ŏρţįɱįžēƌ ŵįţĥ ţĥē Ż-Ŏŗƌēŗ ċŏĺűɱŋş: {0}.]]]

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ. Ţŗűŋċąţįŋğ Ļŏċąĺ Ţąƃĺē (Ƒįĺē) ĥąş ƃēēŋ şţŏρρēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=[[[Ţŗűŋċąţįŋğ Ļŏċąĺ Ţąƃĺē (Ƒįĺē)...∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=[[[Ĭŋƃŏűŋƌ ƃűƒƒēŗ ĺŏċąţįŏŋ ƒŏŗ Ļŏċąĺ Ţąƃĺē (Ƒįĺē) ĥąş ƃēēŋ ƌŗŏρρēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=[[[Ŝţąŗţįŋğ ţŏ ʋąċűűɱ (ƌēĺēţē ąĺĺ ƒűĺĺŷ-ρŗŏċēşşēƌ ŗēċŏŗƌş) Ļŏċąĺ Ţąƃĺē (Ƒįĺē).∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ʋąċűűɱįŋğ Ļŏċąĺ Ţąƃĺē (Ƒįĺē).∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message
LTF_VACUUM_STOPPED=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ. Ʋąċűűɱįŋğ Ļŏċąĺ Ţąƃĺē (Ƒįĺē) ĥąş ƃēēŋ şţŏρρēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message
LTF_VACUUM_PROCESSING=[[[Ʋąċűűɱįŋğ Ļŏċąĺ Ţąƃĺē (Ƒįĺē)...∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message
LTF_VACUUM_SUCCESS=[[[Ʋąċűűɱįŋğ įş ċŏɱρĺēţē.∙∙∙∙∙]]]
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=[[[Ďēĺēţįŋğ ąĺĺ ƒűĺĺŷ-ρŗŏċēşşēƌ ŗēċŏŗƌş ŵĥįċĥ ąŗē ŏĺƌēŗ ţĥąŋ {0} ƌąŷş.]]]
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=[[[Ďēĺēţįŋğ ąĺĺ ƒűĺĺŷ-ρŗŏċēşşēƌ ŗēċŏŗƌş ŵĥįċĥ ąŗē ŏĺƌēŗ ţĥąŋ {0}.]]]

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=[[[Ŝţąŗţįŋğ ţŏ ɱēŗğē ŋēŵ ŗēċŏŗƌş ŵįţĥ Ļŏċąĺ Ţąƃĺē (Ƒįĺē).∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=[[[Ŝţąŗţįŋğ ţŏ ɱēŗğē ŋēŵ ŗēċŏŗƌş ŵįţĥ Ļŏċąĺ Ţąƃĺē (Ƒįĺē) űşįŋğ ţĥē şēţţįŋğ "Μēŗğē Ďąţą Āűţŏɱąţįċąĺĺŷ".∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=[[[Ŝţąŗţįŋğ ţŏ ɱēŗğē ŋēŵ ŗēċŏŗƌş ŵįţĥ Ļŏċąĺ Ţąƃĺē (Ƒįĺē). Ţĥįş ţąşķ ŵąş įŋįţįąţēƌ ţĥŗŏűğĥ ţĥē ĀƤĬ Μēŗğē Řēƣűēşţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=[[[Μēŗğįŋğ ŋēŵ ŗēċŏŗƌş ŵįţĥ Ļŏċąĺ Ţąƃĺē (Ƒįĺē).∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ɱēŗğįŋğ ŋēŵ ŗēċŏŗƌş ŵįţĥ Ļŏċąĺ Ţąƃĺē (Ƒįĺē).∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=[[[Ļŏċąĺ Ţąƃĺē (Ƒįĺē) įş ɱēŗğēƌ.∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ. Ţĥē ɱēŗğē ŏƒ Ļŏċąĺ Ţąƃĺē (Ƒįĺē) ĥąş ƃēēŋ şţŏρρēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=[[[Ţĥē ɱēŗğē ŏƒ Ļŏċąĺ Ţąƃĺē (Ƒįĺē) ĥąş ƒąįĺēƌ ƃēċąűşē ŏƒ ąŋ ēŗŗŏŗ, ƃűţ ţĥē ŏρēŗąţįŏŋ ŵąş ρąŗţįąĺĺŷ şűċċēşşƒűĺ, ąŋƌ şŏɱē ƌąţą ĥąş ąĺŗēąƌŷ ƃēēŋ ɱēŗğēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=[[[Ā ţįɱēŏűţ ēŗŗŏŗ ŏċċűŗŗēƌ. Ţĥē ąċţįʋįţŷ {0} ĥąş ƃēēŋ ŗűŋŋįŋğ ƒŏŗ {1} ĥŏűŗş.]]]
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=[[[Ţĥē ąşŷŋċĥŗŏŋŏűş ēχēċűţįŏŋ ċŏűĺƌ ŋŏţ şţąŗţ ƃēċąűşē ŏƒ ą ĥįğĥ şŷşţēɱ ĺŏąƌ. Ŏρēŋ ţĥē ''Ŝŷşţēɱ Μŏŋįţŏŗ'' ąŋƌ ċĥēċķ ţĥē ŗűŋŋįŋğ ţąşķş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=[[[Ţĥē ąşŷŋċĥŗŏŋŏűş ēχēċűţįŏŋ ĥąş ƃēēŋ ċąŋċēĺēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=[[[Ţĥē {0} ţąşķ ŗąŋ ŵįţĥįŋ ţĥē ɱēɱŏŗŷ ĺįɱįţş ŏƒ {1} ąŋƌ {2}.]]]
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=[[[Ţĥē {0} ţąşķ ŗąŋ ŵįţĥ ţĥē ŗēşŏűŗċē ĬĎ {1}.]]]

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=[[[Ƒįŋƌ ąŋƌ ŗēρĺąċē ĥąş şţąŗţēƌ įŋ Ļŏċąĺ Ţąƃĺē (Ƒįĺē).∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=[[[Ƒįŋƌ ąŋƌ ŗēρĺąċē ĥąş ċŏɱρĺēţēƌ įŋ Ļŏċąĺ Ţąƃĺē (Ƒįĺē).∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=[[[Ƒįŋƌ ąŋƌ ŗēρĺąċē ĥąş ƒąįĺēƌ įŋ Ļŏċąĺ Ţąƃĺē (Ƒįĺē).∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ. Ůρƌąţįŋğ şţąţįşţįċş ƒŏŗ Ļŏċąĺ Ţąƃĺē (Ƒįĺē) ĥąş ƃēēŋ şţŏρρēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=[[[Ţĥē ţąşķ ƒąįĺēƌ ƃēċąűşē ŏƒ ąŋ ŏűţ ŏƒ ɱēɱŏŗŷ ēŗŗŏŗ ŏŋ ţĥē ŜĀƤ ĤĀŃĀ ƌąţąƃąşē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=[[[Ţĥē ţąşķ ƒąįĺēƌ ƃēċąűşē ŏƒ ąŋ ŜĀƤ ĤĀŃĀ Āƌɱįşşįŏŋ Ĉŏŋţŗŏĺ Řēĵēċţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=[[[Ţĥē ţąşķ ƒąįĺēƌ ƃēċąűşē ŏƒ ţŏŏ ɱąŋŷ ąċţįʋē ŜĀƤ ĤĀŃĀ ċŏŋŋēċţįŏŋş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=[[[Ţĥē Řēţŗŷ ŏρēŗąţįŏŋ ċŏűĺƌ ŋŏţ ƃē ρēŗƒŏŗɱēƌ ƃēċąűşē ŗēţŗįēş ąŗē ŏŋĺŷ ąĺĺŏŵēƌ ŏŋ ţĥē ρąŗēŋţ ŏƒ ą ŋēşţēƌ ţąşķ ċĥąįŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=[[[Ļŏğş ŏƒ ţĥē ƒąįĺēƌ ċĥįĺƌ ţąşķş ąŗē ŋŏ ĺŏŋğēŗ ąʋąįĺąƃĺē ƒŏŗ Řēţŗŷ ţŏ ρŗŏċēēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]


####Metrics Labels

performanceOptimized=[[[Ƥēŗƒŏŗɱąŋċē-Ŏρţįɱįžēƌ∙∙∙∙∙]]]
memoryOptimized=[[[Μēɱŏŗŷ-Ŏρţįɱįžēƌ∙∙∙∙∙∙∙∙]]]

JOB_EXECUTION=[[[Ĵŏƃ Ĕχēċűţįŏŋ∙∙∙∙∙∙]]]
EXECUTION_MODE=[[[Řűŋ Μŏƌē∙∙∙∙∙∙]]]
NUMBER_OF_RECORDS_OVERALL=[[[Ńűɱƃēŗ ŏƒ Ƥēŗşįşţēƌ Řēċŏŗƌş∙∙∙∙∙∙∙∙]]]
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=[[[Ńűɱƃēŗ ŏƒ Řēċŏŗƌş ŗēąƌ ƒŗŏɱ Řēɱŏţē Ŝŏűŗċē∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
RUNTIME_MS_REMOTE_EXECUTION_TIME=[[[Řēɱŏţē Ŝŏűŗċē Ƥŗŏċēşşįŋğ Ţįɱē∙∙∙∙∙∙∙∙∙]]]
MEMORY_CONSUMPTION_GIB=[[[ŜĀƤ ĤĀŃĀ Ƥēąķ Μēɱŏŗŷ∙∙∙∙]]]
NUMBER_OF_PARTITIONS=[[[Ńűɱƃēŗ ŏƒ Ƥąŗţįţįŏŋş∙∙∙∙]]]
MEMORY_CONSUMPTION_GIB_OVERALL=[[[ŜĀƤ ĤĀŃĀ Ƥēąķ Μēɱŏŗŷ∙∙∙∙]]]
NUMBER_OF_PARTITIONS_LOCKED=[[[Ńűɱƃēŗ ŏƒ Ļŏċķēƌ Ƥąŗţįţįŏŋş∙∙∙∙∙∙∙∙]]]
PARTITIONING_COLUMN=[[[Ƥąŗţįţįŏŋįŋğ Ĉŏĺűɱŋ∙∙∙∙∙]]]
HANA_PEAK_CPU_TIME=[[[ŜĀƤ ĤĀŃĀ Ţŏţąĺ ĈƤŮ Ţįɱē∙∙∙∙∙∙]]]
USED_IN_DISK=[[[Ůşēƌ Ŝţŏŗąğē∙∙∙∙∙∙∙]]]
INPUT_PARAMETER_PARAMETER_VALUE=[[[Ĭŋρűţ Ƥąŗąɱēţēŗ∙∙∙∙]]]
INPUT_PARAMETER=[[[Ĭŋρűţ Ƥąŗąɱēţēŗ∙∙∙∙]]]
ECN_ID=[[[Ĕĺąşţįċ Ĉŏɱρűţē Ńŏƌē Ńąɱē∙∙∙∙∙∙∙]]]

DAC=[[[Ďąţą Āċċēşş Ĉŏŋţŗŏĺş∙∙∙∙]]]
YES=[[[Ŷēş∙]]]
NO=[[[Ńŏ∙∙]]]
noofrecords=[[[Ńűɱƃēŗ ŏƒ Řēċŏŗƌş∙∙∙∙∙∙∙]]]
partitionpeakmemory=[[[ŜĀƤ ĤĀŃĀ Ƥēąķ Μēɱŏŗŷ∙∙∙∙]]]
value=[[[Ʋąĺűē∙∙∙∙∙∙∙∙∙]]]
metricsTitle=[[[Μēţŗįċş ({0})]]]
partitionmetricsTitle=[[[Ƥąŗţįţįŏŋş ({0})]]]
partitionLabel=[[[Ƥąŗţįţįŏŋ∙∙∙∙∙]]]
OthersNotNull=[[[Ʋąĺűēş ŋŏţ įŋċĺűƌēƌ įŋ ŗąŋğēş∙∙∙∙∙∙∙∙∙]]]
OthersNull=[[[Ńűĺĺ ʋąĺűēş∙∙∙∙∙∙∙∙]]]
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=[[[Ŝēţţįŋğş űşēƌ ƒŏŗ ĺąşţ ƌąţą ρēŗşįşţēŋċē ŗűŋ:∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Message for input parameter name
inputParameterLabel=[[[Ĭŋρűţ Ƥąŗąɱēţēŗ∙∙∙∙]]]
#XMSG: Message for input parameter value
inputParameterValueLabel=[[[Ʋąĺűē∙∙∙∙∙∙∙∙∙]]]
#XMSG: Message for persisted data
inputParameterPersistedLabel=[[[Ƥēŗşįşţēƌ Āţ∙∙∙∙∙∙∙]]]
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=[[[Ďēĺēţē Ďąţą∙∙∙∙∙∙∙∙]]]
REMOVE_DELETED_RECORDS=[[[Řēɱŏʋē Ďēĺēţēƌ Řēċŏŗƌş∙∙∙∙∙]]]
MERGE_FILES=[[[Μēŗğē Ƒįĺēş∙∙∙∙∙∙∙∙]]]
OPTIMIZE_FILES=[[[Ŏρţįɱįžē Ƒįĺēş∙∙∙∙∙]]]
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=[[[Ʋįēŵ įŋ ŜĀƤ ƁŴ Ɓŗįƌğē Μŏŋįţŏŗ∙∙∙∙∙∙∙∙∙]]]

ANALYZE_PERFORMANCE=[[[Āŋąĺŷžē Ƥēŗƒŏŗɱąŋċē∙∙∙∙∙]]]
CANCEL_ANALYZE_PERFORMANCE=[[[Ĉąŋċēĺ Āŋąĺŷžē Ƥēŗƒŏŗɱąŋċē∙∙∙∙∙∙∙]]]

#XFLD: Label for frequency column
everyLabel=[[[Ĕʋēŗŷ∙∙∙∙∙∙∙∙∙]]]
#XFLD: Plural Recurrence text for Hour
hoursLabel=[[[Ĥŏűŗş∙∙∙∙∙∙∙∙∙]]]
#XFLD: Plural Recurrence text for Day
daysLabel=[[[Ďąŷş]]]
#XFLD: Plural Recurrence text for Month
monthsLabel=[[[Μŏŋţĥş∙∙∙∙∙∙∙∙]]]
#XFLD: Plural Recurrence text for Minutes
minutesLabel=[[[Μįŋűţēş∙∙∙∙∙∙∙]]]

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=[[[<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Ʋįēŵ Ƥēŗşįşţēŋċŷ ţŗŏűƃĺēşĥŏŏţįŋğ ğűįƌē</a>]]]
#XTXT TEXT for view persistency guide link
OOMMessage=[[[Ƥŗŏċēşş ŏűţ ŏƒ ɱēɱŏŗŷ. Ůŋąƃĺē ţŏ ρēŗşįşţ ƌąţą ƒŏŗ ţĥē ʋįēŵ "{0}". Ĉŏŋşűĺţ ţĥē Ĥēĺρ Ƥŏŗţąĺ ƒŏŗ ɱŏŗē įŋƒŏŗɱąţįŏŋ ąƃŏűţ ŏűţ ŏƒ ɱēɱŏŗŷ ēŗŗŏŗş. Ĉŏŋşįƌēŗ ċĥēċķįŋğ Ʋįēŵ Āŋąĺŷžēŗ ţŏ ąŋąĺŷžē ţĥē ʋįēŵ ƒŏŗ ɱēɱŏŗŷ ċŏŋşűɱρţįŏŋ ċŏɱρĺēχįţŷ.]]]

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=[[[Ńŏţ Āρρĺįċąƃĺē∙∙∙∙∙]]]
OPEN_BRACKET=[[[(∙∙∙]]]
CLOSE_BRACKET=[[[)∙∙∙]]]
