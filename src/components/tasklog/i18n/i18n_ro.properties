
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Detalii jurnal
#XFLD: Header
TASK_LOGS=Ju<PERSON><PERSON> sarcină ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Execuții ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Vizualizare detalii
#XFLD: Button text
STOP=Oprire execuție
#XFLD: Label text
RUN_START=Început ultima execuție
#XFLD: Label text
RUN_END=Sfârșit ultima execuție
#XFLD: Label text
RUNTIME=Durată
#XTIT: Count for Messages
txtDetailMessages=Mesaje ({0})
#XFLD: Label text
TIME=Marcă de timp
#XFLD: Label text
MESSAGE=Mesaj
#XFLD: Label text
TASK_STATUS=Categorie
#XFLD: Label text
TASK_ACTIVITY=Activitate
#XFLD: Label text
RUN_START_DETAILS=Lansare
#XFLD: Label text
RUN_END_DETAILS=Sfârșit
#XFLD: Label text
LOGS=Execuții
#XFLD: Label text
STATUS=Stare
#XFLD: Label text
RUN_STATUS=Stare execuție
#XFLD: Label text
Runtime=Durată
#XFLD: Label text
RuntimeTooltip=Durată (hh : mm : ss)
#XFLD: Label text
TRIGGEREDBY=Declanșat de
#XFLD: Label text
TRIGGEREDBYNew=Executat de
#XFLD: Label text
TRIGGEREDBYNewImp=Execuție lansată de
#XFLD: Label text
EXECUTIONTYPE=Tip de execuție
#XFLD: Label text
EXECUTIONTYPENew=Tip de execuție
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Spațiu lanț supraordonat
#XFLD: Refresh tooltip
TEXT_REFRESH=Împrospătare
#XFLD: view Details link
VIEW_ERROR_DETAILS=Vizualizare detalii
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Descărcare detalii suplimentare
#XMSG: Download completed
downloadStarted=Descărcare lansată
#XMSG: Error while downloading content
errorInDownload=A apărut o eroare la descărcare.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Vizualizare detalii
#XBTN: cancel button of task details dialog
TXT_CANCEL=Anulare
#XBTN: back button from task details
TXT_BACK=Înapoi
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Sarcină terminată
#XFLD: Log message with failed status
MSG_LOG_FAILED=Sarcină nereușită
#XFLD: Master and detail table with no data
No_Data=Fără date
#XFLD: Retry tooltip
TEXT_RETRY=Reîncercare
#XFLD: Cancel Run label
TEXT_CancelRun=Anulare execuție
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Curățare încărcare nereușită
#XMSG:button copy sql statement
txtSQLStatement=Copiere instrucțiune SQL
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Deschidere monitor de query-uri la distanță
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=Pentru a afișa instrucțiunile SQL la distanță, efectuați click pe "Deschidere Monitor de query-uri la distanță".
#XMSG:button ok
txtOk=OK
#XMSG: button close
txtClose=Închidere
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=Acțiunea de anulare execuție pentru obiectul "{0}" a fost lansată.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=Acțiunea de anulare execuție pentru obiectul "{0}" a eșuat.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=Acțiunea de anulare execuție pentru obiectul "{0}" nu mai este posibilă deoarece starea de replicare a fost modificată.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Niciun jurnal de sarcină nu are starea În execuție.
#XMSG: message for conflicting task
Task_Already_Running=O sarcină în conflict este deja în execuție pentru obiectul "{0}".
#XFLD: Label for no task log with running state title
actionInfo=Informații acțiune
#XMSG Copied to clipboard
copiedToClip=Copiat în clipboard
#XFLD copy
Copy=Copiere
#XFLD copy correlation ID
CopyCorrelationID=Copiere ID de corelare
#XFLD Close
Close=Închidere
#XFLD: show more Label
txtShowMore=Afișare mai mult
#XFLD: message Label
messageLabel=Mesaj:
#XFLD: details Label
detailsLabel=Detalii:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Executare \r\n instrucțiune SQL:
#XFLD:statementId Label
statementIdLabel=ID instrucțiune:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Număr de instrucțiuni \r\n SQL la distanță:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Număr de instrucțiuni
#XFLD: Space Label
txtSpaces=Spațiu
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Spații neautorizate
#XFLD: Privilege Error Text
txtPrivilegeError=Nu aveți suficiente privilegii pentru a vizualiza aceste date.
#XFLD: Label for Object Header
DATA_ACCESS=Acces la date
#XFLD: Label for Object Header
SCHEDULE=Programare
#XFLD: Label for Object Header
DETAILS=Detalii
#XFLD: Label for Object Header
LATEST_UPDATE=Ultima actualizare
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Ultima modificare (sursă)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Frecvență de împrospătare
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Frecvență programată
#XFLD: Label for Object Header
NEXT_RUN=Execuția următoare
#XFLD: Label for Object Header
CONNECTION=Conexiune
#XFLD: Label for Object Header
DP_AGENT=Agent DP
#XFLD: Label for Object Header
USED_IN_MEMORY=Memorie utilizată pentru stocare (MiB)
#XFLD: Label for Object Header
USED_DISK=Disc utilizat pentru stocare (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Spațiu în memorie internă (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Spațiu pe disc (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Număr de înregistrări

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Setare la nereușit
SET_TO_FAILED_ERR=Această sarcină era în execuție, dar utilizatorul a setat starea acestei sarcini la NEREUȘIT.
#XFLD: Label for stopped failed
FAILLOCKED=Execuție deja în desfășurare
#XFLD: sub status STOPPED
STOPPED=Oprit
STOPPED_ERR=Această sarcină a fost oprită, dar nu a fost efectuat niciun rollback.
#XFLD: sub status CANCELLED
CANCELLED=Anulat
CANCELLED_ERR=Această sarcină a fost anulată, după ce a fost lansată. În acest caz, datele au fost aduse la starea anterioară și au fost restaurate la starea care exista înainte ca execuția sarcinii să fie declanșată inițial.
#XFLD: sub status LOCKED
LOCKED=Blocat
LOCKED_ERR=Aceeași sarcină era deja în execuție, așadar acest job de sarcină nu poate fi executat în paralel cu o execuție de sarcină existentă.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Excepție sarcină
TASK_EXCEPTION_ERR=Această sarcină a întâlnit o eroare nespecificată la execuție.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Excepție de executare sarcină
TASK_EXECUTE_EXCEPTION_ERR=Această sarcină a întâlnit o eroare la execuție.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Neautorizat
UNAUTHORIZED_ERR=Utilizatorul nu a putut fi autentificat, a fost blocat sau șters.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Interzis
FORBIDDEN_ERR=Utilizatorul alocat nu are privilegiile necesare pentru executarea acestei sarcini.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Nedeclanșat(ă)
FAIL_NOT_TRIGGERED_ERR=Acest job de sarcină nu a putut fi executat din cauza unei pene de sistem sau a indisponibilității unei părți a sistemului de bază de date la ora execuției planificate. Așteptați următorul timp de execuție al jobului planificat sau reprogramați jobul.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Programare anulată
SCHEDULE_CANCELLED_ERR=Acest job de sarcină nu a putut fi executat din cauza unei erori interne. Contactați suportul SAP și furnizați-le ID-ul de corelare și marca de timp de la informațiile detaliate ale acestui jurnal cu jobul de sarcină.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Execuție anterioară în desfășurare
SUCCESS_SKIPPED_ERR=Execuția acestei sarcini nu a fost declanșată deoarece o execuție anterioară a aceleiași sarcini este încă în desfășurare.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Proprietar lipsă
FAIL_OWNER_MISSING_ERR=Acest job de sarcină nu a putut fi executat deoarece nu are un utilizator de sistem alocat. Alocați un utilizator proprietar la job.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Consimțământ indisponibil
FAIL_CONSENT_NOT_AVAILABLE_ERR=Nu ați autorizat SAP să execute lanțuri de sarcini sau să programeze sarcini de integrare a datelor în numele dvs. Selectați opțiunea furnizată pentru a vă acorda consimțământul.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Consimțământ expirat
FAIL_CONSENT_EXPIRED_ERR=Autorizația care îi permite SAP să execute lanțuri de sarcini sau să programeze sarcini de integrare a datelor în numele dvs. a expirat. Selectați opțiunea furnizată pentru a vă reînnoi consimțământul.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Consimțământ invalidat
FAIL_CONSENT_INVALIDATED_ERR=Această sarcină nu a putut fi executată, de obicei din cauza unei modificări a configurării furnizorului de identitate al tenantului. În acest caz, nu pot fi executate sau programate joburi de sarcină noi în numele utilizatorului afectat. Dacă utilizatorul alocat încă există în noul IdP, revocați consimțământul de programare și apoi acordați-l din nou. Dacă utilizatorul alocat nu mai există, alocați un proprietar de job de sarcină nou și furnizați consimțământul necesar de programare a sarcinii. Consultați următoarea notă SAP: https://launchpad.support.sap.com/#/notes/3089828 pentru mai multe informații.
TASK_EXECUTOR_ERROR=Executor sarcină
TASK_EXECUTOR_ERROR_ERR=Această sarcină a întâlnit o eroare internă, probabil în timpul etapelor de pregătire pentru execuție și sarcina nu a putut fi lansată.
PREREQ_NOT_MET=Condiții preliminare neîndeplinite
PREREQ_NOT_MET_ERR=Această sarcină nu a putut fi executată din cauza problemelor din definiția sa. De exemplu, obiectul nu este implementat, un lanț de sarcini conține logica circulară sau o imagine SQL este nevalabilă.
RESOURCE_LIMIT_ERROR=Eroare de limită de resursă
RESOURCE_LIMIT_ERROR_ERR=În prezent, sarcina nu poate fi efectuată deoarece nu au fost disponibile suficiente resurse sau acestea au fost ocupate.
FAIL_CONSENT_REFUSED_BY_UMS=Consimțământ refuzat
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Această sarcină nu a putut fi executată, în execuțiile programate sau lanțurile de sarcini, din cauza unei modificări a configurării furnizorului de identitate al unui utilizator în tenant. Pentru mai multe informații, consultați următoarea notă SAP: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Programat
#XFLD: status text
SCHEDULEDNew=Definitiv
#XFLD: status text
PAUSED=Întrerupt
#XFLD: status text
DIRECT=Direct
#XFLD: status text
MANUAL=Manual
#XFLD: status text
DIRECTNew=Simplu
#XFLD: status text
COMPLETED=Terminat
#XFLD: status text
FAILED=Nereușit
#XFLD: status text
RUNNING=În execuție
#XFLD: status text
none=Niciunul
#XFLD: status text
realtime=În timp real
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Subsarcină
#XFLD: New Data available in the file
NEW_DATA=Date noi

#XFLD: text for values shown in column Replication Status
txtOff=Inactiv
#XFLD: text for values shown in column Replication Status
txtInitializing=Inițializare
#XFLD: text for values shown in column Replication Status
txtLoading=Încărcare
#XFLD: text for values shown in column Replication Status
txtActive=Activ
#XFLD: text for values shown in column Replication Status
txtAvailable=Disponibil
#XFLD: text for values shown in column Replication Status
txtError=Eroare
#XFLD: text for values shown in column Replication Status
txtPaused=Întrerupt
#XFLD: text for values shown in column Replication Status
txtDisconnected=Deconectat
#XFLD: text for partially Persisted views
partiallyPersisted=Parțial salvat persistent

#XFLD: activity text
REPLICATE=Replicare
#XFLD: activity text
REMOVE_REPLICATED_DATA=Eliminare date replicate
#XFLD: activity text
DISABLE_REALTIME=Dezactivare replicare date în timp real
#XFLD: activity text
REMOVE_PERSISTED_DATA=Eliminare date salvate persistent
#XFLD: activity text
PERSIST=Salvare persistentă
#XFLD: activity text
EXECUTE=Executare
#XFLD: activity text
TASKLOG_CLEANUP=Curățare tacklog
#XFLD: activity text
CANCEL_REPLICATION=Anulare replicare
#XFLD: activity text
MODEL_IMPORT=Import de model
#XFLD: activity text
NONE=Niciunul
#XFLD: activity text
CANCEL_PERSISTENCY=Anulare salvare persistentă
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Analizare imagine
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Anulare analizor de imagini

#XFLD: severity text
INFORMATION=Informații
#XFLD: severity text
SUCCESS=Succes
#XFLD: severity text
WARNING=Avertizare
#XFLD: severity text
ERROR=Eroare
#XFLD: text for values shown for Ascending sort order
SortInAsc=Sortare în ordine ascendentă
#XFLD: text for values shown for Descending sort order
SortInDesc=Sortare în ordine descendentă
#XFLD: filter text for task log columns
Filter=Filtrare
#XFLD: object text for task log columns
Object=Obiect
#XFLD: space text for task log columns
crossSpace=Spațiu

#XBUT: label for remote data access
REMOTE=La distanță
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Replicat (în timp real)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Replicat (instantaneu)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=Replicarea în timp real este blocată din cauza unei erori. După ce eroarea este corectată, puteți utiliza acțiunea “Reîncercare” pentru a continua cu replicarea în timp real.
ERROR_MSG=Replicarea în timp real este blocată din cauza unei erori.
RETRY_FAILED_ERROR=Procesul de reîncercare a eșuat cu o eroare.
LOG_INFO_DETAILS=Nu sunt generate jurnale când datele sunt replicate în modul în timp real. Jurnalele afișate au legătură cu acțiunile anterioare.

#XBUT: Partitioning label
partitionMenuText=Partiționare
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Creare partiție
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Editare partiție
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Ștergere partiție
#XFLD: Initial text
InitialPartitionText=Definiți partiții prin specificarea criteriilor pentru împărțirea seturilor de date mari în seturi mai mici.
DefinePartition=Definire partiții
#XFLD: Message text
partitionChangedInfo=Definiția partiției s-a modificat de la ultima replicare. Se vor aplica modificări la următoarea încărcare de date.
#XFLD: Message text
REAL_TIME_WARNING=Partiționarea este aplicată doar la încărcarea unui instantaneu nou. Nu este aplicată pentru replicarea în timp real.
#XFLD: Message text
loadSelectedPartitions=A început salvarea persistentă a datelor pentru partițiile selectate ale "{0}"
#XFLD: Message text
loadSelectedPartitionsError=A eșuat salvarea persistentă a datelor pentru partițiile selectate ale "{0}"
#XFLD: Message text
viewpartitionChangedInfo=Definiția partiției s-a modificat de la ultima execuție a persistenței. Pentru a aplica modificările, următoarea încărcare de date va fi un instantaneu complet, incluzând partițiile blocate. După ce este finalizată încărcarea completă, veți putea executa datele pentru partiții individuale.
#XFLD: Message text
viewpartitionChangedInfoLocked=Definiția partiției s-a modificat de la ultima execuție a persistenței. Pentru a aplica modificările, următoarea încărcare de date va fi un instantaneu complet, exceptând intervalele de partiții blocate și nemodificate. După ce este finalizată încărcarea, veți putea încărca partițiile selectate din nou.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Replicare tabel
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Programare replicare
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Creare programare
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Editare programare
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Ștergere programare
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Încărcare instantaneu nou
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Lansare replicare date
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Eliminare date replicate
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Activare acces în timp real
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Activare replicare date în timp real
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Dezactivare replicare date în timp real
#XFLD: Message for replicate table action
replicateTableText=Replicare tabel
#XFLD: Message for replicate table action
replicateTableTextNew=Replicare date
#XFLD: Message to schedule task
scheduleText=Programare
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Persistență imagine
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Persistență date
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Încărcare instantaneu nou
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Lansare persistență date
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Eliminare date salvate persistent
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Prelucrare partiționată
#XBUT: Label for scheduled replication
scheduledTxt=Programat
#XBUT: Label for statistics button
statisticsTxt=Statistică
#XBUT: Label for create statistics
createStatsTxt=Creare statistică
#XBUT: Label for edit statistics
editStatsTxt=Editare statistică
#XBUT: Label for refresh statistics
refreshStatsTxt=Împrospătare statistică
#XBUT: Label for delete statistics
dropStatsTxt=Ștergere statistică
#XMSG: Create statistics success message
statsSuccessTxt=A început crearea statisticii de tipul {0} pentru {1}.
#XMSG: Edit statistics success message
statsEditSuccessTxt=A început modificarea tipului de statistică în {0} pentru {1}.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=A început împrospătarea statisticii pentru {0}.
#XMSG: Drop statistics success message
statsDropSuccessTxt=Statistică ștearsă cu succes pentru {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=Eroare la creare statistică
#XMSG: Edit statistics error message
statsEditErrorTxt=Eroare la modificare statistică
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Eroare la împrospătare statistică
#XMSG: Drop statistics error message
statsDropErrorTxt=Eroare la ștergere statistică
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Sigur doriți să eliminați statistica datelor?
startPersistencyAdvisorLabel=Lansare analizor de imagini

#Partition related texts
#XFLD: Label for Column
column=Coloană
#XFLD: Label for No of Partition
noOfPartitions=Nr. de partiții
#XFLD: Label for Column
noOfParallelProcess=Nr. de procese paralele
#XFLD: Label text
noOfLockedPartition=Nr. de partiții blocate
#XFLD: Label for Partition
PARTITION=Partiții
#XFLD: Label for Column
AVAILABLE=Disponibil
#XFLD: Statistics Label
statsLabel=Statistică
#XFLD: Label text
COLUMN=Coloană:
#XFLD: Label text
PARALLEL_PROCESSES=Procese paralele:
#XFLD: Label text
Partition_Range=Interval partiție
#XFLD: Label text
Name=Nume
#XFLD: Label text
Locked=Blocat
#XFLD: Label text
Others=ALTELE
#XFLD: Label text
Delete=Ștergere
#XFLD: Label text
LoadData=Încărcare partiții selectate
#XFLD: Label text
LoadSelectedData=Încărcare partiții selectate
#XFLD: Confirmation text
LoadNewPersistenceConfirm=Această acțiune va încărca un instantaneu nou pentru toate partițiile deblocate și modificate, nu doar pentru cele selectate de dvs. Doriți să continuați?
#XFLD: Label text
Continue=Continuare

#XFLD: Label text
PARTITIONS=Partiții
#XFLD: Label text
ADD_PARTITIONS=+ Adăugare partiție
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Adăugare partiție
#XFLD: Label text
deleteRange=Ștergere partiție
#XFLD: Label text
LOW_PLACE_HOLDER=Introduceți o valoare inferioară
#XFLD: Label text
HIGH_PLACE_HOLDER=Introduceți o valoare superioară
#XFLD: tooltip text
lockedTooltip=Blocare partiție după încărcare inițială

#XFLD: Button text
Edit=Editare
#XFLD: Button text
CANCEL=Anulare

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Ultima actualizare de statistică
#XFLD: Statistics Fields
STATISTICS=Statistică

#XFLD:Retry label
TEXT_Retry=Reîncercare
#XFLD:Retry label
TEXT_Retry_tooltip=Reîncercați replicarea în timp real după ce eroarea este rezolvată.
#XFLD: text retry
Retry=Confirmare
#XMG: Retry confirmation text
retryConfirmationTxt=Ultima replicare în timp real s-a terminat cu o eroare.\n Confirmați că eroarea este corectată și că replicarea în timp real poate fi relansată.
#XMG: Retry success text
retrySuccess=Proces de reîncercare inițializat cu succes.
#XMG: Retry fail text
retryFail=Proces de reîncercare nereușit.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Creare statistică
#XMSG: activity message for edit statistics
DROP_STATISTICS=Ștergere statistică
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Împrospătare statistică
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Editare statistică
#XMSG: Task log message started task
taskStarted=Sarcina {0} a fost lansată.
#XMSG: Task log message for finished task
taskFinished=Sarcina {0} s-a terminat cu starea {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=Sarcina {0} s-a terminat la {2} cu starea {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=Sarcina {0} are parametri de intrare.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=Sarcina {0} s-a terminat cu o eroare neprevăzută. Starea sarcinii a fost setată la {1}.
#XMSG: Task log message for failed task
failedToEnd=Eroare la setare stare la {0} sau eroare la eliminare blocare.
#XMSG: Task log message
lockNotFound=Nu putem finaliza procesul deoarece blocarea lipsește: este posibil să fi fost anulată sarcina.
#XMSG: Task log message failed task
failedOverwrite=Sarcina {0} este deja blocată de {1}. În consecință, a eșuat cu următoarea eroare: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Blocarea acestei sarcini a fost preluată de o altă sarcină.
#XMSG: Task log message failed takeover
failedTakeover=Eroare la preluare o sarcină existentă.
#XMSG: Task log message successful takeover
successTakeover=Blocarea rămasă a trebuit să fie eliberată. Blocarea nouă pentru această sarcină este setată.
#XMSG: Tasklog Dialog Details
txtDetails=Instrucțiunile la distanță prelucrate la execuție pot fi afișate deschizând monitorul de query-uri la distanță în detaliile mesajelor specifice pentru partiție.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=Instrucțiunile SQL la distanță au fost șterse din baza de date și nu pot fi afișate.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Query-urile la distanță care au conexiuni alocate la alte spații nu pot fi afișate. Accesați monitorul de query-uri la distanță și utilizați ID-ul de instrucțiune pentru a le filtra.
#XMSG: Task log message for parallel check error
parallelCheckError=Sarcina nu poate fi prelucrată deoarece o altă sarcină este în execuție și deja blochează această sarcină.
#XMSG: Task log message for parallel running task
parallelTaskRunning=O sarcină în conflict este deja în execuție.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Starea {0} la execuție cu ID corelare {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=Utilizatorul alocat nu are privilegiile necesare pentru executarea acestei sarcini.

#XBUT: Label for open in Editor
openInEditor=Deschidere în editor
#XBUT: Label for open in Editor
openInEditorNew=Deschidere în generator de date
#XFLD:Run deails label
runDetails=Detalii execuție
#XFLD: Label for Logs
Logs=Jurnale
#XFLD: Label for Settings
Settings=Setări
#XFLD: Label for Save button
Save=Salvare
#XFLD: Label for Standard
Standard_PO=Optimizat din punct de vedere al performanței (recomandat)
#XFLD: Label for Hana low memory processing
HLMP_MO=Optimizat din punct de vedere al memoriei
#XFLD: Label for execution mode
ExecutionMode=Mod de execuție
#XFLD: Label for job execution
jobExecution=Mod de prelucrare
#XFLD: Label for Synchronous
syncExec=Sincron
#XFLD: Label for Asynchronous
asyncExec=Asincron
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Utilizare valoare implicită (asincronă, poate fi modificată în viitor)
#XMSG: Save settings success
saveSettingsSuccess=Mod de execuție SAP HANA a fost modificat.
#XMSG: Save settings failure
saveSettingsFailed=Modificare mod de execuție SAP HANA a eșuat.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Execuție job a fost modificată.
#XMSG: Job Execution change failed
jobExecSettingFailed=Modificare execuție job a eșuat.
#XMSG: Text for Type
typeTxt=Tip
#XMSG: Text for Monitor
monitorTxt=Monitor
#XMSG: Text for activity
activityTxt=Activitate
#XMSG: Text for metrics
metricsTxt=Indicatori
#XTXT: Text for Task chain key
TASK_CHAINS=Lanț de sarcini
#XTXT: Text for View Key
VIEWS=Vizualizare
#XTXT: Text for remote table key
REMOTE_TABLES=Tabel la distanță
#XTXT: Text for Space key
SPACE=Spațiu
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Nod de calcul elastic
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Flux de replicare
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Căutare inteligentă
#XTXT: Text for Local Table
LOCAL_TABLE=Tabel local
#XTXT: Text for Data flow key
DATA_FLOWS=Flux de date
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=Procedură script SQL
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=Lanț de procese BW
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Vizualizare în monitor
#XTXT: Task List header text
taskListHeader=Listă de sarcini ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Indicatori pentru execuții istorice ale unui flux de date nu pot fi regăsiți.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=Detalii complete execuție nu se încarcă în prezent. Încercați să împrospătați.
#XFLD: Label text for the Metrices table header
metricesColLabel=Etichetă operator
#XFLD: Label text for the Metrices table header
metricesType=Tip
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Număr de înregistrări
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Executați lanțul de sarcini
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Execuție lanț de sarcini a fost lansată.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Execuție lanț de sarcini a fost lansată pentru {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Eroare la executare lanț de sarcini.
#XTXT: Execute button label
runLabel=Executare
#XTXT: Execute button label
runLabelNew=Lansare execuție
#XMSG: Filter Object header
chainsFilteredTableHeader=Filtrat după obiect: {0}
#XFLD: Parent task chain label
parentChainLabel=Lanț de sarcini supraordonat:
#XFLD: Parent task chain unauthorized
Unauthorized=Fără autorizație pentru vizualizare
#XFLD: Parent task chain label
parentTaskLabel=Sarcină supraordonată:
#XTXT: Task status
NOT_TRIGGERED=Nedeclanșat(ă)
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Intrare în mod ecran întreg
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Ieșire din mod ecran întreg
#XTXT: Close Task log details right panel
closeRightColumn=Închidere secțiune
#XTXT: Sort Text
sortTxt=Sortare
#XTXT: Filter Text
filterTxt=Filtru
#XTXT: Filter by text to show list of filters applied
filterByTxt=Filtrare după
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Mai mult de 5 minute
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Mai mult de 15 minute
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Mai mult de 1 oră
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Ultima oră
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Ultimele 24 de ore
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Ultima lună
#XTXT: Messages title text
messagesText=Mesaje

#XTXT Statistics information message
statisticsInfo=Statistica nu poate fi creată pentru tabelele la distanță cu accesul la date "Replicate". Pentru a crea statistica, eliminați datele replicate din Monitorul de detalii a tabelelor la distanță.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Salt la Monitor de detalii tabele la distanță

#XTXT: Repair latest failed run label
retryRunLabel=Reîncercare ultima execuție
#XTXT: Repair failed run label
retryRun=Reîncercare execuție
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Reîncercare lanț de sarcini a fost lansată
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Reîncercare lanț de sarcini a eșuat
#XMSG: Task chain child elements name
taskChainRetryChildObject=Sarcina {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Sarcină nouă
#XFLD Analyzed View
analyzedView=Imagine analizată
#XFLD Metrics
Metrics=Indicatori
#XFLD Partition Metrics
PartitionMetrics=Indicatori partiție
#XFLD Entities
Messages=Jurnal de sarcini
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Nicio partiție nu a fost încă definită
#XTXT: Description message for empty partition data
partitionEmptyDescText=Creați partiții specificând criteriile pentru a separa volumele mai mari de date în părți mai mici și mai ușor de gestionat.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Nu există jurnale disponibile încă
#XTXT: Description message for empty runs data
runsEmptyDescText=Când lansați o activitate nouă (Încărcare instantaneu nou, Lansare analizor de imagini...) veți vedea jurnalele aferente aici.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=Configurarea {0} a nodului de calcul elastic nu este întreținută corespunzător. Verificați definiția dvs.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Procesul de adăugare a nodului de calcul elastic {0} a eșuat.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Crearea și activarea replicii pentru obiectul sursă "{0}"."{1}" în nodul de calcul elastic {2} au fost lansate.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Eliminarea replicii pentru obiectul sursă "{0}"."{1}" de la nodul de calcul elastic {2} au fost lansate.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Crearea și activarea replicii pentru obiectul sursă "{0}"."{1}" au eșuat.
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Eliminarea replicii pentru obiectul sursă "{0}"."{1}" a eșuat.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Rutarea calculului query-urilor analitice ale spațiului {0} la nodul de calcul elastic {1} a eșuat.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Rutarea calculului query-urilor analitice ale spațiului {0} la nodul de calcul elastic corespondent a eșuat.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Rerutarea calculului query-urilor analitice ale spațiului {0} înapoi de la nodul de calcul elastic {1} a început.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Rerutarea calculului query-urilor analitice ale spațiului {0} înapoi la coordonator a eșuat.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Lanțul de sarcini {0} la nodul de calcul elastic corespondent {1} a fost declanșat.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Generarea lanțului de sarcini pentru nodul de calcul elastic {0} a eșuat.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=Punerea la dispoziție a nodului de calcul elastic {0} a început cu planul de dimensiune indicat: vCPU-uri: {1}, memorie (GiB): {2} și mărime spațiu de stocare (GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=Nodul de calcul elastic {0} a fost deja pus la dispoziție.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=Nodul de calcul elastic {0} a fost deja eliminat.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=Operația nu este permisă. Opriți nodul de calcul elastic {0} și relansați-l pentru a actualiza planul de determinare a dimensiunii.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Procesul de eliminare a nodului de calcul elastic {0} a eșuat.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=Execuția curentă a nodului de calcul elastic {0} a expirat.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=Verificarea stării nodului de calcul elastic {0} este în desfășurare...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=Generarea lanțului de sarcini pentru nodul de calcul elastic {0} este blocată, în consecință lanțul {1} poate fi încă în execuție.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=Obiectul sursă "{0}"."{1}" este replicat ca dependență pentru imaginea "{2}"."{3}".
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Imposibil de replicat tabelul "{0}"."{1}", deoarece replicarea tabelului de linii este învechită.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Nod de calcul elastic maxim per instanță SAP HANA Cloud este depășit.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=Executarea operației pentru nodul de calcul elastic {0} este încă în desfășurare.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Din cauza unor probleme tehnice, ștergerea replicii pentru tabelul sursă {0} a fost oprită. Încercați din nou mai târziu.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=Din cauza unor probleme tehnice, crearea replicii pentru tabelul sursă {0} a fost oprită. Încercați din nou mai târziu.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Imposibil de lansat un nod de calcul elastic deoarece limita de utilizare a fost atinsă sau nu au fost alocate încă ore de bloc de calcul.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Încărcare în curs lanț de sarcini și pregătire de execuție un total de {0} sarcini care fac parte din acest lanț.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=O sarcină în conflict este deja în execuție
#XMSG: Replication will change
txt_replication_change=Tipul de replicare va fi modificat.
txt_repl_viewdetails=Vizualizare detalii

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Se pare că a apărut o eroare la reîncercarea ultimei execuții, deoarece execuția sarcinii anterioare a eșuat înainte ca planul să fi putut fi generat.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=Spațiul "{0}" este blocat.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=Activitatea {0} necesită ca tabelul local {1} să fie implementat.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=Activitatea {0} necesită ca captura delta să fie activată pentru tabelul local {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=Activitatea {0} necesită tabelul local {1} pentru a arhiva date în arhiva de obiecte.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=Activitatea {0} necesită tabelul local {1} pentru a arhiva date în baza de date.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Lansare eliminare înregistrări șterse pentru tabelul local {0}.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Lansare ștergere toate înregistrările pentru tabelul local {0}.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Lansare ștergere înregistrări pentru tabelul local {0} conform condiției de filtru {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=A apărut o eroare la eliminarea înregistrărilor șterse pentru tabelul local {0}.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=A apărut o eroare la ștergerea tuturor înregistrărilor pentru tabelul local {0}.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Ștergerea tuturor înregistrărilor prelucrate complet cu tipul de modificare "Șters" care sunt mai vechi de {0} zile.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Ștergerea tuturor înregistrărilor prelucrate complet cu tipul de modificare "Șters" care sunt mai vechi de {0}.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} înregistrări au fost șterse.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS=Au fost marcate {0} înregistrări pentru ștergere.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=Eliminarea înregistrărilor șterse pentru tabelul local {0} este terminată.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Ștergerea tuturor înregistrărilor pentru tabelul local {0} este terminată.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Lansare marcare înregistrări ca „Șters” pentru tabelul local {0}.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Lansare marcare înregistrări ca „Șters” pentru tabelul local {0} conform condiției de filtru {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Marcarea înregistrărilor ca „Șters” pentru tabelul local {0} este terminată.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Modificările de date se încarcă temporar în tabelul {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Modificările de date sunt încărcate temporar în tabelul {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Modificările de date sunt prelucrate și șterse din tabelul {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Detalii conexiune.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Lansare optimizare tabel local (fișier).
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=A apărut o eroare la optimizarea tabelului local (fișier).
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=A apărut o eroare. Optimizarea tabelului local (fișier) a fost oprită.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Optimizare tabel local (fișier)...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=Tabelul local (fișier) a fost optimizat.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=Tabelul local (fișier) este optimizat.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=Tabelul este optimizat cu coloane sortate în Z: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=A apărut o eroare. Trunchierea tabelului local (fișier) a fost oprită.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Trunchiere tabel local (fișier)...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=Locația bufferului de intrare pentru tabel local (fișier) a fost eliminată.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Începe golirea (ștergerea tuturor înregistrărilor prelucrate complet) tabelului local (fișier).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=A apărut o eroare la golirea tabelului local (fișier).
#XMSG: Task log message
LTF_VACUUM_STOPPED=A apărut o eroare. Golirea tabelului local (fișier) a fost oprită.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Golire tabel local (fișier)...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=Golirea este terminată.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Se șterg toate înregistrările prelucrate complet care sunt mai vechi de {0} zile.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Se șterg toate înregistrărilor prelucrate complet care sunt mai vechi de {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Începe concatenarea înregistrărilor noi cu tabelul local (fișier).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Începe concatenarea înregistrărilor noi cu tabelul local (fișier) utilizând setarea "Concatenare automată date".
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Începe concatenarea înregistrărilor noi cu tabel local (fișier). Această sarcină a fost inițiată prin cererea de concatenare API.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Se concatenează înregistrările noi cu tabelul local (fișier).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=A apărut o eroare la concatenarea înregistrărilor noi cu tabelul local (fișier).
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=Tabelul local (fișier) este concatenat.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=A apărut o eroare. Concatenarea tabelului local (fișier) a fost oprită.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=Concatenarea tabelului local (fișier) a eșuat din cauza unei erori, dar operația a fost parțial reușită și unele date au fost deja concatenate.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=A apărut o eroare de depășire de timp. Activitatea {0} este în desfășurare de {1} ore.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=Execuția asincronă nu a putut fi lansată din cauza unei încărcări ridicate a sistemului. Deschideți ''Monitor sistem'' și verificați sarcinile de execuție.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=Execuția asincronă a fost anulată.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=Sarcina {0} a fost executată între limitele de memorie {1} și {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=Sarcina {0} a fost executată cu ID-ul de resursă {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=Căutarea și înlocuirea a fost lansată în tabelul local (fișier).
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Căutarea și înlocuirea a fost terminată în tabelul local (fișier).
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Căutarea și înlocuirea a eșuat în tabelul local (fișier).

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=A apărut o eroare. Actualizarea statisticilor pentru tabelul local (fișier) a fost oprită.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=Sarcina a eșuat din cauza unei erori de memorie insuficientă în baza de date SAP HANA.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=Sarcina a eșuat din cauza unei respingeri a controlului de admitere SAP HANA.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=Sarcina a eșuat din cauza unui număr prea mare de conexiuni SAP HANA active.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=Operația de reîncercare nu a putut fi efectuată deoarece reîncercările sunt permise doar în elementul supraordonat al unui lanț de sarcini imbricat.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Jurnalele sarcinilor subordonate nereușite nu mai sunt disponibile pentru reîncercare pentru a continua.


####Metrics Labels

performanceOptimized=Optimizat din punct de vedere al performanței
memoryOptimized=Optimizat din punct de vedere al memoriei

JOB_EXECUTION=Execuție job
EXECUTION_MODE=Mod de execuție
NUMBER_OF_RECORDS_OVERALL=Număr de înregistrări persistente
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Număr de înregistrări citite din sursă la distanță
RUNTIME_MS_REMOTE_EXECUTION_TIME=Timp de prelucrare sursă la distanță
MEMORY_CONSUMPTION_GIB=Memorie de vârf SAP HANA
NUMBER_OF_PARTITIONS=Număr de partiții
MEMORY_CONSUMPTION_GIB_OVERALL=Memorie de vârf SAP HANA
NUMBER_OF_PARTITIONS_LOCKED=Număr de partiții blocate
PARTITIONING_COLUMN=Coloană de partiționare
HANA_PEAK_CPU_TIME=Timp CPU total SAP HANA
USED_IN_DISK=Spațiu de stocare utilizat
INPUT_PARAMETER_PARAMETER_VALUE=Parametru de intrare
INPUT_PARAMETER=Parametru de intrare
ECN_ID=Nume nod de calcul elastic

DAC=Controale de acces la date
YES=Da
NO=Nu
noofrecords=Număr de înregistrări
partitionpeakmemory=Memorie de vârf SAP HANA
value=Valoare
metricsTitle=Indicatori ({0})
partitionmetricsTitle=Partiții {0}
partitionLabel=Partiție
OthersNotNull=Valori neincluse în interval
OthersNull=Valori nule
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Setări utilizate pentru ultima execuție de persistență date:
#XMSG: Message for input parameter name
inputParameterLabel=Parametru de intrare
#XMSG: Message for input parameter value
inputParameterValueLabel=Valoare
#XMSG: Message for persisted data
inputParameterPersistedLabel=Salvat persistent la
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Ștergere date
REMOVE_DELETED_RECORDS=Eliminare înregistrări șterse
MERGE_FILES=Concatenare fișiere
OPTIMIZE_FILES=Optimizare fișiere
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Vizualizare în monitor punte SAP BW

ANALYZE_PERFORMANCE=Analizare performanță
CANCEL_ANALYZE_PERFORMANCE=Anulare analizare performanță

#XFLD: Label for frequency column
everyLabel=La fiecare
#XFLD: Plural Recurrence text for Hour
hoursLabel=Ore
#XFLD: Plural Recurrence text for Day
daysLabel=Zile
#XFLD: Plural Recurrence text for Month
monthsLabel=Luni
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minute

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Vizualizare ghid de depanare persistență</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Proces cu memorie insuficientă. Imposibil de salvat persistent date pentru imaginea „{0}”. Consultați Portalul de ajutor pentru mai multe informații despre erorile de memorie insuficientă. Luați în considerare verificarea analizorului de imagini pentru a analiza imaginea pentru complexitatea consumului de memorie.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Nu se aplică
OPEN_BRACKET=(
CLOSE_BRACKET=)
