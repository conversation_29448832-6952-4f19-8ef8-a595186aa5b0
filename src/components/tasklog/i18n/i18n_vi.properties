
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Chi tiết nhật ký
#XFLD: Header
TASK_LOGS=Nhật ký tác vụ ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Thực hiện ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Xem chi tiết
#XFLD: Button text
STOP=Dừng thực hiện
#XFLD: Label text
RUN_START=Khởi động lần thực hiện cuối cùng
#XFLD: Label text
RUN_END=Kết thúc lần thực hiện cuối cùng
#XFLD: Label text
RUNTIME=Khoảng thời gian
#XTIT: Count for Messages
txtDetailMessages=Thông báo ({0})
#XFLD: Label text
TIME=Dạng ngày tháng
#XFLD: Label text
MESSAGE=Thông báo
#XFLD: Label text
TASK_STATUS=Danh mục
#XFLD: Label text
TASK_ACTIVITY=Hoạt động
#XFLD: Label text
RUN_START_DETAILS=Bắt đầu
#XFLD: Label text
RUN_END_DETAILS=Kết thúc
#XFLD: Label text
LOGS=Thực hiện
#XFLD: Label text
STATUS=Trạng thái
#XFLD: Label text
RUN_STATUS=Trạng thái thực hiện
#XFLD: Label text
Runtime=Khoảng thời gian
#XFLD: Label text
RuntimeTooltip=Khoảng thời gian (hh : mm : ss)
#XFLD: Label text
TRIGGEREDBY=Kích hoạt bởi
#XFLD: Label text
TRIGGEREDBYNew=Thực hiện bởi
#XFLD: Label text
TRIGGEREDBYNewImp=Bắt đầu thực hiện bởi
#XFLD: Label text
EXECUTIONTYPE=Kiểu thực hiện
#XFLD: Label text
EXECUTIONTYPENew=Kiểu chạy
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Vùng dữ liệu chuỗi cha
#XFLD: Refresh tooltip
TEXT_REFRESH=Làm mới
#XFLD: view Details link
VIEW_ERROR_DETAILS=Xem chi tiết
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Tải xuống chi tiết bổ sung
#XMSG: Download completed
downloadStarted=Tải xuống đã bắt đầu
#XMSG: Error while downloading content
errorInDownload=Đã xảy ra lỗi trong khi tải xuống.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Xem chi tiết
#XBTN: cancel button of task details dialog
TXT_CANCEL=Hủy
#XBTN: back button from task details
TXT_BACK=Quay lại
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Tác vụ đã hoàn tất
#XFLD: Log message with failed status
MSG_LOG_FAILED=Tác vụ không thành công
#XFLD: Master and detail table with no data
No_Data=Không có dữ liệu
#XFLD: Retry tooltip
TEXT_RETRY=Thử lại
#XFLD: Cancel Run label
TEXT_CancelRun=Hủy thực hiện
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Không thể tải dọn dẹp
#XMSG:button copy sql statement
txtSQLStatement=Sao chép câu lệnh SQL
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Mở giám sát truy vấn từ xa
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=Để hiển thị câu lệnh SQL từ xa, hãy nhấp vào 'Mở giám sát truy vấn từ xa'.
#XMSG:button ok
txtOk=Ok
#XMSG: button close
txtClose=Đóng
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=Thao tác hủy chạy cho đối tượng “{0}” đã được bắt đầu.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=Thao tác hủy chạy cho đối tượng “{0}” không thành công.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=Thao tác hủy chạy cho đối tượng “{0}” không còn khả thi vì trạng thái sao chép đã thay đổi.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Không có nhật ký tác vụ nào có trạng thái đang thực hiện.
#XMSG: message for conflicting task
Task_Already_Running=Tác vụ xung đột đang chạy cho đối tượng ''{0}''.
#XFLD: Label for no task log with running state title
actionInfo=Thông tin thao tác
#XMSG Copied to clipboard
copiedToClip=Được sao chép vào Clipboard
#XFLD copy
Copy=Sao chép
#XFLD copy correlation ID
CopyCorrelationID=Sao chép ID tương quan
#XFLD Close
Close=Đóng
#XFLD: show more Label
txtShowMore=Hiển thị thêm
#XFLD: message Label
messageLabel=Thông báo:
#XFLD: details Label
detailsLabel=Chi tiết:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Thực hiện câu lệnh \r\n SQL:
#XFLD:statementId Label
statementIdLabel=ID câu lệnh:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Số câu lệnh SQL \r\n từ xa:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Số lượng câu lệnh
#XFLD: Space Label
txtSpaces=Vùng dữ liệu
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Vùng dữ liệu không được phép
#XFLD: Privilege Error Text
txtPrivilegeError=Bạn không có đủ đặc quyền để xem dữ liệu này.
#XFLD: Label for Object Header
DATA_ACCESS=Truy cập dữ liệu
#XFLD: Label for Object Header
SCHEDULE=Lập lịch
#XFLD: Label for Object Header
DETAILS=Chi tiết
#XFLD: Label for Object Header
LATEST_UPDATE=Cập nhật mới nhất
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Thay đổi mới nhất (Nguồn)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Tần suất làm mới
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Tần suất được lập lịch
#XFLD: Label for Object Header
NEXT_RUN=Thực hiện kế tiếp
#XFLD: Label for Object Header
CONNECTION=Kết nối
#XFLD: Label for Object Header
DP_AGENT=Tác nhân DP
#XFLD: Label for Object Header
USED_IN_MEMORY=Bộ nhớ được sử dụng để lưu trữ (MiB)
#XFLD: Label for Object Header
USED_DISK=Đĩa được sử dụng để lưu trữ (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Kích thước bộ nhớ trong (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Kích thước trên ổ đĩa (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Số bản ghi

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Đã thiết lập sang Không thành công
SET_TO_FAILED_ERR=Tác vụ này đang chạy nhưng người dùng đã thiết lập trạng thái của tác vụ sang KHÔNG THÀNH CÔNG.
#XFLD: Label for stopped failed
FAILLOCKED=Chạy đang tiến hành
#XFLD: sub status STOPPED
STOPPED=Đã dừng
STOPPED_ERR=Tác vụ này đã dừng, nhưng không có quá trình khôi phục nào được thực hiện.
#XFLD: sub status CANCELLED
CANCELLED=Đã hủy
CANCELLED_ERR=Lần chạy tác vụ này đã hủy sau khi khởi động. Trong trường hợp này, dữ liệu được khôi phục về trạng thái đã tồn tại trước khi chạy tác vụ được kích hoạt ban đầu.
#XFLD: sub status LOCKED
LOCKED=Bị khóa
LOCKED_ERR=Tác vụ tương tự đã chạy, vì vậy tác vụ này không thể chạy song song với việc thực hiện tác vụ hiện có.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Ngoại lệ của tác vụ
TASK_EXCEPTION_ERR=Tác vụ này đã gặp một lỗi không rõ ràng trong quá trình thực hiện.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Ngoại lệ thực hiện tác vụ
TASK_EXECUTE_EXCEPTION_ERR=Tác vụ này đã gặp lỗi trong lúc thực hiện.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Không được phân quyền
UNAUTHORIZED_ERR=Người dùng không thể xác thực đã bị khóa hoặc bị xóa.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Bị cấm
FORBIDDEN_ERR=Người dùng được gán không có đặc quyền cần thiết để thực hiện tác vụ này.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Không được kích hoạt
FAIL_NOT_TRIGGERED_ERR=Không thể thực hiện công việc tác vụ này do hệ thống ngừng hoạt động hoặc một phần nào đó của hệ thống cơ sở dữ liệu không khả dụng tại thời điểm thực hiện theo kế hoạch. Chờ thời gian thực hiện công việc theo lịch trình tiếp theo hoặc lên lịch lại công việc.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Lịch bị hủy
SCHEDULE_CANCELLED_ERR=Không thể thực hiện công việc tác vụ này do lỗi nội bộ. Liên hệ với SAP Support và cung cấp cho họ id tương quan và dạng thời gian từ thông tin chi tiết nhật ký công việc tác vụ này.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Lần chạy trước đang tiến hành
SUCCESS_SKIPPED_ERR=Việc thực hiện tác vụ này chưa được kích hoạt do lần chạy trước của tác vụ tương tự vẫn tiếp tục.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Thiếu chủ sở hữu
FAIL_OWNER_MISSING_ERR=Không thể thực hiện công việc tác vụ này vì nó không có người dùng hệ thống được gán. Gán người dùng chủ sở hữu cho công việc.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Chưa có sự chấp thuận
FAIL_CONSENT_NOT_AVAILABLE_ERR=Bạn chưa ủy quyền cho SAP chạy chuỗi tác vụ hoặc lên lịch tác vụ tích hợp dữ liệu thay cho bạn. Chọn tùy chọn được cung cấp để đưa ra sự chấp thuận của bạn.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Chấp thuận đã hết hạn
FAIL_CONSENT_EXPIRED_ERR=Phân quyền cho SAP chạy chuỗi tác vụ hoặc lên lịch tác vụ tích hợp dữ liệu thay cho bạn đã hết hạn. Chọn tùy chọn được cung cấp để gia hạn sự chấp thuận của bạn.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Chấp thuận đã mất hiệu lực
FAIL_CONSENT_INVALIDATED_ERR=Không thể thực hiện tác vụ này, thường do sự thay đổi trong cấu hình Nhà cung cấp danh tính của đối tượng thuê. Trong trường hợp đó, không có công việc tác vụ mới nào có thể thực hiện hoặc lên lịch dưới tên của người dùng bị ảnh hưởng. Nếu người dùng đã gán vẫn tồn tại trong Nhà cung cấp danh tính mới, hãy thu hồi chấp thuận lên lịch và sau đó cấp lại. Nếu người dùng đã gán không còn tồn tại, hãy gán chủ sở hữu công việc tác vụ mới và cung cấp sự chấp thuận lên lịch tác vụ được yêu cầu. Xem ghi chú SAP sau: https://launchpad.support.sap.com/#/notes/3089828 để biết thêm thông tin.
TASK_EXECUTOR_ERROR=Người thực hiện tác vụ
TASK_EXECUTOR_ERROR_ERR=Tác vụ này đã gặp lỗi nội bộ, có thể xảy ra trong các bước chuẩn bị thực hiện và tác vụ không thể khởi động.
PREREQ_NOT_MET=Yêu cầu tiên quyết không được đáp ứng
PREREQ_NOT_MET_ERR=Không thể chạy tác vụ này vì sự cố trong định nghĩa của nó. Ví dụ: đối tượng không được triển khai, chuỗi tác vụ chứa logic vòng tròn hoặc SQL của màn hình không hợp lệ.
RESOURCE_LIMIT_ERROR=Lỗi giới hạn tài nguyên
RESOURCE_LIMIT_ERROR_ERR=Hiện tại không thể thực hiện tác vụ vì không có đủ tài nguyên hoặc tài nguyên đang bận.
FAIL_CONSENT_REFUSED_BY_UMS=Thỏa thuận bị từ chối
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Không thể thực hiện tác vụ này trong các lần chạy theo lịch hoặc chuỗi tác vụ do có thay đổi trong cấu hình Identity Provider của người dùng trên đối tượng thuê. Để biết thêm thông tin, hãy xem SAP Note sau đây: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Được lập lịch
#XFLD: status text
SCHEDULEDNew=Vĩnh viễn
#XFLD: status text
PAUSED=Đã tạm dừng
#XFLD: status text
DIRECT=Trực tiếp
#XFLD: status text
MANUAL=Thủ công
#XFLD: status text
DIRECTNew=Đơn giản
#XFLD: status text
COMPLETED=Đã hoàn tất
#XFLD: status text
FAILED=Bị lỗi
#XFLD: status text
RUNNING=Chạy
#XFLD: status text
none=Không có
#XFLD: status text
realtime=Thời gian thực
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Tác vụ phụ
#XFLD: New Data available in the file
NEW_DATA=Dữ liệu mới

#XFLD: text for values shown in column Replication Status
txtOff=Tắt
#XFLD: text for values shown in column Replication Status
txtInitializing=Đang khởi tạo
#XFLD: text for values shown in column Replication Status
txtLoading=Đang tải
#XFLD: text for values shown in column Replication Status
txtActive=Đang hoạt động
#XFLD: text for values shown in column Replication Status
txtAvailable=Có sẵn
#XFLD: text for values shown in column Replication Status
txtError=Lỗi
#XFLD: text for values shown in column Replication Status
txtPaused=Đã tạm dừng
#XFLD: text for values shown in column Replication Status
txtDisconnected=Đã ngắt kết nối
#XFLD: text for partially Persisted views
partiallyPersisted=Được ổn định một phần

#XFLD: activity text
REPLICATE=Sao chép
#XFLD: activity text
REMOVE_REPLICATED_DATA=Loại bỏ dữ liệu đã sao chép
#XFLD: activity text
DISABLE_REALTIME=Tắt quá trình sao chép dữ liệu trong thời gian thực
#XFLD: activity text
REMOVE_PERSISTED_DATA=Loại bỏ dữ liệu liên tục
#XFLD: activity text
PERSIST=Vẫn còn
#XFLD: activity text
EXECUTE=Thực hiện
#XFLD: activity text
TASKLOG_CLEANUP=Tacklog_Cleanup
#XFLD: activity text
CANCEL_REPLICATION=Hủy sao chép
#XFLD: activity text
MODEL_IMPORT=Nhập mô hình
#XFLD: activity text
NONE=Không có
#XFLD: activity text
CANCEL_PERSISTENCY=Huỷ tính liên tục
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Phân tích màn hình
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Hủy Công cụ phân tích màn hình

#XFLD: severity text
INFORMATION=Thông tin
#XFLD: severity text
SUCCESS=Thành công
#XFLD: severity text
WARNING=Cảnh báo
#XFLD: severity text
ERROR=Lỗi
#XFLD: text for values shown for Ascending sort order
SortInAsc=Sắp xếp tăng
#XFLD: text for values shown for Descending sort order
SortInDesc=Sắp xếp giảm
#XFLD: filter text for task log columns
Filter=Bộ lọc
#XFLD: object text for task log columns
Object=Đối tượng
#XFLD: space text for task log columns
crossSpace=Vùng dữ liệu

#XBUT: label for remote data access
REMOTE=Từ xa
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Đã sao chép (Theo thời gian thực)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Đã sao chép (Ảnh chụp nhanh)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=Sao chép theo thời gian thực bị chặn do lỗi. Sau khi lỗi được chỉnh sửa, bạn có thể sử dụng thao tác “Thử lại” để tiếp tục sao chép theo thời gian thực.
ERROR_MSG=Sao chép theo thời gian thực bị chặn vì lỗi.
RETRY_FAILED_ERROR=Quá trình thử lại không thành công với lỗi.
LOG_INFO_DETAILS=Không tạo nhật ký khi dữ liệu được sao chép ở chế độ thời gian thực. Các nhật ký được hiển thị liên quan đến các thao tác trước đó.

#XBUT: Partitioning label
partitionMenuText=Phân vùng
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Tạo phân vùng
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Hiệu chỉnh phân vùng
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Xóa phân vùng
#XFLD: Initial text
InitialPartitionText=Xác định các phân vùng bằng cách chỉ định các tiêu chí để chia các tập dữ liệu lớn thành các tập nhỏ hơn.
DefinePartition=Xác định các phân vùng
#XFLD: Message text
partitionChangedInfo=Xác định phân vùng đã thay đổi kể từ lần sao chép cuối cùng. Các thay đổi sẽ được áp dụng trong lần tải dữ liệu tiếp theo.
#XFLD: Message text
REAL_TIME_WARNING=Phân vùng chỉ được áp dụng khi tải một ảnh chụp nhanh mới. Nó không được áp dụng để sao chép thời gian thực.
#XFLD: Message text
loadSelectedPartitions=Đã bắt đầu duy trì dữ liệu cho phân vùng đã chọn của ''{0}''’
#XFLD: Message text
loadSelectedPartitionsError=Không duy trì được dữ liệu cho phân vùng đã chọn của ''{0}''’
#XFLD: Message text
viewpartitionChangedInfo=Định nghĩa phân vùng đã thay đổi kể từ lần chạy ổn định cuối cùng. Để áp dụng các thay đổi, lần tải dữ liệu tiếp theo sẽ là một ảnh chụp nhanh đầy đủ bao gồm cả các phân vùng bị khóa. Khi quá trình tải đầy đủ này kết thúc, bạn sẽ có thể chạy dữ liệu cho các phân vùng đơn lẻ.
#XFLD: Message text
viewpartitionChangedInfoLocked=Định nghĩa phân vùng đã thay đổi kể từ lần chạy ổn định cuối cùng. Để áp dụng các thay đổi, lần tải dữ liệu tiếp theo sẽ là một ảnh chụp nhanh đầy đủ, ngoại trừ phạm vi phân vùng không thay đổi và bị khóa. Một khi quá trình tải này kết thúc, bạn sẽ có thể Tải phân vùng đã chọn lần nữa.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Sao chép bảng
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Lập lịch sao chép
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Tạo lập lịch
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Hiệu chỉnh lịch
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Xóa lịch
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Tải ảnh chụp nhanh mới
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Bắt đầu sao chép dữ liệu
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Loại bỏ dữ liệu đã sao chép
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Bật truy cập theo thời gian thực
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Bật sao chép dữ liệu theo thời gian thực
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Tắt quá trình sao chép dữ liệu trong thời gian thực
#XFLD: Message for replicate table action
replicateTableText=Sao chép bảng
#XFLD: Message for replicate table action
replicateTableTextNew=Sao chép dữ liệu
#XFLD: Message to schedule task
scheduleText=Lập lịch
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Tính ổn định của màn hình
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Ổn định dữ liệu
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Tải ảnh chụp nhanh mới
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Bắt đầu ổn định dữ liệu
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Loại bỏ dữ liệu liên tục
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Xử lý được phân vùng
#XBUT: Label for scheduled replication
scheduledTxt=Được lập lịch
#XBUT: Label for statistics button
statisticsTxt=Thống kê
#XBUT: Label for create statistics
createStatsTxt=Tạo thống kê
#XBUT: Label for edit statistics
editStatsTxt=Hiệu chỉnh thống kê
#XBUT: Label for refresh statistics
refreshStatsTxt=Làm mới thống kê
#XBUT: Label for delete statistics
dropStatsTxt=Xóa thống kê
#XMSG: Create statistics success message
statsSuccessTxt=Đã bắt đầu tạo thống kê của kiểu {0} cho {1}.
#XMSG: Edit statistics success message
statsEditSuccessTxt=Đã bắt đầu thay đổi kiểu thống kê thành {0} cho {1}.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Đã bắt đầu làm mới thống kê cho {0}.
#XMSG: Drop statistics success message
statsDropSuccessTxt=Thống kê đã xóa thành công cho {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=Lỗi khi tạo thống kê
#XMSG: Edit statistics error message
statsEditErrorTxt=Lỗi khi thay đổi thống kê
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Lỗi khi làm mới thống kê
#XMSG: Drop statistics error message
statsDropErrorTxt=Lỗi khi xóa thống kê
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Bạn có chắc là bạn muốn thả thống kê dữ liệu không?
startPersistencyAdvisorLabel=Khởi động trình phân tích màn hình

#Partition related texts
#XFLD: Label for Column
column=Cột
#XFLD: Label for No of Partition
noOfPartitions=Số phân vùng
#XFLD: Label for Column
noOfParallelProcess=Số quy trình song song
#XFLD: Label text
noOfLockedPartition=Số phân vùng bị khóa
#XFLD: Label for Partition
PARTITION=Phân vùng
#XFLD: Label for Column
AVAILABLE=Có sẵn
#XFLD: Statistics Label
statsLabel=Thống kê
#XFLD: Label text
COLUMN=Cột:
#XFLD: Label text
PARALLEL_PROCESSES=Quy trình song song:
#XFLD: Label text
Partition_Range=Khoảng phân vùng
#XFLD: Label text
Name=Tên
#XFLD: Label text
Locked=Bị khóa
#XFLD: Label text
Others=KHÁC
#XFLD: Label text
Delete=Xóa
#XFLD: Label text
LoadData=Tải phân vùng được chọn
#XFLD: Label text
LoadSelectedData=Tải phân vùng được chọn
#XFLD: Confirmation text
LoadNewPersistenceConfirm=Điều này sẽ tải một ảnh chụp nhanh mới cho tất cả các phân vùng đã được mở khóa và thay đổi, không chỉ những phân vùng bạn đã chọn. Bạn có muốn tiếp tục không?
#XFLD: Label text
Continue=Tiếp tục

#XFLD: Label text
PARTITIONS=Phân vùng
#XFLD: Label text
ADD_PARTITIONS=+ Thêm phân vùng
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Thêm phân vùng
#XFLD: Label text
deleteRange=Xóa phân vùng
#XFLD: Label text
LOW_PLACE_HOLDER=Nhập giá trị thấp
#XFLD: Label text
HIGH_PLACE_HOLDER=Nhập giá trị cao
#XFLD: tooltip text
lockedTooltip=Khóa phân vùng sau khi tải ban đầu

#XFLD: Button text
Edit=Hiệu chỉnh
#XFLD: Button text
CANCEL=Hủy

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Cập nhật thống kê sau cùng
#XFLD: Statistics Fields
STATISTICS=Thống kê

#XFLD:Retry label
TEXT_Retry=Thử lại
#XFLD:Retry label
TEXT_Retry_tooltip=Thử lại việc sao chép theo thời gian thực sau khi lỗi được giải quyết.
#XFLD: text retry
Retry=Xác nhận
#XMG: Retry confirmation text
retryConfirmationTxt=Sao chép theo thời gian thực cuối được kết thúc có lỗi.\n Xác nhận rằng lỗi được chỉnh sửa và có thể bắt đầu lại việc sao chép theo thời gian thực.
#XMG: Retry success text
retrySuccess=Quy trình thử lại được khởi tạo thành công.
#XMG: Retry fail text
retryFail=Quy trình thử lại không thành công.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Tạo thống kê
#XMSG: activity message for edit statistics
DROP_STATISTICS=Xóa thống kê
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Làm mới thống kê
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Hiệu chỉnh thống kê
#XMSG: Task log message started task
taskStarted=Công việc {0} đã được bắt đầu.
#XMSG: Task log message for finished task
taskFinished=Công việc {0} đã kết thúc với tình trạng {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=Công việc {0} được kết thúc lúc {2} có tình trạng {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=Tác vụ {0} có tham số đầu vào.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=Công việc {0} đã kết thúc với lỗi không mong muốn. Tình trạng công việc đã được thiết lập thành {1}.
#XMSG: Task log message for failed task
failedToEnd=Không thể thiết lập tình trạng thành {0} hoặc không thể loại bỏ khóa.
#XMSG: Task log message
lockNotFound=Chúng tôi không thể hoàn tất quy trình vì thiếu khóa: công việc có thể đã bị hủy.
#XMSG: Task log message failed task
failedOverwrite=Công việc {0} đã bị khóa bởi {1}. Vì thế, nó không thành công với lỗi sau: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Khóa tác vụ này được tiếp quản bởi tác vụ khác.
#XMSG: Task log message failed takeover
failedTakeover=Không thể tiếp tục công việc hiện có.
#XMSG: Task log message successful takeover
successTakeover=Khóa còn lại phải được mở. Khóa mới cho tác vụ này được thiết lập.
#XMSG: Tasklog Dialog Details
txtDetails=Các câu lệnh từ xa được xử lý trong lúc thực hiện có thể được hiển thị bằng cách mở màn hình truy vấn từ xa, theo chi tiết của các thông báo theo phân vùng cụ thể.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=Các câu lệnh SQL từ xa đã bị xóa khỏi cơ sở dữ liệu và không thể hiển thị.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Không thể hiển thị các truy vấn từ xa có kết nối được gán cho các vùng dữ liệu khác. Đi đến Trình giám sát truy vấn từ xa và sử dụng ID câu lệnh để lọc chúng.
#XMSG: Task log message for parallel check error
parallelCheckError=Không thể xử lý công việc vì công  việc khác đang chạy và đã chặn công việc này.
#XMSG: Task log message for parallel running task
parallelTaskRunning=Công việc xung đột đã chạy.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Trạng thái {0} trong quá trình thực hiện với ID tương quan {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=Người dùng được gán không có đặc quyền cần thiết để thực hiện tác vụ này.

#XBUT: Label for open in Editor
openInEditor=Mở trong trình soạn thảo
#XBUT: Label for open in Editor
openInEditorNew=Mở trong Trình tạo dữ liệu
#XFLD:Run deails label
runDetails=Chi tiết thực hiện
#XFLD: Label for Logs
Logs=Nhật ký
#XFLD: Label for Settings
Settings=Thiết lập
#XFLD: Label for Save button
Save=Lưu
#XFLD: Label for Standard
Standard_PO=Tối ưu hóa hiệu suất (được khuyến nghị)
#XFLD: Label for Hana low memory processing
HLMP_MO=Tối ưu hóa bộ nhớ
#XFLD: Label for execution mode
ExecutionMode=Chế độ chạy
#XFLD: Label for job execution
jobExecution=Chế độ xử lý
#XFLD: Label for Synchronous
syncExec=̣Đồng bộ
#XFLD: Label for Asynchronous
asyncExec=Không đồng bộ
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Sử dụng mặc định (Không đồng bộ, có thể thay đổi trong tương lai)
#XMSG: Save settings success
saveSettingsSuccess=Chế độ thực hiện SAP HANA đã thay đổi.
#XMSG: Save settings failure
saveSettingsFailed=Thay đổi chế độ thực hiện SAP HANA không thành công.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Thực hiện công việc đã thay đổi.
#XMSG: Job Execution change failed
jobExecSettingFailed=Thay đổi thực hiện công việc không thành công.
#XMSG: Text for Type
typeTxt=Kiểu
#XMSG: Text for Monitor
monitorTxt=Giám sát
#XMSG: Text for activity
activityTxt=Hoạt động
#XMSG: Text for metrics
metricsTxt=Số đo
#XTXT: Text for Task chain key
TASK_CHAINS=Chuỗi tác vụ
#XTXT: Text for View Key
VIEWS=Màn hình
#XTXT: Text for remote table key
REMOTE_TABLES=Bảng từ xa
#XTXT: Text for Space key
SPACE=Vùng dữ liệu
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Nút tính toán linh hoạt
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Luồng sao chép
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Tra cứu thông minh
#XTXT: Text for Local Table
LOCAL_TABLE=Bảng cục bộ
#XTXT: Text for Data flow key
DATA_FLOWS=Luồng dữ liệu
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=Thủ tục SQL Script
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=Chuỗi quy trình BW
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Xem trong Giám sát
#XTXT: Task List header text
taskListHeader=Danh sách tác vụ ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Không thể truy xuất số đo lượt chạy lịch sử của luồng dữ liệu.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=Chi tiết thực hiện đầy đủ không tải vào lúc này. Hãy thử làm mới.
#XFLD: Label text for the Metrices table header
metricesColLabel=Nhãn người điều hành
#XFLD: Label text for the Metrices table header
metricesType=Kiểu
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Số lượng bản ghi
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Chạy Chuỗi tác vụ
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Đã bắt đầu quá trình chạy chuỗi tác vụ.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Đã bắt đầu chạy chuỗi tác vụ cho {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Quá trình chạy chuỗi tác vụ không thành công.
#XTXT: Execute button label
runLabel=Chạy
#XTXT: Execute button label
runLabelNew=Bắt đầu chạy
#XMSG: Filter Object header
chainsFilteredTableHeader=Được lọc theo đối tượng: {0}
#XFLD: Parent task chain label
parentChainLabel=Chuỗi tác vụ mẹ:
#XFLD: Parent task chain unauthorized
Unauthorized=Không được quyền xem
#XFLD: Parent task chain label
parentTaskLabel=Tác vụ mẹ:
#XTXT: Task status
NOT_TRIGGERED=Không được kích hoạt
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Nhập chế độ toàn màn hình
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Thoát chế độ toàn màn hình
#XTXT: Close Task log details right panel
closeRightColumn=Đóng phần
#XTXT: Sort Text
sortTxt=Sắp xếp
#XTXT: Filter Text
filterTxt=Bộ lọc
#XTXT: Filter by text to show list of filters applied
filterByTxt=Lọc theo
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Nhiều hơn 5 phút
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Nhiều hơn 15 phút
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Nhiều hơn 1 giờ
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Giờ vừa qua
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=24 giờ qua
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Tháng trước
#XTXT: Messages title text
messagesText=Thông báo

#XTXT Statistics information message
statisticsInfo=Không thể tạo thống kê cho các bảng từ xa có quyền truy cập dữ liệu ''Đã sao chép''. Để tạo thống kê, hãy gỡ bỏ dữ liệu đã sao chép trong Bộ giám sát chi tiết bảng từ xa.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Đi đến Bộ giám sát chi tiết bảng từ xa

#XTXT: Repair latest failed run label
retryRunLabel=Chạy thử lại gần nhất
#XTXT: Repair failed run label
retryRun=Chạy thử lại
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Lần chạy thử lại chuỗi tác vụ đã bắt đầu
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Lần chạy thử lại chuỗi tác vụ không thành công
#XMSG: Task chain child elements name
taskChainRetryChildObject=Tác vụ {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Tác vụ mới
#XFLD Analyzed View
analyzedView=Màn hình được phân tích
#XFLD Metrics
Metrics=Số đo
#XFLD Partition Metrics
PartitionMetrics=Số đo phân vùng
#XFLD Entities
Messages=Nhật ký tác vụ
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Chưa có phân vùng nào được xác định
#XTXT: Description message for empty partition data
partitionEmptyDescText=Tạo phân vùng bằng cách chỉ định tiêu chí phân bổ khối lượng dữ liệu lớn hơn thành các phần nhỏ hơn, dễ quản lý.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Nhật ký chưa có sẵn
#XTXT: Description message for empty runs data
runsEmptyDescText=Khi bạn bắt đầu một hoạt động mới (Tải ảnh chụp nhanh mới, bắt đầu trình phân tích màn hình...) bạn sẽ thấy các nhật ký có liên quan tại đây.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=Cấu hình nút elastic compute {0} không được duy trì tương ứng. Vui lòng kiểm tra định nghĩa của bạn.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Quy trình để thêm nút tính toán linh hoạt {0} không thành công.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Quá trình tạo và kích hoạt bản sao cho đối tượng nguồn ''{0}''.''{1}'' trong nút tính toán đàn hồi {2} đã bắt đầu.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Quá trình gỡ bỏ bản sao cho đối tượng nguồn ''{0}''.''{1}'' khỏi nút tính toán đàn hồi {2} đã bắt đầu.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Quá trình tạo và kích hoạt bản sao cho đối tượng nguồn ''{0}''.''{1}'' không thành công.
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Quá trình gỡ bỏ bản sao cho đối tượng nguồn ''{0}''.''{1}'' không thành công.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Đã bắt đầu tạo lộ trình tính toán truy vấn phân tích của vùng dữ liệu {0} đối với nút tính toán linh hoạt {1}.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Tạo lộ trình tính toán truy vấn phân tích của vùng dữ liệu {0} đối với nút tính toán linh hoạt tương ứng không thành công.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Đã bắt đầu tạo lại lộ trình tính toán truy vấn phân tích của vùng dữ liệu {0} trở lại từ nút tính toán linh hoạt {1}.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Tạo lại lộ trình tính toán truy vấn phân tích của vùng dữ liệu {0} trở lại người điều phối không thành công.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Chuỗi tác vụ {0} cho nút tính toán linh hoạt tương ứng {1} đã được kích hoạt.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Tạo chuỗi tác vụ cho nút tính toán linh hoạt {0} không thành công.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=Quá trình cung cấp nút tính toán đàn hồi {0} đã bắt đầu với kế hoạch định cỡ đã cho: vCPU: {1}, bộ nhớ (GiB): {2} và kích cỡ lưu trữ(GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=Nút tính toán linh hoạt {0} đã được cung cấp.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=Nút tính toán linh hoạt {0} đã được hủy cấp.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=Hoạt động không được phép. Vui lòng dừng nút tính toán linh hoạt {0} và khởi động lại nút đó để cập nhật kế hoạch định cỡ.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Quy trình xóa nút tính toán linh hoạt {0} không thành công.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=Lần chạy nút tính toán linh hoạt hiện tại {0} đã hết thời gian.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=Kiểm tra trạng thái nút tính toán linh hoạt {0} đang tiến hành...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=Tạo chuỗi tác vụ cho nút tính toán linh hoạt {0} bị khóa, do đó chuỗi {1} có thể vẫn đang chạy.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=Đối tượng nguồn ''{0}''.''{1}'' được sao chép làm phần phụ thuộc của màn hình ''{2}''.''{3}''.
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Không thể sao chép bảng ''{0}''.''{1}'' vì quá trình sao chép bảng hàng không được dùng nữa.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Đã vượt quá nút tính toán linh hoạt tối đa cho mỗi thực thể SAP HANA Cloud.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=Thao tác chạy cho nút tính toán đàn hồi {0} vẫn đang được tiến hành.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Do vấn đề kỹ thuật nên việc xóa bản sao cho bảng nguồn {0} đã bị dừng. Vui lòng thử lại sau.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=Do vấn đề kỹ thuật nên việc tạo bản sao cho bảng nguồn {0} đã bị dừng. Vui lòng thử lại sau.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Không thể bắt đầu nút tính toán linh hoạt vì đã đạt đến giới hạn sử dụng hoặc vì chưa phân bổ khối giờ tính toán.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Đang tải chuỗi tác vụ và chuẩn bị chạy tổng cộng {0} tác vụ là một phần của chuỗi này.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Tác vụ xung đột đang chạy
#XMSG: Replication will change
txt_replication_change=Kiểu sao chép sẽ được thay đổi.
txt_repl_viewdetails=Xem chi tiết

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Có vẻ như đã có lỗi trong lần thử chạy gần đây nhất do tác vụ trước đó không thành công trước khi có thể tạo kế hoạch.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=Vùng dữ liệu "{0}" bị khóa.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=Hoạt động {0} yêu cầu bảng cục bộ {1} cần được triển khai.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=Hoạt động {0} yêu cầu Thu thập chênh lệch cần bật lên cho bảng cục bộ {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=Hoạt động {0} yêu cầu bảng cục bộ {1} để lưu trữ dữ liệu trong kho đối tượng.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=Hoạt động {0} yêu cầu bảng cục bộ {1} để lưu trữ dữ liệu trong cơ sở dữ liệu.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Bắt đầu xóa các bản ghi đã xóa cho bảng cục bộ {0}.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Bắt đầu xóa tất cả bản ghi đã xóa cho bảng cục bộ {0}.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Bắt đầu xóa bản ghi cho bảng cục bộ {0} theo điều kiện lọc {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Đã xảy ra lỗi khi xóa bản ghi đã xóa cho bảng cục bộ {0}.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Đã xảy ra lỗi khi xóa tất cả bản ghi đã xóa cho bảng cục bộ {0}.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Xóa tất cả các bản ghi đã được xử lý đầy đủ với Kiểu thay đổi "Đã xóa" vốn đã tồn tại hơn {0} ngày.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Xóa tất cả các bản ghi đã được xử lý đầy đủ với Kiểu thay đổi "Đã xóa" vốn đã tồn tại hơn {0}.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} bản ghi đã xóa.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} bản ghi đã được đánh dấu để xóa.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=Xóa bản ghi đã xóa cho bảng cục bộ {0} hoàn tất.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Xóa tất cả bản ghi cho bảng cục bộ {0} hoàn tất.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Bắt đầu đánh dấu các bản ghi là "Đã xóa" cho bảng cục bộ {0}.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Bắt đầu đánh dấu bản ghi là "Đã xóa" cho bảng cục bộ {0} theo điều kiện lọc {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Việc đánh dấu bản ghi là "Đã xóa" cho bảng cục bộ {0} đã hoàn tất.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Các thay đổi dữ liệu đang tạm thời tải vào bảng {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Các thay đổi dữ liệu đang tạm thời được tải vào bảng {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Các thay đổi dữ liệu được xử lý và xóa khỏi bảng {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Chi tiết kết nối.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Đang bắt đầu tối ưu hóa Bảng cục bộ (tập tin).
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Đã xảy ra lỗi trong khi tối ưu hóa Bảng cục bộ (tập tin).
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Đã xảy ra lỗi. Quá trình tối ưu hóa Bảng cục bộ (tập tin) đã dừng lại.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Đang tối ưu hóa Bảng cục bộ (tập tin)...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=Bảng cục bộ (tập tin) đã được tối ưu hóa.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=Bảng cục bộ (tập tin) được tối ưu hóa.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=Bảng được tối ưu hóa với các cột theo thứ tự Z: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Đã xảy ra lỗi. Quá trình xén bớt Bảng cục bộ (tập tin) đã dừng lại.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Đang xén bớt Bảng cục bộ (tập tin)...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=Vị trí bộ đệm đầu vào cho Bảng cục bộ (Tập tin) đã hủy bỏ.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Đang bắt đầu làm sạch (xóa tất cả các bản ghi được xử lý đầy đủ) Bảng cục bộ (tập tin).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Đã xảy ra lỗi trong khi làm sạch Bảng cục bộ (tập tin).
#XMSG: Task log message
LTF_VACUUM_STOPPED=Đã xảy ra lỗi. Quá trình làm sạch Bảng cục bộ (tập tin) đã dừng lại.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Đang làm sạch Bảng cục bộ (tập tin)...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=Quá trình làm sạch hoàn tất.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Xóa tất cả các bản ghi được xử lý đầy đủ đã tồn tại lâu hơn {0} ngày.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Xóa tất cả các bản ghi được xử lý đầy đủ đã tồn tại lâu hơn {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Đang bắt đầu sáp nhập các bản ghi mới vào Bảng cục bộ (tập tin).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Đang bắt đầu sáp nhập các bản ghi mới với Bảng cục bộ (tập tin) bằng cách sử dụng phần cài đặt "Tự động sáp nhập dữ liệu".
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Bắt đầu sáp nhập các bản ghi mới với Bảng cục bộ (Tập tin). Tác vụ này được khởi tạo thông qua Yêu cầu hợp nhất API.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Đang sáp nhập các bản ghi mới vào Bảng cục bộ (tập tin).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Đã xảy ra lỗi trong khi sáp nhập các bản ghi mới vào Bảng cục bộ (tập tin).
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=Bảng cục bộ (tập tin) được sáp nhập.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Đã xảy ra lỗi. Quá trình sáp nhập Bảng cục bộ (tập tin) đã dừng lại.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=Việc hợp nhất Bảng cục bộ (Tập tin) đã không thành công do có lỗi, nhưng thao tác này đã thành công một phần và một số dữ liệu đã được hợp nhất.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Đã xảy ra lỗi hết thời gian chờ. Hoạt động {0} đã chạy trong {1} giờ.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=Quá trình thực thi không đồng bộ không bắt đầu được do mức tải hệ thống cao. Hãy mở ''Công cụ giám sát hệ thống'' và kiểm tra các tác vụ đang chạy.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=Quá trình thực thi không đồng bộ đã bị hủy.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=Tác vụ {0} đã chạy trong giới hạn bộ nhớ từ {1} đến {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=Tác vụ {0} đã chạy với ID tài nguyên {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=Tính năng tìm và thay thế đã bắt đầu trong Bảng cục bộ (Tập tin).
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Tính năng tìm và thay thế đã hoàn tất trong Bảng cục bộ (Tập tin).
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Tính năng tìm và thay thế không thành công trong Bảng cục bộ (Tập tin).

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Đã xảy ra lỗi. Quá trình cập nhật thống kê cho Bảng cục bộ (tập tin) đã bị dừng lại.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=Tác vụ không thành công do lỗi hết bộ nhớ trên cơ sở dữ liệu SAP HANA.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=Tác vụ không thành công do Từ chối kiểm soát tiếp nhận SAP HANA.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=Tác vụ không thành công do có quá nhiều kết nối SAP HANA đang hoạt động.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=Không thể thực hiện hoạt động Thử lại vì chỉ cho phép thử lại trên tác vụ cha của chuỗi tác vụ lồng nhau.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Nhật ký của các tác vụ con không thành công không còn có sẵn cho Thử lại để tiếp tục.


####Metrics Labels

performanceOptimized=Tối ưu hóa hiệu suất
memoryOptimized=Tối ưu hóa bộ nhớ

JOB_EXECUTION=Thực hiện công việc
EXECUTION_MODE=Chế độ chạy
NUMBER_OF_RECORDS_OVERALL=Số lượng bản ghi ổn định
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Số lượng bản ghi được đọc từ nguồn từ xa
RUNTIME_MS_REMOTE_EXECUTION_TIME=Thời gian xử lý nguồn từ xa
MEMORY_CONSUMPTION_GIB=Bộ nhớ cao nhất của SAP HANA
NUMBER_OF_PARTITIONS=Số phân vùng
MEMORY_CONSUMPTION_GIB_OVERALL=Bộ nhớ cao nhất của SAP HANA
NUMBER_OF_PARTITIONS_LOCKED=Số phân vùng bị khóa
PARTITIONING_COLUMN=Cột phân vùng
HANA_PEAK_CPU_TIME=Tổng thời gian CPU của SAP HANA
USED_IN_DISK=Mức lưu trữ đã sử dụng
INPUT_PARAMETER_PARAMETER_VALUE=Tham số nhập
INPUT_PARAMETER=Tham số nhập
ECN_ID=Tên nút tính toán linh hoạt 

DAC=Kiểm soát quyền truy cập dữ liệu
YES=Có
NO=Không
noofrecords=Số bản ghi
partitionpeakmemory=Bộ nhớ cao nhất của SAP HANA
value=Giá trị
metricsTitle=Số đo ({0})
partitionmetricsTitle=Phân vùng ({0})
partitionLabel=Phân vùng
OthersNotNull=Giá trị không được bao gồm trong phạm vi
OthersNull=Giá trị rỗng
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Thiết lập được sử dụng để thực hiện ổn định dữ liệu lần cuối:
#XMSG: Message for input parameter name
inputParameterLabel=Tham số nhập
#XMSG: Message for input parameter value
inputParameterValueLabel=Giá trị
#XMSG: Message for persisted data
inputParameterPersistedLabel=Được ổn định lúc
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Xóa dữ liệu
REMOVE_DELETED_RECORDS=Loại bỏ bản ghi đã xóa
MERGE_FILES=Sáp nhập các tập tin
OPTIMIZE_FILES=Tối ưu hóa các tập tin
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Xem trong màn hình SAP BW Bridge

ANALYZE_PERFORMANCE=Phân tích hiệu suất làm việc
CANCEL_ANALYZE_PERFORMANCE=Hủy phân tích hiệu suất làm việc

#XFLD: Label for frequency column
everyLabel=Mỗi
#XFLD: Plural Recurrence text for Hour
hoursLabel=Giờ
#XFLD: Plural Recurrence text for Day
daysLabel=Ngày
#XFLD: Plural Recurrence text for Month
monthsLabel=Tháng
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Phút

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Hướng dẫn khắc phục sự cố Tính ổn định của màn hình</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Quy trình bị hết bộ nhớ. Không thể lưu trữ dữ liệu cho màn hình "{0}". Tham khảo Cổng thông tin trợ giúp để biết thêm thông tin về lỗi hết bộ nhớ. Hãy cân nhắc kiểm tra để Trình phân tích màn hình để phân tích màn hình về độ phức tạp của việc sử dụng bộ nhớ.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Không áp dụng
OPEN_BRACKET=(
CLOSE_BRACKET=)
