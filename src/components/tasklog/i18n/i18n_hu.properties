
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Naplórészletek
#XFLD: Header
TASK_LOGS=Feladatnaplók ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Futások ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Részletek megtekintése
#XFLD: Button text
STOP=Futás leállítása
#XFLD: Label text
RUN_START=Legutóbbi futás kezdete
#XFLD: Label text
RUN_END=Legutóbbi futás vége
#XFLD: Label text
RUNTIME=Időtartam
#XTIT: Count for Messages
txtDetailMessages=Üzenetek ({0})
#XFLD: Label text
TIME=Időbélyeg
#XFLD: Label text
MESSAGE=Üzenet
#XFLD: Label text
TASK_STATUS=Kategória
#XFLD: Label text
TASK_ACTIVITY=Tevékenység
#XFLD: Label text
RUN_START_DETAILS=Kezdés
#XFLD: Label text
RUN_END_DETAILS=Befejezés
#XFLD: Label text
LOGS=Futások
#XFLD: Label text
STATUS=Állapot
#XFLD: Label text
RUN_STATUS=Futás állapota
#XFLD: Label text
Runtime=Időtartam
#XFLD: Label text
RuntimeTooltip=Időtartam (óó : pp : mm)
#XFLD: Label text
TRIGGEREDBY=Kiváltó
#XFLD: Label text
TRIGGEREDBYNew=Futtatta
#XFLD: Label text
TRIGGEREDBYNewImp=Futást indította
#XFLD: Label text
EXECUTIONTYPE=Végrehajtás típusa
#XFLD: Label text
EXECUTIONTYPENew=Futás típusa
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Szülőlánc tere
#XFLD: Refresh tooltip
TEXT_REFRESH=Frissítés
#XFLD: view Details link
VIEW_ERROR_DETAILS=Részletek megtekintése
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=További részletek letöltése
#XMSG: Download completed
downloadStarted=A letöltés megkezdődött
#XMSG: Error while downloading content
errorInDownload=Hiba történt a letöltés közben.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Részletek megtekintése
#XBTN: cancel button of task details dialog
TXT_CANCEL=Mégse
#XBTN: back button from task details
TXT_BACK=Vissza
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=A feladat elvégezve
#XFLD: Log message with failed status
MSG_LOG_FAILED=A feladat sikertelen
#XFLD: Master and detail table with no data
No_Data=Nincs adat
#XFLD: Retry tooltip
TEXT_RETRY=Újra
#XFLD: Cancel Run label
TEXT_CancelRun=Futás megszakítása
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Sikertelen betöltés törlése
#XMSG:button copy sql statement
txtSQLStatement=SQL-utasítás másolása
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Távolilekérdezés-figyelő megnyitása
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=A távoli SQL-utasítások megjelenítéséhez kattintson a Távolilekérdezés-figyelő megnyitása lehetőségre.
#XMSG:button ok
txtOk=OK
#XMSG: button close
txtClose=Bezárás
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=A futásmegszakítási művelet elindult a(z) {0} objektumnál.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=A futásmegszakítási művelet nem sikerült a(z) {0} objektumnál.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=A futásmegszakítási művelet már nem lehetséges a(z) {0} objektumnál, mert a replikálási állapot módosult.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Nincs ''Fut'' állapotú feladatnapló.
#XMSG: message for conflicting task
Task_Already_Running=Már fut egy ezzel ütköző feladat a(z) {0} objektumnál.
#XFLD: Label for no task log with running state title
actionInfo=Műveletinfó
#XMSG Copied to clipboard
copiedToClip=Vágólapra másolva
#XFLD copy
Copy=Másolás
#XFLD copy correlation ID
CopyCorrelationID=Korrelációazonosító másolása
#XFLD Close
Close=Bezárás
#XFLD: show more Label
txtShowMore=Több
#XFLD: message Label
messageLabel=Üzenet:
#XFLD: details Label
detailsLabel=Részletek:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Végrehajtódó \r\n SQL-utasítás:
#XFLD:statementId Label
statementIdLabel=Utasításazonosító:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Távoli SQL-utasítások \r\n száma:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Utasítások száma
#XFLD: Space Label
txtSpaces=Tér
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Nem engedélyezett terek
#XFLD: Privilege Error Text
txtPrivilegeError=Nincs jogosultsága ezen adatok megtekintéséhez.
#XFLD: Label for Object Header
DATA_ACCESS=Adathozzáférés
#XFLD: Label for Object Header
SCHEDULE=Ütemezés
#XFLD: Label for Object Header
DETAILS=Részletek
#XFLD: Label for Object Header
LATEST_UPDATE=Legutóbbi frissítés
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Legutóbbi módosítás (forrás)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Frissítés gyakorisága
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Ütemezés szerinti gyakoriság
#XFLD: Label for Object Header
NEXT_RUN=Következő futás
#XFLD: Label for Object Header
CONNECTION=Kapcsolat
#XFLD: Label for Object Header
DP_AGENT=Adatátadási ügynök
#XFLD: Label for Object Header
USED_IN_MEMORY=Tárolóként használt memória (MiB)
#XFLD: Label for Object Header
USED_DISK=Tárolóként használt lemez (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Méret a memóriában (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Méret a lemezen (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Rekordok száma

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Sikertelenre állítás
SET_TO_FAILED_ERR=Ez a feladat futott, de a felhasználó SIKERTELEN állapotra állította.
#XFLD: Label for stopped failed
FAILLOCKED=A futás már folyamatban van
#XFLD: sub status STOPPED
STOPPED=Leállítva
STOPPED_ERR=Ezt a feladatot leállították, de nem történt visszaállítás.
#XFLD: sub status CANCELLED
CANCELLED=Megszakítva
CANCELLED_ERR=Ennek a feladatnak megszakították a futását az elindulása után. Ebben az esetben az adatok vissza lettek állítva abba az állapotba, amelyben a feladat futtatásának elindítása előtt voltak.
#XFLD: sub status LOCKED
LOCKED=Zárolva
LOCKED_ERR=Már futott ugyanez a feladat, ezért ez a feladat nem futtatható párhuzamosan a meglévő feladat végrehajtásával.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Feladatkivétel
TASK_EXCEPTION_ERR=Ez a feladat meghatározatlan hibába ütközött a végrehajtása során.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Feladatvégrehajtási kivétel
TASK_EXECUTE_EXCEPTION_ERR=Ez a feladat hibába ütközött a végrehajtása során.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Nem engedélyezett
UNAUTHORIZED_ERR=Nem sikerült hitelesíteni a felhasználót, vagy zárolt, vagy törölték.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Tiltott
FORBIDDEN_ERR=A hozzárendelt felhasználó nem rendelkezik a feladat végrehajtásához szükséges jogosultságokkal.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Nincs kiváltva
FAIL_NOT_TRIGGERED_ERR=Nem sikerült végrehajtani ezt a feladatot, mert nem üzemel a rendszer, vagy az adatbázisrendszer egy része nem volt elérhető a tervezett végrehajtás időpontjában. Várjon a feladat legközelebbi beütemezett végrehajtási idejéig, vagy ütemezze át a feladatot.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Ütemezés törölve
SCHEDULE_CANCELLED_ERR=Belső hiba miatt nem sikerült végrehajtani ezt a feladatot. Forduljon az SAP-támogatáshoz, és adja meg nekik a korrelációazonosítót és az időbélyegzőt a feladat naplójának részletes adataiból.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Korábbi futás van folyamatban
SUCCESS_SKIPPED_ERR=Ennek a feladatnak nem indult el a végrehajtása, mert még folyamatban van ugyanennek a feladatnak egy korábbi futtatása.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Hiányzó tulajdonos
FAIL_OWNER_MISSING_ERR=Nem sikerült végrehajtani ezt a feladatot, mert nincs hozzárendelve rendszerfelhasználó. Rendeljen hozzá rendszerfelhasználót a feladathoz.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Nem érhető el hozzájárulás
FAIL_CONSENT_NOT_AVAILABLE_ERR=Nem engedélyezte az SAP-nak, hogy feladatláncokat futtasson vagy adatintegrációs feladatokat ütemezzen be a nevében. Jelölje be a megadott beállítást a hozzájáruláshoz.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Lejárt a hozzájárulás
FAIL_CONSENT_EXPIRED_ERR=Lejárt az engedély, ami lehetővé teszi az SAP-nak, hogy feladatláncokat futtasson vagy adatintegrációs feladatokat ütemezzen be az Ön nevében. A hozzájárulás megújításához jelölje be a megadott beállítást.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Érvénytelenített hozzájárulás
FAIL_CONSENT_INVALIDATED_ERR=Nem sikerült végrehajtani ezt a feladatot, minden bizonnyal azért, mert változott a bérlő identitásszolgáltató-konfigurációja. Ebben az esetben nem futtathatók és nem ütemezhetők be új feladatok az érintett felhasználó nevében. Ha a hozzárendelt felhasználó még létezik az új identitásszolgáltatóban, vonja vissza az ütemezési hozzájárulást, és adja meg újra. Ha a hozzárendelt felhasználó már nem létezik, rendeljen hozzá új feladattulajdonost, és adja meg a szükséges feladatütemezési hozzájárulást. További információ a következő SAP-jegyzetben található: https://launchpad.support.sap.com/#/notes/3089828.
TASK_EXECUTOR_ERROR=Feladat-végrehajtó
TASK_EXECUTOR_ERROR_ERR=Ez a feladat valószínűleg a lépések végrehajtásának előkészítésekor belső hibába ütközött, és nem sikerült elindítani a feladatot.
PREREQ_NOT_MET=Nem teljesül az előfeltétel
PREREQ_NOT_MET_ERR=Ez a feladat a definíciójával kapcsolatos problémák miatt nem futtatható. Például nincs üzembe helyezve az objektum, egy feladatlánc körkörös logikát tartalmaz, vagy érvénytelen egy nézet SQL-je.
RESOURCE_LIMIT_ERROR=Erőforráskorlát-hiba
RESOURCE_LIMIT_ERROR_ERR=Jelenleg nem lehet elvégezni a feladatot, mert nem érhető el elég erőforrás, vagy a meglévők foglaltak.
FAIL_CONSENT_REFUSED_BY_UMS=A hozzájárulás megtagadva
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Ez a feladat nem hajtható végre beütemezett futásokban vagy feladatláncokban, mert változott egy felhasználó identitásszolgáltató-konfigurációja a bérlőben. További információt a következő SAP-jegyzetben talál: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Beütemezve
#XFLD: status text
SCHEDULEDNew=Állandó
#XFLD: status text
PAUSED=Szünetel
#XFLD: status text
DIRECT=Közvetlen
#XFLD: status text
MANUAL=Manuális
#XFLD: status text
DIRECTNew=Egyszerű
#XFLD: status text
COMPLETED=Befejeződött
#XFLD: status text
FAILED=Sikertelen
#XFLD: status text
RUNNING=Fut
#XFLD: status text
none=Egyik sem
#XFLD: status text
realtime=Valós idejű
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Alfeladat
#XFLD: New Data available in the file
NEW_DATA=Új adatok

#XFLD: text for values shown in column Replication Status
txtOff=Ki
#XFLD: text for values shown in column Replication Status
txtInitializing=Inicializálás
#XFLD: text for values shown in column Replication Status
txtLoading=Betöltés
#XFLD: text for values shown in column Replication Status
txtActive=Aktív
#XFLD: text for values shown in column Replication Status
txtAvailable=Elérhető
#XFLD: text for values shown in column Replication Status
txtError=Hiba
#XFLD: text for values shown in column Replication Status
txtPaused=Szünetel
#XFLD: text for values shown in column Replication Status
txtDisconnected=Nincs kapcsolat
#XFLD: text for partially Persisted views
partiallyPersisted=Részben véglegesítve

#XFLD: activity text
REPLICATE=Replikálás
#XFLD: activity text
REMOVE_REPLICATED_DATA=Replikált adatok eltávolítása
#XFLD: activity text
DISABLE_REALTIME=Valós idejű adatreplikáció letiltása
#XFLD: activity text
REMOVE_PERSISTED_DATA=Véglegesített adatok eltávolítása
#XFLD: activity text
PERSIST=Véglegesítés
#XFLD: activity text
EXECUTE=Végrehajtás
#XFLD: activity text
TASKLOG_CLEANUP=Feladatnapló ürítése
#XFLD: activity text
CANCEL_REPLICATION=Replikáció megszakítása
#XFLD: activity text
MODEL_IMPORT=Modellimportálás
#XFLD: activity text
NONE=Egyik sem
#XFLD: activity text
CANCEL_PERSISTENCY=Véglegesítés megszakítása
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Nézet elemzése
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Nézetelemző megszakítása

#XFLD: severity text
INFORMATION=Információ
#XFLD: severity text
SUCCESS=Sikeres
#XFLD: severity text
WARNING=Figyelmeztetés
#XFLD: severity text
ERROR=Hiba
#XFLD: text for values shown for Ascending sort order
SortInAsc=Rendezés növekvő sorrendben
#XFLD: text for values shown for Descending sort order
SortInDesc=Rendezés csökkenő sorrendben
#XFLD: filter text for task log columns
Filter=Szűrés
#XFLD: object text for task log columns
Object=Objektum
#XFLD: space text for task log columns
crossSpace=Tér

#XBUT: label for remote data access
REMOTE=Távoli
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Replikált (valós idejű)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Replikált (pillanatkép)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=A valós idejű replikáció hiba miatt nem használható. A hiba javítása után az Újra művelettel folythatja a valós idejű replikációt.
ERROR_MSG=A valós idejű replikáció hiba miatt nem használható.
RETRY_FAILED_ERROR=A folyamat ismétlése hiba miatt nem sikerült.
LOG_INFO_DETAILS=Nem generálódnak naplók, amikor valós idejű módban replikálják az adatokat. A megjelenő naplók a korábbi műveletekre vonatkoznak.

#XBUT: Partitioning label
partitionMenuText=Particionálás
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Partíció létrehozása
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Partíció szerkesztése
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Partíció törlése
#XFLD: Initial text
InitialPartitionText=Partíciók definiálása feltételek meghatározásával, amelyek alapján kisebb halmazokra oszthatók a nagy adathalmazok.
DefinePartition=Partíciók definiálása
#XFLD: Message text
partitionChangedInfo=A partíció definíciója a legutóbbi módosítás óta módosult. A módosítások a következő adatbetöltéskor lesznek alkalmazva.
#XFLD: Message text
REAL_TIME_WARNING=A particionálás csak új pillanatkép betöltésekor van alkalmazva, valós idejű replikáció esetén nem.
#XFLD: Message text
loadSelectedPartitions=Az adatok véglegesítése megkezdődött {0} kiválasztott partícióinál
#XFLD: Message text
loadSelectedPartitionsError=Nem sikerült véglegesíteni az adatokat {0} kiválasztott partícióinál
#XFLD: Message text
viewpartitionChangedInfo=A legutóbbi véglegesítési futás óta megváltozott a partíciódefiníció. A módosítások alkalmazása érdekében a következő adatátvétel teljes pillanatképből fog állni, a zárolt partíciókat is beleértve. A teljes adatátvétel befejezése után futtathat adatokat az egyes partícióknál.
#XFLD: Message text
viewpartitionChangedInfoLocked=A legutóbbi véglegesítési futás óta megváltozott a partíciódefiníció. A módosítások alkalmazása érdekében a következő adatátvétel teljes pillanatképből fog állni, kivéve a zárolt és a változatlan partíciókat. Az adatátvétel befejezése után újra átveheti a kiválasztott partíciókat.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Táblareplikáció
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Replikáció beütemezése
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Ütemezés létrehozása
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Ütemezés szerkesztése
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Ütemezés törlése
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Új pillanatkép betöltése
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Adatreplikáció indítása
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Replikált adatok eltávolítása
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Valós idejű hozzáférés engedélyezése
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Valós idejű adatreplikáció engedélyezése
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Valós idejű adatreplikáció letiltása
#XFLD: Message for replicate table action
replicateTableText=Táblareplikáció
#XFLD: Message for replicate table action
replicateTableTextNew=Adatreplikáció
#XFLD: Message to schedule task
scheduleText=Ütemezés
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Nézetvéglegesítés
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Adatvéglegesítés
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Új pillanatkép betöltése
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Adatvéglegesítés indítása
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Véglegesített adatok eltávolítása
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Particionált feldolgozás
#XBUT: Label for scheduled replication
scheduledTxt=Beütemezve
#XBUT: Label for statistics button
statisticsTxt=Statisztika
#XBUT: Label for create statistics
createStatsTxt=Statisztika készítése
#XBUT: Label for edit statistics
editStatsTxt=Statisztika szerkesztése
#XBUT: Label for refresh statistics
refreshStatsTxt=Statisztika frissítése
#XBUT: Label for delete statistics
dropStatsTxt=Statisztika törlése
#XMSG: Create statistics success message
statsSuccessTxt={0} típusú statisztika készítése elindítva a következőnél: {1}.
#XMSG: Edit statistics success message
statsEditSuccessTxt={0} típusú statisztikára történő módosítás elindítva a következőnél: {1}.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Statisztika frissítése elindítva a következőhöz: {0}.
#XMSG: Drop statistics success message
statsDropSuccessTxt=A statisztika sikeresen törölve lett ({0})
#XMSG: Create statistics error message
statsCreateErrorTxt=Hiba a statisztika létrehozásakor
#XMSG: Edit statistics error message
statsEditErrorTxt=Hiba a statisztika módosításakor
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Hiba a statisztika frissítésekor
#XMSG: Drop statistics error message
statsDropErrorTxt=Hiba a statisztika törlésekor
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Biztosan törli az adatstatisztikát?
startPersistencyAdvisorLabel=Nézetelemző indítása

#Partition related texts
#XFLD: Label for Column
column=Oszlop
#XFLD: Label for No of Partition
noOfPartitions=Partíciók száma
#XFLD: Label for Column
noOfParallelProcess=Párhuzamos folyamatok száma
#XFLD: Label text
noOfLockedPartition=Zárolt partíciók száma
#XFLD: Label for Partition
PARTITION=Partíciók
#XFLD: Label for Column
AVAILABLE=Elérhető
#XFLD: Statistics Label
statsLabel=Statisztika
#XFLD: Label text
COLUMN=Oszlop:
#XFLD: Label text
PARALLEL_PROCESSES=Párhuzamos folyamatok:
#XFLD: Label text
Partition_Range=Partíciótartomány
#XFLD: Label text
Name=Név
#XFLD: Label text
Locked=Zárolva
#XFLD: Label text
Others=EGYÉB
#XFLD: Label text
Delete=Törlés
#XFLD: Label text
LoadData=Kiválasztott partíciók betöltése
#XFLD: Label text
LoadSelectedData=Kiválasztott partíciók betöltése
#XFLD: Confirmation text
LoadNewPersistenceConfirm=A művelet az összes nem zárolt és módosított partícióhoz új pillanatképet tölt be, nem csak a kiválasztottakhoz. Folytatja?
#XFLD: Label text
Continue=Folytatás

#XFLD: Label text
PARTITIONS=Partíciók
#XFLD: Label text
ADD_PARTITIONS=+ Partíció hozzáadása
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Partíció hozzáadása
#XFLD: Label text
deleteRange=Partíció törlése
#XFLD: Label text
LOW_PLACE_HOLDER=Adja meg az alsó határértéket
#XFLD: Label text
HIGH_PLACE_HOLDER=Adja meg a felső határértéket
#XFLD: tooltip text
lockedTooltip=Partíció zárolása a kezdeti adatátvétel után

#XFLD: Button text
Edit=Szerkesztés
#XFLD: Button text
CANCEL=Mégse

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Legutóbb statisztikafrissítés
#XFLD: Statistics Fields
STATISTICS=Statisztika

#XFLD:Retry label
TEXT_Retry=Újra
#XFLD:Retry label
TEXT_Retry_tooltip=A valós idejű replikáció újbóli megkísérlése a hiba javítása után.
#XFLD: text retry
Retry=Megerősítés
#XMG: Retry confirmation text
retryConfirmationTxt=Az utolsó valós idejű replikáció hiba miatt megszakadt.\n Erősítse meg, hogy javította a hibát, és a valós idejű replikáció újraindítható.
#XMG: Retry success text
retrySuccess=A folyamat újbóli futtatása sikeresen elindítva.
#XMG: Retry fail text
retryFail=A folyamat újbóli futtatása nem sikerült.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Statisztika készítése
#XMSG: activity message for edit statistics
DROP_STATISTICS=Statisztika törlése
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Statisztika frissítése
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Statisztika szerkesztése
#XMSG: Task log message started task
taskStarted=A(z) {0} feladat elindult.
#XMSG: Task log message for finished task
taskFinished=A(z) {0} feladat {1} állapottal fejeződött be.
#XMSG: Task log message for finished task with end time
taskFinishedAt=A(z) {0} feladat {1} állapottal fejeződött be ekkor: {2}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=A(z) {0} feladat bemeneti paraméterekkel rendelkezik.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=A(z) {0} feladat váratlan hibával fejeződött be. A feladat állapota következő lett: {1}.
#XMSG: Task log message for failed task
failedToEnd=Nem sikerült {0} állapotra állítani, vagy nem sikerült eltávolítani a zárolást.
#XMSG: Task log message
lockNotFound=Nem lehet befejezni a folyamatot, mert hiányzik a zárolás. Lehet, hogy törölték a feladatot.
#XMSG: Task log message failed task
failedOverwrite=A(z) {0} feladatot már {1} zárolja. Ezért sikertelen a következő hibaüzenettel: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Egy másik feladat átvette ennek a feladatnak a zárolását.
#XMSG: Task log message failed takeover
failedTakeover=Nem sikerült átvenni egy meglévő feladatot.
#XMSG: Task log message successful takeover
successTakeover=A hátramaradt zárolást fel kellett oldani. Az új zárolás beállítva a feladatnál.
#XMSG: Tasklog Dialog Details
txtDetails=A futás során feldolgozott távoli utasítások megjelenítéséhez nyissa meg a távolilekérdezés-figyelőt a partícióspecifikus üzenetek részleteiben.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=A távoli SQL-utasítások törlődtek az adatbázisból, és nem jeleníthetők meg.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Nem jeleníthetők meg olyan távoli lekérdezések, amelyekhez más terekkel való kapcsolatok vannak hozzárendelve. Nyissa meg a távolilekérdezés-figyelőt, és szűrje őket utasításazonosító alapján.
#XMSG: Task log message for parallel check error
parallelCheckError=A feladat nem dolgozható fel, mert egy másik folyamat fut, és már zárolja ezt a feladatot.
#XMSG: Task log message for parallel running task
parallelTaskRunning=Már fut egy ezzel ütköző feladat.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus={0} állapot futtatás közben, korrelációazonosító: {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=A hozzárendelt felhasználó nem rendelkezik a feladat végrehajtásához szükséges jogosultságokkal.

#XBUT: Label for open in Editor
openInEditor=Megnyitás a szerkesztőben
#XBUT: Label for open in Editor
openInEditorNew=Megnyitás az Adatmodell-szerkesztőben
#XFLD:Run deails label
runDetails=Futás részletei
#XFLD: Label for Logs
Logs=Naplók
#XFLD: Label for Settings
Settings=Beállítások
#XFLD: Label for Save button
Save=Mentés
#XFLD: Label for Standard
Standard_PO=Teljesítményre optimalizált (ajánlott)
#XFLD: Label for Hana low memory processing
HLMP_MO=Memóriára optimalizált
#XFLD: Label for execution mode
ExecutionMode=Futtatási mód
#XFLD: Label for job execution
jobExecution=Feldolgozási mód
#XFLD: Label for Synchronous
syncExec=Szinkron
#XFLD: Label for Asynchronous
asyncExec=Aszinkron
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Alapértelmezés használata (Aszinkron; később változhat)
#XMSG: Save settings success
saveSettingsSuccess=Az SAP HANA végrehajtási módja módosult.
#XMSG: Save settings failure
saveSettingsFailed=Az SAP HANA végrehajtási módjának módosítása sikertelen.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=A feladat végrehajtása módosult.
#XMSG: Job Execution change failed
jobExecSettingFailed=A feladat végrehajtásának módosítása sikertelen.
#XMSG: Text for Type
typeTxt=Típus
#XMSG: Text for Monitor
monitorTxt=Figyelő
#XMSG: Text for activity
activityTxt=Tevékenység
#XMSG: Text for metrics
metricsTxt=Mérőszámok
#XTXT: Text for Task chain key
TASK_CHAINS=Feladatlánc
#XTXT: Text for View Key
VIEWS=Nézet
#XTXT: Text for remote table key
REMOTE_TABLES=Távoli tábla
#XTXT: Text for Space key
SPACE=Tér
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Alkalmazkodó számítási csomópont
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Replikációs folyamat
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Intelligens keresés
#XTXT: Text for Local Table
LOCAL_TABLE=Helyi tábla
#XTXT: Text for Data flow key
DATA_FLOWS=Adatáramlás
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=SQL-szkript-eljárás
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=BW-folyamatlánc
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Megtekintés a figyelőben
#XTXT: Task List header text
taskListHeader=Feladatlista ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Nem lehet lehívni az adatáramlás korábbi futásainak mérőszámait.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=A teljes futás részletei jelenleg nem tölthetők be. Próbáljon frissíteni.
#XFLD: Label text for the Metrices table header
metricesColLabel=Operátor megnevezése
#XFLD: Label text for the Metrices table header
metricesType=Típus
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Rekordok száma
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Feladatlánc futtatása
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=A feladatlánc futtatása elindult.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=A feladatlánc futtatása elindult: {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Nem sikerült futtatni a feladatláncot.
#XTXT: Execute button label
runLabel=Futtatás
#XTXT: Execute button label
runLabelNew=Futás indítása
#XMSG: Filter Object header
chainsFilteredTableHeader=A következő objektum szerint szűrve: {0}
#XFLD: Parent task chain label
parentChainLabel=Szülőfeladatlánc:
#XFLD: Parent task chain unauthorized
Unauthorized=Nincs engedély a megtekintéshez
#XFLD: Parent task chain label
parentTaskLabel=Szülőfeladat:
#XTXT: Task status
NOT_TRIGGERED=Nincs kiváltva
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Váltás teljes képernyős módra
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Kilépés a teljes képernyős módból
#XTXT: Close Task log details right panel
closeRightColumn=Szakasz bezárása
#XTXT: Sort Text
sortTxt=Rendezés
#XTXT: Filter Text
filterTxt=Szűrés
#XTXT: Filter by text to show list of filters applied
filterByTxt=Szűrés szempontja
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Több mint 5 perc
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Több mint 15 perc
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Több mint 1 óra
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Az elmúlt óra
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Az elmúlt 24 óra
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Elmúlt hónap
#XTXT: Messages title text
messagesText=Üzenetek

#XTXT Statistics information message
statisticsInfo=Nem készíthető statisztika "Replikálva" adathozzáféréssel rendelkező távoli táblákhoz. Statisztika készítéséhez távolítsa el a replikált adatokat a távolitábla-részletek figyelőjében.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Távolitábla-részletek figyelőjének megnyitása

#XTXT: Repair latest failed run label
retryRunLabel=Legutóbbi futtatás újrapróbálása
#XTXT: Repair failed run label
retryRun=Futtatás újrapróbálása
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=A feladatlánc újbóli futtatása elindult
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=A feladatlánc újbóli futtatása sikertelen
#XMSG: Task chain child elements name
taskChainRetryChildObject={0} feladat
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Új feladat
#XFLD Analyzed View
analyzedView=Elemzett nézet
#XFLD Metrics
Metrics=Mérőszámok
#XFLD Partition Metrics
PartitionMetrics=Partíció-mérőszámok
#XFLD Entities
Messages=Feladatnapló
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Még nincsenek megadva partíciók.
#XTXT: Description message for empty partition data
partitionEmptyDescText=Hozzon létre partíciókat úgy, hogy feltételeket határoz meg a nagy adatmennyiségek kisebb, kezelhetőbb részekre bontásához.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Még nem érhetők el naplók
#XTXT: Description message for empty runs data
runsEmptyDescText=Új tevékenység (új pillanatkép betöltése, nézetelemző indítása stb.) elindításakor itt fognak megjelenni a kapcsolódó naplók.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=Nincs megfelelően karbantartva a(z) {0} alkalmazkodó számítási csomópont konfigurációja. Ellenőrizze a definíciót.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Nem sikerült feldolgozni a következő alkalmazkodó számítási csomópontot: {0}.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Elindult a replika létrehozása és aktiválása a(z) {0}.{1} forrásobjektumhoz a(z) {2} alkalmazkodó számítási csomópontban.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Elindult a replika eltávolítása a(z) {0}.{1} forrásobjektumhoz a(z) {2} alkalmazkodó számítási csomópontból.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Nem sikerült létrehozni és aktiválni a replikát a(z) {0}.{1} forrásobjektumhoz.
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Nem sikerült eltávolítani a(z) {0}.{1} forrásobjektumhoz tartozó replikát.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Megkezdődött a(z) {0} tér elemzésilekérdezés-számításainak átirányítása a(z) {1} alkalmazkodó számítási csomópontba.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Nem sikerült átirányítani a(z) {0} tér elemzésilekérdezés-számításait az alkalmazkodó számítási csomópontba.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Megkezdődött a(z) {0} tér elemzésilekérdezés-számításainak visszairányítása a(z) {1} alkalmazkodó számítási csomópontból.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Nem sikerült visszairányítani a(z) {0} tér elemzésilekérdezés-számításait a koordinátorhoz.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=A(z) {0} feladatlánc kiváltva a vonatkozó alkalmazkodó számítási csomóponthoz ({1}).
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Nem sikerült feladatláncot generálni a(z) {0} alkalmazkodó számítási csomóponthoz.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=A(z) {0} alkalmazkodó számítási csomópont átadása elindult a megadott méretezési tervvel: vCPU-k: {1}, memória (GiB): {2} tárolóméret (GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=A(z) {0} alkalmazkodó számítási csomópont már át van adva.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=A(z) {0} alkalmazkodó számítási csomópont átadása már vissza van vonva.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=A művelet nem engedélyezett. Állítsa le és indítsa újra a(z) {0} alkalmazkodó számítási csomópontot a méretezési terv frissítéséhez.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Nem sikerült eltávolítani a következő alkalmazkodó számítási csomópontot: {0}.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=A(z) {0} alkalmazkodó számítási csomópont aktuális futása időtúllépés miatt megszakadt.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=A(z) {0} alkalmazkodó számítási csomópont állapotának ellenőrzése folyamatban van...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=A(z) {0} alkalmazkodó számítási csomóponthoz tartozó feladatlánc-generálás zárolva van, mert lehet, hogy még fut a(z) {1} lánc.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=A(z) {0}.{1} forrásobjektum a(z) {2}.{3} nézet függőségeként van replikálva.
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Nem lehet replikálni a(z) {0}.{1} táblát, mert a sortábla-replikációs funkció ki lett vezetve.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Túllépte az alkalmazkodó számítási csomópontok SAP HANA Cloud-példányonkénti maximális számát.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=A(z) {0} alkalmazkodó számítási csomópontnál futó művelet még folyamatban van.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Technikai problémák miatt leállt a replika törlése a(z) {0} forrástáblánál. Próbálkozzon újra később.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=Technikai problémák miatt leállt a replika létrehozása a(z) {0} forrástáblánál. Próbálkozzon újra később.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Nem lehet alkalmazkodó számítási csomópontot indítani, mert elérte a használati korlátot, vagy mert még nincsenek hozzárendelve nem számítási blokkórák.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Feladatlánc betöltése és a lánchoz tartozó összesen {0} feladat futtatásának előkészítése.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Már fut egy ezzel ütköző feladat
#XMSG: Replication will change
txt_replication_change=A replikáció típusa módosulni fog.
txt_repl_viewdetails=Részletek megtekintése

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Úgy tűnik, hiba történt a legutóbbi futás újbóli megkísérlésekor, mert az előző feladatfuttatás már a terv generálása előtt meghiúsult.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=A(z) {0} tér zárolva van.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=A(z) {0} tevékenységhez üzembe kell helyezni a(z) {1} helyi táblát.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=A(z) {0} tevékenységhez be kell kapcsolni a deltarögzítést a(z) {1} helyi tábla esetében.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=A(z) {0} tevékenységhez a(z) {1} helyi táblának az objektumtárban kell tárolnia az adatokat.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=A(z) {0} tevékenységhez a(z) {1} helyi táblának az adatbázisban kell tárolnia az adatokat.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Törölt rekordok eltávolításának megkezdése a(z) {0} helyi táblánál.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Az összes rekordok törlésének megkezdése a(z) {0} helyi táblánál.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=A rekordokok törlésének megkezdése a(z) {0} helyi táblánál a következő szűrési feltétel szerint: {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Hiba történt a törölt rekordok eltávolításakor a(z) {0} helyi táblánál.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Hiba történt az összes rekord törlésekor a(z) {0} helyi táblánál.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Azon Törölve módosítástípusú teljesen feldolgozott rekordok törlése, amelyek {0} napnál régebbiek.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Azon Törölve módosítástípusú teljesen feldolgozott rekordok törlése, amelyek régebbiek, mint {0}.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} rekord törlődött.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} rekord lett megjelölve törlésre.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=A törölt rekordok eltávolítása befejeződött a(z) {0} helyi táblánál.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Az összes rekord törlése befejeződött a(z) {0} helyi táblánál.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=A rekordok töröltként való megjelölésének megkezdése a(z) {0} helyi táblánál.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=A rekordokok töröltként való megjelölésének megkezdése a(z) {0} helyi táblánál a következő szűrési feltétel szerint: {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=A rekordok töröltként való megjelölése befejeződött a(z) {0} helyi táblánál.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Az adatváltozások átmenetileg betöltődnek a következő táblába: {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Az adatváltozások átmenetileg betöltve a következő táblába: {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Az adatváltozások feldolgozása megtörtént, és törlődtek a(z) {0} táblából.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Kapcsolat részletei.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Helyi tábla (fájl) optimalizálásának indítása.
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Hiba történt a helyi tábla (fájl) optimalizálásakor.
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Hiba történt. A helyi tábla (fájl) optimalizálása leállt.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Helyi tábla (fájl) optimalizálása...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=A helyi tábla (fájl) optimalizálva.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=A helyi tábla (fájl) optimalizálódik.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=A tábla optimalizálva Z-rendezésű oszlopokkal: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Hiba történt. A helyi tábla (fájl) csonkolása leállt.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Helyi tábla (fájl) csonkolása...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=A bemeneti puffer helye a Helyi tábla (fájl) esetén törölve.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Helyi tábla (fájl) kitisztításának (az összes teljesen feldolgozott rekord törlésének) indítása.
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Hiba történt a helyi tábla (fájl) kitisztításakor.
#XMSG: Task log message
LTF_VACUUM_STOPPED=Hiba történt. A helyi tábla (fájl) kitisztítása leállt.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Helyi tábla (fájl) kitisztítása...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=A kitisztítás befejeződött.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO={0} napnál régebbi teljesen feldolgozott rekordok törlése.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Ennél régebbi teljesen feldolgozott rekordok törlése: {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Új rekordok összevonásának indítása a helyi táblával (fájllal).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Új rekordok összevonásának indítása a helyi táblával (fájllal) az Adatok automatikus összevonása beállítás használatával.
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Új rekordok összevonásának indítása a helyi táblával (fájllal). Ez a feladat az API összevonási kérelem útján lett kezdeményezve.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Új rekordok összevonása a helyi táblával (fájllal).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Hiba történt az új rekordok összevonásakor a helyi táblával (fájllal).
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=A helyi tábla (fájl) összevonva.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Hiba történt. A helyi tábla (fájl) összevonása leállt.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=A helyi tábla (fájl) összevonása hiba miatt nem sikerült, de a művelet részben sikeres volt, és egyes adatok összevonása már megtörtént.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Időtúllépési hiba történt. A(z) {0} tevékenység  {1} órája fut.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=Nagy rendszerterhelés miatt nem sikerült elindítani az aszinkron végrehajtást. Nyissa meg a Rendszerfigyelőt, és tekintse át a futó feladatokat.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=Az aszinkron végrehajtás törölve.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=A(z) {0} feladat a következő memóriakorlátokkal futott: {1} és {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=A(z) {0} feladat a következő erőforrás-azonosítóval futott: {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=A keresés és csere elkezdődött a helyi táblában (fájlban)
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=A keresés és csere befejeződött a helyi táblában (fájlban)
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=A keresés és csere nem sikerült a helyi táblában (fájlban)

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Hiba történt. A helyi tábla (fájl) statisztikájának frissítése leállt.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=A feladat sikertelen, mert a hibaüzenet szerint elfogyott a memória az SAP HANA-adatbázisban.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=A feladat sikertelen, mert az SAP HANA bejutás-ellenőrzése elutasította.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=A feladat túl sok aktív SAP HANA-kapcsolat miatt sikertelen.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=Az Újra műveletet nem sikerült végrehajtani, mert beágyazott feladatlánc esetén csak a szülőnél engedélyezett az újrapróbálkozás.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=A sikertelen gyermekfeladatok naplói már nem érhetők el az újrapróbálkozáshoz.


####Metrics Labels

performanceOptimized=Teljesítményre optimalizált
memoryOptimized=Memóriára optimalizált

JOB_EXECUTION=Feladat-végrehajtás
EXECUTION_MODE=Futtatási mód
NUMBER_OF_RECORDS_OVERALL=Véglegesített rekordok száma
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Távoli forrásból beolvasott rekordok száma
RUNTIME_MS_REMOTE_EXECUTION_TIME=Távoli forrásbeli feldolgozási idő
MEMORY_CONSUMPTION_GIB=SAP HANA memória-csúcsérték
NUMBER_OF_PARTITIONS=Partíciók száma
MEMORY_CONSUMPTION_GIB_OVERALL=SAP HANA memória-csúcsérték
NUMBER_OF_PARTITIONS_LOCKED=Zárolt partíciók száma
PARTITIONING_COLUMN=Particionálási oszlop
HANA_PEAK_CPU_TIME=SAP HANA CPU-idő összesen
USED_IN_DISK=Felhasznált tárolómennyiség
INPUT_PARAMETER_PARAMETER_VALUE=Bemeneti paraméter
INPUT_PARAMETER=Bemeneti paraméter
ECN_ID=Alkalmazkodó számítási csomópont neve

DAC=Adathozzáférés-vezérlők
YES=Igen
NO=Nem
noofrecords=Rekordok száma
partitionpeakmemory=SAP HANA memória-csúcsérték
value=Érték
metricsTitle=Mérőszámok ({0})
partitionmetricsTitle=Partíciók ({0})
partitionLabel=Partíció
OthersNotNull=Tartományokon kívüli értékek
OthersNull=Null értékek
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=A legutóbbi adatvéglegesítési futáshoz használt beállítások:
#XMSG: Message for input parameter name
inputParameterLabel=Bemeneti paraméter
#XMSG: Message for input parameter value
inputParameterValueLabel=Érték
#XMSG: Message for persisted data
inputParameterPersistedLabel=Véglegesítés időpontja
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Adatok törlése
REMOVE_DELETED_RECORDS=Törölt rekordok eltávolítása
MERGE_FILES=Fájlok összevonása
OPTIMIZE_FILES=Fájlok optimalizálása
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Megtekintés az SAP BW-híd monitorában

ANALYZE_PERFORMANCE=Teljesítmény elemzése
CANCEL_ANALYZE_PERFORMANCE=Teljesítményelemzés megszakítása

#XFLD: Label for frequency column
everyLabel=Gyakoriság
#XFLD: Plural Recurrence text for Hour
hoursLabel=óra
#XFLD: Plural Recurrence text for Day
daysLabel=nap
#XFLD: Plural Recurrence text for Month
monthsLabel=hónap
#XFLD: Plural Recurrence text for Minutes
minutesLabel=perc

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Nézetvéglegesítés hibaelhárítási útmutatója</a>
#XTXT TEXT for view persistency guide link
OOMMessage=A folyamat kifogyott a memóriából. Nem lehet véglegesíteni a(z) {0} nézet adatait. További információért keresse fel a túl kevés memóriával kapcsolatos hibákról szóló szakaszt a Help Portalon. Érdemes lehet a Nézetelemzővel elemezni a nézetet a memóriafelhasználás összetettségének tekintetében.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Nem alkalmazható
OPEN_BRACKET=(
CLOSE_BRACKET=)
