
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Detalhes de registo
#XFLD: Header
TASK_LOGS=Registos de tarefa ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Execuções ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Ver detalhes
#XFLD: Button text
STOP=Parar execução
#XFLD: Label text
RUN_START=Início da última execução
#XFLD: Label text
RUN_END=Fim da última execução
#XFLD: Label text
RUNTIME=Duração
#XTIT: Count for Messages
txtDetailMessages=Mensagens ({0})
#XFLD: Label text
TIME=Carimbo de data/hora
#XFLD: Label text
MESSAGE=Mensagem
#XFLD: Label text
TASK_STATUS=Categoria
#XFLD: Label text
TASK_ACTIVITY=Atividade
#XFLD: Label text
RUN_START_DETAILS=Início
#XFLD: Label text
RUN_END_DETAILS=Fim
#XFLD: Label text
LOGS=Execuções
#XFLD: Label text
STATUS=Estado
#XFLD: Label text
RUN_STATUS=Estado de execução
#XFLD: Label text
Runtime=Duração
#XFLD: Label text
RuntimeTooltip=Duração (hh : mm : ss)
#XFLD: Label text
TRIGGEREDBY=Acionado por
#XFLD: Label text
TRIGGEREDBYNew=Executado por
#XFLD: Label text
TRIGGEREDBYNewImp=Execução iniciada por
#XFLD: Label text
EXECUTIONTYPE=Tipo de execução
#XFLD: Label text
EXECUTIONTYPENew=Tipo de execução
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Espaço de cadeia superior
#XFLD: Refresh tooltip
TEXT_REFRESH=Atualizar
#XFLD: view Details link
VIEW_ERROR_DETAILS=Ver detalhes
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Transferir detalhes adicionais
#XMSG: Download completed
downloadStarted=Transferência iniciada
#XMSG: Error while downloading content
errorInDownload=Ocorreu um erro ao transferir.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Ver detalhes
#XBTN: cancel button of task details dialog
TXT_CANCEL=Cancelar
#XBTN: back button from task details
TXT_BACK=Voltar
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Tarefa concluída
#XFLD: Log message with failed status
MSG_LOG_FAILED=Tarefa falhada
#XFLD: Master and detail table with no data
No_Data=Sem dados
#XFLD: Retry tooltip
TEXT_RETRY=Repetir
#XFLD: Cancel Run label
TEXT_CancelRun=Cancelar execução
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Limpeza falhou ao carregar
#XMSG:button copy sql statement
txtSQLStatement=Copiar instrução SQL
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Abrir monitor de consulta remota
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=Para apresentar as instruções SQL remotas, clique em "Abrir monitor de consulta remota".
#XMSG:button ok
txtOk=Ok
#XMSG: button close
txtClose=Fechar
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=A ação Cancelar execução para o objeto "{0}" foi iniciada.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=A ação Cancelar execução para o objeto "{0}" falhou.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=A ação Cancelar execução para o objeto ''"{0}" já não é possível porque o estado da replicação foi alterado.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Nenhum registo de tarefa tem o estado Em execução.
#XMSG: message for conflicting task
Task_Already_Running=Uma tarefa em conflito já está em execução para o objeto "{0}".
#XFLD: Label for no task log with running state title
actionInfo=Informação da ação
#XMSG Copied to clipboard
copiedToClip=Copiado para a área de transferência
#XFLD copy
Copy=Copiar
#XFLD copy correlation ID
CopyCorrelationID=Copiar ID de correlação
#XFLD Close
Close=Fechar
#XFLD: show more Label
txtShowMore=Mostrar mais
#XFLD: message Label
messageLabel=Mensagem:
#XFLD: details Label
detailsLabel=Detalhes:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=A executar instrução \r\n SQL:
#XFLD:statementId Label
statementIdLabel=ID da instrução:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Número de instruções SQL \r\n remotas:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Número de instruções
#XFLD: Space Label
txtSpaces=Espaço
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Espaços não autorizados
#XFLD: Privilege Error Text
txtPrivilegeError=Não tem privilégios suficientes para visualizar estes dados.
#XFLD: Label for Object Header
DATA_ACCESS=Acesso a dados
#XFLD: Label for Object Header
SCHEDULE=Agendar
#XFLD: Label for Object Header
DETAILS=Detalhes
#XFLD: Label for Object Header
LATEST_UPDATE=Última atualização
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Última alteração (origem)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Frequência de atualização
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Frequência agendada
#XFLD: Label for Object Header
NEXT_RUN=Próxima execução
#XFLD: Label for Object Header
CONNECTION=Ligação
#XFLD: Label for Object Header
DP_AGENT=Agente de aprovisionamento de dados
#XFLD: Label for Object Header
USED_IN_MEMORY=Memória utilizada para armazenamento (MiB)
#XFLD: Label for Object Header
USED_DISK=Disco utilizado para armazenamento (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Tamanho na memória interna (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Tamanho no disco (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Número de registos

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Definir como Falhado
SET_TO_FAILED_ERR=Esta tarefa estava a ser executada mas o utilizador definiu o status desta tarefa como FALHADO.
#XFLD: Label for stopped failed
FAILLOCKED=Execução já em curso
#XFLD: sub status STOPPED
STOPPED=Parado
STOPPED_ERR=Esta tarefa foi parada, mas não foi efetuada nenhuma reversão.
#XFLD: sub status CANCELLED
CANCELLED=Cancelado
CANCELLED_ERR=Esta execução de tarefa foi cancelada depois de ter começado. Neste caso, os dados foram revertidos e restaurados para o estado existente antes do acionamento inicial da execução da tarefa.
#XFLD: sub status LOCKED
LOCKED=Bloqueado
LOCKED_ERR=A mesma tarefa já estava a ser executada, por isso, esta tarefa não pode ser executada em paralelo com uma execução de tarefa existente.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Exceção de tarefa
TASK_EXCEPTION_ERR=Esta tarefa encontrou um erro não especificado durante a execução.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Exceção na execução da tarefa
TASK_EXECUTE_EXCEPTION_ERR=Esta tarefa encontrou um erro durante a execução.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Não autorizado
UNAUTHORIZED_ERR=Não foi possível autenticar o utilizador. Ele foi bloqueado ou eliminado.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Proibido
FORBIDDEN_ERR=O utilizador atribuído não tem os privilégios necessários para executar esta tarefa.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Não acionado
FAIL_NOT_TRIGGERED_ERR=Não foi possível executar esta tarefa devido a uma falha do sistema ou alguma parte do sistema de base de dados não está disponível no momento da execução planeada. Aguarde a próxima hora de execução da tarefa agendada ou agende a tarefa novamente.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Agendamento cancelado
SCHEDULE_CANCELLED_ERR=Não foi possível executar esta tarefa devido a um erro interno. Contacte o suporte SAP e forneça o ID de correlação e o carimbo de data/hora a partir das informações detalhadas de registo desta tarefa.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Execução anterior em curso
SUCCESS_SKIPPED_ERR=A execução desta tarefa não foi acionada, porque uma execução anterior da mesma tarefa ainda está em curso.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Proprietário em falta
FAIL_OWNER_MISSING_ERR=Não foi possível executar esta tarefa, porque não tem um utilizador do sistema atribuído. Atribua um utilizador proprietário à tarefa.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Consentimento indisponível
FAIL_CONSENT_NOT_AVAILABLE_ERR=Não autorizou a SAP a executar cadeias de tarefas ou a agendar tarefas de integração de dados em seu nome. Selecione a opção fornecida para dar o seu consentimento.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Consentimento expirado
FAIL_CONSENT_EXPIRED_ERR=A autorização que permite que a SAP execute cadeias de tarefas ou que agende tarefas de integração de dados em seu nome expirou. Selecione a opção fornecida para renovar o seu consentimento.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Consentimento invalidado
FAIL_CONSENT_INVALIDATED_ERR=Não foi possível executar esta tarefa, normalmente, devido a uma modificação na configuração de fornecedor de identidade do inquilino. Nesse caso, não é possível executar ou agendar nenhuma nova tarefa em nome do utilizador afetado. Se o utilizador afetado ainda existir no novo IdP, revogue o consentimento de agendamento e depois conceda-o novamente. Se o utilizador já não existir, atribua um novo proprietário de tarefa e forneça o consentimento de agendamento de tarefa necessário. Para mais informações, consulte a seguinte nota SAP: https://launchpad.support.sap.com/#/notes/3089828.
TASK_EXECUTOR_ERROR=Executor de tarefa
TASK_EXECUTOR_ERROR_ERR=Esta tarefa encontrou um erro interno, possivelmente, durante os passos de preparação, e não é possível iniciar a tarefa.
PREREQ_NOT_MET=Pré-requisito não cumprido
PREREQ_NOT_MET_ERR=Não foi possível executar esta tarefa, devido a problemas na sua definição. Por exemplo, o objeto não está implementado, uma cadeia de tarefas contém lógica circular ou o SQL de uma visão é inválido.
RESOURCE_LIMIT_ERROR=Erro de limite de recursos
RESOURCE_LIMIT_ERROR_ERR=De momento, não é possível executar a tarefa por não haver recursos suficientes disponíveis ou estarem ocupados.
FAIL_CONSENT_REFUSED_BY_UMS=Consentimento recusado
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Não foi possível executar esta tarefa, em execuções agendadas ou cadeias de tarefas, devido a uma alteração na configuração de fornecedor de identidade de um utilizador no inquilino. Para mais informações, consulte a seguinte nota SAP: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Agendado
#XFLD: status text
SCHEDULEDNew=Permanente
#XFLD: status text
PAUSED=Interrompido
#XFLD: status text
DIRECT=Direto
#XFLD: status text
MANUAL=Manual
#XFLD: status text
DIRECTNew=Simples
#XFLD: status text
COMPLETED=Concluído
#XFLD: status text
FAILED=Falhado
#XFLD: status text
RUNNING=Em execução
#XFLD: status text
none=Nenhum
#XFLD: status text
realtime=Tempo real
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Subtarefa
#XFLD: New Data available in the file
NEW_DATA=Novos dados

#XFLD: text for values shown in column Replication Status
txtOff=Desligado
#XFLD: text for values shown in column Replication Status
txtInitializing=A inicializar
#XFLD: text for values shown in column Replication Status
txtLoading=A carregar
#XFLD: text for values shown in column Replication Status
txtActive=Ativo
#XFLD: text for values shown in column Replication Status
txtAvailable=Disponível
#XFLD: text for values shown in column Replication Status
txtError=Erro
#XFLD: text for values shown in column Replication Status
txtPaused=Interrompido
#XFLD: text for values shown in column Replication Status
txtDisconnected=Desligado
#XFLD: text for partially Persisted views
partiallyPersisted=Persistido parcialmente

#XFLD: activity text
REPLICATE=Replicar
#XFLD: activity text
REMOVE_REPLICATED_DATA=Remover dados replicados
#XFLD: activity text
DISABLE_REALTIME=Desativar replicação de dados em tempo real
#XFLD: activity text
REMOVE_PERSISTED_DATA=Remover dados persistentes
#XFLD: activity text
PERSIST=Persistir
#XFLD: activity text
EXECUTE=Executar
#XFLD: activity text
TASKLOG_CLEANUP=Tacklog_Cleanup
#XFLD: activity text
CANCEL_REPLICATION=Cancelar replicação
#XFLD: activity text
MODEL_IMPORT=Importação de modelo
#XFLD: activity text
NONE=Nenhum
#XFLD: activity text
CANCEL_PERSISTENCY=Cancelar persistência
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Analisar vista
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Cancelar analisador de vistas

#XFLD: severity text
INFORMATION=Informação
#XFLD: severity text
SUCCESS=Sucesso
#XFLD: severity text
WARNING=Aviso
#XFLD: severity text
ERROR=Erro
#XFLD: text for values shown for Ascending sort order
SortInAsc=Ordenação ascendente
#XFLD: text for values shown for Descending sort order
SortInDesc=Ordenação descendente
#XFLD: filter text for task log columns
Filter=Filtrar
#XFLD: object text for task log columns
Object=Objeto
#XFLD: space text for task log columns
crossSpace=Espaço

#XBUT: label for remote data access
REMOTE=Remoto
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Replicado (em tempo real)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Replicado (instantâneo)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=A replicação em tempo real está bloqueada devido a um erro. Assim que o erro for corrigido, pode utilizar a ação "Repetir" para continuar a replicação em tempo real.
ERROR_MSG=A replicação em tempo real está bloqueada devido a um erro.
RETRY_FAILED_ERROR=Processo de repetição falhado com um erro.
LOG_INFO_DETAILS=Não são gerados registos quando os dados são replicados em modo de tempo real. Os registos apresentados estão relacionados com ações anteriores.

#XBUT: Partitioning label
partitionMenuText=Partição
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Criar partição
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Editar partição
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Eliminar partição
#XFLD: Initial text
InitialPartitionText=Define partições, especificando critérios para dividir conjuntos de dados grandes em conjuntos mais pequenos.
DefinePartition=Definir partições
#XFLD: Message text
partitionChangedInfo=A definição de partição foi alterada desde a última replicação. As alterações serão aplicadas no próximo carregamento de dados.
#XFLD: Message text
REAL_TIME_WARNING=A partição só é aplicada ao carregar um novo instantâneo. Se não foi aplicada para a replicação em tempo real.
#XFLD: Message text
loadSelectedPartitions=Persistência de dados iniciada para as partições selecionadas de "{0}"
#XFLD: Message text
loadSelectedPartitionsError=Falha ao executar persistência de dados para as partições selecionadas de "{0}"
#XFLD: Message text
viewpartitionChangedInfo=A definição de partição foi alterada desde a última execução de persistência. Para aplicar as alterações, o próximo carregamento de dados será um instantâneo completo, incluindo as partições bloqueadas. Assim que este carregamento de dados estiver concluído, poderá executar dados para partições individuais.
#XFLD: Message text
viewpartitionChangedInfoLocked=A definição de partição foi alterada desde a última execução de persistência. Para aplicar as alterações, o próximo carregamento de dados será um instantâneo completo, exceto os intervalos de partição bloqueados e não alterados. Assim que este carregamento estiver concluído, poderá carregar novamente partições selecionadas.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Replicação de tabela
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Agendar replicação
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Criar agenda
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Editar agenda
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Eliminar agenda
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Carregar novo instantâneo
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Iniciar replicação de dados
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Remover dados replicados
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Ativar acesso em tempo real
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Ativar replicação de dados em tempo real
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Desativar replicação de dados em tempo real
#XFLD: Message for replicate table action
replicateTableText=Replicação de tabela
#XFLD: Message for replicate table action
replicateTableTextNew=Replicação de dados
#XFLD: Message to schedule task
scheduleText=Agendar
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Persistência de vista
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Persistência de dados
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Carregar novo instantâneo
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Iniciar persistência de dados
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Remover dados persistentes
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Processamento particionado
#XBUT: Label for scheduled replication
scheduledTxt=Agendado
#XBUT: Label for statistics button
statisticsTxt=Estatísticas
#XBUT: Label for create statistics
createStatsTxt=Criar estatísticas
#XBUT: Label for edit statistics
editStatsTxt=Editar estatísticas
#XBUT: Label for refresh statistics
refreshStatsTxt=Atualizar estatísticas
#XBUT: Label for delete statistics
dropStatsTxt=Eliminar estatísticas
#XMSG: Create statistics success message
statsSuccessTxt=Iniciada criação de estatísticas do tipo {0} para {1}.
#XMSG: Edit statistics success message
statsEditSuccessTxt=Iniciada alteração do tipo de estatísticas para {0} para {1}.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Iniciada atualização de estatísticas para {0}.
#XMSG: Drop statistics success message
statsDropSuccessTxt=Estatísticas eliminadas com êxito para {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=Erro ao criar estatísticas
#XMSG: Edit statistics error message
statsEditErrorTxt=Erro ao alterar estatísticas
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Erro ao atualizar estatísticas
#XMSG: Drop statistics error message
statsDropErrorTxt=Erro ao eliminar estatísticas
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Tem a certeza de que pretende remover as estatísticas de dados?
startPersistencyAdvisorLabel=Iniciar analisador de vistas

#Partition related texts
#XFLD: Label for Column
column=Coluna
#XFLD: Label for No of Partition
noOfPartitions=Nº de partições
#XFLD: Label for Column
noOfParallelProcess=Nº de processos paralelos
#XFLD: Label text
noOfLockedPartition=Nº de partições bloqueadas
#XFLD: Label for Partition
PARTITION=Partições
#XFLD: Label for Column
AVAILABLE=Disponível
#XFLD: Statistics Label
statsLabel=Estatísticas
#XFLD: Label text
COLUMN=Coluna:
#XFLD: Label text
PARALLEL_PROCESSES=Processos paralelos:
#XFLD: Label text
Partition_Range=Intervalo de partição
#XFLD: Label text
Name=Nome
#XFLD: Label text
Locked=Bloqueado
#XFLD: Label text
Others=OUTROS
#XFLD: Label text
Delete=Eliminar
#XFLD: Label text
LoadData=Carregar partições selecionadas
#XFLD: Label text
LoadSelectedData=Carregar partições selecionadas
#XFLD: Confirmation text
LoadNewPersistenceConfirm=Isto irá carregar um novo instantâneo para todas as partições desbloqueadas e alteradas, não só para as que selecionou. Pretende continuar?
#XFLD: Label text
Continue=Continuar

#XFLD: Label text
PARTITIONS=Partições
#XFLD: Label text
ADD_PARTITIONS=+ Adicionar partição
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Adicionar partição
#XFLD: Label text
deleteRange=Eliminar partição
#XFLD: Label text
LOW_PLACE_HOLDER=Introduzir valor baixo
#XFLD: Label text
HIGH_PLACE_HOLDER=Introduzir valor alto
#XFLD: tooltip text
lockedTooltip=Bloquear partição após carregamento inicial

#XFLD: Button text
Edit=Editar
#XFLD: Button text
CANCEL=Cancelar

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Última atualização de estatísticas
#XFLD: Statistics Fields
STATISTICS=Estatísticas

#XFLD:Retry label
TEXT_Retry=Repetir
#XFLD:Retry label
TEXT_Retry_tooltip=Repita após o erro de replicação em tempo real após estar resolvido.
#XFLD: text retry
Retry=Confirmar
#XMG: Retry confirmation text
retryConfirmationTxt=A última replicação em tempo real terminou com um erro.\n Confirme que o erro está corrigido e que a replicação em tempo real pode ser reiniciada.
#XMG: Retry success text
retrySuccess=Processo de repetição iniciado com êxito.
#XMG: Retry fail text
retryFail=Processo de repetição falhado.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Criar estatísticas
#XMSG: activity message for edit statistics
DROP_STATISTICS=Eliminar estatísticas
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Atualizar estatísticas
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Editar estatísticas
#XMSG: Task log message started task
taskStarted=A tarefa {0} foi iniciada.
#XMSG: Task log message for finished task
taskFinished=A tarefa {0} terminou com o estado {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=A tarefa {0} terminou às {2} com o estado {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=A tarefa {0} tem parâmetros de entrada.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=A tarefa {0} terminou com um erro inesperado. O estado da tarefa foi definido como {1}.
#XMSG: Task log message for failed task
failedToEnd=Falha ao definir o estado como {0} ou falha ao remover o bloqueio.
#XMSG: Task log message
lockNotFound=Não conseguimos finalizar o processo, pois o bloqueio está em falta: a tarefa pode ter sido cancelada.
#XMSG: Task log message failed task
failedOverwrite=A tarefa {0} já foi bloqueada por {1}. Por isso, falhou com o seguinte erro: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=O bloqueio desta tarefa foi assumido por outra tarefa.
#XMSG: Task log message failed takeover
failedTakeover=Falha ao assumir uma tarefa existente.
#XMSG: Task log message successful takeover
successTakeover=Foi necessário libertar o bloqueio restante. O novo bloqueio para esta tarefa está definido.
#XMSG: Tasklog Dialog Details
txtDetails=As instruções remotas processadas durante a execução podem ser visualizadas abrindo o monitor de consulta remota nos detalhes das mensagens específicas de partição.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=As instruções SQL remotas foram eliminadas da base de dados e não podem ser apresentadas.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=As consultas remotas com ligações atribuídas a outros espaços não podem ser apresentadas. Vá para o Monitor de consulta remota e utilize o ID da instrução para filtrá-las.
#XMSG: Task log message for parallel check error
parallelCheckError=A tarefa não pode ser processada porque outra tarefa está em execução e está a bloquear esta tarefa.
#XMSG: Task log message for parallel running task
parallelTaskRunning=Uma tarefa em conflito já está em execução.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Estado {0} durante execução com ID de correlação {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=O utilizador atribuído não tem os privilégios necessários para executar esta tarefa.

#XBUT: Label for open in Editor
openInEditor=Abrir no editor
#XBUT: Label for open in Editor
openInEditorNew=Abrir no gerador de dados
#XFLD:Run deails label
runDetails=Detalhes da execução
#XFLD: Label for Logs
Logs=Registos
#XFLD: Label for Settings
Settings=Definições
#XFLD: Label for Save button
Save=Guardar
#XFLD: Label for Standard
Standard_PO=Otimizado para desempenho (recomendado)
#XFLD: Label for Hana low memory processing
HLMP_MO=Otimizado para memória
#XFLD: Label for execution mode
ExecutionMode=Modo de execução
#XFLD: Label for job execution
jobExecution=Modo de processamento
#XFLD: Label for Synchronous
syncExec=Síncrono
#XFLD: Label for Asynchronous
asyncExec=Assíncrono
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Utilizar predefinição (assíncrono, pode ser alterado no futuro)
#XMSG: Save settings success
saveSettingsSuccess=O modo de execução SAP HANA foi alterado.
#XMSG: Save settings failure
saveSettingsFailed=Falha ao alterar o modo de execução SAP HANA.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Execução da tarefa alterada.
#XMSG: Job Execution change failed
jobExecSettingFailed=Falha ao alterar a execução da tarefa.
#XMSG: Text for Type
typeTxt=Tipo
#XMSG: Text for Monitor
monitorTxt=Monitorizar
#XMSG: Text for activity
activityTxt=Atividade
#XMSG: Text for metrics
metricsTxt=Métricas
#XTXT: Text for Task chain key
TASK_CHAINS=Cadeia de tarefas
#XTXT: Text for View Key
VIEWS=Vista
#XTXT: Text for remote table key
REMOTE_TABLES=Tabela remota
#XTXT: Text for Space key
SPACE=Espaço
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Nó de computação elástico
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Fluxo de replicação
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Pesquisa inteligente
#XTXT: Text for Local Table
LOCAL_TABLE=Tabela local
#XTXT: Text for Data flow key
DATA_FLOWS=Fluxo de dados
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=Procedimento de script SQL
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=Cadeia de processos BW
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Vista no monitor
#XTXT: Task List header text
taskListHeader=Lista de tarefas ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Não é possível obter métricas para execuções históricas de um fluxo de dados.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=O detalhe de execução completo não está a ser carregado neste momento. Tente atualizar.
#XFLD: Label text for the Metrices table header
metricesColLabel=Etiqueta de operador
#XFLD: Label text for the Metrices table header
metricesType=Tipo
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Contagem de registos
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Executar a cadeia de tarefas
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=A execução da cadeia de tarefas foi iniciada.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=A execução da cadeia de tarefas foi iniciada para {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Falha ao executar a cadeia de tarefas.
#XTXT: Execute button label
runLabel=Executar
#XTXT: Execute button label
runLabelNew=Iniciar execução
#XMSG: Filter Object header
chainsFilteredTableHeader=Filtrado por objeto: {0}
#XFLD: Parent task chain label
parentChainLabel=Cadeia de tarefas superior:
#XFLD: Parent task chain unauthorized
Unauthorized=Sem autorização para ver
#XFLD: Parent task chain label
parentTaskLabel=Tarefa superior:
#XTXT: Task status
NOT_TRIGGERED=Não acionado
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Entrar no modo de ecrã inteiro
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Sair do modo de ecrã inteiro
#XTXT: Close Task log details right panel
closeRightColumn=Fechar secção
#XTXT: Sort Text
sortTxt=Ordenar
#XTXT: Filter Text
filterTxt=Filtrar
#XTXT: Filter by text to show list of filters applied
filterByTxt=Filtrar por
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Mais de 5 minutos
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Mais de 15 minutos
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Mais de 1 hora
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Última hora
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Últimas 24 horas
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Último mês
#XTXT: Messages title text
messagesText=Mensagens

#XTXT Statistics information message
statisticsInfo=Não é possível criar estatísticas para tabelas remotas com acesso a dados "Replicados". Para criar estatísticas, remova os dados replicados no monitor de detalhes de tabelas remotas.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Vá para o monitor de detalhes de tabelas remotas

#XTXT: Repair latest failed run label
retryRunLabel=Repetir a última execução
#XTXT: Repair failed run label
retryRun=Repetir execução
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=A execução da repetição da cadeia de tarefas foi iniciada
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=A execução da repetição da cadeia de tarefas falhou
#XMSG: Task chain child elements name
taskChainRetryChildObject=Tarefa {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Nova tarefa
#XFLD Analyzed View
analyzedView=Vista analisada
#XFLD Metrics
Metrics=Métricas
#XFLD Partition Metrics
PartitionMetrics=Métricas de partição
#XFLD Entities
Messages=Registo de tarefas
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Ainda não foram definidas partições.
#XTXT: Description message for empty partition data
partitionEmptyDescText=Criar partições, especificando critérios para repartir volumes de dados maiores em menores, em partes mais fáceis de gerir.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Nenhum registo ainda disponível
#XTXT: Description message for empty runs data
runsEmptyDescText=Quando começar uma nova atividade (carregar um novo instantâneo, iniciar o analisador de vistas...), verá os registos relacionados aqui.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=A configuração do nó de computação elástico não {0} não está mantida de modo correspondente. Verifique a sua definição.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=falha ao processar para adicionar o nó de computação elástico {0}.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=A criação e ativação da réplica para o objeto de origem "{0}"."{1}" no nó de computação elástico {2} foi iniciada.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=A remoção da réplica para o objeto de origem "{0}"."{1}" no nó de computação elástico {2} foi iniciada.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Falha na criação e ativação da réplica para o objeto de origem "{0}"."{1}".
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Falha na remoção da réplica para o objeto de origem "{0}"."{1}".
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Início do encaminhamento da computação de consultas analíticas do espaço {0} para o nó de computação elástico {1}.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Falha no encaminhamento da computação de consultas analíticas do espaço {0} para o nó de computação elástico correspondente.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Início do reencaminhamento da computação de consultas analíticas do espaço {0} de volta do nó de computação elástico {1}.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Falha no reencaminhamento da computação de consultas analíticas do espaço {0} de volta para o coordenador.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=A cadeia de tarefas {0} para o nó de computação elástico{1} foi acionada.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Falha na geração da cadeia de tarefas para o nó de computação elástico {0}.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=O aprovisionamento do nó de computação elástico {0} foi iniciado com o plano de dimensionamento indicado: vCPUs: {1}, memória (GiB): {2} e tamanho de armazenamento (GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=O nó de computação elástico {0} já foi aprovisionado.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=O nó de computação elástico {0} já foi desaprovisionado.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=A operação não é permitida. Pare o nó de computação elástico {0} e reinicie-o para atualizar o plano de dimensionamento.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Falha ao processar para remover o nó de computação elástico {0}.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=A execução atual do nó de computação elástico {0} excedeu o tempo.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=A verificação do estado do nó de computação elástico {0} está em curso...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=A geração da cadeia de tarefas para o nó de computação elástico {0} está bloqueada, pelo que a cadeia {1} poderá estar ainda em execução.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=O objeto de origem "{0}"."{1}" é replicado como dependência da vista "{2}"."{3}".
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Impossível replicar a tabela "{0}"."{1}, uma vez que a replicação da tabela de linhas é preterida.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=O nó máximo de computação elástico por instância SAP HANA Cloud foi excedido.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=A operação de execução do nó de computação elástico {0} ainda está em curso.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Devido a problemas técnicos, a eliminação da réplica para a tabela de origem {0} foi parada. Tente novamente mais tarde.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=Devido a problemas técnicos, a criação da réplica para a tabela de origem {0} foi parada. Tente novamente mais tarde.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Não é posssível iniciar um nó de computação elástico porque o limite de utilização foi atingido ou porque ainda não foi alocado nenhum bloco de horas de computação.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=A carregar a cadeia de tarefas e a preparar a execução de um total de {0} tarefas que fazem parte desta cadeia.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Uma tarefa em conflito já está em execução
#XMSG: Replication will change
txt_replication_change=O tipo de replicação será alterado.
txt_repl_viewdetails=Ver detalhes

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Parece que ocorreu um erro com a nova tentativa da última execução, pois a execução da tarefa anterior falhou antes de o plano poder ser gerado.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=O espaço "{0}" está bloqueado.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=A atividade {0} requer que a tabela local {1} seja implementada.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=A atividade {0} requer que a captura delta esteja ativada para a tabela local {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=A atividade {0} requer que a tabela local {1} armazene dados no armazenamento de objetos.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=A atividade {0} requer que a tabela local {1} armazene dados na base de dados.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=A iniciar a remoção dos registos eliminados para a tabela local {0}.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=A iniciar a eliminação de todos os registos para a tabela local {0}.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=A iniciar a eliminação de registos para a tabela local {0}, de acordo com a condição de filtro {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Ocorreu um erro ao remover os registos eliminados para a tabela local {0}.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Ocorreu um erro ao eliminar todos os registos para a tabela local {0}.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=A eliminar todos os registos totalmente processados com tipo de alteração "Eliminado" com mais de {0} dias.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=A eliminar todos os registos totalmente processados com tipo de alteração "Eliminado" anteriores a {0}.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} registos foram eliminados.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} registos marcados para eliminação.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=A remoção dos registos eliminados para a tabela local {0} está concluída.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=A eliminação de todos os registos para a tabela local {0} está concluída.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=A iniciar a marcação de registos como "Eliminado" para a tabela local {0}.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=A iniciar a marcação de registos como "Eliminado" para a tabela local {0}, de acordo com a condição de filtro {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=A marcação de registos como "Eliminado" para a tabela local {0} está concluída.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=As alterações dos dados estão a ser carregadas temporariamente para a tabela {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=As alterações dos dados são carregadas temporariamente para a tabela {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=As alterações dos dados são processadas e eliminadas da tabela {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Detalhes de ligação.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=A iniciar otimização da tabela local (ficheiro).
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Ocorreu um erro ao otimizar a tabela local (ficheiro).
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Ocorreu um erro. A otimização da tabela local (ficheiro) foi parada.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=A otimizar tabela local (ficheiro)...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=A tabela local (ficheiro) foi otimizada.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=A tabela local (ficheiro) está otimizada.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=A tabela está otimizada com as colunas de ordem Z: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Ocorreu um erro. A truncagem da tabela local (ficheiro) foi parada.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=A truncar tabela local (ficheiro)...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=A localização da memória intermédia de entrada para a tabela local (ficheiro) foi removida.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=A iniciar limpeza (eliminar todos os registos totalmente processados) de tabela local (ficheiro).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Ocorreu um erro ao limpar a tabela local (ficheiro).
#XMSG: Task log message
LTF_VACUUM_STOPPED=Ocorreu um erro. A limpeza da tabela local (ficheiro) foi parada.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=A limpar tabela local (ficheiro)...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=A limpeza está completa.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=A eliminar todos os registos totalmente processados com mais de {0} dias.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=A eliminar todos os registos totalmente processados com mais de {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=A iniciar união de novos registos com tabela local (ficheiro).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=A iniciar união de novos registos com tabela local (ficheiro) utilizando a definição "Unir dados automaticamente".
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=A iniciar união de novos registos com tabela local (ficheiro). Esta tarefa foi iniciada através do pedido de união da API.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=A unir novos registos com tabela local (ficheiro).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Ocorreu um erro ao unir novos registos com tabela local (ficheiro).
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=A tabela local (ficheiro) está unida.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Ocorreu um erro. A união da tabela local (ficheiro) foi parada.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=A união da tabela local (ficheiro) falhou devido a um erro, mas a operação teve êxito parcial e alguns dados já foram unidos.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Ocorreu um erro de tempo limite. A atividade {0} está a decorrer há {1} horas.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=Não foi possível iniciar a execução assíncrona devido a uma elevada carga do sistema. Abra o ''Monitor de sistema'' e verifique as tarefas em execução.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=A execução assíncrona foi cancelada.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=A tarefa {0} foi executada dentro dos limites de memória de {1} e{2} .
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=A tarefa {0} foi executada com o ID de recurso {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=A tarefa Localizar e substituir foi iniciada na tabela local (ficheiro).
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=A tarefa Localizar e substituir foi concluída na tabela local (ficheiro).
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=A tarefa Localizar e substituir falhou na tabela local (ficheiro).

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Ocorreu um erro. A atualização das estatísticas para a tabela local (ficheiro) foi parada.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=A tarefa falhou devido a um erro de memória esgotada na base de dados SAP HANA.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=A tarefa falhou devido a uma rejeição do controlo de admissão SAP HANA.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=A tarefa falhou por haver demasiadas ligações SAP HANA ativas.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=Não foi possível realizar a operação Repetir porque as repetições só são permitidas no principal de uma cadeia de tarefas aninhada.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Os registos das tarefas subordinadas falhadas já não estão disponíveis para que a operação de repetição possa continuar.


####Metrics Labels

performanceOptimized=Otimizado para desempenho
memoryOptimized=Otimizado para memória

JOB_EXECUTION=Execução da tarefa
EXECUTION_MODE=Modo de execução
NUMBER_OF_RECORDS_OVERALL=Número de registos persistidos
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Número de registos lidos da origem remota
RUNTIME_MS_REMOTE_EXECUTION_TIME=Tempo de processamento de origem remota
MEMORY_CONSUMPTION_GIB=Memória de pico SAP HANA
NUMBER_OF_PARTITIONS=Número de partições
MEMORY_CONSUMPTION_GIB_OVERALL=Memória de pico SAP HANA
NUMBER_OF_PARTITIONS_LOCKED=Número de partições bloqueadas
PARTITIONING_COLUMN=Coluna de partição
HANA_PEAK_CPU_TIME=Tempo total de CPU SAP HANA
USED_IN_DISK=Armazenamento utilizado
INPUT_PARAMETER_PARAMETER_VALUE=Parâmetro de entrada
INPUT_PARAMETER=Parâmetro de entrada
ECN_ID=Nome do nó de computação elástico

DAC=Controlos de acesso a dados
YES=Sim
NO=Não
noofrecords=Número de registos
partitionpeakmemory=Memória de pico SAP HANA
value=Valor
metricsTitle=Métricas ({0})
partitionmetricsTitle=Partições ({0})
partitionLabel=Partição
OthersNotNull=Valores não incluídos em intervalos
OthersNull=Valores nulos
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Definições utilizadas para a última execução de persistência de dados:
#XMSG: Message for input parameter name
inputParameterLabel=Parâmetro de entrada
#XMSG: Message for input parameter value
inputParameterValueLabel=Valor
#XMSG: Message for persisted data
inputParameterPersistedLabel=Persistido às
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Eliminar dados
REMOVE_DELETED_RECORDS=Remover registos eliminados
MERGE_FILES=Unir ficheiros
OPTIMIZE_FILES=Otimizar ficheiros
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Ver no monitor SAP BW Bridge

ANALYZE_PERFORMANCE=Analisar desempenho
CANCEL_ANALYZE_PERFORMANCE=Cancelar análise do desempenho

#XFLD: Label for frequency column
everyLabel=A cada
#XFLD: Plural Recurrence text for Hour
hoursLabel=Horas
#XFLD: Plural Recurrence text for Day
daysLabel=Dias
#XFLD: Plural Recurrence text for Month
monthsLabel=Meses
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutos

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Guia de resolução de erros de persistência de vista</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Memória de processo esgotada. Impossível persistir dados para a vista "{0}". Consulte o portal de ajuda para obter mais informações sobre os erros de memória esgotada. Considere verificar o analisador de vistas para analisar a vista para complexidade de consumo de memória.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Não aplicável
OPEN_BRACKET=(
CLOSE_BRACKET=)
