
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Podrobnosti dnevnika
#XFLD: Header
TASK_LOGS=Zapisniki nalog ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Izvajanja ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Prikaz podrobnosti
#XFLD: Button text
STOP=Zaustavi izvajanje
#XFLD: Label text
RUN_START=Začetek zadnjega izvajanja
#XFLD: Label text
RUN_END=Konec zadnjega izvajanja
#XFLD: Label text
RUNTIME=Trajanje
#XTIT: Count for Messages
txtDetailMessages=Sporočila ({0})
#XFLD: Label text
TIME=Časovna oznaka
#XFLD: Label text
MESSAGE=Sporočilo
#XFLD: Label text
TASK_STATUS=Kategorija
#XFLD: Label text
TASK_ACTIVITY=Aktivnost
#XFLD: Label text
RUN_START_DETAILS=Začetek
#XFLD: Label text
RUN_END_DETAILS=Konec
#XFLD: Label text
LOGS=Izvajanja
#XFLD: Label text
STATUS=Status
#XFLD: Label text
RUN_STATUS=Status izvajanja
#XFLD: Label text
Runtime=Trajanje
#XFLD: Label text
RuntimeTooltip=Trajanje (hh : mm : ss)
#XFLD: Label text
TRIGGEREDBY=Sprožil
#XFLD: Label text
TRIGGEREDBYNew=Izvedel
#XFLD: Label text
TRIGGEREDBYNewImp=Uporabnik, ki je zagnal izvajanje
#XFLD: Label text
EXECUTIONTYPE=Tip izvedbe
#XFLD: Label text
EXECUTIONTYPENew=Tip izvajanja
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Prostor nadrejene verige
#XFLD: Refresh tooltip
TEXT_REFRESH=Osveži
#XFLD: view Details link
VIEW_ERROR_DETAILS=Prikaz podrobnosti
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Iz strežnika prenesi dodatne podrobnosti
#XMSG: Download completed
downloadStarted=Prenos iz strežnika se je začel
#XMSG: Error while downloading content
errorInDownload=Pri prenašanju iz strežnika je prišlo do napake.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Prikaz podrobnosti
#XBTN: cancel button of task details dialog
TXT_CANCEL=Prekliči
#XBTN: back button from task details
TXT_BACK=Nazaj
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Opravilo končano
#XFLD: Log message with failed status
MSG_LOG_FAILED=Opravilo ni uspelo
#XFLD: Master and detail table with no data
No_Data=Ni podatkov
#XFLD: Retry tooltip
TEXT_RETRY=Poskusi znova
#XFLD: Cancel Run label
TEXT_CancelRun=Prekliči izvajanje
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Nalaganje čiščenja ni uspelo
#XMSG:button copy sql statement
txtSQLStatement=Kopiraj SQL-navodilo
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Odpri Monitor oddaljene poizvedbe
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=Za prikaz oddaljenih SQL-navodil kliknite "Odpri monitor oddaljene poizvedbe".
#XMSG:button ok
txtOk=V redu
#XMSG: button close
txtClose=Zapri
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=Dejanje izvajanja preklica za objekt "{0}" je bilo začeto.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=Dejanje izvajanja preklica za objekt "{0}" ni uspelo.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=Dejanje izvajanja preklica za objekt ''{0}'' ni več mogoče, ker je status podvajanja spremenjen.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Noben zapisnik opravila nima statusa V izvajanju.
#XMSG: message for conflicting task
Task_Already_Running=Naloga v sporu se že izvaja za objekt "{0}".
#XFLD: Label for no task log with running state title
actionInfo=Informacije o dejanju
#XMSG Copied to clipboard
copiedToClip=Kopirano v vmesno odložišče
#XFLD copy
Copy=Kopiranje
#XFLD copy correlation ID
CopyCorrelationID=Kopiranje ID korelacije
#XFLD Close
Close=Zapiranje
#XFLD: show more Label
txtShowMore=Prikaži več
#XFLD: message Label
messageLabel=Sporočilo:
#XFLD: details Label
detailsLabel=Detajli:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Izvedba SQL-\r\nnavodila:
#XFLD:statementId Label
statementIdLabel=ID navodila:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Število oddaljenih\r\nSQL-navodil:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Število navodil
#XFLD: Space Label
txtSpaces=Prostor
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Nepooblaščeni prostori
#XFLD: Privilege Error Text
txtPrivilegeError=Nimate ustreznih pravic za ogled teh podatkov.
#XFLD: Label for Object Header
DATA_ACCESS=Dostop do podatkov
#XFLD: Label for Object Header
SCHEDULE=Časovni načrt
#XFLD: Label for Object Header
DETAILS=Podrobnosti
#XFLD: Label for Object Header
LATEST_UPDATE=Zadnja posodobitev
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Zadnja sprememba (vir)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Pogostost osvežitve
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Načrtovana pogostost
#XFLD: Label for Object Header
NEXT_RUN=Naslednje izvajanje
#XFLD: Label for Object Header
CONNECTION=Povezava
#XFLD: Label for Object Header
DP_AGENT=Agent za pripravo podatkov
#XFLD: Label for Object Header
USED_IN_MEMORY=Zasedeni pomnilnik za shrambo (MiB)
#XFLD: Label for Object Header
USED_DISK=Zasedeni disk za shrambo (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Velikost v pomnilniku (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Velikost na disku (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Število zapisov

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Nastavitev na Neuspelo
SET_TO_FAILED_ERR=Naloga se je izvajala, vendar je uporabnik status te naloge nastavil na NEUSPELO.
#XFLD: Label for stopped failed
FAILLOCKED=Izvedba je že v teku
#XFLD: sub status STOPPED
STOPPED=Zaustavljeno
STOPPED_ERR=Naloga je bila zaustavljena, vendar povrnitev prejšnjega stanja ni bila izvedena.
#XFLD: sub status CANCELLED
CANCELLED=Prekinjeno
CANCELLED_ERR=Izvajanje naloge je bilo preklicano po začetku izvajanja. V tem primeru se podatki obnovijo in povrnejo v stanje, preden je bilo izvajanje naloge prvič zagnano.
#XFLD: sub status LOCKED
LOCKED=Zaklenjeno
LOCKED_ERR=Enaka naloga je že bila v teku, zato opravila naloge ni mogoče izvesti vzporedno z obstoječo izvedbo naloge.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Izjema naloge
TASK_EXCEPTION_ERR=Pri izvedbi naloge je prišlo do neznane napake.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Izjema med izvajanjem naloge
TASK_EXECUTE_EXCEPTION_ERR=Pri tej nalogi je prišlo do težave med izvajanjem.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Nepooblaščeno
UNAUTHORIZED_ERR=Za uporabnika ni bilo mogoče preveriti pristnosti, uporabnik je bil zaklenjen ali izbrisan.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Prepovedano
FORBIDDEN_ERR=Dodeljeni uporabnik nima pravic, potrebnih za izvedbo naloge.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Ni sproženo
FAIL_NOT_TRIGGERED_ERR=Opravila naloge ni bilo mogoče izvesti zaradi nedelovanja sistema, ali ker nekateri deli sistema zbirke podatkov niso bili na voljo v času načrtovane izvedbe. Počakajte do naslednje načrtovane izvedbe opravila ali znova načrtujte opravilo.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Časovni načrt preklican
SCHEDULE_CANCELLED_ERR=Opravila naloge ni bilo mogoče izvesti zaradi interne napake. Obrnite se na SAP-jevo podporo in jim posredujte ID korelacije in časovno oznako iz podrobnih informacij dnevnika opravila naloge.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Predhodno izvajanje v teku
SUCCESS_SKIPPED_ERR=Izvedba naloge ni bila sprožena, ker je prejšnje izvajanje iste naloge še vedno v teku.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Lastnik manjka
FAIL_OWNER_MISSING_ERR=Opravila naloge ni bilo mogoče izvesti, ker nima dodeljenega uporabnika sistema. Opravilu dodelite odgovornega uporabnika.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Privolitev ni na voljo
FAIL_CONSENT_NOT_AVAILABLE_ERR=SAP-ja niste pooblastili za izvajanje verig nalog ali načrtovanje nalog za integracijo podatkov v vašem imenu. Izberite navedeno možnost za privolitev.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Privolitev je potekla
FAIL_CONSENT_EXPIRED_ERR=Pooblastilo, ki SAP-ju omogoča izvajanje verig nalog ali načrtovanje nalog za integracijo podatkov v vašem imenu, je poteklo. Izberite navedeno možnost za obnovitev privolitve.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Privolitev je razveljavljena
FAIL_CONSENT_INVALIDATED_ERR=Naloge ni bilo mogoče izvesti zaradi spremembe v konfiguraciji ponudnika identitet najemnika. V tem primeru ni mogoče izvajati ali načrtovati novih opravil naloge v imenu zadevnega uporabnika. Če dodeljeni uporabnik še vedno obstaja v novem ponudniku identitet, prekličite privolitev za načrtovanje in jo nato znova potrdite. Če dodeljeni uporabnik ne obstaja več, dodelite novega lastnika opravila naloge in podajte zahtevano privolitev za načrtovanje naloge. Več informacij najdete v naslednjem SAP-navodilu: https://launchpad.support.sap.com/#/notes/3089828.
TASK_EXECUTOR_ERROR=Izvršitelj naloge
TASK_EXECUTOR_ERROR_ERR=V nalogi je prišlo do interne napake, verjetno pri korakih priprave za izvedbo, zato naloge ni bilo mogoče zagnati.
PREREQ_NOT_MET=Predpogoj ni izpolnjen
PREREQ_NOT_MET_ERR=Te naloge ni bilo mogoče izvesti zaradi težav v definiciji. Objekt denimo ni postavljen, veriga naloge vsebuje krožno logiko, ali SQL pogleda ni veljaven.
RESOURCE_LIMIT_ERROR=Napaka pri omejevanju virov
RESOURCE_LIMIT_ERROR_ERR=Naloge trenutno ni mogoče izvesti, ker ni bilo na voljo dovolj virov ali pa so bili v uporabi.
FAIL_CONSENT_REFUSED_BY_UMS=Zavrjeno soglasje
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Te naloge ni bilo mogoče izvesti v načrtovanih izvedbah ali verigah nalog zaradi spremembe konfiguracije ponudnika identitete uporabnika v najemniku. Za več informacij gl. naslednje SAP-ovo obvestilo: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Načrtovano
#XFLD: status text
SCHEDULEDNew=Trajno
#XFLD: status text
PAUSED=Začasno zaustavljeno
#XFLD: status text
DIRECT=Neposredno
#XFLD: status text
MANUAL=Ročno
#XFLD: status text
DIRECTNew=Preprosto
#XFLD: status text
COMPLETED=Dokončano
#XFLD: status text
FAILED=Ni uspelo
#XFLD: status text
RUNNING=Se izvaja
#XFLD: status text
none=Brez
#XFLD: status text
realtime=V dejanskem času
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Podrejena naloga
#XFLD: New Data available in the file
NEW_DATA=Novi podatki

#XFLD: text for values shown in column Replication Status
txtOff=Izključi
#XFLD: text for values shown in column Replication Status
txtInitializing=Inicializacija
#XFLD: text for values shown in column Replication Status
txtLoading=Prenos
#XFLD: text for values shown in column Replication Status
txtActive=Aktivno
#XFLD: text for values shown in column Replication Status
txtAvailable=Na voljo
#XFLD: text for values shown in column Replication Status
txtError=Napaka
#XFLD: text for values shown in column Replication Status
txtPaused=Začasno zaustavljeno
#XFLD: text for values shown in column Replication Status
txtDisconnected=Brez povezave
#XFLD: text for partially Persisted views
partiallyPersisted=Delno trajno shranjeno

#XFLD: activity text
REPLICATE=Podvajanje
#XFLD: activity text
REMOVE_REPLICATED_DATA=Odstrani podvojene podatke
#XFLD: activity text
DISABLE_REALTIME=Onemogoči podvajanje podatkov v dejanskem času
#XFLD: activity text
REMOVE_PERSISTED_DATA=Odstrani trajne podatke
#XFLD: activity text
PERSIST=Trajno
#XFLD: activity text
EXECUTE=Izvedba
#XFLD: activity text
TASKLOG_CLEANUP=Čiščenje_Zapisnika
#XFLD: activity text
CANCEL_REPLICATION=Prekliči podvajanje
#XFLD: activity text
MODEL_IMPORT=Uvoz modela
#XFLD: activity text
NONE=Brez
#XFLD: activity text
CANCEL_PERSISTENCY=Preklic trajnosti
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Analiza pogleda
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Preklic analizatorja pogleda

#XFLD: severity text
INFORMATION=Informacije
#XFLD: severity text
SUCCESS=Uspeh
#XFLD: severity text
WARNING=Opozorilo
#XFLD: severity text
ERROR=Napaka
#XFLD: text for values shown for Ascending sort order
SortInAsc=Naraščajoče razvrščanje
#XFLD: text for values shown for Descending sort order
SortInDesc=Padajoče razvrščanje
#XFLD: filter text for task log columns
Filter=Filtriraj
#XFLD: object text for task log columns
Object=Objekt
#XFLD: space text for task log columns
crossSpace=Prostor

#XBUT: label for remote data access
REMOTE=Oddaljeno
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Podvojeno (v dejanskem času)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Podvojeno (trenutni posnetek)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=Podvajanje v dejanskem času je blokirano zaradi napake. Ko odpravite napako, lahko za nadaljevanje podvajanja v dejanskem času uporabite dejanje "Poskusi znova".
ERROR_MSG=Podvajanje v dejanskem času je blokirano zaradi napake.
RETRY_FAILED_ERROR=Poskus vnovične izvedbe se je končal z napako.
LOG_INFO_DETAILS=Zapisniki se ne generirajo, če se podatki podvojijo v načinu dejanskega časa. Prikazani zapisniki se nanašajo na prejšnja dejanja.

#XBUT: Partitioning label
partitionMenuText=Particioniranje
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Ustvari particijo
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Uredi particijo
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Izbriši particijo
#XFLD: Initial text
InitialPartitionText=Opredelite particije, tako da navedete kriterije za razdelitev velikih podatkovnih nizov na manjše nize.
DefinePartition=Opredelitev particij
#XFLD: Message text
partitionChangedInfo=Opredelitev particije se je spremenila po zadnjem podvajanju. Spremembe bodo uporabljene pri naslednjem prenosu podatkov.
#XFLD: Message text
REAL_TIME_WARNING=Particioniranje velja le pri prenašanju novega trenutnega posnetka. Ne velja za podvajanje v dejanskem času.
#XFLD: Message text
loadSelectedPartitions=Zagnano trajno shranjevanja podatkov za izbrane particije ''{0}''
#XFLD: Message text
loadSelectedPartitionsError=Podatkov za izbrane particije ''{0}''’ ni uspelo trajno shraniti
#XFLD: Message text
viewpartitionChangedInfo=Definicija particije se je od zadnje izvedbe shranjevanja spremenila. Če želite uveljaviti spremembe, bo naslednje nalaganje podatkov celoten posnetek, vključno z zaklenjenimi particijami. Ko bo to celovito nalaganje končano, boste lahko zagnali podatke za posamezne particije.
#XFLD: Message text
viewpartitionChangedInfoLocked=Opredelitev particije se je od zadnje izvedbe trajnega shranjevanja spremenila. Če želite uveljaviti spremembe, bo naslednji prenos podatkov celoten posnetek, razen za blokirane in nespremenjene obsege particij. Ko bo prenos končan, boste lahko izbrane particije znova prenesli.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Podvajanje tabele
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Načrtuj podvajanje
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Ustvari načrt
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Uredi časovni načrt
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Izbriši časovni načrt
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Naloži nov trenutni posnetek
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Začni replikacijo podatkov
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Odstrani podvojene podatke
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Omogoči dostop v dejanskem času
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Omogoči podvajanje podatkov v dejanskem času
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Onemogoči podvajanje podatkov v dejanskem času
#XFLD: Message for replicate table action
replicateTableText=Podvajanje tabele
#XFLD: Message for replicate table action
replicateTableTextNew=Podvajanje podatkov
#XFLD: Message to schedule task
scheduleText=Načrt
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Prikaz trajnosti
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Trajnost podatkov
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Naloži nov trenutni posnetek
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Začetek trajnosti podatkov
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Odstrani trajne podatke
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Razdeljena obdelava
#XBUT: Label for scheduled replication
scheduledTxt=Načrtovano
#XBUT: Label for statistics button
statisticsTxt=Statistika
#XBUT: Label for create statistics
createStatsTxt=Ustvari statistiko
#XBUT: Label for edit statistics
editStatsTxt=Uredi statistiko
#XBUT: Label for refresh statistics
refreshStatsTxt=Osveži statistiko
#XBUT: Label for delete statistics
dropStatsTxt=Izbriši statistiko
#XMSG: Create statistics success message
statsSuccessTxt=Začnite ustvarjati statistiko tipa {0} za {1}.
#XMSG: Edit statistics success message
statsEditSuccessTxt=Začnite spreminjati tip statistike v {0} za {1}.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Začnite osveževati statistiko za {0}.
#XMSG: Drop statistics success message
statsDropSuccessTxt=Statistika uspešno izbrisana za {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=Napaka pri ustvarjanju statistike
#XMSG: Edit statistics error message
statsEditErrorTxt=Napaka pri spremembi statistike
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Napaka pri osvežitvi statistike
#XMSG: Drop statistics error message
statsDropErrorTxt=Napaka pri izbrisu statistike
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Res želite izbrisati statistiko podatkov?
startPersistencyAdvisorLabel=Zaženi analizator pogleda

#Partition related texts
#XFLD: Label for Column
column=Stolpec
#XFLD: Label for No of Partition
noOfPartitions=Število particij
#XFLD: Label for Column
noOfParallelProcess=Število vzporednih procesov
#XFLD: Label text
noOfLockedPartition=Število zaklenjenih particij
#XFLD: Label for Partition
PARTITION=Particije
#XFLD: Label for Column
AVAILABLE=Na voljo
#XFLD: Statistics Label
statsLabel=Statistika
#XFLD: Label text
COLUMN=Stolpec:
#XFLD: Label text
PARALLEL_PROCESSES=Vzporedni postopki:
#XFLD: Label text
Partition_Range=Območje particije
#XFLD: Label text
Name=Ime
#XFLD: Label text
Locked=Zaklenjeno
#XFLD: Label text
Others=DRUGI
#XFLD: Label text
Delete=Izbris
#XFLD: Label text
LoadData=Prenesi izbrane particije
#XFLD: Label text
LoadSelectedData=Prenesi izbrane particije
#XFLD: Confirmation text
LoadNewPersistenceConfirm=Prenesli boste nov trenutni posnetek za vse deblokirane in spremenjene particije, ne le za izbrane. Želite nadaljevati?
#XFLD: Label text
Continue=Naprej

#XFLD: Label text
PARTITIONS=Particije
#XFLD: Label text
ADD_PARTITIONS=+ Dodaj particijo
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Dodajanje particije
#XFLD: Label text
deleteRange=Izbris particije
#XFLD: Label text
LOW_PLACE_HOLDER=Vnesite nizko vrednost
#XFLD: Label text
HIGH_PLACE_HOLDER=Vnesite visoko vrednost
#XFLD: tooltip text
lockedTooltip=Zakleni particijo po začetnem prenosu

#XFLD: Button text
Edit=Uredi
#XFLD: Button text
CANCEL=Prekliči

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Zadnja posodobitev statistike
#XFLD: Statistics Fields
STATISTICS=Statistika

#XFLD:Retry label
TEXT_Retry=Poskusi znova
#XFLD:Retry label
TEXT_Retry_tooltip=Ko je napaka odpravljena, poskusite znova izvesti podvajanje v dejanskem času.
#XFLD: text retry
Retry=Potrdi
#XMG: Retry confirmation text
retryConfirmationTxt=Zadnje podvajanje v dejanskem času je bilo prekinjeno z napako.\n Potrdite, da je napaka odpravljena in da je podvajanje v dejanskem času mogoče znova zagnati.
#XMG: Retry success text
retrySuccess=Poskus vnovične izvedbe procesa je uspešno zagnan.
#XMG: Retry fail text
retryFail=Poskus vnovične izvedbe procesa ni uspel.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Ustvari statistiko
#XMSG: activity message for edit statistics
DROP_STATISTICS=Izbriši statistiko
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Osveži statistiko
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Uredi statistiko
#XMSG: Task log message started task
taskStarted=Naloga {0} se je začela.
#XMSG: Task log message for finished task
taskFinished=Naloga {0} se je končala s statusom {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=Naloga {0} se je končala ob {2} s statusom {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=Naloga {0} ima vhodne parametre.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=Naloga {0} se je končala z nepričakovano napako. Status naloge je bil nastavljen na {1}.
#XMSG: Task log message for failed task
failedToEnd=Nastavitev statusa na {0} ni uspela ali ključavnice ni bilo mogoče odstraniti.
#XMSG: Task log message
lockNotFound=Postopka ni bilo mogoče dokončati, ker manjka ključavnica: naloga je bila morda preklicana.
#XMSG: Task log message failed task
failedOverwrite=Nalogo {0} je že zaklenil {1}. Naloga zato ni uspela zaradi naslednje napake: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Blokado te naloge je prevzela druga naloga.
#XMSG: Task log message failed takeover
failedTakeover=Prevzem obstoječe naloge ni uspel.
#XMSG: Task log message successful takeover
successTakeover=Prejšnjo blokado je bilo treba sprostiti. Nova blokada za to nalogo je nastavljena.
#XMSG: Tasklog Dialog Details
txtDetails=Oddaljene izjave, obdelane pri izvajanju, lahko prikažete v podrobnostih sporočil, specifičnih za particijo, tako da odprete monitor oddaljenih poizvedb.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=Oddaljena SQL-navodila so bila izbrisana iz baze podatkov in jih ni mogoče prikazati.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Oddaljenih poizvedb, ki imajo povezave, dodeljene drugim prostorom, ni mogoče prikazati. Pojdite na Monitor oddaljene poizvedbe in uporabite ID navodila za filtriranje navodil.
#XMSG: Task log message for parallel check error
parallelCheckError=Naloge ni mogoče obdelati, ker se že izvaja druga naloga in blokira to nalogo.
#XMSG: Task log message for parallel running task
parallelTaskRunning=Naloga v sporu se že izvaja.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} med izvajanjem z ID-jem korelacije {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=Dodeljeni uporabnik nima pravic, potrebnih za izvedbo naloge.

#XBUT: Label for open in Editor
openInEditor=Odpri v urejevalniku
#XBUT: Label for open in Editor
openInEditorNew=Odpri v graditelju podatkov
#XFLD:Run deails label
runDetails=Podrobnosti izvajanja
#XFLD: Label for Logs
Logs=Zapisniki
#XFLD: Label for Settings
Settings=Nastavitve
#XFLD: Label for Save button
Save=Shrani
#XFLD: Label for Standard
Standard_PO=Optimizirano za boljše delovanje (priporočeno)
#XFLD: Label for Hana low memory processing
HLMP_MO=Optimizirano za pomnilnik
#XFLD: Label for execution mode
ExecutionMode=Način izvajanja
#XFLD: Label for job execution
jobExecution=Način obdelave
#XFLD: Label for Synchronous
syncExec=Sinhrono
#XFLD: Label for Asynchronous
asyncExec=Asinhrono
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Uporabi privzeto (asinhrono, se lahko spremeni v prihodnosti)
#XMSG: Save settings success
saveSettingsSuccess=Način izvedbe SAP HANA spremenjen.
#XMSG: Save settings failure
saveSettingsFailed=Sprememba načina izvedbe SAP HANA ni uspela.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Izvedba opravila spremenjena.
#XMSG: Job Execution change failed
jobExecSettingFailed=Sprememba izvedbe opravila ni uspela.
#XMSG: Text for Type
typeTxt=Vrsta
#XMSG: Text for Monitor
monitorTxt=Nadzor
#XMSG: Text for activity
activityTxt=Dejavnost
#XMSG: Text for metrics
metricsTxt=Metrika
#XTXT: Text for Task chain key
TASK_CHAINS=Veriga nalog
#XTXT: Text for View Key
VIEWS=Pogled
#XTXT: Text for remote table key
REMOTE_TABLES=Oddaljena tabela
#XTXT: Text for Space key
SPACE=Prostor
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Vozlišče elastičnega računanja
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Tok podvajanja
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Pametno iskanje
#XTXT: Text for Local Table
LOCAL_TABLE=Lokalna tabela
#XTXT: Text for Data flow key
DATA_FLOWS=Tok podatkov
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=Postopek skripta SQL
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=Procesna veriga BW
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Prikaži v Nadzoru
#XTXT: Task List header text
taskListHeader=Seznam nalog ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Metrike za zgodovinska izvajanja toka podatkov ni mogoče priklicati.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=Prenos podrobnosti celotnega izvajanja trenutno ne poteka. Poskusite osvežiti.
#XFLD: Label text for the Metrices table header
metricesColLabel=Oznaka operatorja
#XFLD: Label text for the Metrices table header
metricesType=Vrsta
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Število zapisov
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Izvedite verigo nalog
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Izvajanje verige nalog je bilo zagnano.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Izvajanje verige nalog je bilo zagnano za {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Izvajanje verige nalog ni uspelo.
#XTXT: Execute button label
runLabel=Izvedi
#XTXT: Execute button label
runLabelNew=Začetek izvajanja
#XMSG: Filter Object header
chainsFilteredTableHeader=Filtrirano po objektu: {0}
#XFLD: Parent task chain label
parentChainLabel=Veriga nadrejenih nalog_
#XFLD: Parent task chain unauthorized
Unauthorized=Nimate pooblastila za ogled
#XFLD: Parent task chain label
parentTaskLabel=Nadrejena naloga:
#XTXT: Task status
NOT_TRIGGERED=Ni sproženo
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Zagon celoekranskega načina
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Izhod iz celoekranskega načina
#XTXT: Close Task log details right panel
closeRightColumn=Zapri segment
#XTXT: Sort Text
sortTxt=Razvrsti
#XTXT: Filter Text
filterTxt=Filtriraj
#XTXT: Filter by text to show list of filters applied
filterByTxt=Filtriraj po
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Več kot 5 minut
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Več kot 15 minut
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Več kot 1 uro
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Zadnja ura
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Zadnjih 24 ur
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Prejšnji mesec
#XTXT: Messages title text
messagesText=Sporočila

#XTXT Statistics information message
statisticsInfo=Statistike ni mogoče ustvariti za oddaljene tabele z dostopom do podatkov ''Podvojeno''. Da ustvarite statistiko, odstranite podvojene podatke v nadzoru podrobnosti oddaljenih tabel.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Prehod na monitor podrobnosti oddaljene tabele

#XTXT: Repair latest failed run label
retryRunLabel=Ponoven poskus zadnjega izvajanja
#XTXT: Repair failed run label
retryRun=Ponoven poskus izvajanja
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Ponoven poskus verige nalog se je začel
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Ponoven poskus verige nalog ni uspel
#XMSG: Task chain child elements name
taskChainRetryChildObject=Naloga {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Nova naloga
#XFLD Analyzed View
analyzedView=Analizirani pogled
#XFLD Metrics
Metrics=Metrika
#XFLD Partition Metrics
PartitionMetrics=Metrika particije
#XFLD Entities
Messages=Zapisnik naloge
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Particije še niso določene
#XTXT: Description message for empty partition data
partitionEmptyDescText=Ustvarite particije, tako da določite kriterije za razdelitev večjih količin podatkov na manjše, bolj obvladljive dele.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Zapisniki še niso na voljo
#XTXT: Description message for empty runs data
runsEmptyDescText=Ko zaženete novo dejavnost (nalaganje posnetka zaslona, zagon analizatorja pogleda), bodo tukaj prikazani povezani zapisniki.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=Konfiguracija vozlišča za elastični izračun {0} ni ustrezno vzdrževana. Preverite svojo opredelitev.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Postopek za dodajanje vozlišča elastičnega izračunavanja {0} ni uspel.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Ustvarjanje in aktiviranje replike za izvorni objekt ''{0}''.''{1}'' v vozlišču elastičnega izračunavanja {2} sta zagnana.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Odstranjevanje replike za izvorni objekt ''{0}''.''{1}'' iz vozlišča elastičnega izračunavanja {2} se je začelo.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Ustvarjanje in aktiviranje replike za izvorni objekt ''{0}''.''{1}'' ni uspelo.
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Odstranjevanje replike za izvorni objekt ''{0}''.''{1}'' ni uspelo.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Usmerjanje izračuna analitičnih poizvedb prostora {0} v vozlišče elastičnega izračunavanja {1} zagnano.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Usmerjanje izračuna analitičnih poizvedb prostora {0} v ustrezno vozlišče elastičnega izračunavanja ni uspelo.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Vnovično usmerjanje izračuna analitičnih poizvedb prostora {0} nazaj iz vozlišča elastičnega izračunavanja {1} zagnano.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Vnovično usmerjanje izračuna analitičnih poizvedb prostora {0} nazaj koordinatorju ni uspelo.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Veriga nalog {0} za ustrezno vozlišče elastičnega izračunavanja {1} je sprožena.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Generiranje verige nalog za vozlišče elastičnega izračunavanja {0} ni uspelo.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=Priprava vozlišča elastičnega izračunavanja {0} se je začela z navedenim načrtom določitve velikosti: vCPU-ji: {1}, pomnilnik (GiB): {2} in velikost prostora za shranjevanje (GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=Vozlišče elastičnega izračunavanja {0} je bilo pripravljeno.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=Vozlišče elastičnega izračunavanja {0} je bilo odstranjeno.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=Postopek ni dovoljen. Ustavite vozlišče elastičnega izračunavanja {0} in ga znova zaženite, da posodobite načrt določitve velikosti.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Postopek za odstranjevanje vozlišča elastičnega izračunavanja {0} ni uspel.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=Časovna omejitev trenutnega izvajanja vozlišča elastičnega izračunavanja {0} je potekla.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=Preverjanje statusa vozlišča elastičnega izračunavanja {0} je v teku ...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=Generiranje verige nalog za vozlišča elastičnega izračunavanja {0} je zaklenjeno, zato se veriga {1} morda še vedno izvaja.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=Izvorni objekt ''{0}''.''{1}'' je podvojen kot odvisnost od pogleda ''{2}''.''{3}''.
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Tabele ''{0}''.''{1}'' ni mogoče podvojiti, ker je podvajanje tabele vrstic zastarano.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Maksimalno vozlišče za elastično računanje za primerek SAP HANA Cloud je preseženo.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=Izvajanje postopka za vozlišče za elastično izračunavanje {0} je še vedno v teku.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Zaradi tehničnih težav je bilo brisanje replike v izvorni tabeli {0} zaustavljeno. Poskusite znova pozneje.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=Zaradi tehničnih težav je bilo ustvarjanje replike v izvorni tabeli {0} zaustavljeno. Poskusite znova pozneje.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Vozlišča za elastično računanje ni mogoče zagnati, ker je bila dosežena omejitev uporabe ali ker še niso bile dodeljene blokovne ure za izračun.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Prenos verige nalog in priprava za izvedbo skupno {0} nalog, ki so del te verige.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Naloga v sporu se že izvaja
#XMSG: Replication will change
txt_replication_change=Vrsta podvajanja bo spremenjena.
txt_repl_viewdetails=Prikaz podrobnosti

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Očitno je prišlo do napake pri ponovnem poskusu zadnjega izvajanja, saj prejšnjo izvajanje naloge ni bilo uspešno, preden je bilo mogoče generirati plan.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=Prostor "{0}" je zaklenjen.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=Dejavnost {0} zahteva postavitev lokalne tabele {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=Dejavnost {0} zahteva, da je za lokalno tabelo {1} potrebno vklopiti Zajemanje delta.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=Dejavnost {0} zahteva, da lokalna tabela {1} shranjuje podatke v shrambi objektov.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=Dejavnost {0} zahteva, da lokalna tabela {1} shranjuje podatke v podatkovni bazi.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Začetek odstranitve izbrisanih zapisov za lokalno tabelo {0}.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Začetek odstranitve vseh zapisov za lokalno tabelo {0}.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Začetek odstranitve zapisov za lokalno tabelo {0} v skladu s pogojem filtra {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Pri odstranjevanju izbrisanih zapisov za lokalno tabelo {0} je prišlo do napake.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Pri odstranjevanju vseh zapisov za lokalno tabelo {0} je prišlo do napake.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Izbris vseh popolnoma obdelanih zapisov z vrsto spremembe "Izbrisano", ki so starejši od {0} dni.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Izbris vseh popolnoma obdelanih zapisov z vrsto spremembe "Izbrisano", ki so starejši od {0}.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} zapisov izbrisanih.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS=Zapisi ({0}) so bili označeni za izbris.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=Odstranjevanje izbrisanih zapisov za lokalno tabelo {0} je končano.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Brisanje vseh zapisov za lokalno tabelo {0} je končano.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Označevanje zapisov kot "Izbrisano" za lokalno tabelo {0} se začenja.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Začetek označevanja zapisov kot "Izbrisanih" za lokalno tabelo {0} v skladu s pogojem filtra {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Označevanje zapisov kot "Izbrisano" za lokalno tabelo {0} je dokončano.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Spremembe podatkov se začasno prenašajo v tabelo {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Spremembe podatkov so začasno prenesene v tabelo {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Spremembe podatkov so obdelane in izbrisane iz tabele {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Podatki o povezavi.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Optimiziranje lokalne tabele (datoteke) se začenja.
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Pri optimiziranju lokalne tabele (datoteke) je prišlo do napake.
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Prišlo je do napake. Optimiziranje lokalne tabele (datoteke) se je ustavilo.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Poteka optimiziranje lokalne tabele (datoteke) ...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=Lokalna tabela (datoteka) je bila optimizirana.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=Lokalna tabela (datoteka) je optimizirana.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=Tabela je optimizirana s stolpci z razvrščanjem po osi Z: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Prišlo je do napake. Krajšanje lokalne tabele (datoteke) se je ustavilo.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Poteka krajšanje lokalne tabele (datoteke) ...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=Dohodna lokacija medpomnilnika za (datoteko) lokalne tabele je opuščena.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Čiščenje lokalne tabele (datoteke) (izbris vseh v celoti obdelanih zapisov) se je začelo.
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Pri čiščenju lokalne tabele (datoteke) je prišlo do napake.
#XMSG: Task log message
LTF_VACUUM_STOPPED=Prišlo je do napake. Čiščenje lokalne tabele (datoteke) se je ustavilo.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Poteka čiščenje lokalne tabele (datoteke) ...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=Čiščenje je dokončano.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Izbris vseh popolnoma obdelanih zapisov, ki so starejši od {0} dni.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Izbris vseh popolnoma obdelanih zapisov, ki so starejši od {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Združevanje novih zapisov z lokalno tabelo (datoteko) se je začelo.
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Začenja se združevanje novih zapisov z lokalno tabelo (datoteko) z uporabo nastavitve "Samodejno združevanje podatkov".
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Pričetek združitve novih zapisov z lokalno tabelo (datoteko). Ta naloga je bila vzpostavljena prek zahteve za združitev API.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Združevanje novih zapisov z lokalno tabelo (datoteko).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Pri združevanju novih zapisov z lokalno tabelo (datoteko) je prišlo do napake.
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=Lokalna tabela (datoteka) je združena.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Prišlo je do napake. Združevanje lokalne tabele (datoteke) se je ustavilo.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=Združitev lokalne tabele (datoteke) ni uspela zaradi napake, postopek pa je bil delno uspešen in nekaj podatkov je že bilo združenih.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Prišlo je do napake zaradi časovne omejitve. Aktivnost {0} je potekala {1} h.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=Asinhronega izvajanja zaradi visoke obremenitve sistema ni bilo mogoče začeti. Odprite "Nadzornik sistema" in preverite naloge, ki se izvajajo.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=Asinhrono izvajanje je bilo preklicano.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=Naloga {0} se je izvajala v okviru omejitev pomnilnika {1} in {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=Naloga {0} se je izvedla z ID resursa {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=Išči in zamenjaj se je začelo v lokalni tabeli (datoteka).
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Išči in zamenjaj se je končalo v lokalni tabeli (datoteka).
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Išči in zamenjaj ni uspelo v lokalni tabeli (datoteka).

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Prišlo je do napake. Posodabljanje statistike za lokalno tabelo (datoteke) se je ustavilo.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=Naloga ni uspela zaradi napake pomanjkanja pomnilnika v bazi podatkov SAP HANA.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=Naloga ni uspela zaradi zavrnitve nadzora sprejema SAP HANA.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=Naloga ni uspela zaradi preveč aktivnih povezav SAP HANA.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=Postopka Ponovitev ni bilo mogoče izvesti, ker so ponovitve dovoljene samo v nadrejenem elementu ugnezdene verige opravil.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Zapisniki neuspelih podrejenih opravil niso več na voljo za nadaljevanje vnovičnega poskusa.


####Metrics Labels

performanceOptimized=Optimizirano za boljše delovanje
memoryOptimized=Optimizirano za pomnilnik

JOB_EXECUTION=Izvedba opravila
EXECUTION_MODE=Način izvajanja
NUMBER_OF_RECORDS_OVERALL=Število trajno shranjenih zapisov
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Število zapisov, prebranih iz oddaljenega vira
RUNTIME_MS_REMOTE_EXECUTION_TIME=Čas obdelave oddaljenega izvora
MEMORY_CONSUMPTION_GIB=Največja obremenitev pomnilnika SAP HANA
NUMBER_OF_PARTITIONS=Število particij
MEMORY_CONSUMPTION_GIB_OVERALL=Največja obremenitev pomnilnika SAP HANA
NUMBER_OF_PARTITIONS_LOCKED=Število zaklenjenih particij
PARTITIONING_COLUMN=Stolpec particioniranja
HANA_PEAK_CPU_TIME=Skupni procesorski čas za SAP HANA
USED_IN_DISK=Uporabljena shramba
INPUT_PARAMETER_PARAMETER_VALUE=Vhodni parameter
INPUT_PARAMETER=Vhodni parameter
ECN_ID=Ime vozlišča elastičnega računanja

DAC=Kontrolniki za dostop do podatkov
YES=Da
NO=Ne
noofrecords=Število zapisov
partitionpeakmemory=Največja obremenitev pomnilnika SAP HANA
value=Vrednost
metricsTitle=Metrika ({0})
partitionmetricsTitle=Particije ({0})
partitionLabel=Particija
OthersNotNull=Vrednosti niso vključene v obsege
OthersNull=Ničelne vrednosti
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Nastavitve, uporabljene za zadnje izvajanje trajnega shranjevanja podatkov:
#XMSG: Message for input parameter name
inputParameterLabel=Vhodni parameter
#XMSG: Message for input parameter value
inputParameterValueLabel=Vrednost
#XMSG: Message for persisted data
inputParameterPersistedLabel=Trajno shranjeno ob
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Izbriši podatke
REMOVE_DELETED_RECORDS=Odstrani izbrisane zapise
MERGE_FILES=Združitev datotek
OPTIMIZE_FILES=Optimiziranje datotek
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Ogled v SAP BW Bridge Monitorju

ANALYZE_PERFORMANCE=Analiza učinkovitosti
CANCEL_ANALYZE_PERFORMANCE=Preklic analize učinkovitosti

#XFLD: Label for frequency column
everyLabel=Vsakih
#XFLD: Plural Recurrence text for Hour
hoursLabel=ur
#XFLD: Plural Recurrence text for Day
daysLabel=dni
#XFLD: Plural Recurrence text for Month
monthsLabel=mesecev
#XFLD: Plural Recurrence text for Minutes
minutesLabel=minut

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Pogled priročnika za odpravljanje težav s trajnostjo</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Procesu je zmanjkalo pomnilnika. Podatkov za pogled "{0}" ni mogoče trajno shraniti. Obiščite spletno mesto Help Portal za več informacij o napaki zaradi pomanjkanja pomnilnika. Preverite tudi Analizator pogleda, da analizirate pogled za kompleksnost porabe pomnilnika.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Se ne uporablja
OPEN_BRACKET=(
CLOSE_BRACKET=)
