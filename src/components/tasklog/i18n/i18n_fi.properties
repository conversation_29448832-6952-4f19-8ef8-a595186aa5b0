
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Lokin lisätiedot
#XFLD: Header
TASK_LOGS=Tehtävälokit ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Ajot ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Näytä lisätiedot
#XFLD: Button text
STOP=Pysäytä ajo
#XFLD: Label text
RUN_START=Edellisen ajon käynnistys
#XFLD: Label text
RUN_END=Edellisen ajon loppu
#XFLD: Label text
RUNTIME=Kesto
#XTIT: Count for Messages
txtDetailMessages=Sanomat ({0})
#XFLD: Label text
TIME=Aikaleima
#XFLD: Label text
MESSAGE=Ilmoitus
#XFLD: Label text
TASK_STATUS=Luokka
#XFLD: Label text
TASK_ACTIVITY=Toiminto
#XFLD: Label text
RUN_START_DETAILS=Käynnistä
#XFLD: Label text
RUN_END_DETAILS=Loppu
#XFLD: Label text
LOGS=Ajot
#XFLD: Label text
STATUS=Tila
#XFLD: Label text
RUN_STATUS=Ajon tila
#XFLD: Label text
Runtime=Kesto
#XFLD: Label text
RuntimeTooltip=Kesto (hh : mm : ss)
#XFLD: Label text
TRIGGEREDBY=Käynnistäjä
#XFLD: Label text
TRIGGEREDBYNew=Suorittaja
#XFLD: Label text
TRIGGEREDBYNewImp=Ajon käynnistäjä
#XFLD: Label text
EXECUTIONTYPE=Suorituslaji
#XFLD: Label text
EXECUTIONTYPENew=Ajotyyppi
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Ylätason ketjun tila
#XFLD: Refresh tooltip
TEXT_REFRESH=Päivitä
#XFLD: view Details link
VIEW_ERROR_DETAILS=Näytä lisätiedot
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Lataa lisätiedot paikallisesti
#XMSG: Download completed
downloadStarted=Lataus aloitettu
#XMSG: Error while downloading content
errorInDownload=Tapahtui virhe ladattaessa paikallisesti.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Näytä lisätiedot
#XBTN: cancel button of task details dialog
TXT_CANCEL=Peruuta
#XBTN: back button from task details
TXT_BACK=Paluu
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Tehtävä suoritettu loppuun
#XFLD: Log message with failed status
MSG_LOG_FAILED=Tehtävä epäonnistunut
#XFLD: Master and detail table with no data
No_Data=Ei tietoja
#XFLD: Retry tooltip
TEXT_RETRY=Yritä uudelleen
#XFLD: Cancel Run label
TEXT_CancelRun=Peruuta ajo
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Puhdista epäonnistunut lataus
#XMSG:button copy sql statement
txtSQLStatement=Kopioi SQL-lause
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Avaa etäkyselynvalvonta
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=Napsauta ”Avaa etäkyselynvalvonta” tuodaksesi etä-SQL-lauseet näkyviin.
#XMSG:button ok
txtOk=OK
#XMSG: button close
txtClose=Sulje
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=Objektin ''{0}'' ajon peruutustoimi on käynnistetty.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=Objektin ''{0}'' ajon peruutustoimi epäonnistui.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=Objektin ''{0}'' ajon peruutustoimi ei ole enää mahdollinen, sillä replikoinnin tilaa on muutettu.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Tehtävälokeja ei ole Käynnissä-tilan kanssa.
#XMSG: message for conflicting task
Task_Already_Running=Ristiriitainen tehtävä on jo käynnissä objektille "{0}".
#XFLD: Label for no task log with running state title
actionInfo=Toimenpidetiedot
#XMSG Copied to clipboard
copiedToClip=Kopioitu leikepöydälle
#XFLD copy
Copy=Kopioi
#XFLD copy correlation ID
CopyCorrelationID=Kopioi korrelaatiotunnus
#XFLD Close
Close=Sulje
#XFLD: show more Label
txtShowMore=Näytä enemmän
#XFLD: message Label
messageLabel=Sanoma:
#XFLD: details Label
detailsLabel=Lisätiedot:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Suorittava SQL-\r\n-lause
#XFLD:statementId Label
statementIdLabel=Lauseen ID
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Etä-\r\n-SQL-lauseiden lukumäärä
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Lauseiden lukumäärä
#XFLD: Space Label
txtSpaces=Tila
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Ei-valtuutetut tilat
#XFLD: Privilege Error Text
txtPrivilegeError=Käyttöoikeutesi eivät riitä näiden tietojen näyttämiseen.
#XFLD: Label for Object Header
DATA_ACCESS=Tietojen käyttö
#XFLD: Label for Object Header
SCHEDULE=Ajoita
#XFLD: Label for Object Header
DETAILS=Lisätiedot
#XFLD: Label for Object Header
LATEST_UPDATE=Viimeisin päivitys
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Viimeisin muutos (lähde)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Päivitystaajuus
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Suunniteltu tiheys
#XFLD: Label for Object Header
NEXT_RUN=Seuraava ajo
#XFLD: Label for Object Header
CONNECTION=Yhteys
#XFLD: Label for Object Header
DP_AGENT=Tietojen toimittaja
#XFLD: Label for Object Header
USED_IN_MEMORY=Tallennukseen käytetty muisti (MiB)
#XFLD: Label for Object Header
USED_DISK=Tallennukseen käytetty levy (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=In-memoryn koko (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Levyn koko (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Tietueiden lukumäärä

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Aseta tilaksi Epäonnistunut
SET_TO_FAILED_ERR=Tämä tehtävä oli käynnissä, mutta käyttäjä asetti sen tilaksi Epäonnistunut.
#XFLD: Label for stopped failed
FAILLOCKED=Ajo jo käynnissä
#XFLD: sub status STOPPED
STOPPED=Pysäytetty
STOPPED_ERR=Tämä tehtävä pysäytettiin, mutta peruutusta ei tehty.
#XFLD: sub status CANCELLED
CANCELLED=Peruutettu
CANCELLED_ERR=Tämä tehtäväajo peruutettiin käynnistyksen jälkeen. Tässä tapauksessa tiedot peruutettiin ja palautettiin tilaan, joka oli voimassa ennen tehtäväajon ensimmäistä käynnistystä.
#XFLD: sub status LOCKED
LOCKED=Lukittu
LOCKED_ERR=Sama tehtävä oli jo käynnissä, joten tämän tehtävän työtä ei voi suorittaa rinnan olemassa olevan tehtävän suorituksen kanssa.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Tehtävän poikkeus
TASK_EXCEPTION_ERR=Tämän tehtävän suorituksen aikana tapahtui määrittämätön virhe.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Tehtävän suorituksen poikkeus
TASK_EXECUTE_EXCEPTION_ERR=Tämän tehtävän suorituksen aikana tapahtui virhe.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Ei-valtuutettu
UNAUTHORIZED_ERR=Käyttäjää ei voitu todentaa tai hänet on lukittu tai poistettu.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Kielletty
FORBIDDEN_ERR=Kohdistetulla käyttäjällä ei ole tehtävän suorittamiseen tarvittavia oikeuksia.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Ei käynnistetty
FAIL_NOT_TRIGGERED_ERR=Tämän tehtävän työtä ei voitu suorittaa järjestelmäkatkoksen vuoksi tai koska tietokantajärjestelmä ei ollut käytettävissä suunnitellun suorituksen ajankohtana. Odota seuraavaa suunnitellun työn suoritusaikaa tai ajoita tehtävä uudelleen.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Aikataulu peruutettu
SCHEDULE_CANCELLED_ERR=Tämän tehtävän työtä ei voitu suorittaa sisäisen virheen vuoksi. Ota yhteys SAP-tukeen ja ilmoita korrelaatiotunnus ja aikaleima tämän tehtävän työn lokin lisätiedoista.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Edellinen ajo käynnissä
SUCCESS_SKIPPED_ERR=Tämän tehtävän suoritusta ei ole käynnistetty, koska saman tehtävän edellinen ajo on vielä käynnissä.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Omistaja puuttuu
FAIL_OWNER_MISSING_ERR=Tämän tehtävän työtä ei voitu suorittaa, koska siltä puuttuu kohdistettu järjestelmäkäyttäjä. Kohdista työhön omistajakäyttäjä.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Suostumusta ei käytettävissä
FAIL_CONSENT_NOT_AVAILABLE_ERR=Et ole antanut SAP:lle valtuutusta suorittaa tehtäväketjuja tai suunnitella tietointegraatiotehtäviä puolestasi. Valitse vaihtoehto, jolla annat suostumuksesi.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Suostumus vanhentunut
FAIL_CONSENT_EXPIRED_ERR=Suostumus, jolla sallit SAP:n suorittaa tehtäväketjuja tai suunnitella tietointegraatiotehtäviä puolestasi, on vanhentunut. Valitse vaihtoehto, jolla uudistat suostumuksesi.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Suostumus mitätöity
FAIL_CONSENT_INVALIDATED_ERR=Tätä tehtävää ei voitu suorittaa, yleensä vuokralaisen tunnistustietojen tarjoajakonfiguraation muutoksen vuoksi. Siinä tapauksessa tehtävän uusia töitä ei voida suorittaa tai suunnitella kyseisen käyttäjän puolesta. Jos kohdistettu käyttäjä on edelleen olemassa uudessa tunnistustietojen tarjoajassa, peruuta suunnittelusuostumus ja anna se sitten uudelleen. Jos kohdistettua käyttäjää ei ole enää olemassa, kohdista uusi tehtävän työn omistaja ja anna tarvittava tehtävänsuunnittelusuostumus. Lisätietoja seuraavasta SAP-ohjeesta: https://launchpad.support.sap.com/#/notes/3089828.
TASK_EXECUTOR_ERROR=Tehtävän suorittaja
TASK_EXECUTOR_ERROR_ERR=Tehtävässä tapahtui sisäinen virhe, todennäköisesti suorituksen valmisteluvaiheissa, eikä tehtävää voitu käynnistää.
PREREQ_NOT_MET=Edellytys ei täyty
PREREQ_NOT_MET_ERR=Tehtävää ei voitu suorittaa, koska sen määrityksessä on ongelmia. Esimerkiksi objektia ei ole otettu käyttöön, tehtäväketju sisältää kehälogiikan, tai näkymän SQL on virheellinen.
RESOURCE_LIMIT_ERROR=Resurssin rajan virhe
RESOURCE_LIMIT_ERROR_ERR=Tehtävää ei voi suorittaa tällä hetkellä, koska tarvittavia resursseja ei ole käytettävissä tai ne ovat varattuja.
FAIL_CONSENT_REFUSED_BY_UMS=Suostumus hylätty
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Tätä tehtävää ei voitu suorittaa suunnitelluissa ajoissa tai tehtäväketjuissa käyttäjän tunnistustietojen tarjoajan konfiguraation muutoksen takia vuokralaisessa. Lisätietoja on seuraavassa SAP-ohjeessa: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Ajoitettu
#XFLD: status text
SCHEDULEDNew=Pysyvä
#XFLD: status text
PAUSED=Keskeytetty
#XFLD: status text
DIRECT=Suora
#XFLD: status text
MANUAL=Manuaalinen
#XFLD: status text
DIRECTNew=Yksinkertainen
#XFLD: status text
COMPLETED=Suoritettu loppuun
#XFLD: status text
FAILED=Epäonnistunut
#XFLD: status text
RUNNING=Käynnissä
#XFLD: status text
none=Ei mitään
#XFLD: status text
realtime=Reaaliaikainen
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Alitehtävä
#XFLD: New Data available in the file
NEW_DATA=Uudet tiedot

#XFLD: text for values shown in column Replication Status
txtOff=Pois käytöstä
#XFLD: text for values shown in column Replication Status
txtInitializing=Alustetaan
#XFLD: text for values shown in column Replication Status
txtLoading=Ladataan
#XFLD: text for values shown in column Replication Status
txtActive=Aktiivinen
#XFLD: text for values shown in column Replication Status
txtAvailable=Käytettävissä
#XFLD: text for values shown in column Replication Status
txtError=Virhe
#XFLD: text for values shown in column Replication Status
txtPaused=Keskeytetty
#XFLD: text for values shown in column Replication Status
txtDisconnected=Katkaistu
#XFLD: text for partially Persisted views
partiallyPersisted=Osittain pysyvä

#XFLD: activity text
REPLICATE=Replikoi
#XFLD: activity text
REMOVE_REPLICATED_DATA=Poista replikoidut tiedot
#XFLD: activity text
DISABLE_REALTIME=Poista reaaliaikaisen tietojen replikoinnin aktivointi
#XFLD: activity text
REMOVE_PERSISTED_DATA=Poista pysyvät tiedot
#XFLD: activity text
PERSIST=Säilytä
#XFLD: activity text
EXECUTE=Suorita
#XFLD: activity text
TASKLOG_CLEANUP=Tacklog_Cleanup
#XFLD: activity text
CANCEL_REPLICATION=Peruuta replikointi
#XFLD: activity text
MODEL_IMPORT=Mallin tuonti
#XFLD: activity text
NONE=Ei mitään
#XFLD: activity text
CANCEL_PERSISTENCY=Peruuta pysyvyys
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Analysoi näkymä
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Peruuta näkymän analysoija

#XFLD: severity text
INFORMATION=Tiedot
#XFLD: severity text
SUCCESS=Onnistuminen
#XFLD: severity text
WARNING=Varoitus
#XFLD: severity text
ERROR=Virhe
#XFLD: text for values shown for Ascending sort order
SortInAsc=Lajittele nousevasti
#XFLD: text for values shown for Descending sort order
SortInDesc=Lajittele laskevasti
#XFLD: filter text for task log columns
Filter=Suodata
#XFLD: object text for task log columns
Object=Objekti
#XFLD: space text for task log columns
crossSpace=Tila

#XBUT: label for remote data access
REMOTE=Etä
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Replikoitu (reaaliaikainen)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Replikoitu (tilannevedos)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=Reaaliaikainen replikointi on lukittu virheen vuoksi. Kun virhe on korjattu, voit jatkaa reaaliaikaista replikointia "Yritä uudelleen" -toimella.
ERROR_MSG=Reaaliaikainen replikointi on lukittu virheen vuoksi.
RETRY_FAILED_ERROR=Prosessin toistoyritys epäonnistui, virhe.
LOG_INFO_DETAILS=Lokeja ei generoida, kun tiedot replikoidaan reaaliaikatilassa. Näytetyt lokit liittyvät aiempiin toimiin.

#XBUT: Partitioning label
partitionMenuText=Osiointi
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Luo osio
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Muokkaa osiota
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Poista osio
#XFLD: Initial text
InitialPartitionText=Määritä osiot määrittämällä perusteet suurten tietojoukkojen jakamiseksi pienempiin joukkoihin.
DefinePartition=Määritä osiot
#XFLD: Message text
partitionChangedInfo=Osion määritys on muuttunut viimeisen replikoinnin jälkeen. Muutokset otetaan käyttöön seuraavassa tietojen latauksessa.
#XFLD: Message text
REAL_TIME_WARNING=Osiointia käytetään vain uutta tilannevedosta ladattaessa. Sitä ei käytetä reaaliaikaisessa replikoinnissa.
#XFLD: Message text
loadSelectedPartitions=Tietojen muuttaminen pysyviksi aloitettu valituille osioille - ”{0}”
#XFLD: Message text
loadSelectedPartitionsError=Tietojen muuttaminen pysyviksi epäonnistui valituille osioille - ”{0}”
#XFLD: Message text
viewpartitionChangedInfo=Osion määritys on muuttunut viimeisimmän pysyvyysajon jälkeen. Muutosten käyttöönotossa seuraava tietojen lataus on täydellinen tilannevedos lukitut osiot mukaan lukien. Kun täydellinen lataus on valmis, voit ajaa tietoja yksittäisille osioille.
#XFLD: Message text
viewpartitionChangedInfoLocked=Osion määritys on muuttunut viimeisimmän pysyvyysajon jälkeen. Muutosten toteuttamiseksi seuraava tietojen lataus on täydellinen tilannevedos lukittuja ja muuttumattomia osiovälejä lukuun ottamatta. Kun tämä lataus on valmis, voit taas ladata valitut osiot.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Taulun replikointi
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Aikataulun replikointi
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Luo aikataulu
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Muokkaa aikataulua
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Poista aikataulu
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Lataa uusi tilannevedos
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Aloita tietojen replikointi
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Poista replikoidut tiedot
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Aktivoi reaaliaikainen haku
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Aktivoi reaaliaikainen tietojen replikointi
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Poista reaaliaikaisen tietojen replikoinnin aktivointi
#XFLD: Message for replicate table action
replicateTableText=Taulun replikointi
#XFLD: Message for replicate table action
replicateTableTextNew=Tietojen replikointi
#XFLD: Message to schedule task
scheduleText=Aikataulu
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Näkymän pysyvyys
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Tietojen pysyvyys
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Lataa uusi tilannevedos
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Aloita tietojen pysyvyys
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Poista pysyvät tiedot
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Ositettu käsittely
#XBUT: Label for scheduled replication
scheduledTxt=Ajoitettu
#XBUT: Label for statistics button
statisticsTxt=Tilasto
#XBUT: Label for create statistics
createStatsTxt=Luo tilasto
#XBUT: Label for edit statistics
editStatsTxt=Muokkaa tilastoa
#XBUT: Label for refresh statistics
refreshStatsTxt=Päivitä tilasto
#XBUT: Label for delete statistics
dropStatsTxt=Poista tilasto
#XMSG: Create statistics success message
statsSuccessTxt=Tyypin {0} tilaston luonti aloitettu kohteelle {1}.
#XMSG: Edit statistics success message
statsEditSuccessTxt=Tilaston tyypin muutos tyypiksi {0} aloitettu kohteelle {1}.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Tilaston päivitys aloitettu kohteelle {0}.
#XMSG: Drop statistics success message
statsDropSuccessTxt={0} - tilaston poisto onnistui
#XMSG: Create statistics error message
statsCreateErrorTxt=Tilaston luonnissa tapahtui virhe
#XMSG: Edit statistics error message
statsEditErrorTxt=Tilaston muokkauksessa tapahtui virhe
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Tilaston päivityksessä tapahtui virhe
#XMSG: Drop statistics error message
statsDropErrorTxt=Tilaston poistamisessa tapahtui virhe
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Haluatko varmasti poistaa tilastotiedot?
startPersistencyAdvisorLabel=Käynnistä View Analyzer

#Partition related texts
#XFLD: Label for Column
column=Sarake
#XFLD: Label for No of Partition
noOfPartitions=Osioiden lukumäärä
#XFLD: Label for Column
noOfParallelProcess=Rinnakkaisten prosessien lukumäärä
#XFLD: Label text
noOfLockedPartition=Lukittujen osioiden lukumäärä
#XFLD: Label for Partition
PARTITION=Osiot
#XFLD: Label for Column
AVAILABLE=Käytettävissä
#XFLD: Statistics Label
statsLabel=Tilasto
#XFLD: Label text
COLUMN=Sarake:
#XFLD: Label text
PARALLEL_PROCESSES=Rinnakkaiset prosessit:
#XFLD: Label text
Partition_Range=Osioväli
#XFLD: Label text
Name=Nimi
#XFLD: Label text
Locked=Lukittu
#XFLD: Label text
Others=MUUT
#XFLD: Label text
Delete=Poista
#XFLD: Label text
LoadData=Lataa valitut osiot
#XFLD: Label text
LoadSelectedData=Lataa valitut osiot
#XFLD: Confirmation text
LoadNewPersistenceConfirm=Tämä lataa uuden tilannevedoksen kaikille lukitsemattomille ja muutetuille osioille, ei pelkästään valitsemillesi osioille. Haluatko jatkaa?
#XFLD: Label text
Continue=Jatka

#XFLD: Label text
PARTITIONS=Osiot
#XFLD: Label text
ADD_PARTITIONS=+ Lisää osio
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Lisää osio
#XFLD: Label text
deleteRange=Poista osio
#XFLD: Label text
LOW_PLACE_HOLDER=Syötä ala-arvo
#XFLD: Label text
HIGH_PLACE_HOLDER=Syötä yläarvo
#XFLD: tooltip text
lockedTooltip=Lukitse osio ensimmäisen tiedonsiirron jälkeen

#XFLD: Button text
Edit=Muokkaa
#XFLD: Button text
CANCEL=Peruuta

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Tilaston viime päivitys
#XFLD: Statistics Fields
STATISTICS=Tilasto

#XFLD:Retry label
TEXT_Retry=Yritä uudelleen
#XFLD:Retry label
TEXT_Retry_tooltip=Yritä reaaliaikaista replikointia uudelleen, kun virhe on ratkaistu.
#XFLD: text retry
Retry=Vahvista
#XMG: Retry confirmation text
retryConfirmationTxt=Viimeinen reaaliaikainen replikointi päättyi virheen kanssa.\n Vahvista, että virhe on korjattu ja että reaaliaikainen replikointi voidaan aloittaa uudelleen.
#XMG: Retry success text
retrySuccess=Prosessin toistoyritys aloitettu onnistuneesti.
#XMG: Retry fail text
retryFail=Prosessin toistoyritys epäonnistui.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Luo tilasto
#XMSG: activity message for edit statistics
DROP_STATISTICS=Poista tilasto
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Päivitä tilasto
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Muokkaa tilastoa
#XMSG: Task log message started task
taskStarted=Tehtävä {0} on käynnistynyt.
#XMSG: Task log message for finished task
taskFinished=Tehtävä {0} päättyi tilaan {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=Tehtävä {0} lopetettu {2} tilan {1} kanssa.
#XMSG: Task {0} has input parameters
taskHasInputParameters=Tehtävällä {0} on syöttöparametreja.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=Tehtävä {0} päättyi odottamattomaan virheeseen. Tehtävän tilaksi on asetettu {1}.
#XMSG: Task log message for failed task
failedToEnd=Tilaksi ei voitu asettaa {0} tai lukituksen poisto epäonnistui.
#XMSG: Task log message
lockNotFound=Prosessia ei voitu päättää, koska lukitus puuttuu: tehtävä on ehkä peruutettu.
#XMSG: Task log message failed task
failedOverwrite={1} on jo lukinnut tehtävän {0}. Siitä syystä se epäonnistui seuraavaan virheeseen: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Toinen tehtävä otti tämän tehtävän lukituksen käsiteltäväksi.
#XMSG: Task log message failed takeover
failedTakeover=Olemassa olevaa tehtävää ei voitu ottaa käsiteltäväksi.
#XMSG: Task log message successful takeover
successTakeover=Jäljelle jäänyt lukitus oli vapautettava. Tämän tehtävän uusi lukitus on määritetty.
#XMSG: Tasklog Dialog Details
txtDetails=Ajon aikana käsitellyt etälauseet voi näyttää osaspesifien viestien lisätiedoissa etäkyselynvalvonnan avaamalla.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=Etä-SQL-lauseet on poistettu tietokannasta, eikä niitä voi näyttää.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Etäkyselyitä, joilla on muihin tiloihin kohdistettuja yhteyksiä, ei voi näyttää. Siirry etäkyselynvalvontaan ja käytä lauseen ID:tä niiden suodattamiseen.
#XMSG: Task log message for parallel check error
parallelCheckError=Tehtävää ei voi käsitellä, koska toinen tehtävä on käynnissä ja estää tämän tehtävän.
#XMSG: Task log message for parallel running task
parallelTaskRunning=Ristiriitainen tehtävä on jo käynnissä.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Tila {0} ajon aikana korrelaatiotunnuksen {1} kanssa.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=Kohdistetulla käyttäjällä ei ole tehtävän suorittamiseen tarvittavia oikeuksia.

#XBUT: Label for open in Editor
openInEditor=Avaa muokkausohjelmassa
#XBUT: Label for open in Editor
openInEditorNew=Avaa tietojen muodostimessa
#XFLD:Run deails label
runDetails=Ajon lisätiedot
#XFLD: Label for Logs
Logs=Lokit
#XFLD: Label for Settings
Settings=Asetukset
#XFLD: Label for Save button
Save=Tallenna
#XFLD: Label for Standard
Standard_PO=Suorituskykyoptimoitu (suositus)
#XFLD: Label for Hana low memory processing
HLMP_MO=Muistioptimoitu
#XFLD: Label for execution mode
ExecutionMode=Ajotila
#XFLD: Label for job execution
jobExecution=Käsittelytila
#XFLD: Label for Synchronous
syncExec=Synkroninen
#XFLD: Label for Asynchronous
asyncExec=Asynkroninen
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Käytä vakiota (asynkroninen, saattaa muuttua tulevaisuudessa)
#XMSG: Save settings success
saveSettingsSuccess=SAP HANAn suoritustila muutettu.
#XMSG: Save settings failure
saveSettingsFailed=SAP HANAn suoritustilan muutos epäonnistui.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Työn suoritusta on muutettu.
#XMSG: Job Execution change failed
jobExecSettingFailed=Työn suorituksen muutos epäonnistui.
#XMSG: Text for Type
typeTxt=Tyyppi
#XMSG: Text for Monitor
monitorTxt=Valvonta
#XMSG: Text for activity
activityTxt=Toiminto
#XMSG: Text for metrics
metricsTxt=Metriikat
#XTXT: Text for Task chain key
TASK_CHAINS=Tehtäväketju
#XTXT: Text for View Key
VIEWS=Näkymä
#XTXT: Text for remote table key
REMOTE_TABLES=Etätaulu
#XTXT: Text for Space key
SPACE=Tila
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Joustava laskentasolmu
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Replikointivirta
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Älykäs haku
#XTXT: Text for Local Table
LOCAL_TABLE=Paikallinen taulu
#XTXT: Text for Data flow key
DATA_FLOWS=Tietovirta
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=SQL Script -menettely
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=BW-prosessiketju
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Näytä valvonnassa
#XTXT: Task List header text
taskListHeader=Tehtäväluettelo ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Tietovirran aiempien ajojen metriikkaa ei voida hakea.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=Lisätietojen suorituksen päättäminen ei lataudu tällä hetkellä. Yritä päivittää uudelleen.
#XFLD: Label text for the Metrices table header
metricesColLabel=Operaattorin tunniste
#XFLD: Label text for the Metrices table header
metricesType=Tyyppi
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Tietuemäärä
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Aja tehtäväketju
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Tehtäväketjun ajo on käynnistetty.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Tehtäväketjun ajo on käynnistetty kohteelle {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Tehtäväketjun ajo epäonnistui.
#XTXT: Execute button label
runLabel=Aja
#XTXT: Execute button label
runLabelNew=Käynnistä ajo
#XMSG: Filter Object header
chainsFilteredTableHeader=Suodatettu objektin mukaan: {0}
#XFLD: Parent task chain label
parentChainLabel=Ylätason tehtäväketju:
#XFLD: Parent task chain unauthorized
Unauthorized=Ei näyttöoikeutta
#XFLD: Parent task chain label
parentTaskLabel=Ylätason tehtävä:
#XTXT: Task status
NOT_TRIGGERED=Ei käynnistetty
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Siirry kokonäyttötilaan
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Poistu kokonäyttötilasta
#XTXT: Close Task log details right panel
closeRightColumn=Sulje osio
#XTXT: Sort Text
sortTxt=Lajittele
#XTXT: Filter Text
filterTxt=Suodata
#XTXT: Filter by text to show list of filters applied
filterByTxt=Suodata käyttäen perustetta
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Yli 5 minuuttia
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Yli 15 minuuttia
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Yli 1 tunti
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Viimeinen tunti
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Viimeiset 24 tuntia
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Viimeinen kuukausi
#XTXT: Messages title text
messagesText=Ilmoitukset

#XTXT Statistics information message
statisticsInfo=Etätaulujen tilastoa ei voida luoda tietojen haulla ''Replikoitu''. Jos haluat luoda tilaston, poista replikoidut tiedot etätaulujen lisätietojen valvonnasta.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Siirry etätaulun lisätietojen valvontaan

#XTXT: Repair latest failed run label
retryRunLabel=Yritä viimeistä ajoa uudelleen
#XTXT: Repair failed run label
retryRun=Yritä ajoa uudelleen
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Tehtäväketjun uudelleenyritysajo on käynnistetty
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Tehtäväketjun uudelleenyritysajo on epäonnistunut
#XMSG: Task chain child elements name
taskChainRetryChildObject=Tehtävä {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Uusi tehtävä
#XFLD Analyzed View
analyzedView=Analysoitu näkymä
#XFLD Metrics
Metrics=Metriikat
#XFLD Partition Metrics
PartitionMetrics=Osiometriikat
#XFLD Entities
Messages=Tehtäväloki
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Osioita ei ole vielä määritetty.
#XTXT: Description message for empty partition data
partitionEmptyDescText=Luo osiot määrittämällä kriteerit suurempien tietomäärien jakamiseksi pienempiin, helpommin hallittaviin osiin

#XTXT: Title Message for empty runs data
runsEmptyTitle=Lokeja ei ole vielä käytettävissä
#XTXT: Description message for empty runs data
runsEmptyDescText=Kun käynnistät uuden toiminnon (uuden tilannevedoksen latauksen, näkymän analysoinnin käynnistyksen jne.), liittyvät lokit näytetään tässä.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=Joustavan laskentasolmun {0} konfiguraatiota ei ole ylläpidetty asianmukaisesti. Tarkista määritys.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Prosessi joustavan laskentasolmun {0} lisäämiseksi epäonnistui.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Lähdeobjektin ”{0}”.”{1}” replikan luonti ja aktivointi joustavassa laskentasolmussa {2} on aloitettu.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Lähdeobjektin ”{0}”.”{1}” replikan poisto joustavasta laskentasolmusta {2} on aloitettu.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Lähdeobjektin ”{0}”.”{1}” replikan luonti ja aktivointi epäonnistui.
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Lähdeobjektin ”{0}”.”{1}” replikan poisto epäonnistui.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Tilan {0} analyyttisten kyselyjen laskennan reititys joustavaan laskentasolmuun {1} aloitettu.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Tilan {0} analyyttisten kyselyjen laskennan reititys vastaavaan joustavaan laskentasolmuun epäonnistui.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Tilan {0} analyyttisten kyselyjen laskennan uudelleenohjaus takaisin joustavasta laskentasolmusta {1} aloitettu.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Tilan {0} analyyttisten kyselyjen laskennan uudelleenohjaus takaisin koordinaattorille epäonnistui.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Tehtäväketju {0} vastaavaan joustavaan laskentasolmuun {1} on käynnistetty.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Tehtäväketjun generointi joustavalle laskentasolmulle {0} epäonnistui.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=Joustavan laskentasolmun {0} valmistelu on aloitettu määritetyllä mitoitussuunnitelmalla: vCPU:t: {1}, muisti (GiB): {2} ja tallennustilan koko (GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=Joustava laskentasolmu {0} on jo valmisteltu.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=Joustavan laskentasolmun {0} käyttömahdollisuus on jo poistettu.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=Operaatio ei ole sallittu. Pysäytä joustava laskentasolmu {0} ja käynnistä se uudelleen päivittääksesi mitoitussuunnitelman.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Prosessi joustavan laskentasolmun {0} poistoa varten epäonnistui.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=Nykyinen joustavan laskentasolmun {0} ajo aikakatkaistu.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=Joustavan laskentasolmun {0} tilan tarkistus on käynnissä...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=Tehtäväketjun generointi joustavaa laskentasolmua {0} varten on lukittu, koska ketju {1} saattaa vielä olla käynnissä.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=Lähdeobjekti ”{0}”.”{1}” on replikoitu näkymän ”{2}”.”{3}” sidonnaisuutena.
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Taulua ”{0}” ei voitu replikoida.”{1}”, koska rivitaulun replikointi on vanhentunut.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Joustavien laskentasolmujen enimmäismäärä SAP HANA Cloud -instanssia kohti on ylitetty.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=Joustavan laskentasolmun {0} suoritettava operaatio on vielä käynnissä.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Lähdetaulun {0} replikan poisto on pysäytetty teknisten ongelmien takia. Yritä myöhemmin uudelleen.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=Lähdetaulun {0} replikan luonti on pysäytetty teknisten ongelmien takia. Yritä myöhemmin uudelleen.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Joustavaa laskentasolmua ei voi käynnistää, koska käyttöraja on saavutettu tai laskettuja lohkotunteja ei ole vielä kohdistettu.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Ladataan tehtäväketjua ja valmistellaan yhteensä {0} sellaisen tehtävän ajoa, jotka ovat osa tätä ketjua.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Ristiriitainen tehtävä on jo käynnissä.
#XMSG: Replication will change
txt_replication_change=Replikointityyppi muutetaan.
txt_repl_viewdetails=Näytä lisätiedot

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Viimeisimmän ajon toistossa on ilmeisesti tapahtunut virhe, koska edellinen tehtävän ajo epäonnistui ennen kuin suunnitelma voitiin generoida.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=Tila "{0}" on lukittu.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=Toiminto {0} edellyttää, että paikallinen taulu {1} otetaan käyttöön.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=Toiminto {0} edellyttää, että deltasieppaus otetaan käyttöön paikallista taulua {1} varten.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=Toiminto {0} edellyttää, että paikallinen taulu {1} tallentaa tiedot Object Storeen.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=Toiminto {0} edellyttää, että paikallinen taulu {1} tallentaa tiedot tietokantaan.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Käynnistetään paikallisen taulun {0} poistettujen tietueiden poisto.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Käynnistetään paikallisen taulun {0} kaikkien tietueiden poisto.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Käynnistetään paikallisen taulun {0} kaikkien tietueiden poisto suodatinehdon {1} mukaisesti.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=On tapahtunut virhe poistettaessa paikallisen taulun {0} poistettuja tietueita.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=On tapahtunut virhe poistettaessa paikallisen taulun {0} kaikkia tietueita.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Kaikki kokonaan käsitellyt tietueet, joiden muutoslaji on ”Poistettu” ja jotka ovat vanhempia kuin {0} päivää, poistetaan.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Kaikki kokonaan käsitellyt tietueet, joiden muutoslaji on ”Poistettu” ja jotka ovat vanhempia kuin {0}, poistetaan.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} tietuetta on poistettu.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} tietuetta on merkitty poistettaviksi.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=Paikallisen taulun {0} poistettujen tietueiden poisto on päätetty.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Paikallisen taulun {0} kaikkien tietueiden poisto on päätetty.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Käynnistetään paikallisen taulun {0} tietueiden merkitseminen "poistetuiksi".
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Käynnistetään paikallisen taulun {0} kaikkien tietueiden poisto suodatinehdon {1} mukaisesti.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Paikallisen taulun {0} tietueiden merkitseminen "poistetuiksi" on päätetty.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Tietomuutoksia ladataan tilapäisesti tauluun {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Tietomuutokset on ladattu tilapäisesti tauluun {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Tietomuutokset on käsitelty ja poistettu taulusta {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Yhteyden lisätiedot.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Aloitetaan paikallisen taulun (tiedosto) optimointia.
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Paikallisen taulun (tiedosto) optimoinnin aikana tapahtui virhe.
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Tapahtui virhe. Paikallisen taulun (tiedosto) optimointi on keskeytetty.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Optimoidaan paikallista taulua (tiedosto)...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=Paikallinen taulu (tiedosto) on optimoitu.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=Paikallinen taulu (tiedosto) on optimoitu.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=Taulu on optimoitu Z-järjestyksen sarakkeilla: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Tapahtui virhe. Paikallisen taulun (tiedosto) lyhennys on keskeytetty.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Lyhennetään paikallista taulua (tiedosto)...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=Saapuvien puskurin sijainti paikallista taulua (tiedosto) varten on poistettu.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Aloitetaan paikallisen taulun (tiedosto) tyhjennys (kaikkien täysin käsiteltyjen tietueiden poisto).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Paikallisen taulun (tiedosto) tyhjennyksen aikana tapahtui virhe.
#XMSG: Task log message
LTF_VACUUM_STOPPED=Tapahtui virhe. Paikallisen taulun (tiedosto) tyhjennys on keskeytetty.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Tyhjennetään paikallista taulua (tiedosto)...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=Tyhjennys on valmis.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Kaikki kokonaan käsitellyt tietueet, jotka ovat vanhempia kuin {0} päivää, poistetaan.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Kaikki kokonaan käsitellyt tietueet, jotka ovat vanhempia kuin {0}, poistetaan.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Aloitetaan uusien tietueiden yhdistäminen paikalliseen tauluun (tiedosto).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Aloitetaan uusien tiedostojen yhdistäminen paikalliseen tauluun (tiedosto) käyttämällä "Yhdistä tiedot automaattisesti" -asetusta.
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Aloitetaan uusien tiedostojen yhdistäminen paikalliseen tauluun (tiedosto). Tämä tehtävä käynnistettiin API-yhdistämispyynnöllä.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Yhdistetään uusia tietueita paikalliseen tauluun (tiedosto).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Uusien tietueiden paikalliseen tauluun (tiedosto) yhdistämisen aikana tapahtui virhe.
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=Paikallinen taulu (tiedosto) on yhdistetty.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Tapahtui virhe. Paikallisen taulun (tiedosto) yhdistäminen on keskeytetty.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=Paikallisen taulun (tiedosto) yhdistäminen on epäonnistunut virheen vuoksi, mutta operaatio onnistui osittain, ja joitakin tietoja on jo yhdistetty.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Tapahtui aikakatkaisuvirhe. Toimintoa {0} on suoritettu {1} tuntia.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=Asynkronista suoritusta ei voitu käynnistää suuren järjestelmän kuormituksen takia. Avaa järjestelmänvalvonta ja tarkista käynnissä olevat tehtävät.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=Asynkroninen suoritus on peruutettu.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=Tehtävä {0} suoritettiin muistin rajoissa: {1} ja {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID={0} - tehtävä suoritettiin resurssitunnuksen {1} kanssa.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=Hae ja korvaa on käynnistetty paikallisessa taulussa (tiedosto).
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Hae ja korvaa on päätetty paikallisessa taulussa (tiedosto).
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Hae ja korvaa on epäonnistunut paikallisessa taulussa (tiedosto).

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=On tapahtunut virhe. Paikallisen taulun (tiedosto) tilaston päivitys on pysäytetty.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=Tehtävä epäonnistui SAP HANA -tietokannan muisti ei riitä -virheen vuoksi.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=Tehtävä epäonnistui SAP HANA -pääsynvalvonnan hylkäyksen vuoksi.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=Tehtävä epäonnistui, koska aktiivisia SAP HANA -yhteyksiä on liian monta.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=Yritä uudelleen -toimintoa ei voitu suorittaa, koska uudelleenyritykset sallitaan vain sisäkkäisen tehtäväketjun ylätasolla.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Epäonnistuneiden alatason tehtävien lokeja ei enää ole käytettävissä uudelleenyrityksen jatkamiseksi.


####Metrics Labels

performanceOptimized=Suorituskykyoptimoitu
memoryOptimized=Muistioptimoitu

JOB_EXECUTION=Työn suoritus
EXECUTION_MODE=Ajotila
NUMBER_OF_RECORDS_OVERALL=Pysyvien tietueiden lukumäärä
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Etälähteestä luettujen tietueiden lukumäärä
RUNTIME_MS_REMOTE_EXECUTION_TIME=Etälähteen käsittelyaika
MEMORY_CONSUMPTION_GIB=SAP HANA -huippumuisti
NUMBER_OF_PARTITIONS=Osioiden lukumäärä
MEMORY_CONSUMPTION_GIB_OVERALL=SAP HANA -huippumuisti
NUMBER_OF_PARTITIONS_LOCKED=Lukittujen osioiden lukumäärä
PARTITIONING_COLUMN=Osiointisarake
HANA_PEAK_CPU_TIME=SAP HANAn CPU-kokonaisaika
USED_IN_DISK=Käytetty muisti
INPUT_PARAMETER_PARAMETER_VALUE=Syöttöparametri
INPUT_PARAMETER=Syöttöparametri
ECN_ID=Joustavan laskentasolmun nimi

DAC=Tietojen haun ohjaukset
YES=Kyllä
NO=Ei
noofrecords=Tietueiden lukumäärä
partitionpeakmemory=SAP HANA -huippumuisti
value=Arvo
metricsTitle=Metriikat ({0})
partitionmetricsTitle=Osiot ({0})
partitionLabel=Osio
OthersNotNull=Arvoja ei sisällytetty alueisiin
OthersNull=Tyhjäarvot
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Viimeisessä tietojen pysyvyysajossa käytetyt asetukset:
#XMSG: Message for input parameter name
inputParameterLabel=Syöttöparametri
#XMSG: Message for input parameter value
inputParameterValueLabel=Arvo
#XMSG: Message for persisted data
inputParameterPersistedLabel=Pysyväksi muuttamisaika
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Poista tiedot
REMOVE_DELETED_RECORDS=Poista poistetut tietueet
MERGE_FILES=Yhdistä tiedostot
OPTIMIZE_FILES=Optimoi tiedostot
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Näytä SAP BW -siltavalvonnassa

ANALYZE_PERFORMANCE=Analysoi suorituskyky
CANCEL_ANALYZE_PERFORMANCE=Peruuta suorituskyvyn analysointi

#XFLD: Label for frequency column
everyLabel=Aina
#XFLD: Plural Recurrence text for Hour
hoursLabel=Tunnit
#XFLD: Plural Recurrence text for Day
daysLabel=Pv
#XFLD: Plural Recurrence text for Month
monthsLabel=kuukauden välein
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuutit

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Näkymän pysyvyyden vianmääritysopas</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Muisti ei riitä prosessille. Tietoja ei voi muuttaa pysyviksi näkymää "{0}" varten. Katso lisätietoja Help Portalista lisätietoja muistivirheistä. Harkitse näkymän analysoijan tarkistamista, jos haluat analysoida muistin käytön kompleksisuusnäkymää.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Ei-relevantti
OPEN_BRACKET=(
CLOSE_BRACKET=)
