
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Сведения о журнале
#XFLD: Header
TASK_LOGS=Журналы задач ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Прогоны ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Просмотреть сведения
#XFLD: Button text
STOP=Остановить прогон
#XFLD: Label text
RUN_START=Начало последнего прогона
#XFLD: Label text
RUN_END=Конец последнего прогона
#XFLD: Label text
RUNTIME=Продолжительность
#XTIT: Count for Messages
txtDetailMessages=Сообщения ({0})
#XFLD: Label text
TIME=Метка времени
#XFLD: Label text
MESSAGE=Сообщение
#XFLD: Label text
TASK_STATUS=Категория
#XFLD: Label text
TASK_ACTIVITY=Активность
#XFLD: Label text
RUN_START_DETAILS=Начало
#XFLD: Label text
RUN_END_DETAILS=Конец
#XFLD: Label text
LOGS=Прогоны
#XFLD: Label text
STATUS=Статус
#XFLD: Label text
RUN_STATUS=Статус выполнения
#XFLD: Label text
Runtime=Продолжительность
#XFLD: Label text
RuntimeTooltip=Продолжительность (чч : мм : сс)
#XFLD: Label text
TRIGGEREDBY=Инициировал
#XFLD: Label text
TRIGGEREDBYNew=Выполнил
#XFLD: Label text
TRIGGEREDBYNewImp=Прогон запустил
#XFLD: Label text
EXECUTIONTYPE=Тип выполнения
#XFLD: Label text
EXECUTIONTYPENew=Тип выполнения
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Родительское пространство цепочки
#XFLD: Refresh tooltip
TEXT_REFRESH=Обновить
#XFLD: view Details link
VIEW_ERROR_DETAILS=Просмотреть сведения
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Выгрузить дополнительные сведения
#XMSG: Download completed
downloadStarted=Выгрузка запущена
#XMSG: Error while downloading content
errorInDownload=Во время выгрузки произошла ошибка.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Просмотреть сведения
#XBTN: cancel button of task details dialog
TXT_CANCEL=Отменить
#XBTN: back button from task details
TXT_BACK=Назад
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Задача выполнена
#XFLD: Log message with failed status
MSG_LOG_FAILED=Задача не выполнена
#XFLD: Master and detail table with no data
No_Data=Нет данных
#XFLD: Retry tooltip
TEXT_RETRY=Повторить
#XFLD: Cancel Run label
TEXT_CancelRun=Отменить прогон
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Загрузка очистки с ошибкой
#XMSG:button copy sql statement
txtSQLStatement=Скопировать инструкцию SQL
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Открыть монитор дистанционных запросов
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=Чтобы просмотреть дистанционные инструкции SQL, выберите "Открыть монитор дистанционных запросов".
#XMSG:button ok
txtOk=ОК
#XMSG: button close
txtClose=Закрыть
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=Операция отмены прогона для объекта "{0}" запущена.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=Операция отмены прогона для объекта "{0}" не выполнена.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=Операция отмены прогона для объекта "{0}" больше не возможна, так как изменен статус тиражирования.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Ни одному журналу задач не присвоен статус "Выполняется".
#XMSG: message for conflicting task
Task_Already_Running=Для объекта "{0}" уже выполняется конфликтующая задача.
#XFLD: Label for no task log with running state title
actionInfo=Информация об операции
#XMSG Copied to clipboard
copiedToClip=Скопировано в буфер обмена
#XFLD copy
Copy=Скопировать
#XFLD copy correlation ID
CopyCorrelationID=Скопировать ид. корреляции
#XFLD Close
Close=Закрыть
#XFLD: show more Label
txtShowMore=Показать больше
#XFLD: message Label
messageLabel=Сообщение:
#XFLD: details Label
detailsLabel=Сведения:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Выполнение \r\n инструкции SQL:
#XFLD:statementId Label
statementIdLabel=Ид. инструкции:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Число дистанционных \r\n инструкций SQL:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Число инструкций
#XFLD: Space Label
txtSpaces=Пространство
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Не авторизованные пространства
#XFLD: Privilege Error Text
txtPrivilegeError=Нет привилегий на просмотр этих данных.
#XFLD: Label for Object Header
DATA_ACCESS=Доступ к данным
#XFLD: Label for Object Header
SCHEDULE=Планирование
#XFLD: Label for Object Header
DETAILS=Сведения
#XFLD: Label for Object Header
LATEST_UPDATE=Последнее обновление
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Последнее изменение (источник)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Частота обновления
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Плановая периодичность
#XFLD: Label for Object Header
NEXT_RUN=Следующее выполнение
#XFLD: Label for Object Header
CONNECTION=Соединение
#XFLD: Label for Object Header
DP_AGENT=Агент DP
#XFLD: Label for Object Header
USED_IN_MEMORY=Использование памяти для данных (МиБ)
#XFLD: Label for Object Header
USED_DISK=Использование диска для данных (МиБ)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Размер in-memory (МиБ)
#XFLD: Label for Object Header
USED_DISK_NEW=Размер на диске (МиБ)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Число записей

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Отметить как не выполненное
SET_TO_FAILED_ERR=Эта задача выполнялась, но пользователь отметил ее как НЕ ВЫПОЛНЕННУЮ.
#XFLD: Label for stopped failed
FAILLOCKED=Прогон уже выполняется
#XFLD: sub status STOPPED
STOPPED=Остановлено
STOPPED_ERR=Эта задача остановлена, но откат не выполнен.
#XFLD: sub status CANCELLED
CANCELLED=Отменено
CANCELLED_ERR=Выполнение этой задачи отменено после запуска. Был выполнен откат данных, и восстановлено состояние до инициирования прогона задачи.
#XFLD: sub status LOCKED
LOCKED=Блокировано
LOCKED_ERR=Такая же задача уже выполнялась, поэтому данное задание не удалось выполнить параллельно существующему выполнению задачи.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Особая ситуация задачи
TASK_EXCEPTION_ERR=В задаче возникла неизвестная ошибка во время выполнения.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Особая ситуация выполнения задачи
TASK_EXECUTE_EXCEPTION_ERR=В задаче возникла ошибка во время выполнения.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Не авторизовано
UNAUTHORIZED_ERR=Пользователь не прошел аутентификацию, был блокирован или удален.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Запрещено
FORBIDDEN_ERR=Присвоенный пользователь не имеет полномочий на выполнение этой задачи.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Не инициировано
FAIL_NOT_TRIGGERED_ERR=Это задание задачи не удалось выполнить из-за сбоя системы или недоступности некоторой части системы базы данных во время планового выполнения. Ожидайте следующего запланированного выполнения задания или перепланируйте его.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Планирование отменено
SCHEDULE_CANCELLED_ERR=Это задание задачи не удалось выполнить из-за внутренней ошибки. Обратитесь в службу поддержки SAP и сообщите ид. корреляции и метку времени из сведений журнала этого задания задачи.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Предыдущий прогон выполняется
SUCCESS_SKIPPED_ERR=Это выполнение задачи не инициировано, так как предыдущий прогон этой задачи еще выполняется.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Отсутствует владелец
FAIL_OWNER_MISSING_ERR=Это задание задачи не удалось выполнить, так как не присвоенного пользователя системы. Присвойте пользователя-владельца заданию.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Согласие недоступно
FAIL_CONSENT_NOT_AVAILABLE_ERR=Вы не разрешили SAP выполнять цепочки задач или планировать задачи интеграции данных от вашего имени. Выберите соответствующую опцию, чтобы дать согласие.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Согласие истекло
FAIL_CONSENT_EXPIRED_ERR=Разрешение SAP выполнять цепочки задач или планировать задачи интеграции данных от вашего имени истекло. Выберите соответствующую опцию, чтобы продлить ваше согласие.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Согласие аннулировано
FAIL_CONSENT_INVALIDATED_ERR=Эту задачу не удалось выполнить, скорее всего, из-за изменения конфигурации провайдера идентичности арендатора. В этом случае новые задания задачи не могут выполняться и планироваться от имени затронутого пользователя. Если присвоенный пользователь еще существует в новом провайдере идентичности, отзовите согласие на планирование и снова предоставьте его. Если присвоенный пользователь больше не существует, присвойте нового владельца задания задачи и дайте согласие на планирование задачи. Подробнее см. в следующей SAP-ноте: https://launchpad.support.sap.com/#/notes/3089828.
TASK_EXECUTOR_ERROR=Исполнитель задачи
TASK_EXECUTOR_ERROR_ERR=В задаче возникла внутренняя ошибка, скорее всего, в шагах подготовки к выполнению, и задачу не удалось запустить.
PREREQ_NOT_MET=Предпосылка не выполнена
PREREQ_NOT_MET_ERR=Не удалось выполнить эту задачу из-за проблем с ее определением. Например, объект не развернут, цепочка задач содержит циклическую логику или SQL ракурса недействителен.
RESOURCE_LIMIT_ERROR=Ошибка из-за лимита ресурсов
RESOURCE_LIMIT_ERROR_ERR=Невозможно выполнить задачу, так как необходимые ресурсы недоступны или заняты.
FAIL_CONSENT_REFUSED_BY_UMS=В согласии отказано
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Эту задачу не удалось выполнить в запланированных прогонах или цепочках задач из-за изменения в конфигурации провайдера идентичности пользователя в арендаторе. Для получения дополнительной информации см. следующую SAP-ноту: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Запланировано
#XFLD: status text
SCHEDULEDNew=Постоянно
#XFLD: status text
PAUSED=Приостановлено
#XFLD: status text
DIRECT=Напрямую
#XFLD: status text
MANUAL=Вручную
#XFLD: status text
DIRECTNew=Простой
#XFLD: status text
COMPLETED=Выполнено
#XFLD: status text
FAILED=Ошибка
#XFLD: status text
RUNNING=Выполняется
#XFLD: status text
none=Нет
#XFLD: status text
realtime=В реальном времени
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Субзадача
#XFLD: New Data available in the file
NEW_DATA=Новые данные

#XFLD: text for values shown in column Replication Status
txtOff=Выкл.
#XFLD: text for values shown in column Replication Status
txtInitializing=Инициализация
#XFLD: text for values shown in column Replication Status
txtLoading=Загрузка
#XFLD: text for values shown in column Replication Status
txtActive=Активно
#XFLD: text for values shown in column Replication Status
txtAvailable=Доступно
#XFLD: text for values shown in column Replication Status
txtError=Ошибка
#XFLD: text for values shown in column Replication Status
txtPaused=Приостановлено
#XFLD: text for values shown in column Replication Status
txtDisconnected=Разъединено
#XFLD: text for partially Persisted views
partiallyPersisted=Частично устойчиво сохранено

#XFLD: activity text
REPLICATE=Тиражировать
#XFLD: activity text
REMOVE_REPLICATED_DATA=Удалить тиражированные данные
#XFLD: activity text
DISABLE_REALTIME=Деактивировать тиражирование данных в реальном времени
#XFLD: activity text
REMOVE_PERSISTED_DATA=Удалить сохраненные данные
#XFLD: activity text
PERSIST=Сохранить
#XFLD: activity text
EXECUTE=Выполнить
#XFLD: activity text
TASKLOG_CLEANUP=Tacklog_Cleanup
#XFLD: activity text
CANCEL_REPLICATION=Отменить тиражирование
#XFLD: activity text
MODEL_IMPORT=Импорт модели
#XFLD: activity text
NONE=Нет
#XFLD: activity text
CANCEL_PERSISTENCY=Отменить устойчивость
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Анализ ракурса
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Отменить анализ ракурса

#XFLD: severity text
INFORMATION=Информация
#XFLD: severity text
SUCCESS=Успешно
#XFLD: severity text
WARNING=Предупреждение
#XFLD: severity text
ERROR=Ошибка
#XFLD: text for values shown for Ascending sort order
SortInAsc=Сортировать по восходящей
#XFLD: text for values shown for Descending sort order
SortInDesc=Сортировать по нисходящей
#XFLD: filter text for task log columns
Filter=Фильтровать
#XFLD: object text for task log columns
Object=Объект
#XFLD: space text for task log columns
crossSpace=Пространство

#XBUT: label for remote data access
REMOTE=Дистанционно
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Тиражировано (в реальном времени)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Тиражировано (мгновенный снимок)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=Тиражирование в реальном времени блокировано из‑за ошибки. После устранения ошибки выберите "Повторить" для продолжения тиражирования в реальном времени.
ERROR_MSG=Тиражирование в реальном времени блокировано из‑за ошибки.
RETRY_FAILED_ERROR=Ошибка при попытке возобновить обработку.
LOG_INFO_DETAILS=При тиражировании данных в реальном времени журналы не сгенерированы. Выведенные журналы относятся к предыдущим действиям.

#XBUT: Partitioning label
partitionMenuText=Разделение
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Создать раздел
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Редактировать раздел
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Удалить раздел
#XFLD: Initial text
InitialPartitionText=Определите разделы, указав критерии разделения больших наборов данных на меньшие.
DefinePartition=Определить разделы
#XFLD: Message text
partitionChangedInfo=Определение разделов изменилось с последнего тиражирования. Изменения будут применены при следующей загрузке данных.
#XFLD: Message text
REAL_TIME_WARNING=Разделение применяется только при загрузке нового мгновенного снимка. Оно не применяется к тиражированию в реальном времени.
#XFLD: Message text
loadSelectedPartitions=Начато устойчивое сохранение данных для выбранных разделов "{0}"
#XFLD: Message text
loadSelectedPartitionsError=Не выполнено устойчивое сохранение данных для выбранных разделов "{0}"
#XFLD: Message text
viewpartitionChangedInfo=Определение разделов изменилось с последнего прогона устойчивого сохранения. Чтобы применить изменения, следующая загрузка данных будет полным мгновенным снимком, включая блокированные разделы. По завершении этой полной загрузки вы сможете выполнять данные для отдельных разделов.
#XFLD: Message text
viewpartitionChangedInfoLocked=Определение разделов изменилось с последнего прогона устойчивого сохранения. Чтобы применить изменения, следующая загрузка данных будет полным мгновенным снимком, исключая блокированные и не измененные диапазоны разделов. По завершении этой загрузки вы снова сможете загрузить выбранные разделы.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Тиражирование таблицы
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Запланировать тиражирование
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Создать планирование
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Редактировать планирование
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Удалить планирование
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Загрузить новый мгновенный снимок
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Запустить тиражирование данных
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Удалить тиражированные данные
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Активировать доступ в реальном времени
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Активировать тиражирование данных в реальном времени
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Деактивировать тиражирование данных в реальном времени
#XFLD: Message for replicate table action
replicateTableText=Тиражирование таблицы
#XFLD: Message for replicate table action
replicateTableTextNew=Тиражирование данных
#XFLD: Message to schedule task
scheduleText=Запланировать
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Устойчивость ракурса
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Устойчивость данных
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Загрузить новый мгновенный снимок
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Запустить устойчивое сохранение данных
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Удалить сохраненные данные
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Обработка по частям
#XBUT: Label for scheduled replication
scheduledTxt=Запланировано
#XBUT: Label for statistics button
statisticsTxt=Статистика
#XBUT: Label for create statistics
createStatsTxt=Создать статистику
#XBUT: Label for edit statistics
editStatsTxt=Редактировать статистику
#XBUT: Label for refresh statistics
refreshStatsTxt=Обновить статистику
#XBUT: Label for delete statistics
dropStatsTxt=Удалить статистику
#XMSG: Create statistics success message
statsSuccessTxt=Запущено создание статистики типа {0} для {1}.
#XMSG: Edit statistics success message
statsEditSuccessTxt=Запущено изменение типа статистики на {0} для {1}.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Запущено обновление статистики для {0}.
#XMSG: Drop statistics success message
statsDropSuccessTxt=Статистика успешно удалена для {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=Ошибка при создании статистики
#XMSG: Edit statistics error message
statsEditErrorTxt=Ошибка при изменении статистики
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Ошибка при обновлении статистики
#XMSG: Drop statistics error message
statsDropErrorTxt=Ошибка при удалении статистики
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Действительно удалить данные статистики?
startPersistencyAdvisorLabel=Запустить анализ ракурса

#Partition related texts
#XFLD: Label for Column
column=Столбец
#XFLD: Label for No of Partition
noOfPartitions=Число разделов
#XFLD: Label for Column
noOfParallelProcess=Число параллельных процессов
#XFLD: Label text
noOfLockedPartition=Число блокированных процессов
#XFLD: Label for Partition
PARTITION=Разделы
#XFLD: Label for Column
AVAILABLE=Доступно
#XFLD: Statistics Label
statsLabel=Статистика
#XFLD: Label text
COLUMN=Столбец:
#XFLD: Label text
PARALLEL_PROCESSES=Параллельные процессы:
#XFLD: Label text
Partition_Range=Диапазон раздела
#XFLD: Label text
Name=Имя
#XFLD: Label text
Locked=Блокировано
#XFLD: Label text
Others=ПРОЧЕЕ
#XFLD: Label text
Delete=Удалить
#XFLD: Label text
LoadData=Загрузить выбранные разделы
#XFLD: Label text
LoadSelectedData=Загрузить выбранные разделы
#XFLD: Confirmation text
LoadNewPersistenceConfirm=Будет загружен новый мгновенный снимок для всех разблокированных и измененных разделов, а не только выбранных. Продолжить?
#XFLD: Label text
Continue=Продолжить

#XFLD: Label text
PARTITIONS=Разделы
#XFLD: Label text
ADD_PARTITIONS=+ Добавить раздел
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Добавить раздел
#XFLD: Label text
deleteRange=Удалить раздел
#XFLD: Label text
LOW_PLACE_HOLDER=Ввести низкое значение
#XFLD: Label text
HIGH_PLACE_HOLDER=Ввести высокое значение
#XFLD: tooltip text
lockedTooltip=Блокировать раздел после начальной загрузки

#XFLD: Button text
Edit=Редактировать
#XFLD: Button text
CANCEL=Отменить

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Последнее обновление статистики
#XFLD: Statistics Fields
STATISTICS=Статистика

#XFLD:Retry label
TEXT_Retry=Повторить
#XFLD:Retry label
TEXT_Retry_tooltip=Повторить тиражирование в реальном времени после устранения ошибки.
#XFLD: text retry
Retry=Подтвердить
#XMG: Retry confirmation text
retryConfirmationTxt=Последнее тиражирование в реальном времени завершилось ошибкой.\n Подтвердите, что ошибка исправлена и тиражирование в реальном времени может быть перезапущено.
#XMG: Retry success text
retrySuccess=Процесс успешно перезапущен.
#XMG: Retry fail text
retryFail=Не удалось перезапустить процесс.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Создать статистику
#XMSG: activity message for edit statistics
DROP_STATISTICS=Удалить статистику
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Обновить статистику
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Редактировать статистику
#XMSG: Task log message started task
taskStarted=Задача {0} запущена.
#XMSG: Task log message for finished task
taskFinished=Задача {0} завершена со статусом {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=Задача {0} завершена в {2} со статусом {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=Задача {0} имеет параметры ввода.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=Задача {0} завершена с непредвиденной ошибкой. Статус задачи установлен на {1}.
#XMSG: Task log message for failed task
failedToEnd=Не удалось установить статус на {0} или удалить блокировку.
#XMSG: Task log message
lockNotFound=Невозможно завершить процесс, так как отсутствует блокировка: возможно, задача была отменена.
#XMSG: Task log message failed task
failedOverwrite=Задача {0} уже блокирована {1}, поэтому она выполнена со следующей ошибкой: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Блокировка этой задачи перенесена на другую задачу.
#XMSG: Task log message failed takeover
failedTakeover=Не удалось принять существующую задачу.
#XMSG: Task log message successful takeover
successTakeover=Требовалось снять существующую блокировку. Новая блокировка для этой задачи установлена.
#XMSG: Tasklog Dialog Details
txtDetails=Дистанционные инструкции, обработанные в прогоне, можно просмотреть, открыв монитор дистанционных запросов, в сведениях из сообщений по разделу.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=Дистанционные инструкции SQL удалены из базы данных, невозможно просмотреть.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Дистанционные инструкции с соединениями, присвоенными другим пространствам, невозможно просмотреть. Перейдите к монитору дистанционных запросов и используйте ид. инструкции для фильтрации.
#XMSG: Task log message for parallel check error
parallelCheckError=Невозможно обработать задачу, так как выполняется другая задача, блокирующая ее.
#XMSG: Task log message for parallel running task
parallelTaskRunning=Уже выполняется конфликтующая задача.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Статус {0} во время выполнения с ид. корреляции {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=Присвоенный пользователь не имеет полномочий на выполнение этой задачи.

#XBUT: Label for open in Editor
openInEditor=Открыть в редакторе
#XBUT: Label for open in Editor
openInEditorNew=Открыть в построителе данных
#XFLD:Run deails label
runDetails=Сведения о прогоне
#XFLD: Label for Logs
Logs=Журналы
#XFLD: Label for Settings
Settings=Настройки
#XFLD: Label for Save button
Save=Сохранить
#XFLD: Label for Standard
Standard_PO=Оптимизация производительности (рекомендуется)
#XFLD: Label for Hana low memory processing
HLMP_MO=Оптимизация памяти
#XFLD: Label for execution mode
ExecutionMode=Режим выполнения
#XFLD: Label for job execution
jobExecution=Режим обработки
#XFLD: Label for Synchronous
syncExec=Синхронно
#XFLD: Label for Asynchronous
asyncExec=Асинхронно
#XFLD: Label for default asynchronous execution
defaultAsyncExec=По умолчанию (асинхронно, может быть изменено в будущем)
#XMSG: Save settings success
saveSettingsSuccess=Режим выполнения SAP HANA изменен.
#XMSG: Save settings failure
saveSettingsFailed=Не удалось изменить режим выполнения SAP HANA.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Выполнение задания изменено.
#XMSG: Job Execution change failed
jobExecSettingFailed=Не удалось изменить выполнение задания.
#XMSG: Text for Type
typeTxt=Тип
#XMSG: Text for Monitor
monitorTxt=Монитор
#XMSG: Text for activity
activityTxt=Операция
#XMSG: Text for metrics
metricsTxt=Метрики
#XTXT: Text for Task chain key
TASK_CHAINS=Цепочка задач
#XTXT: Text for View Key
VIEWS=Ракурс
#XTXT: Text for remote table key
REMOTE_TABLES=Дистанционная таблица
#XTXT: Text for Space key
SPACE=Пространство
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Эластичный узел вычислений
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Поток тиражирования
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Интеллектуальный поиск
#XTXT: Text for Local Table
LOCAL_TABLE=Локальная таблица
#XTXT: Text for Data flow key
DATA_FLOWS=Поток данных
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=Процедура скриптов SQL
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=Цепочка процессов BW
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Ракурс в мониторе
#XTXT: Task List header text
taskListHeader=Список задач ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Метрики для исторических прогонов потока данных невозможно вызвать.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=Полные сведения о прогоне не загружаются. Попробуйте обновить.
#XFLD: Label text for the Metrices table header
metricesColLabel=Метка оператора
#XFLD: Label text for the Metrices table header
metricesType=Тип
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Число записей
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Выполнить цепочку задач
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Прогон цепочки задач запущен.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Прогон цепочки задач запущен для {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Не удалось выполнить цепочку задач.
#XTXT: Execute button label
runLabel=Выполнить
#XTXT: Execute button label
runLabelNew=Запустить прогон
#XMSG: Filter Object header
chainsFilteredTableHeader=Отфильтровано по объекту: {0}
#XFLD: Parent task chain label
parentChainLabel=Родительская цепочка задач:
#XFLD: Parent task chain unauthorized
Unauthorized=Нет полномочий на просмотр
#XFLD: Parent task chain label
parentTaskLabel=Родительская задача:
#XTXT: Task status
NOT_TRIGGERED=Не инициировано
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Перейти в полноэкранный режим
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Выйти из полноэкранного режима
#XTXT: Close Task log details right panel
closeRightColumn=Закрыть раздел
#XTXT: Sort Text
sortTxt=Сортировка
#XTXT: Filter Text
filterTxt=Фильтр
#XTXT: Filter by text to show list of filters applied
filterByTxt=Фильтровать по
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Больше 5 минут
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Больше 15 минут
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Больше 1 часа
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Последний час
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Последние 24 часа
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Последний месяц
#XTXT: Messages title text
messagesText=Сообщения

#XTXT Statistics information message
statisticsInfo=Невозможно создать статистику для дистанционных таблиц с доступом к данным "Тиражировано". Чтобы создать статистику, удалите тиражированные данные в мониторе сведений о дистанционных таблицах.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Перейти к монитору сведений о дистанционных таблицах

#XTXT: Repair latest failed run label
retryRunLabel=Повторить последний прогон
#XTXT: Repair failed run label
retryRun=Повторить прогон
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Повторный прогон цепочки задач запущен
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Повторный прогон цепочки задач не выполнен
#XMSG: Task chain child elements name
taskChainRetryChildObject=Задача {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Новая задача
#XFLD Analyzed View
analyzedView=Проанализированный ракурс
#XFLD Metrics
Metrics=Метрики
#XFLD Partition Metrics
PartitionMetrics=Метрики раздела
#XFLD Entities
Messages=Журнал задач
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Разделы еще не определены
#XTXT: Description message for empty partition data
partitionEmptyDescText=Чтобы создать разделы, укажите критерии разделения больших объемов данных на меньшие и более управляемые части.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Еще нет доступных журналов
#XTXT: Description message for empty runs data
runsEmptyDescText=После запуска новой операции (загрузка нового мгновенного снимка, запуск анализа ракурса...) вы увидите связанные журналы здесь.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=Конфигурация эластичного узла вычислений {0} не задана. Проверьте определение.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Процесс добавления эластичного узла вычислений {0} не выполнен.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Создание и активация таблицы тиражирования для исходного объекта "{0}"."{1}" в эластичном узле вычислений {2} начаты.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Удаление таблицы тиражирования для исходного объекта "{0}"."{1}" из эластичного узла вычислений {2} начато.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Создание и активация таблицы тиражирования для исходного объекта "{0}"."{1}" не выполнены.
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Удаление таблицы тиражирования для исходного объекта "{0}"."{1}" не выполнено.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Перенаправление вычисления аналитических запросов для пространства {0} в эластичный узел вычислений {1} начато.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Перенаправление вычисления аналитических запросов для пространства {0} в соответствующий эластичный узел вычислений не выполнено.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Перенаправление вычисления аналитических запросов для пространства {0} обратно из эластичного узла вычислений {1} начато.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Перенаправление вычисления аналитических запросов для пространства {0} обратно координатору не выполнено.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Цепочка задач {0} для соответствующего эластичного узла вычислений {1} инициирована.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Не удалось сгенерировать цепочку задач для эластичного узла вычислений {0}.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=Предоставление эластичного узла вычислений {0} начато со следующими размерами: виртуальные ЦП: {1}, память (ГБ): {2}, размер хранилища (ГБ): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=Эластичный узел вычислений {0} уже предоставлен.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=Предоставление эластичного узла вычислений {0} уже отменено.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=Операция не разрешена. Остановите эластичный узел вычислений {0} и перезапустите его, чтобы обновить план размеров.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Процесс удаления эластичного узла вычислений {0} не выполнен.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=Тайм-аут текущего прогона эластичного узла вычислений {0}.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=Проверка статуса эластичного узла вычислений {0} выполняется...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=Генерация цепочки задач для эластичного узла вычислений {0} блокирована, поэтому цепочка {1} может еще выполняться.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=Исходный объект "{0}"."{1}" тиражируется как зависимость ракурса "{2}"."{3}".
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Невозможно тиражировать таблицу "{0}"."{1}", так как тиражирование строковой таблицы устарело.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Превышен максимум эластичных узлов вычислений на инстанцию SAP HANA Cloud.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=Операция для эластичного узла вычислений {0} еще выполняется.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=По техническим причинам удаление реплики для исходной таблицы {0} остановлено. Повторите попытку позже.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=По техническим причинам создание реплики для исходной таблицы {0} остановлено. Повторите попытку позже.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Невозможно запустить эластичный узел вычислений, так как достигнут лимит использования или еще не выделены блокочасы вычислений.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Загрузка цепочки задач и подготовка к выполнению {0} задач, входящих в эту цепочку.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Уже выполняется конфликтующая задача
#XMSG: Replication will change
txt_replication_change=Тип тиражирования будет изменен.
txt_repl_viewdetails=Просмотреть сведения

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Вероятно, возникла ошибка при повторной попытке выполнения, поскольку выполнение предыдущей задачи закончилось ошибкой до генерации плана.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=Пространство "{0}" заблокировано.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=Для операции {0} требуется развертывание локальной таблицы {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=Для операции {0} требуется включение дельта-записи для локальной таблицы {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=Для операции {0} требуется, чтобы локальная таблица {1} хранила данные в хранилище объектов.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=Для операции {0} требуется, чтобы локальная таблица {1} хранила данные в базе данных.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Начинаем очищать удаленные записи для локальной таблицы {0}.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Начинаем удалять все записи для локальной таблицы {0}.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Начинаем удалять записи для локальной таблицы {0} согласно условию фильтра {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Произошла ошибка при очистке удаленных записей для локальной таблицы {0}.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Произошла ошибка при удалении всех записей для локальной таблицы {0}.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Удаление всех полностью обработанных записей с типом изменения "Удалено" старше {0} дн.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Удаление всех полностью обработанных записей с типом изменения "Удалено" старше {0}.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} записи(ей) удалено.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS=Помеченные для удаления записи: {0}.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=Очистка удаленных записей для локальной таблицы {0} завершена.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Удаление всех записей для локальной таблицы {0} завершено.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Начинаем помечать записи как удаленные для локальной таблицы {0}.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Начинаем помечать записи как удаленные для локальной таблицы {0} согласно условию фильтра {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Записи помечены как удаленные для локальной таблицы {0}.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Изменения данных временно загружаются в таблицу {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Изменения данных временно загружены в таблицу {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Изменения данных обработаны и удалены из таблицы {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Сведения о соединении.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Начало оптимизации локальной таблицы (файловой).
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Ошибка при оптимизации локальной таблицы (файловой).
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Ошибка. Оптимизация локальной таблицы (файловой) остановлена.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Оптимизация локальной таблицы (файловой)...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=Локальная таблица (файловая) оптимизирована.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=Локальная таблица (файловая) оптимизирована.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=Таблица оптимизирована с помощью столбцов Z-последовательности: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Ошибка. Усечение локальной таблицы (файловой) остановлено.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Усечение локальной таблицы (файловой)...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=Местоположение входящего буфера для локальной таблицы (файловой) удалено.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Начало очистки (удаления всех полностью обработанных записей) локальной таблицы (файловой).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Ошибка при очистке локальной таблицы (файловой).
#XMSG: Task log message
LTF_VACUUM_STOPPED=Ошибка. Очистка локальной таблицы (файловой) остановлена.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Очистка локальной таблицы (файловой)...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=Очистка завершена.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Удаление всех полностью обработанных записей старше {0} дн.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Удаление всех полностью обработанных записей старше {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Начало объединения новых записей с локальной таблицей (файловой).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Начало объединения новых записей с локальной таблицей (файловой) с использованием настройки "Объединить данные автоматически".
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Начало объединения новых записей с локальной таблицей (файловой). Эта задача была инициирована посредством API запроса на объединение.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Объединение новых записей с локальной таблицей (файловой).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Ошибка при объединении новых записей с локальной таблицей (файловой).
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=Локальная таблица (файловая) объединена.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Ошибка. Объединение локальной таблицы (файловой) остановлено.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=Объединение локальной таблицы (файловой) завершилось неудачно из-за ошибки, но операция была частично выполнена и некоторые данные уже объединены.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Произошел тайм-аут. Операция {0} выполняется {1} ч.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=Не удалось запустить асинхронное выполнение из-за высокой системной нагрузки. Откройте "Монитор системы" и проверьте выполняющиеся задачи.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=Асинхронное выполнение отменено.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=Выполнение задачи {0} прошло в границах памяти {1} и {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=Задача {0} выполнена с ид. ресурса {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=Поиск и замена начаты в локальной таблице (файловой).
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Поиск и замена завершены в локальной таблице (файловой).
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Сбой поиска и замены в локальной таблице (файловой).

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Ошибка. Обновление статистики для локальной таблицы (файловой) остановлено.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=Задача не выполнена из-за нехватки памяти в базе данных SAP HANA.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=Задача не выполнена из-за отклонения в контроле допуска SAP HANA.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=Задача не выполнена, так как слишком много активных соединений SAP HANA.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=Не удалось выполнить операцию повтора, так как повторы разрешены только для вышестоящей задачи вложенной цепочки задач.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Журналы невыполненных дочерних задач больше не доступны для повторения.


####Metrics Labels

performanceOptimized=Оптимизация производительности
memoryOptimized=Оптимизация памяти

JOB_EXECUTION=Выполнение задания
EXECUTION_MODE=Режим выполнения
NUMBER_OF_RECORDS_OVERALL=Число сохраненных записей
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Число записей, считанных из дистанционного источника
RUNTIME_MS_REMOTE_EXECUTION_TIME=Время обработки дистанционного источника
MEMORY_CONSUMPTION_GIB=Пиковая память SAP HANA
NUMBER_OF_PARTITIONS=Число разделов
MEMORY_CONSUMPTION_GIB_OVERALL=Пиковая память SAP HANA
NUMBER_OF_PARTITIONS_LOCKED=Число заблокированных разделов
PARTITIONING_COLUMN=Столбец разделения
HANA_PEAK_CPU_TIME=Общее время ЦП SAP HANA
USED_IN_DISK=Использование памяти
INPUT_PARAMETER_PARAMETER_VALUE=Параметр ввода
INPUT_PARAMETER=Параметр ввода
ECN_ID=Имя эластичного узла вычислений

DAC=Элементы контроля доступа к данным
YES=Да
NO=Нет
noofrecords=Число записей
partitionpeakmemory=Пиковая память SAP HANA
value=Значение
metricsTitle=Метрики ({0})
partitionmetricsTitle=Разделы ({0})
partitionLabel=Раздел
OthersNotNull=Значения не включены в диапазоны
OthersNull=Значения NULL
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Настройки, использованные для последнего выполнения устойчивого сохранения данных:
#XMSG: Message for input parameter name
inputParameterLabel=Параметр ввода
#XMSG: Message for input parameter value
inputParameterValueLabel=Значение
#XMSG: Message for persisted data
inputParameterPersistedLabel=Устойчиво сохранено в
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Удалить данные
REMOVE_DELETED_RECORDS=Удалить удаленные записи
MERGE_FILES=Объединить файлы
OPTIMIZE_FILES=Оптимизировать файлы
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Просмотреть в мониторе моста SAP BW

ANALYZE_PERFORMANCE=Анализировать производительность
CANCEL_ANALYZE_PERFORMANCE=Отменить анализ производительности

#XFLD: Label for frequency column
everyLabel=Кажд.
#XFLD: Plural Recurrence text for Hour
hoursLabel=ч
#XFLD: Plural Recurrence text for Day
daysLabel=дн.
#XFLD: Plural Recurrence text for Month
monthsLabel=мес.
#XFLD: Plural Recurrence text for Minutes
minutesLabel=мин

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Руководство по устранению ошибок устойчивости ракурса</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Процессу недостаточно памяти. Не удалось устойчиво сохранить данные для ракурса "{0}". Обратитесь к справочному порталу за дополнительными сведениями об ошибках недостатка памяти. Проверьте анализ ракурса на сложность потребления памяти.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Неприменимо
OPEN_BRACKET=(
CLOSE_BRACKET=)
