
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Išsami žurnalo informacija
#XFLD: Header
TASK_LOGS=Užduočių žurnalai ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Vykdymai ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Peržiūrėti išsamią informaciją
#XFLD: Button text
STOP=Stabdyti vykdymą
#XFLD: Label text
RUN_START=Paskutinio vykdymo pradžia
#XFLD: Label text
RUN_END=Paskutinio vykdymo pabaiga
#XFLD: Label text
RUNTIME=Trukmė
#XTIT: Count for Messages
txtDetailMessages=Pranešimai ({0})
#XFLD: Label text
TIME=Laikotarpis
#XFLD: Label text
MESSAGE=Pranešimas
#XFLD: Label text
TASK_STATUS=Kategorija
#XFLD: Label text
TASK_ACTIVITY=Veikla
#XFLD: Label text
RUN_START_DETAILS=Pradžia
#XFLD: Label text
RUN_END_DETAILS=Pabaiga
#XFLD: Label text
LOGS=Vykdymai
#XFLD: Label text
STATUS=Būsena
#XFLD: Label text
RUN_STATUS=Vykdymo būsena
#XFLD: Label text
Runtime=Trukmė
#XFLD: Label text
RuntimeTooltip=Trukmė (hh : mm : ss)
#XFLD: Label text
TRIGGEREDBY=Inicijavo
#XFLD: Label text
TRIGGEREDBYNew=Vykdo
#XFLD: Label text
TRIGGEREDBYNewImp=Vykdymą pradėjo
#XFLD: Label text
EXECUTIONTYPE=Vykdymo tipas
#XFLD: Label text
EXECUTIONTYPENew=Vykdymo tipas
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Pirminės grandinės vieta
#XFLD: Refresh tooltip
TEXT_REFRESH=Atnaujinti
#XFLD: view Details link
VIEW_ERROR_DETAILS=Peržiūrėti išsamią informaciją
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Atsisiųsti papildomą informaciją
#XMSG: Download completed
downloadStarted=Atsisiuntimas pradėtas
#XMSG: Error while downloading content
errorInDownload=Atsisiunčiant įvyko klaida.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Peržiūrėti išsamią informaciją
#XBTN: cancel button of task details dialog
TXT_CANCEL=Atšaukti
#XBTN: back button from task details
TXT_BACK=Atgal
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Užduotis baigta
#XFLD: Log message with failed status
MSG_LOG_FAILED=Užduotis nepavyko
#XFLD: Master and detail table with no data
No_Data=Duomenų nėra
#XFLD: Retry tooltip
TEXT_RETRY=Bandyti iš naujo
#XFLD: Cancel Run label
TEXT_CancelRun=Atšaukti vykdymą
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Nepavyko įkelti valymo
#XMSG:button copy sql statement
txtSQLStatement=Kopijuoti SQL sakinį
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Artidaryti nuotolinių užklausų stebėjimo priemonę
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=Norėdami rodyti nuotolinius SQL sakinius, spustelėkite „Atidaryti nuotolinių užklausų stebėjimo priemonę“.
#XMSG:button ok
txtOk=Gerai
#XMSG: button close
txtClose=Uždaryti
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=Pradėtas objekto „{0}“ vykdymo veiksmo atšaukimas.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=Nepavyko objekto „{0}“ vykdymo veiksmo atšaukimas.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=Atšaukti objekto „{0}“ vykdymo objekto nebegalima, nes replikavimo būsena pakeista.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Nėra užduočių žurnalų, kurių būsena Vykdoma.
#XMSG: message for conflicting task
Task_Already_Running=Objektui „{0}“ jau vykdoma prieštaringa užduotis.
#XFLD: Label for no task log with running state title
actionInfo=Veiksmo informacija
#XMSG Copied to clipboard
copiedToClip=Nukopijuota į mainų sritį
#XFLD copy
Copy=Kopijuoti
#XFLD copy correlation ID
CopyCorrelationID=Kopijuoti koreliacijos ID
#XFLD Close
Close=Uždaryti
#XFLD: show more Label
txtShowMore=Rodyti daugiau
#XFLD: message Label
messageLabel=Pranešimas:
#XFLD: details Label
detailsLabel=Išsami informacija:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Išskyrus SQL \r\n sakinį:
#XFLD:statementId Label
statementIdLabel=Sakinio ID:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Nuotolinių \r\n SQL sakinių skaičius:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Sakinių skaičius
#XFLD: Space Label
txtSpaces=Sritis
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Neautorizuotos sritys
#XFLD: Privilege Error Text
txtPrivilegeError=Neturite reikiamų teisių peržiūrėti šiuos duomenis.
#XFLD: Label for Object Header
DATA_ACCESS=Duomenų prieiga
#XFLD: Label for Object Header
SCHEDULE=Tvarkaraštis
#XFLD: Label for Object Header
DETAILS=Išsami informacija
#XFLD: Label for Object Header
LATEST_UPDATE=Naujausias naujinimas
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Paskutinis keitimas (šaltinio)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Atnaujinimo dažnumas
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Suplanuotas dažnumas
#XFLD: Label for Object Header
NEXT_RUN=Kitas vykdymas
#XFLD: Label for Object Header
CONNECTION=Ryšys
#XFLD: Label for Object Header
DP_AGENT=DP agentas
#XFLD: Label for Object Header
USED_IN_MEMORY=Saugyklai naudojama atmintis (MB)
#XFLD: Label for Object Header
USED_DISK=Saugyklai naudojamas diskas (MB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Dydis atmintyje (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Dydis diske (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Įrašų skaičius

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Nustatyti į Nepavyko
SET_TO_FAILED_ERR=Ši užduotis buvo vykdoma, bet vartotojas nustatė šios užduoties būseną į Nepavyko.
#XFLD: Label for stopped failed
FAILLOCKED=Jau vykdoma
#XFLD: sub status STOPPED
STOPPED=Sustabdyta
STOPPED_ERR=Ši užduotis buvo sustabdyta, bet atšaukimas nebuvo atliktas.
#XFLD: sub status CANCELLED
CANCELLED=Atšaukta
CANCELLED_ERR=Šis užduočių vykdymas buvo atšauktas jam prasidėjus. Šiuo atveju duomenys buvo grąžinti atgal ir atstatyti į būseną, kuri buvo prieš pradedant vykdyti užduotį.
#XFLD: sub status LOCKED
LOCKED=Užrakinta
LOCKED_ERR=Ta pati užduotis jau buvo vykdoma, todėl šios užduoties darbo negalima vykdyti lygiagrečiai su esamos užduoties vykdymu.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Užduoties vykdymas
TASK_EXCEPTION_ERR=Vykdant šią užduotį, įvyko nenurodyta klaida.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Užduoties vykdymo išimtis
TASK_EXECUTE_EXCEPTION_ERR=Vykdant šią užduotį, įvyko klaida.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Neautorizuota
UNAUTHORIZED_ERR=Nepavyko autentifikuoti vartotojo, todėl jis buvo užrakintas arba panaikintas.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Uždrausta
FORBIDDEN_ERR=Nepriskirtas vartotojas neturi teisių, kurių reikia šiai užduočiai įvykdyti.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Neinicijuota
FAIL_NOT_TRIGGERED_ERR=Šios užduoties darbo įvykdyti nepavyko dėl sistemos gedimo arba tam tikros duomenų bazės sistemos dalies, kuri nebuvo pasiekiama planuojamo vykdymo metu. Palaukite kito suplanuoto darbo vykdymo laiko arba suplanuokite užduotį iš naujo.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Tvarkaraštis atšauktas
SCHEDULE_CANCELLED_ERR=Šios užduoties darbo atlikti nepavyko dėl vidinės klaidos. Susisiekite su SAP palaikymo tarnyba ir pateikite koreliacijos ID bei laiko žymą iš šios užduoties žurnalo informacijos.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Vykdomas ankstesnis vykdymas
SUCCESS_SKIPPED_ERR=Šios užduoties vykdymas nebuvo inicijuotas, nes vis dar atliekamas ankstesnis tos pačios užduoties vykdymas.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Trūksta savininko
FAIL_OWNER_MISSING_ERR=Šios užduoties darbo įvykdyti nepavyko, nes nėra jam priskirto sistemos vartotojo. Priskirkite darbui savininką.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Nėra leidimo
FAIL_CONSENT_NOT_AVAILABLE_ERR=Neįgaliojote SAP jūsų vardu vykdyti užduočių grandinių arba planuoti duomenų integravimo užduočių. Norėdami duoti leidimą, pasirinkite pateiktą parinktį.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Baigėsi sutikimo laikas
FAIL_CONSENT_EXPIRED_ERR=Baigėsi įgaliojimas, leidžiantis SAP vykdyti užduočių grandines arba planuoti duomenų integravimo užduotis jūsų vardu. Pasirinkite pateiktą parinktį, kad atnaujintumėte savo sutikimą.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Sutikimas negalioja
FAIL_CONSENT_INVALIDATED_ERR=Šios užduoties nepavyko įvykdyti, paprastai dėl kliento tapatybės teikėjo konfigūracijos pakeitimo. Tokiu atveju paveikto vartotojo vardu negalima vykdyti ar planuoti naujų užduočių darbų. Jei priskirtas vartotojas vis dar egzistuoja naujame IDP, atšaukite planavimo sutikimą ir vėl jį suteikite. Jei priskirto vartotojo nebėra, priskirkite naują užduoties darbo savininką ir pateikite reikiamą užduoties planavimo sutikimą. Norėdami gauti daugiau informacijos, žr. šią SAP pastabą: https://launchpad.support.sap.com/#/notes/3089828.
TASK_EXECUTOR_ERROR=Užduoties vykdytojas
TASK_EXECUTOR_ERROR_ERR=Šioje užduotyje įvyko vidinė klaida, greičiausiai ruošiantis vykdyti veiksmus, todėl užduoties pradėti nepavyko.
PREREQ_NOT_MET=Prielaida neatitinka
PREREQ_NOT_MET_ERR=Šios užduoties negalima vykdyti dėl problemų jos apibrėžime. Pavyzdžiui, neįdiegtas objektas, užduočių grandinėje yra ciklinė logika arba negaliojantis rakurso SQL.
RESOURCE_LIMIT_ERROR=Išteklių apribojimo klaida
RESOURCE_LIMIT_ERROR_ERR=Šiuo metu negalima atlikti užduoties, nes neprieinama pakankamai išteklių arba jie užimti.
FAIL_CONSENT_REFUSED_BY_UMS=Sutikimas atmestas
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Šios užduoties nepavyko vykdyti suplanuotuose vykdymuose ar užduočių grandinėse, nes kliente pakeista vartotojo tapatybės teikėjo konfigūracija. Daugiau informacijos žr. SAP pastaboje: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Suplanuota
#XFLD: status text
SCHEDULEDNew=Nuolatinis
#XFLD: status text
PAUSED=Pristabdyta
#XFLD: status text
DIRECT=Tiesioginis
#XFLD: status text
MANUAL=Rankinis
#XFLD: status text
DIRECTNew=Paprastas
#XFLD: status text
COMPLETED=Užbaigta
#XFLD: status text
FAILED=Nepavyko
#XFLD: status text
RUNNING=Vykdoma
#XFLD: status text
none=Nėra
#XFLD: status text
realtime=Realiojo laiko
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Antrinė užduotis
#XFLD: New Data available in the file
NEW_DATA=Nauji duomenys

#XFLD: text for values shown in column Replication Status
txtOff=Išjungta
#XFLD: text for values shown in column Replication Status
txtInitializing=Inicijuojama
#XFLD: text for values shown in column Replication Status
txtLoading=Įkeliama
#XFLD: text for values shown in column Replication Status
txtActive=Aktyvus
#XFLD: text for values shown in column Replication Status
txtAvailable=Prieinama
#XFLD: text for values shown in column Replication Status
txtError=Klaida
#XFLD: text for values shown in column Replication Status
txtPaused=Pristabdytas
#XFLD: text for values shown in column Replication Status
txtDisconnected=Atjungta
#XFLD: text for partially Persisted views
partiallyPersisted=Iš dalies išlaikyta

#XFLD: activity text
REPLICATE=Replikuoti
#XFLD: activity text
REMOVE_REPLICATED_DATA=Pašalinti replikuotus duomenis
#XFLD: activity text
DISABLE_REALTIME=Išjungti duomenų replikavimą realiuoju laiku
#XFLD: activity text
REMOVE_PERSISTED_DATA=Pašalinti pastovius duomenis
#XFLD: activity text
PERSIST=Išlaikyti
#XFLD: activity text
EXECUTE=Vykdyti
#XFLD: activity text
TASKLOG_CLEANUP=Tacklog_Cleanup
#XFLD: activity text
CANCEL_REPLICATION=Atšaukti replikavimą
#XFLD: activity text
MODEL_IMPORT=Modelio importavimas
#XFLD: activity text
NONE=Nėra
#XFLD: activity text
CANCEL_PERSISTENCY=Atšaukti išsaugojimą
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Rakurso analizė
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Atšaukti rakursų analizės priemonę

#XFLD: severity text
INFORMATION=Informacija
#XFLD: severity text
SUCCESS=Pavyko
#XFLD: severity text
WARNING=Įspėjimas
#XFLD: severity text
ERROR=Klaida
#XFLD: text for values shown for Ascending sort order
SortInAsc=Rūšiuoti didėjimo tvarka
#XFLD: text for values shown for Descending sort order
SortInDesc=Rūšiuoti mažėjimo tvarka
#XFLD: filter text for task log columns
Filter=Filtruoti
#XFLD: object text for task log columns
Object=Objektas
#XFLD: space text for task log columns
crossSpace=Sritis

#XBUT: label for remote data access
REMOTE=Nuotolinė
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Replikuota (realiojo laiko)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Replikuota (momentinė kopija)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=Realiojo laiko replikavimas blokuojamas dėl klaidos. Kai klaida bus ištaisyta, galite naudoti veiksmą „Bandyti dar kartą“, kad galėtumėte tęsti realiojo laiko replikavimą.
ERROR_MSG=Realiojo laiko replikavimas blokuojamas dėl klaidos.
RETRY_FAILED_ERROR=Pakartotinio bandymo procesas nepavyko dėl klaidos.
LOG_INFO_DETAILS=Replikuojant duomenis realiojo laiko režimu, nesugeneruojama jokių žurnalų. Rodomi žurnalai yra susiję su ankstesniais veiksmais.

#XBUT: Partitioning label
partitionMenuText=Skaidiniai
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Kurti skaidinį
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Redaguoti skaidinį
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Naikinti skaidinį
#XFLD: Initial text
InitialPartitionText=Norėdami išskaidyti dideles duomenų aibes į mažesnes, apibrėžkite skaidinius, nurodydami kriterijus.
DefinePartition=Apibrėžti skaidinius
#XFLD: Message text
partitionChangedInfo=Po paskutinio replikavimo buvo pakeistas skaidinio apibrėžimas. Pakeitimai bus pritaikyti kitą kartą įkeliant duomenis.
#XFLD: Message text
REAL_TIME_WARNING=Skaidymas taikomas tik įkeliant naują momentinę kopiją. Jis netaikomas reikalavimui realiuoju laiku.
#XFLD: Message text
loadSelectedPartitions=Pradėta išsaugoti pasirinktų „{0}“ skaidinių duomenis
#XFLD: Message text
loadSelectedPartitionsError=Nepavyko išsaugoti pasirinktų „{0}“ skaidinių duomenų
#XFLD: Message text
viewpartitionChangedInfo=Po paskutinio pastovumo vykdymo pasikeitė skaidinio apibrėžimas. Siekiant taikyti keitimus, kitas duomenų įkėlimas bus visa momentinė kopija, įskaitant užrakintus skaidinius. Užbaigus visą įkėlimą, galėsite vykdyti atskirų skaidinių duomenis.
#XFLD: Message text
viewpartitionChangedInfoLocked=Po paskutinio pastovumo vykdymo pasikeitė skaidinio apibrėžimas. Norint pritaikyti keitimus, kito duomenų įkėlimo metu bus įkelta visa momentinė kopija, išskyrus užrakintus ir nepakeistus skaidinių intervalus. Kai šis įkėlimas bus baigtas, vėl galėsite įkelti pasirinktus skaidinius.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Lentelės replikavimas
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Tvarkaraščio replikavimas
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Kurti tvarkaraštį
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Redaguoti tvarkaraštį
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Naikinti tvarkaraštį
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Įkelti naują momentinę kopiją
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Pradėti duomenų replikavimą
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Pašalinti replikuotus duomenis
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Įgalinti realiojo laiko prieigą
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Įgalinti laiko duomenų replikavimą
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Išjungti duomenų replikavimą realiuoju laiku
#XFLD: Message for replicate table action
replicateTableText=Lentelės replikavimas
#XFLD: Message for replicate table action
replicateTableTextNew=Duomenų replikavimas
#XFLD: Message to schedule task
scheduleText=Tvarkaraštis
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Rakurso išlaikymas
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Duomenų išlaikymas
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Įkelti naują momentinę kopiją
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Pradėti duomenų išlaikymą
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Pašalinti pastovius duomenis
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Išskaidytas apdorojimas
#XBUT: Label for scheduled replication
scheduledTxt=Suplanuota
#XBUT: Label for statistics button
statisticsTxt=Statistika
#XBUT: Label for create statistics
createStatsTxt=Kurti statistiką
#XBUT: Label for edit statistics
editStatsTxt=Redaguoti statistiką
#XBUT: Label for refresh statistics
refreshStatsTxt=Atnaujinti statistiką
#XBUT: Label for delete statistics
dropStatsTxt=Naikinti statistiką
#XMSG: Create statistics success message
statsSuccessTxt=Pradėta kurti {0} tipo statistika, skirta {1}.
#XMSG: Edit statistics success message
statsEditSuccessTxt=Pradėtas keisti statistikos tipas į {0}, skirtą {1}.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Pradėta naujinti {0} statistika.
#XMSG: Drop statistics success message
statsDropSuccessTxt={0} statistika sėkmingai panaikinta
#XMSG: Create statistics error message
statsCreateErrorTxt=Kuriant statistiką įvyko klaida
#XMSG: Edit statistics error message
statsEditErrorTxt=Keičiant statistiką įvyko klaida
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Atnaujinant statistiką įvyko klaida
#XMSG: Drop statistics error message
statsDropErrorTxt=Naikinant statistiką įvyko klaida
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Ar tikrai norite pašalinti duomenų statistiką?
startPersistencyAdvisorLabel=Paleisti peržiūros analizės priemonę

#Partition related texts
#XFLD: Label for Column
column=Stulpelis
#XFLD: Label for No of Partition
noOfPartitions=Skaidinių sk.
#XFLD: Label for Column
noOfParallelProcess=Lygiagrečių apdorojimų sk.
#XFLD: Label text
noOfLockedPartition=Užrakintų skaidinių sk.
#XFLD: Label for Partition
PARTITION=Skaidiniai
#XFLD: Label for Column
AVAILABLE=Prieinama
#XFLD: Statistics Label
statsLabel=Statistika
#XFLD: Label text
COLUMN=Stulpelis:
#XFLD: Label text
PARALLEL_PROCESSES=Lygiagretūs procesai:
#XFLD: Label text
Partition_Range=Skaidymo diapazonas
#XFLD: Label text
Name=Pavadinimas
#XFLD: Label text
Locked=Užrakinta
#XFLD: Label text
Others=KITI
#XFLD: Label text
Delete=Naikinti
#XFLD: Label text
LoadData=Įkelti pasirinktus skaidinius
#XFLD: Label text
LoadSelectedData=Įkelti pasirinktus skaidinius
#XFLD: Confirmation text
LoadNewPersistenceConfirm=Šiuo veiksmu bus įkelta nauja visų atrakintų ir pakeistų skaidinių, o ne tik tų, kuriuos pasirinkote, momentinė kopija. Ar norite tęsti?
#XFLD: Label text
Continue=Tęsti

#XFLD: Label text
PARTITIONS=Skaidiniai
#XFLD: Label text
ADD_PARTITIONS=+ pridėti skaidinį
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Pridėti skaidinį
#XFLD: Label text
deleteRange=Naikinti skaidinį
#XFLD: Label text
LOW_PLACE_HOLDER=Įvesti žemą reikšmę
#XFLD: Label text
HIGH_PLACE_HOLDER=Įvesti aukštą reikšmę
#XFLD: tooltip text
lockedTooltip=Užrakinti skaidinį po pradinio įkėlimo

#XFLD: Button text
Edit=Redaguoti
#XFLD: Button text
CANCEL=Atšaukti

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Paskutinis statistikos atnaujinimas
#XFLD: Statistics Fields
STATISTICS=Statistika

#XFLD:Retry label
TEXT_Retry=Bandyti iš naujo
#XFLD:Retry label
TEXT_Retry_tooltip=Bandykite realiojo laiko replikavimą iš naujo po klaidos ištaisymo.
#XFLD: text retry
Retry=Patvirtinti
#XMG: Retry confirmation text
retryConfirmationTxt=Paskutinis realiojo laiko replikavimas baigtas dėl klaidos.\n Patvirtinkite, kad klaida buvo ištaisyta ir kad replikavimą galima pradėti iš naujo.
#XMG: Retry success text
retrySuccess=Pakartojimas inicijuotas sėkmingai.
#XMG: Retry fail text
retryFail=Pakartojimas nepavyko.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Kurti statistiką
#XMSG: activity message for edit statistics
DROP_STATISTICS=Naikinti statistiką
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Atnaujinti statistiką
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Redaguoti statistiką
#XMSG: Task log message started task
taskStarted=Užduotis {0} prasidėjo.
#XMSG: Task log message for finished task
taskFinished=Užduotis {0} pasibaigė su būsena {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=Užduotis {0} pasibaigė {2} ir jos būsena yra {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=Užduotyje {0} yra įvesties parametrų.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=Užduotis {0} pasibaigė su nenumatyta klaida. Užduoties būsena nustatyta į {1}.
#XMSG: Task log message for failed task
failedToEnd=Nepavyko nustatyti būsenos į {0} arba pašalinti užrakinimo.
#XMSG: Task log message
lockNotFound=Negalima užbaigti proceso, nes trūksta užrakinimo: užduotis galėjo būti atšaukta.
#XMSG: Task log message failed task
failedOverwrite=Užduotį {0} jau užrakino {1}, todėl ji nepavyko su šia klaida: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Šios užduoties užrakinimą perėmė kita užduotis.
#XMSG: Task log message failed takeover
failedTakeover=Nepavyko perimti esamos užduoties.
#XMSG: Task log message successful takeover
successTakeover=Likusį užrakinimą reikėjo panaikinti. Nustatytas naujas šios užduoties užrakinimas.
#XMSG: Tasklog Dialog Details
txtDetails=Nuotoliniai sakiniai, apdoroti vykdymo metu, gali būti rodomi atidarant nuotolinių užklausų stebėjimo priemonę skaidinio pranešimų išsamioje informacijoje.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=Nuotoliniai SQL sakiniai panaikinti iš duomenų bazės ir jų negalima rodyti.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Nuotolinių užklausų, kurios turi ryšių, priskirtų kitoms sritims, negalima rodyti. Eikite į „Nuotolinių užklausų stebėjimo priemonė“ ir naudokite sakinio ID, norėdami jas filtruoti.
#XMSG: Task log message for parallel check error
parallelCheckError=Užduoties negalima apdoroti, nes vykdoma kita, jau blokuojanti šią užduotį.
#XMSG: Task log message for parallel running task
parallelTaskRunning=Jau vykdoma konfliktinė užduotis.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Būsena {0} vykdymo metu su koreliacijos ID {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=Nepriskirtas vartotojas neturi teisių, kurių reikia šiai užduočiai įvykdyti.

#XBUT: Label for open in Editor
openInEditor=Atidaryti rengyklėje
#XBUT: Label for open in Editor
openInEditorNew=Atidaryti duomenų daryklėje
#XFLD:Run deails label
runDetails=Vykdymo išsami informacija
#XFLD: Label for Logs
Logs=Žurnalai
#XFLD: Label for Settings
Settings=Parametrai
#XFLD: Label for Save button
Save=Įrašyti
#XFLD: Label for Standard
Standard_PO=Optimizuota siekiant našumo (rekomenduojama)
#XFLD: Label for Hana low memory processing
HLMP_MO=Optimizuota atmintis
#XFLD: Label for execution mode
ExecutionMode=Vykdymo režimas
#XFLD: Label for job execution
jobExecution=Apdorojimo režimas
#XFLD: Label for Synchronous
syncExec=Sinchroninis
#XFLD: Label for Asynchronous
asyncExec=Nesinchroninis
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Naudoti numatytąjį (nesinchroninis; ateityje gali keistis)
#XMSG: Save settings success
saveSettingsSuccess=SAP HANA vykdymo režimas pakeistas.
#XMSG: Save settings failure
saveSettingsFailed=SAP HANA vykdymo režimo pakeisti nepavyko.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Užduoties vykdymas pakeistas.
#XMSG: Job Execution change failed
jobExecSettingFailed=Užduoties vykdymo pakeisti nepavyko.
#XMSG: Text for Type
typeTxt=Tipas
#XMSG: Text for Monitor
monitorTxt=Stebėjimo priemonė
#XMSG: Text for activity
activityTxt=Veikla
#XMSG: Text for metrics
metricsTxt=Metrikos
#XTXT: Text for Task chain key
TASK_CHAINS=Užduočių grandinė
#XTXT: Text for View Key
VIEWS=Rakursas
#XTXT: Text for remote table key
REMOTE_TABLES=Nuotolinė lentelė
#XTXT: Text for Space key
SPACE=Sritis
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Elastinių skaičiavimų mazgas
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Replikavimo srautas
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Išmanioji peržvalga
#XTXT: Text for Local Table
LOCAL_TABLE=Vietinė lentelė
#XTXT: Text for Data flow key
DATA_FLOWS=Duomenų srautas
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=SQL scenarijaus procedūra
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=BW proceso grandinė
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Rakursas stebėjimo priemonėje
#XTXT: Task List header text
taskListHeader=Užduočių sąrašas ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Duomenų srauto istorinių vykdymų metrikos nepavyksta iškviesti.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=Išsami vykdymo informacija nėra įkeliama šiuo metu. Pabandykite atnaujinti.
#XFLD: Label text for the Metrices table header
metricesColLabel=Operatoriaus etiketė
#XFLD: Label text for the Metrices table header
metricesType=Tipas
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Įrašų skaičius
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Vykdyti užduočių grandinę
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Užduočių grandinės vykdymas pradėtas.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Užduočių grandinės vykdymas pradėtas {0}.
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Nepavyko vykdyti užduočių grandinės.
#XTXT: Execute button label
runLabel=Vykdyti
#XTXT: Execute button label
runLabelNew=Pradėti vykdymą
#XMSG: Filter Object header
chainsFilteredTableHeader=Filtruota pagal objektą: {0}
#XFLD: Parent task chain label
parentChainLabel=Pirminių užduočių grandinė:
#XFLD: Parent task chain unauthorized
Unauthorized=Įgaliojimo peržiūrėti nėra
#XFLD: Parent task chain label
parentTaskLabel=Pirminė užduotis:
#XTXT: Task status
NOT_TRIGGERED=Neinicijuota
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Įjungti rodymą per visą ekraną
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Išjungti rodymą per visą ekraną
#XTXT: Close Task log details right panel
closeRightColumn=Uždaryti sekciją
#XTXT: Sort Text
sortTxt=Rūšiuoti
#XTXT: Filter Text
filterTxt=Filtruoti
#XTXT: Filter by text to show list of filters applied
filterByTxt=Filtruoti pagal
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Daugiau nei 5 minutės
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Daugiau nei 15 minučių
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Daugiau nei 1 valanda
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Pastarąją valandą
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Pastarosios 24 valandos
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Pastarasis mėnuo
#XTXT: Messages title text
messagesText=Pranešimai

#XTXT Statistics information message
statisticsInfo=Neįmanoma sukurti statistikos nuotolinėms lentelėms, kurių duomenų prieiga yra „Replikuota“. Norėdami sukurti statistiką, pašalinkite pasikartojančius duomenis nuotolinių lentelių informacijos stebėjimo priemonėje.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Eiti į nuotolinės lentelės informaicjos stebėjimo priemonę

#XTXT: Repair latest failed run label
retryRunLabel=Kartoti naujausią vykdymą
#XTXT: Repair failed run label
retryRun=Kartoti vykdymą
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Užduočių grandinės kartojimo vykdymas pradėtas
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Užduočių grandinės kartojimo vykdymas nepavyko
#XMSG: Task chain child elements name
taskChainRetryChildObject=Užduotis {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Nauja užduotis
#XFLD Analyzed View
analyzedView=Analizuotas rakursas
#XFLD Metrics
Metrics=Metrika
#XFLD Partition Metrics
PartitionMetrics=Skaidinio metrika
#XFLD Entities
Messages=Užduočių žurnalas
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Kol kas jokie skaidiniai neapibrėžti.
#XTXT: Description message for empty partition data
partitionEmptyDescText=Kurkite skaidinius nurodydami kriterijus, pagal kuriuos dideli duomenų kiekiai skaidomi į mažesnes, geriau valdomas dalis.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Dar nepasiekiami žurnalai
#XTXT: Description message for empty runs data
runsEmptyDescText=Kai pradėsite naują veiklą (įkelsite naują momentinę kopiją, pradėsite rodinio analizatorių...), čia matysite susijusius žurnalus.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=Elastinių skaičiavimų mazgo {0} konfigūracija tvarkoma netinkamai. Patikrinkite savo apibrėžimą.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Elastinių skaičiavimų mazgo {0} pridėjimo procesas nepavyko.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Šaltinio objekto ''{0}''.''{1}'' replikos kūrimas ir aktyvinimas elastiniame skaičiavimo mazge {2} pradėtas.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Šaltinio objekto ''{0}''.''{1}''‘ replikos šalinimas iš elastinio skaičiavimo mazgo {2} pradėtas.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Šaltinio objekto ''{0}''.''{1}''‘ replikos kūrimas ir aktyvinimas nepavyko.
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Šaltinio objekto ''{0}''.''{1}''‘ replikos šalinimas nepavyko.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Pradėtas srities {0} analitinių užklausų skaičiavimo maršrutizavimas į elastinių skaičiavimų mazgą {1}.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Srities {0} analitinių užklausų skaičiavimo maršrutizavimas į atitinkamą elastinių skaičiavimų mazgą nepavyko.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Pradėtas srities {0} analitinių užklausų skaičiavimo krypties pakeitimas atgal iš elastinių skaičiavimų mazgo {1}.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Srities {0} analitinių užklausų skaičiavimo krypties pakeitimas atgal į koordinatorių nepavyko.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Inicijuota užduočių grandinė {0} į atitinkamą elastinių skaičiavimų mazgą {1}.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Elastinio skaičiavimų mazgo {0} užduočių grandinės sugeneruoti nepavyko.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=Elastinio skaičiavimo mazgo {0} teikimas pradėtas naudojant nurodytą dydžio nustatymo planą: vCPU: {1}, atmintis (GiB): {2}, saugyklos dydis (GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=Elastinio skaičiavimo mazgas {0} jau suteiktas.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=Elastinio skaičiavimo mazgas {0} suteikimas jau pašalintas.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=Operacija neleidžiama. Sustabdykite elastinį skaičiavimo mazgą {0} ir paleiskite iš naujo, kad atnaujintumėte dydžio nustatymo planą.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Elastinio skaičiavimo mazgo {0} šalinimo procesas nepavyko.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=Elastinio skaičiavimo mazgo {0} dabartinio vykdymo skirtasis laikas baigėsi.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=Vykdoma elastinio skaičiavimo mazgo {0} būsenos patikra...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=Elastinio skaičiavimo mazgo {0} užduočių grandinės generavimas užrakintas, todėl grandinė {1} gali būti vis dar vykdoma.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=Šaltinio objektas ''{0}''.''{1}''‘ replikuotas kaip rakurso ''{2}''.''{3}''‘ priklausomybė.
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Negalima pakartoti lentelės „{0}“. „{1}“, nes eilučių lentelės kopijavimas nebenaudojamas.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Viršytas „SAP HANA Cloud“ egzemplioriaus maksimalus elastinių skaičiavimų mazgas.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=Elastinio skaičiavimo mazgo {0} vykdoma operacija tebevyksta.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Dėl techninių problemų šaltinio lentelės {0} kopijos trynimas buvo sustabdytas. Pabandykite dar kartą vėliau.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=Dėl techninių problemų šaltinio lentelės {0} kopijos kūrimas buvo sustabdytas. Pabandykite dar kartą vėliau.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Negalima pradėti elastinių skaičiavimų mazgo, nes buvo pasiektas naudojimo limitas arba dar nebuvo priskirtas skaičiavimo blokavimo laikas.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Įkeliama užduočių grandinė ir ruošiamasi vykdyti iš viso {0} užd., kurios priklauso šiai grandinei.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Jau vykdoma konfliktinė užduotis
#XMSG: Replication will change
txt_replication_change=Replikavimo tipas bus pakeistas.
txt_repl_viewdetails=Peržiūrėti išsamią informaciją

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Panašu, kad paskutinį kartą bandant iš naujo įvyko klaida, nes ankstesnė užduotis nepavyko sugeneravus plano.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=Sritis „{0}“ užrakinta.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=Vykdant veiklą {0} reikia įdiegti vietinę lentelę {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=Vykdant veiklą {0} reikia, kad „Delta Capture“ būtų įjungta vietinei lentelei {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=Vykdant veiklą {0} reikia vietinės lentelės {1} duomenims objekto saugykloje saugoti.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=Vykdant veiklą {0} reikia vietinės lentelės {1} duomenims saugoti duomenų bazėje.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Pradedamas panaikintų įrašų šalinimas iš vietinės lentelės {0}.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Pradedamas visų įrašų naikinimas iš vietinės lentelės {0}.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Pradedamas įrašų naikinimas iš vietinės lentelės {0} pagal filtro sąlygą {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Šalinant panaikintus vietinės lentelės {0} įrašus įvyko klaida.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Naikinant visus vietinės lentelės {0} įrašus įvyko klaida.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Naikinami visi visiškai apdoroti įrašai su keitimo tipu „Panaikinta“, senesni nei {0} d.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Naikinami visi visiškai apdoroti įrašai su keitimo tipu „Panaikinta“, senesni nei {0}.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS=Panaikinta įrašų: {0}.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS=Naikinimui pažymėta įrašų: {0}.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=Panaikintų vietinės lentelės {0} įrašų šalinimas baigtas.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Visų vietinės lentelės {0} įrašų naikinimas baigtas.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Vietinės lentelės {0} įrašus pradedama žymėti kaip „Panaikinta“.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Pradedamas įrašų žymėjimas kaip „Panaikinta“ vietinėje lentelėje {0} pagal filtro sąlygą {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Vietinės lentelės {0} įrašai pažymėti kaip „Panaikinta“.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Duomenų pakeitimai laikinai įkeliami į lentelę {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Duomenų pakeitimai laikinai įkelti į lentelę {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Duomenų pakeitimai apdoroti ir panaikinti iš lentelės {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Ryšio išsami informacija.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Pradedama optimizuoti vietinė lentelė (failas).
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Optimizuojant vietinę lentelę (failą) įvyko klaida.
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Įvyko klaida. Vietinės lentelės (failo) optimizavimas sustabdytas.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Optimizuojama vietinė lentelė (failas)...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=Vietinė lentelė (failas) buvo optimizuota.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=Vietinė lentelė (failas) optimizuota.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=Lentelė yra optimizuota su „Z-Order“ stulpeliais: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Įvyko klaida. Vietinės lentelės (failo) trumpinimas sustabdytas.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Trumpinama vietinė lentelė (failas)...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=Vietinės lentelės (Failo) gaunamo buferio vieta buvo panaikinta.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Pradedama valyti (naikinti visus visiškai apdorotus įrašus) vietinę lentelę (failą).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Valant vietinę lentelę (failą) įvyko klaida.
#XMSG: Task log message
LTF_VACUUM_STOPPED=Įvyko klaida. Vietinės lentelės (failo) valymas sustabdytas.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Valoma vietinė lentelė (failas)...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=Valymas baigtas.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Naikinami visi visiškai apdoroti įrašai, senesni nei {0} d.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Naikinami visi visiškai apdoroti įrašai, senesni nei {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Pradedama sulieti naujus įrašus su vietine lentele (failu).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Pradedama sulieti naujus įrašus su vietine lentele (failu) naudojant parametrą „Sulieti duomenis automatiškai“.
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Pradedama sulieti naujus įrašus su vietine lentele (failu). Ši užduotis inicijuota naudojant API suliejimo užklausą.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Nauji įrašai suliejami su vietine lentele (failu).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Suliejant naujus įrašus su vietine lentele (failu) įvyko klaida.
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=Vietinė lentelė (failas) sulieta.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Įvyko klaida. Vietinės lentelės (failo) suliejimas sustabdytas.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=Vietinės lentelės (failo) nepavyko sujungti dėl klaidos, bet operacija buvo dalinai sėkminga, o kai kurie duomenys jau buvo sujungti.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Įvyko skirtojo laiko klaida. Veikla {0} vykdyta {1} val.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=Nesinchroninis vykdymas negalėjo būti pradėtas dėl didelės sistemos apkrovos. Atidarykite skiltį „Sistemos stebėjimas“ ir patikrinkite vykdomas užduotis.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=Nesinchroninis vykdymas atšauktas.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION={0} užduotis vykdyta {1}–{2} atminties ribose.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID={0} užduotis vykdyta su ištekliaus ID {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=Vietinėje lentelėje (faile) pradėta paieška ir keitimas.
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Vietinėje lentelėje (faile) užbaigta paieška ir keitimas.
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Vietinėje lentelėje (faile) nepavyko atlikti paieškos ir keitimo.

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Įvyko klaida. Vietinės lentelės (failo) statistikos naujinimas sustabdytas.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=Užduotis nepavyko dėl atminties trūkumo SAP HANA duomenų bazėje klaidos.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=Užduotis nepavyko dėl SAP HANA leidimų kontrolės atmetimo.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=Užduotis nepavyko dėl per didelio skaičiaus aktyvių SAP HANA jungčių.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=Operacijos „Bandyti iš naujo“ atlikti negalima, nes bandymas iš naujo leidžiamas tik pirminėje įdėtojoje užduočių grandinėje.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Nepavykusių antrinių užduočių žurnalai nebepasiekiami, kad būtų galima tęsti kartojimą.


####Metrics Labels

performanceOptimized=Optimizuota siekiant našumo
memoryOptimized=Optimizuota atmintis

JOB_EXECUTION=Užduoties vykdymas
EXECUTION_MODE=Vykdymo režimas
NUMBER_OF_RECORDS_OVERALL=Pastovių įrašų skaičius
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Iš nuotolinio šaltinio nuskaitytų įrašų skaičius
RUNTIME_MS_REMOTE_EXECUTION_TIME=Nuotolinio šaltinio apdorojimo laikas
MEMORY_CONSUMPTION_GIB=SAP HANA daugiausiai sunaudota atminties
NUMBER_OF_PARTITIONS=Skaidinių skaičius
MEMORY_CONSUMPTION_GIB_OVERALL=SAP HANA daugiausiai sunaudota atminties
NUMBER_OF_PARTITIONS_LOCKED=Užrakintų skaidinių skaičius
PARTITIONING_COLUMN=Skaidinių stulpelis
HANA_PEAK_CPU_TIME=SAP HANA visas CPU laikas
USED_IN_DISK=Panaudota saugykla
INPUT_PARAMETER_PARAMETER_VALUE=Įvesties parametras
INPUT_PARAMETER=Įvesties parametras
ECN_ID=Elastinių skaičiavimų mazgo pavadinimas

DAC=Duomenų prieigos valdikliai
YES=Taip
NO=Ne
noofrecords=Įrašų skaičius
partitionpeakmemory=SAP HANA daugiausiai sunaudota atminties
value=Reikšmė
metricsTitle=Metrika ({0})
partitionmetricsTitle=Skaidiniai {0}
partitionLabel=Skaidinys
OthersNotNull=Į intervalus neįtrauktos reikšmės
OthersNull=Tuščios reikšmės
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Parametrai, naudoti paskutiniam duomenų pastovumui vykdyti:
#XMSG: Message for input parameter name
inputParameterLabel=Įvesties parametras
#XMSG: Message for input parameter value
inputParameterValueLabel=Reikšmė
#XMSG: Message for persisted data
inputParameterPersistedLabel=Išlaikyta
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Naikinti duomenis
REMOVE_DELETED_RECORDS=Pašalinti panaikintus įrašus
MERGE_FILES=Sulieti failus
OPTIMIZE_FILES=Optimizuoti failus
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Peržiūrėti SAP BW tilto monitoriuje

ANALYZE_PERFORMANCE=Analizuoti našumą
CANCEL_ANALYZE_PERFORMANCE=Atšaukti našumo analizavimą

#XFLD: Label for frequency column
everyLabel=Kas
#XFLD: Plural Recurrence text for Hour
hoursLabel=Valandos
#XFLD: Plural Recurrence text for Day
daysLabel=Dienos
#XFLD: Plural Recurrence text for Month
monthsLabel=Mėnesiai
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutės

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Peržiūrėti pastovumo trikčių šalinimo vadovą</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Procesui trūksta atminties. Nepavyko išlaikyti duomenų rakursui „{0}“. Daugiau informacijos apie atminties trūkumo klaidas ieškokite žinyno portale. Apsvarstykite galimybę peržiūrėti analizatorių, kad išanalizuotumėte atminties sunaudojimo kompleksiškumo rakursą.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Netaikoma
OPEN_BRACKET=(
CLOSE_BRACKET=)
