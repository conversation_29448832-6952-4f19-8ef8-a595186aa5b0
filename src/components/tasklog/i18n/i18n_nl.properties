
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Logdetails
#XFLD: Header
TASK_LOGS=Takenlogs ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Runs ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Details weergeven
#XFLD: Button text
STOP=Run stoppen
#XFLD: Label text
RUN_START=Start laatste run
#XFLD: Label text
RUN_END=Einde laatste run
#XFLD: Label text
RUNTIME=Duur
#XTIT: Count for Messages
txtDetailMessages=Meldingen ({0})
#XFLD: Label text
TIME=Timestamp
#XFLD: Label text
MESSAGE=Melding
#XFLD: Label text
TASK_STATUS=Categorie
#XFLD: Label text
TASK_ACTIVITY=Activiteit
#XFLD: Label text
RUN_START_DETAILS=Start
#XFLD: Label text
RUN_END_DETAILS=Einde
#XFLD: Label text
LOGS=Runs
#XFLD: Label text
STATUS=Status
#XFLD: Label text
RUN_STATUS=Runstatus
#XFLD: Label text
Runtime=Duur
#XFLD: Label text
RuntimeTooltip=Duur (uu : mm : ss)
#XFLD: Label text
TRIGGEREDBY=Gestart door
#XFLD: Label text
TRIGGEREDBYNew=Uitgevoerd door
#XFLD: Label text
TRIGGEREDBYNewImp=Run begonnen door
#XFLD: Label text
EXECUTIONTYPE=Type uitvoering
#XFLD: Label text
EXECUTIONTYPENew=Runtype
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Ruimte bovenliggende keten
#XFLD: Refresh tooltip
TEXT_REFRESH=Vernieuwen
#XFLD: view Details link
VIEW_ERROR_DETAILS=Details weergeven
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Aanvullende details downloaden
#XMSG: Download completed
downloadStarted=Download is gestart
#XMSG: Error while downloading content
errorInDownload=Er is een fout opgetreden tijdens het downloaden.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Details weergeven
#XBTN: cancel button of task details dialog
TXT_CANCEL=Annuleren
#XBTN: back button from task details
TXT_BACK=Terug
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Taak voltooid
#XFLD: Log message with failed status
MSG_LOG_FAILED=Taak mislukt
#XFLD: Master and detail table with no data
No_Data=Geen gegevens
#XFLD: Retry tooltip
TEXT_RETRY=Opnieuw proberen
#XFLD: Cancel Run label
TEXT_CancelRun=Run annuleren
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Mislukt laadproces opschonen
#XMSG:button copy sql statement
txtSQLStatement=SQL-instructie kopiëren
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Monitor remote query's openen
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=Klik op 'Monitor remote query's openen' om de remote SQL-instructies weer te geven.
#XMSG:button ok
txtOk=OK
#XMSG: button close
txtClose=Sluiten
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=Annulering van runactie voor object ''{0}'' is gestart.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=Annulering van runactie voor object ''{0}'' is mislukt.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=Annulering van runactie voor object ''{0}'' is niet meer mogelijk omdat de replicatiestatus is gewijzigd.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Er zijn geen taaklogs met de status "Wordt uitgevoerd".
#XMSG: message for conflicting task
Task_Already_Running=Er wordt al een conflicterende taak uitgevoerd voor object "{0}".
#XFLD: Label for no task log with running state title
actionInfo=Actie-informatie
#XMSG Copied to clipboard
copiedToClip=Gekopieerd naar klembord
#XFLD copy
Copy=Kopiëren
#XFLD copy correlation ID
CopyCorrelationID=Correlatie-ID kopiëren
#XFLD Close
Close=Sluiten
#XFLD: show more Label
txtShowMore=Meer weergeven
#XFLD: message Label
messageLabel=Bericht:
#XFLD: details Label
detailsLabel=Details:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=SQL-instructie wordt \r\n uitgevoerd:
#XFLD:statementId Label
statementIdLabel=Instructie-ID:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Aantal remote \r\n SQL-instructies:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Aantal statements
#XFLD: Space Label
txtSpaces=Ruimte
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Niet-bevoegde ruimten
#XFLD: Privilege Error Text
txtPrivilegeError=U hebt onvoldoende bevoegdheden om deze gegevens weer te geven.
#XFLD: Label for Object Header
DATA_ACCESS=Toegang tot gegevens
#XFLD: Label for Object Header
SCHEDULE=Plannen
#XFLD: Label for Object Header
DETAILS=Details
#XFLD: Label for Object Header
LATEST_UPDATE=Laatste update
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Laatste wijziging (bron)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Vernieuwingsfrequentie
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Geplande frequentie
#XFLD: Label for Object Header
NEXT_RUN=Volgende run
#XFLD: Label for Object Header
CONNECTION=Verbinding
#XFLD: Label for Object Header
DP_AGENT=Middel voor datalevering
#XFLD: Label for Object Header
USED_IN_MEMORY=Geheugen gebruikt voor opslag (MiB)
#XFLD: Label for Object Header
USED_DISK=Schijfruimte gebruikt voor opslag (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Grootte in-memory (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Grootte op schijf (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Aantal records

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Op mislukt zetten
SET_TO_FAILED_ERR=Deze taak werd uitgevoerd maar de gebruiker heeft de status van deze taak op MISLUKT gezet
#XFLD: Label for stopped failed
FAILLOCKED=Run wordt al uitgevoerd
#XFLD: sub status STOPPED
STOPPED=Gestopt
STOPPED_ERR=Deze taak is gestopt maar er is geen rollback uitgevoerd.
#XFLD: sub status CANCELLED
CANCELLED=Geannuleerd
CANCELLED_ERR=Deze taakuitvoering is geannuleerd nadat deze was gestart. In dit geval is er een rollback van de gegevens uitgevoerd en zijn deze hersteld naar de status van voordat de taakuitvoering in eerste instantie werd geactiveerd.
#XFLD: sub status LOCKED
LOCKED=Geblokkeerd
LOCKED_ERR=Dezelfde taak wordt al uitgevoerd. Daarom kan deze taak niet in parallel met een bestaande taakuitvoering worden uitgevoerd.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Taakuitzondering
TASK_EXCEPTION_ERR=Er is tijdens de uitvoering van deze taak een niet-gespecificeerde fout opgetreden.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Uitzondering taakuitvoering
TASK_EXECUTE_EXCEPTION_ERR=In deze taak is een fout opgetreden tijdens de uitvoering.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Onbevoegd
UNAUTHORIZED_ERR=De gebruiker kon niet worden geverifieerd, is geblokkeerd of verwijderd.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Verboden
FORBIDDEN_ERR=De toegewezen gebruiker heeft geen bevoegdheden die nodig zijn om deze taak uit te voeren.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Niet geactiveerd
FAIL_NOT_TRIGGERED_ERR=Deze taakjob kon niet worden uitgevoerd vanwege een systeemuitval of omdat een deel van het databasesysteem niet beschikbaar is op het tijdstip van de geplande uitvoering. Wacht op de uitvoeringstijd van de volgende geplande job of plan de job opnieuw.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Planning geannuleerd
SCHEDULE_CANCELLED_ERR=Deze taakjob kon niet worden uitgevoerd vanwege een interne fout. Neem contact op met SAP Support en geef de correlatie-ID en timestamp van de logdetailgegevens van de taakjob op.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Vorige run wordt nog uitgevoerd
SUCCESS_SKIPPED_ERR=De uitvoering van deze taak is niet geactiveerd omdat een vorige uitvoering van dezelfde taak nog wordt uitgevoerd.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Eigenaar ontbreekt
FAIL_OWNER_MISSING_ERR=Deze taakjob kon niet worden uitgevoerd omdat deze geen toegewezen systeemgebruiker heeft. Wijs een eigenaar/gebruiker aan de job toe.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Toestemming niet beschikbaar
FAIL_CONSENT_NOT_AVAILABLE_ERR=U hebt SAP geen toestemming verleend om namens u taakketens uit te voeren of gegevensintegratietaken te plannen. Selecteer de optie om uw toestemming te verlenen.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Toestemming verlopen
FAIL_CONSENT_EXPIRED_ERR=De bevoegdheid die SAP toestaat om namens u taakketens uit te voeren of gegevensintegratietaken te plannen, is verlopen. Selecteer de optie om uw toestemming te vernieuwen.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Toestemming ongeldig gemaakt
FAIL_CONSENT_INVALIDATED_ERR=Deze taak kon niet worden uitgevoerd vanwege een wijziging in de identityproviderconfiguratie van de tenant. In dat geval kunnen er geen nieuwe taakjobs worden uitgevoerd of gepland in naam van de betrokken gebruiker. Als de toegewezen gebruiker nog bestaat in de nieuwe IdP, moet u de planningstoestemming intrekken en vervolgens opnieuw verlenen. Als de toegewezen gebruiker niet meer bestaat, moet u een nieuwe taakjobeigenaar toewijzen en de vereiste taakplanningstoestemming opgeven. Zie de volgende SAP Note: https://launchpad.support.sap.com/#/notes/3089828 voor meer informatie.
TASK_EXECUTOR_ERROR=Taakuitvoerder
TASK_EXECUTOR_ERROR_ERR=Er is een interne fout opgetreden bij deze taak, waarschijnlijk tijdens de voorbereidingsstappen voor uitvoering, en de taak kon niet worden gestart.
PREREQ_NOT_MET=Niet aan voorwaarde voldaan
PREREQ_NOT_MET_ERR=Deze taak kon niet worden uitgevoerd omdat er problemen zijn in de bijhorende definitie. Voorbeeld: het object is niet geïmplementeerd, een taakketen bevat een cycluslogica of een SQL van een view is ongeldig.
RESOURCE_LIMIT_ERROR=Resourcelimietfout
RESOURCE_LIMIT_ERROR_ERR=Taak kan momenteel niet worden uitgevoerd omdat er niet voldoende resources zijn of omdat deze bezet zijn.
FAIL_CONSENT_REFUSED_BY_UMS=Toestemming geweigerd
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Deze taak kon niet worden uitgevoerd in geplande runs of taakketenen vanwege een wijziging in de IdP-configuratie van de gebruiker voor een tenant. Zie de volgende SAP Note voor meer informatie: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Gepland
#XFLD: status text
SCHEDULEDNew=Permanent
#XFLD: status text
PAUSED=Gepauzeerd
#XFLD: status text
DIRECT=Direct
#XFLD: status text
MANUAL=Handmatig
#XFLD: status text
DIRECTNew=Eenvoudig
#XFLD: status text
COMPLETED=Voltooid
#XFLD: status text
FAILED=Mislukt
#XFLD: status text
RUNNING=Wordt uitgevoerd
#XFLD: status text
none=Geen
#XFLD: status text
realtime=Realtime
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Subtaak
#XFLD: New Data available in the file
NEW_DATA=Nieuwe gegevens

#XFLD: text for values shown in column Replication Status
txtOff=Uit
#XFLD: text for values shown in column Replication Status
txtInitializing=Bezig met initialiseren
#XFLD: text for values shown in column Replication Status
txtLoading=Bezig met laden
#XFLD: text for values shown in column Replication Status
txtActive=Actief
#XFLD: text for values shown in column Replication Status
txtAvailable=Beschikbaar
#XFLD: text for values shown in column Replication Status
txtError=Fout
#XFLD: text for values shown in column Replication Status
txtPaused=Gepauzeerd
#XFLD: text for values shown in column Replication Status
txtDisconnected=Verbinding verbroken
#XFLD: text for partially Persisted views
partiallyPersisted=Gedeeltelijk persistent gemaakt

#XFLD: activity text
REPLICATE=Repliceren
#XFLD: activity text
REMOVE_REPLICATED_DATA=Gerepliceerde gegevens verwijderen
#XFLD: activity text
DISABLE_REALTIME=Realtimegegevensreplicatie uitschakelen
#XFLD: activity text
REMOVE_PERSISTED_DATA=Persistente gegevens verwijderen
#XFLD: activity text
PERSIST=Persistent maken
#XFLD: activity text
EXECUTE=Uitvoeren
#XFLD: activity text
TASKLOG_CLEANUP=Takenlog opschonen
#XFLD: activity text
CANCEL_REPLICATION=Replicatie annuleren
#XFLD: activity text
MODEL_IMPORT=Modelimport
#XFLD: activity text
NONE=Geen
#XFLD: activity text
CANCEL_PERSISTENCY=Persistentie annuleren
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=View analyseren
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Viewanalyse annuleren

#XFLD: severity text
INFORMATION=Informatie
#XFLD: severity text
SUCCESS=Gelukt
#XFLD: severity text
WARNING=Waarschuwing
#XFLD: severity text
ERROR=Fout
#XFLD: text for values shown for Ascending sort order
SortInAsc=Oplopend sorteren
#XFLD: text for values shown for Descending sort order
SortInDesc=Aflopend sorteren
#XFLD: filter text for task log columns
Filter=Filteren
#XFLD: object text for task log columns
Object=Object
#XFLD: space text for task log columns
crossSpace=Ruimte

#XBUT: label for remote data access
REMOTE=Remote
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Gerepliceerd (realtime)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Gerepliceerd (snapshot)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=Realtimereplicatie is geblokkeerd vanwege een fout. Zodra de fout is gecorrigeerd kunt u de actie "Opnieuw proberen" gebruiken om door te gaan met realtimereplicatie.
ERROR_MSG=Realtimereplicatie is geblokkeerd vanwege een fout.
RETRY_FAILED_ERROR=Nieuwe poging is mislukt met een fout.
LOG_INFO_DETAILS=Er worden geen logs genereerd als gegevens worden gerepliceerd in realtimemodus. De weergegeven logs horen bij voorgaande acties.

#XBUT: Partitioning label
partitionMenuText=Partitionering
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Partitie maken
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Partitie bewerken
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Partitie verwijderen
#XFLD: Initial text
InitialPartitionText=Partities definiëren door criteria op te geven om grote gegevenssets op te delen in kleinere sets.
DefinePartition=Partities definiëren
#XFLD: Message text
partitionChangedInfo=De partitiedefinitie is gewijzigd sinds de laatste replicatie. Wijzigingen worden toegepast als gegevens de volgende keer worden geladen.
#XFLD: Message text
REAL_TIME_WARNING=Partitionering wordt alleen toegepast bij het laden van een nieuwe snapshot. Het wordt niet toegepast bij realtimereplicatie.
#XFLD: Message text
loadSelectedPartitions=Gestart met persistent maken van gegevens voor de geselecteerde partities van ''{0}''
#XFLD: Message text
loadSelectedPartitionsError=Het persistent maken van gegevens voor de geselecteerde partities van ''{0}'' is mislukt
#XFLD: Message text
viewpartitionChangedInfo=Partitiedefinitie is gewijzigd sinds de laatste persistentierun. Om de wijzigingen toe te passen, zal de volgende gegevenslading een volledige snapshot zijn, inclusief geblokkeerde partities. Als deze volledige lading is voltooid, kunt u gegevens voor enkelvoudige partities uitvoeren.
#XFLD: Message text
viewpartitionChangedInfoLocked=Partitiedefinitie is gewijzigd sinds de laatste persistentierun. Om de wijzigingen toe te passen, zal de volgende gegevenslading een volledige snapshot zijn, met uitzondering van geblokkeerde en ongewijzigde partitie-intervallen. Zodra deze lading is voltooid, kunt u weer geselecteerde partities laden.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Tabelreplicatie
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Replicatie plannen
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Planning maken
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Planning bewerken
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Planning verwijderen
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Nieuwe snapshot laden
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Gegevensreplicatie starten
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Gerepliceerde gegevens verwijderen
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Realtimetoegang inschakelen
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Realtimegegevensreplicatie inschakelen
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Realtimegegevensreplicatie uitschakelen
#XFLD: Message for replicate table action
replicateTableText=Tabelreplicatie
#XFLD: Message for replicate table action
replicateTableTextNew=Gegevensreplicatie
#XFLD: Message to schedule task
scheduleText=Plannen
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Viewpersistentie
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Gegevenspersistentie
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Nieuwe snapshot laden
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Gegevenspersistentie starten
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Persistente gegevens verwijderen
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Gepartitioneerde verwerking
#XBUT: Label for scheduled replication
scheduledTxt=Gepland
#XBUT: Label for statistics button
statisticsTxt=Statistiek
#XBUT: Label for create statistics
createStatsTxt=Statistiek maken
#XBUT: Label for edit statistics
editStatsTxt=Statistiek bewerken
#XBUT: Label for refresh statistics
refreshStatsTxt=Statistiek vernieuwen
#XBUT: Label for delete statistics
dropStatsTxt=Statistiek verwijderen
#XMSG: Create statistics success message
statsSuccessTxt=Begonnen met aanmaken van statistiek van type {0} voor {1}.
#XMSG: Edit statistics success message
statsEditSuccessTxt=Begonnen met wijzigen van type statistiek naar {0} voor {1}.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Begonnen met vernieuwen van statistiek voor {0}.
#XMSG: Drop statistics success message
statsDropSuccessTxt=Statistiek is verwijderd voor {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=Fout bij aanmaken statistiek
#XMSG: Edit statistics error message
statsEditErrorTxt=Fout bij wijzigen statistiek
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Fout bij vernieuwen statistiek
#XMSG: Drop statistics error message
statsDropErrorTxt=Fout bij verwijderen statistiek
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Weet u zeker dat u de gegevensstatistiek wilt verwijderen?
startPersistencyAdvisorLabel=Viewanalysator starten

#Partition related texts
#XFLD: Label for Column
column=Kolom
#XFLD: Label for No of Partition
noOfPartitions=Aantal partities
#XFLD: Label for Column
noOfParallelProcess=Aantal parallelle processen
#XFLD: Label text
noOfLockedPartition=Aantal geblokkeerde partities
#XFLD: Label for Partition
PARTITION=Partities
#XFLD: Label for Column
AVAILABLE=Beschikbaar
#XFLD: Statistics Label
statsLabel=Statistiek
#XFLD: Label text
COLUMN=Kolom:
#XFLD: Label text
PARALLEL_PROCESSES=Parallelle processen:
#XFLD: Label text
Partition_Range=Partitiegebied
#XFLD: Label text
Name=Naam
#XFLD: Label text
Locked=Geblokkeerd
#XFLD: Label text
Others=OVERIGE
#XFLD: Label text
Delete=Verwijderen
#XFLD: Label text
LoadData=Geselecteerde partities laden
#XFLD: Label text
LoadSelectedData=Geselecteerde partities laden
#XFLD: Confirmation text
LoadNewPersistenceConfirm=Hiermee wordt een nieuwe snapshot geladen voor alle niet-geblokkeerde en gewijzigde partities, niet alleen voor de partities die u hebt geselecteerd. Wilt u doorgaan?
#XFLD: Label text
Continue=Doorgaan

#XFLD: Label text
PARTITIONS=Partities
#XFLD: Label text
ADD_PARTITIONS=+ Partitie toevoegen
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Partitie toevoegen
#XFLD: Label text
deleteRange=Partitie verwijderen
#XFLD: Label text
LOW_PLACE_HOLDER=Lage waarde invoeren
#XFLD: Label text
HIGH_PLACE_HOLDER=Hoge waarde invoeren
#XFLD: tooltip text
lockedTooltip=Partitie blokkeren na initiële gegevensovername

#XFLD: Button text
Edit=Bewerken
#XFLD: Button text
CANCEL=Annuleren

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Meest recente statistiekupdate
#XFLD: Statistics Fields
STATISTICS=Statistiek

#XFLD:Retry label
TEXT_Retry=Opnieuw proberen
#XFLD:Retry label
TEXT_Retry_tooltip=Realtimereplicatie opnieuw proberen nadat fout is opgelost.
#XFLD: text retry
Retry=Bevestigen
#XMG: Retry confirmation text
retryConfirmationTxt=De laatste realtimereplicatie is beëindigd met een fout.\n Bevestig dat de fout is gecorrigeerd en dat realtimereplicatie opnieuw kan worden gestart.
#XMG: Retry success text
retrySuccess=Nieuwe poging is met succes geïnitieerd.
#XMG: Retry fail text
retryFail=Nieuwe poging is mislukt.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Statistiek maken
#XMSG: activity message for edit statistics
DROP_STATISTICS=Statistiek verwijderen
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Statistiek vernieuwen
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Statistiek bewerken
#XMSG: Task log message started task
taskStarted=De taak {0} is gestart.
#XMSG: Task log message for finished task
taskFinished=De taak {0} is beëindigd met status {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=De taak {0} is beëindigd om {2} met status {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=Taak {0} heeft invoerparameters.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=De taak {0} is beëindigd met een onverwachte fout. De taakstatus is ingesteld op {1}.
#XMSG: Task log message for failed task
failedToEnd=Status instellen op {0} of blokkering verwijderen is mislukt.
#XMSG: Task log message
lockNotFound=Het proces kan niet worden voltooid aangezien de blokkering ontbreekt: mogelijk is de taak geannuleerd.
#XMSG: Task log message failed task
failedOverwrite=De taak {0} is al geblokkeerd door {1}. De taak is daarom mislukt met de volgende fout: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Blokkering van deze taak is overgenomen door andere taak.
#XMSG: Task log message failed takeover
failedTakeover=Overnemen van bestaande taak is mislukt.
#XMSG: Task log message successful takeover
successTakeover=Blokkering moest worden vrijgegeven. De nieuwe blokkering voor deze taak is ingesteld.
#XMSG: Tasklog Dialog Details
txtDetails=De remote statements die tijdens de run zijn verwerkt, kunnen worden weergegeven door de remote query-monitor te openen in de details van de partitiespecifieke berichten.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=Remote SQL-instructies zijn verwijderd uit de database en kunnen niet worden weergegeven.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Remote query's die verbindingen hebben die zijn toegewezen aan andere ruimten kunnen niet worden weergegeven. Ga naar de Monitor remote query's en gebruik de instructie-ID om ze te filteren.
#XMSG: Task log message for parallel check error
parallelCheckError=De taak kan niet worden verwerkt omdat er al een andere taak wordt uitgevoerd. Deze andere taak blokkeert de huidige taak.
#XMSG: Task log message for parallel running task
parallelTaskRunning=Er wordt al een conflicterende taak uitgevoerd.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} tijdens uitvoering met correlatie-ID {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=De toegewezen gebruiker heeft geen bevoegdheden die nodig zijn om deze taak uit te voeren.

#XBUT: Label for open in Editor
openInEditor=Openen in editor
#XBUT: Label for open in Editor
openInEditorNew=Openen in Data Builder
#XFLD:Run deails label
runDetails=Rundetails
#XFLD: Label for Logs
Logs=Logs
#XFLD: Label for Settings
Settings=Instellingen
#XFLD: Label for Save button
Save=Opslaan
#XFLD: Label for Standard
Standard_PO=Geoptimaliseerd voor prestatie (aanbevolen)
#XFLD: Label for Hana low memory processing
HLMP_MO=Geoptimaliseerd voor geheugen
#XFLD: Label for execution mode
ExecutionMode=Uitvoeringsmodus
#XFLD: Label for job execution
jobExecution=Verwerkingsmodus
#XFLD: Label for Synchronous
syncExec=Synchroon
#XFLD: Label for Asynchronous
asyncExec=Asynchroon
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Standaard gebruiken (asynchroon, kan in de toekomst worden gewijzigd)
#XMSG: Save settings success
saveSettingsSuccess=SAP HANA-uitvoeringsmodus is gewijzigd.
#XMSG: Save settings failure
saveSettingsFailed=SAP HANA-uitvoeringsmodus is niet gewijzigd.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Jobuitvoering is gewijzigd.
#XMSG: Job Execution change failed
jobExecSettingFailed=Jobuitvoering is niet gewijzigd.
#XMSG: Text for Type
typeTxt=Type
#XMSG: Text for Monitor
monitorTxt=Monitor
#XMSG: Text for activity
activityTxt=Activiteit
#XMSG: Text for metrics
metricsTxt=Metrieken
#XTXT: Text for Task chain key
TASK_CHAINS=Taakketen
#XTXT: Text for View Key
VIEWS=Weergave
#XTXT: Text for remote table key
REMOTE_TABLES=Remote tabel
#XTXT: Text for Space key
SPACE=Ruimte
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Knooppunt elastische berekening
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Replicatiestroom
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Intelligente zoekactie
#XTXT: Text for Local Table
LOCAL_TABLE=Lokale tabel
#XTXT: Text for Data flow key
DATA_FLOWS=Gegevensstroom
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=SQL-scriptprocedure
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=BW-procesketen
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Weergave in monitor
#XTXT: Task List header text
taskListHeader=Taaklijst ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Metrieken voor historische runs van een gegevensstroom kunnen niet worden opgehaald.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=Details voltooide uitvoering worden op dit moment niet geladen. Probeer te vernieuwen.
#XFLD: Label text for the Metrices table header
metricesColLabel=Label van operator
#XFLD: Label text for the Metrices table header
metricesType=Type
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Aantal records
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Voer taakketen uit
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Uitvoering van taakketen is gestart.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Uitvoering van taakketen is gestart voor {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Uitvoering van taakketen is mislukt.
#XTXT: Execute button label
runLabel=Uitvoeren
#XTXT: Execute button label
runLabelNew=Run uitvoeren
#XMSG: Filter Object header
chainsFilteredTableHeader=Gefilterd op object: {0}
#XFLD: Parent task chain label
parentChainLabel=Bovenliggende taakketen:
#XFLD: Parent task chain unauthorized
Unauthorized=Niet bevoegd om weer te geven
#XFLD: Parent task chain label
parentTaskLabel=Bovenliggende taak:
#XTXT: Task status
NOT_TRIGGERED=Niet geactiveerd
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Modus volledig scherm openen
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Modus volledig scherm sluiten
#XTXT: Close Task log details right panel
closeRightColumn=Sectie sluiten
#XTXT: Sort Text
sortTxt=Sorteren
#XTXT: Filter Text
filterTxt=Filteren
#XTXT: Filter by text to show list of filters applied
filterByTxt=Filteren op
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Meer dan 5 minuten
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Meer dan 15 minuten
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Meer dan 1 uur
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Afgelopen uur
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Afgelopen 24 uur
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Vorige maand
#XTXT: Messages title text
messagesText=Meldingen

#XTXT Statistics information message
statisticsInfo=Er kunnen geen statistieken worden gemaakt voor remote tabellen met gegevenstoegang "Gerepliceerd". Verwijder de gerepliceerde gegevens in detailsmonitor remote tabellen om statistieken te maken.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Naar detailsmonitor remote tabel

#XTXT: Repair latest failed run label
retryRunLabel=Laatste run opnieuw proberen
#XTXT: Repair failed run label
retryRun=Run opnieuw proberen
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Nieuwe run taakketen is gestart
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Nieuwe run taakketen is mislukt
#XMSG: Task chain child elements name
taskChainRetryChildObject=Taak {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Nieuwe taak
#XFLD Analyzed View
analyzedView=Geanalyseerde view
#XFLD Metrics
Metrics=Metrieken
#XFLD Partition Metrics
PartitionMetrics=Partitiecijfers
#XFLD Entities
Messages=Takenlog
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Er zijn nog geen partities gedefinieerd
#XTXT: Description message for empty partition data
partitionEmptyDescText=Partities aanmaken door criteria op te geven voor het opdelen van grotere gegevensvolumes in kleinere, beter beheersbare delen.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Er zijn nog geen logs beschikbaar
#XTXT: Description message for empty runs data
runsEmptyDescText=Wanneer u een nieuwe activiteit start (een nieuwe snapshot laden, viewanalyse starten ...) ziet u hier gerelateerde logs.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=De configuratie van knooppunt elastische berekening {0} wordt niet correct onderhouden. Controleer uw definitie.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Proces voor toevoeging van knooppunt {0} voor elastische berekening is mislukt.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Maken en activeren van replica voor bronobject ''{0}''.''{1}'' in knooppunt elastische berekening {2} is gestart.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Replica voor bronobject ''{0}".''{1}'' verwijderen uit knooppunt elastische berekening {2} is gestart.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Maken en activeren van replica voor bronobject ''{0}''.''{1}'' is mislukt
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Verwijderen van replica voor bronobject ''{0}''.''{1}'' is mislukt.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Routering van berekening van analytische query''s voor ruimte {0} naar knooppunt {1} van elastische berekening is gestart.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Routering van berekening van analytische query''s voor ruimte {0} naar bijbehorend knooppunt van elastische berekening is mislukt.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Herroutering van berekening van analytische query''s voor ruimte {0} vanuit knooppunt {1} van elastische berekening is gestart.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Herroutering van berekening van analytische query''s voor ruimte {0} naar coördinator is mislukt.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Taakketen {0} voor bijbehorend knooppunt {1} van elastische berekening is gestart.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Genereren van taakketen voor knooppunt elastische berekening {0} is mislukt.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=Levering van knooppunt elastische berekening {0} met de volgende groottebepaling is gestart: vCPU''s: {1}, geheugengrootte (GiB): {2} en opslaggrootte (GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=Knooppunt elastische berekening {0} is al geleverd.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=Levering knooppunt elastische berekening {0} is al ongedaan gemaakt.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=De bewerking is niet toegestaan. Stop het knooppunt voor elastische berekening {0} en start het opnieuw om het plan voor groottebepaling bij te werken.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Proces voor verwijderen van knooppunt {0} voor elastische berekening is mislukt.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=Time-out opgetreden voor huidige run van knooppunt {0} voor elastische berekening.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=Controle van status van knooppunt {0} voor elastische berekening wordt uitgevoerd...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=Genereren taakketen voor knooppunt {0} voor elastische berekening is geblokkeerd; daarom wordt keten {1} mogelijk nog uitgevoerd.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=Bronobject ''{0}''.''{1}'' is gerepliceerd als afhankelijkheid van view ''{2}''.''{3}''.
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Kon tabel ''{0}''.''{1}'' niet repliceren omdat de replicatie van de regeltabel is verouderd.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Maximale knooppunten elastische berekening per SAP HANA Cloud-instance overschreden.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=Actieve bewerking voor knooppunt elastische berekening {0} wordt nog uitgevoerd.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Door technische problemen is het verwijderproces van de replica voor brontabel {0} stopgezet. Probeer later opnieuw.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=De aanmaak voor brontabel {0} is door technische problemen stopgezet. Probeer later opnieuw.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Kan knooppunten elastische berekening niet starten omdat gebruikslimiet is bereikt of omdat nog geen berekeningsblokuren zijn toegewezen.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Bezig met laden van taakketen en voorbereiden van uitvoering van in totaal {0} taken die deel uitmaken van deze keten.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Er wordt al een conflicterende taak uitgevoerd.
#XMSG: Replication will change
txt_replication_change=Replicatietype wordt gewijzigd.
txt_repl_viewdetails=Details weergeven

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Er was blijkbaar een fout bij het opnieuw proberen van de laatste run; de vorige taakrun is mislukt voordat het plan kon worden gegenereerd.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=Ruimte "{0}" is geblokkeerd.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=De activiteit {0} vereist dat de lokale tabel {1} wordt geïmplementeerd.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=De activiteit {0} vereist dat deltaopname wordt ingeschakeld voor de lokale tabel {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=De activiteit {0} vereist dat de lokale tabel {1} gegevens in de objectopslag opslaat.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=De activiteit {0} vereist dat de lokale tabel {1} gegevens in de database opslaat.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Er wordt begonnen met de verwijdering van verwijderde records voor de lokale tabel {0}.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Er wordt begonnen met de verwijdering van alle records voor de lokale tabel {0}.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Er wordt begonnen met de verwijdering van records voor de lokale tabel {0} volgens filterconditie {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Er is een fout opgetreden tijdens het verwijderen van verwijderde records voor de lokale tabel {0}.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Er is een fout opgetreden tijdens het verwijderen van alle records voor de lokale tabel {0}.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Alle volledig verwerkte records met wijzigingstype ''Verwijderd'' die ouder zijn dan {0} dagen worden verwijderd.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Alle volledig verwerkte records met wijzigingstype ''Verwijderd'' die ouder zijn dan {0} worden verwijderd.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} records zijn verwijderd.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} records gemarkeerd voor verwijdering.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=De verwijdering van verwijderde records voor de lokale tabel {0} is voltooid.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=De verwijdering van alle records voor de lokale tabel {0} is voltooid.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Er wordt begonnen met de markering van records als "Verwijderd" voor de lokale tabel {0}.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Er wordt begonnen met markeren van records als "Verwijderd" voor lokale tabel {0} volgens filterconditie {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Het markeren van records als "Verwijderd" voor de lokale tabel {0} is voltooid.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Gegevenswijzigingen worden tijdelijk in tabel {0} geladen.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Gegevenswijzigingen zijn tijdelijk in tabel {0} geladen.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Gegevenswijzigingen zijn verwerkt en uit tabel {0} verwijderd.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Verbindingsgegevens.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Optimaliseren van Lokale tabel (Bestand) wordt gestart.
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Er is een fout opgetreden tijdens het optimaliseren van Lokale tabel (Bestand).
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Er is een fout opgetreden. Het optimaliseren van Lokale tabel (Bestand) is gestopt.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Lokale tabel (Bestand) wordt geoptimaliseerd...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=Lokale tabel (Bestand) is geoptimaliseerd.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=Lokale tabel (Bestand) is geoptimaliseerd.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=De tabel is geoptimaliseerd met Z-orderkolommen: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Er is een fout opgetreden. Het inkorten van Lokale tabel (Bestand) is gestopt.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Lokale tabel (Bestand) wordt ingekort...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=Inkomende bufferlocatie voor Lokale tabel (Bestand) is verwijderd.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Schoonmaken (alle volledig verwerkte records verwijderen) van Lokale tabel (Bestand) wordt gestart.
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Er is een fout opgetreden tijdens het schoonmaken van Lokale tabel (Bestand).
#XMSG: Task log message
LTF_VACUUM_STOPPED=Er is een fout opgetreden. Het schoonmaken van Lokale tabel (Bestand) is gestopt.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Lokale tabel (Bestand) wordt schoongemaakt...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=Schoonmaken is voltooid.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Alle volledig verwerkte records die ouder zijn dan {0} dagen worden verwijderd.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Alle volledig verwerkte records die ouder zijn dan {0} worden verwijderd.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Samenvoegen van nieuwe records met Lokale tabel (Bestand) wordt gestart.
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Samenvoegen van nieuwe records met lokale tabel (bestand) via de instelling "Gegevens automatisch samenvoegen" wordt gestart.
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Samenvoegen van nieuwe records met lokale tabel (bestand) gestart. Deze taak is geïnitieerd via de API-samenvoegingsaanvraag.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Nieuwe records worden samengevoegd met Lokale tabel (Bestand).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Er is een fout opgetreden tijdens het samenvoegen van nieuwe records met Lokale tabel (Bestand).
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=Lokale tabel (Bestand) is samengevoegd.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Er is een fout opgetreden. Het samenvoegen van Lokale tabel (Bestand) is gestopt.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=De samenvoeging van de lokale tabel (bestand) is mislukt vanwege een fout, maar de bewerking was gedeeltelijk succesvol en sommige gegevens zijn al samengevoegd.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Er is een time-outfout opgetreden. De activiteit {0} is {1} uur lang uitgevoerd.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=De asynchrone uitvoering kon niet starten vanwege een hoge systeembelasting. Open de "Systeemmonitor" en controleer de taken in uitvoering.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=De asynchrone uitvoering is geannuleerd.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=Taak {0} uitgevoerd binnen geheugenlimieten van {1} en {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=Taak {0} uitgevoerd met resource-ID {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=Zoeken en vervangen gestart in lokale tabel (bestand).
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Zoeken en vervangen voltooid in lokale tabel (bestand).
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Zoeken en vervangen mislukt in lokale tabel (bestand).

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Er is een fout opgetreden. Het actualiseren van statistieken voor Lokale tabel (Bestand) is gestopt.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=De taak is mislukt vanwege de fout 'Onvoldoende geheugen' binnen de SAP HANA-database.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=De taak is mislukt vanwege een SAP HANA-toegangscontroleafwijzing.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=De taak is mislukt door te veel actieve SAP HANA-verbindingen.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=De bewerking Opnieuw proberen kon niet worden uitgevoerd omdat nieuwe pogingen alleen zijn toegestaan op het bovenliggende object van een geneste taakketen.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Logs van de mislukte onderliggende taken zijn niet langer beschikbaar voor opnieuw proberen.


####Metrics Labels

performanceOptimized=Geoptimaliseerd voor performance
memoryOptimized=Geoptimaliseerd voor geheugen

JOB_EXECUTION=Jobuitvoering
EXECUTION_MODE=Uitvoeringsmodus
NUMBER_OF_RECORDS_OVERALL=Aantal gepersisteerde records
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Aantal uit remote bron gelezen records
RUNTIME_MS_REMOTE_EXECUTION_TIME=Remote verwerkingstijd bron
MEMORY_CONSUMPTION_GIB=SAP HANA: piekgeheugen
NUMBER_OF_PARTITIONS=Aantal partities
MEMORY_CONSUMPTION_GIB_OVERALL=SAP HANA: piekgeheugen
NUMBER_OF_PARTITIONS_LOCKED=Aantal geblokkeerde partities
PARTITIONING_COLUMN=Partitioneringskolom
HANA_PEAK_CPU_TIME=SAP HANA - totale CPU-tijd
USED_IN_DISK=Gebruikt geheugen
INPUT_PARAMETER_PARAMETER_VALUE=Invoerparameter
INPUT_PARAMETER=Invoerparameter
ECN_ID=Naam knooppunt elastische berekening

DAC=Gegevenstoegangscontroles
YES=Ja
NO=Nee
noofrecords=Aantal records
partitionpeakmemory=SAP HANA: piekgeheugen
value=Waarde
metricsTitle=Cijfers ({0})
partitionmetricsTitle=Partities ({0})
partitionLabel=Partitie
OthersNotNull=Waarden niet opgenomen in bereiken
OthersNull=Nulwaarden
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Instellingen die zijn gebruikt voor laatste gegevenspersistentierun:
#XMSG: Message for input parameter name
inputParameterLabel=Invoerparameter
#XMSG: Message for input parameter value
inputParameterValueLabel=Waarde
#XMSG: Message for persisted data
inputParameterPersistedLabel=Persistent gemaakt op
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Gegevens verwijderen
REMOVE_DELETED_RECORDS=Verwijderde records verwijderen
MERGE_FILES=Bestanden samenvoegen
OPTIMIZE_FILES=Bestanden optimaliseren
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Weergeven in SAP BW Bridge Monitor

ANALYZE_PERFORMANCE=Prestatie analyseren
CANCEL_ANALYZE_PERFORMANCE=Prestatie analyseren annuleren

#XFLD: Label for frequency column
everyLabel=Elke
#XFLD: Plural Recurrence text for Hour
hoursLabel=Uur
#XFLD: Plural Recurrence text for Day
daysLabel=Dagen
#XFLD: Plural Recurrence text for Month
monthsLabel=Maanden
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuten

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Handleiding voor probleemoplossing persistentie weergeven</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Proces heeft geen geheugen meer. Kan gegevens voor de view "{0}" niet persistent maken. Raadpleeg de Help Portal voor meer informatie over onvoldoendegeheugenfouten. Controleer View Analyzer om de view te analyseren op de complexiteit van het geheugenverbruik.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Niet van toepassing
OPEN_BRACKET=(
CLOSE_BRACKET=)
