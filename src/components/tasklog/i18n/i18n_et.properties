
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Logi üksikasjad
#XFLD: Header
TASK_LOGS=Tegumilogid ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Käitused ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Kuva üksikasjad
#XFLD: Button text
STOP=Peata käitus
#XFLD: Label text
RUN_START=Viimase käituse algus
#XFLD: Label text
RUN_END=Viimase käituse lõpp
#XFLD: Label text
RUNTIME=Kestus
#XTIT: Count for Messages
txtDetailMessages=Teated ({0})
#XFLD: Label text
TIME=Ajatempel
#XFLD: Label text
MESSAGE=Teade
#XFLD: Label text
TASK_STATUS=Kategooria
#XFLD: Label text
TASK_ACTIVITY=Tegevus
#XFLD: Label text
RUN_START_DETAILS=Algus
#XFLD: Label text
RUN_END_DETAILS=Lõpp
#XFLD: Label text
LOGS=Käitused
#XFLD: Label text
STATUS=Olek
#XFLD: Label text
RUN_STATUS=Käituse olek
#XFLD: Label text
Runtime=Kestus
#XFLD: Label text
RuntimeTooltip=Kestus (hh : mm : ss)
#XFLD: Label text
TRIGGEREDBY=Käivitaja
#XFLD: Label text
TRIGGEREDBYNew=Käitaja
#XFLD: Label text
TRIGGEREDBYNewImp=Käituse käivitas
#XFLD: Label text
EXECUTIONTYPE=Käivitamistüüp
#XFLD: Label text
EXECUTIONTYPENew=Käituse tüüp
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Emaahela ruum
#XFLD: Refresh tooltip
TEXT_REFRESH=Värskenda
#XFLD: view Details link
VIEW_ERROR_DETAILS=Kuva üksikasjad
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Laadi alla täiendavad üksikasjad
#XMSG: Download completed
downloadStarted=Allalaadimine on käivitatud
#XMSG: Error while downloading content
errorInDownload=Allalaadimisel ilmnes tõrge.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Kuva üksikasjad
#XBTN: cancel button of task details dialog
TXT_CANCEL=Tühista
#XBTN: back button from task details
TXT_BACK=Tagasi
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Tegum jõudis lõpule
#XFLD: Log message with failed status
MSG_LOG_FAILED=Tegum nurjus
#XFLD: Master and detail table with no data
No_Data=Andmeid pole
#XFLD: Retry tooltip
TEXT_RETRY=Proovi uuesti
#XFLD: Cancel Run label
TEXT_CancelRun=Tühista käitus
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Puhasta nurjunud laadimine
#XMSG:button copy sql statement
txtSQLStatement=Kopeerige SQL-lause
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Ava kaugpäringute seiretööriist
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=SQL-kauglausete kuvamiseks klõpsake valikut „Ava kaugpäringute seiretööriist“.
#XMSG:button ok
txtOk=OK
#XMSG: button close
txtClose=Sule
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=Objekti „{0}“ käitusetoimingu tühistamine on käivitatud.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=Objekti „{0}“ käitusetoimingu tühistamine nurjus.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=Objekti „{0}“ käitusetoimingu tühistamine pole enam võimalik, kuna tiražeerimise olekut on muudetud.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Ühegi tegumilogi olek pole Töötab.
#XMSG: message for conflicting task
Task_Already_Running=Objekti „{0}“ jaoks käitatakse juba vastuolulist tegumit.
#XFLD: Label for no task log with running state title
actionInfo=Toiminguteave
#XMSG Copied to clipboard
copiedToClip=Kopeeritud lõikelauale
#XFLD copy
Copy=Kopeeri
#XFLD copy correlation ID
CopyCorrelationID=Kopeeri korrelatsiooni ID
#XFLD Close
Close=Sule
#XFLD: show more Label
txtShowMore=Kuva rohkem
#XFLD: message Label
messageLabel=Sõnum:
#XFLD: details Label
detailsLabel=Üksikasjad:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=SQL-lause \r\n käivitamine:
#XFLD:statementId Label
statementIdLabel=Lause ID:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=SQL-kauglausete \r\n arv:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Lausete arv
#XFLD: Space Label
txtSpaces=Ruum
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Volituseta ruumid
#XFLD: Privilege Error Text
txtPrivilegeError=Teil pole nende andmete kuvamiseks piisavaid õigusi.
#XFLD: Label for Object Header
DATA_ACCESS=Andmepääs
#XFLD: Label for Object Header
SCHEDULE=Graafik
#XFLD: Label for Object Header
DETAILS=Üksikasjad
#XFLD: Label for Object Header
LATEST_UPDATE=Viimatine uuendus
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Viimatine muudatus (allikas)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Värskendamise sagedus
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Ajastatud sagedus
#XFLD: Label for Object Header
NEXT_RUN=Järgmine käitus
#XFLD: Label for Object Header
CONNECTION=Ühendus
#XFLD: Label for Object Header
DP_AGENT=DP agent
#XFLD: Label for Object Header
USED_IN_MEMORY=Salvestusruumi jaoks kasutatud mälumaht (MiB)
#XFLD: Label for Object Header
USED_DISK=Salvestusruumi jaoks kasutatud kettaruum (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Suurus mälusiseses ruumis (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Suurus kettal (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Kirjete arv

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Määra nurjunuks
SET_TO_FAILED_ERR=See ülesanne oli töös, kuid kasutaja määras selle ülesande olekuks NURJUNUD.
#XFLD: Label for stopped failed
FAILLOCKED=Käitus on juba pooleli
#XFLD: sub status STOPPED
STOPPED=Peatatud
STOPPED_ERR=See ülesanne peatati, kuid tagasipööramist ei tehtud.
#XFLD: sub status CANCELLED
CANCELLED=Tühistatud
CANCELLED_ERR=See ülesande käitus tühistati pärast selle algust. Sel juhul pöörati andmed tagasi ja taastati olekusse, mis oli enne toimingu käivitamist.
#XFLD: sub status LOCKED
LOCKED=Lukus
LOCKED_ERR=Sama ülesanne oli juba töös, seega ei saa seda ülesande tööd käitada paralleelselt olemasoleva ülesande täitmisega.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Ülesande erand
TASK_EXCEPTION_ERR=Selle ülesande täitmisel ilmnes täpsustamata tõrge.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Ülesande täitmise erand
TASK_EXECUTE_EXCEPTION_ERR=Selle ülesande täitmisel ilmnes tõrge.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Volitamata
UNAUTHORIZED_ERR=Kasutajat ei õnnestunud autentida, kasutaja on lukustatud või kustutatud.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Keelatud
FORBIDDEN_ERR=Määratud kasutajal ei ole selle ülesande täitmiseks vajalikke õigusi.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Pole käivitatud
FAIL_NOT_TRIGGERED_ERR=Seda ülesande tööd ei saanud käivitada süsteemi katkestuse või mõne andmebaasisüsteemi osa tõttu, mis ei olnud kavandatud täitmise ajal saadaval. Oodake järgmist planeeritud töö teostamise aega või ajastage töö ümber.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Ajakava tühistatud
SCHEDULE_CANCELLED_ERR=Seda ülesannet ei saanud sisemise tõrke tõttu käivitada. Võtke ühendust SAP-i toega ja esitage neile selle ülesande logi üksikasjade teabe põhjal korrelatsiooni ID ja ajatempel.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Eelmine käitus on pooleli
SUCCESS_SKIPPED_ERR=Selle ülesande täitmist ei käivitatud, kuna sama ülesande eelmine käitamine on veel pooleli.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Omanik on puudu
FAIL_OWNER_MISSING_ERR=Seda ülesande tööd ei saanud käivitada, kuna sellele pole määratud süsteemi kasutajat. Määrake tööle omanikkasutaja.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Nõusolek pole saadaval
FAIL_CONSENT_NOT_AVAILABLE_ERR=Te ei ole andnud SAP-ile volitust teie nimel tegumiahelaid käitada ega andmeintegratsiooni ülesandeid ajastada. Valige oma nõusoleku andmiseks pakutud valik.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Nõusolek aegunud
FAIL_CONSENT_EXPIRED_ERR=Õigus, mis võimaldab SAP-il teie nimel tegumiahelaid käitada või andmete integreerimise ülesandeid ajastada, on aegunud. Valige oma nõusoleku uuendamiseks pakutav valik.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Nõusolek on kehtetu
FAIL_CONSENT_INVALIDATED_ERR=Seda ülesannet ei saanud täita, tavaliselt rentniku identiteedipakkuja konfiguratsiooni muudatuse tõttu. Sel juhul ei saa mõjutatud kasutaja nimel uusi tööülesandeid käivitada ega ajastada. Kui määratud kasutaja on uues IDP-s endiselt olemas, tühistage ajakava nõusolek ja andke nõusolek uuesti. Kui määratud kasutajat enam ei eksisteeri, määrake uus tööülesannete omanik ja esitage nõutav ülesande ajastamise nõusolek. Lisateabe saamiseks vaadake järgmist SAP-i teadaannet: https://launchpad.support.sap.com/#/notes/3089828.
TASK_EXECUTOR_ERROR=Ülesande täitja
TASK_EXECUTOR_ERROR_ERR=Selles ülesandes ilmnes sisemine tõrge, tõenäoliselt täitmiseks ettevalmistamise käigus, ja toimingut ei saanud käivitada.
PREREQ_NOT_MET=Täitmata eeldus
PREREQ_NOT_MET_ERR=Seda ülesannet ei saanud käivitada selle määratluses esinevate probleemide tõttu. Näited: juurutamata objekt, ringloogikaga tegumiahel või vaate SQL-i sobimatus.
RESOURCE_LIMIT_ERROR=Ressursilimiidi tõrge
RESOURCE_LIMIT_ERROR_ERR=Seda tegumit ei saa praegu teha, kuna saadaval polnud piisavalt ressursse või ressursid olid hõivatud.
FAIL_CONSENT_REFUSED_BY_UMS=Nõusolekust on keeldutud
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Seda toimingut ei saanud käitada ei ajastatud käituste ega tegumiahelate raames, kuna kasutaja identiteedipakkuja konfiguratsioon on rentnikukeskkonnas muutunud. Lisateavet leiate järgmisest SAP-i teadaandest: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Ajastatud
#XFLD: status text
SCHEDULEDNew=Alaline
#XFLD: status text
PAUSED=Peatatud
#XFLD: status text
DIRECT=Otsene
#XFLD: status text
MANUAL=Käsitsi
#XFLD: status text
DIRECTNew=Lihtne
#XFLD: status text
COMPLETED=Lõpetatud
#XFLD: status text
FAILED=Nurjunud
#XFLD: status text
RUNNING=Käitamine
#XFLD: status text
none=Pole
#XFLD: status text
realtime=Reaalajas
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Alamülesanne
#XFLD: New Data available in the file
NEW_DATA=Uued andmed

#XFLD: text for values shown in column Replication Status
txtOff=Väljas
#XFLD: text for values shown in column Replication Status
txtInitializing=Lähtestamine
#XFLD: text for values shown in column Replication Status
txtLoading=Laadimine
#XFLD: text for values shown in column Replication Status
txtActive=Aktiivne
#XFLD: text for values shown in column Replication Status
txtAvailable=Saadaval
#XFLD: text for values shown in column Replication Status
txtError=Tõrge
#XFLD: text for values shown in column Replication Status
txtPaused=Peatatud
#XFLD: text for values shown in column Replication Status
txtDisconnected=Ühendus katkestatud
#XFLD: text for partially Persisted views
partiallyPersisted=Osaliselt püsisalvestatud

#XFLD: activity text
REPLICATE=Tiražeeri
#XFLD: activity text
REMOVE_REPLICATED_DATA=Eemalda tiražeeritud andmed
#XFLD: activity text
DISABLE_REALTIME=Keela reaalajaandmete tiražeerimine
#XFLD: activity text
REMOVE_PERSISTED_DATA=Eemalda püsisalvestatud andmed
#XFLD: activity text
PERSIST=Salvesta püsivalt
#XFLD: activity text
EXECUTE=Käivita
#XFLD: activity text
TASKLOG_CLEANUP=Tacklog_Cleanup
#XFLD: activity text
CANCEL_REPLICATION=Tühista tiražeerimine
#XFLD: activity text
MODEL_IMPORT=Mudeli import
#XFLD: activity text
NONE=Pole
#XFLD: activity text
CANCEL_PERSISTENCY=Tühista püsivus
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Analüüsi vaadet
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Tühista vaateanalüsaator

#XFLD: severity text
INFORMATION=Teave
#XFLD: severity text
SUCCESS=Õnnestumine
#XFLD: severity text
WARNING=Hoiatus
#XFLD: severity text
ERROR=Tõrge
#XFLD: text for values shown for Ascending sort order
SortInAsc=Sordi kasvavalt
#XFLD: text for values shown for Descending sort order
SortInDesc=Sordi kahanevalt
#XFLD: filter text for task log columns
Filter=Filtreeri
#XFLD: object text for task log columns
Object=Objekt
#XFLD: space text for task log columns
crossSpace=Ruum

#XBUT: label for remote data access
REMOTE=Kaug-
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Tiražeeritud (reaalajas)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Tiražeeritud (hetktõmmis)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=Reaalajas kordistamine on blokeeritud tõrke tõttu. Pärast tõrke parandamist saate reaalajas kordistamist jätkata toiminguga „Proovi uuesti“.
ERROR_MSG=Reaalajas tiražeerimine on tõrke tõttu blokeeritud.
RETRY_FAILED_ERROR=Uuesti proovimise protsess nurjus tõrkega.
LOG_INFO_DETAILS=Andmete tiražeerimisel reaalajarežiimis logisid ei genereerita. Kuvatud logid on seotud eelmiste toimingutega.

#XBUT: Partitioning label
partitionMenuText=Osadeks jaotamine
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Loo osa
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Redigeeri osa
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Kustuta osa
#XFLD: Initial text
InitialPartitionText=Osade määratlemiseks määrake suurte andmekogumite väiksemateks kogumiteks jagamise kriteeriumid.
DefinePartition=Määratle osad
#XFLD: Message text
partitionChangedInfo=Partitsiooni määratlus on viimasest tiražeerimisest alates muutunud. Muudatused rakendatakse järgmise andmete laadimise ajal.
#XFLD: Message text
REAL_TIME_WARNING=Sektsioonimist rakendatakse ainult uue hetktõmmise laadimisel. Seda ei rakendata reaalajas kordistamisel.
#XFLD: Message text
loadSelectedPartitions=Üksuse „{0}“ valitud partitsioonide andmete püsisalvestamine on alanud
#XFLD: Message text
loadSelectedPartitionsError=Üksuse „{0}“ valitud partitsioonide andmete püsisalvestamine nurjus
#XFLD: Message text
viewpartitionChangedInfo=Sektsiooni määratlust on pärast viimast püsisalvestuskäitust muudetud. Muudatuste rakendamiseks tehakse andmete järgmisel laadimisel täielik hetktõmmis koos lukustatud partitsioonidega. Kui see täielik laadimine on lõpule jõudnud, saate andmeid käitada üksikute partitsioonide kohta.
#XFLD: Message text
viewpartitionChangedInfoLocked=Partitsiooni määratlust on pärast viimast püsisalvestuskäitust muudetud. Muudatuste rakendamiseks tehakse andmete järgmisel laadimisel täielik hetktõmmis, välja arvatud lukustatud ja muutmata partitsioonivahemikest. Kui see laadimine on lõpule jõudnud, saate valitud partitisoone uuesti laadida.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Tabeli tiražeerimine
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Graafiku tiražeerimine
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Loo graafik
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Redigeeri graafikut
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Kustuta graafik
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Laadi uus hetktõmmis
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Käivita andmete tiražeerimine
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Eemalda tiražeeritud andmed
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Luba reaalajas juurdepääs
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Luba reaalajas andmete tiražeerimine
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Keela reaalajaandmete tiražeerimine
#XFLD: Message for replicate table action
replicateTableText=Tabeli tiražeerimine
#XFLD: Message for replicate table action
replicateTableTextNew=Andmete tiražeerimine
#XFLD: Message to schedule task
scheduleText=Graafik
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Kuva püsisalvestus
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Andmete püsivus
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Laadi uus hetktõmmis
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Käivita andmete püsisalvestus
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Eemalda püsisalvestatud andmed
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Osadeks jaotatud töötlus
#XBUT: Label for scheduled replication
scheduledTxt=Ajastatud
#XBUT: Label for statistics button
statisticsTxt=Statistika
#XBUT: Label for create statistics
createStatsTxt=Loo statistika
#XBUT: Label for edit statistics
editStatsTxt=Redigeeri statistikat
#XBUT: Label for refresh statistics
refreshStatsTxt=Värskenda statistika
#XBUT: Label for delete statistics
dropStatsTxt=Kustuta statistika
#XMSG: Create statistics success message
statsSuccessTxt=Alustati {0} tüüpi statistika loomist {1} jaoks.
#XMSG: Edit statistics success message
statsEditSuccessTxt=Alustati {0} tüüpi statistika muutmist {1} jaoks.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Alustati statistika värskendamist {0} jaoks.
#XMSG: Drop statistics success message
statsDropSuccessTxt={0} statistika on kustutatud
#XMSG: Create statistics error message
statsCreateErrorTxt=Statistika loomisel ilmnes tõrge
#XMSG: Edit statistics error message
statsEditErrorTxt=Statistika muutmisel ilmnes tõrge
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Statistika värskendamisel ilmnes tõrge
#XMSG: Drop statistics error message
statsDropErrorTxt=Statistika kustutamisel ilmnes tõrge
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Kas soovite kindlasti andmete statistika kukutada?
startPersistencyAdvisorLabel=Käivita vaate analüüsija

#Partition related texts
#XFLD: Label for Column
column=Veerg
#XFLD: Label for No of Partition
noOfPartitions=Osade arv
#XFLD: Label for Column
noOfParallelProcess=Paralleelprotsesside arv
#XFLD: Label text
noOfLockedPartition=Lukus osade arv
#XFLD: Label for Partition
PARTITION=Osad
#XFLD: Label for Column
AVAILABLE=Saadaval
#XFLD: Statistics Label
statsLabel=Statistika
#XFLD: Label text
COLUMN=Veerg:
#XFLD: Label text
PARALLEL_PROCESSES=Paralleelprotsessid:
#XFLD: Label text
Partition_Range=Osa vahemik
#XFLD: Label text
Name=Nimi
#XFLD: Label text
Locked=Lukus
#XFLD: Label text
Others=MUUD
#XFLD: Label text
Delete=Kustuta
#XFLD: Label text
LoadData=Laadi valitud partitsioonid
#XFLD: Label text
LoadSelectedData=Laadi valitud partitsioonid
#XFLD: Confirmation text
LoadNewPersistenceConfirm=See laadib kõikide lukust avatud ja muudetud partitsioonide uue hetktõmmise, mitte ainult teie valitute oma. Kas soovite jätkata?
#XFLD: Label text
Continue=Jätka

#XFLD: Label text
PARTITIONS=Osad
#XFLD: Label text
ADD_PARTITIONS=+ Lisa osa
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Lisa osa
#XFLD: Label text
deleteRange=Kustuta osa
#XFLD: Label text
LOW_PLACE_HOLDER=Sisestage vähim väärtus
#XFLD: Label text
HIGH_PLACE_HOLDER=Sisestage suurim väärtus
#XFLD: tooltip text
lockedTooltip=Lukusta osa pärast alglaadimist

#XFLD: Button text
Edit=Redigeeri
#XFLD: Button text
CANCEL=Tühista

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Viimane statistika uuendamine
#XFLD: Statistics Fields
STATISTICS=Statistika

#XFLD:Retry label
TEXT_Retry=Proovi uuesti
#XFLD:Retry label
TEXT_Retry_tooltip=Proovige uuesti reaalajas tiražeerimist pärast tõrke lahendamist.
#XFLD: text retry
Retry=Kinnita
#XMG: Retry confirmation text
retryConfirmationTxt=Viimane reaalajas tiražeerimine katkestati tõrkega.\n Kinnitage, et tõrge on kõrvaldatud ja reaalajas tiražeerimise saab taaskäivitada.
#XMG: Retry success text
retrySuccess=Uuesti proovimise protsess on käivitatud.
#XMG: Retry fail text
retryFail=Uuesti proovimise protsess nurjus.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Loo statistika
#XMSG: activity message for edit statistics
DROP_STATISTICS=Kustuta statistika
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Värskenda statistika
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Redigeeri statistikat
#XMSG: Task log message started task
taskStarted=Tegum {0} on käivitatud.
#XMSG: Task log message for finished task
taskFinished=Tegum {0} lõppes olekuga {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=Tegum {0} lõppes {2} olekuga {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=Ülesandel {0} on sisendparameetrid.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=Tegum {0} lõppes ootamatu tõrkega. Tegumi olekuks on määratud {1}.
#XMSG: Task log message for failed task
failedToEnd=Oleku määramine olekuks {0} nurjus või luku eemaldamine nurjus.
#XMSG: Task log message
lockNotFound=Me ei saa protsessi lõpule viia, kuna lukk puudub: võimalik, et tegum on tühistatud.
#XMSG: Task log message failed task
failedOverwrite={1} on juba tegumi {0} lukustanud. Seetõttu nurjus see järgmise tõrkega: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Selle tegumi luku võttis üle teine tegum.
#XMSG: Task log message failed takeover
failedTakeover=Olemasoleva tegumi ülevõtmine nurjus.
#XMSG: Task log message successful takeover
successTakeover=Ülejäänud lukk tuli vabastada. Sellele tegumile on määratud uus lukk.
#XMSG: Tasklog Dialog Details
txtDetails=Käituse käigus töödeldud kauglausete kuvamiseks saab partitsioonikohaste teadete üksikasjades avada tööriista „Kaugpäringute seiretööriist“.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=SQL-kauglaused on andmebaasist kustutatud ja neid ei saa kuvada.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Kaugpäringuid, millel on teistele ruumidele määratud ühendusi, ei saa kuvada. Avage kaugpäringute seiretööriist ja kasutage nende filtreerimiseks lausete ID-sid.
#XMSG: Task log message for parallel check error
parallelCheckError=Tegumit ei saa töödelda, kuna teine tegum töötab ja blokeerib juba seda tegumit.
#XMSG: Task log message for parallel running task
parallelTaskRunning=Vastuoluline tegum juba töötab.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Olek {0} korrelatsiooni Id-ga {1} käituse ajal.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=Määratud kasutajal ei ole selle ülesande täitmiseks vajalikke õigusi.

#XBUT: Label for open in Editor
openInEditor=Ava redaktoris
#XBUT: Label for open in Editor
openInEditorNew=Ava andmemudelite haldamises
#XFLD:Run deails label
runDetails=Käituse üksikasjad
#XFLD: Label for Logs
Logs=Logid
#XFLD: Label for Settings
Settings=Sätted
#XFLD: Label for Save button
Save=Salvesta
#XFLD: Label for Standard
Standard_PO=Jõudluse jaoks optimeeritud (soovitatav)
#XFLD: Label for Hana low memory processing
HLMP_MO=Mälukasutuse jaoks optimeeritud
#XFLD: Label for execution mode
ExecutionMode=Käitusrežiim
#XFLD: Label for job execution
jobExecution=Töötlemisrežiim
#XFLD: Label for Synchronous
syncExec=Sünkroonne
#XFLD: Label for Asynchronous
asyncExec=Asünkroonne
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Kasuta vaikeväärtust (asünkroonne, võib tulevikus muutuda)
#XMSG: Save settings success
saveSettingsSuccess=SAP HANA käivitusrežiim on muudetud.
#XMSG: Save settings failure
saveSettingsFailed=SAP HANA käivitusrežiimi muutmine nurjus.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Töö käivitamine muutus.
#XMSG: Job Execution change failed
jobExecSettingFailed=Töö käivitamise muutmine nurjus.
#XMSG: Text for Type
typeTxt=Tüüp
#XMSG: Text for Monitor
monitorTxt=Jälgi
#XMSG: Text for activity
activityTxt=Tegevus
#XMSG: Text for metrics
metricsTxt=Mõõdik
#XTXT: Text for Task chain key
TASK_CHAINS=Tegumiahel
#XTXT: Text for View Key
VIEWS=Vaade
#XTXT: Text for remote table key
REMOTE_TABLES=Kaugtabel
#XTXT: Text for Space key
SPACE=Ruum
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Elastne andmetöötlussõlm
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Tiražeerimisvoog
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Intelligentne otsing
#XTXT: Text for Local Table
LOCAL_TABLE=Kohalik tabel
#XTXT: Text for Data flow key
DATA_FLOWS=Andmevoog
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=SQL-skripti protseduur
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=BW protsessiahel
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Kuva seiretööriistas
#XTXT: Task List header text
taskListHeader=Tegumiloend ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Andmevoo ajalooliste käituste mõõdikut ei saa tuua.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=Täieliku käituse üksikasju praegu ei laadita. Proovige värskendada.
#XFLD: Label text for the Metrices table header
metricesColLabel=Operaatori silt
#XFLD: Label text for the Metrices table header
metricesType=Tüüp
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Kirjete arv
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Käivita tegumiahel
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Tegumiahela käitus on käivitatud.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Üksuse „{0}“ tegumiahela käitus on käivitatud
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Tegumiahela käivitamine nurjus.
#XTXT: Execute button label
runLabel=Käivita
#XTXT: Execute button label
runLabelNew=Käivita käitus
#XMSG: Filter Object header
chainsFilteredTableHeader=Objekti filtreerimisalus: {0}
#XFLD: Parent task chain label
parentChainLabel=Emategumiahel:
#XFLD: Parent task chain unauthorized
Unauthorized=Vaatamisõigus puudub
#XFLD: Parent task chain label
parentTaskLabel=Emategum:
#XTXT: Task status
NOT_TRIGGERED=Pole käivitatud
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Sisene täisekraanrežiimi
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Välju täisekraanrežiimist
#XTXT: Close Task log details right panel
closeRightColumn=Sule jaotis
#XTXT: Sort Text
sortTxt=Sordi
#XTXT: Filter Text
filterTxt=Filtreeri
#XTXT: Filter by text to show list of filters applied
filterByTxt=Filtreerimisalus
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Rohkem kui 5 minutit
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Rohkem kui 15 minutit
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Rohkem kui 1 tund
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Viimane tund
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Viimased 24 tundi
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Viimane kuu
#XTXT: Messages title text
messagesText=Teated

#XTXT Statistics information message
statisticsInfo=Statistikat ei saa luua alliktabeli replikatsioonide jaoks, millel on andmete juurdepääs „Tiražeeritud“. Statistika loomiseks eemaldage alliktabeli replikatsioonide üksikasjade jälgimisprogrammis tiražeeritud andmed.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Ava kaugtabelite üksikasjade seiretööriist

#XTXT: Repair latest failed run label
retryRunLabel=Proovi viimast käitust uuesti
#XTXT: Repair failed run label
retryRun=Proovi käitust uuesti
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Tegumiahela käituse korduskatse on käivitatud
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Tegumiahela käituse korduskatse on nurjunud
#XMSG: Task chain child elements name
taskChainRetryChildObject=Tegum {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Uus tegum
#XFLD Analyzed View
analyzedView=Analüüsitud vaade
#XFLD Metrics
Metrics=Mõõdikud
#XFLD Partition Metrics
PartitionMetrics=Sektsioonimõõdikud
#XFLD Entities
Messages=Tegumilogi
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Partitsioone pole veel määratletud
#XTXT: Description message for empty partition data
partitionEmptyDescText=Et jagada suured andmehulgad väiksemateks hõlpsamini hallatavateks osadeks, looge kriteeriumide määramise teel partitsioone.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Logisid pole veel saadaval
#XTXT: Description message for empty runs data
runsEmptyDescText=Kui alustate mõnda uut tegevust (uue hetktõmmise laadimine, vaateanalüsaatori laadimine jne), kuvatakse siin selle tegevusega seotud logid.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=Elastse andmetöötlussõlme {0} konfiguratsioon ei ole vastavalt hallatud. Kontrollige oma määratlust.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Elastse andmetöötlussõlme {0} lisamine nurjus.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Lähteobjekti „{0}“.„{1}“ koopia loomine ja aktiveerimine on elastses andmetöötlussõlmes {2} alanud.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Lähteobjekti „{0}“.„{1}“ koopia eemaldamine elastsest andmetöötlussõlmest {2} on alanud.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Lähteobjekti „{0}“.„{1}“ koopia loomine ja aktiveerimine nurjus.
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Lähteobjekti „{0}“.„{1}“ koopia eemaldamine nurjus.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Ruumi {0} analüütiliste päringute marsruutimine elastsesse andmetöötlussõlme {1} on käivitatud.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Ruumi {0} analüütiliste päringute marsruutimine vastavasse elastsesse andmetöötlussõlme nurjus.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Ruumi {0} analüütiliste päringute ümbersuunamine tagasi elastsest andmetöötlussõlmest {1} on käivitatud.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Ruumi {0} analüütiliste päringute ümbersuunamine tagasi koordinaatorisse nurjus.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Tegumiahel {0} vastavasse elastsesse andmetöötlussõlme {1} on käivitatud.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Elastse andmetöötlussõlme {0} tegumiahela genereerimine nurjus.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=Elastse andmetöötlussõlme {0} ettevalmistamine on alanud järgmise mahuplaaniga: vCPU-d: {1}, mälumaht (GiB): {2} ja salvestusruumi maht (GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=Elastne andmetöötlussõlm {0} on juba ette valmistatud.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=Elastse andmetöötlussõlme {0} ettevalmistamine on juba tühistatud.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=Toiming pole lubatud. Mahupaketi uuendamiseks peatage ja taaskäivitage elastne andmetöötlussõlm {0}.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Elastse andmetöötlussõlme {0} eemaldamine nurjus.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=Elastse andmetöötlussõlme {0} praegune käitus on aegunud.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=Kontrollimine, kas elastse andmetöötlussõlme {0} olekuks on töötluses...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=Elastse andmetöötlussõlme {0} tegumiahela genereerimine on lukustatud, seega ahel {1} võib olla endiselt käimas.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=Lähteobjekt „{0}“.„{1}“ on tiražeeritud vaate „{2}“.„{3}“ sõltuvusseosena.
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Ei saa replikeerida tabelit „{0}“.„{1}“, sest reatabeli replikeerimine on aegunud.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=SAP HANA pilveksemplari kohta lubatud elastsete andmetöötlussõlmede piirmäär on ületatud.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=Elastse andmetöötlussõlme {0} tegumi käitamine on alles pooleli.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Tehniliste probleemide tõttu on koopiate kustutamine lähtetabeli {0} jaoks peatatud. Proovige hiljem uuesti.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=Tehniliste probleemide tõttu on koopiate loomine lähtetabeli {0} jaoks peatatud. Proovige hiljem uuesti.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Elastset andmetöötlussõlme ei saa käivitada, kuna kasutuslimiit on täis või andmetöötluse plokitunde pole veel eraldatud.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Tegumiahela laadimine ja sellesse ahelasse kuuluva {0} tegumi käituse ettevalmistamine.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Vastuoluline tegum juba töötab
#XMSG: Replication will change
txt_replication_change=Alliktabelite replikeerimise tüüpi muudetakse.
txt_repl_viewdetails=Kuva üksikasjad

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Näib, et viimase käituse korduskatsel ilmnes tõrge, kuna eelmine ülesandekäitud nurjus, enne kui plaani sai genereerida.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=Ruum „{0}“ on lukus.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=Tegevus {0} nõuab kohaliku tabeli {1} juurutamist.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=Tegevus {0} nõuab, et kohaliku tabeli {1} jaoks oleks deltahõive sisse lülitatud.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=Tegevus {0} nõuab, et kohalik tabel {1} salvestaks andmeid objektisalves.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=Tegevus {0} nõuab, et kohalik tabel {1} salvestaks andmeid andmebaasis.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Kohaliku tabeli {0} kustutatud kirjete eemaldamise algatamine.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Kohaliku tabeli {0} kõigi kirjete kustutamise algatamine.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Kohaliku tabeli {0} kõigi kirjete kustutamise algatamine vastavalt filtritingimusele {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Kohaliku tabeli {0} kustutatud kirjete eemaldamisel tekkis tõrge.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Kohaliku tabeli {0} kõigi kirjete kustutamisel tekkis tõrge.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Kustutatakse kõik täielikult töödeldud kirjed, mille muudatuse tüüp on „Kustutatud“ ja mis on vanemad kui {0} päeva.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Kustutatakse kõik täielikult töödeldud kirjed, mille muudatuse tüüp on „Kustutatud“ ja mis on vanemad kui {0}.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} kirjet on kustutatud.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS=Kustutamiseks on valitud {0} kirjet.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=Kohaliku tabeli {0} kustutatud kirjete eemaldamine on lõpule viidud.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Kohaliku tabeli {0} kõigi kirjete kustutamine on lõpule viidud.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Kohaliku tabeli {0} kirjeid hakatakse märkima kustutatuks.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Kohaliku tabeli {0} kirjeid hakatakse märkima kustutatuks vastavalt filtritingimusele {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Kohaliku tabeli {0} kirjete märkimine kustutatuks on lõpule viidud.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Andmete muudatused laaditakse ajutiselt tabelisse {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Andmete muudatused laaditakse ajutiselt tabelisse {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Andmete muudatused töödeldakse ja kustutatakse tabelist {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Ühenduse üksikasjad.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Käivitatakse kohaliku tabeli (fail) optimeerimine.
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Kohaliku tabeli (fail) optimeerimisel ilmnes tõrge.
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Ilmnes tõrge. Kohaliku tabeli (fail) optimeerimine on peatatud.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Kohaliku tabeli (fail) optimeerimine...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=Kohalik tabel (fail) on optimeeritud.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=Kohalik tabel (fail) on optimeeritud.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=Tabel on optimeeritud Z-järjestusega veergudega: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Ilmnes tõrge. Kohaliku tabeli (fail) kärpimine on peatatud.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Kohaliku tabeli (fail) kärpimine...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=Kohaliku tabeli (fail) sissetulevate puhvri asukoht on kukutatud.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Käivitatakse kohaliku tabeli (fail) tuulutus (kõigi täielikult töödeldud kirjete kustutamine).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Kohaliku tabeli (fail) tuulutamisel ilmnes tõrge.
#XMSG: Task log message
LTF_VACUUM_STOPPED=Ilmnes tõrge. Kohaliku tabeli (fail) tuulutus on peatatud.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Kohaliku tabeli (fail) tuulutamine...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=Tuulutamine on lõpule viidud.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Kustutatakse kõik täielikult töödeldud kirjed, mis on vanemad kui {0} päeva.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Kustutatakse kõik täielikult töödeldud kirjed, mis on vanemad kui {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Käivitatakse uute kirjete ühendamine kohaliku tabeliga (fail).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Alustatakse uute kirjete ühendamist kohaliku tabeliga (fail), kasutades sätet „Ühenda andmed automaatselt“.
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Alustatakse uute kirjete ühendamist kohalik tabeliga (fail). See toiming algatati API ühendamistaotluse kaudu.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Uued kirjed ühendatakse kohaliku tabeliga (fail).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Uute kirjete ühendamisel kohaliku tabeliga (fail) ilmnes tõrge.
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=Kohalik tabel (fail) on ühendatud.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Ilmnes tõrge. Kohaliku tabeli (fail) ühendamine on peatatud.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=Kohaliku tabeli (faili) ühendamine nurjus tõrke tõttu, kuid toiming oli osaliselt edukas ja osa andmeid on juba ühendatud.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Ilmnes ajalõputõrge. Tegevus {0} on töötanud {1} tundi.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=Asünkroonset käitust ei saanud käivitada suure süsteemikoormuse tõttu. Avage süsteemi jälgimise leht ja vaadake töötavaid tegumeid.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=Asünkroonne käitus on tühistatud.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=Toiming {0} töötas järgmiste üksuste mälulimiitide raames: {1} ja {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=Toimingut {0} käitati ressursi ID-ga {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=Otsimine ja asendamine on kohalikus tabelis (fail) käivitatud.
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Otsimine ja asendamine on kohalikus tabelis (fail) lõpule jõudnud.
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Otsimine ja asendamine on kohalikus tabelis (fail) nurjunud.

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Ilmnes tõrge. Kohaliku tabeli (fail) statistika värskendamine on peatatud.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=Tegum nurjus mälu puudumise tõrke tõttu SAP HANA andmebaasis.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=Tegum nurjus SAP HANA vastuvõtukontrolli tagasilükkamise tõttu.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=Tegum nurjus, kuna aktiivseid SAP HANA ühendusi on liiga palju.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=Uuesti proovimise toimingut ei saanud käitada, kuna uued katsed on lubatud ainult pesastatud tegumiahela emaüksuses.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Nurjunud alamtoimingute logid pole enam saadaval, et uuesti proovimine saaks jätkuda.


####Metrics Labels

performanceOptimized=Jõudluse jaoks optimeeritud
memoryOptimized=Mälukasutuse jaoks optimeeritud

JOB_EXECUTION=Töö käivitamine
EXECUTION_MODE=Käitusrežiim
NUMBER_OF_RECORDS_OVERALL=Püsikirjete arv
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Kaugallikast loetud kirjete arv
RUNTIME_MS_REMOTE_EXECUTION_TIME=Kaugallika töötluse aeg
MEMORY_CONSUMPTION_GIB=SAP HANA maksimaalne mälukasutus
NUMBER_OF_PARTITIONS=Sektsioonide arv
MEMORY_CONSUMPTION_GIB_OVERALL=SAP HANA maksimaalne mälukasutus
NUMBER_OF_PARTITIONS_LOCKED=Lukus sektsioonide arv
PARTITIONING_COLUMN=Sektsioonimisveerg
HANA_PEAK_CPU_TIME=SAP HANA protsessorikasutusaeg kokku
USED_IN_DISK=Kasutatud salvestusruum
INPUT_PARAMETER_PARAMETER_VALUE=Sisendparameeter
INPUT_PARAMETER=Sisendparameeter
ECN_ID=Elastse andmetöötlussõlme nimi

DAC=Andmepääsu juhtelemendid
YES=Jah
NO=Ei
noofrecords=Kirjete arv
partitionpeakmemory=SAP HANA maksimaalne mälukasutus
value=Väärtus
metricsTitle=Mõõdikud ({0})
partitionmetricsTitle=Sektsioonid ({0})
partitionLabel=Sektsioon
OthersNotNull=Väärtused, mis pole vahemikesse kaasatud
OthersNull=Tühiväärtused
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Viimase andmete püsisalvestuskäituse jaoks kasutatud sätted:
#XMSG: Message for input parameter name
inputParameterLabel=Sisendparameeter
#XMSG: Message for input parameter value
inputParameterValueLabel=Väärtus
#XMSG: Message for persisted data
inputParameterPersistedLabel=Püsisalvestatud
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Kustuta andmed
REMOVE_DELETED_RECORDS=Eemalda kustutatud kirjed
MERGE_FILES=Ühenda failid
OPTIMIZE_FILES=Optimeeri failid
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Kuva SAP BW Bridge’i jälgimisprogrammis

ANALYZE_PERFORMANCE=Analüüsi jõudlust
CANCEL_ANALYZE_PERFORMANCE=Tühista jõudluse analüüsimine

#XFLD: Label for frequency column
everyLabel=Iga
#XFLD: Plural Recurrence text for Hour
hoursLabel=tunni järel
#XFLD: Plural Recurrence text for Day
daysLabel=päeva järel
#XFLD: Plural Recurrence text for Month
monthsLabel=kuu järel
#XFLD: Plural Recurrence text for Minutes
minutesLabel=minuti järel

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Vaadake püsisalvestamise tõrkeotsingu juhendit</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Protsessil on mäluruum otsas. Vaate „{0}“ andmeid ei saa püsisalvestada. Täpsemat teavet selle kohta, mida teha mäluruumi otsasaamise tõrgete korral, leiate spikriportaalist. Samuti võiksite vaadata vaateanalüsaatorit, et analüüsida vaate mälukasutuse keerukust.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Pole kohaldatav
OPEN_BRACKET=(
CLOSE_BRACKET=)
