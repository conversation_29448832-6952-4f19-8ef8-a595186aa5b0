
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=日誌明細
#XFLD: Header
TASK_LOGS=工作細項日誌 ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=執行 ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=檢視明細
#XFLD: Button text
STOP=停止執行
#XFLD: Label text
RUN_START=最後執行開始時間
#XFLD: Label text
RUN_END=最後執行結束時間
#XFLD: Label text
RUNTIME=持續期
#XTIT: Count for Messages
txtDetailMessages=訊息 ({0})
#XFLD: Label text
TIME=時間印記
#XFLD: Label text
MESSAGE=訊息
#XFLD: Label text
TASK_STATUS=種類
#XFLD: Label text
TASK_ACTIVITY=活動
#XFLD: Label text
RUN_START_DETAILS=開始
#XFLD: Label text
RUN_END_DETAILS=結束
#XFLD: Label text
LOGS=執行
#XFLD: Label text
STATUS=狀態
#XFLD: Label text
RUN_STATUS=執行狀態
#XFLD: Label text
Runtime=持續期
#XFLD: Label text
RuntimeTooltip=持續期 (hh : mm : ss)
#XFLD: Label text
TRIGGEREDBY=驅動者
#XFLD: Label text
TRIGGEREDBYNew=執行者
#XFLD: Label text
TRIGGEREDBYNewImp=開始執行者
#XFLD: Label text
EXECUTIONTYPE=執行類型
#XFLD: Label text
EXECUTIONTYPENew=執行類型
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=上層鏈空間
#XFLD: Refresh tooltip
TEXT_REFRESH=重新整理
#XFLD: view Details link
VIEW_ERROR_DETAILS=檢視明細
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=下載其他明細
#XMSG: Download completed
downloadStarted=已開始下載
#XMSG: Error while downloading content
errorInDownload=下載時發生錯誤。
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=檢視明細
#XBTN: cancel button of task details dialog
TXT_CANCEL=取消
#XBTN: back button from task details
TXT_BACK=返回
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=工作細項已完成
#XFLD: Log message with failed status
MSG_LOG_FAILED=工作細項失敗
#XFLD: Master and detail table with no data
No_Data=無資料
#XFLD: Retry tooltip
TEXT_RETRY=重試
#XFLD: Cancel Run label
TEXT_CancelRun=取消執行
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=清除失敗的載入作業
#XMSG:button copy sql statement
txtSQLStatement=複製 SQL 陳述式
#XMSG:button open remote query monitor
txtOpenQueryMonitor=開啟遠端查詢監控器
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=若要顯示遠端 SQL 陳述式，請按一下「開啟遠端查詢監控器」。
#XMSG:button ok
txtOk=確定
#XMSG: button close
txtClose=關閉
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=已開始取消物件 "{0}" 的執行動作。
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=取消物件 "{0}" 的執行動作失敗。
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=由於複製狀態已更改，因此無法再取消物件 "{0}" 的執行動作。
#XMSG: Info message for no Running state logId
noTaskWithRunningState=沒有狀態為執行中的工作細項日誌。
#XMSG: message for conflicting task
Task_Already_Running=物件 "{0}" 的衝突工作細項執行中。
#XFLD: Label for no task log with running state title
actionInfo=動作資訊
#XMSG Copied to clipboard
copiedToClip=已複製到剪貼簿
#XFLD copy
Copy=複製
#XFLD copy correlation ID
CopyCorrelationID=複製關聯 ID
#XFLD Close
Close=關閉
#XFLD: show more Label
txtShowMore=顯示更多
#XFLD: message Label
messageLabel=訊息：
#XFLD: details Label
detailsLabel=明細：
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=執行 SQL \r\n陳述式：
#XFLD:statementId Label
statementIdLabel=陳述式 ID：
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=遠端 \r\nSQL 陳述式的數量：
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=陳述式數量
#XFLD: Space Label
txtSpaces=空間
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=未授權的空間
#XFLD: Privilege Error Text
txtPrivilegeError=您沒有足夠權限以檢視此資料。
#XFLD: Label for Object Header
DATA_ACCESS=資料存取
#XFLD: Label for Object Header
SCHEDULE=排程
#XFLD: Label for Object Header
DETAILS=明細
#XFLD: Label for Object Header
LATEST_UPDATE=最近更新
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=最近更改 (來源)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=重新整理頻率
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=排程頻率
#XFLD: Label for Object Header
NEXT_RUN=下一次執行
#XFLD: Label for Object Header
CONNECTION=連線
#XFLD: Label for Object Header
DP_AGENT=資料提供代理程式
#XFLD: Label for Object Header
USED_IN_MEMORY=用於儲存的記憶體 (MiB)
#XFLD: Label for Object Header
USED_DISK=用於儲存的硬碟 (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=大小記憶體內 (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=硬碟大小 (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=記錄數量

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=設定為失敗
SET_TO_FAILED_ERR=此工作細項執行中，但使用者將此工作細項狀態設定為失敗。
#XFLD: Label for stopped failed
FAILLOCKED=執行已在進行中
#XFLD: sub status STOPPED
STOPPED=已停止
STOPPED_ERR=此工作細項已停止，但未執行復原。
#XFLD: sub status CANCELLED
CANCELLED=已取消
CANCELLED_ERR=此工作細項執行在開始後已取消。此情況將復原資料，並還原為工作細項執行初始驅動前的狀態。
#XFLD: sub status LOCKED
LOCKED=已鎖住
LOCKED_ERR=相同工作細項已在執行中，此工作細項工作無法透過現有工作細項執行平行執行。
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=工作細項異常
TASK_EXCEPTION_ERR=此工作細項執行期間發生未指定錯誤。
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=工作細項執行異常
TASK_EXECUTE_EXCEPTION_ERR=此工作細項執行期間發生錯誤。
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=未授權
UNAUTHORIZED_ERR=使用者無法驗證、已鎖住或已刪除。
#XFLD: sub status FORBIDDEN
FORBIDDEN=禁止
FORBIDDEN_ERR=指派的使用者沒有執行此工作細項的所需權限。
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=未驅動
FAIL_NOT_TRIGGERED_ERR=由於系統中斷或部份資料庫系統在計劃執行時間無法使用，無法執行此工作細項。請等待下一次排程工作執行時間或重新排程工作。
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=已取消排程
SCHEDULE_CANCELLED_ERR=此工作細項工作因發生內部錯誤無法執行。請聯絡 SAP 支援並提供來自此工作細項工作日誌詳細資訊的關聯 ID 和時間印記。
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=先前執行進行中
SUCCESS_SKIPPED_ERR=由於相同工作細項先前執行仍在進行中，未驅動此工作細項執行。
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=缺少所有人
FAIL_OWNER_MISSING_ERR=由於此工作細項工作沒有指派的系統使用者，因此無法執行。請將所有人使用者指派給工作。
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=沒有同意
FAIL_CONSENT_NOT_AVAILABLE_ERR=您未授權 SAP 代替您執行工作細項鏈或排程資料整合工作細項。請選擇提供的選項以同意。
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=同意已到期
FAIL_CONSENT_EXPIRED_ERR=允許 SAP 代替您執行工作細項鏈或排程資料整合工作細項的授權已到期。請選擇提供的選項以更新同意。
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=同意已失效
FAIL_CONSENT_INVALIDATED_ERR=無法執行此工作細項，通常是因為租用戶的識別提供者組態有所更改。在此情況下，受影響的使用者名稱中無法執行或排程新的工作細項工作。若新的 IdP 中仍有指派的使用者，則撤銷排程同意並再次授予。若指派的使用者不再存在，請指派新工作細項工作所有人，並提供所需的工作細項排程同意。請參閱下列 SAP 註記：https://launchpad.support.sap.com/#/notes/3089828 for more information。
TASK_EXECUTOR_ERROR=工作細項執行程序
TASK_EXECUTOR_ERROR_ERR=此工作細項發生內部錯誤 (通常發生於執行準備步驟)，無法開始工作細項。
PREREQ_NOT_MET=先決條件未滿足
PREREQ_NOT_MET_ERR=由於此工作細項定義中發生問題，因此無法執行。例如，物件未部署、工作細項鏈包含循環邏輯，或者檢視的 SQL 無效。
RESOURCE_LIMIT_ERROR=資源限制錯誤
RESOURCE_LIMIT_ERROR_ERR=由於資源不足或忙碌中，因此目前無法執行工作細項。
FAIL_CONSENT_REFUSED_BY_UMS=已拒絕同意
FAIL_CONSENT_REFUSED_BY_UMS_ERR=由於租用戶的使用者識別提供者組態已更改，因此無法在排程執行或工作細項鏈中執行此工作細項。如需更多資訊，請參閱下列 SAP 註記：https://launchpad.support.sap.com/#/notes/3120806。
#XFLD: status text
SCHEDULED=已排程
#XFLD: status text
SCHEDULEDNew=永久
#XFLD: status text
PAUSED=已暫停
#XFLD: status text
DIRECT=直接
#XFLD: status text
MANUAL=手動
#XFLD: status text
DIRECTNew=簡易
#XFLD: status text
COMPLETED=已完成
#XFLD: status text
FAILED=失敗
#XFLD: status text
RUNNING=執行中
#XFLD: status text
none=無
#XFLD: status text
realtime=即時
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=子工作細項
#XFLD: New Data available in the file
NEW_DATA=新資料

#XFLD: text for values shown in column Replication Status
txtOff=關閉
#XFLD: text for values shown in column Replication Status
txtInitializing=正在初始化
#XFLD: text for values shown in column Replication Status
txtLoading=正在載入
#XFLD: text for values shown in column Replication Status
txtActive=啟用中
#XFLD: text for values shown in column Replication Status
txtAvailable=可用
#XFLD: text for values shown in column Replication Status
txtError=錯誤
#XFLD: text for values shown in column Replication Status
txtPaused=已暫停
#XFLD: text for values shown in column Replication Status
txtDisconnected=已中斷連線
#XFLD: text for partially Persisted views
partiallyPersisted=已部份保存

#XFLD: activity text
REPLICATE=複製
#XFLD: activity text
REMOVE_REPLICATED_DATA=移除已複製的資料
#XFLD: activity text
DISABLE_REALTIME=停用即時資料複製
#XFLD: activity text
REMOVE_PERSISTED_DATA=移除存續性資料
#XFLD: activity text
PERSIST=持續
#XFLD: activity text
EXECUTE=執行
#XFLD: activity text
TASKLOG_CLEANUP=Tacklog_Cleanup
#XFLD: activity text
CANCEL_REPLICATION=取消複製
#XFLD: activity text
MODEL_IMPORT=模型匯入
#XFLD: activity text
NONE=無
#XFLD: activity text
CANCEL_PERSISTENCY=取消存續性
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=分析檢視
#XFLD: activity text
CANCEL_VIEW_ANALYZER=取消檢視分析工具

#XFLD: severity text
INFORMATION=資訊
#XFLD: severity text
SUCCESS=成功
#XFLD: severity text
WARNING=警告
#XFLD: severity text
ERROR=錯誤
#XFLD: text for values shown for Ascending sort order
SortInAsc=升冪排序
#XFLD: text for values shown for Descending sort order
SortInDesc=降冪排序
#XFLD: filter text for task log columns
Filter=篩選
#XFLD: object text for task log columns
Object=物件
#XFLD: space text for task log columns
crossSpace=空間

#XBUT: label for remote data access
REMOTE=遠端
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=已複製 (即時)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=已複製 (概要)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=即時複製已因發生錯誤而凍結。更正錯誤後，您可使用「重試」動作繼續執行即時複製。
ERROR_MSG=即時複製已因發生錯誤而凍結。
RETRY_FAILED_ERROR=重試程序失敗，出現錯誤。
LOG_INFO_DETAILS=於即時模式複製資料時，未產生日誌。顯示的日誌與先前動作相關。

#XBUT: Partitioning label
partitionMenuText=分割
#XBUT: Drop down menu button to create a partition
createPartitionLabel=建立分割
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=編輯分割
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=刪除分割
#XFLD: Initial text
InitialPartitionText=透過指定準則來定義分割，將大資料集分割為較小集。
DefinePartition=定義分割
#XFLD: Message text
partitionChangedInfo=分割定義已因上次複製而更改。更改將套用至下一次資料載入。
#XFLD: Message text
REAL_TIME_WARNING=只會在載入新概要時套用分割，不會針對即時複製套用。
#XFLD: Message text
loadSelectedPartitions=已開始保存 "{0}" 所選分割的資料
#XFLD: Message text
loadSelectedPartitionsError=無法保存 "{0}" 所選分割的資料
#XFLD: Message text
viewpartitionChangedInfo=上次存續性執行後已更改分割定義。若要套用更改內容，下次資料載入將是包含鎖住分割的完整概要。此完整載入結束後，您即可執行單一分割的資料。
#XFLD: Message text
viewpartitionChangedInfoLocked=上次存續性執行後已更改分割定義。若要套用更改內容，下次資料載入將是除了鎖住和未更改分割範圍的完整概要。此次載入結束後，您即可再次載入所選分割。
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=表格複製
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=排程複製
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=建立排程
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=編輯排程
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=刪除排程
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=載入新概要
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=開始資料複製
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=移除已複製的資料
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=啟用即時存取
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=啟用即時資料複製
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=停用即時資料複製
#XFLD: Message for replicate table action
replicateTableText=表格複製
#XFLD: Message for replicate table action
replicateTableTextNew=資料複製
#XFLD: Message to schedule task
scheduleText=排程
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=檢視存續性
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=資料存續性
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=載入新概要
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=開始資料存續性
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=移除存續性資料
#XFLD: Partitioned Processign
EnablePartitionedProcessing=已分割處理
#XBUT: Label for scheduled replication
scheduledTxt=已排程
#XBUT: Label for statistics button
statisticsTxt=統計
#XBUT: Label for create statistics
createStatsTxt=建立統計
#XBUT: Label for edit statistics
editStatsTxt=編輯統計
#XBUT: Label for refresh statistics
refreshStatsTxt=重新整理統計
#XBUT: Label for delete statistics
dropStatsTxt=刪除統計
#XMSG: Create statistics success message
statsSuccessTxt=已開始建立 {1} 類型為 {0} 的統計。
#XMSG: Edit statistics success message
statsEditSuccessTxt=已開始將 {1} 的統計更改為 {0}。
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=已開始重新整理 {0} 的統計。
#XMSG: Drop statistics success message
statsDropSuccessTxt=已成功刪除 {0} 的統計
#XMSG: Create statistics error message
statsCreateErrorTxt=建立統計時發生錯誤
#XMSG: Edit statistics error message
statsEditErrorTxt=更改統計時發生錯誤
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=重新整理統計時發生錯誤
#XMSG: Drop statistics error message
statsDropErrorTxt=刪除統計時發生錯誤
#XMG: Warning text for deleting statistics
statsDelWarnTxt=您確定要清除資料統計嗎?
startPersistencyAdvisorLabel=開始檢視分析工具

#Partition related texts
#XFLD: Label for Column
column=欄
#XFLD: Label for No of Partition
noOfPartitions=分割數量
#XFLD: Label for Column
noOfParallelProcess=平行程序數量
#XFLD: Label text
noOfLockedPartition=鎖住的分割數量
#XFLD: Label for Partition
PARTITION=分割
#XFLD: Label for Column
AVAILABLE=可用
#XFLD: Statistics Label
statsLabel=統計
#XFLD: Label text
COLUMN=欄：
#XFLD: Label text
PARALLEL_PROCESSES=平行程序：
#XFLD: Label text
Partition_Range=分割範圍
#XFLD: Label text
Name=名稱
#XFLD: Label text
Locked=已鎖住
#XFLD: Label text
Others=其他
#XFLD: Label text
Delete=刪除
#XFLD: Label text
LoadData=載入所選分割
#XFLD: Label text
LoadSelectedData=載入所選分割
#XFLD: Confirmation text
LoadNewPersistenceConfirm=除了您所選分割之外，這也會針對所有已解除鎖定和已更改分割載入新概要。您要繼續嗎？
#XFLD: Label text
Continue=繼續

#XFLD: Label text
PARTITIONS=分割
#XFLD: Label text
ADD_PARTITIONS=+ 新增分割
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=新增分割
#XFLD: Label text
deleteRange=刪除分割
#XFLD: Label text
LOW_PLACE_HOLDER=輸入低值
#XFLD: Label text
HIGH_PLACE_HOLDER=輸入高值
#XFLD: tooltip text
lockedTooltip=初始載入後鎖住分割

#XFLD: Button text
Edit=編輯
#XFLD: Button text
CANCEL=取消

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=上次統計更新
#XFLD: Statistics Fields
STATISTICS=統計

#XFLD:Retry label
TEXT_Retry=重試
#XFLD:Retry label
TEXT_Retry_tooltip=錯誤解決後重試即時複製。
#XFLD: text retry
Retry=確認
#XMG: Retry confirmation text
retryConfirmationTxt=上次即時複製已終止 (出現錯誤)。\n請確認已更正錯誤且可重新開始即時複製。
#XMG: Retry success text
retrySuccess=已成功初始重試程序。
#XMG: Retry fail text
retryFail=程序重試失敗。
#XMSG: activity message for create statistics
CREATE_STATISTICS=建立統計
#XMSG: activity message for edit statistics
DROP_STATISTICS=刪除統計
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=重新整理統計
#XMSG: activity message for edit statistics
ALTER_STATISTICS=編輯統計
#XMSG: Task log message started task
taskStarted=工作細項 {0} 已開始。
#XMSG: Task log message for finished task
taskFinished=工作細項 {0} 已結束，狀態為 {1}。
#XMSG: Task log message for finished task with end time
taskFinishedAt=工作細項 {0} 已於 {2} 結束，狀態為 {1}。
#XMSG: Task {0} has input parameters
taskHasInputParameters=工作細項 {0} 有輸入參數。
#XMSG: Task log message for unexpected error
unexpectedExecutionError=工作細項 {0} 已結束，發生未預期錯誤。工作細項狀態已設定為 {1}。
#XMSG: Task log message for failed task
failedToEnd=無法將狀態設定為 {0}，或無法移除加鎖。
#XMSG: Task log message
lockNotFound=由於缺少加鎖，因此無法結束程序：工作細項可能已取消。
#XMSG: Task log message failed task
failedOverwrite={1} 已鎖住工作細項 {0}，因此失敗並出現下列錯誤：{2}。
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=其他工作細項已接手鎖住此工作細項。
#XMSG: Task log message failed takeover
failedTakeover=無法取代現有工作細項。
#XMSG: Task log message successful takeover
successTakeover=必須釋放剩餘加鎖。已設定此工作細項的新加鎖。
#XMSG: Tasklog Dialog Details
txtDetails=可透過在分割特定訊息明細中開啟遠端查詢監控器，顯示執行期間處理的遠端陳述式。
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=已從資料庫刪除遠端 SQL 陳述式並無法顯示。
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=無法顯示將連線指派給其他空間的遠端查詢，請移至遠端查詢監控器並使用陳述式 ID 進行篩選。
#XMSG: Task log message for parallel check error
parallelCheckError=由於其他工作細項執行中且已凍結此工作細項，因此該工作細項無法處理。
#XMSG: Task log message for parallel running task
parallelTaskRunning=衝突工作細項執行中。
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=執行關聯 ID {1} 期間出現狀態 {0}。
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=指派的使用者沒有執行此工作細項的所需權限。

#XBUT: Label for open in Editor
openInEditor=於編輯器中開啟
#XBUT: Label for open in Editor
openInEditorNew=在資料模型建立器中開啟
#XFLD:Run deails label
runDetails=執行明細
#XFLD: Label for Logs
Logs=日誌
#XFLD: Label for Settings
Settings=設定
#XFLD: Label for Save button
Save=儲存
#XFLD: Label for Standard
Standard_PO=效能最佳化 (建議)
#XFLD: Label for Hana low memory processing
HLMP_MO=記憶體最佳化
#XFLD: Label for execution mode
ExecutionMode=執行模式
#XFLD: Label for job execution
jobExecution=處理模式
#XFLD: Label for Synchronous
syncExec=同步
#XFLD: Label for Asynchronous
asyncExec=非同步
#XFLD: Label for default asynchronous execution
defaultAsyncExec=使用預設 (非同步，未來可能更改)
#XMSG: Save settings success
saveSettingsSuccess=已更改 SAP HANA 執行模式。
#XMSG: Save settings failure
saveSettingsFailed=SAP HANA 執行模式更改失敗。
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=已更改工作執行。
#XMSG: Job Execution change failed
jobExecSettingFailed=工作執行更改失敗。
#XMSG: Text for Type
typeTxt=類型
#XMSG: Text for Monitor
monitorTxt=監控
#XMSG: Text for activity
activityTxt=活動
#XMSG: Text for metrics
metricsTxt=公制
#XTXT: Text for Task chain key
TASK_CHAINS=工作細項鏈
#XTXT: Text for View Key
VIEWS=檢視
#XTXT: Text for remote table key
REMOTE_TABLES=遠端表格
#XTXT: Text for Space key
SPACE=空間
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=彈性計算節點
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=複製流程
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=智慧查詢
#XTXT: Text for Local Table
LOCAL_TABLE=本端表格
#XTXT: Text for Data flow key
DATA_FLOWS=資料流程
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=SQL 指令碼程序
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=BW 程序鏈
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=於監控器中檢視
#XTXT: Task List header text
taskListHeader=工作細項清單 ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=無法擷取資料流程的歷史執行公制。
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=此時未載入完成執行明細，請嘗試重新整理。
#XFLD: Label text for the Metrices table header
metricesColLabel=運算子標籤
#XFLD: Label text for the Metrices table header
metricesType=類型
#XFLD: Label text for the Metrices table header
metricesRecordLabel=記錄計數
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=執行工作細項鏈
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=已開始執行工作細項鏈。
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=已開始 {0} 的工作細項鏈執行
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=無法執行工作細項鏈。
#XTXT: Execute button label
runLabel=執行
#XTXT: Execute button label
runLabelNew=開始執行
#XMSG: Filter Object header
chainsFilteredTableHeader=按物件篩選：{0}
#XFLD: Parent task chain label
parentChainLabel=上層工作細項鏈：
#XFLD: Parent task chain unauthorized
Unauthorized=未授權檢視
#XFLD: Parent task chain label
parentTaskLabel=上層工作細項：
#XTXT: Task status
NOT_TRIGGERED=未驅動
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=進入全螢幕模式
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=跳出全螢幕模式
#XTXT: Close Task log details right panel
closeRightColumn=關閉區段
#XTXT: Sort Text
sortTxt=排序
#XTXT: Filter Text
filterTxt=篩選
#XTXT: Filter by text to show list of filters applied
filterByTxt=篩選依據
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=超過 5 分鐘
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=超過 15 分鐘
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=超過 1 小時
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=過去 1 小時
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=過去 24 小時
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=上個月
#XTXT: Messages title text
messagesText=訊息

#XTXT Statistics information message
statisticsInfo=無法以資料存取「已複製」建立遠端表格的統計。若要建立統計，請移除「遠端表格明細監控器」中的複製資料。
#XFLD
GO_TO_REMOTETABLE_DETAILS=移至遠端表格明細監控器

#XTXT: Repair latest failed run label
retryRunLabel=重試最新執行
#XTXT: Repair failed run label
retryRun=重試執行
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=已開始重試執行工作細項鏈
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=重試執行工作細項鏈失敗
#XMSG: Task chain child elements name
taskChainRetryChildObject=工作細項 {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=新工作細項
#XFLD Analyzed View
analyzedView=分析檢視
#XFLD Metrics
Metrics=公制
#XFLD Partition Metrics
PartitionMetrics=分割公制
#XFLD Entities
Messages=工作細項日誌
#XTXT: Title Message for empty partition data
partitionEmptyTitle=尚未定義分割
#XTXT: Description message for empty partition data
partitionEmptyDescText=透過指定準則，將較大資料量分割為較小、較易管理部份的資料量，來建立分割。

#XTXT: Title Message for empty runs data
runsEmptyTitle=尚無日誌
#XTXT: Description message for empty runs data
runsEmptyDescText=開始新作業時 (載入新概要、開始檢視分析工具...)，您將在此看到相關日誌。


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=未相應維護彈性計算節點 {0} 組態。請檢查定義。
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=新增彈性計算節點 {0} 的程序失敗。
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=已開始彈性計算節點 {2} 中來源物件 "{0}"."{1}" 的副本建立和啟用作業。
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=已開始彈性計算節點 {2} 中來源物件 "{0}"."{1}" 的副本移除作業。
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=來源物件 "{0}"."{1}" 的副本建立和啟用作業失敗。
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=來源物件 "{0}"."{1}" 的副本移除作業失敗。
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=已開始將空間 {0} 的分析查詢計算傳送至彈性計算節點 {1}。
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=將空間 {0} 的分析查詢計算傳送至對應彈性計算節點失敗。
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=已開始將空間 {0} 的分析查詢計算從彈性計算節點 {1} 傳回。
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=將空間 {0} 的分析查詢計算傳回協調員失敗。
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=已驅動工作細項鏈 {0} 至對應彈性計算節點 {1}。
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=彈性計算節點 {0} 的工作細項鏈產生失敗。
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=已使用指定的大小計劃，開始彈性計算節點 {0} 的佈建：vCPU：{1}、記憶體 (GiB)：{2} 和儲存大小 (GiB)：{3}。
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=已佈建彈性計算節點 {0}。
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=已取消佈建彈性計算節點 {0}。
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=不允許作業。請停止彈性計算節點 {0} 並重新開始以更新大小計劃。
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=移除彈性計算節點 {0} 的程序失敗。
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=目前彈性計算節點 {0} 的執行作業已逾時。
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=檢查彈性計算節點 {0} 的狀態進行中...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=已鎖住彈性計算節點 {0} 的工作細項鏈產生作業，因此鏈 {1} 可能仍在執行中。
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=來源物件 "{0}"."{1}" 已複製為檢視 "{2}"."{3}" 的相關性。
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=無法複製表格 "{0}"."{1}"，因為列表格複製已淘汰。
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=已超過各 SAP HANA Cloud 事例的最大彈性計算節點。
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=彈性計算節點 {0} 的執行作業仍在進行中。
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=由於發生技術問題，因此已停止刪除來源表格 {0} 的副本。請稍後再試一次。
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=由於發生技術問題，因此已停止建立來源表格 {0} 的副本。請稍後再試一次。
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=由於已達到使用限制，或尚未分配計算區塊時數，因此無法開始彈性計算節點。
#XMSG: Task log message for chain preparation
chainLoadFromRepository=載入工作細項鏈，並準備執行屬於此鏈的總計 {0} 個工作細項。
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=衝突工作細項執行中
#XMSG: Replication will change
txt_replication_change=複製類型將更改。
txt_repl_viewdetails=檢視明細

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=由於上一個工作細項執行在可產生計劃前失敗，因此重試最後一個執行時似乎發生錯誤。

#general messages
EXECUTION_ERROR_LOCKED_SPACE=空間 "{0}" 已鎖定。

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=作業 {0} 需要部署本端表格 {1}。
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=作業 {0} 需要開啟本端表格 {1} 的差異擷取。
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=作業 {0} 需要本端表格 {1} 在物件儲存中儲存資料。
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=作業 {0} 需要本端表格 {1} 在資料庫中儲存資料。

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=正在開始移除本端表格 {0} 的已刪除記錄。
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=正在開始刪除本端表格 {0} 的所有記錄。
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=正在根據篩選條件 {1}，開始刪除本端表格 {0} 的記錄。
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=移除本端表格 {0} 的已刪除記錄時發生錯誤。
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=刪除本端表格 {0} 的所有記錄時發生錯誤。
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=正在刪除所有晚於 {0} 天更改類型為「已刪除」的完全處理記錄。
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=正在刪除所有晚於 {0} 更改類型為「已刪除」的完全處理記錄。
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS=已刪除 {0} 個記錄。
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS=已將 {0} 個記錄標記刪除。
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=已完成移除本端表格 {0} 的已刪除記錄。
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=已完成刪除本端表格 {0} 的所有記錄。
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=正在開始將本端表格 {0} 的記錄標記為「已刪除」。
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=正在根據篩選條件 {1}，開始將本端表格 {0} 的記錄標記為「已刪除」。
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=已完成將本端表格 {0} 的記錄標記為「已刪除」。

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=資料更改正在暫時載入至表格 {0}。
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=已暫時將資料更改載入至表格 {0}。
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=已處理資料更改，並從表格 {0} 刪除。

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=連線明細。

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=正在開始最佳化本端表格 (檔案)。
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=最佳化本端表格 (檔案) 時發生錯誤。
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=發生錯誤，已停止最佳化本端表格 (檔案)。
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=正在最佳化本端表格 (檔案)...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=已最佳化本端表格 (檔案)。
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=已最佳化本端表格 (檔案)。
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=表格已使用 Z 排序欄來最佳化：{0}。

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=發生錯誤，已停止截斷本端表格 (檔案)。
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=正在截斷本端表格 (檔案)...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=已清除本端表格 (檔案) 的內傳緩衝位置。

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=正在開始清空 (刪除所有已完全處理的記錄) 本端表格 (檔案)。
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=清空本端表格 (檔案) 時發生錯誤。
#XMSG: Task log message
LTF_VACUUM_STOPPED=發生錯誤，已停止清空本端表格 (檔案)。
#XMSG: Task log message
LTF_VACUUM_PROCESSING=正在清空本端表格 (檔案)...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=清空完成。
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=正在刪除所有晚於 {0} 天的完全處理記錄。
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=正在刪除所有晚於 {0} 的完全處理記錄。

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=正在開始合併新記錄與本端表格 (檔案)。
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=正在開始使用設定「自動合併資料」，合併新記錄與本端表格 (檔案)。
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=正在開始合併新記錄與本端表格 (檔案)。此工作細項已透過 API 合併請求所初始。
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=正在合併新記錄與本端表格 (檔案)。
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=合併新記錄與本端表格 (檔案) 時發生錯誤。
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=已合併本端表格 (檔案)。
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=發生錯誤，已停止合併本端表格 (檔案)。
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=本端表格 (檔案) 的合併因為發生錯誤而失敗，但作業已部份成功且部份資料已合併。
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=發生逾時錯誤。活動 {0} 已執行 {1} 小時。
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=由於高系統負載，無法開始非同步執行。請開啟「系統監控器」並檢查執行中工作細項。
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=已取消非同步執行。
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION={0} 工作細項於 {1} 和 {2} 的記憶體限制內執行。
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID={0} 工作細項已使用資源 ID {1} 執行。

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=已在本端表格 (檔案) 中開始搜尋和取代。
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=已在本端表格 (檔案) 中完成搜尋和取代。
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=本端表格 (檔案) 中的搜尋和取代失敗。

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=發生錯誤，已停止更新本端表格 (檔案) 的統計。

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=由於 SAP HANA 資料庫發生記憶體不足錯誤，因此工作細項失敗。
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=由於 SAP HANA 許可控制拒絕，因此工作細項失敗。
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=由於啟用中 SAP HANA 連線過多，因此工作細項失敗。

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=由於僅允許在巢狀工作細項鏈上層重試，因此無法執行重試作業。
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=失敗的下層工作細項日誌無法再用於重試以繼續。


####Metrics Labels

performanceOptimized=效能最佳化
memoryOptimized=記憶體最佳化

JOB_EXECUTION=工作執行
EXECUTION_MODE=執行模式
NUMBER_OF_RECORDS_OVERALL=已保存記錄數量
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=從遠端來源讀取的記錄數量
RUNTIME_MS_REMOTE_EXECUTION_TIME=遠端來源處理時間
MEMORY_CONSUMPTION_GIB=SAP HANA 尖峰記憶體
NUMBER_OF_PARTITIONS=分割數量
MEMORY_CONSUMPTION_GIB_OVERALL=SAP HANA 尖峰記憶體
NUMBER_OF_PARTITIONS_LOCKED=鎖定分割數量
PARTITIONING_COLUMN=分割欄
HANA_PEAK_CPU_TIME=SAP HANA 總計 CPU 時間
USED_IN_DISK=使用的儲存
INPUT_PARAMETER_PARAMETER_VALUE=輸入參數
INPUT_PARAMETER=輸入參數
ECN_ID=彈性計算節點名稱

DAC=資料存取控制
YES=是
NO=否
noofrecords=記錄數量
partitionpeakmemory=SAP HANA 尖峰記憶體
value=值
metricsTitle=公制 ({0})
partitionmetricsTitle=分割 ({0})
partitionLabel=分割
OthersNotNull=範圍中未包含值
OthersNull=Null 值
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=已用於最後一次資料存續性執行的設定：
#XMSG: Message for input parameter name
inputParameterLabel=輸入參數
#XMSG: Message for input parameter value
inputParameterValueLabel=值
#XMSG: Message for persisted data
inputParameterPersistedLabel=保存時間
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=刪除資料
REMOVE_DELETED_RECORDS=移除刪除的記錄
MERGE_FILES=合併檔案
OPTIMIZE_FILES=最佳化檔案
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=於 SAP BW 橋接監控器中檢視

ANALYZE_PERFORMANCE=分析效能
CANCEL_ANALYZE_PERFORMANCE=取消分析效能

#XFLD: Label for frequency column
everyLabel=每
#XFLD: Plural Recurrence text for Hour
hoursLabel=小時
#XFLD: Plural Recurrence text for Day
daysLabel=天
#XFLD: Plural Recurrence text for Month
monthsLabel=個月
#XFLD: Plural Recurrence text for Minutes
minutesLabel=分鐘

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">檢視存續性解決問題指南</a>
#XTXT TEXT for view persistency guide link
OOMMessage=處理記憶體不足。無法保存檢視 "{0}" 的資料。請諮詢 Help Portal 瞭解更多關於記憶體不足錯誤的資訊。請考量查看「檢視分析工具」，分析記憶體使用複雜性的檢視。

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=不適用
OPEN_BRACKET=(
CLOSE_BRACKET=)
