
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Günlük ayrıntıları
#XFLD: Header
TASK_LOGS=<PERSON><PERSON><PERSON><PERSON>ü<PERSON> ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Çalıştırmalar ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Ayrıntıları görüntüle
#XFLD: Button text
STOP=Çalıştırmayı durdur
#XFLD: Label text
RUN_START=Son çalıştırma başlangıcı
#XFLD: Label text
RUN_END=Son çalıştırma bitişi
#XFLD: Label text
RUNTIME=Süre
#XTIT: Count for Messages
txtDetailMessages=İletiler ({0})
#XFLD: Label text
TIME=Zaman damgası
#XFLD: Label text
MESSAGE=İleti
#XFLD: Label text
TASK_STATUS=Kategori
#XFLD: Label text
TASK_ACTIVITY=Aktivite
#XFLD: Label text
RUN_START_DETAILS=Başlangıç
#XFLD: Label text
RUN_END_DETAILS=Bitiş
#XFLD: Label text
LOGS=Çalıştırmalar
#XFLD: Label text
STATUS=Durum
#XFLD: Label text
RUN_STATUS=Çalıştırma durumu
#XFLD: Label text
Runtime=Süre
#XFLD: Label text
RuntimeTooltip=Süre (SS : dd : ss)
#XFLD: Label text
TRIGGEREDBY=Tetikleyen
#XFLD: Label text
TRIGGEREDBYNew=Çalıştıran
#XFLD: Label text
TRIGGEREDBYNewImp=Çalıştırmayı başlatan
#XFLD: Label text
EXECUTIONTYPE=Yürütme türü
#XFLD: Label text
EXECUTIONTYPENew=Çalıştırma türü
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Üst zincir alanı
#XFLD: Refresh tooltip
TEXT_REFRESH=Yenile
#XFLD: view Details link
VIEW_ERROR_DETAILS=Ayrıntıları görüntüle
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Ek ayrıntıları indir
#XMSG: Download completed
downloadStarted=İndirme başlatıldı
#XMSG: Error while downloading content
errorInDownload=İndirme sırasında hata oluştu.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Ayrıntıları görüntüle
#XBTN: cancel button of task details dialog
TXT_CANCEL=İptal
#XBTN: back button from task details
TXT_BACK=Geri
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Görev tamamlandı
#XFLD: Log message with failed status
MSG_LOG_FAILED=Görev başarısız oldu
#XFLD: Master and detail table with no data
No_Data=Veri yok
#XFLD: Retry tooltip
TEXT_RETRY=Yeniden dene
#XFLD: Cancel Run label
TEXT_CancelRun=Çalıştırmayı iptal et
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Temizleme yüklenemedi
#XMSG:button copy sql statement
txtSQLStatement=SQL deyimini kopyala
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Uzak sorgu izlemeyi aç
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=Uzak SQL deyimlerini görüntülemek için "Uzak sorgu izlemeyi aç" seçeneğine tıklayın.
#XMSG:button ok
txtOk=Tamam
#XMSG: button close
txtClose=Kapat
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=Nesne "{0}" için çalıştırmayı iptal etme işlemi başlatıldı.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=Nesne "{0}" için çalıştırmayı iptal etme işlemi başarısız oldu.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=Nesne "{0}" için çalıştırmayı iptal etme işlemi, çoğaltma durumu değiştirildiğinden artık olanaklı değil.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Çalışıyor durumuna sahip görev günlüğü yok.
#XMSG: message for conflicting task
Task_Already_Running=Nesne "{0}" için çakışan bir görev zaten çalışıyor.
#XFLD: Label for no task log with running state title
actionInfo=İşlem bilgileri
#XMSG Copied to clipboard
copiedToClip=Panoya kopyalandı
#XFLD copy
Copy=Kopyala
#XFLD copy correlation ID
CopyCorrelationID=Korelasyon tanıtıcısını kopyala
#XFLD Close
Close=Kapat
#XFLD: show more Label
txtShowMore=Daha fazla göster
#XFLD: message Label
messageLabel=İleti:
#XFLD: details Label
detailsLabel=Ayrıntılar:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Yürütme SQL \r\n deyimi:
#XFLD:statementId Label
statementIdLabel=Deyim tanıtıcısı:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Uzak \r\n SQL deyimi sayısı:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Deyim sayısı
#XFLD: Space Label
txtSpaces=Alan
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Yetkisiz alanlar
#XFLD: Privilege Error Text
txtPrivilegeError=Bu verileri görüntülemek için yeterli ayrıcalıklarınız yok.
#XFLD: Label for Object Header
DATA_ACCESS=Veri erişimi
#XFLD: Label for Object Header
SCHEDULE=Planlama
#XFLD: Label for Object Header
DETAILS=Ayrıntılar
#XFLD: Label for Object Header
LATEST_UPDATE=Son güncelleme
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Son değişiklik (kaynak)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Yenileme sıklığı
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Planlanan sıklık
#XFLD: Label for Object Header
NEXT_RUN=Sonraki çalıştırma
#XFLD: Label for Object Header
CONNECTION=Bağlantı
#XFLD: Label for Object Header
DP_AGENT=Veri sağlama aracısı
#XFLD: Label for Object Header
USED_IN_MEMORY=Depolama için kullanılan bellek (MiB)
#XFLD: Label for Object Header
USED_DISK=Depolama için kullanılan disk (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Bellek içi boyut (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Diskteki boyut (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Kayıt sayısı

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Başarısız oldu olarak belirle
SET_TO_FAILED_ERR=Bu görev çalıştırılıyordu ancak kullanıcı bu görevin durumunu BAŞARISIZ olarak belirledi.
#XFLD: Label for stopped failed
FAILLOCKED=Çalıştırma zaten devam ediyor
#XFLD: sub status STOPPED
STOPPED=Durduruldu
STOPPED_ERR=Bu görev durduruldu ancak geri alma yürütülmedi.
#XFLD: sub status CANCELLED
CANCELLED=İptal edildi
CANCELLED_ERR=Bu görev çalıştırması, başlatıldıktan sonra iptal edildi. Bu durumda, veriler geri alındı ve görev çalıştırmasının başlangıç tetiklenmesinden önce mevcut olan durumuna geri yüklendi.
#XFLD: sub status LOCKED
LOCKED=Kilitli
LOCKED_ERR=Aynı görev zaten çalıştırılıyor, bu nedenle bu görev işi, mevcut işin yürütülmesiyle paralel olarak çalıştırılamaz.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Görev istisnası
TASK_EXCEPTION_ERR=Bu görev, yürütme sırasında belirtilmeyen bir hata ile karşılaştı.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Görev yürütme istisnası
TASK_EXECUTE_EXCEPTION_ERR=Bu görev, yürütme sırasında bir hata ile karşılaştı.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Yetkisiz
UNAUTHORIZED_ERR=Kullanıcı kimliği doğrulanamadı, kilitlendi veya silindi.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Yasak
FORBIDDEN_ERR=Tayin edilen kullanıcı bu görevi yürütmek için gerekli önceliğe sahip değil.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Tetiklenmedi
FAIL_NOT_TRIGGERED_ERR=Bu görev işi, sistem kesintisi veya veri tabanı sisteminin planlanan yürütme saatinde kullanılabilir olmaması nedeniyle yürütülemedi. Sonraki planlanan iş yürütme saati için bekleyin veya işi yeniden planlayın.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Planlama iptal edildi
SCHEDULE_CANCELLED_ERR=Bu görev işi, dahili hata nedeniyle yürütülemedi. SAP desteğiyle irtibata geçin ve onlara bu görev işinin günlük ayrıntısı bilgilerinden korelasyon tanıtıcısı ve zaman damgasını iletin.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Önceki çalıştırma devam ediyor
SUCCESS_SKIPPED_ERR=Aynı görevin önceki çalıştırması hâlâ devam ettiğinden bu görevi yürütme tetiklenmedi.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Sorumlu eksik
FAIL_OWNER_MISSING_ERR=Bu görev işine tayin edilen bir sistem kullanıcısı olmadığından görev işi yürütülemedi. İşe bir sorumlu kullanıcı tayin edin.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=İzin kullanılabilir değil
FAIL_CONSENT_NOT_AVAILABLE_ERR=Adınıza görev zincirleri yürütmek veya veri entegrasyonu görevleri planlamak için yetki SAP'ye sahip değilsiniz. İzinizi vermek için sağlanan opsiyonu seçin.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=İznin süresi doldu
FAIL_CONSENT_EXPIRED_ERR=Adınıza görev zincirleri yürütmek veya veri entegrasyonu görevleri planlamaya izin veren yetkinin süresi doldu. İzninizi yenilemek için sağlanan opsiyonu seçin.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=İzin geçersiz kılındı
FAIL_CONSENT_INVALIDATED_ERR=Bu görev, tipik olarak kiracının Kimlik Sağlayıcı konfigürasyonundaki bir değişiklik nedeniyle yürütülemedi. Bu durumda, etkilenen kullanıcı adına yeni görev işi çalıştırılamaz veya planlanamaz. Tayin edilen kullanıcı hâlâ yeni IdP içinde mevcutsa, planlama izini iptal edin ve yeniden verin. Tayin edilen kullanıcı artık yoksa yeni bir görev işi sorumlusu tayin edin ve gerekli görev planlama iznini sağlayın. Aşağıdaki SAP notuna bakın: Daha fazla bilgi için bkz. https://launchpad.support.sap.com/#/notes/3089828.
TASK_EXECUTOR_ERROR=Görev yürütücü
TASK_EXECUTOR_ERROR_ERR=Bu görev, dahili hata ile karşılaştı. Büyük ihtimalle yürütmeye yönelik hazırlama adımları sırasında ve görev başlatılamadı.
PREREQ_NOT_MET=Önkoşul karşılanmadı
PREREQ_NOT_MET_ERR=Bu görev, tanımındaki sorunlar nedeniyle yürütülemiyor. Sorunlara örnek olarak, bir nesnenin dağıtılmaması, bir görev zincirinin döngüsel mantık içermesi veya bir görünümün SQL'inin geçersiz olması verilebilir.
RESOURCE_LIMIT_ERROR=Kaynak sınırı hatası
RESOURCE_LIMIT_ERROR_ERR=Yeterli kaynak bulunmadığından veya meşgul olduğundan şu anda görev gerçekleştirilemiyor.
FAIL_CONSENT_REFUSED_BY_UMS=İzin reddedildi
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Kiracıda bir kullanıcının kimlik sağlayıcı konfigürasyonundaki bir değişiklik nedeniyle planlanan çalıştırmalarda veya görev zincirlerinde bu görev yürütülemedi. Daha fazla bilgi için şu SAP notuna bakın: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Planlandı
#XFLD: status text
SCHEDULEDNew=Sürekli
#XFLD: status text
PAUSED=Duraklatıldı
#XFLD: status text
DIRECT=Doğrudan
#XFLD: status text
MANUAL=Manüel
#XFLD: status text
DIRECTNew=Basit
#XFLD: status text
COMPLETED=Tamamlandı
#XFLD: status text
FAILED=Başarısız oldu
#XFLD: status text
RUNNING=Çalışıyor
#XFLD: status text
none=Hiçbiri
#XFLD: status text
realtime=Gerçek zamanlı
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Alt görev
#XFLD: New Data available in the file
NEW_DATA=Yeni veriler

#XFLD: text for values shown in column Replication Status
txtOff=Kapalı
#XFLD: text for values shown in column Replication Status
txtInitializing=Başlatılıyor
#XFLD: text for values shown in column Replication Status
txtLoading=Yükleniyor
#XFLD: text for values shown in column Replication Status
txtActive=Etkin
#XFLD: text for values shown in column Replication Status
txtAvailable=Kullanılabilir
#XFLD: text for values shown in column Replication Status
txtError=Hata
#XFLD: text for values shown in column Replication Status
txtPaused=Duraklatıldı
#XFLD: text for values shown in column Replication Status
txtDisconnected=Bağlantı kesildi
#XFLD: text for partially Persisted views
partiallyPersisted=Kısmen kalıcı

#XFLD: activity text
REPLICATE=Çoğalt
#XFLD: activity text
REMOVE_REPLICATED_DATA=Çoğaltılan verileri kaldır
#XFLD: activity text
DISABLE_REALTIME=Gerçek zamanlı veri çoğaltmayı devre dışı bırak
#XFLD: activity text
REMOVE_PERSISTED_DATA=Kalıcı verileri kaldır
#XFLD: activity text
PERSIST=Kalıcı
#XFLD: activity text
EXECUTE=Yürüt
#XFLD: activity text
TASKLOG_CLEANUP=İzleme_günlüğü_temizliği
#XFLD: activity text
CANCEL_REPLICATION=Çoğaltmayı iptal et
#XFLD: activity text
MODEL_IMPORT=Model içe aktarımı
#XFLD: activity text
NONE=Hiçbiri
#XFLD: activity text
CANCEL_PERSISTENCY=Kalıcılığı iptal et
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Görünümü analiz et
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Görünüm analistini iptal et

#XFLD: severity text
INFORMATION=Bilgiler
#XFLD: severity text
SUCCESS=Başarılı
#XFLD: severity text
WARNING=Uyarı
#XFLD: severity text
ERROR=Hata
#XFLD: text for values shown for Ascending sort order
SortInAsc=Artan düzende sırala
#XFLD: text for values shown for Descending sort order
SortInDesc=Azalan düzende sırala
#XFLD: filter text for task log columns
Filter=Filtrele
#XFLD: object text for task log columns
Object=Nesne
#XFLD: space text for task log columns
crossSpace=Alan

#XBUT: label for remote data access
REMOTE=Uzak
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Çoğaltıldı (gerçek zamanlı)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Çoğaltıldı (anlık görüntü)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=Gerçek zamanlı çoğaltma hata nedeniyle bloke edildi. Hata düzeltildiğinde gerçek zamanlı çoğaltmaya devam etmek için "Yeniden Dene" işlemini kullanabilirsiniz.
ERROR_MSG=Gerçek zamanlı çoğaltma hata nedeniyle bloke edildi.
RETRY_FAILED_ERROR=Yeniden deneme süreci hata ile başarısız oldu.
LOG_INFO_DETAILS=Veriler gerçek zamanlı modda çoğaltıldığında günlük oluşturulmaz. Görüntülenen günlükler önceki işlemlerle ilişkilidir.

#XBUT: Partitioning label
partitionMenuText=Bölümleme
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Bölümleme oluştur
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Bölümlemeyi düzenle
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Bölümlemeyi sil
#XFLD: Initial text
InitialPartitionText=Büyük veri kümelerini daha küçük kümelere bölmek için ölçütler belirterek bölümlemeler tanımlayın.
DefinePartition=Bölümlemeler tanımla
#XFLD: Message text
partitionChangedInfo=Bölümleme tanımı son çoğaltmadan sonra değişti. Değişiklikler sonraki veri yüklemesinde uygulanacak.
#XFLD: Message text
REAL_TIME_WARNING=Bölümleme yalnızca yeni anlık görüntü yüklenirken uygulanır. Gerçek zamanlı çoğaltma için uygulanmaz.
#XFLD: Message text
loadSelectedPartitions="{0}" için seçilen bölümlemelere ilişkin verileri kalıcı hale getirme işlemi başlatıldı
#XFLD: Message text
loadSelectedPartitionsError="{0}" için seçilen bölümlemelere ilişkin veriler kalıcı hale getirilemedi
#XFLD: Message text
viewpartitionChangedInfo=Son kalıcılık çalıştırmasından beri bölümleme tanımı değiştirildi. Değişiklikleri uygulamak amacıyla sonraki veri yüklemesi, kilitli bölümlemeleri de içeren tam bir anlık görüntü olacak. Bu tam yükleme tamamlandığında tek bölümlemeler için verileri çalıştırabileceksiniz.
#XFLD: Message text
viewpartitionChangedInfoLocked=Son kalıcılık çalıştırması sonrasında bölümleme tanımı değiştirildi. Değişikliklerin uygulanması için, sonraki veri yüklemesi, kilitli ve değiştirilmemiş bölümleme aralıkları hariç olmak üzere tam bir anlık görüntü olacak. Bu yükleme tamamlandığında, tekrar seçilen bölümlemeleri yükleyebileceksiniz.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Tablo çoğaltma
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Çoğaltmayı planla
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Planlama oluştur
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Planlamayı düzenle
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Planlamayı sil
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Yeni anlık görüntü yükle
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Veri çoğaltmayı başlat
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Çoğaltılan verileri kaldır
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Gerçek zamanlı erişimi etkinleştir
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Gerçek zamanlı veri çoğaltmayı etkinleştir
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Gerçek zamanlı veri çoğaltmayı devre dışı bırak
#XFLD: Message for replicate table action
replicateTableText=Tablo çoğaltma
#XFLD: Message for replicate table action
replicateTableTextNew=Veri çoğaltma
#XFLD: Message to schedule task
scheduleText=Planlama
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Görünüm kalıcılığı
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Veri kalıcılığı
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Yeni anlık görüntü yükle
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Veri kalıcılığını başlat
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Kalıcı verileri kaldır
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Bölümlenen işleme
#XBUT: Label for scheduled replication
scheduledTxt=Planlandı
#XBUT: Label for statistics button
statisticsTxt=İstatistik
#XBUT: Label for create statistics
createStatsTxt=İstatistik oluştur
#XBUT: Label for edit statistics
editStatsTxt=İstatistiği düzenle
#XBUT: Label for refresh statistics
refreshStatsTxt=İstatistiği yenile
#XBUT: Label for delete statistics
dropStatsTxt=İstatistiği sil
#XMSG: Create statistics success message
statsSuccessTxt={1} için {0} türünde istatistik oluşturmaya başlandı.
#XMSG: Edit statistics success message
statsEditSuccessTxt={1} için istatistik türü {0} olarak değiştirilmeye başlandı.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt={0} için istatistik yenilenmeye başland.
#XMSG: Drop statistics success message
statsDropSuccessTxt={0} için istatistik başarıyla silindi
#XMSG: Create statistics error message
statsCreateErrorTxt=İstatistik oluşturulurken hata
#XMSG: Edit statistics error message
statsEditErrorTxt=İstatistik değiştirilirken hata
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=İstatistik yenilenirken hata
#XMSG: Drop statistics error message
statsDropErrorTxt=İstatistik silinirken hata
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Veri istatistiğini silmek istediğinizden emin misiniz?
startPersistencyAdvisorLabel=Görünüm analistini başlat

#Partition related texts
#XFLD: Label for Column
column=Sütun
#XFLD: Label for No of Partition
noOfPartitions=Bölümleme sayısı
#XFLD: Label for Column
noOfParallelProcess=Paralel süreç sayısı
#XFLD: Label text
noOfLockedPartition=Kilitli bölümleme sayısı
#XFLD: Label for Partition
PARTITION=Bölümlemeler
#XFLD: Label for Column
AVAILABLE=Kullanılabilir
#XFLD: Statistics Label
statsLabel=İstatistik
#XFLD: Label text
COLUMN=Sütun:
#XFLD: Label text
PARALLEL_PROCESSES=Paralel süreçler:
#XFLD: Label text
Partition_Range=Bölümleme aralığı
#XFLD: Label text
Name=Ad
#XFLD: Label text
Locked=Kilitli
#XFLD: Label text
Others=DİĞERLERİ
#XFLD: Label text
Delete=Sil
#XFLD: Label text
LoadData=Seçilen bölümlemeleri yükle
#XFLD: Label text
LoadSelectedData=Seçilen bölümlemeleri yükle
#XFLD: Confirmation text
LoadNewPersistenceConfirm=Bu, yalnızca seçtikleriniz için değil, tüm kilidi açılan ve değiştirilen bölümlemeler için yeni anlık görüntü yükleyecek. Devam etmek istiyor musunuz?
#XFLD: Label text
Continue=Devam et

#XFLD: Label text
PARTITIONS=Bölümlemeler
#XFLD: Label text
ADD_PARTITIONS=+ Bölümleme ekle
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Bölümleme ekle
#XFLD: Label text
deleteRange=Bölümlemeyi sil
#XFLD: Label text
LOW_PLACE_HOLDER=Düşük değer gir
#XFLD: Label text
HIGH_PLACE_HOLDER=Yüksek değer gir
#XFLD: tooltip text
lockedTooltip=İlk yükleme sonrasında bölümlemeyi kilitle

#XFLD: Button text
Edit=Düzenle
#XFLD: Button text
CANCEL=İptal

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Son istastistiklerin güncellemesi
#XFLD: Statistics Fields
STATISTICS=İstatistik

#XFLD:Retry label
TEXT_Retry=Yeniden dene
#XFLD:Retry label
TEXT_Retry_tooltip=Hata çözüldükten sonra gerçek zamanlı çoğaltmayı yeniden deneyin.
#XFLD: text retry
Retry=Teyit et
#XMG: Retry confirmation text
retryConfirmationTxt=Son gerçek zamanlı çoğaltma hata ile sonlandırıldı.\n Hatanın düzeltildiğini ve gerçek zamanlı çoğaltmanın yeniden başlatılacağını teyit edin.
#XMG: Retry success text
retrySuccess=Yeniden deneme süreci başarıyla başlatıldı.
#XMG: Retry fail text
retryFail=Yeniden deneme süreci başarısız oldu.
#XMSG: activity message for create statistics
CREATE_STATISTICS=İstatistik oluştur
#XMSG: activity message for edit statistics
DROP_STATISTICS=İstatistiği sil
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=İstatistiği yenile
#XMSG: activity message for edit statistics
ALTER_STATISTICS=İstatistiği düzenle
#XMSG: Task log message started task
taskStarted=Görev {0} başlatıldı.
#XMSG: Task log message for finished task
taskFinished=Görev {0}, {1} durumu ile sonlandırıldı.
#XMSG: Task log message for finished task with end time
taskFinishedAt=Görev {0}, {2} saatinde {1} durumu ile sonlandırıldı.
#XMSG: Task {0} has input parameters
taskHasInputParameters={0} görevinde girdi parametreleri var.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=Görev {0}, beklenmeyen hata ile sonlandırıldı. Görev durumu {1} olarak belirlendi.
#XMSG: Task log message for failed task
failedToEnd=Durum {0} olarak belirlenemedi veya kilit kaldırılamadı.
#XMSG: Task log message
lockNotFound=Kilit eksik olduğu için süreç sonlandırılamadı: Görev iptal edilmiş olabilir.
#XMSG: Task log message failed task
failedOverwrite=Görev {0} zaten {1} tarafından kilitlendi. Bu nedenle şu hata ile başarısız oldu: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Bu görevi kilitleme başka bir görev tarafından devralındı.
#XMSG: Task log message failed takeover
failedTakeover=Mevcut görev devralınamadı.
#XMSG: Task log message successful takeover
successTakeover=Bırakılan kilidin açılması gerekiyordu. Bu görev için yeni kilit belirlendi.
#XMSG: Tasklog Dialog Details
txtDetails=Çalıştırma sırasında işlenen uzak deyimler, uzak sorgu izleme açılarak (bölümlemeye özgü iletilerin ayrıntılarında) görüntülenebilir.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=Uzak SQL deyimleri, veri tabanından silinmiş ve görüntülenemiyor.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Başka alanlara tayin edilmiş bağlantıları bulunan uzak sorgular görüntülenemez. Bunları filtrelemek için uzak sorgu izleme aracına gidin ve deyim tanıtıcısını kullanın.
#XMSG: Task log message for parallel check error
parallelCheckError=Bu görevi zaten kilitleyen başka bir görev çalışmakta olduğu için görev işlenemiyor.
#XMSG: Task log message for parallel running task
parallelTaskRunning=Çakışan bir görev zaten çalışıyor.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Çalışma sırasında korelasyon tanıtıcısı {1} ile durum {0}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=Tayin edilen kullanıcı bu görevi yürütmek için gerekli önceliğe sahip değil.

#XBUT: Label for open in Editor
openInEditor=Düzenleyicide aç
#XBUT: Label for open in Editor
openInEditorNew=Veri oluşturucuda aç
#XFLD:Run deails label
runDetails=Çalıştırma ayrıntıları
#XFLD: Label for Logs
Logs=Günlükler
#XFLD: Label for Settings
Settings=Ayarlar
#XFLD: Label for Save button
Save=Kaydet
#XFLD: Label for Standard
Standard_PO=Performansı optimize edilmiş (önerilen)
#XFLD: Label for Hana low memory processing
HLMP_MO=Belleği optimize edilmiş
#XFLD: Label for execution mode
ExecutionMode=Çalıştırma modu
#XFLD: Label for job execution
jobExecution=İşleme modu
#XFLD: Label for Synchronous
syncExec=Senkron
#XFLD: Label for Asynchronous
asyncExec=Asenkron
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Varsayılanı kullan (asenkron, gelecekte değiştirilebilir)
#XMSG: Save settings success
saveSettingsSuccess=SAP HANA yürütme modu değiştirildi.
#XMSG: Save settings failure
saveSettingsFailed=SAP HANA yürütme modu değiştirilemedi.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=İş yürütme değiştirildi.
#XMSG: Job Execution change failed
jobExecSettingFailed=İş yürütme değiştirilemedi.
#XMSG: Text for Type
typeTxt=Tür
#XMSG: Text for Monitor
monitorTxt=İzleme
#XMSG: Text for activity
activityTxt=Aktivite
#XMSG: Text for metrics
metricsTxt=Metrikler
#XTXT: Text for Task chain key
TASK_CHAINS=Görev zinciri
#XTXT: Text for View Key
VIEWS=Görünüm
#XTXT: Text for remote table key
REMOTE_TABLES=Uzak tablo
#XTXT: Text for Space key
SPACE=Alan
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Esnek işlem düğümü
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Çoğaltma akışı
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Akıllı arama
#XTXT: Text for Local Table
LOCAL_TABLE=Yerel tablo
#XTXT: Text for Data flow key
DATA_FLOWS=Veri akışı
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=SQL komut dosyası prosedürü
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=BW süreç zinciri
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=İzlemede görüntüle
#XTXT: Task List header text
taskListHeader=Görev listesi ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Veri akışının geçmiş çalıştırmaları için metrikler alınamıyor.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=Tüm çalıştırma ayrıntısı şu anda yüklenemiyor. Yenilemeyi deneyin.
#XFLD: Label text for the Metrices table header
metricesColLabel=İşleç etiketi
#XFLD: Label text for the Metrices table header
metricesType=Tür
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Kayıt sayısı
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Görev zincirini çalıştır
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Görev zinciri çalıştırması başlatıldı.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg={0} için görev zinciri çalıştırması başlatıldı
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Görev zinciri çalıştırılamadı.
#XTXT: Execute button label
runLabel=Çalıştır
#XTXT: Execute button label
runLabelNew=Çalıştırmayı başlat
#XMSG: Filter Object header
chainsFilteredTableHeader=Nesneye göre filtrelendi: {0}
#XFLD: Parent task chain label
parentChainLabel=Üst görev zinciri:
#XFLD: Parent task chain unauthorized
Unauthorized=Görüntülemek için yetki yok
#XFLD: Parent task chain label
parentTaskLabel=Üst görev:
#XTXT: Task status
NOT_TRIGGERED=Tetiklenmedi
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Tam ekran moduna gir
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Tam ekran modundan çık
#XTXT: Close Task log details right panel
closeRightColumn=Bölümü kapat
#XTXT: Sort Text
sortTxt=Sırala
#XTXT: Filter Text
filterTxt=Filtrele
#XTXT: Filter by text to show list of filters applied
filterByTxt=Filtreleme ölçütü
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=5 dakikadan fazla
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=15 dakikadan fazla
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=1 saatten fazla
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Son saat
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Son 24 saat
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Son ay
#XTXT: Messages title text
messagesText=Mesajlar

#XTXT Statistics information message
statisticsInfo="Çoğaltıldı" veri erişimine sahip uzak tablolar için istatistik oluşturulamıyor. İstatistik oluşturmak üzere, uzak tablo ayrıntılarını izleme kapsamında çoğaltılan verileri kaldırın.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Uzak tablo ayrıntılarını izlemeye git

#XTXT: Repair latest failed run label
retryRunLabel=Son çalıştırmayı yeniden dene
#XTXT: Repair failed run label
retryRun=Çalıştırmayı yeniden dene
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Görev zinciri yeniden deneme çalıştırması başlatıldı
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Görev zinciri yeniden deneme çalıştırması başarısız oldu
#XMSG: Task chain child elements name
taskChainRetryChildObject={0} görevi
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Yeni görev
#XFLD Analyzed View
analyzedView=Analiz edilen görünüm
#XFLD Metrics
Metrics=Metrikler
#XFLD Partition Metrics
PartitionMetrics=Bölümleme metrikleri
#XFLD Entities
Messages=Görev günlüğü
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Henüz hiç bölümleme tanımlanmadı
#XTXT: Description message for empty partition data
partitionEmptyDescText=Büyük veri hacimlerini daha küçük ve daha yönetilebilir bölümlere ayırmak üzere ölçütler belirterek bölümlemeler oluşturun.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Henüz mevcut günlük yok
#XTXT: Description message for empty runs data
runsEmptyDescText=Yeni bir aktiviteye başladığınızda (yeni anlık görüntü yükleme, görünüm analistini başlatma...) ilgili günlükleri burada bulabilirsiniz.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=Esnek işlem düğümü {0} konfigürasyonu uygun şekilde yapılmamış. Tanımınızı kontrol edin.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Esnek işlem düğümü {0} ekleme süreci başarısız oldu.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED={2} esnek işlem düğümünde "{0}"."{1}" kaynak nesnesinin çoğaltmasını oluşturma ve etkinleştirme işlemi başlatıldı.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED="{0}"."{1}" kaynak nesnesinin çoğaltmasını {2} esnek işlem düğümünden kaldırma işlemi başlatıldı.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED="{0}"."{1}" kaynak nesnesinin çoğaltmasını oluşturma ve etkinleştirme işlemi başarısız oldu.
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED="{0}"."{1}" kaynak nesnesinin çoğaltması kaldırılamadı.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED={0} alanının analitik sorgu hesaplamasını {1} esnek işlem düğümüne yönlendirme başladı.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED={0} alanının analitik sorgu hesaplamasını karşılık gelen esnek işlem düğümüne yönlendirme başarısız oldu.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED={0} alanının analitik sorgu hesaplamasını {1} esnek işlem düğümünden yeniden yönlendirme başladı.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED={0} alanının analitik sorgu hesaplamasını koordinatöre yeniden yönlendirme başarısız oldu.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Karşılık gelen {1} esnek işlem düğümüne görev zinciri {0} tetiklendi.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED={0} esnek işlem düğümünün görev zinciri oluşturulamadı.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=Belirtilen büyüklük belirleme planıyla {0} esnek işlem düğümünü sağlama işlemi başlatıldı: vCPU''lar: {1}, bellek (GiB): {2} ve depolama boyutu (GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED={0} esnek işlem düğümü zaten tedarik edilmiş.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED={0} esnek işlem düğümünü tedarik işlemi zaten kaldırılmış.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=İşleme izin verilmiyor. {0} esnek işlem düğümünü durdurun ve büyüklük belirleme planını güncellemek için yeniden başlatın.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED={0} esnek işlem düğümünü silme süreci başarısız oldu.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT={0} esnek işlem düğümünün geçerli çalıştırması zaman aşımına uğradı.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING={0} esnek işlem düğümünün durumunu kontrol etme işleniyor...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=Esnek işlem düğümü {0} için görev zinciri üretme bloke edildi, bu nedenle zincir {1} hâlâ çalışıyor olabilir.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH="{0}"."{1}" kaynak nesnesi, "{2}"."{3}" görünümünün bağlılığı olarak çoğaltıldı.
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Satır tablosunun çoğaltması kullanımdan kaldırıldığından "{0}"."{1}" tablosu çoğaltılamıyor.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=SAP HANA Cloud örneği başına azami esnek işlem düğmesi aşıldı.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=Esnek işlem düğümü {0} için çalıştırma işlemi hâlâ sürüyor.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Teknik sorunlar nedeniyle kaynak tablo {0} için çoğaltmanın silinmesi durduruldu. Daha sonra tekrar deneyin.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=Teknik sorunlar nedeniyle kaynak tablo {0} için çoğaltmanın oluşturulması durduruldu. Daha sonra tekrar deneyin.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Kullanım sınırına ulaşıldığından veya henüz işlem blok saati dağıtılmadığından esnek işlem düğümü başlatılamıyor.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Görev zinciri yükleniyor ve bu zincirin parçası olan toplam {0} görevi çalıştırmaya hazırlanılıyor.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Çakışan bir görev zaten çalışıyor
#XMSG: Replication will change
txt_replication_change=Çoğaltma türü değiştirilecek.
txt_repl_viewdetails=Ayrıntıları görüntüle

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Plan oluşturulmadan önce önceki görev çalıştırması başarısız olduğundan en son çalıştırmayı yeniden denemeye ilişkin hata oluşmuş gibi görünüyor.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=Alan "{0}" kilitlendi.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED={0} aktivitesi, {1} yerel tablosunun dağıtılmasını gerektiriyor.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE={0} aktivitesi, {1} yerel tablosu için Delta yakalamasının açılmasını gerektiriyor.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE={0} aktivitesi, {1} yerel tablosunun nesne deposunda veri depolamasını gerektiriyor.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE={0} aktivitesi, {1} yerel tablosunun veri tabanında veri depolamasını gerektiriyor.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Yerel tablo {0} için silinen kayıtları kaldırma işlemi başlatılıyor.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Yerel tablo {0} için tüm kayıtları silme işlemi başlatılıyor.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER={0} yerel tablosu için {1} filtre koşuluna göre kayıtlar silinmeye başlanıyor.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Yerel tablo {0} için silinen kayıtlar kaldırılmaya çalışılırken hata oluştu.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Yerel tablo {0} için tüm kayıtlar silinirken hata oluştu.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Değişiklik türü "Silindi" olan ve {0} günden eski olan, tamamen işlenmiş tüm kayıtlar siliniyor.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Değişiklik türü "Silindi" olan ve {0} süresinden eski olan, tamamen işlenmiş tüm kayıtlar siliniyor.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} kayıt silindi.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} kayıt silme için işaretlendi.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=Yerel tablo {0} için silinen kayıtların kaldırılması tamamlandı.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Yerel tablo {0} için tüm kayıtların silinmesi tamamlandı.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Yerel tablo {0} için kayıtları "Silindi" olarak işaretleme başlatılıyor.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER={0} yerel tablosu için {1} filtre koşuluna göre kayıtlar "Silindi" olarak işaretlenmeye başlanıyor.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Yerel tablo {0} için kayıtları "Silindi" olarak işaretleme tamamlandı.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Veri değişiklikleri, {0} tablosuna geçici olarak yükleniyor.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Veri değişiklikleri, {0} tablosuna geçici olarak yüklendi.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Veri değişiklikleri işlendi ve {0} tablosundan silindi.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Bağlantı ayrıntıları.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Yerel tablo (dosya) optimize edilmeye başlanıyor.
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Yerel tablo (dosya) optimize edilirken hata oluştu.
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Hata oluştu. Yerel tablo (dosya) optimizasyonu durduruldu.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Yerel tablo (dosya) optimize ediliyor...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=Yerel tablo (dosya) optimize edildi.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=Yerel tablo (dosya) optimize edildi.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=Tablo, Z-sırası sütunları ile optimize edildi: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Hata oluştu. Yerel tablo (dosya) için kesme işlemi durduruldu.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Yerel tablo (dosya) kesiliyor...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=Yerel tablo (dosya) için gelen arabellek lokasyonu bırakıldı.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Yerel tablo (dosya) temizlenmeye (tam olarak işlenmiş tüm kayıtların silinmesi) başlanıyor.
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Yerel tablo (dosya) temizlenirken hata oluştu.
#XMSG: Task log message
LTF_VACUUM_STOPPED=Hata oluştu. Yerel tablo (dosya) için temizleme işlemi durduruldu.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Yerel tablo (dosya) temizleniyor...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=Temizleme tamamlandı.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO={0} günden eski olan tamamen işlenmiş tüm kayıtlar siliniyor.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING={0} günden eski olan tamamen işlenmiş tüm kayıtlar siliniyor.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Yerel tablo (dosya) yeni kayıtlarla birleştirilmeye başlanıyor.
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING="Verileri otomatik olarak birleştir" ayarını kullanarak yeni kayıtlar yerel tablo (dosya) ile birleştirilmeye başlandı.
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Yerel tablo (dosya) ile yeni kayıtlar birleştirilmeye başlanıyor. Bu görev, API birleştirme talebi aracılığıyla başlatıldı.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Yeni kayıtlar yerel tablo (dosya) ile birleştiriliyor.
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Yeni kayıtlar yerel tablo (dosya) ile birleştirilirken hata oluştu.
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=Yerel tablo (dosya) birleştirildi.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Hata oluştu. Yerel tablo (dosya) için birleştirme işlemi durduruldu.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=Yerel tablo (dosya) birleştirme hata nedeniyle başarısız oldu ancak işlem kısmen başarılı oldu ve bazı veriler zaten birleştirilmiş.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Zaman aşımı hatası oluştu. Aktivite {0}, {1} saattir çalıştırılıyor.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=Asenkron yürütme yüksek sistem yükü nedeniyle başlatılamadı. ''Sistem izleme''yi açın ve çalıştırılmakta olan görevleri kontrol edin.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=Asenkron yürütme iptal edildi.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION={0} görevi, {1} ve {2} bellek sınırları dahilinde çalıştırıldı.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID={0} görevi, kaynak tanıtıcısı {1} ile çalıştırıldı.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=Bul ve değiştir görevi, Yerel tabloda (dosya) başlatıldı.
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Bul ve değiştir görevi, Yerel tabloda (dosya) tamamlandı.
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Bul ve değiştir görevi, Yerel tabloda (dosya) başarısız oldu.

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Hata oluştu. Yerel tablo (dosya) için istatistikleri güncelleme işlemi durduruldu.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=Görev, SAP HANA veri tabanındaki bir yetersiz bellek hatası nedeniyle başarısız oldu.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=Görev bir SAP HANA giriş kontrolü reddi nedeniyle başarısız oldu.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=Görev çok fazla sayıda etkin SAP HANA bağlantısı nedeniyle başarısız oldu.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=Yeniden denemelere yalnızca iç içe geçmiş görev zincirinin üst öğesinde izin verildiğinden Yeniden deneme işlemi gerçekleştirilemedi.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Başarısız alt görevlerin günlükleri, artık Devam etmek için yeniden dene amacıyla kullanılamıyor.


####Metrics Labels

performanceOptimized=Performansı optimize edilmiş
memoryOptimized=Belleği optimize edilmiş

JOB_EXECUTION=İş yürütme
EXECUTION_MODE=Çalıştırma modu
NUMBER_OF_RECORDS_OVERALL=Kalıcı hale getirilen kayıt sayısı
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Uzak kaynaktan okunan kayıt sayısı
RUNTIME_MS_REMOTE_EXECUTION_TIME=Uzak kaynak işleme süresi
MEMORY_CONSUMPTION_GIB=SAP HANA için azami bellek
NUMBER_OF_PARTITIONS=Bölümleme sayısı
MEMORY_CONSUMPTION_GIB_OVERALL=SAP HANA için azami bellek
NUMBER_OF_PARTITIONS_LOCKED=Kilitli bölümleme sayısı
PARTITIONING_COLUMN=Bölümleme sütunu
HANA_PEAK_CPU_TIME=SAP HANA toplam CPU zamanı
USED_IN_DISK=Kullanılan depolama
INPUT_PARAMETER_PARAMETER_VALUE=Girdi parametresi
INPUT_PARAMETER=Girdi parametresi
ECN_ID=Esnek işlem düğümü adı

DAC=Veri erişimi denetimleri
YES=Evet
NO=Hayır
noofrecords=Kayıt sayısı
partitionpeakmemory=SAP HANA için azami bellek
value=Değer
metricsTitle=Metrikler ({0})
partitionmetricsTitle=Bölümlemeler ({0})
partitionLabel=Bölümleme
OthersNotNull=Aralıklara dahil edilmeyen değerler
OthersNull=Null değerler
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Son veri kalıcılığı çalıştırması için kullanılan ayarlar:
#XMSG: Message for input parameter name
inputParameterLabel=Girdi parametresi
#XMSG: Message for input parameter value
inputParameterValueLabel=Değer
#XMSG: Message for persisted data
inputParameterPersistedLabel=Kalıcı hale getirilme zamanı
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Verileri sil
REMOVE_DELETED_RECORDS=Silinen kayıtları kaldır
MERGE_FILES=Dosyaları birleştir
OPTIMIZE_FILES=Dosyaları optimize et
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=SAP BW köprüsü izlemede görüntüle

ANALYZE_PERFORMANCE=Performansı analiz et
CANCEL_ANALYZE_PERFORMANCE=Performansı analiz etmeyi iptal et

#XFLD: Label for frequency column
everyLabel=Her
#XFLD: Plural Recurrence text for Hour
hoursLabel=Saat
#XFLD: Plural Recurrence text for Day
daysLabel=Gün
#XFLD: Plural Recurrence text for Month
monthsLabel=Ay
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Dakika

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Kalıcılık sorun giderme kılavuzunu görüntüle</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Süreç için yetersiz bellek. "{0}" görünümü için veriler kalıcı hale getirilemiyor. Yetersiz bellek hataları hakkında daha fazla bilgi için Yardım Portalına bakın. Bellek kullanımı karmaşıklığı için görünümü analiz etmek üzere Görünüm analistini kontrol etmeyi göz önünde bulundurun.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Uygun değil
OPEN_BRACKET=(
CLOSE_BRACKET=)
