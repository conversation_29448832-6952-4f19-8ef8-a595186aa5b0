
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Detalles de registro
#XFLD: Header
TASK_LOGS=Registros de tareas ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Ejecuciones ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Ver detalles
#XFLD: Button text
STOP=Detener ejecución
#XFLD: Label text
RUN_START=Inicio de última ejecución
#XFLD: Label text
RUN_END=Finalización de última ejecución
#XFLD: Label text
RUNTIME=Duración
#XTIT: Count for Messages
txtDetailMessages=Mensajes ({0})
#XFLD: Label text
TIME=Marca de tiempo
#XFLD: Label text
MESSAGE=Mensaje
#XFLD: Label text
TASK_STATUS=Categoría
#XFLD: Label text
TASK_ACTIVITY=Actividad
#XFLD: Label text
RUN_START_DETAILS=Inicio
#XFLD: Label text
RUN_END_DETAILS=Fin
#XFLD: Label text
LOGS=Ejecuciones
#XFLD: Label text
STATUS=Estado
#XFLD: Label text
RUN_STATUS=Estado de ejecución
#XFLD: Label text
Runtime=Duración
#XFLD: Label text
RuntimeTooltip=Duración (hh : mm : ss)
#XFLD: Label text
TRIGGEREDBY=Activado por
#XFLD: Label text
TRIGGEREDBYNew=Ejecutado por
#XFLD: Label text
TRIGGEREDBYNewImp=Ejecución iniciada por
#XFLD: Label text
EXECUTIONTYPE=Tipo de ejecución
#XFLD: Label text
EXECUTIONTYPENew=Tipo de ejecución
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Espacio de cadena principal
#XFLD: Refresh tooltip
TEXT_REFRESH=Actualizar
#XFLD: view Details link
VIEW_ERROR_DETAILS=Ver detalles
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Descargar detalles adicionales
#XMSG: Download completed
downloadStarted=Descarga iniciada
#XMSG: Error while downloading content
errorInDownload=Se produjo un error durante la descarga.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Ver detalles
#XBTN: cancel button of task details dialog
TXT_CANCEL=Cancelar
#XBTN: back button from task details
TXT_BACK=Atrás
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Tarea completada
#XFLD: Log message with failed status
MSG_LOG_FAILED=Error en la tarea
#XFLD: Master and detail table with no data
No_Data=Sin datos
#XFLD: Retry tooltip
TEXT_RETRY=Reintentar
#XFLD: Cancel Run label
TEXT_CancelRun=Cancelar ejecución
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Borrar carga con errores
#XMSG:button copy sql statement
txtSQLStatement=Copiar instrucción SQL
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Abrir Supervisor de consulta remota
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=Para mostrar las instrucciones SQL remotas, haga clic en "Abrir Supervisor de consulta remota".
#XMSG:button ok
txtOk=Ok
#XMSG: button close
txtClose=Cerrar
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=Se inició la cancelación de la acción de ejecución para el objeto "{0}".
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=La cancelación de la acción de ejecución para el objeto "{0}" no finalizó correctamente.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=La cancelación de la acción de ejecución para el objeto "{0}" ya no es posible porque el estado de replicación cambió.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Ningún registro de tareas tiene el estado En ejecución.
#XMSG: message for conflicting task
Task_Already_Running=Ya se está ejecutando una tarea con conflictos para el objeto "{0}".
#XFLD: Label for no task log with running state title
actionInfo=Información de acción
#XMSG Copied to clipboard
copiedToClip=Copiado al portapapeles
#XFLD copy
Copy=Copiar
#XFLD copy correlation ID
CopyCorrelationID=Copiar ID de correlación
#XFLD Close
Close=Cerrar
#XFLD: show more Label
txtShowMore=Mostrar más
#XFLD: message Label
messageLabel=Mensaje:
#XFLD: details Label
detailsLabel=Detalles:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Ejecutando \r\n instrucción SQL:
#XFLD:statementId Label
statementIdLabel=ID de instrucción:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Número de instrucciones \r\n SQL remotas:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Número de instrucciones
#XFLD: Space Label
txtSpaces=Espacio
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Espacios no autorizados
#XFLD: Privilege Error Text
txtPrivilegeError=No tiene los privilegios suficientes para ver estos datos.
#XFLD: Label for Object Header
DATA_ACCESS=Acceso a datos
#XFLD: Label for Object Header
SCHEDULE=Programar
#XFLD: Label for Object Header
DETAILS=Detalles
#XFLD: Label for Object Header
LATEST_UPDATE=Última actualización
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Último cambio (origen)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Frecuencia de actualización
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Frecuencia programada
#XFLD: Label for Object Header
NEXT_RUN=Siguiente ejecución
#XFLD: Label for Object Header
CONNECTION=Conexión
#XFLD: Label for Object Header
DP_AGENT=Agente de DP
#XFLD: Label for Object Header
USED_IN_MEMORY=Memoria usada para almacenamiento (MiB)
#XFLD: Label for Object Header
USED_DISK=Disco usado para almacenamiento (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Tamaño en memoria (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Tamaño en disco (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Número de registros

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Configurar en Error
SET_TO_FAILED_ERR=Esta tarea estaba en ejecución, pero el usuario definió el estado de la tarea en ERROR.
#XFLD: Label for stopped failed
FAILLOCKED=Ejecución en curso
#XFLD: sub status STOPPED
STOPPED=Detenido
STOPPED_ERR=Esta tarea se detuvo, pero no se realizó ninguna reversión.
#XFLD: sub status CANCELLED
CANCELLED=Cancelado
CANCELLED_ERR=Esta tarea se canceló luego de haberse iniciado. En este caso los datos se revirtieron y restauraron al estado existente antes de que la tarea se haya activado inicialmente.
#XFLD: sub status LOCKED
LOCKED=Bloqueado
LOCKED_ERR=La misma tarea ya estaba en ejecución; este trabajo de tarea no se puede ejecutar en paralelo con una ejecución de tarea existente.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Excepción de tarea
TASK_EXCEPTION_ERR=Se produjo un error no especificado en esta tarea durante la ejecución.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Excepción de ejecución de tarea
TASK_EXECUTE_EXCEPTION_ERR=Se produjo un error en esta tarea durante la ejecución.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Sin autorización
UNAUTHORIZED_ERR=El usuario no pudo autenticarse o se bloqueó o eliminó.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Prohibido
FORBIDDEN_ERR=El usuario no tiene los privilegios necesarios para ejecutar esta tarea.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=No activado
FAIL_NOT_TRIGGERED_ERR=Este trabajo de tarea no se pudo ejecutar debido a una interrupción del sistema o a una parte del sistema de base de datos no disponible en el momento de la ejecución planificada. Espere a la siguiente hora de ejecución del trabajo programada o vuelva a programar el trabajo.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Programación cancelada
SCHEDULE_CANCELLED_ERR=Este trabajo de tarea no se pudo ejecutar debido a un error interno. Comuníquese con el Equipo de soporte de SAP y bríndeles el ID de correlación y la marca de tiempo de la información detallada en el registro de este trabajo de tarea.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Ejecución anterior en curso
SUCCESS_SKIPPED_ERR=La ejecución de esta tarea no se activó porque aún está en curso una ejecución previa de la misma tarea.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Propietario faltante
FAIL_OWNER_MISSING_ERR=Esta tarea no se pudo ejecutar porque no tiene un usuario del sistema asignado. Asigne un usuario propietario para el trabajo.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Consentimiento no disponible
FAIL_CONSENT_NOT_AVAILABLE_ERR=No autorizó a SAP a ejecutar cadenas de tareas o a programar tareas de integración de datos en su nombre. Seleccione la opción para proporcionar su consentimiento.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Consentimiento vencido
FAIL_CONSENT_EXPIRED_ERR=La autorización que permite a SAP ejecutar cadenas de tareas o programar tareas de integración de datos en su nombre venció. Seleccione la opción provista para renovar su consentimiento.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Consentimiento invalidado
FAIL_CONSENT_INVALIDATED_ERR=Esta tarea no se pudo ejecutar, generalmente debido a un cambio en la configuración del proveedor de identidades del inquilino. En ese caso, no se pueden ejecutar nuevos trabajos de tareas ni programarlos en nombre del usuario afectado. Si el usuario asignado todavía existe en el nuevo proveedor de identidades, revoque el consentimiento de programación y, luego, vuelva a otorgarlo. Si el usuario asignado ya no existe, asigne un nuevo propietario al trabajo de tarea y brinde el consentimiento de programación de tarea requerido. Consulte la siguiente nota de SAP: https://launchpad.support.sap.com/#/notes/3089828 para obtener más información.
TASK_EXECUTOR_ERROR=Ejecutor de tarea
TASK_EXECUTOR_ERROR_ERR=Se produjo un error interno en esta tarea, probablemente durante los pasos de preparación para la ejecución, y la tarea no pudo iniciarse.
PREREQ_NOT_MET=No se cumple el requisito previo
PREREQ_NOT_MET_ERR=Esta tarea no se pudo ejecutar por problemas en su definición. Por ejemplo, el objeto no está implementado, una cadena de tareas contiene lógica circular o el SQL de una vista no es válido.
RESOURCE_LIMIT_ERROR=Error de límite de recurso
RESOURCE_LIMIT_ERROR_ERR=Actualmente no se puede realizar la tarea porque no hay recursos suficientes disponibles o están ocupados.
FAIL_CONSENT_REFUSED_BY_UMS=Consentimiento rechazado
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Esta tarea no se pudo ejecutar, en ejecuciones programadas o cadenas de tareas, debido a un cambio en la configuración del proveedor de identidades de un usuario en el inquilino. Para más información, consulte la siguiente nota de SAP: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Programado
#XFLD: status text
SCHEDULEDNew=Permanente
#XFLD: status text
PAUSED=En pausa
#XFLD: status text
DIRECT=Directo
#XFLD: status text
MANUAL=Manual
#XFLD: status text
DIRECTNew=Simple
#XFLD: status text
COMPLETED=Finalizado
#XFLD: status text
FAILED=Error
#XFLD: status text
RUNNING=En ejecución
#XFLD: status text
none=Ninguno
#XFLD: status text
realtime=En tiempo real
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Subtarea
#XFLD: New Data available in the file
NEW_DATA=Nuevos datos

#XFLD: text for values shown in column Replication Status
txtOff=Desactivado
#XFLD: text for values shown in column Replication Status
txtInitializing=Inicializando
#XFLD: text for values shown in column Replication Status
txtLoading=Cargando
#XFLD: text for values shown in column Replication Status
txtActive=Activo
#XFLD: text for values shown in column Replication Status
txtAvailable=Disponible
#XFLD: text for values shown in column Replication Status
txtError=Error
#XFLD: text for values shown in column Replication Status
txtPaused=En pausa
#XFLD: text for values shown in column Replication Status
txtDisconnected=Desconectado
#XFLD: text for partially Persisted views
partiallyPersisted=Persistencia parcial

#XFLD: activity text
REPLICATE=Replicar
#XFLD: activity text
REMOVE_REPLICATED_DATA=Quitar datos replicados
#XFLD: activity text
DISABLE_REALTIME=Deshabilitar replicación de datos en tiempo real
#XFLD: activity text
REMOVE_PERSISTED_DATA=Quitar datos persistentes
#XFLD: activity text
PERSIST=Persistencia
#XFLD: activity text
EXECUTE=Ejecutar
#XFLD: activity text
TASKLOG_CLEANUP=Limpieza_registro
#XFLD: activity text
CANCEL_REPLICATION=Cancelar replicación
#XFLD: activity text
MODEL_IMPORT=Importación de modelo
#XFLD: activity text
NONE=Ninguno
#XFLD: activity text
CANCEL_PERSISTENCY=Cancelar persistencia
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Analizar vista
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Cancelar analizador de vistas

#XFLD: severity text
INFORMATION=Información
#XFLD: severity text
SUCCESS=Correcto
#XFLD: severity text
WARNING=Advertencia
#XFLD: severity text
ERROR=Error
#XFLD: text for values shown for Ascending sort order
SortInAsc=Orden ascendente
#XFLD: text for values shown for Descending sort order
SortInDesc=Orden descendente
#XFLD: filter text for task log columns
Filter=Filtrar
#XFLD: object text for task log columns
Object=Objeto
#XFLD: space text for task log columns
crossSpace=Espacio

#XBUT: label for remote data access
REMOTE=Remoto
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Replicado (en tiempo real)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Replicado (instantánea)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=La replicación en tiempo real está bloqueada debido a un error. Cuando este error se corrija, podrá utilizar la acción "Reintentar" para continuar con la replicación en tiempo real.
ERROR_MSG=La replicación en tiempo real está bloqueada debido a un error.
RETRY_FAILED_ERROR=Se produjo un error en el proceso de reintento.
LOG_INFO_DETAILS=Cuando los datos se replican en el modo de tiempo real, no se genera ningún registro. Los registros mostrados corresponden a acciones anteriores.

#XBUT: Partitioning label
partitionMenuText=Particiones
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Crear partición
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Editar partición
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Eliminar partición
#XFLD: Initial text
InitialPartitionText=Para definir particiones, especifique los criterios de división de grandes conjuntos de datos en conjuntos más pequeños.
DefinePartition=Definir particiones
#XFLD: Message text
partitionChangedInfo=La definición de la partición cambió desde la última replicación. Los cambios se aplicarán en la siguiente carga de datos.
#XFLD: Message text
REAL_TIME_WARNING=La creación de particiones solo se aplica cuando se carga una nueva instantánea. No se aplica para la replicación en tiempo real.
#XFLD: Message text
loadSelectedPartitions=Se inició la persistencia de datos para las particiones seleccionadas de "{0}"
#XFLD: Message text
loadSelectedPartitionsError=Falló la persistencia de datos para las particiones seleccionadas de "{0}"
#XFLD: Message text
viewpartitionChangedInfo=La definición de la partición cambió desde la última ejecución de la persistencia. Para aplicar los cambios, la siguiente carga de datos será una instantánea completa, incluidas las particiones bloqueadas. Una vez que esta carga completa finalice, podrá ejecutar datos para particiones individuales.
#XFLD: Message text
viewpartitionChangedInfoLocked=La definición de la partición cambió desde la última ejecución de la persistencia. Para aplicar los cambios, la siguiente carga de datos será una instantánea completa, a excepción de los rangos de particiones sin cambios y bloqueadas. Una vez que esta carga completa finalice, podrá volver a cargar las particiones seleccionadas.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Replicación de tabla
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Programar replicación
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Crear programa
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Editar programa
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Eliminar programa
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Cargar nueva instantánea
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Iniciar replicación de datos
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Quitar datos replicados
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Habilitar acceso en tiempo real
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Habilitar replicación de datos en tiempo real
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Deshabilitar replicación de datos en tiempo real
#XFLD: Message for replicate table action
replicateTableText=Replicación de tabla
#XFLD: Message for replicate table action
replicateTableTextNew=Replicación de datos
#XFLD: Message to schedule task
scheduleText=Programar
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Persistencia de vista
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Persistencia de datos
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Cargar nueva instantánea
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Iniciar persistencia de datos
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Quitar datos persistentes
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Procesamiento particionado
#XBUT: Label for scheduled replication
scheduledTxt=Programado
#XBUT: Label for statistics button
statisticsTxt=Estadísticas
#XBUT: Label for create statistics
createStatsTxt=Crear estadísticas
#XBUT: Label for edit statistics
editStatsTxt=Editar estadísticas
#XBUT: Label for refresh statistics
refreshStatsTxt=Actualizar estadísticas
#XBUT: Label for delete statistics
dropStatsTxt=Eliminar estadísticas
#XMSG: Create statistics success message
statsSuccessTxt=Se comenzó a crear estadísticas de tipo {0} para {1}.
#XMSG: Edit statistics success message
statsEditSuccessTxt=Se comenzó a cambiar el tipo de estadísticas a {0} para {1}.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Se comenzó a actualizar estadísticas para {0}.
#XMSG: Drop statistics success message
statsDropSuccessTxt=Estadísticas eliminadas correctamente para {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=Error durante la creación de estadísticas
#XMSG: Edit statistics error message
statsEditErrorTxt=Error durante el cambio de estadísticas
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Error durante la actualización de estadísticas
#XMSG: Drop statistics error message
statsDropErrorTxt=Error durante la eliminación de estadísticas
#XMG: Warning text for deleting statistics
statsDelWarnTxt=¿Seguro que desea eliminar las estadísticas de datos?
startPersistencyAdvisorLabel=Iniciar analizador de vista

#Partition related texts
#XFLD: Label for Column
column=Columna
#XFLD: Label for No of Partition
noOfPartitions=Número de particiones
#XFLD: Label for Column
noOfParallelProcess=Número de procesos paralelos
#XFLD: Label text
noOfLockedPartition=Número de particiones bloqueadas
#XFLD: Label for Partition
PARTITION=Particiones
#XFLD: Label for Column
AVAILABLE=Disponible
#XFLD: Statistics Label
statsLabel=Estadísticas
#XFLD: Label text
COLUMN=Columna:
#XFLD: Label text
PARALLEL_PROCESSES=Procesos paralelos:
#XFLD: Label text
Partition_Range=Rango de particiones
#XFLD: Label text
Name=Nombre
#XFLD: Label text
Locked=Bloqueado
#XFLD: Label text
Others=OTROS
#XFLD: Label text
Delete=Eliminar
#XFLD: Label text
LoadData=Cargar particiones seleccionadas
#XFLD: Label text
LoadSelectedData=Cargar particiones seleccionadas
#XFLD: Confirmation text
LoadNewPersistenceConfirm=Esto cargará una nueva instantánea para todas las particiones desbloqueadas y modificadas, y no solo las que seleccionó. ¿Desea continuar?
#XFLD: Label text
Continue=Continuar

#XFLD: Label text
PARTITIONS=Particiones
#XFLD: Label text
ADD_PARTITIONS=+ Agregar particiones
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Agregar partición
#XFLD: Label text
deleteRange=Eliminar partición
#XFLD: Label text
LOW_PLACE_HOLDER=Ingresar valor bajo
#XFLD: Label text
HIGH_PLACE_HOLDER=Ingresar valor alto
#XFLD: tooltip text
lockedTooltip=Bloquear partición luego de la carga inicial

#XFLD: Button text
Edit=Editar
#XFLD: Button text
CANCEL=Cancelar

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Última actualización de estadísticas
#XFLD: Statistics Fields
STATISTICS=Estadísticas

#XFLD:Retry label
TEXT_Retry=Reintentar
#XFLD:Retry label
TEXT_Retry_tooltip=Reintentar replicación en tiempo real después de resolver el error.
#XFLD: text retry
Retry=Confirmar
#XMG: Retry confirmation text
retryConfirmationTxt=La última replicación en tiempo real finalizó con un error.\n Confirme que el error se corrigió y que se puede reintentar el proceso.
#XMG: Retry success text
retrySuccess=El reintento del proceso se inició correctamente.
#XMG: Retry fail text
retryFail=Falló el reintento del proceso.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Crear estadísticas
#XMSG: activity message for edit statistics
DROP_STATISTICS=Eliminar estadísticas
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Actualizar estadísticas
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Editar estadísticas
#XMSG: Task log message started task
taskStarted=Se inició la tarea {0}.
#XMSG: Task log message for finished task
taskFinished=La tarea {0} finalizó con el estado {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=La tarea {0} finalizó a las {2} con el estado {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=La tarea {0} tiene parámetros de entrada.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=La tarea {0} finalizó con un error inesperado. El estado de la tarea se estableció en {1}.
#XMSG: Task log message for failed task
failedToEnd=Se produjo un error al establecer el estado en {0} o al quitar el bloqueo.
#XMSG: Task log message
lockNotFound=No se puede finalizar el proceso porque falta el bloqueo; es posible que se haya cancelado la tarea.
#XMSG: Task log message failed task
failedOverwrite={1} ya bloqueó la tarea {0}. Por lo tanto, no se completó con el siguiente error: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=El bloqueo de esta tarea fue tomado por otra tarea.
#XMSG: Task log message failed takeover
failedTakeover=Se produjo un error que impidió retomar la tarea existente.
#XMSG: Task log message successful takeover
successTakeover=Se liberó el bloqueo anterior y se estableció un nuevo bloqueo para esta tarea.
#XMSG: Tasklog Dialog Details
txtDetails=Para mostrar las instrucciones remotas procesadas durante la ejecución, abra el supervisor de consulta remota en los detalles de los mensajes específicos de la partición.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=Las instrucciones SQL remotas se eliminaron de la base de datos y no se pueden mostrar.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Las consultas remotas que tienen conexiones asignadas a otros espacios no se pueden mostrar. Vaya al Supervisor de consulta remota y use el ID de la instrucción para filtrarlas.
#XMSG: Task log message for parallel check error
parallelCheckError=La tarea no se puede procesar porque otra tarea está en ejecución y ya bloquea esta tarea.
#XMSG: Task log message for parallel running task
parallelTaskRunning=Ya se está ejecutando una tarea con conflictos.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Estado {0} durante la ejecución con el ID de correlación {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=El usuario no tiene los privilegios necesarios para ejecutar esta tarea.

#XBUT: Label for open in Editor
openInEditor=Abrir en el editor
#XBUT: Label for open in Editor
openInEditorNew=Abrir en el generador de datos
#XFLD:Run deails label
runDetails=Detalles de ejecución
#XFLD: Label for Logs
Logs=Registros
#XFLD: Label for Settings
Settings=Opciones
#XFLD: Label for Save button
Save=Guardar
#XFLD: Label for Standard
Standard_PO=Optimización del rendimiento (recomendado)
#XFLD: Label for Hana low memory processing
HLMP_MO=Optimización de la memoria
#XFLD: Label for execution mode
ExecutionMode=Modo de ejecución
#XFLD: Label for job execution
jobExecution=Modo de procesamiento
#XFLD: Label for Synchronous
syncExec=Sincrónico
#XFLD: Label for Asynchronous
asyncExec=Asincrónico
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Usar predeterminado (asincrónico, puede cambiar en el futuro)
#XMSG: Save settings success
saveSettingsSuccess=Se cambió el modo de ejecución de SAP HANA.
#XMSG: Save settings failure
saveSettingsFailed=Ocurrió un error en el cambio de modo de ejecución de SAP HANA.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Cambió la ejecución del trabajo.
#XMSG: Job Execution change failed
jobExecSettingFailed=Ocurrió un error en el cambio de ejecución del trabajo.
#XMSG: Text for Type
typeTxt=Tipo
#XMSG: Text for Monitor
monitorTxt=Supervisor
#XMSG: Text for activity
activityTxt=Actividad
#XMSG: Text for metrics
metricsTxt=Métricas
#XTXT: Text for Task chain key
TASK_CHAINS=Cadena de tareas
#XTXT: Text for View Key
VIEWS=Vista
#XTXT: Text for remote table key
REMOTE_TABLES=Tabla remota
#XTXT: Text for Space key
SPACE=Espacio
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Nodo de procesamiento elástico
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Flujo de replicación
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Búsqueda inteligente
#XTXT: Text for Local Table
LOCAL_TABLE=Tabla local
#XTXT: Text for Data flow key
DATA_FLOWS=Flujo de datos
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=Procedimiento de script SQL
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=Cadena de procesos de BW
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Vista en supervisor
#XTXT: Task List header text
taskListHeader=Lista de tareas ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=No se pueden recuperar las métricas para las ejecuciones históricas de un flujo de datos.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=El detalle completo de la ejecución no se está cargando por el momento. Intente actualizar.
#XFLD: Label text for the Metrices table header
metricesColLabel=Etiqueta de operador
#XFLD: Label text for the Metrices table header
metricesType=Tipo
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Recuento de registros
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Ejecutar la cadena de tareas
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Se inició la cadena de tareas.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Se inició la ejecución de la cadena de tareas para {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Se produjo un error en la ejecución de la cadena de tareas.
#XTXT: Execute button label
runLabel=Ejecutar
#XTXT: Execute button label
runLabelNew=Iniciar ejecución
#XMSG: Filter Object header
chainsFilteredTableHeader=Filtrado por objeto: {0}
#XFLD: Parent task chain label
parentChainLabel=Cadena de tareas principal:
#XFLD: Parent task chain unauthorized
Unauthorized=Visualización no autorizada
#XFLD: Parent task chain label
parentTaskLabel=Tarea principal:
#XTXT: Task status
NOT_TRIGGERED=No activado
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Ingresar al modo de pantalla completa
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Salir del modo de pantalla completa
#XTXT: Close Task log details right panel
closeRightColumn=Cerrar sección
#XTXT: Sort Text
sortTxt=Ordenar
#XTXT: Filter Text
filterTxt=Filtrar
#XTXT: Filter by text to show list of filters applied
filterByTxt=Filtrar por
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Más de 5 minutos
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Más de 15 minutos
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Más de una hora
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Última hora
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Últimas 24 horas
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Último mes
#XTXT: Messages title text
messagesText=Mensajes

#XTXT Statistics information message
statisticsInfo=Las estadísticas no se pueden crear para tablas remotas con el acceso de datos "Replicado". Para crear estadísticas, quite los datos replicados en el supervisor de detalles de tablas remotas.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Ir al Supervisor de detalles de tablas remotas

#XTXT: Repair latest failed run label
retryRunLabel=Reintentar última ejecución
#XTXT: Repair failed run label
retryRun=Reintentar ejecución
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Se inició el reintento de la cadena de tareas
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Falló el reintento de la cadena de tareas
#XMSG: Task chain child elements name
taskChainRetryChildObject=Tarea {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Nueva tarea
#XFLD Analyzed View
analyzedView=Vista analizada
#XFLD Metrics
Metrics=Métricas
#XFLD Partition Metrics
PartitionMetrics=Métricas de partición
#XFLD Entities
Messages=Registro de tareas
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Aún no se definió ninguna partición.
#XTXT: Description message for empty partition data
partitionEmptyDescText=Cree particiones mediante la especificación de criterios para dividir volúmenes de datos grandes en partes más pequeñas y manejables.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Aún no hay registros disponibles
#XTXT: Description message for empty runs data
runsEmptyDescText=Cuando inicie una nueva actividad (cargar una nueva instantánea, iniciar el analizador de vistas, etc.), verá los registros relacionados aquí.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=La configuración del nodo de procesamiento elástico {0} no tiene un mantenimiento acorde. Revise su definición.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=El proceso para agregar el nodo de procesamiento elástico {0} falló.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Se inició la creación y activación de la réplica para el objeto de origen "{0}"."{1}" en el nodo de procesamiento elástico {2}.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Se inició la eliminación de la réplica para el objeto de origen "{0}"."{1}" en el nodo de procesamiento elástico {2}.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Falló la creación y activación de la réplica para el objeto de origen "{0}"."{1}".
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Falló la eliminación de la réplica para el objeto de origen "{0}"."{1}".
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Se inició el enrutamiento del procesamiento de consultas analíticas del espacio {0} al nodo de procesamiento elástico {1}.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Falló el enrutamiento del procesamiento de consultas analíticas del espacio {0} al nodo de procesamiento elástico correspondiente.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Se inició el nuevo enrutamiento del procesamiento de consultas analíticas del espacio {0} desde el nodo de procesamiento elástico {1}.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Falló el nuevo enrutamiento del procesamiento de consultas analíticas del espacio {0} desde el coordinador.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Se activó la cadena de tareas {0} al nodo de procesamiento elástico correspondiente {1}.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Falló la generación de la cadena de tareas para el nodo de procesamiento elástico {0}.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=Se inició el aprovisionamiento del nodo de procesamiento elástico {0} con el siguiente plan de tamaños: vCPU: {1}, memoria (GiB): {2} y almacenamiento (GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=El nodo de procesamiento elástico {0} ya se aprovisionó.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=El nodo de procesamiento elástico {0} ya se desaprovisionó.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=La operación no se permite. Detenga el nodo de procesamiento elástico {0} y reinícielo para actualizar el plan de tamaño.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=El proceso para quitar el nodo de procesamiento elástico {0} falló.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=La ejecución actual del nodo de procesamiento elástico {0} venció.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=La verificación del estado del nodo de procesamiento elástico {0} está en curso…
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=La generación de la cadena de tareas del nodo de procesamiento elástico {0} está bloqueada y, por lo tanto, la cadena {1} podría estar ejecutándose aún.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=El objeto de origen "{0}"."{1}" se replicó como una dependencia de la vista "{2}"."{3}".
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=No se puede replicar la tabla "{0}"."{1}" porque la replicación de la tabla de filas está fuera de servicio.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Se excede el nodo de procesamiento elástico máximo por instancia de SAP HANA Cloud.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=La operación de ejecución para el nodo de procesamiento elástico {0} aún está en curso.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Debido a problemas técnicos, se interrumpió la eliminación de la réplica para la tabla de origen {0}. Vuelva a intentarlo más tarde.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=Debido a problemas técnicos, se interrumpió la creación de la réplica para la tabla de origen {0}. Vuelva a intentarlo más tarde.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=No se puede iniciar un nodo de procesamiento elástico debido a que se alcanzó el límite de uso o a que aún no se asignaron horas de bloque de procesamiento.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=En proceso de carga de la cadena de tareas y en preparación para la ejecución de un total de {0} tareas que son parte de esta cadena.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Ya se está ejecutando una tarea con conflictos
#XMSG: Replication will change
txt_replication_change=Se cambiará el tipo de replicación.
txt_repl_viewdetails=Ver detalles

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Parece que ocurrió un error al reintentar la última ejecución, ya que la ejecución de la tarea anterior falló antes de que se pudiera generar el plan.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=El espacio "{0}" está bloqueado.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=La actividad {0} requiere que se implemente la tabla local {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=La actividad {0} requiere que se active la captura delta en la tabla local {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=La actividad {0} requiere que la tabla local {1} almacene datos en la tienda de objetos.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=La actividad {0} requiere que la tabla local {1} almacene datos en la base de datos.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Se comenzaron a quitar los registros eliminados de la tabla local {0}.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Se comenzaron a eliminar todos los registros de la tabla local {0}.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Se comenzaron a eliminar los registros de la tabla local {0} de acuerdo con la condición de filtro {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Ocurrió un error al quitar los registros eliminados de la tabla local {0}.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Ocurrió un error al eliminar todos los registros de la tabla local {0}.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Se comenzaron a eliminar todos los registros completamente procesados con el tipo de cambio "Eliminado" que tienen más de {0} días.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Se comenzaron a eliminar todos los registros completamente procesados con el tipo de cambio "Eliminado" que tienen más de {0}.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} registros se eliminaron.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS=Se marcaron {0} registros para eliminación.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=Se terminó de quitar los registros eliminados de la tabla local {0}.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Se terminó de eliminar todos los registros de la tabla local {0}.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Se comenzaron a marcar registros como "Eliminados" de la tabla local {0}.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Se comenzaron a marcar los registros como "Eliminados" de la tabla local {0} de acuerdo con la condición de filtro {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Se terminó de marcar registros como "Eliminados" de la tabla local {0}.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Los cambios de datos se están cargando temporalmente en la tabla {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Los cambios de datos se cargaron temporalmente en la tabla {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Los cambios de datos se procesaron y se eliminaron de la tabla {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Detalles de conexión.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Se está iniciando la optimización de la Tabla local (archivo).
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Ocurrió un error durante la optimización de la Tabla local (archivo).
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Ocurrió un error. Se detuvo la optimización de la Tabla local (archivo).
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Optimizando la Tabla local (archivo)…
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=Se optimizó la Tabla local (archivo).
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=La Tabla local (archivo) está optimizada.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=La tabla está optimizada con columnas de orden Z: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Ocurrió un error. Se detuvo el truncamiento de la Tabla local (archivo).
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Truncando la Tabla local (archivo)…
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=Se cayó la ubicación de búfer de entrada para tabla local (archivo).

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Se está iniciando el vaciado (eliminación de todos los registros completamente procesados) de la Tabla local (archivo).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Ocurrió un error durante el vaciado de la Tabla local (archivo).
#XMSG: Task log message
LTF_VACUUM_STOPPED=Ocurrió un error. Se detuvo el vaciado de la Tabla local (archivo).
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Vaciando la Tabla local (archivo)…
#XMSG: Task log message
LTF_VACUUM_SUCCESS=Se completó el vaciado.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Se comenzaron a eliminar todos los registros completamente procesados que tienen más de {0} días.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Se comenzaron a eliminar todos los registros completamente procesados que tienen más de {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Se está iniciando la combinación de nuevos registros con la Tabla local (archivo).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Se inició la combinación de nuevos registros con la tabla local (archivo) mediante la opción "Combinar datos automáticamente".
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Comenzó la combinación de nuevos registros con Tabla local (archivo). Esta tarea se inició a través de la solicitud de combinación de API.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Se están combinando los nuevos registros con la Tabla local (archivo).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Ocurrió un error durante la combinación de los nuevos registros con la Tabla local (archivo).
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=La Tabla local (archivo) está combinada.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Ocurrió un error. Se detuvo la combinación de la Tabla local (archivo).
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=La combinación de tabla local (archivo) falló debido a un error, pero la operación se completó parcialmente, y algunos datos ya se combinaron.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Ocurrió un error de tiempo de espera. La ejecución de la actividad {0} se estuvo realizando durante {1} horas.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=La ejecución asincrónica no se pudo iniciar debido a una alta carga del sistema. Abra el ''Supervisor del sistema'' y compruebe las tareas en ejecución.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=La ejecución asincrónica se canceló.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=La tarea {0} se ejecutó entre los límites de memoria de {1} y {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=La tarea {0} se ejecutó con el ID de recurso {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=Buscar y reemplazar comenzó en Tabla local (Archivo).
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Buscar y reemplazar finalizó en Tabla local (Archivo).
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Buscar y reemplazar falló en Tabla local (Archivo).

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Ocurrió un error. Se detuvo la actualización de estadísticas de la Tabla local (archivo).

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=La tarea falló debido a un error de falta de memoria en la base de datos de SAP HANA.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=La tarea falló debido a un rechazo del control de admisión de SAP HANA.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=La tarea falló debido a demasiadas conexiones activas de SAP HANA.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=La operación Reintentar no se pudo realizar porque los reintentos solo se permiten en el elemento principal de la cadena de tareas anidada.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Los registros de las tareas secundarias fallidas ya no están disponibles para que se proceda con Reintentar.


####Metrics Labels

performanceOptimized=Optimización del rendimiento
memoryOptimized=Optimización de la memoria

JOB_EXECUTION=Ejecución del trabajo
EXECUTION_MODE=Modo de ejecución
NUMBER_OF_RECORDS_OVERALL=Número de registros con persistencia
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Número de registros leídos desde el origen remoto
RUNTIME_MS_REMOTE_EXECUTION_TIME=Tiempo de procesamiento de origen remoto
MEMORY_CONSUMPTION_GIB=Memoria máxima de SAP HANA
NUMBER_OF_PARTITIONS=Cantidad de particiones
MEMORY_CONSUMPTION_GIB_OVERALL=Memoria máxima de SAP HANA
NUMBER_OF_PARTITIONS_LOCKED=Cantidad de particiones bloqueadas
PARTITIONING_COLUMN=Columna de particiones
HANA_PEAK_CPU_TIME=Tiempo de CPU total de SAP HANA
USED_IN_DISK=Almacenamiento usado
INPUT_PARAMETER_PARAMETER_VALUE=Parámetro de entrada
INPUT_PARAMETER=Parámetro de entrada
ECN_ID=Nombre de nodo de procesamiento elástico

DAC=Controles de acceso de datos
YES=Sí
NO=No
noofrecords=Número de registros
partitionpeakmemory=Memoria máxima de SAP HANA
value=Valor
metricsTitle=Métricas ({0})
partitionmetricsTitle=Particiones ({0})
partitionLabel=Partición
OthersNotNull=Valores no incluidos en rangos
OthersNull=Valores nulos
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Opciones utilizadas para la última ejecución de persistencia de datos:
#XMSG: Message for input parameter name
inputParameterLabel=Parámetro de entrada
#XMSG: Message for input parameter value
inputParameterValueLabel=Valor
#XMSG: Message for persisted data
inputParameterPersistedLabel=Persistente en
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Eliminar datos
REMOVE_DELETED_RECORDS=Eliminar registros eliminados
MERGE_FILES=Combinar archivos
OPTIMIZE_FILES=Optimizar archivos
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Ver en el Supervisor de puente de SAP BW

ANALYZE_PERFORMANCE=Analizar rendimiento
CANCEL_ANALYZE_PERFORMANCE=Cancelar análisis de rendimiento

#XFLD: Label for frequency column
everyLabel=Cada
#XFLD: Plural Recurrence text for Hour
hoursLabel=Horas
#XFLD: Plural Recurrence text for Day
daysLabel=Días
#XFLD: Plural Recurrence text for Month
monthsLabel=Meses
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutos

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Ver guía de solución de problemas de persistencia</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Proceso sin memoria. No se puede realizar la persistencia de los datos para la vista "{0}". Consulte más información sobre errores de falta de memoria en el Portal de ayuda. Considere utilizar View Analyzer para analizar la complejidad del consumo de memoria de la vista.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=No aplicable
OPEN_BRACKET=(
CLOSE_BRACKET=)
