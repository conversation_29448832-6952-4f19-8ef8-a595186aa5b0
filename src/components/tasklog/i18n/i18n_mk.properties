
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Детали за дневникот
#XFLD: Header
TASK_LOGS=Дневници на задача ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Извршувања ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Прикажи ги деталите
#XFLD: Button text
STOP=Запри го извршувањето
#XFLD: Label text
RUN_START=Почеток на последното извршување
#XFLD: Label text
RUN_END=Крај на последното извршување
#XFLD: Label text
RUNTIME=Времетраење
#XTIT: Count for Messages
txtDetailMessages=Пораки ({0})
#XFLD: Label text
TIME=Временски печат
#XFLD: Label text
MESSAGE=Порака
#XFLD: Label text
TASK_STATUS=Категорија
#XFLD: Label text
TASK_ACTIVITY=Активност
#XFLD: Label text
RUN_START_DETAILS=Започни
#XFLD: Label text
RUN_END_DETAILS=Крај
#XFLD: Label text
LOGS=Извршувања
#XFLD: Label text
STATUS=Статус
#XFLD: Label text
RUN_STATUS=Статус на извршување
#XFLD: Label text
Runtime=Времетраење
#XFLD: Label text
RuntimeTooltip=Времетраење (чч : мм : сс)
#XFLD: Label text
TRIGGEREDBY=Активирано од
#XFLD: Label text
TRIGGEREDBYNew=Извршено од
#XFLD: Label text
TRIGGEREDBYNewImp=Извршувањето го започна
#XFLD: Label text
EXECUTIONTYPE=Тип извршување
#XFLD: Label text
EXECUTIONTYPENew=Тип извршување
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Простор со надредени синџири
#XFLD: Refresh tooltip
TEXT_REFRESH=Освежи
#XFLD: view Details link
VIEW_ERROR_DETAILS=Прикажи ги деталите
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Преземи ги дополнителните детали
#XMSG: Download completed
downloadStarted=Преземањето започна
#XMSG: Error while downloading content
errorInDownload=Настана грешка при преземањето.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Прикажи ги деталите
#XBTN: cancel button of task details dialog
TXT_CANCEL=Откажи
#XBTN: back button from task details
TXT_BACK=Назад
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Задачата е завршена
#XFLD: Log message with failed status
MSG_LOG_FAILED=Задачата не успеа
#XFLD: Master and detail table with no data
No_Data=Нема податоци
#XFLD: Retry tooltip
TEXT_RETRY=Обиди се повторно
#XFLD: Cancel Run label
TEXT_CancelRun=Откажи го извршувањето
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Неуспешно вчитување на чистењето
#XMSG:button copy sql statement
txtSQLStatement=Копирај SQL-наредба
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Отвори Алатка за следење прашалник на далечина
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=За да ги прикажете SQL-наредбите на далечина, кликнете на „Отвори Алатка за следење прашалник на далечина“.
#XMSG:button ok
txtOk=Во ред
#XMSG: button close
txtClose=Затвори
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=Откажувањето на дејството за извршување за објектот „{0}“ е започнато.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=Откажувањето на дејството за извршување за објектот „{0}“ е неуспешно.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=Откажувањето на дејството за извршување за објектот „{0}“ повеќе не е можно бидејќи статусот на репликацијата е променет.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Ниту еден дневник со задачи нема статус „Се извршува“.
#XMSG: message for conflicting task
Task_Already_Running=Задача во конфликт веќе се извршува за објектот „{0}“.
#XFLD: Label for no task log with running state title
actionInfo=Детали за дејството
#XMSG Copied to clipboard
copiedToClip=Ископирано во складот
#XFLD copy
Copy=Копирај
#XFLD copy correlation ID
CopyCorrelationID=Копирај го ИД-бројот на корелацијата
#XFLD Close
Close=Затвори
#XFLD: show more Label
txtShowMore=Покажи повеќе
#XFLD: message Label
messageLabel=Порака:
#XFLD: details Label
detailsLabel=Детали:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Извршување на SQL \r\n наредба:
#XFLD:statementId Label
statementIdLabel=ИД на наредбата:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Број далечински \r\n SQL наредби:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Број наредби
#XFLD: Space Label
txtSpaces=Простор
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Неовластени простори
#XFLD: Privilege Error Text
txtPrivilegeError=Немате доволно привилегии за да ги прикажете податоциве.
#XFLD: Label for Object Header
DATA_ACCESS=Пристап до податоци
#XFLD: Label for Object Header
SCHEDULE=Распоред
#XFLD: Label for Object Header
DETAILS=Детали
#XFLD: Label for Object Header
LATEST_UPDATE=Најново ажурирање
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Последна промена (извор)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Освежи ја зачестеноста
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Распоредена зачестеност
#XFLD: Label for Object Header
NEXT_RUN=Следно извршување
#XFLD: Label for Object Header
CONNECTION=Врска
#XFLD: Label for Object Header
DP_AGENT=Агент за дистрибуција на податоци
#XFLD: Label for Object Header
USED_IN_MEMORY=Меморија искористена за складирање (MiB)
#XFLD: Label for Object Header
USED_DISK=Диск искористен за складирање (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Големина во меморијата (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Големина на дискот (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Број записи

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Постави на „Не успеа“
SET_TO_FAILED_ERR=Задачава се извршуваше, но корисникот го постави статусот на задачата на НЕУСПЕШНО.
#XFLD: Label for stopped failed
FAILLOCKED=Извршувањето е веќе во тек
#XFLD: sub status STOPPED
STOPPED=Запрено
STOPPED_ERR=Оваа задача е прекината, но не е извршено rollback.
#XFLD: sub status CANCELLED
CANCELLED=Откажано
CANCELLED_ERR=Задачава е откажана по започнувањето. Во овој случај, податоците се вратени во состојбата пред првично да се изврши задачата.
#XFLD: sub status LOCKED
LOCKED=Заклучено
LOCKED_ERR=Истата задача веќе се извршува, така што оваа задача не може да се извршува паралелно со извршувањето на постојната задача.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Исклучок на задача
TASK_EXCEPTION_ERR=Задачава наиде на неодредена грешка при извршувањето.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Исклучок за извршување на задачата
TASK_EXECUTE_EXCEPTION_ERR=Задачава наиде на грешка при извршувањето.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Неовластено
UNAUTHORIZED_ERR=Корисникот не може да се автентицира, корисникот е заклучен или избришан.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Забрането
FORBIDDEN_ERR=Доделениот корисник ги нема потребните привилегии за извршување на оваа задача.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Не е активирано
FAIL_NOT_TRIGGERED_ERR=Задачава не може да се изврши поради прекин на системот или некој дел од системот на базата на податоци не е достапен во времето на планираното извршување. Почекајте го следното закажано време за извршување на задачата или презакажете ја задачата.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Распоредот е откажан
SCHEDULE_CANCELLED_ERR=Задачава не може да се изврши поради внатрешна грешка. Контактирајте со поддршката на SAP и наведете го ИД на корелацијата и временскиот печат од деталните информации на дневникот на оваа задача.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Претходното извршување е во тек
SUCCESS_SKIPPED_ERR=Извршувањето на оваа задача не е започнато бидејќи претходното извршување на истата задача сè уште е во тек.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Недостига сопственик
FAIL_OWNER_MISSING_ERR=Задачава не може да се изврши бидејќи нема доделен корисник на системот. Доделете корисник на задачата што е сопственик.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Согласноста не е достапна
FAIL_CONSENT_NOT_AVAILABLE_ERR=Не сте го овластиле SAP да извршува синџири од задачи или да закажува задачи за интеграција на податоци во ваше име. Изберете ја дадената опција за да дадете согласност.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Согласноста е истечена
FAIL_CONSENT_EXPIRED_ERR=Овластувањето што му дозволува на SAP да извршува синџири од задачи или да закажува задачи за интеграција на податоци во ваше име истече. Изберете ја дадената опција за да ја обновите согласноста.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Согласноста веќе не е важечка
FAIL_CONSENT_INVALIDATED_ERR=Задачава не може да се изврши, обично поради промена во конфигурацијата на давателот на идентитет на закупецот. Во тој случај, не може да се извршуваат или да се закажат нови задачи на име на засегнатиот корисник. Ако доделениот корисник сè уште постои во новиот давател на идентитет, отповикајте ја согласноста за закажување и потоа дајте ја повторно. Ако доделениот корисник не постои повеќе, доделете нов сопственик на задачата и обезбедете ја потребната согласност за закажување задачи. Прочитајте ја следната белешка на SAP: https://launchpad.support.sap.com/#/notes/3089828 за повеќе информации.
TASK_EXECUTOR_ERROR=Извршител на задачи
TASK_EXECUTOR_ERROR_ERR=Задачава наиде на внатрешна грешка, најверојатно за време на подготвителните чекори за извршување, и задачата не можеше да започне.
PREREQ_NOT_MET=Предусловот не е исполнет
PREREQ_NOT_MET_ERR=Задачава не можеше да се изврши поради проблеми во нејзината дефиниција. На пример, објектот не е применет, синџирот од задачи содржи кружна логика или SQL-приказот е неважечки.
RESOURCE_LIMIT_ERROR=Грешка во ограничување на ресурсите
RESOURCE_LIMIT_ERROR_ERR=Моментално не може да се изврши задачата бидејќи не се достапни доволно ресурси или се зафатени.
FAIL_CONSENT_REFUSED_BY_UMS=Согласноста е одбиена
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Задачава не може да се изврши во закажаните извршувања или синџирите од задачи поради промена во конфигурацијата за Давателот на идентитет на корисникот на закупецот. За повеќе информации, видете ја следнава белешка од SAP: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Закажано
#XFLD: status text
SCHEDULEDNew=Трајно
#XFLD: status text
PAUSED=Паузирано
#XFLD: status text
DIRECT=Директно
#XFLD: status text
MANUAL=Рачно
#XFLD: status text
DIRECTNew=Едноставно
#XFLD: status text
COMPLETED=Завршено
#XFLD: status text
FAILED=Неуспешно
#XFLD: status text
RUNNING=Се извршува
#XFLD: status text
none=Ништо
#XFLD: status text
realtime=Реално време
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Подзадача
#XFLD: New Data available in the file
NEW_DATA=Нови податоци

#XFLD: text for values shown in column Replication Status
txtOff=Исклучено
#XFLD: text for values shown in column Replication Status
txtInitializing=Се иницијализира
#XFLD: text for values shown in column Replication Status
txtLoading=Се вчитува
#XFLD: text for values shown in column Replication Status
txtActive=Активно
#XFLD: text for values shown in column Replication Status
txtAvailable=Достапно
#XFLD: text for values shown in column Replication Status
txtError=Грешка
#XFLD: text for values shown in column Replication Status
txtPaused=Паузирано
#XFLD: text for values shown in column Replication Status
txtDisconnected=Не е поврзано
#XFLD: text for partially Persisted views
partiallyPersisted=Делумно трајно зачувано

#XFLD: activity text
REPLICATE=Реплицирај
#XFLD: activity text
REMOVE_REPLICATED_DATA=Отстрани ги реплицираните податоци
#XFLD: activity text
DISABLE_REALTIME=Оневозможи репликација на податоци во реално време
#XFLD: activity text
REMOVE_PERSISTED_DATA=Отстрани трајно зачувани податоци
#XFLD: activity text
PERSIST=Трајно зачувај
#XFLD: activity text
EXECUTE=Изврши
#XFLD: activity text
TASKLOG_CLEANUP=Чистење на дневникот со задачи
#XFLD: activity text
CANCEL_REPLICATION=Откажи репликација
#XFLD: activity text
MODEL_IMPORT=Увоз на модел
#XFLD: activity text
NONE=Ништо
#XFLD: activity text
CANCEL_PERSISTENCY=Откажи трајност
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Анализирај приказ
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Откажи Анализатор на прикази

#XFLD: severity text
INFORMATION=Информации
#XFLD: severity text
SUCCESS=Успешно
#XFLD: severity text
WARNING=Предупредување
#XFLD: severity text
ERROR=Грешка
#XFLD: text for values shown for Ascending sort order
SortInAsc=Подреди по растечки редослед
#XFLD: text for values shown for Descending sort order
SortInDesc=Подреди по опаѓачки редослед
#XFLD: filter text for task log columns
Filter=Филтрирај
#XFLD: object text for task log columns
Object=Објект
#XFLD: space text for task log columns
crossSpace=Простор

#XBUT: label for remote data access
REMOTE=На далечина
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Реплицирано (во реално време)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Реплицирано (слика на состојбата)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=Репликацијата во реално време е блокирана поради грешка. Откако грешката ќе се поправи, можете да го користите дејството „Обиди се повторно“ за да продолжите со репликација во реално време.
ERROR_MSG=Репликацијата во реално време е блокирана поради грешка.
RETRY_FAILED_ERROR=Процесот на повторниот обид заврши неуспешно со грешка.
LOG_INFO_DETAILS=Не се генерираат дневници кога податоците се реплицираат во режим во реално време. Прикажаните дневници се однесуваат на претходните дејства.

#XBUT: Partitioning label
partitionMenuText=Поделба на партиции
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Создај партиција
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Уреди партиција
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Избриши партиција
#XFLD: Initial text
InitialPartitionText=Дефинирајте ги партициите со наведување критериуми за поделба на големи збирки на податоци на помали збирови.
DefinePartition=Дефинирај партиции
#XFLD: Message text
partitionChangedInfo=Дефиницијата на партицијата е променета од последната репликација. Промените ќе се применат при следното вчитување податоци.
#XFLD: Message text
REAL_TIME_WARNING=Поделбата се применува само кога се вчитува нова слика. Не се применува за репликација во реално време.
#XFLD: Message text
loadSelectedPartitions=Започна трајно снимање на податоците за избраните партиции на „{0}“ .
#XFLD: Message text
loadSelectedPartitionsError=Не успеа трајното снимање на податоците за избраните партиции на „{0}“.
#XFLD: Message text
viewpartitionChangedInfo=Дефиницијата на партицијата е променета од последното извршување на трајност. За да се применат промените, следното вчитување податоци ќе биде целосна слика со состојба, вклучувајќи заклучени партиции. Откако ќе заврши ова целосно вчитување, ќе можете да извршите податоци за поединечни партиции.
#XFLD: Message text
viewpartitionChangedInfoLocked=Дефиницијата на партицијата е променета од последното извршување на трајноста. За да се применат промените, следното вчитување податоци ќе биде цела слика на состојбата, освен заклучените и непроменетите опсези на партиции. Откако ќе заврши вчитувањето, ќе можете повторно да ги вчитате избраните партиции.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Репликација на табела
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Закажи репликација
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Создај распоред
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Уреди го распоредот
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Избриши го распоредот
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Вчитај нова слика на состојба
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Започни репликација на податоци
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Отстрани ги реплицираните податоци
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Овозможи пристап во реално време
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Овозможи репликација на податоци во реално време
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Оневозможи репликација на податоци во реално време
#XFLD: Message for replicate table action
replicateTableText=Репликација на табела
#XFLD: Message for replicate table action
replicateTableTextNew=Репликација на податоци
#XFLD: Message to schedule task
scheduleText=Распоред
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Трајност на приказот
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Трајност на податоците
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Вчитај нова слика на состојба
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Започни со трајност на податоци
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Отстрани ги трајно сочуваните податоци
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Партиционирана обработка
#XBUT: Label for scheduled replication
scheduledTxt=Закажано
#XBUT: Label for statistics button
statisticsTxt=Статистика
#XBUT: Label for create statistics
createStatsTxt=Создај статистика
#XBUT: Label for edit statistics
editStatsTxt=Уреди статистика
#XBUT: Label for refresh statistics
refreshStatsTxt=Освежи статистика
#XBUT: Label for delete statistics
dropStatsTxt=Избриши статистика
#XMSG: Create statistics success message
statsSuccessTxt=Започна создавањето статистика од типот {0} за {1}. 
#XMSG: Edit statistics success message
statsEditSuccessTxt=Започна промената на типот статистика во {0} за {1}. 
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Започна освежувањето на статистиката за {0}.
#XMSG: Drop statistics success message
statsDropSuccessTxt=Статистиката е успешно избришана за {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=Грешка при создавање на статистиката
#XMSG: Edit statistics error message
statsEditErrorTxt=Грешка при промена на статистиката
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Грешка при освежување на статистиката
#XMSG: Drop statistics error message
statsDropErrorTxt=Грешка при бришење на статистиката
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Дали сигурно сакате да ја избришете статистиката за податоци?
startPersistencyAdvisorLabel=Активирај го Анализаторот на прикази

#Partition related texts
#XFLD: Label for Column
column=Колона
#XFLD: Label for No of Partition
noOfPartitions=Број партиции
#XFLD: Label for Column
noOfParallelProcess=Број паралелни процеси:
#XFLD: Label text
noOfLockedPartition=Број заклучени партиции
#XFLD: Label for Partition
PARTITION=Партиции
#XFLD: Label for Column
AVAILABLE=Достапно
#XFLD: Statistics Label
statsLabel=Статистика
#XFLD: Label text
COLUMN=Колона:
#XFLD: Label text
PARALLEL_PROCESSES=Паралелни процеси:
#XFLD: Label text
Partition_Range=Опсег на партиции
#XFLD: Label text
Name=Назив
#XFLD: Label text
Locked=Заклучено
#XFLD: Label text
Others=ДРУГИ
#XFLD: Label text
Delete=Избриши
#XFLD: Label text
LoadData=Вчитај ги избраните партиции
#XFLD: Label text
LoadSelectedData=Вчитај ги избраните партиции
#XFLD: Confirmation text
LoadNewPersistenceConfirm=Ова ќе вчита нова слика со состојба за сите отклучени и променети партиции, не само за тие што сте ги избрале. Дали сакате да продолжите?
#XFLD: Label text
Continue=Продолжи

#XFLD: Label text
PARTITIONS=Партиции
#XFLD: Label text
ADD_PARTITIONS=+ Додај партиција
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange= Додај партиција
#XFLD: Label text
deleteRange=Избриши партиција
#XFLD: Label text
LOW_PLACE_HOLDER=Внесете ниска вредност
#XFLD: Label text
HIGH_PLACE_HOLDER=Внесете висока вредност
#XFLD: tooltip text
lockedTooltip=Заклучи партиција по почетно вчитување

#XFLD: Button text
Edit=Уреди
#XFLD: Button text
CANCEL=Откажи

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Последно ажурирање на статистиката
#XFLD: Statistics Fields
STATISTICS=Статистика

#XFLD:Retry label
TEXT_Retry=Обиди се повторно
#XFLD:Retry label
TEXT_Retry_tooltip=Обидете се повторно со репликација во реално време откако ќе се реши грешката.
#XFLD: text retry
Retry=Потврди
#XMG: Retry confirmation text
retryConfirmationTxt=Последната репликација во реално време заврши со грешка.\n Потврдете дека грешката е исправена и дека репликацијата во реално време може да се престартува.
#XMG: Retry success text
retrySuccess=Процесот на повторен обид започна успешно.
#XMG: Retry fail text
retryFail=Процесот на повторен обид не успеа.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Создај статистика
#XMSG: activity message for edit statistics
DROP_STATISTICS=Избриши статистика
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Освежи статистика
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Уреди статистика
#XMSG: Task log message started task
taskStarted=Задачата {0} започна. 
#XMSG: Task log message for finished task
taskFinished=Задачата {0}заврши со статус {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=Задачата {0} заврши на {2} со статус {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=Задачата {0} има влезни параметри.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=Задачата {0} заврши со неочекувана грешка. Статусот на задачата е поставен на {1}.
#XMSG: Task log message for failed task
failedToEnd=Не успеа да се постави статусот на {0} или не успеа да се отстрани блокадата.
#XMSG: Task log message
lockNotFound=Не можеме да го завршиме процесот бидејќи недостига блокадата: задачата можеби е откажана.
#XMSG: Task log message failed task
failedOverwrite=Задачата {0} е веќе заклучена од {1}. Според тоа, не успеа со следнава грешка: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Блокадата на оваа задача ја презеде друга задача.
#XMSG: Task log message failed takeover
failedTakeover=Преземањето постојна задача е неуспешно.
#XMSG: Task log message successful takeover
successTakeover=Преостанатата блокада се ослободи. Поставена е нова блокада за оваа задача.
#XMSG: Tasklog Dialog Details
txtDetails=Наредбите на далечина обработени за време на извршувањето може да се прикажат со отворање на алатката за следење прашалник на далечина во деталите за пораките специфични за партицијата.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted= SQL-наредбите на далечина се избришани од базата на податоци и не може да се прикажат.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Прашалниците на далечина што имаат врски доделени на други простори не може да се прикажат. Одете во алатката за следење прашалник на далечина и користете го ИД на наредба за да ги филтрирате.
#XMSG: Task log message for parallel check error
parallelCheckError=Задачата не може да се обработи бидејќи друга задача се извршува и веќе ја блокира оваа задача.
#XMSG: Task log message for parallel running task
parallelTaskRunning=Задача во конфликт веќе се извршува.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Статус {0} при извршување со ИД на корелација {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=Доделениот корисник ги нема потребните привилегии за извршување на оваа задача.

#XBUT: Label for open in Editor
openInEditor=Отвори во Уредникот
#XBUT: Label for open in Editor
openInEditorNew=Отвори во алатката за градење податоци
#XFLD:Run deails label
runDetails=Детали за извршувањето
#XFLD: Label for Logs
Logs=Дневници
#XFLD: Label for Settings
Settings=Поставки
#XFLD: Label for Save button
Save=Зачувај
#XFLD: Label for Standard
Standard_PO=Оптимизирано за учинок (препорачано)
#XFLD: Label for Hana low memory processing
HLMP_MO=Оптимизирано за меморија
#XFLD: Label for execution mode
ExecutionMode=Режим на извршување
#XFLD: Label for job execution
jobExecution=Режим на обработка
#XFLD: Label for Synchronous
syncExec=Синхроно
#XFLD: Label for Asynchronous
asyncExec=Асинхроно
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Користете стандардно (асинхроно, може да се промени во иднина)
#XMSG: Save settings success
saveSettingsSuccess=Режимот на извршување SAP HANA се промени.
#XMSG: Save settings failure
saveSettingsFailed=Промената на режимот на извршување SAP HANA не успеа.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Извршувањето на задачата се промени.
#XMSG: Job Execution change failed
jobExecSettingFailed=Промената на извршувањето на задачата не успеа.
#XMSG: Text for Type
typeTxt=Тип
#XMSG: Text for Monitor
monitorTxt=Следи
#XMSG: Text for activity
activityTxt=Активност
#XMSG: Text for metrics
metricsTxt=Метрика
#XTXT: Text for Task chain key
TASK_CHAINS=Синџир на задачи
#XTXT: Text for View Key
VIEWS=Прикажи
#XTXT: Text for remote table key
REMOTE_TABLES=Табела на далечина
#XTXT: Text for Space key
SPACE=Простор
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Еластичен јазол на пресметка
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Репликациски тек
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Интелигентно пребарување
#XTXT: Text for Local Table
LOCAL_TABLE=Локална табела
#XTXT: Text for Data flow key
DATA_FLOWS=Податочен тек
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=Процедура за SQL-скрипта
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=Синџир со процеси на BW
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Прикажи во алатката за следење
#XTXT: Task List header text
taskListHeader=Список со задачи ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Метриките за историските извршувања на податочниот тек не може да се повикаат.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=Деталите за целосното извршување не се вчитуваат во моментов. Обидете се да освежите.
#XFLD: Label text for the Metrices table header
metricesColLabel=Етикета на операторот
#XFLD: Label text for the Metrices table header
metricesType=Тип
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Број записи
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Изврши го синџирот од задачи
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Извршувањето на синџирот од задачи започна.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Извршувањето на синџирот од задачи започна за {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Извршувањето на синџирот од задачи не успеа.
#XTXT: Execute button label
runLabel=Изврши
#XTXT: Execute button label
runLabelNew=Започни го извршувањето
#XMSG: Filter Object header
chainsFilteredTableHeader=Филтрирано според објект: {0}
#XFLD: Parent task chain label
parentChainLabel=Синџир од надредени задачи:
#XFLD: Parent task chain unauthorized
Unauthorized=Не сте овластени за прикажување
#XFLD: Parent task chain label
parentTaskLabel=Надредена задача:
#XTXT: Task status
NOT_TRIGGERED=Не е активирано
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Влези во режимот за приказ на цел екран
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Излези од режимот за приказ на цел екран
#XTXT: Close Task log details right panel
closeRightColumn=Затворете го одделот
#XTXT: Sort Text
sortTxt=Подреди
#XTXT: Filter Text
filterTxt=Филтрирај
#XTXT: Filter by text to show list of filters applied
filterByTxt=Филтрирај според
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Повеќе од 5 минути
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Повеќе од 15 минути
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Повеќе од 1 час
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Последниот час
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Последните 24 часа
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Последниот месец
#XTXT: Messages title text
messagesText=Пораки

#XTXT Statistics information message
statisticsInfo=Не може да се создаде статистика за табели на далечина со пристап до податоци „Реплицирано“. За да создадете статистика, отстранете ги реплицираните податоци во алатката за детали за табели на далечина.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Одете во алатка за детали за табела на далечина

#XTXT: Repair latest failed run label
retryRunLabel=Обиди се повторно со последното извршување
#XTXT: Repair failed run label
retryRun=Обиди се со повторно извршување
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Обидот за повторно извршување на синџирот од задачи започна.
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Обидот за повторно извршување на синџирот од задачи не успеа.
#XMSG: Task chain child elements name
taskChainRetryChildObject=Задача {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Нова задача
#XFLD Analyzed View
analyzedView=Анализиран приказ
#XFLD Metrics
Metrics=Метрика
#XFLD Partition Metrics
PartitionMetrics=Метрика за партиции
#XFLD Entities
Messages=Дневник на задача
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Сè уште не се дефинирани партиции
#XTXT: Description message for empty partition data
partitionEmptyDescText=Создајте партиции со наведување критериуми за расчлнување на поголемите количини податоци на помали делови коишто се полесно управливи.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Сè уште нема достапни дневници
#XTXT: Description message for empty runs data
runsEmptyDescText=Кога ќе започнете нова активност (вчитување нова слика на состојбата, започнување на анализатор на прикази...), овде ќе ги видите поврзаните дневници.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=Конфигурацијата на јазолот за еластично пресметување {0}не е соодветно одржувана. Проверете ја дефиницијата.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Процесот на додавање на јазолот за еластично пресметување {0} не успеа. 
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Создавањето и активирањето на репликата, за изворниот објект „{0}." „{1}“ во јазолот за еластично пресметување {2}, е започнато.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Отстранувањето на репликата, за изворниот објект „{0}." „{1}“ од јазолот за еластично пресметување {2}, е започнато.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Создавањето и активирањето на репликата, за изворниот објект „{0}." „{1}“ не успеа.
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Отстранувањето на репликата, за изворниот објект „{0}." „{1}“ не успеа.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Насочувањето на пресметувањето на аналитичките прашалници за просторот {0} до јазолот за еластично пресметување {1} започна.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Насочувањето на пресметувањето на аналитичките прашалници за просторот {0} до соодветниот јазол за еластично пресметување не успеа.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Повторното насочување на пресметувањето на аналитичките прашалници за просторот {0} назад од јазолот за еластично пресметување {1} започна.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Повторното насочување на пресметувањето на аналитичките прашалници за просторот {0} назад до координаторот не успеа.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Синџирот на задачи {0} до соодветниот јазол за еластично пресметување {1} е активиран.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Генерирањето на синџирот на задачи за јазол за еластично пресметување {0} не успеа.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=Обезбедувањето на јазол за еластично пресметување {0}започна со дадениот план за одредување големина: vCPUs: {1}, меморија (GiB): {2}, и големина на склад (GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=Јазолот за еластично пресметување {0} е веќе обезбеден.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=Јазолот за еластично пресметување {0} е веќе отстранет.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=Операцијата не е дозволена. Запрете го јазолот за еластично пресметување {0}- U+00A0 и рестартирајте го за да го ажурирате планот за одредување на големина.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Процесот на отстранување на јазолот за еластично пресметување {0} не успеа. 
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=Тековното извршување на јазолот за еластично пресметување {0} истече.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=Во тек е проверка на статусот на јазолот за еластично пресметување {0}...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=Генерирањето на синџирот од задачи за јазолот за еластично пресметување {0} е заклучено, и синџирот {1} можеби сè уште работи.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=Изворниот објект „{0}“. „{1}“ е реплициран како зависност на приказот „{2}“.„{3}“.
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Не може да се реплицира табелата „{0}“.„{1}“, бидејќи репликацијата на табелата со редови е застарена.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Надминат е максималниот еластичен јазол на пресметка по инстанца на SAP HANA Cloud.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=Операција за извршување на јазол за еластично пресметување {0} сè уште е во тек.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Поради технички проблеми, бришењето на репликата за изворната табела {0} е запрено. Обидете се повторно подоцна.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=Поради технички проблеми, создавањето на репликата за изворната табела {0} е запрено. Обидете се повторно подоцна.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Не може да се започне еластичен јазол на пресметка бидејќи е достигнато ограничувањето за користење или бидејќи сè уште не се доделени блок-часови за пресметка.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Се вчитува синџирот од задачи и се подготвува за извршување на вкупно {0} задачи коишто се дел од овој синџир.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Веќе се извршува задача во конфликт
#XMSG: Replication will change
txt_replication_change=Типот репликација ќе се смени.
txt_repl_viewdetails=Прикажи ги деталите

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Се чини дека имало грешка при повторниот обид на последното извршување бидејќи претходното извршувањето на задачата не успеа пред да се генерира планот.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=Просторот „{0}“ е заклучен.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=Активноста {0} бара да се примени локалната табела {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=Активноста {0} бара да сe вклучи делта-снимањето за локалната табела {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=Активноста {0} бара локалната табела {1} да складира податоци во складот за објекти.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=Активноста {0} бара локалната табела {1} да складира податоци во базата на податоци.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Почнува да ги отстранува избришаните записи за локалната табела {0}.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Почнува да ги брише сите записи за локалната табела {0}.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Почнува да брише записи за локалната табела {0} според условот на филтерот {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Се појави грешка при отстранувањето на избришаните записи за локалната табела {0}.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Се појави грешка при бришењето на сите записи за локалната табела {0}.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Бришење на сите целосно обработени записи со промена на типот „Избришани“, кои се постари од {0} дена.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Бришење на сите целосно обработени записи со промена на типот „Избришани“, кои се постари од {0}.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} записи се избришани.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} записи се означени за бришење.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=Отстранувањето на избришаните записи за локалната табела {0} е завршено.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Бришењето на сите записи за локалната табела {0} е завршено.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Почнува да ги означува записите како „Избришани“ за локалната табела {0}.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Почнува да ги означува записите како „Избришани“ за локалната табела {0} според условот на филтерот {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Означувањето на записите како „Избришани“ за локалната табела {0} е завршено.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Промените на податоците привремено се вчитуваат во табелата {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Промените на податоците се привремено вчитани во табелата {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Промените на податоците се обработени и избришани од табелата {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Детали за врската.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Започнува оптимизирањето на локалната табела (датотека).
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Настана грешка при оптимизирањето на локалната табела (датотека).
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Настана грешка. Оптимизирањето на локалната табела (датотека) е запрено.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Се оптимизира локалната табела (датотека)...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=Локалната табела (датотека) е оптимизирана.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=Локалната табела (датотека) е оптимизирана.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=Табелата е оптимизирана со колоните во Z-редослед: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Настана грешка. Кратењето на локалната табела (датотека) е запрено.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Се крати локалната табела (датотека)...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=Влезната локација на меѓумеморијата за локалната табела (датотека) е отстранета.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Започнува чистењето (бришење на сите целосно обработени записи) на локалната табела (датотека).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Настана грешка при чистењето на локалната табела (датотека).
#XMSG: Task log message
LTF_VACUUM_STOPPED=Настана грешка. Чистењето на локалната табела (датотека) е запрено.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Се чисти локалната табела (датотека)...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=Чистењето е завршено.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Бришење на сите целосно обработени записи постари од {0} дена.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Бришење на сите целосно обработени записи постари од {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Започнува спојувањето на новите записи со локалната табела (датотека).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Почнува да ги спојува новите записи со локалната табела (датотека) користејќи ја поставката „Спој податоци автоматски“.
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Започнува спојување нови записи со локалната табела (датотека). Оваа задача е иницирана преку барањето за спојување со помош на API.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Се спојуваат новите записи со локалната табела (датотека).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Настана грешка при спојување на новите записи со локалната табела (датотека).
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=Локалната табела (датотека) е споена.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Настана грешка. Спојувањето на локалната табела (датотека) е запрено.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=Спојувањето на локалната табела (датотека) не успеа поради грешка, но операцијата делумно успеа и некои податоци веќе се споени.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Истече времето. Активноста {0} се извршува {1} часа.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=Асинхроното извршување не може да започне поради големо оптоварување на системот. Отворете ја „Алатката за следење на системот“ и проверете ги задачите што се извршуваат.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=Асинхроното извршување е откажано.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=Задачата {0} се изврши во рамките на ограничувањето на меморијата меѓу {1} и {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=Задачата {0} се извршуваше со ИД на ресурсот {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=Задачата Најди и замени започна во локалната табела (датотека).
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Задачата Најди и замени заврши во локалната табела (датотека).
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Задачата Најди и замени не успеа во локалната табела (датотека).

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Настана грешка. Ажурирањето на статистиката за локалната табела (датотека) е запрено.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=Задачата не успеа поради грешка со недостаток на меморија во базата на податоци SAP HANA.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=Задачата не успеа поради одбивање на контролите кон SAP HANA.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=Задачата не успеа поради премногу активни врски на SAP HANA.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=Операцијата за повторување не може да се изврши бидејќи повторувањата се дозволени само за наредениот елемент на вгнездениот синџир со задачи.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Дневниците на неуспешните подредени задачи веќе не се достапни за да се продолжи со повторниот обид.


####Metrics Labels

performanceOptimized=Оптимизирано за учинок
memoryOptimized=Оптимизирано за меморија

JOB_EXECUTION=Извршување на задачата
EXECUTION_MODE=Режим на извршување
NUMBER_OF_RECORDS_OVERALL=Број на трајни записи
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Број на записи прочитани од далечински извор
RUNTIME_MS_REMOTE_EXECUTION_TIME=Време на изворна далечинска обработка
MEMORY_CONSUMPTION_GIB=Максимална меморија на базата со податоци SAP HANA
NUMBER_OF_PARTITIONS=Број на партиции
MEMORY_CONSUMPTION_GIB_OVERALL=Максимална меморија на базата со податоци SAP HANA
NUMBER_OF_PARTITIONS_LOCKED=Број на заклучени партиции
PARTITIONING_COLUMN=Колона за поделба на партиции
HANA_PEAK_CPU_TIME=Вкупно време на CPU за SAP HANA
USED_IN_DISK=Искористен склад
INPUT_PARAMETER_PARAMETER_VALUE=Влезен параметар
INPUT_PARAMETER=Влезен параметар
ECN_ID=Назив на еластичниот јазол на пресметка

DAC=Контроли за пристап до податоци
YES=Да
NO=Не
noofrecords=Број на записи
partitionpeakmemory=Максимална меморија на базата со податоци SAP HANA
value=Вредност
metricsTitle=Метрика ({0})
partitionmetricsTitle=Партиции ({0})
partitionLabel=Партиција
OthersNotNull=Вредностите не се вклучени во опсезите
OthersNull=Без вредност
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Поставки што се користат за последно извршување трајно зачувување на податоците:
#XMSG: Message for input parameter name
inputParameterLabel=Влезен параметар
#XMSG: Message for input parameter value
inputParameterValueLabel=Вредност
#XMSG: Message for persisted data
inputParameterPersistedLabel=Трајно зачувано во
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Избриши ги податоците
REMOVE_DELETED_RECORDS=Отстрани ги избришаните записи
MERGE_FILES=Спој ги датотеките
OPTIMIZE_FILES=Оптимизирај ги датотеките
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Прикажи во алатката за следење на SAP BW Bridge

ANALYZE_PERFORMANCE=Анализирај го учинокот
CANCEL_ANALYZE_PERFORMANCE=Откажи ја анализата на учинокот

#XFLD: Label for frequency column
everyLabel=Секој
#XFLD: Plural Recurrence text for Hour
hoursLabel=Часови
#XFLD: Plural Recurrence text for Day
daysLabel=Денови
#XFLD: Plural Recurrence text for Month
monthsLabel=Месеци
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Минути

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Прикажи го водичот за решавање проблеми со трајноста</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Недостига меморија во процесот. Не може трајно да се зачуваат податоците за приказот „{0}“. Консултирајте се со порталот за помош за повеќе информации во врска со грешки при недостиг на меморија. Проверете го анализаторот на прикази за сложеноста.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Не е применливо
OPEN_BRACKET=(
CLOSE_BRACKET=)
