
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Butiran Log
#XFLD: Header
TASK_LOGS=Log Tugas ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Jalanan ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Butiran Paparan
#XFLD: Button text
STOP=Hentikan Jalanan
#XFLD: Label text
RUN_START=Permulaan Jalanan Akhir
#XFLD: Label text
RUN_END=Pengakhiran Jalanan Akhir
#XFLD: Label text
RUNTIME=Jangka masa
#XTIT: Count for Messages
txtDetailMessages=Mesej ({0})
#XFLD: Label text
TIME=Cap Waktu
#XFLD: Label text
MESSAGE=Mesej
#XFLD: Label text
TASK_STATUS=Kategori
#XFLD: Label text
TASK_ACTIVITY=Aktiviti
#XFLD: Label text
RUN_START_DETAILS=Mula
#XFLD: Label text
RUN_END_DETAILS=Tamat
#XFLD: Label text
LOGS=Jalanan
#XFLD: Label text
STATUS=Status
#XFLD: Label text
RUN_STATUS=Status Jalanan
#XFLD: Label text
Runtime=Jangka masa
#XFLD: Label text
RuntimeTooltip=Jangka masa (hh : mm : ss)
#XFLD: Label text
TRIGGEREDBY=Dipicu oleh
#XFLD: Label text
TRIGGEREDBYNew=Dijalankan oleh
#XFLD: Label text
TRIGGEREDBYNewImp=Jalanan Dimulakan oleh
#XFLD: Label text
EXECUTIONTYPE=Jenis Pelaksanaan
#XFLD: Label text
EXECUTIONTYPENew=Jenis Jalanan
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Ruang Rantaian Induk
#XFLD: Refresh tooltip
TEXT_REFRESH=Segar Semula
#XFLD: view Details link
VIEW_ERROR_DETAILS=Butiran Paparan
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Muat turun Butiran Tambahan
#XMSG: Download completed
downloadStarted=Muat Turun Dimulakan
#XMSG: Error while downloading content
errorInDownload=Ralat berlaku ketika memuat turun.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Butiran Paparan
#XBTN: cancel button of task details dialog
TXT_CANCEL=Batalkan
#XBTN: back button from task details
TXT_BACK=Kembali
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Tugas Selesai
#XFLD: Log message with failed status
MSG_LOG_FAILED=Tugas Gagal
#XFLD: Master and detail table with no data
No_Data=Tiada Data
#XFLD: Retry tooltip
TEXT_RETRY=Cuba semula
#XFLD: Cancel Run label
TEXT_CancelRun=Batalkan Jalanan
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Pengosongan Muatan Tidak Berjaya
#XMSG:button copy sql statement
txtSQLStatement=Salin Penyata SQL
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Buka Pemantau Pertanyaan Jauh
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=Untuk memaparkan penyata SQL jauh, klik pada "Buka Pemantau Pertanyaan Jauh".
#XMSG:button ok
txtOk=Ok
#XMSG: button close
txtClose=Tutup
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=Pembatalan tindakan jalanan untuk objek "{0}" telah bermula.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=Pembatalan tindakan jalanan untuk objek "{0}" tidak berjaya.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=Pembatalan tindakan jalanan untuk objek "{0}" tidak lagi boleh dilakukan kerana status replikasi berubah.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Tiada log tugas yang mempunyai status Berjalan.
#XMSG: message for conflicting task
Task_Already_Running=Percanggahan tugas telah berjalan untuk objek "{0}".
#XFLD: Label for no task log with running state title
actionInfo=Maklumat Tindakan
#XMSG Copied to clipboard
copiedToClip=Disalin ke Papan Klip
#XFLD copy
Copy=Salin
#XFLD copy correlation ID
CopyCorrelationID=Salin ID Korelasi
#XFLD Close
Close=Tutup
#XFLD: show more Label
txtShowMore=Tunjuk Lebih
#XFLD: message Label
messageLabel=Mesej:
#XFLD: details Label
detailsLabel=Butiran:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Melaksanakan Penyata \r\n SQL:
#XFLD:statementId Label
statementIdLabel=ID Kenyataan:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Bilangan Penyata SQL \r\n Jauh:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Bilangan Penyata
#XFLD: Space Label
txtSpaces=Ruang
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Ruang Tidak Dibenarkan
#XFLD: Privilege Error Text
txtPrivilegeError=Anda perlukan keistimewaan yang mencukupi untuk memaparkan data ini.
#XFLD: Label for Object Header
DATA_ACCESS=Capaian Data
#XFLD: Label for Object Header
SCHEDULE=Jadual
#XFLD: Label for Object Header
DETAILS=Butiran
#XFLD: Label for Object Header
LATEST_UPDATE=Kemas Kini Terkini
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Perubahan Terkini (Sumber)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Kekerapan Segar Semula
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Kekerapan Berjadual
#XFLD: Label for Object Header
NEXT_RUN=Jalanan Seterusnya
#XFLD: Label for Object Header
CONNECTION=Sambungan
#XFLD: Label for Object Header
DP_AGENT=Ejen DP
#XFLD: Label for Object Header
USED_IN_MEMORY=Ingatan Digunakan untuk Storan (MiB)
#XFLD: Label for Object Header
USED_DISK=Cakera Digunakan untuk Storan (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Saiz In-Memory (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Saiz pada Cakera (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Bilangan Rekod

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Tetapkan kepada Gagal
SET_TO_FAILED_ERR=Tugas sedang berjalan tetapi pengguna menetapkan status tugas kepada GAGAL.
#XFLD: Label for stopped failed
FAILLOCKED=Jalanan Sedang Berjalan
#XFLD: sub status STOPPED
STOPPED=Dihentikan
STOPPED_ERR=Tugas dihentikan, tetapi tiada rollback dilaksanakan.
#XFLD: sub status CANCELLED
CANCELLED=Dibatalkan
CANCELLED_ERR=Tugas ini dibatalkan, selepas ia dimulakan. Dalam kes ini, data telah digulung balik dan dipulihkan kepada keadaan yang wujud sebelum jalanan tugas pada mulanya dicetuskan.
#XFLD: sub status LOCKED
LOCKED=Dikunci
LOCKED_ERR=Tugas sama telah berjalan, jadi tidak boleh jalankan tugas ini selari dengan pelaksanaan tugas sedia ada.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Pengecualian Tugas
TASK_EXCEPTION_ERR=Tugas ini mengalami ralat tidak ditentukan semasa pelaksanaan.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Pengecualian Pelaksanaan Tugas
TASK_EXECUTE_EXCEPTION_ERR=Tugasan ini mengalami ralat semasa pelaksanaan.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Tiada sah kuasa
UNAUTHORIZED_ERR=Pengguna ini tidak boleh disahkan, telah dikunci, atau dipadam.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Tidak dibenarkan
FORBIDDEN_ERR=Pengguna yang telah anda umpukkan tidak mempunyai keistimewaan yang mencukupi untuk melaksanakan tugas ini.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Tidak Dipicu
FAIL_NOT_TRIGGERED_ERR=Kerja tugas ini tidak berjaya dilaksanakan kerana gangguan sistem atau beberapa bahagian sistem pangkalan data tidak tersedia pada masa pelaksanaan yang anda rancang. Tunggu masa pelaksanaan kerja yang anda jadualkan seterusnya atau jadualkan semula kerja.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Jadual Dibatalkan
SCHEDULE_CANCELLED_ERR=Kerja tugas ini tidak berjaya dilaksanakan kerana ralat dalaman. Hubungi SAP Support dan sediakan mereka id korelasi dan cap waktu daripada maklumat butiran log kerja tugas ini.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Jalanan Sebelumnya dalam Proses
SUCCESS_SKIPPED_ERR=Anda belum memicu pelaksanaan tugas ini kerana jalanan sebelum tugas yang sama sedang berjalan.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Pemilik Tiada
FAIL_OWNER_MISSING_ERR=Kerja tugas ini tidak berjaya dilaksanakan kerana ia tiada pengguna sistem diumpukkan. Umpukkan pengguna pemilik kepada kerja.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Persetujuan Tidak Tersedia
FAIL_CONSENT_NOT_AVAILABLE_ERR=Anda belum membenarkan SAP untuk menjalankan rantaian tugas atau jadualkan tugas penyepaduan data bagi pihak anda. Pilih pilihan disediakan untuk memberikan persetujuan anda.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Persetujuan Telah Tamat Tempoh
FAIL_CONSENT_EXPIRED_ERR=Sah kuasa yang membenarkan SAP untuk menjalankan rantaian tugas atau menjadualkan tugas penyepaduan bagi pihak anda telah tamat tempoh. Pilih pilihan disediakan untuk memperbaharui persetujuan anda.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Persetujuan Ditaksahkan
FAIL_CONSENT_INVALIDATED_ERR=Tugas ini tidak berjaya dilaksanakan, biasanya kerana perubahan dalam konfigurasi Pembekal Identiti penyewa. Dalam kes itu, tiada kerja tugas baharu yang anda boleh jalankan atau jadualkan bagi nama pengguna terjejas. Jika pengguna yang anda umpukkan masih wujud dalam IdP baharu, batalkan persetujuan penjadualan dan berikannya semula. Jika pengguna yang anda umpukkan tidak lagi wujud, umpukkan pemilik kerja tugas baharu dan sediakan persetujuan penjadualan tugas yang anda perlukan. Lihat nota SAP berikut: https://launchpad.support.sap.com/#/notes/3089828 untuk maklumat lanjut.
TASK_EXECUTOR_ERROR=Pelaksana Tugas
TASK_EXECUTOR_ERROR_ERR=Tugas ini mengalami ralat dalaman, biasanya semasa langkah persediaan untuk pelaksanaan dan tidak boleh mulakan tugas.
PREREQ_NOT_MET=Anda perlu penuhi prasyarat
PREREQ_NOT_MET_ERR=Tidak boleh menjalankan tugas kerana masalah dalam takrifannya. Contohnya, anda belum mengatur duduk objek, rantaian tugas mengandungi logik pekeliling atau memerlukan SQL paparan yang sah.
RESOURCE_LIMIT_ERROR=Ralat Had Sumber
RESOURCE_LIMIT_ERROR_ERR=Pada masa ini tidak dapat melaksanakan tugas kerana sumber yang mencukupi tidak tersedia atau sibuk.
FAIL_CONSENT_REFUSED_BY_UMS=Persetujuan Ditolak
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Tugas ini tidak dapat dilaksanakan, dalam jalanan berjadual atau rantaian tugas, kerana perubahan dalam konfigurasi Pembekal Identiti pengguna pada penyewa. Untuk maklumat lanjut, lihat nota SAP berikut: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Dijadualkan
#XFLD: status text
SCHEDULEDNew=Tetap
#XFLD: status text
PAUSED=Dihentikan Sementara
#XFLD: status text
DIRECT=Langsung
#XFLD: status text
MANUAL=Manual
#XFLD: status text
DIRECTNew=Mudah
#XFLD: status text
COMPLETED=Selesai
#XFLD: status text
FAILED=Gagal
#XFLD: status text
RUNNING=Sedang Berjalan
#XFLD: status text
none=Tiada
#XFLD: status text
realtime=Masa Nyata
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Subtugas
#XFLD: New Data available in the file
NEW_DATA=Data Baharu

#XFLD: text for values shown in column Replication Status
txtOff=Mati
#XFLD: text for values shown in column Replication Status
txtInitializing=Memulakan
#XFLD: text for values shown in column Replication Status
txtLoading=Memuat
#XFLD: text for values shown in column Replication Status
txtActive=Aktif
#XFLD: text for values shown in column Replication Status
txtAvailable=Tersedia
#XFLD: text for values shown in column Replication Status
txtError=Ralat
#XFLD: text for values shown in column Replication Status
txtPaused=Dihentikan Sementara
#XFLD: text for values shown in column Replication Status
txtDisconnected=Terputus
#XFLD: text for partially Persisted views
partiallyPersisted=Berterusan Sebahagian

#XFLD: activity text
REPLICATE=Replikakan
#XFLD: activity text
REMOVE_REPLICATED_DATA=Keluarkan Data Direplikakan
#XFLD: activity text
DISABLE_REALTIME=Nyahdayakan Replikasi Data Masa Nyata
#XFLD: activity text
REMOVE_PERSISTED_DATA=Keluarkan Data Berterusan
#XFLD: activity text
PERSIST=Teruskan
#XFLD: activity text
EXECUTE=Laksanakan
#XFLD: activity text
TASKLOG_CLEANUP=Pembersihan_Tacklog
#XFLD: activity text
CANCEL_REPLICATION=Batalkan Replikasi
#XFLD: activity text
MODEL_IMPORT=Import Model
#XFLD: activity text
NONE=Tiada
#XFLD: activity text
CANCEL_PERSISTENCY=Batalkan Keterusan
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Menganalisis Paparan
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Batalkan Penganalisis Paparan

#XFLD: severity text
INFORMATION=Maklumat
#XFLD: severity text
SUCCESS=Berjaya
#XFLD: severity text
WARNING=Amaran
#XFLD: severity text
ERROR=Ralat
#XFLD: text for values shown for Ascending sort order
SortInAsc=Isih Menaik
#XFLD: text for values shown for Descending sort order
SortInDesc=Isih Menurun
#XFLD: filter text for task log columns
Filter=Tapis
#XFLD: object text for task log columns
Object=Objek
#XFLD: space text for task log columns
crossSpace=Ruang

#XBUT: label for remote data access
REMOTE=Jauh
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Direplikakan (Masa Nyata)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Direplikakan (Snapshot)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=Replikasi masa nyata disekat disebabkan ralat. Setelah ralat diperbetulkan, anda boleh menggunakan tindakan “Cuba semula” untuk teruskan dengan replikasi masa nyata.
ERROR_MSG=Replikasi masa nyata disekat disebabkan ralat.
RETRY_FAILED_ERROR=Cuba semula proses tidak berjaya dengan ralat.
LOG_INFO_DETAILS=Tiada log dijanakan apabila data direplikasikan dalam mod masa nyata. Log terpapar berkait dengan tindakan sebelumnya.

#XBUT: Partitioning label
partitionMenuText=Bahagian
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Cipta Bahagian
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Edit Bahagian
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Padam Bahagian
#XFLD: Initial text
InitialPartitionText=Takrifkan bahagian dengan menentukan kriteria untuk membahagikan dataset yang besar kepada set yang lebih kecil.
DefinePartition=Takrifkan Bahagian
#XFLD: Message text
partitionChangedInfo=Takrifan bahagian telah berubah sejak replikasi terakhir. Perubahan akan digunakan pada muatan data seterusnya.
#XFLD: Message text
REAL_TIME_WARNING=Anda hanya boleh gunakan pembahagian apabila memuatkan snapshot baharu. Tidak boleh gunakan untuk replikasi masa nyata.
#XFLD: Message text
loadSelectedPartitions=Mula meneruskan data untuk bahagian "{0}" yang anda pilih
#XFLD: Message text
loadSelectedPartitionsError=Cuba meneruskan data untuk bahagian "{0}" yang anda pilih semula
#XFLD: Message text
viewpartitionChangedInfo=Takrifan bahagian telah berubah sejak jalanan keterusan terakhir. Untuk menggunakan perubahan, muatan data seterusnya akan menjadi snapshot penuh termasuk bahagian yang terkunci. Selepas muatan penuh ini selesai, anda boleh menjalankan data untuk bahagian tunggal.
#XFLD: Message text
viewpartitionChangedInfoLocked=Takrifan bahagian telah berubah sejak jalanan keterusan terakhir. Untuk menggunakan perubahan, muatan data seterusnya akan menjadi snapshot penuh kecuali untuk julat bahagian tidak berubah dan yang terkunci. Selepas muatan ini selesai, anda boleh Memuatkan Pembahagian Dipilih sekali lagi.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Replikasi Jadual
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Jadualkan Replikasi
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Cipta Jadual
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Edit Jadual
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Padam Jadual
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Muat Snapshot Baharu
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Mulakan Replikasi Data
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Keluarkan Data Direplikakan
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Dayakan Capaian Masa Nyata
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Dayakan Replikasi Data Masa Nyata
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Nyahdayakan Replikasi Data Masa Nyata
#XFLD: Message for replicate table action
replicateTableText=Replikasi Jadual
#XFLD: Message for replicate table action
replicateTableTextNew=Replikasi Data
#XFLD: Message to schedule task
scheduleText=Jadual
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Keterusan Paparan
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Keterusan Data
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Muat Snapshot Baharu
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Mulakan Keterusan Data
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Keluarkan Data Berterusan
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Pemprosesan Bahagian
#XBUT: Label for scheduled replication
scheduledTxt=Dijadualkan
#XBUT: Label for statistics button
statisticsTxt=Statistik
#XBUT: Label for create statistics
createStatsTxt=Cipta Statistik
#XBUT: Label for edit statistics
editStatsTxt=Edit Statistik
#XBUT: Label for refresh statistics
refreshStatsTxt=Segar Semula Statistik
#XBUT: Label for delete statistics
dropStatsTxt=Padam Statistik
#XMSG: Create statistics success message
statsSuccessTxt=Mula mencipta statistik jenis {0} untuk {1}.
#XMSG: Edit statistics success message
statsEditSuccessTxt=Mula mengubah jenis statistik ke {0} untuk {1}.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Mula menyegar semula statistik untuk {0}.
#XMSG: Drop statistics success message
statsDropSuccessTxt=Statistik berjaya dipadamkan untuk {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=Ralat semasa mencipta statistik
#XMSG: Edit statistics error message
statsEditErrorTxt=Ralat semasa mengubah statistik
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Ralat semasa menyegar semula statistik
#XMSG: Drop statistics error message
statsDropErrorTxt=Ralat semasa memadam Statistik
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Adakah anda pasti ingin memadam statistik data?
startPersistencyAdvisorLabel=Mulakan Penganalisis Paparan

#Partition related texts
#XFLD: Label for Column
column=Lajur
#XFLD: Label for No of Partition
noOfPartitions=Bilangan Bahagian
#XFLD: Label for Column
noOfParallelProcess=Bilangan Proses Selari
#XFLD: Label text
noOfLockedPartition=Bilangan Bahagian Dikunci
#XFLD: Label for Partition
PARTITION=Bahagian
#XFLD: Label for Column
AVAILABLE=Tersedia
#XFLD: Statistics Label
statsLabel=Statistik
#XFLD: Label text
COLUMN=Lajur:
#XFLD: Label text
PARALLEL_PROCESSES=Proses Selari:
#XFLD: Label text
Partition_Range=Julat Petakan
#XFLD: Label text
Name=Nama
#XFLD: Label text
Locked=Dikunci
#XFLD: Label text
Others=LAIN-LAIN
#XFLD: Label text
Delete=Padam
#XFLD: Label text
LoadData=Muatkan Pembahagian Dipilih
#XFLD: Label text
LoadSelectedData=Muatkan Pembahagian Dipilih
#XFLD: Confirmation text
LoadNewPersistenceConfirm=Ini akan memuatkan snapshot baharu untuk semula bahagian yang anda telah buka kunci dan ubah, bukan sahaja yang anda telah pilih. Anda ingin teruskan?
#XFLD: Label text
Continue=Teruskan

#XFLD: Label text
PARTITIONS=Bahagian
#XFLD: Label text
ADD_PARTITIONS=+ Tambah Bahagian
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Tambah Bahagian
#XFLD: Label text
deleteRange=Padam Bahagian
#XFLD: Label text
LOW_PLACE_HOLDER=Masukkan nilai rendah
#XFLD: Label text
HIGH_PLACE_HOLDER=Masukkan nilai tinggi
#XFLD: tooltip text
lockedTooltip=Kunci bahagian selepas muatan awal

#XFLD: Button text
Edit=Edit
#XFLD: Button text
CANCEL=Batalkan

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Kemas Kini Statistik Terakhir
#XFLD: Statistics Fields
STATISTICS=Statistik

#XFLD:Retry label
TEXT_Retry=Cuba semula
#XFLD:Retry label
TEXT_Retry_tooltip=Cuba semula replikasi masa nyata selepas ralat diselesaikan.
#XFLD: text retry
Retry=Sahkan
#XMG: Retry confirmation text
retryConfirmationTxt=Replikasi masa nyata terakhir ditamatkan dengan ralat.\n Sahkan bahawa ralat diperbetulkan dan replikasi masa nyata boleh dimulakan semula.
#XMG: Retry success text
retrySuccess=Proses cubaan semula berjaya dimulakan.
#XMG: Retry fail text
retryFail=Proses cubaan semula gagal.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Cipta Statistik
#XMSG: activity message for edit statistics
DROP_STATISTICS=Padam Statistik
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Segar Semula Statistik
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Edit Statistik
#XMSG: Task log message started task
taskStarted=Tugas {0} telah bermula.
#XMSG: Task log message for finished task
taskFinished=Tugas {0} ditamatkan dengan status {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=Tugas {0} berakhir pada {2} dengan status {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=Tugas {0} mempunyai parameter input.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=Tugas {0} ditamatkan dengan ralat tidak dijangka. Status tugas telah ditetapkan kepada {1}.
#XMSG: Task log message for failed task
failedToEnd=Gagal menetapkan status kepada {0} atau gagal mengeluarkan kunci.
#XMSG: Task log message
lockNotFound=Proses tidak boleh diselesaikan kerana kunci hilang: tugas mungkin telah dibatalkan.
#XMSG: Task log message failed task
failedOverwrite=Tugas {0} telah dikunci oleh {1}. Oleh itu, ia gagal dengan ralat berikut: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Kunci tugas telah diambil alih oleh tugas lain.
#XMSG: Task log message failed takeover
failedTakeover=Gagal mengambil alih tugas sedia ada.
#XMSG: Task log message successful takeover
successTakeover=Kunci sebelah kiri perlu dikeluarkan. Kunci baharu untuk tugas ini telah ditetapkan.
#XMSG: Tasklog Dialog Details
txtDetails=Anda boleh paparkan penyata jauh yang anda proses semasa jalanan dengan membuka pemantau pertanyaan jauh, dalam butiran mesej tertentu bahagian.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=Anda telah padamkan penyata SQL jauh daripada pangkalan data dan tidak boleh paparkannya.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Tidak boleh paparkan pertanyaan jauh yang mempunyai umpukan sambungan ke ruang lain. Pergi ke Pemantau Pertanyaan Jauh dan gunakan ID penyata untuk menapisnya.
#XMSG: Task log message for parallel check error
parallelCheckError=Tugas tidak boleh diproses kerana tugas lain sedang dijalankan dan sudah menyekat tugas ini.
#XMSG: Task log message for parallel running task
parallelTaskRunning=Percanggahan tugas telah berjalan.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} ketika jalanan dengan ID korelasi {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=Pengguna yang telah anda umpukkan tidak mempunyai keistimewaan yang mencukupi untuk melaksanakan tugas ini.

#XBUT: Label for open in Editor
openInEditor=Buka dalam Editor
#XBUT: Label for open in Editor
openInEditorNew=Buka dalam Pembina Data
#XFLD:Run deails label
runDetails=Butiran Jalanan
#XFLD: Label for Logs
Logs=Log
#XFLD: Label for Settings
Settings=Tetapan
#XFLD: Label for Save button
Save=Simpan
#XFLD: Label for Standard
Standard_PO=Prestasi Dioptimumkan (Dicadangkan)
#XFLD: Label for Hana low memory processing
HLMP_MO=Ingatan Dioptimumkan
#XFLD: Label for execution mode
ExecutionMode=Mod Jalanan
#XFLD: Label for job execution
jobExecution=Mod Pemprosesan
#XFLD: Label for Synchronous
syncExec=Segerak
#XFLD: Label for Asynchronous
asyncExec=Tak Segerak
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Gunakan Lalai (Tak segerak, mungkin berubah pada masa hadapan)
#XMSG: Save settings success
saveSettingsSuccess=Anda telah ubah Mod Pelaksanaan SAP HANA.
#XMSG: Save settings failure
saveSettingsFailed=Ubah semula Mod Pelaksanaan SAP HANA.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Anda telah ubah Pelaksanaan Kerja.
#XMSG: Job Execution change failed
jobExecSettingFailed=Ubah semula Pelaksanaan Kerja.
#XMSG: Text for Type
typeTxt=Jenis
#XMSG: Text for Monitor
monitorTxt=Pemantau
#XMSG: Text for activity
activityTxt=Aktiviti
#XMSG: Text for metrics
metricsTxt=Metrik
#XTXT: Text for Task chain key
TASK_CHAINS=Rantaian Tugas
#XTXT: Text for View Key
VIEWS=Papar
#XTXT: Text for remote table key
REMOTE_TABLES=Jadual Jauh
#XTXT: Text for Space key
SPACE=Ruang
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Nod Kira Elastik
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Aliran Replikasi
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Carian Pintar
#XTXT: Text for Local Table
LOCAL_TABLE=Jadual Tempatan
#XTXT: Text for Data flow key
DATA_FLOWS=Aliran Data
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=Tatacara Skrip SQL
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=Rantaian Proses BW
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Papar dalam Pemantau
#XTXT: Task List header text
taskListHeader=Senarai Tugas ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Cuba terbitkan metrik jalanan sejarah bagi aliran data kemudian.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=Butiran jalanan lengkap tidak dimuat pada masa ini. Cuba segar semula.
#XFLD: Label text for the Metrices table header
metricesColLabel=Label Pengendali
#XFLD: Label text for the Metrices table header
metricesType=Jenis
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Kiraan Rekod
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Jalankan Rantaian Tugas
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Jalanan rantaian tugas telah bermula.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Jalanan rantaian tugas telah bermula untuk {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Cuba semula jalanan rantaian tugas kemudian.
#XTXT: Execute button label
runLabel=Jalanan
#XTXT: Execute button label
runLabelNew=Mulakan Jalanan
#XMSG: Filter Object header
chainsFilteredTableHeader=Ditapis mengikut Objek: {0}
#XFLD: Parent task chain label
parentChainLabel=Rantaian Tugas Induk:
#XFLD: Parent task chain unauthorized
Unauthorized=Memerlukan Kebenaran untuk Paparkan
#XFLD: Parent task chain label
parentTaskLabel=Tugas Induk:
#XTXT: Task status
NOT_TRIGGERED=Tidak Dipicu
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Masuk Mod Skrin Penuh
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Keluar Mod Skrin Penuh
#XTXT: Close Task log details right panel
closeRightColumn=Tutup Bahagian
#XTXT: Sort Text
sortTxt=Isih
#XTXT: Filter Text
filterTxt=Tapis
#XTXT: Filter by text to show list of filters applied
filterByTxt=Tapis mengikut
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Lebih daripada 5 Minit
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Lebih daripada 15 Minit
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Lebih daripada 1 Jam
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Satu Jam Terakhir
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=24 Jam Terakhir
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Bulan Lepas
#XTXT: Messages title text
messagesText=Mesej

#XTXT Statistics information message
statisticsInfo=Statistik tidak boleh dicipta untuk jadual jauh dengan capaian data "Direplikakan". Untuk mencipta statistik, keluarkan data direplikakan dalam Pemantau Butiran Jadual Jauh.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Pergi ke Pemantau Butiran Jadual Jauh

#XTXT: Repair latest failed run label
retryRunLabel=Cuba Semula Jalanan Terkini
#XTXT: Repair failed run label
retryRun=Cuba Semula Jalanan
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Jalanan cubaan semula rantaian tugas telah bermula.
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Jalanan cubaan semula rantaian tugas telah gagal
#XMSG: Task chain child elements name
taskChainRetryChildObject=Tugas {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Tugas Baharu
#XFLD Analyzed View
analyzedView=Paparan Dianalisis
#XFLD Metrics
Metrics=Metrik
#XFLD Partition Metrics
PartitionMetrics=Metrik Bahagian
#XFLD Entities
Messages=Log Tugas
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Tiada pembahagian yang anda telah takrifkan lagi
#XTXT: Description message for empty partition data
partitionEmptyDescText=Cipta pembahagian dengan menentukan kriteria untuk memisahkan jumlah data yang lebih besar kepada yang lebih kecil, bahagian yang lebih mudah diurus.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Tiada log tersedia lagi
#XTXT: Description message for empty runs data
runsEmptyDescText=Apabila anda memulakan aktiviti baharu (Muatkan snapshot baharu, mulakan penganalisis paparan...) anda akan melihat log yang berkaitan di sini.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=Konfigurasi nod kira elastik {0} tidak diselenggara dengan sewajarnya. Semak takrifan anda.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Proses untuk menambah nod kira elastik {0} gagal.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Penciptaan dan pengaktifan replikasi untuk objek sumber "{0}"."{1}" dalam nod kira elastik {2} telah bermula.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Mengeluarkan replikasi untuk objek sumber "{0}"."{1}" daripada nod kira elastik {2} telah bermula.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Cuba semula penciptaan dan pengaktifan replikasi untuk objek sumber "{0}"."{1}".
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Cuba semula pengeluaran replikasi untuk objek sumber "{0}"."{1}".
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Penghalaan pengiraan pertanyaan analisis ruang {0} kepada nod kira elastik {1} bermula.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Penghalaan pengiraan pertanyaan analisis ruang {0} kepada nod kira elastik yang sepadan gagal.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Penghalaan semula pengiraan pertanyaan analisis ruang {0} daripada nod kira elastik {1} bermula.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Penghalaan semula pengiraan pertanyaan analisis ruang {0} kepada koordinator gagal.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Rantaian tugas {0} kepada nod kira elastik yang sepadan {1} dipicu.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Cuba semula penjanaan rantaian tugas untuk nod kira elastik {0}.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=Peruntukan untuk nod kira elastik {0} telah bermula dengan rancangan pensaizan yang diberikan: vCPU: {1}, ingatan (GiB): {2} dan saiz storan (GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=Anda telah memperuntukkan nod kira elastik {0}.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=Anda telah nyahperuntukkan nod kira elastik {0}.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=Tiada kebenaran untuk operasi. Sila hentikan nod kira elastik {0} dan mulakannya semula untuk mengemas kini rancangan saiz.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Cuba semula proses untuk mengeluarkan nod kira elastik {0}.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=Jalanan semasa bagi nod kira elastik {0} telah tamat.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=Semakan status nod kira elastik {0} sedang berjalan...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=Penjanaan rantaian tugas untuk nod kira elastik {0} terkunci, oleh itu rantaian {1} mungkin masih berjalan.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=Anda telah mereplikasi objek sumber "{0}"."{1}" sebagai kebersandaran paparan "{2}"."{3}".
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Cuba replikasi jadual "{0}"."{1}" kemudian kerana replikasi jadual baris dikecam.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Nod kira elastik maksimum setiap tika SAP HANA Cloud telah terlebih.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=Operasi jalanan untuk nod kira elastik {0} masih sedang dijalankan.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Disebabkan masalah teknikal, pemadaman replika untuk jadual sumber {0} dihentikan. Sila cuba semula kemudian.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=Disebabkan masalah teknikal, penciptaan replika untuk jadual sumber {0} dihentikan. Sila cuba semula kemudian.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Tidak boleh memulakan nod kira elastik kerana had penggunaan telah dicapai atau jam blok pengiraan perlu diperuntukkan.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Memuatkan rantaian tugas dan bersedia untuk menjalankan sejumlah {0} tugas yang merupakan sebahagian daripada rantaian ini.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Percanggahan tugas telah berjalan
#XMSG: Replication will change
txt_replication_change=Jenis replikasi akan berubah.
txt_repl_viewdetails=Papar Butiran

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Terdapat ralat dengan jalanan terkini cuba semula kerana jalanan tugas sebelumnya gagal sebelum penjanaan rancangan.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=Anda telah kunci ruang "{0}".

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=Aktiviti {0} memerlukan jadual tempatan {1} untuk diatur duduk.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=Aktiviti {0} memerlukan Tangkapan Delta untuk diaktifkan pada jadual tempatan {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=Aktiviti {0} memerlukan jadual tempatan {1} untuk menyimpan data dalam simpanan objek.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=Aktiviti {0} memerlukan jadual tempatan {1} untuk menyimpan data dalam pangkalan data.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Mula mengeluarkan rekod yang dipadam untuk jadual tempatan {0}.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Mula memadam semua rekod untuk jadual tempatan {0}.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Mula memadam rekod untuk jadual tempatan {0} mengikut syarat penapis {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Cuba semula keluarkan rekod yang dipadam untuk jadual tempatan {0}.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Cuba semula padamkan semua rekod untuk jadual tempatan {0}.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Memadam semua rekod yang diproses sepenuhnya dengan Jenis Perubahan "Dipadam" yang lebih lama daripada {0} hari.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Memadam semua rekod yang diproses sepenuhnya dengan Jenis Perubahan "Dipadam" yang lebih lama daripada {0}.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS=Anda telah memadam {0} rekod.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} rekod ditanda untuk pemadaman.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=Mengeluarkan rekod yang dipadam untuk jadual tempatan {0} selesai.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Memadam semua rekod untuk jadual tempatan {0} selesai.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Mula menanda rekod sebagai "Dipadam" untuk jadual tempatan {0}.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Mula menanda rekod sebagai "Dipadam" untuk jadual tempatan {0} mengikut syarat penapis {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Menanda rekod sebagai "Dipadam" untuk jadual tempatan {0} selesai.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Perubahan data dimuatkan ke dalam jadual {0} buat sementara waktu.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Perubahan data dimuatkan ke dalam jadual {0} buat sementara waktu.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Perubahan data diproses dan dipadamkan daripada jadual {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Butiran sambungan.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Mula mengoptimumkan Jadual Tempatan (Fail).
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Ralat berlaku semasa mengoptimumkan Jadual Tempatan (Fail).
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Ralat berlaku. Mengoptimumkan Jadual Tempatan (Fail) telah dihentikan.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Mengoptimumkan Jadual Tempatan (Fail)...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=Jadual Tempatan (Fail) telah dioptimumkan.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=Jadual Tempatan (Fail) dioptimumkan.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=Jadual dioptimumkan dengan lajur Susunan Z: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Ralat berlaku. Memangkas Jadual Tempatan (Fail) telah dihentikan.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Memangkas Jadual Tempatan (Fail)...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=Lokasi penimbal masuk untuk Jadual Tempatan (Fail) telah dilepaskan.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Mula mengosongkan (memadam semua rekod yang diproses sepenuhnya) Jadual Tempatan (Fail).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Ralat berlaku semasa mengosongkan Jadual Tempatan (Fail).
#XMSG: Task log message
LTF_VACUUM_STOPPED=Ralat berlaku. Mengosongkan Jadual Tempatan (Fail) telah dihentikan.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Mengosongkan Jadual Tempatan (Fail)...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=Pengosongan selesai.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Memadam semua rekod yang diproses sepenuhnya yang lebih lama daripada {0} hari.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Memadam semua rekod yang diproses sepenuhnya yang lebih lama daripada {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Bermula untuk menggabungkan rekod baharu dengan Jadual Tempatan (Fail).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Mula gabungkan rekod baharu dengan Jadual Tempatan (Fail) menggunakan tetapan "Gabungkan Data Secara Automatik".
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Mula menggabungkan rekod baharu dengan Jadual Tempatan (Fail). Tugas ini telah dimulakan melalui Permintaan Gabungan API.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Menggabungkan rekod baharu dengan Jadual Tempatan (Fail).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Ralat berlaku semasa menggabungkan rekod baharu dengan Jadual Tempatan (Fail).
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=Jadual Tempatan (Fail) digabungkan.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Ralat berlaku. Gabungan Jadual Tempatan (Fail) telah dihentikan.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=Gabungan Jadual Tempatan (Fail) tidak berjaya kerana ralat, tetapi operasi telah berjaya sebahagian dan beberapa data telah digabungkan.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Ralat tamat masa berlaku. Aktiviti {0} telah berjalan selama {1} jam.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=Pelaksanaan tak segerak tidak dapat dimulakan kerana muatan sistem yang tinggi. Buka ''Pemantau Sistem'' dan semak tugas yang sedang dijalankan.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=Pelaksanaan tak segerak telah dibatalkan.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=Tugas {0} dijalankan dalam had ingatan {1} dan {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=Tugas {0} dijalankan dengan ID sumber {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=Cari dan ganti telah bermula dalam Jadual Tempatan (Fail).
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Cari dan ganti telah selesai dalam Jadual Tempatan (Fail).
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Cari dan ganti tidak berjaya dalam Jadual Tempatan (Fail).

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Ralat berlaku. Mengemas kini statistik untuk Jadual Tempatan (Fail) telah dihentikan.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=Tugas tidak berjaya kerana ralat kehabisan ingatan pada pangkalan data SAP HANA.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=Tugas tidak berjaya kerana Penolakan Kawalan Kemasukan SAP HANA.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=Tugas tidak berjaya kerana terlalu banyak sambungan SAP HANA yang aktif.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=Operasi Cuba Semula tidak berjaya dilaksanakan kerana cuba semula hanya dibenarkan pada induk rantaian tugas bersarang.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Log tugas anak yang tidak berjaya tidak lagi tersedia untuk Cuba Semula bagi meneruskan.


####Metrics Labels

performanceOptimized=Prestasi Dioptimumkan
memoryOptimized=Ingatan Dioptimumkan

JOB_EXECUTION=Pelaksanaan Kerja
EXECUTION_MODE=Mod Jalanan
NUMBER_OF_RECORDS_OVERALL=Bilangan Rekod Berterusan
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Bilangan Rekod dibaca daripada Sumber Jauh
RUNTIME_MS_REMOTE_EXECUTION_TIME=Masa Pemprosesan Sumber Jauh
MEMORY_CONSUMPTION_GIB=Ingatan Puncak SAP HANA
NUMBER_OF_PARTITIONS=Bilangan Bahagian
MEMORY_CONSUMPTION_GIB_OVERALL=Ingatan Puncak SAP HANA
NUMBER_OF_PARTITIONS_LOCKED=Bilangan Bahagian Dikunci
PARTITIONING_COLUMN=Lajur Pembahagian
HANA_PEAK_CPU_TIME=Jumlah Masa CPU SAP HANA
USED_IN_DISK=Storan Digunakan
INPUT_PARAMETER_PARAMETER_VALUE=Parameter Input
INPUT_PARAMETER=Parameter Input
ECN_ID=Nama Nod Kira Elastik

DAC=Kawalan Capaian Data
YES=Ya
NO=Tidak
noofrecords=Bilangan Rekod
partitionpeakmemory=Ingatan Puncak SAP HANA
value=Nilai
metricsTitle=Metrik ({0})
partitionmetricsTitle=Bahagian ({0})
partitionLabel=Bahagian
OthersNotNull=Nilai tidak termasuk dalam julat
OthersNull=Nilai sifar
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Tetapan digunakan untuk jalanan keterusan data terakhir:
#XMSG: Message for input parameter name
inputParameterLabel=Parameter Input
#XMSG: Message for input parameter value
inputParameterValueLabel=Nilai
#XMSG: Message for persisted data
inputParameterPersistedLabel=Berterusan pada
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Padam Data
REMOVE_DELETED_RECORDS=Keluarkan Rekod Dipadam
MERGE_FILES=Gabungkan Fail
OPTIMIZE_FILES=Optimumkan Fail
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Paparkan dalam Pemantau SAP BW Bridge

ANALYZE_PERFORMANCE=Analisis Prestasi
CANCEL_ANALYZE_PERFORMANCE=Batalkan Analisis Prestasi

#XFLD: Label for frequency column
everyLabel=Setiap
#XFLD: Plural Recurrence text for Hour
hoursLabel=Jam
#XFLD: Plural Recurrence text for Day
daysLabel=Hari
#XFLD: Plural Recurrence text for Month
monthsLabel=Bulan
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minit

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Paparan panduan penyelesaian masalah Berterusan</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Proses kehabisan ingatan. Tidak berjaya meneruskan data untuk paparan "{0}". Rujuk Portal Bantuan untuk lebih banyak maklumat tentang ralat kehabisan ingatan. Pertimbangkan untuk menyemak Paparkan Penganalisis untuk menganalisis paparan bagi kerumitan penggunaan ingatan.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Tidak Berkaitan
OPEN_BRACKET=(
CLOSE_BRACKET=)
