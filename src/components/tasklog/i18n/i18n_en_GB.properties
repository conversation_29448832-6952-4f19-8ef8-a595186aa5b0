
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Log Details
#XFLD: Header
TASK_LOGS=Task Logs ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Runs ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=View Details
#XFLD: Button text
STOP=Stop Run
#XFLD: Label text
RUN_START=Last Run Start
#XFLD: Label text
RUN_END=Last Run End
#XFLD: Label text
RUNTIME=Duration
#XTIT: Count for Messages
txtDetailMessages=Messages ({0})
#XFLD: Label text
TIME=Timestamp
#XFLD: Label text
MESSAGE=Message
#XFLD: Label text
TASK_STATUS=Category
#XFLD: Label text
TASK_ACTIVITY=Activity
#XFLD: Label text
RUN_START_DETAILS=Start
#XFLD: Label text
RUN_END_DETAILS=End
#XFLD: Label text
LOGS=Runs
#XFLD: Label text
STATUS=Status
#XFLD: Label text
RUN_STATUS=Run Status
#XFLD: Label text
Runtime=Duration
#XFLD: Label text
RuntimeTooltip=Duration (hh : mm : ss)
#XFLD: Label text
TRIGGEREDBY=Triggered By
#XFLD: Label text
TRIGGEREDBYNew=Run By
#XFLD: Label text
TRIGGEREDBYNewImp=Run Started By
#XFLD: Label text
EXECUTIONTYPE=Execution Type
#XFLD: Label text
EXECUTIONTYPENew=Run Type
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Parent Chain Space
#XFLD: Refresh tooltip
TEXT_REFRESH=Refresh
#XFLD: view Details link
VIEW_ERROR_DETAILS=View Details
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Download Additional Details
#XMSG: Download completed
downloadStarted=Download Started
#XMSG: Error while downloading content
errorInDownload=An error occurred while downloading.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=View Details
#XBTN: cancel button of task details dialog
TXT_CANCEL=Cancel
#XBTN: back button from task details
TXT_BACK=Back
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Task Completed
#XFLD: Log message with failed status
MSG_LOG_FAILED=Task Failed
#XFLD: Master and detail table with no data
No_Data=No Data
#XFLD: Retry tooltip
TEXT_RETRY=Retry
#XFLD: Cancel Run label
TEXT_CancelRun=Cancel Run
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Cleanup Failed Load
#XMSG:button copy sql statement
txtSQLStatement=Copy SQL Statement
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Open Remote Query Monitor
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=To display the remote SQL statements, click on "Open Remote Query Monitor".
#XMSG:button ok
txtOk=OK
#XMSG: button close
txtClose=Close
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=Cancel run action for object "{0}" has been started.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=Cancel run action for object "{0}" is failed.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=Cancel run action for object "{0}" is no longer possible because the replication status has changed.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=No task logs have status Running.
#XMSG: message for conflicting task
Task_Already_Running=A conflicting task is already running for the object "{0}".
#XFLD: Label for no task log with running state title
actionInfo=Action Info
#XMSG Copied to clipboard
copiedToClip=Copied to Clipboard
#XFLD copy
Copy=Copy
#XFLD copy correlation ID
CopyCorrelationID=Copy Correlation ID
#XFLD Close
Close=Close
#XFLD: show more Label
txtShowMore=Show More
#XFLD: message Label
messageLabel=Message:
#XFLD: details Label
detailsLabel=Details:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Executing SQL \r\n Statement:
#XFLD:statementId Label
statementIdLabel=Statement ID:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Number of Remote \r\n SQL Statements:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Number of Statements
#XFLD: Space Label
txtSpaces=Space
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Unauthorised Spaces
#XFLD: Privilege Error Text
txtPrivilegeError=You do not have sufficient privileges to view this data.
#XFLD: Label for Object Header
DATA_ACCESS=Data Access
#XFLD: Label for Object Header
SCHEDULE=Schedule
#XFLD: Label for Object Header
DETAILS=Details
#XFLD: Label for Object Header
LATEST_UPDATE=Latest Update
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Latest Change (Source)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Refresh Frequency
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Scheduled Frequency
#XFLD: Label for Object Header
NEXT_RUN=Next Run
#XFLD: Label for Object Header
CONNECTION=Connection
#XFLD: Label for Object Header
DP_AGENT=DP Agent
#XFLD: Label for Object Header
USED_IN_MEMORY=Memory Used for Storage (MiB)
#XFLD: Label for Object Header
USED_DISK=Disc Used for Storage (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Size in-Memory (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Size on Disk (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Number of Records

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Set to Failed
SET_TO_FAILED_ERR=This task was running but the user set this task's status to FAILED.
#XFLD: Label for stopped failed
FAILLOCKED=Run Already in Progress
#XFLD: sub status STOPPED
STOPPED=Stopped
STOPPED_ERR=This task was stopped, but no rollback was performed.
#XFLD: sub status CANCELLED
CANCELLED=Cancelled
CANCELLED_ERR=This task run was cancelled after it had started. In this case, the data was rolled back and restored to the state that existed before the task run was initially triggered.
#XFLD: sub status LOCKED
LOCKED=Locked
LOCKED_ERR=The same task was already running, so this task job cannot be run in parallel with an existing task's execution.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Task Exception
TASK_EXCEPTION_ERR=This task encountered an unspecified error during execution.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Task Execute Exception
TASK_EXECUTE_EXCEPTION_ERR=This task encountered an error during execution.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Unauthorised
UNAUTHORIZED_ERR=The user could not be authenticated or has been locked or deleted.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Forbidden
FORBIDDEN_ERR=The assigned user does not have the privileges necessary to execute this task.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Not Triggered
FAIL_NOT_TRIGGERED_ERR=This task job could not be executed due to a system outage or some part of the database system not being available at the time of the planned execution. Wait for the next scheduled job execution time or reschedule the job.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Schedule Cancelled
SCHEDULE_CANCELLED_ERR=This task job could not be executed due to an internal error. Contact SAP Support and provide them with the correlation ID and timestamp from this task job's log detail information.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Previous Run in Progress
SUCCESS_SKIPPED_ERR=This task's execution has not been triggered because a previous run of the same task is still in progress.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Owner Missing
FAIL_OWNER_MISSING_ERR=This task job could not be executed because it does not have an assigned system user. Assign an owner user to the job.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Consent Not Available
FAIL_CONSENT_NOT_AVAILABLE_ERR=You have not authorised SAP to run task chains or schedule data integration tasks on your behalf. Select the option provided to give your consent.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Consent Expired
FAIL_CONSENT_EXPIRED_ERR=The authorisation that allows SAP to run task chains or schedule data integration tasks on your behalf has expired. Select the option provided to renew your consent.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Consent Invalidated
FAIL_CONSENT_INVALIDATED_ERR=This task could not be executed, typically due to a change in the Identity Provider configuration of the tenant. In that case, no new task jobs can be run or scheduled in the name of the affected user. If the assigned user still exists in the new IdP, revoke the scheduling consent and then grant it again. If the assigned user no longer exists, assign a new task job owner and provide the required task scheduling consent. See the following SAP Note: https://launchpad.support.sap.com/#/notes/3089828 for more information.
TASK_EXECUTOR_ERROR=Task Executor
TASK_EXECUTOR_ERROR_ERR=This task encountered an internal error, likely during preparation steps for execution, and the task could not be started.
PREREQ_NOT_MET=Prerequisite not met
PREREQ_NOT_MET_ERR=This task could not be run because of issues in its definition. For example, the object is not deployed, a task chain contains circular logic or a view’s SQL is invalid.
RESOURCE_LIMIT_ERROR=Resource Limit Error
RESOURCE_LIMIT_ERROR_ERR=Currently unable to perform task because sufficient resources were not available or were busy.
FAIL_CONSENT_REFUSED_BY_UMS=Consent Refused
FAIL_CONSENT_REFUSED_BY_UMS_ERR=This task could not be executed in scheduled runs or task chains because of a change in a user’s Identity Provider configuration on the tenant. For more information, see the following SAP note: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Scheduled
#XFLD: status text
SCHEDULEDNew=Permanent
#XFLD: status text
PAUSED=Paused
#XFLD: status text
DIRECT=Direct
#XFLD: status text
MANUAL=Manual
#XFLD: status text
DIRECTNew=Simple
#XFLD: status text
COMPLETED=Completed
#XFLD: status text
FAILED=Failed
#XFLD: status text
RUNNING=Running
#XFLD: status text
none=None
#XFLD: status text
realtime=Real-Time
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Sub-Task
#XFLD: New Data available in the file
NEW_DATA=New Data

#XFLD: text for values shown in column Replication Status
txtOff=Off
#XFLD: text for values shown in column Replication Status
txtInitializing=Initialising
#XFLD: text for values shown in column Replication Status
txtLoading=Loading
#XFLD: text for values shown in column Replication Status
txtActive=Active
#XFLD: text for values shown in column Replication Status
txtAvailable=Available
#XFLD: text for values shown in column Replication Status
txtError=Error
#XFLD: text for values shown in column Replication Status
txtPaused=Paused
#XFLD: text for values shown in column Replication Status
txtDisconnected=Disconnected
#XFLD: text for partially Persisted views
partiallyPersisted=Partially Persisted

#XFLD: activity text
REPLICATE=Replicate
#XFLD: activity text
REMOVE_REPLICATED_DATA=Remove Replicated Data
#XFLD: activity text
DISABLE_REALTIME=Disable Real-Time Data Replication
#XFLD: activity text
REMOVE_PERSISTED_DATA=Remove Persisted Data
#XFLD: activity text
PERSIST=Persist
#XFLD: activity text
EXECUTE=Execute
#XFLD: activity text
TASKLOG_CLEANUP=Tacklog_Cleanup
#XFLD: activity text
CANCEL_REPLICATION=Cancel Replication
#XFLD: activity text
MODEL_IMPORT=Model Import
#XFLD: activity text
NONE=None
#XFLD: activity text
CANCEL_PERSISTENCY=Cancel Persistency
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Analyse View
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Cancel View Analyser

#XFLD: severity text
INFORMATION=Information
#XFLD: severity text
SUCCESS=Success
#XFLD: severity text
WARNING=Warning
#XFLD: severity text
ERROR=Error
#XFLD: text for values shown for Ascending sort order
SortInAsc=Sort Ascending
#XFLD: text for values shown for Descending sort order
SortInDesc=Sort Descending
#XFLD: filter text for task log columns
Filter=Filter
#XFLD: object text for task log columns
Object=Object
#XFLD: space text for task log columns
crossSpace=Space

#XBUT: label for remote data access
REMOTE=Remote
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Replicated (Real-Time)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Replicated (Snapshot)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=Real-time replication is blocked because of an error. Once the error is corrected, you can use the “Retry” action to continue with real-time replication.
ERROR_MSG=Real-time replication is blocked due to an error.
RETRY_FAILED_ERROR=Retry process failed with an error.
LOG_INFO_DETAILS=No logs are generated when data is replicated in real-time mode. The displayed logs relate to previous actions.

#XBUT: Partitioning label
partitionMenuText=Partitioning
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Create Partition
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Edit Partition
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Delete Partition
#XFLD: Initial text
InitialPartitionText=Define partitions by specifying criteria to divide large datasets into smaller sets.
DefinePartition=Define Partitions
#XFLD: Message text
partitionChangedInfo=The partition definition has changed since the last replication. Changes will be applied on the next data load.
#XFLD: Message text
REAL_TIME_WARNING=Partitioning is only applied when loading a new snapshot. It is not applied for real-time replication.
#XFLD: Message text
loadSelectedPartitions=Started persisting data for the selected partitions of "{0}"
#XFLD: Message text
loadSelectedPartitionsError=Failed to persist data for the selected partitions of "{0}"
#XFLD: Message text
viewpartitionChangedInfo=Partition definition has changed since the last persistency run. To apply the changes, the next data load will be a full snapshot including locked partitions. Once this full load is finished, you will be able to run data for single partitions.
#XFLD: Message text
viewpartitionChangedInfoLocked=Partition definition has changed since the last persistency run. To apply the changes, the next data load will be a full snapshot, except for locked and unchanged partition ranges. Once this load is finished, you will be able to Load Selected Partitions again.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Table Replication
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Schedule Replication
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Create Schedule
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Edit Schedule
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Delete Schedule
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Load New Snapshot
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Start Data Replication
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Remove Replicated Data
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Enable Real-Time Access
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Enable Real-Time Data Replication
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Disable Real-Time Data Replication
#XFLD: Message for replicate table action
replicateTableText=Table Replication
#XFLD: Message for replicate table action
replicateTableTextNew=Data Replication
#XFLD: Message to schedule task
scheduleText=Schedule
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=View Persistency
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Data Persistence
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Load New Snapshot
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Start Data Persistence
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Remove Persisted Data
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Partitioned Processing
#XBUT: Label for scheduled replication
scheduledTxt=Scheduled
#XBUT: Label for statistics button
statisticsTxt=Statistics
#XBUT: Label for create statistics
createStatsTxt=Create Statistics
#XBUT: Label for edit statistics
editStatsTxt=Edit Statistics
#XBUT: Label for refresh statistics
refreshStatsTxt=Refresh Statistics
#XBUT: Label for delete statistics
dropStatsTxt=Delete Statistics
#XMSG: Create statistics success message
statsSuccessTxt=Started creating statistics of type {0} for {1}.
#XMSG: Edit statistics success message
statsEditSuccessTxt=Started changing statistics of type to {0} for {1}.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Started refreshing statistics for {0}.
#XMSG: Drop statistics success message
statsDropSuccessTxt=Statistics successfully deleted for {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=Error when creating statistics
#XMSG: Edit statistics error message
statsEditErrorTxt=Error when changing statistics
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Error when refreshing statistics
#XMSG: Drop statistics error message
statsDropErrorTxt=Error when deleting statistics
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Are you sure you want to drop the data statistics?
startPersistencyAdvisorLabel=Start View Analyser

#Partition related texts
#XFLD: Label for Column
column=Column
#XFLD: Label for No of Partition
noOfPartitions=No. of Partitions
#XFLD: Label for Column
noOfParallelProcess=No. of Parallel Processes
#XFLD: Label text
noOfLockedPartition=No. of Locked Partitions
#XFLD: Label for Partition
PARTITION=Partitions
#XFLD: Label for Column
AVAILABLE=Available
#XFLD: Statistics Label
statsLabel=Statistics
#XFLD: Label text
COLUMN=Column:
#XFLD: Label text
PARALLEL_PROCESSES=Parallel Processes:
#XFLD: Label text
Partition_Range=Partition Range
#XFLD: Label text
Name=Name
#XFLD: Label text
Locked=Locked
#XFLD: Label text
Others=OTHERS
#XFLD: Label text
Delete=Delete
#XFLD: Label text
LoadData=Load Selected Partitions
#XFLD: Label text
LoadSelectedData=Load Selected Partitions
#XFLD: Confirmation text
LoadNewPersistenceConfirm=This will load a new snapshot for all unlocked and changed partitions, not only the ones you selected. Do you want to continue?
#XFLD: Label text
Continue=Continue

#XFLD: Label text
PARTITIONS=Partitions
#XFLD: Label text
ADD_PARTITIONS=+ Add Partition
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Add Partition
#XFLD: Label text
deleteRange=Delete Partition
#XFLD: Label text
LOW_PLACE_HOLDER=Enter low value
#XFLD: Label text
HIGH_PLACE_HOLDER=Enter high value
#XFLD: tooltip text
lockedTooltip=Lock partition after initial load

#XFLD: Button text
Edit=Edit
#XFLD: Button text
CANCEL=Cancel

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Last Statistics Update
#XFLD: Statistics Fields
STATISTICS=Statistics

#XFLD:Retry label
TEXT_Retry=Retry
#XFLD:Retry label
TEXT_Retry_tooltip=Retry real-time replication after error is solved.
#XFLD: text retry
Retry=Confirm
#XMG: Retry confirmation text
retryConfirmationTxt=The last real-time replication terminated with an error.\n Confirm that the error has been corrected and that real-time replication can be restarted.
#XMG: Retry success text
retrySuccess=Retry process successfully initiated.
#XMG: Retry fail text
retryFail=Retry process failed.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Create Statistics
#XMSG: activity message for edit statistics
DROP_STATISTICS=Delete Statistics
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Refresh Statistics
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Edit Statistics
#XMSG: Task log message started task
taskStarted=The task {0} has started.
#XMSG: Task log message for finished task
taskFinished=The task {0} ended with status {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=Task {0} ended at {2} with status {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=Task {0} has input parameters.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=The task {0} ended with an unexpected error. The task status has been set to {1}.
#XMSG: Task log message for failed task
failedToEnd=Failed to set status to {0} or failed to remove the lock.
#XMSG: Task log message
lockNotFound=We can’t finalise the process as the lock is missing: the task might have been cancelled.
#XMSG: Task log message failed task
failedOverwrite=The task {0} is already locked by {1}. Therefore, it failed with the following error: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Lock of this task was taken over by another task.
#XMSG: Task log message failed takeover
failedTakeover=Failed to take over an existing task.
#XMSG: Task log message successful takeover
successTakeover=Left over lock had to be released. The new lock for this task is set.
#XMSG: Tasklog Dialog Details
txtDetails=The remote statements processed during the run can be displayed by opening the remote query monitor, in the details of the partition-specific messages.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=Remote SQL statements have been deleted from the database and cannot be displayed.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Remote queries that have connections assigned to other spaces can't be displayed. Go to the Remote Query Monitor and use the statement ID to filter them.
#XMSG: Task log message for parallel check error
parallelCheckError=The task can’t be processed because another task is running and already blocking this task.
#XMSG: Task log message for parallel running task
parallelTaskRunning=A conflicting task is already running.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} during run with correlation ID {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=The assigned user does not have the privileges necessary to execute this task.

#XBUT: Label for open in Editor
openInEditor=Open in Editor
#XBUT: Label for open in Editor
openInEditorNew=Open in Data Builder
#XFLD:Run deails label
runDetails=Run Details
#XFLD: Label for Logs
Logs=Logs
#XFLD: Label for Settings
Settings=Settings
#XFLD: Label for Save button
Save=Save
#XFLD: Label for Standard
Standard_PO=Performance-Optimised (Recommended)
#XFLD: Label for Hana low memory processing
HLMP_MO=Memory-Optimised
#XFLD: Label for execution mode
ExecutionMode=Run Mode
#XFLD: Label for job execution
jobExecution=Processing Mode
#XFLD: Label for Synchronous
syncExec=Synchronous
#XFLD: Label for Asynchronous
asyncExec=Asynchronous
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Use Default (Asynchronous, may change in the future)
#XMSG: Save settings success
saveSettingsSuccess=SAP HANA Execution Mode changed.
#XMSG: Save settings failure
saveSettingsFailed=SAP HANA Execution Mode change failed.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Job Execution changed.
#XMSG: Job Execution change failed
jobExecSettingFailed=Job Execution change failed.
#XMSG: Text for Type
typeTxt=Type
#XMSG: Text for Monitor
monitorTxt=Monitor
#XMSG: Text for activity
activityTxt=Activity
#XMSG: Text for metrics
metricsTxt=Metrics
#XTXT: Text for Task chain key
TASK_CHAINS=Task Chain
#XTXT: Text for View Key
VIEWS=View
#XTXT: Text for remote table key
REMOTE_TABLES=Remote Table
#XTXT: Text for Space key
SPACE=Space
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Elastic Compute Node
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Replication Flow
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Intelligent Lookup
#XTXT: Text for Local Table
LOCAL_TABLE=Local Table
#XTXT: Text for Data flow key
DATA_FLOWS=Data Flow
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=SQL Script Procedure
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=BW Process Chain
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=View in Monitor
#XTXT: Task List header text
taskListHeader=Task List ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Metrics for historical runs of a data flow can’t be retrieved.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=Complete run detail isn’t loading at the moment. Try to refresh.
#XFLD: Label text for the Metrices table header
metricesColLabel=Operator Label
#XFLD: Label text for the Metrices table header
metricesType=Type
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Record Count
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Run the Task Chain
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Task chain run has started.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Task chain run has started for {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Failed to run the task chain.
#XTXT: Execute button label
runLabel=Run
#XTXT: Execute button label
runLabelNew=Start Run
#XMSG: Filter Object header
chainsFilteredTableHeader=Filtered by Object: {0}
#XFLD: Parent task chain label
parentChainLabel=Parent Task Chain:
#XFLD: Parent task chain unauthorized
Unauthorized=Not Authorised to View
#XFLD: Parent task chain label
parentTaskLabel=Parent Task:
#XTXT: Task status
NOT_TRIGGERED=Not Triggered
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Enter Full-Screen Mode
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Exit Full-Screen Mode
#XTXT: Close Task log details right panel
closeRightColumn=Close Section
#XTXT: Sort Text
sortTxt=Sort
#XTXT: Filter Text
filterTxt=Filter
#XTXT: Filter by text to show list of filters applied
filterByTxt=Filter by
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=More than 5 Minutes
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=More than 15 Minutes
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=More than 1 Hour
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Last Hour
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Last 24 Hours
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Last Month
#XTXT: Messages title text
messagesText=Messages

#XTXT Statistics information message
statisticsInfo=Statistics cannot be created for remote tables with data access "Replicated". To create statistics, remove the replicated data in the Remote Tables Details Monitor.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Go to Remote Table Details Monitor

#XTXT: Repair latest failed run label
retryRunLabel=Retry Latest Run
#XTXT: Repair failed run label
retryRun=Retry Run
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Task chain retry run has started
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Task chain retry run has failed
#XMSG: Task chain child elements name
taskChainRetryChildObject=Task {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=New Task
#XFLD Analyzed View
analyzedView=Analysed View
#XFLD Metrics
Metrics=Metrics
#XFLD Partition Metrics
PartitionMetrics=Partition Metrics
#XFLD Entities
Messages=Task Log
#XTXT: Title Message for empty partition data
partitionEmptyTitle=No partitions have been defined yet
#XTXT: Description message for empty partition data
partitionEmptyDescText=Create partitions by specifying criteria to break larger data volumes into smaller, more manageable parts.

#XTXT: Title Message for empty runs data
runsEmptyTitle=No logs are available yet
#XTXT: Description message for empty runs data
runsEmptyDescText=When you start a new activity (Load a new snapshot, start view analyser...) you will see related logs here.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=The elastic compute node {0} configuration is not maintained accordingly. Please check your definition.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Process to add the elastic compute node {0} failed.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Creation and activation of replica, for the source object "{0}"."{1}" in the elastic compute node {2}, has been started.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Removing replica, for the source object "{0}"."{1}" from the elastic compute node {2}, has been started.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Creation and activation of the replica, for the source object "{0}"."{1}", has failed.
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Removing replica, for the source object "{0}"."{1}", has failed.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Routing the analytic queries computation of the space {0} to the elastic compute node {1} started.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Routing the analytic queries computation of the space {0} to the corresponding elastic compute node failed.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Rerouting the analytic queries computation of the space {0} back from the elastic compute node {1} started.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Rerouting the analytic queries computation of the space {0} back to coordinator failed.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Task chain {0} to the corresponding elastic compute node {1} has been triggered.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Generation of the task chain for the elastic compute node {0} has failed.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=The provisioning of the elastic compute node {0} has started with the given sizing plan: vCPUs: {1}, memory (GiB): {2} and storage size(GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=The elastic compute node {0} has already been provisioned.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=The elastic compute node {0} has already been deprovisioned.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=The operation is not allowed. Please stop the elastic compute node {0} and restart it to update the sizing plan.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Process to remove the elastic compute node {0} failed.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=The current run of the elastic compute node {0} timed out.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=Checking the status of the elastic compute node {0} is in progress...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=Task chain generation for the elastic compute node {0} is locked, hence the chain {1} might still be running.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=The source object "{0}"."{1}" is replicated as dependency of the view "{2}"."{3}".
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Unable to replicate the table "{0}"."{1}", since the replication of row table is deprecated.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Maximum elastic compute node per SAP HANA Cloud instance is exceeded.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=Running operation for the elastic compute node {0} is still in progress.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Due to technical issues, deleting the replica for the source table {0} has been stopped. Please try again later.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=Due to technical issues, creating the replica for the source table {0} has been stopped. Please try again later.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Cannot start an elastic compute node as the usage limit has been reached or as no compute block-hours have been allocated yet.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Loading task chain and preparing to run a total of {0} tasks that are part of this chain.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=A conflicting task is already running
#XMSG: Replication will change
txt_replication_change=Replication type will be changed.
txt_repl_viewdetails=View Details

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=It looks like there was an error with retry latest run as the previous task run failed before the plan could be generated.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=Space "{0}" is locked.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=The activity {0} requires the local table {1} to be deployed.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=The activity {0} requires Delta Capture to be switched on for the local table {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=The activity {0} requires the local table {1} to store data in the object store.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=The activity {0} requires the local table {1} to store data in the database.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Starting to remove deleted records for the local table {0}.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Starting to delete all records for the local table {0}.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Starting to delete records for the local table {0} according to the filter condition {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=An error occurred while removing deleted records for the local table {0}.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=An error occurred while deleting all records for the local table {0}.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Deleting all fully-processed records with Change Type "Deleted", which are older than {0} days.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Deleting all fully-processed records with Change Type "Deleted", which are older than {0}.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} records have been deleted.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} records have been marked for deletion.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=Removing deleted records for the local table {0} is completed.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Deleting all records for local table {0} is completed.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Starting to mark records as "Deleted" for the local table {0}.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Starting to mark records as "Deleted" for the local table {0} according to the filter condition {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Marking records as "Deleted" for the local table {0} is completed.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Data changes are temporarily loading into the table {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Data changes are temporarily loaded into the table {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Data changes are processed and deleted from the table {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Connection details.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Starting to optimise Local Table (File).
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=An error occurred while optimising Local Table (File).
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=An error occurred. Optimising Local Table (File) has been stopped.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Optimising Local Table (File)...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=Local Table (File) has been optimised.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=Local Table (File) is optimised.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=The table is optimised with the Z-Order columns: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=An error occurred. Truncating Local Table (File) has been stopped.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Truncating Local Table (File)...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=Inbound buffer location for Local Table (File) has been dropped.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Starting to vacuum (delete all fully-processed records) Local Table (File).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=An error occurred while vacuuming Local Table (File).
#XMSG: Task log message
LTF_VACUUM_STOPPED=An error occurred. Vacuuming Local Table (File) has been stopped.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Vacuuming Local Table (File)...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=Vacuuming is complete.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Deleting all fully-processed records which are older than {0} days.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Deleting all fully-processed records which are older than {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Starting to merge new records with Local Table (File).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Starting to merge new records with Local Table (File) using the setting "Merge Data Automatically".
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Starting to merge new records with Local Table (File). This task was initiated through the API Merge Request.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Merging new records with Local Table (File).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=An error occurred while merging new records with Local Table (File).
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=Local Table (File) is merged.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=An error occurred. The merge of Local Table (File) has been stopped.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=The merge of Local Table (File) has failed because of an error, but the operation was partially successful, and some data has already been merged.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=A timeout error occurred. The activity {0} has been running for {1} hours.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=The asynchronous execution could not start because of a high system load. Open the ''System Monitor'' and check the running tasks.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=The asynchronous execution has been cancelled.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=The {0} task ran within the memory limits of {1} and {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=The {0} task ran with the resource ID {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=Find and replace has started in Local Table (File).
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Find and replace has completed in Local Table (File).
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Find and replace has failed in Local Table (File).

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=An error occurred. Updating statistics for Local Table (File) has been stopped.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=The task failed because of an out of memory error on the SAP HANA database.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=The task failed because of an SAP HANA Admission Control Rejection.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=The task failed because of too many active SAP HANA connections.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=The Retry operation could not be performed because retries are only allowed on the parent of a nested task chain.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Logs of the failed child tasks are no longer available for Retry to proceed.


####Metrics Labels

performanceOptimized=Performance-Optimised
memoryOptimized=Memory-Optimised

JOB_EXECUTION=Job Execution
EXECUTION_MODE=Run Mode
NUMBER_OF_RECORDS_OVERALL=Number of Persisted Records
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Number of Records Read from Remote Source
RUNTIME_MS_REMOTE_EXECUTION_TIME=Remote Source Processing Time
MEMORY_CONSUMPTION_GIB=SAP HANA Peak Memory
NUMBER_OF_PARTITIONS=Number of Partitions
MEMORY_CONSUMPTION_GIB_OVERALL=SAP HANA Peak Memory
NUMBER_OF_PARTITIONS_LOCKED=Number of Locked Partitions
PARTITIONING_COLUMN=Partitioning Column
HANA_PEAK_CPU_TIME=SAP HANA Total CPU Time
USED_IN_DISK=Used Storage
INPUT_PARAMETER_PARAMETER_VALUE=Input Parameter
INPUT_PARAMETER=Input Parameter
ECN_ID=Elastic Compute Node Name

DAC=Data Access Controls
YES=Yes
NO=No
noofrecords=Number of Records
partitionpeakmemory=SAP HANA Peak Memory
value=Value
metricsTitle=Metrics ({0})
partitionmetricsTitle=Partitions ({0})
partitionLabel=Partition
OthersNotNull=Values not included in ranges
OthersNull=Null values
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Settings used for last data persistence run:
#XMSG: Message for input parameter name
inputParameterLabel=Input Parameter
#XMSG: Message for input parameter value
inputParameterValueLabel=Value
#XMSG: Message for persisted data
inputParameterPersistedLabel=Persisted At
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Delete Data
REMOVE_DELETED_RECORDS=Remove Deleted Records
MERGE_FILES=Merge Files
OPTIMIZE_FILES=Optimise Files
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=View in SAP BW Bridge Monitor

ANALYZE_PERFORMANCE=Analyse Performance
CANCEL_ANALYZE_PERFORMANCE=Cancel Analyse Performance

#XFLD: Label for frequency column
everyLabel=Every
#XFLD: Plural Recurrence text for Hour
hoursLabel=Hours
#XFLD: Plural Recurrence text for Day
daysLabel=Days
#XFLD: Plural Recurrence text for Month
monthsLabel=Months
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutes

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">View Persistency troubleshooting guide</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Process out of memory. Unable to persist data for the view "{0}". Consult the Help Portal for more information about out of memory errors. Consider checking View Analyser to analyse the view for memory consumption complexity.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Not Applicable
OPEN_BRACKET=(
CLOSE_BRACKET=)
