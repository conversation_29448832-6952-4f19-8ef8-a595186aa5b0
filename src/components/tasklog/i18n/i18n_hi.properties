
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=लॉग विवरण
#XFLD: Header
TASK_LOGS=कार्य लॉग ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=रन ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=विवरण देखें
#XFLD: Button text
STOP=रन करना रोकें
#XFLD: Label text
RUN_START=अंतिम बार रन प्रारंभ करें
#XFLD: Label text
RUN_END=अंतिम बार रन समाप्त करें
#XFLD: Label text
RUNTIME=अवधि
#XTIT: Count for Messages
txtDetailMessages=संदेश ({0})
#XFLD: Label text
TIME=टाइमस्टैम्प
#XFLD: Label text
MESSAGE=संदेश
#XFLD: Label text
TASK_STATUS=श्रेणी
#XFLD: Label text
TASK_ACTIVITY=गतिविधि
#XFLD: Label text
RUN_START_DETAILS=प्रारंभ करें
#XFLD: Label text
RUN_END_DETAILS=समाप्त करें
#XFLD: Label text
LOGS=रन
#XFLD: Label text
STATUS=स्थिति
#XFLD: Label text
RUN_STATUS=स्थिति रन करें
#XFLD: Label text
Runtime=अवधि
#XFLD: Label text
RuntimeTooltip=अवधि (hh : mm : ss)
#XFLD: Label text
TRIGGEREDBY=इसके द्वारा ट्रिगर किया गया
#XFLD: Label text
TRIGGEREDBYNew=इनके द्वारा रन किया गया
#XFLD: Label text
TRIGGEREDBYNewImp=इनके द्वारा रन प्रारंभ किया गया
#XFLD: Label text
EXECUTIONTYPE=निष्पादन प्रकार
#XFLD: Label text
EXECUTIONTYPENew=रन प्रकार
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=पेरेंट श्रृंखला स्थान
#XFLD: Refresh tooltip
TEXT_REFRESH=रीफ़्रेश करें
#XFLD: view Details link
VIEW_ERROR_DETAILS=विवरण देखें
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=अतिरिक्त विवरण डाउनलोड करें
#XMSG: Download completed
downloadStarted=डाउनलोड शुरू हुआ
#XMSG: Error while downloading content
errorInDownload=डाउनलोड करते समय त्रुटि हुई.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=विवरण देखें
#XBTN: cancel button of task details dialog
TXT_CANCEL=रद्द करें
#XBTN: back button from task details
TXT_BACK=वापस जाएं
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=कार्य पूरा हुआ
#XFLD: Log message with failed status
MSG_LOG_FAILED=कार्य विफ़ल हुआ
#XFLD: Master and detail table with no data
No_Data=कोई डेटा नहीं
#XFLD: Retry tooltip
TEXT_RETRY=पुन: प्रयास करें
#XFLD: Cancel Run label
TEXT_CancelRun=रन रद्द करें
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=क्लीनअप लोड करना विफ़ल
#XMSG:button copy sql statement
txtSQLStatement=SQL विवरण की प्रति बनाएं
#XMSG:button open remote query monitor
txtOpenQueryMonitor=दूरस्थ क्वेरी मॉनिटर शुरू करें
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=दूरस्थ SQL विवरण प्रदर्शित करने के लिए, 'दूरस्थ क्वेरी मॉनिटर खोलें' पर क्लिक करें.
#XMSG:button ok
txtOk=ठीक है
#XMSG: button close
txtClose=बंद करें
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=ऑब्जेक्ट ''{0}''’ के लिए रन रद्द करें कारवाई प्रारंभ किया गया है.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=ऑब्जेक्ट ''{0}''’ के लिए रन कार्रवाई रद्द करें को विफ़ल किया गया है.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=ऑब्जेक्ट ''{0}'' के लिए रद्द रन क्रिया अब संभव नहीं है क्योंकि प्रतिकृति स्थिति बदल गई है.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=किसी टास्कलॉग में रनिंग स्थिति नहीं है.
#XMSG: message for conflicting task
Task_Already_Running=ऑब्जेक्ट ''{0}'' के लिए विरोधी कार्य पहले से ही रन हो रहा है.
#XFLD: Label for no task log with running state title
actionInfo=गतिविधि जानकारी
#XMSG Copied to clipboard
copiedToClip=क्क्लिपबोर्ड में कॉपी करें
#XFLD copy
Copy=प्रति बनाएं
#XFLD copy correlation ID
CopyCorrelationID=सहसंबंध ID की प्रति बनाएं 
#XFLD Close
Close=बंद करें
#XFLD: show more Label
txtShowMore=अधिक दिखाएं
#XFLD: message Label
messageLabel=संदेश:
#XFLD: details Label
detailsLabel=विवरणः
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=SQL निष्पादित हो रहा है \r\n विवरण:
#XFLD:statementId Label
statementIdLabel=विवरण ID:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=दूरस्थ की संख्या \r\n SQL विवरण:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=विवरण की संख्या
#XFLD: Space Label
txtSpaces=स्पेस
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=अनधिकृत स्थान
#XFLD: Privilege Error Text
txtPrivilegeError=आपके पास इस डेटा का पूर्वावलोकन करने के लिए पर्याप्त विशेषाधिकार नहीं हैं.
#XFLD: Label for Object Header
DATA_ACCESS=डेटा एक्सेस
#XFLD: Label for Object Header
SCHEDULE=शेड्यूल
#XFLD: Label for Object Header
DETAILS=विवरण
#XFLD: Label for Object Header
LATEST_UPDATE=नवीनतम अपडेट
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=नवीनतम परिवर्तन (स्रोत)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=आवृति रीफ़्रेश करें
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=शेड्यूल की गई आवृत्ति
#XFLD: Label for Object Header
NEXT_RUN=अगला रन
#XFLD: Label for Object Header
CONNECTION=कनेक्शन
#XFLD: Label for Object Header
DP_AGENT=DP एजेंट
#XFLD: Label for Object Header
USED_IN_MEMORY=(MiB) स्टोरेज के लिए उपयोग की जाने वाली मेमोरी
#XFLD: Label for Object Header
USED_DISK=(MiB) संग्रहण के लिए उपयोग की जाने वाली डिस्क
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=आकार इन-मेमोरी (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=डिस्क पर आकार (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=रिकॉर्ड की संख्या

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=विफ़ल पर सेट करें
SET_TO_FAILED_ERR=यह कार्य रन कर रहा था लेकिन उपयोगकर्ता इस कार्य की स्थिति सेट करने में विफल हुआ.
#XFLD: Label for stopped failed
FAILLOCKED=रन पहले से ही प्रगति पर है
#XFLD: sub status STOPPED
STOPPED=रोका गया
STOPPED_ERR=यह कार्य बंद किया गया, लेकिन कोई रॉलबैक निष्पादित नहीं किया गया था.
#XFLD: sub status CANCELLED
CANCELLED=रद्द किया गया
CANCELLED_ERR=यह कार्य रन रद्द कर दिया गया था, इसके प्रारंभ होने के बाद. इस केस में, डेटा को वापस रोल किया गया था और उस स्थिति में पुनर्स्थापित किया गया था जो कार्य रन करने से पहले शुरू में शुरू हुआ था.
#XFLD: sub status LOCKED
LOCKED=लॉक किया गया
LOCKED_ERR=समान कार्य पहले से रन किया जा रहा है, इसलिए यह कार्य जॉब किसी मौजूदा कार्य के निष्पादन के समानांतर रन नहीं किया जा सकता है.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=कार्य निष्पादन
TASK_EXCEPTION_ERR=इस कार्य को निष्पादन के दौरान एक अनिर्दिष्ट त्रुटि का एनकाउंडर किया गया.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=कार्य निष्पादन अपवाद
TASK_EXECUTE_EXCEPTION_ERR=इस कार्य के निष्पादन के दौरान एक त्रुटि उत्पन्न हुई.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=अनधिकृत
UNAUTHORIZED_ERR=उपयोगकर्ता को प्रमाणित नहीं किया जा सका, लॉक कर दिया गया है, या हटा दिया गया है.
#XFLD: sub status FORBIDDEN
FORBIDDEN=वर्जित किया गया
FORBIDDEN_ERR=असाइन किए गए उपयोगकर्ता के पास इस कार्य को निष्पादित करने के लिए आवश्यक विशेषाधिकार नहीं हैं.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=ट्रिगर नहीं किया गया
FAIL_NOT_TRIGGERED_ERR=सिस्टम आउटेज या डेटाबेस सिस्टम का कुछ भाग नियोजित निष्पादन के समय उपलब्ध नहीं होने के कारण यह कार्य कार्य निष्पादित नहीं किया जा सका. अगले निर्धारित कार्य निष्पादन समय की प्रतीक्षा करें या कार्य को पुनर्निर्धारित करें.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=शेड्यूल रद्द किया गया
SCHEDULE_CANCELLED_ERR=किसी आंतरिक त्रुटि के कारण यह कार्य कार्य निष्पादित नहीं किया जा सका. SAP सहायता से संपर्क करें और उन्हें इस कार्य कार्य की लॉग विवरण जानकारी से सहसंबंध id और टाइमस्टैम्प प्रदान करें.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=पिछला प्रगति में है
SUCCESS_SKIPPED_ERR=इस कार्य का निष्पादन ट्रिगर नहीं किया गया है क्योंकि उसी कार्य का पिछला रन अभी भी प्रगति पर है.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=स्वामी गुम है
FAIL_OWNER_MISSING_ERR=यह कार्य जॉब निष्पादित नहीं किया जा सकता क्योंकि इसमें एक निर्दिष्ट सिस्टम उपयोगकर्ता नहीं है. किसी स्वामी उपयोगकर्ता को कार्य के लिए असाइन करें.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=सहमति उपलब्ध नहीं है
FAIL_CONSENT_NOT_AVAILABLE_ERR=आपने SAP को अपनी ओर से कार्य श्रृंखला रन करने या डेटा एकीकरण कार्यों को शेड्यूल करने के लिए अधिकृत नहीं किया है. अपनी सहमति देने के लिए दिए गए विकल्प का चयन करें.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=सहमति समाप्ति की गई
FAIL_CONSENT_EXPIRED_ERR=SAP को आपकी ओर से कार्य श्रृंखला चलाने या डेटा एकीकरण कार्यों को शेड्यूल करने की अनुमति देने वाले प्राधिकरण की समय-सीमा समाप्त हो गई है. अपनी सहमति को नवीनीकृत करने के लिए दिए गए विकल्प का चयन करें.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=सहमति अमान्य किया गया
FAIL_CONSENT_INVALIDATED_ERR=यह कार्य निष्पादित नहीं किया जा सका, आमतौर पर टैनेंट के पहचान प्रदाता कॉन्फ़िगरेशन में परिवर्तन के कारण. उस स्थिति में, प्रभावित उपयोगकर्ता के नाम पर कोई नया कार्य कार्य नहीं चलाया या शेड्यूल किया जा सकता है. यदि असाइन किया गया उपयोगकर्ता अभी भी नए IdP में मौजूद है, तो शेड्यूलिंग सहमति को निरस्त करें और फिर इसे फिर से प्रदान करें. यदि असाइन किया गया उपयोगकर्ता अब मौजूद नहीं है, तो एक नया कार्य कार्य स्वामी असाइन करें और आवश्यक कार्य शेड्यूलिंग सहमति प्रदान करें. निम्नलिखित SAP नोट देखें: अधिक जानकारी के लिए https://launchpad.support.sap.com/#/notes/3089828
TASK_EXECUTOR_ERROR=कार्य निर्वाहक
TASK_EXECUTOR_ERROR_ERR=इस कार्य को एक आंतरिक त्रुटि का सामना करना पड़ा, संभवतः निष्पादन के लिए तैयारी के चरणों के दौरान, और कार्य प्रारंभ नहीं किया जा सका.
PREREQ_NOT_MET=पूर्वापेक्षा पूर्ण नहीं हुई
PREREQ_NOT_MET_ERR=यह कार्य इसकी परिभाषा में समस्याओं के कारण रन नहीं किया जा सका. उदाहरण के लिए, ऑब्जेक्ट परिनियोजित नहीं किया गया है, किसी कार्य श्रृंखला में सर्कुलर तर्क शामिल है या किसी दृश्य का SQL अमान्य है.
RESOURCE_LIMIT_ERROR=संसाधन सीमा त्रुटि
RESOURCE_LIMIT_ERROR_ERR=वर्तमान में कार्य करने में असमर्थ क्योंकि पर्याप्त संसाधन उपलब्ध नहीं थे या व्यस्त थे.
FAIL_CONSENT_REFUSED_BY_UMS=सहमति अस्वीकार की गई
FAIL_CONSENT_REFUSED_BY_UMS_ERR=यह कार्य शेड्यूल किए गए रन या कार्य श्रृंखलाओं में, किराएदार पर उपयोगकर्ता के पहचान प्रदाता कॉन्फ़िगरेशन में परिवर्तन के कारण निष्पादित नहीं किया जा सका. अधिक जानकारी के लिए, निम्न SAP नोट देखें: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=शेड्यूल किया गया
#XFLD: status text
SCHEDULEDNew=स्थायी
#XFLD: status text
PAUSED=रोका गया
#XFLD: status text
DIRECT=प्रत्यक्ष
#XFLD: status text
MANUAL=मैन्युअल
#XFLD: status text
DIRECTNew=सामान्य
#XFLD: status text
COMPLETED=पूरा हुआ
#XFLD: status text
FAILED=विफल
#XFLD: status text
RUNNING=रन हो रहा है
#XFLD: status text
none=कोई नहीं
#XFLD: status text
realtime=वास्तविक-समय
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=सब-कार्य 
#XFLD: New Data available in the file
NEW_DATA=नया डेटा 

#XFLD: text for values shown in column Replication Status
txtOff=बंद
#XFLD: text for values shown in column Replication Status
txtInitializing=शुरु हो रहा है
#XFLD: text for values shown in column Replication Status
txtLoading=लोडिंग
#XFLD: text for values shown in column Replication Status
txtActive=सक्रिय करें
#XFLD: text for values shown in column Replication Status
txtAvailable=उपलब्ध
#XFLD: text for values shown in column Replication Status
txtError=त्रुटि
#XFLD: text for values shown in column Replication Status
txtPaused=रोकें
#XFLD: text for values shown in column Replication Status
txtDisconnected=डिसकनेक्ट किया गया
#XFLD: text for partially Persisted views
partiallyPersisted=आंशिक रूप से निर्बाधित

#XFLD: activity text
REPLICATE=प्रतिकृति बनाई गई
#XFLD: activity text
REMOVE_REPLICATED_DATA=प्रतिकृति बनाए गए डेटा को निकालें
#XFLD: activity text
DISABLE_REALTIME=वास्तविक समय डेटा प्रतिकृति अक्षम करें
#XFLD: activity text
REMOVE_PERSISTED_DATA=स्थायी डेटा निकालें
#XFLD: activity text
PERSIST=जारी
#XFLD: activity text
EXECUTE=निष्पादित करें
#XFLD: activity text
TASKLOG_CLEANUP=Tacklog_Cleanup
#XFLD: activity text
CANCEL_REPLICATION=प्रतिकृति रद्द करें
#XFLD: activity text
MODEL_IMPORT=मॉडल आयात
#XFLD: activity text
NONE=कोई नहीं
#XFLD: activity text
CANCEL_PERSISTENCY=निर्बाध रद्द करें
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=दृश्य विश्लेषण करें
#XFLD: activity text
CANCEL_VIEW_ANALYZER=दृश्य विश्लेषक रद्द करें

#XFLD: severity text
INFORMATION=जानकारी
#XFLD: severity text
SUCCESS=सफलता
#XFLD: severity text
WARNING=चेतावनी
#XFLD: severity text
ERROR=त्रुटि
#XFLD: text for values shown for Ascending sort order
SortInAsc=आरोही क्रम में सॉट करें
#XFLD: text for values shown for Descending sort order
SortInDesc=अवरोही क्रम में क्रमित करें
#XFLD: filter text for task log columns
Filter=फिल्टर करें
#XFLD: object text for task log columns
Object=ऑब्जेक्ट
#XFLD: space text for task log columns
crossSpace=स्पेस

#XBUT: label for remote data access
REMOTE=दूरस्थ
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=प्रतिकृति बनाई गई (वास्तविक समय)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=प्रतिकृति बनाई गई (स्नैपशॉट)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=किसी त्रुटि के कारण रीयल-टाइम प्रतिकृति अवरोधित है. एक बार त्रुटि ठीक हो जाने के बाद, आप रीयल-टाइम प्रतिकृति के साथ जारी रखने के लिए "पुन: प्रयास करें" क्रिया का उपयोग कर सकते हैं.
ERROR_MSG=किसी त्रुटि के कारण रीयल-टाइम प्रतिकृति अवरोधित है.
RETRY_FAILED_ERROR=त्रुटि के साथ पुन: प्रयास प्रक्रिया विफल रही.
LOG_INFO_DETAILS=डेटा के रियल टाइम मोड में प्रतिकृति होने पर कोई लॉग जनरेट नहीं होते हैं. प्रदर्शित लॉग पिछली कार्रवाई से संबंधित होते हैं.

#XBUT: Partitioning label
partitionMenuText=विभाजन
#XBUT: Drop down menu button to create a partition
createPartitionLabel=विभाजन बनाएं
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=विभाजन संपादित करें
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=विभाजन हटाएं
#XFLD: Initial text
InitialPartitionText=बड़े डेटासेट को छोटे सेट में विभाजित करने के लिए मानदंड निर्दिष्ट करके विभाजन को परिभाषित करें.
DefinePartition=विभाजनों को परिभाषित करें
#XFLD: Message text
partitionChangedInfo=पिछले प्रतिकृति के बाद से विभाजन की परिभाषा बदल गई है. परिवर्तन अगले डेटा लोड पर लागू किए जाएंगे.
#XFLD: Message text
REAL_TIME_WARNING=विभाजन केवल एक नया स्नैपशॉट लोड करते समय लागू किया जाता है. यह रीयल-टाइम प्रतिकृति के लिए लागू नहीं है.
#XFLD: Message text
loadSelectedPartitions=‘''{0}''’ के चयनित विभाजन के लिए निर्बाध डेटा आरंभ की गई
#XFLD: Message text
loadSelectedPartitionsError=‘''{0}''’ के चयनित विभाजन के लिए निर्बाध डेटा विफल हुई
#XFLD: Message text
viewpartitionChangedInfo=पिछली दृढ़ता रन करने के बाद से विभाजन की परिभाषा बदल गई है. परिवर्तनों को लागू करने के लिए, अगला डेटा लोड लॉक किए गए विभाजनों सहित एक पूर्ण स्नैपशॉट होगा. एक बार यह पूरा भार समाप्त हो जाने पर, आप एकल विभाजनों के लिए डेटा रन में सक्षम होंगे.
#XFLD: Message text
viewpartitionChangedInfoLocked=पिछली दृढ़ता रन करने के बाद से विभाजन की परिभाषा बदल गई है. परिवर्तनों को लागू करने के लिए, लॉक और अपरिवर्तित विभाजन श्रेणियों को छोड़कर, अगला डेटा लोड कोई पूर्ण स्नैपशॉट होगा. एक बार यह लोड समाप्त हो जाने के बाद, आप चयनित विभाजनों को फिर से लोड करने में सक्षम होंगे.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=तालिका प्रतिकृति
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=प्रतिकृति शेड्यूल करें
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=शेड्यूल बनाएं
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=शेड्यूल संपादित करें
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=शेड्यूल हटाएं
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=नया स्नैपशॉट लोड करें
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=डेटा प्रतिकृति प्रारंभ करें
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=हटाए गए डेटा को निकालें
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=वास्तविक-समय एक्सेस सक्षम करें
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=वास्तविक समय डेटा प्रतिकृति सक्षम करें
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=वास्तविक समय डेटा प्रतिकृति अक्षम करें
#XFLD: Message for replicate table action
replicateTableText=तालिका प्रतिकृति
#XFLD: Message for replicate table action
replicateTableTextNew=डेटा प्रतिकृति
#XFLD: Message to schedule task
scheduleText=शेड्यूल
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=स्थिरता देखें
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=डेटा निर्बाध
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=नया स्नैपशॉट लोड करें
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=डेटा निर्बाध प्रारंभ करें
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=स्थायी डेटा निकालें
#XFLD: Partitioned Processign
EnablePartitionedProcessing=विभाजन प्रक्रिया
#XBUT: Label for scheduled replication
scheduledTxt=शेड्यूल किया गया
#XBUT: Label for statistics button
statisticsTxt=सांख्यिकी
#XBUT: Label for create statistics
createStatsTxt=सांख्यिकी बनाएं
#XBUT: Label for edit statistics
editStatsTxt=सांख्यिकी संपादित करें
#XBUT: Label for refresh statistics
refreshStatsTxt=सांख्यिकी रिफ़्रेश करें
#XBUT: Label for delete statistics
dropStatsTxt=सांख्यिकी हटाएं
#XMSG: Create statistics success message
statsSuccessTxt={1} के लिए {0} प्रकार के सांख्यिकी बनाना शुरू करें.
#XMSG: Edit statistics success message
statsEditSuccessTxt={1} के लिए {0} में सांख्यिकी प्रकार बदलना शुरू करें.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt={0} के लिए सांख्यिकी रीफ़्रेश करना शुरू करें.
#XMSG: Drop statistics success message
statsDropSuccessTxt={0} के लिए सांख्यिकी सफ़लतापूर्वक हटाई गई
#XMSG: Create statistics error message
statsCreateErrorTxt=सांख्यिकी बनाते समय त्रुटि
#XMSG: Edit statistics error message
statsEditErrorTxt=सांख्यिकी परिवर्तित करते समय त्रुटि
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=सांख्यिकी रिफ़्रेश करते समय त्रुटि
#XMSG: Drop statistics error message
statsDropErrorTxt=सांख्यिकी हटाते समय त्रुटि
#XMG: Warning text for deleting statistics
statsDelWarnTxt=क्या आप वाकई डेटा सांख्यिकी को ड्रॉप करना चाहते हैं?
startPersistencyAdvisorLabel=दृश्य विश्लेषक आरंभ करें

#Partition related texts
#XFLD: Label for Column
column=स्तंभ
#XFLD: Label for No of Partition
noOfPartitions=विभाजन की संख्या
#XFLD: Label for Column
noOfParallelProcess=समानांतर प्रक्रियाओं की संख्या
#XFLD: Label text
noOfLockedPartition=लॉक की गई विभाजनों की संख्या
#XFLD: Label for Partition
PARTITION=विभाजन
#XFLD: Label for Column
AVAILABLE=उपलब्ध
#XFLD: Statistics Label
statsLabel=सांख्यिकी
#XFLD: Label text
COLUMN=स्तंभः
#XFLD: Label text
PARALLEL_PROCESSES=समानांतर प्रक्रियाएंः
#XFLD: Label text
Partition_Range=विभाजन श्रेणी
#XFLD: Label text
Name=नाम
#XFLD: Label text
Locked=लॉक
#XFLD: Label text
Others=अन्य
#XFLD: Label text
Delete=हटाएं
#XFLD: Label text
LoadData=चयनित विभाजन लोड करें
#XFLD: Label text
LoadSelectedData=चयनित विभाजन लोड करें
#XFLD: Confirmation text
LoadNewPersistenceConfirm=यह सभी अनलॉक किए गए और बदले गए विभाजनों के लिए नया स्नैपशॉट लोड करेगा, केवल आपके द्वारा चयनित का ही नहीं. क्या आप जारी रखना चाहते हैं?
#XFLD: Label text
Continue=जारी रखें

#XFLD: Label text
PARTITIONS=विभाजन
#XFLD: Label text
ADD_PARTITIONS=+ विभाजन जोड़ें
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=विभाजन जोड़ें
#XFLD: Label text
deleteRange=विभाजन हटाएं
#XFLD: Label text
LOW_PLACE_HOLDER=निम्न मान दर्ज करें
#XFLD: Label text
HIGH_PLACE_HOLDER=उच्च मान दर्ज करें
#XFLD: tooltip text
lockedTooltip=आरंभिक लोड के बाद विभाजन लॉक करें

#XFLD: Button text
Edit=संपादित करें
#XFLD: Button text
CANCEL=रद्द करें

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=अंतिम सांख्यिकी अपडेट
#XFLD: Statistics Fields
STATISTICS=सांख्यिकी

#XFLD:Retry label
TEXT_Retry=पुन: प्रयास करें
#XFLD:Retry label
TEXT_Retry_tooltip=त्रुटि हल होने के बाद वास्तविक समय प्रतिकृति का पुन: प्रयास करें.
#XFLD: text retry
Retry=पुष्टि करें
#XMG: Retry confirmation text
retryConfirmationTxt=अंतिम रीयल-टाइम प्रतिकृति को त्रुटि के साथ समाप्त किया गया.\n पुष्टि करें कि त्रुटि को ठीक किया गया है और वास्तविक-समय की प्रतिकृति को फिर से शुरू किया जा सकता है.
#XMG: Retry success text
retrySuccess=पुनर्प्रयास प्रक्रिया सफलतापूर्वक शुरू की गई.
#XMG: Retry fail text
retryFail=पुनर्प्रयास प्रक्रिया विफल रही.
#XMSG: activity message for create statistics
CREATE_STATISTICS=सांख्यिकी बनाएं
#XMSG: activity message for edit statistics
DROP_STATISTICS=सांख्यिकी हटाएं
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=सांख्यिकी रिफ़्रेश करें
#XMSG: activity message for edit statistics
ALTER_STATISTICS=सांख्यिकी संपादित करें
#XMSG: Task log message started task
taskStarted=कार्य {0} शुरू हो गया है.
#XMSG: Task log message for finished task
taskFinished=कार्य {0} स्थिति {1} के साथ समाप्त हुआ.
#XMSG: Task log message for finished task with end time
taskFinishedAt=कार्य {0} {2} पर {1} स्थिति के साथ समाप्त हुआ.
#XMSG: Task {0} has input parameters
taskHasInputParameters=कार्य {0} में इनपुट पैरामीटर है.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=कार्य {0} एक अप्रत्याशित त्रुटि के साथ समाप्त हुआ. कार्य की स्थिति {1} पर सेट की गई है.
#XMSG: Task log message for failed task
failedToEnd=स्थिति को {0} पर सेट करने में विफ़ल या लॉक निकालने में विफ़ल.
#XMSG: Task log message
lockNotFound=हम प्रक्रिया को अंतिम रूप नहीं दे सकते क्योंकि लॉक गायब है: हो सकता है कि कार्य रद्द कर दिया गया हो.
#XMSG: Task log message failed task
failedOverwrite={1} द्वारा कार्य {0} पहले से ही बंद है. इसलिए, यह निम्न त्रुटि के साथ विफल रहा: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=इस कार्य के लॉक को दूसरे कार्य ने अपने हाथ में ले लिया.
#XMSG: Task log message failed takeover
failedTakeover=किसी मौजूदा कार्य को संभालने में विफ़ल.
#XMSG: Task log message successful takeover
successTakeover=रिलीज़ होने वाला, लॉक किया हुआ कार्य अनलॉक हुआ. इस कार्य के लिए नए लॉक सेट है.
#XMSG: Tasklog Dialog Details
txtDetails=रन के दौरान प्रोसेस किए गए रिमोट दूरस्थ विवरण को विभाजन-विशिष्ट संदेशों के विवरण में दूरस्थ क्वेरी मॉनिटर खोलकर प्रदर्शित किया जा सकता है.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=दूरस्थ SQL विवरणों को डेटाबेस से हटा दिया गया है और प्रदर्शित नहीं किया जा सकता है.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=दूरस्थ प्रश्न जिनके कनेक्शन अन्य स्थानों को निर्दिष्ट किए गए हैं, प्रदर्शित नहीं किए जा सकते. दूरस्थ क्वेरी मॉनिटर पर जाएं और उन्हें फ़िल्टर करने के लिए विवरण ID का उपयोग करें.
#XMSG: Task log message for parallel check error
parallelCheckError=कार्य को संसाधित नहीं किया जा सकता क्योंकि कोई अन्य कार्य रन हो रहा है और पहले से ही इस कार्य को अवरुद्ध कर रहा है.
#XMSG: Task log message for parallel running task
parallelTaskRunning=एक विवादित कार्य पहले से ही रन हो रहा है.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=सहसंबंध ID {1} के साथ रन करने के दौरान स्थिति {0}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=असाइन किए गए उपयोगकर्ता के पास इस कार्य को निष्पादित करने के लिए आवश्यक विशेषाधिकार नहीं हैं.

#XBUT: Label for open in Editor
openInEditor=संपादक में खोलें
#XBUT: Label for open in Editor
openInEditorNew=डेटा बिल्डर में खोलें
#XFLD:Run deails label
runDetails=रन विवरण
#XFLD: Label for Logs
Logs=लॉग
#XFLD: Label for Settings
Settings=सेटिंग
#XFLD: Label for Save button
Save=सहेजें
#XFLD: Label for Standard
Standard_PO=प्रदर्शन-अनुकूलित (अनुशंसित)
#XFLD: Label for Hana low memory processing
HLMP_MO=मेमोरी-ऑप्टिमाइज़ की गई
#XFLD: Label for execution mode
ExecutionMode=रन मोड
#XFLD: Label for job execution
jobExecution=प्रोसेसिंग मोड
#XFLD: Label for Synchronous
syncExec=तुल्यकालिक
#XFLD: Label for Asynchronous
asyncExec=एसिंक्रोनस
#XFLD: Label for default asynchronous execution
defaultAsyncExec=उपयोग डिफ़ॉल्ट (अतुल्यकालिक, भविष्य में बदल सकते हैं)
#XMSG: Save settings success
saveSettingsSuccess=SAP HANA निष्पादन मोड बदला गया.
#XMSG: Save settings failure
saveSettingsFailed=SAP HANA निष्पादन मोड परिवर्तन विफल हुआ.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=जॉब निष्पादन बदला गया.
#XMSG: Job Execution change failed
jobExecSettingFailed=जॉब निष्पादन परिवर्तन विफल हुआ.
#XMSG: Text for Type
typeTxt=प्रकार
#XMSG: Text for Monitor
monitorTxt=निरीक्षण करें
#XMSG: Text for activity
activityTxt=गतिविधि
#XMSG: Text for metrics
metricsTxt=मैट्रिक्स
#XTXT: Text for Task chain key
TASK_CHAINS=कार्य श्रृंखला
#XTXT: Text for View Key
VIEWS=दृश्य
#XTXT: Text for remote table key
REMOTE_TABLES=रिमोट तालिका
#XTXT: Text for Space key
SPACE=स्पेस
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=मूल्य-सापेक्षता कम्प्यूट नोड
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=प्रतिकृति प्रवाह
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=इंटेलिजेंट लुकअप
#XTXT: Text for Local Table
LOCAL_TABLE=लोकल तालिका
#XTXT: Text for Data flow key
DATA_FLOWS=डेटा प्रवाह
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=SQL स्क्रिप्ट प्रक्रिया
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=BW प्रक्रिया श्रृंखला
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=मॉनिटर में दृश्य
#XTXT: Task List header text
taskListHeader=कार्य सूची ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=डेटा प्रवाह के पिछले रनों के मैट्रिक्स को पुनर्प्राप्त नहीं किया जा सकता है.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=इस क्षण पूर्ण निष्पादन विवरण लोड नहीं हो रहा है. रीफ़्रेश करने का प्रयास करें.
#XFLD: Label text for the Metrices table header
metricesColLabel=ऑपरेटर लेबल
#XFLD: Label text for the Metrices table header
metricesType=प्रकार
#XFLD: Label text for the Metrices table header
metricesRecordLabel=रिकॉर्ड संख्या
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=कार्य श्रृंखला रन करें
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=कार्य श्रृंखला रन आरंभ किया गया.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg={0} के लिए कार्य श्रृंखला रन प्रारंभ किया गया.
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=कार्य श्रृंखला रन करने में विफल हुआ.
#XTXT: Execute button label
runLabel=रन
#XTXT: Execute button label
runLabelNew=रन प्रारंभ करें
#XMSG: Filter Object header
chainsFilteredTableHeader=ऑब्जेक्ट द्वारा फिल्टर किया गया: {0}
#XFLD: Parent task chain label
parentChainLabel=पेरेंट कार्य श्रृंखला:
#XFLD: Parent task chain unauthorized
Unauthorized=देखने के लिए प्राधिकृत नहीं है
#XFLD: Parent task chain label
parentTaskLabel=पैरेंट कार्य:
#XTXT: Task status
NOT_TRIGGERED=ट्रिगर नहीं किया गया
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=पूर्ण स्क्रीन मोड दर्ज करें
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=पूर्ण स्क्रीन मोड से बाहर निकलें
#XTXT: Close Task log details right panel
closeRightColumn=अनुभाग बंद करें
#XTXT: Sort Text
sortTxt=क्रमित करें
#XTXT: Filter Text
filterTxt=फ़िल्टर करें
#XTXT: Filter by text to show list of filters applied
filterByTxt=इसके अनुसार फ़िल्टर करें
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=5 मिनट से अधिक
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=15 मिनट से अधिक
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=1 घंटा से अधिक
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=आखिरी घंटे
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=आखिरी 24 घंटे
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=अंतिम माह
#XTXT: Messages title text
messagesText=संदेश

#XTXT Statistics information message
statisticsInfo=डेटा पहुंच ''प्रतिकृति'' वाली दूरस्थ तालिकाओं के लिए सांख्यिकी नहीं बनाए जा सकते. सांख्यिकी बनाने के लिए, दूरस्थ तालिका विवरण मॉनिटर में प्रतिरूपित डेटा निकालें.
#XFLD
GO_TO_REMOTETABLE_DETAILS=दूरस्थ तालिका विवरण मॉनिटर के लिए जाएं

#XTXT: Repair latest failed run label
retryRunLabel=नवीनतम रन पुनःप्रयास करें
#XTXT: Repair failed run label
retryRun=रन का पुनःप्रयास करें
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=कार्य श्रृंखला पुनःप्रयास रन आरंभ किया गया
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=कार्य श्रृंखला पुनः प्रयास रन विफल हुआ
#XMSG: Task chain child elements name
taskChainRetryChildObject=कार्य {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=नया कार्य
#XFLD Analyzed View
analyzedView=विश्लेषित दृश्य
#XFLD Metrics
Metrics=मैट्रिक्स
#XFLD Partition Metrics
PartitionMetrics=विभाजन मेट्रिक्स
#XFLD Entities
Messages=कार्य लॉग
#XTXT: Title Message for empty partition data
partitionEmptyTitle=अभी तक कोई विभाजन निर्धारित नहीं किया गया है.
#XTXT: Description message for empty partition data
partitionEmptyDescText=बड़े डेटा वॉल्यूम को छोटे, अधिक प्रबंधनीय भागों में तोड़ने के लिए मापदंड निर्दिष्ट कर विभाजन बनाएं.

#XTXT: Title Message for empty runs data
runsEmptyTitle=अभी तक कोई लॉग उपलब्ध नहीं है
#XTXT: Description message for empty runs data
runsEmptyDescText=जब आप कोई नई गतिविधि प्रारंभ करते हैं (नया स्नैपशॉट लोड करें, विश्लेषक देखना प्रारंभ करें...) तो आपको यहां संबंधित लॉग दिखाई देंगे.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=इलास्टिक कंप्यूट नोड {0} कॉन्फ़िगरेशन, का तदनुसार रखरखाव नहीं किया गया है. कृपया अपनी परिभाषा की जाँच करें.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=मूल्य-सापेक्षता गणना नोड {0} को जोड़ने की प्रक्रिया विफल रही.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=इलास्टिक कंप्यूट नोड {2} में स्रोत ऑब्जेक्ट ''{0}''.''{1}''’ के लिए प्रतिकृति का निर्माण और सक्रियण प्रारंभ हो गया है.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=इलास्टिक कंप्यूट नोड {2} से स्रोत ऑब्जेक्ट ''{0}''.''{1}''’के लिए प्रतिकृति निकालना प्रारंभ किया गया है.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=स्रोत ऑब्जेक्ट''{0}''.''{1}''’के लिए प्रतिकृति का निर्माण और सक्रियण विफल हो गया है.
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=स्रोत ऑब्जेक्ट ''{0}''.''{1}''’ के लिए प्रतिकृति निकालना विफल रहा.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=मूल्य-सापेक्षता गणना नोड {1} के लिए स्थान {0} की विश्लेषणात्मक क्वेरी परिगणना की रूटिंग शुरू हो गई है.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=स्थान {0} के विश्लेषणात्मक प्रश्नों की संगणना को संबंधित मूल्य-सापेक्षता  गणना नोड में रूटिंग विफल रहा.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=मूल्य-सापेक्षता परिकलन नोड {1} से स्थान {0} की विश्लेषणात्मक क्वेरी परिकलन को दोबारा रूटिंग करना शुरू हुआ.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=समन्वयक को वापस स्थान {0} की विश्लेषणात्मक क्वेरी गणना को पुन: रूटिंग करना विफल रहा.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=संबंधित मूल्य-सापेक्षता गणना नोड {1} के लिए कार्य शृंखला {0} को ट्रिगर किया गया है.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=मूल्य-सापेक्षता गणना नोड {0} के लिए कार्य शृंखला जनरेट करना विफल रहा.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=इलास्टिक कंप्यूट नोड {0} की प्रोविजनिंग दिए गए आकार योजना के साथ शुरू हो गया है: vCPUs: {1}, मेमोरी (GiB):{2} और स्टोरेज आकार (GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=इलास्टिक कंप्यूट नोड {0}पहले ही प्रोविज़न किया जा चुका है.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=इलास्टिक कंप्यूट नोड {0}पहले ही डीप्रोविज़न किया जा चुका है.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=कार्रवाई की अनुमति नहीं है. कृपया इलास्टिक कंप्यूट नोड {0} को रोकें और साइज़िंग योजना को अपडेट करने के लिए इसे पुनः प्रारंभ करें.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=मूल्य-सापेक्षता गणना नोड {0} को निकालने की प्रक्रिया विफल रही.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=मूल्य-सापेक्षता गणना नोड {0} का वर्तमान रन, टाइम आउट हो गया.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=मूल्य-सापेक्षता गणना नोड {0} की स्थिति की जांच प्रगति में है...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=मूल्य-सापेक्षता गणना नोड {0} के लिए कार्य श्रृंखला जनरेशन लॉक है, इसलिए श्रृंखला {1} अभी भी चल रही हो सकती है.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=स्रोत ऑब्जेक्ट "{0}"."{1}” की "{2}"."{3}” दृश्य की निर्भरता के रूप में प्रतिकृति की गई है.
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=तालिका "{0}"."{1}" की प्रतिकृति बनाने में असमर्थ, क्योंकि पंक्ति तालिका की प्रतिकृति रोकी गई है.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=SAP HANA दृष्टांत के अनुसार अधिकतम इलास्टिक कंप्यूट नोड पार हो गया है.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=इलास्टिक कंप्यूट नोड {0} के लिए रनिंग ऑपरेशन अभी भी प्रगति पर है.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=तकनीकी समस्याओं के कारण, स्रोत तालिका {0} के लिए प्रतिकृति को हटाना रोक दिया गया है. कृपया बाद में पुनः प्रयास करें.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=तकनीकी समस्याओं के कारण, स्रोत तालिका {0} के लिए प्रतिकृति बनाना रोक दिया गया है. कृपया बाद में पुनः प्रयास करें.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=इलास्टिक कंप्यूट नोड प्रारंभ नहीं किया जा सकता क्योंकि उपयोग सीमा पूरी हो गई है या अभी तक कोई कंप्यूट ब्लॉक-घंटे आवंटित नहीं किए गए हैं.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=कार्य श्रृंखला लोड हो रही है और कुल {0} कार्यों को रन करने की तैयारी है जो इस श्रृंखला का हिस्सा हैं.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=एक विवादित कार्य पहले से ही रन हो रहा है.
#XMSG: Replication will change
txt_replication_change=प्रतिकृति प्रकार बदल दिया जाएगा.
txt_repl_viewdetails=विवरण देखें

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=ऐसा लगता है कि नवीनतम रन के पुन: प्रयास में कोई त्रुटि थी क्योंकि योजना जनरेट होने से पहले पिछला कार्य रन विफल हो गया था.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=स्पेस "{0}" लॉक किया गया.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=गतिविधि {0} के लिए स्थानीय तालिका {1} को परिनियोजित करना आवश्यक है.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=गतिविधि {0} के लिए स्थानीय तालिका {1} हेतु डेल्टा कैप्चर को चालू करना आवश्यक है.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=गतिविधि {0} को ऑब्जेक्ट स्टोर में डेटा को स्टोर करने के लिए स्थानीय तालिका {1} की आवश्यकता होती है.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=गतिविधि {0} को डेटाबेस में डेटा को स्टोर करने के लिए स्थानीय तालिका {1} की आवश्यकता होती है.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=स्थानीय तालिका {0} के लिए निकाले गए रिकॉर्ड को हटाना प्रारंभ हो रहा है.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=स्थानीय तालिका {0} के लिए हटाए गए रिकॉर्ड को हटाना प्रारंभ हो रहा है.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=फ़िल्टर स्थिति {1} के अनुसार स्थानीय तालिका {0} के लिए रिकॉर्ड हटाना प्रारंभ करना.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=स्थानीय तालिका {0} के लिए निकाले गए रिकॉर्ड हटाते समय एक त्रुटि उत्पन्न हुई.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=स्थानीय तालिका {0} के लिए सभी रिकॉर्ड हटाते समय एक त्रुटि उत्पन्न हुई.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=‘हटाए गए'' प्रकार के परिवर्तन के साथ सभी पूर्णतः संसाधित रिकॉर्ड हटाएं जो {0} दिनों से अधिक पुराने हैं.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=‘हटाए गए'' प्रकार के परिवर्तन के साथ सभी पूर्णतः संसाधित रिकॉर्ड हटाएं जो {0} से अधिक पुराने हैं.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} रिकॉर्ड हटा दिए गए हैं.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS=हटाने के लिए {0} रिकॉर्ड चिह्नित किए गए.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=स्थानीय तालिका {0} के लिए हटाए गए रिकॉर्ड को हटाना पूरा हो गया है.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=स्थानीय तालिका {0} के सभी रिकॉर्ड हटाने का कार्य पूरा हो गया है.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=स्थानीय तालिका {0} के लिए रिकॉर्ड को "हटाए गए" के रूप में चिह्नित करना प्रारंभ किया जा रहा है.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=फ़िल्टर स्थिति {1} के अनुसार स्थानीय तालिका {0} के लिए रिकॉर्ड्स को "हटाए गए" के रूप में चिह्नित करना प्रारंभ करना
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=स्थानीय तालिका {0} के लिए रिकॉर्ड को "हटाए गए" के रूप में चिह्नित करने का कार्य पूर्ण हुआ है.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=तालिका {0} में डेटा परिवर्तन अस्थायी रूप से लोड हो रहे हैं.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=तालिका {0} में डेटा परिवर्तन अस्थायी रूप से लोड हो गए. 
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=तालिका {0}से डेटा परिवर्तन संसाधित और हटा दिए जाते हैं.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=कनेक्शन विवरण

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=स्थानीय तालिका (फ़ाइल) को ऑप्टिमाइज़ करना प्रारंभ कर रहा है
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=स्थानीय तालिका (फ़ाइल) को ऑप्टिमाइज़ करते समय कोई त्रुटि हुई
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=कोई त्रुटि हुई. स्थानीय तालिका (फ़ाइल) को ऑप्टिमाइज़ करना रोक दिया गया है.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=स्थानीय तालिका (फ़ाइल) को ऑप्टिमाइज़ करना...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=स्थानीय तालिका (फ़ाइल) को ऑप्टिमाइज़ कर दिया गया है. 
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=स्थानीय तालिका (फ़ाइल) को ऑप्टिमाइज़ है. 
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=तालिका Z-ऑर्डर स्तंभों के साथ अनुकूलित है: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=एक त्रुटि हुई. स्थानीय तालिका (फ़ाइल) को संक्षिप्त करना रोक दिया गया है.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=स्थानीय तालिका (फ़ाइल) को छोटा करना...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=स्थानीय तालिका (फ़ाइल) के लिए इनबाउंड बफ़र स्थान हटा दिया गया है.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=स्थानीय तालिका (फ़ाइल) को वैक्यूम करना शुरू करना (सभी पूरी तरह से प्रोसेस किए गए रिकॉर्ड हटाएं)
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=स्थानीय तालिका (फ़ाइल) को वैक्यूम करते समय कोई त्रुटि हुई.
#XMSG: Task log message
LTF_VACUUM_STOPPED=कोई त्रुटि हुई. स्थानीय तालिका (फ़ाइल) को वैक्यूम करना रोक दिया गया है.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=स्थानीय तालिका (फ़ाइल) को वैक्यूम करना...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=वैक्यूम करना पूर्ण हुआ.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO={0} दिन से अधिक पुराने सभी पूरी तरह से संसाधित रिकॉर्ड को हटाना.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING={0} से अधिक पुराने सभी पूरी तरह से संसाधित रिकॉर्ड को हटाना.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=स्थानीय तालिका (फ़ाइल) के साथ नए रिकॉर्ड मर्ज करना प्रारंभ करना.
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING="डेटा स्वचालित रूप से मर्ज करें" सेटिंग का उपयोग करके स्थानीय तालिका (फ़ाइल) के साथ नए रिकॉर्ड को मर्ज करना प्रारंभ करना.
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=स्थानीय तालिका (फ़ाइल) के साथ नए रिकॉर्ड मर्ज करना शुरू करना. यह कार्य API मर्ज अनुरोध के माध्यम से शुरू किया गया था.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=स्थानीय तालिका (फ़ाइल) के साथ नए रिकॉर्ड मर्ज करना.
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=स्थानीय तालिका (फ़ाइल) के साथ नए रिकॉर्ड मर्ज करते समय कोई त्रुटि हुई.
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=स्थानीय तालिका (फ़ाइल) मर्ज कर दी गई है.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=कोई त्रुटि हुई. स्थानीय तालिका (फ़ाइल) को मर्ज करना रोक दिया गया है.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=स्थानीय तालिका (फ़ाइल) का मर्ज कोई त्रुटि के कारण विफल हो गया है, लेकिन संचालन आंशिक रूप से सफल रहा और कुछ डेटा पहले ही मर्ज हो चुका है.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=कोई टाइमआउट त्रुटि उत्पन्न हुई. गतिविधि {0} {1} घंटों से रन हो रही है.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=उच्च सिस्टम लोड के कारण, अतुल्यकालिक निष्पादन प्रारंभ नहीं हो सका. "सिस्टम मॉनिटर" खोलें और रन हो रहे कार्यों की जांच करें.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=अतुल्यकालिक निष्पादन रद्द कर दिया गया है.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION={0} कार्य {1} और {2} की स्मृति सीमाओं के भीतर रन हुआ.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=संसाधन ID {1} के साथ {0} कार्य रन हुआ.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=स्थानीय तालिका (फ़ाइल) में खोजें और प्रतिस्थापित करें प्रारंभ हो गया है.
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=स्थानीय तालिका (फ़ाइल) में खोजें और प्रतिस्थापित करें पूर्ण हो गया है.
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=स्थानीय तालिका (फ़ाइल) में ढूँढ़ें और प्रतिस्थापित करें विफल हो गया है.

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=एक त्रुटि हुई. स्थानीय तालिका (फ़ाइल) के लिए सांख्यिकी अपडेट करना रोक दिया गया है.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=SAP HANA database पर मेमोरी समाप्त होने की त्रुटि के कारण कार्य विफल हो गया.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=SAP HANA प्रवेश नियंत्रण अस्वीकृति के कारण कार्य विफल हो गया.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=बहुत अधिक सक्रिय SAP HANA कनेक्शन के कारण कार्य विफल हो गया.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=पुनः प्रयास ऑपरेशन निष्पादित नहीं किया जा सका क्योंकि पुनः प्रयास की अनुमति केवल नेस्टेड कार्य श्रृंखला के पैरेंट पर ही है.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=विफल चाइल्ड कार्यों के लॉग अब आगे बढ़ने के लिए पुनः प्रयास के लिए उपलब्ध नहीं हैं.


####Metrics Labels

performanceOptimized=प्रदर्शन-अनुकूलित
memoryOptimized=मेमोरी-ऑप्टिमाइज़ की गई

JOB_EXECUTION=जॉब निष्पादन
EXECUTION_MODE=रन मोड
NUMBER_OF_RECORDS_OVERALL=कायम रिकार्डों की संख्या
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=दूरस्थ स्रोत से पढ़े गए रिकॉर्ड की संख्या
RUNTIME_MS_REMOTE_EXECUTION_TIME=दूरस्थ स्रोत प्रोसेसिंग समय
MEMORY_CONSUMPTION_GIB=SAP HANA पिक स्मृति
NUMBER_OF_PARTITIONS=विभाजन की संख्या
MEMORY_CONSUMPTION_GIB_OVERALL=SAP HANA पिक स्मृति
NUMBER_OF_PARTITIONS_LOCKED=लॉक किए गए विभाजन की संख्या
PARTITIONING_COLUMN=विभाजन स्तंभ
HANA_PEAK_CPU_TIME=SAP HANA कुलयोग CPU समय
USED_IN_DISK=उपयोग किया गया संग्रहण
INPUT_PARAMETER_PARAMETER_VALUE=इनपुट पैरामीटर
INPUT_PARAMETER=इनपुट पैरामीटर
ECN_ID=इलास्टिक कंप्यूट नोड नाम

DAC=डेटा पहुंच नियंत्रण
YES=हां
NO=नहीं
noofrecords=रिकॉर्ड की संख्या
partitionpeakmemory=SAP HANA पिक स्मृति
value=मान
metricsTitle=मैट्रिक्स  ({0})
partitionmetricsTitle=विभाजन ({0})
partitionLabel=विभाजन
OthersNotNull=श्रेणी में मान शामिल नहीं हैं
OthersNull=शून्य मान
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=अंतिम डेटा निर्बाध रन के लिए उपयोग की गई सेटिंग:
#XMSG: Message for input parameter name
inputParameterLabel=इनपुट पैरामीटर
#XMSG: Message for input parameter value
inputParameterValueLabel=मान
#XMSG: Message for persisted data
inputParameterPersistedLabel=इस पर निर्बाधित
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=डेटा हटाएं
REMOVE_DELETED_RECORDS=हटाए गए रिकॉर्ड निकालें
MERGE_FILES=फ़ाइलें मर्ज करें
OPTIMIZE_FILES=फ़ाइलें ऑप्टिमाइज़ करें
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=SAP BW Bridge Monitor में दृश्य

ANALYZE_PERFORMANCE=प्रदर्शन का विश्लेषण करें
CANCEL_ANALYZE_PERFORMANCE=प्रदर्शन का विश्लेषण रद्द करें

#XFLD: Label for frequency column
everyLabel=प्रत्येक
#XFLD: Plural Recurrence text for Hour
hoursLabel=घंटे
#XFLD: Plural Recurrence text for Day
daysLabel=दिन
#XFLD: Plural Recurrence text for Month
monthsLabel=महीने
#XFLD: Plural Recurrence text for Minutes
minutesLabel=मिनट

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">दृश्य निर्बांध समस्या निवारण मार्गदर्शिका</a>
#XTXT TEXT for view persistency guide link
OOMMessage=प्रक्रिया मेमोरी से बाहर है. दृश्य "{0}" के लिए डेटा का निर्बांध करने में असमर्थ. मेमोरी समाप्त होने की त्रुटियों के बारे में अधिक जानकारी के लिए Help Portal से परामर्श लें. मेमोरी खपत जटिलता के लिए दृश्य का विश्लेषण करने के लिए दृश्य विश्लेषक की जाँच करने पर विचार करें.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=लागू नहीं
OPEN_BRACKET=(
CLOSE_BRACKET=)
