
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=تفاصيل السجل
#XFLD: Header
TASK_LOGS=سجلات المهام ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=عمليات التشغيل ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=عرض التفاصيل
#XFLD: Button text
STOP=إيقاف التشغيل
#XFLD: Label text
RUN_START=بداية آخر تشغيل
#XFLD: Label text
RUN_END=نهاية آخر تشغيل
#XFLD: Label text
RUNTIME=المدة
#XTIT: Count for Messages
txtDetailMessages=الرسائل ({0})
#XFLD: Label text
TIME=طابع الوقت
#XFLD: Label text
MESSAGE=الرسالة
#XFLD: Label text
TASK_STATUS=الفئة
#XFLD: Label text
TASK_ACTIVITY=النشاط
#XFLD: Label text
RUN_START_DETAILS=بدء
#XFLD: Label text
RUN_END_DETAILS=انتهاء
#XFLD: Label text
LOGS=عمليات التشغيل
#XFLD: Label text
STATUS=الحالة
#XFLD: Label text
RUN_STATUS=حالة التشغيل
#XFLD: Label text
Runtime=المدة
#XFLD: Label text
RuntimeTooltip=المدة (hh : mm : ss)
#XFLD: Label text
TRIGGEREDBY=تم بدء التشغيل بواسطة
#XFLD: Label text
TRIGGEREDBYNew=تشغيل بواسطة
#XFLD: Label text
TRIGGEREDBYNewImp=تم بدء التشغيل بواسطة
#XFLD: Label text
EXECUTIONTYPE=نوع التنفيذ
#XFLD: Label text
EXECUTIONTYPENew=نوع التشغيل
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=مساحة السلسلة الأصلية
#XFLD: Refresh tooltip
TEXT_REFRESH=تحديث
#XFLD: view Details link
VIEW_ERROR_DETAILS=عرض التفاصيل
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=تنزيل تفاصيل إضافية
#XMSG: Download completed
downloadStarted=تم بدء التنزيل
#XMSG: Error while downloading content
errorInDownload=حدث خطأ أثناء التنزيل.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=عرض التفاصيل
#XBTN: cancel button of task details dialog
TXT_CANCEL=إلغاء
#XBTN: back button from task details
TXT_BACK=عودة
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=مهمة مكتملة
#XFLD: Log message with failed status
MSG_LOG_FAILED=مهمة فاشلة
#XFLD: Master and detail table with no data
No_Data=لا توجد بيانات
#XFLD: Retry tooltip
TEXT_RETRY=إعادة المحاولة
#XFLD: Cancel Run label
TEXT_CancelRun=إلغاء التشغيل
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=مسح التحميل الفاشل
#XMSG:button copy sql statement
txtSQLStatement=نسخ عبارة SQL
#XMSG:button open remote query monitor
txtOpenQueryMonitor=فتح مراقب الاستعلامات البعيدة
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=لعرض عبارات SQL البعيدة، انقر فوق "فتح مراقب الاستعلامات البعيدة".
#XMSG:button ok
txtOk=موافق
#XMSG: button close
txtClose=إغلاق
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=تم البدء في إلغاء إجراء التشغيل للكائن "{0}".
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=فشل إلغاء إجراء التشغيل للكائن ''{0}''.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=لم يعد إجراء تشغيل الإلغاء للكائن "{0}" ممكنًا لأن حالة النسخ المتماثل قد تغيرت.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=لا توجد سجلات مهام بالحالة 'قيد التشغيل'.
#XMSG: message for conflicting task
Task_Already_Running=توجد مهمة متعارضة قيد التشغيل للكائن "{0}" بالفعل.
#XFLD: Label for no task log with running state title
actionInfo=معلومات الإجراء
#XMSG Copied to clipboard
copiedToClip=منسوخ إلى الحافظة
#XFLD copy
Copy=نسخ
#XFLD copy correlation ID
CopyCorrelationID=نسخ معرف الارتباط
#XFLD Close
Close=إغلاق
#XFLD: show more Label
txtShowMore=إظهار المزيد
#XFLD: message Label
messageLabel=الرسالة:
#XFLD: details Label
detailsLabel=التفاصيل:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=جارٍ تنفيذ عبارة SQL \r\n:
#XFLD:statementId Label
statementIdLabel=معرف العبارة:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=عدد عبارات \r\n SQL البعيدة:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=عدد الكشوفات
#XFLD: Space Label
txtSpaces=المساحة
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=مساحات غير معتمدة
#XFLD: Privilege Error Text
txtPrivilegeError=ليس لديك امتيازات كافية لعرض هذه البيانات.
#XFLD: Label for Object Header
DATA_ACCESS=الوصول إلى البيانات
#XFLD: Label for Object Header
SCHEDULE=الجدول الزمني
#XFLD: Label for Object Header
DETAILS=التفاصيل
#XFLD: Label for Object Header
LATEST_UPDATE=آخر تحديث
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=آخر تغيير (مصدر)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=تكرار التحديث
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=التكرار المجدوَل
#XFLD: Label for Object Header
NEXT_RUN=التشغيل التالي
#XFLD: Label for Object Header
CONNECTION=الاتصال
#XFLD: Label for Object Header
DP_AGENT=وكيل توفير البيانات
#XFLD: Label for Object Header
USED_IN_MEMORY=الذاكرة المستخدمة للتخزين (مبيبايت)
#XFLD: Label for Object Header
USED_DISK=القرص المستخدم للتخزين (مبيبايت)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=الحجم في الذاكرة (ميبي بايت)
#XFLD: Label for Object Header
USED_DISK_NEW=الحجم على القرص (ميبي بايت)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=عدد السجلات

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=تعيين إلى فشل
SET_TO_FAILED_ERR=كانت هذه المهمة قيد التشغيل ولكن المستخدم عيّن حالة هذه المهمة على 'فشل
#XFLD: Label for stopped failed
FAILLOCKED=التشغيل قيد التنفيذ بالفعل
#XFLD: sub status STOPPED
STOPPED=موقوف
STOPPED_ERR=تم إيقاف هذه المهمة، لكن لم يتم تنفيذ أي تراجع.
#XFLD: sub status CANCELLED
CANCELLED=ملغى
CANCELLED_ERR=تم إلغاء تشغيل هذه المهمة بعد بدئها. في هذه الحالة، تم التراجع عن البيانات واستعادتها إلى الحالة التي كانت موجودة قبل بدء تشغيل المهمة في البداية.
#XFLD: sub status LOCKED
LOCKED=مؤمَّن
LOCKED_ERR=كانت نفس المهمة قيد التشغيل بالفعل، لذا لا يمكن تشغيل وظيفة المهمة هذه بالتوازي مع تنفيذ مهمة موجودة.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=استثناء المهمة
TASK_EXCEPTION_ERR=واجهت هذه المهمة خطأ غير محدد أثناء التنفيذ.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=استثناء تنفيذ المهمة
TASK_EXECUTE_EXCEPTION_ERR=واجهت هذه المهمة خطأ أثناء التنفيذ.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=غير مفوَّض
UNAUTHORIZED_ERR=تعذر مصادقة المستخدم أو تأمينه أو حذفه.
#XFLD: sub status FORBIDDEN
FORBIDDEN=ممنوع
FORBIDDEN_ERR=لا يمتلك المستخدم المعين الامتيازات اللازمة لتنفيذ هذه المهمة.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=لم يتم بدء تشغيله
FAIL_NOT_TRIGGERED_ERR=تعذر تنفيذ وظيفة المهمة هذه بسبب انقطاع النظام أو عدم توفر جزء من نظام قاعدة البيانات في وقت التنفيذ المخطط. انتظر وقت تنفيذ المهمة المجدول التالي أو أعد جدولة المهمة.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=تم إلغاء الجدول
SCHEDULE_CANCELLED_ERR=تعذر تنفيذ وظيفة المهمة هذه بسبب خطأ داخلي. اتصل بدعم SAP وقم بتزويده بمعرف الارتباط والطابع الزمني من معلومات تفاصيل سجل وظيفة هذه المهمة.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=التشغيل السابق قيد التنفيذ
SUCCESS_SKIPPED_ERR=لم يتم بدء تشغيل تنفيذ هذه المهمة لأن التشغيل السابق لنفس المهمة لا يزال قيد التقدم.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=المالك مفقود
FAIL_OWNER_MISSING_ERR=تعذر تنفيذ وظيفة المهمة هذه لعدم وجود مستخدم نظام معين لها. قم بتعيين مستخدم مالك للمهمة.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=الموافقة غير متوفرة
FAIL_CONSENT_NOT_AVAILABLE_ERR=لم تقم بتفويض SAP لتشغيل سلاسل المهام أو جدولة مهام تكامل البيانات نيابة عنك. حدد الخيار المقدم لمنح موافقتك.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=انتهت صلاحية الموافقة
FAIL_CONSENT_EXPIRED_ERR=انتهت صلاحية التفويض الذي يسمح لـ SAP بتشغيل سلاسل المهام أو جدولة مهام تكامل البيانات نيابةً عنك. حدد الخيار المقدم لتجديد موافقتك.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=تم إبطال الموافقة
FAIL_CONSENT_INVALIDATED_ERR=تعذر تنفيذ هذه المهمة، عادةً بسبب تغيير في تكوين مزوِّد الهوية للوحدة المستضافة. في هذه الحالة، لا يمكن تشغيل وظائف مهام جديدة أو جدولتها باسم المستخدم المتأثر. إذا كان المستخدم المعين لا يزال موجودًا في IdP الجديد، فقم بإلغاء الموافقة على الجدولة ثم امنحها مرة أخرى. إذا لم يعد المستخدم المعين موجودًا، فقم بتعيين مالك وظيفة مهمة جديد وتقديم الموافقة على جدولة المهمة المطلوبة. راجع ملاحظة SAP التالية: https://launchpad.support.sap.com/#/notes/3089828 for more information.
TASK_EXECUTOR_ERROR=منفذ المهمة
TASK_EXECUTOR_ERROR_ERR=واجهت هذه المهمة خطأ داخلي، على الأرجح أثناء خطوات التحضير للتنفيذ، وتعذر بدء المهمة.
PREREQ_NOT_MET=لم يتم استيفاء المتطلب الأساسي
PREREQ_NOT_MET_ERR=تعذر تشغيل هذه المهمة نظرًا لوجود مشكلات في تعريفها. على سبيل المثال، لم يتم نشر الكائن، أو أن سلسلة المهام تحتوي على منطق دائري، أو أن SQL للعرض غير صالحة.
RESOURCE_LIMIT_ERROR=خطأ في حد الموارد
RESOURCE_LIMIT_ERROR_ERR=يتعذر حاليًا تنفيذ المهمة نتيجة عدم توفر موارد كافية أو انشغالها.
FAIL_CONSENT_REFUSED_BY_UMS=تم رفض الموافقة
FAIL_CONSENT_REFUSED_BY_UMS_ERR=تعذر تنفيذ هذه المهمة، في سلاسل المهام أو عمليات التشغيل المجدولة، لحدوث تغيير في تكوين مزود هوية المستخدم في الوحدة المستضافة. لمزيد من المعلومات، راجع ملاحظة SAP التالية: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=مجدوَل
#XFLD: status text
SCHEDULEDNew=دائم
#XFLD: status text
PAUSED=إيقاف مؤقت
#XFLD: status text
DIRECT=مباشر
#XFLD: status text
MANUAL=يدوي
#XFLD: status text
DIRECTNew=بسيط
#XFLD: status text
COMPLETED=مكتمل
#XFLD: status text
FAILED=فشل
#XFLD: status text
RUNNING=قيد التشغيل
#XFLD: status text
none=لا شيء
#XFLD: status text
realtime=الوقت الفعلي
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=مهمة فرعية
#XFLD: New Data available in the file
NEW_DATA=البيانات الجديدة

#XFLD: text for values shown in column Replication Status
txtOff=غلق
#XFLD: text for values shown in column Replication Status
txtInitializing=تهيئة
#XFLD: text for values shown in column Replication Status
txtLoading=تحميل
#XFLD: text for values shown in column Replication Status
txtActive=نشط
#XFLD: text for values shown in column Replication Status
txtAvailable=متوفر
#XFLD: text for values shown in column Replication Status
txtError=خطأ
#XFLD: text for values shown in column Replication Status
txtPaused=متوقف مؤقتًا
#XFLD: text for values shown in column Replication Status
txtDisconnected=غير متصل
#XFLD: text for partially Persisted views
partiallyPersisted=تم التخزين الدائم للبيانات جزئيًا

#XFLD: activity text
REPLICATE=نسخ متماثل
#XFLD: activity text
REMOVE_REPLICATED_DATA=إزالة البيانات المنسوخة نسخًا متماثلًا
#XFLD: activity text
DISABLE_REALTIME=تعطيل النسخ المتماثل للبيانات في الوقت الفعلي
#XFLD: activity text
REMOVE_PERSISTED_DATA=إزالة البيانات المخزنة بصفة دائمة
#XFLD: activity text
PERSIST=تخزين بصفة دائمة
#XFLD: activity text
EXECUTE=تنفيذ
#XFLD: activity text
TASKLOG_CLEANUP=Tacklog_Cleanup
#XFLD: activity text
CANCEL_REPLICATION=إلغاء النسخ المتماثل
#XFLD: activity text
MODEL_IMPORT=استيراد النموذج
#XFLD: activity text
NONE=لا شيء
#XFLD: activity text
CANCEL_PERSISTENCY=إلغاء التخزين الدائم
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=تحليل العرض
#XFLD: activity text
CANCEL_VIEW_ANALYZER=إلغاء محلل العرض

#XFLD: severity text
INFORMATION=معلومات
#XFLD: severity text
SUCCESS=نجاح
#XFLD: severity text
WARNING=تحذير
#XFLD: severity text
ERROR=خطأ
#XFLD: text for values shown for Ascending sort order
SortInAsc=ترتيب تصاعدي
#XFLD: text for values shown for Descending sort order
SortInDesc=ترتيب تنازلي
#XFLD: filter text for task log columns
Filter=عامل التصفية
#XFLD: object text for task log columns
Object=الكائن
#XFLD: space text for task log columns
crossSpace=المساحة

#XBUT: label for remote data access
REMOTE=بعيد
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=منسوخ نسخًا متماثلًا (الوقت الفعلي)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=منسوخ نسخًا متماثلًا (لقطة)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=تم إيقاف النسخ المتماثل في الوقت الفعلي لوجود خطأ. بمجرد تصحيح الخطأ، يمكنك استخدام الإجراء "إعادة المحاولة" لمتابعة النسخ المتماثل في الوقت الفعلي.
ERROR_MSG=تم إيقاف النسخ المتماثل في الوقت الفعلي لوجود خطأ.
RETRY_FAILED_ERROR=فشلت عملية إعادة المحاولة مع وجود خطأ.
LOG_INFO_DETAILS=لا يتم إنشاء أي سجلات عند نسخ البيانات في نمط الوقت الفعلي نسخًا متماثلاً. السجلات المعروضة تتعلق بالإجراءات السابقة.

#XBUT: Partitioning label
partitionMenuText=تقسيم
#XBUT: Drop down menu button to create a partition
createPartitionLabel=إنشاء تقسيم
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=تحرير التقسيم
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=حذف التقسيم
#XFLD: Initial text
InitialPartitionText=حدد التقسيمات بتحديد معايير لتقسيم مجموعات البيانات الكبيرة إلى مجموعات أصغر.
DefinePartition=حدد التقسيمات
#XFLD: Message text
partitionChangedInfo=تم تغيير تعريف القسم منذ النسخ المتماثل الأخير. سيتم تطبيق التغييرات على تحميل البيانات التالي.
#XFLD: Message text
REAL_TIME_WARNING=يتم تطبيق التقسيم فقط عند تحميل لقطة جديدة. لا يتم تطبيقه على النسخ المتماثل في الوقت الفعلي.
#XFLD: Message text
loadSelectedPartitions=بدأ تخزين البيانات بصفة دائمة للأقسام المحددة من "{0}"
#XFLD: Message text
loadSelectedPartitionsError=فشل تخزين البيانات بصفة دائمة للأقسام المحددة من "{0}"
#XFLD: Message text
viewpartitionChangedInfo=تم تغيير تعريف القسم منذ آخر تشغيل تخزين دائم. لتطبيق التغييرات، سيكون تحميل البيانات التالي لقطة كاملة بما في ذلك الأقسام المؤمَّنة. بمجرد الانتهاء من هذا التحميل الكامل، ستتمكن من تشغيل البيانات لأقسام فردية.
#XFLD: Message text
viewpartitionChangedInfoLocked=تم تغيير تعريف التقسيم منذ آخر تشغيل تخزين دائم. لتطبيق التغييرات، سيكون تحميل البيانات التالي لقطة كاملة، باستثناء نطاقات التقسيم المؤمَّنة وغير المغيَّرة. بمجرد الانتهاء من هذا التحميل، ستصبح قادرًا على تحميل التقسيمات المحددة مرة أخرى.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=النسخ المتماثل للجدول
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=النسخ المتماثل للجدول الزمني
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=إنشاء الجدول الزمني
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=تحرير الجدول الزمني
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=حذف الجدول الزمني
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=تحميل لقطة جديدة
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=بدء النسخ المتماثل للبيانات
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=إزالة البيانات المنسوخة نسخًا متماثلًا
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=تمكين الوصول في الوقت الفعلي
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=تمكين النسخ المتماثل لبيانات الوقت الفعلي
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=تعطيل النسخ المتماثل للبيانات في الوقت الفعلي
#XFLD: Message for replicate table action
replicateTableText=النسخ المتماثل للجدول
#XFLD: Message for replicate table action
replicateTableTextNew=النسخ المتماثل للبيانات
#XFLD: Message to schedule task
scheduleText=جدولة
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=التخزين الدائم لطريقة العرض
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=التخزين الدائم للبيانات
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=تحميل لقطة جديدة
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=بدء التخزين الدائم للبيانات
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=إزالة البيانات المخزنة بصفة دائمة
#XFLD: Partitioned Processign
EnablePartitionedProcessing=المعالجة المقسَّمة
#XBUT: Label for scheduled replication
scheduledTxt=مجدوَل
#XBUT: Label for statistics button
statisticsTxt=الإحصائيات
#XBUT: Label for create statistics
createStatsTxt=إنشاء الإحصائيات
#XBUT: Label for edit statistics
editStatsTxt=تحرير الإحصائيات
#XBUT: Label for refresh statistics
refreshStatsTxt=تحديث الإحصائيات
#XBUT: Label for delete statistics
dropStatsTxt=حذف الإحصائيات
#XMSG: Create statistics success message
statsSuccessTxt=بدأ إنشاء إحصائيات من النوع {0} لـ {1}.
#XMSG: Edit statistics success message
statsEditSuccessTxt=بدأ تغيير نوع الإحصائيات إلى {0} لـ {1}.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=بدأ تحديث إحصائيات لـ {0}.
#XMSG: Drop statistics success message
statsDropSuccessTxt=تم حذف الإحصائيات بنجاح لـ {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=حدث خطأ عند إنشاء الإحصائيات
#XMSG: Edit statistics error message
statsEditErrorTxt=حدث خطأ عند تغيير الإحصائيات
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=حدث خطأ عند تحديث الإحصائيات
#XMSG: Drop statistics error message
statsDropErrorTxt=حدث خطأ عند حذف الإحصائيات
#XMG: Warning text for deleting statistics
statsDelWarnTxt=هل تريد بالتأكيد إسقاط إحصائيات البيانات؟
startPersistencyAdvisorLabel=بدء محلل العرض

#Partition related texts
#XFLD: Label for Column
column=العمود
#XFLD: Label for No of Partition
noOfPartitions=عدد التقسيمات
#XFLD: Label for Column
noOfParallelProcess=عدد العمليات المتوازية
#XFLD: Label text
noOfLockedPartition=عدد التقسيمات المؤمَّنة
#XFLD: Label for Partition
PARTITION=التقسيمات
#XFLD: Label for Column
AVAILABLE=متوفر
#XFLD: Statistics Label
statsLabel=الإحصائيات
#XFLD: Label text
COLUMN=العمود:
#XFLD: Label text
PARALLEL_PROCESSES=العمليات المتوازية:
#XFLD: Label text
Partition_Range=نطاق التقسيم
#XFLD: Label text
Name=الاسم
#XFLD: Label text
Locked=مؤمَّن
#XFLD: Label text
Others=أخرى
#XFLD: Label text
Delete=حذف
#XFLD: Label text
LoadData=تحميل التقسيمات المحددة
#XFLD: Label text
LoadSelectedData=تحميل التقسيمات المحددة
#XFLD: Confirmation text
LoadNewPersistenceConfirm=سيؤدي هذا إلى تحميل لقطة جديدة لجميع التقسيمات غير المؤمّنة والمتغيرة، وليس فقط التقسيمات التي حددتها. هل تريد الاستمرار؟
#XFLD: Label text
Continue=متابعة

#XFLD: Label text
PARTITIONS=التقسيمات
#XFLD: Label text
ADD_PARTITIONS=+ إضافة تقسيم
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=إضافة التقسيم
#XFLD: Label text
deleteRange=حذف التقسيم
#XFLD: Label text
LOW_PLACE_HOLDER=إدخال قيمة منخفضة
#XFLD: Label text
HIGH_PLACE_HOLDER=إدخال قيمة مرتفعة
#XFLD: tooltip text
lockedTooltip=تأمين التقسيم بعد التحميل الأولي

#XFLD: Button text
Edit=تحرير
#XFLD: Button text
CANCEL=إلغاء

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=آخر تحديث للإحصائيات
#XFLD: Statistics Fields
STATISTICS=الإحصائيات

#XFLD:Retry label
TEXT_Retry=إعادة المحاولة
#XFLD:Retry label
TEXT_Retry_tooltip=تم حل النسخ المتماثل في الوقت الفعلي بعد ظهور خطأ.
#XFLD: text retry
Retry=تأكيد
#XMG: Retry confirmation text
retryConfirmationTxt=انتهى أخر نسخ متماثل في الوقت الفعلي بأخطاء.\n تأكد من تصحيح الخطأ وأنه يمكن إعادة بدء تشغيل النسخ المتماثل في الوقت الفعلي.
#XMG: Retry success text
retrySuccess=تم بدء إعادة محاولة العملية بنجاح.
#XMG: Retry fail text
retryFail=فشلت إعادة محاولة العملية.
#XMSG: activity message for create statistics
CREATE_STATISTICS=إنشاء الإحصائيات
#XMSG: activity message for edit statistics
DROP_STATISTICS=حذف الإحصائيات
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=تحديث الإحصائيات
#XMSG: activity message for edit statistics
ALTER_STATISTICS=تحرير الإحصائيات
#XMSG: Task log message started task
taskStarted=بدأت مهمة {0}.
#XMSG: Task log message for finished task
taskFinished=انتهت مهمة {0} بالحالة {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=تم إنهاء المهمة {0} في {2} بالحالة {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=المهمة {0} بها معامِلات إدخال.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=انتهت المهمة {0} بخطأ غير متوقع. تم تعيين حالة المهمة إلى {1}.
#XMSG: Task log message for failed task
failedToEnd=فشل تعيين الحالة إلى {0} أو فشلت إزالة التأمين.
#XMSG: Task log message
lockNotFound=لا يمكننا إنهاء العملية نظرًا لأن التأمين مفقود: ربما تم إلغاء المهمة.
#XMSG: Task log message failed task
failedOverwrite=تم تأمين المهمة {0} بالفعل بواسطة {1}. لذلك، فشل مع الخطأ التالي: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=تم تولي تأمين هذه المهمة من خلال مهمة أخرى.
#XMSG: Task log message failed takeover
failedTakeover=فشل تولي مهمة موجودة.
#XMSG: Task log message successful takeover
successTakeover=كان لابد من تحرير التأمين المتبقي. تم تعيين التأمين الجديد لهذه المهمة.
#XMSG: Tasklog Dialog Details
txtDetails=يمكن عرض العبارات البعيدة التي تمت معالجتها أثناء التشغيل بفتح مراقب الاستعلامات البعيدة، في تفاصيل الرسائل الخاصة بالتقسيم.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=تم حذف عبارات SQL البعيدة من قاعدة البيانات ولا يمكن عرضها.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=لا يمكن عرض الاستعلامات البعيدة التي لها اتصالات معيَّنة لمساحات أخرى. انتقل إلى 'مراقب الاستعلامات البعيدة' واستخدم معرف العبارة لتصفيتها.
#XMSG: Task log message for parallel check error
parallelCheckError=لا يمكن معالجة المهمة نظرًا لوجود مهمة أخرى قيد التشغيل وتوقف هذه المهمة بالفعل.
#XMSG: Task log message for parallel running task
parallelTaskRunning=توجد مهمة متعارضة قيد التشغيل بالفعل.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=الحالة {0} أثناء التشغيل بمعرف الارتباط {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=لا يمتلك المستخدم المعين الامتيازات اللازمة لتنفيذ هذه المهمة.

#XBUT: Label for open in Editor
openInEditor=فتح في المحرِّر
#XBUT: Label for open in Editor
openInEditorNew=فتح في أداة إنشاء البيانات
#XFLD:Run deails label
runDetails=تفاصيل التشغيل
#XFLD: Label for Logs
Logs=السجلات
#XFLD: Label for Settings
Settings=الإعدادات
#XFLD: Label for Save button
Save=حفظ
#XFLD: Label for Standard
Standard_PO=محسَّن بالأداء (موصى به)
#XFLD: Label for Hana low memory processing
HLMP_MO=محسَّن بالذاكرة
#XFLD: Label for execution mode
ExecutionMode=نمط التشغيل
#XFLD: Label for job execution
jobExecution=نمط المعالجة
#XFLD: Label for Synchronous
syncExec=متزامن
#XFLD: Label for Asynchronous
asyncExec=غير متزامن
#XFLD: Label for default asynchronous execution
defaultAsyncExec=استخدام افتراضي (غير متزامن، قد يتغير في المستقبل)
#XMSG: Save settings success
saveSettingsSuccess=تم تغيير نمط تنفيذ SAP HANA.
#XMSG: Save settings failure
saveSettingsFailed=فشل تغيير نمط تنفيذ SAP HANA.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=تم تغيير تنفيذ المهمة.
#XMSG: Job Execution change failed
jobExecSettingFailed=فشل تغيير تنفيذ المهمة.
#XMSG: Text for Type
typeTxt=النوع
#XMSG: Text for Monitor
monitorTxt=مراقبة
#XMSG: Text for activity
activityTxt=النشاط
#XMSG: Text for metrics
metricsTxt=القياسات
#XTXT: Text for Task chain key
TASK_CHAINS=سلسلة المهام
#XTXT: Text for View Key
VIEWS=عرض
#XTXT: Text for remote table key
REMOTE_TABLES=الجدول البعيد
#XTXT: Text for Space key
SPACE=المساحة
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=عقدة حوسبة مرنة
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=تدفق النسخ المتماثل
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=البحث الذكي
#XTXT: Text for Local Table
LOCAL_TABLE=الجدول المحلي
#XTXT: Text for Data flow key
DATA_FLOWS=تدفق البيانات
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=إجراء البرنامج النصي لـ SQL
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=سلسلة عمليات BW
#XTXT: Text for API
API=واجهة برمجة التطبيقات
#XTXT: View in Monitor Link text
viewInMonitorTxt=عرض في المراقب
#XTXT: Task List header text
taskListHeader=قائمة المهام ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=لا يمكن استرجاع قياسات عمليات التشغيل القديمة لتدفق بيانات.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=لا يتم تحميل تفاصيل التشغيل الكاملة في الوقت الحالي. حاول التحديث.
#XFLD: Label text for the Metrices table header
metricesColLabel=تسمية المعامل
#XFLD: Label text for the Metrices table header
metricesType=النوع
#XFLD: Label text for the Metrices table header
metricesRecordLabel=عدد السجلات
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=تشغيل سلسلة المهام
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=بدأ تشغيل سلسلة المهام.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=بدأ تشغيل سلسلة المهام لـ {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=فشل تشغيل سلسلة المهام.
#XTXT: Execute button label
runLabel=تشغيل
#XTXT: Execute button label
runLabelNew=بدء التشغيل
#XMSG: Filter Object header
chainsFilteredTableHeader=تمت تصفيته حسب الكائن: {0}
#XFLD: Parent task chain label
parentChainLabel=سلسلة المهام الأصلية:
#XFLD: Parent task chain unauthorized
Unauthorized=غير مفوَّض بالعرض
#XFLD: Parent task chain label
parentTaskLabel=المهمة الأصلية:
#XTXT: Task status
NOT_TRIGGERED=لم يتم بدء تشغيله
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=أإدخال نمط الشاشة الكاملة
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=خروج من نمط ملء الشاشة
#XTXT: Close Task log details right panel
closeRightColumn=إغلاق القسم
#XTXT: Sort Text
sortTxt=ترتيب
#XTXT: Filter Text
filterTxt=تصفية
#XTXT: Filter by text to show list of filters applied
filterByTxt=تصفية حسب
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=أكثر من 5 دقائق
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=أكثر من 15 دقيقة
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=أكثر من ساعة
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=آخر ساعة
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=آخر 24 ساعة
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=الشهر الماضي
#XTXT: Messages title text
messagesText=الرسائل

#XTXT Statistics information message
statisticsInfo=لا يمكن إنشاء الإحصائيات للجداول البعيدة مع الوصول إلى البيانات "منسوخة نسخًا متماثلاً". لإنشاء إحصائيات، قم بإزالة البيانات المنسوخة نسخًا منسوخًا في مراقب تفاصيل الجداول البعيدة.
#XFLD
GO_TO_REMOTETABLE_DETAILS=الانتقال إلى مراقب تفاصيل الجداول البعيدة

#XTXT: Repair latest failed run label
retryRunLabel=إعادة محاولة أحدث تشغيل
#XTXT: Repair failed run label
retryRun=إعادة محاولة التشغيل
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=بدأ تشغيل إعادة محاولة سلسلة المهام
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=فشل تشغيل إعادة محاولة سلسلة المهام
#XMSG: Task chain child elements name
taskChainRetryChildObject=مهمة {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=مهمة جديدة
#XFLD Analyzed View
analyzedView=عرض تم تحليله
#XFLD Metrics
Metrics=القياسات
#XFLD Partition Metrics
PartitionMetrics=قياسات التقسيم
#XFLD Entities
Messages=سجل المهام
#XTXT: Title Message for empty partition data
partitionEmptyTitle=لم يتم تحديد أي أقسام حتى الآن.
#XTXT: Description message for empty partition data
partitionEmptyDescText=إنشاء أقسام من خلال تحديد معايير لتقسيم أحجام البيانات الأكبر إلى أجزاء أصغر وأكثر قابلية للإدارة.

#XTXT: Title Message for empty runs data
runsEmptyTitle=لا تتوفر سجلات حتى الآن
#XTXT: Description message for empty runs data
runsEmptyDescText=عند بدء نشاط جديد (تحميل لقطة جديدة، بدء تشغيل محلل عرض...) سترى السجلات ذات الصلة هنا.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=لم تتم معالجة تكوين عقدة الاحتساب المرنة {0} وفقًا لذلك. يرجى التحقق من تحديدك.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=فشلت عملية إضافة عقدة الحوسبة المرنة {0}.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=بدأ إنشاء النسخة المتماثلة وتنشيطها للكائن المصدر "{0}"."{1}" في عقدة الاحتساب المرنة {2}.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=بدأت إزالة النسخة المتماثلة للكائن المصدر "{0}"."{1}" من عقدة الاحتساب المرنة {2}.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=فشل إنشاء النسخة المتماثلة وتنشيطها للكائن المصدر "{0}"."{1}".
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=فشلت إزالة النسخة المتماثلة للكائن المصدر "{0}"."{1}".
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=بدأ توجيه حوسبة الاستعلامات التحليلية للمساحة {0} إلى عقدة الحوسبة المرنة {1}.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=فشل توجيه حوسبة الاستعلامات التحليلية للمساحة {0} إلى عقدة الحوسبة المرنة المناظرة.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=بدأت إعادة توجيه حوسبة الاستعلامات التحليلية للمساحة {0} من عقدة الحوسبة المرنة {1}.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=فشلت إعادة توجيه حوسبة الاستعلامات التحليلية للمسافة {0} إلى المنسِّق.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=بدأ تشغيل سلسلة المهام {0} إلى عقدة الحوسبة المرنة المناظرة {1}.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=فشل إنشاء سلسلة المهام لعقدة الاحتساب المرنة {0}.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=بدأ توفير عقدة الاحتساب المرنة {0} بخطة احتساب الحجم المحددة: وحدات المعالجة المركزية الافتراضية: {1} والذاكرة (جيبي بايت): {2} وحجم التخزين (جيبي بايت): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=تم توفير عقدة الاحتساب المرنة {0} بالفعل.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=تمت إزالة بيانات عقدة الاحتساب المرنة {0} بالفعل.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=العملية غير مسموح بها. يُرجى إيقاف عقدة الاحتساب المرنة {0} وإعادة تشغيلها لتحديث خطة احتساب الحجم.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=فشلت عملية إزالة عقدة الاحتساب المرنة {0}.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=انتهت مهلة التشغيل الحالي لعقدة الاحتساب المرنة {0}.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=التحقق من حالة عقدة الاحتساب المرنة {0} قيد التنفيذ...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=إنشاء سلسلة المهام لعقدة الاحتساب المرنة {0} مؤمَّن، ومن ثم ربما لا تزال السلسلة {1} قيد التشغيل.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=الكائن المصدر "{0}"."{1}" منسوخ نسخًا متماثلاً كتبعية للعرض "{2}"."{3}".
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=يتعذر النسخ المتماثل للجدول "{0}"."{1}"، لأنه تم إهمال النسخ المتماثل لجدول الصفوف.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=تم تجاوز الحد الأقصى لعُقدة الاحتساب المرنة لكل نسخة SAP HANA Cloud.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=لا تزال عملية التشغيل لعقدة الاحتساب المرنة {0} قيد التنفيذ.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=نظرًا لوجود مشكلات تقنية، تم إيقاف حذف النسخة المتماثلة للجدول المصدر {0}. يرجى المحاولة مرة أخرى لاحقًا.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=نظرًا لوجود مشكلات تقنية، تم إيقاف إنشاء النسخة المتماثلة للجدول المصدر {0}. يرجى المحاولة مرة أخرى لاحقًا.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=لا يمكن بدء عقدة احتساب مرنة نظرًا للوصول إلى حد الاستخدام أو لعدم تخصيص ساعات مجموعة احتساب حتى الآن.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=جارٍ تحميل سلسلة المهام والتحضير لتشغيل إجمالي {0} من المهام التي تشكل جزءًا من هذه السلسلة.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=توجد مهمة متعارضة قيد التشغيل بالفعل
#XMSG: Replication will change
txt_replication_change=سيتم تغيير نوع النسخ المتماثل.
txt_repl_viewdetails=عرض التفاصيل

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=يبدو أنه كان هناك خطأ في إعادة محاولة التشغيل الأخير حيث فشل تشغيل المهمة السابقة قبل أن يتمكن من إنشاء الخطة.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=تم تأمين المساحة "{0}".

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=يتطلب النشاط {0} نشر الجدول المحلي {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=يتطلب النشاط {0} تشغيل التقاط الفرق للجدول المحلي {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=يتطلب النشاط {0} الجدول المحلي {1} لتخزين البيانات في مخزن الكائنات.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=يتطلب النشاط {0} الجدول المحلي {1} لتخزين البيانات في قاعدة البيانات.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=جارٍ بدء إزالة السجلات المحذوفة للجدول المحلي {0}.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=جارٍ بدء حذف جميع سجلات الجدول المحلي {0}.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=جارٍ بدء حذف السجلات للجدول المحلي {0} وفقًا لشرط عامل التصفية {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=حدث خطأ أثناء إزالة السجلات المحذوفة للجدول المحلي {0}.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=حدث خطأ أثناء حذف جميع السجلات للجدول المحلي {0}.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=حذف جميع السجلات المعالَجة بالكامل بنوع التغيير "محذوف" والتي تكون أقدم من {0} من الأيام.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=حذف جميع السجلات المعالَجة بالكامل بنوع التغيير "محذوف" والتي تكون أقدم من {0} من الأيام.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS=تم حذف {0} من السجلات.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS=تم وضع علامة على {0} من السجلات لحذفها.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=اكتملت إزالة السجلات المحذوفة للجدول المحلي {0}.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=اكتمل حذف جميع السجلات للجدول المحلي {0}.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=جارٍ بدء وضع علامة "محذوف" على السجلات للجدول المحلي {0}.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=جارٍ بدء وضع علامة "محذوف" على السجلات للجدول المحلي {0} وفقًا لشرط عامل التصفية {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=اكتمل وضع علامة "محذوف" على السجلات للجدول المحلي {0}.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=يتم تحميل تغييرات البيانات مؤقتًا في الجدول {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=تم تحميل تغييرات البيانات مؤقتًا في الجدول {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=تمت معالجة تغييرات البيانات وحذفها من الجدول {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=تفاصيل الاتصال.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=جارٍ بدء تحسين الجدول المحلي (الملف).
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=حدث خطأ أثناء تحسين الجدول المحلي (الملف).
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=حدث خطأ. تم إيقاف تحسين الجدول المحلي (الملف).
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=جارٍ تحسين الجدول المحلي (الملف)...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=تم تحسين الجدول المحلي (الملف).
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=تم تحسين الجدول المحلي (الملف).
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=تم تحسين الجدول باستخدام أعمدة الترتيب Z: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=حدث خطأ. تم إيقاف قطع الجدول المحلي (الملف).
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=جارٍ قطع الجدول المحلي (الملف)...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=تم إسقاط موقع المخزن المؤقت الوارد للجدول المحلي (الملف).

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=جارٍ بدء التفريغ (حذف جميع السجلات المعالَجة بالكامل) الجدول المحلي (الملف).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=حدث خطأ أثناء تفريغ الجدول المحلي (الملف).
#XMSG: Task log message
LTF_VACUUM_STOPPED=حدث خطأ. تم إيقاف تفريغ الجدول المحلي (الملف).
#XMSG: Task log message
LTF_VACUUM_PROCESSING=جارٍ تفريغ الجدول المحلي (ملف)...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=اكتمل التفريغ.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=حذف جميع السجلات المعالَجة بالكامل الأقدم من {0} من الأيام.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=حذف جميع السجلات المعالَجة بالكامل الأقدم من {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=جارٍ بدء دمج السجلات الجديدة مع الجدول المحلي (ملف).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=بدء دمج السجلات الجديدة مع الجدول المحلي (الملف) باستخدام الإعداد "دمج البيانات تلقائيًا".
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=بدء دمج السجلات الجديدة مع الجدول المحلي (الملف). تم بدء هذه المهمة من خلال طلب دمج عبر واجهة برمجة التطبيقات (API).
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=جارٍ دمج السجلات الجديدة مع الجدول المحلي (ملف).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=حدث خطأ أثناء دمج السجلات الجديدة بالجدول المحلي (ملف).
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=تم دمج الجدول المحلي (الملف).
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=حدث خطأ. تم إيقاف دمج الجدول المحلي (الملف).
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=فشل دمج الجدول المحلي (الملف) بسبب وجود خطأ، ولكن العملية كانت ناجحة جزئيًا، وتم دمج بعض البيانات بالفعل.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=حدث خطأ في انتهاء المهلة. النشاط {0} قيد التشغيل لمدة {1} من الساعات.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=تعذر بدء التنفيذ غير المتزامن بسبب ارتفاع حِمل النظام. افتح "مراقبة النظام" وتحقق من المهام قيد التشغيل.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=تم إلغاء التنفيذ غير المتزامن.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=تم تشغيل مهمة {0} ضمن حدود الذاكرة {1} و{2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=تم تشغيل المهمة {0} بمعرف المورد {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=بدأ البحث والاستبدال في الجدول المحلي (ملف).
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=اكتمل البحث والاستبدال في الجدول المحلي (ملف).
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=فشل البحث والاستبدال في الجدول المحلي (الملف).

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=حدث خطأ. تم إيقاف تحديث إحصائيات الجدول المحلي (الملف).

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=فشلت المهمة بسبب خطأ نفاد الذاكرة في قاعدة بيانات SAP HANA.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=فشلت المهمة بسبب رفض التحكم بالدخول في SAP HANA.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=فشلت المهمة بسبب وجود اتصالات SAP HANA نشطة كثيرة جدًا.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=تعذر تنفيذ عملية إعادة المحاولة لأن إعادة المحاولات مسموح بها في أصل سلسلة المهام المتداخلة فقط.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=سجلات المهام الفرعية الفاشلة لم تعد متوفرة لإعادة المحاولة للمتابعة.


####Metrics Labels

performanceOptimized=محسَّن بالأداء
memoryOptimized=محسَّن بالذاكرة

JOB_EXECUTION=تنفيذ المهمة
EXECUTION_MODE=نمط التشغيل
NUMBER_OF_RECORDS_OVERALL=عدد السجلات المخزنة بصفة دائمة
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=عدد السجلات المقروءة من المصدر البعيد
RUNTIME_MS_REMOTE_EXECUTION_TIME=وقت معالجة المصدر البعيد
MEMORY_CONSUMPTION_GIB=أقصى ذاكرة لـ SAP HANA
NUMBER_OF_PARTITIONS=عدد التقسيمات
MEMORY_CONSUMPTION_GIB_OVERALL=أقصى ذاكرة لـ SAP HANA
NUMBER_OF_PARTITIONS_LOCKED=عدد التقسيمات المؤمَّنة
PARTITIONING_COLUMN=عمود التقسيم
HANA_PEAK_CPU_TIME=إجمالي وقت وحدة المعالجة المركزية لـ SAP HANA
USED_IN_DISK=التخزين المستخدم
INPUT_PARAMETER_PARAMETER_VALUE=معامل الإدخال
INPUT_PARAMETER=معامل الإدخال
ECN_ID=اسم عقدة الحوسبة المرنة

DAC=عناصر التحكم في الوصول إلى البيانات
YES=نعم
NO=لا
noofrecords=عدد السجلات
partitionpeakmemory=أقصى ذاكرة لـ SAP HANA
value=القيمة
metricsTitle=القياسات ({0})
partitionmetricsTitle=التقسيمات ({0})
partitionLabel=التقسيم
OthersNotNull=لم يتم تضمين القيم في النطاقات
OthersNull=قيم خالية
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=الإعدادات المستخدمة لآخر تشغيل للتخزين الدائم للبيانات:
#XMSG: Message for input parameter name
inputParameterLabel=معامل الإدخال
#XMSG: Message for input parameter value
inputParameterValueLabel=القيمة
#XMSG: Message for persisted data
inputParameterPersistedLabel=تم التخزين الدائم للبيانات في
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=حذف البيانات
REMOVE_DELETED_RECORDS=إزالة السجلات المحذوفة
MERGE_FILES=دمج الملفات
OPTIMIZE_FILES=تحسين الملفات
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=عرض في مراقبة وحدة توصيل SAP BW

ANALYZE_PERFORMANCE=تحليل الأداء
CANCEL_ANALYZE_PERFORMANCE=إلغاء تحليل الأداء

#XFLD: Label for frequency column
everyLabel=كل
#XFLD: Plural Recurrence text for Hour
hoursLabel=ساعات
#XFLD: Plural Recurrence text for Day
daysLabel=أيام
#XFLD: Plural Recurrence text for Month
monthsLabel=شهور
#XFLD: Plural Recurrence text for Minutes
minutesLabel=دقائق

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">عرض دليل استكشاف أخطاء التخزين الدائم للبيانات وإصلاحها</a>
#XTXT TEXT for view persistency guide link
OOMMessage=نفدت ذاكرة العملية. لا يمكن تخزين البيانات بشكل دائم للعرض "{0}". قم باستشارة SAP Help Portal لمزيد من المعلومات حول أخطاء نفاد الذاكرة. قم بمراعاة التحقق من محلل العرض لتحليل العرض لمعرفة مدى تعقيد استهلاك الذاكرة.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=غير قابل للتطبيق
OPEN_BRACKET=(
CLOSE_BRACKET=)
