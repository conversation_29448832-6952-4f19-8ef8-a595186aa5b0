
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=รายละเอียดล็อก
#XFLD: Header
TASK_LOGS=ล็อกงาน ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=การดำเนินการ ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=ดูรายละเอียด
#XFLD: Button text
STOP=หยุดการดำเนินการ
#XFLD: Label text
RUN_START=เริ่มต้นการดำเนินการล่าสุด
#XFLD: Label text
RUN_END=สิ้นสุดการดำเนินการล่าสุด
#XFLD: Label text
RUNTIME=ระยะเวลา
#XTIT: Count for Messages
txtDetailMessages=ข้อความ ({0})
#XFLD: Label text
TIME=การบันทึกเวลา
#XFLD: Label text
MESSAGE=ข้อความ
#XFLD: Label text
TASK_STATUS=หมวด
#XFLD: Label text
TASK_ACTIVITY=กิจกรรม
#XFLD: Label text
RUN_START_DETAILS=เริ่มต้น
#XFLD: Label text
RUN_END_DETAILS=สิ้นสุด
#XFLD: Label text
LOGS=การดำเนินการ
#XFLD: Label text
STATUS=สถานะ
#XFLD: Label text
RUN_STATUS=สถานะการดำเนินการ
#XFLD: Label text
Runtime=ระยะเวลา
#XFLD: Label text
RuntimeTooltip=ระยะเวลา (hh:mm:ss)
#XFLD: Label text
TRIGGEREDBY=ทริกเกอร์โดย
#XFLD: Label text
TRIGGEREDBYNew=ดำเนินการโดย
#XFLD: Label text
TRIGGEREDBYNewImp=เริ่มต้นการดำเนินการโดย
#XFLD: Label text
EXECUTIONTYPE=ประเภทการดำเนินการ
#XFLD: Label text
EXECUTIONTYPENew=ประเภทการดำเนินการ
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=พื้นที่เชนหลัก
#XFLD: Refresh tooltip
TEXT_REFRESH=รีเฟรช
#XFLD: view Details link
VIEW_ERROR_DETAILS=ดูรายละเอียด
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=ดาวน์โหลดรายละเอียดเพิ่มเติม
#XMSG: Download completed
downloadStarted=เริ่มต้นการดาวน์โหลดแล้ว
#XMSG: Error while downloading content
errorInDownload=มีข้อผิดพลาดเกิดขึ้นขณะดาวน์โหลด
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=ดูรายละเอียด
#XBTN: cancel button of task details dialog
TXT_CANCEL=ยกเลิก
#XBTN: back button from task details
TXT_BACK=ย้อนกลับ
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=งานเสร็จสมบูรณ์
#XFLD: Log message with failed status
MSG_LOG_FAILED=งานล้มเหลว
#XFLD: Master and detail table with no data
No_Data=ไม่มีข้อมูล
#XFLD: Retry tooltip
TEXT_RETRY=ลองใหม่
#XFLD: Cancel Run label
TEXT_CancelRun=ยกเลิกการดำเนินการ
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=ล้างข้อมูลการโหลดที่ล้มเหลว
#XMSG:button copy sql statement
txtSQLStatement=คัดลอกคำสั่ง SQL
#XMSG:button open remote query monitor
txtOpenQueryMonitor=เปิดตัวติดตามตรวจสอบคิวรีระยะไกล
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=เมื่อต้องการแสดงคำสั่ง SQL ระยะไกล ให้คลิกที่ "เปิดตัวติดตามตรวจสอบคิวรีระยะไกล"
#XMSG:button ok
txtOk=ตกลง
#XMSG: button close
txtClose=ปิด
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=การยกเลิกการดำเนินการสำหรับออบเจค "{0}" เริ่มต้นแล้ว
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=การยกเลิกการดำเนินการสำหรับออบเจค "{0}" ล้มเหลว
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=การยกเลิกการดำเนินการสำหรับออบเจค "{0}" ไม่สามารถทำได้อีกต่อไปเนื่องจากสถานะการทำสำเนามีการเปลี่ยนแปลง
#XMSG: Info message for no Running state logId
noTaskWithRunningState=ไม่มีล็อกของงานที่มีสถานะ 'กำลังดำเนินการ'
#XMSG: message for conflicting task
Task_Already_Running=งานที่ขัดแย้งกันกำลังดำเนินการอยู่สำหรับออบเจค "{0}"
#XFLD: Label for no task log with running state title
actionInfo=ข้อมูลการดำเนินการ
#XMSG Copied to clipboard
copiedToClip=คัดลอกไปยังคลิปบอร์ดแล้ว
#XFLD copy
Copy=คัดลอก
#XFLD copy correlation ID
CopyCorrelationID=คัดลอก ID ความสัมพันธ์
#XFLD Close
Close=ปิด
#XFLD: show more Label
txtShowMore=แสดงเพิ่มเติม
#XFLD: message Label
messageLabel=ข้อความ:
#XFLD: details Label
detailsLabel=รายละเอียด:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=การดำเนินการคำสั่ง SQL \r\n:
#XFLD:statementId Label
statementIdLabel=ID คำสั่ง:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=จำนวนคำสั่ง SQL \r\n ระยะไกล:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=จำนวนคำสั่ง
#XFLD: Space Label
txtSpaces=พื้นที่
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=พื้นที่ที่ไม่มีสิทธิ
#XFLD: Privilege Error Text
txtPrivilegeError=คุณไม่มีสิทธิพิเศษเพียงพอในการดูข้อมูลนี้
#XFLD: Label for Object Header
DATA_ACCESS=การเข้าถึงข้อมูล
#XFLD: Label for Object Header
SCHEDULE=กำหนดการ
#XFLD: Label for Object Header
DETAILS=รายละเอียด
#XFLD: Label for Object Header
LATEST_UPDATE=การอัพเดทล่าสุด
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=การเปลี่ยนแปลงล่าสุด (แหล่งข้อมูล)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=ความถี่ในการรีเฟรช
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=ความถี่ตามกำหนดการ
#XFLD: Label for Object Header
NEXT_RUN=การดำเนินการครั้งถัดไป
#XFLD: Label for Object Header
CONNECTION=การเชื่อมต่อ
#XFLD: Label for Object Header
DP_AGENT=เอเจนต์ DP
#XFLD: Label for Object Header
USED_IN_MEMORY=หน่วยความจำที่ใช้สำหรับพื้นที่จัดเก็บ (MiB)
#XFLD: Label for Object Header
USED_DISK=ดิสก์ที่ใช้สำหรับพื้นที่จัดเก็บ (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=ขนาดในหน่วยความจำ (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=ขนาดในดิสก์ (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=จำนวนเรคคอร์ด

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=กำหนดเป็น 'ล้มเหลว'
SET_TO_FAILED_ERR=งานนี้กำลังดำเนินการอยู่แต่ผู้ใช้กำหนดสถานะของงานนี้เป็น 'ล้มเหลว'
#XFLD: Label for stopped failed
FAILLOCKED=อยู่ระหว่างดำเนินการแล้ว
#XFLD: sub status STOPPED
STOPPED=ถูกหยุด
STOPPED_ERR=งานนี้หยุดแล้ว แต่ไม่มีการดำเนินการย้อนกลับ
#XFLD: sub status CANCELLED
CANCELLED=ยกเลิกแล้ว
CANCELLED_ERR=การดำเนินการงานนี้ถูกยกเลิกแล้วหลังจากเริ่มต้น ในกรณีนี้ ข้อมูลถูกย้อนกลับและคืนค่าเป็นสถานะที่มีอยู่ก่อนที่การดำเนินการงานถูกทริกเกอร์ในตอนเริ่มต้น
#XFLD: sub status LOCKED
LOCKED=ถูกล็อค
LOCKED_ERR=งานเดียวกันกำลังดำเนินการอยู่แล้ว ดังนั้นไม่สามารถดำเนินการงานนี้แบบขนานกับการดำเนินการของงานที่มีอยู่
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=ข้อยกเว้นของงาน
TASK_EXCEPTION_ERR=งานนี้พบข้อผิดพลาดที่ไม่ได้ระบุระหว่างการดำเนินการ
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=ข้อยกเว้นในการดำเนินการงาน
TASK_EXECUTE_EXCEPTION_ERR=งานนี้พบข้อผิดพลาดระหว่างการดำเนินการ
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=ไม่มีสิทธิ
UNAUTHORIZED_ERR=ไม่สามารถรับรองความถูกต้องของผู้ใช้ได้ ผู้ใช้ถูกล็อคหรือลบ
#XFLD: sub status FORBIDDEN
FORBIDDEN=ไม่อนุญาต
FORBIDDEN_ERR=ผู้ใช้ที่กำหนดไม่มีสิทธิพิเศษที่จำเป็นในการดำเนินการงานนี้
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=ยังไม่ได้ทริกเกอร์
FAIL_NOT_TRIGGERED_ERR=ไม่สามารถการดำเนินการงานนี้ได้เนื่องจากระบบขัดข้องหรือบางส่วนของระบบฐานข้อมูลไม่พร้อมใช้งานเมื่อมีการดำเนินการตามแผน กรุณารอเวลาในการดำเนินการงานตามกำหนดการครั้งถัดไปหรือจัดกำหนดการงานใหม่
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=กำหนดการถูกยกเลิก
SCHEDULE_CANCELLED_ERR=ไม่สามารถดำเนินการงานนี้ได้เนื่องจากเกิดข้อผิดพลาดภายใน กรุณาติดต่อ SAP Support และให้ ID ความสัมพันธ์และการบันทึกเวลาจากข้อมูลรายละเอียดล็อกของงานนี้
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=การดำเนินการก่อนหน้าอยู่ระหว่างดำเนินการ
SUCCESS_SKIPPED_ERR=ไม่ได้ทริกเกอร์การดำเนินการของงานนี้เนื่องจากการดำเนินการก่อนหน้านี้ของงานเดียวกันยังอยู่ระหว่างดำเนินการ
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=เจ้าของขาดหายไป
FAIL_OWNER_MISSING_ERR=ไม่สามารถดำเนินการงานนี้ได้เนื่องจากไม่มีผู้ใช้ระบบที่กำหนด กรุณากำหนดผู้ใช้ที่เป็นเจ้าของให้กับงาน
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=ไม่มีความยินยอม
FAIL_CONSENT_NOT_AVAILABLE_ERR=คุณไม่ได้ให้สิทธิ SAP ในการดำเนินการเชนงานหรือจัดกำหนดการงานการรวมข้อมูลในนามของคุณ กรุณาเลือกตัวเลือกที่มีให้เพื่อให้ความยินยอมของคุณ
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=ความยินยอมหมดอายุ
FAIL_CONSENT_EXPIRED_ERR=สิทธิที่อนุญาตให้ SAP ดำเนินการเชนงานหรือจัดกำหนดการงานการรวมข้อมูลในนามของคุณหมดอายุแล้ว กรุณาเลือกตัวเลือกที่มีให้เพื่อต่ออายุความยินยอมของคุณ
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=ความยินยอมถูกทำให้เป็นโมฆะ
FAIL_CONSENT_INVALIDATED_ERR=ไม่สามารถดำเนินการงานนี้ได้ ซึ่งมักเกิดจากการเปลี่ยนแปลงในการกำหนดรูปแบบผู้ให้บริการข้อมูลประจำตัวของ Tenant ในกรณีดังกล่าวจะไม่สามารถดำเนินการหรือจัดกำหนดการงานใหม่ในนามของผู้ใช้ที่ได้รับผลกระทบได้ ถ้าผู้ใช้ที่กำหนดยังมีอยู่ใน IdP ใหม่ ให้เพิกถอนความยินยอมในการจัดกำหนดการ แล้วอนุญาตอีกครั้ง ถ้าผู้ใช้ที่กำหนดไม่มีอยู่อีกต่อไป ให้กำหนดเจ้าของงานใหม่และให้ความยินยอมในการจัดกำหนดการงานที่ต้องการ สำหรับข้อมูลเพิ่มเติม กรุณาดู SAP Note ต่อไปนี้ https://launchpad.support.sap.com/#/notes/3089828
TASK_EXECUTOR_ERROR=ผู้ดำเนินการงาน
TASK_EXECUTOR_ERROR_ERR=งานนี้เกิดข้อผิดพลาดภายใน ซึ่งอาจเกิดขึ้นระหว่างขั้นตอนการจัดเตรียมสำหรับการดำเนินการ และไม่สามารถเริ่มต้นงานได้
PREREQ_NOT_MET=ไม่ตรงตามข้อกำหนดเบื้องต้น
PREREQ_NOT_MET_ERR=ไม่สามารถดำเนินการงานนี้ได้เนื่องจากมีปัญหาในข้อกำหนด ตัวอย่างเช่น ออบเจคไม่ได้ถูกปรับใช้ เชนงานประกอบด้วยลอจิกแบบวงกลม หรือ SQL ของมุมมองไม่ถูกต้อง
RESOURCE_LIMIT_ERROR=ข้อผิดพลาดเกี่ยวกับขีดจำกัดทรัพยากร
RESOURCE_LIMIT_ERROR_ERR=ไม่สามารถดำเนินการงานได้ในขณะนี้เนื่องจากทรัพยากรไม่พร้อมใช้งานหรือไม่ว่าง
FAIL_CONSENT_REFUSED_BY_UMS=ความยินยอมถูกปฏิเสธ
FAIL_CONSENT_REFUSED_BY_UMS_ERR=ไม่สามารถดำเนินการงานนี้ (ในการดำเนินการตามกำหนดการหรือเชนงาน) เนื่องจากมีการเปลี่ยนแปลงการกำหนดรูปแบบผู้ให้บริการข้อมูลประจำตัวของผู้ใช้ใน Tenant สำหรับข้อมูลเพิ่มเติม กรุณาดู SAP Note ต่อไปนี้: https://launchpad.support.sap.com/#/notes/3120806
#XFLD: status text
SCHEDULED=จัดกำหนดการแล้ว
#XFLD: status text
SCHEDULEDNew=ถาวร
#XFLD: status text
PAUSED=ถูกหยุดชั่วคราว
#XFLD: status text
DIRECT=โดยตรง
#XFLD: status text
MANUAL=ด้วยตนเอง
#XFLD: status text
DIRECTNew=แบบง่าย
#XFLD: status text
COMPLETED=เสร็จสมบูรณ์
#XFLD: status text
FAILED=ล้มเหลว
#XFLD: status text
RUNNING=กำลังดำเนินการ
#XFLD: status text
none=ไม่มี
#XFLD: status text
realtime=เวลาจริง
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=งานย่อย
#XFLD: New Data available in the file
NEW_DATA=ข้อมูลใหม่

#XFLD: text for values shown in column Replication Status
txtOff=ปิด
#XFLD: text for values shown in column Replication Status
txtInitializing=กำลังกำหนดค่าเริ่มต้น
#XFLD: text for values shown in column Replication Status
txtLoading=กำลังโหลด
#XFLD: text for values shown in column Replication Status
txtActive=ใช้งานได้
#XFLD: text for values shown in column Replication Status
txtAvailable=พร้อมใช้งาน
#XFLD: text for values shown in column Replication Status
txtError=ข้อผิดพลาด
#XFLD: text for values shown in column Replication Status
txtPaused=ถูกหยุดชั่วคราว
#XFLD: text for values shown in column Replication Status
txtDisconnected=ยกเลิกการเชื่อมต่อแล้ว
#XFLD: text for partially Persisted views
partiallyPersisted=คงไว้บางส่วน

#XFLD: activity text
REPLICATE=ทำสำเนา
#XFLD: activity text
REMOVE_REPLICATED_DATA=ย้ายข้อมูลที่ทำสำเนาออก
#XFLD: activity text
DISABLE_REALTIME=ปิดใช้งานการทำสำเนาข้อมูลตามเวลาจริง
#XFLD: activity text
REMOVE_PERSISTED_DATA=ย้ายข้อมูลที่คงไว้ออก
#XFLD: activity text
PERSIST=คงไว้
#XFLD: activity text
EXECUTE=ดำเนินการ
#XFLD: activity text
TASKLOG_CLEANUP=Tacklog_Cleanup
#XFLD: activity text
CANCEL_REPLICATION=ยกเลิกการทำสำเนา
#XFLD: activity text
MODEL_IMPORT=การอิมปอร์ตโมเดล
#XFLD: activity text
NONE=ไม่มี
#XFLD: activity text
CANCEL_PERSISTENCY=ยกเลิกการคงอยู่
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=วิเคราะห์มุมมอง
#XFLD: activity text
CANCEL_VIEW_ANALYZER=ยกเลิก View Analyzer

#XFLD: severity text
INFORMATION=ข้อมูล
#XFLD: severity text
SUCCESS=สำเร็จ
#XFLD: severity text
WARNING=คำเตือน
#XFLD: severity text
ERROR=ข้อผิดพลาด
#XFLD: text for values shown for Ascending sort order
SortInAsc=จัดเรียงจากน้อยไปหามาก
#XFLD: text for values shown for Descending sort order
SortInDesc=จัดเรียงจากมากไปหาน้อย
#XFLD: filter text for task log columns
Filter=ฟิลเตอร์
#XFLD: object text for task log columns
Object=ออบเจค
#XFLD: space text for task log columns
crossSpace=พื้นที่

#XBUT: label for remote data access
REMOTE=ระยะไกล
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=ทำสำเนาแล้ว (ตามเวลาจริง)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=ทำสำเนาแล้ว (Snapshot)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=การทำสำเนาตามเวลาจริงถูกระงับเนื่องจากเกิดข้อผิดพลาด เมื่อข้อผิดพลาดได้รับการแก้ไข คุณจะสามารถใช้การดำเนินการ "ลองใหม่" เพื่อดำเนินการทำสำเนาตามเวลาจริงต่อได้
ERROR_MSG=การทำสำเนาตามเวลาจริงถูกระงับเนื่องจากเกิดข้อผิดพลาด
RETRY_FAILED_ERROR=การลองดำเนินการกระบวนการใหม่ล้มเหลวโดยมีข้อผิดพลาด
LOG_INFO_DETAILS=จะไม่มีการสร้างล็อกเมื่อทำสำเนาข้อมูลในโหมดตามเวลาจริง ล็อกที่แสดงจะสัมพันธ์กับการดำเนินการก่อนหน้า

#XBUT: Partitioning label
partitionMenuText=การพาร์ทิชัน
#XBUT: Drop down menu button to create a partition
createPartitionLabel=สร้างพาร์ทิชัน
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=แก้ไขพาร์ทิชัน
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=ลบพาร์ทิชัน
#XFLD: Initial text
InitialPartitionText=กำหนดพาร์ทิชันโดยระบุเกณฑ์เพื่อแบ่งชุดข้อมูลขนาดใหญ่ออกเป็นชุดข้อมูลขนาดเล็ก
DefinePartition=กำหนดพาร์ทิชัน
#XFLD: Message text
partitionChangedInfo=ข้อกำหนดพาร์ทิชันมีการเปลี่ยนแปลงตั้งแต่การทำสำเนาครั้งล่าสุด การเปลี่ยนแปลงจะถูกนำไปใช้กับการโหลดข้อมูลครั้งถัดไป
#XFLD: Message text
REAL_TIME_WARNING=การพาร์ทิชันจะถูกนำไปใช้เฉพาะเมื่อโหลด Snapshot ใหม่ แต่จะไม่ถูกนำไปใช้สำหรับการทำสำเนาตามเวลาจริง
#XFLD: Message text
loadSelectedPartitions=เริ่มต้นข้อมูลการคงอยู่สำหรับพาร์ทิชันที่เลือกของ "{0}" แล้ว
#XFLD: Message text
loadSelectedPartitionsError=ไม่สามารถคงข้อมูลไว้สำหรับพาร์ทิชันที่เลือกของ "{0}"
#XFLD: Message text
viewpartitionChangedInfo=ข้อกำหนดพาร์ทิชันมีการเปลี่ยนแปลงตั้งแต่การดำเนินการคงอยู่ล่าสุด เมื่อต้องการนำการเปลี่ยนแปลงไปใช้ การโหลดข้อมูลถัดไปจะเป็น Snapshot ทั้งหมดรวมถึงพาร์ทิชันที่ล็อค เมื่อเสร็จสิ้นการโหลดทั้งหมดนี้แล้ว คุณจะสามารถดำเนินการข้อมูลสำหรับพาร์ทิชันเดียวได้
#XFLD: Message text
viewpartitionChangedInfoLocked=ข้อกำหนดพาร์ทิชันมีการเปลี่ยนแปลงตั้งแต่การดำเนินการการคงอยู่ล่าสุด เมื่อต้องการนำการเปลี่ยนแปลงไปใช้ การโหลดข้อมูลถัดไปจะเป็น Snapshot ทั้งหมด ยกเว้นช่วงของพาร์ทิชันที่ถูกล็อคและไม่มีการเปลี่ยนแปลง เมื่อเสร็จสิ้นการโหลดนี้แล้ว คุณจะสามารถโหลดพาร์ทิชันที่เลือกได้อีกครั้ง
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=การทำสำเนาตาราง
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=การทำสำเนากำหนดการ
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=สร้างกำหนดการ
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=แก้ไขกำหนดการ
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=ลบกำหนดการ
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=โหลด Snapshot ใหม่
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=เริ่มต้นการทำสำเนาข้อมูล
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=ย้ายข้อมูลที่ทำสำเนาออก
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=เปิดใช้งานการเข้าถึงตามเวลาจริง
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=เปิดใช้งานการทำสำเนาข้อมูลตามเวลาจริง
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=ปิดใช้งานการทำสำเนาข้อมูลตามเวลาจริง
#XFLD: Message for replicate table action
replicateTableText=การทำสำเนาตาราง
#XFLD: Message for replicate table action
replicateTableTextNew=การทำสำเนาข้อมูล
#XFLD: Message to schedule task
scheduleText=กำหนดการ
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=การคงอยู่ของมุมมอง
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=การคงอยู่ของข้อมูล
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=โหลด Snapshot ใหม่
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=เริ่มต้นการคงอยู่ของข้อมูล
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=ย้ายข้อมูลที่คงไว้ออก
#XFLD: Partitioned Processign
EnablePartitionedProcessing=การประมวลผลที่มีการทำพาร์ทิชัน
#XBUT: Label for scheduled replication
scheduledTxt=จัดกำหนดการแล้ว
#XBUT: Label for statistics button
statisticsTxt=สถิติ
#XBUT: Label for create statistics
createStatsTxt=สร้างสถิติ
#XBUT: Label for edit statistics
editStatsTxt=แก้ไขสถิติ
#XBUT: Label for refresh statistics
refreshStatsTxt=รีเฟรชสถิติ
#XBUT: Label for delete statistics
dropStatsTxt=ลบสถิติ
#XMSG: Create statistics success message
statsSuccessTxt=เริ่มต้นการสร้างสถิติประเภท {0} สำหรับ {1} แล้ว
#XMSG: Edit statistics success message
statsEditSuccessTxt=เริ่มต้นการเปลี่ยนแปลงประเภทสถิติเป็น {0} สำหรับ {1} แล้ว
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=เริ่มต้นการรีเฟรชสถิติสำหรับ {0} แล้ว
#XMSG: Drop statistics success message
statsDropSuccessTxt=ลบสถิติได้สำเร็จสำหรับ {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=เกิดข้อผิดพลาดเมื่อสร้างสถิติ
#XMSG: Edit statistics error message
statsEditErrorTxt=เกิดข้อผิดพลาดเมื่อเปลี่ยนแปลงสถิติ
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=เกิดข้อผิดพลาดเมื่อรีเฟรชสถิติ
#XMSG: Drop statistics error message
statsDropErrorTxt=เกิดข้อผิดพลาดเมื่อลบสถิติ
#XMG: Warning text for deleting statistics
statsDelWarnTxt=คุณแน่ใจหรือไม่ว่าต้องการลบสถิติข้อมูล?
startPersistencyAdvisorLabel=เริ่มต้น View Analyzer

#Partition related texts
#XFLD: Label for Column
column=คอลัมน์
#XFLD: Label for No of Partition
noOfPartitions=จำนวนพาร์ทิชัน
#XFLD: Label for Column
noOfParallelProcess=จำนวนกระบวนการแบบขนาน
#XFLD: Label text
noOfLockedPartition=จำนวนพาร์ทิชันที่ถูกล็อค
#XFLD: Label for Partition
PARTITION=พาร์ทิชัน
#XFLD: Label for Column
AVAILABLE=พร้อมใช้งาน
#XFLD: Statistics Label
statsLabel=สถิติ
#XFLD: Label text
COLUMN=คอลัมน์:
#XFLD: Label text
PARALLEL_PROCESSES=กระบวนการแบบขนาน:
#XFLD: Label text
Partition_Range=ช่วงของพาร์ทิชัน
#XFLD: Label text
Name=ชื่อ
#XFLD: Label text
Locked=ถูกล็อค
#XFLD: Label text
Others=อื่นๆ
#XFLD: Label text
Delete=ลบ
#XFLD: Label text
LoadData=โหลดพาร์ทิชันที่เลือก
#XFLD: Label text
LoadSelectedData=โหลดพาร์ทิชันที่เลือก
#XFLD: Confirmation text
LoadNewPersistenceConfirm=การดำเนินการนี้จะโหลด Snapshot ใหม่สำหรับพาร์ทิชันทั้งหมดที่ถูกปลดล็อคและมีการเปลี่ยนแปลง ไม่ใช่แค่เพียงพาร์ทิชันที่คุณเลือกเท่านั้น คุณต้องการดำเนินการต่อหรือไม่?
#XFLD: Label text
Continue=ดำเนินการต่อ

#XFLD: Label text
PARTITIONS=พาร์ทิชัน
#XFLD: Label text
ADD_PARTITIONS=+ เพิ่มพาร์ทิชัน
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=เพิ่มพาร์ทิชัน
#XFLD: Label text
deleteRange=ลบพาร์ทิชัน
#XFLD: Label text
LOW_PLACE_HOLDER=ป้อนค่าต่ำ
#XFLD: Label text
HIGH_PLACE_HOLDER=ป้อนค่าสูง
#XFLD: tooltip text
lockedTooltip=ล็อคพาร์ทิชันหลังจากการโหลดเริ่มต้น

#XFLD: Button text
Edit=แก้ไข
#XFLD: Button text
CANCEL=ยกเลิก

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=การอัพเดทสถิติครั้งล่าสุด
#XFLD: Statistics Fields
STATISTICS=สถิติ

#XFLD:Retry label
TEXT_Retry=ลองใหม่
#XFLD:Retry label
TEXT_Retry_tooltip=ลองดำเนินการทำสำเนาตามเวลาจริงใหม่หลังจากแก้ไขข้อผิดพลาดแล้ว
#XFLD: text retry
Retry=ยืนยัน
#XMG: Retry confirmation text
retryConfirmationTxt=การทำสำเนาตามเวลาจริงสิ้นสุดลงโดยมีข้อผิดพลาด\n กรุณายืนยันว่าข้อผิดพลาดได้รับการแก้ไขแล้วและสามารถเริ่มการทำสำเนาตามเวลาจริงใหม่ได้
#XMG: Retry success text
retrySuccess=เริ่มการลองดำเนินการกระบวนการใหม่ได้สำเร็จ
#XMG: Retry fail text
retryFail=การลองดำเนินการกระบวนการใหม่ล้มเหลว
#XMSG: activity message for create statistics
CREATE_STATISTICS=สร้างสถิติ
#XMSG: activity message for edit statistics
DROP_STATISTICS=ลบสถิติ
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=รีเฟรชสถิติ
#XMSG: activity message for edit statistics
ALTER_STATISTICS=แก้ไขสถิติ
#XMSG: Task log message started task
taskStarted=งาน {0} เริ่มต้นแล้ว
#XMSG: Task log message for finished task
taskFinished=งาน {0} สิ้นสุดโดยมีสถานะ {1}
#XMSG: Task log message for finished task with end time
taskFinishedAt=งาน {0} สิ้นสุดเมื่อ {2} โดยมีสถานะ ''{1}''
#XMSG: Task {0} has input parameters
taskHasInputParameters=งาน {0} มีพารามิเตอร์ป้อนข้อมูล
#XMSG: Task log message for unexpected error
unexpectedExecutionError=งาน {0} สิ้นสุดโดยมีข้อผิดพลาดที่ไม่คาดคิด สถานะของงานถูกกำหนดเป็น {1}
#XMSG: Task log message for failed task
failedToEnd=ไม่สามารถกำหนดสถานะเป็น {0} หรือไม่สามารถย้ายล็อคออกได้
#XMSG: Task log message
lockNotFound=เราไม่สามารถดำเนินการกระบวนการขั้นสุดท้ายได้เนื่องจากไม่พบล็อค: งานอาจถูกยกเลิกแล้ว
#XMSG: Task log message failed task
failedOverwrite=งาน {0} ถูกล็อคอยู่โดย {1} ดังนั้นงานจึงล้มเหลวโดยมีข้อผิดพลาดต่อไปนี้: {2}
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=การล็อคงานนี้ถูกรับช่วงต่อโดยงานอื่น
#XMSG: Task log message failed takeover
failedTakeover=ไม่สามารถรับช่วงต่องานที่มีอยู่ได้
#XMSG: Task log message successful takeover
successTakeover=ล็อคที่เหลือต้องถูกปลดล็อค ล็อคใหม่สำหรับงานนี้ถูกกำหนดแล้ว
#XMSG: Tasklog Dialog Details
txtDetails=คำสั่งระยะไกลที่ทำระหว่างการดำเนินการสามารถแสดงได้โดยการเปิดตัวติดตามตรวจสอบคิวรีระยะไกลในรายละเอียดของข้อความเฉพาะพาร์ทิชัน
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=คำสั่ง SQL ระยะไกลถูกลบออกจากฐานข้อมูลและไม่สามารถแสดงได้
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=คิวรีระยะไกลที่มีการกำหนดการเชื่อมต่อกับพื้นที่อื่นไม่สามารถแสดงได้ กรุณาไปที่ 'ตัวติดตามตรวจสอบคิวรีระยะไกล' และใช้ ID คำสั่งเพื่อฟิลเตอร์
#XMSG: Task log message for parallel check error
parallelCheckError=ไม่สามารถดำเนินการงานได้เนื่องจากงานอื่นกำลังดำเนินการและขัดขวางงานนี้อยู่
#XMSG: Task log message for parallel running task
parallelTaskRunning=งานที่ขัดแย้งกันกำลังดำเนินการอยู่
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=สถานะ {0} ระหว่างดำเนินการด้วย ID ความสัมพันธ์ {1}
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=ผู้ใช้ที่กำหนดไม่มีสิทธิพิเศษที่จำเป็นในการดำเนินการงานนี้

#XBUT: Label for open in Editor
openInEditor=เปิดในเอดิเตอร์
#XBUT: Label for open in Editor
openInEditorNew=เปิดในตัวสร้างข้อมูล
#XFLD:Run deails label
runDetails=รายละเอียดการดำเนินการ
#XFLD: Label for Logs
Logs=ล็อก
#XFLD: Label for Settings
Settings=การกำหนดค่า
#XFLD: Label for Save button
Save=เก็บบันทึก
#XFLD: Label for Standard
Standard_PO=ที่ปรับให้เหมาะสมกับประสิทธิภาพ (แนะนำ)
#XFLD: Label for Hana low memory processing
HLMP_MO=ที่ปรับให้เหมาะสมกับหน่วยความจำ
#XFLD: Label for execution mode
ExecutionMode=โหมดการดำเนินการ
#XFLD: Label for job execution
jobExecution=โหมดการประมวลผล
#XFLD: Label for Synchronous
syncExec=ซิงโครนัส
#XFLD: Label for Asynchronous
asyncExec=อะซิงโครนัส
#XFLD: Label for default asynchronous execution
defaultAsyncExec=ใช้ค่าตั้งต้น (อะซิงโครนัส, อาจเปลี่ยนแปลงในอนาคต)
#XMSG: Save settings success
saveSettingsSuccess=เปลี่ยนแปลงโหมดการดำเนินการ SAP HANA แล้ว
#XMSG: Save settings failure
saveSettingsFailed=การเปลี่ยนแปลงโหมดการดำเนินการ SAP HANA ล้มเหลว
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=เปลี่ยนแปลงการดำเนินการงานแล้ว
#XMSG: Job Execution change failed
jobExecSettingFailed=การเปลี่ยนแปลงการดำเนินการงานล้มเหลว
#XMSG: Text for Type
typeTxt=ประเภท
#XMSG: Text for Monitor
monitorTxt=ตัวติดตามตรวจสอบ
#XMSG: Text for activity
activityTxt=กิจกรรม
#XMSG: Text for metrics
metricsTxt=เมตริก
#XTXT: Text for Task chain key
TASK_CHAINS=เชนงาน
#XTXT: Text for View Key
VIEWS=มุมมอง
#XTXT: Text for remote table key
REMOTE_TABLES=ตารางระยะไกล
#XTXT: Text for Space key
SPACE=พื้นที่
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=โหนดการคำนวณแบบยืดหยุ่น
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=ผังการทำสำเนา
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=การค้นหาแบบอัจฉริยะ
#XTXT: Text for Local Table
LOCAL_TABLE=ตารางภายใน
#XTXT: Text for Data flow key
DATA_FLOWS=ผังข้อมูล
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=ขั้นตอน SQL Script
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=เชนกระบวนการ BW
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=ดูในตัวติดตามตรวจสอบ
#XTXT: Task List header text
taskListHeader=รายการงาน ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=ไม่สามารถดึงข้อมูลเมตริกสำหรับการดำเนินการผังข้อมูลในอดีต
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=รายละเอียดการดำเนินการทั้งหมดไม่ได้กำลังโหลดอยู่ในขณะนี้ กรุณาลองรีเฟรช
#XFLD: Label text for the Metrices table header
metricesColLabel=ป้ายชื่อตัวดำเนินการ
#XFLD: Label text for the Metrices table header
metricesType=ประเภท
#XFLD: Label text for the Metrices table header
metricesRecordLabel=จำนวนเรคคอร์ด
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=ดำเนินการเชนงาน
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=การดำเนินการเชนงานเริ่มต้นแล้ว
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=การดำเนินการเชนงานเริ่มต้นแล้วสำหรับ {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=ไม่สามารถดำเนินการเชนงานได้
#XTXT: Execute button label
runLabel=ดำเนินการ
#XTXT: Execute button label
runLabelNew=เริ่มต้นการดำเนินการ
#XMSG: Filter Object header
chainsFilteredTableHeader=ฟิลเตอร์ตามออบเจค: {0}
#XFLD: Parent task chain label
parentChainLabel=เชนงานหลัก:
#XFLD: Parent task chain unauthorized
Unauthorized=ไม่มีสิทธิในการดู
#XFLD: Parent task chain label
parentTaskLabel=งานหลัก:
#XTXT: Task status
NOT_TRIGGERED=ยังไม่ได้ทริกเกอร์
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=เข้าสู่โหมดเต็มจอภาพ
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=ออกจากโหมดเต็มจอภาพ
#XTXT: Close Task log details right panel
closeRightColumn=ปิดเซกชัน
#XTXT: Sort Text
sortTxt=จัดเรียง
#XTXT: Filter Text
filterTxt=ฟิลเตอร์
#XTXT: Filter by text to show list of filters applied
filterByTxt=ฟิลเตอร์ตาม
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=มากกว่า 5 นาที
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=มากกว่า 15 นาที
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=มากกว่า 1 ชั่วโมง
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=ชั่วโมงที่แล้ว
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=24 ชั่วโมงที่ผ่านมา
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=เดือนที่แล้ว
#XTXT: Messages title text
messagesText=ข้อความ

#XTXT Statistics information message
statisticsInfo=ไม่สามารถสร้างสถิติสำหรับตารางระยะไกลด้วยการเข้าถึงข้อมูล "ทำสำเนาแล้ว" เมื่อต้องการสร้างสถิติ ให้ย้ายข้อมูลที่ทำสำเนาใน 'ตัวติดตามตรวจสอบรายละเอียดของตารางระยะไกล' ออก
#XFLD
GO_TO_REMOTETABLE_DETAILS=ไปที่ 'ตัวติดตามตรวจสอบรายละเอียดของตารางระยะไกล'

#XTXT: Repair latest failed run label
retryRunLabel=ลองดำเนินการครั้งล่าสุดใหม่
#XTXT: Repair failed run label
retryRun=ลองดำเนินการใหม่
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=การลองดำเนินการเชนงานใหม่เริ่มต้นแล้ว
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=การลองดำเนินการเชนงานใหม่ล้มเหลว
#XMSG: Task chain child elements name
taskChainRetryChildObject=งาน {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=งานใหม่
#XFLD Analyzed View
analyzedView=มุมมองที่วิเคราะห์
#XFLD Metrics
Metrics=เมตริก
#XFLD Partition Metrics
PartitionMetrics=เมตริกของพาร์ทิชัน
#XFLD Entities
Messages=ล็อกของงาน
#XTXT: Title Message for empty partition data
partitionEmptyTitle=ยังไม่มีการกำหนดพาร์ทิชัน
#XTXT: Description message for empty partition data
partitionEmptyDescText=สร้างพาร์ทิชันโดยการระบุเกณฑ์เพื่อแบ่งปริมาณข้อมูลขนาดใหญ่ออกเป็นส่วนย่อยๆ ที่สามารถจัดการได้มากขึ้น

#XTXT: Title Message for empty runs data
runsEmptyTitle=ยังไม่มีล็อก
#XTXT: Description message for empty runs data
runsEmptyDescText=เมื่อคุณเริ่มต้นกิจกรรมใหม่ (โหลด Snapshot ใหม่, เริ่มต้น View Analyzer...) คุณจะเห็นล็อกที่เกี่ยวข้องที่นี่


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=การกำหนดรูปแบบโหนดการคำนวณแบบยืดหยุ่น {0} ไม่ได้รับการปรับปรุงให้สอดคล้องกัน กรุณาตรวจสอบข้อกำหนดของคุณ
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=กระบวนการเพิ่มโหนดการคำนวณแบบยืดหยุ่น {0} ล้มเหลว
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=การสร้างและการเปิดใช้งานแบบจำลองสำหรับออบเจคต้นทาง "{0}"."{1}" ในโหนดการคำนวณแบบยืดหยุ่น {2} เริ่มต้นแล้ว
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=การย้ายแบบจำลองสำหรับออบเจคต้นทาง "{0}"."{1}" ออกจากโหนดการคำนวณแบบยืดหยุ่น {2} เริ่มต้นแล้ว
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=การสร้างและการเปิดใช้งานแบบจำลองสำหรับออบเจคต้นทาง "{0}"."{1}" ล้มเหลว
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=การย้ายแบบจำลองสำหรับออบเจคต้นทาง "{0}"."{1}" ออกล้มเหลว
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=เริ่มต้นการกำหนดเส้นทางการคำนวณคิวรีการวิเคราะห์ของพื้นที่ {0} ไปยังโหนดการคำนวณแบบยืดหยุ่น {1} แล้ว
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=การกำหนดเส้นทางการคำนวณคิวรีการวิเคราะห์ของพื้นที่ {0} ไปยังโหนดการคำนวณแบบยืดหยุ่นที่เกี่ยวข้องล้มเหลว
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=เริ่มต้นการกำหนดเส้นทางใหม่สำหรับการคำนวณคิวรีการวิเคราะห์ของพื้นที่ {0} กลับจากโหนดการคำนวณแบบยืดหยุ่น {1} แล้ว
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=การกำหนดเส้นทางใหม่สำหรับการคำนวณคิวรีการวิเคราะห์ของพื้นที่ {0} กลับไปยังผู้ประสานงานล้มเหลว
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=ทริกเกอร์เชนงาน {0} ไปยังโหนดการคำนวณแบบยืดหยุ่นที่เกี่ยวข้อง {1} แล้ว
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=การสร้างเชนงานสำหรับโหนดการคำนวณแบบยืดหยุ่น {0} ล้มเหลว
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=การเตรียมโหนดการคำนวณแบบยืดหยุ่น {0} เริ่มต้นด้วยแผนการปรับขนาดที่ระบุ: vCPU: {1}, หน่วยความจำ (GiB): {2} และขนาดพื้นที่จัดเก็บ (GiB): {3}
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=โหนดการคำนวณแบบยืดหยุ่น {0} ถูกเตรียมแล้ว
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=โหนดการคำนวณแบบยืดหยุ่น {0} ถูกยกเลิกการเตรียมแล้ว
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=ไม่อนุญาตให้ดำเนินการ กรุณาหยุดโหนดการคำนวณแบบยืดหยุ่น {0} และเริ่มต้นใหม่เพื่ออัพเดทแผนการปรับขนาด
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=กระบวนการย้ายโหนดการคำนวณแบบยืดหยุ่น {0} ออกล้มเหลว
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=การดำเนินการปัจจุบันของโหนดการคำนวณแบบยืดหยุ่น {0} หมดเวลาแล้ว
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=การตรวจสอบสถานะของโหนดการคำนวณแบบยืดหยุ่น {0} อยู่ระหว่างดำเนินการ...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=การสร้างเชนงานสำหรับโหนดการคำนวณแบบยืดหยุ่น {0} ถูกล็อค ดังนั้นเชน {1} อาจดำเนินการอยู่
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=ออบเจคต้นทาง "{0}"."{1}" ถูกทำสำเนาเป็นความสัมพันธ์ของมุมมอง "{2}"."{3}"
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=ไม่สามารถทำสำเนาตาราง "{0}"."{1}" ได้เนื่องจากการทำสำเนาของตารางแถวเลิกใช้แล้ว
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=โหนดการคำนวณแบบยืดหยุ่นสูงสุดต่ออินสแตนซ์ SAP HANA Cloud เกินจำนวน
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=การดำเนินการสำหรับโหนดคำนวณแบบยืดหยุ่น {0} ยังอยู่ในระหว่างดำเนินการ
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=การลบแบบจำลองสำหรับตารางต้นทาง {0} หยุดลงเนื่องจากปัญหาทางเทคนิค กรุณาลองอีกครั้งภายหลัง
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=การสร้างแบบจำลองสำหรับตารางต้นทาง {0} หยุดลงเนื่องจากปัญหาทางเทคนิค กรุณาลองอีกครั้งภายหลัง
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=ไม่สามารถเริ่มต้นโหนดการคำนวณแบบยืดหยุ่นเนื่องจากถึงขีดจำกัดการใช้แล้วหรือยังไม่มีการจัดสรรจำนวนชั่วโมงที่บล็อคไว้สำหรับการคำนวณ
#XMSG: Task log message for chain preparation
chainLoadFromRepository=กำลังโหลดเชนงานและกำลังจัดเตรียมเพื่อดำเนินการงานทั้งหมด {0} รายการที่เป็นส่วนหนึ่งของเชนนี้
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=งานที่ขัดแย้งกันกำลังดำเนินการอยู่
#XMSG: Replication will change
txt_replication_change=ประเภทการทำสำเนาจะถูกเปลี่ยนแปลง
txt_repl_viewdetails=ดูรายละเอียด

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=ดูเหมือนว่าจะมีข้อผิดพลาดในการลองดำเนินการครั้งล่าสุดอีกครั้ง เนื่องจากการดำเนินการงานก่อนหน้าล้มเหลวก่อนที่จะสามารถสร้างแผนได้

#general messages
EXECUTION_ERROR_LOCKED_SPACE=พื้นที่ "{0}" ถูกล็อค

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=กิจกรรม {0} ต้องมีการปรับใช้ตารางภายใน {1}
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=กิจกรรม {0} ต้องมีการเปิดการจับข้อมูลเดลต้าสำหรับตารางภายใน {1}
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=กิจกรรม {0} ต้องใช้ตารางภายใน {1} เพื่อจัดเก็บข้อมูลในที่จัดเก็บออบเจค
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=กิจกรรม {0} ต้องใช้ตารางภายใน {1} เพื่อจัดเก็บข้อมูลในฐานข้อมูล

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=การเริ่มการย้ายเรคคอร์ดที่ลบออกสำหรับตารางภายใน {0}
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=การเริ่มการลบเรคคอร์ดทั้งหมดสำหรับตารางภายใน {0}
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=กำลังเริ่มต้นลบเรคคอร์ดสำหรับตารางภายใน {0} ตามเงื่อนไขการฟิลเตอร์ {1}
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=มีข้อผิดพลาดเกิดขึ้นขณะย้ายเรคคอร์ดที่ลบออกสำหรับตารางภายใน {0}
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=มีข้อผิดพลาดเกิดขึ้นขณะลบเรคคอร์ดทั้งหมดสำหรับตารางภายใน {0}
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=กำลังลบเรคคอร์ดทั้งหมดที่ดำเนินการเสร็จสมบูรณ์โดยมีประเภทการเปลี่ยนแปลง "ลบแล้ว" ที่นานกว่า {0} วัน
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=กำลังลบเรคคอร์ดทั้งหมดที่ดำเนินการเสร็จสมบูรณ์โดยมีประเภทการเปลี่ยนแปลง "ลบแล้ว" ที่นานกว่า {0}
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} เรคคอร์ดถูกลบ
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} เรคคอร์ดถูกทำเครื่องหมายสำหรับการลบ
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=การย้ายเรคคอร์ดที่ลบออกสำหรับตารางภายใน {0} เสร็จสมบูรณ์
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=การลบเรคคอร์ดทั้งหมดสำหรับตารางภายใน {0} เสร็จสมบูรณ์
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=กำลังเริ่มต้นทำเครื่องหมายเรคคอร์ดเป็น "ลบแล้ว" สำหรับตารางภายใน {0}
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=กำลังเริ่มต้นทำเครื่องหมายเรคคอร์ดเป็น "ลบแล้ว" สำหรับตารางภายใน {0} ตามเงื่อนไขการฟิลเตอร์ {1}
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=การทำเครื่องหมายเรคคอร์ดเป็น "ลบแล้ว" สำหรับตารางภายใน {0} เสร็จสมบูรณ์

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=การเปลี่ยนแปลงข้อมูลกำลังโหลดลงในตาราง {0} ชั่วคราว
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=การเปลี่ยนแปลงข้อมูลถูกโหลดลงในตาราง {0} ชั่วคราว
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=การเปลี่ยนแปลงข้อมูลถูกประมวลผลและลบออกจากตาราง {0} แล้ว

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=รายละเอียดการเชื่อมต่อ

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=กำลังเริ่มต้นการปรับตารางภายใน (ไฟล์) ให้เหมาะสม
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=มีข้อผิดพลาดเกิดขึ้นขณะปรับตารางภายใน (ไฟล์) ให้เหมาะสม
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=มีข้อผิดพลาดเกิดขึ้น การปรับตารางภายใน (ไฟล์) ให้เหมาะสมถูกหยุด
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=กำลังปรับตารางภายใน (ไฟล์) ให้เหมาะสม...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=ตารางภายใน (ไฟล์) ได้รับการปรับให้เหมาะสมแล้ว
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=ปรับตารางภายใน (ไฟล์) ให้เหมาะสมแล้ว
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=ตารางถูกปรับให้เหมาะสมกับคอลัมน์ลำดับ Z: {0}

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=มีข้อผิดพลาดเกิดขึ้น การตัดทอนตารางภายใน (ไฟล์) ถูกหยุด
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=กำลังตัดทอนตารางภายใน (ไฟล์)...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=ตำแหน่งที่ตั้งบัฟเฟอร์ขาเข้าสำหรับตารางภายใน (ไฟล์) ถูกยกเลิกแล้ว

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=กำลังเริ่มต้นทำให้ตารางภายใน (ไฟล์) ว่าง (ลบเรคคอร์ดที่ประมวลผลเสร็จสมบูรณ์ทั้งหมด)
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=มีข้อผิดพลาดเกิดขึ้นขณะทำให้ตารางภายใน (ไฟล์) ว่าง
#XMSG: Task log message
LTF_VACUUM_STOPPED=มีข้อผิดพลาดเกิดขึ้น การทำให้ตารางภายใน (ไฟล์) ว่างถูกหยุด
#XMSG: Task log message
LTF_VACUUM_PROCESSING=กำลังทำให้ตารางภายใน (ไฟล์) ว่าง...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=การทำให้ว่างเสร็จสมบูรณ์
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=กำลังลบเรคคอร์ดทั้งหมดที่ดำเนินการเสร็จสมบูรณ์ซึ่งมีอายุเกิน {0} วัน
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=กำลังลบเรคคอร์ดทั้งหมดที่ดำเนินการเสร็จสมบูรณ์ซึ่งมีอายุเกิน {0}

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=กำลังเริ่มต้นการผสานเรคคอร์ดใหม่กับตารางภายใน (ไฟล์)
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=กำลังเริ่มต้นการผสานเรคคอร์ดใหม่กับตารางภายใน (ไฟล์) โดยใช้การกำหนดค่า "ผสานข้อมูลโดยอัตโนมัติ"
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=กำลังเริ่มผสานเรคคอร์ดใหม่กับตารางภายใน (ไฟล์) งานนี้เริ่มต้นผ่านคำขอผสาน API
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=กำลังผสานเรคคอร์ดใหม่กับตารางภายใน (ไฟล์)
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=มีข้อผิดพลาดเกิดขึ้นขณะผสานเรคคอร์ดใหม่กับตารางภายใน (ไฟล์)
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=ผสานตารางภายใน (ไฟล์) แล้ว
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=มีข้อผิดพลาดเกิดขึ้น การผสานตารางภายใน (ไฟล์) ถูกหยุด
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=การผสานตารางภายใน (ไฟล์) ล้มเหลวเนื่องจากเกิดข้อผิดพลาด แต่การดำเนินการสำเร็จไปบางส่วน และมีการผสานข้อมูลบางส่วนไปแล้ว
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=มีข้อผิดพลาดเกี่ยวกับการหมดเวลาเกิดขึ้น กิจกรรม {0} ดำเนินไปเป็นเวลา {1} ชั่วโมงแล้ว
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=ไม่สามารถเริ่มต้นการดำเนินการแบบอะซิงโครนัสได้เนื่องจากโหลดระบบสูง เปิด "ตัวติดตามตรวจสอบระบบ" และตรวจสอบงานที่ดำเนินการอยู่
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=การดำเนินการแบบอะซิงโครนัสถูกยกเลิก
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=งาน {0} ดำเนินการภายในขีดจำกัดหน่วยความจำ {1} และ {2}
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=งาน {0} ดำเนินการด้วย ID ทรัพยากร {1}

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=การค้นหาและแทนที่เริ่มต้นแล้วในตารางภายใน (ไฟล์)
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=การค้นหาและแทนที่เสร็จสมบูรณ์แล้วในตารางภายใน (ไฟล์)
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=การค้นหาและแทนที่ล้มเหลวในตารางภายใน (ไฟล์)

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=มีข้อผิดพลาดเกิดขึ้น การอัพเดทสถิติสำหรับตารางภายใน (ไฟล์) ถูกหยุด

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=งานล้มเหลวเนื่องจากเกิดข้อผิดพลาดหน่วยความจำไม่เพียงพอบนฐานข้อมูล SAP HANA
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=งานล้มเหลวเนื่องจากการปฏิเสธการควบคุมการเข้าถึง SAP HANA
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=งานล้มเหลวเนื่องจากมีการเชื่อมต่อ SAP HANA ที่ใช้งานอยู่มากเกินไป

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=ไม่สามารถดำเนินการลองใหม่เนื่องจากการลองใหม่ได้รับอนุญาตเฉพาะในรายการหลักของเชนงานแบบซ้อนกันเท่านั้น
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=ล็อกของงานย่อยที่ล้มเหลวจะไม่สามารถใช้ได้อีกต่อไปสำหรับการลองใหม่เพื่อดำเนินการต่อ


####Metrics Labels

performanceOptimized=ที่ปรับให้เหมาะสมกับประสิทธิภาพ
memoryOptimized=ที่ปรับให้เหมาะสมกับหน่วยความจำ

JOB_EXECUTION=การดำเนินงาน
EXECUTION_MODE=โหมดการดำเนินการ
NUMBER_OF_RECORDS_OVERALL=จำนวนเรคคอร์ดที่คงไว้
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=จำนวนเรคคอร์ดที่อ่านจากแหล่งข้อมูลระยะไกล
RUNTIME_MS_REMOTE_EXECUTION_TIME=เวลาในการประมวลผลแหล่งข้อมูลระยะไกล
MEMORY_CONSUMPTION_GIB=หน่วยความจำสูงสุดของ SAP HANA
NUMBER_OF_PARTITIONS=จำนวนพาร์ทิชัน
MEMORY_CONSUMPTION_GIB_OVERALL=หน่วยความจำสูงสุดของ SAP HANA
NUMBER_OF_PARTITIONS_LOCKED=จำนวนพาร์ทิชันที่ถูกล็อค
PARTITIONING_COLUMN=คอลัมน์การพาร์ทิชัน
HANA_PEAK_CPU_TIME=เวลา CPU ทั้งหมดของ SAP HANA
USED_IN_DISK=พื้นที่จัดเก็บที่ใช้
INPUT_PARAMETER_PARAMETER_VALUE=พารามิเตอร์ป้อนข้อมูล
INPUT_PARAMETER=พารามิเตอร์ป้อนข้อมูล
ECN_ID=ชื่อโหนดการคำนวณแบบยืดหยุ่น

DAC=การควบคุมการเข้าถึงข้อมูล
YES=ใช่
NO=ไม่ใช่
noofrecords=จำนวนเรคคอร์ด
partitionpeakmemory=หน่วยความจำสูงสุดของ SAP HANA
value=ค่า
metricsTitle=เมตริก ({0})
partitionmetricsTitle=พาร์ทิชัน ({0})
partitionLabel=พาร์ทิชัน
OthersNotNull=ค่าที่ไม่รวมอยู่ในช่วง
OthersNull=ค่า Null
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=การกำหนดค่าที่ใช้สำหรับการดำเนินการคงอยู่ของข้อมูลครั้งล่าสุด:
#XMSG: Message for input parameter name
inputParameterLabel=พารามิเตอร์ป้อนข้อมูล
#XMSG: Message for input parameter value
inputParameterValueLabel=ค่า
#XMSG: Message for persisted data
inputParameterPersistedLabel=คงไว้เมื่อ
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=ลบข้อมูล
REMOVE_DELETED_RECORDS=ย้ายข้อมูลที่ลบออก
MERGE_FILES=ผสานไฟล์
OPTIMIZE_FILES=ปรับไฟล์ให้เหมาะสม
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=ดูในตัวติดตามตรวจสอบบริดจ์ของ SAP BW

ANALYZE_PERFORMANCE=วิเคราะห์ประสิทธิภาพ
CANCEL_ANALYZE_PERFORMANCE=ยกเลิก 'วิเคราะห์ประสิทธิภาพ'

#XFLD: Label for frequency column
everyLabel=ทุก
#XFLD: Plural Recurrence text for Hour
hoursLabel=ชั่วโมง
#XFLD: Plural Recurrence text for Day
daysLabel=วัน
#XFLD: Plural Recurrence text for Month
monthsLabel=เดือน
#XFLD: Plural Recurrence text for Minutes
minutesLabel=นาที

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">คำแนะนำในการแก้ไขปัญหาการคงอยู่ของมุมมอง</a>
#XTXT TEXT for view persistency guide link
OOMMessage=หน่วยความจำไม่เพียงพอสำหรับกระบวนการ ไม่สามารถคงข้อมูลสำหรับมุมมอง "{0}" ได้ กรุณาดูข้อมูลเพิ่มเติมเกี่ยวกับข้อผิดพลาดหน่วยความจำไม่เพียงพอใน Help Portal ให้พิจารณาตรวจสอบ View Analyzer เพื่อวิเคราะห์มุมมองสำหรับความซับซ้อนของการใช้หน่วยความจำ

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=ไม่เกี่ยวข้อง
OPEN_BRACKET=(
CLOSE_BRACKET=)
