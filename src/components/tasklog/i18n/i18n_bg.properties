
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Подробни данни за журнал
#XFLD: Header
TASK_LOGS=Журнали на задачи ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Изпълнения ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Преглед на подробни данни
#XFLD: Button text
STOP=Спиране на изпълнение
#XFLD: Label text
RUN_START=Начало на последното изпълнение
#XFLD: Label text
RUN_END=Край на последното изпълнение
#XFLD: Label text
RUNTIME=Времетраене
#XTIT: Count for Messages
txtDetailMessages=Съобщения ({0})
#XFLD: Label text
TIME=Времева отметка
#XFLD: Label text
MESSAGE=Съобщение
#XFLD: Label text
TASK_STATUS=Категория
#XFLD: Label text
TASK_ACTIVITY=Дейност
#XFLD: Label text
RUN_START_DETAILS=Начало
#XFLD: Label text
RUN_END_DETAILS=Край
#XFLD: Label text
LOGS=Изпълнения
#XFLD: Label text
STATUS=Статус
#XFLD: Label text
RUN_STATUS=Статус на изпълнение
#XFLD: Label text
Runtime=Времетраене
#XFLD: Label text
RuntimeTooltip=Времетраене (чч : мм : сс)
#XFLD: Label text
TRIGGEREDBY=Инициирал
#XFLD: Label text
TRIGGEREDBYNew=Изпълнява се от
#XFLD: Label text
TRIGGEREDBYNewImp=Изпълнението е стартирано от
#XFLD: Label text
EXECUTIONTYPE=Вид изпълнение
#XFLD: Label text
EXECUTIONTYPENew=Вид изпълнение
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Пространство на висшестояща верига
#XFLD: Refresh tooltip
TEXT_REFRESH=Опресняване
#XFLD: view Details link
VIEW_ERROR_DETAILS=Преглед на подробни данни
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Изтегляне на допълнителни подробни данни
#XMSG: Download completed
downloadStarted=Изтеглянето е стартирано
#XMSG: Error while downloading content
errorInDownload=Възникна грешка при изтеглянето.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Преглед на подробни данни
#XBTN: cancel button of task details dialog
TXT_CANCEL=Отказ
#XBTN: back button from task details
TXT_BACK=Назад
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Задачата е завършена
#XFLD: Log message with failed status
MSG_LOG_FAILED=Неуспешна задача
#XFLD: Master and detail table with no data
No_Data=Няма данни
#XFLD: Retry tooltip
TEXT_RETRY=Повторен опит
#XFLD: Cancel Run label
TEXT_CancelRun=Отмяна на изпълнение
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Неуспешно зареждане на изчистване
#XMSG:button copy sql statement
txtSQLStatement=Копиране на SQL инструкция
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Отваряне на монитора на отдалечените заявки
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=Кликнете върху „Отваряне на монитора на отдалечените заявки“, за да се отворят отдалечените SQL инструкции.
#XMSG:button ok
txtOk=ОК
#XMSG: button close
txtClose=Затваряне
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=Отмяната на действие изпълнение за обект „{0}“ е стартирана.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=Отмяната на действие изпълнение за обект „{0}“ е неуспешна.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=Отмяната на действие изпълнение за обект „{0}“ вече е невъзможна, защото статусът на репликация е променен.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Няма журнали на задачи със статус “Изпълнява се”.
#XMSG: message for conflicting task
Task_Already_Running=За обект „{0}“ вече се изпълнява конфликтна задача.
#XFLD: Label for no task log with running state title
actionInfo=Информация за действие
#XMSG Copied to clipboard
copiedToClip=Копирано в клипборд
#XFLD copy
Copy=Копиране
#XFLD copy correlation ID
CopyCorrelationID=Копиране на ИД на корелация
#XFLD Close
Close=Затваряне
#XFLD: show more Label
txtShowMore=Покажи повече
#XFLD: message Label
messageLabel=Съобщение:
#XFLD: details Label
detailsLabel=Подробни данни:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Активна SQL \r\n инструкция:
#XFLD:statementId Label
statementIdLabel=ИД на инструкция:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Брой на отдалечените \r\n SQL инструкции:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Брой инструкции
#XFLD: Space Label
txtSpaces=Пространство
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Неоторизирани пространства
#XFLD: Privilege Error Text
txtPrivilegeError=Нямате необходимите права за преглед на тези данни.
#XFLD: Label for Object Header
DATA_ACCESS=Достъп до данните
#XFLD: Label for Object Header
SCHEDULE=Планиране
#XFLD: Label for Object Header
DETAILS=Подробни данни
#XFLD: Label for Object Header
LATEST_UPDATE=Последна актуализация
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Последна промяна (източник)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Честота на опресняване
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Планирана честота
#XFLD: Label for Object Header
NEXT_RUN=Следващо изпълнение
#XFLD: Label for Object Header
CONNECTION=Връзка
#XFLD: Label for Object Header
DP_AGENT=Агент за предоставяне на данни
#XFLD: Label for Object Header
USED_IN_MEMORY=Използвана памет за съхранение (MiB)
#XFLD: Label for Object Header
USED_DISK=Използван диск за съхранение (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Размер на оперативната памет (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Размер на диска (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Брой записи

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Зададено като „Неуспешно“
SET_TO_FAILED_ERR=Задача, чието изпълнение вече е започнало, е определена като неуспешна.
#XFLD: Label for stopped failed
FAILLOCKED=Вече се изпълнява
#XFLD: sub status STOPPED
STOPPED=Спряно
STOPPED_ERR=Задачата е спряна, без да се анулират промените.
#XFLD: sub status CANCELLED
CANCELLED=Отказ
CANCELLED_ERR=Изпълнението на задачата е отказано, след като е било стартирано. Поради това данните са върнати към състоянието им отпреди инициирането на изпълнението.
#XFLD: sub status LOCKED
LOCKED=Заключено
LOCKED_ERR=Същата задача вече се изпълнява, поради което не може да се извърши паралелно изпълнение на това задание.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Изключение за задача
TASK_EXCEPTION_ERR=При изпълнението на задачата е възникнала някаква грешка.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Изключение при изпълнението на задача
TASK_EXECUTE_EXCEPTION_ERR=При изпълнението на задачата е възникнала грешка.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Липсва оторизация
UNAUTHORIZED_ERR=Самоличността на потребителя не може да бъде удостоверена или той е заключен или е изтрит.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Забранено
FORBIDDEN_ERR=Присъединеният потребител няма нужните привилегии, за да изпълни тази задача.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Не е инициирано
FAIL_NOT_TRIGGERED_ERR=Заданието не може да бъде изпълнено, поради системно прекъсване или недостъпност на част от системата на база данни към момента на планираното изпълнение. Изчакайте следващото време за изпълнение на планираното задание или го планирайте повторно.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Планирането е отказано
SCHEDULE_CANCELLED_ERR=Заданието не може да бъде изпълнено поради вътрешна грешка. Свържете се с отдела по поддръжка на SAP и им предоставете ИД на корелация и времевата отметка от информацията в журнала на това задание.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Предходното изпълнение още не е завършено
SUCCESS_SKIPPED_ERR=Изпълнението на задачата не е инициирано, защото все още е в ход предишното изпълнение на същата задача.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Липсва собственик
FAIL_OWNER_MISSING_ERR=Заданието не може да бъде изпълнено поради липса на присъединен системен потребител. Присъединете потребител с права на собственик към заданието.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Липсва съгласие
FAIL_CONSENT_NOT_AVAILABLE_ERR=Не сте упълномощили SAP да изпълнява вериги от задачи или да планира задачи за интеграция на данни от ваше име. Изберете опцията за даване на съгласие.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Валидността на съгласието е изтекло
FAIL_CONSENT_EXPIRED_ERR=Оторизацията, която позволява на SAP да изпълнява вериги от задачи или да планира задачи за интеграция на данни от ваше име, е изтекла. Изберете опцията осигурена за подновяване на съгласието ви.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Съгласието вече е невалидно
FAIL_CONSENT_INVALIDATED_ERR=Тази задача не се изпълнява обикновено поради промяна в конфигурацията на доставчик на самоличност на наемателя. В този случай не могат да бъдат изпълнявани или планирани никакви нови задачи от името на засегнатия потребител. Ако присъединеният потребител все още съществува при новия доставчик на самоличност, оттеглете планираното съгласие и след това го предоставете отново. Ако присъединеният потребител не съществува, присъединете нов собственик на заданието и осигурете необходимото съгласие за планиране на задача. За още информация вижте следната бележка на SAP: https://launchpad.support.sap.com/#/notes/3089828.
TASK_EXECUTOR_ERROR=Изпълнител на задача
TASK_EXECUTOR_ERROR_ERR=При тази задача възникна вътрешна грешка, вероятно по време на подготвителните стъпки за изпълнение и тя не може да бъде стартирана.
PREREQ_NOT_MET=Предварителните изисквания не са изпълнени
PREREQ_NOT_MET_ERR=Тази задача не може да бъде изпълнена заради проблеми в дефиниция ѝ. Например, обектът не се разгръща, верига от задачи съдържа циклична логика, или SQL на изглед е невалиден.
RESOURCE_LIMIT_ERROR=Грешка в ограниченията за ресурси
RESOURCE_LIMIT_ERROR_ERR=В момента задачата не може да бъде изпълнена, защото няма достатъчно налични ресурси или са заети.
FAIL_CONSENT_REFUSED_BY_UMS=Съгласието е отказано
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Тази задача не беше изпълнена в планираните изпълнения на веригите от задачи заради промяна в конфигурацията на доставчика на идентичност на даден потребител в наемателя. За повече информация вижте следната SAP бележка: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Планирано
#XFLD: status text
SCHEDULEDNew=Постоянно
#XFLD: status text
PAUSED=Пауза
#XFLD: status text
DIRECT=Директно
#XFLD: status text
MANUAL=Ръчно
#XFLD: status text
DIRECTNew=Опростено
#XFLD: status text
COMPLETED=Завършено
#XFLD: status text
FAILED=Неуспешно
#XFLD: status text
RUNNING=Изпълнява се
#XFLD: status text
none=Няма
#XFLD: status text
realtime=В реално време
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Подзадача
#XFLD: New Data available in the file
NEW_DATA=Нови данни

#XFLD: text for values shown in column Replication Status
txtOff=Изключено
#XFLD: text for values shown in column Replication Status
txtInitializing=Инициализиране
#XFLD: text for values shown in column Replication Status
txtLoading=Зареждане
#XFLD: text for values shown in column Replication Status
txtActive=Активно
#XFLD: text for values shown in column Replication Status
txtAvailable=Налично
#XFLD: text for values shown in column Replication Status
txtError=Грешка
#XFLD: text for values shown in column Replication Status
txtPaused=Поставена на пауза
#XFLD: text for values shown in column Replication Status
txtDisconnected=Прекъсната връзка
#XFLD: text for partially Persisted views
partiallyPersisted=Частично съхранено

#XFLD: activity text
REPLICATE=Репликация
#XFLD: activity text
REMOVE_REPLICATED_DATA=Премахване на репликирани данни
#XFLD: activity text
DISABLE_REALTIME=Дезактивиране на репликацията на данни в реално време
#XFLD: activity text
REMOVE_PERSISTED_DATA=Премахване на трайно съхранените данни
#XFLD: activity text
PERSIST=Трайно съхранение
#XFLD: activity text
EXECUTE=Изпълнение
#XFLD: activity text
TASKLOG_CLEANUP=Tacklog_Cleanup
#XFLD: activity text
CANCEL_REPLICATION=Отказ от репликация
#XFLD: activity text
MODEL_IMPORT=Импорт на модел
#XFLD: activity text
NONE=Няма
#XFLD: activity text
CANCEL_PERSISTENCY=Отмяна на трайното съхранение
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Изглед за анализ
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Отмяна на анализатор на изгледи

#XFLD: severity text
INFORMATION=Информация
#XFLD: severity text
SUCCESS=Успех
#XFLD: severity text
WARNING=Предупреждение
#XFLD: severity text
ERROR=Грешка
#XFLD: text for values shown for Ascending sort order
SortInAsc=Сортиране във възходящ ред
#XFLD: text for values shown for Descending sort order
SortInDesc=Сортиране в низходящ ред
#XFLD: filter text for task log columns
Filter=Филтър
#XFLD: object text for task log columns
Object=Обект
#XFLD: space text for task log columns
crossSpace=Пространство

#XBUT: label for remote data access
REMOTE=Отдалечено
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Репликация (в реално време)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Репликация (моментна снимка)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=Репликацията в реално време е блокирана поради грешка. Щом грешката бъде отстранена, можете да използвате бутона „Повторен опит“, за да продължите репликацията.
ERROR_MSG=Репликацията в реално време е блокирана поради грешка.
RETRY_FAILED_ERROR=Процесът за повторен опит завърши неуспешно с грешка.
LOG_INFO_DETAILS=При репликиране на данни в реално време не се генерират журнали. Показаните журнали се отнасят до предходни действия.

#XBUT: Partitioning label
partitionMenuText=Разделяне на дялове
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Създаване на дял
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Редактиране на дял
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Изтриване на дял
#XFLD: Initial text
InitialPartitionText=Дефиниране на дялове чрез определяне на критерии за разделяне на големи набори от данни на по-малки.
DefinePartition=Дефиниране на дялове
#XFLD: Message text
partitionChangedInfo=Дефиницията на дял е променена след последната репликация. Промените ще бъдат приложени при следващото зареждане на данни.
#XFLD: Message text
REAL_TIME_WARNING=Разделянето на дялове се прилага само при зареждането на нова моментна снимка. То е неприложимо при репликация в реално време.
#XFLD: Message text
loadSelectedPartitions=Стартирано е трайно съхранение на данни за избраните дялове от „{0}“
#XFLD: Message text
loadSelectedPartitionsError=Неуспешно съхраняване на данни за избраните дялове от „{0}“
#XFLD: Message text
viewpartitionChangedInfo=Определението за дяла е променено след последното изпълнение на съхранение. За да приложите промените, следващото зареждане на данни ще бъде пълна моментна снимка, включваща заключените дялове. Когато това пълно натоварване бъде завършено, ще можете да изпълнявате данни за отделни дялове.
#XFLD: Message text
viewpartitionChangedInfoLocked=Дефиницията за дяла е променена след последното изпълнение на съхранение. За да приложите промените, следващото зареждане на данни ще бъде пълна моментна снимка, с изключение на заключените и непроменените диапазони от дялове. Когато това зареждане бъде завършено, ще може отново да зареждате избраните дялове.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Репликация на таблица
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Насрочване на репликация
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Създаване на график
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Редактиране на графика
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Изтриване на графика
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Зареждане на нова моментна снимка
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Стартиране на репликация на данни
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Премахване на тиражирани данни
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Активиране на достъп в реално време
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Активиране на репликация на данни в реално време
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Дезактивиране на репликация на данни в реално време
#XFLD: Message for replicate table action
replicateTableText=Репликация на таблица
#XFLD: Message for replicate table action
replicateTableTextNew=Репликация на данни
#XFLD: Message to schedule task
scheduleText=Планиране
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Трайно съхранение на изгледи
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Съхранение на данни
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Зареждане на нова моментна снимка
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Стартиране на съхранението на данни
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Премахване на трайно съхранените данни
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Обработка на дялове
#XBUT: Label for scheduled replication
scheduledTxt=Планирано
#XBUT: Label for statistics button
statisticsTxt=Статистика
#XBUT: Label for create statistics
createStatsTxt=Създаване на статистика
#XBUT: Label for edit statistics
editStatsTxt=Редактиране на статистика
#XBUT: Label for refresh statistics
refreshStatsTxt=Опресняване на статистика
#XBUT: Label for delete statistics
dropStatsTxt=Изтриване на статистика
#XMSG: Create statistics success message
statsSuccessTxt=Стартиране на създаването на статистика от вид {0} за {1}.
#XMSG: Edit statistics success message
statsEditSuccessTxt=Стартиране промяната на вида статистика на {0} за {1}.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Стартиране на опресняването на статистиката за {0}.
#XMSG: Drop statistics success message
statsDropSuccessTxt=Статистиката е изтрита успешно за {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=Грешка при създаване на статистика
#XMSG: Edit statistics error message
statsEditErrorTxt=Грешка при промяна на статистика
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Грешка при опресняване на статистика
#XMSG: Drop statistics error message
statsDropErrorTxt=Грешка при изтриване на статистика
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Наистина ли искате да изтриете статистиката за данните?
startPersistencyAdvisorLabel=Стартиране на анализатор изгледи

#Partition related texts
#XFLD: Label for Column
column=Колона
#XFLD: Label for No of Partition
noOfPartitions=Брой дялове
#XFLD: Label for Column
noOfParallelProcess=Брой паралелни процеси
#XFLD: Label text
noOfLockedPartition=Брой заключени дялове
#XFLD: Label for Partition
PARTITION=Дялове
#XFLD: Label for Column
AVAILABLE=Налично
#XFLD: Statistics Label
statsLabel=Статистика
#XFLD: Label text
COLUMN=Колона:
#XFLD: Label text
PARALLEL_PROCESSES=Паралелни процеси:
#XFLD: Label text
Partition_Range=Диапазон дялове
#XFLD: Label text
Name=Име
#XFLD: Label text
Locked=Заключен
#XFLD: Label text
Others=ДРУГИ
#XFLD: Label text
Delete=Изтриване
#XFLD: Label text
LoadData=Зареждане на избраните дялове
#XFLD: Label text
LoadSelectedData=Зареждане на избраните дялове
#XFLD: Confirmation text
LoadNewPersistenceConfirm=Ще се зареди нова моментна снимка не само за избраните, но и за всички отключени и променени дялове. Желаете ли да продължите?
#XFLD: Label text
Continue=Напред

#XFLD: Label text
PARTITIONS=Дялове
#XFLD: Label text
ADD_PARTITIONS=+ Добавяне на дялове
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Добавяне на дялове
#XFLD: Label text
deleteRange=Изтриване на дял
#XFLD: Label text
LOW_PLACE_HOLDER=Въведете ниска стойност
#XFLD: Label text
HIGH_PLACE_HOLDER=Въведете висока стойност
#XFLD: tooltip text
lockedTooltip=Заключване на дял след зареждане

#XFLD: Button text
Edit=Редактиране
#XFLD: Button text
CANCEL=Отказ

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Последна актуализация на статистика
#XFLD: Statistics Fields
STATISTICS=Статистика

#XFLD:Retry label
TEXT_Retry=Повторен опит
#XFLD:Retry label
TEXT_Retry_tooltip=Направете нов опит за репликация в реално време след отстраняване на грешка.
#XFLD: text retry
Retry=Потвърждаване
#XMG: Retry confirmation text
retryConfirmationTxt=Последната репликация в реално време приключи с грешка.\nПотвърдете, че грешката е отстранена и че репликацията може да продължи.
#XMG: Retry success text
retrySuccess=Процесът по повторен опит е инициализиран успешно.
#XMG: Retry fail text
retryFail=Процесът по повторен опит е неуспешен.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Създаване на статистика
#XMSG: activity message for edit statistics
DROP_STATISTICS=Изтриване на статистика
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Опресняване на статистика
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Редактиране на статистика
#XMSG: Task log message started task
taskStarted=Задача {0} стартира.
#XMSG: Task log message for finished task
taskFinished=Задача {0} завърши със статус {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=Задача {0} приключи в {2} със статус {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=Задача {0} има входни параметри.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=Задача {0} завърши с неочаквана грешка. Статусът на задачата е зададен на {1}.
#XMSG: Task log message for failed task
failedToEnd=Неуспешно задаване статуса на {0} или неуспешно премахване на заключването.
#XMSG: Task log message
lockNotFound=Не можем да финализираме процеса, тъй като заключването липсва: възможно е задачата да е отказана.
#XMSG: Task log message failed task
failedOverwrite=Задача {0} вече е заключена от {1}. Затова тя завърши неуспешно със следната грешка: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Заключването на тази задача е прехвърлено към друга задача.
#XMSG: Task log message failed takeover
failedTakeover=Неуспешно приемане на съществуваща задача.
#XMSG: Task log message successful takeover
successTakeover=Заключването трябваше да бъде деблокирано. Зададено е ново заключване за тази задача.
#XMSG: Tasklog Dialog Details
txtDetails=Отдалечените инструкции, обработени по време на изпълнението, се показват в подробните данни на съобщенията за дялове в Монитора на отдалечените заявки.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=Отдалечените SQL инструкции са изтрити от базата данни и не може да се покажат.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Не могат да се показват отдалечени заявки, които имат връзки, присъединени към други пространства. Отидете до Монитора на отдалечените заявки и използвайте ИД на инструкцията, за да ги филтрирате.
#XMSG: Task log message for parallel check error
parallelCheckError=Задачата не може да бъде обработена, защото в момента се изпълнява друга задача, която вече блокира тази.
#XMSG: Task log message for parallel running task
parallelTaskRunning=Вече се изпълнява конфликтна задача.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Статус {0} при изпълнение на ИД на корелация {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=Присъединеният потребител няма нужните привилегии, за да изпълни тази задача.

#XBUT: Label for open in Editor
openInEditor=Отваряне в редактора
#XBUT: Label for open in Editor
openInEditorNew=Отваряне в Генератора на данни
#XFLD:Run deails label
runDetails=Подробни данни за изпълнението
#XFLD: Label for Logs
Logs=Журнали
#XFLD: Label for Settings
Settings=Настройки
#XFLD: Label for Save button
Save=Запазване
#XFLD: Label for Standard
Standard_PO=Представяне-оптимизирано (препоръчително)
#XFLD: Label for Hana low memory processing
HLMP_MO=Оптимизирано за памет
#XFLD: Label for execution mode
ExecutionMode=Режим на изпълнение
#XFLD: Label for job execution
jobExecution=Режим на обработка
#XFLD: Label for Synchronous
syncExec=Синхронно
#XFLD: Label for Asynchronous
asyncExec=Асинхронно
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Използване на стойност по подразбиране (асинхронно – може да се промени по-късно)
#XMSG: Save settings success
saveSettingsSuccess=Режимът на изпълнение за SAP HANA е променен.
#XMSG: Save settings failure
saveSettingsFailed=Промяната на режима на изпълнение за SAP HANA е неуспешна.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Изпълнението на задание е променено.
#XMSG: Job Execution change failed
jobExecSettingFailed=Промяната на изпълнение на задание е неуспешна.
#XMSG: Text for Type
typeTxt=Вид
#XMSG: Text for Monitor
monitorTxt=Наблюдение
#XMSG: Text for activity
activityTxt=Дейност
#XMSG: Text for metrics
metricsTxt=Метрики
#XTXT: Text for Task chain key
TASK_CHAINS=Верига задачи
#XTXT: Text for View Key
VIEWS=Изглед
#XTXT: Text for remote table key
REMOTE_TABLES=Отдалечена таблица
#XTXT: Text for Space key
SPACE=Интервал
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Еластичен изчислителен възел
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Поток на репликация
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Интелигентно търсене
#XTXT: Text for Local Table
LOCAL_TABLE=Локална таблица
#XTXT: Text for Data flow key
DATA_FLOWS=Поток на данни
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=Процедура на SQL скрипт
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=Верига на процес BW
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Преглед в монитор
#XTXT: Task List header text
taskListHeader=Списък със задачи ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Метриките за старите изпълнения на потока от данни не могат да бъдат извлечени.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=В момента пълните подробни данни за изпълнение не могат да се заредят. Опитайте да опресните.
#XFLD: Label text for the Metrices table header
metricesColLabel=Етикет за оператор
#XFLD: Label text for the Metrices table header
metricesType=Вид
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Брой записи
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Изпълнение на веригата от задачи
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Изпълнението на веригата от задачи стартира.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Изпълнението на веригата от задачи е стартирано за {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Неуспешно изпълнение на веригата от задачи.
#XTXT: Execute button label
runLabel=Изпълнение
#XTXT: Execute button label
runLabelNew=Стартиране на изпълнение
#XMSG: Filter Object header
chainsFilteredTableHeader=Филтрирани по обект: {0}
#XFLD: Parent task chain label
parentChainLabel=Висшестояща верига от задачи:
#XFLD: Parent task chain unauthorized
Unauthorized=Няма оторизация за преглед
#XFLD: Parent task chain label
parentTaskLabel=Висшестояща задача:
#XTXT: Task status
NOT_TRIGGERED=Не е инициирано
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Отваряне на цял екран
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Изход от цял екран
#XTXT: Close Task log details right panel
closeRightColumn=Затваряне на раздела
#XTXT: Sort Text
sortTxt=Сортиране
#XTXT: Filter Text
filterTxt=Филтриране
#XTXT: Filter by text to show list of filters applied
filterByTxt=Филтриране по
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Повече от 5 минути
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Повече от 15 минути
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Повече от 1 час
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Последния час
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Последните 24 часа
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Последния месец
#XTXT: Messages title text
messagesText=Съобщения

#XTXT Statistics information message
statisticsInfo=За отдалечени таблици с достъп до данни „Репликирано“ не може да се създаде статистика. За да я създадете, премахнете репликираните данни в монитора на данни за отдалечените таблици.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Към монитор на данни за отдалечените таблици

#XTXT: Repair latest failed run label
retryRunLabel=Повторен опит на последно изпълнение
#XTXT: Repair failed run label
retryRun=Повторен опит на изпълнение
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Повторният опит на изпълнението на веригата от задачи стартира
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Повторният опит на изпълнението на веригата от задачи е неуспешно
#XMSG: Task chain child elements name
taskChainRetryChildObject=Задача {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Нова задача
#XFLD Analyzed View
analyzedView=Анализиран изглед
#XFLD Metrics
Metrics=Метрики
#XFLD Partition Metrics
PartitionMetrics=Метрики на дяла
#XFLD Entities
Messages=Журнал на задачите
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Все още не са дефинирани дялове.
#XTXT: Description message for empty partition data
partitionEmptyDescText=Може да създавате дялове чрез задаване на критерии за разделяне на по-големи обеми от данни на по-малки и по-лесно управляеми части.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Все още няма налични журнали
#XTXT: Description message for empty runs data
runsEmptyDescText=Когато започнете нова дейност (зареждане на нова моментна снимка, стартиране на анализатор на изгледи...), ще видите свързаните журнали тук.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=Конфигурацията на еластичния изчислителен възел {0} не е правилно настроена. Прегледайте дефиницията.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Процесът за добавяне на еластичен изчислителен възел {0} е неуспешен.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Започна създаването и активирането на реплика за изходния обект „{0}“.„{1}“ в еластичния изчислителен възел {2}.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Започна премахването на репликата за изходния обект „{0}“.„{1}“ в еластичния изчислителен възел {2}.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Грешка при създаването и активирането на репликата за изходния обект „{0}“.„{1}“.
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Неуспешно премахване на репликата за изходния обект „{0}“.„{1}“.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Стартирана е маршрутизацията за изчисляване на запитвания за анализ от пространството {0} към еластичния изчислителен възел {1}.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Неуспешна маршрутизация за изчисляване на запитвания за анализ от пространството {0} към съответстващия еластичен изчислителен възел.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Стартирана е повторна маршрутизация за изчисляване на запитвания за анализ от пространството {0} обратно от еластичния изчислителен възел {1}.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Неуспешна повторна маршрутизация за изчисляване на запитвания за анализ от пространството {0} обратно към координатора.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Инициирана е верига задачи {0} към съответния еластичен изчислителен възел {1}.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Неуспешно генериране на верига от задачи за еластичния изчислителен възел {0}.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=Започна предоставянето на еластичния изчислителен възел {0} със следния план за размер: vCPU: {1}, памет(GiB): {2} и размер на мястото за съхранение(GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=Еластичният изчислителен възел {0} вече е предоставен.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=Еластичният изчислителен възел {0} вече е отстранен.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=Операцияте не е разрешена. Моля, спрете възела за еластични изчисления {0} и го стартирайто отново, за да актуализирате плана за размерите.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Процесът за премахване на еластичен изчислителен възел {0} е неуспешен.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=Текущото изпълнение на еластичния изчислителен възел {0} е изтекло.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=Проверката на статуса на еластичния изчислителен възел {0} се изпълнява...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=Генерирането на верига от задачи за еластичния изчислителен възел {0} е заключено, поради което веригата {1} може все още да се изпълнява.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=Изходният обект „{0}“.„{1}“ е тиражиран като зависимост на изгледа „{2}“.“„{3}“.
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Репликацията на таблица „{0}“.„{1}“ беше неуспешна, тъй като репликацията на редове от таблици е изведена от употреба.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Надвишен е максималният брой инстанции за еластичен изчислителен възел на SAP HANA Cloud.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=Операцията по изпълнението за еластичния изчислителен възел {0} все още е в ход.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Изтриването на копието на изходна таблица „{0}“ беше прекратено поради технически проблеми. Опитайте по-късно.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=Създаването на копие на изходна таблица „{0}“ беше прекратено поради технически проблеми. Опитайте по-късно.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Не може да се стартира еластичен изчислителен възел, тъй като лимитът за използване е достигнат или все още не са разпределени изчислителни блокчасове.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Зареждане на верига задачи и подготовка за изпълнение на общо {0} задачи, които са част от тази верига.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Вече се изпълнява конфликтна задача
#XMSG: Replication will change
txt_replication_change=Видът на репликацията ще се промени.
txt_repl_viewdetails=Преглед на подробните данни

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Изглежда се получи грешка при повторния опит на последното изпълнение, поради неуспешно предишно изпълнение на задачата, преди планът да бъде генериран.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=Пространство „{0}“ е заключено.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=Дейността {0} изисква да бъде разгърната локалната таблица {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=Дейността {0} изисква да бъде включено делта заснемането за локалната таблица {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=Дейността {0} изисква локалната таблица {1} да съхранява данни в архива на обекта.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=Дейността {0} изисква локалната таблица {1} да съхранява данни в базата данни.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Стартираме премахването на изтритите записи от локална таблица „{0}“.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Стартираме изтриването на всички записи от локална таблица „{0}“.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Стартираме изтриването на записи от локалната таблица {0} според условието за филтриране {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Възникна грешка при премахването на изтритите записи от локална таблица „{0}“.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Възникна грешка при изтриването на всички записи от локална таблица „{0}“.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Изтриваме всички напълно обработени записи с вид промяна „Изтрито“, по-стари от {0} дни.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Изтриваме всички напълно обработени записи с вид промяна „Изтрито“, по-стари от {0}.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS=Изтрити записи: {0}.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS= {0} записа са маркирани за изтриване.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=Премахването на изтритите записи от локална таблица „{0}“ завърши.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Изтриването на всички записи от локална таблица „{0}“ завърши.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Стартиране на маркирането на записи като „Изтрито” за локалната таблица „{0}”.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Стартираме маркирането на записи от локалната таблица {0} като „Изтрити“ според условието за филтриране {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Маркирането на записи като „Изтрито“ в локалната таблица „{0}” завърши.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Промените на данните се зареждат временно в таблицата {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Промените на данните са заредени временно в таблицата {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Промените на данните са обработени и изтрити от таблицата {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Подробни данни за връзката.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Стартиране на оптимизация на локалната таблица (файл).
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Възникна грешка при оптимизирането на локалната таблица (файл).
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Възникна грешка. Оптимизирането на локалната таблица (файл) е спряно.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Оптимизиране на локална таблица (файл)...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=Локалната таблица (файл) е оптимизирана.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=Локалната таблица (файл) е оптимизирана.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=Таблицата е оптимизирана с колоните на поръчка Z: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Възникна грешка. Съкращаването на локалната таблица (файл) е спряно.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Съкращаване на локална таблица (файл)...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=Местоположение на входящ буфер за локална таблица (файл) е отпаднало.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Стартиране на изчистване (изтриване на всички напълно обработени записи) на локалната таблица (файл).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Възникна грешка при изчистването на локалната таблица (файл).
#XMSG: Task log message
LTF_VACUUM_STOPPED=Възникна грешка. Изчистването на локалната таблица (файл) е спряно.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Изчистване на локална таблица (файл)...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=Изчистването е завършено.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Изтриваме всички напълно обработени записи, по-стари от {0} дни.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Изтриваме всички напълно обработени записи, по-стари от {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Стартиране на сливане на нови записи с локалната таблица (файл).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Стартиране на сливане на нови записи с локалната таблица (файл) с помощта на настройката „Автоматично сливане на данни“ .
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Стартиране на обединяване на нови записи с локалната таблица (файл). Задачата е инициирана от заявка за обединяване на API.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Сливане на нови записи с локалната таблица (файл).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Възникна грешка при сливането на нови записи с локална таблица (файл).
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=Локалната таблица (файл) е слята.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Възникна грешка. Сливането на локалната таблица (файл) е спряно.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=Сливането на локалната таблица (файл) е неуспешно поради грешка. Въпреки това операцията е частично успешна и някои от данните вече са слети.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Възникна грешка с таймаут. Изпълнението на дейност {0} е продължило {1} часа.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=Асинхронното изпълнение не беше стартирано заради високо натоварване на системата. Отворете „Системния монитор“ и проверете изпълняващите се задачи.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=Асинхронното изпълнение е отменено.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=Изпълнението на задачата {0} е в рамките на ограничението на паметта от {1} и {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=Задачата {0} е изпълнена с ИД на ресурса {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=Търсене и заместване е стартирано в локалната таблица (файл).
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Търсене и заместване е завършено в локалната таблица (файл).
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Търсене и заместване е неуспешно в локалната таблица (файл).

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Възникна грешка. Актуализацията на статистиката за локалната таблица (файл) е спряна.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=Задачата е неуспешна поради грешка за липса на памет в базата данни на SAP HANA.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=Задачата е неуспешна заради отхвърляне от контрола на приемане на SAP HANA.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=Задачата е неуспешна, защото има прекалено много активни връзки SAP HANA.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=Операцията „Повторен опит” не може да се изпълни, защото повторните опити са позволени само за висшестоящия елемент на вложена верига задачи.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Журналите на неуспешната нисшестояща задача вече не са налични, за да продължи повторният опит.


####Metrics Labels

performanceOptimized=Оптимизирано за производителност
memoryOptimized=Оптимизирано за памет

JOB_EXECUTION=Изпълнение на задания
EXECUTION_MODE=Режим на изпълнение
NUMBER_OF_RECORDS_OVERALL=Брой трайно съхранени записи
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Брой записи, прочетени от отдалечения източник
RUNTIME_MS_REMOTE_EXECUTION_TIME=Време за обработка на отдалечения източник
MEMORY_CONSUMPTION_GIB=Пиково натоварване на паметта в SAP HANA
NUMBER_OF_PARTITIONS=Брой дялове
MEMORY_CONSUMPTION_GIB_OVERALL=Пиково натоварване на паметта в SAP HANA
NUMBER_OF_PARTITIONS_LOCKED=Брой заключени дялове
PARTITIONING_COLUMN=Разделяща колона
HANA_PEAK_CPU_TIME=Общо време на CPU в SAP HANA
USED_IN_DISK=Използвано място за съхранение
INPUT_PARAMETER_PARAMETER_VALUE=Входен параметър
INPUT_PARAMETER=Входен параметър
ECN_ID=Име на еластичен изчислителен възел

DAC=Контроли за достъпа до данни
YES=Да
NO=Не
noofrecords=Брой записи
partitionpeakmemory=Пиково натоварване на паметта в SAP HANA
value=Стойност
metricsTitle=Метрики ({0})
partitionmetricsTitle=Дялове ({0})
partitionLabel=Дял
OthersNotNull=Стойности извън диапазоните
OthersNull=Стойности null
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Настройки, използвани при последното изпълнение на съхранението на данни:
#XMSG: Message for input parameter name
inputParameterLabel=Входен параметър
#XMSG: Message for input parameter value
inputParameterValueLabel=Стойност
#XMSG: Message for persisted data
inputParameterPersistedLabel=Час на трайно съхранение
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Изтриване на данните
REMOVE_DELETED_RECORDS=Премахване на изтритите записи
MERGE_FILES=Сливане на файлове
OPTIMIZE_FILES=Оптимизиране на файлове
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Преглед в монитора на свързващото приложение SAP BW

ANALYZE_PERFORMANCE=Анализ на производителността
CANCEL_ANALYZE_PERFORMANCE=Отказ на анализа на производителността

#XFLD: Label for frequency column
everyLabel=на всеки
#XFLD: Plural Recurrence text for Hour
hoursLabel=часа
#XFLD: Plural Recurrence text for Day
daysLabel=дни
#XFLD: Plural Recurrence text for Month
monthsLabel=месеца
#XFLD: Plural Recurrence text for Minutes
minutesLabel=минути

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Преглед на ръководството за отстраняване на проблеми със запазването на данни</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Процесът е с изчерпана памет. Невъзможно е да се запазят данните за изгледа „{0}“. Потърсете в Help Portal за повече информация относно грешки, свързани с изчерпване на паметта. В анализатора на изгледи може да проверите сложността на потреблението на памет на изгледа.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Неприложимо
OPEN_BRACKET=(
CLOSE_BRACKET=)
