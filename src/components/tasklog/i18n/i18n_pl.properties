
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Szczegóły logu
#XFLD: Header
TASK_LOGS=<PERSON><PERSON> ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Przebiegi ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Wyświetl szczegóły
#XFLD: Button text
STOP=Zatrzymaj przebieg
#XFLD: Label text
RUN_START=Rozpoczęcie ostatniego przebiegu
#XFLD: Label text
RUN_END=Zakończenie ostatniego przebiegu
#XFLD: Label text
RUNTIME=Czas trwania
#XTIT: Count for Messages
txtDetailMessages=Komunikaty ({0})
#XFLD: Label text
TIME=Znacznik czasu
#XFLD: Label text
MESSAGE=Komunikat
#XFLD: Label text
TASK_STATUS=Kategoria
#XFLD: Label text
TASK_ACTIVITY=Działanie
#XFLD: Label text
RUN_START_DETAILS=Początek
#XFLD: Label text
RUN_END_DETAILS=Koniec
#XFLD: Label text
LOGS=Przebiegi
#XFLD: Label text
STATUS=Status
#XFLD: Label text
RUN_STATUS=Status uruchomienia
#XFLD: Label text
Runtime=Czas trwania
#XFLD: Label text
RuntimeTooltip=Czas trwania (gg : mm : ss)
#XFLD: Label text
TRIGGEREDBY=Wyzwolone przez
#XFLD: Label text
TRIGGEREDBYNew=Uruchomione przez
#XFLD: Label text
TRIGGEREDBYNewImp=Przebieg uruchomiony przez
#XFLD: Label text
EXECUTIONTYPE=Typ wykonania
#XFLD: Label text
EXECUTIONTYPENew=Typ przebiegu
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Przestrzeń sieci nadrzędnej
#XFLD: Refresh tooltip
TEXT_REFRESH=Odśwież
#XFLD: view Details link
VIEW_ERROR_DETAILS=Wyświetl szczegóły
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Pobierz dodatkowe szczegóły
#XMSG: Download completed
downloadStarted=Rozpoczęto pobieranie
#XMSG: Error while downloading content
errorInDownload=Wystąpił błąd podczas pobierania.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Wyświetl szczegóły
#XBTN: cancel button of task details dialog
TXT_CANCEL=Anuluj
#XBTN: back button from task details
TXT_BACK=Wstecz
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Zadanie zakończone
#XFLD: Log message with failed status
MSG_LOG_FAILED=Zadanie nie powiodło się
#XFLD: Master and detail table with no data
No_Data=Brak danych
#XFLD: Retry tooltip
TEXT_RETRY=Spróbuj ponownie
#XFLD: Cancel Run label
TEXT_CancelRun=Anuluj przebieg
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Wyczyść nieudane wczytanie
#XMSG:button copy sql statement
txtSQLStatement=Kopiuj instrukcję SQL
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Otwórz monitor zdalnego zapytania
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=Aby wyświetlić zdalne instrukcje SQL, kliknij opcję "Otwórz monitor zdalnego zapytania".
#XMSG:button ok
txtOk=OK
#XMSG: button close
txtClose=Zamknij
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=Rozpoczęto czynność anulowania przebiegu dla obiektu "{0}".
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=Czynność anulowania przebiegu dla obiektu "{0}" nie powiodła się.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=Czynność anulowania przebiegu dla obiektu "{0}" nie jest już możliwa, ponieważ status replikacji uległ zmianie.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Brak logów zadań o statusie Aktywne.
#XMSG: message for conflicting task
Task_Already_Running=Niezgodne zadanie jest już uruchomione dla obiektu "{0}".
#XFLD: Label for no task log with running state title
actionInfo=Informacja dotycząca czynności
#XMSG Copied to clipboard
copiedToClip=Skopiowano do schowka
#XFLD copy
Copy=Kopiuj
#XFLD copy correlation ID
CopyCorrelationID=Skopiuj ID korelacji
#XFLD Close
Close=Zamknij
#XFLD: show more Label
txtShowMore=Pokaż więcej
#XFLD: message Label
messageLabel=Komunikat:
#XFLD: details Label
detailsLabel=Szczegóły:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Wykonująca instrukcja \r\n SQL:
#XFLD:statementId Label
statementIdLabel=ID instrukcji:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Liczba zdalnych \r\n instrukcji SQL:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Liczba instrukcji
#XFLD: Space Label
txtSpaces=Przestrzeń
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Nieautoryzowane przestrzenie
#XFLD: Privilege Error Text
txtPrivilegeError=Nie masz wystarczających uprawnień do wyświetlenia tych danych.
#XFLD: Label for Object Header
DATA_ACCESS=Dostęp do danych
#XFLD: Label for Object Header
SCHEDULE=Harmonogram
#XFLD: Label for Object Header
DETAILS=Szczegóły
#XFLD: Label for Object Header
LATEST_UPDATE=Ostatnia aktualizacja
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Ostatnia zmiana (źródło)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Częstotliwość odświeżania
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Zaplanowana częstotliwość
#XFLD: Label for Object Header
NEXT_RUN=Następny przebieg
#XFLD: Label for Object Header
CONNECTION=Połączenie
#XFLD: Label for Object Header
DP_AGENT=Agent DP
#XFLD: Label for Object Header
USED_IN_MEMORY=Pamięć wykorzystana do przechowywania (MiB)
#XFLD: Label for Object Header
USED_DISK=Dysk wykorzystany do przechowywania (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Rozmiar pamięci in-memory (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Rozmiar dysku (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Liczba rekordów

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Ustaw na Niepowodzenie
SET_TO_FAILED_ERR=To zadanie było w toku, ale użytkownik ustawił status tego zadania na NIEPOWODZENIE.
#XFLD: Label for stopped failed
FAILLOCKED=Przebieg już w toku
#XFLD: sub status STOPPED
STOPPED=Zatrzymane
STOPPED_ERR=To zadanie zostało zatrzymane, ale nie wykonano cofania zmian.
#XFLD: sub status CANCELLED
CANCELLED=Anulowane
CANCELLED_ERR=Ten przebieg zadania został anulowany po rozpoczęciu. W takim przypadku cofnięto zmiany danych i przywrócono je do stanu sprzed wyzwolenia przebiegu zadania.
#XFLD: sub status LOCKED
LOCKED=Zablokowane
LOCKED_ERR=To samo zadanie było już w toku i nie można wykonać tego zadania równolegle z istniejącym wykonaniem zadania.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Wyjątek zadania
TASK_EXCEPTION_ERR=To zadanie napotkało nieokreślony błąd podczas wykonania.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Wyjątek wykonania zadania
TASK_EXECUTE_EXCEPTION_ERR=Podczas wykonania zadania wystąpił błąd.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Nieuprawnione
UNAUTHORIZED_ERR=Nie można było uwierzytelnić użytkownika, ponieważ został zablokowany lub usunięty.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Zabronione
FORBIDDEN_ERR=Przypisany użytkownik nie ma uprawnień wymaganych do wykonania tego zadania.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Niewyzwolone
FAIL_NOT_TRIGGERED_ERR=Nie można było wykonać tego zadania z powodu awarii systemu lub niedostępności części systemu bazy danych w czasie planowanego wykonania. Poczekaj do kolejnego zaplanowanego czasu wykonania zadania lub ponownie zaplanuj zadanie.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Anulowano planowanie
SCHEDULE_CANCELLED_ERR=Nie można było wykonać tego zadania z powodu błędu wewnętrznego. Skontaktuj się z działem wsparcia SAP i prześlij ID korelacji oraz znacznik czasi ze szczegółowych informacji logu zadania.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Poprzedni przebieg jest w toku
SUCCESS_SKIPPED_ERR=Nie wyzwolono wykonania tego zadania, ponieważ wcześniejszy przebieg tego samego zadania jest nadal w toku.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Brak osoby odpowiedzialnej
FAIL_OWNER_MISSING_ERR=Nie można było wykonać tego zadania, ponieważ nie ma przypisanego użytkownika systemu. Przypisz odpowiedzialnego użytkownika do zadania.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Zgoda niedostępna
FAIL_CONSENT_NOT_AVAILABLE_ERR=SAP nie ma uprawnień do wykonania łańcuchów zadań lub planowania zadań integracji danych w Twoim imieniu. Zaznacz udostępnioną opcję, aby udzielić zgody.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Zgoda wygasła
FAIL_CONSENT_EXPIRED_ERR=Wygasło uprawnienie, które umożliwia SAP wykonywanie łańcuchów zadań lub planowanie zadań integracji danych w Twoim imieniu. Zaznacz udostępnioną opcję, aby odnowić swoją zgodę.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Wycofano zgodę
FAIL_CONSENT_INVALIDATED_ERR=Nie można było wykonać tego zadania z powodu zmiany konfiguracji dostawcy tożsamości tenanta. W takim przypadku nie można wykonać ani zaplanować nowych zadań w imieniu danego użytkownika. Jeśli przypisany użytkownik nadal istnieje u nowego dostawcy tożsamości, wycofaj zgodę na planowanie i udziel jej ponownie. Jeśli przypisany użytkownik już nie istnieje, przypisz nową osobę odpowiedzialną za zadanie i udziel niezbędnej zgody na planowanie zadań. Szczegółowe informacje zawiera nota SAP: https://launchpad.support.sap.com/#/notes/3089828.
TASK_EXECUTOR_ERROR=Wykonawca zadania
TASK_EXECUTOR_ERROR_ERR=To zadanie napotkało wewnętrzny błąd prawdopodobnie podczas kroków przygotowania wykonania i nie można było go rozpocząć.
PREREQ_NOT_MET=Nie spełniono warunku wstępnego
PREREQ_NOT_MET_ERR=Nie można było wykonać tego zadania z powodu problemów w jego definicji. Na przykład obiekt nie został rozmieszczony, łańcuch zadań zawiera logikę cykliczną lub SQL dla widoku jest nieprawidłowy.
RESOURCE_LIMIT_ERROR=Błąd limitu zasobów
RESOURCE_LIMIT_ERROR_ERR=Obecnie nie można wykonać zadania, ponieważ odpowiednie zasoby są niedostępne lub zajęte.
FAIL_CONSENT_REFUSED_BY_UMS=Odmówiono zgody
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Nie można wykonać tego zadania, w zaplanowanych przebiegach lub łańcuchach zadań, z powodu zmiany konfiguracji dostawcy tożsamości użytkownika dla dzierżawcy. Dodatkowe informacje można znaleźć w następującej nocie SAP: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Zaplanowane
#XFLD: status text
SCHEDULEDNew=Stałe
#XFLD: status text
PAUSED=Wstrzymane
#XFLD: status text
DIRECT=Bezpośrednie
#XFLD: status text
MANUAL=Ręczne
#XFLD: status text
DIRECTNew=Proste
#XFLD: status text
COMPLETED=Zakończone
#XFLD: status text
FAILED=Niepowodzenie
#XFLD: status text
RUNNING=Aktywne
#XFLD: status text
none=Brak
#XFLD: status text
realtime=W czasie rzeczywistym
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Zadanie podrzędne
#XFLD: New Data available in the file
NEW_DATA=Nowe dane

#XFLD: text for values shown in column Replication Status
txtOff=Wyłączone
#XFLD: text for values shown in column Replication Status
txtInitializing=Inicjalizacja
#XFLD: text for values shown in column Replication Status
txtLoading=Wczytywanie
#XFLD: text for values shown in column Replication Status
txtActive=Aktywne
#XFLD: text for values shown in column Replication Status
txtAvailable=Dostępne
#XFLD: text for values shown in column Replication Status
txtError=Błąd
#XFLD: text for values shown in column Replication Status
txtPaused=Wstrzymano
#XFLD: text for values shown in column Replication Status
txtDisconnected=Rozłączono
#XFLD: text for partially Persisted views
partiallyPersisted=Utrwalono częściowo

#XFLD: activity text
REPLICATE=Replikuj
#XFLD: activity text
REMOVE_REPLICATED_DATA=Usuń zreplikowane dane
#XFLD: activity text
DISABLE_REALTIME=Wyłącz replikację danych w czasie rzeczywistym
#XFLD: activity text
REMOVE_PERSISTED_DATA=Usuń utrwalone dane
#XFLD: activity text
PERSIST=Utrwal
#XFLD: activity text
EXECUTE=Wykonaj
#XFLD: activity text
TASKLOG_CLEANUP=Czyszczenie_Tacklog
#XFLD: activity text
CANCEL_REPLICATION=Anuluj replikację
#XFLD: activity text
MODEL_IMPORT=Import modelu
#XFLD: activity text
NONE=Brak
#XFLD: activity text
CANCEL_PERSISTENCY=Anuluj utrwalenie
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Analizuj widok
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Anuluj analizatora widoków

#XFLD: severity text
INFORMATION=Informacje
#XFLD: severity text
SUCCESS=Powodzenie
#XFLD: severity text
WARNING=Ostrzeżenie
#XFLD: severity text
ERROR=Błąd
#XFLD: text for values shown for Ascending sort order
SortInAsc=Sortuj rosnąco
#XFLD: text for values shown for Descending sort order
SortInDesc=Sortuj malejąco
#XFLD: filter text for task log columns
Filter=Filtruj
#XFLD: object text for task log columns
Object=Obiekt
#XFLD: space text for task log columns
crossSpace=Przestrzeń

#XBUT: label for remote data access
REMOTE=Zdalne
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Zreplikowane (w czasie rzeczywistym)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Zreplikowane (migawka)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=Replikacja w czasie rzeczywistym została zablokowana z powodu błędu. Po skorygowaniu błędu możesz użyć czynności “Spróbuj ponownie”, aby wznowić replikację w czasie rzeczywistym.
ERROR_MSG=Replikacja w czasie rzeczywistym została zablokowana z powodu błędu.
RETRY_FAILED_ERROR=Wystąpił błąd podczas ponownej próby procesu.
LOG_INFO_DETAILS=Gdy dane replikuje się w trybie czasu rzeczywistego logi nie są generowane. Wyświetlone logi dotyczą wcześniejszych czynności.

#XBUT: Partitioning label
partitionMenuText=Partycjonowanie
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Utwórz partycję
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Edytuj partycję
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Usuń partycję
#XFLD: Initial text
InitialPartitionText=Zdefiniuj partycje, określając kryteria podziału dużych zbiorów danych na mniejsze zbiory.
DefinePartition=Zdefiniuj partycje
#XFLD: Message text
partitionChangedInfo=Definicja partycji zmieniła się od ostatniej replikacji. Zmiany zostaną zastosowane przy kolejnym ładowaniu danych.
#XFLD: Message text
REAL_TIME_WARNING=Partycjonowanie jest stosowane tylko podczas wczytywania nowej migawki. Nie jest stosowane dla replikacji w czasie rzeczywistym.
#XFLD: Message text
loadSelectedPartitions=Rozpoczęto utrwalanie danych dla wybranych partycji "{0}"
#XFLD: Message text
loadSelectedPartitionsError=Utrwalanie danych dla wybranych partycji "{0}" nie powiodło się
#XFLD: Message text
viewpartitionChangedInfo=Definicja partycji uległa zmianie od ostatniego przebiegu utrwalania. Aby zastosować zmiany, kolejne wczytywanie danych będzie pełną migawką uwzględniającą zablokowane partycje. Po zakończeniu pełnego wczytania możliwe będzie uruchomienie danych dla poszczególnych partycji.
#XFLD: Message text
viewpartitionChangedInfoLocked=Definicja partycji uległa zmianie od ostatniego przebiegu utrwalania. Aby zastosować zmiany, kolejne wczytywanie danych będzie pełną migawką z wyjątkiem zablokowanych lub niezmienionych zakresów partycji. Po zakończeniu tego wczytania możliwe będzie ponowne wczytanie wybranych partycji.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Replikacja tabeli
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Replikacja harmonogramu
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Utwórz harmonogram
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Edytuj harmonogram
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Usuń harmonogram
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Załaduj nową migawkę
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Rozpocznij replikację danych
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Usuń zreplikowane dane
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Włącz dostęp w czasie rzeczywistym
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Włącz replikację danych czasu w czasie rzeczywistym
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Wyłącz replikację danych w czasie rzeczywistym
#XFLD: Message for replicate table action
replicateTableText=Replikacja tabeli
#XFLD: Message for replicate table action
replicateTableTextNew=Replikacja danych
#XFLD: Message to schedule task
scheduleText=Zaplanuj
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Utrwalenie widoku
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Trwałość danych
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Załaduj nową migawkę
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Rozpocznij utrwalanie danych
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Usuń utrwalone dane
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Podzielone przetwarzanie
#XBUT: Label for scheduled replication
scheduledTxt=Zaplanowane
#XBUT: Label for statistics button
statisticsTxt=Statystyka
#XBUT: Label for create statistics
createStatsTxt=Utwórz statystykę
#XBUT: Label for edit statistics
editStatsTxt=Edytuj statystykę
#XBUT: Label for refresh statistics
refreshStatsTxt=Odśwież statystykę
#XBUT: Label for delete statistics
dropStatsTxt=Usuń statystykę
#XMSG: Create statistics success message
statsSuccessTxt=Rozpoczęto tworzenie statystyki typu {0} dla {1}.
#XMSG: Edit statistics success message
statsEditSuccessTxt=Rozpoczęto zmienianie rodzaju statystyki na {0} dla {1}.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Rozpoczęto odświeżanie statystyki dla {0}.
#XMSG: Drop statistics success message
statsDropSuccessTxt=Pomyślnie usunięto statystykę dla {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=Błąd podczas tworzenia statystyki
#XMSG: Edit statistics error message
statsEditErrorTxt=Błąd podczas zmiany statystyki
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Błąd podczas odświeżania statystyki
#XMSG: Drop statistics error message
statsDropErrorTxt=Błąd podczas usuwania statystyki
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Czy na pewno chcesz usunąć statystykę danych?
startPersistencyAdvisorLabel=Uruchom analizator widoków

#Partition related texts
#XFLD: Label for Column
column=Kolumna
#XFLD: Label for No of Partition
noOfPartitions=Liczba partycji
#XFLD: Label for Column
noOfParallelProcess=Liczba równoległych procesów
#XFLD: Label text
noOfLockedPartition=Liczba zablokowanych partycji
#XFLD: Label for Partition
PARTITION=Partycje
#XFLD: Label for Column
AVAILABLE=Dostępne
#XFLD: Statistics Label
statsLabel=Statystyka
#XFLD: Label text
COLUMN=Kolumna:
#XFLD: Label text
PARALLEL_PROCESSES=Procesy równoległe:
#XFLD: Label text
Partition_Range=Zakres partycji
#XFLD: Label text
Name=Nazwa
#XFLD: Label text
Locked=Zablokowane
#XFLD: Label text
Others=POZOSTAŁE
#XFLD: Label text
Delete=Usuń
#XFLD: Label text
LoadData=Wczytaj wybrane partycje
#XFLD: Label text
LoadSelectedData=Wczytaj wybrane partycje
#XFLD: Confirmation text
LoadNewPersistenceConfirm=To spowoduje wczytanie nowej migawki dla wszystkich odblokowanych i zmienionych partycji, nie tylko tych wybranych. Czy chcesz kontynuować?
#XFLD: Label text
Continue=Kontynuuj

#XFLD: Label text
PARTITIONS=Partycje
#XFLD: Label text
ADD_PARTITIONS=+ Dodaj partycję
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Dodaj partycje
#XFLD: Label text
deleteRange=Usuń partycję
#XFLD: Label text
LOW_PLACE_HOLDER=Wprowadź niską wartość
#XFLD: Label text
HIGH_PLACE_HOLDER=Wprowadź wysoką wartość
#XFLD: tooltip text
lockedTooltip=Zablokuj partycję po pobraniu danych początkowych

#XFLD: Button text
Edit=Edytuj
#XFLD: Button text
CANCEL=Anuluj

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Ostatnia aktualizacja statystyki
#XFLD: Statistics Fields
STATISTICS=Statystyka

#XFLD:Retry label
TEXT_Retry=Spróbuj ponownie
#XFLD:Retry label
TEXT_Retry_tooltip=Po usunięciu błędu ponów próbę wykonania replikacji w czasie rzeczywistym.
#XFLD: text retry
Retry=Potwierdź
#XMG: Retry confirmation text
retryConfirmationTxt=Ostatnia replikacja w czasie rzeczywistym została zakończona z błędem.\n Potwierdź, że skorygowano błąd i można ponownie uruchomić replikację.
#XMG: Retry success text
retrySuccess=Pomyślnie zainicjowano ponowienie próby wykonania.
#XMG: Retry fail text
retryFail=Ponowienie próby nie powiodło się.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Utwórz statystykę
#XMSG: activity message for edit statistics
DROP_STATISTICS=Usuń statystykę
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Odśwież statystykę
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Edytuj statystykę
#XMSG: Task log message started task
taskStarted=Zadanie {0} zostało rozpoczęte.
#XMSG: Task log message for finished task
taskFinished=Zadanie {0} zostało zakończone ze statusem {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=Zadanie {0} zakończono o {2} ze statusem {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=Zadanie {0} ma parametry wejściowe.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=Zadanie {0} zostało zakończone z nieoczekiwanym błędem. Status zadania ustawiono na {1}.
#XMSG: Task log message for failed task
failedToEnd=Ustawianie statusu na {0} nie powiodło się lub błąd usuwania blokady.
#XMSG: Task log message
lockNotFound=Nie można sfinalizować procesu z powodu braku blokady: Zadanie mogło zostać anulowane.
#XMSG: Task log message failed task
failedOverwrite=Zadanie {0} jest już zablokowane przez użytkownika {1} i nie powiodło się z następującym błędem: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Blokada tego zadania została przejęta przez inne zadanie.
#XMSG: Task log message failed takeover
failedTakeover=Błąd przejmowania istniejącego zadania.
#XMSG: Task log message successful takeover
successTakeover=Pozostała blokada musiała zostać zwolniona. Ustawiono nową blokadę dla tego zadania.
#XMSG: Tasklog Dialog Details
txtDetails=Zdalne instrukcje przetworzone podczas przebiegu można wyświetlić poprzez otwarcie monitora zdalnego zapytania, w szczegółach komunikatów dotyczących partycji.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=Zdalne instrukcje SQL zostały usunięte z bazy danych i nie można ich wyświetlić.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Nie można wyświetlić zdalnych zapytań, które mają połączenia przypisane do innych przestrzeni. Przejdź do monitora zdalnego zapytania i użyj ID instrukcji, aby je odfiltrować.
#XMSG: Task log message for parallel check error
parallelCheckError=Nie można przetworzyć zadania, ponieważ jest blokowane przez inne zadanie w toku.
#XMSG: Task log message for parallel running task
parallelTaskRunning=Niezgodne zadanie jest już uruchomione.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} podczas przebiegu z ID korelacji {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=Przypisany użytkownik nie ma uprawnień wymaganych do wykonania tego zadania.

#XBUT: Label for open in Editor
openInEditor=Otwórz w edytorze
#XBUT: Label for open in Editor
openInEditorNew=Otwórz w edytorze danych
#XFLD:Run deails label
runDetails=Szczegóły przebiegu
#XFLD: Label for Logs
Logs=Logi
#XFLD: Label for Settings
Settings=Ustawienia
#XFLD: Label for Save button
Save=Zapisz
#XFLD: Label for Standard
Standard_PO=Zoptymalizowane pod kątem wydajności (zalecane)
#XFLD: Label for Hana low memory processing
HLMP_MO=Zoptymalizowane pod kątem pamięci
#XFLD: Label for execution mode
ExecutionMode=Tryb wykonania
#XFLD: Label for job execution
jobExecution=Tryb przetwarzania
#XFLD: Label for Synchronous
syncExec=Synchroniczne
#XFLD: Label for Asynchronous
asyncExec=Asynchroniczne
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Użyj trybu domyślnego (asynchronicznego; w przyszłości może się to zmienić)
#XMSG: Save settings success
saveSettingsSuccess=Zmieniono tryb wykonywania SAP HANA.
#XMSG: Save settings failure
saveSettingsFailed=Zmiana trybu wykonywania SAP HANA nie powiodła się.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Zmieniono wykonanie zadania.
#XMSG: Job Execution change failed
jobExecSettingFailed=Zmiana wykonania zadania nie powiodła się.
#XMSG: Text for Type
typeTxt=Typ
#XMSG: Text for Monitor
monitorTxt=Monitor
#XMSG: Text for activity
activityTxt=Działanie
#XMSG: Text for metrics
metricsTxt=Metryki
#XTXT: Text for Task chain key
TASK_CHAINS=Łańcuch zadań
#XTXT: Text for View Key
VIEWS=Widok
#XTXT: Text for remote table key
REMOTE_TABLES=Tabela zdalna
#XTXT: Text for Space key
SPACE=Przestrzeń
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Elastyczny węzeł obliczeniowy
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Przepływ replikacji
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Inteligentne wyszukiwanie
#XTXT: Text for Local Table
LOCAL_TABLE=Tabela lokalna
#XTXT: Text for Data flow key
DATA_FLOWS=Przepływ danych
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=Procedura skryptu SQL
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=Łańcuch procesów BW
#XTXT: Text for API
API=Interfejs API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Wyświetl w monitorze
#XTXT: Task List header text
taskListHeader=Lista zadań ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Nie można pobrać mierników dla historycznych przebiegów przepływu danych.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=Szczegóły dot. zakończonego wykonania obecnie nie wczytują się. Spróbuj odświeżyć.
#XFLD: Label text for the Metrices table header
metricesColLabel=Etykieta operatora
#XFLD: Label text for the Metrices table header
metricesType=Typ
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Liczba rekordów
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Uruchom łańcuch zadań
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Łańcuch zadań został uruchomiony.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Łańcuch zadań został uruchomiony dla {0}.
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Uruchomienie łańcucha zadań nie powiodło się.
#XTXT: Execute button label
runLabel=Przebieg
#XTXT: Execute button label
runLabelNew=Rozpocznij przebieg
#XMSG: Filter Object header
chainsFilteredTableHeader=Filtrowanie według obiektu: {0}
#XFLD: Parent task chain label
parentChainLabel=Łańcuch zadań nadrzędnych:
#XFLD: Parent task chain unauthorized
Unauthorized=Brak uprawnień do wyświetlania
#XFLD: Parent task chain label
parentTaskLabel=Zadanie nadrzędne:
#XTXT: Task status
NOT_TRIGGERED=Niewyzwolone
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Otwórz tryb pełnego ekranu
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Zamknij tryb pełnego ekranu
#XTXT: Close Task log details right panel
closeRightColumn=Zamknij sekcję
#XTXT: Sort Text
sortTxt=Sortuj
#XTXT: Filter Text
filterTxt=Filtruj
#XTXT: Filter by text to show list of filters applied
filterByTxt=Filtruj wg
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Ponad 5 minut
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Ponad 15 minut
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Ponad godzina
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Ostatnia godzina
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Ostatnie 24 godziny
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Ostatni miesiąc
#XTXT: Messages title text
messagesText=Komunikaty

#XTXT Statistics information message
statisticsInfo=Nie można utworzyć statystyki dla tabel zdalnych z dostępem do danych "Zreplikowane". Aby utworzyć statystykę, usuń zreplikowane dane w monitorze szczegółów tabel zdalnych.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Przejdź do monitora szczegółów tabel zdalnych

#XTXT: Repair latest failed run label
retryRunLabel=Ponów ostatni przebieg
#XTXT: Repair failed run label
retryRun=Ponów przebieg
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Rozpoczęto przebieg ponownej próby łańcucha
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Przebieg ponownej próby łańcucha nie powiódł się
#XMSG: Task chain child elements name
taskChainRetryChildObject=Zadanie {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Nowe zadanie
#XFLD Analyzed View
analyzedView=Widok po analizie
#XFLD Metrics
Metrics=Metryki
#XFLD Partition Metrics
PartitionMetrics=Metryki partycji
#XFLD Entities
Messages=Log zadania
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Nie zdefiniowano jeszcze partycji
#XTXT: Description message for empty partition data
partitionEmptyDescText=Utwórz partycje, określając kryteria umożliwiające podział większych wolumenów danych na mniejsze części, którymi łatwiej zarządzać.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Nie ma jeszcze dostępnych logów
#XTXT: Description message for empty runs data
runsEmptyDescText=Gdy rozpoczniesz nowe działanie (Załaduj nową migawkę, Uruchom analizatora widoku...), powiązane logi będą widoczne tutaj.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=Konfiguracja elastycznego węzła obliczeniowego {0} nie jest odpowiednio opracowana. Sprawdź swoją definicję.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Proces dodawania elastycznego węzła obliczeniowego {0} zakończył się niepowodzeniem.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Rozpoczęto tworzenie i aktywację repliki dla obiektu źródłowego "{0}"."{1}" w elastycznym węźle obliczeniowym {2}.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Rozpoczęto usuwanie repliki dla obiektu źródłowego "{0}"."{1}" z elastycznego węzła obliczeniowego {2}.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Tworzenie i aktywacja repliki dla obiektu źródłowego "{0}"."{1}" nie powiodły się.
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Usuwanie repliki dla obiektu źródłowego "{0}"."{1}" nie powiodło się.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Rozpoczęto routing obliczenia zapytań analitycznych przestrzeni {0} do elastycznego węzła obliczeniowego {1}.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Kierowanie obliczenia zapytań analitycznych przestrzeni {0} do powiązanego elastycznego węzła obliczeniowego zakończył się niepowodzeniem.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Rozpoczęto przekierowanie obliczenia zapytań analitycznych przestrzeni {0} z elastycznego węzła obliczeniowego {1}.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Przekierowanie obliczenia zapytań analitycznych przestrzeni {0} do koordynatora zakończyło się niepowodzeniem.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Wyzwolono łańcuch zadań {0} do powiązanego elastycznego węzła obliczeniowego {1}.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Generowanie łańcucha zadań dla elastycznego węzła obliczeniowego {0} zakończyło się niepowodzeniem.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=Dostarczanie elastycznego węzła obliczeniowego {0} zostało rozpoczęte z następującym planem określania rozmiaru: vCPU: {1}, pamięć (GiB): {2} i rozmiar miejsca na dysku (GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=Już dostarczono dane elastycznego węzła obliczeniowego {0}.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=Już usunięto dane elastycznego węzła obliczeniowego {0}.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=Operacja nie jest dozwolona. Zatrzymaj elastyczny węzeł obliczeniowy {0}, a następnie uruchom go ponownie, aby zaktualizować plan określania rozmiaru.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Proces usuwania elastycznego węzła obliczeniowego {0} nie powiódł się.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=Aktualny przebieg elastycznego węzła obliczeniowego {0} przekroczył limit czasu.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=Kontrola statusu elastycznego węzła obliczeniowego {0} w toku...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=Generowanie łańcucha zadań dla elastycznego węzła obliczeniowego {0} jest zablokowane, dlatego łańcuch {1} może nadal być wykonywany.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=Obiekt źródłowy "{0}"."{1}" jest replikowany jako zależność widoku "{2}"."{3}".
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Nie można replikować tabeli "{0}"."{1}", ponieważ replikacja tabeli wiersza nie jest już zalecana.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Przekroczono maksymalną liczbę elastycznych węzłów obliczeniowych na instancję SAP HANA Cloud.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=Bieżąca operacja dla elastycznego węzła obliczeniowego {0} jest nadal w toku.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Usuwanie repliki dla tabeli źródłowej {0} zostało zatrzymane z powodu problemów technicznych. Spróbuj ponownie później.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=Tworzenie repliki dla tabeli źródłowej {0} zostało zatrzymane z powodu problemów technicznych. Spróbuj ponownie później.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Nie można uruchomić elastycznego węzła obliczeniowego, ponieważ osiągnięto limit wykorzystania lub nie przypisano jeszcze godzin bloku obliczania.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Wczytywanie łańcucha zadań i przygotowanie do uruchomienia łącznie {0} zadań należących do tego łańcucha.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Niezgodne zadanie jest już uruchomione
#XMSG: Replication will change
txt_replication_change=Typ replikacji zostanie zmieniony.
txt_repl_viewdetails=Wyświetl szczegóły

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Wygląda na to, że wystąpił błąd z ponowieniem ostatniego przebiegu, ponieważ poprzedni przebieg zadania nie powiódł się przed wygenerowaniem planu.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=Przestrzeń "{0}" jest zablokowana.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=Działanie {0} wymaga wdrożenia lokalnej tabeli {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=Działanie {0}wymaga włączenia rejestrowania delty dla lokalnej tabeli {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=Działanie {0} wymaga lokalnej tabeli {1} do przechowywania danych w magazynie obiektów.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=Działanie {0} wymaga lokalnej tabeli {1} do przechowywania danych w bazie danych.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Rozpoczynanie usuwania wcześniej usuniętych rekordów dla tabeli lokalnej {0}.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Rozpoczynanie usuwania wszystkich rekordów dla tabeli lokalnej {0}.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Rozpoczynanie usuwania rekordów dla tabeli lokalnej {0} zgodnie z warunkiem filtra {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Wystąpił błąd podczas usuwania wcześniej usuniętych rekordów dla tabeli lokalnej {0}.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Wystąpił błąd podczas usuwania wszystkich rekordów dla tabeli lokalnej {0}.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Usuwanie wszystkich całkowicie przetworzonych rekordów z typem zmiany "Usunięte", które są starsze niż {0} dni.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Usuwanie wszystkich całkowicie przetworzonych rekordów z typem zmiany "Usunięte", które są starsze niż {0}.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS=Usunięto rekordy: {0}.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS=Rekordy oznaczone do usunięcia: {0}.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=Usuwanie wcześniej usuniętych rekordów dla tabeli lokalnej {0} zostało zakończone.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Usuwanie wszystkich rekordów dla tabeli lokalnej {0} zostało zakończone.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Rozpoczynanie oznaczania rekordów jako "Usunięte" dla tabeli lokalnej {0}.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Rozpoczynanie oznaczania rekordów jako "Usunięte" dla tabeli lokalnej {0} zgodnie z warunkiem filtra  {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Oznaczanie rekordów jako "Usunięte" dla tabeli lokalnej {0} zostało zakończone.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Zmiany danych są tymczasowo wczytywane do tabeli {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Zmiany danych zostały tymczasowo wczytane do tabeli {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Zmiany danych zostały przetworzone i usunięte z tabeli {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Szczegóły połączenia.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Inicjowanie optymalizacji tabeli lokalnej (plik).
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Wystąpił błąd podczas optymalizacji tabeli lokalnej (plik).
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Wystąpił błąd. Optymalizacja tabeli lokalnej (plik) została zatrzymana.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Optymalizacja tabeli lokalnej (plik)...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=Tabela lokalna (plik) została zoptymalizowana.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=Tabela lokalna (plik) jest zoptymalizowana.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=Tabela została zoptymalizowana z użyciem kolumn Z-Order: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Wystąpił błąd. Obcinanie tabeli lokalnej (plik) zostało zatrzymane.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Obcinanie tabeli lokalnej (plik)...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=Wejściowa lokalizacja bufora dla tabeli lokalnej (plik) została usunięta.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Inicjowanie czyszczenia (usuwania wszystkich w pełni przetworzonych rekordów) tabeli lokalnej (plik).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Wystąpił błąd podczas czyszczenia tabeli lokalnej (plik).
#XMSG: Task log message
LTF_VACUUM_STOPPED=Wystąpił błąd. Czyszczenie tabeli lokalnej (plik) zostało zatrzymane.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Czyszczenie tabeli lokalnej (plik)...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=Czyszczenie zostało zakończone.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Usuwanie wszystkich całkowicie przetworzonych rekordów, które są starsze niż {0} dni.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Usuwanie wszystkich całkowicie przetworzonych rekordów, które są starsze niż {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Inicjowanie scalania nowych rekordów z tabelą lokalną (plik).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Inicjowanie scalania nowych rekordów z tabelą lokalną (plik) za pomocą ustawienia "Połącz dane automatycznie".
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Inicjowanie scalania nowych rekordów z tabelą lokalną (plik). To zadanie zostało zainicjowane przez interfejs API Żądanie scalania.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Scalanie nowych rekordów z tabelą lokalną (plik).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Wystąpił błąd podczas scalania nowych rekordów z tabelą lokalną (plik).
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=Tabela lokalna (plik) jest scalona.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Wystąpił błąd. Scalenie tabeli lokalnej (plik) zostało zatrzymane.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=Scalanie tabeli lokalnej (pliku) nie powiodło się z powodu błędu, ale operacja częściowo się udała i niektóre dane zostały scalone.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Wystąpił błąd przekroczenia limitu czasu. Działanie {0} jest uruchomione od {1} godzin.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=Nie można było rozpocząć wykonania asynchronicznego z powodu dużego obciążenia systemu. Otwórz ''Monitor systemu'' i sprawdź uruchomione zadania.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=Wykonanie asynchroniczne zostało anulowane.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=Przebieg zadania {0} mieścił się w ramach limitów {1} oraz {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=Zadanie {0} zostało uruchomione z ID zasobu {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=Rozpoczęto operację Znajdź i zamień w tabeli lokalnej (plik).
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Zakończono operację Znajdź i zamień w tabeli lokalnej (plik).
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Operacja Znajdź i zamień nie powiodła się w tabeli lokalnej (plik).

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Wystąpił błąd. Aktualizacja statystyki tabeli lokalnej (plik) została zatrzymana.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=Zadanie nie powiodło się z powodu wyczerpania pamięci bazy danych SAP HANA.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=Zadanie nie powiodło się z powodu odrzucenia kontroli przyjęcia SAP HANA.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=Zadanie nie powiodło się z powodu zbyt wielu aktywnych połączeń SAP HANA.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=Nie można było wykonać operacji powtórzenia, ponieważ powtórzenia są dozwolone tylko dla węzła nadrzędnego zagnieżdżonego łańcucha zadań.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Logi nieudanych zadań elementów podrzędnych nie są już dostępne dla kontynuacji ponownej próby.


####Metrics Labels

performanceOptimized=Zoptymalizowane pod kątem wydajności
memoryOptimized=Zoptymalizowane pod kątem pamięci

JOB_EXECUTION=Wykonanie zadania
EXECUTION_MODE=Tryb wykonania
NUMBER_OF_RECORDS_OVERALL=Liczba utrwalonych rekordów
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Liczba rekordów odczytanych ze źródła zdalnego
RUNTIME_MS_REMOTE_EXECUTION_TIME=Czas przetwarzania źródła zdalnego
MEMORY_CONSUMPTION_GIB=SAP HANA - pamięć szczytowa
NUMBER_OF_PARTITIONS=Liczba partycji
MEMORY_CONSUMPTION_GIB_OVERALL=SAP HANA - pamięć szczytowa
NUMBER_OF_PARTITIONS_LOCKED=Liczba zablokowanych partycji
PARTITIONING_COLUMN=Kolumna partycjonowania
HANA_PEAK_CPU_TIME=Łączny czas procesora SAP HANA
USED_IN_DISK=Wykorzystana pamięć
INPUT_PARAMETER_PARAMETER_VALUE=Parametr wejściowy
INPUT_PARAMETER=Parametr wejściowy
ECN_ID=Nazwa elastycznego węzła obliczeniowego

DAC=Kontrole dostępu do danych
YES=Tak
NO=Nie
noofrecords=Liczba rekordów
partitionpeakmemory=SAP HANA - pamięć szczytowa
value=Wartość
metricsTitle=Metryki ({0})
partitionmetricsTitle=Partycje ({0})
partitionLabel=Partycja
OthersNotNull=Wartości nieuwzględnione w zakresach
OthersNull=Wartości null
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Ustawienia używane dla ostatniego przebiegu utrwalania danych:
#XMSG: Message for input parameter name
inputParameterLabel=Parametr wejściowy
#XMSG: Message for input parameter value
inputParameterValueLabel=Wartość
#XMSG: Message for persisted data
inputParameterPersistedLabel=Czas utrwalenia
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Usuń dane
REMOVE_DELETED_RECORDS=Usuń skasowane rekordy
MERGE_FILES=Połącz pliki
OPTIMIZE_FILES=Optymalizuj pliki
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Wyświetl w monitorze SAP BW Bridge

ANALYZE_PERFORMANCE=Analizuj wydajność
CANCEL_ANALYZE_PERFORMANCE=Anuluj analizę wydajności

#XFLD: Label for frequency column
everyLabel=Co
#XFLD: Plural Recurrence text for Hour
hoursLabel=godz.
#XFLD: Plural Recurrence text for Day
daysLabel=dni
#XFLD: Plural Recurrence text for Month
monthsLabel=mies.
#XFLD: Plural Recurrence text for Minutes
minutesLabel=min

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Podręcznik rozwiązywania problemów dotyczących utrwalenia widoku</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Za mało pamięci dla procesu. Nie można utrwalić danych dla widoku "{0}". Poszukaj informacji na temat błędów braku pamięci w SAP Help Portal. Rozważ użycie analizatora widoków w celu przeanalizowania widoku pod kątem złożoności zużycia pamięci.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Nie dotyczy
OPEN_BRACKET=(
CLOSE_BRACKET=)
