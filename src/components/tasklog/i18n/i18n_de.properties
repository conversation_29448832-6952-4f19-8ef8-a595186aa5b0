
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Protokollinformationen
#XFLD: Header
TASK_LOGS=Aufgabenprotokolle ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Läufe ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Details anzeigen
#XFLD: Button text
STOP=Lauf stoppen
#XFLD: Label text
RUN_START=Start des letzten Laufs
#XFLD: Label text
RUN_END=Ende des letzten Laufs
#XFLD: Label text
RUNTIME=Dauer
#XTIT: Count for Messages
txtDetailMessages=Meldungen ({0})
#XFLD: Label text
TIME=Zeitstempel
#XFLD: Label text
MESSAGE=Meldung
#XFLD: Label text
TASK_STATUS=Kategorie
#XFLD: Label text
TASK_ACTIVITY=Aktivität
#XFLD: Label text
RUN_START_DETAILS=Start
#XFLD: Label text
RUN_END_DETAILS=Ende
#XFLD: Label text
LOGS=Läufe
#XFLD: Label text
STATUS=Status
#XFLD: Label text
RUN_STATUS=Laufstatus
#XFLD: Label text
Runtime=Dauer
#XFLD: Label text
RuntimeTooltip=Dauer (hh:mm:ss)
#XFLD: Label text
TRIGGEREDBY=Ausgelöst von
#XFLD: Label text
TRIGGEREDBYNew=Ausgeführt von
#XFLD: Label text
TRIGGEREDBYNewImp=Lauf gestartet von
#XFLD: Label text
EXECUTIONTYPE=Ausführungsart
#XFLD: Label text
EXECUTIONTYPENew=Laufart
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Übergeordneter Ketten-Space
#XFLD: Refresh tooltip
TEXT_REFRESH=Aktualisieren
#XFLD: view Details link
VIEW_ERROR_DETAILS=Details anzeigen
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Zusätzliche Details herunterladen
#XMSG: Download completed
downloadStarted=Download gestartet
#XMSG: Error while downloading content
errorInDownload=Beim Herunterladen ist ein Fehler aufgetreten.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Details anzeigen
#XBTN: cancel button of task details dialog
TXT_CANCEL=Abbrechen
#XBTN: back button from task details
TXT_BACK=Zurück
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Aufgabe abgeschlossen
#XFLD: Log message with failed status
MSG_LOG_FAILED=Aufgabe fehlgeschlagen
#XFLD: Master and detail table with no data
No_Data=Keine Daten
#XFLD: Retry tooltip
TEXT_RETRY=Wiederholen
#XFLD: Cancel Run label
TEXT_CancelRun=Lauf abbrechen
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Fehlgeschlagenen Ladevorgang bereinigen
#XMSG:button copy sql statement
txtSQLStatement=SQL-Anweisung kopieren
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Remote-Abfrage-Monitor öffnen
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=Um die Remote-SQL-Anweisungen anzuzeigen, klicken Sie auf "Remote-Abfrage-Monitor öffnen".
#XMSG:button ok
txtOk=OK
#XMSG: button close
txtClose=Schließen
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=Aktion "Lauf abbrechen" für Objekt "{0}" wurde gestartet.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=Aktion "Lauf abbrechen" für Objekt "{0}" ist fehlgeschlagen.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=Aktion "Lauf abbrechen" für Objekt "{0}" ist nicht mehr möglich, da sich der Replikationsstatus geändert hat.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Keine Aufgabenprotokolle haben den Status "Wird ausgeführt".
#XMSG: message for conflicting task
Task_Already_Running=Für das Objekt "{0}" wird bereits eine in Konflikt stehende Aufgabe ausgeführt.
#XFLD: Label for no task log with running state title
actionInfo=Aktionsinfo
#XMSG Copied to clipboard
copiedToClip=In Zwischenablage kopiert
#XFLD copy
Copy=Kopieren
#XFLD copy correlation ID
CopyCorrelationID=Korrelations-ID kopieren
#XFLD Close
Close=Schließen
#XFLD: show more Label
txtShowMore=Mehr anzeigen
#XFLD: message Label
messageLabel=Meldung:
#XFLD: details Label
detailsLabel=Details:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Folgende SQL-Anweisung \r\n wird ausgeführt:
#XFLD:statementId Label
statementIdLabel=Anweisungs-ID:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Anzahl an Remote- \r\n SQL-Anweisungen:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Anzahl der Anweisungen
#XFLD: Space Label
txtSpaces=Space
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Nicht berechtigte Spaces
#XFLD: Privilege Error Text
txtPrivilegeError=Sie haben keine Berechtigung, diese Daten anzuzeigen.
#XFLD: Label for Object Header
DATA_ACCESS=Datenzugriff
#XFLD: Label for Object Header
SCHEDULE=Zeitplan
#XFLD: Label for Object Header
DETAILS=Details
#XFLD: Label for Object Header
LATEST_UPDATE=Letzte Aktualisierung
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Letzte Änderung (Quelle)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Aktualisierungshäufigkeit
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Eingeplante Häufigkeit
#XFLD: Label for Object Header
NEXT_RUN=Nächster Lauf
#XFLD: Label for Object Header
CONNECTION=Verbindung
#XFLD: Label for Object Header
DP_AGENT=DP-Agent
#XFLD: Label for Object Header
USED_IN_MEMORY=Für Speicherung verwendeter Arbeitsspeicher (MiB)
#XFLD: Label for Object Header
USED_DISK=Für Speicherung verwendeter Festplattenspeicher (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Größe des In-Memory-Speichers (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Größe auf Datenträger (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Anzahl der Datensätze

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Auf "Fehlgeschlagen" gesetzt
SET_TO_FAILED_ERR=Diese Aufgabe wurde ausgeführt, der Benutzer hat den Status der Aufgabe jedoch auf "Fehlgeschlagen" gesetzt.
#XFLD: Label for stopped failed
FAILLOCKED=Lauf wird bereits ausgeführt
#XFLD: sub status STOPPED
STOPPED=Gestoppt
STOPPED_ERR=Diese Aufgabe wurde gestoppt, es wurde jedoch kein Rollback durchgeführt.
#XFLD: sub status CANCELLED
CANCELLED=Abgebrochen
CANCELLED_ERR=Dieser Aufgabenlauf wurde nach dem Start abgebrochen. In diesem Fall wurden die Daten zurückgesetzt, und es wurde der vor dem ersten Auslösen des Aufgabenlaufs bestehende Zustand wiederhergestellt.
#XFLD: sub status LOCKED
LOCKED=Gesperrt
LOCKED_ERR=Dieselbe Aufgabe wurde bereits ausgeführt, deshalb kann sie nicht parallel mit einer schon bestehenden Ausführung der Aufgabe ausgeführt werden.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Aufgabenausnahme
TASK_EXCEPTION_ERR=Bei der Ausführung dieser Aufgabe ist ein unbekannter Fehler aufgetreten.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Ausnahme bei der Ausgabenausführung
TASK_EXECUTE_EXCEPTION_ERR=Bei der Ausführung dieser Aufgabe ist ein Fehler aufgetreten.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Nicht berechtigt
UNAUTHORIZED_ERR=Der Benutzer konnte nicht authentifiziert werden, wurde gesperrt oder gelöscht.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Unzulässig
FORBIDDEN_ERR=Der zugeordnete Benutzer verfügt nicht über die zur Ausführung dieser Aufgabe erforderlichen Berechtigungen.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Nicht ausgelöst
FAIL_NOT_TRIGGERED_ERR=Der Aufgabenjob konnte nicht ausgeführt werden, da zum Zeitpunkt der geplanten Ausführung ein Systemausfall aufgetreten ist oder ein Teil des Datenbanksystems nicht verfügbar war. Warten Sie auf die nächste für den Job eingeplante Ausführungszeit oder planen Sie den Job erneut ein.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Zeitplan abgebrochen
SCHEDULE_CANCELLED_ERR=Der Aufgabenjob konnte aufgrund eines internen Fehlers nicht ausgeführt werden. Wenden Sie sich an den SAP Support, und teilen Sie ihm die Korrelations-ID und den Zeitstempel aus den Detailinformationen des Protokolls des Aufgabenjobs mit.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Vorheriger Lauf wird ausgeführt
SUCCESS_SKIPPED_ERR=Die Ausführung dieser Aufgabe wurde nicht ausgelöst, da noch ein vorheriger Lauf derselben Aufgabe ausgeführt wird.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Eigentümer fehlt
FAIL_OWNER_MISSING_ERR=Der Aufgabenjob konnte nicht ausgeführt werden, da er nicht über einen zugeordneten Systembenutzer verfügt. Ordnen Sie einen Benutzer für den Job zu.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Keine Einwilligung erteilt
FAIL_CONSENT_NOT_AVAILABLE_ERR=Sie haben SAP nicht die Berechtigung gegeben, Aufgabenketten für Sie auszuführen oder Datenintegrationsaufgaben für Sie einzuplanen. Aktivieren Sie die zum Erteilen Ihrer Einwilligung bereitgestellte Option.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Einwilligung abgelaufen
FAIL_CONSENT_EXPIRED_ERR=Die Berechtigung, die SAP erlaubt, in Ihrem Auftrag Aufgabenketten auszuführen oder Datenintegrationsaufgaben einzuplanen, ist abgelaufen. Aktivieren Sie die zum Verlängern Ihrer Einwilligung bereitgestellte Option.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Einwilligung wurde ungültig gemacht
FAIL_CONSENT_INVALIDATED_ERR=Die Aufgabe konnte nicht ausgeführt werden, i. d. R. aufgrund von einer Änderung in der Identity-Provider-Konfiguration des Tenants. In diesem Fall können keine neue Aufgabenjobs im Auftrag des betroffenen Benutzers ausgeführt oder eingeplant werden. Wenn der zugeordnete Benutzer im neuen IdP noch vorhanden ist, ziehen Sie die Einwilligungen zur Einplanung zurück und erteilen Sie sie erneut. Wenn der zugeordnete Benutzer nicht mehr vorhanden ist, ordnen Sie einen neuen Jobverantwortlichen für die Aufgabe zu, und erteilen Sie die erforderliche Einwilligung zur Einplanung der Aufgabe. Weitere Informationen erhalten Sie im folgenden SAP-Hinweis: https://launchpad.support.sap.com/#/notes/3089828.
TASK_EXECUTOR_ERROR=Ausführender der Aufgabe
TASK_EXECUTOR_ERROR_ERR=Bei der Aufgabe ist ein interner Fehler aufgetreten, vermutlich während der Vorbereitungsschritte für die Ausführung, und die Aufgabe konnte nicht gestartet werden.
PREREQ_NOT_MET=Voraussetzung nicht erfüllt
PREREQ_NOT_MET_ERR=Diese Aufgabe konnte aufgrund von Problemen in ihrer Definition nicht ausgeführt werden. Es ist beispielsweise möglich, dass das Objekt nicht aktiviert ist, eine Aufgabenkette eine zirkuläre Logik enthält oder die SQL eines View ungültig ist.
RESOURCE_LIMIT_ERROR=Fehler durch Ressourcenbegrenzung
RESOURCE_LIMIT_ERROR_ERR=Aufgabe kann derzeit nicht ausgeführt werden, da nicht ausreichend Ressourcen zur Verfügung stehen oder diese ausgelastet sind.
FAIL_CONSENT_REFUSED_BY_UMS=Einwilligung abgelehnt
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Diese Aufgabe konnte aufgrund einer Änderung an der Konfiguration des Identity-Providers eines Benutzers auf dem Tenant nicht in eingeplanten Läufen oder Aufgabenketten ausgeführt werden. Weitere Informationen finden Sie im folgenden SAP-Hinweis: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Eingeplant
#XFLD: status text
SCHEDULEDNew=Dauerhaft
#XFLD: status text
PAUSED=Pausiert
#XFLD: status text
DIRECT=Direkt
#XFLD: status text
MANUAL=Manuell
#XFLD: status text
DIRECTNew=Einfach
#XFLD: status text
COMPLETED=Abgeschlossen
#XFLD: status text
FAILED=Fehlgeschlagen
#XFLD: status text
RUNNING=Wird ausgeführt
#XFLD: status text
none=Keine
#XFLD: status text
realtime=Echtzeit
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Unteraufgabe
#XFLD: New Data available in the file
NEW_DATA=Neue Daten

#XFLD: text for values shown in column Replication Status
txtOff=Aus
#XFLD: text for values shown in column Replication Status
txtInitializing=Wird initialisiert
#XFLD: text for values shown in column Replication Status
txtLoading=Wird geladen
#XFLD: text for values shown in column Replication Status
txtActive=Aktiv
#XFLD: text for values shown in column Replication Status
txtAvailable=Verfügbar
#XFLD: text for values shown in column Replication Status
txtError=Fehler
#XFLD: text for values shown in column Replication Status
txtPaused=Pausiert
#XFLD: text for values shown in column Replication Status
txtDisconnected=Nicht verbunden
#XFLD: text for partially Persisted views
partiallyPersisted=Teilweise persistiert

#XFLD: activity text
REPLICATE=Replizieren
#XFLD: activity text
REMOVE_REPLICATED_DATA=Replizierte Daten entfernen
#XFLD: activity text
DISABLE_REALTIME=Echtzeit-Datenreplikation deaktivieren
#XFLD: activity text
REMOVE_PERSISTED_DATA=Persistente Daten entfernen
#XFLD: activity text
PERSIST=Persistieren
#XFLD: activity text
EXECUTE=Ausführen
#XFLD: activity text
TASKLOG_CLEANUP=Aufgabenprotokoll bereinigen
#XFLD: activity text
CANCEL_REPLICATION=Replikation abbrechen
#XFLD: activity text
MODEL_IMPORT=Modellimport
#XFLD: activity text
NONE=Keine
#XFLD: activity text
CANCEL_PERSISTENCY=Persistenz abbrechen
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=View analysieren
#XFLD: activity text
CANCEL_VIEW_ANALYZER=View Analyzer abbrechen

#XFLD: severity text
INFORMATION=Informationen
#XFLD: severity text
SUCCESS=Erfolgreich
#XFLD: severity text
WARNING=Warnung
#XFLD: severity text
ERROR=Fehler
#XFLD: text for values shown for Ascending sort order
SortInAsc=Aufsteigend sortieren
#XFLD: text for values shown for Descending sort order
SortInDesc=Absteigend sortieren
#XFLD: filter text for task log columns
Filter=Filtern
#XFLD: object text for task log columns
Object=Objekt
#XFLD: space text for task log columns
crossSpace=Space

#XBUT: label for remote data access
REMOTE=Remote
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Repliziert (Echtzeit)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Repliziert (Snapshot)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=Echtzeitreplikation ist aufgrund eines Fehlers gesperrt. Wenn der Fehler behoben wurde, können Sie über "Wiederholen" mit der Echtzeitreplikation fortfahren.
ERROR_MSG=Echtzeitreplikation ist aufgrund eines Fehlers gesperrt.
RETRY_FAILED_ERROR=Wiederholung des Prozesses ist mit einem Fehler fehlgeschlagen.
LOG_INFO_DETAILS=Wenn Daten im Echtzeitmodus repliziert werden, werden keine Protokolle generiert. Die angezeigten Protokolle beziehen sich auf vorherige Aktionen.

#XBUT: Partitioning label
partitionMenuText=Partitionierung
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Partition anlegen
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Partition bearbeiten
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Partition löschen
#XFLD: Initial text
InitialPartitionText=Definieren Sie Partitionen, indem Sie Kriterien zum Aufteilen großer Datensets in kleinere Sets angeben.
DefinePartition=Partitionen definieren
#XFLD: Message text
partitionChangedInfo=Die Partitionsdefinition hat sich seit der letzten Replikation geändert. Änderungen werden beim nächsten Datenladevorgang angewendet.
#XFLD: Message text
REAL_TIME_WARNING=Die Partitionierung wird nur beim Laden eines neuen Snapshot angewendet. Sie gilt nicht für die Echtzeitreplikation.
#XFLD: Message text
loadSelectedPartitions=Persistieren von Daten für die ausgewählten Partitionen von "{0}" wurde gestartet.
#XFLD: Message text
loadSelectedPartitionsError=Persistieren von Daten für die ausgewählten Partitionen von "{0}" ist fehlgeschlagen.
#XFLD: Message text
viewpartitionChangedInfo=Partitionsdefinition hat sich seit dem letzten Persistenzlauf geändert. Um die Änderungen anzuwenden, ist der nächste Datenladevorgang ein vollständiger Snapshot einschließlich gesperrter Partitionen. Sobald dieser vollständige Ladevorgang abgeschlossen ist, können Sie Daten für einzelne Partitionen ausführen.
#XFLD: Message text
viewpartitionChangedInfoLocked=Partitionsdefinition hat sich seit dem letzten Persistenzlauf geändert. Um die Änderungen anzuwenden, ist der nächste Datenladevorgang ein vollständiger Snapshot (außer gesperrter und unveränderter Partitionsbereiche). Sobald dieser Ladevorgang abgeschlossen ist, können Sie ausgewählte Partitionen erneut laden.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Tabellenreplikation
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Replikation einplanen
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Zeitplan anlegen
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Zeitplan bearbeiten
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Zeitplan löschen
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Neuen Snapshot laden
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Datenreplikation starten
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Replizierte Daten entfernen
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Echtzeitzugriff aktivieren
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Echtzeit-Datenreplikation aktivieren
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Echtzeit-Datenreplikation deaktivieren
#XFLD: Message for replicate table action
replicateTableText=Tabellenreplikation
#XFLD: Message for replicate table action
replicateTableTextNew=Datenreplikation
#XFLD: Message to schedule task
scheduleText=Zeitplan
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=View-Persistenz
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Datenpersistenz
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Neuen Snapshot laden
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Datenpersistenz starten
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Persistente Daten entfernen
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Partitionierte Verarbeitung
#XBUT: Label for scheduled replication
scheduledTxt=Eingeplant
#XBUT: Label for statistics button
statisticsTxt=Statistik
#XBUT: Label for create statistics
createStatsTxt=Statistik anlegen
#XBUT: Label for edit statistics
editStatsTxt=Statistik bearbeiten
#XBUT: Label for refresh statistics
refreshStatsTxt=Statistik aktualisieren
#XBUT: Label for delete statistics
dropStatsTxt=Statistik löschen
#XMSG: Create statistics success message
statsSuccessTxt=Anlegen von Statistiken vom Typ {0} für {1} wurde gestartet.
#XMSG: Edit statistics success message
statsEditSuccessTxt=Ändern des Statistiktyps für {1} in {0} wurde gestartet.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Aktualisieren der Statistik für {0} wurde gestartet.
#XMSG: Drop statistics success message
statsDropSuccessTxt=Statistik für {0} wurde erfolgreich gelöscht
#XMSG: Create statistics error message
statsCreateErrorTxt=Fehler beim Anlegen der Statistik
#XMSG: Edit statistics error message
statsEditErrorTxt=Fehler beim Ändern der Statistik
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Fehler beim Aktualisieren der Statistik
#XMSG: Drop statistics error message
statsDropErrorTxt=Fehler beim Löschen der Statistik
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Möchten Sie die Datenstatistik wirklich löschen?
startPersistencyAdvisorLabel=View Analyzer starten

#Partition related texts
#XFLD: Label for Column
column=Spalte
#XFLD: Label for No of Partition
noOfPartitions=Anzahl der Partitionen
#XFLD: Label for Column
noOfParallelProcess=Anzahl paralleler Prozesse
#XFLD: Label text
noOfLockedPartition=Anzahl gesperrter Partitionen
#XFLD: Label for Partition
PARTITION=Partitionen
#XFLD: Label for Column
AVAILABLE=Verfügbar
#XFLD: Statistics Label
statsLabel=Statistik
#XFLD: Label text
COLUMN=Spalte:
#XFLD: Label text
PARALLEL_PROCESSES=Parallele Prozesse:
#XFLD: Label text
Partition_Range=Partitionsbereich
#XFLD: Label text
Name=Name
#XFLD: Label text
Locked=Gesperrt
#XFLD: Label text
Others=SONSTIGES
#XFLD: Label text
Delete=Löschen
#XFLD: Label text
LoadData=Ausgewählte Partitionen laden
#XFLD: Label text
LoadSelectedData=Ausgewählte Partitionen laden
#XFLD: Confirmation text
LoadNewPersistenceConfirm=Mit dieser Aktion laden Sie einen neuen Snapshot für alle entsperrten und geänderten Partitionen, nicht nur für Ihre Auswahl. Möchten Sie fortfahren?
#XFLD: Label text
Continue=Fortfahren

#XFLD: Label text
PARTITIONS=Partitionen
#XFLD: Label text
ADD_PARTITIONS=+ Partition hinzufügen
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Partition hinzufügen
#XFLD: Label text
deleteRange=Partition löschen
#XFLD: Label text
LOW_PLACE_HOLDER=Niedrigen Wert eingeben
#XFLD: Label text
HIGH_PLACE_HOLDER=Hohen Wert eingeben
#XFLD: tooltip text
lockedTooltip=Partition nach dem erstmaligen Laden sperren

#XFLD: Button text
Edit=Bearbeiten
#XFLD: Button text
CANCEL=Abbrechen

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Letze Statistikaktualisierung
#XFLD: Statistics Fields
STATISTICS=Statistik

#XFLD:Retry label
TEXT_Retry=Wiederholen
#XFLD:Retry label
TEXT_Retry_tooltip=Echtzeitreplikation erneut versuchen, nachdem Fehler behoben wurde.
#XFLD: text retry
Retry=Bestätigen
#XMG: Retry confirmation text
retryConfirmationTxt=Die letzte Echtzeitreplikation wurde mit einem Fehler beendet.\nBestätigen Sie, dass der Fehler behoben wurde, und dass die Echtzeitreplikation erneut gestartet werden kann.
#XMG: Retry success text
retrySuccess=Wiederholung des Prozesses wurde erfolgreich initiiert.
#XMG: Retry fail text
retryFail=Wiederholung des Prozesses fehlgeschlagen.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Statistik anlegen
#XMSG: activity message for edit statistics
DROP_STATISTICS=Statistik löschen
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Statistik aktualisieren
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Statistik bearbeiten
#XMSG: Task log message started task
taskStarted=Die Aufgabe {0} wurde gestartet.
#XMSG: Task log message for finished task
taskFinished=Die Aufgabe {0} endete mit Status {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=Aufgabe {0} wurde um {2} mit Status {1} beendet.
#XMSG: Task {0} has input parameters
taskHasInputParameters=Aufgabe {0} weist Eingabeparameter auf.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=Die Aufgabe {0} endete mit einem unerwarteten Fehler. Der Aufgabenstatus wurde auf {1} gesetzt.
#XMSG: Task log message for failed task
failedToEnd=Status konnte nicht auf {0} gesetzt werden, oder Sperre konnte nicht entfernt werden.
#XMSG: Task log message
lockNotFound=Der Prozess kann nicht abgeschlossen werden, da die Sperre fehlt: die Aufgabe wurde ggf. storniert.
#XMSG: Task log message failed task
failedOverwrite=Die Aufgabe {0} wurde bereits durch {1} gesperrt. Deshalb ist sie mit dem folgenden Fehler fehlgeschlagen: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Die Sperre der Aufgabe wurde durch eine andere Aufgabe übernommen.
#XMSG: Task log message failed takeover
failedTakeover=Vorhandene Aufgabe konnte nicht übernommen werden.
#XMSG: Task log message successful takeover
successTakeover=Die übriggebliebene Sperre musste aufgehoben werden. Die neue Sperre für diese Aufgabe wurde festgelegt.
#XMSG: Tasklog Dialog Details
txtDetails=Die während des Laufs verarbeiteten Remote-Anweisungen können über den Remote-Abfrage-Monitor in den Details der partitionsspezifischen Meldungen angezeigt werden.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=Remote-SQL-Anweisungen wurden aus der Datenbank gelöscht und können nicht angezeigt werden.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Remote-Abfragen, die zu anderen Spaces zugeordnete Verbindungen aufweisen, können nicht angezeigt werden. Wechseln Sie zum Remote-Abfrage-Monitor, und verwenden Sie die Anweisungs-ID, um sie zu filtern.
#XMSG: Task log message for parallel check error
parallelCheckError=Die Aufgabe kann nicht verarbeitet werden, da bereits eine andere Aufgabe ausgeführt wird, die diese Aufgabe blockiert.
#XMSG: Task log message for parallel running task
parallelTaskRunning=Es wird bereits eine in Konflikt stehende Aufgabe ausgeführt.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Status {0} während Lauf mit Korrelations-ID {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=Der zugeordnete Benutzer verfügt nicht über die zur Ausführung dieser Aufgabe erforderlichen Berechtigungen.

#XBUT: Label for open in Editor
openInEditor=In Editor öffnen
#XBUT: Label for open in Editor
openInEditorNew=In Data Builder öffnen
#XFLD:Run deails label
runDetails=Laufdetails
#XFLD: Label for Logs
Logs=Protokolle
#XFLD: Label for Settings
Settings=Einstellungen
#XFLD: Label for Save button
Save=Sichern
#XFLD: Label for Standard
Standard_PO=Für Performance optimiert (empfohlen)
#XFLD: Label for Hana low memory processing
HLMP_MO=Für Speicher optimiert
#XFLD: Label for execution mode
ExecutionMode=Ausführungsmodus
#XFLD: Label for job execution
jobExecution=Verarbeitungsmodus
#XFLD: Label for Synchronous
syncExec=Synchron
#XFLD: Label for Asynchronous
asyncExec=Asynchron
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Standard verwenden (asynchron, kann sich künftig ändern)
#XMSG: Save settings success
saveSettingsSuccess=Der Ausführungsmodus für SAP HANA wurde geändert.
#XMSG: Save settings failure
saveSettingsFailed=Änderung des Ausführungsmodus für SAP HANA fehlgeschlagen.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Jobausführung wurde geändert.
#XMSG: Job Execution change failed
jobExecSettingFailed=Änderung der Jobausführung fehlgeschlagen.
#XMSG: Text for Type
typeTxt=Typ
#XMSG: Text for Monitor
monitorTxt=Überwachen
#XMSG: Text for activity
activityTxt=Aktivität
#XMSG: Text for metrics
metricsTxt=Metriken
#XTXT: Text for Task chain key
TASK_CHAINS=Aufgabenkette
#XTXT: Text for View Key
VIEWS=View
#XTXT: Text for remote table key
REMOTE_TABLES=Remote-Tabelle
#XTXT: Text for Space key
SPACE=Space
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Elastischer Rechenleistungsknoten
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Replikationsfluss
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Intelligente Suche
#XTXT: Text for Local Table
LOCAL_TABLE=Lokale Tabelle
#XTXT: Text for Data flow key
DATA_FLOWS=Datenfluss
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=SQL-Skript-Prozedur
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=BW-Prozesskette
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Im Monitor anzeigen
#XTXT: Task List header text
taskListHeader=Aufgabenliste ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Metriken für historische Läufe eines Datenflusses können nicht abgerufen werden.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=Die vollständigen Laufdetails können momentan nicht geladen werden. Führen Sie eine Aktualisierung durch.
#XFLD: Label text for the Metrices table header
metricesColLabel=Bezeichner des Operators
#XFLD: Label text for the Metrices table header
metricesType=Typ
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Anzahl Datensätze
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Aufgabenkette ausführen
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Ausführung der Aufgabenkette wurde gestartet.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Ausführung der Aufgabenkette wurde für {0} gestartet.
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Aufgabenkette konnte nicht ausgeführt werden.
#XTXT: Execute button label
runLabel=Ausführen
#XTXT: Execute button label
runLabelNew=Lauf starten
#XMSG: Filter Object header
chainsFilteredTableHeader=Nach Objekt gefiltert: {0}
#XFLD: Parent task chain label
parentChainLabel=Übergeordnete Aufgabenkette:
#XFLD: Parent task chain unauthorized
Unauthorized=Keine Berechtigung zum Anzeigen
#XFLD: Parent task chain label
parentTaskLabel=Übergeordnete Aufgabe:
#XTXT: Task status
NOT_TRIGGERED=Nicht ausgelöst
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Vollbildmodus starten
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Vollbildmodus beenden
#XTXT: Close Task log details right panel
closeRightColumn=Bereich schließen
#XTXT: Sort Text
sortTxt=Sortieren
#XTXT: Filter Text
filterTxt=Filtern
#XTXT: Filter by text to show list of filters applied
filterByTxt=Filtern nach
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Mehr als 5 Minuten
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Mehr als 15 Minuten
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Mehr als 1 Stunde
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Letzte Stunde
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Letzte 24 Stunden
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Letzter Monat
#XTXT: Messages title text
messagesText=Nachrichten

#XTXT Statistics information message
statisticsInfo=Statistiken können nicht für Remote-Tabellen mit dem Datenzugriff "Repliziert" angelegt werden. Um Statistiken anzulegen, entfernen Sie die replizierten Daten im Detailmonitor für Remote-Tabellen.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Zum Detailmonitor für Remote-Tabellen wechseln

#XTXT: Repair latest failed run label
retryRunLabel=Letzten Lauf wiederholen
#XTXT: Repair failed run label
retryRun=Lauf wiederholen
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Ausführung der Wiederholung der Aufgabenkette wurde gestartet.
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Ausführung der Wiederholung der Aufgabenkette ist fehlgeschlagen.
#XMSG: Task chain child elements name
taskChainRetryChildObject=Aufgabe {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Neue Aufgabe
#XFLD Analyzed View
analyzedView=Analysierter View
#XFLD Metrics
Metrics=Metriken
#XFLD Partition Metrics
PartitionMetrics=Partitionsmetriken
#XFLD Entities
Messages=Aufgabenprotokoll
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Es wurden noch keine Partitionen definiert.
#XTXT: Description message for empty partition data
partitionEmptyDescText=Legen Sie Partitionen an, indem Sie Kriterien angeben, um größere Datenmengen in kleinere, besser zu verwaltende Teile zu unterteilen.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Noch keine Protokolle verfügbar
#XTXT: Description message for empty runs data
runsEmptyDescText=Wenn Sie eine neue Aktivität starten (Laden eines neuen Snapshots, Starten des View Analyzers …), werden hier die zugehörigen Protokolle angezeigt.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=Die Konfiguration des elastischen Rechenleistungsknotens {0} ist nicht entsprechend gepflegt. Überprüfen Sie Ihre Definition.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Prozess zum Hinzufügen des elastischen Rechenleistungsknotens {0} ist fehlgeschlagen.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Anlegen und Aktivieren von Replikaten für das Quellobjekt "{0}"."{1}" im elastischen Rechenleistungsknoten {2} wurde gestartet.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Entfernen von Replikaten für das Quellobjekt "{0}"."{1}" aus dem elastischen Rechenleistungsknoten {2} wurde gestartet.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Anlegen und Aktivieren von Replikaten für das Quellobjekt "{0}"."{1}" ist fehlgeschlagen.
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Entfernen des Quellobjekts "{0}"."{1}" ist fehlgeschlagen.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Routenfindung für Analyseabfrage-Berechnung von Space {0} zum elastischen Rechenleistungsknoten {1} wurde gestartet.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Routenfindung für Analyseabfrage-Berechnung von Space {0} zum zugehörigen elastischen Rechenleistungsknoten ist fehlgeschlagen.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Erneute Routenfindung für Analyseabfrage-Berechnung von Space {0} zurück vom elastischen Rechenleistungsknoten {1} wurde gestartet.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Erneute Routenfindung für Analyseabfrage-Berechnung von Space {0} zurück zum Koordinator ist fehlgeschlagen.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Aufgabenkette {0} zum zugehörigen elastischen Rechenleistungsknoten {1} wurde ausgelöst.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Generieren der Aufgabenkette für den elastischen Rechenleistungsknoten {0} ist fehlgeschlagen.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=Die Bereitstellung des elastischen Rechenleistungsknotens {0} mit der angegebenen Größe wurde gestartet: vCPUs: {1}, Arbeitsspeicher (GiB): {2} und Speicherplatz (GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=Der elastische Rechenleistungsknoten {0} wurde bereits bereitgestellt.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=Die Bereitstellung des elastische Rechenleistungsknoten {0} wurde bereits aufgehoben.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=Dieser Vorgang ist unzulässig. Stoppen Sie den elastischen Rechenleistungsknoten {0} und starten Sie ihn erneut, um den Sizing-Plan zu aktualisieren.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Prozess zum Entfernen des elastischen Rechenleistungsknotens {0} ist fehlgeschlagen.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=Zeitüberschreitung beim aktuellen Lauf des elastischen Rechenleistungsknotens {0}.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=Status des elastischen Rechenleistungsknotens {0} wird geprüft …
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=Aufgabenkettengenerierung für den elastischen Rechenleistungsknoten {0} ist gesperrt, deshalb wird die Kette {1} eventuell noch ausgeführt.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=Das Quellobjekt "{0}"."{1}" wurde als Abhängigkeit von View "{2}"."{3}" repliziert.
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Tabelle "{0}"."{1}" kann nicht repliziert werden, da die Replikation der Zeilentabelle abgekündigt ist.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Die maximale Anzahl elastischer Rechenleistungsknoten pro SAP-HANA-Cloud-Instanz wurde überschritten.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=Laufender Vorgang für den elastischen Rechenleistungsknosten {0} wird noch ausgeführt.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Aufgrund technischer Probleme wurde das Löschen des Replikats für die Quelltabelle {0} gestoppt. Versuchen Sie es später erneut.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=Aufgrund technischer Probleme wurde das Anlegen des Replikats für die Quelltabelle {0} gestoppt. Versuchen Sie es später erneut.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Elastischer Rechenleistungsknoten kann nicht gestartet werden, da die Nutzungsgrenze erreicht wurde oder noch keine Rechenleistungsblockstunden zugewiesen wurden.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Aufgabenkette wird geladen und Ausführung von insgesamt {0} Aufgaben, die sich in dieser Kette befinden, wird vorbereitet.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Es wird bereits eine in Konflikt stehende Aufgabe ausgeführt
#XMSG: Replication will change
txt_replication_change=Replikationstyp wird geändert.
txt_repl_viewdetails=Details anzeigen

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Offenbar ist bei der Wiederholung des letzten Laufs ein Fehler aufgetreten, da der Lauf der vorherigen Aufgabe fehlgeschlagen ist, bevor der Plan generiert werden konnte.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=Space "{0}" ist gesperrt.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=Für Aktivität {0} muss die lokale Tabelle {1} aktiviert sein.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=Für Aktivität {0} muss die Delta-Erfassung für die lokale Tabelle {1} aktiviert sein.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=Für Aktivität {0} ist es erforderlich, dass die lokale Tabelle {1} Daten im Objektspeicher speichert.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=Für Aktivität {0} ist es erforderlich, dass die lokale Tabelle {1} Daten in der Datenbank speichert.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Die gelöschten Datensätze für die lokale Tabelle {0} werden nun entfernt.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Alle Datensätze für die lokale Tabelle {0} werden nun gelöscht.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Datensätze für die lokale Tabelle {0} werden nun gemäß Filterbedingung {1} gelöscht.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Beim Entfernen der gelöschten Datensätze für die lokale Tabelle {0} ist ein Fehler aufgetreten.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Beim Löschen aller Datensätze für die lokale Tabelle {0} ist ein Fehler aufgetreten.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Alle vollständig verarbeiteten Datensätze mit dem Änderungstyp "Gelöscht", die älter sind als {0} Tage, werden gelöscht.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Alle vollständig verarbeiteten Datensätze mit dem Änderungstyp "Gelöscht", die älter als {0} sind, werden gelöscht.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} Datensätze wurden gelöscht.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} Datensätze wurden zum Löschen vorgemerkt.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=Entfernen der gelöschten Datensätze für die lokale Tabelle {0} ist abgeschlossen.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Löschen aller Datensätze für die lokale Tabelle {0} ist abgeschlossen.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Datensätze für die lokale Tabelle {0} werden nun zum Löschen vorgemerkt.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Datensätze für die lokale Tabelle {0} werden nun gemäß Filterbedingung {1} zum Löschen vorgemerkt.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Vormerken zum Löschen der Datensätze für die lokale Tabelle {0} ist abgeschlossen.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Datenänderungen werden temporär in Tabelle {0} geladen.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Datenänderungen wurden temporär in Tabelle {0} geladen.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Datenänderungen werden verarbeitet und aus Tabelle {0} gelöscht.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Verbindungsdetails

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Optimierung der lokalen Tabelle (Datei) wird gestartet.
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Bei der Optimierung der lokalen Tabelle (Datei) ist ein Fehler aufgetreten.
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Es ist ein Fehler aufgetreten. Optimierung der lokalen Tabelle (Datei) wurde gestoppt.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Lokale Tabelle (Datei) wird optimiert …
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=Lokale Tabelle (Datei) wurde optimiert.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=Lokale Tabelle (Datei) ist optimiert.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=Die Tabelle wurde mit den Z-Order-Spalten optimiert: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Es ist ein Fehler aufgetreten. Verkürzen der lokalen Tabelle (Datei) wurde gestoppt.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Lokale Tabelle (Datei) wird verkürzt …
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=Speicherort des Eingangspuffers für lokale Tabelle (Datei) wurde gelöscht.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Bereinigung (Löschen aller vollständig verarbeiteten Datensätze) der lokalen Tabelle (Datei) wird gestartet.
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Bei der Bereinigung der lokalen Tabelle (Datei) ist ein Fehler aufgetreten.
#XMSG: Task log message
LTF_VACUUM_STOPPED=Es ist ein Fehler aufgetreten. Bereinigen der lokalen Tabelle (Datei) wurde gestoppt.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Lokale Tabelle (Datei) wird bereinigt …
#XMSG: Task log message
LTF_VACUUM_SUCCESS=Bereinigung wurde abgeschlossen.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Alle vollständig verarbeiteten Datensätze, die älter als {0} Tage sind, werden gelöscht.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Alle vollständig verarbeiteten Datensätze, die älter als {0} sind, werden gelöscht.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Zusammenführung neuer Datensätze mit lokaler Tabelle (Datei) wird gestartet.
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Zusammenführung der neuen Datensätze mit der lokalen Tabelle (Datei) wird über die Einstellung "Daten automatisch zusammenführen" nun gestartet.
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Zusammenführung der neuen Datensätze mit der lokalen Tabelle (Datei) wird gestartet. Diese Aufgabe wurde durch das API "Zusammenführung anfordern" initiiert.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Neue Datensätze werden mit lokaler Tabelle (Datei) zusammengeführt.
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Beim Zusammenführen neuer Datensätze mit lokaler Tabelle (Datei) ist ein Fehler aufgetreten.
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=Lokale Tabelle (Datei) wurde zusammengeführt.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Ein Fehler ist aufgetreten. Zusammenführung der lokalen Tabelle (Datei) wurde gestoppt.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=Die Zusammenführung der lokalen Tabelle (Datei) ist aufgrund eines Fehlers fehlgeschlagen. Der Vorgang war jedoch teilweise erfolgreich, und einige Daten wurden bereits zusammengeführt.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Fehler durch Zeitüberschreitung. Die Aktivität {0} wird seit {1} Stunden ausgeführt.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=Die asynchrone Ausführung konnte aufgrund einer hohen Systemauslastung nicht starten. Öffnen Sie den ''Systemmonitor'', und prüfen Sie die laufenden Aufgaben.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=Die asynchrone Ausführung wurde abgebrochen.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=Die {0}-Aufgabe wurde innerhalb der Speichergrenzen von {1} und {2} ausgeführt.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=Die Aufgabe {0} wurde mit der Ressourcen-ID {1} ausgeführt.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED="Suchen und Ersetzen" in lokaler Tabelle (Datei) hat begonnen.
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED="Suchen und Ersetzen" in lokaler Tabelle (Datei) wurde abgeschlossen.
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR="Suchen und Ersetzen" in lokaler Tabelle (Datei) ist fehlgeschlagen.

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Es ist ein Fehler aufgetreten. Aktualisieren der Statistiken für lokale Tabelle (Datei) wurde gestoppt.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=Die Aufgabe ist aufgrund von unzureichendem Speicher in der SAP-HANA-Datenbank fehlgeschlagen.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=Die Aufgabe ist aufgrund einer Ablehnung der SAP-HANA-Zugangssteuerung fehlgeschlagen.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=Die Aufgabe ist aufgrund zu vieler aktiver SAP-HANA-Verbindungen fehlgeschlagen.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=Der Vorgang "Wiederholen" konnte nicht ausgeführt werden, da Wiederholungen nur für die übergeordnete Aufgabe einer verschachtelten Aufgabenkette zulässig sind.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Die Protokolle der fehlgeschlagenen untergeordneten Aufgaben sind nicht mehr verfügbar, um die Wiederholung durchzuführen.


####Metrics Labels

performanceOptimized=Für Performance optimiert
memoryOptimized=Für Speicher optimiert

JOB_EXECUTION=Jobausführung
EXECUTION_MODE=Ausführungsmodus
NUMBER_OF_RECORDS_OVERALL=Anzahl der persistierten Datensätze
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Anzahl der aus der Remote-Quelle gelesenen Datensätze
RUNTIME_MS_REMOTE_EXECUTION_TIME=Verarbeitungszeit der Remote-Quelle
MEMORY_CONSUMPTION_GIB=Maximale SAP-HANA-Speicherauslastung
NUMBER_OF_PARTITIONS=Anzahl der Partitionen
MEMORY_CONSUMPTION_GIB_OVERALL=Maximale SAP-HANA-Speicherauslastung
NUMBER_OF_PARTITIONS_LOCKED=Anzahl der gesperrten Partitionen
PARTITIONING_COLUMN=Partitionierungsspalte
HANA_PEAK_CPU_TIME=Gesamte SAP-HANA-CPU-Zeit
USED_IN_DISK=Verwendeter Speicherplatz
INPUT_PARAMETER_PARAMETER_VALUE=Eingabeparameter
INPUT_PARAMETER=Eingabeparameter
ECN_ID=Name des elastischen Rechenleistungsknotens

DAC=Datenzugriffskontrollen
YES=Ja
NO=Nein
noofrecords=Anzahl der Datensätze
partitionpeakmemory=Maximale SAP-HANA-Speicherauslastung
value=Wert
metricsTitle=Metriken ({0})
partitionmetricsTitle=Partitionen ({0})
partitionLabel=Partition
OthersNotNull=Nicht in Bereichen enthaltene Werte
OthersNull=Nullwerte
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Verwendete Einstellungen für den letzten Datenpersistenzlauf:
#XMSG: Message for input parameter name
inputParameterLabel=Eingabeparameter
#XMSG: Message for input parameter value
inputParameterValueLabel=Wert
#XMSG: Message for persisted data
inputParameterPersistedLabel=Persistiert um
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Daten löschen
REMOVE_DELETED_RECORDS=Gelöschte Datensätze entfernen
MERGE_FILES=Dateien zusammenführen
OPTIMIZE_FILES=Dateien optimieren
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Im SAP-BW-Bridge-Monitor anzeigen

ANALYZE_PERFORMANCE=Performance analysieren
CANCEL_ANALYZE_PERFORMANCE=Performance-Analyse abbrechen

#XFLD: Label for frequency column
everyLabel=Alle
#XFLD: Plural Recurrence text for Hour
hoursLabel=Stunden
#XFLD: Plural Recurrence text for Day
daysLabel=Tage
#XFLD: Plural Recurrence text for Month
monthsLabel=Monate
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuten

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Leitfaden zur Fehlerbehebung für View-Persistenz</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Unzureichender Speicher für Prozess. Daten können nicht für den View "{0}" persistiert werden. Im Help Portal finden Sie weitere Informationen zu Out-of-Memory-Fehlern. Sie können auch im View Analyzer den View auf Komplexität des Speicherverbrauchs überprüfen.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Nicht zutreffend
OPEN_BRACKET=(
CLOSE_BRACKET=)
