
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Detalls de log
#XFLD: Header
TASK_LOGS=Logs de tasca ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Execucions ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Veure detalls
#XFLD: Button text
STOP=Aturar execució
#XFLD: Label text
RUN_START=Inici de l’última execució
#XFLD: Label text
RUN_END=Fi de l’última execució
#XFLD: Label text
RUNTIME=Durada
#XTIT: Count for Messages
txtDetailMessages=Missatges ({0})
#XFLD: Label text
TIME=Marca horària
#XFLD: Label text
MESSAGE=Missatge
#XFLD: Label text
TASK_STATUS=Categoria
#XFLD: Label text
TASK_ACTIVITY=Activitat
#XFLD: Label text
RUN_START_DETAILS=Inici
#XFLD: Label text
RUN_END_DETAILS=Final
#XFLD: Label text
LOGS=Execucions
#XFLD: Label text
STATUS=Estat
#XFLD: Label text
RUN_STATUS=Estat d’execució
#XFLD: Label text
Runtime=Durada
#XFLD: Label text
RuntimeTooltip=Durada (hh : mm : ss)
#XFLD: Label text
TRIGGEREDBY=Iniciat per
#XFLD: Label text
TRIGGEREDBYNew=Executat per
#XFLD: Label text
TRIGGEREDBYNewImp=Execució iniciada per
#XFLD: Label text
EXECUTIONTYPE=Tipus d’execució
#XFLD: Label text
EXECUTIONTYPENew=Tipus d'execució
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Espai de cadena superior
#XFLD: Refresh tooltip
TEXT_REFRESH=Actualitzar
#XFLD: view Details link
VIEW_ERROR_DETAILS=Veure detalls
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Baixar detalls addicionals
#XMSG: Download completed
downloadStarted=Baixada iniciada
#XMSG: Error while downloading content
errorInDownload=S'ha produït un error durant la baixada.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Veure detalls
#XBTN: cancel button of task details dialog
TXT_CANCEL=Cancel·lar
#XBTN: back button from task details
TXT_BACK=Endarrere
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Tasca conclosa
#XFLD: Log message with failed status
MSG_LOG_FAILED=Tasca errònia
#XFLD: Master and detail table with no data
No_Data=Sense dades
#XFLD: Retry tooltip
TEXT_RETRY=Reintentar
#XFLD: Cancel Run label
TEXT_CancelRun=Cancel·lar execució
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Error de neteja de càrrega
#XMSG:button copy sql statement
txtSQLStatement=Copiar instrucció SQL
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Obrir monitor de consulta remota
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=Per visualitzar les instruccions SQL remotes, feu clic a "Obrir monitor de consulta remota".
#XMSG:button ok
txtOk=D'acord
#XMSG: button close
txtClose=Tancar
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=S’ha iniciat l''acció "Cancel·lar execució" per a l’objecte "{0}".
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=Error durant l''acció "Cancel·lar execució" per a l’objecte "{0}".
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=Ja no es pot cancel·lar l''acció d''execució per a l''objecte ''{0}'' perquè l''estat de replicació ha canviat.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=No hi ha cap log de tasques que tingui l’estat “En execució”.
#XMSG: message for conflicting task
Task_Already_Running=Ja s''està executant una tasca en conflicte per a l''objecte "{0}".
#XFLD: Label for no task log with running state title
actionInfo=Informació de l’acció
#XMSG Copied to clipboard
copiedToClip=S'ha copiat al porta-retalls
#XFLD copy
Copy=Copiar
#XFLD copy correlation ID
CopyCorrelationID=Copiar ID de correlació
#XFLD Close
Close=Tancar
#XFLD: show more Label
txtShowMore=Mostrar més
#XFLD: message Label
messageLabel=Missatge:
#XFLD: details Label
detailsLabel=Detalls:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Instrucció SQL \r\n en execució:
#XFLD:statementId Label
statementIdLabel=ID d'instrucció:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Quantitat d'instruccions SQL \r\n remotes:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Número d'instruccions
#XFLD: Space Label
txtSpaces=Espai
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Espais no autoritzats
#XFLD: Privilege Error Text
txtPrivilegeError=No teniu prou privilegis per veure aquestes dades.
#XFLD: Label for Object Header
DATA_ACCESS=Accés a dades
#XFLD: Label for Object Header
SCHEDULE=Programa
#XFLD: Label for Object Header
DETAILS=Detalls
#XFLD: Label for Object Header
LATEST_UPDATE=Última actualització
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Última modificació (font)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Freqüència d’actualització
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Freqüència programada
#XFLD: Label for Object Header
NEXT_RUN=Execució següent
#XFLD: Label for Object Header
CONNECTION=Connexió
#XFLD: Label for Object Header
DP_AGENT=Agent DP
#XFLD: Label for Object Header
USED_IN_MEMORY=Memòria utilitzada per a l'emmagatzematge (MiB)
#XFLD: Label for Object Header
USED_DISK=Disc utilitzat per a l'emmagatzematge (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Mida d'En memòria (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Mida en disc (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Nombre de registres

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Fixar en "Erroni"
SET_TO_FAILED_ERR=Aquesta tasca s'estava executant, però l'usuari n'ha fixat l'estat en ERRONI.
#XFLD: Label for stopped failed
FAILLOCKED=Execució ja en curs
#XFLD: sub status STOPPED
STOPPED=Aturat
STOPPED_ERR=Aquesta tasca s'ha aturat, però no s'ha realitzat cap rollback.
#XFLD: sub status CANCELLED
CANCELLED=Cancel·lat
CANCELLED_ERR=L'execució d'aquesta tasca s'ha cancel·lat un cop iniciada. En aquest cas, s'ha fet un rollback de les dades i s'ha restablert a l'estat que tenia abans de desencadenar-la inicialment.
#XFLD: sub status LOCKED
LOCKED=Bloquejat
LOCKED_ERR=La mateixa tasca ja estava en curs, per això no es pot executar en paral·lel aquesta tasca amb una execució de la tasca existent.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Excepció de tasca
TASK_EXCEPTION_ERR=Aquesta tasca ha topat amb un error no especificat durant l'execució.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Excepció d'execució de tasca
TASK_EXECUTE_EXCEPTION_ERR=Aquesta tasca ha trobat un error durant l'execució.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=No autoritzat
UNAUTHORIZED_ERR=No s'ha pogut autenticar l'usuari, se l'ha bloquejat o eliminat.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Prohibit
FORBIDDEN_ERR=L'usuari assignat no té els privilegis necessaris per executar aquesta tasca.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=No desencadenat
FAIL_NOT_TRIGGERED_ERR=Aquesta tasca no s'ha pogut executar a causa d'una caiguda del sistema o perquè alguna part del sistema de bases de dades no estava disponible en el moment de l'execució prevista. Espereu a la propera execució de tasca programada o reprogrameu-la.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Programació cancel·lada
SCHEDULE_CANCELLED_ERR=Aquesta tasca no s'ha pogut executar a causa d'un error intern. Adreceu-vos al servei de suport de SAP i proporcioneu-los l'ID de correlació i la marca horària de la informació detallada que conté el log d'aquesta tasca.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Execució anterior en curs
SUCCESS_SKIPPED_ERR=L'execució d'aquesta tasca no s'ha iniciat perquè encara està en curs una execució anterior de la mateixa tasca.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Falta propietari
FAIL_OWNER_MISSING_ERR=Aquesta tasca no s'ha pogut executar perquè no té un usuari de sistema assignat. Assigneu un usuari propietari a la tasca.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Consentiment no disponible
FAIL_CONSENT_NOT_AVAILABLE_ERR=No heu autoritzat SAP per executar les cadenes de tasques o programar tasques d'integració de dades en el vostre nom. Seleccioneu l'opció facilitada per donar el vostre consentiment.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Consentiment caducat
FAIL_CONSENT_EXPIRED_ERR=L'autorització que permet que SAP executi cadenes de tasques o programi tasques d'integració de dades en el vostre nom ha caducat. Seleccioneu l'opció facilitada per donar el vostre consentiment.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Consentiment invalidat
FAIL_CONSENT_INVALIDATED_ERR=Aquesta tasca no s'ha pogut executar. Habitualment el motiu és que s'ha modificat la configuració del proveïdor d'identitats de l'arrendatari. En aquest cas, no es poden executar ni programar tasques noves en nom de l'usuari afectat. Si l'usuari assignat encara existeix a l'IdP nou, anul·leu el consentiment de programació i torneu a concedir-lo. Si l'usuari assignat ja no existeix, assigneu un nou propietari de tasca i doneu el consentiment de programació de tasques necessari. Per obtenir-ne més informació, vegeu la següent nota SAP: https://launchpad.support.sap.com/#/notes/3089828.
TASK_EXECUTOR_ERROR=Executor de tasques
TASK_EXECUTOR_ERROR_ERR=Aquesta tasca ha topat amb un error intern, probablement durant els passos de preparació per a l'execució, i no s'ha pogut iniciar.
PREREQ_NOT_MET=Requisit previ no satisfet
PREREQ_NOT_MET_ERR=Aquesta tasca no s'ha pogut executar perquè hi ha problemes amb la seva definició. Per exemple, l'objecte no està desplegat, una cadena de tasques conté una lògica circular o un SQL de la vista no és vàlid.
RESOURCE_LIMIT_ERROR=Error de límit de recursos
RESOURCE_LIMIT_ERROR_ERR=Ara mateix no es pot dur a terme la tasca perquè no hi havia prou recursos disponibles o estaven ocupats.
FAIL_CONSENT_REFUSED_BY_UMS=Consentiment rebutjat
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Aquesta tasca no s'ha pogut realitzar en execucions programades o cadenes de tasques a causa d'una modificació a la configuració del Proveïdor d'identitats de l'usuari a l'arrendatari. Per obtenir-ne més informació, vegeu la següent nota SAP: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Programat
#XFLD: status text
SCHEDULEDNew=Permanent
#XFLD: status text
PAUSED=En pausa
#XFLD: status text
DIRECT=Directe
#XFLD: status text
MANUAL=Manual
#XFLD: status text
DIRECTNew=Simple
#XFLD: status text
COMPLETED=Conclòs
#XFLD: status text
FAILED=Error
#XFLD: status text
RUNNING=Córrer
#XFLD: status text
none=Cap
#XFLD: status text
realtime=Temps real
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Subtasca
#XFLD: New Data available in the file
NEW_DATA=Dades noves

#XFLD: text for values shown in column Replication Status
txtOff=Desactivat
#XFLD: text for values shown in column Replication Status
txtInitializing=S’està inicialitzant
#XFLD: text for values shown in column Replication Status
txtLoading=Carregant
#XFLD: text for values shown in column Replication Status
txtActive=Actiu
#XFLD: text for values shown in column Replication Status
txtAvailable=Disponible
#XFLD: text for values shown in column Replication Status
txtError=Error
#XFLD: text for values shown in column Replication Status
txtPaused=En pausa
#XFLD: text for values shown in column Replication Status
txtDisconnected=Desconnectat
#XFLD: text for partially Persisted views
partiallyPersisted=Desat de forma persistent i parcial

#XFLD: activity text
REPLICATE=Replicar
#XFLD: activity text
REMOVE_REPLICATED_DATA=Eliminar dades replicades
#XFLD: activity text
DISABLE_REALTIME=Desactivar replicació de dades en temps real
#XFLD: activity text
REMOVE_PERSISTED_DATA=Eliminar dades persistides
#XFLD: activity text
PERSIST=Persistir
#XFLD: activity text
EXECUTE=Executar
#XFLD: activity text
TASKLOG_CLEANUP=Tacklog_Cleanup
#XFLD: activity text
CANCEL_REPLICATION=Cancel·lar replicació
#XFLD: activity text
MODEL_IMPORT=Importació de model
#XFLD: activity text
NONE=Cap
#XFLD: activity text
CANCEL_PERSISTENCY=Cancel·lar persistència
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Analitzar vista
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Cancel·lar analitzador de vistes

#XFLD: severity text
INFORMATION=Informació
#XFLD: severity text
SUCCESS=Èxit
#XFLD: severity text
WARNING=Advertència
#XFLD: severity text
ERROR=Error
#XFLD: text for values shown for Ascending sort order
SortInAsc=Classificar en sentit ascendent
#XFLD: text for values shown for Descending sort order
SortInDesc=Classificar en sentit descendent
#XFLD: filter text for task log columns
Filter=Filtre
#XFLD: object text for task log columns
Object=Objecte
#XFLD: space text for task log columns
crossSpace=Espai

#XBUT: label for remote data access
REMOTE=Remot
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Replicat (en temps real)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Replicat (captura)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=La replicació en temps real està bloquejada a causa d'un error. Un cop s'hagi corregit, podreu fer servir l'acció "Reintentar" per continuar amb la replicació en temps real.
ERROR_MSG=La replicació en temps real està bloquejada a causa d'un error.
RETRY_FAILED_ERROR=Procés de reintent fallit amb un error.
LOG_INFO_DETAILS=No es genera cap log quan les dades es repliquen en mode en temps real. Els logs que es visualitzen estan relacionats amb accions anteriors.

#XBUT: Partitioning label
partitionMenuText=Particions
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Crear partició
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Editar partició
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Suprimir partició
#XFLD: Initial text
InitialPartitionText=Definir particions especificant els criteris per dividir grans conjunts de dades en conjunts més petits.
DefinePartition=Definir particions
#XFLD: Message text
partitionChangedInfo=La definició de la partició ha canviat des de l'última replicació. Les modificacions s'aplicaran a la propera càrrega de dades.
#XFLD: Message text
REAL_TIME_WARNING=El particionament només s'aplica quan es carrega una captura de pantalla nova. No s'aplica a la replicació en temps real.
#XFLD: Message text
loadSelectedPartitions=S''ha iniciat el desament persistent de dades per a les particions seleccionades de ''{0}''
#XFLD: Message text
loadSelectedPartitionsError=Error en el desament persistent de dades per a les particions seleccionades de ''{0}''
#XFLD: Message text
viewpartitionChangedInfo=La definició de partició ha canviat des de la darrera execució de persistència. Per aplicar els canvis, la propera càrrega de dades serà una instantània completa que inclourà les particions bloquejades. Un cop hagi conclòs aquesta càrrega, podreu executar dades per a les particions individuals.
#XFLD: Message text
viewpartitionChangedInfoLocked=La definició de la partició ha canviat des de l'última execució de la persistència. Per aplicar les modificacions, la propera càrrega de dades serà una instantània completa, excepte els intervals de partició bloquejats i sense modificacions. Un cop conclosa aquesta càrrega, podreu tornar a carregar les particions seleccionades.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Replicació de taula
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Programar replicació
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Crear programa
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Editar programa
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Suprimir programa
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Carregar captura nova
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Iniciar replicació de dades
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Eliminar dades replicades
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Activar accés en temps real
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Activar replicació de dades en temps real
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Desactivar replicació de dades en temps real
#XFLD: Message for replicate table action
replicateTableText=Replicació de taula
#XFLD: Message for replicate table action
replicateTableTextNew=Replicació de dades
#XFLD: Message to schedule task
scheduleText=Programa
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Veure persistència
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Persistència de dades
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Carregar captura nova
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Iniciar persistència de dades
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Eliminar dades persistides
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Processament particionat
#XBUT: Label for scheduled replication
scheduledTxt=Programat
#XBUT: Label for statistics button
statisticsTxt=Estadístiques
#XBUT: Label for create statistics
createStatsTxt=Crear estadístiques
#XBUT: Label for edit statistics
editStatsTxt=Editar estadístiques
#XBUT: Label for refresh statistics
refreshStatsTxt=Actualitzar estadístiques
#XBUT: Label for delete statistics
dropStatsTxt=Suprimir estadístiques
#XMSG: Create statistics success message
statsSuccessTxt=S''han començat a crear estadístiques de tipus {0} per a {1}.
#XMSG: Edit statistics success message
statsEditSuccessTxt=S''ha començat a modificar el tipus d''estadístiques a {0} per a {1}.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=S’han començat a actualitzar les estadístiques per a {0}.
#XMSG: Drop statistics success message
statsDropSuccessTxt=Les estadístiques s’han suprimit correctament per a {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=Error en crear les estadístiques
#XMSG: Edit statistics error message
statsEditErrorTxt=Error en modificar les estadístiques
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Error en actualitzar les estadístiques
#XMSG: Drop statistics error message
statsDropErrorTxt=Error en suprimir les estadístiques
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Segur que voleu eliminar les estadístiques de les dades?
startPersistencyAdvisorLabel=Iniciar analitzador de vistes

#Partition related texts
#XFLD: Label for Column
column=Columna
#XFLD: Label for No of Partition
noOfPartitions=Nombre de particions
#XFLD: Label for Column
noOfParallelProcess=Nombre de processos paral·lels
#XFLD: Label text
noOfLockedPartition=Nombre de particions bloquejades
#XFLD: Label for Partition
PARTITION=Particions
#XFLD: Label for Column
AVAILABLE=Disponible
#XFLD: Statistics Label
statsLabel=Estadístiques
#XFLD: Label text
COLUMN=Columna:
#XFLD: Label text
PARALLEL_PROCESSES=Processos paral·lels:
#XFLD: Label text
Partition_Range=Rang de partició
#XFLD: Label text
Name=Nom
#XFLD: Label text
Locked=Bloquejat
#XFLD: Label text
Others=ALTRES
#XFLD: Label text
Delete=Suprimir
#XFLD: Label text
LoadData=Carregar particions seleccionades
#XFLD: Label text
LoadSelectedData=Carregar particions seleccionades
#XFLD: Confirmation text
LoadNewPersistenceConfirm=Es carregarà una nova captura de pantalla per a totes les particions desbloquejades i modificades, no només les que heu seleccionat. Voleu continuar?
#XFLD: Label text
Continue=Continuar

#XFLD: Label text
PARTITIONS=Particions
#XFLD: Label text
ADD_PARTITIONS=+ Afegir partició
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Afegir partició
#XFLD: Label text
deleteRange=Suprimir partició
#XFLD: Label text
LOW_PLACE_HOLDER=Introduir valor inferior
#XFLD: Label text
HIGH_PLACE_HOLDER=Introduir valor superior
#XFLD: tooltip text
lockedTooltip=Bloquejar partició després de càrrega inicial

#XFLD: Button text
Edit=Editar
#XFLD: Button text
CANCEL=Cancel·lar

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Actualització de les últimes estadístiques
#XFLD: Statistics Fields
STATISTICS=Estadístiques

#XFLD:Retry label
TEXT_Retry=Reintentar
#XFLD:Retry label
TEXT_Retry_tooltip=Reintentar replicació en temps real un cop resolt l'error.
#XFLD: text retry
Retry=Confirmar
#XMG: Retry confirmation text
retryConfirmationTxt=L'última replicació en temps real a conclòs amb un error.\n Confirmeu que s'hagi corregit i que es pot tornar a intentar la replicació en temps real.
#XMG: Retry success text
retrySuccess=Nou intent del procés iniciat correctament.
#XMG: Retry fail text
retryFail=Error en reintentar el procés.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Crear estadístiques
#XMSG: activity message for edit statistics
DROP_STATISTICS=Suprimir estadístiques
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Actualitzar estadístiques
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Editar estadístiques
#XMSG: Task log message started task
taskStarted=S''ha iniciat la tasca {0}.
#XMSG: Task log message for finished task
taskFinished=La tasca {0} ha conclòs amb l''estat {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=La tasca {0} ha conclòs a les {2} amb l''estat {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=La tasca {0} té paràmetres d''entrada.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=La tasca {0} ha conclòs amb un error inesperat. L''estat s''ha fixat en {1}.
#XMSG: Task log message for failed task
failedToEnd=No s''ha pogut fixar l''estat en {0} o no s''ha pogut eliminar el bloqueig.
#XMSG: Task log message
lockNotFound=No es pot finalitzar el procés perquè falta el bloqueig: és possible que s'hagi cancel·lat la tasca.
#XMSG: Task log message failed task
failedOverwrite=La tasca {0} ja està bloquejada per {1}. Per tant, ha generat l''error següent: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=El bloqueig d'aquesta tasca ha estat assumit per una altra tasca.
#XMSG: Task log message failed takeover
failedTakeover=No s'ha pogut assumir una tasca existent.
#XMSG: Task log message successful takeover
successTakeover=Calia alliberar el bloqueig sobrant. S'ha fixat el bloqueig nou per a aquesta tasca.
#XMSG: Tasklog Dialog Details
txtDetails=Les declaracions remotes que s'han processat durant l'execució es poden veure obrint el monitor de consultes remotes, en els detalls dels missatges específics de partició.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=Les instruccions SQL remotes s'han suprimit de la base de dades i no es poden visualitzar.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Les consultes remotes que tenen connexions assignades a altres espais no es poden visualitzar. Aneu a Monitor de consulta remota i utilitzeu l'ID d'instrucció per filtrar-les.
#XMSG: Task log message for parallel check error
parallelCheckError=La tasca no es pot processar perquè hi ha una altra tasca que s'està executant i que ja la bloqueja.
#XMSG: Task log message for parallel running task
parallelTaskRunning=Ja s’està executant una tasca en conflicte.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Estat {0} durant l''execució amb l''ID de correlació {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=L'usuari assignat no té els privilegis necessaris per executar aquesta tasca.

#XBUT: Label for open in Editor
openInEditor=Obrir en editor
#XBUT: Label for open in Editor
openInEditorNew=Obrir al Generador de dades
#XFLD:Run deails label
runDetails=Executar detalls
#XFLD: Label for Logs
Logs=Logs
#XFLD: Label for Settings
Settings=Opcions
#XFLD: Label for Save button
Save=Desar
#XFLD: Label for Standard
Standard_PO=Optimitzat per millorar el rendiment (recomanat)
#XFLD: Label for Hana low memory processing
HLMP_MO=Optimitzat per a la memòria
#XFLD: Label for execution mode
ExecutionMode=Mode d'execució
#XFLD: Label for job execution
jobExecution=Mode de processament
#XFLD: Label for Synchronous
syncExec=Sincrònic
#XFLD: Label for Asynchronous
asyncExec=Asincrònic
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Utilitzar predeterminat (asincrònic, podria canviar més endavant)
#XMSG: Save settings success
saveSettingsSuccess=S'ha modificat el mode d'execució SAP HANA.
#XMSG: Save settings failure
saveSettingsFailed=No s'ha pogut modificar el mode d'execució SAP HANA.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=S'ha modificat l'execució de tasques.
#XMSG: Job Execution change failed
jobExecSettingFailed=No s'ha pogut modificar l'execució de tasques.
#XMSG: Text for Type
typeTxt=Tipus
#XMSG: Text for Monitor
monitorTxt=Supervisar
#XMSG: Text for activity
activityTxt=Activitat
#XMSG: Text for metrics
metricsTxt=Mètrica
#XTXT: Text for Task chain key
TASK_CHAINS=Cadena de tasques
#XTXT: Text for View Key
VIEWS=Veure
#XTXT: Text for remote table key
REMOTE_TABLES=Taula remota
#XTXT: Text for Space key
SPACE=Espai
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Node de computació flexible
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Flux de replicació
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Cerca intel·ligent
#XTXT: Text for Local Table
LOCAL_TABLE=Taula local
#XTXT: Text for Data flow key
DATA_FLOWS=Flux de dades
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=Procediment SQLScript
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=Cadena de processos BW
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Veure al monitor
#XTXT: Task List header text
taskListHeader=Llista de tasques ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=No es poden recuperar les mètriques de les execucions històriques d'un flux de dades.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=En aquests moments no s'estan carregant els detalls de l'execució complets. Intenteu actualitzar.
#XFLD: Label text for the Metrices table header
metricesColLabel=Etiqueta d'operador
#XFLD: Label text for the Metrices table header
metricesType=Tipus
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Recompte de registres
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Executar la cadena de tasques
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=S'ha iniciat l'execució de la cadena de tasques.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=S''ha iniciat l''execució de la cadena de tasques per a {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Error en executar la cadena de tasques.
#XTXT: Execute button label
runLabel=Executar
#XTXT: Execute button label
runLabelNew=Iniciar execució
#XMSG: Filter Object header
chainsFilteredTableHeader=Filtrat per objecte: {0}
#XFLD: Parent task chain label
parentChainLabel=Cadena de tasques superiors:
#XFLD: Parent task chain unauthorized
Unauthorized=Sense autorització per veure
#XFLD: Parent task chain label
parentTaskLabel=Tasca superior:
#XTXT: Task status
NOT_TRIGGERED=No s'ha desencadenat
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Entrar en el mode de pantalla completa
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Sortir del mode de pantalla completa
#XTXT: Close Task log details right panel
closeRightColumn=Tancar secció
#XTXT: Sort Text
sortTxt=Classificar
#XTXT: Filter Text
filterTxt=Filtrar
#XTXT: Filter by text to show list of filters applied
filterByTxt=Filtrar per
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Més de 5 minuts
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Més de 15 minuts
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Més d'1 hora
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Última hora
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Últimes 24 hores
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Últim mes
#XTXT: Messages title text
messagesText=Missatges

#XTXT Statistics information message
statisticsInfo=No es poden crear estadístiques per a taules remotes amb accés a dades ''Replicat''. Per fer-ho, elimineu les dades replicades al monitor de detalls de taules remotes.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Anar al monitor de detalls de taules remotes

#XTXT: Repair latest failed run label
retryRunLabel=Reintentar última execució
#XTXT: Repair failed run label
retryRun=Reintentar execució
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=S'ha iniciat l'execució de reintent de la cadena de tasques
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=L'execució de reintent de la cadena de tasques ha estat fallida
#XMSG: Task chain child elements name
taskChainRetryChildObject=Tasca {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Tasca nova
#XFLD Analyzed View
analyzedView=Vista analitzada
#XFLD Metrics
Metrics=Mètrica
#XFLD Partition Metrics
PartitionMetrics=Mètrica de partició
#XFLD Entities
Messages=Log de tasques
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Encara no s'han definit particions
#XTXT: Description message for empty partition data
partitionEmptyDescText=Creeu particions especificant els criteris per dividir grans volums de dades en particions més petites i fàcils de gestionar.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Encara no hi ha logs
#XTXT: Description message for empty runs data
runsEmptyDescText=Quan inicieu una activitat nova (carregar una captura nova, iniciar l'analitzador de vistes...), aquí trobareu els logs relacionats.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=La configuració del node de computació flexible {0} no està actualitzada en conseqüència. Comproveu la definició.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=S''ha produït un error en el procés per afegir el node de computació flexible {0}.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=S''ha iniciat la creació i activació de la rèplica per a l''objecte d''origen ''{0}''.''{1}'' al node de computació flexible {2}.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=S''ha iniciat l''eliminació de la rèplica per a l''objecte d''origen ''{0}''.''{1}'' des del node de computació flexible {2}.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=No s''ha pogut crear i activar la rèplica per a l''objecte d''origen ''{0}''.''{1}''.
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=No s''ha pogut eliminar la rèplica per a l''objecte d''origen ''{0}''.''{1}''.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=S''ha iniciat l''encaminament de la computació de consultes analítiques de l''espai {0} al node de computació flexible {1}.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=No s''ha pogut encaminar la computació de consultes analítiques de l''espai {0} al node de computació flexible corresponent.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=S''ha iniciat el reencaminament de la computació de consultes analítiques de l''espai {0} des del node de computació flexible {1}.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=No s''ha pogut reencaminar el càlcul de consultes analítiques de l''espai {0}al coordinador.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=S''ha iniciat la cadena de tasques {0} per al node de computació flexible corresponent {1}.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=No s''ha pogut generar la cadena de tasques per al node de computació flexible {0}.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=S''ha iniciat l''aprovisionament del node de computació flexible {0} amb el pla de mida indicat: vCPUs: {1}, memòria (GiB): {2} i mida d''emmagatzematge (GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=Ja s''ha aprovisionat el node de computació flexible {0}.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=Ja s''ha desaprovisionat el node de computació flexible {0}.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=L''operació no està permesa. Atureu el node de computació flexible {0} i reinicieu-lo per actualitzar el pla de mida.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=S''ha produït un error en el procés per eliminar el node de computació flexible {0}.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=L''execució actual del node de computació flexible {0} ha superat el temps d''espera.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=S''està verificant l''estat del node de computació flexible {0}...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=La generació de cadenes de tasques per al node de computació flexible {0} està bloquejada, per això pot ser que encara s''estigui executant la cadena {1}.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=L''objecte d''origen ''{0}''.''{1}'' està replicat com una dependència de la vista ''{2}''.''{3}''.
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=No es pot replicar la taula "{0}"."{1}", ja que la replicació de la taula de files està obsoleta.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=S'ha superat el nombre màxim de nodes d'informàtica flexible per instància de SAP HANA Cloud.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=L''operació d''execució del node de computació flexible {0} encara està en curs.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Per problemes tècnics, s''ha aturat la supressió de la rèplica per a la taula d''origen {0}. Torneu a provar-ho més tard.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=Per problemes tècnics, s''ha aturat la creació de la rèplica per a la taula d''origen {0}. Torneu a provar-ho més tard.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=No es pot iniciar un node d'informàtica flexible perquè s'ha assolit el límit d'utilització o perquè encara no s'han assignat hores de bloc de càlcul.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=S''està carregant la cadena de tasques i preparant l''execució d''un total de {0} tasques que formen part d''aquesta cadena.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Ja s'està executant una tasca en conflicte.
#XMSG: Replication will change
txt_replication_change=El tipus de replicació canviarà.
txt_repl_viewdetails=Veure detalls

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Sembla que s'ha produït un error en tornar a intentar l'última execució, ja que l'execució de la tasca anterior ha fallat abans que es pogués generar el pla.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=L''espai "{0}" està bloquejat.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=L''activitat {0} requereix que es desplegui la taula local {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=L''activitat {0} requereix que la captura delta estigui activada per a la taula local {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=L''activitat {0} requereix que la taula local {1} emmagatzemi dades a l''arxiu d''objectes.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=L''activitat {0} requereix que la taula local {1} emmagatzemi dades a la base de dades.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=S''estan començant a eliminar els registres suprimits de la taula local {0}.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=S''estan començant a suprimir tots els registres de la taula local {0}.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=S''estan començant a suprimir els registres de la taula local {0} d''acord amb la condició de filtre {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=S''ha produït un error en eliminar els registres suprimits de la taula local {0}.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=S''ha produït un error en suprimir tots els registres de la taula local {0}.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Se suprimiran tots els registres processats completament amb el tipus de modificació "Suprimit", que tenen més de {0} dies.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Se suprimiran tots els registres processats completament amb el tipus de modificació "Suprimit", que tenen més de {0}.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS=S''han suprimit {0} registres.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} registres s''han marcat per a la supressió.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=S''han eliminat els registres suprimits de la taula local {0}.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=S''han suprimit tots els registres de la taula local {0}.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Es comença a marcar els registres com a "Suprimits" per a la taula local {0}.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=S''estan començant a marcar els registres com a "Suprimit" per a la taula local {0} d''acord amb la condició de filtre {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Es completa el marcatge de registres com a "Suprimits" per a la taula local {0}.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=S''estan carregant les modificacions de dades temporalment a la taula {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Es carreguen les modificacions de dades temporalment a la taula {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Es processen les modificacions de dades i s''eliminen de la taula {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Dades de la connexió.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Començant a optimitzar la taula local (fitxer).
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=S'ha produït un error en optimitzar la taula local (fitxer).
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=S'ha produït un error. S'ha aturat l'optimització de la taula local (fitxer).
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Optimitzant la taula local (fitxer)...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=S'ha optimitzat la taula local (fitxer).
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=La taula local (fitxer) està optimitzada.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=La taula està optimitzada amb les columnes d''ordre Z: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=S'ha produït un error. S'ha aturat el truncament de la taula local (fitxer).
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Truncant la taula local (fitxer)...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=S'ha eliminat la ubicació de memòria intermèdia d'entrada per a la taula local (fitxer).

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Començant a fer el vacuum de la taula local (fitxer) (eliminar tots els registres processats completament).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=S'ha produït un error en fer el vacuum de la taula local (fitxer).
#XMSG: Task log message
LTF_VACUUM_STOPPED=S'ha produït un error. S'ha aturat el vaccum de la taula local (fitxer).
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Fent el vacuum de la taula local (fitxer)...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=El vacuum és complet.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Se suprimiran tots els registres processats completament que tenen més de {0} dies.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Se suprimiran tots els registres processats completament de més de {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Començant a fusionar nous registres amb la taula local (fitxer).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Començant a fusionar nous registres amb la taula local (fitxer) mitjançant l'opció "Fusionar dades automàticament".
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Inici de la fusió de nous registres amb la Taula local (fitxer). Aquesta tasca s'ha iniciat mitjançant la Sol·licitud de fusió API.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Fusionant nous registres amb la taula local (fitxer).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=S'ha produït un error en fusionar nous registres amb la taula local (fitxer).
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=La taula local (fitxer) està fusionada.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=S'ha produït un error. S'ha aturat la fusió de la taula local (fitxer).
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=La fusió de la Taula local (fitxer) ha fallat a causa d'un error, però l'operació s'ha realitzat parcialment correctament i algunes dades ja s'han fusionat.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=S''ha produït un error del temps en espera. L''activitat  {0} s''ha executat durant {1} hores.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=No s'ha pogut iniciar l'execució asíncrona a causa d'una càrrega de sistema elevada. Obriu el "Monitor del sistema" i verifiqueu les tasques en execució.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=S'ha cancel·lat l'execució asíncrona.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=La tasca {0} s''ha executat dins dels límits de la memòria de {1} i {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=La tasca {0} s''ha executat amb l''ID de recurs {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=Cercar i substituir ha començat a la Taula local (fitxer).
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Cercar i substituir ha finalitzat a la Taula local (fitxer).
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Cercar i substituir ha donat error a la Taula local (fitxer).

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=S'ha produït un error. S'ha aturat l'actualització d'estadístiques per a la taula local (fitxer).

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=La tasca ha estat errònia perquè s'ha produït un error de falta de memòria a la base de dades de SAP HANA.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=S'ha produït un error a la tasca a causa d'un rebuig de control d'admissió a SAP HANA.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=La tasca ha estat fallida perquè hi ha massa connexions actives de SAP HANA.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=L'operació Reintentar no s'ha pogut realitzar perquè només es permeten reintents en el node superior d'una cadena de tasques imbricada.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Els logs de tasques secundàries fallides ja no estan disponibles perquè es pugui Reintentar.


####Metrics Labels

performanceOptimized=Optimitzat per millorar el rendiment
memoryOptimized=Optimitzat per a la memòria

JOB_EXECUTION=Execució de tasques
EXECUTION_MODE=Mode d'execució
NUMBER_OF_RECORDS_OVERALL=Número de registres persistits
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Número de registres llegits de la font remota
RUNTIME_MS_REMOTE_EXECUTION_TIME=Temps de processament de font remota
MEMORY_CONSUMPTION_GIB=Memòria màxima de SAP HANA
NUMBER_OF_PARTITIONS=Número de particions
MEMORY_CONSUMPTION_GIB_OVERALL=Memòria màxima de SAP HANA
NUMBER_OF_PARTITIONS_LOCKED=Número de particions bloquejades
PARTITIONING_COLUMN=Columna de partició
HANA_PEAK_CPU_TIME=Temps de CPU de SAP HANA
USED_IN_DISK=Emmagatzematge utilitzat
INPUT_PARAMETER_PARAMETER_VALUE=Paràmetre d’entrada
INPUT_PARAMETER=Paràmetre d’entrada
ECN_ID=Nom del node d'informàtica flexible

DAC=Controls d'accés a dades
YES=Sí
NO=No
noofrecords=Nombre de registres
partitionpeakmemory=Memòria màxima de SAP HANA
value=Valor
metricsTitle=Mètrica ({0})
partitionmetricsTitle=Particions ({0})
partitionLabel=Partició
OthersNotNull=Valors no inclosos en intervals
OthersNull=Valors nuls
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Opcions utilitzades per a l'última execució de persistència de dades: 
#XMSG: Message for input parameter name
inputParameterLabel=Paràmetre d’entrada
#XMSG: Message for input parameter value
inputParameterValueLabel=Valor
#XMSG: Message for persisted data
inputParameterPersistedLabel=Persistit a
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Suprimir dades
REMOVE_DELETED_RECORDS=Eliminar registres suprimits
MERGE_FILES=Fusionar fitxers
OPTIMIZE_FILES=Optimitzar fitxers
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Mostra al monitor de SAP BW Bridge

ANALYZE_PERFORMANCE=Analitzar rendiment
CANCEL_ANALYZE_PERFORMANCE=Cancel·lar Analitzar rendiment

#XFLD: Label for frequency column
everyLabel=Cada
#XFLD: Plural Recurrence text for Hour
hoursLabel=Hores
#XFLD: Plural Recurrence text for Day
daysLabel=Dies
#XFLD: Plural Recurrence text for Month
monthsLabel=Mesos
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuts

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Guia de resolució de problemes de persistència de vistes</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Memòria esgotada per al procés. No es poden desar dades de forma persistent per a la vista "{0}". Consulteu el Portal d''ajuda per obtenir més informació sobre els errors de falta de memòria. Valoreu l''opció de consultar l''Analitzador de vistes per estudiar la vista de la complexitat del consum de memòria.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=No aplicable
OPEN_BRACKET=(
CLOSE_BRACKET=)
