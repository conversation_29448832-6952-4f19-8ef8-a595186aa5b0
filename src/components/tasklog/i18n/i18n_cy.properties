
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Manylion Log
#XFLD: Header
TASK_LOGS=Logiau Tasgau ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Rhediadau ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Gweld Manylion
#XFLD: Button text
STOP=Stopio Rhedeg
#XFLD: Label text
RUN_START=Dechrau Rhediad Diwethaf
#XFLD: Label text
RUN_END=Gorffen Rhediad Diwethaf
#XFLD: Label text
RUNTIME=Hyd
#XTIT: Count for Messages
txtDetailMessages=Negeseuon ({0})
#XFLD: Label text
TIME=Stamp Amser
#XFLD: Label text
MESSAGE=Neges
#XFLD: Label text
TASK_STATUS=Categori
#XFLD: Label text
TASK_ACTIVITY=Gweithgaredd
#XFLD: Label text
RUN_START_DETAILS=Dechrau
#XFLD: Label text
RUN_END_DETAILS=Gorffen
#XFLD: Label text
LOGS=Rhediadau
#XFLD: Label text
STATUS=Statws
#XFLD: Label text
RUN_STATUS=Statws Rhedeg
#XFLD: Label text
Runtime=Hyd
#XFLD: Label text
RuntimeTooltip=Hyd (aa : mm : ee)
#XFLD: Label text
TRIGGEREDBY=Wedi'i Sbarduno gan
#XFLD: Label text
TRIGGEREDBYNew=Rhedwyd gan
#XFLD: Label text
TRIGGEREDBYNewImp=Proses Rhedeg Wedi Cychwyn
#XFLD: Label text
EXECUTIONTYPE=Math o Weithrediad
#XFLD: Label text
EXECUTIONTYPENew=Math o Redeg
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Gofod Cadwyn Rhiant
#XFLD: Refresh tooltip
TEXT_REFRESH=Adnewyddu
#XFLD: view Details link
VIEW_ERROR_DETAILS=Gweld Manylion
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Llwytho Manylion Ychwanegol i Lawr
#XMSG: Download completed
downloadStarted=Lawrlwythiad Wedi Dechrau
#XMSG: Error while downloading content
errorInDownload=Roedd gwall wrth lwytho i lawr.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Gweld Manylion
#XBTN: cancel button of task details dialog
TXT_CANCEL=Canslo
#XBTN: back button from task details
TXT_BACK=Yn ôl
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Tasg wedi'i Chwblhau
#XFLD: Log message with failed status
MSG_LOG_FAILED=Tasg wedi Methu
#XFLD: Master and detail table with no data
No_Data=Dim Data
#XFLD: Retry tooltip
TEXT_RETRY=Ailgynnig
#XFLD: Cancel Run label
TEXT_CancelRun=Canslo’r broses Rhedeg
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Llwytho Glanhau wedi Methu
#XMSG:button copy sql statement
txtSQLStatement=Copïo Datganiad SQL
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Agor Monitor Ymholiadau Pell
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=I ddangos y datganiadau SQL pell, cliciwch "Agor Monitor Ymholiadau Pell".
#XMSG:button ok
txtOk=Iawn
#XMSG: button close
txtClose=Cau
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=Mae tasg canslo’r broses rhedeg ar gyfer gwrthrych "{0}" wedi cychwyn.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=Mae tasg canslo’r broses rhedeg ar gyfer gwrthrych "{0}" wedi methu.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=Dydy tasg canslo''r broses rhedeg ar gyfer gwrthrych "{0}" ddim yn bosibl bellach oherwydd bod y statws dyblygu wedi newid.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Does dim statws Rhedeg ar gyfer logiau tasgau.
#XMSG: message for conflicting task
Task_Already_Running=Mae tasg sy’n gwrthdaro wrthi’n rhedeg yn barod ar gyfer y gwrthrych "{0}".
#XFLD: Label for no task log with running state title
actionInfo=Gwybodaeth am Weithredu
#XMSG Copied to clipboard
copiedToClip=Wedi’i Gopïo i’r Clipfwrdd
#XFLD copy
Copy=Copïo
#XFLD copy correlation ID
CopyCorrelationID=Copïo ID Cydberthyniad
#XFLD Close
Close=Cau
#XFLD: show more Label
txtShowMore=Dangos Mwy
#XFLD: message Label
messageLabel=Neges:
#XFLD: details Label
detailsLabel=Manylion:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Gweithredu Datganiad \r\n SQL:
#XFLD:statementId Label
statementIdLabel=ID Datganiad:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Nifer y Datganiadau SQL \r\n Pell:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Nifer y Datganiadau
#XFLD: Space Label
txtSpaces=Gofod
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Gofodau Heb Awdurdod
#XFLD: Privilege Error Text
txtPrivilegeError=Does gennych chi ddim ddigon o hawliau i weld y data hwn.
#XFLD: Label for Object Header
DATA_ACCESS=Mynediad Data
#XFLD: Label for Object Header
SCHEDULE=Amserlennu
#XFLD: Label for Object Header
DETAILS=Manylion
#XFLD: Label for Object Header
LATEST_UPDATE=Diweddarwyd Ddiwethaf
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Newid Diweddaraf (Ffynhonnell)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Amlder Adnewyddu
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Amlder a Drefnwyd
#XFLD: Label for Object Header
NEXT_RUN=Rhediad Nesaf
#XFLD: Label for Object Header
CONNECTION=Cysylltiad
#XFLD: Label for Object Header
DP_AGENT=Asiant DP
#XFLD: Label for Object Header
USED_IN_MEMORY=Cof wedi'i ddefnyddio ar gyfer Storio (MiB)
#XFLD: Label for Object Header
USED_DISK=Disg wedi'i defnyddio ar gyfer Storio (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Maint yn y Cof (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Maint ar y Disg (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Nifer y Cofnodion

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Gosod i Wedi Methu
SET_TO_FAILED_ERR=Roedd y dasg hon yn rhedeg ond gosododd y defnyddiwr statws y dasg hon i WEDI METHU.
#XFLD: Label for stopped failed
FAILLOCKED=Rhediad Eisoes ar Waith
#XFLD: sub status STOPPED
STOPPED=Wedi'i Stopio
STOPPED_ERR=Cafodd y dasg hon ei stopio, ond ni lwyddwyd i ddychwelyd.
#XFLD: sub status CANCELLED
CANCELLED=Wedi'i Ganslo
CANCELLED_ERR=Cafodd y rhediad tasg hwn ei ganslo, ar ôl iddo ddechrau. Yn yr achos hwn, cafodd y data ei rolio yn ôl a'i adfer i'r cyflwr a oedd yn bodoli cyn i'r rhediad tasg gael ei sbarduno i ddechrau.
#XFLD: sub status LOCKED
LOCKED=Wedi Cloi
LOCKED_ERR=Roedd yr un dasg eisoes yn rhedeg, felly ni ellir rhedeg y dasg hon yn gyfochrog â chyflawniad tasg sy'n bodoli eisoes.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Eithriad Tasg
TASK_EXCEPTION_ERR=Daeth y dasg hon ar draws gwall amhenodol wrth weithredu.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Eithriad Cyflawni Tasg
TASK_EXECUTE_EXCEPTION_ERR=Daeth y dasg hon ar draws gwall wrth ei chyflawni.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Heb ei Ddilysu
UNAUTHORIZED_ERR=Doedd dim modd awdurdodi'r defnyddiwr, neu mae wedi'i gloi neu ei ddileu.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Gwaharddedig
FORBIDDEN_ERR=Does gan y defnyddiwr a neilltuwyd ddim y caniatâd angenrheidiol i gyflawni'r dasg hon.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Heb ei Sbarduno
FAIL_NOT_TRIGGERED_ERR=Doedd dim modd cyflawni'r dasg hon oherwydd toriad yn y system neu am nad yw rhyw ran o'r system gronfa ddata ar gael ar adeg y gweithrediad a gynlluniwyd. Arhoswch am yr amser gweithredu dasg nesaf a drefnwyd neu aildrefnu'r dasg.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Amserlen wedi'i Chanslo
SCHEDULE_CANCELLED_ERR=Doedd dim modd gweithredu'r dasg hon oherwydd gwall mewnol. Cysylltwch â SAP Support a darparwch yr ID cydberthynas a'r stamp amser o wybodaeth fanylion log y dasg hon.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Rhediad blaenorol ar waith
SUCCESS_SKIPPED_ERR=Nid yw gweithredu'r dasg hon wedi'i sbarduno oherwydd bod rhediad blaenorol o'r un dasg yn dal ar waith.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Perchennog Ar Goll
FAIL_OWNER_MISSING_ERR=Doedd dim modd gweithredu'r dasg hon oherwydd nad oes ganddi ddefnyddiwr system penodedig. Dylech neilltuo defnyddiwr perchennog i'r swydd.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Caniatâd Ddim Ar Gael
FAIL_CONSENT_NOT_AVAILABLE_ERR=Dydych chi heb awdurdodi SAP i redeg cadwyni tasgau nac i drefnu tasgau integreiddio data ar eich rhan. Dewiswch yr opsiwn a ddarperir i roi eich caniatâd.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Caniatâd Wedi Dod i Ben
FAIL_CONSENT_EXPIRED_ERR=Mae'r awdurdodiad sy'n caniatáu i SAP redeg cadwyni tasgau neu drefnu tasgau integreiddio data ar eich rhan wedi dod i ben. Dewiswch yr opsiwn a ddarperir i adnewyddu eich caniatâd.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Caniatâd Annilys
FAIL_CONSENT_INVALIDATED_ERR=Doedd dim modd cyflawni'r dasg hon, fel arfer oherwydd newid yn ffurfweddiad Darparwr Hunaniaeth y tenant. Yn yr achos hwnnw, does dim modd rhedeg na threfnu unrhyw dasgau newydd yn enw'r defnyddiwr dan sylw. Os yw'r defnyddiwr a neilltuwyd yn dal i fodoli yn y IdP newydd, tynnwch y caniatâd amserlennu yn ôl ac yna ei roi eto. Os nad yw'r defnyddiwr a neilltuwyd yn bodoli bellach, dylech neilltuo perchennog tasg newydd a rhoi'r caniatâd amserlennu tasgau gofynnol. Gweler y nodyn SAP canlynol: https://launchpad.support.sap.com/#/notes/3089828 i gael rhagor o wybodaeth.
TASK_EXECUTOR_ERROR=Gweithredwr Tasgau
TASK_EXECUTOR_ERROR_ERR=Daeth y dasg hon ar draws gwall mewnol, yn ystod y camau paratoi ar gyfer gweithredu mae'n debyg, a doedd dim modd dechrau'r dasg.
PREREQ_NOT_MET=Rhagofynion heb eu bodloni
PREREQ_NOT_MET_ERR=Doedd dim modd rhedeg y dasg hon oherwydd problemau gyda’i diffiniad. Er enghraifft, nid yw’r gwrthrych wedi’i osod, mae cadwyn dasgau’n cynnwys rhesymeg gylchol, neu mae SQL gwedd yn annilys.
RESOURCE_LIMIT_ERROR=Gwall Terfynau Adnoddau
RESOURCE_LIMIT_ERROR_ERR=Methu perfformio tasg ar hyn o bryd oherwydd bod dim digon o adnoddau ar gael neu oherwydd prysurdeb.
FAIL_CONSENT_REFUSED_BY_UMS=Caniatâd wedi'i wrthod
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Nid oedd modd cwblhau'r dasg hon mewn cadwyni tasgau neu rediadau sydd wedi'u trefnu gan fod ffurfweddiad Darparwr Manylion Adnabod defnyddiwr wedi newid ar y tenant. I gael rhagor o wybodaeth, darllenwch y nodyn SAP canlynol: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Wedi'i Amserlennu
#XFLD: status text
SCHEDULEDNew=Parhaol
#XFLD: status text
PAUSED=Wedi'i Rewi
#XFLD: status text
DIRECT=Uniongyrchol
#XFLD: status text
MANUAL=Â llaw
#XFLD: status text
DIRECTNew=Syml
#XFLD: status text
COMPLETED=Wedi Cwblhau
#XFLD: status text
FAILED=Wedi Methu
#XFLD: status text
RUNNING=Yn Rhedeg
#XFLD: status text
none=Dim
#XFLD: status text
realtime=Amser Real
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Is-dasg
#XFLD: New Data available in the file
NEW_DATA=Data Newydd

#XFLD: text for values shown in column Replication Status
txtOff=Diffodd
#XFLD: text for values shown in column Replication Status
txtInitializing=Wrthi’n Ymgychwyn
#XFLD: text for values shown in column Replication Status
txtLoading=Wrthi'n Llwytho
#XFLD: text for values shown in column Replication Status
txtActive=Gweithredol
#XFLD: text for values shown in column Replication Status
txtAvailable=Ar Gael
#XFLD: text for values shown in column Replication Status
txtError=Gwall
#XFLD: text for values shown in column Replication Status
txtPaused=Wedi'i Rewi
#XFLD: text for values shown in column Replication Status
txtDisconnected=Wedi Datgysylltu
#XFLD: text for partially Persisted views
partiallyPersisted=Wedi Parhau'n Rhannol

#XFLD: activity text
REPLICATE=Dyblygu
#XFLD: activity text
REMOVE_REPLICATED_DATA=Tynnu Data Dyblyg
#XFLD: activity text
DISABLE_REALTIME=Analluogi Dyblygu Data Amser Real
#XFLD: activity text
REMOVE_PERSISTED_DATA=Tynnu Data Parhad
#XFLD: activity text
PERSIST=Parhau
#XFLD: activity text
EXECUTE=Gweithredu
#XFLD: activity text
TASKLOG_CLEANUP=Tacklog_Cleanup
#XFLD: activity text
CANCEL_REPLICATION=Canslo’r Dyblygu
#XFLD: activity text
MODEL_IMPORT=Mewngludo Model
#XFLD: activity text
NONE=Dim
#XFLD: activity text
CANCEL_PERSISTENCY=Canslo Parhad
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Dadansoddi Gwedd
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Canslo Dadansoddwr Gwedd

#XFLD: severity text
INFORMATION=Gwybodaeth
#XFLD: severity text
SUCCESS=Llwyddiant
#XFLD: severity text
WARNING=Rhybudd
#XFLD: severity text
ERROR=Gwall
#XFLD: text for values shown for Ascending sort order
SortInAsc=Trefnu o'r Dechrau i'r Diwedd
#XFLD: text for values shown for Descending sort order
SortInDesc=Trefnu o'r Diwedd i'r Dechrau
#XFLD: filter text for task log columns
Filter=Hidlydd
#XFLD: object text for task log columns
Object=Gwrthrych
#XFLD: space text for task log columns
crossSpace=Gofod

#XBUT: label for remote data access
REMOTE=Pell
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Wedi Dyblygu (Amser Real)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Wedi Dyblygu (Ciplun)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=Mae dyblygu amser real yn cael ei rwystro oherwydd gwall. Ar ôl cywiro'r gwall, gallwch ddefnyddio'r weithred 'Ailgynnig' i barhau gyda dyblygu amser real.
ERROR_MSG=Mae dyblygu amser real wedi'i rwystro oherwydd gwall.
RETRY_FAILED_ERROR=Rhoi cynnig arall ar y broses wedi methu â gwall.
LOG_INFO_DETAILS=Does dim logiau’n cael eu creu pan fydd data yn cael ei ddyblygu mewn modd amser real. Mae’r logiau sydd i’w gweld yn ymwneud â gweithredoedd blaenorol.

#XBUT: Partitioning label
partitionMenuText=Wrthi'n Rhannu
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Creu Rhaniad
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Golygu Rhaniad
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Dileu Rhaniad
#XFLD: Initial text
InitialPartitionText=Diffinio rhaniadau trwy nodi meini prawf i rannu setiau data mawr i setiau llai.
DefinePartition=Diffinio Rhaniadau
#XFLD: Message text
partitionChangedInfo=Mae’r diffiniad rhannu wedi newid ers y dyblygiad diwethaf. Bydd y newidiadau’n cael eu rhoi ar waith ar y llwyth data nesaf.
#XFLD: Message text
REAL_TIME_WARNING=Dim ond wrth lwytho ciplun newydd y defnyddir rhaniad. Nid yw'n cael ei gymhwyso ar gyfer dyblygu amser real.
#XFLD: Message text
loadSelectedPartitions=Wedi cychwyn parhau â data ar gyfer rhaniadau a ddewiswyd ar gyfer "{0}"
#XFLD: Message text
loadSelectedPartitionsError=Wedi methu parhau â data ar gyfer rhaniadau a ddewiswyd ar gyfer "{0}"
#XFLD: Message text
viewpartitionChangedInfo=Mae diffiniad o raniad wedi newid ers rhedeg y broses barhau ddiwethaf. I roi'r newidiadau ar waith, bydd y llwyth data nesaf yn giplun llawn gan gynnwys rhaniadau wedi'u cloi. Pan fydd y llwyth llawn wedi'i gwblhau, byddwch yn gallu rhedeg data ar gyfer rhaniadau sengl,
#XFLD: Message text
viewpartitionChangedInfoLocked=Mae diffiniad o raniad wedi newid ers rhedeg y broses barhau ddiwethaf. I roi'r newidiadau ar waith, bydd y llwyth data nesaf yn giplun llawn heblaw am ystodau rhaniadau wedi'u cloi a heb eu newid. Pan fydd y llwyth wedi'i gwblhau, byddwch yn gallu Llwytho Rhaniadau a Ddewiswyd eto.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Dyblygu Tabl
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Dyblygu Amserlen
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Creu Amserlen
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Golygu Amserlen
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Dileu Amserlen
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Llwytho Ciplun Newydd
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Dechrau Dyblygu Data
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Tynnu Data Dyblyg
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Galluogi Mynediad Amser Real
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Galluogi Dyblygu Data Amser Real
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Analluogi Dyblygu Data Amser Real
#XFLD: Message for replicate table action
replicateTableText=Dyblygiad Tabl
#XFLD: Message for replicate table action
replicateTableTextNew=Dyblygu Data
#XFLD: Message to schedule task
scheduleText=Amserlennu
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Parhad Gwedd
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Parhad Data
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Llwytho Ciplun Newydd
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Dechrau Parhad Data
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Tynnu Data Parhad
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Prosesu Rhanedig
#XBUT: Label for scheduled replication
scheduledTxt=Wedi'i Amserlennu
#XBUT: Label for statistics button
statisticsTxt=Ystadegau
#XBUT: Label for create statistics
createStatsTxt=Creu Ystadegau
#XBUT: Label for edit statistics
editStatsTxt=Golygu Ystadegau
#XBUT: Label for refresh statistics
refreshStatsTxt=Adnewyddu Ystadegau
#XBUT: Label for delete statistics
dropStatsTxt=Gollwng Ystadegau
#XMSG: Create statistics success message
statsSuccessTxt=Wedi dechrau creu ystadegau math {0} ar gyfer {1}.
#XMSG: Edit statistics success message
statsEditSuccessTxt=Wedi dechrau newid y math ystadegau i {0} ar gyfer {1}.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Wedi dechrau adnewyddu ystadegau ar gyfer {0}.
#XMSG: Drop statistics success message
statsDropSuccessTxt=Ystadegau wedi''u dileu’n llwyddiannus ar gyfer {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=Gwall wrth greu ystadegau
#XMSG: Edit statistics error message
statsEditErrorTxt=Gwall wrth newid ystadegau
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Gwall wrth adnewyddu ystadegau
#XMSG: Drop statistics error message
statsDropErrorTxt=Gwall wrth ollwng ystadegau
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Ydych chi'n siŵr eich bod am ollwng yr ystadegau data?
startPersistencyAdvisorLabel=Dechrau Dadansoddwr Gwedd

#Partition related texts
#XFLD: Label for Column
column=Colofn
#XFLD: Label for No of Partition
noOfPartitions=Nifer y Rhaniadau
#XFLD: Label for Column
noOfParallelProcess=Nifer y Prosesau Cyfochrog
#XFLD: Label text
noOfLockedPartition=Nifer y Rhaniadau wedi'u Cloi
#XFLD: Label for Partition
PARTITION=Rhaniadau
#XFLD: Label for Column
AVAILABLE=Ar Gael
#XFLD: Statistics Label
statsLabel=Ystadegau
#XFLD: Label text
COLUMN=Colofn:
#XFLD: Label text
PARALLEL_PROCESSES=Prosesau Paralel:
#XFLD: Label text
Partition_Range=Ystod Rhaniad
#XFLD: Label text
Name=Enw
#XFLD: Label text
Locked=Wedi Cloi
#XFLD: Label text
Others=ERAILL
#XFLD: Label text
Delete=Dileu
#XFLD: Label text
LoadData=Llwytho Rhaniadau a Ddewiswyd
#XFLD: Label text
LoadSelectedData=Llwytho Rhaniadau a Ddewiswyd
#XFLD: Confirmation text
LoadNewPersistenceConfirm=Bydd hyn yn llwytho ciplun newydd ar gyfer pob rhaniad sydd wedi’i ddatgloi neu ei newid, nid yn unig y rhai rydych chi wedi’u dewis. Ydych chi eisiau bwrw ymlaen?
#XFLD: Label text
Continue=Bwrw ymlaen

#XFLD: Label text
PARTITIONS=Rhaniadau
#XFLD: Label text
ADD_PARTITIONS=+ Ychwanegu Rhaniad
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Ychwanegu Rhaniad
#XFLD: Label text
deleteRange=Dileu Rhaniad
#XFLD: Label text
LOW_PLACE_HOLDER=Rhowch werth isel
#XFLD: Label text
HIGH_PLACE_HOLDER=Rhowch werth uchel
#XFLD: tooltip text
lockedTooltip=Cloi rhaniad ar ôl llwytho'r tro cyntaf

#XFLD: Button text
Edit=Golygu
#XFLD: Button text
CANCEL=Canslo

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Diweddariad Ystadegau Diwethaf
#XFLD: Statistics Fields
STATISTICS=Ystadegau

#XFLD:Retry label
TEXT_Retry=Ailgynnig
#XFLD:Retry label
TEXT_Retry_tooltip=Ailgynnig y dyblygu amser real ar ôl datrys y gwall.
#XFLD: text retry
Retry=Cadarnhau
#XMG: Retry confirmation text
retryConfirmationTxt=Daeth y dyblygu amser real diwethaf i ben gyda gwall.\n Cadarnhewch fod y gwall wedi'i gywiro ac y gellir ailgychwyn dyblygu amser real.
#XMG: Retry success text
retrySuccess=Dechreuwyd y broses ail-gynnig yn llwyddiannus.
#XMG: Retry fail text
retryFail=Proses ail-gynnig wedi methu.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Creu Ystadegau
#XMSG: activity message for edit statistics
DROP_STATISTICS=Gollwng Ystadegau
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Adnewyddu Ystadegau
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Golygu Ystadegau
#XMSG: Task log message started task
taskStarted=Mae’r dasg {0} wedi cychwyn.
#XMSG: Task log message for finished task
taskFinished=Daeth y dasg {0} i ben gyda statws {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=Daeth y dasg {0} i ben am {2} gyda statws {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=Mae gan y dasg {0} paramedr mewnbwn.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=Daeth y dasg {0} i ben gyda gwall annisgwyl. Mae statws y dasg wedi’i gosod i {1}.
#XMSG: Task log message for failed task
failedToEnd=Wedi methu â gosod statws i {0} neu wedi methu tynnu’r clo.
#XMSG: Task log message
lockNotFound=Dydyn ni ddim yn gallu gorffen y broses gan fod y clo ar goll: efallai fod y dasg wedi cael ei chanslo.
#XMSG: Task log message failed task
failedOverwrite=Mae’r dasg {0} wedi’i chloi’n barod gan {1}. Felly, mae wedi methu gyda’r gwall canlynol: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Tasg arall wedi cymryd drosodd clo y dasg yma.
#XMSG: Task log message failed takeover
failedTakeover=Wedi methu cymryd drosodd tasg sy’n bodoli’n barod.
#XMSG: Task log message successful takeover
successTakeover=Roedd yn rhaid rhyddhau'r clo dros ben. Mae'r clo newydd ar gyfer y dasg yma wedi'i osod.
#XMSG: Tasklog Dialog Details
txtDetails=Mae modd dangos y datganiadau pell a gafodd eu prosesu yn ystod y rhediad drwy agor y monitor ymholiadau pell, ym manylion y negeseuon penodol i raniad.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=Mae datganiadau SQL pell wedi cael eu dileu o’r gronfa ddata ac nid oes modd eu dangos.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Does dim modd dangos ymholiadau pell sydd â chysylltiadau wedi’u neilltuo i ofodau eraill. Ewch i’r Monitor Ymholiadau Pell a defnyddio’r ID datganiad i’w hidlo.
#XMSG: Task log message for parallel check error
parallelCheckError=Does dim modd prosesu’r dasg gan fod tasg arall yn rhedeg ac yn rhwystro’r dasg hon yn barod.
#XMSG: Task log message for parallel running task
parallelTaskRunning=Mae tasg sy’n gwrthdaro eisoes yn rhedeg.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Statws {0} yn ystod proses rhedeg gyda ID Cydberthyniad {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=Does gan y defnyddiwr a neilltuwyd ddim y caniatâd angenrheidiol i gyflawni'r dasg hon.

#XBUT: Label for open in Editor
openInEditor=Agor yn y Golygydd
#XBUT: Label for open in Editor
openInEditorNew=Agor yn y Lluniwr Data
#XFLD:Run deails label
runDetails=Manylion Rhedeg
#XFLD: Label for Logs
Logs=Logiau
#XFLD: Label for Settings
Settings=Gosodiadau
#XFLD: Label for Save button
Save=Cadw
#XFLD: Label for Standard
Standard_PO=Wedi'i Optimeiddio ar gyfer Perfformio (Argymhellir)
#XFLD: Label for Hana low memory processing
HLMP_MO=Wedi’i Optimeiddio ar gyfer Cof
#XFLD: Label for execution mode
ExecutionMode=Modd Rhedeg
#XFLD: Label for job execution
jobExecution=Modd Prosesu
#XFLD: Label for Synchronous
syncExec=Syncronaidd
#XFLD: Label for Asynchronous
asyncExec=Ansyncronaidd
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Defnyddio Diofyn (Ansyncronaidd, efallai y bydd yn newid yn y dyfodol)
#XMSG: Save settings success
saveSettingsSuccess=Wedi newid Modd Gweithredu SAP HANA.
#XMSG: Save settings failure
saveSettingsFailed=Wedi methu newid Modd Gweithredu SAP HANA.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Wedi newid Gweithredu Tasg.
#XMSG: Job Execution change failed
jobExecSettingFailed=Wedi methu newid Gweithredu Tasg.
#XMSG: Text for Type
typeTxt=Math
#XMSG: Text for Monitor
monitorTxt=Monitro
#XMSG: Text for activity
activityTxt=Gweithgaredd
#XMSG: Text for metrics
metricsTxt=Metrics
#XTXT: Text for Task chain key
TASK_CHAINS=Cadwyn Tasgau
#XTXT: Text for View Key
VIEWS=Gwedd
#XTXT: Text for remote table key
REMOTE_TABLES=Tabl Pell
#XTXT: Text for Space key
SPACE=Gofod
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Cwgn Cyfrifyddu Elastig
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Llif Dyblygu
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Chwilio Deallus
#XTXT: Text for Local Table
LOCAL_TABLE=Tabl Lleol
#XTXT: Text for Data flow key
DATA_FLOWS=Llif Data
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=Gweithdrefn Sgript SQL
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=Cadwyn Broses BW
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Gweld yn y Monitor
#XTXT: Task List header text
taskListHeader=Rhestr Tasgau ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Dim modd adfer metrigau ar gyfer rhediadau hanesyddol llif data.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=Dydy'r manylion cyflawni rhediad ddim yn llwytho ar hyn o bryd. Rhowch gynnig ar adnewyddu.
#XFLD: Label text for the Metrices table header
metricesColLabel=Label Gweithredwr
#XFLD: Label text for the Metrices table header
metricesType=Math
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Cyfrif Cofnodion
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Rhedeg y Gadwyn Tasgau
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Rhedeg y gadwyn tasgau wedi dechrau.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Rhedeg y gadwyn tasgau wedi dechrau ar gyfer {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Wedi methu rhedeg y gadwyn tasgau.
#XTXT: Execute button label
runLabel=Rhedeg
#XTXT: Execute button label
runLabelNew=Dechrau Rhedeg
#XMSG: Filter Object header
chainsFilteredTableHeader=Wedi Hidlo yn ôl Gwrthrych: {0}
#XFLD: Parent task chain label
parentChainLabel=Cadwyn Tasgau Rhiant:
#XFLD: Parent task chain unauthorized
Unauthorized=Heb ei Awdurdodi i'w Weld
#XFLD: Parent task chain label
parentTaskLabel=Tasgau Rhiant:
#XTXT: Task status
NOT_TRIGGERED=Heb ei Sbarduno
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Mynd i’r Modd Sgrin Lawn
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Gadael y Modd Sgrin Lawn
#XTXT: Close Task log details right panel
closeRightColumn=Cau'r Adran
#XTXT: Sort Text
sortTxt=Trefnu
#XTXT: Filter Text
filterTxt=Hidlo
#XTXT: Filter by text to show list of filters applied
filterByTxt=Hidlo yn ôl
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Mwy na 5 Munud
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Mwy na 15 Munud
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Mwy nag 1 Awr
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Awr Olaf
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=24 Awr Diwethaf
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Mis Diwethaf
#XTXT: Messages title text
messagesText=Negeseuon

#XTXT Statistics information message
statisticsInfo=Does dim modd creu ystadegau ar gyfer tablau pell gyda mynediad data "Dyblyg". I greu ystadegau, tynnwch y data a ddyblygwyd yn y Monitor Manylion Tablau Pell.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Mynd i'r Monitor Manylion Tablau Pell

#XTXT: Repair latest failed run label
retryRunLabel=Rhoi Ailgynnig ar y Rhediad Diweddaraf
#XTXT: Repair failed run label
retryRun=Rhoi Ailgynnig ar y Rhediad
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Rhedeg ailgynnig y gadwyn tasgau wedi dechrau
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Rhedeg ailgynnig y gadwyn tasgau wedi methu
#XMSG: Task chain child elements name
taskChainRetryChildObject=Tasg {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Tasg Newydd
#XFLD Analyzed View
analyzedView=Gwedd wedi'i dadansoddi
#XFLD Metrics
Metrics=Metrigau
#XFLD Partition Metrics
PartitionMetrics=Metrigau Rhaniad
#XFLD Entities
Messages=Log Tasgau
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Does dim rhaniadau wedi'u diffinio ar hyn o bryd.
#XTXT: Description message for empty partition data
partitionEmptyDescText=Gallwch greu rhaniadau drwy nodi meini prawf i rannu swm uchel o ddata yn rannau llai sy'n haws eu rheoli.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Dim logiau ar gael eto
#XTXT: Description message for empty runs data
runsEmptyDescText=Pan fyddwch chi'n dechrau gweithgaredd newydd (Llwytho ciplun newydd, dechrau dadansoddwr gweddau...), byddwch yn gweld y logiau cysylltiedig yma.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=Dydy ffurfweddiad {0} y cwgn cyfrifyddu elastig ddim yn cael ei gynnal yn gyfatebol. Gwiriwch eich diffiniad.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Proses i ychwanegu''r cwgn cyfrifyddu elastig {0} wedi methu.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Mae creu ac ysgogi replica ar gyfer y gwrthrych ffynhonnell "{0}"."{1}" yn y cwgn cyfrifo elastig {2}, wedi dechrau.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Mae tynnu replica ar gyfer y gwrthrych ffynhonnell "{0}"."{1}" o’r cwgn cyfrifyddu elastig {2}, wedi dechrau.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Mae creu ac ysgogi replica ar gyfer y gwrthrych ffynhonnell "{0}"."{1}", wedi methu.
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Mae tynnu replica ar gyfer gwrthrych ffynhonnell "{0}"."{1}", wedi methu.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Wedi dechrau llwybro''r ymholiadau dadansoddol i gyfrifo''r gofod {0} i''r cwgn cyfrifyddu elastig {1}.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Wedi methu llwybro''r ymholiadau dadansoddol i gyfrifo''r gofod {0} i''r cwgn cyfrifyddu elastig.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Wedi dechrau ail-lwybro''r ymholiadau dadansoddol i gyfrifo''r gofod {0} yn ôl o''r cwgn cyfrifyddu elastig {1}.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Wedi methu ail-lwybro''r ymholiadau dadansoddol i gyfrifo''r gofod {0} yn ôl i''r cydlynydd.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Mae cadwyn dasgau {0} i''r cwgn cyfrifyddu elastig {1} cyfatebol wedi''i sbarduno.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Wedi methu creu cadwyn dasgau ar gyfer y cwgn cyfrifyddu elastig {0}.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=Mae darparu’r cwgn cyfrifyddu elastig {0} wedi dechrau gyda’r cynllun maint penodol: vCPUs: {1}, cof(GiB): {2} a maint storio(GiB): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=Mae’r cwgn cyfrifyddu elastig {0} eisoes wedi cael ei ddarparu.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=Mae’r cwgn cyfrifyddu elastig {0} eisoes wedi cael ei ddad-darparu.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=Ni chaniateir hyn. Rhowch stop ar y cwgn cyfrifyddu elastig  {0} a''i ailgychwyn i ddiweddaru''r cynllun maint.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Proses i dynnu''r cwgn cyfrifyddu elastig {0} wedi methu.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=Mae rhediad cyfredol y cwgn cyfrifyddu elastig {0} wedi dod i ben.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=Wrthi’n gwirio statws cwgn cyfrifyddu elastig {0}...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=Mae creu cadwyn dasgau ar gyfer cwgn cyfrifyddu elastig {0} wedi’i gloi, felly efallai fod cadwyn {1} yn dal i redeg.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=Mae gwrthrych ffynhonnell "{0}"."{1}" yn cael ei ddyblygu fel dibyniaeth o wedd "{2}"."{3}".
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Methu dyblygu''r tabl "{0}"."{1}", gan fod proses dyblygu tabl rhes wedi''i datgymeradwyo.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Mae mwy na'r nifer fwyaf o gygnau cyfrifyddu elastig a ganiateir am bob fersiwn SAP HANA Cloud wedi'u defnyddio.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=Mae''r weithred rhedeg ar gyfer y cwgn cyfrifyddu elastig {0} yn dal ar waith.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Yn sgil problemau technegol, mae dileu''r replica ar gyfer tabl ffynhonnell {0} ar stop. Rhowch gynnig arall arni wedyn.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=Yn sgil problemau technegol, mae creu''r replica ar gyfer tabl ffynhonnell {0} ar stop. Rhowch gynnig arall arni wedyn.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Methu dechrau cwgn cyfrifyddu elastig oherwydd eich bod wedi cyrraedd y terfyn defnydd, neu oherwydd nad oes oriau-bloc cyfrifyddu wedi'u neilltuo ar hyn o bryd.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Wrthi''n llwytho cadwyn tasgau a pharatoi i redeg {0} o dasgau sy''n rhan o''r gadwyn hon.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Mae tasg sy’n gwrthdaro eisoes yn rhedeg
#XMSG: Replication will change
txt_replication_change=Bydd y math dyblygu’n cael ei newid.
txt_repl_viewdetails=Gweld Manylion

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Mae'n ymddangos bod gwall wedi codi yn y broses ailgynnig diwethaf gan fod y broses rhedeg tasgau flaenorol wedi methu cyn bod modd creu'r cynllun.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=Gofod "{0}" wedi''i gloi.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=Mae''r weithgaredd {0} yn gofyn i''r tabl lleol {1} gael ei osod.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=Mae''r weithgaredd {0} yn gofyn i Delta Capture ei roi ymlaen ar gyfer tabl lleol {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=Mae''r weithgaredd {0} yn gofyn i''r tabl lleol {1} storio data yn y storfa gwrthrych.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=Mae''r weithgaredd {0} yn gofyn i''r tabl lleol {1} storio data yn y gronfa ddata.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Cychwyn tynnu''r cofnodion sydd wedi''u dileu ar gyfer y tabl pell {0}.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Cychwyn dileu pob cofnod ar gyfer y tabl pell {0}.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Dechrau dileu cofnodion ar gyfer y tabl lleol {0} yn ôl yr amod hidlo {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Gwall wrth dynnu''r cofnodion sydd wedi''u dileu ar gyfer y tabl pell {0}.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Gwall wrth ddileu pob cofnod ar gyfer y tabl pell {0}.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Wrthi''n dileu pob cofnod sydd wedi’i brosesu’n llawn gyda Math o Newid "Wedi Dileu", sy’n hŷn na {0} diwrnod.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Wrthi''n dileu pob cofnod sydd wedi’i brosesu’n llawn gyda Math o Newid "Wedi Dileu", sy’n hŷn na {0}.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS=Mae {0} cofnod wedi cael ei ddileu.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS=Mae {0} cofnod wedi cael eu marcio i gael eu dileu.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=Wedi gorffen tynnu''r cofnodion sydd wedi''u dileu ar gyfer y tabl pell {0}.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Wedi gorffen dileu pob cofnod ar gyfer y tabl pell {0}.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Cychwyn marcio cofnodion fel "Wedi Dileu" ar gyfer y tabl lleol {0}.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Dechrau marcio cofnodion fel "Wedi Dileu" ar gyfer y tabl lleol {0} yn ôl yr amod hidlo {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Wedi gorffen marcio''r cofnodion fel "Wedi Dileu" ar gyfer y tabl lleol {0}.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Mae''r newidiadau i''r data wrthi''n cael eu llwytho i''r tabl {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Mae newidiadau i''r data yn cael eu llwytho i''r tabl {0} dros dro.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Mae''r newidiadau i''r data yn cael eu prosesu ac yn cael eu dileu o''r tabl {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Manylion y cysylltiad.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Wrthi'n dechrau optimeiddio Tabl Lleol (Ffeil).
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Cafwyd gwall wrth optimeiddio Tabl Lleol (Ffeil).
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Cafwyd gwall. Mae'r broses optimeiddio Tabl Lleol (Ffeil) wedi stopio.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Wrthi'n optimeiddio Tabl Lleol (Ffeil)...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=Tabl Lleol (Ffeil) wedi'i optimeiddio.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=Tabl Lleol (Ffeil) wedi'i optimeiddio.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=Mae''r tabl wedi''i optimeiddio gyda''r colofnau Z-Order: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Cafwyd gwall. Mae'r broses tocio Tabl Lleol (Ffeil) wedi stopio.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Wrthi'n tocio Tabl Lleol (Ffeil)...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=Wedi gollwng lleoliad byffer am i mewn ar gyfer Tabl Lleol (Ffeil).

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Wrthi'n dechrau gwacáu (ewch ati i ddileu cofnodion sydd wedi'u prosesu'n llawn) Tabl Lleol (Ffeil).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Cafwyd gwall wrth wacάu Tabl Lleol (Ffeil).
#XMSG: Task log message
LTF_VACUUM_STOPPED=Cafwyd gwall. Mae'r broses gwacάu Tabl Lleol (Ffeil) wedi stopio.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Wrthi'n gwacάu Tabl Lleol (Ffeil)...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=Wedi gwacάu.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Wrthi''n dileu pob cofnod sydd wedi’i brosesu’n llawn sy’n hŷn na {0} diwrnod.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Wrthi''n dileu pob cofnod sydd wedi’i brosesu’n llawn sy’n hŷn na {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Wrthi'n dechrau cyfuno cofnodion newydd gyda Tabl Lleol (Ffeil).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Dechrau cyfuno cofnodion newydd â Thabl Lleol (Ffeil) gan ddefnyddio'r gosodiad "Cyfuno Data'n Awtomatig".
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Dechrau cyfuno cofnodion newydd â Thabl Lleol (Ffeil). Dechreuwyd y dasg hon trwy'r Cais Cyfuno API.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Wrthi'n cyfuno cofnodion newydd gyda Tabl Lleol (Ffeil).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Cafwyd gwall wrth gyfuno cofnodion newydd gyda Tabl Lleol (Ffeil).
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=Tabl Lleol (Ffeil) wedi'i gyfuno.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Cafwyd gwall. Mae'r broses cyfuno Tabl Lleol (Ffeil) wedi stopio.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=Wedi methu cyfuno'r Tabl Lleol (ffeil) oherwydd gwall, ond roedd y gweithrediad yn rhannol lwyddiannus, ac mae rhai data eisoes wedi'i gyfuno.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Gwall terfyn amser wedi codi. Mae''r gweithgaredd {0} wedi bod yn rhedeg ers {1} awr.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=Doedd dim modd dechrau'r cam anghydamserol gan fod llwyth mawr ar y system. Agorwch "Monitro System" a gwiriwch y tasgau sy'n rhedeg.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=Mae'r cam anghydamserol wedi'i ganslo.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=Roedd y dasg {0} wedi rhedeg o fewn y terfynau cof o {1} a {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=Roedd y dasg {0} wedi rhedeg gyda''r ID adnodd {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=Mae canfod a disodli wedi dechrau yn y Tabl Lleol (ffeil).
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Mae canfod a disodli wedi'i gwblhau yn y Tabl Lleol (ffeil).
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Mae canfod a disodli wedi methu yn y Tabl Lleol (ffeil).

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Cafwyd gwall. Mae'r broses o ddiweddaru ystadegau ar gyfer Tabl Lleol (Ffeil) wedi'i stopio.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=Mae'r dasg wedi methu oherwydd gwall dim cof ar ôl yng nghronfa ddata SAP HANA.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=Mae'r dasg wedi methu oherwydd Gwrthod Rheoli Mynediad SAP HANA.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=Mae'r dasg wedi methu oherwydd bod gormod o gysylltiadau gweithredol SAP HANA.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=Doedd dim modd perfformio'r weithred Ailgynnig oherwydd dim ond ar riant cadwyn tasgau wedi'i nythu y caniateir ailgynnig.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Dydy logiau tasgau plentyn sydd wedi methu ddim ar gael mwyach ar gyfer Ailgynnig er mwyn bwrw ymlaen.


####Metrics Labels

performanceOptimized=Wedi'i Optimeiddio ar gyfer Perfformio
memoryOptimized=Wedi’i Optimeiddio ar gyfer Cof

JOB_EXECUTION=Gweithredu Tasg
EXECUTION_MODE=Modd Rhedeg
NUMBER_OF_RECORDS_OVERALL=Nifer y Cofnodion wedi'u Parhau
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Nifer y Cofnodion o Ffynhonnell Bell
RUNTIME_MS_REMOTE_EXECUTION_TIME=Amser Prosesu Ffynhonnell Pell
MEMORY_CONSUMPTION_GIB=Cof Uchaf SAP HANA
NUMBER_OF_PARTITIONS=Nifer y Rhaniadau
MEMORY_CONSUMPTION_GIB_OVERALL=Cof Uchaf SAP HANA
NUMBER_OF_PARTITIONS_LOCKED=Nifer y Rhaniadau Ar Glo
PARTITIONING_COLUMN=Colofn Rannu
HANA_PEAK_CPU_TIME=Cyfanswm Amser CPU SAP HANA
USED_IN_DISK=Lle Storio wedi'i Ddefnyddio
INPUT_PARAMETER_PARAMETER_VALUE=Paramedr Mewnbwn
INPUT_PARAMETER=Paramedr Mewnbwn
ECN_ID=Enw'r Cwgn Cyfrifyddu Elastig

DAC=Rheolyddion Mynediad Data
YES=Iawn
NO=Na
noofrecords=Nifer y Cofnodion
partitionpeakmemory=Cof Uchaf SAP HANA
value=Gwerth
metricsTitle=Metrigau ({0})
partitionmetricsTitle=Rhaniadau ({0})
partitionLabel=Rhaniad
OthersNotNull=Gwerthoedd ddim wedi'u cynnwys mewn ystodau
OthersNull=Gwerthoedd nwl
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Gosodiadau a ddefnyddiwyd ar gyfer y broses rhedeg parhau data ddiwethaf:
#XMSG: Message for input parameter name
inputParameterLabel=Paramedr Mewnbwn
#XMSG: Message for input parameter value
inputParameterValueLabel=Gwerth
#XMSG: Message for persisted data
inputParameterPersistedLabel=Wedi Parhau Yn
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Dileu Data
REMOVE_DELETED_RECORDS=Tynnu Cofnodion Data
MERGE_FILES=Cyfuno Ffeiliau
OPTIMIZE_FILES=Optimeiddio Ffeiliau
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Gweld yn Monitor Pont SAP BW

ANALYZE_PERFORMANCE=Dadansoddi Perfformiad
CANCEL_ANALYZE_PERFORMANCE=Canslo Dadansoddiad Perfformiad

#XFLD: Label for frequency column
everyLabel=Pob
#XFLD: Plural Recurrence text for Hour
hoursLabel=Oriau
#XFLD: Plural Recurrence text for Day
daysLabel=Diwrnod
#XFLD: Plural Recurrence text for Month
monthsLabel=Mis
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Munud

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Gweld canllaw datrys problemau Parhad</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Dim mwy o gof ar gyfer y broses. Wedi methu parhau''r data ar gyfer gwedd "{0}". Holwch y Porth Cymorth i gael rhagor o wybodaeth am wallau cof. Cofiwch ystyried gwirio''r Dadansoddwr Gwedd i ddadansoddi''r wedd ar gyfer cymhlethdod defnydd cof.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Amherthnasol
OPEN_BRACKET=(
CLOSE_BRACKET=)
