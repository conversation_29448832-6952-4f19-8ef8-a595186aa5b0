
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Докладна інформація про журнал
#XFLD: Header
TASK_LOGS=Журнали завдань ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Прогонів ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Переглянути подробиці
#XFLD: Button text
STOP=Зупинити прогін
#XFLD: Label text
RUN_START=Початок останнього прогону
#XFLD: Label text
RUN_END=Завершення останнього прогону
#XFLD: Label text
RUNTIME=Тривалість
#XTIT: Count for Messages
txtDetailMessages=Повідомлення ({0})
#XFLD: Label text
TIME=Мітка часу
#XFLD: Label text
MESSAGE=Повідомлення
#XFLD: Label text
TASK_STATUS=Категорія
#XFLD: Label text
TASK_ACTIVITY=Операція
#XFLD: Label text
RUN_START_DETAILS=Початок
#XFLD: Label text
RUN_END_DETAILS=Завершення
#XFLD: Label text
LOGS=Прогони
#XFLD: Label text
STATUS=Статус
#XFLD: Label text
RUN_STATUS=Статус прогону
#XFLD: Label text
Runtime=Тривалість
#XFLD: Label text
RuntimeTooltip=Тривалість (гг : хх : сс)
#XFLD: Label text
TRIGGEREDBY=Ініціатор
#XFLD: Label text
TRIGGEREDBYNew=Ініціатор запуску
#XFLD: Label text
TRIGGEREDBYNewImp=Ініціатор запуску прогону
#XFLD: Label text
EXECUTIONTYPE=Тип виконання
#XFLD: Label text
EXECUTIONTYPENew=Тип прогону
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Батьківський простір ланцюга
#XFLD: Refresh tooltip
TEXT_REFRESH=Оновити
#XFLD: view Details link
VIEW_ERROR_DETAILS=Переглянути подробиці
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Завантажити додаткові відомості
#XMSG: Download completed
downloadStarted=Завантаження розпочато
#XMSG: Error while downloading content
errorInDownload=Під час завантаження сталася помилка.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Переглянути подробиці
#XBTN: cancel button of task details dialog
TXT_CANCEL=Скасувати
#XBTN: back button from task details
TXT_BACK=Назад
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Завдання завершено
#XFLD: Log message with failed status
MSG_LOG_FAILED=Завдання не виконано
#XFLD: Master and detail table with no data
No_Data=Немає даних
#XFLD: Retry tooltip
TEXT_RETRY=Повторити спробу
#XFLD: Cancel Run label
TEXT_CancelRun=Скасувати прогін
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Очистити невдале завантаження
#XMSG:button copy sql statement
txtSQLStatement=Копіювати SQL-оператор
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Відкрити монітор віддалених запитів
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=Щоб відобразити віддалені SQL-оператори, клацніть "Відкрити монітор віддалених запитів".
#XMSG:button ok
txtOk=OK
#XMSG: button close
txtClose=Закрити
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=Дію скасування прогону для об''єкта "{0}" розпочато.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=Не вдалося розпочати дію скасування прогону для об''єкта "{0}".
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=Скасувати прогін для об''єкта "{0}" не можна, оскільки статус реплікації змінено.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Немає журналів завдань зі статусом "Виконується".
#XMSG: message for conflicting task
Task_Already_Running=Конфліктне завдання вже виконується для об''єкта "{0}".
#XFLD: Label for no task log with running state title
actionInfo=Відомості про дію
#XMSG Copied to clipboard
copiedToClip=Скопійовано до буфера обміну
#XFLD copy
Copy=Копіювати
#XFLD copy correlation ID
CopyCorrelationID=Копіювати ідентифікатор кореляції
#XFLD Close
Close=Закрити
#XFLD: show more Label
txtShowMore=Показати більше
#XFLD: message Label
messageLabel=Повідомлення:
#XFLD: details Label
detailsLabel=Подробиці:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Виконуваний \r\nSQL-оператор
#XFLD:statementId Label
statementIdLabel=Ідентифікатор оператора:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Кількість віддалених \r\n SQL-операторів:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Кількість операторів
#XFLD: Space Label
txtSpaces=Простір
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Неавторизовані простори
#XFLD: Privilege Error Text
txtPrivilegeError=Ви не маєте достатніх привілеїв для перегляду цих даних.
#XFLD: Label for Object Header
DATA_ACCESS=Доступ до даних
#XFLD: Label for Object Header
SCHEDULE=Розклад
#XFLD: Label for Object Header
DETAILS=Подробиці
#XFLD: Label for Object Header
LATEST_UPDATE=Найостанніше оновлення
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Найостанніше змінення (джерело)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Частота оновлення
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Запланована частота
#XFLD: Label for Object Header
NEXT_RUN=Наступний запуск
#XFLD: Label for Object Header
CONNECTION=З’єднання
#XFLD: Label for Object Header
DP_AGENT=Агент підготовки даних
#XFLD: Label for Object Header
USED_IN_MEMORY=Пам'ять, використана для сховища (МіБ)
#XFLD: Label for Object Header
USED_DISK=Диск, використаний для сховища (МіБ)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Розмір у пам'яті (МіБ)
#XFLD: Label for Object Header
USED_DISK_NEW=Розмір на диску (МіБ)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Кількість записів

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Позначити як збій
SET_TO_FAILED_ERR=Це завдання виконувалося, але користувач встановив для цього завдання статус "ЗБІЙ".
#XFLD: Label for stopped failed
FAILLOCKED=Уже триває прогін
#XFLD: sub status STOPPED
STOPPED=Зупинено
STOPPED_ERR=Це завдання було зупинено, але відкочення не виконано.
#XFLD: sub status CANCELLED
CANCELLED=Скасовано
CANCELLED_ERR=Виконання цього завдання було скасовано після його початку. У цьому випадку дані було відкочено та відновлено до стану, який існував до початкового запуску завдання.
#XFLD: sub status LOCKED
LOCKED=Заблоковано
LOCKED_ERR=Те саме завдання вже виконувалося, тому це завдання не можна запускати паралельно з виконанням існуючого завдання.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Виключення завдання
TASK_EXCEPTION_ERR=Під час виконання цього завдання сталася невизначена помилка.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Виключення виконання завдання
TASK_EXECUTE_EXCEPTION_ERR=Під час виконання цього завдання сталася помилка.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Не авторизовано
UNAUTHORIZED_ERR=Користувача не вдалося автентифікувати, його заблоковано або видалено.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Заборонено
FORBIDDEN_ERR=Призначений користувач не має привілеїв, необхідних для виконання цього завдання.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Не ініційовано
FAIL_NOT_TRIGGERED_ERR=Це завдання не вдалося виконати через збій системи або через те, що якась частина системи бази даних була недоступна під час запланованого виконання. Зачекайте наступного запланованого часу виконання завдання або перенесіть це завдання.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Розклад скасовано
SCHEDULE_CANCELLED_ERR=Це завдання не вдалося виконати через внутрішню помилку. Зв’яжіться зі службою підтримки SAP і надайте їм ідентифікатор кореляції та позначку часу з відомостей у журналі цього завдання.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Триває попередній прогін
SUCCESS_SKIPPED_ERR=Виконання цього завдання не було запущено, оскільки попередній запуск того самого завдання все ще триває.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Відсутній власник
FAIL_OWNER_MISSING_ERR=Не вдалося виконати це завдання, оскільки для нього не призначено системного користувача. Призначте користувача-власника для цього завдання.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Згода недоступна
FAIL_CONSENT_NOT_AVAILABLE_ERR=Ви не надали SAP дозвіл запускати ланцюжки завдань або планувати завдання інтеграції даних від вашого імені. Виберіть потрібний варіант, щоб дати свою згоду.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Термін дії згоди минув
FAIL_CONSENT_EXPIRED_ERR=Термін дії дозволу для SAP на запуск ланцюжків завдань або планувати завдання інтеграції даних від вашого імені, минув. Виберіть потрібний варіант, щоб дати свою згоду.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Згода недійсна
FAIL_CONSENT_INVALIDATED_ERR=Це завдання не вдалося виконати, скоріше за все через зміну конфігурації постачальника ідентифікаційної інформації клієнта. У цьому випадку нові завдання не можуть бути запущені або заплановані від імені відповідного користувача. Якщо призначений користувач усе ще існує в новому постачальнику ідентифікаційної інформації, відкличте згоду на планування, а потім надайте її повторно. Якщо призначений користувач більше не існує, призначте нового власника завдання та надайте необхідну згоду на планування завдань. Перегляньте цю нотатку SAP: https://launchpad.support.sap.com/#/notes/3089828, щоб отримати додаткову інформацію.
TASK_EXECUTOR_ERROR=Виконавець завдання
TASK_EXECUTOR_ERROR_ERR=У цьому завданні сталася внутрішня помилка, ймовірно, під час підготовки до виконання, і завдання не вдалося запустити.
PREREQ_NOT_MET=Не виконано передумову
PREREQ_NOT_MET_ERR=Це завдання не можна виконати через проблему в його визначенні. Наприклад, об'єкт не розгорнуто, ланцюжок завдань містить циклічну логіку або код SQL подання недійсний.
RESOURCE_LIMIT_ERROR=Помилка через ліміт ресурсів
RESOURCE_LIMIT_ERROR_ERR=Наразі не вдається виконати завдання через недоступність достатньої кількості ресурсів або через те, що вони зайняті.
FAIL_CONSENT_REFUSED_BY_UMS=У згоді відмовлено
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Це завдання не можна виконати в межах запланованих прогонів або ланцюжків завдань через зміну в конфігурації провайдера ідентичностей користувача орендатора. Додаткову інформацію див. в цій нотатці SAP: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Заплановано
#XFLD: status text
SCHEDULEDNew=Постійно
#XFLD: status text
PAUSED=Призупинено
#XFLD: status text
DIRECT=Прямий
#XFLD: status text
MANUAL=Вручну
#XFLD: status text
DIRECTNew=Простий
#XFLD: status text
COMPLETED=Завершено
#XFLD: status text
FAILED=Не виконано
#XFLD: status text
RUNNING=Виконується
#XFLD: status text
none=Немає
#XFLD: status text
realtime=Реальний час
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Підзавдання
#XFLD: New Data available in the file
NEW_DATA=Нові дані

#XFLD: text for values shown in column Replication Status
txtOff=Вимк.
#XFLD: text for values shown in column Replication Status
txtInitializing=Ініціалізація
#XFLD: text for values shown in column Replication Status
txtLoading=Завантаження
#XFLD: text for values shown in column Replication Status
txtActive=Активний
#XFLD: text for values shown in column Replication Status
txtAvailable=Доступно
#XFLD: text for values shown in column Replication Status
txtError=Помилка
#XFLD: text for values shown in column Replication Status
txtPaused=На паузі
#XFLD: text for values shown in column Replication Status
txtDisconnected=Роз’єднано
#XFLD: text for partially Persisted views
partiallyPersisted=З частковим постійним відтворенням

#XFLD: activity text
REPLICATE=Реплікація
#XFLD: activity text
REMOVE_REPLICATED_DATA=Видалити репліковані дані
#XFLD: activity text
DISABLE_REALTIME=Вимкнути реплікацію даних у режимі реального часу
#XFLD: activity text
REMOVE_PERSISTED_DATA=Вилучити постійні дані
#XFLD: activity text
PERSIST=Постійно
#XFLD: activity text
EXECUTE=Виконати
#XFLD: activity text
TASKLOG_CLEANUP=Tacklog_Cleanup
#XFLD: activity text
CANCEL_REPLICATION=Скасувати реплікацію
#XFLD: activity text
MODEL_IMPORT=Імпортування моделі
#XFLD: activity text
NONE=Немає
#XFLD: activity text
CANCEL_PERSISTENCY=Скасувати постійне відтворення подання
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Аналізувати подання
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Скасувати аналізатор подання

#XFLD: severity text
INFORMATION=Інформація
#XFLD: severity text
SUCCESS=Успішно
#XFLD: severity text
WARNING=Застереження
#XFLD: severity text
ERROR=Помилка
#XFLD: text for values shown for Ascending sort order
SortInAsc=Сортувати за зростанням
#XFLD: text for values shown for Descending sort order
SortInDesc=Сортувати за спаданням
#XFLD: filter text for task log columns
Filter=Фільтр
#XFLD: object text for task log columns
Object=Об’єкт
#XFLD: space text for task log columns
crossSpace=Простір

#XBUT: label for remote data access
REMOTE=Віддалений
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Реплікований (реальний час)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Реплікований (знімок)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=Реплікацію в режимі реального часу заблоковано через помилку. Після виправлення помилки можна скористатися дією "Повторити", щоб продовжити реплікацію в режимі реального часу.
ERROR_MSG=Реплікацію в режимі реального часу заблоковано через помилку.
RETRY_FAILED_ERROR=Не вдалося повторно виконати через помилку.
LOG_INFO_DETAILS=Коли дані реплікуються в режимі реального часу, журнали не створюються. Показані журнали стосуються попередніх дій.

#XBUT: Partitioning label
partitionMenuText=Створення розділів
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Створити розділ
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Редагувати розділ
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Видалити розділ
#XFLD: Initial text
InitialPartitionText=Визначте розділи, вказавши критерії для поділу великих наборів даних на менші набори.
DefinePartition=Визначити розділи
#XFLD: Message text
partitionChangedInfo=Визначення розділу змінилося після останньої реплікації. Зміни буде застосовано під час наступного завантаження даних.
#XFLD: Message text
REAL_TIME_WARNING=Поділ застосовується лише під час завантаження нового знімка. Він не застосовується для реплікації в реальному часі.
#XFLD: Message text
loadSelectedPartitions=Розпочато постійне відтворення даних для вибраних розділів "{0}"
#XFLD: Message text
loadSelectedPartitionsError=Не вдалося активувати постійне відтворення даних для вибраних розділів "{0}"
#XFLD: Message text
viewpartitionChangedInfo=Визначення розділу змінилося після останнього прогону збереження. Щоб застосувати зміни, наступним завантаженням даних буде повний знімок, включно із заблокованими розділами. Після завершення цього повного завантаження ви зможете проганяти дані для окремих розділів.
#XFLD: Message text
viewpartitionChangedInfoLocked=Визначення розділу змінилося після останнього прогону збереження. Щоб застосувати зміни, наступним завантаженням даних буде повний знімок, за винятком заблокованих і не змінених діапазонів розділів. Після завершення цього завантаження ви зможете знову завантажити вибрані розділи.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Реплікація таблиці
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Запланувати реплікацію
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Створити розклад
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Змінити розклад
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Видалити розклад
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Завантажити новий знімок
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Почати реплікацію даних
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Видалити репліковані дані
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Увімкнути доступ у режимі реального часу
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Увімкнути реплікацію даних у режимі реального часу
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Вимкнути реплікацію даних у режимі реального часу
#XFLD: Message for replicate table action
replicateTableText=Реплікація таблиці
#XFLD: Message for replicate table action
replicateTableTextNew=Реплікація даних
#XFLD: Message to schedule task
scheduleText=Розклад
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Переглянути постійне подання
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Постійне відтворення даних
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Завантажити новий знімок
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Почати постійне відтворення даних
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Вилучити постійні дані
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Секційна обробка
#XBUT: Label for scheduled replication
scheduledTxt=Заплановано
#XBUT: Label for statistics button
statisticsTxt=Статистика
#XBUT: Label for create statistics
createStatsTxt=Створити статистику
#XBUT: Label for edit statistics
editStatsTxt=Редагувати статистику
#XBUT: Label for refresh statistics
refreshStatsTxt=Оновити статистику
#XBUT: Label for delete statistics
dropStatsTxt=Видалити статистику
#XMSG: Create statistics success message
statsSuccessTxt=Розпочато створення статистики типу {0} для {1}.
#XMSG: Edit statistics success message
statsEditSuccessTxt=Розпочато змінення типу статистики на {0} для {1}.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Розпочато оновлення статистики для {0}.
#XMSG: Drop statistics success message
statsDropSuccessTxt=Статистику успішно видалено для {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=Помилка під час створення статистики
#XMSG: Edit statistics error message
statsEditErrorTxt=Помилка під час змінення статистики
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Помилка під час оновлення статистики
#XMSG: Drop statistics error message
statsDropErrorTxt=Помилка під час видалення статистики
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Справді видалити дані статистики?
startPersistencyAdvisorLabel=Запустити Аналізатор подання

#Partition related texts
#XFLD: Label for Column
column=Стовпчик
#XFLD: Label for No of Partition
noOfPartitions=К-сть розділів
#XFLD: Label for Column
noOfParallelProcess=К-сть паралельних обробок
#XFLD: Label text
noOfLockedPartition=К-сть заблокованих розділів
#XFLD: Label for Partition
PARTITION=Розділи
#XFLD: Label for Column
AVAILABLE=Доступно
#XFLD: Statistics Label
statsLabel=Статистика
#XFLD: Label text
COLUMN=Стовпчик:
#XFLD: Label text
PARALLEL_PROCESSES=Паралельні процеси:
#XFLD: Label text
Partition_Range=Діапазон розділів
#XFLD: Label text
Name=Ім’я
#XFLD: Label text
Locked=Заблоковано
#XFLD: Label text
Others=ІНШІ
#XFLD: Label text
Delete=Видалити
#XFLD: Label text
LoadData=Завантажити вибрані розділи
#XFLD: Label text
LoadSelectedData=Завантажити вибрані розділи
#XFLD: Confirmation text
LoadNewPersistenceConfirm=Це призведе до завантаження нового знімку для всіх незаблокованих і змінених розділів, а не лише для тих, які вибрано. Продовжити?
#XFLD: Label text
Continue=Продовжити

#XFLD: Label text
PARTITIONS=Розділи
#XFLD: Label text
ADD_PARTITIONS=+ Додати розділ
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Додати розділ
#XFLD: Label text
deleteRange=Видалити розділ
#XFLD: Label text
LOW_PLACE_HOLDER=Введіть нижнє значення
#XFLD: Label text
HIGH_PLACE_HOLDER=Введіть верхнє значення
#XFLD: tooltip text
lockedTooltip=Блокування розділу після початкового завантаження

#XFLD: Button text
Edit=Редагувати
#XFLD: Button text
CANCEL=Скасувати

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Останнє оновлення статистики
#XFLD: Statistics Fields
STATISTICS=Статистика

#XFLD:Retry label
TEXT_Retry=Повторити спробу
#XFLD:Retry label
TEXT_Retry_tooltip=Повторити спробу реплікації в реальному часі після усунення помилки.
#XFLD: text retry
Retry=Підтвердити
#XMG: Retry confirmation text
retryConfirmationTxt=Остання реплікація в режимі реального часу завершилася з помилкою. \n Переконайтеся, що помилку виправлено й що реплікацію в режимі реального часу можна перезапустити.
#XMG: Retry success text
retrySuccess=Процес успішно розпочато повторно.
#XMG: Retry fail text
retryFail=Не вдалося повторити процес.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Створити статистику
#XMSG: activity message for edit statistics
DROP_STATISTICS=Видалити статистику
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Оновити статистику
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Редагувати статистику
#XMSG: Task log message started task
taskStarted=Завдання {0} розпочато.
#XMSG: Task log message for finished task
taskFinished=Завдання {0} завершено зі статусом {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=Завдання {0} завершилося о {2} зі статусом {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=Завдання "{0}" має параметри введення.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=Завдання {0} завершено з неочікуваною помилкою. Для завдання встановлено статус {1}.
#XMSG: Task log message for failed task
failedToEnd=Не вдалося встановити статус {0} або не вдалося зняти блокування.
#XMSG: Task log message
lockNotFound=Ми не можемо завершити процес, оскільки відсутнє блокування: можливо, завдання було скасовано.
#XMSG: Task log message failed task
failedOverwrite=Завдання {0} вже заблоковано користувачем {1}. Тому воно не виконано з такою помилкою: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Блокування цього завдання перейшло на інше завдання.
#XMSG: Task log message failed takeover
failedTakeover=Не вдалося перенести існуюче завдання.
#XMSG: Task log message successful takeover
successTakeover=Залишене блокування слід звільнити. Нове блокування для цього завдання встановлено.
#XMSG: Tasklog Dialog Details
txtDetails=Щоб відобразити віддалені оператори, оброблені під час прогону, можна відкрити монітор віддалених запитів і переглянути подробиці в повідомленнях, що стосуються розділів.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=Віддалені SQL-оператори видалено з бази даних, і їх відобразити не можна.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Віддалені запити, з'єднання яких присвоєно іншим просторам, відобразити не можна. Перейдіть до монітора віддалених запитів і скористайтеся ідентифікатором оператора, щоб їх відфільтрувати.
#XMSG: Task log message for parallel check error
parallelCheckError=Не вдається обробити завдання, оскільки виконується інше завдання й вже блокує це завдання.
#XMSG: Task log message for parallel running task
parallelTaskRunning=Конфліктне завдання вже виконується.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Статус {0} під час прогону з ідентифікатором кореляції {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=Призначений користувач не має привілеїв, необхідних для виконання цього завдання.

#XBUT: Label for open in Editor
openInEditor=Відкрити в редакторі
#XBUT: Label for open in Editor
openInEditorNew=Відкрити в Конструкторі моделей даних
#XFLD:Run deails label
runDetails=Подробиці прогону
#XFLD: Label for Logs
Logs=Журнали
#XFLD: Label for Settings
Settings=Настройки
#XFLD: Label for Save button
Save=Зберегти
#XFLD: Label for Standard
Standard_PO=Оптимізовано за продуктивністю (рекомендовано)
#XFLD: Label for Hana low memory processing
HLMP_MO=Оптимізовано за пам'яттю
#XFLD: Label for execution mode
ExecutionMode=Режим прогону
#XFLD: Label for job execution
jobExecution=Режим обробки
#XFLD: Label for Synchronous
syncExec=Синхронний
#XFLD: Label for Asynchronous
asyncExec=Асинхронний
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Використовувати усталене (асинхронний, може змінитися в майбутньому)
#XMSG: Save settings success
saveSettingsSuccess=Режим виконання SAP HANA успішно змінено.
#XMSG: Save settings failure
saveSettingsFailed=Не вдалося змінити режим виконання SAP HANA.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Виконання завдання змінено.
#XMSG: Job Execution change failed
jobExecSettingFailed=Змінити виконання завдання не вдалося.
#XMSG: Text for Type
typeTxt=Тип
#XMSG: Text for Monitor
monitorTxt=Монітор
#XMSG: Text for activity
activityTxt=Операція
#XMSG: Text for metrics
metricsTxt=Метрики
#XTXT: Text for Task chain key
TASK_CHAINS=Ланцюжок завдань
#XTXT: Text for View Key
VIEWS=Подання
#XTXT: Text for remote table key
REMOTE_TABLES=Віддалена таблиця
#XTXT: Text for Space key
SPACE=Простір
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Обчислювальний вузол Elastic
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Потік реплікації
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Інтелектуальний пошук
#XTXT: Text for Local Table
LOCAL_TABLE=Локальна таблиця
#XTXT: Text for Data flow key
DATA_FLOWS=Потік даних
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=Процедура скрипта SQL
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=Ланцюг процесів BW
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Переглянути в моніторі
#XTXT: Task List header text
taskListHeader=Список завдань ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Не вдалося отримати метрики для історичних прогонів потоку даних.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=Інформація про завершене виконання зараз не завантажується. Спробуйте оновити.
#XFLD: Label text for the Metrices table header
metricesColLabel=Надпис оператора
#XFLD: Label text for the Metrices table header
metricesType=Тип
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Кількість записів
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Прогін ланцюжка завдань
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Прогін ланцюжка завдань розпочато.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Прогін ланцюжка завдань розпочато для {0}.
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Не вдалося прогнати ланцюжок завдань.
#XTXT: Execute button label
runLabel=Прогін
#XTXT: Execute button label
runLabelNew=Почати прогін
#XMSG: Filter Object header
chainsFilteredTableHeader=Відфільтровано за об’єктом: {0}.
#XFLD: Parent task chain label
parentChainLabel=Батьківський ланцюжок завдань:
#XFLD: Parent task chain unauthorized
Unauthorized=Не авторизовано для перегляду
#XFLD: Parent task chain label
parentTaskLabel=Батьківське завдання:
#XTXT: Task status
NOT_TRIGGERED=Не ініційовано
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Перейти в повноекранний режим
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Вийти з повноекранного режиму
#XTXT: Close Task log details right panel
closeRightColumn=Закрити розділ
#XTXT: Sort Text
sortTxt=Сортувати
#XTXT: Filter Text
filterTxt=Фільтр
#XTXT: Filter by text to show list of filters applied
filterByTxt=Фільтрувати за
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Понад 5 хвилин
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Понад 15 хвилин
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Понад 1 годину
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Остання година
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Останні 24 години
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Останній місяць
#XTXT: Messages title text
messagesText=Повідомлення

#XTXT Statistics information message
statisticsInfo=Не можна створити статистику для віддалених таблиць із доступом до даних "Репліковано". Щоб створити статистику, видаліть репліковані дані в монітор відомостей віддаленої таблиці.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Перейти до монітора відомостей віддаленої таблиці

#XTXT: Repair latest failed run label
retryRunLabel=Повторити останній прогін
#XTXT: Repair failed run label
retryRun=Повторити прогін
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Повторний прогін ланцюжка завдань розпочато
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Не вдалося повторити прогін ланцюжка завдань
#XMSG: Task chain child elements name
taskChainRetryChildObject=Завдання {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Нове завдання
#XFLD Analyzed View
analyzedView=Проаналізоване подання
#XFLD Metrics
Metrics=Метрики
#XFLD Partition Metrics
PartitionMetrics=Метрики розділу
#XFLD Entities
Messages=Журнал завдань
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Розділи ще не визначено.
#XTXT: Description message for empty partition data
partitionEmptyDescText=Створюйте розділи, вказуючи критерії, щоб розбивати великі обсяги даних на менші, більш керовані частини.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Наразі немає доступних журналів
#XTXT: Description message for empty runs data
runsEmptyDescText=Коли ви почнете нову операцію (завантаження нового знімка, запуск аналізатора подань...), тут відображатимуться пов'язані журнали.


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=Конфігурація {0} обчислювального вузла Elastic не ведеться як належить. Перевірте визначення.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Помилка процесу додавання обчислювального вузла Elastic {0}.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Розпочато створення та активація репліки для вихідного об''єкта "{0}"."{1}" в обчислювальному вузлі Elastic {2}.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Розпочато вилучення репліки для вихідного об''єкта "{0}"."{1}" з обчислювального вузла Elastic {2}.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Не вдалося створити та активувати репліку для вихідного об''єкта "{0}"."{1}".
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Не вдалося вилучити репліку з вихідного об''єкта "{0}"."{1}".
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Розпочато маршрутизацію обчислення аналітичних запитів простору {0} до обчислювального вузла Elastic {1}.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Помилка маршрутизації обчислення аналітичних запитів простору {0} до відповідного обчислювального вузла Elastic.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Розпочато повернення маршрутизації обчислення аналітичних запитів простору {0} назад до обчислювального вузла Elastic {1}.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Помилка повернення назад маршрутизації обчислення аналітичних запитів простору {0}.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Ініційовано ланцюжок завдань {0} до відповідного обчислювального вузла Elastic {1}.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Не вдалося згенерувати ланцюжка завдань для обчислювального вузла Elastic {0}.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=Розпочато надання обчислювального вузла Elastic {0} з таким планом щодо обсягів: кількість віртуальних центральних процесорів: {1}, пам''ять (ГіБ): {2} і обсяг сховища (ГіБ): {3}.
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=Обчислювальний вузол Elastic {0} уже надано.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=Надання обчислювального вузла Elastic {0} уже скасовано.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=Операція недозволена. Зупиніть обчислювальний вузол Elastic {0} і перезапустіть його, щоб оновити план обсягів.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Помилка процесу вилучення обчислювального вузла Elastic {0}.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=Вичерпано час очікування поточного прогону обчислювального вузла Elastic {0}.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=Триває перевірка статусу обчислювального вузла Elastic {0}...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=Генерування ланцюжка завдань для обчислювального вузла Elastic {0} заблоковано, тому, можливо, ланцюжок ''{1}'' ще виконується.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=Вихідний об''єкт "{0}"."{1}" репліковано як залежність подання "{2}"."{3}".
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Не вдалося реплікувати таблицю "{0}"."{1}", оскільки реплікація рядкових таблиць є небажаною.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Перевищено максимальну кількість обчислювальних вузлів Elastic на одну інстанцію SAP HANA Cloud.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=Ще триває виконання операції для обчислювального вузла Elastic "{0}".
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=Через технічні проблеми видалення репліки для вихідної таблиці "{0}" зупинено. Спробуйте пізніше.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=Через технічні проблеми створення репліки для вихідної таблиці "{0}" зупинено. Спробуйте пізніше.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Не вдалося запустити обчислювальний вузол Elastic, оскільки досягнуто ліміту використання або ще не призначено блок-години обчислень.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Завантажте ланцюжок завдань і підготовку до прогону завдань ({0}), які є частиною цього ланцюжка.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=Уже виконується конфліктне завдання
#XMSG: Replication will change
txt_replication_change=Тип реплікації буде змінено.
txt_repl_viewdetails=Переглянути подробиці

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Схоже, сталася помилка під час спроби повторити останній прогін, оскільки попередній прогін завдання завершився помилкою до того, як вдалося згенерувати план.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=Простір "{0}" заблоковано.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=Для операції "{0}" має бути розгорнуто локальну таблицю "{1}".
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=Для операції "{0}" має бути ввімкнуто дельта-захоплення для локальної таблиці "{1}".
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=Для операції {0} дані локальної таблиці "{1}" мають зберігатися в сховищі об''єктів.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=Для операції {0} дані локальної таблиці "{1}" мають зберігатися в базі даних.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Початок вилучення видалених записів для локальної таблиці "{0}".
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Початок видалення всіх записів для локальної таблиці "{0}".
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Початок видалення всіх записів для локальної таблиці "{0}" відповідно до умов фільтрації "{1}".
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Сталася помилка під час вилучення видалених записів для локальної таблиці ''{0}''.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Сталася помилка під час видалення всіх записів для локальної таблиці ''{0}''.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Видалення всіх повністю оброблених записів із типом зміни "Видалено", що існують довше ніж {0} дн.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Видалення всіх повністю оброблених записів із типом зміни "Видалено", що існують довше ніж {0}.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS=Видалено записів: {0}.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS=Позначено для видалення записів: {0}.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=Вилучення видалених записів для локальної таблиці ''{0}'' завершено.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Видалення всіх записів для локальної таблиці ''{0}'' завершено.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Початок позначення записів як "Видалено" для локальної таблиці "{0}".
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Початок позначення записів як "Видалено" для локальної таблиці "{0}" відповідно до умов фільтрації "{1}".
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Позначення записів як "Видалено" для локальної таблиці "{0}" завершено.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=Зміни в даних тимчасово завантажуються до таблиці "{0}".
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Зміни в даних тимчасово завантажено до таблиці "{0}".
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Зміни в даних оброблено й видалено з таблиці "{0}".

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Подробиці з'єднання.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Початок оптимізації локальної таблиці (файл).
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Сталася помилка під час оптимізації локальної таблиці (файл).
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Сталася помилка. Оптимізацію локальної таблиці (файл) зупинено.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Триває оптимізація локальної таблиці (файл)...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=Локальну таблицю (файл) оптимізовано.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=Локальна таблиця (файл) оптимізована.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=Таблицю оптимізовано за допомогою стовпчиків Z-упорядкування: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Сталася помилка. Скорочення локальної таблиці (файл) зупинено.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Триває скорочення локальної таблиці (файл)...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=Скинуто розташування вхідного буфера для локальної таблиці (файлу).

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Розпочато вакуумування (видалення всіх повністю оброблених записів) локальної таблиці (файл).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Сталася помилка під час вакуумування локальної таблиці (файл).
#XMSG: Task log message
LTF_VACUUM_STOPPED=Сталася помилка. Вакуумування локальної таблиці (файл) зупинено.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Триває вакуумування локальної таблиці (файл)...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=Вакуумування завершено.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Видалення всіх повністю оброблених записів, що існують довше ніж {0} дн.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Видалення всіх повністю оброблених записів, що існують довше ніж {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Початок об'єднання нових записів із локальною таблицею (файлом).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Початок об'єднання нових записів із локальною таблицею (файлом) з використанням настройки "Автоматично об'єднувати дані".
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Початок об'єднання нових записів із локальною таблицею (файлом). Це завдання ініційовано запитом на об'єднання API.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Триває об'єднання нових записів із локальною таблицею (файлом).
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Сталася помилка під час об'єднання нових записів із локальною таблицею (файлом).
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=Локальна таблиця (файл) об'єднана.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Сталася помилка. Об'єднання локальної таблиці (файлу) зупинено.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=Об'єднання локальної таблиці (файлу) завершилося помилкою, але операцію вдалося виконати частково, і деякі дані вже об'єднано.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Сталася помилка часу очікування. Операція "{0}" виконувалася протягом {1} год.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=Не вдалося розпочати асинхронне виконання через високе завантаження системи. Відкрийте вікно "Системний монітор" і перевірте запущені завдання.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=Асинхронне виконання скасовано.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=Виконання завдання "{0}" здійснювалося в межах лімітів пам''яті {1} і {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=Виконання завдання "{0}" з ідентифікатором ресурсу {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=У локальній таблиці (файлі) розпочато пошук і заміну.
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Пошук і заміну в локальній таблиці (файлі) завершено.
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Не вдалося виконати пошук і заміну в локальній таблиці (файлі).

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Сталася помилка. Оновлення статистики для локальної таблиці (файлу) зупинено.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=Не вдалося виконати завдання через помилку нестачі пам'яті в базі даних SAP HANA.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=Не вдалося виконати завдання через відхилення контролю допуску SAP HANA.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=Не вдалося виконати завдання через завелику кількість активних з'єднань SAP HANA.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=Не вдалося виконати операцію повторення спроби, оскільки повторні спроби дозволені лише для батьківського елемента вкладеного ланцюжка завдань.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=Журнали дочірніх завдань, що завершилися помилкою, більше не доступні для продовження повторних спроб.


####Metrics Labels

performanceOptimized=Оптимізовано за продуктивністю
memoryOptimized=Оптимізовано за пам'яттю

JOB_EXECUTION=Виконання завдання
EXECUTION_MODE=Режим прогону
NUMBER_OF_RECORDS_OVERALL=Кількість постійно відтворюваних записів
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Кількість записів, зчитаних із віддаленого джерела
RUNTIME_MS_REMOTE_EXECUTION_TIME=Час обробки віддаленого джерела
MEMORY_CONSUMPTION_GIB=Пікове споживання пам'яті SAP HANA
NUMBER_OF_PARTITIONS=Кількість розділів
MEMORY_CONSUMPTION_GIB_OVERALL=Пікове споживання пам'яті SAP HANA
NUMBER_OF_PARTITIONS_LOCKED=Кількість заблокованих розділів
PARTITIONING_COLUMN=Стовпчик розбиття
HANA_PEAK_CPU_TIME=Загальний час ЦП SAP HANA
USED_IN_DISK=Використаний простір у сховищі
INPUT_PARAMETER_PARAMETER_VALUE=Параметр введення
INPUT_PARAMETER=Параметр введення
ECN_ID=Ім'я обчислювального вузла Elastic

DAC=Засоби контролю доступу до даних
YES=Так
NO=Ні
noofrecords=Кількість записів
partitionpeakmemory=Пікове споживання пам'яті SAP HANA
value=Значення
metricsTitle=Метрики ({0})
partitionmetricsTitle=Розділи ({0})
partitionLabel=Розділ
OthersNotNull=Значення, не включені в діапазони
OthersNull=Значення Null
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Настройки, що використовувалися для останнього прогону постійного відтворення даних:
#XMSG: Message for input parameter name
inputParameterLabel=Параметр введення
#XMSG: Message for input parameter value
inputParameterValueLabel=Значення
#XMSG: Message for persisted data
inputParameterPersistedLabel=Постійно відтворювалися
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Видалити дані
REMOVE_DELETED_RECORDS=Вилучити видалені записи
MERGE_FILES=Об'єднати файли
OPTIMIZE_FILES=Оптимізувати файли
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Переглянути в моніторі мосту SAP BW

ANALYZE_PERFORMANCE=Продуктивність аналізу
CANCEL_ANALYZE_PERFORMANCE=Скасувати відстеження продуктивності аналізу

#XFLD: Label for frequency column
everyLabel=Кожні
#XFLD: Plural Recurrence text for Hour
hoursLabel=Години
#XFLD: Plural Recurrence text for Day
daysLabel=Дні
#XFLD: Plural Recurrence text for Month
monthsLabel=Місяці
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Хвилини

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Посібник із виправлення помилок постійного відтворення подання</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Процесу забракло пам''яті. Не вдалося забезпечити постійне відтворення даних для подання "{0}". Додаткову інформацію про помилки через брак пам''яті див. на порталі Help Portal. Радимо проаналізувати подання на наявність ускладнень зі споживанням пам''яті за допомогою Аналізатора подань.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Не застосовується
OPEN_BRACKET=(
CLOSE_BRACKET=)
