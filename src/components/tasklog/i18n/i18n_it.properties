
#~~~~~~~~~~~~~~~~~~Texts for Task Log UI~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Header
LOG_DETAILS=Dettagli registro
#XFLD: Header
TASK_LOGS=Registri task ({0})

#XTIT: Count for View in ViewMonitor Toolbar
txtLogCount=Esecuzioni ({0})
#XTIT: View Details title ViewMonitor Dialog
txtViewDetails=Visualizza dettagli
#XFLD: Button text
STOP=Interrompi esecuzione
#XFLD: Label text
RUN_START=Avvio ultima esecuzione
#XFLD: Label text
RUN_END=Fine ultima esecuzione
#XFLD: Label text
RUNTIME=Durata
#XTIT: Count for Messages
txtDetailMessages=Messaggi ({0})
#XFLD: Label text
TIME=Timestamp
#XFLD: Label text
MESSAGE=Messaggio
#XFLD: Label text
TASK_STATUS=Categoria
#XFLD: Label text
TASK_ACTIVITY=Attività
#XFLD: Label text
RUN_START_DETAILS=Inizio
#XFLD: Label text
RUN_END_DETAILS=Fine
#XFLD: Label text
LOGS=Esecuzioni
#XFLD: Label text
STATUS=Stato
#XFLD: Label text
RUN_STATUS=Stato esecuzione
#XFLD: Label text
Runtime=Durata
#XFLD: Label text
RuntimeTooltip=Durata (hh:mm:ss)
#XFLD: Label text
TRIGGEREDBY=Avvio da
#XFLD: Label text
TRIGGEREDBYNew=Eseguito da
#XFLD: Label text
TRIGGEREDBYNewImp=Esecuzione avviata da
#XFLD: Label text
EXECUTIONTYPE=Tipo di esecuzione
#XFLD: Label text
EXECUTIONTYPENew=Tipo di esecuzione
#XFLD: Label text
PARENT_TASKCHAIN_SPACE=Spazio catena sovraordinata
#XFLD: Refresh tooltip
TEXT_REFRESH=Aggiorna
#XFLD: view Details link
VIEW_ERROR_DETAILS=Visualizza dettagli
#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Scarica dettagli aggiuntivi
#XMSG: Download completed
downloadStarted=Download avviato
#XMSG: Error while downloading content
errorInDownload=Si è verificato un errore durante il download.
#XBTN: view Details link
VIEW_ERROR_DETAILS_DIALOG_TITLE=Visualizza dettagli
#XBTN: cancel button of task details dialog
TXT_CANCEL=Annulla
#XBTN: back button from task details
TXT_BACK=Indietro
#XFLD: Log message with complete status
MSG_LOG_COMPLETED=Task completato
#XFLD: Log message with failed status
MSG_LOG_FAILED=Task non riuscito
#XFLD: Master and detail table with no data
No_Data=Nessun dato
#XFLD: Retry tooltip
TEXT_RETRY=Riprova
#XFLD: Cancel Run label
TEXT_CancelRun=Annulla esecuzione
#XBTN: button for Cleanup Failed Load
TXT_Cleanup=Pulisci caricamento non riuscito
#XMSG:button copy sql statement
txtSQLStatement=Copia istruzione SQL
#XMSG:button open remote query monitor
txtOpenQueryMonitor=Apri Monitor query remote
#XMSG:message for opening remote query monitor
messageOpenRemoteQueryMonitor=Per visualizzare le istruzioni SQL remote, fare clic su "Apri Monitor query remote".
#XMSG:button ok
txtOk=Ok
#XMSG: button close
txtClose=Chiudi
#XMSG: success message for cancelling last performed action
cancelLastPerformedAction=L''azione Annulla esecuzione per l''oggetto "{0}" è stata avviata.
#XMSG: error message for cancelling last performed action
errorCancelLastPerformedAction=L''azione Annulla esecuzione per l''oggetto "{0}" non è riuscita.
#XMSG: error when cancelling last performed action due to unexpected replication status
errorUnexpectedReplicationStatus=L''azione Annulla esecuzione per l''oggetto "{0}" non è più possibile perché lo stato di replicazione è stato modificato.
#XMSG: Info message for no Running state logId
noTaskWithRunningState=Nessun registro di task ha lo stato In esecuzione.
#XMSG: message for conflicting task
Task_Already_Running=È già in esecuzione un task in conflitto per l''oggetto "{0}".
#XFLD: Label for no task log with running state title
actionInfo=Informazioni azione
#XMSG Copied to clipboard
copiedToClip=Copiato nel clipboard
#XFLD copy
Copy=Copia
#XFLD copy correlation ID
CopyCorrelationID=Copia ID correlazione
#XFLD Close
Close=Chiudi
#XFLD: show more Label
txtShowMore=Mostra di più
#XFLD: message Label
messageLabel=Messaggio:
#XFLD: details Label
detailsLabel=Dettagli:
#XFLD: executingSQLStatement Label
executingSQLStatementLabel=Esecuzione istruzione \r\n SQL:
#XFLD:statementId Label
statementIdLabel=ID istruzione:
#XFLD: numberOfRemoteStatements Label
numberOfRemoteStatementsLabel=Numero di istruzioni \r\n SQL remote:
#XFLD: numberOfRemoteStatements Label
txtNumberOfStatements=Numero di istruzioni
#XFLD: Space Label
txtSpaces=Spazio
#XFLD : Unauthorised Spaces Text
txtUnauthorisedSpaces=Spazi non autorizzati
#XFLD: Privilege Error Text
txtPrivilegeError=Non si dispone di privilegi sufficienti per visualizzare questi dati.
#XFLD: Label for Object Header
DATA_ACCESS=Accesso ai dati
#XFLD: Label for Object Header
SCHEDULE=Pianifica
#XFLD: Label for Object Header
DETAILS=Dettagli
#XFLD: Label for Object Header
LATEST_UPDATE=Ultimo aggiornamento
#XFLD: Label for Object Header
LATEST_CHANGE_SOURCE=Modifica più recente (origine)
#XFLD: Label for Object Header
REFRESH_FREQUENCY=Frequenza di aggiornamento
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Frequenza pianificata
#XFLD: Label for Object Header
NEXT_RUN=Prossima esecuzione
#XFLD: Label for Object Header
CONNECTION=Connessione
#XFLD: Label for Object Header
DP_AGENT=Agente FD
#XFLD: Label for Object Header
USED_IN_MEMORY=Memoria utilizzata per archivio (MiB)
#XFLD: Label for Object Header
USED_DISK=Disco utilizzato per archivio (MiB)
#XFLD: Label for Object Header
USED_IN_MEMORY_NEW=Dimensioni in memoria (MiB)
#XFLD: Label for Object Header
USED_DISK_NEW=Dimensioni su disco (MiB)
#XFLD: Label for Object Header
NUMBER_OF_RECORDS=Numero di record

#XFLD: sub status SET_TO_FAILED
SET_TO_FAILED=Impostato su Non riuscito
SET_TO_FAILED_ERR=Questo task è stato eseguito ma l'utente ne ha impostato lo stato su NON RIUSCITO.
#XFLD: Label for stopped failed
FAILLOCKED=Esecuzione già in corso
#XFLD: sub status STOPPED
STOPPED=Interrotto
STOPPED_ERR=Questo task è stato interrotto ma non è stato eseguito il rollback.
#XFLD: sub status CANCELLED
CANCELLED=Annullato
CANCELLED_ERR=Questo task è stato annullato, dopo essere stato avviato. In questo caso, è stato eseguito il rollback dei dati ed è stato ripristinato lo stato esistente prima dell'avvio iniziale del task.
#XFLD: sub status LOCKED
LOCKED=Bloccato
LOCKED_ERR=Lo stesso task è stato già eseguito, pertanto questo processo del task non può essere eseguito in parallelo con l'esecuzione di un task esistente.
#XFLD: sub status TASK_EXCEPTION
TASK_EXCEPTION=Eccezione task
TASK_EXCEPTION_ERR=Per questo task si è verificato un errore non specificato durante l'esecuzione.
#XFLD: sub status TASK_EXECUTE_EXCEPTION
TASK_EXECUTE_EXCEPTION=Eccezione di esecuzione task
TASK_EXECUTE_EXCEPTION_ERR=Per questo task si è verificato un errore durante l'esecuzione.
#XFLD: sub status UNAUTHORIZED
UNAUTHORIZED=Non autorizzato
UNAUTHORIZED_ERR=Non è stato possibile autenticare l'utente, oppure l'utente è stato bloccato o eliminato.
#XFLD: sub status FORBIDDEN
FORBIDDEN=Vietato
FORBIDDEN_ERR=L'utente assegnato non dispone dei privilegi necessari per eseguire questo task.
#XFLD: sub status FAIL_NOT_TRIGGERED
FAIL_NOT_TRIGGERED=Non avviato
FAIL_NOT_TRIGGERED_ERR=Questo processo del task non può essere eseguito a causa di un'interruzione di sistema o perché alcune parti del sistema di database non sono disponibili al momento dell'esecuzione pianificata. Attendere Il successivo orario pianificato di esecuzione del processo o ripianificarlo.
#XFLD: sub status SCHEDULE_CANCELLED
SCHEDULE_CANCELLED=Pianificazione annullata
SCHEDULE_CANCELLED_ERR=Questo processo del task non può essere eseguito a causa di un errore interno. Contattare il supporto SAP e fornire loro l'ID correlazione e il timestamp delle informazioni dettagliate del registro del processo del task.
#XFLD: sub status SUCCESS_SKIPPED - This task  execution has not been triggered because a previous run of the same task is still in progress.
SUCCESS_SKIPPED=Esecuzione precedente in corso
SUCCESS_SKIPPED_ERR=L'esecuzione di questo task non è stata avviata perché è ancora in corso un'esecuzione precedente dello stesso task.
#XFLD: sub status FAIL_OWNER_MISSING
FAIL_OWNER_MISSING=Responsabile mancante
FAIL_OWNER_MISSING_ERR=Questo processo del task non può essere eseguito perché non presenta l'assegnazione di un utente di sistema. Assegnare un utente responsabile al processo.
#XFLD: sub status FAIL_CONSENT_NOT_AVAILABLE
FAIL_CONSENT_NOT_AVAILABLE=Consenso non disponibile
FAIL_CONSENT_NOT_AVAILABLE_ERR=L'utente non ha autorizzato SAP a eseguire catene di task o pianificare l'integrazione dei dati per proprio conto; selezionare l'opzione fornita per acconsentire.
#XFLD: sub status FAIL_CONSENT_EXPIRED
FAIL_CONSENT_EXPIRED=Consenso scaduto
FAIL_CONSENT_EXPIRED_ERR=L'autorizzazione che consente a SAP di eseguire catene di task o di pianificare task di integrazione dei dati per proprio conto è scaduta; selezionare l'opzione fornita per rinnovare il consenso.
#XFLD: sub status FAIL_CONSENT_INVALIDATED
FAIL_CONSENT_INVALIDATED=Consenso invalidato
FAIL_CONSENT_INVALIDATED_ERR=Questo task non può essere eseguito, generalmente a causa di una modifica nella configurazione dell'Identity Provider del tenant. In questo caso, non è possibile eseguire o pianificare alcun nuovo processo del task a nome dell'utente interessato. Se l'utente assegnato esiste ancora nel nuovo IdP, revocare il consenso alla pianificazione, quindi concederlo di nuovo; se l'utente assegnato non esiste più, assegnare un nuovo responsabile del processo del task e fornire il consenso alla pianificazione del task richiesto. Per maggiori informazioni, consultare la seguente nota SAP: https://launchpad.support.sap.com/#/notes/3089828.
TASK_EXECUTOR_ERROR=Esecutore task
TASK_EXECUTOR_ERROR_ERR=Per questo task si è verificato un errore interno, probabilmente durante i passaggi di preparazione per l'esecuzione e non è stato possibile avviare il task.
PREREQ_NOT_MET=Prerequisito non soddisfatto
PREREQ_NOT_MET_ERR=Questo task non può essere eseguito a causa di problemi con la sua definizione. Ad esempio, l'oggetto non è distribuito, una catena di task contiene logica circolare o l'SQL di una vista non è valido.
RESOURCE_LIMIT_ERROR=Errore limite di risorsa
RESOURCE_LIMIT_ERROR_ERR=Attualmente impossibile eseguire il task perché le risorse sufficienti non erano disponibili o erano occupate.
FAIL_CONSENT_REFUSED_BY_UMS=Consenso rifiutato
FAIL_CONSENT_REFUSED_BY_UMS_ERR=Non è stato possibile eseguire il task, in esecuzioni pianificate o catene di task, a causa di una modifica nella configurazione dell'Identity Provider dell'utente nel tenant. Per ulteriori informazioni, consultare la seguente nota SAP: https://launchpad.support.sap.com/#/notes/3120806.
#XFLD: status text
SCHEDULED=Pianificato
#XFLD: status text
SCHEDULEDNew=Permanente
#XFLD: status text
PAUSED=Sospeso
#XFLD: status text
DIRECT=Diretto
#XFLD: status text
MANUAL=Manuale
#XFLD: status text
DIRECTNew=Semplice
#XFLD: status text
COMPLETED=Completato
#XFLD: status text
FAILED=Non riuscito
#XFLD: status text
RUNNING=In esecuzione
#XFLD: status text
none=Nessuno
#XFLD: status text
realtime=Tempo reale
#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: sub task
SUB_TASK=Sottotask
#XFLD: New Data available in the file
NEW_DATA=Nuovi dati

#XFLD: text for values shown in column Replication Status
txtOff=Disattivato
#XFLD: text for values shown in column Replication Status
txtInitializing=Inizializzazione in corso
#XFLD: text for values shown in column Replication Status
txtLoading=Caricamento in corso
#XFLD: text for values shown in column Replication Status
txtActive=Attivo
#XFLD: text for values shown in column Replication Status
txtAvailable=Disponibile
#XFLD: text for values shown in column Replication Status
txtError=Errore
#XFLD: text for values shown in column Replication Status
txtPaused=Interrotto
#XFLD: text for values shown in column Replication Status
txtDisconnected=Disconnesso
#XFLD: text for partially Persisted views
partiallyPersisted=Persistenza parzialmente applicata

#XFLD: activity text
REPLICATE=Replica
#XFLD: activity text
REMOVE_REPLICATED_DATA=Rimuovi dati replicati
#XFLD: activity text
DISABLE_REALTIME=Disabilita replicazione dei dati in tempo reale
#XFLD: activity text
REMOVE_PERSISTED_DATA=Rimuovi dati persistenza
#XFLD: activity text
PERSIST=Applica persistenza
#XFLD: activity text
EXECUTE=Esegui
#XFLD: activity text
TASKLOG_CLEANUP=Tacklog_Cleanup
#XFLD: activity text
CANCEL_REPLICATION=Annulla replicazione
#XFLD: activity text
MODEL_IMPORT=Importazione modello
#XFLD: activity text
NONE=Nessuno
#XFLD: activity text
CANCEL_PERSISTENCY=Annulla persistenza
#XFLD: activity text
EXECUTE_VIEW_ANALYZER=Analizza vista
#XFLD: activity text
CANCEL_VIEW_ANALYZER=Annulla analizzatore vista

#XFLD: severity text
INFORMATION=Informazioni
#XFLD: severity text
SUCCESS=Operazione riuscita
#XFLD: severity text
WARNING=Avviso
#XFLD: severity text
ERROR=Errore
#XFLD: text for values shown for Ascending sort order
SortInAsc=Classifica in ordine crescente
#XFLD: text for values shown for Descending sort order
SortInDesc=Classifica in ordine decrescente
#XFLD: filter text for task log columns
Filter=Filtra
#XFLD: object text for task log columns
Object=Oggetto
#XFLD: space text for task log columns
crossSpace=Spazio

#XBUT: label for remote data access
REMOTE=Remoto
#XBUT: label for real-time replication data access
REALTIME_REPLICATION=Replicato (tempo reale)
#XBUT: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Replicato (istantanea)

#XMSG Error message to show in error strip
ERROR_MSG_REAL_TIME=Replicazione in tempo reale bloccata a causa di un errore. Una volta corretto l'errore, sarà possibile utilizzare l'azione "Riprova" per continuare con la replicazione in tempo reale.
ERROR_MSG=Replicazione in tempo reale bloccata a causa di un errore.
RETRY_FAILED_ERROR=Processo per nuovo tentativo non riuscito con errore.
LOG_INFO_DETAILS=Non vengono generati registri quando si replicano i dati in modalità in tempo reale. I registri visualizzati sono correlati ad azioni precedenti.

#XBUT: Partitioning label
partitionMenuText=Esecuzione partizioni
#XBUT: Drop down menu button to create a partition
createPartitionLabel=Crea partizione
#XBUT: Drop down menu button to edit a partition
editPartitionLabel=Modifica partizione
#XBUT: Drop down menu button to delete a partition
deletePartitionLabel=Elimina partizione
#XFLD: Initial text
InitialPartitionText=Definisci le partizioni specificando i criteri per suddividere insiemi di dati grandi in insiemi più piccoli.
DefinePartition=Definisci partizioni
#XFLD: Message text
partitionChangedInfo=La definizione partizione è cambiata dall'ultima replicazione. Le modifiche verranno applicate al prossimo caricamento dei dati.
#XFLD: Message text
REAL_TIME_WARNING=La partizione viene applicata solo quando si carica una nuova istantanea. Non si applica alla replicazione in tempo reale.
#XFLD: Message text
loadSelectedPartitions=Applicazione della persistenza ai dati avviata per le partizioni selezionate di "{0}"
#XFLD: Message text
loadSelectedPartitionsError=Applicazione della persistenza ai dati non riuscita per le partizioni selezionate di "{0}"
#XFLD: Message text
viewpartitionChangedInfo=Definizione della partizione modificata dall'ultima esecuzione di persistenza. Per applicare le modifiche, il successivo caricamento di dati sarà un'istantanea completa che include le partizioni bloccate. Una volta terminato questo caricamento completo, sarà possibile eseguire i dati per le singole partizioni.
#XFLD: Message text
viewpartitionChangedInfoLocked=Definizione della partizione modificata dall'ultima esecuzione di persistenza. Per applicare le modifiche, il successivo caricamento di dati sarà un'istantanea completa, ad eccezione degli intervalli di partizione bloccati e invariati. Una volta terminato questo caricamento, sarà nuovamente possibile caricare di nuovo le partizioni selezionate.
#XBUT: Drop down menu button for table replication
TABLE_REPLICATION=Replicazione tabella
#XBUT: Drop down menu button for schedule replication
SCHEDULE_REPLICATION=Pianifica replicazione
#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Crea pianificazione
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Modifica pianificazione
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Elimina pianificazione
#XBUT: Drop down menu button to load new snapshot
loadNewsSnapshotLabel=Carica nuova istantanea
#XBUT: Drop down menu button to load new snapshot
startDataReplicationLabel=Avvia replicazione di dati
#XBUT: Drop down menu button to remove replicated data
removeReplicatedDataLabel=Rimuovi dati replicati
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabel=Abilita accesso in tempo reale
#XBUT: Drop down menu button to enable real time replication
enableRealTimeReplicationLabelNew=Abilita replicazione dei dati in tempo reale
#XBUT: Drop down menu button to enable real time replication
disableRealTimeReplicationLabel=Disabilita replicazione dei dati in tempo reale
#XFLD: Message for replicate table action
replicateTableText=Replicazione tabella
#XFLD: Message for replicate table action
replicateTableTextNew=Replicazione dei dati
#XFLD: Message to schedule task
scheduleText=Pianifica
#XBUT: Menu Button for view persistancy
ViewPersistenceMenu=Persistenza viste
#XBUT: Menu Button for view persistancy
ViewPersistenceMenuNew=Persistenza dei dati
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Carica nuova istantanea
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew=Avvia persistenza dei dati
#XFLD: Label for Remove persisted data menuitem
removePersistedDataLabel=Rimuovi dati persistenza
#XFLD: Partitioned Processign
EnablePartitionedProcessing=Elaborazione ripartita
#XBUT: Label for scheduled replication
scheduledTxt=Pianificata
#XBUT: Label for statistics button
statisticsTxt=Statistiche
#XBUT: Label for create statistics
createStatsTxt=Crea statistiche
#XBUT: Label for edit statistics
editStatsTxt=Modifica statistiche
#XBUT: Label for refresh statistics
refreshStatsTxt=Aggiorna statistiche
#XBUT: Label for delete statistics
dropStatsTxt=Elimina statistiche
#XMSG: Create statistics success message
statsSuccessTxt=Creazione delle statistiche di tipo {0} per {1} avviata.
#XMSG: Edit statistics success message
statsEditSuccessTxt=Modifica delle statistiche di tipo {0} per {1} avviata.
#XMSG: Refresh statistics success message
statsRefreshSuccessTxt=Aggiornamento delle statistiche per {0} avviato.
#XMSG: Drop statistics success message
statsDropSuccessTxt=Statistiche eliminate correttamente per {0}
#XMSG: Create statistics error message
statsCreateErrorTxt=Errore durante la creazione delle statistiche
#XMSG: Edit statistics error message
statsEditErrorTxt=Errore durante la modifica delle statistiche
#XMSG: Refresh statistics error message
statsRefreshErrorTxt=Errore durante l'aggiornamento delle statistiche
#XMSG: Drop statistics error message
statsDropErrorTxt=Errore durante l'eliminazione delle statistiche
#XMG: Warning text for deleting statistics
statsDelWarnTxt=Eliminare le statistiche dei dati?
startPersistencyAdvisorLabel=Avvia analizzatore vista

#Partition related texts
#XFLD: Label for Column
column=Colonna
#XFLD: Label for No of Partition
noOfPartitions=N. di partizioni
#XFLD: Label for Column
noOfParallelProcess=N. di processi paralleli
#XFLD: Label text
noOfLockedPartition=N. di partizioni bloccate
#XFLD: Label for Partition
PARTITION=Partizioni
#XFLD: Label for Column
AVAILABLE=Disponibile
#XFLD: Statistics Label
statsLabel=Statistiche
#XFLD: Label text
COLUMN=Colonna:
#XFLD: Label text
PARALLEL_PROCESSES=Processi paralleli:
#XFLD: Label text
Partition_Range=Intervallo partizione
#XFLD: Label text
Name=Nome
#XFLD: Label text
Locked=Bloccato
#XFLD: Label text
Others=ALTRI
#XFLD: Label text
Delete=Elimina
#XFLD: Label text
LoadData=Carica partizioni selezionate
#XFLD: Label text
LoadSelectedData=Carica partizioni selezionate
#XFLD: Confirmation text
LoadNewPersistenceConfirm=L'operazione caricherà una nuova istantanea per tutte le partizioni sbloccate e modificate, non solo quelle selezionate. Continuare?
#XFLD: Label text
Continue=Continua

#XFLD: Label text
PARTITIONS=Partizioni
#XFLD: Label text
ADD_PARTITIONS=+ Aggiungi partizione
#XFLD: Label text
GTT=>=
#XFLD: Label text
LT=<
#XFLD: Label text
AddRange=Aggiungi partizione
#XFLD: Label text
deleteRange=Elimina partizione
#XFLD: Label text
LOW_PLACE_HOLDER=Inserisci valore inferiore
#XFLD: Label text
HIGH_PLACE_HOLDER=Inserisci valore superiore
#XFLD: tooltip text
lockedTooltip=Blocca partizione dopo caricamento iniziale

#XFLD: Button text
Edit=Modifica
#XFLD: Button text
CANCEL=Annulla

#XFLD: Statistics Fields
LAST_STATISTICS_UPDATE=Aggiornamento ultime statistiche
#XFLD: Statistics Fields
STATISTICS=Statistiche

#XFLD:Retry label
TEXT_Retry=Riprova
#XFLD:Retry label
TEXT_Retry_tooltip=Riprova replicazione in tempo reale dopo la risoluzione dell'errore.
#XFLD: text retry
Retry=Conferma
#XMG: Retry confirmation text
retryConfirmationTxt=L'ultima replicazione in tempo reale si è conclusa con un errore.\nConfermare che l'errore è stato corretto e che la replicazione in tempo reale può essere riavviata.
#XMG: Retry success text
retrySuccess=Nuovo tentativo processo iniziato correttamente.
#XMG: Retry fail text
retryFail=Nuovo tentativo processo non riuscito.
#XMSG: activity message for create statistics
CREATE_STATISTICS=Crea statistiche
#XMSG: activity message for edit statistics
DROP_STATISTICS=Elimina statistiche
#XMSG: activity message for refresh statistics
REFRESH_STATISTICS=Aggiorna statistiche
#XMSG: activity message for edit statistics
ALTER_STATISTICS=Modifica statistiche
#XMSG: Task log message started task
taskStarted=Il task {0} è iniziato.
#XMSG: Task log message for finished task
taskFinished=Il task {0} è terminato con lo stato {1}.
#XMSG: Task log message for finished task with end time
taskFinishedAt=Il task {0} è terminato alle ore {2} con stato {1}.
#XMSG: Task {0} has input parameters
taskHasInputParameters=Il task {0} ha parametri di input.
#XMSG: Task log message for unexpected error
unexpectedExecutionError=Il task {0} è terminato con un errore imprevisto. Lo stato del task è stato impostato su {1}.
#XMSG: Task log message for failed task
failedToEnd=Impossibile impostare lo stato su {0} o rimuovere il blocco.
#XMSG: Task log message
lockNotFound=Impossibile finalizzare il processo a causa della mancanza del blocco: il task potrebbe essere stato annullato.
#XMSG: Task log message failed task
failedOverwrite=Il task {0} è già bloccato da {1}. Pertanto, non è riuscito con il seguente errore: {2}.
#XMSG: Task log message task lock removed successfully by another task
successOverwrite=Il blocco di questo task è stato rilevato da un altro task.
#XMSG: Task log message failed takeover
failedTakeover=Impossibile prendere in carico un task esistente.
#XMSG: Task log message successful takeover
successTakeover=È stato necessario rilasciare il blocco rimanente; il nuovo blocco per questo task è impostato.
#XMSG: Tasklog Dialog Details
txtDetails=L'istruzione remota elaborata durante l'esecuzione può essere visualizzata aprendo il monitor query remote, nei dettagli dei messaggi specifici della partizione.
#XMSG: Tasklog Dialog message strip details
txtStatementsDeleted=Le istruzioni SQL remote sono state eliminate dal database e non possono essere visualizzate.
#XMSG: Tasklog Dialog Message strip
txtCrossSpace=Le query remote con connessioni assegnate ad altri spazi non possono essere visualizzate. Passare al Monitor query remote e utilizzare l'ID istruzione per filtrarle.
#XMSG: Task log message for parallel check error
parallelCheckError=Impossibile elaborare il task perché un altro task è in esecuzione e lo sta già bloccando.
#XMSG: Task log message for parallel running task
parallelTaskRunning=È già in esecuzione un task in conflitto.
#XMSG: Written error message to the central TaskLog, which indicates that a task run with a certain correlationId and status occurred.
pacemakerRunStatus=Stato {0} durante l''esecuzione con ID correlazione {1}.
#XMSG: Task log message for a task which was triggered by a user without the necessary priviledges
forbiddenMessage=L'utente assegnato non dispone dei privilegi necessari per eseguire questo task.

#XBUT: Label for open in Editor
openInEditor=Apri nell'editor
#XBUT: Label for open in Editor
openInEditorNew=Apri in Generatore di dati
#XFLD:Run deails label
runDetails=Dettagli esecuzione
#XFLD: Label for Logs
Logs=Registri
#XFLD: Label for Settings
Settings=Impostazioni
#XFLD: Label for Save button
Save=Salva
#XFLD: Label for Standard
Standard_PO=Ottimizzato per prestazione (consigliato)
#XFLD: Label for Hana low memory processing
HLMP_MO=Ottimizzato per memoria
#XFLD: Label for execution mode
ExecutionMode=Modalità esecuzione
#XFLD: Label for job execution
jobExecution=Modalità di elaborazione
#XFLD: Label for Synchronous
syncExec=Sincrono
#XFLD: Label for Asynchronous
asyncExec=Asincrono
#XFLD: Label for default asynchronous execution
defaultAsyncExec=Utilizza predefinito (asincrono, potrebbe cambiare in futuro)
#XMSG: Save settings success
saveSettingsSuccess=Modalità di esecuzione SAP HANA modificata.
#XMSG: Save settings failure
saveSettingsFailed=Modifica alla modalità di esecuzione SAP HANA non riuscita.
#XMSG: JJob Execution changed successfully
jobExecSettingSuccessful=Esecuzione processo modificata.
#XMSG: Job Execution change failed
jobExecSettingFailed=Modifica esecuzione processo non riuscita.
#XMSG: Text for Type
typeTxt=Tipo
#XMSG: Text for Monitor
monitorTxt=Monitor
#XMSG: Text for activity
activityTxt=Attività
#XMSG: Text for metrics
metricsTxt=Metriche
#XTXT: Text for Task chain key
TASK_CHAINS=Catena di task
#XTXT: Text for View Key
VIEWS=Visualizza
#XTXT: Text for remote table key
REMOTE_TABLES=Tabella remota
#XTXT: Text for Space key
SPACE=Spazio
#XTXT: Text for Elastic compute node key
ELASTIC_COMPUTE_NODE=Nodo di calcolo elastico
#XTXT: Text for Replication flow key
REPLICATION_FLOWS=Flusso di replicazione
#XTXT: Text for Intelligent look up
INTELLIGENT_LOOKUP=Ricerca intelligente
#XTXT: Text for Local Table
LOCAL_TABLE=Tabella locale
#XTXT: Text for Data flow key
DATA_FLOWS=Flusso di dati
#XTXT: Text for SQL Script Procedure key
SQL_SCRIPT_PROCEDURE=Procedura script SQL
#XTXT: Text for BW Process Chain key
BW_PROCESS_CHAIN=Catena di processi BW
#XTXT: Text for API
API=API
#XTXT: View in Monitor Link text
viewInMonitorTxt=Visualizza nel monitor
#XTXT: Task List header text
taskListHeader=Elenco di task ({0})
#XMSG: error message for histroical graph executions.
noMetricesHistoryTxt=Impossibile recuperare le metriche delle esecuzioni cronologiche di un flusso di dati.
#XMSG: error message for reading data from backend for details
metricesBackendErrorTxt=Dettaglio di esecuzione completa non in caricamento al momento. Provare ad aggiornare.
#XFLD: Label text for the Metrices table header
metricesColLabel=Etichetta operatore
#XFLD: Label text for the Metrices table header
metricesType=Tipo
#XFLD: Label text for the Metrices table header
metricesRecordLabel=Conteggio record
#XMSG: label for execute taskchain button
executeTaskTooltipLabel=Esegui la catena di task
#XMSG: Task chain run started toast message
msgTaskExecuteSuccess=Esecuzione catena di task avviata.
#XMSG: Task chain run started toast message with Object name
taskExecuteSuccessMsg=Esecuzione catena di task avviata per {0}
#XMSG: Task Chain error execution toast message
msgTaskExecuteFail=Esecuzione catena di task non riuscita.
#XTXT: Execute button label
runLabel=Esegui
#XTXT: Execute button label
runLabelNew=Avvia esecuzione
#XMSG: Filter Object header
chainsFilteredTableHeader=Filtro in base a oggetto: {0}
#XFLD: Parent task chain label
parentChainLabel=Catena di task sovraordinata:
#XFLD: Parent task chain unauthorized
Unauthorized=Non autorizzato alla visualizzazione
#XFLD: Parent task chain label
parentTaskLabel=Task sovraordinato:
#XTXT: Task status
NOT_TRIGGERED=Non avviato
#XTXT: Tooltip message to enter full screen
enterFullScreenTxt=Accedi a modalità a schermo intero
#XTXT: Tooltip message to exit full screen
exitFullScreenTxt=Esci da modalità a schermo intero
#XTXT: Close Task log details right panel
closeRightColumn=Chiudi sezione
#XTXT: Sort Text
sortTxt=Ordina
#XTXT: Filter Text
filterTxt=Filtra
#XTXT: Filter by text to show list of filters applied
filterByTxt=Filtra per
#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Più di 5 minuti
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Più di 15 minuti
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Più di 1 ora
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Ultima ora
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Ultime 24 ore
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Ultimo mese
#XTXT: Messages title text
messagesText=Messaggi

#XTXT Statistics information message
statisticsInfo=Impossibile creare le statistiche per le tabelle remote con l'accesso ai dati "Replicato". Per creare le statistiche, rimuovere i dati replicati nel monitor dei dettagli delle tabelle remote.
#XFLD
GO_TO_REMOTETABLE_DETAILS=Passa a monitor dei dettagli tabella remota

#XTXT: Repair latest failed run label
retryRunLabel=Riprova ultima esecuzione
#XTXT: Repair failed run label
retryRun=Riprova esecuzione
#XMSG: Task chain repair execution success message
msgTaskRetryExecuteSuccess=Esecuzione nuovo tentativo catena di task avviata
#XMSG: Task chain repair execution failed
msgTaskRetryExecuteFail=Esecuzione nuovo tentativo catena di task non riuscita
#XMSG: Task chain child elements name
taskChainRetryChildObject=Task {0}
#XMSG: Task chain child element name without task log id
taskChainRetryNewChildObject=Nuovo task
#XFLD Analyzed View
analyzedView=Vista analizzata
#XFLD Metrics
Metrics=Metriche
#XFLD Partition Metrics
PartitionMetrics=Metriche di partizione
#XFLD Entities
Messages=Registro task
#XTXT: Title Message for empty partition data
partitionEmptyTitle=Non è ancora stata definita alcuna partizione.
#XTXT: Description message for empty partition data
partitionEmptyDescText=Crea partizioni specificando i criteri per suddividere grandi volumi di dati in parti più piccole e gestibili.

#XTXT: Title Message for empty runs data
runsEmptyTitle=Ancora nessun registro disponibile
#XTXT: Description message for empty runs data
runsEmptyDescText=Quando si avvia una nuova attività (caricamento di una nuova istantanea, avvio dell'analizzatore dati...) i registri correlati verranno visualizzati qui. 


#ECN Monitoring messages
#XMSG: Task log message for ecn not configured
ECN_NOT_CONFIGURED=La configurazione {0} del nodo di calcolo elastico non è specificata in modo appropriato. Verificare la definizione.
#XMSG: Task log message ecn add failed
ECN_ADD_FAILED=Processo di aggiunta del nodo di calcolo elastico {0} non riuscito.
#XMSG: Task log message for replica creation in ecn
REPLICATE_TO_ECN_STARTED=Creazione e attivazione della replicazione per l''oggetto di origine "{0}"."{1}" nel nodo di calcolo elastico {2} avviate.
#XMSG: Task log message for replica removal in ecn
REMOVE_REPLICA_FROM_ECN_STARTED=Rimozione della replicazione per l''oggetto di origine "{0}"."{1}" dal nodo di calcolo elastico {2} avviata.
#XMSG: Task log message for create replica failed
REPLICATE_TO_ECN_FAILED=Creazione e attivazione della replicazione per l''oggetto di origine "{0}"."{1}" non riuscite.
#XMSG: Task log message for remove replica failed
REMOVE_REPLICA_FROM_ECN_FAILED=Rimozione della replicazione per l''oggetto di origine "{0}"."{1}" non riuscita.
#XMSG: Task log message for routing
ROUTING_TO_ECN_STARTED=Instradamento del calcolo delle query analitiche dello spazio {0} al nodo di calcolo elastico {1} avviato.
#XMSG: Task log message for routing failed
ROUTING_TO_ECN_FAILED=Instradamento del calcolo delle query analitiche dello spazio {0} al nodo di calcolo elastico corrispondente non riuscito.
#XMSG: Task log message for re-routing
REROUTING_TO_COORDINATOR_STARTED=Nuovo instradamento del calcolo delle query analitiche dello spazio {0} dal nodo di calcolo elastico {1} avviato.
#XMSG: Task log message for re-routing failed
REROUTING_TO_COORDINATOR_FAILED=Nuovo instradamento del calcolo delle query analitiche dello spazio {0} al coordinatore non riuscito.
#XMSG: Task log message for task chain
TRIGGERED_TASK_CHAIN_LOG_ID=Catena di task {0} per il corrispondente nodo calcolo elastico {1} attivata.
#XMSG: Task log message for task chain
GENERATE_TASK_CHAIN_FAILED=Generazione della catena di task per il nodo di calcolo elastico {0} non riuscita.
#XMSG: Task log message for ecn provisioning with given plan
ECN_PROVISIONING_SIZING_PLAN=Il provisioning del nodo di calcolo elastico {0} è stato avviato con il seguente piano di dimensionamento: {1} vCPU, {2} di memoria (GiB) e {3} di spazio di archiviazione (GiB).
#XMSG: Task log message for already provisioned ecn
ECN_PROVISIONED=È già stato eseguito il provisioning del nodo di calcolo elastico {0}.
#XMSG: Task log message for already deprovisioned ecn
ECN_DEPROVISIONED=È già stato annullato il provisioning del nodo di calcolo elastico {0}.
#XMSG: Block the provisioning of the ecn if sizing plan has been changed
ECN_UPDATE_SIZING_PLAN=L''operazione non è consentita. Interrompere il nodo di calcolo elastico {0} e riavviarlo per aggiornare il piano di dimensionamento.
#XMSG: Task log message for failed remove
ECN_REMOVE_FAILED=Processo di rimozione del nodo di calcolo elastico {0} non riuscito.
#XMSG: Task log message for timed-out operation
ECN_PROVISIONING_TIME_OUT=Timeout dell''esecuzione corrente del nodo di calcolo elastico {0}.
#XMSG: Task log message for polling
ECN_OBSERVER_POLLING=Verifica dello stato del nodo di calcolo elastico {0} in corso...
#XMSG: Locked task chain generation
ECN_LOCKED_CHAIN_RUN=Generazione della catena di task per nodo di calcolo elastico {0} bloccata, pertanto la catena {1} potrebbe ancora essere in esecuzione.
#XMSG: Task log message for parent view of local table
REPLICATE_PARENT_CONSUMPTION_VIEW_PATH=L''oggetto di origine "{0}"."{1}" viene replicato come dipendenza della vista "{2}"."{3}".
#XMSG: Unsupported replication of row store tables
ECN_ROW_STORE_REPLICATION=Impossibile replicare la tabella "{0}"."{1}", perché la replicazione della tabella di righe è obsoleta.
#XMSG: Max ECN instances exceeded
ECN_INSTANCES_MAX=Nodo di calcolo elastico massimo per istanza SAP HANA Cloud superato.
#XMSG: ECN current operations are in progress based on HANA instance status
ECN_UPDATE_IN_PROGRESS=Operazione di esecuzione del nodo di calcolo elastico {0} ancora in corso.
#XMSG: ECN drop replica stopped
ECN_DROP_REPLICA_STOPPED=A causa di problemi tecnici, l''eliminazione della replicazione per la tabella di origine {0} è stata arrestata. Riprovare più tardi.
#XMSG: ECN create replica stopped
ECN_CREATE_REPLICA_STOPPED=A causa di problemi tecnici, la creazione della replicazione per la tabella di origine {0} è stata arrestata. Riprovare più tardi.
#XMSG: ECN limiting usage at start
ECN_LIMITING_USAGE=Impossibile avviare un nodo di calcolo elastico poiché è stato raggiunto il limite di utilizzo o non sono ancora state assegnate ore di blocco calcolo.
#XMSG: Task log message for chain preparation
chainLoadFromRepository=Caricamento della catena di task e preparazione dell''esecuzione di un totale di {0} task parte di questa catena.
#NOTR: Activity text
ADD=Add
#NOTR: Activity text
REMOVE=Remove
#NOTR: Activity text
CREATE_REPLICA=Create Replica
#NOTR: Activity text
DROP_REPLICA=Drop Replica
#NOTR: Activity text
ROUTE_COMPUTE_SERVER=Route Compute Server
#NOTR: Activity text
ROUTE_COORDINATOR=Route Coordinator
#NOTR: Activity text
GENERATE_START_CHAIN=Generate Start Chain
#NOTR: Activity text
GENERATE_STOP_CHAIN=Generate Stop Chain
#NOTR: Activity text
RUN_CHAIN_TECHNICAL=Run Chain Technical
#NOTR: Activity text
RUN_CHAIN=RUN_CHAIN
#NOTR: Activity text
TEST_RUN=TEST_RUN
#NOTR: Activity text
RUN=RUN
#XMSG: Conflicting tasks running at the same time
taskAlreadyRunning=È già in esecuzione un task in conflitto
#XMSG: Replication will change
txt_replication_change=Il tipo di replicazione verrà modificato.
txt_repl_viewdetails=Visualizza dettagli

#NOTR: Activity text
VALIDATE=Validate
#NOTR: Activity text
UPLOAD_DATA=Upload Data
#XMSG: Error text for Task chain might have children scenario .. no latest failed child task found
noFailedChildTaskErrorTxt=Sembra che si sia verificato un errore nell'ultimo tentativo di esecuzione, poiché l'esecuzione del task precedente non è riuscita prima che il piano potesse essere generato.

#general messages
EXECUTION_ERROR_LOCKED_SPACE=Spazio "{0}" bloccato.

#Local Tables Task Validation Messages
#XMSG: Validation error message for local table task
LOCAL_TABLE_NOT_DEPLOYED=L''attività {0} richiede la distribuzione della tabella locale {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_DELTA_CAPTURE=L''attività {0} richiede l''attivazione dell''acquisizione delta per la tabella locale {1}.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_FILE_STORAGE=L''attività {0} richiede la memorizzazione dei dati da parte della tabella locale {1} nell''Object Store.
#XMSG: Validation error message for local table task
LOCAL_TABLE_NO_HANA_STORAGE=L''attività {0} richiede la memorizzazione dei dati da parte della tabella locale {1} nel database.

#LOCAL TABLE Deleting records messages
#XMSG: Task log message for starting removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING=Avvio della rimozione dei record eliminati per la tabella locale {0}.
#XMSG: Task log message for starting removing records
LT_REMOVE_RECORDS_RUN_STATUS_STARTING=Avvio dell''eliminazione di tutti i record per la tabella locale {0}.
#XMSG: Task log message for starting removing records with filter (delta capture OFF)
LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Avvio dell''eliminazione dei record per la tabella locale {0} in base all condizione di filtro {1}.
#XMSG: Task log message for displaing error message on failing on removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR=Si è verificato un errore nella rimozione dei record eliminati per la tabella locale {0}.
#XMSG: Task log message for displaing error message on failing on deleting records
LT_REMOVE_RECORDS_RUN_FAILED_ERROR=Si è verificato un errore nell''eliminazione di tutti i record per la tabella locale {0}.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables via retention time in days
LT_REMOVE_DELETED_RECORDS_STATUS_INFO=Eliminazione di tutti i record completamente elaborati con tipo di modifica "Eliminato" precedenti a {0} giorni.
#XMSG: Task log message for displaying info removing deleted record on "delta enable" local tables older than the watermark date
LT_REMOVE_DELETED_RECORDS_STATUS_WARNING=Eliminazione di tutti i record completamente elaborati con tipo di modifica "Eliminato" precedenti a {0}.
#XMSG: Task log message for displaying the number of records deleted
LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} record sono stati eliminati.
#XMSG: Task log message for displaying the number of records deleted for delta capture ON
LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS={0} record sono stati contrassegnati per l’eliminazione.
#XMSG: Task log message for displaying message on success for removing deleted records on "delta enable" local tables
LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED=La rimozione dei record eliminati per la tabella locale {0} è stata completata.
#XMSG: Task log message for displaying message on success for removing records
LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED=L''eliminazione di tutti i record per la tabella locale {0} è stata completata.
#XMSG: Task log message for starting mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING=Avvio del contrassegno dei record come "Eliminato" per la tabella locale {0}.
#XMSG: Task log message for starting removing records with filter (delta capture ON)
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER=Avvio del contrassegno dei record come "Eliminato" per la tabella locale {0} in base all condizione di filtro {1}.
#XMSG: Task log message on  success for mark records to deleted on "delta enable" local tables
LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED=Il contrassegno dei record come "Eliminato" per la tabella locale {0} è stata completato.

#LOCAL TABLE Load procedure messages (used in Seal)
#XMSG: Task log message after request has started
LOCAL_TABLE_REQUEST_STARTED=È in corso il caricamento temporaneo delle modifiche ai dati nella tabella {0}.
#XMSG: Task log message after request has loaded data changes
LOCAL_TABLE_REQUEST_LOADED=Le modifiche ai dati sono state temporaneamente caricate nella tabella {0}.
#XMSG: Task log message after request has been processed and deleted
LOCAL_TABLE_REQUEST_DELETED=Le modifiche ai dati sono state elaborate ed eliminate dalla tabella {0}.

#LOCAL TABLE FILES messages
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Dettagli di connessione.

#LOCAL TABLE FILES OPTIMIZE messages
#XMSG: Task log message
LTF_OPTIMIZE_STARTING=Avvio ottimizzazione della tabella locale (file).
#XMSG: Task log message
LTF_OPTIMIZE_FAILED_ERROR=Errore di ottimizzazione della tabella locale (file).
#XMSG: Task log message
LTF_OPTIMIZE_STOPPED=Si è verificato un errore. Ottimizzazione della tabella locale (file) interrotta.
#XMSG: Task log message
LTF_OPTIMIZE_PROCESSING=Ottimizzazione della tabella locale (file) in corso...
#XMSG: Task log message
LTF_OPTIMIZE_METRICS=La tabella locale (file) è stata ottimizzata.
#XMSG: Task log message
LTF_OPTIMIZE_SUCCESS=La tabella locale (file) è ottimizzata.
#XMSG: Task log message
LTF_OPTIMIZE_ZORDER=La tabella è ottimizzata con le colonne Z-Order: {0}.

#LOCAL TABLE FILES TRUNCATE messages
#XMSG: Task log message
LTF_TRUNCATE_STOPPED=Si è verificato un errore. Troncamento della tabella locale (file) interrotto.
#XMSG: Task log message
LTF_TRUNCATE_PROCESSING=Troncamento della tabella locale (file) in corso...
#XMSG: Task log message
LTF_TRUNCATE_DROP_INBOUND_BUFFER=L'ubicazione buffer in entrata per la tabella locale (file) è stata eliminata.

#LOCAL TABLE FILES VACUUM messages
#XMSG: Task log message
LTF_VACUUM_STARTING=Avvio dello svuotamento (eliminazione di tutti i record completamente elaborati) della tabella locale (file).
#XMSG: Task log message
LTF_VACUUM_FAILED_ERROR=Errore di svuotamento della tabella locale (file).
#XMSG: Task log message
LTF_VACUUM_STOPPED=Si è verificato un errore. Svuotamento della tabella locale (file) interrotto.
#XMSG: Task log message
LTF_VACUUM_PROCESSING=Svuotamento della tabella locale (file) in corso...
#XMSG: Task log message
LTF_VACUUM_SUCCESS=Svuotamento completato.
#XMSG: Task log message
LTF_VACUUM_STATUS_INFO=Eliminazione di tutti i record completamente elaborati precedenti a {0} giorni.
#XMSG: Task log message
LTF_VACUUM_STATUS_WARNING=Eliminazione di tutti i record completamente elaborati precedenti a {0}.

#LOCAL TABLE FILES MERGE messages
#XMSG: Task log message for starting the merge on Local Table (File)
LTF_MERGE_STARTING=Avvio dell'unione dei nuovi record con la tabella locale (file).
#XMSG: Task log message for starting the merge on Local Table (File) using the setting "Merge Data Automatically"
LTF_AUTO_MERGE_STARTING=Avvio dell'unione dei nuovi record con la tabella locale (file) mediante l'impostazione "Unisci dati automaticamente".
#XMSG: Task log message for starting the merge on Local Table (File), which was initiated through the API Merge Request
LTF_API_MERGE_STARTING=Avvio dell'unione dei nuovi record con la tabella locale (file). Questo task è stato avviato tramite la richiesta di unione API.
#XMSG: Task log message for merging new records with Local Table (File)
LTF_MERGE_PROCESSING=Unione dei nuovi record con la tabella locale (file) in corso...
#XMSG: Task log message for displaying error message on merging new records with Local Table (File)
LTF_MERGE_FAILED_ERROR=Errore di unione dei nuovi record con la tabella locale (file).
#XMSG: Task log message for displaying successful merge of new records with Local Table (File)
LTF_MERGE_SUCCESS=La tabella locale (file) è unita.
#XMSG: Task log message for displaying error message on stopping the merge of new records with Local Table (File)
LTF_MERGE_STOPPED=Si è verificato un errore. Unione della tabella locale (file) interrotta.
#XMSG: Task log message for displaying warning message on failed merge of new records with Local Table (File) which was partially successful
LTF_MERGE_PARTIALLY_SUCCESSFUL=L'unione della tabella locale (file) non è riuscita a causa di un errore, ma l'operazione è parzialmente riuscita e alcuni dati sono già stati uniti.
#XMSG: Task log message for displaying error message on timeout
LTF_ERROR_TIMEOUT=Si è verificato un errore di timeout. L''esecuzione dell''attività {0} è stata effettuata per {1} ore.
#XMSG: Task log message for displaying error message on asynchronous execution could not start
LTF_ERROR_ASYNC_NOT_STARTED=L'esecuzione asincrona non è stata avviata a causa di un carico di sistema elevato. Aprire il Monitor di sistema e verificare i task in esecuzione.
#XMSG: Task log message for displaying error message on cancelled asynchronous execution
LTF_ERROR_CANCELED=L'esecuzione asincrona è stata annullata.
#XMSG: Task log message for displaying resource consumption on asynchronous execution
LTF_RESOURCE_CONSUMPTION=Il task {0} è stato eseguito entro i limiti di memoria di {1} e {2}.
#XMSG: Task log message for displaying resource ID on asynchronous execution
LTF_RESOURCE_ID=Il task {0} è stato eseguito con l''ID risorsa {1}.

#XMSG: Task log message - Find and replace started
LTF_ETV_FIND_AND_REPLACE_STARTED=Operazione Trova e sostituisci avviata nella tabella locale (file).
#XMSG: Task log message - Find and replace completed successfully
LTF_ETV_FIND_AND_REPLACE_COMPLETED=Operazione Trova e sostituisci completata nella tabella locale (file).
#XMSG: Task log message - Find and replace is failed
LTF_ETV_FIND_AND_REPLACE_ERROR=Operazione Trova e sostituisci non riuscita nella tabella locale (file).

#XMSG: Task log message
LTF_UPDATE_STATISTICS_LOCAL_TABLE_STOPPED=Si è verificato un errore. Aggiornamento delle statistiche della tabella locale (file) interrotto.

#HANA Resource Limit error messages uses_in tasks
#XMSG: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=Il task non è riuscito a causa di un errore di esaurimento della memoria nel database SAP HANA.
#XMSG: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=Il task non è riuscito a causa di un rifiuto del controllo accettazione SAP HANA.
#XMSG: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=Il task non è riuscito a causa di troppe connessioni SAP HANA attive.

#XMSG: Error message for task  retry with error code initiateChainRetry.nestedChain
nestedChainErrorTxt=Impossibile eseguire l'operazione Riprova perché i nuovi tentativi sono consentiti solo nell'elemento sovraordinato di una catena di task annidati.
#XMSG: Error message for task retry with error code initiateChainRetry.undefinedStatus
undefinedStatusErrorTxt=I registri dei task secondari non riusciti non sono più disponibili per un nuovo tentativo per continuare.


####Metrics Labels

performanceOptimized=Ottimizzato per prestazione
memoryOptimized=Ottimizzato per memoria

JOB_EXECUTION=Esecuzione processo
EXECUTION_MODE=Modalità esecuzione
NUMBER_OF_RECORDS_OVERALL=Numero di record con persistenza
NUMBER_OF_RECORDS_REMOTE_RECORD_COUNT=Numero di record letti dall'origine remota
RUNTIME_MS_REMOTE_EXECUTION_TIME=Tempo di elaborazione origine remota
MEMORY_CONSUMPTION_GIB=Memoria massima SAP HANA
NUMBER_OF_PARTITIONS=Numero di partizioni
MEMORY_CONSUMPTION_GIB_OVERALL=Memoria massima SAP HANA
NUMBER_OF_PARTITIONS_LOCKED=Numero di partizioni bloccate
PARTITIONING_COLUMN=Colonna di partizionamento
HANA_PEAK_CPU_TIME=Tempo CPU totale SAP HANA
USED_IN_DISK=Archivio utilizzato
INPUT_PARAMETER_PARAMETER_VALUE=Parametro di input
INPUT_PARAMETER=Parametro di input
ECN_ID=Nome nodo di calcolo elastico

DAC=Controlli di accesso ai dati
YES=Sì
NO=No
noofrecords=Numero di record
partitionpeakmemory=Memoria massima SAP HANA
value=Valore
metricsTitle=Metriche ({0})
partitionmetricsTitle=Partizioni ({0})
partitionLabel=Partizione
OthersNotNull=Valori non inclusi negli intervalli
OthersNull=Valori nulli
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Impostazioni utilizzate per l'ultima esecuzione di persistenza dei dati:
#XMSG: Message for input parameter name
inputParameterLabel=Parametro di input
#XMSG: Message for input parameter value
inputParameterValueLabel=Valore
#XMSG: Message for persisted data
inputParameterPersistedLabel=Ora di persistenza
#XTIT: Count for View in ViewMonitor Toolbar

#TXT: Text for activity in Local Table Object Details Page
DELETE_DATA=Elimina dati
REMOVE_DELETED_RECORDS=Rimuovi record eliminati
MERGE_FILES=Unisci file
OPTIMIZE_FILES=Ottimizza file
#XFLD: View in BW Bridge Monitor
VIEW_IN_BW_BRIDGE=Visualizza nel monitor del ponte SAP BW

ANALYZE_PERFORMANCE=Analizza prestazioni
CANCEL_ANALYZE_PERFORMANCE=Annulla analisi delle prestazioni

#XFLD: Label for frequency column
everyLabel=Ogni
#XFLD: Plural Recurrence text for Hour
hoursLabel=Ore
#XFLD: Plural Recurrence text for Day
daysLabel=Giorni
#XFLD: Plural Recurrence text for Month
monthsLabel=Mesi
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuti

#XTXT TEXT for view persistency guide link
VIEW_PERSISTENCY_GUIDE=<a href="https://help.sap.com/docs/SAP_DATASPHERE/be5967d099974c69b77f4549425ca4c0/e3d04951a4a344c28b25b2b1b13bf3d8.html?state=DRAFT">Visualizza la guida alla risoluzione dei problemi di persistenza</a>
#XTXT TEXT for view persistency guide link
OOMMessage=Memoria del processo esaurita. Impossibile applicare la persistenza ai dati per la vista "{0}". Consultare il portale dell''help per ulteriori informazioni sugli errori di esaurimento della memoria. Prendere in considerazione di consultare l''analizzatore vista per analizzare la vista in termini di complessità del consumo di memoria.

#XFLD: Message for table stored on disk -> memory size is not applicable
NOT_APPLICABLE=Non rilevante
OPEN_BRACKET=(
CLOSE_BRACKET=)
