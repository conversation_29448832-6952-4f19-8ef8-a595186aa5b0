/**
 * Copyright 2020 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { State } from "@sap/dwc-circuit-breaker";
import { ShellNavigationService } from "@sap/orca-shell";
import { isUndefined } from "lodash";
import moment from "moment-timezone";
import { AdaptionLocationType, ReplicationStatus, ReplicationType } from "../../../../shared/remoteTables/types";
import { isDiMonitorImprovementsEnabled } from "../../abstractbuilder/commonservices/FeatureFlagCheck";
import { smartExtend } from "../../basecomponent/controller/BaseController.controller";
import { PrivilegeTypes } from "../../databuilder/utility/Constants";
import { DataIntegrationToolAdapter } from "../../dataintegration/DataIntegrationToolAdapter";
import { PartitionDialog } from "../../monitorUtil/controller/PartitionDialog.controller";
import { CreateStatistics, DeleteStatistics } from "../../monitorUtil/util/StatisticsUtil";
import { IRemoteQueryUI } from "../../remotequerymonitor/utility/Types";
import { issueCause, replicationAction } from "../../remotetablemonitor/utility/Constants";
import { RemoteTableServiceUtil } from "../../remotetablemonitor/utility/RemoteTableServiceUtil";
import { checkForCustomerDPTenantAndSAPSpace } from "../../reuse/utility/DataPlaneConstraintsUtil";
import { ExtensionPoint } from "../../reuse/utility/ExtensionPoint";
import { Format } from "../../reuse/utility/Format";
import { MessageHandler } from "../../reuse/utility/MessageHandler";
import { getResourceText } from "../../reuse/utility/MessageLoader";
import { ContentType, DataType, HttpMethod, ServiceCall } from "../../reuse/utility/ServiceCall";
import { CDS_TO_ICON, taskLogIconMap } from "../../reuse/utility/Types";
import { isCircuitBreakerYellowStateEnabled } from "../../shell/featureState/CircuitBreakerYellowStateFeatureState";
import { ShellContainer } from "../../shell/utility/Container";
import { Repo } from "../../shell/utility/Repo";
import { EventType } from "../../shell/utility/ShellUsageCollectionService";
import { User } from "../../shell/utility/User";
import { ITTaskScheduleController, ITaskScheduleRequest } from "../../taskscheduler/controller/TaskSchedule.controller";
import { getTaskScheduer, openSchedulePopover, recordAction } from "../../taskscheduler/utility/ScheduleUtil";
import { PreferenceChanges } from "../../userSettings/utility/Constants";
import { AnalyzerDetailsDialog } from "../../viewanalyzer/controller/AnalyzerDetailsDialog.controller";
import { EntitiesClass } from "../../viewanalyzer/controller/Entities.controller";
import { ExplainPlanDetailsClass } from "../../viewanalyzer/controller/ExplainPlanDetails.controller";
import { StartAnalyzerDialog } from "../../viewanalyzer/controller/StartAnalyzerDialog.controller";
import { showRuntimeMetricsdialog } from "../../viewmonitor/reuse/RuntimeMetricsUtility";
import {
  SERVICENAMES,
  fetchViewPersistenceService,
  handleViewPersistenceResponse,
  openPartiallyPersistedInfo,
  replicationState,
} from "../../viewmonitor/viewpersistencyservice/ServiceConsumption";
import {
  Activity,
  ApplicationId,
  IMessage,
  ITaskLogIncomplete,
  ITaskLogResponse,
  JobExecutionType,
  MAP_MESSAGEBUNDLE_IDS,
  TaskStatus,
  dataAccessType,
  metrics_value,
  monitoringObjectType,
  objectDetailsRoutes,
  taskLogRoutes,
} from "../utility/Constants";
import * as formatterJS from "../utility/formatters";
import * as logFormatterUtils from "../utility/logFormatterUtils";
import { TaskLogBase, TaskLogBaseClass } from "./TaskLogBase.controller";
const encodeXML = sap.ui.require("sap/base/security/encodeXML");

export class TaskLogClass extends TaskLogBaseClass {
  private router: sap.m.routing.Router;
  public partiallyPersistedModel: sap.ui.model.json.JSONModel;
  selectedTaskLogId: string;
  taskLogHeaderDetails: sap.uxap.ObjectPageLayout;
  private srcObjectId: string;
  private sTableHeader: string;
  private activity: string;
  private sDetailsTableHeader: string;
  private chainTableHeader: string;
  private routeName: string;
  formatter: any;
  private extensionPoint: ExtensionPoint;
  private serviceUtil: any;
  private isRemoteTableRoute: boolean;
  private isViewMonitorRoute: boolean;
  private isTaskChainRoute: boolean;
  private isStatisticsRoute: boolean;
  partitionDialog: PartitionDialog;
  partitionView: sap.ui.core.mvc.View;
  statisticsDialog;
  staticsView: sap.ui.core.mvc.View;
  partitionPopover: sap.m.Popover;
  exceptionID: any;
  remoteSource: any;
  messageDialog: sap.m.Dialog;
  scheduleList: any;
  settings: any;
  newPartitionView: any;
  analyzerDetailsDialog: AnalyzerDetailsDialog;
  persistencyAnalyzerView: any;
  displayName: any;
  user: User;
  isFVT: boolean;
  location: string;
  viewSettingsDialogs = {};
  subStatusPopover: sap.m.Popover;
  oDetails: any;
  taskStatHeaderDetails: any;
  taskLogStatisticsHeaderDetails: any;
  analyzerView: any;
  analyzerDialog: any;
  entitiesView: sap.ui.core.mvc.View;
  applicationId: any;
  rangeSelected = false;
  numOfQueries: number;
  statementId: any;
  /**
   * init
   *
   * @memberof TaskLogClass
   */
  public onInit(): void {
    require("../css/style.css");
    super.onInit();
    const extensionMetatada = require("../extensions.json");
    this.extensionPoint = ExtensionPoint.create(extensionMetatada);
    this.partiallyPersistedModel = new sap.ui.model.json.JSONModel();
    this.getView().setModel(this.partiallyPersistedModel, "partiallyPersistedModel");
    this.view = this.getView();
    this.setupModels();
    this.setupRouter();
    this.displayNameCheck();
    this.attachHanaStateChangeHandler();
    this["isReusableTaskScheduleFFEnabled"] = sap.ui
      .getCore()
      .getModel("featureflags")
      .getProperty("/DWCO_REUSABLE_TASK_SCHEDULING_UI");
    this["isAdoptionOfTaskSchedulerEnabled"] = sap.ui
      .getCore()
      .getModel("featureflags")
      .getProperty("/DWCO_REUSABLE_TASK_SCHEDULER_ADOPTION");
  }

  private displayNameCheck() {
    sap.ui
      .getCore()
      .getEventBus()
      .subscribe("userPreferences", "change", (_, __, preferenceChanges: PreferenceChanges) => {
        if (!preferenceChanges.changes.DWC_OBJECT_NAME_DISPLAY) {
          return;
        }
        const objectNameDisplay = this.displayName;
        if (objectNameDisplay !== preferenceChanges.changes.DWC_OBJECT_NAME_DISPLAY.newValue) {
          this.displayName = preferenceChanges.changes.DWC_OBJECT_NAME_DISPLAY.newValue;
          this.onCompleteRefresh();
        }
      });
  }

  public setupModels(): void {
    // default model which contains data regarding remote table infos and performance
    super.setupModels();

    this.view.setModel(new sap.ui.model.json.JSONModel({}), "taskChainTreeModel");
    this.view.setModel(new sap.ui.model.json.JSONModel({}), "replicationModel");
    this.view.setModel(new sap.ui.model.json.JSONModel({}), "privilegeModel");
    this.view.setModel(new sap.ui.model.json.JSONModel({}), "schedulingModel");
    this.view.setModel(new sap.ui.model.json.JSONModel({}), "persistencyActionModel");
    this.view.setModel(new sap.ui.model.json.JSONModel({}), "statisticsModel");
    this.view.setModel(new sap.ui.model.json.JSONModel({}), "metricesModel");
    this.view.setModel(new sap.ui.model.json.JSONModel({}), "routerModel");
    this.view.setModel(new sap.ui.model.json.JSONModel({}), "settingsModel");
    this.view.setModel(new sap.ui.model.json.JSONModel({}), "partitionSettingsModel");
    this.view.setModel(new sap.ui.model.json.JSONModel({}), "blobContentMessageModel");

    /** Below model properties are set for scheduling **/
    (this.view.getModel("schedulingModel") as sap.ui.model.json.JSONModel).setProperty("/newSchedule", false);
    (this.view.getModel("schedulingModel") as sap.ui.model.json.JSONModel).setProperty("/editSchedule", false);
    (this.view.getModel("schedulingModel") as sap.ui.model.json.JSONModel).setProperty("/deleteSchedule", false);

    /** Below model properties are set for remote table replication **/
    (this.view.getModel("replicationModel") as sap.ui.model.json.JSONModel).setProperty("/loadNewSnapshot", false);
    (this.view.getModel("replicationModel") as sap.ui.model.json.JSONModel).setProperty("/removeReplicatedData", false);
    (this.view.getModel("replicationModel") as sap.ui.model.json.JSONModel).setProperty("/enableRealTimeAccess", false);

    /** Below model properties are set for Data Statistics **/
    (this.view.getModel("statisticsModel") as sap.ui.model.json.JSONModel).setProperty("/newStatistics", false);
    (this.view.getModel("statisticsModel") as sap.ui.model.json.JSONModel).setProperty("/editStatistics", false);
    (this.view.getModel("statisticsModel") as sap.ui.model.json.JSONModel).setProperty("/refreshStatistics", false);
    (this.view.getModel("statisticsModel") as sap.ui.model.json.JSONModel).setProperty("/dropStatistics", false);

    const isSDPEnabled = sap.ui.getCore().getModel("featureflags").getProperty("/DWC_DUMMY_SPACE_PERMISSIONS");
    if (!isSDPEnabled) {
      (this.view.getModel("privilegeModel") as sap.ui.model.json.JSONModel).setProperty(
        "/read",
        ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_DATAINTEGRATION").read
      );
      (this.view.getModel("privilegeModel") as sap.ui.model.json.JSONModel).setProperty(
        "/update",
        ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_DATAINTEGRATION").update
      );
      (this.view.getModel("privilegeModel") as sap.ui.model.json.JSONModel).setProperty(
        "/execute",
        ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_DATAINTEGRATION").execute
      );
    }

    (this.view.getModel("taskChainTreeModel") as sap.ui.model.json.JSONModel).setProperty("/", { nodes: [] });

    this.view.setModel(
      new sap.ui.model.json.JSONModel({ isCustomerDPTenantAndSAPSpace: false }),
      "dataplaneRestrictionModel"
    );
    const oBundle = this.getBundle();
    this.view.setModel(
      new sap.ui.model.json.JSONModel({
        tableId: {
          LOGS: "taskLogTable",
          MESSAGES: "taskLogMessageTable",
          CHAIN_MESSAGES: "taskChainMessageTable",
          TASK_CHAINS: "taskChainLogMessagesTable",
          METRICS: "partitionMetricsTable",
        },
        dialogLayout: {
          MESSAGES_SORT: "MessagesSortDialog",
          MESSAGES_FILTER: "MessagesFilterDialog",
          LOGS_SORT: "TaskLogSortDialog",
          BB_LOGS_SORT: "BBTaskLogSortDialog",
          LOGS_FILTER: "TaskLogFilterDialog",
          CHAIN_MESSAGES_SORT: "ChainMessagesSortDialog",
          CHAIN_MESSAGES_FILTER: "ChainMessagesFilterDialog",
          METRICS_SORT: "MetricsSortDialog",
        },
        tableSearchId: {
          taskLogTable: "taskLogsSearch",
          taskLogMessageTable: "messagesSearch",
          taskChainMessageTable: "chainMessageSearch",
        },
        messagesFilterBarText: oBundle.getText("filterByTxt"),
      }),
      "viewSettingsDialogModel"
    );
    const subStatusErrModel = new sap.ui.model.json.JSONModel({});
    this.view.setModel(subStatusErrModel, "subStatusErrModel");
  }

  private isHanaDown() {
    const circuitBreakerModel = sap.ui.getCore().getModel("circuitbreaker");
    if (circuitBreakerModel) {
      const hanaState = circuitBreakerModel?.getProperty("/DataHANA");
      const isHanaDown: boolean =
        hanaState === State.Red || (hanaState === State.Yellow && !isCircuitBreakerYellowStateEnabled());
      const dataHANAProvisioningState = circuitBreakerModel?.getProperty("/DataHANAProvisioningState");
      const isHanaUpgradeInProgress: boolean =
        dataHANAProvisioningState === State.Red ||
        (dataHANAProvisioningState === State.Yellow && !isCircuitBreakerYellowStateEnabled());
      if (isHanaDown || isHanaUpgradeInProgress) {
        return true;
      }
    }
    return false;
  }

  private checkValidView(event: sap.ui.base.Event): boolean {
    const views = event.getParameter("views");
    const view = views.find((view) => view.getId() === this.view.getId());
    return Boolean(view);
  }

  public setupRouter(): void {
    this.router = sap.ui.core.UIComponent.getRouterFor(this) as sap.m.routing.Router;
    this.router.attachRouteMatched("taskLog", async (event: sap.ui.base.Event) => {
      if (
        (taskLogRoutes.includes(event.getParameter("name")) || event.getParameter("arguments").activity) &&
        this.checkValidView(event)
      ) {
        this.view.setBusy(true);
        this.routeName = event.getParameter("name");
        const args = event.getParameter("arguments");
        this.spaceId = ["ecntaskchainmonitoring", "ecnlogsmonitoring"].includes(this.routeName)
          ? "$$ecn$$"
          : args.spaceId;
        if (!this.spaceId) {
          return;
        }
        if (!ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_SPACEFILE")?.read) {
          ShellNavigationService.toBadRequest();
          return;
        }
        this.setHelpScreenIDForRoutes(this.routeName);
        this.checkRoute(this.routeName);

        this.setupFlexColumnLayout();

        if (this.routeName === "viewMonitorTaskLog" || this.routeName === "viewMonitorTaskLogDetails") {
          const isCustomerDPTenantAndSAPSpace = await checkForCustomerDPTenantAndSAPSpace(this.spaceId);
          (this.view.getModel("dataplaneRestrictionModel") as sap.ui.model.json.JSONModel).setProperty(
            "/isCustomerDPTenantAndSAPSpace",
            isCustomerDPTenantAndSAPSpace
          );
        }
        if (this.routeName === "logsmonitoring") {
          this.resetPrivilegeModel();
        }
        if (args.objectId) {
          this.srcObjectId = args.objectId;
          (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty(
            "/objectId",
            this.srcObjectId
          );
        }
        if (this.isHanaDown()) {
          this.view.setBusy(false);
          this.view.getModel("detailsModel").setProperty("/statInfoVisible", false);
          this.view.getModel("messagesModel").setProperty("/", []);
          this.view.getModel("selectedModel").setProperty("/", {});
          this.view.getModel("taskChainTreeModel").setProperty("/", []);
          return;
        }

        const oParameter = {
          srcObjectId: undefined,
          selectedTaskLogId: undefined,
          activity: undefined,
          applicationId: undefined,
        };
        const exts = await this.extensionPoint.findRoute(this.routeName); // Here the extender's Component.ts is loaded if needed
        if (exts !== undefined) {
          this.formatter = exts.getExtension("formatter");
          this.serviceUtil = exts.getExtension("service");
        }
        (this.view.getModel("routerModel") as sap.ui.model.json.JSONModel).setProperty("/routeName", this.routeName);

        if (args.objectId) {
          this.srcObjectId = args.objectId;
          oParameter.srcObjectId = this.srcObjectId;
          (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty(
            "/objectId",
            this.srcObjectId
          );
        }
        this.user = await User.getInstance();
        this.displayName = this.user.getObjectNameDisplay();
        if (args.taskLogId) {
          this.selectedTaskLogId = args.taskLogId;
          oParameter.selectedTaskLogId = this.selectedTaskLogId;
        }
        const growing = args.taskLogId ? false : true;
        this.setLogsTableGrowing(growing);
        if (!this.isECNRoute() && !this.isGenericLogs()) {
          await this.updateSpaceLockedDetail();
        }

        const isSDPEnabled = sap.ui.getCore().getModel("featureflags").getProperty("/DWC_DUMMY_SPACE_PERMISSIONS");
        const isSpaceLocked = this.view.getModel("settingsModel").getProperty("/isSpaceLocked");
        const canCreateOrUpdate = !isSpaceLocked;

        if (!this.isECNRoute() && !this.isGenericLogs()) {
          this.spaceType = await this.loadSpaceInfo();
        }

        if (isSDPEnabled) {
          (this.view.getModel("privilegeModel") as sap.ui.model.json.JSONModel).setProperty(
            "/canCreateOrUpdateMonitoringUI",
            canCreateOrUpdate
          );
        } else {
          const canUpdate =
            canCreateOrUpdate &&
            ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_DATAINTEGRATION").update;
          const canExecute =
            canCreateOrUpdate &&
            ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_DATAINTEGRATION").execute;
          (this.view.getModel("privilegeModel") as sap.ui.model.json.JSONModel).setProperty("/update", canUpdate);
          (this.view.getModel("privilegeModel") as sap.ui.model.json.JSONModel).setProperty("/execute", canExecute);
        }

        if (!isSDPEnabled) {
          const hasDatabuilderReadPrivilege = ShellContainer.get()
            .getPrivilegeService()
            .hasPrivilege("DWC_DATABUILDER", "read");
          (this.view.getModel("privilegeModel") as sap.ui.model.json.JSONModel).setProperty(
            "/hasDatabuilderReadPrivilege",
            hasDatabuilderReadPrivilege
          );
        }

        if (args.activity) {
          this.activity = args.activity;
          oParameter.activity = this.activity;
        }
        if (this.isECNRoute() || this.isGenericLogs()) {
          this.applicationId = oParameter.applicationId = args.objectType;
          this.selectedTaskLogId = oParameter.selectedTaskLogId = args.logId;
          if (this.isGenericLogs()) {
            this.selectedTaskLogId = oParameter.selectedTaskLogId = args.taskLogId;
            this.applicationId = oParameter.applicationId = args.applicationId;
          }
        }
        await this.getTaskLogDetails(oParameter);

        this.view.getModel("detailsModel").setProperty("/statInfoVisible", false);
        this.view.getModel("messagesModel").setProperty("/", []);
        this.view.getModel("selectedModel").setProperty("/", {});
        this.view.getModel("taskChainTreeModel").setProperty("/", []);
        if (
          !["importLogs", "logDetails", "ecnlogsmonitoring", "ecntaskchainmonitoring", "logsmonitoring"].includes(
            this.routeName
          )
        ) {
          (this.view.getModel("privilegeModel") as sap.ui.model.json.JSONModel).setProperty(
            "/isRemoteTable",
            this.isRemoteTableRoute
          );
          (this.view.getModel("privilegeModel") as sap.ui.model.json.JSONModel).setProperty(
            "/isViewMonitor",
            this.isViewMonitorRoute
          );
          (this.view.getModel("privilegeModel") as sap.ui.model.json.JSONModel).setProperty(
            "/isTaskChainMonitor",
            this.isTaskChainRoute
          );
          (this.view.getModel("privilegeModel") as sap.ui.model.json.JSONModel).setProperty(
            "/spaceType",
            this.spaceType
          );
          (this.view.getModel("privilegeModel") as sap.ui.model.json.JSONModel).setProperty(
            "/isStatisticsRoute",
            this.isStatisticsRoute
          );
          await this.setUpNewTaskLogUI();
        } else {
          const table = this.byId("taskLogTable");
          table.removeSelections();

          this.view.setBusy(false);
          (this.view.getModel("settingsModel") as sap.ui.model.json.JSONModel).setProperty("/isExeModeVisible", false);
          (this.view.getModel("settingsModel") as sap.ui.model.json.JSONModel).setProperty("/partitionSection", false);
        }
        await this.selectLog(oParameter);
      }
    });
  }

  private isECNRoute() {
    if (["ecntaskchainmonitoring", "ecnlogsmonitoring"].includes(this.routeName)) {
      return true;
    }
    return false;
  }

  private isGenericLogs() {
    if ("logsmonitoring".includes(this.routeName)) {
      return true;
    }
    return false;
  }

  private resetEntitiesUI() {
    const entitySection = this.view.byId(
      "shellMainContent---dataIntegrationComponent---taskLog--Entities"
    ) as sap.uxap.ObjectPageSection;
    entitySection?.setVisible(false);
  }

  private async updateSpaceLockedDetail() {
    const isSpaceLocked = await this.isSpaceLocked(this.spaceId);
    this.view.getModel("settingsModel").setProperty("/isSpaceLocked", isSpaceLocked);
  }

  private setLogsTableGrowing(growing: boolean) {
    const otaskLogTable = this.byId("taskLogTable") as sap.m.Table;
    otaskLogTable.setGrowing(growing);
    otaskLogTable.invalidate();
  }

  public async isSpaceLocked(spaceId: string): Promise<boolean> {
    const spaceObject = await ServiceCall.get("resources/spaces?islocked&spaceids=" + spaceId);
    if (spaceObject.data[spaceId]) {
      const data = spaceObject.data[spaceId];
      return data.isLocked;
    }
    return false;
  }

  setupFlexColumnLayout() {
    const routerModel = this.view.getModel("routerModel") as sap.ui.model.json.JSONModel;
    const isTaskChainRoute =
      this.routeName === "taskChainMonitorLog" ||
      this.routeName === "taskChainMonitorLogDetails" ||
      this.routeName === "ecntaskchainmonitoring";
    routerModel.setProperty("/layout", isTaskChainRoute ? "ThreeColumnsMidExpanded" : "TwoColumnsMidExpanded");
    const settingModel = this.view.getModel("viewSettingsDialogModel") as sap.ui.model.json.JSONModel;
    const logsTable = settingModel.getProperty("/tableId/LOGS");
    const messagesTable = settingModel.getProperty("/tableId/MESSAGES");
    const chainsMessagesTable = settingModel.getProperty("/tableId/CHAIN_MESSAGES");
    this.resetFilterAndSorters(
      logsTable,
      settingModel.getProperty("/dialogLayout/LOGS_SORT"),
      settingModel.getProperty("/dialogLayout/LOGS_FILTER")
    );
    this.resetFilterAndSorters(
      messagesTable,
      settingModel.getProperty("/dialogLayout/MESSAGES_SORT"),
      settingModel.getProperty("/dialogLayout/MESSAGES_FILTER")
    );
    this.resetFilterAndSorters(
      chainsMessagesTable,
      settingModel.getProperty("/dialogLayout/CHAIN_MESSAGES_SORT"),
      settingModel.getProperty("/dialogLayout/CHAIN_MESSAGES_FILTER")
    );
    const searchFieldId = settingModel.getProperty("/tableSearchId");
    this.resetSearchField(searchFieldId[logsTable]);
    this.resetSearchField(searchFieldId[messagesTable]);
    this.resetSearchField(searchFieldId[chainsMessagesTable]);
  }

  setLayout(layout) {
    const routerModel = this.view.getModel("routerModel") as sap.ui.model.json.JSONModel;
    routerModel.setProperty("/layout", layout);
  }
  runStatusVisibilityFormatter(status, subStatus) {
    if (status !== TaskStatus.FAILED || (status === TaskStatus.FAILED && !subStatus)) {
      return true;
    }
    return false;
  }
  subStatusLinkVisibilityFormatter(status, subStatus) {
    if (status === TaskStatus.FAILED && subStatus) {
      return true;
    }
    return false;
  }
  subStatusClickHandler(event, subStatus) {
    const sourceControl = event.getSource();
    const subStatusErr = this.getBundle().getText(`${subStatus}_ERR`);
    this.getView().getModel("subStatusErrModel").setProperty("/errDetail", subStatusErr);
    const oFragment = require("../view/settings/subStatusPopover.fragment.xml");
    if (!this.subStatusPopover) {
      this.subStatusPopover = sap.ui.xmlfragment(
        this.getView().getId() + "--subStatus",
        oFragment,
        this
      ) as sap.m.Popover;
      this.view.addDependent(this.subStatusPopover);
    }
    this.subStatusPopover.openBy(sourceControl, false);
  }

  onMidColumnClose() {
    const routerModel = this.view.getModel("routerModel") as sap.ui.model.json.JSONModel;
    const table = this.view.byId("taskLogTable") as any;
    table.removeSelections();
    routerModel.setProperty("/layout", "OneColumn");
  }

  onEndColumnClose() {
    const routerModel = this.view.getModel("routerModel") as sap.ui.model.json.JSONModel;
    const table = this.view.byId("taskChainLogMessagesTable") as any;
    table.setSelectedIndex(-1);
    routerModel.setProperty("/layout", "TwoColumnsMidExpanded");
  }

  getViewSettingsDialog(sDialogFragmentName) {
    let pDialog = this.viewSettingsDialogs[sDialogFragmentName];
    if (!pDialog) {
      const fragmentID = require(`../view/settings/${sDialogFragmentName}.fragment.xml`);
      pDialog = sap.ui.xmlfragment("taskLog", fragmentID, this);
      this.viewSettingsDialogs[sDialogFragmentName] = pDialog;
      this.getView().addDependent(pDialog);
    }
    return pDialog;
  }

  handleSortButtonPressed(oEvent, sDialogFragmentName) {
    const pDialog = this.getViewSettingsDialog(sDialogFragmentName);
    pDialog.open();
  }

  handleFilterButtonPressed(oEvent, sDialogFragmentName) {
    const pDialog = this.getViewSettingsDialog(sDialogFragmentName);
    pDialog.open();
  }

  handleSortDialogConfirm(oEvent, sTableId) {
    const oTable = this.byId(sTableId) as sap.m.Table,
      mParams = oEvent.getParameters(),
      oBinding = oTable.getBinding("items") as sap.ui.model.ListBinding,
      sPath = mParams.sortItem.getKey(),
      aSorters = [],
      bDescending = mParams.sortDescending;
    aSorters.push(new sap.ui.model.Sorter(sPath, bDescending));
    oBinding.sort(aSorters);
  }

  handleFilterDialogConfirm(oEvent, sTableId) {
    const oTable = this.byId(sTableId),
      mParams = oEvent.getParameters(),
      oBinding = oTable.getBinding("items"),
      aFilters = [];
    const searchFieldId = this.view.getModel("viewSettingsDialogModel").getProperty("/tableSearchId")[sTableId];
    if (searchFieldId) {
      this.resetSearchField(searchFieldId);
    }
    mParams.filterItems.forEach(function (oItem) {
      const aSplit = oItem.getKey().split("___"),
        sOperator = aSplit[1],
        sValue1 = aSplit[2],
        sValue2 = aSplit[3],
        sPath = aSplit[0];
      const oFilter = new sap.ui.model.Filter(sPath, sOperator, sValue1, sValue2);
      aFilters.push(oFilter);
    });
    // apply filter settings
    oBinding.filter(aFilters);
    // update filter bar
    (this.byId(`${sTableId}-FilterBar`) as sap.m.OverflowToolbar).setVisible(aFilters.length > 0);
    this.view.getModel("viewSettingsDialogModel").setProperty(`/${sTableId}FilterBarText`, mParams.filterString);
    this.updateTableHeader(sTableId, oTable.getMaxItemsCount());
    this.afterApplyFilter(sTableId);
  }

  afterApplyFilter(sTableId) {
    const model = this.view.getModel("viewSettingsDialogModel");
    const tableIds = model.getProperty("/tableId");
    const oTable = this.byId(sTableId);
    switch (sTableId) {
      case tableIds.LOGS:
        const items = oTable.getItems();
        if (items.length > 0) {
          oTable.setSelectedItem(items[0], true, true);
        } else {
          (this.view.getModel("messagesModel") as sap.ui.model.json.JSONModel).setProperty("/", []);
          (this.view.getModel("taskChainTreeModel") as sap.ui.model.json.JSONModel).setProperty("/", []);
          this.updateTableHeader("taskLogMessageTable", 0);
          this.updateTableHeader("taskChainLogMessagesTable", 0);
          this.updateTableHeader("taskChainMessageTable", 0);
        }
        break;
    }
  }

  resetSearchField(searchFieldId) {
    const searchField = this.view.byId(searchFieldId) as sap.m.SearchField;
    searchField?.["clear"]();
  }

  updateTableHeader(tableId, rowCount) {
    switch (tableId) {
      case "taskLogTable":
        const tableHeader = this.getBundle().getText("txtLogCount", [rowCount]);
        this.view.getModel("logsModel").setProperty("/tableHeader", tableHeader);
        break;
      case "taskLogMessageTable":
      case "taskChainMessageTable":
        const sDetailsTableHeader = this.getBundle().getText("txtDetailMessages", [rowCount]);
        this.view.getModel("logsModel").setProperty("/sDetailsTableHeader", sDetailsTableHeader);
        break;
    }
  }

  resetFilterAndSorters(tableId: string, sortDialog: string, filterDialog: string) {
    const oTable = this.byId(tableId);
    if (oTable) {
      const filterBar = this.byId(`${tableId}-FilterBar`) as sap.m.OverflowToolbar;
      const oBinding = oTable.getBinding("items") as sap.ui.model.ListBinding;
      oBinding.filter(null);
      oBinding.sort(null);
      filterBar.setVisible(false);
      this.view.getModel("viewSettingsDialogModel").setProperty(`/${tableId}FilterBarText`, "");
      const dSort = this.viewSettingsDialogs[sortDialog];
      const dFilter = this.viewSettingsDialogs[filterDialog];
      dSort?.destroy();
      dFilter?.destroy();
      this.viewSettingsDialogs[sortDialog] = null;
      this.viewSettingsDialogs[filterDialog] = null;
    }
  }

  public onMessageSearch(oEvent, sTableId, filterProperty, sortDialog, filterDialog) {
    const oTable = this.byId(sTableId);
    const aFilters = [];
    const sQuery = oEvent.getSource().getValue();
    const oBinding = oTable.getBinding("items");
    if (sQuery && sQuery.length > 0) {
      this.resetFilterAndSorters(sTableId, sortDialog, filterDialog);
      const filter = new sap.ui.model.Filter(filterProperty, sap.ui.model.FilterOperator.Contains, sQuery);
      aFilters.push(filter);
    }
    oBinding.filter(aFilters);
    this.updateTableHeader(sTableId, oTable.getMaxItemsCount());
  }

  public fireOnFilterColumn(): void {
    setTimeout(() => {
      const oElement: any = this.view.byId("taskLogTable");
      const oBinding: any = oElement.getBinding("rows");
      const filteredTableRecordLength = oBinding.getLength().toString();
      this.sTableHeader = this.getBundle().getText("txtLogCount", [filteredTableRecordLength]);
      (this.view.getModel("logsModel") as sap.ui.model.json.JSONModel).setProperty("/tableHeader", this.sTableHeader);
    }, 0);
  }

  public fireOnFilterColumnMsgTable(): void {
    setTimeout(() => {
      const oElement: any = this.view.byId("taskLogMessageTable");
      const oBinding: any = oElement.getBinding("rows");
      const filteredTableRecordLength = oBinding.getLength().toString();
      this.sDetailsTableHeader = this.getBundle().getText("txtDetailMessages", [filteredTableRecordLength]);
      (this.view.getModel("logsModel") as sap.ui.model.json.JSONModel).setProperty(
        "/sDetailsTableHeader",
        this.sDetailsTableHeader
      );
    }, 0);
  }

  private checkRoute(routeName: string) {
    this.isRemoteTableRoute = false;
    this.isViewMonitorRoute = false;
    this.isTaskChainRoute = false;
    this.isStatisticsRoute = false;
    if (routeName === "remoteTableTaskLog" || routeName === "remoteTableTaskLogDetails") {
      this.isRemoteTableRoute = true;
    } else if (routeName === "viewMonitorTaskLog" || routeName === "viewMonitorTaskLogDetails") {
      this.isViewMonitorRoute = true;
    } else if (
      routeName === "taskChainMonitorLog" ||
      routeName === "taskChainMonitorLogDetails" ||
      routeName === "ecntaskchainmonitoring"
    ) {
      this.isTaskChainRoute = true;
    } else if (
      routeName === "remoteTableStatisticsMonitorLog" ||
      routeName === "remoteTableStatisticsMonitorLogDetails"
    ) {
      this.isStatisticsRoute = true;
    }
  }

  private attachHanaStateChangeHandler() {
    const circuitBreakerModel = sap.ui.getCore().getModel("circuitbreaker");
    if (circuitBreakerModel) {
      const binding = new sap.ui.model.Binding(
        circuitBreakerModel,
        "/DataHANA",
        (circuitBreakerModel as any).getContext("/"),
        {}
      );
      binding.attachChange(() => {
        const hanaState = circuitBreakerModel?.getProperty("/DataHANA");
        const isHanaDown: boolean =
          hanaState === State.Red || (hanaState === State.Yellow && !isCircuitBreakerYellowStateEnabled());
        const dataHANAProvisioningState = circuitBreakerModel?.getProperty("/DataHANAProvisioningState");
        const isHanaUpgradeInProgress: boolean =
          dataHANAProvisioningState === State.Red ||
          (dataHANAProvisioningState === State.Yellow && !isCircuitBreakerYellowStateEnabled());
        if (isHanaDown || isHanaUpgradeInProgress) {
          const headerPane = this.view.byId("masterLogTableOPL") as sap.uxap.ObjectPageLayout;
          const title = headerPane.getHeaderTitle();
          title["getActions"]().forEach((action) => {
            action.setBlocked(true);
          });
          this.view.getModel("logsModel").setProperty("/", []);
          this.view.getModel("messagesModel").setProperty("/", []);
          this.view.getModel("metricesModel").setProperty("/", []);
          this.view.getModel("selectedModel").setProperty("/", {});
          this.view.getModel("taskChainTreeModel").setProperty("/", []);
          this.view.getModel("detailsModel").setProperty("/", {});
          this.view.getModel("selectedModel").setProperty("/", {});
          this.view.getModel("metricesModel").setProperty("/", {});
          this.view.getModel("detailsModel").setProperty("/showErrorStrip", false);
          this.view.getModel("detailsModel").setProperty("/displayName", this.srcObjectId);
        } else {
          const headerPane = this.view.byId("masterLogTableOPL") as sap.uxap.ObjectPageLayout;
          const title = headerPane.getHeaderTitle();
          title["getActions"]().forEach((action) => {
            action.setBlocked(false);
          });
          this.onCompleteRefresh();
        }
      });
    }
  }

  private async setUpNewTaskLogUI() {
    this.view.setBusy(true);
    (this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel).setProperty("/showErrorStrip", false);
    const oplName = "shellMainContent---dataIntegrationComponent---taskLog--masterLogTableOPL";
    const opl = this.view.byId(oplName) as sap.uxap.ObjectPageLayout;
    if (opl) {
      opl?.removeAllHeaderContent();
      opl?.getHeaderTitle()?.["removeAllActions"]();
    }
    if (!this.taskLogHeaderDetails && opl) {
      const fragmentID = require("../view/TaskLogHeaderDetails.fragment.xml");
      this.taskLogHeaderDetails = sap.ui.xmlfragment("taskLog", fragmentID, this) as sap.uxap.ObjectPageLayout;
      this.updateHeader(this.taskLogHeaderDetails, oplName, false);
    } else if (!this.isStatisticsRoute && opl) {
      this.updateHeader(this.taskLogHeaderDetails, oplName, true);
    }
    if (this.isStatisticsRoute && opl) {
      if (!this.taskLogStatisticsHeaderDetails) {
        const statFragmentID = require("../view/TaskLogStatisticsHeaderDetails.fragment.xml");
        this.taskLogStatisticsHeaderDetails = sap.ui.xmlfragment(
          "taskLog",
          statFragmentID,
          this
        ) as sap.uxap.ObjectPageLayout;
      }
      opl?.removeAllHeaderContent();
      opl?.getHeaderTitle()?.["removeAllActions"]();
      this.updateHeader(this.taskLogStatisticsHeaderDetails, oplName, true);
    }
    if (this.isTaskChainRoute && !this.isECNRoute() && !this.isGenericLogs()) {
      await this.getTaskChainDetail();
    } else if (!this.isECNRoute() && !this.isGenericLogs()) {
      await this.getObjectDetails(this.srcObjectId, this.routeName);
    }
  }

  protected updateBreadCrumb(text) {
    if (this.displayName === "businessName") {
      DataIntegrationToolAdapter.getInstance().setEntityBreadcrumbText(text);
    }
  }

  updateHeader(pageContent, opl, clearCache) {
    const headerPane = this.view.byId(opl) as sap.uxap.ObjectPageLayout;
    if (pageContent && headerPane) {
      const title = headerPane.getHeaderTitle();
      if (pageContent.getHeaderTitle().getExpandedHeading()) {
        title["setExpandedHeading"](pageContent.getHeaderTitle().getExpandedHeading());
      }
      if (pageContent.getHeaderTitle().getSnappedHeading()) {
        title["setSnappedHeading"](pageContent.getHeaderTitle().getSnappedHeading());
      }
      const actions = pageContent.getHeaderTitle().getActions();
      actions.forEach((action) => {
        title["addAction"](action);
      });
      title["removeStyleClass"]("taskLogMasterTitle");
      if (clearCache) {
        headerPane.removeAllHeaderContent();
      }
      headerPane.addHeaderContent(pageContent.getHeaderContent()[0]);
    }
  }

  async getTaskChainDetail() {
    let scheduleDialog;
    if (this["isReusableTaskScheduleFFEnabled"] && this["isAdoptionOfTaskSchedulerEnabled"]) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
    } else {
      scheduleDialog = (
        this.getView().byId("taskSchedulingDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    const user = User.getInstance();
    const displayNamePreference = user.getObjectNameDisplay();
    const isBusinessNameEnabled = displayNamePreference === "businessName";
    const taskChainHeader = await this.getTaskChainHeaderDetail(this.srcObjectId);
    const taskChainDisplayName = isBusinessNameEnabled ? taskChainHeader.businessName : taskChainHeader.objectId;
    this.updateBreadCrumb(taskChainDisplayName);
    (this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel).setProperty(
      "/displayName",
      taskChainDisplayName
    );
    (this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel).setProperty("/status", taskChainHeader.status);
    (this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel).setProperty(
      "/subStatus",
      taskChainHeader.subStatus
    );
    (this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel).setProperty(
      "/latestUpdate",
      taskChainHeader.startTime
    );
    this.view.getModel("detailsModel").setProperty("/latestLogId", taskChainHeader.logId);
    (this.view.getModel("settingsModel") as sap.ui.model.json.JSONModel).setProperty("/isExeModeVisible", false);
    (this.view.getModel("settingsModel") as sap.ui.model.json.JSONModel).setProperty("/partitionSection", false);
    const scheduleList = await scheduleDialog?.getTaskScheduleList(this.spaceId, ApplicationId.TASK_CHAINS);
    const schedule = scheduleList?.find((obj) => obj.objectId === this.srcObjectId);
    this.view.getModel("detailsModel").setProperty("/scheduleObject", schedule);

    if (schedule && schedule.activationStatus === "ENABLED") {
      const scheduletxt = this.getBundle().getText("SCHEDULED");
      (this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel).setProperty(
        "/refresh_frequency",
        scheduletxt
      );
      (this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel).setProperty(
        "/refresh_frequency_active",
        true
      );
      (this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel).setProperty(
        "/nextSchedule",
        schedule.nextRun
      );
      (this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel).setProperty("/scheduled", true);
    } else if (schedule && schedule.activationStatus === "DISABLED") {
      const pausedTxt = this.getBundle().getText("PAUSED");
      (this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel).setProperty("/refresh_frequency", pausedTxt);
      (this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel).setProperty(
        "/refresh_frequency_active",
        false
      );
      (this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel).setProperty("/nextSchedule", "");
      (this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel).setProperty("/scheduled", true);
    } else {
      const noneTxt = this.getBundle().getText("none");
      (this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel).setProperty("/refresh_frequency", noneTxt);
      (this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel).setProperty(
        "/refresh_frequency_active",
        false
      );
      (this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel).setProperty("/nextSchedule", "");
    }
    this.enableSchdeulingMenuOptions(schedule);
  }

  public async fetchDataFlowDetails(dataFlowName: string) {
    let sUrl = `/dataflow/status/${dataFlowName}?space=${this.spaceId}&&includeBusinessNames=true`;
    return ServiceCall.request<any>({
      url: sUrl,
      type: HttpMethod.GET,
      contentType: ContentType.APPLICATION_JSON,
    });
  }

  public async getDataflowDetails(objectId: string, selectedLogId: string, detailsData: any) {
    const oMetricesTable = this.view.byId("runsMetricsTable") as sap.ui.table.Table;
    oMetricesTable.setBusy(true);
    const src = objectId + "?space=" + this.spaceId;
    const { logId, handle, isHistory, isAutoRestarted, diStatus, status } = detailsData;
    if (objectId && (logId || handle)) {
      let sUrl =
        "/dataflow/status/" +
        src +
        `&isHistory=${isHistory}&isAutoRestarted=${isAutoRestarted}&diStatus=${diStatus || status}`;
      // if (this.spaceId) {
      //   sUrl += "?space=" + this.spaceId;
      // }
      if (selectedLogId) {
        sUrl += "&logId=" + selectedLogId;
      }
      if (handle) {
        sUrl += "&handleId=" + handle;
      }
      return ServiceCall.request<any>({
        url: sUrl,
        type: HttpMethod.GET,
        contentType: ContentType.APPLICATION_JSON,
      })
        .then(async (oResponse) => {
          (this.view.getModel("metricesModel") as sap.ui.model.json.JSONModel).setProperty("/", []);
          if (oResponse.data?.metrices) {
            this.getMetricesDetailsNew(oResponse.data.metrices);
          } else {
            oMetricesTable.setNoData(this.getBundle().getText("noMetricesHistoryTxt"));
          }
          // const selectedInstance = oResponse.data.instances.find((elem) => elem.logId === selectedLogId);
          // await this.getMetricesDetails(selectedInstance);
          oMetricesTable.setBusy(false);
        })
        .catch((error) => {
          // show error
          const errorMsg = this.getBundle().getText("metricesBackendErrorTxt");
          MessageHandler.exception({ exception: error, message: errorMsg });
          oMetricesTable.setBusy(false);
        });
    }
  }

  public async getMetricesDetailsNew(oMetricesData: any) {
    const oMetricesTable = this.view.byId("runsMetricsTable") as sap.ui.table.Table;
    (this.view.getModel("metricesModel") as sap.ui.model.json.JSONModel).setProperty("/", []);
    oMetricesTable.setBusyIndicatorDelay(0);
    if (oMetricesData) {
      let filteredMetricesData = [];
      if (oMetricesData.metrices) {
        filteredMetricesData = this.filterMetricesData(oMetricesData.metrices);
      }
      (this.view.getModel("metricesModel") as sap.ui.model.json.JSONModel).setProperty("/", filteredMetricesData);
    }
  }

  public async getMetricesDetails(oSelectedData: any) {
    const oMetricesTable = this.view.byId("runsMetricsTable") as sap.ui.table.Table;
    (this.view.getModel("metricesModel") as sap.ui.model.json.JSONModel).setProperty("/", []);
    oMetricesTable.setBusyIndicatorDelay(0);
    if (oSelectedData && !oSelectedData.isHistory && oSelectedData.handle) {
      const oMetricesData = await this.fetchMetrices(oSelectedData.handle);
      if (oMetricesData) {
        let filteredMetricesData = [];
        if (oMetricesData.metrices) {
          filteredMetricesData = this.filterMetricesData(oMetricesData.metrices);
        }
        (this.view.getModel("metricesModel") as sap.ui.model.json.JSONModel).setProperty("/", filteredMetricesData);
      }
    } else {
      oMetricesTable.setNoData(this.getBundle().getText("noMetricesHistoryTxt"));
    }
  }

  private fetchMetrices(handleId: string) {
    let sUrl = "/dataflow/status/" + handleId + "/metrices";
    //
    if (this.spaceId) {
      sUrl += "?space=" + this.spaceId;
      return ServiceCall.request<any>({
        url: sUrl,
        type: HttpMethod.GET,
        contentType: ContentType.APPLICATION_JSON,
      })
        .then(async (oResponse) => oResponse.data)
        .catch(() => {
          // show error
          const oMetricesTable = this.view.byId("runsMetricsTable") as sap.ui.table.Table;
          oMetricesTable.setNoData(this.getBundle().getText("metricesBackendErrorTxt"));
          return;
        });
    }
  }

  public filterMetricesData(metricsArr: any[]) {
    const returnArr = [];
    metricsArr.forEach((item) => {
      if (item.type === "Source" || item.type === "Target") {
        returnArr.push(item);
      }
    });
    return returnArr;
  }

  public async getObjectDetails(objectID: string, routeName: string): Promise<any> {
    let objectType = monitoringObjectType.REMOTETABLE;
    if (this.isViewMonitorRoute) {
      objectType = monitoringObjectType.VIEW;
    }
    if (this.isStatisticsRoute) {
      objectType = monitoringObjectType.REMOTETABLESTATISTICS;
    }
    const oDetails = await super.getObjectDetails(objectID, objectType, this.spaceId, this.displayName);
    if (oDetails && Object.keys(oDetails)?.length > 0) {
      this.processDetailsData(oDetails.data);
    }
  }

  private async processDetailsData(oDetails: any) {
    this.oDetails = oDetails;
    const oDetailsModel = this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel;
    oDetailsModel?.setProperty("/", oDetails);
    oDetailsModel?.setProperty("/routeName", this.routeName);
    const isSDPEnabled = sap.ui.getCore().getModel("featureflags").getProperty("/DWC_DUMMY_SPACE_PERMISSIONS");
    const showBusinessName = this.displayName === "businessName";

    if (this.isRemoteTableRoute || this.isViewMonitorRoute) {
      oDetails.inMemorySizeReplicaTableMB = [undefined, null].includes(oDetails.inMemorySizeReplicaTableMB)
        ? this.getText("NOT_APPLICABLE")
        : oDetails.inMemorySizeReplicaTableMB;
      const appId = this.isRemoteTableRoute ? ApplicationId.REMOTE_TABLES : ApplicationId.VIEWS;
      let scheduleDialog;
      if (this["isReusableTaskScheduleFFEnabled"] && this["isAdoptionOfTaskSchedulerEnabled"]) {
        await this.initScheduleDialog();
        scheduleDialog = this["newScheduleDialog"];
      } else {
        scheduleDialog = (
          this.getView().byId("taskSchedulingDialog") as sap.ui.core.mvc.View
        ).getController() as ITTaskScheduleController;
      }
      this.scheduleList = await scheduleDialog?.getTaskScheduleList(this.spaceId, appId);
      const selectedTableSchedule = this.scheduleList?.find((obj) => obj.objectId === this.srcObjectId);
      oDetailsModel.setProperty("/scheduleObject", selectedTableSchedule);
      oDetails.scheduled = false;
      if (selectedTableSchedule !== undefined) {
        oDetails.scheduled = true;
        oDetails.nextSchedule = selectedTableSchedule.nextRun;
      }
    }
    if (this.isViewMonitorRoute) {
      oDetailsModel?.setProperty("/objectName", oDetails.viewName ? oDetails.viewName : this.srcObjectId);
      oDetailsModel?.setProperty("/dataAccess", oDetails.dataPersistency);
      oDetailsModel?.setProperty("/partiallyPersisted", oDetails?.partiallyPersisted);
      oDetailsModel?.setProperty("/inputParametersDetails", oDetails?.inputParametersDetails);
      oDetailsModel?.setProperty("/connection_visibility", false);
      oDetailsModel?.setProperty("/showErrorStrip", false);
      oDetailsModel?.setProperty("/showRetry", false);
      oDetailsModel?.setProperty("/replicationStatus", oDetails.replicationState);
      oDetailsModel?.setProperty("/isUsedInChains", oDetails.usedInTaskChain);
      (this.view.getModel("settingsModel") as sap.ui.model.json.JSONModel).setProperty("/isExeModeVisible", true);
      this.settings = {
        jobExecSettingsModified: false,
      };
      oDetailsModel?.setProperty("/exeMode", oDetails.executionMode);
      this.settings.executionMode = oDetails.executionMode;
      this.settings.initialexecutionMode = oDetails.initialexecutionMode;
      this.settings.jobExecutionType = oDetails.jobExecutionType;
      if (oDetails.jobExecutionType === JobExecutionType.DEFAULT) {
        oDetailsModel?.setProperty("/jobExecType", 0);
      } else if (oDetails.jobExecutionType === JobExecutionType.SYNCHRONOUS) {
        oDetailsModel?.setProperty("/jobExecType", 1);
      } else if (oDetails.jobExecutionType === JobExecutionType.ASYNCHRONOUS) {
        oDetailsModel?.setProperty("/jobExecType", 2);
      }
      if (showBusinessName && oDetails.viewBusinessName && oDetails.viewBusinessName !== "") {
        oDetailsModel?.setProperty("/displayName", oDetails.viewBusinessName);
        this.updateBreadCrumb(oDetails.viewBusinessName);
      } else {
        oDetailsModel?.setProperty("/displayName", oDetails.viewName ? oDetails.viewName : this.srcObjectId);
      }
    } else {
      let showError = false;
      if (oDetails?.errorDetails?.exception_id >= 0) {
        showError = true;
        this.exceptionID = oDetails?.errorDetails?.exception_id;
      }
      oDetailsModel?.setProperty("/objectName", oDetails.tableName);

      const isRemoteConn = sap.ui.getCore().getModel("privilege").getProperty("/DWC_REMOTECONNECTION")?.read;
      oDetailsModel?.setProperty("/connection_link_visibility", isRemoteConn);

      // this.taskLogHeaderDetails.getModel("detailsModel").setProperty("/dataAccess", this.formatter.getDataAccessText(oDetails.dataAccess));
      oDetailsModel?.setProperty("/replicationState", oDetails.replicationState);
      oDetailsModel?.setProperty("/showErrorStrip", showError);
      oDetailsModel?.setProperty("/showRetry", showError);
      if (oDetails.location === "indexserver" || oDetails.location === "dataintelligence") {
        oDetails.isFVT = true;
      }
      if (showBusinessName && oDetails.businessName && oDetails.businessName !== "") {
        oDetailsModel?.setProperty("/displayName", oDetails.businessName);
        this.updateBreadCrumb(oDetails.businessName);
      } else {
        oDetailsModel?.setProperty("/displayName", oDetails.tableName);
      }
      if (showBusinessName && oDetails.businessNameConnection && oDetails.businessNameConnection !== "") {
        oDetailsModel?.setProperty("/displayConnectionName", oDetails.businessNameConnection);
      } else {
        oDetailsModel?.setProperty("/displayConnectionName", oDetails.connectionName);
      }
      if (showError) {
        this.remoteSource = oDetails.remoteSourceName;
        this.isFVT = oDetails.isFVT || false;
        this.location = oDetails.location || undefined;
        let msg = this.getBundle().getText("ERROR_MSG_REAL_TIME");
        if (!!this.isFVT && this?.location === "dataintelligence" && this.exceptionID.toString() === "0") {
          oDetailsModel?.setProperty("/showErrorStrip", showError);
          oDetailsModel?.setProperty("/showRetry", true);
          msg = this.getBundle().getText("ERROR_MSG_REAL_TIME");
        } else if (!!this.isFVT && this.exceptionID.toString() === "0") {
          oDetailsModel?.setProperty("/showErrorStrip", showError);
          oDetailsModel?.setProperty("/showRetry", false);
          msg = this.getBundle().getText("ERROR_MSG");
        }
        const errorMessage = oDetails?.errorDetails?.message;
        oDetailsModel?.setProperty("/errorMessage", errorMessage);
        oDetailsModel?.setProperty("/error", msg);
      }
      (this.view.getModel("settingsModel") as sap.ui.model.json.JSONModel).setProperty("/isExeModeVisible", false);
      oDetailsModel.setProperty("/statInfoVisible", false);
      if (this.isStatisticsRoute) {
        this.processStatDetails(oDetails);
      }
    }

    // check if partitionExists & update the new section if it does
    const updatePriv = this.view.getModel("privilegeModel").getProperty("/update");
    if (this.isRemoteTableRoute && (isSDPEnabled || (!isSDPEnabled && updatePriv)) && oDetails.partitioningSupported) {
      (this.view.getModel("settingsModel") as sap.ui.model.json.JSONModel).setProperty("/partitionSection", true);
    } else if (this.isViewMonitorRoute && (isSDPEnabled || (!isSDPEnabled && updatePriv))) {
      (this.view.getModel("settingsModel") as sap.ui.model.json.JSONModel).setProperty("/partitionSection", true);
    } else {
      (this.view.getModel("settingsModel") as sap.ui.model.json.JSONModel).setProperty("/partitionSection", false);
    }
    (this.view.getModel("settingsModel") as sap.ui.model.json.JSONModel).setProperty(
      "/partitioningExists",
      oDetails.partitioningExists
    );
    if (oDetails.partitioningExists) {
      await this.updatePartitionSection();
    } else {
      (this.view.getModel() as sap.ui.model.json.JSONModel).setData({});
      const objectPage = this.view.byId("masterLogTableOPL") as sap.uxap.ObjectPageLayout;
      const partitionSection = objectPage.getSections()[1].getSubSections()[0];
      partitionSection.getBlocks()[0]?.setVisible(true);
      partitionSection.getBlocks()[1]?.setVisible(false);
    }

    await this.enableMenuOptions(oDetails);
    oDetailsModel?.updateBindings(true);
  }

  private processStatDetails(oDetails) {
    const oDetailsModel = this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel;
    if (oDetails.dataAccess !== dataAccessType.REMOTE) {
      oDetailsModel.setProperty("/statInfoVisible", true);
    } else {
      oDetailsModel.setProperty("/statInfoVisible", false);
    }
    oDetails.statisticsLatestUpdate = oDetails.statisticsLatestUpdate
      ? this.formatDateTime(oDetails.statisticsLatestUpdate)
      : "---";
    oDetails.statisticsType = oDetails.statisticsType ? oDetails.statisticsType : "---";
  }

  private async updatePartitionSection() {
    const objectPage = this.view.byId("masterLogTableOPL") as sap.uxap.ObjectPageLayout;
    const partitionSection = objectPage.getSections()[1].getSubSections()[0];
    partitionSection.setBusy(true);
    this.view.setModel(new sap.ui.model.json.JSONModel({}), "infoModel");
    this.view.getModel("partitionSettingsModel").setProperty("/showPersistencyInfo", false);
    this.view.getModel("partitionSettingsModel").setProperty("/tableSelectMode", "None");
    this.view.getModel("partitionSettingsModel").setProperty("/isLoadDataVisible", false);
    this.view.getModel("infoModel").setProperty("/showrealtimeWarning", false);
    this.partitionDialog = this.getPartitionDialog();
    const appId = this.isRemoteTableRoute ? ApplicationId.REMOTE_TABLES : ApplicationId.VIEWS;
    const partitionData = (await this.partitionDialog.getPartitionDetails(
      this.srcObjectId,
      appId,
      this.spaceId
    )) as any;
    partitionData.isView = true;
    if (appId === ApplicationId.REMOTE_TABLES) {
      partitionData.isView = false;
      this.checkPartitionChangedInfo(partitionData);
      this.view.getModel("partitionSettingsModel").setProperty("/tableSelectMode", "None");
      this.view.getModel("partitionSettingsModel").setProperty("/isLoadDataVisible", false);
    } else if (appId === ApplicationId.VIEWS) {
      (this.view.getModel("partitionSettingsModel") as sap.ui.model.json.JSONModel).setProperty(
        "/showChangedInfo",
        false
      );
      this.view.getModel("partitionSettingsModel").setProperty("/tableSelectMode", "MultiSelect");
      this.view.getModel("partitionSettingsModel").setProperty("/isLoadDataVisible", true);
      this.view.getModel("partitionSettingsModel").setProperty("/isLoadDataEnabled", false);
      this.checkPartitionChangedInfo(partitionData);
    }
    (this.view.getModel() as sap.ui.model.json.JSONModel).setData(partitionData);
    (this.view.getModel("partitionSettingsModel") as sap.ui.model.json.JSONModel).setProperty("/isEditable", false);
    (this.view.getModel("partitionSettingsModel") as sap.ui.model.json.JSONModel).setProperty(
      "/isActionVisible",
      false
    );
    // Explicity set selected column in case of large number of columns
    (this.view.getModel("partitionSettingsModel") as sap.ui.model.json.JSONModel).setProperty(
      "/selectedColumn",
      partitionData.column
    );
    partitionSection.getBlocks()[0].setVisible(false);
    partitionSection.getBlocks()[1].setVisible(true);
    this.view.setBusy(false);
    partitionSection.setBusy(false);

    if (appId === ApplicationId.VIEWS) {
      const table = this.view.byId("shellMainContent---dataIntegrationComponent---taskLog--rangeList") as sap.m.Table;
      const obj = this;
      table?.["addDelegate"]({
        onAfterRendering: function () {
          obj.disableOthersRowSelection(table);
        },
      });
    }
    if (this.view.getModel("settingsModel").getProperty("/isSpaceLocked")) {
      this.view.getModel("partitionSettingsModel").setProperty("/isLoadDataVisible", false);
    }
  }

  disableOthersRowSelection(table) {
    const allItems = table.getItems();
    const lastItem = allItems[allItems.length - 1];
    const obj = lastItem.getBindingContext().getObject();
    const enabled = !obj.isOthers;
    const cb = lastItem.$().find(".sapMCb");
    const oCb = sap.ui.getCore().byId(cb.attr("id")) as sap.m.CheckBox;
    oCb.setEnabled(enabled);
    oCb.setSelected(false);
  }

  async onRangeSelectionChange() {
    const table = this.view.byId("shellMainContent---dataIntegrationComponent---taskLog--rangeList") as sap.m.Table;
    const isChanged = this.view.getModel("detailsModel").getProperty("/isChanged");
    const items = table.getSelectedItems();
    const otaskLogModelData = this.getView().getModel("logsModel").getData();
    this.disableOthersRowSelection(table);
    const isSpaceLocked = await this.isSpaceLocked(this.spaceId);
    const latestRunningTaskDetails = otaskLogModelData.find(
      (i) => i.status === "RUNNING" && i.activity === Activity.PERSIST
    );
    const persistency = (this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel).getProperty(
      "/dataPersistency"
    );
    if (isSpaceLocked) {
      this.view.getModel("partitionSettingsModel").setProperty("/isLoadDataVisible", false);
    }
    if (items?.length <= 0) {
      this.view.getModel("partitionSettingsModel").setProperty("/isLoadDataEnabled", false);
      this.rangeSelected = false;
    } else if (!isChanged && !latestRunningTaskDetails && persistency === "Persisted" && !isSpaceLocked) {
      this.view.getModel("partitionSettingsModel").setProperty("/isLoadDataEnabled", true);
    }
    if (items?.length > 0) {
      this.rangeSelected = true;
    }
  }

  async handleLoadData() {
    const rawData = [];
    const table = this.view.byId("shellMainContent---dataIntegrationComponent---taskLog--rangeList") as sap.m.Table;
    const items = table.getSelectedItems();
    items?.forEach((row) => {
      const rowData = row.getBindingContext().getObject();
      if (rowData.id !== "") {
        rawData.push(rowData.id);
      }
    });
    const sUrlStart = "tf/directexecute";
    const reqBody = {
      activity: Activity.PERSIST,
      applicationId: ApplicationId.VIEWS,
      objectId: encodeURIComponent(this.srcObjectId),
      spaceId: this.spaceId,
      parameters: {
        manualPartitions: rawData,
      },
    };
    const data = JSON.stringify(reqBody);
    const callType = HttpMethod.POST;

    this.view.setBusy(true);
    table.removeSelections(true);
    this.view.getModel("partitionSettingsModel").setProperty("/isLoadDataEnabled", false);
    try {
      await ServiceCall.request<any>({
        url: sUrlStart,
        type: callType,
        contentType: ContentType.APPLICATION_JSON,
        data: data,
      });
      sap.m.MessageToast.show(this.getText("loadSelectedPartitions", [this.srcObjectId]));
      this.onCompleteRefresh();
      this.view.setBusy(false);
    } catch (e) {
      this.view.setBusy(false);
      const msg = this.getText("loadSelectedPartitionsError", [this.srcObjectId]);
      MessageHandler.exception({ exception: e, message: msg });
      this.onCompleteRefresh();
    }
  }

  checkPartitionChangedInfo(partitionData) {
    const dataAccess = (this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel).getProperty("/dataAccess");
    if (partitionData.isView) {
      const persistency = (this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel).getProperty(
        "/dataPersistency"
      );
      if (partitionData && partitionData.isChanged && persistency === "Persisted") {
        (this.view.getModel("partitionSettingsModel") as sap.ui.model.json.JSONModel).setProperty(
          "/showChangedInfo",
          true
        );
        this.view
          .getModel("partitionSettingsModel")
          .setProperty("/partitionChangedText", this.getText("viewpartitionChangedInfoLocked"));
        this.view.getModel("partitionSettingsModel").setProperty("/isLoadDataEnabled", false);
        this.view.getModel("detailsModel").setProperty("/isChanged", true);
      }
    } else {
      if (dataAccess === dataAccessType.REMOTE) {
        // Never show changed banner if dataaccess is remote
        (this.view.getModel("partitionSettingsModel") as sap.ui.model.json.JSONModel).setProperty(
          "/showChangedInfo",
          false
        );
      } else if (dataAccess === dataAccessType.REALTIME_REPLICATION && partitionData.isFVT) {
        (this.view.getModel("partitionSettingsModel") as sap.ui.model.json.JSONModel).setProperty(
          "/showChangedInfo",
          false
        );
        this.view.getModel("infoModel").setProperty("/realtimeWarning", this.getText("REAL_TIME_WARNING"));
        this.view.getModel("infoModel").setProperty("/showrealtimeWarning", true);
      } else {
        if (partitionData && partitionData.isChanged) {
          (this.view.getModel("partitionSettingsModel") as sap.ui.model.json.JSONModel).setProperty(
            "/showChangedInfo",
            true
          );
          this.view
            .getModel("partitionSettingsModel")
            .setProperty("/partitionChangedText", this.getText("partitionChangedInfo"));
        } else {
          (this.view.getModel("partitionSettingsModel") as sap.ui.model.json.JSONModel).setProperty(
            "/showChangedInfo",
            false
          );
        }
      }
    }
  }

  private enableSchdeulingMenuOptions(schedule: any) {
    if (schedule) {
      (this.view.getModel("schedulingModel") as sap.ui.model.json.JSONModel).setProperty("/newSchedule", false);
      (this.view.getModel("schedulingModel") as sap.ui.model.json.JSONModel).setProperty("/editSchedule", true);
      (this.view.getModel("schedulingModel") as sap.ui.model.json.JSONModel).setProperty("/deleteSchedule", true);
    } else {
      (this.view.getModel("schedulingModel") as sap.ui.model.json.JSONModel).setProperty("/newSchedule", true);
      (this.view.getModel("schedulingModel") as sap.ui.model.json.JSONModel).setProperty("/editSchedule", false);
      (this.view.getModel("schedulingModel") as sap.ui.model.json.JSONModel).setProperty("/deleteSchedule", false);
    }
    this.view.setBusy(false);
  }

  private async enableMenuOptions(oDetails: any) {
    const appId = this.isRemoteTableRoute ? ApplicationId.REMOTE_TABLES : ApplicationId.VIEWS;
    const selectedTableSchedule = this.scheduleList?.find((obj) => obj.objectId === this.srcObjectId);

    const isScheduled = oDetails.scheduled;
    const repStatus = (this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel)?.getData().replicationStatus;

    if (
      appId !== ApplicationId.VIEWS &&
      selectedTableSchedule === undefined &&
      (repStatus === ReplicationStatus.INITIALIZING || repStatus === ReplicationStatus.DISCONNECTED)
    ) {
      (this.view.getModel("replicationModel") as sap.ui.model.json.JSONModel).setProperty("/newSchedule", false);
      (this.view.getModel("replicationModel") as sap.ui.model.json.JSONModel).setProperty("/editSchedule", false);
      (this.view.getModel("replicationModel") as sap.ui.model.json.JSONModel).setProperty("/deleteSchedule", false);
    } else if (selectedTableSchedule !== undefined && isScheduled) {
      (this.view.getModel("schedulingModel") as sap.ui.model.json.JSONModel).setProperty("/newSchedule", false);
      (this.view.getModel("schedulingModel") as sap.ui.model.json.JSONModel).setProperty("/editSchedule", true);
      (this.view.getModel("schedulingModel") as sap.ui.model.json.JSONModel).setProperty("/deleteSchedule", true);
    } else {
      (this.view.getModel("schedulingModel") as sap.ui.model.json.JSONModel).setProperty("/newSchedule", true);
      (this.view.getModel("schedulingModel") as sap.ui.model.json.JSONModel).setProperty("/editSchedule", false);
      (this.view.getModel("schedulingModel") as sap.ui.model.json.JSONModel).setProperty("/deleteSchedule", false);
    }

    const oDetailsModel = this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel;
    if (oDetails.scheduled && selectedTableSchedule.activationStatus === "ENABLED") {
      oDetailsModel?.setProperty("/refresh_frequency", "Scheduled");
      oDetailsModel?.setProperty("/refresh_frequency_active", true);
    } else if (oDetails.scheduled && selectedTableSchedule.activationStatus === "DISABLED") {
      const pausedTxt = this.getBundle().getText("PAUSED");
      oDetailsModel?.setProperty("/refresh_frequency", pausedTxt);
      oDetailsModel?.setProperty("/refresh_frequency_active", false);
    } else {
      const ref = this.getTextRefreshFrequency(
        oDetails.replicationStatus,
        oDetails.replicationType,
        oDetails.scheduled
      );
      oDetailsModel?.setProperty("/refresh_frequency", ref);
    }

    if (this.isRemoteTableRoute) {
      this.serviceUtil.blueGreenMenuBtnStates(oDetails, 0, selectedTableSchedule, isScheduled, this.view);
    }

    if (this.isViewMonitorRoute) {
      (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
        "/loadNewSnapShot",
        true
      );
      switch (oDetails.replicationState) {
        case replicationState.Initialize:
        case replicationState.None:
          (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
            "/removePersistencyData",
            false
          );
          break;
        case replicationState.Available:
        case replicationState.Error:
          if (oDetails.dataPersistency === "Persisted") {
            (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
              "/removePersistencyData",
              true
            );
          } else {
            (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
              "/removePersistencyData",
              false
            );
          }
          break;
      }
    }
    this.view.setBusy(false);
  }

  public actionForOverwriteDefault(oEvent) {
    this.settings.jobExecSettingsModified = true;
    const oDetailsModel = this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel;
    const bSelected = oEvent.getParameters().selected;
    if (bSelected) {
      oDetailsModel?.setProperty("/overwriteDefaultSelected", true);
      oDetailsModel?.setProperty("/jobExecTypeEnabled", true);
      this.settings.jobExecutionType = JobExecutionType.SYNCHRONOUS;
    } else {
      oDetailsModel?.setProperty("/overwriteDefaultSelected", false);
      oDetailsModel?.setProperty("/jobExecTypeEnabled", false);
      oDetailsModel?.setProperty("/jobExecType", 0);
      this.settings.jobExecutionType = JobExecutionType.DEFAULT;
    }
    this.onSaveSettings();
  }

  public onJobExecTypeSelect(oEvent) {
    this.settings.jobExecSettingsModified = true;
    const selectedIndex = oEvent.getParameters().selectedIndex;
    switch (selectedIndex) {
      case 0:
        this.settings.jobExecutionType = JobExecutionType.DEFAULT;
        break;
      case 1:
        this.settings.jobExecutionType = JobExecutionType.SYNCHRONOUS;
        break;
      case 2:
        this.settings.jobExecutionType = JobExecutionType.ASYNCHRONOUS;
    }
    this.onSaveSettings();
  }

  public onModeChange(oEvent) {
    const selectedIndex = oEvent.getParameters().selectedIndex;
    this.settings.executionMode = selectedIndex;
    this.onSaveSettings();
  }

  public async onSaveSettings() {
    this.view.setBusy(true);
    const data: any = { executionMode: this.settings.executionMode };
    const url = "monitor/" + this.spaceId + "/persistedViewsSettings/" + encodeURIComponent(this.srcObjectId);
    try {
      await ServiceCall.request<[]>({
        url: url,
        type: HttpMethod.PUT,
        contentType: ContentType.APPLICATION_JSON,
        data: JSON.stringify(data),
      });
      let msg = this.getText("saveSettingsSuccess");
      if (this.settings.jobExecSettingsModified) {
        msg = this.getText("jobExecSettingSuccessful");
      }
      sap.m.MessageToast.show(msg);
      this.onCompleteRefresh();
    } catch (oResponse) {
      this.view.setBusy(false);
      let msg = this.getText("saveSettingsFailed");
      if (this.settings.jobExecSettingsModified) {
        msg = this.getText("jobExecSettingFailed");
      }
      MessageHandler.exception({ exception: oResponse, message: msg });
      this.onCompleteRefresh();
      return [];
    }
  }

  public async onLoadNewPersistence() {
    this.view.setBusy(true);
    // record action for usage tracking
    ShellContainer.get().getUsageCollectionService().recordAction({
      action: "startPersistence",
      feature: "viewMonitor",
      eventtype: "click",
    });

    if (this.rangeSelected) {
      sap.m.MessageBox.show(this.getText("LoadNewPersistenceConfirm"), {
        id: "confirmLoadPersistency",
        icon: sap.m.MessageBox.Icon.QUESTION,
        title: this.getText("loadNewSnapShotLabel"),
        actions: [this.getText("Continue"), sap.m.MessageBox.Action.CLOSE],
        onClose: (action: sap.m.MessageBox.Action) => {
          if (action === this.getText("Continue")) {
            this.loadSnapshotForView();
          } else {
            this.view.setBusy(false);
          }
        },
      });
    } else {
      this.loadSnapshotForView();
    }
  }

  private loadSnapshotForView() {
    const i18n_view = this.formatter.getViewMsgBundle();
    fetchViewPersistenceService(this.spaceId, SERVICENAMES.startPersistence, this.srcObjectId)
      .then((oResponse) => {
        this.view.setBusy(false);
        // start was successful, so that the view can be refreshed
        MessageHandler.success(i18n_view.getText("startPersistenceSuccess", [this.srcObjectId]));
        this.onCompleteRefresh();
      })
      .catch((oResponse) => {
        this.view.setBusy(false);
        handleViewPersistenceResponse(oResponse, this.srcObjectId, this.spaceId);
        this.onCompleteRefresh();
      });
  }

  public async onStopPersistence(): Promise<void> {
    const i18n_view = this.formatter.getViewMsgBundle();
    sap.m.MessageBox.warning(i18n_view.getText("RemovePersistency_Confirm_Msg", [this.srcObjectId]), {
      styleClass: "sapUiSizeCompact",
      actions: [sap.m.MessageBox.Action.OK, sap.m.MessageBox.Action.CANCEL],
      initialFocus: sap.m.MessageBox.Action.CANCEL,
      onClose: (action: sap.m.MessageBox.Action) => {
        if (action === sap.m.MessageBox.Action.OK) {
          this.stopPersistence();
        }
      },
    });
  }

  public async stopPersistence(): Promise<any> {
    this.view.setBusy(true);
    (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
      "/removePersistencyData",
      false
    );
    // record action for usage tracking
    ShellContainer.get().getUsageCollectionService().recordAction({
      action: "stopPersistence",
      feature: "viewMonitor",
      eventtype: "click",
    });
    const i18n_view = this.formatter.getViewMsgBundle();
    fetchViewPersistenceService(this.spaceId, SERVICENAMES.stopPersistence, this.srcObjectId)
      .then((oResponse) => {
        this.view.setBusy(false);
        MessageHandler.success(i18n_view.getText("stopPersistenceSuccess", [this.srcObjectId]));
        (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
          "/loadNewSnapShot",
          false
        );
        (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
          "/removePersistencyData",
          false
        );
        this.onCompleteRefresh();
      })
      .catch((oResponse) => {
        this.view.setBusy(false);
        (this.view.getModel("persistencyActionModel") as sap.ui.model.json.JSONModel).setProperty(
          "/removePersistencyData",
          true
        );
        if (oResponse && oResponse.responseJSON && oResponse.responseJSON.details) {
          MessageHandler.error(oResponse.responseJSON.details.message);
        } else {
          MessageHandler.error(i18n_view.getText("STOP_PERSISTENCE_ERROR", [this.srcObjectId]));
        }
        this.onCompleteRefresh();
        return false;
      });
  }

  public async loadNewSnapshot() {
    this.view.setBusy(true);
    const displayName = this.view.getModel("detailsModel").getData().displayName;
    const remoteTable = this.view.getModel("detailsModel").getData();
    await this.serviceUtil.loadNewSnapshot(this.spaceId, this.srcObjectId, this.view, displayName, remoteTable);
    await this.onCompleteRefresh();
    this.view.setBusy(false);
  }

  public async removeReplicatedData() {
    const displayName = this.view.getModel("detailsModel").getData().displayName;
    const remoteTable = this.view.getModel("detailsModel").getData();
    await this.serviceUtil.removeReplicatedData(this.spaceId, this.srcObjectId, this.view, displayName, remoteTable);
    await this.onCompleteRefresh();
  }

  public async enableRealTimeReplication() {
    const displayName = this.view.getModel("detailsModel").getData().displayName;
    const dataAccess = this.view.getModel("detailsModel").getData().dataAccess;
    const usedInTaskChain = this.view.getModel("detailsModel").getData().usedInTaskChain;
    const srcObject = this.view.getModel("detailsModel").getData();
    await this.serviceUtil.enableRealTimeReplication(
      this.spaceId,
      srcObject,
      this.view,
      displayName,
      dataAccess,
      usedInTaskChain
    );
    await this.onCompleteRefresh();
  }

  public async disableRealTimeReplication() {
    const displayName = this.view.getModel("detailsModel").getData().displayName;
    const remoteTable = this.getView().getModel("detailsModel").getData();
    const isFvt = remoteTable.location === "indexserver";
    const warnSubscription = await RemoteTableServiceUtil.warnBeforeDropSubscription(remoteTable, this.view);
    await this.serviceUtil.disableRealTimeReplication(
      this.spaceId,
      remoteTable,
      this.view,
      displayName,
      isFvt,
      warnSubscription
    );
    await this.onCompleteRefresh();
  }

  public async initScheduleDialog() {
    if (!this["newScheduleDialog"]) {
      const customURLParams = {
        SCHEDULE: "/tf/:spaceid/schedules",
        SCHEDULE_BY_ID: "/tf/:spaceid/schedules/:scheduleid",
        OWNER_CHANGE: "/tf/:spaceid/schedules/:scheduleid/ownerchange",
      };
      this["newScheduleDialog"] = await getTaskScheduer("newTaskSchedule_TaskLog", customURLParams);
    }
  }

  public async onCreateScheduleReplication(): Promise<void> {
    const applicationId = this.getApplicationId();
    const activity = this.getActivity();
    const desc = this.getDesc();
    const dataAccess = this.getView().getModel("detailsModel").getProperty("/dataAccess");
    const data: ITaskScheduleRequest = {
      objectId: this.srcObjectId,
      applicationId: applicationId,
      activity: activity,
      description: desc,
      activationStatus: "ENABLED",
      dataAccess: dataAccess,
    };
    let scheduleDialog;
    if (
      sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_REUSABLE_TASK_SCHEDULING_UI") &&
      sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_REUSABLE_TASK_SCHEDULER_ADOPTION")
    ) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
      recordAction(`createTaskSchedule: ${data.applicationId}`, "taskSchedule", "onCreate");
    } else if (
      this.isRemoteTableRoute &&
      sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_REUSABLE_TASK_SCHEDULING_UI")
    ) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
      recordAction(`createTaskSchedule: ${data.applicationId}`, "taskSchedule", "onCreate");
    } else {
      scheduleDialog = (
        this.getView().byId("taskSchedulingDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    const oRessourceBundle = this.getTaskBundle();
    this.view.setBusy(true);
    scheduleDialog.createTaskSchedule(
      data,
      this.spaceId,
      applicationId,
      () => {
        if (
          sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_REUSABLE_TASK_SCHEDULING_UI") &&
          sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_REUSABLE_TASK_SCHEDULER_ADOPTION")
        ) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
        } else if (
          this.isRemoteTableRoute &&
          sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_REUSABLE_TASK_SCHEDULING_UI")
        ) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
        }
        this.onCompleteRefresh().then(() => {
          const msg = oRessourceBundle.getText("createScheduleSuccess");
          sap.m.MessageToast.show(msg);
          this.view.setBusy(false);
        });
      },
      (err) => {
        if (
          sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_REUSABLE_TASK_SCHEDULING_UI") &&
          sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_REUSABLE_TASK_SCHEDULER_ADOPTION")
        ) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
          MessageHandler.exception({
            exception: err.error,
            message: err.message,
          });
        } else if (
          this.isRemoteTableRoute &&
          sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_REUSABLE_TASK_SCHEDULING_UI")
        ) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
          MessageHandler.exception({
            exception: err.error,
            message: err.message,
          });
        }
        this.view.setBusy(false);
      },
      () => {
        this.view.setBusy(false);
      }
    );
  }

  async onChangeScheduleReplication(): Promise<void> {
    const applicationId = this.getApplicationId();
    const activity = this.getActivity();
    const desc = this.getDesc();
    const dataAccess = this.getView().getModel("detailsModel").getProperty("/dataAccess");
    const data: ITaskScheduleRequest = {
      objectId: this.srcObjectId,
      applicationId: applicationId,
      activity: activity,
      description: desc,
      activationStatus: "ENABLED",
      dataAccess: dataAccess,
    };
    let scheduleDialog;
    if (
      sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_REUSABLE_TASK_SCHEDULING_UI") &&
      sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_REUSABLE_TASK_SCHEDULER_ADOPTION")
    ) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
      recordAction(`editTaskSchedule: ${data.applicationId}`, "taskSchedule", "onEdit");
    } else if (
      this.isRemoteTableRoute &&
      sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_REUSABLE_TASK_SCHEDULING_UI")
    ) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
      recordAction(`editTaskSchedule: ${data.applicationId}`, "taskSchedule", "onEdit");
    } else {
      scheduleDialog = (
        this.getView().byId("taskSchedulingDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    const oRessourceBundle = this.getTaskBundle();
    this.view.setBusy(true);
    scheduleDialog.changeTaskSchedule(
      data,
      this.spaceId,
      applicationId,
      () => {
        if (
          sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_REUSABLE_TASK_SCHEDULING_UI") &&
          sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_REUSABLE_TASK_SCHEDULER_ADOPTION")
        ) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
        } else if (
          this.isRemoteTableRoute &&
          sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_REUSABLE_TASK_SCHEDULING_UI")
        ) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "edit");
        }
        this.onCompleteRefresh().then(() => {
          const msg = oRessourceBundle.getText("updateScheduleSuccess");
          sap.m.MessageToast.show(msg);
          this.view.setBusy(false);
        });
      },
      (err) => {
        if (
          sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_REUSABLE_TASK_SCHEDULING_UI") &&
          sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_REUSABLE_TASK_SCHEDULER_ADOPTION")
        ) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
          MessageHandler.exception({
            exception: err.error,
            message: err.message,
          });
        } else if (
          this.isRemoteTableRoute &&
          sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_REUSABLE_TASK_SCHEDULING_UI")
        ) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "edit");
          MessageHandler.exception({
            exception: err.error,
            message: err.message,
          });
        }
        this.view.setBusy(false);
      },
      () => {
        this.view.setBusy(false);
      }
    );
  }

  async onDeleteReplication(): Promise<void> {
    const applicationId = this.getApplicationId();
    const activity = this.getActivity();
    const desc = this.getDesc();
    const data: ITaskScheduleRequest = {
      objectId: this.srcObjectId,
      applicationId: applicationId,
      activity: activity,
      description: desc,
      activationStatus: "ENABLED",
    };
    let scheduleDialog;
    if (
      sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_REUSABLE_TASK_SCHEDULING_UI") &&
      sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_REUSABLE_TASK_SCHEDULER_ADOPTION")
    ) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
    } else if (
      this.isRemoteTableRoute &&
      sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_REUSABLE_TASK_SCHEDULING_UI")
    ) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
    } else {
      scheduleDialog = (
        this.getView().byId("taskSchedulingDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    const oResourceBundle = this.getTaskBundle();
    this.view.setBusy(true);
    scheduleDialog.deleteSchedule(
      data,
      this.spaceId,
      applicationId,
      () => {
        this.onCompleteRefresh().then(() => {
          const msg = oResourceBundle.getText("deleteScheduleSuccess");
          sap.m.MessageToast.show(msg);
          this.view.setBusy(false);
        });
      },
      (err) => {
        if (
          sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_REUSABLE_TASK_SCHEDULING_UI") &&
          sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_REUSABLE_TASK_SCHEDULER_ADOPTION")
        ) {
          MessageHandler.exception({
            exception: err.error,
            message: err.message,
          });
        } else if (
          this.isRemoteTableRoute &&
          sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_REUSABLE_TASK_SCHEDULING_UI")
        ) {
          MessageHandler.exception({
            exception: err.error,
            message: err.message,
          });
        }
        this.view.setBusy(false);
      },
      () => {
        this.view.setBusy(false);
      }
    );
  }

  getApplicationId() {
    return this.isRemoteTableRoute
      ? ApplicationId.REMOTE_TABLES
      : this.isViewMonitorRoute
      ? ApplicationId.VIEWS
      : ApplicationId.TASK_CHAINS;
  }

  getActivity() {
    return this.isRemoteTableRoute
      ? Activity.REPLICATE
      : this.isViewMonitorRoute
      ? Activity.PERSIST
      : Activity.RUN_CHAIN;
  }

  getDesc() {
    return this.isRemoteTableRoute
      ? "Remote Table Monitoring"
      : this.isViewMonitorRoute
      ? "View Persistency Scheduling"
      : "Task Chain Scheduling";
  }

  public statusTextStateFormatter = logFormatterUtils.statusTextStateFormatter;
  public statusTextFormatter = logFormatterUtils.statusTextFormatter;
  public runtimeFormatter = logFormatterUtils.runtimeFormatter;
  public activityTextFormatter = logFormatterUtils.activityTextFormatter;
  public severityStateFormatter = logFormatterUtils.severityStateFormatter;
  public severityTextFormatter = logFormatterUtils.severityTextFormatter;
  public ObjectTypeFormatter = logFormatterUtils.ObjectTypeFormatter;

  public hanaStateFormatter = formatterJS.hanaStateFormatter;
  public formatExecutionType(status: any, scheduleId: any, activity: any, isSubTask: any) {
    if (status && scheduleId) {
      if (isDiMonitorImprovementsEnabled()) {
        return this.getBundle().getText("DIRECTNew");
      } else {
        return this.getBundle().getText("SCHEDULED");
      }
    } else if (isSubTask && activity === Activity.GENERATE_STOP_CHAIN) {
      return this.getBundle().getText("SUB_TASK");
    } else if (status) {
      if (isDiMonitorImprovementsEnabled()) {
        return this.getBundle().getText("DIRECTNew");
      } else {
        return this.getBundle().getText("DIRECT");
      }
    }
    return "";
  }

  public formatParentChainName(parent: any) {
    return parent && parent.objectId ? parent.objectId : "";
  }

  /**
   * fetch messages
   *
   * @memberof TaskLogClass
   */
  // TODO: This downloads a full log and uses only the messages. I guess that messages are already available from getTaskLogDetails
  // The code would be much simpler if there would be only one model that contains both, log and messages
  protected async fetchMessages(taskLogId: string, space?: string, withMetrics: boolean = false): Promise<any> {
    const spaceId = space !== undefined ? space : this.spaceId;
    let sUrl = "tf/" + spaceId + "/logs?taskLogId=" + taskLogId;
    if (withMetrics) {
      sUrl = `tf/${spaceId}/extendedlogs/${taskLogId}`;
    }
    if (this.isECNRoute()) {
      sUrl = "ecns/runtime/logs/" + taskLogId;
    }
    try {
      const results = await ServiceCall.request<ITaskLogIncomplete[]>({
        url: sUrl,
        type: HttpMethod.GET,
        contentType: ContentType.APPLICATION_JSON,
      });

      return withMetrics ? results?.data : results?.data[0];
    } catch (e) {
      return [];
    }
  }

  private async fetchJsonContent(taskLogId: string, messageNumber: number): Promise<any> {
    const sUrl =
      // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
      "tf/" + this.spaceId + "/logs/" + taskLogId + "/messages/" + messageNumber + "/jsondata";
    try {
      const results = await ServiceCall.request<ITaskLogIncomplete[]>({
        url: sUrl,
        type: HttpMethod.GET,
        contentType: ContentType.APPLICATION_JSON,
      });
      return results;
    } catch (e) {
      return {};
    }
  }

  private async fetchTaskChainMessages(taskLogId: string): Promise<any> {
    const user = User.getInstance();
    const displayNamePreference = user.getObjectNameDisplay();
    const isBusinessNameEnabled = displayNamePreference === "businessName";
    let sUrl = `tf/${this.spaceId}/taskchains/${taskLogId}${isBusinessNameEnabled ? "?includeBusinessNames=true" : ""}`;
    if (this.routeName === "ecntaskchainmonitoring") {
      sUrl = "ecns/taskchains/logs/" + taskLogId;
    }
    return ServiceCall.request<ITaskLogIncomplete[]>({
      url: sUrl,
      type: HttpMethod.GET,
      contentType: ContentType.APPLICATION_JSON,
    }).catch(() => []);
  }

  public async translateMessages(messages: IMessage[]) {
    const dropSubscriptionKeys = [
      "DROP_SUBSCRIPTION_WARNING",
      "START_BATCH_REPLICATION_BLOCKED",
      "STOP_REPLICATION_BLOCKED",
    ];
    for (const m of messages) {
      if (m && m.messageBundleKey) {
        // if (m.messageBundleId === "sap.dwc.dataflowTaskLogs") {
        // Pending: need to translate messages from DF but keys are not unique in DF.
        // m.text = m.text;
        // m.text = this.getBundle().getText(`${m.messageBundleKey}_${m.messageNumber}`, m.parameterValues);
        if (
          this.isViewMonitorRoute &&
          m?.isJsonDataAvailable &&
          m.messageBundleKey === "START_VIEW_PERSISTENCY_RECORD_COUNT_PARTITIONS_SUCCESS"
        ) {
          m.details = "ViewDetailsWithInstructions";
        }
        if (this.isECNRoute() || m.messageBundleId === "sap.dwc.localTable") {
          // fix to get translated text from tasklog comp
          m.text = this.getText(m.messageBundleKey, m.parameterValues);
        } else if (
          m.messageBundleId !== "sap.dwc.dataflowTaskLogs" &&
          m.messageBundleId !== "sap.dis.replicationflowTaskLogs" &&
          m.messageBundleId !== "sap.dwc.procedureTaskLogs"
        ) {
          if (m.messageBundleKey === "taskFinishedAt") {
            const date = new Date(m.timestamp);
            let formattedDate = Format.toLocalDateTime(date);
            m.parameterValues[2] = formattedDate;
          }
          m.text = await getResourceText(
            MAP_MESSAGEBUNDLE_IDS[m.messageBundleId] || m.messageBundleId,
            m.messageBundleKey,
            m.parameterValues
          );
        }
        if (m.messageBundleId === "sap.dwc.remoteTable") {
          if (dropSubscriptionKeys.includes(m.messageBundleKey)) {
            m.details = "SHOW_DROPSUBSCRIPTION";
          }
        }
      }
    }
  }

  private getNavLinkUrl(appId, objectId, logId, space?: string) {
    const taskLogMonitoringRouteMap = {
      [ApplicationId.DATA_FLOWS]: "dataFlowMonitor",
      [ApplicationId.REMOTE_TABLES]: "remoteTableMonitor",
      [ApplicationId.VIEWS]: "viewMonitor",
      [ApplicationId.TASK_CHAINS]: "taskChainMonitor",
      [ApplicationId.TRANSFORMATION_FLOWS]: "transformationFlowMonitorDetails",
      [ApplicationId.REPLICATION_FLOWS]: "replicationFlowMonitorDetails",
    };
    const spaceId = space !== undefined ? space : this.spaceId;
    if (
      appId === ApplicationId.SQL_SCRIPT_PROCEDURE ||
      appId === ApplicationId.BW_PROCESS_CHAIN ||
      appId === ApplicationId.API ||
      appId === ApplicationId.NOTIFICATION
    ) {
      return `#/dataintegration&/di/logdetails/${spaceId}/${appId}/${objectId}/${logId}`;
    } else {
      return `#/dataintegration&/di/${spaceId}/${taskLogMonitoringRouteMap[appId]}/${encodeURIComponent(
        objectId
      )}/${logId}`;
    }
  }

  private async getTaskChainTreeModel(data: any) {
    const children = data.children;
    const oBundle = this.getBundle();
    const user = User.getInstance();
    const displayNamePreference = user.getObjectNameDisplay();
    let isBusinessNameEnabled = displayNamePreference === "businessName";
    if (this.isECNRoute()) {
      isBusinessNameEnabled = false;
    }

    const showParentErrorIconFlag =
      data.status === TaskStatus.FAILED &&
      (isUndefined(children) || children.length === 0) &&
      data.activity !== "CANCEL";
    const errorMsg = await getResourceText(MAP_MESSAGEBUNDLE_IDS["sap.dwc.taskFramework"], "taskChainNoChildErrorText");
    const taskChainRoot = {
      nodes: [
        {
          objectId: data.objectId,
          objectName: isBusinessNameEnabled ? data.businessName : data.objectId,
          status: data.status,
          applicationId: data.applicationId,
          logId: data.logId,
          nodes: [],
          icon: taskLogIconMap[ApplicationId.TASK_CHAINS],
          showErrorIcon: showParentErrorIconFlag,
          errorIconText: showParentErrorIconFlag ? errorMsg : "",
          showNavLink: false,
          activity: data.activity,
          runTime: data.runTime,
          crossSpaceName: data.spaceId,
        },
      ],
    };
    children?.forEach((element) => {
      const retryHistory = element.retryHistory;
      if (retryHistory.length === 0) {
        element.icon = taskLogIconMap[element.applicationId];
        element.showErrorIcon = false;
        element.showNavLink = this.canShowNavLink(element);
        element.navLinkUrl = this.getNavLinkUrl(
          element.applicationId,
          element.objectId,
          element.logId,
          element.spaceId
        );
        element.objectName = isBusinessNameEnabled ? element.businessName : element.objectId;
        element.crossSpaceName = element.spaceId;
        taskChainRoot.nodes[0].nodes.push(element);
      } else {
        const totalChildren = retryHistory.length + 1;
        const childRootElement = {
          objectId: `${element.objectId}`,
          objectName: `${isBusinessNameEnabled ? element.businessName : element.objectId}(${totalChildren})`,
          status: element.status,
          applicationId: element.applicationId,
          icon: taskLogIconMap[element.applicationId],
          showErrorIcon: false,
          nodes: [],
          showNavLink: false,
          activity: data.activity,
        };
        const firstElementName =
          element.logId != null
            ? oBundle.getText("taskChainRetryChildObject", element.logId)
            : oBundle.getText("taskChainRetryNewChildObject");
        const firstElement = {
          objectName: `${isBusinessNameEnabled ? element.businessName : element.objectId}(${firstElementName})`,
          objectId: `${element.objectId}`,
          subStatus: element.subStatus,
          runTime: element.runTime,
          status: element.status,
          applicationId: element.applicationId,
          logId: element.logId,
          icon: taskLogIconMap[element.applicationId],
          showErrorIcon: false,
          showNavLink: this.canShowNavLink(element),
          navLinkUrl: this.getNavLinkUrl(element.applicationId, element.objectId, element.logId, element.spaceId),
          activity: data.activity,
          crossSpaceName: element.spaceId,
        };
        element.retryHistory.forEach((obj) => {
          obj.objectId = element.objectId;
          obj.objectName = `${isBusinessNameEnabled ? element.businessName : element.objectId}(Task ${obj.logId})`;
          obj.applicationId = element.applicationId;
          obj.icon = taskLogIconMap[element.applicationId];
          obj.showErrorIcon = false;
          obj.showNavLink = this.canShowNavLink(element);
          obj.navLinkUrl = this.getNavLinkUrl(element.applicationId, element.objectId, element.logId, element.spaceId);
          obj.crossSpaceName = element.spaceId;
        });
        childRootElement.nodes = [firstElement, ...element.retryHistory];
        taskChainRoot.nodes[0].nodes.push(childRootElement);
      }
    });
    return taskChainRoot;
  }

  private canShowNavLink(element: any) {
    if (
      element?.applicationId === ApplicationId.INTELLIGENT_LOOKUP ||
      element?.applicationId === ApplicationId.LOCAL_TABLE
    ) {
      return false;
    }

    return true;
  }

  private async processmetricsData(jsonData: any) {
    const metricsArray = [];
    const uniquePartitions: Record<string, any> = {};
    let hasPartitionMetrics = false;
    const partitionColumn = jsonData.metrics.find((metric: any) => metric.name === "PARTITIONING_COLUMN")?.value;
    let noofPartitions = 0;
    let showPartitionRange = false;

    jsonData.metrics.forEach((metric: any) => {
      const partitionLabel = metric.labels.find((label: any) => label.name === "PARTITION");

      if (!partitionLabel) {
        // exclude job execution metrics
        // Handle generic metrics
        let key;
        if (metric.name === "DAC") {
          key = "DAC";
          if (metric.value === "true") {
            metric.value = this.getText("YES");
          } else {
            metric.value = this.getText("NO");
          }
        } else {
          key = metric.name + (metric.labels[0] && metric.labels[0]?.name ? `_${metric.labels[0].name}` : "");
        }
        if (key === "JOB_EXECUTION" || key === "RUNTIME_MS_STEP" || (key !== "ECN_ID" && !metric.value)) {
          return;
        }
        if (key === "NUMBER_OF_PARTITIONS") {
          noofPartitions = metric.value;
        }
        if (key === "MEMORY_CONSUMPTION_GIB_OVERALL" || key === "MEMORY_CONSUMPTION_GIB") {
          metric.value = Number(metric.value * 1024) + " MiB";
        }
        if (key === "RUNTIME_MS_REMOTE_EXECUTION_TIME") {
          metric.value = metric.value + " s";
        }
        if (key === "ECN_ID") {
          if (!metric.value) {
            metric.value = "";
          }
        }

        metricsArray.push({
          name: this.getText(key),
          value: metrics_value[key] ? this.getText(metrics_value[key][metric.value]) : metric.value,
        });
      } else {
        // Handle partition metrics
        hasPartitionMetrics = true;
        const partitionNumber = partitionLabel.value;

        if (!uniquePartitions[partitionNumber]) {
          uniquePartitions[partitionNumber] = {
            NAME: `${this.getText("partitionLabel")} ${partitionNumber}`,
            PARTITION_LOW_VALUE: "",
            PARTITION_HIGH_VALUE: "",
            NUMBER_OF_RECORDS: "",
            // Initialize other properties as needed
          };
        }

        metric.labels.forEach((label: any) => {
          if (label.name === "PARTITION_LOW_VALUE" || label.name === "PARTITION_HIGH_VALUE") {
            uniquePartitions[partitionNumber][label.name] = label.value;
          }
        });

        // Specific metric properties
        if (metric.name === "NUMBER_OF_RECORDS") {
          uniquePartitions[partitionNumber].NUMBER_OF_RECORDS = metric.value;
        } else if (metric.name === "MEMORY_CONSUMPTION_GIB" || metric.name === "MEMORY_CONSUMPTION_GIB_OVERALL") {
          uniquePartitions[partitionNumber].MEMORY_CONSUMPTION_GIB = Number(metric.value * 1024) + " MiB";
        }

        // Derive partition range if applicable
        const { PARTITION_LOW_VALUE, PARTITION_HIGH_VALUE } = uniquePartitions[partitionNumber];
        if (PARTITION_LOW_VALUE && PARTITION_HIGH_VALUE) {
          uniquePartitions[
            partitionNumber
          ].PARTITION_RANGE = `${PARTITION_LOW_VALUE} <= ${partitionColumn} < ${PARTITION_HIGH_VALUE}`;
          showPartitionRange = true;
        }
      }
    });

    //Post-prcessing for locked partitions
    // Add missing locked partition entry
    for (let i = 1; i <= noofPartitions - 1; i++) {
      let key = i.toString(); // Convert number to string, as JSON keys are strings
      if (!uniquePartitions.hasOwnProperty(key)) {
        uniquePartitions[key] = {
          NAME: `${this.getText("partitionLabel")} ${key}`,
          PARTITION_RANGE: "N/A (Locked)",
          NUMBER_OF_RECORDS: "",
          MEMORY_CONSUMPTION_GIB: "",
        };
      }
    }

    // Post-processing for unique partitions (e.g., handling "Others")
    Object.values(uniquePartitions).forEach((partition: any) => {
      if (partition.NAME.includes("OthersNotNull")) {
        partition.PARTITION_RANGE = `${partitionColumn} =  ${this.getText("OthersNotNull")}`;
        partition.NAME = this.getText("Others");
      }
      if (partition.NAME.includes("OthersNull")) {
        partition.PARTITION_RANGE = `${partitionColumn} =  ${this.getText("OthersNull")}`;
        partition.NAME = this.getText("Others");
      }
    });

    // read CPU time
    const cpuTime = await this.getSystemLogDetails(this.selectedTaskLogId);
    if (cpuTime && cpuTime?.value) {
      if (cpuTime?.value[0]?.PEAK_CPU) {
        metricsArray.push({
          name: this.getText("HANA_PEAK_CPU_TIME"),
          value: `${cpuTime.value[0].PEAK_CPU} ms`,
        });
      }
      if (cpuTime?.value[0]?.USED_IN_DISK) {
        metricsArray.push({
          name: this.getText("USED_IN_DISK"),
          value: `${cpuTime.value[0].USED_IN_DISK} MiB`,
        });
      }
    }

    this.view.getModel("logsModel").setProperty("/showPartitionRange", showPartitionRange);
    this.view.getModel("logsModel").setProperty("/generalMetrics", metricsArray);
    this.view
      .getModel("logsModel")
      .setProperty("/metricTitle", this.getText("metricsTitle", [metricsArray.length.toString()]));
    this.view.getModel("logsModel").setProperty("/hasPartitionMetrics", hasPartitionMetrics);
    this.view.getModel("logsModel").setProperty("/partitionMetrics", Object.values(uniquePartitions));
    this.view
      .getModel("logsModel")
      .setProperty(
        "/partitionmetricTitle",
        this.getText("partitionmetricsTitle", [Object.values(uniquePartitions).length.toString()])
      );
  }

  private async getSystemLogDetails(taskLogId: string): Promise<any> {
    const sUrl = `monitoring/TASKS/TASK_LOGS_MEMORY?disableLazyLoading=true&$filter=(TASK_LOG_ID eq ${taskLogId})&$skip=0&$top=531`;

    try {
      const results = await ServiceCall.request({
        url: sUrl,
        type: HttpMethod.GET,
        contentType: ContentType.APPLICATION_JSON,
      });
      return results?.data;
    } catch (e) {
      return [];
    }
  }

  onMetricSearch(oEvent: sap.ui.base.Event, isPartitionMetrics: boolean) {
    const sValue = oEvent.getParameter("newValue");
    let oFilter, tableId, title;
    if (isPartitionMetrics) {
      const filters = [
        new sap.ui.model.Filter("NAME", sap.ui.model.FilterOperator.Contains, sValue),
        new sap.ui.model.Filter("PARTITION_RANGE", sap.ui.model.FilterOperator.Contains, sValue),
        new sap.ui.model.Filter("NUMBER_OF_RECORDS", sap.ui.model.FilterOperator.Contains, sValue),
        new sap.ui.model.Filter("MEMORY_CONSUMPTION_GIB", sap.ui.model.FilterOperator.Contains, sValue),
      ];
      oFilter = new sap.ui.model.Filter({
        filters: filters,
        and: false,
      });
      tableId = "partitionMetricsTable";
      title = "/partitionmetricTitle";
    } else {
      const filters = [
        new sap.ui.model.Filter("name", sap.ui.model.FilterOperator.Contains, sValue),
        new sap.ui.model.Filter("value", sap.ui.model.FilterOperator.Contains, sValue),
      ];
      oFilter = new sap.ui.model.Filter({
        filters: filters,
        and: false,
      });
      tableId = "metricsTable";
      title = "/metricTitle";
    }
    const oBinding = this.byId(tableId).getBinding("items");
    oBinding.filter([oFilter]);

    if (isPartitionMetrics) {
      this.view
        .getModel("logsModel")
        .setProperty("/partitionmetricTitle", this.getText("partitionmetricsTitle", [oBinding.getLength().toString()]));
    } else {
      this.view
        .getModel("logsModel")
        .setProperty("/metricTitle", this.getText("metricsTitle", [oBinding.getLength().toString()]));
    }
  }

  public async onTaskChainRowSelect(oEvent: sap.ui.base.Event) {
    let oObject;
    let oMessages;
    this.setLayout("ThreeColumnsMidExpanded");
    if (oEvent.getParameter("rowIndex") != -1) {
      const oSource = oEvent.getSource() as sap.m.Table;
      const context = oSource["getContextByIndex"](oSource["getSelectedIndex"]());
      oObject = context ? context.getObject() : undefined;
    } else {
      oObject =
        oEvent.getParameter("rowContext") &&
        oEvent.getParameter("rowContext").getModel() &&
        oEvent.getParameter("rowContext").getModel().getProperty(oEvent.getParameter("rowContext").getPath());
    }
    if (isUndefined(oObject)) {
      return;
    }
    // fetch messages
    const oMessagesTable: any = this.view.byId("taskChainMessageTable");
    oMessagesTable.setBusy(true);
    oMessagesTable.setBusyIndicatorDelay(0);
    const chainsFilteredTableHeader = this.getBundle().getText("chainsFilteredTableHeader", [oObject.objectId]);
    this.view.getModel("logsModel").setProperty("/chainsFilteredTableHeader", chainsFilteredTableHeader);
    this.view.getModel("logsModel").setProperty("/chainsMessagesTableHeader", oObject.objectName);
    // this.view.getModel("logsModel").setProperty("/chainsTableHeader", oObject.objectName);
    if (oObject && oObject.logId) {
      let oMsgObj = await this.fetchMessages(oObject.logId, oObject.crossSpaceName);
      oMessages = oObject.logId ? (oMsgObj ? oMsgObj.messages : []) : oObject.messages;
      let messageText = "";
      if (oMessages === undefined && oObject.spaceId && oObject.spaceId !== this.spaceId) {
        oMessages = [];
        messageText = await getResourceText(
          MAP_MESSAGEBUNDLE_IDS["sap.dwc.taskFramework"],
          "missingPrivilegeOnSpaceError"
        );
      }
      await this.translateMessages(oMessages);
      oMessages?.forEach((msg) => {
        msg.fullTimeStamp = msg.timestamp;
        msg.timestamp = this.formatDateTime(msg.timestamp);
        msg.formattedSeverity = this.severityTextFormatter(msg.severity);
        const isBWProcessChain = oObject.applicationId === ApplicationId.BW_PROCESS_CHAIN ? true : false;
        if (isBWProcessChain && msg?.link && msg?.link?.url) {
          msg.isBWProcessChainLink = isBWProcessChain;
        }
      });
      (this.view.getModel("messagesModel") as sap.ui.model.json.JSONModel).setProperty("/", oMessages);
      const isDataFlowNode = oObject.applicationId === ApplicationId.DATA_FLOWS ? true : false;
      if (!isDataFlowNode) {
        (this.view.getModel("blobContentMessageModel") as sap.ui.model.json.JSONModel).setProperty(
          "/logId",
          oObject.logId
        );
        (this.view.getModel("blobContentMessageModel") as sap.ui.model.json.JSONModel).setProperty(
          "/spaceId",
          oObject.spaceId
        );
      }
      (this.view.getModel("messagesModel") as sap.ui.model.json.JSONModel).setProperty(
        "/isDataFlowNode",
        isDataFlowNode
      );
      (this.view.getModel("messagesModel") as sap.ui.model.json.JSONModel).setProperty(
        "/chainsMessagesCrossSpaceIssueText",
        messageText !== "" ? messageText : ""
      );
      this.sDetailsTableHeader = this.getBundle().getText("txtDetailMessages", [oMessages.length.toString()]);
      (this.view.getModel("logsModel") as sap.ui.model.json.JSONModel).setProperty(
        "/sDetailsTableHeader",
        this.sDetailsTableHeader
      );
      if (oObject.applicationId === ApplicationId.DATA_FLOWS) {
        const oResponse = await this.fetchDataFlowDetails(oObject.objectId);
        let handleId;
        let selectedTasklogIndex = 0;
        if (oResponse.data) {
          if (this.selectedTaskLogId) {
            selectedTasklogIndex = oResponse.data.instances.findIndex(
              (e) => e.logId?.toString() === this.selectedTaskLogId
            );
            if (selectedTasklogIndex === -1) {
              selectedTasklogIndex = 0;
            }
          }
          handleId = oResponse.data.instances[selectedTasklogIndex].externalInstanceId;
          oResponse.data.instances[selectedTasklogIndex].handle = handleId;
        }
        await this.getDataflowDetails(
          oObject.objectId,
          oObject.logId,
          oResponse.data?.instances?.[selectedTasklogIndex] || { logId: oObject.logId }
        );
      }
    } else {
      (this.view.getModel("messagesModel") as sap.ui.model.json.JSONModel).setProperty("/", []);
      this.sDetailsTableHeader = this.getBundle().getText("txtDetailMessages", ["0"]);
      (this.view.getModel("logsModel") as sap.ui.model.json.JSONModel).setProperty(
        "/sDetailsTableHeader",
        this.sDetailsTableHeader
      );
    }
    oMessagesTable.setBusy(false);
  }

  /**
   * on select
   *
   * @memberof TaskLogClass
   */
  public async onSelect(oEvent: sap.ui.base.Event) {
    let oMessages;
    const rowContext = oEvent.getParameter("listItem").getBindingContext("logsModel");
    const oObject = rowContext.getObject();
    const routerModel = this.view.getModel("routerModel") as sap.ui.model.json.JSONModel;
    const layout = routerModel.getProperty("/layout");
    if (layout === "OneColumn") {
      this.setLayout(this.isTaskChainRoute ? "ThreeColumnsMidExpanded" : "TwoColumnsMidExpanded");
    }
    if (
      oObject.activity === Activity.EXECUTE_VIEW_ANALYZER &&
      sap.ui.getCore().getModel("privilege").getProperty("/DWC_DATABUILDER")?.read
    ) {
      if (oObject.status === "RUNNING") {
        await this.updateViewAnalyzerUI(oObject.logId, true);
      } else {
        await this.updateViewAnalyzerUI(oObject.logId, false);
      }
      this.setLayout("TwoColumnsMidExpanded");
    } else if (this.isViewMonitorRoute) {
      const entitySection = this.view.byId(
        "shellMainContent---dataIntegrationComponent---taskLog--Entities"
      ) as sap.uxap.ObjectPageSection;
      entitySection?.setVisible(false);
      this.setLayout("TwoColumnsMidExpanded");
    }

    (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty("/", oObject);
    const oSelectedTasklogIndex = (this.view.getModel("logsModel") as sap.ui.model.json.JSONModel)
      ?.getData()
      .findIndex((e) => e.logId === oObject.logId);
    (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty(
      "/selectedIndex",
      oSelectedTasklogIndex
    );

    // fetch messages
    const oMessagesTable: any = this.isTaskChainRoute
      ? this.view.byId("taskChainLogMessagesTable")
      : this.view.byId("taskLogMessageTable");
    const oSubMessagesTable: any = this.view.byId("taskChainMessageTable");
    oMessagesTable.setBusy(true);
    oMessagesTable.setBusyIndicatorDelay(0);
    if (oObject) {
      (this.view.getModel("blobContentMessageModel") as sap.ui.model.json.JSONModel).setProperty(
        "/logId",
        oObject.logId
      );
      (this.view.getModel("blobContentMessageModel") as sap.ui.model.json.JSONModel).setProperty(
        "/spaceId",
        oObject.spaceId
      );
      if (this.isTaskChainRoute) {
        oSubMessagesTable.setBusy(true);
        oSubMessagesTable.setBusyIndicatorDelay(0);
        let msgObj;
        if (oObject.logId) {
          msgObj = await this.fetchTaskChainMessages(oObject.logId);
        }
        if (msgObj === null || oObject.logId === undefined) {
          msgObj = {};
          msgObj.data = {
            children: [],
            objectId: oObject.objectId,
            status: oObject.status,
            applicationId: oObject.applicationId,
            messages: oObject.messages ? oObject.messages : [],
          };
        }
        oMessages = msgObj.data.messages;
        const chainTree = await this.getTaskChainTreeModel(msgObj.data);
        const treeTableRowCount = chainTree.nodes[0].nodes.length + 5;
        (this.view.getModel("taskChainTreeModel") as sap.ui.model.json.JSONModel).setProperty("/", chainTree);
        this.view.getModel("taskChainTreeModel").setProperty("/treeTableRowCount", treeTableRowCount);
        const childrenLength = msgObj.data.children ? msgObj.data.children.length : 0;
        this.chainTableHeader = this.getBundle().getText("taskListHeader", [childrenLength.toString()]);
        (this.view.getModel("logsModel") as sap.ui.model.json.JSONModel).setProperty(
          "/chainsTableHeader",
          this.chainTableHeader
        );
        (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty(
          "/parentLabel",
          this.getText("parentChainLabel")
        );
        const authorizedOnParent = msgObj?.data && msgObj?.data?.parent && msgObj?.data?.parent?.authorizedOnParent;
        (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty(
          "/authorizedOnParent",
          authorizedOnParent
        );
        const taskChainParent = msgObj.data && msgObj.data.parent && msgObj.data.parent.objectId;
        (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty(
          "/taskChainParent",
          taskChainParent
        );
        const taskChainParentSpace = msgObj?.data && msgObj?.data?.parent && msgObj?.data?.parent?.spaceId;
        (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty(
          "/taskChainParentSpace",
          taskChainParentSpace
        );
        const taskChainParentLink = this.getTaskChainParentLink(taskChainParentSpace);
        (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty(
          "/taskChainParentLink",
          taskChainParentLink
        );
        const chainsFilteredTableHeader = this.getBundle().getText("chainsFilteredTableHeader", [oObject.objectId]);
        this.view.getModel("logsModel").setProperty("/chainsFilteredTableHeader", chainsFilteredTableHeader);
        this.view.getModel("logsModel").setProperty("/chainsMessagesTableHeader", oObject.objectName);
        oMessagesTable.setSelectedIndex(0);
      } else {
        const withMetrics = this.isViewMonitorRoute;
        let oMsgObj = await this.fetchMessages(oObject.logId, oObject.spaceId, withMetrics);
        this.selectedTaskLogId = oObject.logId;
        this.view.getModel("logsModel").setProperty("/hasPartitionMetrics", false);
        if (withMetrics) {
          if (oMsgObj.metrics) {
            await this.processmetricsData(oMsgObj.metrics);
            this.view.getModel("logsModel").setProperty("/hasMetrics", true);
          } else {
            this.view.getModel("logsModel").setProperty("/hasMetrics", false);
          }
          oMsgObj = oMsgObj.logDetails;
        }
        oMessages = oObject?.logId ? (oMsgObj ? oMsgObj.messages : []) : oObject?.messages;
        let taskChainParent = oMsgObj && oMsgObj.parent && oMsgObj.parent.objectId;
        (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty(
          "/parentLabel",
          this.getText("parentChainLabel")
        );
        if (this.isECNRoute() && oMsgObj && oMsgObj.activity === Activity.GENERATE_STOP_CHAIN) {
          taskChainParent = oMsgObj && oMsgObj.externalInstanceId;
          (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty(
            "/parentLabel",
            this.getText("parentTaskLabel")
          );
          (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty("/isSubTask", true);
        }
        const authorizedOnParent = oMsgObj && oMsgObj.parent && oMsgObj.parent.authorizedOnParent;
        (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty(
          "/authorizedOnParent",
          authorizedOnParent
        );
        (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty(
          "/taskChainParent",
          taskChainParent
        );
        const taskChainParentSpace = oMsgObj && oMsgObj.parent && oMsgObj.parent.spaceId;
        (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty(
          "/taskChainParentSpace",
          taskChainParentSpace
        );
        const taskChainParentLink = this.getTaskChainParentLink();
        (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty(
          "/taskChainParentLink",
          taskChainParentLink
        );
      }
      await this.translateMessages(oMessages);
      oMessages?.forEach((msg) => {
        msg.fullTimeStamp = msg.timestamp;
        msg.timestamp = this.formatDateTime(msg.timestamp);
        msg.formattedSeverity = this.severityTextFormatter(msg.severity);
        const isBWProcessChain = oObject.applicationId === ApplicationId.BW_PROCESS_CHAIN ? true : false;
        if (isBWProcessChain && msg.link && msg.link.url) {
          msg.isBWProcessChainLink = isBWProcessChain;
        }
      });
      (this.view.getModel("messagesModel") as sap.ui.model.json.JSONModel).setProperty("/", oMessages);
      this.sDetailsTableHeader = this.getBundle().getText("txtDetailMessages", [oMessages.length.toString()]);
      (this.view.getModel("logsModel") as sap.ui.model.json.JSONModel).setProperty(
        "/sDetailsTableHeader",
        this.sDetailsTableHeader
      );
    }
    oMessagesTable.setBusy(false);
    if (this.isTaskChainRoute) {
      oSubMessagesTable.setBusy(false);
    }
    if (this.isGenericLogs()) {
      if (this.srcObjectId) {
        this.view.getModel("selectedModel").setProperty("/objectId", this.srcObjectId);
      } else {
        this.view.getModel("selectedModel").setProperty("/objectId", this.spaceId);
      }
    }
    this.view.setBusy(false);
  }

  onMidColumnSectionChange(oEvent) {
    this.setLayout("TwoColumnsMidExpanded");
  }

  showPayLoadMsgStrip(payload, activity) {
    if (activity === Activity.EXECUTE_VIEW_ANALYZER && payload !== undefined && payload !== "") {
      return true;
    }
    return false;
  }

  async updateViewAnalyzerUI(logId, isReset) {
    this.view.getModel("selectedModel").setProperty("/globalPayload", "");
    this.getExplainPlanDetails().resetData();
    if (!this.entitiesView) {
      this.entitiesView = await sap.ui.core.mvc.View.create({
        type: sap.ui.core.mvc.ViewType.XML,
        id: "entitiesListView",
        viewName: require("../../viewanalyzer/view/Entities.view.xml"),
      });
      const middlePage = this.view.byId("detailLogTable") as sap.uxap.ObjectPageLayout;
      middlePage.getSections()[0].getSubSections()[0].addBlock(this.entitiesView);
    }
    const entitySection = this.view.byId(
      "shellMainContent---dataIntegrationComponent---taskLog--Entities"
    ) as sap.uxap.ObjectPageSection;
    entitySection?.setVisible(true);
    const viewButton = sap.ui.getCore().byId("entitiesListView--entityViewButton") as sap.m.SegmentedButton;
    viewButton?.setSelectedItem(viewButton?.getItems()[0]);
    viewButton?.fireSelectionChange({
      item: viewButton?.getItems()[0],
    });
    this.view.getModel("actionControlModel")?.setProperty("/enableStart", false);
    this.view.getModel("actionControlModel")?.setProperty("/enableNavigation", false);

    (this.entitiesView.getController() as EntitiesClass).setDetails(this.srcObjectId, this.spaceId);
    if (isReset) {
      (this.entitiesView.getController() as EntitiesClass).resetModelData();
    } else {
      (this.entitiesView.getController() as EntitiesClass).getResults(this.spaceId, logId);
      (this.entitiesView.getController() as EntitiesClass).initializeLineageView();
    }
  }

  private async selectLog(oParameter) {
    let selectedTaskLogIdDetails, oSelectedTasklogIndex;
    const otaskLogTable = this.byId("taskLogTable");
    if (oParameter.selectedTaskLogId) {
      selectedTaskLogIdDetails = (this.view.getModel("logsModel") as sap.ui.model.json.JSONModel)
        ?.getData()
        .filter((e) => e.hasOwnProperty("logId") && e.logId.toString() === oParameter.selectedTaskLogId.toString());
      oSelectedTasklogIndex = (this.view.getModel("logsModel") as sap.ui.model.json.JSONModel)
        ?.getData()
        .findIndex((e) => e.hasOwnProperty("logId") && e.logId.toString() === oParameter.selectedTaskLogId.toString());
    }
    if (
      oParameter.selectedTaskLogId &&
      selectedTaskLogIdDetails &&
      selectedTaskLogIdDetails.length === 1 &&
      oSelectedTasklogIndex >= 0
    ) {
      (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty(
        "/",
        selectedTaskLogIdDetails[0]
      ); // Need to filter logs with selectedTaskLogId
      setTimeout(() => {
        const table = otaskLogTable as sap.m.Table;
        const item = table.getItems()[oSelectedTasklogIndex];
        (table as any).scrollToIndex(oSelectedTasklogIndex);
        table.setSelectedItem(item, true, true);
      }, 10);
    } else {
      const table = otaskLogTable as sap.m.Table;
      const item = table.getItems()[0];
      table.setSelectedItem(item, true, true);
    }

    if (this.isGenericLogs()) {
      if (this.srcObjectId) {
        this.view.getModel("selectedModel").setProperty("/objectId", this.srcObjectId);
      } else {
        this.view.getModel("selectedModel").setProperty("/objectId", this.spaceId);
      }
    }
  }

  /**
   * Error Message details link text formatter
   *
   * @memberof TaskLogClass
   */

  public showLogErrorDetailsLinkFormatter(messageText: string, sDetails: string): boolean {
    if (messageText && sDetails && sDetails !== "undefined") {
      return true;
    }
    return false;
  }

  public pickerTooltipFormatter(value, type) {
    return this.partitionDialog?.pickerTooltipFormatter(value, type);
  }

  public showViewDetailsForExplainPlan(contentSemanticValue): boolean {
    if (
      contentSemanticValue === "ExplainPlanJSON" &&
      ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_RUNTIME").read &&
      ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_DATABUILDER").read
    ) {
      return true;
    }
    return false;
  }

  /**
   * BW tenant link visibility formatter
   *
   */
  public showBWTenantLink(isBWProcessChainLink: boolean): boolean {
    if (isBWProcessChainLink) {
      return true;
    }
    return false;
  }

  /**
   * visibility of message strip when we are unable to fetch message because of privileage or other issue
   * @param chainMessageIssueText
   * @returns boolean
   */
  public chainMessageIssueVisibilityFormatter(chainMessageIssueText: string): boolean {
    if (chainMessageIssueText === null || chainMessageIssueText === undefined || chainMessageIssueText === "") {
      return false;
    }
    return true;
  }

  public showDownloadButton(isBlobContentAvailable, contentSemanticValue) {
    if (
      isBlobContentAvailable &&
      (contentSemanticValue === "PlanVizXML" || contentSemanticValue === "ExplainPlanJSON") &&
      ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_RUNTIME")?.read &&
      ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_DATABUILDER")?.read
    ) {
      return true;
    } else if (isBlobContentAvailable && !["PlanVizXML", "ExplainPlanJSON"].includes(contentSemanticValue)) {
      return true;
    }
    return false;
  }

  public openImpactLineageDialog() {
    const table = this.getView().getModel("detailsModel").getData();
    this.serviceUtil.showImpactAnalysisDialog(table);
  }

  // Handle popover on click of view details
  public handleReplicationChangeViewDetails(oEvent) {
    const table = this.view.getModel("detailsModel");
    if (table?.getData()?.scheduled) {
      let nextSchedule = this.view.getModel("detailsModel")?.getProperty("/nextSchedule");
      let nextScheduleRun = this.formatNextSchedule(nextSchedule, true);
      table.setProperty("/nextScheduleRun", nextScheduleRun);
    }
    const sourceControl = oEvent.getSource();
    RemoteTableServiceUtil.getReplicationMessage(oEvent?.getSource(), this.view, table.getData());
  }

  /**
   * handle view error log detail press
   *
   * @memberof TaskLogClass
   */

  public async handleLogErrorDetailPress(oEvent: sap.ui.base.Event) {
    const src = oEvent.getSource() as any;
    const messageModel = src.getBindingContext("messagesModel");
    if (messageModel?.getObject("details") === "SHOW_DROPSUBSCRIPTION") {
      const message = messageModel.getObject();
      this.serviceUtil.showDropSubscriptionDetails(message, this.view);
    } else if (
      messageModel?.getObject()?.messageBundleKey === "VIEW_PERSISTENCY_OUT_OF_MEMORY_RESULT_MESSAGE" &&
      sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_VIEW_PERSISTENCY_ADVISE_ON_OOM_ERRORS")
    ) {
      this.formatOOMErrorDetails(oEvent, messageModel);
    } else if (
      messageModel?.getObject()?.messageBundleKey === "PERFORMANCE_ANALYSIS_FINISHED" &&
      sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_VIEW_RUNTIME_METRICS")
    ) {
      this.view.setBusy(true);
      await showRuntimeMetricsdialog(
        this.spaceId,
        this.srcObjectId,
        this.selectedTaskLogId,
        this.view.getModel("detailsModel").getProperty("/displayName")
      );
      this.view.setBusy(false);
    } else if (!src.getId()?.includes("taskLogMessageTable") && !src.getId()?.includes("taskChainMessageTable")) {
      this.handleErrorPopover(oEvent);
    } else {
      this.formatDetailsDialog(oEvent, messageModel);
    }
  }

  private formatOOMErrorDetails(oEvent, messageModel) {
    const errorDetais = JSON.parse(messageModel.getObject().details);
    const details = {
      "Out of Memory Event Reason": errorDetais?.eventReason,
      Message: this.getText("OOMMessage", [this.srcObjectId]),
      "Memory Limit(GB)": errorDetais?.memoryLimitSize / (1024 * 1024 * 1024), // convert to gigabytes
    };
    if (errorDetais?.workloadClassName) {
      details["Workload Class"] = errorDetais?.workloadClassName;
    }
    details["Links"] = this.getText("VIEW_PERSISTENCY_GUIDE");
    messageModel.getObject().OOMDetails = JSON.stringify(details);
    this.formatDetailsDialog(oEvent, messageModel, true);
  }

  protected async formatDetailsDialog(oEvent, messageModel, isOOMDetails = false) {
    let sDetails = "";
    const src = oEvent.getSource() as any;
    if (oEvent.getParameters().id === this.routeName + "--ErrorStripLink") {
      sDetails = (this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel)?.getData().errorMessage;
    } else {
      sDetails = isOOMDetails ? messageModel.getObject("OOMDetails") : messageModel.getObject("details");
    }
    const isJSONDataAvailable = messageModel.getObject("isJsonDataAvailable");
    const textMessage = messageModel.getObject("text");
    const oBundle = this.getBundle();
    (this.view.getModel("messagesModel") as sap.ui.model.json.JSONModel).setProperty(
      "/viewDetailsWithRemoteQuery",
      true
    );
    (this.view.getModel("messagesModel") as sap.ui.model.json.JSONModel).setProperty("/remoteQueryButtonEnable", true);
    (this.view.getModel("messagesModel") as sap.ui.model.json.JSONModel).setProperty("/remoteStatementsDeleted", false);
    (this.view.getModel("messagesModel") as sap.ui.model.json.JSONModel).setProperty("/maxLinesGreaterThanTwo", false);
    (this.view.getModel("messagesModel") as sap.ui.model.json.JSONModel).setProperty(
      "/viewDetailsWithRemoteQueryCS",
      false
    );

    let parsedDetails;
    let hasNestedProperties = false;

    try {
      parsedDetails = JSON.parse(sDetails);
      hasNestedProperties = Object.values(parsedDetails).some((value) => typeof value === "object");
      if (parsedDetails && !isOOMDetails) {
        Object.entries(parsedDetails).forEach(([key, value]) => {
          parsedDetails[key] = encodeXML(value);
        });
      }
    } catch (e) {
      parsedDetails = null;
    }

    if (!parsedDetails && sDetails.includes("CorrelationId:") && (sDetails.match(/:/g) || []).length === 1) {
      const correlationId = sDetails.split("CorrelationId: ")[1].split(",")[0];
      parsedDetails = { CorrelationId: correlationId };
    }

    if (isJSONDataAvailable && !this.messageDialog) {
      await this.setUpDialogModel(src, sDetails);
      this.messageDialog.open();
      this.getView().setBusy(false);
    } else {
      if (!this.messageDialog) {
        let content;
        if (parsedDetails && !hasNestedProperties) {
          content = new sap.ui.layout.form.SimpleForm({
            content: Object.entries(parsedDetails).flatMap(([key, value]) => [
              new sap.m.Label({ text: `${key}` }),
              new sap.m.FormattedText({
                htmlText: `${value}`,
              }),
            ]),
          });
        } else {
          content = new sap.m.Text({});
          content.setText(sDetails);
          (this.view.getModel("messagesModel") as sap.ui.model.json.JSONModel).setProperty(
            "/viewDetailsWithRemoteQuery",
            false
          );
        }

        const hasCorrelationId = parsedDetails && (parsedDetails.correlationId || parsedDetails.CorrelationId);
        const buttonText = hasCorrelationId ? oBundle.getText("CopyCorrelationID") : oBundle.getText("Copy");

        this.messageDialog = new sap.m.Dialog({
          type: sap.m.DialogType.Message,
          title: this.getBundle().getText("VIEW_ERROR_DETAILS_DIALOG_TITLE"),
          content: content,
          beginButton: new sap.m.Button({
            text: buttonText,
            press: function () {
              let textToCopy = sDetails;
              if (hasCorrelationId) {
                textToCopy = parsedDetails.correlationId;
              }
              sap.m.MessageToast.show(oBundle.getText("copiedToClip"));
              const oShadowDomInput = document.createElement("input");
              document.body.appendChild(oShadowDomInput);
              oShadowDomInput.setAttribute("value", textToCopy);
              oShadowDomInput.select();
              document.execCommand("copy");
              document.body.removeChild(oShadowDomInput);
            }.bind(this),
          }),
          endButton: new sap.m.Button({
            text: oBundle.getText("Close"),
            press: function () {
              this.messageDialog.close();
            }.bind(this),
          }),
        });
        this.messageDialog.setContentWidth("45%");
        if (isOOMDetails) {
          this.messageDialog.getBeginButton().setVisible(false);
        }
        this.messageDialog.open();
      }
      this.messageDialog.attachAfterClose(() => {
        if (this.messageDialog) {
          this.messageDialog.destroy();
          this.messageDialog = null;
        }
      });
    }
  }

  public async setUpDialogModel(src: any, sDetails: any) {
    const taskLogId = (this.view.getModel("blobContentMessageModel") as sap.ui.model.json.JSONModel).getProperty(
      "/logId"
    );
    const textMessage = src.getBindingContext("messagesModel").getObject("text");
    const messageNumber = src.getBindingContext("messagesModel").getObject("messageNumber");
    this.getView().setBusy(true);
    const jsonData = await this.fetchJsonContent(taskLogId, messageNumber);
    const fragmentId: string = require("../view/fragment/MessageViewDetails.fragment.xml");
    this.messageDialog = sap.ui.xmlfragment("MessageViewdetailsBlob", fragmentId, this) as sap.m.Dialog;
    this.getView().addDependent(this.messageDialog);
    const Message = sap.ui.core.Fragment.byId("MessageViewdetailsBlob", "txtMessage") as sap.m.Text;
    Message.setText(textMessage);
    const executingSqlStatement = sap.ui.core.Fragment.byId(
      "MessageViewdetailsBlob",
      "executingSQLStatement"
    ) as sap.m.Text;
    executingSqlStatement.setText(sDetails);
    let statementId = jsonData.data?.content?.statementId;
    const numberOfRemoteSqlStatements = jsonData.data?.content?.numberOfRemoteSqlStatements;
    const textStatementId = sap.ui.core.Fragment.byId("MessageViewdetailsBlob", "txtStatementId") as sap.m.Text;
    textStatementId.setText(statementId);
    this.statementId = statementId;
    const textnumberOfSqlStatements = sap.ui.core.Fragment.byId(
      "MessageViewdetailsBlob",
      "txtnumberOfSqlStatements"
    ) as sap.m.Text;
    textnumberOfSqlStatements.setText(numberOfRemoteSqlStatements);
    if (jsonData.data?.uiId === "ViewDetailsWithInstructions") {
      (this.view.getModel("messagesModel") as sap.ui.model.json.JSONModel).setProperty(
        "/viewDetailsWithRemoteQuery",
        false
      );
    } else {
      if (jsonData.data?.uiId === "ViewDetailsWithRemoteQueryLinkCrossSpace") {
        (this.view.getModel("messagesModel") as sap.ui.model.json.JSONModel).setProperty(
          "/maxLinesGreaterThanTwo",
          false
        );
        (this.view.getModel("messagesModel") as sap.ui.model.json.JSONModel).setProperty(
          "/viewDetailsWithRemoteQueryCS",
          true
        );
        const crossSpaces = jsonData.data["crossSpaceDetails"];
        const privilegeService = ShellContainer.get().getPrivilegeService();
        interface SpaceObject {
          numberOfRemoteSqlStatementsSpace: number;
          spaceName: string;
          hasDataIntegration: boolean;
        }
        const priviligedSpaces: { [key: string]: boolean } = {};
        await Repo.getSpaceList(["id", "name"]).then((spaceList) => {
          for (const space of spaceList) {
            if (
              privilegeService.hasPrivilegeInScope(space.name, "DWC_DATAINTEGRATION", PrivilegeTypes.EXECUTE) ||
              privilegeService.hasPrivilegeInScope(space.name, "DWC_DATAINTEGRATION", PrivilegeTypes.READ) ||
              privilegeService.hasPrivilegeInScope(space.name, "DWC_DATAINTEGRATION", PrivilegeTypes.UPDATE)
            ) {
              priviligedSpaces[space.name] = true;
            } else {
              priviligedSpaces[space.name] = false;
            }
            (this.view.getModel("messagesModel") as sap.ui.model.json.JSONModel).setProperty(
              "/" + space.name,
              space.businessName
            );
          }
        });
        let unAuthorisedSpaces = 0;
        let unAuthorisedRemoteStatements = 0;
        let index = 0;
        const crossSpaceObjects: SpaceObject[] = [];
        let spaceId = this.spaceId;
        crossSpaces.forEach((element) => {
          if (priviligedSpaces[element.spaceName] === undefined) {
            unAuthorisedSpaces++;
            unAuthorisedRemoteStatements += element.numberOfRemoteSqlStatementsSpace;
          } else {
            element.hasDataIntegration = priviligedSpaces[element.spaceName];
            spaceId = element.spaceName;
            crossSpaceObjects.push(element);
          }
          index++;
        });
        if (unAuthorisedSpaces > 0) {
          const unAuthSpaces = {
            numberOfRemoteSqlStatementsSpace: unAuthorisedRemoteStatements,
            spaceName: this.getBundle().getText("txtUnauthorisedSpaces"),
            hasDataIntegration: false,
          };
          crossSpaceObjects.push(unAuthSpaces);
        }
        (this.view.getModel("messagesModel") as sap.ui.model.json.JSONModel).setProperty(
          "/crossSpaceList",
          crossSpaceObjects
        );
        statementId = statementId.toString();
        const queries = await this.loadRemoteQueries(statementId, spaceId);
        if (queries === 0) {
          (this.view.getModel("messagesModel") as sap.ui.model.json.JSONModel).setProperty(
            "/remoteStatementsDeleted",
            true
          );
        }
      } else {
        if (jsonData.data.crossSpaceStatements) {
          (this.view.getModel("messagesModel") as sap.ui.model.json.JSONModel).setProperty(
            "/remoteQueryButtonEnable",
            false
          );
        } else {
          statementId = statementId.toString();
          const queries = await this.loadRemoteQueries(statementId, this.spaceId);
          if (queries === 0) {
            (this.view.getModel("messagesModel") as sap.ui.model.json.JSONModel).setProperty(
              "/remoteQueryButtonEnable",
              false
            );
            (this.view.getModel("messagesModel") as sap.ui.model.json.JSONModel).setProperty(
              "/remoteStatementsDeleted",
              true
            );
          }
        }
      }
      const textControl = sap.ui.core.Fragment.byId("MessageViewdetailsBlob", "executingSQLStatement") as sap.m.Text;
      const oTextDomRef = textControl.getText();
      const bTextOverflow = oTextDomRef.length > 150;
      if (bTextOverflow) {
        (this.view.getModel("messagesModel") as sap.ui.model.json.JSONModel).setProperty(
          "/maxLinesGreaterThanTwo",
          true
        );
      }
    }
  }

  public async navigateToMonitor(hasDataIntegration, spaceName) {
    if (hasDataIntegration) {
      this.openRemoteQueryMonitorCS(spaceName);
    } else {
      const msg = this.getBundle().getText("txtPrivilegeError");
      sap.m.MessageBox.error(msg, {
        id: "privilegeText",
      });
    }
  }

  public handleCopySqlStatement() {
    const oBundle = this.getBundle();
    const sDetails = sap.ui.core.Fragment.byId("MessageViewdetailsBlob", "executingSQLStatement")["getText"]();
    sap.m.MessageToast.show(oBundle.getText("copiedToClip"));
    const oShadowDomInput = document.createElement("input");
    document.body.appendChild(oShadowDomInput);
    oShadowDomInput.setAttribute("value", sDetails);
    oShadowDomInput.select();
    document.execCommand("copy");
    document.body.removeChild(oShadowDomInput);
  }

  public handleClose() {
    if (this.messageDialog) {
      this.messageDialog.destroy();
      this.messageDialog = null;
    }
  }

  public openRemoteQueryMonitor() {
    const shellHash = "dataintegration&/di";
    const targetHash = `${encodeURIComponent(this.spaceId)}/remoteQueryMonitor/${this.statementId}`;
    const sUrl = `#/${shellHash}/${targetHash}`;
    sap.m.URLHelper.redirect(sUrl, false);
    this.messageDialog.destroy();
    this.messageDialog = null;
  }

  public openRemoteQueryMonitorCS(spaceId?: any) {
    const shellHash = "dataintegration&/di";
    const targetHash = `${encodeURIComponent(spaceId)}/remoteQueryMonitor/${this.statementId}`;
    const sUrl = `#/${shellHash}/${targetHash}`;
    sap.m.URLHelper.redirect(sUrl, false);
    this.messageDialog.destroy();
    this.messageDialog = null;
  }

  public async loadRemoteQueries(statementId, spaceId): Promise<any> {
    let sUrl = "monitor/" + spaceId + "/remotequeries";
    sUrl = sUrl + "?includeBusinessNames=true";
    return ServiceCall.request<any>({
      url: sUrl,
      type: HttpMethod.GET,
      contentType: ContentType.APPLICATION_JSON,
    })
      .then((oResponse) => {
        const queries: IRemoteQueryUI[] = oResponse?.data?.queries;
        let index = 0;
        queries.forEach((element) => {
          if (element.statementid === statementId) {
            index = index + 1;
          }
        });
        return index;
      })
      .catch((error) => {
        this.view.setBusy(false);
        MessageHandler.exception({ message: this.getText("txtReadBackendError"), exception: error });
        this.logError(error.message || error);
      });
  }

  onShowMoreClicked() {
    const textControl = sap.ui.core.Fragment.byId("MessageViewdetailsBlob", "executingSQLStatement") as sap.m.Text;
    textControl.setMaxLines(0);
    (this.view.getModel("messagesModel") as sap.ui.model.json.JSONModel).setProperty("/maxLinesGreaterThanTwo", false);
  }

  private getExplainPlanDetails() {
    if (!this["explainPlanView"]) {
      const dialog = require("../../viewanalyzer/view/ExplainPlanDetails.view.xml");
      this["explainPlanView"] = sap.ui.view({
        id: this.getView().getId() + "--explainPlanDetails",
        type: sap.ui.core.mvc.ViewType.XML,
        viewName: dialog,
        viewData: {
          spaceId: this.spaceId,
        },
      });
    }
    return this["explainPlanView"].getController() as ExplainPlanDetailsClass;
  }

  recordExplainPlan() {
    ShellContainer.get().getUsageCollectionService().recordAction({
      action: "ExplainPlan",
      feature: "viewMonitorTaskLog",
      eventtype: EventType.CLICK,
    });
  }

  /**
   * @description Used to download addtional message details
   * @param event Button press event
   * @param msgD MessageModel data
   */
  public async onDownloadAdditionalDetails(event, msgD) {
    const oSelectedModel = this.view.getModel("blobContentMessageModel");
    const sSpaceId = oSelectedModel?.getProperty("/spaceId");
    const sLogId = oSelectedModel?.getProperty("/logId");
    const msgNum = msgD?.messageNumber;
    let sUrl = `tf/${sSpaceId}/logs/${sLogId}/messages/${msgNum}/blobcontent`;
    if (msgD?.contentSemanticValue === "PlanVizXML") {
      sUrl = `advisor/${sSpaceId}/logs/${sLogId}/messages/${msgNum}/planviz`;
    }
    if (msgD?.contentSemanticValue === "ExplainPlanJSON") {
      this["explainPlanDetails"] = this.getExplainPlanDetails();
      this["explainPlanDetails"].downloadExplainPlanJSON(sSpaceId, sLogId, msgNum);
      return;
    }
    try {
      const xhr = new XMLHttpRequest();
      xhr.onreadystatechange = () => {
        if (xhr.readyState === 4) {
          if (xhr.status === 200) {
            window.location.href = sUrl;
            MessageHandler.success(this.getBundle().getText("downloadStarted"));
          } else if (xhr.status === 404) {
            MessageHandler.info(xhr.response);
          } else if (xhr.status === 500) {
            MessageHandler.exception({
              message: this.getBundle().getText("errorInDownload"),
              exception: xhr.response,
            });
          }
        }
      };
      xhr.open("get", sUrl);
      xhr.send(null);
    } catch (error) {
      MessageHandler.exception({
        message: this.getBundle().getText("errorInDownload"),
        exception: error,
      });
    }
  }

  public handleViewExplainPlanDetails(event, msgD) {
    const oSelectedModel = this.view.getModel("blobContentMessageModel");
    const sSpaceId = oSelectedModel?.getProperty("/spaceId");
    const sLogId = oSelectedModel?.getProperty("/logId");
    const msgNum = msgD?.messageNumber;
    this.getView().setBusy(true);
    this.getExplainPlanDetails().showExplainPlanDetails(sSpaceId, sLogId, msgNum, this.srcObjectId);
    this.getView().setBusy(false);
  }

  /**
   * get tasklog and message details on refresh
   *
   * @memberof TaskLogClass
   */

  public async onRefresh(isReset): Promise<void> {
    this.view.setBusy(true);
    isReset === true ? (this.selectedTaskLogId = undefined) : null; // reset log selection
    await this.getTaskLogDetails({
      srcObjectId: this.srcObjectId,
      selectedTaskLogId: this.selectedTaskLogId,
      activity: this.activity,
      applicationId: this.applicationId,
    });
    await this.selectLog({
      srcObjectId: this.srcObjectId,
      selectedTaskLogId: this.selectedTaskLogId,
      activity: this.activity,
    });
    this.view.setBusy(false);
  }

  public async onCompleteRefresh(): Promise<void> {
    this.view.setBusy(true);
    await this.onRefresh(true);
    if (objectDetailsRoutes.includes(this.routeName) && this.srcObjectId) {
      if (this.isTaskChainRoute) {
        await this.getTaskChainDetail();
      } else {
        await this.getObjectDetails(this.srcObjectId, this.routeName);
      }
      let scheduleDialog;
      if (this["isReusableTaskScheduleFFEnabled"] && this["isAdoptionOfTaskSchedulerEnabled"]) {
        await this.initScheduleDialog();
        scheduleDialog = this["newScheduleDialog"];
      } else {
        scheduleDialog = (
          this.getView().byId("taskSchedulingDialog") as sap.ui.core.mvc.View
        ).getController() as ITTaskScheduleController;
      }
      const sTableId = this.view.getModel("viewSettingsDialogModel").getProperty("/tableId/LOGS");
      const oTable = this.byId(sTableId);
      this.updateTableHeader(sTableId, oTable.getMaxItemsCount());
      const appId = this.getCurrentMonitorId();
      await scheduleDialog.refreshTaskScheduleList(this.spaceId, appId);
    }
    this.view.setBusy(false);
  }

  public async getTaskLogDetails(oParameter: {
    selectedTaskLogId?: string;
    activity?: string;
    srcObjectId?: string;
    applicationId?: string;
  }) {
    const oResponse = await super.getTaskLogDetails(oParameter, this.routeName);
    let data,
      logs = [],
      locks;
    if (oResponse?.data?.hasOwnProperty("logs")) {
      data = oResponse.data as ITaskLogResponse;
      logs = data.logs;
      locks = data.locks;
    } else {
      data = oResponse.data as ITaskLogIncomplete[];
      logs = data;
      locks = [];
    }

    const statActivity = [
      Activity.ALTER_STATISTICS,
      Activity.CREATE_STATISTICS,
      Activity.DROP_STATISTICS,
      Activity.REFRESH_STATISTICS,
    ];
    if (this.isStatisticsRoute) {
      logs = logs?.filter(checkActivity);
      function checkActivity(log) {
        return statActivity.includes(log.activity);
      }
    } else {
      logs = logs?.filter(checkActivity);
      function checkActivity(log) {
        return !statActivity.includes(log.activity);
      }
    }
    const excludeActivity = ["VALIDATE", "CANCEL_VALIDATE"];
    if (this.isViewMonitorRoute && sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_MODEL_VALIDATION")) {
      logs = logs?.filter(checkActivity);
      function checkActivity(log) {
        return !excludeActivity.includes(log.activity);
      }
    }

    if (locks.length > 0) {
      (this.view.getModel("settingsModel") as sap.ui.model.json.JSONModel).setProperty("/lockExists", true);
      (this.view.getModel("settingsModel") as sap.ui.model.json.JSONModel).setProperty("/locks", locks);
    } else {
      (this.view.getModel("settingsModel") as sap.ui.model.json.JSONModel).setProperty("/lockExists", false);
    }
    (this.view.getModel("settingsModel") as sap.ui.model.json.JSONModel).setProperty("/logsExists", true);
    if (!logs || logs?.length === 0) {
      this.resetEntitiesUI();
      if (this.isViewMonitorRoute) {
        (this.view.getModel("settingsModel") as sap.ui.model.json.JSONModel).setProperty("/logsExists", false);
      }
    }
    logs?.forEach((log) => {
      log.formattedStatus = logFormatterUtils.statusTextFormatter(log.status, log.subStatus);
      log.formattedStartTime = this.formatDateTime(log.startTime);
      log.formattedRuntime = isUndefined(log.runTime) ? "" : this.formatTimespanHHMMSS(Math.floor(log.runTime / 1000));
    });
    const sTableHeader = this.getBundle().getText("txtLogCount", [logs?.length + ""]);
    (this.view.getModel("logsModel") as sap.ui.model.json.JSONModel).setProperty("/", logs);
    (this.view.getModel("logsModel") as sap.ui.model.json.JSONModel).setProperty("/tableHeader", sTableHeader);

    this.setLogsModelWithFilterOptions();

    // (this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel).setProperty("/showErrorStrip", false);
    if (logs && logs[0]?.status === "ERROR") {
      (this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel).setProperty(
        "/ERROR_MSG",
        "An error occurred during real-time replication"
      );
      (this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel).setProperty("/showErrorStrip", true);
    }
  }

  setLogsModelWithFilterOptions() {
    const lastHour = moment.utc().add(-1, "hours").format();
    const last24Hour = moment.utc().add(-1, "days").format();
    const lastMonth = moment.utc().add(-1, "months").format();
    const logsModel = this.view.getModel("logsModel") as sap.ui.model.json.JSONModel;
    logsModel.setProperty("/filterLastHour", lastHour);
    logsModel.setProperty("/filterLast24Hour", last24Hour);
    logsModel.setProperty("/filterLastMonth", lastMonth);
  }

  public async partitionRefresh(data): Promise<void> {
    this.view.setBusy(true);
    (this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel).setProperty("/partitioningExists", true);
    this.checkPartitionChangedInfo(data);
    (this.view.getModel() as sap.ui.model.json.JSONModel).setData(data);
    this.view.setBusy(false);
  }

  public async objectDetailsRefresh(): Promise<void> {
    this.view.setBusy(true);
    await this.getObjectDetails(this.srcObjectId, this.routeName);
    this.view.setBusy(false);
  }

  public async onCancelRun(): Promise<void> {
    const remoteTable = this.getView().getModel("detailsModel").getData();
    const displayName = this.view.getModel("detailsModel").getData().displayName;
    let warnSubscription, latestRunningTaskDetails;
    if (this.isRemoteTableRoute) {
      this.view.setBusy(true);
      warnSubscription = await RemoteTableServiceUtil.warnBeforeDropSubscription(remoteTable, this.view);
      const otaskLogModelData = (this.getView().getModel("logsModel") as sap.ui.model.json.JSONModel)?.getData();
      latestRunningTaskDetails = otaskLogModelData.find((i) => i.status === "RUNNING");
      this.view.setBusy(false);
    }
    if (warnSubscription && remoteTable.dataAccess === "REMOTE" && latestRunningTaskDetails) {
      // call remove replicated data
      let continueAnyway;
      const stopUser = warnSubscription === issueCause.AGENTDOWN || warnSubscription === issueCause.CONNECTIONDOWN;
      continueAnyway = await RemoteTableServiceUtil.showDropSubscriptionWarning_SupportingDisableRTReplication(
        this.view,
        remoteTable,
        replicationAction.CANCELREALTIMEREPLICATION,
        warnSubscription,
        stopUser
      );
      if (
        !continueAnyway ||
        warnSubscription === issueCause.AGENTDOWN ||
        warnSubscription === issueCause.CONNECTIONDOWN
      ) {
        return;
      }
      this.cancelRun(continueAnyway);
    } else {
      const cancelRunMsg = await getResourceText(MAP_MESSAGEBUNDLE_IDS["sap.dwc.remoteTable"], "Msg_CancelRun");
      const cancelRunTitle = await getResourceText(MAP_MESSAGEBUNDLE_IDS["sap.dwc.remoteTable"], "TEXT_CancelRun");
      sap.m.MessageBox.show(cancelRunMsg, {
        icon: sap.m.MessageBox.Icon.WARNING,
        title: cancelRunTitle,
        actions: [sap.m.MessageBox.Action.OK, sap.m.MessageBox.Action.CANCEL],
        onClose: async (action: sap.m.MessageBox.Action) => {
          if (action === sap.m.MessageBox.Action.OK) {
            this.cancelRun();
          }
        },
      });
    }
  }

  private cancelRun(continueAnyway?: boolean) {
    this.getView().setBusy(true);
    const noTaskWithRunningStateMsg = this.getBundle().getText("noTaskWithRunningState");
    const actionInfoTitle = this.getBundle().getText("actionInfo");
    const otaskLogModelData = (this.getView().getModel("logsModel") as sap.ui.model.json.JSONModel)?.getData();
    const latestRunningTaskDetails = otaskLogModelData.find((i) => i.status === "RUNNING");
    const latestRunningTaskLogId = latestRunningTaskDetails ? latestRunningTaskDetails.logId : null;
    let sUrlGet =
      "replication/" +
      this.spaceId +
      "/remoteTables/" +
      encodeURIComponent(this.srcObjectId) +
      "/cancel?" +
      "oldLogId=" +
      latestRunningTaskLogId +
      (continueAnyway ? "&continueAnyway=true" : "");

    let reqParam, reqBody;
    let reqType = HttpMethod.POST;
    if (this.routeName === "viewMonitorTaskLog" || this.routeName === "viewMonitorTaskLogDetails") {
      sUrlGet = "persistence/" + this.spaceId + "/persistedviews/" + encodeURIComponent(this.srcObjectId) + "/cancel";
      reqType = HttpMethod.PUT;
      if (latestRunningTaskDetails) {
        reqType = HttpMethod.POST;
        sUrlGet = "tf/directexecute";
        reqBody = {
          applicationId: ApplicationId.VIEWS,
          spaceId: this.spaceId,
          objectId: this.srcObjectId,
          parameters: { taskLogIdToCancel: latestRunningTaskLogId },
        };
        switch (latestRunningTaskDetails.activity) {
          case Activity.EXECUTE_VIEW_ANALYZER:
            reqBody.activity = Activity.CANCEL_VIEW_ANALYZER;
            break;
          case Activity.PERSIST:
            reqBody.activity = Activity.CANCEL_PERSISTENCY;
            break;
          default:
            break;
        }
      }
    }
    if (this.routeName === "taskChainMonitorLog" || this.routeName === "taskChainMonitorLogDetails") {
      const isChainCancelEnabled = sap.ui
        .getCore()
        ?.getModel("featureflags")
        .getProperty("/DWCO_TASK_FRAMEWORK_CANCEL_CHAIN");
      if (isChainCancelEnabled && latestRunningTaskDetails) {
        reqType = HttpMethod.POST;
        sUrlGet = "tf/cancelexecute";
        reqBody = {
          applicationId: ApplicationId.TASK_CHAINS,
          spaceId: this.spaceId,
          objectId: this.srcObjectId,
          activity: Activity.RUN_CHAIN,
          logId: latestRunningTaskLogId,
        };
      }
    }
    if (latestRunningTaskDetails && reqBody) {
      reqParam = {
        url: sUrlGet,
        type: reqType,
        data: JSON.stringify(reqBody),
      };
    } else {
      reqParam = {
        url: sUrlGet,
        type: reqType,
      };
    }

    if (latestRunningTaskLogId) {
      ServiceCall.request<any>(reqParam)
        .then((oResponse) => {
          this.getView().setBusy(false);
          // cancel load action was successful
          if (oResponse.status === "success") {
            MessageHandler.success(this.getBundle().getText("cancelLastPerformedAction", [this.srcObjectId]));
            this.onCompleteRefresh();
          }
        })
        .catch((err) => {
          this.getView().setBusy(false);
          if (
            err &&
            err.length > 0 &&
            err[0].responseJSON &&
            err[0].responseJSON.internalDetails &&
            err[0].responseJSON.internalDetails.code === "taskAlreadyRunning"
          ) {
            MessageHandler.exception({
              message: this.getBundle().getText("Task_Already_Running", [this.srcObjectId]),
              exception: err,
            });
          } else if (err && err.length > 0 && err[0].responseJSON?.errorCode === "errorUnexpectedReplicationStatus") {
            MessageHandler.exception({
              message: this.getBundle().getText("errorUnexpectedReplicationStatus", [this.srcObjectId]),
              exception: err,
            });
          } else {
            MessageHandler.exception({
              exception: err,
              message: this.getBundle().getText("errorCancelLastPerformedAction", [this.srcObjectId]),
            });
          }
          this.onCompleteRefresh();
        });
    } else {
      this.getView().setBusy(false);
      sap.m.MessageBox.show(noTaskWithRunningStateMsg, {
        icon: sap.m.MessageBox.Icon.INFORMATION,
        title: actionInfoTitle,
        actions: [sap.m.MessageBox.Action.OK],
        emphasizedAction: sap.m.MessageBox.Action.OK,
      });
    }
  }

  private executeTaskChain() {
    const runPayload = {
      objectId: this.srcObjectId,
      activity: "RUN_CHAIN",
      applicationId: "TASK_CHAINS",
      spaceId: this.spaceId,
    };
    this.setScreenBusy(true);
    const url = `./tf/${this.spaceId}/taskchains/${this.srcObjectId}/start`;
    ServiceCall.post(url, { contentType: ContentType.APPLICATION_JSON }, true, JSON.stringify(runPayload)).then(
      () => {
        const toastMessage = this.getBundle().getText("taskExecuteSuccessMsg", [this.srcObjectId]);
        this.setScreenBusy(false);
        this.onCompleteRefresh();
        sap.m.MessageToast.show(toastMessage);
      },
      (error) => {
        const errorResp = error[0].responseJSON;
        this.setScreenBusy(false);
        const toastMessage = errorResp
          ? this.getBundle().getText(errorResp.code)
          : this.getBundle().getText("msgTaskExecuteFail");
        MessageHandler.exception({ exception: error, message: toastMessage, id: "executeFailedErrorMsgbox" });
      }
    );
  }

  private retryRecentRun() {
    const url = `./tf/${this.spaceId}/taskchains/${encodeURIComponent(this.srcObjectId)}/retry`;
    this.setScreenBusy(true);
    ServiceCall.request({
      url: url,
      type: HttpMethod.POST,
      dataType: DataType.TEXT,
    }).then(
      () => {
        const toastMessage = this.getBundle().getText("msgTaskRetryExecuteSuccess");
        this.setScreenBusy(false);
        this.onCompleteRefresh();
        sap.m.MessageToast.show(toastMessage);
      },
      (error) => {
        const errorCode = error[0] && error[0].responseText ? JSON.parse(error[0].responseText) : null;
        let errorTxt;
        switch (errorCode.code) {
          case "initiateChainRetry.noFailedChildTask":
            errorTxt = "noFailedChildTaskErrorTxt";
            break;
          case "initiateChainRetry.nestedChain":
            errorTxt = "nestedChainErrorTxt";
            break;
          case "initiateChainRetry.undefinedStatus":
            errorTxt = "undefinedStatusErrorTxt";
            break;
          default:
            errorTxt = "msgTaskRetryExecuteFail";
        }
        this.setScreenBusy(false);
        const toastMessage = this.getBundle().getText(errorTxt);
        MessageHandler.exception({ exception: error, message: toastMessage, id: "executeFailedErrorMsgbox" });
      }
    );
  }

  private getCurrentMonitorId() {
    let appId: ApplicationId;
    switch (this.routeName) {
      case "remoteTableTaskLog":
      case "remoteTableTaskLogDetails":
        appId = ApplicationId.REMOTE_TABLES;
        break;
      case "viewMonitorTaskLog":
      case "viewMonitorTaskLogDetails":
        appId = ApplicationId.VIEWS;
        break;
      case "taskChainMonitorLog":
      case "taskChainMonitorLogDetails":
        appId = ApplicationId.TASK_CHAINS;
        break;
    }
    return appId;
  }

  private processPartition(event, action: string) {
    let recordActionfeature = "remoteTableMonitor";
    const appId = this.getCurrentMonitorId();
    const dataAccess = (this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel).getProperty("/dataAccess");

    if (appId === ApplicationId.VIEWS) {
      recordActionfeature = "persistedViews";
    }
    // record action for usage tracking
    ShellContainer.get()
      .getUsageCollectionService()
      .recordAction({
        action: action + " Partition",
        feature: recordActionfeature,
        eventtype: "click",
      });
    this.view.setBusy(true);
    this.partitionDialog = this.getPartitionDialog();
    if (action === "Delete") {
      this.partitionDialog.deletePartition(this.srcObjectId, appId, this.spaceId, this.view);
      this.rangeSelected = false;
    } else {
      this.view.getModel("partitionSettingsModel").setProperty("/isLoadDataVisible", false);
      const title = action + " Partition";
      this.partitionDialog.processPartitioningDetails(
        this.srcObjectId,
        appId,
        this.spaceId,
        title,
        this.view,
        dataAccess
      );
    }
  }

  private getPartitionDialog() {
    if (!this.partitionView) {
      this.partitionView = sap.ui.view({
        id: this.getView().getId() + "--partitionDialog",
        type: sap.ui.core.mvc.ViewType.XML,
        viewName: require("../../monitorUtil/view/PartitionAdvisorDialog.view.xml"),
      });
    }
    if (!this["partitionViewRemoteTable"]) {
      this["partitionViewRemoteTable"] = sap.ui.view({
        id: this.getView().getId() + "--remote" + "--partitionDialog",
        type: sap.ui.core.mvc.ViewType.XML,
        viewName: require("../../monitorUtil/view/PartitionDialog.view.xml"),
      });
    }
    this.partitionDialog = this.isViewMonitorRoute
      ? (this.partitionView.getController() as PartitionDialog)
      : (this["partitionViewRemoteTable"].getController() as PartitionDialog);
    return this.partitionDialog;
  }

  private async openPartitionData(oEvent) {
    this.view.setBusy(true);
    const appId = this.getCurrentMonitorId();
    const popOverFragment = require("../../monitorUtil/view/PartitionData.fragment.xml");
    const link = oEvent.getSource();
    this.partitionDialog = this.getPartitionDialog();
    const rawData = (await this.partitionDialog.getPartitionDetails(this.srcObjectId, appId, this.spaceId)) as any;
    let lockedRanges = [];
    if (rawData.ranges) {
      lockedRanges = rawData.ranges.filter((elem) => elem.locked === true);
    }
    const data = {
      column: rawData.column,
      noofPartition: rawData.ranges ? rawData.ranges.length : 0,
      noofParallel: rawData.numParallelPartitions,
      noofLockedPartition: lockedRanges.length,
    };
    const partitionModel = new sap.ui.model.json.JSONModel(data);
    this.view.setModel(partitionModel, "partitionModel");
    partitionModel.setProperty("/monitorId", appId);
    this.view.setBusy(false);
    if (!this.partitionPopover) {
      this.partitionPopover = sap.ui.xmlfragment(this.getView().getId(), popOverFragment, this) as sap.m.Popover;
      this.getView().addDependent(this.partitionPopover);
      this.partitionPopover.openBy(link, true);
    } else {
      this.partitionPopover.openBy(link, true);
    }
  }

  private partitionTextFormatter(id: string) {
    return "Partition " + id;
  }

  public dataTypeIconFormatter(column) {
    const columnDetails = this.view.getModel().getProperty("/partitioningColumns")[column];
    return CDS_TO_ICON[columnDetails.validationType];
  }

  private partitionAvailableVisibility = formatterJS.partitionAvailableVisibility;
  private retryVisibilityformatter = formatterJS.retryVisibilityformatter;
  private partitionVisibilityFormatter = formatterJS.partitionVisibilityFormatter;
  private createStatisticsVisibilityFormatter = formatterJS.createStatisticsVisibilityFormatter;
  private deleteStatisticsVisibilityFormatter = formatterJS.deleteStatisticsVisibilityFormatter;
  private executeTaskChainMenuFormatter = formatterJS.executeTaskChainMenuFormatter;
  private executeRetryLatestRunFormatter = formatterJS.executeRetryLatestRunFormatter;
  private executeRetryLatestRunMenuFormatter = formatterJS.executeRetryLatestRunMenuFormatter;
  private runDetailsCancelRunVisibilityformatter = formatterJS.runDetailsCancelRunVisibilityformatter;
  private runDetailsCancelRunEnableformatter = formatterJS.runDetailsCancelRunEnableformatter;
  private navToTableFormatter = formatterJS.navToTableFormatter;

  private async navigateToConnection() {
    const target: any = {
      semanticObject: "connections",
      action: "",
    };
    const params: any = {
      spaceId: this.spaceId,
    };
    ShellNavigationService.toExternal({ target, params });
  }

  private getDataAccessText(sDataAccess: string) {
    if (this.routeName === "remoteTableTaskLog" || this.routeName === "remoteTableTaskLogDetails") {
      return this.formatter.getDataAccessText(sDataAccess);
    } else if (
      this.routeName === "remoteTableStatisticsMonitorLog" ||
      this.routeName === "remoteTableStatisticsMonitorLogDetails"
    ) {
      return logFormatterUtils.getDataAccessText(sDataAccess);
    } else {
      return sDataAccess;
    }
  }

  private getTextRepStatusRealTime(sReplicationStatus: string, sReplicationType: string, sTaskState: string) {
    if (this.routeName === "remoteTableTaskLog" || this.routeName === "remoteTableTaskLogDetails") {
      return this.formatter.getTextRepStatusRealTime(sReplicationStatus, sReplicationType, sTaskState);
    } else if (this.routeName === "viewMonitorTaskLog" || this.routeName === "viewMonitorTaskLogDetails") {
      return this.formatter.getTextViewPersistenceStatus(sReplicationStatus, sReplicationType, sTaskState);
    } else if (this.routeName === "taskChainMonitorLog" || this.routeName === "taskChainMonitorLogDetails") {
      const recentLog = this.view.getModel("logsModel").getData();
      return logFormatterUtils.statusTextFormatter(recentLog[0]?.status, recentLog[0]?.subStatus);
    }
  }

  private getActiveStatus(sReplicationStatus: string, sReplicationType: string, sTaskState: string) {
    if (this.routeName === "remoteTableTaskLog" || this.routeName === "remoteTableTaskLogDetails") {
      if (this.formatter.getTextRepStatusRealTime(sReplicationStatus, sReplicationType, sTaskState) === "Error") {
        return true;
      }
      return false;
    } else {
      return false;
    }
  }

  private getSemanticColorValue(sStatusBackend: string, sTaskState: string) {
    if (this.routeName === "remoteTableTaskLog" || this.routeName === "remoteTableTaskLogDetails") {
      return this.formatter.getSemanticColorValue(sStatusBackend, sTaskState);
    } else if (this.routeName === "viewMonitorTaskLog" || this.routeName === "viewMonitorTaskLogDetails") {
      return this.formatter._getSemanticColorValue(sStatusBackend, sTaskState);
    } else if (this.routeName === "taskChainMonitorLog" || this.routeName === "taskChainMonitorLogDetails") {
      const recentLog = this.view.getModel("logsModel").getData();
      return this.formatter.statusColorFormatter(recentLog[0]?.status);
    }
  }

  private getTextRefreshFrequency(sReplicationStatus: string, sReplicationType: string, isScheduled: boolean) {
    if (this.routeName === "remoteTableTaskLog" || this.routeName === "remoteTableTaskLogDetails") {
      return this.formatter.getTextRefreshFrequency(sReplicationStatus, sReplicationType, isScheduled);
    } else if (this.routeName === "viewMonitorTaskLog" || this.routeName === "viewMonitorTaskLogDetails") {
      return this.formatter.getTextRefreshFrequency(sReplicationStatus, isScheduled);
    }
  }

  private handleErrorPopover(oEvent) {
    const src = oEvent.getSource();
    const remoteTableSelected = src.getModel("detailsModel").getData();
    if (
      [ReplicationStatus.ERROR, ReplicationStatus.INITIALIZING].includes(remoteTableSelected.replicationStatus) &&
      remoteTableSelected.errorDetails
    ) {
      const errorFragment = this.formatter.getPopover();
      const errorDetailsPopover = sap.ui.xmlfragment("", errorFragment, this) as sap.m.Popover;
      errorDetailsPopover.setModel(this.formatter.getModelForPopOver(remoteTableSelected, true));
      this.getView().addDependent(errorDetailsPopover);
      errorDetailsPopover.openBy(src, true);
    }
  }

  private getTextRepStatusRealTimeTooltip(
    sReplicationStatus: string,
    sReplicationType: string,
    sTaskState: string,
    sReplicationError: string
  ) {
    if (this.routeName === "remoteTableTaskLog" || this.routeName === "remoteTableTaskLogDetails") {
      return this.formatter.getTextRepStatusRealTimeTooltip(
        sReplicationStatus,
        sReplicationType,
        sTaskState,
        sReplicationError
      );
    } else if (this.routeName === "viewMonitorTaskLog" || this.routeName === "viewMonitorTaskLogDetails") {
      return this.formatter.getViewPersistenceStatusTooltip(
        sReplicationStatus,
        sReplicationType,
        sTaskState,
        sReplicationError
      );
    } else if (this.routeName === "taskChainMonitorLog" || this.routeName === "taskChainMonitorLogDetails") {
    }
  }
  private replicationChangeStripVisibilityFormatter(scheduled, isRefreshFrequencyActive, isUsedInChains, dataAccess) {
    if (this.routeName === "remoteTableTaskLog" || this.routeName === "remoteTableTaskLogDetails") {
      if (
        dataAccess === dataAccessType.REALTIME_REPLICATION &&
        ((scheduled && isRefreshFrequencyActive) || isUsedInChains)
      ) {
        return true;
      }
    }
    return false;
  }

  private logInformationVisibilityFormatter(
    repStatus: string,
    dataAccess: string,
    location: string,
    scheduled: string,
    isRefreshFrequencyActive: boolean,
    usedInTaskChain: boolean
  ) {
    if (this.routeName === "remoteTableTaskLog" || this.routeName === "remoteTableTaskLogDetails") {
      if (
        dataAccess === dataAccessType.REALTIME_REPLICATION &&
        ((scheduled && isRefreshFrequencyActive) || usedInTaskChain)
      ) {
        return false;
      } else {
        return (
          dataAccess === dataAccessType.REALTIME_REPLICATION &&
          (repStatus === ReplicationStatus.ACTIVE || repStatus === ReplicationStatus.PAUSED) &&
          !(location === AdaptionLocationType.DataIntelligence)
        );
      }
    }
    return false;
  }

  // TODO: Refactor this function as part of DWC_DUMMY_SPACE_PERMISSIONS FF Removal and remove hasDatabuilderReadPrivilege, isSDPEnabled from params and xml file
  private openInEditorBtnVisibility(hasDatabuilderReadPrivilege, isSDPEnabled, canReadDataBuilder, viewAnalyzer2_FF) {
    if (viewAnalyzer2_FF) {
      return false;
    }
    this.openInEditorCheck(hasDatabuilderReadPrivilege, isSDPEnabled, canReadDataBuilder);
  }

  private openInEditorCheck(hasDatabuilderReadPrivilege, isSDPEnabled, canReadDataBuilder) {
    if (isSDPEnabled) {
      return (
        canReadDataBuilder &&
        [
          "remoteTableTaskLog",
          "remoteTableTaskLogDetails",
          "viewMonitorTaskLog",
          "viewMonitorTaskLogDetails",
          "taskChainMonitorLog",
          "taskChainMonitorLogDetails",
          "remoteTableStatisticsMonitorLog",
          "remoteTableStatisticsMonitorLogDetails",
        ].includes(this.routeName)
      );
    } else {
      return (
        hasDatabuilderReadPrivilege &&
        [
          "remoteTableTaskLog",
          "remoteTableTaskLogDetails",
          "viewMonitorTaskLog",
          "viewMonitorTaskLogDetails",
          "taskChainMonitorLog",
          "taskChainMonitorLogDetails",
          "remoteTableStatisticsMonitorLog",
          "remoteTableStatisticsMonitorLogDetails",
        ].includes(this.routeName)
      );
    }
  }

  private onOpenInEditorPress(): void {
    // record action for usage tracking
    ShellContainer.get().getUsageCollectionService().recordAction({
      action: "onOpenInEditorPress",
      feature: "monitorTaskLog",
      eventtype: "click",
    });

    const params = {
      spaceId: this.spaceId,
      model: this.srcObjectId,
    };
    ShellNavigationService.toExternal({
      target: {
        semanticObject: "databuilder",
      },
      params,
    });
  }

  private onProcessError() {
    const oDetailsModel = this.view.getModel("detailsModel") as sap.ui.model.json.JSONModel;
    this.serviceUtil.retryRealTimeReplication(oDetailsModel.getData(), this.view);
  }

  private formatNextSchedule(nextSchedule: string, isScheduled: boolean) {
    if (this.formatter) {
      return this.formatter.formatNextSchedule(nextSchedule, isScheduled, this.srcObjectId, this.scheduleList);
    }
    return this.formatDateTime(nextSchedule);
  }

  private sharedReplicaMessageText(hasReplica, hasSourceSharing, hasSourceSharingWithReplica) {
    if (this.routeName === "remoteTableTaskLog" || this.routeName === "remoteTableTaskLogDetails") {
      return this.formatter.sharedReplicaMessageText(hasReplica, hasSourceSharing, hasSourceSharingWithReplica);
    }
  }

  private sharedReplicaMessageType(hasReplica, hasSourceSharing, hasSourceSharingWithReplica) {
    if (this.routeName === "remoteTableTaskLog" || this.routeName === "remoteTableTaskLogDetails") {
      return this.formatter.sharedReplicaMessageType(hasReplica, hasSourceSharing, hasSourceSharingWithReplica);
    }
  }

  private sharedReplicaMenuEnablementFormatter(enableFlag, detailsModel) {
    if (this.routeName === "remoteTableTaskLog" || this.routeName === "remoteTableTaskLogDetails") {
      return this.formatter.sharedReplicaMenuCheck(detailsModel) && enableFlag;
    } else {
      return enableFlag;
    }
  }

  private loadSnapshotVisibilityFormatter(enableFlag, detailsModel) {
    return (
      this.sharedReplicaMenuEnablementFormatter(enableFlag, detailsModel) &&
      !(
        detailsModel.location === AdaptionLocationType.DataIntelligence &&
        detailsModel.dataAccess === dataAccessType.REALTIME_REPLICATION &&
        detailsModel.replicationType === ReplicationType.REALTIME
      )
    );
  }

  private handleSharedTableDetailsPress() {
    const tableName = this.view.getModel("detailsModel").getProperty("/objectName");
    this.serviceUtil?.handleSharedTableDetailsPress(tableName, this.spaceId, this.view, this.formatter);
  }

  async openSchedule(oEvent: any, tableName: string): Promise<void> {
    let scheduleDialog;

    const link = oEvent.getSource();
    const appId = this.isRemoteTableRoute
      ? ApplicationId.REMOTE_TABLES
      : this.isViewMonitorRoute
      ? ApplicationId.VIEWS
      : ApplicationId.TASK_CHAINS;
    if (this["isReusableTaskScheduleFFEnabled"] && this["isAdoptionOfTaskSchedulerEnabled"]) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
      if (isDiMonitorImprovementsEnabled()) {
        const applicationId = this.getApplicationId();
        const activity = this.getActivity();
        const desc = this.getDesc();
        const dataAccess = this.getView().getModel("detailsModel").getProperty("/dataAccess");
        const data: ITaskScheduleRequest = {
          objectId: this.srcObjectId,
          applicationId: applicationId,
          activity: activity,
          description: desc,
          activationStatus: "ENABLED",
          dataAccess: dataAccess,
        };
        const isSpaceLocked = this.getView().getModel("settingsModel")?.getProperty("/isSpaceLocked");
        await openSchedulePopover(
          link,
          this.srcObjectId,
          appId,
          this.spaceId,
          scheduleDialog,
          data,
          this,
          this.onCompleteRefresh.bind(this),
          isSpaceLocked
        );
      } else {
        await openSchedulePopover(link, this.srcObjectId, appId, this.spaceId, scheduleDialog);
      }
    } else {
      scheduleDialog = (
        this.getView().byId("taskSchedulingDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
      scheduleDialog.openSchedulePopover(link, this.srcObjectId, appId, this.spaceId);
    }
  }

  createStatisticsNew() {
    const tableName = this.view.getModel("detailsModel").getProperty("/objectName");
    const statisticsType = this.view.getModel("detailsModel").getProperty("/statisticsType");
    const isParameterAbapCdsViews = sap.ui
      .getCore()
      .getModel("featureflags")
      .getProperty("/DWCO_REMOTE_TABLE_PARAMETER_SUPPORT_ABAP_CDS_VIEWS");
    let statisticsLimitedToRecordCount = false;
    if (isParameterAbapCdsViews) {
      statisticsLimitedToRecordCount = (this.view.getModel("detailsModel") as any)?.getData()
        ?.statisticsLimitedToRecordCount;
    }
    CreateStatistics(tableName, this.spaceId, this.view, statisticsType, statisticsLimitedToRecordCount);
  }

  dropStatistics() {
    const tableName = this.view.getModel("detailsModel").getProperty("/objectName");
    DeleteStatistics(tableName, this.spaceId, this.view);
  }

  private getTaskChainParentLink(spaceId?: string) {
    if (this.isECNRoute()) {
      const shellHash = "monitoring&/m";
      const targetHash = `logs/ELASTIC_COMPUTE_NODE/${this.srcObjectId}/${
        this.view.getModel("selectedModel").getData().taskChainParent
      }`;

      return `#/${shellHash}/${targetHash}`;
    } else {
      const objectId = this.view.getModel("selectedModel").getData().taskChainParent;
      return this.getNavLinkUrl(ApplicationId.TASK_CHAINS, objectId, "", spaceId);
    }
  }

  /**
   * Navigates to task chain
   *
   * @memberof TaskLogClass
   */
  public openTaskChain(oEvent: sap.ui.base.Event): void {
    oEvent.preventDefault();
    if (this.isECNRoute()) {
      const shellHash = "monitoring&/m";
      const targetHash = `logs/ELASTIC_COMPUTE_NODE/${this.srcObjectId}/${
        this.view.getModel("selectedModel").getData().taskChainParent
      }`;
      const sUrl = `#/${shellHash}/${targetHash}`;
      sap.m.URLHelper.redirect(sUrl, false);
    } else {
      const semanticObject = "dataintegration";
      const targetParameter = "routeTo";
      const spaceName = this.view.getModel("selectedModel").getData().taskChainParentSpace;
      const params: any = {
        spaceId: spaceName !== undefined ? spaceName : this.spaceId,
      };
      params.objectId = this.view.getModel("selectedModel").getData().taskChainParent;
      params[targetParameter] = "taskChainMonitorLog";
      ShellNavigationService.toExternal({
        target: {
          semanticObject: semanticObject,
          action: "",
        },
        params,
      });
    }
  }

  private getStartAnalyzerDialog() {
    const dialog = require("../../viewanalyzer/view/StartAnalyzerDialog.view.xml");
    const analyzerView = sap.ui.view({
      id: this.getView().getId() + "--startAnalyzerrDialog",
      type: sap.ui.core.mvc.ViewType.XML,
      viewName: dialog,
      viewData: {
        spaceId: this.spaceId,
      },
    });
    return analyzerView.getController() as StartAnalyzerDialog;
  }

  recordViewAnalyzerAction() {
    ShellContainer.get().getUsageCollectionService().recordAction({
      action: "startViewAnalyzer",
      feature: "viewMonitorTaskLog",
      eventtype: EventType.CLICK,
    });
  }

  public startPersistencyAdvisor() {
    this.recordViewAnalyzerAction();
    const startDialog = this.getStartAnalyzerDialog();
    startDialog.openDialog(true, this.srcObjectId, this.spaceId);
  }

  public spaceNameFormatter(spaceName) {
    const businessName = this.view.getModel("messagesModel").getProperty("/" + spaceName);
    if (businessName === undefined) {
      return spaceName;
    }
    const combinedname = businessName + " (" + spaceName + ")";
    return combinedname;
  }

  public openPartiallyPersistedLink(oEvent: any, inputParametersDetails: any, latestUpdate: any) {
    const link = oEvent.getSource();
    openPartiallyPersistedInfo(link, inputParametersDetails, latestUpdate, this);
  }

  private resetPrivilegeModel() {
    const privilegeModel = this.view.getModel("privilegeModel") as sap.ui.model.json.JSONModel;
    privilegeModel.setProperty("/isRemoteTable", false);
    privilegeModel.setProperty("/isStatisticsRoute", false);
    privilegeModel.setProperty("/isViewMonitor", false);
    privilegeModel.setProperty("/isTaskChainMonitor", false);
  }

  public getScheduledText(formattedRefreshFrequency, schedule) {
    if (isDiMonitorImprovementsEnabled() && schedule?.activationStatus === "ENABLED" && schedule !== undefined) {
      if (schedule) {
        return schedule?.cron !== undefined
          ? schedule?.cron
          : this.getText("everyLabel") +
              " " +
              schedule?.frequency?.interval +
              " " +
              this.getFrequencyText(schedule?.frequency?.type);
      } else {
        return formattedRefreshFrequency;
      }
    } else {
      return formattedRefreshFrequency;
    }
  }

  public getFrequencyText(type) {
    switch (type) {
      case "MINUTES":
        return this.getText("minutesLabel");
      case "HOURLY":
        return this.getText("hoursLabel");
      case "DAILY":
        return this.getText("daysLabel");
      case "WEEKLY":
        return this.getText("weeksLabel");
      case "MONTHLY":
        return this.getText("monthsLabel");
      default:
        return "";
    }
  }
}

export const TaskLogs = smartExtend(TaskLogBase, "sap.cdw.components.tasklog.controller.TaskLog", TaskLogClass);

sap.ui.define("sap/cdw/components/tasklog/controller/TaskLog.controller", [], function () {
  return TaskLogs;
});
