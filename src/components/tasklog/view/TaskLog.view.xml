<mvc:View
  height="100%"
  controllerName="sap.cdw.components.tasklog.controller.TaskLog"
  xmlns="sap.m"
  xmlns:uxap="sap.uxap"
  xmlns:mvc="sap.ui.core.mvc"
  xmlns:layout="sap.ui.layout"
  xmlns:core="sap.ui.core"
  xmlns:t="sap.ui.table"
  xmlns:f="sap.f"
  xmlns:form="sap.ui.layout.form"
  xmlns:unified="sap.ui.unified"
  xmlns:ac="sap.cdw.components.reuse.control.actionchecker"
  xmlns:cd="http://schemas.sap.com/sapui5/extension/sap.ui.core.CustomData/1"
>
  <ac:ActionChecker
    id="tasklogDetailsAc"
    class="sapUiContentPadding"
    hanaState="{path:'circuitbreaker>/DataHANA', formatter:'.hanaStateFormatter'}"
    hanaProvisioningState="{path:'circuitbreaker>/DataHANAProvisioningState', formatter:'.hanaStateFormatter'}"
    spaceLocked="{= ${settingsModel>/isSpaceLocked} === true }"
    actionControlIds="refeshTable,EditPartitionButton,DeletePartitionButton,exeModeGrp,DefinePartition,taskschedulingAuth"
    hiddenMode="false"
  >
  </ac:ActionChecker>
  <Page
    showHeader="false"
    id="pageContainer"
  >
    <content>
      <f:FlexibleColumnLayout
        backgroundDesign="Solid"
        layout="{routerModel>/layout}"
        id="flexDetailsPage"
      >
        <f:beginColumnPages id="MasterPane">
          <Page
            id="masterLogTable"
            backgroundDesign="List"
            showHeader="false"
            enableScrolling="false"
          >
            <uxap:ObjectPageLayout
              id="masterLogTableOPL"
              showTitleInHeaderContent="true"
              headerContentPinnable="false"
              useIconTabBar="true"
              upperCaseAnchorBar="false"
              enableLazyLoading="true"
            >
              <uxap:headerTitle>
                <uxap:ObjectPageDynamicHeaderTitle class="taskLogMasterTitle sapUiResponsivePadding--header">
                  <uxap:expandedHeading>
                    <Title
                      text="{parts: [{path: 'selectedModel>/objectId'}]}"
                      tooltip="{parts: [{path: 'selectedModel>/objectId'}]}"
                    />
                  </uxap:expandedHeading>
                  <uxap:snappedHeading>
                    <Title
                      text="{parts: [{path: 'selectedModel>/objectId'}]}"
                      tooltip="{parts: [{path: 'selectedModel>/objectId'}]}"
                    />
                  </uxap:snappedHeading>
                </uxap:ObjectPageDynamicHeaderTitle>
              </uxap:headerTitle>
              <uxap:sections>
                <uxap:ObjectPageSection
                  showTitle="{= ${settingsModel>/isExeModeVisible}}"
                  titleUppercase="false"
                  title="{i18n>Logs}"
                >
                  <uxap:subSections>
                    <uxap:ObjectPageSubSection
                      showTitle="false"
                      mode="Expanded"
                    >
                      <uxap:blocks>
                        <VBox visible="{= !${settingsModel>/logsExists}}">
                          <IllustratedMessage
                            id="norunsMsg"
                            title="{i18n>runsEmptyTitle}"
                            description="{i18n>runsEmptyDescText}"
                            illustrationType="sapIllus-NoActivities"
                          >
                          </IllustratedMessage>
                        </VBox>
                        <Table
                          growing="true"
                          inset="false"
                          id="taskLogTable"
                          selectionChange="onSelect"
                          mode="SingleSelectMaster"
                          includeItemInSelection="true"
                          fixedLayout="false"
                          items="{
                            path: 'logsModel>/',
                            templateShareable: false,
                            sorter: {
                            path: 'startTime',
                            descending: true
                            }
                          }"
                          visible="{= ${settingsModel>/logsExists}}"
                          sticky="HeaderToolbar,InfoToolbar,ColumnHeaders"
                          class="sapUiTinyMarginBottom noColumnBorder"
                        >
                          <headerToolbar>
                            <OverflowToolbar>
                              <content>
                                <Title
                                  id="titleLogList"
                                  text="{logsModel>/tableHeader}"
                                />
                                <ToolbarSpacer />
                                <SearchField
                                  width="40%"
                                  id="taskLogsSearch"
                                  visible="{= ${routerModel>/routeName} === 'importLogs' || ${routerModel>/routeName} === 'logDetails'}"
                                  liveChange="onMessageSearch($event, ${viewSettingsDialogModel>/tableId/LOGS}, 'objectId', ${viewSettingsDialogModel>/dialogLayout/LOGS_SORT}, ${viewSettingsDialogModel>/dialogLayout/LOGS_FILTER})"
                                />
                                <Button
                                  id="logsSortSettingButton"
                                  tooltip="{i18n>sortTxt}"
                                  icon="sap-icon://sort"
                                  press="handleSortButtonPressed($event, ${viewSettingsDialogModel>/dialogLayout/LOGS_SORT})"
                                  visible="{= ${routerModel>/routeName} !== 'importLogs' &amp;&amp; ${routerModel>/routeName} !== 'logDetails'}"
                                />
                                <Button
                                  id="bbLogsSortSettingButton"
                                  tooltip="{i18n>sortTxt}"
                                  icon="sap-icon://sort"
                                  press="handleSortButtonPressed($event, ${viewSettingsDialogModel>/dialogLayout/BB_LOGS_SORT})"
                                  visible="{= ${routerModel>/routeName} === 'importLogs' || ${routerModel>/routeName} === 'logDetails'}"
                                />
                                <Button
                                  id="logsFilterSettingButton"
                                  tooltip="{i18n>filterTxt}"
                                  icon="sap-icon://filter"
                                  press="handleFilterButtonPressed($event, ${viewSettingsDialogModel>/dialogLayout/LOGS_FILTER})"
                                />
                                <Button
                                  id="refeshTable"
                                  type="Transparent"
                                  icon="sap-icon://refresh"
                                  tooltip="{i18n>TEXT_REFRESH}"
                                  press="onRefresh"
                                  visible="{= ${routerModel>/routeName} === 'importLogs' || ${routerModel>/routeName} === 'logDetails' || ${routerModel>/routeName} === 'ecntaskchainmonitoring' || ${routerModel>/routeName} === 'ecnlogsmonitoring' || ${routerModel>/routeName} === 'logsmonitoring'}"
                                  cd:actionId="monitoring/editor/refresh"
                                />
                              </content>
                            </OverflowToolbar>
                          </headerToolbar>
                          <infoToolbar>
                            <OverflowToolbar
                              id="taskLogTable-FilterBar"
                              visible="false"
                            >
                              <Text text="{viewSettingsDialogModel>/taskLogTableFilterBarText}" />
                            </OverflowToolbar>
                          </infoToolbar>
                          <columns>
                            <Column id="idLogStarttime">
                              <Label
                                text="{i18n>RUN_START_DETAILS}"
                                tooltip="{i18n>RUN_START_DETAILS}"
                              />

                            </Column>
                            <Column
                              id="idLogRuntime"
                              width="auto"
                            >
                              <Label
                                text="{i18n>Runtime}"
                                tooltip="{i18n>RuntimeTooltip}"
                              />
                            </Column>
                            <Column
                              id="idLogStatus"
                              width="auto"
                            >
                              <Label
                                text="{i18n>STATUS}"
                                tooltip="{i18n>STATUS}"
                              />
                            </Column>
                            <Column
                              id="idObject"
                              minScreenWidth="Tablet"
                              demandPopin="true"
                              width="auto"
                              visible="{= ${routerModel>/routeName} === 'importLogs' || ${routerModel>/routeName} === 'logDetails'}"
                            >
                              <Label
                                text="{i18n>Object}"
                                tooltip="{i18n>Object}"
                              />
                            </Column>
                            <Column
                              id="idEcnActivity"
                              minScreenWidth="Tablet"
                              demandPopin="true"
                              width="auto"
                              visible="{= (${routerModel>/routeName} === 'ecntaskchainmonitoring' || ${routerModel>/routeName} === 'ecnlogsmonitoring') || ${routerModel>/routeName} === 'logsmonitoring' || ((${privilegeModel>/isRemoteTable} === true || ${privilegeModel>/isViewMonitor} === true) || (${featureflags>/DWCO_TASK_FRAMEWORK_CANCEL_CHAIN} === true &amp;&amp; ${privilegeModel>/isTaskChainMonitor === true}))}"
                            >
                              <Label
                                text="{i18n>TASK_ACTIVITY}"
                                tooltip="{i18n>TASK_ACTIVITY}"
                              />
                            </Column>
                          </columns>
                          <items>
                            <ColumnListItem
                              vAlign="Middle"
                              type="Navigation"
                            >
                              <cells>
                                <Text
                                  wrapping="false"
                                  text="{logsModel>formattedStartTime}"
                                  tooltip="{logsModel>formattedStartTime}"
                                ></Text>
                                <ObjectStatus
                                  text="{logsModel>formattedRuntime}"
                                  tooltip="{logsModel>formattedRuntime}"
                                ></ObjectStatus>
                                <HBox>
                                  <ObjectStatus
                                    text="{logsModel>formattedStatus}"
                                    tooltip="{logsModel>formattedStatus}"
                                    visible="{ parts: [{ path: 'logsModel>status'},{ path: 'logsModel>subStatus'}], formatter:'.runStatusVisibilityFormatter' }"
                                    state="{ parts: [{ path: 'logsModel>status'}], formatter:'.statusTextStateFormatter' }"
                                  >
                                  </ObjectStatus>
                                  <Link
                                    text="{ parts: [{ path: 'logsModel>status'}, { path: 'logsModel>subStatus'}], formatter:'.statusTextFormatter' }"
                                    visible="{ parts: [{ path: 'logsModel>status'},{ path: 'logsModel>subStatus'}], formatter:'.subStatusLinkVisibilityFormatter' }"
                                    class="valueStateError"
                                    press="subStatusClickHandler($event, ${logsModel>subStatus})"
                                  />
                                </HBox>
                                <Text
                                  wrapping="false"
                                  text="{logsModel>objectId}"
                                  tooltip="{logsModel>objectId}"
                                ></Text>
                                <Text
                                  wrapping="false"
                                  text="{ parts: [{ path: 'logsModel>activity'}], formatter:'.activityTextFormatter' }"
                                  tooltip="{ parts: [{ path: 'logsModel>activity'}], formatter:'.activityTextFormatter' }"
                                ></Text>
                              </cells>
                            </ColumnListItem>
                          </items>
                        </Table>


                      </uxap:blocks>
                    </uxap:ObjectPageSubSection>
                  </uxap:subSections>
                </uxap:ObjectPageSection>
                <uxap:ObjectPageSection
                  title="{i18n>PARTITIONS}"
                  titleUppercase="false"
                  showTitle="false"
                  id="partitions"
                  visible="{= ((${settingsModel>/partitioningExists}) &amp;&amp; (${privilege>/DWC_DATABUILDER/read} === true)) || (${featureflags>/DWC_DUMMY_SPACE_PERMISSIONS} ? ${settingsModel>/partitionSection} &amp;&amp; ${privilege>/DWC_DATAINTEGRATION/update} &amp;&amp; ${privilegeModel>/canCreateOrUpdateMonitoringUI} &amp;&amp; ${privilege>/DWC_DATABUILDER/read} === true : ${settingsModel>/partitionSection} &amp;&amp; ${privilegeModel>/update} === true  &amp;&amp; ${privilege>/DWC_DATABUILDER/read} === true) }"
                >
                  <uxap:subSections>
                    <uxap:ObjectPageSubSection>
                      <uxap:blocks>
                        <VBox>
                          <IllustratedMessage
                            title="{i18n>partitionEmptyTitle}"
                            description="{i18n>partitionEmptyDescText}"
                            illustrationType="sapIllus-NoActivities"
                          >
                            <additionalContent>
                              <Button
                                id="DefinePartition"
                                text="{i18n>DefinePartition}"
                                press=".processPartition($event, 'Create')"
                                cd:actionId="monitoring/editor/partition"
                                enabled="{= !${detailsModel>/federationOnly} }"
                              />
                            </additionalContent>
                          </IllustratedMessage>
                        </VBox>
                        <VBox>
                          <OverflowToolbar
                            design="Transparent"
                            height="3rem"
                          >
                            <ToolbarSpacer />
                            <Button
                              id="EditPartitionButton"
                              text="{i18n>Edit}"
                              tooltip="{i18n>editPartitionLabel}"
                              press=".processPartition($event, 'Edit')"
                              cd:actionId="monitoring/editor/partition"
                              visible="{= ${privilege>/DWC_DATABUILDER/read} === true &amp;&amp; ${privilege>/DWC_DATAINTEGRATION/update} &amp;&amp; ${settingsModel>/isSpaceLocked} !== true}"
                            >
                              <layoutData>
                                <OverflowToolbarLayoutData priority="Low" />
                              </layoutData>
                            </Button>
                            <Button
                              id="DeletePartitionButton"
                              tooltip="{i18n>deletePartitionLabel}"
                              text="{i18n>Delete}"
                              press=".processPartition($event, 'Delete')"
                              cd:actionId="monitoring/editor/partition"
                              visible="{= ${privilege>/DWC_DATABUILDER/read} === true &amp;&amp; ${privilege>/DWC_DATAINTEGRATION/update} &amp;&amp; ${settingsModel>/isSpaceLocked} !== true}"
                            >
                              <layoutData>
                                <OverflowToolbarLayoutData priority="Low" />
                              </layoutData>
                            </Button>
                          </OverflowToolbar>
                          <Panel>
                            <core:Fragment
                              fragmentName="sap.cdw.components.monitorUtil.view.PartitionSection"
                              type="XML"
                            />
                          </Panel>
                        </VBox>
                      </uxap:blocks>
                    </uxap:ObjectPageSubSection>
                  </uxap:subSections>
                </uxap:ObjectPageSection>
                <uxap:ObjectPageSection
                  id="settings"
                  titleUppercase="false"
                  title="{i18n>Settings}"
                  visible="{= ${settingsModel>/isExeModeVisible} === true &amp;&amp; (${featureflags>/DWC_DUMMY_SPACE_PERMISSIONS} ? ${privilege>/DWC_DATAINTEGRATION/update} : ${privilegeModel>/update} === true)}"
                >
                  <uxap:subSections>
                    <uxap:ObjectPageSubSection
                      titleUppercase="false"
                      title="{i18n>Settings}"
                      mode="Expanded"
                    >
                      <uxap:blocks>
                        <VBox>
                        <form:SimpleForm
                          id="modePanel"
                          layout="ResponsiveGridLayout"
                          labelSpanXL="3"
                          labelSpanL="3"
                          labelSpanM="2"
                          labelSpanS="12"
                          adjustLabelSpan="true"
                          emptySpanXL="4"
                          emptySpanL="4"
                          emptySpanM="4"
                          emptySpanS="0"
                          columnsXL="1"
                          columnsL="1"
                          columnsM="1"
                        >
                          <form:content>
                            <Label text="{i18n>ExecutionMode}" />
                            <RadioButtonGroup
                              id="exeModeGrp"
                              valueState="None"
                              selectedIndex="{detailsModel>/exeMode}"
                              select="onModeChange"
                              cd:actionId="monitoring/editor/settings"
                            >
                              <RadioButton
                                id="std"
                                text="{i18n>Standard_PO}"
                              />
                              <RadioButton
                                id="hlpm"
                                text="{i18n>HLMP_MO}"
                              />
                            </RadioButtonGroup>
                          </form:content>
                        </form:SimpleForm>
                        </VBox>
                      </uxap:blocks>
                    </uxap:ObjectPageSubSection>
                  </uxap:subSections>
                </uxap:ObjectPageSection>
              </uxap:sections>
            </uxap:ObjectPageLayout>
          </Page>
        </f:beginColumnPages>
        <f:midColumnPages id="DetailsPane">
          <uxap:ObjectPageLayout
            id="detailLogTable"
            showTitleInHeaderContent="true"
            headerContentPinnable="false"
            isChildPage="false"
            upperCaseAnchorBar="false"
            useIconTabBar="true"
            sectionChange="onMidColumnSectionChange"
          >
            <uxap:headerTitle>
              <uxap:ObjectPageDynamicHeaderTitle>
                <uxap:expandedHeading>
                  <Title
                    text="{i18n>runDetails}"
                    wrapping="true"
                    class="sapUiSmallMarginEnd"
                  />
                </uxap:expandedHeading>
                <uxap:snappedHeading>
                  <Title
                    text="{i18n>runDetails}"
                    wrapping="true"
                    class="sapUiSmallMarginEnd"
                  />
                </uxap:snappedHeading>
                <uxap:actions>
                  <Button
                    id="retryRunButton"
                    text="{i18n>retryRun}"
                    type="Transparent"
                    visible="{
                  parts: [
                  {path:'routerModel>/routeName'}, {path: 'privilegeModel>/update'}, {path: 'detailsModel>/status'}, {path: 'detailsModel>/subStatus'},{path: 'selectedModel>/logId'}, {path: 'detailsModel>/latestLogId'},{path: 'featureflags>/DWC_DUMMY_SPACE_PERMISSIONS'}, {path: 'privilege>/DWC_DATAINTEGRATION/update'}, {path: 'privilegeModel>/canCreateOrUpdateMonitoringUI'}
                  ],
                  formatter : '.executeRetryLatestRunFormatter'
                }"
                    press="retryRecentRun"
                  />
                  <Button
                    id="cancelLoad"
                    text="{i18n>TEXT_CancelRun}"
                    tooltip="{i18n>TEXT_CancelRun}"
                    type="Transparent"
                    press="onCancelRun"
                    enabled="{
                        parts: [
                        {path:'logsModel>/'},
                        {path:'routerModel>/routeName'}
                        ],
                        formatter : '.runDetailsCancelRunEnableformatter'
                      }"
                    visible="{
                        parts: [
                        {path:'routerModel>/routeName'},
                        {path: 'privilegeModel>/update'},
                        {path: 'featureflags>/DWC_DUMMY_SPACE_PERMISSIONS'},
                        {path: 'privilege>/DWC_DATAINTEGRATION/update'},
                        {path: 'privilegeModel>/canCreateOrUpdateMonitoringUI'}
                        ],
                        formatter : '.runDetailsCancelRunVisibilityformatter'
                      }"
                  />
                  <ToolbarSeparator
                    class="sapUiTinyMarginBottom"
                  />
                  <OverflowToolbarButton
                    type="Transparent"
                    icon="sap-icon://full-screen"
                    press="setLayout('MidColumnFullScreen')"
                    id="enterFullScreenBtn"
                    tooltip="{i18n>enterFullScreenTxt}"
                    visible="{= ${routerModel>/layout} === 'TwoColumnsMidExpanded' || ${routerModel>/layout} === 'TwoColumnsBeginExpanded' }"
                  />
                  <OverflowToolbarButton
                    type="Transparent"
                    icon="sap-icon://exit-full-screen"
                    press="setLayout('TwoColumnsMidExpanded')"
                    id="exitFullScreenBtn"
                    tooltip="{i18n>exitFullScreenTxt}"
                    visible="{= ${routerModel>/layout} === 'MidColumnFullScreen' }"
                  />
                  <OverflowToolbarButton
                    type="Transparent"
                    icon="sap-icon://decline"
                    press="onMidColumnClose"
                    id="closeMidColumn"
                    tooltip="{i18n>closeRightColumn}"
                    visible="{= ${routerModel>/layout} === 'TwoColumnsMidExpanded' || ${routerModel>/layout} === 'MidColumnFullScreen' || ${routerModel>/layout} === 'TwoColumnsBeginExpanded' }"
                  />
                </uxap:actions>
              </uxap:ObjectPageDynamicHeaderTitle>
            </uxap:headerTitle>
            <uxap:headerContent>
              <VBox>
                <MessageStrip
                id="globalPayloadMessageStrip"
                text="{selectedModel>/globalPayload}"
                type="Warning"
                showIcon="true"
                visible="{ parts: [{ path: 'selectedModel>/globalPayload'}, { path: 'selectedModel>/activity'}], formatter:'.showPayLoadMsgStrip' }"
                class="sapUiTinyMarginBottom"
              >
              </MessageStrip>

              </VBox>
              <FlexBox
                wrap="Wrap"
                alignItems="Start"
                class="sapUiMarginBeginEnd"
              >
                <layout:VerticalLayout
                  visible="{= ${privilegeModel>/isRemoteTable} === true || ${privilegeModel>/isViewMonitor} === true || ${privilegeModel>/isStatisticsRoute} === true || ${routerModel>/routeName} === 'ecntaskchainmonitoring' || ${routerModel>/routeName} === 'ecnlogsmonitoring' || ${routerModel>/routeName} === 'logsmonitoring' || (${featureflags>/DWCO_TASK_FRAMEWORK_CANCEL_CHAIN} &amp;&amp; ${privilegeModel>/isTaskChainMonitor})}"
                  class="sapUiTinyMarginTop sapUiSmallMarginEnd"
                >
                  <layout:layoutData>
                    <FlexItemData growFactor="1" />
                  </layout:layoutData>
                  <Label text="{i18n>TASK_ACTIVITY}:" />
                  <ObjectStatus
                    id="taskActivity"
                    text="{ parts: [{ path: 'selectedModel>/activity'}], formatter:'.activityTextFormatter' }"
                  />
                </layout:VerticalLayout>

                <layout:VerticalLayout class="sapUiTinyMarginTop sapUiSmallMarginEnd">
                  <layout:layoutData>
                    <FlexItemData growFactor="1" />
                  </layout:layoutData>
                  <Label text="{i18n>RUN_STATUS}:" />
                  <ObjectStatus
                    text="{ parts: [{ path: 'selectedModel>/status'}, { path: 'selectedModel>/subStatus'}], formatter:'.statusTextFormatter' }"
                    visible="{ parts: [{ path: 'selectedModel>/status'},{ path: 'selectedModel>/subStatus'}], formatter:'.runStatusVisibilityFormatter' }"
                    state="{ parts: [{ path: 'selectedModel>/status'}], formatter:'.statusTextStateFormatter' }"
                    id="objectHeaderStatus"
                  />
                  <Link
                    text="{ parts: [{ path: 'selectedModel>/status'}, { path: 'selectedModel>/subStatus'}], formatter:'.statusTextFormatter' }"
                    class="valueStateError"
                    visible="{ parts: [{ path: 'selectedModel>/status'},{ path: 'selectedModel>/subStatus'}], formatter:'.subStatusLinkVisibilityFormatter' }"
                    press="subStatusClickHandler($event, ${selectedModel>/subStatus})"
                    id="objectHeaderStatusLink"
                  />
                </layout:VerticalLayout>

                <layout:VerticalLayout class="sapUiTinyMarginTop sapUiSmallMarginEnd">
                  <layout:layoutData>
                    <FlexItemData growFactor="1" />
                  </layout:layoutData>
                  <Label text="{i18n>RUN_START_DETAILS}:" />
                  <ObjectStatus text="{ parts: [{ path: 'selectedModel>/startTime'}], formatter:'.formatDateTime' }" />
                </layout:VerticalLayout>

                <layout:VerticalLayout class="sapUiTinyMarginTop sapUiSmallMarginEnd">
                  <layout:layoutData>
                    <FlexItemData growFactor="1" />
                  </layout:layoutData>
                  <Label text="{i18n>RUN_END_DETAILS}:" />
                  <ObjectStatus text="{path: 'selectedModel>/endTime', formatter: '.formatDateTime'}" />
                </layout:VerticalLayout>

                <layout:VerticalLayout class="sapUiTinyMarginTop sapUiSmallMarginEnd">
                  <layout:layoutData>
                    <FlexItemData growFactor="1" />
                  </layout:layoutData>
                  <Label
                    text="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} === true ? ${i18n>TRIGGEREDBYNewImp} : ${i18n>TRIGGEREDBYNew}}:"
                  />
                  <ObjectStatus text="{selectedModel>/user}" visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} === false}" />
                  <VBox visible="{featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS}">
                    <!--  User name and opening bracket -->
                    <ObjectStatus
                      text="{selectedModel>/user}"  visible="{= ${selectedModel>/user} !== undefined}"/>
                    <HBox>
                      <ObjectStatus
                      text="{i18n>OPEN_BRACKET}"
                      visible="{= ${selectedModel>/user} !== undefined}"
                    />
                    <!-- If Scheduled -->
                    <Link
                        text="{i18n>SCHEDULE}"
                        press=".openSchedule($event)"
                        visible="{= !!${selectedModel>/status} &amp;&amp; !!${selectedModel>/scheduleId} }" />

                    <!-- If Manual -->
                    <ObjectStatus
                      text="{i18n>MANUAL}"
                      visible="{= !!${selectedModel>/status} &amp;&amp; !${selectedModel>/scheduleId} &amp;&amp; !${selectedModel>/taskChainParent} }"
                    />

                    <!-- If triggered by parent task chain -->
                      <Link
                        text="{selectedModel>/taskChainParent}"
                        href="{selectedModel>/taskChainParentLink}"
                        press="openTaskChain"
                        visible="{= ${selectedModel>/taskChainParent} !== undefined}" />
                    <!--  Closing bracket -->
                    <ObjectStatus
                      text="{i18n>CLOSE_BRACKET}"
                      visible="{= ${selectedModel>/user} !== undefined}"
                    />
                    </HBox>
                  </VBox>

                </layout:VerticalLayout>

                <layout:VerticalLayout class="sapUiTinyMarginTop sapUiSmallMarginEnd">
                  <layout:layoutData>
                    <FlexItemData growFactor="1" />
                  </layout:layoutData>
                  <Label
                    text="{i18n>EXECUTIONTYPENew}:"
                  />
                  <ObjectStatus
                    text="{ parts: [{ path: 'selectedModel>/status'},{ path: 'selectedModel>/scheduleId'}, { path: 'selectedModel>/activity'}, { path: 'selectedModel>/isSubTask'}], formatter:'.formatExecutionType' }"
                  />
                </layout:VerticalLayout>
                <layout:VerticalLayout
                  visible="{= (${selectedModel>/taskChainParent} !== undefined || (${featureflags>/DWCO_TASK_FRAMEWORK_SHARE_TASK_CHAINS} &amp;&amp; ${selectedModel>/authorizedOnParent} === false)) &amp;&amp; (${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} === false)}"
                  class="sapUiTinyMarginTop sapUiSmallMarginEnd"
                >
                  <layout:layoutData>
                    <FlexItemData growFactor="1" />
                  </layout:layoutData>
                  <Label text="{selectedModel>/parentLabel}"/>
                  <Link
                    id="parentTaskChain"
                    visible="{= ${selectedModel>/taskChainParent} !== undefined}"
                    text="{selectedModel>/taskChainParent}"
                    href="{selectedModel>/taskChainParentLink}"
                    press="openTaskChain"
                  />
                  <Text
                  id="unautorizedParentTaskChain"
                  visible="{= (${featureflags>/DWCO_TASK_FRAMEWORK_SHARE_TASK_CHAINS} &amp;&amp; ${selectedModel>/authorizedOnParent} === false)}"
                  text="{i18n>Unauthorized}"
                  ></Text>
                </layout:VerticalLayout>

                <layout:VerticalLayout class="sapUiTinyMarginTop sapUiSmallMarginEnd">
                  <layout:layoutData>
                    <FlexItemData growFactor="1" />
                  </layout:layoutData>
                  <Label
                    text="{i18n>PARENT_TASKCHAIN_SPACE}:"
                    visible="{= ${selectedModel>/taskChainParentSpace} !== undefined &amp;&amp; ${featureflags>/DWCO_TASK_FRAMEWORK_SHARE_TASK_CHAINS} &amp;&amp; (${selectedModel>/authorizedOnParent} === false || ${selectedModel>/taskChainParent} !== undefined)}"
                  />
                  <ObjectStatus
                    id="parentTaskChainSpace"
                    text="{selectedModel>/taskChainParentSpace}"
                    visible="{= ${selectedModel>/taskChainParentSpace} !== undefined &amp;&amp; ${featureflags>/DWCO_TASK_FRAMEWORK_SHARE_TASK_CHAINS} &amp;&amp; (${selectedModel>/authorizedOnParent} === false || ${selectedModel>/taskChainParent} !== undefined)}"
                  />
                </layout:VerticalLayout>
              </FlexBox>
            </uxap:headerContent>
            <uxap:sections>
              <uxap:ObjectPageSection
                titleUppercase="false"
                id="Entities"
                title="{= ${i18n>analyzedView} }"
              >
                <uxap:subSections>
                  <uxap:ObjectPageSubSection>
                    <!-- Dynamically add the section content-->
                  </uxap:ObjectPageSubSection>
                  <uxap:ObjectPageSubSection>
                    <!-- Dynamically add the section content-->
                  </uxap:ObjectPageSubSection>
                </uxap:subSections>
              </uxap:ObjectPageSection>
              <uxap:ObjectPageSection showTitle="false">
                <uxap:subSections>

                  <uxap:ObjectPageSubSection
                    mode="Expanded"
                    visible="{= (${privilegeModel>/isTaskChainMonitor} !== undefined &amp;&amp; ${privilegeModel>/isTaskChainMonitor} === true) ||   ${routerModel>/routeName} === 'ecntaskchainmonitoring'}"
                  >
                    <uxap:blocks>
                      <t:TreeTable
                        id="taskChainLogMessagesTable"
                        enableBusyIndicator="true"
                        selectionMode="Single"
                        selectionBehavior="RowOnly"
                        showNoData="true"
                        enableColumnReordering="false"
                        expandFirstLevel="true"
                        rowSelectionChange="onTaskChainRowSelect"
                        noData="{i18n>No_Data}"
                        visibleRowCount="{taskChainTreeModel>/treeTableRowCount}"
                        rows="{path:'taskChainTreeModel>/', templateShareable:false,
                                          parameters : {
                                            numberOfExpandedLevels: 1,
                                            arrayNames:['nodes']
                                        }}"
                        class="sapUiNoMarginBegin sapUiNoMarginEnd noColumnBorder"
                      >

                        <t:extension>
                          <OverflowToolbar>
                            <Title
                              id="taskChainLogMessagesTitle"
                              text="{logsModel>/chainsTableHeader}"
                            />

                          </OverflowToolbar>
                        </t:extension>
                        <t:Column
                          id="idTaskChainObjectName"
                          autoResizable="true"
                          width="35%"
                        >
                          <Label
                            text="{i18n>Object}"
                            tooltip="{i18n>Object}"
                          />
                          <t:template>
                            <HBox>
                              <core:Icon
                                src="{taskChainTreeModel>icon}"
                                size="1rem"
                                class="sapUiTinyMarginEnd"
                              />
                              <Text
                                text="{taskChainTreeModel>objectName}"
                                wrapping="false"
                                tooltip="{taskChainTreeModel>objectName}"
                              ></Text>
                            </HBox>
                          </t:template>
                        </t:Column>
                        <t:Column
                        id="idTaskChainSharedSpaceName"
                        width="20%"
                      >
                        <Label
                          text="{i18n>crossSpace}"
                          tooltip="{i18n>crossSpace}"
                        />
                        <t:template>
                          <HBox>
                            <Text
                              text="{taskChainTreeModel>crossSpaceName}"
                              tooltip="{taskChainTreeModel>crossSpaceName}"
                              wrapping="false"
                            ></Text>
                          </HBox>
                        </t:template>
                      </t:Column>
                        <t:Column id="idTaskChainTypeName">
                          <Label
                            text="{i18n>typeTxt}"
                            tooltip="{i18n>typeTxt}"
                          />
                          <t:template>
                            <Text
                              text="{ parts: [{ path: 'taskChainTreeModel>applicationId'}], formatter:'.ObjectTypeFormatter' }"
                              tooltip="{ parts: [{ path: 'taskChainTreeModel>applicationId'}], formatter:'.ObjectTypeFormatter' }"
                              wrapping="false"
                            ></Text>
                          </t:template>
                        </t:Column>
                        <t:Column
                          id="idTaskChainObjectActivity"
                          visible="{= ${routerModel>/routeName} === 'ecntaskchainmonitoring'}"
                        >
                          <Label
                            text="{i18n>activityTxt}"
                            tooltip="{i18n>activityTxt}"
                          />
                          <t:template>
                            <Text
                              text="{ parts: [{ path: 'taskChainTreeModel>activity'}, { path: 'taskChainTreeModel>' }], formatter:'.nodeBasedActivityTextFormatter' }"
                              wrapping="false"
                              tooltip="{ parts: [{ path: 'taskChainTreeModel>activity'}, { path: 'taskChainTreeModel>' }], formatter:'.nodeBasedActivityTextFormatter' }"
                            ></Text>
                          </t:template>
                        </t:Column>
                        <t:Column id="idTaskChainObjectStatus">
                          <Label
                            text="{i18n>STATUS}"
                            tooltip="{i18n>STATUS}"
                          />
                          <t:template>
                            <HBox>
                              <ObjectStatus
                                text="{ parts: [{ path: 'taskChainTreeModel>status'}, { path: 'taskChainTreeModel>subStatus'}], formatter:'.statusTextFormatter' }"
                                visible="{ parts: [{ path: 'taskChainTreeModel>/status'},{ path: 'taskChainTreeModel>/subStatus'}], formatter:'.runStatusVisibilityFormatter' }"
                                tooltip="{ parts: [{ path: 'taskChainTreeModel>status'}, { path: 'taskChainTreeModel>subStatus'}], formatter:'.statusTextFormatter' }"
                                state="{ parts: [{ path: 'taskChainTreeModel>status'}], formatter:'.statusTextStateFormatter' }"
                                class="replicationStatusCls"
                                active="{ parts: [{ path: 'taskChainTreeModel>status'},{ path: 'taskChainTreeModel>subStatus'}], formatter:'.subStatusLinkVisibilityFormatter' }"
                                press="subStatusClickHandler($event, ${taskChainTreeModel>subStatus})"
                              />
                              <core:Icon
                                src="sap-icon://message-warning"
                                size="1rem"
                                id="noChildNodesWarn"
                                class="sapUiTinyMarginBegin"
                                visible="{taskChainTreeModel>showErrorIcon}"
                                tooltip="{taskChainTreeModel>errorIconText}"
                              />
                            </HBox>

                          </t:template>
                        </t:Column>
                        <t:Column
                          id="idTaskChainDuration">
                          <Label
                            text="{i18n>RUNTIME}"
                            tooltip="{i18n>RUNTIME}"
                          />
                          <t:template>
                            <Text
                              text="{ parts: [{ path: 'taskChainTreeModel>runTime'}], formatter:'.runtimeFormatter' }"
                              wrapping="false"
                              tooltip="{ parts: [{ path: 'taskChainTreeModel>runTime'}], formatter:'.runtimeFormatter' }"
                            ></Text>
                          </t:template>
                        </t:Column>
                        <t:Column
                          id="idTaskChainObjectMonitor"
                          visible="{= ${routerModel>/routeName} !== 'ecntaskchainmonitoring'}"
                        >
                          <Label
                            text="{i18n>monitorTxt}"
                            tooltip="{i18n>monitorTxt}"
                          />
                          <t:template>
                            <Link
                              id="navMonitor"
                              text="{i18n>viewInMonitorTxt}"
                              tooltip="{i18n>viewInMonitorTxt}"
                              press="navigatateToMonitor($event, ${taskChainTreeModel>objectId}, ${taskChainTreeModel>applicationId}, ${taskChainTreeModel>logId}, ${taskChainTreeModel>crossSpaceName})"
                              visible="{= ${taskChainTreeModel>showNavLink} === true &amp;&amp; !!${taskChainTreeModel>logId} &amp;&amp; !!${taskChainTreeModel>status} &amp;&amp; ${routerModel>/routeName} !== 'ecntaskchainmonitoring'}"
                              wrapping="false"
                              href="{taskChainTreeModel>navLinkUrl}"
                            />
                          </t:template>
                        </t:Column>
                        <t:Column
                          id="idActivity"
                        >
                          <Label
                            text="{i18n>TASK_ACTIVITY}"
                            tooltip="{i18n>TASK_ACTIVITY}"
                          />
                          <t:template>
                            <Text
                              text="{taskChainTreeModel>activity}"
                              wrapping="false"
                              tooltip="{taskChainTreeModel>activity}"
                            ></Text>
                          </t:template>
                        </t:Column>


                      </t:TreeTable>
                    </uxap:blocks>
                  </uxap:ObjectPageSubSection>

                  <uxap:ObjectPageSubSection
                    mode="Expanded"
                    visible="{= (${privilegeModel>/isTaskChainMonitor} === undefined || ${privilegeModel>/isTaskChainMonitor} === false) &amp;&amp; ${routerModel>/routeName} !== 'ecntaskchainmonitoring'}"
                    titleUppercase="false"
                    id="Messages"
                    title="{i18n>Messages}"
                  >
                    <uxap:blocks>
                      <Table
                        inset="false"
                        id="taskLogMessageTable"
                        items="{
                            path: 'messagesModel>/',
                            sorter: {
                                 path: 'timestamp'
                              }
                            }"
                      >
                        <headerToolbar>
                          <OverflowToolbar>
                            <content>
                              <Title
                                id="titleMessages"
                                text="{logsModel>/sDetailsTableHeader}"
                              />
                              <ToolbarSpacer />
                              <SearchField
                                id="messagesSearch"
                                width="25%"
                                liveChange="onMessageSearch($event, ${viewSettingsDialogModel>/tableId/MESSAGES}, 'text', ${viewSettingsDialogModel>/dialogLayout/MESSAGES_SORT}, ${viewSettingsDialogModel>/dialogLayout/MESSAGES_FILTER})"
                              />
                              <Button
                                id="messagesSortSettingButton"
                                tooltip="{i18n>sortTxt}"
                                icon="sap-icon://sort"
                                press="handleSortButtonPressed($event, ${viewSettingsDialogModel>/dialogLayout/MESSAGES_SORT})"
                              />
                              <Button
                                id="messagesFilterSettingButton"
                                tooltip="{i18n>filterTxt}"
                                icon="sap-icon://filter"
                                press="handleFilterButtonPressed($event, ${viewSettingsDialogModel>/dialogLayout/MESSAGES_FILTER})"
                              />
                            </content>
                          </OverflowToolbar>
                        </headerToolbar>
                        <infoToolbar>
                          <OverflowToolbar
                            id="taskLogMessageTable-FilterBar"
                            visible="false"
                          >
                            <Text text="{viewSettingsDialogModel>/taskLogMessageTableFilterBarText}" />
                          </OverflowToolbar>
                        </infoToolbar>
                        <columns>
                          <Column
                            id="idLogDetailTimeStamp"
                            width="20%"
                          >
                            <Label
                              text="{i18n>TIME}"
                              tooltip="{i18n>TIME}"
                            />

                          </Column>
                          <Column
                            id="idLogDetailStatus"
                            minScreenWidth="Tablet"
                            demandPopin="true"
                            width="20%"
                          >
                            <Label
                              text="{i18n>TASK_STATUS}"
                              tooltip="{i18n>TASK_STATUS}"
                            />
                          </Column>
                          <Column
                            id="idLogDetailMessages"
                            width="auto"
                          >
                            <Label
                              text="{i18n>MESSAGE}"
                              tooltip="{i18n>MESSAGE}"
                            />
                          </Column>
                        </columns>
                        <items>
                          <ColumnListItem vAlign="Middle">
                            <cells>
                              <Text text="{messagesModel>timestamp}"></Text>

                              <HBox
                                alignItems="Center"
                                justifyContent="SpaceBetween"
                              >
                                <HBox justifyContent="Start">
                                  <ObjectStatus
                                    text="{messagesModel>formattedSeverity}"
                                    tooltip="{messagesModel>formattedSeverity}"
                                    state="{ path: 'messagesModel>severity', formatter:'.severityStateFormatter' }"
                                  />
                                </HBox>
                              </HBox>

                              <HBox>
                                <Text
                                  text="{messagesModel>text}"
                                  wrapping="true"
                                ></Text>
                                <Link
                                  id="viewdetails"
                                  visible="{parts:[{path:'messagesModel>text'}, {path:'messagesModel>details'}], formatter:'.showLogErrorDetailsLinkFormatter'}"
                                  text="{i18n>VIEW_ERROR_DETAILS}"
                                  press="handleLogErrorDetailPress"
                                  class="sapUiTinyMarginBegin"
                                />
                                <Link
                                  id="viewdetailsofExplainPlan"
                                  visible="{parts:[{path:'messagesModel>contentSemanticValue'}], formatter:'.showViewDetailsForExplainPlan'}"
                                  text="{i18n>VIEW_ERROR_DETAILS}"
                                  press=".handleViewExplainPlanDetails($event, ${messagesModel>})"
                                  class="sapUiTinyMarginBegin"
                                />
                                <Link
                                  id="bwProcessTenantLink"
                                  visible="{= (${path:'messagesModel>isBWProcessChainLink', formatter:'.showBWTenantLink'} &amp;&amp; ${featureflags>/DWCO_INFRA_TASKS_BW_PROCESS_CHAIN} === true) ? true : false}"
                                  text="{i18n>VIEW_IN_BW_BRIDGE}"
                                  href="{messagesModel>link/url}"
                                  rel="noopener noreferrer" target="_blank"
                                />
                                <Button
                                  id="downloadAdtnlDetails"
                                  icon="sap-icon://download"
                                  type="Transparent"
                                  tooltip="{i18n>DOWNLOAD_ADTNL_DETAILS}"
                                  press=".onDownloadAdditionalDetails($event, ${messagesModel>})"
                                  class="sapUiTinyMarginBegin sapUiTinyMarginBottom"
                                  visible="{parts:[{path:'messagesModel>isBlobContentAvailable'}, {path:'messagesModel>contentSemanticValue'}], formatter:'.showDownloadButton'}"
                                />
                              </HBox>

                            </cells>
                          </ColumnListItem>
                        </items>

                      </Table>
                    </uxap:blocks>
                  </uxap:ObjectPageSubSection>

                </uxap:subSections>
              </uxap:ObjectPageSection>
              <uxap:ObjectPageSection
                title="{i18n>Metrics}"
                titleUppercase="false"
                id="Metrics"
                visible="{= (${privilegeModel>/isViewMonitor} === true)}">

                <uxap:ObjectPageSubSection
                    mode="Expanded"
                    visible="{= ${logsModel>/hasMetrics} === true}"
                    titleUppercase="false"
                    id="MetricsSubSection"
                    title="{i18n>Metrics}"
                  >
                    <uxap:blocks>
                      <Table id="metricsTable"
                      inset="false"
                      items="{
                        path: 'logsModel>/generalMetrics'
                      }">
                      <headerToolbar>
                        <OverflowToolbar>
                          <content>
                            <Title
                              id="metricTitle"
                              text="{logsModel>/metricTitle}"
                            />
                            <ToolbarSpacer />
                            <SearchField
                              id="metricSearch"
                              width="25%"
                              liveChange="onMetricSearch($event, false)"
                            />
                          </content>
                        </OverflowToolbar>
                      </headerToolbar>
                        <columns>
                          <Column>
                            <Label text="{i18n>Name}" />
                          </Column>
                          <Column>
                            <Label text="{i18n>value}" />
                          </Column>
                        </columns>
                        <items>
                          <ColumnListItem>
                            <cells>
                              <Text text="{logsModel>name}" />
                              <Text text="{logsModel>value}" />
                            </cells>
                          </ColumnListItem>
                        </items>
                      </Table>
                    </uxap:blocks>
                  </uxap:ObjectPageSubSection>
                </uxap:ObjectPageSection>
                <uxap:ObjectPageSection id="PartitionMetrics">
                  <uxap:ObjectPageSubSection
                    mode="Expanded"
                    titleUppercase="false"
                    id="PartitionMetricsSubsection"
                    title="{i18n>PartitionMetrics}"
                    visible="{= ${logsModel>/hasPartitionMetrics} === true}"
                  >
                    <uxap:blocks>
                      <Table id="partitionMetricsTable"
                      items="{
                        path: 'logsModel>/partitionMetrics',
                        templateShareable: false,
                        sorter: {
                        path: 'MEMORY_CONSUMPTION_GIB',
                        descending: true
                        }
                      }">
                      <headerToolbar>
                        <OverflowToolbar>
                          <content>
                            <Title
                              id="partitionmetricTitle"
                              text=""
                            />
                            <ToolbarSpacer />
                            <SearchField
                              id="partitionmetricSearch"
                              width="25%"
                              liveChange="onMetricSearch($event, true)"
                            />
                            <Button
                                id="partitionMetricsSortSettingButton"
                                tooltip="{i18n>sortTxt}"
                                icon="sap-icon://sort"
                                press="handleSortButtonPressed($event, ${viewSettingsDialogModel>/dialogLayout/METRICS_SORT})"
                              />
                          </content>
                        </OverflowToolbar>
                      </headerToolbar>
                        <columns>
                          <Column>
                            <Label text="{i18n>Name}" />
                          </Column>
                          <Column
                            id="partitionRange"
                            visible="{logsModel>/showPartitionRange}">
                            <Label text="{i18n>Partition_Range}" />
                          </Column>
                          <Column>
                            <Label text="{i18n>noofrecords}" />
                          </Column>
                          <Column>
                            <Label text="{i18n>partitionpeakmemory}" />
                          </Column>
                        </columns>
                        <items>
                          <ColumnListItem>
                            <cells>
                              <Text text="{logsModel>NAME}" />
                              <Text text="{logsModel>PARTITION_RANGE}" />
                              <Text text="{logsModel>NUMBER_OF_RECORDS}" />
                              <Text text="{logsModel>MEMORY_CONSUMPTION_GIB}" />
                            </cells>
                          </ColumnListItem>
                        </items>

                      </Table>
                    </uxap:blocks>
                  </uxap:ObjectPageSubSection>
                </uxap:ObjectPageSection>
            </uxap:sections>

          </uxap:ObjectPageLayout>
        </f:midColumnPages>
        <f:endColumnPages id="taskLogEndColumnPages">
          <ScrollContainer
            height="100%"
            width="100%"
            vertical="true"
            focusable="true"
          >
            <core:Fragment
              fragmentName="sap.cdw.components.tasklog.view.fragment.TaskChainMessageDetails"
              type="XML"
            />
          </ScrollContainer>
        </f:endColumnPages>
      </f:FlexibleColumnLayout>
    </content>
  </Page>
  <VBox>
    <mvc:XMLView
      id="taskschedulingAuth"
      viewName="sap.cdw.components.taskscheduler.view.TaskScheduleAuth"
      visible="{= ${privRemoteConnModel>/update} === true}"
      cd:actionId="monitoring/editor/authFlow"
    />
    <mvc:XMLView
      id="taskSchedulingDialog"
      viewName="sap.cdw.components.taskscheduler.view.TaskSchedule"
      visible="{= ${privRemoteConnModel>/update} === true}"
    />
  </VBox>
</mvc:View>
