<core:FragmentDefinition
  xmlns="sap.m"
  xmlns:uxap="sap.uxap"
  xmlns:mvc="sap.ui.core.mvc"
  xmlns:layout="sap.ui.layout"
  xmlns:core="sap.ui.core"
  xmlns:t="sap.ui.table"
  xmlns:f="sap.f"
  xmlns:unified="sap.ui.unified"
  xmlns:ac="sap.cdw.components.reuse.control.actionchecker"
  xmlns:cd="http://schemas.sap.com/sapui5/extension/sap.ui.core.CustomData/1"
>

  <uxap:ObjectPageLayout
    id="taskLogMessagesSection"
    showTitleInHeaderContent="true"
    headerContentPinnable="false"
    isChildPage="false"
    upperCaseAnchorBar="false"
    useIconTabBar="true"
  >
    <uxap:headerTitle>
      <uxap:ObjectPageDynamicHeaderTitle>
        <uxap:expandedHeading>
          <Title
            text="{logsModel>/chainsMessagesTableHeader}"
            tooltip="{logsModel>/chainsMessagesTableHeader}"
            wrapping="false"
            class="sapUiSmallMarginEnd"
          />
        </uxap:expandedHeading>
        <uxap:snappedHeading>
          <Title
            text="{logsModel>/chainsMessagesTableHeader}"
            tooltip="{logsModel>/chainsMessagesTableHeader}"
            wrapping="false"
            class="sapUiSmallMarginEnd"
          />
        </uxap:snappedHeading>
        <uxap:navigationActions>
          <OverflowToolbarButton
            type="Transparent"
            icon="sap-icon://full-screen"
            press="setLayout('EndColumnFullScreen')"
            id="enterFullScreenBtnEndCol"
            tooltip="{i18n>enterFullScreenTxt}"
            visible="{= ${routerModel>/layout} === 'ThreeColumnsMidExpanded' || ${routerModel>/layout} === 'ThreeColumnsEndExpanded' }"
          />
          <OverflowToolbarButton
            type="Transparent"
            icon="sap-icon://exit-full-screen"
            press="setLayout('ThreeColumnsMidExpanded')"
            id="exitFullScreenBtnEndCol"
            tooltip="{i18n>exitFullScreenTxt}"
            visible="{= ${routerModel>/layout} === 'EndColumnFullScreen' }"
          />
          <OverflowToolbarButton
            type="Transparent"
            icon="sap-icon://decline"
            press="onEndColumnClose"
            id="closeEndColumn"
            tooltip="{i18n>closeRightColumn}"
            visible="{= ${routerModel>/layout} === 'ThreeColumnsMidExpanded' || ${routerModel>/layout} === 'EndColumnFullScreen' || ${routerModel>/layout} === 'ThreeColumnsEndExpanded' }"
          />
        </uxap:navigationActions>
      </uxap:ObjectPageDynamicHeaderTitle>
    </uxap:headerTitle>
    <uxap:sections>
      <uxap:ObjectPageSection showTitle="false">
        <uxap:subSections>
          <uxap:ObjectPageSubSection>
            <uxap:blocks>
              <IconTabBar id="childLogMessagesSection">
                <items>
                  <IconTabFilter
                    text="{logsModel>/sDetailsTableHeader}"
                    key="messages"
                  >
                  <MessageStrip
                  id="crossSpaceIssueMessage"
                  text="{messagesModel>/chainsMessagesCrossSpaceIssueText}"
                  showIcon="true"
                  enableFormattedText="true"
                  height="100%"
                  visible="{= ${path: 'messagesModel>/chainsMessagesCrossSpaceIssueText', formatter: '.chainMessageIssueVisibilityFormatter'}}"
                  >
                </MessageStrip>
                    <Table
                      id="taskChainMessageTable"
                      inset="false"
                      items="{
                path: 'messagesModel>/',
                sorter: {
                  path: 'timestamp',
                  descending: true
                }
              }"
                    >
                      <headerToolbar>
                        <OverflowToolbar>
                          <content>
                            <ToolbarSpacer />
                            <SearchField
                              id="chainMessageSearch"
                              width="50%"
                              liveChange="onMessageSearch($event, ${viewSettingsDialogModel>/tableId/CHAIN_MESSAGES}, 'text', ${viewSettingsDialogModel>/dialogLayout/CHAIN_MESSAGES_SORT}, ${viewSettingsDialogModel>/dialogLayout/CHAIN_MESSAGES_FILTER})"
                            />
                            <Button
                              id="chainMessagesSortSettingButton"
                              tooltip="{i18n>sortTxt}"
                              icon="sap-icon://sort"
                              press="handleSortButtonPressed($event, ${viewSettingsDialogModel>/dialogLayout/CHAIN_MESSAGES_SORT})"
                            />
                            <Button
                              id="chainMessagesFilterSettingButton"
                              tooltip="{i18n>filterTxt}"
                              icon="sap-icon://filter"
                              press="handleFilterButtonPressed($event, ${viewSettingsDialogModel>/dialogLayout/CHAIN_MESSAGES_FILTER})"
                            />
                          </content>
                        </OverflowToolbar>
                      </headerToolbar>
                      <infoToolbar>
                        <OverflowToolbar
                          id="taskChainMessageTable-FilterBar"
                          visible="false"
                        >
                          <Text text="{viewSettingsDialogModel>/taskChainMessageTableFilterBarText}" />
                        </OverflowToolbar>
                      </infoToolbar>


                      <columns>
                        <Column
                          id="chainLogDetailTimeStamp"
                          width="30%"
                        >
                          <Label
                            text="{i18n>TIME}"
                            tooltip="{i18n>TIME}"
                          />

                        </Column>
                        <Column
                          id="chainLogDetailStatus"
                          minScreenWidth="Tablet"
                          demandPopin="true"
                          width="20%"
                        >
                          <Label
                            text="{i18n>TASK_STATUS}"
                            tooltip="{i18n>TASK_STATUS}"
                          />
                        </Column>
                        <Column
                          id="chainLogDetailMessages"
                          width="auto"
                        >
                          <Label
                            text="{i18n>MESSAGE}"
                            tooltip="{i18n>MESSAGE}"
                          />
                        </Column>
                      </columns>
                      <items>
                        <ColumnListItem vAlign="Middle">
                          <cells>
                            <Text text="{messagesModel>timestamp}"></Text>

                            <HBox
                              alignItems="Center"
                              justifyContent="SpaceBetween"
                            >
                              <HBox justifyContent="Start">
                                <ObjectStatus
                                  text="{messagesModel>formattedSeverity}"
                                  tooltip="{messagesModel>formattedSeverity}"
                                  state="{ path: 'messagesModel>severity', formatter:'.severityStateFormatter' }"
                                />
                              </HBox>
                            </HBox>

                            <VBox>
                              <Text
                                text="{messagesModel>text}"
                                wrapping="true"
                              ></Text>
                              <HBox>
                                <Link
                                  id="chainViewdetails"
                                  visible="{parts:[{path:'messagesModel>text'}, {path:'messagesModel>details'}], formatter:'.showLogErrorDetailsLinkFormatter'}"
                                  text="{i18n>VIEW_ERROR_DETAILS}"
                                  press="handleLogErrorDetailPress"
                                />
                                <Link
                                  id="bwTenantLink"
                                  visible="{= (${path:'messagesModel>isBWProcessChainLink', formatter:'.showBWTenantLink'} &amp;&amp; ${featureflags>/DWCO_INFRA_TASKS_BW_PROCESS_CHAIN} === true) ? true : false}"
                                  text="{i18n>VIEW_IN_BW_BRIDGE}"
                                  href="{messagesModel>link/url}"
                                  tooltip="{i18n>VIEW_IN_BW_BRIDGE}"
                                  rel="noopener noreferrer" target="_blank"
                                />
                                <Button
                                  id="downloadtaskChainBlobContent"
                                  icon="sap-icon://download"
                                  type="Transparent"
                                  tooltip="{i18n>DOWNLOAD_ADTNL_DETAILS}"
                                  press=".onDownloadAdditionalDetails($event, ${messagesModel>})"
                                  visible="{= ${featureflags>/DWCO_INFRA_TASKS_BLOB_COLUMN} === true &amp;&amp; ${messagesModel>isBlobContentAvailable} === true}"
                                />
                              </HBox>
                            </VBox>
                          </cells>
                        </ColumnListItem>
                      </items>

                    </Table>
                  </IconTabFilter>
                  <IconTabFilter
                    text="{i18n>metricsTxt}"
                    key="metrics"
                    visible="{= ${messagesModel>/isDataFlowNode} === true }"
                  >
                    <Table
                      inset="false"
                      id="runsMetricsTable"
                      items="{
          path: 'metricesModel>/',
          sorter: {
            path: 'name'
          }
        }"
                    >
                      <columns>
                        <Column
                          id="idMetricsName"
                          width="auto"
                        >
                          <Label
                            text="{i18n>metricesColLabel}"
                            tooltip="{i18n>metricesColLabel}"
                          />
                        </Column>
                        <Column
                          id="idMetricsType"
                          minScreenWidth="Tablet"
                          demandPopin="true"
                          width="auto"
                        >
                          <Label
                            text="{i18n>metricesType}"
                            tooltip="{i18n>metricesType}"
                          />
                        </Column>
                        <Column
                          id="idMetricsRecordCount"
                          width="auto"
                        >
                          <Label
                            text="{i18n>metricesRecordLabel}"
                            tooltip="{i18n>metricesRecordLabel}"
                          />
                        </Column>
                      </columns>
                      <items>
                        <ColumnListItem vAlign="Middle">
                          <cells>
                            <Text text="{metricesModel>name}"></Text>
                            <Text text="{metricesModel>type}"></Text>
                            <Text text="{metricesModel>rowCount}"></Text>
                          </cells>
                        </ColumnListItem>
                      </items>
                    </Table>
                  </IconTabFilter>
                </items>
              </IconTabBar>
            </uxap:blocks>
          </uxap:ObjectPageSubSection>
        </uxap:subSections>
      </uxap:ObjectPageSection>
    </uxap:sections>
  </uxap:ObjectPageLayout>
</core:FragmentDefinition>
