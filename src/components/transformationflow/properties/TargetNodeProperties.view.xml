<mvc:View
  xmlns:core="sap.ui.core"
  xmlns="sap.m"
  xmlns:l="sap.ui.layout"
  xmlns:table="sap.ui.table"
  xmlns:dnd="sap.ui.core.dnd"
  xmlns:f="sap.ui.layout.form"
  xmlns:mvc="sap.ui.core.mvc"
  xmlns:tokenListItem="sap.cdw.components.tokenListItem.control"
  xmlns:er="sap.cdw.components.ermodeler.control"
  xmlns:cb="sap.cdw.components.reuse.control.circuitbreaker"
  controllerName="sap.cdw.components.transformationflow.properties.TargetNodeProperties"
  class="propertyPanel--content"
  xmlns:pkgs="sap.skyline.tools.packageselector"
  xmlns:dos="sap.cdw.components.reuse.control.dwcobjectstatus"
>
  <VBox class="propertyPanel--vbox">
    <core:Fragment
      fragmentName="sap.cdw.components.transformationflow.properties.fragment.CommonPropertyHeader"
      type="XML"
    />

    <VBox class="propertyPanel--scrollable">
      <er:PropertyDetailsHeader
        id="detailsHeader"
        objectName="{galileiModel>/nodeDisplayName}"
        className="{path:'galileiModel>/', formatter:'.classNameFormatter'}"
        validationButtonType="{path:'galileiModel>/aggregatedValidations/validations', formatter:'.aggregationValidationType'}"
        validationButtonIconCustomStyleClass="{path:'galileiModel>/aggregatedValidations/validations', formatter:'.aggregationValidationTypeCustomStyle'}"
        validationButtonText="{path:'galileiModel>/aggregatedValidations/validations', formatter:'.aggregationValidationCount'}"
        validationButtonPress="onShowValidations"
        columnCount="{path:'columnsModel>/columns', formatter:'.amountOfColumnsFormatter'}"
      />
      <!-- Template Content -->
      <VBox
        class="tf-properties-template"
        visible="{=${galileiModel>/isTemplate}===true}"
        alignItems="Center"
      >
        <Title
          text="{i18n>defineTarget}"
          titleStyle="H5"
          wrapping="true"
        />
        <Text
          text="{i18n>defineTargetInformation}"
          wrapping="true"
        />
        <Button
          id="createNewTargetTable"
          text="{i18n>createNewTargetTable}"
          icon="sap-icon://sac/table"
          press="onCreateNewTargetButtonPress"
        />
      </VBox>
      <!-- Non-Template Content -->
      <VBox
        class="NodePropertiesContainer propertiesPanelContainer"
        visible="{=${galileiModel>/isTemplate}!==true}"
      >
        <Panel
          id="targetNodePropertyPanel"
          expandable="true"
          expanded="true"
          headerText="{i18n>general}"
        >
          <headerToolbar>
            <OverflowToolbar>
              <Title
                level="H3"
                text="{i18n>general}"
              />
              <ToolbarSpacer />
            </OverflowToolbar>
          </headerToolbar>
          <f:SimpleForm
            editable="{header>/editable}"
            id="datasourceForm"
            class="propertiesForm"
            layout="ResponsiveGridLayout"
            labelSpanXL="3"
            labelSpanL="3"
            labelSpanM="3"
            labelSpanS="12"
            adjustLabelSpan="true"
            emptySpanXL="4"
            emptySpanL="4"
            emptySpanM="4"
            emptySpanS="0"
            columnsXL="1"
            columnsL="1"
            columnsM="1"
            singleContainerFullSize="false"
          >
            <f:content>

              <Label text="{i18n>businessName}" />
              <Input
                id="businessName"
                liveChange="onBusinessNameLiveChange"
                change="onBusinessNameChanged"
                maxLength="1000"
                value="{galileiModel>/businessName}"
                enabled="{galileiModel>/isNew}"
                editable="{header>/editable}"
              />
              <!--Existing object as target-->
              <Label text="{i18n>technicalName}" />
              <Input
                id="technicalNameExistingTarget"
                value="{/technicalName}"
                enabled="false"
                editable="{header>/editable}"
                visible="{= ${galileiModel>/isNew} === true ? false : true}"
              />
              <!--New Target-->
              <Label text="{i18n>technicalName}" />
              <Input
                id="technicalName"
                value="{galileiModel>/technicalName}"
                liveChange="onTechnicalNameLiveChange"
                change="onTechnicalNameChanged"
                enabled="{galileiModel>/isNew}"
                editable="{header>/editable}"
                visible="{= ${galileiModel>/isNew} === true ? true : false}"
              />
                  <!-- Package Selector -->
              <Label
                text="{i18n>package}"
                labelFor="packageSelector"
              ></Label>
              <pkgs:PackageSelector
                id="packageSelector"
                packages="{workbenchEnv>/packages}"
                forceStatus="disabled"
                packageValue="{galileiModel>/packageValue}"
                packageStatus="{galileiModel>/packageStatus}"
                visible="{path:'workbenchEnv>/packages',formatter: '.displayPackageSelector'}"
              />
              <!-- Object status -->
              <Label
                text="{i18n_erd>@objectStatus}"
                visible="{= ${/showObjectStatus} === true ? true : false}"
              ></Label>
              <HBox
                id="objectStatus"
                visible="{= ${/showObjectStatus} === true ? true : false}"
              >
              <dos:DWCObjectStatus id="objectStatusText" class="sapUiTinyMarginEnd" statusType ="{galileiModel>/#objectStatus}"/>
              </HBox>
              <!--Truncate-->
              <Label text="{= ${featureflags>/DWCO_DELETE_DATA_LOCAL_HANA_TABLE} ? ${i18n>deleteAllBeforeLoading} : ${i18n>truncate}}"
  />
              <Switch
                id="truncateSelector"
                state="{/truncate}"
                change="onTruncateChange"
                enabled="{header>/editable}"
                visible="{/showTruncateMode}"
              />
              <!-- Delta-->
              <Label
                labelFor="deltaCaptureTF"
                text="{i18n_erd>lblDelta}"
              />
              <Switch
                id="deltaCaptureTF"
                state="{/deltaCaptureState}"
                enabled='{/isDeltaCaptureEditable}'
                change="onDeltaCaptureChange"
              />

              <Label
                labelFor="deltaCaptureTableName"
                text="{i18n>deltaCaptureTableName}"
              />
              <Input
                id="deltaCaptureTableName"
                value="{/deltaCaptureTableName}"
                editable="false"
                visible='{= ${/showDeltaCaptureTableName} === true ? true : false}'
              />
              <Label
              labelFor="storage"
              text="{i18n>storage}" />
                <Input
                  id="storage"
                  maxLength="1000"
                  value="{/storage}"
                  editable="false"
                  visible='{= ${/showStorageInfo} === true ? true : false}'
                />
            </f:content>
          </f:SimpleForm>
        </Panel>
        <!-- Mappings -->
        <core:Fragment
          fragmentName="sap.cdw.components.transformationflow.properties.fragment.TargetMappingPanel"
          type="XML"
        />
        <!-- Incremental Aggregation -->
        <core:Fragment
          fragmentName="sap.cdw.components.transformationflow.properties.fragment.IncrementalAggregationPanel"
          type="XML"
        />
        <!-- Columns -->
        <core:Fragment
          fragmentName="sap.cdw.components.transformationflow.properties.fragment.ColumnsPanel"
          type="XML"
        />

      </VBox>
    </VBox>
  </VBox>
</mvc:View>
