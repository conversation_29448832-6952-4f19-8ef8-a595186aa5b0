<mvc:View
  xmlns:core="sap.ui.core"
  xmlns="sap.m"
  xmlns:mvc="sap.ui.core.mvc"
  xmlns:er="sap.cdw.components.ermodeler.control"
  xmlns:f="sap.ui.layout.form"
  xmlns:pkgs="sap.skyline.tools.packageselector"
  xmlns:dos="sap.cdw.components.reuse.control.dwcobjectstatus"
  controllerName="sap.cdw.components.transformationflow.properties.SourceTableNodeProperties"
  class="propertyPanel--content"
>
  <VBox class="propertyPanel--vbox">
    <HBox
      fitContainer="true"
      class="propertyPanelHeader"
      justifyContent="SpaceBetween"
    >
      <Text
        id="propertyPanelHeaderText"
        class="propertyPanelTitle propertyPanelHeaderLeftMargin"
        text="{
          parts: [
            {path: 'galileiModel>/classDefinition/name'},
            {path: 'galileiModel>/'}
          ],
          formatter: '.propertyPanelHeaderTextFormatter'
        }"
      >
        <layoutData>
          <FlexItemData growFactor="1" />
        </layoutData>
      </Text>
      <Button
        class="propertyPanelFullscreen"
        id="toggleFullScreen"
        type="Emphasized"
        icon="{path:'workbenchEnv>/panels/right/mode', formatter:'.fullScreenIconFormatter'}"
        press="onToggleFullScreen"
      ></Button>
    </HBox>
    <VBox class="propertyPanel--scrollable">
      <VBox>
        <er:PropertyDetailsHeader
          id="detailsHeader"
          objectName="{header>/displayName}"
          className="{path:'galileiModel>/', formatter:'.classNameFormatter'}"
          columnCount="{galileiModel>/columnsCount}"
          validationButtonType="{path:'galileiModel>/aggregatedValidations/validations', formatter:'.aggregationValidationType'}"
          validationButtonIconCustomStyleClass="{path:'galileiModel>/aggregatedValidations/validations', formatter:'.aggregationValidationTypeCustomStyle'}"
          validationButtonText="{path:'galileiModel>/aggregatedValidations/validations', formatter:'.aggregationValidationCount'}"
          validationButtonPress="onShowValidations"
        />
      </VBox>
      <VBox>
        <!-- General section -->
        <Panel
          id="GeneralPanel"
          class="generalPanel"
          expandable="true"
          expanded="true"
          headerText="{i18n>general}"
        >
          <headerToolbar>
            <Toolbar height="48px">
              <content>
                <Title text="{i18n>general}"></Title>
                <ToolbarSpacer />
              </content>
            </Toolbar>
          </headerToolbar>
          <f:SimpleForm
            class="propertiesForm"
            layout="ResponsiveGridLayout"
            labelSpanXL="3"
            labelSpanL="3"
            labelSpanM="3"
            labelSpanS="12"
            adjustLabelSpan="true"
            emptySpanXL="4"
            emptySpanL="4"
            emptySpanM="4"
            emptySpanS="0"
            columnsXL="1"
            columnsL="1"
            columnsM="1"
            singleContainerFullSize="false"
            editable="false"
          >
            <f:content>
              <!-- Business Name -->
              <Label text="{i18n>businessName}"></Label>
              <Input
                id="bname"
                value="{galileiModel>/businessName}"
                enabled="false"
              />
              <!-- Technical Name -->
              <Label text="{i18n>technicalName}"></Label>
              <Input
                id="technicalNameInput"
                value="{galileiModel>/displayTechnicalName}"
                enabled="false"
              />
              <!-- Deployment status -->
              <Label
                id="deployStatusLabel"
                text="{i18n_erd>@objectStatus}"
              ></Label>
              <HBox id="objectStatus">
                <dos:DWCObjectStatus id="objectStatusText" class="sapUiTinyMarginEnd" statusType ="{galileiModel>/#objectStatus}"/>
              </HBox>
              <!-- Delta capture switch -->
              <Label
                labelFor="deltaCapture"
                text="{i18n_erd>lblDelta}"
                visible="{= ${galileiModel>/isDeltaTable} ? true : false }"
              />
              <Switch
                id="deltaCapture"
                state="{galileiModel>/isDeltaTable}"
                enabled="false"
                visible="{= ${galileiModel>/isDeltaTable} ? true : false }"
                class="sapUiTinyMarginBottom"
              />
              <!-- Delta Capture Table Name -->
              <Label
                text="{i18n>deltaCaptureTableName}"
                visible="{= ${galileiModel>/isDeltaTable} ? true : false }"
              ></Label>
              <Input
                id="deltaCaptureName"
                editable="false"
                enabled="false"
                value="{galileiModel>/deltaTableName}"
                visible="{= ${galileiModel>/isDeltaTable} ? true : false }"
              />
              <!--Storage Type-->
              <Label
                text="{i18n_erd>@txtStorageType}"
                visible="{= ${galileiModel>/isLTF} ? true : false }"
              />
              <Input
                enabled="false"
                editable="false"
                value="{i18n_erd>fileStorageText}"
                visible="{= ${galileiModel>/isLTF} ? true : false }"
              />
            </f:content>
          </f:SimpleForm>
        </Panel>
        <!-- Delta settings -->
          <mvc:XMLView
            id="deltaSettingsPanel"
            viewName="sap.cdw.components.transformationflow.properties.PrimaryEditorDeltaSettings"
          >
          </mvc:XMLView>

        <!-- Columns -->
        <core:Fragment
          fragmentName="sap.cdw.components.transformationflow.properties.fragment.ColumnsPanel"
          type="XML"
        />
      </VBox>
    </VBox>
  </VBox>
</mvc:View>
