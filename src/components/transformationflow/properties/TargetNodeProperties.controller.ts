/**
 * Copyright 2023 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { smartExtend } from "../../basecomponent/controller/BaseController.controller";
import { DataWarehouse } from "../../commonmodel/csn/csnAnnotations";
import { NameUsage } from "../../commonui/utility/NameInputValidator";
import { MessageHandler } from "../../reuse/utility/MessageHandler";
import { NamingHelper } from "../../reuse/utility/NamingHelper";
import Constants from "../js/utility/Constants";
import Utils from "../js/utils";
import {
  TransformationFlowCommonProperties,
  TransformationFlowCommonPropertiesClass,
} from "./TransformationFlowCommonProperties.controller";

export class TargetNodePropertiesClass extends TransformationFlowCommonPropertiesClass {
  technicalNameValidationTimer: any;

  public refreshModel() {
    if (!this.nodeObject) {
      // undo / redo causing empty node to get created due to the way we create nodes on drag and drop
      return;
    }
    const model = {
      supportTableMapping: false,
      showMappingControl: false, // hide for new table
      showObjectStatus: !this.nodeObject.isNew,
      inputDisplayName: "",
      outputDisplayName: this.nodeObject.nodeDisplayName,
      inputColumns: [],
      outputColumns: [],
      mappings: [],
      truncate: this.nodeObject.truncate,
      technicalName: this.nodeObject.technicalName,
      deltaCaptureState: false,
      deltaCaptureTableName: "",
      showDeltaCaptureTableName: false,
      isDeltaCaptureEditable: false,
      storage: "",
      showStorageInfo: false,
      showTruncateMode: true,
    };

    const columnsModel = {
      columns: [],
      showInfo: true,
      showMore: false,
      isEditable: false,
      mode: "MultiSelect",
    };

    columnsModel.columns = this.nodeObject.getAttributesArray();
    const sourceNode = this.nodeObject.getSourceNode();
    // Show storage as file for LTF tables in large system space
    if (this.nodeObject.container?.isLargeSystemSpace) {
      const ltfAnnotation = this.nodeObject?.definition?.[DataWarehouse.persistence_hdlf_tableFormat];
      model.showTruncateMode = false;
      if (ltfAnnotation) {
        model.showStorageInfo = true;
        model.storage = Constants.FileStorage;
      }
    }
    if (!this.nodeObject.isNew && !this.nodeObject.isTemplate) {
      model.supportTableMapping = true;
      model.outputColumns = columnsModel.columns;
      model.mappings = this.nodeObject.attributeMappings.toArray();
      if (!sourceNode.isTemplate) {
        model.inputDisplayName = sourceNode.nodeDisplayName;
        model.showMappingControl = true; // show mapping control only when it is connected
        model.inputColumns = sourceNode.attributes.toArray();
      }
      // sets delta table information
      if (this.nodeObject.isDeltaTable) {
        model.deltaCaptureState = true;
        model.showDeltaCaptureTableName = true;
      }
    }
    if (this.nodeObject.isNew) {
      if (this.nodeObject.container?.isLargeSystemSpace) {
        model.isDeltaCaptureEditable = false;
        model.deltaCaptureState = true;
        model.showStorageInfo = true;
        model.storage = Constants.FileStorage;
      }
      const oFeatures = sap.ui.getCore().getModel("featureflags").getData();
      if (!this.nodeObject.container?.isLargeSystemSpace) {
        model.isDeltaCaptureEditable = true;
      }

      if (this.nodeObject.uniqueTechnicalName === undefined) {
        // do validation for the first time
        this.validateTechnicalName(this.nodeObject.technicalName);
      } else if (this.nodeObject.uniqueTechnicalName === false) {
        this.updateTechnicalNameInputStatus(false, this.nodeObject.technicalName);
      } else if (this.nodeObject.uniqueTechnicalName === true) {
        this.updateTechnicalNameInputStatus(true);
      }
      const isDeltaTable = this.nodeObject.isTargetDelta;
      if (isDeltaTable) {
        model.deltaCaptureState = true;
        model.showDeltaCaptureTableName = true;
        model.deltaCaptureTableName = Utils.generateDeltaCaptureTableName(this.nodeObject.technicalName);
        if (this.nodeObject.uniqueDeltaCaptureTableName === undefined) {
          // do validation for the first time
          this.validateDeltaCaptureTableName(model.deltaCaptureTableName);
        }
      }
    } else {
      if (this.nodeObject.isDeltaTable) {
        // show actual technical name as delta capture table name
        model.deltaCaptureTableName = this.nodeObject.technicalName;
        // show active records view name technical name
        model.technicalName = Utils.getAllActiveRecordsViewName(this.nodeObject.definition);
      }
    }

    const oModel = new sap.ui.model.json.JSONModel(model);
    oModel.setSizeLimit(1000);
    const oColumnModel = new sap.ui.model.json.JSONModel(columnsModel);
    this.getView().setModel(oColumnModel, "columnsModel");
    this.getView().setModel(oModel);
    const mappingsPanel = this.byId("mappings") as sap.cdw.components.reuse.control.tokensmapping.TokensMapping;
    // eslint-disable-next-line no-underscore-dangle
    // eslint-disable-next-line no-underscore-dangle
    mappingsPanel["_resetSort"]();
  }

  /**
   * Sets object model
   * @param {sap.cdw.transformationflow.Node} oObject
   * @memberof TargetNodePropertiesClass
   */
  setObjectModel(oObject: sap.cdw.transformationflow.Node) {
    // clear validation on technical name when the user selects new node
    this.clearTechnicalNameInputStatus();
    super.setObjectModel(oObject);
  }

  /**
   * Create mapping from source to target column
   * @param {*} source
   * @param {*} target
   * @memberof TargetNodePropertiesClass
   */
  public createMapping(source, target): void {
    const sourcePath = source && source.getBindingContextPath && source.getBindingContextPath();
    const targetPath = target && target.getBindingContextPath && target.getBindingContextPath();
    const oModel = this.getView().getModel();
    const sourceColumn = sourcePath && oModel.getProperty(sourcePath);
    const targetColumn = targetPath && oModel.getProperty(targetPath);
    const oFeatures = sap.ui.getCore().getModel("featureflags").getData();
    // Check for datatype match
    if (sourceColumn && targetColumn) {
      const diagramBuilder = this.getDiagramBuilder();
      const outputMapping = diagramBuilder.getAttributeMapping(
        { target: targetColumn },
        this.nodeObject.attributeMappings
      );
      if (outputMapping) {
        // show message toast if there is already a mapping to the output column from same input
        sap.m.MessageToast.show(this.localizeText("i18n", "mappingExists"));
      } else {
        const value = Utils.validateTargetColumnMapping(sourceColumn, targetColumn);
        const hasDeltaUpsertBlockDeltaBleeding = oFeatures?.DWCO_TABLE_DELTA_UPSERT_BLOCK_DELTA_BLEEDING;
        let sourceTimeStampColumnNames: string[];
        let targetTimeStampColumnNames: string[];

        if (hasDeltaUpsertBlockDeltaBleeding) {
          sourceTimeStampColumnNames = this.nodeObject.getSourceNode().getTimestampColumns();
          targetTimeStampColumnNames = this.nodeObject.getTimestampColumns();
        }
        if (value === Constants.Mapping.InvalidTimestampColumn) {
          MessageHandler.uiError(
            this.localizeText("i18n", "validationSouceTargetColumnMappingIncompatibleTimestamp", [
              sourceTimeStampColumnNames,
              targetTimeStampColumnNames,
            ]),
            null,
            null,
            null,
            null,
            null,
            null,
            "cdcColumnMismatchErrorMsgBox"
          );
        } else if (value !== Constants.Mapping.Invalid && value !== Constants.Mapping.InvalidTimestampColumn) {
          diagramBuilder.getResource().applyUndoableAction(() => {
            diagramBuilder.createAttributeMapping({ source: sourceColumn, target: targetColumn }, this.nodeObject);
          });
          this.validateModel();
          this.reloadMappings();
        } else {
          MessageHandler.uiError(
            this.localizeText("i18n", "datatypeMismatchTarget", [
              sourceColumn.name,
              sourceColumn.primitiveDisplayType,
              targetColumn.name,
              targetColumn.primitiveDisplayType,
            ]),
            null,
            null,
            null,
            null,
            null,
            null,
            "datatypeMismatchErrorMsgBox"
          );
        }
      }
    }
  }

  /**
   * Handles auto mapping of source and target columns
   *
   * @memberof TargetNodePropertiesClass
   */
  public onAutoMap() {
    let autoMappedColumns = false;
    const diagramBuilder = this.getDiagramBuilder();
    diagramBuilder.getResource().applyUndoableAction(() => {
      autoMappedColumns = diagramBuilder.autoMapTargetColumns(this.nodeObject);
    });
    if (!autoMappedColumns) {
      sap.m.MessageToast.show(this.localizeMessage("i18n", "autoMapInvalid"));
    }
    this.validateNode();
    this.reloadMappings();
  }

  /**
   * update mappings
   *
   * @private
   * @memberof TargetNodePropertiesClass
   */
  public reloadMappings() {
    const oModel = this.getView().getModel() as sap.ui.model.json.JSONModel;
    const mappings = this.nodeObject ? this.nodeObject.attributeMappings.toArray() : [];
    oModel.setProperty("/mappings", mappings);
  }

  /**
   *
   *
   * @param {sap.ui.base.Event} oEvent
   * @memberof TargetNodePropertiesClass
   */
  public onTruncateChange(oEvent: sap.ui.base.Event) {
    this.nodeObject.truncate = oEvent.getParameters().state;
    this.getView().getModel().setProperty("/truncate", this.nodeObject.truncate);
    this.validateNode();
  }

  /**
   * Updates business name on focus out
   * @param {sap.ui.base.Event} oEvent
   * @memberof TargetNodePropertiesClass
   */
  public onBusinessNameChanged(oEvent: sap.ui.base.Event) {
    const nameValidator = NamingHelper.getNameInputValidator();
    const input = oEvent.getSource();
    input["setValueState"](sap.ui.core.ValueState.None);
    const technicalNameInput = this.getView().byId("technicalName") as sap.m.Input;
    // eslint-disable-next-line dot-notation
    const technicalName = technicalNameInput["newValue"];
    const correctedValue = nameValidator.deriveTechnicalName(input["lastValue"], NameUsage.entity);
    const diagramBuilder = this.getDiagramBuilder();
    diagramBuilder.getResource().applyUndoableAction(
      () => {
        if (input["getValue"]() === "") {
          if (technicalNameInput.getValue() === "") {
            if (technicalName) {
              this.getView().getModel("galileiModel").setProperty("/technicalName", technicalName);
              this.nodeObject.technicalName = technicalName;
              this.validateTechnicalName(technicalName);
              this.validateDeltaCaptureTableName(Utils.generateDeltaCaptureTableName(technicalName));
            } else {
              this.getView().getModel("galileiModel").setProperty("/technicalName", correctedValue);
              this.nodeObject.technicalName = correctedValue;
              this.validateTechnicalName(correctedValue);
              this.validateDeltaCaptureTableName(Utils.generateDeltaCaptureTableName(correctedValue));
            }
          }
          input["setValue"](input["lastValue"]);
          this.nodeObject.businessName = input["lastValue"];
          this.nodeObject.displayName = input["lastValue"];
        }
        if (this.nodeObject.isNew) {
          this.nodeObject.displayName = input["getValue"]();
          this.nodeObject.businessName = input["getValue"]();
        }
        this.validateNode();
        this.updateDeltaCaptureTableName();
      },
      "update businessName and technicalName",
      true
    );
    if (input["oldValue"]) {
      input["oldValue"] = undefined;
    }
  }

  /**
   * Validates business name on live change
   * @param {sap.ui.base.Event} oEvent
   * @memberof TargetNodePropertiesClass
   */
  public onBusinessNameLiveChange(oEvent: sap.ui.base.Event) {
    const nameValidator = NamingHelper.getNameInputValidator();
    oEvent.getSource()["setValueState"](sap.ui.core.ValueState.None);
    const technicalNameInput = this.getView().byId("technicalName") as sap.m.Input;
    const model = this.getView().getModel("galileiModel").getProperty("/");
    nameValidator.onBusinessNameChange(oEvent, technicalNameInput, NameUsage.entity, undefined, model.technicalName);
    // update business name in the config untill the table is created
    const newName = oEvent.getSource()["getValue"]();
    const diagramBuilder = this.getDiagramBuilder();

    if (this.technicalNameValidationTimer) {
      clearTimeout(this.technicalNameValidationTimer);
    }
    this.technicalNameValidationTimer = setTimeout(() => {
      diagramBuilder.getResource().applyUndoableAction(
        () => {
          this.clearTechnicalNameInputStatus(); // clear the validation status
          this.validateTechnicalName(model.technicalName);
          this.updateDeltaCaptureTableName();
          this.validateDeltaCaptureTableName(Utils.generateDeltaCaptureTableName(model.technicalName));
        },
        "validate technicalName",
        true
      );
    }, 500);
    if (newName === "") {
      oEvent.getSource()["setValueState"](sap.ui.core.ValueState.Error);
      oEvent.getSource()["setValueStateText"](this.localizeText("i18n", "validationEmptyBusinessName"));
    }
  }

  /**
   * Validates target table DeltaCaptureTableName
   * @param {string} deltaCaptureTableName
   * @memberof TargetNodePropertiesClass
   */
  async validateDeltaCaptureTableName(deltaCaptureTableName: string) {
    if (this.nodeObject?.isDeltaTable) {
      const unique = await Utils.checkUniqueness(deltaCaptureTableName, true, this.getSpaceName());
      if (unique) {
        this.nodeObject.uniqueDeltaCaptureTableName = true;
      } else {
        this.nodeObject.uniqueDeltaCaptureTableName = false;
      }
      this.validateNode();
    }
  }

  /**
   * clears technical name value state
   * @memberof TargetNodePropertiesClass
   */
  clearTechnicalNameInputStatus() {
    const technicalNameInput = this.getView().byId("technicalName") as sap.m.Input;
    technicalNameInput.setValueState(sap.ui.core.ValueState.None);
  }

  /**
   * Validates target table technical name
   * @param {string} technicalName
   * @memberof TargetNodePropertiesClass
   */
  async validateTechnicalName(technicalName: string) {
    this.updateTechnicalNameInputStatus(true);
    const unique = await Utils.checkUniqueness(technicalName);
    if (unique) {
      this.nodeObject.uniqueTechnicalName = true;
    } else {
      this.nodeObject.uniqueTechnicalName = false;
      this.updateTechnicalNameInputStatus(false, technicalName);
    }
    this.validateNode();
  }

  /**
   * Updates technical name value state
   * @param {boolean} valid
   * @param {string} technicalName
   * @memberof TargetNodePropertiesClass
   */
  updateTechnicalNameInputStatus(valid: boolean, technicalName?: string) {
    const technicalNameInput = this.getView().byId("technicalName") as sap.m.Input;
    const status = technicalNameInput.getValueState();
    if (technicalNameInput && status !== "Error") {
      if (valid) {
        technicalNameInput.setValueState(sap.ui.core.ValueState.None);
      } else {
        technicalNameInput.setValueState(sap.ui.core.ValueState.Error);
        technicalNameInput.setValueStateText(
          this.localizeText("i18n", "validationDuplicateTargetTechnicalName", [technicalName])
        );
      }
    }
  }

  /**
   * Validates technical name on live change
   * @param {sap.ui.base.Event} oEvent
   * @memberof TargetNodePropertiesClass
   */
  public onTechnicalNameLiveChange(oEvent: sap.ui.base.Event) {
    oEvent.getSource()["setValueState"](sap.ui.core.ValueState.None);
    const nameValidator = NamingHelper.getNameInputValidator();
    const technicalName = oEvent.getSource()["getValue"]();
    // it validates the technical name and removes special characters
    nameValidator.onTechnicalNameChange(oEvent, NameUsage.entity);
    const newName = oEvent.getSource()["getValue"]();
    const technicalNameInput = this.getView().byId("technicalName") as sap.m.Input;
    const diagramBuilder = this.getDiagramBuilder();
    diagramBuilder.getResource().applyUndoableAction(
      () => {
        let defaultValidationError = false;
        if (technicalNameInput.getValueState() === sap.ui.core.ValueState.Error) {
          defaultValidationError = true;
        }
        if (defaultValidationError) {
          // defer unique name check if default validation error is shown
          setTimeout(() => {
            this.validateTechnicalName(newName);
            this.validateDeltaCaptureTableName(Utils.generateDeltaCaptureTableName(newName));
          }, 2000);
        } else {
          this.validateTechnicalName(newName);
          this.validateDeltaCaptureTableName(Utils.generateDeltaCaptureTableName(newName));
        }
        this.updateDeltaCaptureTableName();
      },
      "update technicalName",
      true
    );
    if (technicalName === "") {
      technicalNameInput.setValueState(sap.ui.core.ValueState.Error);
      technicalNameInput.setValueStateText(this.localizeText("i18n", "validationEmptyTechnicalName"));
    }
  }

  /**
   * updates technical name on focus out
   * @param {sap.ui.base.Event} oEvent
   * @memberof TargetNodePropertiesClass
   */
  public onTechnicalNameChanged(oEvent: sap.ui.base.Event) {
    const input = oEvent.getSource();
    const diagramBuilder = this.getDiagramBuilder();
    diagramBuilder.getResource().applyUndoableAction(
      () => {
        if (input["getValue"]() === "" && input["lastValue"]) {
          input["setValue"](input["lastValue"]);
          this.nodeObject.technicalName = input["lastValue"];
          setTimeout(() => {
            input["setValueState"](sap.ui.core.ValueState.None);
            this.validateTechnicalName(input["lastValue"]);
            this.validateDeltaCaptureTableName(Utils.generateDeltaCaptureTableName(input["lastValue"]));
          }, 2000);
        }
        this.updateDeltaCaptureTableName();
      },
      "update technical name",
      true
    );
    if (input["oldValue"]) {
      input["oldValue"] = undefined;
    }
  }

  public onCreateNewTargetButtonPress() {
    this.getEditorController()["createNewTarget"](this.nodeObject);
  }

  private updateDeltaCaptureTableName() {
    const model = this.getView().getModel();
    model.setProperty("/deltaCaptureTableName", Utils.generateDeltaCaptureTableName(this.nodeObject.technicalName));
  }

  /**
   * display package selector
   * @param {any[]} packages
   * @returns {boolean}
   */
  public displayPackageSelector(packages: any[]) {
    // packages list has at least "none" element, so when the length equals one that means no other package can be selected
    // at this scenario we should hide this package selector
    if (packages?.length > 1) {
      return true;
    } else {
      return false;
    }
  }

  /**
   * capture delta setting change for target table
   * @param {sap.ui.base.Event} oEvent
   */
  public onDeltaCaptureChange(oEvent: sap.ui.base.Event) {
    const oModel = this.getView().getModel().getData();
    this.getDiagramBuilder()
      .getResource()
      .applyUndoableAction(() => {
        oModel.deltaCaptureState = oEvent.getParameters().state;
        this.nodeObject.isTargetDelta = oEvent.getParameters().state;
        if (oEvent.getParameters().state) {
          this.addCDCColumns();
        } else {
          this.getDiagramBuilder().deleteCDCColumns(this.nodeObject);
        }
      }, "capture delta change");
    this.reloadColumns();
    this.refreshModel();
  }

  /**
   * Adds CDC columns to the column list
   */
  public addCDCColumns() {
    const viewTransformNode = this.nodeObject.getViewTransformNode();
    if (viewTransformNode.isDeltaTable && viewTransformNode?.attributes?.length) {
      const sourceCDCColumns: sap.cdw.transformationflow.Attribute[] = viewTransformNode.attributes.filter(
        (column: sap.cdw.transformationflow.Attribute) => column.isCDCColumn === true
      );
      this.getDiagramBuilder().addSourceCDCColumnsToTarget(sourceCDCColumns, this.nodeObject);
    } else {
      this.getDiagramBuilder().addDefaultCDCColumns(this.nodeObject);
    }
  }
}

export const NodeProperties = smartExtend(
  TransformationFlowCommonProperties,
  "sap.cdw.components.transformationflow.properties.TargetNodeProperties",
  TargetNodePropertiesClass
);

sap.ui.define("sap/cdw/components/transformationflow/properties/TargetNodeProperties.controller", [], function () {
  return NodeProperties;
});
