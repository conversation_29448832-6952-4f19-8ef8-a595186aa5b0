<core:FragmentDefinition
  xmlns:core="sap.ui.core"
  xmlns="sap.m"
  xmlns:tokenListItem="sap.cdw.components.tokenListItem.control"
>
  <Panel
    id="IncrementalAggregationPanel"
    expanded="true"
    class="NodeColumnsPanel"
    visible="{parts: [{path:'featureflags>/DWCO_TRF_SPARK_AGGR'},{path: 'galileiModel>/container/isLargeSystemSpace'}, {path: 'galileiModel>/container/nodes/0/isTemplate'}], formatter:'.showAggregationPanel'}"
    backgroundDesign="{path: 'workbenchEnv>/panels/right/mode', formatter:'.showPanelSeparator'}"
  >
    <headerToolbar>
      <OverflowToolbar
        height="40px"
        enabled="{header>/editable}"
      >
        <Title
          id="incrementalAggregationTitle"
          level="H3"
          text="{i18n>incrementalAggregation}"
        />
        <ToolbarSpacer />
        <Button
          id="editAggregationsButton"
          tooltip="{i18n>editAggregations}"
          icon="sap-icon://edit"
          press="onPressEditAggregations"
        ></Button>
      </OverflowToolbar>
    </headerToolbar>
    <VBox
      visible="true"
      height="100%"
      class="NodeVBoxContainer"
    >
      <ScrollContainer
        vertical="true"
        height="100%"
      >
        <List
          id="aggregationColumns"
          growing="true"
          growingThreshold="50"
          inset="false"
          items="{path:'/aggregationColumns', templateShareable:true}"
          class="NodeElementsTable"
          rememberSelections="false"
          noDataText="{i18n>defineAggregations}"
          showSeparators="None"
          growingFinished="onGrowingFinished"
        >
          <tokenListItem:TokenListItem
            title="{displayName}"
            showDescription="false"
            icon="{path:'primitiveDataType', formatter:'.dataTypeIconFormatter'}"
            hideActionButtonsOnMouseOut="true"
          >
            <tokenListItem:rightButton>
              <HBox>
                <Label
                  id="aggregationColumnMenu"
                  text="{path:'aggregation', formatter: '.aggregationDisplayTextFormatter'}"
                  class="tf-token-label"
                >
                </Label>
              </HBox>
            </tokenListItem:rightButton>
            <tokenListItem:customData>
              <core:CustomData
                key="column"
                value="{displayName}"
              />
            </tokenListItem:customData>
          </tokenListItem:TokenListItem>
        </List>
      </ScrollContainer>
    </VBox>
  </Panel>

</core:FragmentDefinition>
