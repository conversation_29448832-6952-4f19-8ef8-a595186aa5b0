<!-- @format -->

<core:FragmentDefinition xmlns="sap.m" xmlns:core="sap.ui.core">
  <Dialog
    id="incrementalMaintenance"
    title="{i18n>incrementalAggregation}"
    draggable="true"
    contentHeight="40%"
    contentWidth="70%"
    class="sapUiResponsiveContentPadding"
    afterClose="onAfterCloseAggregationDialog"
  >
    <content>
      <MessageStrip
      id="aggregationWarningMsgStrip"
      text="{i18n>validationAggregation}"
      type="Warning"
      showIcon="true"
      showCloseButton="false"
    >
  </MessageStrip>
      <Toolbar
        width="100%"
        class="sapUiSmallMarginTop"
      >
      <ToolbarSpacer />
   
        <SearchField
          id="aggregationSearchField"
          placeholder="{i18n>search}"
          liveChange="onAggregationSearch"
          width="25%"
        />
        <Button id="resetAggregation" text="{i18n>resetAggregations}" press="onResetAggregations" type="Transparent"/>
      </Toolbar>
      <Table
        id="aggregationList"
        items="{path: 'aggregationsModel>/columns'}"
        growing="false"
        contentWidth="50%"
        contentHeight="50%"
        noDataText="{i18n>noMappedColumns}"
      >
        <columns>
          <Column>
            <Label text="{i18n>businessName}" />
          </Column>
          <Column>
            <Label text="{i18n>technicalName}" />
          </Column>
          <Column>
            <Label text="{i18n>columnDataType}" />
          </Column>
          <Column>
            <Label text="{i18n>aggregationType}" />
          </Column>
        </columns>
        <items>
          <ColumnListItem>
            <cells>
              <Text text="{aggregationsModel>label}" />
              <Text text="{aggregationsModel>name}" />
              <Button id="columndatatype" text="{aggregationsModel>primitiveDisplayType}" enabled="false"/>
              <ComboBox
              id="columnAggregation"
              selectedKey="{aggregationsModel>aggregation}"
              items="{path:'aggregationsModel>/aggregationsList', templateShareable:false}"
              change="onAggregationChange"
              enabled="{path:'aggregationsModel>datatype', formatter:'.isAggregationEnabledFormatter'}"
            >
              <core:Item
                key="{aggregationsModel>key}"
                text="{aggregationsModel>name}"
              />
            </ComboBox>
            </cells>
          </ColumnListItem>
        </items>
      </Table>
    </content>
    <beginButton>
      <Button id="aggregationsSave" text="{i18n>save}" press="onSaveAggregations" type="Emphasized"></Button>
    </beginButton>
    <endButton>
      <Button id="aggregationsCancel" text="{i18n>cancel}" press="onCancelAggregations" type="Transparent"></Button>
    </endButton>
  </Dialog>
</core:FragmentDefinition>
