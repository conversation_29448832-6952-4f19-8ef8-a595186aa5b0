/**
 * Copyright 2023 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import ParameterUtils from "../../../../shared/transformationflow/parameterUtils";
import { SidepanelMode } from "../../abstractbuilder/api";
import { isSACPlanningIntegrationEnabled } from "../../abstractbuilder/commonservices/FeatureFlagCheck";
import {
  AbstractDiagramEditor,
  AbstractDiagramEditorClass,
} from "../../abstractbuilder/controller/AbstractDiagramEditor.controller";
import { getObjectFilesProperties, updateObjectFileInfo } from "../../abstractbuilder/utility/RepositoryUtils";
import { smartExtend } from "../../basecomponent/controller/BaseController.controller";
import { DeploymentStatus } from "../../commonmodel/api/DocumentStorageService";
import { DataWarehouse } from "../../commonmodel/csn/csnAnnotations";
import { MessageHandler } from "../../reuse/utility/MessageHandler";
import { ContentType, ServiceCall } from "../../reuse/utility/ServiceCall";
import { ObjectStatus } from "../../reuse/utility/Types";
import { showDialog } from "../../reuse/utility/UIHelper";
import { Crud } from "../../shell/utility/Crud";
import { Repo } from "../../shell/utility/Repo";
import { buildSqlFromSelectCqn } from "../../sqleditor/utility/SqlEditorUtility";
import { ApplicationId } from "../../tasklog/utility/Constants";
import { TransformationFlowEditorComponentClass } from "../Component";
import { TFDiagramEditorControl } from "../control/TFDiagramEditor";
import { library } from "../js/library";
import Constants from "../js/utility/Constants";
import Utils from "../js/utils";

export interface IParameterTF {
  name: string;
  value: string;
  type?: string;
}
export interface IRunTransformationFlowPayload {
  spaceId: string;
  objectId: string;
  applicationId: ApplicationId;
  activity: string;
  parameters?: {
    flowParameters: {
      [key: string]: string;
    };
  };
}

export class TFModelerClass extends AbstractDiagramEditorClass {
  public operatorLibrary: any;
  protected DIAGRAM_EDITOR_CHANNEL = "sap.cdw.transformationflow.ui.DiagramEditorChannel";
  private changeManagementDialogOpeningOrOpen = false;
  private getDeploymentStatusRetries = 0;
  private updateObjectsFileInfoTimer: any;
  private confirmRunDeployedVersionDialog: any;
  public tfDelayedPreview: any;
  private runWithParameterFragment: sap.m.Dialog;

  public onInit() {
    require("../css/style.css");
    super.onInit();
    this.onDefaultInit();

    const bundleName = require("../i18n/i18n.properties");
    this.resourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: bundleName,
    });
    this.getView().setModel(this.resourceModel, "i18n");
    this.operatorLibrary = library;

    if (this.propertyPaneController) {
      this.registerPropertyPanels();
    }
  }

  public onAfterRendering(): void {
    this.onDefaultAfterRendering();
    this.getDeploymentStatusRetries = 0;
    const oModel: any = this.getGalileiModel();
    if (oModel) {
      if (oModel?.isNew) {
        oModel.setObjectStatus(ObjectStatus.notDeployed); // set not deployed status for new flow
      } else {
        this.updateObjectsFileInfo();
      }
    }
  }

  public registerPropertyPanels(): void {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    this.propertyPaneController.registerObjectProperties(
      this.DIAGRAM_EDITOR_CHANNEL,
      "sap.cdw.transformationflow.Node",
      require("../properties/TargetNodeProperties.view.xml"),
      (objectInstance) => {
        if (objectInstance) {
          if (objectInstance.component) {
            switch (objectInstance.component) {
              case Constants.OPERATORS.TARGET:
                return require("../properties/TargetNodeProperties.view.xml");
              case Constants.OPERATORS.VIEW_TRANSFORM:
              case Constants.OPERATORS.SQL_TRANSFORM:
              case Constants.OPERATORS.SQL_SCRIPT_TRANSFORM:
                return require("../properties/ViewTransformNodeProperties.view.xml");
              case Constants.OPERATORS.PYTHON:
                return require("../properties/ScriptProperties.view.xml");
              case Constants.OPERATORS.SOURCE_TABLE:
                return require("../properties/SourceTableNodeProperties.view.xml");
            }
          }
        }
      }
    );

    this.propertyPaneController.registerObjectProperties(
      this.DIAGRAM_EDITOR_CHANNEL,
      "sap.cdw.transformationflow.Model",
      require("../properties/ModelProperties.view.xml")
    );
    this.propertyPaneController.registerObjectProperties(
      this.DIAGRAM_EDITOR_CHANNEL,
      "sap.cdw.transformationflow.SecondaryNode",
      require("../properties/SecondarySourceProperties.view.xml")
    );
  }

  /**
   * Localize given text id & parameters
   * @param text property id
   * @param parameters property parameters
   */
  public localizeText(text: string, parameters: any[] = []): string {
    return this.resourceModel.getResourceBundle().getText(text, parameters);
  }

  /**
   * Hides side panel when model is empty
   *
   * @protected
   * @param {sap.cdw.transformationflow.Model} model
   * @return {*}  {boolean}
   * @memberof TFModelerClass
   */
  protected isModelEmpty(): boolean {
    return false; // property panel should be visible by default
  }

  protected getEditorControlId(): string {
    return "transformationFlowEditorControl";
  }

  public async openChangeManagementInfoDialog(channel, event, data) {
    const model = data?.modifiedObjects?.length > 0 && data?.modifiedObjects[0].modifiedObject?.rootContainer;
    if (!model) {
      return;
    }
    if (!$("#" + this.getView().getId()).is(":hidden")) {
      // user already navigated elsewhere but GVB
      // eslint-disable-next-line dot-notation
      const firstModifiedObject = data.modifiedObjects[0].modifiedObject;
      if (
        firstModifiedObject.rootContainer.name === this.getOwnerComponent()["modelId"] ||
        sap.galilei.model.isInstanceOf(firstModifiedObject, "sap.cdw.querybuilder.Entity") ||
        sap.galilei.model.isInstanceOf(firstModifiedObject, "sap.cdw.querybuilder.Output")
      ) {
        if (!this.changeManagementDialogOpeningOrOpen) {
          this.setBusy(true);
          this.changeManagementDialogOpeningOrOpen = true;
          let spaceModels = [];
          // Retrieve objects from space
          Repo.getModelList(this.spaceName, ["id", "name", "modification_date", "#shared"]).then(async (models) => {
            if (models) {
              spaceModels = models.map((res) => ({
                // eslint-disable-next-line camelcase
                space_id: res.space_id,
                // eslint-disable-next-line camelcase
                qualified_name: res.qualified_name,
                modifier: res.owner,
                changedBy: res["#ownerBusinessName"], // modifier's display name
              }));
            }
            this.setBusy(false);
            await showDialog(
              require("../view/ChangeManagementInfo.view.xml"),
              this.getText("changeManagementTitleSourceOrTarget"),
              this.getText("ok"),
              undefined, // this.getText("cancel"),
              undefined,
              "65%", // width
              "60%", // height
              this.getView().getModel("i18n"),
              {
                changeManagement: data,
                spaceModels: spaceModels,
              },
              undefined // sap.ui.core.ValueState.Information,
            )
              .then(() => {
                // ok
              })
              .finally(() => {
                // dialog closed
                this.changeManagementDialogOpeningOrOpen = false;
              });
          });
        }
      }
    }
  }

  onSourceDragEnd(): void {
    const diagramEditorExtn = this.getDiagramEditor().extension;
    diagramEditorExtn.prevDraggingData = diagramEditorExtn.currentDraggingData;
    diagramEditorExtn.isDropComplete = false;
    diagramEditorExtn.dropStarted = true;
    if (this.isVisible()) {
      this.getDiagramEditor().selectPointerTool();
    }
    if (diagramEditorExtn.canDropSymbol) {
      this.completeDragEnd();
    }
    diagramEditorExtn.canDropSymbol = false;
    diagramEditorExtn.editor.unhighlightSymbol(diagramEditorExtn.symbolHighlighted);
    super.onSourceDragEnd();
  }

  async completeDragEnd() {
    const diagramEditorExtn = this.getDiagramEditor().extension;

    if (
      diagramEditorExtn.currentDraggingData.definitions &&
      typeof diagramEditorExtn.currentDraggingData.definitions.then === "function"
    ) {
      await diagramEditorExtn.currentDraggingData.definitions.then((res) => {
        diagramEditorExtn.currentDraggingData.definitions = res.csn?.definitions;
      });
    }

    if (Utils.isTableAsSourceSupportedSpark()) {
      await this.updateNodeOnDrop(diagramEditorExtn.currentDraggingData, diagramEditorExtn.symbolHighlighted);
    } else {
      await this.updateTargetNodeOnDrop(diagramEditorExtn.currentDraggingData, diagramEditorExtn.symbolHighlighted);
    }
    this.editorControl["onSourceDropCompletion"](diagramEditorExtn.currentDraggingData);
    diagramEditorExtn.isDropComplete = true;
    diagramEditorExtn.dropStarted = false;
    diagramEditorExtn.canDropSymbol = false;
    diagramEditorExtn.editor.unhighlightSymbol(diagramEditorExtn.symbolHighlighted);
    this.getDiagramEditor().selectSymbol(diagramEditorExtn.symbolHighlighted);
    setTimeout(() => {
      this.getDiagramEditor().drawAllSymbols();
    }, 100);
  }

  // function to be removed on Removal of FF DWCO_TRF_SPARK_PRI_ED_DND - DW18-699
  public async updateTargetNodeOnDrop(dropData: any, nodeSymbol: any) {
    const editor = this.getDiagramEditor();
    let details = Utils.getDWCMetadata(dropData, [], editor.model?.customTypesMap);
    if (!this.validateTarget(details)) {
      // invalid target
      return;
    }
    if (details.inValidColumns.length > 0) {
      const invalidColumnMsg = this.localizeText("invalidColumns");
      const invalidColumns = details.inValidColumns.join(",");
      const warningMsg = `${invalidColumnMsg}\n${invalidColumns}`;
      if (sap.m.MessageToast) {
        sap.m.MessageToast.show(warningMsg);
      }
    }
    let label = dropData.label;
    let name = dropData.name;
    let isDeltaTable = false;
    const deltaAnnotation = details.definition[DataWarehouse.delta];
    if (deltaAnnotation) {
      isDeltaTable = true;
      if (Array.isArray(deltaAnnotation.deltaFromEntities)) {
        // Active records view dropped.
        // find delta table
        const deltaEntityName = Utils.getDeltaEntityName(details.definition);
        if (dropData.definitions[deltaEntityName]) {
          // use definition from drop data
          dropData.name = deltaEntityName;
          details = Utils.getDWCMetadata(dropData, [], editor.model?.customTypesMap);
        } else {
          // fetch csn
          const object = await Utils.getRepositoryObject({ name: deltaEntityName }, this.getSpaceName());
          object.csn.name = deltaEntityName;
          details = Utils.getDWCMetadata(object.csn, [], editor.model?.customTypesMap);
        }
        label = details.definition["@EndUserText.label"];
        name = deltaEntityName;
      }
    }
    const oNode = nodeSymbol.object;

    this.updateTargetNode(oNode, { label, name, dropData, details, isDeltaTable });
    this.refreshProperty(oNode);
    this.getDiagramBuilder().validateModel();
  }

  /**
   * Updates the node on which entity has been dropped
   *
   * @param dropData
   * @param nodeSymbol
   */
  public async updateNodeOnDrop(dropData: any, nodeSymbol: any) {
    const editor = this.getDiagramEditor();
    let details = Utils.getDWCMetadata(dropData, [], editor.model?.customTypesMap);
    const oNode = nodeSymbol.object;
    if (oNode.name === Constants.PROCESS_IDS.TARGET) {
      if (!this.validateTarget(details)) {
        // invalid target
        return;
      }
      if (details.inValidColumns.length > 0) {
        const invalidColumnMsg = this.localizeText("invalidColumns");
        const invalidColumns = details.inValidColumns.join(",");
        const warningMsg = `${invalidColumnMsg}\n${invalidColumns}`;
        if (sap.m.MessageToast) {
          sap.m.MessageToast.show(warningMsg);
        }
      }
    }
    let label = dropData.label;
    let name = dropData.name;
    let isDeltaTable = false;
    const deltaAnnotation = details.definition[DataWarehouse.delta];
    if (deltaAnnotation) {
      isDeltaTable = true;
      if (Array.isArray(deltaAnnotation.deltaFromEntities)) {
        // Active records view dropped.
        // find delta table
        const deltaEntityName = Utils.getDeltaEntityName(details.definition);
        if (dropData.definitions[deltaEntityName]) {
          // use definition from drop data
          dropData.name = deltaEntityName;
          details = Utils.getDWCMetadata(dropData, [], editor.model?.customTypesMap);
        } else {
          // fetch csn
          const object = await Utils.getRepositoryObject({ name: deltaEntityName }, this.getSpaceName());
          object.csn.name = deltaEntityName;
          details = Utils.getDWCMetadata(object.csn, [], editor.model?.customTypesMap);
        }
        label = details.definition["@EndUserText.label"];
        name = deltaEntityName;
      }
    }

    this.updateNode(oNode, { label, name, dropData, details, isDeltaTable, highlightedSymbol: oNode.name });
    this.refreshProperty(oNode);
    this.getDiagramBuilder().validateModel();
  }

  // function to be removed on Removal of FF DWCO_TRF_SPARK_PRI_ED_DND - DW18-699
  updateTargetNode(oNode: sap.cdw.transformationflow.Node, oParam: any) {
    const oDiagramEditor = this.getDiagramEditor();
    oDiagramEditor.resource.applyUndoableAction(() => {
      // set template as false
      oNode.isTemplate = false;
      // refresh diagram
      this.getDiagramBuilder().checkTemplateModel();
      // update deployment status
      oNode.deploymentStatus = Utils.getObjectDeploymentStatus(oParam.dropData);
      // object status
      oNode["#objectStatus"] = oParam.dropData["#objectStatus"];
      if (!oNode["#objectStatus"] && oParam.dropData.isAlreadyDeployed) {
        oNode["#objectStatus"] = DeploymentStatus.Active as any;
      }
      // update business name and technical name
      oNode.businessName = oParam.label;
      oNode.technicalName = oParam.name;
      // update definition
      oNode.definition = oParam.details.definition;
      // update type
      oNode["#technicalType"] = oParam.dropData["#technicalType"];
      // update display name
      oNode.displayName = oNode.businessName;
      oNode.packageValue = oParam.dropData.packageValue;
      // create columns
      this.createOutputColumnsAndMappings(oNode, oParam.details);
      if (oParam.isDeltaTable) {
        oDiagramEditor.drawAllSymbols(); // update columns count and label in the node symbol
      }
      const oModel = this.getGalileiModel() as sap.cdw.transformationflow.Model;
      if (!oModel.isLoadTypeModifiedByUser) {
        Utils.setLoadType(oModel);
      }
    }, "update target node");
  }

  /**
   * updates the galilei model of the required node
   *
   * @param {sap.cdw.transformationflow.Node} oNode
   * @param {any} oParam
   */
  updateNode(oNode: sap.cdw.transformationflow.Node, oParam: any) {
    const oDiagramEditor = this.getDiagramEditor();
    oDiagramEditor.resource.applyUndoableAction(() => {
      // set template as false
      oNode.isTemplate = false;
      // refresh diagram
      this.getDiagramBuilder().checkTemplateModel();
      // update view transform node if it's a table as source
      if (Utils.isTableAsSourceSupportedSpark() && oParam.highlightedSymbol !== Constants.PROCESS_IDS.TARGET) {
        oNode.component = Constants.OPERATORS.SOURCE_TABLE;
        oNode.icon = "sap-icon://sac/table";
      }
      // update deployment status
      oNode.deploymentStatus = Utils.getObjectDeploymentStatus(oParam.dropData);
      // object status
      oNode["#objectStatus"] = oParam.dropData["#objectStatus"];
      if (!oNode["#objectStatus"] && oParam.dropData.isAlreadyDeployed) {
        oNode["#objectStatus"] = DeploymentStatus.Active as any;
      }
      // update business name and technical name
      oNode.businessName = oParam.label;
      oNode.technicalName = oParam.name;
      // update definition
      oNode.definition = oParam.details.definition;
      if (Utils.isTableAsSourceSupportedSpark() && oParam.highlightedSymbol !== Constants.PROCESS_IDS.TARGET) {
        if (oNode.definition[DataWarehouse.delta]) {
          oNode.definition[DataWarehouse.delta].deltaFromEntities = [oParam.name];
          oNode.displayTechnicalName = oNode.definition[DataWarehouse.deltaEnclosing];
        }
      }
      // update type
      oNode["#technicalType"] = oParam.dropData["#technicalType"];
      // update display name
      oNode.displayName = oNode.businessName;
      oNode.packageValue = oParam.dropData.packageValue;
      // create columns
      this.createOutputColumnsAndMappings(oNode, oParam.details);
      if (oParam.isDeltaTable) {
        if (Utils.isTableAsSourceSupportedSpark() && oParam.name !== Constants.PROCESS_IDS.TARGET) {
          oNode.isDeltaTable = true;
          oNode.useAs = "Delta";
          oNode.deltaTableName = oParam.name;
          oNode.cdcColumns = Utils.getCdcColumnsSourceTable(oNode);
        }
        oDiagramEditor.drawAllSymbols(); // update columns count and label in the node symbol
      }
      const oModel = this.getGalileiModel() as sap.cdw.transformationflow.Model;
      if (!oModel.isLoadTypeModifiedByUser) {
        Utils.setLoadType(oModel);
      }
      if (oParam.highlightedSymbol === Constants.PROCESS_IDS.VIEW_TRANSFORM) {
        oNode.name = Constants.PROCESS_IDS.SOURCE_TABLE;
      }
    }, "update node");
  }

  /**
   *
   *
   * @private
   * @param {*} details
   * @return {*}  {boolean}
   * @memberof TFModelerClass
   */
  private validateTarget(details): boolean {
    if (details?.metadata?.config?.dwcSpace) {
      // shared object
      this.showErrorDialog("unsupportedTargetSharedTable", "unsupportedTargetSharedTableErrMsg");
      return false;
    }
    if (details?.metadata?.config?.dwcExternalSchema) {
      // open schema or HDI container
      this.showErrorDialog("unsupportedTargetOpenSchema", "unsupportedTargetOpenSchemaErrMsg");
      return false;
    }
    if (
      isSACPlanningIntegrationEnabled() &&
      (details?.canManipulateData === "false" || details?.canManipulateData === false)
    ) {
      // SAC Artefact
      this.showErrorDialog("unsupportedSACTarget", "unsupportedSACTargetErrMsg");
      return false;
    }
    return true;
  }

  /**
   *
   *
   * @private
   * @param {string} messageKey
   * @param {string} id
   * @memberof TFModelerClass
   */
  private showErrorDialog(messageKey: string, id: string) {
    // show error dialog
    MessageHandler.uiError(this.localizeText(messageKey), null, null, null, null, null, null, id);
  }

  public createOutputColumnsAndMappings(oNode, details) {
    const diagramBuilder = this.getDiagramBuilder();
    let propagate = false;
    if (details.columns.size) {
      for (const item of details.columns.values()) {
        if (Utils.isTableAsSourceSupportedSpark() && oNode.isSourceTable) {
          propagate = true;
        }
        diagramBuilder.createAttribute(item, oNode, propagate);
      }
    }
    diagramBuilder.autoMapTargetColumns(oNode);
  }

  public onDragEnd() {
    sap.ui.getCore().getEventBus().publish("SOURCES_LIST", "DRAG_END", {});
  }

  public refreshProperty(node: sap.cdw.transformationflow.Node) {
    sap.ui.getCore().getEventBus().publish("TRANSFORMATIONFLOW", "PROPERTY_PANEL_RELOAD", { oNode: node });
  }

  public reloadProperty(node: sap.cdw.transformationflow.Node) {
    const diagramEditor = this.getDiagramEditor();
    diagramEditor.unselectAllSymbols();
    setTimeout(() => {
      diagramEditor.selectSymbol(node.relatedSymbols.get(0));
    }, 200);
  }

  /**
   * @override
   * @param {*} aSelectObjects
   * @returns {void}
   * @memberof TFModelerClass
   */
  public async updateDataPreview(aSelectObjects): Promise<boolean> {
    const oObject = aSelectObjects?.length > 0 ? aSelectObjects[0] : aSelectObjects;
    const diagramEditor = this.getDiagramEditor();
    const oDataBuilderWorkbenchController = diagramEditor && diagramEditor.previewService;
    if (oDataBuilderWorkbenchController && oObject) {
      if (oDataBuilderWorkbenchController.isPreviewDataPending()) {
        clearTimeout(this["tfdelayedPreview"]);
        this["tfdelayedPreview"] = setTimeout(() => {
          this.updateDataPreview(aSelectObjects);
        }, 200);
        return false;
      }
      if (oObject.classDefinition.name === "Node" && !oObject.isNew) {
        // source or target node and not new target
        const tableName = oObject.technicalName || oObject.name;
        oDataBuilderWorkbenchController.previewData(tableName, oObject);
      } else {
        oDataBuilderWorkbenchController.showPreviewMessage(
          this.resourceModel.getResourceBundle().getText("msgDataPreviewNotSupp"),
          /* isError*/ false,
          oDataBuilderWorkbenchController.getPreviewDataUIControls()
        );
      }
    } else {
      oDataBuilderWorkbenchController.resetPreviewTable(true, true);
    }
    return true;
  }

  /**
   * Update object file info (modificationDate, deploymentDate, ...)
   */
  private async updateObjectsFileInfo(): Promise<any> {
    this.getDeploymentStatusRetries++;
    const oModel: any = this.getGalileiModel();
    const undeployedStatuses: ObjectStatus[] = [ObjectStatus.notDeployed, ObjectStatus.hasNoObjectStatus];
    if (oModel && this.getDeploymentStatusRetries <= 5) {
      // Remove cached data
      Crud.get().clearCache([
        { type: Repo.space, name: this.spaceName },
        { type: Repo.model, name: oModel.name },
      ]);
      getObjectFilesProperties(this.getSpaceName(), [oModel.name])
        .then((properties) => {
          if (undeployedStatuses.includes(+properties?.[0]?.["#objectStatus"])) {
            // Re-fetch the deployment status
            this.updateObjectsFileInfoTimer = setTimeout(() => {
              this.updateObjectsFileInfo();
            }, this.getDeploymentStatusRetries * 5000);
          } else if (+properties?.[0]?.["#objectStatus"] === ObjectStatus.pending) {
            // Re-fetch the deployment status and also update the properties panel
            updateObjectFileInfo(oModel, properties[0]);
            sap.ui.getCore().getEventBus().publish("propertyPanel", "updateTransformationFlowDeploymentStatus");
            this.updateObjectsFileInfoTimer = setTimeout(() => {
              this.updateObjectsFileInfo();
            }, this.getDeploymentStatusRetries * 5000);
          } else if (properties?.[0]) {
            // update the deployment status in properties panel
            updateObjectFileInfo(oModel, properties[0]);
            sap.ui.getCore().getEventBus().publish("propertyPanel", "updateTransformationFlowDeploymentStatus");
          }
        })
        .catch(() => {
          // Re-fetch deployment status
          this.updateObjectsFileInfoTimer = setTimeout(() => {
            this.updateObjectsFileInfo();
          }, this.getDeploymentStatusRetries * 5000);
        });
    }
  }

  public getDiagramBuilder(): any {
    const editorControl = this.getEditorControl() as TFDiagramEditorControl;
    return editorControl.getDiagramBuilder();
  }

  public editViewTransform(oNode: sap.cdw.transformationflow.Node) {
    const component = this.getOwnerComponent() as TransformationFlowEditorComponentClass;
    const modelId = oNode.name;
    let loadingData;
    if (!oNode.isTemplate) {
      const csn = oNode.getCSN();
      if (csn) {
        loadingData = {
          csn: csn,
          file: {
            "#objectStatus": "3", // Force validation
          },
          "#isViewEntity": true,
        };
        if (!loadingData.csn.editorSettings || oNode.definition[DataWarehouse.querybuilder_model]) {
          loadingData.query = {};
          loadingData.query[DataWarehouse.querybuilder_model] = oNode.definition[DataWarehouse.querybuilder_model];
        }
      }
    }
    this.resetDataPreviewErrors();
    component.switchToSecondaryEditor(modelId, oNode, {
      galileiObject: oNode,
      loadingData: loadingData,
    });
  }

  public editSQLTransform(oNode: sap.cdw.transformationflow.Node) {
    const component = this.getOwnerComponent() as TransformationFlowEditorComponentClass;
    const modelId = oNode.name;
    let loadingData;
    if (!oNode.isTemplate) {
      const csn = oNode.getCSN();
      if (csn) {
        loadingData = {
          csn: csn,
          "#isViewEntity": true,
        };
      }
    }
    this.resetDataPreviewErrors();
    component.switchToSecondaryEditor(modelId, oNode, {
      galileiObject: oNode,
      loadingData: loadingData,
    });
  }

  public resetDataPreviewErrors() {
    const previewErrors = this.getWorkbenchController().getWorkbenchEnvModel().getProperty("/detailsPage/problems");
    if (previewErrors.length > 0) {
      this.getWorkbenchController().getWorkbenchEnvModel().setProperty("/detailsPage/problems", []);
      this.getWorkbenchController().getWorkbenchEnvModel().refresh(true);
    }
  }

  public createNewTarget(oNode: sap.cdw.transformationflow.Node) {
    this.getDiagramBuilder().createNewTarget(oNode);
    // open side panel when user click the context pad to create target
    this.getWorkbenchController().onToggleSidepanel(SidepanelMode.normal);
  }

  public welcomeVisibleFormatterTF(isTemplate: boolean, isDragging: boolean, isDownloading?: boolean): boolean {
    return isTemplate && !isDragging && !isDownloading;
  }

  protected onModelChanged(event) {
    super.onModelChanged(event);
    this.getOwnerComponent().refreshWorkbenchEnvModel();
  }

  public setBusy(isBusy: boolean, sTitle?: string) {
    super.setBusy(isBusy, sTitle);
  }

  public resetEditor() {
    // Clear the update object file info timer if it exists
    if (this.updateObjectsFileInfoTimer) {
      clearTimeout(this.updateObjectsFileInfoTimer);
      this.getDeploymentStatusRetries = 0;
    }
  }

  /**
   * Validates the model before performing the run
   * @returns {boolean}
   */
  private canRunTransformationFlow(): boolean {
    const oModel = this.getGalileiModel() as sap.cdw.transformationflow.Model;
    // check if the transformation flow is new
    if (!oModel || oModel.isNew) {
      MessageHandler.uiError(
        this.localizeText("runUnsavedError"),
        this.localizeText("runError"),
        null,
        null,
        null,
        null,
        null,
        "runUnsavedErrorMsgbox"
      );
      return false;
    }
    // check if the model is dirty (unsaved changes)
    // if yes, show error message
    if (!oModel.isNew && (this.getOwnerComponent() as TransformationFlowEditorComponentClass).isDirty()) {
      MessageHandler.uiError(
        this.localizeText("runModifiedError"),
        this.localizeText("runError"),
        null,
        null,
        null,
        null,
        null,
        "runModifiedErrorMsgbox"
      );
      return false;
    }
    return true;
  }

  /**
   * Handler after Run Anyway is selected
   */
  public onRunAnyway() {
    this.closeDialog();
    this.triggerRun();
  }

  /**
   * Closes the rundeployedversion and runwithvalidationerrors dialogs
   */
  public onConfirmDialogClosed() {
    this.closeDialog();
  }

  /**
   * Closes the rundeployedversion and runwithvalidationerrors dialogs
   */
  private closeDialog() {
    if (this["confirmDialog"]) {
      this["confirmDialog"].close();
    }
    if (this.confirmRunDeployedVersionDialog) {
      this.confirmRunDeployedVersionDialog.close();
    }
  }

  /**
   * Opens the dialog box when user tries to run transformation with undeployed changes
   */
  private showRunDeployedVersionConfirmDialog() {
    if (!this.confirmRunDeployedVersionDialog) {
      const fragmentId = require("../view/fragment/RunDeployedVersionConfirmDialog.fragment.xml");
      this.confirmRunDeployedVersionDialog = sap.ui.xmlfragment("", fragmentId, this);
      this.getView().addDependent(this.confirmRunDeployedVersionDialog);
    }
    this.confirmRunDeployedVersionDialog.open();
  }

  /**
   * Opens the dialog box when user tries to run transformation flows with validation errors
   */
  private showRunConfirmDialog() {
    if (!this["confirmDialog"]) {
      const fragmentId = require("../view/fragment/RunConfirmDialog.fragment.xml");
      this["confirmDialog"] = sap.ui.xmlfragment("", fragmentId, this);
      this.getView().addDependent(this["confirmDialog"]);
    }
    this["confirmDialog"].open();
  }

  /**
   * Opens the dialog box when user runs the transformation flow
   */
  public showBusyDialog(title: string, msg: string) {
    if (!this["oBusyDialog"]) {
      const fragmentId = require("../view/fragment/BusyDialog.fragment.xml");
      this["oBusyDialog"] = sap.ui.xmlfragment("", fragmentId, this);
      this.getView().addDependent(this["oBusyDialog"]);
    }
    if (title) {
      this["oBusyDialog"].setTitle(title);
    }
    if (msg) {
      this["oBusyDialog"].setText(msg);
    }
    this["oBusyDialog"].open();
  }

  /**
   * Closes the busy dialog
   */
  public closeBusyDialog() {
    if (this["oBusyDialog"]) {
      this["oBusyDialog"].close();
    }
  }
  private runTransformationFlow(data: IRunTransformationFlowPayload) {
    const runTFURL = "tf/directexecute";
    ServiceCall.post(runTFURL, { contentType: ContentType.APPLICATION_JSON }, true, JSON.stringify(data)).then(
      () => {
        if (this["oBusyDialog"]) {
          this["oBusyDialog"].close();
        }
        sap.m.MessageToast.show(this.localizeText("runSuccess"));
        sap.ui.getCore().getEventBus().publish("propertyPanel", "updateTFRunStatus");
      },
      (error) => {
        if (this["oBusyDialog"]) {
          this["oBusyDialog"].close();
        }
        const errorMsg = this.localizeText("runFail");
        MessageHandler.exception({ exception: error, message: errorMsg, id: "runFailedErrorMsgbox" });
      }
    );
  }
  /**
   * Collect input parameter values and run the Transformation flow
   *
   * @memberof TFModelerClass
   */
  public async onRunWithInputParameterConfirmationTF() {
    const parameters = {};
    const inputParameters = this.getView().getModel("parametersModel")?.getProperty("/parameters");

    if (Array.isArray(inputParameters)) {
      inputParameters.forEach((item) => {
        parameters[`${item.name}`] = item.value.toString();
      });
    }

    const oModel = this.getGalileiModel() as sap.cdw.transformationflow.Model;
    const data: IRunTransformationFlowPayload = {
      objectId: oModel.name,
      activity: "EXECUTE",
      spaceId: this.getSpaceName(),
      applicationId: ApplicationId.TRANSFORMATION_FLOWS,
      parameters: {
        flowParameters: parameters,
      },
    };

    if (this.runWithParameterFragment) {
      this.runWithParameterFragment.close();
    }
    this.runTransformationFlow(data);
  }

  /**
   * close the run with parameter dialog
   *
   * @memberof TFModelerClass
   */
  public onRunWithInputParameterClose(): void {
    if (this["oBusyDialog"]) {
      this["oBusyDialog"].close();
    }
    this.runWithParameterFragment.close();
    this.runWithParameterFragment.destroy();
    this.runWithParameterFragment = null;
  }
  /**
   * Handler for live change of input parameter value
   * @param oEvent Event
   */
  public onLiveChangeInputParameterValue(oEvent: sap.ui.base.Event): void {
    const oModel = this.getView().getModel("parametersModel");
    const parameterList = oModel.getProperty("/parameters");

    const parameterName = oEvent.getSource().getProperty("name");
    const newValue = oEvent.getParameter("newValue");

    const currentParameter = parameterList.find((param) => param.name === parameterName);

    if (currentParameter) {
      const parameterType = currentParameter.type;

      const isValid = ParameterUtils.validateInputValue(newValue, parameterType);

      // Set value state and message
      const setValueState = (state: sap.ui.core.ValueState, message: string) => {
        oEvent.getSource()["setValueState"](state);
        oEvent.getSource()["setValueStateText"](message);
        currentParameter.valueState = state;
        currentParameter.valueStateText = message;
      };

      if (!isValid) {
        setValueState(sap.ui.core.ValueState.Error, Utils.getErrorMessageForType(parameterType));
      } else {
        setValueState(sap.ui.core.ValueState.None, "");
      }

      currentParameter.value = newValue;
      oModel.setProperty("/parameters", parameterList);

      // Check if all input parameters are valid & not empty
      const checkValidValues = parameterList.every(
        (p) => !p.valueState || p.valueState === sap.ui.core.ValueState.None
      );
      const checkEmptyValues = !this.checkAreInputParametersEmpty(parameterList);

      const enableRunButton = checkValidValues && checkEmptyValues;
      oModel.setProperty("/enableRunButton", enableRunButton);
    }
  }

  /**
   * Checks if any of the input parameters have no value
   */
  private checkAreInputParametersEmpty(parameterList: IParameterTF[]): boolean {
    for (const inputParam of parameterList) {
      if (inputParam.value === undefined || inputParam.value.trim() === "") {
        return true;
      }
    }
    return false;
  }
  /**
   * opens input parameters dialog
   */
  private openInputParametersDialogTF(parameterList: IParameterTF[]) {
    this["oBusyDialog"].close();
    const parametersModel = new sap.ui.model.json.JSONModel({
      parameters: parameterList,
      minVisibleRowCount: parameterList.length,
      enableRunButton: !this.checkAreInputParametersEmpty(parameterList),
    });
    this.getView().setModel(parametersModel, "parametersModel");
    if (!this.runWithParameterFragment) {
      const fragmentName = require("../properties/fragment/TransformationflowRunWithParameter.fragment.xml");
      this.runWithParameterFragment = sap.ui.xmlfragment(
        "runWithInputParameterDialogTF",
        fragmentName,
        this
      ) as sap.m.Dialog;
      this.getView().addDependent(this.runWithParameterFragment);
      this.runWithParameterFragment.open();
    } else {
      this.runWithParameterFragment.open();
    }
  }

  /**
   * Handler to trigger the run
   */
  private async triggerRun() {
    const spaceName = this.getSpaceName();
    const oWorkbenchModel = this.getView().getModel("workbenchEnv") as sap.ui.model.json.JSONModel;
    this.showBusyDialog(this.localizeText("waitBusy"), this.localizeText("runBusy"));
    this.getDiagramEditor().unselectAllSymbols();
    oWorkbenchModel.setProperty("/panels/right/mode", SidepanelMode.normal);
    const oModel = this.getGalileiModel() as sap.cdw.transformationflow.Model;

    const oFeatures = sap.ui.getCore().getModel("featureflags").getData();

    try {
      if (oFeatures.DWCO_TRF_INPUT_PARAMETERS_SUPPORT) {
        const parameterList = await Utils.findInputParametersInDeployedTF(this.getSpaceName(), oModel.name);

        if (parameterList.length > 0) {
          return this.openInputParametersDialogTF(parameterList);
        }
      }
    } catch (error) {
      if (this["oBusyDialog"]) {
        this["oBusyDialog"].close();
      }
      const errorMsg = this.localizeText("transformationflowFetchFailMessage");
      MessageHandler.exception({
        exception: error,
        message: errorMsg,
        id: "transformationFlowFetchFailedErrorMsgbox",
      });
      return;
    }

    // Normal execution if feature flag is off or no parameters are found
    const data: IRunTransformationFlowPayload = {
      objectId: oModel.name,
      activity: "EXECUTE",
      spaceId: spaceName,
      applicationId: ApplicationId.TRANSFORMATION_FLOWS,
    };
    this.runTransformationFlow(data);
  }

  /**
   * Handler to validate and trigger run of transformation flow
   */
  public onRunTransformationFlow() {
    const oModel = this.getGalileiModel() as sap.cdw.transformationflow.Model;
    // validate for unsaved transformation flows
    if (!this.canRunTransformationFlow()) {
      return;
    }
    // Inform user that deployed version of transformation flow will get executed
    if (+oModel?.["#objectStatus"] === ObjectStatus.changesToDeploy) {
      this.showRunDeployedVersionConfirmDialog();
    } else if (oModel && oModel.aggregatedValidations && oModel.aggregatedValidations.status === "error") {
      // validation errors in the model.
      // confirm execution
      this.showRunConfirmDialog();
    } else {
      this.triggerRun();
    }
  }

  public openSQLViewConfirmationDialog(oNode: sap.cdw.transformationflow.Node) {
    if (oNode?.aggregatedValidations?.status === "error") {
      // show error DW101-68455
      MessageHandler.uiError(
        this.localizeText("convertGVTransformToSQLViewTransformError"),
        null,
        null,
        null,
        null,
        null,
        null,
        "convertSQLViewTransformErrorMessageBox"
      );
      return;
    }
    const confirmButton = this.localizeText("confirmSQLViewTransform") as sap.m.MessageBox.Action;
    sap.m.MessageBox.warning(this.localizeText("convertToSQLViewConfirmation"), {
      id: `${this.getView().getViewName().replace(/\./g, "-")}--gvSQLSwitchWarning`,
      actions: [confirmButton, sap.m.MessageBox.Action.CANCEL],
      emphasizedAction: confirmButton,
      onClose: async (action: sap.m.MessageBox.Action) => {
        if (action === confirmButton) {
          const oDiagramEditor = this.getDiagramEditor();
          oDiagramEditor.resource.applyUndoableAction(async () => {
            // converts GV to SQL
            this.changeGVToSQL(oNode);
            if (oNode.definition?.params) {
              // generate SQL text before opening SQL editor and replace parameter syntax
              const csn = oNode.getCSN();
              csn.isFullCSN = true;
              // eslint-disable-next-line camelcase
              csn.space_id = this.getSpaceName();
              const sqlText = await buildSqlFromSelectCqn(csn, oNode.name);
              oNode.definition["@DataWarehouse.sqlEditor.query"] = sqlText.replaceAll("=>", ":"); // use parameter syntax of SQL language, the utility uses SQLScript syntax
            }
            this.editSQLTransform(oNode);
          }, "update to sql view");
          // clear undo/redo stack
          oDiagramEditor?.resource?.clearListOfActions();
          oDiagramEditor?.resource?.clearUndoStack();
        }
      },
    });
  }

  public onConfirmSQLView(oNode: sap.cdw.transformationflow.Node) {
    this.changeGVToSQL(oNode);
    // sql editor will generate the sql text from the csn
    this.editSQLTransform(oNode);
  }

  private changeGVToSQL(oNode: sap.cdw.transformationflow.Node) {
    // switch to SQL- update component and name
    oNode.component = Constants.OPERATORS.SQL_TRANSFORM;
    oNode.name = "sqltransform1";
    this.getDiagramBuilder().updateSourceIcon(oNode);
  }
}

export const TFModeler = smartExtend(
  AbstractDiagramEditor,
  "sap.cdw.components.transformationflow.controller.TFModeler",
  TFModelerClass
);

sap.ui.define("sap/cdw/components/transformationflow/controller/TFModeler.controller", [], function () {
  return TFModeler;
});
