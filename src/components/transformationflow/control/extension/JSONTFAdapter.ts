/**
 * Copyright 2023 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { AbstractBaseEditorControl } from "../../../commonui/control/baseeditor/AbstractBaseEditor";
import { BaseDocumentAdapter, IAdaptableDocument } from "../../../commonui/control/baseeditor/api/BaseDocumentAdapter";
import { BaseDiagramEditorControl } from "../../../commonui/control/diagrameditor/BaseDiagramEditor";
import { TransformationFlowModelToJSON } from "../../js/model/ModelToJSON";
import Constants from "../../js/utility/Constants";
import Utils from "../../js/utils";
import { TFModelHandler } from "./TFModelHandler";
/**
 * Convenient class responsible for adapting a data flow JSON to galilei model and
 * creating back data flow JSON from galilei model
 *
 * @export
 * @class JSONTFAdapter
 * @extends {BaseDocumentAdapter}
 */
export class JSONTFAdapter extends BaseDocumentAdapter {
  constructor(editor: AbstractBaseEditorControl) {
    super(editor);
    this.modelHanlder = new TFModelHandler("sap.cdw.transformationflow", editor as BaseDiagramEditorControl);
    this.modelHanlder.setDocumentAdapter(this);
  }

  public getModelHandler(): TFModelHandler {
    return this.modelHanlder as TFModelHandler;
  }

  /**
   *
   * @override
   * @param {IAdaptableDocument} TFCSNDef
   * @return {*}  {Promise<any>}
   * @memberof JSONTFAdapter
   */
  public async adaptDocument(TFCSNDef: IAdaptableDocument): Promise<any> {
    const model = await this.modelHanlder.createGalileiModel(TFCSNDef);

    this.modelHanlder.clearUndostack();

    return Promise.resolve(model);
  }

  /**
   *
   * @override
   * @return {*}  {Promise<any>}
   * @memberof JSONTFAdapter
   */
  public createDocument(): Promise<any> {
    const modelHandler = this.getModelHandler();
    const model = modelHandler.getModel();
    const name = model.name;

    const graphJson = TransformationFlowModelToJSON.serializeModel(model);
    const repoJson = this.generateRepoJson(model, graphJson);

    const transformationflow = {
      content: {
        version: {
          transformationflow: "0.1",
        },
        transformationflows: {},
      },
    };
    transformationflow.content.transformationflows[name] = repoJson;

    // Add revert last version flag
    if (model.revertLastVersion) {
      // eslint-disable-next-line dot-notation
      transformationflow.content["extensions"] = {
        [model.name]: {
          revertLastVersion: true,
        },
      };
    }
    model.nodes.forEach((oNode: sap.cdw.transformationflow.Node) => {
      if (oNode.component === Constants.OPERATORS.TARGET) {
        if (oNode.isNew) {
          const targetDefinition = this.getTargetTableDefinition(oNode, model);
          transformationflow.content["definitions"] = targetDefinition;
        }

        if (Utils.isIncrementalAggregationSupported() && model.isLargeSystemSpace) {
          const aggregationColumns = oNode
            .getAttributesArray()
            .filter(
              (column: sap.cdw.transformationflow.Attribute) => column.aggregation !== Constants.aggregationtypes.LAST
            );
          if (aggregationColumns.length) {
            const aggregations = [];
            const diagramBuilder = this.getModelHandler()
              .getDocumentAdapter()
              .getEditor()
              .getContainerController()
              ["getDiagramBuilder"]();
            aggregationColumns.forEach((column) => {
              if (diagramBuilder) {
                const mappedColumn = diagramBuilder.getAttributeMapping({ target: column }, oNode.attributeMappings);
                if (mappedColumn) {
                  aggregations.push({
                    columnName: column.name,
                    sourceColumnName: mappedColumn.source.name,
                    aggregationType: column.aggregation,
                    type: column.datatype,
                  });
                }
              }
            });
            if (aggregations.length && transformationflow.content.transformationflows[name]?.contents["metadata"]) {
              transformationflow.content.transformationflows[name].contents["metadata"]["incrementalMaintenance"] = {
                deferredAggregations: aggregations,
              };
            }
          }
        }
      }
    });

    return Promise.resolve(transformationflow);
  }

  /**
   * Gets target table definition
   *
   * @param {sap.cdw.transformationflow.Node} oNode
   * @param {sap.cdw.transformationflow.Model} oModel
   * @return {*} definitions
   * @memberof JSONTFAdapter
   */
  public getTargetTableDefinition(oNode: sap.cdw.transformationflow.Node, oModel: sap.cdw.transformationflow.Model) {
    let technicalName = oNode.technicalName;

    if (oNode.isDeltaTable) {
      technicalName = `${oNode.technicalName}${Constants.DELTA_TABLE_SUFFIX}`;
    }
    const definitions = {};
    definitions[technicalName] = TransformationFlowModelToJSON.generateTargetDefinition(oNode);
    if (oModel?.isLargeSystemSpace && oNode?.isDeltaTable && oNode?.isLTF) {
      definitions[technicalName].params = Constants.TargetParametersLTF;
      definitions[technicalName]["@cds.persistence.udf"] = true;
      definitions[technicalName]["@Semantics.interval"] = Constants.SemanticsInterval;
    }
    return definitions;
  }

  /**
   * Generates repo json
   *
   * @param {*} oModel
   * @param {*} graphJson
   * @param {*} sourcetargets
   * @return {*} repo Json
   * @memberof JSONTFAdapter
   */
  public generateRepoJson(oModel, graphJson) {
    return {
      kind: "sap.dis.transformationflow",
      "@EndUserText.label": oModel.label,
      contents: graphJson,
    };
  }
}
