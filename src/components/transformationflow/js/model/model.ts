/**
 * Copyright 2023 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import "@sap/velocity-data-tools-ui/src/vflow/common/galilei/diagramModel";
import { DataWarehouse } from "../../../commonmodel/csn/csnAnnotations";
import * as sharedModelMethods from "../../../commonmodel/model/sharedDefinitions/Methods";
import { dataTypeProperties } from "../../../commonmodel/model/sharedDefinitions/Properties";
import { expandModelDefinition } from "../../../commonmodel/utility/GalileiUtils";
import { User } from "../../../shell/utility/User";
import Constants from "../utility/Constants";
function switchTechnicalNameFeatureFlag() {
  return (
    window.sap?.ui?.getCore &&
    sap.ui.getCore().getModel("featureflags")?.getProperty("/DWC_MODELING_SWITCH_TECHNICAL_NAME")
  );
}
sap.galilei.namespace("sap.cdw.transformationflow", function () {
  const nsLocal = sap.cdw.transformationflow;

  /**
   * Transformation Flow meta-model definition
   *
   */
  const oModelDef = {
    contents: {
      /**
       * sap.cdw.transformationflow definition
       */
      "sap.cdw.transformationflow": {
        classDefinition: "sap.galilei.model.Package",
        displayName: "Transformation Flow Model",
        namespaceName: "sap.cdw.transformationflow",
        classifiers: {
          /**
           * @class
           * Node
           * A Node can have several input anchors and several output anchors.
           */
          Group: {
            displayName: "Group",
            parent: "sap.modeling.vflow.Group",
          },
          /**
           * @class
           * Node
           * A Node can have several input anchors and several output anchors.
           */
          Node: {
            displayName: "Process",
            parent: "sap.modeling.vflow.Node",
            properties: {
              nodeDisplayName: {
                dataType: sap.galilei.model.dataTypes.gString,
                get: function () {
                  if (!switchTechnicalNameFeatureFlag()) {
                    return this.businessName || this.displayName;
                  }
                  const userInstance = User.getInstance();
                  if (userInstance) {
                    const objectNameDisplay = userInstance.getObjectNameDisplay();
                    if (objectNameDisplay === "businessName") {
                      return this.businessName || this.displayName;
                    }
                    if (objectNameDisplay === "technicalName") {
                      return this.technicalName || this.displayName;
                    }
                  }
                },
              },
              columnsCount: {
                name: "columnsCount",
                dataType: sap.galilei.model.dataTypes.gInteger,
                get: function () {
                  const attributesArray = this.getAttributesArray();
                  if (Array.isArray(attributesArray)) {
                    return attributesArray.length;
                  }
                  return 0;
                },
              },
              isNew: {
                // this property will be indicate new table
                name: "isNew",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
              },
              isTemplate: {
                // this property will be indicate template node
                name: "isTemplate",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
              },
              definition: {
                // the csn will be stored in this property
                name: "definition",
                defaultValue: undefined,
              },
              meta: {
                // the csn meta value  will be stored in this property
                name: "meta",
                defaultValue: undefined,
              },
              $version: {
                // the csn $version will be stored in this property
                name: "$version",
                defaultValue: undefined,
              },
              version: {
                // the csn version will be stored in this property
                name: "version",
                defaultValue: undefined,
              },
              editorSettings: {
                // the editorSettings will be stored in this property
                name: "editorSettings",
                defaultValue: undefined,
              },
              cdcColumns: {
                // the cdcColumns of view transform will be stored in this property
                name: "cdcColumns",
                defaultValue: undefined,
              },
              useAs: {
                name: "useAs",
                dataType: sap.galilei.model.dataTypes.gString,
              },
              isDeleted: {
                // this property will be indicate deleted table
                name: "isDeleted",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
              },
              deploymentStatus: {
                // this property will show the deployment status
                name: "deploymentStatus",
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: undefined,
                isInternal: true, // ignore this property from undo redo
              },
              businessName: {
                name: "businessName",
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: undefined,
              },
              technicalName: {
                name: "technicalName",
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: undefined,
              },
              deltaTableTechnicalName: {
                name: "deltaTableTechnicalName",
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: undefined,
                isInternal: true, // ignore this property from undo redo
              },
              truncate: {
                name: "truncate",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
              },
              isTargetTable: {
                name: "isTargetTable",
                get: function () {
                  return this?.component === Constants.OPERATORS.TARGET;
                },
              },
              showPlaceHolder: {
                name: "showPlaceHolder",
                get: function () {
                  return this.isTargetTable && this.isTemplate && !this.container?.isTemplate;
                },
              },
              uniqueTechnicalName: {
                name: "uniqueTechnicalName",
                defaultValue: undefined,
                isInternal: true, // ignore this property from undo redo
              },
              hasBWBridgeDeltaSource: {
                name: "hasBWDeltaSource",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
                isInternal: true, // ignore this property from undo redo
              },
              uniqueDeltaCaptureTableName: {
                name: "uniqueDeltaCaptureTableName",
                defaultValue: undefined,
                isInternal: true, // ignore this property from undo redo
              },
              isTargetDelta: {
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
              },
              isDeltaTable: {
                name: "isDeltaTable",
                get: function () {
                  if (this.isNew) {
                    return this.isTargetDelta;
                  }
                  if (this.definition?.[DataWarehouse.delta]) {
                    return true;
                  }
                  return false;
                },
              },
              isLTF: {
                name: "isLTF",
                get: function () {
                  if (this.definition?.[DataWarehouse.persistence_hdlf_tableFormat]) {
                    return true;
                  }
                  return false;
                },
              },
              isViewTransform: {
                name: "isViewTransform",
                get: function () {
                  if (
                    this.component === Constants.OPERATORS.VIEW_TRANSFORM ||
                    this.component === Constants.OPERATORS.SQL_TRANSFORM ||
                    this.component === Constants.OPERATORS.SQL_SCRIPT_TRANSFORM
                  ) {
                    return true;
                  }
                  return false;
                },
              },
              isSourceTable: {
                name: "isSourceTable",
                get: function () {
                  if (this.component === Constants.OPERATORS.SOURCE_TABLE) {
                    return true;
                  }
                  return false;
                },
              },
              internalValidationStatus: {
                name: "internalValidationStatus",
                defaultValue: undefined,
                isInternal: true, // ignore this property from undo redo
              },
              changeManagement: {
                name: "changeManagement",
                defaultValue: undefined,
                isInternal: true, // ignore this property from undo redo
              },
              packageValue: {
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: "_NONE_KEY_PACKAGE_",
              },
              isValidationSkipped: {
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
                isInternal: true, // ignore this property from undo redo
              },
              secondaryNodes: {
                name: "secondaryNodes",
                defaultValue: undefined,
              },
              sourceDefinitions: {
                name: "sourceDefinitions",
                defaultValue: {},
              },
            },
            references: {
              attributes: {
                // save target table schema
                name: "attributes",
                contentType: "sap.cdw.transformationflow.Attribute",
                isMany: true,
                isContainment: true,
              },
              validations: {
                contentType: "sap.cdw.commonmodel.ValidationStatus",
                isMany: true,
                isContainment: true,
                isVolatile: true,
                isInternal: true,
                isSupportNotification: true,
              },
              aggregatedValidations: {
                contentType: "sap.cdw.commonmodel.ValidationStatus",
                isMany: true,
                isContainment: false,
                isVolatile: true,
                isComputed: true,
                isCached: false,
                isInternal: true,
                isSupportNotification: true,
                get: function () {
                  return nsLocal.Validation.getAggregatedValidation(this, this.getAttributes());
                },
              },
            },
            methods: {
              validate: function (bSkipRefreshDecorators, bSkipClear, bSkipNoAttributes, bSkipDuplicate) {
                return nsLocal.Validation.validateNode(
                  this,
                  bSkipRefreshDecorators,
                  bSkipClear,
                  bSkipNoAttributes,
                  bSkipDuplicate
                );
              },
              clearValidation: function () {
                this.resource.applyUndoableAction(
                  function () {
                    this.validations.deleteAll();
                  }.bind(this),
                  "Clear validations",
                  /* protectedFromUndo */ true
                );
                const aAttributes = this.getAttributes();
                for (let nIndex = 0; nIndex < aAttributes.length; nIndex++) {
                  aAttributes.get(nIndex).clearValidation();
                }
              },
              getAttributes: function () {
                return this.attributes;
              },
              getTimestampColumns: function () {
                return this.getAttributesArray()
                  .filter(
                    (attribute: sap.cdw.transformationflow.Attribute) =>
                      attribute.datatype === "cds.Timestamp" && attribute.isCDCColumn === true
                  )
                  .map((attribute: sap.cdw.transformationflow.Attribute) => attribute.displayName);
              },
              getAttributesArray: function () {
                const attributes = this.getAttributes();
                if (attributes && typeof attributes.toArray === "function") {
                  return attributes.toArray();
                }
                // return empty array
                return [];
              },
              getTargetNode: function () {
                if (this.outputs?.length) {
                  const oOutput = this.outputs.get(0);
                  if (oOutput) {
                    return oOutput.getTargetNode();
                  }
                }
              },
              getSourceNode: function () {
                if (this.inputs?.length) {
                  const oInput = this.inputs.get(0);
                  if (oInput) {
                    return oInput.getSourceNode();
                  }
                }
              },
              getViewTransformNode: function () {
                const sourceNode = this.getSourceNode();
                if (sourceNode) {
                  if (sourceNode.isViewTransform) {
                    return sourceNode;
                  }
                  return sourceNode.getViewTransformNode();
                }
                return null;
              },
              getTFSourceNode: function () {
                const sourceNode = this.getSourceNode();
                if (sourceNode) {
                  if (sourceNode.isViewTransform || sourceNode.isSourceTable) {
                    return sourceNode;
                  }
                  return sourceNode.getTFSourceNode();
                }
                return null;
              },
              getTargetTableNode: function () {
                const targetNode = this.getTargetNode();
                if (targetNode) {
                  if (targetNode.isTargetTable) {
                    return targetNode;
                  }
                  return targetNode.getTargetTableNode();
                }
                return null;
              },
              getCSN: function () {
                if (this.definition) {
                  const modelId = this.technicalName || this.name;
                  const csn = {
                    definitions: {},
                    meta: this.meta,
                    $version: this.$version,
                    version: this.version,
                  } as any;
                  csn.definitions[modelId] = this.definition;
                  if (this.config?.additionalDefinition) {
                    csn.definitions = { ...csn.definitions, ...this.config.additionalDefinition };
                  }
                  if (this.component === Constants.OPERATORS.VIEW_TRANSFORM) {
                    if (this.editorSettings) {
                      csn.editorSettings = {};
                      csn.editorSettings[modelId] = this.editorSettings;
                    }
                  }
                  return csn;
                }
              },
              clearCSN: function () {
                this.meta = undefined;
                this.$version = undefined;
                this.version = undefined;
                this.definition = undefined;
                this.editorSettings = undefined;
                this.cdcColumns = undefined;
              },
              // Methods for repository object (objectStatus for instance)
              ...sharedModelMethods.repositoryObjectMethods,
            },
          },

          /**
           * @class
           * Secondary Node
           */
          SecondaryNode: {
            displayName: "Process",
            parent: "sap.modeling.vflow.Node",
            properties: {
              nodeDisplayName: {
                dataType: sap.galilei.model.dataTypes.gString,
                get: function () {
                  if (!switchTechnicalNameFeatureFlag()) {
                    return this.businessName || this.displayName;
                  }
                  const userInstance = User.getInstance();
                  if (userInstance) {
                    const objectNameDisplay = userInstance.getObjectNameDisplay();
                    if (objectNameDisplay === "businessName") {
                      return this.businessName || this.displayName;
                    }
                    if (objectNameDisplay === "technicalName") {
                      return this.technicalName || this.displayName;
                    }
                  }
                },
              },
              columnsCount: {
                name: "columnsCount",
                dataType: sap.galilei.model.dataTypes.gInteger,
                get: function () {
                  const attributesArray = this.getAttributesArray();
                  if (Array.isArray(attributesArray)) {
                    return attributesArray.length;
                  }
                  return 0;
                },
              },
              displayName: {
                name: "displayName",
                defaultValue: false,
              },
              isDeltaTable: {
                name: "isDeltaTable",
                defaultValue: false,
              },
              definition: {
                // the csn will be stored in this property
                name: "definition",
                defaultValue: undefined,
              },
              deploymentStatus: {
                // this property will show the deployment status
                name: "deploymentStatus",
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: undefined,
                isInternal: true, // ignore this property from undo redo
              },
              businessName: {
                name: "businessName",
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: undefined,
              },
              technicalName: {
                name: "technicalName",
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: undefined,
              },
              useAs: {
                name: "useAs",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: 0,
              },
              localSchemaName: {
                name: "localSchemaName",
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: undefined,
              },
              releaseState: {
                name: "releaseState",
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: undefined,
              },
              crossSpaceName: {
                name: "crossSpaceName",
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: undefined,
              },
              connection: {
                name: "connection",
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: undefined,
              },
              remoteTable: {
                name: "remoteTable",
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: undefined,
              },
              qualifiedName: {
                name: "qualifiedName",
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: undefined,
              },
              deltaTableName: {
                name: "deltaTableName",
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: undefined,
              },
              packageValue: {
                name: "packageValue",
                dataType: sap.galilei.model.dataTypes.gString,
              },
              isSecondarySource: {
                name: "isSecondarySource",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
              },
              "#objectStatus": {
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: "0",
              },
              isView: {
                name: "isView",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
              },
              isQualified: {
                name: "isQualified",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
              },
              isCrossSpace: {
                name: "isCrossSpace",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
              },
              context: {
                name: "context",
                dataType: sap.galilei.model.dataTypes.gString,
              },
              contextDisplayName: {
                name: "contextDisplayName",
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: undefined,
              },
              isRemote: {
                name: "isRemote",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
              },
              isLocal: {
                name: "isLocal",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
              },
              parameters: {
                name: "parameters",
              },
              isLTF: {
                name: "isLTF",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
              },
              listOfPackages: {
                name: "listOfPackages",
              },
              isSecondaryNodeSql: {
                name: "isSecondaryNodeSql",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
              },
            },
            references: {
              attributes: {
                name: "attributes",
                contentType: "sap.cdw.transformationflow.Attribute",
                isMany: true,
                isContainment: true,
              },
              parameters: {
                name: "parameters",
                contentType: "sap.cdw.transformationflow.Parameter",
                isMany: true,
                isContainment: true,
              },
            },
            methods: {
              getAttributes: function () {
                return this.attributes;
              },
              getAttributesArray: function () {
                const attributes = this.getAttributes();
                if (attributes && typeof attributes.toArray === "function") {
                  return attributes.toArray();
                }
                // return empty array
                return [];
              },
              // Methods for repository object (objectStatus for instance)
              ...sharedModelMethods.repositoryObjectMethods,
            },
          },
          /**
           * @class
           * Secondary Flow
           */
          SecondaryFlow: {
            displayName: "SecondaryFlow",
            parent: "sap.modeling.vflow.Flow",
          },

          /**
           * @class
           * Input Parameters.
           * The Input Parameters has business name, technical name and data type
           */
          Parameter: {
            displayName: "Parameter",
            parent: "sap.galilei.common.NamedObject",
            properties: {
              dataType: {
                name: "dataType",
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: undefined,
              },
              defaultValue: {
                name: "defaultValue",
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: undefined,
              },
            },
            references: {
              parameterMappings: {
                name: "parameterMappings",
                contentType: "sap.galilei.common.NamedObject",
                isMany: true,
                isContainment: false,
              },
            },
          },
          /**
           * @class
           * Attribute
           *
           */
          Attribute: {
            displayName: "Attribute",
            parent: "sap.modeling.vflow.Attribute",
            properties: {
              key: {
                name: "key",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
              },
              displayName: {
                dataType: sap.galilei.model.dataTypes.gString,
                get: function () {
                  if (!switchTechnicalNameFeatureFlag()) {
                    return this.newName || this.name; // graphical view modeler : show technical name
                  }
                  const userInstance = User.getInstance();
                  if (userInstance) {
                    const objectNameDisplay = userInstance.getObjectNameDisplay();
                    if (objectNameDisplay === "businessName") {
                      return this.label || this.newName || this.name;
                    }
                    if (objectNameDisplay === "technicalName") {
                      return this.newName || this.name;
                    }
                  }
                },
                set: function (displayName) {
                  this.newName = displayName;
                },
              },
              isKey: {
                isKey: sap.galilei.model.dataTypes.gBool,
                get: function () {
                  return this.key;
                },
              },
              label: {
                name: "label",
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: undefined,
              },
              // The data type properties
              ...dataTypeProperties,
              // The display data type
              dataType: {
                dataType: sap.galilei.model.dataTypes.gString,
                get: function () {
                  return this.datatype;
                },
              },
              length: {
                name: "length",
                dataType: sap.galilei.model.dataTypes.gULong,
              },
              precision: {
                name: "precision",
                dataType: sap.galilei.model.dataTypes.gUInteger,
              },
              scale: {
                name: "scale",
                dataType: sap.galilei.model.dataTypes.gUInteger,
              },
              isCDCColumn: {
                name: "isCDCColumn",
                defaultValue: false,
                isInternal: true, // ignore this property from undo redo
              },
              default: {
                name: "default",
                defaultValue: undefined,
              },
              notNull: {
                name: "notNull",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
              },
              baseType: {
                name: "baseType",
                defaultValue: undefined,
              },
              isCustomScriptColumn: {
                name: "isCustomScriptColumn",
                defaultValue: false,
                isInternal: true, // ignore this property from undo redo
              },
              aggregation: {
                name: "aggregation",
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: "LAST",
              },
            },
            references: {
              validations: {
                contentType: "sap.cdw.commonmodel.ValidationStatus",
                isMany: true,
                isContainment: true,
                isVolatile: true,
                isInternal: true,
                isSupportNotification: true,
              },
              aggregatedValidations: {
                contentType: "sap.cdw.commonmodel.ValidationStatus",
                isMany: true,
                isContainment: false,
                isVolatile: true,
                isComputed: true,
                isInternal: true,
                isSupportNotification: true,
                isCached: false,
                get: function () {
                  return nsLocal.Validation.getAggregatedValidation(this);
                },
              },
            },
            methods: {
              clearValidation: function () {
                this.resource.applyUndoableAction(
                  function () {
                    this.validations.deleteAll();
                  }.bind(this),
                  "Clear validations",
                  /* protectedFromUndo */ true
                );
              },
            },
          },
          /**
           * @class
           * Input Anchor.
           * An Input Anchor belongs to a Node.
           */
          Input: {
            displayName: "Input",
            parent: "sap.modeling.vflow.Input",
            methods: {
              getSourceNodeName: function () {
                const aLinkObjects = this.getLinkObjects();
                if (aLinkObjects.length) {
                  if (aLinkObjects[0].sourceNode) {
                    return aLinkObjects[0].sourceNode?.displayName;
                  }
                }
                return this.name; // fallback return input name if the port is not connected
              },
              getSourceNode: function () {
                const aLinkObjects = this.getLinkObjects();
                if (aLinkObjects.length) {
                  if (aLinkObjects[0].sourceNode) {
                    return aLinkObjects[0].sourceNode;
                  }
                }
              },
            },
          },
          /**
           * @class
           * Output Anchor
           * An Output Anchor belongs to a Node.
           */
          Output: {
            displayName: "Output",
            parent: "sap.modeling.vflow.Output",
            methods: {
              getTargetNode: function () {
                const aLinkObjects = this.getLinkObjects();
                if (aLinkObjects.length) {
                  if (aLinkObjects[0].targetNode) {
                    return aLinkObjects[0].targetNode;
                  }
                }
              },
            },
          },
          /**
           * @class
           * Flow
           * The Flow connects an output anchor of the source node to an input anchor of the target node.
           */
          Flow: {
            displayName: "Flow",
            parent: "sap.modeling.vflow.Flow",
          },
          /**
           * @class
           * Transformation Flow Model.
           * The Transformation Flow Model has nodes and flows.
           */
          Model: {
            displayName: "Model",
            parent: "sap.modeling.vflow.Model",
            properties: {
              isNew: {
                name: "isNew",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
              },
              isTemplate: {
                name: "isTemplate",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
              },
              label: {
                name: "label",
                dataType: sap.galilei.model.dataTypes.gString,
              },
              loadType: {
                name: "loadType",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: Constants.LoadType.INITIAL_ONLY,
              },
              targetCount: {
                name: "targetCount",
                dataType: sap.galilei.model.dataTypes.gInteger,
                defaultValue: 0,
              },
              hasUnconnectedOperator: {
                name: "hasUnconnectedOperator",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
              },
              isEmpty: {
                name: "isEmpty",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: true,
              },
              executionStatus: {
                name: "executionStatus",
                defaultValue: {},
                isInternal: true, // ignore this property from undo redo
              },
              highWaterMarkExists: {
                name: "highWaterMarkExists",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
                isInternal: true, // ignore this property from undo redo
              },
              targetTableTechnicalName: {
                name: "targetTableTechnicalName",
                dataType: sap.galilei.model.dataTypes.gString,
                isInternal: true, // ignore this property from undo redo
              },
              // Gets the list of contexts of the space returned by Repo.getModelList().
              availableContexts: {
                dataType: sap.galilei.model.dataTypes.gBlob,
                isInternal: true,
                isVolatile: true,
              },
              // Gets the list of simple types of the space returned by Repo.getModelList().
              availableSimpleTypes: {
                dataType: sap.galilei.model.dataTypes.gBlob,
                isInternal: true,
                isVolatile: true,
              },
              packageValue: {
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: "_NONE_KEY_PACKAGE_",
              },
              packageStatus: {
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: "",
                onCascadeChange: function (oEventArgs) {
                  const object = oEventArgs?.instance;
                  nsLocal.Validation.validateModel(object);
                },
              },
              isLoadTypeModifiedByUser: {
                name: "isLoadTypeModifiedByUser",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
                isInternal: true,
              },
              customTypesMap: {
                name: "customTypesMap",
                defaultValue: {},
                isInternal: true,
              },
              runtime: {
                name: "runtime",
                dataType: sap.galilei.model.dataTypes.gString,
                defaultValue: Constants.Runtime.HANA,
              },
              isLargeSystemSpace: {
                name: "isLargeSystemSpace",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
              },
              showSecondarySources: {
                name: "showSecondarySources",
                dataType: sap.galilei.model.dataTypes.gBool,
                defaultValue: false,
                isInternal: true,
              },
            },
            references: {
              validations: {
                contentType: "sap.cdw.commonmodel.ValidationStatus",
                isMany: true,
                isContainment: true,
                isVolatile: true,
                isInternal: true,
                isSupportNotification: true,
              },
              aggregatedValidations: {
                contentType: "sap.cdw.commonmodel.ValidationStatus",
                isMany: true,
                isContainment: false,
                isVolatile: true,
                isComputed: true,
                isCached: false,
                isInternal: true,
                isSupportNotification: true,
                get: function () {
                  return nsLocal.Validation.getAggregatedValidation(this, this.nodes);
                },
              },
              // Collection for BaseTypes
              types: {
                contentType: "sap.cdw.commonmodel.BaseType",
                isMany: true,
                isContainment: true,
              },
              // Collection for SimpleTypes
              simpleTypes: {
                contentType: "sap.cdw.commonmodel.SimpleType",
                isMany: true,
                isCached: false,
                get: function () {
                  return this.types.selectAllObjects({
                    isSimpleType: true,
                  });
                },
              },
              secondarynodes: {
                name: "secondarynodes",
                contentType: "sap.cdw.transformationflow.SecondaryNode",
                isMany: true,
                isContainment: true,
              },
              secondaryflows: {
                name: "secondaryflows",
                contentType: "sap.cdw.transformationflow.SecondaryFlow",
                isMany: true,
                isContainment: true,
              },
              parameters: {
                name: "parameters",
                contentType: "sap.cdw.transformationflow.Parameter",
                isMany: true,
                isContainment: true,
              },
            },
            methods: {
              // Methods for processing
              ...sharedModelMethods.modelProcessingMethods,
              // Methods for repository object (objectStatus for instance)
              ...sharedModelMethods.repositoryObjectMethods,
              validate: async function (): Promise<any> {
                return nsLocal.Validation.validateModel(this);
              },
              clearValidation: function () {
                this.resource.applyUndoableAction(
                  function () {
                    this.validations.deleteAll();
                  }.bind(this),
                  "Clear validations",
                  /* protectedFromUndo */ true
                );
                for (let nIndex = 0; nIndex < this.nodes.length; nIndex++) {
                  this.nodes.get(nIndex).clearValidation();
                }
              },
              getProcess: function (name: string) {
                // overwrite this function as function with same name is used in ...sharedModelMethods.modelProcessingMethods and vflow which is mandatory.
                // this function will take care of both nodes and processes.
                if (name) {
                  let oNode = this.nodes.selectObject({
                    name: name,
                  });
                  if (!oNode && this.groups.length) {
                    // sometimes the node will be moved to group . e.g: when we move one process into an existing group
                    this.groups.forEach(function (oGroup) {
                      const oObject = oGroup.nodes.selectObject({
                        name: name,
                      });
                      if (oObject) {
                        oNode = oObject;
                      }
                    });
                  }
                  if (oNode) {
                    return oNode;
                  } else {
                    // model process
                    if (Array.isArray(this.processes)) {
                      for (let i = 0, l = this.processes.length; i < l; i++) {
                        if (this.processes[i].id === name) {
                          return this.processes[i];
                        }
                      }
                    }
                  }
                }
              },
              getChangeManagementInfo: function () {
                const changeManagement = {
                  modifiedObjects: [],
                };

                if (
                  Array.isArray(this.changeManagement?.modifiedObjects) &&
                  this.changeManagement.modifiedObjects.length > 0
                ) {
                  changeManagement.modifiedObjects = this.changeManagement.modifiedObjects;
                }

                const secondaryEditorNodes: sap.cdw.transformationflow.Node[] = this.nodes
                  .toArray()
                  .filter((oNode: sap.cdw.transformationflow.Node) => oNode.isViewTransform);

                secondaryEditorNodes.forEach((oNode) => {
                  if (
                    Array.isArray(oNode.changeManagement?.modifiedObjects) &&
                    oNode.changeManagement.modifiedObjects.length > 0
                  ) {
                    changeManagement.modifiedObjects = [
                      ...changeManagement.modifiedObjects,
                      ...oNode.changeManagement.modifiedObjects,
                    ];
                  }
                });

                return changeManagement;
              },
            },
          },
        },
      },
    },
  };

  const oResource = new sap.galilei.model.Resource("sap.cdw.transformationflow.model");
  const oReader = new sap.galilei.model.JSONReader();
  oReader.load(oResource, expandModelDefinition(oModelDef));
});
