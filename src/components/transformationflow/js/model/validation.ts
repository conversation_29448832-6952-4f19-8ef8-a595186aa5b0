/**
 * Copyright 2023 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { isTableDeleteDataEnabled } from "../../../abstractbuilder/commonservices/FeatureFlagCheck";
import { DataWarehouse } from "../../../commonmodel/csn/csnAnnotations";
import Constants from "../utility/Constants";
import Utils from "../utils";

sap.galilei.namespace("sap.cdw.transformationflow", function () {
  "use strict";
  const nsLocal = sap.cdw.transformationflow;
  const nsValidationStatus = sap.cdw.commonmodel.ValidationStatus;
  const nsCommonValidation = sap.cdw.commonmodel.Validation;
  const sMessageGroupId = "i18n_tf";

  /**
   * @class
   * Validation implements all methods related to the validation of model objects
   */
  sap.cdw.transformationflow.Validation = sap.galilei.core.defineClass({
    fullClassName: "sap.cdw.transformationflow.Validation",

    statics: {
      // Avoid infinite loop in validateModel()
      isValidatingModel: false,

      getAggregatedValidation: function (oObject, aChildren) {
        return nsCommonValidation.getAggregatedValidation(oObject, aChildren);
      },

      validateModel: async function (oModel: sap.cdw.transformationflow.Model): Promise<any> {
        this.doValidateModel(oModel);
      },

      doValidateModel: async function (oModel: sap.cdw.transformationflow.Model): Promise<any> {
        if (!oModel) {
          return;
        }
        // eslint-disable-next-line @typescript-eslint/no-this-alias
        const self = this;
        oModel.clearValidation();
        oModel.resource.applyUndoableAction(
          async function () {
            let sResult = "ok";
            let source: sap.cdw.transformationflow.Node;
            let target: sap.cdw.transformationflow.Node;
            for (let nIndex = 0; nIndex < oModel.nodes.length; nIndex++) {
              const oNode: sap.cdw.transformationflow.Node = oModel.nodes.get(nIndex);
              if (oNode.validate instanceof Function) {
                oNode.validate(/* bSkipRefreshDecorators*/ true);
              }
              if (oNode.isViewTransform || (Utils.isTableAsSourceSupportedSpark() && oNode.isSourceTable)) {
                source = oNode;
              } else if (oNode.component === Constants.OPERATORS.TARGET) {
                target = oNode;
              }
            }

            // validate load type
            if (oModel.loadType === Constants.LoadType.INITIAL_AND_DELTA) {
              if (!source.isDeltaTable) {
                sResult = "validationNonDeltaSource";
                nsValidationStatus.createErrorInstance(oModel, sMessageGroupId, sResult);
              }
              if (!target.isDeltaTable) {
                sResult = "validationNonDeltaTarget";
                nsValidationStatus.createErrorInstance(oModel, sMessageGroupId, sResult);
              }
            }
            if (source.isDeltaTable && !target.isTemplate && !target.isDeltaTable) {
              // delta source -> non-delta target
              const sName = target.technicalName;
              sResult = "validationDeltaSourceNonDeltaTarget";
              nsValidationStatus.createErrorInstance(oModel, sMessageGroupId, sResult, [sName]);
            }

            if (source.isDeltaTable && target.isDeltaTable && !oModel.isLoadTypeModifiedByUser) {
              sResult = "validationInitialDeltaLoadType";
              nsValidationStatus.createInfoInstance(oModel, sMessageGroupId, sResult);
            }

            if (source.component === Constants.OPERATORS.SQL_SCRIPT_TRANSFORM) {
              if (source.hasBWBridgeDeltaSource) {
                sResult = "validationBWDeltaSourceInSQLScriptTransform";
                nsValidationStatus.createErrorInstance(oModel, sMessageGroupId, sResult);
              }
            }
            if (oModel.isLargeSystemSpace && !target.isNew && !target.isTemplate) {
              const ltfAnnotation = target?.definition?.[DataWarehouse.persistence_hdlf_tableFormat];
              if (!(ltfAnnotation && target.isDeltaTable)) {
                sResult = "validationLTFDeltaTargetLargeSystemSpace";
                const sName = target.technicalName;
                nsValidationStatus.createErrorInstance(oModel, sMessageGroupId, sResult, [sName]);
              }
            }
            // Show validation error if a transformation flow from hana runtime space is imported into large system space
            if (oModel.isLargeSystemSpace && oModel.runtime !== Constants.Runtime.SPARK) {
              sResult = "validationNonLsaTFInLsaSpace";
              nsValidationStatus.createErrorInstance(oModel, sMessageGroupId, sResult);
            }
            if (!oModel.isLargeSystemSpace && oModel.runtime === Constants.Runtime.SPARK) {
              sResult = "validationLsaTFInNonLsaSpace";
              nsValidationStatus.createErrorInstance(oModel, sMessageGroupId, sResult);
            }
            // change management
            if (oModel.changeManagement) {
              let modifiedHeaderMessageId = "";
              let modifiedHeaderMessageParameters = [];
              let descriptionMessageId = "";
              let descriptionParameters = [];
              let descriptionParameterString;

              for (const modifiedObjectsItem of oModel.changeManagement.modifiedObjects) {
                for (const autoFixObjectsItem of modifiedObjectsItem.autoFixedObjects) {
                  // new columns
                  if (autoFixObjectsItem.changeInfo.newColumns.length > 0) {
                    modifiedHeaderMessageId = "validationChangeMgmtNewColMsgHdr";
                    modifiedHeaderMessageParameters = [autoFixObjectsItem.autoFixedObject.displayName];
                    descriptionMessageId = "validationChangeMgmtColsMsgDesc";
                    descriptionParameters = [];
                    descriptionParameterString = "";
                    for (const newColumn of autoFixObjectsItem.changeInfo.newColumns) {
                      if (descriptionParameterString !== "") {
                        descriptionParameterString = descriptionParameterString + "\n";
                      }
                      descriptionParameterString += "- " + newColumn.element.name;
                    }
                    if (descriptionParameterString !== "") {
                      descriptionParameters.push(descriptionParameterString);
                    }
                    // message for modified node - symbol
                    if (modifiedHeaderMessageId !== "" && descriptionParameterString !== "") {
                      nsValidationStatus.createInfoInstance(
                        modifiedObjectsItem.modifiedObject,
                        sMessageGroupId,
                        modifiedHeaderMessageId,
                        modifiedHeaderMessageParameters,
                        undefined,
                        undefined,
                        descriptionMessageId,
                        descriptionParameters
                      );
                    }
                  }
                  // data type change
                  if (autoFixObjectsItem.changeInfo.updatedDatatypeColumns.length > 0) {
                    modifiedHeaderMessageId = "validationChangeMgmtUpdDTColMsgHdr";
                    modifiedHeaderMessageParameters = [autoFixObjectsItem.autoFixedObject.displayName];
                    descriptionMessageId = "validationChangeMgmtColsMsgDesc";
                    descriptionParameters = [];
                    descriptionParameterString = "";
                    // message for modified node - symbol
                    for (const updatedColumn of autoFixObjectsItem.changeInfo.updatedDatatypeColumns) {
                      const formattedOldValue = Utils.generateDatatypeText(updatedColumn.old);
                      const formattedNewValue = Utils.generateDatatypeText(updatedColumn.new);
                      const updatedColumnText = self.localizeText("validationChangeMgmtUpdDTColMsgDesc", [
                        updatedColumn.element.displayName,
                        formattedOldValue,
                        formattedNewValue,
                      ]);
                      if (descriptionParameterString !== "") {
                        descriptionParameterString = descriptionParameterString + "\n";
                      }
                      descriptionParameterString += "- " + updatedColumnText;
                    }
                    descriptionParameters.push(descriptionParameterString);
                    nsValidationStatus.createInfoInstance(
                      autoFixObjectsItem.autoFixedObject,
                      sMessageGroupId,
                      modifiedHeaderMessageId,
                      modifiedHeaderMessageParameters,
                      undefined,
                      undefined,
                      descriptionMessageId,
                      descriptionParameters
                    );
                  }

                  // deleted columns
                  if (autoFixObjectsItem.changeInfo.deletedColumns.length > 0) {
                    const deletedColumns = autoFixObjectsItem.changeInfo.deletedColumns;
                    modifiedHeaderMessageId = "validationChangeMgmtDelColMsgHdr";
                    modifiedHeaderMessageParameters = [autoFixObjectsItem.autoFixedObject.displayName];
                    descriptionMessageId = "validationChangeMgmtColsMsgDesc";
                    descriptionParameters = [];
                    descriptionParameterString = "";
                    for (const deletedColumn of deletedColumns) {
                      if (descriptionParameterString !== "") {
                        descriptionParameterString += "\n";
                      }
                      descriptionParameterString += "- " + deletedColumn.displayName;
                    }
                    descriptionParameters.push(descriptionParameterString);
                    nsValidationStatus.createInfoInstance(
                      autoFixObjectsItem.autoFixedObject,
                      sMessageGroupId,
                      modifiedHeaderMessageId,
                      modifiedHeaderMessageParameters,
                      undefined,
                      undefined,
                      descriptionMessageId,
                      descriptionParameters
                    );
                  }

                  // updated columns --- business name
                  if (autoFixObjectsItem.changeInfo.updateBusinessNameColumns.length > 0) {
                    descriptionMessageId = "VAL_CHANGE_MGMT_COLUMNS_MSG_DESC";
                    modifiedHeaderMessageId = "VAL_CHANGE_MGMT_UPD_BN_COL_MSG_HDR";
                    modifiedHeaderMessageParameters = [autoFixObjectsItem.changeInfo.updateBusinessNameColumns.length];
                    descriptionParameterString = "";
                    descriptionParameters = [];
                    let updatedColumnText = "";
                    for (const updatedColumn of autoFixObjectsItem.changeInfo.updateBusinessNameColumns) {
                      updatedColumnText = `'${updatedColumn.element.displayName}': `;
                      if (typeof updatedColumn.old.label === "undefined") {
                        updatedColumnText += `'${updatedColumn.new.label}'${self.localizeText("VAL_VALUE_NEW")}`;
                      } else if (typeof updatedColumn.new.label === "undefined") {
                        updatedColumnText += ` - ${self.localizeText("VAL_VALUE_DELETED")}`;
                      } else {
                        updatedColumnText += `'${updatedColumn.old.label}'`;
                        updatedColumnText += ` -> '${updatedColumn.new.label}'`;
                      }
                      if (descriptionParameterString !== "") {
                        descriptionParameterString = `${descriptionParameterString}\n`;
                      }
                      descriptionParameterString += `- ${updatedColumnText}`;
                    }
                    descriptionParameters.push(descriptionParameterString);
                    nsValidationStatus.createInfoInstance(
                      autoFixObjectsItem.autoFixedObject,
                      sMessageGroupId,
                      modifiedHeaderMessageId,
                      modifiedHeaderMessageParameters,
                      undefined,
                      undefined,
                      descriptionMessageId,
                      descriptionParameters
                    );
                  }

                  if (autoFixObjectsItem.changeInfo.upsertModeChanged) {
                    modifiedHeaderMessageId = "VAL_CHANGE_MGMT_TARGET_UPSERT_CHANGED";
                    modifiedHeaderMessageParameters = [];
                    descriptionMessageId = "VAL_CHANGE_MGMT_MSG_TARGET_UPSERT_CHANGED";
                    descriptionParameters = [];
                    nsValidationStatus.createInfoInstance(
                      autoFixObjectsItem.autoFixedObject,
                      sMessageGroupId,
                      modifiedHeaderMessageId,
                      modifiedHeaderMessageParameters,
                      undefined,
                      undefined,
                      descriptionMessageId,
                      descriptionParameters
                    );
                  }
                  if (autoFixObjectsItem.changeInfo.updatedKeyColumns?.length > 0) {
                    modifiedHeaderMessageId = "validationChangeMgmtUpdKeyColMsgHdr";
                    modifiedHeaderMessageParameters = [autoFixObjectsItem.changeInfo.updatedKeyColumns.length];
                    descriptionMessageId = "validationChangeMgmtColsMsgDesc";
                    descriptionParameters = [];
                    descriptionParameterString = "";
                    let updatedColumnText = "";
                    for (const updatedColumn of autoFixObjectsItem.changeInfo.updatedKeyColumns) {
                      updatedColumnText = `'${updatedColumn.element.displayName}': `;
                      if (updatedColumn.new.isKey) {
                        updatedColumnText += self.localizeText("validationChangeMgmtKeyPropertySet");
                      } else if (updatedColumn.old.isKey) {
                        updatedColumnText += self.localizeText("validationChangeMgmtKeyPropertyRemoved");
                      }
                      if (descriptionParameterString !== "") {
                        descriptionParameterString = `${descriptionParameterString}\n`;
                      }
                      descriptionParameterString += `- ${updatedColumnText}`;
                    }
                    descriptionParameters.push(descriptionParameterString);
                    nsValidationStatus.createInfoInstance(
                      autoFixObjectsItem.autoFixedObject,
                      sMessageGroupId,
                      modifiedHeaderMessageId,
                      modifiedHeaderMessageParameters,
                      undefined,
                      undefined,
                      descriptionMessageId,
                      descriptionParameters
                    );
                  }
                }
              }
            }
            self.updatePackageValidations(oModel);
            if (
              target?.isDeltaTable === true &&
              source?.isDeltaTable === true &&
              oModel.loadType === Constants.LoadType.INITIAL_AND_DELTA &&
              oModel.highWaterMarkExists === true &&
              ((oModel.targetTableTechnicalName && oModel.targetTableTechnicalName !== target.technicalName) ||
                target.isNew === true)
            ) {
              sResult = "validationDeltaTargetTableChanged";
              nsValidationStatus.createWarnInstance(oModel, sMessageGroupId, sResult);
            }

            nsLocal.Validation.requestRefreshDecorators(oModel);
          },
          "New Validation",
          /* bIsprotectedFromUndo*/ true
        );
      },

      requestRefreshDecorators: function (oModel: sap.cdw.transformationflow.Model) {
        if (!oModel) {
          return;
        }
        if (typeof sap !== "undefined" && sap.ui && sap.ui.getCore instanceof Function) {
          sap.ui
            .getCore()
            .getEventBus()
            .publish(nsCommonValidation.VALIDATION_CHANNEL, nsCommonValidation.REFRESH_VALIDATION_EVENT, {
              model: oModel,
            });
        }
      },

      validateNode: function (
        oNode: sap.cdw.transformationflow.Node,
        bSkipRefreshDecorators: boolean,
        bSkipClear: boolean
      ) {
        let sResult = "ok";
        if (oNode) {
          if (bSkipClear !== true) {
            oNode.clearValidation();
          }
          // validate template
          if (oNode.isTemplate) {
            sResult = Utils.getValidationMessageTemplateNode(oNode);
            nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, sResult);
          } else {
            // validation for non template node
            sResult = this.validateNonTemplateNode(oNode);
          }

          if (!bSkipRefreshDecorators) {
            nsLocal.Validation.requestRefreshDecorators(oNode.container);
          }
        }
        return sResult;
      },

      validateNonTemplateNode: function (oNode: sap.cdw.transformationflow.Node) {
        let sResult = "ok";
        // check for empty attributes - common for all nodes
        const attributes = oNode.getAttributesArray();
        const attributeLength = attributes && attributes.length;
        if (attributeLength === 0) {
          sResult = "validationEmptyColumns";
          const errorInstance = nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, sResult, [
            oNode.displayName,
          ]);
          if (oNode.component === Constants.OPERATORS.TARGET && oNode.isNew) {
            // add blocker if no columns in the new target
            // this will prevent user from saving the editor and no target table will be created without columns
            errorInstance.isBlocker = true;
          }
        }
        // validate target table
        if (oNode.component === Constants.OPERATORS.TARGET) {
          sResult = this.validateTargetNode(oNode);
        }
        // validate view transform operator
        if (
          oNode.component === Constants.OPERATORS.VIEW_TRANSFORM ||
          oNode.component === Constants.OPERATORS.SQL_TRANSFORM ||
          oNode.component === Constants.OPERATORS.SQL_SCRIPT_TRANSFORM
        ) {
          sResult = this.validateViewTransformNode(oNode);
        }
        // validate python node
        if (oNode.component === Constants.OPERATORS.PYTHON) {
          sResult = this.validatePythonNode(oNode);
        }
        return sResult;
      },

      validatePythonNode: function (oNode: sap.cdw.transformationflow.Node) {
        let sResult = "ok";
        const oModel = oNode.container;
        // Always show a validation info message for the Python node
        nsValidationStatus.createInfoInstance(oNode, sMessageGroupId, "validationUpdatePythonScript");

        // Filter CDC columns from both source and Python node
        const sourceNodeCDCColumns = oNode
          .getSourceNode()
          .getAttributesArray()
          .filter((column) => column.isCDCColumn === true);
        const pythonNodeCDCColumns = oNode.getAttributesArray().filter((column) => column.isCDCColumn === true);

        // Compare CDC columns between source and Python node
        const isMatchingCDCColumns = this.compareCDCColumns(sourceNodeCDCColumns, pythonNodeCDCColumns);

        // If load type is initial and delta, ensure CDC columns match between the source and Python node
        if (oModel.loadType === Constants.LoadType.INITIAL_AND_DELTA && !isMatchingCDCColumns) {
          sResult = "validationChangedCDCColumns";
          nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, sResult);
        }

        return sResult;
      },

      validateTargetNode: function (oNode: sap.cdw.transformationflow.Node) {
        let sResult = "ok";
        const primaryKeyColumns = oNode.getAttributesArray().filter((item) => item.key);
        // new target
        if (oNode.isNew) {
          if (oNode.uniqueTechnicalName === false) {
            sResult = "validationDuplicateTechnicalName";
            const sName = oNode.technicalName;
            const errorInstance = nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, sResult, [sName]);
            errorInstance.isBlocker = true;
          }
          if (oNode.technicalName === "") {
            sResult = "validationEmptyTechnicalTargetTable";
            const errorInstance = nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, sResult);
            errorInstance.isBlocker = true;
          }
          if (oNode.isDeltaTable) {
            if (oNode.isNew) {
              if (!primaryKeyColumns.length) {
                sResult = "validationNoKeyColumn";
                const errorInstance = nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, sResult);
                errorInstance.isBlocker = true;
              }

              // Find duplicate columns
              const attributes = oNode.getAttributesArray();
              const attributeNames = new Map<string, int>();
              for (const attribute of attributes) {
                if (attributeNames.has(attribute.name)) {
                  attributeNames.set(attribute.name, attributeNames.get(attribute.name) + 1);
                } else {
                  attributeNames.set(attribute.name, 1);
                }
              }

              attributeNames.forEach((value, key) => {
                if (value > 1) {
                  // Column already exists
                  sResult = "validationColumnNameNotUnique";
                  const errorInstance = nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, sResult, [
                    key,
                    oNode.technicalName,
                  ]);
                  errorInstance.isBlocker = true;
                }
              });

              const cdcColumns: sap.cdw.transformationflow.Attribute[] = attributes.filter(
                (column: sap.cdw.transformationflow.Attribute) => column.isCDCColumn === true
              );

              if (cdcColumns.length < 2) {
                sResult = "validationMissingCDCColumns";
                const errorInstance = nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, sResult, [
                  oNode.technicalName,
                ]);
                errorInstance.isBlocker = true;
              }
            }
            if (oNode.uniqueDeltaCaptureTableName === false) {
              sResult = "validationDuplicateTechnicalName";
              const sName = Utils.generateDeltaCaptureTableName(oNode.technicalName);
              const errorInstance = nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, sResult, [sName]);
              errorInstance.isBlocker = true;
            }
          }
        } else {
          // existing target
          // validate mappings
          sResult = this.validateTargetNodeMappings(oNode);
        }
        // common for new and existing target
        const oModel = oNode.container;
        if (oNode.truncate && oModel.loadType === Constants.LoadType.INITIAL_AND_DELTA) {
          sResult = isTableDeleteDataEnabled()
            ? "validationDeleteAllBeforeLoadingInvalidMode"
            : "validationTruncateInvalidMode";
          nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, sResult);
        }
        if (oNode.truncate && oNode.isDeltaTable) {
          sResult = isTableDeleteDataEnabled()
            ? "validationDeleteAllBeforeLoadingDeltaTarget"
            : "validationTruncateDeltaTarget";
          nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, sResult);
        }
        if (!oNode.truncate && primaryKeyColumns.length > 0) {
          sResult = isTableDeleteDataEnabled()
            ? "validationDeleteAllBeforeLoadingOffandKeyColumn"
            : "validationTruncateOffandKeyColumn";
          nsValidationStatus.createInfoInstance(oNode, sMessageGroupId, sResult);
        }
        return sResult;
      },

      validateTargetNodeMappings: function (oNode: sap.cdw.transformationflow.Node) {
        let sResult = "ok";
        const attributeMappings = oNode.attributeMappings.toArray();
        const primaryKeyColumns = oNode.getAttributesArray().filter((item) => item.key);
        const missingCdcChangeTypeMapping = attributeMappings.some(
          (mapping: sap.cdw.transformationflow.AttributeMapping) => {
            const hasValidTarget = mapping?.target?.datatype === "cds.String" && mapping?.target?.isCDCColumn === true;
            const hasValidSource = mapping?.source?.datatype === "cds.String" && mapping?.source?.isCDCColumn === true;

            return hasValidTarget && hasValidSource;
          }
        );
        const viewTransformNode = oNode.getViewTransformNode();
        if (viewTransformNode?.isDeltaTable && oNode?.isDeltaTable && !missingCdcChangeTypeMapping) {
          // No mapping defined for Change_Type_column
          const errorMessage = "validationTargetMappingMissingForChangeTypeColumn";
          nsValidationStatus.createWarnInstance(oNode, sMessageGroupId, errorMessage, ["Change Type"]);
        }

        // Check if oNode target columns have CDC columns Change_Date
        const changeDateExistOnTarget = oNode
          .getAttributesArray()
          .some((nodeColumn) => nodeColumn?.datatype === "cds.Timestamp" && nodeColumn?.isCDCColumn === true);
        if (changeDateExistOnTarget) {
          // Info warning message for Change Date
          const warningMessage = "validationTargetMappingMissingForChangeDateColumn";
          nsValidationStatus.createInfoInstance(oNode, sMessageGroupId, warningMessage, ["Change Date"]);
        }
        if (attributeMappings.length) {
          for (const attributeMapping of attributeMappings) {
            const sourceColumn = attributeMapping.source;
            const targetColumn = attributeMapping.target;
            if (sourceColumn && targetColumn) {
              // validate source and target
              const value = Utils.validateTargetColumnMapping(sourceColumn, targetColumn);
              if (value === Constants.Mapping.Invalid) {
                // invalid mapping
                sResult = "validationTargetColumnMappingIncompatible";
                nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, sResult, [
                  sourceColumn.name,
                  sourceColumn.datatypeText,
                  targetColumn.name,
                  targetColumn.datatypeText,
                  oNode.displayName,
                ]);
              }
            }
            if (targetColumn.key && primaryKeyColumns.includes(targetColumn)) {
              // remove item from PK list
              primaryKeyColumns.splice(primaryKeyColumns.indexOf(targetColumn), 1);
            }
          }
          if (primaryKeyColumns.length) {
            // unmapped primary key column
            sResult = "validationTargetMappingMissingForPrimaryKeyColumn";
            nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, sResult, [
              primaryKeyColumns[0].name,
              oNode.displayName,
            ]);
          }
        } else {
          // no mapping defined
          sResult = "validationTargetMappingMissing";
          nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, sResult, [oNode.displayName]);
        }
        return sResult;
      },

      validateViewTransformNode: function (oNode: sap.cdw.transformationflow.Node) {
        // let sResult = "ok";
        let sResult = "ok";
        if (oNode.isValidationSkipped) {
          sResult = "VAL_TENANT_UPGRADE_IN_PROGRESS";
          nsValidationStatus.createInfoInstance(oNode, sMessageGroupId, sResult);
        }
        // errors in view transform model
        if (oNode.internalValidationStatus === "error") {
          if (oNode.component === Constants.OPERATORS.VIEW_TRANSFORM) {
            sResult = "validationGraphicalViewTransformHasError";
          } else {
            sResult = "validationSQLViewTransformHasError";
          }

          nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, sResult, [oNode.name]);
        }
        if (
          Array.isArray(oNode.changeManagement?.modifiedObjects) &&
          oNode.changeManagement?.modifiedObjects.length > 0
        ) {
          if (oNode.component === Constants.OPERATORS.VIEW_TRANSFORM) {
            sResult = "validationGraphicalViewTransformHasChangeManagement";
          } else if (oNode.component === Constants.OPERATORS.SQL_TRANSFORM) {
            const hasSource = Utils.changeManagementHasSource(oNode.changeManagement);
            if (hasSource) {
              sResult = "validationSQLViewTransformHasChangeManagement";
            } else {
              sResult = "ok";
            }
            // sResult = "validationSQLViewTransformHasChangeManagement";
          } else {
            sResult = "validationSQLViewTransformHasChangeManagement";
          }

          if (sResult !== "ok") {
            nsValidationStatus.createErrorInstance(oNode, sMessageGroupId, sResult, [oNode.name]);
          }
        }
        return sResult;
      },

      /**
       * Returns a localized text
       * @param textId id of the text (i18n properties file)
       * @param parameters values for the placeholders of the text resource (see i18n properties file, {0}, {1}, ...)
       */
      localizeText: function (textId: string, parameters: string[]) {
        if (
          typeof sap !== "undefined" &&
          sap.ui &&
          sap.ui.getCore instanceof Function &&
          sap.ui.model &&
          sap.ui.model.resource &&
          sap.ui.model.resource.ResourceModel
        ) {
          const bundleName = require("../../i18n/i18n.properties");
          const resourceModel = new sap.ui.model.resource.ResourceModel({
            bundleName: bundleName,
          });
          return resourceModel.getResourceBundle().getText(textId, parameters);
        }
        return textId;
      },
      updatePackageValidations: function (oModel: sap.cdw.transformationflow.Model) {
        if ((oModel as any)?.packageStatus?.length > 0) {
          const msg: any = nsValidationStatus.createWarnInstance(
            oModel,
            sMessageGroupId,
            (oModel as any)?.packageStatus,
            [oModel.name, (oModel as any).packageValue]
          );
          msg.isExternal = true;
        }
      },
      compareCDCColumns: function (
        sourceNodeCDCColumns: sap.cdw.transformationflow.Attribute[],
        pythonNodeCDCColumns: sap.cdw.transformationflow.Attribute[]
      ) {
        // Number of CDC columns in both nodes doesn't match
        if (sourceNodeCDCColumns.length !== pythonNodeCDCColumns.length) {
          return false;
        }
        const sourceColumns = new Map();
        for (const column of sourceNodeCDCColumns) {
          sourceColumns.set(column.name, { datatype: column.datatype, length: column.length });
        }

        // Iterate over pythonNodeCDCColumns and check if each column exists and matches
        for (const pythonColumn of pythonNodeCDCColumns) {
          const sourceColumn = sourceColumns.get(pythonColumn.name);
          if (
            !sourceColumn ||
            sourceColumn.datatype !== pythonColumn.datatype ||
            sourceColumn.length !== pythonColumn.length
          ) {
            return false;
          }
        }

        return true;
      },
    },
  });
});
