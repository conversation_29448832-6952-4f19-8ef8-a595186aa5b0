/**
 * Copyright 2023 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

export default class Constants {
  static NewTargetTableComponent = "com.sap.dwc.target";
  static TargetTemplateBusinessName = "Target";
  static NewTargetBusinessName = "Target 1";
  static NewTargetTechnicalName = "Target_1";

  static PythonOperatorDisplayName = "Python";

  static UsageActions = {};

  static DELTA_TABLE_SUFFIX = "_Delta";
  static DELTA_ACTIVE_RECORDS_VIEW_TYPE = "ACTIVE";

  static SECONDARY_SOURCE = "com.sap.dwc.secondarysource";

  static OPERATORS = {
    VIEW_TRANSFORM: "com.sap.dwc.viewtransform",
    SQL_TRANSFORM: "com.sap.dwc.sqltransform",
    SQL_SCRIPT_TRANSFORM: "com.sap.dwc.sqlscripttransform",
    TARGET: "com.sap.dwc.target",
    PYTHON: "com.sap.dwc.python",
    SOURCE_TABLE: "com.sap.dwc.source.table",
  };

  static PROCESS_IDS = {
    VIEW_TRANSFORM: "viewtransform1",
    SQL_TRANSFORM: "sqltransform1",
    SQL_SCRIPT_TRANSFORM: "sqlscripttransform1",
    PYTHON: "python1",
    TARGET: "target1",
    SOURCE_TABLE: "sourcetable1",
  };

  static TRANSFORM_OPERATORS = new Set([
    Constants.OPERATORS.VIEW_TRANSFORM,
    Constants.OPERATORS.SQL_TRANSFORM,
    Constants.OPERATORS.SQL_SCRIPT_TRANSFORM,
  ]);
  static TARGET_OPERATORS = new Set([Constants.OPERATORS.TARGET]);
  static SOURCE_TABLE_OPERATORS = new Set([Constants.OPERATORS.SOURCE_TABLE]);

  static EVENTS = {
    TF_DIAGRAM_CHANNEL: "tfDiagram",
    UNDO_REDO_EVENT: "undoRedo",
  };

  static Action = {
    Column: {
      Delete: "deleteColumn",
      Rename: "renameColumn",
      DataTypeChange: "updateDataType",
    },
  };

  static TargetDataTypeMap = {
    "cds.String": {
      valid: ["cds.String"],
    },
    "cds.LargeString": {
      valid: ["cds.LargeString"],
    },
    "cds.Integer": {
      valid: ["cds.Integer"],
    },
    "cds.Integer64": {
      valid: ["cds.Integer64"],
    },
    "cds.Double": {
      valid: ["cds.Double"],
    },
    "cds.Date": {
      valid: ["cds.Date"],
    },
    "cds.DateTime": {
      valid: ["cds.DateTime"],
    },
    "cds.Time": {
      valid: ["cds.Time"],
    },
    "cds.Timestamp": {
      valid: ["cds.Timestamp"],
    },
    "cds.UTCTimestamp": {
      valid: ["cds.UTCTimestamp"],
    },
    "cds.Boolean": {
      valid: ["cds.Boolean"],
    },
    "cds.Decimal": {
      valid: ["cds.Decimal"],
    },
    "cds.DecimalFloat": {
      valid: ["cds.DecimalFloat"],
    },
    "cds.Binary": {
      valid: ["cds.Binary"],
    },
    "cds.LargeBinary": {
      valid: ["cds.LargeBinary"],
    },
    "cds.UUID": {
      valid: ["cds.UUID"],
    },
    "cds.Association": {
      valid: ["cds.Association"],
    },
    "cds.Composition": {
      valid: ["cds.Composition"],
    },
    "cds.hana.SMALLINT": {
      valid: ["cds.hana.SMALLINT"],
    },
    "cds.hana.TINYINT": {
      valid: ["cds.hana.TINYINT"],
    },
    "cds.hana.SMALLDECIMAL": {
      valid: ["cds.hana.SMALLDECIMAL"],
    },
    "cds.hana.REAL": {
      valid: ["cds.hana.REAL"],
    },
    "cds.hana.NCHAR": {
      valid: ["cds.hana.NCHAR"],
    },
    "cds.hana.VARCHAR": {
      valid: ["cds.hana.VARCHAR"],
    },
    "cds.hana.BINARY": {
      valid: ["cds.hana.BINARY"],
    },
    "cds.hana.ST_POINT": {
      valid: ["cds.hana.ST_POINT"],
    },
    "cds.hana.ST_GEOMETRY": {
      valid: ["cds.hana.ST_GEOMETRY"],
    },
    // int16: {
    //   valid: ["int16", "int8"],
    // },
  };

  static Mapping = {
    Valid: "valid",
    Truncation: "truncation",
    Invalid: "invalid",
    InvalidTimestampColumn: "InvalidTimestampColumn",
  };

  static LoadType = {
    INITIAL_ONLY: "INITIAL_ONLY",
    INITIAL_AND_DELTA: "INITIAL_AND_DELTA",
  };

  static LoadTypes = [
    {
      key: Constants.LoadType.INITIAL_ONLY,
      displayName: "initialOnly",
    },
    {
      key: Constants.LoadType.INITIAL_AND_DELTA,
      displayName: "initialAndDelta",
    },
  ];

  static LoadFromLocalTableType = {
    DELTA: "Delta",
    ACTIVE_RECORDS: "ActiveRecords",
  };

  static DeltaTableType = {
    UPSERT: "UPSERT",
    ACTIVE: "ACTIVE",
  };

  static ParametersList = ["REQTSN_LOW", "REQTSN_HIGH", "EXTRACTION_MODE", "FROM_CHANGE_TIME", "TILL_CHANGE_TIME"];

  static Runtime = {
    HANA: "HANA",
    SPARK: "SPARK",
    HanaDisplayName: "SAP HANA",
    SparkDisplayName: "Apache Spark",
  };
  static FileStorage = "File";

  static DATA_TYPE_DEFAULT = {
    LENGTH: 10,
    PRECISION: 5,
    SCALE: 2,
  };

  static TargetParametersLTF = {
    EXTRACTION_MODE: {
      "@EndUserText.label": "Extraction Mode",
      type: "cds.String",
      length: 10,
      default: "FULL",
      "@DataWarehouse.bw.extractionMode": true,
    },
    FROM_CHANGE_TIME: {
      "@EndUserText.label": "From Change Time",
      type: "cds.Timestamp",
      default: "0001-01-01 00:00:00",
    },
    TILL_CHANGE_TIME: {
      "@EndUserText.label": "Till Change Time",
      type: "cds.Timestamp",
      default: "9999-12-31 23:59:59",
    },
  };

  static SemanticsInterval = [
    {
      qualifier: "changeTime",
      lowerBoundaryParameter: { "=": "FROM_CHANGE_TIME" },
      lowerBoundaryIncluded: false,
      upperBoundaryParameter: { "=": "TILL_CHANGE_TIME" },
      upperBoundaryIncluded: true,
    },
  ];

  static RepoObjectDetails = [
    "id",
    "name",
    "@EndUserText.label",
    "kind",
    "csn",
    "modification_date",
    "deployment_date",
    "#deploymentExecutionStatus",
    "#objectStatus",
    "releaseStateValue",
    "#technicalType",
    "#repositoryPackage",
    "owner", // modifier
  ];

  static SharedObjectDetails = [...Constants.RepoObjectDetails, "#repairedCsn"];

  static parameterCreatedBy = {
    USER: "user",
    SYSTEM: "system",
  };

  static aggregationtypes = {
    SUM: "SUM",
    COUNT: "COUNT",
    LAST: "LAST",
  };
}
