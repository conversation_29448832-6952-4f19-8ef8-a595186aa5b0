/**
 * Copyright 2023 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import DiagramBuilder from "@sap/velocity-data-tools-ui/src/vflow/common/galilei/diagramBuilder";
import { DataWarehouse } from "../../../commonmodel/csn/csnAnnotations";
import { getReleaseStateFromValue } from "../../../commonmodel/utility/CompatibilityContractUtils";
import { objectStatusTextFormatter } from "../../../ermodeler/js/utility/sharedFunctions";
import { MessageHandler } from "../../../reuse/utility/MessageHandler";
import { IModel } from "../../../shell/utility/Repo";
import Constants from "../utility/Constants";
import Utils from "../utils";

const MODEL_PACKAGE = "sap.cdw.transformationflow";
const VFLOW_MODEL_PACKAGE = "sap.modeling.vflow";
const DIAGRAM_PACKAGE = `${MODEL_PACKAGE}.ui`;

interface IIncrementalMaintenance {
  deferredAggregations?: IAggregationDefinition[];
}

interface IAggregationDefinition {
  sourceColumnName: string;
  columnName: string;
  type: string;
  aggregationType: AggregationType;
}

enum AggregationType {
  SUM = "SUM",
  COUNT = "COUNT",
}

export class TFDiagramBuilder extends DiagramBuilder {
  controller: any;
  oEditor: any;
  oResource: any;
  oModel: any;
  library: any;
  modelValidationTimer: any;
  nodeValidationTimer: any;
  attributeMappings: Map<sap.cdw.transformationflow.Node, Array<{ source: string; target: string }>>;
  params: any;

  constructor(oEditor: sap.galilei.ui.editor.DiagramEditor, library: any, parentCtrl: any) {
    const properties = {
      oEditor: oEditor,
      oResource: oEditor.resource,
      oModel: oEditor.model,
      oDiagram: oEditor.diagram,
      library: library,
      iconpath: "sap-icon://",
      ns: MODEL_PACKAGE,
    };
    super(properties);
    this.oEditor = oEditor;
    this.oResource = oEditor.resource;
    this.oModel = oEditor.model;
    this.library = library;
    this.controller = parentCtrl;
  }

  buildModel(graphContent) {
    this.attributeMappings = new Map();
    super.buildModel(graphContent);
    this.createAttributeMappings();
    this.checkTemplateModel();
    this.createParameters(graphContent);
    // update loadType
    this.oModel.loadType = graphContent.metadata?.loadType || Constants.LoadType.INITIAL_ONLY;
    // update isLoadTypeModifiedByUser flag
    if (graphContent?.metadata && graphContent.metadata?.hasOwnProperty("isLoadTypeModifiedByUser")) {
      this.oModel.isLoadTypeModifiedByUser = graphContent.metadata.isLoadTypeModifiedByUser;
    }
    // update incremental aggregations
    if (
      Utils.isIncrementalAggregationSupported() &&
      this.oModel.isLargeSystemSpace &&
      graphContent?.metadata &&
      graphContent.metadata?.hasOwnProperty("incrementalMaintenance")
    ) {
      this.updateIncrementalAggregation(graphContent.metadata.incrementalMaintenance);
    }
  }

  /**
   *
   *
   * @param {IIncrementalMaintenance} incrementalMaintenance
   * @memberof TFDiagramBuilder
   */
  updateIncrementalAggregation(incrementalMaintenance: IIncrementalMaintenance) {
    const oNode = this.oModel.nodes.toArray().find((node) => node.component === Constants.OPERATORS.TARGET);
    incrementalMaintenance.deferredAggregations.forEach((agg) => {
      const column = oNode.getAttributesArray().find((col) => col.name === agg.columnName);
      if (column) {
        column.aggregation = agg.aggregationType;
      }
    });
  }

  checkTemplateModel() {
    if (this.oModel.nodes) {
      let isTemplate = true;
      for (const node of this.oModel.nodes.toArray()) {
        if (!node.isTemplate) {
          isTemplate = false;
          break;
        }
      }
      this.oModel.isTemplate = isTemplate;
      this.oEditor.viewer?.mainShapeLayer?.d3Layer.selectAll("#trf-welcomeText").remove();
      if (this.oModel.isTemplate) {
        // welcome text node doe not
        this.createWelcomeTextShape();
      }
    }
  }

  createWelcomeTextShape() {
    const welcomeText =
      this.oModel.isLargeSystemSpace && Utils.isTableAsSourceSupportedSpark() ? "welcomeTextDndSpark" : "welcomeText";
    const dx = this.oModel.isLargeSystemSpace && Utils.isTableAsSourceSupportedSpark() ? "-10rem" : "-5rem";
    return this.oEditor.viewer?.mainShapeLayer?.d3Layer
      ?.insert("svg:g")
      .attr("id", "trf-welcomeText")
      .append("text")
      .attr("fill", "#666")
      .attr("dx", dx)
      .attr("dy", "6rem")
      .attr("font-size", ".875rem")
      .text(this.controller.localizeText(welcomeText));
  }

  readAndClearValueFromConfig(key: string, metadata: any) {
    const value = metadata?.config?.[key];
    if (value !== undefined) {
      delete metadata.config[key];
    }
    return value;
  }

  /**
   *
   *
   * @param {*} options
   * @returns {sap.cdw.transformationflow.Node}
   * @memberof TFDiagramBuilder
   */
  createNode(options: any): sap.cdw.transformationflow.Node {
    const properties = options?.oNodeProperties;
    const attributeMappings = this.readAndClearValueFromConfig("attributeMappings", properties.metadata);
    const definition = this.readAndClearValueFromConfig("definition", properties.metadata);
    const editorSettings = this.readAndClearValueFromConfig("editorSettings", properties.metadata);
    const cdcColumns = this.readAndClearValueFromConfig("cdcColumns", properties.metadata);
    const version = this.readAndClearValueFromConfig("version", properties.metadata);
    const meta = this.readAndClearValueFromConfig("meta", properties.metadata);
    const $version = this.readAndClearValueFromConfig("$version", properties.metadata);
    const truncate = this.readAndClearValueFromConfig("truncate", properties.metadata);

    const oNode = super.createNode(options);
    if (properties.metadata.template) {
      oNode.isTemplate = true;
      if (oNode.component === Constants.OPERATORS.TARGET) {
        oNode.businessName = Constants.TargetTemplateBusinessName;
        oNode.displayName = Constants.TargetTemplateBusinessName;
      }
    } else {
      oNode.definition = definition; // common for all nodes
      // create attributes
      this.createAttributesFromCSNElements(oNode);
      if (oNode.component === Constants.OPERATORS.TARGET) {
        // push attribute mappings to the MAP
        if (attributeMappings?.length) {
          this.attributeMappings.set(oNode, attributeMappings);
        }
        // set business name, technical name and truncate value
        oNode.businessName = oNode.definition["@EndUserText.label"];
        oNode.technicalName = oNode.config.name;
        oNode.truncate = truncate;
        delete oNode.config.name;
      } else if (oNode.isViewTransform) {
        oNode.editorSettings = editorSettings;
        oNode.cdcColumns = cdcColumns;
        oNode.version = version;
        oNode.meta = meta;
        oNode.$version = $version;
        // update CDC columns
        this.updateCDCColumns(oNode);
        // update annotation as dataset if it was set differently before
        if (oNode.definition?.["@ObjectModel.modelingPattern"]?.["#"] !== "DATA_STRUCTURE") {
          oNode.definition["@ObjectModel.supportedCapabilities"] = [{ "#": "DATA_STRUCTURE" }];
          oNode.definition["@ObjectModel.modelingPattern"] = { "#": "DATA_STRUCTURE" };
        }
      } else if (oNode.component === Constants.OPERATORS.PYTHON) {
        oNode.businessName = Constants.PythonOperatorDisplayName;
        oNode.cdcColumns = cdcColumns;
        // update CDC columns
        this.updateCDCColumns(oNode);
      } else if (oNode.component === Constants.OPERATORS.SOURCE_TABLE && Utils.isTableAsSourceSupportedSpark()) {
        oNode.cdcColumns = cdcColumns;
        // update CDC columns
        this.updateCDCColumns(oNode);
        const sourceTableName = this.readAndClearValueFromConfig("sourceTableName", properties.metadata);
        oNode.technicalName = sourceTableName;
        const deltaAnnotation = definition[DataWarehouse.delta];
        if (deltaAnnotation && deltaAnnotation.type["#"] !== Constants.DeltaTableType.ACTIVE) {
          oNode.useAs = Constants.LoadFromLocalTableType.DELTA;
          oNode.deltaTableName = oNode.technicalName;
          oNode.displayTechnicalName = oNode.definition["@DataWarehouse.enclosingObject"];
        } else {
          oNode.useAs = Constants.LoadFromLocalTableType.ACTIVE_RECORDS;
          oNode.displayTechnicalName = oNode.technicalName;
          oNode.deltaTableName = oNode.definition["@DataWarehouse.delta"].deltaFromEntities[0];
        }
      }
    }
    this.updateSourceIcon(oNode);
    return oNode;
  }
  /**
   *
   *
   * @param {sap.cdw.transformationflow.Node} oNode
   * @memberof TFDiagramBuilder
   */
  updateSourceIcon(oNode: sap.cdw.transformationflow.Node) {
    if (oNode.isViewTransform) {
      let icon: string;
      if (oNode.isTemplate) {
        icon = ""; // Set empty string for template. This will be used in diagram.ts to set visibility of view transform template icon.
      } else {
        switch (oNode.component) {
          case Constants.OPERATORS.SQL_TRANSFORM:
          case Constants.OPERATORS.SQL_SCRIPT_TRANSFORM:
            icon = "sap-icon://sac/SQL";
            break;
          case Constants.OPERATORS.VIEW_TRANSFORM:
            icon = "sap-icon://sac/table-view";
        }
      }
      if (oNode.icon !== icon) {
        oNode.icon = icon;
        this.oEditor.drawAllSymbols();
      }
    }
  }

  updateCDCColumns(oNode: sap.cdw.transformationflow.Node) {
    if (oNode && Array.isArray(oNode.cdcColumns)) {
      for (const cdcColumn of oNode.cdcColumns) {
        const attribute: sap.cdw.transformationflow.Attribute = oNode.attributes.selectObject({ name: cdcColumn });
        if (attribute) {
          attribute.isCDCColumn = true;
        }
      }
    }
  }

  /**
   *
   *
   * @param {sap.cdw.transformationflow.Attribute} oAttribute
   * @param {sap.cdw.transformationflow.Node} oNode
   * @returns
   * @memberof TFDiagramBuilder
   */
  findSourceColumn(oAttribute: sap.cdw.transformationflow.Attribute, oNode: sap.cdw.transformationflow.Node) {
    return;
  }

  updateNodeElements(oNode: sap.cdw.transformationflow.Node, csn, cdcColumns: string[] = []) {
    const attributes = oNode.getAttributesArray();
    const details = Utils.getDWCMetadata(csn, cdcColumns, this.oModel.customTypesMap);

    const columnsMap = details.columns;
    const a2delete = [];
    const a2insert: sap.cdw.transformationflow.Attribute[] = Array.from(columnsMap.values());

    // iterate columns
    // - merge element changes (see updateObjectUsingMapping)
    // - look out for new and deleted columns
    for (const element of attributes) {
      const sName = element && element.name;

      if (sName) {
        const processedElementCsn = columnsMap.get(sName);
        // Element has been deleted or unsupported type?
        if (processedElementCsn === undefined) {
          const deletedColumn = {
            elementName: sName,
            displayName: sName,
            element: element,
          };
          a2delete.push(deletedColumn);
        } else {
          const modify = { datatype: false };
          // - data type
          if (element.datatype !== processedElementCsn.datatype) {
            modify.datatype = true;
          } else if (
            (element.datatype === "cds.String" || element.datatype === "cds.Binary") &&
            element.length !== processedElementCsn.length
          ) {
            modify.datatype = true;
          } else if (
            element.datatype === "cds.Decimal" &&
            (element.precision !== processedElementCsn.precision || element.scale !== processedElementCsn.scale)
          ) {
            modify.datatype = true;
          }
          // add to change management
          if (modify.datatype) {
            const newColumnData = {
              datatype: processedElementCsn.datatype,
              length: processedElementCsn.length,
              precision: processedElementCsn.precision,
              scale: processedElementCsn.scale,
            };

            // update datatype
            this.updateDatatype(element, newColumnData, true /* propagate*/);
          }
          // update key
          if (
            (element.key && (processedElementCsn.key === undefined || !processedElementCsn.key)) ||
            (processedElementCsn.key && (element.key === undefined || !element.key))
          ) {
            this.updateKeyColumns(
              element,
              { key: processedElementCsn.key ? processedElementCsn.key : false },
              true /* propagate*/
            );
          }
          // update business name
          if (element.label !== processedElementCsn.label) {
            this.updateBusinessName(element, processedElementCsn.label);
          }

          // update if cdc column
          if (
            (element.isCDCColumn &&
              (processedElementCsn.isCDCColumn === undefined || !processedElementCsn.isCDCColumn)) ||
            (processedElementCsn.isCDCColumn && (element.isCDCColumn === undefined || !element.isCDCColumn))
          ) {
            this.updateIsCDCColumnFlag(
              element,
              { isCDCColumn: processedElementCsn.isCDCColumn ? processedElementCsn.isCDCColumn : false },
              true /* propagate*/
            );
          }
          if (processedElementCsn.isCDCColumn) {
            element.isCDCColumn = true;
          } else {
            element.isCDCColumn = false;
          }
          // remove from objects to insert..
          const index = a2insert.indexOf(processedElementCsn);
          if (index > -1) {
            a2insert.splice(index, 1);
          }
        }
      }
    }

    const targetTable: sap.cdw.transformationflow.Node = oNode.getTargetTableNode();
    // move the existing cdc column in the target for deletion when the source has cdc columns
    const cdcColumns2insert = a2insert.filter((column) => column.isCDCColumn === true);
    const cdcColumn2delete = [];
    for (let i = a2delete.length; i > 0; i--) {
      const attribute = a2delete[i - 1].element;
      if (attribute.isCDCColumn) {
        cdcColumn2delete.push(attribute);
      }
    }
    if (cdcColumns2insert.length || cdcColumn2delete.length) {
      if (targetTable.isNew && targetTable.isDeltaTable) {
        this.deleteCDCColumns(targetTable);
        const sourceCDCColumns: sap.cdw.transformationflow.Attribute[] = Array.from(columnsMap.values()).filter(
          (column: sap.cdw.transformationflow.Attribute) => column.isCDCColumn === true
        ) as sap.cdw.transformationflow.Attribute[];
        if (sourceCDCColumns.length) {
          this.addSourceCDCColumnsToTarget(sourceCDCColumns, targetTable);
        } else {
          this.addDefaultCDCColumns(targetTable);
        }
      }
    }

    // Delete missing elements..
    for (let i = a2delete.length; i > 0; i--) {
      const attribute = a2delete[i - 1].element;
      if (attribute.isCDCColumn && targetTable.isNew) {
        // delete column and do not propagate cdc column deletion to new target (cdc column deletion propagation to new target is handled above)
        this.deleteAttribute(attribute, true, false /* propagate cdc column*/);
      } else {
        this.deleteAttribute(attribute, true);
      }
    }

    // Add new elements..
    for (const element of a2insert) {
      if (element.isCDCColumn && targetTable.isNew) {
        // add column and do not propagate cdc columns to new target (cdc column addition propagation to new target is handled above)
        this.createAttribute(element, oNode, true /* propagate*/, false /* propagate cdc column*/);
      } else {
        // add column and propagate to target
        this.createAttribute(element, oNode, true /* propagate*/);
      }
    }

    // refresh diagram
    this.checkTemplateModel();
  }

  createAttributesFromCSNElements(oNode: sap.cdw.transformationflow.Node, propagate?: boolean) {
    if (oNode.definition?.elements) {
      const elements = oNode.definition.elements;
      for (const item in elements) {
        const technicalName = oNode.technicalName ? oNode.technicalName : oNode.config?.name;
        const definition = {
          [technicalName]: oNode.definition,
        };
        const element = Utils.getItemFromDefinition(definition, elements[item], this.oModel.customTypesMap);
        if (element && element.type && element.type !== "cds.Association") {
          const attributeDefinition = {
            name: item,
            label: element["@EndUserText.label"] ? element["@EndUserText.label"] : item,
            key: element.key,
            datatype: element.type,
            precision: element.precision,
            scale: element.scale,
            length: element.length,
            notNull: element.notNull,
            default: element.default,
            baseType: element.baseType,
          };
          this.createAttribute(attributeDefinition, oNode, propagate);
        }
      }
    }
  }

  createAttributeMappings() {
    this.attributeMappings.forEach((value, key) => {
      const oTargetNode = key;
      if (value.length) {
        for (const item of value) {
          const oTargetAttribute = oTargetNode.attributes.selectObject({ name: item.target });
          let oSourceAttribute;
          if (oTargetNode.inputs.length) {
            const oSourceNode = oTargetNode.inputs.get(0).getSourceNode();
            if (oSourceNode) {
              oSourceAttribute = oSourceNode.attributes.selectObject({ name: item.source });
            }
          }
          if (oSourceAttribute && oTargetAttribute) {
            super.createAttributeMapping({ source: oSourceAttribute, target: oTargetAttribute }, oTargetNode);
          }
        }
      }
    });
  }

  /**
   *
   *
   * @param {*} oNode
   * @memberof TFDiagramBuilder
   */
  createNewTarget(oNode: sap.cdw.transformationflow.Node) {
    this.oEditor.resource.applyUndoableAction(() => {
      oNode.isTemplate = false;
      oNode.isNew = true;
      oNode.technicalName = Constants.NewTargetTechnicalName;
      oNode.businessName = Constants.NewTargetBusinessName;
      oNode.displayName = Constants.NewTargetBusinessName;
      // update icon
      oNode.icon = "sap-icon://sac/new-table";
      const sourceNode = oNode.getSourceNode();
      const viewTransformNode: sap.cdw.transformationflow.Node = oNode.getViewTransformNode();
      oNode.isTargetDelta = sourceNode.isDeltaTable
        ? true
        : this.oModel.isLargeSystemSpace
        ? true
        : viewTransformNode?.isDeltaTable;
      const sourceCDCColumns: sap.cdw.transformationflow.Attribute[] = [];
      if (sourceNode?.attributes?.length) {
        for (const sourceAttribute of sourceNode.getAttributesArray()) {
          if (sourceAttribute.isCDCColumn) {
            sourceCDCColumns.push(sourceAttribute);
          } else {
            const targetAttribute = this.createAttribute(sourceAttribute, oNode);
            if (!targetAttribute.label) {
              // use technical name as business name if it does not exist in the source column
              targetAttribute.label = targetAttribute.name;
            }
          }
        }
        if (oNode.isTargetDelta) {
          if (sourceCDCColumns.length) {
            this.addSourceCDCColumnsToTarget(sourceCDCColumns, oNode);
          } else {
            this.addDefaultCDCColumns(oNode);
          }
        }
        this.autoMapTargetColumns(oNode);
      }
      this.checkTemplateModel();
      this.controller.refreshProperty(oNode);
      if (!this.oModel.isLoadTypeModifiedByUser) {
        Utils.setLoadType(this.oModel);
      }
    }, "create new target");

    this.controller.reloadProperty(oNode);
    this.validateModel();
  }

  /**
   *
   *
   * @param {*} oAttributeDefinition
   * @param {sap.cdw.transformationflow.Node} oParent
   * @param {boolean} [propagate=false]
   * @param {boolean} [propagateCDCColumns=true]
   * @returns
   * @memberof TFDiagramBuilder
   */
  createAttribute(
    oAttributeDefinition: any,
    oParent: sap.cdw.transformationflow.Node,
    propagate = false,
    propagateCDCColumns = true
  ) {
    // oAttributeDefinition can be Attribute object or javascript object.
    // we should extract properties from oAttributeDefinition
    const properties = {
      name: oAttributeDefinition.name,
      label: oAttributeDefinition.label,
      key: oAttributeDefinition.key,
      datatype: oAttributeDefinition.datatype,
      length: oAttributeDefinition.length,
      precision: oAttributeDefinition.precision,
      scale: oAttributeDefinition.scale,
      isCDCColumn: oAttributeDefinition.isCDCColumn || false,
      notNull: oAttributeDefinition.notNull,
      default: oAttributeDefinition.default,
      baseType: oAttributeDefinition.baseType,
      // nativeDataType: oAttributeDefinition.nativeDataType,
    };
    if (oAttributeDefinition.baseType) {
      if (oAttributeDefinition.baseType.isSimpleType) {
        properties.baseType = oAttributeDefinition.baseType;
      } else if (typeof oAttributeDefinition.baseType === "string") {
        properties.baseType = sap.cdw.commonmodel.ModelImpl.getOrCreateSimpleType(
          oAttributeDefinition.baseType,
          this.oModel
        );
      }
    }
    const oAttribute = super.createObject(MODEL_PACKAGE, "Attribute", properties);
    // Attach new simple type

    oParent.attributes.push(oAttribute);
    if (propagate) {
      if (sap.galilei.model.isInstanceOf(oParent, `${MODEL_PACKAGE}.Node`)) {
        const oTargetNode: sap.cdw.transformationflow.Node = oParent.getTargetNode();
        if (oTargetNode.component === Constants.OPERATORS.PYTHON) {
          const col: sap.cdw.transformationflow.Attribute = oTargetNode.attributes.selectObject({
            name: oAttribute.name,
          });
          if (!col) {
            this.createAttribute(oAttribute, oTargetNode, true, propagateCDCColumns);
          }
        } else if (oTargetNode.component === Constants.OPERATORS.TARGET && oTargetNode.isNew) {
          if (propagateCDCColumns) {
            const oOutputAttribute = this.createAttribute(oAttribute, oTargetNode);
            super.createAttributeMapping({ target: oOutputAttribute, source: oAttribute }, oTargetNode);
          }
        }
      }
    }
    return oAttribute;
  }

  /**
   *
   *
   * @param {*} inPort
   * @returns
   * @memberof TFDiagramBuilder
   */
  handleIntraVtypePropagation(inPort: any) {
    return;
  }

  /**
   *
   *
   * @param {sap.cdw.transformationflow.Node} oTargetNode
   * @return {*}  {boolean}
   * @memberof TFDiagramBuilder
   */
  autoMapTargetColumns(oTargetNode: sap.cdw.transformationflow.Node): boolean {
    let autoMappedColumns = false;
    const oSourceNode = oTargetNode.getSourceNode();
    if (oSourceNode) {
      const aSourceColumns = oSourceNode.attributes.toArray();
      for (const oSourceColumn of aSourceColumns) {
        let oTargetColumn: sap.cdw.transformationflow.Attribute;
        if (oSourceColumn.isCDCColumn) {
          // find if there a target cdc column with same datatype
          oTargetColumn = oTargetNode.attributes.selectObject({ datatype: oSourceColumn.dataType, isCDCColumn: true });
        } else {
          // find if there a target column with same name
          oTargetColumn = oTargetNode.attributes.selectObject({ name: oSourceColumn.name, isCDCColumn: false });
        }
        if (oTargetColumn) {
          autoMappedColumns = true;
          const value = Utils.validateTargetColumnMapping(oSourceColumn, oTargetColumn);
          if (value !== Constants.Mapping.Invalid) {
            const mappingExist = super.getAttributeMapping({ target: oTargetColumn }, oTargetNode.attributeMappings);
            if (!mappingExist) {
              super.createAttributeMapping({ source: oSourceColumn, target: oTargetColumn }, oTargetNode);
            }
          }
        }
      }
    }
    return autoMappedColumns;
  }

  /**
   *
   *
   * @param {sap.cdw.transformationflow.Attribute} oColumn
   * @param {sap.cdw.transformationflow.Node} oNode
   * @memberof TFDiagramBuilder
   */
  public deleteTargetColumn(oColumn: sap.cdw.transformationflow.Attribute, oNode: sap.cdw.transformationflow.Node) {
    // remove mappings
    for (const mapping of oNode.attributeMappings.toArray()) {
      if (mapping.target === oColumn) {
        mapping.deleteObject();
      }
    }
    // remove column
    oColumn.deleteObject();
  }

  /**
   *
   *
   * @param {sap.cdw.transformationflow.Attribute} oAttribute
   * @param {boolean} [propagate]
   * @param {boolean} [propagateCDCColumns=true]
   * @memberof TFDiagramBuilder
   */
  deleteAttribute(oAttribute: sap.cdw.transformationflow.Attribute, propagate?: boolean, propagateCDCColumns = true) {
    if (oAttribute) {
      if (propagate) {
        const mappedColumnsAndMappings = this.findMappedColumns(
          oAttribute,
          true /* includeMappings */,
          Constants.Action.Column.Delete,
          undefined,
          propagateCDCColumns
        );
        for (const item of mappedColumnsAndMappings) {
          if (typeof item.deleteObject === "function") {
            item.deleteObject();
          }
        }
      }
      if (typeof oAttribute.deleteObject === "function") {
        oAttribute.deleteObject();
      }
    }
  }

  /**
   *
   *
   * @param {sap.cdw.transformationflow.Attribute} oAttribute
   * @param {{ datatype: string; length: any; precision: any; scale: any }} values
   * @param {boolean} [propagate]
   * @memberof TFDiagramBuilder
   */
  updateDatatype(
    oAttribute: sap.cdw.transformationflow.Attribute,
    values: { datatype: string; length: any; precision: any; scale: any },
    propagate?: boolean
  ) {
    if (propagate) {
      // propagate datatype
      const mappedColumns = this.findMappedColumns(oAttribute, false, Constants.Action.Column.DataTypeChange, values);
      for (const mappedColumn of mappedColumns) {
        if (oAttribute.datatype === mappedColumn.datatype) {
          // propagate only if data types are same
          this.updateDatatype(mappedColumn, values);
        }
      }
    }
    if (values.datatype) {
      oAttribute.datatype = values.datatype;
      let length: number;
      let precision: number;
      let scale: number;
      if (!isNaN(parseInt(values.length, 10))) {
        length = parseInt(values.length, 10);
      }
      if (!isNaN(parseInt(values.precision, 10))) {
        precision = parseInt(values.precision, 10);
      }
      if (!isNaN(parseInt(values.scale, 10))) {
        scale = parseInt(values.scale, 10);
      }
      oAttribute.length = length;
      oAttribute.precision = precision;
      oAttribute.scale = scale;
    }
  }

  /**
   *
   *
   * @param {sap.cdw.transformationflow.Attribute} oInputAttribute
   * @param {boolean} [includeMappings=false]
   * @param {string} [action]
   * @param {*} [values]
   * @param {boolean} [propagateCDCColumns]
   * @returns
   * @memberof TFDiagramBuilder
   */
  findMappedColumns(
    oAttribute: sap.cdw.transformationflow.Attribute,
    includeMappings = false,
    action?: string,
    values?,
    propagateCDCColumns = true
  ) {
    let aOutputAttributes: sap.cdw.transformationflow.Attribute[] = []; // use array since columns can be mapped to multiple columns
    const oSourceOutput = oAttribute.container.outputs.length ? oAttribute.container.outputs.get(0) : undefined;
    if (oSourceOutput) {
      for (const oLink of oSourceOutput.getLinkObjects()) {
        const oInput = oLink.target;
        if (oInput && oInput.container && oAttribute) {
          const oNode = oInput.container;
          if (oNode.component === Constants.OPERATORS.TARGET) {
            for (const attributeMapping of oNode.attributeMappings.toArray()) {
              if (attributeMapping.source === oAttribute) {
                if (includeMappings) {
                  aOutputAttributes.push(attributeMapping);
                }
                if (oNode.isNew) {
                  if (action === Constants.Action.Column.Rename && values.newName) {
                    // do not propagate if column with new name exist
                    const columnExist = oNode.attributes.selectObject({ name: values.newName });
                    if (columnExist) {
                      continue;
                    }
                  }
                  aOutputAttributes.push(attributeMapping.target);
                }
              }
            }
          } else if (oNode.component === Constants.OPERATORS.PYTHON) {
            for (const attribute of oNode.attributes.toArray()) {
              // assume the column is mapped when the name is same
              if (attribute.name === oAttribute.name) {
                aOutputAttributes.push(attribute);
                if (propagateCDCColumns) {
                  // call recursive
                  aOutputAttributes = aOutputAttributes.concat(
                    this.findMappedColumns(attribute, includeMappings, action, values)
                  );
                }
              }
            }
          }
        }
      }
    }
    return aOutputAttributes;
  }

  /**
   *
   *
   * @memberof TFDiagramBuilder
   */
  deleteSelection() {
    if (this.oEditor.selectedSymbols && this.oEditor.selectedSymbols.length) {
      // clear all validations as it creates reference issue while performing undo redo DIBUGS-3843
      this.clearValidations();
      const additionalSymbolsToDelete = this.getDependentSymbols(this.oEditor.selectedSymbols);
      if (additionalSymbolsToDelete.length) {
        additionalSymbolsToDelete.forEach((oSymbol) => {
          if (this.oEditor.selectedSymbols.indexOf(oSymbol) === -1) {
            this.oEditor.selectedSymbols.push(oSymbol);
          }
        });
      }
      const aLinkSymbols = this.oEditor.selectedSymbols.filter((oSymbol) =>
        sap.galilei.model.isInstanceOf(oSymbol, this.oEditor.extension.FLOW_SYMBOL)
      );
      const aNodeSymbols = this.oEditor.selectedSymbols.filter((oSymbol) =>
        sap.galilei.model.isInstanceOf(oSymbol, this.oEditor.extension.NODE_SYMBOL)
      );
      this.oEditor.resource.applyUndoableAction(() => {
        this.deleteLinkSymbolsDependencies(aLinkSymbols); // deletes link symbol related dependencies
        this.deleteNodeSymbolsDependencies(aNodeSymbols); // removes the dependant objects of node
        const oNode = aNodeSymbols.length ? aNodeSymbols[0].object : undefined;
        if (oNode) {
          if (
            oNode.component === Constants.OPERATORS.VIEW_TRANSFORM ||
            oNode.component === Constants.OPERATORS.SQL_TRANSFORM ||
            oNode.component === Constants.OPERATORS.SQL_SCRIPT_TRANSFORM
          ) {
            if (this.oModel.isLargeSystemSpace && Utils.isTableAsSourceSupportedSpark()) {
              oNode.displayName = "Source";
              oNode.businessName = "Source";
            }
            if (this.oModel.showSecondarySources) {
              this.deleteSecondarySources();
              oNode.secondaryNodes = undefined;
              oNode.sourceDefinitions = {};
            }
          }
          if (oNode.component === Constants.OPERATORS.PYTHON) {
            const sourceNode = oNode.getSourceNode();
            const targetNode = oNode.getTargetNode();
            this.oEditor.deleteSelectedSymbols(true); // deletes the selected symbol(node symbol or link symbol or both if node symbol is connected)
            const sourceNodeSymbol = sourceNode.relatedSymbols.get(0);
            const targetNodeSymbol = targetNode.relatedSymbols.get(0);
            this.updateNodeSymbolCoordinates(sourceNodeSymbol, 0, 12);
            this.updateNodeSymbolCoordinates(targetNodeSymbol, 200, 12);
            this.connectNodes(sourceNode, targetNode);
            if (targetNode.component === Constants.OPERATORS.TARGET && !targetNode.isTemplate) {
              for (const attributeMapping of targetNode.attributeMappings.toArray()) {
                attributeMapping.deleteObject();
              }
              this.autoMapTargetColumns(targetNode);
            }
          }
          if (
            this.oModel.isLargeSystemSpace &&
            Utils.isTableAsSourceSupportedSpark() &&
            oNode.component === Constants.OPERATORS.SOURCE_TABLE
          ) {
            oNode.component = Constants.OPERATORS.VIEW_TRANSFORM;
            oNode.displayName = "Source";
            oNode.businessName = "Source";
            oNode.icon = "sap-icon://switch-views";
            oNode.name = "viewtransform1";
            oNode.technicalName = undefined;
          }
          if (oNode.component === Constants.OPERATORS.TARGET) {
            // reset the properties
            oNode.isNew = false;
            oNode.icon = "sap-icon://sac/table";
            oNode.displayName = Constants.TargetTemplateBusinessName;
            oNode.businessName = Constants.TargetTemplateBusinessName;
            oNode.truncate = false;
          }
          oNode.isTargetDelta = false;
          if (
            oNode.component === Constants.OPERATORS.SQL_TRANSFORM ||
            oNode.component === Constants.OPERATORS.SQL_SCRIPT_TRANSFORM
          ) {
            oNode.component = Constants.OPERATORS.VIEW_TRANSFORM;
            oNode.name = "viewtransform1";
          }
          oNode.deploymentStatus = undefined;
          if (oNode.component !== Constants.OPERATORS.PYTHON) {
            oNode.isTemplate = true;
          }
          oNode.clearCSN();
          // update icon
          this.updateSourceIcon(oNode);
        }
        // update load type
        if (!this.oModel.isLoadTypeModifiedByUser) {
          Utils.setLoadType(this.oModel);
        }
        this.checkTemplateModel();
        this.oEditor.unselectAllSymbols();
      }, "delete symbols");
      this.validateModel();
    }
  }

  /**
   *
   * @param {*} aSymbols
   * @returns
   * @memberof TFDiagramBuilder
   */
  getDependentSymbols(aSymbols) {
    let dependentSymbols = [];
    for (const oSymbol of aSymbols) {
      if (sap.galilei.model.isInstanceOf(oSymbol, this.oEditor.extension.NODE_SYMBOL)) {
        // add all connection(flow) symbols which are connected to the node ports
        oSymbol.symbols.forEach((oSubSymbol) => {
          if (sap.galilei.model.isInstanceOf(oSubSymbol, this.oEditor.extension.PORT_SYMBOL)) {
            const symbols = oSubSymbol.getLinkSymbols();
            if (Array.isArray(symbols)) {
              dependentSymbols = [...dependentSymbols, ...symbols];
            }
          }
        });
      }
    }
    return dependentSymbols;
  }

  /**
   *
   * @param {*} aNodeSymbols
   * @memberof TFDiagramBuilder
   */
  deleteNodeSymbolsDependencies(aNodeSymbols) {
    if (Array.isArray(aNodeSymbols)) {
      for (const oNodeSymbol of aNodeSymbols) {
        // if (oNodeSymbol && oNodeSymbol.object && oNodeSymbol.object.component === Constants.OPERATORS.TARGET) {
        if (oNodeSymbol && oNodeSymbol.object) {
          // delete attributes
          for (const attribute of oNodeSymbol.object.attributes.toArray()) {
            attribute.deleteObject();
          }
        }
      }
    }
  }

  /**
   *
   * @param {*} aLinkSymbols
   * @memberof TFDiagramBuilder
   */
  deleteLinkSymbolsDependencies(aLinkSymbols) {
    if (Array.isArray(aLinkSymbols)) {
      // check link symbol deletion
      for (const linkSymbol of aLinkSymbols) {
        if (linkSymbol.object && linkSymbol.object.sourceNode) {
          const sourceNode = linkSymbol.object.sourceNode;
          for (const oAttribute of sourceNode.attributes.toArray()) {
            // remove mapped column
            const mappedColumns = this.findMappedColumns(oAttribute, true, Constants.Action.Column.Delete);
            for (const item of mappedColumns) {
              if (typeof item.deleteObject === "function" && item.container !== sourceNode) {
                //  do not delete from source node
                item.deleteObject();
              }
            }
          }
        }
      }
    }
  }

  /**
   *
   *
   * @param {*} oNode
   * @param {*} oNodeSymbol
   * @memberof TFDiagramBuilder
   */
  updateNodeSymbol(oNode, oNodeSymbol) {
    if (oNode) {
      // Creates input symbols
      for (let index = 0; index < oNode.inputs.length; index++) {
        const oInput = oNode.inputs.get(index);
        const oInputSymbol = super.createObject(MODEL_PACKAGE + ".ui", "InputSymbol", {
          object: oInput,
        });
        oNodeSymbol.symbols.push(oInputSymbol);
        super.drawSymbolAndRegisterEvents(oInputSymbol);
      }

      // Creates output symbols
      for (let index = 0; index < oNode.outputs.length; index++) {
        const oOutput = oNode.outputs.get(index);
        const oOutputSymbol = super.createObject(MODEL_PACKAGE + ".ui", "OutputSymbol", {
          object: oOutput,
        });
        oNodeSymbol.symbols.push(oOutputSymbol);
        super.drawSymbolAndRegisterEvents(oOutputSymbol);
      }
      oNodeSymbol.updateBoundarySymbols();
      super.drawSymbol(oNodeSymbol);
      this.oEditor.extension.updatePorts(oNodeSymbol);
    }
  }

  /**
   *
   *
   * @memberof TFDiagramBuilder
   */
  public async validateModel() {
    // validate the model
    if (this.oModel) {
      // debounce the validation
      if (this.modelValidationTimer) {
        clearTimeout(this.modelValidationTimer);
      }
      this.modelValidationTimer = setTimeout(() => {
        this.oModel.validate();
        this.checkTemplateModel();
      }, 500);
    }
  }

  /**
   *
   *
   * @memberof TFDiagramBuilder
   */
  public async clearValidations() {
    if (this.oModel) {
      this.oModel.clearValidation();
    }
  }

  /**
   *
   *
   * @param {*} oNode
   * @memberof TFDiagramBuilder
   */
  public async validateNode(oNode) {
    if (oNode && this.oEditor) {
      // debounce the validation
      if (this.nodeValidationTimer) {
        clearTimeout(this.nodeValidationTimer);
      }
      this.nodeValidationTimer = setTimeout(() => {
        this.oEditor.resource.applyUndoableAction(
          () => {
            oNode.validate();
          },
          "validate node",
          true
        );
      }, 500);
    }
  }

  /**
   *
   *
   * @param {*} label
   * @param {*} name
   * @param {*} vflowOpName
   * @returns
   * @memberof TFDiagramBuilder
   */
  getOperatorLabel(label, name, vflowOpName) {
    if (this.library.operatorLibrary[vflowOpName].description) {
      return `${this.library.operatorLibrary[vflowOpName].description} ${name.substr(label.length)}`;
    } else {
      return `${label.charAt(0).toUpperCase()}${label.substr(1).toLowerCase()} ${name.substr(label.length)}`;
    }
  }

  getUniqueColumn(column, oNode) {
    const name = this.controller.getDiagramBuilder().getUniqueName(column.name, 0, oNode.attributes);
    // create
    return {
      name: name,
      datatype: column.datatype,
      length: column.length,
      precision: column.precision,
      scale: column.scale,
      key: column.key,
      isCDCColumn: column.isCDCColumn,
      label: column.label,
      notNull: column.notNull,
      default: column.default,
    };
  }

  getResource() {
    return this.oResource;
  }

  /**
   *
   *
   * @param {sap.cdw.transformationflow.Attribute} oAttribute
   * @param {{ key: boolean }} values
   * @param {boolean} [propagate]
   * @memberof TFDiagramBuilder
   */
  updateKeyColumns(oAttribute: sap.cdw.transformationflow.Attribute, values: { key: boolean }, propagate?: boolean) {
    if (propagate) {
      // propagate primary key
      const mappedColumns = this.findMappedColumns(oAttribute, false, undefined, values);
      for (const mappedColumn of mappedColumns) {
        if (mappedColumn.key !== values.key) {
          mappedColumn.key = values.key;
        }
      }
    }
    oAttribute.key = values.key;
  }

  /**
   * Updates the business name of an attribute and its mapped columns.
   *
   * @param oAttribute - The attribute to update the business name for.
   * @param businessName - The new business name to set.
   */
  updateBusinessName(oAttribute: sap.cdw.transformationflow.Attribute, businessName: string) {
    const mappedColumns = this.findMappedColumns(oAttribute, false);
    for (const mappedColumn of mappedColumns) {
      if (mappedColumn.label !== businessName) {
        mappedColumn.label = businessName;
      }
    }
    if (oAttribute) {
      oAttribute.label = businessName;
      oAttribute.displayName = businessName;
    }
  }

  /**
   *
   *
   * @param {sap.cdw.transformationflow.Attribute} oAttribute
   * @param {{ isCDCColumn: boolean }} values
   * @param {boolean} [propagate]
   * @memberof TFDiagramBuilder
   */
  updateIsCDCColumnFlag(
    oAttribute: sap.cdw.transformationflow.Attribute,
    values: { isCDCColumn: boolean },
    propagate?: boolean
  ) {
    if (propagate) {
      // propagate primary key
      const mappedColumns = this.findMappedColumns(oAttribute, false, undefined, values);
      for (const mappedColumn of mappedColumns) {
        if (mappedColumn.isCDCColumn !== values.isCDCColumn) {
          mappedColumn.isCDCColumn = values.isCDCColumn;
        }
      }
    }
    oAttribute.isCDCColumn = values.isCDCColumn;
  }

  /**
   * Adds default CDC columns to the column list
   * @param {sap.cdw.transformationflow.Node} nodeObject
   * @memberof TFDiagramBuilder
   */
  addDefaultCDCColumns(nodeObject: sap.cdw.transformationflow.Node) {
    this.oEditor.resource.applyUndoableAction(() => {
      this.createAttribute(
        {
          name: "Change_Type",
          label: "Change Type",
          isCDCColumn: true,
          datatype: "cds.String",
          length: 1,
          default: {
            val: "I",
          },
          notNull: true,
          key: false,
        },
        nodeObject
      );
      this.createAttribute(
        {
          name: "Change_Date",
          label: "Change Date",
          isCDCColumn: true,
          datatype: "cds.Timestamp",
          default: {
            func: "CURRENT_UTCTIMESTAMP",
          },
          notNull: true,
          key: false,
        },
        nodeObject
      );
    }, "add default CDC columns");
  }

  /**
   * Deletes CDC columns from the column list
   * @param {sap.cdw.transformationflow.Node} nodeObject
   * @memberof TFDiagramBuilder
   */
  deleteCDCColumns(nodeObject: sap.cdw.transformationflow.Node) {
    const cdcColumns: sap.cdw.transformationflow.Attribute[] = nodeObject.attributes.filter(
      (column: sap.cdw.transformationflow.Attribute) => column.isCDCColumn === true
    );
    this.oEditor.resource.applyUndoableAction(() => {
      for (const oColumn of cdcColumns) {
        this.deleteAttribute(oColumn);
        const mapping = super.getAttributeMapping({ target: oColumn }, nodeObject.attributeMappings);
        if (mapping) {
          mapping.deleteObject();
        }
      }
    }, "delete CDC columns and mapping");
  }

  /**
   * Adds source CDC columns to the column list
   * @param {sap.cdw.transformationflow.Attribute[]} sourceCDCColumns
   * @param {sap.cdw.transformationflow.Node} nodeObject
   * @memberof TFDiagramBuilder
   */
  addSourceCDCColumnsToTarget(
    sourceCDCColumns: sap.cdw.transformationflow.Attribute[],
    nodeObject: sap.cdw.transformationflow.Node
  ) {
    this.oEditor.resource.applyUndoableAction(() => {
      if (sourceCDCColumns && sourceCDCColumns.length) {
        for (const sourceAttribute of sourceCDCColumns) {
          const targetAttribute: sap.cdw.transformationflow.Attribute = this.createAttribute(
            sourceAttribute,
            nodeObject
          );
          if (!targetAttribute.label) {
            // use technical name as business name if it does not exist in the source column
            targetAttribute.label = targetAttribute.name;
          }
          if (!targetAttribute.default) {
            if (targetAttribute.datatype === "cds.Timestamp") {
              targetAttribute.default = {
                func: "CURRENT_UTCTIMESTAMP",
              };
              targetAttribute.notNull = true;
            } else if (targetAttribute.datatype === "cds.String") {
              targetAttribute.default = {
                val: "I",
              };
              targetAttribute.notNull = true;
            }
          }
        }
      } else {
        this.addDefaultCDCColumns(nodeObject);
      }
      const targetCDCColumns: sap.cdw.transformationflow.Attribute[] = nodeObject.attributes.filter(
        (column: sap.cdw.transformationflow.Attribute) => column.isCDCColumn === true
      );
      if (targetCDCColumns.length < 2) {
        for (const targetAttribute of targetCDCColumns) {
          if (targetAttribute.datatype === "cds.Timestamp") {
            this.createAttribute(
              {
                name: "Change_Type",
                label: "Change Type",
                isCDCColumn: true,
                datatype: "cds.String",
                length: 1,
                default: {
                  val: "I",
                },
                notNull: true,
                key: false,
              },
              nodeObject
            );
          } else if (targetAttribute.datatype === "cds.String") {
            this.createAttribute(
              {
                name: "Change_Date",
                label: "Change Date",
                isCDCColumn: true,
                datatype: "cds.Timestamp",
                default: {
                  func: "CURRENT_UTCTIMESTAMP",
                },
                notNull: true,
                key: false,
              },
              nodeObject
            );
          }
        }
      }
      this.autoMapTargetColumns(nodeObject);
    }, "add source CDC columns to target");
  }

  /**
   * Adds python operator
   * @param {sap.cdw.transformationflow.Node} sourceNode
   * @memberof TFDiagramBuilder
   */
  addPythonOperator(sourceNode: sap.cdw.transformationflow.Node) {
    // find target node
    const targetNode = sourceNode.getTargetNode();
    const sourceNodeSymbol = sourceNode.relatedSymbols.get(0);
    const targetNodeSymbol = targetNode.relatedSymbols.get(0);

    // remove connection between the source and target
    const sourceNodeOutput = sourceNode.outputs.get(0);
    const oldLink = sourceNodeOutput.getLinkObjects()[0];
    const oldLinkSymbol = oldLink.relatedSymbols.get(0);
    this.oEditor.deleteSymbol(oldLinkSymbol);
    oldLink.deleteObject();
    // update source node coordinates
    this.updateNodeSymbolCoordinates(sourceNodeSymbol, 12, 12);
    // update target node coordinates
    this.updateNodeSymbolCoordinates(targetNodeSymbol, 312, 12);
    let oNodeSymbol;
    if (sourceNode.relatedSymbols.length) {
      oNodeSymbol = sourceNode.relatedSymbols.get(0);
    }
    const pythonNode = this.createPythonNode();
    if (sourceNode.cdcColumns) {
      pythonNode.cdcColumns = sourceNode.cdcColumns;
    }
    // copy column from source node to python
    for (let sourceNodeColumn of sourceNode.getAttributesArray()) {
      this.createAttribute(sourceNodeColumn, pythonNode, false /* propagate*/);
    }
    // update columns/mappings in the target node
    if (targetNode.component === Constants.OPERATORS.TARGET && !targetNode.isTemplate) {
      // update target node attribute mappings with source column
      for (let attributeMapping of targetNode.attributeMappings.toArray()) {
        const newSourceColumn = pythonNode.attributes.selectObject({ name: attributeMapping.source.name });
        attributeMapping.source = newSourceColumn;
      }
    }

    // connect python to target
    this.connectNodes(pythonNode, targetNode);

    // connect source node to python
    this.connectNodes(sourceNode, pythonNode);
    this.oEditor.drawAllSymbols();
    this.autoLayout();
  }

  /**
   * updates node symbol coordinates
   * @param {any} symbol
   * @param {Number} x
   * @param {Number} y
   * @memberof TFDiagramBuilder
   */
  updateNodeSymbolCoordinates(symbol: any, x: Number, y: Number) {
    symbol.x = x;
    symbol.y = y;
    symbol.updateBoundarySymbols();
    this.oEditor.drawSymbol(symbol);
  }

  /**
   * layouts the diagram
   * @override
   *
   */
  autoLayout() {
    this.oEditor.unselectAllSymbols();

    const layoutProperties = {
      isSupportMultiEdges: true,
      isSupportPorts: true,
      isSupportSubGraph: true,
      isDirected: true,
      subGraphPadding: "0,16,0,0",
      // Specify the layouter name and its options (klay.js options)
      layout: {
        name: "klayjs",
        direction: "RIGHT", // Layout direction
        edgeRouting: "ORTHOGONAL",
        nodePlace: "SIMPLE", // Align nodes
        spacing: 40, // Distance between nodes
        edgeSpacingFactor: 0.4,
      },
      animationDuration: 200,
    };
    const onSuccess = function (oAutoLayout) {
      // If success, show the global view.
      oAutoLayout.editor.drawAllSymbols();
      oAutoLayout.editor.showGlobalView();
    };
    const onError = function (error) {
      throw error;
    };
    // Creates a diagram auto-layout helper.
    const oAutoLayout = new sap.galilei.ui.editor.layout.DiagramAutoLayout();
    // Layouts the diagram (all top-level symbols) using the 'Layered' algorithm that is based on klay.js. The klay.js needs to be included manually.
    // The layouter is sap.galilei.ui.common.layout.KlayLayouter.
    // For the options, see http://layout.rtsys.informatik.uni-kiel.de:9444/Providedlayout.html?algorithm=de.cau.cs.kieler.klay.layered
    // The other layouters are 'Directed' and 'Organic'. They are based on dagre.js. The dagre.js needs to be included manually.
    // You could also develop your own layouter.
    setTimeout(
      () => oAutoLayout.layoutDiagram(this.oEditor.diagram, layoutProperties, this.oEditor, onSuccess, onError),
      10
    );
  }

  /**
   * creates python node
   * @memberof TFDiagramBuilder
   * @returns {sap.cdw.transformationflow.Node}
   */
  createPythonNode() {
    const operatorMetadata = JSON.parse(
      JSON.stringify(this.library.operatorLibrary[Constants.OPERATORS.PYTHON].metadata)
    );
    const values = Constants.OPERATORS.PYTHON.split(".");
    const label = values[values.length - 1];
    const name = this.oEditor.extension.getUniqueName(label);
    const options = {
      operator: Constants.OPERATORS.PYTHON,
      name: name,
      oNodeProperties: {
        component: Constants.OPERATORS.PYTHON,
        metadata: operatorMetadata,
      },
    };
    const pythonNode = this.createNode(options);
    super.createNodeSymbol(pythonNode);
    return pythonNode;
  }

  /**
   * creates link between source node and target node
   * @param {sap.cdw.transformationflow.Node} sourceNode
   * @param {sap.cdw.transformationflow.Node} targetNode
   * @memberof TFDiagramBuilder
   */
  connectNodes(sourceNode: sap.cdw.transformationflow.Node, targetNode: sap.cdw.transformationflow.Node) {
    const sourceNodeOutput = sourceNode.outputs.get(0);
    const targetNodeInput = targetNode.inputs.get(0);
    const flow = super.createObject(MODEL_PACKAGE, "Flow", {
      metadata: {},
      source: sourceNodeOutput,
      target: targetNodeInput,
    });
    this.oModel.flows.push(flow);
    super.createFlowSymbol(flow);
  }

  public deleteOutputColumn(oColumn: sap.cdw.transformationflow.Attribute) {
    this.deleteAttribute(oColumn, true /* propagate */);
  }

  /**
   *
   *
   * @param {sap.cdw.transformationflow.Attribute} oAttribute
   * @param {*} newName
   * @param {*} [newLabel]
   * @param {boolean} [propagate]
   * @memberof TFDiagramBuilder
   */
  renameColumn(
    oAttribute: sap.cdw.transformationflow.Attribute,
    newName: string,
    newLabel: string,
    propagate?: boolean
  ) {
    const oldName = oAttribute.name;
    if (newName !== oldName) {
      // no need to update if the name is same
      if (propagate) {
        // propgate new column name
        const result = this.findMappedColumns(oAttribute, true, Constants.Action.Column.Rename, {
          newName,
          oldName,
        });
        for (const item of result) {
          if (sap.galilei.model.isInstanceOf(item, `${MODEL_PACKAGE}.Attribute`)) {
            if (item.name === oldName) {
              item.name = newName;
            }
          }
        }
      }
      // column name should be updated only after finding the mapped columns
      oAttribute.name = newName;
    }
    oAttribute.label = newLabel;
  }

  /**
   * Handler for show secondary sources
   * @memberof TFDiagramBuilder
   */
  showSecondarySources() {
    const viewTransform = this.oModel.nodes.toArray().find((node) => node.isViewTransform === true);
    if (
      (viewTransform.component === Constants.OPERATORS.SQL_TRANSFORM ||
        viewTransform.component === Constants.OPERATORS.SQL_SCRIPT_TRANSFORM) &&
      viewTransform.internalValidationStatus === "error"
    ) {
      this.oModel.showSecondarySources = false;
      this.controller.getView().getModel("galileiModel").refresh(true);
      MessageHandler.uiError(
        this.controller.localizeText("secondarySourceErrorDialog"),
        null,
        null,
        null,
        null,
        null,
        null,
        "secondarySourceErrorDialog"
      );
      return;
    }
    this.oModel.showSecondarySources = true;
    this.controller.setBusy(true);
    try {
      let secondaryNodes = [];
      if (viewTransform.component === Constants.OPERATORS.VIEW_TRANSFORM && viewTransform.secondaryNodes) {
        for (const node of viewTransform.secondaryNodes.toArray()) {
          if (node.classDefinition?.name === "Entity") {
            secondaryNodes.push(node);
          }
        }
      } else if (
        viewTransform.component === Constants.OPERATORS.SQL_TRANSFORM ||
        viewTransform.component === Constants.OPERATORS.SQL_SCRIPT_TRANSFORM
      ) {
        for (const node in viewTransform.sourceDefinitions) {
          const sqlNode = this.createSqlNodeProperties(viewTransform.sourceDefinitions[node]);
          secondaryNodes.push(sqlNode);
        }
      }
      if (secondaryNodes.length) {
        this.oEditor.resource.applyUndoableAction(
          () => {
            for (const oNode of secondaryNodes) {
              this.createSecondaryNode(oNode);
            }
          },
          "create secondary sources",
          true
        );
      } else {
        this.oModel.showSecondarySources = false;
        MessageHandler.uiError(
          this.controller.localizeText("noSecondarySourceErrorDialog"),
          null,
          null,
          null,
          null,
          null,
          null,
          "noSecondarySourceErrorDialog"
        );
      }
    } catch (error) {
      this.oModel.showSecondarySources = false;
      MessageHandler.uiError(
        this.controller.localizeText("secondarySourceLoadingErrorDialog"),
        null,
        null,
        null,
        null,
        null,
        null,
        "secondarySourceLoadingErrorDialog"
      );
    } finally {
      this.controller.getView().getModel("galileiModel").refresh(true);
      this.controller.setBusy(false);
    }
  }

  /**
   * create sql node properties
   * @param {IModel} oNodeProperties
   * @returns
   * @memberof TFDiagramBuilder
   */
  createSqlNodeProperties(oNodeProperties: IModel) {
    if (oNodeProperties.name?.includes(".")) {
      let bFirstQualifier = false;
      let contextName = sap.cdw.commonmodel.ObjectImpl.getQualifierName(oNodeProperties.name, bFirstQualifier);
      let context = sap.cdw.commonmodel.ModelImpl.getOrCreateContext(contextName, this.oModel);
      if (!context) {
        // Try the first qualifier
        bFirstQualifier = true;
        contextName = sap.cdw.commonmodel.ObjectImpl.getQualifierName(oNodeProperties.name, bFirstQualifier);
        context = sap.cdw.commonmodel.ModelImpl.getOrCreateContext(contextName, this.oModel);
      }
      if (context) {
        oNodeProperties["context"] = context;
      }
    }
    const isRemote = oNodeProperties.csn?.definitions[oNodeProperties["name"]]["@DataWarehouse.remote.connection"]
      ? true
      : false;
    const isLocalSchema = oNodeProperties.csn?.definitions[oNodeProperties["name"]]["@DataWarehouse.external.schema"]
      ? true
      : false;
    const isLTF = oNodeProperties.csn?.definitions[oNodeProperties["name"]][
      "@DataWarehouse.persistence.hdlf.tableFormat"
    ]
      ? true
      : false;
    const isDeltaTable = oNodeProperties.csn?.definitions[oNodeProperties["name"]]["@DataWarehouse.delta"]
      ? true
      : false;
    const isView = oNodeProperties["#isViewEntity"] === "true" ? true : false;
    const label = oNodeProperties["@EndUserText.label"]
      ? oNodeProperties["@EndUserText.label"]
      : oNodeProperties.csn?.definitions[oNodeProperties["name"]]["@EndUserText.label"];
    let deltaTableName = `${oNodeProperties.name}${Constants.DELTA_TABLE_SUFFIX}`;
    let listOfPackages;
    if (oNodeProperties["#repositoryPackage"] && oNodeProperties["#repositoryPackage"] !== "_NONE_KEY_PACKAGE_") {
      listOfPackages = [
        { technicalName: oNodeProperties["#repositoryPackage"], businessName: oNodeProperties["#repositoryPackage"] },
      ];
    } else {
      listOfPackages = [{ technicalName: "_NONE_KEY_PACKAGE_", businessName: "None" }];
    }
    let useAs;
    if (isDeltaTable) {
      useAs = oNodeProperties["name"].includes(Constants.DELTA_TABLE_SUFFIX)
        ? Constants.LoadFromLocalTableType.DELTA
        : Constants.LoadFromLocalTableType.ACTIVE_RECORDS;
      deltaTableName = oNodeProperties.name;
    }
    return {
      elements: oNodeProperties.csn?.definitions[oNodeProperties["name"]].elements,
      localSchema: {
        schema: oNodeProperties.csn?.definitions[oNodeProperties["name"]]["@DataWarehouse.external.schema"],
      },
      displayName: label,
      label: label,
      technicalName: oNodeProperties.name,
      name: oNodeProperties.qualified_name,
      deltaTableName: deltaTableName,
      deploymentStatus: objectStatusTextFormatter(oNodeProperties["#objectStatus"], false),
      isDeltaTable: isDeltaTable,
      useAs: useAs,
      releaseState: getReleaseStateFromValue(oNodeProperties.releaseStateValue),
      crossSpaceName:
        oNodeProperties["spaceName"] ||
        oNodeProperties.csn?.definitions[oNodeProperties["name"]]["@DataWarehouse.space.name"],
      remote: {
        connection: oNodeProperties.csn?.definitions[oNodeProperties["name"]]["@DataWarehouse.remote.connection"],
        table: oNodeProperties.csn?.definitions[oNodeProperties["name"]]["@DataWarehouse.remote.entity"],
      },
      packageValue: oNodeProperties["#repositoryPackage"]
        ? oNodeProperties["#repositoryPackage"]
        : "_NONE_KEY_PACKAGE_",
      repositoryCSN: oNodeProperties.csn?.definitions[oNodeProperties["name"]],
      isView: isView,
      "#objectStatus": oNodeProperties["#objectStatus"],
      isQualified: oNodeProperties.name !== sap.cdw.commonmodel.ObjectImpl.getShortName(oNodeProperties.name),
      isCrossSpace:
        oNodeProperties["isCrossSpace"] || oNodeProperties.csn?.definitions[oNodeProperties["name"]].isCrossSpace,
      isRemote: isRemote,
      isLocal:
        (!isRemote && !isLocalSchema && !oNodeProperties["isCrossSpace"] && !isView) || (isDeltaTable && !isRemote),
      contextDisplayName: oNodeProperties["context"]?.displayName,
      context: oNodeProperties["context"],
      isLTF: isLTF,
      isSecondaryNodeSql: true,
      listOfPackages: listOfPackages,
    };
  }

  /**
   * create secondary node
   * @param {any} oNodeProperties
   * @memberof TFDiagramBuilder
   */
  createSecondaryNode(oNodeProperties: any) {
    const values = Constants.SECONDARY_SOURCE.split(".");
    const label = values[values.length - 1];
    const name = this.oEditor.extension.getUniqueName(label);
    let listOfPackages;
    if (oNodeProperties.packageValue && oNodeProperties.packageValue !== "_NONE_KEY_PACKAGE_") {
      listOfPackages = [{ technicalName: oNodeProperties.packageValue, businessName: oNodeProperties.packageValue }];
    } else {
      listOfPackages = [{ technicalName: "_NONE_KEY_PACKAGE_", businessName: "None" }];
    }
    let oNode = super.createObject(MODEL_PACKAGE, "SecondaryNode", {
      name: name, // unique id of the process
      component: "com.sap.dwc.secondarysource",
      displayName: oNodeProperties.displayName || oNodeProperties["@EndUserText.label"],
      metadata: {
        outports: [
          {
            name: "outTable",
            type: "table",
          },
        ],
      },
      iconsvg:
        "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNgYAAAAAMAASsJTYQAAAAASUVORK5CYII=",
      icon: "sap-icon://sac/table",
      description: "SecondarySource",
      operatorError: false,
      showErrorIcon: false,
      statusIcon: "",
      status: null,
      group: null,
      enablePortExtension: false,
      niceport: false,
      conversionOperator: false,
      isDeltaTable: oNodeProperties.isDeltaTable,
      businessName: oNodeProperties.label,
      technicalName: oNodeProperties.technicalName,
      qualifiedName: oNodeProperties.name,
      deltaTableName: oNodeProperties.deltaTableName,
      deploymentStatus: oNodeProperties.deploymentStatus,
      useAs: oNodeProperties.useAs,
      localSchemaName: oNodeProperties.localSchema?.schema,
      releaseState: oNodeProperties.releaseState,
      crossSpaceName: oNodeProperties.crossSpaceName,
      connection: oNodeProperties.remote?.connection,
      remoteTable: oNodeProperties.remote?.table,
      packageValue: oNodeProperties.packageValue,
      definition: oNodeProperties.repositoryCSN ? oNodeProperties.repositoryCSN : oNodeProperties.csn,
      isSecondarySource: true,
      isView: oNodeProperties.isView,
      "#objectStatus": oNodeProperties["#objectStatus"],
      isQualified: oNodeProperties.isQualified,
      isCrossSpace: oNodeProperties.isCrossSpace,
      isRemote: oNodeProperties.isRemote,
      isLocal: oNodeProperties.isLocal,
      isLTF: oNodeProperties.isLTF,
      contextDisplayName: oNodeProperties.contextDisplayName,
      context: oNodeProperties.context,
      parameters: oNodeProperties.parameters,
      listOfPackages: listOfPackages,
      isSecondaryNodeSql: oNodeProperties.isSecondaryNodeSql ? oNodeProperties.isSecondaryNodeSql : false,
    });
    // create attributes
    this.createAttributesFromCSNElements(oNode, false);
    // create outport
    let oOutput = super.getOutput(oNode, "outTable");
    if (!oOutput) {
      oOutput = super.createObject(MODEL_PACKAGE, "Output", {
        name: "outTable",
        type: "normal",
        typegroup: 0,
      });
    }
    const viewTransform = this.oModel.nodes.toArray().find((node) => node.isViewTransform === true);
    let oInput = super.getInput(viewTransform, "inTable");
    oNode.outputs.push(oOutput);
    this.oModel.secondarynodes.push(oNode);
    this.createSecondaryNodeSymbol(oNode);
    let oFlow = super.getFlow(oOutput, oInput);
    if (!oFlow) {
      oFlow = super.createObject(MODEL_PACKAGE, "SecondaryFlow", {
        source: oOutput,
        target: oInput,
      });
      this.oModel.secondaryflows.push(oFlow);
    }
    let oFlowSymbol = super.createObject(DIAGRAM_PACKAGE, "SecondaryFlowSymbol", {
      object: oFlow,
      sourceSymbol: oFlow.source.relatedSymbols.get(0),
      targetSymbol: oFlow.target.relatedSymbols.get(0),
    });
    this.oEditor.diagram.symbols.push(oFlowSymbol);
    super.drawSymbolAndRegisterEvents(oFlowSymbol);
    this.autoLayout();
    return oNode;
  }

  /**
   * create secondary node symbol
   * @param {sap.cdw.transformationflow.SecondaryNode} oSecondaryNode
   * @memberof TFDiagramBuilder
   * @returns
   */
  createSecondaryNodeSymbol(oSecondaryNode: sap.cdw.transformationflow.SecondaryNode) {
    // Creates secondary node symbol
    let oSecondaryNodeSymbol = super.createObject(DIAGRAM_PACKAGE, "SecondaryNodeSymbol", {
      object: oSecondaryNode,
      x: oSecondaryNode.x,
      y: oSecondaryNode.y,
    });
    this.oEditor.diagram.symbols.push(oSecondaryNodeSymbol);
    super.drawSymbolAndRegisterEvents(oSecondaryNodeSymbol);
    for (let index = 0; index < oSecondaryNode.outputs.length; index++) {
      let oOutput = oSecondaryNode.outputs.get(index);
      let oOutputSymbol = super.createObject(DIAGRAM_PACKAGE, "OutputSymbol", {
        object: oOutput,
      });
      oSecondaryNodeSymbol.symbols.push(oOutputSymbol);
      super.drawSymbolAndRegisterEvents(oOutputSymbol);
    }
    oSecondaryNodeSymbol.updateBoundarySymbols();
    return oSecondaryNodeSymbol;
  }

  /**
   * delete secondary source
   * @memberof TFDiagramBuilder
   */
  deleteSecondarySources() {
    const nodeSymbols = this.oEditor.diagram.symbols.filter((oSymbol) =>
      sap.galilei.model.isInstanceOf(oSymbol, this.oEditor.extension.SECONDARY_NODE_SYMBOL)
    );
    this.oEditor.resource.applyUndoableAction(
      () => {
        this.deleteNodeSymbolsDependencies(nodeSymbols); // removes the dependant objects of node
        for (const secondaryFlow of this.oModel.secondaryflows.toArray()) {
          secondaryFlow.deleteObject();
        }
        for (const secondaryNode of this.oModel.secondarynodes.toArray()) {
          secondaryNode.deleteObject();
        }
        this.autoLayout();
      },
      "delete secondary sources",
      true
    );
  }

  /**
   * update secondary source
   * @memberof TFDiagramBuilder
   */
  updateSecondarySources() {
    this.oEditor.resource.applyUndoableAction(
      () => {
        const viewTransform = this.oModel.nodes.toArray().find((node) => node.isViewTransform === true);
        if (viewTransform.isTemplate) {
          this.deleteSecondarySources();
        } else {
          this.deleteSecondarySources();
          this.showSecondarySources();
        }
      },
      "update secondary sources",
      true
    );
  }

  /**
   * Create a New parameter and adds to ouput.
   */
  createNewParameterElement(oNode, param) {
    const oClass = sap.galilei.model.getClass("sap.cdw.querybuilder.ViewParameter");
    const oParamElement = oClass.create(oNode.resource, param);
    oParamElement.definedBy = param.definedBy;
    oNode.parameters.push(oParamElement);
    return oParamElement;
  }

  /**
   * Creates parameters from the graph json
   *
   * @param {*} graphContent
   */
  createParameters(graphContent) {
    const oFeatures = sap.ui.getCore().getModel("featureflags").getData();
    if (
      oFeatures.DWCO_TRF_INPUT_PARAMETERS_SUPPORT &&
      graphContent?.parameters &&
      typeof graphContent.parameters === "object"
    ) {
      for (const param in graphContent.parameters) {
        const parameter = {
          name: param,
          dataType: graphContent.parameters[param].type,
          defaultValue: graphContent.parameters[param].default,
          length: graphContent.parameters[param].length,
          precision: graphContent.parameters[param].precision,
          scale: graphContent.parameters[param].scale,
          definedBy: graphContent.parameters[param].definedBy,
        };
        this.createNewParameterElement(this.oModel, parameter);
      }
    }
  }
}
