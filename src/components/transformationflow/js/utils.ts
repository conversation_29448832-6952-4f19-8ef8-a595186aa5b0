/**
 * Copyright 2023 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */
import { State } from "@sap/dwc-circuit-breaker";
import { getArtefactSharesForTarget } from "../../../services/metadata";
import { isSACPlanningIntegrationEnabled } from "../../abstractbuilder/commonservices/FeatureFlagCheck";
import * as RepositoryUtils from "../../abstractbuilder/utility/RepositoryUtils";
import { DeploymentStatus } from "../../commonmodel/api/DocumentStorageService";
import { DataWarehouse } from "../../commonmodel/csn/csnAnnotations";
import { isAnalyticMeasureElement } from "../../commonmodel/utility/CommonUtils";
import { getReleaseStateFromValue } from "../../commonmodel/utility/CompatibilityContractUtils";
import { GraphicalViewEditorComponentClass, IReplaceSourceOptions } from "../../csnquerybuilder/Component";
import { ContentType, HttpMethod, ServiceCall } from "../../reuse/utility/ServiceCall";
import { ObjectStatus } from "../../reuse/utility/Types";
import { Logger } from "../../reuse/utility/UIHelper";
import { isCircuitBreakerYellowStateEnabled } from "../../shell/featureState/CircuitBreakerYellowStateFeatureState";
import { Crud } from "../../shell/utility/Crud";
import { Repo } from "../../shell/utility/Repo";
import { UISpaceCapabilities } from "../../shell/utility/UISpaceCapabilities";
import { getDependentObjects } from "../../sqleditor/utility/ParserUtil";
import { removeMapping } from "../../sqleditor/utility/SqlEditorUtility";
import { IParameterTF } from "../controller/TFModeler.controller";
import { excluded_dataTypes_list, excluded_dataTypes_list_old } from "../js/utility/SparkUtils";
import Constants from "./utility/Constants";

export interface IReplaceOptions {
  gve: GraphicalViewEditorComponentClass;
  sourceEntity: any;
  targetEntityName: string;
  spaceName: string;
  object: any;
}

export interface ISpaceEntity {
  space: string;
  entity: string;
}

export interface ILineageProperties {
  hasRemoteTable: boolean;
  hasRemoteTableOtherSpaces: boolean;
  spaceRemoteTables: string[];
  otherSpacesRemoteTables: ISpaceEntity[];
  hasDAC?: boolean;
}

export interface ISourceDefinitions {
  [key: string]: ICsnDefinitionExtended;
}

export interface ICsnDefinitionExtended extends ICsnDefinition {
  isCrossSpace?: boolean;
  crossSpaceName?: string;
}

export interface ICrossSpaceObject {
  name: string;
  csn: ICsn;
  "#isViewEntity": boolean;
  isCrossSpace: boolean;
  spaceName: string;
  qualified_name: string;
  "#objectStatus": ObjectStatus;
  releaseStateValue: string;
  "#repositoryPackage": string;
}

export interface IExtendedValidationData {
  sourceDefinitions: ISourceDefinitions;
}

export interface IParameterDefinition {
  default: string;
  type: string;
  definedBy?: "user" | "system";
  precision?: number;
  scale?: number;
  length?: number;
}

export interface ITransformationFlowJSON {
  kind: string;
  "@EndUserText.label": string;
  contents: {
    metadata: {
      deltaEnabled: boolean;
    };
    parameters: {
      [key: string]: IParameterDefinition;
    };
  };
}

export interface ITransformationFlowCsn {
  transformationflows: {
    [name: string]: ITransformationFlowJSON;
  };
  $version?: number;
}

export interface ITransformationFlowObject {
  id: string;
  name: string;
  csn: ITransformationFlowCsn;
}

export default class Utils {
  static getDWCMetadata(csnData: any, cdcColumns: string[] = [], customTypesMap?: any): any {
    const name = csnData.name || csnData.technicalName;
    let canManipulateData: boolean | string;

    let objectType: string;
    if (csnData?.type) {
      objectType = Utils.isDWCViewType(csnData) ? "VIEW" : "TABLE";
    }

    const details = {
      metadata: {
        label: name,
        config: {},
      },
      definition: undefined,
      columns: new Map(),
      inValidColumns: [],
      canManipulateData: canManipulateData,
    } as any;

    if (isSACPlanningIntegrationEnabled()) {
      details.canManipulateData = csnData["#canManipulateData"];
    }

    let definition = csnData.definitions[name];

    for (const key in csnData.definitions) {
      const externalSchemaName = csnData.definitions[key][DataWarehouse.external_schema];
      if (externalSchemaName) {
        details.metadata.config.dwcExternalSchema = externalSchemaName;
        break;
      }
    }

    if (!definition) {
      // Getting definitions for Cross space tables.
      for (const key in csnData.definitions) {
        if (csnData.definitions[key].kind === "context" && csnData.definitions[key][DataWarehouse.space_name]) {
          const spaceName = csnData.definitions[key][DataWarehouse.space_name];
          const elementKey = `${spaceName}.${name}`;
          if (csnData.definitions[elementKey]) {
            definition = csnData.definitions[elementKey];
            details.metadata.config.dwcSpace = spaceName;
            break;
          }
        }
      }
    }
    // Check if the table is delta enabled
    if (definition) {
      const deltaAnnotation = definition[DataWarehouse.delta];
      if (deltaAnnotation?.dateTimeElement?.["="] && deltaAnnotation?.type?.["#"]) {
        cdcColumns.push(deltaAnnotation?.dateTimeElement?.["="]);
        cdcColumns.push(deltaAnnotation?.modeElement?.["="]);
      }
    }
    if (definition && definition.elements) {
      const elements = definition.elements;
      for (const key in elements) {
        // check for analytical measure and skip column
        if (objectType === "VIEW" && isAnalyticMeasureElement(elements[key])) {
          details.inValidColumns.push(key);
          // remove unsupported column from definition
          delete definition.elements[key];
          continue;
        }
        const item = Utils.getItemFromDefinition(csnData.definitions, elements[key], customTypesMap);
        if (item) {
          if (item.type && item.type !== "cds.Association") {
            let isCDCColumn = false;
            if (cdcColumns?.length > 0) {
              isCDCColumn = Utils.isCDCColumn(cdcColumns, key);
            }
            const column: any = {
              name: key,
              datatype: item.type,
              length: item.length,
              precision: item.precision,
              scale: item.scale,
              key: item.key,
              isCDCColumn: isCDCColumn,
              label: item["@EndUserText.label"] ? item["@EndUserText.label"] : undefined,
              notNull: item.notNull,
              default: item.default,
              baseType: item.baseType ? item.baseType : undefined,
            };
            // fix for bug FPA101-9976. uuid type should have fixed length
            // if (item.type === "cds.UUID") {
            //   column.length = 36;
            // }
            details.columns.set(column.name, column);
          } else {
            details.inValidColumns.push(key);
            // remove unsupported column from definition
            delete definition.elements[key];
          }
        }
      }
    }
    details.definition = definition; // used in resource loader to update business name
    return details;
  }

  /**
   * get the hana state from the circuit breaker model
   * @returns {boolean}
   */

  static isHanaNotProvisioned(): boolean {
    const circuitbreakermodel = sap.ui.getCore().getModel("circuitbreaker");
    const hanaProvisioningState = circuitbreakermodel?.getProperty("/DataHANAProvisioningState");
    const hanaNotProvisioned: boolean =
      hanaProvisioningState === State.Red ||
      // On FF removal of isCircuitBreakerYellowStateEnabled be sure to keep boolean logic consistent
      (hanaProvisioningState === State.Yellow && !isCircuitBreakerYellowStateEnabled());
    return hanaNotProvisioned;
  }

  /**
   * This function will be called recursively to resolve simple type
   * @param definitions
   * @param item
   */
  static getItemFromDefinition(definitions, item, customTypesMap?) {
    if (item.type && !item.type.startsWith("cds.")) {
      // simple type
      if (definitions[item.type]) {
        const subItem = definitions[item.type];
        return Utils.getItemFromDefinition(definitions, subItem);
      }
      if (customTypesMap && customTypesMap[item.type]) {
        const custType = customTypesMap[item.type];
        const typeDef = Utils.getItemFromDefinition(customTypesMap, custType);
        return {
          type: typeDef.type,
          length: item.length ? item.length : typeDef.length,
          precision: item.precision ? item.precision : typeDef.precision,
          scale: item.scale ? item.scale : typeDef.scale,
          key: item.key ? item.key : typeDef.key,
          "@EndUserText.label": item["@EndUserText.label"] ? item["@EndUserText.label"] : typeDef["@EndUserText.label"],
          notNull: item.notNull ? item.notNull : typeDef.notNull,
          default: item.default ? item.default : typeDef.default,
          baseType: item.type,
        };
      }
    }
    return item;
  }

  static getDefaultTargetTableMetadata() {
    return {
      config: {
        // dwcEntity: "",
        // hanaConnection: {
        //   configurationType: "Configuration Manager",
        //   connectionID: Constants.DWC_CONNECTION_ID,
        // },
        // mode: "append",
        // qualifiedName: "",
        // remoteObjectType: "TABLE",
        // service: "HANA",
        // forceBatchSize: Constants.DEFAULT_ADVANCED_PROPS.forceBatchSize,
        // batchSize: Constants.DEFAULT_ADVANCED_PROPS.batchSize,
      },
      x: 0,
      y: 0,
      label: "",
    };
  }

  // show only the basic status
  static getObjectDeploymentStatus(object): DeploymentStatus {
    if (object.deployment_date === null) {
      // RepositoryUtils.getObjectFileDeploymentStatus checks deployment_date = undefined
      // eslint-disable-next-line camelcase
      object.deployment_date = undefined;
    }
    return RepositoryUtils.getObjectFileDeploymentStatus(object);
  }

  /**
   *
   *
   * @static
   * @param {sap.cdw.transformationflow.Attribute} oSourceColumn
   * @param {sap.cdw.transformationflow.Attribute} oTargetColumn
   * @param {boolean} [allowTruncation=false]
   * @returns {string}
   * @memberof Utils
   */
  static validateTargetColumnMapping(
    oSourceColumn: sap.cdw.transformationflow.Attribute,
    oTargetColumn: sap.cdw.transformationflow.Attribute
  ): string {
    if (oSourceColumn && oTargetColumn) {
      const dataTypeMap = Constants.TargetDataTypeMap[oTargetColumn.datatype];
      if (dataTypeMap) {
        if (Array.isArray(dataTypeMap.valid) && dataTypeMap.valid.includes(oSourceColumn.datatype)) {
          const oFeatures = sap.ui.getCore().getModel("featureflags").getData();
          const isSourceCdcTimestamp = oSourceColumn?.datatype === "cds.Timestamp" && oSourceColumn.isCDCColumn;
          const isTargetCdcTimestamp = oTargetColumn?.datatype === "cds.Timestamp" && oTargetColumn.isCDCColumn;
          if (
            oFeatures?.DWCO_TABLE_DELTA_UPSERT_BLOCK_DELTA_BLEEDING &&
            ((isSourceCdcTimestamp && !isTargetCdcTimestamp) || (!isSourceCdcTimestamp && isTargetCdcTimestamp))
          ) {
            return Constants.Mapping.InvalidTimestampColumn;
          }
          // length check for string
          if (oSourceColumn.datatype === "cds.String" && oTargetColumn.datatype === "cds.String") {
            if (oSourceColumn.length && oTargetColumn.length) {
              // string with length
              if (oSourceColumn.length > oTargetColumn.length) {
                return Constants.Mapping.Invalid;
              }
            }
          }
          return Constants.Mapping.Valid;
        }
      } else if (oSourceColumn.datatype === oTargetColumn.datatype) {
        // simple type
        return Constants.Mapping.Valid;
      }
    }
    return Constants.Mapping.Invalid;
  }

  /**
   *
   *
   * @static
   * @param {*} item
   * @returns
   * @memberof Utils
   */
  static generateDatatypeText(item: any) {
    let datatypeText: string;
    if (item && item.datatype) {
      datatypeText = item.datatype;
      if (item.datatype === "cds.Decimal") {
        datatypeText += `(${item.precision},${item.scale})`;
      }
      if (item.datatype === "cds.String" || item.datatype === "cds.Binary") {
        if (item.length !== undefined && item.length !== "") {
          // length is optional
          datatypeText += `(${item.length})`;
        }
      }
    }
    return datatypeText;
  }
  /**
   * Get Error message input parameter datatype validation
   *
   * @memberof Utils
   */
  static getErrorMessageForType(type: string): string {
    const bundleName = require("./../i18n/i18n.properties");
    const i18nBundle = new sap.ui.model.resource.ResourceModel({
      bundleName,
    });
    switch (type) {
      case "cds.Integer":
        return i18nBundle.getProperty("INVALID_NUMBER");
      case "cds.Decimal":
        return i18nBundle.getProperty("INVALID_DECIMAL");
      case "cds.Date":
        return i18nBundle.getProperty("INVALID_DATE");
      case "cds.Time":
        return i18nBundle.getProperty("INVALID_TIME");
      case "cds.DateTime":
        return i18nBundle.getProperty("INVALID_DATETIME");
      case "cds.Boolean":
        return i18nBundle.getProperty("INVALID_BOOLEAN");
      default:
        return i18nBundle.getProperty("INVALID_INPUT");
    }
  }

  /**
   *
   *
   * @static
   * @param {sap.ui.model.resource.ResourceModel} model
   * @param {string} text
   * @param {any[]} [parameters=[]]
   * @returns {string}
   * @memberof Utils
   */
  static localizeText(model: sap.ui.model.resource.ResourceModel, text: string, parameters: any[] = []): string {
    try {
      return model.getResourceBundle().getText(text, parameters);
    } catch (e) {
      return text;
    }
  }

  /**
   *
   * @static
   * @param {sap.ui.core.mvc.View} view View
   * @param {string} sModelName
   * @param {string} sMessageId
   * @returns
   */
  static localizeMessage(view: sap.ui.core.mvc.View, sModelName: string, sMessageId: string): string {
    try {
      const i18nText = view.getModel(sModelName).getProperty(sMessageId);
      if (i18nText) {
        return i18nText;
      }
      return sMessageId;
    } catch (e) {
      return sMessageId;
    }
  }

  /**
   * Description
   * @param {any} csnData
   * @returns {any}
   */
  static isDWCViewType(csnData: any) {
    if (csnData) {
      const technicalType = csnData["#technicalType"];
      if (technicalType === "DWC_VIEW") {
        return true;
      }
    }
    return false;
  }

  /* Returns true if the column is CDC (change delta capture)column
   * @param {string} cdcColumns[]
   * @param {string} key
   * @returns {boolean}
   * @memberof Utils
   */
  static isCDCColumn(cdcColumns: string[], key: string): boolean {
    let isCDCColumn = false;
    if (cdcColumns.includes(key)) {
      isCDCColumn = true;
    }
    return isCDCColumn;
  }

  static isDWCObject(oNode: sap.cdw.transformationflow.Node) {
    if (oNode.component === Constants.OPERATORS.TARGET) {
      return true;
    }
    if (Utils.isTableAsSourceSupportedSpark() && oNode.component === Constants.OPERATORS.SOURCE_TABLE) {
      return true;
    }
    return false;
  }

  /* Returns true if table name is unique
   * @param {string} name
   * @returns {Promise<boolean>}
   * @memberof Utils
   */
  static async checkUniqueness(name: string, clearCache = false, spaceName?: string): Promise<boolean> {
    // eslint-disable-next-line @typescript-eslint/no-misused-promises
    return new Promise(async (resolve, reject) => {
      if (clearCache) {
        Crud.get().clearCache([
          { type: Repo.space, name: spaceName },
          { type: Repo.model, name: name },
        ]);
      }
      const isExist = await RepositoryUtils.isCurrentRepositoryObjectExist(name);
      if (isExist) {
        resolve(false);
      } else {
        resolve(true);
      }
    });
  }

  /**
   * This function will fetch the repository object with all required details
   * @param object
   * @param {string} spaceName
   * @param {string[]} details
   * @returns
   */
  static async getRepositoryObject(object, spaceName: string, details: string[] = []) {
    if (object.isCrossSpace) {
      if (object.crossSpaceName && object.crossSpaceEntityName) {
        // Sometimes the user doesn't have permission on the source space,
        // and designObject service also doesn't support recursive cross space sharing,
        // we have to directly use shares/targetSpace to load data.
        if (details.length === 0) {
          details = Constants.SharedObjectDetails;
        }
        let file = await getArtefactSharesForTarget(spaceName, details, [`name:${object.crossSpaceEntityName}`]); // It's better to also filter source space on backend, but backend for now only support to filter guid of the source space, and frontend only has space name when the user don't have permission on the source space
        if (file && file.results && file.results.length > 0) {
          file = (file.results as [any]).filter(
            (f) => f.name === object.crossSpaceEntityName && f.spaceName === object.crossSpaceName
          );
          if (file && file.length === 1) {
            file = file[0];
            file.csn = file.content;
            delete file.content;
            if (typeof file.properties === "object") {
              if (file.properties["#repairedCsn"]) {
                file.csn = file.properties["#repairedCsn"];
                delete file.properties["#repairedCsn"];
              }
              // copy technical type and other properties to the root object
              Object.assign(file, file.properties);
              delete file.properties;
            }
          } else {
            file = undefined;
          }
        } else {
          file = undefined;
        }
        return Promise.resolve(file);
      }
    } else {
      if (details.length === 0) {
        details = Constants.RepoObjectDetails;
      }
      return RepositoryUtils.getRepositoryObject(spaceName, object.name, { details });
    }
  }

  static replaceGVSource(replaceOptions: IReplaceOptions): void {
    // replace source
    // fetch csn
    const targetEntityName = replaceOptions.targetEntityName;
    const sourceEntity = replaceOptions.sourceEntity;
    const object = replaceOptions.object;
    // DW101-83657	package value for table is lost after replace source
    const packageValue = sourceEntity.packageValue;
    const packageStatus = sourceEntity.packageStatus;
    // csn should have only 1 definition as the replace logic will take only the 1st object
    const csn = {
      definitions: {},
    } as any;
    csn.definitions[targetEntityName] = JSON.parse(JSON.stringify(object.csn.definitions[targetEntityName]));
    const isCrossSpace = sourceEntity.isCrossSpace;
    if (isCrossSpace && csn.definitions[targetEntityName]?.query) {
      // DW101-72081 delete query property from csn definition as it causes issue when converting GV to SQL VT
      delete csn.definitions[targetEntityName]?.query;
    }
    // call replace function in GVE
    const replaceSourceOptions: IReplaceSourceOptions = {
      oldEntity: sourceEntity,
      csnNewEntity: csn,
      excludeUnmatched: false,
    };
    replaceOptions.gve.replaceSource(replaceSourceOptions);
    // update properties
    // sourceEntity["#objectStatus"] = object["#objectStatus"];
    Utils.updateObjectProperties(sourceEntity, object);
    // DW101-83657	update package value for table after replace source
    sourceEntity.packageValue = packageValue;
    sourceEntity.packageStatus = packageStatus;
    // Cross space entity
    if (isCrossSpace) {
      const oErCsnToModel = sap.cdw.ermodeler.ErCsnToModel.getInstance(true);
      // update technical name of the source to include space name
      sourceEntity.name = `${object.spaceName}.${targetEntityName}`;
      oErCsnToModel.createContextAndAttachToObject(
        sourceEntity.container,
        sourceEntity,
        object.spaceName,
        {
          "@DataWarehouse.space.name": object.spaceName,
          "@DataWarehouse.space.schema": object.spaceSchemaName,
          "@EndUserText.label": object.spaceBusinessName,
          kind: "context",
        },
        /* bForce*/ true
      );
    }
  }

  static updateObjectProperties(entity, oFileObject) {
    if (oFileObject["#objectStatus"]) {
      entity.setObjectStatus(oFileObject["#objectStatus"]);
    } else if (entity.deployment_date && !entity.deployFailed) {
      entity.setObjectStatus(ObjectStatus.deployed);
    } else if (oFileObject.deployment_date) {
      entity.setObjectStatus(ObjectStatus.deployed);
    } else {
      entity.setObjectStatus(ObjectStatus.notDeployed);
    }
    if (oFileObject.deploymentStatus) {
      entity.deploymentStatus = oFileObject.deploymentStatus;
    }
    if (oFileObject.deployment_date) {
      entity.deploymentDate = oFileObject.deployment_date;
    } else if (oFileObject.deploymentDate) {
      entity.deploymentDate = oFileObject.deploymentDate;
    }
    if (oFileObject.modification_date) {
      entity.modificationDate = oFileObject.modification_date;
    }
    if (oFileObject.releaseStateValue) {
      entity.releaseState = getReleaseStateFromValue(oFileObject.releaseStateValue);
      // entity.initialState = getReleaseStateFromValue(oFileObject.releaseStateValue);
    }
  }

  static getDeltaEntityName(csn: ICsn): string {
    let deltaEntityName: string;
    const deltaAnnotation = csn?.[DataWarehouse.delta];
    if (Array.isArray(deltaAnnotation?.deltaFromEntities) && deltaAnnotation.deltaFromEntities.length) {
      deltaEntityName = deltaAnnotation.deltaFromEntities[0];
    }
    return deltaEntityName;
  }

  static getAllActiveRecordsViewName(csn: ICsn): string {
    return csn?.["@DataWarehouse.enclosingObject"];
  }

  static getReplaceObjectFromCSN(csn, name: string) {
    let object;
    if (csn[DataWarehouse.space_name]) {
      // cross space
      object = {
        isCrossSpace: true,
        crossSpaceName: csn[DataWarehouse.space_name],
        crossSpaceEntityName: name,
      };
    } else {
      object = {
        name: name,
      };
    }
    return object;
  }

  static generateDeltaCaptureTableName(technicalName: string): string {
    return `${technicalName}${Constants.DELTA_TABLE_SUFFIX}`;
  }

  /**
   * Sets load type
   * @static
   * @param {sap.cdw.transformationflow.Model} oModel
   * @memberof Utils
   */
  static setLoadType(oModel: sap.cdw.transformationflow.Model) {
    let source: sap.cdw.transformationflow.Node;
    let target: sap.cdw.transformationflow.Node;
    for (let nIndex = 0; nIndex < oModel.nodes.length; nIndex++) {
      const oNode: sap.cdw.transformationflow.Node = oModel.nodes.get(nIndex);
      if (Utils.isTransformNode(oNode) || (Utils.isTableAsSourceSupportedSpark() && Utils.isSourceTableNode(oNode))) {
        source = oNode;
      } else if (Utils.isTargetNode(oNode)) {
        target = oNode;
      }
    }
    let isSourceDelta = source?.isDeltaTable ? true : false;
    if (
      Utils.isTableAsSourceSupportedSpark() &&
      source?.component === Constants.OPERATORS.SOURCE_TABLE &&
      source?.useAs !== Constants.LoadFromLocalTableType.DELTA
    ) {
      isSourceDelta = false;
    }
    if (isSourceDelta && target?.isDeltaTable) {
      oModel.loadType = Constants.LoadType.INITIAL_AND_DELTA;
    } else {
      oModel.loadType = Constants.LoadType.INITIAL_ONLY;
    }
  }

  /**
   *
   *
   * @static
   * @param {sap.cdw.transformationflow.Node} oNode
   * @return {*}  {boolean}
   * @memberof Utils
   */
  static isTargetNode(oNode: sap.cdw.transformationflow.Node): boolean {
    if (Constants.TARGET_OPERATORS.has(oNode?.component)) {
      return true;
    }
    return false;
  }

  /**
   *
   *
   * @static
   * @param {sap.cdw.transformationflow.Node} oNode
   * @return {*}  {boolean}
   * @memberof Utils
   */
  static isTransformNode(oNode: sap.cdw.transformationflow.Node): boolean {
    if (Constants.TRANSFORM_OPERATORS.has(oNode?.component)) {
      return true;
    }
    return false;
  }

  /**
   * Checks if the given source node has a table as source
   *
   * @static
   * @param {sap.cdw.transformationflow.Node} oNode
   * @return {*}  {boolean}
   * @memberof Utils
   */
  static isSourceTableNode(oNode: sap.cdw.transformationflow.Node): boolean {
    if (Constants.SOURCE_TABLE_OPERATORS.has(oNode?.component)) {
      return true;
    }
    return false;
  }

  /**
   * Fetch the high watermark and add it to galilei model
   * @param model transformation flow galilei model
   * @param spaceName space technical name
   */
  static async updateWaterMarkData(model: sap.cdw.transformationflow.Model, spaceName: string) {
    try {
      const deltaSubscriptions = await ServiceCall.get(
        `transformationflow/${spaceName}/deltasubscriptions/${model.name}`
      );
      if (deltaSubscriptions.data?.[0]?.highWaterMark) {
        model.highWaterMarkExists = true;
      } else {
        model.highWaterMarkExists = false;
      }
      // store target table name
      const targetNode = model.nodes.toArray().find((n) => n.isTargetTable === true);
      if (targetNode?.isTemplate !== true) {
        model.targetTableTechnicalName = targetNode?.technicalName;
      }
    } catch (error) {
      Logger.logError(`Unbale to fetch delta subscriptions for ${model.name}`, error);
    }
  }

  /**
   * returns true when given space is BW Bridge type.
   *
   * @static
   * @param {string} spaceName
   * @return {*}  {Promise<boolean>}
   * @memberof Utils
   */
  static async isBWBridgeSpace(spaceName: string): Promise<boolean> {
    if (spaceName && spaceName !== "" && typeof spaceName === "string") {
      try {
        const spaceDetails = await Repo.getSpaceDetails(spaceName, ["spaceType"]);
        if (spaceDetails?.spaceType === "abapbridge") {
          return true;
        }
      } catch (error) {
        // getSpaceDetails will throw if the sapce does not exist or is not allowed for current user
        // The same exception will also be handled by BreadCrumbs - which will navigate back to Space Selection screen
        // Therefore we can ignore it here.
        return true;
      }
    }
    return false;
  }

  /**
   * Returns lineage properties
   *
   * @param {string} spaceName
   * @param {string[]} entityList
   */
  static async getEntityLineageProps(spaceName: string, entityList: ISpaceEntity[]): Promise<any> {
    let response;
    try {
      let sUrl = `transformationflow/${spaceName}/hasremotetables`;
      const data = {
        entityList: entityList,
      };
      response = await ServiceCall.request<any>({
        url: sUrl,
        type: HttpMethod.POST,
        contentType: ContentType.APPLICATION_JSON,
        data: JSON.stringify(data),
      });
    } catch (error) {
      if (error?.length > 0 && error[0]?.responseJSON?.details?.code === 10001) {
        Logger.logInfo(`View is undeployed, hence unable to fetch entity lineage properties for ${entityList}`);
      } else {
        Logger.logError(`Unbale to fetch entity lineage properties for ${entityList}`, error);
      }
    }
    return response;
  }

  /**
   * Concat space and cross space remote tables array into single array
   *
   * @static
   * @param {ILineageProperties} lineageProp
   * @returns {string[]}
   */
  static getRemoteTableNames(lineageProp: ILineageProperties): string[] {
    let remoteTablesArray: string[] = [];
    if (lineageProp.spaceRemoteTables.length > 0) {
      remoteTablesArray.push(...lineageProp.spaceRemoteTables);
    }
    if (lineageProp.otherSpacesRemoteTables.length > 0) {
      lineageProp?.otherSpacesRemoteTables?.forEach((item) => {
        const fullName = `${item.space}.${item.entity}`;
        remoteTablesArray.push(fullName);
      });
    }
    return remoteTablesArray;
  }

  /**
   * Return entity name for crossspace objects
   *
   * @static
   * @param {string} entityName
   * @param {string} crossSpaceName
   * @returns {string}
   */
  static getEntityName(entityName: string, crossSpaceName: string): string {
    return entityName?.substring(crossSpaceName?.length + 1, entityName?.length);
  }

  /* Check the change management data for source
   * @param data Change management data
   * @returns Flag whether the change management data has source
   */
  static changeManagementHasSource(data: { modifiedObjects: Array<{ autofixedObjects: any[] }> }): boolean {
    let sourceExists = false;
    if (data?.modifiedObjects && Array.isArray(data.modifiedObjects)) {
      for (const modifiedObjectItem of data.modifiedObjects) {
        if (Array.isArray(modifiedObjectItem.autofixedObjects)) {
          for (const autofixedObjectItem of modifiedObjectItem.autofixedObjects) {
            if (autofixedObjectItem.sources) {
              sourceExists = true;
              break;
            }
          }
        }
        if (sourceExists) {
          break;
        }
      }
    }

    return sourceExists;
  }

  /**
   * returns if the cross space table is an open SQL Schema table or not
   *
   * @static
   * @return {Promise<boolean>}
   * @memberof Utils
   */
  static async isOpenSchemaCrossSpace(entity: sap.cdw.querybuilder.Entity, spaceName: string): Promise<boolean> {
    if (entity.crossSpaceName && entity.crossSpaceEntityName) {
      const fullCsn = await Utils.getRepositoryObject(
        {
          isCrossSpace: true,
          crossSpaceName: entity.crossSpaceName,
          crossSpaceEntityName: entity.crossSpaceEntityName,
        },
        spaceName,
        [DataWarehouse.external_entity]
      );
      if (fullCsn?.[DataWarehouse.external_entity]) {
        return true;
      }
    }
    return false;
  }

  /**
   * Check if data types hana.TINYINT, Datetime and hana.BINARY is activated in transformation flow
   *
   * @static
   * @returns {boolean}
   * @memberof Utils
   */
  static isAdditionalDataTypesDisabled(): boolean {
    const oFeatures = sap.ui.getCore().getModel("featureflags").getData();
    if (oFeatures && oFeatures.DWCO_LOCAL_TABLE_FILES_HANAQRC4_2024) {
      return true;
    }
    return false;
  }

  /**
   * Checks if given space is of type "fileSpace"
   * @param {string} spaceName
   * @returns boolean
   */
  static isLargeSystemSpace(spaceName: string): boolean {
    if (spaceName) {
      return UISpaceCapabilities.get().hasCapability(spaceName, "hdlfStorage");
    }
    return false;
  }

  /**
   * Returns false if feature is not supported in spark runtime
   *
   * @static
   * @param {sap.cdw.transformationflow.Node} galileiObject
   * @returns {boolean}
   */
  static isFeatureSupportedInSparkRT(galileiObject: sap.cdw.transformationflow.Node): boolean {
    if (galileiObject?.container?.isLargeSystemSpace) {
      return false;
    }
    return true;
  }

  /**
   * Find the input parameters inside transformationflow deployed copy
   * @param spaceId Space technical name
   * @param transformationFlow TransformationFlow technical name
   * @returns Input parameters list
   */
  static async findInputParametersInDeployedTF(
    spaceId: string,
    transformationFlowName: string
  ): Promise<IParameterTF[]> {
    const parameterList: IParameterTF[] = [];
    // Fetch deployed copy of transformation flow and check for parameters
    const url = `transformationflow/${spaceId}/${transformationFlowName}/deployedobject`;
    const response = (await ServiceCall.get(url)) as { data: ITransformationFlowObject | undefined };
    if (response.data) {
      const csn = typeof response.data === "object" ? response.data.csn : undefined;
      if (csn) {
        const deployedTrasnformationFlow = csn?.transformationflows?.[transformationFlowName]
          ? csn.transformationflows[transformationFlowName]
          : undefined;
        const parameters = deployedTrasnformationFlow?.contents.parameters;
        if (parameters) {
          for (const key in parameters) {
            parameterList.push({
              name: key,
              value: parameters[key]?.default || "",
              type: parameters[key]?.type,
            });
          }
        }
      }
    }
    return parameterList;
  }

  /**
   * Returns list of datatypes supported in spark runtime
   *
   * @static
   * @param {Array<{ key: string }>} defaultList
   * @returns {Array<{ key: string }>}
   */
  static getSparkSupportedDatatypes(defaultList: Array<{ key: string }>): Array<{ key: string }> {
    return defaultList.filter((dataType) => {
      let list = excluded_dataTypes_list;
      if (!Utils.isAdditionalDataTypesDisabled()) {
        list = excluded_dataTypes_list_old;
      }

      if (!list.includes(dataType?.key?.toLowerCase())) {
        return dataType;
      }
    });
  }

  static skipParamsValidationAndLabel(entity): boolean {
    const csn = entity?.csn || entity?.repositoryCSN;
    const ltfAnnotation = csn?.[DataWarehouse.persistence_hdlf_tableFormat];
    if (ltfAnnotation && entity?.isTable) {
      return true;
    }
    return false;
  }

  static isCustomScriptColumn(
    column: sap.cdw.transformationflow.Attribute,
    nodeObject: sap.cdw.transformationflow.Node
  ): boolean {
    const sourceNode = nodeObject?.getSourceNode()?.getAttributesArray();
    return !sourceNode?.find((attr) => attr.name === column.name);
  }

  static getAllCustomColumn(nodeObject: sap.cdw.transformationflow.Node) {
    const sourceNodeAttributes = nodeObject.getSourceNode()?.getAttributesArray() || [];
    const pythonNodeAttributes = nodeObject?.getAttributesArray() || [];

    // Create a set of source column names
    const sourceNodeSet = new Set(sourceNodeAttributes.map((attribute) => attribute.name));

    // Mark attributes as custom script columns if they are not in the source node set
    pythonNodeAttributes.forEach((attribute) => {
      attribute.isCustomScriptColumn = !sourceNodeSet.has(attribute.name);
    });
  }

  /**
   * Invalidates the expression with CDC columns in calculated column for spark runtime
   *
   * @static
   * @param {sap.cdw.transformationflow.Node} galileiObject
   * @returns {boolean}
   */
  static invalidateComplexExpressionsWithCDCColumns(galileiObject: sap.cdw.transformationflow.Node): boolean {
    if (galileiObject?.container?.isLargeSystemSpace) {
      return true;
    }
    return false;
  }

  /**
   *
   * @param {ICsn} csn
   * @returns
   */
  static async getDependentObjects(csn: ICsn) {
    let dependentObjects = [];
    try {
      if (csn?.definitions?.["sqltransform1"]) {
        let query = csn.definitions["sqltransform1"][DataWarehouse.sqlEditor_query];
        const values = removeMapping(query);
        if (values && values.processedSql) {
          query = values.processedSql;
        }
        dependentObjects = await getDependentObjects(query, true);
      } else if (csn?.definitions?.["sqlscripttransform1"]) {
        dependentObjects = await getDependentObjects(
          csn.definitions["sqlscripttransform1"][DataWarehouse.tableFunction_script],
          false
        );
      }
    } catch (err) {
      dependentObjects = [];
    }
    return dependentObjects;
  }

  /**
   * Generates the cdc columns when source table is dragged and dropped
   *
   * @param {sap.cdw.transformationflow.Node} oNode
   * @returns {string[]}
   */
  static getCdcColumnsSourceTable(oNode: sap.cdw.transformationflow.Node): string[] {
    const elements = oNode.getAttributesArray();
    const cdcColumns: string[] = [];
    elements.forEach((element) => {
      if (element.isCDCColumn) {
        cdcColumns.push(element.name);
      }
    });
    return cdcColumns;
  }

  /**
   * filter repository objects
   * @param {any} dependentObjects
   * @param {any} filteredRepositoryObjects
   * @param {string} spaceName
   * @returns
   */
  static filterRepoObjects(dependentObjects: any, filteredRepositoryObjects: any, spaceName: string) {
    const modelNames = [];
    const sharedNames = [];
    for (const object of dependentObjects) {
      const name = object.identifier;
      let aObject = filteredRepositoryObjects.find((repo) => repo.name === name);
      if (aObject) {
        modelNames.push(name);
        continue;
      }
      let entityName;
      if (name.includes(".")) {
        const entitySpaceName = sap.cdw.commonmodel.ObjectImpl.getQualifierName(name, true);
        entityName = sap.cdw.commonmodel.ObjectImpl.removeQualifierInName(name, true);
        if (entitySpaceName === spaceName) {
          aObject = filteredRepositoryObjects.find((repo) => repo.name === entityName);
          if (aObject) {
            modelNames.push(entityName);
          }
        } else {
          sharedNames.push(sap.cdw.commonmodel.ObjectImpl.removeQualifierInName(name, true));
        }
        continue;
      }
      if (object.schema) {
        sharedNames.push(name);
      }
    }
    return [modelNames, sharedNames];
  }

  /**
   * gets the repository objects
   * @param {string[]} modelNames
   * @param {string[]} sharedNames
   * @param {string} spaceName
   * @param {any} dependentObjects
   * @returns
   */
  static async getRepoObjects(modelNames: string[], sharedNames: string[], spaceName: string, dependentObjects: any) {
    let repoObjs, sharedObjects;
    const sourceDefinitions = {};
    if (modelNames?.length) {
      repoObjs = await Repo.getModelList(spaceName, ["csn"], { name: modelNames });
    }
    if (sharedNames?.length) {
      sharedObjects = await this.getCrossSpaceObjects(sharedNames, spaceName);
    }
    for (const object of dependentObjects) {
      const name = object.identifier;
      let aObject = repoObjs?.find((repo) => repo.name === name);
      if (aObject) {
        sourceDefinitions[name] = aObject;
        continue;
      }
      let entityName;
      if (name.includes(".")) {
        const entitySpaceName = sap.cdw.commonmodel.ObjectImpl.getQualifierName(name, true);
        entityName = sap.cdw.commonmodel.ObjectImpl.removeQualifierInName(name, true);
        if (entitySpaceName === spaceName) {
          aObject = repoObjs?.find((repo) => repo.name === entityName);
          if (aObject) {
            sourceDefinitions[name] = aObject;
          }
        } else {
          const crossSpaceObject = sharedObjects?.find((repo) => repo.name === name);
          if (crossSpaceObject) {
            sourceDefinitions[name] = crossSpaceObject;
          }
        }
        continue;
      }
      if (object.schema) {
        const crossSpaceObject = sharedObjects?.find((repo) => repo.name === `${object.schema}.${name}`);
        if (crossSpaceObject) {
          sourceDefinitions[`${object.schema}.${name}`] = crossSpaceObject;
        }
      }
    }
    return sourceDefinitions;
  }

  /**
   * gets the cross space objects
   * @param {string[]} sharedNames
   * @param {string} spaceName
   * @returns {ICrossSpaceObject[]}
   */
  static async getCrossSpaceObjects(sharedNames: string[], spaceName: string) {
    const sharedArtefacts = await getArtefactSharesForTarget(
      spaceName,
      [
        "#isViewEntity",
        "csn",
        "#consumptionCsn",
        "#objectStatus",
        "releaseStateValue",
        "#repositoryPackage",
        "@DataWarehouse.external.schema",
        "@DataWarehouse.remote.connection",
        "@DataWarehouse.persistence.hdlf.tableFormat",
        "@DataWarehouse.delta",
        "@DataWarehouse.remote.entity",
      ],
      [`name:${sharedNames.join("|")}`]
    );
    const crossSpaceObjects: ICrossSpaceObject[] = [];
    if (sharedArtefacts && sharedArtefacts.results) {
      sharedArtefacts.results.forEach((object) => {
        const name = object.name;
        const csn = object.properties["#consumptionCsn"];
        if (csn && csn.definitions && csn.definitions[name]) {
          csn.definitions[name]["@DataWarehouse.space.name"] = object.spaceName;
          csn.definitions[name].isCrossSpace = true;
          csn.definitions[object.spaceName + "." + name] = csn.definitions[name];
        }
        crossSpaceObjects.push({
          name: `${object.spaceName}.${name}`,
          csn,
          "#isViewEntity": object.properties["#isViewEntity"],
          isCrossSpace: true,
          spaceName: object.spaceName,
          qualified_name: object.qualifiedName,
          "#objectStatus": object.properties["#objectStatus"],
          releaseStateValue: object.properties.releaseStateValue,
          "#repositoryPackage": object.properties["#repositoryPackage"],
        });
      });
    }
    return crossSpaceObjects;
  }

  /**
   * Returns true if its large system space and spark runtime is enabled
   *
   * @static
   * @param {sap.cdw.transformationflow.Node} galileiObject
   * @returns {boolean}
   */
  static hideViews(galileiObject: sap.cdw.transformationflow.Node): boolean {
    if (galileiObject?.container?.isLargeSystemSpace) {
      return true;
    }
    return false;
  }

  /**
   * Determines if there is change management present in the given model change management object.
   *
   * @param modelChangeManagement - The model change management object to check for changes.
   * @returns `true` if change management is present, otherwise `false`.
   */
  static trfHasChangeManagement(modelChangeManagement): boolean {
    let hasChangeManagement = false;

    for (const modifiedObjectItem of modelChangeManagement.modifiedObjects) {
      if (
        sap.galilei.model.isInstanceOf(modifiedObjectItem.modifiedObject, "sap.cdw.transformationflow.Node") ||
        sap.galilei.model.isInstanceOf(modifiedObjectItem.modifiedObject, "sap.cdw.querybuilder.Entity")
      ) {
        hasChangeManagement = true;
        break;
      } else if (sap.galilei.model.isInstanceOf(modifiedObjectItem.modifiedObject, "sap.cdw.querybuilder.Output")) {
        const sources = modifiedObjectItem.autofixedObjects.filter((auto) => auto.sources);
        if (sources.length > 0) {
          hasChangeManagement = true;
          break;
        }
      }
    }
    return hasChangeManagement;
  }

  /**
   * Checks if Transformation Flow - SQL Script Data preview FF is enabled
   *
   * @static
   * @return {boolean}
   * @memberof Utils
   */
  static isSQLScriptDataPreviewFFEnabled(): boolean {
    const oFeatures = sap.ui.getCore().getModel("featureflags").getData();
    if (oFeatures && oFeatures.DWCO_TRF_DPREVIEW_SQLSCRIPT) {
      return true;
    }
    return false;
  }

  /**
   * Checks if Transformation Flow - Drag and drop source table onto primary editor FF is enabled
   *
   * @static
   * @return {boolean}
   * @memberof Utils
   */
  static isTableAsSourceSupportedSpark() {
    const oFeatures = sap.ui.getCore().getModel("featureflags").getData();
    if (oFeatures && oFeatures.DWCO_TRF_SPARK_PRI_ED_DND) {
      return true;
    }
    return false;
  }

  /**
   * Checks if incremental aggregation FF is enabled
   *
   * @static
   * @return {boolean}
   * @memberof Utils
   */
  static isIncrementalAggregationSupported(): boolean {
    const oFeatures = sap.ui.getCore().getModel("featureflags").getData();
    if (oFeatures && oFeatures.DWCO_TRF_SPARK_AGGR) {
      return true;
    }
    return false;
  }

  /**
   * returns the validation text message for a template node
   *
   * @param {sap.cdw.transformationflow.Node} oNode
   * @returns {string}
   */
  static getValidationMessageTemplateNode(oNode: sap.cdw.transformationflow.Node) {
    let sResult = "";
    if (oNode.component === Constants.OPERATORS.VIEW_TRANSFORM) {
      const isLargeSystemSpace = oNode?.container?.isLargeSystemSpace;
      if (Utils.isTableAsSourceSupportedSpark() && isLargeSystemSpace) {
        sResult = "validationEmptySource";
      } else {
        sResult = "validationEmptyViewTransform";
      }
    } else {
      sResult = "validationEmptyTargetTable";
    }
    return sResult;
  }

  /**
   * returns if datatype is numeric or not
   *
   * @param {string} datatype
   * @returns {boolean}
   */
  static isNumericDatatype(datatype: string): boolean {
    switch (datatype) {
      case "cds.Decimal":
      case "cds.Double":
      case "cds.Integer":
      case "cds.Integer64":
      case "cds.hana.REAL":
      case "cds.hana.SMALLINT":
      case "cds.hana.TINYINT":
        return true;
        break;
      default:
        return false;
    }
  }
}
