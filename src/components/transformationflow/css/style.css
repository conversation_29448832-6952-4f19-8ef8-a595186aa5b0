/** @format */

.tf-galilei-page .sapMPageHeader {
  border-bottom: solid 0.0625rem var(--sapShell_BorderColor);
}

.tf-galilei-page .sapMPageHeader .sapMBarContainer {
  background-image: linear-gradient(to right, var(--sapBackgroundColor), rgba(255, 255, 255, 0));
}

.tf-header-content .sapMBarRight {
  justify-content: flex-start;
}
.tf-palette-lbl {
  background-color: var(--sapBackgroundColor);
  display: inline-block !important;
  padding: 0.25rem 0.25rem 0.25rem 0.75rem;
  margin: 0 !important;
}

.sapUiTheme-sap_horizon #transformationFlowEditor .tf-palette-lbl {
  background-color: var(--sapField_Background);
  display: inline-block !important;
  padding: 0.25rem 0.25rem 0.25rem 0.75rem;
  margin: 0 !important;
}

.tf-palette-container {
  background-color: var(--sapBackgroundColor);
  width: 55%;
  border-bottom-right-radius: 1rem;
  border-bottom: none;
  border-right: solid 0.0625rem var(--sapShell_BorderColor);
  margin-left: 0 !important;
}

.tf-palette-container .operator[data-operator-type="create-table"] {
  padding-left: 8px;
}

.tf-palette-container .operator[data-operator-type="create-table"]:before {
  content: "";
  background-color: var(--sapToolbar_SeparatorColor);
  position: absolute;
  width: 0.0625rem;
  height: 1.5rem;
  left: 0;
  display: block;
}

#transformationFlowEditor--transformationFlowEditorControl--tf-palette-container-popover
  .operator[data-operator-type="create-table"] {
  padding-top: 8px;
  margin-bottom: 5px;
}

#transformationFlow--transformationFlowEditorControl--tf-palette-container-popover
  .operator[data-operator-type="create-table"]:before {
  content: "";
  background-color: var(--sapToolbar_SeparatorColor);
  position: absolute;
  height: 0.0625rem;
  width: 100%;
  top: 0.15rem;
  display: block;
}

.tf-palette-container .tf-palette-control,
.tf-palette-container .tf-palette-control .sapMBtnInner {
  background-color: transparent;
  border: none;
}

.tf-EditorToolbar {
  width: 25%;
  border-bottom: none;
}

.tf-token-label {
  align-self: center;
  font-size: 0.9rem;
  padding: 6.5px;
}

.tf-token {
  padding: 7px;
  color: var(--sapContent_NonInteractiveIconColor);
}

.columnName .token-details > span {
  white-space: nowrap;
  overflow-x: hidden;
  text-overflow: ellipsis;
}

.columnName .token-details {
  max-width: 72%;
}

.tf-mapping-container .sapMScrollContVH.sapMScrollCont {
  height: 200px !important;
}

.column-list-header {
  width: 100%;
  overflow: hidden;
  padding: 10px;
}

.columnInfoPopover {
  max-width: 30% !important;
}

.columnInfoPopover .sapUiRFL {
  display: contents !important;
}

.validationCell {
  height: 50px !important;
}

.validationCell > div {
  width: 100%;
  padding-right: 5px;
  padding-bottom: 5px;
}

.validationTextArea {
  height: 100% !important;
}

.validationVBox {
  padding: 5px;
  border: 0;
  min-height: 250px;
}

.validationVBox > div,
.validationVBox > div > div,
.validationVBox .sapMNav {
  min-height: 250px !important;
}

.dataTypeIcon {
  color: var(--sapNeutralTextColor);
}

#transformationFlowEditor--transformationFlowEditorControl--editorControl {
  overflow: hidden !important;
}

#transformationFlowEditor--transformationFlowEditorControl--tfWelcomeBox {
  margin-top: -230px;
  height: 230px;
  padding: 0 10%;
}

#transformationFlowEditor--transformationFlowEditorControl--tfWelcomeBox > div {
  overflow-y: hidden !important;
}

#transformationFlowEditor--transformationFlowEditorControl--tfWelcomeBox > .sapMFlexItem {
  padding-top: 5px;
}

.tf-token {
  padding: 7px;
  color: var(--sapContent_NonInteractiveIconColor);
}

#backToTFlow-inner {
  border: 1.5px solid;
}

.tf-properties-template {
  padding: 1.5rem;
}

.tf-properties-template > div {
  padding-bottom: 1rem;
}

#shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--transformationflow-properties-ViewTransformNodeProperties--useSQLEditor-img {
  padding-right: 0.75rem;
}

div:has(> .runStatusClass) {
  width: fit-content !important;
  margin: 7px !important;
}

.propertiesPanelContainer {
  margin-top: 2px;
}
