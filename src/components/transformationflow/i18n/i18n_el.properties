#~~~~~~~~~~~~~~~~~~~~~~~ Properties Panel texts starts here ~~~~~~~~~~~~~~~~~~~~
#XTIT
properties=Ιδιότητες
#XTIT
general=Γενικό
#XFLD
businessName=Επωνυμία Επιχείρησης
#XFLD
technicalName=Τεχνικό Ονομα
#XFLD
loadType=Τύπος Φόρτωσης
#XFLD
deltaCaptureTableName=Ονομα Πίνακα Λήψης Delta
#XSEL
initialOnly=Αρχικό Μόνο
#XSEL
initialAndDelta=Αρχικό και Delta
#XCKL
truncate=Περικοπή
#XCKL
deleteAllBeforeLoading=Διαγραφή Όλων πριν την Φόρτωση
#XTIT
columns=Στήλες
#XTIT
mappings=Αντιστοιχίσεις
#XFLD
columnDataType=Τύπος Δεδομένων
#XFLD
search=Αναζήτηση
#XBUT
autoMap=Αυτόματη Αντιστοίχιση
#XBUT
removeAllMappings=Διαγραφή Ολων των Αντιστοιχίσεων
#XMSG
noTargetInputs=Θέλετε να καθορίσετε έναν χειριστή Προβολής Μετασχηματισμού για να εμφανίσετε τις πληροφορίες αντιστοίχισης.
#XMSG
noColumnsData=Δεν υπάρχουν στήλες για εμφάνιση.
#XTOL
@validateModel=Μηνύματα Επικύρωσης
#XTOL
@hierarchy=Ιεραρχία
#XTOL
@columnCount=Αριθμός Στηλών
#XTOL
info=Πληροφορίες
#XTOL
cdcColumn=Στήλη CDC
#XTIT
statusPanel=Κατάσταση Εκτέλεσης
#XTOL
schedule=Χρονοδ/μα
#XTOL
navToMonitoring=Ανοιγμα σε Οθόνη Ροής Μετασχηματισμού
#XBTN
createSchedule=Δημιουργία Προγράμματος
#XBTN
editSchedule=Επεξεργασία Προγράμματος
#XBTN
deleteSchedule=Διαγραφή Προγράμματος
#XFLD
lastRun=Τελευταία Εκτέλεση
#XFLD
status=Κατάσταση
#XMSG: Error run status cannot be fetched
errorDetails=Αδύνατη ανάκτηση κατάστασης εκτέλεσης.
#XLNK
viewDetails=Προβολή Λεπτομερειών
#XMSG: Error data is not loading from the server
backendError=Τα δεδομένα ενδεχομένως δεν φορτώνονται από τον διακομιστή προσωρινά. Δοκιμάστε αργότερα.
#XMSG: Error transformation flow run failed. open data integration monitoring for more details
viewDetailsFallback=Η εκτέλεση ροής μετασχηματισμού δεν άρχισε. Πλοηγηθείτε στην εφαρμογή Παρακολούθηση για περισσότερες λεπτομέρειες.
#XMSG
statusCompleted=Ολοκληρωμένο
#XMSG
statusRunning=Εκτελείται
#XMSG
statusFailed=Απέτυχε
#XMSG
statusNotExecuted=Δεν Εκτελέστηκε Ακόμα
#XBTN
editColumns=Επεξεργασία Στηλών
#XBUT: button in the properties panel of the target table
createNewTargetTable=Δημιουργία Νέου Πίνακα Στόχου
#XMSG: message shown in the properties panel of target table when it is not defined.
defineTargetInformation=Για να δημιουργήσετε έναν νέο πίνακα στόχου, πατήστε το κουμπί Δημιουργία Νέου Πίνακα Στόχου. Για να χρησιμοποιήσετε έναν υπάρχοντα πίνακα ως πίνακα στόχου μπορείτε να σύρετε και να αποθέσετε έναν πίνακα από την αποθήκη στον καμβά.
#XTIT
defineTarget=Καθορισμός Πίνακα Στόχου
#XTIT
createViewTransform=Δημιουργία Μετασχηματισμού Προβολής
#XMSG: message shown in the properties panel of view transform when it is not defined.
createViewTransformInformation=Για να ξεκινήσετε τη ροή μετασχηματισμού σας, δημιουργήστε έναν μετασχηματισμό προβολής πατώντας στο σχετικό κουμπί.
#XMSG: message shown in the properties panel of view transform when it is not defined.
createViewTransformInformationDndSpark=Για να ξεκινήσετε τη ροή μετασχηματισμού σας, καθορίστε μία πηγή με την μέθοδο drag and drop σε έναν πίνακα πηγής από τον πίνακα αποθήκης ή πατώντας στο σχετικό κουμπί.
#XBUT: button in the properties panel of view transform
sqlViewTransform=Μετασχηματισμός Προβολής SQL
#XBUT: button in the properties panel of view transform
graphicalViewTransform=Μετασχηματισμός Προβολής με Γραφικά
#XFLD: view transform operator type - the value will be either SQL View or Graphical View
viewTransformType=Τύπος
#XMSG: value of type property
sqlView=Προβολή SQL
#XMSG: value of type property
graphicalView=Προβολή με Γραφικά

#~~~~~~~~~~~~Script properties text~~~~~~~~~~~~~~~~~~~~~
#XBTN: select all columns button text
selectAllColumns=Επιλογή Ολων
#XBUT: Add column menu button
addColumn=Προσθήκη Στήλης
#XBUT: Create new column menu button
createNewColumn=Δημιουργία Νέας Στήλης
#XBUT: Edit column menu button
editColumn=Επεξεργασία Στήλης
#XBUT: Delete button
deleteColumn=Διαγραφή
#XTIT: title for select columns dialog
columnSelectionDialogTitle=Επιλογή Στηλών
#XMSG: message shown in the select columns dialog
columnSelectionNoDataText=Δεν Βρέθηκαν Στήλες
#XFLD
columnOrigin=Προέλευση
#XFLD
columnLength=Μήκος
#XFLD
columnPrecision=Ακρίβεια
#XFLD
columnScale=Κλίμακα
#XBTN: save button text
save=Αποθήκευση
#XTOL: Tooltip text for more button
tooltipTxt=Περισσότερα
#XTOL: Tooltip text for new column
newColumn=Νέα Στήλη
#XTIT: title for the script editor
scriptTitle=Σενάριο
#XMSG: Message strip text in the script editor
messageStripText=Ανοίξτε την πύλη βοήθειας για να λάβετε πληροφορίες και πλήρη τεκμηρίωση.
#XMSG: Learn more link text
linkText=Γνωρίστε Περισσότερα
#XMSG: For a data type with length, the length cannot be greater than the maximum value.
VAL_ELEMENT_PROPERTY_LENGTH_GREATER_THAN_MAX=Η τιμή για το μήκος δεν μπορεί να είναι μεγαλύτερη από τη μέγιστη τιμή ({0}).
#XMSG: For a data type with length, the length cannot be negative.
VAL_ELEMENT_PROPERTY_LENGTH_LESS_THAN_ZERO=Η τιμή για το μήκος δεν μπορεί να είναι μικρότερη από 0.
#XMSG: For a data type with length, the length cannot be zero.
VAL_ELEMENT_PROPERTY_LENGTH_CANNOT_BE_ZERO=Η τιμή για το μήκος δεν μπορεί να είναι μικρότερη από {0}.
#XMSG: The attribute of an Entity has a data type that must have a length.
VAL_ELEMENT_DATATYPE_NO_LENGTH=Εισάγετε μία τιμή για μήκος.
#XMSG: The attribute of an Entity has a data type that must have precision.
VAL_ELEMENT_DATATYPE_NO_PRECISION=Εισάγετε μία τιμή για ακρίβεια
#XMSG: For a data type with precision, the precision cannot be greater than the maximum value.
VAL_ELEMENT_PROPERTY_PRECISION_GREATER_THAN_MAX=Η τιμή για την ακρίβεια δεν μπορεί να είναι μεγαλύτερη από {0}
#XMSG: For a data type with precision, the precision cannot be less than the minimum value.
VAL_ELEMENT_PROPERTY_PRECISION_LESS_THAN_MIN=Η τιμή για την ακρίβεια δεν μπορεί να είναι μικρότερη από {0}.
#XMSG: For a data type with precision and scale, the precision cannot be less than the scale.
VAL_ELEMENT_PROPERTY_PRECISION_LESS_THAN_SCALE=Η τιμή για την ακρίβεια δεν μπορεί να είναι μικρότερη από την κλίμακα {0}
#XMSG: The attribute of an Entity has a data type that must have scale.
VAL_ELEMENT_DATATYPE_NO_SCALE=Εισάγετε μία τιμή για κλίμακα.
#XMSG: For a numeric data type like Decimal(precision, scale), the scale cannot be negative.
VAL_ELEMENT_PROPERTY_SCALE_LESS_THAN_ZERO=Η τιμή για την κλίμακα δεν μπορεί να είναι μικρότερη από 0.
#XMSG: For a numeric data type like Decimal(precision, scale), the scale cannot be greater than the precision.
VAL_ELEMENT_PROPERTY_SCALE_GREATER_THAN_PREC=Η τιμή για την κλίμακα δεν μπορεί να είναι μεγαλύτερη από την ακρίβεια ({0}).
#~~~~~~~~~~~~~~~~~~~~~~~ Properties Panel texts ends here ~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Diagram texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XTOL: diagram tool item
zoomToFit=Εστίαση για Προσαρμογή
#XTOL: tooltip for context pad to open data preview (new text)
dataViewer=Προβολή Δεδομένων
#XTOL: tooltip for context pad to open data preview (old text)
previewData=Δεδομένα Προεπισκόπησης
#XTOL: tooltip for context pad to open impact and lineage editor
openImpactLineage=Ανάλυση Επιπτώσεων και Προέλευσης
#XTOL: tooltip for context pad to open object in new tab
openInNewTab=Ανοιγμα σε Νέα Καρτέλα
#XTOL: tooltip for context pad to create new target table
createTable=Δημιουργία Νέου Πίνακα
#XTOL: tooltip for context pad to edit view transform operator
edit=Επεξεργασία
#XTOL: tooltip for context pad to clear the source or target node in the diagram
remove=Διαγραφή
#XTOL: tooltip for context pad to define view transform operator using SQL editor
useSQLViewTransform=Μετασχηματισμός Προβολής SQL
#XTOL: tooltip for context pad to define view transform operator using Graphical View editor
useGraphicalViewTransform=Μετασχηματισμός Προβολής με Γραφικά
#XMSG: Error shared table cannot be used as target
unsupportedTargetSharedTable=Δεν μπορείτε να χρησιμοποιήσετε έναν κοινόχρηστο πίνακα ως τελικό πίνακα σε μία ροή μετασχηματισμού.
#XMSG: Error SAC artefact cannot be used as target
unsupportedSACTarget=Αδύνατη χρήση αντικειμένου ανάγνωσης μόνο ως στόχο για ροή μετασχηματισμού.
#XMSG: Error Open SQL Schema/HDI Container table cnnot be used as target
unsupportedTargetOpenSchema=Αδύνατη χρήση σχήματος βάσης δεδομένων ή HDI Container ως στόχο σε μία ροή μετασχηματισμού.
#XMSG: Message to show when view transform validation is in progress
vtValidationInProgress=Προβολή επαλήθευσης μετασχηματισμού σε εξέλιξη.
#XMSG: welcome screen message when the editor is new or empty
welcomeText=Ξεκινήστε πατώντας σε έναν κόμβο για να καθορίσετε έναν μετασχηματισμό προβολής ή προσθέστε/δημιουργήστε έναν πίνακα στόχου.
#XMSG: new welcome screen message when drag and drop of table is enabled (spark)
welcomeTextDndSpark=Ξεκινήστε πατώντας σε έναν κόμβο ή με τη μέθοδο drag and drop από τον πίνακα αποθήκης για προσθήκη πίνακα πηγής ή στόχου
#XBUT: confirmation button to switch graphical view source to sql view source in the warning dialog
confirmSQLViewTransform=Επιβεβαίωση
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Αναβάθμιση μισθωτή σε εξέλιξη.

#~~~~~~~~~~~~~~~~~~~~~~~ Diagram texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Change Management texts starts here ~~~~~~~~~~~~~~~~~~~
#XTIT: title for the change management dialog
changeManagementTitle=Ενημερώσεις Στόχου
#XTIT: title for the change management dialog
changeManagementTitleSourceOrTarget=Ενημερώσεις Πηγής/Στόχου
#XTIT
objectDisplayName=Εμφάνιση Ονόματος
#XTIT: name of the user who modifed the object
changedBy=Αλλαγμένο Από
#XTIT: the time when the object is modified
changedOn=Αλλαγή Στις
#XTIT
objectType=Τύπος
#XBUT
ok=ΟΚ
#XMSG: message in the change management dialog
reviewText=Ελέγξτε τις τροποποιήσεις. Θα πρέπει να αποθηκεύσετε κι έπειτα να αναπτύξετε ξανά τη ροή μετασχηματισμού για να ισχύουν οι αλλαγές.
#XMSG: message in the change management dialog
changesText=Ο παρακάτω πίνακας στόχου που χρησιμοποιείταισε αυτή τη ροή μετασχηματισμού τροποποιήθηκε (δείτε μηνύματα επαλήθευσης για λεπτομέρειες):
#XMSG: message in the change management dialog
changesTextSourceOrTarget=Ο παρακάτω πίνακας/πηγές στόχου που χρησιμοποιούνται σε αυτή τη ροή μετασχηματισμού τροποποιήθηκαν (δείτε μηνύματα επαλήθευσης για λεπτομέρειες):
#XMSG: Info column removed from the target: params: {0} label of the node
validationChangeMgmtDelColMsgHdr=Οι στήλες διαγράφηκαν από {0}.
#XMSG: Info column data type updated in the target: params: {0} label of the node
validationChangeMgmtUpdDTColMsgHdr=Ο τύπος δεδομένων των παρακάτω στηλών στο {0} άλλαξε.
#XMSG: Info column added to the target: params: {0} label of the node
validationChangeMgmtNewColMsgHdr=Οι στήλες προστέθηκαν στο {0}.
#NOTR: description text of message params: {0} list (with line breaks) of columns
validationChangeMgmtColsMsgDesc={0}
#XMSG: data type change information for the column params: {0} column name {1} old data type {2} new data type
validationChangeMgmtUpdDTColMsgDesc={0}, άλλαξε από {1} σε {2}.
#XMSG: title text of message, change of key/isKey: params: {0} number of columns
validationChangeMgmtUpdKeyColMsgHdr=Στήλες με κλειδιά καθορίστηκαν ή διαγράφηκαν ({0}).
#XMSG: key property of a column has been set
validationChangeMgmtKeyPropertySet=Το πεδίο είναι τώρα πεδίο κλειδί.
#XMSG: key property of a column has been removed
validationChangeMgmtKeyPropertyRemoved=Το πεδίο δεν είναι πια πεδίο κλειδί.
#XMSG: title text of message, change of business name: params: {0} number of columns
VAL_CHANGE_MGMT_UPD_BN_COL_MSG_HDR=Στήλες με ενημερωμένη επωνυμία επιχείρησης ({0}).
#XMSG: generic message to fill description of message with a list of column names: params: {0} list (with line breaks) of columns
VAL_CHANGE_MGMT_COLUMNS_MSG_DESC={0}
#XMSG: displayed next to a column name to indicate whether the value is new (maintained for the first time). Example: The business name of a column got maintained and before only the technical name was in place
VAL_VALUE_NEW=(νέο)
#XMSG: displayed next to a column name to indicate whether the value has been resetted/deleted. Example: The business name of a column or source has been removed
VAL_VALUE_DELETED=(διαγραμμένο)
#XFLD: Label for source type node change management
source=Πηγή
#XFLD: Label for target type node change management
target=Στόχος
#XFLD: Tooltip label for view icon
view=Προβολή
#~~~~~~~~~~~~~~~~~~~~~~~ Change Management texts ends here ~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary Editor texts starts here ~~~~~~~~~~~~~~~~~~~~

#XTIT: title of the secondry diagram editor (graphical view editor)
graphicalViewEditor=Επεξεργαστής Προβολής με Γραφικά
#XTIT: title of the secondry diagram editor (sql view editor)
sqlViewEditor=Επεξεργαστής Προβολής SQL
#XTIT: title of the panel in Entity/Source properties panel
deltaSettings=Ρυθμίσεις Delta
#XFLD: decides which object of delta table to be used(Delta Capture or All Active Records)
LoadFromLocalTable=Φόρτωση από Τοπικό Πίνακα
#XFLD: decides which object of delta table to be used(Delta Capture or All Active Records)
LoadFromTable=Φόρτωση από Πίνακα
#XRBL: only delta records will be processed when this option is selected
deltaCapture=Λήψη Delta
#XRBL: all the records will be processed when this option is selected
allActiveRecords=Ολες οι Ενεργές Εγγραφές
#XMSG: Error multiple delta source exist with Delta Capture selected
validationMultipleDeltaSource=Μόνο ένας αρχικός πίνακας μπορεί να έχει τιμή Delta Capture για την επιλογή Φόρτωση Από Τοπικό Πίνακα.
#XMSG: Error Open SQL Schema/HDI Container table cannot be used as a source: params: {0} technical name of the object
validationOpenSchemaSource=Αδύνατη χρήση σχήματος βάσης δεδομένων ή πίνακα HDI Container "{0}" ως πηγή σε μία ροή μετασχηματισμού.
#XMSG: Error remote table cannot be used as a source :params: {0} technical name of the object
validationRemoteTableSource=Αδύνατη χρήση απομακρυσμένου πίνακα "{0}" ως πηγή σε μία ροή μετασχηματισμού.
#XMSG: Error view cannot be used as a source :params: {0} technical name of the object
validationViewSource=Αδύνατη χρήση προβολής "{0}" ως πηγή σε μία ροή μετασχηματισμού.
#XMSG: Error Aggregation node cannot be used with delta source(source with Delta Capture selected)
validationAggregationWithDelta=Ο χειριστής συνόλου δεν μπορεί να χρησιμοποιηθεί αν ένας αρχικός πίνακας έχει την τιμή Delta Capture για την επιλογή Φόρτωση Από Τοπικό Πίνακα.
#XMSG: Error Left Outer Join with Delta Table as Outer Operand
validationErrorLeftOuterJoinWithDeltaTable=Αδύνατη χρήση πίνακα πηγής για τον οποίο η ρύθμιση λήψης delta είναι ενεργή ως εξωτερικός τελεστής ενός  LEFT-OUTER-JOIN.
#XMSG: Info: Outer Join with Delta Table as Outer Operand
validationErrorOuterJoinWithDeltaTable=Η χρήση πίνακα πηγών ενεργοποιημένων από Delta ως εξωτερικού τελεστή της αριστερής ή δεξιάς σύνδεσης μπορεί να οδηγήσει σε σφάλμα κατά την εκτέλεση.
#XMSG: Error Union with Delta Source
validationErrorUnionWithDeltaSource=Ο τελεστής ένωσης δεν μπορεί να χρησιμοποιηθεί αν κάποιος πίνακας πηγής έχει τιμή Λήψη Delta για την επιλογή Φόρτωση από Πίνακα.
#XMSG: Error Timestap column should not be used in any calculated column
validationForCalculatedColumnWithDelta=Η στήλη {0} δεν μπορεί να χρησιμοποιηθεί σε υπολογισμένη στήλη {1}.
#XMSG: Warning
invalidSQLViewDefinitionConfirmation=Αδύνατη επαλήθευση δήλωσης SQL.
#XMSG: Warning SQL statment contains validation error
sqlErrorConfirmation=Η δήλωση SQL περιέχει σφάλματα επαλήθευσης.
#XMSG: Error multiple delta source exist in the sql statement
validationSQLEditorMultipleDeltaSource=Πολλές πηγές delta υπάρχουν στη δήλωση SQL. Μόνο ένας πίνακας delta μπορεί να χρησιμοποιηθεί ως πηγή.
#XBTN: delete button on parameters panel
deleteParameters=Διαγραφή
#XMSG: loading message shown in busy dialog when parameter is appended in sql statement
loadingParameters=Φόρτωση παραμέτρων.
#XMSG: Open SQl schema source using one remote source directly or indirectly
validationOpenSchemaRemoteSource=Το αντικείμενο "{0}" αναφέρεται στον απομακρυσμένο πίνακα "{1}" (είτε άμεσα ή έμμεσα).
#XMSG: Open SQl schema source using multiple remote sources directly or indirectly
validationOpenSchemaRemoteSources=Το αντικείμενο "{0}" αναφέρεται στους παρακάτω απομακρυσμένους πίνακες (είτε άμεσα ή έμμεσα): {1}
#XMSG: Open SQL schema source DAC validation
validationOpenSchemaDAC=Αδύνατη προσθήκη αντικειμένου "{0}". Ενας έλεγχος πρόσβασης δεδομένων εφαρμόστηκε σε αυτό το αντικείμενο (ή εξαρτημένο αντικείμενο).

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary Editor texts ends here ~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Dialog Boxes texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error new editor is opened and the object is not saved and Run button is clicked
runUnsavedError=Πρέπει να αποθηκεύσετε τη ροή μετασχηματισμού πριν την εκτελέσετε.
#XTIT
runError=Σφάλμα
#XMSG: Error unsaved changes in the editor and Run button is clicked
runModifiedError=Υπάρχουν μη αποθηκευμένες αλλαγές στη ροή μετασχηματισμού. Αποθηκεύστε τη ροή μετασχηματισμού.
#XTIT
runWarning=Προειδοποίηση
#XMSG: Warning the object has changes to deploy and Run button is clicked
runDeployedVersion=Η τελευταία έκδοση ροής μετασχηματισμού δεν αναπτύχθηκε ακόμα. Η τελευταία αναπτυγμένη έκδοση της ροής μετασχηματισμού θα εκτελεστεί. Θέλετε να συνεχίσετε;
#XBTN: run with error or warning
runAnyway=Εκτέλεση Ροής Μετασχηματισμού
#XBTN
close=Κλείσιμο
#XMSG: Error object has validation error
runWithValidationErrors=Η ροή μετασχηματισμού έχει σφάλματα επαλήθευσης. Αυτό ίσως δημιουργήσει προβλήματα στην εκτέλεση.
#XTIT
waitBusy=Περιμένετε.
#XMSG: initiating transformation flow run
runBusy=Προετοιμασία δεδομένων...
#XMSG: Success
runSuccess=Εκτέλεση ροής μετασχηματισμού άρχισε
#XMSG: Error
runFail=Αδύνατη εκτέλεση ροής μετασχηματισμού
#XTIT: loading dialog title
loading=Φόρτωση
#XMSG: fetching run details from the server
loaderDetails=Προσκόμιση λεπτομερειών από τον διακομιστή
#XMSG: Error graphical view transform with error cannot be converted to SQL view transform
convertGVTransformToSQLViewTransformError=Αδύνατη μετατροπή Μετασχηματισμού Προβολής με Γραφικά με σφάλματα στον Μετασχηματισμό Προβολής SQL.
#~~~~~~~~~~~~~~~~~~~~~~~ Dialog Boxes texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Validation texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error Duplicate technical name validation message : params: {0} technical name of the object
validationDuplicateTechnicalName=Αντικείμενο ''{0}'' υπάρχει ήδη στην αποθήκη.
#XMSG: Error There should be a technical name for the target table.
validationEmptyTechnicalTargetTable=Εισάγετε ένα τεχνικό όνομα για τον τελικό πίνακα.
#XMSG: Error View transform operator not defined(template node is shown in the diagram)
validationEmptyViewTransform=Ενας χειριστής μίας Προβολής Μετασχηματισμού δεν καθορίστηκε για τη ροή μετασχηματισμού.
#XMSG: Error source operator not defined(template node is shown in the diagram) in large system spaces
validationEmptySource=Ένας χειριστής Πηγής δεν καθορίστηκε για τη ροή μετασχηματισμού.
#XMSG: Error Target table not defined (template node is shown in the diagram)
validationEmptyTargetTable=Η ροή μετασχηματισμού δεν περιέχει τελικό πίνακα.
#XMSG: Error validation message for incompatible data type mapping in target : params: {0} source column name {1} source column data type {2} target column name {3} target column data type {4} label of the target node
validationTargetColumnMappingIncompatible={0} ({1}) είναι ασύμβατο με {2} ({3}) σε χειριστή {4}.
#XMSG: Error validation message for incompatible mapping for timestamp column :  params: {0} & {1} and compatible source and column names
validationSouceTargetColumnMappingIncompatibleTimestamp=Η στήλη {0} μπορεί να αντιστοιχιστεί μόνο στη στήλη {1} στον τελικό πίνακα.
#XMSG: Error No mappping defined in the target operator : params: {0} label of the target node
validationTargetMappingMissing=Δεν καθορίστηκε αντιστοίχιση σε χειριστή ''{0}''.
#XMSG: Error No mappping defined in the target operator : params: {0} name of the primary key column {1} label of the target node
validationTargetMappingMissingForPrimaryKeyColumn=Δεν καθορίστηκε αντιστοίχιση για στήλη κωδικού "{0}" στον χειριστή {1}.
#XMSG: warning: No mappping defined for change Type Column
validationTargetMappingMissingForChangeTypeColumn=Το πεδίο πηγής {0} δεν αντιστοιχίστηκε στο πεδίο στόχου {0} .
#XMSG: Info: For change Date Column, The timestamp of this field will be set to the time that the transformation flow runs
validationTargetMappingMissingForChangeDateColumn=Το πεδίο {0} δεν είναι σχετικό με αντιστοίχιση. Η χρονική ένδειξη του πεδίου θα καθοριστεί στον χρόνο που εκτελείται η ροή μετασχηματισμού.
#XMSG: Error No columns defined in the target operator : params: {0} label of the target node
validationEmptyColumns=Δεν καθορίστηκαν στήλες για τον χειριστή {0}.
#XMSG: Error load type "Initial and Delta" not supported when the truncate mode is enbled in the target table properties
validationTruncateInvalidMode=Ο τύπος φόρτωσης Αρχικό και Delta δεν υποστηρίζεται αν η επιλογή Περικοπή είναι ενεργή για τον τελικό πίνακα.
#XMSG: Error load type "Initial and Delta" not supported when the deleteAllBeforeLoading mode is enbled in the target table properties
validationDeleteAllBeforeLoadingInvalidMode=Ο τύπος φόρτωσης Αρχικό και Delta δεν υποστηρίζεται αν η επιλογή Περικοπή είναι ενεργή για τον τελικό πίνακα.
#XMSG: Error Object has duplicated technical name. : params: {0} technical name of the object
validationDuplicateTargetTechnicalName=Το αντικείμενο ''{0}'' υπάρχει ήδη στην αποθήκη. Εισάγετε άλλο όνομα.
#XMSG: Error technical name field is empty
validationEmptyTechnicalName=Εισάγετε ένα τεχνικό όνομα για τον πίνακα.
#XMSG: Error business name field is empty
validationEmptyBusinessName=Εισάγετε μία επωνυμία επιχείρησης για τον πίνακα.
#XMSG: Error message for data type mismatch in target : params: {0} source column name {1} source column data type {2} target column name {3} target column data type
datatypeMismatchTarget=Ο τύπος δεδομένων πρέπει να συμφωνεί για την δημιουργία αντιστοίχισης. {0} ({1}) δεν συμφωνεί με {2} ({3}).
#XMSG: Error load type "Initial and Delta" not supported for non-delta source
validationNonDeltaSource=Ο τύπος φόρτωσης Αρχικό και Delta δεν υποστηρίζεται αν η πηγή δεν επιτρέπει delta.
#XMSG: Error load type "Initial and Delta" not supported for non-delta target
validationNonDeltaTarget=Ο τύπος φόρτωσης Αρχικό και Delta δεν υποστηρίζεται αν ο τελικός πίνακας δεν επιτρέπει delta.
#XMSG: Error truncate mode is not supported for delta-enabled target table
validationTruncateDeltaTarget=Η λειτουργία αποκοπής δεν υποστηρίζεται για πίνακα στόχου delta.
#XMSG: Info Message for upsert of records when target node has truncate off and a key column
validationTruncateOffandKeyColumn=Οι διαβασμένες εγγραφές με χρήση του χειριστή μετασχηματισμού προβολής φορτώθηκαν στον τελικό πίνακα με τη λειτουργία UPSERT.
#XMSG: Error deleteAllBeforeLoading mode is not supported for delta-enabled target table
validationDeleteAllBeforeLoadingDeltaTarget=Η λειτουργία Διαγραφή Όλων πριν από την Φόρτωση δεν υποστηρίζεται για πίνακα στόχου delta.
#XMSG: Info Message for upsert of records when target node has deleteAllBeforeLoading off and a key column
validationDeleteAllBeforeLoadingOffandKeyColumn=Οι διαβασμένες εγγραφές με χρήση του χειριστή μετασχηματισμού προβολής φορτώθηκαν στον τελικό πίνακα με τη λειτουργία UPSERT.
#XMSG: delta-enabled source cannot be used with non-delta target : params: {0} label of the target node
validationDeltaSourceNonDeltaTarget=Αδύνατη χρήση πηγής Delta με μη ενεργό στόχο delta "{0}".
#XMSG: Error graphicalview transform has a validation error.
validationGraphicalViewTransformHasError=Ένα ή περισσότερα σφάλματα υπάρχουν για τον Μετασχηματισμό Προβολής με Γραφικά. Για να εμφανίσετε τα σφάλματα επεξεργαστείτε τον Μετασχηματισμό και πατήστε Μηνύματα Επαλήθευσης.
#XMSG: Error sql view transform has a validation error.
validationSQLViewTransformHasError=Ένα ή περισσότερα σφάλματα υπάρχουν για τον Μετασχηματισμό Προβολής SQL. Για να εμφανίσετε τα σφάλματα επεξεργαστείτε τον Μετασχηματισμό και πατήστε Μηνύματα Επαλήθευσης.
#XMSG: Error graphical view transform has a change management error.
validationGraphicalViewTransformHasChangeManagement=Ένας ή περισσότεροι πηγαίοι πίνακες άλλαξαν. Για να εμφανίσετε αυτές τις αλλαγές, επεξεργαστείτε τον Μετασχηματισμό Προβολής με Γραφικά και πατήστε Μηνύματα Επαλήθευσης.
#XMSG: Error sql view transform has a change management error.
validationSQLViewTransformHasChangeManagement=Ένας ή περισσότεροι πηγαίοι πίνακες άλλαξαν. Για να εμφανίσετε αυτές τις αλλαγές, επεξεργαστείτε τον Μετασχηματισμό Προβολής SQL και πατήστε Μηνύματα Επαλήθευσης.
#XMSG: Info load type set to intial and delta as source and traget are delta tables
validationInitialDeltaLoadType=Καθώς το χαρακτηριστικό λήψης delta είναι ενεργό για αμφότερους τους αρχικούς και τελικούς πίνακες, ο προεπιλεγμένος τύπος φόρτωσης είναι αρχικός και Delta.
#XMSG: Info reset target table watermark
validationDeltaTargetTableChanged=Ο τελικός πίνακας άλλαξε. Μόνο τα δεδομένα delta θα μεταφερθούν στον νέο τελικό πίνακα. Αν θέλετε να μεταφέρετε όλα τα δεδομένα στον τελικό πίνακα, μπορείτε να επανακαθορίσετε το υδατογράφημα στην Οθόνη Ενοποίησης Δεδομένων.
#XMSG: Error delta table from BW bridge cannot be used in SQL Script view transform
validationBWDeltaSourceInSQLScriptTransform=Για να φορτώσετε αλλαγές delta από έναν απομακρυσμένο πίνακα που βρίσκεται σε έναν χώρο BW Bridge χρησιμοποιώντας Μετασχηματισμό Προβολής SQL, η γλώσσα πρέπει να είναι SQL (Βασικό Ερώτημα). SQLScript (Λειτουργία Πίνακα) δεν υποστηρίζεται.
#XMSG: No primary key column in new delta target
validationNoKeyColumn=Για να ενεργοποιήσετε την ρύθμιση λήψης delta, ο πίνακας στόχου πρέπει να περιέχει τουλάχιστον μία στήλη κωδικού.
#XMSG: Duplicate column names in new delta target
validationColumnNameNotUnique=Τα ονόματα στήλης πρέπει να είναι μοναδικά. {0} έχει πολλές εμφανίσεις στον  πίνακα "{1}".
#XMSG: Missing CDC Columns in new delta target
validationMissingCDCColumns=Η ρύθμιση λήψης  delta είναι ενεργή για τον πίνακα {0}, αλλά λείπουν οι στήλες λήψης  delta.
#XMSG: Validate non LTF target in large system space
validationLTFDeltaTargetLargeSystemSpace=Ο πίνακας στόχου "{0}" πρέπει να υποστηρίζει delta με αποθήκευση αρχείου σε μεγάλο χώρο συστήματος. 
#XMSG: Validation error if a transformation flow from hana runtime space is imported into large system space
validationNonLsaTFInLsaSpace=Ροή Μετασχηματισμού που δημιουργήθηκε με χρόνο εκτέλεσης ΗΑΝΑ δεν μπορεί να χρησιμοποιηθεί σε μεγάλο χώρο συστήματος.
#XMSG: Always show a validation info message in the python node to inform the user to update the python script manually to generate the data to match the output columns.
validationUpdatePythonScript=Ενημέρωση σχήματος στήλης εξόδου ώστε να ταιριάζει με τις στήλες που εμφανίζονται στο DataFrame του σεναρίου Python.
#XMSG: Error message for the python operator for changed CDC columns
validationChangedCDCColumns=Οι στήλες λήψης delta είτε λείπουν ή τροποποιήθηκαν στο σενάριο Python.
#XMSG: Validation error if a transformation flow from spark runtime space is imported into non large system space
validationLsaTFInNonLsaSpace=Μία ροή μετασχηματισμού με χρόνο εκτέλεσης spark δεν μπορεί να δημιουργηθεί σε έναν χώρο με την αποθήκευση SAP HANA Database (Δίσκος και Στη Μνήμη).
#~~~~~~~~~~~~~~~~~~~~~~~ Validation texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Data Preview texts starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error data preview is not available for selected operator
dataPreviewNotSupp=Προεπισκόπηση δεδομένων μη διαθέσιμη για αυτόν τον χειριστή.
#XMSG: Error data preview is not available as the selected node is a template.
dataPreviewTemplate=Προεπισκόπηση δεδομένων μη διαθέσιμη για αυτόν τον κόμβο.
#XMSG: Error data preview is not available as the selected node is a new target table. i.e. the target table is not created
dataPreviewNewTarget=Προεπισκόπηση δεδομένων μη διαθέσιμη για αυτόν τον τελικό πίνακα.
#XMSG: Error data preview is not avalibale as there are no columns in the selected node
dataPreviewEmptyColumns=Προεπισκόπηση δεδομένων μη διαθέσιμη γιατί ο πίνακας δεν έχει ορατές στήλες.
#XMSG Error insuffcient privileges to view data
dataPreviewInSufficientPrivileges=Δεν έχετε επαρκείς άδειες για προβολή αυτών των δεδομένων
#XMSG Not suffcient privileges to view data
dataPreviewNoPrivileges=Δεν επιτρέπεται προβολή δεδομένων.
#XTIT Data preview problems tab
txtProblems=Λάθη
#XMSG: Error data preview is not available for SQL Script
dataPreviewNotSupportedSQLScript=Η λειτουργία προεπισκόπησης δεδομένων δεν είναι διαθέσιμη για το  SQLScript (Λειτουργία Πίνακα).
#XMSG: Error message for data preview with Cross space object with Input parameters
dataPreviewNotSupportedRemoteTableWithParam=Αδύνατη προεπισκόπηση δεδομένων:\nΠροεπισκόπηση δεδομένων για Μετασχηματισμό Προβολής με αντικείμενα πολλών χώρων που περιέχουν Παραμέτρους εισόδου δεν υποστηρίζεται.
#~~~~~~~~~~~~~~~~~~~~~~~ Data Preview texts ends here~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG:view transform is modified and changes are applied to the editor
viewTransformChangesUpdated=Όλες οι αλλαγές στην προβολή μετασχηματισμού εφαρμόστηκαν.
#XMSG:confirm switch from graphical view to sql view in the view transform
convertToSQLViewConfirmation=Αλλάζετε τον τύπο μετασχηματισμού προβολής στον μετασχηματισμό προβολής SQL. Αυτή η αλλαγή δεν ανακαλείται.
#XTIT Save Dialog param
modelNameTransformationFlow=Ροή Μετασχηματισμού
#XMSG: Info automap operation cannot be performed due non matching columns
autoMapInvalid=Ο τύπος δεδομένων και ονόματος των στηλών πρέπει να συμφωνεί με την αυτόματη αντιστοίχιση.
#XMSG Info: mapping already exists
mappingExists=Αντιστοίχιση υπάρχει ήδη.
#XMSG: There are invalid columns in the target table
invalidColumns=Οι παρακάτω στήλες δεν μπορούν να αντιστοιχιστούν και επομένως θα διαγραφούν.
#XFLD: Label for package select
package=Πακέτο

#XMSG warning for repository package assignment changed for Object name {1}
VAL_PACKAGE_CHANGED=Αντιστοιχίσατε αυτό το αντικείμενο στο πακέτο {1}. Πατήστε Αποθήκευση για να επιβεβαιώσετε και να επαληθεύσετε αυτή την αλλαγή. Η αντιστοίχιση σε ένα πακέτο δεν ανακαλείται σε αυτό τον επεξεργαστή μετά την αποθήκευση.
#XMSG warning for repository package dependency issue for object name{0}
MISSING_DEPENDENCY=Οι εξαρτήσεις του αντικειμένου ''{0}'' δεν μπορούν να επιλυθούν στο πλαίσιο του πακέτου {1}.
#XFLD: Mapped to parameter name{0}
mappedTo=Αντιστοιχισμένο σε: {0}
#XFLD: Value parameter default Value{0}
defaultVal=Τιμή: {0}
#XFLD: Not Mapped
notMapped=Μη Αντιστοιχισμένο
#XMSG : Validation message for views with remote table name {1} for view name {0}
validateViewWithRemoteTables=Η προβολή "{0}" αναφέρεται στους παρακάτω απομακρυσμένους πίνακες (είτε άμεσα ή έμμεσα): "{1}"
#XMSG : Validation message for views with DAC for view name {0}
validationDAC=Αδύνατη προσθήκη προβολής "{0}". Ενας έλεγχος πρόσβασης δεδομένων εφαρμόστηκε σε αυτή την  προβολή (ή εξαρτημένη προβολή).
#XMSG : Validation message for objects with remote tables, remote table name{0} in sql editor,
validateSqlRemoteTables=Η δήλωση SQL περιέχει ένα ή περισσότερα αντικείμενα που αναφέρονται στους παρακάτω απομακρυσμένους πίνακες (είτε άμεσα ή έμμεσα): "{0}"
#XBTN : Set Value
setValue=Καθορισμός Τιμής
#XBTN : Map TO
mapTo=Αντιστοίχιση σε
#XMSG : This source name{0} contains input parameters.
parameterMappingInfo=Αυτή η πηγή {0} περιέχει παραμέτρους εισόδου. Για κάθε παράμετρο, μπορείτε είτε να καθορίσετε μία τιμή ή να την αντιστοιχίσετε σε μία παράμετρο εισόδου στην προβολή σας.
#XBTN : Cancel
cancel=Ακύρωση
#XTIT : Set Value for Input Parameter name {0}
setValueForParam=Καθορισμός τιμής για Παράμετρο Εισόδου {0}
#XMSG : Value
enterValue=Αξία
#XMSG : Enter Valid Decimal value with valid precision and scale
VAL_ENTER_VALID_DECIMAL_GEN=Εισάγετε μία έγκυρη δεκαδική τιμή με ακρίβεια {0} και κλίμακα {1}.
#XFLD : Runtime
runtime=Χρόνος Εκτέλεσης
#XFLD: Storage
storage=Αποθήκευση
#XMSG: Info message for skipping SQL validation when tenant upgrade is in progress
VAL_TENANT_UPGRADE_IN_PROGRESS=Μετασχηματισμός Προβολής SQL παραβλέφθηκε γιατί η αναβάθμιση μισθωτή ήταν σε εξέλιξη. Επαληθεύστε τον Μετασχηματισμό Προβολής SQL πριν αναπτύξετε τη ροή μετασχηματισμού.
#XMSG: Error LTF cannot be used as a source :params: {0} technical name of the object
validationLTFSource=Αδύνατη χρήση τοπικού πίνακα (αρχείο) "{0}" ως πηγή σε μία ροή μετασχηματισμού (spark).
#XMSG: Error shared tables cannot be used as a source :params: {0} technical name of the object
validationSharedSource=Αδύνατη χρήση κοινόχρηστου πίνακα (αρχείο) ) "{0}" ως πηγή σε μία ροή μετασχηματισμού (spark).
#XMSG: Error Only LTF are supported in large system space
validationNonLTFSource=Αδύνατη χρήση πηγής "{0}" σε ροή μετασχηματισμού (spark) γιατί δεν είναι τοπικός πίνακα (αρχείο).
#~~~~~~~~~~~~~~~~~~~~~~~ Python Operator texts starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XTOL: Tooltip for python operator in context pad
pythonOperator=Python Operator

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary sources properties starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Show Sources
showSources=Εμφάνιση Πηγών:
#XFLD: Qualified Name
qualifiedName=Προσδιορισμένο Όνομα
#XFLD: Space Name
spaceName=Όνομα Χώρου
#XFLD: Context Name
contextName=Όνομα Γενικού Πλαισίου
#XFLD: Connection
connection=Σύνδεση
#XFLD: Remote Table
remoteTable=Απομακρυσμένος Πίνακας
#XFLD: Active Records
activeRecords=Ενεργές Εγγραφές
#XFLD: Local Table
localTable=Τοπικός Πίνακας
#XFLD: Local Schema
localSchema=Τοπικό Σχήμα
#XMSG: no parameters message
noParameters=Δεν υπάρχουν παράμετροι
#XMSG: Validation Error in secondary source error message
secondarySourceErrorDialog=Οι πηγές μετασχηματισμού προβολής δεν μπορούν να εμφανιστούν γιατί υπάρχει τουλάχιστον ένα σφάλμα στον Μετασχηματισμό Πηγών SQL.
#XMSG: Unable to load sources error message
secondarySourceLoadingErrorDialog=Οι πηγές μετασχηματισμού προβολής δεν μπορούν να φορτωθούν.
#XMSG: No sources error message
noSecondarySourceErrorDialog=Ο μετασχηματισμός προβολής δεν έχει πηγή.
#XTIT Local Table (File)
LTFTable=Τοπικός Πίνακας (Αρχείο)
#XMSG: Error data preview is not available for undeployed SQLScript Table function
dataPreviewNotDeployWarning=Τα δεδομένα για τον μετασχηματισμό της προβολής σας δεν εμφανίζονται εδώ μέχρι να αναπτυχθούν όλες οι αλλαγές. Αναπτύξτε την ροή μετασχηματισμού και δοκιμάστε πάλι.
#Input parameters
#XFLD : Input Parameters
parametersTitle=Παράμετροι Εισόδου ({0})
#XMSG: Error message for empty input parameters
noInputParameters=Δεν υπάρχουν προσωρινά διαθέσιμες παράμετροι. Για να προσθέσετε ορισμένες, πατήστε το κουμπί Επεξεργασία.
#XTOL: Tooltip for edit input parameters
parametersTooltip=Επεξεργασία παραμέτρων εισόδου
#XMSG: edit input parameters
editInputParameters=Επεξεργασία παραμέτρων εισόδου
#XMSG: Create input parameters in the object to be used in inner filters
parametersDescription=Δημιουργία παραμέτρων εισόδου στο αντικείμενο που θα χρησιμοποιηθεί στα εσωτερικά φίλτρα.
#XBUT: Add new parameter
addParameters=Προσθήκη νέας παραμέτρου
#XBUT: Delete parameter
deleteParameter=Διαγραφή παραμέτρου
#XFLD: Data Type
parameterDatatype=Τύπος Δεδομένων
#XFLD: Name
inputParameterName=Όνομα
#XFLD: Placeholder for string input type
placeholderText=Εισαγωγή προεπιλεγμένης τιμής
#XFLD: Default Value
defaultValue=Προτεινόμενη Αξία
#XFLD defined by
definedBy=Ορίστηκε από
#XMSG Error message for empty input parameter name
VAL_PARAM_EMPTY_NM=Το όνομα παραμέτρου είναι κενό.
#XMSG Error message for parameter name not unique
VAL_PARAM_NAME_NOT_UNIQUE=Το όνομα παραμέτρου δεν είναι μοναδικό.
#XMSG: Error message when object is not deployed yet
cannotViewNotDeployed=Το αντικείμενο ''{0}'' δεν αναπτύχθηκε ποτέ. Αναπτύξτε το πριν το προβάλλετε.
#XFLD: Placeholder for string input type
stringPlaceholderText=Εισάγετε αλφαριθμητικό
#XFLD: Placeholder for integer input type
intPlaceholderText=Εισάγετε αριθμό
#XFLD: Placeholder for decimal input type
decPlaceholderText=Εισάγετε μία δεκαδική τιμή
#NOTR: Placeholder for date format Filter
dateFormatFilter=YYYY-MM-DD
#NOTR: Placeholder for time format Filter
timeFormatFilter=HH:mm:ss
#NOTR: Placeholder for date and time format Filter
dateTimeFormatFilter=YYYY-MM-DD, HH:mm:ss
#XTXT: Text for true dropdown of boolean datatype for input parameter - the value should be in small case matching the actual data
param_true=σωστό
#XTXT: Text for false dropdown of boolean datatype for input parameter - the value should be in small case matching the actual data
param_false=λάθος
#XTXT: Text for None dropdown of boolean datatype for input parameter
none=Κανένα
#XFLD: Input Parameter Name
OperatorName=Όνομα
#XFLD: Input Parameter Value
Value=Αξία
#XBTN: run button of run with parameter dialog
TXT_RUN=Εκτέλεση
#XFLD
InputParameters=Παράμετροι Εισόδου
#XTXT: Text for dialog box for input parameter
EnterParameterValues=Εισάγετε τιμές Παραμέτρου για να εκτελεστεί η ροή μετασχηματισμού
#XMSG: Error message for invalid number input
INVALID_NUMBER=Εισαγάγετε έναν έγκυρο αριθμό.
#XMSG: Error message for invalid decimal input
INVALID_DECIMAL=Πληκτρολογήστε έναν έγκυρο δεκαδικό αριθμό.
#XMSG: Error message for invalid date input
INVALID_DATE=Εισάγετε έγκυρη ημερομηνία (ΕΕΕΕ-ΜΜ-ΗΗ).
#XMSG: Error message for invalid time input
INVALID_TIME=Εισαγάγετε μια έγκυρη ώρα (ΩΩ:λλ:δδ)
#XMSG: Error message for invalid date and time input
INVALID_DATETIME=Εισάγετε έγκυρη ημερομηνία και ώρα (ΕΕΕΕ-ΜΜ-ΗΗ, ΩΩ:λλ:δδ).
#XMSG: Error message for invalid Boolean input
INVALID_BOOLEAN=Εισάγετε έγκυρη τιμή Boolean.
#XMSG: Error message for invalid generic input
INVALID_INPUT=Ακυρη είσοδος.
#XTIP: Tooltip for parameter value
more=Περισσότερα
#XTIT
mappingDialogTitle=Αντιστοίχιση Παραμέτρων Εισόδου
