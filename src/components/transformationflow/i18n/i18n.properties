#~~~~~~~~~~~~~~~~~~~~~~~ Properties Panel texts starts here ~~~~~~~~~~~~~~~~~~~~
#XTIT
properties=Properties
#XTIT
general=General
#XFLD
businessName=Business Name
#XFLD
technicalName=Technical Name
#XFLD
loadType=Load Type
#XFLD
deltaCaptureTableName=Delta Capture Table Name
#XSEL
initialOnly=Initial Only
#XSEL
initialAndDelta=Initial and Delta
#XCKL
truncate=Truncate
#XCKL
deleteAllBeforeLoading=Delete All Before Loading
#XTIT
columns=Columns
#XTIT
mappings=Mappings
#XFLD
columnDataType=Data Type
#XFLD
search=Search
#XBUT
autoMap=Auto Map
#XBUT
removeAllMappings=Remove all Mappings
#XMSG
noTargetInputs=You need to define a View Transform operator in order to view mapping information.
#XMSG
noColumnsData=There are no columns to display.
#XTOL
@validateModel=Validation Messages
#XTOL
@hierarchy=Hierarchy
#XTOL
@columnCount=Number of Columns
#XTOL
info=Info
#XTOL
cdcColumn=CDC Column
#XTIT
statusPanel=Run Status
#XTOL
schedule=Schedule
#XTOL
navToMonitoring=Open in Transformation Flow Monitor
#XBTN
createSchedule=Create Schedule
#XBTN
editSchedule=Edit Schedule
#XBTN
deleteSchedule=Delete Schedule
#XFLD
lastRun=Last Run
#XFLD
status=Status
#XMSG: Error run status cannot be fetched
errorDetails=Unable to retrieve status of run.
#XLNK
viewDetails=View Details
#XMSG: Error data is not loading from the server
backendError=It looks like the data isn’t loading from the server at the moment. Try again later.
#XMSG: Error transformation flow run failed. open data integration monitoring for more details
viewDetailsFallback=The transformation flow run failed to start. Navigate to the Monitoring application for more details.
#XMSG
statusCompleted=Completed
#XMSG
statusRunning=Running
#XMSG
statusFailed=Failed
#XMSG
statusNotExecuted=Not Run Yet
#XBTN
editColumns=Edit Columns
#XBUT: button in the properties panel of the target table
createNewTargetTable=Create New Target Table
#XMSG: message shown in the properties panel of target table when it is not defined.
defineTargetInformation=To create a new target table, click the Create New Target Table button below. To use an existing table as the target table, you can drag and drop a table from the repository to the canvas.
#XTIT
defineTarget=Define Target Table
#XTIT
createViewTransform=Create a View Transform
#XMSG: message shown in the properties panel of view transform when it is not defined.
createViewTransformInformation=To start your transformation flow, create a view transform by clicking the relevant button below.
#XMSG: message shown in the properties panel of view transform when it is not defined.
createViewTransformInformationDndSpark=To start your transformation flow, define a source by dragging and dropping a source table from the repository panel or clicking the relevant button below.
#XBUT: button in the properties panel of view transform
sqlViewTransform=SQL View Transform
#XBUT: button in the properties panel of view transform
graphicalViewTransform=Graphical View Transform
#XFLD: view transform operator type - the value will be either SQL View or Graphical View
viewTransformType=Type
#XMSG: value of type property
sqlView=SQL View
#XMSG: value of type property
graphicalView=Graphical View

#~~~~~~~~~~~~Script properties text~~~~~~~~~~~~~~~~~~~~~
#XBTN: select all columns button text
selectAllColumns=Select All
#XBUT: Add column menu button
addColumn=Add Column
#XBUT: Create new column menu button
createNewColumn=Create New Column
#XBUT: Edit column menu button
editColumn=Edit Column
#XBUT: Delete button
deleteColumn=Delete
#XTIT: title for select columns dialog
columnSelectionDialogTitle=Select Columns
#XMSG: message shown in the select columns dialog
columnSelectionNoDataText=No Columns Found
#XFLD
columnOrigin=Origin
#XFLD
columnLength=Length
#XFLD
columnPrecision=Precision
#XFLD
columnScale=Scale
#XBTN: save button text
save=Save
#XTOL: Tooltip text for more button
tooltipTxt=More
#XTOL: Tooltip text for new column
newColumn=New Column
#XTIT: title for the script editor
scriptTitle=Script
#XMSG: Message strip text in the script editor
messageStripText=Access help portal to get information and complete documentation.
#XMSG: Learn more link text
linkText=Learn More
#XMSG: For a data type with length, the length cannot be greater than the maximum value.
VAL_ELEMENT_PROPERTY_LENGTH_GREATER_THAN_MAX=The value for length cannot be greater than the maximum value ({0}).
#XMSG: For a data type with length, the length cannot be negative.
VAL_ELEMENT_PROPERTY_LENGTH_LESS_THAN_ZERO=The value for the length cannot be less than 0.
#XMSG: For a data type with length, the length cannot be zero.
VAL_ELEMENT_PROPERTY_LENGTH_CANNOT_BE_ZERO=The value for the length cannot be {0}.
#XMSG: The attribute of an Entity has a data type that must have a length.
VAL_ELEMENT_DATATYPE_NO_LENGTH=Please enter a value for length.
#XMSG: The attribute of an Entity has a data type that must have precision.
VAL_ELEMENT_DATATYPE_NO_PRECISION=Please enter a value for precision.
#XMSG: For a data type with precision, the precision cannot be greater than the maximum value.
VAL_ELEMENT_PROPERTY_PRECISION_GREATER_THAN_MAX=The value for the precision cannot be greater than {0}.
#XMSG: For a data type with precision, the precision cannot be less than the minimum value.
VAL_ELEMENT_PROPERTY_PRECISION_LESS_THAN_MIN=The value for the precision cannot be less than {0}.
#XMSG: For a data type with precision and scale, the precision cannot be less than the scale.
VAL_ELEMENT_PROPERTY_PRECISION_LESS_THAN_SCALE=The value for the precision cannot be less than the scale ({0})
#XMSG: The attribute of an Entity has a data type that must have scale.
VAL_ELEMENT_DATATYPE_NO_SCALE=Please enter a value for scale.
#XMSG: For a numeric data type like Decimal(precision, scale), the scale cannot be negative.
VAL_ELEMENT_PROPERTY_SCALE_LESS_THAN_ZERO=The value for the scale cannot be less than 0.
#XMSG: For a numeric data type like Decimal(precision, scale), the scale cannot be greater than the precision.
VAL_ELEMENT_PROPERTY_SCALE_GREATER_THAN_PREC=The value for the scale cannot be greater than the precision ({0}).
#~~~~~~~~~~~~~~~~~~~~~~~ Properties Panel texts ends here ~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Diagram texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XTOL: diagram tool item
zoomToFit=Zoom to Fit
#XTOL: tooltip for context pad to open data preview (new text)
dataViewer=Data Viewer
#XTOL: tooltip for context pad to open data preview (old text)
previewData=Preview Data
#XTOL: tooltip for context pad to open impact and lineage editor
openImpactLineage=Impact and Lineage Analysis
#XTOL: tooltip for context pad to open object in new tab
openInNewTab=Open in New Tab
#XTOL: tooltip for context pad to create new target table
createTable=Create New Table
#XTOL: tooltip for context pad to edit view transform operator
edit=Edit
#XTOL: tooltip for context pad to clear the source or target node in the diagram
remove=Remove
#XTOL: tooltip for context pad to define view transform operator using SQL editor
useSQLViewTransform=SQL View Transform
#XTOL: tooltip for context pad to define view transform operator using Graphical View editor
useGraphicalViewTransform=Graphical View Transform
#XMSG: Error shared table cannot be used as target
unsupportedTargetSharedTable=You can’t use shared table as a target table in a transformation flow.
#XMSG: Error SAC artefact cannot be used as target
unsupportedSACTarget=You cannot use a read-only object as a target for a transformation flow.
#XMSG: Error Open SQL Schema/HDI Container table cnnot be used as target
unsupportedTargetOpenSchema=It is not possible to use a database schema or HDI Container as a target in a transformation flow.
#XMSG: Message to show when view transform validation is in progress
vtValidationInProgress=View transform validation in progress.
#XMSG: welcome screen message when the editor is new or empty
welcomeText=Start by clicking on a node to define a view transform or add/create a target table.
#XMSG: new welcome screen message when drag and drop of table is enabled (spark)
welcomeTextDndSpark=Start by clicking on a node or dragging and dropping from the repository panel to add a source or a target table
#XBUT: confirmation button to switch graphical view source to sql view source in the warning dialog
confirmSQLViewTransform=Confirm
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Tenant upgrade in progress.

#~~~~~~~~~~~~~~~~~~~~~~~ Diagram texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Change Management texts starts here ~~~~~~~~~~~~~~~~~~~
#XTIT: title for the change management dialog
changeManagementTitle=Target Updates
#XTIT: title for the change management dialog
changeManagementTitleSourceOrTarget=Source/Target Updates
#XTIT
objectDisplayName=Display Name
#XTIT: name of the user who modifed the object
changedBy=Changed By
#XTIT: the time when the object is modified
changedOn=Changed On
#XTIT
objectType=Type
#XBUT
ok=OK
#XMSG: message in the change management dialog
reviewText=Please review the modifications. You will need to save and then redeploy the transformation flow for the changes to take effect.
#XMSG: message in the change management dialog
changesText=The following target table used in this transformation flow has been modified (see validation messages for details):
#XMSG: message in the change management dialog
changesTextSourceOrTarget=The following sources/target table used in this transformation flow have been modified (see validation messages for details):
#XMSG: Info column removed from the target: params: {0} label of the node
validationChangeMgmtDelColMsgHdr=Columns removed from {0}.
#XMSG: Info column data type updated in the target: params: {0} label of the node
validationChangeMgmtUpdDTColMsgHdr=The data type of the following columns in {0} has changed.
#XMSG: Info column added to the target: params: {0} label of the node
validationChangeMgmtNewColMsgHdr=Columns added to {0}.
#NOTR: description text of message params: {0} list (with line breaks) of columns
validationChangeMgmtColsMsgDesc={0}
#XMSG: data type change information for the column params: {0} column name {1} old data type {2} new data type
validationChangeMgmtUpdDTColMsgDesc={0}, changed from {1} to {2}.
#XMSG: title text of message, change of key/isKey: params: {0} number of columns
validationChangeMgmtUpdKeyColMsgHdr=Columns with keys set or removed ({0}).
#XMSG: key property of a column has been set
validationChangeMgmtKeyPropertySet=The field is now a key field.
#XMSG: key property of a column has been removed
validationChangeMgmtKeyPropertyRemoved=The field is no longer a key field.
#XMSG: title text of message, change of business name: params: {0} number of columns
VAL_CHANGE_MGMT_UPD_BN_COL_MSG_HDR=Columns with updated business name ({0}).
#XMSG: generic message to fill description of message with a list of column names: params: {0} list (with line breaks) of columns
VAL_CHANGE_MGMT_COLUMNS_MSG_DESC={0}
#XMSG: displayed next to a column name to indicate whether the value is new (maintained for the first time). Example: The business name of a column got maintained and before only the technical name was in place
VAL_VALUE_NEW=(new)
#XMSG: displayed next to a column name to indicate whether the value has been resetted/deleted. Example: The business name of a column or source has been removed
VAL_VALUE_DELETED=(deleted)
#XFLD: Label for source type node change management
source=Source
#XFLD: Label for target type node change management
target=Target
#XFLD: Tooltip label for view icon
view=View
#~~~~~~~~~~~~~~~~~~~~~~~ Change Management texts ends here ~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary Editor texts starts here ~~~~~~~~~~~~~~~~~~~~

#XTIT: title of the secondry diagram editor (graphical view editor)
graphicalViewEditor=Graphical View Editor
#XTIT: title of the secondry diagram editor (sql view editor)
sqlViewEditor=SQL View Editor
#XTIT: title of the panel in Entity/Source properties panel
deltaSettings=Delta Settings
#XFLD: decides which object of delta table to be used(Delta Capture or All Active Records)
LoadFromLocalTable=Load from Local Table
#XFLD: decides which object of delta table to be used(Delta Capture or All Active Records)
LoadFromTable=Load from Table
#XRBL: only delta records will be processed when this option is selected
deltaCapture=Delta Capture
#XRBL: all the records will be processed when this option is selected
allActiveRecords=All Active Records
#XMSG: Error multiple delta source exist with Delta Capture selected
validationMultipleDeltaSource=Only one source table can have the value Delta Capture for the option Load From Local Table.
#XMSG: Error Open SQL Schema/HDI Container table cannot be used as a source: params: {0} technical name of the object
validationOpenSchemaSource=It’s not possible to use a database schema or HDI container table "{0}" as a source in a transformation flow.
#XMSG: Error remote table cannot be used as a source :params: {0} technical name of the object
validationRemoteTableSource=It’s not possible to use remote table "{0}" as a source in a transformation flow.
#XMSG: Error view cannot be used as a source :params: {0} technical name of the object
validationViewSource=It’s not possible to use view "{0}" as a source in a transformation flow.
#XMSG: Error Aggregation node cannot be used with delta source(source with Delta Capture selected)
validationAggregationWithDelta=The aggregation operator cannot be used if any of the source tables have the value Delta Capture for the option Load From Local Table.
#XMSG: Error Left Outer Join with Delta Table as Outer Operand
validationErrorLeftOuterJoinWithDeltaTable=It is not possible to use a source table for which the delta capture setting is enabled as the outer operand of a LEFT-OUTER-JOIN.
#XMSG: Info: Outer Join with Delta Table as Outer Operand
validationErrorOuterJoinWithDeltaTable=Using a Delta enabled source table as the outer operand of left or right join may result in error during run.
#XMSG: Error Union with Delta Source
validationErrorUnionWithDeltaSource=The union operator cannot be used if any of the source tables have the value Delta Capture for the option Load from Table.
#XMSG: Error Timestap column should not be used in any calculated column
validationForCalculatedColumnWithDelta=The column {0} cannot be used in calculated column {1}.
#XMSG: Warning
invalidSQLViewDefinitionConfirmation=Failed to validate SQL statement.
#XMSG: Warning SQL statment contains validation error
sqlErrorConfirmation=The SQL statement contains validation errors.
#XMSG: Error multiple delta source exist in the sql statement
validationSQLEditorMultipleDeltaSource=Multiple delta sources exist in the SQL statement. Only one delta table can be used as source.
#XBTN: delete button on parameters panel
deleteParameters=Delete
#XMSG: loading message shown in busy dialog when parameter is appended in sql statement
loadingParameters=Loading parameters.
#XMSG: Open SQl schema source using one remote source directly or indirectly
validationOpenSchemaRemoteSource=The object "{0}" references the remote table "{1}" (either directly or indirectly).
#XMSG: Open SQl schema source using multiple remote sources directly or indirectly
validationOpenSchemaRemoteSources=The object "{0}" references the following remote tables (either directly or indirectly): {1}
#XMSG: Open SQL schema source DAC validation
validationOpenSchemaDAC=Cannot add object "{0}". A data access control has been applied to this object (or a dependent object).

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary Editor texts ends here ~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Dialog Boxes texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error new editor is opened and the object is not saved and Run button is clicked
runUnsavedError=You need to save your transformation flow before you can run it.
#XTIT
runError=Error
#XMSG: Error unsaved changes in the editor and Run button is clicked
runModifiedError=There are unsaved changes in the transformation flow. Please save the transformation flow.
#XTIT
runWarning=Warning
#XMSG: Warning the object has changes to deploy and Run button is clicked
runDeployedVersion=The latest version of the transformation flow has not yet been deployed. The last deployed version of the transformation flow will be run. Do you want to continue?
#XBTN: run with error or warning
runAnyway=Run Transformation Flow
#XBTN
close=Close
#XMSG: Error object has validation error
runWithValidationErrors=The transformation flow contains validation errors. Running the transformation flow may result in failure.
#XTIT
waitBusy=Please wait.
#XMSG: initiating transformation flow run
runBusy=Preparing data...
#XMSG: Success
runSuccess=Transformation flow run has started
#XMSG: Error
runFail=Failed to run the transformation flow
#XTIT: loading dialog title
loading=Loading
#XMSG: fetching run details from the server
loaderDetails=Fetching details from the server
#XMSG: Error graphical view transform with error cannot be converted to SQL view transform
convertGVTransformToSQLViewTransformError=It’s not possible to convert Graphical View Transform with errors to SQL View Transform.
#~~~~~~~~~~~~~~~~~~~~~~~ Dialog Boxes texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Validation texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error Duplicate technical name validation message : params: {0} technical name of the object
validationDuplicateTechnicalName=Object "{0}" already exists in the repository.
#XMSG: Error There should be a technical name for the target table.
validationEmptyTechnicalTargetTable=Enter a technical name for the target table.
#XMSG: Error View transform operator not defined(template node is shown in the diagram)
validationEmptyViewTransform=A View Transform operator is not yet defined for the transformation flow.
#XMSG: Error source operator not defined(template node is shown in the diagram) in large system spaces
validationEmptySource=A Source operator is not yet defined for the transformation flow.
#XMSG: Error Target table not defined (template node is shown in the diagram)
validationEmptyTargetTable=The transformation flow does not contain a target table.
#XMSG: Error validation message for incompatible data type mapping in target : params: {0} source column name {1} source column data type {2} target column name {3} target column data type {4} label of the target node
validationTargetColumnMappingIncompatible={0} ({1}) is incompatible with {2} ({3}) in the operator "{4}".
#XMSG: Error validation message for incompatible mapping for timestamp column :  params: {0} & {1} and compatible source and column names
validationSouceTargetColumnMappingIncompatibleTimestamp=The column {0} can only be mapped to the column {1} in the target table.
#XMSG: Error No mappping defined in the target operator : params: {0} label of the target node
validationTargetMappingMissing=No mapping defined in the operator "{0}".
#XMSG: Error No mappping defined in the target operator : params: {0} name of the primary key column {1} label of the target node
validationTargetMappingMissingForPrimaryKeyColumn=No mapping defined for the key column "{0}" in the operator {1}.
#XMSG: warning: No mappping defined for change Type Column
validationTargetMappingMissingForChangeTypeColumn=The source {0} field is not mapped to the target {0} field.
#XMSG: Info: For change Date Column, The timestamp of this field will be set to the time that the transformation flow runs
validationTargetMappingMissingForChangeDateColumn=The {0} field is not relevant for mapping. The timestamp of this field will be set to the time that the transformation flow runs.
#XMSG: Error No columns defined in the target operator : params: {0} label of the target node
validationEmptyColumns=Columns aren’t defined for the operator "{0}".
#XMSG: Error load type "Initial and Delta" not supported when the truncate mode is enbled in the target table properties
validationTruncateInvalidMode=The load type Initial and Delta is not supported if the option Truncate is enabled for the target table.
#XMSG: Error load type "Initial and Delta" not supported when the deleteAllBeforeLoading mode is enbled in the target table properties
validationDeleteAllBeforeLoadingInvalidMode=The load type Initial and Delta is not supported if the option Delete All Before Loading is enabled for the target table.
#XMSG: Error Object has duplicated technical name. : params: {0} technical name of the object
validationDuplicateTargetTechnicalName=Object "{0}" already exists in the repository. Please enter another name.
#XMSG: Error technical name field is empty
validationEmptyTechnicalName=Enter a technical name for the table.
#XMSG: Error business name field is empty
validationEmptyBusinessName=Enter a business name for the table.
#XMSG: Error message for data type mismatch in target : params: {0} source column name {1} source column data type {2} target column name {3} target column data type
datatypeMismatchTarget=Data type must match to create mapping. {0} ({1}) is incompatible with {2} ({3}).
#XMSG: Error load type "Initial and Delta" not supported for non-delta source
validationNonDeltaSource=The load type Initial and Delta is not supported if the source is not delta-enabled.
#XMSG: Error load type "Initial and Delta" not supported for non-delta target
validationNonDeltaTarget=The load type Initial and Delta is not supported if the target table is not delta-enabled.
#XMSG: Error truncate mode is not supported for delta-enabled target table
validationTruncateDeltaTarget=Truncate mode is not supported for delta-enabled target table.
#XMSG: Info Message for upsert of records when target node has truncate off and a key column
validationTruncateOffandKeyColumn=The records read using the view transform operator are loaded to the target table using the UPSERT operation.
#XMSG: Error deleteAllBeforeLoading mode is not supported for delta-enabled target table
validationDeleteAllBeforeLoadingDeltaTarget=Delete All Before Loading mode is not supported for delta-enabled target table.
#XMSG: Info Message for upsert of records when target node has deleteAllBeforeLoading off and a key column
validationDeleteAllBeforeLoadingOffandKeyColumn=The records read using the view transform operator are loaded to the target table using the UPSERT operation.
#XMSG: delta-enabled source cannot be used with non-delta target : params: {0} label of the target node
validationDeltaSourceNonDeltaTarget=Delta-enabled source cannot be used with non delta-enabled target "{0}".
#XMSG: Error graphicalview transform has a validation error.
validationGraphicalViewTransformHasError=One or more errors exist for the Graphical View Transform. To view these errors, edit the Graphical View Transform and click Validation Messages.
#XMSG: Error sql view transform has a validation error.
validationSQLViewTransformHasError=One or more errors exist for the SQL View Transform. To view these errors, edit the SQL View Transform and click Validation Messages.
#XMSG: Error graphical view transform has a change management error.
validationGraphicalViewTransformHasChangeManagement=One or more source tables have changed. To view these changes, edit the Graphical View Transform and click Validation Messages.
#XMSG: Error sql view transform has a change management error.
validationSQLViewTransformHasChangeManagement=One or more source tables have changed. To view these changes, edit the SQL View Transform and click Validation Messages.
#XMSG: Info load type set to intial and delta as source and traget are delta tables
validationInitialDeltaLoadType=As the delta capture feature is enabled for both source and target tables, the default load type is Initial and Delta.
#XMSG: Info reset target table watermark
validationDeltaTargetTableChanged=The target table has changed. Only delta data will be transferred to the new target table. If you want to transfer all data to the target table, you can reset the watermark in the Data Integration Monitor.
#XMSG: Error delta table from BW bridge cannot be used in SQL Script view transform
validationBWDeltaSourceInSQLScriptTransform=To load delta changes from a remote table located in a BW Bridge space using an SQL View Transform, the language must be SQL (Standard Query). SQLScript (Table Function) is not supported.
#XMSG: No primary key column in new delta target
validationNoKeyColumn=To enable the delta capture setting, the target table must contain at least one key column.
#XMSG: Duplicate column names in new delta target
validationColumnNameNotUnique=Column names must be unique. {0} has multiple occurrences in the table "{1}".
#XMSG: Missing CDC Columns in new delta target
validationMissingCDCColumns=The delta capture setting is enabled for table {0}, but the delta capture columns are missing.
#XMSG: Validate non LTF target in large system space
validationLTFDeltaTargetLargeSystemSpace=The target table "{0}" must be delta enabled with file storage in large system space.
#XMSG: Validation error if a transformation flow from hana runtime space is imported into large system space
validationNonLsaTFInLsaSpace=Transformation Flow created with HANA runtime cannot be used in large system space.
#XMSG: Always show a validation info message in the python node to inform the user to update the python script manually to generate the data to match the output columns.
validationUpdatePythonScript=Update the output column schema to match the columns returned by the DataFrame in Python script.
#XMSG: Error message for the python operator for changed CDC columns
validationChangedCDCColumns=The delta capture columns are either missing or have been modified in the Python script.
#XMSG: Validation error if a transformation flow from spark runtime space is imported into non large system space
validationLsaTFInNonLsaSpace=A transformation flow with spark runtime cannot be created in a space with SAP HANA Database (Disk and In-Memory) storage.
#XMSG Error message if parameters are used when table is used as source
validationSourceTableInputParams=Input parameters are supported only for view transform operator.
#~~~~~~~~~~~~~~~~~~~~~~~ Validation texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Data Preview texts starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error data preview is not available for selected operator
dataPreviewNotSupp=The data preview function is not available for this operator.
#XMSG: Error data preview is not available as the selected node is a template.
dataPreviewTemplate=The data preview function is not available for this node.
#XMSG: Error data preview is not available as the selected node is a new target table. i.e. the target table is not created
dataPreviewNewTarget=The data preview function is not available for this target table.
#XMSG: Error data preview is not avalibale as there are no columns in the selected node
dataPreviewEmptyColumns=The data preview function is not available as the table has no visible columns.
#XMSG Error insuffcient privileges to view data
dataPreviewInSufficientPrivileges=You do not have sufficient privileges to view this data
#XMSG Not suffcient privileges to view data
dataPreviewNoPrivileges=You aren’t allowed to view this data.
#XTIT Data preview problems tab
txtProblems=Errors
#XMSG: Error data preview is not available for SQL Script
dataPreviewNotSupportedSQLScript=The data preview function is not available for SQLScript (Table Function).
#XMSG: Error message for data preview with Cross space object with Input parameters
dataPreviewNotSupportedRemoteTableWithParam=Cannot preview data :\nData preview for View Transform with Cross space objects containing Input Parameters is not supported.
#~~~~~~~~~~~~~~~~~~~~~~~ Data Preview texts ends here~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG:view transform is modified and changes are applied to the editor
viewTransformChangesUpdated=All changes to the view transform are applied.
#XMSG:confirm switch from graphical view to sql view in the view transform
convertToSQLViewConfirmation=You are changing the type of your view transform to SQL view transform. This change cannot be undone.
#XTIT Save Dialog param
modelNameTransformationFlow=Transformation Flow
#XMSG: Info automap operation cannot be performed due non matching columns
autoMapInvalid=Name and data type of the columns must match for auto mapping.
#XMSG Info: mapping already exists
mappingExists=Mapping already exists.
#XMSG: There are invalid columns in the target table
invalidColumns=The following columns cannot be mapped, so they will be removed.
#XFLD: Label for package select
package=Package

#XMSG warning for repository package assignment changed for Object name {1}
VAL_PACKAGE_CHANGED=You have assigned this object to package {1}. Click Save to confirm and validate this change. Note that assignment to a package cannot be undone in this editor after you save.
#XMSG warning for repository package dependency issue for object name{0}
MISSING_DEPENDENCY=Dependencies of object "{0}" cannot be resolved in the context of package {1}.
#XFLD: Mapped to parameter name{0}
mappedTo=Mapped to: {0}
#XFLD: Value parameter default Value{0}
defaultVal=Value: {0}
#XFLD: Not Mapped
notMapped=Not Mapped
#XMSG : Validation message for views with remote table name {1} for view name {0}
validateViewWithRemoteTables=The view "{0}" references the following remote tables (either directly or indirectly): "{1}"
#XMSG : Validation message for views with DAC for view name {0}
validationDAC=Cannot add view "{0}". A data access control has been applied to this view (or a dependent view).
#XMSG : Validation message for objects with remote tables, remote table name{0} in sql editor,
validateSqlRemoteTables=The SQL statement contains one or more objects which references the following remote tables (either directly or indirectly): "{0}"
#XBTN : Set Value
setValue=Set Value
#XBTN : Map TO
mapTo=Map To
#XMSG : This source name{0} contains input parameters.
parameterMappingInfo=This source {0} contains input parameters. For each parameter, you can either set a value or map to an input parameter in your view.
#XBTN : Cancel
cancel=Cancel
#XTIT : Set Value for Input Parameter name {0}
setValueForParam=Set Value for Input Parameter {0}
#XMSG : Value
enterValue=Value
#XMSG : Enter Valid Decimal value with valid precision and scale
VAL_ENTER_VALID_DECIMAL_GEN=Enter Valid Decimal value with precision {0} and scale {1}.
#XFLD : Runtime
runtime=Runtime
#XFLD: Storage
storage=Storage
#XMSG: Info message for skipping SQL validation when tenant upgrade is in progress
VAL_TENANT_UPGRADE_IN_PROGRESS=SQL View Transform validation was skipped as the tenant upgrade was in progress. Please validate the SQL View Transform before deploying the transformation flow.
#XMSG: Error LTF cannot be used as a source :params: {0} technical name of the object
validationLTFSource=It isn’t possible to use the local table (file) "{0}" as a source in a transformation flow (spark).
#XMSG: Error shared tables cannot be used as a source :params: {0} technical name of the object
validationSharedSource=It isn’t possible to use the shared table (file) "{0}" as a source in a transformation flow (spark).
#XMSG: Error Only LTF are supported in large system space
validationNonLTFSource=It isn’t possible to use the source "{0}" in transformation flow (spark) because it isn’t a local table (file).
#~~~~~~~~~~~~~~~~~~~~~~~ Python Operator texts starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XTOL: Tooltip for python operator in context pad
pythonOperator= Python Operator

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary sources properties starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Show Sources
showSources=Show Sources:
#XFLD: Qualified Name
qualifiedName= Qualified Name
#XFLD: Space Name
spaceName=Space Name
#XFLD: Context Name
contextName=Context Name
#XFLD: Connection
connection=Connection
#XFLD: Remote Table
remoteTable=Remote Table
#XFLD: Active Records
activeRecords=Active Records
#XFLD: Local Table
localTable=Local Table
#XFLD: Local Schema
localSchema=Local Schema
#XMSG: no parameters message
noParameters=There are no parameters available
#XMSG: Validation Error in secondary source error message
secondarySourceErrorDialog=The view transform sources cannot be shown because there is at least one error in the SQL View Transform.
#XMSG: Unable to load sources error message
secondarySourceLoadingErrorDialog=The view transform sources cannot be loaded.
#XMSG: No sources error message
noSecondarySourceErrorDialog=The view transform does not have any source.
#XTIT Local Table (File)
LTFTable=Local Table (File)
#XMSG: Error data preview is not available for undeployed SQLScript Table function
dataPreviewNotDeployWarning=The data for your view transform is not visible here until all changes are deployed. Please deploy the transformation flow and try again.
#Input parameters
#XFLD : Input Parameters
parametersTitle=Input Parameters ({0})
#XMSG: Error message for empty input parameters
noInputParameters=There are currently no parameters available. To add some, press the Edit button.
#XTOL: Tooltip for edit input parameters
parametersTooltip=Edit input parameters
#XMSG: edit input parameters
editInputParameters=Edit input parameters
#XMSG: Create input parameters in the object to be used in inner filters
parametersDescription=Create input parameters in the object to be used in inner filters
#XBUT: Add new parameter
addParameters=Add new parameter
#XBUT: Delete parameter
deleteParameter=Delete parameter
#XFLD: Data Type
parameterDatatype=Data Type
#XFLD: Name
inputParameterName=Name
#XFLD: Placeholder for string input type
placeholderText=Enter default value
#XFLD: Default Value
defaultValue=Default Value
#XFLD defined by
definedBy=Defined By
#XMSG Error message for empty input parameter name
VAL_PARAM_EMPTY_NM=The name of the parameter is empty.
#XMSG Error message for parameter name not unique
VAL_PARAM_NAME_NOT_UNIQUE=Parameter name is not unique.
#XMSG: Error message when object is not deployed yet
cannotViewNotDeployed=The object "{0}" has never been deployed. Deploy it before viewing it.
#XFLD: Placeholder for string input type
stringPlaceholderText=Enter a string
#XFLD: Placeholder for integer input type
intPlaceholderText=Enter a number
#XFLD: Placeholder for decimal input type
decPlaceholderText=Enter a decimal value
#NOTR: Placeholder for date format Filter
dateFormatFilter=YYYY-MM-DD
#NOTR: Placeholder for time format Filter
timeFormatFilter=HH:mm:ss
#NOTR: Placeholder for date and time format Filter
dateTimeFormatFilter=YYYY-MM-DD, HH:mm:ss
#XTXT: Text for true dropdown of boolean datatype for input parameter - the value should be in small case matching the actual data
param_true=true
#XTXT: Text for false dropdown of boolean datatype for input parameter - the value should be in small case matching the actual data
param_false=false
#XTXT: Text for None dropdown of boolean datatype for input parameter
none=None
#XFLD: Input Parameter Name
OperatorName=Name
#XFLD: Input Parameter Value
Value=Value
#XBTN: run button of run with parameter dialog
TXT_RUN=Run
#XFLD
InputParameters=Input Parameters
#XTXT: Text for dialog box for input parameter
EnterParameterValues=Enter Parameter values to run your transformation flow
#XMSG: Error message for invalid number input
INVALID_NUMBER=Please enter a valid number.
#XMSG: Error message for invalid decimal input
INVALID_DECIMAL=Please enter a valid decimal.
#XMSG: Error message for invalid date input
INVALID_DATE=Please enter a valid date (YYYY-MM-DD).
#XMSG: Error message for invalid time input
INVALID_TIME=Please enter a valid time (HH:mm:ss).
#XMSG: Error message for invalid date and time input
INVALID_DATETIME=Please enter a valid date and time (YYYY-MM-DD, HH:mm:ss).
#XMSG: Error message for invalid Boolean input
INVALID_BOOLEAN=Please enter a valid boolean value.
#XMSG: Error message for invalid generic input
INVALID_INPUT=Invalid input.
#XTIP: Tooltip for parameter value
more=More
#XTIT
mappingDialogTitle=Map Input Parameters

#~~~~~~~~~~~~Incremental agrregation panel~~~~~~~~~~~~~~~~~~~~~
#XFLD Incremental aggregation panel name
incrementalAggregation=Incremental Aggregation
#XTOL
editAggregations=Edit Aggregation
#XMSG Define aggregation message
defineAggregations=Click Edit to define Aggregations
#XFLD Aggregation type
aggregationType=Aggregation Type
#XMSG Mapping Deletion not supported message
mappingDeletionNotSupported=The mapping cannot be deleted because an incremental aggregation is already defined for that column.
#XFLD Reset aggregation
resetAggregations=Reset
#XMSG aggregation validation
validationAggregation=After the flow’s first run, any change in the aggregation may create data inconsistencies.
#XMSG no mapped columns
noMappedColumns=Map columns to display data.
#XMSG Validation message for aggregation with non numerical datatype
validationInvalidAggregationDatatype=The column "{0}" has a non-numerical data type which isn’t supported in incremental aggregations.
#XMSG Validation message for aggregation with no mapping
validationNoMappedColumnAggregation=The column "{0}" isn’t mapped. Map it to a column to apply the incremental aggregation.
