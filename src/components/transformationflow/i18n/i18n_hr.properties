#~~~~~~~~~~~~~~~~~~~~~~~ Properties Panel texts starts here ~~~~~~~~~~~~~~~~~~~~
#XTIT
properties=Svojstva
#XTIT
general=Općenito
#XFLD
businessName=Poslovni naziv
#XFLD
technicalName=Tehnički naziv
#XFLD
loadType=Tip učitavanja
#XFLD
deltaCaptureTableName=Naziv tablice snimanja delte
#XSEL
initialOnly=Samo početno
#XSEL
initialAndDelta=Početno i delta
#XCKL
truncate=Skraćivanje
#XCKL
deleteAllBeforeLoading=Izbrišite sve prije učitavanja
#XTIT
columns=Stupci
#XTIT
mappings=Mapiranja
#XFLD
columnDataType=Tip podataka
#XFLD
search=Pretraživanje
#XBUT
autoMap=Automatsko mapiranje
#XBUT
removeAllMappings=Uklanjanje svih mapiranja
#XMSG
noTargetInputs=Morate definirati operator transformacije prikaza kako biste prikazali informacije o mapiranju.
#XMSG
noColumnsData=Nema stupaca za prikaz.
#XTOL
@validateModel=Poruke validacije
#XTOL
@hierarchy=Hijerarhija
#XTOL
@columnCount=Broj stupaca
#XTOL
info=Informacije
#XTOL
cdcColumn=Stupac CDC
#XTIT
statusPanel=Status izvođenja
#XTOL
schedule=Raspored
#XTOL
navToMonitoring=Otvori u monitoru toka replikacija
#XBTN
createSchedule=Stvori raspored
#XBTN
editSchedule=Uredi raspored
#XBTN
deleteSchedule=Izbriši raspored
#XFLD
lastRun=Zadnje izvođenje
#XFLD
status=Status
#XMSG: Error run status cannot be fetched
errorDetails=Status izvođenja nije moguće dohvatiti.
#XLNK
viewDetails=Prikaži pojedinosti
#XMSG: Error data is not loading from the server
backendError=Čini se da se podaci trenutačno ne učitavaju s poslužitelja. Pokušajte ponovo poslije.
#XMSG: Error transformation flow run failed. open data integration monitoring for more details
viewDetailsFallback=Izvođenja toka transformacije nije uspjelo. Za više pojedinosti navigirajte do aplikacije Nadzor.
#XMSG
statusCompleted=Dovršeno
#XMSG
statusRunning=Izvodi se
#XMSG
statusFailed=Nije uspjelo
#XMSG
statusNotExecuted=Još nije izvedeno
#XBTN
editColumns=Uređivanje stupaca
#XBUT: button in the properties panel of the target table
createNewTargetTable=Stvaranje nove ciljne tablice
#XMSG: message shown in the properties panel of target table when it is not defined.
defineTargetInformation=Za stvaranje nove ciljne tablice kliknite gumb u nastavku Stvori novu ciljnu tablicu. Za upotrebu postojeće tablice kao ciljne tablice, možete povući i ispustiti tablicu iz spremišta na platno.
#XTIT
defineTarget=Definiranje ciljne tablice
#XTIT
createViewTransform=Stvaranje transformacije prikaza
#XMSG: message shown in the properties panel of view transform when it is not defined.
createViewTransformInformation=Da biste pokrenuli tok transformacije, stvorite transformaciju prikaza klikom na odgovarajući gumb u nastavku.
#XMSG: message shown in the properties panel of view transform when it is not defined.
createViewTransformInformationDndSpark=Kako biste pokrenuli svoj tok transformacije, definirajte izvor tako da povučete i ispustite izvornu tablicu iz panela spremišta ili kliknete relevantan gumb u nastavku.
#XBUT: button in the properties panel of view transform
sqlViewTransform=Transformacija SQL prikaza
#XBUT: button in the properties panel of view transform
graphicalViewTransform=Transformacija grafičkog prikaza
#XFLD: view transform operator type - the value will be either SQL View or Graphical View
viewTransformType=Tip
#XMSG: value of type property
sqlView=SQL prikaz
#XMSG: value of type property
graphicalView=Grafički prikaz

#~~~~~~~~~~~~Script properties text~~~~~~~~~~~~~~~~~~~~~
#XBTN: select all columns button text
selectAllColumns=Odaberi sve
#XBUT: Add column menu button
addColumn=Dodaj stupac
#XBUT: Create new column menu button
createNewColumn=Stvori novi stupac
#XBUT: Edit column menu button
editColumn=Uredi stupac
#XBUT: Delete button
deleteColumn=Izbriši
#XTIT: title for select columns dialog
columnSelectionDialogTitle=Odaberi stupce
#XMSG: message shown in the select columns dialog
columnSelectionNoDataText=Stupci nisu pronađeni
#XFLD
columnOrigin=Porijeklo
#XFLD
columnLength=Dužina
#XFLD
columnPrecision=Preciznost
#XFLD
columnScale=Ljestvica
#XBTN: save button text
save=Spremi
#XTOL: Tooltip text for more button
tooltipTxt=Više
#XTOL: Tooltip text for new column
newColumn=Novi stupac
#XTIT: title for the script editor
scriptTitle=Skripta
#XMSG: Message strip text in the script editor
messageStripText=Pristupite portalu za pomoć kako biste dobili informacije i potpunu dokumentaciju.
#XMSG: Learn more link text
linkText=Saznaj više
#XMSG: For a data type with length, the length cannot be greater than the maximum value.
VAL_ELEMENT_PROPERTY_LENGTH_GREATER_THAN_MAX=Vrijednost za dužinu ne smije biti veća od maksimalne vrijednosti ({0}).
#XMSG: For a data type with length, the length cannot be negative.
VAL_ELEMENT_PROPERTY_LENGTH_LESS_THAN_ZERO=Vrijednost za dužinu ne smije biti manja od 0.
#XMSG: For a data type with length, the length cannot be zero.
VAL_ELEMENT_PROPERTY_LENGTH_CANNOT_BE_ZERO=Vrijednost za dužinu ne smije biti {0}.
#XMSG: The attribute of an Entity has a data type that must have a length.
VAL_ELEMENT_DATATYPE_NO_LENGTH=Unesite vrijednost za dužinu.
#XMSG: The attribute of an Entity has a data type that must have precision.
VAL_ELEMENT_DATATYPE_NO_PRECISION=Unesite vrijednost za preciznost.
#XMSG: For a data type with precision, the precision cannot be greater than the maximum value.
VAL_ELEMENT_PROPERTY_PRECISION_GREATER_THAN_MAX=Vrijednost za preciznost ne smije biti veća od {0}.
#XMSG: For a data type with precision, the precision cannot be less than the minimum value.
VAL_ELEMENT_PROPERTY_PRECISION_LESS_THAN_MIN=Vrijednost za preciznost ne smije biti manja od {0}.
#XMSG: For a data type with precision and scale, the precision cannot be less than the scale.
VAL_ELEMENT_PROPERTY_PRECISION_LESS_THAN_SCALE=Vrijednost za preciznost ne smije biti manja od ljestvice ({0})
#XMSG: The attribute of an Entity has a data type that must have scale.
VAL_ELEMENT_DATATYPE_NO_SCALE=Unesite vrijednost za ljestvicu.
#XMSG: For a numeric data type like Decimal(precision, scale), the scale cannot be negative.
VAL_ELEMENT_PROPERTY_SCALE_LESS_THAN_ZERO=Vrijednost za ljestvicu ne smije biti manja od 0.
#XMSG: For a numeric data type like Decimal(precision, scale), the scale cannot be greater than the precision.
VAL_ELEMENT_PROPERTY_SCALE_GREATER_THAN_PREC=Vrijednost za ljestvicu ne smije biti veća od preciznosti ({0}).
#~~~~~~~~~~~~~~~~~~~~~~~ Properties Panel texts ends here ~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Diagram texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XTOL: diagram tool item
zoomToFit=Zumiraj da stane
#XTOL: tooltip for context pad to open data preview (new text)
dataViewer=Preglednik podataka
#XTOL: tooltip for context pad to open data preview (old text)
previewData=Pretpregled podataka
#XTOL: tooltip for context pad to open impact and lineage editor
openImpactLineage=Analiza utjecaja i porijekla
#XTOL: tooltip for context pad to open object in new tab
openInNewTab=Otvori u novoj kartici
#XTOL: tooltip for context pad to create new target table
createTable=Stvori novu tablicu
#XTOL: tooltip for context pad to edit view transform operator
edit=Uredi
#XTOL: tooltip for context pad to clear the source or target node in the diagram
remove=Ukloni
#XTOL: tooltip for context pad to define view transform operator using SQL editor
useSQLViewTransform=Transformacija SQL prikaza
#XTOL: tooltip for context pad to define view transform operator using Graphical View editor
useGraphicalViewTransform=Transformacija grafičkog prikaza
#XMSG: Error shared table cannot be used as target
unsupportedTargetSharedTable=Ne možete upotrebljavati dijeljenu tablicu kao ciljnu tablicu u toku transformacije.
#XMSG: Error SAC artefact cannot be used as target
unsupportedSACTarget=Objekt samo za čitanje ne možete upotrebljavati kao cilj za tok transformacije.
#XMSG: Error Open SQL Schema/HDI Container table cnnot be used as target
unsupportedTargetOpenSchema=Nije moguće upotrebljavati shemu baze podataka ili HDI spremnik kao cilj u toku transformacije.
#XMSG: Message to show when view transform validation is in progress
vtValidationInProgress=Validacija transformacije prikaza u tijeku.
#XMSG: welcome screen message when the editor is new or empty
welcomeText=Započnite klikom na čvor kako biste definirali transformaciju prikaza ili dodali/stvorili ciljnu tablicu.
#XMSG: new welcome screen message when drag and drop of table is enabled (spark)
welcomeTextDndSpark=Započnite klikom na čvor ili povlačenjem i ispuštanjem iz panela spremišta kako biste dodali izvornu ili ciljnu tablicu
#XBUT: confirmation button to switch graphical view source to sql view source in the warning dialog
confirmSQLViewTransform=Potvrdi
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Nadogradnja zakupca u tijeku.

#~~~~~~~~~~~~~~~~~~~~~~~ Diagram texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Change Management texts starts here ~~~~~~~~~~~~~~~~~~~
#XTIT: title for the change management dialog
changeManagementTitle=Ciljna ažuriranja
#XTIT: title for the change management dialog
changeManagementTitleSourceOrTarget=Ažuriranja izvora/cilja
#XTIT
objectDisplayName=Prikaz naziva
#XTIT: name of the user who modifed the object
changedBy=Promijenio
#XTIT: the time when the object is modified
changedOn=Promijenjeno
#XTIT
objectType=Tip
#XBUT
ok=OK
#XMSG: message in the change management dialog
reviewText=Pregledajte izmjene. Trebat ćete spremiti, a zatim ponovo uvesti tok transformacije kako bi promjene stupile na snagu.
#XMSG: message in the change management dialog
changesText=Sljedeća je ciljna tablica koja se upotrebljava u ovom toku transformacije izmijenjena (za pojedinosti pogledajte poruke validacije):
#XMSG: message in the change management dialog
changesTextSourceOrTarget=Sljedeća je izvorna/ciljna tablica koja se upotrebljava u ovom toku transformacije izmijenjena (za pojedinosti pogledajte poruke validacije):
#XMSG: Info column removed from the target: params: {0} label of the node
validationChangeMgmtDelColMsgHdr=Stupci su uklonjeni iz {0}.
#XMSG: Info column data type updated in the target: params: {0} label of the node
validationChangeMgmtUpdDTColMsgHdr=Tip podataka sljedećih stupaca u {0} promijenjen je.
#XMSG: Info column added to the target: params: {0} label of the node
validationChangeMgmtNewColMsgHdr=Stupci su dodani {0}.
#NOTR: description text of message params: {0} list (with line breaks) of columns
validationChangeMgmtColsMsgDesc={0}
#XMSG: data type change information for the column params: {0} column name {1} old data type {2} new data type
validationChangeMgmtUpdDTColMsgDesc=Stupac {0} promijenjen iz tipa podataka {1} u tip podataka {2}.
#XMSG: title text of message, change of key/isKey: params: {0} number of columns
validationChangeMgmtUpdKeyColMsgHdr=Stupci s postavljenim ili uklonjenim ključevima ({0}).
#XMSG: key property of a column has been set
validationChangeMgmtKeyPropertySet=Polje je sada polje ključa.
#XMSG: key property of a column has been removed
validationChangeMgmtKeyPropertyRemoved=Polje više nije polje ključa.
#XMSG: title text of message, change of business name: params: {0} number of columns
VAL_CHANGE_MGMT_UPD_BN_COL_MSG_HDR=Stupci s ažuriranim poslovnim nazivom ({0}).
#XMSG: generic message to fill description of message with a list of column names: params: {0} list (with line breaks) of columns
VAL_CHANGE_MGMT_COLUMNS_MSG_DESC={0}
#XMSG: displayed next to a column name to indicate whether the value is new (maintained for the first time). Example: The business name of a column got maintained and before only the technical name was in place
VAL_VALUE_NEW=(novo)
#XMSG: displayed next to a column name to indicate whether the value has been resetted/deleted. Example: The business name of a column or source has been removed
VAL_VALUE_DELETED=(izbrisano)
#XFLD: Label for source type node change management
source=Izvor
#XFLD: Label for target type node change management
target=Cilj
#XFLD: Tooltip label for view icon
view=Prikaz
#~~~~~~~~~~~~~~~~~~~~~~~ Change Management texts ends here ~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary Editor texts starts here ~~~~~~~~~~~~~~~~~~~~

#XTIT: title of the secondry diagram editor (graphical view editor)
graphicalViewEditor=Uređivač grafičkog prikaza
#XTIT: title of the secondry diagram editor (sql view editor)
sqlViewEditor=Uređivač SQL prikaza
#XTIT: title of the panel in Entity/Source properties panel
deltaSettings=Postavke delte
#XFLD: decides which object of delta table to be used(Delta Capture or All Active Records)
LoadFromLocalTable=Učitavanje iz lokalne tablice
#XFLD: decides which object of delta table to be used(Delta Capture or All Active Records)
LoadFromTable=Učitavanje iz tablice
#XRBL: only delta records will be processed when this option is selected
deltaCapture=Snimanje delte
#XRBL: all the records will be processed when this option is selected
allActiveRecords=Svi aktivni zapisi
#XMSG: Error multiple delta source exist with Delta Capture selected
validationMultipleDeltaSource=Samo jedna izvorna tablica može imati vrijednost Snimanje delte za mogućnost Učitavanje iz lokalne tablice.
#XMSG: Error Open SQL Schema/HDI Container table cannot be used as a source: params: {0} technical name of the object
validationOpenSchemaSource=Nije moguće upotrebljavati shemu baze podataka ili tablicu HDI spremnika "{0}" kao izvor u toku transformacije.
#XMSG: Error remote table cannot be used as a source :params: {0} technical name of the object
validationRemoteTableSource=Nije moguće upotrebljavati udaljenu tablicu "{0}" kao izvor u toku transformacije.
#XMSG: Error view cannot be used as a source :params: {0} technical name of the object
validationViewSource=Nije moguće upotrijebiti prikaz "{0}" kao izvor u toku transformacije.
#XMSG: Error Aggregation node cannot be used with delta source(source with Delta Capture selected)
validationAggregationWithDelta=Operator agregiranja ne može se upotrebljavati ako bilo koja od izvornih tablica ima vrijednost Snimanje delte za mogućnost Učitavanje iz lokalne tablice.
#XMSG: Error Left Outer Join with Delta Table as Outer Operand
validationErrorLeftOuterJoinWithDeltaTable=Nije moguće upotrijebiti izvornu tablicu za koju je omogućena postavka snimanja delte kao vanjskog operanda za način kombiniranja tablica LEFT-OUTER-JOIN.
#XMSG: Info: Outer Join with Delta Table as Outer Operand
validationErrorOuterJoinWithDeltaTable=Upotreba izvorne tablice omogućene za deltu kao vanjskog operanda lijevog ili desnog spoja može dovesti do pogreške tijekom izvođenja.
#XMSG: Error Union with Delta Source
validationErrorUnionWithDeltaSource=Operator unije ne može se upotrebljavati ako bilo koja od izvornih tablica ima vrijednost Snimanje delte za mogućnost Učitavanje iz tablice.
#XMSG: Error Timestap column should not be used in any calculated column
validationForCalculatedColumnWithDelta=Stupac {0} ne može se upotrebljavati u izračunatom stupcu {1}.
#XMSG: Warning
invalidSQLViewDefinitionConfirmation=Validacija SQL naredbe nije uspjela.
#XMSG: Warning SQL statment contains validation error
sqlErrorConfirmation=SQL naredba sadržava pogreške validacije.
#XMSG: Error multiple delta source exist in the sql statement
validationSQLEditorMultipleDeltaSource=U SQL naredbi postoji više delta izvora. Samo se jedna delta tablica može upotrijebiti kao izvor.
#XBTN: delete button on parameters panel
deleteParameters=Izbriši
#XMSG: loading message shown in busy dialog when parameter is appended in sql statement
loadingParameters=Učitavanje parametara.
#XMSG: Open SQl schema source using one remote source directly or indirectly
validationOpenSchemaRemoteSource=Objekt "{0}" referencira udaljenu tablicu "{1}" (bilo izravno ili neizravno).
#XMSG: Open SQl schema source using multiple remote sources directly or indirectly
validationOpenSchemaRemoteSources=Objekt "{0}" referencira sljedeće udaljene tablice (bilo izravno ili neizravno): {1}
#XMSG: Open SQL schema source DAC validation
validationOpenSchemaDAC=Nije moguće dodati objekt "{0}". Kontrola pristupa podacima primjenjuje se na taj objekt (ili neki zavisni objekt).

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary Editor texts ends here ~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Dialog Boxes texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error new editor is opened and the object is not saved and Run button is clicked
runUnsavedError=Prije izvođenja morate spremiti svoj tok transformacije.
#XTIT
runError=Pogreška
#XMSG: Error unsaved changes in the editor and Run button is clicked
runModifiedError=Postoje nespremljene promjene u toku transformacije. Spremite tok transformacije.
#XTIT
runWarning=Upozorenje
#XMSG: Warning the object has changes to deploy and Run button is clicked
runDeployedVersion=Najnovija verzija toka transformacije još nije uvedena. Izvest će se zadnja uvedena verzija tijeka transformacije. Želite li nastaviti?
#XBTN: run with error or warning
runAnyway=Izvedi tok transformacije
#XBTN
close=Zatvori
#XMSG: Error object has validation error
runWithValidationErrors=Tok transformacije sadržava pogreške validacije. Izvođenje toka transformacije može dovesti do pogreške.
#XTIT
waitBusy=Pričekajte.
#XMSG: initiating transformation flow run
runBusy=Priprema podataka...
#XMSG: Success
runSuccess=Izvođenje toka transformacije pokrenuto
#XMSG: Error
runFail=Izvođenje toka transformacije nije uspjelo
#XTIT: loading dialog title
loading=Učitavanje
#XMSG: fetching run details from the server
loaderDetails=Dohvaćanje pojedinosti s poslužitelja
#XMSG: Error graphical view transform with error cannot be converted to SQL view transform
convertGVTransformToSQLViewTransformError=Transformaciju grafičkog prikaza s pogreškama nije moguće pretvoriti u transformaciju SQL prikaza.
#~~~~~~~~~~~~~~~~~~~~~~~ Dialog Boxes texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Validation texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error Duplicate technical name validation message : params: {0} technical name of the object
validationDuplicateTechnicalName=Objekt "{0}" već postoji u spremištu.
#XMSG: Error There should be a technical name for the target table.
validationEmptyTechnicalTargetTable=Unesite tehnički naziv za ciljnu tablicu.
#XMSG: Error View transform operator not defined(template node is shown in the diagram)
validationEmptyViewTransform=Operator transformacije prikaza još nije definiran za tok transformacije.
#XMSG: Error source operator not defined(template node is shown in the diagram) in large system spaces
validationEmptySource=Izvorni operator još nije definiran za tok transformacije.
#XMSG: Error Target table not defined (template node is shown in the diagram)
validationEmptyTargetTable=Tok transformacije ne sadržava ciljnu tablicu.
#XMSG: Error validation message for incompatible data type mapping in target : params: {0} source column name {1} source column data type {2} target column name {3} target column data type {4} label of the target node
validationTargetColumnMappingIncompatible={0} ({1}) nije kompatibilan s {2} ({3}) u operatoru "{4}".
#XMSG: Error validation message for incompatible mapping for timestamp column :  params: {0} & {1} and compatible source and column names
validationSouceTargetColumnMappingIncompatibleTimestamp=Stupac {0} može se mapirati samo na stupac u ciljnoj tablici {1}.
#XMSG: Error No mappping defined in the target operator : params: {0} label of the target node
validationTargetMappingMissing=U operatoru {0} nije definirano mapiranje.
#XMSG: Error No mappping defined in the target operator : params: {0} name of the primary key column {1} label of the target node
validationTargetMappingMissingForPrimaryKeyColumn=Mapiranje nije definirano za stupac ključa "{0}" u operatoru {1}.
#XMSG: warning: No mappping defined for change Type Column
validationTargetMappingMissingForChangeTypeColumn=Izvorno polje {0} nije mapirano u ciljno polje {0}.
#XMSG: Info: For change Date Column, The timestamp of this field will be set to the time that the transformation flow runs
validationTargetMappingMissingForChangeDateColumn=Polje {0} nije relevantno za mapiranje. Vremenska oznaka ovog polja bit će postavljena na vrijeme izvođenja toka transformacije.
#XMSG: Error No columns defined in the target operator : params: {0} label of the target node
validationEmptyColumns=Za operator "{0}" nisu definirani stupci.
#XMSG: Error load type "Initial and Delta" not supported when the truncate mode is enbled in the target table properties
validationTruncateInvalidMode=Tipovi učitavanja Početno i Delta nisu podržani ako je mogućnost Skraćivanje omogućena za ciljnu tablicu.
#XMSG: Error load type "Initial and Delta" not supported when the deleteAllBeforeLoading mode is enbled in the target table properties
validationDeleteAllBeforeLoadingInvalidMode=Tipovi učitavanja Početno i Delta nisu podržani ako je mogućnost Izbriši sve prije učitavanja omogućena za ciljnu tablicu.
#XMSG: Error Object has duplicated technical name. : params: {0} technical name of the object
validationDuplicateTargetTechnicalName=Objekt "{0}" već postoji u spremištu. Unesite drugi naziv.
#XMSG: Error technical name field is empty
validationEmptyTechnicalName=Unesite tehnički naziv za tablicu.
#XMSG: Error business name field is empty
validationEmptyBusinessName=Unesite poslovni naziv za tablicu.
#XMSG: Error message for data type mismatch in target : params: {0} source column name {1} source column data type {2} target column name {3} target column data type
datatypeMismatchTarget=Kako bi se stvorilo mapiranje, tip podataka mora se podudarati. {0} ({1}) nije kompatibilan s {2} ({3}).
#XMSG: Error load type "Initial and Delta" not supported for non-delta source
validationNonDeltaSource=Tipovi učitavanja Početno i Delta nisu podržani ako u izvoru nije omogućena delta.
#XMSG: Error load type "Initial and Delta" not supported for non-delta target
validationNonDeltaTarget=Tipovi učitavanja Početno i Delta nisu podržani ako u ciljnoj tablici nije omogućena delta.
#XMSG: Error truncate mode is not supported for delta-enabled target table
validationTruncateDeltaTarget=Način skraćivanja nije podržan za ciljnu tablicu u kojoj je omogućena delta.
#XMSG: Info Message for upsert of records when target node has truncate off and a key column
validationTruncateOffandKeyColumn=Zapisi pročitani s pomoću operatora transformacije prikaza učitani su u ciljnu tablicu operacijom UPSERT.
#XMSG: Error deleteAllBeforeLoading mode is not supported for delta-enabled target table
validationDeleteAllBeforeLoadingDeltaTarget=Način Izbriši sve prije učitavanja nije podržan za ciljnu tablicu s omogućenu za deltu.
#XMSG: Info Message for upsert of records when target node has deleteAllBeforeLoading off and a key column
validationDeleteAllBeforeLoadingOffandKeyColumn=Zapisi pročitani s pomoću operatora transformacije prikaza učitavaju se u ciljnu tablicu s pomoću operacije UPSERT.
#XMSG: delta-enabled source cannot be used with non-delta target : params: {0} label of the target node
validationDeltaSourceNonDeltaTarget=Izvor u kojem je omogućena delta ne može se upotrijebiti s ciljem u kojem nije omogućena delta "{0}".
#XMSG: Error graphicalview transform has a validation error.
validationGraphicalViewTransformHasError=Postoji jedna ili više grešaka za transformaciju grafičkog prikaza. Kako biste vidjeli te pogreške, uredite transformaciju grafičkog prikaza i kliknite Poruke validacije.
#XMSG: Error sql view transform has a validation error.
validationSQLViewTransformHasError=Postoji jedna ili više grešaka za transformaciju SQL prikaza. Kako biste vidjeli te pogreške, uredite transformaciju SQL prikaza i kliknite Poruke validacije.
#XMSG: Error graphical view transform has a change management error.
validationGraphicalViewTransformHasChangeManagement=Promijenjena je jedna ili više izvornih tablica. Kako biste vidjeli te promjene, uredite transformaciju grafičkog prikaza i kliknite Poruke validacije.
#XMSG: Error sql view transform has a change management error.
validationSQLViewTransformHasChangeManagement=Promijenjena je jedna ili više izvornih tablica. Kako biste vidjeli te promjene, uredite transformaciju SQL prikaza i kliknite Poruke validacije.
#XMSG: Info load type set to intial and delta as source and traget are delta tables
validationInitialDeltaLoadType=Budući da je značajka delta snimanja omogućena za izvorne za ciljne tablice, zadani tip učitavanja je Početno i Delta.
#XMSG: Info reset target table watermark
validationDeltaTargetTableChanged=Ciljna tablica je promijenjena. Samo će se delta podaci prenijeti u novu ciljnu tablicu. Ako želite prenijeti sve podatke u ciljnu tablicu, možete ponovo postaviti vodeni žig u nadzoru integracije podataka.
#XMSG: Error delta table from BW bridge cannot be used in SQL Script view transform
validationBWDeltaSourceInSQLScriptTransform=Za učitavanje delta promjena iz udaljene tablice koja se nalazi u prostoru mosta za BW s pomoću transformacije SQL prikaza, jezik mora biti SQL (standardni upit). SQLScript (funkcija tablice) nije podržan.
#XMSG: No primary key column in new delta target
validationNoKeyColumn=Da biste omogućili postavku delta snimanja, ciljna tablica mora sadržavati barem jedan stupac ključa.
#XMSG: Duplicate column names in new delta target
validationColumnNameNotUnique=Nazivi stupaca moraju biti jedinstveni. {0} se pojavljuje više puta u tablici "{1}".
#XMSG: Missing CDC Columns in new delta target
validationMissingCDCColumns=Postavka delta snimanja omogućena je za tablicu {0}, ali nedostaju stupci delta snimanja.
#XMSG: Validate non LTF target in large system space
validationLTFDeltaTargetLargeSystemSpace=Ciljna tablica "{0}" mora biti omogućena za deltu s pohranom datoteka u prostoru velikog sustava.
#XMSG: Validation error if a transformation flow from hana runtime space is imported into large system space
validationNonLsaTFInLsaSpace=Tok transformacije stvoren s vremenom izvođenja HANA ne može se upotrijebiti u prostoru velikog sustava.
#XMSG: Always show a validation info message in the python node to inform the user to update the python script manually to generate the data to match the output columns.
validationUpdatePythonScript=Ažurirajte shemu izlaznog stupca kako bi se podudarala sa stupcima koje vraća DataFrame u skripti Python.
#XMSG: Error message for the python operator for changed CDC columns
validationChangedCDCColumns=Stupci snimanja delte nedostaju ili su izmijenjeni u skripti Python.
#XMSG: Validation error if a transformation flow from spark runtime space is imported into non large system space
validationLsaTFInNonLsaSpace=Tok transformacije s vremenom izvođenja Spark ne može se stvoriti u prostoru s pohranom SAP HANA baze podataka (disk i In-Memory).
#~~~~~~~~~~~~~~~~~~~~~~~ Validation texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Data Preview texts starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error data preview is not available for selected operator
dataPreviewNotSupp=Funkcija pretpregleda podataka nije dostupna za ovaj operator.
#XMSG: Error data preview is not available as the selected node is a template.
dataPreviewTemplate=Funkcija pretpregleda podataka nije dostupna za ovaj čvor.
#XMSG: Error data preview is not available as the selected node is a new target table. i.e. the target table is not created
dataPreviewNewTarget=Funkcija pretpregleda podataka nije dostupna za ovu ciljnu tablicu.
#XMSG: Error data preview is not avalibale as there are no columns in the selected node
dataPreviewEmptyColumns=Funkcija pretpregleda podataka nije dostupna jer tablica nema vidljive stupce.
#XMSG Error insuffcient privileges to view data
dataPreviewInSufficientPrivileges=Nemate dovoljne ovlasti za prikaz tih podataka
#XMSG Not suffcient privileges to view data
dataPreviewNoPrivileges=Nemate dopuštenje za prikaz tih podataka.
#XTIT Data preview problems tab
txtProblems=Pogreške
#XMSG: Error data preview is not available for SQL Script
dataPreviewNotSupportedSQLScript=Funkcija pretpregleda podataka nije dostupna za  SQLScript (funkcija tablice).
#XMSG: Error message for data preview with Cross space object with Input parameters
dataPreviewNotSupportedRemoteTableWithParam=Nije moguće pretpregledati podatke :\nPretpregled podataka za transformnaciju prikaza s višeprostornim objektima koji sadrže ulazne parametre nije podržan.
#~~~~~~~~~~~~~~~~~~~~~~~ Data Preview texts ends here~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG:view transform is modified and changes are applied to the editor
viewTransformChangesUpdated=Primijenjene su sve promjene na transformaciji prikaza.
#XMSG:confirm switch from graphical view to sql view in the view transform
convertToSQLViewConfirmation=Mijenjate tip svoje transformacije prikaza u transformaciju SQL prikaza. Tu promjenu nije moguće poništiti.
#XTIT Save Dialog param
modelNameTransformationFlow=Tok transformacije
#XMSG: Info automap operation cannot be performed due non matching columns
autoMapInvalid=Naziv i tip podataka stupaca moraju se podudarati za automatsko mapiranje.
#XMSG Info: mapping already exists
mappingExists=Mapiranje već postoji.
#XMSG: There are invalid columns in the target table
invalidColumns=Sljedeće stupce nije moguće mapirati, pa će biti uklonjeni.
#XFLD: Label for package select
package=Paket

#XMSG warning for repository package assignment changed for Object name {1}
VAL_PACKAGE_CHANGED=Ovaj ste objekt dodijelili paketu {1}. Kliknite Spremi za potvrdu i validaciju te promjene. Uzmite u obzir da dodjelu paketu nakon spremanja nije moguće poništiti u ovom uređivaču.
#XMSG warning for repository package dependency issue for object name{0}
MISSING_DEPENDENCY=Zavisnosti objekta ''{0}'' nije moguće riješiti u kontekstu paketa {1}.
#XFLD: Mapped to parameter name{0}
mappedTo=Mapirano u: {0}
#XFLD: Value parameter default Value{0}
defaultVal=Vrijednost: {0}
#XFLD: Not Mapped
notMapped=Nije mapirano
#XMSG : Validation message for views with remote table name {1} for view name {0}
validateViewWithRemoteTables=Prikaz "{0}" referencira sljedeće udaljene tablice (bilo izravno ili neizravno): "{1}"
#XMSG : Validation message for views with DAC for view name {0}
validationDAC=Nije moguće dodati prikaz "{0}". Kontrola pristupa podacima primjenjuje se na taj prikaz (ili neki zavisni prikaz).
#XMSG : Validation message for objects with remote tables, remote table name{0} in sql editor,
validateSqlRemoteTables=SQL naredba sadržava jedan ili više objekata koji referenciraju sljedeće udaljene tablice (bilo izravno ili neizravno): "{0}"
#XBTN : Set Value
setValue=Postavi vrijednost
#XBTN : Map TO
mapTo=Mapiraj u
#XMSG : This source name{0} contains input parameters.
parameterMappingInfo=Ovaj izvor {0} sadrži ulazne parametre. Za svaki parametar možete postaviti vrijednost ili ga mapirati u neki ulazni parametar u svom prikazu.
#XBTN : Cancel
cancel=Odustani
#XTIT : Set Value for Input Parameter name {0}
setValueForParam=Postavi vrijednost za ulazni parametar {0}
#XMSG : Value
enterValue=Vrijednost
#XMSG : Enter Valid Decimal value with valid precision and scale
VAL_ENTER_VALID_DECIMAL_GEN=Unesite valjanu decimalnu vrijednost s preciznošću {0} i ljestvicom {1}.
#XFLD : Runtime
runtime=Vrijeme izvođenja
#XFLD: Storage
storage=Pohrana
#XMSG: Info message for skipping SQL validation when tenant upgrade is in progress
VAL_TENANT_UPGRADE_IN_PROGRESS=Validacija transformacije SQL prikaza preskočena je jer je nadogradnja zakupca bila u tijeku. Validirajte transformaciju SQL prikaza prije uvođenja toka transformacije.
#XMSG: Error LTF cannot be used as a source :params: {0} technical name of the object
validationLTFSource=Nije moguće upotrebljavati lokalnu tablicu (datoteku) "{0}" kao izvor u toku transformacije (spark).
#XMSG: Error shared tables cannot be used as a source :params: {0} technical name of the object
validationSharedSource=Nije moguće upotrebljavati dijeljenu tablicu (datoteku) "{0}" kao izvor u toku transformacije (spark).
#XMSG: Error Only LTF are supported in large system space
validationNonLTFSource=Nije moguće upotrebljavati izvor "{0}"  u toku transformacije (spark) jer to nije lokalna tablica (datoteka).
#~~~~~~~~~~~~~~~~~~~~~~~ Python Operator texts starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XTOL: Tooltip for python operator in context pad
pythonOperator=Operator Python

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary sources properties starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Show Sources
showSources=Pokaži izvore:
#XFLD: Qualified Name
qualifiedName=Kvalificirani naziv
#XFLD: Space Name
spaceName=Naziv prostora
#XFLD: Context Name
contextName=Naziv konteksta
#XFLD: Connection
connection=Veza
#XFLD: Remote Table
remoteTable=Udaljena tablica
#XFLD: Active Records
activeRecords=Aktivni zapisi
#XFLD: Local Table
localTable=Lokalna tablica
#XFLD: Local Schema
localSchema=Lokalna shema
#XMSG: no parameters message
noParameters=Nema dostupnih parametara
#XMSG: Validation Error in secondary source error message
secondarySourceErrorDialog=Izvore transformacije prikaza nije moguće pokazati jer postoji barem jedna pogreška u transformaciji SQL prikaza
#XMSG: Unable to load sources error message
secondarySourceLoadingErrorDialog=Izvore transformacije prikaza nije moguće učitati.
#XMSG: No sources error message
noSecondarySourceErrorDialog=Transformacija prikaza nema nijedan izvor.
#XTIT Local Table (File)
LTFTable=Lokalna tablica (datoteka)
#XMSG: Error data preview is not available for undeployed SQLScript Table function
dataPreviewNotDeployWarning=Podaci za transformaciju prikaza nisu vidljivi ovdje dok se ne uvedu sve promjene. Uvedite tijek transformacije i pokušajte ponovo.
#Input parameters
#XFLD : Input Parameters
parametersTitle=Ulazni parametri ({0})
#XMSG: Error message for empty input parameters
noInputParameters=Trenutačno nema dostupnih parametara. Kako biste neke dodali, pritisnite gumb Uredi.
#XTOL: Tooltip for edit input parameters
parametersTooltip=Uredi ulazne parametre
#XMSG: edit input parameters
editInputParameters=Uredi ulazne parametre
#XMSG: Create input parameters in the object to be used in inner filters
parametersDescription=Stvorite ulazne parametre u objektu kako bi ih se upotrebljavali u unutarnjim filtrima
#XBUT: Add new parameter
addParameters=Dodaj novi parametar
#XBUT: Delete parameter
deleteParameter=Izbriši parametar
#XFLD: Data Type
parameterDatatype=Tip podataka
#XFLD: Name
inputParameterName=Naziv
#XFLD: Placeholder for string input type
placeholderText=Unesite zadanu vrijednost
#XFLD: Default Value
defaultValue=Zadana vrijednost
#XFLD defined by
definedBy=Definirao
#XMSG Error message for empty input parameter name
VAL_PARAM_EMPTY_NM=Naziv parametra prazan.
#XMSG Error message for parameter name not unique
VAL_PARAM_NAME_NOT_UNIQUE=Naziv parametra nije jedinstven.
#XMSG: Error message when object is not deployed yet
cannotViewNotDeployed=Objekt "{0}" nikad nije uveden. Uvedite ga prije prikazivanja.
#XFLD: Placeholder for string input type
stringPlaceholderText=Unesi niz
#XFLD: Placeholder for integer input type
intPlaceholderText=Unesi broj
#XFLD: Placeholder for decimal input type
decPlaceholderText=Unesi decimalnu vrijednost
#NOTR: Placeholder for date format Filter
dateFormatFilter=YYYY-MM-DD
#NOTR: Placeholder for time format Filter
timeFormatFilter=HH:mm:ss
#NOTR: Placeholder for date and time format Filter
dateTimeFormatFilter=YYYY-MM-DD, HH:mm:ss
#XTXT: Text for true dropdown of boolean datatype for input parameter - the value should be in small case matching the actual data
param_true=točno
#XTXT: Text for false dropdown of boolean datatype for input parameter - the value should be in small case matching the actual data
param_false=netočno
#XTXT: Text for None dropdown of boolean datatype for input parameter
none=Nijedno
#XFLD: Input Parameter Name
OperatorName=Naziv
#XFLD: Input Parameter Value
Value=Vrijednost
#XBTN: run button of run with parameter dialog
TXT_RUN=Izvedi
#XFLD
InputParameters=Ulazni parametri
#XTXT: Text for dialog box for input parameter
EnterParameterValues=Unesite vrijednosti parametra za izvođenje vašeg toka transformacije.
#XMSG: Error message for invalid number input
INVALID_NUMBER=Unesite valjan broj.
#XMSG: Error message for invalid decimal input
INVALID_DECIMAL=Unesite valjanu decimalu.
#XMSG: Error message for invalid date input
INVALID_DATE=Unesite valjan datum (GGGG-MM-DD).
#XMSG: Error message for invalid time input
INVALID_TIME=Unesite valjano vrijeme (HH:mm:ss).
#XMSG: Error message for invalid date and time input
INVALID_DATETIME=Unesite valjan datum i vrijeme (GGGG-MM-DD, HH:mm:ss).
#XMSG: Error message for invalid Boolean input
INVALID_BOOLEAN=Unesite valjanu Booleovu vrijednost.
#XMSG: Error message for invalid generic input
INVALID_INPUT=Nevaljan unos.
#XTIP: Tooltip for parameter value
more=Više
#XTIT
mappingDialogTitle=Mapiranje ulaznih parametara
