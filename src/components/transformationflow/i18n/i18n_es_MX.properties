#~~~~~~~~~~~~~~~~~~~~~~~ Properties Panel texts starts here ~~~~~~~~~~~~~~~~~~~~
#XTIT
properties=Propiedades
#XTIT
general=General
#XFLD
businessName=Nombre empresarial
#XFLD
technicalName=Nombre técnico
#XFLD
loadType=Tipo de carga
#XFLD
deltaCaptureTableName=Nombre de tabla de captura delta
#XSEL
initialOnly=Inicial únicamente
#XSEL
initialAndDelta=Inicial y delta
#XCKL
truncate=Truncar
#XCKL
deleteAllBeforeLoading=Eliminar todo antes de la carga
#XTIT
columns=Columnas
#XTIT
mappings=Asignaciones
#XFLD
columnDataType=Tipo de datos
#XFLD
search=Búsqueda
#XBUT
autoMap=Asignación automática
#XBUT
removeAllMappings=Quitar todas las asignaciones
#XMSG
noTargetInputs=Necesita definir un operador de vista de transformación para ver la información de asignación.
#XMSG
noColumnsData=No hay columnas para mostrar.
#XTOL
@validateModel=Mensajes de validación
#XTOL
@hierarchy=Jerarquía
#XTOL
@columnCount=Número de columnas
#XTOL
info=Información
#XTOL
cdcColumn=Columna CDC
#XTIT
statusPanel=Estado de ejecución
#XTOL
schedule=Programar
#XTOL
navToMonitoring=Abrir en el supervisor de flujo de transformación
#XBTN
createSchedule=Crear programa
#XBTN
editSchedule=Editar programa
#XBTN
deleteSchedule=Eliminar programa
#XFLD
lastRun=Última ejecución
#XFLD
status=Estado
#XMSG: Error run status cannot be fetched
errorDetails=No se puede recuperar el esto de ejecución.
#XLNK
viewDetails=Ver detalles
#XMSG: Error data is not loading from the server
backendError=Parece que no se están cargando los datos del servidor en este momento. Vuelva a intentarlo más tarde.
#XMSG: Error transformation flow run failed. open data integration monitoring for more details
viewDetailsFallback=Ocurrió un error en el inicio de la ejecución del flujo de transformación. Navegue hasta la aplicación de supervisión para más detalles.
#XMSG
statusCompleted=Finalizado
#XMSG
statusRunning=En ejecución
#XMSG
statusFailed=Error
#XMSG
statusNotExecuted=Aún sin ejecutar
#XBTN
editColumns=Editar columnas
#XBUT: button in the properties panel of the target table
createNewTargetTable=Crear nueva tabla de destino
#XMSG: message shown in the properties panel of target table when it is not defined.
defineTargetInformation=Para crear una nueva tabla de destino, haga clic en el botón Crear nueva tabla de destino a continuación. Para usar una tabla existente como la tabla de destino, puede arrastrar y soltar una tabla desde el repositorio hasta el lienzo.
#XTIT
defineTarget=Definir tabla de destino
#XTIT
createViewTransform=Crear una vista de transformación
#XMSG: message shown in the properties panel of view transform when it is not defined.
createViewTransformInformation=Si desea iniciar su flujo de transformación, haga clic en el botón correspondiente a continuación para crear una vista de transformación.
#XMSG: message shown in the properties panel of view transform when it is not defined.
createViewTransformInformationDndSpark=Para iniciar el flujo de transformación, defina un origen arrastrando y soltando una tabla de origen desde el panel del repositorio o haciendo clic en el botón relevante a continuación.
#XBUT: button in the properties panel of view transform
sqlViewTransform=Transformación de vista SQL
#XBUT: button in the properties panel of view transform
graphicalViewTransform=Transformación de vista gráfica
#XFLD: view transform operator type - the value will be either SQL View or Graphical View
viewTransformType=Tipo
#XMSG: value of type property
sqlView=Vista SQL
#XMSG: value of type property
graphicalView=Vista gráfica

#~~~~~~~~~~~~Script properties text~~~~~~~~~~~~~~~~~~~~~
#XBTN: select all columns button text
selectAllColumns=Seleccionar todo
#XBUT: Add column menu button
addColumn=Agregar columna
#XBUT: Create new column menu button
createNewColumn=Crear columna nueva
#XBUT: Edit column menu button
editColumn=Editar columna
#XBUT: Delete button
deleteColumn=Eliminar
#XTIT: title for select columns dialog
columnSelectionDialogTitle=Seleccionar columnas
#XMSG: message shown in the select columns dialog
columnSelectionNoDataText=No se encontraron columnas
#XFLD
columnOrigin=Origen
#XFLD
columnLength=Longitud
#XFLD
columnPrecision=Precisión
#XFLD
columnScale=Escala
#XBTN: save button text
save=Guardar
#XTOL: Tooltip text for more button
tooltipTxt=Más
#XTOL: Tooltip text for new column
newColumn=Nueva columna
#XTIT: title for the script editor
scriptTitle=Script
#XMSG: Message strip text in the script editor
messageStripText=Acceda al portal de ayuda para obtener información y documentación completa.
#XMSG: Learn more link text
linkText=Más información
#XMSG: For a data type with length, the length cannot be greater than the maximum value.
VAL_ELEMENT_PROPERTY_LENGTH_GREATER_THAN_MAX=El valor ingresado para la longitud no puede ser mayor que el valor máximo ({0}).
#XMSG: For a data type with length, the length cannot be negative.
VAL_ELEMENT_PROPERTY_LENGTH_LESS_THAN_ZERO=El valor ingresado para la longitud no puede ser menor que 0.
#XMSG: For a data type with length, the length cannot be zero.
VAL_ELEMENT_PROPERTY_LENGTH_CANNOT_BE_ZERO=El valor ingresado para la longitud no puede ser {0}.
#XMSG: The attribute of an Entity has a data type that must have a length.
VAL_ELEMENT_DATATYPE_NO_LENGTH=Ingrese un valor para la longitud.
#XMSG: The attribute of an Entity has a data type that must have precision.
VAL_ELEMENT_DATATYPE_NO_PRECISION=Ingrese un valor para la precisión.
#XMSG: For a data type with precision, the precision cannot be greater than the maximum value.
VAL_ELEMENT_PROPERTY_PRECISION_GREATER_THAN_MAX=El valor ingresado para la precisión no puede ser mayor que {0}.
#XMSG: For a data type with precision, the precision cannot be less than the minimum value.
VAL_ELEMENT_PROPERTY_PRECISION_LESS_THAN_MIN=El valor para la precisión no puede ser menor que {0}.
#XMSG: For a data type with precision and scale, the precision cannot be less than the scale.
VAL_ELEMENT_PROPERTY_PRECISION_LESS_THAN_SCALE=El valor ingresado para la precisión no puede ser menor que la escala ({0}).
#XMSG: The attribute of an Entity has a data type that must have scale.
VAL_ELEMENT_DATATYPE_NO_SCALE=Ingrese un valor para la escala.
#XMSG: For a numeric data type like Decimal(precision, scale), the scale cannot be negative.
VAL_ELEMENT_PROPERTY_SCALE_LESS_THAN_ZERO=El valor ingresado para la escala no puede ser menor que 0.
#XMSG: For a numeric data type like Decimal(precision, scale), the scale cannot be greater than the precision.
VAL_ELEMENT_PROPERTY_SCALE_GREATER_THAN_PREC=El valor ingresado para la escala no puede ser mayor que la precisión ({0}).
#~~~~~~~~~~~~~~~~~~~~~~~ Properties Panel texts ends here ~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Diagram texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XTOL: diagram tool item
zoomToFit=Ampliar para ajustar
#XTOL: tooltip for context pad to open data preview (new text)
dataViewer=Visor de datos
#XTOL: tooltip for context pad to open data preview (old text)
previewData=Vista previa de datos
#XTOL: tooltip for context pad to open impact and lineage editor
openImpactLineage=Análisis de impacto y linaje
#XTOL: tooltip for context pad to open object in new tab
openInNewTab=Abrir en una pestaña nueva
#XTOL: tooltip for context pad to create new target table
createTable=Crear tabla nueva
#XTOL: tooltip for context pad to edit view transform operator
edit=Editar
#XTOL: tooltip for context pad to clear the source or target node in the diagram
remove=Quitar
#XTOL: tooltip for context pad to define view transform operator using SQL editor
useSQLViewTransform=Transformación de vista SQL
#XTOL: tooltip for context pad to define view transform operator using Graphical View editor
useGraphicalViewTransform=Transformación de vista gráfica
#XMSG: Error shared table cannot be used as target
unsupportedTargetSharedTable=No puede usar una tabla compartida como tabla de destino en un flujo de transformación.
#XMSG: Error SAC artefact cannot be used as target
unsupportedSACTarget=No puede usar un objeto de solo lectura como un destino para un flujo de transformación.
#XMSG: Error Open SQL Schema/HDI Container table cnnot be used as target
unsupportedTargetOpenSchema=No es posible usar un esquema de base de datos o contenedor HDI como destino en un flujo de transformación.
#XMSG: Message to show when view transform validation is in progress
vtValidationInProgress=Validación de vista de transformación en curso.
#XMSG: welcome screen message when the editor is new or empty
welcomeText=Para comenzar, haga clic en un nodo a fin de definir una vista de transformación o agregar/crear una tabla de destino.
#XMSG: new welcome screen message when drag and drop of table is enabled (spark)
welcomeTextDndSpark=Comience haciendo clic en un nodo o arrastrando y soltando desde el panel del repositorio para agregar un origen o una tabla de destino
#XBUT: confirmation button to switch graphical view source to sql view source in the warning dialog
confirmSQLViewTransform=Confirmar
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=La actualización de inquilino está en curso.

#~~~~~~~~~~~~~~~~~~~~~~~ Diagram texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Change Management texts starts here ~~~~~~~~~~~~~~~~~~~
#XTIT: title for the change management dialog
changeManagementTitle=Actualizaciones de destino
#XTIT: title for the change management dialog
changeManagementTitleSourceOrTarget=Actualizaciones de origen/destino
#XTIT
objectDisplayName=Nombre para mostrar
#XTIT: name of the user who modifed the object
changedBy=Modificado por
#XTIT: the time when the object is modified
changedOn=Fecha de modificación
#XTIT
objectType=Tipo
#XBUT
ok=OK
#XMSG: message in the change management dialog
reviewText=Revise las modificaciones. Necesitará guardar y, luego, volver a implementar el flujo de transformación para que los cambios surtan efecto.
#XMSG: message in the change management dialog
changesText=La siguiente tabla de destino usada en el flujo de transformación se modificó (consulte los mensajes de validación para obtener detalles):
#XMSG: message in the change management dialog
changesTextSourceOrTarget=La siguiente tabla de orígenes/destino usada en el flujo de transformación se modificó (consulte los mensajes de validación para obtener detalles):
#XMSG: Info column removed from the target: params: {0} label of the node
validationChangeMgmtDelColMsgHdr=Columnas quitadas de {0}.
#XMSG: Info column data type updated in the target: params: {0} label of the node
validationChangeMgmtUpdDTColMsgHdr=El tipo de datos de las siguientes columnas en {0} se cambió.
#XMSG: Info column added to the target: params: {0} label of the node
validationChangeMgmtNewColMsgHdr=Se agregaron columnas a {0}.
#NOTR: description text of message params: {0} list (with line breaks) of columns
validationChangeMgmtColsMsgDesc={0}
#XMSG: data type change information for the column params: {0} column name {1} old data type {2} new data type
validationChangeMgmtUpdDTColMsgDesc={0}, cambió de {1} a {2}.
#XMSG: title text of message, change of key/isKey: params: {0} number of columns
validationChangeMgmtUpdKeyColMsgHdr=Columnas con claves configuradas o quitadas ({0}).
#XMSG: key property of a column has been set
validationChangeMgmtKeyPropertySet=El campo ahora es un campo clave.
#XMSG: key property of a column has been removed
validationChangeMgmtKeyPropertyRemoved=El campo ya no es un campo clave.
#XMSG: title text of message, change of business name: params: {0} number of columns
VAL_CHANGE_MGMT_UPD_BN_COL_MSG_HDR=Columnas con nombre empresarial actualizado ({0}).
#XMSG: generic message to fill description of message with a list of column names: params: {0} list (with line breaks) of columns
VAL_CHANGE_MGMT_COLUMNS_MSG_DESC={0}
#XMSG: displayed next to a column name to indicate whether the value is new (maintained for the first time). Example: The business name of a column got maintained and before only the technical name was in place
VAL_VALUE_NEW=(nuevo)
#XMSG: displayed next to a column name to indicate whether the value has been resetted/deleted. Example: The business name of a column or source has been removed
VAL_VALUE_DELETED=(eliminado)
#XFLD: Label for source type node change management
source=Origen
#XFLD: Label for target type node change management
target=Destino
#XFLD: Tooltip label for view icon
view=Vista
#~~~~~~~~~~~~~~~~~~~~~~~ Change Management texts ends here ~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary Editor texts starts here ~~~~~~~~~~~~~~~~~~~~

#XTIT: title of the secondry diagram editor (graphical view editor)
graphicalViewEditor=Editor de vista gráfica
#XTIT: title of the secondry diagram editor (sql view editor)
sqlViewEditor=Editor de vista SQL
#XTIT: title of the panel in Entity/Source properties panel
deltaSettings=Opciones delta
#XFLD: decides which object of delta table to be used(Delta Capture or All Active Records)
LoadFromLocalTable=Cargar desde tabla local
#XFLD: decides which object of delta table to be used(Delta Capture or All Active Records)
LoadFromTable=Cargar desde tabla
#XRBL: only delta records will be processed when this option is selected
deltaCapture=Captura delta
#XRBL: all the records will be processed when this option is selected
allActiveRecords=Todos los registros activos
#XMSG: Error multiple delta source exist with Delta Capture selected
validationMultipleDeltaSource=Solo una tabla de origen puede tener el valor Captura delta para la opción Cargar desde tabla local.
#XMSG: Error Open SQL Schema/HDI Container table cannot be used as a source: params: {0} technical name of the object
validationOpenSchemaSource=No es posible usar un esquema de base de datos o tabla de contenedor HDI "{0}" como origen en un flujo de transformación.
#XMSG: Error remote table cannot be used as a source :params: {0} technical name of the object
validationRemoteTableSource=No es posible usar una tabla remota "{0}" como origen en un flujo de transformación.
#XMSG: Error view cannot be used as a source :params: {0} technical name of the object
validationViewSource=No es posible usar una vista "{0}" como origen en un flujo de transformación.
#XMSG: Error Aggregation node cannot be used with delta source(source with Delta Capture selected)
validationAggregationWithDelta=El operador de agregación no se puede usar si alguna de las tablas de origen tiene el valor Captura delta para la opción Cargar desde tabla local.
#XMSG: Error Left Outer Join with Delta Table as Outer Operand
validationErrorLeftOuterJoinWithDeltaTable=No se puede usar una tabla de origen para la que la configuración de captura delta está habilitada como el operando externo de una UNIÓN EXTERNA IZQUIERDA.
#XMSG: Info: Outer Join with Delta Table as Outer Operand
validationErrorOuterJoinWithDeltaTable=El uso de una tabla de origen con habilitación delta como el operando externo de la unión izquierda o derecha puede dar lugar a un error durante la ejecución.
#XMSG: Error Union with Delta Source
validationErrorUnionWithDeltaSource=El operador de unión no se puede usar si alguna de las tablas de origen tiene el valor Captura delta para la opción Cargar desde tabla.
#XMSG: Error Timestap column should not be used in any calculated column
validationForCalculatedColumnWithDelta=La columna {0} no se puede usar en la columna calculada {1}.
#XMSG: Warning
invalidSQLViewDefinitionConfirmation=Falló la validación de la instrucción SQL.
#XMSG: Warning SQL statment contains validation error
sqlErrorConfirmation=La instrucción SQL contiene errores de validación.
#XMSG: Error multiple delta source exist in the sql statement
validationSQLEditorMultipleDeltaSource=Existen múltiples orígenes delta en la instrucción SQL. Solo se puede usar una tabla delta como origen.
#XBTN: delete button on parameters panel
deleteParameters=Eliminar
#XMSG: loading message shown in busy dialog when parameter is appended in sql statement
loadingParameters=Cargando parámetros.
#XMSG: Open SQl schema source using one remote source directly or indirectly
validationOpenSchemaRemoteSource=El objeto "{0}" hace referencia a la tabla remota "{1}" (directa o indirectamente).
#XMSG: Open SQl schema source using multiple remote sources directly or indirectly
validationOpenSchemaRemoteSources=El objeto "{0}" hace referencia a las siguientes tablas remotas (directa o indirectamente): {1}
#XMSG: Open SQL schema source DAC validation
validationOpenSchemaDAC=No se puede agregar el objeto "{0}". Se aplicó un control de acceso de datos a este objeto (o un objeto dependiente).

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary Editor texts ends here ~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Dialog Boxes texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error new editor is opened and the object is not saved and Run button is clicked
runUnsavedError=Necesita guardar su flujo de transformación antes de que pueda ejecutarlo.
#XTIT
runError=Error
#XMSG: Error unsaved changes in the editor and Run button is clicked
runModifiedError=Hay cambios sin guardar en el flujo de transformación. Guarde el flujo de transformación.
#XTIT
runWarning=Advertencia
#XMSG: Warning the object has changes to deploy and Run button is clicked
runDeployedVersion=La última versión del flujo de transformación aún no se implementó. Se ejecutará la versión implementada más reciente. ¿Quiere continuar?
#XBTN: run with error or warning
runAnyway=Ejecutar flujo de transformación
#XBTN
close=Cerrar
#XMSG: Error object has validation error
runWithValidationErrors=El flujo de transformación contiene errores de validación. La ejecución del flujo de transformación puede fallar.
#XTIT
waitBusy=Espere.
#XMSG: initiating transformation flow run
runBusy=Preparando los datos…
#XMSG: Success
runSuccess=Ejecución del flujo de transformación iniciada
#XMSG: Error
runFail=Se produjo un error al ejecutar el flujo de transformación
#XTIT: loading dialog title
loading=Cargando
#XMSG: fetching run details from the server
loaderDetails=Recuperando detalles del servidor
#XMSG: Error graphical view transform with error cannot be converted to SQL view transform
convertGVTransformToSQLViewTransformError=No se puede convertir la transformación de vista gráfica con errores a una transformación de vista SQL.
#~~~~~~~~~~~~~~~~~~~~~~~ Dialog Boxes texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Validation texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error Duplicate technical name validation message : params: {0} technical name of the object
validationDuplicateTechnicalName=El objeto ''{0}'' ya existe en el repositorio.
#XMSG: Error There should be a technical name for the target table.
validationEmptyTechnicalTargetTable=Ingrese un nombre técnico para la tabla de destino.
#XMSG: Error View transform operator not defined(template node is shown in the diagram)
validationEmptyViewTransform=Aún no se definió un operador de vista de transformación para el flujo de transformación.
#XMSG: Error source operator not defined(template node is shown in the diagram) in large system spaces
validationEmptySource=Aún no se definió un operador de origen para el flujo de transformación.
#XMSG: Error Target table not defined (template node is shown in the diagram)
validationEmptyTargetTable=El flujo de transformación no contiene una tabla de destino.
#XMSG: Error validation message for incompatible data type mapping in target : params: {0} source column name {1} source column data type {2} target column name {3} target column data type {4} label of the target node
validationTargetColumnMappingIncompatible={0} ({1}) no es compatible con {2} ({3}) en el operador {4}.
#XMSG: Error validation message for incompatible mapping for timestamp column :  params: {0} & {1} and compatible source and column names
validationSouceTargetColumnMappingIncompatibleTimestamp=La columna {0} solo se puede asignar a la columna {1} en la tabla de destino.
#XMSG: Error No mappping defined in the target operator : params: {0} label of the target node
validationTargetMappingMissing=No hay ninguna asignación definida en el operador {0}.
#XMSG: Error No mappping defined in the target operator : params: {0} name of the primary key column {1} label of the target node
validationTargetMappingMissingForPrimaryKeyColumn=No hay ninguna asignación definida para la columna de clave "{0}" en el operador {1}.
#XMSG: warning: No mappping defined for change Type Column
validationTargetMappingMissingForChangeTypeColumn=El campo {0} de origen no está asignado al campo {0} de destino.
#XMSG: Info: For change Date Column, The timestamp of this field will be set to the time that the transformation flow runs
validationTargetMappingMissingForChangeDateColumn=El campo {0} no es relevante para asignación. La marca de tiempo de este campo se configurará a la hora en que se ejecuta el flujo de transformación.
#XMSG: Error No columns defined in the target operator : params: {0} label of the target node
validationEmptyColumns=No se definieron columnas para el operador "{0}".
#XMSG: Error load type "Initial and Delta" not supported when the truncate mode is enbled in the target table properties
validationTruncateInvalidMode=El tipo de carga Inicial y delta no se admite si la opción Truncar está habilitada para la tabla de destino.
#XMSG: Error load type "Initial and Delta" not supported when the deleteAllBeforeLoading mode is enbled in the target table properties
validationDeleteAllBeforeLoadingInvalidMode=El tipo de carga Inicial y delta no se admite si la opción Eliminar todo antes de la carga está activada para la tabla de destino.
#XMSG: Error Object has duplicated technical name. : params: {0} technical name of the object
validationDuplicateTargetTechnicalName=El objeto "{0}" ya existe en el repositorio. Ingrese otro nombre.
#XMSG: Error technical name field is empty
validationEmptyTechnicalName=Ingrese un nombre técnico para la tabla.
#XMSG: Error business name field is empty
validationEmptyBusinessName=Ingrese un nombre empresarial para la tabla.
#XMSG: Error message for data type mismatch in target : params: {0} source column name {1} source column data type {2} target column name {3} target column data type
datatypeMismatchTarget=El tipo de datos debe coincidir para crear la asignación. {0} ({1}) no es compatible con {2} ({3}).
#XMSG: Error load type "Initial and Delta" not supported for non-delta source
validationNonDeltaSource=El tipo de carga Inicial y delta no se admite si el origen no tiene habilitación delta.
#XMSG: Error load type "Initial and Delta" not supported for non-delta target
validationNonDeltaTarget=El tipo de carga Inicial y delta no se admite si la tabla de destino no tiene habilitación delta.
#XMSG: Error truncate mode is not supported for delta-enabled target table
validationTruncateDeltaTarget=El modo de truncado no se admite para la tabla de destino con activación delta.
#XMSG: Info Message for upsert of records when target node has truncate off and a key column
validationTruncateOffandKeyColumn=Los registros leídos que usan el operador de transformación de vista se cargan en la tabla de destino mediante la operación UPSERT.
#XMSG: Error deleteAllBeforeLoading mode is not supported for delta-enabled target table
validationDeleteAllBeforeLoadingDeltaTarget=El modo Eliminar todo antes de la carga no se admite para la tabla de destino con activación delta.
#XMSG: Info Message for upsert of records when target node has deleteAllBeforeLoading off and a key column
validationDeleteAllBeforeLoadingOffandKeyColumn=Los registros leídos que usan el operador de transformación de vista se cargan en la tabla de destino mediante la operación UPSERT.
#XMSG: delta-enabled source cannot be used with non-delta target : params: {0} label of the target node
validationDeltaSourceNonDeltaTarget=El origen con activación delta no se puede usar el destino con activación delta "{0}".
#XMSG: Error graphicalview transform has a validation error.
validationGraphicalViewTransformHasError=La transformación de vista gráfica tiene uno o más errores. Para verlos, edite la transformación de vista gráfica y haga clic en Mensajes de validación.
#XMSG: Error sql view transform has a validation error.
validationSQLViewTransformHasError=La transformación de vista SQL tiene uno o más errores. Para verlos, edite la transformación de vista SQL y haga clic en Mensajes de validación.
#XMSG: Error graphical view transform has a change management error.
validationGraphicalViewTransformHasChangeManagement=Una o más tablas de origen cambiaron. Para ver estos cambios, edite la transformación de vista gráfica y haga clic en Mensajes de validación.
#XMSG: Error sql view transform has a change management error.
validationSQLViewTransformHasChangeManagement=Una o más tablas de origen cambiaron. Para ver estos cambios, edite la transformación de vista SQL y haga clic en Mensajes de validación.
#XMSG: Info load type set to intial and delta as source and traget are delta tables
validationInitialDeltaLoadType=Dado que la característica de captura delta está habilitada tanto para tablas de origen como de destino, el tipo de carga predeterminado es Inicial y delta.
#XMSG: Info reset target table watermark
validationDeltaTargetTableChanged=La tabla de destino cambió. Solo se transferirán los datos delta a la nueva tabla de destino. Si quiere transferir todos los datos a la tabla de destino, puede restablecer la marca de agua en el Supervisor de integración de datos.
#XMSG: Error delta table from BW bridge cannot be used in SQL Script view transform
validationBWDeltaSourceInSQLScriptTransform=Para cargar cambios delta desde una tabla remota ubicada en un espacio de puente de BW mediante una transformación de vista SQL, el lenguaje debe ser SQL (consulta estándar). SQLScript (función de tabla) no se admite.
#XMSG: No primary key column in new delta target
validationNoKeyColumn=Para habilitar la configuración de captura delta, la tabla de destino debe contener al menos una columna de clave.
#XMSG: Duplicate column names in new delta target
validationColumnNameNotUnique=Los nombres de columna deben ser únicos. {0} se repite varias veces en la tabla "{1}".
#XMSG: Missing CDC Columns in new delta target
validationMissingCDCColumns=La configuración de captura delta está habilitada para la tabla {0}, pero faltan las columnas de captura delta.
#XMSG: Validate non LTF target in large system space
validationLTFDeltaTargetLargeSystemSpace=La tabla de destino "{0}" debe tener habilitación delta con almacenamiento de archivo en un gran espacio del sistema.
#XMSG: Validation error if a transformation flow from hana runtime space is imported into large system space
validationNonLsaTFInLsaSpace=El flujo de transformación creado con tiempo de ejecución de HANA no se puede usar en un gran espacio del sistema.
#XMSG: Always show a validation info message in the python node to inform the user to update the python script manually to generate the data to match the output columns.
validationUpdatePythonScript=Actualice el esquema de columnas de salida para que coincida con las columnas que muestra el marco de datos en el script de Python.
#XMSG: Error message for the python operator for changed CDC columns
validationChangedCDCColumns=Las columnas de captura delta no existen o se modificaron en el script de Python.
#XMSG: Validation error if a transformation flow from spark runtime space is imported into non large system space
validationLsaTFInNonLsaSpace=Un flujo de transformación con tiempo de ejecución de Spark no se puede crear en un espacio con almacenamiento en la base de datos de SAP HANA (Disco y En memoria).
#~~~~~~~~~~~~~~~~~~~~~~~ Validation texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Data Preview texts starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error data preview is not available for selected operator
dataPreviewNotSupp=La función de vista previa de datos no está disponible para este operador.
#XMSG: Error data preview is not available as the selected node is a template.
dataPreviewTemplate=La función de vista previa de datos no está disponible para este nodo.
#XMSG: Error data preview is not available as the selected node is a new target table. i.e. the target table is not created
dataPreviewNewTarget=La función de vista previa de datos no está disponible para esta tabla de destino.
#XMSG: Error data preview is not avalibale as there are no columns in the selected node
dataPreviewEmptyColumns=La función de vista previa de datos no está disponible porque la tabla no tiene columnas visibles.
#XMSG Error insuffcient privileges to view data
dataPreviewInSufficientPrivileges=No tiene los privilegios suficientes para ver estos datos
#XMSG Not suffcient privileges to view data
dataPreviewNoPrivileges=No tiene permitido ver estos datos.
#XTIT Data preview problems tab
txtProblems=Errores
#XMSG: Error data preview is not available for SQL Script
dataPreviewNotSupportedSQLScript=La función de vista previa de datos no está disponible para SQLScript (función de tabla).
#XMSG: Error message for data preview with Cross space object with Input parameters
dataPreviewNotSupportedRemoteTableWithParam=No se puede obtener una vista previa de los datos:\nLa vista previa de los datos para transformación de vista con objetos de espacio transversal que contienen parámetros de entrada no se admite.
#~~~~~~~~~~~~~~~~~~~~~~~ Data Preview texts ends here~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG:view transform is modified and changes are applied to the editor
viewTransformChangesUpdated=Se aplicaron todos los cambios a la transformación de vista.
#XMSG:confirm switch from graphical view to sql view in the view transform
convertToSQLViewConfirmation=Está cambiando el tipo de su transformación de vista a una transformación de vista SQL. Este cambio no se puede deshacer.
#XTIT Save Dialog param
modelNameTransformationFlow=Flujo de transformación
#XMSG: Info automap operation cannot be performed due non matching columns
autoMapInvalid=El nombre y el tipo de datos de las columnas deben coincidir para la asignación automática.
#XMSG Info: mapping already exists
mappingExists=La asignación ya existe.
#XMSG: There are invalid columns in the target table
invalidColumns=Las siguientes columnas no se pueden asignar y, por lo tanto, se quitarán.
#XFLD: Label for package select
package=Paquete

#XMSG warning for repository package assignment changed for Object name {1}
VAL_PACKAGE_CHANGED=Asignó este objeto al paquete {1}. Haga clic en Guardar para confirmar y validar este cambio. Tenga presente que, luego de guardar este cambio, la asignación a un paquete no se puede deshacer en este editor.
#XMSG warning for repository package dependency issue for object name{0}
MISSING_DEPENDENCY=Las dependencias del objeto "{0}" no se pueden resolver en el contexto del paquete ''{1}''.
#XFLD: Mapped to parameter name{0}
mappedTo=Se asignó a: {0}
#XFLD: Value parameter default Value{0}
defaultVal=Valor: {0}
#XFLD: Not Mapped
notMapped=Sin asignar
#XMSG : Validation message for views with remote table name {1} for view name {0}
validateViewWithRemoteTables=La vista "{0}" hace referencia a las siguientes tablas remotas (directa o indirectamente): "{1}"
#XMSG : Validation message for views with DAC for view name {0}
validationDAC=No se puede agregar la vista "{0}". Se aplicó un control de acceso de datos a esta vista (o una vista dependiente).
#XMSG : Validation message for objects with remote tables, remote table name{0} in sql editor,
validateSqlRemoteTables=La instrucción SQL contiene uno o más objetos que hacen referencia a las siguientes tablas remotas (directa o indirectamente): "{0}"
#XBTN : Set Value
setValue=Establecer valor
#XBTN : Map TO
mapTo=Asignar a
#XMSG : This source name{0} contains input parameters.
parameterMappingInfo=Este {0} de origen contiene parámetros de entrada. Para cada parámetro, puede establecer un valor o realizar una asignación a un parámetro de entrada en su vista.
#XBTN : Cancel
cancel=Cancelar
#XTIT : Set Value for Input Parameter name {0}
setValueForParam=Establecer valor para el parámetro de entrada {0}
#XMSG : Value
enterValue=Valor
#XMSG : Enter Valid Decimal value with valid precision and scale
VAL_ENTER_VALID_DECIMAL_GEN=Ingrese un valor decimal válido con precisión {0} y escala {1}.
#XFLD : Runtime
runtime=Tiempo de ejecución
#XFLD: Storage
storage=Almacenamiento
#XMSG: Info message for skipping SQL validation when tenant upgrade is in progress
VAL_TENANT_UPGRADE_IN_PROGRESS=La validación de la Transformación de vista SQL se omitió porque la actualización del inquilino estaba en curso. Valide la Transformación de vista SQL antes de implementar el flujo de transformación.
#XMSG: Error LTF cannot be used as a source :params: {0} technical name of the object
validationLTFSource=No es posible usar la tabla local (archivo) "{0}" como origen en un flujo de transformación (spark).
#XMSG: Error shared tables cannot be used as a source :params: {0} technical name of the object
validationSharedSource=No es posible usar la tabla compartida (archivo) "{0}" como origen en un flujo de transformación (spark).
#XMSG: Error Only LTF are supported in large system space
validationNonLTFSource=No es posible usar el origen "{0}" en un flujo de transformación (spark) porque no es una tabla local (archivo).
#~~~~~~~~~~~~~~~~~~~~~~~ Python Operator texts starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XTOL: Tooltip for python operator in context pad
pythonOperator=Operador de Python

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary sources properties starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Show Sources
showSources=Mostrar orígenes:
#XFLD: Qualified Name
qualifiedName=Nombre calificado
#XFLD: Space Name
spaceName=Nombre de espacio
#XFLD: Context Name
contextName=Nombre de contexto
#XFLD: Connection
connection=Conexión
#XFLD: Remote Table
remoteTable=Tabla remota
#XFLD: Active Records
activeRecords=Registros activos
#XFLD: Local Table
localTable=Tabla local
#XFLD: Local Schema
localSchema=Esquema local
#XMSG: no parameters message
noParameters=No hay parámetros disponibles
#XMSG: Validation Error in secondary source error message
secondarySourceErrorDialog=Los orígenes de transformación de la vista no se pueden mostrar porque hay al menos un error en Transformación de vista SQL.
#XMSG: Unable to load sources error message
secondarySourceLoadingErrorDialog=Los orígenes de transformación de la vista no se pueden cargar.
#XMSG: No sources error message
noSecondarySourceErrorDialog=La transformación de la vista no tiene ningún origen.
#XTIT Local Table (File)
LTFTable=Tabla local (Archivo)
#XMSG: Error data preview is not available for undeployed SQLScript Table function
dataPreviewNotDeployWarning=Los datos para la transformación de su vista no estarán visibles aquí hasta que se implementen todos los cambios. Implemente el flujo de transformación y vuelva a intentarlo.
#Input parameters
#XFLD : Input Parameters
parametersTitle=Parámetros de entrada ({0})
#XMSG: Error message for empty input parameters
noInputParameters=Actualmente, no hay parámetros disponibles. Para agregar algunos, presione el botón Editar.
#XTOL: Tooltip for edit input parameters
parametersTooltip=Editar parámetros de entrada
#XMSG: edit input parameters
editInputParameters=Editar parámetros de entrada
#XMSG: Create input parameters in the object to be used in inner filters
parametersDescription=Cree parámetros de entrada en el objeto para utilizarlos en filtros internos
#XBUT: Add new parameter
addParameters=Agregar nuevo parámetro
#XBUT: Delete parameter
deleteParameter=Eliminar parámetro
#XFLD: Data Type
parameterDatatype=Tipo de datos
#XFLD: Name
inputParameterName=Nombre
#XFLD: Placeholder for string input type
placeholderText=Ingresar valor predeterminado
#XFLD: Default Value
defaultValue=Valor predeterminado
#XFLD defined by
definedBy=Definido por
#XMSG Error message for empty input parameter name
VAL_PARAM_EMPTY_NM=El nombre del parámetro está vacío.
#XMSG Error message for parameter name not unique
VAL_PARAM_NAME_NOT_UNIQUE=El nombre del parámetro no es único.
#XMSG: Error message when object is not deployed yet
cannotViewNotDeployed=El objeto "{0}" nunca se implementó. Impleméntelo antes de visualizarlo.
#XFLD: Placeholder for string input type
stringPlaceholderText=Ingrese una string
#XFLD: Placeholder for integer input type
intPlaceholderText=Ingrese un número
#XFLD: Placeholder for decimal input type
decPlaceholderText=Ingrese un valor decimal
#NOTR: Placeholder for date format Filter
dateFormatFilter=YYYY-MM-DD
#NOTR: Placeholder for time format Filter
timeFormatFilter=HH:mm:ss
#NOTR: Placeholder for date and time format Filter
dateTimeFormatFilter=YYYY-MM-DD, HH:mm:ss
#XTXT: Text for true dropdown of boolean datatype for input parameter - the value should be in small case matching the actual data
param_true=verdadero
#XTXT: Text for false dropdown of boolean datatype for input parameter - the value should be in small case matching the actual data
param_false=falso
#XTXT: Text for None dropdown of boolean datatype for input parameter
none=Ninguno
#XFLD: Input Parameter Name
OperatorName=Nombre
#XFLD: Input Parameter Value
Value=Valor
#XBTN: run button of run with parameter dialog
TXT_RUN=Ejecutar
#XFLD
InputParameters=Parámetros de entrada
#XTXT: Text for dialog box for input parameter
EnterParameterValues=Ingrese valores de parámetros para ejecutar su flujo de transformación
#XMSG: Error message for invalid number input
INVALID_NUMBER=Ingrese un número válido.
#XMSG: Error message for invalid decimal input
INVALID_DECIMAL=Ingrese un decimal válido.
#XMSG: Error message for invalid date input
INVALID_DATE=Ingrese una fecha válida (AAAA-MM-DD).
#XMSG: Error message for invalid time input
INVALID_TIME=Ingrese una hora válida (HH:mm:ss).
#XMSG: Error message for invalid date and time input
INVALID_DATETIME=Ingrese una fecha y hora válidas (AAAA-MM-DD, HH:mm:ss).
#XMSG: Error message for invalid Boolean input
INVALID_BOOLEAN=Ingrese un valor booleano válido.
#XMSG: Error message for invalid generic input
INVALID_INPUT=Entrada no válida.
#XTIP: Tooltip for parameter value
more=Más
#XTIT
mappingDialogTitle=Asignar parámetros de entrada
