#~~~~~~~~~~~~~~~~~~~~~~~ Properties Panel texts starts here ~~~~~~~~~~~~~~~~~~~~
#XTIT
properties=Tulajdonságok
#XTIT
general=Általános
#XFLD
businessName=Üzleti név
#XFLD
technicalName=Technikai név
#XFLD
loadType=Adatátvétel típusa
#XFLD
deltaCaptureTableName=Deltarögzítési tábla neve
#XSEL
initialOnly=Csak kezdeti
#XSEL
initialAndDelta=Kezdeti és delta
#XCKL
truncate=Csonkolás
#XCKL
deleteAllBeforeLoading=Összes törlése adatátvétel előtt
#XTIT
columns=Oszlopok
#XTIT
mappings=Hozzárendelések
#XFLD
columnDataType=Adattípus
#XFLD
search=Keresés
#XBUT
autoMap=Automatikus hozzárendelés
#XBUT
removeAllMappings=Minden hozzárendelés eltávolítása
#XMSG
noTargetInputs=A nézet-hozzárendelési információk megtekintéséhez meg kell határoznia egy nézetátalakítási operátort.
#XMSG
noColumnsData=Nincs megjeleníthető oszlop.
#XTOL
@validateModel=Validálási üzenetek
#XTOL
@hierarchy=Hierarchia
#XTOL
@columnCount=Oszlopok száma
#XTOL
info=Infó
#XTOL
cdcColumn=CDC-oszlop
#XTIT
statusPanel=Futás állapota
#XTOL
schedule=Ütemezés
#XTOL
navToMonitoring=Megnyitás az átalakításifolyamat-figyelőben
#XBTN
createSchedule=Ütemezés létrehozása
#XBTN
editSchedule=Ütemezés szerkesztése
#XBTN
deleteSchedule=Ütemezés törlése
#XFLD
lastRun=Utolsó futás
#XFLD
status=Állapot
#XMSG: Error run status cannot be fetched
errorDetails=Nem lehet lehívni a futás állapotát.
#XLNK
viewDetails=Részletek megtekintése
#XMSG: Error data is not loading from the server
backendError=Úgy tűnik, hogy jelenleg nem töltődnek be az adatok a szerverről. Próbálkozzon újra később.
#XMSG: Error transformation flow run failed. open data integration monitoring for more details
viewDetailsFallback=Nem sikerült elindítani az átalakítási futást. További részletekért nyissa meg a Figyelés alkalmazást.
#XMSG
statusCompleted=Befejeződött
#XMSG
statusRunning=Fut
#XMSG
statusFailed=Sikertelen
#XMSG
statusNotExecuted=Még nem futott
#XBTN
editColumns=Oszlopok szerkesztése
#XBUT: button in the properties panel of the target table
createNewTargetTable=Új céltábla létrehozása
#XMSG: message shown in the properties panel of target table when it is not defined.
defineTargetInformation=Új céltábla létrehozásához kattintson az Új céltábla létrehozása gomra lentebb. Ha meglévő táblát szeretne céltáblaként használni, húzzon egy táblát a tárházból a vászonra.
#XTIT
defineTarget=Céltábla meghatározása
#XTIT
createViewTransform=Nézetátalakítás létrehozása
#XMSG: message shown in the properties panel of view transform when it is not defined.
createViewTransformInformation=Az átalakítási folyamat indításához hozzon létre nézetátalakítást a megfelelő gombra kattintva.
#XMSG: message shown in the properties panel of view transform when it is not defined.
createViewTransformInformationDndSpark=Az átalakítási folyamat indításához adja meg a forrást: húzzon át egy forrástáblát a tárházpanelről, vagy kattintson a megfelelő gombra lentebb.
#XBUT: button in the properties panel of view transform
sqlViewTransform=SQL-nézet-átalakítás
#XBUT: button in the properties panel of view transform
graphicalViewTransform=Grafikusnézet-átalakítás
#XFLD: view transform operator type - the value will be either SQL View or Graphical View
viewTransformType=Típus
#XMSG: value of type property
sqlView=SQL-nézet
#XMSG: value of type property
graphicalView=Grafikus nézet

#~~~~~~~~~~~~Script properties text~~~~~~~~~~~~~~~~~~~~~
#XBTN: select all columns button text
selectAllColumns=Összes kijelölése
#XBUT: Add column menu button
addColumn=Oszlop hozzáadása
#XBUT: Create new column menu button
createNewColumn=Új oszlop létrehozása
#XBUT: Edit column menu button
editColumn=Oszlop szerkesztése
#XBUT: Delete button
deleteColumn=Törlés
#XTIT: title for select columns dialog
columnSelectionDialogTitle=Oszlopok kiválasztása
#XMSG: message shown in the select columns dialog
columnSelectionNoDataText=Nem találhatók oszlopok
#XFLD
columnOrigin=Eredet
#XFLD
columnLength=Hossz
#XFLD
columnPrecision=Pontosság
#XFLD
columnScale=Skála
#XBTN: save button text
save=Mentés
#XTOL: Tooltip text for more button
tooltipTxt=Több
#XTOL: Tooltip text for new column
newColumn=Új oszlop
#XTIT: title for the script editor
scriptTitle=Szkript
#XMSG: Message strip text in the script editor
messageStripText=Információért és a teljes dokumentációért látogasson el a súgóportálra.
#XMSG: Learn more link text
linkText=További információ
#XMSG: For a data type with length, the length cannot be greater than the maximum value.
VAL_ELEMENT_PROPERTY_LENGTH_GREATER_THAN_MAX=A hosszként megadott érték nem lehet nagyobb a maximális értéknél ({0}).
#XMSG: For a data type with length, the length cannot be negative.
VAL_ELEMENT_PROPERTY_LENGTH_LESS_THAN_ZERO=A hosszként megadott érték nem lehet nullánál kisebb.
#XMSG: For a data type with length, the length cannot be zero.
VAL_ELEMENT_PROPERTY_LENGTH_CANNOT_BE_ZERO=A hosszként megadott érték nem lehet {0}.
#XMSG: The attribute of an Entity has a data type that must have a length.
VAL_ELEMENT_DATATYPE_NO_LENGTH=Adja meg a hossz értékét.
#XMSG: The attribute of an Entity has a data type that must have precision.
VAL_ELEMENT_DATATYPE_NO_PRECISION=Adja meg a pontosság értékét.
#XMSG: For a data type with precision, the precision cannot be greater than the maximum value.
VAL_ELEMENT_PROPERTY_PRECISION_GREATER_THAN_MAX=A pontosság értéke nem lehet nagyobb, mint {0}.
#XMSG: For a data type with precision, the precision cannot be less than the minimum value.
VAL_ELEMENT_PROPERTY_PRECISION_LESS_THAN_MIN=A pontosság értéke nem lehet kisebb, mint {0}.
#XMSG: For a data type with precision and scale, the precision cannot be less than the scale.
VAL_ELEMENT_PROPERTY_PRECISION_LESS_THAN_SCALE=A pontosság értéke nem lehet kisebb a skálánál ({0}).
#XMSG: The attribute of an Entity has a data type that must have scale.
VAL_ELEMENT_DATATYPE_NO_SCALE=Adja meg a skála értékét.
#XMSG: For a numeric data type like Decimal(precision, scale), the scale cannot be negative.
VAL_ELEMENT_PROPERTY_SCALE_LESS_THAN_ZERO=A skála értéke nem lehet nullánál kisebb.
#XMSG: For a numeric data type like Decimal(precision, scale), the scale cannot be greater than the precision.
VAL_ELEMENT_PROPERTY_SCALE_GREATER_THAN_PREC=A skála értéke nem lehet nagyobb a pontosságnál ({0}).
#~~~~~~~~~~~~~~~~~~~~~~~ Properties Panel texts ends here ~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Diagram texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XTOL: diagram tool item
zoomToFit=Nagyítás ablakméretre
#XTOL: tooltip for context pad to open data preview (new text)
dataViewer=Adatmegtekintő
#XTOL: tooltip for context pad to open data preview (old text)
previewData=Adatelőnézet
#XTOL: tooltip for context pad to open impact and lineage editor
openImpactLineage=Hatás- és származáselemzés
#XTOL: tooltip for context pad to open object in new tab
openInNewTab=Megnyitás új lapon
#XTOL: tooltip for context pad to create new target table
createTable=Új tábla létrehozása
#XTOL: tooltip for context pad to edit view transform operator
edit=Szerkesztés
#XTOL: tooltip for context pad to clear the source or target node in the diagram
remove=Eltávolítás
#XTOL: tooltip for context pad to define view transform operator using SQL editor
useSQLViewTransform=SQL-nézet-átalakítás
#XTOL: tooltip for context pad to define view transform operator using Graphical View editor
useGraphicalViewTransform=Grafikusnézet-átalakítás
#XMSG: Error shared table cannot be used as target
unsupportedTargetSharedTable=Az átalakítási folyamat céltáblája nem lehet megosztott tábla.
#XMSG: Error SAC artefact cannot be used as target
unsupportedSACTarget=Az átalakítási folyamat célja nem lehet írásvédett objektum.
#XMSG: Error Open SQL Schema/HDI Container table cnnot be used as target
unsupportedTargetOpenSchema=Az átalakítási folyamat célja nem lehet adatbázisséma vagy HDI-tároló.
#XMSG: Message to show when view transform validation is in progress
vtValidationInProgress=A nézetátalakítás validálása folyamatban van.
#XMSG: welcome screen message when the editor is new or empty
welcomeText=Először is kattintson az egyik csomópontra nézetátalakítás meghatározásához vagy céltábla hozzáadásához/létrehozásához.
#XMSG: new welcome screen message when drag and drop of table is enabled (spark)
welcomeTextDndSpark=Először is kattintson az egyik csomópontra, vagy húzza át a tárházpanelről a forrás vagy a céltábla hozzáadásához.
#XBUT: confirmation button to switch graphical view source to sql view source in the warning dialog
confirmSQLViewTransform=Megerősítés
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=A bérlő frissítése folyamatban van.

#~~~~~~~~~~~~~~~~~~~~~~~ Diagram texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Change Management texts starts here ~~~~~~~~~~~~~~~~~~~
#XTIT: title for the change management dialog
changeManagementTitle=Célfrissítések
#XTIT: title for the change management dialog
changeManagementTitleSourceOrTarget=Forrás-/célfrissítések
#XTIT
objectDisplayName=Megjelenített név
#XTIT: name of the user who modifed the object
changedBy=Módosította
#XTIT: the time when the object is modified
changedOn=Módosítás dátuma
#XTIT
objectType=Típus
#XBUT
ok=OK
#XMSG: message in the change management dialog
reviewText=Ellenőrizze a módosításokat. A módosítások érvénybe léptetéséhez mentenie kell és újra üzembe kell helyeznie az átalakítási folyamatot.
#XMSG: message in the change management dialog
changesText=Az átalakítási folyamatban használt alábbi céltábla módosult (részletek a validálási üzenetekben):
#XMSG: message in the change management dialog
changesTextSourceOrTarget=Az átalakítási folyamatban használt alábbi forrás-/céltáblák módosultak (részletek a validálási üzenetekben):
#XMSG: Info column removed from the target: params: {0} label of the node
validationChangeMgmtDelColMsgHdr=Oszlopok lettek eltávolítva a(z) {0} csomópontból.
#XMSG: Info column data type updated in the target: params: {0} label of the node
validationChangeMgmtUpdDTColMsgHdr=A(z) {0} következő oszlopainak adattípusa megváltozott.
#XMSG: Info column added to the target: params: {0} label of the node
validationChangeMgmtNewColMsgHdr=A(z) {0} csomóponthoz oszlopok lettek hozzáadva.
#NOTR: description text of message params: {0} list (with line breaks) of columns
validationChangeMgmtColsMsgDesc={0}
#XMSG: data type change information for the column params: {0} column name {1} old data type {2} new data type
validationChangeMgmtUpdDTColMsgDesc=A(z) {0} oszlop {1} adattípusúról {2} adattípusúra változott.
#XMSG: title text of message, change of key/isKey: params: {0} number of columns
validationChangeMgmtUpdKeyColMsgHdr=Kulcsok lettek beállítva vagy eltávolítva az oszlopoknál ({0}).
#XMSG: key property of a column has been set
validationChangeMgmtKeyPropertySet=A mező most kulcsmező.
#XMSG: key property of a column has been removed
validationChangeMgmtKeyPropertyRemoved=A mező már nem kulcsmező.
#XMSG: title text of message, change of business name: params: {0} number of columns
VAL_CHANGE_MGMT_UPD_BN_COL_MSG_HDR=Módosított üzleti nevű oszlopok ({0}).
#XMSG: generic message to fill description of message with a list of column names: params: {0} list (with line breaks) of columns
VAL_CHANGE_MGMT_COLUMNS_MSG_DESC={0}
#XMSG: displayed next to a column name to indicate whether the value is new (maintained for the first time). Example: The business name of a column got maintained and before only the technical name was in place
VAL_VALUE_NEW=(új)
#XMSG: displayed next to a column name to indicate whether the value has been resetted/deleted. Example: The business name of a column or source has been removed
VAL_VALUE_DELETED=(törölve)
#XFLD: Label for source type node change management
source=Forrás
#XFLD: Label for target type node change management
target=Cél
#XFLD: Tooltip label for view icon
view=Megtekintés
#~~~~~~~~~~~~~~~~~~~~~~~ Change Management texts ends here ~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary Editor texts starts here ~~~~~~~~~~~~~~~~~~~~

#XTIT: title of the secondry diagram editor (graphical view editor)
graphicalViewEditor=Grafikusnézet-szerkesztő
#XTIT: title of the secondry diagram editor (sql view editor)
sqlViewEditor=SQ-nézet-szerkesztő
#XTIT: title of the panel in Entity/Source properties panel
deltaSettings=Deltabeállítások
#XFLD: decides which object of delta table to be used(Delta Capture or All Active Records)
LoadFromLocalTable=Adatátvétel helyi táblából
#XFLD: decides which object of delta table to be used(Delta Capture or All Active Records)
LoadFromTable=Adatátvétel táblából
#XRBL: only delta records will be processed when this option is selected
deltaCapture=Deltarögzítés
#XRBL: all the records will be processed when this option is selected
allActiveRecords=Minden aktív rekord
#XMSG: Error multiple delta source exist with Delta Capture selected
validationMultipleDeltaSource=Csak egy forrástáblának lehet az értéke Deltarögzítés az Adatátvétel helyi táblából beállításnál.
#XMSG: Error Open SQL Schema/HDI Container table cannot be used as a source: params: {0} technical name of the object
validationOpenSchemaSource=Az átalakítási folyamat forrása nem lehet adatbázisséma vagy a(z) {0} HDI-tároló.
#XMSG: Error remote table cannot be used as a source :params: {0} technical name of the object
validationRemoteTableSource=Az átalakítási folyamat forrása nem lehet a(z) {0} távoli tábla.
#XMSG: Error view cannot be used as a source :params: {0} technical name of the object
validationViewSource=Az átalakítási folyamat forrása nem lehet a(z) {0} nézet.
#XMSG: Error Aggregation node cannot be used with delta source(source with Delta Capture selected)
validationAggregationWithDelta=Nem használható az összesítési operátor, ha bármelyik forrástáblánál a Deltarögzítés érték van megadva az Adatátvétel helyi táblából beállításnál.
#XMSG: Error Left Outer Join with Delta Table as Outer Operand
validationErrorLeftOuterJoinWithDeltaTable=Nem használható olyan forrástábla, amelynél a deltarögzítési beállítás egy LEFT-OUTER-JOIN külső operandusaként van engedélyezve.
#XMSG: Info: Outer Join with Delta Table as Outer Operand
validationErrorOuterJoinWithDeltaTable=Egy deltaképes forrástábla bal vagy jobb oldali külső operandusként való használata futási hibát eredményezhet.
#XMSG: Error Union with Delta Source
validationErrorUnionWithDeltaSource=Nem használható az unióoperátor, ha bármelyik forrástáblánál a Deltarögzítés érték van megadva az Adatátvétel táblából beállításnál.
#XMSG: Error Timestap column should not be used in any calculated column
validationForCalculatedColumnWithDelta=A(z) {0} oszlop nem használható a(z) {1} számított oszlopban.
#XMSG: Warning
invalidSQLViewDefinitionConfirmation=Nem sikerült validálni az SQL-utasítást.
#XMSG: Warning SQL statment contains validation error
sqlErrorConfirmation=Az SQL-utasítás validálási hibákat tartalmaz.
#XMSG: Error multiple delta source exist in the sql statement
validationSQLEditorMultipleDeltaSource=Több deltaforrás van az SQL-utasításban. Csak egy deltatábla használható forrásként.
#XBTN: delete button on parameters panel
deleteParameters=Törlés
#XMSG: loading message shown in busy dialog when parameter is appended in sql statement
loadingParameters=A paraméterek betöltése.
#XMSG: Open SQl schema source using one remote source directly or indirectly
validationOpenSchemaRemoteSource=A(z) {0} objektum (közvetlenül vagy közvetve) a következő távoli táblákra hivatkozik: {1}.
#XMSG: Open SQl schema source using multiple remote sources directly or indirectly
validationOpenSchemaRemoteSources=A(z) {0} objektum (közvetlenül vagy közvetve) a következő távoli táblákra hivatkozik: {1}
#XMSG: Open SQL schema source DAC validation
validationOpenSchemaDAC=A(z) {0} objektum nem adható hozzá. Egy adathozzáférés-vezérlő van alkalmazva erre az objektumra (vagy egy függő objektumra).

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary Editor texts ends here ~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Dialog Boxes texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error new editor is opened and the object is not saved and Run button is clicked
runUnsavedError=A futtatáshoz előbb menteni kell az átalakítási folyamatot.
#XTIT
runError=Hiba
#XMSG: Error unsaved changes in the editor and Run button is clicked
runModifiedError=Az átalakítási folyamat nem mentett módosításokat tartalmaz. Mentse az átalakítási folyamatot.
#XTIT
runWarning=Figyelmeztetés
#XMSG: Warning the object has changes to deploy and Run button is clicked
runDeployedVersion=Még nincs üzembe helyezve az átalakítási futás legújabb verziója. Az átalakítási futás legutóbb üzembe helyezett verziója fog futni. Folytatja?
#XBTN: run with error or warning
runAnyway=Átalakítási folyamat futtatása
#XBTN
close=Bezárás
#XMSG: Error object has validation error
runWithValidationErrors=Az átalakítási folyamat validálási hibákat tartalmaz. Ez sikertelen futást okozhat.
#XTIT
waitBusy=Kis türelmet.
#XMSG: initiating transformation flow run
runBusy=Adatok előkészítése...
#XMSG: Success
runSuccess=Az átalakítási folyamat futtatása elindult
#XMSG: Error
runFail=Nem sikerült futtatni az átalakítási folyamatot.
#XTIT: loading dialog title
loading=Betöltés
#XMSG: fetching run details from the server
loaderDetails=Adatok lehívása a szerverről
#XMSG: Error graphical view transform with error cannot be converted to SQL view transform
convertGVTransformToSQLViewTransformError=Hibás grafikusnézet-átalakítást nem lehet SQL-nézet-átalakításra konvertálni.
#~~~~~~~~~~~~~~~~~~~~~~~ Dialog Boxes texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Validation texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error Duplicate technical name validation message : params: {0} technical name of the object
validationDuplicateTechnicalName=A(z) {0} objektum már létezik a tárházban.
#XMSG: Error There should be a technical name for the target table.
validationEmptyTechnicalTargetTable=Adjon technikai nevet a céltáblának.
#XMSG: Error View transform operator not defined(template node is shown in the diagram)
validationEmptyViewTransform=Még nincs meghatározva nézetátalakítási operátor az átalakítási folyamathoz.
#XMSG: Error source operator not defined(template node is shown in the diagram) in large system spaces
validationEmptySource=Még nincs meghatározva forrásoperátor az átalakítási folyamathoz.
#XMSG: Error Target table not defined (template node is shown in the diagram)
validationEmptyTargetTable=Az átalakítási folyamat nem tartalmaz céltáblát.
#XMSG: Error validation message for incompatible data type mapping in target : params: {0} source column name {1} source column data type {2} target column name {3} target column data type {4} label of the target node
validationTargetColumnMappingIncompatible=A(z) {0} ({1}) nem kompatibilis a következővel: {2} ({3}) a(z) {4} operátorban.
#XMSG: Error validation message for incompatible mapping for timestamp column :  params: {0} & {1} and compatible source and column names
validationSouceTargetColumnMappingIncompatibleTimestamp=A(z) {0} oszlop csak a(z) {1} oszlophoz rendelhető hozzá a céltáblában.
#XMSG: Error No mappping defined in the target operator : params: {0} label of the target node
validationTargetMappingMissing=Nincs megadva hozzárendelés a(z) {0} operátorhoz.
#XMSG: Error No mappping defined in the target operator : params: {0} name of the primary key column {1} label of the target node
validationTargetMappingMissingForPrimaryKeyColumn=Nincs megadva hozzárendelés a(z) {0} kulcsoszlophoz a(z) {1} operátorban.
#XMSG: warning: No mappping defined for change Type Column
validationTargetMappingMissingForChangeTypeColumn=A forrásmező ({0}) nincs hozzárendelve a célmezőhöz ({0}.
#XMSG: Info: For change Date Column, The timestamp of this field will be set to the time that the transformation flow runs
validationTargetMappingMissingForChangeDateColumn=A(z) {0} mező nem releváns a hozzárendeléshez. A mező időbélyegzője az átalakítási folyamat futásának időpontja lesz.
#XMSG: Error No columns defined in the target operator : params: {0} label of the target node
validationEmptyColumns=Nincsenek megadva oszlopok a(z) {0} operátorhoz.
#XMSG: Error load type "Initial and Delta" not supported when the truncate mode is enbled in the target table properties
validationTruncateInvalidMode=A Kezdeti és delta adatátvétel nem támogatott, ha a Csonkolás beállítás engedélyezett a céltáblánál.
#XMSG: Error load type "Initial and Delta" not supported when the deleteAllBeforeLoading mode is enbled in the target table properties
validationDeleteAllBeforeLoadingInvalidMode=A Kezdeti és delta adatátvétel nem támogatott, ha az Összes törlése adatátvétel előtt beállítás engedélyezett a céltáblánál.
#XMSG: Error Object has duplicated technical name. : params: {0} technical name of the object
validationDuplicateTargetTechnicalName=Már van {0} nevű objektum a tárházban. Adjon meg más nevet.
#XMSG: Error technical name field is empty
validationEmptyTechnicalName=Adjon technikai nevet a táblának.
#XMSG: Error business name field is empty
validationEmptyBusinessName=Adjon üzleti nevet a táblának.
#XMSG: Error message for data type mismatch in target : params: {0} source column name {1} source column data type {2} target column name {3} target column data type
datatypeMismatchTarget=A hozzárendelés létrehozásához egyeznie kell az adattípusnak. A(z) {0} ({1}) nem kompatibilis a következővel: {2} ({3}).
#XMSG: Error load type "Initial and Delta" not supported for non-delta source
validationNonDeltaSource=A Kezdeti és delta adatátvétel nem támogatott, ha a forrás nem deltaképes.
#XMSG: Error load type "Initial and Delta" not supported for non-delta target
validationNonDeltaTarget=A Kezdeti és delta adatátvétel nem támogatott, ha a céltábla nem deltaképes.
#XMSG: Error truncate mode is not supported for delta-enabled target table
validationTruncateDeltaTarget=Deltaképes céltáblánál nem támogatott a csonkolási mód.
#XMSG: Info Message for upsert of records when target node has truncate off and a key column
validationTruncateOffandKeyColumn=A nézetátalakítási operátorral beolvasott rekordok betöltve a céltáblába az UPSERT művelet használatával.
#XMSG: Error deleteAllBeforeLoading mode is not supported for delta-enabled target table
validationDeleteAllBeforeLoadingDeltaTarget=Deltaképes céltáblánál nem támogatott az Összes törlése adatátvétel előtt mód.
#XMSG: Info Message for upsert of records when target node has deleteAllBeforeLoading off and a key column
validationDeleteAllBeforeLoadingOffandKeyColumn=A nézetátalakítási operátorral beolvasott rekordok betöltve a céltáblába az UPSERT művelet használatával.
#XMSG: delta-enabled source cannot be used with non-delta target : params: {0} label of the target node
validationDeltaSourceNonDeltaTarget=Nem használható deltaképes tábla a(z) {0} nem deltaképes céllal.
#XMSG: Error graphicalview transform has a validation error.
validationGraphicalViewTransformHasError=Egy vagy több hiba található a grafikusnézet-átalakításnál. A hibák megtekintéséhez szerkessze a grafikusnézet-átalakítást, és kattintson a Validálási üzenetek lehetőségre.
#XMSG: Error sql view transform has a validation error.
validationSQLViewTransformHasError=Egy vagy több hiba található az SQL-nézet-átalakításnál. A hibák megtekintéséhez szerkessze az SQL-nézet-átalakítást, és kattintson a Validálási üzenetek lehetőségre.
#XMSG: Error graphical view transform has a change management error.
validationGraphicalViewTransformHasChangeManagement=Egy vagy több forrástábla módosult. A módosítások megtekintéséhez szerkessze a grafikusnézet-átalakítást, és kattintson a Validálási üzenetek lehetőségre.
#XMSG: Error sql view transform has a change management error.
validationSQLViewTransformHasChangeManagement=Egy vagy több forrástábla módosult. A módosítások megtekintéséhez szerkessze az SQL-nézet-átalakítást, és kattintson a Validálási üzenetek lehetőségre.
#XMSG: Info load type set to intial and delta as source and traget are delta tables
validationInitialDeltaLoadType=Mivel a deltarögzítési funkció a forrás- és céltábláknál is engedélyezett, az alapértelmezett adatátvétel-típus Kezdeti és delta.
#XMSG: Info reset target table watermark
validationDeltaTargetTableChanged=Módosult a céltábla. Csak a deltaadatok kerülnek át az új céltáblába. Ha minden adatot át szeretne vinni a céltáblába, visszaállíthatja a vízjelet az Adatintegráció-figyelőben.
#XMSG: Error delta table from BW bridge cannot be used in SQL Script view transform
validationBWDeltaSourceInSQLScriptTransform=Ha SQL-nézetátalakítással szeretné betölteni a deltamódosításokat egy BW-híd-térben található távoli táblából, akkor az SQL (normál lekérdezés) nyelvet kell használnia. Az SQLScript (táblafüggvény) nem támogatott.
#XMSG: No primary key column in new delta target
validationNoKeyColumn=A deltarögzítési beállítás aktiválásához a céltáblának tartalmaznia kell legalább egy kulcsoszlopot.
#XMSG: Duplicate column names in new delta target
validationColumnNameNotUnique=Az oszlopneveknek egyedinek kell lenniük. A(z) {0} név többször szerepel a(z) {1} táblában.
#XMSG: Missing CDC Columns in new delta target
validationMissingCDCColumns=A deltarögzítési beállítás engedélyezett a(z) {0} táblánál, de hiányoznak a deltarögzítési oszlopok.
#XMSG: Validate non LTF target in large system space
validationLTFDeltaTargetLargeSystemSpace=A(z) {0} céltáblának deltaképesnek kell lennie fájltárolással, nagy méretű rendszertérben.
#XMSG: Validation error if a transformation flow from hana runtime space is imported into large system space
validationNonLsaTFInLsaSpace=A HANA-val futásidőben létrehozott átalakítási folyamatok nem használhatók nagy méretű rendszertérben.
#XMSG: Always show a validation info message in the python node to inform the user to update the python script manually to generate the data to match the output columns.
validationUpdatePythonScript=Módosítsa úgy a kimenetioszlop-sémát, hogy megfeleljen a Python-szkriptbeli DataFrame által eredményül adott oszlopoknak.
#XMSG: Error message for the python operator for changed CDC columns
validationChangedCDCColumns=Hiányoznak a deltarögzítési oszlopok, vagy módosultak a Python-szkriptben.
#XMSG: Validation error if a transformation flow from spark runtime space is imported into non large system space
validationLsaTFInNonLsaSpace=SAP HANA-adatbázis (lemez és memória) típusú tárolóval rendelkező térben nem hozható létre Spark-futásidővel rendelkező átalakítási folyamat.
#~~~~~~~~~~~~~~~~~~~~~~~ Validation texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Data Preview texts starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error data preview is not available for selected operator
dataPreviewNotSupp=Ennél az operátornál nem érhető el adatelőnézeti funkció.
#XMSG: Error data preview is not available as the selected node is a template.
dataPreviewTemplate=Ennél a csomópontnál nem érhető el adatelőnézeti funkció.
#XMSG: Error data preview is not available as the selected node is a new target table. i.e. the target table is not created
dataPreviewNewTarget=Ennél a céltáblánál nem érhető el adatelőnézeti funkció.
#XMSG: Error data preview is not avalibale as there are no columns in the selected node
dataPreviewEmptyColumns=Nem érhető el adatelőnézeti funkció, mert a táblának nincsenek látható oszlopai.
#XMSG Error insuffcient privileges to view data
dataPreviewInSufficientPrivileges=Nincs jogosultsága ezen adatok megtekintéséhez
#XMSG Not suffcient privileges to view data
dataPreviewNoPrivileges=Nincs jogosultsága ezeknek az adatoknak a megtekintéséhez.
#XTIT Data preview problems tab
txtProblems=Hibák
#XMSG: Error data preview is not available for SQL Script
dataPreviewNotSupportedSQLScript=SQLScript (táblafüggyvény) esetén nem érhető el adatelőnézeti funkció.
#XMSG: Error message for data preview with Cross space object with Input parameters
dataPreviewNotSupportedRemoteTableWithParam=Nem lehet megtekinteni az adatelőnézetet:\nAz adatelőnézet nem támogatott a bemeneti paramétereket tartalmazó térfüggetlen objektumokkal rendelkező nézetátalakítások esetében.
#~~~~~~~~~~~~~~~~~~~~~~~ Data Preview texts ends here~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG:view transform is modified and changes are applied to the editor
viewTransformChangesUpdated=A nézetátalakítás minden módosítása alkalmazva.
#XMSG:confirm switch from graphical view to sql view in the view transform
convertToSQLViewConfirmation=A nézetátalakítást SQL-nézet-átalakítás típusúra módosítja. Ez a módosítás nem vonható vissza.
#XTIT Save Dialog param
modelNameTransformationFlow=Átalakítási folyamat
#XMSG: Info automap operation cannot be performed due non matching columns
autoMapInvalid=Az automatikus hozzárendeléshez az oszlopok nevének és adattípusának meg kell egyeznie.
#XMSG Info: mapping already exists
mappingExists=Már van hozzárendelés.
#XMSG: There are invalid columns in the target table
invalidColumns=A következő oszlopokat nem lehet hozzárendelni, ezért el lesznek távolítva.
#XFLD: Label for package select
package=Csomag

#XMSG warning for repository package assignment changed for Object name {1}
VAL_PACKAGE_CHANGED=Hozzárendelte ezt az objektumot a(z) {1} csomaghoz. Kattintson a Mentés gombra a módosítás megerősítéséhez és validálásához. Ne feledje, hogy a csomaghoz való hozzárendelés mentés után nem vonható vissza ebben a szerkesztőben.
#XMSG warning for repository package dependency issue for object name{0}
MISSING_DEPENDENCY=A(z) {0} objektum függőségei nem szüntethetők meg a(z) {1} csomag kontextusában.
#XFLD: Mapped to parameter name{0}
mappedTo=Hozzárendelve ehhez: {0}
#XFLD: Value parameter default Value{0}
defaultVal=Érték: {0}
#XFLD: Not Mapped
notMapped=Nincs hozzárendelve
#XMSG : Validation message for views with remote table name {1} for view name {0}
validateViewWithRemoteTables=A(z) {0} nézet (közvetlenül vagy közvetve) a következő távoli táblákra hivatkozik: {1}
#XMSG : Validation message for views with DAC for view name {0}
validationDAC=A(z) {0} nézet nem adható hozzá. Egy adathozzáférés-vezérlő van alkalmazva erre a nézetre (vagy egy függő nézetre).
#XMSG : Validation message for objects with remote tables, remote table name{0} in sql editor,
validateSqlRemoteTables=Az SQL-utasítás egy vagy több olyan objektumot tartalmaz, ami (közvetlenül vagy közvetve) a következő távoli táblákra hivatkozik: {0}
#XBTN : Set Value
setValue=Érték megadása
#XBTN : Map TO
mapTo=Hozzárendelés
#XMSG : This source name{0} contains input parameters.
parameterMappingInfo=A(z) {0} forrás bemeneti paramétereket tartalmaz. Mindegyik paraméternél megadhat egy értéket, vagy hozzárendelheti egy bemeneti paraméterhez a nézetben.
#XBTN : Cancel
cancel=Mégse
#XTIT : Set Value for Input Parameter name {0}
setValueForParam={0} bemeneti paraméter értékének megadása
#XMSG : Value
enterValue=Érték
#XMSG : Enter Valid Decimal value with valid precision and scale
VAL_ENTER_VALID_DECIMAL_GEN=Adjon meg érvényes decimális értéket {0} pontossággal és {1} skálával.
#XFLD : Runtime
runtime=Futásidő
#XFLD: Storage
storage=Tároló
#XMSG: Info message for skipping SQL validation when tenant upgrade is in progress
VAL_TENANT_UPGRADE_IN_PROGRESS=Az SQL-nézet-átalakítás validálása kimaradt, mert bérlőfrissítés volt folyamatban. Az átalakítási folyamat üzembe helyezése előtt validálja az SQL-nézet-átalakítást.
#XMSG: Error LTF cannot be used as a source :params: {0} technical name of the object
validationLTFSource=Az átalakítási folyamat (Spark) forrása nem lehet a(z) {0} helyi tábla (fájl).
#XMSG: Error shared tables cannot be used as a source :params: {0} technical name of the object
validationSharedSource=Az átalakítási folyamat (Spark) forrása nem lehet a(z) {0} megosztott tábla (fájl).
#XMSG: Error Only LTF are supported in large system space
validationNonLTFSource=Az átalakítási folyamat (Spark) forrása nem lehet a(z) {0}, mert ez nem helyi tábla (fájl).
#~~~~~~~~~~~~~~~~~~~~~~~ Python Operator texts starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XTOL: Tooltip for python operator in context pad
pythonOperator=Python-operátor

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary sources properties starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Show Sources
showSources=Források megjelenítése:
#XFLD: Qualified Name
qualifiedName=Minősített név
#XFLD: Space Name
spaceName=Tér neve
#XFLD: Context Name
contextName=Kontextus neve
#XFLD: Connection
connection=Kapcsolat
#XFLD: Remote Table
remoteTable=Távoli tábla
#XFLD: Active Records
activeRecords=Aktív rekordok
#XFLD: Local Table
localTable=Helyi tábla
#XFLD: Local Schema
localSchema=Helyi séma
#XMSG: no parameters message
noParameters=Nincs elérhető paraméter
#XMSG: Validation Error in secondary source error message
secondarySourceErrorDialog=A nézetátalakítási források nem jeleníthetők meg, mert legalább egy hiba van az SQL-nézet-átalakításban.
#XMSG: Unable to load sources error message
secondarySourceLoadingErrorDialog=A nézetátalakítási források nem tölthetők be.
#XMSG: No sources error message
noSecondarySourceErrorDialog=A nézetátalakításnak nincs forrása.
#XTIT Local Table (File)
LTFTable=Helyi tábla (fájl)
#XMSG: Error data preview is not available for undeployed SQLScript Table function
dataPreviewNotDeployWarning=A nézetátalakítási adatok nem láthatók itt, amíg nincs üzembe helyezve az összes módosítás. Helyezze üzembe az átalakítási folyamatot, és próbálkozzon újra.
#Input parameters
#XFLD : Input Parameters
parametersTitle=Bemeneti paraméterek ({0})
#XMSG: Error message for empty input parameters
noInputParameters=Jelenleg nincs elérhető paraméter. Paraméter hozzáadásához kattintson a Szerkesztés gombra.
#XTOL: Tooltip for edit input parameters
parametersTooltip=Bemeneti paraméterek szerkesztése
#XMSG: edit input parameters
editInputParameters=Bemeneti paraméterek szerkesztése
#XMSG: Create input parameters in the object to be used in inner filters
parametersDescription=Hozzon létre bemeneti paramétereket az objektumban a belső szűrőkben való használathoz
#XBUT: Add new parameter
addParameters=Új paraméter hozzáadása
#XBUT: Delete parameter
deleteParameter=Paraméter törlése
#XFLD: Data Type
parameterDatatype=Adattípus
#XFLD: Name
inputParameterName=Név
#XFLD: Placeholder for string input type
placeholderText=Adja meg az alapértelmezett értéket
#XFLD: Default Value
defaultValue=Alapértelmezett érték
#XFLD defined by
definedBy=Meghatározta
#XMSG Error message for empty input parameter name
VAL_PARAM_EMPTY_NM=A paraméter neve üres.
#XMSG Error message for parameter name not unique
VAL_PARAM_NAME_NOT_UNIQUE=A paraméter neve nem egyedi.
#XMSG: Error message when object is not deployed yet
cannotViewNotDeployed=A(z) {0} objektum még sosem volt üzembe helyezve. A megtekintéshez helyezze üzembe.
#XFLD: Placeholder for string input type
stringPlaceholderText=Adjon meg karakterláncot
#XFLD: Placeholder for integer input type
intPlaceholderText=Adjon meg számot
#XFLD: Placeholder for decimal input type
decPlaceholderText=Adjon meg decimális értéket
#NOTR: Placeholder for date format Filter
dateFormatFilter=YYYY-MM-DD
#NOTR: Placeholder for time format Filter
timeFormatFilter=HH:mm:ss
#NOTR: Placeholder for date and time format Filter
dateTimeFormatFilter=YYYY-MM-DD, HH:mm:ss
#XTXT: Text for true dropdown of boolean datatype for input parameter - the value should be in small case matching the actual data
param_true=igaz
#XTXT: Text for false dropdown of boolean datatype for input parameter - the value should be in small case matching the actual data
param_false=hamis
#XTXT: Text for None dropdown of boolean datatype for input parameter
none=Egyik sem
#XFLD: Input Parameter Name
OperatorName=Név
#XFLD: Input Parameter Value
Value=Érték
#XBTN: run button of run with parameter dialog
TXT_RUN=Futtatás
#XFLD
InputParameters=Bemeneti paraméterek
#XTXT: Text for dialog box for input parameter
EnterParameterValues=Adja meg a paraméterértékeket az átalakítási folyamat futtatásához
#XMSG: Error message for invalid number input
INVALID_NUMBER=Érvényes számot adjon meg.
#XMSG: Error message for invalid decimal input
INVALID_DECIMAL=Érvényes decimális számot adjon meg.
#XMSG: Error message for invalid date input
INVALID_DATE=Érvényes dátumot adjon meg (ÉÉÉÉ-HH-NN).
#XMSG: Error message for invalid time input
INVALID_TIME=Érvényes időt adjon meg (ÓÓ:PP:MM).
#XMSG: Error message for invalid date and time input
INVALID_DATETIME=Érvényes dátumot és időt adjon meg (ÉÉÉÉ-HH-NN, ÓÓ:PP:MM).
#XMSG: Error message for invalid Boolean input
INVALID_BOOLEAN=Érvényes logikai értéket adjon meg.
#XMSG: Error message for invalid generic input
INVALID_INPUT=Érvénytelen bevitel.
#XTIP: Tooltip for parameter value
more=Több
#XTIT
mappingDialogTitle=Bemeneti paraméterek hozzárendelése
