#~~~~~~~~~~~~~~~~~~~~~~~ Properties Panel texts starts here ~~~~~~~~~~~~~~~~~~~~
#XTIT
properties=プロパティ
#XTIT
general=一般
#XFLD
businessName=ビジネス名
#XFLD
technicalName=技術名
#XFLD
loadType=タイプをロード
#XFLD
deltaCaptureTableName=デルタキャプチャテーブル名
#XSEL
initialOnly=初期のみ
#XSEL
initialAndDelta=初期およびデルタ
#XCKL
truncate=切り詰め
#XCKL
deleteAllBeforeLoading=ロード前にすべて削除
#XTIT
columns=列
#XTIT
mappings=マッピング
#XFLD
columnDataType=データ型
#XFLD
search=検索
#XBUT
autoMap=自動マッピング
#XBUT
removeAllMappings=すべてのマッピングを削除
#XMSG
noTargetInputs=マッピング情報を表示するには、変換表示演算子を定義する必要があります。
#XMSG
noColumnsData=表示する列がありません。
#XTOL
@validateModel=チェックメッセージ
#XTOL
@hierarchy=階層
#XTOL
@columnCount=列数
#XTOL
info=情報
#XTOL
cdcColumn=CDC 列
#XTIT
statusPanel=実行ステータス
#XTOL
schedule=スケジュール
#XTOL
navToMonitoring=変換フローモニタで開く
#XBTN
createSchedule=スケジュールを作成
#XBTN
editSchedule=スケジュールを編集
#XBTN
deleteSchedule=スケジュールを削除
#XFLD
lastRun=前回実行
#XFLD
status=ステータス
#XMSG: Error run status cannot be fetched
errorDetails=実行のステータスを取得できません。
#XLNK
viewDetails=詳細表示
#XMSG: Error data is not loading from the server
backendError=現在、データがサーバからロードされていないようです。後でもう一度実行してください。
#XMSG: Error transformation flow run failed. open data integration monitoring for more details
viewDetailsFallback=変換フロー実行を開始できませんでした。詳細については、モニタリングアプリケーションにナビゲートしてください。
#XMSG
statusCompleted=完了
#XMSG
statusRunning=実行中
#XMSG
statusFailed=失敗
#XMSG
statusNotExecuted=未実行
#XBTN
editColumns=列の編集
#XBUT: button in the properties panel of the target table
createNewTargetTable=新規ターゲットテーブルの作成
#XMSG: message shown in the properties panel of target table when it is not defined.
defineTargetInformation=新規ターゲットテーブルを作成するには、下にある "新規ターゲットテーブルの作成" ボタンをクリックします。既存のテーブルをターゲットテーブルとして使用するためには、テーブルをリポジトリからキャンバスにドラッグ & ドロップできます。
#XTIT
defineTarget=ターゲットテーブル定義
#XTIT
createViewTransform=表示変換の作成
#XMSG: message shown in the properties panel of view transform when it is not defined.
createViewTransformInformation=変換フローを開始するには、以下の関連ボタンをクリックして表示変換を作成します。
#XMSG: message shown in the properties panel of view transform when it is not defined.
createViewTransformInformationDndSpark=変換フローを開始するには、リポジトリパネルからソーステーブルをドラッグ & ドロップするか以下の関連ボタンをクリックしてソースを定義します。
#XBUT: button in the properties panel of view transform
sqlViewTransform=SQL ビュー変換
#XBUT: button in the properties panel of view transform
graphicalViewTransform=グラフィックビュー変換
#XFLD: view transform operator type - the value will be either SQL View or Graphical View
viewTransformType=タイプ
#XMSG: value of type property
sqlView=SQL ビュー
#XMSG: value of type property
graphicalView=グラフィックビュー

#~~~~~~~~~~~~Script properties text~~~~~~~~~~~~~~~~~~~~~
#XBTN: select all columns button text
selectAllColumns=すべて選択
#XBUT: Add column menu button
addColumn=列の追加
#XBUT: Create new column menu button
createNewColumn=新しい列の作成
#XBUT: Edit column menu button
editColumn=列の編集
#XBUT: Delete button
deleteColumn=削除
#XTIT: title for select columns dialog
columnSelectionDialogTitle=列の選択
#XMSG: message shown in the select columns dialog
columnSelectionNoDataText=列が見つかりません
#XFLD
columnOrigin=ソース
#XFLD
columnLength=長さ
#XFLD
columnPrecision=精度
#XFLD
columnScale=スケール
#XBTN: save button text
save=保存
#XTOL: Tooltip text for more button
tooltipTxt=詳細
#XTOL: Tooltip text for new column
newColumn=新しい列
#XTIT: title for the script editor
scriptTitle=スクリプト
#XMSG: Message strip text in the script editor
messageStripText=情報および詳細な文書を取得するには、Help Portal にアクセスしてください。
#XMSG: Learn more link text
linkText=詳細
#XMSG: For a data type with length, the length cannot be greater than the maximum value.
VAL_ELEMENT_PROPERTY_LENGTH_GREATER_THAN_MAX=長さの値を最大値 ({0}) よりも大きくすることはできません。
#XMSG: For a data type with length, the length cannot be negative.
VAL_ELEMENT_PROPERTY_LENGTH_LESS_THAN_ZERO=長さの値を 0 未満にすることはできません。
#XMSG: For a data type with length, the length cannot be zero.
VAL_ELEMENT_PROPERTY_LENGTH_CANNOT_BE_ZERO=長さの値を {0} にすることはできません。
#XMSG: The attribute of an Entity has a data type that must have a length.
VAL_ELEMENT_DATATYPE_NO_LENGTH=長さの値を入力してください。
#XMSG: The attribute of an Entity has a data type that must have precision.
VAL_ELEMENT_DATATYPE_NO_PRECISION=精度の値を入力してください。
#XMSG: For a data type with precision, the precision cannot be greater than the maximum value.
VAL_ELEMENT_PROPERTY_PRECISION_GREATER_THAN_MAX=精度の値を {0} よりも大きくすることはできません。
#XMSG: For a data type with precision, the precision cannot be less than the minimum value.
VAL_ELEMENT_PROPERTY_PRECISION_LESS_THAN_MIN=精度の値を {0} よりも小さくすることはできません。
#XMSG: For a data type with precision and scale, the precision cannot be less than the scale.
VAL_ELEMENT_PROPERTY_PRECISION_LESS_THAN_SCALE=精度の値をスケール ({0}) よりも小さくすることはできません
#XMSG: The attribute of an Entity has a data type that must have scale.
VAL_ELEMENT_DATATYPE_NO_SCALE=スケールの値を入力してください。
#XMSG: For a numeric data type like Decimal(precision, scale), the scale cannot be negative.
VAL_ELEMENT_PROPERTY_SCALE_LESS_THAN_ZERO=スケールの値を 0 未満にすることはできません。
#XMSG: For a numeric data type like Decimal(precision, scale), the scale cannot be greater than the precision.
VAL_ELEMENT_PROPERTY_SCALE_GREATER_THAN_PREC=スケールの値を精度 ({0}) よりも大きくすることはできません。
#~~~~~~~~~~~~~~~~~~~~~~~ Properties Panel texts ends here ~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Diagram texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XTOL: diagram tool item
zoomToFit=合わせてズーム
#XTOL: tooltip for context pad to open data preview (new text)
dataViewer=データビューア
#XTOL: tooltip for context pad to open data preview (old text)
previewData=データのプレビュー
#XTOL: tooltip for context pad to open impact and lineage editor
openImpactLineage=インパクトおよびリネージ分析
#XTOL: tooltip for context pad to open object in new tab
openInNewTab=新規タブで開く
#XTOL: tooltip for context pad to create new target table
createTable=新規テーブルの作成
#XTOL: tooltip for context pad to edit view transform operator
edit=編集
#XTOL: tooltip for context pad to clear the source or target node in the diagram
remove=削除
#XTOL: tooltip for context pad to define view transform operator using SQL editor
useSQLViewTransform=SQL ビュー変換
#XTOL: tooltip for context pad to define view transform operator using Graphical View editor
useGraphicalViewTransform=グラフィックビュー変換
#XMSG: Error shared table cannot be used as target
unsupportedTargetSharedTable=ターゲットテーブルが変換フロー内にあるため、共有テーブルは使用できません。
#XMSG: Error SAC artefact cannot be used as target
unsupportedSACTarget=読み込み専用オブジェクトを変換フローのターゲットして使用することはできません。
#XMSG: Error Open SQL Schema/HDI Container table cnnot be used as target
unsupportedTargetOpenSchema=ターゲットが変換フロー内にあるため、データベーススキーマまたは HDI コンテナを使用できません。
#XMSG: Message to show when view transform validation is in progress
vtValidationInProgress=変換表示チェックが進行中です。
#XMSG: welcome screen message when the editor is new or empty
welcomeText=表示変換を定義する、またはターゲットテーブルを追加/作成するには、ノードをクリックすることで開始します。
#XMSG: new welcome screen message when drag and drop of table is enabled (spark)
welcomeTextDndSpark=ソーステーブルまたはターゲットテーブルを追加するには、ノードをクリックするかリポジトリパネルからドラッグ & ドロップすることで開始します。
#XBUT: confirmation button to switch graphical view source to sql view source in the warning dialog
confirmSQLViewTransform=確認
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=テナントアップグレードが進行中です。

#~~~~~~~~~~~~~~~~~~~~~~~ Diagram texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Change Management texts starts here ~~~~~~~~~~~~~~~~~~~
#XTIT: title for the change management dialog
changeManagementTitle=ターゲットの更新
#XTIT: title for the change management dialog
changeManagementTitleSourceOrTarget=ソース/ターゲットの更新
#XTIT
objectDisplayName=表示名
#XTIT: name of the user who modifed the object
changedBy=変更者
#XTIT: the time when the object is modified
changedOn=変更日付
#XTIT
objectType=タイプ
#XBUT
ok=OK
#XMSG: message in the change management dialog
reviewText=変更内容を確認してください。変更を反映するには、変換フローを保存後、再デプロイする必要があります。
#XMSG: message in the change management dialog
changesText=この変換フローで使用されている以下のターゲットテーブルは変更されています (詳細についてはチェックメッセージを参照):
#XMSG: message in the change management dialog
changesTextSourceOrTarget=この変換フローで使用されている以下のソース/ターゲットテーブルは変更されています (詳細についてはチェックメッセージを参照):
#XMSG: Info column removed from the target: params: {0} label of the node
validationChangeMgmtDelColMsgHdr={0} から削除された列。
#XMSG: Info column data type updated in the target: params: {0} label of the node
validationChangeMgmtUpdDTColMsgHdr={0} で以下の列のデータ型が変更されました。
#XMSG: Info column added to the target: params: {0} label of the node
validationChangeMgmtNewColMsgHdr={0} に追加された列。
#NOTR: description text of message params: {0} list (with line breaks) of columns
validationChangeMgmtColsMsgDesc={0}
#XMSG: data type change information for the column params: {0} column name {1} old data type {2} new data type
validationChangeMgmtUpdDTColMsgDesc={0} のデータ型が {1} から {2} に変更されました。
#XMSG: title text of message, change of key/isKey: params: {0} number of columns
validationChangeMgmtUpdKeyColMsgHdr=キーが設定または削除された列 ({0})。
#XMSG: key property of a column has been set
validationChangeMgmtKeyPropertySet=このフィールドはキーフィールドになりました。
#XMSG: key property of a column has been removed
validationChangeMgmtKeyPropertyRemoved=このフィールドはキーフィールドではなくなりました。
#XMSG: title text of message, change of business name: params: {0} number of columns
VAL_CHANGE_MGMT_UPD_BN_COL_MSG_HDR=ビジネス名が更新された列 ({0})。
#XMSG: generic message to fill description of message with a list of column names: params: {0} list (with line breaks) of columns
VAL_CHANGE_MGMT_COLUMNS_MSG_DESC={0}
#XMSG: displayed next to a column name to indicate whether the value is new (maintained for the first time). Example: The business name of a column got maintained and before only the technical name was in place
VAL_VALUE_NEW=(新規)
#XMSG: displayed next to a column name to indicate whether the value has been resetted/deleted. Example: The business name of a column or source has been removed
VAL_VALUE_DELETED=(削除済み)
#XFLD: Label for source type node change management
source=ソース
#XFLD: Label for target type node change management
target=ターゲット
#XFLD: Tooltip label for view icon
view=ビュー
#~~~~~~~~~~~~~~~~~~~~~~~ Change Management texts ends here ~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary Editor texts starts here ~~~~~~~~~~~~~~~~~~~~

#XTIT: title of the secondry diagram editor (graphical view editor)
graphicalViewEditor=グラフィックビューエディタ
#XTIT: title of the secondry diagram editor (sql view editor)
sqlViewEditor=SQL ビューエディタ
#XTIT: title of the panel in Entity/Source properties panel
deltaSettings=デルタ設定
#XFLD: decides which object of delta table to be used(Delta Capture or All Active Records)
LoadFromLocalTable=ローカルテーブルからロード
#XFLD: decides which object of delta table to be used(Delta Capture or All Active Records)
LoadFromTable=テーブルからロード
#XRBL: only delta records will be processed when this option is selected
deltaCapture=デルタキャプチャ
#XRBL: all the records will be processed when this option is selected
allActiveRecords=有効なすべてのレコード
#XMSG: Error multiple delta source exist with Delta Capture selected
validationMultipleDeltaSource="ローカルテーブルからロード" オプションでデルタキャプチャ値を設定できるのは 1 つのソーステーブルに限られます。
#XMSG: Error Open SQL Schema/HDI Container table cannot be used as a source: params: {0} technical name of the object
validationOpenSchemaSource=ソースが変換フロー内にあるため、データベーススキーマまたは HDI コンテナテーブル "{0}" を使用できません。
#XMSG: Error remote table cannot be used as a source :params: {0} technical name of the object
validationRemoteTableSource=ソースが変換フロー内にあるため、リモートテーブル "{0}" を使用できません。
#XMSG: Error view cannot be used as a source :params: {0} technical name of the object
validationViewSource=ソースが変換フロー内にあるため、ビュー "{0}" を使用できません。
#XMSG: Error Aggregation node cannot be used with delta source(source with Delta Capture selected)
validationAggregationWithDelta=ソーステーブルのいずれかで "ローカルテーブルからロード" オプションのデルタキャプチャ値が設定されている場合、集計演算子は使用できません。
#XMSG: Error Left Outer Join with Delta Table as Outer Operand
validationErrorLeftOuterJoinWithDeltaTable=デルタキャプチャ設定が LEFT-OUTER-JOIN の外部オペランドとして有効化されたソーステーブルを使用することはできません。
#XMSG: Info: Outer Join with Delta Table as Outer Operand
validationErrorOuterJoinWithDeltaTable=左外部結合または右外部結合の外部オペラントとしてデルタ対応ソーステーブルを使用すると、実行時にエラーが発生する可能性があります。
#XMSG: Error Union with Delta Source
validationErrorUnionWithDeltaSource=ソーステーブルのいずれかで "テーブルからロード" オプションのデルタキャプチャ値が設定されている場合、ユニオン演算子は使用できません。
#XMSG: Error Timestap column should not be used in any calculated column
validationForCalculatedColumnWithDelta=計算列 {1} で列 {0} は使用できません。
#XMSG: Warning
invalidSQLViewDefinitionConfirmation=SQL 文をチェックできませんでした。
#XMSG: Warning SQL statment contains validation error
sqlErrorConfirmation=SQL 文にチェックエラーがあります。
#XMSG: Error multiple delta source exist in the sql statement
validationSQLEditorMultipleDeltaSource=SQL 文に複数のデルタソースが存在します。ソースとして使用できるデルタテーブルは 1 つのみです。
#XBTN: delete button on parameters panel
deleteParameters=削除
#XMSG: loading message shown in busy dialog when parameter is appended in sql statement
loadingParameters=パラメータをロードしています。
#XMSG: Open SQl schema source using one remote source directly or indirectly
validationOpenSchemaRemoteSource=オブジェクト "{0}" ではリモートテーブル "{1}" が (直接的または間接的に) 参照されます。
#XMSG: Open SQl schema source using multiple remote sources directly or indirectly
validationOpenSchemaRemoteSources=オブジェクト "{0}" では次のリモートテーブルが (直接的または間接的に) 参照されます: {1}
#XMSG: Open SQL schema source DAC validation
validationOpenSchemaDAC=オブジェクト "{0}" を追加できません。このオブジェクト (または従属オブジェクト) にはデータアクセス制御が適用されています。

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary Editor texts ends here ~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Dialog Boxes texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error new editor is opened and the object is not saved and Run button is clicked
runUnsavedError=変換フローを実行するには、先ず保存する必要があります。
#XTIT
runError=エラー
#XMSG: Error unsaved changes in the editor and Run button is clicked
runModifiedError=変換フローに保存されていない変更があります。変換フローを保存してください。
#XTIT
runWarning=警告
#XMSG: Warning the object has changes to deploy and Run button is clicked
runDeployedVersion=最新バージョンの変換フローがデプロイされていません。最後にデプロイされたバージョンの変換フローが実行されます。続行しますか?
#XBTN: run with error or warning
runAnyway=変換フローの実行
#XBTN
close=閉じる
#XMSG: Error object has validation error
runWithValidationErrors=変換フローにチェックエラーがあります。変換フローを実行すると、エラーが発生する可能性があります。
#XTIT
waitBusy=お待ちください。
#XMSG: initiating transformation flow run
runBusy=データを準備しています...
#XMSG: Success
runSuccess=変換フロー実行が開始されました
#XMSG: Error
runFail=変換フローの実行が失敗しました。
#XTIT: loading dialog title
loading=ロード中
#XMSG: fetching run details from the server
loaderDetails=サーバから詳細をフェッチしています
#XMSG: Error graphical view transform with error cannot be converted to SQL view transform
convertGVTransformToSQLViewTransformError=エラーが存在するグラフィックビュー変換を SQL ビュー変換に変換することはできません。
#~~~~~~~~~~~~~~~~~~~~~~~ Dialog Boxes texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Validation texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error Duplicate technical name validation message : params: {0} technical name of the object
validationDuplicateTechnicalName=オブジェクト "{0}" はリポジトリにすでに存在します。
#XMSG: Error There should be a technical name for the target table.
validationEmptyTechnicalTargetTable=ターゲットテーブルの技術名を入力してください。
#XMSG: Error View transform operator not defined(template node is shown in the diagram)
validationEmptyViewTransform=変換フローで変換表示演算子が定義されていません。
#XMSG: Error source operator not defined(template node is shown in the diagram) in large system spaces
validationEmptySource=変換フローでソース演算子が定義されていません。
#XMSG: Error Target table not defined (template node is shown in the diagram)
validationEmptyTargetTable=変換フローにターゲットテーブルが含まれていません。
#XMSG: Error validation message for incompatible data type mapping in target : params: {0} source column name {1} source column data type {2} target column name {3} target column data type {4} label of the target node
validationTargetColumnMappingIncompatible={0} ({1}) は演算子 {4} の {2} ({3}) と互換性がありません。
#XMSG: Error validation message for incompatible mapping for timestamp column :  params: {0} & {1} and compatible source and column names
validationSouceTargetColumnMappingIncompatibleTimestamp=列 {0} はターゲットテーブルの列 {1} にのみマッピングできます。
#XMSG: Error No mappping defined in the target operator : params: {0} label of the target node
validationTargetMappingMissing=演算子 {0} でマッピングが定義されていません。
#XMSG: Error No mappping defined in the target operator : params: {0} name of the primary key column {1} label of the target node
validationTargetMappingMissingForPrimaryKeyColumn=演算子 "{1}" のキー列 "{0}" にマッピングが定義されていません。
#XMSG: warning: No mappping defined for change Type Column
validationTargetMappingMissingForChangeTypeColumn=ソース {0} フィールドがターゲット {0} フィールドにマッピングされていません。
#XMSG: Info: For change Date Column, The timestamp of this field will be set to the time that the transformation flow runs
validationTargetMappingMissingForChangeDateColumn={0} フィールドはマッピングに関連していません。このフィールドのタイムスタンプは、変換フローが実行される時間に設定されます。
#XMSG: Error No columns defined in the target operator : params: {0} label of the target node
validationEmptyColumns=演算子 "{0}" に対して列が定義されていません。
#XMSG: Error load type "Initial and Delta" not supported when the truncate mode is enbled in the target table properties
validationTruncateInvalidMode=ターゲットテーブルで切り詰めオプションが有効化されている場合、"初期およびデルタ" ロードタイプはサポートされません。
#XMSG: Error load type "Initial and Delta" not supported when the deleteAllBeforeLoading mode is enbled in the target table properties
validationDeleteAllBeforeLoadingInvalidMode=ターゲットテーブルで "ロード前にすべて削除" オプションが有効化されている場合、"初期およびデルタ" ロードタイプはサポートされません。
#XMSG: Error Object has duplicated technical name. : params: {0} technical name of the object
validationDuplicateTargetTechnicalName=オブジェクト "{0}" はすでにリポジトリに存在します。別の名前を入力してください。
#XMSG: Error technical name field is empty
validationEmptyTechnicalName=テーブルの技術名を入力してください。
#XMSG: Error business name field is empty
validationEmptyBusinessName=テーブルのビジネス名を入力してください。
#XMSG: Error message for data type mismatch in target : params: {0} source column name {1} source column data type {2} target column name {3} target column data type
datatypeMismatchTarget=マッピングを作成するには、データ型が一致する必要があります。{0} ({1}) は {2} ({3}) と互換性がありません。
#XMSG: Error load type "Initial and Delta" not supported for non-delta source
validationNonDeltaSource=ソースでデルタが有効化されていない場合、"初期およびデルタ" ロードタイプはサポートされません。
#XMSG: Error load type "Initial and Delta" not supported for non-delta target
validationNonDeltaTarget=ターゲットテーブルでデルタが有効化されていない場合、"初期およびデルタ" ロードタイプはサポートされません。
#XMSG: Error truncate mode is not supported for delta-enabled target table
validationTruncateDeltaTarget=デルタ対応ターゲットテーブルでは、切り詰めモードはサポートされていません。
#XMSG: Info Message for upsert of records when target node has truncate off and a key column
validationTruncateOffandKeyColumn=ビュー変換演算子を使用して読み込まれたレコードが UPSERT 操作を使用してターゲットテーブルにロードされます。
#XMSG: Error deleteAllBeforeLoading mode is not supported for delta-enabled target table
validationDeleteAllBeforeLoadingDeltaTarget=デルタ対応ターゲットテーブルでは、"ロード前にすべて削除" モードはサポートされていません。
#XMSG: Info Message for upsert of records when target node has deleteAllBeforeLoading off and a key column
validationDeleteAllBeforeLoadingOffandKeyColumn=ビュー変換演算子を使用して読み込まれたレコードが UPSERT 操作を使用してターゲットテーブルにロードされます。
#XMSG: delta-enabled source cannot be used with non-delta target : params: {0} label of the target node
validationDeltaSourceNonDeltaTarget=デルタ対応ソースを非デルタ対応ターゲット "{0}" と使用することはできません。
#XMSG: Error graphicalview transform has a validation error.
validationGraphicalViewTransformHasError=グラフィックビュー変換に 1 つ以上のエラーが存在します。それらのエラーを表示するには、グラフィックビュー変換を編集し、チェックメッセージをクリックしてください。
#XMSG: Error sql view transform has a validation error.
validationSQLViewTransformHasError=SQL ビュー変換に 1 つ以上のエラーが存在します。それらのエラーを表示するには、SQL ビュー変換を編集し、チェックメッセージをクリックしてください。
#XMSG: Error graphical view transform has a change management error.
validationGraphicalViewTransformHasChangeManagement=1 つ以上のソーステーブルが変更されました。それらの変更を表示するには、グラフィックビュー変換を編集し、チェックメッセージをクリックしてください。
#XMSG: Error sql view transform has a change management error.
validationSQLViewTransformHasChangeManagement=1 つ以上のソーステーブルが変更されました。それらの変更を表示するには、SQL ビュー変換を編集し、チェックメッセージをクリックしてください。
#XMSG: Info load type set to intial and delta as source and traget are delta tables
validationInitialDeltaLoadType=ソーステーブルとターゲットテーブルの両方でデルタキャプチャ機能が有効化されているため、デフォルトのロードタイプは "初期およびデルタ" になります。
#XMSG: Info reset target table watermark
validationDeltaTargetTableChanged=ターゲットテーブルが変更されました。新しいターゲットテーブルにはデルタデータのみが転送されます。すべてのデータをターゲットテーブルに転送する場合は、データ統合モニタでウォーターマークをリセットできます。
#XMSG: Error delta table from BW bridge cannot be used in SQL Script view transform
validationBWDeltaSourceInSQLScriptTransform=SQL ビュー変換を使用して BW ブリッジスペースにあるリモートテーブルからデルタ変更をロードするには、言語が SQL (標準クエリ) である必要があります。SQLScript (テーブル関数) はサポートされていません。
#XMSG: No primary key column in new delta target
validationNoKeyColumn=デルタキャプチャ設定を有効化するには、ターゲットテーブルにキー列が少なくとも 1 つ含まれている必要があります。
#XMSG: Duplicate column names in new delta target
validationColumnNameNotUnique=列名は一意にしてください。テーブル "{1}" に複数の {0} があります。
#XMSG: Missing CDC Columns in new delta target
validationMissingCDCColumns=デルタキャプチャ設定がテーブル {0} で有効化されましたが、デルタキャプチャ列がありません。
#XMSG: Validate non LTF target in large system space
validationLTFDeltaTargetLargeSystemSpace=ターゲットテーブル "{0}" は、大規模なシステムの領域内のファイルストレージで有効化されている必要があります。
#XMSG: Validation error if a transformation flow from hana runtime space is imported into large system space
validationNonLsaTFInLsaSpace=HANA ランタイムで作成された変換フローを大規模なシステムの領域で使用することはできません。
#XMSG: Always show a validation info message in the python node to inform the user to update the python script manually to generate the data to match the output columns.
validationUpdatePythonScript=データフレームによって返される列に一致するように Python スクリプトで出力列スキーマを更新します。
#XMSG: Error message for the python operator for changed CDC columns
validationChangedCDCColumns=Python スクリプトでデルタキャプチャ列が存在しないか変更されています。
#XMSG: Validation error if a transformation flow from spark runtime space is imported into non large system space
validationLsaTFInNonLsaSpace=SAP HANA データベース (ディスクおよびインメモリ) では、Spark ランタイムの変換フローをスペースに作成できません。
#~~~~~~~~~~~~~~~~~~~~~~~ Validation texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Data Preview texts starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error data preview is not available for selected operator
dataPreviewNotSupp=この演算子ではデータプレビュー機能を利用できません。
#XMSG: Error data preview is not available as the selected node is a template.
dataPreviewTemplate=このノードではデータプレビュー機能を利用できません。
#XMSG: Error data preview is not available as the selected node is a new target table. i.e. the target table is not created
dataPreviewNewTarget=このターゲットテーブルではデータプレビュー機能を利用できません。
#XMSG: Error data preview is not avalibale as there are no columns in the selected node
dataPreviewEmptyColumns=テーブルに表示列がないためデータプレビュー機能を利用できません。
#XMSG Error insuffcient privileges to view data
dataPreviewInSufficientPrivileges=このデータを表示するために十分な権限を持っていません
#XMSG Not suffcient privileges to view data
dataPreviewNoPrivileges=このデータを表示する権限がありません。
#XTIT Data preview problems tab
txtProblems=エラー
#XMSG: Error data preview is not available for SQL Script
dataPreviewNotSupportedSQLScript=SQLScript (テーブル関数) ではデータプレビュー機能を利用できません。
#XMSG: Error message for data preview with Cross space object with Input parameters
dataPreviewNotSupportedRemoteTableWithParam=データをプレビューできません。\n入力パラメータがあるクロススペースオブジェクトで変換表示のデータをプレビューすることはできません。
#~~~~~~~~~~~~~~~~~~~~~~~ Data Preview texts ends here~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG:view transform is modified and changes are applied to the editor
viewTransformChangesUpdated=ビュー変換のすべての変更が適用されました。
#XMSG:confirm switch from graphical view to sql view in the view transform
convertToSQLViewConfirmation=ビュー変換タイプを SQL ビュー変換に変更しようとしています。この変更を元に戻すことはできません。
#XTIT Save Dialog param
modelNameTransformationFlow=変換フロー
#XMSG: Info automap operation cannot be performed due non matching columns
autoMapInvalid=自動マッピングでは列の名前とデータ型が一致する必要があります。
#XMSG Info: mapping already exists
mappingExists=マッピングがすでに存在します。
#XMSG: There are invalid columns in the target table
invalidColumns=以下の列はマップできないため削除されます。
#XFLD: Label for package select
package=パッケージ

#XMSG warning for repository package assignment changed for Object name {1}
VAL_PACKAGE_CHANGED=このオブジェクトをパッケージ {1} に割り当てました。保存をクリックしてこの変更を確認および有効化してください。保存後はこのエディタでパッケージへの割り当てを元に戻すことができないので注意してください。
#XMSG warning for repository package dependency issue for object name{0}
MISSING_DEPENDENCY=パッケージ {1} のコンテキストでオブジェクト "{0}" の依存関係を解決できません。
#XFLD: Mapped to parameter name{0}
mappedTo=マッピング先: {0}
#XFLD: Value parameter default Value{0}
defaultVal=値: {0}
#XFLD: Not Mapped
notMapped=マッピング未了
#XMSG : Validation message for views with remote table name {1} for view name {0}
validateViewWithRemoteTables=ビュー "{0}" では次のリモートテーブルが (直接的または間接的に) 参照されます: "{1}"
#XMSG : Validation message for views with DAC for view name {0}
validationDAC=ビュー "{0}" を追加できません。このビュー (または従属ビュー) にはデータアクセス制御が適用されています。
#XMSG : Validation message for objects with remote tables, remote table name{0} in sql editor,
validateSqlRemoteTables=この SQL 文には次のリモートテーブルを (直接的または間接的に) 参照する 1 つまたは複数のオブジェクトが含まれています: "{0}"
#XBTN : Set Value
setValue=値を設定
#XBTN : Map TO
mapTo=マッピング先
#XMSG : This source name{0} contains input parameters.
parameterMappingInfo=このソース {0} には入力パラメータがあります。個々の入力パラメータについて、値を設定するか、ビュー内の入力パラメータにマッピングすることができます。
#XBTN : Cancel
cancel=キャンセル
#XTIT : Set Value for Input Parameter name {0}
setValueForParam=入力パラメータ {0} の値を設定
#XMSG : Value
enterValue=値
#XMSG : Enter Valid Decimal value with valid precision and scale
VAL_ENTER_VALID_DECIMAL_GEN=精度が {0} でスケールが {1} である有効な 10 進数を入力してください。
#XFLD : Runtime
runtime=ランタイム
#XFLD: Storage
storage=ストレージ
#XMSG: Info message for skipping SQL validation when tenant upgrade is in progress
VAL_TENANT_UPGRADE_IN_PROGRESS=テナントのアップグレードが進行中であったため、SQL ビュー変換チェックがスキップされました。変換フローをデプロイする前に、SQL ビュー変換をチェックしてください。
#XMSG: Error LTF cannot be used as a source :params: {0} technical name of the object
validationLTFSource=ソースが変換フロー (spark) 内にあるため、ローカルテーブル (ファイル) "{0}" を使用できません。
#XMSG: Error shared tables cannot be used as a source :params: {0} technical name of the object
validationSharedSource=ソースが変換フロー (spark) 内にあるため、共有テーブル (ファイル) "{0}" を使用できません。
#XMSG: Error Only LTF are supported in large system space
validationNonLTFSource=ソース "{0}" がローカルテーブル (spark) でないため、変換フロー (spark) でソースを使用できません。
#~~~~~~~~~~~~~~~~~~~~~~~ Python Operator texts starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XTOL: Tooltip for python operator in context pad
pythonOperator=Python 演算子

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary sources properties starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Show Sources
showSources=ソースを表示
#XFLD: Qualified Name
qualifiedName=修飾名
#XFLD: Space Name
spaceName=スペース名
#XFLD: Context Name
contextName=コンテキスト名
#XFLD: Connection
connection=接続
#XFLD: Remote Table
remoteTable=リモートテーブル
#XFLD: Active Records
activeRecords=有効なレコード
#XFLD: Local Table
localTable=ローカルテーブル
#XFLD: Local Schema
localSchema=ローカルスキーマ
#XMSG: no parameters message
noParameters=利用可能なパラメータがありません
#XMSG: Validation Error in secondary source error message
secondarySourceErrorDialog=SQL ビュー変換に少なくとも 1 つのエラーがあるため、ビュー変換のソースを表示できません。
#XMSG: Unable to load sources error message
secondarySourceLoadingErrorDialog=ビュー変換のソースをロードできません。
#XMSG: No sources error message
noSecondarySourceErrorDialog=ビュー変換にソースがありません。
#XTIT Local Table (File)
LTFTable=ローカルテーブル (ファイル)
#XMSG: Error data preview is not available for undeployed SQLScript Table function
dataPreviewNotDeployWarning=すべての変更がデプロイされるまでビュー変換のデータは表示されません。変換フローをデプロイしてもう一度実行してください。
#Input parameters
#XFLD : Input Parameters
parametersTitle=入力パラメータ ({0})
#XMSG: Error message for empty input parameters
noInputParameters=現在利用できるパラメータはありません。パラメータを追加するには、編集ボタンを押してください。
#XTOL: Tooltip for edit input parameters
parametersTooltip=入力パラメータを編集
#XMSG: edit input parameters
editInputParameters=入力パラメータを編集します
#XMSG: Create input parameters in the object to be used in inner filters
parametersDescription=内側フィルタで使用されるオブジェクト内に、入力パラメータを作成します
#XBUT: Add new parameter
addParameters=新規パラメータを追加
#XBUT: Delete parameter
deleteParameter=パラメータを削除
#XFLD: Data Type
parameterDatatype=データ型
#XFLD: Name
inputParameterName=名前
#XFLD: Placeholder for string input type
placeholderText=デフォルト値を入力
#XFLD: Default Value
defaultValue=デフォルト値
#XFLD defined by
definedBy=定義元
#XMSG Error message for empty input parameter name
VAL_PARAM_EMPTY_NM=パラメータの名前が空です。
#XMSG Error message for parameter name not unique
VAL_PARAM_NAME_NOT_UNIQUE=パラメータ名が一意でありません。
#XMSG: Error message when object is not deployed yet
cannotViewNotDeployed=オブジェクト "{0}" は一度もデプロイされたことがありません。このオブジェクトを表示する前に、デプロイしてください。
#XFLD: Placeholder for string input type
stringPlaceholderText=文字列を入力
#XFLD: Placeholder for integer input type
intPlaceholderText=数値を入力
#XFLD: Placeholder for decimal input type
decPlaceholderText=10 進数を入力
#NOTR: Placeholder for date format Filter
dateFormatFilter=YYYY-MM-DD
#NOTR: Placeholder for time format Filter
timeFormatFilter=HH:mm:ss
#NOTR: Placeholder for date and time format Filter
dateTimeFormatFilter=YYYY-MM-DD, HH:mm:ss
#XTXT: Text for true dropdown of boolean datatype for input parameter - the value should be in small case matching the actual data
param_true=True
#XTXT: Text for false dropdown of boolean datatype for input parameter - the value should be in small case matching the actual data
param_false=False
#XTXT: Text for None dropdown of boolean datatype for input parameter
none=None
#XFLD: Input Parameter Name
OperatorName=名前
#XFLD: Input Parameter Value
Value=値
#XBTN: run button of run with parameter dialog
TXT_RUN=実行
#XFLD
InputParameters=入力パラメータ
#XTXT: Text for dialog box for input parameter
EnterParameterValues=変換フローを実行するためのパラメータ値の入力
#XMSG: Error message for invalid number input
INVALID_NUMBER=有効な数値を入力してください。
#XMSG: Error message for invalid decimal input
INVALID_DECIMAL=有効な 10 進数を入力してください。
#XMSG: Error message for invalid date input
INVALID_DATE=有効な日付 (YYYY-MM-DD) を入力してください。
#XMSG: Error message for invalid time input
INVALID_TIME=有効な時刻 (HH:mm:ss) を入力してください。
#XMSG: Error message for invalid date and time input
INVALID_DATETIME=有効な日付と時刻 (YYYY-MM-DD, HH:mm:ss) を入力してください。
#XMSG: Error message for invalid Boolean input
INVALID_BOOLEAN=有効な論理値を入力してください。
#XMSG: Error message for invalid generic input
INVALID_INPUT=無効な入力です。
#XTIP: Tooltip for parameter value
more=詳細
#XTIT
mappingDialogTitle=入力パラメータにマッピング
