#~~~~~~~~~~~~~~~~~~~~~~~ Properties Panel texts starts here ~~~~~~~~~~~~~~~~~~~~
#XTIT
properties=Wła<PERSON><PERSON>wości
#XTIT
general=Ogólne
#XFLD
businessName=Nazwa biznesowa
#XFLD
technicalName=Nazwa techniczna
#XFLD
loadType=Typ wczytania
#XFLD
deltaCaptureTableName=Nazwa tabeli rejestrowania delty
#XSEL
initialOnly=Tylk<PERSON> p<PERSON>tko<PERSON>
#XSEL
initialAndDelta=Początkowe i delta
#XCKL
truncate=Przytnij
#XCKL
deleteAllBeforeLoading=Usuń wszystko przed wczytaniem
#XTIT
columns=Kolumny
#XTIT
mappings=Przypisania
#XFLD
columnDataType=Typ danych
#XFLD
search=Szukaj
#XBUT
autoMap=Mapuj automatycznie
#XBUT
removeAllMappings=<PERSON><PERSON><PERSON> wszystkie mapowania
#XMSG
noTargetInputs=Aby wyświetlić informacje o mapowaniu, musisz zdefiniować operator Wyświetlanie transformacji.
#XMSG
noColumnsData=Brak kolumn do wyświetlenia.
#XTOL
@validateModel=Komunikaty walidacji
#XTOL
@hierarchy=Hierarchia
#XTOL
@columnCount=Liczba kolumn
#XTOL
info=Informacje
#XTOL
cdcColumn=Kolumna CDC
#XTIT
statusPanel=Status uruchomienia
#XTOL
schedule=Harmonogram
#XTOL
navToMonitoring=Otwórz w monitorze przepływu transformacji
#XBTN
createSchedule=Utwórz harmonogram
#XBTN
editSchedule=Edytuj harmonogram
#XBTN
deleteSchedule=Usuń harmonogram
#XFLD
lastRun=Ostatni przebieg
#XFLD
status=Status
#XMSG: Error run status cannot be fetched
errorDetails=Nie można pobrać statusu przebiegu.
#XLNK
viewDetails=Wyświetl szczegóły
#XMSG: Error data is not loading from the server
backendError=Wygląda na to, że aktualnie dane nie wczytują się z serwera. Spróbuj ponownie później.
#XMSG: Error transformation flow run failed. open data integration monitoring for more details
viewDetailsFallback=Rozpoczęcie przebiegu przepływu transformacji nie powiodło się. Przejdź do aplikacji Monitorowanie, aby uzyskać szczegółowe informacje.
#XMSG
statusCompleted=Zakończone
#XMSG
statusRunning=Aktywne
#XMSG
statusFailed=Niepowodzenie
#XMSG
statusNotExecuted=Jeszcze nie uruchomiono
#XBTN
editColumns=Edytuj kolumny
#XBUT: button in the properties panel of the target table
createNewTargetTable=Utwórz nową tabelę docelową
#XMSG: message shown in the properties panel of target table when it is not defined.
defineTargetInformation=Aby utworzyć nową tabelę docelową, kliknij poniższy przycisk Utwórz nową tabelę docelową. Aby użyć istniejącej tabeli jako tabeli docelowej, możesz przeciągnąć i upuścić tabelę z repozytorium do obszaru zawartości.
#XTIT
defineTarget=Definiowanie tabeli docelowej
#XTIT
createViewTransform=Tworzenie wyświetlania transformacji
#XMSG: message shown in the properties panel of view transform when it is not defined.
createViewTransformInformation=Aby rozpocząć przepływ transformacji, utwórz wyświetlanie transformacji, klikając odpowiedni przycisk poniżej.
#XMSG: message shown in the properties panel of view transform when it is not defined.
createViewTransformInformationDndSpark=Aby rozpocząć przepływ transformacji, określ źródło. W tym celu przeciągnij i upuść tabelę źródłową z panelu repozytorium lub kliknij odpowiedni przycisk poniżej.
#XBUT: button in the properties panel of view transform
sqlViewTransform=Wyświetlanie transformacji SQL
#XBUT: button in the properties panel of view transform
graphicalViewTransform=Graficzne wyświetlanie transformacji
#XFLD: view transform operator type - the value will be either SQL View or Graphical View
viewTransformType=Typ
#XMSG: value of type property
sqlView=Widok SQL
#XMSG: value of type property
graphicalView=Widok graficzny

#~~~~~~~~~~~~Script properties text~~~~~~~~~~~~~~~~~~~~~
#XBTN: select all columns button text
selectAllColumns=Zaznacz wszystko
#XBUT: Add column menu button
addColumn=Dodaj kolumnę
#XBUT: Create new column menu button
createNewColumn=Utwórz nową kolumnę
#XBUT: Edit column menu button
editColumn=Edytuj kolumnę
#XBUT: Delete button
deleteColumn=Usuń
#XTIT: title for select columns dialog
columnSelectionDialogTitle=Wybierz kolumny
#XMSG: message shown in the select columns dialog
columnSelectionNoDataText=Nie znaleziono kolumn
#XFLD
columnOrigin=Pochodzenie
#XFLD
columnLength=Długość
#XFLD
columnPrecision=Dokładność
#XFLD
columnScale=Skala
#XBTN: save button text
save=Zapisz
#XTOL: Tooltip text for more button
tooltipTxt=Więcej
#XTOL: Tooltip text for new column
newColumn=Nowa kolumna
#XTIT: title for the script editor
scriptTitle=Skrypt
#XMSG: Message strip text in the script editor
messageStripText=Otwórz portal pomocy, aby uzyskać informacje i skompletować dokumentację.
#XMSG: Learn more link text
linkText=Dowiedz się więcej
#XMSG: For a data type with length, the length cannot be greater than the maximum value.
VAL_ELEMENT_PROPERTY_LENGTH_GREATER_THAN_MAX=Wartość dla długości nie może być większa niż wartość maksymalna ({0}).
#XMSG: For a data type with length, the length cannot be negative.
VAL_ELEMENT_PROPERTY_LENGTH_LESS_THAN_ZERO=Wartość dla długości nie może być mniejsza niż 0.
#XMSG: For a data type with length, the length cannot be zero.
VAL_ELEMENT_PROPERTY_LENGTH_CANNOT_BE_ZERO=Wartość dla długości nie może wynosić {0}.
#XMSG: The attribute of an Entity has a data type that must have a length.
VAL_ELEMENT_DATATYPE_NO_LENGTH=Wprowadź wartość dla długości.
#XMSG: The attribute of an Entity has a data type that must have precision.
VAL_ELEMENT_DATATYPE_NO_PRECISION=Wprowadź wartość dla dokładności.
#XMSG: For a data type with precision, the precision cannot be greater than the maximum value.
VAL_ELEMENT_PROPERTY_PRECISION_GREATER_THAN_MAX=Wartość dla dokładności nie może być większa niż {0}.
#XMSG: For a data type with precision, the precision cannot be less than the minimum value.
VAL_ELEMENT_PROPERTY_PRECISION_LESS_THAN_MIN=Wartość dla dokładności nie może być mniejsza niż {0}.
#XMSG: For a data type with precision and scale, the precision cannot be less than the scale.
VAL_ELEMENT_PROPERTY_PRECISION_LESS_THAN_SCALE=Wartość dla dokładności nie może być mniejsza niż skala ({0})
#XMSG: The attribute of an Entity has a data type that must have scale.
VAL_ELEMENT_DATATYPE_NO_SCALE=Wprowadź wartość dla skali.
#XMSG: For a numeric data type like Decimal(precision, scale), the scale cannot be negative.
VAL_ELEMENT_PROPERTY_SCALE_LESS_THAN_ZERO=Wartość dla skali nie może być mniejsza niż 0.
#XMSG: For a numeric data type like Decimal(precision, scale), the scale cannot be greater than the precision.
VAL_ELEMENT_PROPERTY_SCALE_GREATER_THAN_PREC=Wartość dla skali nie może być większa niż dokładność ({0}).
#~~~~~~~~~~~~~~~~~~~~~~~ Properties Panel texts ends here ~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Diagram texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XTOL: diagram tool item
zoomToFit=Powiększ w celu dopasowania
#XTOL: tooltip for context pad to open data preview (new text)
dataViewer=Przeglądarka danych
#XTOL: tooltip for context pad to open data preview (old text)
previewData=Wyświetl podgląd danych
#XTOL: tooltip for context pad to open impact and lineage editor
openImpactLineage=Analiza wpływu i pochodzenia
#XTOL: tooltip for context pad to open object in new tab
openInNewTab=Otwórz w nowej karcie
#XTOL: tooltip for context pad to create new target table
createTable=Utwórz nową tabelę
#XTOL: tooltip for context pad to edit view transform operator
edit=Edytuj
#XTOL: tooltip for context pad to clear the source or target node in the diagram
remove=Usuń
#XTOL: tooltip for context pad to define view transform operator using SQL editor
useSQLViewTransform=Wyświetlanie transformacji SQL
#XTOL: tooltip for context pad to define view transform operator using Graphical View editor
useGraphicalViewTransform=Graficzne wyświetlanie transformacji
#XMSG: Error shared table cannot be used as target
unsupportedTargetSharedTable=Nie możesz użyć tabeli współdzielonej jako tabeli docelowej w przepływie transformacji.
#XMSG: Error SAC artefact cannot be used as target
unsupportedSACTarget=Nie możesz użyć obiektu tylko do odczytu jako celu przepływu transformacji.
#XMSG: Error Open SQL Schema/HDI Container table cnnot be used as target
unsupportedTargetOpenSchema=Nie można użyć schematu bazy danych lub kontenera HDI jako celu w przepływie transformacji.
#XMSG: Message to show when view transform validation is in progress
vtValidationInProgress=Walidacja wyświetlania transformacji w toku
#XMSG: welcome screen message when the editor is new or empty
welcomeText=Na początek kliknij węzeł, aby zdefiniować wyświetlanie transformacji lub dodaj/utwórz tabelę docelową.
#XMSG: new welcome screen message when drag and drop of table is enabled (spark)
welcomeTextDndSpark=Na początek kliknij węzeł lub przeciągnij i upuść tabelę z panelu repozytorium, aby dodać tabelę źródłową lub docelową
#XBUT: confirmation button to switch graphical view source to sql view source in the warning dialog
confirmSQLViewTransform=Potwierdź
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Upgrade tenanta jest w toku.

#~~~~~~~~~~~~~~~~~~~~~~~ Diagram texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Change Management texts starts here ~~~~~~~~~~~~~~~~~~~
#XTIT: title for the change management dialog
changeManagementTitle=Aktualizacje celu
#XTIT: title for the change management dialog
changeManagementTitleSourceOrTarget=Aktualizacje źródła/celu
#XTIT
objectDisplayName=Nazwa wyświetlana
#XTIT: name of the user who modifed the object
changedBy=Autor zmiany
#XTIT: the time when the object is modified
changedOn=Data zmiany
#XTIT
objectType=Typ
#XBUT
ok=OK
#XMSG: message in the change management dialog
reviewText=Przejrzyj modyfikacje. Aby zmiany zostały zastosowane, musisz zapisać i ponownie wdrożyć przepływ transformacji.
#XMSG: message in the change management dialog
changesText=Poniższa tabela docelowa wykorzystana w tym przepływie transformacji została zmodyfikowana (szczegóły można znaleźć w komunikatach walidacji):
#XMSG: message in the change management dialog
changesTextSourceOrTarget=Poniższa tabela źródłowa/docelowa wykorzystana w tym przepływie transformacji została zmodyfikowana (szczegóły można znaleźć w komunikatach walidacji):
#XMSG: Info column removed from the target: params: {0} label of the node
validationChangeMgmtDelColMsgHdr=Usunięto kolumny z {0}.
#XMSG: Info column data type updated in the target: params: {0} label of the node
validationChangeMgmtUpdDTColMsgHdr=Typ danych poniższych kolumn w {0} został zmieniony.
#XMSG: Info column added to the target: params: {0} label of the node
validationChangeMgmtNewColMsgHdr=Dodano kolumny do {0}.
#NOTR: description text of message params: {0} list (with line breaks) of columns
validationChangeMgmtColsMsgDesc={0}
#XMSG: data type change information for the column params: {0} column name {1} old data type {2} new data type
validationChangeMgmtUpdDTColMsgDesc={0}, zmieniono z {1} na {2}.
#XMSG: title text of message, change of key/isKey: params: {0} number of columns
validationChangeMgmtUpdKeyColMsgHdr=Kolumny z ustawionymi lub usuniętymi kluczami ({0}).
#XMSG: key property of a column has been set
validationChangeMgmtKeyPropertySet=Pole jest obecnie polem kluczowym.
#XMSG: key property of a column has been removed
validationChangeMgmtKeyPropertyRemoved=Pole nie jest już polem kluczowym.
#XMSG: title text of message, change of business name: params: {0} number of columns
VAL_CHANGE_MGMT_UPD_BN_COL_MSG_HDR=Kolumny ze zaktualizowaną nazwą biznesową ({0}).
#XMSG: generic message to fill description of message with a list of column names: params: {0} list (with line breaks) of columns
VAL_CHANGE_MGMT_COLUMNS_MSG_DESC={0}
#XMSG: displayed next to a column name to indicate whether the value is new (maintained for the first time). Example: The business name of a column got maintained and before only the technical name was in place
VAL_VALUE_NEW=(nowe)
#XMSG: displayed next to a column name to indicate whether the value has been resetted/deleted. Example: The business name of a column or source has been removed
VAL_VALUE_DELETED=(usunięte)
#XFLD: Label for source type node change management
source=Źródło
#XFLD: Label for target type node change management
target=Cel
#XFLD: Tooltip label for view icon
view=Wyświetl
#~~~~~~~~~~~~~~~~~~~~~~~ Change Management texts ends here ~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary Editor texts starts here ~~~~~~~~~~~~~~~~~~~~

#XTIT: title of the secondry diagram editor (graphical view editor)
graphicalViewEditor=Edytor widoków graficznych
#XTIT: title of the secondry diagram editor (sql view editor)
sqlViewEditor=Edytor widoków SQL
#XTIT: title of the panel in Entity/Source properties panel
deltaSettings=Ustawienia delty
#XFLD: decides which object of delta table to be used(Delta Capture or All Active Records)
LoadFromLocalTable=Wczytanie z tabeli lokalnej
#XFLD: decides which object of delta table to be used(Delta Capture or All Active Records)
LoadFromTable=Wczytanie z tabeli
#XRBL: only delta records will be processed when this option is selected
deltaCapture=Rejestrowanie delty
#XRBL: all the records will be processed when this option is selected
allActiveRecords=Wszystkie aktywne rekordy
#XMSG: Error multiple delta source exist with Delta Capture selected
validationMultipleDeltaSource=Tylko tabela źródłowa może mieć wartość Rejestrowanie delty dla opcji Wczytanie z tabeli lokalnej.
#XMSG: Error Open SQL Schema/HDI Container table cannot be used as a source: params: {0} technical name of the object
validationOpenSchemaSource=Nie można użyć schematu bazy danych lub tabeli kontenera HDI "{0}" jako źródła w przepływie transformacji.
#XMSG: Error remote table cannot be used as a source :params: {0} technical name of the object
validationRemoteTableSource=Nie można użyć tabeli zdalnej "{0}" jako źródła w przepływie transformacji.
#XMSG: Error view cannot be used as a source :params: {0} technical name of the object
validationViewSource=Nie można użyć widoku "{0}" jako źródła w przepływie transformacji.
#XMSG: Error Aggregation node cannot be used with delta source(source with Delta Capture selected)
validationAggregationWithDelta=Nie można użyć operatora agregacji, jeśli jedna z tabel źródłowych ma wartość Rejestrowanie delty dla opcji Wczytanie z tabeli lokalnej.
#XMSG: Error Left Outer Join with Delta Table as Outer Operand
validationErrorLeftOuterJoinWithDeltaTable=Nie można użyć tabeli źródłowej, dla której włączono rejestrowanie delty jako zewnętrzny operand LEFT-OUTER-JOIN.
#XMSG: Info: Outer Join with Delta Table as Outer Operand
validationErrorOuterJoinWithDeltaTable=Użycie źródłowej tabeli z obsługą delty jako zewnętrznego operandu dla left join lub right join może skutkować błędem podczas przebiegu.
#XMSG: Error Union with Delta Source
validationErrorUnionWithDeltaSource=Nie można użyć operatora UNION, jeśli jedna z tabel źródłowych ma wartość Rejestrowanie delty dla opcji Wczytanie z tabeli.
#XMSG: Error Timestap column should not be used in any calculated column
validationForCalculatedColumnWithDelta=Nie można użyć kolumny {0} w obliczonej kolumnie {1}.
#XMSG: Warning
invalidSQLViewDefinitionConfirmation=Walidacja instrukcji SQL nie powiodła się.
#XMSG: Warning SQL statment contains validation error
sqlErrorConfirmation=Instrukcja SQL zawiera błędy walidacji.
#XMSG: Error multiple delta source exist in the sql statement
validationSQLEditorMultipleDeltaSource=W instrukcji SQL istnieje wiele źródeł delty. Tylko jedna tabela delty może być używana jako źródło.
#XBTN: delete button on parameters panel
deleteParameters=Usuń
#XMSG: loading message shown in busy dialog when parameter is appended in sql statement
loadingParameters=Wczytywanie parametrów.
#XMSG: Open SQl schema source using one remote source directly or indirectly
validationOpenSchemaRemoteSource=Obiekt "{0}" odnosi się do zdalnej tabeli "{1}" (bezpośrednio lub pośrednio).
#XMSG: Open SQl schema source using multiple remote sources directly or indirectly
validationOpenSchemaRemoteSources=Obiekt "{0}" odnosi się do następujących tabel zdalnych (bezpośrednio lub pośrednio): {1}
#XMSG: Open SQL schema source DAC validation
validationOpenSchemaDAC=Nie można dodać obiektu "{0}". Do tego obiektu (lub obiektu zależnego) zastosowano kontrolę dostępu do danych.

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary Editor texts ends here ~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Dialog Boxes texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error new editor is opened and the object is not saved and Run button is clicked
runUnsavedError=Przed uruchomieniem przepływu transformacji musisz go zapisać.
#XTIT
runError=Błąd
#XMSG: Error unsaved changes in the editor and Run button is clicked
runModifiedError=Istnieją niezapisane zmiany w przepływie transformacji . Zapisz przepływ transformacji.
#XTIT
runWarning=Ostrzeżenie
#XMSG: Warning the object has changes to deploy and Run button is clicked
runDeployedVersion=Najnowsza wersja przepływu transformacji nie została jeszcze wdrożona. Uruchomiona zostanie ostatnia wdrożona wersja przepływu transformacji. Czy chcesz kontynuować?
#XBTN: run with error or warning
runAnyway=Uruchom przepływ transformacji
#XBTN
close=Zamknij
#XMSG: Error object has validation error
runWithValidationErrors=Przepływ transformacji zawiera błędy walidacji. Uruchomienie przepływu transformacji może skutkować niepowodzeniem.
#XTIT
waitBusy=Czekaj.
#XMSG: initiating transformation flow run
runBusy=Przygotowywanie danych...
#XMSG: Success
runSuccess=Rozpoczęto przebieg przepływu transformacji
#XMSG: Error
runFail=Uruchomienie przepływu transformacji nie powiodło się
#XTIT: loading dialog title
loading=Wczytywanie
#XMSG: fetching run details from the server
loaderDetails=Pobieranie szczegółów z serwera
#XMSG: Error graphical view transform with error cannot be converted to SQL view transform
convertGVTransformToSQLViewTransformError=Nie można przekonwertować graficznego wyświetlania transformacji z błędami na wyświetlanie transformacji SQL.
#~~~~~~~~~~~~~~~~~~~~~~~ Dialog Boxes texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Validation texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error Duplicate technical name validation message : params: {0} technical name of the object
validationDuplicateTechnicalName=Obiekt ''{0}'' już istnieje w repozytorium.
#XMSG: Error There should be a technical name for the target table.
validationEmptyTechnicalTargetTable=Wprowadź nazwę techniczną dla tabeli docelowej.
#XMSG: Error View transform operator not defined(template node is shown in the diagram)
validationEmptyViewTransform=Operator Wyświetlanie transformacji nie został jeszcze zdefiniowany dla przepływu transformacji.
#XMSG: Error source operator not defined(template node is shown in the diagram) in large system spaces
validationEmptySource=Operator Źródło nie został jeszcze zdefiniowany dla przepływu transformacji.
#XMSG: Error Target table not defined (template node is shown in the diagram)
validationEmptyTargetTable=Przepływ transformacji nie zawiera tabeli docelowej.
#XMSG: Error validation message for incompatible data type mapping in target : params: {0} source column name {1} source column data type {2} target column name {3} target column data type {4} label of the target node
validationTargetColumnMappingIncompatible=Niezgodność {0}({1}) z {2} ({3}) w operatorze "{4}".
#XMSG: Error validation message for incompatible mapping for timestamp column :  params: {0} & {1} and compatible source and column names
validationSouceTargetColumnMappingIncompatibleTimestamp=Kolumnę {0} można przypisać tylko do kolumny {1} w tabeli docelowej.
#XMSG: Error No mappping defined in the target operator : params: {0} label of the target node
validationTargetMappingMissing=Nie zdefiniowano mapowania w operatorze {0}.
#XMSG: Error No mappping defined in the target operator : params: {0} name of the primary key column {1} label of the target node
validationTargetMappingMissingForPrimaryKeyColumn=Nie zdefiniowano mapowania dla kolumny kluczowej "{0}" w operatorze {1}.
#XMSG: warning: No mappping defined for change Type Column
validationTargetMappingMissingForChangeTypeColumn=Pole źródłowe {0} nie jest przypisane do pola docelowego {0}.
#XMSG: Info: For change Date Column, The timestamp of this field will be set to the time that the transformation flow runs
validationTargetMappingMissingForChangeDateColumn=Pole {0} jest nieistotne dla mapowania. Znacznik czasu pola zostanie ustawiony na czas, w którym odbywa się przepływ transformacji.
#XMSG: Error No columns defined in the target operator : params: {0} label of the target node
validationEmptyColumns=Nie zdefiniowano kolumn dla operatora "{0}".
#XMSG: Error load type "Initial and Delta" not supported when the truncate mode is enbled in the target table properties
validationTruncateInvalidMode=Typ wczytania Początkowe i delta nie jest obsługiwany, jeśli dla tabeli docelowej włączono opcję Przytnij.
#XMSG: Error load type "Initial and Delta" not supported when the deleteAllBeforeLoading mode is enbled in the target table properties
validationDeleteAllBeforeLoadingInvalidMode=Typ wczytania Początkowe i delta nie jest obsługiwany, jeśli dla tabeli docelowej włączono opcję Usuń wszystko przed wczytaniem.
#XMSG: Error Object has duplicated technical name. : params: {0} technical name of the object
validationDuplicateTargetTechnicalName=Obiekt ''{0}'' już istnieje w repozytorium. Wprowadź inną nazwę.
#XMSG: Error technical name field is empty
validationEmptyTechnicalName=Wprowadź nazwę techniczną dla tabeli.
#XMSG: Error business name field is empty
validationEmptyBusinessName=Wprowadź nazwę biznesową dla tabeli.
#XMSG: Error message for data type mismatch in target : params: {0} source column name {1} source column data type {2} target column name {3} target column data type
datatypeMismatchTarget=Aby można było utworzyć mapowanie, typ danych musi być zgodny. {0} ({1}) jest niezgodny z {2} ({3}).
#XMSG: Error load type "Initial and Delta" not supported for non-delta source
validationNonDeltaSource=Typ wczytania Początkowe i delta nie jest obsługiwany, jeśli źródło nie jest kompatybilne z deltą.
#XMSG: Error load type "Initial and Delta" not supported for non-delta target
validationNonDeltaTarget=Typ wczytania Początkowe i delta nie jest obsługiwany, jeśli tabela docelowa nie jest kompatybilna z deltą.
#XMSG: Error truncate mode is not supported for delta-enabled target table
validationTruncateDeltaTarget=Tryb obcinania nie jest obsługiwany dla tabeli docelowej kompatybilnej z deltą.
#XMSG: Info Message for upsert of records when target node has truncate off and a key column
validationTruncateOffandKeyColumn=Rekordy odczytane za pomocą operatora Wyświetlanie transformacji są wczytywane w tabeli docelowej za pomocą operacji UPSERT.
#XMSG: Error deleteAllBeforeLoading mode is not supported for delta-enabled target table
validationDeleteAllBeforeLoadingDeltaTarget=Tryb Usuń wszystko przed wczytaniem nie jest obsługiwany dla tabeli docelowej kompatybilnej z deltą.
#XMSG: Info Message for upsert of records when target node has deleteAllBeforeLoading off and a key column
validationDeleteAllBeforeLoadingOffandKeyColumn=Rekordy odczytane za pomocą operatora Wyświetlanie transformacji są wczytywane w tabeli docelowej za pomocą operacji UPSERT.
#XMSG: delta-enabled source cannot be used with non-delta target : params: {0} label of the target node
validationDeltaSourceNonDeltaTarget=Nie można użyć źródła kompatybilnego z deltą z celem niekompatybilnym z deltą "{0}".
#XMSG: Error graphicalview transform has a validation error.
validationGraphicalViewTransformHasError=Wystąpił co najmniej jeden błąd dla graficznego wyświetlania transformacji. Możesz wyświetlić te błędy, edytując graficzne wyświetlanie transformacji i klikając opcję Komunikaty walidacji.
#XMSG: Error sql view transform has a validation error.
validationSQLViewTransformHasError=Wystąpił co najmniej jeden błąd dla wyświetlania transformacji SQL. Możesz wyświetlić te błędy, edytując wyświetlanie transformacji SQL i klikając opcję Komunikaty walidacji.
#XMSG: Error graphical view transform has a change management error.
validationGraphicalViewTransformHasChangeManagement=Co najmniej jedna z tabel źródłowych została zmieniona. Możesz wyświetlić te zmiany, edytując graficzne wyświetlanie transformacji i klikając opcję Komunikaty walidacji.
#XMSG: Error sql view transform has a change management error.
validationSQLViewTransformHasChangeManagement=Co najmniej jedna z tabel źródłowych została zmieniona. Możesz wyświetlić te zmiany, edytując wyświetlanie transformacji SQL i klikając opcję Komunikaty walidacji.
#XMSG: Info load type set to intial and delta as source and traget are delta tables
validationInitialDeltaLoadType=Ponieważ funkcja rejestrowania delty jest włączona zarówno dla tabel źródłowych, jak i docelowych, domyślny typ wczytywania to Początkowe i delta.
#XMSG: Info reset target table watermark
validationDeltaTargetTableChanged=Tabela docelowa zmieniła się. Do nowej tabeli docelowej zostaną przeniesione tylko dane delty. Jeśli chcesz przenieść do tabeli docelowej wszystkie dane, możesz zresetować znak wodny w Monitorze integracji danych.
#XMSG: Error delta table from BW bridge cannot be used in SQL Script view transform
validationBWDeltaSourceInSQLScriptTransform=Aby można było wczytać zmiany delta z tabeli zdalnej umieszczonej w przestrzeni BW Bridge przy użyciu wyświetlania transformacji SQL, język musi być ustawiony na SQL (standardowe zapytanie). SQLScript (funkcja tabeli) nie jest obsługiwany.
#XMSG: No primary key column in new delta target
validationNoKeyColumn=Aby włączyć ustawienie rejestrowania delty, tabela docelowa musi zawierać co najmniej jedną kolumnę klucza.
#XMSG: Duplicate column names in new delta target
validationColumnNameNotUnique=Nazwy kolumn muszą być unikalne. {0} ma wiele wystąpień w tabeli "{1}".
#XMSG: Missing CDC Columns in new delta target
validationMissingCDCColumns=Ustawienie rejestrowania delty jest włączone dla tabeli {0}, ale brakuje kolumn rejestrowania delty.
#XMSG: Validate non LTF target in large system space
validationLTFDeltaTargetLargeSystemSpace=Tabela docelowa "{0}" musi obsługiwać deltę z pamięcią plików w przestrzeni dużego systemu.
#XMSG: Validation error if a transformation flow from hana runtime space is imported into large system space
validationNonLsaTFInLsaSpace=Nie można użyć przepływu transformacji utworzonego przy użyciu czasu wykonania HANA w przestrzeni dużego systemu.
#XMSG: Always show a validation info message in the python node to inform the user to update the python script manually to generate the data to match the output columns.
validationUpdatePythonScript=Zaktualizuj schemat kolumny danych wyjściowych, aby odpowiadał kolumnom zwróconym przez DataFrame w skrypcie Python.
#XMSG: Error message for the python operator for changed CDC columns
validationChangedCDCColumns=Brak kolumn rejestrowania delty lub zostały one zmodyfikowane w skrypcie Python.
#XMSG: Validation error if a transformation flow from spark runtime space is imported into non large system space
validationLsaTFInNonLsaSpace=Przepływu transformacji przy użyciu czasu wykonania Spark nie można utworzyć w przestrzeni z pamięcią bazy danych SAP HANA (dyskową oraz in-memory).
#~~~~~~~~~~~~~~~~~~~~~~~ Validation texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Data Preview texts starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error data preview is not available for selected operator
dataPreviewNotSupp=Dla tego operatora funkcja podglądu danych nie jest dostępna.
#XMSG: Error data preview is not available as the selected node is a template.
dataPreviewTemplate=Dla tego węzła funkcja podglądu danych nie jest dostępna.
#XMSG: Error data preview is not available as the selected node is a new target table. i.e. the target table is not created
dataPreviewNewTarget=Dla tej tabeli docelowej funkcja podglądu danych nie jest dostępna.
#XMSG: Error data preview is not avalibale as there are no columns in the selected node
dataPreviewEmptyColumns=Funkcja podglądu danych jest niedostępna, ponieważ tabela nie ma widocznych kolumn.
#XMSG Error insuffcient privileges to view data
dataPreviewInSufficientPrivileges=Nie masz wystarczających uprawnień do wyświetlenia tych danych.
#XMSG Not suffcient privileges to view data
dataPreviewNoPrivileges=Nie masz uprawnień do wyświetlenia tych danych.
#XTIT Data preview problems tab
txtProblems=Błędy
#XMSG: Error data preview is not available for SQL Script
dataPreviewNotSupportedSQLScript=Funkcja podglądu danych nie jest dostępna dla SQLScript (funkcja tabeli).
#XMSG: Error message for data preview with Cross space object with Input parameters
dataPreviewNotSupportedRemoteTableWithParam=Nie można wyświetlić podglądu danych:\nPodgląd danych dla wyświetlania transformacji z obiektami dla wielu przestrzeni zawierających parametry wejściowe nie jest obsługiwany.
#~~~~~~~~~~~~~~~~~~~~~~~ Data Preview texts ends here~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG:view transform is modified and changes are applied to the editor
viewTransformChangesUpdated=Zastosowano wszystkie zmiany w wyświetlaniu transformacji.
#XMSG:confirm switch from graphical view to sql view in the view transform
convertToSQLViewConfirmation=Zmieniasz typ wyświetlania transformacji na wyświetlanie transformacji SQL. Ta zmiana jest nieodwracalna.
#XTIT Save Dialog param
modelNameTransformationFlow=Przepływ transformacji
#XMSG: Info automap operation cannot be performed due non matching columns
autoMapInvalid=Nazwa i typ danych kolumn muszą być zgodne, aby możliwe było automatyczne mapowanie.
#XMSG Info: mapping already exists
mappingExists=Mapowanie już istnieje.
#XMSG: There are invalid columns in the target table
invalidColumns=Nie można przypisać poniższych kolumn, więc zostaną one usunięte.
#XFLD: Label for package select
package=Pakiet

#XMSG warning for repository package assignment changed for Object name {1}
VAL_PACKAGE_CHANGED=Ten obiekt został przypisany do pakietu {1}. Kliknij opcję Zapisz, aby potwierdzić tę zmianę i przeprowadzić jej walidację. Zauważ, że po zapisaniu nie będzie można cofnąć przypisania do pakietu w tym edytorze.
#XMSG warning for repository package dependency issue for object name{0}
MISSING_DEPENDENCY=Zależności obiektu ''{0}'' nie mogą zostać rozwiązane kontekście pakietu {1}.
#XFLD: Mapped to parameter name{0}
mappedTo=Przypisane do: {0}
#XFLD: Value parameter default Value{0}
defaultVal=Wartość: {0}
#XFLD: Not Mapped
notMapped=Nieprzypisane
#XMSG : Validation message for views with remote table name {1} for view name {0}
validateViewWithRemoteTables=Widok "{0}" odnosi się do następujących tabel zdalnych (bezpośrednio lub pośrednio): "{1}"
#XMSG : Validation message for views with DAC for view name {0}
validationDAC=Nie można dodać widoku "{0}". Do tego widoku (lub widoku zależnego) zastosowano kontrolę dostępu do danych.
#XMSG : Validation message for objects with remote tables, remote table name{0} in sql editor,
validateSqlRemoteTables=Instrukcja SQL zawiera co najmniej jeden obiekt odnoszący się do następujących tabel zdalnych (bezpośrednio lub pośrednio): "{0} "
#XBTN : Set Value
setValue=Ustaw wartość
#XBTN : Map TO
mapTo=Przypisz do
#XMSG : This source name{0} contains input parameters.
parameterMappingInfo=To źródło {0} zawiera parametry wejściowe. Dla każdego parametru można albo ustawić wartość, albo przypisać go do parametru wejściowego w swoim widoku.
#XBTN : Cancel
cancel=Anuluj
#XTIT : Set Value for Input Parameter name {0}
setValueForParam=Ustaw wartość dla parametru wejściowego {0}
#XMSG : Value
enterValue=Wartość
#XMSG : Enter Valid Decimal value with valid precision and scale
VAL_ENTER_VALID_DECIMAL_GEN=Wprowadź prawidłową wartość dziesiętną z dokładnością {0} i skalą {1}.
#XFLD : Runtime
runtime=Czas wykonania
#XFLD: Storage
storage=Pamięć
#XMSG: Info message for skipping SQL validation when tenant upgrade is in progress
VAL_TENANT_UPGRADE_IN_PROGRESS=Pominięto walidację transformacji widoku SQL, ponieważ uaktualnianie dzierżawcy było w toku. Przeprowadź walidację transformacji widoku SQL przed wdrożeniem przepływu transformacji.
#XMSG: Error LTF cannot be used as a source :params: {0} technical name of the object
validationLTFSource=Nie można użyć lokalnej tabeli (plik) "{0}" jako źródła w przepływie transformacji (Spark).
#XMSG: Error shared tables cannot be used as a source :params: {0} technical name of the object
validationSharedSource=Nie można użyć tabeli współdzielonej (plik) "{0}" jako źródła w przepływie transformacji (Spark).
#XMSG: Error Only LTF are supported in large system space
validationNonLTFSource=Nie można użyć źródła "{0}" w przepływie transformacji (Spark), ponieważ nie jest tabelą lokalną (plik).
#~~~~~~~~~~~~~~~~~~~~~~~ Python Operator texts starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XTOL: Tooltip for python operator in context pad
pythonOperator=Operator Python

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary sources properties starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Show Sources
showSources=Pokaż źródła:
#XFLD: Qualified Name
qualifiedName=Nazwa kwalifikowana
#XFLD: Space Name
spaceName=Nazwa przestrzeni
#XFLD: Context Name
contextName=Nazwa kontekstu
#XFLD: Connection
connection=Połączenie
#XFLD: Remote Table
remoteTable=Tabela zdalna
#XFLD: Active Records
activeRecords=Aktywne rekordy
#XFLD: Local Table
localTable=Tabela lokalna
#XFLD: Local Schema
localSchema=Schemat lokalny
#XMSG: no parameters message
noParameters=Brak dostępnych parametrów
#XMSG: Validation Error in secondary source error message
secondarySourceErrorDialog=Nie można wyświetlić źródeł transformacji widoku, ponieważ w transformacji widoku SQL występuje co najmniej jeden błąd.
#XMSG: Unable to load sources error message
secondarySourceLoadingErrorDialog=Nie można wczytać źródeł transformacji widoku.
#XMSG: No sources error message
noSecondarySourceErrorDialog=Transformacja widoku nie ma żadnego źródła.
#XTIT Local Table (File)
LTFTable=Tabela lokalna (plik)
#XMSG: Error data preview is not available for undeployed SQLScript Table function
dataPreviewNotDeployWarning=Dane dla Twojego wyświetlania transformacji są w tym miejscu niewidoczne do czasu wdrożenia wszystkich zmian. Wykonaj wdrożenie przepływu transformacji i spróbuj ponownie.
#Input parameters
#XFLD : Input Parameters
parametersTitle=Parametry wejściowe ({0})
#XMSG: Error message for empty input parameters
noInputParameters=Obecnie nie ma żadnych dostępnych parametrów. Aby je dodać, naciśnij przycisk Edytuj.
#XTOL: Tooltip for edit input parameters
parametersTooltip=Edytuj parametry wejściowe
#XMSG: edit input parameters
editInputParameters=Edytuj parametry wejściowe
#XMSG: Create input parameters in the object to be used in inner filters
parametersDescription=Utwórz parametry wejściowe w obiekcie, który będzie wykorzystywany w filtrach wewnętrznych.
#XBUT: Add new parameter
addParameters=Dodaj nowy parametr
#XBUT: Delete parameter
deleteParameter=Usuń parametr
#XFLD: Data Type
parameterDatatype=Typ danych
#XFLD: Name
inputParameterName=Nazwa
#XFLD: Placeholder for string input type
placeholderText=Wprowadź wartość domyślną
#XFLD: Default Value
defaultValue=Wartość domyślna
#XFLD defined by
definedBy=Zdefiniowane przez
#XMSG Error message for empty input parameter name
VAL_PARAM_EMPTY_NM=Nazwa parametru jest pusta.
#XMSG Error message for parameter name not unique
VAL_PARAM_NAME_NOT_UNIQUE=Nazwa parametru nie jest unikalna.
#XMSG: Error message when object is not deployed yet
cannotViewNotDeployed=Obiekt "{0}" nie został nigdy wdrożony. Wdróż go przed wyświetleniem.
#XFLD: Placeholder for string input type
stringPlaceholderText=Wprowadź ciąg znaków
#XFLD: Placeholder for integer input type
intPlaceholderText=Wprowadź liczbę
#XFLD: Placeholder for decimal input type
decPlaceholderText=Wprowadź wartość dziesiętną
#NOTR: Placeholder for date format Filter
dateFormatFilter=YYYY-MM-DD
#NOTR: Placeholder for time format Filter
timeFormatFilter=HH:mm:ss
#NOTR: Placeholder for date and time format Filter
dateTimeFormatFilter=YYYY-MM-DD, HH:mm:ss
#XTXT: Text for true dropdown of boolean datatype for input parameter - the value should be in small case matching the actual data
param_true=prawda
#XTXT: Text for false dropdown of boolean datatype for input parameter - the value should be in small case matching the actual data
param_false=fałsz
#XTXT: Text for None dropdown of boolean datatype for input parameter
none=Brak
#XFLD: Input Parameter Name
OperatorName=Nazwa
#XFLD: Input Parameter Value
Value=Wartość
#XBTN: run button of run with parameter dialog
TXT_RUN=Uruchom
#XFLD
InputParameters=Parametry wejściowe
#XTXT: Text for dialog box for input parameter
EnterParameterValues=Wprowadź wartości parametrów, aby uruchomić przepływ transformacji
#XMSG: Error message for invalid number input
INVALID_NUMBER=Wprowadź prawidłową liczbę.
#XMSG: Error message for invalid decimal input
INVALID_DECIMAL=Wprowadź prawidłową wartość dziesiętną.
#XMSG: Error message for invalid date input
INVALID_DATE=Wprowadź prawidłową datę (RRRR-MM-DD).
#XMSG: Error message for invalid time input
INVALID_TIME=Wprowadź prawidłową godzinę (GG:mm:ss).
#XMSG: Error message for invalid date and time input
INVALID_DATETIME=Wprowadź prawidłową datę i godzinę (RRRR-MM-DD, GG:mm:ss).
#XMSG: Error message for invalid Boolean input
INVALID_BOOLEAN=Wprowadź prawidłowe wyrażenie logiczne.
#XMSG: Error message for invalid generic input
INVALID_INPUT=Nieprawidłowe dane wejściowe.
#XTIP: Tooltip for parameter value
more=Więcej
#XTIT
mappingDialogTitle=Przypisanie parametrów wejściowych
