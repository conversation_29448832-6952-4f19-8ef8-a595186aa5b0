#~~~~~~~~~~~~~~~~~~~~~~~ Properties Panel texts starts here ~~~~~~~~~~~~~~~~~~~~
#XTIT
properties=属性
#XTIT
general=常规
#XFLD
businessName=业务名称
#XFLD
technicalName=技术名称
#XFLD
loadType=加载类型
#XFLD
deltaCaptureTableName=增量捕获表名称
#XSEL
initialOnly=仅限初始
#XSEL
initialAndDelta=初始和增量
#XCKL
truncate=截断
#XCKL
deleteAllBeforeLoading=加载前全部删除
#XTIT
columns=列
#XTIT
mappings=映射
#XFLD
columnDataType=数据类型
#XFLD
search=搜索
#XBUT
autoMap=自动映射
#XBUT
removeAllMappings=移除所有映射
#XMSG
noTargetInputs=需要定义 "视图转换" 运算符，才能查看映射信息。
#XMSG
noColumnsData=没有要显示的列。
#XTOL
@validateModel=验证消息
#XTOL
@hierarchy=层次结构
#XTOL
@columnCount=列数
#XTOL
info=信息
#XTOL
cdcColumn=CDC 列
#XTIT
statusPanel=运行状态
#XTOL
schedule=计划
#XTOL
navToMonitoring=在转换流监控器中打开
#XBTN
createSchedule=创建计划
#XBTN
editSchedule=编辑计划
#XBTN
deleteSchedule=删除计划
#XFLD
lastRun=上次运行
#XFLD
status=状态
#XMSG: Error run status cannot be fetched
errorDetails=没能获取运行状态。
#XLNK
viewDetails=查看详细信息
#XMSG: Error data is not loading from the server
backendError=目前似乎没有从服务器加载数据。请稍后重试。
#XMSG: Error transformation flow run failed. open data integration monitoring for more details
viewDetailsFallback=没能启动转换流运行。请导航到监控应用程序，查看更多详细信息。
#XMSG
statusCompleted=已完成
#XMSG
statusRunning=运行中
#XMSG
statusFailed=失败
#XMSG
statusNotExecuted=还没有运行
#XBTN
editColumns=编辑列
#XBUT: button in the properties panel of the target table
createNewTargetTable=创建新目标表
#XMSG: message shown in the properties panel of target table when it is not defined.
defineTargetInformation=要创建新目标表，请点击下面的 "创建新目标表" 按钮。要将现有的表用作目标表，可以将资源库中的表拖放到画布中。
#XTIT
defineTarget=定义目标表
#XTIT
createViewTransform=创建视图转换
#XMSG: message shown in the properties panel of view transform when it is not defined.
createViewTransformInformation=要启动转换流，可点击下面的相关按钮来创建视图转换。
#XMSG: message shown in the properties panel of view transform when it is not defined.
createViewTransformInformationDndSpark=要启动转换流，请通过从资源库面板中拖放源表或点击下方相关按钮来定义源。
#XBUT: button in the properties panel of view transform
sqlViewTransform=SQL 视图转换
#XBUT: button in the properties panel of view transform
graphicalViewTransform=图形化视图转换
#XFLD: view transform operator type - the value will be either SQL View or Graphical View
viewTransformType=类型
#XMSG: value of type property
sqlView=SQL 视图
#XMSG: value of type property
graphicalView=图形化视图

#~~~~~~~~~~~~Script properties text~~~~~~~~~~~~~~~~~~~~~
#XBTN: select all columns button text
selectAllColumns=全选
#XBUT: Add column menu button
addColumn=添加列
#XBUT: Create new column menu button
createNewColumn=新建列
#XBUT: Edit column menu button
editColumn=编辑列
#XBUT: Delete button
deleteColumn=删除
#XTIT: title for select columns dialog
columnSelectionDialogTitle=选择列
#XMSG: message shown in the select columns dialog
columnSelectionNoDataText=找不到列
#XFLD
columnOrigin=来源
#XFLD
columnLength=长度
#XFLD
columnPrecision=精度
#XFLD
columnScale=标度
#XBTN: save button text
save=保存
#XTOL: Tooltip text for more button
tooltipTxt=更多
#XTOL: Tooltip text for new column
newColumn=新列
#XTIT: title for the script editor
scriptTitle=脚本
#XMSG: Message strip text in the script editor
messageStripText=访问帮助门户，获取信息和完整文档。
#XMSG: Learn more link text
linkText=了解更多
#XMSG: For a data type with length, the length cannot be greater than the maximum value.
VAL_ELEMENT_PROPERTY_LENGTH_GREATER_THAN_MAX=长度值不能大于最大值（{0}）。
#XMSG: For a data type with length, the length cannot be negative.
VAL_ELEMENT_PROPERTY_LENGTH_LESS_THAN_ZERO=长度值不能小于 0。
#XMSG: For a data type with length, the length cannot be zero.
VAL_ELEMENT_PROPERTY_LENGTH_CANNOT_BE_ZERO=长度值不能为 {0}。
#XMSG: The attribute of an Entity has a data type that must have a length.
VAL_ELEMENT_DATATYPE_NO_LENGTH=请输入长度值。
#XMSG: The attribute of an Entity has a data type that must have precision.
VAL_ELEMENT_DATATYPE_NO_PRECISION=请输入精度值。
#XMSG: For a data type with precision, the precision cannot be greater than the maximum value.
VAL_ELEMENT_PROPERTY_PRECISION_GREATER_THAN_MAX=精度值不能大于 {0}。
#XMSG: For a data type with precision, the precision cannot be less than the minimum value.
VAL_ELEMENT_PROPERTY_PRECISION_LESS_THAN_MIN=精度值不能小于 {0}。
#XMSG: For a data type with precision and scale, the precision cannot be less than the scale.
VAL_ELEMENT_PROPERTY_PRECISION_LESS_THAN_SCALE=精度值不能小于标度（{0}）
#XMSG: The attribute of an Entity has a data type that must have scale.
VAL_ELEMENT_DATATYPE_NO_SCALE=请输入标度值。
#XMSG: For a numeric data type like Decimal(precision, scale), the scale cannot be negative.
VAL_ELEMENT_PROPERTY_SCALE_LESS_THAN_ZERO=标度值不能小于 0。
#XMSG: For a numeric data type like Decimal(precision, scale), the scale cannot be greater than the precision.
VAL_ELEMENT_PROPERTY_SCALE_GREATER_THAN_PREC=标度值不能大于精度（{0}）。
#~~~~~~~~~~~~~~~~~~~~~~~ Properties Panel texts ends here ~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Diagram texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XTOL: diagram tool item
zoomToFit=缩放到合适大小
#XTOL: tooltip for context pad to open data preview (new text)
dataViewer=数据查看器
#XTOL: tooltip for context pad to open data preview (old text)
previewData=预览数据
#XTOL: tooltip for context pad to open impact and lineage editor
openImpactLineage=影响和沿袭分析
#XTOL: tooltip for context pad to open object in new tab
openInNewTab=在新选项卡中打开
#XTOL: tooltip for context pad to create new target table
createTable=创建新表
#XTOL: tooltip for context pad to edit view transform operator
edit=编辑
#XTOL: tooltip for context pad to clear the source or target node in the diagram
remove=移除
#XTOL: tooltip for context pad to define view transform operator using SQL editor
useSQLViewTransform=SQL 视图转换
#XTOL: tooltip for context pad to define view transform operator using Graphical View editor
useGraphicalViewTransform=图形化视图转换
#XMSG: Error shared table cannot be used as target
unsupportedTargetSharedTable=在转换流中，共享表不能用作目标表。
#XMSG: Error SAC artefact cannot be used as target
unsupportedSACTarget=只读对象不能用作转换流的目标。
#XMSG: Error Open SQL Schema/HDI Container table cnnot be used as target
unsupportedTargetOpenSchema=在转换流中，数据库模式或 HDI 容器不能用作目标。
#XMSG: Message to show when view transform validation is in progress
vtValidationInProgress=正在执行视图转换验证。
#XMSG: welcome screen message when the editor is new or empty
welcomeText=要开始流程，请点击其中一个节点，定义视图转换或添加/创建目标表。
#XMSG: new welcome screen message when drag and drop of table is enabled (spark)
welcomeTextDndSpark=要开始流程，请点击其中一个节点，或者从资源库面板中拖放以添加源或目标表。
#XBUT: confirmation button to switch graphical view source to sql view source in the warning dialog
confirmSQLViewTransform=确认
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=正在进行租户升级。

#~~~~~~~~~~~~~~~~~~~~~~~ Diagram texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Change Management texts starts here ~~~~~~~~~~~~~~~~~~~
#XTIT: title for the change management dialog
changeManagementTitle=目标更新
#XTIT: title for the change management dialog
changeManagementTitleSourceOrTarget=源/目标更新
#XTIT
objectDisplayName=显示名称
#XTIT: name of the user who modifed the object
changedBy=更改者
#XTIT: the time when the object is modified
changedOn=更改日期
#XTIT
objectType=类型
#XBUT
ok=确定
#XMSG: message in the change management dialog
reviewText=请检查修改内容。需要先保存修改，然后再重新部署转换流，更改才能生效。
#XMSG: message in the change management dialog
changesText=转换流中使用的下列目标表已经修改（请查看验证消息，了解详细信息）：
#XMSG: message in the change management dialog
changesTextSourceOrTarget=转换流中使用的下列源/目标表已经修改（请查看验证消息，了解详细信息）：
#XMSG: Info column removed from the target: params: {0} label of the node
validationChangeMgmtDelColMsgHdr=列已经从 "{0}" 移除。
#XMSG: Info column data type updated in the target: params: {0} label of the node
validationChangeMgmtUpdDTColMsgHdr="{0}" 中，以下列的数据类型已经更改。
#XMSG: Info column added to the target: params: {0} label of the node
validationChangeMgmtNewColMsgHdr=列已经添加到 "{0}"。
#NOTR: description text of message params: {0} list (with line breaks) of columns
validationChangeMgmtColsMsgDesc={0}
#XMSG: data type change information for the column params: {0} column name {1} old data type {2} new data type
validationChangeMgmtUpdDTColMsgDesc={0}，已从 {1} 更改为 {2}。
#XMSG: title text of message, change of key/isKey: params: {0} number of columns
validationChangeMgmtUpdKeyColMsgHdr=已经设置或移除含有键的列（{0}）。
#XMSG: key property of a column has been set
validationChangeMgmtKeyPropertySet=这个字段现在已经成为键字段。
#XMSG: key property of a column has been removed
validationChangeMgmtKeyPropertyRemoved=这个字段已经不再是键字段。
#XMSG: title text of message, change of business name: params: {0} number of columns
VAL_CHANGE_MGMT_UPD_BN_COL_MSG_HDR=已更新业务名称的列（{0}）。
#XMSG: generic message to fill description of message with a list of column names: params: {0} list (with line breaks) of columns
VAL_CHANGE_MGMT_COLUMNS_MSG_DESC={0}
#XMSG: displayed next to a column name to indicate whether the value is new (maintained for the first time). Example: The business name of a column got maintained and before only the technical name was in place
VAL_VALUE_NEW=（新）
#XMSG: displayed next to a column name to indicate whether the value has been resetted/deleted. Example: The business name of a column or source has been removed
VAL_VALUE_DELETED=（已删除）
#XFLD: Label for source type node change management
source=源
#XFLD: Label for target type node change management
target=目标
#XFLD: Tooltip label for view icon
view=查看次数
#~~~~~~~~~~~~~~~~~~~~~~~ Change Management texts ends here ~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary Editor texts starts here ~~~~~~~~~~~~~~~~~~~~

#XTIT: title of the secondry diagram editor (graphical view editor)
graphicalViewEditor=图形化视图编辑器
#XTIT: title of the secondry diagram editor (sql view editor)
sqlViewEditor=SQL 视图编辑器
#XTIT: title of the panel in Entity/Source properties panel
deltaSettings=增量设置
#XFLD: decides which object of delta table to be used(Delta Capture or All Active Records)
LoadFromLocalTable=从本地表加载
#XFLD: decides which object of delta table to be used(Delta Capture or All Active Records)
LoadFromTable=从表加载
#XRBL: only delta records will be processed when this option is selected
deltaCapture=增量捕获
#XRBL: all the records will be processed when this option is selected
allActiveRecords=所有活动记录
#XMSG: Error multiple delta source exist with Delta Capture selected
validationMultipleDeltaSource=只能将一个源表的 "从本地表加载" 选项设置为值 "增量捕获"。
#XMSG: Error Open SQL Schema/HDI Container table cannot be used as a source: params: {0} technical name of the object
validationOpenSchemaSource=在转换流中，数据库模式或 HDI 容器表 "{0}" 不能用作源。
#XMSG: Error remote table cannot be used as a source :params: {0} technical name of the object
validationRemoteTableSource=在转换流中，远程表 "{0}" 不能用作源。
#XMSG: Error view cannot be used as a source :params: {0} technical name of the object
validationViewSource=在转换流中，视图 "{0}" 不能用作源。
#XMSG: Error Aggregation node cannot be used with delta source(source with Delta Capture selected)
validationAggregationWithDelta=如果有任何源表的 "从本地表加载" 选项设置了值 "增量捕获"，就不能使用聚合运算符。
#XMSG: Error Left Outer Join with Delta Table as Outer Operand
validationErrorLeftOuterJoinWithDeltaTable=不可以使用启用了增量捕获设置的源表作为 LEFT-OUTER-JOIN 的外部操作数。
#XMSG: Info: Outer Join with Delta Table as Outer Operand
validationErrorOuterJoinWithDeltaTable=使用启用增量的源表作为左联接或右联接的外部操作数可能会导致运行时出错。
#XMSG: Error Union with Delta Source
validationErrorUnionWithDeltaSource=如果有任何源表的 "从表加载" 选项设置了 "增量捕获" 值，就不能使用合并运算符。
#XMSG: Error Timestap column should not be used in any calculated column
validationForCalculatedColumnWithDelta=列 {0} 不可以用在计算所得列 {1} 中。
#XMSG: Warning
invalidSQLViewDefinitionConfirmation=SQL 语句验证失败。
#XMSG: Warning SQL statment contains validation error
sqlErrorConfirmation=SQL 语句包含验证错误。
#XMSG: Error multiple delta source exist in the sql statement
validationSQLEditorMultipleDeltaSource=SQL 语句中包含多个增量源。只有一个增量表可以用作源。
#XBTN: delete button on parameters panel
deleteParameters=删除
#XMSG: loading message shown in busy dialog when parameter is appended in sql statement
loadingParameters=正在加载参数。
#XMSG: Open SQl schema source using one remote source directly or indirectly
validationOpenSchemaRemoteSource=对象 "{0}" 引用了远程表 "{1}"（直接或间接）。
#XMSG: Open SQl schema source using multiple remote sources directly or indirectly
validationOpenSchemaRemoteSources=对象 "{0}" 引用了下列远程表（直接或间接）：{1}
#XMSG: Open SQL schema source DAC validation
validationOpenSchemaDAC=没能添加对象 "{0}"。这个对象（或依赖对象）应用了数据访问控制。

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary Editor texts ends here ~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Dialog Boxes texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error new editor is opened and the object is not saved and Run button is clicked
runUnsavedError=需要先保存转换流才能运行。
#XTIT
runError=错误
#XMSG: Error unsaved changes in the editor and Run button is clicked
runModifiedError=转换流中有更改还没保存。请保存转换流。
#XTIT
runWarning=警告
#XMSG: Warning the object has changes to deploy and Run button is clicked
runDeployedVersion=还没有部署最新版本的转换流。系统将运行上次部署的转换流版本，是否要继续？
#XBTN: run with error or warning
runAnyway=运行转换流
#XBTN
close=关闭
#XMSG: Error object has validation error
runWithValidationErrors=转换流包含验证错误。运行转换流可能失败。
#XTIT
waitBusy=请稍候。
#XMSG: initiating transformation flow run
runBusy=正在准备数据...
#XMSG: Success
runSuccess=转换流运行已经开始
#XMSG: Error
runFail=转换流运行失败
#XTIT: loading dialog title
loading=正在加载
#XMSG: fetching run details from the server
loaderDetails=正在从服务器获取详细信息
#XMSG: Error graphical view transform with error cannot be converted to SQL view transform
convertGVTransformToSQLViewTransformError=出错的“图形化视图转换”不能转换为“SQL 视图转换”。
#~~~~~~~~~~~~~~~~~~~~~~~ Dialog Boxes texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Validation texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error Duplicate technical name validation message : params: {0} technical name of the object
validationDuplicateTechnicalName=资源库中已经存在对象 "{0}"。
#XMSG: Error There should be a technical name for the target table.
validationEmptyTechnicalTargetTable=输入目标表的技术名称。
#XMSG: Error View transform operator not defined(template node is shown in the diagram)
validationEmptyViewTransform=还没有为转换流定义视图转换运算符。
#XMSG: Error source operator not defined(template node is shown in the diagram) in large system spaces
validationEmptySource=还没有为转换流定义源运算符。
#XMSG: Error Target table not defined (template node is shown in the diagram)
validationEmptyTargetTable=转换流不包含目标表。
#XMSG: Error validation message for incompatible data type mapping in target : params: {0} source column name {1} source column data type {2} target column name {3} target column data type {4} label of the target node
validationTargetColumnMappingIncompatible=在运算符 "{4}" 中，{0} ({1}) 与 {2} ({3}) 不兼容。
#XMSG: Error validation message for incompatible mapping for timestamp column :  params: {0} & {1} and compatible source and column names
validationSouceTargetColumnMappingIncompatibleTimestamp=列 {0} 只能映射到目标表中的列 {1}。
#XMSG: Error No mappping defined in the target operator : params: {0} label of the target node
validationTargetMappingMissing=没有在运算符 "{0}" 中定义映射。
#XMSG: Error No mappping defined in the target operator : params: {0} name of the primary key column {1} label of the target node
validationTargetMappingMissingForPrimaryKeyColumn=没有在运算符 {1} 中定义键列 "{0}" 的映射。
#XMSG: warning: No mappping defined for change Type Column
validationTargetMappingMissingForChangeTypeColumn=源 {0} 字段没有映射到目标 {0} 字段。
#XMSG: Info: For change Date Column, The timestamp of this field will be set to the time that the transformation flow runs
validationTargetMappingMissingForChangeDateColumn={0} 字段与映射不相关。这个字段的时间戳将设置为转换流运行的时间。
#XMSG: Error No columns defined in the target operator : params: {0} label of the target node
validationEmptyColumns=没有为运算符 "{0}" 定义列。
#XMSG: Error load type "Initial and Delta" not supported when the truncate mode is enbled in the target table properties
validationTruncateInvalidMode=如果为目标表启用 "截断" 选项，将不支持 "初始和增量" 加载类型。
#XMSG: Error load type "Initial and Delta" not supported when the deleteAllBeforeLoading mode is enbled in the target table properties
validationDeleteAllBeforeLoadingInvalidMode=如果为目标表启用 "加载前全部删除" 选项，将不支持 "初始和增量" 加载类型。
#XMSG: Error Object has duplicated technical name. : params: {0} technical name of the object
validationDuplicateTargetTechnicalName=资源库中已经存在对象 "{0}"。请输入其他名称。
#XMSG: Error technical name field is empty
validationEmptyTechnicalName=输入表的技术名称。
#XMSG: Error business name field is empty
validationEmptyBusinessName=输入表的业务名称。
#XMSG: Error message for data type mismatch in target : params: {0} source column name {1} source column data type {2} target column name {3} target column data type
datatypeMismatchTarget=要创建映射，数据类型必须匹配。{0} ({1}) 与 {2} ({3}) 不兼容。
#XMSG: Error load type "Initial and Delta" not supported for non-delta source
validationNonDeltaSource=如果没有为源启用增量，将不支持 "初始和增量" 加载类型。
#XMSG: Error load type "Initial and Delta" not supported for non-delta target
validationNonDeltaTarget=如果没有为目标表启用增量，将不支持 "初始和增量" 加载类型。
#XMSG: Error truncate mode is not supported for delta-enabled target table
validationTruncateDeltaTarget=启用增量的目标表不支持截断模式。
#XMSG: Info Message for upsert of records when target node has truncate off and a key column
validationTruncateOffandKeyColumn=使用视图转换运算符读取的记录将使用 UPSERT 运算加载到目标表。
#XMSG: Error deleteAllBeforeLoading mode is not supported for delta-enabled target table
validationDeleteAllBeforeLoadingDeltaTarget=启用增量的目标表不支持 "加载前全部删除" 模式。
#XMSG: Info Message for upsert of records when target node has deleteAllBeforeLoading off and a key column
validationDeleteAllBeforeLoadingOffandKeyColumn=使用视图转换运算符读取的记录将使用 UPSERT 运算加载到目标表。
#XMSG: delta-enabled source cannot be used with non-delta target : params: {0} label of the target node
validationDeltaSourceNonDeltaTarget=启用增量的源不能与没有启用增量的目标 "{0}" 一同使用。
#XMSG: Error graphicalview transform has a validation error.
validationGraphicalViewTransformHasError=图形化视图转换存在一个或多个错误。要查看这些错误，请编辑 "图形化视图转换"，然后点击 "验证消息"。
#XMSG: Error sql view transform has a validation error.
validationSQLViewTransformHasError=SQL 视图转换存在一个或多个错误。要查看这些错误，请编辑 "SQL 视图转换"，然后点击 "验证消息"。
#XMSG: Error graphical view transform has a change management error.
validationGraphicalViewTransformHasChangeManagement=一个或多个源表已更改。要查看这些更改，请编辑 "图形化视图转换"，然后点击 "验证消息"。
#XMSG: Error sql view transform has a change management error.
validationSQLViewTransformHasChangeManagement=一个或多个源表已更改。要查看这些更改，请编辑 "SQL 视图转换"，然后点击 "验证消息"。
#XMSG: Info load type set to intial and delta as source and traget are delta tables
validationInitialDeltaLoadType=因为源表和目标表都启用了增量捕获功能，所以默认加载类型为 "初始和增量"。
#XMSG: Info reset target table watermark
validationDeltaTargetTableChanged=目标表已更改。只有增量数据会传输到新的目标表。如果希望所有数据传输到目标表，可以在数据集成监控器中重置水印。
#XMSG: Error delta table from BW bridge cannot be used in SQL Script view transform
validationBWDeltaSourceInSQLScriptTransform=要使用 SQL 视图转换加载 SAP BW 网桥空间内远程表中的增量更改，需要使用 SQL（标准查询）语言。不支持使用 SQLScript（表函数）。  
#XMSG: No primary key column in new delta target
validationNoKeyColumn=要启用增量捕获设置，目标表需要包含至少一个键列。
#XMSG: Duplicate column names in new delta target
validationColumnNameNotUnique=列名称需要具有唯一性。{0} 在表 "{1}" 中出现多次。
#XMSG: Missing CDC Columns in new delta target
validationMissingCDCColumns=表 {0} 已启用增量捕获设置，但缺少增量捕获列。
#XMSG: Validate non LTF target in large system space
validationLTFDeltaTargetLargeSystemSpace=目标表 "{0}" 需要启用增量，并在大型系统空间中存储文件。
#XMSG: Validation error if a transformation flow from hana runtime space is imported into large system space
validationNonLsaTFInLsaSpace=使用 SAP HANA 运行时创建的转换流不能在大型系统空间中使用。
#XMSG: Always show a validation info message in the python node to inform the user to update the python script manually to generate the data to match the output columns.
validationUpdatePythonScript=更新输出列模式，匹配 Python 脚本中的数据结构返回的列。
#XMSG: Error message for the python operator for changed CDC columns
validationChangedCDCColumns=增量捕获列在 Python 脚本中缺失或已被修改。
#XMSG: Validation error if a transformation flow from spark runtime space is imported into non large system space
validationLsaTFInNonLsaSpace=无法在具有 SAP HANA 数据库（磁盘和内存）存储的空间中创建含有 spark 运行时的转换流。
#~~~~~~~~~~~~~~~~~~~~~~~ Validation texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Data Preview texts starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error data preview is not available for selected operator
dataPreviewNotSupp=数据预览功能不适用于这个运算符。
#XMSG: Error data preview is not available as the selected node is a template.
dataPreviewTemplate=数据预览功能不适用于这个节点。
#XMSG: Error data preview is not available as the selected node is a new target table. i.e. the target table is not created
dataPreviewNewTarget=数据预览功能不适用于这个目标表。
#XMSG: Error data preview is not avalibale as there are no columns in the selected node
dataPreviewEmptyColumns=这个表没有可显示的列，因此不能使用数据预览功能。
#XMSG Error insuffcient privileges to view data
dataPreviewInSufficientPrivileges=你没有足够的权限查看这些数据
#XMSG Not suffcient privileges to view data
dataPreviewNoPrivileges=你没有查看这些数据的权限。
#XTIT Data preview problems tab
txtProblems=错误
#XMSG: Error data preview is not available for SQL Script
dataPreviewNotSupportedSQLScript=数据预览功能不适用于 SQLScript（表函数）。
#XMSG: Error message for data preview with Cross space object with Input parameters
dataPreviewNotSupportedRemoteTableWithParam=没能预览数据 :\n视图转换具有包含输入参数的跨空间对象，不支持数据预览。
#~~~~~~~~~~~~~~~~~~~~~~~ Data Preview texts ends here~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG:view transform is modified and changes are applied to the editor
viewTransformChangesUpdated=已应用视图转换的所有更改。
#XMSG:confirm switch from graphical view to sql view in the view transform
convertToSQLViewConfirmation=正在将视图转换的类型更改为 SQL 视图转换。这项更改不可以撤销。
#XTIT Save Dialog param
modelNameTransformationFlow=转换流
#XMSG: Info automap operation cannot be performed due non matching columns
autoMapInvalid=列的名称和数据类型必须匹配，才能进行自动映射。
#XMSG Info: mapping already exists
mappingExists=映射已经存在。
#XMSG: There are invalid columns in the target table
invalidColumns=没能映射以下列，这些列将会被移除。
#XFLD: Label for package select
package=包

#XMSG warning for repository package assignment changed for Object name {1}
VAL_PACKAGE_CHANGED=这个对象已经分配给包 {1}。点击 "保存"，确认并验证这项更改。请注意，保存后将不能在这个编辑器中撤销对包的分配。
#XMSG warning for repository package dependency issue for object name{0}
MISSING_DEPENDENCY=对象 "{0}" 的依赖项不能在包 "{1}" 的产品上下文中解析。
#XFLD: Mapped to parameter name{0}
mappedTo=已映射到：{0}
#XFLD: Value parameter default Value{0}
defaultVal=值：{0}
#XFLD: Not Mapped
notMapped=没有映射
#XMSG : Validation message for views with remote table name {1} for view name {0}
validateViewWithRemoteTables=视图 "{0}" 引用了下列远程表（直接或间接）："{1}"
#XMSG : Validation message for views with DAC for view name {0}
validationDAC=没能添加视图 "{0}"。这个视图（或依赖视图）应用了数据访问控制。
#XMSG : Validation message for objects with remote tables, remote table name{0} in sql editor,
validateSqlRemoteTables=SQL 语句包含的一个或多个对象引用了下列远程表（直接或间接）："{0}"
#XBTN : Set Value
setValue=设置值
#XBTN : Map TO
mapTo=映射到
#XMSG : This source name{0} contains input parameters.
parameterMappingInfo=这个源 {0} 包含输入参数。对于每个参数，你可以设置一个值，或映射到视图中的输入参数。
#XBTN : Cancel
cancel=取消
#XTIT : Set Value for Input Parameter name {0}
setValueForParam=设置输入参数 {0} 的值
#XMSG : Value
enterValue=值
#XMSG : Enter Valid Decimal value with valid precision and scale
VAL_ENTER_VALID_DECIMAL_GEN=输入精度为 {0} 且小数位为 {1} 的有效小数值。
#XFLD : Runtime
runtime=运行时
#XFLD: Storage
storage=存储
#XMSG: Info message for skipping SQL validation when tenant upgrade is in progress
VAL_TENANT_UPGRADE_IN_PROGRESS=正在进行租户升级，因此跳过了 SQL 视图转换验证。请在部署转换流之前先验证 SQL 视图转换。
#XMSG: Error LTF cannot be used as a source :params: {0} technical name of the object
validationLTFSource=在转换流（spark）中，不可以将本地表（文件）"{0}" 用作源。
#XMSG: Error shared tables cannot be used as a source :params: {0} technical name of the object
validationSharedSource=在转换流（spark）中，不可以将共享表（文件）"{0}" 用作源。
#XMSG: Error Only LTF are supported in large system space
validationNonLTFSource=在转换流（spark）中，不可以使用源 "{0}"，因为它不是本地表（文件）。
#~~~~~~~~~~~~~~~~~~~~~~~ Python Operator texts starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XTOL: Tooltip for python operator in context pad
pythonOperator=Python 运算符

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary sources properties starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Show Sources
showSources=显示源：
#XFLD: Qualified Name
qualifiedName=限定名称
#XFLD: Space Name
spaceName=空间名称
#XFLD: Context Name
contextName=产品上下文名称
#XFLD: Connection
connection=连接
#XFLD: Remote Table
remoteTable=远程表
#XFLD: Active Records
activeRecords=活动记录
#XFLD: Local Table
localTable=本地表
#XFLD: Local Schema
localSchema=本地模式
#XMSG: no parameters message
noParameters=没有可用参数
#XMSG: Validation Error in secondary source error message
secondarySourceErrorDialog=没能显示视图转换源，因为 SQL 视图转换中至少存在一个错误。
#XMSG: Unable to load sources error message
secondarySourceLoadingErrorDialog=没能加载视图转换源。
#XMSG: No sources error message
noSecondarySourceErrorDialog=视图转换没有任何源。
#XTIT Local Table (File)
LTFTable=本地表（文件）
#XMSG: Error data preview is not available for undeployed SQLScript Table function
dataPreviewNotDeployWarning=在部署所有更改之前，视图转换数据在此处不可见。请部署转换流，然后重试。
#Input parameters
#XFLD : Input Parameters
parametersTitle=输入参数（{0}）
#XMSG: Error message for empty input parameters
noInputParameters=目前没有可用的参数。要添加参数，请按 "编辑" 按钮。
#XTOL: Tooltip for edit input parameters
parametersTooltip=编辑输入参数
#XMSG: edit input parameters
editInputParameters=编辑输入参数
#XMSG: Create input parameters in the object to be used in inner filters
parametersDescription=在对象中创建输入参数，在内部筛选器中使用。
#XBUT: Add new parameter
addParameters=添加新参数
#XBUT: Delete parameter
deleteParameter=删除参数
#XFLD: Data Type
parameterDatatype=数据类型
#XFLD: Name
inputParameterName=名称
#XFLD: Placeholder for string input type
placeholderText=输入默认值
#XFLD: Default Value
defaultValue=默认值
#XFLD defined by
definedBy=定义者
#XMSG Error message for empty input parameter name
VAL_PARAM_EMPTY_NM=参数的名称为空。
#XMSG Error message for parameter name not unique
VAL_PARAM_NAME_NOT_UNIQUE=参数名称不唯一。
#XMSG: Error message when object is not deployed yet
cannotViewNotDeployed=从来没有部署过对象 "{0}"。请先部署，然后再查看。
#XFLD: Placeholder for string input type
stringPlaceholderText=输入字符串
#XFLD: Placeholder for integer input type
intPlaceholderText=输入数字
#XFLD: Placeholder for decimal input type
decPlaceholderText=输入小数值
#NOTR: Placeholder for date format Filter
dateFormatFilter=YYYY-MM-DD
#NOTR: Placeholder for time format Filter
timeFormatFilter=HH:mm:ss
#NOTR: Placeholder for date and time format Filter
dateTimeFormatFilter=YYYY-MM-DD, HH:mm:ss
#XTXT: Text for true dropdown of boolean datatype for input parameter - the value should be in small case matching the actual data
param_true=真
#XTXT: Text for false dropdown of boolean datatype for input parameter - the value should be in small case matching the actual data
param_false=假
#XTXT: Text for None dropdown of boolean datatype for input parameter
none=无
#XFLD: Input Parameter Name
OperatorName=名称
#XFLD: Input Parameter Value
Value=值
#XBTN: run button of run with parameter dialog
TXT_RUN=运行
#XFLD
InputParameters=输入参数
#XTXT: Text for dialog box for input parameter
EnterParameterValues=请输入参数值，运行转换流。
#XMSG: Error message for invalid number input
INVALID_NUMBER=请输入有效数字。
#XMSG: Error message for invalid decimal input
INVALID_DECIMAL=请输入有效小数。
#XMSG: Error message for invalid date input
INVALID_DATE=请输入有效日期（年-月-日）。
#XMSG: Error message for invalid time input
INVALID_TIME=请输入有效时间（时:分:秒）。
#XMSG: Error message for invalid date and time input
INVALID_DATETIME=请输入有效日期和时间（年-月-日，时:分:秒）。
#XMSG: Error message for invalid Boolean input
INVALID_BOOLEAN=请输入有效布尔值。
#XMSG: Error message for invalid generic input
INVALID_INPUT=输入无效。
#XTIP: Tooltip for parameter value
more=更多
#XTIT
mappingDialogTitle=映射输入参数
