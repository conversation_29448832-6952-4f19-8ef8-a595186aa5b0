#~~~~~~~~~~~~~~~~~~~~~~~ Properties Panel texts starts here ~~~~~~~~~~~~~~~~~~~~
#XTIT
properties=Properti
#XTIT
general=Umum
#XFLD
businessName=Nama Bisnis
#XFLD
technicalName=<PERSON><PERSON>
#XFLD
loadType=Tipe Muatan
#XFLD
deltaCaptureTableName=Nama Tabel Pemindaian Delta
#XSEL
initialOnly=Hanya Awal
#XSEL
initialAndDelta=Awal dan Delta
#XCKL
truncate=Potong
#XCKL
deleteAllBeforeLoading=Hapus Permanen Semua Sebelum Memuat
#XTIT
columns=Kolom
#XTIT
mappings=Pemetaan
#XFLD
columnDataType=Tipe Data
#XFLD
search=Pencarian
#XBUT
autoMap=Petakan Otomatis
#XBUT
removeAllMappings=Hapus semua Pemetaan
#XMSG
noTargetInputs=Anda perlu menentukan operator Perubahan Tampilan untuk menampilkan informasi pemetaan.
#XMSG
noColumnsData=Tidak ada kolom untuk ditampilkan.
#XTOL
@validateModel=Pesan Validasi
#XTOL
@hierarchy=Hierarki
#XTOL
@columnCount=Jumlah Kolom
#XTOL
info=Info
#XTOL
cdcColumn=Kolom CDC
#XTIT
statusPanel=Status Eksekusi
#XTOL
schedule=Jadwalkan
#XTOL
navToMonitoring=Buka di Pemantau Aliran Perubahan
#XBTN
createSchedule=Buat Jadwal
#XBTN
editSchedule=Edit Jadwal
#XBTN
deleteSchedule=Hapus Permanen Jadwal
#XFLD
lastRun=Eksekusi Terakhir
#XFLD
status=Status
#XMSG: Error run status cannot be fetched
errorDetails=Tidak dapat mengambil status eksekusi.
#XLNK
viewDetails=Tampilkan Rincian
#XMSG: Error data is not loading from the server
backendError=Tampaknya data tidak dimuat dari server saat ini. Coba lagi nanti.
#XMSG: Error transformation flow run failed. open data integration monitoring for more details
viewDetailsFallback=Eksekusi aliran perubahan gagal dimulai. Navigasikan ke aplikasi Pemantauan untuk rincian selengkapnya.
#XMSG
statusCompleted=Selesai
#XMSG
statusRunning=Sedang Dieksekusi
#XMSG
statusFailed=Gagal
#XMSG
statusNotExecuted=Belum Dieksekusi
#XBTN
editColumns=Edit Kolom
#XBUT: button in the properties panel of the target table
createNewTargetTable=Buat Tabel Target Baru
#XMSG: message shown in the properties panel of target table when it is not defined.
defineTargetInformation=Untuk membuat tabel target baru, klik tombol Buat Tabel Target Baru di bawah ini. Untuk menggunakan tabel yang sudah ada sebagai tabel target, Anda dapat menyeret dan menjatuhkan tabel dari repositori ke kanvas.
#XTIT
defineTarget=Tentukan Tabel Target
#XTIT
createViewTransform=Buat Perubahan Tampilan
#XMSG: message shown in the properties panel of view transform when it is not defined.
createViewTransformInformation=Untuk memulai aliran perubahan Anda, buat perubahan tampilan dengan mengklik tombol yang relevan di bawah ini.
#XMSG: message shown in the properties panel of view transform when it is not defined.
createViewTransformInformationDndSpark=Untuk memulai aliran perubahan, tentukan sumber terlebih dahulu dengan menyeret tabel sumber dari panel repositori lalu meletakkannya, atau dengan mengklik tombol yang tersedia di bawah ini.
#XBUT: button in the properties panel of view transform
sqlViewTransform=Perubahan Tampilan SQL
#XBUT: button in the properties panel of view transform
graphicalViewTransform=Perubahan Tampilan Grafis
#XFLD: view transform operator type - the value will be either SQL View or Graphical View
viewTransformType=Tipe
#XMSG: value of type property
sqlView=Tampilan SQL
#XMSG: value of type property
graphicalView=Tampilan Grafis

#~~~~~~~~~~~~Script properties text~~~~~~~~~~~~~~~~~~~~~
#XBTN: select all columns button text
selectAllColumns=Pilih Semua
#XBUT: Add column menu button
addColumn=Tambahkan Kolom
#XBUT: Create new column menu button
createNewColumn=Buat Kolom Baru
#XBUT: Edit column menu button
editColumn=Edit Kolom
#XBUT: Delete button
deleteColumn=Hapus Permanen
#XTIT: title for select columns dialog
columnSelectionDialogTitle=Pilih Kolom
#XMSG: message shown in the select columns dialog
columnSelectionNoDataText=Kolom Tidak Ditemukan
#XFLD
columnOrigin=Asal
#XFLD
columnLength=Panjang
#XFLD
columnPrecision=Presisi
#XFLD
columnScale=Skala
#XBTN: save button text
save=Simpan
#XTOL: Tooltip text for more button
tooltipTxt=Lainnya
#XTOL: Tooltip text for new column
newColumn=Kolom Baru
#XTIT: title for the script editor
scriptTitle=Skrip
#XMSG: Message strip text in the script editor
messageStripText=Akses portal bantuan untuk mendapatkan informasi dan dokumentasi lengkap.
#XMSG: Learn more link text
linkText=Pelajari Selengkapnya
#XMSG: For a data type with length, the length cannot be greater than the maximum value.
VAL_ELEMENT_PROPERTY_LENGTH_GREATER_THAN_MAX=Nilai untuk panjang tidak boleh lebih besar dari nilai maksimum ({0}).
#XMSG: For a data type with length, the length cannot be negative.
VAL_ELEMENT_PROPERTY_LENGTH_LESS_THAN_ZERO=Nilai untuk panjang tidak boleh kurang dari 0.
#XMSG: For a data type with length, the length cannot be zero.
VAL_ELEMENT_PROPERTY_LENGTH_CANNOT_BE_ZERO=Nilai untuk panjang tidak boleh {0}.
#XMSG: The attribute of an Entity has a data type that must have a length.
VAL_ELEMENT_DATATYPE_NO_LENGTH=Masukkan nilai untuk panjang.
#XMSG: The attribute of an Entity has a data type that must have precision.
VAL_ELEMENT_DATATYPE_NO_PRECISION=Masukkan nilai untuk presisi.
#XMSG: For a data type with precision, the precision cannot be greater than the maximum value.
VAL_ELEMENT_PROPERTY_PRECISION_GREATER_THAN_MAX=Nilai untuk presisi tidak boleh lebih besar dari {0}.
#XMSG: For a data type with precision, the precision cannot be less than the minimum value.
VAL_ELEMENT_PROPERTY_PRECISION_LESS_THAN_MIN=Nilai untuk presisi tidak boleh kurang dari {0}.
#XMSG: For a data type with precision and scale, the precision cannot be less than the scale.
VAL_ELEMENT_PROPERTY_PRECISION_LESS_THAN_SCALE=Nilai untuk presisi tidak boleh kurang dari skala ({0})
#XMSG: The attribute of an Entity has a data type that must have scale.
VAL_ELEMENT_DATATYPE_NO_SCALE=Masukkan nilai untuk skala.
#XMSG: For a numeric data type like Decimal(precision, scale), the scale cannot be negative.
VAL_ELEMENT_PROPERTY_SCALE_LESS_THAN_ZERO=Nilai untuk skala tidak boleh kurang dari 0.
#XMSG: For a numeric data type like Decimal(precision, scale), the scale cannot be greater than the precision.
VAL_ELEMENT_PROPERTY_SCALE_GREATER_THAN_PREC=Nilai untuk skala tidak boleh lebih besar dari presisi ({0}).
#~~~~~~~~~~~~~~~~~~~~~~~ Properties Panel texts ends here ~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Diagram texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XTOL: diagram tool item
zoomToFit=Sesuaikan dengan Ukuran Jendela
#XTOL: tooltip for context pad to open data preview (new text)
dataViewer=Penampil Data
#XTOL: tooltip for context pad to open data preview (old text)
previewData=Pratinjau Data
#XTOL: tooltip for context pad to open impact and lineage editor
openImpactLineage=Analisis Dampak dan Silsilah
#XTOL: tooltip for context pad to open object in new tab
openInNewTab=Buka di Tab Baru
#XTOL: tooltip for context pad to create new target table
createTable=Buat Tabel Baru
#XTOL: tooltip for context pad to edit view transform operator
edit=Edit
#XTOL: tooltip for context pad to clear the source or target node in the diagram
remove=Hapus
#XTOL: tooltip for context pad to define view transform operator using SQL editor
useSQLViewTransform=Perubahan Tampilan SQL
#XTOL: tooltip for context pad to define view transform operator using Graphical View editor
useGraphicalViewTransform=Perubahan Tampilan Grafis
#XMSG: Error shared table cannot be used as target
unsupportedTargetSharedTable=Anda tidak dapat menggunakan tabel yang dibagikan sebagai tabel target dalam aliran perubahan.
#XMSG: Error SAC artefact cannot be used as target
unsupportedSACTarget=Anda tidak dapat menggunakan objek hanya dapat dibaca sebagai target untuk aliran perubahan.
#XMSG: Error Open SQL Schema/HDI Container table cnnot be used as target
unsupportedTargetOpenSchema=Tidak dimungkinkan untuk menggunakan skema basis data atau Kontainer HDI sebagai target dalam aliran perubahan.
#XMSG: Message to show when view transform validation is in progress
vtValidationInProgress=Validasi perubahan tampilan sedang diproses.
#XMSG: welcome screen message when the editor is new or empty
welcomeText=Mulailah dengan mengklik node untuk menentukan perubahan tampilan atau menambahkan/membuat tabel target.
#XMSG: new welcome screen message when drag and drop of table is enabled (spark)
welcomeTextDndSpark=Mulailah dengan mengklik node atau menyeret dari panel repositori, lalu meletakkannya untuk menambahkan tabel sumber atau target.
#XBUT: confirmation button to switch graphical view source to sql view source in the warning dialog
confirmSQLViewTransform=Konfirmasi
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Pemutakhiran penyewa sedang diproses.

#~~~~~~~~~~~~~~~~~~~~~~~ Diagram texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Change Management texts starts here ~~~~~~~~~~~~~~~~~~~
#XTIT: title for the change management dialog
changeManagementTitle=Pembaruan Target
#XTIT: title for the change management dialog
changeManagementTitleSourceOrTarget=Pembaruan Sumber/Target
#XTIT
objectDisplayName=Nama Tampilan
#XTIT: name of the user who modifed the object
changedBy=Diubah Oleh
#XTIT: the time when the object is modified
changedOn=Diubah Pada
#XTIT
objectType=Tipe
#XBUT
ok=OKE
#XMSG: message in the change management dialog
reviewText=Silakan tinjau modifikasinya. Anda harus menyimpan lalu menyebarkan ulang aliran perubahan agar perubahan diterapkan.
#XMSG: message in the change management dialog
changesText=Tabel target berikut yang digunakan dalam aliran perubahan ini telah dimodifikasi (lihat pesan validasi untuk rinciannya):
#XMSG: message in the change management dialog
changesTextSourceOrTarget=Tabel sumber/target berikut yang digunakan dalam aliran perubahan ini telah dimodifikasi (lihat pesan validasi untuk rinciannya):
#XMSG: Info column removed from the target: params: {0} label of the node
validationChangeMgmtDelColMsgHdr=Kolom dihapus dari {0}.
#XMSG: Info column data type updated in the target: params: {0} label of the node
validationChangeMgmtUpdDTColMsgHdr=Tipe data dari kolom berikut di {0} telah diubah.
#XMSG: Info column added to the target: params: {0} label of the node
validationChangeMgmtNewColMsgHdr=Kolom ditambahkan ke {0}.
#NOTR: description text of message params: {0} list (with line breaks) of columns
validationChangeMgmtColsMsgDesc={0}
#XMSG: data type change information for the column params: {0} column name {1} old data type {2} new data type
validationChangeMgmtUpdDTColMsgDesc={0}, diubah dari {1} menjadi {2}.
#XMSG: title text of message, change of key/isKey: params: {0} number of columns
validationChangeMgmtUpdKeyColMsgHdr=Kolom dengan kunci diatur atau dihapus ({0}).
#XMSG: key property of a column has been set
validationChangeMgmtKeyPropertySet=Bidang ini sekarang menjadi bidang kunci.
#XMSG: key property of a column has been removed
validationChangeMgmtKeyPropertyRemoved=Bidang bukan lagi bidang kunci.
#XMSG: title text of message, change of business name: params: {0} number of columns
VAL_CHANGE_MGMT_UPD_BN_COL_MSG_HDR=Kolom dengan nama bisnis yang diperbarui ({0}).
#XMSG: generic message to fill description of message with a list of column names: params: {0} list (with line breaks) of columns
VAL_CHANGE_MGMT_COLUMNS_MSG_DESC={0}
#XMSG: displayed next to a column name to indicate whether the value is new (maintained for the first time). Example: The business name of a column got maintained and before only the technical name was in place
VAL_VALUE_NEW=(baru)
#XMSG: displayed next to a column name to indicate whether the value has been resetted/deleted. Example: The business name of a column or source has been removed
VAL_VALUE_DELETED=(dihapus permanen)
#XFLD: Label for source type node change management
source=Sumber
#XFLD: Label for target type node change management
target=Target
#XFLD: Tooltip label for view icon
view=Lihat
#~~~~~~~~~~~~~~~~~~~~~~~ Change Management texts ends here ~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary Editor texts starts here ~~~~~~~~~~~~~~~~~~~~

#XTIT: title of the secondry diagram editor (graphical view editor)
graphicalViewEditor=Editor Tampilan Grafis
#XTIT: title of the secondry diagram editor (sql view editor)
sqlViewEditor=Editor Tampilan SQL
#XTIT: title of the panel in Entity/Source properties panel
deltaSettings=Pengaturan Delta
#XFLD: decides which object of delta table to be used(Delta Capture or All Active Records)
LoadFromLocalTable=Muat dari Tabel Lokal
#XFLD: decides which object of delta table to be used(Delta Capture or All Active Records)
LoadFromTable=Muat dari Tabel
#XRBL: only delta records will be processed when this option is selected
deltaCapture=Pemindaian Delta
#XRBL: all the records will be processed when this option is selected
allActiveRecords=Semua Catatan Aktif
#XMSG: Error multiple delta source exist with Delta Capture selected
validationMultipleDeltaSource=Hanya satu tabel sumber yang dapat memiliki nilai Pemindaian Delta untuk opsi Muat Dari Tabel Lokal.
#XMSG: Error Open SQL Schema/HDI Container table cannot be used as a source: params: {0} technical name of the object
validationOpenSchemaSource=Tidak dimungkinkan untuk menggunakan skema basis data atau tabel kontainer HDI "{0}" sebagai sumber dalam aliran perubahan.
#XMSG: Error remote table cannot be used as a source :params: {0} technical name of the object
validationRemoteTableSource=Tidak dimungkinkan untuk menggunakan tabel jarak jauh "{0}" sebagai sumber dalam aliran perubahan.
#XMSG: Error view cannot be used as a source :params: {0} technical name of the object
validationViewSource=Tidak dimungkinkan untuk menggunakan tampilan "{0}" sebagai sumber dalam aliran perubahan.
#XMSG: Error Aggregation node cannot be used with delta source(source with Delta Capture selected)
validationAggregationWithDelta=Operator agregasi tidak dapat digunakan jika salah satu tabel sumber memiliki nilai Pemindaian Delta untuk opsi Muat Dari Tabel Lokal.
#XMSG: Error Left Outer Join with Delta Table as Outer Operand
validationErrorLeftOuterJoinWithDeltaTable=Tabel sumber dengan pengaturan pemindaian delta yang diaktifkan tidak dapat digunakan sebagai operand luar dari LEFT-OUTER-JOIN.
#XMSG: Info: Outer Join with Delta Table as Outer Operand
validationErrorOuterJoinWithDeltaTable=Penggunaan tabel sumber yang diaktifkan Delta sebagai operand luar dari penghubung kiri atau kanan dapat menyebabkan kesalahan selama eksekusi.
#XMSG: Error Union with Delta Source
validationErrorUnionWithDeltaSource=Operator gabungan tidak dapat digunakan jika salah satu tabel sumber memiliki nilai Pemindaian Delta untuk opsi Muat dari Tabel.
#XMSG: Error Timestap column should not be used in any calculated column
validationForCalculatedColumnWithDelta=Kolom {0} tidak dapat digunakan di kolom terhitung {1}.
#XMSG: Warning
invalidSQLViewDefinitionConfirmation=Gagal memvalidasi pernyataan SQL.
#XMSG: Warning SQL statment contains validation error
sqlErrorConfirmation=Pernyataan SQL berisi kesalahan validasi.
#XMSG: Error multiple delta source exist in the sql statement
validationSQLEditorMultipleDeltaSource=Beberapa sumber delta ada dalam pernyataan SQL. Hanya satu tabel delta yang dapat digunakan sebagai sumber.
#XBTN: delete button on parameters panel
deleteParameters=Hapus Permanen
#XMSG: loading message shown in busy dialog when parameter is appended in sql statement
loadingParameters=Memuat parameter.
#XMSG: Open SQl schema source using one remote source directly or indirectly
validationOpenSchemaRemoteSource=Objek “{0}” mereferensikan tabel jarak jauh “{1}” (baik secara langsung maupun tidak langsung).
#XMSG: Open SQl schema source using multiple remote sources directly or indirectly
validationOpenSchemaRemoteSources=Objek "{0}" mereferensikan tabel jarak jauh berikut (baik secara langsung maupun tidak langsung): {1}
#XMSG: Open SQL schema source DAC validation
validationOpenSchemaDAC=Tidak dapat menambahkan objek "{0}". Kontrol akses data telah diterapkan pada objek ini (atau objek dependen).

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary Editor texts ends here ~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Dialog Boxes texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error new editor is opened and the object is not saved and Run button is clicked
runUnsavedError=Anda harus menyimpan aliran perubahan sebelum dapat mengeksekusinya.
#XTIT
runError=Kesalahan
#XMSG: Error unsaved changes in the editor and Run button is clicked
runModifiedError=Terdapat perubahan yang belum disimpan dalam aliran perubahan. Silakan simpan aliran perubahan.
#XTIT
runWarning=Peringatan
#XMSG: Warning the object has changes to deploy and Run button is clicked
runDeployedVersion=Versi terbaru aliran perubahan belum disebarkan. Versi aliran perubahan yang terakhir disebarkan akan dieksekusi. Apakah Anda ingin melanjutkan?
#XBTN: run with error or warning
runAnyway=Eksekusi Aliran Perubahan
#XBTN
close=Tutup
#XMSG: Error object has validation error
runWithValidationErrors=Aliran perubahan memiliki kesalahan validasi. Mengeksekusi aliran perubahan dapat mengakibatkan kegagalan.
#XTIT
waitBusy=Silakan tunggu.
#XMSG: initiating transformation flow run
runBusy=Mempersiapkan data...
#XMSG: Success
runSuccess=Eksekusi aliran perubahan telah dimulai
#XMSG: Error
runFail=Gagal mengeksekusi aliran perubahan
#XTIT: loading dialog title
loading=Memuat
#XMSG: fetching run details from the server
loaderDetails=Mengambil rincian dari server
#XMSG: Error graphical view transform with error cannot be converted to SQL view transform
convertGVTransformToSQLViewTransformError=Perubahan Tampilan Grafis yang memiliki kesalahan tidak dapat dikonversi ke Perubahan Tampilan SQL.
#~~~~~~~~~~~~~~~~~~~~~~~ Dialog Boxes texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Validation texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error Duplicate technical name validation message : params: {0} technical name of the object
validationDuplicateTechnicalName=Objek ''{0}'' sudah ada di repositori.
#XMSG: Error There should be a technical name for the target table.
validationEmptyTechnicalTargetTable=Masukkan nama teknis untuk tabel target.
#XMSG: Error View transform operator not defined(template node is shown in the diagram)
validationEmptyViewTransform=Operator Perubahan Tampilan belum ditentukan untuk aliran perubahan.
#XMSG: Error source operator not defined(template node is shown in the diagram) in large system spaces
validationEmptySource=Operator Sumber belum ditentukan untuk aliran transformasi.
#XMSG: Error Target table not defined (template node is shown in the diagram)
validationEmptyTargetTable=Aliran perubahan tidak berisi tabel target.
#XMSG: Error validation message for incompatible data type mapping in target : params: {0} source column name {1} source column data type {2} target column name {3} target column data type {4} label of the target node
validationTargetColumnMappingIncompatible={0} ({1}) tidak sesuai dengan {2} ({3}) dalam operator {4}.
#XMSG: Error validation message for incompatible mapping for timestamp column :  params: {0} & {1} and compatible source and column names
validationSouceTargetColumnMappingIncompatibleTimestamp=Kolom {0} hanya dapat dipetakan ke kolom {1} di tabel target.
#XMSG: Error No mappping defined in the target operator : params: {0} label of the target node
validationTargetMappingMissing=Tidak ada pemetaan ditentukan di operator {0}.
#XMSG: Error No mappping defined in the target operator : params: {0} name of the primary key column {1} label of the target node
validationTargetMappingMissingForPrimaryKeyColumn=Tidak ada pemetaan yang ditentukan untuk kolom kunci "{0}" di operator {1}.
#XMSG: warning: No mappping defined for change Type Column
validationTargetMappingMissingForChangeTypeColumn=Bidang sumber {0} tidak dipetakan ke bidang target {0}.
#XMSG: Info: For change Date Column, The timestamp of this field will be set to the time that the transformation flow runs
validationTargetMappingMissingForChangeDateColumn=Bidang {0} tidak relevan untuk pemetaan. Cap waktu dari bidang ini akan diatur ke waktu eksekusi aliran perubahan.
#XMSG: Error No columns defined in the target operator : params: {0} label of the target node
validationEmptyColumns=Kolom untuk operator "{0}" tidak ditentukan.
#XMSG: Error load type "Initial and Delta" not supported when the truncate mode is enbled in the target table properties
validationTruncateInvalidMode=Tipe muatan Awal dan Delta tidak didukung jika opsi Potong diaktifkan untuk tabel target.
#XMSG: Error load type "Initial and Delta" not supported when the deleteAllBeforeLoading mode is enbled in the target table properties
validationDeleteAllBeforeLoadingInvalidMode=Tipe muatan Awal dan Delta tidak didukung jika opsi Hapus Permanen Semua Sebelum Memuat diaktifkan untuk tabel target.
#XMSG: Error Object has duplicated technical name. : params: {0} technical name of the object
validationDuplicateTargetTechnicalName=Objek ''{0}'' sudah ada di repositori. Silakan masukkan nama yang lain.
#XMSG: Error technical name field is empty
validationEmptyTechnicalName=Masukkan nama teknis untuk tabel.
#XMSG: Error business name field is empty
validationEmptyBusinessName=Masukkan nama bisnis untuk tabel.
#XMSG: Error message for data type mismatch in target : params: {0} source column name {1} source column data type {2} target column name {3} target column data type
datatypeMismatchTarget=Untuk membuat pemetaan, tipe data harus sesuai. {0} ({1}) tidak sesuai dengan {2} ({3}).
#XMSG: Error load type "Initial and Delta" not supported for non-delta source
validationNonDeltaSource=Tipe muatan Awal dan Delta tidak didukung jika sumber tidak diaktifkan delta.
#XMSG: Error load type "Initial and Delta" not supported for non-delta target
validationNonDeltaTarget=Tipe muatan Awal dan Delta tidak didukung jika tabel target tidak diaktifkan delta.
#XMSG: Error truncate mode is not supported for delta-enabled target table
validationTruncateDeltaTarget=Mode potong tidak didukung untuk tabel target yang diaktifkan delta.
#XMSG: Info Message for upsert of records when target node has truncate off and a key column
validationTruncateOffandKeyColumn=Catatan yang dibaca menggunakan operator perubahan tampilan dimuat ke tabel target menggunakan operasi UPSERT.
#XMSG: Error deleteAllBeforeLoading mode is not supported for delta-enabled target table
validationDeleteAllBeforeLoadingDeltaTarget=Mode Hapus Permanen Semua Sebelum Memuat tidak didukung untuk tabel target yang diaktifkan delta.
#XMSG: Info Message for upsert of records when target node has deleteAllBeforeLoading off and a key column
validationDeleteAllBeforeLoadingOffandKeyColumn=Catatan yang dibaca menggunakan operator perubahan tampilan dimuat ke tabel target menggunakan operasi UPSERT.
#XMSG: delta-enabled source cannot be used with non-delta target : params: {0} label of the target node
validationDeltaSourceNonDeltaTarget=Sumber yang diaktifkan delta tidak dapat digunakan dengan target "{0}" yang tidak diaktifkan delta.
#XMSG: Error graphicalview transform has a validation error.
validationGraphicalViewTransformHasError=Terdapat satu atau beberapa kesalahan pada Perubahan Tampilan Grafis. Untuk menampilkan kesalahan ini, edit Perubahan Tampilan Grafis dan klik Pesan Validasi.
#XMSG: Error sql view transform has a validation error.
validationSQLViewTransformHasError=Terdapat satu atau beberapa kesalahan pada Perubahan Tampilan SQL. Untuk menampilkan kesalahan ini, edit Perubahan Tampilan SQL dan klik Pesan Validasi.
#XMSG: Error graphical view transform has a change management error.
validationGraphicalViewTransformHasChangeManagement=Satu atau beberapa tabel sumber telah berubah. Untuk menampilkan perubahan ini, edit Perubahan Tampilan Grafis dan klik Pesan Validasi.
#XMSG: Error sql view transform has a change management error.
validationSQLViewTransformHasChangeManagement=Satu atau beberapa tabel sumber telah berubah. Untuk menampilkan perubahan ini, edit Perubahan Tampilan SQL dan klik Pesan Validasi.
#XMSG: Info load type set to intial and delta as source and traget are delta tables
validationInitialDeltaLoadType=Karena fitur pemindaian delta diaktifkan untuk tabel sumber dan target, tipe pemuatan default adalah Awal dan Delta.
#XMSG: Info reset target table watermark
validationDeltaTargetTableChanged=Tabel target telah diubah. Hanya data delta yang akan ditransfer ke tabel target baru. Jika Anda ingin mentransfer semua data ke tabel target, Anda dapat mengatur ulang markah tirta dalam Pemantau Integrasi Data
#XMSG: Error delta table from BW bridge cannot be used in SQL Script view transform
validationBWDeltaSourceInSQLScriptTransform=Untuk memuat perubahan delta dari tabel jarak jauh yang terletak di ruang BW Bridge menggunakan Perubahan Tampilan SQL, bahasanya harus SQL (Kueri Standar). SQLScript (Fungsi Tabel) tidak didukung.
#XMSG: No primary key column in new delta target
validationNoKeyColumn=Untuk mengaktifkan pengaturan pemindaian delta, tabel target harus berisi setidaknya satu kolom kunci.
#XMSG: Duplicate column names in new delta target
validationColumnNameNotUnique=Nama kolom harus unik. {0} memiliki beberapa kejadian di tabel "{1}".
#XMSG: Missing CDC Columns in new delta target
validationMissingCDCColumns=Pengaturan pemindaian delta diaktifkan untuk tabel {0}, tetapi kolom pemindaian delta tidak ditemukan.
#XMSG: Validate non LTF target in large system space
validationLTFDeltaTargetLargeSystemSpace=Tabel target "{0}" harus diaktifkan delta dengan penyimpanan file dalam ruang sistem yang besar.
#XMSG: Validation error if a transformation flow from hana runtime space is imported into large system space
validationNonLsaTFInLsaSpace=Aliran Perubahan yang dibuat dengan runtime HANA tidak dapat digunakan dalam ruang sistem yang besar.
#XMSG: Always show a validation info message in the python node to inform the user to update the python script manually to generate the data to match the output columns.
validationUpdatePythonScript=Perbarui skema kolom output agar sesuai dengan kolom yang dihasilkan oleh Kerangka Data pada skrip Phython.
#XMSG: Error message for the python operator for changed CDC columns
validationChangedCDCColumns=Kolom pemindaian delta tidak ditemukan atau telah dimodifikasi di skrip Python.
#XMSG: Validation error if a transformation flow from spark runtime space is imported into non large system space
validationLsaTFInNonLsaSpace=Aliran transformasi dengan runtime Spark tidak dapat dibuat di ruang yang menggunakan penyimpanan Basis Data SAP HANA (Disk dan dalam Memori).
#~~~~~~~~~~~~~~~~~~~~~~~ Validation texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Data Preview texts starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error data preview is not available for selected operator
dataPreviewNotSupp=Fungsi pratinjau data tidak tersedia untuk operator ini.
#XMSG: Error data preview is not available as the selected node is a template.
dataPreviewTemplate=Fungsi pratinjau data tidak tersedia untuk node ini.
#XMSG: Error data preview is not available as the selected node is a new target table. i.e. the target table is not created
dataPreviewNewTarget=Fungsi pratinjau data tidak tersedia untuk tabel target ini.
#XMSG: Error data preview is not avalibale as there are no columns in the selected node
dataPreviewEmptyColumns=Fungsi pratinjau data tidak tersedia karena tabel tidak memiliki kolom yang dapat dilihat.
#XMSG Error insuffcient privileges to view data
dataPreviewInSufficientPrivileges=Anda tidak memiliki hak istimewa yang mencukupi untuk menampilkan data ini
#XMSG Not suffcient privileges to view data
dataPreviewNoPrivileges=Anda tidak diizinkan untuk melihat data ini.
#XTIT Data preview problems tab
txtProblems=Kesalahan
#XMSG: Error data preview is not available for SQL Script
dataPreviewNotSupportedSQLScript=Fungsi pratinjau data tidak tersedia untuk SQLScript (Fungsi Tabel).
#XMSG: Error message for data preview with Cross space object with Input parameters
dataPreviewNotSupportedRemoteTableWithParam=Tidak dapat melakukan pratinjau data :\nPratinjau data untuk Perubahan Tampilan dengan Objek lintas ruang yang berisi Parameter Input tidak didukung.
#~~~~~~~~~~~~~~~~~~~~~~~ Data Preview texts ends here~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG:view transform is modified and changes are applied to the editor
viewTransformChangesUpdated=Semua perubahan pada perubahan tampilan diterapkan.
#XMSG:confirm switch from graphical view to sql view in the view transform
convertToSQLViewConfirmation=Anda mengubah tipe perubahan tampilan Anda menjadi perubahan tampilan SQL. Perubahan ini tidak dapat dibatalkan.
#XTIT Save Dialog param
modelNameTransformationFlow=Aliran Perubahan
#XMSG: Info automap operation cannot be performed due non matching columns
autoMapInvalid=Nama dan tipe data kolom harus sesuai untuk pemetaan otomatis.
#XMSG Info: mapping already exists
mappingExists=Pemetaan sudah ada.
#XMSG: There are invalid columns in the target table
invalidColumns=Kolom-kolom berikut tidak dapat dipetakan, sehingga akan dihapus.
#XFLD: Label for package select
package=Paket

#XMSG warning for repository package assignment changed for Object name {1}
VAL_PACKAGE_CHANGED=Anda telah menetapkan objek ini ke paket {1}. Klik Simpan untuk mengonfirmasi dan memvalidasi perubahan ini. Perhatikan bahwa penetapan ke paket tidak dapat dibatalkan di editor ini setelah Anda simpan.
#XMSG warning for repository package dependency issue for object name{0}
MISSING_DEPENDENCY=Dependensi objek ''{0}'' tidak dapat diselesaikan dalam konteks paket ''{1}''.
#XFLD: Mapped to parameter name{0}
mappedTo=Dipetakan ke: {0}
#XFLD: Value parameter default Value{0}
defaultVal=Nilai: {0}
#XFLD: Not Mapped
notMapped=Tidak Dipetakan
#XMSG : Validation message for views with remote table name {1} for view name {0}
validateViewWithRemoteTables=Tampilan "{0}" mereferensikan tabel jarak jauh berikut (baik secara langsung maupun tidak langsung): "{1}"
#XMSG : Validation message for views with DAC for view name {0}
validationDAC=Tidak dapat menambahkan tampilan "{0}". Kontrol akses data telah diterapkan pada tampilan ini (atau tampilan dependen).
#XMSG : Validation message for objects with remote tables, remote table name{0} in sql editor,
validateSqlRemoteTables=Pernyataan SQL berisi satu atau beberapa objek yang mereferensikan tabel jarak jauh berikut (baik secara langsung maupun tidak langsung): "{0}"
#XBTN : Set Value
setValue=Tetapkan Nilai
#XBTN : Map TO
mapTo=Petakan Ke
#XMSG : This source name{0} contains input parameters.
parameterMappingInfo=Sumber ini {0} berisi parameter input. Untuk setiap parameter, Anda dapat menetapkan nilai atau memetakannya ke parameter input di tampilan Anda.
#XBTN : Cancel
cancel=Batalkan
#XTIT : Set Value for Input Parameter name {0}
setValueForParam=Tetapkan Nilai untuk Parameter Input {0}
#XMSG : Value
enterValue=Nilai
#XMSG : Enter Valid Decimal value with valid precision and scale
VAL_ENTER_VALID_DECIMAL_GEN=Masukkan nilai Desimal yang Valid dengan presisi {0} dan skala {1}.
#XFLD : Runtime
runtime=Runtime
#XFLD: Storage
storage=Penyimpanan
#XMSG: Info message for skipping SQL validation when tenant upgrade is in progress
VAL_TENANT_UPGRADE_IN_PROGRESS=Validasi Perubahan Tampilan SQL dilewati karena pemutakhiran penyewa sedang dalam proses. Silakan validasi Perubahan Tampilan SQL dengan menyebarkan aliran perubahan.
#XMSG: Error LTF cannot be used as a source :params: {0} technical name of the object
validationLTFSource=Menggunakan tabel lokal (file) "{0}" sebagai sumber dalam aliran perubahan (spark) tidak dapat dilakukan.
#XMSG: Error shared tables cannot be used as a source :params: {0} technical name of the object
validationSharedSource=Menggunakan tabel yang dibagikan (file) "{0}" sebagai sumber dalam aliran perubahan (spark) tidak dapat dilakukan.
#XMSG: Error Only LTF are supported in large system space
validationNonLTFSource=Menggunakan sumber "{0}" dalam aliran perubahan (spark) tidak dapat dilakukan karena bukan merupakan tabel lokal (file).
#~~~~~~~~~~~~~~~~~~~~~~~ Python Operator texts starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XTOL: Tooltip for python operator in context pad
pythonOperator=Operator Phyton

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary sources properties starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Show Sources
showSources=Tampilkan Sumber:
#XFLD: Qualified Name
qualifiedName=Nama yang Memenuhi Syarat
#XFLD: Space Name
spaceName=Nama Ruang
#XFLD: Context Name
contextName=Nama Konteks
#XFLD: Connection
connection=Koneksi
#XFLD: Remote Table
remoteTable=Tabel Jarak Jauh
#XFLD: Active Records
activeRecords=Catatan Aktif
#XFLD: Local Table
localTable=Tabel Lokal
#XFLD: Local Schema
localSchema=Skema Lokal
#XMSG: no parameters message
noParameters=Tidak ada parameter yang tersedia
#XMSG: Validation Error in secondary source error message
secondarySourceErrorDialog=Sumber perubahan tampilan tidak dapat ditampilkan karena terdapat setidaknya satu kesalahan di Perubahan Tampilan SQL.
#XMSG: Unable to load sources error message
secondarySourceLoadingErrorDialog=Sumber perubahan tampilan tidak dapat dimuat.
#XMSG: No sources error message
noSecondarySourceErrorDialog=Perubahan tampilan tidak memiliki sumber apa pun.
#XTIT Local Table (File)
LTFTable=Tabel Lokal (File)
#XMSG: Error data preview is not available for undeployed SQLScript Table function
dataPreviewNotDeployWarning=Data untuk perubahan tampilan Anda tidak ditampilkan di sini sampai semua perubahan disebarkan. Harap sebarkan aliran perubahan, lalu coba lagi.
#Input parameters
#XFLD : Input Parameters
parametersTitle=Parameter Input ({0})
#XMSG: Error message for empty input parameters
noInputParameters=Saat ini tidak ada parameter yang tersedia. Untuk menambahkan, tekan tombol Edit.
#XTOL: Tooltip for edit input parameters
parametersTooltip=Edit parameter input
#XMSG: edit input parameters
editInputParameters=Edit parameter input
#XMSG: Create input parameters in the object to be used in inner filters
parametersDescription=Buat parameter input dalam objek yang akan digunakan di filter bagian dalam.
#XBUT: Add new parameter
addParameters=Tambahkan parameter baru
#XBUT: Delete parameter
deleteParameter=Hapus permanen parameter
#XFLD: Data Type
parameterDatatype=Tipe Data
#XFLD: Name
inputParameterName=Nama
#XFLD: Placeholder for string input type
placeholderText=Masukkan nilai default
#XFLD: Default Value
defaultValue=Nilai Default
#XFLD defined by
definedBy=Ditetapkan Oleh
#XMSG Error message for empty input parameter name
VAL_PARAM_EMPTY_NM=Nama parameter kosong.
#XMSG Error message for parameter name not unique
VAL_PARAM_NAME_NOT_UNIQUE=Nama parameter tidak unik.
#XMSG: Error message when object is not deployed yet
cannotViewNotDeployed=Objek "{0}" belum pernah disebarkan. Sebarkan objek ini sebelum menampilkannya.
#XFLD: Placeholder for string input type
stringPlaceholderText=Masukkan string
#XFLD: Placeholder for integer input type
intPlaceholderText=Masukkan angka
#XFLD: Placeholder for decimal input type
decPlaceholderText=Masukkan nilai desimal
#NOTR: Placeholder for date format Filter
dateFormatFilter=YYYY-MM-DD
#NOTR: Placeholder for time format Filter
timeFormatFilter=HH:mm:ss
#NOTR: Placeholder for date and time format Filter
dateTimeFormatFilter=YYYY-MM-DD, HH:mm:ss
#XTXT: Text for true dropdown of boolean datatype for input parameter - the value should be in small case matching the actual data
param_true=benar
#XTXT: Text for false dropdown of boolean datatype for input parameter - the value should be in small case matching the actual data
param_false=salah
#XTXT: Text for None dropdown of boolean datatype for input parameter
none=Tidak ada
#XFLD: Input Parameter Name
OperatorName=Nama
#XFLD: Input Parameter Value
Value=Nilai
#XBTN: run button of run with parameter dialog
TXT_RUN=Eksekusi
#XFLD
InputParameters=Parameter Input
#XTXT: Text for dialog box for input parameter
EnterParameterValues=Masukkan nilai Parameter untuk mengeksekusi aliran perubahan Anda
#XMSG: Error message for invalid number input
INVALID_NUMBER=Silakan masukkan nomor yang valid.
#XMSG: Error message for invalid decimal input
INVALID_DECIMAL=Silakan masukkan desimal yang valid.
#XMSG: Error message for invalid date input
INVALID_DATE=Silakan masukkan tanggal yang valid (TTTT-BB-HH).
#XMSG: Error message for invalid time input
INVALID_TIME=Silakan masukkan waktu yang valid (JJ:mm:dd).
#XMSG: Error message for invalid date and time input
INVALID_DATETIME=Silakan masukkan tanggal dan waktu yang valid (TTTT-BB-HH, JJ:mm:dd).
#XMSG: Error message for invalid Boolean input
INVALID_BOOLEAN=Silakan masukkan nilai boolean yang valid.
#XMSG: Error message for invalid generic input
INVALID_INPUT=Input tidak valid.
#XTIP: Tooltip for parameter value
more=Lainnya
#XTIT
mappingDialogTitle=Petakan Parameter Input
