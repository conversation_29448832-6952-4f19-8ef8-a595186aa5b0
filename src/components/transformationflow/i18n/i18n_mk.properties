#~~~~~~~~~~~~~~~~~~~~~~~ Properties Panel texts starts here ~~~~~~~~~~~~~~~~~~~~
#XTIT
properties=Својства
#XTIT
general=Општо
#XFLD
businessName=Деловен назив
#XFLD
technicalName=Технички назив
#XFLD
loadType=Тип вчитување
#XFLD
deltaCaptureTableName=Назив на табела со делта-снимање
#XSEL
initialOnly=Само почетно
#XSEL
initialAndDelta=Почетно и Делта
#XCKL
truncate=Скрати
#XCKL
deleteAllBeforeLoading=Избриши ги сите пред вчитувањето
#XTIT
columns=Колони
#XTIT
mappings=Мапирања
#XFLD
columnDataType=Тип податоци
#XFLD
search=Пребарај
#XBUT
autoMap=Мапирај автоматски
#XBUT
removeAllMappings=Отстрани ги сите мапирања
#XMSG
noTargetInputs=Треба да дефинирате оператор за трансформација на приказ за да ги видите информациите за мапирањето.
#XMSG
noColumnsData=Нема колони за прикажување.
#XTOL
@validateModel=Пораки за потврдување
#XTOL
@hierarchy=Хиерархија
#XTOL
@columnCount=Број на колони
#XTOL
info=Информации
#XTOL
cdcColumn=CDC-колона
#XTIT
statusPanel=Статус на извршување
#XTOL
schedule=Распоред
#XTOL
navToMonitoring=Отвори во мониторот за трансформацискиот тек
#XBTN
createSchedule=Создај распоред
#XBTN
editSchedule=Уреди го распоредот
#XBTN
deleteSchedule=Избриши го распоредот
#XFLD
lastRun=Последно извршување
#XFLD
status=Статус
#XMSG: Error run status cannot be fetched
errorDetails=Не може да се врати статусот на извршување.
#XLNK
viewDetails=Прикажи ги деталите
#XMSG: Error data is not loading from the server
backendError=Изгледа дека податоците не се вчитуваат од серверот во моментов. Обидете се повторно подоцна.
#XMSG: Error transformation flow run failed. open data integration monitoring for more details
viewDetailsFallback=Стартувањето на извршувањето на трансформацискиот тек не е успешно. Одете во апликацијата Следење за повеќе детали.
#XMSG
statusCompleted=Завршено
#XMSG
statusRunning=Се извршува
#XMSG
statusFailed=Неуспешно
#XMSG
statusNotExecuted=Сè уште не е извршено
#XBTN
editColumns=Уреди колони
#XBUT: button in the properties panel of the target table
createNewTargetTable=Создај нова целна табела
#XMSG: message shown in the properties panel of target table when it is not defined.
defineTargetInformation=За да создадете нова целна табела, кликнете на копчето Создај нова целна табела подолу. За да користите постојна табела како целна табела, можете да повлечете па спуштете табела од репозиториумот на платното.
#XTIT
defineTarget=Дефинирај целна табела
#XTIT
createViewTransform=Создај трансформација на приказ
#XMSG: message shown in the properties panel of view transform when it is not defined.
createViewTransformInformation=За да го започнете трансформацискиот тек, создајте трансформација на приказ со кликнување на релевантното копче подолу.
#XMSG: message shown in the properties panel of view transform when it is not defined.
createViewTransformInformationDndSpark=За да го започнете трансформацискиот тек, дефинирајте избор со влечење и спуштање изворна табела од панелот за складови или со кликнување на релевантното копче подолу.
#XBUT: button in the properties panel of view transform
sqlViewTransform=Трансформација на SQL-приказ
#XBUT: button in the properties panel of view transform
graphicalViewTransform=Трансформација на графички приказ
#XFLD: view transform operator type - the value will be either SQL View or Graphical View
viewTransformType=Тип
#XMSG: value of type property
sqlView=SQL-приказ
#XMSG: value of type property
graphicalView=Графички приказ

#~~~~~~~~~~~~Script properties text~~~~~~~~~~~~~~~~~~~~~
#XBTN: select all columns button text
selectAllColumns=Избери ги сите
#XBUT: Add column menu button
addColumn=Додај колона
#XBUT: Create new column menu button
createNewColumn=Создај нова колона
#XBUT: Edit column menu button
editColumn=Уреди ја колоната
#XBUT: Delete button
deleteColumn=Избриши
#XTIT: title for select columns dialog
columnSelectionDialogTitle=Избери колони
#XMSG: message shown in the select columns dialog
columnSelectionNoDataText=Не се најдени колони
#XFLD
columnOrigin=Потекло
#XFLD
columnLength=Должина
#XFLD
columnPrecision=Прецизност
#XFLD
columnScale=Скала
#XBTN: save button text
save=Зачувај
#XTOL: Tooltip text for more button
tooltipTxt=Повеќе
#XTOL: Tooltip text for new column
newColumn=Нова колона
#XTIT: title for the script editor
scriptTitle=Скрипта
#XMSG: Message strip text in the script editor
messageStripText=Пристапете до порталот за помош за да добиете информации и целосна документација.
#XMSG: Learn more link text
linkText=Дознај повеќе
#XMSG: For a data type with length, the length cannot be greater than the maximum value.
VAL_ELEMENT_PROPERTY_LENGTH_GREATER_THAN_MAX=Вредноста за должина не може да биде поголема од максималната вредност ({0}).
#XMSG: For a data type with length, the length cannot be negative.
VAL_ELEMENT_PROPERTY_LENGTH_LESS_THAN_ZERO=Вредноста за должина не може да биде помала од 0.
#XMSG: For a data type with length, the length cannot be zero.
VAL_ELEMENT_PROPERTY_LENGTH_CANNOT_BE_ZERO=Вредноста за должина не може да биде {0}.
#XMSG: The attribute of an Entity has a data type that must have a length.
VAL_ELEMENT_DATATYPE_NO_LENGTH=Внесете вредност за должина.
#XMSG: The attribute of an Entity has a data type that must have precision.
VAL_ELEMENT_DATATYPE_NO_PRECISION=Внесете вредност за прецизност.
#XMSG: For a data type with precision, the precision cannot be greater than the maximum value.
VAL_ELEMENT_PROPERTY_PRECISION_GREATER_THAN_MAX=Вредноста за прецизноста не може да биде поголема од {0}.
#XMSG: For a data type with precision, the precision cannot be less than the minimum value.
VAL_ELEMENT_PROPERTY_PRECISION_LESS_THAN_MIN=Вредноста за прецизноста не може да биде помала од {0}.
#XMSG: For a data type with precision and scale, the precision cannot be less than the scale.
VAL_ELEMENT_PROPERTY_PRECISION_LESS_THAN_SCALE=Вредноста за прецизност не може да биде помала од скалата ({0}).
#XMSG: The attribute of an Entity has a data type that must have scale.
VAL_ELEMENT_DATATYPE_NO_SCALE=Внесете вредност за скалата.
#XMSG: For a numeric data type like Decimal(precision, scale), the scale cannot be negative.
VAL_ELEMENT_PROPERTY_SCALE_LESS_THAN_ZERO=Вредноста за скалата не може да биде помала од 0.
#XMSG: For a numeric data type like Decimal(precision, scale), the scale cannot be greater than the precision.
VAL_ELEMENT_PROPERTY_SCALE_GREATER_THAN_PREC=Вредноста за скалата не може да биде поголема од прецизноста ({0}).
#~~~~~~~~~~~~~~~~~~~~~~~ Properties Panel texts ends here ~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Diagram texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XTOL: diagram tool item
zoomToFit=Зумирај за да приспособиш
#XTOL: tooltip for context pad to open data preview (new text)
dataViewer=Прегледувач на податоци
#XTOL: tooltip for context pad to open data preview (old text)
previewData=Прегледај ги податоците
#XTOL: tooltip for context pad to open impact and lineage editor
openImpactLineage=Анализа на потеклото и влијанието
#XTOL: tooltip for context pad to open object in new tab
openInNewTab=Отвори во нова картичка
#XTOL: tooltip for context pad to create new target table
createTable=Создај нова табела
#XTOL: tooltip for context pad to edit view transform operator
edit=Уреди
#XTOL: tooltip for context pad to clear the source or target node in the diagram
remove=Отстрани
#XTOL: tooltip for context pad to define view transform operator using SQL editor
useSQLViewTransform=Трансформација на SQL-приказ
#XTOL: tooltip for context pad to define view transform operator using Graphical View editor
useGraphicalViewTransform=Трансформација на графички приказ
#XMSG: Error shared table cannot be used as target
unsupportedTargetSharedTable=Не можете да користите споделена табела како целна табела во трансформациски тек.
#XMSG: Error SAC artefact cannot be used as target
unsupportedSACTarget=Не можете да користите објект само за читање како цел за трансформациски тек.
#XMSG: Error Open SQL Schema/HDI Container table cnnot be used as target
unsupportedTargetOpenSchema=Не е можно да се користи шема на база на податоци или HDI контејнер како цел во трансформациски тек.
#XMSG: Message to show when view transform validation is in progress
vtValidationInProgress=Потврдувањето на трансформацијата на приказот е во тек.
#XMSG: welcome screen message when the editor is new or empty
welcomeText=Започнете со кликнување на јазол за да дефинирате трансформација на приказ или да додадете/создадете целна табела.
#XMSG: new welcome screen message when drag and drop of table is enabled (spark)
welcomeTextDndSpark=Започнете со кликнување јазол или со влечење и спуштање од панелот со складови за да додадете извор или целна табела
#XBUT: confirmation button to switch graphical view source to sql view source in the warning dialog
confirmSQLViewTransform=Потврди
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Надградбата на закупецот е во тек.

#~~~~~~~~~~~~~~~~~~~~~~~ Diagram texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Change Management texts starts here ~~~~~~~~~~~~~~~~~~~
#XTIT: title for the change management dialog
changeManagementTitle=Ажурирања на цел
#XTIT: title for the change management dialog
changeManagementTitleSourceOrTarget=Ажурирања на изворот/целта
#XTIT
objectDisplayName=Назив за прикажување
#XTIT: name of the user who modifed the object
changedBy=Променето од
#XTIT: the time when the object is modified
changedOn=Променето на
#XTIT
objectType=Тип
#XBUT
ok=Во ред
#XMSG: message in the change management dialog
reviewText=Прегледајте ги промените. Ќе треба да го зачувате, а потоа да го примените трансформацискиот тек за промените да стапат во сила.
#XMSG: message in the change management dialog
changesText=Следната целна табела што се користи во овој трансформациски тек е изменета (прочитајте ги пораките од потврдувањето за повеќе детали):
#XMSG: message in the change management dialog
changesTextSourceOrTarget=Следната целна/изворна табела што се користи во овој трансформациски тек е изменета (прочитајте ги пораките од потврдувањето за повеќе детали):
#XMSG: Info column removed from the target: params: {0} label of the node
validationChangeMgmtDelColMsgHdr=Колони се отстранети од {0}.
#XMSG: Info column data type updated in the target: params: {0} label of the node
validationChangeMgmtUpdDTColMsgHdr=Типот податоци на следниве колони во {0} е променет.
#XMSG: Info column added to the target: params: {0} label of the node
validationChangeMgmtNewColMsgHdr=Додадени се колони во {0}.
#NOTR: description text of message params: {0} list (with line breaks) of columns
validationChangeMgmtColsMsgDesc={0}
#XMSG: data type change information for the column params: {0} column name {1} old data type {2} new data type
validationChangeMgmtUpdDTColMsgDesc={0}, променето од {1} во {2}.
#XMSG: title text of message, change of key/isKey: params: {0} number of columns
validationChangeMgmtUpdKeyColMsgHdr=Колони со поставени или отстранети клучеви ({0}).
#XMSG: key property of a column has been set
validationChangeMgmtKeyPropertySet=Полето сега е поле на клучот.
#XMSG: key property of a column has been removed
validationChangeMgmtKeyPropertyRemoved=Полето не е веќе поле на клучот.
#XMSG: title text of message, change of business name: params: {0} number of columns
VAL_CHANGE_MGMT_UPD_BN_COL_MSG_HDR=Колони со ажуриран деловен назив ({0}).
#XMSG: generic message to fill description of message with a list of column names: params: {0} list (with line breaks) of columns
VAL_CHANGE_MGMT_COLUMNS_MSG_DESC={0}
#XMSG: displayed next to a column name to indicate whether the value is new (maintained for the first time). Example: The business name of a column got maintained and before only the technical name was in place
VAL_VALUE_NEW=(ново)
#XMSG: displayed next to a column name to indicate whether the value has been resetted/deleted. Example: The business name of a column or source has been removed
VAL_VALUE_DELETED=(избришано)
#XFLD: Label for source type node change management
source=Извор
#XFLD: Label for target type node change management
target=Цел
#XFLD: Tooltip label for view icon
view=Прикажи
#~~~~~~~~~~~~~~~~~~~~~~~ Change Management texts ends here ~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary Editor texts starts here ~~~~~~~~~~~~~~~~~~~~

#XTIT: title of the secondry diagram editor (graphical view editor)
graphicalViewEditor=Уредувач на графички приказ
#XTIT: title of the secondry diagram editor (sql view editor)
sqlViewEditor=Уредувач на SQL-приказ
#XTIT: title of the panel in Entity/Source properties panel
deltaSettings=Делта-поставки
#XFLD: decides which object of delta table to be used(Delta Capture or All Active Records)
LoadFromLocalTable=Вчитај од локална табела
#XFLD: decides which object of delta table to be used(Delta Capture or All Active Records)
LoadFromTable=Вчитај од табела
#XRBL: only delta records will be processed when this option is selected
deltaCapture=Делта-снимање
#XRBL: all the records will be processed when this option is selected
allActiveRecords=Сите активни записи
#XMSG: Error multiple delta source exist with Delta Capture selected
validationMultipleDeltaSource=Само една изворна табела може да ја има вредноста Делта-снимање за опцијата Вчитај од локална табела.
#XMSG: Error Open SQL Schema/HDI Container table cannot be used as a source: params: {0} technical name of the object
validationOpenSchemaSource=Не е можно да се користи шема на база на податоци или табела со HDI контејнер „ {0}“ како извор во трансформацискиот тек.
#XMSG: Error remote table cannot be used as a source :params: {0} technical name of the object
validationRemoteTableSource=Не е можно да се користи табела на далечина „ {0}“ како извор во трансформацискиот тек.
#XMSG: Error view cannot be used as a source :params: {0} technical name of the object
validationViewSource=Не е можно да се користи приказ „ {0}“ како извор во трансформацискиот тек.
#XMSG: Error Aggregation node cannot be used with delta source(source with Delta Capture selected)
validationAggregationWithDelta=Операторот за собирање не може да се користи ако некоја од изворните табели има вредност Делта-снимање за опцијата Вчитај од локална табела.
#XMSG: Error Left Outer Join with Delta Table as Outer Operand
validationErrorLeftOuterJoinWithDeltaTable=Не е можно да се користи изворна табела за која поставката за делта-снимање е овозможена како надворешен операнд на LEFT-OUTER-JOIN.
#XMSG: Info: Outer Join with Delta Table as Outer Operand
validationErrorOuterJoinWithDeltaTable=Користењето изворни табели што се компатибилни со делта како надворешен операнд за лево или десно спојување може да предизвика грешка во текот на извршувањето.
#XMSG: Error Union with Delta Source
validationErrorUnionWithDeltaSource=Операторот union не може да се користи ако некоја од изворните табели ја имаат вредноста Делта-снимање за опцијата Вчитај од табела.
#XMSG: Error Timestap column should not be used in any calculated column
validationForCalculatedColumnWithDelta=Колоната {0} не може да се користи во пресметаната колона {1}.
#XMSG: Warning
invalidSQLViewDefinitionConfirmation=Не успеа да се потврди SQL-наредбата.
#XMSG: Warning SQL statment contains validation error
sqlErrorConfirmation=SQL-наредбата содржи грешки од потврдување.
#XMSG: Error multiple delta source exist in the sql statement
validationSQLEditorMultipleDeltaSource=Во SQL-наредбата постојат повеќе делта-извори. Само една делта-табела може да се користи како извор.
#XBTN: delete button on parameters panel
deleteParameters=Избриши
#XMSG: loading message shown in busy dialog when parameter is appended in sql statement
loadingParameters=Параметрите се вчитуваат.
#XMSG: Open SQl schema source using one remote source directly or indirectly
validationOpenSchemaRemoteSource=Објектот „{0}“ содржи референции за табелата на далечина „{1}" (директно или индиректно).
#XMSG: Open SQl schema source using multiple remote sources directly or indirectly
validationOpenSchemaRemoteSources=Објектот „{0}“ содржи референции за следниве табели на далечина (директно или индиректно): {1}
#XMSG: Open SQL schema source DAC validation
validationOpenSchemaDAC=Објектот „{0}“ не може да се додаде. Контролата за пристап до податоци е применета врз него (или врз зависниот објект).

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary Editor texts ends here ~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Dialog Boxes texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error new editor is opened and the object is not saved and Run button is clicked
runUnsavedError=Треба да го зачувате трансформацискиот тек пред да го извршите.
#XTIT
runError=Грешка
#XMSG: Error unsaved changes in the editor and Run button is clicked
runModifiedError=Има незачувани промени во репликацискиот тек. Зачувајте го репликацискиот тек.
#XTIT
runWarning=Предупредување
#XMSG: Warning the object has changes to deploy and Run button is clicked
runDeployedVersion=Најновата верзија на трансформацискиот тек сè уште не е применета. Ќе се изврши последната применета верзија на трансформацискиот тек. Дали сакате да продолжите?
#XBTN: run with error or warning
runAnyway=Изврши трансформациски тек
#XBTN
close=Затвори
#XMSG: Error object has validation error
runWithValidationErrors=Трансформацискиот тек има грешки при проверка. Извршувањето на трансформацискиот тек може да доведе до грешка.
#XTIT
waitBusy=Почекајте.
#XMSG: initiating transformation flow run
runBusy=Се подготвуваат податоците...
#XMSG: Success
runSuccess=Извршувањето на трансформацискиот тек започна
#XMSG: Error
runFail=Извршувањето на трансформациски тек заврши неуспешно
#XTIT: loading dialog title
loading=Се вчитува
#XMSG: fetching run details from the server
loaderDetails=Се добиваат детали од серверот
#XMSG: Error graphical view transform with error cannot be converted to SQL view transform
convertGVTransformToSQLViewTransformError=Не е возможно да се конвертира трансформацијата на графичкиот приказ со грешки во трансформација на SQL-приказ.
#~~~~~~~~~~~~~~~~~~~~~~~ Dialog Boxes texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Validation texts starts here ~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error Duplicate technical name validation message : params: {0} technical name of the object
validationDuplicateTechnicalName=Објектот „{0}“ веќе постои во репозиториумот.
#XMSG: Error There should be a technical name for the target table.
validationEmptyTechnicalTargetTable=Внесете технички назив за целната табела.
#XMSG: Error View transform operator not defined(template node is shown in the diagram)
validationEmptyViewTransform=Операторот Трансформација на приказ сè уште не е дефиниран за трансформацискиот тек.
#XMSG: Error source operator not defined(template node is shown in the diagram) in large system spaces
validationEmptySource=Сè уште не е дефиниран изворен оператор за трансформацискиот тек.
#XMSG: Error Target table not defined (template node is shown in the diagram)
validationEmptyTargetTable=Трансформацискиот тек не содржи целна табела.
#XMSG: Error validation message for incompatible data type mapping in target : params: {0} source column name {1} source column data type {2} target column name {3} target column data type {4} label of the target node
validationTargetColumnMappingIncompatible={0} ({1}) е некомпатибилен со {2} ({3}) во операторот „{4}“.
#XMSG: Error validation message for incompatible mapping for timestamp column :  params: {0} & {1} and compatible source and column names
validationSouceTargetColumnMappingIncompatibleTimestamp=Колоната {0} може да се мапира само со колоната {1} во целната табела.
#XMSG: Error No mappping defined in the target operator : params: {0} label of the target node
validationTargetMappingMissing=Не е дефинирано мапирање во операторот „{0}“.
#XMSG: Error No mappping defined in the target operator : params: {0} name of the primary key column {1} label of the target node
validationTargetMappingMissingForPrimaryKeyColumn=Не е дефинирано мапирање за колоната на клучот „{0}“ во операторот {1}.
#XMSG: warning: No mappping defined for change Type Column
validationTargetMappingMissingForChangeTypeColumn=Изворното поле {0} не е мапирано во целното поле {0}.
#XMSG: Info: For change Date Column, The timestamp of this field will be set to the time that the transformation flow runs
validationTargetMappingMissingForChangeDateColumn=Полето {0} не е релевантно за мапирање. Временската ознака на ова поле ќе биде поставена на време во кое се извршува трансформацискиот тек.
#XMSG: Error No columns defined in the target operator : params: {0} label of the target node
validationEmptyColumns=Не се дефинирани колони за операторот „{0}“.
#XMSG: Error load type "Initial and Delta" not supported when the truncate mode is enbled in the target table properties
validationTruncateInvalidMode=Типот вчитување Почетно и Делта не е поддржан ако опцијата Скрати е овозможена за целната табела.
#XMSG: Error load type "Initial and Delta" not supported when the deleteAllBeforeLoading mode is enbled in the target table properties
validationDeleteAllBeforeLoadingInvalidMode=Типот вчитување Почетно и Делта не е поддржан ако опцијата Избриши ги сите пред вчитувањето е овозможена за целната табела.
#XMSG: Error Object has duplicated technical name. : params: {0} technical name of the object
validationDuplicateTargetTechnicalName=Објектот „{0}“ веќе постои во репозиториумот. Внесете друг назив.
#XMSG: Error technical name field is empty
validationEmptyTechnicalName=Внесете технички назив за табелата.
#XMSG: Error business name field is empty
validationEmptyBusinessName=Внесете деловен назив за табелата.
#XMSG: Error message for data type mismatch in target : params: {0} source column name {1} source column data type {2} target column name {3} target column data type
datatypeMismatchTarget=Типот податоци мора да се совпаѓа за да се создаде мапирање. {0} ({1}) не е компатибилен со {2} ({3}).
#XMSG: Error load type "Initial and Delta" not supported for non-delta source
validationNonDeltaSource=Типот вчитување Почетно и Делта не е поддржан ако во изворот не е овозможена делта.
#XMSG: Error load type "Initial and Delta" not supported for non-delta target
validationNonDeltaTarget=Типот вчитување Почетно и Делта не е поддржан ако во целната табела не е овозможена делта.
#XMSG: Error truncate mode is not supported for delta-enabled target table
validationTruncateDeltaTarget=Режимот на скратување не е поддржан за целната табела во која е овозможена делта.
#XMSG: Info Message for upsert of records when target node has truncate off and a key column
validationTruncateOffandKeyColumn=Записите што се читаат со помош на операторот за трансформација на приказ се вчитуваат во целната табела со помош на операцијата UPSERT.
#XMSG: Error deleteAllBeforeLoading mode is not supported for delta-enabled target table
validationDeleteAllBeforeLoadingDeltaTarget=Режимот Избриши ги сите пред вчитувањето не е поддржан за целната табела во која е овозможена делта.
#XMSG: Info Message for upsert of records when target node has deleteAllBeforeLoading off and a key column
validationDeleteAllBeforeLoadingOffandKeyColumn=Записите што се читаат со помош на операторот за трансформација на приказ се вчитуваат во целната табела со помош на операцијата UPSERT.
#XMSG: delta-enabled source cannot be used with non-delta target : params: {0} label of the target node
validationDeltaSourceNonDeltaTarget=Изворот во кој е овозможена делта не може да се користи со цел во која не е овозможена делта „{0}“.
#XMSG: Error graphicalview transform has a validation error.
validationGraphicalViewTransformHasError=Постои една или повеќе грешки во трансформацијата на графичкиот приказ. За да ги видите грешките, уредете ја трансформацијата на графичкиот приказ и кликнете Пораки за потврдување.
#XMSG: Error sql view transform has a validation error.
validationSQLViewTransformHasError=Постои една или повеќе грешки во трансформацијата на SQL-приказот. За да ги видите грешките, уредете ја трансформацијата на SQL-приказот и кликнете Пораки за потврдување.
#XMSG: Error graphical view transform has a change management error.
validationGraphicalViewTransformHasChangeManagement=Една или повеќе изворни табели се променети. За да ги видите промените, уредете ја трансформацијата на графичкиот приказ и кликнете Пораки за потврдување.
#XMSG: Error sql view transform has a change management error.
validationSQLViewTransformHasChangeManagement=Една или повеќе изворни табели се променети. За да ги видите промените, уредете ја трансформацијата на SQL-приказот и кликнете Пораки за потврдување.
#XMSG: Info load type set to intial and delta as source and traget are delta tables
validationInitialDeltaLoadType=Бидејќи карактеристиката за делта-снимање е овозможена и за изворните и за целните табели, стандардниот тип на оптоварување е Почетно и Делта.
#XMSG: Info reset target table watermark
validationDeltaTargetTableChanged=Целната табела е променета. Само делта-податоците ќе бидат префрлени на новата целна табела. Ако сакате да ги префрлите сите податоци во целната табела, можете да го ресетирате водениот жиг во Алатката за следење интеграција на податоци.
#XMSG: Error delta table from BW bridge cannot be used in SQL Script view transform
validationBWDeltaSourceInSQLScriptTransform=За вчитување на делта-промените од табелата на далечина што се наоѓа во просторот за премостување на апликацијата BW користејќи транформација на приказ SQL, јазикот мора да биде SQL (стандарден прашалник). SQLScript (функција на табела) не е поддржана.
#XMSG: No primary key column in new delta target
validationNoKeyColumn=За да ја овозможите поставката за делта-снимање, целната табела мора да содржи најмалку една колона со клуч.
#XMSG: Duplicate column names in new delta target
validationColumnNameNotUnique=Називите на колоните мора да бидат единствени. {0} се среќава повеќепати во табелата „{1}“.
#XMSG: Missing CDC Columns in new delta target
validationMissingCDCColumns=Поставката за делта-снимање е овозможена за табелата {0}, но недостигаат колоните за делта-снимање.
#XMSG: Validate non LTF target in large system space
validationLTFDeltaTargetLargeSystemSpace=Во целната табела „{0}“ мора да се овозможи делта со складирање датотеки во големиот простор на системот.
#XMSG: Validation error if a transformation flow from hana runtime space is imported into large system space
validationNonLsaTFInLsaSpace=Трансформацискиот тек создаден во времето на извршување HANA не може да се користи во големиот простор на системот.
#XMSG: Always show a validation info message in the python node to inform the user to update the python script manually to generate the data to match the output columns.
validationUpdatePythonScript=Ажурирајте ја шемата на излезните колони за да се совпаѓаат со колоните што ги враќа податочната рамка во Python-скриптата.
#XMSG: Error message for the python operator for changed CDC columns
validationChangedCDCColumns=Колоните за делта-снимање или недостигаат или се изменети во Python-скриптата.
#XMSG: Validation error if a transformation flow from spark runtime space is imported into non large system space
validationLsaTFInNonLsaSpace=Трансформацискиот тек со време на извршување за spark не може да се создаде во простор со склад на базата со податоци SAP HANA (на диск и во меморија).
#~~~~~~~~~~~~~~~~~~~~~~~ Validation texts ends here ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~~~~~~~~~~~~~ Data Preview texts starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Error data preview is not available for selected operator
dataPreviewNotSupp=Карактеристиката за преглед на податоците не е достапна за овој оператор.
#XMSG: Error data preview is not available as the selected node is a template.
dataPreviewTemplate=Карактеристиката за преглед на податоците не е достапна за овој јазол.
#XMSG: Error data preview is not available as the selected node is a new target table. i.e. the target table is not created
dataPreviewNewTarget=Карактеристиката за преглед на податоците не е достапна за оваа целна табела.
#XMSG: Error data preview is not avalibale as there are no columns in the selected node
dataPreviewEmptyColumns=Карактеристиката за преглед на податоци не е достапна бидејќи табелата нема видливи колони.
#XMSG Error insuffcient privileges to view data
dataPreviewInSufficientPrivileges=Немате доволно привилегии за да ги прикажете податоциве
#XMSG Not suffcient privileges to view data
dataPreviewNoPrivileges=Не ви е дозволено да ги прегледате податоциве.
#XTIT Data preview problems tab
txtProblems=Грешки
#XMSG: Error data preview is not available for SQL Script
dataPreviewNotSupportedSQLScript=Карактеристиката за преглед на податоците не е достапна за SQLScript (табеларна функција).
#XMSG: Error message for data preview with Cross space object with Input parameters
dataPreviewNotSupportedRemoteTableWithParam=Податоците не може да се прегледаат:\nНе е поддржан преглед на податоците за Трансформација на прикази на објекти за сите простори што содржат параметри на внес.
#~~~~~~~~~~~~~~~~~~~~~~~ Data Preview texts ends here~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG:view transform is modified and changes are applied to the editor
viewTransformChangesUpdated=Применети се сите промени во трансформацијата на приказот.
#XMSG:confirm switch from graphical view to sql view in the view transform
convertToSQLViewConfirmation=Го менувате типот на трансформација на приказ во трансформација на SQL-приказ. Оваа промена не може да се врати.
#XTIT Save Dialog param
modelNameTransformationFlow=Трансформациски тек
#XMSG: Info automap operation cannot be performed due non matching columns
autoMapInvalid=Називот и типот на податоците во колоните мора да се совпаѓаат за автоматско мапирање.
#XMSG Info: mapping already exists
mappingExists=Мапирањето веќе постои.
#XMSG: There are invalid columns in the target table
invalidColumns=Следниве колони не можат да се мапираат, па затоа ќе се отстранат.
#XFLD: Label for package select
package=Пакет

#XMSG warning for repository package assignment changed for Object name {1}
VAL_PACKAGE_CHANGED=Го доделивте овој објект на пакетот {1}. Кликнете Зачувај за да ја потврдите променава. Имајте предвид дека доделувањето на пакет не може да се поништи во овој уредувач по зачувувањето.
#XMSG warning for repository package dependency issue for object name{0}
MISSING_DEPENDENCY=Зависностите на објектот „{0}“ не може да се решат во контекст на пакетот {1}.
#XFLD: Mapped to parameter name{0}
mappedTo=Мапирано до: {0}
#XFLD: Value parameter default Value{0}
defaultVal=Вредност: {0}
#XFLD: Not Mapped
notMapped=Не е мапирано
#XMSG : Validation message for views with remote table name {1} for view name {0}
validateViewWithRemoteTables=Приказот „{0}“ содржи референции за следниве табели на далечина (директно или индиректно): „{1}"
#XMSG : Validation message for views with DAC for view name {0}
validationDAC=Приказот „{0}“ не може да се додаде. Контролата за пристап до податоци е применета врз него (или врз зависниот приказ).
#XMSG : Validation message for objects with remote tables, remote table name{0} in sql editor,
validateSqlRemoteTables=SQL-наредбата содржи еден или повеќе објекти коишто содржат референции за следниве табели на далечина (директно или индиректно): „{0}"
#XBTN : Set Value
setValue=Поставете вредност
#XBTN : Map TO
mapTo=Мапирај во
#XMSG : This source name{0} contains input parameters.
parameterMappingInfo=Овој извор {0} содржи влезни параметри. За секој параметар може да поставите вредност или да го мапирате на влезен параметар во вашиот приказ.
#XBTN : Cancel
cancel=Откажи
#XTIT : Set Value for Input Parameter name {0}
setValueForParam=Поставете вредност за влезниот параметар {0}
#XMSG : Value
enterValue=Вредност
#XMSG : Enter Valid Decimal value with valid precision and scale
VAL_ENTER_VALID_DECIMAL_GEN=Внесете важечка децимална вредност со прецизност {0} и скала {1}.
#XFLD : Runtime
runtime=Време на извршување
#XFLD: Storage
storage=Склад
#XMSG: Info message for skipping SQL validation when tenant upgrade is in progress
VAL_TENANT_UPGRADE_IN_PROGRESS=Потврдувањето на трансформацијата на SQL-приказот е прескокната бидејќи надградбата на закупецот беше во тек. Потврдете ја трансформацијата на SQL-приказот пред да го започнете текот на трансформација.
#XMSG: Error LTF cannot be used as a source :params: {0} technical name of the object
validationLTFSource=Не може да се користи локалната табела (датотека) „{0}“ како извор во трансформацискиот тек (spark).
#XMSG: Error shared tables cannot be used as a source :params: {0} technical name of the object
validationSharedSource=Не може да се користи споделената табела (датотека) „{0}“ како извор во трансформацискиот тек (spark).
#XMSG: Error Only LTF are supported in large system space
validationNonLTFSource=Не може да се користи изворот „{0}“ во трансформацискиот тек (spark) бидејќи не е локална табела (датотека).
#~~~~~~~~~~~~~~~~~~~~~~~ Python Operator texts starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XTOL: Tooltip for python operator in context pad
pythonOperator=Оператор во Python

#~~~~~~~~~~~~~~~~~~~~~~~ Secondary sources properties starts here~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD : Show Sources
showSources=Покажи ги изворите:
#XFLD: Qualified Name
qualifiedName=Квалификуван назив
#XFLD: Space Name
spaceName=Назив на просторот
#XFLD: Context Name
contextName=Назив на контекстот
#XFLD: Connection
connection=Врска
#XFLD: Remote Table
remoteTable=Табела на далечина
#XFLD: Active Records
activeRecords=Активни записи
#XFLD: Local Table
localTable=Локална табела
#XFLD: Local Schema
localSchema=Локална шема
#XMSG: no parameters message
noParameters=Нема достапни параметри
#XMSG: Validation Error in secondary source error message
secondarySourceErrorDialog=Изворите за трансформација на приказот не можат да се прикажат бидејќи постои најмалку една грешка во трансформацијата на SQL-приказот.
#XMSG: Unable to load sources error message
secondarySourceLoadingErrorDialog=Изворите за трансформација на приказот не можат да се вчитаат.
#XMSG: No sources error message
noSecondarySourceErrorDialog=Трансформацијата на приказот нема ниту еден извор.
#XTIT Local Table (File)
LTFTable=Локална табела (датотека)
#XMSG: Error data preview is not available for undeployed SQLScript Table function
dataPreviewNotDeployWarning=Податоците за трансформацијата на приказот не се видливи тука додека не се применат сите промени. Применете го трансформацискиот тек и обидете се повторно.
#Input parameters
#XFLD : Input Parameters
parametersTitle=Влезни параметри ({0})
#XMSG: Error message for empty input parameters
noInputParameters=Во моментов не се достапни параметри. За да додадете, притиснете го копчето Уреди.
#XTOL: Tooltip for edit input parameters
parametersTooltip=Уреди ги влезните параметри
#XMSG: edit input parameters
editInputParameters=Уреди ги влезните параметри
#XMSG: Create input parameters in the object to be used in inner filters
parametersDescription=Создајте влезни параметри во објектот што треба да се користи во внатрешните филтри
#XBUT: Add new parameter
addParameters=Додај нов параметар
#XBUT: Delete parameter
deleteParameter=Избриши го параметарот
#XFLD: Data Type
parameterDatatype=Тип податоци
#XFLD: Name
inputParameterName=Назив
#XFLD: Placeholder for string input type
placeholderText=Внесете стандардна вредност
#XFLD: Default Value
defaultValue=Стандардна вредност
#XFLD defined by
definedBy=Дефинирано од
#XMSG Error message for empty input parameter name
VAL_PARAM_EMPTY_NM=Називот на параметарот е празен.
#XMSG Error message for parameter name not unique
VAL_PARAM_NAME_NOT_UNIQUE=Називот на параметарот не е единствен.
#XMSG: Error message when object is not deployed yet
cannotViewNotDeployed=Објектот „{0}“ никогаш не бил применет. Применете го пред прегледот.
#XFLD: Placeholder for string input type
stringPlaceholderText=Внесете низа
#XFLD: Placeholder for integer input type
intPlaceholderText=Внесете број
#XFLD: Placeholder for decimal input type
decPlaceholderText=Внесете децимална вредност
#NOTR: Placeholder for date format Filter
dateFormatFilter=YYYY-MM-DD
#NOTR: Placeholder for time format Filter
timeFormatFilter=HH:mm:ss
#NOTR: Placeholder for date and time format Filter
dateTimeFormatFilter=YYYY-MM-DD, HH:mm:ss
#XTXT: Text for true dropdown of boolean datatype for input parameter - the value should be in small case matching the actual data
param_true=точно
#XTXT: Text for false dropdown of boolean datatype for input parameter - the value should be in small case matching the actual data
param_false=неточно
#XTXT: Text for None dropdown of boolean datatype for input parameter
none=Ништо
#XFLD: Input Parameter Name
OperatorName=Назив
#XFLD: Input Parameter Value
Value=Вредност
#XBTN: run button of run with parameter dialog
TXT_RUN=Изврши
#XFLD
InputParameters=Влезни параметри
#XTXT: Text for dialog box for input parameter
EnterParameterValues=Внесете вредности за параметарот за го извршите текот на трансформацијата
#XMSG: Error message for invalid number input
INVALID_NUMBER=Внесете важечки број.
#XMSG: Error message for invalid decimal input
INVALID_DECIMAL=Внесете важечкa децимала.
#XMSG: Error message for invalid date input
INVALID_DATE=Внесете важечки датум (ГГГГ-ММ-ДД).
#XMSG: Error message for invalid time input
INVALID_TIME=Внесете важечко време (ЧЧ:мм:сс).
#XMSG: Error message for invalid date and time input
INVALID_DATETIME=Внесете важечки датум и време (ГГГГ-ММ-ДД, ЧЧ:мм:сс).
#XMSG: Error message for invalid Boolean input
INVALID_BOOLEAN=Внесете важечка Булова вредност.
#XMSG: Error message for invalid generic input
INVALID_INPUT=Неважечки внес.
#XTIP: Tooltip for parameter value
more=Повеќе
#XTIT
mappingDialogTitle=Мапирајте ги влезните параметри
