/**
 * Copyright 2023 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import {
  IBrowserOptions,
  IDetailsConfiguration,
  IEditorDocument,
  IEditorExtensions,
  ISecondaryEditorOptions,
} from "../abstractbuilder/api";
import { isFolderSelectorEnabled } from "../abstractbuilder/commonservices/FeatureFlagCheck";
import { smartExtendComponent } from "../basecomponent/BaseComponent";
import { DocumentProperty, DocumentStorageService } from "../commonmodel/api/DocumentStorageService";
import {
  AbstractDataBuilderEditorComponent,
  AbstractDataBuilderEditorComponentClass,
} from "../databuilder/AbstractDataBuilderEditorComponent";
import { getIsEditorSupportVersions, getIsVersioningReadOnlyMode } from "../databuilder/utility/DatabuilderHelper";
import { ObjectStatus } from "../reuse/utility/Types";
import { ShellContainer } from "../shell/utility/Container";
import { SecondaryGVEditorConfigurator } from "./SecondaryGVEditorConfigurator";
import { SecondarySQLEditorConfigurator } from "./SecondarySQLEditorConfigurator";
import { TFModelerClass } from "./controller/TFModeler.controller";
import Constants from "./js/utility/Constants";
import Utils from "./js/utils";

export class TransformationFlowEditorComponentClass extends AbstractDataBuilderEditorComponentClass {
  private configurator: ISecondaryEditorConfigurator;
  /**
   * @override
   */
  public init(): void {
    // eslint-disable-next-line prefer-rest-params
    super.init.apply(this, arguments);
    this["resourceModel"] = new sap.ui.model.resource.ResourceModel({
      bundleName: require("./i18n/i18n.properties"),
    });
  }

  public getOrCreateMetaDocument(document: IEditorDocument): any {
    let transformationflows;
    if (document?.content?.transformationflows) {
      transformationflows = document.content.transformationflows;
    } else if (document?.csn?.transformationflows) {
      transformationflows = document.csn.transformationflows;
    }
    const modelName =
      transformationflows && Object.keys(transformationflows).length === 1
        ? Object.keys(transformationflows)[0]
        : undefined;
    if (modelName) {
      // eslint-disable-next-line dot-notation
      const model = transformationflows[modelName];
      if (!model["_meta"]) {
        model["_meta"] = {};
      }
      return model["_meta"];
    }
  }

  /**
   * @inheritdoc
   * @implements
   */
  public getModelTitle(): string {
    const controller = this.controller();
    return controller.localizeText("modelNameTransformationFlow");
  }

  /**
   *
   * @override
   * @param {IEditorDocument} document
   * @return {*}  {IEditorDocument}
   * @memberof TransformationFlowEditorComponentClass
   */
  public appendPackageMetaInfoToDocument(document: IEditorDocument): IEditorDocument {
    let transformationflows;
    if (document?.content?.transformationflows) {
      transformationflows = document.content.transformationflows;
    } else if (document?.csn?.transformationflows) {
      transformationflows = document.csn.transformationflows;
    }
    const oGalileiModel = this.getGalileiModelSync();
    if (transformationflows && oGalileiModel.packageValue) {
      // only append package info for the editor that implement this
      if (oGalileiModel.packageValue !== "_NONE_KEY_PACKAGE_") {
        const modelName =
          transformationflows && Object.keys(transformationflows).length === 1
            ? Object.keys(transformationflows)[0]
            : undefined;
        if (modelName) {
          // eslint-disable-next-line dot-notation
          if (transformationflows[modelName]["_meta"]) {
            transformationflows[modelName]["_meta"]["#repositoryPackage"] = oGalileiModel.packageValue;
          } else {
            transformationflows[modelName]["_meta"] = {
              "#repositoryPackage": oGalileiModel.packageValue,
            };
          }
        }
      }
    }

    return document;
  }

  /**
   * @override
   */
  public getHelpScreenId(): string {
    return "transformationflow";
  }

  /**
   * @override
   */
  public createContent() {
    return super.createContent();
  }

  /**
   * @override
   */
  public async setContextObject(spaceId: string, modelId: string, loadingData?: any, isActive = true): Promise<void> {
    super.setContextObject(spaceId, modelId, loadingData);
    const workbench = this.getWorkbench();
    // Resets toolbar model
    if (isActive) {
      workbench.getToolbarModel().setData({});
    }
    const controller = this.controller();
    if (controller && isActive) {
      const effectiveLoadingData = loadingData; // || this.getComponentData();
      controller.setContextObject(spaceId, modelId);
      const workbench = this.getWorkbench();
      if (effectiveLoadingData && effectiveLoadingData.json && effectiveLoadingData.file) {
        // if it's an existing model, set isNew - false
        workbench.getToolbarModel().setProperty("/isNew", false);
        // Open an existing Transformation Flow model
        await controller.editDiagram(effectiveLoadingData);
      } else {
        // if it's an existing model, set isNew - true
        workbench.getToolbarModel().setProperty("/isNew", true);
        await controller.newDiagram();
      }
    }
  }

  private updateToolBarModel() {
    const workbench = this.getWorkbench();
    const oGalileiModel = this.getGalileiModelSync();
    if (oGalileiModel?.isNew === true || oGalileiModel?.isNew === undefined) {
      // set isNew - true on workbench to display SaveAs btn.
      (workbench.getToolbarModel() as sap.ui.model.json.JSONModel).setProperty("/isNew", true);
    } else {
      // set isNew - false on workbench to display only save btn.
      (workbench.getToolbarModel() as sap.ui.model.json.JSONModel).setProperty("/isNew", false);
    }
  }

  public async switchToSecondaryEditor(modelId: string, galileiObject: any, options: ITFlowSecondaryEditorOptions) {
    const workbench = this.getWorkbench();
    options.modelName = modelId;
    options.galileiObject = galileiObject;
    if (this.configurator) {
      this.configurator.destroy();
    }
    this.configurator = this.getSecondaryEditorConfigurator(galileiObject, options);
    options.configuration = await this.configurator.getConfiguration();
    const effectiveModelId = options.loadingData ? modelId : this.configurator.getNewModelType();
    this.subscribeToVTModelLoaded(workbench);
    await workbench.loadAndSwitchToSecondaryEditor(effectiveModelId, options);
    this.updateToolBarModel();
  }

  /**
   * Subscribes to new model event
   *
   * @private
   * @param {DataBuilderWorkbench} workbench
   */
  private subscribeToVTModelLoaded(workbench) {
    {
      const oFeatures = sap.ui.getCore().getModel("featureflags").getData();
      if (oFeatures.DWCO_TRF_INPUT_PARAMETERS_SUPPORT) {
        sap.ui
          .getCore()
          .getEventBus()
          .subscribeOnce(
            sap.cdw.querybuilder.ModelImpl.CSNMODEL_CHANNEL,
            (sap.cdw.querybuilder.ModelImpl as any).CSNMODEL_NEWMODEL_EVENT,
            this.updateInputParameters.bind(this, workbench)
          );
      }
    }
  }
  /**
   * Adds user defined input parameters to the view transform parameters
   *
   * @private
   */
  private updateInputParameters(workbench, channelId, eventId, loadingData): void {
    const oFeatures = sap.ui.getCore().getModel("featureflags").getData();
    if (oFeatures.DWCO_TRF_INPUT_PARAMETERS_SUPPORT) {
      const primaryEditor = workbench?.getActiveEditor(false);
      const primaryModel = primaryEditor?.getGalileiModelSync();
      const userDefinedParams = primaryModel.parameters;
      const outputParameters = loadingData?.model?.output?.parameters;
      const outputParametersArr = outputParameters?.toArray();
      // To update existing and add new parameters
      userDefinedParams?.forEach((userParam) => {
        const existing = outputParametersArr?.find((p) => p.name === userParam.name);
        if (primaryModel.isNew && userParam.container && !userParam.container.qualifiedName) {
          // Fix DW101-101558 deployment of tf with parameters fails due to calculated element ref is undefined
          // as container of parameter is set to tf Model
          userParam.container.qualifiedName = userParam.container.name;
        }
        if (existing) {
          // Update datatype and value
          existing.dataType = userParam.dataType;
          existing.defaultValue = userParam.defaultValue;
          existing.definedBy = userParam.definedBy;
        } else {
          // Add new param
          outputParameters.push({ ...userParam });
        }
      });
      // Delete parameters not in userDefinedParams
      for (let i = outputParametersArr?.length - 1; i >= 0; i--) {
        const outputParam = outputParametersArr[i];
        const existsInUserDefined = userDefinedParams?.toArray()?.some((p) => p.name === outputParam.name);

        if (!Constants.ParametersList.includes(outputParam.name) && !existsInUserDefined) {
          outputParameters?.removeAt(outputParameters.indexOf(outputParam));
          outputParam.deleteObject();
        }
      }
    }
  }

  private getSecondaryEditorConfigurator(
    node: sap.cdw.transformationflow.Node,
    options: ITFlowSecondaryEditorOptions
  ): ISecondaryEditorConfigurator {
    let configurator: ISecondaryEditorConfigurator;
    switch (node.component) {
      case Constants.OPERATORS.SQL_TRANSFORM:
      case Constants.OPERATORS.SQL_SCRIPT_TRANSFORM:
        configurator = new SecondarySQLEditorConfigurator(this, options);
        break;
      default:
        configurator = new SecondaryGVEditorConfigurator(this, options);
    }
    return configurator;
  }

  /**
   * @inheritdoc
   * @override
   */
  public checkAlternativePropertiesSidePanelView(object: any, view: string) {
    // by default no alternative view
    if (this.getWorkbench()?.isSecondaryEditorActive) {
      if (["sap.cdw.querybuilder.Model", "sap.cdw.querybuilder.Output"].includes(object?.qualifiedClassName)) {
        const model = object?.qualifiedClassName === "sap.cdw.querybuilder.Output" ? object.container : object;
        // eslint-disable-next-line no-underscore-dangle
        if (model?._isSqlEditorModel) {
          return require("./properties/secondary/SQLOutputProperties.view.xml");
        } else {
          return require("./properties/secondary/GVOutputProperties.view.xml");
        }
      }
    }
    return super.checkAlternativePropertiesSidePanelView(object, view);
  }

  /**
   *
   * @override
   * @returns {IBrowserOptions}
   * @memberof TransformationFlowEditorComponentClass
   */
  public getBrowserOptions(): IBrowserOptions {
    return {
      hideSharedObjects: true,
      remoteSourceSupported: false,
      supportIntelligentLookup: false,
      supportDataFlows: false,
      supportTaskChain: false,
      hideViews: true,
      hideRemoteTables: true,
    };
  }

  /**
   * @override
   */
  public getDetailsConfiguration(): IDetailsConfiguration {
    const workbench = this.getWorkbench();
    return {
      getViewName: () => {
        if (workbench.isSecondaryEditorActive) {
          return require("../csnquerybuilder/view/mdmPreviewViewEditor.view.xml");
        }
        return require("./view/mdmPreviewTFEditor.view.xml");
      },
      useDefault: {
        useDefaultCallback: false,
      },
    };
  }

  /**
   * @override
   */
  public onUndo() {
    const controller = this.controller();
    controller.onUndo();
  }

  /**
   * @override
   */
  public onRedo() {
    const controller = this.controller();
    controller.onRedo();
  }

  /**
   * @override
   */
  public notifySaveFinished(isSaveSuccessful: boolean) {
    if (isSaveSuccessful) {
      const model = this.controller().getGalileiModel() as sap.cdw.transformationflow.Model;
      if (model) {
        if (model.hasOwnProperty("changeManagement")) {
          // eslint-disable-next-line dot-notation
          delete model["changeManagement"];
          model.validate();
        }
        const nodes = model.nodes.toArray();
        nodes.forEach((oNode) => {
          if (oNode.component === Constants.OPERATORS.TARGET && oNode.isNew) {
            this.controller()
              .getDiagramBuilder()
              .getResource()
              .applyUndoableAction(
                () => {
                  oNode.isNew = false;
                  if (oNode.deltaTableTechnicalName) {
                    // update technical name of new delta target
                    // this will be used when user creates a new delta target from an existing transformation flow
                    oNode.technicalName = oNode.deltaTableTechnicalName;
                    oNode.deltaTableName = undefined;
                  }
                  model.validate();
                },
                "save successful and target table created",
                true
              );
          }
        });
      }
    }
  }

  /**
   * @override
   */
  public async getGalileiModel(): Promise<any> {
    const galileiModel = Promise.resolve(this.getGalileiModelSync());
    const oModel = await galileiModel;
    if (oModel && oModel.nodes && oModel.nodes.length) {
      for (let nIndex = 0; nIndex < oModel.nodes.length; nIndex++) {
        const oNode = oModel.nodes.get(nIndex);
        if (oNode.component === Constants.OPERATORS.TARGET && oNode.isNew) {
          oNode.uniqueTechnicalName = await Utils.checkUniqueness(oNode.technicalName);
          if (oNode.isDeltaTable) {
            oNode.uniqueDeltaCaptureTableName = await Utils.checkUniqueness(
              Utils.generateDeltaCaptureTableName(oNode.technicalName)
            );
          }
        }
      }
    }
    return galileiModel;
  }

  /**
   * @override
   */
  public getGalileiModelSync(): any {
    const controller = this.controller();
    return controller.getModel();
  }

  /**
   * @override
   */
  isDeploySupported(): boolean {
    return true;
  }

  /**
   * @override
   * @param currentMessage message to show on UI on Deploy
   */
  public getDeployStartedMessage(currentMessage: string): string {
    return currentMessage;
  }

  /**
   * @inheritdoc
   * @implements
   */
  public isObjectNameDisplaySwitchEnabled(): boolean {
    return false;
  }

  /**
   * @override
   */
  public isRunDataflowSupported(): boolean {
    const isSDPEnabled = sap.ui.getCore().getModel("featureflags").getProperty("/DWC_DUMMY_SPACE_PERMISSIONS");
    let privileges;
    if (isSDPEnabled) {
      privileges = sap.ui.getCore().getModel("privilege").getProperty("/DWC_DATAINTEGRATION");
    } else {
      privileges = ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_DATAINTEGRATION");
    }
    if (privileges && privileges.update) {
      return true;
    } else {
      return false;
    }
  }

  public isGeneralSectionVisible(isVisible: boolean): boolean {
    if (!isVisible) {
      const isSDPEnabled = sap.ui.getCore().getModel("featureflags").getProperty("/DWC_DUMMY_SPACE_PERMISSIONS");
      let integrationPriv, builderPriv;
      if (isSDPEnabled) {
        integrationPriv = sap.ui.getCore().getModel("privilege").getProperty("/DWC_DATAINTEGRATION");
        builderPriv = sap.ui.getCore().getModel("privilege").getProperty("/DWC_DATABUILDER");
      } else {
        integrationPriv = ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_DATAINTEGRATION");
        builderPriv = ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_DATABUILDER");
      }
      // handle corner case where user had integration update and builder read privilege
      if (integrationPriv && builderPriv && builderPriv.read && integrationPriv.update) {
        return true;
      }
      return isVisible;
    }
    return isVisible;
  }

  /**
   * @override
   */
  public isEditExportVisible(): boolean {
    return true;
  }
  /**
   * @override
   */
  public async onExportCSN(spaceId: string, clearCache: boolean): Promise<any> {
    const workbench = this.getWorkbench();
    const message = workbench.getText("exportingCompleted");
    return await this.exportDocumentAsCSN.call(
      this,
      spaceId,
      undefined,
      await this.getGalileiModel(),
      clearCache,
      message
    );
  }

  /**
   * @override
   * triggers the run of transformation flow after validation
   */
  public runDataflow(/* event*/): void {
    const oController = this.controller();
    oController.onRunTransformationFlow();
  }

  /**
   * @override
   */
  public async getDocument(/* currentEntityName: any, galileiModel?: any , options?: any*/): Promise<IEditorDocument> {
    const documentAdapter = this.controller().getEditorControl().getModelHandler().getDocumentAdapter();
    return documentAdapter.createDocument();
  }

  /**
   * @override
   */
  public onToggleDetails(modelName?, galileiModel?): void {
    const workbench = this.getWorkbench();
    if (workbench.isPreviewdataActive()) {
      if (!modelName) {
        this.getSelectedGalileiObject().then((object) => {
          this.controller().updateDataPreview([object]);
        });
      } else {
        workbench.previewData(modelName, galileiModel);
      }
    }
  }

  public controller(): TFModelerClass {
    return super.controller() as TFModelerClass;
  }

  public async getSelectedGalileiObject() {
    const controller = this.controller && this.controller();
    const selectedSymbol = controller.getFirstSelectedSymbol();
    if (selectedSymbol) {
      return selectedSymbol.object;
    }
    return undefined;
  }

  /**
   * @override
   *
   * Overrides data flow modeler editor component class
   * @param galileiModel
   * @returns model
   */
  public async validateModel(galileiModel): Promise<any> {
    await super.validateModel(galileiModel);
  }

  /**
   * @override
   */
  public isDirty(): boolean {
    const isDirty = super.isDirty();
    return isDirty;
  }

  /**
   * @override
   */
  public onModelDateUpdated() {
    sap.ui.getCore().getEventBus().publish("propertyPanel", "updateTransformationFlowDeploymentStatus");
    this.updateTargetObjectStatus();
    this.refreshWorkbenchEnvModel();
  }

  /**
   *
   * @override
   * @return {*}  {boolean}
   * @memberof TransformationFlowEditorComponentClass
   */
  public disableSave(): boolean {
    return !!this.getWorkbench()?.isSecondaryEditorActive;
  }

  /**
   *
   * @override
   * @return {*}  {boolean}
   * @memberof TransformationFlowEditorComponentClass
   */
  public disableDeploy(): boolean {
    return !!this.getWorkbench()?.isSecondaryEditorActive;
  }

  /**
   *
   * @override
   * @return {*}  {boolean}
   * @memberof TransformationFlowEditorComponentClass
   */
  public disableRunDataflow(): boolean {
    const oModel: any = this.controller().getGalileiModel();
    let isDeployed = false;
    let isVersionReadOnlyModeEnabled = false;
    if (getIsVersioningReadOnlyMode() && getIsEditorSupportVersions()) {
      isVersionReadOnlyModeEnabled = true;
    }
    if (oModel) {
      if (!oModel?.isNew) {
        isDeployed = this.checkIfDeployed(oModel["#objectStatus"]);
      }
    }
    return !!this.getWorkbench()?.isSecondaryEditorActive || !isDeployed || isVersionReadOnlyModeEnabled;
  }

  /**
   * checks if the given object status comes under a deployed flow
   * @param objectStatus
   * @returns {boolean}
   */
  public checkIfDeployed(objectStatus: string) {
    const deployedStatuses = ["1", "2"];
    if (deployedStatuses.includes(objectStatus)) {
      return true;
    }
    return false;
  }

  /**
   * updates the deployment status of target table
   */
  public updateTargetObjectStatus() {
    const oModel: any = this.controller().getGalileiModel();
    const nodes: any[] = oModel.nodes.toArray();
    const targetNode = nodes.filter((node) => node.isTargetTable)[0];
    const objectStatus = parseInt(oModel["#objectStatus"], 10);
    if (objectStatus === ObjectStatus.deployed) {
      targetNode["#objectStatus"] = objectStatus.toString();
    }
  }

  /**
   *
   *
   * @memberof TransformationFlowEditorComponentClass
   */
  public refreshWorkbenchEnvModel() {
    this.updateToolBarModel();
    // update save, deploy and run button disabled state
    const workbenchEnvModel = this.getWorkbench()?.getView()?.getModel("workbenchEnv") as sap.ui.model.json.JSONModel;
    workbenchEnvModel?.refresh(true);
  }

  /**
   * set package value and status
   * @param {string} packageValue
   * @param {string} packageStatus
   */
  public async setPackageInfo(packageValue: string, packageStatus: string) {
    // do not set again for secondary editor
    if (this.getWorkbench().isSecondaryEditorActive) {
      return;
    }
    const model = await this.getGalileiModel();
    // use protectedFromUndo in applyUndoableAction to avoid model change to dirty status
    model?.resource?.applyUndoableAction(
      function () {
        model.packageValue = packageValue;
        model.packageStatus = packageStatus;
      },
      "update packageValue and packageStatus",
      /* protectedFromUndo */ true
    );
    const targetTableList: string[] = [];
    const targetNodesList: sap.cdw.transformationflow.Node[] = [];
    model?.nodes?.forEach((node) => {
      if (!node.isTemplate && node.isTargetTable && Utils.isDWCObject(node)) {
        targetTableList.push(node.technicalName);
        targetNodesList.push(node);
      }
    });
    if (targetTableList.length > 0) {
      // get packages for the target table nodes
      const nodesNamePackageValueMap = await this.getNodesNamePackages(targetTableList);
      //set the package info at node level
      this.setNodePackageInfo(nodesNamePackageValueMap, targetNodesList);
    }
    // after set package information complete, we should validate model again and refresh decorator
    model.validate();
  }

  /**
   * set package info for individual nodes
   * @param {sap.cdw.transformationflow.Model} model
   * @param {Map<string,string>} nodesNamePackageValueMap
   */
  public setNodePackageInfo(
    nodesNamePackageValueMap: Map<string, string>,
    targetNodesList: sap.cdw.transformationflow.Node[]
  ) {
    for (let i = 0; i < targetNodesList?.length; i++) {
      const node = targetNodesList[i];
      if (nodesNamePackageValueMap[node.technicalName]) {
        this.updateNodePackageName(node, nodesNamePackageValueMap[node.technicalName]);
      }
    }
  }
  /**
   * set package at model
   * @param {sap.cdw.transformationflow.Node} node
   * @param {string} packageName
   */
  public updateNodePackageName(node: sap.cdw.transformationflow.Node, packageName: string) {
    node?.resource.applyUndoableAction(
      function () {
        node.packageValue = packageName;
      },
      "update packageValue",
      /* protectedFromUndo */ true
    );
  }
  /**
   * get package values for node objects in the transformation flow
   * @param {sap.cdw.transformationflow.Model} model
   * @return {Promise<Map<string, string>>}
   */
  public async getNodesNamePackages(targetTableList: string[]): Promise<Map<string, string>> {
    const nodesNamePackageValueMap = new Map<string, string>();

    // get repoPackage value for all the nodes (tables) in the model
    const nodesRepoPackages = await DocumentStorageService.getInstance().getOrLoadDocumentsList(
      {
        detailsProperties: [DocumentProperty.repoPackage],
        filters: { name: targetTableList },
      },
      true
    );
    nodesRepoPackages?.forEach((item) => {
      // value of #repositoryPackage for not assigned table is null, filter out these table
      if (item["#repositoryPackage"]) {
        nodesNamePackageValueMap[item.name] = item["#repositoryPackage"];
      }
    });

    return nodesNamePackageValueMap;
  }
  /** appends meta info for target table
   * @override
   * @param {IEditorDocument} document
   **/
  public onPostAppendMetaInfo(document: IEditorDocument) {
    if (isFolderSelectorEnabled()) {
      const targetDefinition = document.content.definitions;
      const folderAssignment = this.getWorkbench().getFolderAssignment();
      if (targetDefinition && folderAssignment) {
        for (const target in targetDefinition) {
          targetDefinition[target]["_meta"] = {
            dependencies: {
              folderAssignment,
            },
          };
        }
      }
    }
  }
}

export const TransformationFlowEditorComponent = smartExtendComponent(
  AbstractDataBuilderEditorComponent,
  "sap.cdw.components.transformationflow.Component",
  TransformationFlowEditorComponentClass as any
);

sap.ui.define("sap/cdw/components/transformationflow/Component", [], function () {
  require("../commonui/utility/GalileiLoader");

  require("./js/model/validation");
  require("./js/model/model");
  require("./js/model/ModelToJSON");
  require("./js/diagram/diagram");
  require("./js/diagram/diagramBuilder");
  require("./js/diagram/editorExtension");
  require("./js/library");

  require("../csnquerybuilder/js/diagram/diagram");

  return TransformationFlowEditorComponent;
});

export interface ITFlowSecondaryEditorOptions extends ISecondaryEditorOptions {
  galileiObject: sap.cdw.transformationflow.Node;
}
export interface ISecondaryEditorConfigurator {
  getConfiguration: () => Promise<IEditorExtensions>;
  destroy: () => void;
  getNewModelType: () => string;
}
