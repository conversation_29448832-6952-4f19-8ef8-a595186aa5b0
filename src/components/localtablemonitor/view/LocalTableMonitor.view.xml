<mvc:View
  controllerName="sap.cdw.components.localtablemonitor.controller.LocalTableMonitor"
	xmlns="sap.m"
	xmlns:f="sap.f"
	xmlns:t="sap.ui.table"
	xmlns:mvc="sap.ui.core.mvc"
	xmlns:core="sap.ui.core"
	xmlns:unified="sap.ui.unified"
  height="100%"
  xmlns:fb="sap.ui.comp.filterbar"
	xmlns:ac="sap.cdw.components.reuse.control.actionchecker"
	xmlns:cd="http://schemas.sap.com/sapui5/extension/sap.ui.core.CustomData/1"
>
	<ac:ActionChecker
    id="localTableMonitorAc"
    class="sapUiContentPadding"
    hanaState="{circuitbreaker>/DataHANA}"
    hanaProvisioningState="{circuitbreaker>/DataHANAProvisioningState}"
    spaceLocked="{= ${diPageModel>/isSpaceLocked} === true }"
    actionControlIds="refeshTableButton"
    hiddenMode="true"
  ></ac:ActionChecker>
	<f:DynamicPage
    id="localTableMonitoringPage"
    preserveHeaderStateOnScroll="true"
    headerExpanded="true"
  >
		<f:content>
			<t:Table
        id="localTableMonitorTable"
        class="sapFDynamicPageAlignContent noColumnBorder"
        rows="{path:'/localTables', templateShareable:false}"
        selectionMode="Single"
        selectionBehavior="RowOnly"
        rowSelectionChange="onRowSelectionChange"
        visibleRowCountMode="Auto"
        enableBusyIndicator="true"
        busyIndicatorDelay="0"
        busyIndicatorSize="Small"
        noDataText="{i18n>noDataText}"
        noDataTextDirection="LTR"
        noDataTextInNoData="true"
        rowActionCount="{parts:[
          {path: 'ltfSpace>/isHdlfStorage' },
          {path: 'featureflags>/DWCO_LARGE_SYSTEMS_APPS_API'},
          {path: 'featureflags>/DWCO_LARGE_SYSTEMS_SPARK_SELECTION'}
          ],
          formatter: '.onLTFSpaceRowActionCount'
          }"
        filter=".fireOnFilterColumn"
      >
				<t:extension>
          <VBox
           id="localTableMonitorAuthSetting"
          class="sapUiSmallMarginBottom"
          >
          <mvc:XMLView
          id="localTableMonitorAuth"
          viewName="sap.cdw.components.taskscheduler.view.TaskScheduleAuth"
          visible="{= ${privilege>/DWC_DATAINTEGRATION/execute} === true }"
          cd:actionId="monitoring/editor/authFlow"
          />
          <MessageStrip
            text="{i18n>txtRepoAccessError}"
            showIcon="true"
            showCloseButton="true"
            type="Warning"
            visible="{= ${/repoAccessError} === true }"
            class="sapUiTinyMarginBottom"
          />
          </VBox>
					<OverflowToolbar height="40px">
						<Title
              id="localtableTitle"
              text="{/headerTitle}"
              class="sapUiTinyMarginBottom"
            />
						<ToolbarSpacer />
						<SearchField
              id="searchTablesInput"
              placeholder="{i18n>txtSearch}"
              liveChange="onSearchField"
              width="15rem"
            />
            <core:Fragment
                fragmentName="sap.cdw.components.localtablemonitor.view.fragment.ltfActionButtons"
                type="XML"
              />
            <OverflowToolbarButton
              id="refeshTableButton"
              type="Transparent"
              icon="sap-icon://refresh"
              tooltip="{i18n>txtRefresh}"
              press="onRefreshTable"
              cd:actionId="monitoring/editor/refresh"
            />
            <OverflowToolbarButton
              id="personalizeTableButton"
              tooltip="{i18n>selectColumnsBtn}"
              text="{i18n>text_selectColumns}"
              type="Transparent"
              icon="sap-icon://action-settings"
              press="onPersonalizeButtonPress"
              cd:actionId="monitoring/editor/refresh"
            />
					</OverflowToolbar>
				</t:extension>
				<t:columns>
				</t:columns>
				<t:rowActionTemplate>
					<t:RowAction id="NavigationLink">
						<t:RowActionItem
              press="loadLocalTableLogs"
              type="Navigation"
              icon="sap-icon://feeder-arrow"
              visible="{/isFileStorage}"
            />
					</t:RowAction>
				</t:rowActionTemplate>
			</t:Table>
		</f:content>
	</f:DynamicPage>
</mvc:View>
