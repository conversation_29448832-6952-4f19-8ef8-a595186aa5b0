<mvc:View
  xmlns:core="sap.ui.core"
  xmlns="sap.m"
  xmlns:ux="sap.uxap"
  xmlns:l="sap.ui.layout"
  xmlns:form="sap.ui.layout.form"
  xmlns:mvc="sap.ui.core.mvc"
  xmlns:ac="sap.cdw.components.reuse.control.actionchecker"
  xmlns:cd="http://schemas.sap.com/sapui5/extension/sap.ui.core.CustomData/1"
  controllerName="sap.cdw.components.localtablemonitor.controller.LocalTableObjectDetails"
  busyIndicatorDelay="0"
>

  <ac:ActionChecker
    id="localTableObjectDetailsActionChecker"
    spaceLocked="{diPageModel>/isSpaceLocked}"
    hanaState="{circuitbreaker>/DataHANA}"
    hanaProvisioningState="{circuitbreaker>/DataHANAProvisioningState}"
    actionControlIds="navToDataBuilderBtnId,mdm-editDataToggle,refreshMetricsBtnId"
  ></ac:ActionChecker>
    <MessageStrip
      text="{i18n>txtRepoAccessError}"
      visible="{= ${localTableRouteObj>/repoAccessError} === true }"
      showIcon="true"
      showCloseButton="true"
      type="Warning"
      class="sapUiTinyMargin"
    />
    <ux:ObjectPageLayout
      id="localTableDataManagement"
      alwaysShowContentHeader="true"
      showTitleInHeaderContent="true"
      upperCaseAnchorBar="false"
      useIconTabBar="{= ${ltfSpace>/isHdlfStorage} === true }"
    >
      <ux:headerTitle>
        <ux:ObjectPageDynamicHeaderTitle>
          <ux:expandedHeading>
            <Title
              id="localTableTitileId"
              text="{tableMetrics>/businessName}"
              class="sapUiTinyMarginBottom"
            />
          </ux:expandedHeading>
          <ux:expandedContent>
            <Title
              id="localTableObjId"
              text="{localTableRouteObj>/objectId}"
              class="sapUiSmallMarginBottom"
            />
          </ux:expandedContent>
          <ux:actions>
            <Button
              id="dataDeleteCommonBtnId"
              text="{i18n>deleteRecords}"
              type="Transparent"
              visible="{= ${featureflags>/DWCO_LTA_DATA_DELETION} === true}"
              cd:actionId="monitoring/editor/deleteData"
            />
            <core:Fragment
                fragmentName="sap.cdw.components.localtablemonitor.view.fragment.ltfActionButtons"
                type="XML"
              />
            <Button
              id="mdm-editDataToggle"
              type="Transparent"
              text="{i18n>openDataEditor}"
              press="onNavToDataBuilder"
              visible="{
                parts: [
                {path: 'ltfSpace>/isHdlfStorage'},
                {path: 'privilegeModel>/hasDatabuilderReadPrivilege'},
                {path: 'featureflags>/DWC_DUMMY_SPACE_PERMISSIONS'},
                {path: 'privilege>/DWC_CONSUMPTION'},
                {path: 'privilege>/DWC_DATAINTEGRATION'}],
              formatter: '.openInEditorCheck'
            }"
            enabled="{= ${localTableRouteObj>/repoAccessError} !== true }"
            cd:actionId="monitoring/editor/editData"
          />
          <OverflowToolbarButton
            id="navToDataBuilderBtnId"
            icon="sap-icon://sac/table-builder"
            tooltip="{i18n>openInEditorNew}"
            text="{i18n>openInEditorNew}"
            type="Transparent"
            press="onNavToDataBuilder"
            visible="{
                parts: [
                  {path: 'ltfSpace>/isHdlfStorage'},
                  {path: 'privilegeModel>/hasDatabuilderReadPrivilege'},
                  {path: 'featureflags>/DWC_DUMMY_SPACE_PERMISSIONS'},
                  {path: 'privilege>/DWC_DATABUILDER/read'}],
                formatter: '.tableEditorFormatter'
              }"
              enabled="{= ${localTableRouteObj>/repoAccessError} !== true }"
              cd:actionId="monitoring/editor/editData"
            />
            <OverflowToolbarButton
            id="refreshMetricsBtnId"
            icon="sap-icon://refresh"
            type="Transparent"
            press="refreshTableMetricsAndLogs"
            cd:actionId="monitoring/editor/refresh"
            />
          </ux:actions>
        </ux:ObjectPageDynamicHeaderTitle>
      </ux:headerTitle>
      <ux:headerContent>
        <FlexBox
          wrap="Wrap"
          justifyContent="SpaceBetween"
          visible="{parts:[
            {path: 'ltfSpace>/isHdlfStorage' }
            ],
            formatter:'.nonHdlfTabsFormatter'
          }"
        >
          <l:VerticalLayout>
            <ObjectStatus title="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} ? ${i18n>txtSizeOnDisk} : ${i18n>txtUsedDisk}}" />
            <Text
              id="txtUsedDiskId"
              emptyIndicatorMode="On"
              text="{=${tableMetrics>/diskSizeMiB}.toFixed(2) }"
            ></Text>
          </l:VerticalLayout>

        <l:VerticalLayout>
          <ObjectStatus title="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} ? ${i18n>txtSizeInMemory} : ${i18n>txtUsedInMemory}}" />
          <Text
            id="txtUsedInMemoryId"
            emptyIndicatorMode="On"
            text="{tableMetrics>/inMemorySizeMiB}"
          ></Text>
        </l:VerticalLayout>

        <l:VerticalLayout>
          <ObjectStatus title="{i18n>txtNumberOfRecords}" />
          <Text
            id="txtNumberOfRecordsId"
            emptyIndicatorMode="On"
            text="{tableMetrics>/recordCount}"
          ></Text>
        </l:VerticalLayout>

        <l:VerticalLayout>
          <ObjectStatus title="{i18n>txtDeltaCapture}" />
          <Text
            id="txtDeltaCaptureId"
            text="{= ${tableMetrics>/isDeltaEnabled} ? ${i18n>txtOn} : ${i18n>txtOff}}"
          ></Text>
        </l:VerticalLayout>

        <l:VerticalLayout>
          <ObjectStatus title="{i18n>txtInMemoryStorage}" />
          <Text
            id="txtInMemoryStorageId"
            text="{= ${tableMetrics>/inMemorySizeMiB} > 0 ?  ${i18n>txtOn} : ${i18n>txtOff}}"
          ></Text>
        </l:VerticalLayout>
      </FlexBox>
    </ux:headerContent>
    <ux:sections>
      <ux:ObjectPageSection
        id="hdlfLogsObjectPageId"
        titleUppercase="false"
        showTitle="true"
        visible="{parts:[
            {path: 'ltfSpace>/isHdlfStorage' },
            {path: 'featureflags>/DWCO_LTA_DATA_DELETION'}
            ],
            formatter:'.logsTabVisibilityFormatter'
          }"
      >
        <ux:subSections>
          <ux:ObjectPageSubSection
            title="{parts: [
              {path: 'ltfSpace>/isHdlfStorage'},
              {path: 'featureflags>/DWCO_LTA_DATA_DELETION'},
              {path: 'localTableRouteObj>/logLength'}
              ], formatter: '.logsTabTitleFormatter'}"
            class="sapUxAPObjectPageSubSection ui-helper-clearfix"
          >
            <core:Fragment
              fragmentName="sap.cdw.components.localtablemonitor.view.fragment.DataDeleteLogs"
              type="XML"
            />
          </ux:ObjectPageSubSection>
        </ux:subSections>
      </ux:ObjectPageSection>
      <ux:ObjectPageSection
        id="scheduleObjectPageId"
        titleUppercase="false"
        visible="{= ${featureflags>/DWCO_LTA_DATA_DELETION} === true }"
        title="{parts: [
          {path: 'ltfSpace>/isHdlfStorage'},
          {path: 'featureflags>/DWCO_LTA_DATA_DELETION'},
          {path: 'localTableRouteObj>/scheduleLogsLength'}
          ], formatter: '.scheduleTabTitleFormatter'}"
      >
        <ux:subSections>
          <ux:ObjectPageSubSection
            id="scheduleSubObjectPageId"
            titleUppercase="false"
          >
            <mvc:XMLView
              id="scheduleView"
              viewName="sap.cdw.components.localtablemonitor.view.ScheduleDelete"
            />
          </ux:ObjectPageSubSection>
        </ux:subSections>
      </ux:ObjectPageSection>
      <ux:ObjectPageSection
        id="hdlfSettingsObjectPageId"
        titleUppercase="false"
        showTitle="true"
        title="{= ${featureflags>/DWCO_LOCAL_TABLE_FILES_ZORDER} ? ${i18n>hdlfSettingsObjectPageTxt}: '' }"
        visible="{parts:[
                      {path: 'ltfSpace>/isHdlfStorage' },
                      {path: 'featureflags>/DWCO_LARGE_SYSTEMS_APPS_API'},
                      {path: 'featureflags>/DWCO_LARGE_SYSTEMS_SPARK_SELECTION'}
                      ],
                      formatter:'.fileStorageActionsFormatter'
                    }"
      >
        <ux:subSections>
          <ux:ObjectPageSubSection
            title="{= ${featureflags>/DWCO_LOCAL_TABLE_FILES_ZORDER} ? ${i18n>txtHeaderApacheSettings} : ${i18n>hdlfSettingsObjectPageTxt}}"
            class="sapUxAPObjectPageSubSection ui-helper-clearfix"
          >
            <!-- Settings Section starts-->
            <VBox>
              <VBox>
                <OverflowToolbar>
                  <Title
                    titleStyle="H4"
                    visible="{= ${featureflags>/DWCO_LOCAL_TABLE_FILES_ZORDER} === false}"
                    text="{i18n>txtHeaderApacheSettings}"
                    class="sapUiTinyMarginBottom sapUiNoMarginBegin"
                  />
                </OverflowToolbar>
                <!-- Merge Section -->
                <Title
                  text="{i18n>txtMergeSectionTitle}"
                  class="sapUiTinyMarginTop sapUiTinyMarginBegin"
                />
                <RadioButtonGroup
                  id="mergeDefaultRadioGroup"
                  columns="1"
                >
                  <RadioButton
                    id="mergeDefaultId"
                    text="{i18n>txtUseSpaceDefault}"
                    select=".onSettingsTabOptionSelect"
                    selected="true"
                  />
                </RadioButtonGroup>
                <form:Form editable="false">
                  <form:layout>
                    <form:ResponsiveGridLayout
                      labelSpanXL="4"
                      labelSpanL="3"
                      labelSpanM="4"
                      labelSpanS="12"
                      adjustLabelSpan="false"
                      emptySpanXL="0"
                      emptySpanL="4"
                      emptySpanM="0"
                      emptySpanS="0"
                      columnsXL="2"
                      columnsL="1"
                      columnsM="1"
                      singleContainerFullSize="false"
                    />
                  </form:layout>
                  <form:formContainers>
                    <form:FormContainer>
                      <form:formElements>
                        <form:FormElement label="{i18n>txtApplication}">
                          <form:fields>
                            <Input
                              id="mergeDefaultInput"
                              value="{ltfAppModel>/mergeDefault}"
                              enabled="true"
                              editable="false"
                            />
                          </form:fields>
                        </form:FormElement>
                      </form:formElements>
                    </form:FormContainer>
                  </form:formContainers>
                </form:Form>
                <RadioButtonGroup
                  id="mergeNewSettingsRadioGroup"
                  columns="1"
                >
                  <RadioButton
                    id="mergeNewSettingsId"
                    text="{i18n>txtNewSettings}"
                    select=".onSettingsTabOptionSelect"
                    selected="false"
                  />
                </RadioButtonGroup>
                <form:Form>
                  <form:layout>
                    <form:ResponsiveGridLayout
                      labelSpanXL="4"
                      labelSpanL="3"
                      labelSpanM="4"
                      labelSpanS="12"
                      adjustLabelSpan="false"
                      emptySpanXL="0"
                      emptySpanL="4"
                      emptySpanM="0"
                      emptySpanS="0"
                      columnsXL="2"
                      columnsL="1"
                      columnsM="1"
                      singleContainerFullSize="false"
                    />
                  </form:layout>
                  <form:formContainers>
                    <form:FormContainer>
                      <form:formElements>
                        <form:FormElement label="{i18n>txtApplication}">
                          <form:fields>
                            <Select
                              id="mergeNewSelect"
                              selectedKey="{ltfAppModel>/mergeAppSelected}"
                              items="{path:'ltfAppModel>/appData', templateShareable: false }"
                              change=".onMergeSelectChange"
                              enabled="false"
                              showSecondaryValues="true"
                            >
                              <core:ListItem
                                key="{ltfAppModel>index}"
                                text="{ltfAppModel>index}"
                                additionalText="{parts:[
                            {path: 'ltfAppModel>maxCore' },
                            {path: 'ltfAppModel>maxMemory'}
                            ],
                            formatter:'.appDetailsListFormatter'
                          }"
                              >
                              </core:ListItem>
                            </Select>
                            </form:fields>
                        </form:FormElement>
                      </form:formElements>
                    </form:FormContainer>
                    </form:formContainers>
                </form:Form>
              </VBox>
              <VBox>
                <!-- Optimize Section -->
                <Title text="{i18n>txtOptimizeSectionTitle}"
                  class="sapUiTinyMarginTop sapUiTinyMarginBegin" />
                <RadioButtonGroup
                  id="optimizeDefaultRadioGroup"
                  columns="1"
                >
                  <RadioButton
                    id="optimizeDefaultId"
                    text="{i18n>txtUseSpaceDefault}"
                    select=".onSettingsTabOptionSelect"
                    selected="true"
                  />
                </RadioButtonGroup>
                <form:Form editable="false">
                  <form:layout>
                    <form:ResponsiveGridLayout
                      labelSpanXL="4"
                      labelSpanL="3"
                      labelSpanM="4"
                      labelSpanS="12"
                      adjustLabelSpan="false"
                      emptySpanXL="0"
                      emptySpanL="4"
                      emptySpanM="0"
                      emptySpanS="0"
                      columnsXL="2"
                      columnsL="1"
                      columnsM="1"
                      singleContainerFullSize="false"
                    />
                  </form:layout>
                  <form:formContainers>
                    <form:FormContainer>
                      <form:formElements>
                        <form:FormElement label="{i18n>txtApplication}">
                          <form:fields>
                            <Input
                              id="optimizeDefaultInput"
                              value="{ltfAppModel>/optimizeDefault}"
                              enabled="true"
                              editable="false"
                            />
                          </form:fields>
                        </form:FormElement>
                      </form:formElements>
                    </form:FormContainer>
                  </form:formContainers>
                </form:Form>
                <RadioButtonGroup
                  id="optimizeNewSettingsRadioGroup"
                  columns="1"
                >
                  <RadioButton
                    id="optimizeNewSettingsId"
                    text="{i18n>txtNewSettings}"
                    select=".onSettingsTabOptionSelect"
                    selected="false"
                  />
                </RadioButtonGroup>
                <form:Form>
                  <form:layout>
                    <form:ResponsiveGridLayout
                      labelSpanXL="4"
                      labelSpanL="3"
                      labelSpanM="4"
                      labelSpanS="12"
                      adjustLabelSpan="false"
                      emptySpanXL="0"
                      emptySpanL="4"
                      emptySpanM="0"
                      emptySpanS="0"
                      columnsXL="2"
                      columnsL="1"
                      columnsM="1"
                      singleContainerFullSize="false"
                    />
                  </form:layout>
                  <form:formContainers>
                    <form:FormContainer>
                      <form:formElements>
                        <form:FormElement label="{i18n>txtApplication}">
                          <form:fields>
                            <Select
                              id="optimizeNewSelect"
                              selectedKey="{ltfAppModel>/optimizeAppSelected}"
                              items="{path:'ltfAppModel>/appData', templateShareable: false }"
                              change=".onOptimizeSelectChange"
                              enabled="false"
                              showSecondaryValues="true"
                              autoAdjustWidth="true"
                            >
                              <core:ListItem
                                key="{ltfAppModel>index}"
                                text="{ltfAppModel>index}"
                                additionalText="{parts:[
                            {path: 'ltfAppModel>maxCore' },
                            {path: 'ltfAppModel>maxMemory'}
                            ],
                            formatter:'.appDetailsListFormatter'
                          }"
                              >
                              </core:ListItem>
                            </Select>
                            </form:fields>
                        </form:FormElement>
                      </form:formElements>
                    </form:FormContainer>
                    </form:formContainers>
                </form:Form>
              </VBox>
            </VBox>

            <!-- Settings Section ends-->
          </ux:ObjectPageSubSection>
          <ux:ObjectPageSubSection
            title="{i18n>txtOptimizeSettings}"
            id="zOrderSettingsObjectPageId"
            visible="{= ${featureflags>/DWCO_LOCAL_TABLE_FILES_ZORDER} === true }"
            class="sapUxAPObjectPageSubSection ui-helper-clearfix"
          >
            <!-- Optimize Settings Section Starts-->
            <form:Form visible="{= ${/zOrderCols}.length > 0}" editable="true">
              <form:layout>
                <form:ResponsiveGridLayout
                  labelSpanXL="3"
                  labelSpanL="3"
                  labelSpanM="6"
                  labelSpanS="12"
                  adjustLabelSpan="false"
                  emptySpanXL="0"
                  emptySpanL="4"
                  emptySpanM="0"
                  emptySpanS="0"
                  columnsXL="3"
                  columnsL="3"
                  columnsM="6"
                  singleContainerFullSize="false"
                />
              </form:layout>
              <form:formContainers>
                <form:FormContainer>
                  <form:formElements>
                    <form:FormElement>
                      <form:fields>
                        <List
                          id="zOrderListId"
                          items="{ path: '/zOrderCols', templateShareable: false }">
                          <items>
                            <StandardListItem
                              title="{name}" />
                          </items>
                        </List>
                        </form:fields>
                    </form:FormElement>
                  </form:formElements>
                </form:FormContainer>
                </form:formContainers>
            </form:Form>
              <IllustratedMessage
              illustrationType="sapIllus-Tent"
              enableFormattedText="true"
              visible="{= ${/zOrderCols}.length === 0 }"
              title="{i18n>txtNoZOrderList}"
              description="{i18n>txtEmptyZOrderList}"
              >
                <additionalContent>
                  <Button id="addZOrderBtnId" text="{i18n>txtDefineZOrder}" press="onZOrderEdit"/>
                </additionalContent>
              </IllustratedMessage>
            <!-- Optimize Settings Section Ends-->
            <ux:actions>
              <Button id="zOrderEditBtnId" text="{i18n>txtZOrderEdit}" press="onZOrderEdit"/>
              <Button id="zOrderDeleteBtnId" text="{i18n>txtZOrderDelete}" press="onZOrderDelete" enabled="{= ${/zOrderCols}.length > 0 }"/>
            </ux:actions>
          </ux:ObjectPageSubSection>
        </ux:subSections>
      </ux:ObjectPageSection>
      <ux:ObjectPageSection
        id="dataDeletionObjectPageSectionId"
        titleUppercase="false"
        visible="{parts:[
            {path: 'ltfSpace>/isHdlfStorage' },
            {path: 'privilege>/DWC_DATAINTEGRATION/update'},
            {path: 'featureflags>/DWCO_LTA_DATA_DELETION'}
            ],
            formatter:'.dataDeleteTabVisibilityFormatter'
          }"
        showTitle="true"
      >
        <ux:subSections>
          <ux:ObjectPageSubSection
            title="{i18n>txtDataDeletion}"
            class="sapUxAPObjectPageSubSection ui-helper-clearfix"
          >
            <ux:blocks>
              <VBox height="100%">
                <MessageStrip
                  text="{i18n>dataDeleteWarningMsgTxt}"
                  width="100%"
                  showIcon="true"
                  showCloseButton="true"
                  type="Warning"
                  class="sapUiSmallMarginTop"
                />
                <mvc:XMLView
                  id="dataDeleteSectionId"
                  viewName="sap.cdw.components.localtablemonitor.view.DataDelete"
                />
              </VBox>
            </ux:blocks>
          </ux:ObjectPageSubSection>
        </ux:subSections>
      </ux:ObjectPageSection>
      <ux:ObjectPageSection
        id="scheduleDataDeleteObjectPageId"
        titleUppercase="false"
        visible="{parts:[
            {path: 'ltfSpace>/isHdlfStorage' },
            {path: 'featureflags>/DWCO_LTA_DATA_DELETION'}
            ],
            formatter:'.objectPageTabVisibilityHANA'
          }"
        title="{parts: [
          {path: 'ltfSpace>/isHdlfStorage'},
          {path: 'featureflags>/DWCO_LTA_DATA_DELETION'},
          {path: 'localTableRouteObj>/scheduleLogsLength'}
          ], formatter: '.scheduleTabTitleFormatter'}"
      >
        <ux:subSections>
          <ux:ObjectPageSubSection
            id="scheduleDataDeleteSubObjectPageId"
            titleUppercase="false"
          >
            <mvc:XMLView
              id="scheduleDeleteView"
              viewName="sap.cdw.components.localtablemonitor.view.ScheduleDelete"
            />
          </ux:ObjectPageSubSection>
        </ux:subSections>
      </ux:ObjectPageSection>
      <ux:ObjectPageSection
        id="dataDeleteLogsObjectPageSectionId"
        titleUppercase="false"
        visible="{parts:[
            {path: 'ltfSpace>/isHdlfStorage'},
            {path: 'featureflags>/DWCO_LTA_DATA_DELETION'}
            ],
            formatter:'.objectPageTabVisibilityHANA'
          }"
        title="{i18n>txtDataDeletionLogs} ({localTableRouteObj>/logLength})"
      >
        <ux:subSections>
          <ux:ObjectPageSubSection>
            <ux:blocks>
              <core:Fragment
                id="dataDeleteLogTableView"
                fragmentName="sap.cdw.components.localtablemonitor.view.fragment.DataDeleteLogs"
                type="XML"
              />
            </ux:blocks>
          </ux:ObjectPageSubSection>
        </ux:subSections>
      </ux:ObjectPageSection>
    </ux:sections>
  </ux:ObjectPageLayout>
</mvc:View>
