<mvc:View
  xmlns:core="sap.ui.core"
  xmlns:mvc="sap.ui.core.mvc"
  xmlns:ac="sap.cdw.components.reuse.control.actionchecker"
  xmlns:cd="http://schemas.sap.com/sapui5/extension/sap.ui.core.CustomData/1"
  xmlns="sap.m"
  controllerName="sap.cdw.components.localtablemonitor.controller.ScheduleDataDelete"
  >
  <ac:ActionChecker
    id="scheduleDataDeletePageActionChecker"
    hanaState="{circuitbreaker>/DataHANA}"
    hanaProvisioningState="{circuitbreaker>/DataHANAProvisioningState}"
    actionControlIds="createScheduleId,DataDeleteScheduleDeleteBtnId, DataDeleteScheduleCreateBtnId, dataDeleteScheduleAuth, DataDeleteScheduleEditBtnId"
    hiddenMode="true"
></ac:ActionChecker>
  <VBox>
    <mvc:XMLView
      id="dataDeleteScheduleAuth"
      viewName="sap.cdw.components.taskscheduler.view.TaskScheduleAuth"
      visible="{= ${featureflags>/DWC_DUMMY_SPACE_PERMISSIONS} ? ${privilege>/DWC_DATAINTEGRATION/execute} === true &amp;&amp; ${privilege>/DWC_SPACEFILE/create} &amp;&amp; ${privilege>/DWC_SPACEFILE/update} &amp;&amp; ${privilege>/DWC_SPACEFILE/delete} : ${privPersistedView>/execute} === true &amp;&amp; ${privSpaceFile>/create} &amp;&amp; ${privSpaceFile>/update} &amp;&amp; ${privSpaceFile>/delete} }"
      cd:actionId="monitoring/editor/authFlow"
    />
    <Table id="scheduleDataDeleteTableId" items="{/logs}" busy="{/busy}" backgroundDesign="Transparent"
    mode="SingleSelectMaster" selectionChange="onSelectionChange">
      <headerToolbar>
      <OverflowToolbar>
        <ToolbarSpacer/>
        <SearchField id="deleteScheduleSearchId" placeholder="{i18n>search}" liveChange="onSearchField" width="15rem" />
          <Button id="DataDeleteScheduleCreateBtnId" text="{i18n>createbtn}" type="Transparent" enabled="{= ${localTableRouteObj>/repoAccessError} !== true }" press="handleOpenDialog" cd:actionId="monitoring/editor/deleteData" visible="{= ${privilege>/DWC_DATAINTEGRATION/execute} === true &amp;&amp; ${isHdlfStorage} !== true}"></Button>
          <Button id="DataDeleteScheduleEditBtnId" text="{i18n>editBtn}" type="Transparent" enabled="{parts: [{path: 'localTableRouteObj>/repoAccessError'},'/editBtnEnabled', '/logs'], formatter: '.scheduleTableBtnFormatter'}" press="handleEditDialog" visible="{= ${privilege>/DWC_DATAINTEGRATION/execute} === true &amp;&amp; ${isHdlfStorage} !== true}"></Button>
          <Button id="DataDeleteScheduleDeleteBtnId" text="{i18n>deleteBtn}" type="Transparent" enabled="{parts: [{path: 'localTableRouteObj>/repoAccessError'},'/editBtnEnabled', '/logs'], formatter: '.scheduleTableBtnFormatter'}" press="handleDeleteSchedule" cd:actionId="monitoring/editor/deleteData" visible="{= ${privilege>/DWC_DATAINTEGRATION/execute} === true &amp;&amp; ${isHdlfStorage} !== true}"></Button>
          <core:Fragment
            id="scheduleActionMenuButton"
            fragmentName="sap.cdw.components.localtablemonitor.view.fragment.ScheduleCreateButtons"
            type="XML"
          />
          <OverflowToolbarButton
            icon="sap-icon://sort"
            type="Transparent"
            press="OnDataDeleteScheduleSort"
            id="dataDeleteScheduleSortBtn"
          />
          <OverflowToolbarButton
            icon="sap-icon://filter"
            type="Transparent"
            press="OnDataDeleteScheduleFilter"
            id="dataDeleteScheduleFilterBtn"
          />
      </OverflowToolbar>
    </headerToolbar>
    <infoToolbar>
      <OverflowToolbar
        id="scheduleDataDeleteTable--FilterBar"
        visible="false"
      >
        <Text text="{/dataDeleteScheduleTableFilterBarText}" />
      </OverflowToolbar>
    </infoToolbar>
      <columns>
        <Column>
          <Label text="{i18n>deletionScheduleType}"></Label>
        </Column>
        <Column>
          <Label text="{i18n>deletionScheduleName}"></Label>
        </Column>
        <Column>
          <Label text="{i18n>deletionScheduleFrequency}"></Label>
        </Column>
        <Column hAlign="End">
          <Label text="{i18n>deletionScheduleLastRunStart}"></Label>
        </Column>
        <Column hAlign="End">
          <Label text="{i18n>deletionScheduleLastRunEnd}"></Label>
        </Column>
        <Column>
          <Label text="{i18n>txtStatusColumn}"></Label>
        </Column>
      </columns>
      <items>
        <ColumnListItem vAlign="Middle">
          <Text
              text="{
                parts: ['configuration'],
                formatter: '.formatScheduleType'
               }"
              wrapping="false"
            />
            <Text
              text="{configuration/businessName}"
              wrapping="false"
            />
            <Text
            text="{
              parts: ['frequency', 'cron', 'activationStatus'],
              formatter: '.formatFrequency'
             }"
              wrapping="false"
            />
            <Text
              text="{
                parts: ['lastRun/start'],
                formatter: '.formatLastRunDateTime'
               }"
              wrapping="false"
            />
            <Text
              text="{
                parts: ['lastRun/end'],
                formatter: '.formatLastRunDateTime'
              }"
              wrapping="false"
            />
            <ObjectStatus
              text="{ parts: [{ path: 'lastRun/status'}], formatter:'.formatStatus' }"
              state="{ parts: [{ path: 'lastRun/status'}], formatter:'.statusTextStateFormatter' }"
            />
        </ColumnListItem>
      </items>
    </Table>
  </VBox>
</mvc:View>
