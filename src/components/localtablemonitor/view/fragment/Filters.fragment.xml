<core:FragmentDefinition
  xmlns="sap.m"
  xmlns:core="sap.ui.core"
  xmlns:l="sap.ui.layout"
  xmlns:f="sap.ui.layout.form"
  xmlns:cd="http://schemas.sap.com/sapui5/extension/sap.ui.core.CustomData/1"
  xmlns:ac="sap.cdw.components.reuse.control.actionchecker"
>
<ac:ActionChecker
id="dataDeleteWithFilterActionChecker"
spaceLocked="{diPageModel>/isSpaceLocked}"
hanaState="{circuitbreaker>/DataHANA}"
hanaProvisioningState="{circuitbreaker>/DataHANAProvisioningState}"
actionControlIds="showPreviewBtnId"
hiddenMode="true"
></ac:ActionChecker>

  <VBox visible="{= ${/dataDeleteWithFilter} === true}" class="sapUiSmallMarginBegin">
    <Title text="{i18n>txtDeletionSettings}" class="sapUiTinyMarginBottom"></Title>
    <HBox width="60%">
      <List
        items="{filterModel>/filteredDeletions}"
        id="filterValues"
        class="filter-row-item"
        visible="true"
        showSeparators="None"
      >
        <CustomListItem id="filterCustomListItem">
          <HBox class="column-list-header">
            <HBox width="100%">
              <Select
                id="filterColumns"
                visible="true"
                class="sapUiTinyMarginEnd"
                selectedKey="{filterModel>name}"
                items="{path:'filterModel>filterableColumns', templateShareable:false}"
                change=".onFilterColumnChange"
                enabled="true"
                width="13rem"
              >
                <core:ListItem
                  key="{filterModel>name}"
                  text="{parts: [{path:'filterModel>label'}, {path:'filterModel>name'}], formatter:'.getDisplayNameForColumns'}"
                >
                </core:ListItem>
              </Select>
              <Select
                id="operators"
                class="sapUiTinyMarginEnd"
                visible="{path: 'filterModel>type', formatter: '.notDataTypeFilterApplicable'}"
                selectedKey="{filterModel>operation}"
                items="{path:'filterModel>possibleFilterOperations', templateShareable:false}"
                editable="true"
                enabled="true"
                change=".onOperatorChange"
                width="13rem"
              >
                <core:ListItem
                  key="{filterModel>key}"
                  text="{filterModel>text}"
                />
              </Select>
              <Input
                id="textInputValueLow"
                class="sapUiTinyMarginEnd"
                value="{filterModel>value1}"
                placeholder="{path:'filterModel>type', formatter:'.inputPlaceholderFormatter'}"
                liveChange=".onValueChange($event, false, true)"
                valueState="{filterModel>lowValueState}"
                valueStateText="{filterModel>lowValueStateText}"
                visible="{parts:[{path: 'filterModel>type'}, {path:'filterModel>operation'},{path: 'filterModel>cdcType'}], formatter:'.textInputLowValueToBeVisible'}"
                width="{= ${filterModel>operation} === 'BT' ? '10rem' : '20.5rem'}"
                enabled="true"
              />
              <Input
                id="textInputValueHigh"
                class="sapUiTinyMarginEnd"
                value="{filterModel>value2}"
                placeholder="{parts:[{path:'filterModel>type'}], formatter:'.inputPlaceholderFormatter'}"
                liveChange=".onValueChange($event, true, true)"
                valueState="{filterModel>highValueState}"
                valueStateText="{filterModel>highValueStateText}"
                visible="{parts:[{path:'filterModel>operation'},{path: 'filterModel>type'}], formatter:'.highGenericVisibilityFormatter'}"
                width="10rem"
                enabled="{workbenchEnv>/canCreateOrUpdateModel}"
              />
              <DynamicDateRange
                id="dynamic-range"
                class="sapUiTinyMarginEnd"
                change=".onDynamicDateOptionChange"
                standardOptions="{/stdOpts}"
                visible="{path: 'filterModel>type', formatter:'.isDateRangeFilterApplicable'}"
                value="{parts: [{path:'filterModel>operation'},{path: 'filterModel>value1'}, {path: 'filterModel>value2'}, {path: 'filterModel>range1'}, {path: 'filterModel>range2'}], formatter:'.setValueToDDR'}"
                valueState="{filterModel>lowValueState}"
                valueStateText="{filterModel>lowValueStateText}"
                width="34rem"
                calendarWeekNumbering="ISO_8601"
              />
              <Select
                id="boolean-select"
                class="sapUiTinyMarginEnd"
                selectedKey="{filterModel>booleanSelected}"
                visible="{= ${filterModel>type} === 'boolean' &amp;&amp; ${filterModel>operation} !== 'Empty'}"
                items="{path:'filterModel>/booleanValues', templateShareable:false}"
                change=".onBooleanSelect"
                valueState="{filterModel>lowValueState}"
                valueStateText="{filterModel>lowValueStateText}"
                editable="true"
                width="20.5rem"
              >
                <core:ListItem
                  key="{filterModel>key}"
                  text="{filterModel>text}"
                />
              </Select>
              <Select
                id="change-Type-select"
                class="sapUiTinyMarginEnd"
                selectedKey="{filterModel>ModeSelected}"
                visible="{= ${filterModel>cdcType} === 'MODE' &amp;&amp; ${filterModel>operation} !== 'Empty'}"
                items="{path:'filterModel>/changeTypeFilterValues', templateShareable:false}"
                change=".onChangeTypeSelect"
                valueState="{filterModel>lowValueState}"
                valueStateText="{filterModel>lowValueStateText}"
                editable="true"
                width="20.5rem"
              >
                <core:ListItem
                  key="{filterModel>key}"
                  text="{filterModel>text}"
                />
              </Select>
            </HBox>
            <HBox>
              <Button
              id="deleteRow"
              visible="true"
              icon="sap-icon://decline"
              enabled="{= ${filterModel>/filtersLength} &gt; 1}"
              type="Transparent"
              press="handleDeleteRow"
            />
              <Button
                id="addRow"
                visible="true"
                icon="sap-icon://add"
                enabled="true"
                type="Transparent"
                press="handleAddFilterRow"
              />
            </HBox>
          </HBox>
        </CustomListItem>
      </List>
    </HBox>
    <Button
      id="showPreviewBtnId"
      class="sapUiNoMarginBegin sapUiSmallMarginTop"
      text="{= ${i18n>datViewerBtn}}"
      press="showPreview"
      enabled="{
        parts: [{path: '/dataDeleteEnabled'},
                {path: '/repoAccessError'}],
        formatter: '.repoAccessCheckFormatter'
      }"
      visible="{
      parts: [{path: 'privilegeModel>/hasDatabuilderReadPrivilege'},
              {path: 'featureflags>/DWC_DUMMY_SPACE_PERMISSIONS'},
              {path: 'privilege>/DWC_CONSUMPTION/execute'}],
      formatter: '.showPreviewCheck'
    }"
      cd:actionId="monitoring/editor/editData"
    ></Button>
  </VBox>
</core:FragmentDefinition>
