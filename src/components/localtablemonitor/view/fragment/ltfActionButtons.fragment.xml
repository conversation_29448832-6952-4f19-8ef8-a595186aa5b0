<core:FragmentDefinition
  xmlns="sap.m"
  xmlns:core="sap.ui.core"
  xmlns:ac="sap.cdw.components.reuse.control.actionchecker"
  xmlns:cd="http://schemas.sap.com/sapui5/extension/sap.ui.core.CustomData/1"
>
<ac:ActionChecker
id="headerButtonActionChecker"
spaceLocked="{diPageModel>/isSpaceLocked}"
hanaState="{circuitbreaker>/DataHANA}"
hanaProvisioningState="{circuitbreaker>/DataHANAProvisioningState}"
actionControlIds="mergeTableActionButton,optimizeTableActionButton,scheduleActionMenuButton"
hiddenMode="true"
></ac:ActionChecker>
<Button
  id="mergeTableActionButton"
  type="Transparent"
  text="{i18n>txtMergeTable}"
  visible="{parts:[
  {path: 'ltfSpace>/isHdlfStorage' },
  {path: 'featureflags>/DWCO_LARGE_SYSTEMS_APPS_API'},
  {path: 'featureflags>/DWCO_LARGE_SYSTEMS_SPARK_SELECTION'},
  {path: 'privilege>/DWC_DATAINTEGRATION/update' }
  ],
  formatter: '.fileStorageActionsFormatter'
}"
  enabled="{= ${/isLTFActionsEnabled} === true }"
  press="onFileObjectActionPress"
  cd:actionId="monitoring/editor/execute"
/>
<Button
  id="optimizeTableActionButton"
  type="Transparent"
  text="{i18n>txtOptimizeTable}"
  visible="{parts:[
                {path: 'ltfSpace>/isHdlfStorage' },
                {path: 'featureflags>/DWCO_LARGE_SYSTEMS_APPS_API'},
                {path: 'featureflags>/DWCO_LARGE_SYSTEMS_SPARK_SELECTION'},
                {path: 'privilege>/DWC_DATAINTEGRATION/update' }
                ],
                formatter: '.fileStorageActionsFormatter'
              }"
  enabled="{= ${/isLTFActionsEnabled} === true }"
  press="onFileObjectActionPress"
  cd:actionId="monitoring/editor/execute"
/>
<MenuButton
  id="scheduleActionMenuButton"
  text="{i18n>scheduleStep}"
  type="Transparent"
  visible="{parts:[
              {path: 'ltfSpace>/isHdlfStorage' },
              {path: 'featureflags>/DWCO_LARGE_SYSTEMS_APPS_API'},
              {path: 'featureflags>/DWCO_LARGE_SYSTEMS_SPARK_SELECTION'},
              {path: 'privilege>/DWC_DATAINTEGRATION/execute' },
              {path: 'featureflags>/DWCO_LTA_DATA_DELETION'}
              ],
              formatter: '.fileStorageActionsFormatter'
            }"
  enabled="{= ${/isLTFActionsEnabled} === true }"
  cd:actionId="monitoring/editor/execute"
>
  <menu>
    <Menu itemSelected="onMenuAction">
      <MenuItem
        text="{i18n>txtMergeTable}"
        id="scheduleStepMergeParentButton"
        visible="{= ${privilege>/DWC_DATAINTEGRATION/execute} === true }"
      >
      <items>
        <MenuItem
          id="scheduleStepMergeCreateMenuItem"
          text="{i18n>createScheduleNewLabel}"
          press="onCreateScheduleFileObject"
          enabled="{= ${/newMergeSchedule} === true }"
        />
        <MenuItem
          id="scheduleStepMergeEditMenuItem"
          text="{i18n>changeScheduleLabel}"
          press="onEditScheduleFileObject"
          enabled="{= ${/newMergeSchedule} !== true }"
        />
        <MenuItem
          id="scheduleStepMergeDeleteMenuItem"
          text="{i18n>deleteScheduleLabel}"
          press="onDeleteScheduleFileObject"
          enabled="{= ${/newMergeSchedule} !== true }"
        />
      </items>
      </MenuItem>
      <MenuItem
        text="{i18n>txtOptimizeTable}"
        id="scheduleStepOptimizeParentButton"
      >
      <items>
        <MenuItem
          id="scheduleStepOptimizeCreateMenuItem"
          text="{i18n>createScheduleNewLabel}"
          press="onCreateScheduleFileObject"
          enabled="{= ${/newOptimizeSchedule} === true }"
        />
        <MenuItem
          id="scheduleStepOptimizeEditMenuItem"
          text="{i18n>changeScheduleLabel}"
          press="onEditScheduleFileObject"
          enabled="{= ${/newOptimizeSchedule} !== true }"
        />
        <MenuItem
          id="scheduleStepOptimizeDeleteMenuItem"
          text="{i18n>deleteScheduleLabel}"
          press="onDeleteScheduleFileObject"
          enabled="{= ${/newOptimizeSchedule} !== true }"
        />
      </items>
      </MenuItem>
    </Menu>
  </menu>
</MenuButton>
</core:FragmentDefinition>
