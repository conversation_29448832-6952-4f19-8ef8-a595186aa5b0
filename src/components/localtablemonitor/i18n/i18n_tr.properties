
msgDataDeletionStarted="{0}" tablosunun verilerini siliyoruz

#XMSG: message to inform table not found in the repository
localTableNotFound=<PERSON>rel tablo {0}, havuzda bulunamadı.

#XFLD: Used Disk
txtUsedDisk=Kullanılan disk (MiB)
txtSizeOnDisk=<PERSON>skte<PERSON> boyut (MiB)
#XFLD: Used In-Memory
txtUsedInMemory=Kullanılan bellek içi (MiB)
txtSizeInMemory=Bellek içi boyut (MiB)
#XFLD: Number of Records
txtNumberOfRecords=Kayıt sayısı
#XFLD: Delta capture
txtDeltaCapture=Delta yakalaması
#XFLD: In-Memory storage
txtInMemoryStorage=Bellek içi depolama
#XFLD: Technical Name
txtTechnicalName=Teknik ad
#XFLD: Business Name
txtBusinessName=İş adı
#XFLD: Growth Rate (Last Month)
txtgrowthRate=30 gündeki büyüme
#XFLD: Number of Partitions
txtnumberofpartitions=Bölümleme sayısı
#XFLD: Last Updated
txtlastUpdatedBy=Son güncelleyen
#XFLD: Last Updated
txtlastupdated=Son güncelleme
#XFLD:delta Capture
txtdeltaCapture=Delta yakalaması
txtYes=Evet
txtNo=Hayır
txtOn=Açık
txtOff=Kapalı
localTablesHeaderTitle=Yerel tablolar ({0})
localTablesLTFHeaderTitle=Yerel tablolar (dosya) ({0})
txtSearch=Ara
txtRefresh=Yenile
txtNotApplicable=Uygun değil
txtObject=Nesne ({0})

#XTOL:for Last updated column header
tolLastUpdated=Verilerin en son ne zaman güncellendiğini gösterir
#XTOL: for Last updated by column header
tolLastUpdatedBy=Verilerin en son nasıl güncellendiğini gösterir
#XTOL: for Last buffer updated column header
tolBufferLastUpdated=Gelen arabellekteki verilerin en son ne zaman güncellendiğini gösterir
#XTOL: for Last buffer updated by column header
tolBufferLastUpdatedBy=Gelen arabellekteki verilerin en son nasıl güncellendiğini gösterir


#XMSG: message to inform repo Access Error
txtRepoAccessError=Havuz kullanılamıyor ve belirli özellikler devre dışı.

#XBUT: Label for open in Editor
openInEditorNew=Veri oluşturucuda aç

#XTOL: Toggle button to open data editor
openDataEditor=Veri düzenleyici

#XBUT: Radio button group
txtDeleteAllRecords=Tüm kayıtları sil
txtDeleteFilteredRecords=Filtrelenen kayıtları sil
txtRecordsMarkedForDeleted="Silindi" olarak işaretlenen kayıtları sil

#XTXT: preview
txtPreview=Önizleme
#XMSG: Warning message for All Records delete
dataDeleteWarningMsgTxt=Kayıtların silinmesi, akışların tüketilmesini etkileyebilir.
#XMSG: Warning popup header
@confirmDataDeletion=Verileri sil
#XMSG: Warning popup message
@dataDeletionText=Bu işlem sonucunda tablo kayıtları silinecek ve bu durum akışların tüketilmesini etkileyebilir. Bu işlem geri alınamaz. Devam etmek istiyor musunuz?

#XBUT: Data Deletion warning
btnDeleteConfirm=Sil

#XFLD: Label for table column
durationInSeconds=saniye

#XMSG: Records marked as "Deleted" description
recordsMarkedForDeletionDescription="Silindi" değişiklik türüne sahip ve şu süreden daha eski olan, tamamen işlenmiş tüm kayıtları sil
daysForDeltaTableDeletion=Gün
txtCurrentWatermark=Geçerli filigran

#XBUT: show preview button in filter
datViewerBtn=Veri görüntüleyiciyi aç

#XFLD: Number of deletable records
txtNumberOfDeletableRecords=Silinebilir kayıt sayısı

#XFLD: Size of deletable records
txtSizeOfDeletableRecords=Silinebilir kayıtların boyutu (MiB)

numberOfRecordsRefresh=Yenile

#XTXT: Data Deletion Logs
txtDataDeletionLogs=Veri silme logoları

#XTXT: Data Deletion Schedules
txtDataDeletionSchedules=Veri silme planları

#XTXT: Data Deletion
txtDataDeletion=Veri silme

#XBTN: Delete Records
deleteRecords=Kayıtları sil
#XBTN: Create
createbtn=Oluştur
#XBTN: Edit
editBtn=Düzenle
#XBTN: Delete
deleteBtn=Sil
#XTXT: Placeholder for search bar
txtSearchPlaceholder=Ara

#XTXT: Data Deletion
txtDeletionSettings=Silme ayarları

currentWatermark=Geçerli filigran

#XBUT: Select Columns Button
selectColumnsBtn=Sütun seç

#XBUT: Merge table for ltf table
txtMergeTable=Tabloyu birleştir

#XBUT: Optimize table for ltf table
txtOptimizeTable=Tabloyu optimize et

#XBUT: schedule Merge and Optimize for ltf table
scheduleTextLabel=Planla

#XBUT: Drop down menu button to create a schedule
createScheduleNewLabel=Planlama oluştur
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Planlamayı düzenle
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Planlamayı sil
#XBUT: Assign schedule menu button label
assignScheduleLabel=Planlamayı bana tayin et
#XBUT: Pause schedule menu label
pauseScheduleLabel=Planlamayı duraklat
#XBUT: Resume schedule menu label
resumeScheduleLabel=Planlamayı sürdür

#XMSG: message to say that merge task is started
msgMergeTaskStarted=Tablo "{0}" için birleştirme görevi başlatıldı

#XMSG: message to say that optimize task is started
msgOptimizeTaskStarted=Tablo "{0}" için optimizasyon görevi başlatıldı

#XTXT: title for object page for logs
hdlfLogsObjectPageTxt=Günlükler

#XTXT: title for object page for settings
hdlfSettingsObjectPageTxt=Ayarlar
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Delete Logs Table~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Label for Data delete logs table
txtStartColumn=Başlangıç
txtStartColumnTooltip=Başlangıç
#XFLD: Label for Data delete logs table
txtDurationColumn=Süre
txtDurationColumnTooltip=Süre
#XFLD: Label for Data delete logs table
txtObjectTypeColumn=Nesne türü
txtObjectTypeColumnTooltip=Nesne türü
#XFLD: Label for Data delete logs table
txtActivityColumn=Aktivite
txtActivityColumnTooltip=Aktivite
#XFLD: Label for Data delete logs table
txtUserColumn=Kullanıcı
txtUserColumnTooltip=Kullanıcı
txtRunStartedBy=Çalıştırmayı başlatan
txtManualRun=Manüel
txtScheduledRun=Planlandı
#XFLD: Label for Data delete logs table
txtNumberOfRecordsColumn=Kayıt sayısı
txtNumberOfRecordsColumnTooltip=Kayıt sayısı
#XFLD: Label for Data delete logs table
txtStatusColumn=Durum
txtStatusColumnTooltip=Durum

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Delete Logs Filter Dialog ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Son saat
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Son 24 saat
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Son ay

#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=5 dakikadan fazla
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=15 dakikadan fazla
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=1 saatten fazla

#XFLD: status text
COMPLETED=Tamamlandı
#XFLD: status text
FAILED=Başarısız oldu
#XFLD: status text
RUNNING=Çalışıyor

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Deletion Schedules Table~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XCOL: Type of Data deletion schedule
deletionScheduleType=Tür
#XCOL: Name of Data deletion schedule
deletionScheduleName=Ad
#XCOL: Frequency of schedule
deletionScheduleFrequency=Sıklık
#XCOL: DateTime of when the last run started
deletionScheduleLastRunStart=Son çalıştırma başlangıcı
#XCOL: DateTime of when the last run ended
deletionScheduleLastRunEnd=Son çalıştırma bitişi
#XCOL: Number of records deleted in the last scheduled run
deletionScheduleRecordsLastRun=Kayıtlar (son çalıştırma)
#XBUT: To create a data deletion schedule
createDataDeletionScheduleBtn=Veri silme planı oluştur
#XMSG: Data Deletion Schedules Table
noDataDeletionScheduleText=Henüz veri silme planı tanımlanmadı
#XMSG: Data Deletion Schedules Table
noDataDeletionScheduleDescription=İhtiyacınız olmayan verileri düzenli aralıklarla silmek için veri silme planları oluşturun
#XFLD: Frequency if a schedule is paused
paused:Duraklatıldı

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Schedule Data Deletion Wizard ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Title for the schedule data deletion wizard
scheduleDataDeletionTxt=Veri silme planla
#XBUT: Schedule Data Deletion Wizard
back=Geri
#XBUT: Schedule Data Deletion Wizard
nextStep=Sonraki adım
#XBUT: Schedule Data Deletion Wizard
createSchedule=Planlama oluştur
#XBUT: Schedule Data Deletion Wizard
updateSchedule=Planlamayı güncelle
#XGRP: Schedule Data Deletion Wizard's 1st step
settingStep=Ayarlar
#XGRP: Schedule Data Deletion Wizard's 2nd step
scheduleStep=Planlama
#XGRP: Schedule Data Deletion Wizard's 3rd step
reviewStep=Gözden geçirme
#XBUT: Schedule Data Deletion Wizard
cancel=İptal
#XBUT: Schedule Data Deletion Wizard
edit=Düzenle
#XFLD: Schedule Data Deletion Wizard
deleteAllRecordsFilteredBy=Tüm kayıtları silme filtre ölçütü
#XFLD: Schedule Data Deletion Wizard
recurrence=Yineleme
#XFLD: Schedule Data Deletion Wizard
nextRun=Sonraki çalıştırma
#XMSG: Error state for empty technical name
emptyLabel=Geçerli bir plan adı girin.
#XMSG: Error state for duplicate technical name
duplicateID=Aynı ada sahip bir planlama zaten mevcut.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Filtered Deletions ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Create Filtered Deletion button
createFilteredDeletionBtn=Filtre oluştur

#XFLD: Filtered Deletions Operators
EQ=Şuna eşittir
BT=Arasında
Contains=Şunu içerir
GT=Şundan büyüktür
GE=Şundan büyüktür veya şuna eşittir
LT=Şundan küçüktür
LE=Şundan küçüktür veya şuna eşittir
StartsWith=Şununla başlar
EndsWith=Şununla biter
Empty=Boş
BXD={0} gün önce


#XFLD: Placeholder for filter input
txtDatePlaceholder=YYYY-AA-GG
txtTimePlaceholder=SS:dd:ss
txtDateTimePlaceholder=YYYY-AA-GG SS:dd:ss
txtStringPlaceholder=Dize girin
txtNumberPlaceholder=Sayı girin
txtValuePlaceholder=Değer girin

#XMSG-Validation messages
txtEmptyFilterValue=Geçerli bir filtre değeri girin.
#XMSG: Error message for empty  value
txtEmptyValueForDDR=Gün cinsinden değer sağlayın.

#XMSG: lower bound error text
txtLowerbounderrormsg=Alt sınır üst sınırdan düşük olmalıdır.
#XMSG: lower bound error text for string
txtLowerbounderrormsgforString="{0}" dizesi, "{1}" değerini aşıyor.
#XMSG: higher bound error text
txtHigherbounderrormsg=Üst sınır alt sınırdan yüksek olmalıdır.
#XMSG: higher bound error text for string
txtHigherbounderrormsgforString="{1}" dizesi, "{0}" değerinin altında kalıyor.
#XMSG Error msg for missing values
VAL_LENGTH_EXCEED=Değer uzunluğu {0} değerini aşmamalıdır.
VAL_ENTER_VALID_INT=Geçerli tamsayı girin.
VAL_ENTER_VALID_DECIMAL={0} doğruluğuna ve {1} ölçeğine sahip geçerli ondalık değer girin.
VAL_ENTER_VALID_DECIMAL_VALUE=Ondalık değer girin.
VAL_ENTER_VALID_DATE=Geçerli bir tarih girin.
VAL_ENTER_VALID_NUMBER=Geçerli bir sayı girin.
VAL_DUPLICATE_FILTER=Filtre değerini çoğaltın.
VAL_DEFAULT_RANGE_EXCEED_INT=-2.147.483.648 ila 2.147.483.647 arasında bir değere sahip geçerli bir tamsayı girin.
VAL_DEFAULT_RANGE_EXCEED_BIGINT=-9.223.372.036.854.775.808 ila 9.223.372.036.854.775.807 arasında bir değere sahip geçerli bir tamsayı girin.

#XMSG: Incase Current Watermark is not applicable
notApplicableText=İlişkili değil

#XTXT: Preview Data to be Deleted
previewDataToBeDeleted=Silinecek verileri önizle

#XBTN: Close Preview
txtCloseBtn=Kapat


#XBTN: Segmented button for filtered Data
txtFilteredData=Filtrelenen veriler

#XBTN: Segmented button for All Data
txtAllData=Tüm veriler

txtDataViewer=Veri görüntüleyici
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ LTF ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XCOL: Buffer Merge Status
txtBufferMergeStatus=Arabellek birleştirme durumu

#XCOL: Buffer Last Updated
txtBufferLastUpdated=Arabelleğin son güncellenme zamanı

#XCOL: Buffer Last Updated By
txtBufferLastUpdatedBy=Arabelleği son güncelleyen

#XCOL: Partitions
txtPartitions=Bölümlemeler

#XCOL: Determine the number of records
txtActiveRecords=Etkin kayıt sayısı

#XCOL: Collect data via Spark (DeltaTable)
txtActiveRecordsFileStorage=Etkin kayıtlar depolaması (MiB)

#XCOL: Calculate data via Spark
txtPreviousVersionsFileStorage=Önceki versiyonlar depolaması (MiB)

#XCOL: Collect the size of all files via Spark
txtTotalTableFileStorage=Toplam depolama (MiB)

#XCOL: Collect the inbound buffer file size
txtInboundBufferFileSize=Arabellek dosya boyutu (MiB)

#XCOL: Collect the inbound buffer file count
txtInboundBufferFileCount=Arabellek dosya sayısı

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Viewer dialog ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=Sütun ayarları
#XFLD: Title for Error type
messagesTableType=Tür
#XFLD: Title for Error Message
messagesTableMessage=İleti
#XFLD: Title for filter
filteredBy=Filtre ölçütü:
#XTIT: text for values contained in filter
filterContains=şunu içerir
#XTIT: text for values starting with in filter
filterStartsWith=şununla başlar
#XTIT: text for values ending with in filter
filterEndsWith=şununla biter
#XTIT: Title for search in data preview toolbar
toolbarSearch=Ara
#XBUT: Button to clear filter
clearFilter=Filtreyi temizle
#XBUT: Button to save the operation
ok=Tamam
#XBUT: Button to restore the data
toolbarRestoreButton=Yenile
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=Sayfayı güncelle
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=İşleniyor...
#XMSG: Message Confirmation
confirmation=Teyit
#XMSG: Message for refresh successful
refreshSuccess=Başarıyla yenilendi
#XMSG: Message for refresh successful
refreshSuccessful=Yenilendi
#XMSG: Message for restore successful
restoreSuccessful=Başarıyla geri yüklendi
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" boş olamaz. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=Artan düzende sırala
#XTIT: Sort Descending
mdm-sortDescending=Azalan düzende sırala
#XTIT: Filter
mdm-Filter=Filtre
#XBUT: Button Cancel
mdm-cancel=İptal
#XBUT: Button Add
mdm-Add=Ekle
#XMSG: and inside error message
and=ve

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=Filtrele, sırala, sil ve tablo ayarı araçları kaydedilmeyen değişiklikler tarafından devre dışı bırakıldı. Etkinleştirmek için değişikliklerinizi kaydedin.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=Verileri yükle
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=Çoğalt
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=Sil
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=Ekle

#XTIT: text to be apended for key columns in p13n dialog e.g. (Key) <Column Name>
mdm-keyText=Anahtar
#XTIT: text to be apended for Not Null columns
mdm-notNullText=Null değil

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=Eksik dize değerini şu şekilde ekle:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=NULL
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=Boş dize
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=Eksik dize değerleri yalnızca yeni ve düzenlenen satırlardaki görünür dizelere eklenebilir. Tüm ilgili sütunları görüntülemek için sütun ayarlarını kullanın.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=Filtrele, sırala, eksik dize değerini ekle ve tablo ayarı araçları kaydedilmeyen değişiklikler tarafından devre dışı bırakıldı. Etkinleştirmek için değişikliklerinizi kaydedin.
#XFLD: Open SAP HANA Cockpit

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Settings LSA ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
txtHeaderApacheSettings=Apache Spark uygulama ayarları
txtMergeSectionTitle=Birleştir
txtUseSpaceDefault=Alan varsayılanını kullan
txtApplication=Uygulama
txtOptimizeSectionTitle=Optimize et
txtNewSettings=Bu tablo için yeni ayar tanımla
txtNewSettingsMessageBox=Bu görev için yeni ayarlar tanımla
txtUseDefault=Varsayılanı kullan
txtMergeMsgBoxTitle=Tabloyu birleştir
txtOptimizeMsgBoxTitle=Tabloyu optimize et
#XTIT: sub section header for LSA settings i.e Optimize settings
txtOptimizeSettings=Ayarları optimize et
#XTIT: Z - order columns list header
txtZOrderList=Z-sırası sütunları
#XBUT: Button to define z order columns
txtZOrderEdit=Düzenle
#XBUT: Button to save the z order columns
txtZOrderDelete=Sil
#XMSG: message for empty z order columns
txtNoZOrderList=Ayar mevcut değil
#XMSG: message for empty z order list
txtEmptyZOrderList=Z-sırası sütunlarını tanımlayarak performansı iyileştirin
#XBUT: Button to add z order columns
txtDefineZOrder=Z-sırası sütunlarını tanımlayın
#XLBL: Label for z order columns
txtColumnZOrder=Sütunlar
#XMSG: error message to inform user about HANA limitation
zOrderLimitationError=Yalnızca bir Z-sırası sütunu seçebilirsiniz.

#XMSG: message for conflicting task
taskAlreadyRunning=Nesne "{0}" için çakışan bir görev zaten çalışıyor.

#XMSG: Save spark settings success
saveSparkSettingsSuccess=Apache Spark uygulama ayarları kaydedildi.
