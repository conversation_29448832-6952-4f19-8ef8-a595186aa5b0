
msgDataDeletionStarted=Data i tabell "{0}" raderas

#XMSG: message to inform table not found in the repository
localTableNotFound=Lokal tabell {0} hittades inte i repository.

#XFLD: Used Disk
txtUsedDisk=Använd disk (MiB)
txtSizeOnDisk=Storlek på disk (MiB)
#XFLD: Used In-Memory
txtUsedInMemory=Använd minnesbaserad lagring (MiB)
txtSizeInMemory=Storlek för minnesbaserad (MiB)
#XFLD: Number of Records
txtNumberOfRecords=Antal poster
#XFLD: Delta capture
txtDeltaCapture=Deltaregistrering
#XFLD: In-Memory storage
txtInMemoryStorage=Minnesbaserad lagring
#XFLD: Technical Name
txtTechnicalName=Tekniskt namn
#XFLD: Business Name
txtBusinessName=Affärsnamn
#XFLD: Growth Rate (Last Month)
txtgrowthRate=Tillväxt under 30 dagar
#XFLD: Number of Partitions
txtnumberofpartitions=Antal partitioner
#XFLD: Last Updated
txtlastUpdatedBy=Senaste uppdatering av
#XFLD: Last Updated
txtlastupdated=Senaste uppdatering
#XFLD:delta Capture
txtdeltaCapture=Deltaregistrering
txtYes=Ja
txtNo=Nej
txtOn=På
txtOff=Av
localTablesHeaderTitle=Lokala tabeller ({0})
localTablesLTFHeaderTitle=Lokala tabeller (fil) ({0})
txtSearch=Sök
txtRefresh=Uppdatera
txtNotApplicable=Ej tillämplig
txtObject=Objekt ({0})

#XTOL:for Last updated column header
tolLastUpdated=Visar senaste gången data uppdaterades
#XTOL: for Last updated by column header
tolLastUpdatedBy=Visar hur data senast uppdaterades
#XTOL: for Last buffer updated column header
tolBufferLastUpdated=Visar senaste gången data i ingångsbuffert uppdaterades
#XTOL: for Last buffer updated by column header
tolBufferLastUpdatedBy=Visar hur data i ingångsbuffert senast uppdaterades


#XMSG: message to inform repo Access Error
txtRepoAccessError=Repository är inte tillgängligt och vissa funktioner har inaktiverats.

#XBUT: Label for open in Editor
openInEditorNew=Öppna i Data Builder

#XTOL: Toggle button to open data editor
openDataEditor=Dataeditor

#XBUT: Radio button group
txtDeleteAllRecords=Radera alla poster
txtDeleteFilteredRecords=Radera alla filtrerade poster
txtRecordsMarkedForDeleted=Radera poster som markerats som "Raderad"

#XTXT: preview
txtPreview=Förhandsgranskning
#XMSG: Warning message for All Records delete
dataDeleteWarningMsgTxt=Radering av poster kan påverka förbrukande flöden.
#XMSG: Warning popup header
@confirmDataDeletion=Radera data
#XMSG: Warning popup message
@dataDeletionText=Denna åtgärd kommer att radera tabellposter vilket kan påverka förbrukande flöden. Detta kan inte ångras. Vill du fortsätta?

#XBUT: Data Deletion warning
btnDeleteConfirm=Radera

#XFLD: Label for table column
durationInSeconds=sekunder

#XMSG: Records marked as "Deleted" description
recordsMarkedForDeletionDescription=Radera alla fullständigt bearbetade poster med ändringstyp "Raderad" som är äldre än
daysForDeltaTableDeletion=Dagar
txtCurrentWatermark=Aktuell vattenstämpel

#XBUT: show preview button in filter
datViewerBtn=Öppna Data Viewer

#XFLD: Number of deletable records
txtNumberOfDeletableRecords=Antal raderbara poster

#XFLD: Size of deletable records
txtSizeOfDeletableRecords=Storlek på raderbara poster (MiB)

numberOfRecordsRefresh=Uppdatera

#XTXT: Data Deletion Logs
txtDataDeletionLogs=Dataraderingsprotokoll

#XTXT: Data Deletion Schedules
txtDataDeletionSchedules=Dataraderingsscheman

#XTXT: Data Deletion
txtDataDeletion=Dataradering

#XBTN: Delete Records
deleteRecords=Radera poster
#XBTN: Create
createbtn=Skapa
#XBTN: Edit
editBtn=Redigera
#XBTN: Delete
deleteBtn=Radera
#XTXT: Placeholder for search bar
txtSearchPlaceholder=Sök

#XTXT: Data Deletion
txtDeletionSettings=Raderingsinställningar

currentWatermark=Aktuell vattenstämpel

#XBUT: Select Columns Button
selectColumnsBtn=Välj kolumner

#XBUT: Merge table for ltf table
txtMergeTable=Slå samman tabell

#XBUT: Optimize table for ltf table
txtOptimizeTable=Optimera tabell

#XBUT: schedule Merge and Optimize for ltf table
scheduleTextLabel=Schema

#XBUT: Drop down menu button to create a schedule
createScheduleNewLabel=Skapa schema
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Redigera schema
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Radera schema
#XBUT: Assign schedule menu button label
assignScheduleLabel=Allokera schema till mig
#XBUT: Pause schedule menu label
pauseScheduleLabel=Pausa schema
#XBUT: Resume schedule menu label
resumeScheduleLabel=Återuppta schema

#XMSG: message to say that merge task is started
msgMergeTaskStarted=Sammanslagningsuppgift har startats för tabell "{0}"

#XMSG: message to say that optimize task is started
msgOptimizeTaskStarted=Optimeringsuppgift har startats för tabell "{0}"

#XTXT: title for object page for logs
hdlfLogsObjectPageTxt=Protokoll

#XTXT: title for object page for settings
hdlfSettingsObjectPageTxt=Inställningar
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Delete Logs Table~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Label for Data delete logs table
txtStartColumn=Start
txtStartColumnTooltip=Start
#XFLD: Label for Data delete logs table
txtDurationColumn=Tidslängd
txtDurationColumnTooltip=Tidslängd
#XFLD: Label for Data delete logs table
txtObjectTypeColumn=Objekttyp
txtObjectTypeColumnTooltip=Objekttyp
#XFLD: Label for Data delete logs table
txtActivityColumn=Aktivitet
txtActivityColumnTooltip=Aktivitet
#XFLD: Label for Data delete logs table
txtUserColumn=Användare
txtUserColumnTooltip=Användare
txtRunStartedBy=Körning startad av
txtManualRun=Manuell
txtScheduledRun=Inplanerad
#XFLD: Label for Data delete logs table
txtNumberOfRecordsColumn=Antal poster
txtNumberOfRecordsColumnTooltip=Antal poster
#XFLD: Label for Data delete logs table
txtStatusColumn=Status
txtStatusColumnTooltip=Status

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Delete Logs Filter Dialog ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Senaste timmen
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Senaste 24 timmarna
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Senaste månaden

#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Mer än 5 minuter
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Mer än 15 minuter
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Mer än 1 timme

#XFLD: status text
COMPLETED=Slutfördes
#XFLD: status text
FAILED=Misslyckades
#XFLD: status text
RUNNING=Körs

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Deletion Schedules Table~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XCOL: Type of Data deletion schedule
deletionScheduleType=Typ
#XCOL: Name of Data deletion schedule
deletionScheduleName=Namn
#XCOL: Frequency of schedule
deletionScheduleFrequency=Frekvens
#XCOL: DateTime of when the last run started
deletionScheduleLastRunStart=Start för senaste körning
#XCOL: DateTime of when the last run ended
deletionScheduleLastRunEnd=Slut på senaste körning
#XCOL: Number of records deleted in the last scheduled run
deletionScheduleRecordsLastRun=Poster (senaste körning)
#XBUT: To create a data deletion schedule
createDataDeletionScheduleBtn=Skapa dataraderingsschema
#XMSG: Data Deletion Schedules Table
noDataDeletionScheduleText=Inga dataraderingsscheman har definierats än
#XMSG: Data Deletion Schedules Table
noDataDeletionScheduleDescription=Skapa dataraderingsscheman för att regelbundet ta bort data du inte behöver
#XFLD: Frequency if a schedule is paused
paused:Pausat

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Schedule Data Deletion Wizard ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Title for the schedule data deletion wizard
scheduleDataDeletionTxt=Planera in dataradering
#XBUT: Schedule Data Deletion Wizard
back=Tillbaka
#XBUT: Schedule Data Deletion Wizard
nextStep=Nästa steg
#XBUT: Schedule Data Deletion Wizard
createSchedule=Skapa schema
#XBUT: Schedule Data Deletion Wizard
updateSchedule=Uppdatera schema
#XGRP: Schedule Data Deletion Wizard's 1st step
settingStep=Inställningar
#XGRP: Schedule Data Deletion Wizard's 2nd step
scheduleStep=Schema
#XGRP: Schedule Data Deletion Wizard's 3rd step
reviewStep=Granska
#XBUT: Schedule Data Deletion Wizard
cancel=Avbryt
#XBUT: Schedule Data Deletion Wizard
edit=Redigera
#XFLD: Schedule Data Deletion Wizard
deleteAllRecordsFilteredBy=Radera alla poster filtrerade efter
#XFLD: Schedule Data Deletion Wizard
recurrence=Upprepning
#XFLD: Schedule Data Deletion Wizard
nextRun=Nästa körning
#XMSG: Error state for empty technical name
emptyLabel=Ange ett giltigt schemanamn.
#XMSG: Error state for duplicate technical name
duplicateID=Schema med samma namn finns redan.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Filtered Deletions ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Create Filtered Deletion button
createFilteredDeletionBtn=Skapa filter

#XFLD: Filtered Deletions Operators
EQ=Lika med
BT=Mellan
Contains=Innehåller
GT=Större än
GE=Större än eller lika med
LT=Mindre än
LE=Mindre än eller lika med
StartsWith=Börjar med
EndsWith=Slutar med
Empty=Tom
BXD=Före {0} dagar


#XFLD: Placeholder for filter input
txtDatePlaceholder=ÅÅÅÅ-MM-DD
txtTimePlaceholder=HH:mm:ss
txtDateTimePlaceholder=ÅÅÅÅ-MM-DD HH:mm:ss
txtStringPlaceholder=Ange en sträng
txtNumberPlaceholder=Ange ett tal
txtValuePlaceholder=Ange ett värde

#XMSG-Validation messages
txtEmptyFilterValue=Ange ett giltigt filtervärde.
#XMSG: Error message for empty  value
txtEmptyValueForDDR=Ange värde i dagar.

#XMSG: lower bound error text
txtLowerbounderrormsg=Nedre gräns måste vara lägre än övre gräns.
#XMSG: lower bound error text for string
txtLowerbounderrormsgforString=Sträng "{0}" är större än "{1}".
#XMSG: higher bound error text
txtHigherbounderrormsg=Övre gräns måste vara högre än nedre gräns.
#XMSG: higher bound error text for string
txtHigherbounderrormsgforString=Sträng "{1}" är mindre än "{0}".
#XMSG Error msg for missing values
VAL_LENGTH_EXCEED=Längden på värdet får inte överskrida {0}.
VAL_ENTER_VALID_INT=Ange ett giltigt heltal.
VAL_ENTER_VALID_DECIMAL=Ange ett giltigt decimalvärde med precision {0} och skala {1}.
VAL_ENTER_VALID_DECIMAL_VALUE=Ange ett decimalvärde.
VAL_ENTER_VALID_DATE=Ange ett giltigt datum.
VAL_ENTER_VALID_NUMBER=Ange ett giltigt tal.
VAL_DUPLICATE_FILTER=Duplicera filtervärde.
VAL_DEFAULT_RANGE_EXCEED_INT=Ange ett giltigt heltal med ett värde mellan -2147483648 och 2147483647.
VAL_DEFAULT_RANGE_EXCEED_BIGINT=Ange ett giltigt heltal med ett värde mellan -9223372036854775808 och 9223372036854775807.

#XMSG: Incase Current Watermark is not applicable
notApplicableText=Ej tillämpligt

#XTXT: Preview Data to be Deleted
previewDataToBeDeleted=Förhandsgranska data som ska raderas

#XBTN: Close Preview
txtCloseBtn=Stäng


#XBTN: Segmented button for filtered Data
txtFilteredData=Filtrerade data

#XBTN: Segmented button for All Data
txtAllData=Alla data

txtDataViewer=Data Viewer
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ LTF ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XCOL: Buffer Merge Status
txtBufferMergeStatus=Status för buffertsammanslagning

#XCOL: Buffer Last Updated
txtBufferLastUpdated=Buffert senast uppdaterad

#XCOL: Buffer Last Updated By
txtBufferLastUpdatedBy=Buffert senast uppdaterad av

#XCOL: Partitions
txtPartitions=Partitioner

#XCOL: Determine the number of records
txtActiveRecords=Antal aktiva poster

#XCOL: Collect data via Spark (DeltaTable)
txtActiveRecordsFileStorage=Lagring för aktiva poster (MiB)

#XCOL: Calculate data via Spark
txtPreviousVersionsFileStorage=Lagring för föregående versioner (MiB)

#XCOL: Collect the size of all files via Spark
txtTotalTableFileStorage=Total lagring (MiB)

#XCOL: Collect the inbound buffer file size
txtInboundBufferFileSize=Buffertfilstorlek (MiB)

#XCOL: Collect the inbound buffer file count
txtInboundBufferFileCount=Antal buffertfiler

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Viewer dialog ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=Kolumninställningar
#XFLD: Title for Error type
messagesTableType=Typ
#XFLD: Title for Error Message
messagesTableMessage=Meddelande
#XFLD: Title for filter
filteredBy=Filtrering via:
#XTIT: text for values contained in filter
filterContains=innehåller
#XTIT: text for values starting with in filter
filterStartsWith=börjar med
#XTIT: text for values ending with in filter
filterEndsWith=slutar med
#XTIT: Title for search in data preview toolbar
toolbarSearch=Sök
#XBUT: Button to clear filter
clearFilter=Rensa filter
#XBUT: Button to save the operation
ok=OK
#XBUT: Button to restore the data
toolbarRestoreButton=Uppdatera
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=Uppdatera sidan
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=Bearbetar...
#XMSG: Message Confirmation
confirmation=Bekräftelse
#XMSG: Message for refresh successful
refreshSuccess=Uppdatering har slutförts
#XMSG: Message for refresh successful
refreshSuccessful=Uppdaterad
#XMSG: Message for restore successful
restoreSuccessful=Återställning har slutförts
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" kan inte vara tom. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=Sortera stigande
#XTIT: Sort Descending
mdm-sortDescending=Sortera fallande
#XTIT: Filter
mdm-Filter=Filtrera
#XBUT: Button Cancel
mdm-cancel=Avbryt
#XBUT: Button Add
mdm-Add=Lägg till
#XMSG: and inside error message
and=och

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=Inställningsverktyg för filtrering, sortering, radering och tabell har inaktiverats av ej sparade ändringar. Spara ändringarna för att aktivera dem.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=Läs in data
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=Duplicera
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=Radera
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=Lägg till

#XTIT: text to be apended for key columns in p13n dialog e.g. (Key) <Column Name>
mdm-keyText=Nyckel
#XTIT: text to be apended for Not Null columns
mdm-notNullText=Inte null

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=Infoga saknat strängvärde som:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=NULL
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=Tom sträng
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=Saknade strängvärden infogas endast i synliga strängkolumner i nya och redigerade rader. Använd kolumninställningarna för att visa alla relevanta kolumner.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=Verktyg för filtrering, sortering, infogande av saknat strängvärde och tabellinställningar har inaktiverats av ej sparade ändringar. Spara ändringarna för att aktivera dem.
#XFLD: Open SAP HANA Cockpit

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Settings LSA ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
txtHeaderApacheSettings=Applikationsinställningar för Apache Spark
txtMergeSectionTitle=Slå samman
txtUseSpaceDefault=Använd standard för utrymme
txtApplication=Applikation
txtOptimizeSectionTitle=Optimera
txtNewSettings=Definiera ny inställning för tabellen
txtNewSettingsMessageBox=Definiera nya inställningar för uppgiften
txtUseDefault=Använd standard
txtMergeMsgBoxTitle=Slå samman tabell
txtOptimizeMsgBoxTitle=Optimera tabell
#XTIT: sub section header for LSA settings i.e Optimize settings
txtOptimizeSettings=Optimera inställningar
#XTIT: Z - order columns list header
txtZOrderList=Z-ordningskolumner
#XBUT: Button to define z order columns
txtZOrderEdit=Redigera
#XBUT: Button to save the z order columns
txtZOrderDelete=Radera
#XMSG: message for empty z order columns
txtNoZOrderList=Inga inställningar finns
#XMSG: message for empty z order list
txtEmptyZOrderList=Förbättra prestanda genom att definiera Z-ordningskolumner
#XBUT: Button to add z order columns
txtDefineZOrder=Definiera Z-ordningskolumner
#XLBL: Label for z order columns
txtColumnZOrder=Kolumner
#XMSG: error message to inform user about HANA limitation
zOrderLimitationError=Du kan endast välja en Z-ordningskolumn.

#XMSG: message for conflicting task
taskAlreadyRunning=Uppgift i konflikt körs redan för objekt "{0}".

#XMSG: Save spark settings success
saveSparkSettingsSuccess=Applikationsinställningar för Apache Spark har sparats.
