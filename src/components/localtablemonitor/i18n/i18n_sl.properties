
msgDataDeletionStarted=<PERSON>z<PERSON>s podatkov tabele "{0}" je v teku

#XMSG: message to inform table not found in the repository
localTableNotFound=Lokalne tabele {0} ni mogoče najti v odložišču.

#XFLD: Used Disk
txtUsedDisk=Uporabljeni disk (MiB)
txtSizeOnDisk=Velikost na disku (MiB)
#XFLD: Used In-Memory
txtUsedInMemory=Uporabljeno v pomnilniku (MiB)
txtSizeInMemory=Velikost v pomnilniku (MiB)
#XFLD: Number of Records
txtNumberOfRecords=Število zapisov
#XFLD: Delta capture
txtDeltaCapture=Zajemanje delte
#XFLD: In-Memory storage
txtInMemoryStorage=Prostor za shranjevanje v pomnilniku
#XFLD: Technical Name
txtTechnicalName=Tehnično ime
#XFLD: Business Name
txtBusinessName=Poslovno ime
#XFLD: Growth Rate (Last Month)
txtgrowthRate=Rast v 30 dneh
#XFLD: Number of Partitions
txtnumberofpartitions=Število particij
#XFLD: Last Updated
txtlastUpdatedBy=Nazadnje posodobil
#XFLD: Last Updated
txtlastupdated=Nazadnje posodobljeno
#XFLD:delta Capture
txtdeltaCapture=Zajemanje delte
txtYes=Da
txtNo=Ne
txtOn=Dne
txtOff=Izključi
localTablesHeaderTitle=Lokalne tabele ({0})
localTablesLTFHeaderTitle=Lokalne tabele (datoteka) ({0})
txtSearch=Išči
txtRefresh=Osveži
txtNotApplicable=Se ne uporablja
txtObject=Objekt ({0})

#XTOL:for Last updated column header
tolLastUpdated=Prikaže, kdaj so bili podatki nazadnje posodobljeni
#XTOL: for Last updated by column header
tolLastUpdatedBy=Prikaže, kako so bili podatki nazadnje posodobljeni
#XTOL: for Last buffer updated column header
tolBufferLastUpdated=Prikaže, kdaj so bili podatki v vhodnem medpomnilniku nazadnje posodobljeni
#XTOL: for Last buffer updated by column header
tolBufferLastUpdatedBy=Prikaže, kako so bili podatki v vhodnem medpomnilniku nazadnje posodobljeni


#XMSG: message to inform repo Access Error
txtRepoAccessError=Odložišče ni na voljo in določene funkcije so onemogočene.

#XBUT: Label for open in Editor
openInEditorNew=Odpri v graditelju podatkov

#XTOL: Toggle button to open data editor
openDataEditor=Urejevalnik podatkov

#XBUT: Radio button group
txtDeleteAllRecords=Izbris vseh zapisov
txtDeleteFilteredRecords=Izbris filtriranih zapisov
txtRecordsMarkedForDeleted=Izbris zapisov, označenih kot "Izbrisano"

#XTXT: preview
txtPreview=Predogled
#XMSG: Warning message for All Records delete
dataDeleteWarningMsgTxt=Brisanje zapisov lahko vpliva na tokove porabe
#XMSG: Warning popup header
@confirmDataDeletion=Izbriši podatke
#XMSG: Warning popup message
@dataDeletionText=S tem dejanjem boste izbrisali zapise v tabeli, ki lahko vplivajo na tokove porabe. Tega ni mogoče preklicati. Ali želite nadaljevati?

#XBUT: Data Deletion warning
btnDeleteConfirm=Izbriši

#XFLD: Label for table column
durationInSeconds=sekunde

#XMSG: Records marked as "Deleted" description
recordsMarkedForDeletionDescription=Izbriši vse v celoti obdelane zapise z vrsto spremembe "Izbrisano", ki so starejši od
daysForDeltaTableDeletion=dni
txtCurrentWatermark=Trenutni vodni žig

#XBUT: show preview button in filter
datViewerBtn=Odpri pregledovalnik podatkov

#XFLD: Number of deletable records
txtNumberOfDeletableRecords=Število zapisov, ki jih je mogoče izbrisati

#XFLD: Size of deletable records
txtSizeOfDeletableRecords=Velikost zapisov, ki jih je mogoče izbrisati (MiB)

numberOfRecordsRefresh=Osveži

#XTXT: Data Deletion Logs
txtDataDeletionLogs=Zapisniki o brisanju podatkov

#XTXT: Data Deletion Schedules
txtDataDeletionSchedules=Časovni načrt brisanja podatkov

#XTXT: Data Deletion
txtDataDeletion=Brisanje podatkov

#XBTN: Delete Records
deleteRecords=Izbriši zapise
#XBTN: Create
createbtn=Ustvari
#XBTN: Edit
editBtn=Uredi
#XBTN: Delete
deleteBtn=Izbriši
#XTXT: Placeholder for search bar
txtSearchPlaceholder=Išči

#XTXT: Data Deletion
txtDeletionSettings=Nastavitve izbrisa

currentWatermark=Trenutni vodni žig

#XBUT: Select Columns Button
selectColumnsBtn=Izbira stolpcev

#XBUT: Merge table for ltf table
txtMergeTable=Združevanje tabele

#XBUT: Optimize table for ltf table
txtOptimizeTable=Optimiziranje tabele

#XBUT: schedule Merge and Optimize for ltf table
scheduleTextLabel=Časovni razpored

#XBUT: Drop down menu button to create a schedule
createScheduleNewLabel=Ustvari časovni razpored
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Uredi časovni razpored
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Izbriši časovni razpored
#XBUT: Assign schedule menu button label
assignScheduleLabel=Dodeli časovni načrt meni
#XBUT: Pause schedule menu label
pauseScheduleLabel=Začasna prekinitev časovnega razporeda
#XBUT: Resume schedule menu label
resumeScheduleLabel=Nadaljevanje časovnega razporeda

#XMSG: message to say that merge task is started
msgMergeTaskStarted=Združevanje naloge, ki je bila začeta za tabelo "{0}"

#XMSG: message to say that optimize task is started
msgOptimizeTaskStarted=Optimiziranje naloge, ki je bila začeta za tabelo "{0}"

#XTXT: title for object page for logs
hdlfLogsObjectPageTxt=Zapisniki

#XTXT: title for object page for settings
hdlfSettingsObjectPageTxt=Nastavitve
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Delete Logs Table~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Label for Data delete logs table
txtStartColumn=Začetek
txtStartColumnTooltip=Začetek
#XFLD: Label for Data delete logs table
txtDurationColumn=Trajanje
txtDurationColumnTooltip=Trajanje
#XFLD: Label for Data delete logs table
txtObjectTypeColumn=Vrsta objekta
txtObjectTypeColumnTooltip=Vrsta objekta
#XFLD: Label for Data delete logs table
txtActivityColumn=Dejavnost
txtActivityColumnTooltip=Dejavnost
#XFLD: Label for Data delete logs table
txtUserColumn=Uporabnik
txtUserColumnTooltip=Uporabnik
txtRunStartedBy=Uporabnik, ki je zagnal izvajanje
txtManualRun=Ročno
txtScheduledRun=Načrtovano
#XFLD: Label for Data delete logs table
txtNumberOfRecordsColumn=Število zapisov
txtNumberOfRecordsColumnTooltip=Število zapisov
#XFLD: Label for Data delete logs table
txtStatusColumn=Status
txtStatusColumnTooltip=Status

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Delete Logs Filter Dialog ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Zadnja ura
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Zadnjih 24 ur
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Prejšnji mesec

#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Več kot 5 minut
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Več kot 15 minut
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Več kot 1 uro

#XFLD: status text
COMPLETED=Dokončano
#XFLD: status text
FAILED=Ni uspelo
#XFLD: status text
RUNNING=Se izvaja

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Deletion Schedules Table~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XCOL: Type of Data deletion schedule
deletionScheduleType=Vrsta
#XCOL: Name of Data deletion schedule
deletionScheduleName=Ime
#XCOL: Frequency of schedule
deletionScheduleFrequency=Pogostost
#XCOL: DateTime of when the last run started
deletionScheduleLastRunStart=Začetek zadnjega izvajanja
#XCOL: DateTime of when the last run ended
deletionScheduleLastRunEnd=Konec zadnjega izvajanja
#XCOL: Number of records deleted in the last scheduled run
deletionScheduleRecordsLastRun=Zapisi (zadnje izvajanje)
#XBUT: To create a data deletion schedule
createDataDeletionScheduleBtn=Ustvari časovni načrt brisanja podatkov
#XMSG: Data Deletion Schedules Table
noDataDeletionScheduleText=Časovni načrt brisanja podatkov še ni bil določen
#XMSG: Data Deletion Schedules Table
noDataDeletionScheduleDescription=Ustvari časovni načrt brisanja podatkov za redno odstranitev podatkov, ki jih ne potrebujete
#XFLD: Frequency if a schedule is paused
paused:Začasno zaustavljeno

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Schedule Data Deletion Wizard ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Title for the schedule data deletion wizard
scheduleDataDeletionTxt=Ustvari časovni načrt brisanja podatkov
#XBUT: Schedule Data Deletion Wizard
back=Nazaj
#XBUT: Schedule Data Deletion Wizard
nextStep=Naslednji korak
#XBUT: Schedule Data Deletion Wizard
createSchedule=Ustvari časovni razpored
#XBUT: Schedule Data Deletion Wizard
updateSchedule=Posodobitev plana
#XGRP: Schedule Data Deletion Wizard's 1st step
settingStep=Nastavitve
#XGRP: Schedule Data Deletion Wizard's 2nd step
scheduleStep=Časovni razpored
#XGRP: Schedule Data Deletion Wizard's 3rd step
reviewStep=Pregled
#XBUT: Schedule Data Deletion Wizard
cancel=Prekliči
#XBUT: Schedule Data Deletion Wizard
edit=Uredi
#XFLD: Schedule Data Deletion Wizard
deleteAllRecordsFilteredBy=Izbriši vse zapise, filtrirane po
#XFLD: Schedule Data Deletion Wizard
recurrence=Ponovitev
#XFLD: Schedule Data Deletion Wizard
nextRun=Naslednje izvajanje
#XMSG: Error state for empty technical name
emptyLabel=Vnesite veljavno ime razporeda.
#XMSG: Error state for duplicate technical name
duplicateID=Časovni načrt z istim imenom že obstaja.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Filtered Deletions ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Create Filtered Deletion button
createFilteredDeletionBtn=Ustvarjenje filtra

#XFLD: Filtered Deletions Operators
EQ=Je enako
BT=Med
Contains=Vsebuje
GT=Večje od
GE=Večje od ali enako
LT=Manjše od
LE=Manjše od ali enako
StartsWith=Se začne z
EndsWith=Se konča z
Empty=Prazno
BXD=Pred {0} dnevi


#XFLD: Placeholder for filter input
txtDatePlaceholder=LLLL-MM-DD
txtTimePlaceholder=UU:mm:ss
txtDateTimePlaceholder=LLLL-MM-DD UU:mm:ss
txtStringPlaceholder=Vnesite niz
txtNumberPlaceholder=Vnesite številko
txtValuePlaceholder=Vnesite vrednost

#XMSG-Validation messages
txtEmptyFilterValue=Vnesite veljavno vrednost filtra.
#XMSG: Error message for empty  value
txtEmptyValueForDDR=Navedite vrednost v dnevih.

#XMSG: lower bound error text
txtLowerbounderrormsg=Spodnja meja mora biti nižja od zgornje meje.
#XMSG: lower bound error text for string
txtLowerbounderrormsgforString=Niz "{0}" je daljši od "{1}".
#XMSG: higher bound error text
txtHigherbounderrormsg=Zgornja meja mora biti višja od spodnje meje.
#XMSG: higher bound error text for string
txtHigherbounderrormsgforString=Niz "{1}" je krajši od "{0}".
#XMSG Error msg for missing values
VAL_LENGTH_EXCEED=Dolžina vrednosti ne sme preseči {0}.
VAL_ENTER_VALID_INT=Vnesite veljavno celo število.
VAL_ENTER_VALID_DECIMAL=Vnesite veljavno decimalno vrednost z natančnostjo {0} in merilom {1}.
VAL_ENTER_VALID_DECIMAL_VALUE=Vnesite decimalno vrednost.
VAL_ENTER_VALID_DATE=Vnesite veljaven datum.
VAL_ENTER_VALID_NUMBER=Vnesite veljavno število.
VAL_DUPLICATE_FILTER=Podvojite vrednost filtra.
VAL_DEFAULT_RANGE_EXCEED_INT=Vnesite veljavno celo število z vrednostjo med -2147483648 in 2147483647.
VAL_DEFAULT_RANGE_EXCEED_BIGINT=Vnesite veljavno celo število z vrednostjo med -9223372036854775808 in 9223372036854775807.

#XMSG: Incase Current Watermark is not applicable
notApplicableText=Se ne uporablja

#XTXT: Preview Data to be Deleted
previewDataToBeDeleted=Predogled podatkov za izbris

#XBTN: Close Preview
txtCloseBtn=Zapri


#XBTN: Segmented button for filtered Data
txtFilteredData=Filtrirani podatki

#XBTN: Segmented button for All Data
txtAllData=Vsi podatki

txtDataViewer=Pogledovalnik podatkov
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ LTF ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XCOL: Buffer Merge Status
txtBufferMergeStatus=Status združevanja medpomnilnika

#XCOL: Buffer Last Updated
txtBufferLastUpdated=Zadnja posodobitev medpomnilnika

#XCOL: Buffer Last Updated By
txtBufferLastUpdatedBy=Medpomnilnik nazadnje posodobil uporabnik

#XCOL: Partitions
txtPartitions=Particije

#XCOL: Determine the number of records
txtActiveRecords=Število aktivnih zapisov

#XCOL: Collect data via Spark (DeltaTable)
txtActiveRecordsFileStorage=Shramba aktivnih zapisov (MiB)

#XCOL: Calculate data via Spark
txtPreviousVersionsFileStorage=Shramba prejšnjih različic (MiB)

#XCOL: Collect the size of all files via Spark
txtTotalTableFileStorage=Skupna shramba (MiB)

#XCOL: Collect the inbound buffer file size
txtInboundBufferFileSize=Velikost medpomnilniške datoteke (MiB)

#XCOL: Collect the inbound buffer file count
txtInboundBufferFileCount=Število medpomnilniških datotek

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Viewer dialog ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=Nastavitve stolpcev
#XFLD: Title for Error type
messagesTableType=Vrsta
#XFLD: Title for Error Message
messagesTableMessage=Sporočilo
#XFLD: Title for filter
filteredBy=Filtrirano po:
#XTIT: text for values contained in filter
filterContains=vsebuje
#XTIT: text for values starting with in filter
filterStartsWith=se začne z
#XTIT: text for values ending with in filter
filterEndsWith=se konča z
#XTIT: Title for search in data preview toolbar
toolbarSearch=Iskanje
#XBUT: Button to clear filter
clearFilter=Počisti filter
#XBUT: Button to save the operation
ok=V redu
#XBUT: Button to restore the data
toolbarRestoreButton=Osveži
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=Posodobi stran
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=Obdelava poteka ...
#XMSG: Message Confirmation
confirmation=Potrditev
#XMSG: Message for refresh successful
refreshSuccess=Uspešno osveženo
#XMSG: Message for refresh successful
refreshSuccessful=Osveženo
#XMSG: Message for restore successful
restoreSuccessful=Uspešno obnovljeno
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" ne sme biti prazno. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=Naraščajoče razvrščanje
#XTIT: Sort Descending
mdm-sortDescending=Padajoče razvrščanje
#XTIT: Filter
mdm-Filter=Filter
#XBUT: Button Cancel
mdm-cancel=Prekliči
#XBUT: Button Add
mdm-Add=Dodaj
#XMSG: and inside error message
and=in

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=Orodja Filtriranje, Razvrščanje, Izbris in Nastavitev tabele so onemogočena zaradi neshranjenih sprememb. Da jih omogočite, shranite spremembe.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=Prenos podatkov v strežnik
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=Podvoji
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=Izbriši
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=Dodaj

#XTIT: text to be apended for key columns in p13n dialog e.g. (Key) <Column Name>
mdm-keyText=Ključ
#XTIT: text to be apended for Not Null columns
mdm-notNullText=Ni ničelna vrednost

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=Vnesite manjkajočo vrednost niza kot:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=NIČELNA VREDNOST
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=Prazen niz
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=Manjkajoče vrednosti niza se vstavijo samo v vidne stolpce nizov v novih in urejenih vrsticah. Uporabite možnost Nastavitve stolpcev, da prikažete vse ustrezne stolpce.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=Orodja Filtriranje, Razvrščanje, Vstavljanje manjkajoče vrednosti niza in Nastavitev tabele so onemogočena zaradi neshranjenih sprememb. Da jih omogočite, shranite spremembe.
#XFLD: Open SAP HANA Cockpit

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Settings LSA ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
txtHeaderApacheSettings=Nastavitve za aplikacijo Apache Spark
txtMergeSectionTitle=Združitev
txtUseSpaceDefault=Uporaba privzetih nastavitev prostora
txtApplication=Aplikacija
txtOptimizeSectionTitle=Optimiziranje
txtNewSettings=Opredelitev nove nastavitve za to tabelo
txtNewSettingsMessageBox=Opredelitev novih nastavitev za to tabelo
txtUseDefault=Uporabi privzeto
txtMergeMsgBoxTitle=Združevanje tabele
txtOptimizeMsgBoxTitle=Optimiziranje tabele
#XTIT: sub section header for LSA settings i.e Optimize settings
txtOptimizeSettings=Optimizacija nastavitev
#XTIT: Z - order columns list header
txtZOrderList=Stolpci z razvrščanjem po osi Z
#XBUT: Button to define z order columns
txtZOrderEdit=Uredi
#XBUT: Button to save the z order columns
txtZOrderDelete=Izbriši
#XMSG: message for empty z order columns
txtNoZOrderList=Nastavitve ne obstajajo
#XMSG: message for empty z order list
txtEmptyZOrderList=Izboljšanje delovanja z določitvijo stolpcev z razvrščanjem po osi Z
#XBUT: Button to add z order columns
txtDefineZOrder=Določitev stolpcev z razvrščanjem po osi Z
#XLBL: Label for z order columns
txtColumnZOrder=Stolpci
#XMSG: error message to inform user about HANA limitation
zOrderLimitationError=Izberete lahko le en stolpec z razvrščanjem po osi Z.

#XMSG: message for conflicting task
taskAlreadyRunning=Naloga v sporu se že izvaja za objekt "{0}".

#XMSG: Save spark settings success
saveSparkSettingsSuccess=Nastavitve za aplikacijo Apache Spark so shranjene.
