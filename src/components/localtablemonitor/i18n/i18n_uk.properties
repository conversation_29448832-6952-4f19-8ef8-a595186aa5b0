
msgDataDeletionStarted=Триває видалення даних таблиці "{0}"

#XMSG: message to inform table not found in the repository
localTableNotFound=Не вдається знайти в репозиторії локальну таблицю "{0}".

#XFLD: Used Disk
txtUsedDisk=Використано на диску (МіБ)
txtSizeOnDisk=Розмір на диску (МіБ)
#XFLD: Used In-Memory
txtUsedInMemory=Використано в пам’яті (МіБ)
txtSizeInMemory=Розмір у пам'яті (МіБ)
#XFLD: Number of Records
txtNumberOfRecords=Кількість записів
#XFLD: Delta capture
txtDeltaCapture=Дельта-захоплення
#XFLD: In-Memory storage
txtInMemoryStorage=Зберігання в пам'яті
#XFLD: Technical Name
txtTechnicalName=Технічне ім'я
#XFLD: Business Name
txtBusinessName=Бізнес-ім'я
#XFLD: Growth Rate (Last Month)
txtgrowthRate=Збільшення за 30 днів
#XFLD: Number of Partitions
txtnumberofpartitions=Кількість розділів
#XFLD: Last Updated
txtlastUpdatedBy=Автор останнього оновлення
#XFLD: Last Updated
txtlastupdated=Останнє оновлення
#XFLD:delta Capture
txtdeltaCapture=Дельта-захоплення
txtYes=Так
txtNo=Ні
txtOn=Увімк.
txtOff=Вимк.
localTablesHeaderTitle=Локальні таблиці ({0})
localTablesLTFHeaderTitle=Локальні таблиці (файл) ({0})
txtSearch=Пошук
txtRefresh=Оновити
txtNotApplicable=Не застосовується
txtObject=Об''єкт ({0})

#XTOL:for Last updated column header
tolLastUpdated=Показує, коли востаннє було оновлено дані
#XTOL: for Last updated by column header
tolLastUpdatedBy=Показує, як востаннє було оновлено дані
#XTOL: for Last buffer updated column header
tolBufferLastUpdated=Показує, коли востаннє було оновлено дані у вхідному буфері
#XTOL: for Last buffer updated by column header
tolBufferLastUpdatedBy=Показує, як востаннє було оновлено дані у вхідному буфері


#XMSG: message to inform repo Access Error
txtRepoAccessError=Репозиторій недоступний, і певні функції вимкнуто.

#XBUT: Label for open in Editor
openInEditorNew=Відкрити в Конструкторі моделей даних

#XTOL: Toggle button to open data editor
openDataEditor=Редактор даних

#XBUT: Radio button group
txtDeleteAllRecords=Видалити всі записи
txtDeleteFilteredRecords=Видалити фільтровані записи
txtRecordsMarkedForDeleted=Видалити записи, позначені як "Видалено"

#XTXT: preview
txtPreview=Попередній перегляд
#XMSG: Warning message for All Records delete
dataDeleteWarningMsgTxt=Видалення записів може вплинути на потоки споживання.
#XMSG: Warning popup header
@confirmDataDeletion=Видалити дані
#XMSG: Warning popup message
@dataDeletionText=Ця дія призведе до видалення записів таблиці, що може вплинути на споживчі потоки. Її скасувати не можна. Продовжити?

#XBUT: Data Deletion warning
btnDeleteConfirm=Видалити

#XFLD: Label for table column
durationInSeconds=с

#XMSG: Records marked as "Deleted" description
recordsMarkedForDeletionDescription=Видалення всіх повністю оброблених записів із типом зміни "Видалено", що існують довше ніж
daysForDeltaTableDeletion=дн.
txtCurrentWatermark=Поточний водяний знак

#XBUT: show preview button in filter
datViewerBtn=Відкрити переглядач даних

#XFLD: Number of deletable records
txtNumberOfDeletableRecords=Кількість записів для видалення

#XFLD: Size of deletable records
txtSizeOfDeletableRecords=Розмір записів для видалення (МіБ)

numberOfRecordsRefresh=Оновити

#XTXT: Data Deletion Logs
txtDataDeletionLogs=Журнали видалення даних

#XTXT: Data Deletion Schedules
txtDataDeletionSchedules=Розклади видалення даних

#XTXT: Data Deletion
txtDataDeletion=Видалення даних

#XBTN: Delete Records
deleteRecords=Видалити записи
#XBTN: Create
createbtn=Створити
#XBTN: Edit
editBtn=Редагувати
#XBTN: Delete
deleteBtn=Видалити
#XTXT: Placeholder for search bar
txtSearchPlaceholder=Пошук

#XTXT: Data Deletion
txtDeletionSettings=Настройки видалення

currentWatermark=Поточний водяний знак

#XBUT: Select Columns Button
selectColumnsBtn=Вибрати стовпчики

#XBUT: Merge table for ltf table
txtMergeTable=Об'єднати таблицю

#XBUT: Optimize table for ltf table
txtOptimizeTable=Оптимізувати таблицю

#XBUT: schedule Merge and Optimize for ltf table
scheduleTextLabel=Розклад

#XBUT: Drop down menu button to create a schedule
createScheduleNewLabel=Створити розклад
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Змінити розклад
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Видалити розклад
#XBUT: Assign schedule menu button label
assignScheduleLabel=Призначити мені розклад
#XBUT: Pause schedule menu label
pauseScheduleLabel=Призупинити розклад
#XBUT: Resume schedule menu label
resumeScheduleLabel=Відновити розклад

#XMSG: message to say that merge task is started
msgMergeTaskStarted=Об''єднати завдання, розпочате для таблиці "{0}"

#XMSG: message to say that optimize task is started
msgOptimizeTaskStarted=Оптимізувати завдання, розпочате для таблиці "{0}"

#XTXT: title for object page for logs
hdlfLogsObjectPageTxt=Журнали

#XTXT: title for object page for settings
hdlfSettingsObjectPageTxt=Настройки
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Delete Logs Table~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Label for Data delete logs table
txtStartColumn=Початок
txtStartColumnTooltip=Початок
#XFLD: Label for Data delete logs table
txtDurationColumn=Тривалість
txtDurationColumnTooltip=Тривалість
#XFLD: Label for Data delete logs table
txtObjectTypeColumn=Тип об'єкта
txtObjectTypeColumnTooltip=Тип об'єкта
#XFLD: Label for Data delete logs table
txtActivityColumn=Операція
txtActivityColumnTooltip=Операція
#XFLD: Label for Data delete logs table
txtUserColumn=Користувач
txtUserColumnTooltip=Користувач
txtRunStartedBy=Ініціатор запуску прогону
txtManualRun=Вручну
txtScheduledRun=За розкладом
#XFLD: Label for Data delete logs table
txtNumberOfRecordsColumn=Кількість записів
txtNumberOfRecordsColumnTooltip=Кількість записів
#XFLD: Label for Data delete logs table
txtStatusColumn=Статус
txtStatusColumnTooltip=Статус

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Delete Logs Filter Dialog ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Остання година
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Останні 24 години
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Останній місяць

#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Понад 5 хвилин
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Понад 15 хвилин
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Понад 1 годину

#XFLD: status text
COMPLETED=Завершено
#XFLD: status text
FAILED=Помилка
#XFLD: status text
RUNNING=Виконується

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Deletion Schedules Table~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XCOL: Type of Data deletion schedule
deletionScheduleType=Тип
#XCOL: Name of Data deletion schedule
deletionScheduleName=Ім'я
#XCOL: Frequency of schedule
deletionScheduleFrequency=Частота
#XCOL: DateTime of when the last run started
deletionScheduleLastRunStart=Початок останнього прогону
#XCOL: DateTime of when the last run ended
deletionScheduleLastRunEnd=Завершення останнього прогону
#XCOL: Number of records deleted in the last scheduled run
deletionScheduleRecordsLastRun=Кількість записів (останній прогін)
#XBUT: To create a data deletion schedule
createDataDeletionScheduleBtn=Створити розклад видалення даних
#XMSG: Data Deletion Schedules Table
noDataDeletionScheduleText=Ще не визначено жодного розкладу видалення даних
#XMSG: Data Deletion Schedules Table
noDataDeletionScheduleDescription=Створіть розклади видалення даних, щоб регулярно вилучати непотрібні дані
#XFLD: Frequency if a schedule is paused
paused:Призупинено

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Schedule Data Deletion Wizard ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Title for the schedule data deletion wizard
scheduleDataDeletionTxt=Планування видалення даних
#XBUT: Schedule Data Deletion Wizard
back=Назад
#XBUT: Schedule Data Deletion Wizard
nextStep=Наступний крок
#XBUT: Schedule Data Deletion Wizard
createSchedule=Створити розклад
#XBUT: Schedule Data Deletion Wizard
updateSchedule=Оновити розклад
#XGRP: Schedule Data Deletion Wizard's 1st step
settingStep=Настройки
#XGRP: Schedule Data Deletion Wizard's 2nd step
scheduleStep=Розклад
#XGRP: Schedule Data Deletion Wizard's 3rd step
reviewStep=Перевірка
#XBUT: Schedule Data Deletion Wizard
cancel=Скасувати
#XBUT: Schedule Data Deletion Wizard
edit=Редагувати
#XFLD: Schedule Data Deletion Wizard
deleteAllRecordsFilteredBy=Видалити всі записи, відфільтровані за
#XFLD: Schedule Data Deletion Wizard
recurrence=Повторення
#XFLD: Schedule Data Deletion Wizard
nextRun=Наступний прогін
#XMSG: Error state for empty technical name
emptyLabel=Введіть дійсне ім'я розкладу.
#XMSG: Error state for duplicate technical name
duplicateID=Розклад із таким іменем уже існує.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Filtered Deletions ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Create Filtered Deletion button
createFilteredDeletionBtn=Створити фільтр

#XFLD: Filtered Deletions Operators
EQ=Дорівнює
BT=Між
Contains=Містить
GT=Більше ніж
GE=Більше або дорівнює
LT=Менше ніж
LE=Менше або дорівнює
StartsWith=Починається з
EndsWith=Завершується на
Empty=Порожнє
BXD=За {0} дн.


#XFLD: Placeholder for filter input
txtDatePlaceholder=РРРР-ММ-ДД
txtTimePlaceholder=ГГ:хх:сс
txtDateTimePlaceholder=РРРР-ММ-ДД, ГГ:хх:сс
txtStringPlaceholder=Введіть рядок
txtNumberPlaceholder=Введіть число
txtValuePlaceholder=Введіть значення

#XMSG-Validation messages
txtEmptyFilterValue=Введіть дійсне значення фільтра.
#XMSG: Error message for empty  value
txtEmptyValueForDDR=Надає значення в днях.

#XMSG: lower bound error text
txtLowerbounderrormsg=Нижня межа має бути нижчою за верхню.
#XMSG: lower bound error text for string
txtLowerbounderrormsgforString=Рядок "{0}" більший ніж "{1}".
#XMSG: higher bound error text
txtHigherbounderrormsg=Верхня межа має бути вищою за нижню.
#XMSG: higher bound error text for string
txtHigherbounderrormsgforString=Рядок "{1}" менший ніж "{0}".
#XMSG Error msg for missing values
VAL_LENGTH_EXCEED=Довжина значення не має перевищувати {0}.
VAL_ENTER_VALID_INT=Введіть дійсне ціле число.
VAL_ENTER_VALID_DECIMAL=Введіть дійсне десяткове значення з точністю {0} та масштабом {1}.
VAL_ENTER_VALID_DECIMAL_VALUE=Введіть десяткове значення.
VAL_ENTER_VALID_DATE=Введіть дійсну дату.
VAL_ENTER_VALID_NUMBER=Введіть дійсне число.
VAL_DUPLICATE_FILTER=Дублюйте значення фільтра.
VAL_DEFAULT_RANGE_EXCEED_INT=Введіть дійсне ціле число зі значенням від -2147483648 до 2147483647.
VAL_DEFAULT_RANGE_EXCEED_BIGINT=Введіть дійсне ціле число зі значенням від -9223372036854775808 до 9223372036854775807.

#XMSG: Incase Current Watermark is not applicable
notApplicableText=Не застосовується

#XTXT: Preview Data to be Deleted
previewDataToBeDeleted=Попередній перегляд даних для видалення

#XBTN: Close Preview
txtCloseBtn=Закрити


#XBTN: Segmented button for filtered Data
txtFilteredData=Фільтровані дані

#XBTN: Segmented button for All Data
txtAllData=Усі дані

txtDataViewer=Переглядач даних
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ LTF ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XCOL: Buffer Merge Status
txtBufferMergeStatus=Статус об'єднання буферів

#XCOL: Buffer Last Updated
txtBufferLastUpdated=Буфер востаннє оновлено

#XCOL: Buffer Last Updated By
txtBufferLastUpdatedBy=Ініціатор останнього оновлення буфера

#XCOL: Partitions
txtPartitions=Розділи

#XCOL: Determine the number of records
txtActiveRecords=Кількість активних записів

#XCOL: Collect data via Spark (DeltaTable)
txtActiveRecordsFileStorage=Сховище активних записів (МіБ)

#XCOL: Calculate data via Spark
txtPreviousVersionsFileStorage=Сховище попередніх версій (МіБ)

#XCOL: Collect the size of all files via Spark
txtTotalTableFileStorage=Загальний обсяг сховища (МіБ)

#XCOL: Collect the inbound buffer file size
txtInboundBufferFileSize=Розмір файлу буфера (МіБ)

#XCOL: Collect the inbound buffer file count
txtInboundBufferFileCount=Кількість файлів буфера

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Viewer dialog ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=Настройки стовпчиків
#XFLD: Title for Error type
messagesTableType=Тип
#XFLD: Title for Error Message
messagesTableMessage=Повідомлення
#XFLD: Title for filter
filteredBy=Відфільтровано за:
#XTIT: text for values contained in filter
filterContains=містить
#XTIT: text for values starting with in filter
filterStartsWith=починається з
#XTIT: text for values ending with in filter
filterEndsWith=закінчується на
#XTIT: Title for search in data preview toolbar
toolbarSearch=Пошук
#XBUT: Button to clear filter
clearFilter=Очистити фільтр
#XBUT: Button to save the operation
ok=OK
#XBUT: Button to restore the data
toolbarRestoreButton=Оновити
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=Оновити сторінку
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=Обробляється…
#XMSG: Message Confirmation
confirmation=Підтвердження
#XMSG: Message for refresh successful
refreshSuccess=Успішно оновлено
#XMSG: Message for refresh successful
refreshSuccessful=Оновлено
#XMSG: Message for restore successful
restoreSuccessful=Успішно відновлено
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" не може бути порожнім. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=Сортувати за зростанням
#XTIT: Sort Descending
mdm-sortDescending=Сортувати за спаданням
#XTIT: Filter
mdm-Filter=Фільтр
#XBUT: Button Cancel
mdm-cancel=Скасувати
#XBUT: Button Add
mdm-Add=Додати
#XMSG: and inside error message
and=і

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=Через незбережені зміни вимкнуто інструменти "Фільтр", "Сортування", "Видалити" та "Настройка таблиці". Збережіть зміни, щоб їх увімкнути.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=Вивантажити дані
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=Дублювати
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=Видалити
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=Додати

#XTIT: text to be apended for key columns in p13n dialog e.g. (Key) <Column Name>
mdm-keyText=Ключ
#XTIT: text to be apended for Not Null columns
mdm-notNullText=Не Null

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=Вставити відсутнє значення рядка як:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=NULL
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=Порожній рядок
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=Відсутні рядкові значення можна вставити лише у видимі рядкові стовпчики в нових і доступних для редагування рядках. Скористайтеся настройками стовпчиків, щоб відобразити всі потрібні стовпчики.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=Через незбережені зміни вимкнуто інструменти "Фільтр", "Сортування", "Вставити відсутнє рядкове значення" та "Настройка таблиці". Збережіть зміни, щоб їх увімкнути.
#XFLD: Open SAP HANA Cockpit

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Settings LSA ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
txtHeaderApacheSettings=Настройки застосунку Apache Spark
txtMergeSectionTitle=Об'єднати
txtUseSpaceDefault=Використовувати усталені настройки простору
txtApplication=Застосунок
txtOptimizeSectionTitle=Оптимізувати
txtNewSettings=Визначити нову настройку для цієї таблиці
txtNewSettingsMessageBox=Визначити нові настройки для цього завдання
txtUseDefault=Використовувати усталені
txtMergeMsgBoxTitle=Об'єднати таблицю
txtOptimizeMsgBoxTitle=Оптимізувати таблицю
#XTIT: sub section header for LSA settings i.e Optimize settings
txtOptimizeSettings=Оптимізувати настройки
#XTIT: Z - order columns list header
txtZOrderList=Стовпчики Z-упорядкування
#XBUT: Button to define z order columns
txtZOrderEdit=Редагувати
#XBUT: Button to save the z order columns
txtZOrderDelete=Видалити
#XMSG: message for empty z order columns
txtNoZOrderList=Настройок не існує
#XMSG: message for empty z order list
txtEmptyZOrderList=Покращте продуктивність, визначивши стовпчик Z-упорядкування
#XBUT: Button to add z order columns
txtDefineZOrder=Визначити стовпчики Z-упорядкування
#XLBL: Label for z order columns
txtColumnZOrder=Стовпчики
#XMSG: error message to inform user about HANA limitation
zOrderLimitationError=Можна вибрати лише один стовпчик Z-упорядкування.

#XMSG: message for conflicting task
taskAlreadyRunning=Конфліктне завдання вже виконується для об''єкта "{0}".

#XMSG: Save spark settings success
saveSparkSettingsSuccess=Настройки застосунку Apache Spark збережено.
