
msgDataDeletionStarted=Odstraňujeme údaje tabuľky "{0}"

#XMSG: message to inform table not found in the repository
localTableNotFound=Lokálnu tabuľku {0} nemožno nájsť v úložisku.

#XFLD: Used Disk
txtUsedDisk=Využitý disk (MiB)
txtSizeOnDisk=Veľkosť na disku (MiB)
#XFLD: Used In-Memory
txtUsedInMemory=Využitá vnútorná pamäť (MiB)
txtSizeInMemory=Vnútorná pamäť (MiB)
#XFLD: Number of Records
txtNumberOfRecords=Počet záznamov
#XFLD: Delta capture
txtDeltaCapture=Delta Capture
#XFLD: In-Memory storage
txtInMemoryStorage=Ukladací priestor in-memory
#XFLD: Technical Name
txtTechnicalName=Technický názov
#XFLD: Business Name
txtBusinessName=Podnikový názov
#XFLD: Growth Rate (Last Month)
txtgrowthRate=Rast za 30 dní
#XFLD: Number of Partitions
txtnumberofpartitions=Počet segmentov
#XFLD: Last Updated
txtlastUpdatedBy=Naposledy aktualizoval
#XFLD: Last Updated
txtlastupdated=Posledná aktualizácia
#XFLD:delta Capture
txtdeltaCapture=Delta Capture
txtYes=Áno
txtNo=Nie
txtOn=Zapnuté
txtOff=Vypnuté
localTablesHeaderTitle=Lokálne tabuľky ({0})
localTablesLTFHeaderTitle=Lokálne tabuľky (súbor) ({0})
txtSearch=Hľadať
txtRefresh=Obnoviť
txtNotApplicable=Nerelevantné
txtObject=Objekt ({0})

#XTOL:for Last updated column header
tolLastUpdated=Zobrazuje, kedy boli údaje naposledy aktualizované
#XTOL: for Last updated by column header
tolLastUpdatedBy=Zobrazuje, akým spôsobom boli údaje naposledy aktualizované
#XTOL: for Last buffer updated column header
tolBufferLastUpdated=Zobrazuje, kedy boli údaje vo vstupnom bufferi naposledy aktualizované
#XTOL: for Last buffer updated by column header
tolBufferLastUpdatedBy=Zobrazuje, ako boli údaje vo vstupnom bufferi naposledy aktualizované


#XMSG: message to inform repo Access Error
txtRepoAccessError=Úložisko nie je k dispozícii a niektoré funkcie sú vypnuté.

#XBUT: Label for open in Editor
openInEditorNew=Otvoriť v zostavovači údajov

#XTOL: Toggle button to open data editor
openDataEditor=Editor údajov

#XBUT: Radio button group
txtDeleteAllRecords=Odstrániť všetky záznamy
txtDeleteFilteredRecords=Odstrániť filtrované záznamy
txtRecordsMarkedForDeleted=Odstrániť záznamy označené ako "Odstránené"

#XTXT: preview
txtPreview=Ukážka
#XMSG: Warning message for All Records delete
dataDeleteWarningMsgTxt=Odstránenie záznamov môže ovplyvniť toky spotreby.
#XMSG: Warning popup header
@confirmDataDeletion=Odstrániť údaje
#XMSG: Warning popup message
@dataDeletionText=Táto akcia vymaže záznamy tabuľky, čo môže ovplyvniť náročné toky. Toto sa nedá spätne zrušiť. Chcete pokračovať?

#XBUT: Data Deletion warning
btnDeleteConfirm=Odstrániť

#XFLD: Label for table column
durationInSeconds=Sekundy

#XMSG: Records marked as "Deleted" description
recordsMarkedForDeletionDescription=Odstráňte všetky úplne spracované záznamy s typom zmeny „Odstránené“, ktoré sú staršie ako
daysForDeltaTableDeletion=Dni
txtCurrentWatermark=Aktuálny vodoznak

#XBUT: show preview button in filter
datViewerBtn=Otvoriť zobrazovač údajov

#XFLD: Number of deletable records
txtNumberOfDeletableRecords=Počet odstrániteľných záznamov

#XFLD: Size of deletable records
txtSizeOfDeletableRecords=Veľkosť odstrániteľných záznamov (MiB)

numberOfRecordsRefresh=Obnoviť

#XTXT: Data Deletion Logs
txtDataDeletionLogs=Protokoly odstránenia údajov

#XTXT: Data Deletion Schedules
txtDataDeletionSchedules=Plány odstránenia údajov

#XTXT: Data Deletion
txtDataDeletion=Odstránenie údajov

#XBTN: Delete Records
deleteRecords=Odstrániť záznamy
#XBTN: Create
createbtn=Vytvoriť
#XBTN: Edit
editBtn=Upraviť
#XBTN: Delete
deleteBtn=Odstrániť
#XTXT: Placeholder for search bar
txtSearchPlaceholder=Hľadať

#XTXT: Data Deletion
txtDeletionSettings=Nastavenia odstránenia

currentWatermark=Aktuálny vodoznak

#XBUT: Select Columns Button
selectColumnsBtn=Vybrať stĺpce

#XBUT: Merge table for ltf table
txtMergeTable=Zlúčiť tabuľku

#XBUT: Optimize table for ltf table
txtOptimizeTable=Optimalizovať tabuľku

#XBUT: schedule Merge and Optimize for ltf table
scheduleTextLabel=Plán

#XBUT: Drop down menu button to create a schedule
createScheduleNewLabel=Vytvoriť plán
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Upraviť plán
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Odstrániť plán
#XBUT: Assign schedule menu button label
assignScheduleLabel=Priradiť plán mne
#XBUT: Pause schedule menu label
pauseScheduleLabel=Pozastaviť plán
#XBUT: Resume schedule menu label
resumeScheduleLabel=Obnoviť plán

#XMSG: message to say that merge task is started
msgMergeTaskStarted=Úloha zlúčenia pre tabuľku "{0}" bola spustená

#XMSG: message to say that optimize task is started
msgOptimizeTaskStarted=Úloha optimalizácie pre tabuľku "{0}" bola spustená

#XTXT: title for object page for logs
hdlfLogsObjectPageTxt=Protokoly

#XTXT: title for object page for settings
hdlfSettingsObjectPageTxt=Nastavenia
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Delete Logs Table~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Label for Data delete logs table
txtStartColumn=Začiatok
txtStartColumnTooltip=Začiatok
#XFLD: Label for Data delete logs table
txtDurationColumn=Trvanie
txtDurationColumnTooltip=Trvanie
#XFLD: Label for Data delete logs table
txtObjectTypeColumn=Typ objektu
txtObjectTypeColumnTooltip=Typ objektu
#XFLD: Label for Data delete logs table
txtActivityColumn=Aktivita
txtActivityColumnTooltip=Aktivita
#XFLD: Label for Data delete logs table
txtUserColumn=Používateľ
txtUserColumnTooltip=Používateľ
txtRunStartedBy=Chod spustil
txtManualRun=Manuálne
txtScheduledRun=Naplánované
#XFLD: Label for Data delete logs table
txtNumberOfRecordsColumn=Počet záznamov
txtNumberOfRecordsColumnTooltip=Počet záznamov
#XFLD: Label for Data delete logs table
txtStatusColumn=Status
txtStatusColumnTooltip=Status

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Delete Logs Filter Dialog ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Posledná hodina
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Posledných 24 hodín
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Posledný mesiac

#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Viac ako 5 minút
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Viac ako 15 minút
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Viac ako 1 hodina

#XFLD: status text
COMPLETED=Dokončené
#XFLD: status text
FAILED=Neúspešné
#XFLD: status text
RUNNING=Spustené

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Deletion Schedules Table~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XCOL: Type of Data deletion schedule
deletionScheduleType=Typ
#XCOL: Name of Data deletion schedule
deletionScheduleName=Názov
#XCOL: Frequency of schedule
deletionScheduleFrequency=Frekvencia
#XCOL: DateTime of when the last run started
deletionScheduleLastRunStart=Začiatok posledného chodu
#XCOL: DateTime of when the last run ended
deletionScheduleLastRunEnd=Koniec posledného chodu
#XCOL: Number of records deleted in the last scheduled run
deletionScheduleRecordsLastRun=Záznamy (posledný chod)
#XBUT: To create a data deletion schedule
createDataDeletionScheduleBtn=Vytvoriť plán odstránenia údajov
#XMSG: Data Deletion Schedules Table
noDataDeletionScheduleText=Zatiaľ neboli definované žiadne plány odstránenia údajov
#XMSG: Data Deletion Schedules Table
noDataDeletionScheduleDescription=Vytvorte plány odstraňovania údajov na pravidelné odstraňovanie údajov, ktoré nepotrebujete
#XFLD: Frequency if a schedule is paused
paused:Pozastavené

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Schedule Data Deletion Wizard ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Title for the schedule data deletion wizard
scheduleDataDeletionTxt=Naplánovať odstránenie údajov
#XBUT: Schedule Data Deletion Wizard
back=Späť
#XBUT: Schedule Data Deletion Wizard
nextStep=Ďalší krok
#XBUT: Schedule Data Deletion Wizard
createSchedule=Vytvoriť plán
#XBUT: Schedule Data Deletion Wizard
updateSchedule=Aktualizovať plán
#XGRP: Schedule Data Deletion Wizard's 1st step
settingStep=Nastavenia
#XGRP: Schedule Data Deletion Wizard's 2nd step
scheduleStep=Plán
#XGRP: Schedule Data Deletion Wizard's 3rd step
reviewStep=Skontrolovať
#XBUT: Schedule Data Deletion Wizard
cancel=Zrušiť
#XBUT: Schedule Data Deletion Wizard
edit=Upraviť
#XFLD: Schedule Data Deletion Wizard
deleteAllRecordsFilteredBy=Odstrániť všetky záznamy filtrované podľa
#XFLD: Schedule Data Deletion Wizard
recurrence=Opakovanie
#XFLD: Schedule Data Deletion Wizard
nextRun=Ďalší chod
#XMSG: Error state for empty technical name
emptyLabel=Zadajte platný názov plánu.
#XMSG: Error state for duplicate technical name
duplicateID=Plán s rovnakým názvom už existuje.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Filtered Deletions ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Create Filtered Deletion button
createFilteredDeletionBtn=Vytvoriť filter

#XFLD: Filtered Deletions Operators
EQ=Rovná sa
BT=Medzi
Contains=Obsahuje
GT=Väčšie ako 
GE=Väčšie alebo rovné 
LT=Menšie ako
LE=Menšie alebo rovné
StartsWith=Začínajúce sa na
EndsWith=Končí s
Empty=Prázdne
BXD=Pred {0} dňami


#XFLD: Placeholder for filter input
txtDatePlaceholder=RRRR-MM-DD
txtTimePlaceholder=HH:mm:ss
txtDateTimePlaceholder=RRRR-MM-DD HH:mm:ss
txtStringPlaceholder=Zadajte reťazec
txtNumberPlaceholder=Zadajte číslo
txtValuePlaceholder=Zadať hodnotu

#XMSG-Validation messages
txtEmptyFilterValue=Zadajte platnú hodnotu filtra.
#XMSG: Error message for empty  value
txtEmptyValueForDDR=Zadajte hodnotu v dňoch.

#XMSG: lower bound error text
txtLowerbounderrormsg=Dolná hranica musí byť nižšia ako horná hranica.
#XMSG: lower bound error text for string
txtLowerbounderrormsgforString=Reťazec „{0}“ je väčší ako „{1}“.
#XMSG: higher bound error text
txtHigherbounderrormsg=Horná hranica musí byť vyššia ako dolná hranica.
#XMSG: higher bound error text for string
txtHigherbounderrormsgforString=Reťazec „{1}“ je menší ako „{0}“.
#XMSG Error msg for missing values
VAL_LENGTH_EXCEED=Dĺžka hodnoty by nemala prekročiť {0}.
VAL_ENTER_VALID_INT=Zadajte platné celé číslo.
VAL_ENTER_VALID_DECIMAL=Zadajte platnú desatinnú hodnotu s presnosťou {0} a stupnicou {1}.
VAL_ENTER_VALID_DECIMAL_VALUE=Zadajte desatinnú hodnotu.
VAL_ENTER_VALID_DATE=Zadajte platný dátum.
VAL_ENTER_VALID_NUMBER=Zadajte platné číslo.
VAL_DUPLICATE_FILTER=Duplicitná hodnota filtra.
VAL_DEFAULT_RANGE_EXCEED_INT=Zadajte platné celé číslo s hodnotou od -2147483648 do 2147483647.
VAL_DEFAULT_RANGE_EXCEED_BIGINT=Zadajte platné celé číslo s hodnotou od -9223372036854775808 do 9223372036854775807.

#XMSG: Incase Current Watermark is not applicable
notApplicableText=Nedá sa použiť

#XTXT: Preview Data to be Deleted
previewDataToBeDeleted=Ukážka údajov, ktoré sa majú odstrániť

#XBTN: Close Preview
txtCloseBtn=Zavrieť


#XBTN: Segmented button for filtered Data
txtFilteredData=Filtrované údaje

#XBTN: Segmented button for All Data
txtAllData=Všetky dáta

txtDataViewer=Zobrazovač údajov
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ LTF ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XCOL: Buffer Merge Status
txtBufferMergeStatus=Stav zlúčenia vyrovnávacej pamäte

#XCOL: Buffer Last Updated
txtBufferLastUpdated=Posledná aktualizácia vyrovnávacej pamäte

#XCOL: Buffer Last Updated By
txtBufferLastUpdatedBy=Poslednú aktualizáciu vyrovnávacej pamäte vykonal

#XCOL: Partitions
txtPartitions=Segmenty

#XCOL: Determine the number of records
txtActiveRecords=Počet aktívnych záznamov

#XCOL: Collect data via Spark (DeltaTable)
txtActiveRecordsFileStorage=Úložisko aktívnych záznamov (MiB)

#XCOL: Calculate data via Spark
txtPreviousVersionsFileStorage=Úložisko predchádzajúcich verzií (MiB)

#XCOL: Collect the size of all files via Spark
txtTotalTableFileStorage=Celkové úložisko (MiB)

#XCOL: Collect the inbound buffer file size
txtInboundBufferFileSize=Veľkosť súboru vyrovnávacej pamäte (MiB)

#XCOL: Collect the inbound buffer file count
txtInboundBufferFileCount=Počet súborov vyrovnávacej pamäte

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Viewer dialog ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=Nastavenia stĺpcov
#XFLD: Title for Error type
messagesTableType=Typ
#XFLD: Title for Error Message
messagesTableMessage=Hlásenie
#XFLD: Title for filter
filteredBy=Filtrované podľa:
#XTIT: text for values contained in filter
filterContains=obsahuje
#XTIT: text for values starting with in filter
filterStartsWith=začína na
#XTIT: text for values ending with in filter
filterEndsWith=končí na
#XTIT: Title for search in data preview toolbar
toolbarSearch=Hľadať
#XBUT: Button to clear filter
clearFilter=Vymazať filter
#XBUT: Button to save the operation
ok=OK
#XBUT: Button to restore the data
toolbarRestoreButton=Obnoviť
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=Aktualizovať stránku
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=Spracováva sa...
#XMSG: Message Confirmation
confirmation=Potvrdenie
#XMSG: Message for refresh successful
refreshSuccess=Úspešne obnovené
#XMSG: Message for refresh successful
refreshSuccessful=Obnovené
#XMSG: Message for restore successful
restoreSuccessful=Úspešne obnovené
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError=„{0}“ nemôže byť prázdne. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=Triediť vzostupne
#XTIT: Sort Descending
mdm-sortDescending=Triediť zostupne
#XTIT: Filter
mdm-Filter=Filtrovať
#XBUT: Button Cancel
mdm-cancel=Zrušiť
#XBUT: Button Add
mdm-Add=Pridať
#XMSG: and inside error message
and=a

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=Nástroje Filtrovať, Triediť, Odstrániť a Nastavenia tabuľky sú deaktivované neuloženými zmenami. Ak ich chcete aktivovať, uložte svoje zmeny.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=Odovzdať údaje
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=Duplikovať
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=Odstrániť
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=Pridať

#XTIT: text to be apended for key columns in p13n dialog e.g. (Key) <Column Name>
mdm-keyText=Kľúč
#XTIT: text to be apended for Not Null columns
mdm-notNullText=Nie null

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=Vložiť chýbajúcu hodnotu reťazca ako:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=NULL
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=Prázdny reťazec
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=Chýbajúce hodnoty reťazcov sa vkladajú iba do viditeľných stĺpcov reťazcov v nových a upravených riadkoch. Na zobrazenie všetkých relevantných stĺpcov použite Nastavenia stĺpcov.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=Nástroje Filtrovať, Triediť, Vložiť chýbajúcu hodnotu reťazca a Nastavenia tabuľky sú deaktivované neuloženými zmenami. Ak ich chcete aktivovať, uložte svoje zmeny.
#XFLD: Open SAP HANA Cockpit

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Settings LSA ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
txtHeaderApacheSettings=Nastavenia aplikácie Apache Spark
txtMergeSectionTitle=Zlúčiť
txtUseSpaceDefault=Použiť predvolený priestor
txtApplication=Aplikácia
txtOptimizeSectionTitle=Optimalizovať
txtNewSettings=Definujte nové nastavenie pre túto tabuľku
txtNewSettingsMessageBox=Definujte nové nastavenia pre túto úlohu
txtUseDefault=Použiť predvolené
txtMergeMsgBoxTitle=Zlúčiť tabuľku
txtOptimizeMsgBoxTitle=Optimalizovať tabuľku
#XTIT: sub section header for LSA settings i.e Optimize settings
txtOptimizeSettings=Optimalizovať nastavenia
#XTIT: Z - order columns list header
txtZOrderList=Stĺpce v poradí Z
#XBUT: Button to define z order columns
txtZOrderEdit=Upraviť
#XBUT: Button to save the z order columns
txtZOrderDelete=Odstrániť
#XMSG: message for empty z order columns
txtNoZOrderList=Neexistujú žiadne nastavenia
#XMSG: message for empty z order list
txtEmptyZOrderList=Zlepšite výkon definovaním stĺpcov v poradí Z
#XBUT: Button to add z order columns
txtDefineZOrder=Definovať stĺpce v poradí Z
#XLBL: Label for z order columns
txtColumnZOrder=Stĺpce
#XMSG: error message to inform user about HANA limitation
zOrderLimitationError=Môžete vybrať len jeden stĺpec Z-zákazky.

#XMSG: message for conflicting task
taskAlreadyRunning=Konfliktná úloha už prebieha pre objekt „{0}“.

#XMSG: Save spark settings success
saveSparkSettingsSuccess=Nastavenia aplikácie Apache Spark uložené.
