
msgDataDeletionStarted=正在删除表 "{0}" 的数据

#XMSG: message to inform table not found in the repository
localTableNotFound=在资源库中找不到本地表 {0}。

#XFLD: Used Disk
txtUsedDisk=已用磁盘（MiB）
txtSizeOnDisk=磁盘存储大小 (MiB)
#XFLD: Used In-Memory
txtUsedInMemory=已用内存（MiB）
txtSizeInMemory=内存大小 (MiB)
#XFLD: Number of Records
txtNumberOfRecords=记录数
#XFLD: Delta capture
txtDeltaCapture=增量捕获
#XFLD: In-Memory storage
txtInMemoryStorage=内存存储
#XFLD: Technical Name
txtTechnicalName=技术名称
#XFLD: Business Name
txtBusinessName=业务名称
#XFLD: Growth Rate (Last Month)
txtgrowthRate=30 天内增长率
#XFLD: Number of Partitions
txtnumberofpartitions=分区数
#XFLD: Last Updated
txtlastUpdatedBy=上次更新者
#XFLD: Last Updated
txtlastupdated=上次更新时间
#XFLD:delta Capture
txtdeltaCapture=增量捕获
txtYes=是
txtNo=否
txtOn=启用
txtOff=禁用
localTablesHeaderTitle=本地表（{0}）
localTablesLTFHeaderTitle=本地表（文件）（{0}）
txtSearch=搜索
txtRefresh=刷新
txtNotApplicable=不适用
txtObject=对象（{0}）

#XTOL:for Last updated column header
tolLastUpdated=显示上次更新数据的时间
#XTOL: for Last updated by column header
tolLastUpdatedBy=显示上次更新数据的方式
#XTOL: for Last buffer updated column header
tolBufferLastUpdated=显示上次更新入站缓冲区数据的时间
#XTOL: for Last buffer updated by column header
tolBufferLastUpdatedBy=显示上次更新入站缓冲区数据的方式


#XMSG: message to inform repo Access Error
txtRepoAccessError=这个资源库不可用，某些功能被禁用。

#XBUT: Label for open in Editor
openInEditorNew=在数据模型构建器中打开

#XTOL: Toggle button to open data editor
openDataEditor=数据编辑器

#XBUT: Radio button group
txtDeleteAllRecords=删除所有记录
txtDeleteFilteredRecords=删除筛选的记录
txtRecordsMarkedForDeleted=删除标记为 "已删除" 的记录

#XTXT: preview
txtPreview=预览
#XMSG: Warning message for All Records delete
dataDeleteWarningMsgTxt=删除记录可能会影响正在使用记录的流。
#XMSG: Warning popup header
@confirmDataDeletion=删除数据
#XMSG: Warning popup message
@dataDeletionText=这项操作将删除表记录，可能会影响正在使用表记录的流。这项操作不可以撤消。是否继续？

#XBUT: Data Deletion warning
btnDeleteConfirm=删除

#XFLD: Label for table column
durationInSeconds=秒

#XMSG: Records marked as "Deleted" description
recordsMarkedForDeletionDescription=删除所有已完全处理并符合以下条件的记录：更改类型为 "已删除" 且时间超过
daysForDeltaTableDeletion=天
txtCurrentWatermark=当前水印

#XBUT: show preview button in filter
datViewerBtn=打开数据查看器

#XFLD: Number of deletable records
txtNumberOfDeletableRecords=可删除记录数

#XFLD: Size of deletable records
txtSizeOfDeletableRecords=可删除记录大小（MiB）

numberOfRecordsRefresh=刷新

#XTXT: Data Deletion Logs
txtDataDeletionLogs=数据删除日志

#XTXT: Data Deletion Schedules
txtDataDeletionSchedules=数据删除计划

#XTXT: Data Deletion
txtDataDeletion=数据删除

#XBTN: Delete Records
deleteRecords=删除记录
#XBTN: Create
createbtn=创建
#XBTN: Edit
editBtn=编辑
#XBTN: Delete
deleteBtn=删除
#XTXT: Placeholder for search bar
txtSearchPlaceholder=搜索

#XTXT: Data Deletion
txtDeletionSettings=删除设置

currentWatermark=当前水印

#XBUT: Select Columns Button
selectColumnsBtn=选择列

#XBUT: Merge table for ltf table
txtMergeTable=合并表

#XBUT: Optimize table for ltf table
txtOptimizeTable=优化表

#XBUT: schedule Merge and Optimize for ltf table
scheduleTextLabel=计划

#XBUT: Drop down menu button to create a schedule
createScheduleNewLabel=创建计划
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=编辑计划
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=删除计划
#XBUT: Assign schedule menu button label
assignScheduleLabel=将计划分配给我
#XBUT: Pause schedule menu label
pauseScheduleLabel=暂停计划
#XBUT: Resume schedule menu label
resumeScheduleLabel=恢复计划

#XMSG: message to say that merge task is started
msgMergeTaskStarted=表 "{0}" 的合并任务已启动

#XMSG: message to say that optimize task is started
msgOptimizeTaskStarted=表 "{0}" 的优化任务已启动

#XTXT: title for object page for logs
hdlfLogsObjectPageTxt=日志

#XTXT: title for object page for settings
hdlfSettingsObjectPageTxt=设置
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Delete Logs Table~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Label for Data delete logs table
txtStartColumn=开始
txtStartColumnTooltip=开始
#XFLD: Label for Data delete logs table
txtDurationColumn=持续时间
txtDurationColumnTooltip=持续时间
#XFLD: Label for Data delete logs table
txtObjectTypeColumn=对象类型
txtObjectTypeColumnTooltip=对象类型
#XFLD: Label for Data delete logs table
txtActivityColumn=活动
txtActivityColumnTooltip=活动
#XFLD: Label for Data delete logs table
txtUserColumn=用户
txtUserColumnTooltip=用户
txtRunStartedBy=运行启动者
txtManualRun=手动
txtScheduledRun=按计划
#XFLD: Label for Data delete logs table
txtNumberOfRecordsColumn=记录数
txtNumberOfRecordsColumnTooltip=记录数
#XFLD: Label for Data delete logs table
txtStatusColumn=状态
txtStatusColumnTooltip=状态

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Delete Logs Filter Dialog ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=过去 1 小时
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=过去 24 小时
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=上个月

#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=超过 5 分钟
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=超过 15 分钟
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=超过 1 小时

#XFLD: status text
COMPLETED=已完成
#XFLD: status text
FAILED=失败
#XFLD: status text
RUNNING=运行中

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Deletion Schedules Table~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XCOL: Type of Data deletion schedule
deletionScheduleType=类型
#XCOL: Name of Data deletion schedule
deletionScheduleName=名称
#XCOL: Frequency of schedule
deletionScheduleFrequency=频率
#XCOL: DateTime of when the last run started
deletionScheduleLastRunStart=上次运行开始
#XCOL: DateTime of when the last run ended
deletionScheduleLastRunEnd=上次运行结束
#XCOL: Number of records deleted in the last scheduled run
deletionScheduleRecordsLastRun=记录（上次运行）
#XBUT: To create a data deletion schedule
createDataDeletionScheduleBtn=创建数据删除计划
#XMSG: Data Deletion Schedules Table
noDataDeletionScheduleText=还没有定义数据删除计划
#XMSG: Data Deletion Schedules Table
noDataDeletionScheduleDescription=创建数据删除计划，定期删除不需要的数据
#XFLD: Frequency if a schedule is paused
paused:已暂停

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Schedule Data Deletion Wizard ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Title for the schedule data deletion wizard
scheduleDataDeletionTxt=计划数据删除
#XBUT: Schedule Data Deletion Wizard
back=返回
#XBUT: Schedule Data Deletion Wizard
nextStep=下一步
#XBUT: Schedule Data Deletion Wizard
createSchedule=创建计划
#XBUT: Schedule Data Deletion Wizard
updateSchedule=更新计划
#XGRP: Schedule Data Deletion Wizard's 1st step
settingStep=设置
#XGRP: Schedule Data Deletion Wizard's 2nd step
scheduleStep=计划
#XGRP: Schedule Data Deletion Wizard's 3rd step
reviewStep=检查
#XBUT: Schedule Data Deletion Wizard
cancel=取消
#XBUT: Schedule Data Deletion Wizard
edit=编辑
#XFLD: Schedule Data Deletion Wizard
deleteAllRecordsFilteredBy=删除依据以下条件筛选的所有记录
#XFLD: Schedule Data Deletion Wizard
recurrence=重复周期
#XFLD: Schedule Data Deletion Wizard
nextRun=下次运行
#XMSG: Error state for empty technical name
emptyLabel=请输入有效的计划名称。
#XMSG: Error state for duplicate technical name
duplicateID=已存在具有相同名称的计划。

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Filtered Deletions ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Create Filtered Deletion button
createFilteredDeletionBtn=创建筛选器

#XFLD: Filtered Deletions Operators
EQ=等于
BT=介于
Contains=包含
GT=大于
GE=大于或等于
LT=小于
LE=小于或等于
StartsWith=开头为
EndsWith=结尾为
Empty=空
BXD={0} 天前


#XFLD: Placeholder for filter input
txtDatePlaceholder=YYYY-MM-DD
txtTimePlaceholder=HH:mm:ss
txtDateTimePlaceholder=YYYY-MM-DD HH:mm:ss
txtStringPlaceholder=输入字符串
txtNumberPlaceholder=输入数字
txtValuePlaceholder=输入值

#XMSG-Validation messages
txtEmptyFilterValue=输入有效的筛选器值。
#XMSG: Error message for empty  value
txtEmptyValueForDDR=请提供天数值。

#XMSG: lower bound error text
txtLowerbounderrormsg=下限需要低于上限。
#XMSG: lower bound error text for string
txtLowerbounderrormsgforString=字符串 "{0}" 大于 "{1}"。
#XMSG: higher bound error text
txtHigherbounderrormsg=上限需要高于下限。
#XMSG: higher bound error text for string
txtHigherbounderrormsgforString=字符串 "{1}" 小于 "{0}"。
#XMSG Error msg for missing values
VAL_LENGTH_EXCEED=值的长度不能超过 {0}。
VAL_ENTER_VALID_INT=输入有效的整数。
VAL_ENTER_VALID_DECIMAL=输入精度为 {0} 且标度为 {1} 的有效小数值。
VAL_ENTER_VALID_DECIMAL_VALUE=输入小数值。
VAL_ENTER_VALID_DATE=输入有效日期。
VAL_ENTER_VALID_NUMBER=输入有效数字。
VAL_DUPLICATE_FILTER=复制筛选器值。
VAL_DEFAULT_RANGE_EXCEED_INT=输入值在 -2147483648 到 2147483647 之间的有效整数。
VAL_DEFAULT_RANGE_EXCEED_BIGINT=输入值在 -9223372036854775808 到 9223372036854775807 之间的有效整数。

#XMSG: Incase Current Watermark is not applicable
notApplicableText=不适用

#XTXT: Preview Data to be Deleted
previewDataToBeDeleted=预览要删除的数据

#XBTN: Close Preview
txtCloseBtn=关闭


#XBTN: Segmented button for filtered Data
txtFilteredData=已筛选数据

#XBTN: Segmented button for All Data
txtAllData=所有数据

txtDataViewer=数据查看器
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ LTF ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XCOL: Buffer Merge Status
txtBufferMergeStatus=缓冲区合并状态

#XCOL: Buffer Last Updated
txtBufferLastUpdated=缓冲区上次更新

#XCOL: Buffer Last Updated By
txtBufferLastUpdatedBy=缓冲区上次更新者

#XCOL: Partitions
txtPartitions=分区

#XCOL: Determine the number of records
txtActiveRecords=活动记录数

#XCOL: Collect data via Spark (DeltaTable)
txtActiveRecordsFileStorage=活动记录存储（MiB）

#XCOL: Calculate data via Spark
txtPreviousVersionsFileStorage=先前版本存储（MiB）

#XCOL: Collect the size of all files via Spark
txtTotalTableFileStorage=汇总存储（MiB）

#XCOL: Collect the inbound buffer file size
txtInboundBufferFileSize=缓冲文件大小（MiB）

#XCOL: Collect the inbound buffer file count
txtInboundBufferFileCount=缓冲文件计数

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Viewer dialog ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=列设置
#XFLD: Title for Error type
messagesTableType=类型
#XFLD: Title for Error Message
messagesTableMessage=消息
#XFLD: Title for filter
filteredBy=筛选条件：
#XTIT: text for values contained in filter
filterContains=包含
#XTIT: text for values starting with in filter
filterStartsWith=开头为
#XTIT: text for values ending with in filter
filterEndsWith=结尾为
#XTIT: Title for search in data preview toolbar
toolbarSearch=搜索
#XBUT: Button to clear filter
clearFilter=清除筛选器
#XBUT: Button to save the operation
ok=确定
#XBUT: Button to restore the data
toolbarRestoreButton=刷新
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=更新页面
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=正在处理……
#XMSG: Message Confirmation
confirmation=确认
#XMSG: Message for refresh successful
refreshSuccess=已成功刷新
#XMSG: Message for refresh successful
refreshSuccessful=已刷新
#XMSG: Message for restore successful
restoreSuccessful=已成功还原
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" 不能为空。{1}。
#XTIT: Sort Ascending
mdm-sortAscending=升序排序
#XTIT: Sort Descending
mdm-sortDescending=降序排序
#XTIT: Filter
mdm-Filter=筛选
#XBUT: Button Cancel
mdm-cancel=取消
#XBUT: Button Add
mdm-Add=添加
#XMSG: and inside error message
and=与

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=因为存在没有保存的更改，筛选、排序、删除和表设置工具已禁用。请保存更改，以启用这些工具。
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=上载数据
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=复制
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=删除
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=添加

#XTIT: text to be apended for key columns in p13n dialog e.g. (Key) <Column Name>
mdm-keyText=键
#XTIT: text to be apended for Not Null columns
mdm-notNullText=非 Null

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=插入缺少的字符串值作为：
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=NULL
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=空字符串
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=缺少的字符串值只可以插入新行或已编辑行中的可见字符串列。使用 "列设置" 显示所有相关列。
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=因为存在没有保存的更改，筛选、排序、"插入缺少的字符串值" 和表设置工具已禁用。请保存更改，以启用这些工具。
#XFLD: Open SAP HANA Cockpit

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Settings LSA ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
txtHeaderApacheSettings=Apache Spark 应用程序设置
txtMergeSectionTitle=合并
txtUseSpaceDefault=使用空间默认值
txtApplication=应用程序
txtOptimizeSectionTitle=优化
txtNewSettings=为这个表定义新设置
txtNewSettingsMessageBox=为这个任务定义新设置
txtUseDefault=使用默认值
txtMergeMsgBoxTitle=合并表
txtOptimizeMsgBoxTitle=优化表
#XTIT: sub section header for LSA settings i.e Optimize settings
txtOptimizeSettings=优化设置
#XTIT: Z - order columns list header
txtZOrderList=Z-Order 列
#XBUT: Button to define z order columns
txtZOrderEdit=编辑
#XBUT: Button to save the z order columns
txtZOrderDelete=删除
#XMSG: message for empty z order columns
txtNoZOrderList=不存在设置
#XMSG: message for empty z order list
txtEmptyZOrderList=通过定义 Z-Order 列提升性能
#XBUT: Button to add z order columns
txtDefineZOrder=定义 Z-Order 列
#XLBL: Label for z order columns
txtColumnZOrder=列
#XMSG: error message to inform user about HANA limitation
zOrderLimitationError=只能选择一个 Z-Order 列。

#XMSG: message for conflicting task
taskAlreadyRunning=对象“{0}”已经有一个冲突任务在运行。

#XMSG: Save spark settings success
saveSparkSettingsSuccess=已保存 Apache Spark 应用设置。
