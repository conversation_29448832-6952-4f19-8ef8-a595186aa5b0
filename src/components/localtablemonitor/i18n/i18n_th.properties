
msgDataDeletionStarted=เรากำลังลบข้อมูลของตาราง "{0}"

#XMSG: message to inform table not found in the repository
localTableNotFound=ไม่พบตารางภายใน ''{0}'' ในพื้นที่เก็บข้อมูล

#XFLD: Used Disk
txtUsedDisk=ดิสก์ที่ใช้ (MiB)
txtSizeOnDisk=ขนาดในดิสก์ (MiB)
#XFLD: Used In-Memory
txtUsedInMemory=หน่วยความจำที่ใช้ (MiB)
txtSizeInMemory=ขนาดในหน่วยความจำ (MiB)
#XFLD: Number of Records
txtNumberOfRecords=จำนวนเรคคอร์ด
#XFLD: Delta capture
txtDeltaCapture=การจับข้อมูลเดลต้า
#XFLD: In-Memory storage
txtInMemoryStorage=พื้นที่จัดเก็บในหน่วยความจำ
#XFLD: Technical Name
txtTechnicalName=ชื่อทางเทคนิค
#XFLD: Business Name
txtBusinessName=ชื่อทางธุรกิจ
#XFLD: Growth Rate (Last Month)
txtgrowthRate=การเติบโตใน 30 วัน
#XFLD: Number of Partitions
txtnumberofpartitions=จำนวนพาร์ทิชัน
#XFLD: Last Updated
txtlastUpdatedBy=อัพเดทครั้งล่าสุดโดย
#XFLD: Last Updated
txtlastupdated=อัพเดทครั้งล่าสุด
#XFLD:delta Capture
txtdeltaCapture=การจับข้อมูลเดลต้า
txtYes=ใช่
txtNo=ไม่ใช่
txtOn=เปิด
txtOff=ปิด
localTablesHeaderTitle=ตารางภายใน ({0})
localTablesLTFHeaderTitle=ตารางภายใน (ไฟล์) ({0})
txtSearch=ค้นหา
txtRefresh=รีเฟรช
txtNotApplicable=ไม่เกี่ยวข้อง
txtObject=ออบเจค ({0})

#XTOL:for Last updated column header
tolLastUpdated=แสดงเวลาที่อัพเดทข้อมูลครั้งล่าสุด
#XTOL: for Last updated by column header
tolLastUpdatedBy=แสดงวิธีอัพเดทข้อมูลครั้งล่าสุด
#XTOL: for Last buffer updated column header
tolBufferLastUpdated=แสดงเวลาที่ข้อมูลในบัฟเฟอร์ขาเข้าได้รับการอัพเดทล่าสุด
#XTOL: for Last buffer updated by column header
tolBufferLastUpdatedBy=แสดงวิธีที่ข้อมูลในบัฟเฟอร์ขาเข้าได้รับการอัพเดทล่าสุด


#XMSG: message to inform repo Access Error
txtRepoAccessError=พื้นที่เก็บข้อมูลไม่พร้อมใช้งานและฟีเจอร์บางอย่างถูกปิดใช้งาน

#XBUT: Label for open in Editor
openInEditorNew=เปิดในตัวสร้างข้อมูล

#XTOL: Toggle button to open data editor
openDataEditor=เอดิเตอร์ข้อมูล

#XBUT: Radio button group
txtDeleteAllRecords=ลบเรคคอร์ดทั้งหมด
txtDeleteFilteredRecords=ลบเรคคอร์ดที่ฟิลเตอร์
txtRecordsMarkedForDeleted=ลบเรคคอร์ดถูกทำเครื่องหมายเป็น "ลบแล้ว"

#XTXT: preview
txtPreview=แสดงตัวอย่าง
#XMSG: Warning message for All Records delete
dataDeleteWarningMsgTxt=การลบเรคคอร์ดอาจส่งผลต่อผังที่ใช้
#XMSG: Warning popup header
@confirmDataDeletion=ลบข้อมูล
#XMSG: Warning popup message
@dataDeletionText=การดำเนินการนี้จะลบเรคคอร์ดตารางซึ่งอาจส่งผลต่อผังที่ใช้และไม่สามารถเลิกทำได้ คุณต้องการดำเนินการต่อหรือไม่?

#XBUT: Data Deletion warning
btnDeleteConfirm=ลบ

#XFLD: Label for table column
durationInSeconds=วินาที

#XMSG: Records marked as "Deleted" description
recordsMarkedForDeletionDescription=ลบเรคคอร์ดทั้งหมดที่ดำเนินการเสร็จสมบูรณ์โดยมีประเภทการเปลี่ยนแปลง 'ลบแล้ว' ที่นานกว่า
daysForDeltaTableDeletion=วัน
txtCurrentWatermark=ลายน้ำปัจจุบัน

#XBUT: show preview button in filter
datViewerBtn=เปิดตัวแสดงข้อมูล

#XFLD: Number of deletable records
txtNumberOfDeletableRecords=จำนวนเรคคอร์ดที่ลบได้

#XFLD: Size of deletable records
txtSizeOfDeletableRecords=ขนาดของเรคคอร์ดที่ลบได้ (MiB)

numberOfRecordsRefresh=รีเฟรช

#XTXT: Data Deletion Logs
txtDataDeletionLogs=ล็อกการลบข้อมูล

#XTXT: Data Deletion Schedules
txtDataDeletionSchedules=กำหนดการลบข้อมูล

#XTXT: Data Deletion
txtDataDeletion=การลบข้อมูล

#XBTN: Delete Records
deleteRecords=ลบเรคคอร์ด
#XBTN: Create
createbtn=สร้าง
#XBTN: Edit
editBtn=แก้ไข
#XBTN: Delete
deleteBtn=ลบ
#XTXT: Placeholder for search bar
txtSearchPlaceholder=ค้นหา

#XTXT: Data Deletion
txtDeletionSettings=การกำหนดค่าการลบ

currentWatermark=ลายน้ำปัจจุบัน

#XBUT: Select Columns Button
selectColumnsBtn=เลือกคอลัมน์

#XBUT: Merge table for ltf table
txtMergeTable=ผสานตาราง

#XBUT: Optimize table for ltf table
txtOptimizeTable=ปรับตารางให้เหมาะสม

#XBUT: schedule Merge and Optimize for ltf table
scheduleTextLabel=จัดกำหนดการ

#XBUT: Drop down menu button to create a schedule
createScheduleNewLabel=สร้างกำหนดการ
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=แก้ไขกำหนดการ
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=ลบกำหนดการ
#XBUT: Assign schedule menu button label
assignScheduleLabel=กำหนดกำหนดการให้กับฉัน
#XBUT: Pause schedule menu label
pauseScheduleLabel=หยุดกำหนดการชั่วคราว
#XBUT: Resume schedule menu label
resumeScheduleLabel=ดำเนินการกำหนดการต่อ

#XMSG: message to say that merge task is started
msgMergeTaskStarted=เริ่มต้นการผสานตารางสำหรับตาราง "{0}" แล้ว

#XMSG: message to say that optimize task is started
msgOptimizeTaskStarted=เริ่มต้นการปรับตารางให้เหมาะสมสำหรับตาราง "{0}" แล้ว

#XTXT: title for object page for logs
hdlfLogsObjectPageTxt=ล็อก

#XTXT: title for object page for settings
hdlfSettingsObjectPageTxt=การกำหนดค่า
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Delete Logs Table~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Label for Data delete logs table
txtStartColumn=เริ่มต้น
txtStartColumnTooltip=เริ่มต้น
#XFLD: Label for Data delete logs table
txtDurationColumn=ระยะเวลา
txtDurationColumnTooltip=ระยะเวลา
#XFLD: Label for Data delete logs table
txtObjectTypeColumn=ประเภทออบเจค
txtObjectTypeColumnTooltip=ประเภทออบเจค
#XFLD: Label for Data delete logs table
txtActivityColumn=กิจกรรม
txtActivityColumnTooltip=กิจกรรม
#XFLD: Label for Data delete logs table
txtUserColumn=ผู้ใช้
txtUserColumnTooltip=ผู้ใช้
txtRunStartedBy=เริ่มต้นการดำเนินการโดย
txtManualRun=โดยผู้ใช้
txtScheduledRun=ตามกำหนดการ
#XFLD: Label for Data delete logs table
txtNumberOfRecordsColumn=จำนวนเรคคอร์ด
txtNumberOfRecordsColumnTooltip=จำนวนเรคคอร์ด
#XFLD: Label for Data delete logs table
txtStatusColumn=สถานะ
txtStatusColumnTooltip=สถานะ

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Delete Logs Filter Dialog ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=ชั่วโมงที่แล้ว
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=24 ชั่วโมงที่ผ่านมา
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=เดือนที่แล้ว

#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=มากกว่า 5 นาที
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=มากกว่า 15 นาที
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=มากกว่า 1 ชั่วโมง

#XFLD: status text
COMPLETED=เสร็จสมบูรณ์
#XFLD: status text
FAILED=ล้มเหลว
#XFLD: status text
RUNNING=กำลังดำเนินการ

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Deletion Schedules Table~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XCOL: Type of Data deletion schedule
deletionScheduleType=ประเภท
#XCOL: Name of Data deletion schedule
deletionScheduleName=ชื่อ
#XCOL: Frequency of schedule
deletionScheduleFrequency=ความถี่
#XCOL: DateTime of when the last run started
deletionScheduleLastRunStart=เริ่มต้นการดำเนินการครั้งล่าสุด
#XCOL: DateTime of when the last run ended
deletionScheduleLastRunEnd=สิ้นสุดการดำเนินการครั้งล่าสุด
#XCOL: Number of records deleted in the last scheduled run
deletionScheduleRecordsLastRun=เรคคอร์ด (การดำเนินการครั้งล่าสุด)
#XBUT: To create a data deletion schedule
createDataDeletionScheduleBtn=สร้างกำหนดการลบข้อมูล
#XMSG: Data Deletion Schedules Table
noDataDeletionScheduleText=ยังไม่ได้ระบุกำหนดการลบข้อมูล
#XMSG: Data Deletion Schedules Table
noDataDeletionScheduleDescription=สร้างกำหนดการลบข้อมูลเพื่อย้ายข้อมูลที่คุณไม่ต้องการออกเป็นประจำ
#XFLD: Frequency if a schedule is paused
paused:ถูกหยุดชั่วคราว

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Schedule Data Deletion Wizard ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Title for the schedule data deletion wizard
scheduleDataDeletionTxt=จัดกำหนดการลบข้อมูล
#XBUT: Schedule Data Deletion Wizard
back=ย้อนกลับ
#XBUT: Schedule Data Deletion Wizard
nextStep=ขั้นตอนถัดไป
#XBUT: Schedule Data Deletion Wizard
createSchedule=สร้างกำหนดการ
#XBUT: Schedule Data Deletion Wizard
updateSchedule=อัพเดทกำหนดการ
#XGRP: Schedule Data Deletion Wizard's 1st step
settingStep=การกำหนดค่า
#XGRP: Schedule Data Deletion Wizard's 2nd step
scheduleStep=จัดกำหนดการ
#XGRP: Schedule Data Deletion Wizard's 3rd step
reviewStep=ตรวจทาน
#XBUT: Schedule Data Deletion Wizard
cancel=ยกเลิก
#XBUT: Schedule Data Deletion Wizard
edit=แก้ไข
#XFLD: Schedule Data Deletion Wizard
deleteAllRecordsFilteredBy=ลบเรคคอร์ดทั้งหมดที่ฟิลเตอร์ตาม
#XFLD: Schedule Data Deletion Wizard
recurrence=การเกิดซ้ำ
#XFLD: Schedule Data Deletion Wizard
nextRun=การดำเนินการครั้งถัดไป
#XMSG: Error state for empty technical name
emptyLabel=กรุณาป้อนชื่อกำหนดการที่ถูกต้อง
#XMSG: Error state for duplicate technical name
duplicateID=กำหนดการที่มีชื่อเดียวกันมีอยู่แล้ว

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Filtered Deletions ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Create Filtered Deletion button
createFilteredDeletionBtn=สร้างฟิลเตอร์

#XFLD: Filtered Deletions Operators
EQ=เท่ากับ
BT=ระหว่าง
Contains=มี
GT=มากกว่า
GE=มากกว่าหรือเท่ากับ
LT=น้อยกว่า
LE=น้อยกว่าหรือเท่ากับ
StartsWith=ขึ้นต้นด้วย
EndsWith=ลงท้ายด้วย
Empty=ว่าง
BXD=ก่อน {0} วัน


#XFLD: Placeholder for filter input
txtDatePlaceholder=YYYY-MM-DD
txtTimePlaceholder=HH:mm:ss
txtDateTimePlaceholder=YYYY-MM-DD HH:mm:ss
txtStringPlaceholder=ป้อนสตริง
txtNumberPlaceholder=ป้อนตัวเลข
txtValuePlaceholder=ป้อนค่า

#XMSG-Validation messages
txtEmptyFilterValue=ป้อนค่าฟิลเตอร์ที่ถูกต้อง
#XMSG: Error message for empty  value
txtEmptyValueForDDR=ระบุค่าเป็นวัน

#XMSG: lower bound error text
txtLowerbounderrormsg=ขอบเขตล่างต้องต่ำกว่าขอบเขตบน
#XMSG: lower bound error text for string
txtLowerbounderrormsgforString=สตริง "{0}" มีขนาดใหญ่กว่า "{1}"
#XMSG: higher bound error text
txtHigherbounderrormsg=ขอบเขตบนต้องสูงกว่าขอบเขตล่าง
#XMSG: higher bound error text for string
txtHigherbounderrormsgforString=สตริง "{1}" มีขนาดเล็กกว่า "{0}"
#XMSG Error msg for missing values
VAL_LENGTH_EXCEED=ความยาวของค่าต้องไม่เกิน {0}
VAL_ENTER_VALID_INT=ป้อนจำนวนเต็มที่ถูกต้อง
VAL_ENTER_VALID_DECIMAL=ป้อนค่าทศนิยมที่ถูกต้องที่มีความแม่นยำ {0} และสเกล {1}
VAL_ENTER_VALID_DECIMAL_VALUE=ป้อนค่าทศนิยม
VAL_ENTER_VALID_DATE=ป้อนวันที่ที่ถูกต้อง
VAL_ENTER_VALID_NUMBER=ป้อนตัวเลขที่ถูกต้อง
VAL_DUPLICATE_FILTER=ทำซ้ำค่าฟิลเตอร์
VAL_DEFAULT_RANGE_EXCEED_INT=ป้อนจำนวนเต็มที่ถูกต้องที่มีค่าระหว่าง -2147483648 ถึง 2147483647
VAL_DEFAULT_RANGE_EXCEED_BIGINT=ป้อนจำนวนเต็มที่ถูกต้องที่มีค่าระหว่าง -9223372036854775808 ถึง 9223372036854775807

#XMSG: Incase Current Watermark is not applicable
notApplicableText=ไม่เกี่ยวข้อง

#XTXT: Preview Data to be Deleted
previewDataToBeDeleted=แสดงตัวอย่างข้อมูลที่จะลบ

#XBTN: Close Preview
txtCloseBtn=ปิด


#XBTN: Segmented button for filtered Data
txtFilteredData=ข้อมูลที่ฟิลเตอร์

#XBTN: Segmented button for All Data
txtAllData=ข้อมูลทั้งหมด

txtDataViewer=ตัวแสดงข้อมูล
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ LTF ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XCOL: Buffer Merge Status
txtBufferMergeStatus=สถานะการผสานบัฟเฟอร์

#XCOL: Buffer Last Updated
txtBufferLastUpdated=อัพเดทบัฟเฟอร์ครั้งล่าสุด

#XCOL: Buffer Last Updated By
txtBufferLastUpdatedBy=อัพเดทบัฟเฟอร์ครั้งล่าสุดโดย

#XCOL: Partitions
txtPartitions=พาร์ทิชัน

#XCOL: Determine the number of records
txtActiveRecords=จำนวนเรคคอร์ดที่ใช้งาน

#XCOL: Collect data via Spark (DeltaTable)
txtActiveRecordsFileStorage=พื้นที่จัดเก็บเรคคอร์ดที่ใช้งาน (MiB)

#XCOL: Calculate data via Spark
txtPreviousVersionsFileStorage=พื้นที่จัดเก็บเวอร์ชันก่อนหน้า (MiB)

#XCOL: Collect the size of all files via Spark
txtTotalTableFileStorage=พื้นที่จัดเก็บทั้งหมด (MiB)

#XCOL: Collect the inbound buffer file size
txtInboundBufferFileSize=ขนาดไฟล์บัฟเฟอร์ (MiB)

#XCOL: Collect the inbound buffer file count
txtInboundBufferFileCount=จำนวนไฟล์บัฟเฟอร์

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Viewer dialog ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=การกำหนดค่าคอลัมน์
#XFLD: Title for Error type
messagesTableType=ประเภท
#XFLD: Title for Error Message
messagesTableMessage=ข้อความ
#XFLD: Title for filter
filteredBy=ฟิลเตอร์ตาม:
#XTIT: text for values contained in filter
filterContains=มี
#XTIT: text for values starting with in filter
filterStartsWith=ขึ้นต้นด้วย
#XTIT: text for values ending with in filter
filterEndsWith=ลงท้ายด้วย
#XTIT: Title for search in data preview toolbar
toolbarSearch=ค้นหา
#XBUT: Button to clear filter
clearFilter=ล้างฟิลเตอร์
#XBUT: Button to save the operation
ok=ตกลง
#XBUT: Button to restore the data
toolbarRestoreButton=รีเฟรช
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=อัพเดทหน้า
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=กำลังประมวลผล...
#XMSG: Message Confirmation
confirmation=การยืนยัน
#XMSG: Message for refresh successful
refreshSuccess=รีเฟรชได้สำเร็จ
#XMSG: Message for refresh successful
refreshSuccessful=รีเฟรชแล้ว
#XMSG: Message for restore successful
restoreSuccessful=คืนค่าได้สำเร็จ
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" ต้องไม่เว้นว่างไว้ {1}
#XTIT: Sort Ascending
mdm-sortAscending=จัดเรียงจากน้อยไปหามาก
#XTIT: Sort Descending
mdm-sortDescending=จัดเรียงจากมากไปหาน้อย
#XTIT: Filter
mdm-Filter=ฟิลเตอร์
#XBUT: Button Cancel
mdm-cancel=ยกเลิก
#XBUT: Button Add
mdm-Add=เพิ่ม
#XMSG: and inside error message
and=และ

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=เครื่องมือ 'ฟิลเตอร์' 'จัดเรียง' 'ลบ' และ 'การกำหนดค่าตาราง' ถูกปิดใช้งานเนื่องจากมีการเปลี่ยนแปลงที่ยังไม่ได้เก็บบันทึก กรุณาเก็บบันทึกการเปลี่ยนแปลงของคุณเพื่อเปิดใช้งาน
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=อัพโหลดข้อมูล
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=ทำซ้ำ
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=ลบ
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=เพิ่ม

#XTIT: text to be apended for key columns in p13n dialog e.g. (Key) <Column Name>
mdm-keyText=คีย์
#XTIT: text to be apended for Not Null columns
mdm-notNullText=ไม่ใช่ค่า Null

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=แทรกค่าสตริงที่ขาดหายไปเป็น:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=ค่า Null
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=สตริงว่าง
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=ค่าสตริงที่ขาดหายไปจะถูกแทรกเฉพาะในคอลัมน์สตริงที่มองเห็นได้ในแถวใหม่และแถวที่แก้ไข ใช้ 'การกำหนดค่าคอลัมน์' เพื่อแสดงคอลัมน์ที่เกี่ยวข้องทั้งหมด
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=เครื่องมือ 'ฟิลเตอร์' 'จัดเรียง' 'แทรกค่าสตริงที่ขาดหายไป' และ 'การกำหนดค่าตาราง' ถูกปิดใช้งานเนื่องจากมีการเปลี่ยนแปลงที่ยังไม่ได้เก็บบันทึก กรุณาเก็บบันทึกการเปลี่ยนแปลงของคุณเพื่อเปิดใช้งาน
#XFLD: Open SAP HANA Cockpit

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Settings LSA ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
txtHeaderApacheSettings=การกำหนดค่าแอพพลิเคชัน Apache Spark
txtMergeSectionTitle=ผสาน
txtUseSpaceDefault=ใช้ค่าตั้งต้นของพื้นที่
txtApplication=แอพพลิเคชัน
txtOptimizeSectionTitle=ปรับให้เหมาะสม
txtNewSettings=ระบุการกำหนดค่าใหม่สำหรับตารางนี้
txtNewSettingsMessageBox=ระบุการกำหนดค่าใหม่สำหรับงานนี้
txtUseDefault=ใช้ค่าตั้งต้น
txtMergeMsgBoxTitle=ผสานตาราง
txtOptimizeMsgBoxTitle=ปรับตารางให้เหมาะสม
#XTIT: sub section header for LSA settings i.e Optimize settings
txtOptimizeSettings=ปรับการกำหนดค่าให้เหมาะสม
#XTIT: Z - order columns list header
txtZOrderList=คอลัมน์ลำดับ Z
#XBUT: Button to define z order columns
txtZOrderEdit=แก้ไข
#XBUT: Button to save the z order columns
txtZOrderDelete=ลบ
#XMSG: message for empty z order columns
txtNoZOrderList=ไม่มีการกำหนดค่า
#XMSG: message for empty z order list
txtEmptyZOrderList=ปรับปรุงประสิทธิภาพโดยการกำหนดคอลัมน์ลำดับ Z
#XBUT: Button to add z order columns
txtDefineZOrder=กำหนดคอลัมน์ลำดับ Z
#XLBL: Label for z order columns
txtColumnZOrder=คอลัมน์
#XMSG: error message to inform user about HANA limitation
zOrderLimitationError=คุณสามารถเลือกคอลัมน์ลำดับ Z ได้เพียงคอลัมน์เดียว

#XMSG: message for conflicting task
taskAlreadyRunning=งานที่ขัดแย้งกันกำลังดำเนินการอยู่สำหรับออบเจค "{0}"

#XMSG: Save spark settings success
saveSparkSettingsSuccess=เก็บบันทึกการกำหนดค่าแอพพลิเคชัน Apache Spark แล้ว
