
msgDataDeletionStarted=系統正在刪除表格 "{0}" 的資料

#XMSG: message to inform table not found in the repository
localTableNotFound=儲藏庫中找不到本端表格 {0}。

#XFLD: Used Disk
txtUsedDisk=使用的硬碟 (MiB)
txtSizeOnDisk=硬碟大小 (MiB)
#XFLD: Used In-Memory
txtUsedInMemory=使用的記憶體內 (MiB)
txtSizeInMemory=使用的記憶體內 (MiB)
#XFLD: Number of Records
txtNumberOfRecords=記錄數量
#XFLD: Delta capture
txtDeltaCapture=差異擷取
#XFLD: In-Memory storage
txtInMemoryStorage=記憶體內儲存
#XFLD: Technical Name
txtTechnicalName=技術名稱
#XFLD: Business Name
txtBusinessName=業務名稱
#XFLD: Growth Rate (Last Month)
txtgrowthRate=30 天內的成長
#XFLD: Number of Partitions
txtnumberofpartitions=分割數量
#XFLD: Last Updated
txtlastUpdatedBy=最後更新者
#XFLD: Last Updated
txtlastupdated=最後更新
#XFLD:delta Capture
txtdeltaCapture=差異擷取
txtYes=是
txtNo=否
txtOn=開啟
txtOff=關閉
localTablesHeaderTitle=本端表格 ({0})
localTablesLTFHeaderTitle=本端表格 (檔案) ({0})
txtSearch=搜尋
txtRefresh=重新整理
txtNotApplicable=不適用
txtObject=物件 ({0})

#XTOL:for Last updated column header
tolLastUpdated=顯示資料最後更新的時間
#XTOL: for Last updated by column header
tolLastUpdatedBy=顯示資料最後更新的方式
#XTOL: for Last buffer updated column header
tolBufferLastUpdated=顯示內傳緩衝中資料最後更新的時間
#XTOL: for Last buffer updated by column header
tolBufferLastUpdatedBy=顯示內傳緩衝中資料最後更新的方式


#XMSG: message to inform repo Access Error
txtRepoAccessError=儲藏庫無法使用，且特定功能已停用。

#XBUT: Label for open in Editor
openInEditorNew=在資料模型建立器中開啟

#XTOL: Toggle button to open data editor
openDataEditor=資料編輯器

#XBUT: Radio button group
txtDeleteAllRecords=刪除所有記錄
txtDeleteFilteredRecords=刪除篩選的記錄
txtRecordsMarkedForDeleted=刪除標記為「已刪除」的記錄

#XTXT: preview
txtPreview=預覽
#XMSG: Warning message for All Records delete
dataDeleteWarningMsgTxt=刪除記錄可能影響使用中的流程。
#XMSG: Warning popup header
@confirmDataDeletion=刪除資料
#XMSG: Warning popup message
@dataDeletionText=此動作將刪除可能影響使用流程的表格記錄，且無法復原。您要繼續嗎？

#XBUT: Data Deletion warning
btnDeleteConfirm=刪除

#XFLD: Label for table column
durationInSeconds=秒

#XMSG: Records marked as "Deleted" description
recordsMarkedForDeletionDescription=刪除所有更改類型為「已刪除」，且晚於下列天數的完全處理記錄：
daysForDeltaTableDeletion=天
txtCurrentWatermark=目前浮水印

#XBUT: show preview button in filter
datViewerBtn=開啟資料檢視器

#XFLD: Number of deletable records
txtNumberOfDeletableRecords=可刪除記錄數量

#XFLD: Size of deletable records
txtSizeOfDeletableRecords=可刪除記錄大小 (MiB)

numberOfRecordsRefresh=重新整理

#XTXT: Data Deletion Logs
txtDataDeletionLogs=資料刪除日誌

#XTXT: Data Deletion Schedules
txtDataDeletionSchedules=資料刪除排程

#XTXT: Data Deletion
txtDataDeletion=資料刪除

#XBTN: Delete Records
deleteRecords=刪除記錄
#XBTN: Create
createbtn=建立
#XBTN: Edit
editBtn=編輯
#XBTN: Delete
deleteBtn=刪除
#XTXT: Placeholder for search bar
txtSearchPlaceholder=搜尋

#XTXT: Data Deletion
txtDeletionSettings=刪除設定

currentWatermark=目前浮水印

#XBUT: Select Columns Button
selectColumnsBtn=選擇欄

#XBUT: Merge table for ltf table
txtMergeTable=合併表格

#XBUT: Optimize table for ltf table
txtOptimizeTable=最佳化表格

#XBUT: schedule Merge and Optimize for ltf table
scheduleTextLabel=排程

#XBUT: Drop down menu button to create a schedule
createScheduleNewLabel=建立排程
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=編輯排程
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=刪除排程
#XBUT: Assign schedule menu button label
assignScheduleLabel=將排程指派給我
#XBUT: Pause schedule menu label
pauseScheduleLabel=暫停排程
#XBUT: Resume schedule menu label
resumeScheduleLabel=繼續排程

#XMSG: message to say that merge task is started
msgMergeTaskStarted=表格 "{0}" 的合併工作細項已開始

#XMSG: message to say that optimize task is started
msgOptimizeTaskStarted=表格 "{0}" 的最佳化工作細項已開始

#XTXT: title for object page for logs
hdlfLogsObjectPageTxt=日誌

#XTXT: title for object page for settings
hdlfSettingsObjectPageTxt=設定
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Delete Logs Table~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Label for Data delete logs table
txtStartColumn=開始
txtStartColumnTooltip=開始
#XFLD: Label for Data delete logs table
txtDurationColumn=持續期
txtDurationColumnTooltip=持續期
#XFLD: Label for Data delete logs table
txtObjectTypeColumn=物件類型
txtObjectTypeColumnTooltip=物件類型
#XFLD: Label for Data delete logs table
txtActivityColumn=活動
txtActivityColumnTooltip=活動
#XFLD: Label for Data delete logs table
txtUserColumn=使用者
txtUserColumnTooltip=使用者
txtRunStartedBy=開始執行者
txtManualRun=手動
txtScheduledRun=已排程
#XFLD: Label for Data delete logs table
txtNumberOfRecordsColumn=記錄數量
txtNumberOfRecordsColumnTooltip=記錄數量
#XFLD: Label for Data delete logs table
txtStatusColumn=狀態
txtStatusColumnTooltip=狀態

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Delete Logs Filter Dialog ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=過去 1 小時
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=過去 24 小時
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=上個月

#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=超過 5 分鐘
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=超過 15 分鐘
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=超過 1 小時

#XFLD: status text
COMPLETED=已完成
#XFLD: status text
FAILED=失敗
#XFLD: status text
RUNNING=執行中

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Deletion Schedules Table~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XCOL: Type of Data deletion schedule
deletionScheduleType=類型
#XCOL: Name of Data deletion schedule
deletionScheduleName=名稱
#XCOL: Frequency of schedule
deletionScheduleFrequency=頻率
#XCOL: DateTime of when the last run started
deletionScheduleLastRunStart=最後執行開始
#XCOL: DateTime of when the last run ended
deletionScheduleLastRunEnd=最後執行結束
#XCOL: Number of records deleted in the last scheduled run
deletionScheduleRecordsLastRun=記錄 (最後執行)
#XBUT: To create a data deletion schedule
createDataDeletionScheduleBtn=建立資料刪除排程
#XMSG: Data Deletion Schedules Table
noDataDeletionScheduleText=尚未定義資料刪除排程
#XMSG: Data Deletion Schedules Table
noDataDeletionScheduleDescription=建立資料刪除排程，定期移除不需要的資料
#XFLD: Frequency if a schedule is paused
paused:已暫停

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Schedule Data Deletion Wizard ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Title for the schedule data deletion wizard
scheduleDataDeletionTxt=排程資料刪除
#XBUT: Schedule Data Deletion Wizard
back=返回
#XBUT: Schedule Data Deletion Wizard
nextStep=下一步
#XBUT: Schedule Data Deletion Wizard
createSchedule=建立排程
#XBUT: Schedule Data Deletion Wizard
updateSchedule=更新排程
#XGRP: Schedule Data Deletion Wizard's 1st step
settingStep=設定
#XGRP: Schedule Data Deletion Wizard's 2nd step
scheduleStep=排程
#XGRP: Schedule Data Deletion Wizard's 3rd step
reviewStep=審查
#XBUT: Schedule Data Deletion Wizard
cancel=取消
#XBUT: Schedule Data Deletion Wizard
edit=編輯
#XFLD: Schedule Data Deletion Wizard
deleteAllRecordsFilteredBy=刪除下列篩選依據的所有記錄
#XFLD: Schedule Data Deletion Wizard
recurrence=週期
#XFLD: Schedule Data Deletion Wizard
nextRun=下一次執行
#XMSG: Error state for empty technical name
emptyLabel=請輸入有效排程名稱。
#XMSG: Error state for duplicate technical name
duplicateID=具有相同名稱的排程已存在。

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Filtered Deletions ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Create Filtered Deletion button
createFilteredDeletionBtn=建立篩選

#XFLD: Filtered Deletions Operators
EQ=等於
BT=介於
Contains=包含
GT=大於
GE=大於或等於
LT=小於
LE=小於或等於
StartsWith=開頭為
EndsWith=結尾為
Empty=空白
BXD=早於 {0} 天


#XFLD: Placeholder for filter input
txtDatePlaceholder=YYYY-MM-DD
txtTimePlaceholder=HH:mm:ss
txtDateTimePlaceholder=YYYY-MM-DD HH:mm:ss
txtStringPlaceholder=請輸入字串
txtNumberPlaceholder=請輸入數字
txtValuePlaceholder=請輸入值

#XMSG-Validation messages
txtEmptyFilterValue=請輸入有效的篩選值。
#XMSG: Error message for empty  value
txtEmptyValueForDDR=請以天數提供值。

#XMSG: lower bound error text
txtLowerbounderrormsg=下限必須小於上限。
#XMSG: lower bound error text for string
txtLowerbounderrormsgforString=字串「{0}」大於「{1}」。
#XMSG: higher bound error text
txtHigherbounderrormsg=上限必須大於下限。
#XMSG: higher bound error text for string
txtHigherbounderrormsgforString=字串「{1}」小於「{0}」。
#XMSG Error msg for missing values
VAL_LENGTH_EXCEED=值長度不可超過 {0}。
VAL_ENTER_VALID_INT=請輸入有效的整數。
VAL_ENTER_VALID_DECIMAL=請輸入總位數為 {0} 且小數位數為 {1} 的有效小數值。
VAL_ENTER_VALID_DECIMAL_VALUE=請輸入小數值。
VAL_ENTER_VALID_DATE=請輸入有效日期。
VAL_ENTER_VALID_NUMBER=請輸入有效數字。
VAL_DUPLICATE_FILTER=複製篩選值。
VAL_DEFAULT_RANGE_EXCEED_INT=請輸入介於 -2147483648 到 2147483647 之間的有效整數值。
VAL_DEFAULT_RANGE_EXCEED_BIGINT=請輸入介於 -9223372036854775808 到 9223372036854775807 之間的有效整數值。

#XMSG: Incase Current Watermark is not applicable
notApplicableText=不適用

#XTXT: Preview Data to be Deleted
previewDataToBeDeleted=待刪除的預覽資料

#XBTN: Close Preview
txtCloseBtn=關閉


#XBTN: Segmented button for filtered Data
txtFilteredData=篩選的資料

#XBTN: Segmented button for All Data
txtAllData=所有資料

txtDataViewer=資料檢視器
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ LTF ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XCOL: Buffer Merge Status
txtBufferMergeStatus=緩衝合併狀態

#XCOL: Buffer Last Updated
txtBufferLastUpdated=緩衝最後更新

#XCOL: Buffer Last Updated By
txtBufferLastUpdatedBy=緩衝最後更新者

#XCOL: Partitions
txtPartitions=分割

#XCOL: Determine the number of records
txtActiveRecords=啟用中記錄數量

#XCOL: Collect data via Spark (DeltaTable)
txtActiveRecordsFileStorage=啟用中記錄儲存 (MiB)

#XCOL: Calculate data via Spark
txtPreviousVersionsFileStorage=先前版本儲存 (MiB)

#XCOL: Collect the size of all files via Spark
txtTotalTableFileStorage=總計儲存 (MiB)

#XCOL: Collect the inbound buffer file size
txtInboundBufferFileSize=緩衝檔案大小 (MiB)

#XCOL: Collect the inbound buffer file count
txtInboundBufferFileCount=緩衝檔案計數

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Viewer dialog ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=欄設定
#XFLD: Title for Error type
messagesTableType=類型
#XFLD: Title for Error Message
messagesTableMessage=訊息
#XFLD: Title for filter
filteredBy=篩選依據：
#XTIT: text for values contained in filter
filterContains=包含
#XTIT: text for values starting with in filter
filterStartsWith=開頭為
#XTIT: text for values ending with in filter
filterEndsWith=結尾為
#XTIT: Title for search in data preview toolbar
toolbarSearch=搜尋
#XBUT: Button to clear filter
clearFilter=清除篩選
#XBUT: Button to save the operation
ok=確定
#XBUT: Button to restore the data
toolbarRestoreButton=重新整理
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=更新頁面
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=正在處理...
#XMSG: Message Confirmation
confirmation=確認
#XMSG: Message for refresh successful
refreshSuccess=已成功重新整理
#XMSG: Message for refresh successful
refreshSuccessful=已重新整理
#XMSG: Message for restore successful
restoreSuccessful=已成功還原
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" 不得空白。{1}。
#XTIT: Sort Ascending
mdm-sortAscending=升冪排序
#XTIT: Sort Descending
mdm-sortDescending=降冪排序
#XTIT: Filter
mdm-Filter=篩選
#XBUT: Button Cancel
mdm-cancel=取消
#XBUT: Button Add
mdm-Add=新增
#XMSG: and inside error message
and=和

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=未儲存的更改內容將停用篩選、排序、刪除和表格設定工具。請儲存更改內容後，才能啟用。
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=上傳資料
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=複製
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=刪除
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=新增

#XTIT: text to be apended for key columns in p13n dialog e.g. (Key) <Column Name>
mdm-keyText=鍵值
#XTIT: text to be apended for Not Null columns
mdm-notNullText=非 Null

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=插入缺少的字串值為：
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=NULL
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=空白字串
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=缺少字串值只能在新和編輯列中的可檢視字串欄插入。使用「欄設定」顯示所有相關欄。
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=未儲存的更改內容將停用篩選、排序、插入缺少字串值、和表格設定工具。請儲存更改內容後，才能啟用。
#XFLD: Open SAP HANA Cockpit

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Settings LSA ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
txtHeaderApacheSettings=Apache Spark 應用程式設定
txtMergeSectionTitle=合併
txtUseSpaceDefault=使用空間預設
txtApplication=應用程式
txtOptimizeSectionTitle=最佳化
txtNewSettings=定義此表格的新設定
txtNewSettingsMessageBox=定義此工作細項的新設定
txtUseDefault=使用預設
txtMergeMsgBoxTitle=合併表格
txtOptimizeMsgBoxTitle=最佳化表格
#XTIT: sub section header for LSA settings i.e Optimize settings
txtOptimizeSettings=最佳化設定
#XTIT: Z - order columns list header
txtZOrderList=Z 排序欄
#XBUT: Button to define z order columns
txtZOrderEdit=編輯
#XBUT: Button to save the z order columns
txtZOrderDelete=刪除
#XMSG: message for empty z order columns
txtNoZOrderList=沒有設定
#XMSG: message for empty z order list
txtEmptyZOrderList=透過定義 Z 排序欄來改善效能
#XBUT: Button to add z order columns
txtDefineZOrder=定義 Z 排序欄
#XLBL: Label for z order columns
txtColumnZOrder=欄
#XMSG: error message to inform user about HANA limitation
zOrderLimitationError=您僅可選擇一個 Z 順序欄。

#XMSG: message for conflicting task
taskAlreadyRunning=物件 "{0}" 的衝突工作細項執行中。

#XMSG: Save spark settings success
saveSparkSettingsSuccess=已儲存 Apache Spark 應用程式設定。
