
msgDataDeletionStarted=Бришемо податке табеле "{0}"

#XMSG: message to inform table not found in the repository
localTableNotFound=Није могуће наћи локалну табелу {0} у репозиторијуму.

#XFLD: Used Disk
txtUsedDisk=Искоришћени простор на диску (MiB)
txtSizeOnDisk=Величина на диску (MiB)
#XFLD: Used In-Memory
txtUsedInMemory=Искоришћеност за In-Memory (MiB)
txtSizeInMemory=Величина у меморији (MiB)
#XFLD: Number of Records
txtNumberOfRecords=Број записа
#XFLD: Delta capture
txtDeltaCapture=Делта снимање
#XFLD: In-Memory storage
txtInMemoryStorage=In-Memory складиштење
#XFLD: Technical Name
txtTechnicalName=Технички назив
#XFLD: Business Name
txtBusinessName=Пословни назив
#XFLD: Growth Rate (Last Month)
txtgrowthRate=Раст за 30 дана
#XFLD: Number of Partitions
txtnumberofpartitions=Број партиција
#XFLD: Last Updated
txtlastUpdatedBy=Последњи пут ажурирао
#XFLD: Last Updated
txtlastupdated=Последњи пут ажурирано
#XFLD:delta Capture
txtdeltaCapture=Делта снимање
txtYes=Да
txtNo=Не
txtOn=Укључено
txtOff=Искључено
localTablesHeaderTitle=Локалне табеле ({0})
localTablesLTFHeaderTitle=Локалне табеле (фајл) ({0})
txtSearch=Тражи
txtRefresh=Освежи
txtNotApplicable=Није применљиво
txtObject=Објекат ({0})

#XTOL:for Last updated column header
tolLastUpdated=Показује када су подаци последњи пут ажурирани
#XTOL: for Last updated by column header
tolLastUpdatedBy=Показује када су подаци последњи пут ажурирани
#XTOL: for Last buffer updated column header
tolBufferLastUpdated=Показује када су подаци у улазној међумеморији последњи пут ажурирани
#XTOL: for Last buffer updated by column header
tolBufferLastUpdatedBy=Показује како су подаци у улазној међумеморији последњи пут ажурирани


#XMSG: message to inform repo Access Error
txtRepoAccessError=Репозиторијум није доступан и нека својства су деактивирана.

#XBUT: Label for open in Editor
openInEditorNew=Отвори у генератору података

#XTOL: Toggle button to open data editor
openDataEditor=Уређивач података

#XBUT: Radio button group
txtDeleteAllRecords=Избриши све записе
txtDeleteFilteredRecords=Избриши филтриране записе
txtRecordsMarkedForDeleted=Избриши записе означене као "Избрисано"

#XTXT: preview
txtPreview=Претходни приказ
#XMSG: Warning message for All Records delete
dataDeleteWarningMsgTxt=Брисање записа може утицати на коришћење токова
#XMSG: Warning popup header
@confirmDataDeletion=Избриши податке
#XMSG: Warning popup message
@dataDeletionText=Ова радња ће избрисати записе табеле, што може утицати на токове коришћења. То се не може поништити. Да ли желите да наставите?

#XBUT: Data Deletion warning
btnDeleteConfirm=Избриши

#XFLD: Label for table column
durationInSeconds=секунди

#XMSG: Records marked as "Deleted" description
recordsMarkedForDeletionDescription=Избришите све потпуно обрађене записе с типом промене "Избрисано" старије од
daysForDeltaTableDeletion=Дани
txtCurrentWatermark=Тренутни водени жиг

#XBUT: show preview button in filter
datViewerBtn=Отвори Прегледач података

#XFLD: Number of deletable records
txtNumberOfDeletableRecords=Број записа који се могу избрисати

#XFLD: Size of deletable records
txtSizeOfDeletableRecords=Величина записа који се могу избрисати (MiB)

numberOfRecordsRefresh=Освежи

#XTXT: Data Deletion Logs
txtDataDeletionLogs=Протоколи брисања података

#XTXT: Data Deletion Schedules
txtDataDeletionSchedules=Распореди брисања података

#XTXT: Data Deletion
txtDataDeletion=Брисање података

#XBTN: Delete Records
deleteRecords=Избриши записе
#XBTN: Create
createbtn=Креирај
#XBTN: Edit
editBtn=Уреди
#XBTN: Delete
deleteBtn=Избриши
#XTXT: Placeholder for search bar
txtSearchPlaceholder=Тражи

#XTXT: Data Deletion
txtDeletionSettings=Подешавања брисања

currentWatermark=Тренутни водени жиг

#XBUT: Select Columns Button
selectColumnsBtn=Одабери колоне

#XBUT: Merge table for ltf table
txtMergeTable=Спој табелу

#XBUT: Optimize table for ltf table
txtOptimizeTable=Оптимизуј табелу

#XBUT: schedule Merge and Optimize for ltf table
scheduleTextLabel=Распоред

#XBUT: Drop down menu button to create a schedule
createScheduleNewLabel=Креирај распоред
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Уреди распоред
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Избриши распоред
#XBUT: Assign schedule menu button label
assignScheduleLabel=Додели ми распоред
#XBUT: Pause schedule menu label
pauseScheduleLabel=Паузирај распоред
#XBUT: Resume schedule menu label
resumeScheduleLabel=Настави распоред

#XMSG: message to say that merge task is started
msgMergeTaskStarted=Задатак спајања је започет за табелу "{0}"

#XMSG: message to say that optimize task is started
msgOptimizeTaskStarted=Задатак оптимизације је започет за табелу "{0}"

#XTXT: title for object page for logs
hdlfLogsObjectPageTxt=Протоколи

#XTXT: title for object page for settings
hdlfSettingsObjectPageTxt=Подешавања
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Delete Logs Table~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Label for Data delete logs table
txtStartColumn=Почетак
txtStartColumnTooltip=Почетак
#XFLD: Label for Data delete logs table
txtDurationColumn=Трајање
txtDurationColumnTooltip=Трајање
#XFLD: Label for Data delete logs table
txtObjectTypeColumn=Тип објекта
txtObjectTypeColumnTooltip=Тип објекта
#XFLD: Label for Data delete logs table
txtActivityColumn=Активност
txtActivityColumnTooltip=Активност
#XFLD: Label for Data delete logs table
txtUserColumn=Корисник
txtUserColumnTooltip=Корисник
txtRunStartedBy=Извођење покренуо/ла
txtManualRun=Ручно
txtScheduledRun=Планирано
#XFLD: Label for Data delete logs table
txtNumberOfRecordsColumn=Број записа
txtNumberOfRecordsColumnTooltip=Број записа
#XFLD: Label for Data delete logs table
txtStatusColumn=Статус
txtStatusColumnTooltip=Статус

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Delete Logs Filter Dialog ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Последњи сат
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=Последња 24 сата
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Последњи месец

#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Више од 5 минута
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Више од 15 минута
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Више од 1 сата

#XFLD: status text
COMPLETED=Завршено
#XFLD: status text
FAILED=Није успело
#XFLD: status text
RUNNING=Изводи се

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Deletion Schedules Table~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XCOL: Type of Data deletion schedule
deletionScheduleType=Тип
#XCOL: Name of Data deletion schedule
deletionScheduleName=Назив
#XCOL: Frequency of schedule
deletionScheduleFrequency=Учесталост
#XCOL: DateTime of when the last run started
deletionScheduleLastRunStart=Почетак последњег извођења
#XCOL: DateTime of when the last run ended
deletionScheduleLastRunEnd=Завршетак последњег извођења
#XCOL: Number of records deleted in the last scheduled run
deletionScheduleRecordsLastRun=Записи (последње извођење)
#XBUT: To create a data deletion schedule
createDataDeletionScheduleBtn=Креирај распоред брисања података
#XMSG: Data Deletion Schedules Table
noDataDeletionScheduleText=Распореди брисања података још нису дефинисани
#XMSG: Data Deletion Schedules Table
noDataDeletionScheduleDescription=Креирајте распореде брисања података да бисте редовно уклањали податке који вам нису потребни
#XFLD: Frequency if a schedule is paused
paused:Паузирано

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Schedule Data Deletion Wizard ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Title for the schedule data deletion wizard
scheduleDataDeletionTxt=Закажи брисање података
#XBUT: Schedule Data Deletion Wizard
back=Назад
#XBUT: Schedule Data Deletion Wizard
nextStep=Следећи корак
#XBUT: Schedule Data Deletion Wizard
createSchedule=Креирај распоред
#XBUT: Schedule Data Deletion Wizard
updateSchedule=Ажурирај распоред
#XGRP: Schedule Data Deletion Wizard's 1st step
settingStep=Подешавања
#XGRP: Schedule Data Deletion Wizard's 2nd step
scheduleStep=Распоред
#XGRP: Schedule Data Deletion Wizard's 3rd step
reviewStep=Преглед
#XBUT: Schedule Data Deletion Wizard
cancel=Одустани
#XBUT: Schedule Data Deletion Wizard
edit=Уреди
#XFLD: Schedule Data Deletion Wizard
deleteAllRecordsFilteredBy=Избриши све записе филтриране по
#XFLD: Schedule Data Deletion Wizard
recurrence=Понављање
#XFLD: Schedule Data Deletion Wizard
nextRun=Следеће извођење
#XMSG: Error state for empty technical name
emptyLabel=Унесите важећи назив распореда.
#XMSG: Error state for duplicate technical name
duplicateID=Распоред са истим називом већ постоји.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Filtered Deletions ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Create Filtered Deletion button
createFilteredDeletionBtn=Креирај филтер

#XFLD: Filtered Deletions Operators
EQ=Једнако
BT=Између
Contains=Садржи
GT=Веће од
GE=Веће од или једнако
LT=Мање од
LE=Мање од или једнако
StartsWith=Почиње са
EndsWith=Завршава се са
Empty=Празно
BXD=Пре {0} дана


#XFLD: Placeholder for filter input
txtDatePlaceholder=YYYY-MM-DD
txtTimePlaceholder=HH:mm:ss
txtDateTimePlaceholder=YYYY-MM-DD HH:mm:ss
txtStringPlaceholder=Унесите низ
txtNumberPlaceholder=Унесите број
txtValuePlaceholder=Унесите вредност

#XMSG-Validation messages
txtEmptyFilterValue=Унесите важећу вредност филтера.
#XMSG: Error message for empty  value
txtEmptyValueForDDR=Наведите вредност у данима.

#XMSG: lower bound error text
txtLowerbounderrormsg=Доња граница мора бити нижа од горње границе.
#XMSG: lower bound error text for string
txtLowerbounderrormsgforString=Низ "{0}" је већи од "{1}".
#XMSG: higher bound error text
txtHigherbounderrormsg=Горња граница мора бити виша од доње границе.
#XMSG: higher bound error text for string
txtHigherbounderrormsgforString=Низ "{1}" је мањи од "{0}".
#XMSG Error msg for missing values
VAL_LENGTH_EXCEED=Дужина вредности не сме да прекорачи {0}.
VAL_ENTER_VALID_INT=Унесите важећи цео број.
VAL_ENTER_VALID_DECIMAL=Унесите важећу децималну вредност с прецизношћу {0} и скалом {1}.
VAL_ENTER_VALID_DECIMAL_VALUE=Унесите децималну вредност.
VAL_ENTER_VALID_DATE=Унесите важећи датум.
VAL_ENTER_VALID_NUMBER=Унесите важећи број.
VAL_DUPLICATE_FILTER=Дуплирајте вредност филтера.
VAL_DEFAULT_RANGE_EXCEED_INT=Унесите важећи цео број с вредношћу између -2147483648 и 2147483647.
VAL_DEFAULT_RANGE_EXCEED_BIGINT=Унесите важећи цео број с вредношћу између -9223372036854775808 и 9223372036854775807.

#XMSG: Incase Current Watermark is not applicable
notApplicableText=Није применљиво

#XTXT: Preview Data to be Deleted
previewDataToBeDeleted=Претходно прикажи податке за брисање

#XBTN: Close Preview
txtCloseBtn=Затвори


#XBTN: Segmented button for filtered Data
txtFilteredData=Филтрирани подаци

#XBTN: Segmented button for All Data
txtAllData=Сви подаци

txtDataViewer=Прегледач података
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ LTF ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XCOL: Buffer Merge Status
txtBufferMergeStatus=Статус спајања међумеморије

#XCOL: Buffer Last Updated
txtBufferLastUpdated=Међумеморија последњи пут ажурирана

#XCOL: Buffer Last Updated By
txtBufferLastUpdatedBy=Међумеморију последњи ажурирао

#XCOL: Partitions
txtPartitions=Партиције

#XCOL: Determine the number of records
txtActiveRecords=Број активних записа

#XCOL: Collect data via Spark (DeltaTable)
txtActiveRecordsFileStorage=Складиште активних записа (MiB)

#XCOL: Calculate data via Spark
txtPreviousVersionsFileStorage=Складиште претходних верзија (MiB)

#XCOL: Collect the size of all files via Spark
txtTotalTableFileStorage=Укупно складиште (MiB)

#XCOL: Collect the inbound buffer file size
txtInboundBufferFileSize=Величина фајла међумеморије (MiB)

#XCOL: Collect the inbound buffer file count
txtInboundBufferFileCount=Број фајлова међумеморије

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Viewer dialog ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=Подешавања колона
#XFLD: Title for Error type
messagesTableType=Тип
#XFLD: Title for Error Message
messagesTableMessage=Порука
#XFLD: Title for filter
filteredBy=Филтрирано по:
#XTIT: text for values contained in filter
filterContains=садржи
#XTIT: text for values starting with in filter
filterStartsWith=почиње са
#XTIT: text for values ending with in filter
filterEndsWith=завршава се са
#XTIT: Title for search in data preview toolbar
toolbarSearch=Тражи
#XBUT: Button to clear filter
clearFilter=Поништи филтер
#XBUT: Button to save the operation
ok=ОК
#XBUT: Button to restore the data
toolbarRestoreButton=Освежи
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=Ажурирај страницу
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=Обрада...
#XMSG: Message Confirmation
confirmation=Потврда
#XMSG: Message for refresh successful
refreshSuccess=Успешно освежено
#XMSG: Message for refresh successful
refreshSuccessful=Освежено
#XMSG: Message for restore successful
restoreSuccessful=Успешно обновљено
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" не сме бити празно. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=Поређај по растућем редоследу
#XTIT: Sort Descending
mdm-sortDescending=Поређај по опадајућем редоследу
#XTIT: Filter
mdm-Filter=Филтрирај
#XBUT: Button Cancel
mdm-cancel=Одустани
#XBUT: Button Add
mdm-Add=Додај
#XMSG: and inside error message
and=и

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=Алати Филтрирај, Поређај, Избриши и Подешавања табеле су деактивирани јер промене нису сачуване. Сачувајте промене које сте извршили да бисте их активирали.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=Пренеси податке на сервер
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=Дуплирај
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=Избриши
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=Додај

#XTIT: text to be apended for key columns in p13n dialog e.g. (Key) <Column Name>
mdm-keyText=Кључ
#XTIT: text to be apended for Not Null columns
mdm-notNullText=Није без вредности

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=Унеси вредност низа која недостаје као:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=БЕЗ ВРЕДНОСТИ
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=Празан низ
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=Вредности низа које недостају унете су само у видљиве колоне низа у новим и уређеним редовима. Користите подешавања колона за приказ свих релевантних колона.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=Алати Филтрирај, Поређај, Унеси вредност низа која недостаје и Подешавања табеле су деактивирани јер промене нису сачуване. Сачувајте промене које сте извршили да бисте их активирали.
#XFLD: Open SAP HANA Cockpit

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Settings LSA ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
txtHeaderApacheSettings=Подешавања апликације Apache Spark 
txtMergeSectionTitle=Спој
txtUseSpaceDefault=Користи стандардни простор
txtApplication=Апликација
txtOptimizeSectionTitle=Оптимизирај
txtNewSettings=Дефиниши ново подешавање за ову табелу
txtNewSettingsMessageBox=Дефиниши нова подешавања за овај задатак
txtUseDefault=Користи стандардну вредност
txtMergeMsgBoxTitle=Спој табелу
txtOptimizeMsgBoxTitle=Оптимизирај табелу
#XTIT: sub section header for LSA settings i.e Optimize settings
txtOptimizeSettings=Оптимизуј подешавања
#XTIT: Z - order columns list header
txtZOrderList=Колоне Z-редоследа
#XBUT: Button to define z order columns
txtZOrderEdit=Уреди
#XBUT: Button to save the z order columns
txtZOrderDelete=Избриши
#XMSG: message for empty z order columns
txtNoZOrderList=Подешавања не постоје
#XMSG: message for empty z order list
txtEmptyZOrderList=Побољшајте учинак дефинисањем колона Z-редоследа
#XBUT: Button to add z order columns
txtDefineZOrder=Колоне Z-редоследа
#XLBL: Label for z order columns
txtColumnZOrder=Колоне
#XMSG: error message to inform user about HANA limitation
zOrderLimitationError=Можете одабрати само једну колону Z-редоследа.

#XMSG: message for conflicting task
taskAlreadyRunning=Задатак у конфликту се већ изводи за објекат "{0}".

#XMSG: Save spark settings success
saveSparkSettingsSuccess=Подешавања апликације Apache Spark сачувана.
