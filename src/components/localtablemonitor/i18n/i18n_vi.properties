
msgDataDeletionStarted=Chúng tôi đang xóa dữ liệu của bảng "{0}"

#XMSG: message to inform table not found in the repository
localTableNotFound=Không tìm thấy bảng cục bộ {0} trong kho lưu trữ.

#XFLD: Used Disk
txtUsedDisk=Ổ đĩa đã sử dụng (MiB)
txtSizeOnDisk=Kích thước ổ đĩa (MiB)
#XFLD: Used In-Memory
txtUsedInMemory=Bộ nhớ trong đã sử dụng (MiB)
txtSizeInMemory=Kích thước bộ nhớ trong (MiB)
#XFLD: Number of Records
txtNumberOfRecords=Số bản ghi
#XFLD: Delta capture
txtDeltaCapture=Thu thập chênh lệch
#XFLD: In-Memory storage
txtInMemoryStorage=Lưu trữ bộ nhớ trong
#XFLD: Technical Name
txtTechnicalName=Tên kỹ thuật
#XFLD: Business Name
txtBusinessName=Tên doanh nghiệp
#XFLD: Growth Rate (Last Month)
txtgrowthRate=Tăng trưởng trong 30 ngày
#XFLD: Number of Partitions
txtnumberofpartitions=Số phân vùng
#XFLD: Last Updated
txtlastUpdatedBy=Cập nhật lần cuối bởi
#XFLD: Last Updated
txtlastupdated=Cập nhật lần cuối
#XFLD:delta Capture
txtdeltaCapture=Thu thập chênh lệch
txtYes=Có
txtNo=Không
txtOn=Bật
txtOff=Tắt
localTablesHeaderTitle=Bảng cục bộ ({0})
localTablesLTFHeaderTitle=Bảng cục bộ (tập tin) ({0})
txtSearch=Tìm kiếm
txtRefresh=Làm mới
txtNotApplicable=Không áp dụng
txtObject=Đối tượng ({0})

#XTOL:for Last updated column header
tolLastUpdated=Cho biết thời điểm dữ liệu đã được cập nhật lần cuối
#XTOL: for Last updated by column header
tolLastUpdatedBy=Cho biết dữ liệu đã được cập nhật lần cuối như thế nào
#XTOL: for Last buffer updated column header
tolBufferLastUpdated=Cho biết dữ liệu trong bộ đệm nhập vào đã được cập nhật lần cuối khi nào
#XTOL: for Last buffer updated by column header
tolBufferLastUpdatedBy=Cho biết dữ liệu trong bộ đệm nhập vào đã được cập nhật lần cuối như thế nào


#XMSG: message to inform repo Access Error
txtRepoAccessError=Kho lưu trữ không có sẵn và một số tính năng nhất định bị tắt.

#XBUT: Label for open in Editor
openInEditorNew=Mở trong Trình tạo dữ liệu

#XTOL: Toggle button to open data editor
openDataEditor=Trình soạn thảo dữ liệu

#XBUT: Radio button group
txtDeleteAllRecords=Xóa tất cả các bản ghi
txtDeleteFilteredRecords=Xóa bản ghi được lọc
txtRecordsMarkedForDeleted=Xóa bản ghi được đánh dấu là "Đã xóa"

#XTXT: preview
txtPreview=Xem trước
#XMSG: Warning message for All Records delete
dataDeleteWarningMsgTxt=Xóa bản ghi có thể ảnh hưởng đến luồng sử dụng.
#XMSG: Warning popup header
@confirmDataDeletion=Xóa dữ liệu
#XMSG: Warning popup message
@dataDeletionText=Thao tác này sẽ xóa các bản ghi bảng có thể ảnh hưởng đến luồng tiêu thụ. Không thể hoàn tác thao tác này. Bạn có muốn tiếp tục không?

#XBUT: Data Deletion warning
btnDeleteConfirm=Xóa

#XFLD: Label for table column
durationInSeconds=giây

#XMSG: Records marked as "Deleted" description
recordsMarkedForDeletionDescription=Xóa tất cả bản ghi được xử lý hoàn toàn với loại thay đổi 'Đã xóa', cũ hơn
daysForDeltaTableDeletion=Ngày
txtCurrentWatermark=Hình mờ hiện tại

#XBUT: show preview button in filter
datViewerBtn=Mở trình xem dữ liệu

#XFLD: Number of deletable records
txtNumberOfDeletableRecords=Số bản ghi có thể xóa

#XFLD: Size of deletable records
txtSizeOfDeletableRecords=Kích thước bản ghi có thể xóa (MiB)

numberOfRecordsRefresh=Làm mới

#XTXT: Data Deletion Logs
txtDataDeletionLogs=Nhật ký xóa dữ liệu

#XTXT: Data Deletion Schedules
txtDataDeletionSchedules=Lịch xóa dữ liệu

#XTXT: Data Deletion
txtDataDeletion=Xóa dữ liệu

#XBTN: Delete Records
deleteRecords=Xóa bản ghi
#XBTN: Create
createbtn=Tạo
#XBTN: Edit
editBtn=Hiệu chỉnh
#XBTN: Delete
deleteBtn=Xóa
#XTXT: Placeholder for search bar
txtSearchPlaceholder=Tìm kiếm

#XTXT: Data Deletion
txtDeletionSettings=Thiết lập xóa

currentWatermark=Hình mờ hiện tại

#XBUT: Select Columns Button
selectColumnsBtn=Chọn cột

#XBUT: Merge table for ltf table
txtMergeTable=Sáp nhập bảng

#XBUT: Optimize table for ltf table
txtOptimizeTable=Tối ưu hóa bảng

#XBUT: schedule Merge and Optimize for ltf table
scheduleTextLabel=Lịch

#XBUT: Drop down menu button to create a schedule
createScheduleNewLabel=Tạo lịch
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Hiệu chỉnh lịch
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Xóa lịch
#XBUT: Assign schedule menu button label
assignScheduleLabel=Gán lịch cho tôi
#XBUT: Pause schedule menu label
pauseScheduleLabel=Tạm dừng lịch
#XBUT: Resume schedule menu label
resumeScheduleLabel=Tiếp tục lại lịch

#XMSG: message to say that merge task is started
msgMergeTaskStarted=Tác vụ sáp nhập đã được bắt đầu cho bảng "{0}"

#XMSG: message to say that optimize task is started
msgOptimizeTaskStarted=Tác vụ tối ưu hóa đã được bắt đầu cho bảng "{0}"

#XTXT: title for object page for logs
hdlfLogsObjectPageTxt=Nhật ký

#XTXT: title for object page for settings
hdlfSettingsObjectPageTxt=Cài đặt
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Delete Logs Table~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Label for Data delete logs table
txtStartColumn=Bắt đầu
txtStartColumnTooltip=Bắt đầu
#XFLD: Label for Data delete logs table
txtDurationColumn=Khoảng thời gian
txtDurationColumnTooltip=Khoảng thời gian
#XFLD: Label for Data delete logs table
txtObjectTypeColumn=Kiểu đối tượng
txtObjectTypeColumnTooltip=Kiểu đối tượng
#XFLD: Label for Data delete logs table
txtActivityColumn=Hoạt động
txtActivityColumnTooltip=Hoạt động
#XFLD: Label for Data delete logs table
txtUserColumn=Người dùng
txtUserColumnTooltip=Người dùng
txtRunStartedBy=Bắt đầu thực hiện bởi
txtManualRun=Thủ công
txtScheduledRun=Đã lập lịch
#XFLD: Label for Data delete logs table
txtNumberOfRecordsColumn=Số bản ghi
txtNumberOfRecordsColumnTooltip=Số bản ghi
#XFLD: Label for Data delete logs table
txtStatusColumn=Trạng thái
txtStatusColumnTooltip=Trạng thái

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Delete Logs Filter Dialog ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XTXT: Filter Label for runs executed in Last one Hour
filterStart1HourText=Giờ vừa qua
#XTXT: Filter Label for runs executed in Last one Day
filterStart24HoursText=24 giờ qua
#XTXT: Filter Label for runs executed in Last one Month
filterStartMonthText=Tháng trước

#XTXT: Filter Duration label for More than 5 minutes
filterDuration5MinText=Nhiều hơn 5 phút
#XTXT: Filter Duration label for More than 15 minutes
filterDuration15MinText=Nhiều hơn 15 phút
#XTXT: Filter Duration label for More than 1 hour
filterDuration1HourText=Nhiều hơn 1 giờ

#XFLD: status text
COMPLETED=Đã hoàn tất
#XFLD: status text
FAILED=Không thành công
#XFLD: status text
RUNNING=Đang chạy

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Deletion Schedules Table~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XCOL: Type of Data deletion schedule
deletionScheduleType=Kiểu
#XCOL: Name of Data deletion schedule
deletionScheduleName=Tên
#XCOL: Frequency of schedule
deletionScheduleFrequency=Tần suất
#XCOL: DateTime of when the last run started
deletionScheduleLastRunStart=Bắt đầu thực hiện lần cuối
#XCOL: DateTime of when the last run ended
deletionScheduleLastRunEnd=Kết thúc thực hiện lần cuối
#XCOL: Number of records deleted in the last scheduled run
deletionScheduleRecordsLastRun=Bản ghi (Thực hiện lần cuối)
#XBUT: To create a data deletion schedule
createDataDeletionScheduleBtn=Tạo lịch xóa dữ liệu
#XMSG: Data Deletion Schedules Table
noDataDeletionScheduleText=Chưa xác định lịch xóa dữ liệu
#XMSG: Data Deletion Schedules Table
noDataDeletionScheduleDescription=Tạo lịch xóa dữ liệu để loại bỏ thường xuyên dữ liệu bạn không cần
#XFLD: Frequency if a schedule is paused
paused:Đã tạm dừng

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Schedule Data Deletion Wizard ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XFLD: Title for the schedule data deletion wizard
scheduleDataDeletionTxt=Lập lịch xóa dữ liệu
#XBUT: Schedule Data Deletion Wizard
back=Quay lại
#XBUT: Schedule Data Deletion Wizard
nextStep=Bước kế tiếp
#XBUT: Schedule Data Deletion Wizard
createSchedule=Tạo lịch
#XBUT: Schedule Data Deletion Wizard
updateSchedule=Cập nhật lịch
#XGRP: Schedule Data Deletion Wizard's 1st step
settingStep=Thiết lập
#XGRP: Schedule Data Deletion Wizard's 2nd step
scheduleStep=Lập lịch
#XGRP: Schedule Data Deletion Wizard's 3rd step
reviewStep=Xem lại
#XBUT: Schedule Data Deletion Wizard
cancel=Hủy
#XBUT: Schedule Data Deletion Wizard
edit=Hiệu chỉnh
#XFLD: Schedule Data Deletion Wizard
deleteAllRecordsFilteredBy=Xóa tất cả bản ghi được lọc theo
#XFLD: Schedule Data Deletion Wizard
recurrence=Lặp lại
#XFLD: Schedule Data Deletion Wizard
nextRun=Thực hiện kế tiếp
#XMSG: Error state for empty technical name
emptyLabel=Vui lòng nhập tên lịch hợp lệ.
#XMSG: Error state for duplicate technical name
duplicateID=Đã tồn tại lịch trình có cùng tên.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Filtered Deletions ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XBUT: Create Filtered Deletion button
createFilteredDeletionBtn=Tạo bộ lọc

#XFLD: Filtered Deletions Operators
EQ=Bằng
BT=Giữa
Contains=Bao gồm
GT=Lớn hơn
GE=Lớn hơn hoặc bằng
LT=Nhỏ hơn
LE=Nhỏ hơn hoặc bằng
StartsWith=Bắt đầu bằng
EndsWith=Kết thúc bằng
Empty=Trống
BXD=Trước {0} ngày


#XFLD: Placeholder for filter input
txtDatePlaceholder=YYYY-MM-DD
txtTimePlaceholder=HH:mm:ss
txtDateTimePlaceholder=YYYY-MM-DD HH:mm:ss
txtStringPlaceholder=Nhập chuỗi
txtNumberPlaceholder=Nhập số
txtValuePlaceholder=Nhập giá trị

#XMSG-Validation messages
txtEmptyFilterValue=Nhập giá trị bộ lọc hợp lệ.
#XMSG: Error message for empty  value
txtEmptyValueForDDR=Cung cấp giá trị theo ngày.

#XMSG: lower bound error text
txtLowerbounderrormsg=Giới hạn dưới phải thấp hơn giới hạn trên.
#XMSG: lower bound error text for string
txtLowerbounderrormsgforString=Chuỗi "{0}" lớn hơn "{1}".
#XMSG: higher bound error text
txtHigherbounderrormsg=Giới hạn trên phải cao hơn giới hạn dưới.
#XMSG: higher bound error text for string
txtHigherbounderrormsgforString=Chuỗi "{1}" nhỏ hơn "{0}".
#XMSG Error msg for missing values
VAL_LENGTH_EXCEED=Độ dài của giá trị không được vượt quá {0}.
VAL_ENTER_VALID_INT=Nhập số nguyên hợp lệ.
VAL_ENTER_VALID_DECIMAL=Nhập giá trị thập phân hợp lệ với độ chính xác {0} và tỷ lệ {1}.
VAL_ENTER_VALID_DECIMAL_VALUE=Nhập giá trị thập phân.
VAL_ENTER_VALID_DATE=Nhập ngày hợp lệ.
VAL_ENTER_VALID_NUMBER=Nhập một số hợp lệ.
VAL_DUPLICATE_FILTER=Sao chép giá trị bộ lọc.
VAL_DEFAULT_RANGE_EXCEED_INT=Nhập số nguyên hợp lệ có giá trị từ -2147483648 đến 2147483647.
VAL_DEFAULT_RANGE_EXCEED_BIGINT=Nhập số nguyên hợp lệ có giá trị từ -9223372036854775808 đến 9223372036854775807.

#XMSG: Incase Current Watermark is not applicable
notApplicableText=Không áp dụng

#XTXT: Preview Data to be Deleted
previewDataToBeDeleted=Xem trước dữ liệu sẽ bị xóa

#XBTN: Close Preview
txtCloseBtn=Đóng


#XBTN: Segmented button for filtered Data
txtFilteredData=Dữ liệu được lọc

#XBTN: Segmented button for All Data
txtAllData=Tất cả dữ liệu

txtDataViewer=Trình xem dữ liệu
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ LTF ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XCOL: Buffer Merge Status
txtBufferMergeStatus=Trạng thái sáp nhập bộ đệm

#XCOL: Buffer Last Updated
txtBufferLastUpdated=Bộ đệm được cập nhật lần cuối

#XCOL: Buffer Last Updated By
txtBufferLastUpdatedBy=Bộ đệm được cập nhật lần cuối bởi

#XCOL: Partitions
txtPartitions=Phân vùng

#XCOL: Determine the number of records
txtActiveRecords=Số lượng bản ghi đang hoạt động

#XCOL: Collect data via Spark (DeltaTable)
txtActiveRecordsFileStorage=Lưu trữ bản ghi đang hoạt động (MiB)

#XCOL: Calculate data via Spark
txtPreviousVersionsFileStorage=Lưu trữ phiên bản trước đó (MiB)

#XCOL: Collect the size of all files via Spark
txtTotalTableFileStorage=Tổng lưu trữ (MiB)

#XCOL: Collect the inbound buffer file size
txtInboundBufferFileSize=Kích thước tập tin bộ đệm (MiB)

#XCOL: Collect the inbound buffer file count
txtInboundBufferFileCount=Tổng số tập tin bộ đệm

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Data Viewer dialog ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#Localization for @sap/dragonet-ui5-table-editor node module
#XTIT: Title for column settings dialog
columnsSettingsDialogTitle=Thiết lập cột
#XFLD: Title for Error type
messagesTableType=Loại
#XFLD: Title for Error Message
messagesTableMessage=Thông báo
#XFLD: Title for filter
filteredBy=Được lọc theo:
#XTIT: text for values contained in filter
filterContains=chứa
#XTIT: text for values starting with in filter
filterStartsWith=bắt đầu bằng
#XTIT: text for values ending with in filter
filterEndsWith=kết thúc bằng
#XTIT: Title for search in data preview toolbar
toolbarSearch=Tìm kiếm
#XBUT: Button to clear filter
clearFilter=Xóa bộ lọc
#XBUT: Button to save the operation
ok=OK
#XBUT: Button to restore the data
toolbarRestoreButton=Làm mới
#XBUT: tooltip for Button to restore the data
toolbarRestoreButtonTooltip=Cập nhật trang
#XMSG: Busy dialog for Columns Settings dialog
PROCESSING=Đang xử lý...
#XMSG: Message Confirmation
confirmation=Xác nhận
#XMSG: Message for refresh successful
refreshSuccess=Đã làm mới thành công
#XMSG: Message for refresh successful
refreshSuccessful=Đã làm mới
#XMSG: Message for restore successful
restoreSuccessful=Được khôi phục thành công
#XMSG: Error message when null value found on non-nullable field
mdm-emptyValueError="{0}" không thể để trống. {1}.
#XTIT: Sort Ascending
mdm-sortAscending=Sắp xếp tăng dần
#XTIT: Sort Descending
mdm-sortDescending=Sắp xếp giảm dần
#XTIT: Filter
mdm-Filter=Bộ lọc
#XBUT: Button Cancel
mdm-cancel=Hủy
#XBUT: Button Add
mdm-Add=Thêm
#XMSG: and inside error message
and=và

#Localization keys not needed for System Monitor but required for @sap/dragonet-ui5-table-editor node module to remove console errors
#XTXT: Message information strip for filtering and sorting
mdm-InfoStrip=Các công cụ Lọc, Sắp xếp, Xóa và Cài đặt bảng bị tắt bởi các thay đổi chưa lưu. Hãy lưu thay đổi để bật các công cụ này.
#XBUT: Button for file upload in mdm data preview
toolbarUploadButton=Tải lên dữ liệu
#XBUT: Button for duplicating rows in mdm data preview
toolbarDuplicateButton=Sao lại
#XBUT: Button for deleting rows in mdm data preview
toolbarDeleteButton=Xóa
#XBUT: Button for adding rows in mdm data preview
toolbarAddButton=Thêm

#XTIT: text to be apended for key columns in p13n dialog e.g. (Key) <Column Name>
mdm-keyText=Khóa
#XTIT: text to be apended for Not Null columns
mdm-notNullText=Không rỗng

#XTIT: MDM Empty value Handling in Table Editor Label
mdmInsertMissingString=Chèn giá trị chuỗi bị thiếu dưới dạng:
#XTIT: MDM Empty value Handling in Table Editor Null Combobox item
mdmNullText=NULL
#XTIT: MDM Empty value Handling in Table Editor Empty string Combobox item
mdmEmptyStringText=Chuỗi trống
#XTXT: Message information strip for Insert Missing String Value
mdmInfoStripMissingString=Chỉ có thể chèn giá trị chuỗi bị thiếu vào cột chuỗi nhìn thấy trong hàng mới và hàng được hiệu chỉnh. Hãy sử dụng Cài đặt cột để hiển thị tất cả các cột phù hợp.
#XTXT: Message information strip for filtering sorting and Insert Missing String Value
mdm-InfoStripWithMissingString=Các công cụ Lọc, Sắp xếp, Chèn giá trị chuỗi bị thiếu và Cài đặt bảng bị tắt bởi các thay đổi chưa lưu. Hãy lưu thay đổi để bật các công cụ này.
#XFLD: Open SAP HANA Cockpit

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Settings LSA ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
txtHeaderApacheSettings=Cài đặt ứng dụng Apache Spark
txtMergeSectionTitle=Sáp nhập
txtUseSpaceDefault=Sử dụng vùng dữ liệu mặc định
txtApplication=Ứng dụng
txtOptimizeSectionTitle=Tối ưu hóa
txtNewSettings=Xác định cài đặt mới cho bảng này
txtNewSettingsMessageBox=Xác định cài đặt mới cho nhiệm vụ này
txtUseDefault=Sử dụng mặc định
txtMergeMsgBoxTitle=Sáp nhập bảng
txtOptimizeMsgBoxTitle=Tối ưu hóa bảng
#XTIT: sub section header for LSA settings i.e Optimize settings
txtOptimizeSettings=Tối ưu hóa thiết lập
#XTIT: Z - order columns list header
txtZOrderList=Cột theo thứ tự Z
#XBUT: Button to define z order columns
txtZOrderEdit=Hiệu chỉnh
#XBUT: Button to save the z order columns
txtZOrderDelete=Xóa
#XMSG: message for empty z order columns
txtNoZOrderList=Không tồn tại thiết lập
#XMSG: message for empty z order list
txtEmptyZOrderList=Cải thiện hiệu suất bằng cách xác định các cột theo thứ tự Z
#XBUT: Button to add z order columns
txtDefineZOrder=Xác định cột theo thứ tự Z
#XLBL: Label for z order columns
txtColumnZOrder=Cột
#XMSG: error message to inform user about HANA limitation
zOrderLimitationError=Bạn chỉ có thể chọn một cột thứ tự Z.

#XMSG: message for conflicting task
taskAlreadyRunning=Tác vụ xung đột đang chạy cho đối tượng "{0}".

#XMSG: Save spark settings success
saveSparkSettingsSuccess=Thiết lập ứng dụng Apache Spark đã được lưu.
