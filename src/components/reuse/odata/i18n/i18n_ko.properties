#XGRP: Generate OData Request
ODataDialogTitle=OData 요청 생성
#XBUT: Close
close=닫기
#XBUT: Preview
preview=미리보기
#XBUT: Data
data=데이터
#XBUT: Metadata
metadata=메타데이터
#XBUT: Service
service=서비스
#XBUT: Reset
reset=재설정
#XFLD: Request
request=요청
#XFLD: OData Request URL
generatedRequest=OData 요청 URL
#XFLD: Copy URL
copyURL=URL 복사
#XFLD: Download OpenAPI request
downloadButton=OpenAPI 요청 다운로드
#XFLD: Name
name=이름
#XFLD: Value
value=값
#XFLD: Input Parameters
inputParameters=입력 매개변수({0})
#XFLD: Variables
variables=변수({0})
#XFLD: No parameter defined
noParameter=자산에 정의된 입력 매개변수가 없습니다.
#XFLD: No variable defined
noVariable=자산에 정의된 변수가 없습니다.
#XBUT: Configure
configure=구성
#YMSG: Inform the limit in preview data
previewDataTopLimit=데이터 미리보기가 100개 레코드로 제한됩니다.
#YMSG: Inform the Editor is considering undeployed changes.
previewNotEnabledView=뷰에 배포되지 않은 변경 내용이 있습니다. OData 요청을 사용하기 전에 먼저 배포하십시오.
#YMSG: Inform the Editor is considering undeployed changes.
previewNotEnabledAM=분석 모델에 배포되지 않은 변경 내용이 있습니다. OData 요청을 사용하기 전에 먼저 배포하십시오.
#YMSG: Inform that metadata request does not support parameter settings.
metadataRequest=메타데이터 요청이 매개변수 설정을 지원하지 않습니다.
#XBUT: Copy Preview Data to Clipboard
previewCopy=미리보기 데이터 복사
#XGRP: Inform that data is copied to clipboard
copiedToClipboard=데이터가 클립보드에 복사되었습니다.
#YMSG: Inform unknown error happened
unknownError=알 수 없는 오류가 발생했습니다. 다시 시도하거나 관리자에게 문의하십시오.
#YMSG: Inform that the request is invalid
openAPIFileName=Datasphere Consumption API - OpenAPI 요청
#XFLD: Name of the OpenAPI file to be downloaded
openAPITitle=Datasphere Consumption API - OpenAPI 요청
#YMSG: Inform about Consumption API Authorization needs and point the docs, this is added to an OpenAPI file
openAPIAuthDescripion=OAuth 2.0 인증 코드 부여를 사용한 인증. 자세한 내용은 https://help.sap.com/docs/SAP_DATASPHERE/43509d67b8b84e66a30851e832f66911/7a453609c8694b029493e7d87e0de60a.html 페이지를 참조하십시오.
#XMSG: Inform that URL has been copied to clipboard
urlCopiedToClipboard=URL이 클립보드로 복사되었습니다.
#XMSG: Inform that parameter value is invalid
parameterError=매개변수 값이 잘못되었습니다. 모델 정의를 확인하십시오.
#XMSG: Inform that query parameter value is invalid
queryParameterError=쿼리 매개변수 값이 잘못되었습니다. OData 사양에 따라 유효한 매개변수 값을 지정하십시오.
#XMSG: Inform that the query selection is empty.
emptyQueryError=계속하려면 리스트에서 쿼리 매개변수를 선택해야 합니다.
#XFLD: Query Parameters
odataParameters=쿼리 매개변수({0})
#XBUT: Add Button
addVariableValue=추가
#XFLD: Lower Bound for Analytic Model Variables
lowerBound=하한
#XFLD: Upper Bound for Analytic Model Variables
upperBound=상한
#XFLD: Equal operator symbol
operatorEQ==
#XFLD: Not Equal operator symbol
operatorNE=≠
#XFLD: Greater Than operator symbol
operatorGT=>
#XFLD: Greater Than or Equal operator symbol
operatorGE=≥
#XFLD: Less Than operator symbol
operatorLT=<
#XFLD: Less Than or Equal operator symbol
operatorLE=≤
#XFLD: Between operator symbol
operatorBT=[ ]
#XMSG: Inform that the range variable has equal values
boundsVariableWarningEqualValues=상위 값과 하위 값이 같습니다. 입력 내용을 확인하십시오.
#XMSG: Inform that the range variable has equal values
boundsVariableWarningHighLessThanLow=하위 값이 상위 값보다 큽니다. 입력 내용을 확인하십시오.
#XMSG: Inform that variables had an error while fetching the data
variableError=변수를 로드할 수 없습니다. 다시 시도하거나 관리자에게 문의하십시오.
#XBUT: Retry button
retry=재시도
