#XGRP: Generate OData Request
ODataDialogTitle=Buat Permintaan OData
#XBUT: Close
close=Tutup
#XBUT: Preview
preview=Pratinjau
#XBUT: Data
data=Data
#XBUT: Metadata
metadata=Metadata
#XBUT: Service
service=Layanan
#XBUT: Reset
reset=Atur Ulang
#XFLD: Request
request=Permintaan
#XFLD: OData Request URL
generatedRequest=URL Permintaan OData
#XFLD: Copy URL
copyURL=Salin URL
#XFLD: Download OpenAPI request
downloadButton=Unduh Permintaan OpenAPI
#XFLD: Name
name=Nama
#XFLD: Value
value=Nilai
#XFLD: Input Parameters
inputParameters=Parameter Input ({0})
#XFLD: Variables
variables=Variabel ({0})
#XFLD: No parameter defined
noParameter=Tidak ada parameter input yang ditentukan dalam aset.
#XFLD: No variable defined
noVariable=Tidak ada variabel yang ditentukan dalam aset.
#XBUT: Configure
configure=Konfigurasikan
#YMSG: Inform the limit in preview data
previewDataTopLimit=Pratinjau data dibatasi hingga 100 catatan.
#YMSG: Inform the Editor is considering undeployed changes.
previewNotEnabledView=Tampilan berisi perubahan yang belum disebarkan. Sebarkan perubahan sebelum menggunakan permintaan OData.
#YMSG: Inform the Editor is considering undeployed changes.
previewNotEnabledAM=Model analitik berisi perubahan yang belum disebarkan. Sebarkan perubahan sebelum menggunakan permintaan OData.
#YMSG: Inform that metadata request does not support parameter settings.
metadataRequest=Permintaan metadata tidak mendukung pengaturan parameter.
#XBUT: Copy Preview Data to Clipboard
previewCopy=Salin Data Pratinjau
#XGRP: Inform that data is copied to clipboard
copiedToClipboard=Data disalin ke clipboard
#YMSG: Inform unknown error happened
unknownError=Terjadi kesalahan yang tidak diketahui. Silakan coba lagi atau hubungi administrator.
#YMSG: Inform that the request is invalid
openAPIFileName=API Pemakaian Datasphere - Permintaan OpenAPI
#XFLD: Name of the OpenAPI file to be downloaded
openAPITitle=API Pemakaian Datasphere - Permintaan OpenAPI
#YMSG: Inform about Consumption API Authorization needs and point the docs, this is added to an OpenAPI file
openAPIAuthDescripion=Otorisasi menggunakan Pemberian Kode Otorisasi OAuth 2.0. Info selengkapnya di https://help.sap.com/docs/SAP_DATASPHERE/43509d67b8b84e66a30851e832f66911/7a453609c8694b029493e7d87e0de60a.html
#XMSG: Inform that URL has been copied to clipboard
urlCopiedToClipboard=URL disalin ke clipboard
#XMSG: Inform that parameter value is invalid
parameterError=Nilai parameter tidak valid. Silakan cek definisi model Anda.
#XMSG: Inform that query parameter value is invalid
queryParameterError=Nilai parameter kueri tidak valid. Silakan ikuti spesifikasi OData untuk menentukan nilai parameter yang valid.
#XMSG: Inform that the query selection is empty.
emptyQueryError=Anda harus memilih parameter kueri dari daftar sebelum dapat melanjutkan.
#XFLD: Query Parameters
odataParameters=Parameter Kueri ({0})
#XBUT: Add Button
addVariableValue=Tambahkan
#XFLD: Lower Bound for Analytic Model Variables
lowerBound=Bawah
#XFLD: Upper Bound for Analytic Model Variables
upperBound=Atas
#XFLD: Equal operator symbol
operatorEQ==
#XFLD: Not Equal operator symbol
operatorNE=≠
#XFLD: Greater Than operator symbol
operatorGT=>
#XFLD: Greater Than or Equal operator symbol
operatorGE=≥
#XFLD: Less Than operator symbol
operatorLT=<
#XFLD: Less Than or Equal operator symbol
operatorLE=≤
#XFLD: Between operator symbol
operatorBT=[ ]
#XMSG: Inform that the range variable has equal values
boundsVariableWarningEqualValues=Harap periksa input Anda karena nilai bawah dan atas sama.
#XMSG: Inform that the range variable has equal values
boundsVariableWarningHighLessThanLow=Harap periksa input Anda karena nilai bawah lebih besar dari nilai atas.
#XMSG: Inform that variables had an error while fetching the data
variableError=Tidak dapat memuat variabel. Silakan coba lagi atau hubungi administrator Anda.
#XBUT: Retry button
retry=Coba Lagi
