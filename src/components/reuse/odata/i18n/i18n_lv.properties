#XGRP: Generate OData Request
ODataDialogTitle=Ģenerēt OData pieprasījumu
#XBUT: Close
close=Aizvērt
#XBUT: Preview
preview=Priekšskatīt
#XBUT: Data
data=Dati
#XBUT: Metadata
metadata=Metadati
#XBUT: Service
service=Pakalpojums
#XBUT: Reset
reset=Atiestatīt
#XFLD: Request
request=Pieprasījums
#XFLD: OData Request URL
generatedRequest=OData pieprasījuma URL
#XFLD: Copy URL
copyURL=Kopēt URL
#XFLD: Download OpenAPI request
downloadButton=Lejupielādēt OpenAPI pieprasījumu
#XFLD: Name
name=Nosaukums
#XFLD: Value
value=Vērtība
#XFLD: Input Parameters
inputParameters=Ievades parametri ({0})
#XFLD: Variables
variables=Mainīgie ({0})
#XFLD: No parameter defined
noParameter=Pamatlīdzeklī nav definēts neviens ievades parametrs.
#XFLD: No variable defined
noVariable=Pamatlīdzeklī nav definēts neviens mainīgais.
#XBUT: Configure
configure=Konfigurēt
#YMSG: Inform the limit in preview data
previewDataTopLimit=Datu priekšskatījuma limits ir 100 ieraksti.
#YMSG: Inform the Editor is considering undeployed changes.
previewNotEnabledView=Skatā ietilpst neizvietotas izmaiņas. Lūdzu, izvietojiet tās pirms OData pieprasījuma lietošanas.
#YMSG: Inform the Editor is considering undeployed changes.
previewNotEnabledAM=Analītiskajā modelī ietilpst neizvietotas izmaiņas. Lūdzu, izvietojiet tās pirms OData pieprasījuma lietošanas.
#YMSG: Inform that metadata request does not support parameter settings.
metadataRequest=Metadatu pieprasījums neatbalsta parametru iestatījumus.
#XBUT: Copy Preview Data to Clipboard
previewCopy=Kopēt priekšskatījuma datus
#XGRP: Inform that data is copied to clipboard
copiedToClipboard=Dati kopēti uz starpliktuvi
#YMSG: Inform unknown error happened
unknownError=Radās nezināma kļūda. Lūdzu, mēģiniet vēlreiz vai sazinieties ar administratoru.
#YMSG: Inform that the request is invalid
openAPIFileName=Datasphere Consumption API - OpenAPI pieprasījums
#XFLD: Name of the OpenAPI file to be downloaded
openAPITitle=Datasphere Consumption API - OpenAPI pieprasījums
#YMSG: Inform about Consumption API Authorization needs and point the docs, this is added to an OpenAPI file
openAPIAuthDescripion=Autorizācija, izmantojot OAuth 2.0 autorizācijas koda piešķiri. Plašāka informācija šeit: https://help.sap.com/docs/SAP_DATASPHERE/43509d67b8b84e66a30851e832f66911/7a453609c8694b029493e7d87e0de60a.html
#XMSG: Inform that URL has been copied to clipboard
urlCopiedToClipboard=URL kopēts uz starpliktuvi
#XMSG: Inform that parameter value is invalid
parameterError=Parametra vērtība ir nederīga. Lūdzu, pārbaudiet sava modeļa definīciju.
#XMSG: Inform that query parameter value is invalid
queryParameterError=Vaicājuma parametra vērtība ir nederīga. Lūdzu, ievērojiet OData specifikāciju, lai norādītu derīgas parametru vērtības.
#XMSG: Inform that the query selection is empty.
emptyQueryError=Lai varētu turpināt, ir jāatlasa vaicājuma parametrs no saraksta.
#XFLD: Query Parameters
odataParameters=Vaicājuma parametri ({0})
#XBUT: Add Button
addVariableValue=Pievienot
#XFLD: Lower Bound for Analytic Model Variables
lowerBound=Zemākais
#XFLD: Upper Bound for Analytic Model Variables
upperBound=Augstākais
#XFLD: Equal operator symbol
operatorEQ==
#XFLD: Not Equal operator symbol
operatorNE=≠
#XFLD: Greater Than operator symbol
operatorGT=>
#XFLD: Greater Than or Equal operator symbol
operatorGE=≥
#XFLD: Less Than operator symbol
operatorLT=<
#XFLD: Less Than or Equal operator symbol
operatorLE=≤
#XFLD: Between operator symbol
operatorBT=[ ]
#XMSG: Inform that the range variable has equal values
boundsVariableWarningEqualValues=Lūdzu, pārbaudiet savu ievadi, jo zemākā vērtība ir vienāda ar augstāko vērtību.
#XMSG: Inform that the range variable has equal values
boundsVariableWarningHighLessThanLow=Lūdzu, pārbaudiet savu ievadi, jo zemākā vērtība ir lielāka par augstāko vērtību.
#XMSG: Inform that variables had an error while fetching the data
variableError=Nevar ielādēt mainīgos. Lūdzu, mēģiniet vēlreiz vai sazinieties ar administratoru.
#XBUT: Retry button
retry=Mēģināt vēlreiz
