#XGRP: Generate OData Request
ODataDialogTitle=OData-kérelem generálása
#XBUT: Close
close=Bezárás
#XBUT: Preview
preview=Előnézet
#XBUT: Data
data=Adatok
#XBUT: Metadata
metadata=Metaadatok
#XBUT: Service
service=Szolgáltatás
#XBUT: Reset
reset=Visszaállítás
#XFLD: Request
request=Kérelem
#XFLD: OData Request URL
generatedRequest=OData-kérelem URL-je
#XFLD: Copy URL
copyURL=URL másolása
#XFLD: Download OpenAPI request
downloadButton=OpenAPI-kérelem letöltése
#XFLD: Name
name=Név
#XFLD: Value
value=Érték
#XFLD: Input Parameters
inputParameters=Bemeneti paraméterek ({0})
#XFLD: Variables
variables=Változók ({0})
#XFLD: No parameter defined
noParameter=Nincsenek megadva bemeneti paraméterek a katalógusobjektumban.
#XFLD: No variable defined
noVariable=Nincsenek megadva változók a katalógusobjektumban.
#XBUT: Configure
configure=Konfigurálás
#YMSG: Inform the limit in preview data
previewDataTopLimit=Az adatelőnézet 100 rekordra korlátozódik.
#YMSG: Inform the Editor is considering undeployed changes.
previewNotEnabledView=A nézet üzembe nem helyezett módosításokat tartalmaz. Helyezze őket üzembe az OData-kérelem használatához.
#YMSG: Inform the Editor is considering undeployed changes.
previewNotEnabledAM=Az elemzési modell üzembe nem helyezett módosításokat tartalmaz. Helyezze őket üzembe az OData-kérelem használatához.
#YMSG: Inform that metadata request does not support parameter settings.
metadataRequest=A metaadat-kérelem nem támogatja a paraméterbeállításokat.
#XBUT: Copy Preview Data to Clipboard
previewCopy=Előnézeti adatok másolása
#XGRP: Inform that data is copied to clipboard
copiedToClipboard=Az adatok a vágólapra másolva
#YMSG: Inform unknown error happened
unknownError=Ismeretlen hiba történt. Próbálkozzon újra, vagy forduljon az adminisztrátorhoz.
#YMSG: Inform that the request is invalid
openAPIFileName=Datasphere adatfelhasználási API - OpenAPI-kérelem
#XFLD: Name of the OpenAPI file to be downloaded
openAPITitle=Datasphere adatfelhasználási API - OpenAPI-kérelem
#YMSG: Inform about Consumption API Authorization needs and point the docs, this is added to an OpenAPI file
openAPIAuthDescripion=Engedélyezés OAuth 2.0 engedélyezőkód megadásával. További információ: https://help.sap.com/docs/SAP_DATASPHERE/43509d67b8b84e66a30851e832f66911/7a453609c8694b029493e7d87e0de60a.html
#XMSG: Inform that URL has been copied to clipboard
urlCopiedToClipboard=Az URL a vágólapra másolva
#XMSG: Inform that parameter value is invalid
parameterError=A paraméterérték érvénytelen. Ellenőrizze a modelldefiníciót.
#XMSG: Inform that query parameter value is invalid
queryParameterError=A lekérdezésparaméter értéke érvénytelen. Az érvényes paraméterértékek megadásához kövesse az OData-specifikációt.
#XMSG: Inform that the query selection is empty.
emptyQueryError=A folytatáshoz választania kell egy lekérdezésparamétert a listából.
#XFLD: Query Parameters
odataParameters=Lekérdezésparaméterek ({0})
#XBUT: Add Button
addVariableValue=Hozzáadás
#XFLD: Lower Bound for Analytic Model Variables
lowerBound=Kisbetűs
#XFLD: Upper Bound for Analytic Model Variables
upperBound=Nagybetűs
#XFLD: Equal operator symbol
operatorEQ==
#XFLD: Not Equal operator symbol
operatorNE=≠
#XFLD: Greater Than operator symbol
operatorGT=>
#XFLD: Greater Than or Equal operator symbol
operatorGE=≥
#XFLD: Less Than operator symbol
operatorLT=<
#XFLD: Less Than or Equal operator symbol
operatorLE=≤
#XFLD: Between operator symbol
operatorBT=[ ]
#XMSG: Inform that the range variable has equal values
boundsVariableWarningEqualValues=Az alsó és felső értékhatár megegyezik. Ellenőrizze a megadott értékeket.
#XMSG: Inform that the range variable has equal values
boundsVariableWarningHighLessThanLow=Az alsó értékhatár magasabb a felső értékhatárnál. Ellenőrizze a megadott értékeket.
#XMSG: Inform that variables had an error while fetching the data
variableError=Nem sikerült betölteni a változókat. Próbálkozzon újra, vagy forduljon az adminisztrátorhoz.
#XBUT: Retry button
retry=Újra
