#XGRP: Generate OData Request
ODataDialogTitle=Generar solicitud OData
#XBUT: Close
close=Cerrar
#XBUT: Preview
preview=Vista previa
#XBUT: Data
data=Datos
#XBUT: Metadata
metadata=Metadatos
#XBUT: Service
service=Servicio
#XBUT: Reset
reset=Restablecer
#XFLD: Request
request=Solicitud
#XFLD: OData Request URL
generatedRequest=URL de solicitud OData
#XFLD: Copy URL
copyURL=Copiar URL
#XFLD: Download OpenAPI request
downloadButton=Descargar solicitud OpenAPI
#XFLD: Name
name=Nombre
#XFLD: Value
value=Valor
#XFLD: Input Parameters
inputParameters=Parámetros de entrada ({0})
#XFLD: Variables
variables=Variables ({0})
#XFLD: No parameter defined
noParameter=Ningún parámetro de entrada definido en el activo.
#XFLD: No variable defined
noVariable=Ninguna variable definida en el activo.
#XBUT: Configure
configure=Configurar
#YMSG: Inform the limit in preview data
previewDataTopLimit=La vista previa de datos está limitada a 100 registros.
#YMSG: Inform the Editor is considering undeployed changes.
previewNotEnabledView=La vista contiene modificaciones no implementadas. Impleméntelas antes de utilizar la solicitud de OData.
#YMSG: Inform the Editor is considering undeployed changes.
previewNotEnabledAM=El modelo analítico contiene modificaciones no implementadas. Impleméntelas antes de utilizar la solicitud de OData.
#YMSG: Inform that metadata request does not support parameter settings.
metadataRequest=La solicitud de metadatos no admite la configuración de parámetros.
#XBUT: Copy Preview Data to Clipboard
previewCopy=Copiar datos de la vista previa
#XGRP: Inform that data is copied to clipboard
copiedToClipboard=Los datos se han copiado en el portapapeles
#YMSG: Inform unknown error happened
unknownError=Se ha producido un error desconocido. Inténtelo de nuevo o contacte con el administrador.
#YMSG: Inform that the request is invalid
openAPIFileName=API de consumo de Datasphere - Solicitud OpenAPI
#XFLD: Name of the OpenAPI file to be downloaded
openAPITitle=API de consumo de Datasphere - Solicitud OpenAPI
#YMSG: Inform about Consumption API Authorization needs and point the docs, this is added to an OpenAPI file
openAPIAuthDescripion=Autorización mediante concesión de código de autorización OAuth 2.0. Más información en https://help.sap.com/docs/SAP_DATASPHERE/43509d67b8b84e66a30851e832f66911/7a453609c8694b029493e7d87e0de60a.html
#XMSG: Inform that URL has been copied to clipboard
urlCopiedToClipboard=URL copiado en el portapapeles
#XMSG: Inform that parameter value is invalid
parameterError=El valor del parámetro no es válido. Compruebe la definición de su modelo.
#XMSG: Inform that query parameter value is invalid
queryParameterError=El valor del parámetro de consulta no es válido. Siga las especificaciones de OData para especificar valores de parámetro válidos.
#XMSG: Inform that the query selection is empty.
emptyQueryError=Debe seleccionar un parámetro de consulta de la lista antes de poder continuar.
#XFLD: Query Parameters
odataParameters=Parámetros de consulta ({0})
#XBUT: Add Button
addVariableValue=Añadir
#XFLD: Lower Bound for Analytic Model Variables
lowerBound=Inferior
#XFLD: Upper Bound for Analytic Model Variables
upperBound=Superior
#XFLD: Equal operator symbol
operatorEQ==
#XFLD: Not Equal operator symbol
operatorNE=≠
#XFLD: Greater Than operator symbol
operatorGT=>
#XFLD: Greater Than or Equal operator symbol
operatorGE=≥
#XFLD: Less Than operator symbol
operatorLT=<
#XFLD: Less Than or Equal operator symbol
operatorLE=≤
#XFLD: Between operator symbol
operatorBT=[ ]
#XMSG: Inform that the range variable has equal values
boundsVariableWarningEqualValues=Compruebe los datos introducidos, ya que los valores inferior y superior son iguales.
#XMSG: Inform that the range variable has equal values
boundsVariableWarningHighLessThanLow=Compruebe los datos introducidos, ya que el valor inferior es mayor que el valor superior.
#XMSG: Inform that variables had an error while fetching the data
variableError=No es posible cargar variables. Vuelva a intentarlo o póngase en contacto con el administrador.
#XBUT: Retry button
retry=Reintentar
