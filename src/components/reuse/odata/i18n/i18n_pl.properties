#XGRP: Generate OData Request
ODataDialogTitle=Generuj żądanie OData
#XBUT: Close
close=Zamknij
#XBUT: Preview
preview=Podgląd
#XBUT: Data
data=Dane
#XBUT: Metadata
metadata=Metadane
#XBUT: Service
service=Usługa
#XBUT: Reset
reset=Resetuj
#XFLD: Request
request=Żądanie
#XFLD: OData Request URL
generatedRequest=URL żądania OData
#XFLD: Copy URL
copyURL=Kopiuj adres URL
#XFLD: Download OpenAPI request
downloadButton=Pobierz żądanie OpenAPI
#XFLD: Name
name=Nazwa
#XFLD: Value
value=Wartość
#XFLD: Input Parameters
inputParameters=Parametry wejściowe ({0})
#XFLD: Variables
variables=Zmienne ({0})
#XFLD: No parameter defined
noParameter=Nie zdefiniowano parametrów wejściowych w zasobie.
#XFLD: No variable defined
noVariable=Nie zdefiniowano zmiennych w zasobie.
#XBUT: Configure
configure=Konfiguruj
#YMSG: Inform the limit in preview data
previewDataTopLimit=Podgląd danych jest ograniczony do 100 rekordów.
#YMSG: Inform the Editor is considering undeployed changes.
previewNotEnabledView=Widok zawiera niewdrożone zmiany. Wdróż go przed użyciem żądania OData.
#YMSG: Inform the Editor is considering undeployed changes.
previewNotEnabledAM=Model analityczny zawiera niewdrożone zmiany. Wdróż go przed użyciem żądania OData.
#YMSG: Inform that metadata request does not support parameter settings.
metadataRequest=Żądanie metadanych nie obsługuje ustawień parametrów.
#XBUT: Copy Preview Data to Clipboard
previewCopy=Kopiuj podgląd danych
#XGRP: Inform that data is copied to clipboard
copiedToClipboard=Dane skopiowano do schowka
#YMSG: Inform unknown error happened
unknownError=Wystąpił nieznany błąd. Spróbuj ponownie lub skontaktuj się z administratorem.
#YMSG: Inform that the request is invalid
openAPIFileName=API wykorzystania Datasphere – żądanie OpenAPI
#XFLD: Name of the OpenAPI file to be downloaded
openAPITitle=API wykorzystania Datasphere – żądanie OpenAPI
#YMSG: Inform about Consumption API Authorization needs and point the docs, this is added to an OpenAPI file
openAPIAuthDescripion=Autoryzacja za pomocą przyznania kodu autoryzacji OAuth 2.0. Więcej informacji można znaleźć na stronie https://help.sap.com/docs/SAP_DATASPHERE/43509d67b8b84e66a30851e832f66911/7a453609c8694b029493e7d87e0de60a.html
#XMSG: Inform that URL has been copied to clipboard
urlCopiedToClipboard=Adres URL skopiowano do schowka
#XMSG: Inform that parameter value is invalid
parameterError=Wartość parametru jest nieprawidłowa. Sprawdź definicję modelu.
#XMSG: Inform that query parameter value is invalid
queryParameterError=Wartość parametru zapytania jest nieprawidłowa. Postępuj zgodnie ze specyfikacją OData, aby określić prawidłowe wartości parametrów.
#XMSG: Inform that the query selection is empty.
emptyQueryError=Aby kontynuować, wybierz parametr zapytania z listy.
#XFLD: Query Parameters
odataParameters=Parametry zapytania ({0})
#XBUT: Add Button
addVariableValue=Dodaj
#XFLD: Lower Bound for Analytic Model Variables
lowerBound=Dolna
#XFLD: Upper Bound for Analytic Model Variables
upperBound=Górna
#XFLD: Equal operator symbol
operatorEQ==
#XFLD: Not Equal operator symbol
operatorNE=≠
#XFLD: Greater Than operator symbol
operatorGT=>
#XFLD: Greater Than or Equal operator symbol
operatorGE=≥
#XFLD: Less Than operator symbol
operatorLT=<
#XFLD: Less Than or Equal operator symbol
operatorLE=≤
#XFLD: Between operator symbol
operatorBT=[ ]
#XMSG: Inform that the range variable has equal values
boundsVariableWarningEqualValues=Sprawdź swój wpis, ponieważ dolne i górne wartości są jednakowe.
#XMSG: Inform that the range variable has equal values
boundsVariableWarningHighLessThanLow=Sprawdź swój wpis, ponieważ dolna wartość jest większa niż górna wartość.
#XMSG: Inform that variables had an error while fetching the data
variableError=Nie można wczytać zmiennych. Spróbuj ponownie lub skontaktuj się z administratorem.
#XBUT: Retry button
retry=Ponów
