#XGRP: Generate OData Request
ODataDialogTitle=<PERSON>ruoti „OData“ užklausą
#XBUT: Close
close=Uždaryti
#XBUT: Preview
preview=Peržiūra
#XBUT: Data
data=Duomenys
#XBUT: Metadata
metadata=Metaduomenys
#XBUT: Service
service=Paslauga
#XBUT: Reset
reset=Nustatyti iš naujo
#XFLD: Request
request=Užklausa
#XFLD: OData Request URL
generatedRequest=„OData“ užklausos URL
#XFLD: Copy URL
copyURL=Kopijuoti URL
#XFLD: Download OpenAPI request
downloadButton=Atsisiųsti „OpenAPI“ užklausą
#XFLD: Name
name=Pavadinimas
#XFLD: Value
value=Reikšmė
#XFLD: Input Parameters
inputParameters=Įvesties parametrai ({0})
#XFLD: Variables
variables=Kintamieji ({0})
#XFLD: No parameter defined
noParameter=Neapibr<PERSON><PERSON>ti ištekliaus įvesties parametrai.
#XFLD: No variable defined
noVariable=Neapibrėžti ištekliaus kintamieji.
#XBUT: Configure
configure=Konfigūruoti
#YMSG: Inform the limit in preview data
previewDataTopLimit=Duomenų peržiūra apribota iki 100 įrašų.
#YMSG: Inform the Editor is considering undeployed changes.
previewNotEnabledView=Rodinyje yra neįdiegtų keitimų. Įdiekite juos prieš naudodami „OData“ užklausą.
#YMSG: Inform the Editor is considering undeployed changes.
previewNotEnabledAM=Analitiniame modelyje yra neįdiegtų keitimų. Įdiekite juos prieš naudodami „OData“ užklausą.
#YMSG: Inform that metadata request does not support parameter settings.
metadataRequest=Metaduomenų užklausa nepalaiko parametrų nustatymų.
#XBUT: Copy Preview Data to Clipboard
previewCopy=Kopijuoti peržiūros duomenis
#XGRP: Inform that data is copied to clipboard
copiedToClipboard=Duomenys nukopijuoti į mainų sritį
#YMSG: Inform unknown error happened
unknownError=Įvyko nežinoma klaida. Pabandykite dar kartą arba susisiekite su savo administratoriumi.
#YMSG: Inform that the request is invalid
openAPIFileName=„Datasphere Consumption API“ – „OpenAPI“ užklausa
#XFLD: Name of the OpenAPI file to be downloaded
openAPITitle=„Datasphere Consumption API“ – „OpenAPI“ užklausa
#YMSG: Inform about Consumption API Authorization needs and point the docs, this is added to an OpenAPI file
openAPIAuthDescripion=Įgaliojimas, naudojant „OAuth 2.0“ įgaliojimo kodo suteikimą. Daugiau informacijos rasite adresu https://help.sap.com/docs/SAP_DATASPHERE/43509d67b8b84e66a30851e832f66911/7a453609c8694b029493e7d87e0de60a.html
#XMSG: Inform that URL has been copied to clipboard
urlCopiedToClipboard=URL nukopijuotas į mainų sritį
#XMSG: Inform that parameter value is invalid
parameterError=Parametro reikšmė netinkama. Patikrinkite savo modelio apibrėžimą.
#XMSG: Inform that query parameter value is invalid
queryParameterError=Užklausos parametro reikšmė yra netinkama. Stebėkite „OData“ specifikaciją, norėdami nurodyti leistinas parametro reikšmes.
#XMSG: Inform that the query selection is empty.
emptyQueryError=Prieš tęsdami turite pasirinkti užklausos parametrą iš sąrašo.
#XFLD: Query Parameters
odataParameters=Užklausos parametrai ({0})
#XBUT: Add Button
addVariableValue=Pridėti
#XFLD: Lower Bound for Analytic Model Variables
lowerBound=Apatinė
#XFLD: Upper Bound for Analytic Model Variables
upperBound=Viršutinė
#XFLD: Equal operator symbol
operatorEQ==
#XFLD: Not Equal operator symbol
operatorNE=≠
#XFLD: Greater Than operator symbol
operatorGT=>
#XFLD: Greater Than or Equal operator symbol
operatorGE=≥
#XFLD: Less Than operator symbol
operatorLT=<
#XFLD: Less Than or Equal operator symbol
operatorLE=≤
#XFLD: Between operator symbol
operatorBT=[ ]'
#XMSG: Inform that the range variable has equal values
boundsVariableWarningEqualValues=Patikrinkite savo įvestį, nes žemesnė ir aukštesnė reikšmės yra lygios.
#XMSG: Inform that the range variable has equal values
boundsVariableWarningHighLessThanLow=Patikrinkite savo įvestį, nes žemesnė reikšmė yra didesnė už aukštesnę reikšmę.
#XMSG: Inform that variables had an error while fetching the data
variableError=Negalima įkelti kintamųjų. Bandykite dar kartą arba susisiekite su administratoriumi.
#XBUT: Retry button
retry=Bandyti iš naujo
