#XGRP: Generate OData Request
ODataDialogTitle=產生 OData 請求
#XBUT: Close
close=關閉
#XBUT: Preview
preview=預覽
#XBUT: Data
data=資料
#XBUT: Metadata
metadata=中繼資料
#XBUT: Service
service=服務
#XBUT: Reset
reset=重設
#XFLD: Request
request=請求
#XFLD: OData Request URL
generatedRequest=OData 請求 URL
#XFLD: Copy URL
copyURL=複製 URL
#XFLD: Download OpenAPI request
downloadButton=下載 OpenAPI 請求
#XFLD: Name
name=名稱
#XFLD: Value
value=值
#XFLD: Input Parameters
inputParameters=輸入參數 ({0})
#XFLD: Variables
variables=變數 ({0})
#XFLD: No parameter defined
noParameter=資產中未定義輸入參數。
#XFLD: No variable defined
noVariable=資產中未定義變數。
#XBUT: Configure
configure=組態
#YMSG: Inform the limit in preview data
previewDataTopLimit=資料預覽已限制為 100 個記錄。
#YMSG: Inform the Editor is considering undeployed changes.
previewNotEnabledView=檢視包含未部署的更改，請部署再使用 OData 請求。
#YMSG: Inform the Editor is considering undeployed changes.
previewNotEnabledAM=分析模型包含未部署的更改，請部署再使用 OData 請求。
#YMSG: Inform that metadata request does not support parameter settings.
metadataRequest=中繼資料請求不支援參數設定。
#XBUT: Copy Preview Data to Clipboard
previewCopy=複製預覽資料
#XGRP: Inform that data is copied to clipboard
copiedToClipboard=資料已複製到剪貼簿
#YMSG: Inform unknown error happened
unknownError=發生未知錯誤，請再試一次或聯絡管理員。
#YMSG: Inform that the request is invalid
openAPIFileName=Datasphere 使用 API - OpenAPI 請求
#XFLD: Name of the OpenAPI file to be downloaded
openAPITitle=Datasphere 使用 API - OpenAPI 請求
#YMSG: Inform about Consumption API Authorization needs and point the docs, this is added to an OpenAPI file
openAPIAuthDescripion=使用 OAuth 2.0 權限代碼授與的全縣。更多資訊請造訪：https://help.sap.com/docs/SAP_DATASPHERE/43509d67b8b84e66a30851e832f66911/7a453609c8694b029493e7d87e0de60a.html
#XMSG: Inform that URL has been copied to clipboard
urlCopiedToClipboard=URL 已複製到剪貼簿
#XMSG: Inform that parameter value is invalid
parameterError=參數值無效。請檢查模型定義。
#XMSG: Inform that query parameter value is invalid
queryParameterError=查詢參數值無效。請遵循 OData 規格以指定有效參數值。
#XMSG: Inform that the query selection is empty.
emptyQueryError=您必須從清單選擇查詢參數，才能繼續。
#XFLD: Query Parameters
odataParameters=查詢參數 ({0})
#XBUT: Add Button
addVariableValue=新增
#XFLD: Lower Bound for Analytic Model Variables
lowerBound=較低
#XFLD: Upper Bound for Analytic Model Variables
upperBound=較高
#XFLD: Equal operator symbol
operatorEQ==
#XFLD: Not Equal operator symbol
operatorNE=≠
#XFLD: Greater Than operator symbol
operatorGT=>
#XFLD: Greater Than or Equal operator symbol
operatorGE=≥
#XFLD: Less Than operator symbol
operatorLT=<
#XFLD: Less Than or Equal operator symbol
operatorLE=≤
#XFLD: Between operator symbol
operatorBT=[ ]
#XMSG: Inform that the range variable has equal values
boundsVariableWarningEqualValues=由於較低和較高值相同，請檢查輸入。
#XMSG: Inform that the range variable has equal values
boundsVariableWarningHighLessThanLow=由於較低值大於較高值，請檢查輸入。
#XMSG: Inform that variables had an error while fetching the data
variableError=無法載入變數。請再試一次，或聯絡管理員。
#XBUT: Retry button
retry=重試
