#XGRP: Generate OData Request
ODataDialogTitle=Генерирај барање за OData
#XBUT: Close
close=Затвори
#XBUT: Preview
preview=Преглед
#XBUT: Data
data=Податоци
#XBUT: Metadata
metadata=Метаподатоци
#XBUT: Service
service=Услуга
#XBUT: Reset
reset=Ресетирај
#XFLD: Request
request=Барање
#XFLD: OData Request URL
generatedRequest=URL на барањето за OData
#XFLD: Copy URL
copyURL=Копирај ја URL-адресата
#XFLD: Download OpenAPI request
downloadButton=Преземи го барањето за OpenAPI
#XFLD: Name
name=Назив
#XFLD: Value
value=Вредност
#XFLD: Input Parameters
inputParameters=Влезни параметри ({0})
#XFLD: Variables
variables=Променливи ({0})
#XFLD: No parameter defined
noParameter=Не се дефинирани параметри за внес во ресурсот.
#XFLD: No variable defined
noVariable=Не се дефинирани променливи во ресурсот.
#XBUT: Configure
configure=Конфигурирај
#YMSG: Inform the limit in preview data
previewDataTopLimit=Прегледот на податоци е ограничен на 100 записи.
#YMSG: Inform the Editor is considering undeployed changes.
previewNotEnabledView=Приказот содржи неприменети промени. Применете го пред да го користите барањето за OData.
#YMSG: Inform the Editor is considering undeployed changes.
previewNotEnabledAM=Аналитичкиот модел содржи неприменети промени. Применете го пред да го користите барањето за OData.
#YMSG: Inform that metadata request does not support parameter settings.
metadataRequest=Барањето за метаподатоци не поддржува поставки за параметри.
#XBUT: Copy Preview Data to Clipboard
previewCopy=Копирај ги податоците за преглед
#XGRP: Inform that data is copied to clipboard
copiedToClipboard=Податоците се ископирани во складот
#YMSG: Inform unknown error happened
unknownError=Се појави непозната грешка. Обидете се повторно или контактирајте со администраторот на системот.
#YMSG: Inform that the request is invalid
openAPIFileName=API за потрошувачка на Datasphere – Барање за OpenAPI
#XFLD: Name of the OpenAPI file to be downloaded
openAPITitle=API за потрошувачка на Datasphere – Барање за OpenAPI
#YMSG: Inform about Consumption API Authorization needs and point the docs, this is added to an OpenAPI file
openAPIAuthDescripion=Авторизација со користење на OAuth 2.0 Доделување код за авторизација. Прочитајте повеќе на https://help.sap.com/docs/SAP_DATASPHERE/43509d67b8b84e66a30851e832f66911/7a453609c8694b029493e7d87e0de60a.html
#XMSG: Inform that URL has been copied to clipboard
urlCopiedToClipboard=URL-адресата е ископирана во складот
#XMSG: Inform that parameter value is invalid
parameterError=Вредноста на параметарот е неважечка. Проверете ја дефиницијата на моделот.
#XMSG: Inform that query parameter value is invalid
queryParameterError=Вредноста на параметарот на прашалникот е неважечка. Следете ја спецификацијата за OData за да наведете важечки вредности на параметарот.
#XMSG: Inform that the query selection is empty.
emptyQueryError=Мора да изберете параметар на прашалникот од списокот пред да можете да продолжите.
#XFLD: Query Parameters
odataParameters=Параметри на барањето ({0})
#XBUT: Add Button
addVariableValue=Додај
#XFLD: Lower Bound for Analytic Model Variables
lowerBound=Долна
#XFLD: Upper Bound for Analytic Model Variables
upperBound=Горна
#XFLD: Equal operator symbol
operatorEQ==
#XFLD: Not Equal operator symbol
operatorNE=≠
#XFLD: Greater Than operator symbol
operatorGT=>
#XFLD: Greater Than or Equal operator symbol
operatorGE=≥
#XFLD: Less Than operator symbol
operatorLT=<
#XFLD: Less Than or Equal operator symbol
operatorLE=≤
#XFLD: Between operator symbol
operatorBT=[ ]
#XMSG: Inform that the range variable has equal values
boundsVariableWarningEqualValues=Проверете го внесот бидејќи долната и горната вредност се исти.
#XMSG: Inform that the range variable has equal values
boundsVariableWarningHighLessThanLow=Проверете го внесот бидејќи долната вредност е поголема од горната.
#XMSG: Inform that variables had an error while fetching the data
variableError=Не може да се вчитаат променливите. Обидете се повторно или контактирајте со администраторот.
#XBUT: Retry button
retry=Обиди се повторно
