#XTIT: Title for import entities
importEntitiesTitle=エンティティのインポート
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=接続タイプを選択
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=対象スペースを選択
#XFLD: import wizard step 3: select connection
selectConnectionStep=接続を選択
#XFLD: import wizard step 4: select entities
selectEntitiesStep=エンティティを選択
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=エンティティをレビュー
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=接続/対象スペースを選択

#XTIT: title connection types
connectionTypesTitle=アイテム ({0})
#XTIT: title target spaces
targetSpaceTitle=スペース ({0})
#XTIT: title connections
connectionsTitle=接続 ({0})

#XCOL: space business name
spaceBusinessName=スペース
#XCOL: spaces business name
spacesBusinessName=スペース
#XCOL: business name
businessName=ビジネス名
#XCOL: technical name
technicalName=技術名
#XCOL: modeling pattern
modelingPattern=モデリングパターン
#XCOL: import status
importStatus=インポートステータス
#XCOL: replication flow
replicationFlow=複製フロー
#XCOL: entity
entity=エンティティ
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=選択済エンティティ
#XHED: heading selected entities
selectedApis=選択された API
#XHED: heading dependent entities
dependentEntities=依存エンティティ
#XHED: heading dependent entities
dependentEntitiesOf={0} の依存エンティティ
#XHED: heading dependent entities
selectedEntitiesOfApi=API {0} の選択されたエンティティ
#XHED: heading entities of API
entitiesOfApi=API {0} のエンティティ
#XHED: heading business builder
businessBuilderObjects=ビジネスビルダ ({0})
#XHED: heading data builder
dataBuilderObjects=データビルダ ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=前
#XBUT
buttonNextStep=次のステップ
#XBUT
buttonImport=インポートおよびデプロイを開始
#XBUT
buttonSchedule=インポートおよびデプロイをスケジュール
#XBUT
buttonCancel=キャンセル
#XBUT
buttonContinue=続行
#XMSG
loadConnectionTypesFailed=接続タイプをロードできません。
#XMSG
loadTargetSpacesFailed=対象スペースをロードできません。
#XMSG
loadConnectionsFailed=接続をロードできません。
#XMSG
loadBwBridgeConnectionFailed=SAP BW ブリッジ接続をロードできません。
#XMSG
getConnectionStatusFailed=接続 {0} のステータスを取得できません。
#XMSG
getSharedConnectionDetailsFailed=システム ID {0} の接続の詳細を取得できません。
#XMSG
connectionNotValid=接続 {0} は有効な接続でありません。
#XMSG
connectionBwBridgeNotValid=SAP BW ブリッジ接続 {0} は有効な接続でありません。
#XMSG
connectionBwBridgeNotAvailable=SAP BW ブリッジ接続は利用できません。
#XMSG
loadEntitiesFailed=エンティティをロードできません。
#XMSG
importEntitiesFailed=エンティティをインポートできません。
#XMSG
importEntitiesStarted=エンティティをインポートしています。インポートのステータスに関する通知をチェックしてください。
#XMSG
loadReviewEntitiesListFailed=インポート対象オブジェクトをロードできません。
#XMSG
selectOneResultListItem=検索結果一覧でアイテムを 1 つだけ選択してください。
#XMSG
dialogCancel=エンティティのインポートをキャンセルしてもよろしいですか?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=接続 {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=ラベル
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW ブリッジ

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=ビジネスビルダオブジェクト作成
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=ビジネスエンティティと利用モデル
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=ビジネスエンティティのみ
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=なし

#XFLD: label Remote Access Mode
remoteAccessModeLabel=データアクセス
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=ローカルテーブルへの複製フロー
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=リモートテーブル
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=複製フロー
#XFLD: label Ingestion Space
ingestionSpaceLabel=摂取スペース

#XMSG
modelTransferOk=モデルインポートが有効になっています。
#XMSG
modelTransferNotOk=モデルインポートは使用できません。
#XMSG
hanaSdiOk=リモートテーブルが有効になっています。
#XMSG
hanaSdiNotOk=リモートテーブルは使用できません。
#XMSG
replicationFlowOk=複製フローが有効になっています。
#XMSG
replicationFlowNotOk=複製フローは使用できません。
#XMSG
onboardingOk=リモートテーブルと複製フローのオンボーディングは有効です。
#XMSG
onboardingNotOk=リモートテーブルと複製フローのオンボーディングは実行できません。
#XMSG
enableReplicationFlow=この接続では、複製フローを使用したデータの複製をサポートすることができますが、この機能は現在有効になっていません。管理者に連絡して接続を更新してください。機能が有効になるまでは、リモートターブルを介して連携データのエンティティをインポートすることだけが可能です。
#XMSG
selectedApiWithoutEntities=選択した API ではエンティティは指定されません。
#XMSG
switchReplicationFlowToRemoteTables=これらのオブジェクトは現在ローカルテーブルとしてインストールされ、データがローカルテーブルに複製されています。連携を変更すると、パフォーマンスが低下する可能性があります。この変更はこれらのオブジェクトが利用されるすべてのスペースに影響します。\n\nリモートテーブルを介した連携へのデータアクセスを変更しますか?
#XMSG
switchRemoteTablesToReplicationFlow=これらのオブジェクトは現在リモートテーブルとしてインストールされ、データがリモートテーブルに連携されています。複製を変更すると、パフォーマンスは向上しますが、ライブアクセスが無効化されるため、データの新しさは複製スケジュールによって異なります。この変更はこれらのオブジェクトが利用されるすべてのスペースに影響します。\n\n複製フローおよびローカルテーブルを介した複製へのデータアクセスを変更しますか?

#XFLD
spaceTypeAbapBridge=SAP BW ブリッジ
#XFLD
spaceTypeDatasphere=SAP Datasphere
