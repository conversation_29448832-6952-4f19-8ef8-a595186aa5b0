#XTIT: Title for import entities
importEntitiesTitle=Importa entità
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Seleziona tipo di connessione
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Seleziona spazio di destinazione
#XFLD: import wizard step 3: select connection
selectConnectionStep=Seleziona connessione
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Seleziona entità
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Rivedi entità
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Seleziona connessione/spazio di destinazione

#XTIT: title connection types
connectionTypesTitle=Elementi ({0})
#XTIT: title target spaces
targetSpaceTitle=Spazi ({0})
#XTIT: title connections
connectionsTitle=Connessioni ({0})

#XCOL: space business name
spaceBusinessName=Spazio
#XCOL: spaces business name
spacesBusinessName=Spazi
#XCOL: business name
businessName=Nome aziendale
#XCOL: technical name
technicalName=Nome tecnico
#XCOL: modeling pattern
modelingPattern=Pattern di modellazione
#XCOL: import status
importStatus=Stato importazione
#XCOL: replication flow
replicationFlow=Flusso di replicazione
#XCOL: entity
entity=Entità
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Entità selezionate
#XHED: heading selected entities
selectedApis=API selezionate
#XHED: heading dependent entities
dependentEntities=Entità dipendenti
#XHED: heading dependent entities
dependentEntitiesOf=Entità dipendenti di {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Entità selezionate dell''API {0}
#XHED: heading entities of API
entitiesOfApi=Entità dell''API {0}
#XHED: heading business builder
businessBuilderObjects=Generatore aziendale ({0})
#XHED: heading data builder
dataBuilderObjects=Generatore di dati ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Indietro
#XBUT
buttonNextStep=Fase successiva
#XBUT
buttonImport=Avvia importazione e distribuisci
#XBUT
buttonSchedule=Pianifica importazione e distribuisci
#XBUT
buttonCancel=Annulla
#XBUT
buttonContinue=Continua
#XMSG
loadConnectionTypesFailed=Impossibile caricare i tipi di connessione.
#XMSG
loadTargetSpacesFailed=Impossibile caricare gli spazi di destinazione.
#XMSG
loadConnectionsFailed=Impossibile caricare le connessioni.
#XMSG
loadBwBridgeConnectionFailed=Impossibile caricare la connessione ponte SAP BW.
#XMSG
getConnectionStatusFailed=Impossibile recuperare lo stato della connessione {0}.
#XMSG
getSharedConnectionDetailsFailed=Impossibile recuperare i dettagli della connessione per l''ID sistema {0}.
#XMSG
connectionNotValid=La connessione {0} non è una connessione valida.
#XMSG
connectionBwBridgeNotValid=La connessione ponte SAP BW {0} non è una connessione valida.
#XMSG
connectionBwBridgeNotAvailable=La connessione ponte SAP BW non è disponibile.
#XMSG
loadEntitiesFailed=Impossibile caricare le entità.
#XMSG
importEntitiesFailed=Impossibile importare le entità.
#XMSG
importEntitiesStarted=Importazione delle entità in corso. Verificare le notifiche sullo stato dell'importazione.
#XMSG
loadReviewEntitiesListFailed=Impossibile caricare gli oggetti da importare.
#XMSG
selectOneResultListItem=Selezionare un solo elemento nell'elenco dei risultati della ricerca.
#XMSG
dialogCancel=Confermare l'annullamento dell'importazione delle entità?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=Connessione {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=Etichetta
#XTXT: SAP BW Bridge Connection type
sapBwBridge=Ponte SAP BW

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Crea oggetti per Generatore aziendale
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Entità aziendali e modelli di consumo
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Solo entità aziendali
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Nessuno

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Accesso ai dati
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Flusso di replicazione nelle tabelle locali
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Tabelle remote
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Flusso di replicazione
#XFLD: label Ingestion Space
ingestionSpaceLabel=Spazio di inserimento

#XMSG
modelTransferOk=L'importazione modello è abilitata.
#XMSG
modelTransferNotOk=Impossibile utilizzare l'importazione modello.
#XMSG
hanaSdiOk=Le tabelle remote sono abilitate.
#XMSG
hanaSdiNotOk=Impossibile utilizzare le tabelle remote.
#XMSG
replicationFlowOk=I flussi di replicazione sono abilitati.
#XMSG
replicationFlowNotOk=Impossibile utilizzare i flussi di replicazione.
#XMSG
onboardingOk=Onboarding di tabelle remote e flussi di replicazione abilitato.
#XMSG
onboardingNotOk=Onboarding di tabelle remote e flussi di replicazione non possibile.
#XMSG
enableReplicationFlow=Questa connessione può supportare la replicazione dei dati tramite un flusso di replicazione, ma questa funzionalità non è attualmente attivata. Contattare l'amministratore per aggiornare la connessione. Nel frattempo, è possibile importare solo entità con accesso ai dati federati tramite tabelle remote.
#XMSG
selectedApiWithoutEntities=L'API selezionata non fornisce alcuna entità.
#XMSG
switchReplicationFlowToRemoteTables=Questi oggetti sono attualmente installati come tabelle locali con dati in esse replicati. Il passaggio alla federazione potrebbe compromettere le prestazioni. Questa modifica influirà su tutti gli spazi che utilizzano questi oggetti.\n\nModificare l'accesso ai dati in federazione tramite tabelle remote?
#XMSG
switchRemoteTablesToReplicationFlow=Questi oggetti sono attualmente installati come tabelle remote con dati in esse federati. Il passaggio alla replicazione migliorerà le prestazioni ma disabiliterà anche l'accesso in tempo reale, pertanto l'attualità dei dati dipenderà dalla pianificazione di replicazione. Questa modifica influirà su tutti gli spazi che utilizzano questi oggetti.\n\nModificare l'accesso ai dati in replicazione tramite un flusso di replicazione e tabelle locali?

#XFLD
spaceTypeAbapBridge=Ponte SAP BW
#XFLD
spaceTypeDatasphere=SAP Datasphere
