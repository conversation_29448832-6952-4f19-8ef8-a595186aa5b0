#XTIT: Title for import entities
importEntitiesTitle=Nhập thực thể
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Chọn kiểu kết nối
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=chọn vùng dữ liệu đích
#XFLD: import wizard step 3: select connection
selectConnectionStep=Chọn kết nối
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Chọn thực thể
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Đánh giá thực thể
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Chọn kết nối / Vùng dữ liệu đích

#XTIT: title connection types
connectionTypesTitle=Mục ({0})
#XTIT: title target spaces
targetSpaceTitle=Vùng dữ liệu ({0})
#XTIT: title connections
connectionsTitle=Kết nối ({0})

#XCOL: space business name
spaceBusinessName=Vùng dữ liệu
#XCOL: spaces business name
spacesBusinessName=Vùng dữ liệu
#XCOL: business name
businessName=Tên doanh nghiệp
#XCOL: technical name
technicalName=Tên kỹ thuật
#XCOL: modeling pattern
modelingPattern=Mẫu mô hình
#XCOL: import status
importStatus=Tình trạng nhập
#XCOL: replication flow
replicationFlow=Luồng sao chép
#XCOL: entity
entity=Thực thể
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Thực thể được chọn
#XHED: heading selected entities
selectedApis=API được chọn
#XHED: heading dependent entities
dependentEntities=Thực thể phụ thuộc
#XHED: heading dependent entities
dependentEntitiesOf=Thực thể phụ thuộc của {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Thực thể được chọn của API {0}
#XHED: heading entities of API
entitiesOfApi=Thực thể API {0}
#XHED: heading business builder
businessBuilderObjects=Trình tạo doanh nghiệp ({0})
#XHED: heading data builder
dataBuilderObjects=Trình tạo dữ liệu ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Trước
#XBUT
buttonNextStep=Bước kế tiếp
#XBUT
buttonImport=Bắt đầu nhập và triển khai
#XBUT
buttonSchedule=Lên lịch nhập và triển khai
#XBUT
buttonCancel=Hủy
#XBUT
buttonContinue=Tiếp tục
#XMSG
loadConnectionTypesFailed=Không thể tải các loại kết nối.
#XMSG
loadTargetSpacesFailed=Không thể tải vùng dữ liệu đích.
#XMSG
loadConnectionsFailed=Không thể tải kết nối.
#XMSG
loadBwBridgeConnectionFailed=Không thể tải kết nối cầu SAP BW.
#XMSG
getConnectionStatusFailed=Không thể truy xuất trạng thái kết nối {0}.
#XMSG
getSharedConnectionDetailsFailed=Không thể truy xuất chi tiết về kết nối cho ID hệ thống {0}.
#XMSG
connectionNotValid=Kết nối {0} không phải là kết nối hợp lệ.
#XMSG
connectionBwBridgeNotValid=Kết nối cầu SAP BW {0} không phải là kết nối hợp lệ.
#XMSG
connectionBwBridgeNotAvailable=Kết nối cầu nối SAP BW không khả dụng.
#XMSG
loadEntitiesFailed=Không thể tải thực thể.
#XMSG
importEntitiesFailed=Không thể nhập thực thể.
#XMSG
importEntitiesStarted=Nhập thực thể. Kiểm tra thông báo cho trạng thái nhập.
#XMSG
loadReviewEntitiesListFailed=Không thể tải đối tượng cần nhập.
#XMSG
selectOneResultListItem=Chỉ chọn một mục trong danh sách kết quả tìm kiếm.
#XMSG
dialogCancel=Bạn có thực sự muốn hủy nhập thực thể không?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=Kết nối {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=Nhãn
#XTXT: SAP BW Bridge Connection type
sapBwBridge=Cầu nối SAP BW

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Tạo đối tượng trình tạo doanh nghiệp
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Thực thể kinh doanh và mô hình tiêu thụ
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Chỉ thực thể kinh doanh
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Không có

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Truy cập dữ liệu
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Luồng sao chép đến bảng cục bộ
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Bảng từ xa
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Luồng sao chép
#XFLD: label Ingestion Space
ingestionSpaceLabel=Vùng dữ liệu nhập vào

#XMSG
modelTransferOk=Nhập mô hình được bật.
#XMSG
modelTransferNotOk=Không thể sử dụng nhập mô hình.
#XMSG
hanaSdiOk=Bảng từ xa được bật.
#XMSG
hanaSdiNotOk=Không thể sử dụng bảng từ xa.
#XMSG
replicationFlowOk=Luồng sao chép được bật.
#XMSG
replicationFlowNotOk=Không thể sử dụng luồng sao chép.
#XMSG
onboardingOk=Tính năng tích hợp bảng từ xa và luồng sao chép được bật.
#XMSG
onboardingNotOk=Tính năng tích hợp bảng từ xa và luồng sao chép không khả thi.
#XMSG
enableReplicationFlow=Kết nối này có thể hỗ trợ sao chép dữ liệu thông qua luồng sao chép, nhưng tính năng này hiện thời chưa được bật. Vui lòng liên hệ người quản trị của bạn để cập nhật kết nối. Trong thời gian chờ đợi, bạn chỉ có thể nhập các thực thể có quyền truy cập dữ liệu được liên kết qua các bảng từ xa.
#XMSG
selectedApiWithoutEntities=API được chọn không cung cấp bất kỳ thực thể nào.
#XMSG
switchReplicationFlowToRemoteTables=Các đối tượng này hiện được cài đặt dưới dạng bảng cục bộ với dữ liệu được sao chép vào chúng. Việc thay đổi sang liên kết có thể làm giảm hiệu suất. Thay đổi này sẽ ảnh hưởng đến tất cả các vùng dữ liệu các đối tượng này.\n\nBạn có muốn thay đổi quyền truy cập dữ liệu vào liên kết thông qua các bảng từ xa không?
#XMSG
switchRemoteTablesToReplicationFlow=Các đối tượng này hiện được cài đặt dưới dạng bảng từ xa với dữ liệu được liên kết với chúng. Việc thay đổi sang sao chép sẽ cải thiện hiệu suất nhưng cũng sẽ vô hiệu hóa quyền truy cập trực tiếp để độ mới của dữ liệu phụ thuộc vào sao chép của bạn. Sự thay đổi này sẽ tác động đến tất cả các vùng dữ liệu các đối tượng này.\n\nBạn có muốn thay đổi quyền truy cập dữ liệu vào sao chép thông qua luồng sao chép và bảng cục bộ không?

#XFLD
spaceTypeAbapBridge=Cầu nối SAP BW
#XFLD
spaceTypeDatasphere=SAP Datasphere
