#XTIT: Title for import entities
importEntitiesTitle=Импортиране на единици
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Изберете вид връзка
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Изберете целево пространство
#XFLD: import wizard step 3: select connection
selectConnectionStep=Изберете връзка
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Изберете единици
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Преглед на единици
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Изберете връзка/целево пространство

#XTIT: title connection types
connectionTypesTitle=Позиции ({0})
#XTIT: title target spaces
targetSpaceTitle=Пространства ({0})
#XTIT: title connections
connectionsTitle=Връзки ({0})

#XCOL: space business name
spaceBusinessName=Пространство
#XCOL: spaces business name
spacesBusinessName=Пространства
#XCOL: business name
businessName=Бизнес наименование
#XCOL: technical name
technicalName=Техническо име
#XCOL: modeling pattern
modelingPattern=Шаблон за моделиране
#XCOL: import status
importStatus=Статус на импорт
#XCOL: replication flow
replicationFlow=Поток на репликация
#XCOL: entity
entity=Единица
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Избрани единици
#XHED: heading selected entities
selectedApis=Избрани API
#XHED: heading dependent entities
dependentEntities=Зависими единици
#XHED: heading dependent entities
dependentEntitiesOf=Зависими единици от {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Избрани единици на API {0}
#XHED: heading entities of API
entitiesOfApi=Единици на API {0}
#XHED: heading business builder
businessBuilderObjects=Бизнес генератор ({0})
#XHED: heading data builder
dataBuilderObjects=Генератор на данни ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Назад
#XBUT
buttonNextStep=Следваща стъпка
#XBUT
buttonImport=Старт на импорт и разгръщане
#XBUT
buttonSchedule=Планиране на импорт и разгръщане
#XBUT
buttonCancel=Отказ
#XBUT
buttonContinue=Напред
#XMSG
loadConnectionTypesFailed=Невъзможно зареждане на видовете връзки.
#XMSG
loadTargetSpacesFailed=Невъзможно зареждане на целевите пространства.
#XMSG
loadConnectionsFailed=Невъзможно зареждане на връзките.
#XMSG
loadBwBridgeConnectionFailed=Невъзможно зареждане на връзката от свързващото приложение SAP BW.
#XMSG
getConnectionStatusFailed=Невъзможно извличане на статуса на връзката {0}.
#XMSG
getSharedConnectionDetailsFailed=Неуспешно извличане на подробните данни за връзката за ИД на система {0}.
#XMSG
connectionNotValid=Връзката {0} не е валидна.
#XMSG
connectionBwBridgeNotValid=Връзката от свързващото приложение SAP BW {0} не е валидна.
#XMSG
connectionBwBridgeNotAvailable=Няма връзка със свързващото приложение SAP BW.
#XMSG
loadEntitiesFailed=Невъзможно зареждане на единиците.
#XMSG
importEntitiesFailed=Невъзможен импорт на единиците.
#XMSG
importEntitiesStarted=Импортиране на единици. стартирано. Проверете известията за статуса на импортиране.
#XMSG
loadReviewEntitiesListFailed=Невъзможно зареждане на обектите за импорт.
#XMSG
selectOneResultListItem=Изберете само една позиция в списъка с резултати от търсене.
#XMSG
dialogCancel=Наистина ли желаете да откажете импортирането на единиците?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ИД
identifier_plural=Връзка {0}
identifier_identifier=ИД
identifier_entityname=ИД
identifier_entitylabel=Етикет
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW свързващо приложение

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Създаване на обекти на бизнес генератор
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Бизнес единици и модели на потребление
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Само бизнес единици
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Няма

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Достъп до данните
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Поток на репликация към локалните таблици
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Отдалечени таблици
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Поток на репликация
#XFLD: label Ingestion Space
ingestionSpaceLabel=Пространство за приемане

#XMSG
modelTransferOk=Импортирането на модели е активирано.
#XMSG
modelTransferNotOk=Импортирането на модели не може да бъде използвано.
#XMSG
hanaSdiOk=Отдалечените таблици са активирани.
#XMSG
hanaSdiNotOk=Отдалечените таблици не може да бъдат използвани.
#XMSG
replicationFlowOk=Потоците на репликация са активирани.
#XMSG
replicationFlowNotOk=Потоците на репликация не може да бъдат използвани.
#XMSG
onboardingOk=Онбордингът на отдалечени таблици и потоци на репликация е активиран.
#XMSG
onboardingNotOk=Онбордингът на отдалечени таблици и потоци на репликация е невъзможен.
#XMSG
enableReplicationFlow=Тази връзка поддържа репликация на данни чрез поток, но тази функция не е активирана. Поискайте от администратор да актуализира връзката. Междувременно може да импортирате единици само чрез достъп до обединени данни през отдалечени таблици.
#XMSG
selectedApiWithoutEntities=Избраният API не предоставя никакви единици.
#XMSG
switchReplicationFlowToRemoteTables=Тези обекти понастоящем са инсталирани като локални таблици с репликирани в тях данни. Промяната на „федериране“ може да понижи производителността. Тази промяна ще засегне всички пространства, които използват тези обекти.\n\nЖелаете ли да промените достъпа до данните на „федериране“ чрез отдалечени таблици?
#XMSG
switchRemoteTablesToReplicationFlow=Тези обекти понастоящем са инсталирани като отдалечени таблици с обединени с тях данни. Промяната на „репликация“ ще подобри производителността, но и ще дезактивира достъпа в реално време, така че актуалността на данните ви зависи от графика ви за репликация. Тази промяна ще засегне всички пространства, които използват тези обекти.\n\nЖелаете ли да промените достъпа до данни на „репликация“ чрез поток на репликация и локални таблици?

#XFLD
spaceTypeAbapBridge=SAP BW свързващо приложение
#XFLD
spaceTypeDatasphere=SAP Datasphere
