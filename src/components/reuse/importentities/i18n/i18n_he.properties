#XTIT: Title for import entities
importEntitiesTitle=יבא ישויות
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=בחר סוג חיבור
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=בחר מרחב יעד
#XFLD: import wizard step 3: select connection
selectConnectionStep=בחר חיבור
#XFLD: import wizard step 4: select entities
selectEntitiesStep=בחר ישויות
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=סקור ישויות
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=בחר חיבור/מרחב יעד

#XTIT: title connection types
connectionTypesTitle=פריטים ({0})
#XTIT: title target spaces
targetSpaceTitle=מרחבים ({0})
#XTIT: title connections
connectionsTitle=חיבורים ({0})

#XCOL: space business name
spaceBusinessName=מרחב
#XCOL: spaces business name
spacesBusinessName=מרחבים
#XCOL: business name
businessName=שם עסקי
#XCOL: technical name
technicalName=שם טכני
#XCOL: modeling pattern
modelingPattern=דפוס עיצוב
#XCOL: import status
importStatus=סטאטוס יבוא
#XCOL: replication flow
replicationFlow=תזרים שכפולים
#XCOL: entity
entity=ישות
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=ישויות שנבחרו
#XHED: heading selected entities
selectedApis=ממשק API שנבחרו
#XHED: heading dependent entities
dependentEntities=ישויות תלויות
#XHED: heading dependent entities
dependentEntitiesOf=ישויות תלויות  של {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=ישויות שנבחרו של API {0}
#XHED: heading entities of API
entitiesOfApi=ישויות של API {0}
#XHED: heading business builder
businessBuilderObjects=בונה עסקי ({0})
#XHED: heading data builder
dataBuilderObjects=בונה מודל נתונים ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=הקודם
#XBUT
buttonNextStep=השלב הבא
#XBUT
buttonImport=התחל ייבוא ופריסה
#XBUT
buttonSchedule=תזמן ייבוא ופריסה
#XBUT
buttonCancel=בטל
#XBUT
buttonContinue=המשך
#XMSG
loadConnectionTypesFailed=לא ניתן לטעון סוגי חיבור.
#XMSG
loadTargetSpacesFailed=לא ניתן לטעון מרחבי יעד.
#XMSG
loadConnectionsFailed=לא ניתן לטעון חיבורים.
#XMSG
loadBwBridgeConnectionFailed=לא ניתן לטעון חיבור ל-SAP BW bridge.
#XMSG
getConnectionStatusFailed=לא ניתן לאחזר את סטאטוס החיבור {0}.
#XMSG
getSharedConnectionDetailsFailed=לא ניתן לאחזר את הפרטים של החיבור עבור זיהוי מערכת {0}.
#XMSG
connectionNotValid=החיבור {0} אינו חיבור חוקי.
#XMSG
connectionBwBridgeNotValid=החיבור של SAP BW bridge {0} אינו חיבור חוקי.
#XMSG
connectionBwBridgeNotAvailable=החיבור של SAP BW bridge אינו זמין.
#XMSG
loadEntitiesFailed=לא ניתן לטעון ישויות.
#XMSG
importEntitiesFailed=לא ניתן ליבא ישויות.
#XMSG
importEntitiesStarted=מיבא ישויות. בדוק את ההודעות עבור הסטאטוס של היבוא.
#XMSG
loadReviewEntitiesListFailed=לא ניתן לטעון אובייקטים ליבוא.
#XMSG
selectOneResultListItem=בחר פריט אחד בלבד ברשימת תוצאות החיפוש.
#XMSG
dialogCancel=האם אתה בטוח שברצונך לבטל את יבוא הישויות?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=זיהוי
identifier_plural=חיבור {0}
identifier_identifier=זיהוי
identifier_entityname=זיהוי
identifier_entitylabel=תווית
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW Bridge

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=צור אובייקטים של Business Builder
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=ישויות עסקיות ומודלים של צריכה
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=ישויות עסקיות בלבד
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=ללא

#XFLD: label Remote Access Mode
remoteAccessModeLabel=גישה לנתונים
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=תזרים שכפולים לטבלאות מקומיות
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=טבלאות מרוחקות
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=תזרים שכפול
#XFLD: label Ingestion Space
ingestionSpaceLabel=מרחב קליטה

#XMSG
modelTransferOk=ייבוא מודל מופעל.
#XMSG
modelTransferNotOk=ייבוא מודל אינו ניתן לשימוש.
#XMSG
hanaSdiOk=טבלאות מרוחקות מופעלות.
#XMSG
hanaSdiNotOk=טבלאות מרוחקות אינן ניתנות לשימוש.
#XMSG
replicationFlowOk=תזרימי שכפול מופעלים.
#XMSG
replicationFlowNotOk=לא ניתן להשתמש בתזרימי שכפולים.
#XMSG
onboardingOk=שילוב של טבלאות מרוחקות ותזרימי שכפולים מופעל.
#XMSG
onboardingNotOk=שילוב של טבלאות מרוחקות ותזרימי שכפולים אינו אפשרי.
#XMSG
enableReplicationFlow=חיבור זה יכול לתמוך בשכפול של נתונים דרך תזרים שכפול, אבל מאפיין זה אינו מופעל כרגע. צור קשר עם מנהל המערכת שלך כדי לעדכן את החיבור. בינתיים תוכל רק לייבא ישויות עם גישת נתונים מאוחדת דרך טבלאות מרוחקות.
#XMSG
selectedApiWithoutEntities=ה-API שנבחר אינו מספק ישויות כלשהן.
#XMSG
switchReplicationFlowToRemoteTables=אובייקטים אלה מותקנים כרגע בטבלאות מקומיות עם נתונים שמשוכפלים בהם. שינוי לאיחוד עשויים לפגוע בביצועים. שינוי זה ישפיע על כל המרחבים שצורכים אובייקטים אלה./n/nהאם ברצונך לשנות את הגישה לנתונים לאיחוד באמצעות טבלאות מרוחקות?
#XMSG
switchRemoteTablesToReplicationFlow=אובייקטים אלה מותקנים כרגע כטבלאות מרוחקות עם נתונים מאוחדים בהם. שינוי לשכפול ישפר את הביצועים אבל גם ישבית את הגישה החיה כך שרעננות הנתונים שלך תלויה בתזמון השכפול. שינוי זה ישפיע על כל המרחבים שצורכים אובייקטים אלה.\n\n האם ברצונך לשנות את גישת הנתונים לשכפול באמצעות תזרים שכפול וטבלאות מקומיות?

#XFLD
spaceTypeAbapBridge=SAP BW Bridge
#XFLD
spaceTypeDatasphere=SAP Datasphere
