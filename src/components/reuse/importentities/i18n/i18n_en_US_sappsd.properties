#XTIT: Title for import entities
importEntitiesTitle=[[[Ĭɱρŏŗţ Ĕŋţįţįēş∙∙∙∙]]]
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=[[[Ŝēĺēċţ Ĉŏŋŋēċţįŏŋ Ţŷρē∙∙∙∙∙]]]
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=[[[Ŝēĺēċţ Ţąŗğēţ Ŝρąċē∙∙∙∙∙]]]
#XFLD: import wizard step 3: select connection
selectConnectionStep=[[[Ŝēĺēċţ Ĉŏŋŋēċţįŏŋ∙∙∙∙∙∙∙]]]
#XFLD: import wizard step 4: select entities
selectEntitiesStep=[[[Ŝēĺēċţ Ĕŋţįţįēş∙∙∙∙]]]
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=[[[Řēʋįēŵ Ĕŋţįţįēş∙∙∙∙]]]
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=[[[Ŝēĺēċţ Ĉŏŋŋēċţįŏŋ / Ţąŗğēţ Ŝρąċē∙∙∙∙∙∙∙∙∙∙]]]

#XTIT: title connection types
connectionTypesTitle=[[[Ĭţēɱş ({0})]]]
#XTIT: title target spaces
targetSpaceTitle=[[[Ŝρąċēş ({0})]]]
#XTIT: title connections
connectionsTitle=[[[Ĉŏŋŋēċţįŏŋş ({0})]]]

#XCOL: space business name
spaceBusinessName=[[[Ŝρąċē∙∙∙∙∙∙∙∙∙]]]
#XCOL: spaces business name
spacesBusinessName=[[[Ŝρąċēş∙∙∙∙∙∙∙∙]]]
#XCOL: business name
businessName=[[[Ɓűşįŋēşş Ńąɱē∙∙∙∙∙∙]]]
#XCOL: technical name
technicalName=[[[Ţēċĥŋįċąĺ Ńąɱē∙∙∙∙∙]]]
#XCOL: modeling pattern
modelingPattern=[[[Μŏƌēĺįŋğ Ƥąţţēŗŋ∙∙∙∙∙∙∙∙]]]
#XCOL: import status
importStatus=[[[Ĭɱρŏŗţ Ŝţąţűş∙∙∙∙∙∙]]]
#XCOL: replication flow
replicationFlow=[[[Řēρĺįċąţįŏŋ Ƒĺŏŵ∙∙∙∙∙∙∙∙]]]
#XCOL: entity
entity=[[[Ĕŋţįţŷ∙∙∙∙∙∙∙∙]]]
#XCOL: api
api=[[[ĀƤĬ∙]]]
#XHED: heading selected entities
selectedEntities=[[[Ŝēĺēċţēƌ Ĕŋţįţįēş∙∙∙∙∙∙∙]]]
#XHED: heading selected entities
selectedApis=[[[Ŝēĺēċţēƌ ĀƤĬş∙∙∙∙∙∙]]]
#XHED: heading dependent entities
dependentEntities=[[[Ďēρēŋƌēŋţ Ĕŋţįţįēş∙∙∙∙∙∙]]]
#XHED: heading dependent entities
dependentEntitiesOf=[[[Ďēρēŋƌēŋţ Ĕŋţįţįēş ŏƒ {0}]]]
#XHED: heading dependent entities
selectedEntitiesOfApi=[[[Ŝēĺēċţēƌ Ĕŋţįţįēş ŏƒ ĀƤĬ {0}]]]
#XHED: heading entities of API
entitiesOfApi=[[[Ĕŋţįţįēş ŏƒ ĀƤĬ {0}]]]
#XHED: heading business builder
businessBuilderObjects=[[[Ɓűşįŋēşş Ɓűįĺƌēŗ ({0})]]]
#XHED: heading data builder
dataBuilderObjects=[[[Ďąţą Ɓűįĺƌēŗ ({0})]]]
#XFLD path delimiter
delimiter=[[[/∙∙∙]]]

#XBUT
buttonPreviousStep=[[[Ƥŗēʋįŏűş∙∙∙∙∙∙]]]
#XBUT
buttonNextStep=[[[Ńēχţ Ŝţēρ∙∙∙∙∙]]]
#XBUT
buttonImport=[[[Ŝţąŗţ Ĭɱρŏŗţ ąŋƌ Ďēρĺŏŷ∙∙∙∙∙∙]]]
#XBUT
buttonSchedule=[[[Ŝċĥēƌűĺē Ĭɱρŏŗţ ąŋƌ Ďēρĺŏŷ∙∙∙∙∙∙∙]]]
#XBUT
buttonCancel=[[[Ĉąŋċēĺ∙∙∙∙∙∙∙∙]]]
#XBUT
buttonContinue=[[[Ĉŏŋţįŋűē∙∙∙∙∙∙]]]
#XMSG
loadConnectionTypesFailed=[[[Ůŋąƃĺē ţŏ ĺŏąƌ ċŏŋŋēċţįŏŋ ţŷρēş.∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
loadTargetSpacesFailed=[[[Ůŋąƃĺē ţŏ ĺŏąƌ ţąŗğēţ şρąċēş.∙∙∙∙∙∙∙∙∙]]]
#XMSG
loadConnectionsFailed=[[[Ůŋąƃĺē ţŏ ĺŏąƌ ċŏŋŋēċţįŏŋş.∙∙∙∙∙∙∙∙]]]
#XMSG
loadBwBridgeConnectionFailed=[[[Ůŋąƃĺē ţŏ ĺŏąƌ ŜĀƤ ƁŴ ƃŗįƌğē ċŏŋŋēċţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
getConnectionStatusFailed=[[[Ůŋąƃĺē ţŏ ŗēţŗįēʋē ţĥē şţąţűş ŏƒ ċŏŋŋēċţįŏŋ {0}.]]]
#XMSG
getSharedConnectionDetailsFailed=[[[Ůŋąƃĺē ţŏ ŗēţŗįēʋē ţĥē ƌēţąįĺş ŏƒ ţĥē ċŏŋŋēċţįŏŋ ƒŏŗ şŷşţēɱ ĬĎ {0}.]]]
#XMSG
connectionNotValid=[[[Ĉŏŋŋēċţįŏŋ {0} įş ŋŏţ ą ʋąĺįƌ ċŏŋŋēċţįŏŋ.]]]
#XMSG
connectionBwBridgeNotValid=[[[ŜĀƤ ƁŴ ƃŗįƌğē ċŏŋŋēċţįŏŋ {0} įş ŋŏţ ą ʋąĺįƌ ċŏŋŋēċţįŏŋ.]]]
#XMSG
connectionBwBridgeNotAvailable=[[[ŜĀƤ ƁŴ ƃŗįƌğē ċŏŋŋēċţįŏŋ įş ŋŏţ ąʋąįĺąƃĺē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
loadEntitiesFailed=[[[Ůŋąƃĺē ţŏ ĺŏąƌ ēŋţįţįēş.∙∙∙∙∙∙]]]
#XMSG
importEntitiesFailed=[[[Ůŋąƃĺē ţŏ įɱρŏŗţ ēŋţįţįēş.∙∙∙∙∙∙∙]]]
#XMSG
importEntitiesStarted=[[[Ĭɱρŏŗţįŋğ ēŋţįţįēş. Ĉĥēċķ ţĥē ŋŏţįƒįċąţįŏŋş ƒŏŗ ţĥē şţąţűş ŏƒ ţĥē įɱρŏŗţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
loadReviewEntitiesListFailed=[[[Ůŋąƃĺē ţŏ ĺŏąƌ ŏƃĵēċţş ţŏ įɱρŏŗţ.∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
selectOneResultListItem=[[[Ŝēĺēċţ ŏŋĺŷ ŏŋē įţēɱ įŋ ţĥē şēąŗċĥ ŗēşűĺţ ĺįşţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
dialogCancel=[[[Ďŏ ŷŏű ŗēąĺĺŷ ŵąŋţ ţŏ ċąŋċēĺ ţĥē įɱρŏŗţ ŏƒ ēŋţįţįēş?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=[[[ĬĎ∙∙]]]
identifier_plural=[[[Ĉŏŋŋēċţįŏŋ {0}]]]
identifier_identifier=[[[ĬĎ∙∙]]]
identifier_entityname=[[[ĬĎ∙∙]]]
identifier_entitylabel=[[[Ļąƃēĺ∙∙∙∙∙∙∙∙∙]]]
#XTXT: SAP BW Bridge Connection type
sapBwBridge=[[[ŜĀƤ ƁŴ Ɓŗįƌğē∙∙∙∙∙∙]]]

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=[[[Ĉŗēąţē Ɓűşįŋēşş Ɓűįĺƌēŗ Ŏƃĵēċţş∙∙∙∙∙∙∙∙∙∙]]]
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=[[[Ɓűşįŋēşş Ĕŋţįţįēş ąŋƌ Ĉŏŋşűɱρţįŏŋ Μŏƌēĺş∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=[[[Ɓűşįŋēşş Ĕŋţįţįēş Ŏŋĺŷ∙∙∙∙∙]]]
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=[[[Ńŏŋē]]]

#XFLD: label Remote Access Mode
remoteAccessModeLabel=[[[Ďąţą Āċċēşş∙∙∙∙∙∙∙∙]]]
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=[[[Řēρĺįċąţįŏŋ Ƒĺŏŵ ţŏ Ļŏċąĺ Ţąƃĺēş∙∙∙∙∙∙∙∙∙∙]]]
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=[[[Řēɱŏţē Ţąƃĺēş∙∙∙∙∙∙]]]
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=[[[Řēρĺįċąţįŏŋ Ƒĺŏŵ∙∙∙∙∙∙∙∙]]]
#XFLD: label Ingestion Space
ingestionSpaceLabel=[[[Ĭŋğēşţįŏŋ Ŝρąċē∙∙∙∙]]]

#XMSG
modelTransferOk=[[[Μŏƌēĺ įɱρŏŗţ įş ēŋąƃĺēƌ.∙∙∙∙∙∙]]]
#XMSG
modelTransferNotOk=[[[Μŏƌēĺ įɱρŏŗţ ċąŋŋŏţ ƃē űşēƌ.∙∙∙∙∙∙∙∙]]]
#XMSG
hanaSdiOk=[[[Řēɱŏţē ţąƃĺēş ąŗē ēŋąƃĺēƌ.∙∙∙∙∙∙∙]]]
#XMSG
hanaSdiNotOk=[[[Řēɱŏţē ţąƃĺēş ċąŋŋŏţ ƃē űşēƌ.∙∙∙∙∙∙∙∙∙]]]
#XMSG
replicationFlowOk=[[[Řēρĺįċąţįŏŋ ƒĺŏŵş ąŗē ēŋąƃĺēƌ.∙∙∙∙∙∙∙∙∙]]]
#XMSG
replicationFlowNotOk=[[[Řēρĺįċąţįŏŋ ƒĺŏŵş ċąŋŋŏţ ƃē űşēƌ.∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
onboardingOk=[[[Ŏŋƃŏąŗƌįŋğ ŏƒ ŗēɱŏţē ţąƃĺēş ąŋƌ ŗēρĺįċąţįŏŋ ƒĺŏŵş įş ēŋąƃĺēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
onboardingNotOk=[[[Ŏŋƃŏąŗƌįŋğ ŏƒ ŗēɱŏţē ţąƃĺēş ąŋƌ ŗēρĺįċąţįŏŋ ƒĺŏŵş įş ŋŏţ ρŏşşįƃĺē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
enableReplicationFlow=[[[Ţĥįş ċŏŋŋēċţįŏŋ ċąŋ şűρρŏŗţ ţĥē ŗēρĺįċąţįŏŋ ŏƒ ƌąţą ʋįą ą ŗēρĺįċąţįŏŋ ƒĺŏŵ, ƃűţ ţĥįş ƒēąţűŗē įş ŋŏţ ċűŗŗēŋţĺŷ ēŋąƃĺēƌ. Ƥĺēąşē ċŏŋţąċţ ŷŏűŗ ąƌɱįŋįşţŗąţŏŗ ţŏ űρƌąţē ţĥē ċŏŋŋēċţįŏŋ. Ĭŋ ţĥē ɱēąŋţįɱē ŷŏű ċąŋ ŏŋĺŷ įɱρŏŗţ ēŋţįţįēş ŵįţĥ ƒēƌēŗąţēƌ ƌąţą ąċċēşş ʋįą ŗēɱŏţē ţąƃĺēş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
selectedApiWithoutEntities=[[[Ţĥē şēĺēċţēƌ ĀƤĬ ƌŏēş ŋŏţ ρŗŏʋįƌē ąŋŷ ēŋţįţįēş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
switchReplicationFlowToRemoteTables=[[[Ţĥēşē ŏƃĵēċţş ąŗē ċűŗŗēŋţĺŷ įŋşţąĺĺēƌ ąş ĺŏċąĺ ţąƃĺēş ŵįţĥ ƌąţą ŗēρĺįċąţēƌ ţŏ ţĥēɱ. Ĉĥąŋğįŋğ ţŏ ƒēƌēŗąţįŏŋ ɱąŷ ƌēğŗąƌē ρēŗƒŏŗɱąŋċē. Ţĥįş ċĥąŋğē ŵįĺĺ įɱρąċţ ąĺĺ şρąċēş ţĥąţ ċŏŋşűɱē ţĥēşē ŏƃĵēċţş.\\u014B\\u014BĎŏ ŷŏű ŵąŋţ ţŏ ċĥąŋğē ţĥē ƌąţą ąċċēşş ţŏ ƒēƌēŗąţįŏŋ ʋįą ŗēɱŏţē ţąƃĺēş?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
switchRemoteTablesToReplicationFlow=[[[Ţĥēşē ŏƃĵēċţş ąŗē ċűŗŗēŋţĺŷ įŋşţąĺĺēƌ ąş ŗēɱŏţē ţąƃĺēş ŵįţĥ ƌąţą ƒēƌēŗąţēƌ ţŏ ţĥēɱ. Ĉĥąŋğįŋğ ţŏ ŗēρĺįċąţįŏŋ ŵįĺĺ įɱρŗŏʋē ρēŗƒŏŗɱąŋċē ƃűţ ŵįĺĺ ąĺşŏ ƌįşąƃĺē ĺįʋē ąċċēşş şŏ ţĥąţ ţĥē ƒŗēşĥŋēşş ŏƒ ŷŏűŗ ƌąţą ƌēρēŋƌş ŏŋ ŷŏűŗ ŗēρĺįċąţįŏŋ şċĥēƌűĺē. Ţĥįş ċĥąŋğē ŵįĺĺ įɱρąċţ ąĺĺ şρąċēş ţĥąţ ċŏŋşűɱē ţĥēşē ŏƃĵēċţş.\\u014B\\u014BĎŏ ŷŏű ŵąŋţ ţŏ ċĥąŋğē ţĥē ƌąţą ąċċēşş ţŏ ŗēρĺįċąţįŏŋ ʋįą ą ŗēρĺįċąţįŏŋ ƒĺŏŵ ąŋƌ ĺŏċąĺ ţąƃĺēş?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XFLD
spaceTypeAbapBridge=[[[ŜĀƤ ƁŴ Ɓŗįƌğē∙∙∙∙∙∙]]]
#XFLD
spaceTypeDatasphere=[[[ŜĀƤ Ďąţąşρĥēŗē∙∙∙∙∙]]]
