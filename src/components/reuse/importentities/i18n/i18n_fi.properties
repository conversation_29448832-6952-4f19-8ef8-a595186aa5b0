#XTIT: Title for import entities
importEntitiesTitle=Tuo entiteetit
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Valitse yhteystyyppi
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Valitse kohdetila
#XFLD: import wizard step 3: select connection
selectConnectionStep=Valitse yhteys
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Valitse entiteetit
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Tarkista entiteetit
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Valitse yhteys/kohdetila

#XTIT: title connection types
connectionTypesTitle=Rivit ({0})
#XTIT: title target spaces
targetSpaceTitle=Tilat ({0})
#XTIT: title connections
connectionsTitle=Yhteydet ({0})

#XCOL: space business name
spaceBusinessName=Tila
#XCOL: spaces business name
spacesBusinessName=Tilat
#XCOL: business name
businessName=Liiketoiminnallinen nimi
#XCOL: technical name
technicalName=Tekninen nimi
#XCOL: modeling pattern
modelingPattern=Mallinnusmalli
#XCOL: import status
importStatus=Tuonnin tila
#XCOL: replication flow
replicationFlow=Replikointivirta
#XCOL: entity
entity=Entiteetti
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Valitut entiteetit
#XHED: heading selected entities
selectedApis=Valitut APIt
#XHED: heading dependent entities
dependentEntities=Sidonnaiset entiteetit
#XHED: heading dependent entities
dependentEntitiesOf=Kohteen {0} sidonnaiset entiteetit
#XHED: heading dependent entities
selectedEntitiesOfApi=Valitut APIn {0} entiteetit
#XHED: heading entities of API
entitiesOfApi=APIn {0} entiteetit
#XHED: heading business builder
businessBuilderObjects=Liiketoiminnan muodostin ({0})
#XHED: heading data builder
dataBuilderObjects=Tietojen muodostin ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Edellinen
#XBUT
buttonNextStep=Seuraava askel
#XBUT
buttonImport=Käynnistä Tuo ja ota käyttöön
#XBUT
buttonSchedule=Ajoita Tuo ja ota käyttöön
#XBUT
buttonCancel=Peruuta
#XBUT
buttonContinue=Jatka
#XMSG
loadConnectionTypesFailed=Yhteystyyppejä ei voi ladata.
#XMSG
loadTargetSpacesFailed=Kohdetiloja ei voi ladata.
#XMSG
loadConnectionsFailed=Yhteyksiä ei voi ladata.
#XMSG
loadBwBridgeConnectionFailed=SAP BW -siltaa ei voi ladata.
#XMSG
getConnectionStatusFailed=Yhteyden {0} tilaa ei voi hakea.
#XMSG
getSharedConnectionDetailsFailed=Järjestelmätunnuksen {0} yhteyden lisätietoja ei voi hakea..
#XMSG
connectionNotValid=Yhteys {0} ei ole kelvollinen yhteys.
#XMSG
connectionBwBridgeNotValid=SAP BW -sillan yhteys {0} ei ole kelvollinen yhteys.
#XMSG
connectionBwBridgeNotAvailable=SAP BW -sillan yhteys ei ole käytettävissä.
#XMSG
loadEntitiesFailed=Entiteettejä ei voi ladata.
#XMSG
importEntitiesFailed=Entiteettejä ei voi tuoda.
#XMSG
importEntitiesStarted=Entiteettejä tuodaan. Tarkista tuonnin tilan ilmoitukset.
#XMSG
loadReviewEntitiesListFailed=Objekteja ei voi ladata tuotavaksi.
#XMSG
selectOneResultListItem=Valitse vain yksi rivi hakutulosluettelosta.
#XMSG
dialogCancel=Haluatko varmasti peruuttaa entiteettien tuonnin?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=Tunnus
identifier_plural=Yhteys {0}
identifier_identifier=Tunnus
identifier_entityname=Tunnus
identifier_entitylabel=Tunniste
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW -silta

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Luo Business Builder -objekteja
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Liiketoimintaentiteetit ja kulutusmallit
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Vain liiketoimintaentiteetit
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Ei mitään

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Tietojen käyttö
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Replikointivirta paikallisiin tauluihin
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Etätaulut
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Replikointivirta
#XFLD: label Ingestion Space
ingestionSpaceLabel=Vastaanottotila

#XMSG
modelTransferOk=Mallin tuonti on otettu käyttöön.
#XMSG
modelTransferNotOk=Mallin tuontia ei voi käyttää.
#XMSG
hanaSdiOk=Etätaulut on otettu käyttöön.
#XMSG
hanaSdiNotOk=Etätauluja ei voi käyttää.
#XMSG
replicationFlowOk=Replikointivirrat on otettu käyttöön.
#XMSG
replicationFlowNotOk=Replikointivirtoja ei voi käyttää.
#XMSG
onboardingOk=Etätaulujen ja replikointivirtojen käyttöönotto on otettu käyttöön.
#XMSG
onboardingNotOk=Etätaulujen ja replikointivirtojen käyttöönotto ei ole mahdollista.
#XMSG
enableReplicationFlow=Yhteys voi tukea tietojen replikointia replikointivirran avulla, mutta ominaisuus ei ole tällä hetkellä käytössä. Ota yhteys pääkäyttäjään yhteyden päivittämiseksi. Sillä aikaa voit tuoda merkintöjä vain yhdistetyssä tietokäytössä etätaulujen avulla.
#XMSG
selectedApiWithoutEntities=Valittu API ei anna entiteettejä.
#XMSG
switchReplicationFlowToRemoteTables=Nämä objektit on tällä hetkellä asennettu paikallisiksi tauluiksi, joihin on replikoitu tietoja. Siirtyminen yhdistettyyn käyttöön voi heikentää suorituskykyä. Muutos vaikuttaa kaikkiin tiloihin, jotka käyttävät näitä objekteja. \n\nHaluatko muuttaa tietojen käytön yhdistetyksi käytöksi etätaulujen avulla?
#XMSG
switchRemoteTablesToReplicationFlow=Nämä objektit on tällä hetkellä asennettu etätauluiksi, joihin on yhdistetty tietoja. Siirtyminen replikointiin parantaa suorituskykyä, mutta poistaa Live Accessin käytöstä, niin että tietojen tuoreus riippuu replikointiaikataulusta. Muutos vaikuttaa kaikkiin tiloihin, jotka käyttävät näitä objekteja. \n\nHaluatko muuttaa tietojen käytön replikoinniksi replikointivirran ja paikallisten taulujen avulla?

#XFLD
spaceTypeAbapBridge=SAP BW -silta
#XFLD
spaceTypeDatasphere=SAP Datasphere
