#XTIT: Title for import entities
importEntitiesTitle=Importera entiteter
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Välj anslutningstyp
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Välj målutrymme
#XFLD: import wizard step 3: select connection
selectConnectionStep=Välj anslutning
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Välj entiteter
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Granska entiteter
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Välj anslutning/målutrymme

#XTIT: title connection types
connectionTypesTitle=Element ({0})
#XTIT: title target spaces
targetSpaceTitle=Utrymmen ({0})
#XTIT: title connections
connectionsTitle=Anslutningar({0})

#XCOL: space business name
spaceBusinessName=Utrymme
#XCOL: spaces business name
spacesBusinessName=Utrymmen
#XCOL: business name
businessName=Affärsnamn
#XCOL: technical name
technicalName=Tekniskt namn
#XCOL: modeling pattern
modelingPattern=Modelleringsmönster
#XCOL: import status
importStatus=Importstatus
#XCOL: replication flow
replicationFlow=Replikeringsflöde
#XCOL: entity
entity=Entitet
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Valda entiteter
#XHED: heading selected entities
selectedApis=Valda API:er
#XHED: heading dependent entities
dependentEntities=Beroende entiteter
#XHED: heading dependent entities
dependentEntitiesOf=Beroende entiteter för {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Valda entiteter för API {0}
#XHED: heading entities of API
entitiesOfApi=Entiteter för API {0}
#XHED: heading business builder
businessBuilderObjects=Business Builder ({0})
#XHED: heading data builder
dataBuilderObjects=Data Builder ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Föregående
#XBUT
buttonNextStep=Nästa steg
#XBUT
buttonImport=Starta Importera och distribuera
#XBUT
buttonSchedule=Schemalägg Importera och distribuera
#XBUT
buttonCancel=Avbryt
#XBUT
buttonContinue=Fortsätt
#XMSG
loadConnectionTypesFailed=Anslutningstyper kunde inte läsas in.
#XMSG
loadTargetSpacesFailed=Målutrymmen kunde inte läsas in.
#XMSG
loadConnectionsFailed=Anslutningar kunde inte läsas in.
#XMSG
loadBwBridgeConnectionFailed=SAP BW Bridge-anslutning kunde inte läsas in.
#XMSG
getConnectionStatusFailed=Status för anslutning {0} kunde inte hämtas.
#XMSG
getSharedConnectionDetailsFailed=Detaljer för anslutning för system-ID {0} kunde inte hämtas.
#XMSG
connectionNotValid=Anslutning {0} är inte en giltig anslutning.
#XMSG
connectionBwBridgeNotValid=SAP BW Bridge-anslutning {0} är inte en giltig anslutning.
#XMSG
connectionBwBridgeNotAvailable=SAP BW Bridge-anslutning är inte tillgänglig.
#XMSG
loadEntitiesFailed=Entiteter kunde inte läsas in.
#XMSG
importEntitiesFailed=Entiteter kunde inte importeras.
#XMSG
importEntitiesStarted=Importerar entiteter. Se aviseringarna för att se status för importen.
#XMSG
loadReviewEntitiesListFailed=Objekt som ska importeras kunde inte läsas in.
#XMSG
selectOneResultListItem=Välj endast en position i sökresultatlistan.
#XMSG
dialogCancel=Avbryta import av entiteter?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=Anslutning {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=Etikett
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW Bridge

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Skapa Business Builder-objekt
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Affärsentiteter och förbrukningsmodeller
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Endast affärsentiteter
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Ingen

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Dataåtkomst
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Replikeringsflöde till lokala tabeller
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Fjärrtabeller
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Replikeringsflöde
#XFLD: label Ingestion Space
ingestionSpaceLabel=Inmatningsutrymme

#XMSG
modelTransferOk=Modellimport har aktiverats.
#XMSG
modelTransferNotOk=Modellimport kan inte användas.
#XMSG
hanaSdiOk=Fjärrtabeller har aktiverats.
#XMSG
hanaSdiNotOk=Fjärrtabeller kan inte användas.
#XMSG
replicationFlowOk=Replikeringsflöden har aktiverats.
#XMSG
replicationFlowNotOk=Replikeringsflöden kan inte användas.
#XMSG
onboardingOk=Onboarding av fjärrtabeller och replikeringsflöden har aktiverats.
#XMSG
onboardingNotOk=Onboarding av fjärrtabeller och replikeringsflöden är inte möjlig.
#XMSG
enableReplicationFlow=Denna anslutning medger replikering av data via ett replikeringsflöde, men funktionen är för närvarande inte aktiverad. Kontakta administratören för att uppdatera anslutningen. Under tiden kan du endast importera entiteter med samordnad dataåtkomst via fjärrtabeller.
#XMSG
selectedApiWithoutEntities=Valt API tillhandahåller inga entiteter.
#XMSG
switchReplicationFlowToRemoteTables=Dessa objekt är för närvarande installerade som lokala tabeller med data replikerade till dem. Om du ändrar till federation kan prestandan försämras. Ändringen påverkar alla utrymmen som förbrukar dessa objekt.\n\nVill du ändra dataåtkomsten till federation via fjärrtabeller?
#XMSG
switchRemoteTablesToReplicationFlow=Dessa objekt är för närvarande installerade som fjärrtabeller med data samordnade till dem. Om du ändrar till replikering förbättras prestandan, men liveåtkomst inaktiveras också så att aktualiteten för dina data är beroende av ditt replikeringsschema. ar till federation kan prestandan försämras. Ändringen påverkar alla utrymmen som förbrukar dessa objekt.\n\nVill du ändra dataåtkomsten till replikering via ett replikeringsflöde och lokala tabeller?

#XFLD
spaceTypeAbapBridge=SAP BW Bridge
#XFLD
spaceTypeDatasphere=SAP Datasphere
