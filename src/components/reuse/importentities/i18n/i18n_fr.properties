#XTIT: Title for import entities
importEntitiesTitle=Importer les entités
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Sélectionner un type de connexion
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Sélectionner un espace cible
#XFLD: import wizard step 3: select connection
selectConnectionStep=Sélectionner une connexion
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Sélectionner des entités
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Réviser les entités
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Sélectionner une connexion/un espace cible

#XTIT: title connection types
connectionTypesTitle=Éléments ({0})
#XTIT: title target spaces
targetSpaceTitle=Espaces ({0})
#XTIT: title connections
connectionsTitle=Connexions ({0})

#XCOL: space business name
spaceBusinessName=Espace
#XCOL: spaces business name
spacesBusinessName=Espaces
#XCOL: business name
businessName=Appellation
#XCOL: technical name
technicalName=Nom technique
#XCOL: modeling pattern
modelingPattern=Modèle de modélisation
#XCOL: import status
importStatus=Statut de l'import
#XCOL: replication flow
replicationFlow=Flux de réplication
#XCOL: entity
entity=Entité
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Entités sélectionnées
#XHED: heading selected entities
selectedApis=API sélectionnées
#XHED: heading dependent entities
dependentEntities=Entités dépendantes
#XHED: heading dependent entities
dependentEntitiesOf=Entités dépendantes de {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Entités sélectionnées des API {0}
#XHED: heading entities of API
entitiesOfApi=Entités de l''API {0}
#XHED: heading business builder
businessBuilderObjects=Business Builder ({0})
#XHED: heading data builder
dataBuilderObjects=Data Builder ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Précédent
#XBUT
buttonNextStep=Étape suivante
#XBUT
buttonImport=Lancer l'import et le déploiement
#XBUT
buttonSchedule=Planifier l'import et le déploiement
#XBUT
buttonCancel=Annuler
#XBUT
buttonContinue=Poursuivre
#XMSG
loadConnectionTypesFailed=Impossible de charger les types de connexion.
#XMSG
loadTargetSpacesFailed=Impossible de charger les espaces cible.
#XMSG
loadConnectionsFailed=Impossible de charger les connexions.
#XMSG
loadBwBridgeConnectionFailed=Impossible de charger une connexion du pont vers SAP BW.
#XMSG
getConnectionStatusFailed=Impossible de récupérer le statut de la connexion {0}.
#XMSG
getSharedConnectionDetailsFailed=Impossible de récupérer les détails de la connexion pour l''ID système {0}.
#XMSG
connectionNotValid=La connexion {0} n''est pas une connexion valide.
#XMSG
connectionBwBridgeNotValid=La connexion du pont vers SAP BW {0} n''est pas une connexion valide.
#XMSG
connectionBwBridgeNotAvailable=La connexion du pont vers SAP BW n'est pas disponible.
#XMSG
loadEntitiesFailed=Impossible de charger des entités.
#XMSG
importEntitiesFailed=Impossible d'importer des entités.
#XMSG
importEntitiesStarted=Entités en cours d'import. Consultez les notifications pour connaître le statut de l'import.
#XMSG
loadReviewEntitiesListFailed=Impossible de charger des objets à importer.
#XMSG
selectOneResultListItem=Sélectionnez un seul et unique élément dans la liste des résultats de la recherche.
#XMSG
dialogCancel=Voulez-vous vraiment annuler l'import d'entités ?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=Connexion {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=Étiquette
#XTXT: SAP BW Bridge Connection type
sapBwBridge=Pont vers SAP BW

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Créer des objets du Business Builder
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Entités commerciales et modèles de consommation
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Entités commerciales uniquement
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Aucun

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Accès aux données
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Flux de réplication dans les tables locales
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Tables distantes
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Flux de réplication
#XFLD: label Ingestion Space
ingestionSpaceLabel=Espace d'ingestion

#XMSG
modelTransferOk=L'import de modèle est activé.
#XMSG
modelTransferNotOk=L'import de modèle ne peut pas être utilisé.
#XMSG
hanaSdiOk=Les tables distantes sont activées.
#XMSG
hanaSdiNotOk=Les tables distantes ne peuvent pas être utilisées.
#XMSG
replicationFlowOk=Les flux de réplication sont activés.
#XMSG
replicationFlowNotOk=Les flux de réplication ne peuvent pas être utilisés.
#XMSG
onboardingOk=L'intégration de tables distantes et de flux de réplication est activée.
#XMSG
onboardingNotOk=L'intégration de tables distantes et de flux de réplication n'est pas possible.
#XMSG
enableReplicationFlow=Cette connexion peut prendre en charge la réplication de données via un flux de réplication, mais cette fonctionnalité est actuellement désactivée. Contactez l'administrateur pour mettre à jour la connexion. Tant que la mise à jour n'aura pas eu lieu, vous pourrez uniquement importer des entités avec un accès fédéré aux données via des tables distantes.
#XMSG
selectedApiWithoutEntities=L'API sélectionnée ne fournit aucune entité.
#XMSG
switchReplicationFlowToRemoteTables=Ces objets sont actuellement installés en tant que tables locales avec des données qui leur sont répliquées. Le passage au mode fédération peut dégrader la performance. Cette modification aura un impact sur tous les espaces qui utilisent ces objets.\n\nVoulez-vous définir l'accès aux données sur le mode fédération via des tables distantes ?
#XMSG
switchRemoteTablesToReplicationFlow=Ces objets sont actuellement installés en tant que tables distantes avec des données qui leur sont fédérées. Le passage au mode réplication améliorera la performance mais désactivera également l'accès en ligne. Ainsi, l'actualisation de vos données dépend de la planification de votre réplication. Cette modification aura un impact sur tous les espaces qui utilisent ces objets.\n\nVoulez-vous définir l'accès aux données sur le mode réplication via un flux de réplication et des tables locales ?

#XFLD
spaceTypeAbapBridge=Pont vers SAP BW
#XFLD
spaceTypeDatasphere=SAP Datasphere
