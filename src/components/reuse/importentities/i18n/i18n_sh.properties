#XTIT: Title for import entities
importEntitiesTitle=Uvezi entitete
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Odaberi tip veze
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Odaberi ciljni prostor
#XFLD: import wizard step 3: select connection
selectConnectionStep=Odaberi vezu
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Odaberi entitete
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Pregledaj entitete
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Odaberi vezu / ciljni prostor 

#XTIT: title connection types
connectionTypesTitle=Stavke ({0})
#XTIT: title target spaces
targetSpaceTitle=Prostori ({0})
#XTIT: title connections
connectionsTitle=Veze ({0})

#XCOL: space business name
spaceBusinessName=Prostor
#XCOL: spaces business name
spacesBusinessName=Prostori
#XCOL: business name
businessName=Poslovni naziv
#XCOL: technical name
technicalName=Tehnički naziv
#XCOL: modeling pattern
modelingPattern=Šablon modeliranja
#XCOL: import status
importStatus=Status uvoza
#XCOL: replication flow
replicationFlow=Tok replikacije
#XCOL: entity
entity=Entitet
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Odabrani entiteti
#XHED: heading selected entities
selectedApis=Odabrani API-ji
#XHED: heading dependent entities
dependentEntities=Zavisni entiteti
#XHED: heading dependent entities
dependentEntitiesOf=Zavisni entiteti za {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Odabrani entiteti API-ja {0}
#XHED: heading entities of API
entitiesOfApi=Entiteti API-ja {0}
#XHED: heading business builder
businessBuilderObjects=Generator poslovanja ({0})
#XHED: heading data builder
dataBuilderObjects=Generator podataka ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Prethodno
#XBUT
buttonNextStep=Sledeći korak
#XBUT
buttonImport=Pokreni uvoz i implementaciju
#XBUT
buttonSchedule=Planiraj uvoz i implementaciju
#XBUT
buttonCancel=Odustani
#XBUT
buttonContinue=Nastavi
#XMSG
loadConnectionTypesFailed=Nije moguće učitati tipove veze.
#XMSG
loadTargetSpacesFailed=Nije moguće učitati ciljne prostore.
#XMSG
loadConnectionsFailed=Nije moguće učitati veze.
#XMSG
loadBwBridgeConnectionFailed=Nije moguće učitati vezu premošćavanja aplikacije SAP BW.
#XMSG
getConnectionStatusFailed=Nije moguće pozvati status veze {0}.
#XMSG
getSharedConnectionDetailsFailed=Nije moguće pozvati detalje veze za ID sistema {0}.
#XMSG
connectionNotValid=Veza {0} nije važeća veza.
#XMSG
connectionBwBridgeNotValid=Veza premošćavanja aplikacije SAP BW {0} nije važeća veza.
#XMSG
connectionBwBridgeNotAvailable=Veza premošćavanja aplikacije SAP BW nije dostupna.
#XMSG
loadEntitiesFailed=Nije moguće učitati entitete.
#XMSG
importEntitiesFailed=Nije moguće uvesti entitete.
#XMSG
importEntitiesStarted=Uvoz entiteta. Proverite obaveštenja za status uvoza.
#XMSG
loadReviewEntitiesListFailed=Nije moguće učitati objekte za uvoz.
#XMSG
selectOneResultListItem=Odaberite samo jednu stavku na listi rezultata traženja.
#XMSG
dialogCancel=Da li zaista želite da otkažete uvoz entiteta?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=Veza {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=Oznaka
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW Bridge

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Kreirajte objekte generatora poslovanja
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Poslovni entiteti i modeli potrošnje
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Samo poslovni entiteti
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Ništa

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Pristup podacima
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Tok replikacije za lokalne tabele
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Udaljene tabele
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Tok replikacije
#XFLD: label Ingestion Space
ingestionSpaceLabel=Prostor brzog unošenja

#XMSG
modelTransferOk=Uvoz modela je aktiviran.
#XMSG
modelTransferNotOk=Uvoz modela se ne može koristiti.
#XMSG
hanaSdiOk=Udaljene tabele su aktivirane.
#XMSG
hanaSdiNotOk=Udaljene tabele se ne mogu koristiti.
#XMSG
replicationFlowOk=Tokovi replikacije su aktivirani.
#XMSG
replicationFlowNotOk=Tokovi replikacije se ne mogu koristiti.
#XMSG
onboardingOk=Uvođenje udaljenih tabela i tokova replikacije je aktivirano.
#XMSG
onboardingNotOk=Uvođenje udaljenih tabela i tokova replikacije nije moguće.
#XMSG
enableReplicationFlow=Ova veza može da podržava replikaciju podataka preko toka replikacije, ali ova funkcija trenutno nije aktivirana. Obratite se administratoru kako bi ažurirao vezu. U međuvremenu možete da uvozite entitete samo pomoću povezanog pristupa podacima preko udaljenih tabela.
#XMSG
selectedApiWithoutEntities=Odabrani API ne daje nijedan entitet.
#XMSG
switchReplicationFlowToRemoteTables=Ovi objekti su trenutno instalirani kao lokalne tabele s podacima koji su za njih replicirani. Promena u federaciju može narušiti učinak. Ova promena će uticati na sve prostore koji koriste te objekte.\n\nDa li želite da promenite pristup podacima u federaciju preko udaljenih tabela?
#XMSG
switchRemoteTablesToReplicationFlow=Ovi objekti su trenutno instalirani kao udaljene tabele s podacima koji su im dodeljeni preko federacije. Promena u replikaciju će poboljšati učinak, ali će takođe deaktivirati pristup uživo, tako da aktuelnost vaših podataka zavisi od rasporeda replikacije. Ova promena će uticati na sve prostore koji koriste te objekte.\n\nDa li želite da promenite pristup podacima u replikaciju preko toka replikacije i lokalnih tabela?

#XFLD
spaceTypeAbapBridge=Premošćavanje aplikacije SAP BW
#XFLD
spaceTypeDatasphere=SAP Datasphere
