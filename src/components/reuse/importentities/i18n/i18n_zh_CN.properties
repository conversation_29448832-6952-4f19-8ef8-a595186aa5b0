#XTIT: Title for import entities
importEntitiesTitle=导入实体
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=选择连接类型
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=选择目标空间
#XFLD: import wizard step 3: select connection
selectConnectionStep=选择连接
#XFLD: import wizard step 4: select entities
selectEntitiesStep=选择实体
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=检查实体
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=选择连接/目标空间

#XTIT: title connection types
connectionTypesTitle=项目（{0}）
#XTIT: title target spaces
targetSpaceTitle=空间（{0}）
#XTIT: title connections
connectionsTitle=连接（{0}）

#XCOL: space business name
spaceBusinessName=空间
#XCOL: spaces business name
spacesBusinessName=空间
#XCOL: business name
businessName=业务名称
#XCOL: technical name
technicalName=技术名称
#XCOL: modeling pattern
modelingPattern=建模模式
#XCOL: import status
importStatus=导入状态
#XCOL: replication flow
replicationFlow=复制流
#XCOL: entity
entity=实体
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=所选实体
#XHED: heading selected entities
selectedApis=所选 API
#XHED: heading dependent entities
dependentEntities=依赖实体
#XHED: heading dependent entities
dependentEntitiesOf={0} 的依赖实体
#XHED: heading dependent entities
selectedEntitiesOfApi=所选 API  {0} 的实体
#XHED: heading entities of API
entitiesOfApi=API {0} 的实体
#XHED: heading business builder
businessBuilderObjects=业务模型构建器（{0}）
#XHED: heading data builder
dataBuilderObjects=数据模型构建器（{0}）
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=上一步
#XBUT
buttonNextStep=下一步
#XBUT
buttonImport=开始导入并部署
#XBUT
buttonSchedule=计划导入并部署
#XBUT
buttonCancel=取消
#XBUT
buttonContinue=继续
#XMSG
loadConnectionTypesFailed=没能加载连接类型。
#XMSG
loadTargetSpacesFailed=没能加载目标空间。
#XMSG
loadConnectionsFailed=没能加载连接。
#XMSG
loadBwBridgeConnectionFailed=没能加载 SAP BW 网桥连接。
#XMSG
getConnectionStatusFailed=没能获取连接 {0} 的状态。
#XMSG
getSharedConnectionDetailsFailed=没能检索系统 ID {0} 的连接详细信息 。
#XMSG
connectionNotValid=连接 {0} 不是有效连接。
#XMSG
connectionBwBridgeNotValid=SAP BW 网桥连接 {0} 不是有效连接。
#XMSG
connectionBwBridgeNotAvailable=SAP BW 网桥连接不可用。
#XMSG
loadEntitiesFailed=没能加载实体。
#XMSG
importEntitiesFailed=没能导入实体。
#XMSG
importEntitiesStarted=正在导入实体。请查看通知，了解导入状态。
#XMSG
loadReviewEntitiesListFailed=没能加载要导入的对象。
#XMSG
selectOneResultListItem=仅在搜索结果列表中选择一项。
#XMSG
dialogCancel=是否确定要取消导入实体？
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=连接 {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=标签
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW 网桥

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=创建业务模型构建器对象
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=业务实体和使用模型
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=仅业务实体
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=无

#XFLD: label Remote Access Mode
remoteAccessModeLabel=数据访问
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=复制流到本地表
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=远程表
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=复制流
#XFLD: label Ingestion Space
ingestionSpaceLabel=引入空间

#XMSG
modelTransferOk=已经启用模型导入。
#XMSG
modelTransferNotOk=不可以使用模型导入。
#XMSG
hanaSdiOk=已经启用远程表。
#XMSG
hanaSdiNotOk=不可以使用远程表。
#XMSG
replicationFlowOk=已经启用复制流。
#XMSG
replicationFlowNotOk=不可以使用复制流。
#XMSG
onboardingOk=已启用远程表和复制流的载入。
#XMSG
onboardingNotOk=没能载入远程表和复制流。
#XMSG
enableReplicationFlow=这个连接支持通过复制流复制数据，但当前未启用这项功能。 请联系管理员更新连接。 同时，你只能通过远程表导入具有联合数据访问权限的实体。
#XMSG
selectedApiWithoutEntities=所选 API 没有提供任何实体。
#XMSG
switchReplicationFlowToRemoteTables=这些对象当前以本地表形式安装，数据已复制到其中。更改为联合查询可能导致性能下降。这种更改将会影响使用这些对象的所有空间。\n\n是否希望将数据访问方式更改为通过远程表联合查询？
#XMSG
switchRemoteTablesToReplicationFlow=这些对象当前以远程表形式安装，数据采用联合查询方式访问。更改为复制模式将提升性能，但会失去实时访问能力，数据的新鲜度将取决于你的复制计划。这种更改将会影响使用这些对象的所有空间。\n\n是否希望将数据访问方式更改为通过复制流和本地表进行数据复制？

#XFLD
spaceTypeAbapBridge=SAP BW 网桥
#XFLD
spaceTypeDatasphere=SAP Datasphere
