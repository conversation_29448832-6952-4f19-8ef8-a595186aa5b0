#XTIT: Title for import entities
importEntitiesTitle=Importar entidades
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Selecionar tipo de ligação
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Selecionar espaço de destino
#XFLD: import wizard step 3: select connection
selectConnectionStep=Selecionar ligação
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Selecionar entidades
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Rever entidades
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Selecionar ligação/espaço de destino

#XTIT: title connection types
connectionTypesTitle=Itens ({0})
#XTIT: title target spaces
targetSpaceTitle=Espaços ({0})
#XTIT: title connections
connectionsTitle=Ligações ({0})

#XCOL: space business name
spaceBusinessName=Espaço
#XCOL: spaces business name
spacesBusinessName=Espaços
#XCOL: business name
businessName=Nome comercial
#XCOL: technical name
technicalName=Nome técnico
#XCOL: modeling pattern
modelingPattern=Padrão de modelação
#XCOL: import status
importStatus=Estado de importação
#XCOL: replication flow
replicationFlow=Fluxo de replicação
#XCOL: entity
entity=Entidade
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Entidades selecionadas
#XHED: heading selected entities
selectedApis=APIs selecionadas
#XHED: heading dependent entities
dependentEntities=Entidades dependentes
#XHED: heading dependent entities
dependentEntitiesOf=Entidades dependentes de {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Entidades selecionadas da API {0}
#XHED: heading entities of API
entitiesOfApi=Entidades da API {0}
#XHED: heading business builder
businessBuilderObjects=Gerador de negócio ({0})
#XHED: heading data builder
dataBuilderObjects=Gerador de dados ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Anterior
#XBUT
buttonNextStep=Passo seguinte
#XBUT
buttonImport=Iniciar importação e implementação
#XBUT
buttonSchedule=Agendar importação e implementação
#XBUT
buttonCancel=Cancelar
#XBUT
buttonContinue=Continuar
#XMSG
loadConnectionTypesFailed=Impossível carregar tipos de ligação.
#XMSG
loadTargetSpacesFailed=Impossível carregar espaços de destino.
#XMSG
loadConnectionsFailed=Impossível carregar ligações.
#XMSG
loadBwBridgeConnectionFailed=Impossível carregar a ligação de SAP BW Bridge.
#XMSG
getConnectionStatusFailed=Impossível obter o estado da ligação {0}.
#XMSG
getSharedConnectionDetailsFailed=Impossível obter os detalhes da ligação para o ID de sistema {0}
#XMSG
connectionNotValid=A ligação {0} não é uma ligação válida.
#XMSG
connectionBwBridgeNotValid=A ligação de SAP BW Bridge {0} não é uma ligação válida.
#XMSG
connectionBwBridgeNotAvailable=A ligação de SAP BW Bridge não está disponível.
#XMSG
loadEntitiesFailed=Impossível carregar entidades.
#XMSG
importEntitiesFailed=Impossível importar entidades.
#XMSG
importEntitiesStarted=A importar entidades. Verifique as notificações para o estado da importação.
#XMSG
loadReviewEntitiesListFailed=Impossível carregar objetos a importar.
#XMSG
selectOneResultListItem=Selecione apenas um item na lista de resultados de pesquisa.
#XMSG
dialogCancel=Quer mesmo cancelar a importação das entidades?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=Ligação {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=Etiqueta
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW Bridge

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Criar objetos do gerador de negócio
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Entidades de negócio e modelos de consumo
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Só entidades de negócio
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Nenhum

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Acesso a dados
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Fluxo de replicação para tabelas locais
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Tabelas remotas
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Fluxo de replicação
#XFLD: label Ingestion Space
ingestionSpaceLabel=Espaço de ingestão

#XMSG
modelTransferOk=A importação de modelo está ativada.
#XMSG
modelTransferNotOk=A importação de modelo não pode ser utilizada.
#XMSG
hanaSdiOk=Tabelas remotas ativadas.
#XMSG
hanaSdiNotOk=As tabelas remotas não podem ser utilizadas.
#XMSG
replicationFlowOk=Fluxos de replicação ativados.
#XMSG
replicationFlowNotOk=Os fluxos de replicação não podem ser utilizados.
#XMSG
onboardingOk=A inclusão de tabelas remotas e fluxos de replicação está ativada.
#XMSG
onboardingNotOk=A inclusão de tabelas remotas e fluxos de replicação não é possível.
#XMSG
enableReplicationFlow=Esta ligação pode suportar a replicação de dados mediante um fluxo de replicação, mas esta funcionalidade não está ativada de momento. Contacte o seu administrador para atualizar a ligação. Entretanto, só pode importar entidades com o acesso a dados federados via tabelas remotas.
#XMSG
selectedApiWithoutEntities=A API selecionada não fornece entidades.
#XMSG
switchReplicationFlowToRemoteTables=Atualmente, estes objetos estão instalados como tabelas locais com dados replicados para elas. A alteração para federação pode degradar o desempenho. Esta alteração poderá ter impacto em todos os espaços que utilizam estes objetos.\n\nQuer alterar o acesso aos dados para federação via tabelas remotas?
#XMSG
switchRemoteTablesToReplicationFlow=Atualmente, estes objetos estão instalados como tabelas remotas com dados federados para elas. A alteração para replicação melhorará o desempenho, mas também desativará o acesso em direto, de modo que a atualidade dos seus dados depende da sua agenda de replicação. Esta alteração terá impacto em todos os espaços que utilizam estes objetos.\n\nQuer alterar o acesso aos dados para replicação via um fluxo de replicação e tabelas locais?

#XFLD
spaceTypeAbapBridge=Ponte do SAP BW
#XFLD
spaceTypeDatasphere=SAP Datasphere
