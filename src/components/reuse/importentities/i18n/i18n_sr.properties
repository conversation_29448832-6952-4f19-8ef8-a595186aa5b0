#XTIT: Title for import entities
importEntitiesTitle=Увези ентитете
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Одабери тип везе
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Одабери циљни простор
#XFLD: import wizard step 3: select connection
selectConnectionStep=Одабери везу
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Одабери ентитете
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Прегледај ентитете
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Одабери везу / циљни простор 

#XTIT: title connection types
connectionTypesTitle=Ставке ({0})
#XTIT: title target spaces
targetSpaceTitle=Простори ({0})
#XTIT: title connections
connectionsTitle=Везе ({0})

#XCOL: space business name
spaceBusinessName=Простор
#XCOL: spaces business name
spacesBusinessName=Простори
#XCOL: business name
businessName=Пословни назив
#XCOL: technical name
technicalName=Технички назив
#XCOL: modeling pattern
modelingPattern=Шаблон моделирања
#XCOL: import status
importStatus=Статус увоза
#XCOL: replication flow
replicationFlow=Ток репликације
#XCOL: entity
entity=Ентитет
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Одабрани ентитети
#XHED: heading selected entities
selectedApis=Одабрани API-ји
#XHED: heading dependent entities
dependentEntities=Зависни ентитети
#XHED: heading dependent entities
dependentEntitiesOf=Зависни ентитети за {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Одабрани ентитети API-ја {0}
#XHED: heading entities of API
entitiesOfApi=Ентитети API-ја {0}
#XHED: heading business builder
businessBuilderObjects=Генератор пословања ({0})
#XHED: heading data builder
dataBuilderObjects=Генератор података ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Претходно
#XBUT
buttonNextStep=Следећи корак
#XBUT
buttonImport=Покрени увоз и имплементацију
#XBUT
buttonSchedule=Планирај увоз и имплементацију
#XBUT
buttonCancel=Одустани
#XBUT
buttonContinue=Настави
#XMSG
loadConnectionTypesFailed=Није могуће учитати типове везе.
#XMSG
loadTargetSpacesFailed=Није могуће учитати циљне просторе.
#XMSG
loadConnectionsFailed=Није могуће учитати везе.
#XMSG
loadBwBridgeConnectionFailed=Није могуће учитати везу премошћавања апликације SAP BW.
#XMSG
getConnectionStatusFailed=Није могуће позвати статус везе {0}.
#XMSG
getSharedConnectionDetailsFailed=Није могуће позвати детаље везе за ID система {0}.
#XMSG
connectionNotValid=Веза {0} није важећа веза.
#XMSG
connectionBwBridgeNotValid=Веза премошћавања апликације SAP BW {0} није важећа веза.
#XMSG
connectionBwBridgeNotAvailable=Веза премошћавања апликације SAP BW није доступна.
#XMSG
loadEntitiesFailed=Није могуће учитати ентитете.
#XMSG
importEntitiesFailed=Није могуће увести ентитете.
#XMSG
importEntitiesStarted=Увоз ентитета. Проверите обавештења за статус увоза.
#XMSG
loadReviewEntitiesListFailed=Није могуће учитати објекте за увоз.
#XMSG
selectOneResultListItem=Одаберите само једну ставку на листи резултата тражења.
#XMSG
dialogCancel=Да ли заиста желите да откажете увоз ентитета?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=Веза {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=Ознака
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW Bridge

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Креирајте објекте генератора пословања
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Пословни ентитети и модели потрошње
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Само пословни ентитети
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Ништа

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Приступ подацима
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Ток репликације за локалне табеле
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Удаљене табеле
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Ток репликације
#XFLD: label Ingestion Space
ingestionSpaceLabel=Простор брзог уношења

#XMSG
modelTransferOk=Увоз модела је активиран.
#XMSG
modelTransferNotOk=Увоз модела се не може користити.
#XMSG
hanaSdiOk=Удаљене табеле су активиране.
#XMSG
hanaSdiNotOk=Удаљене табеле се не могу користити.
#XMSG
replicationFlowOk=Токови репликације су активирани.
#XMSG
replicationFlowNotOk=Токови репликације се не могу користити.
#XMSG
onboardingOk=Увођење удаљених табела и токова репликације је активирано.
#XMSG
onboardingNotOk=Увођење удаљених табела и токова репликације није могуће.
#XMSG
enableReplicationFlow=Ова веза може да подржава репликацију података преко тока репликације, али ова функција тренутно није активирана. Обратите се администратору како би ажурирао везу. У међувремену можете да увозите ентитете само помоћу повезаног приступа подацима преко удаљених табела.
#XMSG
selectedApiWithoutEntities=Одабрани API не даје ниједан ентитет.
#XMSG
switchReplicationFlowToRemoteTables=Ови објекти су тренутно инсталирани као локалне табеле с подацима који су за њих реплицирани. Промена у федерацију може нарушити учинак. Ова промена ће утицати на све просторе који користе те објекте.\n\nДа ли желите да промените приступ подацима у федерацију преко удаљених табела?
#XMSG
switchRemoteTablesToReplicationFlow=Ови објекти су тренутно инсталирани као удаљене табеле с подацима који су им додељени преко федерације. Промена у репликацију ће побољшати учинак, али ће такође деактивирати приступ уживо, тако да актуелност ваших података зависи од распореда репликације. Ова промена ће утицати на све просторе који користе те објекте.\n\nДа ли желите да промените приступ подацима у репликацију преко тока репликације и локалних табела?

#XFLD
spaceTypeAbapBridge=Премошћавање апликације SAP BW
#XFLD
spaceTypeDatasphere=SAP Datasphere
