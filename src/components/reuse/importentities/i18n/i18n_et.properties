#XTIT: Title for import entities
importEntitiesTitle=Impordi olemid
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Vali ühenduse tüüp
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Vali sihtruum
#XFLD: import wizard step 3: select connection
selectConnectionStep=Vali ühendus
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Vali olemid
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Vaata olemid üle
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Valige ühendus/sihtruum

#XTIT: title connection types
connectionTypesTitle=Üksused ({0})
#XTIT: title target spaces
targetSpaceTitle=Ruumid ({0})
#XTIT: title connections
connectionsTitle=Ühendused ({0})

#XCOL: space business name
spaceBusinessName=Ruum
#XCOL: spaces business name
spacesBusinessName=Ruumid
#XCOL: business name
businessName=Ärinimi
#XCOL: technical name
technicalName=Tehniline nimi
#XCOL: modeling pattern
modelingPattern=Modelleerimismuster
#XCOL: import status
importStatus=Impordi olek
#XCOL: replication flow
replicationFlow=Alliktabelite replikeerimise voog
#XCOL: entity
entity=Olem
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Valitud olemid
#XHED: heading selected entities
selectedApis=Valitud API-d
#XHED: heading dependent entities
dependentEntities=Sõltuvad olemid
#XHED: heading dependent entities
dependentEntitiesOf={0} sõltuvad üksused
#XHED: heading dependent entities
selectedEntitiesOfApi=API {0} valitud olemid
#XHED: heading entities of API
entitiesOfApi=API üksused {0}
#XHED: heading business builder
businessBuilderObjects=Ärikoostur ({0})
#XHED: heading data builder
dataBuilderObjects=Andmekoostur ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Eelmine
#XBUT
buttonNextStep=Järgmine etapp
#XBUT
buttonImport=Alusta importi ja juurutamist
#XBUT
buttonSchedule=Ajasta import ja juurutamine
#XBUT
buttonCancel=Tühista
#XBUT
buttonContinue=Jätka
#XMSG
loadConnectionTypesFailed=Ühendusetüüpe ei saa laadida.
#XMSG
loadTargetSpacesFailed=Sihtruume ei saa laadida.
#XMSG
loadConnectionsFailed=Ühendusi ei saa laadida.
#XMSG
loadBwBridgeConnectionFailed=SAP BW silla ühendust ei saa laadida.
#XMSG
getConnectionStatusFailed=Ühenduse {0} olekut ei saa tuua.
#XMSG
getSharedConnectionDetailsFailed=Süsteemi ID {0} ühenduse üksikasju ei saa tuua.
#XMSG
connectionNotValid=Ühendust {0} pole sobiv ühendus.
#XMSG
connectionBwBridgeNotValid=SAP BW silla ühendus {0} pole sobiv ühendus.
#XMSG
connectionBwBridgeNotAvailable=SAP BW silla ühendus pole saadaval.
#XMSG
loadEntitiesFailed=Olemeid ei saa laadida.
#XMSG
importEntitiesFailed=Olemeid ei saa importida.
#XMSG
importEntitiesStarted=Olemite importimine on pooleli. Importimise olekut vaadake teadetest.
#XMSG
loadReviewEntitiesListFailed=Imporditavaid objekte ei saa laadida.
#XMSG
selectOneResultListItem=Valige otsingutulemite loendis ainult üks üksus.
#XMSG
dialogCancel=Kas soovite kindlasti olemite importimise tühistada?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=Ühendus {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=Silt
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW Bridge

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Loo ärimõistete haldamise objektid
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Äriolemid ja tarbimismudelid
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Ainult äriolemid
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Pole

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Juurdepääs andmetele
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Alliktabelite replikeerimise voog kohalikesse tabelitesse
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Alliktabeli replikatsioonid
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Tiražeerimisvoog
#XFLD: label Ingestion Space
ingestionSpaceLabel=Valmendusruum

#XMSG
modelTransferOk=Mudeli importimine on lubatud.
#XMSG
modelTransferNotOk=Mudeli importimist ei saa kasutada.
#XMSG
hanaSdiOk=Alliktabeli replikatsioonid on lubatud.
#XMSG
hanaSdiNotOk=Alliktabeli replikatsioone ei saa kasutada.
#XMSG
replicationFlowOk=Alliktabelite replikeerimise vood on lubatud.
#XMSG
replicationFlowNotOk=Alliktabelite replikeerimise vooge ei saa kasutada.
#XMSG
onboardingOk=Kaugtabelite ja tiražeerimisvoogude kasutuselevõtt on lubatud.
#XMSG
onboardingNotOk=Kaugtabelite ja tiražeerimisvoogude kasutuselevõtt pole võimalik.
#XMSG
enableReplicationFlow=See ühendus saab toetada andmete tiražeerimist tiražeerimisvoo kaudu, kuid see funktsioon pole praegu lubatud. Ühenduse värskendamiseks pöörduge oma administraatori poole. Seni saate kaugtabelite kaudu importida üksnes välisandmete juurdepääsuga olemeid.
#XMSG
selectedApiWithoutEntities=Valitud API ei esita olemeid.
#XMSG
switchReplicationFlowToRemoteTables=Need objektid on praegu installitud kohalike tabelitena, kuhu andmed tiražeeritakse. Koondamise valimine võib jõudlust halvendada. See muudatus mõjutab kõiki ruume, mis kasutavad neid objekte.\n\nKas soovite andmete juurdepääsuks määrata koondamise kaugtabelite kaudu?
#XMSG
switchRemoteTablesToReplicationFlow=Need objektid on praegu installitud kaugtabelitena, kuhu andmed koondatakse. Tiražeerimise valimine parendab jõudlust, ent samas lülitab see ka reaalajas juurdepääsu välja ning andmete värskus hakkab sõltuma teie tiražeerimise ajakavast. See muudatus mõjutab kõiki ruume, mis kasutavad neid objekte.\n\nKas soovite andmete juurdepääsuks määrata tiražeerimise tiražeerimisvoo ja kohalike tabelite kaudu?

#XFLD
spaceTypeAbapBridge=SAP BW Bridge
#XFLD
spaceTypeDatasphere=SAP Datasphere
