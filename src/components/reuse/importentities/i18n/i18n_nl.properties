#XTIT: Title for import entities
importEntitiesTitle=Entiteiten importeren
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Verbindingstype selecteren
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Doelruimte selecteren
#XFLD: import wizard step 3: select connection
selectConnectionStep=Verbinding selecteren
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Entiteiten selecteren
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Entiteiten controleren
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Verbinding/doelruimte selecteren

#XTIT: title connection types
connectionTypesTitle=Items ({0})
#XTIT: title target spaces
targetSpaceTitle=Ruimten ({0})
#XTIT: title connections
connectionsTitle=Verbindingen ({0})

#XCOL: space business name
spaceBusinessName=Ruimte
#XCOL: spaces business name
spacesBusinessName=Ruimten
#XCOL: business name
businessName=Objectnaam
#XCOL: technical name
technicalName=Technische naam
#XCOL: modeling pattern
modelingPattern=Modelleringspatroon
#XCOL: import status
importStatus=Importstatus
#XCOL: replication flow
replicationFlow=Replicatiestroom
#XCOL: entity
entity=Entiteit
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Geselecteerde entiteiten
#XHED: heading selected entities
selectedApis=Geselecteerde API's
#XHED: heading dependent entities
dependentEntities=Afhankelijke entiteiten
#XHED: heading dependent entities
dependentEntitiesOf=Afhankelijke entiteiten van {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Geselecteerde entiteiten van API {0}
#XHED: heading entities of API
entitiesOfApi=Entiteiten van API {0}
#XHED: heading business builder
businessBuilderObjects=Business Builder ({0})
#XHED: heading data builder
dataBuilderObjects=Data Builder ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Vorige
#XBUT
buttonNextStep=Volgende stap
#XBUT
buttonImport=Import en implementatie starten
#XBUT
buttonSchedule=Import en implementatie plannen
#XBUT
buttonCancel=Annuleren
#XBUT
buttonContinue=Doorgaan
#XMSG
loadConnectionTypesFailed=Laden van verbindingstypen is mislukt.
#XMSG
loadTargetSpacesFailed=Laden van doelruimten is mislukt.
#XMSG
loadConnectionsFailed=Laden van verbindingen is mislukt.
#XMSG
loadBwBridgeConnectionFailed=Laden van SAP BW Bridge-verbinding is mislukt.
#XMSG
getConnectionStatusFailed=Ophalen van status van verbinding {0} is mislukt.
#XMSG
getSharedConnectionDetailsFailed=Kan de details van de verbinding voor systeem-ID {0} niet ophalen.
#XMSG
connectionNotValid=Verbinding {0} is geen geldige verbinding.
#XMSG
connectionBwBridgeNotValid=SAP BW-bridgeverbinding {0} is geen geldige verbinding.
#XMSG
connectionBwBridgeNotAvailable=SAP BW-bridgeverbinding is niet beschikbaar.
#XMSG
loadEntitiesFailed=Laden entiteiten mislukt.
#XMSG
importEntitiesFailed=Importeren entiteiten mislukt.
#XMSG
importEntitiesStarted=Importeren van entiteiten is mislukt. Controleer de meldingen voor de status van de import.
#XMSG
loadReviewEntitiesListFailed=Laden van te importeren objecten is mislukt.
#XMSG
selectOneResultListItem=Selecteer slechts één item in de lijst met zoekresultaten.
#XMSG
dialogCancel=Wilt u de import van entiteiten echt annuleren?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=Verbinding {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=Label
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW-bridge

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Business Builder-objecten creëren
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Businessentiteiten en gebruiksmodellen
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Alleen businessentiteiten
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Geen

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Toegang tot gegevens
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Replicatiestroom naar lokale tabellen
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Remote tabellen
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Replicatiestroom
#XFLD: label Ingestion Space
ingestionSpaceLabel=Genereringsruimte

#XMSG
modelTransferOk=Modelimport is ingeschakeld.
#XMSG
modelTransferNotOk=Modelimport kan niet worden gebruikt.
#XMSG
hanaSdiOk=Remote tabellen zijn ingeschakeld.
#XMSG
hanaSdiNotOk=Remote tabellen kunnen niet worden gebruikt.
#XMSG
replicationFlowOk=Replicatiestromen zijn ingeschakeld.
#XMSG
replicationFlowNotOk=Replicatiestromen kunnen niet worden gebruikt.
#XMSG
onboardingOk=Onboarding van remote tabellen en replicatiestromen is geactiveerd.
#XMSG
onboardingNotOk=Onboarding van remote tabellen en replicatiestromen is niet mogelijk.
#XMSG
enableReplicationFlow=Deze verbinding kan de replicatie van gegevens via een replicatiestroom ondersteunen, maar deze functie is momenteel niet ingeschakeld. Neem contact op met uw beheerder om de verbinding te actualiseren. In de tussentijd kunt u alleen entiteiten met aaneengesloten gegevenstoegang importeren via remote tabellen.
#XMSG
selectedApiWithoutEntities=Geselecteerde API geeft geen entiteiten op.
#XMSG
switchReplicationFlowToRemoteTables=Deze objecten zijn nu geïnstalleerd als lokale tabellen met replicatiegegevens. Wijzigen naar federatie kan de prestatie verlagen. Deze wijziging zal invloed hebben op alle ruimten die deze objecten gebruiken.\n\nWilt u de gegevenstoegang naar federatie wijzigen via externe tabellen?
#XMSG
switchRemoteTablesToReplicationFlow=Deze objecten zijn nu geïnstalleerd als externe tabellen met federatiegegevens. Wijzigen naar replicatie kan de prestatie verbeteren maar schakelt ook live toegang uit, zodat de mate waarin uw gegevens actueel zijn, afhankelijk is van uw replicatieplanning. Deze wijziging zal invloed hebben op alle ruimten die deze objecten gebruiken.\n\nWilt u de gegevenstoegang wijzigen naar replicatie via een replicatiestroom en lokale tabellen?

#XFLD
spaceTypeAbapBridge=SAP BW Bridge
#XFLD
spaceTypeDatasphere=SAP Datasphere
