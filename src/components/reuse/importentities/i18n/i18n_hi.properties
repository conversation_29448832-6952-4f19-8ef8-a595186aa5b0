#XTIT: Title for import entities
importEntitiesTitle=निकायों को आयात करें
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=कनेक्शन प्रकार का चयन करें
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=लक्ष्य स्थान का चयन करें
#XFLD: import wizard step 3: select connection
selectConnectionStep=कनेक्शन का चयन करें
#XFLD: import wizard step 4: select entities
selectEntitiesStep=निकाय का चयन करें
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=निकायों की समीक्षा करें
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=कनेक्शन / लक्ष्य स्थान चुनें

#XTIT: title connection types
connectionTypesTitle=आइटम ({0})
#XTIT: title target spaces
targetSpaceTitle=स्थान ({0})
#XTIT: title connections
connectionsTitle=कनेक्शन ({0})

#XCOL: space business name
spaceBusinessName=स्पेस
#XCOL: spaces business name
spacesBusinessName=स्पेस
#XCOL: business name
businessName=व्यवसाय नाम
#XCOL: technical name
technicalName=तकनीकी नाम
#XCOL: modeling pattern
modelingPattern=मॉडलिंग पैटर्न
#XCOL: import status
importStatus=आयात स्थिति
#XCOL: replication flow
replicationFlow=प्रतिकृति प्रवाह
#XCOL: entity
entity=निकाय
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=निकायों को चयनित किया गया
#XHED: heading selected entities
selectedApis=API चयनित किया गया 
#XHED: heading dependent entities
dependentEntities=आश्रित निकाय
#XHED: heading dependent entities
dependentEntitiesOf={0} के निर्भर निकाय
#XHED: heading dependent entities
selectedEntitiesOfApi=API {0} का निकाय चयनित किया गया 
#XHED: heading entities of API
entitiesOfApi=API {0} का निकाय 
#XHED: heading business builder
businessBuilderObjects=व्यवसाय बिल्डर ({0})
#XHED: heading data builder
dataBuilderObjects=डेटा बिल्डर ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=पिछला
#XBUT
buttonNextStep=अगला चरण
#XBUT
buttonImport=आयात और परिनियोजन आरंभ करें
#XBUT
buttonSchedule=आयात और परिनियोजन आरंभ करें
#XBUT
buttonCancel=रद्द करें
#XBUT
buttonContinue=जारी रखें
#XMSG
loadConnectionTypesFailed=कनेक्शन प्रकार को लोड करने में असमर्थ.
#XMSG
loadTargetSpacesFailed=लक्ष्य स्थान को लोड करने में असमर्थ.
#XMSG
loadConnectionsFailed=कनेक्शन लोड करने में असमर्थ.
#XMSG
loadBwBridgeConnectionFailed=SAP BW ब्रिज कनेक्शन लोड करने में असमर्थ.
#XMSG
getConnectionStatusFailed=कनेक्शन {0} की स्थिति पुनः प्राप्त करने में असमर्थ.
#XMSG
getSharedConnectionDetailsFailed=सिस्टम ID {0} के लिए कनेक्शन का विवरण प्राप्त करने में असमर्थ.
#XMSG
connectionNotValid=कनेक्शन {0} कोई मान्य कनेक्शन नहीं है.
#XMSG
connectionBwBridgeNotValid=SAP BW ब्रिज कनेक्शन {0} कोई मान्य कनेक्शन नहीं है.
#XMSG
connectionBwBridgeNotAvailable=SAP BW ब्रिज कनेक्शन उपलब्ध नहीं है.
#XMSG
loadEntitiesFailed=निकाय लोड करने में असमर्थ.
#XMSG
importEntitiesFailed=निकाय आयात करने में असमर्थ.
#XMSG
importEntitiesStarted=आयात करने वाली निकाय. आयात की स्थिति के लिए सूचनाओं की जांच करें.
#XMSG
loadReviewEntitiesListFailed=आयात करने के लिए ऑब्जेक्ट लोड करने में असमर्थ.
#XMSG
selectOneResultListItem=खोज परिणाम सूची में केवल एक आइटम का चयन करें.
#XMSG
dialogCancel=क्या आप वाकई निकायों के आयात को रद्द करना चाहते हैं?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=कनेक्शन {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=लेबल
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW ब्रिज

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=व्यवसाय बिल्डर ऑब्जेक्ट बनाएं
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=व्यवसाय निकाय और उपभोग मॉडल
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=केवल व्यवसाय निकाय
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=कोई नहीं

#XFLD: label Remote Access Mode
remoteAccessModeLabel=डेटा पहुंच
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=स्थानीय तालिका में प्रतिकृति प्रवाह
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=दूरस्थ तालिकाएं
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=प्रतिकृति प्रवाह
#XFLD: label Ingestion Space
ingestionSpaceLabel=अंतर्ग्रहण स्थान

#XMSG
modelTransferOk=मॉडल आयात सक्षम है.
#XMSG
modelTransferNotOk=मॉडल आयात का उपयोग नहीं किया जा सकता.
#XMSG
hanaSdiOk=दूरस्थ तालिकाएं सक्षम हैं.
#XMSG
hanaSdiNotOk=दूरस्थ तालिकाएं का उपयोग नहीं किया जा सकता.
#XMSG
replicationFlowOk=प्रतिकृति प्रवाह बनाना सक्षम है.
#XMSG
replicationFlowNotOk=प्रतिकृति प्रवाह का उपयोग नहीं किया जा सकता.
#XMSG
onboardingOk=दूरस्थ तालिकाओं और प्रतिकृति प्रवाहों की ऑनबोर्डिंग सक्षम है.
#XMSG
onboardingNotOk=दूरस्थ तालिकाओं और प्रतिकृति प्रवाहों की ऑनबोर्डिंग संभव नहीं है.
#XMSG
enableReplicationFlow=यह कनेक्शन प्रतिकृति प्रवाह के माध्यम से डेटा की प्रतिकृति का समर्थन कर सकता है, लेकिन यह सुविधा वर्तमान में सक्षम नहीं है. कृपया कनेक्शन अपडेट करने के लिए अपने व्यवस्थापक से संपर्क करें. इस बीच आप केवल दूरस्थ तालिकाओं के माध्यम से फ़ेडरेटेड डेटा पहुंच के साथ निकाय आयात कर सकते हैं.
#XMSG
selectedApiWithoutEntities=चयनित API कोई प्रविष्टियां प्रदान नहीं करता है.
#XMSG
switchReplicationFlowToRemoteTables=ये ऑब्जेक्ट वर्तमान में स्थानीय तालिका के रूप में इंस्टॉल हैं, जिनमें डेटा प्रतिकृति है. फ़ेडरेशन में परिवर्तन करने से प्रदर्शन में गिरावट आ सकती है. यह परिवर्तन उन सभी स्पेस को प्रभावित करेगा जो इन ऑब्जेक्ट का उपयोग करते हैं.\n\nक्या आप दूरस्थ तालिका के माध्यम से फ़ेडरेशन तक डेटा पहुंच को बदलना चाहते हैं?
#XMSG
switchRemoteTablesToReplicationFlow=ये ऑब्जेक्ट वर्तमान में दूरस्थ तालिका के रूप में स्थापित हैं, जिनमें डेटा फ़ेडरेटेड है. प्रतिकृति में परिवर्तन करने से प्रदर्शन में सुधार होगा, लेकिन लाइव पहुंच भी अक्षम हो जाएगा, ताकि आपके डेटा की ताज़गी आपके प्रतिकृति शेड्यूल पर निर्भर हो. यह परिवर्तन उन सभी स्थानों को प्रभावित करेगा जो इन ऑब्जेक्ट का उपभोग करते हैं.\n\nक्या आप प्रतिकृति प्रवाह और स्थानीय तालिकाओं के माध्यम से प्रतिकृति तक डेटा पहुंच को बदलना चाहते हैं?

#XFLD
spaceTypeAbapBridge=SAP BW ब्रिज
#XFLD
spaceTypeDatasphere=SAP Datasphere
