#XTIT: Title for import entities
importEntitiesTitle=Birimleri içe aktar
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Bağlantı türü seçin
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Hede<PERSON> alan se<PERSON>
#XFLD: import wizard step 3: select connection
selectConnectionStep=Bağlantı seçin
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Birim seçin
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Birimleri gözden geçirin
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Bağlantı/hedef alan seçin

#XTIT: title connection types
connectionTypesTitle=Öğeler ({0})
#XTIT: title target spaces
targetSpaceTitle=Alanlar ({0})
#XTIT: title connections
connectionsTitle=Bağlantılar ({0})

#XCOL: space business name
spaceBusinessName=Alan
#XCOL: spaces business name
spacesBusinessName=Alanlar
#XCOL: business name
businessName=İş adı
#XCOL: technical name
technicalName=Teknik ad
#XCOL: modeling pattern
modelingPattern=Modelleme örneği
#XCOL: import status
importStatus=İçe aktarım durumu
#XCOL: replication flow
replicationFlow=Çoğaltma akışı
#XCOL: entity
entity=Birim
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Seçilen birimler
#XHED: heading selected entities
selectedApis=Seçilen API'ler
#XHED: heading dependent entities
dependentEntities=Bağlı birimler
#XHED: heading dependent entities
dependentEntitiesOf=Bağlı {0} birimleri
#XHED: heading dependent entities
selectedEntitiesOfApi=Seçilen {0} API''si birimleri
#XHED: heading entities of API
entitiesOfApi={0} API''si birimleri
#XHED: heading business builder
businessBuilderObjects=İş oluşturucu ({0})
#XHED: heading data builder
dataBuilderObjects=Veri oluşturucu ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Önceki
#XBUT
buttonNextStep=Sonraki adım
#XBUT
buttonImport=İçe aktarımı ve dağıtımı başlat
#XBUT
buttonSchedule=İçe aktarımı ve dağıtımı planla
#XBUT
buttonCancel=İptal
#XBUT
buttonContinue=Devam et
#XMSG
loadConnectionTypesFailed=Bağlantı türleri yüklenemiyor.
#XMSG
loadTargetSpacesFailed=Hedef alanlar yüklenemiyor.
#XMSG
loadConnectionsFailed=Bağlantılar yüklenemiyor.
#XMSG
loadBwBridgeConnectionFailed=SAP BW köprü bağlantısı yüklenmiyor.
#XMSG
getConnectionStatusFailed={0} bağlantısının durumu alınamıyor.
#XMSG
getSharedConnectionDetailsFailed=Sistem tanıtıcısı {0} için bağlantının ayrıntıları alınamıyor.
#XMSG
connectionNotValid=Bağlantı {0} geçerli bağlantı değil.
#XMSG
connectionBwBridgeNotValid=SAP BW köprü bağlantısı {0} geçerli bağlantı değil.
#XMSG
connectionBwBridgeNotAvailable=SAP BW köprü bağlantısı geçerli değil.
#XMSG
loadEntitiesFailed=Birimler yüklenemiyor.
#XMSG
importEntitiesFailed=Birimler içe aktarılamıyor.
#XMSG
importEntitiesStarted=Birimler içe aktarılıyor. İçe aktarma durumu için bildirimleri kontrol edin.
#XMSG
loadReviewEntitiesListFailed=İçe aktarılacak nesneler yüklenemiyor.
#XMSG
selectOneResultListItem=Arama sonucu listesinde yalnız bir kalem seçin.
#XMSG
dialogCancel=Birimleri içe aktarmayı iptal etmek istiyor musunuz?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=Tanıtıcı
identifier_plural=Bağlantı {0}
identifier_identifier=Tanıtıcı
identifier_entityname=Tanıtıcı
identifier_entitylabel=Etiket
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW köprüsü

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=İş oluşturucu nesneleri oluştur
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=İş birimleri ve tüketim modelleri
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Yalnız iş birimleri
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Hiçbiri

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Veri erişimi
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Yerel tablolara çoğaltma akışı
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Uzak tablolar
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Çoğaltma akışı
#XFLD: label Ingestion Space
ingestionSpaceLabel=Alım alanı

#XMSG
modelTransferOk=Model içe aktarımı etkinleştirildi.
#XMSG
modelTransferNotOk=Model içe aktarımı kullanılamıyor.
#XMSG
hanaSdiOk=Uzak tablolar etkinleştirildi.
#XMSG
hanaSdiNotOk=Uzak tablolar kullanılamıyor.
#XMSG
replicationFlowOk=Çoğaltma akışları etkinleştirildi.
#XMSG
replicationFlowNotOk=Çoğaltma akışları kullanılamıyor.
#XMSG
onboardingOk=Uzak tabloları ve çoğaltma akışlarını etkinleştirme olanağı sağlandı.
#XMSG
onboardingNotOk=Uzak tabloları ve çoğaltma akışlarını etkinleştirmek olanaklı değil.
#XMSG
enableReplicationFlow=Bu bağlantı, bir çoğaltma akışı yoluyla verilerin çoğaltılmasını destekleyebilir ancak bu özellik şu anda etkin değil. Bağlantıyı güncellemek için yöneticinizle bağlantı kurun. Bu sırada yalnızca uzak tablolar aracılığıyla birlik veri erişimi içeren birimleri içe aktarabilirsiniz.
#XMSG
selectedApiWithoutEntities=Seçilen API birim sağlamıyor.
#XMSG
switchReplicationFlowToRemoteTables=Bu nesneler, şu anda verilerin çoğaltıldığı yerel tablolar olarak kuruldu. Birleştirme olarak değiştirilmesi performansı düşürebilir. Bu değişiklik, bu nesneleri kullanan tüm alanları etkileyecektir.\n\nVeri erişimini uzak tablolar aracılığıyla birleştirmeye değiştirmek istiyor musunuz?
#XMSG
switchRemoteTablesToReplicationFlow=Bu nesneler, şu anda verilerin birleştirildiği uzak tablolar olarak kuruldu. Çoğaltma olarak değiştirilmesi performansı iyileştirecektir ancak canlı erişimi de devre dışı bırakacaktır. Bu nedenle verilerinizin güncelliği, çoğaltma planınıza bağlıdır. Bu değişiklik, bu nesneleri kullanan tüm alanları etkileyecektir.\n\nVeri erişimini çoğaltma akışı ve yerel tablolar aracılığıyla çoğaltma olarak değiştirmek istiyor musunuz?

#XFLD
spaceTypeAbapBridge=SAP BW köprüsü
#XFLD
spaceTypeDatasphere=SAP Datasphere
