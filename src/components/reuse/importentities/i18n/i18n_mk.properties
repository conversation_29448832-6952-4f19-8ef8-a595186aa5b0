#XTIT: Title for import entities
importEntitiesTitle=Увези ентитети
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Избери тип врска
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Избери целен простор
#XFLD: import wizard step 3: select connection
selectConnectionStep=Избери врска
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Избери ентитети
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Прегледај ентитети
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Избери врска/целен простор

#XTIT: title connection types
connectionTypesTitle=Ставки ({0})
#XTIT: title target spaces
targetSpaceTitle=Простори ({0})
#XTIT: title connections
connectionsTitle=Врски ({0})

#XCOL: space business name
spaceBusinessName=Простор
#XCOL: spaces business name
spacesBusinessName=Простори
#XCOL: business name
businessName=Деловен назив
#XCOL: technical name
technicalName=Технички назив
#XCOL: modeling pattern
modelingPattern=Шема за моделирање
#XCOL: import status
importStatus=Статус на увозот
#XCOL: replication flow
replicationFlow=Репликациски тек
#XCOL: entity
entity=Ентитет
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Избрани ентитети
#XHED: heading selected entities
selectedApis=Избрани API-интерфејси
#XHED: heading dependent entities
dependentEntities=Зависни ентитети
#XHED: heading dependent entities
dependentEntitiesOf=Зависни ентитети за {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Избрани ентитети за API {0}
#XHED: heading entities of API
entitiesOfApi=Ентитети за API {0}
#XHED: heading business builder
businessBuilderObjects=Деловна алатка за градење ({0})
#XHED: heading data builder
dataBuilderObjects=Алатка за градење податоци ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Претходно
#XBUT
buttonNextStep=Следен чекор
#XBUT
buttonImport=Започни увоз и примени
#XBUT
buttonSchedule=Закажи увоз и примени
#XBUT
buttonCancel=Откажи
#XBUT
buttonContinue=Продолжи
#XMSG
loadConnectionTypesFailed=Не може да се вчитаат типовите врски.
#XMSG
loadTargetSpacesFailed=Не може да се вчитаат целните простори.
#XMSG
loadConnectionsFailed=Не може да се вчитаат врските.
#XMSG
loadBwBridgeConnectionFailed=Не може да се вчита врската на премостувањето на апликацијата SAP BW.
#XMSG
getConnectionStatusFailed=Не може да се врати статусот на врската {0}.
#XMSG
getSharedConnectionDetailsFailed=Не може да се вчитаат деталите за врската за ИД-бројот на системот {0}.
#XMSG
connectionNotValid=Врската {0} не е важечка врска.
#XMSG
connectionBwBridgeNotValid=Врската на премостувањето на апликацијата SAP BW {0} не е важечка врска.
#XMSG
connectionBwBridgeNotAvailable=Врската на премостувањето на апликацијата SAP BW не е достапна.
#XMSG
loadEntitiesFailed=Не може да се вчитаат ентитети.
#XMSG
importEntitiesFailed=Не може да се увезат ентитети.
#XMSG
importEntitiesStarted=Увезување субјекти. Проверете ги известувањата за статусот на увозот.
#XMSG
loadReviewEntitiesListFailed=Не може да се вчитаат објекти за увоз.
#XMSG
selectOneResultListItem=Изберете само една ставка во списокот со резултати од пребарувањето.
#XMSG
dialogCancel=Дали сигурно сакате да го откажете увозот на ентитети?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ИД
identifier_plural=Врска {0} 
identifier_identifier=ИД
identifier_entityname=ИД
identifier_entitylabel=Етикета
#XTXT: SAP BW Bridge Connection type
sapBwBridge=Премостување на апликацијата SAP BW

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Создај објекти на деловната алатка за градење
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Деловни ентитети и модели на потрошувачка
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Само деловни ентитети
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Ништо

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Пристап до податоци
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Репликациски тек за локални табели
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Табели на далечина
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Репликациски тек
#XFLD: label Ingestion Space
ingestionSpaceLabel=Место за увезување

#XMSG
modelTransferOk=Увозот на модел е овозможен.
#XMSG
modelTransferNotOk=Увозот на модел не може да се користи.
#XMSG
hanaSdiOk=Табелите на далечина се овозможени.
#XMSG
hanaSdiNotOk=Табелите на далечина не може да се користат.
#XMSG
replicationFlowOk=Репликациските текови се овозможени.
#XMSG
replicationFlowNotOk=Репликациските текови не може да се користат...
#XMSG
onboardingOk=Овозможено е воведување на табелите на далечина и на репликациските текови.
#XMSG
onboardingNotOk=Воведувањето на табелите на далечина и на репликациските текови не е возможно.
#XMSG
enableReplicationFlow=Оваа врска може да поддржува репликација на податоци преку репликациски тек, но карактеристиката моментално не е овозможена. Контактирајте со администраторот за да ја ажурира врската. Во меѓувреме, можете да увезувате само ентитети со заеднички пристап до податоци преку табели на далечина.
#XMSG
selectedApiWithoutEntities=Избраниот API не дава ентитети.
#XMSG
switchReplicationFlowToRemoteTables=Овие објекти моментално се инсталираат како локални табели во коишто се реплицираат податоци. Ако промените во поврзување, може да се намали учинокот. Оваа промена ќе влијае врз сите простори во кои се наоѓаат овие објекти.\n\nДали сакате да го промените пристапот до податоци во поврзување преку табели на далечина?
#XMSG
switchRemoteTablesToReplicationFlow=Овие објекти моментално се инсталираат како табели на далечина во коишто се поврзуваат податоци. Ако измените во репликација, може да се подобри учинокот, но и ќе се оневозможи пристапот во живо, така што ажурираноста на податоците ќе зависи од распоредот на репликацијата. Оваа промена ќе влијае врз сите простори во кои се наоѓаат овие објекти.\n\nДали сакате да го промените пристапот до податоци во репликација преку тек на репликација и локални табели?

#XFLD
spaceTypeAbapBridge=Премостување на апликацијата SAP BW
#XFLD
spaceTypeDatasphere=SAP Datasphere
