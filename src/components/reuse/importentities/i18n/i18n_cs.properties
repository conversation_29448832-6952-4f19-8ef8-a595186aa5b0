#XTIT: Title for import entities
importEntitiesTitle=Import entit
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Vybrat typ spojení
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Vybrat cílov<PERSON> prostor
#XFLD: import wizard step 3: select connection
selectConnectionStep=Vybrat připojení
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Vybrat entity
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Zkontrolovat entity
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Vybrat připojení / cílový prostor

#XTIT: title connection types
connectionTypesTitle=<PERSON><PERSON>ky ({0})
#XTIT: title target spaces
targetSpaceTitle=Prostory ({0})
#XTIT: title connections
connectionsTitle=Připojení ({0})

#XCOL: space business name
spaceBusinessName=Prostor
#XCOL: spaces business name
spacesBusinessName=Prostory
#XCOL: business name
businessName=Business název
#XCOL: technical name
technicalName=Technický název
#XCOL: modeling pattern
modelingPattern=Modelování - vzor
#XCOL: import status
importStatus=Status importu
#XCOL: replication flow
replicationFlow=Replikační tok
#XCOL: entity
entity=Entita
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Vybrané entity
#XHED: heading selected entities
selectedApis=Vybraná API
#XHED: heading dependent entities
dependentEntities=Závislé entity
#XHED: heading dependent entities
dependentEntitiesOf=Závislé entity {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Vybrané entity API {0}
#XHED: heading entities of API
entitiesOfApi=Entity API {0}
#XHED: heading business builder
businessBuilderObjects=Business editor ({0})
#XHED: heading data builder
dataBuilderObjects=Editor dat ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Předchozí
#XBUT
buttonNextStep=Další krok
#XBUT
buttonImport=Zahájit import a nasazení
#XBUT
buttonSchedule=Naplánovat import a nasazení
#XBUT
buttonCancel=Zrušit
#XBUT
buttonContinue=Dále
#XMSG
loadConnectionTypesFailed=Nelze načíst typy připojení.
#XMSG
loadTargetSpacesFailed=Nelze načíst cílové prostory.
#XMSG
loadConnectionsFailed=Nelze načíst připojení.
#XMSG
loadBwBridgeConnectionFailed=Nelze načíst připojení SAP BW Bridge.
#XMSG
getConnectionStatusFailed=Nelze načíst status připojení {0}.
#XMSG
getSharedConnectionDetailsFailed=Nelze načíst detaily připojení pro ID systému {0}.
#XMSG
connectionNotValid=Připojení {0} není platným připojením.
#XMSG
connectionBwBridgeNotValid=Připojení SAP BW Bridge {0} není platným připojením.
#XMSG
connectionBwBridgeNotAvailable=Připojení SAP BW Bridge není dostupné.
#XMSG
loadEntitiesFailed=Nelze načíst entity.
#XMSG
importEntitiesFailed=Nelze importovat entity.
#XMSG
importEntitiesStarted=Entity se importují. Zkontrolujte hlášení pro status importu.
#XMSG
loadReviewEntitiesListFailed=Nelze načíst objekty k importu.
#XMSG
selectOneResultListItem=V seznamu výsledků hledání vyberte jen jednu položku.
#XMSG
dialogCancel=Opravdu chcete zrušit import entit?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=Připojení {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=Popisek
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW Bridge

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Vytvořit objekty business editoru
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Business entity a modely spotřeby
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Pouze business entity
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Nic

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Přístup k datům
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Replikační tok k lokálním tabulkám
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Vzdálené tabulky
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Replikační tok
#XFLD: label Ingestion Space
ingestionSpaceLabel=Prostor přijetí

#XMSG
modelTransferOk=Import modelu je aktivován.
#XMSG
modelTransferNotOk=Import modelu nelze použít.
#XMSG
hanaSdiOk=Vzdálené tabulky jsou aktivovány.
#XMSG
hanaSdiNotOk=Vzdálené tabulky nelze použít.
#XMSG
replicationFlowOk=Replikační toky jsou aktivovány.
#XMSG
replicationFlowNotOk=Replikační toky nelze zastavit
#XMSG
onboardingOk=Zavádění vzdálených tabulek a replikačních toků je aktivováno.
#XMSG
onboardingNotOk=Zavádění vzdálených tabulek a replikačních toků není možné.
#XMSG
enableReplicationFlow=Toto připojení může podporovat replikaci dat prostřednictvím replikačního toku, ale tato funkce není aktuálně povolena. Chcete-li aktualizovat připojení, kontaktujte svého správce. Zatím můžete importovat pouze entity s federovaným přístupem k datům prostřednictvím vzdálených tabulek.
#XMSG
selectedApiWithoutEntities=Vybrané rozhraní API neposkytuje žádné entity.
#XMSG
switchReplicationFlowToRemoteTables=Tyto objekty jsou právě instalovány jako lokální tabulky s replikovanými daty. Změnou na federování se může snížit výkon.Tato změna ovlivní všechny prostory, které používají tyto objekty.\n\nChcete změnit přístup k datům na federování pomocí vzdálených tabulek?
#XMSG
switchRemoteTablesToReplicationFlow=Tyto objekty jsou právě instalovány jako vzdálené tabulky s federovanými daty. Změnou na replikaci se zlepší výkon, ale také se deaktivuje živý přístup, takže čerstvost vašich dat bude záviset na vašem plánu replikace.Tato změna ovlivní všechny prostory, které používají tyto objekty.\n\nChcete změnit přístup k datům na replikaci pomocí replikačního toku a lokálních tabulek?

#XFLD
spaceTypeAbapBridge=SAP BW Bridge
#XFLD
spaceTypeDatasphere=SAP Datasphere
