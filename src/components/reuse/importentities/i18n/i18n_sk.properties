#XTIT: Title for import entities
importEntitiesTitle=Importovať entity
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Vybrať typ pripojenia
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Vyberať cieľový priestor
#XFLD: import wizard step 3: select connection
selectConnectionStep=Vybrať pripojenie
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Vybrať entity
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Skontrolovať entity
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Vybrať pripojenie / cieľový priestor

#XTIT: title connection types
connectionTypesTitle=<PERSON><PERSON>ky ({0})
#XTIT: title target spaces
targetSpaceTitle=Priestory ({0})
#XTIT: title connections
connectionsTitle=Pripojenia ({0})

#XCOL: space business name
spaceBusinessName=Priestor
#XCOL: spaces business name
spacesBusinessName=Priestory
#XCOL: business name
businessName=Podnikový názov
#XCOL: technical name
technicalName=Technický názov
#XCOL: modeling pattern
modelingPattern=Vzor modelovania
#XCOL: import status
importStatus=Status importu
#XCOL: replication flow
replicationFlow=Replikačný tok
#XCOL: entity
entity=Entita
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Vybraté entity
#XHED: heading selected entities
selectedApis=Vybrané API
#XHED: heading dependent entities
dependentEntities=Závislé entity
#XHED: heading dependent entities
dependentEntitiesOf=Závislé entity {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Vybrané entity API {0}
#XHED: heading entities of API
entitiesOfApi=Entity API {0}
#XHED: heading business builder
businessBuilderObjects=Podnikový zostavovač ({0})
#XHED: heading data builder
dataBuilderObjects=Zostavovač údajov ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Späť
#XBUT
buttonNextStep=Ďalší krok
#XBUT
buttonImport=Spustiť import a nasadenie
#XBUT
buttonSchedule=Naplánovať import a nasadenie
#XBUT
buttonCancel=Zrušiť
#XBUT
buttonContinue=Pokračovať
#XMSG
loadConnectionTypesFailed=Nie je možné načítať typy pripojenia.
#XMSG
loadTargetSpacesFailed=Nie je možné načítanie cieľových priestorov.
#XMSG
loadConnectionsFailed=Nie je možné načítať pripojenia.
#XMSG
loadBwBridgeConnectionFailed=Nedá sa načítať pripojenie SAP BW bridge.
#XMSG
getConnectionStatusFailed=Nie je možné načítať status pripojenia {0}.
#XMSG
getSharedConnectionDetailsFailed=Nie je možné získať podrobnosti o pripojení pre ID systému {0}.
#XMSG
connectionNotValid=Pripojenie {0} nie je platné pripojenie.
#XMSG
connectionBwBridgeNotValid=Pripojenie SAP BW bridge {0} nie je platné pripojenie.
#XMSG
connectionBwBridgeNotAvailable=Pripojenie SAP BW bridge nie je dostupné.
#XMSG
loadEntitiesFailed=Nie je možné načítať entity.
#XMSG
importEntitiesFailed=Nie je možné importovať entity.
#XMSG
importEntitiesStarted=Import entít. Pozrite si status importu v oznámeniach.
#XMSG
loadReviewEntitiesListFailed=Nepodarilo sa načítať objekty na import.
#XMSG
selectOneResultListItem=Vyberte len jednu položku v zozname výsledkov vyhľadávania.
#XMSG
dialogCancel=Naozaj chcete zrušiť import entít?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=Pripojenie {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=Označenie
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW Bridge

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Vytvoriť objekty podnikového zostavovača
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Obchodné entity a modely spotreby
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Len obchodné entity
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Žiadne

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Prístup k údajom
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Replikačný tok do lokálnych tabuliek
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Vzdialené tabuľky
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Replikačný tok
#XFLD: label Ingestion Space
ingestionSpaceLabel=Priestor na príjem

#XMSG
modelTransferOk=Import modelu je aktivovaný.
#XMSG
modelTransferNotOk=Import modelu nie je možné použiť.
#XMSG
hanaSdiOk=Vzdialené tabuľky sú aktivované.
#XMSG
hanaSdiNotOk=Vzdialené tabuľky nie je možné použiť.
#XMSG
replicationFlowOk=Toky replikácie sú aktivované.
#XMSG
replicationFlowNotOk=Replikačné toky nie je možné použiť.
#XMSG
onboardingOk=Onboarding vzdialených tabuliek a tokov replikácie je aktivovaný.
#XMSG
onboardingNotOk=Onboarding vzdialených tabuliek a tokov replikácie nie je možný.
#XMSG
enableReplicationFlow=Toto pripojenie môže podporovať replikáciu údajov prostredníctvom toku replikácie, ale táto funkcia momentálne nie je povolená. Ak chcete aktualizovať pripojenie, kontaktujte svojho správcu. Medzitým môžete importovať iba entity s federatívnym prístupom k údajom prostredníctvom vzdialených tabuliek.
#XMSG
selectedApiWithoutEntities=Vybrané rozhranie API neposkytuje žiadne entity.
#XMSG
switchReplicationFlowToRemoteTables=Tieto objekty sú momentálne nainštalované ako lokálne tabuľky s replikovanými údajmi. Zmena na federáciu môže znížiť výkon. Táto zmena ovplyvní všetky priestory, ktoré tieto objekty spotrebúvajú.\n\nChcete zmeniť prístup k údajom na federáciu prostredníctvom vzdialených tabuliek?
#XMSG
switchRemoteTablesToReplicationFlow=Tieto objekty sú momentálne nainštalované ako vzdialené tabuľky s federovanými údajmi. Zmena na replikáciu zlepší výkon, ale tiež zakáže prístup v reálnom čase, takže aktuálnosť vašich údajov bude závisieť od vášho harmonogramu replikácie. Táto zmena ovplyvní všetky priestory, ktoré tieto objekty spotrebúvajú.\n\nChcete zmeniť prístup k údajom na replikáciu prostredníctvom replikačného toku a lokálnych tabuliek?

#XFLD
spaceTypeAbapBridge=SAP BW Bridge
#XFLD
spaceTypeDatasphere=SAP Datasphere
