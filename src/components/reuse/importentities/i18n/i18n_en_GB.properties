#XTIT: Title for import entities
importEntitiesTitle=Import Entities
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Select Connection Type
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Select Target Space
#XFLD: import wizard step 3: select connection
selectConnectionStep=Select Connection
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Select Entities
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Review Entities
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Select Connection/Target Space

#XTIT: title connection types
connectionTypesTitle=Items ({0})
#XTIT: title target spaces
targetSpaceTitle=Spaces ({0})
#XTIT: title connections
connectionsTitle=Connections ({0})

#XCOL: space business name
spaceBusinessName=Space
#XCOL: spaces business name
spacesBusinessName=Spaces
#XCOL: business name
businessName=Business Name
#XCOL: technical name
technicalName=Technical Name
#XCOL: modeling pattern
modelingPattern=Modelling Pattern
#XCOL: import status
importStatus=Import Status
#XCOL: replication flow
replicationFlow=Replication Flow
#XCOL: entity
entity=Entity
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Selected Entities
#XHED: heading selected entities
selectedApis=Selected APIs
#XHED: heading dependent entities
dependentEntities=Dependent Entities
#XHED: heading dependent entities
dependentEntitiesOf=Dependent Entities of {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Selected Entities of API {0}
#XHED: heading entities of API
entitiesOfApi=Entities of API {0}
#XHED: heading business builder
businessBuilderObjects=Business Builder ({0})
#XHED: heading data builder
dataBuilderObjects=Data Builder ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Previous
#XBUT
buttonNextStep=Next Step
#XBUT
buttonImport=Start Import and Deploy
#XBUT
buttonSchedule=Schedule Import and Deploy
#XBUT
buttonCancel=Cancel
#XBUT
buttonContinue=Continue
#XMSG
loadConnectionTypesFailed=Unable to load connection types.
#XMSG
loadTargetSpacesFailed=Unable to load target spaces.
#XMSG
loadConnectionsFailed=Unable to load connections.
#XMSG
loadBwBridgeConnectionFailed=Unable to load SAP BW bridge connection.
#XMSG
getConnectionStatusFailed=Unable to retrieve the status of connection {0}.
#XMSG
getSharedConnectionDetailsFailed=Unable to retrieve the details of the connection for system ID {0}.
#XMSG
connectionNotValid=Connection {0} is not a valid connection.
#XMSG
connectionBwBridgeNotValid=SAP BW bridge connection {0} is not a valid connection.
#XMSG
connectionBwBridgeNotAvailable=SAP BW bridge connection is not available.
#XMSG
loadEntitiesFailed=Unable to load entities.
#XMSG
importEntitiesFailed=Unable to import entities.
#XMSG
importEntitiesStarted=Importing entities. Check the notifications for the status of the import.
#XMSG
loadReviewEntitiesListFailed=Unable to load objects to import.
#XMSG
selectOneResultListItem=Select only one item in the search results list.
#XMSG
dialogCancel=Do you really want to cancel the import of entities?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=Connection {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=Label
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW Bridge

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Create Business Builder Objects
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Business Entities and Consumption Models
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Business Entities Only
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=None

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Data Access
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Replication Flow to Local Tables
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Remote Tables
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Replication Flow
#XFLD: label Ingestion Space
ingestionSpaceLabel=Ingestion Space

#XMSG
modelTransferOk=Model import is enabled.
#XMSG
modelTransferNotOk=Model import cannot be used.
#XMSG
hanaSdiOk=Remote tables are enabled.
#XMSG
hanaSdiNotOk=Remote tables cannot be used.
#XMSG
replicationFlowOk=Replication flows are enabled.
#XMSG
replicationFlowNotOk=Replication flows cannot be used.
#XMSG
onboardingOk=Onboarding of remote tables and replication flows is enabled.
#XMSG
onboardingNotOk=Onboarding of remote tables and replication flows is not possible.
#XMSG
enableReplicationFlow=This connection can support the replication of data via a replication flow, but this feature is not currently enabled. Please contact your administrator to update the connection. In the meantime you can only import entities with federated data access via remote tables.
#XMSG
selectedApiWithoutEntities=The selected API does not provide any entities.
#XMSG
switchReplicationFlowToRemoteTables=These objects are currently installed as local tables with data replicated to them. Changing to federation may degrade performance. This change will impact all spaces that consume these objects.\n\nDo you want to change the data access to federation via remote tables?
#XMSG
switchRemoteTablesToReplicationFlow=These objects are currently installed as remote tables with data federated to them. Changing to replication will improve performance but will also disable live access so that the freshness of your data depends on your replication schedule. This change will impact all spaces that consume these objects.\n\nDo you want to change the data access to replication via a replication flow and local tables?

#XFLD
spaceTypeAbapBridge=SAP BW Bridge
#XFLD
spaceTypeDatasphere=SAP Datasphere
