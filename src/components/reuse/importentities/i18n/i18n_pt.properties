#XTIT: Title for import entities
importEntitiesTitle=Importar entidades
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Selecionar tipo de conexão
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Selecionar área de destino
#XFLD: import wizard step 3: select connection
selectConnectionStep=Selecionar conexão
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Selecionar entidades
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Revisar entidades
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Selecionar conexão/área de destino 

#XTIT: title connection types
connectionTypesTitle=Itens ({0})
#XTIT: title target spaces
targetSpaceTitle=Áreas ({0})
#XTIT: title connections
connectionsTitle=Conexões ({0})

#XCOL: space business name
spaceBusinessName=Área
#XCOL: spaces business name
spacesBusinessName=Áreas
#XCOL: business name
businessName=Nome comercial
#XCOL: technical name
technicalName=Nome técnico
#XCOL: modeling pattern
modelingPattern=Padrão de modelagem
#XCOL: import status
importStatus=Status da importação
#XCOL: replication flow
replicationFlow=Fluxo de replicação
#XCOL: entity
entity=Entidade
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Entidades selecionadas
#XHED: heading selected entities
selectedApis=APIs selecionadas
#XHED: heading dependent entities
dependentEntities=Entidades dependentes
#XHED: heading dependent entities
dependentEntitiesOf=Entidades dependentes de {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Entidades selecionadas da API {0}
#XHED: heading entities of API
entitiesOfApi=Entidades da API {0}
#XHED: heading business builder
businessBuilderObjects=Gerador de negócios ({0})
#XHED: heading data builder
dataBuilderObjects=Gerador de dados ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Voltar
#XBUT
buttonNextStep=Etapa seguinte
#XBUT
buttonImport=Iniciar importação e implementação
#XBUT
buttonSchedule=Programar importação e implementação
#XBUT
buttonCancel=Cancelar
#XBUT
buttonContinue=Continuar
#XMSG
loadConnectionTypesFailed=Não é possível carregar tipos de conexão.
#XMSG
loadTargetSpacesFailed=Não é possível carregar áreas de destino.
#XMSG
loadConnectionsFailed=Não é possível carregar conexões.
#XMSG
loadBwBridgeConnectionFailed=Não é possível carregar conexão de ponte do SAP BW.
#XMSG
getConnectionStatusFailed=Não é possível recuperar o status da conexão {0}.
#XMSG
getSharedConnectionDetailsFailed=Não é possível recuperar os detalhes da conexão para o ID do sistema {0}.
#XMSG
connectionNotValid=A conexão {0} não é válida.
#XMSG
connectionBwBridgeNotValid=A conexão de ponte do SAP BW {0} não é uma conexão válida.
#XMSG
connectionBwBridgeNotAvailable=A conexão de ponte do SAP BW não está disponível.
#XMSG
loadEntitiesFailed=Não é possível carregar entidades.
#XMSG
importEntitiesFailed=Não é possível importar entidades.
#XMSG
importEntitiesStarted=Importando entidades. Verifique as notificações para obter o status da importação.
#XMSG
loadReviewEntitiesListFailed=Não é possível carregar objetos a serem importados.
#XMSG
selectOneResultListItem=Selecione apenas um item na lista de resultados de pesquisa.
#XMSG
dialogCancel=Realmente deseja cancelar a importação de entidades?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=Conexão {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=Etiqueta
#XTXT: SAP BW Bridge Connection type
sapBwBridge=Ponte SAP BW

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Criar objetos do gerador de negócios
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Entidades empresariais e modelos de consumo
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Somente entidades empresariais
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Nenhum

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Acesso aos dados
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Fluxo de replicação em tabelas locais
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Tabelas remotas
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Fluxo de replicação
#XFLD: label Ingestion Space
ingestionSpaceLabel=Área de ingestão

#XMSG
modelTransferOk=Importação de modelos ativada.
#XMSG
modelTransferNotOk=Não é possível usar importação de modelos.
#XMSG
hanaSdiOk=Tabelas remotas ativadas.
#XMSG
hanaSdiNotOk=Não é possível usar tabelas remotas.
#XMSG
replicationFlowOk=Fluxos de replicação ativados.
#XMSG
replicationFlowNotOk=Não é possível usar fluxos de replicação.
#XMSG
onboardingOk=A integração de tabelas remotas e fluxos de replicação está ativada.
#XMSG
onboardingNotOk=A integração de tabelas remotas e fluxos de replicação não é possível.
#XMSG
enableReplicationFlow=Esta conexão pode suportar a replicação de dados via fluxo de replicação, mas esse recurso não está disponível no momento. Entre em contato com o administrador para atualizar a conexão. Nesse meio tempo, você só poderá importar entidades com acesso a dados federados por meio de tabelas remotas.
#XMSG
selectedApiWithoutEntities=A API selecionada não fornece nenhuma entidade.
#XMSG
switchReplicationFlowToRemoteTables=Esses objetos estão atualmente instalados como tabelas locais com dados replicados nelas. A alteração para federação pode reduzir o desempenho. Essa alteração afetará todas as áreas que consumem esses objetos.\n\nDeseja alterar o acesso aos dados para federação via tabelas remotas?
#XMSG
switchRemoteTablesToReplicationFlow=Esses objetos estão atualmente instalados como tabelas remotas com dados federados para eles. Alterar a replicação melhorará o desempenho e também desativará o acesso em tempo real, ou seja, manter seus dados atualizados dependerá da programação de replicação. Essa alteração afetará todas as áreas que consumem esses objetos.\n\nDeseja alterar o acesso aos dados para replicação via fluxo de replicação e tabelas locais?

#XFLD
spaceTypeAbapBridge=Ponte SAP BW
#XFLD
spaceTypeDatasphere=SAP Datasphere
