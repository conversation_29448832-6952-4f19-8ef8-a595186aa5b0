#XTIT: Title for import entities
importEntitiesTitle=Uvoz entitet
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Izbira vrste povezave
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Izbira ciljnega prostora
#XFLD: import wizard step 3: select connection
selectConnectionStep=Izbira povezave
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Izbira entitet
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Pregled entitet
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Izbira povezave/ciljnega prostora

#XTIT: title connection types
connectionTypesTitle=Elementi ({0})
#XTIT: title target spaces
targetSpaceTitle=Prostori ({0})
#XTIT: title connections
connectionsTitle=Povezave ({0})

#XCOL: space business name
spaceBusinessName=Prostor
#XCOL: spaces business name
spacesBusinessName=Prostori
#XCOL: business name
businessName=Poslovno ime
#XCOL: technical name
technicalName=Tehnično ime
#XCOL: modeling pattern
modelingPattern=Vzorec modeliranja
#XCOL: import status
importStatus=Status uvoza
#XCOL: replication flow
replicationFlow=Tok podvajanja
#XCOL: entity
entity=Entiteta
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Izbrane entitete
#XHED: heading selected entities
selectedApis=Izbrani API-ji
#XHED: heading dependent entities
dependentEntities=Odvisne entitete
#XHED: heading dependent entities
dependentEntitiesOf=Odvisne entitete {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Izbrane entitete API {0}
#XHED: heading entities of API
entitiesOfApi=Entitete API-ja {0}
#XHED: heading business builder
businessBuilderObjects=Poslovni graditelj ({0})
#XHED: heading data builder
dataBuilderObjects=Graditelj podatkov ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Prejšnje
#XBUT
buttonNextStep=Naslednji korak
#XBUT
buttonImport=Zaženi uvoz in postavitev
#XBUT
buttonSchedule=Načrtuj uvoz in postavitev
#XBUT
buttonCancel=Prekliči
#XBUT
buttonContinue=Nadaljuj
#XMSG
loadConnectionTypesFailed=Vrst povezave ni mogoče naložiti.
#XMSG
loadTargetSpacesFailed=Ciljnih prostorov ni mogoče naložiti.
#XMSG
loadConnectionsFailed=Povezav ni mogoče naložiti.
#XMSG
loadBwBridgeConnectionFailed=Povezave SAP BW Bridge ni mogoče naložiti.
#XMSG
getConnectionStatusFailed=Priklic statusa povezave za povezavo {0} ni uspel.
#XMSG
getSharedConnectionDetailsFailed=Ni mogoče pridobiti podrobnosti povezave za ID sistema {0}.
#XMSG
connectionNotValid=Povezava {0} ni veljavna povezava.
#XMSG
connectionBwBridgeNotValid=Povezava SAP BW Bridge {0} ni veljavna povezava.
#XMSG
connectionBwBridgeNotAvailable=Povezava SAP BW Bridge ni na voljo.
#XMSG
loadEntitiesFailed=Entitet ni mogoče naložiti.
#XMSG
importEntitiesFailed=Entitet ni mogoče uvoziti.
#XMSG
importEntitiesStarted=Uvoz entitet poteka. Preverite obvestila za status uvoza.
#XMSG
loadReviewEntitiesListFailed=Nalaganje objektov za uvoz ni uspelo.
#XMSG
selectOneResultListItem=Izberite le eno postavko s seznama rezultatov iskanja.
#XMSG
dialogCancel=Ali res želite preklicati uvoz entitet?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=Povezava {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=Oznaka
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW Bridge

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Ustvari objekte poslovnega graditelja
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Poslovne entitete in modeli porabe
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Samo poslovne entitete
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Brez

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Dostop do podatkov
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Tok podvajanja v lokalne tabele
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Oddaljene tabele
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Tok podvajanja
#XFLD: label Ingestion Space
ingestionSpaceLabel=Prostor sprejema

#XMSG
modelTransferOk=Uvoz modela je omogočen.
#XMSG
modelTransferNotOk=Uvoza modela ni mogoče uporabiti.
#XMSG
hanaSdiOk=Oddaljene tabele so omogočene.
#XMSG
hanaSdiNotOk=Oddaljenih tabel ni mogoče uporabiti.
#XMSG
replicationFlowOk=Tokovi podvajanja so omogočeni.
#XMSG
replicationFlowNotOk=Tokov podvajanja ni mogoče uporabiti.
#XMSG
onboardingOk=Uvajanje oddaljenih tabel in tokov replikacije je omogočeno.
#XMSG
onboardingNotOk=Uvajanje oddaljenih tabel in tokov replikacije ni mogoče.
#XMSG
enableReplicationFlow=Ta povezava lahko podpira podvajanje podatkov prek toka podvajanja, vendar ta funkcija trenutno ni omogočena. Obrnite se na vašega skrbnika, da posodobi povezavo. V vmesnem času lahko uvažate le entitete z dostopom do povezanih podatkov prek oddaljenih tabel.
#XMSG
selectedApiWithoutEntities=Izbrani API ne nudi entitet.
#XMSG
switchReplicationFlowToRemoteTables=Ti objekti so trenutno nameščeni kot lokalne tabele s podvojenimi podatki. S spremembo na združevanje se lahko poslabša delovanje. Ta sprememba bo vplivala na vse prostore, ki uporabljajo te objekte.\n\nŽelite spremeniti dostop do podatkov na združevanje prek oddaljenih tabel?
#XMSG
switchRemoteTablesToReplicationFlow=Ti objekti so trenutno nameščeni kot oddaljene tabele, v katere so združeni podatki. S spremembo na podvajanje se bo izboljšalo delovanje, vendar bo tudi onemogočen dostop v živo, tako da bo aktualnost podatkov odvisna od razporeda podvajanja. Ta sprememba bo vplivala na vse prostore, ki uporabljajo te predmete.\n\nŽelite spremeniti dostop do podatkov na podvajanje prek toka podvajanja in lokalnih tabel?

#XFLD
spaceTypeAbapBridge=SAP BW Bridge
#XFLD
spaceTypeDatasphere=SAP Datasphere
