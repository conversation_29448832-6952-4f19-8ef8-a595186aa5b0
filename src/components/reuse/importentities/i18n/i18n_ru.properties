#XTIT: Title for import entities
importEntitiesTitle=Импортировать сущности
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Выбрать тип соединения
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Выбрать целевое пространство
#XFLD: import wizard step 3: select connection
selectConnectionStep=Выбрать соединение
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Выбрать сущности
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Проверить сущности
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Выбрать соединение / целевое пространство

#XTIT: title connection types
connectionTypesTitle=Элементы ({0})
#XTIT: title target spaces
targetSpaceTitle=Пространства ({0})
#XTIT: title connections
connectionsTitle=Соединения ({0})

#XCOL: space business name
spaceBusinessName=Пространство
#XCOL: spaces business name
spacesBusinessName=Пространства
#XCOL: business name
businessName=Бизнес-имя
#XCOL: technical name
technicalName=Техническое имя
#XCOL: modeling pattern
modelingPattern=Шаблон моделирования
#XCOL: import status
importStatus=Статус импорта
#XCOL: replication flow
replicationFlow=Поток тиражирования
#XCOL: entity
entity=Сущность
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Выбранные сущности
#XHED: heading selected entities
selectedApis=Выбранные API
#XHED: heading dependent entities
dependentEntities=Зависимые сущности
#XHED: heading dependent entities
dependentEntitiesOf=Зависимые сущности {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Выбранные сущности API {0}
#XHED: heading entities of API
entitiesOfApi=Сущности API {0}
#XHED: heading business builder
businessBuilderObjects=Построитель бизнеса ({0})
#XHED: heading data builder
dataBuilderObjects=Построитель данных ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Назад
#XBUT
buttonNextStep=Следующий шаг
#XBUT
buttonImport=Запустить "Импорт и развернуть"
#XBUT
buttonSchedule=Запланировать "Импорт и развернуть"
#XBUT
buttonCancel=Отменить
#XBUT
buttonContinue=Продолжить
#XMSG
loadConnectionTypesFailed=Не удалось загрузить типы соединений.
#XMSG
loadTargetSpacesFailed=Не удалось загрузить целевые пространства.
#XMSG
loadConnectionsFailed=Не удалось загрузить соединения.
#XMSG
loadBwBridgeConnectionFailed=Не удалось загрузить соединение моста SAP BW.
#XMSG
getConnectionStatusFailed=Не удалось вызвать статус соединения {0}.
#XMSG
getSharedConnectionDetailsFailed=Не удалось вызвать подробные данные подключения для ид. системы {0}.
#XMSG
connectionNotValid=Соединение {0} недействительно.
#XMSG
connectionBwBridgeNotValid=Соединение моста SAP BW {0} недействительно.
#XMSG
connectionBwBridgeNotAvailable=Соединение моста SAP BW недоступно.
#XMSG
loadEntitiesFailed=Не удалось загрузить сущности.
#XMSG
importEntitiesFailed=Не удалось импортировать сущности.
#XMSG
importEntitiesStarted=Импортируем сущности. Проверьте уведомления о статусе импорта.
#XMSG
loadReviewEntitiesListFailed=Не удалось загрузить объекты для импорта.
#XMSG
selectOneResultListItem=Выберите только одну позицию из списка результатов поиска.
#XMSG
dialogCancel=Действительно отменить импорт сущностей?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=Ид.
identifier_plural=Соединение {0}
identifier_identifier=Ид.
identifier_entityname=Ид.
identifier_entitylabel=Метка
#XTXT: SAP BW Bridge Connection type
sapBwBridge=Мост SAP BW

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Создать объекты построителя бизнеса
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Бизнес-сущности и модели потребления
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Только бизнес-сущности
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Нет

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Доступ к данным
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Поток тиражирования в локальные таблицы
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Дистанционные таблицы
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Поток тиражирования
#XFLD: label Ingestion Space
ingestionSpaceLabel=Пространство приема

#XMSG
modelTransferOk=Импорт моделей активирован.
#XMSG
modelTransferNotOk=Невозможно использовать импорт моделей.
#XMSG
hanaSdiOk=Дистанционные таблицы активированы.
#XMSG
hanaSdiNotOk=Невозможно использовать дистанционные таблицы.
#XMSG
replicationFlowOk=Потоки тиражирования активированы.
#XMSG
replicationFlowNotOk=Невозможно использовать потоки тиражирования.
#XMSG
onboardingOk=Подключение дистанционных таблиц и потоков тиражирования активировано.
#XMSG
onboardingNotOk=Подключение дистанционных таблиц и потоков тиражирования невозможно.
#XMSG
enableReplicationFlow=Это соединение может поддерживать тиражирование данных с помощью потока тиражирования, но в настоящее время эта функция не активирована. Обратитесь к администратору с просьбой обновить соединение. В данный момент вы можете только импортировать сущности с доступом к федеративным данным с помощью дистанционных таблиц.
#XMSG
selectedApiWithoutEntities=Выбранный API не предоставляет никаких сущностей.
#XMSG
switchReplicationFlowToRemoteTables=В настоящее время эти объекты установлены как локальные таблицы с тиражируемыми в них данными. Изменение на федерацию может понизить производительность. Это изменение затронет все пространства, которые используют эти объекты.\n\nДействительно изменить доступ к данным на федерацию через дистанционные таблицы?
#XMSG
switchRemoteTablesToReplicationFlow=В настоящее время эти объекты установлены как дистанционные таблицы с федерируемыми в них данными. Изменение на тиражирование повысит производительность, но также отключит интерактивный доступ, и свежесть данных будет зависеть от расписания тиражирования. Это изменение затронет все пространства, которые используют эти объекты.\n\nДействительно изменить доступ к данным на тиражирование через поток тиражирования и локальные таблицы?

#XFLD
spaceTypeAbapBridge=Мост SAP BW
#XFLD
spaceTypeDatasphere=SAP Datasphere
