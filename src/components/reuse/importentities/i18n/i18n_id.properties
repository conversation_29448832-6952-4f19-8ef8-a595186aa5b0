#XTIT: Title for import entities
importEntitiesTitle=Impor Entitas
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Pilih Tipe Koneksi
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Pilih Ruang Target
#XFLD: import wizard step 3: select connection
selectConnectionStep=<PERSON>lih Koneksi
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Pilih Entitas
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Tinjau Entitas
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Pilih Koneksi / Ruang Target

#XTIT: title connection types
connectionTypesTitle=Item ({0})
#XTIT: title target spaces
targetSpaceTitle=Ruang ({0})
#XTIT: title connections
connectionsTitle=Koneksi ({0})

#XCOL: space business name
spaceBusinessName=Ruang
#XCOL: spaces business name
spacesBusinessName=Ruang
#XCOL: business name
businessName=Nama Bisnis
#XCOL: technical name
technicalName=<PERSON>a <PERSON>
#XCOL: modeling pattern
modelingPattern=Pola Pemodelan
#XCOL: import status
importStatus=Status Impor
#XCOL: replication flow
replicationFlow=Aliran Replikasi
#XCOL: entity
entity=Entitas
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Entitas yang Dipilih
#XHED: heading selected entities
selectedApis=API yang dipilih
#XHED: heading dependent entities
dependentEntities=Entitas Dependen
#XHED: heading dependent entities
dependentEntitiesOf=Entitas Dependen {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Entitas yang Dipilih dari API {0}
#XHED: heading entities of API
entitiesOfApi=Entitas API {0}
#XHED: heading business builder
businessBuilderObjects=Business Builder ({0})
#XHED: heading data builder
dataBuilderObjects=Data Builder ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Sebelumnya
#XBUT
buttonNextStep=Langkah Selanjutnya
#XBUT
buttonImport=Mulai Impor dan Sebarkan
#XBUT
buttonSchedule=Jadwalkan Impor dan Sebarkan
#XBUT
buttonCancel=Batalkan
#XBUT
buttonContinue=Lanjutkan
#XMSG
loadConnectionTypesFailed=Tidak dapat memuat tipe koneksi.
#XMSG
loadTargetSpacesFailed=Tidak dapat memuat ruang target.
#XMSG
loadConnectionsFailed=Tidak dapat memuat koneksi.
#XMSG
loadBwBridgeConnectionFailed=Tidak dapat memuat koneksi SAP BW bridge.
#XMSG
getConnectionStatusFailed=Tidak dapat mengambil status koneksi {0}.
#XMSG
getSharedConnectionDetailsFailed=Tidak dapat mengambil rincian koneksi untuk ID sistem {0}.
#XMSG
connectionNotValid=Koneksi {0} bukan koneksi yang valid.
#XMSG
connectionBwBridgeNotValid=Koneksi SAP BW bridge {0} bukan koneksi yang valid.
#XMSG
connectionBwBridgeNotAvailable=Koneksi SAP BW bridge tidak tersedia.
#XMSG
loadEntitiesFailed=Tidak dapat memuat entitas.
#XMSG
importEntitiesFailed=Tidak dapat mengimpor entitas.
#XMSG
importEntitiesStarted=Mengimpor entitas. Periksa pemberitahuan untuk status impor.
#XMSG
loadReviewEntitiesListFailed=Tidak dapat memuat objek untuk diimpor.
#XMSG
selectOneResultListItem=Pilih hanya satu item dalam daftar hasil pencarian.
#XMSG
dialogCancel=Apakah Anda benar-benar ingin membatalkan impor entitas?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=Koneksi {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=Label
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW Bridge

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Buat Objek Business Builder
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Entitas Bisnis dan Model Pemakaian
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Hanya Entitas Bisnis
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Tidak Ada

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Akses Data
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Aliran Replikasi ke Tabel Lokal
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Tabel Jarak Jauh
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Aliran Replikasi
#XFLD: label Ingestion Space
ingestionSpaceLabel=Ruang Pengumpulan

#XMSG
modelTransferOk=Impor model diaktifkan.
#XMSG
modelTransferNotOk=Impor model tidak dapat digunakan.
#XMSG
hanaSdiOk=Tabel jarak jauh diaktifkan.
#XMSG
hanaSdiNotOk=Tabel jarak jauh tidak dapat digunakan.
#XMSG
replicationFlowOk=Aliran replikasi diaktifkan.
#XMSG
replicationFlowNotOk=Aliran replikasi tidak dapat digunakan.
#XMSG
onboardingOk=Proses penyiapan tabel jarak jauh dan aliran replikasi telah diaktifkan.
#XMSG
onboardingNotOk=Proses penyiapan tabel jarak jauh dan aliran replikasi tidak dapat dilakukan.
#XMSG
enableReplicationFlow=Koneksi ini dapat mendukung replikasi data melalui aliran replikasi, tetapi fitur ini tidak diaktifkan saat ini. Silakan hubungi administrator Anda untuk memperbarui koneksi. Sementara itu, Anda hanya dapat mengimpor entitas dengan akses data yang difederasi melalui tabel jarak jauh.
#XMSG
selectedApiWithoutEntities=API yang dipilih tidak menyediakan entitas apa pun.
#XMSG
switchReplicationFlowToRemoteTables=Saat ini, objek ini diinstal sebagai tabel lokal dengan data yang direplikasi ke dalam tabel tersebut. Dengan mengubahnya menjadi federasi, kinerja dapat menurun. Perubahan ini akan berdampak pada semua ruang yang menggunakan objek tersebut.\n\nApakah Anda ingin mengubah akses data menjadi federasi melalui tabel jarak jauh?
#XMSG
switchRemoteTablesToReplicationFlow=Saat ini, objek ini diinstal sebagai tabel jarak jauh dengan data yang difederasikan ke dalam tabel tersebut. Dengan mengubahnya menjadi replikasi, kinerja akan meningkat, tetapi akses langsung juga akan dinonaktifkan. Akibatnya, data Anda akan bergantung pada jadwal replikasi agar tetap mutakhir. Perubahan ini akan berdampak pada semua ruang yang menggunakan objek tersebut.\n\nApakah Anda ingin mengubah akses data ke replikasi melalui aliran replikasi dan tabel lokal?

#XFLD
spaceTypeAbapBridge=SAP BW Bridge
#XFLD
spaceTypeDatasphere=SAP Datasphere
