#XTIT: Title for import entities
importEntitiesTitle=Entitások importálása
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Kapcsolattípus kiválasztása
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Céltér kiválasztása
#XFLD: import wizard step 3: select connection
selectConnectionStep=Kapcsolat kiválasztása
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Entitások kiválasztása
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Entitások ellenőrzése
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Kapcsolat / céltér kiválasztása

#XTIT: title connection types
connectionTypesTitle=Elemek ({0})
#XTIT: title target spaces
targetSpaceTitle=Terek ({0})
#XTIT: title connections
connectionsTitle=Kapcsolatok ({0})

#XCOL: space business name
spaceBusinessName=Tér
#XCOL: spaces business name
spacesBusinessName=Terek
#XCOL: business name
businessName=Üzleti név
#XCOL: technical name
technicalName=Technikai név
#XCOL: modeling pattern
modelingPattern=Modellezési minta
#XCOL: import status
importStatus=Importálási állapot
#XCOL: replication flow
replicationFlow=Replikációs folyamat
#XCOL: entity
entity=Entitás
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Kiválasztott entitások
#XHED: heading selected entities
selectedApis=Kiválasztott API-k
#XHED: heading dependent entities
dependentEntities=Függő entitások
#XHED: heading dependent entities
dependentEntitiesOf={0} függő entitásai
#XHED: heading dependent entities
selectedEntitiesOfApi={0} API kiválasztott entitásai
#XHED: heading entities of API
entitiesOfApi=A(z) {0} API entitásai
#XHED: heading business builder
businessBuilderObjects=Üzleti szerkesztő ({0})
#XHED: heading data builder
dataBuilderObjects=Adatmodell-szerkesztő ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Vissza
#XBUT
buttonNextStep=Következő lépés
#XBUT
buttonImport=Importálás és üzembe helyezés indítása
#XBUT
buttonSchedule=Importálás és üzembe helyezés beütemezése
#XBUT
buttonCancel=Mégse
#XBUT
buttonContinue=Folytatás
#XMSG
loadConnectionTypesFailed=Nem lehet betölteni a kapcsolattípusokat.
#XMSG
loadTargetSpacesFailed=Nem lehet betölteni a céltereket.
#XMSG
loadConnectionsFailed=Nem lehet betölteni a kapcsolatokat.
#XMSG
loadBwBridgeConnectionFailed=Nem lehet betölteni az SAP BW-híd-kapcsolatot
#XMSG
getConnectionStatusFailed=Nem lehet lehívni a(z) {0} kapcsolat állapotát.
#XMSG
getSharedConnectionDetailsFailed=Nem lehet lehívni a kapcsolat részleteit a(z) {0} rendszerazonosítóhoz.
#XMSG
connectionNotValid=A(z) {0} kapcsolat nem érvényes.
#XMSG
connectionBwBridgeNotValid=A(z) {0} SAP BW-híd-kapcsolat nem érvényes.
#XMSG
connectionBwBridgeNotAvailable=Az SAP BW-híd-kapcsolat nem érhető el.
#XMSG
loadEntitiesFailed=Nem lehet betölteni az entitásokat.
#XMSG
importEntitiesFailed=Nem lehet importálni az entitásokat.
#XMSG
importEntitiesStarted=Az entitások importálása folyamatban van. Az importálás állapota az értesítésekben követhető nyomon.
#XMSG
loadReviewEntitiesListFailed=Nem lehet betölteni az importálandó objektumokat.
#XMSG
selectOneResultListItem=Csak egy elemet válasszon a találatlistából.
#XMSG
dialogCancel=Biztosan megszakítja az entitások importálását?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=Azonosító
identifier_plural={0} kapcsolat
identifier_identifier=Azonosító
identifier_entityname=Azonosító
identifier_entitylabel=Címke
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW-híd

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Üzletiszerkesztő-objektumok létrehozása
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Üzleti entitások és felhasználási modellek
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Csak az üzleti entitások
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Egyik sem

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Adathozzáférés
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Replikációs folyamat helyi táblákba
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Távoli táblák
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Replikációs folyamat
#XFLD: label Ingestion Space
ingestionSpaceLabel=Betöltési tér

#XMSG
modelTransferOk=A modellimportálás engedélyezett.
#XMSG
modelTransferNotOk=Nem használható modellimportálás.
#XMSG
hanaSdiOk=A távoli táblák engedélyezettek.
#XMSG
hanaSdiNotOk=Nem használhatók távoli táblák.
#XMSG
replicationFlowOk=A replikációs folyamatok engedélyezettek.
#XMSG
replicationFlowNotOk=Nem használhatók replikációs folyamatok.
#XMSG
onboardingOk=A távoli táblák és replikációs folyamatok beiktatása engedélyezve.
#XMSG
onboardingNotOk=A távoli táblák és replikációs folyamatok beiktatása nem lehetséges.
#XMSG
enableReplicationFlow=Ez a kapcsolat támogatja az adatok replikációs folyamat általi replikálását, de jelenleg nincs engedélyezve ez a funkció. Kérje meg az adminisztrátort, hogy frissítse a kapcsolatot. Addig csak távoli táblák általi összevont adathozzáféréssel importálhat entitásokat.
#XMSG
selectedApiWithoutEntities=A kiválasztott API nem biztosít entitásokat.
#XMSG
switchReplicationFlowToRemoteTables=Ezek az objektumok jelenleg olyan helyi táblákként vannak telepítve, amelyekbe replikálódnak az adatok. Ha összevonásra vált, akkor romolhat a teljesítmény. Ez a változás minden olyan térre hatással lesz, amely felhasználja ezeket az objektumokat.\n\nÁtváltja az adathozzáférést távoli táblák általi összevonásra?
#XMSG
switchRemoteTablesToReplicationFlow=Ezek az objektumok jelenleg olyan helyi táblákként vannak telepítve, amelyekben össze vannak vonva az adatok. Ha replikálásra vált, akkor javulhat a teljesítmény, de megszűnik az élő hozzáférés, vagyis az adatai frissessége a replikáció ütemezésétől függ. Ez a változás minden olyan térre hatással lesz, amely felhasználja ezeket az objektumokat.\n\nÁtváltja az adathozzáférést replikációs folyamat és helyi táblák általi replikációra?

#XFLD
spaceTypeAbapBridge=SAP BW-híd
#XFLD
spaceTypeDatasphere=SAP Datasphere
