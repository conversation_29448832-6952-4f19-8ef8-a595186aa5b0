#XTIT: Title for import entities
importEntitiesTitle=Объектілерді импорттау
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Қосылым түрін таңдау
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Мақсатты кеңістікті таңдау
#XFLD: import wizard step 3: select connection
selectConnectionStep=Қосылымды таңдау
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Объектілерді таңдау
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Объектілерді қарап шығу
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Қосылымды/Мақсатты кеңістікті таңдау

#XTIT: title connection types
connectionTypesTitle=Тармақтар ({0})
#XTIT: title target spaces
targetSpaceTitle=Кеңістіктер ({0})
#XTIT: title connections
connectionsTitle=Қосылымдар ({0})

#XCOL: space business name
spaceBusinessName=Кеңістік
#XCOL: spaces business name
spacesBusinessName=Кеңістіктер
#XCOL: business name
businessName=Бизнес атау
#XCOL: technical name
technicalName=Техникалық атау
#XCOL: modeling pattern
modelingPattern=Үлгілеу сұлбасы
#XCOL: import status
importStatus=Импорттау күйі
#XCOL: replication flow
replicationFlow=Тираждау ағыны
#XCOL: entity
entity=Объект
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Таңдалған объектілер
#XHED: heading selected entities
selectedApis=Таңдалған API
#XHED: heading dependent entities
dependentEntities=Тәуелді объектілер
#XHED: heading dependent entities
dependentEntitiesOf={0} тәуелді объектілері
#XHED: heading dependent entities
selectedEntitiesOfApi=API {0} таңдалған объектілері
#XHED: heading entities of API
entitiesOfApi=API {0} объектілері
#XHED: heading business builder
businessBuilderObjects=Business Builder ({0})
#XHED: heading data builder
dataBuilderObjects=Дерек құрастырғыш ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Алдыңғы
#XBUT
buttonNextStep=Келесі қадам
#XBUT
buttonImport=Импорттауды және қолданысқа енгізуді бастау
#XBUT
buttonSchedule=Импорттауды және қолданысқа енгізуді жоспарлау
#XBUT
buttonCancel=Бас тарту
#XBUT
buttonContinue=Жалғастыру
#XMSG
loadConnectionTypesFailed=Қосылым түрлерін жүктеу мүмкін емес.
#XMSG
loadTargetSpacesFailed=Мақсатты кеңістіктерді жүктеу мүмкін емес.
#XMSG
loadConnectionsFailed=Қосылымдарды жүктеу мүмкін емес.
#XMSG
loadBwBridgeConnectionFailed=SAP BW Bridge қосылымын жүктеу мүмкін емес.
#XMSG
getConnectionStatusFailed={0} қосылым күйін алу мүмкін емес.
#XMSG
getSharedConnectionDetailsFailed={0} жүйе ид. үшін қосылымның мәліметтерін алу мүмкін емес.
#XMSG
connectionNotValid={0} қосылымы жарамды қосылым емес.
#XMSG
connectionBwBridgeNotValid={0} SAP BW Bridge қосылымы жарамды қосылым емес.
#XMSG
connectionBwBridgeNotAvailable=SAP BW Bridge қосылымы қолжетімді емес.
#XMSG
loadEntitiesFailed=Объектілерді жүктеу мүмкін емес.
#XMSG
importEntitiesFailed=Объектілерді импорттау мүмкін емес.
#XMSG
importEntitiesStarted=Объектілер импортталуда. Импорттау күйіне арналған хабарландыруларды тексеріңіз.
#XMSG
loadReviewEntitiesListFailed=Импорттау үшін нысандарды жүктеу мүмкін емес.
#XMSG
selectOneResultListItem=Іздеу нәтижелерінің тізімінен тек бір элементті таңдаңыз.
#XMSG
dialogCancel=Объектілерді импорттаудан бас тарту қажеттігін растайсыз ба?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=Ид.
identifier_plural={0} қосылымы
identifier_identifier=Ид.
identifier_entityname=Ид.
identifier_entitylabel=Белгі
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW Bridge

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Business Builder нысандарын жасау
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Бизнес объектілер мен тұтыну үлгілері
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Тек бизнес объект
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Ешқайсысы

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Дерекке қол жеткізу
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Жергілікті кестелерге тираждау ағыны
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Қашықтағы кестелер
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Тираждау ағыны
#XFLD: label Ingestion Space
ingestionSpaceLabel=Қабылдау кеңістігі

#XMSG
modelTransferOk=Үлгіні импорттау қосылды.
#XMSG
modelTransferNotOk=Үлгіні импорттауды пайдалану мүмкін емес.
#XMSG
hanaSdiOk=Қашықтағы кестелер қосылды.
#XMSG
hanaSdiNotOk=Қашықтағы кестелерді пайдалану мүмкін емес.
#XMSG
replicationFlowOk=Тираждау ағындары қосылды.
#XMSG
replicationFlowNotOk=Тираждау ағындарын пайдалану мүмкін емес.
#XMSG
onboardingOk=Қашықтағы кестелерді және тираждау ағындарын бейімдеу қосылды.
#XMSG
onboardingNotOk=Қашықтағы кестелерді және тираждау ағындарын бейімдеу мүмкін емес.
#XMSG
enableReplicationFlow=Бұл қосылым тираждау ағыны арқылы деректерді тираждауға қолдау көрсете алады, бірақ бұл функция қазір қосылмаған. Қосылымды жаңарту үшін әкімшіңізге хабарласыңыз. Әзірге қашықтағы кестелер арқылы федеративтік деректерге қол жеткізу рұқсаты бар объектілерді ғана импорттай аласыз.
#XMSG
selectedApiWithoutEntities=Таңдалған API ешқандай объектілерді ұсынбайды.
#XMSG
switchReplicationFlowToRemoteTables=Ағымдағы уақытта бұл нысандар дерек тираждалатын жергілікті кестелер ретінде орнатылады. Біріктіруге ауысу өнімділікті төмендетуі мүмкін. Бұл өзгеріс осы нысандарды тұтынатын барлық кеңістіктерге әсер етеді.\n\nҚашықтағы кестелер арқылы біріктіру үшін деректерге қол жеткізуді өзгерту қажет пе?
#XMSG
switchRemoteTablesToReplicationFlow=Ағымдағы уақытта бұл нысандар дерек біріктірілетін қашықтағы кестелер ретінде орнатылады. Тираждауға ауысу өнімділікті жақсартады, сонымен қатар дерек жаңалығы тираждау кестеңізге байланысты болатындай тікелей қол жеткізуді ажыратады. Бұл өзгеріс осы нысандарды тұтынатын барлық кеңістіктерге әсер етеді.\n\nТираждау ағыны және жергілікті кестелер арқылы тираждау үшін дерекке қол жеткізуді өзгерту қажет пе?

#XFLD
spaceTypeAbapBridge=SAP BW Bridge
#XFLD
spaceTypeDatasphere=SAP Datasphere
