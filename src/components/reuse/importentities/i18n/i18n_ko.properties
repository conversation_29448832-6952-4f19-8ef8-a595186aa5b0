#XTIT: Title for import entities
importEntitiesTitle=엔티티 임포트
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=연결 유형 선택
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=대상 공간 선택
#XFLD: import wizard step 3: select connection
selectConnectionStep=연결 선택
#XFLD: import wizard step 4: select entities
selectEntitiesStep=엔티티 선택
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=엔티티 검토
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=연결/대상 공간 선택

#XTIT: title connection types
connectionTypesTitle=항목({0})
#XTIT: title target spaces
targetSpaceTitle=공간({0})
#XTIT: title connections
connectionsTitle=연결({0})

#XCOL: space business name
spaceBusinessName=공간
#XCOL: spaces business name
spacesBusinessName=공간
#XCOL: business name
businessName=업무 이름
#XCOL: technical name
technicalName=기술적 이름
#XCOL: modeling pattern
modelingPattern=모델링 패턴
#XCOL: import status
importStatus=임포트 상태
#XCOL: replication flow
replicationFlow=복제 흐름
#XCOL: entity
entity=엔티티
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=엔티티 선택
#XHED: heading selected entities
selectedApis=선택한 API
#XHED: heading dependent entities
dependentEntities=종속 엔티티
#XHED: heading dependent entities
dependentEntitiesOf={0}의 종속 엔티티
#XHED: heading dependent entities
selectedEntitiesOfApi=API {0}의 선택된 엔티티
#XHED: heading entities of API
entitiesOfApi=API {0}의 엔티티
#XHED: heading business builder
businessBuilderObjects=비즈니스 빌더({0})
#XHED: heading data builder
dataBuilderObjects=데이터 빌더({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=이전
#XBUT
buttonNextStep=다음 단계
#XBUT
buttonImport=임포트 후 배포 시작
#XBUT
buttonSchedule=임포트 후 배포 예약
#XBUT
buttonCancel=취소
#XBUT
buttonContinue=계속
#XMSG
loadConnectionTypesFailed=연결 유형을 로드할 수 없습니다.
#XMSG
loadTargetSpacesFailed=대상 공간을 로드할 수 없습니다.
#XMSG
loadConnectionsFailed=연결을 로드할 수 없습니다.
#XMSG
loadBwBridgeConnectionFailed=SAP BW 브리지 연결을 로드할 수 없습니다.
#XMSG
getConnectionStatusFailed=연결 {0}의 상태를 가져올 수 없습니다.
#XMSG
getSharedConnectionDetailsFailed=시스템 ID {0}에 대한 연결 세부사항을 가져올 수 없습니다.
#XMSG
connectionNotValid=연결 {0}은(는) 유효한 연결이 아닙니다.
#XMSG
connectionBwBridgeNotValid=SAP BW 브리지 연결 {0}은(는) 유효한 연결이 아닙니다.
#XMSG
connectionBwBridgeNotAvailable=SAP BW 브리지 연결을 사용할 수 없습니다.
#XMSG
loadEntitiesFailed=엔티티를 로드할 수 없습니다.
#XMSG
importEntitiesFailed=엔티티를 임포트할 수 없습니다.
#XMSG
importEntitiesStarted=엔티티를 임포트하는 중입니다. 임포트 상태에 대한 알림을 확인하십시오.
#XMSG
loadReviewEntitiesListFailed=임포트할 오브젝트를 로드할 수 없습니다.
#XMSG
selectOneResultListItem=검색 결과 리스트에서 항목을 하나만 선택하십시오.
#XMSG
dialogCancel=엔티티 임포트를 취소하시겠습니까?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=연결 {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=레이블
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW 브리지

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=비즈니스 빌더 오브젝트 생성
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=비즈니스 엔티티 및 사용 모델
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=비즈니스 엔티티만
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=없음

#XFLD: label Remote Access Mode
remoteAccessModeLabel=데이터 액세스
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=로컬 테이블로의 복제 흐름
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=원격 테이블
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=복제 흐름
#XFLD: label Ingestion Space
ingestionSpaceLabel=수집 공간

#XMSG
modelTransferOk=모델 임포트가 활성화되었습니다.
#XMSG
modelTransferNotOk=모델 임포트를 사용할 수 없습니다.
#XMSG
hanaSdiOk=원격 테이블이 활성화되었습니다.
#XMSG
hanaSdiNotOk=원격 테이블을 사용할 수 없습니다.
#XMSG
replicationFlowOk=복제 흐름이 활성화되었습니다.
#XMSG
replicationFlowNotOk=복제 흐름을 사용할 수 없습니다.
#XMSG
onboardingOk=원격 테이블과 복제 흐름의 온보딩이 활성화되었습니다.
#XMSG
onboardingNotOk=원격 테이블과 복제 흐름을 온보딩할 수 없습니다.
#XMSG
enableReplicationFlow=이 연결을 사용하면 복제 흐름을 통한 데이터 복제가 가능하지만 이 기능은 현재 활성화되어 있지 않습니다. 연결을 업데이트하려면 관리자에게 문의하십시오. 그 동안에는 원격 테이블을 통해 통합 데이터 액세스가 가능한 엔티티만 임포트할 수 있습니다.
#XMSG
selectedApiWithoutEntities=선택한 API가 엔티티를 제공하지 않습니다.
#XMSG
switchReplicationFlowToRemoteTables=이러한 오브젝트는 현재 복제된 데이터를 포함하여 로컬 테이블로 설치되어 있습니다. 페더레이션을 변경할 경우 성능이 저하될 수 있습니다. 이 변경을 수행하면 해당 오브젝트를 사용하는 모든 공간에 영향을 줍니다.\n\n원격 테이블을 통해 페더레이션에 대한 데이터 액세스를 변경하시겠습니까?
#XMSG
switchRemoteTablesToReplicationFlow=이러한 오브젝트는 현재 페더레이션된 데이터를 포함하여 원격 테이블로 설치되어 있습니다. 복제를 변경할 경우 성능이 향상되지만 라이브 액세스 역시 비활성화되어 데이터의 최신성은 복제 일정에 따라 달라집니다. 이 변경을 수행하면 해당 오브젝트를 사용하는 모든 공간에 영향을 줍니다.\n\n복제 흐름 및 로컬 테이블을 통해 복제에 대한 데이터 액세스를 변경하시겠습니까?

#XFLD
spaceTypeAbapBridge=SAP BW 브리지
#XFLD
spaceTypeDatasphere=SAP Datasphere
