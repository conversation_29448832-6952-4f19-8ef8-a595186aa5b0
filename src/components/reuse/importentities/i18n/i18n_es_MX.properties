#XTIT: Title for import entities
importEntitiesTitle=Importar entidades
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Seleccionar tipo de conexión
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Seleccionar espacio de destino
#XFLD: import wizard step 3: select connection
selectConnectionStep=Seleccionar conexión
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Seleccionar entidades
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Revisar entidades
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Seleccionar conexión/espacio de destino

#XTIT: title connection types
connectionTypesTitle=Elementos ({0})
#XTIT: title target spaces
targetSpaceTitle=Espacios ({0})
#XTIT: title connections
connectionsTitle=Conexiones ({0})

#XCOL: space business name
spaceBusinessName=Espacio
#XCOL: spaces business name
spacesBusinessName=Espacios
#XCOL: business name
businessName=Nombre empresarial
#XCOL: technical name
technicalName=Nombre técnico
#XCOL: modeling pattern
modelingPattern=Patrón de modelado
#XCOL: import status
importStatus=Estado de importación
#XCOL: replication flow
replicationFlow=Flujo de replicación
#XCOL: entity
entity=Entidad
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Entidades seleccionadas
#XHED: heading selected entities
selectedApis=API seleccionadas
#XHED: heading dependent entities
dependentEntities=Entidades dependientes
#XHED: heading dependent entities
dependentEntitiesOf=Entidades dependientes de {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Entidades seleccionadas de API {0}
#XHED: heading entities of API
entitiesOfApi=Entidades de API {0}
#XHED: heading business builder
businessBuilderObjects=Generador empresarial ({0})
#XHED: heading data builder
dataBuilderObjects=Generador de datos ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Anterior
#XBUT
buttonNextStep=Próximo paso
#XBUT
buttonImport=Iniciar importación e implementar
#XBUT
buttonSchedule=Programar importación e implementar
#XBUT
buttonCancel=Cancelar
#XBUT
buttonContinue=Continuar
#XMSG
loadConnectionTypesFailed=No se pueden cargar tipos de conexión.
#XMSG
loadTargetSpacesFailed=No se pueden cargar espacios de destino.
#XMSG
loadConnectionsFailed=No se pueden cargar conexiones.
#XMSG
loadBwBridgeConnectionFailed=No se pueden cargar conexiones de puente de SAP BW.
#XMSG
getConnectionStatusFailed=No se pudo recuperar el estado de la conexión {0}.
#XMSG
getSharedConnectionDetailsFailed=No se pueden recuperar los detalles de la conexión para el ID de sistema {0}.
#XMSG
connectionNotValid=La conexión {0} no es válida.
#XMSG
connectionBwBridgeNotValid=La conexión de puente de SAP BW {0} no es válida.
#XMSG
connectionBwBridgeNotAvailable=La conexión de puente de SAP BW no está disponible.
#XMSG
loadEntitiesFailed=No se pueden cargar entidades.
#XMSG
importEntitiesFailed=No se pueden importar entidades.
#XMSG
importEntitiesStarted=Importando entidades. Verifique las notificaciones para ver el estado de la importación.
#XMSG
loadReviewEntitiesListFailed=No se pueden cargar objetos para importar.
#XMSG
selectOneResultListItem=Seleccione solo un elemento en la lista de resultados de la búsqueda.
#XMSG
dialogCancel=¿Seguro que desea cancelar la importación de las entidades?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=Conexión {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=Etiqueta
#XTXT: SAP BW Bridge Connection type
sapBwBridge=Puente de SAP BW

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Crear objetos del generador empresarial
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Entidades empresariales y modelos de consumo
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Solo entidades empresariales
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Ninguno

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Acceso a datos
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Flujo de replicación hasta tablas locales
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Tablas remotas
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Flujo de replicación
#XFLD: label Ingestion Space
ingestionSpaceLabel=Espacio de ingestión

#XMSG
modelTransferOk=Se habilitó la importación del modelo.
#XMSG
modelTransferNotOk=La importación del modelo no se puede utilizar.
#XMSG
hanaSdiOk=Las tablas remotas están habilitadas.
#XMSG
hanaSdiNotOk=Las tablas remotas no se pueden usar.
#XMSG
replicationFlowOk=Los flujos de replicación están habilitados.
#XMSG
replicationFlowNotOk=Los flujos de replicación no se pueden utilizar.
#XMSG
onboardingOk=Se activó la incorporación de tablas remotas y flujos de replicación.
#XMSG
onboardingNotOk=La incorporación de tablas remotas y flujos de replicación no es posible.
#XMSG
enableReplicationFlow=Esta conexión admite la replicación de datos a través de un flujo de replicación, pero esta función no está habilitada actualmente. Comuníquese con el administrador para actualizar la conexión. Mientras tanto, solo puede importar entidades con acceso a datos federados a través de tablas remotas.
#XMSG
selectedApiWithoutEntities=La API seleccionada no proporciona ninguna entidad.
#XMSG
switchReplicationFlowToRemoteTables=Estos objetos están actualmente instalados como tablas locales con datos replicados. El cambio a federación puede bajar el rendimiento. Este cambio impactará en todos los espacios que consumen estos objetos.\n\n¿Quiere cambiar el acceso de los datos a federación a través de tablas remotas?
#XMSG
switchRemoteTablesToReplicationFlow=Estos objetos están actualmente instalados como tablas remotas con datos federados. El cambio a replicación mejorará el rendimiento, pero también desactivará el acceso en tiempo real de manera que la actualización de sus datos dependerá del programa de replicación. Este cambio impactará en todos los espacios que consumen estos objetos.\n\n¿Quiere cambiar el acceso de los datos a replicación a través del flujo de replicación y las tablas locales?

#XFLD
spaceTypeAbapBridge=Puente de SAP BW
#XFLD
spaceTypeDatasphere=SAP Datasphere
