#XTIT: Title for import entities
importEntitiesTitle=Importer entiteter
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Vælg forbindelsestype
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Vælg målspace
#XFLD: import wizard step 3: select connection
selectConnectionStep=Vælg forbindelse
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Vælg entiteter
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Gennemgå entiteter
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Vælg forbindelse/målspace

#XTIT: title connection types
connectionTypesTitle=Elementer ({0})
#XTIT: title target spaces
targetSpaceTitle=Spaces ({0})
#XTIT: title connections
connectionsTitle=Forbindelser ({0})

#XCOL: space business name
spaceBusinessName=Space
#XCOL: spaces business name
spacesBusinessName=Spaces
#XCOL: business name
businessName=Forretningsnavn
#XCOL: technical name
technicalName=Teknisk navn
#XCOL: modeling pattern
modelingPattern=Modelleringsmønster
#XCOL: import status
importStatus=Importstatus
#XCOL: replication flow
replicationFlow=Replikeringsflow
#XCOL: entity
entity=Entitet
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Valgte entiteter
#XHED: heading selected entities
selectedApis=Valgte API'er
#XHED: heading dependent entities
dependentEntities=Afhængige entiteter
#XHED: heading dependent entities
dependentEntitiesOf=Afhængige entiteter for {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Valgte entiteter for API {0}
#XHED: heading entities of API
entitiesOfApi=Entiteter for API {0}
#XHED: heading business builder
businessBuilderObjects=Forretningsgenerator ({0})
#XHED: heading data builder
dataBuilderObjects=Datagenerator ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Forrige
#XBUT
buttonNextStep=Næste trin
#XBUT
buttonImport=Start import, og implementer
#XBUT
buttonSchedule=Planlæg import, og implementer
#XBUT
buttonCancel=Annuller
#XBUT
buttonContinue=Fortsæt
#XMSG
loadConnectionTypesFailed=Kan ikke indlæse forbindelsestyper.
#XMSG
loadTargetSpacesFailed=Kan ikke indlæse målspaces.
#XMSG
loadConnectionsFailed=Kan ikke indlæse forbindelser.
#XMSG
loadBwBridgeConnectionFailed=Kan ikke indlæse SAP BW Bridge-forbindelse.
#XMSG
getConnectionStatusFailed=Kan ikke hente statussen for forbindelsen {0}.
#XMSG
getSharedConnectionDetailsFailed=Kan ikke hente detaljerne om forbindelsen for system-id {0}.
#XMSG
connectionNotValid=Forbindelsen {0} er ikke en gyldig forbindelse.
#XMSG
connectionBwBridgeNotValid=SAP BW Bridge-forbindelsen {0} er ikke en gyldig forbindelse.
#XMSG
connectionBwBridgeNotAvailable=SAP BW Bridge-forbindelsen er ikke tilgængelig.
#XMSG
loadEntitiesFailed=Kan ikke indlæse entiteter.
#XMSG
importEntitiesFailed=Kan ikke importere entiteter.
#XMSG
importEntitiesStarted=Importerer entiteter. Kontroller meddelelserne for at se importens status.
#XMSG
loadReviewEntitiesListFailed=Kan ikke indlæse objekter, der skal importeres.
#XMSG
selectOneResultListItem=Vælg kun et element på listen med søgeresultater.
#XMSG
dialogCancel=Er du sikker på, at du vil annullere importen af entiteter?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=Id
identifier_plural=Forbindelse {0}
identifier_identifier=Id
identifier_entityname=Id
identifier_entitylabel=Etiket
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW Bridge

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Opret forretningsgeneratorobjekter
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Forretningsentiteter og forbrugsmodeller
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Kun forretningsentiteter
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Ingen

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Dataadgang
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Replikeringsflow til lokale tabeller
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Eksterne tabeller
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Replikeringsflow
#XFLD: label Ingestion Space
ingestionSpaceLabel=Indtagelses-space

#XMSG
modelTransferOk=Import af modeller er aktiveret.
#XMSG
modelTransferNotOk=Import af modeller kan ikke bruges.
#XMSG
hanaSdiOk=Eksterne tabeller er aktiveret.
#XMSG
hanaSdiNotOk=Eksterne tabeller kan ikke bruges.
#XMSG
replicationFlowOk=Replikeringsflows er aktiveret.
#XMSG
replicationFlowNotOk=Replikeringsflows kan ikke bruges.
#XMSG
onboardingOk=Onboarding af eksterne tabeller og replikeringsflows er aktiveret.
#XMSG
onboardingNotOk=Onboarding af eksterne tabeller og replikeringsflows er ikke mulig.
#XMSG
enableReplicationFlow=Denne forbindelse kan understøtte replikering af data via et replikeringsflow, men denne funktion er p.t. ikke aktiv. Kontakt din administrator for at opdatere forbindelsen. I mellemtiden kan du kun importere entiteter med sammenkædet dataadgang via eksterne tabeller.
#XMSG
selectedApiWithoutEntities=Den valgte API leverer ikke nogen entiteter.
#XMSG
switchReplicationFlowToRemoteTables=Disse objekter er aktuelt installeret som lokale tabeller med data replikeret til dem. Ændring til sammenslutning kan forringe ydeevnen. Denne ændring vil påvirke alle spaces, der forbruger disse objekter.\n\nVil du ændre dataadgangen til sammenslutning via eksterne tabeller?
#XMSG
switchRemoteTablesToReplicationFlow=Disse objekter er aktuelt installeret som eksterne tabeller med data sammenkædet med dem. Ændring til replikering vil forbedre ydeevnen, men vil også deaktivere live-adgang, så aktualiteten af dine data afhænger af din replikeringstidsplan. Denne ændring vil påvirke alle spaces, der forbruger disse objekter\n\nVil du ændre dataadgangen til replikering via et replikeringsflow og lokale tabeller?

#XFLD
spaceTypeAbapBridge=SAP BW Bridge
#XFLD
spaceTypeDatasphere=SAP Datasphere
