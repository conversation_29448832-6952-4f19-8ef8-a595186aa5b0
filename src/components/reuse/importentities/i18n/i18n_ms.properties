#XTIT: Title for import entities
importEntitiesTitle=Import Entiti
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Pilih Jenis <PERSON>nga<PERSON>
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Pilih Ruang Sasaran
#XFLD: import wizard step 3: select connection
selectConnectionStep=Pilih Sambungan
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Pilih Entiti
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Semak Entiti
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Pilih Sambungan / Ruang Sasaran

#XTIT: title connection types
connectionTypesTitle=Item ({0})
#XTIT: title target spaces
targetSpaceTitle=Ruang ({0})
#XTIT: title connections
connectionsTitle=Sambungan ({0})

#XCOL: space business name
spaceBusinessName=Ruang
#XCOL: spaces business name
spacesBusinessName=Ruang
#XCOL: business name
businessName=Nama <PERSON>
#XCOL: technical name
technicalName=Nama Teknikal
#XCOL: modeling pattern
modelingPattern=Corak <PERSON>emodelan
#XCOL: import status
importStatus=Status Import
#XCOL: replication flow
replicationFlow=Aliran Replikasi
#XCOL: entity
entity=Entiti
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Entiti Dipilih
#XHED: heading selected entities
selectedApis=API Dipilih
#XHED: heading dependent entities
dependentEntities=Entiti Bersandar
#XHED: heading dependent entities
dependentEntitiesOf=Entiti Bersandar {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Entiti API {0} Dipilih
#XHED: heading entities of API
entitiesOfApi=Entiti API {0}
#XHED: heading business builder
businessBuilderObjects=Pembina Perniagaan ({0})
#XHED: heading data builder
dataBuilderObjects=Pembina Data ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Sebelumnya
#XBUT
buttonNextStep=Langkah Seterusnya
#XBUT
buttonImport=Mulakan Import dan Atur Duduk
#XBUT
buttonSchedule=Jadualkan Import dan Atur Duduk
#XBUT
buttonCancel=Batalkan
#XBUT
buttonContinue=Teruskan
#XMSG
loadConnectionTypesFailed=Muatkan jenis sambungan semula.
#XMSG
loadTargetSpacesFailed=Muatkan ruang sasaran semula.
#XMSG
loadConnectionsFailed=Muatkan sambungan semula.
#XMSG
loadBwBridgeConnectionFailed=Muatkan sambungan SAP BW bridge semula.
#XMSG
getConnectionStatusFailed=Cuba dapatkan semula status sambungan {0}.
#XMSG
getSharedConnectionDetailsFailed=Cuba dapatkan semula butiran sambungan untuk ID sistem {0}.
#XMSG
connectionNotValid=Masukkan sambungan {0} yang sah.
#XMSG
connectionBwBridgeNotValid=Masukkan sambungan SAP BW bridge {0} yang sah.
#XMSG
connectionBwBridgeNotAvailable=Masukkan sambungan SAP BW bridge.
#XMSG
loadEntitiesFailed=Muatkan entiti semula.
#XMSG
importEntitiesFailed=Import entiti semula.
#XMSG
importEntitiesStarted=Mengimport entiti. Semak pemberitahuan untuk status import.
#XMSG
loadReviewEntitiesListFailed=Muatkan objek untuk diimport semula.
#XMSG
selectOneResultListItem=Pilih satu item sahaja dalam senarai hasil carian.
#XMSG
dialogCancel=Adakah anda pasti ingin membatalkan import entiti?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=Sambungan {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=Label
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW Bridge

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Cipta Objek Pembina Perniagaan
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Entiti Perniagaan dan Model Penggunaan
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Entiti Perniagaan Sahaja
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Tiada

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Capaian Data
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Aliran Replikasi ke Jadual Tempatan
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Jadual Jauh
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Aliran Replikasi
#XFLD: label Ingestion Space
ingestionSpaceLabel=Ruang Pengingesan

#XMSG
modelTransferOk=Anda dayakan import model.
#XMSG
modelTransferNotOk=Tidak boleh gunakan import model.
#XMSG
hanaSdiOk=Jadual jauh didayakan.
#XMSG
hanaSdiNotOk=Tidak boleh gunakan jadual jauh.
#XMSG
replicationFlowOk=Aliran replikasi didayakan.
#XMSG
replicationFlowNotOk=Tidak boleh gunakan aliran replikasi.
#XMSG
onboardingOk=Onboarding jadual jauh dan aliran replikasi didayakan.
#XMSG
onboardingNotOk=Onboarding jadual jauh dan aliran replikasi tidak boleh dilakukan.
#XMSG
enableReplicationFlow=Sambungan ini boleh menyokong replikasi data melalui aliran replikasi, tetapi ciri ini tidak didayakan pada masa ini. Sila hubungi pentadbir anda untuk mengemas kini sambungan. Sementara itu anda hanya boleh mengimport entiti dengan capaian data bersekutu melalui jadual jauh.
#XMSG
selectedApiWithoutEntities=API dipilih tidak menyediakan apa-apa entiti.
#XMSG
switchReplicationFlowToRemoteTables=Pada masa ini, objek ini dipasang sebagai jadual tempatan dengan data yang direplikakan kepada mereka. Mengubah kepada persekutuan mungkin merendahkan prestasi. Perubahan ini akan memberi kesan kepada semua ruang yang menggunakan objek ini.\n\nAdakah anda ingin mengubah capaian data kepada persekutuan melalui jadual jauh?
#XMSG
switchRemoteTablesToReplicationFlow=Pada masa ini, objek ini dipasang sebagai jadual jauh dengan data bersekutu kepada mereka. Mengubah kepada replikasi akan meningkatkan prestasi tetapi juga akan melumpuhkan capaian langsung supaya kesegaran data anda bergantung pada jadual replikasi anda. Perubahan ini akan memberi kesan kepada semua ruang yang menggunakan objek ini.\n\nAdakah anda ingin mengubah capaian data kepada replikasi melalui aliran replikasi dan jadual tempatan?

#XFLD
spaceTypeAbapBridge=SAP BW Bridge
#XFLD
spaceTypeDatasphere=SAP Datasphere
