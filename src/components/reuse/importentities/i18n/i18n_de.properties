#XTIT: Title for import entities
importEntitiesTitle=Entitäten importieren
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Verbindungstyp auswählen
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Ziel-Space auswählen
#XFLD: import wizard step 3: select connection
selectConnectionStep=Verbindung auswählen
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Entitäten auswählen
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Entitäten überprüfen
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Verbindung/Ziel-Space auswählen

#XTIT: title connection types
connectionTypesTitle=Elemente ({0})
#XTIT: title target spaces
targetSpaceTitle=Spaces {0}
#XTIT: title connections
connectionsTitle=Verbindungen ({0})

#XCOL: space business name
spaceBusinessName=Space
#XCOL: spaces business name
spacesBusinessName=Spaces
#XCOL: business name
businessName=Betriebswirtschaftlicher Name
#XCOL: technical name
technicalName=Technischer Name
#XCOL: modeling pattern
modelingPattern=Modellierungsmuster
#XCOL: import status
importStatus=Importstatus
#XCOL: replication flow
replicationFlow=Replikationsfluss
#XCOL: entity
entity=Entität
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Ausgewählte Entitäten
#XHED: heading selected entities
selectedApis=Ausgewählte APIs
#XHED: heading dependent entities
dependentEntities=Abhängige Entitäten
#XHED: heading dependent entities
dependentEntitiesOf=Abhängige Entitäten von {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Ausgewählte Entitäten von API {0}
#XHED: heading entities of API
entitiesOfApi=Entitäten von API {0}
#XHED: heading business builder
businessBuilderObjects=Business Builder ({0})
#XHED: heading data builder
dataBuilderObjects=Data Builder ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Zurück
#XBUT
buttonNextStep=Nächster Schritt
#XBUT
buttonImport="Importieren und aktivieren" starten
#XBUT
buttonSchedule="Importieren und aktivieren" einplanen
#XBUT
buttonCancel=Abbrechen
#XBUT
buttonContinue=Fortfahren
#XMSG
loadConnectionTypesFailed=Verbindungstypen können nicht geladen werden.
#XMSG
loadTargetSpacesFailed=Ziel-Spaces können nicht geladen werden.
#XMSG
loadConnectionsFailed=Verbindungen können nicht geladen werden.
#XMSG
loadBwBridgeConnectionFailed=SAP-BW-Bridge-Verbindung kann nicht geladen werden.
#XMSG
getConnectionStatusFailed=Status der Verbindung {0} kann nicht abgerufen werden.
#XMSG
getSharedConnectionDetailsFailed=Details der Verbindung für System-ID {0} können nicht abgerufen werden.
#XMSG
connectionNotValid=Verbindung {0} ist keine gültige Verbindung.
#XMSG
connectionBwBridgeNotValid=SAP-BW-Bridge-Verbindung {0} ist keine gültige Verbindung.
#XMSG
connectionBwBridgeNotAvailable=SAP-BW-Bridge-Verbindung ist nicht verfügbar.
#XMSG
loadEntitiesFailed=Entitäten können nicht geladen werden.
#XMSG
importEntitiesFailed=Entitäten können nicht importiert werden.
#XMSG
importEntitiesStarted=Entitäten werden importiert. Überprüfen Sie den Status des Imports in den Benachrichtigungen.
#XMSG
loadReviewEntitiesListFailed=Objekte für den Import können nicht geladen werden.
#XMSG
selectOneResultListItem=Wählen Sie nur ein Element in der Suchergebnisliste aus
#XMSG
dialogCancel=Möchten Sie den Import der Entitäten wirklich abbrechen?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=Verbindung {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=Bezeichner
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP-BW-Bridge

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Business-Builder-Objekte anlegen
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Geschäftsentitäten und Verwendungsmodelle
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Nur Geschäftsentitäten
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Keine

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Datenzugriff
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Replikationsfluss zu lokalen Tabellen
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Remote-Tabellen
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Replikationsfluss
#XFLD: label Ingestion Space
ingestionSpaceLabel=Aufnahme-Space

#XMSG
modelTransferOk=Modellimport ist aktiviert.
#XMSG
modelTransferNotOk=Modellimport kann nicht verwendet werden.
#XMSG
hanaSdiOk=Remote-Tabellen sind aktiviert.
#XMSG
hanaSdiNotOk=Remote-Tabellen können nicht verwendet werden.
#XMSG
replicationFlowOk=Replikationsflüsse sind aktiviert.
#XMSG
replicationFlowNotOk=Replikationsflüsse können nicht verwendet werden
#XMSG
onboardingOk=Onboarding von Remote-Tabellen und Replikationsflüssen ist aktiviert.
#XMSG
onboardingNotOk=Onboarding von Remote-Tabellen und Replikationsflüssen ist nicht möglich.
#XMSG
enableReplicationFlow=Diese Verbindung kann die Replikation von Daten über einen Replikationsfluss unterstützen, diese Funktion ist jedoch derzeit nicht aktiviert. Wenden Sie sich an Ihren Administrator, damit er die Verbindung aktualisiert. Bis dahin können Sie nur Entitäten mit föderiertem Datenzugriff über Remote-Tabellen importieren.
#XMSG
selectedApiWithoutEntities=Durch das ausgewählte API werden keine Entitäten bereitgestellt.
#XMSG
switchReplicationFlowToRemoteTables=Diese Objekte sind derzeit als lokale Tabellen installiert, in die Daten repliziert werden. Durch das Ändern in eine Föderation kann die Performance beeinträchtigt werden. Diese Änderung wirkt sich auf alle Spaces aus, die diese Objekte verwenden.\n\nMöchten Sie den Datenzugriff in eine Föderation über Remote-Tabellen ändern?
#XMSG
switchRemoteTablesToReplicationFlow=Diese Objekte sind derzeit als Remote-Tabellen installiert, in die Daten föderiert werden. Durch das Ändern in eine Replikation wird zwar die Performance verbessert auber auch der Live-Zugriff deaktiviert, sodass die Aktualität Ihrer Daten von Ihrem Replikationszeitplan abhängt. Diese Änderung wirkt sich auf alle Spaces aus, die diese Objekte verwenden.\n\nMöchten Sie den Datenzugriff in die Replikation über einen Replikationsfluss und lokale Tabellen ändern?

#XFLD
spaceTypeAbapBridge=SAP-BW-Bridge
#XFLD
spaceTypeDatasphere=SAP Datasphere
