#XTIT: Title for import entities
importEntitiesTitle=Εισαγωγή Οντοτήτων
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Ειπλογή Τύπου Σύνδεσης
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Επιλογή <PERSON>ώρου Στόχου
#XFLD: import wizard step 3: select connection
selectConnectionStep=Επιλογή Σύνδεσης
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Επιλογή Οντοτήτων
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Ελεγχος Οντοτήτων
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Επιλογή Σύνδεσης / Χώρου Στόχου

#XTIT: title connection types
connectionTypesTitle=Στοιχεία ({0})
#XTIT: title target spaces
targetSpaceTitle=Χώροι ({0})
#XTIT: title connections
connectionsTitle=Συνδέσεις ({0})

#XCOL: space business name
spaceBusinessName=Χώρος
#XCOL: spaces business name
spacesBusinessName=Χώροι
#XCOL: business name
businessName=Επωνυμία Επιχείρησης
#XCOL: technical name
technicalName=Τεχνικό Ονομα
#XCOL: modeling pattern
modelingPattern=Πρότυπο Μοντελοποίησης
#XCOL: import status
importStatus=Εισαγωγή Κατάστασης
#XCOL: replication flow
replicationFlow=Ροή Αναπαραγωγής
#XCOL: entity
entity=Οντότητα
#XCOL: api
api=ΑΡΙ
#XHED: heading selected entities
selectedEntities=Επιλεγμένες Οντότητες
#XHED: heading selected entities
selectedApis=Επιλεγμένα ΑΡΙ
#XHED: heading dependent entities
dependentEntities=Εξαρτημένες Οντότητες
#XHED: heading dependent entities
dependentEntitiesOf=Εξαρτημένες Οντότητες {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Επιλεγμένες Οντότητες ΑΡΙ {0}
#XHED: heading entities of API
entitiesOfApi=Οντότητες API {0}
#XHED: heading business builder
businessBuilderObjects=Δημιουργός Επιχείρησης ({0})
#XHED: heading data builder
dataBuilderObjects=Δημιουργός Δεδομένων ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Προηγούμενο
#XBUT
buttonNextStep=Επόμ.Βήμα
#XBUT
buttonImport=Εναρξη Εισαγωγής και Ανάπτυξης
#XBUT
buttonSchedule=Προγραμματισμός Εισαγωγής και Ανάπτυξης
#XBUT
buttonCancel=Ακύρωση
#XBUT
buttonContinue=Συνέχεια
#XMSG
loadConnectionTypesFailed=Φόρτωση τύπων σύνδεσης απέτυχε.
#XMSG
loadTargetSpacesFailed=Φόρτωση χώρων στόχου απέτυχε.
#XMSG
loadConnectionsFailed=Αποτυχία φόρτωσης συνδέσεων.
#XMSG
loadBwBridgeConnectionFailed=Αδύνατη φόρτωση σύνδεσης SAP BW bridge.
#XMSG
getConnectionStatusFailed=Η ανάκτηση κατάστασης σύνδεσης {0}απέτυχε.
#XMSG
getSharedConnectionDetailsFailed=Αδύνατη ανάκτηση λεπτομερειών σύνδεσης για ID σύνδεσης {0}.
#XMSG
connectionNotValid=Σύνδεση {0} δεν είναι έγκυρη σύνδεση.
#XMSG
connectionBwBridgeNotValid=Η σύνδεση SAP BW bridge {0} δεν είναι έγκυρη σύνδεση.
#XMSG
connectionBwBridgeNotAvailable=Σύνδεση SAP BW bridge  μη διαθέσιμη.
#XMSG
loadEntitiesFailed=Αποτυχία φόρτωσης οντοτήτων.
#XMSG
importEntitiesFailed=Αποτυχία εισαγωγής οντοτήτων.
#XMSG
importEntitiesStarted=Εισαγωγή οντοτήτων άρχισε. Ελέγξτε τις ειδοποιήσεις για την κατάσταση της εισαγωγής.
#XMSG
loadReviewEntitiesListFailed=Φόρτωση αντικειμένων που θα εισαχθούν.
#XMSG
selectOneResultListItem=Επιλέξτε μόνο ένα στοιχείο στη λίστα αποτελεσμάτων αναζήτησης.
#XMSG
dialogCancel=Θέλετε να ακυρώσετε την εισαγωγή οντοτήτων;
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=Σύνδεση {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=Ετικέτα
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW Bridge

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Δημιουργία Αντικειμένων Δημιουργού Επιχείρησης
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Επιχειρηματικές Οντότητες και Μοντέλα Ανάλωσης
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Επιχειρηματικές Οντότητες Μόνο
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Κανένα

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Πρόσβαση Δεδομένων
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Ροή Αντιγραφής σε Τοπικούς Πίνακες
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Απομακρυσμένοι Πίνακες
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Ροή Αντιγραφής
#XFLD: label Ingestion Space
ingestionSpaceLabel=Ingestion Space

#XMSG
modelTransferOk=Εισαγωγή μοντέλου ενεργοποιήθηκε.
#XMSG
modelTransferNotOk=Αδύνατη χρήση εισαγωγής μοντέλου.
#XMSG
hanaSdiOk=Οι απομακρυσμένοι πίνακες είναι ενεργοί.
#XMSG
hanaSdiNotOk=Αδύνατη χρήση απομακρυσμένων πινάκων.
#XMSG
replicationFlowOk=Ροές αντιγραφής ενεργές.
#XMSG
replicationFlowNotOk=Αδύνατη χρήση ροών αντιγραφής.
#XMSG
onboardingOk=Η ενσωμάτωση απομακρυσμένων πινάκων και ροών αντιγραφής είναι ενεργή.
#XMSG
onboardingNotOk=Η ενσωμάτωση απομακρυσμένων πινάκων και ροών αντιγραφής είναι ενεργή.
#XMSG
enableReplicationFlow=Αυτή η σύνδεση μπορεί να υποστηρίξει την αντιγραφή δεδομένων μέσω ροής αντιγραφής, αλλά αυτή η ιδιότητα δεν είναι ενεργή. Επικοινωνήστε με τον διαχειριστή σας ώστε να ενημερώσει τη σύνδεση. Εσείς ωστόσο μπορείτε να εισάγετε οντότητες μόνο με ενιαία πρόσβαση δεδομένων μέσω απομακρυσμένων πινάκων.
#XMSG
selectedApiWithoutEntities=Το επιλεγμένο ΑΡΙ δεν παρέχει οντότητες.
#XMSG
switchReplicationFlowToRemoteTables=Αυτά τα αντικείμενα είναι εγκατεστημένα ως τοπικοί πίνακες με δεδομένα αντιγραμμένα σε αυτά. Αν αλλάξετε την προσάρτηση ενδέχεται να επιβαρυνθεί η απόδοση. Αυτή η αλλαγή θα επηρεάσει όλους τους χώρους που χρησιμοποιούν αυτά τα αντικείμενα.\n\nΘέλετε να αλλάξετε την πρόσβαση δεδομένων σε προσάρτηση μέσω απομακρυσμένων πινάκων;
#XMSG
switchRemoteTablesToReplicationFlow=Αυτά τα αντικείμενα είναι εγκατεστημένα ως απομακρυσμένοι πίνακες με δεδομένα προσαρτημένα σε αυτά. Η αλλαγή της αντιγραφής θα βελτιώσει την απόδοση αλλά θα απενεργοποιήσει την ζωντανή πρόσβαση έτσι ώστε η ενημέρωση των δεδομένων σας να εξαρτάται από το πρόγραμμα αντιγραφής. Αυτή η αλλαγή θα επηρεάσει όλους τους χώρους που χρησιμοποιούν αυτά τα αντικείμενα.\n\nΘέλετε να αλλάξετε την πρόσβαση δεδομένων σε αντιγραφή μέσω μίας ροής αντιγραφής και τοπικών πινάκων;

#XFLD
spaceTypeAbapBridge=SAP BW Bridge
#XFLD
spaceTypeDatasphere=SAP Datasphere
