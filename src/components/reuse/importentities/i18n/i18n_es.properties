#XTIT: Title for import entities
importEntitiesTitle=Importar entidades
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Seleccionar tipo de conexión
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Seleccionar espacio de destino
#XFLD: import wizard step 3: select connection
selectConnectionStep=Seleccionar conexión
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Seleccionar entidades
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Revisar entidades
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Seleccionar conexión/espacio de destino

#XTIT: title connection types
connectionTypesTitle=Posiciones ({0})
#XTIT: title target spaces
targetSpaceTitle=Espacios ({0})
#XTIT: title connections
connectionsTitle=Conexiones ({0})

#XCOL: space business name
spaceBusinessName=Espacio
#XCOL: spaces business name
spacesBusinessName=Espacios
#XCOL: business name
businessName=Nombre empresarial
#XCOL: technical name
technicalName=Nombre técnico
#XCOL: modeling pattern
modelingPattern=Patrón de modelado
#XCOL: import status
importStatus=Estado de importación
#XCOL: replication flow
replicationFlow=Flujo de replicación
#XCOL: entity
entity=Entidad
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Entidades seleccionadas
#XHED: heading selected entities
selectedApis=API seleccionadas
#XHED: heading dependent entities
dependentEntities=Entidades dependientes
#XHED: heading dependent entities
dependentEntitiesOf=Entidades dependientes de {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Entidades seleccionadas de la API {0}
#XHED: heading entities of API
entitiesOfApi=Entidades de API {0}
#XHED: heading business builder
businessBuilderObjects=Generador empresarial ({0})
#XHED: heading data builder
dataBuilderObjects=Generador de datos ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Anterior
#XBUT
buttonNextStep=Paso siguiente
#XBUT
buttonImport=Iniciar importación y despliegue
#XBUT
buttonSchedule=Desplegar importación y despliegue
#XBUT
buttonCancel=Cancelar
#XBUT
buttonContinue=Continuar
#XMSG
loadConnectionTypesFailed=No es posible cargar los tipos de conexión.
#XMSG
loadTargetSpacesFailed=No es posible cargar los espacios de destino.
#XMSG
loadConnectionsFailed=No es posible cargar las conexiones.
#XMSG
loadBwBridgeConnectionFailed=No es posible cargar la conexión del puente de SAP BW.
#XMSG
getConnectionStatusFailed=No es posible recuperar el estado de la conexión {0}.
#XMSG
getSharedConnectionDetailsFailed=No se pueden recuperar los detalles de la conexión para el ID de sistema {0}.
#XMSG
connectionNotValid=La conexión {0} no es válida.
#XMSG
connectionBwBridgeNotValid=La conexión del puente de SAP BW {0} no es válida.
#XMSG
connectionBwBridgeNotAvailable=La conexión del puente de SAP BW no está disponible.
#XMSG
loadEntitiesFailed=No es posible cargar las entidades.
#XMSG
importEntitiesFailed=No es posible importar las entidades.
#XMSG
importEntitiesStarted=Se están importando las entidades. Consulte en las notificaciones el estado de la importación.
#XMSG
loadReviewEntitiesListFailed=No es posible cargar los objetos para importar.
#XMSG
selectOneResultListItem=Seleccione solo una posición en la lista de resultados de la búsqueda.
#XMSG
dialogCancel=¿Seguro que desea cancelar la importación de entidades?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=Conexión {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=Etiqueta
#XTXT: SAP BW Bridge Connection type
sapBwBridge=Puente de SAP BW

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Crear objetos del generador empresarial
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Entidades empresariales y modelos de consumo
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Solo entidades empresariales
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Ninguno

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Acceso a datos
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Flujo de replicación a tablas locales
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Tablas remotas
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Flujo de replicación
#XFLD: label Ingestion Space
ingestionSpaceLabel=Espacio de ingesta

#XMSG
modelTransferOk=La importación de modelos está activada.
#XMSG
modelTransferNotOk=La importación de modelos no se puede utilizar.
#XMSG
hanaSdiOk=Las tablas remotas están activadas.
#XMSG
hanaSdiNotOk=Las tablas remotas no se pueden utilizar.
#XMSG
replicationFlowOk=Los flujos de replicación están activados.
#XMSG
replicationFlowNotOk=Los flujos de replicación no se pueden utilizar.
#XMSG
onboardingOk=La incorporación de tablas remotas y flujos de replicación está activada.
#XMSG
onboardingNotOk=La incorporación de tablas remotas y flujos de replicación no es posible.
#XMSG
enableReplicationFlow=Esta conexión puede admitir la replicación de datos mediante un flujo de replicación, pero esta función no está activada en estos momentos. Póngase en contacto con el administrador para actualizar la conexión. Mientras tanto, solo puede importar entidades con acceso a datos federados mediante tablas remotas.
#XMSG
selectedApiWithoutEntities=La API seleccionada no proporciona ninguna entidad.
#XMSG
switchReplicationFlowToRemoteTables=Estos objetos están instalados como tablas locales con datos replicados en ellas. Un cambio a la federación puede reducir el rendimiento. Este cambio puede afectar a todos los espacios que consuman estos objetos.\n\n¿Desea modificar el acceso de datos a la federación mediante tablas remotas?
#XMSG
switchRemoteTablesToReplicationFlow=Estos objetos están instalados como tablas remotas con datos federados en ellos. Un cambio a la replicación mejorará el rendimiento, pero también desactivará el acceso en tiempo real, de modo que la disponibilidad de los datos dependerá de la programación de la replicación. Este cambio afectará a todos los espacios que consuman estos objetos.\n\n¿Desea modificar el acceso de datos a la replicación mediante un flujo de replicación y tablas locales?

#XFLD
spaceTypeAbapBridge=Puente de SAP BW
#XFLD
spaceTypeDatasphere=SAP Datasphere
