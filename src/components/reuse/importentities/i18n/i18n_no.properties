#XTIT: Title for import entities
importEntitiesTitle=Importer entiteter
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Velg forbindelsestype
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Velg målrom
#XFLD: import wizard step 3: select connection
selectConnectionStep=Velg forbindelse
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Velg entiteter
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Kontroller entiteter
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Velg forbindelse/målrom

#XTIT: title connection types
connectionTypesTitle=Posisjoner ({0})
#XTIT: title target spaces
targetSpaceTitle=Rom ({0})
#XTIT: title connections
connectionsTitle=Forbindelser ({0})

#XCOL: space business name
spaceBusinessName=Rom
#XCOL: spaces business name
spacesBusinessName=Rom
#XCOL: business name
businessName=Forretningsnavn
#XCOL: technical name
technicalName=Teknisk navn
#XCOL: modeling pattern
modelingPattern=Modelleringsmønster
#XCOL: import status
importStatus=Importstatus
#XCOL: replication flow
replicationFlow=Replikeringsflyt
#XCOL: entity
entity=Entitet
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Valgte entiteter
#XHED: heading selected entities
selectedApis=Valgte API-er
#XHED: heading dependent entities
dependentEntities=Avhengige entiteter
#XHED: heading dependent entities
dependentEntitiesOf=Avhengige entiteter for {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Valgte entiteter for API-et {0}
#XHED: heading entities of API
entitiesOfApi=Entiteter for API {0}
#XHED: heading business builder
businessBuilderObjects=Forretningsbygger ({0})
#XHED: heading data builder
dataBuilderObjects=Databygger ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Forrige
#XBUT
buttonNextStep=Neste trinn
#XBUT
buttonImport=Start import og distribuer
#XBUT
buttonSchedule=Planlegg import og distribuer
#XBUT
buttonCancel=Avbryt
#XBUT
buttonContinue=Fortsett
#XMSG
loadConnectionTypesFailed=Kan ikke laste forbindelsestyper.
#XMSG
loadTargetSpacesFailed=Kan ikke laste målrom.
#XMSG
loadConnectionsFailed=Kan ikke laste forbindelser.
#XMSG
loadBwBridgeConnectionFailed=Kan ikke laste SAP BW-broforbindelse.
#XMSG
getConnectionStatusFailed=Kan ikke hente status for forbindelsen {0}.
#XMSG
getSharedConnectionDetailsFailed=Kan ikke hente forbindelsesdetaljer for system-ID {0}.
#XMSG
connectionNotValid=Forbindelsen {0} er ikke en gyldig forbindelse.
#XMSG
connectionBwBridgeNotValid=SAP BW-broforbindelsen {0} er ikke en gyldig forbindelse.
#XMSG
connectionBwBridgeNotAvailable=SAP BW-broforbindelsen er ikke tilgjengelig.
#XMSG
loadEntitiesFailed=Kan ikke laste entiteter.
#XMSG
importEntitiesFailed=Kan ikke importere entiteter.
#XMSG
importEntitiesStarted=Importer entiteter. Kontroller varslingene for å se statusen for importen.
#XMSG
loadReviewEntitiesListFailed=Kan ikke laste objekter som skal importeres.
#XMSG
selectOneResultListItem=Velg bare ett element i søkeresultatlisten.
#XMSG
dialogCancel=Er du sikker på at du vil avbryte importen av entitetene?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=Forbindelse {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=Etikett
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW-bro

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Opprett forretningsbyggerobjekter
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Forretningsentiteter og forbruksmodeller
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Bare forretningsentiteter
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Ingen

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Datatilgang
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Replikeringsflyt til lokale tabeller
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Fjerntabeller
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Replikeringsflyt
#XFLD: label Ingestion Space
ingestionSpaceLabel=Inntaksrom

#XMSG
modelTransferOk=Modellimport er aktivert.
#XMSG
modelTransferNotOk=Modellimport kan ikke brukes.
#XMSG
hanaSdiOk=Fjerntabeller er aktivert.
#XMSG
hanaSdiNotOk=Fjerntabeller kan ikke brukes.
#XMSG
replicationFlowOk=Replikeringsflyter er aktivert.
#XMSG
replicationFlowNotOk=Replikeringsflyter kan ikke brukes.
#XMSG
onboardingOk=Pålasting av fjerntabeller og replikeringsflyter er aktivert.
#XMSG
onboardingNotOk=Pålasting av fjerntabeller og replikeringsflyter er ikke mulig.
#XMSG
enableReplicationFlow=Denne forbindelsen kan støtte replikeringen av data via en replikeringsflyt, men funksjonen er ikke aktivert for øyeblikket. Kontakt administrator for å oppdatere forbindelsen. I mellomtiden kan du bare importere entiteter med samlet datatilgang via fjerntabeller.
#XMSG
selectedApiWithoutEntities=Valgt API leverer ingen entiteter.
#XMSG
switchReplicationFlowToRemoteTables=Disse objektene installeres som lokale tabeller med data som replikeres til tabellene. Hvis du endrer til føderering, kan ytelsen forverres. Denne endringen vil påvirke alle rom som forbruker disse objektene.\n\nVil du endre datatilgangen til føderering via fjerntabeller?
#XMSG
switchRemoteTablesToReplicationFlow=Disse objektene installeres som lokale tabeller med data som fødereres til tabellene. Hvis du endrer til replikering, forbedres ytelsen. Live tilgang blir imidlertid deaktivert og hvor oppdaterte dataene dine er, avhenger av replikeringsplanen din. Denne endringen vil påvirke alle rom som forbruker disse objektene.\n\nVil du endre datatilgangen til replikering via en replikeringsflyt og lokale tabeller?

#XFLD
spaceTypeAbapBridge=SAP BW-bro
#XFLD
spaceTypeDatasphere=SAP Datasphere
