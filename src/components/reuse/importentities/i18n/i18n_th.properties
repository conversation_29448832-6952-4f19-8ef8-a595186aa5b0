#XTIT: Title for import entities
importEntitiesTitle=อิมปอร์ตเอนทิตี้
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=เลือกประเภทการเชื่อมต่อ
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=เลือกพื้นที่เป้าหมาย
#XFLD: import wizard step 3: select connection
selectConnectionStep=เลือกการเชื่อมต่อ
#XFLD: import wizard step 4: select entities
selectEntitiesStep=เลือกเอนทิตี้
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=ตรวจทานเอนทิตี้
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=เลือกการเชื่อมต่อ/พื้นที่เป้าหมาย

#XTIT: title connection types
connectionTypesTitle=รายการ ({0})
#XTIT: title target spaces
targetSpaceTitle=พื้นที่ ({0})
#XTIT: title connections
connectionsTitle=การเชื่อมต่อ ({0})

#XCOL: space business name
spaceBusinessName=พื้นที่
#XCOL: spaces business name
spacesBusinessName=พื้นที่
#XCOL: business name
businessName=ชื่อทางธุรกิจ
#XCOL: technical name
technicalName=ชื่อทางเทคนิค
#XCOL: modeling pattern
modelingPattern=รูปแบบการจัดทำโมเดล
#XCOL: import status
importStatus=สถานะการอิมปอร์ต
#XCOL: replication flow
replicationFlow=ผังการทำสำเนา
#XCOL: entity
entity=เอนทิตี้
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=เอนทิตี้ที่เลือก
#XHED: heading selected entities
selectedApis=API ที่เลือก
#XHED: heading dependent entities
dependentEntities=เอนทิตี้ที่สัมพันธ์กัน
#XHED: heading dependent entities
dependentEntitiesOf=เอนทิตี้ที่สัมพันธ์กันของ {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=เอนทิตี้ที่เลือกของ API {0}
#XHED: heading entities of API
entitiesOfApi=เอนทิตี้ของ API {0}
#XHED: heading business builder
businessBuilderObjects=ตัวสร้างธุรกิจ ({0})
#XHED: heading data builder
dataBuilderObjects=ตัวสร้างข้อมูล ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=ก่อนหน้า
#XBUT
buttonNextStep=ขั้นตอนถัดไป
#XBUT
buttonImport=เริ่มอิมปอร์ตและปรับใช้
#XBUT
buttonSchedule=จัดกำหนดการอิมปอร์ตและปรับใช้
#XBUT
buttonCancel=ยกเลิก
#XBUT
buttonContinue=ดำเนินการต่อ
#XMSG
loadConnectionTypesFailed=ไม่สามารถโหลดประเภทการเชื่อมต่อ
#XMSG
loadTargetSpacesFailed=ไม่สามารถโหลดพื้นที่เป้าหมาย
#XMSG
loadConnectionsFailed=ไม่สามารถโหลดการเชื่อมต่อ
#XMSG
loadBwBridgeConnectionFailed=ไม่สามารถโหลดการเชื่อมต่อแบบบริดจ์ของ SAP BW
#XMSG
getConnectionStatusFailed=ไม่สามารถดึงข้อมูลสถานะการเชื่อมต่อ {0}
#XMSG
getSharedConnectionDetailsFailed=ไม่สามารถดึงข้อมูลรายละเอียดการเชื่อมต่อสำหรับ ID ระบบ {0}
#XMSG
connectionNotValid=การเชื่อมต่อ {0} ไม่ใช่การเชื่อมต่อที่ถูกต้อง
#XMSG
connectionBwBridgeNotValid=การเชื่อมต่อแบบบริดจ์ของ SAP BW {0} ไม่ใช่การเชื่อมต่อที่ถูกต้อง
#XMSG
connectionBwBridgeNotAvailable=การเชื่อมต่อแบบบริดจ์ของ SAP BW ไม่พร้อมใช้งาน
#XMSG
loadEntitiesFailed=ไม่สามารถโหลดเอนทิตี้
#XMSG
importEntitiesFailed=ไม่สามารถอิมปอร์ตเอนทิตี้
#XMSG
importEntitiesStarted=กำลังอิมปอร์ตเอนทิตี้ กรุณาตรวจสอบการแจ้งสำหรับสถานะการอิมปอร์ต
#XMSG
loadReviewEntitiesListFailed=ไม่สามารถโหลดออบเจคที่จะอิมปอร์ต
#XMSG
selectOneResultListItem=เลือกเพียงหนึ่งรายการในรายการผลการค้นหา
#XMSG
dialogCancel=คุณแน่ใจหรือไม่ว่าต้องการยกเลิกการอิมปอร์ตเอนทิตี้?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=การเชื่อมต่อ {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=ป้ายชื่อ
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW Bridge

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=สร้างออบเจคตัวสร้างธุรกิจ
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=เอนทิตี้ธุรกิจและโมเดลการใช้
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=เอนทิตี้ธุรกิจเท่านั้น
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=ไม่มี

#XFLD: label Remote Access Mode
remoteAccessModeLabel=การเข้าถึงข้อมูล
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=ผังการทำสำเนาไปยังตารางภายใน
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=ตารางระยะไกล
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=ผังการทำสำเนา
#XFLD: label Ingestion Space
ingestionSpaceLabel=พื้นที่นำข้อมูลเข้า

#XMSG
modelTransferOk=เปิดใช้งานการอิมปอร์ตโมเดลแล้ว
#XMSG
modelTransferNotOk=ไม่สามารถใช้การอิมปอร์ตโมเดล
#XMSG
hanaSdiOk=เปิดใช้งานตารางระยะไกลแล้ว
#XMSG
hanaSdiNotOk=ไม่สามารถใช้ตารางระยะไกล
#XMSG
replicationFlowOk=เปิดใช้งานผังการทำสำเนาแล้ว
#XMSG
replicationFlowNotOk=ไม่สามารถใช้ผังการทำสำเนา
#XMSG
onboardingOk=เปิดใช้งานการจัดเตรียมระบบสำหรับตารางระยะไกลและผังการทำสำเนาแล้ว
#XMSG
onboardingNotOk=ไม่สามารถทำการจัดเตรียมระบบสำหรับตารางระยะไกลและผังการทำสำเนาได้
#XMSG
enableReplicationFlow=การเชื่อมต่อนี้สามารถรองรับการทำสำเนาข้อมูลผ่านผังการทำสำเนา แต่ฟีเจอร์นี้ไม่ได้เปิดใช้งานอยู่ในขณะนี้ กรุณาติดต่อผู้ดูแลระบบของคุณเพื่ออัพเดทการเชื่อมต่อ ในระหว่างนี้ คุณสามารถอิมปอร์ตได้เฉพาะเอนทิตี้ที่มีการเข้าถึงข้อมูลแบบรวมผ่านตารางระยะไกลเท่านั้น
#XMSG
selectedApiWithoutEntities=API ที่เลือกไม่มีเอนทิตี้ใดๆ
#XMSG
switchReplicationFlowToRemoteTables=ขณะนี้ออบเจคเหล่านี้ได้รับการติดตั้งเป็นตารางภายในที่มีข้อมูลที่ถูกทำสำเนาไปยังออบเจค การเปลี่ยนเป็นการรวมอาจทำให้ประสิทธิภาพลดลง การเปลี่ยนแปลงนี้จะมีผลกระทบต่อพื้นที่ทั้งหมดที่ใช้ออบเจคเหล่านี้\n\nคุณต้องการเปลี่ยนการเข้าถึงข้อมูลเป็นการรวมผ่านตารางระยะไกลหรือไม่?
#XMSG
switchRemoteTablesToReplicationFlow=ขณะนี้ออบเจคเหล่านี้ได้รับการติดตั้งเป็นตารางระยะไกลที่มีข้อมูลที่ถูกรวมไว้ในออบเจค การเปลี่ยนเป็นการทำสำเนาจะช่วยเพิ่มประสิทธิภาพแต่จะปิดใช้งาน Live Access ด้วย เพื่อให้ความสดใหม่ของข้อมูลของคุณ ทั้งนี้ขึ้นอยู่กับกำหนดการทำสำเนาของคุณ การเปลี่ยนแปลงนี้จะมีผลกระทบต่อพื้นที่ทั้งหมดที่ใช้ออบเจคเหล่านี้\n\nคุณต้องการเปลี่ยนการเข้าถึงข้อมูลเป็นการทำสำเนาผ่านผังการทำสำเนาและตารางภายในหรือไม่?

#XFLD
spaceTypeAbapBridge=บริดจ์ของ SAP BW
#XFLD
spaceTypeDatasphere=SAP Datasphere
