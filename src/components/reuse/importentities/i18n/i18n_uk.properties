#XTIT: Title for import entities
importEntitiesTitle=Імпортувати сутності
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Виберіть тип підключення
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Виберіть цільовий простір
#XFLD: import wizard step 3: select connection
selectConnectionStep=Вибір з’єднання
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Вибір сутності
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Перевірте сутності
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Виберіть з'єднання / цільовий простір

#XTIT: title connection types
connectionTypesTitle=Елементи ({0})
#XTIT: title target spaces
targetSpaceTitle=Простори ({0})
#XTIT: title connections
connectionsTitle=З’єднання ({0})

#XCOL: space business name
spaceBusinessName=Простір
#XCOL: spaces business name
spacesBusinessName=Простори
#XCOL: business name
businessName=Бізнес-ім’я
#XCOL: technical name
technicalName=Технічне ім’я
#XCOL: modeling pattern
modelingPattern=Зразок моделювання
#XCOL: import status
importStatus=Статус імпорту
#XCOL: replication flow
replicationFlow=Потік реплікації
#XCOL: entity
entity=Сутність
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Вибрані сутності
#XHED: heading selected entities
selectedApis=Вибрані інтерфейси API
#XHED: heading dependent entities
dependentEntities=Залежні сутності
#XHED: heading dependent entities
dependentEntitiesOf=Залежні сутності {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Вибрані сутності API {0}
#XHED: heading entities of API
entitiesOfApi=Сутності для API {0}
#XHED: heading business builder
businessBuilderObjects=Конструктор бізнесів ({0})
#XHED: heading data builder
dataBuilderObjects=Конструктор моделей даних ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Попередній
#XBUT
buttonNextStep=Наступний крок
#XBUT
buttonImport=Почніть імпорт і розгортання
#XBUT
buttonSchedule=Заплануйте імпорт і розгортання
#XBUT
buttonCancel=Скасувати
#XBUT
buttonContinue=Продовжити
#XMSG
loadConnectionTypesFailed=Не вдалося завантажити типи з'єднань.
#XMSG
loadTargetSpacesFailed=Не вдалося завантажити цільові простори.
#XMSG
loadConnectionsFailed=Не вдалося завантажити з'єднання.
#XMSG
loadBwBridgeConnectionFailed=Не вдалося завантажити з'єднання мосту SAP BW.
#XMSG
getConnectionStatusFailed=Не вдалося отримати статус з''єднання ''{0}''.
#XMSG
getSharedConnectionDetailsFailed=Не вдалося отримати подробиці з''єднання для ідентифікатора системи {0}.
#XMSG
connectionNotValid=З''єднання ''{0}'' недійсне.
#XMSG
connectionBwBridgeNotValid=З''єднання мосту SAP BW ''{0}'' недійсне.
#XMSG
connectionBwBridgeNotAvailable=З'єднання мосту SAP BW недоступне.
#XMSG
loadEntitiesFailed=Не вдалося завантажити сутності.
#XMSG
importEntitiesFailed=Не вдалось імпортувати сутності.
#XMSG
importEntitiesStarted=Триває імпорт сутностей. Перевірте сповіщення щодо статусу імпорту.
#XMSG
loadReviewEntitiesListFailed=Не вдалося завантажити об'єкти для імпорту.
#XMSG
selectOneResultListItem=Виберіть лише один елемент у списку результатів пошуку.
#XMSG
dialogCancel=Справді скасувати імпорт сутностей?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ІД
identifier_plural=З’єднання {0}
identifier_identifier=ІД
identifier_entityname=ІД
identifier_entitylabel=Надпис
#XTXT: SAP BW Bridge Connection type
sapBwBridge=Міст SAP BW

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Створення об'єктів Конструктора бізнесів
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Бізнес-сутності та моделі споживання
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Тільки бізнес-сутності
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Немає

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Доступ до даних
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Потік реплікації до локальних таблиць
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Віддалені таблиці
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Потік реплікації
#XFLD: label Ingestion Space
ingestionSpaceLabel=Простір проковтування

#XMSG
modelTransferOk=Імпорт моделі активовано.
#XMSG
modelTransferNotOk=Імпорт моделі використовувати не можна.
#XMSG
hanaSdiOk=Віддалені таблиці активовано.
#XMSG
hanaSdiNotOk=Віддалені таблиці використовувати не можна.
#XMSG
replicationFlowOk=Потоки реплікації активовано.
#XMSG
replicationFlowNotOk=Потоки реплікації використовувати не можна.
#XMSG
onboardingOk=Активовано інтеграцію віддалених таблиць і потоків реплікації.
#XMSG
onboardingNotOk=Інтеграція віддалених таблиць і потоків реплікації неможлива.
#XMSG
enableReplicationFlow=Це з'єднання може підтримувати реплікацію даних за допомогою потоку реплікації, але наразі цю функцію не активовано. Зверніться до свого адміністратора з проханням оновити з'єднання. Тим часом, ви можете тільки імпортувати записи з об'єднаним доступом до даних за допомогою віддалених таблиць.
#XMSG
selectedApiWithoutEntities=Вибраний інтерфейс API не надає жодної сутності.
#XMSG
switchReplicationFlowToRemoteTables=Наразі ці об'єкти інстальовано як локальні таблиці, у які репліковано дані. Перехід на федерацію може призвести до погіршення продуктивності. Ця зміна вплине на всі простори, які споживають такі об'єкти.\n\nСправді змінити спосіб доступу до даних на федерацію через віддалені таблиці?
#XMSG
switchRemoteTablesToReplicationFlow=Наразі ці об'єкти інстальовано як віддалені таблиці, у яких здійснюється федерація даних. Перехід на реплікацію сприятиме покращенню продуктивності, але водночас унеможливить активний доступ у реальному часі й оновлення даних залежатиме від розкладу реплікації. Ця зміна вплине на всі простори, які споживають такі об'єкти.\n\nСправді змінити спосіб доступу до даних на реплікацію через потік реплікації та локальні таблиці?

#XFLD
spaceTypeAbapBridge=Міст SAP BW
#XFLD
spaceTypeDatasphere=SAP Datasphere
