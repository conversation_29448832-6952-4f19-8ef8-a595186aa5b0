#XTIT: Title for import entities
importEntitiesTitle=Importuoti subjektus
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Pasirinkti ryšio tipą
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Pasirinkti tikslinę sritį
#XFLD: import wizard step 3: select connection
selectConnectionStep=Pasirinkti ryšį
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Pasirinkti subjektus
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Peržiūrėti subjektus
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Pasirinkti ryšį / tikslinę sritį

#XTIT: title connection types
connectionTypesTitle=Pozicijos ({0})
#XTIT: title target spaces
targetSpaceTitle=Sritys ({0})
#XTIT: title connections
connectionsTitle=Ryšiai ({0})

#XCOL: space business name
spaceBusinessName=Sritis
#XCOL: spaces business name
spacesBusinessName=Vietos
#XCOL: business name
businessName=Verslo pavadinimas
#XCOL: technical name
technicalName=Techninis pavadinimas
#XCOL: modeling pattern
modelingPattern=Modeliavimo šablonas
#XCOL: import status
importStatus=Importavimo būsena
#XCOL: replication flow
replicationFlow=Replikavimo srautas
#XCOL: entity
entity=Subjektas
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Pasirinkti subjektai
#XHED: heading selected entities
selectedApis=Pasirinkti API
#XHED: heading dependent entities
dependentEntities=Priklausomi subjektai
#XHED: heading dependent entities
dependentEntitiesOf={0} priklausomi subjektai
#XHED: heading dependent entities
selectedEntitiesOfApi=Pasirinkti API {0} subjektai
#XHED: heading entities of API
entitiesOfApi=API {0} objektai
#XHED: heading business builder
businessBuilderObjects=Verslo daryklė ({0})
#XHED: heading data builder
dataBuilderObjects=Duomenų daryklė ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Ankstesnis
#XBUT
buttonNextStep=Kitas veiksmas
#XBUT
buttonImport=Pradėti importuoti ir diegti
#XBUT
buttonSchedule=Suplanuoti importavimą ir diegimą
#XBUT
buttonCancel=Atšaukti
#XBUT
buttonContinue=Tęsti
#XMSG
loadConnectionTypesFailed=Ryšių tipų įkelti nepavyko.
#XMSG
loadTargetSpacesFailed=Tikslinių sričių įkelti nepavyko.
#XMSG
loadConnectionsFailed=Ryšių įkelti nepavyko.
#XMSG
loadBwBridgeConnectionFailed=SAP BW tilto ryšio įkelti nepavyko.
#XMSG
getConnectionStatusFailed=Ryšio {0} būsenos gauti nepavyko.
#XMSG
getSharedConnectionDetailsFailed=Nepavyksta nuskaityti sistemos, kurios ID {0}, ryšio išsamios informacijos.
#XMSG
connectionNotValid=Ryšys {0} yra netinkamas.
#XMSG
connectionBwBridgeNotValid=SAP BW tilto ryšys {0} yra netinkamas.
#XMSG
connectionBwBridgeNotAvailable=SAP BW tilto ryšys neprieinamas.
#XMSG
loadEntitiesFailed=Objektų įkelti nepavyko.
#XMSG
importEntitiesFailed=Objektų importuoti nepavyko.
#XMSG
importEntitiesStarted=Objektai importuojami. Norėdami sužinoti importavimo būseną, patikrinkite pranešimus.
#XMSG
loadReviewEntitiesListFailed=Importuotinų objektų įkelti nepavyko.
#XMSG
selectOneResultListItem=Pasirinkite tik vieną poziciją ieškos rezultatų sąraše.
#XMSG
dialogCancel=Ar tikrai norite atšaukti subjektų importavimą?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=Ryšio {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=Etiketė
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW tiltas

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Kurti verslo daryklės objektus
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Verslo vienetai ir naudojimo modeliai
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Tik verslo vienetai
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Nėra

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Duomenų prieiga
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Replikacijos srautas į vietines lenteles
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Nuotolinės lentelės
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Replikavimo srautas
#XFLD: label Ingestion Space
ingestionSpaceLabel=Įtraukimo erdvė

#XMSG
modelTransferOk=Modelio importavimas įgalintas.
#XMSG
modelTransferNotOk=Modelio importavimo naudoti negalima.
#XMSG
hanaSdiOk=Nuotolinės lentelės įjungtos.
#XMSG
hanaSdiNotOk=Nuotolinių lentelių naudoti negalima.
#XMSG
replicationFlowOk=Replikavimo srautai įjungti.
#XMSG
replicationFlowNotOk=Replikavimo srautų naudoti negalima.
#XMSG
onboardingOk=Nuotolinių lentelių ir replikavimo srautų parengimas yra įgalintas.
#XMSG
onboardingNotOk=Nuotolinių lentelių ir replikavimo srautų parengimas negalimas.
#XMSG
enableReplicationFlow=Šis ryšys gali palaikyti duomenų replikavimą per replikavimo srautą, tačiau ši funkcija šiuo metu neįjungta. Norėdami atnaujinti ryšį, susisiekite su administratoriumi. Tuo tarpu per nuotolines lenteles galite importuoti tik objektus, turinčius susietą duomenų prieigą.
#XMSG
selectedApiWithoutEntities=Pasirinktas API neteikia jokių objektų.
#XMSG
switchReplicationFlowToRemoteTables=Dabar šie objektai įdiegti kaip vietinės lentelės su į jas replikuotais duomenimis. Pakeitus į sujungimą gali suprastėti našumas. Toks pakeitimas turės poveikį visoms sritims, naudojančioms šiuos objektus.\n\nAr norite pakeisti duomenų prieigą į sujungimą naudojant nuotolines lenteles?
#XMSG
switchRemoteTablesToReplicationFlow=Dabar šie objektai įdiegti kaip nuotolinės lentelės su duomenimis, sujungtais su jomis. Pakeitus į replikavimą pagerės našumas, bet bus išjungta tiesioginė prieiga, todėl jūsų duomenų naujumas priklausys nuo replikavimo tvarkaraščio. Toks pakeitimas turės poveikį visoms sritims, naudojančioms šiuos objektus.\n\nAr norite pakeisti duomenų prieigą į replikavimą naudojant replikavimo srautą ir vietines lenteles?

#XFLD
spaceTypeAbapBridge=SAP BW tiltas
#XFLD
spaceTypeDatasphere=SAP Datasphere
