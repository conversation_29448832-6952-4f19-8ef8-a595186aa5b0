#XTIT: Title for import entities
importEntitiesTitle=Mewngludo Endidau
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=<PERSON><PERSON>s Math o Gysylltiad
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=<PERSON><PERSON><PERSON> Bwlch Targed
#XFLD: import wizard step 3: select connection
selectConnectionStep=Dewis <PERSON>sylltiad
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Dewis Endidau
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Adolygu Endidau
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Dewis Blwch Cysylltiad / Targed

#XTIT: title connection types
connectionTypesTitle=Eitemau ({0})
#XTIT: title target spaces
targetSpaceTitle=Gofodau ({0})
#XTIT: title connections
connectionsTitle=Cysylltiadau ({0})

#XCOL: space business name
spaceBusinessName=Gofod
#XCOL: spaces business name
spacesBusinessName=Gofodau
#XCOL: business name
businessName=Enw Busnes
#XCOL: technical name
technicalName=Enw Technegol
#XCOL: modeling pattern
modelingPattern=Patrwm Modelu
#XCOL: import status
importStatus=Staws Mewngludo
#XCOL: replication flow
replicationFlow=Llif Dyblygu
#XCOL: entity
entity=Endid
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Endidau wedi'u Dewis
#XHED: heading selected entities
selectedApis=APIs wedi'u dewis
#XHED: heading dependent entities
dependentEntities=Endidau Dibynnol
#XHED: heading dependent entities
dependentEntitiesOf=Endidau Dibynnol {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Endidau API wedi''u Dewis {0} 
#XHED: heading entities of API
entitiesOfApi=Endidau API {0}
#XHED: heading business builder
businessBuilderObjects=Lluniwr Busnes ({0})
#XHED: heading data builder
dataBuilderObjects=Lluniwr Data ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Blaenorol
#XBUT
buttonNextStep=Cam Nesaf
#XBUT
buttonImport=Dechrau Mewngludo a Gosod
#XBUT
buttonSchedule=Amserlennu Mewngludo a Gosod
#XBUT
buttonCancel=Canslo
#XBUT
buttonContinue=Bwrw ymlaen
#XMSG
loadConnectionTypesFailed=Does dim modd llwytho mathau o gysylltiad.
#XMSG
loadTargetSpacesFailed=Does dim modd llwytho gofodau targed.
#XMSG
loadConnectionsFailed=Does dim modd llwytho cysylltiadau.
#XMSG
loadBwBridgeConnectionFailed=Does dim modd llwytho cysylltiad pont SAP BW.
#XMSG
getConnectionStatusFailed=Does dim modd adfer statws cysylltiad {0}.
#XMSG
getSharedConnectionDetailsFailed=Does dim modd nôl manylion y cysylltiad ar gyfer ID system {0}.
#XMSG
connectionNotValid=Dydy cysylltiad {0} ddim yn gysylltiad dilys.
#XMSG
connectionBwBridgeNotValid=Dydy cysylltiad pont SAP BW {0} ddim yn gysylltiad dilys.
#XMSG
connectionBwBridgeNotAvailable=Dydy cysylltiad pont SAP BW ddim ar gael.
#XMSG
loadEntitiesFailed=Does dim modd llwytho endidau.
#XMSG
importEntitiesFailed=Does dim modd mewngludo endidau.
#XMSG
importEntitiesStarted=Wrthi'n mewngludo endidau. Edrychwch ar y hysbysiadau i weld statws y broses fewngludo.
#XMSG
loadReviewEntitiesListFailed=Does dim modd llwytho gwrthrychau i'w mewngludo.
#XMSG
selectOneResultListItem=Dewiswch un eitem yn uniog o’r rhestr canlyniadau chwilio.
#XMSG
dialogCancel=Ydych chi wir am ganslo proses mewngludo’r endidau?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=Cysylltiad {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=Label
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW Bridge

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Creu Gwrthrychau Lluniwr Busnes
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Endidau Busnes a Modelau Defnyddio
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Endidau Busnes yn Unig
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Dim

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Mynediad Data
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Llif Dyblygu i Dablau Lleol
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Tablau Pell
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Llif Dyblygu
#XFLD: label Ingestion Space
ingestionSpaceLabel=Gofod wedi'i Ddefnyddio

#XMSG
modelTransferOk=Mewngludo model wedi'i alluogi.
#XMSG
modelTransferNotOk=Does dim modd defnyddio mewngludo model.
#XMSG
hanaSdiOk=Tablau pell wedi'u galluogi.
#XMSG
hanaSdiNotOk=Does dim modd defnyddio tablau pell.
#XMSG
replicationFlowOk=Mae llifau dyblygu wedi'u galluogi.
#XMSG
replicationFlowNotOk=Dim modd defnyddio llifau dyblygu
#XMSG
onboardingOk=Mae'r broses o gynefino llifau dyblygu a thablau pell wedi'i galluogi.
#XMSG
onboardingNotOk=Does dim modd cynnal y broses o gynefino llifau dyblygu a thablau pell.
#XMSG
enableReplicationFlow=Mae'r cysylltiad hwn yn gallu delio â dyblygu data drwy lif dyblygu, ond dydy'r nodwedd hon ddim wedi'i galluogi ar hyn o bryd. Cysylltwch â'ch gweinyddwr i ddiweddaru'r cysylltiad. Yn y cyfamser, gallwch ond fewngludo endidau gyda mynediad data wedi'i ffedereiddio drwy dablau pell.
#XMSG
selectedApiWithoutEntities=Nid yw'r API a ddewiswyd yn darparu endidau.
#XMSG
switchReplicationFlowToRemoteTables=Ar hyn o bryd, mae'r gwrthrychau hyn wedi'u gosod fel tablau lleol gyda data wedi'u dyblygu iddynt. Os byddwch yn newid i ffederasiwn, gall hyn ddirywio'r perfformiad. Bydd y newid hwn yn effeithio ar bob gofod sy'n defnyddio'r gwrthrychau hyn.\n\nYdych chi eisiau newid y mynediad data i ffederasiwn trwy dablau pell?
#XMSG
switchRemoteTablesToReplicationFlow=Ar hyn o bryd, mae'r gwrthrychau hyn wedi'u gosod fel tablau pell gyda data wedi'u cyfuno iddynt. Drwy newid i ddyblygu, bydd hyn yn gwella perfformiad ac yn analluogi mynediad byw felly bydd newydd-deb eich data yn dibynnu ar eich amserlen dyblygu. Bydd y newid hwn yn effeithio ar bob gofod sy'n defnyddio'r gwrthrychau hyn.\n\nYdych chi eisiau newid y mynediad data i ddyblygiad trwy lif dyblygu a thablau lleol?

#XFLD
spaceTypeAbapBridge=Pont SAP BW
#XFLD
spaceTypeDatasphere=SAP Datasphere
