#XTIT: Title for import entities
importEntitiesTitle=Import encji
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=W<PERSON><PERSON>rz typ połączenia
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=W<PERSON><PERSON><PERSON> docelową przestrzeń
#XFLD: import wizard step 3: select connection
selectConnectionStep=W<PERSON><PERSON>rz połączenie
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Wybierz encje
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Przejrzyj encje
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Wybierz połączenie / docelową przestrzeń

#XTIT: title connection types
connectionTypesTitle=Pozycje ({0})
#XTIT: title target spaces
targetSpaceTitle=Przestrzenie ({0})
#XTIT: title connections
connectionsTitle=Połączenia ({0})

#XCOL: space business name
spaceBusinessName=Przestrzeń
#XCOL: spaces business name
spacesBusinessName=Przestrzenie
#XCOL: business name
businessName=Nazwa biznesowa
#XCOL: technical name
technicalName=Nazwa techniczna
#XCOL: modeling pattern
modelingPattern=Wzorzec modelowania
#XCOL: import status
importStatus=Status importu
#XCOL: replication flow
replicationFlow=Przepływ replikacji
#XCOL: entity
entity=Encja
#XCOL: api
api=Interfejs API
#XHED: heading selected entities
selectedEntities=Wybrane encje
#XHED: heading selected entities
selectedApis=Wybrane API
#XHED: heading dependent entities
dependentEntities=Zależne encje
#XHED: heading dependent entities
dependentEntitiesOf=Zależne encje {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Wybrane encje API {0}
#XHED: heading entities of API
entitiesOfApi=Encje API {0}
#XHED: heading business builder
businessBuilderObjects=Edytor biznesowy ({0})
#XHED: heading data builder
dataBuilderObjects=Edytor danych ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Poprzednie
#XBUT
buttonNextStep=Następny krok
#XBUT
buttonImport=Rozpocznij import i wdróż
#XBUT
buttonSchedule=Zaplanuj import i wdróż
#XBUT
buttonCancel=Anuluj
#XBUT
buttonContinue=Kontynuuj
#XMSG
loadConnectionTypesFailed=Nie można wczytać typów połączenia.
#XMSG
loadTargetSpacesFailed=Nie można wczytać docelowych przestrzeni.
#XMSG
loadConnectionsFailed=Nie można wczytać połączeń.
#XMSG
loadBwBridgeConnectionFailed=Nie można wczytać połączenia SAP BW Bridge.
#XMSG
getConnectionStatusFailed=Nie można wywołać statusu połączenia {0}.
#XMSG
getSharedConnectionDetailsFailed=Nie można pobrać szczegółów połączenia dla ID systemu {0}.
#XMSG
connectionNotValid=Połączenie {0} jest nieprawidłowe.
#XMSG
connectionBwBridgeNotValid=Połączenie SAP BW Bridge {0} jest nieprawidłowe.
#XMSG
connectionBwBridgeNotAvailable=Połączenie SAP BW Bridge nie jest dostępne.
#XMSG
loadEntitiesFailed=Nie można wczytać encji.
#XMSG
importEntitiesFailed=Nie można zaimportować encji.
#XMSG
importEntitiesStarted=Importowanie encji. Sprawdź powiadomienia, aby zapoznać się ze statusem importu.
#XMSG
loadReviewEntitiesListFailed=Nie można wczytać obiektów do zaimportowania.
#XMSG
selectOneResultListItem=Wybierz tylko jedną pozycję na liście wyników wyszukiwania.
#XMSG
dialogCancel=Czy na pewno chcesz anulować import encji?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=Połączenie {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=Etykieta
#XTXT: SAP BW Bridge Connection type
sapBwBridge=Łączenie SAP BW

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Utwórz obiekty edytora biznesowego
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Encje biznesowe i modele wykorzystania
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Tylko encje biznesowe
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Brak

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Dostęp do danych
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Przepływ replikacji do tabel lokalnych
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Tabele zdalne
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Przepływ replikacji
#XFLD: label Ingestion Space
ingestionSpaceLabel=Przestrzeń pozyskiwania danych

#XMSG
modelTransferOk=Import modelu jest aktywny.
#XMSG
modelTransferNotOk=Nie można użyć importu modelu
#XMSG
hanaSdiOk=Tabele zdalne są włączone.
#XMSG
hanaSdiNotOk=Nie można użyć tabel zdalnych.
#XMSG
replicationFlowOk=Przepływy replikacji są włączone.
#XMSG
replicationFlowNotOk=Nie można użyć przepływów replikacji.
#XMSG
onboardingOk=Dodawanie tabel zdalnych i przepływów replikacji jest włączone.
#XMSG
onboardingNotOk=Dodawanie tabel zdalnych i przepływów replikacji jest niemożliwe.
#XMSG
enableReplicationFlow=To połączenie może obsługiwać replikację danych poprzez przepływ replikacji, ale ta funkcja nie jest obecnie włączona. Skontaktuj się z administratorem, aby zaktualizować połączenie. W międzyczasie możesz jedynie importować encje ze zintegrowanym dostępem do danych poprzez tabele zdalne.
#XMSG
selectedApiWithoutEntities=Wybrany API nie zapewnia encji.
#XMSG
switchReplicationFlowToRemoteTables=Te obiekty są obecnie zainstalowane jako tabele lokalne ze zreplikowanymi danymi. Zmiana na federację może obniżyć wydajność. Ta zmiana wpłynie na wszystkie przestrzenie, które korzystają z tych obiektów.\n\nCzy chcesz zmienić dostęp do danych na federację przez tabele zdalne?
#XMSG
switchRemoteTablesToReplicationFlow=Te obiekty są obecnie zainstalowane jako tabele zdalne ze sfederowanymi danymi. Zmiana na replikację może podwyższyć wydajność, ale także wyłączy dostęp na żywo, więc aktualność danych będzie zależała od harmonogramu replikacji. Ta zmiana wpłynie na wszystkie przestrzenie, które korzystają z tych obiektów.\n\nCzy chcesz zmienić dostęp do danych na replikację przez przepływ replikacji i tabele lokalne?

#XFLD
spaceTypeAbapBridge=Łączenie SAP BW
#XFLD
spaceTypeDatasphere=SAP Datasphere
