#XTIT: Title for import entities
importEntitiesTitle=Importēt entītijas
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Atlasīt savienojuma tipu
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Atlasīt mērķa vietu
#XFLD: import wizard step 3: select connection
selectConnectionStep=Atlasīt savienojumu
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Atlasīt entītijas
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Pārskatīt entītijas
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Atlasīt savienojumu / mērķa vietu

#XTIT: title connection types
connectionTypesTitle=Elementi ({0})
#XTIT: title target spaces
targetSpaceTitle=Vietas ({0})
#XTIT: title connections
connectionsTitle=Savienojumi ({0})

#XCOL: space business name
spaceBusinessName=Vieta
#XCOL: spaces business name
spacesBusinessName=Atstarpes
#XCOL: business name
businessName=Biznesa nosaukums
#XCOL: technical name
technicalName=Tehniskais nosaukums
#XCOL: modeling pattern
modelingPattern=Modelēšanas modelis
#XCOL: import status
importStatus=Importēšanas statuss
#XCOL: replication flow
replicationFlow=Replicēšanas plūsma
#XCOL: entity
entity=Entītija
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Atlasītās entītijas
#XHED: heading selected entities
selectedApis=Atlasītās API
#XHED: heading dependent entities
dependentEntities=Atkarīgās entītijas
#XHED: heading dependent entities
dependentEntitiesOf={0} atkarīgās entītijas
#XHED: heading dependent entities
selectedEntitiesOfApi=API {0} atlasītās entītijas
#XHED: heading entities of API
entitiesOfApi=API {0} entītijas
#XHED: heading business builder
businessBuilderObjects=Biznesa veidotājs ({0})
#XHED: heading data builder
dataBuilderObjects=Datu veidotājs ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Iepriekšējais
#XBUT
buttonNextStep=Nākamais solis
#XBUT
buttonImport=Sākt importēšanu un izvietošanu
#XBUT
buttonSchedule=Ieplānot importēšanu un izvietošanu
#XBUT
buttonCancel=Atcelt
#XBUT
buttonContinue=Turpināt
#XMSG
loadConnectionTypesFailed=Nevarēja ielādēt savienojumu tipus.
#XMSG
loadTargetSpacesFailed=Nevarēja ielādēt mērķa vietas.
#XMSG
loadConnectionsFailed=Nevarēja ielādēt savienojumus.
#XMSG
loadBwBridgeConnectionFailed=Nevarēja ielādēt SAP BW tilta savienojumu.
#XMSG
getConnectionStatusFailed=Nevarēja izgūt savienojuma {0} statusu.
#XMSG
getSharedConnectionDetailsFailed=Nevar izgūt detalizētu informāciju par savienojumu sistēmai ar ID {0}.
#XMSG
connectionNotValid=Savienojums {0} nav derīgs savienojums.
#XMSG
connectionBwBridgeNotValid=SAP BW tilta savienojums {0} nav derīgs savienojums.
#XMSG
connectionBwBridgeNotAvailable=SAP BW tilta savienojums nav pieejams.
#XMSG
loadEntitiesFailed=Nevarēja ielādēt entītijas.
#XMSG
importEntitiesFailed=Nevarēja importēt entītijas.
#XMSG
importEntitiesStarted=Notiek entītiju importēšana. Importēšanas statusu skatiet paziņojumos.
#XMSG
loadReviewEntitiesListFailed=Importējamo objektu ielāde neizdevās.
#XMSG
selectOneResultListItem=Meklēšanas rezultātu sarakstā atlasiet tikai vienu elementu.
#XMSG
dialogCancel=Vai tiešām vēlaties atcelt entītiju importēšanu?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=Savienojums {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=Etiķete
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW tilts

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Biznesa veidotāja objektu izveide
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Saimnieciskās vienības un patēriņa modeļi
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Tikai saimnieciskās vienības
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Nav

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Datu piekļuve
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Replicēšanas plūsma uz vietējām tabulām
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Attālās tabulas
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Replicēšanas plūsma
#XFLD: label Ingestion Space
ingestionSpaceLabel=Uzņemšanas vieta

#XMSG
modelTransferOk=Modeļa importēšana ir iespējota.
#XMSG
modelTransferNotOk=Modeļa importēšanu nevar izmantot.
#XMSG
hanaSdiOk=Attālās tabulas ir iespējotas.
#XMSG
hanaSdiNotOk=Attālās tabulas nevar izmantot.
#XMSG
replicationFlowOk=Replicēšanas plūsmas ir iespējotas.
#XMSG
replicationFlowNotOk=Replicēšanas plūsmas nevar izmantot.
#XMSG
onboardingOk=Attālo tabulu un replicēšanas plūsmu integrēšana ir iespējota.
#XMSG
onboardingNotOk=Attālo tabulu un replicēšanas plūsmu integrēšana nav iespējama.
#XMSG
enableReplicationFlow=Šis savienojums var atbalstīt datu replicēšanu, izmantojot replicēšanas plūsmu, bet šis līdzeklis pašlaik nav iespējots. Lūdzu, sazinieties ar savu administratoru, lai atjauninātu šo savienojumu. Tikmēr entītijas varat importēt tikai ar federatīvo datu piekļuvi, izmantojot attālās tabulas.
#XMSG
selectedApiWithoutEntities=Atlasītā API nenodrošina nekādas entītijas.
#XMSG
switchReplicationFlowToRemoteTables=Pašlaik šie objekti ir instalēti kā lokālas tabulas ar datiem, kuri uz tām tiek replicēti. Mainīšana uz federāciju var pasliktināt veiktspēju. Šī izmaiņa ietekmēs visas vietas, kuras šos objektus patērē.\n\nVai vēlaties mainīt datu piekļuvi uz federāciju, izmantojot attālās tabulas?
#XMSG
switchRemoteTablesToReplicationFlow=Pašlaik šie objekti ir instalēti kā attālās tabulas ar datiem, kuri uz tām tiek federalizēti. Mainīšana uz replicēšanu uzlabos veiktspēju, taču arī atspējos reāllaika piekļuvi, tāpēc datu svaigums ir atkarīgs no jūsu replicēšanas grafika. Šī izmaiņa ietekmēs visas vietas, kuras šos objektus patērē.\n\nVai vēlaties mainīt datu piekļuvi uz replicēšanu, izmantojot replicēšanas plūsmu un lokālās tabulas?

#XFLD
spaceTypeAbapBridge=SAP BW tilts
#XFLD
spaceTypeDatasphere=SAP Datasphere
