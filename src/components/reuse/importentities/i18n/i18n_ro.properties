#XTIT: Title for import entities
importEntitiesTitle=Importare entități
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=Selectare tip de conexiune
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=Selectare spațiu țintă
#XFLD: import wizard step 3: select connection
selectConnectionStep=Selectare conexiune
#XFLD: import wizard step 4: select entities
selectEntitiesStep=Selectare entități
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=Revizuire entități
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=Selectare conexiune/spațiu țintă

#XTIT: title connection types
connectionTypesTitle=Elemente ({0})
#XTIT: title target spaces
targetSpaceTitle=Spații ({0})
#XTIT: title connections
connectionsTitle=Conexiuni ({0})

#XCOL: space business name
spaceBusinessName=Spațiu
#XCOL: spaces business name
spacesBusinessName=Spații
#XCOL: business name
businessName=Nume comercial
#XCOL: technical name
technicalName=Nume tehnic
#XCOL: modeling pattern
modelingPattern=Model de modelare
#XCOL: import status
importStatus=Stare import
#XCOL: replication flow
replicationFlow=Flux de replicare
#XCOL: entity
entity=Entitate
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=Entități selectate
#XHED: heading selected entities
selectedApis=API-uri selectate
#XHED: heading dependent entities
dependentEntities=Entități dependente
#XHED: heading dependent entities
dependentEntitiesOf=Entități dependente ale {0}
#XHED: heading dependent entities
selectedEntitiesOfApi=Entități selectate ale API-ului {0}
#XHED: heading entities of API
entitiesOfApi=Entități ale API-ului {0}
#XHED: heading business builder
businessBuilderObjects=Generator de modele de afaceri ({0})
#XHED: heading data builder
dataBuilderObjects=Generator de date ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=Anterior
#XBUT
buttonNextStep=Etapă următoare
#XBUT
buttonImport=Lansare import și implementare
#XBUT
buttonSchedule=Programare import și implementare
#XBUT
buttonCancel=Anulare
#XBUT
buttonContinue=Continuare
#XMSG
loadConnectionTypesFailed=Imposibil de încărcat tipurile de conexiune.
#XMSG
loadTargetSpacesFailed=Imposibil de încărcat spațiile țintă.
#XMSG
loadConnectionsFailed=Imposibil de încărcat conexiunile.
#XMSG
loadBwBridgeConnectionFailed=Imposibil de încărcat conexiunea la puntea SAP BW.
#XMSG
getConnectionStatusFailed=Imposibil de regăsit starea conexiunii {0}.
#XMSG
getSharedConnectionDetailsFailed=Imposibil de regăsit detaliile conexiunii pentru ID sistem {0}.
#XMSG
connectionNotValid=Conexiunea {0} nu este o conexiune valabilă.
#XMSG
connectionBwBridgeNotValid=Conexiunea la puntea SAP BW {0} nu este o conexiune valabilă.
#XMSG
connectionBwBridgeNotAvailable=Conexiunea la puntea SAP BW nu este disponibilă.
#XMSG
loadEntitiesFailed=Imposibil de încărcat entitățile.
#XMSG
importEntitiesFailed=Imposibil de importat entitățile.
#XMSG
importEntitiesStarted=Se importă entitățile. Verificați notificările pentru starea importului.
#XMSG
loadReviewEntitiesListFailed=Imposibil de încărcat obiectele de importat.
#XMSG
selectOneResultListItem=Selectați un singur element din lista de rezultate de căutare.
#XMSG
dialogCancel=Sigur doriți să anulați importul entităților?
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=Conexiune {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=Etichetă
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW Bridge

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=Creare obiecte de generator de modele de afaceri
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=Entități de afaceri și modele de consum
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=Doar entități de afaceri
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=Nimic

#XFLD: label Remote Access Mode
remoteAccessModeLabel=Acces la date
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=Flux de replicare în tabele locale
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=Tabele la distanță
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=Flux de replicare
#XFLD: label Ingestion Space
ingestionSpaceLabel=Spațiu de ingestie

#XMSG
modelTransferOk=Import de model este activat.
#XMSG
modelTransferNotOk=Import de model nu poate fi utilizat.
#XMSG
hanaSdiOk=Tabele la distanță sunt activate.
#XMSG
hanaSdiNotOk=Tabele la distanță nu pot fi utilizate.
#XMSG
replicationFlowOk=Fluxuri de replicare sunt activate.
#XMSG
replicationFlowNotOk=Fluxuri de replicare nu pot fi utilizate.
#XMSG
onboardingOk=Integrarea de tabele la distanță și fluxuri de replicare este activată.
#XMSG
onboardingNotOk=Integrarea de tabele la distanță și fluxuri de replicare este imposibilă.
#XMSG
enableReplicationFlow=Această conexiune poate suporta replicarea datelor printr-un flux de replicare, dar această caracteristică nu este activată în prezent. Contactați administratorul dvs. pentru a actualiza conexiunea. Între timp, puteți importa doar entități cu acces la date federal prin tabelele la distanță.
#XMSG
selectedApiWithoutEntities=API-ul selectat nu furnizează nicio entitate.
#XMSG
switchReplicationFlowToRemoteTables=Aceste obiecte sunt instalate în prezent ca tabele locale cu date replicate în ele. Modificarea la federare poate degrada performanța. Această modificare va afecta toate spațiile care consumă aceste obiecte.\n\nDoriți să modificați accesul la date pentru federare prin tabele la distanță?
#XMSG
switchRemoteTablesToReplicationFlow=Aceste obiecte sunt instalate în prezent ca tabele la distanță cu date federate în ele. Modificarea la replicare va îmbunătăți performanța, dar va dezactiva și accesul live, astfel încât actualitatea datelor dvs. să depindă de programul dvs. de replicare. Această modificare va afecta toate spațiile care consumă aceste obiecte.\n\nDoriți să modificați accesul la date pentru replicare printr-un flux de replicare și tabele locale?

#XFLD
spaceTypeAbapBridge=SAP BW Bridge
#XFLD
spaceTypeDatasphere=SAP Datasphere
