#XTIT: Title for import entities
importEntitiesTitle=匯入實體
#XFLD: import wizard step 1: select connection type
selectConnectionTypeStep=選擇連線類型
#XFLD: import wizard step 2: select target space
selectTargetSpaceStep=選擇目標空間
#XFLD: import wizard step 3: select connection
selectConnectionStep=選擇連線
#XFLD: import wizard step 4: select entities
selectEntitiesStep=選擇實體
#XFLD: import wizard step 5: review entities
reviewEntitiesStep=審查實體
#XFLD: import wizard step: select connection / target space
selectConnectionTargetSpaceStep=選擇連線/目標空間

#XTIT: title connection types
connectionTypesTitle=項目 ({0})
#XTIT: title target spaces
targetSpaceTitle=空間 ({0})
#XTIT: title connections
connectionsTitle=連線 ({0})

#XCOL: space business name
spaceBusinessName=空間
#XCOL: spaces business name
spacesBusinessName=空間
#XCOL: business name
businessName=業務名稱
#XCOL: technical name
technicalName=技術名稱
#XCOL: modeling pattern
modelingPattern=模型化模式
#XCOL: import status
importStatus=匯入狀態
#XCOL: replication flow
replicationFlow=複製流程
#XCOL: entity
entity=實體
#XCOL: api
api=API
#XHED: heading selected entities
selectedEntities=所選實體
#XHED: heading selected entities
selectedApis=所選 API
#XHED: heading dependent entities
dependentEntities=相關實體
#XHED: heading dependent entities
dependentEntitiesOf={0} 的相關實體
#XHED: heading dependent entities
selectedEntitiesOfApi=API {0} 的所選實體
#XHED: heading entities of API
entitiesOfApi=API {0} 的實體
#XHED: heading business builder
businessBuilderObjects=業務建立器 ({0})
#XHED: heading data builder
dataBuilderObjects=資料模型建立器 ({0})
#XFLD path delimiter
delimiter=/

#XBUT
buttonPreviousStep=上一步
#XBUT
buttonNextStep=下一步
#XBUT
buttonImport=開始匯入和部署
#XBUT
buttonSchedule=排程匯入和部署
#XBUT
buttonCancel=取消
#XBUT
buttonContinue=繼續
#XMSG
loadConnectionTypesFailed=無法載入連線類型。
#XMSG
loadTargetSpacesFailed=無法載入目標空間。
#XMSG
loadConnectionsFailed=無法載入連線。
#XMSG
loadBwBridgeConnectionFailed=無法載入 SAP BW 橋接連線。
#XMSG
getConnectionStatusFailed=無法檢索連線 {0} 狀態。
#XMSG
getSharedConnectionDetailsFailed=無法檢索系統 ID {0} 的連線明細。
#XMSG
connectionNotValid=連線 {0} 不是有效的連線。
#XMSG
connectionBwBridgeNotValid=SAP BW 橋接連線 {0} 不是有效的連線。
#XMSG
connectionBwBridgeNotAvailable=SAP BW 橋接連線無法使用。
#XMSG
loadEntitiesFailed=無法載入實體。
#XMSG
importEntitiesFailed=無法匯入實體。
#XMSG
importEntitiesStarted=正在匯入實體。請查看通知瞭解匯入狀態。
#XMSG
loadReviewEntitiesListFailed=無法載入物件以匯入。
#XMSG
selectOneResultListItem=請只在搜尋結果清單中選擇一個項目。
#XMSG
dialogCancel=您確定要取消匯入實體嗎？
#XFLD: Search results, step "Select Entity" -> Overwrite column headers
identifier=ID
identifier_plural=連線 {0}
identifier_identifier=ID
identifier_entityname=ID
identifier_entitylabel=標籤
#XTXT: SAP BW Bridge Connection type
sapBwBridge=SAP BW 橋接

#XFLD: label Create Business Builder Objects
createBlObjectsLabel=建立業務建立器
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importAll=業務實體和使用模型
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDlBe=僅業務實體
#XMIT: selection entry in list of possible import options which kind of Business Builder Objects should be imported
importDl=無

#XFLD: label Remote Access Mode
remoteAccessModeLabel=資料存取
#XMIT: selection entry in list of possible remote access mode options
viaReplicationFlow=複製流程至本端表格
#XMIT: selection entry in list of possible remote access mode options
viaRemoteTables=遠端表格
#XFLD: label Proposed replication flow name
proposedRepflowNameLabel=複製流程
#XFLD: label Ingestion Space
ingestionSpaceLabel=擷取空間

#XMSG
modelTransferOk=已啟用模型匯入。
#XMSG
modelTransferNotOk=無法使用模型匯入。
#XMSG
hanaSdiOk=已啟用遠端表格。
#XMSG
hanaSdiNotOk=無法使用遠端表格。
#XMSG
replicationFlowOk=已啟用複製流程。
#XMSG
replicationFlowNotOk=無法使用複製流程。
#XMSG
onboardingOk=已啟用遠端表格和複製流程的登錄。
#XMSG
onboardingNotOk=無法進行遠端表格和複製流程的登錄。
#XMSG
enableReplicationFlow=此連線透過複製流程可支援資料複製，但此功能目前未啟用。請聯絡您的管理員以更新連線。目前您只能透過遠端表格匯入包含聯合資料存取的實體。
#XMSG
selectedApiWithoutEntities=所選 API 未提供實體。
#XMSG
switchReplicationFlowToRemoteTables=這些物件目前已安裝為本機表格，且包含資料複製到其中。更改為聯合可能會降低效能。此更改將影響使用這些物件的所有空間。\n\n您要將資料存取更改為透過遠端表格聯合嗎？
#XMSG
switchRemoteTablesToReplicationFlow=這些物件目前已安裝為遠端表格，且包含資料聯合到其中。更改為複製可能會改善效能但也會停用即時存取，因此資料最新程度取決於複製排程。此更改將影響使用這些物件的所有空間。\n\n您要將資料存取更改為透過複製流程和本端表格複製嗎？

#XFLD
spaceTypeAbapBridge=SAP BW 橋接
#XFLD
spaceTypeDatasphere=SAP Datasphere
