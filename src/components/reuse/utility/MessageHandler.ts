/** @format */

// FILEOWNER: [dw101_crossarchitecture]

import { Configuration, ErrorCode, ObjectType, validateName as validateNameService } from "@sap/dwc-name-validator";
import { WarningCode } from "@sap/dwc-name-validator/dist/constants";
import { ErrorCodes } from "../../../../shared/lib/hanaConnectionMonitoring/ClientSideHanaError";
import { getResourceText } from "./MessageLoader";
import { ServiceCall } from "./ServiceCall";
import { Logger } from "./UIHelper";
export { ErrorCode, ObjectType };
export interface IMessageHandlerExceptionParams {
  message?: string;
  title?: string;
  exception: any;
  contentWidth?: string;
  id?: string;
  onClose?: (action: string) => void;
  customAction?: string;
  messageBundleId?: string;
  messageKey?: string;
  messageParams?: string[];
}
export const NAME_VALIDATION_TEXTS: {
  [key in ErrorCode | WarningCode]: string;
} = {
  nameValidator_DuplicateString: "nameValidationDuplicateName",
  nameValidator_InvalidCharacter: "nameValidationInvalidCharacter",
  nameValidator_InvalidNamespace: "nameValidator_InvalidNamespace",
  nameValidator_NameError: "nameValidationNameError",
  nameValidator_WhitespaceAsFirstCharacter: "nameValidationWhitespaceAsFirstCharacter",
  nameValidator_NameTooLong: "nameValidationNameTooLong",
  nameValidator_NameTooShort: "nameValidationNameTooShort",
  nameValidator_ReservedKeyword: "nameValidationReservedKeyword",
  nameValidator_ReservedPrefix: "nameValidationReservedPrefix",
  nameValidator_warning_ReservedPrefix: "nameValidationWarningReservedPrefix",
  nameValidator_ConfigurationError: "nameValidationConfigurationError",
  nameValidator_InvalidUUID: "nameValidationInvalidUUID",
  nameValidator_DotInInvalidPosition: "nameValidationDotInInvalidPosition",
  nameValidator_UnderscoreAsFirstCharacter: "nameValidationUnderscoreAsFirstOrLastCharacter",
  nameValidator_UnderscoreAsLastCharacter: "nameValidationUnderscoreAsFirstOrLastCharacter",
  nameValidator_InvalidURI: "nameValidationInvalidURI",
};

export interface IMessageParams {
  messageBundleId?: string;
  messageKey?: string;
}

export class MessageHandler {
  private static resourceModel: sap.ui.model.resource.ResourceModel | undefined;

  public static show(
    messageType: sap.ui.core.MessageType,
    message: string,
    title?: string,
    duration?: number,
    details?: string,
    contentWidth?: string,
    onClose?: Function,
    customAction?: string,
    id?: string
  ) {
    const lfnDestroy = function () {
      const control = sap.ui.getCore().byId(this.id);
      if (control) {
        control.destroy();
      }
    };

    const checkTimeout = function (sUniqueId, iDuration) {
      if (iDuration) {
        setTimeout(lfnDestroy.bind({ id: sUniqueId }), iDuration);
      }
    };

    const MessageBox = sap.ui.require("sap/m/MessageBox") as typeof sap.m.MessageBox;
    const MessageToast = sap.ui.require("sap/m/MessageToast") as typeof sap.m.MessageToast;
    const Uid = sap.ui.require("sap/base/util/uid") as () => string;
    if (MessageBox && MessageToast && Uid) {
      const lsUniqueId = id || Uid();

      switch (messageType) {
        case sap.ui.core.MessageType.Information: {
          MessageBox.information(message, {
            id: lsUniqueId,
            title: title || this.getText("messageTitleInformation"),
            onClose: (action: sap.m.MessageBox.Action) => {
              if (onClose) {
                onClose(action);
              }
            },
          });
          break;
        }
        case sap.ui.core.MessageType.Success: {
          const oMessageToastParams = { duration: duration, closeOnBrowserNavigation: false };
          MessageToast.show(message, oMessageToastParams);
          break;
        }
        case sap.ui.core.MessageType.None: {
          break;
        }
        case sap.ui.core.MessageType.Warning: {
          MessageBox.warning(message, {
            id: lsUniqueId,
            title: title || this.getText("messageTitleWarning"),
            details: details,
            contentWidth: contentWidth || "",
            closeOnNavigation: false,
          });
          break;
        }
        case sap.ui.core.MessageType.Error: {
          const parameters: any = {
            id: lsUniqueId,
            title: title || this.getText("messageTitleError"),
            details: details,
            styleClass: details ? "sapUiSizeCompact" : "",
            contentWidth: contentWidth || "",
            closeOnNavigation: false,
          };
          if (onClose) {
            if (customAction) {
              parameters.actions = [customAction, MessageBox.Action.CLOSE];
            }
            parameters.onClose = (action: sap.m.MessageBox.Action) => {
              onClose(action);
            };
          }
          MessageBox.error(message, parameters);
          break;
        }
        case sap.ui.core.MessageType.Confirm: {
          MessageBox.confirm(message, {
            id: lsUniqueId,
            title: title || this.getText("messageTitleConfirmation"),
            details: details,
            styleClass: details ? "sapUiSizeCompact" : "",
            contentWidth: contentWidth || "",
            onClose: (action: sap.m.MessageBox.Action) => {
              if (onClose) {
                onClose(action);
              }
            },
          });
          break;
        }
      }

      checkTimeout(lsUniqueId, duration);
      // returns all messagebox based controls, but not the messagetoast
      return sap.ui.getCore().byId(lsUniqueId);
    } else {
      // eslint-disable-next-line no-console
      console.log("UI5 Log not yet instantiated");
    }
  }

  public static success(message: string, title?: string, duration?: number) {
    return MessageHandler.show(sap.ui.core.MessageType.Success, message, title, duration);
  }

  public static info(message: string, title?: string, duration?: number) {
    return MessageHandler.show(sap.ui.core.MessageType.Information, message, title, duration);
  }

  public static warning(message: string, title?: string, duration?: number, details?: string, contentWidth?: string) {
    return MessageHandler.show(sap.ui.core.MessageType.Warning, message, title, duration, details, contentWidth);
  }

  /** This function tries to extract usefull information from an excetpion. It will ususally work well with exceptions coming from ServiceCall
   * We will extend this function over time to avoid that similar code spreades over the codebase.
   */
  public static guessExceptionDetails(exception: any = {}) {
    if (exception[0] !== undefined) {
      exception = exception[0];
    }
    const errorInfo = {
      correlationId: "",
      httpStatus: "",
      responseJSON: {
        code: "",
        parameters: {},
        details: {
          message: "",
          stack: "",
          code: undefined,
          name: "",
        },
      },
    };
    if (exception.getResponseHeader) {
      errorInfo.correlationId = exception.getResponseHeader("x-request-id");
    }
    if (exception.responseJSON) {
      errorInfo.responseJSON = { ...errorInfo.responseJSON, ...exception.responseJSON };
      if (exception.responseJSON.message) {
        errorInfo.responseJSON.details.message = exception.responseJSON.message;
      }
    } else if (exception.responseText) {
      errorInfo.responseJSON.details.message = exception.responseText;
    } else {
      if (exception.message) {
        errorInfo.responseJSON.details.message = exception.message;
      }
      if (exception.stack) {
        errorInfo.responseJSON.details.stack = exception.stack;
      }
    }

    if (exception.status) {
      errorInfo.httpStatus += exception.status + " ";
    }

    if (exception.statusText) {
      errorInfo.httpStatus += exception.statusText;
    }

    return errorInfo;
  }

  /**
   * Similar to guessExceptionDetails, but format the result to a string.
   */
  public static getExceptionDetails(exeception: any) {
    const errorInfo = this.guessExceptionDetails(exeception);
    return this.getText("technicalErrorDetails", [
      errorInfo.correlationId,
      errorInfo.responseJSON.details.message,
      errorInfo.responseJSON.code,
      errorInfo.httpStatus,
    ]);
  }

  /**
   * Call this function to handle exceptions from server calls, aka all direct and indirect calls of ServiceCall.
   * Error logging is contained, thus don't call logError.
   * The function will try to derive as much technical information and show it in the "details" section of the MessageBox.
   * @example
   *  try {
   *   // call some service
   * } catch(e) {
   *     MessageHandler.exception({message: this.getText("resetPasswordError"), exeception: e });
   * }
   * @param params
   * For main error message you have the following possibilities
   *  * Pass in the plain (already translated) text via "message" parameter
   *  * Pass in messageBundleId and messageKey (and optionally messageParams). Then the message bundle is loaded dynamically using MessageLoader
   *  * Pass nothing and rely that backend error code is translated using BackendToFrontendError
   */
  public static async exception(params: IMessageHandlerExceptionParams) {
    const errorInfo = this.guessExceptionDetails(params.exception);
    if (
      errorInfo?.responseJSON?.details?.name === "HanaError" &&
      (errorInfo?.responseJSON?.details?.code === 616 ||
        errorInfo?.responseJSON?.details?.code === ErrorCodes.RATE_LIMIT_REJECTION)
    ) {
      // HANA Cloud rejection error already caught in ServiceCall.ts
      return;
    }
    const details = this.getText("technicalErrorDetails", [
      errorInfo.correlationId,
      errorInfo.responseJSON.details.message,
      errorInfo.responseJSON.code,
      errorInfo.httpStatus,
    ]);

    const message =
      params.message || (await this.getMessageFromBundle(params)) || this.getText("unexpectedErrorMessage", []);
    Logger.logError(
      `Service call exception. UI-message: ${message}, Correlation ID: ${errorInfo.correlationId}, Backend-message: ${errorInfo.responseJSON.details.message}, Backend-error code: ${errorInfo.responseJSON.code}}`
    );
    let p: Promise<string> | undefined;
    let onClose = params.onClose;
    if (!onClose) {
      p = new Promise((resolve, reject) => {
        onClose = resolve;
      });
    }
    MessageHandler.show(
      sap.ui.core.MessageType.Error,
      message,
      params.title,
      undefined,
      details,
      params.contentWidth,
      onClose,
      params.customAction,
      params.id
    );
    return p;
  }

  private static async getMessageFromBundle(
    params?: IMessageParams & { messageParams?: string[] }
  ): Promise<string> | undefined {
    if (params && params.messageBundleId && params.messageKey) {
      return getResourceText(params.messageBundleId, params.messageKey, params.messageParams);
    }
    return undefined;
  }

  /**
   * Validates the given name for a specific object type against global rules.
   *
   * @param type The object type defines the rules to validate the name against.
   * @param name Object name to be validated.
   * @param blacklistedStrings Optional array of not allowed strings. Supply all strings the name must not match exactly. Useful to check for duplicates.
   * @param texts Optional map of UI-specific error messages. If provided, the generic error messages are ignored.
   * @returns {{ state: sap.ui.core.ValueState, text: string }} Validation result.
   */
  public static async validateName(
    type: ObjectType,
    name: string,
    blacklistedStrings?: string[],
    texts?: {
      messageBundleId?: string;
      messageKeys: {
        [errorCode in ErrorCode]?: IMessageParams;
      };
    },
    config?: Configuration
  ) {
    const res = validateNameService(config)(type, name, blacklistedStrings);

    if (res.status === "OK") {
      return { state: sap.ui.core.ValueState.None, text: "", derivationResults: res.derivationResults };
    }

    const { errorCode, details, matchedSubstrings } = res.failedValidations[0];
    let args;
    switch (errorCode) {
      case ErrorCode.nameValidator_NameTooLong:
        args = [(details["maxLen"] as number).toString()];
        break;
      case ErrorCode.nameValidator_NameTooShort:
        args = [(details["minLen"] as number).toString()];
        break;
      case ErrorCode.nameValidator_InvalidCharacter:
        args = [matchedSubstrings[0].value, matchedSubstrings[0].offset.toString()];
        break;
      case ErrorCode.nameValidator_ReservedKeyword:
        args = [details["keywordNotAllowed"]];
        break;
      case ErrorCode.nameValidator_ReservedPrefix:
      case WarningCode.nameValidator_warning_ReservedPrefix:
        args = [details["reservedPrefix"]];
        break;
      case ErrorCode.nameValidator_InvalidUUID:
        args = [name];
        break;
    }

    return {
      state: res.status === "ERROR" ? sap.ui.core.ValueState.Error : sap.ui.core.ValueState.Warning,
      text:
        texts && texts.messageKeys[errorCode]
          ? await this.getMessageFromBundle({
              ...texts.messageKeys[errorCode],
              messageBundleId: texts.messageKeys[errorCode].messageBundleId || texts.messageBundleId,
              messageParams: args,
            })
          : this.getText(NAME_VALIDATION_TEXTS[errorCode], args),
      derivationResults: res.derivationResults,
    };
  }

  /**
   * @deprecated use @see MessageHandler.exception or  @see MessageHandler.uiError
   */
  public static error(
    message: string,
    title?: string,
    duration?: number,
    details?: string,
    contentWidth?: string,
    onClose?: Function,
    customAction?: string,
    id?: string
  ) {
    const lastError = ServiceCall.getLastErrorResponse();
    if (lastError?.responseJSON?.details?.code === 616 && lastError?.responseJSON?.details?.name === "HanaError") {
      // HANA Cloud rejection error already caught in ServiceCall.ts
      return;
    }
    if (details === undefined && ServiceCall.getLastErrorResponse() !== undefined) {
      details = this.getExceptionDetails(ServiceCall.getLastErrorResponse());
    }

    return MessageHandler.show(
      sap.ui.core.MessageType.Error,
      message,
      title,
      duration,
      details,
      contentWidth,
      onClose,
      customAction,
      id
    );
  }

  /**
   * Use this function only, if the cause of the error comes directly from UI, and not from a backend service.
   * Else use @see MessageHandler.exception
   */
  public static uiError(
    message: string,
    title?: string,
    duration?: number,
    details?: string,
    contentWidth?: string,
    onClose?: Function,
    customAction?: string,
    id?: string
  ) {
    return MessageHandler.show(
      sap.ui.core.MessageType.Error,
      message,
      title,
      duration,
      details,
      contentWidth,
      onClose,
      customAction,
      id
    );
  }

  public static confirm(
    message: string,
    title?: string,
    duration?: number,
    details?: string,
    contentWidth?: string,
    onClose?: Function,
    customAction?: string,
    id?: string
  ) {
    return MessageHandler.show(
      sap.ui.core.MessageType.Confirm,
      message,
      title,
      duration,
      details,
      contentWidth,
      onClose,
      customAction,
      id
    );
  }

  public static getText(i18nKey: string, args?: string[]) {
    if (!this.resourceModel) {
      this.resourceModel = new sap.ui.model.resource.ResourceModel({
        bundleName: require("./i18n/i18n.properties"),
      });
    }
    return this.resourceModel.getResourceBundle().getText(i18nKey, args);
  }
}
if (window.sap && sap.ui?.define) {
  // Usefull for tests: register logger as SAPUI5 module
  sap.ui.define(
    "sap/cdw/components/shell/utility/MessageHandler",
    ["sap/m/MessageBox", "sap/m/MessageToast", "sap/base/util/uid"],
    () => MessageHandler,
    true
  );
}
