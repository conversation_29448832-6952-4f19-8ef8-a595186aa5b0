#XTIT: timeout warning dialog titles
timeoutTitle=¿Sigue ahí?
#XTXT: info text in dialog
timeoutInfo=Parece que ya ha dejado de trabajar y en breve finalizará su sesión. Haga clic en Actualizar si desea mantener la sesión activa.
#XFLD: Renew
timeoutRenewButton=Actualizar
#XFLD: Cancel
timeoutCancelButton=Cancelar
#XMSG: Validation of input field for technical name of model
onlyAlphanumericCharAllowed=Tenga en cuenta que la longitud máxima es de 30 caracteres y que solo se pueden utilizar los siguientes caracteres: a-z, A-Z, 0-9, _.
#XTIT: confirmation title
isDirtyConfirmationTitle=Confirmación
#XMSG
isDirtyConfirmationMessage=¿Seguro que quiere salir? Se perderán las modificaciones.
#XMSG
technicalErrorDetails=Podemos proporcionar la siguiente información para ayudarle a usted o a un ingeniero de SAP a resolver el problema: <br><br>ID de correlación: {0}<br>Estado HTTP: {3}<br>Código de error: {2}<br>Mensaje técnico: {1}
#XMSG
unexpectedErrorMessage=Se ha producido un error inesperado. Disculpe las molestias.
#XFLD
messageTitleError=Error
#XFLD
messageTitleWarning=Advertencia
#XFLD
messageTitleInformation=Información
#XFLD
messageTitleConfirmation=Confirmación
#XMSG
nameValidationDuplicateName=Ya existe un objeto con el mismo nombre.
#XMSG
nameValidationInvalidCharacter=El nombre del objeto contiene un carácter no válido "{0}" en la posición {1}.
#XMSG
nameValidationNameError=El nombre del objeto no es válido.
#XMSG
nameValidationNameStartsWithWhitespace=No se permiten espacios en blanco como primer carácter.
#XMSG
nameValidationWhitespaceAsFirstCharacter=No se permiten espacios en blanco como primer carácter.
#XMSG
nameValidationNameTooLong=La longitud del nombre del objeto supera la longitud máxima de {0}.
#XMSG
nameValidationNameTooShort=El nombre del objeto debe contener al menos {0} caracteres.
#XMSG
nameValidationReservedKeyword=No puede utilizar {0} porque es una palabra clave reservada.
#XMSG
nameValidationReservedPrefix=No puede utilizar {0} porque es un prefijo reservado.
#XMSG
nameValidationWarningReservedPrefix=No utilice el prefijo {0} para evitar posibles conflictos.
#XMSG
nameValidationConfigurationError=El nombre del objeto no es válido.
#XMSG
nameValidationInvalidUUID=El UUID {0} no es válido.
#XMSG
nameValidationDotInInvalidPosition=El nombre de un objeto no debe comenzar ni terminar con un carácter de punto.
#XMSG
nameValidatorUnderscoreAsFirstCharacter=El nombre de un objeto no debe comenzar con un guion bajo.
#XMSG
nameValidatorUnderscoreAsLastCharacter=El nombre de un objeto no debe terminar con un guion bajo.
#XMSG
nameValidationDonameValidationUnderscoreAsFirstOrLastCharactertInInvalidPosition=El nombre de un objeto no debe comenzar ni terminar con un carácter de guion bajo.
#XMSG
nameValidator_InvalidURI=URL no válida
#XFLD: Days
days=Días
#XFLD: Hours
hours=Horas
#XFLD: Minutes
minutes=Minutos
#XFLD: Seconds
seconds=Segundos
#XFLD: Milliseconds
milliseconds=ms
#XFLD: Object status, model has been changed but changes are not yet deployed
@statusChangesToDeploy=Modificaciones para desplegar
#XFLD: Object status, deployed model has run-time errors, example: data type of used source has been changed
@statusRunTimeError=Error en tiempo de ejecución
#XFLD: Object status, deployed model is OK current/changed model is not consistent, example: error in expression of calc. column, after changing the model
@statusDesignTimeError=Error en tiempo de diseño
#XFLD: Object status, model has never been deployed up to now / no deployment date exists
@statusNew=No desplegado
#XFLD: Object status, model has  been deployed / deployment date exists
@statusActive=Desplegado
#XFLD: Deployment status, model has been changed but changes are not yet deployed
@statusRevised=Actualizaciones locales
#XFLD: Deployment status, model deploy failed
@statusFailed=Error
#XFLD: Deployment status
@statusPending=Desplegando...
#XFLD: label for remote data access
REMOTE=Remoto
#XFLD: label for real-time replication data access
REALTIME_REPLICATION=Replicado (en tiempo real)
#XFLD: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Replicado (instantánea)
#XFLD: text for values shown in column Replication Status
txtInitializing=Inicializando
#XFLD: text for values shown in column Replication Status
txtLoading=Cargando
#XFLD: text for values shown in column Replication Status
txtActive=Activa
#XFLD: text for values shown in column Replication Status
txtAvailable=Disponible
#XFLD: text for values shown in column Replication Status
txtError=Error
#XFLD: text for values shown in column Replication Status
txtPaused=En pausa
#XFLD: text for values shown in column Replication Status
txtDisconnected=Desconectada
#XMSG: Error message for unknown replication type that gets logged in Kibana
unknownReplicationTypeError=El tipo de replicación ''{0}'' no es compatible.
#XFLD: placeholder for empty cell
emptyCell=---
#XMSG For Scheduled text
scheduled=Programada
#XBUT: label for radio button in Refresh Frequency Dialog
none=Ninguna
#XBUT: label for radio button Refresh Frequency Dialog
realtime=En tiempo real
#XFLD: Status text for Completed
@statusCompleted=Completada
#XFLD: Status text for Running
@statusRunning=En ejecución
#XFLD: Status text for Stopped
@statusStopped=Parado
#XFLD: Status text for Stopping
@statusStopping=Parando
#XFLD: Status text for Not run
@lblNotRunYet=Todavía no ejecutada
#XFLD: data access type
@defaultAccess=Predeterminado (sistema derivado)
#XFLD: data access type
@remoteAccess=Remoto
#XFLD: data access type
@replicate=Replicar
#XTEXT: Text for Latency
txtLatencyDefault=Predeterminado (sistema predeterminado)
#XTEXT: Text for Latency
txtOneHour=1 hora
#XTEXT: Text for Latency
txtTwoHours=2 horas
#XTEXT: Text for Latency
txtThreeHours=3 horas
#XTEXT: Text for Latency
txtFourHours=4 horas
#XTEXT: Text for Latency
txtSixHours=6 horas
#XTEXT: Text for Latency
txtTwelveHours=12 horas
#XTEXT: Text for Latency
txtOneDay=1 día
#XTEXT: Version Status
version_status_in_process_title=En curso
#XTEXT: Version Status
version_status_in_validation_title=En proceso de validación
#XTEXT: Version Status
version_status_ready_title=Lista para su utilización
#XTEXT: Version Status
version_status_deprecated_title=Obsoleta
#XTEXT: Version Status
version_status_discontinued_title=Descatalogada
#XFLD: Business Builder type
cube_source=Conjunto de datos analíticos
#XFLD: Business Builder type
master_data_source=Dimensión
#XFLD: Business Builder type
hierarchy_source=Jerarquía
#XFLD: Business Builder type
semantic=Modelo de consumo
#XFLD: Business Builder type
kpi_model=Modelo de hechos
#XFLD: Business Builder type
query=Perspectiva
#XFLD: Business Builder type
responsibilityScenario=Escenario de autorización
#XFLD: Show Help
openHelpButtonText=Mostrar ayuda
#XTIT: Deployment Success Msg
deploymentInitiated=Se están desplegando {0} objetos. Le notificaremos cuando se complete el proceso.
#XTIT: Deployment Success Single Object Msg
deploymentSingleInitiated=Se está desplegando {0} objeto. Le notificaremos cuando se complete el proceso.
#XTIT: Deployment Failed info
deployFailed=No se ha podido desplegar
#XTIT: Deployment Perspectives Failed info
deployPerspectivesFailed=No se ha podido desplegar la perspectiva. ID de correlación: {0}
#XTIT: Deployment Multiple Space Error Msg
deploymentMultipleSpaceError=No puede desplegar objetos de más de un espacio.
#XTIT: Deployment No Objects Deployable Error Msg
deploymentMultipleNoObjectsDeployable=No puede desplegar los objetos seleccionados.
#XMSG: Details of deploymentMultipleNoObjectsDeployable
deploymentMultipleNoObjectsDeployableDetails=Los siguientes objetos no admiten el despliegue en masa:<ul><li>modelos E/R, flujos de datos, cadenas de tareas, búsquedas inteligentes</li><li>, perspectivas, modelos de consumo, modelos de hechos</li><li>objetos generados (como las dimensiones de tiempo)</li><li>cualquier objeto con el estado "Error en tiempo de diseño" o "Error en tiempo de ejecución"</li><li>cualquier objeto compartido desde un espacio del que no es miembro.</li></ul>
#XMSG: Details of deploymentMultipleNoObjectsDeployableDetailsBLObjects
deploymentMultipleNoObjectsDeployableDetailsBLObjects=Los siguientes objetos no admiten el despliegue en masa:<ul><li>modelos E/R, flujos de datos, cadenas de tareas, búsquedas inteligentes</li><li>, modelos de consumo, modelos de hechos</li><li>objetos generados (como las dimensiones de tiempo)</li><li>cualquier objeto con el estado "Error en tiempo de diseño" o "Error en tiempo de ejecución"</li><li>cualquier objeto compartido desde un espacio del que no es miembro.</li></ul>
#XTIT: Deploy
artefactDeployingDialogTitle=Desplegar
#XTIT: Deployment Warning info
deployWarningTitle=No puede desplegar modelos E/R, flujos de datos, cadenas de tareas, búsquedas inteligentes, perspectivas, modelos de consumo, modelos de hechos, objetos generados (como las dimensiones de tiempo), cualquier objeto con el estado "Error en tiempo de diseño" o "Error en tiempo de ejecución", cualquier objeto compartido desde un espacio del que no es miembro.
#XTIT: Deployment Warning info when BL Objects FF are enabled
deployWarningTitleBLObjectsFF=No puede desplegar modelos E/R, flujos de datos, cadenas de tareas, búsquedas inteligentes, modelos de hechos o modelos de consumo, objetos generados (como las dimensiones de tiempo), objetos con el estado "Error en tiempo de diseño" o "Error en tiempo de ejecución" u objetos compartidos desde espacios de los que no es miembro.
#XTIT: Deployment Warning info when Taskchain FF enabled
deployWarningTitleTaskChainFF=No puede desplegar modelos E/R, búsquedas inteligentes, perspectivas, modelos de hechos o modelos de consumo, objetos generados (como las dimensiones de tiempo), objetos con el estado "Error en tiempo de diseño" o "Error en tiempo de ejecución" u objetos compartidos desde espacios de los que no es miembro.
#XTIT: Deployment Warning info when Taskchain FF and BL Objects FF are enabled
deployWarningTitleTaskChainFFAndBLObjectsFF=No puede desplegar modelos E/R, búsquedas inteligentes, modelos de hechos o modelos de consumo, objetos generados (como las dimensiones de tiempo), objetos con el estado "Error en tiempo de diseño" o "Error en tiempo de ejecución" u objetos compartidos desde espacios de los que no es miembro.
#XMSG: Details of deploymentMultipleNoObjectsDeployable when Taskchain FF enbaled
deploymentMultipleNoObjectsDeployableDetailsTaskChain=Los siguientes objetos no admiten el despliegue en masa:<ul><li>modelos E/R, búsquedas inteligentes</li><li>, perspectivas, modelos de consumo, modelos de hechos</li><li>objetos generados (como las dimensiones de tiempo)</li><li>cualquier objeto con el estado "Error en tiempo de diseño" o "Error en tiempo de ejecución"</li><li>cualquier objeto compartido desde un espacio del que no es miembro.</li></ul>
#XMSG: Details of deploymentMultipleNoObjectsDeployable when Taskchain FF and BL Objects FF are enabled
deploymentMultipleNoObjectsDeployableDetailsTaskChainBLObjects=Los siguientes objetos no admiten el despliegue en masa:<ul><li>modelos E/R, búsquedas inteligentes</li><li>, modelos de consumo, modelos de hechos</li><li>objetos generados (como las dimensiones de tiempo)</li><li>cualquier objeto con el estado "Error en tiempo de diseño" o "Error en tiempo de ejecución"</li><li>cualquier objeto compartido desde un espacio del que no es miembro.</li></ul>
#XTIT: Business Name Column Label
businessName=Nombre empresarial
#XTIT: Technical Name Column Label
technicalName=Nombre técnico
#XTIT: type Column Label
type=Tipo (uso semántico)
#XTIT: space Column Label
space=Espacio
#XTIT: status Column Label
status=Estado
#XTIT: Deployable objects number info
deployableObjectsText=Se desplegarán {0} objetos de {1}.
#XTIT: Deployable objects number info
deployableObjectText=Se desplegará {0} objeto de {1}.
#XTIT: Deployable objects Title
deployableObjectsTitle=Se puede desplegar
#XTIT: Deployable objects Title
notDeployableObjectsTitle=No se puede desplegar
#XMSG: Search suggestion term. "{0}" is the suggested search term. "{1}" is the suggested data source in which to search for the suggested term. The data source should be enclosed in <i> tags. Example: Hasso <i>in Employees</i>.
resultsIn={0} <i>en {1}</i>
#XFLD
AllDataSource=Todo
#XTIT: Admission Control error dialog title
admissionControlErrorTitle=Control de admisión
#XMSG Admission Control error message text
admissionControlErrorText=La base de datos está ocupada y se ha denegado su solicitud. Inténtelo de nuevo más tarde.
#XMSG Client side HANA rate limit rejection error message text
clientSideHanaRateLimitRejection=Se ha rechazado la solicitud porque hay demasiadas solicitudes de base de datos activas en estos momentos. Inténtelo de nuevo más tarde.

#XTXT Text for date setting
dateFormat1=MMM d, aaaa (Oct 1, 2019)
#XTXT Text for date setting
dateFormat1Short=MMM d, aaaa
#XTXT Text for date setting
dateFormat2=MMM dd, aaaa (01 de octubre de 2019)
#XTXT Text for date setting
dateFormat2Short=MMM dd, aaaa
#XTXT Text for date setting
dateFormat3=aaaa.MM.dd (2019.10.01)
#XTXT Text for date setting
dateFormat3Short=aaaa.MM.dd
#XTXT Text for date setting
dateFormat4=dd.MM.aaaa (01.10.2019)
#XTXT Text for date setting
dateFormat4Short=dd.MM.aaaa
#XTXT Text for date setting
dateFormat5=MM.dd.aaaa (10.01.2019)
#XTXT Text for date setting
dateFormat5Short=MM.dd.aaaa
#XTXT Text for date setting
dateFormat6=aaaa/MM/dd (2019/10/01)
#XTXT Text for date setting
dateFormat6Short=aaaa/MM/dd
#XTXT Text for date setting
dateFormat7=dd/MM/aaaa (01/10/2019)
#XTXT Text for date setting
dateFormat7Short=dd/MM/aaaa
#XTXT Text for date setting
dateFormat8=MM/dd/aaaa (10/01/2019)
#XTXT Text for date setting
dateFormat8Short=MM/dd/aaaa

#XTXT Text for 24 Hour Format - Short
txtTimeFormat24HoursShort=24 h
#XTXT Text for 24 Hour Format
txtTimeFormat24Hours=Formato de 24 horas (16:05:10)
#XTXT Text for 12 Hour Format - Short
txtTimeFormat12HoursShort=12 h
#XTXT Text for 12 Hour Format
txtTimeFormat12Hours=Formato de 12 horas (4:05:10 PM)

#XTIT: title of busy dialog for checking selction before deleting objects
deleteObjectVerifyTitle=Verificando selección
#XTIT: title of confirmation msgbox for deleting an object
deleteObjectConfirmationTitle=Eliminar
#XMSG: message text in confirmation msgbox for deleting a single object
deleteObjectConfirmationText=¿Desea eliminar el archivo seleccionado?
#XMSG: message text in confirmation msgbox for deleting several objects
deleteObjectsConfirmationText=¿Desea eliminar los archivos seleccionados?
#XMSG: error message text after failed to fetch data for deleting several objects
deleteObjectsFetchingError=No se han podido obtener los datos al eliminar el archivo.
#XMSG: error message text confirmation msgbox for deleting objects including folders object
deleteFoldersConfirmationText=Ha seleccionado {0} objetos, incluidas {1} carpetas, para su eliminación.\nSi elimina una carpeta, también eliminará todos los objetos que contenga.\nEsta acción no se puede deshacer.\n\n¿Desea continuar?
#XMSG: message text in confirmation msgbox for deleting a single object
deleteObjectConfirmationText1=Ha seleccionado {0} para su eliminación.\nEsta acción no se puede deshacer.\n\n¿Desea continuar?

#XTIT
mad_delete_title=Eliminar
#XMSG
mad_delete_message=No puede eliminar objetos si no tiene suficientes autorizaciones, si tienen un estado de liberación "Liberado" u "Obsoleto" o si están generados (como dimensiones de tiempo).
mad_delete_message_protected=No puede eliminar objetos si no tiene suficientes autorizaciones, si tienen un estado de liberación "Liberado" u "Obsoleto", si están generados (por ejemplo, dimensiones de tiempo) o si son contenido protegido por SAP en un área de nombres de solo lectura.
mad_delete_message_massDelete=No puede eliminar objetos si no tiene suficientes autorizaciones o si están generados (como dimensiones de tiempo).
#XMSG
mad_delete_summary=Se eliminarán "{0}" objetos de "{1}".
#XBUT
mad_delete_deletable=Se puede eliminar ({0})
#XBUT
mad_delete_not_deletable=No se puede eliminar ({0})
#XBUT
mad_delete_ok=Eliminar
#XBUT
mad_delete_cancel=Cancelar
#XTOL Tooltip for link
openInNewTab=Abrir en una pestaña nueva

#~~~~~~~~~~~ Begin of Texts for TaskSchedulerUtilies ~~~~~~~~~~~~~~~~
#XMSG: Informing user that url is about to redirect for authentication required to create schedule
redirectInfoTextNew=Se requiere la autenticación de usuario para ejecutar las tareas programadas en SAP Datasphere.\n Al hacer clic en Autorizar, da su consentimiento para ejecutar tareas programadas futuras en su nombre.
#XBUT: Authorise
authorise=Autorizar
#XMSG: Error message in authorisation
errorAuthorize=Error de autorización
#~~~~~~~~~~~ End of Texts for TaskSchedulerUtilies ~~~~~~~~~~~~~~~~
txt_enrichProgress={0} columnas restantes...
tit_gen_semantics=Generar semántica
txtCancel=Cancelar
#XMSG
sendAIRequest=Preparando la generación...
#XMSG
getAIResponse=Determinando el uso semántico...
#XMSG
semanticsGenerated=Se ha generado la semántica
#XMSG
txt_enrichDelayed=Reintentando…
#XMSG
noSemanticChange=La generación semántica no ha encontrado ninguna mejora por hacer
