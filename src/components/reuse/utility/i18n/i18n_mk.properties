#XTIT: timeout warning dialog titles
timeoutTitle=Дали сте сè уште тука?
#XTXT: info text in dialog
timeoutInfo=Се чини дека повеќе не работите и наскоро ќе бидете одјавени. Кликнете  Освежи ако сакате да останете најавени.
#XFLD: Renew
timeoutRenewButton=Освежи
#XFLD: Cancel
timeoutCancelButton=Откажи
#XMSG: Validation of input field for technical name of model
onlyAlphanumericCharAllowed=Имајте предвид дека максималната должина е 30 знаци и дека може да се користат само следниве знаци: a – ш, A – Ш, 0 – 9, _.
#XTIT: confirmation title
isDirtyConfirmationTitle=Потврда
#XMSG
isDirtyConfirmationMessage=Дали сигурно сакате да излезете? Промените ќе ви се изгубат.
#XMSG
technicalErrorDetails=Можеме да ги обезбедиме следниве информации за да ви помогнеме вам или на инженерот на SAP да го решите проблемот: <br><br>ИД на корелација: {0}<br>HTTP статус: {3}<br>Код на грешка: {2}<br>Техничка порака:{1}
#XMSG
unexpectedErrorMessage=Настана неочекувана грешка. Извинете за непријатностите.
#XFLD
messageTitleError=Грешка
#XFLD
messageTitleWarning=Предупредување
#XFLD
messageTitleInformation=Информации
#XFLD
messageTitleConfirmation=Потврда
#XMSG
nameValidationDuplicateName=Веќе постои објект со ист назив.
#XMSG
nameValidationInvalidCharacter=Називот на објектот содржи неважечки знак „ {0}“ на позицијата {1}.
#XMSG
nameValidationNameError=Називот на објектот е неважечки.
#XMSG
nameValidationNameStartsWithWhitespace=Не се дозволени празни места како прв знак.
#XMSG
nameValidationWhitespaceAsFirstCharacter=Не се дозволени празни места како прв знак.
#XMSG
nameValidationNameTooLong=Должината на називот на објектот ја надминува максималната должина од {0}.
#XMSG
nameValidationNameTooShort=Називот на објектот мора да содржи најмалку {0} знаци.
#XMSG
nameValidationReservedKeyword=Не можете да го користите {0} бидејќи е резервиран клучен збор.
#XMSG
nameValidationReservedPrefix=Не можете да го користите  {0} бидејќи е резервиран префикс.
#XMSG
nameValidationWarningReservedPrefix=Не користете го префиксот {0} за да избегнете можни конфликти.
#XMSG
nameValidationConfigurationError=Називот на објектот е неважечки.
#XMSG
nameValidationInvalidUUID=UUID {0} е неважечки.
#XMSG
nameValidationDotInInvalidPosition=Називот на објектот не смее да започнува или да завршува со точка.
#XMSG
nameValidatorUnderscoreAsFirstCharacter=Називот на објектот не смее да започнува со долна црта.
#XMSG
nameValidatorUnderscoreAsLastCharacter=Називот на објектот не смее да завршува со долна црта.
#XMSG
nameValidationDonameValidationUnderscoreAsFirstOrLastCharactertInInvalidPosition=Називот на објектот не смее да започнува или да завршува со долна црта.
#XMSG
nameValidator_InvalidURI=URL-адресата е неважечка
#XFLD: Days
days=Денови
#XFLD: Hours
hours=Часови
#XFLD: Minutes
minutes=Минути
#XFLD: Seconds
seconds=Секунди
#XFLD: Milliseconds
milliseconds=мс
#XFLD: Object status, model has been changed but changes are not yet deployed
@statusChangesToDeploy=Промени што треба да се применат
#XFLD: Object status, deployed model has run-time errors, example: data type of used source has been changed
@statusRunTimeError=Грешка во времето на извршувањето
#XFLD: Object status, deployed model is OK current/changed model is not consistent, example: error in expression of calc. column, after changing the model
@statusDesignTimeError=Грешка во времето на дизајнирањето
#XFLD: Object status, model has never been deployed up to now / no deployment date exists
@statusNew=Не е применето
#XFLD: Object status, model has  been deployed / deployment date exists
@statusActive=Применето
#XFLD: Deployment status, model has been changed but changes are not yet deployed
@statusRevised=Локални ажурирања
#XFLD: Deployment status, model deploy failed
@statusFailed=Неуспешно
#XFLD: Deployment status
@statusPending=Се применува...
#XFLD: label for remote data access
REMOTE=На далечина
#XFLD: label for real-time replication data access
REALTIME_REPLICATION=Реплицирано (во реално време)
#XFLD: label for snap-shot replication data access
SNAPSHOT_REPLICATION=Реплицирано (слика на состојбата)
#XFLD: text for values shown in column Replication Status
txtInitializing=Започнување
#XFLD: text for values shown in column Replication Status
txtLoading=Се вчитува
#XFLD: text for values shown in column Replication Status
txtActive=Активно
#XFLD: text for values shown in column Replication Status
txtAvailable=Достапно
#XFLD: text for values shown in column Replication Status
txtError=Грешка
#XFLD: text for values shown in column Replication Status
txtPaused=Паузирано
#XFLD: text for values shown in column Replication Status
txtDisconnected=Не е поврзано
#XMSG: Error message for unknown replication type that gets logged in Kibana
unknownReplicationTypeError=Типот репликација „{0}“ не е поддржан.
#XFLD: placeholder for empty cell
emptyCell=---
#XMSG For Scheduled text
scheduled=Закажано
#XBUT: label for radio button in Refresh Frequency Dialog
none=Ништо
#XBUT: label for radio button Refresh Frequency Dialog
realtime=Реално време
#XFLD: Status text for Completed
@statusCompleted=Завршено
#XFLD: Status text for Running
@statusRunning=Се извршува
#XFLD: Status text for Stopped
@statusStopped=Запрено
#XFLD: Status text for Stopping
@statusStopping=Се запира
#XFLD: Status text for Not run
@lblNotRunYet=Сè уште неизвршено
#XFLD: data access type
@defaultAccess=Стандардно (изведено од системот)
#XFLD: data access type
@remoteAccess=На далечина
#XFLD: data access type
@replicate=Реплицирај
#XTEXT: Text for Latency
txtLatencyDefault=Стандардно (стандардно во системот)
#XTEXT: Text for Latency
txtOneHour=1 час
#XTEXT: Text for Latency
txtTwoHours=2 часа
#XTEXT: Text for Latency
txtThreeHours=3 часа
#XTEXT: Text for Latency
txtFourHours=4 часа
#XTEXT: Text for Latency
txtSixHours=6 часа
#XTEXT: Text for Latency
txtTwelveHours=12 часа
#XTEXT: Text for Latency
txtOneDay=1 ден
#XTEXT: Version Status
version_status_in_process_title=Во тек
#XTEXT: Version Status
version_status_in_validation_title=Се потврдува
#XTEXT: Version Status
version_status_ready_title=Подготвено за употреба
#XTEXT: Version Status
version_status_deprecated_title=Застарено
#XTEXT: Version Status
version_status_discontinued_title=Прекинато
#XFLD: Business Builder type
cube_source=Аналитичка група податоци
#XFLD: Business Builder type
master_data_source=Димензија
#XFLD: Business Builder type
hierarchy_source=Хиерархија
#XFLD: Business Builder type
semantic=Модел на потрошувачка
#XFLD: Business Builder type
kpi_model=Модел на факти
#XFLD: Business Builder type
query=Перспектива
#XFLD: Business Builder type
responsibilityScenario=Сценарио за авторизација
#XFLD: Show Help
openHelpButtonText=Покажи помош
#XTIT: Deployment Success Msg
deploymentInitiated=Применување на {0} објекти. Ќе ве известиме кога процесот ќе заврши.
#XTIT: Deployment Success Single Object Msg
deploymentSingleInitiated=Применување на {0} објект. Ќе ве известиме кога процесот ќе заврши.
#XTIT: Deployment Failed info
deployFailed=Примената не успеа
#XTIT: Deployment Perspectives Failed info
deployPerspectivesFailed=Применувањето на перспективата не успеа. ИД на корелација: {0}
#XTIT: Deployment Multiple Space Error Msg
deploymentMultipleSpaceError=Не може да примените објекти од повеќе од еден простор.
#XTIT: Deployment No Objects Deployable Error Msg
deploymentMultipleNoObjectsDeployable=Не може да примените избрани објекти.
#XMSG: Details of deploymentMultipleNoObjectsDeployable
deploymentMultipleNoObjectsDeployableDetails=Следниве објекти не поддржуваат масовно применување: <ul><li>E/R модели, податочни текови, синџири од задачи, интелигентно пребарување</li><li>перспективи, модели на потрошувачка, модели на факти</li><li> генерирани објекти (како што се димензии на време) </li><li>секој објект со статус „Грешка во времето на дизајнирање“ или „Грешка во времето на извршување“ </li><li>секој споделен објект од простор во кој не сте член</li></ul>
#XMSG: Details of deploymentMultipleNoObjectsDeployableDetailsBLObjects
deploymentMultipleNoObjectsDeployableDetailsBLObjects=Следниве објекти не поддржуваат масовно применување: <ul><li>E/R модели, податочни текови, синџири од задачи, интелигентно пребарување</li><li>модели на потрошувачка, модели на факти</li><li> генерирани објекти (како што се димензии на време) </li><li>секој објект со статус „Грешка во времето на дизајнирање“ или „Грешка во времето на извршување“ </li><li>секој споделен објект од простор во кој не сте член</li></ul>
#XTIT: Deploy
artefactDeployingDialogTitle=Примени
#XTIT: Deployment Warning info
deployWarningTitle=Не може да примените E/R модели, податочни текови, синџир од задачи, интелигентно пребарување, перспектива, модел на потрошувачка, модел на факти, генерирани објекти (како што се димензии на време), кој било објект со статус „Грешка во времето на дизајнирање“ или „Грешка во времето на извршување“, кој било споделен објект од Простор во кој не сте член.
#XTIT: Deployment Warning info when BL Objects FF are enabled
deployWarningTitleBLObjectsFF=Не може да примените E/R модели, податочни текови, синџир од задачи, интелигентни пребарувања, модели на факти или модели на потрошувачка, генерирани објекти (како што се димензии на време), објекти со статус „Грешка во времето на дизајнирање“ или „Грешка во времето на извршување“, или споделени објекти од простори во кои не сте член.
#XTIT: Deployment Warning info when Taskchain FF enabled
deployWarningTitleTaskChainFF=Не може да примените E/R модели, интелигентни пребарувања, перспективи, модели на факти или модели на потрошувачка, генерирани објекти (како што се димензии на време), објекти со статус „Грешка во времето на дизајнирање“ или „Грешка во времето на извршување“, или споделени објекти од простори во кои не сте член.
#XTIT: Deployment Warning info when Taskchain FF and BL Objects FF are enabled
deployWarningTitleTaskChainFFAndBLObjectsFF=Не може да примените E/R модели, интелигентни пребарувања, модели на факти или модели на потрошувачка, генерирани објекти (како што се димензии на време), објекти со статус „Грешка во времето на дизајнирање“ или „Грешка во времето на извршување“, или споделени објекти од простори во кои не сте член.
#XMSG: Details of deploymentMultipleNoObjectsDeployable when Taskchain FF enbaled
deploymentMultipleNoObjectsDeployableDetailsTaskChain=Следниве објекти не поддржуваат масовно применување: <ul><li>E/R модели, интелигентно пребарување</li><li>перспективи, модели на потрошувачка, модели на факти</li><li>генерирани објекти (како што се димензии на време) </li><li>секој објект со статус „Грешка во времето на дизајнирање“ или „ Грешка во времето на извршување“ </li><li>секој споделен објект од Простор во кој не сте член</li></ul>
#XMSG: Details of deploymentMultipleNoObjectsDeployable when Taskchain FF and BL Objects FF are enabled
deploymentMultipleNoObjectsDeployableDetailsTaskChainBLObjects=Следниве објекти не поддржуваат масовно применување: <ul><li>E/R модели, интелигентно пребарување</li><li> модели на потрошувачка, модели на факти</li><li>генерирани објекти (како што се димензии на време) </li><li>секој објект со статус „Грешка во времето на дизајнирање“ или „ Грешка во времето на извршување“ </li><li>секој споделен објект од Простор во кој не сте член</li></ul>
#XTIT: Business Name Column Label
businessName=Деловен назив
#XTIT: Technical Name Column Label
technicalName=Технички назив
#XTIT: type Column Label
type=Тип (Семантичка употреба)
#XTIT: space Column Label
space=Простор
#XTIT: status Column Label
status=Статус
#XTIT: Deployable objects number info
deployableObjectsText={0} од {1} објекти ќе бидат применети.
#XTIT: Deployable objects number info
deployableObjectText={0} од {1} објект ќе биде применет.
#XTIT: Deployable objects Title
deployableObjectsTitle=Може да се применат
#XTIT: Deployable objects Title
notDeployableObjectsTitle=Не може да се применат
#XMSG: Search suggestion term. "{0}" is the suggested search term. "{1}" is the suggested data source in which to search for the suggested term. The data source should be enclosed in <i> tags. Example: Hasso <i>in Employees</i>.
resultsIn={0} <i>во {1}</i>
#XFLD
AllDataSource=Сите
#XTIT: Admission Control error dialog title
admissionControlErrorTitle=Контрола на прием
#XMSG Admission Control error message text
admissionControlErrorText=Базата на податоци е зафатена и барањето е одбиено. Обидете се повторно подоцна.
#XMSG Client side HANA rate limit rejection error message text
clientSideHanaRateLimitRejection=Барањето беше одбиено бидејќи има премногу барања за бази на податоци коишто се активни истовремено. Обидете се повторно подоцна.

#XTXT Text for date setting
dateFormat1=d.MMM.yyyy (1 октомври 2019 година)
#XTXT Text for date setting
dateFormat1Short=d.MMM.yyyy
#XTXT Text for date setting
dateFormat2=МММ дд, гггг (01 октомври 2019 година)
#XTXT Text for date setting
dateFormat2Short=dd.MMM.yyyy
#XTXT Text for date setting
dateFormat3=гггг.ММ.дд (2019.10.01)
#XTXT Text for date setting
dateFormat3Short=гггг.ММ.дд
#XTXT Text for date setting
dateFormat4=дд.ММ.гггг (01.10.2019)
#XTXT Text for date setting
dateFormat4Short=дд.ММ.гггг
#XTXT Text for date setting
dateFormat5=ММ.дд.гггг (10.01.2019)
#XTXT Text for date setting
dateFormat5Short=ММ.дд.гггг
#XTXT Text for date setting
dateFormat6=гггг/М/дд (2019/10/01)
#XTXT Text for date setting
dateFormat6Short=гггг/ММ/дд
#XTXT Text for date setting
dateFormat7=дд/ММ/гггг (01/10/2019)
#XTXT Text for date setting
dateFormat7Short=дд/ММ/гггг
#XTXT Text for date setting
dateFormat8=ММ/дд/гггг (10/01/2019)
#XTXT Text for date setting
dateFormat8Short=dd.MM.yyyy

#XTXT Text for 24 Hour Format - Short
txtTimeFormat24HoursShort=24 ч.
#XTXT Text for 24 Hour Format
txtTimeFormat24Hours=24-часовен формат (16:05:10)
#XTXT Text for 12 Hour Format - Short
txtTimeFormat12HoursShort=12 ч.
#XTXT Text for 12 Hour Format
txtTimeFormat12Hours=12-часовен формат (4:05:10 PM)

#XTIT: title of busy dialog for checking selction before deleting objects
deleteObjectVerifyTitle=Потврдување на изборот
#XTIT: title of confirmation msgbox for deleting an object
deleteObjectConfirmationTitle=Избриши
#XMSG: message text in confirmation msgbox for deleting a single object
deleteObjectConfirmationText=Дали да се избрише избраната датотека?
#XMSG: message text in confirmation msgbox for deleting several objects
deleteObjectsConfirmationText=Дали да се избришат избраните датотеки?
#XMSG: error message text after failed to fetch data for deleting several objects
deleteObjectsFetchingError=Земањето податоци при бришење на датотеката не успеа.
#XMSG: error message text confirmation msgbox for deleting objects including folders object
deleteFoldersConfirmationText=Избравте {0} објекти вклучувајќи {1} папки за бришење.\nБришењето папка ќе ги избрише и сите објекти што ги содржи.\nОва дејство не може да се врати.\n\nДали сакате да продолжите?
#XMSG: message text in confirmation msgbox for deleting a single object
deleteObjectConfirmationText1=Избравте {0} објекти за бришење.\in Ова дејство не може да се врати.\n\\u0414али сакате да продолжите?

#XTIT
mad_delete_title=Избриши
#XMSG
mad_delete_message=Не може да бришете објекти ако немате доволно привилегии, ако имаат состојба на издавање „Издадени“ или „Застарени“ или ако се генерирани (како што се димензиите на времето).
mad_delete_message_protected=Не може да бришете објекти ако немате доволно привилегии, ако имаат состојба на издавање „Издадени“ или „Застарени“, ако се генерирани (како што се димензиите на времето) или ако се заштитена содржина на SAP во поле за назив само за читање.
mad_delete_message_massDelete=Не може да избришете објекти ако немате доволно привилегии или ако се генерираат (како што се димензиите на времето).
#XMSG
mad_delete_summary={0} од {1} објекти ќе се избришат.
#XBUT
mad_delete_deletable=Може да се избрише ({0})
#XBUT
mad_delete_not_deletable=Не може да се избрише ({0})
#XBUT
mad_delete_ok=Избриши
#XBUT
mad_delete_cancel=Откажи
#XTOL Tooltip for link
openInNewTab=Отвори во нова картичка

#~~~~~~~~~~~ Begin of Texts for TaskSchedulerUtilies ~~~~~~~~~~~~~~~~
#XMSG: Informing user that url is about to redirect for authentication required to create schedule
redirectInfoTextNew=Потребна е автентикација на корисникот за извршување на закажаните задачи во SAP Datasphere.\n Со кликнување на Овласти, давате согласност за извршување на идните закажани задачи во ваше име.
#XBUT: Authorise
authorise=Овласти
#XMSG: Error message in authorisation
errorAuthorize=Грешка во овластувањето
#~~~~~~~~~~~ End of Texts for TaskSchedulerUtilies ~~~~~~~~~~~~~~~~
txt_enrichProgress=Преостануваат {0} колони...
tit_gen_semantics=Генерирај семантика
txtCancel=Откажи
#XMSG
sendAIRequest=Се подготвува генерирањето...
#XMSG
getAIResponse=Се одредува семантичката употреба...
#XMSG
semanticsGenerated=Семантиката е генерирана
#XMSG
txt_enrichDelayed=Повторен обид...
#XMSG
noSemanticChange=Семантичкото генерирање не најде подобрувања што треба да се направат
