/** @format */

import { ApplicationId } from "../../tasklog/utility/Constants";

// FILEOWNER: [DSP General]
/** Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved. */

/**
 * Object status (design time, deployment and run time)
 */
export enum ObjectStatus { // see service/repository/types/objectStatus.ts
  notDeployed = 0, // Not deployed
  deployed = 1, // Deployed
  changesToDeploy = 2, // Changes to deploy
  designTimeError = 3, // Design time error
  runTimeError = 4, // Run time errors
  pending = 5, // Deploying... (Pending)
  hasNoObjectStatus = 999, // Object does not provide status (example: kind 'sap.dis.dataflow')
}

/**
 * Execution status (deployment)
 */
export enum DeploymentExecutionStatus {
  success = "success",
  failed = "failed",
  pending = "pending",
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const OBJECT_STATUS_ICONS = {
  [ObjectStatus.notDeployed]: "sap-icon://status-inactive",
  [ObjectStatus.deployed]: "sap-icon://status-positive",
  [ObjectStatus.changesToDeploy]: "sap-icon://status-critical",
  [ObjectStatus.designTimeError]: "sap-icon://status-negative",
  [ObjectStatus.runTimeError]: "sap-icon://status-negative",
  [ObjectStatus.pending]: "sap-icon://pending",
  [ObjectStatus.hasNoObjectStatus]: "sap-icon://less",
};

/**
 * Node Type Icon Mapping
 */
export const NODETYPE_TO_ICON = {
  Entity: "sap-icon://sac/table",
  Output: "sap-icon://sac/table-view",
  Union: "sap-icon://sac/union",
  Filter: "sap-icon://sac/filter",
  RenameElements: "sap-icon://customize",
  Projection: "sap-icon://customize",
  CalculatedElements: "sap-icon://sac/formula",
  RemoveElements: "sap-icon://customize",
  Join: "sap-icon://sac/primary-data-left-joint",
  Table: "sap-icon://sac/table",
  RemoteTable: "sap-icon://sac/live-table",
  Model: "sap-icon://overview-chart",
  View: "sap-icon://sac/table-view",
  Association: "sap-icon://sac/association",
  Aggregation: "sap-icon://sac/sum",
  TransformationFlow: "sap-icon://sac/transform",
  PrimaryViewTransform: "sap-icon://sac/SQL",
  TransformationFlowTarget: "sap-icon://table-chart",
  SecondaryViewTransform: "sap-icon://sac/table-view",
  SQLViewTransform: "sap-icon://sac/SQL",
  GraphicalViewTransform: "sap-icon://sac/table-view",
  TemplateViewTransform: "sap-icon://switch-views",
  DataAccessControl: "sap-icon://permission",
  PythonOperator: "sap-icon://SAP-icons-TNT/python",
};

export const DATATYPE_TO_ICON = {
  // eslint-disable-next-line id-blacklist
  string: "sap-icon://sac/text",
  int8: "sap-icon://sac/number",
  int16: "sap-icon://sac/number",
  int32: "sap-icon://sac/number",
  int64: "sap-icon://sac/number",
  double: "sap-icon://sac/number-decimals",
  date: "sap-icon://appointment-2",
  dateTime: "sap-icon://appointment-2",
  time: "sap-icon://appointment-2",
  timestamp: "sap-icon://appointment-2",
  bool: "sap-icon://sac/boolean",
  decimal: "sap-icon://sac/number-decimals",
  decfloat34: "sap-icon://sac/number-decimals",
  decfloat16: "sap-icon://sac/number-decimals",
  float16: "sap-icon://sac/number-decimals",
  float32: "sap-icon://sac/number-decimals",
  float64: "sap-icon://sac/number-decimals",
  binary: "sap-icon://sac/file",
  uint64: "sap-icon://sac/number",
  uint8: "sap-icon://sac/number",
  geometry: "sap-icon://sac/number",
};

/**
 * CDS Data Type Icon Mapping
 */
export const CDS_TO_ICON = {
  "cds.String": DATATYPE_TO_ICON.string,
  "cds.LargeString": DATATYPE_TO_ICON.string,
  "cds.Integer": DATATYPE_TO_ICON.int32,
  "cds.Integer64": DATATYPE_TO_ICON.int64,
  "cds.Double": DATATYPE_TO_ICON.double,
  "cds.Date": DATATYPE_TO_ICON.date,
  "cds.DateTime": DATATYPE_TO_ICON.dateTime,
  "cds.Time": DATATYPE_TO_ICON.time,
  "cds.Timestamp": DATATYPE_TO_ICON.timestamp,
  "cds.UTCTimestamp": DATATYPE_TO_ICON.timestamp,
  "cds.Boolean": DATATYPE_TO_ICON.bool,
  "cds.Decimal": DATATYPE_TO_ICON.decimal,
  "cds.DecimalFloat": DATATYPE_TO_ICON.decfloat16,
  "cds.Binary": DATATYPE_TO_ICON.binary,
  "cds.LargeBinary": DATATYPE_TO_ICON.binary,
  "cds.UUID": "sap-icon://horizontal-grip",
  "cds.Association": "sap-icon://arrow-right",
  "cds.Composition": "sap-icon://arrow-right",
  "cds.hana.SMALLINT": DATATYPE_TO_ICON.int16,
  "cds.hana.TINYINT": DATATYPE_TO_ICON.int8,
  "cds.hana.SMALLDECIMAL": DATATYPE_TO_ICON.decimal,
  "cds.hana.REAL": DATATYPE_TO_ICON.decimal,
  "cds.hana.NCHAR": DATATYPE_TO_ICON.string,
  "cds.hana.VARCHAR": DATATYPE_TO_ICON.string,
  "cds.hana.BINARY": DATATYPE_TO_ICON.binary,
  "cds.hana.ST_POINT": "sap-icon://sac/location-pin",
  "cds.hana.ST_GEOMETRY": "sap-icon://sac/location-pin",
  calculated: "sap-icon://sac/formula",
  currencyConversion: "sap-icon://lead",
  "abap.char": DATATYPE_TO_ICON.string,
  "abap.clnt": DATATYPE_TO_ICON.string,
  "abap.cuky": DATATYPE_TO_ICON.string,
  "abap.datn": DATATYPE_TO_ICON.date,
  "abap.dats": DATATYPE_TO_ICON.string,
  "abap.geom_ewkb": "sap-icon://sac/feature",
  "abap.lang": DATATYPE_TO_ICON.string,
  "abap.numc": DATATYPE_TO_ICON.string,
  "abap.rawstring": DATATYPE_TO_ICON.binary,
  "abap.sstring": DATATYPE_TO_ICON.string,
  "abap.string": DATATYPE_TO_ICON.string,
  "abap.timn": DATATYPE_TO_ICON.time,
  "abap.tims": DATATYPE_TO_ICON.string,
  "abap.unit": DATATYPE_TO_ICON.string,
  "abap.utclong": DATATYPE_TO_ICON.timestamp,
  "abap.curr": DATATYPE_TO_ICON.decimal,
  "abap.dec": DATATYPE_TO_ICON.decimal,
  "abap.decfloat16": DATATYPE_TO_ICON.decfloat16,
  "abap.decfloat34": DATATYPE_TO_ICON.decfloat34,
  "abap.fltp": DATATYPE_TO_ICON.float32,
  "abap.int1": DATATYPE_TO_ICON.int8,
  "abap.int2": DATATYPE_TO_ICON.int8,
  "abap.int4": DATATYPE_TO_ICON.int8,
  "abap.int8": DATATYPE_TO_ICON.int8,
  "abap.quan": DATATYPE_TO_ICON.decimal,
};

export const SIMPLE_TYPE_ICON = "sap-icon://waiver";

export enum dataAccessType {
  REMOTE = "REMOTE",
  REALTIME_REPLICATION = "REALTIME_REPLICATION",
  SNAPSHOT_REPLICATION = "SNAPSHOT_REPLICATION",
}

export enum BusinessBuilderTypeID {
  cubeSource = 0,
  semantic = 1,
  kpiModel = 2,
  masterDataSource = 3,
  hierarchySource = 4,
  query = 5,
  responsibilityScenario = 6,
}

export enum repositoryObjectBusinessType {
  dimension = "DWC_DIMENSION",
  relationalDataset = "DWC_DATASET",
  analyticalDataset = "DWC_FACT",
  fact = "DWC_SQLFACT",
  businessEntity = "DWC_BUSINESS_ENTITY",
  businessEntityVariant = "DWC_BUSINESS_ENTITY_VARIANT",
  authorizationScenario = "DWC_AUTH_SCENARIO",
  dataAccessControl = "DWC_DAC",
  perspective = "DWC_CUBE",
  consumptionModel = "DWC_CONSUMPTION_MODEL",
  textTable = "DWC_TEXT",
  factModel = "DWC_FACT_MODEL",
  factModelVersion = "DWC_FACT_MODEL_VARIANT",
  hierarchy = "DWC_HIERARCHY",
}

export const ContentTypeIcon = {
  STORY: "sap-icon://sac/stories",
  "STORY.TEMPLATE": "sap-icon://sac/template",
  "STORY.APPLICATION": "sap-icon://sac/details",
  INPUTSCHEDULE: "sap-icon://sac/input-form",
  PRESENTATION_V2: "sap-icon://sac/boardroom",
  CUBE: "sap-icon://sac/models",
  APPLICATION: "sap-icon://sac/details",
  ROAMBI: "sap-icon://sac/roambi",
  FOLDER: "sap-icon://sac/folder",
  DATASET: "sap-icon://sac/data-set",
  POINTOFINTEREST: "sap-icon://sac/marker",
  FILE: "sap-icon://sac/file",
  DWC_REMOTE_TABLE: "sap-icon://sac/live-table",
  DWC_LOCAL_TABLE: "sap-icon://sac/table",
  GRAPHICAL_VIEW: "sap-icon://sac/new-view",
  SQL_VIEW: "sap-icon://sac/SQL",
  DWC_ERMODEL: "sap-icon://sac/relationship-diagram",
  THEMING: "sap-icon://sac/style",
  DWC_WRANG_SESSION: "sap-icon://sac/wrangling",
  DWC_VIEW: "sap-icon://sac/table-view",
  EXTERNALCONTENT: "sap-icon://sac/link",
  DWC_DATAFLOW: "sap-icon://instance",
  DWC_DAC: "sap-icon://permission",
  DWC_TASKCHAIN: "sap-icon://sac/requirement-diagram",
  DWC_IDT: "sap-icon://sac/intelligent-lookup",
  default: "sap-icon://sac/file",
  DWC_ANALYTIC_MODEL: "sap-icon://database",
  DWC_REPLICATIONFLOW: "sap-icon://sac/replication-flow",
  CURRENCY_CONVERSION_VIEWS: "sap-icon://lead",
  UNIT_CONVERSION_VIEWS: "sap-icon://SAP-icons-TNT/unit",
  DWC_TRANSFORMATIONFLOW: "sap-icon://sac/transform",
  SQL_SCRIPT_PROCEDURE: "sap-icon://SAP-icons-TNT/bdd-diagram",
  BW_PROCESS_CHAIN: "sap-icon://SAP-icons-TNT/package-diagram",
  API: "sap-icon://sac/open-connectors",
  NOTIFICATION: "sap-icon://ui-notifications",
};

export const taskLogIconMap = {
  [ApplicationId.DATA_FLOWS]: ContentTypeIcon.DWC_DATAFLOW,
  [ApplicationId.REMOTE_TABLES]: ContentTypeIcon.DWC_REMOTE_TABLE,
  [ApplicationId.VIEWS]: ContentTypeIcon.DWC_VIEW,
  [ApplicationId.TASK_CHAINS]: ContentTypeIcon.DWC_TASKCHAIN,
  [ApplicationId.SPACE]: "sap-icon://sac/data-space-management",
  [ApplicationId.ELASTIC_COMPUTE_NODE]: "sap-icon://sac/data-space-management",
  [ApplicationId.LOCAL_TABLE]: ContentTypeIcon.DWC_LOCAL_TABLE,
  [ApplicationId.INTELLIGENT_LOOKUP]: ContentTypeIcon.DWC_IDT,
  [ApplicationId.TRANSFORMATION_FLOWS]: "sap-icon://sac/transform",
  [ApplicationId.REPLICATION_FLOWS]: ContentTypeIcon.DWC_REPLICATIONFLOW,
  [ApplicationId.SQL_SCRIPT_PROCEDURE]: ContentTypeIcon.SQL_SCRIPT_PROCEDURE,
  [ApplicationId.BW_PROCESS_CHAIN]: ContentTypeIcon.BW_PROCESS_CHAIN,
  [ApplicationId.API]: ContentTypeIcon.API,
  [ApplicationId.NOTIFICATION]: ContentTypeIcon.NOTIFICATION,
};

export const taskLogMonitoringRouteMap = {
  [ApplicationId.DATA_FLOWS]: "dataFlowMonitorTaskLogDetails",
  [ApplicationId.REMOTE_TABLES]: "remoteTableTaskLogDetails",
  [ApplicationId.VIEWS]: "viewMonitorTaskLogDetails",
  [ApplicationId.TASK_CHAINS]: "taskChainMonitorLogDetails",
  [ApplicationId.TRANSFORMATION_FLOWS]: "transformationFlowMonitorDetails",
  [ApplicationId.REPLICATION_FLOWS]: "replicationFlowMonitorTaskLogDetails",
  [ApplicationId.SQL_SCRIPT_PROCEDURE]: "logsmonitoring",
  [ApplicationId.BW_PROCESS_CHAIN]: "logsmonitoring",
  [ApplicationId.API]: "logsmonitoring",
  [ApplicationId.LOCAL_TABLE]: "localTableObjectDetails",
  [ApplicationId.LOCAL_TABLE_VARIANT]: "localTableObjectDetails",
  [ApplicationId.NOTIFICATION]: "logsmonitoring",
};
export const repositoryObjectBusinessTypeI18nMap = {
  [repositoryObjectBusinessType.dimension]: "dimensionType",
  [repositoryObjectBusinessType.analyticalDataset]: "factDatasetType",
  [repositoryObjectBusinessType.fact]: "factDatasetType",
  [repositoryObjectBusinessType.authorizationScenario]: "responsibility_scenario",
  [repositoryObjectBusinessType.factModel]: "kpi_model",
  [repositoryObjectBusinessType.consumptionModel]: "semantic",
  [repositoryObjectBusinessType.perspective]: "query",
};
