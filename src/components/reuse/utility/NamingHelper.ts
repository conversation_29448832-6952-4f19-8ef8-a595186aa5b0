// FILEOWNER: [Modelling]
/** Copyright 2021 SAP SE or an SAP affiliate company. All rights reserved. */

import { ObjectType, validateName } from "@sap/dwc-name-validator";
import { nameLengths, increasedNameLengths } from "@sap/dwc-name-validator/dist/constants";
import { SupportedFeaturesService } from "../../commonmodel/api/SupportedFeaturesService";
import { ensureUniqueness, INameValidatorService, IValidateTechnicalNameOptions, IValidateTechnicalNameResult, IValidationError, NameInputValidator, NameUsage, ValidationErrorCode } from "../../commonui/utility/NameInputValidator";
import { Repo } from "../../shell/utility/Repo";
import { User } from "../../shell/utility/User";
import { ObjectNameDisplay } from "../../userSettings/utility/Constants";

export class NamingHelper {

  private static NameInputValidator: NameInputValidator;

  /**
   * Standard conversion of any string to technical name
   * Only alpha-numeric characters are allowed (plus _ underscore)
   * @param originalName
   */
  static converToDefaultTechnicalName(originalName: string): string {
    const modelRegexp = /[^a-zA-Z0-9_]/g;
    let name: string = originalName;
    if (name?.length) {
      name = name.replace(/\s/g, "_");  // convert spaces to _
      name = name.replace(/\./g, "_");  // convert dots to _
      name = name.normalize("NFKD").replace(/[\u0300-\u036f]/g, "");  // remove accent modifiers (é -> e)
      // Keep only alpha-numeric and _ characters
      name = name.replace(modelRegexp, "");

      // TODO: Remoe leading and trailing underscores??
    }

    // Ensure the name does not become empty (case of non latin characters used in original name!)
    if (name === undefined || name === "") {
      // do not return emtpty string!
      name = "no_name"; // TODO: i18n?
    }

    return name;
  }

  /**
   * Increments a name by adding a suffix integer (used to ensure uniqueness for default name values)
   * @param name - name to increment
   * @param i - value of the increment
   * @param maxLength - maximum length allowed for the name
   */
  static incrementName(name: string, i: number, maxLength?: number) {
    const incrementSuffix = i?.toString() || "";
    let nameToIncrement = name || "";

    if (maxLength) {
      // Ensure incremented name haave correct length
      const incrementLength = incrementSuffix?.length;
      if (nameToIncrement.length + incrementLength > maxLength) {
        nameToIncrement = nameToIncrement.substring(0, maxLength - incrementLength);
      }
    }

    return nameToIncrement + incrementSuffix;
  }

  /**
   * Avoid to replace the CSN of this object, to encode name for data preview
   * @param name - name to encode
   */
  static encodeDataPreviewName(name: string) {
    return name?.replace(/\./g, "%%DOT%%") + "%%END%%";
  }

  /**
   * Decode name for data preview
   * @param name - name to decode
   */
  static decodeDataPreviewName(name: string) {
    return name?.replace(/(%%DOT%%)/g, ".").replace(/(%%END%%)/g, "");
  }

  // ************** Just for association ****************
  static canNewTableModel(model) {
    return ["sap.cdw.ermodeler.Model", "sap.cdw.tableEditor.Model"].includes(model?.qualifiedClassName);
  }

  static canNotShowLinkModel(model) {
    return ["sap.cdw.dataflowmodeler.Model", "sap.cdw.intelligentlookup.Model"].includes(model?.qualifiedClassName);
  }

  static canNotShowLinkObject(object) {
    return object?.classDefinition ? !(object?.isTable || object?.isView || object?.isEntity) : false; // Unresolved association does not have classDefinition since it is not a galilei object yet
  }

  static showLeftTextFormatter(currentObject, object, isLeft) {
    // For view's output and table editor, newly created table in table editor and er model, data flow, intelligent lookup, union, left object in table editor, we don't show link for object
    const model = currentObject?.container?.container || currentObject?.container || object?.resource?.model;
    return !!(
      NamingHelper.canNotShowLinkModel(model)
      || (isLeft && (model?.qualifiedClassName === "sap.cdw.tableEditor.Model"))
      || (object?.classDefinitionName === "Union")
      || (!currentObject && !object) // data flow, intelligent lookup
      || (NamingHelper.canNotShowLinkObject(object))
      || ["sap.cdw.querybuilder.Output"].includes(object?.qualifiedClassName)
      || (NamingHelper.canNewTableModel(model) && object?.isNew));
  }

  static showLeftLinkFormatter(currentObject, leftObject) {
    return !NamingHelper.showLeftTextFormatter(currentObject, leftObject, true);
  }

  static showRightTextFormatter(currentObject, rightObject) {
    return NamingHelper.showLeftTextFormatter(currentObject, rightObject, false);
  }

  static showRightLinkFormatter(currentObject, rightObject) {
    return !NamingHelper.showRightTextFormatter(currentObject, rightObject);
  }
  // ************** Just for association. end ****************

  static getDisplayName(techName: string, businessName) {
    let displayName;
    if (techName) {
      const user = User.getInstance();
      switch (user.getObjectNameDisplay()) {
        case ObjectNameDisplay.businessName:
          displayName = businessName || techName;
          break;
        default:
          displayName = techName;
          break;
      }
    }
    return displayName;
  }

  static getSpaceBusinessNameSync(spaceName, spaces) {
    return spaceName && spaces?.[spaceName];
  }

  static getSpaceDisplayNameSync(spaceName: string, spaces, spaceBusinessName?) {
    let spaceDisplayName;
    if (spaceName) {
      const user = User.getInstance();
      switch (user.getObjectNameDisplay()) {
        case ObjectNameDisplay.businessName:
          spaceDisplayName = spaceBusinessName || NamingHelper.getSpaceBusinessNameSync(spaceName, spaces) || spaceName; // If no space permission, directly use space name at the end
          break;
        default:
          spaceDisplayName = spaceName;
          break;
      }
    }
    return spaceDisplayName;
  }

  static async getSpaceDisplayName(spaceName: string, spaceBusinessName?) {
    let spaceDisplayName;
    if (spaceName) {
      const user = User.getInstance();
      switch (user.getObjectNameDisplay()) {
        case ObjectNameDisplay.businessName:
          let space;
          try {
            space = spaceBusinessName || await Repo.getSpaceDetails(spaceName, ["businessName"]); // No new service request
          } catch {
            space = spaceName;// No space permission
          }
          spaceDisplayName = space?.businessName || spaceBusinessName || spaceName;
          break;
        default:
          spaceDisplayName = spaceName;
          break;
      }
    }
    return spaceDisplayName;
  }

  static async getDWCObjectLinkText(object) {
    if (object) {
      let crossSpaceDisplayName;
      if (object.isCrossSpace) {
        const crossSpaceName = object.crossSpaceName || object.spaceName;
        if (crossSpaceName && (object.crossSpaceDisplayName === crossSpaceName || object.crossSpaceDisplayName === undefined)) {
          crossSpaceDisplayName = await NamingHelper.getSpaceDisplayName(crossSpaceName, object.spaceBusinessName);
        } else {
          crossSpaceDisplayName = object.crossSpaceDisplayName;
        }
      }
      return object.isCrossSpace === true ? (`${object.displayName} (${crossSpaceDisplayName})`) : (object.displayName === undefined ? object.name : object.displayName);
    }
  }

  static getDWCObjectLinkTextSync(object) {
    if (object) {
      return object.isCrossSpace === true ? (`${object.displayName} (${object.crossSpaceDisplayName})`) : (object.displayName === undefined ? object.name : object.displayName);
    }
  }

  static getDWCObjectLinkTooltip(showSimpleTooltip, technicalName?, businessName?, text?, spaces?, spaceName?, spaceBusinessName?) {
    const bundleName = require("../../commonmodel/i18n/i18n.properties");
    const resourceModel = new sap.ui.model.resource.ResourceModel({
      "bundleName": bundleName,
    });
    const resourceBundle = resourceModel.getResourceBundle();

    const spaceDisplayName = NamingHelper.getSpaceDisplayNameSync(spaceName, spaces, spaceBusinessName);

    let tooltip = "";
    const businessTemplate = spaceName === undefined ? "tooltipNamesBusinessName" : "tooltipNamesBusinessNameSpace";
    const technicalTemplate = spaceName === undefined ? "tooltipNamesTechnicalName" : "tooltipNamesTechnicalNameSpace";
    const otherTemplate = spaceName === undefined ? "tooltipNamesOtherName" : "tooltipNamesOtherNameSpace";

    if (showSimpleTooltip) {
      tooltip = resourceBundle.getText("openInNewTab");
    } else {
      if (text === businessName) {
        tooltip = resourceBundle.getText(businessTemplate, [businessName, technicalName, spaceDisplayName]);
      } else if (text === technicalName) {
        tooltip = resourceBundle.getText(technicalTemplate, [businessName, technicalName, spaceDisplayName]);
      } else if (text.includes(businessName) && text.includes(technicalName)) {
        if (businessName.length > technicalName.length) {
          tooltip = resourceBundle.getText(businessTemplate, [businessName, technicalName, spaceDisplayName]);
        } else if (businessName.length < technicalName.length) {
          tooltip = resourceBundle.getText(technicalTemplate, [businessName, technicalName, spaceDisplayName]);
        } else {
          tooltip = resourceBundle.getText(otherTemplate, [businessName, technicalName, spaceDisplayName]);
        }
      } else if (text.includes(businessName)) {
        tooltip = resourceBundle.getText(businessTemplate, [businessName, technicalName, spaceDisplayName]);
      } else if (text.includes(technicalName)) {
        tooltip = resourceBundle.getText(technicalTemplate, [businessName, technicalName, spaceDisplayName]);
      } else {
        tooltip = resourceBundle.getText(otherTemplate, [businessName, technicalName, spaceDisplayName]);
      }
    }
    return tooltip;
  }

  /**
   * Ensures a name is not already used, otherwise increment it by adding sufix as necessary..
   *
   * @param initialName Initial name
   * @param siblings Siblings to check if name is already used
   * @param maxLength (optional) maximum length of the name (to avoid exceeding it after adding increment)
   *
   * Returns a name that is not in the siblings array
   */
  static getUniqueName(initialName: string, siblings: string[], maxLength?: number): string {
    let name = initialName;

    // Check siblings
    if (Array.isArray(siblings) && siblings?.length) {
      let i = 0;
      let incrementedName = name;
      while (siblings.includes(incrementedName)) {
        incrementedName = NamingHelper.incrementName(name, ++i, maxLength);
      }
      name = incrementedName;
    }

    return name;
  }

  /**
   * Computes default technical name from business name
   * Uses standard conversion and cares about length and siblings (for uniqueness)
   *
   * @param businessName
   * @param maxLength
   * @param siblings - Array of technical names of all (other) siblings
   */
  static getDefaultTechnicalNameFromBusinessName(businessName: string, maxLength?: number, siblings?: string[]): string {
    let name: string = NamingHelper.converToDefaultTechnicalName(businessName);

    // Check length
    if (name.length > maxLength) {
      name = name.substring(0, maxLength);
    }

    // Return unique name
    return this.getUniqueName(name, siblings, maxLength);
  }

  /**
   * Return default alias as the last part of dot splitted qualified name
   *
   * @param qualifiedName full name that may cotntain dots
   */
  static getDefaultAliasFromQualifiedName(qualifiedName: string): string {
    let aliasName: string;

    // Undefined is returned if there's no "." in the qualified name!
    if (qualifiedName && qualifiedName.includes(".")) {
      aliasName = qualifiedName.split(".")?.pop();
    }

    return aliasName;
  }

  static resetNameInputValidator(): void { NamingHelper.NameInputValidator = undefined; }
  static getNameInputValidator(): NameInputValidator {
    if (!NamingHelper.NameInputValidator) {
      const service = new CommonNameVaidatorService();
      NamingHelper.NameInputValidator = new NameInputValidator(service);
    }
    return NamingHelper.NameInputValidator;
  }
  static getObjectTypeFromNameUsage(nameUsage: NameUsage): ObjectType {
    let objectType = ObjectType.Object;
    switch (nameUsage) {
      case NameUsage.entity:
        objectType = ObjectType.Entity;
        break;
      case NameUsage.analyticaldataset:
        // ADS is using AnalyticModel name validation rules for now
        // before FF isIncreaseLengthOfTechnicalNamesEnabled() switch to on, this method will not get
        // analyticaldataset as input parameter so we will not put this "case" section after ff
        objectType = ObjectType.AnalyticModel;
        break;
      case NameUsage.element:
        objectType = ObjectType.Element;
        break;
      case NameUsage.parameter:
        objectType = ObjectType.Parameter;
        break;
      case NameUsage.association:
        objectType = ObjectType.Association;
        break;
      case NameUsage.internalhierarchy:
        objectType = ObjectType.InternalHierarchy;
        break;
      case NameUsage.intermediatenode:
        objectType = ObjectType.IntermediateNode;
        break;
      case NameUsage.authorization:
        objectType = ObjectType.Authorization;
        break;
      case NameUsage.space:
        objectType = ObjectType.Space;
        break;
      case NameUsage.UUID:
        objectType = ObjectType.UUID;
        break;
      case NameUsage.databaseuser:
        objectType = ObjectType.DatabaseUser;
        break;
      default:
        objectType = ObjectType.Object;
        break;
    }
    return objectType;
  }

  static getMaxLength(usage: NameUsage) {
    let MAX_LENGTH;
    const isIncreaseLengthOfTechnicalNamesEnabled = SupportedFeaturesService.getInstance().isIncreaseLengthOfTechnicalNamesEnabled();
    switch (usage) {
      case NameUsage.authorization:
        MAX_LENGTH = nameLengths[ObjectType.Authorization];
        break;
      case NameUsage.internalhierarchy:
        MAX_LENGTH = isIncreaseLengthOfTechnicalNamesEnabled ? increasedNameLengths[ObjectType.InternalHierarchy] : nameLengths[ObjectType.InternalHierarchy];
        break;
      case NameUsage.association:
        MAX_LENGTH = nameLengths[ObjectType.Association];
        break;
      case NameUsage.entity:
        MAX_LENGTH = isIncreaseLengthOfTechnicalNamesEnabled ? increasedNameLengths[ObjectType.Entity] : nameLengths[ObjectType.Entity];
        break;
      default:
        MAX_LENGTH = 50;
    }
    return MAX_LENGTH;
  }

  // get name candidates from a qualified name: A.B.C => [C, B.C]
  static getNameCandidatesFromQualifiedName(qualifiedName: string): string[] {
    const nameCandidates = [];
    let dotIndex;
    while ((dotIndex = qualifiedName?.indexOf(".") + 1) > 0) {
      qualifiedName = qualifiedName.substring(dotIndex);
      nameCandidates.push(qualifiedName);
    }
    return nameCandidates;
  }

  static isTimeDimensionName(name: string): boolean {
    return name?.startsWith("SAP.TIME.");
  }
}

export class CommonNameVaidatorService implements INameValidatorService {

  public deriveTechnicalName(nameToDerive: string, usage: NameUsage, options: IValidateTechnicalNameOptions): IValidateTechnicalNameResult {
    const objectType = NamingHelper.getObjectTypeFromNameUsage(usage);
    nameToDerive = nameToDerive || "";
    const config = {
      supportAllForElement: !!options.supportAllForElements,
      supportContext: !!options.supportDotForObject,
      skipValidation: options.computeValidationError !== true,
      skipTrailingChecks: options.skipTrailingChecks === true,
      computeDerivedName: true,
      supportIncreasedMaxLength: !!SupportedFeaturesService.getInstance().isIncreaseLengthOfTechnicalNamesEnabled(),
    };

    const validationResult = validateName(config)(objectType, nameToDerive, options.avoidList);
    if (validationResult.failedValidations) {
      const convertedErrors = this.convertErrors(validationResult.failedValidations);
      let maxlength;
      // check for maxLen in details of error in ConvertedErrors
      convertedErrors.forEach(err => {
        if (err?.details?.maxLen) {
          maxlength = err.details.maxLen;
        }
      });
      return {
        validatedName: validationResult.derivationResults.derivedName,
        errors: convertedErrors,
        maxLength: maxlength,
        // there are multiple errors like below. How do we set the maxLength in the return object. What
        //   [
        //     {
        //         "errorCode": "InvalidName",
        //         "details": {
        //             "maxLen": 50
        //         }
        //     },
        //     {
        //         "errorCode": "InvalidName"
        //     }
        // ]
      };
    } else {
      return {
        validatedName: validationResult.derivationResults.derivedName,
      };
    }
  }

  public ensureUniqueness(proposedName: string, siblings: string[] = [], maxLength: number): string {
    return ensureUniqueness(proposedName, siblings, maxLength);
  }

  private convertErrors(errors: any[]): IValidationError[] {
    const result: IValidationError[] = [];
    errors?.forEach(error => {
      let errorCode, details;
      switch (error.errorCode) {
        case "nameValidator_NameTooShort":
          errorCode = ValidationErrorCode.EmptyName;
          break;
        case "nameValidator_DuplicateString":
          errorCode = ValidationErrorCode.DuplicateName;
          break;
        default:
          errorCode = ValidationErrorCode.InvalidName;
          details = error.matchedSubstrings;
          break;
      }
      if (error?.details) {
        details = error.details;
      }
      if (!result.includes(errorCode)) {
        result.push({ "errorCode": errorCode, "details": details });
      }
    });
    return result;
  }
}
