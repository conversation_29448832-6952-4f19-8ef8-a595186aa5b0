/** @format */

// FILEOWNER: [Modelling.BussinessCatalog]
/** Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved. */

export interface IEntity extends ICsnDefinitionAnnotations {
  space_id?: string;
  space_name?: string;
  id?: string;
  kind?: string;
  business_type?: string;
  technical_type?: string;
  type?: string;
  hash?: string;
  name: string;
  label?: string;
  qualifiedName?: string;
  creator?: any;
  creation_date?: string;
  owner?: any;
  modification_date?: string;
  deployment_date?: string | number | Date;
  isCrossSpace?: boolean;
  qualified_name?: string;
  properties?: any;
  csn?: ICsn;
  "#isViewEntity"?: string | boolean;
  "#deploymentExecutionStatus"?: string;
  "#objectStatus"?: number;
  releaseStateValue?: string;
  releaseDate?: string;
  "#businessType"?: string;
}

// Simple file or composite document types
export enum FileType {
  All = 1,
  View = 2, // View
  RemoteTable = 3, // Remote Table
  LocalTable = 4, // Local Table
  ERModel = 5,
  Story = 6,
  Application = 7,
  DeployedObject = 8,
  DataFlow = 9,
  Cube = 11,
  ESModel = 12,
  DAC = 13,
  IntelligentLookup = 14,
  TaskChain = 15,
  ReplicationFlow = 16,
  TransformationFlow = 17,
  Folder = 18,
  ConceptualModel = 19,
  RemoteConnection = 20,
  DataObject = 21,
  Unknown = -1,
}

export enum AnalysisObjectType {
  Dimension = "dimension",
  Fact = "fact",
  SACFile = "sac/file",
  Measure = "measure",
  Dataset = "dataset",
  SACDataset = "sac/data-set",
  Datasource = "datasource",
}
