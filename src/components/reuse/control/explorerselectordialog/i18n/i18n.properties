# https://wiki.wdf.sap.corp/wiki/display/DWAAS/How+to+add+text+classifications+for+translatable+texts
#
# use the following text types for short texts (up to 120 characters)
# XACT Accessibility
# XBUT Button
# XCKL Checkbox
# XCOL Column header
# XFLD Label
# XGRP Group title
# XLNK Hyperlink
# XLOG Log entry
# XLST List box item
# XMIT Menu item
# XMSG Message
# XRBL Radio button
# XSEL Selection
# XTIT Table title
# XTOL Tooltip
#
# for longer texts (more than 120 characters) use
# YINS Instruction
# YMSG Message

# XFLD
exco_available=Available
# XFLD
exco_selected=Selection {0}
# XBUT
btn_cancel=Cancel
# XBUT
btn_select=Add Objects
# XBUT
btn_next=Next
# XBUT
btn_previous=Previous
# XFLD
dialog_extend_title=Add Objects - Extend Selection
# XFLD
dialog_title=Add Objects
# XMSG
ext_selection_message=Press <strong>Ctrl</strong> (or <strong>Command</strong>) + select / unselect to include / remove its dependencies
# XFLD
ext_selection_col_status=Status
# XFLD
ext_selection_col_requiredby=Required By
#XFLD
search_design_name=Technical Name
#XFLD
search_design_space_id____description=Space ID
#XFLD
search_design_folder_id=Space ID
#XFLD
search_design_space_id=Space ID
#XFLD
search_design_folder_name=Folder Name
#XFLD
search_design_object_status_description=Status
#XFLD
search_design=Object
#XFLD
search_design_plural=Objects
#XFLD
search_design_folder_id____description=Folder Name
#XFLD
search_design_technical_type_business_type=Type (Semantic Usage)
#XFLD
search_design_modification_date=Changed On
#XFLD
search_design_changed_by_user_name=Owner
#XFLD
search_design_source=Source
#XFLD
search_design_creation_date=Created On
#XFLD
search_design_creator_user_name=Creator
#XFLD
search_design_last_accessed_globally=Last Accessed
#XFLD
search_design_deployment_date=Deployed On
#XFLD
search_design_remote_connection_type=Remote Source Connection Type
#XFLD
search_design_business_type_description=Semantic Usage
#XFLD
search_design_technical_type_description=Type
#XFLD
search_design_remote_connection=Connection (Model Import)
#XFLD
search_design_exposed_for_consumption=Expose for Consumption
#XFLD
search_design_is_shared_tag=Sharing
#XFLD
search_design_repository_package=Package
#XFLD
search_design_release_state=Release State
#XFLD
search_design_release_state____description=Release State
#XFLD: Object type is businessEntityVariant
TypeBusinessEntityVariant=Business Entity Version
# XFLD Column businessName
selection_col_name=Name
# XFLD Column Type
selection_col_type=Type
# XFLD Column Location
selection_col_location=Location
#XFLD: Object type is remote connection
TypeRemoteConnection=Remote Connection
#XFLD: Object type is data object
TypeDataObject=Data Object
#XTOL: Sharing info "Shared to My Space"
sharedToMySpace=Shared to My Space
#XTOL: Sharing info "Shared from My Space"
sharedFromMySpace=Shared from My Space
#XFLD
search_design_remote_connection_for_data=Connection (Remote Tables)
