/** @format */

import { RepositoryObjectType } from "@sap/deepsea-types";
import { SearchResultSet } from "@sap/esh-search-ui/dist/types/sap/esh/search/ui/sinaNexTS/sina/SearchResultSet";
import { SearchResultSetItem } from "@sap/esh-search-ui/dist/types/sap/esh/search/ui/sinaNexTS/sina/SearchResultSetItem";
import { SearchResultSetItemAttribute } from "@sap/esh-search-ui/dist/types/sap/esh/search/ui/sinaNexTS/sina/SearchResultSetItemAttribute";
import { SearchResultSetItemAttributeGroup } from "@sap/esh-search-ui/dist/types/sap/esh/search/ui/sinaNexTS/sina/SearchResultSetItemAttributeGroup";

export default class ObjectFormatterFactory {
  static create(Formatter: any, util: any, ctx: { i18n: sap.base.i18n.ResourceBundle; space: string }) {
    const i18n = ctx.i18n;
    const currentSpace = ctx.space;
    class ObjectFormatter extends Formatter {
      private urlTemplates = {
        DWC_ERMODEL: "#/databuilder&/db/{{space_name}}/{{name}}",
        DWC_VIEW: {
          DWC_CUBE: "#/businessbuilder&/bb/{{space_name}}/{{name}}", // ToDo: This line will be obsolete when DWCO_MODELING_CUBE_BUILDER is 'true'
          "*": "#/databuilder&/db/{{space_name}}/{{name}}",
        },
        DWC_BUSINESS_ENTITY: "#/businessbuilder&/bb/{{space_name}}/{{name}}",
        DWC_FACT_MODEL: "#/businessbuilder&/bb/{{space_name}}/{{name}}",
        DWC_CONSUME_MODEL: "#/businessbuilder&/bb/{{space_name}}/{{name}}",
        DWC_CONSUMPTION_MODEL: "#/businessbuilder&/bb/{{space_name}}/{{name}}",
        DWC_PERSPECTIVE: "#/businessbuilder&/bb/{{space_name}}/{{name}}",
        DWC_AUTH_SCENARIO: "#/businessbuilder&/bb/{{space_name}}/{{name}}",
        DWC_BUSINESS_ENTITY_VARIANT: "#/businessbuilder&/bb/{{space_name}}/{{name}}",
        DWC_LOCAL_TABLE: "#/databuilder&/db/{{space_name}}/{{name}}",
        DWC_REMOTE_TABLE: "#/databuilder&/db/{{space_name}}/{{name}}",
        DWC_DATAFLOW: "#/databuilder&/db/{{space_name}}/{{name}}",
        DWC_IDT: "#/databuilder&/db/{{space_name}}/{{name}}",
        DWC_DAC: "#/databuilder&/db/{{space_name}}/{{name}}",
        DWC_TASKCHAIN: "#/databuilder&/db/{{space_name}}/{{name}}",
        DWC_REPLICATIONFLOW: "#/databuilder&/db/{{space_name}}/{{name}}",
        DWC_ANALYTIC_MODEL: "#/databuilder&/db/{{space_name}}/{{name}}",
        DWC_TRANSFORMATIONFLOW: "#/databuilder&/db/{{space_name}}/{{name}}",
        DWC_REMOTECONNECTION: "#/connections&/cn/{{space_name}}",
        // see end of class for kind-specific teplate logic based ob feature flage
      };
      public formatAsync(resultSet: SearchResultSet) {
        for (const item of resultSet.items) {
          this.insertTitleShareIcon(item);
          // Don't show blank () if business_type_description has no value inside the attr group technical_type_business_type
          const attrGroupTechnicalTypeBusinessType = item.detailAttributes?.find(
            (detailAttr) => detailAttr.id === "technical_type_business_type"
          ) as SearchResultSetItemAttributeGroup;
          if (
            typeof attrGroupTechnicalTypeBusinessType === "object" &&
            !item.attributesMap?.["business_type_description"]?.value &&
            attrGroupTechnicalTypeBusinessType?.template
          ) {
            attrGroupTechnicalTypeBusinessType.template = "{technical_type_description}";
          }

          const attributes: any = this.extractAttributes(item);
          this.formatBusinessEntityVariant(attributes, item);
          this.formatFolder(attributes, item);
          this.formatConnection(attributes, item);
          this.formatDataObject(attributes, item);
          const urlTemplate = this.getTemplate(attributes["technical_type"], attributes["business_type"]);
          if (!urlTemplate) {
            continue;
          }
          const url = util.evaluateTemplate(urlTemplate, attributes);
          item.defaultNavigationTarget = resultSet.sina._createNavigationTarget({
            targetUrl: url,
            target: "_blank",
          });
          item.defaultNavigationTarget.parent = item; // 'parent' added to NavigationTargetOptions
        }

        return Promise.resolve(resultSet);
      }

      private getTemplate(technicalType: string, businessType: string): string {
        let template = this.urlTemplates[technicalType];
        if (typeof template === "string") {
          return template;
        }
        if (typeof template === "object") {
          const tmpTemplates = template;
          template = tmpTemplates[businessType];
          if (template) {
            return template;
          }
          template = tmpTemplates["*"];
          if (template) {
            return template;
          }
          return null;
        }
        return null;
      }

      private extractAttributes(item: SearchResultSetItem): any {
        const obj = {};
        for (const attribute of item.attributes) {
          obj[attribute.id] = attribute.value;
        }
        return obj;
      }

      private insertTitleShareIcon(item: SearchResultSetItem): void {
        const isShared = this.findAttribute(item, "is_shared"); // value -> language dependent shared-label
        const isSharedTag = this.findAttribute(item, "is_shared_tag"); // value -> boolean
        if (isShared) {
          let infoIconUrl = "sap-icon://none";
          if (isSharedTag?.value === "true") {
            infoIconUrl = "sap-icon://share-2";
            const spaceId = this.findAttribute(item, "space_name");
            // 'Shared from My Space' / 'Shared to My Space'
            if (currentSpace && currentSpace !== spaceId.value) {
              // eslint-disable-next-line dot-notation
              infoIconUrl = "sap-icon://sac/shared-to-my-space";
              isShared.tooltip = i18n.getText("sharedToMySpace");
              isShared.value = i18n.getText("sharedToMySpace");
              isShared.valueHighlighted = i18n.getText("sharedToMySpace");
              isShared.valueFormatted = i18n.getText("sharedToMySpace");
              // value(s), used for tooltip
            } else if (currentSpace) {
              infoIconUrl = "sap-icon://sac/shared-from-my-space";
              // value(s), used for tooltip
              isShared.tooltip = i18n.getText("sharedFromMySpace");
              isShared.value = i18n.getText("sharedFromMySpace");
              isShared.valueHighlighted = i18n.getText("sharedFromMySpace");
              isShared.valueFormatted = i18n.getText("sharedFromMySpace");
            }
          }
          isShared["infoIconUrl"] = infoIconUrl;
          item.titleAttributes.push(isShared);
        }
      }

      private findAttribute(item: SearchResultSetItem, attributeId: string): SearchResultSetItemAttribute {
        let foundAttribute;
        for (const attribute of item.attributes) {
          if (attributeId === attribute.id) {
            foundAttribute = attribute;
            break;
          }
        }
        return foundAttribute;
      }

      private formatBusinessEntityVariant(attributes, item) {
        if (
          attributes.technical_type == RepositoryObjectType.DWC_BUSINESS_ENTITY_VARIANT &&
          item.attributesMap?.technical_type_description
        ) {
          item.attributesMap.technical_type_description.value = i18n.getText("TypeBusinessEntityVariant");
          item.attributesMap.technical_type_description.valueFormatted = i18n.getText("TypeBusinessEntityVariant");
          item.attributesMap.technical_type_description.valueHighlighted = i18n.getText("TypeBusinessEntityVariant");
        }
      }

      private formatFolder(attributes, item) {
        if (attributes?.technical_type === RepositoryObjectType.DWC_FOLDER) {
          if (item.attributesMap?.name.id === "name") {
            (item.attributesMap.name as any).folderId = item.attributesMap.name.value;
            item.attributesMap.name.value = "";
            item.attributesMap.name.valueFormatted = "";
            item.attributesMap.name.valueHighlighted = "";
          }
        }
      }

      private formatDataObject(attributes, item) {
        if (attributes?.technical_type === RepositoryObjectType.DWC_DATA_OBJECT) {
          if (item.attributesMap.technical_type_description) {
            item.attributesMap.technical_type_description.value = i18n.getText("TypeDataObject");
            item.attributesMap.technical_type_description.valueFormatted = i18n.getText("TypeDataObject");
            item.attributesMap.technical_type_description.valueHighlighted = i18n.getText("TypeDataObject");
          }
          if (item.attributesMap.technical_type_icon) {
            item.attributesMap.technical_type_icon.value = "sap-icon://sac/data-set";
            item.attributesMap.technical_type_icon.valueFormatted = "sap-icon://sac/data-set";
            item.attributesMap.technical_type_icon.valueHighlighted = "sap-icon://sac/data-set";
          }
        }
      }

      private formatConnection(attributes, item) {
        if (!attributes?.technical_type && attributes?.kind === "repository.remote") {
          attributes.technical_type = RepositoryObjectType.DWC_REMOTECONNECTION;
        }
        if (attributes?.technical_type === RepositoryObjectType.DWC_REMOTECONNECTION) {
          if (item.attributesMap.technical_type_description) {
            item.attributesMap.technical_type_description.value = i18n.getText("TypeRemoteConnection");
            item.attributesMap.technical_type_description.valueFormatted = i18n.getText("TypeRemoteConnection");
            item.attributesMap.technical_type_description.valueHighlighted = i18n.getText("TypeRemoteConnection");
          }
          if (item.attributesMap.technical_type_icon) {
            item.attributesMap.technical_type_icon.value = "sap-icon://connected";
            item.attributesMap.technical_type_icon.valueFormatted = "sap-icon://connected";
            item.attributesMap.technical_type_icon.valueHighlighted = "sap-icon://connected";
          }
          if (item.attributesMap.object_status) {
            item.attributesMap.object_status.value = "";
            item.attributesMap.object_status.valueFormatted = "";
            item.attributesMap.object_status.valueHighlighted = "";
          }
          if (item.attributesMap.object_status_description) {
            item.attributesMap.object_status_description.value = "";
            item.attributesMap.object_status_description.valueFormatted = "";
            item.attributesMap.object_status_description.valueHighlighted = "";
            item.attributesMap.object_status_description.iconUrl = "";
          }
          if (item.attributesMap.object_status_icon) {
            item.attributesMap.object_status_icon.value = "";
            item.attributesMap.object_status_icon.valueFormatted = "";
            item.attributesMap.object_status_icon.valueHighlighted = "";
          }
        }
      }
    }

    return new ObjectFormatter();
  }
}
