txt_remote_tables=Віддалені таблиці ({0})
txt_import_remote_tables=Імпорт віддалених таблиць
txtTitle_RemoteSourceChange=Змінити джерело віддалених таблиць
btnNextStep=Наступний крок
btnPreviousStep=Повернутися
btnClose=Закрити
tit_Connections=З’єднання
txt_Connections=З’єднання ({0})
txt_Tables=Таблиці
txt_Recap=Перевірка
txt_Business_name=Комерційна назва
txt_technical_name=Технічне ім’я
txt_ready_for_import=Готовий до імпорту ({0})
txt_available_in_repo=Уже в репозиторії ({0})
txt_type=Тип
txt_location=Місцезнаходження
txt_import_status=Статус імпорту
#XBTN : Deploy
txtImportandDeploy=Імпортувати й розгорнути
txtConfirm=Підтвердити
txtImportOnly=Імпортувати
txt_no_data=Немає даних
txt_import_success=Імпорт успішний
txt_import_failed=Помилка імпорту
txt_to_import=Буде імпортовано
txt_no_import=Не буде імпортовано
changeRemoteTable=Змінити віддалену таблицю
currentRemoteSourceDetails=Поточне джерело віддалених таблиць
newRemoteSourceDetails=Нове джерело віддалених таблиць
#XMSG The Label for connection name.
connectionName=З'єднання:
#XMSG The Message toast  for remote source change.
txtSourceChanged=Віддалене джерело змінено
#XMSG: Toaster to display table up to date
txttableuptodate=Таблицю вже оновлено.
#XMSG The Label for remote source table.
remoteSourceName=Віддалена таблиця:
#XMSG The message for remote source change.
revalidationStrip=Щоб застосувати зміни, необхідно повторно розгорнути віддалену таблицю. Під час розгортання структуру віддаленої таблиці буде повторно перевірено, що може вплинути на залежні об'єкти.
#XMSG The message for remote connection not available.
errorForConnectionNotAvailable=З'єднання наразі недоступне. Ви не можете змінити віддалену таблицю.
errorForConnectionFailure=Не вдалося встановити з''єднання з віддаленим джерелом "{0}"
#XMSG The columns technical names must be unique in tha table.
VAL_TABLE_DUPLICATED_ELT=Технічне ім’я стовпчика має бути унікальним.
#XMSG The columns business names must be unique in tha table.
VAL_TABLE_DUPLICATED_ELT_BUSINESS=Бізнес-ім’я стовпчика має бути унікальним.
#XMSG The column technical name is mandatory.
VAL_TABLE_EMPTY_ELT_REUSE=Виберіть ім’я стовпчика.
#XMSG The entity name exceeds 50 characters
VAL_ENTITY_NAME_EXCEED=Довжина імені перевищує максимальну довжину в 50 символів.
#XMSG The column name already exists in repo.
VAL_TABLE_ELT_DUPLICATE=Об''єкт "({0})" в репозиторії вже існує. Введіть інше ім''я.
VAL_TABLE_ELT_DUPLICATE_CUR=Об''єкт "({0})" вже існує. Введіть інше ім''я.
#XTXT The details link to show the error Message
txt_details=Подробиці
txt_error=Помилка
txt_close=Закрити
txt_loading=Завантаження
txtDelete=Видалити
confirmConnChange=OK
confirmConnectionContent=Справді змінити тип з’єднання? Вибрані параметри буде скинуто.
confirmConnectionTitle=Підтвердження
txt_no_connections=У вашому просторі немає дійсних підключень.
txt_table_cannot_import=Таблицю неможливо імпортувати.
txt_reset_folder=Скинути папку
txt_warningMoreItems=Перелічено лише перші 1000 елементів. Ви можете скористатися пошуком, щоб знайти елементи, яких іще немає в списку.
txt_msgMoreItems=У цій папці понад 1000 елементів. Скористайтеся пошуком, щоб знайти елементи, яких іще немає в списку.
txt_import_sourceObject=Імпортувати вихідний об'єкт
txt_sourceObject=Вихідний об’єкт
txt_countTables=Кількість ({0})
