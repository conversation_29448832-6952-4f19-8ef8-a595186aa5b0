/** @format */

import { IPartitionSet } from "../../../../shared/partitioning/Types";
import { ReplicationStatus } from "../../../../shared/remoteTables/types";
import { BaseController, smartExtend } from "../../basecomponent/controller/BaseController.controller";
import { CDSDataType } from "../../commonmodel/model/types/cds.types";
import { getBigNumber } from "../../reuse/utility/BigNumber";
import { Format } from "../../reuse/utility/Format";
import { MessageHandler } from "../../reuse/utility/MessageHandler";
import { ContentType, HttpMethod, ServiceCall } from "../../reuse/utility/ServiceCall";
import { CDS_TO_ICON } from "../../reuse/utility/Types";
import { ShellContainer } from "../../shell/utility/Container";
import { EventType } from "../../shell/utility/ShellUsageCollectionService";
import { TaskLogClass } from "../../tasklog/controller/TaskLog.controller";
import { ApplicationId, dataAccessType } from "../../tasklog/utility/Constants";
// require("../../../commonmodel/model/common.objectImpl");

export class PartitionDialog extends BaseController {
  view: sap.ui.core.mvc.View;
  i18nModel: sap.ui.model.resource.ResourceModel;
  objectID: string;
  spaceID: string;
  dialog: sap.m.Dialog;
  oDetails: any;
  partitionDialogModel: sap.ui.model.json.JSONModel;
  appID: any;
  taskView: sap.ui.core.mvc.View;
  currentColumn: string;
  savedData: any;
  noOfColumns: number;
  saveButton: sap.m.Button;
  infoModel: sap.ui.model.json.JSONModel;
  isFVT: boolean;
  newPartitionView: sap.m.VBox;
  quickConfirmPopover: any;
  isSecondStepNavigated = false;
  isRatingAvailable: boolean;
  columnsbeforeSorting: any;
  isSuitabilityOn = false;
  fullPayLoadList = [];
  searchText = "";

  public onInit(): void {
    require("../css/style.css");
    super.onInit();
    this.view = this.getView();
    this.setupModels();
  }

  private setupModels() {
    this.view.setModel(new sap.ui.model.json.JSONModel({}));
    this.view.getModel().setSizeLimit(1000);
    // i18n model
    this.i18nModel = new sap.ui.model.resource.ResourceModel({
      bundleName: require("../i18n/i18n.properties"),
    });
    this.view.setModel(this.i18nModel, "i18n");
    this.infoModel = new sap.ui.model.json.JSONModel({});
    this.view.setModel(this.infoModel, "infoModel");
    this.view.setModel(new sap.ui.model.json.JSONModel({}), "partitionSettingsModel");
    this.view.setModel(
      new sap.ui.model.json.JSONModel({ isDefineIntervalEnabled: true, isSaveEnabled: false }),
      "scoredColumnsModel"
    );
  }

  public async processPartitioningDetails(
    objectID: string,
    appID: any,
    spaceID: string,
    title: string,
    taskView,
    viewDataAccess
  ) {
    this.objectID = objectID;
    this.spaceID = spaceID;
    this.appID = appID;
    this.taskView = taskView;
    this.taskView.setBusy(true);
    this.isRatingAvailable = false;
    const busyDialog = new sap.m.BusyDialog("PartitionDialog", {
      text: this.getText("busydialog-partition"),
    });
    this.getView().addDependent(busyDialog);
    busyDialog.open();

    try {
      if (this.appID === ApplicationId.VIEWS) {
        const data = (await this.getPartitionDetails(objectID, appID, spaceID)) as any;
        if (!!data.partitioningColumns) {
          this.getViewPartitionAdvisorDetails();
        }
      } else {
        await this.getPartitionDetails(objectID, appID, spaceID);
      }
    } finally {
      busyDialog.close();
      busyDialog.destroy();
      this.taskView.setBusy(false);
    }

    this.dialog = this.getView().getContent()[0];
    this.dialog.setModel(this.partitionDialogModel);
    this.dialog.setModel(sap.ui.getCore().getModel("featureflags"), "featureflags");
    this.dialog.setTitle(title);
    const remoteDetails = taskView.getModel("detailsModel").getData();
    if (remoteDetails.isFVT) {
      this.isFVT = true;
      this.infoModel.setProperty("/realtimeWarning", this.getText("REAL_TIME_WARNING"));
      this.infoModel.setProperty("/showrealtimeWarning", true);
    } else {
      this.isFVT = false;
      this.infoModel.setProperty("/showrealtimeWarning", false);
    }
    if (this.appID === ApplicationId.VIEWS) {
      this.partitionDialogModel?.setProperty("/partitionDialogHeader", this.getText("SELECTED_VIEW"));
      this.partitionDialogModel?.setProperty("/isView", true);
      this.infoModel.setProperty("/showrealtimeWarning", false);
    } else {
      this.partitionDialogModel?.setProperty("/partitionDialogHeader", this.getText("SELECTED_TABLE"));
      this.partitionDialogModel?.setProperty("/isView", false);
    }
    // for the new partitioning section
    if (!this.newPartitionView) {
      const partitionSection = require("../view/PartitionSection.fragment.xml");
      this.newPartitionView = sap.ui.xmlfragment(
        this.appID + "--partitionSection",
        partitionSection,
        this
      ) as sap.m.VBox;
    }
    if (this.appID !== ApplicationId.VIEWS) {
      this.dialog.removeAllContent();
      this.dialog.addContent(this.newPartitionView);
    }

    if (title?.includes("Create")) {
      this.partitionDialogModel?.setProperty("/ranges", []);
      this.addPartition();
      if (this.appID === ApplicationId.VIEWS) {
        this.addOthersPartition();
      }
    } else if (title?.includes("Edit")) {
      (this.view.getModel("partitionSettingsModel") as sap.ui.model.json.JSONModel).setProperty(
        "/showChangedInfo ",
        false
      );
    }
    this.view.getModel("partitionSettingsModel").setProperty("/isLoadDataVisible", false);
    (this.view.getModel("partitionSettingsModel") as sap.ui.model.json.JSONModel).setProperty("/isEditable", true);
    (this.view.getModel("partitionSettingsModel") as sap.ui.model.json.JSONModel).setProperty("/isActionVisible", true);
    (this.view.getModel("partitionSettingsModel") as sap.ui.model.json.JSONModel).setProperty(
      "/showPersistencyInfo",
      false
    );
    if (viewDataAccess === "Persisted" && title?.includes("Edit")) {
      (this.view.getModel("partitionSettingsModel") as sap.ui.model.json.JSONModel).setProperty(
        "/showPersistencyInfo",
        true
      );
    }

    if (this.noOfColumns > 0) {
      this.dialog.open();
      if (this.appID === ApplicationId.VIEWS) {
        const isCreate = title?.includes("Create");
        const isEdit = title?.includes("Edit");
        this.resetPartitionWizard(isCreate, isEdit);
      }
    } else if (this.appID === ApplicationId.REMOTE_TABLES && title?.includes("Create")) {
      sap.m.MessageBox.information(this.getText("noPartitionColumnsText", [this.objectID]));
    }
    const loadData = this.view.byId("headerToolbarLoadData") as sap.m.Toolbar;
    loadData?.addStyleClass("headerToolBarDisplay");
    this.taskView.setBusy(false);
  }

  resetPartitionWizard(isCreate, isEdit) {
    const wizard = this.view.byId("SelectColumnWizard") as sap.m.Wizard;
    const scoredColumnTable = this.view.byId("scoredColumnsTable") as sap.m.Table;
    const checkbox = this.view.byId("recommendColumnCheckbox") as sap.m.CheckBox;
    const searchField = this.view.byId("searchScoredColumns") as sap.m.SearchField;
    wizard.discardProgress(wizard.getSteps()[0], false);
    checkbox?.setSelected(false);
    searchField.setValue("");
    this.isSecondStepNavigated = false;
    if (isCreate) {
      scoredColumnTable.removeSelections(true);
      this.handleButtonVisibility(0);
    } else if (isEdit) {
      this.resetScoredColumnSelection();
      this.goToSecondStep();
      this.handleButtonVisibility(1);
    }
  }

  resetScoredColumnSelection() {
    const scoredColumnTable = this.view.byId("scoredColumnsTable") as any;
    const selectedColumn = this.partitionDialogModel.getProperty("/column");
    const listItems = scoredColumnTable.getItems();
    const model = this.view.getModel("scoredColumnsModel");
    const selectItem = listItems.find(
      (item) => model.getProperty(item.getBindingContextPath()).Name === selectedColumn
    );
    if (selectItem) {
      scoredColumnTable.setSelectedItem(selectItem);
    } else {
      scoredColumnTable.removeSelections();
    }
  }

  goToSecondStep() {
    const wizard = this.view.byId("SelectColumnWizard") as sap.m.Wizard;
    if (this.isSecondStepNavigated) {
      const oNextStep = wizard.getSteps()[1];
      wizard.goToStep(oNextStep, true);
    } else {
      wizard.nextStep();
      this.isSecondStepNavigated = true;
    }
  }

  public async getPartitionDetails(objectID: string, appID: any, spaceID: string) {
    return new Promise((resolve) => {
      this.appID = appID;
      let objectType = "/remoteTables/";
      if (appID === ApplicationId.VIEWS) {
        objectType = "/persistedViews/";
      }
      const sUrlStart = "partitioning/" + spaceID + objectType + encodeURIComponent(objectID);

      ServiceCall.request<IPartitionSet>({
        url: sUrlStart,
        type: HttpMethod.GET,
        contentType: ContentType.APPLICATION_JSON,
      })
        .then((oDetails) => {
          this.processData(oDetails.data);
          resolve(oDetails.data);
        })
        .catch((error) => {
          this.view.setBusy(false);
          MessageHandler.exception({
            exception: error,
            message: this.getText("partitionDataFetchError"),
          });
          resolve({});
        });
    });
  }

  private filterScoredPartitions(isSuggested: boolean, searchName?: string) {
    const scoredColumnsList = [];
    const model = this.view.getModel("scoredColumnsModel");
    const scoredColumns = model.getProperty("/scoredColumnsObject");

    for (const column in scoredColumns) {
      let displayType;
      try {
        displayType = sap.cdw.commonmodel.ObjectImpl.computeDisplayType(
          scoredColumns[column],
          scoredColumns[column].type
        );
      } catch {
        displayType = scoredColumns[column].type;
      }
      scoredColumnsList.push({
        Name: column,
        Rating: scoredColumns[column].rating,
        Veto: scoredColumns[column].veto,
        Datatype: displayType,
        suitable: this.getText(scoredColumns[column]?.suitable),
        details: this.processDetails(scoredColumns[column]?.details),
        viewAll: this.processDetails(scoredColumns[column]?.details)?.length > 1 ? true : false,
        detailsLinkText: this.getText("VIEW_ALL", [this.processDetails(scoredColumns[column]?.details)?.length as any]),
        detailsText: this.getText(
          this.processDetails(scoredColumns[column]?.details)[0]?.key,
          this.processDetails(scoredColumns[column]?.details)[0]?.param
        ),
        isKey: scoredColumns[column]?.key ? true : false,
      });
    }

    let filterList = scoredColumnsList
      .filter((column) => !column.Veto)
      .filter((column) => (isSuggested ? column.Rating >= 0 : true))
      .filter((column) =>
        searchName && searchName.length > 0
          ? column.Name.toLowerCase().search(searchName.trim().toLowerCase()) > -1
          : true
      );
    filterList = isSuggested ? filterList.sort((column1, column2) => column2.Rating - column1.Rating) : filterList;
    return filterList;
  }

  private processDetails(details: any) {
    const detailsList = [];
    if (details?.length > 0) {
      details?.forEach((element) => {
        detailsList.push({ key: element.detailsCode, param: element.parameters });
      });
      if (details?.length > 1) {
        this.view
          .getModel("scoredColumnsModel")
          .setProperty("/detailsLinkText", this.getText("VIEW_ALL", details?.length));
      }
    }
    return detailsList;
  }

  public getSemanticColorValue(suitable): string {
    switch (suitable) {
      case this.getText("DPW_SUIT_BAD"):
        return "Error";
      case this.getText("DPW_SUIT_GOOD"):
        return "Success";
      case this.getText("DPW_SUIT_MED"):
        return "Warning";
      default:
        return "None";
    }
  }

  private async getScoredColumnDetails(objectID: string, spaceID: string) {
    return new Promise((resolve, reject) => {
      const sUrl = `advisor/${spaceID}/partitioncolumn/${objectID}`;
      ServiceCall.request<IPartitionSet>({
        url: sUrl,
        type: HttpMethod.GET,
        contentType: ContentType.APPLICATION_JSON,
      })
        .then((oDetails) => {
          const model = this.view.getModel("scoredColumnsModel");
          const scoredColumns = (oDetails.data as any).scoredColumns;
          const errors = (oDetails.data as any).errors;
          const scoredColumnsObject = model.getProperty("/scoredColumnsObject");
          for (const column in scoredColumnsObject) {
            scoredColumnsObject[column].rating = scoredColumns[column]?.rating;
            scoredColumnsObject[column].veto = scoredColumns[column]?.veto;
            scoredColumnsObject[column].suitable = scoredColumns[column]?.suitable;
            scoredColumnsObject[column].details = scoredColumns[column]?.details;
          }

          model.setProperty("/payload", (oDetails.data as any).payload);

          const scoredColumnsList = this.filterScoredPartitions(false);
          model.setProperty("/columns", scoredColumnsList);
          model.setProperty("/hasErrors", errors.length > 0);
          model.setProperty("/errors", errors);
          resolve(oDetails.data);
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  public async getViewPartitionAdvisorDetails() {
    const columnsList = this.partitionDialogModel.getProperty("/partitioningColumns");
    const model = this.view.getModel("scoredColumnsModel");
    model.setProperty("/scoredColumnsObject", columnsList);
    const scoredColumnsList = this.filterScoredPartitions(false);
    model.setProperty("/columns", scoredColumnsList);
    this.view.getModel("scoredColumnsModel").setProperty("/showColumns", false);
    const colNumber = scoredColumnsList.length as any;

    this.view.getModel("scoredColumnsModel").setProperty("/ColumnsText", this.getText("ColumnsLabel", colNumber));
  }

  public async savePartitionDetails() {
    const data = this.partitionDialogModel.getData();
    // Check if high value of last range is empty for view monitor and remote table monitor
    const lastRange = data.ranges[data.ranges.length - 1];
    const penultRange = data.ranges[data.ranges.length - 2];
    if (
      (lastRange?.id !== "" && lastRange?.high.value === "") || // View monitor
      (lastRange?.id === "" && penultRange?.high.value === "") // Remote table monitor
    ) {
      this.validateAllBounds();
      this.partitionDialogModel.updateBindings(true);
      return;
    }
    this.dialog.close();
    this.taskView.setBusy(true);
    const taskCont = this.taskView.getController() as TaskLogClass;
    let objectType = "/remoteTables/";
    if (this.appID === ApplicationId.VIEWS) {
      objectType = "/persistedViews/";
    }
    const sUrlStart = "partitioning/" + this.spaceID + objectType + encodeURIComponent(this.objectID);
    const displayData = Object.assign({}, data);
    this.processPostData(data);
    try {
      const response = await ServiceCall.request<any>({
        url: sUrlStart,
        type: HttpMethod.POST,
        contentType: ContentType.APPLICATION_JSON,
        data: JSON.stringify(data),
      });
      sap.m.MessageToast.show(this.getText("PARTITION_SAVED"));
      if (this.dialog.getTitle() === "Create Partition") {
        taskCont.objectDetailsRefresh();
      } else {
        displayData.isChanged = response?.data?.isChanged;
        taskCont.partitionRefresh(displayData);
      }
      this.taskView.setBusy(false);
    } catch (e) {
      this.taskView.setBusy(false);
      taskCont.objectDetailsRefresh();
      MessageHandler.exception({
        exception: e,
        message: this.getText("savefailed"),
      });
      return [];
    }
  }

  private processData(data: IPartitionSet) {
    this.view.setBusy(false);
    this.oDetails = data;
    this.savedData = JSON.parse(JSON.stringify(data));
    const numArr = [];
    if (!this.partitionDialogModel) {
      this.partitionDialogModel = new sap.ui.model.json.JSONModel(data);
    } else {
      this.partitionDialogModel.setData(data);
    }
    const columnLength = Object.keys(data.partitioningColumns).length;
    if (columnLength > 0) {
      if (columnLength > 100) {
        this.partitionDialogModel.setSizeLimit(Object.keys(data.partitioningColumns).length);
      }
      if (data.column === "") {
        data.column = Object.keys(data.partitioningColumns)[0];
      }
      data.runtimeDataCalculation = data.partitioningColumns[data.column].runtimeDataCalculation;
      data.columnType = data.partitioningColumns[data.column].type;
      this.noOfColumns = Object.keys(data.partitioningColumns).length;
      this.currentColumn = data.column;
    } else {
      this.noOfColumns = 0;
    }
    if (data.ranges.length > 1) {
      for (let i = 0; i < data.ranges.length - 1; i++) {
        const elem = data.ranges[i];
        if (elem && elem.high) {
          elem.high.editable = false;
        }
      }
    }
    if (data?.ranges?.length >= 1) {
      const index = data?.ranges?.length - 1;
      data.ranges[index].high.editable = true; // set last row editable for views with others partition
      for (let i = 0; i < data.ranges.length; i++) {
        const elem = data.ranges[i];
        if (elem && data.columnType === CDSDataType.DATE) {
          elem.low.value = this.formatDate(elem.low.value);
          elem.high.value = this.formatDate(elem.high.value);
        }
        if (elem && (data.columnType === CDSDataType.DATETIME || data.columnType === CDSDataType.TIMESTAMP)) {
          elem.low.value = this.formatDateTime(elem.low.value);
          elem.high.value = this.formatDateTime(elem.high.value);
        }
      }
    }
    this.infoModel.setProperty("/showErrorStrip", false);
    if (data.partitioningMessage && data.partitioningMessage.length > 0) {
      this.infoModel.setProperty("/info", this.getText(data.partitioningMessage[0].message));
      this.infoModel.setProperty("/showErrorStrip", true);
    }
    data.isFVT = data.isFVT ? data.isFVT : false;

    // numArr.push({ name: 0 });
    if (data.maxNumParallelPartitions) {
      for (let i = 0; i < data.maxNumParallelPartitions; i++) {
        numArr.push({
          name: i + 1,
        });
      }
      const partitioningColumnsListKeys = [
        ...Object.keys(data.partitioningColumns).filter((e) => data.partitioningColumns[e].key),
        ...Object.keys(data.partitioningColumns).filter((e) => !data.partitioningColumns[e].key),
      ]
        // browser does not support entering or pasting binary values
        .filter((e) => data.partitioningColumns[e].type !== CDSDataType.BINARY);
      this.partitionDialogModel.setProperty("/partitioningColumnsList", partitioningColumnsListKeys);
      // to show the number of parallel process supported
      this.partitionDialogModel.setProperty("/parallelPartitionSeq", numArr);
      // disable save on creation
      this.setSaveButtonEnabled(false);
      this.noOfColumns = partitioningColumnsListKeys.length;
    }
    if (this.appID === ApplicationId.VIEWS) {
      this.addOthersPartition();
    }
    this.view.getModel("scoredColumnsModel")?.setProperty("ColumnsLabel", "Columns (" + this.noOfColumns + ")");
  }

  private processPostData(data) {
    delete data.parallelPartitionSeq;
    delete data.maxNumParallelPartitions;
    delete data.partitioningColumns;
    delete data.partitioningSupported;
    delete data.isFVT;
    delete data.partitioningColumnsList;
    delete data.notNull;
    delete data.partitionDialogHeader;
    delete data.isView;
    delete data.partitioningMessage;
    delete data.keyColumns;
    delete data.isFVT;
    delete data.isChanged;
    delete data.hanaDataType;
    delete data.columnIsNullable;
    delete data.isPartitioningColumnValid;
    let isRemotetableData = false;
    if (this.appID === ApplicationId.REMOTE_TABLES) {
      isRemotetableData = true;
    }
    data.ranges = data.ranges.filter((row) => row.isOthers !== true);
    data.ranges.forEach((element) => {
      delete element.low.editable;
      delete element.high.editable;
      delete element.low.valueState;
      delete element.high.valueState;
      delete element.low.valueStateText;
      delete element.high.valueStateText;
      delete element.rangeVisible;
      delete element.isOthers;
      if (isRemotetableData) {
        delete element.locked;
      }
      if (data.columnType === "cds.Date") {
        element.low.value = this.formatDate(element.low.value);
        element.high.value = this.formatDate(element.high.value);
      }
      if (data.columnType === "cds.DateTime" || data.columnType === "cds.Timestamp") {
        element.low.value = this.formatDateTime(element.low.value);
        element.high.value = this.formatDateTime(element.high.value);
      }
    });
    data.numParallelPartitions = JSON.parse(data.numParallelPartitions);
  }

  public onColumnChange() {
    const rangeData = this.partitionDialogModel.getProperty("/ranges");
    if (rangeData.length >= 1 && rangeData[0]?.low?.value !== "") {
      sap.m.MessageBox.confirm(this.getText("columnChangeConfirmationTxt"), {
        onClose: (action) => {
          if (action === sap.m.MessageBox.Action.OK) {
            this.partitionDialogModel.setProperty("/ranges", []);
            this.addPartition();
            if (this.appID === ApplicationId.VIEWS) {
              this.addOthersPartition();
            }
            this.currentColumn = this.partitionDialogModel.getData().column;
            this.processColumnChange();
            this.setSaveButtonEnabled(false);
          } else {
            this.partitionDialogModel.setProperty("/column", this.currentColumn);
            this.partitionDialogModel.updateBindings(true);
            return;
          }
        },
      });
    } else {
      this.currentColumn = this.partitionDialogModel.getData().column;
      this.processColumnChange();
    }
  }

  private processColumnChange() {
    const { runtimeDataCalculation, type: columnType } =
      this.partitionDialogModel.getProperty("/partitioningColumns")[this.currentColumn];
    this.partitionDialogModel.setProperty("/runtimeDataCalculation", runtimeDataCalculation);
    this.partitionDialogModel.setProperty("/columnType", columnType);
  }

  public onParallelProcessChange() {
    this.validateAllBounds();
  }

  public onLowValueChangeLive(event) {
    const source = event.getSource();
    const currRange = source.getBindingContext().getObject();
    const currValue = event.getParameter("value");
    const rangeData = this.partitionDialogModel.getProperty("/ranges");
    const previousRange = rangeData[currRange.id - 2];
    // update previous range high value same as current range low value
    if (previousRange) {
      previousRange.high.value = currValue;
    }
    this.onChange(currValue, currRange, false);
  }

  public onHighValueChangeLive(event) {
    const input: sap.m.Input = event.getSource() as sap.m.Input;
    const currRange = input.getBindingContext().getObject();
    const currValue = event.getParameter("value") as number;
    currRange.high.value = currValue;
    this.onChange(currValue, currRange, true);
  }

  public onValueChange(event, isHighValue: boolean) {
    const input: sap.m.Input = event.getSource() as sap.m.Input;
    const currRange = input.getBindingContext().getObject();
    const currValue = event.getParameter("value") as number;
    this.onChange(currValue, currRange, isHighValue);
  }

  public onChange(currValue, currRange, isHighValue: boolean) {
    let currLowValue, currHighValue, rangeData, previousRange, prevLowValue;
    let inputName;
    let columnName = this.partitionDialogModel.getProperty("/column");
    if (columnName === "") {
      const combo = this.byId("columnCombo") as sap.m.Select;
      columnName = combo.getSelectedKey();
    }
    const columnDetails = this.partitionDialogModel.getProperty("/partitioningColumns")[columnName];
    const validationMsg = this.validateUserInputValue(columnDetails, currValue);
    const type = columnDetails.validationType;
    if (isHighValue) {
      inputName = "high";
      currHighValue = currValue;
      currLowValue = currRange.low.value;
    } else {
      inputName = "low";
      currLowValue = currValue;
      currHighValue = currRange.high.value as number;
      rangeData = this.partitionDialogModel.getProperty("/ranges");
      previousRange = rangeData[currRange.id - 2];
      if (previousRange && !isHighValue) {
        previousRange.high.value = currValue;
      }
      prevLowValue = previousRange ? previousRange.low.value : null;
    }

    let dataTypeValid = true;
    let isSaveEnabled = true;

    // Check for empty values
    let hasError = false;
    if (currLowValue === "" || currHighValue === "") {
      currRange[inputName].valueState = sap.ui.core.ValueState.Error;
      currRange[inputName].valueStateText = this.getText("emptyBoundValuesErrorMsg");
      isSaveEnabled = false;
      hasError = true;
    }
    if (validationMsg !== "") {
      currRange[inputName].valueState = sap.ui.core.ValueState.Error;
      currRange[inputName].valueStateText = validationMsg;
      isSaveEnabled = false;
      dataTypeValid = false;
    } else {
      // check validation type - if cds.string do string like validation
      // else do number like validation
      if (
        type !== CDSDataType.STRING &&
        type !== CDSDataType.DATE &&
        type !== CDSDataType.DATETIME &&
        type !== CDSDataType.TIMESTAMP
      ) {
        currLowValue = +currLowValue;
        currHighValue = +currHighValue;
        if (prevLowValue) {
          prevLowValue = +prevLowValue;
        }
      }

      // to check if lower bound larger than higher bound
      if (currLowValue >= currHighValue && !hasError) {
        currRange[inputName].valueState = sap.ui.core.ValueState.Error;
        if (type !== CDSDataType.STRING) {
          currRange[inputName].valueStateText = isHighValue
            ? this.getText("higherbounderrormsg")
            : this.getText("lowerbounderrormsg");
        } else {
          currRange[inputName].valueStateText = isHighValue
            ? this.getText("higherbounderrormsgforString", [currLowValue, currHighValue])
            : this.getText("lowerbounderrormsgforString", [currLowValue, currHighValue]);
        }
        isSaveEnabled = false;
        hasError = true;
      }
      if (prevLowValue && currLowValue <= prevLowValue && !hasError) {
        currRange[inputName].valueState = sap.ui.core.ValueState.Error;
        currRange[inputName].valueStateText = this.getText("lowerboundoverlaperrormsg");
        isSaveEnabled = false;
        hasError = true;
      }
      if (!hasError) {
        currRange.high.valueState = sap.ui.core.ValueState.None;
        currRange.high.valueStateText = "";
        currRange.low.valueState = sap.ui.core.ValueState.None;
        currRange.low.valueStateText = "";
      }
    }
    this.setSaveButtonEnabled(isSaveEnabled);
    if (dataTypeValid) {
      this.validateAllBounds();
    }
    this.partitionDialogModel.updateBindings(true);
  }

  private setSaveButtonEnabled(isEnabled) {
    if (!this.saveButton) {
      this.saveButton = this.getView().byId("saveData") as sap.m.Button;
    }
    this.saveButton?.setEnabled(isEnabled);
  }

  private validateAllBounds() {
    let rangeData = this.partitionDialogModel.getProperty("/ranges");
    let column = this.partitionDialogModel.getProperty("/column");
    if (column === "") {
      const combo = this.byId("columnCombo") as sap.m.Select;
      column = combo.getSelectedKey();
    }
    const columnDetails = this.partitionDialogModel.getProperty("/partitioningColumns")[column];
    const type = columnDetails.validationType;

    let currLowValue, currHighValue;
    rangeData = rangeData.filter((elem) => elem.isOthers !== true);
    rangeData.every((elem) => {
      currLowValue = elem.low.value;
      currHighValue = elem.high.value;
      if (currLowValue === "" || currHighValue === "") {
        if (currLowValue === "") {
          elem.low.valueState = sap.ui.core.ValueState.Error;
          elem.low.valueStateText = this.getText("emptyBoundValuesErrorMsg");
        } else {
          elem.high.valueState = sap.ui.core.ValueState.Error;
          elem.high.valueStateText = this.getText("emptyBoundValuesErrorMsg");
        }
        this.setSaveButtonEnabled(false);
        return false;
      } else if (currLowValue !== "" && currHighValue !== "") {
        const validationMsgLow = this.validateUserInputValue(columnDetails, currLowValue);
        const validationMsgHigh = this.validateUserInputValue(columnDetails, currHighValue);
        if (validationMsgLow !== "") {
          elem.low.valueState = sap.ui.core.ValueState.Error;
          elem.low.valueStateText = validationMsgLow;
          this.setSaveButtonEnabled(false);
          return false;
        }
        if (validationMsgHigh !== "") {
          elem.high.valueState = sap.ui.core.ValueState.Error;
          elem.high.valueStateText = validationMsgHigh;
          this.setSaveButtonEnabled(false);
          return false;
        }
        if (
          type !== CDSDataType.STRING &&
          type !== CDSDataType.DATE &&
          type !== CDSDataType.DATETIME &&
          type !== CDSDataType.TIMESTAMP
        ) {
          currLowValue = +currLowValue;
          currHighValue = +currHighValue;
        }
        if (currLowValue < currHighValue) {
          const index = rangeData.indexOf(elem);
          let prevLowValue = index > 0 ? rangeData[index - 1].low.value : undefined;
          if (
            type !== CDSDataType.STRING &&
            type !== CDSDataType.DATE &&
            type !== CDSDataType.DATETIME &&
            type !== CDSDataType.TIMESTAMP &&
            prevLowValue
          ) {
            prevLowValue = +prevLowValue;
          }
          if (!prevLowValue || (prevLowValue && currLowValue > prevLowValue)) {
            elem.low.valueState = sap.ui.core.ValueState.None;
            elem.low.valueStateText = "";
            elem.high.valueState = sap.ui.core.ValueState.None;
            elem.high.valueStateText = "";
            this.setSaveButtonEnabled(true);
          }
          return true;
        } else {
          elem.low.valueState = sap.ui.core.ValueState.Error;
          elem.low.valueStateText = this.getText("lowerbounderrormsg");
          this.setSaveButtonEnabled(false);
          return false;
        }
      } else {
        // enable for string values where there is no scope of comparison.
        this.setSaveButtonEnabled(true);
        return true;
      }
    });
    if (rangeData.length === 0 || column === "") {
      this.setSaveButtonEnabled(false);
    }
  }

  private validateUserInputValue(element: any, defaultVal: any) {
    let message = "";
    const type = element.validationType;
    // check for negative values in case of FVT
    if (this.isFVT && element.validationType !== CDSDataType.STRING && defaultVal < 0) {
      message = this.getText("negativeValueErrorMsg");
    }
    if (element.type === CDSDataType.STRING || element.type === CDSDataType.HANA_NCHAR) {
      if (defaultVal.length > element.length) {
        message = this.getText("VAL_LENGTH_EXCEED", element.length);
      }
    } else if (element.type === CDSDataType.HANA_TINYINT) {
      if (defaultVal.toString().match(/^([+]?[0-9]\d*|0)$/g) !== null) {
        if (!(Number(defaultVal) >= 0 && Number(defaultVal) <= 255)) {
          message = this.getText("VAL_DEFAULT_RANGE_EXCEED_TINYINT");
        }
      } else {
        message = this.getText("VAL_ENTER_VALID_INT");
      }
    } else if (element.type === CDSDataType.HANA_SMALLINT) {
      if (this.checkDefaultInteger(defaultVal)) {
        if (!(Number(defaultVal) >= -32768 && Number(defaultVal) <= 32767)) {
          message = this.getText("VAL_DEFAULT_RANGE_EXCEED_SMALLINT");
        }
      } else {
        message = this.getText("VAL_ENTER_VALID_INT");
      }
    } else if (element.type === CDSDataType.INTEGER) {
      if (this.checkDefaultInteger(defaultVal)) {
        if (!(Number(defaultVal) >= -2147483648 && Number(defaultVal) <= 2147483647)) {
          message = this.getText("VAL_DEFAULT_RANGE_EXCEED_INT");
        }
      } else {
        message = this.getText("VAL_ENTER_VALID_INT");
      }
    } else if (element.type === CDSDataType.INTEGER64) {
      if (this.checkDefaultInteger(defaultVal)) {
        if (!(getBigNumber(defaultVal) >= -9223372036854775808 && getBigNumber(defaultVal) <= 9223372036854775807)) {
          message = this.getText("VAL_DEFAULT_RANGE_EXCEED_BIGINT");
        }
      } else {
        message = this.getText("VAL_ENTER_VALID_INT");
      }
    } else if (element.type === CDSDataType.DECIMAL) {
      if (defaultVal.toString().match(/^[+]?[0-9]\d*(\.\d+)?$/g) !== null) {
        if (defaultVal.toString().length > element.precision) {
          message = this.getText("VAL_ENTER_VALID_DECIMAL", [element.precision, element.scale]);
        }
      } else {
        message = this.getText("VAL_ENTER_VALID_DECIMAL", [element.precision, element.scale]);
      }
    } else if (element.type === CDSDataType.DATE) {
      if (!defaultVal.match(/^\d{1,4}-\d{1,2}-\d{1,2}$/)) {
        message = this.getText("VAL_ENTER_VALID_DATE");
      }
    } else if (element.type === CDSDataType.DATETIME) {
      if (!defaultVal.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)) {
        message = this.getText("VAL_ENTER_VALID_DATETIME");
      }
    } else if (element.type === CDSDataType.TIMESTAMP) {
      if (!defaultVal.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)) {
        message = this.getText("VAL_ENTER_VALID_TIMESTAMP");
      }
    } else if (type !== CDSDataType.STRING && type !== CDSDataType.LARGE_STRING) {
      if (isNaN(defaultVal)) {
        return this.getText("VAL_ENTER_VALID_NUMBER");
      }
    }
    return message;
  }

  public formatDate(dateStr: string): string {
    if (/^\d{4}\d{2}\d{2}$/.test(dateStr)) {
      return dateStr.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3");
    }
    return dateStr;
  }

  public formatDateTime(dateStr: string): string {
    if (/^\d{4}\d{2}\d{2}\d{2}\d{2}\d{2}$/.test(dateStr)) {
      return dateStr.replace(/^(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})$/, "$1-$2-$3 $4:$5:$6");
    }
    return dateStr;
  }

  public checkDefaultInteger(defaultVal: number) {
    if (defaultVal.toString().match(/^([+-]?[0-9]\d*|0)$/g) !== null) {
      return true;
    } else {
      return false;
    }
  }

  public dataTypeIconFormatter(column) {
    const columnDetails = this.partitionDialogModel.getProperty("/partitioningColumns")[column];
    return CDS_TO_ICON[columnDetails.validationType];
  }

  public pickerTooltipFormatter(value, type) {
    switch (type) {
      case "cds.Date":
        value = this.formatDate(value);
        return Format.toUTCDate(value);
      case "cds.DateTime":
      case "cds.Timestamp":
        value = this.formatDateTime(value);
        return Format.toUTCDateTime(value);
      default:
        return value;
    }
  }

  public addPartition() {
    const rangeData = this.partitionDialogModel.getProperty("/ranges");
    const currRange = rangeData[0];
    const newPartition = {
      id: "",
      low: {
        include: true,
        value: "",
        editable: true,
      },
      high: {
        include: false,
        value: currRange ? currRange.low.value : "",
        editable: currRange ? (currRange.low.value ? false : true) : true,
      },
      locked: false,
      rangeVisible: true,
      isOthers: false,
    };
    if (rangeData.length >= 1) {
      // currRange.high.editable = false;
      newPartition.high.editable = false;
    }
    rangeData.splice(0, 0, newPartition);
    this.updateRange(rangeData);
    this.setSaveButtonEnabled(false);
  }

  public addOthersPartition() {
    const newPartition = {
      id: "",
      low: {
        include: true,
        value: "",
        editable: false,
      },
      high: {
        include: false,
        value: "",
        editable: true,
      },
      rangeVisible: false,
      locked: false,
      isOthers: true,
    };
    const rangeData = this.partitionDialogModel.getProperty("/ranges");
    rangeData.push(newPartition);
    this.partitionDialogModel.setProperty("/ranges", rangeData);
    this.partitionDialogModel.updateBindings(true);
  }

  public addRange(event) {
    const source = event.getSource();
    const currRange = source.getBindingContext().getObject();
    const rangeData = this.partitionDialogModel.getProperty("/ranges");
    const newPartition = {
      id: "",
      low: {
        include: true,
        value: currRange.high.value,
        editable: currRange.high.value ? false : true,
      },
      high: {
        include: false,
        value: "",
        editable: true,
      },
      locked: false,
      rangeVisible: true,
      isOthers: false,
    };
    currRange.high.editable = false;
    // disable editing on high if it is not last partition
    if (rangeData[currRange.id]) {
      newPartition.high.editable = false;
    }
    rangeData.splice(currRange.id, 0, newPartition);
    this.updateRange(rangeData);
    this.validateAllBounds();
    // enable save on adding row
    this.setSaveButtonEnabled(true);
  }

  private updateRange(rangeData) {
    this.resetRange(rangeData);
    this.partitionDialogModel.setProperty("/ranges", rangeData);
    this.partitionDialogModel.updateBindings(true);
  }

  public deleteRange(event) {
    const source = event.getSource();
    const currId = source.getBindingContext().getObject().id;
    const rangeData = this.partitionDialogModel.getProperty("/ranges");
    rangeData.splice(currId - 1, 1);
    this.resetRange(rangeData);
    this.partitionDialogModel.setProperty("/ranges", rangeData);
    this.partitionDialogModel.updateBindings(true);
    this.validateAllBounds();
    if (
      (this.appID === ApplicationId.VIEWS && rangeData.length === 1) ||
      (this.appID === ApplicationId.REMOTE_TABLES && rangeData.length === 0)
    ) {
      this.addPartition();
    }
  }

  private onLocked() {
    this.validateAllBounds();
  }

  private resetRange(rangeData) {
    if (rangeData) {
      let num = 1;
      let prevRange;
      const others = rangeData.filter((elem) => elem.isOthers === true);
      rangeData = rangeData.filter((elem) => elem.isOthers !== true);
      rangeData.forEach((elem) => {
        elem.id = num;
        num = num + 1;
        if (elem.id != 1 && prevRange) {
          elem.low.value = prevRange.high.value;
        }
        prevRange = elem;
      });
      const lastIndex = rangeData.length - 1;
      if (rangeData[lastIndex]) {
        rangeData[lastIndex].high.editable = true;
      }
      if (others?.length > 0) {
        rangeData = rangeData.concat(others);
      }
    }
  }

  public async deletePartition(ObjectId: string, appID: string, spaceId: string, taskView) {
    const remoteDetails = taskView.getModel("detailsModel").getData();
    if (remoteDetails.isFVT) {
      if (
        (remoteDetails.dataAccess === dataAccessType.REMOTE &&
          remoteDetails.replicationStatus === ReplicationStatus.DIRECT) ||
        remoteDetails.dataAccess === dataAccessType.REALTIME_REPLICATION
      ) {
        this.delete(ObjectId, appID, spaceId, taskView);
      } else {
        sap.m.MessageBox.error(this.getText("deleteMsgforFVT"));
        taskView.setBusy(false);
      }
    } else {
      this.delete(ObjectId, appID, spaceId, taskView);
    }
  }

  private delete(ObjectId: string, appID: string, spaceId: string, taskView) {
    this.taskView = taskView;
    this.taskView.setBusy(true);
    sap.m.MessageBox.confirm(this.getText("deleteConfirmationTxt"), {
      onClose: async (action) => {
        if (action === sap.m.MessageBox.Action.OK) {
          let objectType = "/remoteTables/";
          if (appID === ApplicationId.VIEWS) {
            objectType = "/persistedViews/";
          }
          const sUrlStart = "partitioning/" + spaceId + objectType + encodeURIComponent(ObjectId);

          try {
            await ServiceCall.request<[]>({
              url: sUrlStart,
              type: HttpMethod.DELETE,
              contentType: ContentType.APPLICATION_JSON,
            });
            this.taskView.setBusy(false);
          } catch (e) {
            this.taskView.setBusy(false);
            return [];
          } finally {
            sap.m.MessageToast.show(this.getText("PARTITION_DELETED"));
            const taskCont = this.taskView.getController() as TaskLogClass;
            taskCont.objectDetailsRefresh();
          }
        } else {
          this.taskView.setBusy(false);
        }
      },
    });
  }

  private checkforUnsaved() {
    const data2 = Object.assign({}, this.partitionDialogModel.getData());
    this.processPostData(data2);
    this.processPostData(this.savedData);
    const isDiff = JSON.stringify(this.savedData) === JSON.stringify(data2);
    return !isDiff;
  }

  public onCancel() {
    if (this.checkforUnsaved()) {
      sap.m.MessageBox.confirm(this.getText("unsavedDataTxt"), {
        onClose: async (action) => {
          if (action === sap.m.MessageBox.Action.OK) {
            if (this.dialog) {
              if (this.appID === ApplicationId.VIEWS) {
                this.clearSearch();
              }
              this.dialog.close();
            }
          }
        },
      });
    } else {
      if (this.dialog) {
        if (this.appID === ApplicationId.VIEWS) {
          this.clearSearch();
        }
        this.dialog.close();
      }
    }
  }

  afterClose() {
    const taskCont = this.taskView.getController() as TaskLogClass;
    if (this.appID === ApplicationId.VIEWS) {
      taskCont.getView().getModel("partitionSettingsModel").setProperty("/isLoadDataVisible", true);
    }
  }

  recordColumnSuitabilityAction() {
    ShellContainer.get().getUsageCollectionService().recordAction({
      action: "suggestedPartitionColumnFilter",
      feature: "viewMonitorTaskLog",
      eventtype: EventType.CLICK,
    });
  }

  onShowSuitability(oEvent: sap.ui.base.Event) {
    this.isSuitabilityOn = oEvent.getParameter("state");
    this.recordColumnSuitabilityAction();
    this.view.getModel("scoredColumnsModel").setProperty("/showColumns", false);
    function compare(a, b) {
      if (a.Rating > b.Rating) {
        return -1;
      }
      if (a.Rating < b.Rating) {
        return 1;
      }
      return 0;
    }
    if (this.isSuitabilityOn) {
      if (!this.isRatingAvailable) {
        const busyDialog = new sap.m.BusyDialog("PartitionDialog", {
          text: this.getText("LOADINGCOLUMNRATINGS"),
        });
        busyDialog.open();

        this.getScoredColumnDetails(this.objectID, this.spaceID)
          .then(() => {
            const model = this.view.getModel("scoredColumnsModel");
            const hasErrors = model.getProperty("/hasErrors");
            if (hasErrors) {
              const errors = model.getProperty("/errors");
              let allErrorTexts = "";
              errors.forEach((err) => {
                let errMsgKey = "noColumnErrorDefault";
                switch (err.detailsCode) {
                  case "DPW_INELIGIBLE":
                    errMsgKey = "noColumnError1";
                    break;
                  case "DPW_VIEW_WITH_PARAM_NOT_ANALYZED":
                    errMsgKey = "noColumnError2";
                    break;
                  case "DPW_NO_SUITABLE_COLUMN":
                    errMsgKey = "noScoredColumnText";
                    break;
                }
                allErrorTexts += this.getText(errMsgKey) + "\n";
              });
              sap.m.MessageBox.error(allErrorTexts);
            } else {
              this.showPayloadInfo();
              const columns = model.getProperty("/columns");
              this.view.getModel("scoredColumnsModel").setProperty("/showColumns", true);
              this.columnsbeforeSorting = Object.assign({}, columns);
              const sortedColumns = columns?.sort(compare);
              model.setProperty("/columns", sortedColumns);
              const colNumber = sortedColumns.length as any;
              this.view
                .getModel("scoredColumnsModel")
                .setProperty("/ColumnsText", this.getText("ColumnsLabel", colNumber));
            }
            this.isRatingAvailable = true;
            busyDialog.close();
            busyDialog.destroy();
          })
          .catch((e) => {
            this.taskView.setBusy(false);
            busyDialog.close();
            busyDialog.destroy();
            MessageHandler.exception({
              exception: e,
              message: this.getText("partitionColumnSuggestionError"),
            });
            this.view.getModel("scoredColumnsModel").setProperty("/showColumns", false);
          });
      } else {
        this.showPayloadInfo();
        const columns = this.view.getModel("scoredColumnsModel").getProperty("/columns");
        const sortedColumns = Object.values(columns)?.sort(compare);
        this.view.getModel("scoredColumnsModel").setProperty("/columns", sortedColumns);
        this.view.getModel("scoredColumnsModel").setProperty("/showColumns", true);
        const colNumber = sortedColumns.length as any;
        this.view.getModel("scoredColumnsModel").setProperty("/ColumnsText", this.getText("ColumnsLabel", colNumber));
      }
    } else {
      this.view.getModel("scoredColumnsModel").setProperty("/columns", this.columnsbeforeSorting);
      this.view.getModel("scoredColumnsModel").setProperty("/showColumns", false);
      const colNumber = Object.keys(this.columnsbeforeSorting).length as any;
      this.view.getModel("scoredColumnsModel").setProperty("/ColumnsText", this.getText("ColumnsLabel", colNumber));
    }

    this.applySearch();
  }

  onShowDetails(oEvent: sap.ui.base.Event, details) {
    const text = [];
    details?.forEach((txt) => {
      const icon = txt.key?.includes("POS") ? "sap-icon://accept" : "sap-icon://decline";
      const color = txt.key?.includes("POS") ? "greenColor" : "redColor";
      const txtIcon = new sap.ui.core.Icon({ src: icon }).addStyleClass("sapUiTinyMarginEnd");
      txtIcon.addStyleClass(color);
      text.push(
        new sap.m.HBox({
          items: [txtIcon, new sap.m.Text({ text: this.getText(txt.key, txt.param) })],
        })
      );
    });

    const container = new sap.m.VBox({
      items: text,
    });
    //container.addStyleClass("bulletlist");
    container.addStyleClass("sapUiSmallMarginEnd");
    container.addStyleClass("sapUiSmallMarginTop");
    container.addStyleClass("sapUiSmallMarginBottom");
    container.addStyleClass("sapUiSmallMarginBegin");
    const popover = new sap.m.Popover("DetailsPopOver", {
      placement: sap.m.PlacementType.Bottom,
      showHeader: false,
      contentWidth: "20%",
    });
    popover.addContent(container);
    popover.openBy(oEvent?.getSource(), true);
    popover.attachAfterClose(() => {
      popover.destroy();
    });
  }

  showAll() {
    const fullpayload = Object.values(this.fullPayLoadList);
    const list = sap.ui.getCore().byId("payloadlist");
    const payloaddialog = sap.ui.getCore().byId("payloadDialog") as sap.m.Dialog;
    sap.ui.getCore().byId("showMoreLink")?.setVisible(false);
    payloaddialog.setContentHeight("45%");
    fullpayload.forEach((element) => {
      list?.addItem(element);
    });
  }

  showPayloadInfo() {
    const model = this.view.getModel("scoredColumnsModel");
    const payload = model.getProperty("/payload");
    let payloadList = [];
    let isShowMoreVisible = false;
    if (payload?.length > 0) {
      payload?.forEach((element) => {
        if (element.messageKey === "PA_FINDINGS" || element.messageKey === "PA_ENTITIES") {
          return;
        }
        payloadList.push(new sap.m.Text({ text: this.getText(element.messageKey, element.params) }));
      });
      if (payloadList?.length > 15) {
        isShowMoreVisible = true;
        this.fullPayLoadList = Object.assign({}, payloadList);

        payloadList = payloadList.splice(0, 15);
      }
      const payloadWarningText = new sap.m.Text({
        text: this.getText("PAYLOAD_WARNING"),
      });

      const listBox = new sap.m.VBox({
        id: "payloadlist",
        items: payloadList,
      });

      listBox.addStyleClass("bulletlist");
      const showMoreLink = new sap.m.Link({
        id: "showMoreLink",
        text: this.getText("showMoreText"),
      });
      showMoreLink.setVisible(isShowMoreVisible);
      showMoreLink.attachPress(() => {
        this.showAll();
      });
      const payloadText = new sap.m.VBox({
        items: [payloadWarningText, listBox, showMoreLink],
      });
      const warningDialog = new sap.m.Dialog({
        title: sap.ui.core.ValueState.Warning,
        type: sap.m.DialogType.Message,
        state: sap.ui.core.ValueState.Warning,
        id: "payloadDialog",
        contentWidth: "30%",
        content: payloadText,
        endButton: new sap.m.Button({
          id: "payLoadClose",
          text: this.getText("CLOSE"),
          press: function () {
            warningDialog.close();
            warningDialog.destroy();
          }.bind(this),
        }),
      });
      warningDialog.open();
    }
  }

  applySearch() {
    var list = this.getView().byId("scoredColumnsTable") as sap.m.Table;
    var binding = list.getBinding("items") as sap.ui.model.json.JSONListBinding;
    let aFilters = new sap.ui.model.Filter({
      filters: [],
      and: true,
    });
    if (this.searchText && this.searchText?.length > 0) {
      aFilters = new sap.ui.model.Filter({
        filters: [
          new sap.ui.model.Filter("Name", sap.ui.model.FilterOperator.Contains, this.searchText),
          new sap.ui.model.Filter("Datatype", sap.ui.model.FilterOperator.Contains, this.searchText),
        ],
        and: false,
      });
      if (this.isSuitabilityOn) {
        aFilters = new sap.ui.model.Filter({
          filters: [
            new sap.ui.model.Filter("Name", sap.ui.model.FilterOperator.Contains, this.searchText),
            new sap.ui.model.Filter("Datatype", sap.ui.model.FilterOperator.Contains, this.searchText),
            new sap.ui.model.Filter("suitable", sap.ui.model.FilterOperator.Contains, this.searchText),
            new sap.ui.model.Filter("detailsText", sap.ui.model.FilterOperator.Contains, this.searchText),
          ],
          and: false,
        });
      }
    }
    binding.filter(aFilters);
  }

  clearSearch() {
    this.searchText = "";
    this.applySearch();
  }

  onSearchScoredColumn(oEvent) {
    const model = this.view.getModel("scoredColumnsModel");
    this.searchText = oEvent.getSource().getValue();

    // update list binding
    this.applySearch();

    this.resetScoredColumnSelection();
  }

  onDefineIntervalClick(oEvent) {
    const model = this.view.getModel("scoredColumnsModel");
    const scoredColumnsList = model.getProperty("/columns");
    const scoredColumnTable = this.view.byId("scoredColumnsTable") as any;
    const selectedPath = scoredColumnTable.getSelectedContextPaths()[0];
    const selectedIndex = selectedPath ? selectedPath.split("/")[2] : -1;
    const selectedColumn = scoredColumnsList[selectedIndex];
    if (selectedPath && selectedColumn) {
      const isSuitable = selectedColumn.Rating ? selectedColumn.Rating >= 0 : true;
      if (isSuitable) {
        this.goToSecondStep();
        this.handleButtonVisibility(1);
      } else {
        this.openQuickConfirmationPopover(oEvent);
      }
    } else {
      const errorMsg = this.getText("noColumnSelectedText");
      sap.m.MessageToast.show(errorMsg);
    }
  }

  onScoredColumnSelect(oEvent: sap.ui.base.Event) {
    const rowContext = oEvent.getParameter("listItem").getBindingContext("scoredColumnsModel");
    const columnObject = rowContext.getObject();
    this.partitionDialogModel.setProperty("/column", columnObject.Name);
    this.onColumnChange();
  }

  openQuickConfirmationPopover(oEvent) {
    const oButton = oEvent.getSource();
    if (!this.quickConfirmPopover) {
      const popOverFragment = require("../../monitorUtil/view/PartitionDefineQuickPopover.fragment.xml");
      this.quickConfirmPopover = sap.ui.xmlfragment(this.getView().getId(), popOverFragment, this) as sap.m.Popover;
      this.getView().addDependent(this.quickConfirmPopover);
      this.quickConfirmPopover.openBy(oButton, true);
    } else {
      this.quickConfirmPopover.openBy(oButton, true);
    }
  }

  continueDefineIntervals() {
    this.goToSecondStep();
    this.handleButtonVisibility(1);
  }

  handleNavigationChange(oEvent) {
    const wizard = this.view.byId("SelectColumnWizard") as sap.m.Wizard;
    const selectedStep = oEvent.getParameter("step");
    const selectedStepIndex = wizard.getSteps().indexOf(selectedStep);
    // this.selectedStepIndex = wizard.getSteps().indexOf(selectedStep);
    this.handleButtonVisibility(selectedStepIndex);
  }

  handleButtonVisibility(selectedStepIndex) {
    const model = this.view.getModel("scoredColumnsModel");
    switch (selectedStepIndex) {
      case 0:
        model.setProperty("/isDefineIntervalEnabled", true);
        model.setProperty("/isSaveEnabled", false);
        break;
      case 1:
        model.setProperty("/isDefineIntervalEnabled", false);
        model.setProperty("/isSaveEnabled", true);
        break;
    }
  }

  private partitionTextFormatter(id: string) {
    return "Partition " + id;
  }

  private keyFormatter(column) {
    const columnDetails = this.partitionDialogModel.getProperty("/partitioningColumns")[column];
    const key = columnDetails.key;
    if (key) {
      return this.getText("key");
    }
    return this.getText(" ");
  }
}

export const partitionDialog = smartExtend(
  BaseController,
  "sap.cdw.components.monitorUtil.controller.PartitionDialog",
  PartitionDialog
);

sap.ui.define("sap/cdw/components/monitorUtil/controller/PartitionDialog.controller", [], function () {
  return partitionDialog;
});
