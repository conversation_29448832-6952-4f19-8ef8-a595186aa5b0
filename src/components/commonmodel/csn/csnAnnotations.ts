/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

enum cds {
  persistence_exists = "@cds.persistence.exists",
  persistence_skip = "@cds.persistence.skip",
  persistence_udf = "@cds.persistence.udf",
}

enum Common {
  Label = "@Common.Label",
  IsLanguageIdentifier = "@Common.IsLanguageIdentifier",
}

export enum EndUserText {
  label = "@EndUserText.label",
}

enum Analytics {
  dataCategory = "@Analytics.dataCategory",
  dimension = "@Analytics.dimension",
  measure = "@Analytics.measure",
  provider = "@Analytics.provider",
  hidden = "@Analytics.hidden",
  dbViewType = "@Analytics.dbViewType",
}

enum AnalyticsDetails {
  measureType = "@AnalyticsDetails.measureType",
  variable_usageType = "@AnalyticsDetails.variable.usageType",
  variable_referenceElement = "@AnalyticsDetails.variable.referenceElement",
  variable_selectionType = "@AnalyticsDetails.variable.selectionType",
  variable_multipleSelections = "@AnalyticsDetails.variable.multipleSelections",
  variable_defaultRanges = "@AnalyticsDetails.variable.defaultRanges",
  exceptionAggregationSteps = "@AnalyticsDetails.exceptionAggregationSteps",
  analyticParamDefaultValue = "@AnalyticsDetails.variable.defaultValue",
  analyticParamDefaultValueHigh = "@AnalyticsDetails.variable.defaultValueHigh",
  queryVariableSequence = "@AnalyticsDetails.query.variableSequence",
  variable_mandatory = "@AnalyticsDetails.variable.mandatory",
  variable_hidden = "@Analytics.variable.hidden",
}

enum Aggregation {
  default = "@Aggregation.default",
  referenceElement = "@Aggregation.referenceElement",
}

enum Hierarchy {
  leveled = "@Hierarchy.leveled",
  parentChild = "@Hierarchy.parentChild",
}

enum ObjectModel {
  text_element = "@ObjectModel.text.element",
  foreignKey_association = "@ObjectModel.foreignKey.association",
  hierarchy_association = "@ObjectModel.hierarchy.association",
  text_association = "@ObjectModel.text.association",
  supportedCapabilities = "@ObjectModel.supportedCapabilities",
  modelingPattern = "@ObjectModel.modelingPattern",
  representativeKey = "@ObjectModel.representativeKey",
}

enum Consumption {
  labelElement = "@Consumption.labelElement",
  valueHelpDefinition = "@Consumption.valueHelpDefinition",
  filter_mandatory = "@Consumption.filter.mandatory",
  filter_defaultValue = "@Consumption.filter.defaultValue",
  filter_defaultValueHigh = "@Consumption.filter.defaultValueHigh",
  filter_hidden = "@Consumption.filter.hidden",
  filter_selectionType = "@Consumption.filter.selectionType",
  filter_multipleSelections = "@Consumption.filter.multipleSelections",
  filter_defaultRanges = "@Consumption.filter.defaultRanges",
  view_filter = "@Consumption.filter",
  db_hints = "@Consumption.dbHints",
  hidden = "@Consumption.hidden",
  derivationLookupEntity = "@Consumption.derivation.lookupEntity",
  derivationResultElement = "@Consumption.derivation.resultElement",
  derivationBinding = "@Consumption.derivation.binding",
}

enum UI {
  hidden = "@UI.hidden",
}

export enum DataWarehouse {
  businessDefinition_description = "@DataWarehouse.businessDefinition.description",
  businessDefinition_purpose = "@DataWarehouse.businessDefinition.purpose",
  businessDefinition_contact = "@DataWarehouse.businessDefinition.contact",
  businessDefinition_responsibleTeam = "@DataWarehouse.businessDefinition.responsibleTeam",
  businessDefinition_tags = "@DataWarehouse.businessDefinition.tags",
  native_dataType = "@DataWarehouse.native.dataType",
  filter_allowed_Expressions = "@DataWarehouse.capabilities.filter.allowedExpressions",
  filter_enabled = "@DataWarehouse.capabilities.filter.enabled",
  remote_connection = "@DataWarehouse.remote.connection",
  remote_entity = "@DataWarehouse.remote.entity",
  remote_query = "@DataWarehouse.remote.query",
  external_schema = "@DataWarehouse.external.schema",
  external_entity = "@DataWarehouse.external.entity",
  space_schema = "@DataWarehouse.space.schema",
  space_name = "@DataWarehouse.space.name",
  space_businessName = "@DataWarehouse.space.businessName",
  pinToMemory = "@DataWarehouse.pinToMemory",
  sap_reserved = "@DataWarehouse.sap.reserved",
  querybuilder_model = "@DataWarehouse.querybuilder.model",
  sqlEditor_query = "@DataWarehouse.sqlEditor.query",
  tableFunction_script = "@DataWarehouse.tableFunction.script",
  dataAccessControl_definition = "@DataWarehouse.dataAccessControl.definition",
  dataAccessControl_usage = "@DataWarehouse.dataAccessControl.usage",
  dataSource_schema = "@DataWarehouse.dataSource.schema",
  dataSource_entity = "@DataWarehouse.dataSource.entity",
  tooling_hidden = "@DataWarehouse.tooling.hidden",
  consumption_external = "@DataWarehouse.consumption.external",
  dataCleansing_geoValue = "@DataWarehouse.spatial.resultOnError",
  compoundKeySequence = "@DataWarehouse.compoundKeySequence",
  delta = "@DataWarehouse.delta",
  deltaEnclosing = "@DataWarehouse.enclosingObject",
  partition = "@DataWarehouse.partition",
  persistence_hdlf_tableFormat = "@DataWarehouse.persistence.hdlf.tableFormat",
  bw_extractionMode = "@DataWarehouse.bw.extractionMode",
  contentOwner = "@DataWarehouse.contentImport.owner",
}

export enum ltfAnnotations {
  params = "params",
  interval = "@Semantics.interval",
  fromChangeTime = "FROM_CHANGE_TIME",
  toChangeTime = "TILL_CHANGE_TIME",
}

enum AnalyticsMeasureValues {
  BASE = "BASE",
  CALCULATION = "CALCULATION",
  RESTRICTION = "RESTRICTION",
}

enum FiscalTimeAnnotations {
  interval = "@Semantics.interval",
  yearPeriod = "@Semantics.fiscal.yearPeriod",
}

export const CsnAnnotations = {
  cds,
  Common,
  EndUserText,
  Analytics,
  AnalyticsDetails,
  Aggregation,
  Hierarchy,
  ObjectModel,
  Consumption,
  UI,
  DataWarehouse,
  AnalyticsMeasureValues,
  ltfAnnotations,
  FiscalTimeAnnotations,
};

export const PCHierarchyUIAnnotations = ["name", "label", "recurse", "nodeType", "directory"];

export const LBHierarchyUIAnnotations = ["name", "label", "levels"];
