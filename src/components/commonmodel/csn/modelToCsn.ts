/**
 * Copyright 2020 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */
import { SupportedFeaturesService } from "../api/SupportedFeaturesService";
import { CommonQualifiedClassNames, CommonShortClassNames } from "../model/const/model.const";
import { AssociationType, CDSDataType } from "../model/types/cds.types";
import { getEntityName, isAnalyticMeasureElement } from "../utility/CommonUtils";
import { CsnAnnotations } from "./csnAnnotations";
import { CsnPropertyMappings } from "./csnProperyMappings";
import * as csnUtils from "./csnUtils";

sap.galilei.namespace("sap.cdw.commonmodel", function (nsLocal) {
  /**
   * @class
   * ModelToCsn implements the conversion from common model to CSN
   */
  nsLocal.ModelToCsn = sap.galilei.core.defineClass({
    fullClassName: "sap.cdw.commonmodel.ModelToCsn",

    statics: {
      CSN_VERSION: "1.0",
      // model to CSN generator instance
      instance: undefined,

      /**
       * Get common model to CSN generator instance.
       */
      getInstance: function () {
        if (!this.instance) {
          this.instance = new nsLocal.ModelToCsn();
        }
        return this.instance;
      },
    },

    properties: {
      // The CSN creator
      // In some Mocha UI tests, the feature flag model is not correctly stubbed. Therefore, we need to add this null check here.
      // Adding proper stubs in all related Mocha UI tests would be too much of work for that little added testing value.
      CSN_CREATOR: csnUtils.DWC_CSN_CREATOR,
      // The CSN kind if needed
      CSN_KIND: undefined,
    },

    fields: {
      // Define the default common model to CSN conversion mappings. It can be overriden.
      CSN_PROP_MAPPING: CsnPropertyMappings,
    },

    methods: {
      /**
       * Gets the current Galilei model.
       * @param {*} oModel
       */
      getModel: function (oModel) {
        if (!oModel) {
          const oResource = sap.galilei.model.getResource(this.UNIQUE_CSN_RESOURCE_ID);
          return oResource && oResource.model;
        }
        return oModel;
      },

      /**
       * Gets an empty CSN document object.
       * @param {object} options The parameters
       *  creator: The creator.
       *  documentKind: The document kind.
       *  label: The document label.
       */
      getEmptyCSNDocument: function (options: any = {}): any {
        const oParams: any = {
          creator: options.creator || this.CSN_CREATOR,
          kind: options.documentKind || this.CSN_KIND,
          deploymentHints: options.deploymentHints,
        };
        if (options.label) {
          oParams.label = options.label;
        }
        return csnUtils.getEmptyCSNDocument(oParams);
      },

      /**
       * Gets the CSN from model.
       * @param {string} sName? The name of model.
       * @param {Model} model? The model.
       * @param {Object} oOptions? The options.
       *     rootObject: The root object o use.
       *     serializeModel: Add serialized Gaiilei model in CSN.
       */
      getValue: function (sName?: string, model?: sap.cdw.commonmodel.Model, oOptions: any = {}): any {
        return this.convertToCsn && this.convertToCsn(sName, model, oOptions);
      },

      /**
       * Converts a model to CSN.
       * @function
       * @name convertToCsn
       * @memberOf sap.cdw.ermodeler.ErModelToCsn
       * @param {string} name? The name of model.
       * @param {Model} model? The model.
       * @param {Object} oOptions? The options.
       *     rootObject: The root object to use.
       *     creator: The creator.
       *     documentKind: Document kind
       *     serializeModel: Add serialized Gaiilei model in CSN.
       *     modelKind: Galilei model kind
       */
      convertToCsn: function (
        name: string,
        model: any,
        options: { rootObject?: any; serializeModel?: any; modelKind?: any; deploymentHints?: boolean } = {}
      ): any {
        let oCsnDocument;

        if (model) {
          // Initialize model name
          if (!model.name && !name) {
            name = "Untitled";
          }
          if (!model.name) {
            model.name = name;
          }
          if (!name) {
            name = model.name;
          }

          // Get empty CSN document
          oCsnDocument = this.getEmptyCSNDocument(options);

          // Add contexts
          this.addContexts(oCsnDocument.definitions, this.getContexts(model));

          // Add simple types
          this.addSimpleTypes(oCsnDocument.definitions, this.getSimpleTypes(model));

          // Add entities
          this.addEntities(oCsnDocument.definitions, this.getEntities(model), options);

          // Add release states
          this.addReleaseStates(oCsnDocument, this.getEntities(model));

          // Add objects
          this.addObjects(oCsnDocument, model, name, options);

          // Add extensions
          this.addExtensions(oCsnDocument, model, name, options);

          if (oCsnDocument.deploymentHints) {
            // Add ord entities in deploymentHints section
            this.addOrdEntities(oCsnDocument.deploymentHints, this.getEntities(model), options);

            if (JSON.stringify(oCsnDocument.deploymentHints) === "{}") {
              delete oCsnDocument.deploymentHints;
            }
          }
        }

        return oCsnDocument;
      },

      getCsnDocumentForEntity: function (entity) {
        const csnDocument = { definitions: {}, releaseContractDefinitions: {} };
        if (entity) {
          // Keep isNew state before setting csn
          const isNew = entity.isNew;
          entity.csn = entity?.csn || {};
          entity.isNew = isNew;
          this.addEntity(csnDocument.definitions, entity, { editCSN: true, useNewName: true });
          // DW101-21403 - Output case: add entity and view mappings
          if (entity.classDefinition.name === CommonShortClassNames.OUTPUT) {
            this.addDefaultPropertyMappingForClass(
              csnDocument.definitions[getEntityName(entity)],
              entity,
              CommonQualifiedClassNames.BASE_ENTITY
            );
            this.addDefaultPropertyMappingForClass(
              csnDocument.definitions[getEntityName(entity)],
              entity,
              CommonQualifiedClassNames.ENTITY
            );
            this.addDefaultPropertyMappingForClass(
              csnDocument.definitions[getEntityName(entity)],
              entity,
              CommonQualifiedClassNames.VIEW
            );
          }
          this.addReleaseState(csnDocument, entity);
        }
        return csnDocument;
      },

      getCsnForEntity: function (entity) {
        const csnDocument = {};
        if (entity) {
          // Keep isNew state before setting csn
          const isNew = entity.isNew;
          entity.csn = entity?.csn || {};
          entity.isNew = isNew;
          this.addEntity(csnDocument, entity, { editCSN: true, useNewName: true });
          // DW101-21403 - Output case: add entity and view mappings
          if (entity.classDefinition.name === CommonShortClassNames.OUTPUT) {
            this.addDefaultPropertyMappingForClass(
              csnDocument[getEntityName(entity)],
              entity,
              CommonQualifiedClassNames.ENTITY
            );
            this.addDefaultPropertyMappingForClass(
              csnDocument[getEntityName(entity)],
              entity,
              CommonQualifiedClassNames.VIEW
            );
          }
        }
        return csnDocument;
      },

      /**
       * Adds Release State in the CSN document.
       * @param csnDocument
       * @param entity
       */
      addReleaseState: function (csnDocument: any, entity: sap.cdw.commonmodel.Entity) {
        if (entity.releaseState !== "NOTRELEASED") {
          csnUtils.releaseContractWriteCsn(entity, csnDocument);
        }
      },

      /**
       * Gets the contexts to generate. It can be overridden.
       * @param model
       */
      getContexts: function (
        model: sap.cdw.commonmodel.Model
      ): sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Context> {
        return model && model.allContexts;
      },

      /**
       * Adds the contexts in the CSN definitions.
       * @param oCsnDefinitions
       * @param contexts
       */
      addContexts: function (
        oCsnDefinitions: any,
        contexts: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Context>
      ) {
        if (oCsnDefinitions && contexts) {
          for (const context of contexts.toArray()) {
            this.addContext(oCsnDefinitions, context);
          }
        }
      },

      /**
       * Adds one context in the CSN definitions.
       * @param oCsnDefinitions
       * @param context
       */
      addContext: function (oCsnDefinitions: any, context: sap.cdw.commonmodel.Context) {
        if (oCsnDefinitions && context) {
          let oCsnContext = {
            kind: "context",
          };
          if (context.unhandledCsn) {
            oCsnContext = Object.assign(oCsnContext, csnUtils.cloneJson(context.unhandledCsn));
          }
          const sQualifiedName = context.qualifiedName;
          if (sQualifiedName) {
            csnUtils.ensureBusinessName(context);

            // Add context properties and annotations
            this.addDefaultPropertyMapping(oCsnContext, context);
            oCsnDefinitions[sQualifiedName] = oCsnContext;
          }
        }
      },

      /**
       * Gets the simple types to generate. It can be overridden.
       * @param model
       */
      getSimpleTypes: function (
        model: sap.cdw.commonmodel.Model
      ): sap.galilei.model.BaseCollection<sap.cdw.commonmodel.SimpleType> {
        return model && model.simpleTypes;
      },

      /**
       * Adds the Simple Types in the CSN definitions.
       * @param oCsnDefinitions
       * @param simpleTypes
       */
      addSimpleTypes: function (
        oCsnDefinitions: any,
        simpleTypes: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.SimpleType>
      ) {
        if (oCsnDefinitions && simpleTypes) {
          for (const simpleType of simpleTypes.toArray()) {
            this.addSimpleType(oCsnDefinitions, simpleType);
          }
        }
      },

      /**
       * Adds one Simple Type in the CSN definitions.
       * @param oCsnDefinitions
       * @param simpleType
       */
      addSimpleType: function (oCsnDefinitions: any, simpleType: sap.cdw.commonmodel.SimpleType) {
        if (oCsnDefinitions && simpleType) {
          let oCsnSimpleType = {
            kind: "type",
          };
          if (simpleType.unhandledCsn) {
            oCsnSimpleType = Object.assign(oCsnSimpleType, csnUtils.cloneJson(simpleType.unhandledCsn));
          }
          const sQualifiedName = simpleType.qualifiedName;
          if (sQualifiedName) {
            csnUtils.ensureBusinessName(simpleType);

            // Add simple type properties and annotations
            this.addDefaultPropertyMapping(oCsnSimpleType, simpleType);

            oCsnDefinitions[sQualifiedName] = oCsnSimpleType;
          }
        }
      },

      /**
       * Adds the release states in the CSN document.
       * @param oCsnDocument
       * @param entities
       */
      addReleaseStates: function (
        oCsnDocument: any,
        entities: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Entity> | sap.cdw.commonmodel.Entity[]
      ) {
        if (SupportedFeaturesService.getInstance().isCompatibilityContractsEnabled() && oCsnDocument && entities) {
          entities = entities instanceof Array ? entities : entities.toArray();
          for (const entity of entities) {
            if (entity?.isView) {
              this.addReleaseState(oCsnDocument, entity);
            }
          }
        }
      },

      /**
       * Gets the entities to generate. It can be overridden.
       * @param model
       */
      getEntities: function (
        model: sap.cdw.commonmodel.Model
      ): sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Entity> {
        return model && model.entities;
      },

      /**
       * Adds the entities in the CSN definitions.
       * @param oCsnDefinitions
       * @param entities
       */
      addEntities: function (
        oCsnDefinitions: any,
        entities: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Entity> | sap.cdw.commonmodel.Entity[],
        oOptions?
      ) {
        if (oCsnDefinitions && entities) {
          entities = entities instanceof Array ? entities : entities.toArray();
          for (const entity of entities) {
            // do not add read only entities to csn definition!
            if (oOptions?.skipReadOnlyObjects === true && entity?.isReadOnlyObject === true) {
              continue;
            }
            this.addEntity(oCsnDefinitions, entity, oOptions);
          }
        }
      },

      /**
       * Adds one entity in the CSN definitions.
       * @param oCsnDocument
       * @param entity
       */
      addEntity: function (oCsnDefinitions: any, entity: sap.cdw.commonmodel.Entity, oOptions?) {
        if (oCsnDefinitions && entity) {
          if (entity.isView) {
            this.addView(oCsnDefinitions, entity, oOptions);
          } else {
            this.addTable(oCsnDefinitions, entity, oOptions);
          }
        }
      },

      /**
       * Adds one table in the CSN definitions.
       * @param oCsnDocument
       * @param entity
       */
      addTable: function (oCsnDefinitions: any, table: sap.cdw.commonmodel.Entity, oOptions?) {
        if (oCsnDefinitions && table && !table.isView) {
          const reposCSN = (table.getRepositoryCSN && table.getRepositoryCSN()) || table.csn || table.repositoryCSN;
          let oCsnEntity: ICsnDefinition;
          if (table.writeAccess === false && reposCSN) {
            oCsnEntity = reposCSN;
          } else {
            oCsnEntity = Object.assign(
              {
                kind: "entity",
              },
              table.unhandledCsn
            );

            csnUtils.ensureBusinessName(table);

            // Add table properties and annotations
            this.addDefaultPropertyMapping(oCsnEntity, table);

            // Add elements and assoviations after annotations
            oCsnEntity.elements = {};
            this.addElements(oCsnEntity.elements, table);
            this.addAssociations(oCsnEntity.elements, table, /* isMixin */ false);
          }

          let sQualifiedName = table.qualifiedName;
          if (table.deltaTable?.type === "UPSERT" && table.deltaTableName) {
            if (table.container?.classDefinition?.qualifiedName === "sap.cdw.querybuilder.Model") {
              // no need to append _Delta, the name will have the value when _Delta entity is used in querybuilder
              sQualifiedName = table.qualifiedName;
            } else {
              sQualifiedName = table.deltaTableName || table.qualifiedName;
            }
          }
          if (table.deltaTable?.type === "UPSERT" && oCsnEntity?.query) {
            delete oCsnEntity.query;
          }

          if (oOptions && oOptions.dataPreview) {
            // add others annotations from repo CSN
            const oCsnEntityClone = csnUtils.cloneJson(oCsnEntity);
            if (reposCSN) {
              for (const ppty in reposCSN) {
                if (ppty.startsWith("@") && oCsnEntityClone[ppty] === undefined) {
                  // annotation missing from computed CSN & available in repo CSN
                  oCsnEntityClone[ppty] = reposCSN[ppty];
                }
              }
            }
            csnUtils.handleCsnForDataPreview(oCsnEntityClone, undefined, oOptions.isMdmPreview);
            oCsnDefinitions[sQualifiedName] = oCsnEntityClone;
          } else {
            oCsnDefinitions[sQualifiedName] = oCsnEntity;
            table.isNew = table.isNew; //NOSONAR <It will set _isNew, and _isNew is more important than csn when compute isNew, so that isNew will not be affected by csn change below>
            // Update Entity's CSN
            table.csn = oCsnEntity;
          }
        }
      },

      /**
       * Adds one view in the CSN definitions.
       * @param oCsnDocument
       * @param entity
       */
      addView: function (oCsnDefinitions: any, view: sap.cdw.commonmodel.Entity, oOptions?) {
        if (oCsnDefinitions && view && view.isView) {
          let csnView;
          const reposCSN = view.getRepositoryCSN && view.getRepositoryCSN();
          if (view.writeAccess === false && reposCSN) {
            csnView = reposCSN;
          } else if (view.csn) {
            const definitionsProperty = "definitions";
            const viewName = view.technicalName || view.name;
            const csnDefinition = view.csn?.[definitionsProperty]?.[viewName] || view.csn;
            csnView = csnUtils.cloneJson(csnDefinition);
            if (view.unhandledCsn) {
              csnView = Object.assign(csnView, csnUtils.cloneJson(view.unhandledCsn));
            }
            csnView = this.createViewCSN(csnView, view, oOptions);
          } else if (
            view.repositoryCSN &&
            ((oOptions?.dataPreview && oOptions?.isMdmPreview) ||
              (oOptions?.dataPreview && oOptions?.isMdmPreview && view.isCrossSpace))
          ) {
            csnView = csnUtils.cloneJson(view.repositoryCSN);
            csnView = this.createViewCSN(csnView, view, oOptions);
            csnView[CsnAnnotations.cds.persistence_exists] = true;
            delete csnView.query;
          } else {
            // Common model does not have view internal definition.
            return;
          }
          const qualifiedName = view.qualifiedName;
          oCsnDefinitions[qualifiedName] = csnView;
          if (oOptions && oOptions.dataPreview) {
            const csnViewClone = csnUtils.cloneJson(csnView);
            csnUtils.handleCsnForDataPreview(csnViewClone, undefined, oOptions.isMdmPreview);
            oCsnDefinitions[qualifiedName] = csnViewClone;
          } else if (oOptions?.editCSN !== true) {
            // Update View's CSN
            view.csn = csnView;
          }
        }
      },

      createViewCSN(csnView: any, view: any, oOptions?) {
        const elementNames = csnView && csnView.elements && Object.keys(csnView.elements);
        const formulaElements = [];
        if (elementNames?.length > 0) {
          elementNames.forEach((elementName) => {
            const element = csnView.elements[elementName];
            if (isAnalyticMeasureElement(element)) {
              const obj = {};
              obj[elementName] = element;
              const elt = view.elements.toArray().find((e) => e.name === elementName);
              if (elt?.unhandledCsn) {
                Object.assign(obj[elementName], csnUtils.cloneJson(elt.unhandledCsn));
              }
              formulaElements.push(obj);
            }
          });
        }
        csnUtils.ensureBusinessName(view);

        // Remove existing hierarchies
        if (csnView[CsnAnnotations.Hierarchy.leveled]) {
          delete csnView[CsnAnnotations.Hierarchy.leveled];
        }
        if (csnView[CsnAnnotations.Hierarchy.parentChild]) {
          delete csnView[CsnAnnotations.Hierarchy.parentChild];
        }

        // TODO: Remove other existing annotations that could be generated

        // Add view properties and annotations
        // First delete parameters to ensure skipping analytic ones
        if ((view as any)._skipAnalyticMeasures === true) {
          delete csnView.params;
        }
        this.addDefaultPropertyMapping(csnView, view);

        // view.csn.elements may have existing association elements, should remove them before
        csnView.elements = {};
        this.addElements(csnView.elements, view, oOptions?.useNewName);
        this.addAssociations(csnView.elements, view, /* isMixin */ false);

        // Add formula element, which is not there in the view (if not asked to skip)
        if ((view as any)._skipAnalyticMeasures !== true) {
          Object.assign(csnView.elements, ...formulaElements);
        }

        // Remove existing associations based on updated elements:
        // We need to check existing assocaitions in the csn
        // This cleaning is made AFTER all exisiting associations, including unresolved, are processed
        // check existing mixin befaure cleaning
        const oldMixin = csnView?.query?.SELECT?.mixin;
        this.cleanCsnQuery(csnView);
        if (csnView.query) {
          if (csnView.query.SELECT) {
            this.addAssociations(csnView.query.SELECT, view, /* isMixin */ true);
            this.addAssociationColumns(csnView.query.SELECT.columns, view);
            // check cleared mixin: restore UNION SET if relevant
            const newMixin = csnView?.query?.SELECT?.mixin;
            if (oldMixin !== undefined && newMixin === undefined && !(formulaElements && formulaElements.length > 0)) {
              // remove encapsulating SELECT ?
              this.restoreUnionQuery(csnView);
            }
          } else if (csnView.query.SET && csnView.query.SET.op === "union") {
            // Encapsulate the union in a SELECT statement:
            this.updateUnionQuery(csnView, view, undefined, true /* bSkipAnalyticMeasures*/);
          }
        }
        return csnView;
      },

      updateSQLModelCSN(csnEntityFromModel, csnEntityFromSql) {
        // csnEntityFromSql: from CDS compiler
        // csnEntityFromModel: from SQL model => has to be updated !
        if (csnEntityFromModel && csnEntityFromSql) {
          // Copy association columns generated from model in csnEntityFromSql
          const columnsQueryFromModel =
            csnEntityFromModel.query && csnEntityFromModel.query.SELECT && csnEntityFromModel.query.SELECT.columns;
          const associationColumns = [];
          if (columnsQueryFromModel) {
            if (csnEntityFromSql.query && csnEntityFromSql.query.SELECT && csnEntityFromSql.query.SELECT.columns) {
              columnsQueryFromModel.forEach((column) => {
                let columnName = column.ref && column.ref[column.ref.length - 1];
                let isExist = false;
                if (column.as) {
                  columnName = column.as;
                } else if (column && column.ref && column.ref.length === 1) {
                  columnName = column.ref[0];
                }
                csnEntityFromSql.query.SELECT.columns.forEach((sqlColumn) => {
                  const SqlColumnName = sqlColumn.as
                    ? sqlColumn.as
                    : sqlColumn.ref && sqlColumn.ref[sqlColumn.ref.length - 1];
                  if (SqlColumnName && columnName) {
                    if (SqlColumnName.toUpperCase() === columnName.toUpperCase()) {
                      isExist = true;
                      return;
                    }
                  }
                });
                if (!isExist && columnName) {
                  associationColumns.push(column);
                }
              });
              associationColumns.forEach((associationColumn) => {
                csnEntityFromSql.query.SELECT.columns.push(associationColumn);
              });
            }
          }
          // Copy mixin generated from model in csnEntityFromSql and handle the case of union query
          const mixinQueryFromModel =
            csnEntityFromModel.query && csnEntityFromModel.query.SELECT && csnEntityFromModel.query.SELECT.mixin;
          if (mixinQueryFromModel && csnEntityFromSql.query) {
            // Union case
            if (
              csnEntityFromSql.query.SET &&
              csnEntityFromSql.query.SET.args[0] &&
              csnEntityFromSql.query.SET.args[0].SELECT
            ) {
              // Move the SET to an encapsulating SELECT
              csnEntityFromSql.query.SELECT = csnEntityFromSql.query.SELECT || {};
              csnEntityFromSql.query.SELECT.from = { SET: { ...csnEntityFromSql.query.SET }, as: "unionSet" };
              delete csnEntityFromSql.query.SET;

              // Add columns from all elements
              csnEntityFromSql.query.SELECT.columns = [];
              Object.keys(csnEntityFromModel?.elements).forEach((sKey) => {
                csnEntityFromSql.query.SELECT.columns.push({ ref: [sKey] });
              });
            }
            // Copy mixin
            if (csnEntityFromSql.query.SELECT) {
              csnEntityFromSql.query.SELECT.mixin = mixinQueryFromModel;
            }
          }
          // Replace csnEntityFromModel.query by csnEntityFromSql.query (produced by CDS compiler)
          csnEntityFromModel.query = csnEntityFromSql.query;

          // FIXME this is not good: writing & deleting things - SQL CSN generation should not be tightly coupled with GVE CSN generation!
          // Design need to be reworked
          csnEntityFromModel["@DataWarehouse.sqlEditor.query"] = csnEntityFromSql["@DataWarehouse.sqlEditor.query"];
          delete csnEntityFromModel["@DataWarehouse.querybuilder.model"]; // No model serialization for SQL
        }
      },

      /**
       * Add an encapsulating SELECT for a union query
       * @param oCsnView
       * @param view
       */
      updateUnionQuery: function (oCsnView, view, bDataPreview?, bSkipAnalyticMeasures?) {
        const bAssociationExists = view && view.sourceAssociations?.length;
        const bAnalyticMeasures = this.addAnalyticMeasures && view && view.analyticMeasureElements?.length;
        if (
          oCsnView &&
          oCsnView.query &&
          oCsnView.query.SET &&
          oCsnView.elements &&
          (bAssociationExists || bAnalyticMeasures)
        ) {
          const oCsnQuery = oCsnView.query;
          const oCsnElements = oCsnView.elements;
          // 1- Insert set into SELECT from, with a (mandatory) alias
          oCsnQuery.SELECT = { from: { SET: { ...oCsnQuery.SET }, as: "unionSet" } };
          // Clear SET
          delete oCsnQuery.SET;
          // Add columns from elements that are not of type cds.Association
          oCsnQuery.SELECT.columns = [];
          Object.keys(oCsnElements).forEach((sKey) => {
            const oCsnElement = oCsnElements[sKey];
            if (oCsnElement && !(oCsnElement.type === "cds.Association" || isAnalyticMeasureElement(oCsnElement))) {
              oCsnQuery.SELECT.columns.push({ ref: [sKey] });
            }
          });

          if (bAssociationExists) {
            // Add associations and Association Columns
            this.addAssociations(oCsnQuery.SELECT, view, /* isMixin */ true, bDataPreview);
            if (!bDataPreview) {
              this.addAssociationColumns(oCsnQuery.SELECT.columns, view);
            }
          }
          if (bAnalyticMeasures && this.addAnalyticMeasures && !bSkipAnalyticMeasures) {
            this.addAnalyticMeasures(oCsnQuery.SELECT.columns, view, bDataPreview);
          }
        }
      },

      /**
       * Remove the encapsulating SELECT for a union query if useless!
       * @param oCsnView
       */
      restoreUnionQuery: function (oCsnView) {
        const oCsnQuery = oCsnView?.query;
        // Check conditions:
        // 1- SELECT query
        // 2- No mixin
        // 3- the "from" is a "union" "SET"
        if (oCsnQuery?.SELECT?.from?.SET?.op === "union") {
          if (oCsnQuery.SELECT.mixin === undefined) {
            // Add union SET
            oCsnQuery.SET = { ...oCsnQuery.SELECT.from.SET };
            // Clear SELECT
            delete oCsnQuery.SELECT;
          }
        }
      },

      /**
       * Cleans a view query in CSN.
       * @param oCsnView
       */
      cleanCsnQuery: function (oCsnView: any) {
        if (oCsnView && oCsnView.query) {
          if (oCsnView.query.SELECT) {
            this.cleanCsnSelect(oCsnView.query.SELECT, oCsnView.elements);
          } else if (oCsnView.query.SET && oCsnView.query.SET.args) {
            // UNION/INTERSECT case
            oCsnView.query.SET.args.forEach((arg) => {
              if (arg && arg.SELECT) {
                this.cleanCsnSelect(arg.SELECT, oCsnView.elements);
              }
            });
          }
        }
      },

      /**
       * Cleans a view query SELECT in CSN.
       * @param oCsnSelect
       * @param oCsnElements
       */
      cleanCsnSelect: function (oCsnSelect: any, oCsnElements: any) {
        // First step: clean columns
        if (oCsnSelect && oCsnSelect.columns && oCsnElements) {
          for (let iColumn = oCsnSelect.columns.length - 1; iColumn >= 0; iColumn--) {
            const oColumn = oCsnSelect.columns[iColumn];
            const sColumn =
              oColumn && (oColumn.as || (oColumn.ref && oColumn.ref.length && oColumn.ref[oColumn.ref.length - 1]));
            if (sColumn && oCsnElements[sColumn] === undefined) {
              oCsnSelect.columns.splice(iColumn, 1);
            }
          }
        }
        // Second step: clean mixin
        if (oCsnSelect && oCsnSelect.mixin && oCsnElements) {
          Object.keys(oCsnSelect.mixin).forEach((sMixin) => {
            if (sMixin && oCsnElements[sMixin] === undefined) {
              delete oCsnSelect.mixin[sMixin];
            }
          });
        }
      },

      /**
       * Adds elements in the CSN elements of the entity.
       * @param oCsnElements
       * @param elements
       * @param bUseNewName
       */
      addElements: function (
        oCsnElements: any,
        entity: sap.cdw.commonmodel.Entity,
        bUseNewName: boolean = false,
        bUseQualifiedNames: boolean = false,
        bUseIndex: boolean = false,
        bDataPreview: boolean = false
      ) {
        if (oCsnElements && entity && entity.orderedElements) {
          for (const element of entity.orderedElements) {
            if (element.isRemoved || element.isExcluded) {
              continue; // data preview case
            }

            if (bDataPreview && element.isAnalyticMeasure) {
              continue; // data preview case ignore analytic measures
            }

            let elementName = this.getElementName(element, bUseNewName);
            if (bUseQualifiedNames) {
              elementName = sap.cdw.commonmodel.ObjectImpl.getQualifiedNameAsArray(
                element,
                bUseNewName,
                bUseIndex
              ).join("-");
            }

            if (element.dataType === CDSDataType.ASSOCIATION) {
              // The element is an association
              this.addElementAssociation(oCsnElements, element, elementName);
            } else {
              const columnVh = SupportedFeaturesService.getInstance().isColumnValueHelpGVEnabled();
              /* to add calculated element to query only if the expression could be parsed successfully without any error
              - This is required to fetch the column value help without adding the element with invalid/incomplete expression
              */
              if (columnVh && entity.classDefinition.name === "CalculatedElements" && element.isCalculated) {
                if (element.parsedExpression && !element.parsedExpression?.error) {
                  this.addElement(oCsnElements, element, elementName);
                }
              }
              // The element is an element
              else {
                this.addElement(oCsnElements, element, elementName);
              }
            }
          }
        }
      },

      /**
       * Adds an element in the CSN elements of the entity.
       * @param oCsnElements
       * @param element
       * @param elementName
       */
      addElement: function (oCsnElements: any, element: sap.cdw.commonmodel.Element, elementName: string) {
        if (oCsnElements && element) {
          let oCsnElement: any = {};
          if (element.unhandledCsn) {
            oCsnElement = { ...element.unhandledCsn };
          }
          // Add element properties and annotations
          this.addDefaultPropertyMapping(oCsnElement, element);

          oCsnElements[elementName] = oCsnElement;
        }
      },

      /**
       * Gets element name for CSN.
       * @param {*} element
       */
      getElementName: function (element: sap.cdw.commonmodel.Element, bUseNewName: boolean) {
        if (element) {
          const elementName = bUseNewName !== false ? element.newName || element.name : element.name;
          // TODO: Escape characters
          return elementName;
        }
        return "";
      },

      /**
       * Gets elementRef for CSN.
       * @param {*} oElement
       */
      getElementRef: function (element: sap.cdw.commonmodel.Element, bUseAlias) {
        return [element && element.name];
      },

      /**
       * Adds an element for association in the CSN elements of the entity.
       * @param oCsnElements
       * @param element
       * @param elementName
       */
      addElementAssociation: function (oCsnElements: any, element: sap.cdw.commonmodel.Element, elementName: string) {
        if (oCsnElements && element) {
          let oCsnElement: any = {};

          // Managed Associations should be generated with respect to element order
          const result = this.getManagedAssociation(element);
          if (result) {
            // Back Link Case
            if (result.isBackLink) {
              this.updateBackLinkManaged(oCsnElement, result.association);
            } else {
              // Standard Managed case
              this.updateManagedAssociation(oCsnElement, result.association);
            }
            this.addDefaultPropertyMapping(oCsnElement, element);
          } else {
            // Unresolved element?
            if (element.savedCsn) {
              oCsnElement = element.savedCsn;
              // Add element properties and annotations
              this.addDefaultPropertyMapping(oCsnElement, element);
            } else {
              return;
            }
          }

          oCsnElements[elementName] = oCsnElement;
        }
      },

      /**
       * Add Unmanaged Associations.
       * @param {boolean} isMixin: If false, generate an element for an association. oCsnElements is elements.
       *   If true: generate a mixin for an associaiton. oCsnElements is query.
       */
      addAssociations: function (
        oCsnElements: any,
        entity: sap.cdw.commonmodel.Entity,
        isMixin: boolean,
        bDataPreview?: boolean
      ) {
        const oModel: any = entity && entity.rootContainer;
        const associations = this.getUnmanagedAssociations(
          {
            source: entity,
          },
          oModel
        );

        // In case of View, associations MUST be added in the mixin section
        // count is the number of 'resolved' associations, there might be unresolved ones!
        let oSourceCsn;
        if (isMixin) {
          oCsnElements.mixin = oCsnElements.mixin || {};
          oSourceCsn = oCsnElements.mixin;
        } else {
          oSourceCsn = oCsnElements;
        }

        // Add associations in CSN
        const count = associations ? associations.length : 0;
        for (let i = 0; i < count; i++) {
          if (!isMixin || (isMixin && !associations[i].isExistsInSource)) {
            this.addAssociation(oSourceCsn, associations[i], isMixin);
          }
        }

        // Add unresolved unmanaged associations
        if (oModel && oModel.unresolvedAssociations) {
          const nLen = oModel.unresolvedAssociations.length || 0;
          for (let i = 0; i < nLen; i++) {
            const oUnresolvedAssociation = oModel.unresolvedAssociations[i];
            if (oUnresolvedAssociation.source === entity) {
              if (isMixin && oCsnElements.mixin) {
                const unresolvedObj = $.extend(true, {}, oUnresolvedAssociation);
                this.addProjectionForUnresolved(oSourceCsn, unresolvedObj);
              } else {
                this.addUnresolvedAssociation(oSourceCsn, oUnresolvedAssociation);
              }
            }
          }
        }

        // Clean empty mixin?
        if (isMixin) {
          if (oSourceCsn && !Object.keys(oSourceCsn).length) {
            delete oCsnElements.mixin;
          }
        }

        // Delete mixin when do data preview, it does not affect data and avoid the issue - cannot find columns which are referred by association (mixin in query csn)
        if (bDataPreview) {
          delete oCsnElements.mixin;
        }
      },

      /**
       * Adds join cardinality in CSN.
       * @param aCsnOnClause
       * @param joinobject
       */
      addQueryCardinalityClause: function (oCsn, oJoin) {
        oCsn.cardinality = csnUtils.getCardinalityCsn(oJoin);
      },

      /**
       * Add an Associations in CSN.
       * @param oCsnElements
       * @param association
       * @param isMixin
       */
      addAssociation: function (oCsnElements: any, association: sap.cdw.commonmodel.Association, isMixin: boolean) {
        let oCsnAssociation = {} as any;
        if (oCsnElements && association && association.target) {
          // FPA101-3411: Safety check - Association without target should not be generated!
          oCsnAssociation.type = CDSDataType.ASSOCIATION;
          if (association.unhandledCsn && !isMixin) {
            oCsnAssociation = Object.assign(oCsnAssociation, csnUtils.cloneJson(association.unhandledCsn));
          }

          // Add association properties and annotations
          this.addDefaultPropertyMapping(oCsnAssociation, association);

          if ((association.type && association.type === AssociationType.UNMANAGED) || !association.type) {
            // Association view => association add default max cardinality
            if (!association.type) {
              oCsnAssociation.cardinality = {
                max: 1,
              };
            }
            oCsnAssociation.on = [];
            this.addAssociationOnClause(oCsnAssociation.on, association, isMixin);
          } else if (association.type && association.type === AssociationType.MANAGED) {
            this.addAssociationManagedClause(oCsnAssociation, association, isMixin);
          }
          let qualifiedName = association.target?.qualifiedName
            ? association.target.qualifiedName
            : association.target.name;
          if (
            association.target?.crossSpaceName &&
            qualifiedName?.indexOf(association.target.crossSpaceName + ".") !== 0
          ) {
            qualifiedName = `${association.target.crossSpaceName}.${association.target.qualifiedName}`;
          }
          oCsnAssociation.target = qualifiedName;
          if (!isMixin && (association.label || association.displayName)) {
            oCsnAssociation["@EndUserText.label"] = association.label || association.displayName;
          }
          oCsnElements[this.getAssociationName(association)] = oCsnAssociation;
        }
      },

      /**
       * Adds association columns in CSN.
       * @param oCsnQueryColumns
       * @param entity
       */
      addAssociationColumns: function (oCsnQueryColumns: any, entity: sap.cdw.commonmodel.Entity) {
        const oModel: any = entity && entity.rootContainer;
        const associations = this.getUnmanagedAssociations(
          {
            source: entity,
          },
          oModel
        );

        if (oCsnQueryColumns && associations) {
          const count = associations && associations.length;
          for (let i = 0; i < count; i++) {
            const association = associations[i];
            const isExists = oCsnQueryColumns.findIndex((column) => column.ref && column.ref[0] === association.name);
            if (isExists === -1) {
              oCsnQueryColumns.push({
                ref: [this.getAssociationName(association)],
              });
            }
          }
        }
      },

      /**
       * Gets the association name.
       * @param association
       */
      getAssociationName: function (association: sap.cdw.commonmodel.Association): string {
        if (association) {
          return association.name || (association.target && "_" + association.target.shortName);
        }
        return "";
      },

      /**
       * Gets unmanaged associations.
       * @param args { source: <source entity>, target: <target entity> }
       * @param model
       */
      getUnmanagedAssociations: function (
        args: { source: any; target: any },
        model: sap.cdw.commonmodel.Model
      ): sap.cdw.commonmodel.Association[] {
        const source = args && args.source;
        const target = args && args.target;
        const associations = [];

        if (model && model.associations) {
          for (const association of model.associations.toArray()) {
            if (
              (!source || (source && source === association.source)) &&
              (!target || (target && target === association.target)) &&
              association.type !== AssociationType.MANAGED
            ) {
              associations.push(association);
            }
          }
        }
        return associations;
      },

      /**
       * Gets managed association for of an element.
       * @param element
       */
      getManagedAssociation: function (element: sap.cdw.commonmodel.Element) {
        const entity = element && (element.container as sap.cdw.commonmodel.Entity);

        // Search direct Managed Associations
        if (entity && entity.sourceAssociations) {
          const count = entity.sourceAssociations.length;
          for (let i = 0; i < count; i++) {
            const oAssociation = entity.sourceAssociations.get(i);
            if (oAssociation && oAssociation.sourceElement === element) {
              return {
                isBackLink: false,
                association: oAssociation,
              };
            }
          }
        }

        // Search back-link Managed Associations
        if (entity && entity.targetAssociations) {
          const count = entity.targetAssociations.length;
          for (let i = 0; i < count; i++) {
            const oAssociation = entity.targetAssociations.get(i);
            if (oAssociation && oAssociation.targetElement === element) {
              return {
                isBackLink: true,
                association: oAssociation,
              };
            }
          }
        }

        return undefined;
      },

      /**
       * Checks if an element is linked to a managed association.
       * @param element
       */
      isManagedAssociation: function (element: sap.cdw.commonmodel.Element) {
        let result = false;
        if (element && element.container) {
          const entity = element.container as sap.cdw.commonmodel.Entity;
          if (entity.targetAssociations) {
            const count = entity.targetAssociations.length;
            for (let i = 0; i < count; i++) {
              if (entity.targetAssociations.get(i).targetElement === element) {
                result = !!entity.targetAssociations.get(i);
                break;
              }
            }
          }
        }
        return result;
      },

      /**
       * Adds association on clause in CSN.
       * @param aCsnOnClause
       * @param association
       * @param isMixin
       */
      addAssociationOnClause: function (
        aCsnOnClause: any[],
        association: sap.cdw.commonmodel.Association,
        isMixin: boolean
      ) {
        const oElementMappings = association && association.mappings;
        const length = oElementMappings && oElementMappings.length;

        for (let index = 0; index < length; index++) {
          const mapping = oElementMappings.get(index);
          if (mapping.source && mapping.target) {
            const sourceElementRef = {
              ref: [],
            };
            const oElement = mapping.source as sap.cdw.commonmodel.BaseElement;
            const elementSourceRef = [oElement.newName || oElement.name]; // Only element name for association source
            if (isMixin) {
              elementSourceRef.unshift("$projection");
            }
            sourceElementRef.ref = elementSourceRef;
            aCsnOnClause.push(sourceElementRef);
            aCsnOnClause.push("=");
            const elementTargetRef = this.getElementRef(mapping.target, /* useAlias */ false);
            if (elementTargetRef.length > 1) {
              elementTargetRef.splice(0, 1);
            }
            const targetElementRef = {
              ref: [this.getAssociationName(association)].concat(elementTargetRef),
            };
            aCsnOnClause.push(targetElementRef);
            if (index + 1 < length) {
              aCsnOnClause.push("and");
            }
          }
        }
      },

      /**
       * Adds a managed association in CSN.
       * @param oCsnAssociation
       * @param association
       * @param isMixin
       */
      addAssociationManagedClause: function (
        oCsnAssociation: any,
        association: sap.cdw.commonmodel.Association,
        isMixin: boolean
      ) {
        if (oCsnAssociation && association && association.sourceElement && association.targetElement) {
          oCsnAssociation.on = [];
          const sourceElementRef = {
            ref: [],
          };
          sourceElementRef.ref.push(association.name);
          sourceElementRef.ref.push(association.targetElement.name);
          oCsnAssociation.on.push(sourceElementRef);
          oCsnAssociation.on.push("=");
          const targetElementRef = {
            ref: ["$self"],
          };
          oCsnAssociation.on.push(targetElementRef);
        }
      },

      /**
       * Adds projection for an unresolved association.
       * @param oCsnSource
       * @param unresolvedAssociation
       */
      addProjectionForUnresolved: function (oCsnSource: any, unresolvedAssociation: sap.cdw.commonmodel.Association) {
        // Update the association CSN only if its'not already present in the old mixin
        if (
          oCsnSource &&
          unresolvedAssociation &&
          unresolvedAssociation.name &&
          !oCsnSource[unresolvedAssociation.name]
        ) {
          const association = unresolvedAssociation;
          if (association.csn && association.csn.on && association.csn.on.length > 0) {
            // splitting the array and creating a new array to add projection in the mixin clause
            let count = 0;
            const onClause = association.csn.on;
            onClause.forEach(function (obj, index) {
              if (obj === "and") {
                count++;
              } else {
                if (index === 0 || count === 1) {
                  if (obj.ref && obj.ref.indexOf("$projection") === -1) {
                    obj.ref.unshift("$projection");
                    count = 0;
                  }
                }
              }
            });

            oCsnSource[this.getAssociationName(association)] = csnUtils.cloneJson(association.csn);
            oCsnSource[this.getAssociationName(association)].on = onClause;
          }
        }
      },

      /**
       * Adds saved unresolved association in CSN.
       * @param oCsnElements
       * @param unresolvedAssociation
       */
      addUnresolvedAssociation: function (oCsnElements: any, unresolvedAssociation: sap.cdw.commonmodel.Association) {
        // Restitute original csn
        if (oCsnElements && unresolvedAssociation?.csn) {
          const associationName = this.getAssociationName(unresolvedAssociation);
          oCsnElements[associationName] = csnUtils.cloneJson(unresolvedAssociation.csn);
          if (unresolvedAssociation.unhandledCsn) {
            Object.assign(oCsnElements[associationName], csnUtils.cloneJson(unresolvedAssociation.unhandledCsn));
          }
        }
      },

      /**
       * Adds back link for a managed association.
       * @param oCsnElement
       * @param association
       */
      updateBackLinkManaged(oCsnElement: any, association: sap.cdw.commonmodel.Association) {
        if (oCsnElement && association) {
          oCsnElement.type = CDSDataType.ASSOCIATION;
          oCsnElement.cardinality = {
            max: 1,
          };
          oCsnElement.target = association.source.qualifiedName
            ? association.source.qualifiedName
            : association.source.name;
        }
      },

      /**
       * Updates managed association properties in CSN.
       * @param oCsnElement
       * @param association
       */
      updateManagedAssociation: function (oCsnElement: any, association: sap.cdw.commonmodel.Association) {
        if (oCsnElement && association && association.target) {
          oCsnElement.type = CDSDataType.ASSOCIATION;
          this.addDefaultPropertyMapping(oCsnElement, association);
          // Manages to many?
          this.addAssociationManagedClause(oCsnElement, association);
          oCsnElement.target = association.target.qualifiedName
            ? association.target.qualifiedName
            : association.target.name;
        }
      },

      /**
       * Adds extensions in the CSN. It can be overridden.
       * @param oCsnDocument
       * @param model
       */
      addExtensions: function (oCsnDocument: any, model: sap.cdw.commonmodel.Model, name: string, options: any = {}) {
        if (oCsnDocument && model) {
          for (const entity of model.entities.toArray()) {
            const table = entity as any;
            if (entity.isTable && table.pendingPartitionExtension !== undefined) {
              if (table.pendingPartitionExtension === null) {
                if (
                  oCsnDocument["#repairedCsn"]?.extensions &&
                  Array.isArray(oCsnDocument["#repairedCsn"].extensions)
                ) {
                  const extensionIndex = oCsnDocument["#repairedCsn"].extensions.findIndex(
                    (ext: any) => ext.annotate === entity.name && ext["@DataWarehouse.partition"]
                  );
                  if (extensionIndex !== -1) {
                    oCsnDocument["#repairedCsn"].extensions.splice(extensionIndex, 1);
                  }
                }
              } else {
                if (!oCsnDocument["#repairedCsn"]) oCsnDocument["#repairedCsn"] = {};
                if (!oCsnDocument["#repairedCsn"].extensions) oCsnDocument["#repairedCsn"].extensions = [];
                oCsnDocument["#repairedCsn"].extensions.push(table.pendingPartitionExtension);
              }
              delete table.pendingPartitionExtension;
            }
          }
        }
      },

      /**
       * Adds the objects dependencies section in the CSN. It can be overridden.
       * @param oCsnDocument
       * @param model
       */
      addObjects: function (oCsnDocument: any, model: sap.cdw.commonmodel.Model, name: string, options: any = {}) {
        // No default implementation
      },

      /**
       * Adds the objects' ORD info into deploymentHints section in the CSN. It can be overridden.
       * @param oCsnDeploymentHints
       * @param entities
       * @param oOptions
       */
      addOrdEntities: function (
        oCsnDeploymentHints: any,
        entities: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Entity> | sap.cdw.commonmodel.Entity[],
        oOptions?
      ) {
        // No default implementation
      },

      /**
       * Adds the default Galilei model object to CSN properties mapping for the object.
       * @param {*} oCsnObject The current CSN object.
       * @param {*} object The model object.
       */
      addDefaultPropertyMapping: function (oCsnObject: any, object: sap.cdw.commonmodel.BaseObject) {
        if (!oCsnObject || !object) {
          return;
        }

        // Get super classes
        const aClasses = csnUtils.getClassHierarchy(object);

        // Add properties mapping for super classes and the current class.
        for (const sOneClassName of aClasses) {
          this.addDefaultPropertyMappingForClass(oCsnObject, object, sOneClassName);
        }
      },

      /**
       * Adds the default Galilei model object to CSN properties mapping for the object.
       * @param {*} oCsnObject The current CSN object.
       * @param {*} object The model object.
       * @param {*} sClassName The mapping for a class qualified name.
       */
      addDefaultPropertyMappingForClass: function (
        oCsnObject: any,
        object: sap.cdw.commonmodel.BaseObject,
        sClassName: string
      ) {
        if (!oCsnObject || !object || !sClassName) {
          return;
        }

        const oMapping = csnUtils.getObjectMapping(this.CSN_PROP_MAPPING, sClassName);
        if (oMapping) {
          const aMappedPropertyNames = Object.getOwnPropertyNames(oMapping);

          for (const sPropertyName of aMappedPropertyNames) {
            const csnMappingValue = oMapping[sPropertyName];
            if (csnMappingValue) {
              if (csnMappingValue.writeCsn instanceof Function) {
                // Use the writeCsn() function to generate CSN if it is defined.
                csnMappingValue.writeCsn(object, oCsnObject);
              } else if (sPropertyName.indexOf(".") > 0) {
                const aParts = sPropertyName.split(".");
                let vPropertyValue = object;
                aParts.forEach(function (sPart) {
                  if (typeof vPropertyValue === "object") {
                    vPropertyValue = vPropertyValue[sPart];
                  } else {
                    vPropertyValue = undefined;
                  }
                });
                if (vPropertyValue !== undefined) {
                  this.setPropertyMappingValue(oCsnObject, csnMappingValue, vPropertyValue);
                }
              } else {
                if (object[sPropertyName] !== undefined) {
                  if (sPropertyName !== "filterEnabled" && object[sPropertyName] !== false) {
                    this.setPropertyMappingValue(oCsnObject, csnMappingValue, object[sPropertyName]);
                  } else if (sPropertyName === "filterEnabled") {
                    this.setPropertyMappingValue(oCsnObject, csnMappingValue, object[sPropertyName]);
                  }
                }
              }
            }
          }
        }
      },

      /**
       * Sets a property value for the CSN object.
       * @param oCsnObject The current CSN object.
       * @param csnMappingValue The property key in the CSN object.
       * @param vPropertyValue The property value.
       */
      setPropertyMappingValue: function (oCsnObject: any, csnMappingValue: string | string[], vPropertyValue: any) {
        if (oCsnObject && csnMappingValue) {
          if (csnMappingValue instanceof Array) {
            if (csnMappingValue.length > 0) {
              // If there is a list of annotations, use the first one to set.
              oCsnObject[csnMappingValue[0]] = vPropertyValue;
            }
          } else {
            // Simple value: do not write empty string nor empty array
            if (vPropertyValue !== "" && !(Array.isArray(vPropertyValue) && vPropertyValue?.length === 0)) {
              oCsnObject[csnMappingValue] = vPropertyValue;
            }
          }
        }
      },
    },
  });
});
