/**
 * eslint-disable @typescript-eslint/prefer-for-of
 *
 * @format
 */

/** Copyright 2020 SAP SE or an SAP affiliate company. All rights reserved. */

import { getArtefactSharesForTarget } from "../../../services/metadata";
import { AdvisorUtil, IAdvisorResponse, ISpaceEntity } from "../../reuse/utility/AdvisorUtil";
import { NamingHelper } from "../../reuse/utility/NamingHelper";
import { ContentType, ServiceCall } from "../../reuse/utility/ServiceCall";
import { CurrentContextService } from "../api/CurrentContextService";
import { DocumentProperty, DocumentStorageService } from "../api/DocumentStorageService";
import { SupportedFeaturesService } from "../api/SupportedFeaturesService";
import { AllowConsumptionAnnotation } from "../model/annotations/allowConsumptionAnnotation";
import { BaseAnnotation } from "../model/annotations/baseAnnotation";
import { DataCategoryAnnotation } from "../model/annotations/dataCategoryAnnotation";
import { ExceptionAggregationStepsAnnotation } from "../model/annotations/exceptionAggregationStepsAnnotation";
import { LabelColumnAnnotation } from "../model/annotations/labelColumnAnnotation";
import { MeasureAnnotation } from "../model/annotations/measureAnnotation";
import { CommonQualifiedClassNames } from "../model/const/model.const";
import {
  ABAPDataType,
  ABAPType,
  CDSDataType,
  CDSDataTypePropertyValues,
  CDS_BINARY_MAX_LENGTH,
  CardinalityValues,
  DBViewType,
  DEFAULT_SRID,
  DataCategory,
  DecimalDataTypes,
  DimensionType,
  OperatorsList,
  SemanticType,
  SpatialResultOnErr,
  isCDSPrimitiveDataType,
} from "../model/types/cds.types";
import { isAnalyticMeasureElement, isViewOutput } from "../utility/CommonUtils";
import { releaseStateValues, releaseStates } from "../utility/CompatibilityContractUtils";
import { CsnAnnotations } from "./csnAnnotations";
import { newCsnPropertyMappings } from "./csnProperyMappings";

export const CSN_VERSION = "1.0";
export const DWC_CSN_CREATOR = "SAP Datasphere";

export enum Consumption_DB_Hints {
  USE_OLAP_PLAN = "USE_OLAP_PLAN",
}

export enum DELTATYPES {
  UPSERT = "UPSERT",
  Lake = "DELTA_LAKE",
}

export enum PARTITIONTYPES {
  RANGE = "RANGE",
  COLUMNS = "COLUMNS",
  HASH = "HASH",
}

// CurrencyConversion column's propertyName maps to parameter name in SQL
export const CurrencyConversionMappings = {
  sourceAmountColumn: "AMOUNT",
  steps: "STEPS",
  sourceCurrency: "SOURCE_UNIT",
  targetCurrency: "TARGET_UNIT",
  referenceDate: "REFERENCE_DATE",
  client: "CLIENT",
  conversionType: "CONVERSION_TYPE",
  errorHandling: "ERROR_HANDLING",
  lookup: "LOOKUP",
  accuracy: "ACCURACY",
  dateFormat: "DATE_FORMAT",
  precisionsTable: "PRECISIONS_TABLE",
  configurationTable: "CONFIGURATION_TABLE",
  prefactorsTable: "PREFACTORS_TABLE",
  ratesTable: "RATES_TABLE",
  notationsTable: "NOTATIONS_TABLE",
  schema: "SCHEMA",
};

/**
 * Get the first kind of object name from CSN.
 * @param {object} oCSNDefinitions
 * @param kind
 */
export const getObjectFromCSN = function (oCSNDefinitions: any = {}, objectName: string, kind?: string): any {
  let oCSNObject: any;
  if (oCSNDefinitions && objectName) {
    const csn = oCSNDefinitions[objectName];
    if (csn) {
      oCSNObject = !kind || csn.kind === kind ? csn : undefined;
    }
  }
  return oCSNObject;
};

/**
 * Add default scale 0 for cds.Decimal column if no scale
 * @param {object} oCSNDefinitions
 */
export const setDefaultScaleForDecimalColumn = function (oCSNDefinitions: any = {}): void {
  const elements = oCSNDefinitions?.elements;
  if (elements) {
    const keys = Object.keys(elements);
    for (const columnName of keys) {
      const column = elements[columnName];
      if (column?.type === "cds.Decimal" && column.scale === undefined) {
        column.scale = 0;
      }
    }
  }
};

export const getBreakingChanges = function (
  repoCSN: any,
  toCompareWithCSN: any
): { deletedElements: string[]; changedElements: string[] } {
  const getDefinition = (csn) => {
    let def = {};
    if (csn?.csn) {
      def = csn.csn.definitions[csn.name];
    } else if (csn?.definitions) {
      def = csn.definitions[csn.name];
    } else {
      def = csn;
    }
    return def && JSON.parse(JSON.stringify(def));
  };
  const deletedElements = [];
  const changedElements = [];
  const repoDefinition = getDefinition(repoCSN);
  const compareDefinition = getDefinition(toCompareWithCSN);

  if (repoDefinition && compareDefinition) {
    for (const eltName in repoDefinition.elements) {
      if (!compareDefinition.elements[eltName]) {
        deletedElements.push(eltName);
      } else if (
        compareDefinition.elements[eltName].type !== repoDefinition.elements[eltName].type ||
        compareDefinition.elements[eltName].length !== repoDefinition.elements[eltName].length ||
        compareDefinition.elements[eltName].scale !== repoDefinition.elements[eltName].scale ||
        compareDefinition.elements[eltName].precision !== repoDefinition.elements[eltName].precision
      ) {
        changedElements.push(eltName);
      }
    }
  }

  return {
    deletedElements: deletedElements,
    changedElements: changedElements,
  };
};

/**
 * Get the first kind of object name from CSN.
 * @param {object} oCSNDefinitions
 * @param kind
 */
export const getObjectNameFromCSN = function (oCSNDefinitions: any = {}, kind?: string): string {
  let objectName: string;
  if (oCSNDefinitions) {
    const keys = Object.keys(oCSNDefinitions);
    objectName = keys.find((key) => key.includes("%%END%%"));
    if (!objectName) {
      for (const key of keys) {
        if (oCSNDefinitions[key] && (!kind || oCSNDefinitions[key].kind === kind)) {
          objectName = key;
          break;
        }
      }
    }
  }
  return objectName;
};

/**
 * Get the list kind of object names from CSN.
 * @param {object} oCSNDefinitions
 * @param kind
 */
export const getObjectNamesFromCSN = function (oCSNDefinitions: any = {}, kind?: string): string[] {
  const objectNames: string[] = [];
  if (oCSNDefinitions) {
    const keys = Object.keys(oCSNDefinitions);
    for (const objectName of keys) {
      if (oCSNDefinitions[objectName] && (!kind || oCSNDefinitions[objectName].kind === kind)) {
        objectNames.push(objectName);
      }
    }
  }
  return objectNames;
};

/**
 * Get the first entity name from CSN.
 * @param {object} oCSNDefinitions
 */
export const getEntityNameFromCSN = function (oCSNDefinitions: any = {}): string {
  return getObjectNameFromCSN(oCSNDefinitions, "entity") || getObjectNameFromCSN(oCSNDefinitions); // For remote table, the entity details could be a promise.
};

/**
 * Get cross space entity CSN including context from source entity file
 * @param {object} file
 */
export const getCrossSpaceEntityCSNFromSourceEntityFile = function (file: any = {}): any {
  if (file && file.csn && file.csn.definitions) {
    const result = cloneJson(file);
    const definitions = result.csn.definitions;
    const entityName = definitions && getEntityNameFromCSN(definitions);
    if (file.spaceName !== undefined && entityName !== undefined) {
      definitions[file.spaceName + "." + entityName] = {
        ...definitions[entityName],
        [CsnAnnotations.cds.persistence_exists]: true,
        [CsnAnnotations.DataWarehouse.space_name]: file.spaceName,
        [CsnAnnotations.DataWarehouse.space_schema]: file.spaceSchemaName,
        [CsnAnnotations.DataWarehouse.space_businessName]: file.spaceBusinessName,
      };
      delete definitions[entityName];

      definitions[file.spaceName] = {
        kind: "context",
        [CsnAnnotations.DataWarehouse.space_name]: file.spaceName,
        [CsnAnnotations.DataWarehouse.space_schema]: file.spaceSchemaName,
        [CsnAnnotations.EndUserText.label]: file.spaceBusinessName,
      };

      // Remove references to other objects them may not be shared
      removeCrossSpaceReferencesInCSN(definitions, file.spaceName + "." + entityName);
    }

    return result;
  }
};

/**
 * Get the list of entity names from CSN.
 * @param {object} oCSNDefinitions
 */
export const getEntityNamesFromCSN = function (oCSNDefinitions: any = {}): string[] {
  return getObjectNamesFromCSN(oCSNDefinitions, "entity");
};

/**
 * Get the entity context name if it exists in the CSN.
 * @param {object} oCSNDefinitions
 * @param {object} entityName
 */
export const getEntityContextNameFromCSN = function (
  oCSNDefinitions: any = {},
  entityName: string,
  bFirstQualifier = false
): string {
  let contextName: string;
  if (oCSNDefinitions && entityName) {
    const qualifier = sap.cdw.commonmodel.ObjectImpl.getQualifierName(entityName, bFirstQualifier);
    if (qualifier && oCSNDefinitions[qualifier] && oCSNDefinitions[qualifier].kind === "context") {
      const context = getObjectFromCSN(oCSNDefinitions, qualifier, "context");
      if (context) {
        contextName = qualifier;
      }
    }
  }
  return contextName;
};

/**
 * Get the first entity and its context from CSN.
 * @param {object} oCSNDefinitions
 */
export const getEntityAndContextNameFromCSN = function (oCSNDefinitions: any = {}, bFirstQualifier = false): any {
  const entityName = getEntityNameFromCSN(oCSNDefinitions);
  let contextName: string;
  if (oCSNDefinitions && entityName) {
    contextName = getEntityContextNameFromCSN(oCSNDefinitions, entityName, bFirstQualifier);
    if (!contextName && bFirstQualifier === false) {
      // Try with the first qualifier
      contextName = getEntityContextNameFromCSN(oCSNDefinitions, entityName, true);
    }
  }
  return { entityName, contextName };
};

/**
 * Check if an entity is a cross space entity.
 * @param {object} oCSNDefinitions
 * @param {object} entityName
 */
export const isCrossSpaceEntityFromCSN = function (oCSNDefinitions: any = {}, entityName: string): boolean {
  if (oCSNDefinitions && entityName) {
    const entity = getObjectFromCSN(oCSNDefinitions, entityName, "entity");
    if (!entity) {
      return false;
    }

    const contextName = getEntityContextNameFromCSN(oCSNDefinitions, entityName, /* bFirstQualifier */ true);
    if (!contextName) {
      return false;
    }
    const context = getObjectFromCSN(oCSNDefinitions, contextName, "context");
    if (context) {
      if (context[CsnAnnotations.DataWarehouse.space_name]) {
        return true;
      }
    }
  }
  return false;
};

/**
 * Get the first entity and its cross space context name from CSN.
 * @param {object} oCSNDefinitions
 */
export const getEntityAndCrossSpaceNameFromCSN = function (oCSNDefinitions: any = {}): any {
  let entityName = getEntityNameFromCSN(oCSNDefinitions);
  let contextName: string;
  let spaceName: string;
  if (oCSNDefinitions && entityName) {
    contextName = getEntityContextNameFromCSN(oCSNDefinitions, entityName, /* bFirstQualifier */ true);
    if (contextName) {
      const context = getObjectFromCSN(oCSNDefinitions, contextName, "context");
      if (context) {
        if (context[CsnAnnotations.DataWarehouse.space_name]) {
          spaceName = contextName;
          entityName = sap.cdw.commonmodel.ObjectImpl.removeQualifierInName(entityName, /* bFirstQualifier */ true);
        }
      }
    }
  }
  return { entityName, spaceName };
};

/**
 * Get the primitive data type of an element or simple type from CSN.
 * @param oCSNDefinitions
 * @param typeName
 */
export const getPrimitiveDataTypeFromCSN = function (oCSNDefinitions: any = {}, typeName: string): string {
  let primitiveDataType = typeName;
  if (oCSNDefinitions && typeName) {
    while (!isCDSPrimitiveDataType(typeName)) {
      const oCSNType = getObjectFromCSN(oCSNDefinitions, typeName, "type");
      if (oCSNType) {
        primitiveDataType = typeName = oCSNType.type;
      } else {
        break;
      }
    }
  }
  return primitiveDataType;
};

/**
 * For a cross space entity, remove query, associations, DAC.
 * @param oCSNDefinitions
 * @param {object} entityName
 */
export const removeCrossSpaceReferencesInCSN = function (oCSNDefinitions: any = {}, entityName: string): void {
  if (oCSNDefinitions && entityName) {
    const oCSNEntity = getObjectFromCSN(oCSNDefinitions, entityName, "entity");

    if (oCSNEntity) {
      // Remove query
      if (oCSNEntity.query) {
        delete oCSNEntity.query;
      }

      // Remove DAC
      if (oCSNEntity["@DataWarehouse.dataAccessControl.usage"]) {
        delete oCSNEntity["@DataWarehouse.dataAccessControl.usage"];
      }

      // Remove associations and simple types
      if (oCSNEntity.elements) {
        for (const elementName of Object.keys(oCSNEntity.elements)) {
          const element = oCSNEntity.elements[elementName];
          if (element) {
            if (element.type === CDSDataType.ASSOCIATION || element.type === CDSDataType.COMPOSITION) {
              // Remove association
              delete oCSNEntity.elements[elementName];
            } else if (!isCDSPrimitiveDataType(element.type)) {
              // Remove simple type
              const primaryDataType = getPrimitiveDataTypeFromCSN(oCSNDefinitions, element.type);
              if (primaryDataType) {
                element.type = primaryDataType;
                // Remove simple type completely
                if (element.kind === "type") {
                  delete element.kind;
                }
              }
            } else if (isCDSPrimitiveDataType(element.type)) {
              // Remove simple type completely
              if (element.kind === "type") {
                delete element.kind;
              }
            }
          }
        }
      }
    }
  }
};

/**
 * Gets an empty CSN document object.
 * @param {object} oParam The parameters
 *  creator: The creator.
 *  kind: The document kind.
 *  label: The document label.
 */
export const getEmptyCSNDocument = function (oParam: any = {}): any {
  const csn = {
    definitions: {},
    version: {
      csn: CSN_VERSION,
    },
    meta: {
      creator: oParam.creator || DWC_CSN_CREATOR,
    },
    $version: CSN_VERSION,
  };
  if (oParam.kind) {
    (csn.meta as any).kind = oParam.kind;
  }
  if (oParam.label) {
    (csn.meta as any).label = oParam.label;
  }
  if (oParam.deploymentHints) {
    (csn as any).deploymentHints = {};
  }
  return csn;
};

/**
 * Converts an entity or view name to valid qualified name for CSN.
 * @param {string} sQualifiedName
 */
export const getQualifiedEntityName = function (sQualifiedName: string): string {
  if (sQualifiedName) {
    sQualifiedName = sQualifiedName.replace(/[^a-zA-Z0-9!#%&'()+,-.;=@\[\]^_`{}~]/g, "_");
    sQualifiedName = sQualifiedName.replace(/__*/g, "_");
  }
  return sQualifiedName;
};

/**
 * Ensure that object has business name
 * @param object
 */
export function ensureBusinessName(object: sap.cdw.commonmodel.BaseObject) {
  if (object && (object.label === "" || object.label === undefined)) {
    object.label = object.name;
  }
}

/**
 * Ensure that value is a number of undefined.
 * @param value
 */
export function ensureNumber(value: any): number {
  if (isNaN(value)) {
    value = undefined;
  } else if (typeof value === "string") {
    value = parseInt(value, 10);
  }
  return value;
}

/**
 * Gets the name of super classes and its own class qualified names.
 * @param object
 */
export function getClassHierarchy(object: sap.cdw.commonmodel.BaseObject) {
  const aClasses = [];

  if (object && object.classDefinition) {
    const sClassName = object.classDefinition.qualifiedName;
    aClasses.push(sClassName);

    // Get super classes
    let superClass = object.classDefinition.parent;
    let superClassName;
    while (superClass) {
      superClassName = superClass.qualifiedName;
      if (superClassName === "sap.galilei.model.Object") {
        break;
      }
      if (!aClasses.includes(superClassName)) {
        aClasses.unshift(superClassName);
      }
      superClass = superClass.parent;
    }
  }
  return aClasses;
}
export function getClassDefinitionHierarchy(classDefinition: any) {
  const aClasses = [];

  if (classDefinition) {
    const sClassName = classDefinition.qualifiedName;
    aClasses.push(sClassName);

    // Get super classes
    let superClass = classDefinition.parent;
    let superClassName;
    while (superClass) {
      superClassName = superClass.qualifiedName;
      if (superClassName === "sap.galilei.model.Object") {
        break;
      }
      if (!aClasses.includes(superClassName)) {
        aClasses.unshift(superClassName);
      }
      superClass = superClass.parent;
    }
  }
  return aClasses;
}

/**
 * Gets object properties to CSN properties mapping for a class.
 * @param sClassName The class qualified name.
 */
export function getObjectMapping(oPropertyMappings: any, sClassName: string): any {
  if (oPropertyMappings && sClassName) {
    if (oPropertyMappings[sClassName]) {
      return oPropertyMappings[sClassName];
    }
    if (sClassName.includes(".")) {
      const shortClassName = sClassName.substring(sClassName.lastIndexOf(".") + 1);
      return (
        oPropertyMappings[shortClassName] ||
        (SupportedFeaturesService.getInstance().isSkylineERModelerEnabled() && newCsnPropertyMappings[shortClassName])
      );
    }
  }
}

/**
 * Write label -> @EndUserText.label (or @Common.Label)
 * "label": [CsnAnnotations.EndUserText.label, CsnAnnotations.Common.Label],
 * @param baseObject
 * @param oCsnObject
 */
export function labelWriteCsn(
  baseObject: sap.cdw.commonmodel.BaseObject | sap.cdw.commonmodel.BaseLinkObject,
  oCsnObject: any
) {
  if (baseObject && oCsnObject) {
    if (baseObject.label && baseObject.label.trim() !== "") {
      oCsnObject[CsnAnnotations.EndUserText.label] = baseObject.label.trim();
    }
  }
}

/**
 * Read label -> @EndUserText.label (or @Common.Label)
 * "label": [CsnAnnotations.EndUserText.label, CsnAnnotations.Common.Label],
 * @param baseObject
 * @param oCsnObject
 */
export function labelReadCsn(
  baseObject: sap.cdw.commonmodel.BaseObject | sap.cdw.commonmodel.BaseLinkObject,
  oCsnObject: any
) {
  if (baseObject && oCsnObject) {
    if (oCsnObject[CsnAnnotations.EndUserText.label] && oCsnObject[CsnAnnotations.EndUserText.label].trim() !== "") {
      baseObject.label = oCsnObject[CsnAnnotations.EndUserText.label].trim();
    } else if (oCsnObject[CsnAnnotations.Common.Label] && oCsnObject[CsnAnnotations.Common.Label].trim() !== "") {
      baseObject.label = oCsnObject[CsnAnnotations.Common.Label].trim();
    }
    if (baseObject.unhandledCsn) {
      BaseAnnotation.deleteHandledAnnotations(baseObject.unhandledCsn, [
        CsnAnnotations.Common.Label,
        CsnAnnotations.EndUserText.label,
      ]);
    }
  }
}
export function labelGetAnnotations() {
  return [CsnAnnotations.Common.Label, CsnAnnotations.EndUserText.label, "name", "alias"];
}

/**
 * Write dataCategory -> @Analytics.dataCategory
 * @param entity
 * @param oCsnEntity
 */
export function dataCategoryWriteCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any) {
  if (entity && oCsnEntity) {
    if (entity.dataCategory) {
      DataCategoryAnnotation.writeCsnValue(oCsnEntity, entity.dataCategory as DataCategory);
      if (SupportedFeaturesService.getInstance().isFiscalTimeDimensionEnabled()) {
        // Clear known values
        if (oCsnEntity) {
          delete oCsnEntity[CsnAnnotations.FiscalTimeAnnotations.yearPeriod];
          delete oCsnEntity[CsnAnnotations.FiscalTimeAnnotations.interval];
        }
        if (
          (entity.dataCategory as DataCategory) === DataCategory.DIMENSION &&
          entity.dimensionType === DimensionType.FISCAL_TIME
        ) {
          DataCategoryAnnotation.writeFiscalTimeRelatedCsnValue(oCsnEntity, entity);
        }
      }
      // DW101-2650 Restore SQL_DATA_SOURCE capability
      DataCategoryAnnotation.restoreSqlDataSourceCapability(oCsnEntity, entity);
    }
  }
}

/**
 * Read dataCategory <- @Analytics.dataCategory
 * @param entity
 * @param oCsnEntity
 */
export function dataCategoryReadCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any) {
  if (entity && oCsnEntity) {
    const dataCategory: DataCategory = DataCategoryAnnotation.readCsn(oCsnEntity);
    if (dataCategory) {
      entity.dataCategory = dataCategory;
      if (SupportedFeaturesService.getInstance().isFiscalTimeDimensionEnabled()) {
        if (dataCategory === DataCategory.DIMENSION) {
          // DimensionType.FISCAL_TIME
          const { dimensionType, fiscalTimeStartCol, fiscalTimeEndCol } =
            DataCategoryAnnotation.readFiscalTimeRelatedCsnValue(oCsnEntity);
          entity.dimensionType = dimensionType;
          entity.fiscalTimeSettingsStart = findInCollectionByNewNameOrName(entity?.elements, fiscalTimeStartCol);
          entity.fiscalTimeSettingsEnd = findInCollectionByNewNameOrName(entity?.elements, fiscalTimeEndCol);
        }
      }
      // DW101-2650 Save SQL_DATA_SOURCE capability
      DataCategoryAnnotation.saveSqlDataSourceCapability(oCsnEntity, entity);
    }
    if (entity.unhandledCsn) {
      DataCategoryAnnotation.deleteHandledAnnotation(entity.unhandledCsn);
    }
  }
}
export function dataCategoryGetAnnotations() {
  return DataCategoryAnnotation.getAnnotations();
}

export function getCommonParamCsn(parameter: sap.cdw.commonmodel.Parameter): any {
  let param: any = {};
  if (parameter.unhandledCsn) {
    param = { ...parameter.unhandledCsn };
  }
  // Add parameter properties and annotations
  const modelToCsn = sap.cdw.commonmodel.ModelToCsn.getInstance();
  modelToCsn.addDefaultPropertyMapping(param, parameter);
  if (parameter.defaultValue) {
    if (parameter.primitiveDataType === CDSDataType.BOOLEAN && parameter.defaultValue === "true") {
      param.default = true;
    } else if (parameter.primitiveDataType === CDSDataType.BOOLEAN && parameter.defaultValue === "false") {
      param.default = false;
    } else {
      param.default = parameter.defaultValue;
    }
  }

  let length = ensureNumber(parameter.length);
  // Check dataType
  if (parameter.primitiveDataType === CDSDataType.DECIMAL) {
    // For cds.Decimal, precision and scale must be defined.
    length = undefined;
    let precision = ensureNumber(parameter.precision);
    let scale = ensureNumber(parameter.scale);
    if (isNaN(precision)) {
      precision = 8;
    }
    if (isNaN(scale)) {
      scale = 0;
    }
    param.precision = precision;
    param.scale = scale;
  } else if (
    parameter.primitiveDataType === CDSDataType.INTEGER ||
    parameter.primitiveDataType === CDSDataType.INTEGER64
  ) {
    length = undefined;
  }
  if (length !== undefined && length > 0) {
    param.length = length;
  }
  if (parameter.name === "EXTRACTION_MODE") {
    param[CsnAnnotations.DataWarehouse.bw_extractionMode] = true;
  }
  return param;
}

export function getLtfParameters(entity: sap.cdw.commonmodel.Entity): any {
  const parameterClass = sap.galilei.model.getClass(CommonQualifiedClassNames.PARAMETER);
  const parameter1 = parameterClass.create(entity.resource, {
    displayName: "Extraction Mode",
    dataType: "cds.String",
    length: "10",
    name: "EXTRACTION_MODE",
    defaultValue: "FULL",
    extractionMode: true,
  });
  const parameter2 = parameterClass.create(entity.resource, {
    displayName: "From Change Time",
    dataType: "cds.Timestamp",
    name: "FROM_CHANGE_TIME",
    defaultValue: "0001-01-01 00:00:00",
  });
  const parameter3 = parameterClass.create(entity.resource, {
    displayName: "Till Change Time",
    dataType: "cds.Timestamp",
    name: "TILL_CHANGE_TIME",
    defaultValue: "9999-12-31 23:59:59",
  });
  entity.parameters.push(parameter1);
  entity.parameters.push(parameter2);
  entity.parameters.push(parameter3);
}

export function paramsWriteCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any) {
  if (entity?.fileStorage && entity?.deltaTable?.type && entity?.parameters?.length === 0) {
    getLtfParameters(entity);
  }
  if (entity.parameters && oCsnEntity && entity.parameters.length > 0) {
    if (!oCsnEntity.params) {
      oCsnEntity.params = {};
    }
    entity.parameters.forEach((parameter: sap.cdw.commonmodel.Parameter) => {
      oCsnEntity.params[parameter.name] = getCommonParamCsn(parameter);
    });
    if (entity?.isRemote) {
      oCsnEntity[CsnAnnotations.cds.persistence_udf] = true;
    }
  }
}

export function paramsReadCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any, isMapping?: any) {
  if ((entity && oCsnEntity && oCsnEntity.params && !entity.isCrossSpace) || isMapping) {
    const parameterClass = sap.galilei.model.getClass(CommonQualifiedClassNames.PARAMETER);
    const oCsnParams = oCsnEntity.params;
    if (oCsnParams) {
      const aParamKeys = Object.keys(oCsnParams);
      for (const paramKey of aParamKeys) {
        // skip existing parameter
        if (entity?.parameters?.selectObject({ name: paramKey })) {
          continue;
        }
        // skip analytic parameter
        const csnParam = oCsnParams[paramKey];
        if (isAnalyticParameter(csnParam)) {
          continue;
        }
        const parameter = parameterClass.create(entity.resource, {
          displayName: csnParam[CsnAnnotations.EndUserText.label],
          dataType: csnParam.type,
          length: csnParam?.length,
          precision: csnParam?.precision,
          scale: csnParam?.scale,
          name: paramKey,
          default: csnParam.default,
          defaultValue: csnParam.default,
          valueHelpDefinition: csnParam[CsnAnnotations.Consumption.valueHelpDefinition],
        });
        // Read mapped csn attributes/annotations
        const erCsnToModel = sap.cdw.ermodeler.ErCsnToModel.getInstance(true);
        erCsnToModel?.getDefaultPropertyMapping(parameter, csnParam);
        // Add parameter
        entity.parameters.push(parameter);
      }
      if (entity.unhandledCsn) {
        BaseAnnotation.deleteHandledAnnotation(entity.unhandledCsn, "params");
      }
    }
  }
}
export function paramsGetAnnotations() {
  return ["params"];
}

export function representativeKeyWriteCsn(entity, oCsnEntity: any) {
  const representativeKeyName = entity?.representativeKey?.newName || entity?.representativeKey?.name;
  const compoundKeySequences = [];
  if (representativeKeyName) {
    oCsnEntity["@ObjectModel.representativeKey"] = { "=": representativeKeyName };
  } else {
    if (oCsnEntity["@ObjectModel.representativeKey"]) {
      delete oCsnEntity["@ObjectModel.representativeKey"];
    }
  }
  if (entity.compoundKeySequence && representativeKeyName) {
    for (const element of entity.compoundKeySequence.toArray()) {
      compoundKeySequences.push({ "=": element.newName || element.name });
    }
    if (representativeKeyName) {
      compoundKeySequences.push({ "=": representativeKeyName });
    }
  }
  if (compoundKeySequences.length > 0) {
    oCsnEntity[CsnAnnotations.DataWarehouse.compoundKeySequence] = compoundKeySequences;
  } else {
    if (oCsnEntity[CsnAnnotations.DataWarehouse.compoundKeySequence]) {
      delete oCsnEntity[CsnAnnotations.DataWarehouse.compoundKeySequence];
    }
  }
}

export function representativeKeyReadCsn(entity, oCsnEntity: any) {
  const representativeKey =
    oCsnEntity[CsnAnnotations.ObjectModel.representativeKey] &&
    oCsnEntity[CsnAnnotations.ObjectModel.representativeKey]["="];
  let representativeElement = representativeKey && findInCollectionByName(entity.elements, representativeKey);
  if (!representativeElement) {
    representativeElement = representativeKey && findInCollectionByNewName(entity.elements, representativeKey);
  }
  if (representativeElement) {
    const isExists = entity?.representativeKey?.name === representativeElement?.name;
    if (!isExists) {
      entity.representativeKey = representativeElement;
    }
  }
  if (oCsnEntity[CsnAnnotations.DataWarehouse.compoundKeySequence]) {
    if (entity?.compoundKeySequence?.length > 0) {
      entity?.compoundKeySequence?.clear();
    }
    for (const compoundElementName of oCsnEntity[CsnAnnotations.DataWarehouse.compoundKeySequence]) {
      const compoundKeyName = compoundElementName["="];
      let compoundElement = findInCollectionByName(entity.elements, compoundKeyName);
      if (!compoundElement) {
        compoundElement = findInCollectionByNewName(entity.elements, compoundKeyName);
      }
      if (compoundElement !== representativeElement) {
        const isExists = entity?.compoundKeySequence?.selectObject({
          name: compoundElement.name,
        });
        if (!isExists) {
          entity?.compoundKeySequence?.push(compoundElement);
        }
      }
    }
  }
}
export function representativeKeyGetAnnotations() {
  return [CsnAnnotations.ObjectModel.representativeKey, CsnAnnotations.DataWarehouse.compoundKeySequence];
}
// _DP_ Analytic Parameters
export function isAnalyticParameter(csnParameter: any): boolean {
  const referenceElement = BaseAnnotation.getCsnEqualValue(
    csnParameter,
    CsnAnnotations.AnalyticsDetails.variable_referenceElement
  );
  return referenceElement !== undefined;
}
export function analyticParametersWriteCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any) {
  // do Nothing if asked to skip
  if ((entity as any)._skipAnalyticMeasures === true) {
    return;
  }
  if (entity.analyticParameters && oCsnEntity && entity.analyticParameters.length > 0) {
    if (!oCsnEntity.params) {
      oCsnEntity.params = {};
    }
    entity.analyticParameters.forEach((parameter: sap.cdw.commonmodel.Parameter) => {
      const displayName = parameter.displayName;
      const referenceElement =
        sap.cdw.commonmodel.ObjectImpl.getAnalyticParameterReferenceElement(parameter) || parameter;
      const name = parameter.name;
      const defaultValue = parameter.defaultValue;
      const dataType = referenceElement?.dataType;
      const length = referenceElement?.length;
      const precision = referenceElement?.precision;
      const scale = referenceElement?.scale;
      const param: any = {
        "@EndUserText.label": displayName,
        type: dataType,
      };
      if (parameter.unhandledCsn) {
        Object.assign(param, cloneJson(parameter.unhandledCsn));
      }
      if (defaultValue) {
        param.default = defaultValue;
      }
      if (length) {
        param.length = length;
      }
      if (precision) {
        param.precision = precision;
      }
      if (scale) {
        param.scale = scale;
      }
      // TODO: Handle with constants..
      BaseAnnotation.setCsnEnumValue(param, CsnAnnotations.AnalyticsDetails.variable_usageType, "FILTER");
      BaseAnnotation.setCsnEnumValue(param, CsnAnnotations.AnalyticsDetails.variable_selectionType, "SINGLE");
      if (defaultValue) {
        BaseAnnotation.setCsnValue(param, CsnAnnotations.AnalyticsDetails.analyticParamDefaultValue, defaultValue);
      }
      // referenceElement
      if (referenceElement) {
        BaseAnnotation.setCsnEqualValue(
          param,
          CsnAnnotations.AnalyticsDetails.variable_referenceElement,
          referenceElement.newName
        );
      }
      BaseAnnotation.setCsnValue(param, CsnAnnotations.AnalyticsDetails.variable_multipleSelections, true);
      oCsnEntity.params[name] = param;
    });
  }
}

export function analyticParametersReadCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any) {
  if (entity && oCsnEntity && oCsnEntity.params && !entity.isCrossSpace) {
    const analyticParameterClass = sap.galilei.model.getClass(CommonQualifiedClassNames.ANALYTIC_PARAMETER);
    const oCsnParams = oCsnEntity.params;
    if (oCsnParams) {
      const aParamKeys = Object.keys(oCsnParams);
      for (const paramKey of aParamKeys) {
        const csnParam = oCsnParams[paramKey];
        if (isAnalyticParameter(csnParam)) {
          let analyticParameter = analyticParameterClass.create(entity.resource, {
            displayName: csnParam["@EndUserText.label"],
            dataType: csnParam.type,
            length: csnParam.length,
            precision: csnParam.precision,
            scale: csnParam.scale,
            name: paramKey,
            default: csnParam.default,
            defaultValue: csnParam[CsnAnnotations.AnalyticsDetails.analyticParamDefaultValue],
          });
          const existingParameter = entity.analyticParameters?.selectObject({ name: analyticParameter.name });
          if (existingParameter) {
            analyticParameter = existingParameter;
          } else {
            entity.analyticParameters.push(analyticParameter);
          }
          // Handle Common/Custom annotations
          const erCsnToModel = sap.cdw.ermodeler.ErCsnToModel.getInstance(true);
          erCsnToModel?.getDefaultPropertyMapping(analyticParameter, csnParam);
          // Handle variable_referenceElement
          const refElementName = BaseAnnotation.getCsnEqualValue(
            csnParam,
            CsnAnnotations.AnalyticsDetails.variable_referenceElement
          );
          if (analyticParameter && refElementName) {
            sap.cdw.commonmodel.ObjectImpl.setAnalyticParameterReferenceElementFromName(
              analyticParameter,
              refElementName
            );
          }
        }
      }
      if (entity.unhandledCsn) {
        BaseAnnotation.deleteHandledAnnotation(entity.unhandledCsn, "params");
      }
    }
  }
}
export function analyticParametersGetAnnotations() {
  return ["params"];
}

/**
 * Write dbViewType -> @Analytics.dbViewType
 * @param entity
 * @param oCsnEntity
 */
export function dbViewTypeWriteCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any) {
  if (entity && oCsnEntity) {
    if (entity.dbViewType === DBViewType.TABLEFUNCTION) {
      oCsnEntity[CsnAnnotations.Analytics.dbViewType] = entity.dbViewType;
    }
  }
}

/**
 * Read dbViewType <- @Analytics.dbViewType
 * @param entity
 * @param oCsnEntity
 */
export function dbViewTypeReadCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any) {
  if (entity && oCsnEntity) {
    if (oCsnEntity[CsnAnnotations.Analytics.dbViewType]) {
      entity.dbViewType = oCsnEntity[CsnAnnotations.Analytics.dbViewType];
      if (entity.unhandledCsn) {
        BaseAnnotation.deleteHandledAnnotation(entity.unhandledCsn, CsnAnnotations.Analytics.dbViewType);
      }
    }
  }
}
export function dbViewTypeGetAnnotations() {
  return [CsnAnnotations.Analytics.dbViewType];
}

/**
 * Write hierarchyWithDirectory -> @Hierarchy.parentChild
 * @param entity
 * @param oCsnEntity
 */
export function hierarchyWithDirectoryWriteCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any) {
  if (oCsnEntity) {
    if (entity?.hierarchyWithDirectory) {
      oCsnEntity[CsnAnnotations.Hierarchy.parentChild] = entity.hierarchyWithDirectory;
    } else if (oCsnEntity[CsnAnnotations.Hierarchy.parentChild]?.["0"]?.nodeType) {
      delete oCsnEntity[CsnAnnotations.Hierarchy.parentChild];
    }
  }
}

/**
 * Read hierarchyWithDirectory <- @Hierarchy.parentChild
 * @param entity
 * @param oCsnEntity
 */
export function hierarchyWithDirectoryReadCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any) {
  if (entity && oCsnEntity) {
    // (entity.hierarchies as sap.galilei.model.ChildrenCollection)?.deleteAll();
    if (oCsnEntity[CsnAnnotations.Hierarchy.parentChild]?.["0"]?.nodeType) {
      entity.hierarchyWithDirectory = oCsnEntity[CsnAnnotations.Hierarchy.parentChild];
    }
  }
}

export function hierarchyWithDirectoryGetAnnotations() {
  return [CsnAnnotations.Hierarchy.parentChild];
}

/**
 * Write parent-child and level-based hierarchies -> @Hierarchy.parentChild, @Hierarchy.leveled
 * @param entity
 * @param oCsnEntity
 */
export function hierarchyWriteCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any) {
  if (!entity.hierarchies || entity.hierarchies.length === 0) {
    return;
  }
  const hierarchies = entity.hierarchies.toArray();

  // Level-Based Hierarchies
  const lbh = (hierarchies as sap.cdw.commonmodel.LevelBasedHierarchy[]).filter((hierarchy) => !!hierarchy.levels);
  if (lbh && lbh.length) {
    oCsnEntity[CsnAnnotations.Hierarchy.leveled] = lbh.map((hierarchy) => ({
      name: hierarchy.name,
      label: hierarchy.label,
      levels: hierarchy.orderedLevels
        .filter((levelElement) => !!levelElement.element)
        .map((levelElement) => ({
          element: {
            "=": levelElement.element.newName || levelElement.element.name,
          },
        })),
      ...hierarchy.unhandledCsn,
    }));
  }

  // Parent-Child Hierarchies
  const pch = (hierarchies as sap.cdw.commonmodel.ParentChildHierarchy[]).filter(
    (hierarchy) => hierarchy?.parentElement?.length > 0 && hierarchy?.childElement?.length > 0
  );
  if (pch && pch.length) {
    // External Hierarchy case: Use entity label as hierarchy label
    const externalHierarchyLabel = entity.isHierarchy && pch.length === 1 && entity.label;
    oCsnEntity[CsnAnnotations.Hierarchy.parentChild] = pch.map((hierarchy) => ({
      name: hierarchy.name,
      label: externalHierarchyLabel || hierarchy.label,
      recurse: {
        parent: hierarchy.parentElement.map((pElm) => ({ "=": pElm.newName || pElm.name })),
        child: hierarchy.childElement.map((cElm) => ({ "=": cElm.newName || cElm.name })),
      },
      ...hierarchy.unhandledCsn,
    }));
  }
}

/**
 * Read parent-child and level-based hierarchies <- @Hierarchy.parentChild, @Hierarchy.leveled
 * @param entity
 * @param oCsnEntity
 */
export function hierarchyReadCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any) {
  if (entity && oCsnEntity) {
    (entity.hierarchies as sap.galilei.model.ChildrenCollection)?.deleteAll();
    if (oCsnEntity[CsnAnnotations.Hierarchy.leveled]) {
      readLevelBasedHierarchyCsn(entity, oCsnEntity[CsnAnnotations.Hierarchy.leveled]);
      if (entity.unhandledCsn) {
        BaseAnnotation.deleteHandledAnnotation(entity.unhandledCsn, CsnAnnotations.Hierarchy.leveled);
      }
    }
    if (
      oCsnEntity[CsnAnnotations.Hierarchy.parentChild] &&
      !oCsnEntity[CsnAnnotations.Hierarchy.parentChild]["0"]?.nodeType
    ) {
      readParentChildHierarchyCsn(entity, oCsnEntity[CsnAnnotations.Hierarchy.parentChild]);
      if (entity.unhandledCsn) {
        BaseAnnotation.deleteHandledAnnotation(entity.unhandledCsn, CsnAnnotations.Hierarchy.parentChild);
      }
    }
  }
}
export function hierarchiesGetAnnotations() {
  return [CsnAnnotations.Hierarchy.parentChild, CsnAnnotations.Hierarchy.leveled];
}

/**
 * Read level-based hierarchies <- @Hierarchy.leveled
 * @param entity
 * @param oCsnHierarchy
 */
export function readLevelBasedHierarchyCsn(entity: sap.cdw.commonmodel.Entity, oCsnHierarchy: any) {
  const hierarchyClass = sap.galilei.model.getClass(CommonQualifiedClassNames.LEVEL_BASED_HIERARCHY);
  const levelElementClass = sap.galilei.model.getClass(CommonQualifiedClassNames.LEVEL_BASED_HIERARCHY_ELEMENT);
  oCsnHierarchy.forEach((csnHierarchy) => {
    const hierarchy = hierarchyClass.create(entity.resource, {
      name: csnHierarchy.name,
      label: csnHierarchy.label,
    });
    hierarchy.unhandledCsn = cloneJson(csnHierarchy);
    csnHierarchy.levels.forEach((csnLevel, i) => {
      const elementName = csnLevel.element["="];
      let element = entity.elements.filter((el) => elementName === el.newName)[0];
      if (!element) {
        element = entity.elements.filter((el) => elementName === el.name)[0];
      }
      const levelElement = levelElementClass.create(entity.resource, {
        indexOrder: i,
        element,
      });
      hierarchy.levels.push(levelElement);
    });
    BaseAnnotation.deleteHandledAnnotations(hierarchy.unhandledCsn, ["name", "label", "levels"]);
    entity.hierarchies.push(hierarchy);
  });
}

/**
 * Read parent-child hierarchies <- @Hierarchy.parentChild
 * @param entity
 * @param oCsnHierarchy
 */
export function readParentChildHierarchyCsn(entity: sap.cdw.commonmodel.Entity, oCsnHierarchy: any) {
  if (!entity || !oCsnHierarchy) {
    return;
  }
  const hierarchyClass = sap.galilei.model.getClass(CommonQualifiedClassNames.PARENT_CHILD_HIERARCHY);
  oCsnHierarchy.forEach((csnHierarchy) => {
    if (!csnHierarchy || !csnHierarchy.recurse || !csnHierarchy.recurse.parent || !csnHierarchy.recurse.child) {
      return;
    }
    const hierarchy = hierarchyClass.create(entity.resource, {
      name: csnHierarchy.name,
      label: csnHierarchy.label,
    });
    hierarchy.unhandledCsn = cloneJson(csnHierarchy);
    csnHierarchy.recurse.parent &&
      csnHierarchy.recurse.parent.forEach((pElm) => {
        let pElmName;
        if (typeof pElm === "string") {
          pElmName = pElm;
        } else {
          pElmName = pElm["="];
        }

        let parentElement = entity.elements.filter((elm) => pElmName === elm.newName)[0];
        if (!parentElement) {
          parentElement = entity.elements.filter((elm) => pElmName === elm.name)[0];
        }
        hierarchy.parentElement.push(parentElement);
      });
    csnHierarchy.recurse.child &&
      csnHierarchy.recurse.child.forEach((cElm) => {
        let cElmName;
        if (typeof cElm === "string") {
          cElmName = cElm;
        } else {
          cElmName = cElm["="];
        }

        let childElement = entity.elements.filter((elm) => cElmName === elm.newName)[0];
        if (!childElement) {
          childElement = entity.elements.filter((elm) => cElmName === elm.name)[0];
        }
        hierarchy.childElement.push(childElement);
      });
    if (!hierarchy.parentElement || !hierarchy.childElement) {
      return;
    }
    BaseAnnotation.deleteHandledAnnotations(hierarchy.unhandledCsn, ["name", "label", "recurse"]);
    entity.hierarchies.push(hierarchy);
  });
}

/**
 * Write remote table properties -> @DataWarehouse.remote.connection, @DataWarehouse.remote.entity
 * @param entity
 * @param oCsnEntity
 */
export function remoteTableWriteCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any) {
  if (entity && entity.isTable && oCsnEntity) {
    const table = entity as sap.cdw.commonmodel.Table;
    if (table.remote) {
      if (table.remote.connection) {
        oCsnEntity[CsnAnnotations.DataWarehouse.remote_connection] = table.remote.connection;
      }
      if (table.remote.table) {
        oCsnEntity[CsnAnnotations.DataWarehouse.remote_entity] = table.remote.table;
      }
      if (table.remoteFilter.length > 0) {
        oCsnEntity[CsnAnnotations.DataWarehouse.remote_query] = getQueryFromCSN(table.remoteFilter, table.name);
      }
    }
  }
}

export function isSavingViewAs(entity: sap.cdw.commonmodel.Entity) {
  return (
    isViewOutput(entity) &&
    entity.repositoryCSN &&
    Object.keys(entity.repositoryCSN).length &&
    !entity.repositoryCSN.definitions?.[entity.technicalName]
  );
}
export function isNotReleased(entity: sap.cdw.commonmodel.Entity) {
  return !entity?.releaseState || entity.releaseState === releaseStateValues.NOTRELEASED;
}
export function isReleaseDateRelevant(entity: sap.cdw.commonmodel.Entity): boolean {
  return (
    !!entity.releaseDate &&
    releaseStates.indexOf(entity.releaseState) >= releaseStates.indexOf(releaseStateValues.RELEASED)
  );
}
export function isDeprecationDateRelevant(entity: sap.cdw.commonmodel.Entity): boolean {
  return (
    !!entity.deprecationDate &&
    releaseStates.indexOf(entity.releaseState) >= releaseStates.indexOf(releaseStateValues.DEPRECATED)
  );
}
export function isDecommissioningDateRelevant(entity: sap.cdw.commonmodel.Entity): boolean {
  return !!entity.decommissioningDate && entity.releaseState === releaseStateValues.DECOMMISSIONED;
}
export function isSuccessorObjectRelevant(entity: sap.cdw.commonmodel.Entity): boolean {
  return (
    !!entity.successorObject &&
    releaseStates.indexOf(entity.releaseState) >= releaseStates.indexOf(releaseStateValues.DEPRECATED)
  );
}
export function updateEntityReleaseContractCSN(entity: sap.cdw.commonmodel.Entity, properties: any) {
  if (SupportedFeaturesService.getInstance().isCompatibilityContractsEnabled()) {
    // Reset Release Contract Info
    const releaseContractCSN: any = {};
    if (properties?.releaseStateValue && properties?.releaseStateValue !== "NOT_RELEASED") {
      // Initialize/update releaseContract and releaseState
      if (!releaseContractCSN.releaseContract) {
        releaseContractCSN.releaseContract = { "#": "C1" };
      }
      releaseContractCSN.releaseState = { "#": properties.releaseStateValue };
      // Object Successor
      if (entity.successorObject) {
        if (
          releaseStates.indexOf(properties?.releaseStateValue) >= releaseStates.indexOf(releaseStateValues.DEPRECATED)
        )
          releaseContractCSN.successorObjects = [{ "=": entity.successorObject }];
      }
      // Update dates!
      const dateProperties = ["releaseDate", "deprecationDate", "decommissioningDate"];
      for (const dateProperty of dateProperties) {
        if (properties[dateProperty]) {
          releaseContractCSN[dateProperty] = properties[dateProperty];
        }
      }
    }
    entity.releaseContractCSN = releaseContractCSN;
  }
}
/**
 * Write release contract data
 * @param entity
 * @param csnDocument
 */
export function releaseContractWriteCsn(entity: sap.cdw.commonmodel.Entity, csnDocument: any) {
  if (SupportedFeaturesService.getInstance().isCompatibilityContractsEnabled() && entity && csnDocument) {
    const entityName = entity.technicalName;
    if (isNotReleased(entity) || isSavingViewAs(entity)) {
      // Clear section!
      if (csnDocument?.releaseContractDefinitions) {
        csnDocument.releaseContractDefinitions[entityName] = {};
      }
    } else {
      if (!csnDocument.releaseContractDefinitions) {
        csnDocument.releaseContractDefinitions = {};
      }
      if (!csnDocument.releaseContractDefinitions[entityName]) {
        csnDocument.releaseContractDefinitions[entityName] = {};
      }
      const rcdCSN = csnDocument.releaseContractDefinitions[entityName];
      BaseAnnotation.setCsnEnumValue(rcdCSN, "releaseContract", "C1");
      BaseAnnotation.setCsnEnumValue(rcdCSN, "releaseState", entity.releaseState);
      // Restitute Release Dates
      if (isReleaseDateRelevant(entity)) {
        rcdCSN.releaseDate = entity.releaseDate;
      }
      if (isDeprecationDateRelevant(entity)) {
        rcdCSN.deprecationDate = entity.deprecationDate;
      }
      if (isDecommissioningDateRelevant(entity)) {
        rcdCSN.decommissioningDate = entity.decommissioningDate;
      }
      // Restitute Successor Object
      if (isSuccessorObjectRelevant(entity)) {
        rcdCSN.successorObjects = [];
        BaseAnnotation.addEqualValue(rcdCSN.successorObjects, entity.successorObject);
      }
    }
  }
}

export function releaseContractGetReleaseState(releaseContractDefinition: any) {
  const releaseState = releaseContractDefinition?.releaseState;
  if (releaseState) {
    return BaseAnnotation.enumFromCsnValue(releaseState);
  }
}
export function releaseContractGetReleaseDate(releaseContractDefinition: any) {
  return releaseContractDefinition?.releaseDate;
}
export function releaseContractGetDeprecationDate(releaseContractDefinition: any) {
  return releaseContractDefinition?.deprecationDate;
}
export function releaseContractGetDecommissioningDate(releaseContractDefinition: any) {
  return releaseContractDefinition?.decommissioningDate;
}
export function releaseContractGetSuccessorObject(releaseContractDefinition: any) {
  const successorObjects = releaseContractDefinition?.successorObjects;
  if (successorObjects?.length) {
    return BaseAnnotation.equalFromCsnValue(successorObjects[0]);
  }
  return undefined;
}
/**
 * Read release contract data
 * @param entity
 * @param releaseContractDefinition
 */
export function releaseContractReadCsn(entity: sap.cdw.commonmodel.Entity, releaseContractDefinition: any) {
  if (SupportedFeaturesService.getInstance().isCompatibilityContractsEnabled() && entity && releaseContractDefinition) {
    // Release State
    const releaseState = releaseContractGetReleaseState(releaseContractDefinition);
    if (releaseState) {
      entity.releaseState = releaseState;
      entity.initialState = releaseState;
    }
    // Successor Object(s)
    entity.successorObject = releaseContractGetSuccessorObject(releaseContractDefinition);
  }
}

export function getQueryFromCSN(remoteFilters, tableName) {
  const queryObj = {
    SELECT: {
      columns: [],
      from: { ref: [tableName] },
      where: [],
    },
  };
  let filters = remoteFilters?.toArray();
  if (filters?.length > 0) {
    filters.forEach((filter, index) => {
      const initialIndex = filters?.findIndex((item) => item.name === filter.name);
      const hasDuplicate = initialIndex !== index;
      const obj = filters[initialIndex];
      if (hasDuplicate) {
        // get last column to ensure ")" brackets if needed
        const lastIndex = getLastIndexOf(filters, filter.name);
        if (lastIndex !== -1) {
          filters[lastIndex].lastColumn = true;
        }
        // set first column to ensure "(" brackets if needed
        obj.hasMoreConditions = true;
      } else {
        obj.hasMoreConditions = false;
      }
    });
    // Sort the array
    filters = getSortedArray(filters);
    let previousFilterColumn;
    const oCsnWhereClause = queryObj.SELECT.where;
    filters.forEach((filterItem) => {
      if (oCsnWhereClause.length > 0) {
        if (previousFilterColumn !== filterItem.name) {
          oCsnWhereClause.push("and");
        } else {
          oCsnWhereClause.push("or");
        }
      }
      if (filterItem.hasMoreConditions) {
        oCsnWhereClause.push("(");
        filterItem.hasBrackets = true;
      } else {
        filterItem.hasBrackets = false;
      }
      oCsnWhereClause.push({ ref: [filterItem.name] });
      if (filterItem.operation === "EQ") {
        oCsnWhereClause.push(OperatorsList[filterItem.operation]);
        oCsnWhereClause.push({ val: filterItem.value1 });
      } else if (filterItem.operation === "BT") {
        oCsnWhereClause.push(OperatorsList[filterItem.operation + "LOW"]);
        oCsnWhereClause.push({ val: filterItem.value1 });
        oCsnWhereClause.push("and");
        oCsnWhereClause.push({ ref: [filterItem.name] });
        oCsnWhereClause.push(OperatorsList[filterItem.operation + "HIGH"]);
        oCsnWhereClause.push({ val: filterItem.value2 });
      }
      if (filterItem?.lastColumn) {
        const initialFind = filters[filters?.findIndex((item) => item?.name === filterItem?.name)];
        if (initialFind?.hasBrackets) {
          oCsnWhereClause.push(")");
        }
      }
      previousFilterColumn = filterItem.name;
    });
  }
  return queryObj;
}

export function getLastIndexOf(arr, itemToFind) {
  for (let i = arr.length - 1; i >= 0; i--) {
    if (arr[i].name === itemToFind) {
      return i;
    }
  }
  return -1;
}

export function getSortedArray(arr) {
  if (arr.length > 0) {
    arr.sort((a, b) => {
      if (a.name < b.name) {
        return -1;
      }
      if (a.name > b.name) {
        return 1;
      }
      return 0;
    });
  }
  return arr;
}

/**
 * Read remote table properties <- @DataWarehouse.remote.connection, @DataWarehouse.remote.entity
 * @param entity
 * @param oCsnEntity
 */
export function remoteTableReadCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any) {
  if (entity && oCsnEntity) {
    const table = entity as sap.cdw.commonmodel.Table;
    if (
      oCsnEntity[CsnAnnotations.DataWarehouse.remote_connection] &&
      oCsnEntity[CsnAnnotations.DataWarehouse.remote_entity]
    ) {
      // Remote table
      table.remote = {
        connection: oCsnEntity[CsnAnnotations.DataWarehouse.remote_connection],
        table: oCsnEntity[CsnAnnotations.DataWarehouse.remote_entity],
      };
    }
    if (oCsnEntity[CsnAnnotations.DataWarehouse.remote_query]) {
      const value = oCsnEntity[CsnAnnotations.DataWarehouse.remote_query];
      const whereClause = value?.SELECT?.where;
      parseRemoteFilter(whereClause, table);
      if (entity.unhandledCsn) {
        BaseAnnotation.deleteHandledAnnotation(entity.unhandledCsn, CsnAnnotations.DataWarehouse.remote_query);
      }
    }
    if (entity.unhandledCsn) {
      BaseAnnotation.deleteHandledAnnotations(entity.unhandledCsn, [
        CsnAnnotations.DataWarehouse.remote_connection,
        CsnAnnotations.DataWarehouse.remote_entity,
      ]);
    }
  }
}
export function remoteTableGetAnnotations() {
  return [CsnAnnotations.DataWarehouse.remote_connection, CsnAnnotations.DataWarehouse.remote_entity];
}

export function parseRemoteFilter(whereClause, table) {
  const oWhere = whereClause;
  let filterColumn;
  let operation,
    value1,
    value2,
    item,
    isCreated,
    condition = 0,
    canCreate = false;
  for (let i = 0; i < oWhere.length; i++) {
    const entry = oWhere[i];
    if (entry === "or" || entry === "and" || entry === "(" || entry === ")") {
      continue;
    }
    if (entry.ref) {
      filterColumn = entry.ref[0];
      continue;
    }
    if (entry === "=") {
      operation = "EQ";
      continue;
    } else if (entry === ">=" || entry === "<=") {
      operation = "BT";
    }
    if (entry.val !== undefined) {
      if (operation === "EQ") {
        value1 = entry.val;
      } else if (operation === "BT") {
        if (value1 === undefined) {
          value1 = entry.val;
        } else if (value2 === undefined) {
          value2 = entry.val;
        }
      }
      if (operation === "EQ" && value1 !== undefined && filterColumn) {
        canCreate = true;
      } else if (operation === "BT" && value1 !== undefined && value2 !== undefined && filterColumn) {
        canCreate = true;
      }
      if (canCreate) {
        condition = ++condition;
        item = getRemoteFilterItemNew(
          {
            filterColumn: filterColumn,
            operation: operation,
            value1: value1,
            value2: value2,
          },
          table
        );
        table.remoteFilter.push(item);
        isCreated = true;
        canCreate = false;
      }
      if (isCreated) {
        // reset the values
        filterColumn = undefined;
        operation = undefined;
        value1 = undefined;
        value2 = undefined;
        isCreated = false;
      }
    }
  }
}

export function getRemoteFilterItemNew(oParams, entity) {
  const oClass = sap.galilei.model.getClass("sap.cdw.commonmodel.RemoteTableFilterItemNew");
  let oRemoteTableFilter;
  if (oClass) {
    const column = entity?.orderedElements?.filter((o) => o.name === oParams.filterColumn)?.[0];
    oRemoteTableFilter = oClass.create(entity.resource, {
      name: oParams.filterColumn,
      operation: oParams.operation,
      possibleFilterOperations: column.allowedFilterOperationsRef,
      value1: oParams.value1,
      value2: oParams.value2,
      dataType: column.dataType,
      length: column.length,
      precision: column.precision,
      scale: column.scale,
    });
  }
  return oRemoteTableFilter;
}

/**
 * Write local schema table properties -> @DataWarehouse.external.schema, @DataWarehouse.external.entity
 * @param entity
 * @param oCsnEntity
 */
export function localSchemaTableWriteCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any) {
  if (entity && entity.isTable && oCsnEntity) {
    const table = entity as sap.cdw.commonmodel.Table;
    if (table.localSchema) {
      if (table.localSchema.schema) {
        oCsnEntity[CsnAnnotations.DataWarehouse.external_schema] = table.localSchema.schema;
      }
      if (table.localSchema.table) {
        oCsnEntity[CsnAnnotations.DataWarehouse.external_entity] = table.localSchema.table;
      }
    }
  }
}

/**
 * Read local schema table properties <- @DataWarehouse.external.schema, @DataWarehouse.external.entity
 * @param entity
 * @param oCsnEntity
 */
export function contentOwnerReadCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any) {
  if (entity && entity.isTable && oCsnEntity) {
    const table = entity as sap.cdw.commonmodel.Table;
    if (oCsnEntity[CsnAnnotations.DataWarehouse.contentOwner]) {
      table.contentOwner = oCsnEntity[CsnAnnotations.DataWarehouse.contentOwner];
      if (entity.unhandledCsn) {
        BaseAnnotation.deleteHandledAnnotations(entity.unhandledCsn, [CsnAnnotations.DataWarehouse.contentOwner]);
      }
    }
  }
}
export function contentOwnerWriteCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any) {
  const table = entity as sap.cdw.commonmodel.Table;

  if (table.contentOwner !== undefined) {
    oCsnEntity[CsnAnnotations.DataWarehouse.contentOwner] = table.contentOwner;
  } else {
    if (oCsnEntity[CsnAnnotations.DataWarehouse.contentOwner]) {
      delete oCsnEntity[CsnAnnotations.DataWarehouse.contentOwner];
    }
  }
}

/**
 * Read local schema table properties <- @DataWarehouse.external.schema, @DataWarehouse.external.entity
 * @param entity
 * @param oCsnEntity
 */
export function localSchemaTableReadCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any) {
  if (entity && entity.isTable && oCsnEntity) {
    const table = entity as sap.cdw.commonmodel.Table;
    if (oCsnEntity[CsnAnnotations.DataWarehouse.external_schema]) {
      // Remote table
      table.localSchema = {
        schema: oCsnEntity[CsnAnnotations.DataWarehouse.external_schema],
        table: oCsnEntity[CsnAnnotations.DataWarehouse.external_entity],
      };
      if (entity.unhandledCsn) {
        BaseAnnotation.deleteHandledAnnotations(entity.unhandledCsn, [
          CsnAnnotations.DataWarehouse.external_schema,
          CsnAnnotations.DataWarehouse.external_entity,
        ]);
      }
    }
  }
}

export function deltaTableReadCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any) {
  if (oCsnEntity) {
    const table = entity as sap.cdw.commonmodel.Table;
    const deltaInfo = oCsnEntity[CsnAnnotations.DataWarehouse.delta];
    let ltfInfo;
    if (oCsnEntity[CsnAnnotations.DataWarehouse.persistence_hdlf_tableFormat]) {
      ltfInfo = oCsnEntity[CsnAnnotations.DataWarehouse.persistence_hdlf_tableFormat];
      table.ltfTable = ltfInfo?.["#"];
    }

    if (deltaInfo) {
      // Remote table
      table.deltaTable = {
        type: deltaInfo.type?.["#"],
        mode: deltaInfo.modeElement?.["="],
        timestamp: deltaInfo.dateTimeElement?.["="],
      };
    }
    if (table?.deltaTable?.mode) {
      const index = table.elements?.toArray().findIndex((el) => el.name === table.deltaTable.mode);
      if (index !== -1) {
        (table.elements?.get(index) as any).isCDCColumn = true;
      }
    }
    if (table?.deltaTable?.timestamp) {
      const index = table.elements?.toArray().findIndex((el) => el.name === table.deltaTable.timestamp);
      if (index !== -1) {
        (table.elements?.get(index) as any).isCDCColumn = true;
      }
    }
  }
}

export function deltaTableWriteCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any) {
  if (entity?.deltaTable) {
    const table = entity as sap.cdw.commonmodel.Table;

    const cdcCols = entity.elements?.filter((el) => el.isCDCColumn === true);
    let modeElement, timeStampElement;
    cdcCols.forEach((element) => {
      if (element?.dataType === CDSDataType.STRING) {
        modeElement = element;
      } else if (element?.dataType === CDSDataType.TIMESTAMP) {
        timeStampElement = element;
      }
    });
    if (table.deltaTable?.type && modeElement && timeStampElement) {
      oCsnEntity[CsnAnnotations.DataWarehouse.delta] = {
        type: { "#": DELTATYPES.UPSERT },
        dateTimeElement: { "=": timeStampElement.name },
        modeElement: { "=": modeElement.name },
      };
    }
    if (table.deltaTable?.type && table.deltaTableName) {
      oCsnEntity[CsnAnnotations.DataWarehouse.deltaEnclosing] = entity.name;
    }
    if (table?.fileStorage && table.deltaTable?.type) {
      oCsnEntity[CsnAnnotations.DataWarehouse.persistence_hdlf_tableFormat] = {
        "#": DELTATYPES.Lake,
      };
      oCsnEntity[CsnAnnotations.cds.persistence_udf] = true;
      oCsnEntity[CsnAnnotations.ltfAnnotations.interval] = [];
      const semanticObject = {
        qualifier: "changeTime",
        lowerBoundaryParameter: { "=": CsnAnnotations.ltfAnnotations.fromChangeTime },
        lowerBoundaryIncluded: false,
        upperBoundaryParameter: { "=": CsnAnnotations.ltfAnnotations.toChangeTime },
        upperBoundaryIncluded: true,
      };
      oCsnEntity[CsnAnnotations.ltfAnnotations.interval].push(semanticObject);
    }
  } else {
    if (oCsnEntity[CsnAnnotations.DataWarehouse.delta]) {
      delete oCsnEntity[CsnAnnotations.DataWarehouse.delta];
    }
    if (oCsnEntity[CsnAnnotations.DataWarehouse.deltaEnclosing]) {
      delete oCsnEntity[CsnAnnotations.DataWarehouse.deltaEnclosing];
    }
  }
}

export function partitionsReadCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any, oCsnExtensions?: any) {
  if (entity?.isTable && oCsnEntity && entity.isLocal) {
    const table = entity as sap.cdw.commonmodel.Table;
    const extensibleProperties = oCsnEntity["#extensibleProperties"];
    const allowExtensions =
      Array.isArray(extensibleProperties) && extensibleProperties.includes("@DataWarehouse.partition");

    let partitionInfo = oCsnEntity[CsnAnnotations.DataWarehouse.partition];
    let isFromExtension = false;
    let actualPartitionInfo = partitionInfo;

    if (allowExtensions && oCsnExtensions && Array.isArray(oCsnExtensions)) {
      const partitionExtension = oCsnExtensions.find(
        (ext) => ext.annotate === entity.name && ext[CsnAnnotations.DataWarehouse.partition]
      );
      if (partitionExtension) {
        partitionInfo = partitionExtension[CsnAnnotations.DataWarehouse.partition];
        isFromExtension = true;
      }
    }

    (table as any).allowExtensions = allowExtensions;
    (table as any).partitionsFromExtension = isFromExtension;
    (table as any).actualPartitionInfo = actualPartitionInfo;

    if (oCsnEntity[CsnAnnotations.DataWarehouse.persistence_hdlf_tableFormat]) {
      if (partitionInfo) {
        partitionInfo?.elements?.forEach((element) => {
          const oClass = sap.galilei.model.getClass("sap.cdw.commonmodel.Partitions");
          const oPartitions = oClass.create(entity.resource, { id: element["="] });
          table.partitions.push(oPartitions);
        });
      }
    } else {
      if (partitionInfo) {
        const type = partitionInfo.by?.["#"];
        if (type === PARTITIONTYPES.RANGE) {
          const oParams = {
            type: partitionInfo.by?.["#"],
            id: partitionInfo.elements?.[0]?.["="],
            ranges: partitionInfo.ranges,
          };
          const partition = getPartitionsFromCSN(oParams, entity);
          table.partitions.push(partition);
        } else if (SupportedFeaturesService.getInstance().isHashPartitioningEnabled() && type === PARTITIONTYPES.HASH) {
          const oClass = sap.galilei.model.getClass("sap.cdw.commonmodel.Partitions");
          const oHashPartition = oClass.create(entity.resource, {
            partitionType: type,
            noOfHashPartitions: partitionInfo.numberOfPartitions,
          });
          const hashElementNames = partitionInfo.elements.map((element) => element["="]);
          hashElementNames.forEach((name) => {
            const element = findInCollectionByName(entity.elements, name);
            if (element !== undefined) {
              oHashPartition.hashedColumns.push(element);
            }
          });
          table.partitions.push(oHashPartition);
        }
      }
    }
  }
}

export function getPartitionsFromCSN(oParams, entity) {
  // Create partition class
  const element = getColumnDetails(entity, oParams.id);
  const oClass = sap.galilei.model.getClass("sap.cdw.commonmodel.Partitions");
  const oPartitions = oClass.create(entity.resource, {
    id: oParams.id,
    partitionDatatype: element.primitiveDataType,
    partitionType: oParams.type,
  });

  // Create Ranges collection
  const oRangeClass = sap.galilei.model.getClass("sap.cdw.commonmodel.PartitionRanges");
  oParams.ranges.forEach((range, index) => {
    const rangeDef = oRangeClass.create(oPartitions.resource, {
      partitionId: index + 1,
      low: range?.low,
      high: range?.high,
    });
    // Add ranges to partitions
    oPartitions.ranges.push(rangeDef);
  });
  return oPartitions;
}

export function getColumnDetails(entity, columnName) {
  return entity?.orderedElements?.filter((elem) => elem.name === columnName)?.[0];
}

export function partitionsWriteCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any, oCsnExtensions?: any) {
  const extensibleProperties = oCsnEntity["#extensibleProperties"];
  const allowExtensions =
    Array.isArray(extensibleProperties) && extensibleProperties.includes("@DataWarehouse.partition");
  const writeToExtensions = allowExtensions && oCsnExtensions;

  if (entity?.partitions) {
    if (entity?.fileStorage && entity?.partitions?.length > 0) {
      const partitionedColumns = [];
      entity.partitions?.forEach((partition) => {
        partitionedColumns.push({ "=": (partition as { id: string }).id });
      });

      if (writeToExtensions) {
        oCsnExtensions.push({
          annotate: entity.name,
          [CsnAnnotations.DataWarehouse.partition]: {
            by: { "#": PARTITIONTYPES.COLUMNS },
            elements: partitionedColumns,
          },
        });
      } else {
        oCsnEntity[CsnAnnotations.DataWarehouse.partition] = {
          by: { "#": PARTITIONTYPES.COLUMNS },
          elements: partitionedColumns,
        };
      }
    } else {
      const partition = entity.partitions?.get(0);
      const partitionType = partition?.partitionType;
      if (partition) {
        let partitionData;
        if (partitionType === PARTITIONTYPES.RANGE) {
          partitionData = {
            by: { "#": PARTITIONTYPES.RANGE },
            elements: [{ "=": partition.id }],
            ranges: [],
            others: {},
          };
          partition.ranges.forEach((range) => {
            partitionData.ranges.push({ low: range.low, high: range.high });
          });
        } else if (partitionType === PARTITIONTYPES.HASH) {
          partitionData = {
            by: { "#": PARTITIONTYPES.HASH },
            elements: [],
            numberOfPartitions: parseInt(partition.noOfHashPartitions, 10),
          };
          partition.hashedColumns.forEach((hashCol) => {
            partitionData.elements.push({ "=": hashCol.name });
          });
        }

        if (writeToExtensions) {
          oCsnExtensions.push({ annotate: entity.name, [CsnAnnotations.DataWarehouse.partition]: partitionData });
        } else {
          oCsnEntity[CsnAnnotations.DataWarehouse.partition] = partitionData;
        }
      } else {
        if (writeToExtensions) {
          const extensionIndex = oCsnExtensions.findIndex(
            (ext) => ext.annotate === entity.name && ext[CsnAnnotations.DataWarehouse.partition]
          );
          if (extensionIndex !== -1) {
            oCsnExtensions.splice(extensionIndex, 1);
          }
        } else {
          if (oCsnEntity[CsnAnnotations.DataWarehouse.partition]) {
            delete oCsnEntity[CsnAnnotations.DataWarehouse.partition];
          }
        }
      }
    }
  }
}

export function localSchemaGetAnnotations() {
  return [CsnAnnotations.DataWarehouse.external_schema, CsnAnnotations.DataWarehouse.external_entity];
}

export function deltaTableGetAnnotations() {
  return [CsnAnnotations.DataWarehouse.delta];
}

export function contentOwnerGetAnnotations() {
  return [CsnAnnotations.DataWarehouse.contentOwner];
}

export function partitionsGetAnnotations() {
  return [CsnAnnotations.DataWarehouse.partition];
}

/**
 * Write data access control properties <-> @DataWarehouse.dataAccessControl.definition {...}.
 * @param entity
 * @param oCsnEntity
 */
export function dataAccessControlDefinitionWriteCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any) {
  if (entity && entity.isDataAccessControl && oCsnEntity) {
    const dataAccessControl = entity as sap.cdw.commonmodel.DataAccessControl;
    oCsnEntity[CsnAnnotations.DataWarehouse.dataAccessControl_definition] = {
      sourceEntity: dataAccessControl.sourceEntityName,
      principalElement: dataAccessControl.principalElementName,
      errorText: dataAccessControl.errorText,
    };
  }
}

/**
 * Read data access control properties <-> @DataWarehouse.dataAccessControl.definition {...}.
 * @param entity
 * @param oCsnEntity
 */
export function dataAccessControlDefinitionReadCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any) {
  if (entity && entity.isDataAccessControl && oCsnEntity) {
    const dataAccessControl = entity as sap.cdw.commonmodel.DataAccessControl;
    const dataAccessControlDefinition = oCsnEntity[CsnAnnotations.DataWarehouse.dataAccessControl_definition];
    if (dataAccessControlDefinition) {
      // Set text members
      // TODO: handle object members for source entity and principal element?
      dataAccessControl.errorText = dataAccessControlDefinition.errorText;
      dataAccessControl.sourceEntityName = dataAccessControlDefinition.sourceEntity;
      dataAccessControl.principalElementName = dataAccessControlDefinition.principalElement;
      if (entity.unhandledCsn) {
        BaseAnnotation.deleteHandledAnnotation(
          entity.unhandledCsn,
          CsnAnnotations.DataWarehouse.dataAccessControl_definition
        );
      }
    }
  }
}
export function dataAccessControlDefinitionGetAnnotations() {
  return [CsnAnnotations.DataWarehouse.dataAccessControl_definition];
}

/**
 * Write cross space entity properties -> @DataWarehouse.space.schema, @DataWarehouse.space.name
 * @param object Context or entity
 * @param oCsn
 */
export function crossSpaceContextWriteCsn(object: sap.cdw.commonmodel.Context, oCsn: any) {
  if (object && oCsn) {
    if (object.crossSpace) {
      if (object.crossSpace.schema) {
        oCsn[CsnAnnotations.DataWarehouse.space_schema] = object.crossSpace.schema;
      }
      if (object.crossSpace.space) {
        oCsn[CsnAnnotations.DataWarehouse.space_name] = object.crossSpace.space;
      }
    }
  }
}

/**
 * Read cross space entity properties <- @DataWarehouse.space.schema, @DataWarehouse.space.name
 * @param object Context or entity
 * @param oCsn
 */
export function crossSpaceContextReadCsn(object: sap.cdw.commonmodel.Context, oCsn: any) {
  if (object && oCsn) {
    if (oCsn[CsnAnnotations.DataWarehouse.space_schema]) {
      // Cross space entity
      object.crossSpace = {
        schema: oCsn[CsnAnnotations.DataWarehouse.space_schema],
        space: oCsn[CsnAnnotations.DataWarehouse.space_name],
      };
      if (object.unhandledCsn) {
        BaseAnnotation.deleteHandledAnnotations(object.unhandledCsn, [
          CsnAnnotations.DataWarehouse.space_name,
          CsnAnnotations.DataWarehouse.space_schema,
        ]);
      }
    }
  }
}
export function crossSpaceContextGetAnnotations() {
  return [CsnAnnotations.DataWarehouse.space_name, CsnAnnotations.DataWarehouse.space_schema];
}

/**
 * Write isAllowConsumption -> @Analytics.provider
 * @param entity
 * @param oCsnEntity
 */
export function allowConsumptionWriteCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any) {
  if (entity && oCsnEntity) {
    AllowConsumptionAnnotation.writeCsnValue(
      oCsnEntity,
      entity.isAllowConsumption,
      entity.dataCategory as DataCategory
    );
  }
}

/**
 * Read isAllowConsumption <- @Analytics.provider || @ObjectModel.supportedCapabilities
 * @param entity
 * @param oCsnEntity
 */
export function allowConsumptionReadCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any) {
  if (entity && oCsnEntity) {
    entity.isAllowConsumption = AllowConsumptionAnnotation.getAllowConsumption(oCsnEntity);
    if (entity.unhandledCsn) {
      AllowConsumptionAnnotation.deleteHandledAnnotation(entity.unhandledCsn);
    }
  }
}
export function allowConsumptionGetAnnotations() {
  return AllowConsumptionAnnotation.getAnnotations();
}

/**
 * Write isUseOLAPDBHint -> @Consumption.dbHints
 * @param entity
 * @param oCsnEntity
 */
export function useOLAPDBHintWriteCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any) {
  if (entity && oCsnEntity && entity.isUseOLAPDBHint && entity.isAllowConsumption) {
    if (oCsnEntity[CsnAnnotations.Consumption.db_hints] !== undefined) {
      // we should avoid push Consumption_DB_Hints.USE_OLAP_PLAN multiple times
      const dbHintsArray = oCsnEntity[CsnAnnotations.Consumption.db_hints];
      if (Array.isArray(dbHintsArray) && !dbHintsArray.includes(Consumption_DB_Hints.USE_OLAP_PLAN)) {
        oCsnEntity[CsnAnnotations.Consumption.db_hints].push(Consumption_DB_Hints.USE_OLAP_PLAN);
      }
    } else {
      oCsnEntity[CsnAnnotations.Consumption.db_hints] = [Consumption_DB_Hints.USE_OLAP_PLAN];
    }
  }
}

/**
 * Read isUseOLAPDBHint <- @Consumption.dbHints
 * @param entity
 * @param oCsnEntity
 */
export function useOLAPDBHintReadCsn(entity: sap.cdw.commonmodel.Entity, oCsnEntity: any) {
  if (entity && oCsnEntity) {
    if (oCsnEntity[CsnAnnotations.Consumption.db_hints] !== undefined) {
      entity.isUseOLAPDBHint = oCsnEntity[CsnAnnotations.Consumption.db_hints].includes(
        Consumption_DB_Hints.USE_OLAP_PLAN
      );
      if (entity.unhandledCsn) {
        BaseAnnotation.deleteHandledAnnotationArrayEntry(
          entity.unhandledCsn,
          CsnAnnotations.Consumption.db_hints,
          Consumption_DB_Hints.USE_OLAP_PLAN
        );
      }
    } else {
      entity.isUseOLAPDBHint = false;
    }
  }
}
export function useOLAPDBHintGetAnnotations() {
  return [CsnAnnotations.Consumption.db_hints];
}

/**
 * Write dataType -> type
 * @param elementOrType
 * @param oCsnElementOrType
 */
export function dataTypeWriteCsn(
  elementOrType: sap.cdw.commonmodel.Element | sap.cdw.commonmodel.SimpleType,
  oCsnElementOrType: any
) {
  if (elementOrType && oCsnElementOrType && elementOrType.dataType) {
    dataTypePropertiesWriteCsn(elementOrType, oCsnElementOrType);
  }
}

/**
 * Generates dataType properties length, precision, scale, srid.
 * @param oCsnElementOrType
 * @param elementOrType
 */
export function dataTypePropertiesWriteCsn(
  elementOrType: sap.cdw.commonmodel.Element | sap.cdw.commonmodel.SimpleType,
  oCsnElementOrType: any
) {
  if (elementOrType && oCsnElementOrType) {
    // Check length
    let length = ensureNumber(elementOrType.length);

    // Check dataType
    if (DecimalDataTypes.includes(elementOrType.primitiveDataType)) {
      // For cds.Decimal, precision and scale must be defined.
      length = undefined;
      let precision = ensureNumber(elementOrType.precision);
      let scale = ensureNumber(elementOrType.scale);
      if (isNaN(precision)) {
        precision = 8;
      }
      if (isNaN(scale)) {
        scale = 0;
      }
      oCsnElementOrType.precision = precision;
      oCsnElementOrType.scale = scale;
    } else if (elementOrType.primitiveDataType === CDSDataType.BINARY) {
      // For cds.Binary, use maximum length as default length
      if (!length) {
        length = CDS_BINARY_MAX_LENGTH;
      }
    } else if (
      elementOrType.primitiveDataType === CDSDataType.HANA_ST_POINT ||
      elementOrType.primitiveDataType === CDSDataType.HANA_ST_GEOMETRY ||
      elementOrType.primitiveDataType === ABAPType.GEOM_EWKB
    ) {
      let srid = ensureNumber(elementOrType.srid);
      if (isNaN(srid)) {
        srid = DEFAULT_SRID;
      }
      oCsnElementOrType.srid = srid;
    } else if (
      elementOrType.primitiveDataType === CDSDataType.INTEGER ||
      elementOrType.primitiveDataType === CDSDataType.INTEGER64
    ) {
      // introduced to fix Jira#DW101-231
      length = undefined;
    }

    if (elementOrType.dataType) {
      oCsnElementOrType.type = elementOrType.dataType;
    }
    if (length !== undefined && length > 0) {
      oCsnElementOrType.length = length;
    }
  }
}

/**
 * Read dataType <- type
 * @param elementOrType
 * @param oCsnElementOrType
 */
export function dataTypeReadCsn(
  elementOrType: sap.cdw.commonmodel.Element | sap.cdw.commonmodel.SimpleType,
  oCsnElementOrType: any
) {
  if (elementOrType && oCsnElementOrType) {
    // Replace unsupported HANA native types
    const dataType = oCsnElementOrType.type;
    if (dataType) {
      elementOrType.dataType = converUnsupportedDataType(dataType);
    }

    // Set length, precision, scale and srid
    if (!isNaN(oCsnElementOrType.precision)) {
      elementOrType.precision = oCsnElementOrType.precision;
    }
    if (!isNaN(oCsnElementOrType.scale)) {
      elementOrType.scale = oCsnElementOrType.scale;
    }
    if (!isNaN(oCsnElementOrType.length)) {
      elementOrType.length = oCsnElementOrType.length;
    }
    if (!isNaN(oCsnElementOrType.srid)) {
      elementOrType.srid = oCsnElementOrType.srid;
    }

    // Auto-fix dataType
    autoFixAbapDataType(elementOrType);
    switch (elementOrType.dataType) {
      case CDSDataType.STRING:
        if (!elementOrType.length) {
          elementOrType.length = CDSDataTypePropertyValues[CDSDataType.STRING].LENGTH.MAX;
        }
        break;
    }
    if (elementOrType.unhandledCsn) {
      BaseAnnotation.deleteHandledAnnotations(elementOrType.unhandledCsn, [
        "type",
        "precision",
        "scale",
        "length",
        "srid",
      ]);
    }
  }
}

export function autoFixAbapDataType(elementOrType: sap.cdw.commonmodel.Element | sap.cdw.commonmodel.SimpleType) {
  if (elementOrType.dataType?.startsWith("abap.")) {
    const definition = ABAPDataType[elementOrType.dataType];
    if (definition?.precision && !elementOrType.precision) {
      elementOrType.precision = definition.precision;
    } else if (definition?.length && !elementOrType.length) {
      elementOrType.length = definition.length;
    }
  }
}
export function dataTypeGetAnnotations() {
  return ["type", "precision", "scale", "length", "srid", "dataType", "ref"];
}

/**
 * Write default <->
 * @param element
 * @param oCsnElement
 */
export function defaultValueWriteCsn(element: sap.cdw.commonmodel.Element, oCsnElement: any) {
  if (element && oCsnElement && element.default === "") {
    delete oCsnElement?.default;
  } else if (element && oCsnElement && element.default !== undefined) {
    switch (element.primitiveDataType) {
      case CDSDataType.STRING:
      case CDSDataType.HANA_NCHAR:
        oCsnElement.default = {
          val: element.default,
        };
        break;
      case CDSDataType.INTEGER:
      case CDSDataType.INTEGER64:
      case CDSDataType.HANA_SMALLINT:
      case CDSDataType.HANA_TINYINT:
        const negativeInt = parseInt(element.default);
        if (Math.sign(negativeInt) === -1) {
          oCsnElement.default = {
            xpr: [
              "-",
              {
                val: Math.abs(negativeInt),
              },
            ],
          };
        } else if (isNaN(oCsnElement.default)) {
          oCsnElement.default = {
            val: element.default,
          };
        } else {
          oCsnElement.default = {
            val: parseInt(element.default),
          };
        }
        break;
      case CDSDataType.DECIMAL:
      case CDSDataType.DECIMAL_FLOAT:
      case CDSDataType.DOUBLE:
      case CDSDataType.HANA_SMALLDECIMAL:
      case CDSDataType.HANA_REAL:
        const negativeDec = parseFloat(element.default);
        if (Math.sign(negativeDec) === -1) {
          const unsignedVal = Math.abs(element.default);
          oCsnElement.default = {
            xpr: [
              "-",
              {
                val: unsignedVal.toString(),
                literal: "number",
              },
            ],
          };
        } else {
          oCsnElement.default = {
            val: element.default,
            literal: "number",
          };
        }
        break;
      case CDSDataType.DATE:
        if (element.default === "CURRENT_DATE") {
          oCsnElement.default = {
            func: "CURRENT_DATE",
          };
        } else if (element.default === "CURRENT_UTCDATE") {
          oCsnElement.default = {
            func: "CURRENT_UTCDATE",
          };
        } else {
          oCsnElement.default = {
            val: element.default,
            literal: "date",
          };
        }
        break;
      case CDSDataType.TIME:
        if (element.default === "CURRENT_TIME") {
          oCsnElement.default = {
            func: "CURRENT_TIME",
          };
        } else if (element.default === "CURRENT_UTCTIME") {
          oCsnElement.default = {
            func: "CURRENT_UTCTIME",
          };
        } else {
          oCsnElement.default = {
            val: element.default,
            literal: "time",
          };
        }
        break;
      case CDSDataType.DATETIME:
      case CDSDataType.TIMESTAMP:
        if (element.default === "CURRENT_TIMESTAMP") {
          oCsnElement.default = {
            func: "CURRENT_TIMESTAMP",
          };
        } else if (element.default === "CURRENT_UTCTIMESTAMP") {
          oCsnElement.default = {
            func: "CURRENT_UTCTIMESTAMP",
          };
        } else {
          oCsnElement.default = {
            val: element.default,
            literal: "timestamp",
          };
        }
        break;
      case CDSDataType.BOOLEAN:
        if (element.default === true || element.default === "true") {
          oCsnElement.default = {
            val: true,
          };
        } else if (element.default === false || element.default === "false") {
          oCsnElement.default = {
            val: false,
          };
        }
        break;
      default:
        oCsnElement.default = {
          val: element.default,
        };
        break;
    }
  }
}

/**
 * Read default <->
 * @param element
 * @param oCsnElement
 */
export function defaultValueReadCsn(element: sap.cdw.commonmodel.Element, oCsnElement: any) {
  if (element && oCsnElement && oCsnElement.default === "") {
    element.default = undefined;
  } else if (element && oCsnElement && oCsnElement.default !== undefined) {
    const defaultValue = oCsnElement.default;
    switch (element.primitiveDataType) {
      case CDSDataType.STRING:
      case CDSDataType.LARGE_STRING:
      case CDSDataType.HANA_NCHAR:
        element.default = defaultValue.val;
        break;
      case CDSDataType.INTEGER:
      case CDSDataType.INTEGER64:
      case CDSDataType.HANA_SMALLINT:
      case CDSDataType.HANA_TINYINT:
      case CDSDataType.DECIMAL:
      case CDSDataType.DECIMAL_FLOAT:
      case CDSDataType.DOUBLE:
      case CDSDataType.HANA_REAL:
      case CDSDataType.HANA_SMALLDECIMAL:
        if (defaultValue.xpr !== undefined) {
          element.default = -defaultValue.xpr[1].val;
        } else {
          element.default = defaultValue.val;
        }
        break;
      case CDSDataType.DATE:
      case CDSDataType.TIME:
      case CDSDataType.DATETIME:
      case CDSDataType.TIMESTAMP:
        if (defaultValue && defaultValue.func) {
          element.default = defaultValue.func;
        } else {
          element.default = defaultValue.val;
        }
        break;
      case CDSDataType.BOOLEAN:
        if (defaultValue && defaultValue.val === true) {
          element.default = true;
        } else if (defaultValue && defaultValue.val === false) {
          element.default = false;
        }
      default:
        if (defaultValue && defaultValue.func) {
          element.default = defaultValue.func;
        } else if (defaultValue && defaultValue.xpr) {
          element.default = -defaultValue.xpr[1].val;
        } else {
          element.default = defaultValue.val;
        }
        break;
    }
    if (element.unhandledCsn) {
      BaseAnnotation.deleteHandledAnnotation(element.unhandledCsn, "default");
    }
  }
}
export function defaultGetAnnotations() {
  return ["default"];
}

/**
 * Read @Semantics.language: true <- @Common.IsLanguageIdentifier===true
 * @param element
 * @param oCsnElement
 */
export function IsLanguageColumnReadCsn(element: sap.cdw.commonmodel.Element, oCsnElement: any) {
  if (element && oCsnElement && oCsnElement[CsnAnnotations.Common.IsLanguageIdentifier] === true) {
    element.semanticType = SemanticType.LANGUAGE;
  }
  if (element.unhandledCsn) {
    BaseAnnotation.deleteHandledAnnotation(element.unhandledCsn, CsnAnnotations.Common.IsLanguageIdentifier);
  }
}
export function isLanguageColumnGetAnnotations() {
  return [CsnAnnotations.Common.IsLanguageIdentifier];
}

/**
 * Write nothing because its readCsn is redirected to semanticType
 * @param element
 * @param oCsnElement
 */
export function IsLanguageColumnWriteCsn(element: sap.cdw.commonmodel.Element, oCsnElement: any) {
  //
}

/**
 * Write semanticType -> @Semantics.xxx
 * @param element
 * @param oCsnElement
 */
export function semanticTypeWriteCsn(element: sap.cdw.commonmodel.Element, oCsnElement: any) {
  if (element && oCsnElement) {
    if (element.semanticType) {
      if (element.unitTypeElement) {
        oCsnElement[element.semanticType] = {
          "=": element.unitTypeElement.newName || element.unitTypeElement.name,
        };
      } else if (element.semanticType !== SemanticType.QUANTITY_WITH_UNIT) {
        oCsnElement[element.semanticType] = true;
      }
    }
  }
}

/**
 * Read semanticType <- @Semantics.xxx
 * @param element
 * @param oCsnElement
 */
export function semanticTypeReadCsn(element: sap.cdw.commonmodel.Element, oCsnElement: any) {
  let postProcessingUnitTypeElement: any;
  if (element && oCsnElement) {
    for (const sCsnPropName in oCsnElement) {
      if (sCsnPropName.startsWith("@Semantics.")) {
        const csnValue = oCsnElement[sCsnPropName];
        if (csnValue !== undefined && csnValue !== "") {
          element.semanticType = sCsnPropName;
          if (csnValue && csnValue["="]) {
            postProcessingUnitTypeElement = {
              unitTypeElement: csnValue["="],
            };
          }
          if (element.unhandledCsn) {
            BaseAnnotation.deleteHandledAnnotation(element.unhandledCsn, sCsnPropName);
          }
        }
      }
    }
  }
  return postProcessingUnitTypeElement;
}
export function semanticTypeGetAnnotations() {
  const allSemantics = Object.values(SemanticType); // All supported annotations starting with "@Semantics."
  // add check filter!
  return allSemantics.filter((s) => s.startsWith("@Semantics."));
}

/**
 * Write labelElement -> @ObjectModel.text.element
 * @param element
 * @param oCsnElement
 */
export function labelColumnWriteCsn(element: sap.cdw.commonmodel.Element, oCsnElement: any) {
  if (element && oCsnElement) {
    if (element.labelElement) {
      // When the user has multiple settings for one attribute, like text element, and text association and foreign key association,
      // with the next save of the view/table the conflicts will be removed. Only one of the settings will be kept.
      // Repair Priority: 1: Foreign Key Association, 2. Text association, 3. Text element.
      // So check if there is other high priority setting(foreignKey or foreignText), if no, write to csn.
      if (element.foreignKey || element.foreignText) {
        return;
      }

      LabelColumnAnnotation.writeCsnValue(oCsnElement, element.labelElement.newName || element.labelElement.name);
    }
  }
}

/**
 * Read labelElement <- @ObjectModel.text.element (or @Consumption.labelElement)
 * @param element
 * @param oCsnElement
 */
export function labelColumnReadCsn(element: sap.cdw.commonmodel.Element, oCsnElement: any) {
  let postProcessingLabelColumn: any;
  if (element && oCsnElement) {
    const labelColumn = LabelColumnAnnotation.getLabelColumn(oCsnElement);
    if (labelColumn) {
      postProcessingLabelColumn = {
        labelColumn,
      };
    }
    if (element.unhandledCsn) {
      LabelColumnAnnotation.deleteHandledAnnotation(element.unhandledCsn);
    }
  }
  return postProcessingLabelColumn;
}
export function labelElementGetAnnotations() {
  return LabelColumnAnnotation.getAnnotations();
}

/**
 * Check if ths csn element is a measure
 * Should be used to avoid hard-coding annotation
 * @param oCsnElement
 */
export function isCsnMeasure(oCsnElement: any): boolean {
  return MeasureAnnotation.isCsnMeasure(oCsnElement);
}

/**
 * Read isMeasure <- @AnalyticsDetails.measureType
 * @param element
 * @param oCsnElement
 */
export function isMeasureReadCsn(element: sap.cdw.commonmodel.Element, oCsnElement: any) {
  if (element && MeasureAnnotation.isCsnMeasure(oCsnElement)) {
    element.isMeasure = true;
  }
  if (element.unhandledCsn) {
    MeasureAnnotation.deleteHandledAnnotation(element.unhandledCsn);
  }
}
export function isMeasureGetAnnotations() {
  return MeasureAnnotation.getAnnotations();
}

/**
 * Write isMeasure -> formerly @Analytics.measure...
 * ... or set @AnalyticsDetails.measureType to BASE
 * @param element
 * @param oCsnElement
 */
export function isMeasureWriteCsn(element: sap.cdw.commonmodel.Element, oCsnElement: any) {
  MeasureAnnotation.writeCsn(oCsnElement, element);
}

/**
 * Write isDimension -> @Aggregation.dimension
 * @param element
 * @param oCsnElement
 */
export function isDimensionWriteCsn(element: sap.cdw.commonmodel.Element, oCsnElement: any) {
  if (element && oCsnElement) {
    // do NOT write dimension if element is also a measure!
    if (element.isDimension && !element.isMeasure) {
      oCsnElement[CsnAnnotations.Analytics.dimension] = true;
    }
  }
}

/**
 * Read isDimension <- @Aggregation.dimension
 * @param element
 * @param oCsnElement
 */
export function isDimensionReadCsn(element: sap.cdw.commonmodel.Element, oCsnElement: any) {
  if (element && oCsnElement) {
    // do NOT set measure as dimension!
    if (oCsnElement[CsnAnnotations.Analytics.dimension] && !MeasureAnnotation.isCsnMeasure(oCsnElement)) {
      element.isDimension = true;
    }
    if (element.unhandledCsn) {
      BaseAnnotation.deleteHandledAnnotation(element.unhandledCsn, CsnAnnotations.Analytics.dimension);
    }
  }
}
export function isDimensionGetAnnotations() {
  return [CsnAnnotations.Analytics.dimension];
}

/**
 * Write dataCleansingGeoValue -> @Datawarehouse.spatial.resultOnError
 * @param element
 * @param oCsnElement
 */
export function dataCleansingGeoValueWriteCsn(element: sap.cdw.commonmodel.Element, oCsnElement: any) {
  const FF = sap.ui.getCore().getModel("featureflags").getData();
  if (element && element.dataType === "cds.hana.ST_GEOMETRY" && oCsnElement && FF.DWCO_GEO_DATA_CLEANSING_ON_ERROR) {
    oCsnElement[CsnAnnotations.DataWarehouse.dataCleansing_geoValue] = { "#": element.dataCleansingGeoValue };
  }
}

/**
 * Read dataCleansingGeoValue <- @Datawarehouse.spatial.resultOnError
 * @param element
 * @param oCsnElement
 */
export function dataCleansingGeoValueReadCsn(element: sap.cdw.commonmodel.Element, oCsnElement: any) {
  const FF = sap.ui.getCore().getModel("featureflags").getData();
  if (element && element.dataType === "cds.hana.ST_GEOMETRY" && oCsnElement && FF.DWCO_GEO_DATA_CLEANSING_ON_ERROR) {
    let dataCleansingGeoValue = oCsnElement[CsnAnnotations.DataWarehouse.dataCleansing_geoValue];
    dataCleansingGeoValue =
      dataCleansingGeoValue && dataCleansingGeoValue["#"] && dataCleansingGeoValue["#"].toUpperCase();
    if (dataCleansingGeoValue === SpatialResultOnErr.NULL) {
      element.dataCleansingGeoValue = SpatialResultOnErr.NULL;
    } else if (dataCleansingGeoValue === SpatialResultOnErr.FAIL) {
      element.dataCleansingGeoValue = SpatialResultOnErr.FAIL;
    }
  }
}
export function dataCleansingGeoValueGetAnnotations() {
  return [CsnAnnotations.DataWarehouse.dataCleansing_geoValue];
}

/**
 * Read exceptionAggregagtionSteps <- @AnalyticsDetails.exceptionAggregationSteps
 * @param element
 * @param oCsnElement
 */
export function exceptionAggregationStepsReadCsn(element: sap.cdw.commonmodel.Element, oCsnElement: any) {}
export function exceptionAggregationStepsGetAnnotations() {
  return ExceptionAggregationStepsAnnotation.getAnnotations();
}

/**
 * Write @AnalyticsDetails.exceptionAggregationSteps
 * @param element
 * @param oCsnElement
 */
export function exceptionAggregationStepsWriteCsn(element: sap.cdw.commonmodel.Element, oCsnElement: any) {
  ExceptionAggregationStepsAnnotation.writeCsn(oCsnElement, element);
}

/**
 * Write defaultAggregation -> @Aggregation.default
 * @param element
 * @param oCsnElement
 */
export function defaultAggregationWriteCsn(element: sap.cdw.commonmodel.Element, oCsnElement: any) {
  if (element && oCsnElement) {
    if (element.defaultAggregation && element.defaultAggregation !== "") {
      oCsnElement[CsnAnnotations.Aggregation.default] = {
        "#": element.defaultAggregation,
      };
    }
  }
}

/**
 * Read defaultAggregation <- @Aggregation.default
 * @param element
 * @param oCsnElement
 */
export function defaultAggregationReadCsn(element: sap.cdw.commonmodel.Element, oCsnElement: any) {
  if (element && oCsnElement) {
    if (oCsnElement[CsnAnnotations.Aggregation.default] && oCsnElement[CsnAnnotations.Aggregation.default]["#"]) {
      element.defaultAggregation = oCsnElement[CsnAnnotations.Aggregation.default]["#"];
    }
    if (element.unhandledCsn) {
      BaseAnnotation.deleteHandledAnnotation(element.unhandledCsn, CsnAnnotations.Aggregation.default);
    }
  }
}
export function defaultAggregationGetAnnotations() {
  return [CsnAnnotations.Aggregation.default];
}

export function isCsnHidden(oCsnElement: any): boolean {
  // @Analytics.hidden
  if (oCsnElement[CsnAnnotations.Analytics.hidden] === true) {
    return true;
  }
  return false;
}

export function isCsnAuxiliaryHidden(oCsnElement: any): boolean {
  // @Consumption.hidden
  if (oCsnElement[CsnAnnotations.Consumption.hidden] === true) {
    return true;
  }
  return false;
}

/**
 * Write isVisible -> @Analytics.hidden
 * @param element
 * @param oCsnElement
 */
export function isVisibleWriteCsn(element: sap.cdw.commonmodel.Element, oCsnElement: any) {
  if ((element as any)?.container?.dataCategory !== DataCategory.SQLFACT) {
    if (element && oCsnElement) {
      if (element.isVisible === false) {
        if (element.isAuxiliary) {
          oCsnElement[CsnAnnotations.Consumption.hidden] = true;
          delete oCsnElement[CsnAnnotations.Analytics.hidden];
        } else {
          oCsnElement[CsnAnnotations.Analytics.hidden] = true;
          delete oCsnElement[CsnAnnotations.Consumption.hidden];
        }
      } else if (element.isVisible === true) {
        delete oCsnElement[CsnAnnotations.Consumption.hidden];
        delete oCsnElement[CsnAnnotations.Analytics.hidden];
      }
    }
  }
}

/**
 * Read isVisible <- @Analytics.hidden or @Consumption.hidden
 * @param element
 * @param oCsnElement
 */
export function isVisibleReadCsn(element: sap.cdw.commonmodel.Element, oCsnElement: any) {
  if (element && oCsnElement) {
    if (isCsnAuxiliaryHidden(oCsnElement)) {
      element.isVisible = false;
      // element.isAuxiliary = true;
    } else if (isCsnHidden(oCsnElement)) {
      element.isVisible = false;
      element.isAuxiliary = false;
    }
    if (element.unhandledCsn) {
      BaseAnnotation.deleteHandledAnnotations(element.unhandledCsn, [
        CsnAnnotations.Analytics.hidden,
        CsnAnnotations.Consumption.hidden,
      ]);
    }
  }
}
export function isVisibleGetAnnotations() {
  return [CsnAnnotations.Analytics.hidden, CsnAnnotations.Consumption.hidden];
}

/**
 * Write Filter Selection Type -> @Consumption.filter.selectionKey : { "#": "SINGLE"}
 * @param element
 * @param oCsnElement
 */
export function filterSelectionTypeWriteCsn(element: any, oCsnElement: any) {
  if (element.container?.dataCategory !== DataCategory.SQLFACT) {
    if (element && oCsnElement) {
      if (typeof element.filterSelectionType !== "undefined" && element.filterSelectionType !== "") {
        oCsnElement[CsnAnnotations.Consumption.filter_selectionType] = {
          "#": element.filterSelectionType,
        };
        if (typeof element.filterMandatory !== "undefined") {
          oCsnElement[CsnAnnotations.Consumption.filter_mandatory] = element.filterMandatory;
        }
        if (typeof element.filterHidden !== "undefined") {
          oCsnElement[CsnAnnotations.Consumption.filter_hidden] = element.filterHidden;
        }
        if (typeof element.filterMultipleSelections !== "undefined") {
          oCsnElement[CsnAnnotations.Consumption.filter_multipleSelections] = element.filterMultipleSelections;
        }
        if (typeof element.filterDefaultValue !== "undefined") {
          oCsnElement[CsnAnnotations.Consumption.filter_defaultValue] = element.filterDefaultValue;
        }
        if (typeof element.filterDefaultValueHigh !== "undefined") {
          oCsnElement[CsnAnnotations.Consumption.filter_defaultValueHigh] = element.filterDefaultValueHigh;
        }
      }
    }
  }
}

/**
 * Read Filter Selection Type -> @Consumption.filter.selectionKey : { "#": "SINGLE"}
 * @param element
 * @param oCsnElement
 */
export function filterSelectionTypeReadCsn(element: any, oCsnElement: any) {
  if (element && oCsnElement) {
    let filterMandatory,
      filterHidden,
      filterMultipleSelections,
      filterDefaultValue = "",
      filterDefaultValueHigh = "",
      filterSelectionType = "";
    if (oCsnElement[CsnAnnotations.Consumption.view_filter]) {
      if (typeof oCsnElement[CsnAnnotations.Consumption.view_filter].mandatory !== "undefined") {
        filterMandatory = oCsnElement[CsnAnnotations.Consumption.view_filter].mandatory;
      }
      if (typeof oCsnElement[CsnAnnotations.Consumption.view_filter].hidden !== "undefined") {
        filterHidden = oCsnElement[CsnAnnotations.Consumption.view_filter].hidden;
      }
      if (typeof oCsnElement[CsnAnnotations.Consumption.view_filter].multipleSelections !== "undefined") {
        filterMultipleSelections = oCsnElement[CsnAnnotations.Consumption.view_filter].multipleSelections;
      }
      if (typeof oCsnElement[CsnAnnotations.Consumption.view_filter].defaultValue !== "undefined") {
        filterDefaultValue = oCsnElement[CsnAnnotations.Consumption.view_filter].defaultValue;
      }
      if (typeof oCsnElement[CsnAnnotations.Consumption.view_filter].defaultValueHigh !== "undefined") {
        filterDefaultValueHigh = oCsnElement[CsnAnnotations.Consumption.view_filter].defaultValueHigh;
      }
      if (typeof oCsnElement[CsnAnnotations.Consumption.view_filter].selectionType !== "undefined") {
        filterSelectionType = oCsnElement[CsnAnnotations.Consumption.view_filter].selectionType.slice(1);
      }
    } else {
      if (typeof oCsnElement[CsnAnnotations.Consumption.filter_mandatory] !== "undefined") {
        filterMandatory = oCsnElement[CsnAnnotations.Consumption.filter_mandatory];
      }
      if (typeof oCsnElement[CsnAnnotations.Consumption.filter_hidden] !== "undefined") {
        filterHidden = oCsnElement[CsnAnnotations.Consumption.filter_hidden];
      }
      if (typeof oCsnElement[CsnAnnotations.Consumption.filter_multipleSelections] !== "undefined") {
        filterMultipleSelections = oCsnElement[CsnAnnotations.Consumption.filter_multipleSelections];
      }
      if (typeof oCsnElement[CsnAnnotations.Consumption.filter_defaultValue] !== "undefined") {
        filterDefaultValue = oCsnElement[CsnAnnotations.Consumption.filter_defaultValue];
      }
      if (typeof oCsnElement[CsnAnnotations.Consumption.filter_defaultValueHigh] !== "undefined") {
        filterDefaultValueHigh = oCsnElement[CsnAnnotations.Consumption.filter_defaultValueHigh];
      }
      if (
        oCsnElement[CsnAnnotations.Consumption.filter_selectionType] &&
        oCsnElement[CsnAnnotations.Consumption.filter_selectionType]["#"]
      ) {
        filterSelectionType = oCsnElement[CsnAnnotations.Consumption.filter_selectionType]["#"];
      }
    }
    if (element.unhandledCsn) {
      BaseAnnotation.deleteHandledAnnotations(element.unhandledCsn, [
        CsnAnnotations.Consumption.view_filter,
        CsnAnnotations.Consumption.filter_mandatory,
        CsnAnnotations.Consumption.filter_hidden,
        CsnAnnotations.Consumption.filter_multipleSelections,
        CsnAnnotations.Consumption.filter_defaultValue,
        CsnAnnotations.Consumption.filter_defaultValueHigh,
        CsnAnnotations.Consumption.filter_selectionType,
      ]);
    }
    element.filterMandatory = filterMandatory;
    element.filterHidden = filterHidden;
    element.filterMultipleSelections = filterMultipleSelections;
    element.filterDefaultValue = filterDefaultValue;
    element.filterDefaultValueHigh = filterDefaultValueHigh;
    element.filterSelectionType = filterSelectionType;
  }
}
export function filterSelectionTypeGetAnnotations() {
  return [
    CsnAnnotations.Consumption.view_filter,
    CsnAnnotations.Consumption.filter_mandatory,
    CsnAnnotations.Consumption.filter_hidden,
    CsnAnnotations.Consumption.filter_multipleSelections,
    CsnAnnotations.Consumption.filter_defaultValue,
    CsnAnnotations.Consumption.filter_defaultValueHigh,
    CsnAnnotations.Consumption.filter_selectionType,
  ];
}

/**
 * write referenceAttributes --> @Aggregation.referenceElement
 * @param element
 * @param oCsnElement
 */
export function referenceElementWriteCsn(element, oCsnElement: any) {
  if (element && oCsnElement && element.isCountDistinct) {
    const referenceElement = [];
    element?.referenceAttributes?.toArray().forEach((referenceAttribute) => {
      referenceElement.push(referenceAttribute.newName || referenceAttribute.name);
    });
    oCsnElement[CsnAnnotations.Aggregation.referenceElement] = referenceElement;
  }
}

/**
 * read referenceAttributes <-- @Aggregation.referenceElement
 * @param element
 * @param oCsnElement
 */

export function referenceElementReadCsn(element, oCsnElement: any) {}
export function referenceAttributesGetAnnotations() {
  return [CsnAnnotations.Aggregation.referenceElement];
}

// Multiple DW... around wrong or missing association annotatations
// Repair association annotations for one entity
// Basic rules..
// I- Loop on elements (having association annotation):
//   I-1 REMOVE annotation if wrong (association does not exist)
//   I-2 SAVE (in memory) element and association
// II- Loop on associations (ordered by number of mappings!)
//   II-1 "foreignKey" associations: Set annotation to element IF it has no annotation
//   II-2 non "foreignKey" association: Set if element has no annotation AND if the association is not set to another element
//     (As for non-foreignKey associations, the annotation is NOT supposed to be set to more than one element)
export function repairAssociationAnnotations(entity: sap.cdw.commonmodel.Entity) {
  const entityAssociations = {};
  const nonAnnotatedElements = {};

  // Helper functions
  const checkElementAnnotation = function (element: sap.cdw.commonmodel.Element, annotationProperty): boolean {
    let annotated = false;
    if (element) {
      const annotationValue = element[annotationProperty]; // association name
      if (annotationValue) {
        if (!existsAssociation(entity, annotationValue)) {
          element[annotationProperty] = undefined;
        } else {
          entityAssociations[annotationValue]?.annotatedElements?.push(element.newName || element.name);
          annotated = true;
        }
      }
    }
    return annotated;
  };

  // Pre-processing: Initialize associations
  const sourceAssociations = entity?.allAssociations;
  if (sourceAssociations?.length > 0) {
    for (const sourceAssociation of sourceAssociations) {
      const associationName = sourceAssociation.name;
      const entityAssociation = {
        isText: false,
        isHierarchy: false,
        sourceElements: [],
        annotatedElements: [],
      };
      // association kind
      entityAssociation.isText = sourceAssociation.isText;
      entityAssociation.isHierarchy = sourceAssociation.isHierarchy;
      // source element names: Care about unresolved associations
      const sourceElements = [];
      if (typeof sourceAssociation.target === "string") {
        sourceElements.push(getJoinMembers(sourceAssociation.csn?.on, sourceAssociation.target)?.source);
      } else {
        // loop on mappings..
        sourceAssociation.mappings.forEach(function (oMapping) {
          if (oMapping.source) {
            sourceElements.push(oMapping.source.newName || oMapping.source.name);
          }
        });
      }
      entityAssociation.sourceElements = sourceElements;
      entityAssociations[associationName] = entityAssociation;
    }
  }

  // Processing: Cleand Elements, then Repair associations
  const repairAnnotations = function () {
    // I- First step: Clean elements/remember non-annotated ones
    for (const element of entity.elements?.toArray()) {
      let annotated = false;

      annotated = checkElementAnnotation(element, "foreignKey") || annotated;
      annotated = checkElementAnnotation(element, "foreignText") || annotated; // Ensure checking annotation EVEN IF element already annotated
      annotated = checkElementAnnotation(element, "foreignHierarchy") || annotated;

      if (!annotated) {
        nonAnnotatedElements[element.newName || element.name] = element;
      }
    }

    // II- Second step: (Try to?) Repair associations
    const sortedAssociations = entity.allAssociations.sort(
      (a1, a2) =>
        (entityAssociations[a1?.name]?.sourceElements?.length || 0) -
        (entityAssociations[a2?.name]?.sourceElements?.length || 0)
    );
    for (const association of sortedAssociations) {
      const associationName = association.name;
      const entityAssociation = entityAssociations[associationName];
      const sourceElements = entityAssociation?.sourceElements;
      const annotatedElements = entityAssociation?.annotatedElements;
      if (sourceElements?.length > 0) {
        for (const elementName of sourceElements) {
          const element = nonAnnotatedElements[elementName];
          if (element) {
            // Set annotation if relevant!
            if (entityAssociation.isText) {
              // ONLY one element can hold text association annotation
              if (annotatedElements?.length === 0) {
                element.foreignText = associationName;
                annotatedElements.push(elementName);
              }
            } else if (entityAssociation.isHierarchy) {
              // ONLY one element can hold hierarchy association annotation ??
              if (annotatedElements?.length === 0) {
                element.foreignHierarchy = associationName;
                annotatedElements.push(elementName);
              }
            } else {
              // ONLY one element can hold foreignKey association annotation
              if (annotatedElements?.length === 0) {
                element.foreignKey = associationName;
                annotatedElements.push(elementName);
              }
            }
          }
        }
      }
    }
  };
  const oResource = entity.resource;
  // Protect from undo!
  if (oResource) {
    oResource.applyUndoableAction(
      function () {
        repairAnnotations();
      },
      "Repair association annotations",
      true
    ); // Protect from Undo!
  }
}

/**
 * Get source element names of unresolved/resolved associations
 * @param association
 * @param acceptWrongAnnotation - if true, return source elements even if the element is not the source element of the association but it has the annotation
 */
export function getSourceElementsNames(
  association: sap.cdw.commonmodel.Association,
  acceptWrongAnnotation?: boolean
): string[] {
  const sourceElementsNamesTemp = [];
  if (!association.target?.classDefinition) {
    // unresolved association
    // note that source returned getJoinMembers is an array
    sourceElementsNamesTemp.push(...getJoinMembers(association.csn?.on, association.target)?.source);
  } else {
    // resolved association
    // loop on mappings..
    association.mappings?.forEach(function (oMapping: sap.cdw.commonmodel.ElementMapping) {
      const sourceElement = oMapping.source as any;
      if (sourceElement) {
        sourceElementsNamesTemp.push(sourceElement.newName || sourceElement.name);
      }
    });
  }
  if (acceptWrongAnnotation) {
    for (const oe of association.source.orderedElements) {
      if (
        (oe?.foreignKey === association.name || oe?.foreignText === association.name) &&
        !sourceElementsNamesTemp.includes(oe.newName || oe.name)
      ) {
        sourceElementsNamesTemp.push(oe.newName || oe.name);
      }
    }
  }
  return sourceElementsNamesTemp; // Use the order of mappings rather than orderedElements

  // // Order source elements as orderedElements
  // const sourceElementsNames = [];
  // if (sourceElementsNamesTemp.length > 0) {
  //   for (const oe of association.source.orderedElements) {
  //     if (sourceElementsNamesTemp.includes(oe?.name)) {
  //       sourceElementsNames.push(oe.name);
  //       if (sourceElementsNamesTemp.length === sourceElementsNames.length) {
  //         break;
  //       }
  //     }
  //   }
  // }
  // return sourceElementsNames;
}

// Check if an association exists in an entity (used to avoid inconsistency when writing foreign key annotations)
function existsAssociation(entity: sap.cdw.commonmodel.Entity, associationName: string): boolean {
  if (entity?.allAssociations) {
    return entity.allAssociations.some((a) => associationName === (a.newName || a.name));
  }
  return true; // keep basic unit-tests working !
}

/**
 * Write foreignKey -> @ObjectModel.foreignKey.association
 * @param element
 * @param oCsnElement
 */
export function foreignKeyWriteCsn(element: sap.cdw.commonmodel.Element, oCsnElement: any) {
  if (element && oCsnElement) {
    if (typeof element.foreignKey !== "undefined" && element.foreignKey !== "") {
      // When the user has multiple settings for one attribute, like text element, and text association and foreign key association,
      // with the next save of the view/table the conflicts will be removed. Only one of the settings will be kept.
      // Repair Priority: 1: Foreign Key Association, 2. Text association, 3. Text element.
      // So no change on foreignKeyWriteCsn - directly write to csn.

      // DW101-41633: do NOT write annotation if association does not exist!
      if (existsAssociation(element.container as sap.cdw.commonmodel.Entity, element.foreignKey)) {
        BaseAnnotation.setCsnEqualValue(
          oCsnElement,
          CsnAnnotations.ObjectModel.foreignKey_association,
          element.foreignKey
        );
      }
    }
  }
}

/**
 * Read foreignKey <- @ObjectModel.foreignKey.association
 * @param element
 * @param oCsnElement
 */
export function foreignKeyReadCsn(element: sap.cdw.commonmodel.Element, oCsnElement: any) {
  if (element && oCsnElement) {
    if (
      oCsnElement[CsnAnnotations.ObjectModel.foreignKey_association] &&
      oCsnElement[CsnAnnotations.ObjectModel.foreignKey_association]["="]
    ) {
      element.foreignKey = oCsnElement[CsnAnnotations.ObjectModel.foreignKey_association]["="];
    }
    if (element.unhandledCsn) {
      BaseAnnotation.deleteHandledAnnotation(element.unhandledCsn, CsnAnnotations.ObjectModel.foreignKey_association);
    }
  }
}
export function foreignKeyGetAnnotations() {
  return [CsnAnnotations.ObjectModel.foreignKey_association];
}

/**
 * Write foreignHierarchy -> @ObjectModel.hierarchy.association
 * @param element
 * @param oCsnElement
 */
export function foreignHierarchyWriteCsn(element: sap.cdw.commonmodel.Element, oCsnElement: any) {
  return;
}

/**
 * Read foreignHierarchy <- @ObjectModel.hierarchy.association
 * @param element
 * @param oCsnElement
 */
export function foreignHierarchyReadCsn(element: sap.cdw.commonmodel.Element, oCsnElement: any) {
  if (element && oCsnElement) {
    if (element.unhandledCsn) {
      BaseAnnotation.deleteHandledAnnotation(element.unhandledCsn, CsnAnnotations.ObjectModel.hierarchy_association);
    }
  }
}
export function foreignHierarchyGetAnnotations() {
  return [CsnAnnotations.ObjectModel.hierarchy_association];
}

/**
 * Write foreignText -> @ObjectModel.text.association
 * @param element
 * @param oCsnElement
 */
export function foreignTextWriteCsn(element: sap.cdw.commonmodel.Element, oCsnElement: any) {
  if (oCsnElement && element?.foreignText) {
    // When the user has multiple settings for one attribute, like text element, and text association and foreign key association,
    // with the next save of the view/table the conflicts will be removed. Only one of the settings will be kept.
    // Repair Priority: 1: Foreign Key Association, 2. Text association, 3. Text element.
    // So check if there is other high priority setting(foreignKey), if no, write to csn.
    if (element.foreignKey) {
      return;
    }

    // DW101-41633: do NOT write annotation if association does not exist!
    if (existsAssociation(element.container as sap.cdw.commonmodel.Entity, element.foreignText)) {
      BaseAnnotation.setCsnEqualValue(oCsnElement, CsnAnnotations.ObjectModel.text_association, element.foreignText);
    }
  }
}

/**
 * Read foreignText <- @ObjectModel.text.association
 * @param element
 * @param oCsnElement
 */
export function foreignTextReadCsn(element: sap.cdw.commonmodel.Element, oCsnElement: any) {
  if (element && oCsnElement) {
    const foreignText = BaseAnnotation.getCsnEqualValue(oCsnElement, CsnAnnotations.ObjectModel.text_association);
    element.foreignText = foreignText;
    if (element.unhandledCsn) {
      BaseAnnotation.deleteHandledAnnotation(element.unhandledCsn, CsnAnnotations.ObjectModel.text_association);
    }
  }
}
export function foreignTextGetAnnotations() {
  return [CsnAnnotations.ObjectModel.text_association];
}

/**
 * Write minCardinality, maxCardinality -> cardinality.min, cardinality.max
 * @param association
 * @param oCsnAssociation
 */
export function cardinalityWriteCsn(association: sap.cdw.commonmodel.Association, oCsnAssociation: any) {
  if (association && oCsnAssociation) {
    if (association.minCardinality || association.maxCardinality) {
      oCsnAssociation.cardinality = {};
    }
    if (association.minCardinality) {
      oCsnAssociation.cardinality.min =
        isNaN(parseInt(association.minCardinality, 10)) === true
          ? association.minCardinality
          : parseInt(association.minCardinality, 10);
    }
    if (association.maxCardinality) {
      oCsnAssociation.cardinality.max =
        isNaN(parseInt(association.maxCardinality, 10)) === true
          ? association.maxCardinality
          : parseInt(association.maxCardinality, 10);
    }
  }
}

/**
 * Write isToolingHidden -> @DataWarehouse.tooling.hidden
 * @param element
 * @param oCsnElement
 */
export function isToolingHiddenWriteCsn(element: sap.cdw.commonmodel.Table, oCsnElement: any) {
  if (!SupportedFeaturesService.getInstance().isRepositryHiddenPerspectiveEnabled()) {
    return;
  }
  // by default isToolingHidden is false, so it's unnecessary to add it to csn.
  if (!oCsnElement || !element?.isToolingHidden) {
    return;
  }

  BaseAnnotation.setCsnValue(oCsnElement, CsnAnnotations.DataWarehouse.tooling_hidden, true);
}

/**
 * Read isToolingHidden <- @DataWarehouse.tooling.hidden
 * @param element
 * @param oCsnElement
 */
export function isToolingHiddenReadCsn(element: sap.cdw.commonmodel.Table, oCsnElement: any) {
  if (!SupportedFeaturesService.getInstance().isRepositryHiddenPerspectiveEnabled()) {
    return;
  }
  if (!oCsnElement || !element) {
    return;
  }
  element.isToolingHidden = BaseAnnotation.getCsnValue(oCsnElement, CsnAnnotations.DataWarehouse.tooling_hidden);
  if (element && oCsnElement && element.unhandledCsn) {
    BaseAnnotation.deleteHandledAnnotation(element.unhandledCsn, CsnAnnotations.DataWarehouse.tooling_hidden);
  }
}
export function isToolingHiddenGetAnnotations() {
  return [CsnAnnotations.DataWarehouse.tooling_hidden];
}

export const findInCollectionByNewNameOrName = function (collection, name) {
  let resultElement;
  if (collection) {
    resultElement = collection.selectObject({ newName: name });
    if (!resultElement) {
      resultElement = collection.selectObject({ name: name });
    }
  }
  return resultElement;
};

export const findInCollectionByName = function (collection, name) {
  return (
    collection &&
    collection.selectObject({
      name: name,
    })
  );
};

export const findInCollectionByNewName = function (collection, name) {
  return (
    collection &&
    collection.selectObject({
      newName: name,
    })
  );
};

export const updateAssociationMappings = function (oAssociation, sourceElements, targetElements) {
  const isSkylineERModel = oAssociation?.resource?.model?.isSkylineERModel;
  const oSource = oAssociation && (oAssociation.source || oAssociation.view);
  const oTarget = oAssociation && (oAssociation.target || oAssociation.dataAccessControl);
  if (oSource) {
    let nLen = (sourceElements && targetElements && sourceElements.length) || 0;
    oAssociation.joinSourceElements = [];
    oAssociation.joinSourceElementNames = [];
    oAssociation.joinTargetElementNames = [];
    if (oTarget && oAssociation.mappings.length > 0) {
      oAssociation.mappings.forEach(function (oMapping) {
        if (oMapping.source && oMapping.target) {
          const sourceName = oMapping.source.newName || oMapping.source.name;
          const targetName = oMapping.target.name;
          const sIndex = sourceElements.indexOf(sourceName);
          const tIndex = targetElements.indexOf(targetName);
          if (sIndex !== -1 && tIndex !== -1) {
            sourceElements.splice(sIndex, 1);
            targetElements.splice(tIndex, 1);

            // Save source and target
            oAssociation.joinSourceElements.push(oMapping.source);
            oAssociation.joinSourceElementNames.push(sourceName);
            oAssociation.joinTargetElementNames.push(targetName);

            // decrease remaining mapping count
            nLen--;
          }
        }
      });
    }
    for (let i = 0; i < nLen; i++) {
      const sourceElement = sourceElements[i];
      const targetElement = targetElements[i];
      const oSourceElement = findInCollectionByNewName(oSource.elements, sourceElement);
      const oTargetElement = oTarget && findInCollectionByName(oTarget.elements, targetElement);

      // Create Mapping
      oAssociation.joinSourceElementNames.push(sourceElement);
      oAssociation.joinTargetElementNames.push(targetElement);
      if (oSourceElement) {
        oAssociation.joinSourceElements.push(oSourceElement);
        if (oTargetElement) {
          const oClass = sap.galilei.model.getClass(
            isSkylineERModel ? "sap.cdw.ermodeler.ElementMapping" : CommonQualifiedClassNames.ELEMENT_MAPPING
          );
          const oElementMapping = oClass.create(oAssociation.resource, {
            source: oSourceElement,
            target: oTargetElement,
          });
          oAssociation.mappings.push(oElementMapping);
        }
      }
    }
  }
};

/**
 *
 * @param {*} oJoinOn : csn definition for join condition
 * @param {*} sJoinName : association name (important for guessing source and target)
 */
export const getJoinMembers = function (oJoinOn: any, sJoinName: any): any {
  const oJoinMembers = { source: [], target: [] };
  const len = (oJoinOn && Array.isArray(oJoinOn) && oJoinOn.length) || 0;
  let i = 0;

  while (i + 2 < len) {
    // Source & Target
    let targetFound = false;
    let iTarget;
    let oRef;
    // sJoin Name is Set: the target is prefixed by the join name
    if (sJoinName) {
      let oRefTarget = oJoinOn[i].ref;
      if (
        oRefTarget &&
        Array.isArray(oRefTarget) &&
        (oRefTarget.length === 2 || oRefTarget.length === 3) &&
        oRefTarget[0] === sJoinName
      ) {
        oRef = oJoinOn[i + 2].ref;
        iTarget = i;
        targetFound = true;
      }
      if (!targetFound) {
        oRefTarget = oJoinOn[i + 2].ref;
        if (
          oRefTarget &&
          Array.isArray(oRefTarget) &&
          (oRefTarget.length === 2 || oRefTarget.length === 3) &&
          oRefTarget[0] === sJoinName
        ) {
          oRef = oJoinOn[i].ref;
          targetFound = true;
          iTarget = i + 2;
        }
      }
    }
    if (!targetFound) {
      // Assume that source is the first ref in the array and target is the third one..
      iTarget = i + 2;
      oRef = oJoinOn[i].ref;
      if (oRef && Array.isArray(oRef) && oRef.length > 1 && oRef[0] !== "$projection") {
        // .. unless the first ref is actually the target !
        iTarget = i;
        oRef = oJoinOn[i + 2].ref;
      }
    }

    // Source ref
    if (oRef && Array.isArray(oRef)) {
      oJoinMembers.source.push(oRef[oRef.length - 1]);
    }

    // Target ref
    oRef = oJoinOn[iTarget].ref;
    if (oRef && Array.isArray(oRef)) {
      oJoinMembers.target.push(oRef[oRef.length - 1]);
    }

    i += 4; // ["s1", "=", "t2", "and", ...]
  }
  return oJoinMembers;
};

/**
 * Read minCardinality, maxCardinality <- cardinality.min, cardinality.max
 * @param associationss
 * @param oCsnAssociation
 */
export function cardinalityReadCsn(association: sap.cdw.commonmodel.Association, oCsnAssociation: any) {
  if (association && oCsnAssociation && oCsnAssociation.cardinality) {
    if (oCsnAssociation.cardinality.min) {
      association.minCardinality = oCsnAssociation.cardinality.min;
    }
    if (oCsnAssociation.cardinality.max) {
      association.maxCardinality = oCsnAssociation.cardinality.max;
    }
    if (association.unhandledCsn) {
      BaseAnnotation.deleteHandledAnnotation(association.unhandledCsn, "cardinality");
      // Association case: remove type, target and on attributes
      BaseAnnotation.deleteHandledAnnotations(association.unhandledCsn, ["type", "target", "on"]);
    }
  }
}
export function cardinalityGetAnnotations() {
  return ["cardinality"];
}

/**
 * Write valueHelpDefinition -> @Consumption.valueHelpDefinition
 * @param parameter
 * @param oCsnParameter
 */
export function parameterValueHelpWriteCsn(parameter: sap.cdw.commonmodel.Parameter, oCsnParameter: any) {
  if (SupportedFeaturesService.getInstance().isParameterValueHelpEnabled()) {
    if (parameter && oCsnParameter && parameter.valueHelpDefinition) {
      const valueHelpEntity = parameter.valueHelpDefinition;
      if (
        valueHelpEntity &&
        (valueHelpEntity.entity || valueHelpEntity.entityName) &&
        (valueHelpEntity.element || valueHelpEntity.elementName)
      ) {
        oCsnParameter[CsnAnnotations.Consumption.valueHelpDefinition] = [];
        oCsnParameter[CsnAnnotations.Consumption.valueHelpDefinition].push({
          entity: {
            name: valueHelpEntity.entity?.qualifiedName || valueHelpEntity.entityName,
            element: valueHelpEntity.element?.newName || valueHelpEntity.element?.name || valueHelpEntity.elementName,
          },
        });
      }
    }
  }
}

/**
 * Read @Consumption.valueHelpDefinition and set valueHelpDefinition
 * @param parameter
 * @param oCsnParameter
 */
export function parameterValueHelpReadCsn(parameter: sap.cdw.commonmodel.Parameter, oCsnParameter: any) {
  if (SupportedFeaturesService.getInstance().isParameterValueHelpEnabled()) {
    if (parameter && oCsnParameter) {
      const valueHelpDefinition = oCsnParameter[CsnAnnotations.Consumption.valueHelpDefinition];
      if (valueHelpDefinition && valueHelpDefinition.length > 0 && parameter.resource) {
        const oElementClass = sap.galilei.model.getClass("sap.cdw.commonmodel.ReferenceEntity");
        const ref = oElementClass.create(parameter.resource, {
          entityName: valueHelpDefinition[0].entity.name,
          elementName: valueHelpDefinition[0].entity.element,
        });
        parameter.valueHelpDefinition = ref;
      }
      if (parameter.unhandledCsn) {
        BaseAnnotation.deleteHandledAnnotation(parameter.unhandledCsn, CsnAnnotations.Consumption.valueHelpDefinition);
      }
    }
  }
}
export function parameterValueHelpGetAnnotations() {
  if (SupportedFeaturesService.getInstance().isParameterValueHelpEnabled()) {
    return [CsnAnnotations.Consumption.valueHelpDefinition];
  } else {
    return [];
  }
}

/**
 * Update View Data Access Controls from its oCsn array
 * @param view : View to update
 * @param oCsnViewDataAccessControls : CSN array of view data access controls
 */
export function updateViewDataAccessControls(view: sap.cdw.commonmodel.View, oCsnViewDataAccessControls: any[]) {
  const viewDataAccessControlClass = sap.galilei.model.getClass(CommonQualifiedClassNames.VIEW_DATA_ACCESS_CONTROL);
  const model = view.resource && (view.resource.model as sap.cdw.commonmodel.Model);
  if (!model || !viewDataAccessControlClass) {
    return;
  }

  // Create/Update View Data Access Controls
  view.viewDataAccessControls &&
    oCsnViewDataAccessControls.forEach((oCsnViewDataAccessControl) => {
      updateViewDataAccessControl(view, oCsnViewDataAccessControl);
    });
}

/**
 * Update View Data Access Control from its oCsn
 * @param view : View to update
 * @param oCsnViewDataAccessControl : CSN of view data access control
 */
export function updateViewDataAccessControl(view: sap.cdw.commonmodel.View, oCsnViewDataAccessControl: any) {
  const model = view?.resource?.model as sap.cdw.commonmodel.Model;
  const isSkylineERModel = (model as any)?.isSkylineERModel;
  const viewDataAccessControlClass = sap.galilei.model.getClass(
    isSkylineERModel ? "sap.cdw.ermodeler.ViewDataAccessControl" : CommonQualifiedClassNames.VIEW_DATA_ACCESS_CONTROL
  );
  if (!model || !viewDataAccessControlClass) {
    return;
  }

  // Create/Update View Data Access Controls
  if (view.viewDataAccessControls) {
    // Get target name and set it as object name
    const name = oCsnViewDataAccessControl?.target;
    if (name) {
      // Find/Create ViewDataAccessControl
      let viewDataAccessControl = findInCollectionByName(view.viewDataAccessControls, name);
      if (viewDataAccessControl) {
        // clear mappings and update csn
        viewDataAccessControl.mappings.deleteAll();
        viewDataAccessControl.csn = oCsnViewDataAccessControl;
      } else {
        viewDataAccessControl = viewDataAccessControlClass.create(view.resource, {
          name,
          view: view,
          csn: oCsnViewDataAccessControl, // original csn MUST be saved to handel 'unresolved' Data Access Controls
        });
        view.viewDataAccessControls.push(viewDataAccessControl);
      }
      // Look for target Data Access Control
      const targetDataAccessControl = model.dataAccessControls
        .toArray()
        .find((dataAccessControl) => oCsnViewDataAccessControl.target === dataAccessControl.name);
      if (targetDataAccessControl) {
        // Set target and create mappings
        viewDataAccessControl.dataAccessControl = targetDataAccessControl;
        const oJoinMembers = getJoinMembers(oCsnViewDataAccessControl.on, oCsnViewDataAccessControl.target);
        if (oJoinMembers && oJoinMembers.source && oJoinMembers.target) {
          updateAssociationMappings(viewDataAccessControl, oJoinMembers.source, oJoinMembers.target);
        }
      }
    }
  }
}

/**
 * Read view data access controls
 * @param view
 * @param oCsnView
 */
export function viewDataAccessControlsReadCsn(view: sap.cdw.commonmodel.View, oCsnView: any) {
  if (!view || !oCsnView) {
    return;
  }
  const oCsnViewDataAccessControls = oCsnView[CsnAnnotations.DataWarehouse.dataAccessControl_usage];
  // delete CsnAnnotations.DataWarehouse.dataAccessControl_usage
  if (view.unhandledCsn) {
    BaseAnnotation.deleteHandledAnnotation(view.unhandledCsn, CsnAnnotations.DataWarehouse.dataAccessControl_usage);
  }
  if (!oCsnViewDataAccessControls || oCsnViewDataAccessControls.length === 0) {
    view.isRestrictAccess = false; // TODO: Check for use of specific annotation ?
    return;
  }

  view.isRestrictAccess = true;
  // Read View Data Access Controls
  updateViewDataAccessControls(view, oCsnViewDataAccessControls);
}
export function viewDataAccessControlsGetAnnotations() {
  return [CsnAnnotations.DataWarehouse.dataAccessControl_usage];
}

export function getViewDataAccessControlOnCsn(viewDataAccessControl: sap.cdw.commonmodel.ViewDataAccessControl) {
  const onCsn = [];
  const mappings = viewDataAccessControl && viewDataAccessControl.mappings;
  if (mappings && mappings.length) {
    let needAnd = false;
    mappings.toArray().forEach((mapping) => {
      if (mapping && mapping.source && mapping.target) {
        if (needAnd) {
          onCsn.push("and");
        } else {
          needAnd = true;
        }
        const sourceElement = mapping.source as sap.cdw.commonmodel.Element;
        onCsn.push({ ref: [sourceElement?.newName || sourceElement?.name] });
        onCsn.push("=");
        onCsn.push({ ref: [viewDataAccessControl.name, mapping.target.name] });
      }
    });
  }
  return onCsn;
}

/**
 * Write view data access controls
 * @param view
 * @param oCsnView
 */
export function viewDataAccessControlsWriteCsn(view: sap.cdw.commonmodel.View, oCsnView: any) {
  if (!view.viewDataAccessControls || view.viewDataAccessControls.length === 0) {
    delete oCsnView?.[CsnAnnotations.DataWarehouse.dataAccessControl_usage];
    return;
  }
  const viewDataAccessControls = view.viewDataAccessControls.toArray();

  // Write View Data Access Controls
  oCsnView[CsnAnnotations.DataWarehouse.dataAccessControl_usage] = viewDataAccessControls.map((viewDataAccessControl) =>
    viewDataAccessControl.dataAccessControl
      ? {
          on: getViewDataAccessControlOnCsn(viewDataAccessControl),
          target: viewDataAccessControl.dataAccessControl.name,
        }
      : viewDataAccessControl.csn
  );
}

/**
 * Find/Create the DataAccessControl then Find/Create the ViewDataAccessControl
 * TODO: in case of new attachement, a default mapping could be suggested.
 * @param view : view to update
 * @param name : name of Data Access Control
 * @param dacCSN : CSN definition of the data access control
 */
export function findOrCreateAndAttachDACFromCSN(view: sap.cdw.commonmodel.View, name: string, dacCSN: any) {
  let oVDAC;

  const nsErModeler = sap.cdw.ermodeler;
  const oResource = view && view.resource;
  const oModel = oResource && (oResource.model as sap.cdw.commonmodel.Model);
  const isSkylineERModel = (oModel as any).isSkylineERModel;
  const label = dacCSN["@EndUserText.label"];
  let dacObject = oModel && findInCollectionByName(oModel.dataAccessControls, name);
  if (dacObject === undefined) {
    dacObject = nsErModeler.ModelImpl.createObject(
      isSkylineERModel ? "sap.cdw.ermodeler.DataAccessControl" : CommonQualifiedClassNames.DATA_ACCESS_CONTROL,
      { name },
      oModel
    );
    dacObject && oModel.dataAccessControls.push(dacObject);
  }
  if (dacObject) {
    const erCsnToModel = nsErModeler.ErCsnToModel.getInstance(true);
    // Refresh DAC elements from CSN
    dacObject.elements.deleteAll();
    erCsnToModel.updateObjectFromCsn(dacObject, dacCSN);

    // Create view data access control and attach to view
    const viewDataAccessControlClass = sap.galilei.model.getClass(
      isSkylineERModel ? "sap.cdw.ermodeler.ViewDataAccessControl" : CommonQualifiedClassNames.VIEW_DATA_ACCESS_CONTROL
    );
    oVDAC = view && findInCollectionByName(view.viewDataAccessControls, name);
    if (oVDAC === undefined) {
      oVDAC = viewDataAccessControlClass.create(oResource, {
        name,
        label,
        view: view,
      });
      oVDAC && view.viewDataAccessControls.push(oVDAC);
    }
    if (oVDAC) {
      oVDAC.dataAccessControl = dacObject;
    }
  }

  return oVDAC;
}

/**
 * Find/Create the DataAccessControl then update ViewDataAccessControl
 * @param viewDataAccessControl : view dta access control to update
 * @param dacCSN : CSN definition of the data access control
 */
export function updateViewFromDataAccessControlCSN(
  viewDataAccessControl: sap.cdw.commonmodel.ViewDataAccessControl,
  dacCSN: any
) {
  const nsErModeler = sap.cdw.ermodeler;
  const oResource = viewDataAccessControl && viewDataAccessControl.resource;
  const oModel = oResource && (oResource.model as sap.cdw.commonmodel.Model);
  const isSkylineERModel = (oModel as any).isSkylineERModel;
  const name = viewDataAccessControl && viewDataAccessControl.name;
  let dacObject = oModel && findInCollectionByName(oModel.dataAccessControls, name);
  if (dacObject === undefined) {
    dacObject = nsErModeler.ModelImpl.createObject(
      isSkylineERModel ? "sap.cdw.ermodeler.DataAccessControl" : CommonQualifiedClassNames.DATA_ACCESS_CONTROL,
      { name },
      oModel
    );
    dacObject && oModel.dataAccessControls.push(dacObject);
  }
  if (dacObject) {
    const erCsnToModel = nsErModeler.ErCsnToModel.getInstance(true);
    // delete elements and update
    dacObject.elements.deleteAll();
    erCsnToModel.updateObjectFromCsn(dacObject, dacCSN);

    // Update view data access control
    updateViewDataAccessControl(viewDataAccessControl.container as sap.cdw.commonmodel.View, viewDataAccessControl.csn);
  }
}

/**
 * Clone Json to avoid overwritting original one
 * @param oCsn
 */
export function cloneJson(oCsn: any) {
  try {
    return JSON.parse(JSON.stringify(oCsn));
  } catch {
    return Object.assign({}, oCsn);
  }
}

/**
 * Compare 2 Csn objects
 * @param oCsn
 */
export function compareCsn(csn1, csn2) {
  try {
    return JSON.stringify(csn1) === JSON.stringify(csn2);
  } catch {
    return false;
  }
}

/**
 * Handle csn of elements for data preview
 * @param oCsn
 */
export function handleCsnForDataPreview(oCsn: any, isCrossEntity?, isMdmPreview?) {
  const csnElements = oCsn && oCsn.elements;
  if (csnElements) {
    const aElementKeys = Object.keys(csnElements);
    // Fix bug FPA101-7816, When deployed table has association with shared target table in other ER, data preview of this table in GV will be failed
    for (const elementKey of aElementKeys) {
      const csnElement = csnElements[elementKey];
      if (csnElement && csnElement.type === "cds.Association") {
        delete csnElements[elementKey];
      }
      if (isCrossEntity && csnElement && csnElement.kind === "type") {
        // simple type in element has been changed to normal datatype, so the kind is no need
        delete csnElement.kind;
      }
    }
  }
  /* The below condition is added to allow query and params in CSN if there are input parameters available in the csn for data preview */
  if (oCsn && !oCsn.params && oCsn.query && !isMdmPreview) {
    delete oCsn.query;
  }

  // Do not send mixin for any data preview
  handleRemoveMixinForDataPreview(oCsn);
}
/**
 * Handle csn of persisted view for data preview
 * @param oCsn
 * @param oObject
 */
export function handlePersistedViewCsnForDataPreview(oCsn: any, oObject) {
  const tempCloneCsn = cloneJson(oCsn);
  const objectName = oObject.technicalName ? oObject.technicalName : oObject.name;
  const selectedOutputViewCSNName = NamingHelper.encodeDataPreviewName(objectName);
  let outputViewCSNDef = tempCloneCsn.definitions[selectedOutputViewCSNName];
  outputViewCSNDef = getSelectCSNOfPersistedView(outputViewCSNDef, objectName);
  tempCloneCsn.definitions = {};
  tempCloneCsn.definitions[selectedOutputViewCSNName] = outputViewCSNDef;
  tempCloneCsn.definitions[objectName] = oCsn.definitions[selectedOutputViewCSNName];
  tempCloneCsn.definitions[objectName]["@EndUserText.label"] = objectName;
  delete tempCloneCsn.definitions[objectName].query;
  oCsn.definitions = tempCloneCsn.definitions;
  return oCsn;
}

/**
 * Data Preview On Repository Tree
 * @param fileObject
 * @param checkType
 * @param modelTreeHandler
 */
export async function dataPreviewOnRepoTree(fileObject, checkType, modelTreeHandler) {
  const { entityName, spaceName } = getEntityAndCrossSpaceNameFromCSN(fileObject && fileObject.definitions);
  if (
    fileObject &&
    (!checkType || DocumentStorageService.getInstance().getDocumentType(fileObject)) &&
    fileObject.definitions
  ) {
    const isCrossEntity = !!fileObject.definitions[spaceName + "." + entityName];
    const objectStatus = fileObject?.attributesMap?.object_status?.value;
    const name = isCrossEntity ? spaceName + "." + entityName : entityName;
    const previewName = NamingHelper.encodeDataPreviewName(name); // Avoid to replace the CSN of this object
    const csnFromGetCSN = await sap.cdw.querybuilder.ViewModelToCsn.getCSN(
      previewName,
      {},
      {
        rootObject: { name: previewName, fromRepositoryTree: true },
        dataPreview: true,
      },
      true
    ); // Get CSN structure
    const csn = {
      ...csnFromGetCSN,
      "#technicalType": fileObject["#technicalType"],
      selectedEntityFromDiagram: fileObject.selectedEntityFromDiagram,
    };
    // Fill CSN
    const csnDefinitions = cloneJson(fileObject.definitions);
    handleCsnForDataPreview(csnDefinitions[name], isCrossEntity);
    const elements = csnDefinitions[name] && csnDefinitions[name].elements;
    // Remove the formula element (Analytic Measure)
    const elementNames = elements && Object.keys(elements);
    elementNames &&
      elementNames.forEach((elementName) => {
        if (isAnalyticMeasureElement(elements[elementName])) {
          delete elements[elementName];
        }
      });

    const mimicQueryDef = csn && csn.definitions && csn.definitions[previewName];
    if (
      mimicQueryDef &&
      mimicQueryDef.elements &&
      mimicQueryDef.query &&
      mimicQueryDef.query.SELECT &&
      mimicQueryDef.query.SELECT.from &&
      mimicQueryDef.query.SELECT.columns
    ) {
      Object.assign(mimicQueryDef.elements, csnDefinitions[name].elements);
      for (const columnName in elements) {
        mimicQueryDef.query.SELECT.columns.push(
          elements[columnName].key ? { key: true, ref: [columnName] } : { ref: [columnName] }
        );
      }
      mimicQueryDef.query.SELECT.from = { ref: [name] };
      Object.assign(csn.definitions, csnDefinitions);
      modelTreeHandler(
        previewName,
        csn,
        this.spaceName,
        /* Indicate this is data preview from tree */ name,
        undefined,
        undefined,
        undefined,
        objectStatus
      );
    }
  }
}

/* Handle csn of persisted view for sql preview
 * @param oCsn
 * @param oObject
 */
export function handlePersistedViewCsnForSQLPreview(oCsn: any, oObject) {
  const tempCloneCsn = cloneJson(oCsn);
  const srcCloneCsn = cloneJson(oCsn);
  const objectName = oObject.technicalName ? oObject.technicalName : oObject.name;
  delete srcCloneCsn.definitions[objectName].query;
  const selectedOutputViewCSNName = objectName;
  let outputViewCSNDef = tempCloneCsn.definitions[selectedOutputViewCSNName];
  outputViewCSNDef = getSelectCSNOfPersistedView(outputViewCSNDef, objectName);
  tempCloneCsn.definitions = {};
  const dummyNameForPersistence = selectedOutputViewCSNName + "_Persisted";
  tempCloneCsn.definitions[dummyNameForPersistence] = outputViewCSNDef;
  tempCloneCsn.definitions[objectName] = srcCloneCsn.definitions[objectName];
  oCsn.definitions = tempCloneCsn.definitions;
  return oCsn;
}

/**
 * Get select csn of persisted view for sql and data preview
 * @param outputViewCSNDef
 * @param oObject
 */
export function getSelectCSNOfPersistedView(outputViewCSNDef: any, objectName: any) {
  if (outputViewCSNDef.query && outputViewCSNDef.query.SET) {
    delete outputViewCSNDef.query.SET;
  }
  outputViewCSNDef.query.SELECT = {};
  const from = {
    ref: [objectName],
  };
  outputViewCSNDef.query.SELECT.from = from;
  const columnArray = [];
  if (outputViewCSNDef.elements) {
    const elementArray = Object.keys(outputViewCSNDef.elements);
    for (const elementValue of elementArray) {
      let refObj = {};
      const element = elementValue;
      refObj = {
        ref: [element],
      };
      columnArray.push(refObj);
    }
    outputViewCSNDef.query.SELECT.columns = columnArray;
  }
  return outputViewCSNDef;
}
export function getCardinalityCsn(oObject) {
  const lCardinality = oObject.leftCardinality;
  const rCardinality = oObject.rightCardinality;
  if (lCardinality !== "" && rCardinality !== "") {
    let cardinalityCsn;
    const left = Object.keys(CardinalityValues).filter((obj) => CardinalityValues[obj] === oObject.leftCardinality);
    const right = Object.keys(CardinalityValues).filter((obj) => CardinalityValues[obj] === oObject.rightCardinality);
    const cardinality = left + "TO" + right;
    switch (cardinality) {
      case "ONETOONE":
        cardinalityCsn = { src: 1, max: 1 };
        break;
      case "ONETOMANY":
        cardinalityCsn = { src: 1, max: "*" };
        break;
      case "ONETOEXACTONE":
        cardinalityCsn = { src: 1, min: 1, max: 1 };
        break;
      case "EXACTONETOONE":
        cardinalityCsn = { src: 1, srcmin: 1, max: 1 };
        break;
      case "EXACTONETOMANY":
        cardinalityCsn = { src: 1, srcmin: 1, max: "*" };
        break;
      case "EXACTONETOEXACTONE":
        cardinalityCsn = { src: 1, srcmin: 1, min: 1, max: 1 };
        break;
      case "MANYTOONE":
        cardinalityCsn = { max: 1 };
        break;
      case "MANYTOMANY":
        cardinalityCsn = { max: "*" };
        break;
      case "MANYTOEXACTONE":
        cardinalityCsn = { min: 1, max: 1 };
        break;
    }
    return cardinalityCsn;
  }
}

/**
 * Get isAllowConsumption from CSN.
 * @param {object} oCSN
 * @param kind
 */
export function getAllowConsumptionFromCsn(oCsn: any) {
  return AllowConsumptionAnnotation.getAllowConsumption(oCsn);
}

/**
 * @param csn the csn object whose query member should be updated
 */
export function updateCsnQueryAliases(csn: any): void {
  if (csn) {
    if (csn && csn.SELECT) {
      updateQuerySelectAliases(csn.SELECT);
    } else if (csn.SET && csn.SET.args) {
      updateQuerySetAliases(csn.SET.args);
    }
  }
}

export function updateQuerySelectAliases(SELECT: any): void {
  // Columns..
  if (SELECT.columns) {
    updateSelectColumnAliases(SELECT.columns);
  }

  // Search for sub-Queries
  if (SELECT.where) {
    updateSelectWhereAliases(SELECT.where);
  }
}

export function updateSelectColumnAliases(columns: any): void {
  if (columns && Array.isArray(columns)) {
    let nVal = 0;
    let nXpr = 0;
    let nFunc = 0;
    const usedNames = {};
    for (let iCol = 0; iCol < columns.length; iCol++) {
      const oColumn = columns[iCol];
      // Constant
      if (oColumn.val !== undefined && oColumn.as === undefined) {
        oColumn.as = "val" + ++nVal;
      }

      // Expression
      if (oColumn.xpr !== undefined && oColumn.as === undefined) {
        oColumn.as = "xpr" + ++nXpr;
      }

      // Function
      if (oColumn.func !== undefined && oColumn.as === undefined) {
        oColumn.as = "func" + ++nFunc;
      }

      // Other cases: if no alias is defined, ensure name is unique!
      if (oColumn.as === undefined && oColumn.ref && oColumn.ref.length) {
        const name = oColumn.ref[oColumn.ref.length - 1];
        const oUsedName = usedNames[name];
        if (oUsedName === undefined) {
          // First use: initialize, no alias neded
          usedNames[name] = { name: name, i: 1 };
        } else {
          // Already used? add alias and increment
          oColumn.as = name + oUsedName.i++;
        }
      }
    }
  }
}

export function updateSelectWhereAliases(where: any): void {
  if (where && Array.isArray(where)) {
    for (let iWhere = 0; iWhere < where.length; iWhere++) {
      const oWhere = where[iWhere];
      if (oWhere && (oWhere.SELECT || oWhere.SET)) {
        updateCsnQueryAliases(oWhere);
      }
    }
  }
}

export function updateQuerySetAliases(args: any): void {
  if (args && Array.isArray(args)) {
    for (let iArgs = 0; iArgs < args.length; iArgs++) {
      const oArg = args[iArgs];
      if (oArg && (oArg.SELECT || oArg.SET)) {
        updateCsnQueryAliases(oArg);
      }
    }
  }
}

export function converUnsupportedDataType(dataType: any) {
  if (dataType === "cds.hana.CHAR") {
    return "cds.hana.NCHAR";
  } else if (dataType === "cds.hana.VARCHAR") {
    return "cds.String";
  } else if (dataType === "cds.hana.CLOB") {
    return "cds.LargeString";
  } else {
    return dataType;
  }
}

export function updateUnreolvedAssociationsProperties(model, fnCallback?) {
  const unresolvedAssociations = model.allAssociations.filter((a) => !!!a?.target?.classDefinition?.name) || [];
  const targetNames = [];
  for (const association of unresolvedAssociations) {
    const targetName = association?.targetName;
    if (targetName && !targetNames.includes(targetName)) {
      targetNames.push(targetName);
    }
  }
  if (targetNames.length) {
    setUnresolvedAssoProperties(targetNames, unresolvedAssociations, model, fnCallback);
  }
}

export function setUnresolvedAssoProperties(targetNames, aAssociationTargetMaps, oModel?, fnCallback?) {
  const localEntityCSN = DocumentStorageService.getInstance().getOrLoadDocumentsList({
    detailsProperties: [DocumentProperty.csn, DocumentProperty.businessType],
    filters: { name: targetNames },
  });
  const promiseArr = [localEntityCSN];
  const sharedNames = targetNames
    .filter((target) => target?.includes("."))
    .map((fullname) => fullname.substring(fullname.indexOf(".") + 1));
  if (sharedNames.length > 0) {
    // If target of association is shared object
    const spaceId = CurrentContextService.getInstance().getCurrentSpaceId();
    const sharedEntityCSN = getArtefactSharesForTarget(
      spaceId,
      [DocumentProperty.csn, DocumentProperty.businessType],
      [`name:${sharedNames.join("|")}`]
    );
    promiseArr.push(sharedEntityCSN);
  }

  Promise.all(promiseArr)
    .then((result) => {
      const aRepoTargetObjects =
        result.length === 2 && result[1].results ? result[0].concat(result[1].results) : result[0];
      aRepoTargetObjects.forEach((oTargetRepoObject) => {
        if (oModel) {
          // To cache (the shared spaces and objects's) name->label based on target objects of associations
          // because sometimes login does not have some space's permission so that cannot use getSpaceList/getSpaceDetail,
          // otherwise we need enhance shares endpoint call to use CRUD to be same as designObjects but it still may not work due to different filters
          oModel._spaces = oModel._spaces || {};
          oModel._sharedObjects = oModel._sharedObjects || {};
          if (oTargetRepoObject.spaceName) {
            oModel._spaces[oTargetRepoObject.spaceName] =
              oTargetRepoObject.spaceBusinessName || oTargetRepoObject.spaceName;
            oModel._sharedObjects[oTargetRepoObject.name] =
              oTargetRepoObject.content?.definitions?.[oTargetRepoObject.name]?.["@EndUserText.label"] ||
              oTargetRepoObject.name;
          }
        }
        const sTargetName = oTargetRepoObject.name;
        const aAssociations = aAssociationTargetMaps?.filter(
          (o) =>
            (o?.targetName || o?.target?.name || o?.target) === sTargetName ||
            (o?.targetName || o?.target?.name || o?.target) === oTargetRepoObject.spaceName + "." + sTargetName
        );
        aAssociations?.forEach((oUnresolvedAssociation) => {
          if (oTargetRepoObject.spaceName) {
            if (oUnresolvedAssociation.target?.classDefinition?.name) {
              oUnresolvedAssociation.target.crossSpaceName = oTargetRepoObject.spaceName;
              oUnresolvedAssociation.target.crossSpaceBusinessName = oTargetRepoObject.spaceBusinessName;
              oUnresolvedAssociation.target.label =
                oTargetRepoObject.content?.definitions?.[oTargetRepoObject.name]?.["@EndUserText.label"];
              oUnresolvedAssociation.target.name =
                oUnresolvedAssociation.target.entityName =
                oUnresolvedAssociation.target.crossSpaceEntityName =
                  oTargetRepoObject.name;
              oUnresolvedAssociation.target.displayName = NamingHelper.getDisplayName(
                oUnresolvedAssociation.target.name,
                oUnresolvedAssociation.target.label
              );
              oUnresolvedAssociation.target.isEntity = true;
              oUnresolvedAssociation.target.isCrossSpace = true;
            } else {
              oUnresolvedAssociation.crossSpaceName = oTargetRepoObject.spaceName;
              oUnresolvedAssociation.crossSpaceBusinessName = oTargetRepoObject.spaceBusinessName;
              oUnresolvedAssociation.entityBusinessName =
                oTargetRepoObject.content?.definitions?.[oTargetRepoObject.name]?.["@EndUserText.label"];
              oUnresolvedAssociation.entityName = oTargetRepoObject.name;
            }
          } else {
            if (oUnresolvedAssociation.target?.classDefinition?.name) {
              oUnresolvedAssociation.target.label =
                oTargetRepoObject.label ||
                oTargetRepoObject.content?.definitions?.[oTargetRepoObject.name]?.["@EndUserText.label"];
              oUnresolvedAssociation.target.name = oTargetRepoObject.name;
              oUnresolvedAssociation.target.isEntity = true;
            } else {
              oUnresolvedAssociation.entityBusinessName = oTargetRepoObject.label;
            }
          }
          const businessType = oTargetRepoObject["#businessType"] || oTargetRepoObject.properties["#businessType"];
          const definitions =
            oTargetRepoObject.definitions?.[sTargetName] || oTargetRepoObject.content.definitions?.[sTargetName];
          // For Hierarchy Association
          if (
            businessType === "DWC_HIERARCHY" ||
            DataCategoryAnnotation.readCsn(definitions) === DataCategory.HIERARCHY
          ) {
            oUnresolvedAssociation.isHierarchy = true;
            const exHierarchy = definitions?.["@Hierarchy.parentChild"]?.[0].recurse;
            oUnresolvedAssociation.hierarchyParentName = exHierarchy?.parent[0]["="];
            oUnresolvedAssociation.hierarchyChildName = exHierarchy?.child[0]["="];
            exHierarchy.parent?.forEach((pElm) => {
              if (
                !oUnresolvedAssociation.hierarchyParentsName ||
                !Array.isArray(oUnresolvedAssociation.hierarchyParentsName)
              ) {
                oUnresolvedAssociation.hierarchyParentsName = [];
              }
              oUnresolvedAssociation?.hierarchyParentsName?.push({ name: pElm["="] });
            });

            exHierarchy.child?.forEach((pElm) => {
              if (
                !oUnresolvedAssociation.hierarchyChildrenName ||
                !Array.isArray(oUnresolvedAssociation.hierarchyChildrenName)
              ) {
                oUnresolvedAssociation.hierarchyChildrenName = [];
              }
              oUnresolvedAssociation?.hierarchyChildrenName?.push({ name: pElm["="] });
            });
          }

          // For Hierarchy with Directory Association
          if (
            businessType === "DWC_HIERARCHY_WITH_DIRECTORY" ||
            DataCategoryAnnotation.readCsn(definitions) === DataCategory.HIERARCHY_WITH_DIRECTORY
          ) {
            oUnresolvedAssociation.isHierarchyWithDirectory = true;
          }

          // Association target is a dimension
          if (
            businessType === "DWC_DIMENSION" ||
            DataCategoryAnnotation.readCsn(definitions) === DataCategory.DIMENSION
          ) {
            oUnresolvedAssociation.isDimension = true;
          }

          // For unresolved Text Association, set csn values
          if (businessType === "DWC_TEXT") {
            oUnresolvedAssociation.isText = true;
            oUnresolvedAssociation.isTextTarget = true;
            oUnresolvedAssociation.unresolvedMappings = [];
            const csnOn = [...oUnresolvedAssociation?.csn?.on];
            if (csnOn?.length > 0) {
              csnOn.unshift("and"); // Add 'and' at the beginning of the array
              for (const i in csnOn) {
                const m = csnOn[i];
                if (m === "and") {
                  oUnresolvedAssociation.unresolvedMappings.push({});
                } else if (m.ref) {
                  if (m.ref.length === 1) {
                    const sourceName = m.ref[m.ref.length - 1];
                    oUnresolvedAssociation.unresolvedMappings[
                      oUnresolvedAssociation.unresolvedMappings.length - 1
                    ].source = { name: sourceName };
                  } else if (m.ref.length === 2) {
                    const targetName = m.ref[m.ref.length - 1];
                    const oMappingTargetCsn = { name: targetName, isKey: undefined, isNoneSemanticType: undefined };
                    const targetElement = definitions?.elements[targetName];
                    oMappingTargetCsn.isKey = targetElement.key;
                    oMappingTargetCsn.isNoneSemanticType = !Object.getOwnPropertyNames(targetElement)?.some(
                      (t) => t.includes("@Semantics.") && targetElement[t]
                    );
                    oUnresolvedAssociation.unresolvedMappings[
                      oUnresolvedAssociation.unresolvedMappings.length - 1
                    ].target = oMappingTargetCsn;
                  }
                }
              }
            }
          }
          // Define targetInfo property for unresolved Associations! (used in Popover)
          if (oUnresolvedAssociation.target && !oUnresolvedAssociation.target.classDefinition) {
            Object.defineProperty(oUnresolvedAssociation, "targetInfo", {
              get() {
                return oUnresolvedAssociation.unresolvedTarget;
              },
            });
          }
        });
      });
    })
    .catch(() => {})
    .finally(() => {
      if (fnCallback) {
        // After all process of associations are done, call back to do UI refresh
        fnCallback();
      }
    });
}

/**
 * Data Preview On Task chain editor Table and view
 * @param fileObject
 */
export async function dataPreviewForTaskChainObject(fileObject) {
  const { entityName, spaceName } = getEntityAndCrossSpaceNameFromCSN(fileObject && fileObject.definitions);
  if (fileObject && fileObject.definitions) {
    const isCrossEntity = !!fileObject.definitions[spaceName + "." + entityName];
    const name = isCrossEntity ? spaceName + "." + entityName : entityName;
    const previewName = NamingHelper.encodeDataPreviewName(name); // Avoid to replace the CSN of this object
    const csn = await sap.cdw.querybuilder.ViewModelToCsn.getCSN(
      previewName,
      {},
      {
        rootObject: { name: previewName, fromRepositoryTree: true },
        dataPreview: true,
      },
      true
    ); // Get CSN structure
    // Fill CSN
    const csnDefinitions = cloneJson(fileObject.definitions);
    handleCsnForDataPreview(csnDefinitions[name], isCrossEntity);
    const elements = csnDefinitions[name] && csnDefinitions[name].elements;
    // Remove the formula element (Analytic Measure)
    const elementNames = elements && Object.keys(elements);
    elementNames &&
      elementNames.forEach((elementName) => {
        if (isAnalyticMeasureElement(elements[elementName])) {
          delete elements[elementName];
        }
      });

    const mimicQueryDef = csn && csn.definitions && csn.definitions[previewName];
    if (
      mimicQueryDef &&
      mimicQueryDef.elements &&
      mimicQueryDef.query &&
      mimicQueryDef.query.SELECT &&
      mimicQueryDef.query.SELECT.from &&
      mimicQueryDef.query.SELECT.columns
    ) {
      Object.assign(mimicQueryDef.elements, csnDefinitions[name].elements);
      for (const columnName in elements) {
        mimicQueryDef.query.SELECT.columns.push(
          elements[columnName].key ? { key: true, ref: [columnName] } : { ref: [columnName] }
        );
      }
      mimicQueryDef.query.SELECT.from = { ref: [name] };
      Object.assign(csn.definitions, csnDefinitions);
    }
    return csn;
  }
}

export const getNonPersistedTablesForAdvisorPreview = async function (
  selectedObj: any,
  previewModelName: string,
  spaceId: string,
  isDirty: boolean,
  csn: any,
  isRepotree: boolean = false
) {
  let entityName;
  let listOfDependentEntities = [];
  if (isRepotree) {
    listOfDependentEntities.push({
      entity: previewModelName,
      space: spaceId,
    });
    entityName = previewModelName;
  } else if (selectedObj?.classDefinition?.name === "Entity" && selectedObj?.isCrossSpace) {
    /* TO-DO: Add me when advisor supports cross space entities
      listOfDependentEntities.push({
        entity: selectedObj.crossSpaceEntityName,
        space: selectedObj.crossSpaceName,
      });
      entityName = previewModelName;
    */
  } else if (selectedObj?.classDefinition?.name === "Entity") {
    listOfDependentEntities.push({
      entity: previewModelName,
      space: spaceId,
    });
    entityName = previewModelName;
  } else if (selectedObj?.classDefinition?.name === "Output" && selectedObj.deploymentStatus === 1 && !isDirty) {
    listOfDependentEntities.push({
      entity: previewModelName,
      space: spaceId,
    });
    entityName = previewModelName;
  } else {
    let csnEntityName = previewModelName;
    if (!csn.definitions.hasOwnProperty(csnEntityName)) {
      if (csnEntityName.endsWith("%%END%%")) {
        csnEntityName = NamingHelper.decodeDataPreviewName(csnEntityName);
      } else {
        csnEntityName = NamingHelper.encodeDataPreviewName(csnEntityName);
      }
    }
    listOfDependentEntities = getDependentsFromCsnForAdvisorPreview(csn, csnEntityName, spaceId);
  }
  if (listOfDependentEntities.length > 0) {
    const advisorResponse = await getAdvisorResponse(listOfDependentEntities, spaceId);
    const nonReplicatedEntities = AdvisorUtil.getNonPersistedEntities(advisorResponse, entityName);
    return {
      errors: advisorResponse?.errors || [],
      nonReplicatedEntities,
    };
  }
  return {
    nonReplicatedEntities: [],
  };
};

export const updateParamDefaults = function (isEREditor, parameters, workbenchModel, qualifiedName) {
  if (isEREditor) {
    // update parameters in UI model with new values from parameters
    const paramDefaults: any[] = workbenchModel.getProperty("/paramDefaults") || [];
    for (const param of parameters) {
      param.objectName = qualifiedName;
      const index = paramDefaults.findIndex(
        (p) => p.objectName === param.objectName && (p.name || p.mappedName) === (param.name || param.mappedName)
      );
      if (index !== -1) {
        paramDefaults[index] = param;
      } else {
        paramDefaults.push(param);
      }
    }
    workbenchModel.setProperty("/paramDefaults", paramDefaults);
  }
};

/**
 * Get the list of dependent Views and Remote tables from CSN
 * @param {object} oCSNDefinitions
 * @param {string} entityName
 * @param {string} spaceId
 */
const getDependentsFromCsnForAdvisorPreview = function (
  oCSNDefinitions: any = {},
  previewEntityName: string,
  spaceId: string
): ISpaceEntity[] {
  const dependentEntities: ISpaceEntity[] = [];
  if (oCSNDefinitions && oCSNDefinitions.definitions) {
    Object.keys(oCSNDefinitions.definitions).forEach((element) => {
      if (
        !element?.endsWith("%%END%%") &&
        element !== previewEntityName &&
        oCSNDefinitions.definitions[element].kind !== "context" &&
        oCSNDefinitions.definitions[element].kind !== "type"
      ) {
        const contextName = getEntityContextNameFromCSN(
          oCSNDefinitions.definitions,
          element,
          /* bFirstQualifier */ true
        );
        if (contextName) {
          /* TO-DO: Add me when advisor supports cross space entities
            const context = getObjectFromCSN(oCSNDefinitions.definitions, contextName, "context");
            if (context) {
              if (context[CsnAnnotations.DataWarehouse.space_name]) {
                const entityName = sap.cdw.commonmodel.ObjectImpl.removeQualifierInName(
                  element,
                  true
                );
                dependentEntities.push({
                  entity: entityName,
                  space: contextName,
                });
              }
            }
          */
        } else {
          if (dependentEntities.findIndex((el: ISpaceEntity) => el.space === spaceId && el.entity === element) === -1) {
            dependentEntities.push({
              entity: element,
              space: spaceId,
            });
          }
        }
      }
    });
  }
  return dependentEntities;
};

/**
 * Get the list of non persisted entities from Deployed Views and Remote tables.
 * @param {object} listOfDependentEntities
 * @param {string} spaceId
 */

const getAdvisorResponse = async function (listOfDependentEntities: ISpaceEntity[], spaceId: string) {
  let advisorResponse: IAdvisorResponse;
  try {
    advisorResponse = await AdvisorUtil.getPreviewLineageAdvisorDetails(spaceId, listOfDependentEntities);
  } catch {
    advisorResponse = undefined;
  }
  return advisorResponse;
};

/**
 * Get the list of dependent Views and Remote tables from CSN
 * @param {object} oCSNDefinitions
 * @param {string} entityName
 * @param {string} spaceId
 * @param {string} originalModelName
 */
export const getDependentsFromCsn = async function (
  oCSNDefinitions: any = {},
  previewEntityName: string,
  spaceId: string,
  originalModelName?: string,
  selectedObj?: any
): Promise<any[]> {
  const dependentEntities = [];
  let advisorResponse;
  if (
    (selectedObj &&
      selectedObj?.classDefinition?.name !== "Entity" &&
      selectedObj?.classDefinition?.name !== "Output") ||
    (selectedObj && !(selectedObj.deploymentStatus === 1 || selectedObj.deploymentStatus === 2))
  ) {
    advisorResponse = undefined;
  } else {
    try {
      const originalName = NamingHelper.decodeDataPreviewName(previewEntityName);
      advisorResponse = await AdvisorUtil.getPersistencyAdvisorDetails(originalName, spaceId);
    } catch {
      advisorResponse = undefined;
    }
  }
  let isViewPersisted = false;
  if (
    advisorResponse !== undefined &&
    Object.keys(advisorResponse).length > 0 &&
    advisorResponse?.entityStats &&
    advisorResponse?.entityStats[0]?.isPersisted
  ) {
    isViewPersisted = true;
  }
  if (isViewPersisted) {
    return dependentEntities;
  } else {
    if (oCSNDefinitions && oCSNDefinitions.definitions) {
      Object.keys(oCSNDefinitions.definitions).forEach((element) => {
        let remoteEntity = {};
        const csnElements = oCSNDefinitions.definitions[element];
        if (csnElements.hasOwnProperty("@DataWarehouse.remote.connection")) {
          const contextName = getEntityContextNameFromCSN(
            oCSNDefinitions.definitions,
            element,
            /* bFirstQualifier */ true
          );
          if (contextName) {
            const context = getObjectFromCSN(oCSNDefinitions.definitions, contextName, "context");
            if (context) {
              if (context[CsnAnnotations.DataWarehouse.space_name]) {
                const entityName = sap.cdw.commonmodel.ObjectImpl.removeQualifierInName(
                  element,
                  /* bFirstQualifier */ true
                );
                remoteEntity = {
                  entity: entityName,
                  label: "RemoteTable",
                  spaceName: contextName,
                };
              }
            }
          } else {
            remoteEntity = {
              entity: element,
              label: "RemoteTable",
              spaceName: spaceId,
            };
          }
          dependentEntities.push(remoteEntity);
        } else if (
          element !== previewEntityName &&
          element !== originalModelName &&
          (oCSNDefinitions.definitions[element].hasOwnProperty("@DataWarehouse.sqlEditor.query") ||
            oCSNDefinitions.definitions[element].hasOwnProperty("@DataWarehouse.querybuilder.model"))
        ) {
          const viewEntity = {
            entity: element,
            label: "View",
            spaceName: spaceId,
          };
          dependentEntities.push(viewEntity);
        }
      });
    }
    return dependentEntities;
  }
};

/**
 * Get the list of non persisted entities from Deployed Views and Remote tables.
 * @param {object} listOfDependentEntities
 * @param {string} spaceId
 */

export async function getNonPersistedRemoteTables(
  listOfDependentEntities: any = {},
  spaceId: string,
  selectedObj: any
): Promise<any> {
  let nonReplicatedEntities = [];
  const remoteTableList = [];
  const crossSpaceRemoteTableList = [];
  const remoteTableFromSameSpace = [];
  const remoteTableFromCrossSpace = {};
  for (const obj of listOfDependentEntities) {
    if (obj.label === "View") {
      let advisorNonPersistedRes = [];
      let advisorResponse;
      try {
        const originalName = NamingHelper.decodeDataPreviewName(obj.entity);
        advisorResponse = await AdvisorUtil.getPersistencyAdvisorDetails(originalName, obj.spaceName);
      } catch {
        advisorResponse = undefined;
      }

      if (advisorResponse && advisorResponse.entityStats && advisorResponse.entityStats.length > 0) {
        advisorNonPersistedRes = AdvisorUtil.getNonPersistedEntities(advisorResponse, obj.entity);
      }
      if (advisorNonPersistedRes) {
        nonReplicatedEntities = nonReplicatedEntities.concat(advisorNonPersistedRes);
      }
    } else if (obj.label === "RemoteTable") {
      if (selectedObj?.isCrossSpace) {
        const isFederatedTables = await getFederationInfo(obj.spaceName, [obj.entity]);
        if (isFederatedTables && isFederatedTables.length > 0) {
          isFederatedTables.forEach((element) => {
            if (!element.FEDERATION_ONLY) {
              crossSpaceRemoteTableList.push(element.OBJECT_NAME);
            }
          });
        }
        const crossSpaceRemoteTableRes = await getReplicationInfo(obj.spaceName, crossSpaceRemoteTableList);
        if (crossSpaceRemoteTableRes && crossSpaceRemoteTableRes.length > 0) {
          crossSpaceRemoteTableRes.forEach((element) => {
            const remoteInfo = listNonPersistedRemoteTables(element);
            nonReplicatedEntities.push(remoteInfo);
          });
        }
      } else {
        remoteTableList.push({ entity: obj.entity, spaceName: obj.spaceName });
      }
    }
  }
  if (remoteTableList.length > 0) {
    remoteTableList.forEach((remoteEntity) => {
      if (remoteEntity.spaceName === spaceId) {
        remoteTableFromSameSpace.push(remoteEntity.entity);
      } else {
        if (remoteTableFromCrossSpace[remoteEntity.spaceName] === undefined) {
          remoteTableFromCrossSpace[remoteEntity.spaceName] = [];
        }
        remoteTableFromCrossSpace[remoteEntity.spaceName].push(remoteEntity);
      }
    });
  }
  if (remoteTableFromSameSpace.length > 0) {
    const updatedRemoteTableFromSameSpace = [];
    const isFederatedTables = await getFederationInfo(spaceId, remoteTableFromSameSpace);
    if (isFederatedTables && isFederatedTables.length > 0) {
      isFederatedTables.forEach((element) => {
        if (!element.FEDERATION_ONLY) {
          updatedRemoteTableFromSameSpace.push(element.OBJECT_NAME);
        }
      });
    }
    const remoteTableRes = await getReplicationInfo(spaceId, updatedRemoteTableFromSameSpace);
    if (remoteTableRes && remoteTableRes.length > 0) {
      remoteTableRes.forEach((element) => {
        const remoteInfo = listNonPersistedRemoteTables(element);
        if (remoteInfo) {
          nonReplicatedEntities.push(remoteInfo);
        }
      });
    }
  }
  if (Object.keys(remoteTableFromCrossSpace).length > 0) {
    for (const [space, tables] of Object.entries(remoteTableFromCrossSpace)) {
      const sharedObjArr = [];
      const isFederatedTables = await getFederationInfo(
        space,
        (tables as []).map((remoteTable: any) => remoteTable.entity)
      );
      if (isFederatedTables && isFederatedTables.length > 0) {
        isFederatedTables.forEach((element) => {
          if (!element.FEDERATION_ONLY) {
            sharedObjArr.push(element.OBJECT_NAME);
          }
        });
      }
      const crossSpaceRemoteTableRes = await getReplicationInfo(space, sharedObjArr);
      if (crossSpaceRemoteTableRes && crossSpaceRemoteTableRes.length > 0) {
        crossSpaceRemoteTableRes.forEach((element) => {
          const remoteInfo = listNonPersistedRemoteTables(element);
          // Adding condition check to prevent 'undefined' being pushed to nonReplicatedEntities
          // Fix for: DW101-41475
          if (remoteInfo) {
            nonReplicatedEntities.push(remoteInfo);
          }
        });
      }
    }
  }
  return nonReplicatedEntities;
}

export async function getReplicationInfo(spaceId: string, objectNames: string[]): Promise<any[]> {
  const sUrl = `monitor/${spaceId}/remotetables/dataaccessstatus?includeAdapterInfo=true`;
  if (objectNames.length === 0) {
    return;
  }
  try {
    const body: any = { objectNames: objectNames };
    const response = await ServiceCall.post(
      sUrl,
      {
        contentType: ContentType.APPLICATION_JSON,
      },
      true,
      JSON.stringify(body)
    );
    return (response as any).data.results;
  } catch (err) {
    return;
  }
}

export async function getFederationInfo(spaceId: string, objectNames: string[]): Promise<any[]> {
  const sUrl = `monitor/${spaceId}/remotetables/dataaccessstatus?includeFederationOnly=true`;
  if (objectNames.length === 0) {
    return;
  }
  try {
    const body: any = { objectNames: objectNames };
    const response = await ServiceCall.post(
      sUrl,
      {
        contentType: ContentType.APPLICATION_JSON,
      },
      true,
      JSON.stringify(body)
    );
    return (response as any).data.results;
  } catch (err) {
    return;
  }
}

export function listNonPersistedRemoteTables(remoteApiRes: any): any {
  let remoteInfo;
  if (remoteApiRes.DATA_ACCESS_TYPE === "REMOTE") {
    remoteInfo = {
      entity: remoteApiRes.OBJECT_NAME,
      label: "RemoteTable",
      isPersisted: false,
      adapterName: remoteApiRes.ADAPTER_NAME,
    };
  }
  return remoteInfo;
}

export function fillParamterDefaultValue(param) {
  switch (param.primitiveDataType) {
    case CDSDataType.STRING:
    case CDSDataType.LARGE_STRING:
    case CDSDataType.HANA_NCHAR:
      param.defaultForDataPreview = "";
      break;
    case CDSDataType.INTEGER:
    case CDSDataType.INTEGER64:
    case CDSDataType.HANA_SMALLINT:
    case CDSDataType.HANA_TINYINT:
      param.defaultForDataPreview = 0;
      break;
    case CDSDataType.DECIMAL:
    case CDSDataType.DECIMAL_FLOAT:
    case CDSDataType.DOUBLE:
    case CDSDataType.HANA_REAL:
    case CDSDataType.HANA_SMALLDECIMAL:
      param.defaultForDataPreview = 1.0;
      break;
    case CDSDataType.DATE:
      param.defaultForDataPreview = { func: "CURRENT_DATE" };
      break;
    case CDSDataType.TIME:
    case CDSDataType.DATETIME:
      param.defaultForDataPreview = { func: "CURRENT_TIME" };
      break;
    case CDSDataType.TIMESTAMP:
      param.defaultForDataPreview = { func: "CURRENT_TIMESTAMP" };
      break;
    case CDSDataType.BOOLEAN:
      param.defaultForDataPreview = true;
    default:
      param.defaultForDataPreview = 0;
      break;
  }
}

/**
 * Handle removing of mixin for data preview
 * @param oCsn
 */
export function handleRemoveMixinForDataPreview(oCsn: any) {
  if (oCsn?.query?.SELECT?.mixin) {
    // Remove associated columns from query column section
    const columns = oCsn.query.SELECT.columns || [];
    if (oCsn.elements && columns.length !== Object.keys(oCsn.elements).length) {
      const keys = Object.keys(oCsn.query.SELECT.mixin);
      for (const associationName of keys) {
        const foundColIndex =
          oCsn.query?.SELECT?.columns?.findIndex((col) =>
            col.hasOwnProperty("as")
              ? col.as === associationName
              : col.ref && col.ref[col.ref.length - 1] === associationName
          ) || -1;
        if (foundColIndex > -1) {
          oCsn.query.SELECT.columns?.splice(foundColIndex, 1);
        }
      }
    }
    delete oCsn.query.SELECT.mixin;
  }
  return oCsn;
}
