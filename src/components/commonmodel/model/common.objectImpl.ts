/**
 * eslint-disable id-blacklist
 *
 * @format
 */

/** Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved. */

import { ICsnDefinition as INektonCsnDefinition } from "@sap-nekton/core-types";
import { DacType, getDacType } from "@sap-nekton/dac-utils";
import {
  isBDCRepositoryTransportDataEnabled,
  isHashPartitioningEnabled,
} from "../../abstractbuilder/commonservices/FeatureFlagCheck";
import {
  labelColumnIconFormatter,
  labelColumnKeyFormatter,
  labelColumnTIconFormatter,
} from "../../ermodeler/js/utility/commonFormatter";
import { IModel, Repo } from "../../shell/utility/Repo";
import { CurrentContextService } from "../api/CurrentContextService";
import { DocumentProperty } from "../api/DocumentStorageService";
import { SupportedFeaturesService } from "../api/SupportedFeaturesService";
import { CsnAnnotations } from "../csn/csnAnnotations";
import { getEntityName, handleAIChange, isORDTable } from "../utility/CommonUtils";
import {
  changeColumnNamesInHierarchyWithDirectory,
  deleteAssociationInHierarchyWithDirectory,
} from "../utility/HierarchyWithDirectoryUtils";
import { CommonQualifiedClassNames, CommonShortClassNames, ObjectInstanceTypes } from "./const/model.const";
import {
  AggregationDisplayTypes,
  AssociationMaxCardinality,
  AssociationType,
  AttributesSemanticTypes,
  CDSDataType,
  DEFAULT_SRID,
  DataCategory,
  ExceptionAggregationDisplayTypes,
  ExceptionAggregationSupportedSourceTypes,
  IDisplayDataType,
  MeasureSemanticTypes,
  SRID_VALUES,
  SemanticType,
  TextSemanticTypes,
  analyticMeasureUnsupportedDataTypes,
  getCDSDataTypeDefinitions,
  getElementPossibleDataTypes,
  possibleCardinalityValues,
} from "./types/cds.types";

sap.galilei.namespace("sap.cdw.commonmodel", function (nsLocal) {
  /**
   * @class
   * ObjectImpl implements all methods related to objects
   */
  nsLocal.ObjectImpl = sap.galilei.core.defineClass({
    fullClassName: "sap.cdw.commonmodel.ObjectImpl",

    statics: {
      resourceBundle: undefined,

      /**
       * Gets the resource bundle.
       */
      getResourceBundle: function () {
        if (!nsLocal.ObjectImpl.resourceBundle) {
          // eslint-disable-next-line @typescript-eslint/no-var-requires
          const bundleName = require("../i18n/i18n.properties");
          const resourceModel = new sap.ui.model.resource.ResourceModel({
            bundleName: bundleName,
          });
          nsLocal.ObjectImpl.resourceBundle = resourceModel.getResourceBundle();
        }
        return nsLocal.ObjectImpl.resourceBundle;
      },

      /**
       * Checks if a Context represents a Cross Space.
       * @param context
       */
      isCrossSpaceContext: function (context: sap.cdw.commonmodel.Context): boolean {
        if (context && context.crossSpace && context.crossSpace.schema) {
          return true;
        }
        return false;
      },

      /**
       * Checks if an Entity is a Local Table.
       * @param entity
       */
      isRemoteTable: function (entity: sap.cdw.commonmodel.Entity): boolean {
        if (
          entity &&
          ((entity.remote && entity.remote.connection) ||
            String(entity.type) === String(sap.cdw.ermodeler?.ModelImpl.SOURCE_OBJECT_TYPE["RemoteTable"]))
        ) {
          return true;
        }
        return false;
      },

      /**
       * Checks if an Entity is a delta enabled Local Table.
       * @param entity
       */
      isDeltaTable: function (entity: sap.cdw.commonmodel.Entity): boolean {
        if (entity?.deltaTable?.type) {
          return true;
        }
        return false;
      },

      /**
       * Checks if an Entity is a Local  Table on Files.
       * @param entity
       */
      isLTF: function (entity: sap.cdw.commonmodel.Entity): boolean {
        if (entity && entity?.ltfTable) {
          return true;
        }
        return false;
      },

      /**
       * Checks if an Entity has data transport allowed.
       * @param entity
       */
      isDataTransportAllowed: function (entity: sap.cdw.commonmodel.Entity): boolean {
        if (isBDCRepositoryTransportDataEnabled() && entity && entity?.isDataTransportAllowed === true) {
          return true;
        }
        return false;
      },

      partitionedColumns: function (entity: sap.cdw.commonmodel.Entity): any {
        const partitionedColumns = [];
        entity?.partitions?.forEach((elm: any) => {
          partitionedColumns.push(elm?.id);
        });
        return partitionedColumns;
      },

      /**
       * Checks if an Entity is a Local Schema Table.
       * @param entity
       */
      isLocalSchemaTable: function (entity: sap.cdw.commonmodel.Entity): boolean {
        if (entity && entity.localSchema && entity.localSchema.schema) {
          return true;
        }
        return false;
      },

      /**
       * Checks if an Entity is a Cross Space Entity.
       * @param entity
       */
      isCrossSpaceEntity: function (entity: sap.cdw.commonmodel.Entity): boolean {
        if (entity) {
          if (entity.rootContext) {
            return entity.rootContext.isCrossSpace;
          }
          if (entity.crossSpace && entity.crossSpace.spaceName) {
            return true;
          }
        }
        return false;
      },

      /**
       * Checks if an Entity is a Table.
       * @param entity
       */
      isTable: function (entity: sap.cdw.commonmodel.Entity): boolean {
        if (entity) {
          if (
            nsLocal.ObjectImpl.isRemoteTable(entity) ||
            nsLocal.ObjectImpl.isLocalSchemaTable(entity) ||
            !nsLocal.ObjectImpl.isView(entity)
          ) {
            return true;
          }
        }
        return false;
      },

      /**
       * Checks if an Entity CSN has a query definition.
       * @param entity
       */
      hasQueryInCSN: function (csnEntity: any): boolean {
        if (
          csnEntity &&
          (csnEntity.query ||
            csnEntity[CsnAnnotations.DataWarehouse.querybuilder_model] ||
            csnEntity[CsnAnnotations.DataWarehouse.sqlEditor_query] ||
            csnEntity[CsnAnnotations.DataWarehouse.tableFunction_script])
        ) {
          return true;
        }
        return false;
      },

      /**
       * Checks if an Entity is a Data Access Control.
       * @param entity
       */
      isDataAccessControl: function (entity: sap.cdw.commonmodel.Entity): boolean {
        if (entity) {
          if (!nsLocal.ObjectImpl.isTable(entity) && !nsLocal.ObjectImpl.isView(entity)) {
            return true;
          }
        }
        return false;
      },

      hasDataAccessControl: function (entity: sap.cdw.commonmodel.Entity): boolean {
        return Boolean(entity?.viewDataAccessControls?.toArray().length > 0);
      },

      /**
       * Check if there is boolean Data Access Control attached.
       * @param entity
       */
      areThereFilterDacsAttached: async function (entity: sap.cdw.commonmodel.Entity): Promise<boolean> {
        return ((await nsLocal.ObjectImpl.getDistinctDacTypesAttached(entity)) as string[]).some(
          (d) => d !== DacType.SingleValues
        );
      },

      /**
       * For a given view, loads all DAC CSNs and gets their types
       * @param entity
       */
      getDistinctDacTypesAttached: async function (entity: sap.cdw.commonmodel.Entity): Promise<string[]> {
        if (!(entity?.viewDataAccessControls.length > 0)) {
          return [];
        }

        const distinctDacTypesSet: Set<string> = new Set();
        let dacsInSpace: IModel[];
        for (const { dataAccessControl, name: dacName } of entity.viewDataAccessControls.toArray()) {
          // For the DACs attached after the page is opened
          if (dataAccessControl) {
            // Currently ICsnDefinition from @types and ICsnDefinition from @sap-nekton/core-types have incompatibilities.
            // This casting is a workaround to avoid compilation errors that should be removed once ICsnDefinition from @types gets updated properly.
            distinctDacTypesSet.add(getDacType(dataAccessControl.csn as INektonCsnDefinition & ICsnDefinition));
          } else {
            // Opening a saved view with DACs: need to load from repo
            if (!dacsInSpace) {
              const spaceName = CurrentContextService.getInstance().getCurrentSpaceId();
              dacsInSpace = await Repo.getDataAccessControlList(spaceName, ["name", "csn"]);
            }

            const dacSavedWithView = dacsInSpace.find(({ name }) => name === dacName);
            distinctDacTypesSet.add(
              getDacType(
                dacSavedWithView.csn.definitions[dacSavedWithView.name] as INektonCsnDefinition & ICsnDefinition
              )
            );
          }
        }
        return [...distinctDacTypesSet];
      },

      /**
       * Checks if an Entity CSN is a Data Access Control.
       * @param entity
       */
      isDataAccessControlCSN: function (csnEntity: any): boolean {
        if (csnEntity && csnEntity[CsnAnnotations.DataWarehouse.dataAccessControl_definition]) {
          return true;
        }
        return false;
      },

      /**
       * Checks if an Entity has a query definition.
       * @param entity
       */
      hasQuery: function (entity: sap.cdw.commonmodel.Entity): boolean {
        return nsLocal.ObjectImpl.hasQueryInCSN(entity && (entity.csn || entity.repositoryCSN));
      },

      /**
       * Checks if an Entity is a View.
       * @param entity
       */
      isView: function (entity: sap.cdw.commonmodel.Entity): boolean {
        if (entity) {
          if (nsLocal.ObjectImpl.hasQuery(entity)) {
            return true;
          } else if (
            entity.isCrossSpace &&
            [
              sap.cdw.ermodeler.ModelImpl.SOURCE_OBJECT_TYPE["View"],
              sap.cdw.ermodeler.ModelImpl.SOURCE_OBJECT_TYPE["DataSet"],
              sap.cdw.ermodeler.ModelImpl.SOURCE_OBJECT_TYPE["Dimension"],
              sap.cdw.ermodeler.ModelImpl.SOURCE_OBJECT_TYPE["Fact"],
              sap.cdw.ermodeler.ModelImpl.SOURCE_OBJECT_TYPE["SQLFact"],
              sap.cdw.ermodeler.ModelImpl.SOURCE_OBJECT_TYPE["Text"],
              sap.cdw.ermodeler.ModelImpl.SOURCE_OBJECT_TYPE["Hierarchy"],
            ].includes((entity as any).type)
          ) {
            // For shared view, its csn may not have query, so use type to check
            return true;
          } else if (
            entity.repositoryCSN &&
            entity.repositoryCSN.definition &&
            entity.repositoryCSN.definition[DocumentProperty.isView]
          ) {
            return true;
          } else if (entity.repositoryCSN && entity.repositoryCSN[DocumentProperty.isView]) {
            return true;
          }
        }
        return false;
      },

      /**
       * Gets all the direct contexts and all their sub-contexts.
       * @param contextContainer
       */
      getAllContexts: function (
        contextContainer: sap.cdw.commonmodel.Model | sap.cdw.commonmodel.Context
      ): sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Context> {
        const allContexts = new sap.galilei.model.ArrayCollection();
        if (contextContainer && contextContainer.contexts) {
          for (const context of contextContainer.contexts.toArray()) {
            allContexts.push(context);
            allContexts.concat(context.allContexts.toArray());
          }
        }
        return allContexts;
      },

      /**
       * Gets the root context of a context, entity or type.
       * @param object
       */
      getRootContext: function (
        object: sap.cdw.commonmodel.Context | sap.cdw.commonmodel.Entity | sap.cdw.commonmodel.BaseType
      ): sap.cdw.commonmodel.Context {
        let rootContext: sap.cdw.commonmodel.Context;
        if (object) {
          let context: sap.cdw.commonmodel.Context;
          if (ObjectInstanceTypes.isContext(object)) {
            context = object as sap.cdw.commonmodel.Context;
          } else if (
            ObjectInstanceTypes.isEntity(object) ||
            ObjectInstanceTypes.isTable(object) ||
            ObjectInstanceTypes.isView(object)
          ) {
            context = (object as sap.cdw.commonmodel.Entity).context;
          } else if (ObjectInstanceTypes.isType(object)) {
            context = (object as sap.cdw.commonmodel.BaseType).context;
          }
          rootContext = context;
          while (rootContext && rootContext.parentContext) {
            if (!rootContext.parentContext.isContext) {
              break;
            }
            rootContext = rootContext.parentContext;
          }
        }
        return rootContext;
      },

      /**
       * Attaches an object to a context if the object has qualified name and the qualifier is a context.
       * @param model The model.
       * @param object The model (Entity, SimpleType, ...)
       * @param bRemoveQualifier
       * @param contextName if contextName is provided, we force to attach object to context
       */
      attachObjectToContext: function (
        model: sap.cdw.commonmodel.Model,
        object: sap.cdw.commonmodel.Entity,
        bRemoveQualifier: boolean = false,
        contextName?: string
      ) {
        let context;
        if (model && object) {
          if (contextName || ObjectInstanceTypes.isEntity(object) || ObjectInstanceTypes.isType(object)) {
            // Get context
            const name = object.name;
            if (contextName || name.includes(".")) {
              let bFirstQualifier = false;
              contextName = contextName || nsLocal.ObjectImpl.getQualifierName(name, bFirstQualifier);
              context = nsLocal.ModelImpl.getContext(contextName, model);
              if (!context) {
                // Try the first qualifier
                bFirstQualifier = true;
                contextName = nsLocal.ObjectImpl.getQualifierName(name, bFirstQualifier);
                context = nsLocal.ModelImpl.getContext(contextName, model);
              }
              if (context) {
                if (bRemoveQualifier) {
                  object.name = nsLocal.ObjectImpl.computeEntityShortName(object, bFirstQualifier);
                }
                (object as any).context = context;
              }
            }
          }
        }
        return context;
      },

      /**
       * Gets all the base types. For a type, it does not include itself.
       * @param object A type or an element.
       */
      getAllBaseTypes: function (
        object: sap.cdw.commonmodel.BaseType | sap.cdw.commonmodel.Element
      ): sap.galilei.model.BaseCollection<sap.cdw.commonmodel.BaseType> {
        const allTypes = new sap.galilei.model.ArrayCollection();
        if (object) {
          let type: sap.cdw.commonmodel.BaseType;
          if (ObjectInstanceTypes.isType(object)) {
            type = object as sap.cdw.commonmodel.BaseType;
          } else if (ObjectInstanceTypes.isElement(object)) {
            type = (object as sap.cdw.commonmodel.Element).baseType;
            if (type) {
              allTypes.push(type);
            }
          }
          if (type) {
            let rootType = type.baseType;
            while (rootType) {
              if (!rootType.isType) {
                break;
              }
              allTypes.insert(0, rootType);
              rootType = rootType.baseType;
            }
          }
        }
        return allTypes;
      },

      /**
       * Gets the root type of a type or an element.
       * @param object A type or an element.
       */
      getRootType: function (
        object: sap.cdw.commonmodel.BaseType | sap.cdw.commonmodel.Element
      ): sap.cdw.commonmodel.BaseType {
        let rootType: sap.cdw.commonmodel.BaseType;
        if (object) {
          let type: sap.cdw.commonmodel.BaseType;
          if (ObjectInstanceTypes.isType(object)) {
            type = object as sap.cdw.commonmodel.BaseType;
          } else if (ObjectInstanceTypes.isElement(object)) {
            type = (object as sap.cdw.commonmodel.Element).baseType;
          }
          rootType = type;
          while (rootType && rootType.baseType) {
            if (!rootType.baseType.isType) {
              break;
            }
            rootType = rootType.baseType;
          }
        }
        return rootType;
      },

      /**
       * Attach a Type (SimpleType) to a SimpleType or Element if the dataType is changed.
       * @param dataTypeObject SimpleType or Element.
       * @param dataType
       * @param cascadeProperties
       */
      updateObjectBaseType: function (
        dataTypeObject: sap.cdw.commonmodel.SimpleType | sap.cdw.commonmodel.Element,
        dataType?: string,
        cascadeProperties = false
      ) {
        if (dataTypeObject) {
          const model = dataTypeObject.rootContainer as any;
          if (model && dataTypeObject) {
            dataType = dataType || dataTypeObject.dataType;
            let baseType;
            if (dataType && !dataType.startsWith("cds.")) {
              baseType = nsLocal.ModelImpl.getOrCreateSimpleType(dataType, model);
            }
            if (dataType !== dataTypeObject.dataType || baseType !== dataTypeObject.baseType) {
              if (dataType !== dataTypeObject.dataType) {
                dataTypeObject.dataType = dataType;
              }
              if (baseType !== dataTypeObject.baseType) {
                if (dataTypeObject.baseType && cascadeProperties) {
                  // Remove old type properties
                  nsLocal.ObjectImpl.cascadeDetachTypeFromObject(dataTypeObject);
                }
                dataTypeObject.baseType = baseType;
                if (baseType && cascadeProperties) {
                  // Cascade type properties
                  nsLocal.ObjectImpl.cascadeAttachTypeToObject(dataTypeObject);
                }
              }
            }
          }
        }
      },

      /**
       * When simple type property is changed, cascade the change to dependent simple types and elements.
       * @param simpleType
       * @param property
       * @param oldValue
       * @param newValue
       */
      cascadeChangeSimpleTypeProperty: function (
        simpleType: sap.cdw.commonmodel.SimpleType,
        property: string,
        oldValue: any,
        newValue: any
      ) {
        const allowedProperties = ["name", "length", "precision", "scale", "srid"];

        if (simpleType && property && allowedProperties.includes(property)) {
          // Cascade change dependent simple types
          for (const dependentType of simpleType.types.toArray()) {
            if (ObjectInstanceTypes.isSimpleType(dependentType)) {
              if (property === "name") {
                (dependentType as sap.cdw.commonmodel.SimpleType).dataType = newValue;
              } else {
                if (dependentType[property] === oldValue) {
                  dependentType[property] = newValue;
                  // Cascade change dependent simple types
                  nsLocal.ObjectImpl.cascadeChangeSimpleTypeProperty(dependentType, oldValue, newValue);
                }
              }
            }
          }

          // Cascade change dependent elements
          for (const dependentElement of simpleType.elements.toArray()) {
            if (property === "name") {
              dependentElement.dataType = newValue;
            } else {
              if (dependentElement[property] === oldValue) {
                dependentElement[property] = newValue;
              }
            }
          }
        }
      },

      /**
       * Attaches a baseType to a Simple Type or Element.
       * @param dataTypeObject Simple Type or Element.
       * @param baseType The base type.
       */
      cascadeAttachTypeToObject: function (
        dataTypeObject: sap.cdw.commonmodel.SimpleType | sap.cdw.commonmodel.Element,
        baseType?: sap.cdw.commonmodel.BaseType
      ) {
        if (dataTypeObject) {
          baseType = baseType || dataTypeObject.baseType;
          if (baseType && ObjectInstanceTypes.isSimpleType(baseType)) {
            // Copy base type properties
            const baseSimpleType = baseType as sap.cdw.commonmodel.SimpleType;
            dataTypeObject.length = baseSimpleType.length;
            dataTypeObject.precision = baseSimpleType.precision;
            dataTypeObject.scale = baseSimpleType.scale;
            dataTypeObject.srid = baseSimpleType.srid;
          }
          // Set baseType
          if (dataTypeObject.baseType !== baseType) {
            dataTypeObject.baseType = baseType;
          }
        }
      },

      /**
       * Detaches a baseType from a Simple Type or Element.
       * @param dataTypeObject Simple Type or Element.
       * @param baseType The base type.
       */
      cascadeDetachTypeFromObject: function (
        dataTypeObject: sap.cdw.commonmodel.SimpleType | sap.cdw.commonmodel.Element,
        baseType?: sap.cdw.commonmodel.BaseType
      ) {
        if (dataTypeObject) {
          baseType = baseType || dataTypeObject.baseType;
          if (baseType) {
            // Restore the dataType to the original CDS type of root type.
            const rootType = baseType.rootType;
            if (rootType && ObjectInstanceTypes.isSimpleType(rootType)) {
              const rootSimpleType = rootType as sap.cdw.commonmodel.SimpleType;
              if (dataTypeObject.dataType === rootSimpleType.name) {
                dataTypeObject.dataType = rootSimpleType.dataType;
                dataTypeObject.length = rootSimpleType.length;
                dataTypeObject.precision = rootSimpleType.precision;
                dataTypeObject.scale = rootSimpleType.scale;
                dataTypeObject.srid = rootSimpleType.srid;
              }
            }
          }
          // Remove baseType
          if (dataTypeObject.baseType) {
            dataTypeObject.baseType = undefined;
          }
        }
      },

      /**
       * Attaches elements of an entity to base types.
       * @param entity The Entity, Output, ...
       */
      attachElementsToBaseTypes: function (entity: any) {
        if (entity && entity.elements) {
          for (const element of entity.elements.toArray()) {
            nsLocal.ObjectImpl.updateObjectBaseType(element, undefined, false);
            const baseType = element.baseType;
            if (baseType && ObjectInstanceTypes.isSimpleType(baseType)) {
              if (!element.length && baseType.length) {
                element.length = baseType.length;
              }
              if (!element.precision && baseType.precision) {
                element.precision = baseType.precision;
              }
              if (!element.scale && baseType.scale) {
                element.scale = baseType.scale;
              }
              if (!element.srid && baseType.srid) {
                element.srid = baseType.srid;
              }
            }
          }
        }
      },

      /**
       * Attaches simple types and elements of entities to base types.
       * @param model The model
       */
      attachObjectsToBaseTypes: function (model: sap.cdw.commonmodel.Model) {
        if (model) {
          if (model.entities) {
            for (const entity of model.entities.toArray()) {
              nsLocal.ObjectImpl.attachElementsToBaseTypes(entity);
            }
          }
          if (model.simpleTypes) {
            for (const simpleType of model.simpleTypes.toArray()) {
              nsLocal.ObjectImpl.updateObjectBaseType(simpleType, undefined, false);
            }
          }
        }
      },

      /**
       * Copies cross space entity properties from context.
       * @param entity
       * @param context
       */
      setEntityCrossSpaceProperties: function (entity: sap.cdw.commonmodel.Entity) {
        const context = entity && entity.rootContext;
        if (entity && context && context.isCrossSpace) {
          entity.crossSpace = {
            spaceName: context.crossSpace.space || context.name,
            spaceLabel: context.label,
          };
          entity.isPersistenceExists = true;
        }
      },

      /**
       * Gets the local entity name in the cross space.
       * @param entity
       */
      getCrossSpaceEntityName: function (entity: sap.cdw.commonmodel.Entity): string {
        let objectName;
        if (entity && entity.isCrossSpace && entity.name) {
          if (entity.name.startsWith(entity.crossSpaceName + ".")) {
            const index = entity.crossSpaceName.length + 1;
            objectName = entity.name.substring(index);
          } else {
            objectName = entity.name;
          }
        }
        return objectName;
      },

      /**
       * Gets the space name and entity name of an entity.
       * If the entity is a cross space entity, the space will be the source and the object name will be the local entity name.
       * @param entity
       * @returns {
       *   spaceName,
       *   objectName
       * }
       */
      getSpaceAndObjectNames: function (entity: sap.cdw.commonmodel.Entity): {
        spaceName?: string;
        objectName?: string;
      } {
        let spaceName;
        let objectName;
        if (entity) {
          if (entity.isCrossSpace) {
            spaceName = entity.crossSpaceName;
            objectName = entity.crossSpaceEntityName;
          } else {
            spaceName = CurrentContextService.getInstance().getCurrentSpaceId();
            objectName = entity.name;
          }
        }
        return {
          spaceName,
          objectName,
        };
      },

      /**
       * Compute context qualified name.
       * @param context
       */
      computeContextQualifiedName: function (context: sap.cdw.commonmodel.Context): string {
        if (context) {
          const sanitizeQualifiedName = nsLocal.ObjectImpl.sanitizeQualifiedName(context.name);
          if (context.parentContext && context.parentContext.isContext) {
            return `${context.parentContext.qualifiedName}.${sanitizeQualifiedName}`;
          }
          return sanitizeQualifiedName;
        }
        return "";
      },

      /**
       * Compute the position (starts with 1) of the entity in the model.entities collection
       * taking only into account other entities
       * @param {Entity} entity
       */
      computeEntityIndex: function (entity: sap.cdw.commonmodel.Entity): number {
        const oEntities = entity && entity.container && (entity.container as any).entities;
        let nEntityIndex = oEntities && oEntities.indexOf(entity);
        if (oEntities) {
          nEntityIndex = nEntityIndex >= 0 ? nEntityIndex + 1 : 0;
        }
        return nEntityIndex;
      },

      /**
       * Computes entity qualified name.
       * @param {Entity} entity
       */
      computeEntityQualifiedName: function (entity: sap.cdw.commonmodel.Entity | sap.cdw.commonmodel.BaseType): string {
        let sQualifiedName = "";
        const entityName = getEntityName(entity);

        if (entityName) {
          // Escape invalid characters
          const sEntityName = nsLocal.ObjectImpl.sanitizeQualifiedName(entityName);
          if (entity.context && !sEntityName.includes(".")) {
            sQualifiedName = `${entity.context.qualifiedName}.${sEntityName}`;
          } else {
            sQualifiedName = sEntityName;
          }
        }

        return sQualifiedName;
      },

      /**
       * Computes entity short name.
       * @param {Entity} entity
       */
      computeEntityShortName: function (entity: sap.cdw.commonmodel.Entity, bFirstQualifier = false): string {
        return nsLocal.ObjectImpl.getShortName(entity.technicalName, bFirstQualifier);
      },

      /**
       * Gets the qualifier name.
       * @param qualifiedName
       */
      getQualifierName: function (qualifiedName: string, bFirstQualifier = false): string {
        let qualifierName: string;
        if (qualifiedName) {
          const nIndex = bFirstQualifier ? qualifiedName.indexOf(".") : qualifiedName.lastIndexOf(".");
          if (nIndex > 0) {
            qualifierName = qualifiedName.substring(0, nIndex);
          }
        }
        return qualifierName;
      },

      /**
       * Gets the short name without qualifier.
       * @param qualifiedName
       */
      removeQualifierInName: function (qualifiedName: string, bFirstQualifier = false): string {
        let sShortName = qualifiedName;
        if (sShortName) {
          // Keep only last part of qualified name
          const nIndex = bFirstQualifier ? sShortName.indexOf(".") : sShortName.lastIndexOf(".");
          if (nIndex > 0) {
            sShortName = sShortName.substring(nIndex + 1);
          }
          // Remove quotes
          sShortName = nsLocal.ObjectImpl.sanitizeQualifiedName(sShortName);
        }
        return sShortName;
      },

      /**
       * Gets the short name without qualifier.
       * @param qualifiedName
       */
      getShortName: function (qualifiedName: string): string {
        return nsLocal.ObjectImpl.removeQualifierInName(qualifiedName, /* bFirstQualifier */ false);
      },

      /**
       * Gets the short name  based on the allowed maximimum limit
       * @param qualifiedName
       * @param maximum length allowed
       */
      getMaxLengthText: function (name: string, maxLength: number): string {
        return name.slice(0, maxLength);
      },

      /**
       *
       * @param {*} inString
       */
      sanitizeQualifiedName: function (inString: string): string {
        let outString = inString;

        outString = inString.replace(/"/g, "");
        // outString = outString.replace(/[^a-zA-Z0-9_.$#]/g, "_");
        // outString = outString.replace(/_+/g, "_");

        return outString;
      },

      /**
       * Get the entity BusinessName from csn and i18n section
       * @param {*} object entity object
       */
      getBusinessName: function (object: any): string {
        let result = "";
        const definitions = object[object.documentPart || "definitions"];
        const i18nSection = object?.i18n;
        for (const key in definitions) {
          const definition = definitions[key];
          result = definition && (definition["@EndUserText.label"] || definition["@Common.Label"]);
          // "object" is analysed result for @sap/deepsea-document-analysis-wrapper,
          // so i18n section has two potential values: undefined or object with only one key(sourceLanguage name)
          // undefined is caused by sourceLanguage  node is not contains in analysed CSN
          if (i18nSection) {
            // when i18nSection exist, the "@endUserText.label" should be i18n key when i18nSection existing
            // so we need replace the i18n key with translated text,
            // at this time i18n key like "{i18n>XXXXXXXXXX}"
            const currentLang = i18nSection[Object.keys(i18nSection)[0]];
            // get translated text between "{i18n>" and "}"
            const translatedText = result.substring(6, result.length - 1);
            // if the translated text cannot be found then use i18n key
            result = currentLang && currentLang[translatedText] ? currentLang[translatedText] : result;
          }
          if (result) {
            break;
          }
        }
        return result;
      },

      /**
       * Gets the possible data types for an element.
       * @param element The element.
       */
      getPossibleDataTypes(element: any): any[] {
        return getElementPossibleDataTypes(element);
      },

      /**
       * Get the possible filter operations for an element
       * @param element filterItem
       */
      getPossibleFilterOperations(element: any) {
        const result = [];
        if (element.allowedExpressions !== undefined) {
          if (element.dataType !== CDSDataType.TIMESTAMP) {
            element.allowedExpressions.forEach(function (operation) {
              if (operation["#"] === "EQUAL") {
                result.push({ key: "EQ", text: "Equal to" });
              } else if (operation["#"] === "BETWEEN") {
                result.push({ key: "BT", text: "Between" });
              }
            });
          } else {
            result.push({ key: "BT", text: "Between" });
          }
        } else if (element.dataType === CDSDataType.TIMESTAMP) {
          result.push({ key: "BT", text: "Between" });
        } else {
          result.push({ key: "EQ", text: "Equal to" });
          result.push({ key: "BT", text: "Between" });
        }
        return result;
      },

      isCDIadapter(entity) {
        return (
          entity.adapter === "CloudDataIntegrationAdapter" ||
          entity.adapter === "ABAPAdapter" ||
          entity.adapter === "ODataAdapter"
        );
      },

      isFilterableColumn(element) {
        return element.filterEnabled === undefined;
      },

      /**
       * Gets the possible srid values for an element.
       * @param element The element.
       */
      getPossibleSridValues(element: any): any[] {
        return SRID_VALUES;
      },

      /**
       * Gets the possible cardinality values
       */
      getPossibleCardinalityValues(): any[] {
        return possibleCardinalityValues;
      },

      /**
       * Gets the supported semantic types for the element data type.
       * @param {string} dataType: The data type of the element.
       * @param {boolean} isMeasure: Indicates whether the element is a measure.
       */
      getSemanticTypesForDataType: function (dataType: string | undefined, object: any): any[] {
        if (!dataType || !object) {
          return [];
        }

        let semanticTypeList: any = AttributesSemanticTypes;
        const isContainerDimension = object?.container?.isDimension;
        // only keep this new semanticType for the scenario that container semantic type is dimension
        if (!isContainerDimension) {
          const imageUrlIndex = semanticTypeList.indexOf("@Semantics.imageUrl");
          const clonedSemanticTypeList = [...semanticTypeList];
          clonedSemanticTypeList.splice(imageUrlIndex, 1);
          semanticTypeList = clonedSemanticTypeList;
        }

        const mapping = object.isMeasure
          ? MeasureSemanticTypes
          : object.isTextEntity
          ? TextSemanticTypes
          : semanticTypeList;
        let semanticTypes = [SemanticType.EMPTY, ...mapping];

        if (!SupportedFeaturesService.getInstance().isFiscalTimeDimensionEnabled()) {
          // four new fiscal time related semantic types already added in cds.types.ts file,
          // when remove ff, just delete this if block
          semanticTypes = semanticTypes.filter((semanticType) => {
            if (
              ["@Semantics.fiscal.quarter", "@Semantics.fiscal.yearQuarter", "@Semantics.fiscal.yearWeek"].includes(
                semanticType
              )
            ) {
              return false;
            }
            return true;
          });
        }

        semanticTypeList = semanticTypes.map((semanticType) => ({
          key: semanticType,
          text: nsLocal.ObjectImpl.getSemanticTypesText(semanticType),
        }));
        if (SupportedFeaturesService.getInstance().isGenAISemanticEnabled()) {
          // && object.hasAIChangeOnSemanticType
          const resourceBundle = nsLocal.ObjectImpl.getResourceBundle();
          const txtRecommendations = resourceBundle.getText("@recommendations");
          const txtOther = resourceBundle.getText("@other");
          const semanticTypeAI = object.semanticTypeAI;

          const semanticTypeAIList = semanticTypeList.map((semanticType) => ({
            key: semanticType.key,
            text: semanticType.text,
            recommended: semanticTypeAI === semanticType.key,
            groupName: semanticTypeAI === semanticType.key ? txtRecommendations : txtOther,
          }));

          return semanticTypeAIList;
        } else {
          return semanticTypeList;
        }
      },

      getPossibleLabelColumnsAI: function (object: any): any[] {
        const possibleLabelColumns: any[] = object.possibleLabelColumns;
        const possibleLabelColumnsAI = [];

        if (SupportedFeaturesService.getInstance().isGenAISemanticEnabled()) {
          const resourceBundle = nsLocal.ObjectImpl.getResourceBundle();
          const txtRecommendations = resourceBundle.getText("@recommendations");
          const txtOther = resourceBundle.getText("@other");
          const labelElementAI = object.labelElementAI;

          const recommendedGroup = [];
          const otherGroup = [];

          possibleLabelColumns.forEach((labelColumn) => {
            if (labelColumn?.classDefinition?.name === "Element") {
              const item = {
                key: labelColumnKeyFormatter(labelColumn),
                text: labelColumn.displayName,
                isGroup: false,
                icon: labelColumnIconFormatter(labelColumn),
                rightIcon: labelColumnTIconFormatter(labelColumn),
              };
              if (labelElementAI === (labelColumn.newName || labelColumn.name)) {
                recommendedGroup.push(item);
              } else {
                otherGroup.push(item);
              }
            } else if (labelColumn?.classDefinition?.name === "Association") {
              const item = {
                key: labelColumnKeyFormatter(labelColumn),
                text: labelColumn.displayName,
                isGroup: false,
                icon: labelColumnIconFormatter(labelColumn),
                rightIcon: labelColumnTIconFormatter(labelColumn),
              };
              otherGroup.push(item);
            } else {
              // empty case
              const item = {
                text: labelColumn.displayName,
                key: labelColumnKeyFormatter(labelColumn),
                isGroup: false,
                icon: "",
                rightIcon: "",
              };
              if (labelElementAI === labelColumn.name) {
                recommendedGroup.push(item);
              } else {
                otherGroup.push(item);
              }
            }
            return labelColumn;
          });

          if (recommendedGroup.length > 0) {
            recommendedGroup.unshift({
              key: "recommendations",
              text: txtRecommendations,
              isGroup: true,
            });
          }
          if (recommendedGroup.length > 0 && otherGroup.length > 0) {
            otherGroup.unshift({
              key: "other",
              text: txtOther,
              isGroup: true,
            });
          }

          possibleLabelColumnsAI.push(...recommendedGroup, ...otherGroup);
        }
        return possibleLabelColumnsAI;
      },

      /**
       * Gets the semantic type display name.
       * @param semanticType The semantic type.
       */
      getSemanticTypesText(semanticType: SemanticType) {
        const resourceBundle = nsLocal.ObjectImpl.getResourceBundle();

        if (semanticType === SemanticType.EMPTY) {
          return resourceBundle.getText("@none");
        } else {
          return resourceBundle.getText(semanticType);
        }
      },

      /**
       * Computes the primtive CDS data type.
       * @param {*} element
       */
      computePrimitiveDataType: function (
        elementOrSimpleType: sap.cdw.commonmodel.Element | sap.cdw.commonmodel.SimpleType
      ): string {
        if (!elementOrSimpleType) {
          return;
        }
        let dataType = elementOrSimpleType.dataType;
        const rootType = elementOrSimpleType.rootType;
        if (rootType && ObjectInstanceTypes.isSimpleType(rootType)) {
          dataType = (rootType as sap.cdw.commonmodel.SimpleType).dataType;
        } else {
          // Check if it is a simple type and if it exists in availableSimpleTypes loaded from repository
          if (
            dataType &&
            !dataType.startsWith("cds.") &&
            (elementOrSimpleType.rootContainer as any).availableSimpleTypes
          ) {
            const SimpleTypeDefinitions = {};
            for (const simpleTypeDefinition of (elementOrSimpleType.rootContainer as any).availableSimpleTypes) {
              const csnType =
                simpleTypeDefinition.csn && simpleTypeDefinition.csn.definitions[simpleTypeDefinition.name];
              SimpleTypeDefinitions[simpleTypeDefinition.name] = {
                dataType: csnType?.type,
              };
            }
            while (dataType && !dataType.startsWith("cds.")) {
              const baseSimpleType = SimpleTypeDefinitions[dataType];
              if (!baseSimpleType) {
                break;
              }
              dataType = baseSimpleType.dataType;
            }
          }
        }
        return dataType;
      },

      /**
       * Computes the display type from element CSN
       * @param {elementCSN} the element CSN
       * @param {resource} the model resource used to get current simple types
       */
      computeDisplayTypeFromCSN: function (elementCSN: any, resource: any) {
        const tmpElt = {
          dataType: elementCSN.type,
          length: elementCSN.length,
          precision: elementCSN.precision,
          scale: elementCSN.scale,
          srid: elementCSN.srid,
        };
        return nsLocal.ObjectImpl.computeDisplayType(tmpElt, /* dataType*/ undefined, resource.model);
      },

      /**
       * Computes the display data type combining dataType, length, precision, scale, srid.
       * @param {*} element
       * @param {string} dataType Data type if different than the object dataType.
       */
      computeDisplayType: function (
        elementOrSimpleType: sap.cdw.commonmodel.Element | sap.cdw.commonmodel.SimpleType,
        dataType?: string,
        simpleTypesContainer?: any
      ): string {
        if (!elementOrSimpleType) {
          return;
        }

        const allCDSDataTypeDefinitions = getCDSDataTypeDefinitions(elementOrSimpleType, simpleTypesContainer);
        dataType = dataType || elementOrSimpleType.dataType;
        let primitiveDataType;
        let typeText;
        if (allCDSDataTypeDefinitions[dataType]) {
          primitiveDataType = allCDSDataTypeDefinitions[dataType].primitiveDataType || dataType;
          typeText = allCDSDataTypeDefinitions[dataType].uiKey;
        } else {
          primitiveDataType = elementOrSimpleType.primitiveDataType;
          typeText = dataType;
        }

        switch (primitiveDataType) {
          case CDSDataType.DECIMAL:
            if (elementOrSimpleType.precision !== undefined && elementOrSimpleType.precision !== 0) {
              return elementOrSimpleType.scale !== undefined
                ? `${typeText}(${elementOrSimpleType.precision}, ${elementOrSimpleType.scale})`
                : `${typeText}(${elementOrSimpleType.precision})`;
            } else {
              return typeText;
            }
          case CDSDataType.STRING:
          case CDSDataType.HANA_NCHAR:
          case CDSDataType.BINARY:
          case CDSDataType.HANA_BINARY:
            if (elementOrSimpleType.length === undefined) {
              return typeText;
            }
            return `${typeText}(${elementOrSimpleType.length})`;
          case CDSDataType.HANA_ST_POINT:
          case CDSDataType.HANA_ST_GEOMETRY:
            if (elementOrSimpleType.srid === undefined) {
              return typeText;
            }
            return `${typeText}(${elementOrSimpleType.srid})`;
          default:
            if (!allCDSDataTypeDefinitions[dataType]) {
              // Could not find the type definition
              if (elementOrSimpleType.length !== undefined && elementOrSimpleType.length !== 0) {
                return `${typeText}(${elementOrSimpleType.length})`;
              } else if (elementOrSimpleType.precision !== undefined && elementOrSimpleType.precision !== 0) {
                return elementOrSimpleType.scale !== undefined
                  ? `${typeText}(${elementOrSimpleType.precision}, ${elementOrSimpleType.scale})`
                  : `${typeText}(${elementOrSimpleType.precision})`;
              }
            }
            return typeText;
        }
      },

      /**
       * Computes the primitive CDS display data type combining dataType, length, precision, scale, srid.
       * @param {*} element
       */
      computePrimitiveDisplayType: function (
        elementOrSimpleType: sap.cdw.commonmodel.Element | sap.cdw.commonmodel.SimpleType
      ): string {
        if (!elementOrSimpleType) {
          return;
        }
        let dataType = elementOrSimpleType.dataType;
        const rootType = elementOrSimpleType.rootType;
        if (rootType && ObjectInstanceTypes.isSimpleType(rootType)) {
          dataType = (rootType as sap.cdw.commonmodel.SimpleType).dataType;
        }
        return nsLocal.ObjectImpl.computeDisplayType(elementOrSimpleType, dataType);
      },

      /**
       * Computes the entity remote host from csn
       * @param {*} definitions entity definitions
       */
      computeRemoteHost: function (definitions) {
        let result = "";
        for (const key in definitions) {
          const definition = definitions[key];
          result = definition && (definition["@DataWarehouse.remote.connection"] || definition["@remote.source"]); // TODO: Remove @remote.source later.
          if (result) {
            break;
          }
        }
        return result;
      },

      /**
       * Computes the entity remote table from csn
       * @param {*} definitions entity definitions
       */
      computeRemoteTable: function (definitions) {
        let result = "";
        for (const key in definitions) {
          const definition = definitions[key];
          result =
            definition &&
            (definition["@DataWarehouse.remote.entity"] ||
              definition["@DataWarehouse.remote.table"] ||
              definition["@remote.table"]); // TODO: Remove @DataWarehouse.remote.table, @remote.table later.
          if (result) {
            break;
          }
        }
        return result;
      },

      /**
       * Get aggregation types key and label.
       */
      getAggregationDisplayTypes(): any[] {
        return AggregationDisplayTypes;
      },

      /**
       * Get aggregation types key and label.
       */
      getExceptionAggregationDisplayTypes(): any[] {
        return ExceptionAggregationDisplayTypes;
      },
      /**
       * Get aggregation types key and label for source aggregation types of exception aggregation.
       */
      getExceptionAggregationSourceTypes(): any[] {
        return ExceptionAggregationSupportedSourceTypes;
      },
      /**
       * Checks if the element's data type is a supported data type for analytic measure attributes (Exception aggregation & Count Distinct).
       * @param oElement
       */
      checkAnalyticMeasureAttributesAllowedDataTypes(oElement?: any): boolean {
        if (analyticMeasureUnsupportedDataTypes.includes(oElement.primitiveDataType)) {
          return false;
        } else {
          return true;
        }
      },

      /**
       * Determines the column is used for partitioning or not
       */
      isPartitionedColumn(element): any {
        if (element?.container?.isLTF === true) {
          if (element?.container?.partitionedColumns?.length === 0) {
            return false;
          } else if (element?.container?.partitionedColumns?.includes(element.name)) {
            return true;
          }
        } else {
          if (isHashPartitioningEnabled() && element?.container?.partitions?.get(0)?.hashedColumns?.length > 0) {
            let isPartitionedCol = false;
            element?.container?.partitions?.get(0)?.hashedColumns?.forEach((hashedColumn) => {
              if (hashedColumn.name === element.name) {
                isPartitionedCol = isPartitionedCol || true;
              }
            });
            return isPartitionedCol;
          } else {
            const partitionedColName = element?.container?.partitions?.get(0)?.id;
            if (element.name === partitionedColName) {
              return true;
            }
          }
        }
        return false;
      },

      /**
       * Determines the table has nokey columns and partition exists
       */
      hasNoKeyWithPartitions(entity): any {
        let hasNoKeyWithPartitions = false;
        const elements = entity?.orderedElements;
        const partitionExists = entity?.partitions?.length > 0 ? true : false;
        let tableHasKey = false;
        for (let i = 0; i < elements?.length; i++) {
          if (elements[i].isKey === true) {
            tableHasKey = true;
            break;
          }
        }
        if (!tableHasKey && partitionExists) {
          hasNoKeyWithPartitions = true;
        }
        return hasNoKeyWithPartitions;
      },

      /**
       * Update a restrictied measure expression by changing the source measure
       * @param {*} restrictedMeasure the restricted measure
       * @param {*} sourceMeasure The name of the new base measure
       */
      updateRestrictedMeasureParsedExpression: function (restrictedMeasure, sourceMeasure) {
        // _DP_ Update Restricted measure expression
        const parsedExpression = restrictedMeasure?.parsedExpression?.expression?.xpr;
        if (parsedExpression && Array.isArray(parsedExpression) && sourceMeasure) {
          const expressionLength = parsedExpression.length;
          if (
            expressionLength > 6 &&
            parsedExpression[expressionLength - 3] === "then" &&
            parsedExpression[expressionLength - 2].ref &&
            parsedExpression[expressionLength - 1] === "end"
          ) {
            parsedExpression[expressionLength - 2].refElement = sourceMeasure;
            parsedExpression[expressionLength - 2].ref = sourceMeasure.newName;
          }
        }
      },

      /**
       * Change data type helper.
       * @param element
       * @param dataType
       */
      changeDataType: function (element: any, dataType: string) {
        const dataTypeDefinitions: any[] = Object.values(getCDSDataTypeDefinitions(element));
        const dataTypeDefinition = dataTypeDefinitions.find((definition) => definition.key === dataType);
        if (!dataTypeDefinition) {
          return;
        }
        const result: IDisplayDataType = { dataType: dataTypeDefinition.key };
        switch (dataType) {
          case CDSDataType.DECIMAL:
            result.precision = element.precision || 5;
            result.scale = element.scale || 2;
            break;
          case CDSDataType.STRING:
          case CDSDataType.BINARY:
            result.length = element.length || 10;
          case CDSDataType.HANA_NCHAR:
          case CDSDataType.HANA_BINARY:
            result.length = element.length || 100;
            break;
          case CDSDataType.HANA_ST_POINT:
          case CDSDataType.HANA_ST_GEOMETRY:
            result.srid = element.srid || DEFAULT_SRID;
        }

        element.resource.applyUndoableAction(() => {
          const bIsLoading = element.resource.isLoading;
          // Detach old simple type
          if (!bIsLoading && element.baseType && element.dataType && element.dataType.startsWith("cds.")) {
            element.baseType = undefined;
          }
          // Disable validation
          element.resource.isLoading = true;
          element.dataType = result.dataType;
          element.length = result.length;
          element.precision = result.precision;
          element.scale = result.scale;
          element.srid = result.srid;
          element.semanticType = "";
          element.default = "";
          // cascade simple type properties
          if (!bIsLoading && element.dataType) {
            sap.cdw.commonmodel.ObjectImpl.updateObjectBaseType(element, undefined, /* cascadeProperties*/ true);
          }
          element.resource.isLoading = bIsLoading;

          // Check element data type, length, precision, scale, srid, default value after changing all the properties
          if (!bIsLoading && element.dataType) {
            nsLocal.Validation.validateDataType(element);
            nsLocal.Validation.validateLength(element);
            nsLocal.Validation.validatePrecision(element, false);
            nsLocal.Validation.validateScale(element, false);
            nsLocal.Validation.validateSrid(element, false);
            nsLocal.Validation.validateDefaultVal(element);
          }
        }, "Change Data Type");
        if (element?.container) {
          nsLocal.ObjectImpl.publishElementsChangeEvent(element?.container);
        }
      },

      setElementForeignAssociation: function (sourceElement: any, association: any, targetElement: any) {
        const associationName = association?.name;
        if (sourceElement && associationName) {
          sourceElement[association?.getForeignAttributeName()] = associationName;
        }

        const targetView = association.target;
        const representativeKeyElement = targetView?.representativeKey;
        if (representativeKeyElement && targetElement && representativeKeyElement !== targetElement) {
          sourceElement[association?.getForeignAttributeName()] = "";
        }
      },

      clearElementForeignAssociation: function (element: any, associationName: string) {
        // Foreign Key?
        if (element?.foreignKey === associationName) {
          element.foreignKey = "";
        }
        // External Hierarchy?
        if (element?.foreignHierarchy === associationName) {
          element.foreignHierarchy = "";
        }
        // Text?
        if (element?.foreignText === associationName) {
          element.foreignText = "";
        }
      },

      /**
       * Gets qualified name array for CSN generation.
       * @param {*} oElement
       */
      getQualifiedNameAsArray: function (
        oElement,
        bUseNewName: boolean = true,
        bUseEntityIndex: boolean = false
      ): any[] {
        const resultArr = [];
        let oSourceElement;
        if (oElement.container && oElement.container._subquery) {
          oSourceElement = oElement;
        } else if (oElement.container?.classDefinition.name === "Union") {
          oSourceElement = oElement;
        } else {
          oSourceElement = oElement && (oElement.sourceElement || oElement);
        }

        const oSourceNode = oSourceElement && oSourceElement.container;
        const sElementName = bUseNewName !== false ? oElement.newName || oElement.name : oElement.name;
        // TODO:
        // const quotedName = sap.cdw.querybuilder.ViewModelToCsn.quoteName(sElementName);
        if (oSourceNode) {
          let alias = oSourceNode.alias || oSourceNode._aliasForCsn || oSourceNode.qualifiedName;
          if (bUseEntityIndex) {
            alias = (oSourceNode && oSourceNode.entityIndex) || alias;
          }
          if (alias !== undefined) {
            resultArr.push(alias);
          }
        }
        resultArr.push(sElementName);
        return resultArr;
      },

      /**
       * Gets all Business definition responsible team of entites.
       * @param {*} oModel
       */
      getTeamsAsArray(oModel: sap.cdw.commonmodel.Model) {
        const teamsArray = [];

        if (oModel.entities) {
          oModel.entities.forEach((entity) => {
            const team = (entity as sap.cdw.commonmodel.Entity).businessDefinitionResponsibleTeam;
            if (!teamsArray.includes(team)) {
              teamsArray.push(team);
            }
          });
        }
        return teamsArray;
      },

      /**
       * Create source element and target element for Managed Association.
       * @param oAssociation - Association
       * @param sElementClassName - The Element class name if different.
       */
      createMangedAssociationElements: function (
        oAssociation: sap.cdw.commonmodel.Association,
        sElementClassName?: string
      ) {
        let bCreateSourceElement;
        if (
          oAssociation.type === AssociationType.MANAGED &&
          oAssociation.maxCardinality === AssociationMaxCardinality.MANY &&
          !oAssociation.targetElement
        ) {
          const targetElement = nsLocal.ObjectImpl.createElementForAssociation(
            false,
            oAssociation.target,
            oAssociation,
            sElementClassName
          );
          oAssociation.targetElement = targetElement;
          bCreateSourceElement = !oAssociation.sourceElement ? true : false;
        }
        if (
          oAssociation.type === AssociationType.MANAGED &&
          oAssociation.maxCardinality !== AssociationMaxCardinality.MANY &&
          !oAssociation.sourceElement
        ) {
          bCreateSourceElement = true;
        }
        if (bCreateSourceElement === true) {
          const sourceElement = nsLocal.ObjectImpl.createElementForAssociation(
            true,
            oAssociation.source,
            oAssociation,
            sElementClassName
          );
          oAssociation.sourceElement = sourceElement;
        }
      },

      /**
       * Create an Element for an Association.
       * @param isSource
       * @param oEntity
       * @param oAssociation
       */
      createElementForAssociation: function (
        isSource: boolean,
        oEntity: sap.cdw.commonmodel.Entity,
        oAssociation: sap.cdw.commonmodel.Association,
        sElementClassName?: string
      ) {
        const name = isSource === false ? oAssociation.source.name : oAssociation.name;
        const oElement = nsLocal.ModelImpl.createObject(
          sElementClassName || CommonQualifiedClassNames.ELEMENT,
          { name: name, dataType: CDSDataType.ASSOCIATION },
          oEntity,
          oEntity.elements.length
        );
        return oElement;
      },

      /**
       * Remove source element and/or target element for Managed Association.
       * @param {*} oAssociation - Association
       */
      removeMangedAssociationElements: function (oAssociation: sap.cdw.commonmodel.Association, bForceRemove) {
        if (oAssociation && oAssociation.sourceElement) {
          // Source Element should be removed if the Association is not Managed
          if (oAssociation.type !== AssociationType.MANAGED || bForceRemove) {
            nsLocal.ObjectImpl.deleteElement(oAssociation.sourceElement);
          }
        }
        if (oAssociation && oAssociation.targetElement) {
          // Target Element should be removed if the Association is not Managed
          // Or if the max cardinality is not "*"
          if (
            oAssociation.type !== AssociationType.MANAGED ||
            oAssociation.maxCardinality !== AssociationMaxCardinality.MANY ||
            bForceRemove
          ) {
            nsLocal.ObjectImpl.deleteElement(oAssociation.targetElement);
          }
        }
      },

      /**
       * Remove Text Association From Element
       * @param entity - Source of Association
       * @param element - Source of mapping in Association
       */
      removeTextAssociationFromElement: function (entity: any, element?: any) {
        const associations = [
          ...entity.sourceAssociations.toArray(),
          ...(entity.unresolvedAssociations ? entity.unresolvedAssociations : []),
        ];
        associations.forEach((oAsso) => {
          if (oAsso?.isText) {
            let canRemove = true;
            if (oAsso.classDefinition && oAsso.classDefinition.name === "Association") {
              if (element) {
                // Check if element is the Source of any mapping in Association
                canRemove = oAsso.mappings.toArray().some((m) => m.source === element);
              }
              if (canRemove) {
                oAsso.deleteObject();
              }
            } else if (oAsso && oAsso.csn && oAsso.source) {
              // Unresolved associations
              if (element) {
                // Check if element is the Source of any mapping in Unresolved Association Csn
                canRemove = oAsso.unresolvedMappings.some((m) => m.source.name === element.name);
              }
              if (canRemove) {
                const index = oAsso.source.resource.model.unresolvedAssociations.findIndex(
                  (obj) => obj.name === oAsso.name
                );
                const aUnresolvedAssociations = oAsso.source.resource.model.unresolvedAssociations;
                const aUnresolved = [...aUnresolvedAssociations];
                aUnresolved.splice(index, 1);
                oAsso.source.resource.model.unresolvedAssociations = aUnresolved;
              }
            }
          }
        });
      },

      /**
       * Remove Element from its container and delete it.
       * @param {*} oElement
       */
      deleteElement: function (oElement: sap.cdw.commonmodel.Element) {
        if (oElement) {
          const elements = ((oElement && oElement.container && oElement.container) as sap.cdw.commonmodel.BaseEntity)
            .elements;
          if (elements) {
            elements.removeAt(elements.indexOf(oElement));
          }
          oElement.deleteObject();
        }
      },

      getForeignAttributeName: function (oAssociation: sap.cdw.commonmodel.Association) {
        return oAssociation?.isHierarchy === true || oAssociation?.isHierarchyWithDirectory === true
          ? "foreignHierarchy"
          : oAssociation?.isText === true
          ? "foreignText"
          : "foreignKey";
      },

      /**
       * Association name is changed.
       * @param oAssociation
       */
      onNameChangeAssociation: function (oAssociation: any, oldName?: string) {
        if (oAssociation?.sourceElement) {
          // Managed associations?
          oAssociation.sourceElement.name = oAssociation.name;
        }
        // Foreign attribute(s)
        if (oAssociation?.mappings.length > 0) {
          oAssociation.mappings.forEach((mapping: any) => {
            if (mapping?.source) {
              const foreignAttributeName = oAssociation.getForeignAttributeName();
              if (oldName && mapping.source[foreignAttributeName] === oldName) {
                mapping.source[foreignAttributeName] = oAssociation.name;
              }
            }
          });

          if (oldName) {
            const sourceEntity = oAssociation.source;
            if (sourceEntity?.hierarchyWithDirectory?.[0]?.directory?.["="] === oldName) {
              sourceEntity.hierarchyWithDirectory[0].directory["="] = oAssociation.name;
            }
          }
        }
      },

      /**
       * Triggered when a join or association is created
       * @param oObject Association or Join node
       */
      onDefaultProposeMappings: function (oObject, targetType?, selectedElement?): void {
        const self = this;
        let tableAElems: any = [];
        let tableBElems: any = [];
        let aTableAElems = [];
        let aTableBElems = [];
        let tableA: any;
        let tableB: any;

        if (oObject.predecessorNodes && oObject.predecessorNodes.length > 1) {
          // TODO: Move graphical view case to View Builder
          // case of graphical view builder
          tableA = oObject.predecessorNodes.get(0);
          tableB = oObject.predecessorNodes.get(1);
        } else {
          tableA = oObject.tableA || oObject.source;
          tableB = oObject.tableB || oObject.target;
        }
        if (tableA && tableB && oObject.mappings && oObject.mappings.length === 0) {
          // No existing join
          tableAElems = tableA.orderedElements?.filter((e) => !e.isRemoved && !e.isCDCColumn); // Should not consider the excluded columns
          tableBElems = tableB.orderedElements?.filter((e) => !e.isRemoved && !e.isCDCColumn);
          aTableAElems = Array.from(tableAElems).map((element: any) => ({
            name: element.name,
            isKey: element.isKey,
            dataType: element.primitiveDataType,
            elementObj: element,
          }));
          aTableBElems = Array.from(tableBElems).map((element: any) => ({
            name: element.name,
            isKey: element.isKey,
            dataType: element.primitiveDataType,
            elementObj: element,
          }));
          const primeKeysTableA = [];
          const primeKeysTableB = [];
          const joinMappings = [];
          let leftElement: string;
          let rightElement: string;

          const toCreateTextAssociation = targetType === DataCategory.TEXT && selectedElement;
          if (toCreateTextAssociation) {
            // Only map from selected element to the 1st key element with type None
            let firstNoneKey;
            for (const i in aTableBElems) {
              if (
                aTableBElems[i]?.elementObj?.isKey &&
                aTableBElems[i]?.elementObj?.semanticType === SemanticType.EMPTY
              ) {
                firstNoneKey = aTableBElems[i].elementObj;
                break;
              }
            }
            if (firstNoneKey) {
              leftElement = selectedElement;
              rightElement = firstNoneKey;
              joinMappings.push({
                leftElement,
                rightElement,
              });
            }
            aTableAElems = [];
            aTableBElems = [];
          }

          // To handle multiple primary keys in entity
          for (const el in aTableAElems) {
            if (aTableAElems[el].isKey === true) {
              primeKeysTableA.push(aTableAElems[el]);
            }
          }
          for (const element in aTableBElems) {
            if (aTableBElems[element].isKey === true) {
              primeKeysTableB.push(aTableBElems[element]);
            }
          }

          // Rule 1 : Propose Mappings by entity With primary keys mapped to foreign keys in another entity
          if (primeKeysTableB.length > 0) {
            for (let i = 0; i < primeKeysTableB.length; i++) {
              const primeElement = primeKeysTableB[i];
              const compNameB = primeElement.name;
              let isColumnAvailable = aTableAElems.findIndex((tableElement) => tableElement.name === compNameB);
              if (isColumnAvailable === -1) {
                // Ignoring special character and case sensitivity
                isColumnAvailable = aTableAElems.findIndex(
                  (j) =>
                    this.ignoreSpecialCharacters(j.name).toUpperCase() ===
                    this.ignoreSpecialCharacters(compNameB).toUpperCase()
                );
              }
              // check for dataType compatibility before creating mapping
              if (isColumnAvailable !== -1 && aTableAElems[isColumnAvailable].dataType === primeElement.dataType) {
                rightElement = primeElement.elementObj;
                leftElement = aTableAElems[isColumnAvailable].elementObj;
                joinMappings.push({
                  leftElement,
                  rightElement,
                });
              }
            }
          }
          if (joinMappings.length === 0 && primeKeysTableA.length > 0) {
            for (let i = 0; i < primeKeysTableA.length; i++) {
              const primeElement = primeKeysTableA[i];
              const compNameA = primeElement.name;
              let isColumnAvailable = aTableBElems.findIndex((j) => j.name === compNameA);
              if (isColumnAvailable === -1) {
                // Ignoring special character and case sensitivity
                isColumnAvailable = aTableBElems.findIndex(
                  (j) =>
                    this.ignoreSpecialCharacters(j.name).toUpperCase() ===
                    this.ignoreSpecialCharacters(compNameA).toUpperCase()
                );
              }
              // check for dataType compatibility before creating mapping
              if (isColumnAvailable !== -1 && aTableBElems[isColumnAvailable].dataType === primeElement.dataType) {
                leftElement = primeElement.elementObj;
                rightElement = aTableBElems[isColumnAvailable].elementObj;
                joinMappings.push({
                  leftElement,
                  rightElement,
                });
              }
            }
          }
          if (joinMappings.length > 0) {
            oObject.resource.applyUndoableAction(function () {
              joinMappings.forEach((join) => {
                self.createMapping(oObject, join.leftElement, join.rightElement);
              });
            }, "Create Mappings");
            return;
          }

          // Rule2 : Exact name match
          if (joinMappings.length === 0) {
            const aExactNameMatch = [];
            for (let j = 0; j < aTableAElems.length; j++) {
              let index = aTableBElems.findIndex((elementObj) => elementObj.name === aTableAElems[j].name);
              if (index === -1) {
                index = aTableBElems.findIndex(
                  (elementObj) =>
                    this.ignoreSpecialCharacters(elementObj.name).toUpperCase() ===
                    this.ignoreSpecialCharacters(aTableAElems[j].name).toUpperCase()
                );
              }
              if (index !== -1 && aTableAElems[j].dataType === aTableBElems[index].dataType) {
                aExactNameMatch.push({
                  leftElement: aTableAElems[j].elementObj,
                  rightElement: aTableBElems[index].elementObj,
                });
              }
            }
            if (aExactNameMatch.length > 0) {
              oObject.resource.applyUndoableAction(function () {
                self.createMapping(oObject, aExactNameMatch[0].leftElement, aExactNameMatch[0].rightElement);
              }, "Create Mappings");
            }
          }
        }
      },

      /**
       * Ignore special characters in column name.
       * @param colName
       */
      ignoreSpecialCharacters(colName: string): string {
        const columnName = colName.replace(/[^a-z0-9]/gi, "");
        return columnName;
      },

      /**
       * Create mapping between elements.
       */
      createMapping(oAssociation, leftElement, rightElement): void {
        const oClass = sap.galilei.model.getClass(CommonQualifiedClassNames.ELEMENT_MAPPING);
        if (oClass) {
          const oElementMapping = oClass.create(oAssociation.resource, {
            source: leftElement,
            target: rightElement,
          });
          oAssociation.mappings.push(oElementMapping);
          // Set foreign Key
          if (
            leftElement &&
            oAssociation?.classDefinition?.name === CommonShortClassNames.ASSOCIATION &&
            (oAssociation.isHierarchy || oAssociation.isHierarchyWithDirectory)
          ) {
            // Set right foreign attribute
            nsLocal.ObjectImpl.setElementForeignAssociation(leftElement, oAssociation, rightElement);
          }
        }
      },

      /**
       * Triggered when table is deleted,
       * Delete unresolved associations if table is new
       * @param oTable
       */
      onBeforeDeleteTable(oTable: sap.cdw.commonmodel.Table) {
        if (oTable.isNew && oTable.resource) {
          const oModel = oTable.resource.model as any;
          const nLength = oModel && oModel.unresolvedAssociations && oModel.unresolvedAssociations.length;
          for (let nIndex = 0; nIndex < nLength; nIndex++) {
            if (
              oModel.unresolvedAssociations[nIndex].csn &&
              oModel.unresolvedAssociations[nIndex].csn.target === oTable.name
            ) {
              oModel.unresolvedAssociations.splice(nIndex, 1);
            }
          }
        }
        oTable.defaultOnBeforeDelete();
      },

      /**
       * Triggered before Entity is deleted
       * @param oEntity
       */
      onBeforeDeleteEntity(oEntity: sap.cdw.commonmodel.Entity) {
        if (oEntity) {
          const oContext = oEntity.context;
          // Delete empty cross space context
          if (oContext && oContext.isCrossSpace && oContext.entities?.length === 1) {
            oContext.deleteObject();
          }
          oEntity.defaultOnBeforeDelete();
        }
      },

      /**
       * Receive oAssociation notification for cascading deletion,
       * Delete source element and target element for managed association
       * (This is onBeforeDelete of Association)
       * @param association
       */
      onCascadeDeleteAssociation(association: sap.cdw.commonmodel.Association) {
        deleteAssociationInHierarchyWithDirectory(association?.source, association);

        // Delete the foreign key reference for mappings in association
        if (!association?.["_changedToUnresolved"] && association?.mappings?.length > 0) {
          association.mappings.forEach(function (oElementMapping: any) {
            nsLocal.ObjectImpl.clearElementForeignAssociation(oElementMapping?.source, association.name);
          });
        }
        if (association?.["_changedToUnresolved"]) {
          delete association["_changedToUnresolved"];
        }

        // Delete source element and target element
        if (association.sourceElement) {
          nsLocal.ObjectImpl.deleteElement(association.sourceElement);
        }
        if (association.targetElement) {
          nsLocal.ObjectImpl.deleteElement(association.targetElement);
        }
        // Delete association from model
        if (association.resource) {
          const model = association.resource.model as sap.cdw.commonmodel.Model;
          const oModelAssociations = model.associations.toArray();
          if (oModelAssociations.length > 0) {
            const oAssociationToDelete = oModelAssociations.findIndex((obj) => obj.name === association.name);
            if (oAssociationToDelete !== -1) {
              oModelAssociations.splice(oAssociationToDelete, 1);
            }
          }
        }
        association.defaultOnBeforeDelete();
      },

      changeTargetAssociationsToUnresolved(entity: sap.cdw.commonmodel.Entity, aDeleteSymbols?: any[]) {
        if (entity) {
          entity.resource.applyUndoableAction(() => {
            for (const association of entity.targetAssociations.toArray()) {
              // Do not change the association to unsolved if the source symbol is also deleted
              if (aDeleteSymbols && aDeleteSymbols.length > 1) {
                const associationSymbol = (association as any).relatedSymbols.get(0);
                if (associationSymbol && aDeleteSymbols.includes(associationSymbol.sourceSymbol)) {
                  continue;
                }
              }
              nsLocal.ObjectImpl.changeAssociationToUnresolved(association);
            }
          }, "Change associations to unresolved");
        }
      },

      changeAssociationToUnresolved(association: sap.cdw.commonmodel.Association, bDelete = true) {
        if (association) {
          const modelToCsn = sap.cdw.commonmodel.ModelToCsn.getInstance();
          const name = modelToCsn.getAssociationName(association);
          const csn = {};
          modelToCsn.addAssociation(csn, association, /* isMixin*/ false);
          const oUnresolvedAssociation = {
            source: association.source,
            name: name,
            csn: csn[name],
          };
          const aUnresolvedAssociations = (association.rootContainer as any).unresolvedAssociations
            ? [...(association.rootContainer as any).unresolvedAssociations]
            : [];
          aUnresolvedAssociations.push(oUnresolvedAssociation);
          (association.rootContainer as any).unresolvedAssociations = aUnresolvedAssociations;

          if (bDelete) {
            association["_changedToUnresolved"] = true; // Tricky fix: use this temporary property to indicate to keep the foreignKey/foreignText
            association.deleteObject();
          }
        }
      },

      onCascadeChangeUnresolvedAssociations: function (oEventArgs, oModel) {
        const oObject = oEventArgs && oEventArgs.instance;
        const oldAssociations = oEventArgs.oldValue;
        let newAssociations = oEventArgs.newValue;
        if (oModel?.associations?.length > 0) {
          newAssociations = newAssociations.concat(oModel.associations.toArray());
        }
        const deletedAssociations = [];
        if (oldAssociations?.length > 0) {
          oldAssociations.forEach(function (oAssociation) {
            if (newAssociations.length > 0) {
              const isAssocAvailable = newAssociations.findIndex(
                (oAssoc) => oAssoc?.source?.name === oAssociation?.source?.name && oAssoc.name === oAssociation.name
              );
              if (isAssocAvailable === -1) {
                deletedAssociations.push(oAssociation);
              }
            } else if (newAssociations.length === 0 && oldAssociations.length === 1) {
              deletedAssociations.push(oldAssociations[0]);
            }
          });
        }

        // Delete the foreign key references for unresolved associations
        if (deletedAssociations?.length > 0) {
          oModel?.resource?.applyUndoableAction(function () {
            deletedAssociations.forEach(function (tobeDeletedAssociation) {
              const dAssociation = tobeDeletedAssociation;
              deleteAssociationInHierarchyWithDirectory(dAssociation?.source, dAssociation);
              if (dAssociation?.csn?.on?.length > 0) {
                const mappingObj = {};
                const csn = dAssociation.csn;
                for (let j = 0; j < csn.on.length; j++) {
                  const refObj = csn.on[j];
                  if (refObj.ref && refObj.ref.length && refObj.ref.length > 0) {
                    if (refObj.ref.length === 1) {
                      (mappingObj as any).source = refObj.ref[0];
                      break;
                    }
                  }
                }
                if ((mappingObj as any)?.source) {
                  const oEntity = oModel?.entities?.filter((entity) => entity.name === dAssociation.source.name);
                  const element = oEntity[0]?.elements?.filter(
                    (element) =>
                      element.name === (mappingObj as any).source || element.displayName === (mappingObj as any).source
                  );
                  if (element && element[0]) {
                    nsLocal.ObjectImpl.clearElementForeignAssociation(element[0], dAssociation.name);
                  }
                }
              }
            });
          });
        }
      },

      /**
       * Triggered when the name of an entity changes
       * @function
       * @name onCascadeChangeEntityName
       * @memberOf sap.cdw.ermodeler.ObjectImpl
       * @param {Object} oEventArgs
       */
      onCascadeChangeEntityName: function (oEventArgs) {
        const oObject = oEventArgs && oEventArgs.instance,
          oModel = oObject.resource.model;
        // update association's target name with updated name for managed association
        if (oObject.sourceAssociations && oObject.sourceAssociations.length > 0) {
          oObject.sourceAssociations.forEach(function (oAssociation) {
            if (oAssociation.type === "Managed" && oAssociation.targetElement) {
              oAssociation.targetElement.name = oObject.name;
            }
          });
        }
        if (oModel) {
          oModel.validate();
        }
      },

      /**
       * Triggered when the name of an element changes
       * @function
       * @name onCascadeChangeElementName
       * @param {Object} oEventArgs
       */
      onCascadeChangeElementName: function (oEventArgs) {
        const oObject = oEventArgs && oEventArgs.instance,
          sNewValue = oObject && oEventArgs.newValue,
          sOldValue = oObject && oEventArgs.oldValue;
        const oModel = oObject && oObject.resource.model;
        const oEntity = oObject?.container;

        // Change names in hierarchy with directory
        if (oEntity?.isHierarchyWithDirectory && oEntity?.classDefinition.name !== "Output") {
          changeColumnNamesInHierarchyWithDirectory(oEntity, sOldValue, sNewValue);
        }

        // Validate only if it is already attached to an entity
        if (oEntity && oModel) {
          oModel.validate();
        }

        // Association case
        if (oObject?.classDefinition?.name === CommonShortClassNames.ASSOCIATION) {
          const oldName = oEventArgs?.oldValue;
          nsLocal.ObjectImpl.onNameChangeAssociation(oObject, oldName);
        }
        nsLocal.ObjectImpl.publishElementsChangeEvent(oEntity);
      },

      publishElementsChangeEvent: function (oObject) {
        if (oObject) {
          if (oObject._delayedNotification !== undefined) {
            clearTimeout(oObject._delayedNotification);
          }
          oObject._delayedNotification = setTimeout(() => {
            delete oObject._delayedNotification;
            if (sap?.ui?.getCore instanceof Function) {
              sap.ui.getCore().getEventBus().publish("modelChanged", "elementChanged");
            }
          }, 0);
        }
      },

      /**
       * Triggered when the key of an element changes
       * @function
       * @name onCascadeChangeKey
       * @param {Object} oEventArgs
       */
      onCascadeChangeKey: function (oEventArgs) {
        const oObject = oEventArgs && oEventArgs.instance;
        const oModel = oObject && oObject.resource.model;

        if (oObject) {
          // Key element must be not null
          if (oObject.isKey) {
            oObject.isNotNull = true;
            oObject.default = undefined;
          }
          // if key is removed , remove representative key and compound sequence
          const entity = oModel?.output ? oModel?.output : oModel?.table;
          if (!oObject.isKey && entity?.representativeKey) {
            let isImpacted = false;
            if (oObject === entity.representativeKey) {
              entity.representativeKey = undefined;
              isImpacted = true;
            }
            const index = entity.compoundKeySequence?.indexOf(oObject);
            if (index >= 0) {
              entity.compoundKeySequence?.removeAt(index);
            }
            if (entity.compoundKeySequence.length <= 0) {
              entity.representativeKey = undefined;
              isImpacted = true;
            }
            const resourceBundle = nsLocal.ObjectImpl.getResourceBundle();
            if (isImpacted) {
              sap.m.MessageToast.show(resourceBundle.getText("INFO_REPRESENTATIVE_KEY_IS_REMOVED_WITH_MESSAGE"));
            }
          }

          nsLocal.ModelImpl.onPrimaryKeyChange(oObject);

          handleAIChange(oEventArgs, oObject, "isKey");

          // Validate only if it is already attached to a base entity
          if (oObject.container && oModel) {
            oModel.validate();
          }
        }
      },

      /**
       * Triggered when an entity is added or removed from the collection
       * @name onCascadeChangeEntities
       * @param {Object} oEventArgs
       */
      onCascadeChangeEntities: function (oEventArgs) {
        const oModel = oEventArgs && oEventArgs.instance;
        if (oModel) {
          const isORDInERMEnabled = SupportedFeaturesService.getInstance().isORDInERMEnabled();
          if (isORDInERMEnabled) {
            const oNewEntity = oEventArgs.newObject;
            const oOldEntity = oEventArgs.oldObject;
            // When add local table into model, sync ORD settings from model to the local table if local table does not have the settings but model have
            if (
              oNewEntity &&
              !oOldEntity &&
              isORDTable(oNewEntity) &&
              oNewEntity.connection === "" &&
              oNewEntity.ordId === "" &&
              (oModel.connection !== "" || oModel.ordId !== "")
            ) {
              oNewEntity.connection = oModel.connection;
              oNewEntity.ordId = oModel.ordId;
            }
          }
          oModel.validate();
        }
      },

      /**
       * Triggered when an element is added or removed from the collection
       * @function
       * @name onCascadeChangeElements
       * @memberOf sap.cdw.ermodeler.ObjectImpl
       * @param {Object} oEventArgs
       */
      onCascadeChangeElements: function (oEventArgs) {
        if (oEventArgs) {
          const oBaseEntity = oEventArgs.instance;
          const oModel = oBaseEntity.resource.model;
          // Element deletion
          if (oEventArgs.eventType === "referenceRemove" && oEventArgs.newObject === undefined) {
            // Check invalid hierachy level..
            if (oBaseEntity && oBaseEntity.hierarchies && oBaseEntity.hierarchies.length) {
              nsLocal.ObjectImpl.removeOrphanHierarchyLevels(oBaseEntity);
            }
            // _DP_ Remove irrelevant analytic parameters
            if (oBaseEntity && oBaseEntity.analyticParameters && oBaseEntity.analyticParameters.length) {
              nsLocal.ObjectImpl.removeUselessAnalyticParameters(oBaseEntity);
            }
          } else if (oEventArgs.eventType !== "referenceRemove") {
            nsLocal.ObjectImpl.updateIndexOrderOnCascadeChangeElements(oEventArgs);
          }
          if (oModel) {
            oModel.validate();
          }
          nsLocal.ObjectImpl.publishElementsChangeEvent(oBaseEntity);
        }
      },

      /**
       * Triggered when an Params is added or removed from the collection
       * @function
       * @name onCascadeChangeElements
       * @memberOf sap.cdw.ermodeler.ObjectImpl
       * @param {Object} oEventArgs
       */
      onCascadeChangeParams: function (oEventArgs) {
        if (oEventArgs) {
          const oBaseEntity = oEventArgs.instance;
          const oModel = oBaseEntity.resource.model;
          if (oEventArgs?.eventType === "referenceAdd" && oEventArgs?.referenceName === "parameters") {
            if (oEventArgs.newObject.container.loadExternalImpact instanceof Function) {
              oEventArgs.newObject.container.loadExternalImpact({ entityDependency: true });
            }
          }
          if (oModel) {
            oModel.validate();
          }
        }
      },

      /**
       * Triggered when  filter is added or removed from the collection
       * @function
       * @name onCascadeChangeFilters
       * @memberOf sap.cdw.ermodeler.ObjectImpl
       * @param {Object} oEventArgs
       */
      onCascadeChangeFilters: function (oEventArgs) {
        if (oEventArgs) {
          const oBaseEntity = oEventArgs.instance;
          const oModel = oBaseEntity.resource.model;
          if (oModel) {
            oModel.validate();
          }
        }
      },

      /**
       * Triggered when  partition is updated in the collection
       * @function
       * @name onCascadeChangePartitions
       * @memberOf sap.cdw.ermodeler.ObjectImpl
       * @param {Object} oEventArgs
       */
      onCascadeChangePartitions: function (oEventArgs) {
        if (oEventArgs) {
          const oBaseEntity = oEventArgs.instance;
          const oModel = oBaseEntity.resource.model;
          if (oModel) {
            oModel.validate();
          }
        }
      },

      /**
       * Update index order when an element is added or removed from the collection
       * @param oEventArgs
       */
      updateIndexOrderOnCascadeChangeElements: function (oEventArgs) {
        if (oEventArgs) {
          const oBaseEntity = oEventArgs.instance;
          const oNewElement = oEventArgs.newObject;
          const oOldElement = oEventArgs.oldObject;
          // Add
          if (oNewElement && !oOldElement) {
            let index = oEventArgs.index;
            if (index === undefined || index < 0 || index > oBaseEntity.elements.length) {
              index = oBaseEntity.elements.length - 1;
            } else {
              if (
                oBaseEntity.elements.toArray().some((e) => e !== oNewElement && e.indexOrder === oNewElement.indexOrder)
              ) {
                oBaseEntity.elements.forEach((el) => {
                  if (el !== oNewElement && el.indexOrder >= oNewElement.indexOrder) {
                    el.indexOrder++;
                  }
                });
              }
            }
            oNewElement.indexOrder = index;
          }
          // Delete
          if (!oNewElement && oOldElement) {
            if (oBaseEntity.elements.toArray().some((ele) => ele.indexOrder >= oBaseEntity.elements.length)) {
              const aOrderedElements = oBaseEntity.elements.toArray().sort(function (oElement1, oElement2) {
                return oElement1.indexOrder - oElement2.indexOrder;
              });
              for (let nIndex = 0; nIndex < aOrderedElements.length; nIndex++) {
                if (aOrderedElements[nIndex].indexOrder !== nIndex) {
                  aOrderedElements[nIndex].indexOrder = nIndex;
                }
              }
            }
          }
        }
      },

      /**
       * Triggered when the name of an element changes
       * @param {Object} oEventArgs
       */
      onCascadeChangeHierarchyName: function (oEventArgs) {
        const oObject = oEventArgs && oEventArgs.instance;
        const oModel = oObject && oObject.resource.model;
        if (oModel && oObject.container) {
          // only if it is already attached to a base entity
          oObject.container.validate();
        }
      },

      /**
       *
       * @param oDimension {Object} The dimension whose levels are to be cleaned
       * @param oElement {Object} The element being deleted (Optional: when not set -or called after deletion- all inconsistent levels will be cleaned)
       */
      removeOrphanHierarchyLevels: function (
        oDimension: sap.cdw.commonmodel.Entity,
        oElement: sap.cdw.commonmodel.Element
      ) {
        if (oDimension && oDimension.hierarchies && oDimension.hierarchies.length) {
          for (const oneHierarchy of oDimension.hierarchies.toArray()) {
            const hierarchy = oneHierarchy as sap.cdw.commonmodel.LevelBasedHierarchy;
            if (hierarchy && hierarchy.levels && hierarchy.levels.length) {
              // oElement may be undefined (element already deleted) then all levels with undefined element will be deleted
              // Otherwise (element being deleted) the levels referencing this element will be deleted
              // In this case, at most ONE level would be deleted!
              for (let i = hierarchy.levels.length; i > 0; i--) {
                const level = hierarchy.levels.get(i - 1);
                if (level && level.element === oElement) {
                  level.deleteObject();
                }
              }
            }
          }
        }
      },

      // _DP_ Delete analytic parameters if no restricted measure
      removeUselessAnalyticParameters: function (oView: sap.cdw.commonmodel.Entity) {
        if (oView?.analyticParameters && oView.analyticParameters.length) {
          let hasRestrictedMeasure = false;
          for (const analyticMeasure of oView.measureElements.toArray()) {
            if (analyticMeasure?.isRestrictedMeasure) {
              hasRestrictedMeasure = true;
              break;
            }
          }
          if (!hasRestrictedMeasure) {
            oView.analyticParameters.clear();
          }
        }
      },

      getExternalParentChildHierarchy: function (
        oEntity: sap.cdw.commonmodel.Entity
      ): sap.cdw.commonmodel.ParentChildHierarchy {
        // Check entity has the right semantic type
        if (oEntity?.isHierarchy) {
          // Check only ONE Parent/Child hierarchy defined
          return (
            oEntity.hierarchies?.length === 1 &&
            (oEntity.hierarchies.get(0) as sap.cdw.commonmodel.ParentChildHierarchy)
          );
        }
        return undefined;
      },
      getExternalHierarchyParentNode: function (
        oEntity: sap.cdw.commonmodel.Entity
      ): sap.galilei.model.BaseCollection<any> {
        const pcHierarchy: sap.cdw.commonmodel.ParentChildHierarchy =
          nsLocal.ObjectImpl.getExternalParentChildHierarchy(oEntity);
        return pcHierarchy?.parentElement;
      },
      getExternalHierarchyChildNode: function (
        oEntity: sap.cdw.commonmodel.Entity
      ): sap.galilei.model.BaseCollection<any> {
        const pcHierarchy: sap.cdw.commonmodel.ParentChildHierarchy =
          nsLocal.ObjectImpl.getExternalParentChildHierarchy(oEntity);
        return pcHierarchy?.childElement;
      },

      // _DP_ Analytic Parameter helpers
      getAnalyticParameterReferenceElement: function (
        analyticParameter: sap.cdw.commonmodel.Parameter
      ): sap.cdw.commonmodel.Element {
        const valueHelpReference: sap.cdw.commonmodel.ReferenceEntity = analyticParameter?.valueHelpDefinition;
        return valueHelpReference?.element;
      },
      setAnalyticParameterReferenceElement: function (
        analyticParameter: sap.cdw.commonmodel.Parameter,
        refElement: sap.cdw.commonmodel.Element
      ) {
        if (refElement) {
          // Clear value help definition
          if (analyticParameter.valueHelpDefinition) {
            analyticParameter.valueHelpDefinition = undefined;
          }
          // Create reference element
          const ref = nsLocal.ModelImpl.createObject(
            CommonQualifiedClassNames.REFERENCE_ENTITY,
            {
              entity: refElement.container,
              element: refElement,
            },
            analyticParameter
          );
          analyticParameter.valueHelpDefinition = ref;
        }
      },
      setAnalyticParameterReferenceElementFromName: function (
        analyticParameter: sap.cdw.commonmodel.Parameter,
        refElementName: string
      ) {
        if (refElementName) {
          const output = analyticParameter?.container as sap.cdw.commonmodel.Entity;
          if (output?.elements?.length) {
            // Find element using reference element name and use set function
            const referenceElement = output.elements.selectObject({
              newName: refElementName,
            });
            if (referenceElement) {
              nsLocal.ObjectImpl.setAnalyticParameterReferenceElement(analyticParameter, referenceElement);
            }
          }
        }
      },
      setAnalyticParameterDataTypeByReferenceElement: function (
        analyticParameter: sap.cdw.commonmodel.Parameter,
        refElement: sap.cdw.commonmodel.Element
      ) {
        if (refElement && analyticParameter) {
          analyticParameter.dataType = refElement.dataType;
          analyticParameter.length = refElement.length;
          analyticParameter.scale = refElement.scale;
          analyticParameter.srid = refElement.srid;
        }
      },
    },
  });
});
