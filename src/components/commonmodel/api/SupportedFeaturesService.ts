/** @format */

export class SupportedFeaturesService {
  private static instance: ISupportedFeaturesService;

  public static registerService(service: ISupportedFeaturesService) {
    SupportedFeaturesService.instance = service;
  }

  public static getInstance(): ISupportedFeaturesService {
    if (!SupportedFeaturesService.instance) {
      SupportedFeaturesService.instance = new NullSupportedFeatureService();
    }
    return SupportedFeaturesService.instance;
  }
}

export class NullSupportedFeatureService implements ISupportedFeaturesService {
  isFiscalTimeDimensionEnabled(): boolean {
    return false;
  }
  isTechnicalVersionRestoreEnabled(): boolean {
    return false;
  }
  isImproveOutputUpdateEnabled(): boolean {
    return false;
  }
  isDataActivationEnabled(): boolean {
    return false;
  }
  isAnalyticMeasureFormulaEnabled(): boolean {
    return false;
  }
  isExceptionAggregationEnabled(): boolean {
    return false;
  }
  isCountDistinctEnabled(): boolean {
    return false;
  }
  isDotSupportEnabled(): boolean {
    return false;
  }
  isDotSupportEnabledFF(): boolean {
    return false;
  }
  isLoadingRepairedCsnEnabled(): boolean {
    return false;
  }
  isRepositryHiddenPerspectiveEnabled(): boolean {
    return false;
  }
  isRepositoryExtensionsEnabled(): boolean {
    return false;
  }
  isORDInERMEnabled(): boolean {
    return false;
  }
  isSpacePermissionsEnabled(): boolean {
    return false;
  }
  isBrowserLasyLoadEnabled(): boolean {
    return false;
  }
  isRestrictedMeasureEnabled(): boolean {
    return false;
  }
  isCompatibilityContractsEnabled(): boolean {
    return false;
  }
  isModelingNamespaceEnabled(): boolean {
    return false;
  }
  isSQLCMEnabled(): boolean {
    return false;
  }
  isColumnValueHelpGVEnabled(): boolean {
    return false;
  }
  isParameterValueHelpEnabled(): boolean {
    return false;
  }
  isFolderSelectorEnabled(): boolean {
    return false;
  }
  isSQLSaveWithErrorEnabled(): boolean {
    return false;
  }
  isDataLayerLandingWithSearchCompositeEnabled(): boolean {
    return false;
  }
  isHWDSupportCompounding(): boolean {
    return false;
  }
  isSACPlanningIntegrationEnabled(): boolean {
    return false;
  }
  isHarmonizationObjectSelection(): boolean {
    return false;
  }
  isCSNDepsAnalyzerEnabled(): boolean {
    return false;
  }
  isModelValidationEnabled(): boolean {
    return false;
  }
  isBWBridgeForTaskChainEnabled(): boolean {
    return false;
  }
  isSQLScriptProcedureForTaskChainEnabled(): boolean {
    return false;
  }
  isSkylineERModelerEnabled(): boolean {
    return false;
  }
  isGenAISemanticEnabled(): boolean {
    return false;
  }

  isTableRestoreEnabled(): boolean {
    return false;
  }
  isAnalyticModelDimensionHandlingEnabled(): boolean {
    return false;
  }
  isDeltaReadAPIEnabled(): boolean {
    return false;
  }
  isTableDeleteDataEnabled(): boolean {
    return false;
  }
  isSparkSelectionVacuumEnabled(): boolean {
    return false;
  }
  isModelingAnnotatePartitionsEnabled(): boolean {
    return false;
  }
  isBWPCEPushEnabled(): boolean {
    return false;
  }
  isDiMonitorImprovementsEnabled(): boolean {
    return false;
  }

  isTableTasksUseActiveRecords() {
    return false;
  }
  isHDLFSpaceImportTransportEnabled() {
    return false;
  }
  isBDCRepositoryTransportDataEnabled() {
    return false;
  }
  isBDCGAEnabled() {
    return false;
  }
  isHashPartitioningEnabled() {
    return false;
  }
}

export interface ISupportedFeaturesService {
  isTechnicalVersionRestoreEnabled(): boolean;
  isImproveOutputUpdateEnabled(): boolean;
  isFiscalTimeDimensionEnabled(): boolean;
  isDataActivationEnabled(): boolean;
  isAnalyticMeasureFormulaEnabled(): boolean;
  isExceptionAggregationEnabled(): boolean;
  isCountDistinctEnabled(): boolean;
  isDotSupportEnabled(): boolean;
  isDotSupportEnabledFF(): boolean;
  isLoadingRepairedCsnEnabled(): boolean;
  isRepositryHiddenPerspectiveEnabled(): boolean;
  isRepositoryExtensionsEnabled(): boolean;
  isORDInERMEnabled(): boolean;
  isSpacePermissionsEnabled(): boolean;
  isBrowserLasyLoadEnabled(): boolean;
  isRestrictedMeasureEnabled(): boolean;
  isCompatibilityContractsEnabled(): boolean;
  isModelingNamespaceEnabled(): boolean;
  isSQLCMEnabled(): boolean;
  isColumnValueHelpGVEnabled(): boolean;
  isParameterValueHelpEnabled(): boolean;
  isFolderSelectorEnabled(): boolean;
  isSQLSaveWithErrorEnabled(): boolean;
  isDataLayerLandingWithSearchCompositeEnabled(): boolean;
  isHWDSupportCompounding(): boolean;
  isSACPlanningIntegrationEnabled(): boolean;
  isHarmonizationObjectSelection(): boolean;
  isCSNDepsAnalyzerEnabled(): boolean;
  isModelValidationEnabled(): boolean;
  isBWBridgeForTaskChainEnabled(): boolean;
  isSQLScriptProcedureForTaskChainEnabled(): boolean;
  isSkylineERModelerEnabled(): boolean;
  isGenAISemanticEnabled(): boolean;

  isTableRestoreEnabled(): boolean;
  isDeltaReadAPIEnabled(): boolean;
  isTableDeleteDataEnabled(): boolean;
  isSparkSelectionVacuumEnabled(): boolean;
  isModelingAnnotatePartitionsEnabled(): boolean;
  isBWPCEPushEnabled(): boolean;
  isDiMonitorImprovementsEnabled(): boolean;
  isAnalyticModelDimensionHandlingEnabled: () => boolean;
  isTableTasksUseActiveRecords: () => boolean;
  isHDLFSpaceImportTransportEnabled: () => boolean;
  isBDCRepositoryTransportDataEnabled: () => boolean;
  isBDCGAEnabled: () => boolean;
  isHashPartitioningEnabled: () => boolean;
}
