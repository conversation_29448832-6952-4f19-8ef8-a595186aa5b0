/** @format */

import { <PERSON>atter, Utils, makeConfig, makeParse } from "sqlparser-js";
import { CurrentContextService } from "../../commonmodel/api/CurrentContextService";
import { Repo } from "../../shell/utility/Repo";
import { createContextObject, isCrossSpaceEntity, loadCrossSpaceEntity } from "./SqlEditorUtility";
import { Analyzer } from "./SqlScriptUtil";
import { processParseResult } from "./SqlViewUtil";

export async function parseSql(sqlQuery, isScript: boolean = false) {
  try {
    const config = Utils.getConfig("HANA");
    const oFeatures = sap.ui.getCore().getModel("featureflags").getData();
    if (!isScript && oFeatures.DWCO_SQL_INLINE_VALIDATION) {
      config.recover = true;
    }
    const parse = makeParse(makeConfig(config));
    const parseResult = parse && (parse({ src: sqlQuery }) as any);
    const isError = parseResult.messages.some((item) => item.type === "Error");
    const isCommaMessage = parseResult.messages.some((item) => item.code === "superflous_comma");
    if (isError && parseResult?.messages?.length > 0) {
      if (isCommaMessage) {
        throw parseResult?.messages;
      } else {
        throw parseResult?.messages[0];
      }
    }
    if (isScript) {
      try {
        const script = new Analyzer(parseResult);
        script.analyze();
        const result = {
          isStarColumnScript: parseResult.isStarColumn,
          selectColumns: parseResult.selectColumns,
          tableName: parseResult.tableName,
        };
        return result;
      } catch (err) {
        const errorResult = { isStarColumnScript: false, selectColumns: [], tableName: undefined };
        return errorResult;
      }
    } else {
      if (parseResult?.statements[0]) {
        const result = processParseResult(parseResult?.statements[0]);
        return result;
      }
    }
  } catch (err) {
    if (Array.isArray(err)) {
      throw err;
    }
    const response = {
      error: err,
    };
    throw response;
  }
}

export function formatSql(sqlQuery, includeWS: boolean = false) {
  try {
    const parseResult = getParsedResult(sqlQuery, includeWS);
    const formatter = new Formatter();
    const result = formatter && formatter.format(parseResult);
    return result;
  } catch (err) {
    const response = {
      error: err,
    };
    throw response;
  }
}

export function getParsedResult(sqlQuery: string, includeWS: boolean) {
  const parse = makeParse(makeConfig(Utils.getConfig("HANA")));
  if (parse && includeWS) {
    return parse({ src: sqlQuery, includeWS });
  }
  return parse(sqlQuery);
}
export async function getParsedResultNew(sqlQuery: string, includeWS: boolean, options) {
  const parseConfig = Utils.getConfig("HANA");
  const dependentObjects = await getDependentObjects(sqlQuery, true);
  const spaceName = CurrentContextService.getInstance().getCurrentSpaceId();
  let tables = [];
  for (const dependentObject of dependentObjects) {
    let table, tableJson, filteredObject;
    try {
      if (options?.repositoryObjects) {
        filteredObject = options.repositoryObjects.filter(function (obj) {
          return obj.qualified_name === dependentObject.identifier;
        });
      }
      if (filteredObject && filteredObject.length === 0 && options?.crossSpaceObjects) {
        filteredObject = options?.crossSpaceObjects.filter(function (obj) {
          return obj.qualified_name === dependentObject.identifier;
        });
      }

      if (filteredObject && filteredObject.length === 0 && dependentObject.identifier) {
        // Check if it is a cross space entity
        if (isCrossSpaceEntity(dependentObject.identifier, options.model)) {
          const objFile = await loadCrossSpaceEntity(dependentObject.identifier, options);
          if (objFile) {
            filteredObject = [objFile];
          }
        } else if (dependentObject.identifier && dependentObject.identifier.includes(".")) {
          const objFile = await loadCrossSpaceEntity(dependentObject.identifier, options);
          await createContextObject(dependentObject.identifier, options.model, objFile);
          if (objFile) {
            filteredObject = [objFile];
          }
        }
      }
      if (filteredObject && filteredObject.length === 1) {
        if (!filteredObject[0].csn) {
          const csnDefinitions = await Repo.getModelDetails(filteredObject[0].spaceName, filteredObject[0].name, [
            "csn",
          ]);
          filteredObject[0].csn = csnDefinitions.csn;
        }
      }
      table = Array.isArray(filteredObject) && filteredObject.length === 1 && filteredObject[0];
      if (table) {
        const definition = table.csn.definitions && table.csn.definitions[table.name];
        const columnsJSON = [];
        if (definition && definition.elements) {
          const columns = Object.keys(definition.elements);
          for (const column of columns) {
            columnsJSON.push({
              kind: "column",
              identifier: column,
              resolved: true,
              dataType: "NUMERIC",
            });
          }
        }
        tableJson = {
          kind: "table",
          identifier: dependentObject.identifier,
          resolved: true,
          columns: columnsJSON,
        };
      }
    } catch (err) {
      err;
    }
    tables.push(tableJson);
  }

  tables = tables[0] ? tables : [];
  const value = JSON.stringify(
    {
      table: tables,
    },
    undefined,
    2
  );

  parseConfig.resolver = makeResolve(value);
  const parse = makeParse(makeConfig(parseConfig));

  const parseResult = parse(sqlQuery);
  if (parseResult.errors === 0) {
    parseResult.resolve(-1, () => {
      if (parseResult.messages.length > 0) {
        throw parseResult.messages;
      }
    });
    // return tables;
  }
  //return parse(sqlQuery);
}

export async function getDependentObjects(src, isStandardQuery: boolean = false) {
  try {
    let tables: any;
    const parseConfig = Utils.getConfig("HANA");
    parseConfig.recover = true;
    parseConfig.messages = true;
    parseConfig.resolver = (objects, args, config, done, fail) => {
      tables = objects.table;
      done();
    };
    // make the parse function
    const parse = makeParse(makeConfig(parseConfig));
    const sqlPrefix = `CREATE function "function"()
     returns table
     (id nvarchar(1))
     as
     begin
     `;

    const sqlSuffix = `\nend;`;
    let parseResult;
    if (isStandardQuery) {
      parseResult = parse && parse(src);
    } else {
      parseResult = parse && parse({ src, sqlPrefix, sqlSuffix });
    }
    if (parseResult.errors === 0) {
      parseResult.resolve(-1, () => {
        tables = tables && tables.filter((item: any) => item.identifier !== "function");
      });
      return tables;
    } else {
      const errMessages = parseResult.messages && parseResult.messages.filter((item: any) => item.type === "Error");
      throw errMessages;
    }
  } catch (err) {
    throw err;
  }
}

function makeResolve(resolved) {
  if (typeof resolved === "string") {
    resolved = JSON.parse(resolved);
  }
  return function (toResolve, args, config, done) {
    var i,
      j,
      k,
      symbols,
      resolvedSymbols,
      kinds = Object.keys(toResolve);
    for (i = 0; i < kinds.length; i++) {
      resolvedSymbols = resolved[kinds[i]];
      if (resolvedSymbols) {
        symbols = toResolve[kinds[i]];
        for (j = 0; j < symbols.length; j++) {
          for (k = 0; k < resolvedSymbols.length; k++) {
            if (!symbols[j].identifier || symbols[j].identifier === resolvedSymbols[k].identifier) {
              if (symbols[j].schema && symbols[j].schema !== resolvedSymbols[k].schema) {
                continue;
              }
              if (symbols[j].kind && symbols[j].kind !== resolvedSymbols[k].kind) {
                continue;
              }
              symbols[j].schema = resolvedSymbols[k].schema;
              symbols[j].identifier = resolvedSymbols[k].identifier;
              symbols[j].kind = resolvedSymbols[k].kind;
              if (resolvedSymbols[k].columns) {
                symbols[j].columns = resolvedSymbols[k].columns;
              }
              symbols[j].kind = resolvedSymbols[k].kind;
              symbols[j].resolved = true;
            }
          }
        }
      }
    }
    if (done) {
      done();
    }
  };
}
