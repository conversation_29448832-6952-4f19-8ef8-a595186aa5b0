#XTIT Save Dialog param
modelNameSQL=SQL-visning
txtSQLFileValidationError=Modelvisningen er ikke gyldig. Løs alle valideringsproblemer, og forsøg igen.

#
# parser errors
#
0_not_supported={0} understøttes ikke.
1_already_defined=Identifikator {1} er allerede defineret.
column_order_and_index_order_specified=Kolonnerækkefølge og indeksrækkefølge kan ikke angives samtidigt.
annotation_0_not_supported_for_1=Annotering {0} understøttes ikke for {1}.

expected_1_found_0={1} blev forventet, men {0} fundet i stedet.
expected_data_type=Datatype forventet.
expected_expression=Udtryk forventet.
expected_expression_or_subquery=Udtryk eller underforespørgsel forventet.
expected_first_or_last=FIRST eller LAST forventet.
expected_identifier=Identifikator forventet.
expected_identifier_or_asterisk=Identifikator eller * forventet.
expected_identifier_or_sql_error_code=Identifikator eller SQL-fejlkode forventet.
expected_integer=Heltal forventet.
expected_language=Sprog forventet.
expected_literal=Streng eller numerisk litteral forventet.
expected_number=Numerisk litteral forventet.
expected_operator=Operator forventet.
expected_path_or_identifier=Sti eller identifikator forventet.
expected_select_clause=SELECT forventet.
expected_select_or_set_operator=SELECT- eller SET-operator forventet.
expected_sequence_option=Sekvensvalgmulighed forventet.
expected_set_operator=Sætoperator forventet.
expected_simple_identifier=Simpel identifikator forventet.
expected_statement=Sætning forventet.
expected_string=Streng forventet.
expected_subquery=Underforespørgsel forventet.
expected_varref_found_identifier_use_1=Variabelreference forventet, men identifikator fundet, brug {1} i stedet.
#YMSG,145:SQL script semantic check warning message
txtSqlError=Vi kunne ikke aflede returneringsopgørelsen fuldstændigt fra scriptet. Gennemse listen over kolonner og deres datatyper i sideområdet.
#XFLD: warning dialog title label
lblWarningDialogTitle=Advars.
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=Hvis du ændrer sproget til "SQL (standardforespørgsel)", udløses der fejl for eventuelle SQLScript-nøgleord i din kode.
#XBUT:Warning dialog ok button for DB View type
btnOk=OK
#XBUT,14:Warning dialog close button for DB View type
btnClose=Luk
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=Du kan ikke se data i en SQLScript-visning, før alle ændringer er implementeret. Implementer visningen, og prøv igen.
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=Du kan ikke se data i en SQLScript-visning, før alle ændringer er implementeret. Implementer visningen, og prøv igen.
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=Du kan ikke se data i en visning, der er beskyttet af en dataadgangskontrol, før alle ændringer er implementeret. Implementer visningen, og prøv igen.
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=Du kan ikke se data i en SQLScript-visning, før alle ændringer er implementeret. Implementer visningen, og prøv igen.
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=Du kan ikke se data i en SQLScript-visning, før alle ændringer er implementeret. Implementer visningen, og prøv igen.
cannotFormat=Sætningen kan ikke formateres.
shouldHaveGroupBy=SQL-forespørgslen skal have en GROUP BY-klausul.
groupByCols=GROUP BY-klausulen skal have alle kolonner, der ikke er inkluderet i aggregeringsfunktioner.
@msgTableImported=''{0}''’, ''{1}''’ er importeret og implementeret.
@msgTableImportFailed=Problem ved import: Kilden blev ikke importeret til repository og kan derfor ikke anvendes i din visning. Slip den i editoren på ny for at forsøge igen.
VAL_SAVE_CANCELLED=Importen af ''{0}''’ er annulleret.
#XMSG
uniqueAlias=Kolonnealiasser skal være entydige.
#XMSG
ANALYSING_SQL=Analyserer SQL
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn=Datatyper kunne ikke afledes for {0}. Kontroller datatyperne for disse kolonner i panelet Egenskaber.
#XBUT : SQL format button
format=Format
#XMSG:Format failure message
formatError=Forkert format. Ret syntaksfejlen.
#XTOL
validateAndPreview=Valider SQL og forhåndsvis data
#XTOL
validateAndViewData=Valider SQL, og vis data
#XMSG
unexpected_end=Uventet afslutning
#XTOL
validate=Valider SQL
#XTOL
formattol=Formater SQL

#begin of change management (pop-up)
#XTIT
changeManagementTitle=Kildeopdateringer
#XCOL
changedOn=Ændret den
#XCOL
changedBy=Ændret af

objectDisplayNameLocation=Kilde
reviewText=Gennemse ændringerne, og gem og implementer det opdaterede objekt på ny for at anvende dem.
changesText=Følgende kilder, der anvendes i objektet, blev ændret, og ændringerne er markeret med valideringsmeddelelser i editoren:
sharedEntity=Delt
#end of change management (pop-up)

#XFLD
VALIDATEFLD=Valider
#XFLD
CANCELVALIDATEFLD=Annuller validering
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=Deltaregistreringstabellen {0} er ikke tilladt til forbrug.
#XMSG
DATAVALIDATION=Datavalidering
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=Tabellen eller visningen {0} har designtids- eller kørselstidsfejl.
# analyzer errors
#
superflous_comma=Fjern komma
unterminated_path=Valg ufuldstændigt
undefined_name_0=Id''et {0} er ikke defineret eller findes ikke.
unresolved_col_0=Kolonnen {0} blev ikke fundet.
unresolved_table_0=Tabellen eller visningen {0} blev ikke fundet.
ambigous_col_definition_0_1_2=Det samme kolonnenavn defineres mere end én gang i {0}, {1} og {2}.
expected_scalar_typed_expression=Indtast en enkelt værdi.
invalid_datatype_1_operator_2_expected_3=Operatoren {2} forventer datatypen {3}. Ændr datatypen {1}.
UNRESOLVED_TABLE_OR_MISSING_QUOTE=Tabellen eller visningen {0} blev ikke fundet, fordi den ikke findes, eller der er mindst ét manglende dobbelt anførselstegn. 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=Kolonnen {0} blev ikke fundet, fordi den ikke findes, eller der er mindst ét manglende dobbelt anførselstegn. 

