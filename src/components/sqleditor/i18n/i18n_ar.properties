#XTIT Save Dialog param
modelNameSQL=عرض لغة الاستعلامات المركبة
txtSQLFileValidationError=عرض النموذج غير صالح. يرجى إصلاح جميع مشاكل التحقق من الصحة والمحاولة مرة أخرى.

#
# parser errors
#
0_not_supported={0} غير مدعوم.
1_already_defined=المعرف {1} محدد بالفعل.
column_order_and_index_order_specified=لا يمكن تحديد ترتيب العمود وترتيب الفهرس في الوقت نفسه.
annotation_0_not_supported_for_1=التعليق التوضيحي {0} غير مدعوم لـ {1}.

expected_1_found_0=كان {1} متوقعًا، ولكن تم العثور على {0} بدلًا من ذلك.
expected_data_type=نوع بيانات متوقع.
expected_expression=تعبير متوقع.
expected_expression_or_subquery=تعبير أو استعلام فرعي متوقع.
expected_first_or_last=FIRST أو LAST متوقع.
expected_identifier=معرف متوقع.
expected_identifier_or_asterisk=معرف أو * متوقع.
expected_identifier_or_sql_error_code=معرف أو رمز خطأ SQL متوقع.
expected_integer=عدد صحيح متوقع.
expected_language=لغة متوقعة.
expected_literal=سلسلة أو قيمة حرفية رقمية متوقعة.
expected_number=قيمة حرفية رقمية متوقعة.
expected_operator=عامل متوقع.
expected_path_or_identifier=مسار أو معرف متوقع.
expected_select_clause=SELECT متوقع.
expected_select_or_set_operator=SELECT أو معامل تعيين متوقع.
expected_sequence_option=خيار تسلسل متوقع.
expected_set_operator=معامل مجموعة متوقع.
expected_simple_identifier=معرّف بسيط متوقع.
expected_statement=عبارة متوقعة.
expected_string=سلسلة متوقعة.
expected_subquery=استعلام فرعي متوقع.
expected_varref_found_identifier_use_1=مرجع متغير متوقع، ولكن تم العثور على معرف، استخدم {1} بدلاً من ذلك.
#YMSG,145:SQL script semantic check warning message
txtSqlError=يتعذر علينا اشتقاق عبارة الإرجاع بالكامل من البرنامج النصي. يرجى مراجعة قائمة الأعمدة وأنواع بياناتها في اللوحة الجانبية.
#XFLD: warning dialog title label
lblWarningDialogTitle=تحذير
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=سيؤدي تغيير اللغة إلى "SQL (استعلام قياسي)" إلى حدوث أخطاء لأي كلمات أساسية للغة SQLScript في تعليماتك البرمجية.
#XBUT:Warning dialog ok button for DB View type
btnOk=موافق
#XBUT,14:Warning dialog close button for DB View type
btnClose=إغلاق
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=لا يمكنك الاطلاع على البيانات في عرض SQLScript حتى يتم نشر جميع التغييرات. يرجى نشر العرض والمحاولة مرة أخرى.
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=لا يمكنك الاطلاع على البيانات في عرض SQLScript حتى يتم نشر جميع التغييرات. يرجى نشر العرض والمحاولة مرة أخرى.
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=لا يمكنك الاطلاع على البيانات في عرض محمٍ بواسطة التحكم في الوصول إلى البيانات حتى يتم نشر جميع التغييرات. يرجى نشر العرض والمحاولة مرة أخرى.
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=لا يمكنك الاطلاع على البيانات في عرض SQLScript حتى يتم نشر جميع التغييرات. يرجى نشر العرض والمحاولة مرة أخرى.
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=لا يمكنك الاطلاع على البيانات في عرض SQLScript حتى يتم نشر جميع التغييرات. يرجى نشر العرض والمحاولة مرة أخرى.
cannotFormat=لا يمكن تنسيق العبارة.
shouldHaveGroupBy=يجب أن يحتوي استعلام SQL على عبارة GROUP BY.
groupByCols=يجب أن تحتوي عبارة GROUP BY على جميع الأعمدة التي لم يتم تضمينها في وظائف التجميع.
@msgTableImported=''{0}''’، ''{1}''’ تم استيراده ونشره بنجاح.
@msgTableImportFailed=هناك مشكلة في الاستيراد: لم يتم استيراد المصدر إلى المستودع وبالتالي لا يمكن استخدامه في طريقة العرض لديك. وللمحاولة مرة أخرى، يرجى إعادة إسقاطه على المحرر.
VAL_SAVE_CANCELLED=تم إلغاء استيراد ''{0}''’.
#XMSG
uniqueAlias=يجب أن تكون الأسماء المستعارة للعمود فريدة.
#XMSG
ANALYSING_SQL=تحليل SQL
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn=لا يمكن اشتقاق أنواع البيانات لـ {0}. يرجى مراجعة أنواع البيانات لهذه الأعمدة في لوحة الخصائص.
#XBUT : SQL format button
format=التنسيق
#XMSG:Format failure message
formatError=فشل التنسيق. قم بتصحيح الخطأ في بناء الجملة.
#XTOL
validateAndPreview=التحقق من صحة SQL ومعاينة البيانات
#XTOL
validateAndViewData=التحقق من صحة SQL وعرض البيانات
#XMSG
unexpected_end=انتهاء غير متوقع
#XTOL
validate=التحقق من صحة SQL
#XTOL
formattol=SQL للتنسيق

#begin of change management (pop-up)
#XTIT
changeManagementTitle=تحديثات المصدر
#XCOL
changedOn=تاريخ التغيير
#XCOL
changedBy=تم التغيير بواسطة

objectDisplayNameLocation=المصدر
reviewText=يرجى مراجعة التعديلات وحفظ الكائن المحدث وإعادة نشره لتطبيقها.
changesText=تم تعديل المصادر التالية المستخدمة في هذا الكائن، وتم وضع علامة على هذه التعديلات بواسطة رسائل التحقق من الصحة في المحرِّر:
sharedEntity=مشترك
#end of change management (pop-up)

#XFLD
VALIDATEFLD=تحقق من الصحة
#XFLD
CANCELVALIDATEFLD=إلغاء التحقق من الصحة
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=غير مسموح بجدول التقاط الفرق {0} للاستهلاك.
#XMSG
DATAVALIDATION=التحقق من صحة البيانات
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=يحتوي الجدول أو طريقة العرض ''{0}'' على أخطاء وقت تصميم أو أخطاء وقت تشغيل.
# analyzer errors
#
superflous_comma=إزالة الفاصلة
unterminated_path=التحديد غير مكتمل
undefined_name_0=لم يتم تحديد المعرف {0} أو أنه غير موجود.
unresolved_col_0=لا يمكن العثور على العمود {0}.
unresolved_table_0=لا يمكن العثور على الجدول أو العرض {0}.
ambigous_col_definition_0_1_2=تم تحديد اسم العمود نفسه أكثر من مرة واحدة في {0} و{1} و{2}.
expected_scalar_typed_expression=أدخِل قيمة فردية.
invalid_datatype_1_operator_2_expected_3=المعامل {2} يتوقع نوع البيانات {3}. قم بتغيير نوع البيانات {1}.
UNRESOLVED_TABLE_OR_MISSING_QUOTE=لا يمكن العثور على الجدول أو طريقة العرض {0} نظرًا لعدم وجوده أو لعدم وجود علامة اقتباس مزدوجة واحدة على الأقل. 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=لا يمكن العثور على العمود {0} لأنه غير موجود أو توجد علامة اقتباس مزدوجة واحدة على الأقل. 

