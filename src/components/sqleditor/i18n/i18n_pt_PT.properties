#XTIT Save Dialog param
modelNameSQL=Vista SQL
txtSQLFileValidationError=A vista do modelo não é válida. Corrija todos os problemas de validação e tente novamente.

#
# parser errors
#
0_not_supported={0} não é suportado.
1_already_defined=O identificador {1} já está definido.
column_order_and_index_order_specified=A ordem das colunas e a ordem dos índices não podem ser especificadas ao mesmo tempo.
annotation_0_not_supported_for_1=A anotação {0} não é suportada para {1}.

expected_1_found_0=Previsto {1}, mas em vez disso encontrado {0}.
expected_data_type=Tipo de dados previsto.
expected_expression=Expressão prevista.
expected_expression_or_subquery=Expressão ou subconsulta prevista.
expected_first_or_last=FIRST ou LAST previsto.
expected_identifier=Identificador previsto.
expected_identifier_or_asterisk=Identificador ou * previsto.
expected_identifier_or_sql_error_code=Identificador ou código de erro SQL previsto.
expected_integer=Número inteiro previsto.
expected_language=Linguagem prevista.
expected_literal=Cadeia ou literal numérico previsto.
expected_number=Literal numérico previsto.
expected_operator=Operador previsto.
expected_path_or_identifier=Caminho ou identificador previsto.
expected_select_clause=SELECT previsto.
expected_select_or_set_operator=Operador SELECT ou SET previsto.
expected_sequence_option=Opção de sequência prevista.
expected_set_operator=Operador SET previsto.
expected_simple_identifier=Identificador simples previsto.
expected_statement=Instrução prevista.
expected_string=Cadeia prevista.
expected_subquery=Subconsulta prevista.
expected_varref_found_identifier_use_1=Prevista referência a variável, mas encontrado identificador, utilizar {1} em vez disso.
#YMSG,145:SQL script semantic check warning message
txtSqlError=Impossível derivar por completo instrução de retorno a partir do script. Reveja a lista de colunas e os seus tipos de dados no painel lateral.
#XFLD: warning dialog title label
lblWarningDialogTitle=Aviso
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=Alterar a linguagem para "SQL (consulta padrão)" dará erros para quaisquer palavras-chave de SQLScript no seu código.
#XBUT:Warning dialog ok button for DB View type
btnOk=OK
#XBUT,14:Warning dialog close button for DB View type
btnClose=Fechar
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=Não pode ver os dados numa vista SQLScript até todas as alterações estarem implementadas. Implemente a vista e tente novamente.
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=Não pode ver os dados numa vista SQLScript até todas as alterações estarem implementadas. Implemente a vista e tente novamente.
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=Não pode ver os dados numa vista protegida por um controlo de acesso a dados até todas as alterações estarem implementadas. Implemente a vista e tente novamente.
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=Não pode ver os dados numa vista SQLScript até todas as alterações estarem implementadas. Implemente a vista e tente novamente.
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=Não pode ver os dados numa vista SQLScript até todas as alterações estarem implementadas. Implemente a vista e tente novamente.
cannotFormat=A instrução não pode ser formatada.
shouldHaveGroupBy=A consulta SQL deve ter uma cláusula GROUP BY.
groupByCols=A cláusula GROUP BY deve ter todas as colunas que não estão incluídas em funções de agregação.
@msgTableImported=''{0}'', ''{1}''’ importado e implementado com sucesso.
@msgTableImportFailed=Problema com a importação: a origem não foi importada para o repositório e, por isso, não pode ser utilizada na sua vista. Para tentar novamente, volte a largá-la no editor.
VAL_SAVE_CANCELLED=A importação de ''{0}'' foi cancelada.
#XMSG
uniqueAlias=Os alias de coluna devem ser exclusivos.
#XMSG
ANALYSING_SQL=Analisar SQL
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn=Não foi possível derivar tipos de dados p/{0}. Rever tipos dados das colunas no painel Propriedades.
#XBUT : SQL format button
format=Formato
#XMSG:Format failure message
formatError=Formato falhou. Corrija o erro de sintaxe.
#XTOL
validateAndPreview=Validar SQL e pré-visualizar dados
#XTOL
validateAndViewData=Validar SQL e visualizar dados
#XMSG
unexpected_end=Fim inesperado
#XTOL
validate=Validar SQL
#XTOL
formattol=Formatar SQL

#begin of change management (pop-up)
#XTIT
changeManagementTitle=Atualizações de origem
#XCOL
changedOn=Alterado em
#XCOL
changedBy=Alterado por

objectDisplayNameLocation=Origem
reviewText=Reveja as modificações e guarde e reimplemente o objeto atualizado para aplicá-las.
changesText=As seguintes origens utilizadas neste objeto foram modificadas e essas modificações são marcadas por mensagens de validação no editor:
sharedEntity=Partilhado
#end of change management (pop-up)

#XFLD
VALIDATEFLD=Validar
#XFLD
CANCELVALIDATEFLD=Cancelar validação
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=A tabela de captura delta {0} não é permitida para consumo.
#XMSG
DATAVALIDATION=Validação dos dados
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=A tabela ou vista {0} tem erros em tempo de design ou execução.
# analyzer errors
#
superflous_comma=Remover vírgula
unterminated_path=Seleção incompleta
undefined_name_0=O identificador {0} não foi definido ou não existe.
unresolved_col_0=Impossível encontrar a coluna {0}.
unresolved_table_0=Impossível encontrar a tabela ou a vista {0}.
ambigous_col_definition_0_1_2=O mesmo nome de coluna está a ser definido mais de uma vez em {0}, {1} e {2}.
expected_scalar_typed_expression=Introduza um valor individual.
invalid_datatype_1_operator_2_expected_3=O operador {2} espera o tipo de dados {3}. Altere o tipo de dados {1}.
UNRESOLVED_TABLE_OR_MISSING_QUOTE=Impossível encontrar a tabela ou a vista {0} porque não existe ou porque pelo menos um par de aspas duplas está em falta. 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=Impossível encontrar a coluna {0} porque não existe ou porque pelo menos um par de aspas duplas está em falta. 

