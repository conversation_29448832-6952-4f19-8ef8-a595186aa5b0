#XTIT Save Dialog param
modelNameSQL=มุมมอง SQL
txtSQLFileValidationError=โมเดลมุมมองไม่ถูกต้อง กรุณาแก้ไขปัญหาการตรวจสอบความถูกต้องทั้งหมดแล้วลองอีกครั้ง

#
# parser errors
#
0_not_supported={0} ไม่ได้รับการสนับสนุน
1_already_defined=ID {1} ถูกกำหนดไว้แล้ว
column_order_and_index_order_specified=ไม่สามารถระบุลำดับคอลัมน์และลำดับดัชนีพร้อมกันได้
annotation_0_not_supported_for_1=คำอธิบายประกอบ {0} ไม่ได้รับการสนับสนุนสำหรับ {1}

expected_1_found_0=คาดว่าจะพบ {1} แต่พบ {0} แทน
expected_data_type=คาดว่าจะพบประเภทข้อมูล
expected_expression=คาดว่าจะพบนิพจน์
expected_expression_or_subquery=คาดว่าจะพบนิพจน์หรือคิวรีย่อย
expected_first_or_last=คาดว่าจะพบ FIRST หรือ LAST
expected_identifier=คาดว่าจะพบ ID
expected_identifier_or_asterisk=คาดว่าจะพบ ID หรือ *
expected_identifier_or_sql_error_code=คาดว่าจะพบรหัสข้อผิดพลาด SQL หรือ ID
expected_integer=คาดว่าจะพบจำนวนเต็ม
expected_language=คาดว่าจะพบภาษา
expected_literal=คาดว่าจะพบสตริงหรือค่าคงที่ที่เป็นตัวเลข
expected_number=คาดว่าจะพบค่าคงที่ที่เป็นตัวเลข
expected_operator=คาดว่าจะพบตัวดำเนินการ
expected_path_or_identifier=คาดว่าจะพบพาธหรือ ID
expected_select_clause=คาดว่าจะพบ SELECT
expected_select_or_set_operator=คาดว่าจะพบตัวดำเนินการ SELECT หรือ SET
expected_sequence_option=คาดว่าจะพบตัวเลือกลำดับ
expected_set_operator=คาดว่าจะพบตัวดำเนินการ SET
expected_simple_identifier=คาดว่าจะพบ ID แบบง่าย
expected_statement=คาดว่าจะพบคำสั่ง
expected_string=คาดว่าจะพบสตริง
expected_subquery=คาดว่าจะพบคิวรีย่อย
expected_varref_found_identifier_use_1=คาดว่าจะพบตัวแปรการอ้างอิง แต่พบ ID ใช้ {1} แทน
#YMSG,145:SQL script semantic check warning message
txtSqlError=เราไม่สามารถรับคำสั่งส่งคืนจากสคริปต์ได้โดยสมบูรณ์ กรุณาตรวจทานรายการคอลัมน์และประเภทข้อมูลของคอลัมน์ในแผงด้านข้าง
#XFLD: warning dialog title label
lblWarningDialogTitle=คำเตือน
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=การเปลี่ยนภาษาเป็น "SQL (คิวรีมาตรฐาน)" จะทำให้เกิดข้อผิดพลาดกับคำสำคัญของ SQLScript ในรหัสของคุณ
#XBUT:Warning dialog ok button for DB View type
btnOk=ตกลง
#XBUT,14:Warning dialog close button for DB View type
btnClose=ปิด
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=คุณไม่สามารถดูข้อมูลในมุมมอง SQLScript ได้จนกว่าการเปลี่ยนแปลงทั้งหมดจะถูกปรับใช้ กรุณาปรับใช้มุมมองแล้วลองอีกครั้ง
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=คุณไม่สามารถดูข้อมูลในมุมมอง SQLScript ได้จนกว่าการเปลี่ยนแปลงทั้งหมดจะถูกปรับใช้ กรุณาปรับใช้มุมมองแล้วลองอีกครั้ง
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=คุณไม่สามารถดูข้อมูลในมุมมองที่ได้รับการป้องกันโดยการควบคุมการเข้าถึงข้อมูลจนกว่าการเปลี่ยนแปลงทั้งหมดจะถูกปรับใช้ กรุณาปรับใช้มุมมองแล้วลองอีกครั้ง
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=คุณไม่สามารถดูข้อมูลในมุมมอง SQLScript ได้จนกว่าการเปลี่ยนแปลงทั้งหมดจะถูกปรับใช้ กรุณาปรับใช้มุมมองแล้วลองอีกครั้ง
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=คุณไม่สามารถดูข้อมูลในมุมมอง SQLScript ได้จนกว่าการเปลี่ยนแปลงทั้งหมดจะถูกปรับใช้ กรุณาปรับใช้มุมมองแล้วลองอีกครั้ง
cannotFormat=ไม่สามารถจัดรูปแบบคำสั่งได้
shouldHaveGroupBy=คิวรี SQL ควรมีส่วนคำสั่ง GROUP BY
groupByCols=ส่วนคำสั่ง GROUP BY ควรมีคอลัมน์ทั้งหมดที่ไม่รวมอยู่ในฟังก์ชันการรวม
@msgTableImported=อิมปอร์ตและปรับใช้ ''{0}''’, ''{1}''’ ได้สำเร็จ
@msgTableImportFailed=ปัญหาเกี่ยวกับการอิมปอร์ต: แหล่งข้อมูลยังไม่ถูกอิมปอร์ตลงในพื้นที่เก็บข้อมูล จึงไม่สามารถใช้ในมุมมองของคุณได้ เมื่อต้องการลองอีกครั้ง กรุณาปล่อยในเอดิเตอร์ใหม่
VAL_SAVE_CANCELLED=ผู้ใช้ได้ยกเลิกการอิมปอร์ต ''{0}''’
#XMSG
uniqueAlias=ชื่อแฝงของคอลัมน์ไม่ควรซ้ำกัน
#XMSG
ANALYSING_SQL=กำลังวิเคราะห์ SQL
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn=ไม่สามารถรับประเภทข้อมูลสำหรับ {0} กรุณาตรวจทานประเภทข้อมูลของคอลัมน์เหล่านี้ในแผง ''คุณสมบัติ''
#XBUT : SQL format button
format=รูปแบบ
#XMSG:Format failure message
formatError=รูปแบบล้มเหลว กรุณาแก้ไขข้อผิดพลาดทางไวยากรณ์
#XTOL
validateAndPreview=ตรวจสอบความถูกต้องของ SQL และแสดงตัวอย่างข้อมูล
#XTOL
validateAndViewData=ตรวจสอบความถูกต้องของ SQL และดูข้อมูล
#XMSG
unexpected_end=จบการทำงานโดยไม่คาดคิด
#XTOL
validate=ตรวจสอบความถูกต้องของ SQL
#XTOL
formattol=จัดรูปแบบ SQL

#begin of change management (pop-up)
#XTIT
changeManagementTitle=การอัพเดทแหล่งข้อมูล
#XCOL
changedOn=เปลี่ยนแปลงเมื่อ
#XCOL
changedBy=เปลี่ยนแปลงโดย

objectDisplayNameLocation=แหล่งข้อมูล
reviewText=กรุณาตรวจทานการปรับเปลี่ยนและเก็บบันทึก จากนั้นปรับใช้ออบเจคที่อัพเดทใหม่เพื่อนำไปใช้
changesText=แหล่งข้อมูลต่อไปนี้ที่ใช้ในออบเจคนี้ได้รับการปรับเปลี่ยนแล้ว และการปรับเปลี่ยนเหล่านี้จะถูกทำเครื่องหมายด้วยข้อความการตรวจสอบความถูกต้องในเอดิเตอร์:
sharedEntity=แชร์แล้ว
#end of change management (pop-up)

#XFLD
VALIDATEFLD=ตรวจสอบความถูกต้อง
#XFLD
CANCELVALIDATEFLD=ยกเลิกการตรวจสอบความถูกต้อง
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=ตารางการจับข้อมูลเดลต้า {0} ไม่ได้รับอนุญาตสำหรับการใช้
#XMSG
DATAVALIDATION=การตรวจสอบความถูกต้องของข้อมูล
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=ตารางหรือมุมมอง {0} มีข้อผิดพลาดเกี่ยวกับ Design Time หรือรันไทม์
# analyzer errors
#
superflous_comma=ย้ายเครื่องหมายจุลภาคออก
unterminated_path=การเลือกไม่สมบูรณ์
undefined_name_0=ตัวระบุ {0} ไม่ได้ถูกกำหนดหรือไม่มีอยู่
unresolved_col_0=ไม่พบคอลัมน์ {0}
unresolved_table_0=ไม่พบตารางหรือมุมมอง {0}
ambigous_col_definition_0_1_2=มีการกำหนดชื่อคอลัมน์เดียวกันมากกว่าหนึ่งครั้งใน {0}, {1} และ {2}
expected_scalar_typed_expression=ป้อนค่าเดียว
invalid_datatype_1_operator_2_expected_3=ตัวดำเนินการ {2} ต้องการประเภทข้อมูล {3} เปลี่ยนแปลงประเภทข้อมูล {1}
UNRESOLVED_TABLE_OR_MISSING_QUOTE=ไม่พบตารางหรือมุมมอง {0} เนื่องจากไม่มีอยู่หรือมีอัญประกาศคู่อย่างน้อยหนึ่งตัวขาดหายไป 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=ไม่พบคอลัมน์ {0} เนื่องจากไม่มีอยู่หรือมีอัญประกาศคู่อย่างน้อยหนึ่งตัวขาดหายไป 

