#XTIT Save Dialog param
modelNameSQL=Tampilan SQL
txtSQLFileValidationError=Tampilan model ini tidak valid. Harap perbaiki semua masalah validasi dan coba lagi.

#
# parser errors
#
0_not_supported={0} tidak didukung.
1_already_defined=Pengidentifikasi {1} sudah ditentukan.
column_order_and_index_order_specified=Urutan kolom dan urutan indeks tidak dapat ditentukan pada saat yang bersamaan.
annotation_0_not_supported_for_1=Anotasi {0} tidak didukung untuk {1}.

expected_1_found_0={1} diharapkan, tetapi ditemukan {0} sebagai gantinya.
expected_data_type=Jenis data yang diharapkan.
expected_expression=Ekspresi yang diharapkan.
expected_expression_or_subquery=Ekspresi atau subkueri yang diharapkan.
expected_first_or_last=FIRST atau LAST yang diharapkan.
expected_identifier=Pengidentifikasi yang diharapkan.
expected_identifier_or_asterisk=Pengidentifikasi atau * yang diharapkan.
expected_identifier_or_sql_error_code=Pengidentifikasi atau kode kesalahan SQL yang diharapkan.
expected_integer=Bilangan bulat yang diharapkan.
expected_language=Bahasa yang diharapkan.
expected_literal=Literal numerik atau string yang diharapkan.
expected_number=Literal numerik yang diharapkan.
expected_operator=Operator yang diharapkan.
expected_path_or_identifier=Jalur atau pengidentifikasi yang diharapkan.
expected_select_clause=SELECT yang diharapkan.
expected_select_or_set_operator=SELECT atau operator kumpulan yang diharapkan.
expected_sequence_option=Opsi urutan yang diharapkan.
expected_set_operator=Operator kumpulan yang diharapkan.
expected_simple_identifier=Pengidentifikasi sederhana yang diharapkan.
expected_statement=Pernyataan yang diharapkan.
expected_string=String yang diharapkan.
expected_subquery=Subkueri yang diharapkan.
expected_varref_found_identifier_use_1=Referensi variabel diharapkan, tetapi ditemukan pengidentifikasi, gunakan {1} sebagai gantinya.
#YMSG,145:SQL script semantic check warning message
txtSqlError=Kami tidak dapat menurunkan pernyataan pengembalian sepenuhnya dari skrip. Silakan tinjau daftar kolom dan jenis datanya dalam panel samping.
#XFLD: warning dialog title label
lblWarningDialogTitle=Peringatan
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=Mengubah bahasa menjadi "SQL (Kueri Standar)" akan mengakibatkan kesalahan untuk kata kunci SQLScript apa pun dalam kode Anda.
#XBUT:Warning dialog ok button for DB View type
btnOk=OKE
#XBUT,14:Warning dialog close button for DB View type
btnClose=Tutup
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=Anda tidak dapat melihat data dalam tampilan Skrip SQL hingga semua perubahan disebarkan. Silakan sebarkan tampilan tersebut dan coba lagi.
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=Anda tidak dapat melihat data dalam tampilan Skrip SQL hingga semua perubahan disebarkan. Silakan sebarkan tampilan tersebut dan coba lagi.
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=Anda tidak dapat melihat data dalam tampilan yang dilindungi oleh kontrol akses data hingga semua perubahan disebarkan. Silakan sebarkan tampilan tersebut dan coba lagi.
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=Anda tidak dapat melihat data dalam tampilan Skrip SQL hingga semua perubahan disebarkan. Silakan sebarkan tampilan tersebut dan coba lagi.
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=Anda tidak dapat melihat data dalam tampilan SkripSQL hingga semua perubahan disebarkan. Silakan sebarkan tampilan tersebut dan coba lagi.
cannotFormat=Pernyataan ini tidak dapat diformatkan.
shouldHaveGroupBy=Kueri SQL harus memiliki klausa GROUP BY.
groupByCols=Klausa GROUP BY harus memiliki semua kolom yang tidak termasuk dalam fungsi agregasi.
@msgTableImported=''{0}'',''{1}''’ berhasil diimpor dan disebarkan.
@msgTableImportFailed=Masalah dengan impor: Sumber tidak diimpor ke dalam repositori dan oleh karena itu tidak dapat digunakan dalam tampilan Anda. Untuk mencoba lagi, silakan jatuhkan kembali di editor.
VAL_SAVE_CANCELLED=Impor ''{0}'' telah dibatalkan.
#XMSG
uniqueAlias=Alias kolom harus unik.
#XMSG
ANALYSING_SQL=Menganalisis SQL
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn=Tipe data tidak dapat diturunkan untuk {0}. Silakan tinjau tipe data kolom ini dalam panel Properti.
#XBUT : SQL format button
format=Format
#XMSG:Format failure message
formatError=Format gagal. Perbaiki kesalahan sintaksis.
#XTOL
validateAndPreview=Validasi Data Pratinjau dan SQL
#XTOL
validateAndViewData=Validasi SQL dan Tampilkan Data
#XMSG
unexpected_end=Akhir yang tidak terduga
#XTOL
validate=Validasi SQL
#XTOL
formattol=Format SQL

#begin of change management (pop-up)
#XTIT
changeManagementTitle=Pembaruan Sumber
#XCOL
changedOn=Diubah Pada
#XCOL
changedBy=Diubah Oleh

objectDisplayNameLocation=Sumber
reviewText=Silakan tinjau modifikasi serta simpan dan sebar ulang objek yang diperbarui untuk menerapkannya.
changesText=Sumber-sumber berikut yang digunakan dalam objek ini telah dimodifikasi dan modifikasi tersebut ditandai oleh pesan validasi di editor:
sharedEntity=Dibagikan
#end of change management (pop-up)

#XFLD
VALIDATEFLD=Validasi
#XFLD
CANCELVALIDATEFLD=Batalkan Validasi
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=Tabel pemindaian delta {0} tidak diizinkan untuk dipakai.
#XMSG
DATAVALIDATION=Validasi Data
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=Tabel atau tampilan {0} memiliki kesalahan design-time atau runtime.
# analyzer errors
#
superflous_comma=Hapus koma
unterminated_path=Pemilihan tidak lengkap
undefined_name_0=Pengidentifikasi {0} belum ditentukan atau tidak ada.
unresolved_col_0=Kolom {0} tidak dapat ditemukan.
unresolved_table_0=Tabel atau tampilan {0} tidak dapat ditemukan.
ambigous_col_definition_0_1_2=Nama kolom yang sama ditentukan lebih dari satu kali di {0}, {1}, dan {2}.
expected_scalar_typed_expression=Masukkan nilai tunggal.
invalid_datatype_1_operator_2_expected_3=Operator {2} mengharapkan tipe data {3}. Ubah tipe data {1}.
UNRESOLVED_TABLE_OR_MISSING_QUOTE=Tabel atau tampilan {0} tidak ditemukan karena tabel atau tampilan tersebut tidak ada atau karena terdapat setidaknya satu tanda kutip ganda yang tidak lengkap. 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=Kolom {0} tidak ditemukan karena kolom tersebut tidak ada atau karena terdapat setidaknya satu tanda kutip ganda yang tidak lengkap. 

