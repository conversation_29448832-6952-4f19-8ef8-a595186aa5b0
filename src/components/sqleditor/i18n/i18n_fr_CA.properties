#XTIT Save Dialog param
modelNameSQL=Vue SQL
txtSQLFileValidationError=La vue du modèle n'est pas valide. Corrigez toutes les erreurs de validation et réessayez.

#
# parser errors
#
0_not_supported={0} non pris(e) en charge.
1_already_defined=L''identifiant {1} est déjà défini.
column_order_and_index_order_specified=Impossible de préciser l'ordre des colonnes et l'ordre des index en même temps.
annotation_0_not_supported_for_1=Annotation {0} non prise en charge pour {1}.

expected_1_found_0=Le système attendait {1}, mais a trouvé {0} à la place.
expected_data_type=Type de données attendu.
expected_expression=Expression attendue.
expected_expression_or_subquery=Expression ou sous-requête attendue.
expected_first_or_last=FIRST ou LAST attendu.
expected_identifier=Identifiant attendu.
expected_identifier_or_asterisk=Identifiant ou * attendu.
expected_identifier_or_sql_error_code=Identifiant ou code d'erreur SQL attendu.
expected_integer=Nombre entier attendu.
expected_language=Langage attendu.
expected_literal=Chaîne ou littéral numérique attendu(e).
expected_number=Littéral numérique attendu.
expected_operator=Opérateur attendu.
expected_path_or_identifier=Chemin d'accès ou identifiant attendu.
expected_select_clause=SELECT attendu.
expected_select_or_set_operator=Opérateur SELECT ou SET attendu.
expected_sequence_option=Option de séquence attendue.
expected_set_operator=Opérateur SET attendu.
expected_simple_identifier=Identifiant simple attendu.
expected_statement=Instruction attendue.
expected_string=Chaîne attendue.
expected_subquery=Sous-requête attendue.
expected_varref_found_identifier_use_1=Le système attendait une référence de variable, mais a trouvé un identifiant. Utilisation de {1} à la place.
#YMSG,145:SQL script semantic check warning message
txtSqlError=Impossible de dériver complètement l'instruction de retour du script. Examinez la liste des colonnes et types de données dans le panneau latéral.
#XFLD: warning dialog title label
lblWarningDialogTitle=Avert.
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=La modification du langage en "SQL (requête standard)" générera des erreurs si votre code contient des mots-clés SQLScript.
#XBUT:Warning dialog ok button for DB View type
btnOk=OK
#XBUT,14:Warning dialog close button for DB View type
btnClose=Fermer
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=Vous ne pouvez pas afficher les données d'une vue SQLScript tant que toutes les modifications n'ont pas été déployées. Déployez la vue et réessayez.
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=Vous ne pouvez pas afficher les données d'une vue SQLScript tant que toutes les modifications n'ont pas été déployées. Déployez la vue et réessayez.
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=Vous ne pouvez pas afficher les données d'une vue protégée par un contrôle d'accès aux données tant que toutes les modifications n'ont pas été déployées. Déployez la vue et réessayez.
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=Vous ne pouvez pas afficher les données d'une vue SQLScript tant que toutes les modifications n'ont pas été déployées. Déployez la vue et réessayez.
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=Vous ne pouvez pas afficher les données d'une vue SQLScript tant que toutes les modifications n'ont pas été déployées. Déployez la vue et réessayez.
cannotFormat=Impossible de mettre en forme l'instruction
shouldHaveGroupBy=La requête SQL doit avoir une clause REGROUPER PAR.
groupByCols=La clause REGROUPER PAR doit contenir toutes les colonnes qui ne sont pas incluses dans les fonctions d'agrégation.
@msgTableImported="{0}" et "{1}" ont été correctement importé(e)s et déployé(e)s.
@msgTableImportFailed=Problème lors de l'import : la source n'a pas été importée dans le référentiel. Elle ne peut donc pas être utilisée dans votre vue. Pour réessayer, déposez-la à nouveau dans l'éditeur.
VAL_SAVE_CANCELLED=L''import de "{0}" a été annulé.
#XMSG
uniqueAlias=Les alias de colonnes doivent être uniques.
#XMSG
ANALYSING_SQL=Analyse SQL en cours
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn=Imposs. dériver types données pour {0}. Veuillez réviser types données colonnes ds panneau Propriétés.
#XBUT : SQL format button
format=Mettre en forme
#XMSG:Format failure message
formatError=Échec de la mise en forme. Corrigez l'erreur de syntaxe.
#XTOL
validateAndPreview=Valider l'instruction SQL et l'aperçu des données
#XTOL
validateAndViewData=Valider l'instruction SQL et l'affichage des données
#XMSG
unexpected_end=Fin inattendue
#XTOL
validate=Valider l'instruction SQL
#XTOL
formattol=Mettre en forme SQL

#begin of change management (pop-up)
#XTIT
changeManagementTitle=Mises à jour de la source
#XCOL
changedOn=Date de modification
#XCOL
changedBy=Auteur de la modification

objectDisplayNameLocation=Source
reviewText=Veuillez contrôler les modifications, puis enregistrer et redéployer l'objet mis à jour pour appliquer les modifications.
changesText=Les sources suivantes utilisées dans cet objet ont été modifiées et ces modifications sont marquées par des messages de validation dans l'éditeur :
sharedEntity=Partagés
#end of change management (pop-up)

#XFLD
VALIDATEFLD=Valider
#XFLD
CANCELVALIDATEFLD=Annuler la validation
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=La table des captures delta {0} n''est pas autorisée pour la consommation.
#XMSG
DATAVALIDATION=Validation des données
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=La table ou la vue {0} comporte des erreurs de conception ou d''exécution.
# analyzer errors
#
superflous_comma=Retirer la virgule
unterminated_path=Sélection incomplète
undefined_name_0=L''identifiant {0} n''a pas été défini ou n''existe pas.
unresolved_col_0=La colonne {0} est introuvable.
unresolved_table_0=La table ou la vue {0} est introuvable.
ambigous_col_definition_0_1_2=Le même nom de colonne est défini plusieurs fois dans {0}, {1} et {2}.
expected_scalar_typed_expression=Saisissez une valeur unique.
invalid_datatype_1_operator_2_expected_3=L''opérateur {2} attend le type de données {3}. Modifiez le type de données {1}.
UNRESOLVED_TABLE_OR_MISSING_QUOTE=La table ou la vue {0} est introuvable car elle n''existe pas ou bien il manque au moins un guillemet double. 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=La colonne {0} est introuvable car elle n''existe pas ou bien il manque au moins un guillemet double. 

