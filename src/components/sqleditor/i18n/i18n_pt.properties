#XTIT Save Dialog param
modelNameSQL=Visão SQL
txtSQLFileValidationError=Visão de modelo inválida. Resolva todos os problemas de validação e tente novamente.

#
# parser errors
#
0_not_supported=Não há suporte a {0}.
1_already_defined=O identificador {1} já foi definido.
column_order_and_index_order_specified=As ordens de coluna e de índice não podem ser especificadas ao mesmo tempo.
annotation_0_not_supported_for_1=A anotação {0} não é suportada para {1}.

expected_1_found_0={1} esperado; {0} encontrado.
expected_data_type=Tipo de dados esperado.
expected_expression=Expressão esperada.
expected_expression_or_subquery=Expressão ou subconsulta esperada.
expected_first_or_last=FIRST ou LAST esperado.
expected_identifier=Identificador esperado.
expected_identifier_or_asterisk=Identificador ou * esperado.
expected_identifier_or_sql_error_code=Identificador ou código de erro SQL esperado.
expected_integer=Inteiro esperado.
expected_language=Linguagem esperada.
expected_literal=Literal de string ou numérico esperado.
expected_number=Literal numérico esperado.
expected_operator=Operador esperado.
expected_path_or_identifier=Caminho ou identificador esperado.
expected_select_clause=SELECT esperado.
expected_select_or_set_operator=SELECT ou operador SET esperado.
expected_sequence_option=Opção de sequência esperada.
expected_set_operator=Operador SET esperado.
expected_simple_identifier=Identificador simples esperado.
expected_statement=Instrução esperada.
expected_string=String esperada.
expected_subquery=Subconsulta esperada.
expected_varref_found_identifier_use_1=Referência de variável esperada, mas identificador encontrado; use {1} em vez disso.
#YMSG,145:SQL script semantic check warning message
txtSqlError=Não é possível derivar a instrução de retorno completamente do script. Revise a lista de colunas e respectivos tipos de dados no painel lateral.
#XFLD: warning dialog title label
lblWarningDialogTitle=Aviso
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=Alterar a linguagem para "SQL (consulta padrão)" retornará erros para qualquer palavra-chave SQLScript em seu código.
#XBUT:Warning dialog ok button for DB View type
btnOk=OK
#XBUT,14:Warning dialog close button for DB View type
btnClose=Fechar
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=Você não poderá ver os dados em uma visão SQLScript até que todas as alterações sejam implementadas. Implemente a visão e tente novamente.
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=Você não poderá ver os dados em uma visão SQLScript até que todas as alterações sejam implementadas. Implemente a visão e tente novamente.
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=Para ver os dados em um visão protegida por um controle de acesso aos dados, é necessário que todas as alterações sejam implementadas. Implemente a visão e tente novamente.
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=Você não poderá ver os dados em uma visão SQLScript até que todas as alterações sejam implementadas. Implemente a visão e tente novamente.
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=Você não poderá ver os dados em uma visão SQLScript até que todas as alterações sejam implementadas. Implemente a visão e tente novamente.
cannotFormat=Não é possível formatar a instrução.
shouldHaveGroupBy=A consulta SQL deve ter uma cláusula GROUP BY.
groupByCols=A cláusula GROUP BY deve ter todas as colunas que não estão incluídas em funções de agregação.
@msgTableImported="{0}"."{1}" importado e implementado com sucesso.
@msgTableImportFailed=Problema na importação: a fonte não foi importada para o repositório; portanto, não é possível usá-la na sua visão. Tente novamente, solte a fonte novamente no editor.
VAL_SAVE_CANCELLED=Importação de "{0}" cancelada.
#XMSG
uniqueAlias=Os aliases de coluna devem ser exclusivos.
#XMSG
ANALYSING_SQL=Analisando SQL
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn=Impossível derivar tipos de dados de {0}. Revise tipos de dados de colunas no painel Propriedades.
#XBUT : SQL format button
format=Formatar
#XMSG:Format failure message
formatError=Falha ao formatar. Corrija o erro de sintaxe.
#XTOL
validateAndPreview=Validar SQL e visualizar dados
#XTOL
validateAndViewData=Validar SQL e exibir dados
#XMSG
unexpected_end=Fim inesperado
#XTOL
validate=Validar SQL
#XTOL
formattol=Formatar SQL

#begin of change management (pop-up)
#XTIT
changeManagementTitle=Atualizações da fonte
#XCOL
changedOn=Alterado em
#XCOL
changedBy=Alterado por

objectDisplayNameLocation=Fonte
reviewText=Revise as modificações, salve e implemente novamente o objeto atualizado para aplicá-las.
changesText=As fontes a seguir usadas no objeto foram modificadas, e essas modificações estão marcadas por mensagens de validação no editor:
sharedEntity=Compartilhado
#end of change management (pop-up)

#XFLD
VALIDATEFLD=Validar
#XFLD
CANCELVALIDATEFLD=Cancelar validação
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=A tabela de captura de delta {0} não é permitida para consumo.
#XMSG
DATAVALIDATION=Validação de dados
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=A tabela ou visão {0} tem erros de tempo de design/execução.
# analyzer errors
#
superflous_comma=Remover vírgula
unterminated_path=Seleção incompleta
undefined_name_0=O identificador {0} ainda não foi definido ou não existe.
unresolved_col_0=Não é possível encontrar a coluna {0}.
unresolved_table_0=Não é possível encontrar a tabela ou visão {0}.
ambigous_col_definition_0_1_2=O mesmo nome de coluna está sendo definido mais de uma vez em {0}, {1} e {2}.
expected_scalar_typed_expression=Insira um valor individual.
invalid_datatype_1_operator_2_expected_3=O operador {2} espera o tipo de dados {3}. Altere o tipo de dados {1}.
UNRESOLVED_TABLE_OR_MISSING_QUOTE=A tabela ou visão {0} não pode ser encontrada, ela não existe ou há pelo menos uma aspa dupla ausente. 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=A coluna {0} não pode ser encontrada, ela não existe ou há pelo menos uma aspa dupla ausente. 

