#XTIT Save Dialog param
modelNameSQL=Widok SQL
txtSQLFileValidationError=Widok modelu jest nieprawidłowy. Rozwiąż wszystkie problemy z walidacją i spróbuj ponownie.

#
# parser errors
#
0_not_supported={0} nie jest obsługiwany.
1_already_defined=Identyfikator {1} został już zdefiniowany.
column_order_and_index_order_specified=Nie można w tym samym czasie określić kolejności kolumn i kolejności indeksów.
annotation_0_not_supported_for_1=Adnotacja {0} nie jest obsługiwana dla {1}.

expected_1_found_0=Oczekiwano {1}, ale zamiast tego znaleziono {0}.
expected_data_type=Oczekiwano typu danych.
expected_expression=Oczekiwano wyrażenia.
expected_expression_or_subquery=Oczekiwano wyrażenia lub zapytania podrzędnego.
expected_first_or_last=Oczekiwano FIRST lub LAST.
expected_identifier=Oczekiwano identyfikatora.
expected_identifier_or_asterisk=Oczekiwano identyfikatora lub *.
expected_identifier_or_sql_error_code=Oczekiwano identyfikatora lub kodu błędu SQL.
expected_integer=Oczekiwano liczby całkowitej.
expected_language=Oczekiwano języka.
expected_literal=Oczekiwano ciągu znaków lub literału liczbowego.
expected_number=Oczekiwano literału liczbowego.
expected_operator=Oczekiwano operatora.
expected_path_or_identifier=Oczekiwano ścieżki lub identyfikatora.
expected_select_clause=Oczekiwano SELECT.
expected_select_or_set_operator=Oczekiwano operatora SELECT lub SET.
expected_sequence_option=Oczekiwano opcji sekwencji.
expected_set_operator=Oczekiwano operatora SET.
expected_simple_identifier=Oczekiwano prostego identyfikatora.
expected_statement=Oczekiwano instrukcji.
expected_string=Oczekiwano ciągu znaków.
expected_subquery=Oczekiwano zapytania podrzędnego.
expected_varref_found_identifier_use_1=Oczekiwano referencji zmiennej, a znaleziono identyfikator, zamiast tego użyj {1}.
#YMSG,145:SQL script semantic check warning message
txtSqlError=Nie możemy wyprowadzić całej instrukcji return ze skryptu. Przejrzyj listę kolumn i ich typów danych w panelu bocznym.
#XFLD: warning dialog title label
lblWarningDialogTitle=Uwaga
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=Zmiana języka na "SQL (standardowe zapytanie)" spowoduje błędy w przypadku każdego słowa kluczowego SQLScript w kodzie.
#XBUT:Warning dialog ok button for DB View type
btnOk=OK
#XBUT,14:Warning dialog close button for DB View type
btnClose=Zamknij
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=Nie możesz wyświetlić danych w widoku SQLScript do momentu wdrożenia wszystkich zmian. Wdróż widok i spróbuj ponownie.
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=Nie możesz wyświetlić danych w widoku SQLScript do momentu wdrożenia wszystkich zmian. Wdróż widok i spróbuj ponownie.
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=Nie możesz wyświetlić danych w widoku chronionym przez kontrolę dostępu do danych do momentu wdrożenia wszystkich zmian. Wdróż widok i spróbuj ponownie.
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=Nie możesz wyświetlić danych w widoku SQLScript do momentu wdrożenia wszystkich zmian. Wdróż widok i spróbuj ponownie.
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=Nie możesz wyświetlić danych w widoku SQLScript do momentu wdrożenia wszystkich zmian. Wdróż widok i spróbuj ponownie.
cannotFormat=Nie można sformatować instrukcji.
shouldHaveGroupBy=Zapytanie SQL powinno zawierać klauzulę GRUPUJ WEDŁUG.
groupByCols=Klauzula GRUPUJ WEDŁUG powinna zawierać wszystkie kolumny, których nie uwzględniono w funkcjach agregacji.
@msgTableImported=Pomyślnie importowano i wdrożono "{0}", "{1}".
@msgTableImportFailed=Problem z importem: Źródło nie zostało importowane do repozytorium i nie można go użyć w widoku. Aby spróbować ponownie, upuść je jeszcze raz w edytorze.
VAL_SAVE_CANCELLED=Anulowano import "{0}".
#XMSG
uniqueAlias=Aliasy kolumn powinny być unikalne.
#XMSG
ANALYSING_SQL=Analiza SQL
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn=Nie można wyprowadzić typów danych dla {0}. Przejrzyj typy danych tych kolumn w panelu Właściwości.
#XBUT : SQL format button
format=Format
#XMSG:Format failure message
formatError=Format nie powiódł się. Popraw błąd składni.
#XTOL
validateAndPreview=Waliduj SQL i wyświetl podgląd danych
#XTOL
validateAndViewData=Waliduj SQL i wyświetl dane
#XMSG
unexpected_end=Nieoczekiwane zakończenie
#XTOL
validate=Waliduj SQL
#XTOL
formattol=Formatuj SQL

#begin of change management (pop-up)
#XTIT
changeManagementTitle=Aktualizacje źródła
#XCOL
changedOn=Data zmiany
#XCOL
changedBy=Autor zmiany

objectDisplayNameLocation=Źródło
reviewText=Przejrzyj modyfikacje, zapisz i ponownie wdróż zaktualizowany obiekt, aby je zastosować.
changesText=Poniższe źródła użyte w tym obiekcie zostały zmodyfikowane, a wprowadzone modyfikacje zostały oznaczone przez komunikaty walidacji w edytorze:
sharedEntity=Udostępnione
#end of change management (pop-up)

#XFLD
VALIDATEFLD=Waliduj
#XFLD
CANCELVALIDATEFLD=Anuluj walidację
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=Tabela rejestrowania delty {0} nie jest dozwolona do wykorzystania.
#XMSG
DATAVALIDATION=Sprawdzanie poprawności danych
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=Tabela lub widok {0} zawiera błędy czasu projektowania lub wykonania.
# analyzer errors
#
superflous_comma=Usuń przecinek
unterminated_path=Wybór niekompletny
undefined_name_0=Identyfikator {0} nie został zdefiniowany lub nie istnieje.
unresolved_col_0=Nie można znaleźć kolumny {0}.
unresolved_table_0=Nie można znaleźć tabeli lub widoku {0}.
ambigous_col_definition_0_1_2=Ta sama nazwa kolumny jest określona więcej niż raz w {0}, {1} i {2}.
expected_scalar_typed_expression=Wprowadź pojedynczą wartość.
invalid_datatype_1_operator_2_expected_3=Dla operatora {2} oczekiwany jest typ danych {3}. Zmień typ danych {1}.
UNRESOLVED_TABLE_OR_MISSING_QUOTE=Nie można znaleźć tabeli lub widoku {0}, ponieważ ten obiekt nie istnieje lub brakuje co najmniej jednego podwójnego cudzysłowu. 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=Nie można znaleźć kolumny {0}, ponieważ ona nie istnieje lub brakuje co najmniej jednego podwójnego cudzysłowu. 

