#XTIT Save Dialog param
modelNameSQL=Розріз даних
txtSQLFileValidationError=Подання моделі недійсне. Виправте всі проблеми з підтвердженням і повторіть спробу.

#
# parser errors
#
0_not_supported={0} не підтримується.
1_already_defined=Ідентифікатор {1} вже визначено.
column_order_and_index_order_specified=Порядок стовпчика та порядок індексу не можна визначати одночасно.
annotation_0_not_supported_for_1=Анотація {0} не підтримується для {1}.

expected_1_found_0=Очікувався {1}, але натомість знайдено {0}.
expected_data_type=Очікувався тип даних.
expected_expression=Очікувався вираз.
expected_expression_or_subquery=Очікувався  вираз або підзапит.
expected_first_or_last=Очікувався FIRST або LAST.
expected_identifier=Очікувався ідентифікатор.
expected_identifier_or_asterisk=Очікувався ідентифікатор або *.
expected_identifier_or_sql_error_code=Очікувався  ідентифікатор або код помилки SQL.
expected_integer=Очікувалося ціле значення.
expected_language=Очікувалася мова.
expected_literal=Очікувався рядок або числовий літерал.
expected_number=Очікувався числовий літерал.
expected_operator=Очікувався оператор.
expected_path_or_identifier=Очікувався шлях або ідентифікатор.
expected_select_clause=Очікувався SELECT.
expected_select_or_set_operator=Очікувався SELECT або оператор SET.
expected_sequence_option=Очікувався параметр послідовності.
expected_set_operator=Очікувався оператор SET.
expected_simple_identifier=Очікувався простий ідентифікатор.
expected_statement=Очікувалася інструкція.
expected_string=Очікувався рядок.
expected_subquery=Очікувався підзапит.
expected_varref_found_identifier_use_1=Очікувалося посилання на змінну, але знайдено ідентифікатор. Натомість використано {1}.
#YMSG,145:SQL script semantic check warning message
txtSqlError=Ми не можемо повністю отримати інструкцію зі скрипта. Перегляньте список стовпчиків і їх типи даних на бічній панелі.
#XFLD: warning dialog title label
lblWarningDialogTitle=Застереження
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=Змінення мови на "SQL (стандартний запит)" призведе до помилок для всіх ключових слів SQLScript у коді.
#XBUT:Warning dialog ok button for DB View type
btnOk=OK
#XBUT,14:Warning dialog close button for DB View type
btnClose=Закрити
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=У поданні SQLScript дані не можна бачити, доки не буде розгорнуто всі зміни. Розгорніть подання і спробуйте ще раз.
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=У поданні SQLScript дані не можна бачити, доки не буде розгорнуто всі зміни. Розгорніть подання і спробуйте ще раз.
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=У поданні, захищеному засобом контролю доступу до даних, дані не можна бачити, доки не буде розгорнуто всі зміни. Розгорніть подання і спробуйте ще раз.
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=У поданні SQLScript дані не можна бачити, доки не буде розгорнуто всі зміни. Розгорніть подання і спробуйте ще раз.
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=У поданні SQLScript дані не можна бачити, доки не буде розгорнуто всі зміни. Розгорніть подання і спробуйте ще раз.
cannotFormat=Інструкцію неможливо форматувати.
shouldHaveGroupBy=Запит SQL має містити речення GROUP BY.
groupByCols=Речення GROUP BY має містити всі стовпчики, які не додані до функцій агрегації.
@msgTableImported=''{0}''’, ''{1}''’ успішно імпортовано та розгорнуто.
@msgTableImportFailed=Проблема з імпортом: джерело не було імпортовано в сховище, тому його не можна використовувати у вашому поданні. Щоб повторити спробу, перетягніть його в редактор ще раз.
VAL_SAVE_CANCELLED=Імпорт ''{0}'' скасовано.
#XMSG
uniqueAlias=Псевдоніми стовпчиків мають бути унікальними.
#XMSG
ANALYSING_SQL=Аналіз SQL
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn=Неможливо отримати типи даних для {0}. Перегляньте типи даних у цих стовпчиках на панелі "Властивості".
#XBUT : SQL format button
format=Формат
#XMSG:Format failure message
formatError=Неприпустимий формат. Виправте синтаксичну помилку.
#XTOL
validateAndPreview=Перевірка даних SQL і попереднього перегляду
#XTOL
validateAndViewData=Перевірка даних SQL і даних подання
#XMSG
unexpected_end=Неочікуване завершення
#XTOL
validate=Перевірити SQL
#XTOL
formattol=Форматувати SQL

#begin of change management (pop-up)
#XTIT
changeManagementTitle=Оновлення джерел
#XCOL
changedOn=Дата зміни
#XCOL
changedBy=Автор зміни

objectDisplayNameLocation=Джерело
reviewText=Перегляньте зміни та збережіть і повторно розгорніть оновлений об’єкт, щоб застосувати їх.
changesText=Такі джерела, використані в цьому об’єкті, було змінено, і зміни позначено повідомленнями про перевірку в редакторі:
sharedEntity=Спільне використання
#end of change management (pop-up)

#XFLD
VALIDATEFLD=Перевірити
#XFLD
CANCELVALIDATEFLD=Скасувати перевірку
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=Використовувати таблицю дельта-захоплення "{0}" для споживання не дозволено.
#XMSG
DATAVALIDATION=Перевірка даних
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=Таблиця або подання "{0}" мають помилки часу розробки або часу виконання.
# analyzer errors
#
superflous_comma=Вилучити кому
unterminated_path=Не завершено вибір
undefined_name_0=Ідентифікатор {0} не визначено, або його не існує.
unresolved_col_0=Не вдалося знайти стовпчик "{0}".
unresolved_table_0=Не вдалося знайти таблицю або подання "{0}".
ambigous_col_definition_0_1_2=Одне й те саме ім''я стовпчика визначено кілька разів у "{0}", "{1}" і "{2}".
expected_scalar_typed_expression=Введіть одне значення.
invalid_datatype_1_operator_2_expected_3=Для оператора {2} очікується тип даних "{3}". Змініть тип даних "{1}".
UNRESOLVED_TABLE_OR_MISSING_QUOTE=Не вдалося знайти таблицю або подання "{0}" оскільки їх не існує або бракує принаймні однієї подвійної лапки. 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=Не вдалося знайти стовпчик "{0}" оскільки його не існує або бракує принаймні однієї подвійної лапки. 

