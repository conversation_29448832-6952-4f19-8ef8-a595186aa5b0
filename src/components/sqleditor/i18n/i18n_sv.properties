#XTIT Save Dialog param
modelNameSQL=SQL-vy
txtSQLFileValidationError=Modellvy är inte giltig. Åtgärda alla valideringsfel och försök igen.

#
# parser errors
#
0_not_supported={0} medges ej.
1_already_defined=Identifikator {1} har redan definierats.
column_order_and_index_order_specified=Ordningsföljd för kolumn och index kan inte anges samtidigt.
annotation_0_not_supported_for_1=Anmärkning {0} medges ej för {1}.

expected_1_found_0={1} förv<PERSON>nta<PERSON>, men {0} hittades.
expected_data_type=Datatyp förväntades.
expected_expression=Uttryck förväntades.
expected_expression_or_subquery=Uttryck eller underfråga förväntades.
expected_first_or_last=FIRST eller LAST förväntades.
expected_identifier=Identifikator förväntades.
expected_identifier_or_asterisk=Identifikator eller * förväntades.
expected_identifier_or_sql_error_code=Identifikator eller SQL-felkod förväntades.
expected_integer=Heltal förväntades.
expected_language=Språk förväntades.
expected_literal=Stäng eller numerisk litteral förväntades.
expected_number=Numerisk litteral förväntades.
expected_operator=Operator förväntades.
expected_path_or_identifier=Sökväg eller identifikator förväntades.
expected_select_clause=SELECT förväntades.
expected_select_or_set_operator=SELECT eller mängdoperator förväntades.
expected_sequence_option=Sekvensalternativ förväntades.
expected_set_operator=Mängdoperator förväntades.
expected_simple_identifier=Enkel identifikator förväntades.
expected_statement=Instruktion förväntades.
expected_string=Sträng förväntades.
expected_subquery=Underfråga förväntades.
expected_varref_found_identifier_use_1=Variabelreferens förväntades, men identifikator hittades. Använd {1} i stället.
#YMSG,145:SQL script semantic check warning message
txtSqlError=Returinstruktion kunde inte härledas fullständigt från skriptet. Granska listan över kolumner och deras datatyper i sidopanelen.
#XFLD: warning dialog title label
lblWarningDialogTitle=Varning
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=Om du ändrar språket till "SQL (standardfråga)" kommer detta orsaka fel för eventuella SQLScript-nyckelord i din kod.
#XBUT:Warning dialog ok button for DB View type
btnOk=OK
#XBUT,14:Warning dialog close button for DB View type
btnClose=Stäng
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=Du kan inte se data i en SQLScript-vy förrän alla ändringar har distribuerats. Distribuera vyn och försök igen.
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=Du kan inte se data i en SQLScript-vy förrän alla ändringar har distribuerats. Distribuera vyn och försök igen.
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=Du kan inte se data i en vy som skyddas av en dataåtkomstkontroll innan alla ändringar har distribuerats. Distribuera vyn och försök igen.
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=Du kan inte se data i en SQLScript-vy förrän alla ändringar har distribuerats. Distribuera vyn och försök igen.
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=Du kan inte se data i en SQLScript-vy förrän alla ändringar har distribuerats. Distribuera vyn och försök igen.
cannotFormat=Instruktion kan inte formateras.
shouldHaveGroupBy=SQL-fråga ska ha en GROUP BY-delinstruktion.
groupByCols=GROUP BY-delinstruktionen ska ha alla kolumner som inte inkluderas i aggregeringsfunktioner.
@msgTableImported=''{0}''’, ''{1}''’ har importerats och distribuerats.
@msgTableImportFailed=Problem med import: Källa har inte importerats till repository och kan inte användas i din vy. Försök igen genom att släppa den på nytt i editorn.
VAL_SAVE_CANCELLED=Import av ''{0}''’ har avbrutits.
#XMSG
uniqueAlias=Kolumnalias ska vara unika.
#XMSG
ANALYSING_SQL=Analyserar SQL
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn=Datatyper kunde inte härledas för {0}. Granska datatyper för dessa kolumner i panelen Egenskaper.
#XBUT : SQL format button
format=Format
#XMSG:Format failure message
formatError=Format misslyckades. Korrigera syntaxfel.
#XTOL
validateAndPreview=Validera SQL och förhandsgranska data
#XTOL
validateAndViewData=Validera SQL och visa data
#XMSG
unexpected_end=Oväntat slut
#XTOL
validate=Validera SQL
#XTOL
formattol=Formatera SQL

#begin of change management (pop-up)
#XTIT
changeManagementTitle=Källuppdateringar
#XCOL
changedOn=Ändring den
#XCOL
changedBy=Ändring av

objectDisplayNameLocation=Källa
reviewText=Granska ändringar och spara och omdistribuera uppdaterat objekt för tillämpning.
changesText=Följande källor som används i detta objekt har ändrats och ändringarna markeras med valideringsmeddelanden i editorn:
sharedEntity=Delad
#end of change management (pop-up)

#XFLD
VALIDATEFLD=Validera
#XFLD
CANCELVALIDATEFLD=Avbryt validering
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=Deltaregistreringstabell {0} tillåts inte för förbrukning.
#XMSG
DATAVALIDATION=Datavalidering
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=Tabell eller vy {0} innehåller designtids- eller körtidsfel.
# analyzer errors
#
superflous_comma=Ta bort komma
unterminated_path=Ofullständigt urval
undefined_name_0=Identifikator {0} har inte definierats eller finns ej.
unresolved_col_0=Kolumn {0} hittades ej.
unresolved_table_0=Tabell eller vy {0} hittades ej.
ambigous_col_definition_0_1_2=Samma kolumnnamn har definierats mer än en gång i {0}, {1} och {2}.
expected_scalar_typed_expression=Ange ett enskilt värde.
invalid_datatype_1_operator_2_expected_3=Operator {2} förväntar sig datatyp {3}. Ändra datatyp {1}.
UNRESOLVED_TABLE_OR_MISSING_QUOTE=Tabell eller vy {0} hittades ej eftersom den inte finns eller för att det saknas minst ett dubbelt citattecken. 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=Kolumnen {0} hittades ej eftersom den inte finns eller för att det saknas minst ett dubbelt citattecken. 

