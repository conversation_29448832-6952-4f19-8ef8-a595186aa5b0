#XTIT Save Dialog param
modelNameSQL=Pohled SQL
txtSQLFileValidationError=Pohled modelu je neplatný. Opravte chyby ověření a zkuste to znovu.

#
# parser errors
#
0_not_supported={0} nen<PERSON> podpor<PERSON>no.
1_already_defined=Identifikátor {1} je ji<PERSON> definován.
column_order_and_index_order_specified=Pořadí sloupců a pořadí indexů nelze zadat současně.
annotation_0_not_supported_for_1=Anotace {0} není podporována pro {1}.

expected_1_found_0=<PERSON><PERSON> {1}, ale namísto toho nalezeno {0}.
expected_data_type=Datový typ oček<PERSON>ván.
expected_expression=Výraz očekáván.
expected_expression_or_subquery=Výraz nebo dílčí dotaz očekáván.
expected_first_or_last=FIRST nebo LAST  očekáváno.
expected_identifier=Identifik<PERSON><PERSON>.
expected_identifier_or_asterisk=Identifikátor nebo * očekáváno.
expected_identifier_or_sql_error_code=Identifikátor nebo kód chyby SQL očekáván.
expected_integer=Kladné celé číslo očekáváno.
expected_language=Jazyk očekáván.
expected_literal=Řetězec nebo numerický literál očekáván.
expected_number=Numerický literál očekáván.
expected_operator=Operátor očekáván.
expected_path_or_identifier=Cesta nebo identifikátor očekávány.
expected_select_clause=SELECT očekáváno.
expected_select_or_set_operator=SELECT nebo operátor sady očekáván.
expected_sequence_option=Možnost sekvence očekávána.
expected_set_operator=Operátor sady očekáván.
expected_simple_identifier=Jednoduchý identifikátor očekáván.
expected_statement=Příkaz očekáván.
expected_string=Řetězec očekáván.
expected_subquery=Dílčí dotaz očekáván.
expected_varref_found_identifier_use_1=Odkaz na proměnnou očekáván, ale nalezen identifikátor, použijte namísto toho {1}.
#YMSG,145:SQL script semantic check warning message
txtSqlError=Nemůžeme odvodit příkaz vrácení kompletně ze skriptu. Zkontrolujte seznam sloupců a jejich datové typy na postranním panelu.
#XFLD: warning dialog title label
lblWarningDialogTitle=Upozor.
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=Změnou jazyka na "SQL (standardní dotaz)" vzniknou chyby pro některá klíčová slova SQLScript ve vašem kódu.
#XBUT:Warning dialog ok button for DB View type
btnOk=OK
#XBUT,14:Warning dialog close button for DB View type
btnClose=Zavřít
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=Data v pohledu SQLScript nelze vidět, dokud nebudou nasazeny všechny změny. Nasaďte tento pohled a zkuste to znovu.
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=Data v pohledu SQLScript nelze vidět, dokud nebudou nasazeny všechny změny. Nasaďte tento pohled a zkuste to znovu.
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=Data v pohledu chráněném řízením přístupu k datům nelze vidět, dokud nebudou nasazeny všechny změny. Nasaďte tento pohled a zkuste to znovu.
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=Data v pohledu SQLScript nelze vidět, dokud nebudou nasazeny všechny změny. Nasaďte tento pohled a zkuste to znovu.
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=Data v pohledu SQLScript nelze vidět, dokud nebudou nasazeny všechny změny. Nasaďte tento pohled a zkuste to znovu.
cannotFormat=Příkaz nelze formátovat.
shouldHaveGroupBy=Dotaz SQL má mít klauzuli GROUP BY.
groupByCols=Klauzule GROUP BY má mít všechny sloupce, které nejsou zahrnuté do agregačních funkcí.
@msgTableImported=''{0}'',''{1}''’ úspěšně importováno a nasazeno.
@msgTableImportFailed=Problém při importu: Zdroj nebyl importován do úložiště, a proto nemůže být používán ve vašem pohledu. Zkuste ho v editoru znovu umístit.
VAL_SAVE_CANCELLED=Import ''{0}''‘ byl zrušen.
#XMSG
uniqueAlias=Aliasy sloupců mají být jednoznačné.
#XMSG
ANALYSING_SQL=Analýza SQL
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn=Datové typy nebylo možné odvodit pro {0}. Zkontrolujte datové typy těchto sloupců na panelu Vlastnosti.
#XBUT : SQL format button
format=Formát
#XMSG:Format failure message
formatError=Formátování neúspěšné. Opravte chybu syntaxe.
#XTOL
validateAndPreview=Ověření SQL a náhled dat
#XTOL
validateAndViewData=Ověření SQL a zobrazení dat
#XMSG
unexpected_end=Neočekávaný konec
#XTOL
validate=Ověřit SQL
#XTOL
formattol=Formátovat SQL

#begin of change management (pop-up)
#XTIT
changeManagementTitle=Aktualizace zdroje
#XCOL
changedOn=Změněno dne
#XCOL
changedBy=Změnil

objectDisplayNameLocation=Zdroj
reviewText=Zkontrolujte úpravy a uložte a znovu nasaďte aktualizovaný objekt k dalšímu použití.
changesText=Následující zdroje použité v tomto objektu byly upraveny a tyto úpravy jsou označeny ověřovacími zprávami v editoru:
sharedEntity=Sdíleno
#end of change management (pop-up)

#XFLD
VALIDATEFLD=Ověřit
#XFLD
CANCELVALIDATEFLD=Zrušit ověření
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=Tabulka zaznamenání delta {0} je pro spotřebu nepřípustná.
#XMSG
DATAVALIDATION=Ověření dat
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=Tabulka nebo pohled {0} má chyby doby návrhu nebo doby běhu.
# analyzer errors
#
superflous_comma=Odebrat čárku
unterminated_path=Výběr neúplný
undefined_name_0=Identifikátor {0} nebyl definován nebo neexistuje.
unresolved_col_0=Sloupec {0} nelze najít.
unresolved_table_0=Tabulku nebo pohled {0} nelze najít.
ambigous_col_definition_0_1_2=Stejný sloupec byl definován vícekrát v {0}, {1} a {2}.
expected_scalar_typed_expression=Zadejte jednotlivou hodnotu.
invalid_datatype_1_operator_2_expected_3=Operátor {2} očekává datový typ {3}. Změňte datový typ {1}.
UNRESOLVED_TABLE_OR_MISSING_QUOTE=Tabulku nebo pohled {0} nelze najít, protože neexistuje nebo chybí alespoň jedna dvojitá uvozovka. 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=Sloupec {0} nelze najít, protože neexistuje nebo chybí alespoň jedna dvojitá uvozovka. 

