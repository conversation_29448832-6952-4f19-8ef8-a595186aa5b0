#XTIT Save Dialog param
modelNameSQL=Vista SQL
txtSQLFileValidationError=La vista de modelo no es válida. Solucione todos los problemas de validación y vuelva a intentar.

#
# parser errors
#
0_not_supported={0} no se admite.
1_already_defined=Ya se definió el identificador {1}.
column_order_and_index_order_specified=El orden de las columnas y el de los índices no se puede especificar al mismo tiempo.
annotation_0_not_supported_for_1=La anotación {0} no se admite para {1}.

expected_1_found_0=Se esperaba {1}, pero en su lugar se encontró {0}.
expected_data_type=Se esperaba el tipo de datos.
expected_expression=Se esperaba la expresión.
expected_expression_or_subquery=Se esperaba la expresión o subconsulta.
expected_first_or_last=Se esperaba FIRST o LAST.
expected_identifier=Se esperaba el identificador.
expected_identifier_or_asterisk=Se esperaba el identificador o *.
expected_identifier_or_sql_error_code=Se esperaba el identificador o el código de error SQL.
expected_integer=Se esperaba el entero.
expected_language=Se esperaba el lenguaje.
expected_literal=Se esperaba la string o el literal numérico.
expected_number=Se esperaba el literal numérico.
expected_operator=Se esperaba el operador.
expected_path_or_identifier=Se esperaba la ruta de acceso o el identificador.
expected_select_clause=Se esperaba SELECT.
expected_select_or_set_operator=Se esperaba SELECT o el operador SET.
expected_sequence_option=Se esperaba la opción de secuencia.
expected_set_operator=Se esperaba el operador SET.
expected_simple_identifier=Se esperaba el identificador simple.
expected_statement=Se esperaba la instrucción.
expected_string=Se esperaba la string.
expected_subquery=Se esperaba la subconsulta.
expected_varref_found_identifier_use_1=Se esperaba la referencia de variable, pero se encontró el identificador; use {1} en su lugar.
#YMSG,145:SQL script semantic check warning message
txtSqlError=Es imposible derivar la instrucción return completamente desde el script. Revise la lista de columnas y sus tipos de datos en el panel lateral.
#XFLD: warning dialog title label
lblWarningDialogTitle=Cuidado
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=El cambio de lenguaje a "SQL (consulta estándar)" dará errores para cualquier palabra clave de SQLScript en su código.
#XBUT:Warning dialog ok button for DB View type
btnOk=OK
#XBUT,14:Warning dialog close button for DB View type
btnClose=Cerrar
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=No puede ver datos en una vista SQLScript hasta que se hayan implementado todos los cambios. Implemente la vista y vuelva a intentarlo.
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=No puede ver datos en una vista SQLScript hasta que se hayan implementado todos los cambios. Implemente la vista y vuelva a intentarlo.
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=No puede ver datos en una vista protegida por un control de acceso de datos hasta que se hayan implementado todos los cambios. Implemente la vista y vuelva a intentarlo.
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=No puede ver datos en una vista SQLScript hasta que se hayan implementado todos los cambios. Implemente la vista y vuelva a intentarlo.
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=No puede ver datos en una vista SQLScript hasta que se hayan implementado todos los cambios. Implemente la vista y vuelva a intentarlo.
cannotFormat=La instrucción no se puede formatear.
shouldHaveGroupBy=La consulta de SQL debe tener una cláusula GROUP BY.
groupByCols=La cláusula GROUP BY debe tener todas las columnas que no están incluidas en las funciones de agregación.
@msgTableImported="{0}", "{1}''’ se importaron e implementaron correctamente.
@msgTableImportFailed=Problema con la importación: No se importó la fuente al repositorio y, por lo tanto, no se puede usar en su vista. Para volver a intentar, vuelva a soltarla en el editor.
VAL_SAVE_CANCELLED=El usuario canceló la importación de "{0}".
#XMSG
uniqueAlias=Los alias de columna deben ser únicos.
#XMSG
ANALYSING_SQL=Analizando SQL
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn=Tipos de datos no derivados para {0}. Revise tipos de datos de columnas en panel Propiedades.
#XBUT : SQL format button
format=Formato
#XMSG:Format failure message
formatError=Error de formato. Corrija el error de sintaxis.
#XTOL
validateAndPreview=Validar SQL y ver vista previa de datos
#XTOL
validateAndViewData=Validar SQL y ver los datos
#XMSG
unexpected_end=Final inesperado
#XTOL
validate=Validar SQL
#XTOL
formattol=SQL formal

#begin of change management (pop-up)
#XTIT
changeManagementTitle=Actualizaciones de origen
#XCOL
changedOn=Fecha de modificación
#XCOL
changedBy=Modificado por

objectDisplayNameLocation=Origen
reviewText=Revise las modificaciones, y guarde y vuelva a implementar el objeto actualizado para aplicarlas.
changesText=Se modificaron los siguientes orígenes utilizados en este objeto; las modificaciones se marcarán con mensajes de validación en el editor:
sharedEntity=Compartido
#end of change management (pop-up)

#XFLD
VALIDATEFLD=Validar
#XFLD
CANCELVALIDATEFLD=Cancelar validación
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=La tabla de captura delta {0} no está permitida para el consumo.
#XMSG
DATAVALIDATION=Validación de datos
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=La tabla o la vista {0} tienen errores de tiempo de diseño o de ejecución.
# analyzer errors
#
superflous_comma=Quitar coma
unterminated_path=Selección incompleta
undefined_name_0=El identificador {0} no se definió o no existe.
unresolved_col_0=No se puede encontrar la columna {0}.
unresolved_table_0=No se puede encontrar la tabla o la vista {0}.
ambigous_col_definition_0_1_2=El mismo nombre de columna está definido más de una vez en {0}, {1} y {2}.
expected_scalar_typed_expression=Ingrese un valor único.
invalid_datatype_1_operator_2_expected_3=El operador {2} espera el tipo de datos {3}. Cambie el tipo de datos {1}.
UNRESOLVED_TABLE_OR_MISSING_QUOTE=La tabla o vista {0} no se puede encontrar porque no existe o hay al menos una comilla doble faltante. 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=La columna {0} no se puede encontrar porque no existe o hay al menos una comilla doble faltante. 

