#XTIT Save Dialog param
modelNameSQL=SQL rakursas
txtSQLFileValidationError=Modelio rakursas negalioja. Ištaisykite visas tikrinimo klaidas ir bandykite dar kartą.

#
# parser errors
#
0_not_supported={0} nepalaikomas.
1_already_defined=Identifikatorius {1} jau apibrėžtas.
column_order_and_index_order_specified=Stulpelio tvarkos ir indekso tvarkos negalima apibrėžti vienu metu.
annotation_0_not_supported_for_1={1} anotacija {0} nepalaikoma.

expected_1_found_0=Buvo numatoma {1}, bet rasta {0}.
expected_data_type=Duomenų tipas numatytas.
expected_expression=Išraiška numatyta.
expected_expression_or_subquery=Išraiška arba antrinė užklausa numatyta.
expected_first_or_last=PIRMAS arba PASKUTINIS numatytas.
expected_identifier=Identifikatorius numatytas.
expected_identifier_or_asterisk=Identifikatorius arba * numatytas.
expected_identifier_or_sql_error_code=Identifikatoriaus arba SQL klaidos kodas numatytas.
expected_integer=Sveikasis skaičius numatytas.
expected_language=Kalba numatyta.
expected_literal=Eilutė arba skaitinis literalas numatytas.
expected_number=Skaitinis literalas numatytas.
expected_operator=Operatroius numatytas.
expected_path_or_identifier=Kelias arba identifikatorius numatytas.
expected_select_clause=PASIRINKTI numatytas
expected_select_or_set_operator=PASIRINKTI arba aibės operatorius numatytas.
expected_sequence_option=Sekos parinktis numatyta.
expected_set_operator=Aibės operatorius numatytas.
expected_simple_identifier=Vienpusis identifikatorius numatytas.
expected_statement=Pranešimas numatytas.
expected_string=Eilutė numatyta.
expected_subquery=Antrinė užklausa numatyta.
expected_varref_found_identifier_use_1=Kintamojo nuoroda numatyta, bet rastas identifikatorius, vietoje jo naudokite {1}.
#YMSG,145:SQL script semantic check warning message
txtSqlError=Nepavyksta iš scenarijaus iki galo išvesti grąžinimo pranešimo. Šoniniame skyde peržiūrėkite stulpelių sąrašą ir jų duomenų tipus.
#XFLD: warning dialog title label
lblWarningDialogTitle=Įspėj.
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=Kalbos pakeitimas į „SQL (standartinė užklausa)“ jūsų kode visiems „SQLScript“ raktažodžiams lems klaidas.
#XBUT:Warning dialog ok button for DB View type
btnOk=Gerai
#XBUT,14:Warning dialog close button for DB View type
btnClose=Uždaryti
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=Negalite matyti duomenų SQL scenarijuje, kol bus įdiegti visi keitimai. Įdiekite rakursą ir bandykite dar kartą.
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=Negalite matyti duomenų SQL scenarijuje, kol bus įdiegti visi keitimai. Įdiekite rakursą ir bandykite dar kartą.
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=Negalite matyti duomenų rakurse, apsaugotame taikant duomenų prieigos kontrolę, kol bus įdiegti visi keitimai. Įdiekite rakursą ir bandykite dar kartą.
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=Negalite matyti duomenų SQL scenarijuje, kol bus įdiegti visi keitimai. Įdiekite rakursą ir bandykite dar kartą.
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=Negalite matyti duomenų SQL scenarijuje, kol bus įdiegti visi keitimai. Įdiekite rakursą ir bandykite dar kartą.
cannotFormat=Pranešimo nepavyksta suformatuoti.
shouldHaveGroupBy=SQL užklausoje turėtų būti sąlyga GRUPUOTI PAGAL.
groupByCols=Sąlygoje GRUPUOTI PAGAL turi būti visi stulpeliai, kurie nėra įtraukti į agregavimo funkcijas.
@msgTableImported=„{0}“. „{1}“ sėkmingai importuota ir įdiegta.
@msgTableImportFailed=Problema importuojant: šaltinis nebuvo importuotas į saugyklą, todėl jo negalima naudoti rakurse. Norėdami bandyti dar kartą, dar kartą numeskite jį ant rengyklės.
VAL_SAVE_CANCELLED=Vartotojas atšaukė „{0}“ importavimą.
#XMSG
uniqueAlias=Stulpelių pseudonimai turi būti unikalūs.
#XMSG
ANALYSING_SQL=Analizuojama SQL
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn={0} duomenų tipų gauti nepavyko. Peržiūrėkite šių stulpelių duomenų tipus skydelyje Ypatybės.
#XBUT : SQL format button
format=Formatuoti
#XMSG:Format failure message
formatError=Formatuoti nepavyko. Ištaisykite sintaksės klaidą.
#XTOL
validateAndPreview=Tikrinti SQL ir peržiūros duomenis
#XTOL
validateAndViewData=Tikrinti SQL ir rakurso duomenis
#XMSG
unexpected_end=Nenumatyta pabaiga
#XTOL
validate=Tikrinti SQL
#XTOL
formattol=Formatuoti SQL

#begin of change management (pop-up)
#XTIT
changeManagementTitle=Šaltinio naujinimai
#XCOL
changedOn=Keitimo data
#XCOL
changedBy=Keitimo autorius

objectDisplayNameLocation=Šaltinis
reviewText=Peržiūrėkite modifikacijas, tada įrašykite ir dar kartą įdiekite atnaujintą objektą, kad galėtumėte taikyti.
changesText=Tolesni šiame objekte naudoti šaltiniai buvo modifikuoti; šios modifikacijos yra pažymėtos tikrinimo pranešimais rengyklėje:
sharedEntity=Bendrinama
#end of change management (pop-up)

#XFLD
VALIDATEFLD=Tikrinti
#XFLD
CANCELVALIDATEFLD=Atšaukti tikrinimą
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=Delta įvedimo lentelės {0} sunaudoti neleidžiama.
#XMSG
DATAVALIDATION=Duomenų patvirtinimas
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=Lentelė arba rodinys {0} turi kūrimo laiko arba vykdymo laiko klaidų.
# analyzer errors
#
superflous_comma=Pašalinti kablelį
unterminated_path=Pasirinkimas nebaigtas
undefined_name_0=Identifikatorius {0} neapibrėžtas arba jo nėra.
unresolved_col_0=Stulpelio {0} rasti nepavyko.
unresolved_table_0=Lentelės arba rakurso {0} rasti nepavyko.
ambigous_col_definition_0_1_2={0}, {1} ir {2} tas pats stulpelio pavadinimas apibrėžiamas daugiau nei vieną kartą.
expected_scalar_typed_expression=Įveskite atskirą reikšmę.
invalid_datatype_1_operator_2_expected_3=Operatorius {2} tikisi duomenų tipo {3}. Pakeiskite duomenų tipą {1}.
UNRESOLVED_TABLE_OR_MISSING_QUOTE=Lentelės ar rakurso {0} rasti nepavyko, nes jo nėra arba trūksta bent vienų dvigubų kabučių. 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=Stulpelio {0} rasti nepavyko, nes jo nėra arba trūksta bent vienų dvigubų kabučių. 

