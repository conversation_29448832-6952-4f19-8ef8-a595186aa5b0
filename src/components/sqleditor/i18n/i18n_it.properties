#XTIT Save Dialog param
modelNameSQL=Vista SQL
txtSQLFileValidationError=Vista del modello non valida. Correggere tutti gli errori di convalida e riprovare.

#
# parser errors
#
0_not_supported=Supporto di {0} non disponibile.
1_already_defined=L''identificatore {1} è già definito.
column_order_and_index_order_specified=Non è possibile specificare contemporaneamente l'ordine delle colonne e dell'indice.
annotation_0_not_supported_for_1=Annotazione {0} non supportata per {1}.

expected_1_found_0=Previsto {1}, ma rilevato {0}.
expected_data_type=Tipo di dati previsto.
expected_expression=Espressione prevista.
expected_expression_or_subquery=Espressione o sottoquery prevista.
expected_first_or_last=FIRST o LAST previsto.
expected_identifier=Identificatore previsto.
expected_identifier_or_asterisk=Identificatore o * previsto.
expected_identifier_or_sql_error_code=Identificatore o codice di errore SQL previsto.
expected_integer=Numero intero previsto.
expected_language=Lingua prevista.
expected_literal=Stringa o letterale numerico previsti.
expected_number=Letterale numerico previsto.
expected_operator=Operatore previsto.
expected_path_or_identifier=Percorso o identificatore previsto.
expected_select_clause=SELECT previsto.
expected_select_or_set_operator=SELECT o operatore impostato previsto.
expected_sequence_option=Opzione di sequenza prevista.
expected_set_operator=Operatore impostato previsto.
expected_simple_identifier=Identificatore semplice previsto.
expected_statement=Istruzione prevista.
expected_string=Stringa prevista.
expected_subquery=Sottoquery prevista.
expected_varref_found_identifier_use_1=Previsto riferimento variabile, ma rilevato identificatore; utilizzare {1}.
#YMSG,145:SQL script semantic check warning message
txtSqlError=Impossibile derivare l'intera istruzione di restituzione dallo script. Rivedere l'elenco di colonne e i tipi di dati nel pannello laterale.
#XFLD: warning dialog title label
lblWarningDialogTitle=Avviso
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=La modifica della lingua in "SQL (query standard)" genererà errori per qualsiasi parola chiave SQLScript nel codice.
#XBUT:Warning dialog ok button for DB View type
btnOk=OK
#XBUT,14:Warning dialog close button for DB View type
btnClose=Chiudi
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=Impossibile visualizzare i dati in una vista SQLScript fino alla distribuzione delle modifiche. Distribuire la vista e riprovare.
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=Impossibile visualizzare i dati in una vista SQLScript fino alla distribuzione delle modifiche. Distribuire la vista e riprovare.
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=Impossibile visualizzare i dati in una vista protetta da un controllo di accesso ai dati fino alla distribuzione delle modifiche. Distribuire la vista e riprovare.
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=Impossibile visualizzare i dati in una vista SQLScript fino alla distribuzione delle modifiche. Distribuire la vista e riprovare.
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=Impossibile visualizzare i dati in una vista SQLScript fino alla distribuzione delle modifiche. Distribuire la vista e riprovare.
cannotFormat=Impossibile formattare l'istruzione.
shouldHaveGroupBy=La query SQL deve presentare una clausola GROUP BY.
groupByCols=La clausola GROUP BY deve presentare tutte le colonne non incluse nelle funzioni di aggregazione.
@msgTableImported=Importazione e distribuzione di "{0}", "{1}" riuscite.
@msgTableImportFailed=Problema di importazione: l'origine non è stata importata nel repository e pertanto non può essere utilizzata nella vista. Per riprovare, rilasciarla nuovamente nell'editor.
VAL_SAVE_CANCELLED=L''importazione di "{0}" è stata annullata.
#XMSG
uniqueAlias=Gli alias colonna devono essere univoci.
#XMSG
ANALYSING_SQL=Analisi SQL
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn=Tipi di dati per {0} non derivati. Verificare i tipi di queste colonne nel pannello Proprietà.
#XBUT : SQL format button
format=Formato
#XMSG:Format failure message
formatError=Formato non riuscito. Correggere l'errore di sintassi.
#XTOL
validateAndPreview=Convalida SQL e visualizza anteprima dati
#XTOL
validateAndViewData=Convalida SQL e visualizza dati
#XMSG
unexpected_end=Fine non prevista
#XTOL
validate=Convalida SQL
#XTOL
formattol=Formatta SQL

#begin of change management (pop-up)
#XTIT
changeManagementTitle=Aggiornamenti origine
#XCOL
changedOn=Data di modifica
#XCOL
changedBy=Autore modifica

objectDisplayNameLocation=Origine
reviewText=Rivedere le modifiche, salvare e ridistribuire l'oggetto aggiornato per applicarle.
changesText=Le seguenti origini utilizzate in questo oggetto sono state modificate e tali modifiche verranno contrassegnate da messaggi di convalida nell'editor:
sharedEntity=Condiviso
#end of change management (pop-up)

#XFLD
VALIDATEFLD=Convalida
#XFLD
CANCELVALIDATEFLD=Annulla convalida
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=Non è consentito il consumo della tabella di acquisizione delta {0}.
#XMSG
DATAVALIDATION=Convalida dati
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=La tabella o la vista {0} presenta errori in fase di progettazione o run-time.
# analyzer errors
#
superflous_comma=Rimuovere la virgola
unterminated_path=Selezione incompleta
undefined_name_0=L''identificatore {0} non è stato definito o non esiste.
unresolved_col_0=Colonna {0} non trovata.
unresolved_table_0=Tabella o vista {0} non trovata.
ambigous_col_definition_0_1_2=Lo stesso nome di colonna è definito più volte in {0}, {1} e {2}.
expected_scalar_typed_expression=Immettere un valore singolo.
invalid_datatype_1_operator_2_expected_3=L''operatore {2} prevede il tipo di dati {3}. Modificare il tipo di dati {1}.
UNRESOLVED_TABLE_OR_MISSING_QUOTE=La tabella o la vista {0} non può essere trovata perché non esiste o perché manca almeno una virgoletta doppia. 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=La colonna {0} non può essere trovata perché non esiste o perché manca almeno una virgoletta doppia. 

