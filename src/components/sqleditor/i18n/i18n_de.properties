#XTIT Save Dialog param
modelNameSQL=SQL-View
txtSQLFileValidationError=Der Modell-View ist ungültig. Beheben Sie alle Validierungsprobleme, und versuchen Sie es erneut.

#
# parser errors
#
0_not_supported={0} wird nicht unterstützt.
1_already_defined=Der Identifikator {1} ist bereits definiert.
column_order_and_index_order_specified=Spaltenreihenfolge und Indexreihenfolge dürfen nicht gleichzeitig angegeben werden.
annotation_0_not_supported_for_1=Annotation {0} wird nicht unterstützt für {1}.

expected_1_found_0={1} wurde erwartet, stattdessen wurde {0} gefunden.
expected_data_type=Datentyp erwartet.
expected_expression=Ausdruck erwartet.
expected_expression_or_subquery=Ausdruck oder Unterabfrage erwartet.
expected_first_or_last=FIRST oder LAST erwartet.
expected_identifier=Identifikator erwartet.
expected_identifier_or_asterisk=Identifikator oder * erwartet.
expected_identifier_or_sql_error_code=Identifikator oder SQL-Fehlercode erwartet.
expected_integer=Ganzzahl erwartet.
expected_language=Sprache erwartet.
expected_literal=Zeichenfolge (String) oder Zahlenliteral erwartet.
expected_number=Zahlenliteral erwartet.
expected_operator=Operator erwartet.
expected_path_or_identifier=Pfad oder Identifikator erwartet.
expected_select_clause=SELECT erwartet.
expected_select_or_set_operator=SELECT oder SET-Operator erwartet.
expected_sequence_option=Reihenfolgeoption erwartet.
expected_set_operator=SET-Operator erwartet.
expected_simple_identifier=Einfacher Identifikator erwartet.
expected_statement=Anweisung erwartet.
expected_string=Zeichenfolge (String) erwartet.
expected_subquery=Unterabfrage erwartet.
expected_varref_found_identifier_use_1=Variablenreferenz erwartet, jedoch Identifikator gefunden; alternativ {1} verwenden.
#YMSG,145:SQL script semantic check warning message
txtSqlError=Rückgabeanweisung kann nicht vollständig aus dem Skript abgeleitet werden. Spaltenliste und ihre Datentypen im Seitenbereich prüfen.
#XFLD: warning dialog title label
lblWarningDialogTitle=Warnung
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=Wenn Sie die Sprache in "SQL (Standardabfrage)" ändern, werden Fehler zu allen SQLScript-Schlüsselwörtern in Ihrem Code angezeigt.
#XBUT:Warning dialog ok button for DB View type
btnOk=OK
#XBUT,14:Warning dialog close button for DB View type
btnClose=Schließen
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=Sie können die Daten in einem SQLScript-View erst sehen, wenn alle Änderungen aktiviert wurden. Aktivieren Sie den View und versuchen Sie es erneut.
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=Sie können die Daten in einem SQLScript-View erst sehen, wenn alle Änderungen aktiviert wurden. Aktivieren Sie den View und versuchen Sie es erneut.
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=Sie können die Daten in einem View, der durch eine Datenzugriffskontrolle geschützt ist, erst sehen, wenn alle Änderungen aktiviert wurden. Aktivieren Sie den View und versuchen Sie es erneut.
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=Sie können die Daten in einem SQLScript-View erst sehen, wenn alle Änderungen aktiviert wurden. Aktivieren Sie den View und versuchen Sie es erneut.
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=Sie können die Daten in einem SQLScript-View erst sehen, wenn alle Änderungen aktiviert wurden. Aktivieren Sie den View und versuchen Sie es erneut.
cannotFormat=Anweisung kann nicht formatiert werden.
shouldHaveGroupBy=Die SQL-Abfrage sollte eine "GROUP BY"-Klausel aufweisen.
groupByCols=Die "GROUP BY"-Klausel sollte alle Spalten aufweisen, die nicht in Aggregationsfunktionen eingeschlossen sind.
@msgTableImported=''{0}''.''{1}" wurden erfolgreich importiert und aktiviert.
@msgTableImportFailed=Problem beim Import: Die Quelle wurde nicht in das Repository importiert und kann daher nicht in Ihrem View verwendet werden. Versuchen Sie es erneut, indem Sie sie erneut in den Editor ziehen.
VAL_SAVE_CANCELLED=Der Import von "{0}" wurde abgebrochen.
#XMSG
uniqueAlias=Die Spalten-Aliasnamen sollten eindeutig sein.
#XMSG
ANALYSING_SQL=SQL wird analysiert
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn=Datentypen konnten für {0} nicht abgerufen werden. Prüfen Sie die Datentypen dieser Spalten im Bereich "Eigenschaften".
#XBUT : SQL format button
format=Formatieren
#XMSG:Format failure message
formatError=Formatieren fehlgeschlagen. Korrigieren Sie den Syntaxfehler.
#XTOL
validateAndPreview=SQL- und Vorschaudaten validieren
#XTOL
validateAndViewData=SQL validieren und Daten anzeigen
#XMSG
unexpected_end=Unerwartetes Ende
#XTOL
validate=SQL validieren
#XTOL
formattol=SQL formatieren

#begin of change management (pop-up)
#XTIT
changeManagementTitle=Quellenaktualisierung
#XCOL
changedOn=Geändert am
#XCOL
changedBy=Geändert von

objectDisplayNameLocation=Quelle
reviewText=Prüfen Sie Ihre Änderungen und sichern Sie sie. Aktivieren Sie das aktualisierte Objekt anschließend erneut, um sie zu übernehmen.
changesText=Die folgenden im Objekt verwendeten Quellen wurden geändert. Diese Änderungen sind im Editor mit Validierungsmeldungen versehen:
sharedEntity=Geteilt
#end of change management (pop-up)

#XFLD
VALIDATEFLD=Validieren
#XFLD
CANCELVALIDATEFLD=Validierung abbrechen
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=Die Tabelle für Delta-Erfassung {0} ist für die Verwendung nicht zulässig.
#XMSG
DATAVALIDATION=Datenvalidierung
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=Tabelle oder View {0} weist Entwurfszeit- oder Laufzeitfehler auf.
# analyzer errors
#
superflous_comma=Komma entfernen
unterminated_path=Auswahl unvollständig
undefined_name_0=Identifikator {0} wurde nicht definiert oder ist nicht vorhanden.
unresolved_col_0=Spalte {0} wurde nicht gefunden.
unresolved_table_0=Tabelle oder View {0} wurde nicht gefunden.
ambigous_col_definition_0_1_2=Derselbe Spaltenname wird in {0}, {1} und {2} mehrfach definiert.
expected_scalar_typed_expression=Geben Sie einen Einzelwert ein.
invalid_datatype_1_operator_2_expected_3=Der Operator {2} erwartet den Datentyp {3}. Ändern Sie den Datentyp {1}.
UNRESOLVED_TABLE_OR_MISSING_QUOTE=Tabelle oder View {0} wurde nicht gefunden, da sie/er nicht vorhanden ist oder mindestens ein doppeltes Anführungszeichen fehlt. 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=Spalte {0} wurde nicht gefunden, da sie nicht vorhanden ist oder mindestens ein doppeltes Anführungszeichen fehlt. 

