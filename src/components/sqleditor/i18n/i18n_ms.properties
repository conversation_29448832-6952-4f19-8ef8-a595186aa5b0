#XTIT Save Dialog param
modelNameSQL=Paparan SQL
txtSQLFileValidationError=Paparan model tidak sah. Baiki semua isu pengesahan dan cuba lagi.

#
# parser errors
#
0_not_supported={0} tidak disokong.
1_already_defined=Pengenal pasti {1} telah ditakrifkan.
column_order_and_index_order_specified=Tentukan pesanan lajur dan indeks pada masa yang berlainan.
annotation_0_not_supported_for_1=Anotasi {0} tidak disokong untuk {1}.

expected_1_found_0={1} telah dijangka tetapi sebaliknya {0} ditemui.
expected_data_type=Jenis data dijangka.
expected_expression=Ungkapan dijangka.
expected_expression_or_subquery=Ungkapan atau subpertanyaan dijangka.
expected_first_or_last=FIRST atau LAST dijangka.
expected_identifier=Pengenal pasti dijangka.
expected_identifier_or_asterisk=Pengenal pasti atau * dijangka.
expected_identifier_or_sql_error_code=Pengenal pasti atau kod ralat SQL dijangka.
expected_integer=Integer dijangka.
expected_language=Bahasa dijangka.
expected_literal=Rentetan atau harafiah berangka dijangka.
expected_number=Harafiah berangka dijangka.
expected_operator=Pengendali dijangka.
expected_path_or_identifier=Laluan atau pengenal pasti dijangka.
expected_select_clause=SELECT dijangka.
expected_select_or_set_operator=PILIH atau set pengendali dijangka.
expected_sequence_option=Pilihan jujukan dijangka.
expected_set_operator=Set pengendali dijangka.
expected_simple_identifier=Pengenal pasti mudah dijangka.
expected_statement=Penyataan dijangka.
expected_string=Rentetan dijangka.
expected_subquery=Subpertanyaan dijangka.
expected_varref_found_identifier_use_1=Rujukan pemboleh ubah dijangka tetapi pengenal pasti yang ditemui, sebaliknya gunakan {1}.
#YMSG,145:SQL script semantic check warning message
txtSqlError=Kami tidak dapat menerbitkan penyataan pulangan selengkapnya daripada skrip. Semak semula senarai lajur dan jenis datanya dalam panel sampingan.
#XFLD: warning dialog title label
lblWarningDialogTitle=Amaran
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=Perubahan bahasa kepada "SQL (Pertanyaan Piawai)" akan menyebabkan ralat pada mana-mana kata kunci SQLScript dalam kod anda.
#XBUT:Warning dialog ok button for DB View type
btnOk=OK
#XBUT,14:Warning dialog close button for DB View type
btnClose=Tutup
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=Atur duduk semua perubahan untuk melihat data dalam paparan SQLScript. Sila atur duduk paparan dan cuba semula.
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=Atur duduk semua perubahan untuk melihat data dalam paparan SQLScript. Sila atur duduk paparan dan cuba semula.
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=Atur duduk semua perubahan untuk melihat data dalam paparan yang dilindungi oleh kawalan capaian data. Sila atur duduk paparan dan cuba semula.
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=Atur duduk semua perubahan untuk melihat data dalam paparan SQLScript. Sila atur duduk paparan dan cuba semula.
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=Atur duduk semua perubahan untuk melihat data dalam paparan SQLScript. Sila atur duduk paparan dan cuba semula.
cannotFormat=Penyata tidak berjaya diformatkan.
shouldHaveGroupBy=Pertanyaan SQL perlu mempunyai klausa KUMPULAN MENGIKUT.
groupByCols=Klausa KUMPULAN MENGIKUT perlu mempunyai semua lajur yang tidak termasuk dalam fungsi pengagregatan.
@msgTableImported=Berjaya mengimport dan mengatur duduk ''{0}''’, ''{1}''’.
@msgTableImportFailed=Masalah dengan import. Sumber tidak diimport ke dalam repositori, oleh itu, ia tidak boleh digunakan dalam paparan anda. Untuk cuba lagi, lepaskannya semula pada editor.
VAL_SAVE_CANCELLED=Import ''{0}''’ telah dibatalkan.
#XMSG
uniqueAlias=Alias lajur perlulah unik.
#XMSG
ANALYSING_SQL=Analisis SQL
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn=Jenis data boleh boleh diterbitkan untuk {0}. Semak jenis data bagi lajur ini dalam panel Sifat.
#XBUT : SQL format button
format=Format
#XMSG:Format failure message
formatError=Format gagal. Betulkan ralat sintaks.
#XTOL
validateAndPreview=Sahkan SQL dan Data Pratonton
#XTOL
validateAndViewData=Sahkan SQL dan Data Paparan
#XMSG
unexpected_end=Akhir yang tidak dijangka
#XTOL
validate=Sahkan SQL
#XTOL
formattol=Format SQL

#begin of change management (pop-up)
#XTIT
changeManagementTitle=Pengemaskinian Sumber
#XCOL
changedOn=Diubah pada
#XCOL
changedBy=Diubah oleh

objectDisplayNameLocation=Sumber
reviewText=Semak pengubahsuaian dan simpan serta atur duduk semula objek yang dikemas kini untuk menggunakannya.
changesText=Sumber berikut digunakan dalam objek ini yang telah diubah suai, dan pengubahsuaian ini ditanda mengikut mesej pengesahan dalam editor:
sharedEntity=Dikongsi
#end of change management (pop-up)

#XFLD
VALIDATEFLD=Sahkan
#XFLD
CANCELVALIDATEFLD=Batalkan Pengesahan
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=Tiada kebenaran bagi jadual tangkapan delta {0} untuk penggunaan.
#XMSG
DATAVALIDATION=Pengesahan Data
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=Jadual atau paparan {0} mempunyai ralat masa reka bentuk atau masa jalanan.
# analyzer errors
#
superflous_comma=Keluarkan koma
unterminated_path=Pemilihan tidak lengkap
undefined_name_0=Pengenal pasti {0} belum ditakrifkan atau tidak wujud.
unresolved_col_0=Lajur {0} tidak ditemui.
unresolved_table_0=Jadual atau paparan {0} tidak ditemui.
ambigous_col_definition_0_1_2=Nama lajur yang sama ditakrifkan lebih daripada sekali dalam {0}, {1}, dan {2}.
expected_scalar_typed_expression=Masukkan nilai tunggal.
invalid_datatype_1_operator_2_expected_3=Pengendali {2} menjangkakan jenis data {3}. Ubah jenis data {1}.
UNRESOLVED_TABLE_OR_MISSING_QUOTE=Jadual atau paparan {0} tidak ditemui kerana ia tidak wujud atau terdapat sekurang-kurangnya satu petikan berganda yang tiada. 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=Lajur {0} tidak ditemui kerana ia tidak wujud atau terdapat sekurang-kurangnya satu petikan berganda yang tiada. 

