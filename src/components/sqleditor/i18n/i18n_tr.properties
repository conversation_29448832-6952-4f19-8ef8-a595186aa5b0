#XTIT Save Dialog param
modelNameSQL=SQL görünümü
txtSQLFileValidationError=Model görünümü geçerli değil. Tüm doğrulama sorunlarını düzeltin ve tekrar deneyin.

#
# parser errors
#
0_not_supported={0} desteklenmiyor.
1_already_defined={1} tanıtıcısı zaten tanımlı.
column_order_and_index_order_specified=Sütun sırası ve dizin sırası aynı anda belirtilemez.
annotation_0_not_supported_for_1={0} açıklaması {1} için desteklenmiyor.

expected_1_found_0={1} beklendi ancak {0} bulundu.
expected_data_type=Veri türü beklenir.
expected_expression=İfade beklenir.
expected_expression_or_subquery=İfade veya alt sorgu beklenir.
expected_first_or_last=FIRST veya LAST beklenir.
expected_identifier=Tanıtıc<PERSON> beklenir.
expected_identifier_or_asterisk=Tanıtıcı veya * beklenir.
expected_identifier_or_sql_error_code=Tanıtıcı veya SQL hata kodu beklenir.
expected_integer=Tamsayı beklenir.
expected_language=Dil beklenir.
expected_literal=Dize veya sayısal sabit değer beklenir.
expected_number=Sayısal sabit değer beklenir.
expected_operator=İşleç beklenir.
expected_path_or_identifier=Yol veya tanıtıcı beklenir.
expected_select_clause=SELECT beklenir.
expected_select_or_set_operator=SELECT veya küme işleci beklenir.
expected_sequence_option=Sıra seçeneği beklenir.
expected_set_operator=Küme işleci beklenir.
expected_simple_identifier=Basit tanıtıcı beklenir.
expected_statement=Deyim beklenir.
expected_string=Dize beklenir.
expected_subquery=Alt sorgu beklenir.
expected_varref_found_identifier_use_1=Değişken referans beklendi ancak tanıtıcı bulundu, bunun yerine {1} kullanın.
#YMSG,145:SQL script semantic check warning message
txtSqlError=Return deyimini komut dosyasından tam olarak türetemedik. Sütunların listesini ve yan paneldeki veri türlerini inceleyin.
#XFLD: warning dialog title label
lblWarningDialogTitle=Uyarı
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=Dilin "SQL (standart sorgu)" olarak değiştirilmesi kodunuzdaki SQLScript anahtar sözcükleri için hata oluşmasına neden olur.
#XBUT:Warning dialog ok button for DB View type
btnOk=Tamam
#XBUT,14:Warning dialog close button for DB View type
btnClose=Kapat
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=Tüm değişiklikler dağıtılana kadar SQLScript görünümünde verileri göremezsiniz. Görünümü dağıtıp tekrar deneyin.
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=Tüm değişiklikler dağıtılana kadar SQLScript görünümünde verileri göremezsiniz. Görünümü dağıtıp tekrar deneyin.
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=Tüm değişiklikler dağıtılana kadar veri erişimi denetimi tarafından korunan bir görünümde verileri göremezsiniz. Görünümü dağıtıp tekrar deneyin.
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=Tüm değişiklikler dağıtılana kadar SQLScript görünümünde verileri göremezsiniz. Görünümü dağıtıp tekrar deneyin.
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=Tüm değişiklikler dağıtılana kadar SQLScript görünümünde verileri göremezsiniz. Görünümü dağıtıp tekrar deneyin.
cannotFormat=Deyim biçimlendirilemiyor.
shouldHaveGroupBy=SQL sorgusu GRUPLAMA ÖLÇÜTÜ koşulu içermelidir.
groupByCols=GRUPLAMA ÖLÇÜTÜ koşulu, toplama işlevlerine dahil edilmeyen tüm sütunları içermelidir.
@msgTableImported=''{0}''’, ''{1}''’ başarıyla içe aktarıldı ve dağıtıldı.
@msgTableImportFailed=İçe aktarımla ilgili bir sorun oluştu: Kaynak, havuza dahil edilmedi ve bu nedenle, görünümünüzde kullanılamıyor. Tekrar denemek için, kaynağı, düzenleyiciye yeniden bırakın.
VAL_SAVE_CANCELLED=''{0}''’ içe aktarımı iptal edildi.
#XMSG
uniqueAlias=Sütun takma adları benzersiz olmalıdır.
#XMSG
ANALYSING_SQL=SQL analiz ediliyor
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn={0} için veri türleri türetilemiyor. Özellikler panelinde bu sütunların veri türlerini gözden geçirin.
#XBUT : SQL format button
format=Biçim
#XMSG:Format failure message
formatError=Biçim başarısız oldu. Sözdizim hatasını düzeltin.
#XTOL
validateAndPreview=SQL'i doğrula ve verileri önizle
#XTOL
validateAndViewData=SQL'i doğrula ve verileri görüntüle
#XMSG
unexpected_end=Beklenmeyen bitiş
#XTOL
validate=SQL'i doğrula
#XTOL
formattol=SQL'i biçimlendir

#begin of change management (pop-up)
#XTIT
changeManagementTitle=Kaynak güncellemeleri
#XCOL
changedOn=Değişiklik tarihi
#XCOL
changedBy=Değiştiren

objectDisplayNameLocation=Kaynak
reviewText=Değişiklikleri gözden geçirin ve bunları uygulamak için güncellenen nesneyi kaydedin ve yeniden dağıtın.
changesText=Bu nesnede kullanılan şu kaynaklar değiştirildi ve değişiklikler düzenleyicide doğrulama iletileriyle işaretlendi:
sharedEntity=Paylaşılan
#end of change management (pop-up)

#XFLD
VALIDATEFLD=Doğrula
#XFLD
CANCELVALIDATEFLD=Doğrulamayı iptal et
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT={0} delta yakalama tablosuna tüketim için izin verilmiyor.
#XMSG
DATAVALIDATION=Veri doğrulama
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=Tablo veya görünüm {0}, tasarım süresi veya çalıştırma süresi hataları içeriyor.
# analyzer errors
#
superflous_comma=Virgülü kaldır
unterminated_path=Seçim eksik
undefined_name_0={0} tanıtıcısı, tanımlanmadı veya mevcut değil.
unresolved_col_0={0} sütunu bulunamıyor.
unresolved_table_0=Tablo veya görünüm {0} bulunamıyor.
ambigous_col_definition_0_1_2={0}, {1} ve {2} içinde aynı sütun adı birden fazla kez tanımlanıyor.
expected_scalar_typed_expression=Münferit değer girin.
invalid_datatype_1_operator_2_expected_3={2} işleci, {3} veri türünü bekliyor. {1} veri türünü değiştirin.
UNRESOLVED_TABLE_OR_MISSING_QUOTE=Tablo veya görünüm {0}, mevcut olmadığından veya en az bir eksik çift tırnak olduğundan bulunamıyor. 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=Sütun {0}, mevcut olmadığından veya en az bir eksik çift tırnak olduğundan bulunamıyor. 

