#XTIT Save Dialog param
modelNameSQL=SQL-visning
txtSQLFileValidationError=Modellvisningen er ikke gyldig. Løs alle valideringsproblemer og prøv på nytt.

#
# parser errors
#
0_not_supported={0} støttes ikke.
1_already_defined=Identifikatoren {1} er allerede definert.
column_order_and_index_order_specified=Rekkefølgen for kolonne og indeks kan ikke spesifiseres samtidig.
annotation_0_not_supported_for_1=Merknaden {0} støttes ikke for {1}.

expected_1_found_0=Forventet {1}, men fant {0} i stedet.
expected_data_type=Datatype forventet.
expected_expression=Uttrykk forventet.
expected_expression_or_subquery=Uttrykk eller delspørring forventet.
expected_first_or_last=FIRST eller LAST forventet.
expected_identifier=Identifikator forventet.
expected_identifier_or_asterisk=Identifikator eller * forventet.
expected_identifier_or_sql_error_code=Identifikator eller SQL-feilkode forventet.
expected_integer=Heltall forventet.
expected_language=Språk forventet.
expected_literal=Streng eller numerisk litteral forventet.
expected_number=Numerisk litteral forventet.
expected_operator=Operator forventet.
expected_path_or_identifier=Bane eller identifikator forventet.
expected_select_clause=SELECT forventet.
expected_select_or_set_operator=SELECT eller definert operator forventet.
expected_sequence_option=Sekvensalternativ forventet.
expected_set_operator=SET-operator forventet.
expected_simple_identifier=Enkel identifikator forventet.
expected_statement=Utsagn forventet.
expected_string=Streng forventet.
expected_subquery=Delspørring forventet.
expected_varref_found_identifier_use_1=Forventet variabelreferanse, men fant identifikator. Bruk {1} i stedet.
#YMSG,145:SQL script semantic check warning message
txtSqlError=Vi kan ikke avlede returutsagnet helt fra skriptet. Gå gjennom listen over kolonner og datatypene i sidepanelet.
#XFLD: warning dialog title label
lblWarningDialogTitle=Adv.
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=Endring av språk til "SQL (standardspørring)" vil føre til feil for alle SQLScript-nøkkelord i koden.
#XBUT:Warning dialog ok button for DB View type
btnOk=OK
#XBUT,14:Warning dialog close button for DB View type
btnClose=Lukk
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=Du kan ikke se data i en SQLScript-visning før alle endringene er distribuert. Distribuer visningen og prøv på nytt.
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=Du kan ikke se data i en SQLScript-visning før alle endringene er distribuert. Distribuer visningen og prøv på nytt.
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=Du kan ikke se data i en visning som er beskyttet av datatilgangskontroll før alle endringene er distribuert. Distribuer visningen og prøv på nytt.
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=Du kan ikke se data i en SQLScript-visning før alle endringene er distribuert. Distribuer visningen og prøv på nytt.
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=Du kan ikke se data i en SQLScript-visning før alle endringene er distribuert. Distribuer visningen og prøv på nytt.
cannotFormat=Setningen kan ikke formateres.
shouldHaveGroupBy=SQL-spørringen skal ha en GROUP BY-setning.
groupByCols=GROUP BY-setningen skal ha alle kolonnene som ikke er inkludert i aggregeringsfunksjonene.
@msgTableImported="{0}", "{1}" er importert og distribuert.
@msgTableImportFailed=Problem under import: Kilden ble ikke importert til repository og kan dermed ikke brukes i visningen din. Slipp den inn i redigeringsprogrammet på nytt for å prøve igjen.
VAL_SAVE_CANCELLED=Import av "{0}" er avbrutt.
#XMSG
uniqueAlias=Kolonnealiaser kal være unike.
#XMSG
ANALYSING_SQL=Analyserer SQL
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn=Kan ikke avlede datatyper for {0}. Kontroller datatypene for disse kolonnene i panelet Egenskaper.
#XBUT : SQL format button
format=Format
#XMSG:Format failure message
formatError=Format mislyktes. Korriger syntaksfeilen.
#XTOL
validateAndPreview=Valider SQL- og forhåndsvisningsdata
#XTOL
validateAndViewData=Valider SQL- og visningsdata
#XMSG
unexpected_end=Uventet slutt
#XTOL
validate=Valider SQL
#XTOL
formattol=Formater SQL

#begin of change management (pop-up)
#XTIT
changeManagementTitle=Kildeoppdateringer
#XCOL
changedOn=Endret den
#XCOL
changedBy=Endret av

objectDisplayNameLocation=Kilde
reviewText=Kontroller endringene og lagre og implementer det oppdaterte objektet på nytt for å bruke dem.
changesText=Følgende kilder som brukes i dette objektet, er endret og disse endringene er merket med valideringsmeldinger i redigeringsprogrammet:
sharedEntity=Delt
#end of change management (pop-up)

#XFLD
VALIDATEFLD=Valider
#XFLD
CANCELVALIDATEFLD=Avbryt validering
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=Deltaregistreringstabell {0} er ikke tillatt for forbruk.
#XMSG
DATAVALIDATION=Datavalidering
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=Tabellen eller visningen {0} har designtids- eller kjøringstidsfeil.
# analyzer errors
#
superflous_comma=Fjern komma
unterminated_path=Utvalget er ufullstendig
undefined_name_0=ID-en {0} er ikke definert eller finnes ikke.
unresolved_col_0=Finner ikke kolonnen {0}.
unresolved_table_0=Finner ikke tabell eller visning {0}.
ambigous_col_definition_0_1_2=Det samme kolonnenavnet defineres flere ganger i {0}, {1} og {2}.
expected_scalar_typed_expression=Oppgi en enkeltverdi.
invalid_datatype_1_operator_2_expected_3=Operatoren {2} forventer datatype {3}. Endre datatype {1}.
UNRESOLVED_TABLE_OR_MISSING_QUOTE=Finner ikke tabell eller visning {0} fordi den ikke finnes eller det mangler minst ett dobbelt anførselstegn.  
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=Finner ikke kolonne {0} fordi den ikke finnes eller det mangler minst ett dobbelt anførselstegn.  

