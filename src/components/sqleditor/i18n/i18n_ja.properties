#XTIT Save Dialog param
modelNameSQL=SQL ビュー
txtSQLFileValidationError=モデルビューが無効です。チェックの問題をすべて修正してからもう一度実行してください。

#
# parser errors
#
0_not_supported={0} はサポートされていません。
1_already_defined=識別子 {1} はすでに定義されています。
column_order_and_index_order_specified=列順序とインデックス順序を同時に指定することはできません。
annotation_0_not_supported_for_1=注釈 {0} は {1} ではサポートされていません。

expected_1_found_0={1} が必要でしたが、代わりに {0} が見つかりました。
expected_data_type=データ型が必要です。
expected_expression=式が必要です。
expected_expression_or_subquery=式またはサブクエリが必要です。
expected_first_or_last=FIRST または LAST が必要です。
expected_identifier=識別子が必要です。
expected_identifier_or_asterisk=識別子または * が必要です。
expected_identifier_or_sql_error_code=識別子または SQL エラーコードが必要です。
expected_integer=整数が必要です。
expected_language=言語が必要です。
expected_literal=文字列または数値リテラルが必要です。
expected_number=数値リテラルが必要です。
expected_operator=演算子が必要です。
expected_path_or_identifier=パスまたは識別子が必要です。
expected_select_clause=SELECT が必要です。
expected_select_or_set_operator=SELECT または set 演算子が必要です。
expected_sequence_option=順序オプションが必要です。
expected_set_operator=set 演算子が必要です。
expected_simple_identifier=単純識別子が必要です。
expected_statement=文が必要です。
expected_string=文字列が必要です。
expected_subquery=サブクエリが必要です。
expected_varref_found_identifier_use_1=変数参照が必要でしたが、識別子が見つかりました。代わりに {1} を使用してください。
#YMSG,145:SQL script semantic check warning message
txtSqlError=スクリプトから return 文を完全に誘導できません。サイドパネルで列およびそのデータ型の一覧を確認してください。
#XFLD: warning dialog title label
lblWarningDialogTitle=警告
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=言語を "SQL (標準クエリ)" に変更すると、コード内の SQLScript キーワードのすべてでエラーが発生します。
#XBUT:Warning dialog ok button for DB View type
btnOk=OK
#XBUT,14:Warning dialog close button for DB View type
btnClose=閉じる
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=すべての変更がデプロイされるまで、SQLScript ビューでデータを表示できません。ビューをデプロイしてから、もう一度実行してください。
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=すべての変更がデプロイされるまで、SQLScript ビューでデータを表示できません。ビューをデプロイしてから、もう一度実行してください。
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=すべての変更がデプロイされるまで、データアクセス制御によって保護されたビューでデータを表示できません。ビューをデプロイしてから、もう一度実行してください。
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=すべての変更がデプロイされるまで、SQLScript ビューでデータを表示できません。ビューをデプロイしてから、もう一度実行してください。
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=すべての変更がデプロイされるまで、SQLScript ビューでデータを表示できません。ビューをデプロイしてから、もう一度実行してください。
cannotFormat=文の書式を設定できません。
shouldHaveGroupBy=SQL クエリには GROUP BY 句が必要です。
groupByCols=GROUP BY 句には、集計関数に含まれていない列をすべて指定する必要があります。
@msgTableImported="{0}"、"{1}" が正常にインポートおよびデプロイされました。
@msgTableImportFailed=インポートの問題: ソースがリポジトリにインポートされなかったため、ビューで使用できません。もう一度試すには、エディタでソースを再ドロップしてください。
VAL_SAVE_CANCELLED="{0}" のインポートがキャンセルされました。
#XMSG
uniqueAlias=列エイリアスは一意にしてください。
#XMSG
ANALYSING_SQL=SQL を分析しています
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn={0} のデータ型を確定できませんでした。プロパティパネルで、これらの列のデータ型を確認してください。
#XBUT : SQL format button
format=フォーマット
#XMSG:Format failure message
formatError=フォーマットが失敗しました。構文エラーを修正してください。
#XTOL
validateAndPreview=SQL のチェックおよびデータのプレビュー
#XTOL
validateAndViewData=SQL のチェックおよびデータの表示
#XMSG
unexpected_end=予期しない終了
#XTOL
validate=SQL をチェック
#XTOL
formattol=SQL を書式化する

#begin of change management (pop-up)
#XTIT
changeManagementTitle=ソース更新
#XCOL
changedOn=変更日付
#XCOL
changedBy=変更者

objectDisplayNameLocation=ソース
reviewText=変更内容をレビューし、更新されたオブジェクトを保存および再デプロイして適用してください。
changesText=このオブジェクトで使用されている以下のソースが変更されました。これらの変更は、エディタ内でチェックメッセージによってマークされます。
sharedEntity=共有済み
#end of change management (pop-up)

#XFLD
VALIDATEFLD=チェック
#XFLD
CANCELVALIDATEFLD=チェックをキャンセル
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=デルタキャプチャテーブル {0} の利用は許可されていません。
#XMSG
DATAVALIDATION=データ検証
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=テーブルまたはビュー {0} に設計時エラーまたは実行時エラーがあります。
# analyzer errors
#
superflous_comma=カンマの削除
unterminated_path=選択が不完全です
undefined_name_0=ID {0} が定義されていないか、存在しません。
unresolved_col_0=列 {0} が見つかりません。
unresolved_table_0=テーブルまたはビュー {0} が見つかりません。
ambigous_col_definition_0_1_2={0}、{1}、および {2} で同じ列名が複数回定義されています。
expected_scalar_typed_expression=単一の値を入力してください。
invalid_datatype_1_operator_2_expected_3=演算子 {2} ではデータ型 {3} が想定されています。データ型 {1} を変更してください。
UNRESOLVED_TABLE_OR_MISSING_QUOTE=存在しないか、二重引用符が少なくとも 1 つ欠落しているため、テーブルまたはビュー {0} が見つかりません。 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=存在しないか、二重引用符が少なくとも 1 つ欠落しているため、列 {0} が見つかりません。 

