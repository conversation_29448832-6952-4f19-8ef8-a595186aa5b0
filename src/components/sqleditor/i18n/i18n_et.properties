#XTIT Save Dialog param
modelNameSQL=SQL-vaade
txtSQLFileValidationError=Mudelivaade ei sobi. Parandage kõik valideerimisprobleemid ja proovige uuesti.

#
# parser errors
#
0_not_supported={0} ei toetata.
1_already_defined=Tunnus {1} on juba määratletud.
column_order_and_index_order_specified=Veerujärjestust ja indeksijärjestust ei saa samaaegselt määrata.
annotation_0_not_supported_for_1=Annotatsiooni {0} ei toetata - {1}.

expected_1_found_0=Eeldati {1}, kuid leiti hoopis {0}.
expected_data_type=Eeldati andmetüüpi.
expected_expression=Eeldati avaldist.
expected_expression_or_subquery=Eeldati avaldist või alampäringut.
expected_first_or_last=Eeldati väärtust FIRST või LAST.
expected_identifier=Eeldati tunnust.
expected_identifier_or_asterisk=Eeldati tunnust või väärtust *.
expected_identifier_or_sql_error_code=Eeldati tunnust või SQL-tõrkekoodi.
expected_integer=Eeldati täisarvu.
expected_language=Eeldati keelt.
expected_literal=Eeldati stringi või arvliteraali.
expected_number=Eeldati arvliteraali.
expected_operator=Eeldati tehtemärki.
expected_path_or_identifier=Eeldati teed või tunnust.
expected_select_clause=Eeldati väärtust SELECT.
expected_select_or_set_operator=Eeldati väärtust SELECT või kogumi tehtemärki.
expected_sequence_option=Eeldati järjestussuvandit.
expected_set_operator=Eeldati kogumi tehtemärki.
expected_simple_identifier=Eeldati lihttunnust.
expected_statement=Eeldati lauset.
expected_string=Eeldati stringi.
expected_subquery=Eeldati alampäringut.
expected_varref_found_identifier_use_1=Eeldati muutujaviidet,kuid leiti tunnus, kasutage hoopis {1}.
#YMSG,145:SQL script semantic check warning message
txtSqlError=Me ei saa tagastuslauset täielikult skriptist tuletada. Kontrollige veergude ja nende andmetüüpide loendit külgpaanil.
#XFLD: warning dialog title label
lblWarningDialogTitle=Hoiatus
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=Keele muutmine keeleks „SQL (standardne päring)“ annab tõrketeate kõigi teie koodi SQLScripti märksõnade puhul.
#XBUT:Warning dialog ok button for DB View type
btnOk=OK
#XBUT,14:Warning dialog close button for DB View type
btnClose=Sule
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=Te ei näe SQLScripti vaates olevaid andmeid enne kõigi muudatuste juurutamist. Juurutage vaade ja proovige uuesti.
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=Te ei näe SQLScripti vaates olevaid andmeid enne kõigi muudatuste juurutamist. Juurutage vaade ja proovige uuesti.
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=Te ei näe andmepääsu juhtelemendiga kaitstud vaates olevaid andmeid enne kõigi muudatuste juurutamist. Juurutage vaade ja proovige uuesti.
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=Te ei näe SQLScripti vaates olevaid andmeid enne kõigi muudatuste juurutamist. Juurutage vaade ja proovige uuesti.
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=Te ei näe SQLScripti vaates olevaid andmeid enne kõigi muudatuste juurutamist. Juurutage vaade ja proovige uuesti.
cannotFormat=Lauset ei saa vormindada.
shouldHaveGroupBy=SQL-päringul peab olema klausel GROUP BY.
groupByCols=Klauslil GROUP BY peavad olema kõik veerud, mis pole kaasatud agregeerimisfunktsioonidesse.
@msgTableImported=„{0}“.„{1}“ on imporditud ja juurutatud.
@msgTableImportFailed=Probleem importimisega: allikat ei imporditud hoidlasse ja seetõttu ei saa seda teie vaates kasutada. Uuesti proovimiseks kukutage see uuesti redaktorisse.
VAL_SAVE_CANCELLED=Kasutaja tühistas üksuse „{0}“ impordi.
#XMSG
uniqueAlias=Veeru pseudonüümid peavad olemas kordumatud.
#XMSG
ANALYSING_SQL=SQL-i analüüsimine
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn={0} jaoks ei saanud andmetüüpe tuletada. Kontrollige nende veergude andmetüüpe paanil Atribuudid.
#XBUT : SQL format button
format=Vorminda
#XMSG:Format failure message
formatError=Vormindamine nurjus. Parandage süntaksiviga.
#XTOL
validateAndPreview=Valideeri SQL ja kuva andmete eelvaade
#XTOL
validateAndViewData=Valideeri SQL ja kuva andmed
#XMSG
unexpected_end=Ootamatu lõpp
#XTOL
validate=Valideeri SQL
#XTOL
formattol=Vorminda SQL

#begin of change management (pop-up)
#XTIT
changeManagementTitle=Allika uuendused
#XCOL
changedOn=Muutmiskuupäev
#XCOL
changedBy=Muutja

objectDisplayNameLocation=Allikas
reviewText=Kontrollige muudatusi ja salvestage ning juurutage uuendatud objekt nende rakendamiseks uuesti.
changesText=Selles objektis kasutatud järgmised allikad on muudetud ja need muudatused on märgitud redaktoris valideerimisteadetega:
sharedEntity=Jagatud
#end of change management (pop-up)

#XFLD
VALIDATEFLD=Valideeri
#XFLD
CANCELVALIDATEFLD=Tühista valideerimine
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=Deltahõivetabel {0} pole tarbimiseks lubatud.
#XMSG
DATAVALIDATION=Andmete kinnitamine
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=Tabelil või vaatel {0} on kujundusaja või käitusaja tõrkeid.
# analyzer errors
#
superflous_comma=Eemalda koma
unterminated_path=Valik on puudulik
undefined_name_0=Identifikaatorit {0} pole määratletud või pole olemas.
unresolved_col_0=Veergu {0} ei leidu.
unresolved_table_0=Tabelit või vaadet {0} ei leidu.
ambigous_col_definition_0_1_2=Sama veerunimi on siin määratletud rohkem kui üks kord: {0}, {1} ja {2}.
expected_scalar_typed_expression=Sisestage üksikväärtus.
invalid_datatype_1_operator_2_expected_3=Operaator {2} eeldab andmetüüpi {3}. Muutke andmetüüpi {1}.
UNRESOLVED_TABLE_OR_MISSING_QUOTE=Tabelit või vaadet {0} ei leitud, kuna seda pole olemas või siis on puudu vähemalt üks kahekordne jutumärk. 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=Veergu {0} ei leitud, kuna seda pole olemas või siis on puudu vähemalt üks kahekordne jutumärk. 

