#XTIT Save Dialog param
modelNameSQL=SQL 뷰
txtSQLFileValidationError=모델 뷰가 유효하지 않습니다. 모든 유효성 확인 이슈를 수정하고 다시 시도하십시오.

#
# parser errors
#
0_not_supported={0}은(는) 지원되지 않습니다.
1_already_defined={1} 식별자는 이미 정의되어 있습니다.
column_order_and_index_order_specified=열 순서와 인덱스 순서를 동시에 지정할 수 없습니다.
annotation_0_not_supported_for_1={0} 주석은 {1}에 지원되지 않습니다.

expected_1_found_0={1}을(를) 예상했는데 {0}이(가) 있습니다.
expected_data_type=데이터 유형이 필요합니다.
expected_expression=표현식이 필요합니다.
expected_expression_or_subquery=표현식 또는 하위 쿼리가 필요합니다.
expected_first_or_last=FIRST 또는 LAST가 필요합니다.
expected_identifier=식별자가 필요합니다.
expected_identifier_or_asterisk=식별자 또는 *가 필요합니다.
expected_identifier_or_sql_error_code=식별자 또는 SQL 오류 코드가 필요합니다.
expected_integer=정수가 필요합니다.
expected_language=언어가 필요합니다.
expected_literal=문자열 또는 숫자 리터럴이 필요합니다.
expected_number=숫자 리터럴이 필요합니다.
expected_operator=연산자가 필요합니다.
expected_path_or_identifier=경로 또는 식별자가 필요합니다.
expected_select_clause=SELECT가 필요합니다.
expected_select_or_set_operator=SELECT 또는 SET 연산자가 필요합니다.
expected_sequence_option=순서 옵션이 필요합니다.
expected_set_operator=SET 연산자가 필요합니다.
expected_simple_identifier=단순 식별자가 필요합니다.
expected_statement=명령문이 필요합니다.
expected_string=문자열이 필요합니다.
expected_subquery=하위 쿼리가 필요합니다.
expected_varref_found_identifier_use_1=변수 참조를 예상했는데 식별자가 있습니다. {1}을(를) 사용하십시오.
#YMSG,145:SQL script semantic check warning message
txtSqlError=스크립트에서 리턴 문을 완전히 추출할 수 없습니다. 사이드 패널에서 열 리스트와 해당 데이터 유형을 검토하십시오.
#XFLD: warning dialog title label
lblWarningDialogTitle=경고
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=코드의 SQLScript 키워드에서 언어를 "SQL(표준 쿼리)"로 변경하면 오류가 발생합니다.
#XBUT:Warning dialog ok button for DB View type
btnOk=확인
#XBUT,14:Warning dialog close button for DB View type
btnClose=닫기
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=모든 변경사항이 배포될 때까지는 SQLScript 뷰에서 데이터를 볼 수 없습니다. 뷰를 배포하고 다시 시도하십시오.
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=모든 변경사항이 배포될 때까지는 SQLScript 뷰에서 데이터를 볼 수 없습니다. 뷰를 배포하고 다시 시도하십시오.
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=모든 변경사항이 배포될 때까지는 데이터 액세스 제어로 보호되는 뷰에서 데이터를 볼 수 없습니다. 뷰를 배포하고 다시 시도하십시오.
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=모든 변경사항이 배포될 때까지는 SQLScript 뷰에서 데이터를 볼 수 없습니다. 뷰를 배포하고 다시 시도하십시오.
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=모든 변경사항이 배포될 때까지는 SQLScript 뷰에서 데이터를 볼 수 없습니다. 뷰를 배포하고 다시 시도하십시오.
cannotFormat=명령문에 형식을 지정할 수 없습니다.
shouldHaveGroupBy=SQL 쿼리에 GROUP BY 절이 필요합니다.
groupByCols=집계 함수에 포함되지 않은 모든 열이 GROUP BY 절에 포함되어야 합니다.
@msgTableImported="{0}", "{1}"이(가) 임포트되고 배포되었습니다.
@msgTableImportFailed=임포트에 문제가 있습니다. 소스가 저장소에 임포트되지 않아서 뷰에서 사용할 수 없습니다. 다시 시도하려면 편집기에 다시 끌어 놓으십시오.
VAL_SAVE_CANCELLED="{0}"의 임포트를 취소했습니다.
#XMSG
uniqueAlias=열 별칭은 고유해야 합니다.
#XMSG
ANALYSING_SQL=SQL 분석 중
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn={0}의 데이터 유형을 추출할 수 없습니다. 속성 패널에서 해당 열의 데이터 유형을 검토하십시오.
#XBUT : SQL format button
format=형식
#XMSG:Format failure message
formatError=형식화에 실패했습니다. 구문 오류를 수정하십시오.
#XTOL
validateAndPreview=SQL 유효성 확인 및 데이터 미리보기
#XTOL
validateAndViewData=SQL 유효성 확인 및 데이터 보기
#XMSG
unexpected_end=예상되지 않은 종료
#XTOL
validate=SQL 유효성 확인
#XTOL
formattol=SQL 형식화

#begin of change management (pop-up)
#XTIT
changeManagementTitle=소스 업데이트
#XCOL
changedOn=변경일
#XCOL
changedBy=변경자

objectDisplayNameLocation=소스
reviewText=수정사항을 검토하고 저장한 다음 업데이트된 오브젝트를 재배포하여 적용하십시오.
changesText=이 오브젝트에 사용되는 다음과 같은 소스가 수정되었으며 이 수정 내용은 편집기에서 유효성 확인 메시지로 표시됩니다.
sharedEntity=공유됨
#end of change management (pop-up)

#XFLD
VALIDATEFLD=유효성 확인
#XFLD
CANCELVALIDATEFLD=유효성 확인 취소
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=델타 캡처 테이블 {0}은(는) 사용할 수 없습니다.
#XMSG
DATAVALIDATION=데이터 유효성 확인
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=테이블 또는 뷰 {0}에 디자인 타임 또는 런타임 오류가 있습니다.
# analyzer errors
#
superflous_comma=쉼표 제거
unterminated_path=선택이 불완전합니다.
undefined_name_0=식별자 {0}이(가) 정의되지 않았거나 존재하지 않습니다.
unresolved_col_0=열 {0}을(를) 찾을 수 없습니다.
unresolved_table_0=테이블 또는 뷰 {0}을(를) 찾을 수 없습니다.
ambigous_col_definition_0_1_2=동일한 열 이름이 {0}, {1}, {2}에서 여러 번 정의되고 있습니다.
expected_scalar_typed_expression=단일 값을 입력하십시오.
invalid_datatype_1_operator_2_expected_3=연산자 {2}에 데이터 유형 {3}이(가) 필요합니다. 데이터 유형 {1}을(를) 변경하십시오.
UNRESOLVED_TABLE_OR_MISSING_QUOTE=테이블이나 뷰 {0}이(가) 존재하지 않거나 큰따옴표가 하나 이상 누락되어서 해당 테이블이나 뷰를 찾을 수 없습니다. 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=열 {0}이(가) 존재하지 않거나 큰따옴표가 하나 이상 누락되어서 해당 열을 찾을 수 없습니다. 

