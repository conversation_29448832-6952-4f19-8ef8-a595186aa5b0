#XTIT Save Dialog param
modelNameSQL=SQL skats
txtSQLFileValidationError=Šis modeļa skats nav derīgs. Izlabojiet visas pārbaudes problēmas un mēģiniet vēlreiz.

#
# parser errors
#
0_not_supported={0} nav atbalstīts.
1_already_defined=Identifikators {1} ir jau definēts.
column_order_and_index_order_specified=Kolonnu secību un indeksu secību nevar norādīt vienlaikus.
annotation_0_not_supported_for_1=Anotācija {0} nav atbalstīta šim: {1}.

expected_1_found_0=Bija gaidīts {1}, taču tā vietā tika atrasts {0}.
expected_data_type=Tika gaidīts datu tips.
expected_expression=Tika gaidīta izteiksme.
expected_expression_or_subquery=Tika gaidīta izteiksme vai apakšvaicājums.
expected_first_or_last=Tika gaidīts PIRMAIS vai PĒDĒJAIS.
expected_identifier=Tika gaidīts identifikators.
expected_identifier_or_asterisk=Tika gaidīts identifikators vai *.
expected_identifier_or_sql_error_code=Tika gaidīts identifikators vai SQL kļūdas kods.
expected_integer=Tika gaidīts vesels skaitlis.
expected_language=Tika gaidīta valoda.
expected_literal=Tika gaidīta virkne vai ciparisks literālis.
expected_number=Tika gaidīts ciparisks literālis.
expected_operator=Tika gaidīts operators.
expected_path_or_identifier=Tika gaidīts ceļš vai identifikators.
expected_select_clause=Tika gaidīts ATLASĪT.
expected_select_or_set_operator=Tika gaidīts ATLASĪT vai iestatītais operators.
expected_sequence_option=Tika gaidīta secības iespēja.
expected_set_operator=Tika gaidīts iestatītais operators.
expected_simple_identifier=Tika gaidīts vienkāršs identifikators.
expected_statement=Tika gaidīts priekšraksts.
expected_string=Tika gaidīta virkne.
expected_subquery=Tika gaidīts apakšvaicājums.
expected_varref_found_identifier_use_1=Tika gaidīta mainīgā atsauce, taču atrasts identifikators; izmantojiet tā vietā {1}.
#YMSG,145:SQL script semantic check warning message
txtSqlError=Mēs nevarējām atvasināt atgriešanas priekšrakstu pilnībā no skripta. Pārskatiet kolonnu sarakstu un viņu datu tipus sānu panelī.
#XFLD: warning dialog title label
lblWarningDialogTitle=Brīdin.
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=Mainot valodu uz “SQL (standarta vaicājums)”, tiks izraisītas kļūdas jūsu kodā esošajiem SQLScript atslēgvārdiem.
#XBUT:Warning dialog ok button for DB View type
btnOk=Labi
#XBUT,14:Warning dialog close button for DB View type
btnClose=Aizv.
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=Datus SQLScript skatā var redzēt tikai pēc tam, kad visas izmaiņas ir izvietotas. Lūdzu, izvietojiet skatu un mēģiniet vēlreiz.
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=Datus SQLScript skatā var redzēt tikai pēc tam, kad visas izmaiņas ir izvietotas. Lūdzu, izvietojiet skatu un mēģiniet vēlreiz.
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=Skatā, kas ir aizsargāts ar datu piekļuves vadību, datus var redzēt tikai pēc tam, kad visas izmaiņas ir izvietotas. Lūdzu, izvietojiet skatu un mēģiniet vēlreiz.
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=Datus SQLScript skatā var redzēt tikai pēc tam, kad visas izmaiņas ir izvietotas. Lūdzu, izvietojiet skatu un mēģiniet vēlreiz.
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=Datus SQLScript skatā var redzēt tikai pēc tam, kad visas izmaiņas ir izvietotas. Lūdzu, izvietojiet skatu un mēģiniet vēlreiz.
cannotFormat=Priekšrakstu nevar formatēt.
shouldHaveGroupBy=SQL vaicājumam ir jābūt ar klauzulu GRUPĒT PĒC.
groupByCols=Klauzulai GRUPĒT PĒC ir jābūt ar visām kolonnām, kas nav iekļautas agregācijas funkcijās.
@msgTableImported=“{0}”.“{1}” ir sekmīgi importēts un izvietots.
@msgTableImportFailed=Problēma ar importēšanu: avots netika importēts repozitorijā un tāpēc to nevar izmantot jūsu skatā. Lai mēģinātu vēlreiz, nometiet to atkārtoti redaktorā.
VAL_SAVE_CANCELLED=“{0}” importēšana ir atcelta.
#XMSG
uniqueAlias=Kolonnu aizstājvārdiem ir jābūt unikāliem.
#XMSG
ANALYSING_SQL=SQL analizēšana
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn=Nevarēja atvasināt {0} paredzētus datu tipus. Pārskatiet šo kolonnu datu tipus panelī Rekvizīti.
#XBUT : SQL format button
format=Formāts
#XMSG:Format failure message
formatError=Formāts neizdevās. Izlabojiet sintakses kļūdu.
#XTOL
validateAndPreview=Pārbaudiet SQL un priekšskatiet datus
#XTOL
validateAndViewData=Pārbaudiet SQL un skatiet datus
#XMSG
unexpected_end=Neparedzētas beigas
#XTOL
validate=Pārbaudīt SQL
#XTOL
formattol=Formatēt SQL

#begin of change management (pop-up)
#XTIT
changeManagementTitle=Avota atjauninājumi
#XCOL
changedOn=Mainīšanas datums
#XCOL
changedBy=Mainīja

objectDisplayNameLocation=Avots
reviewText=Pārskatiet modifikācijas un, lai tās lietotu, saglabājiet un atkārtoti izvietojiet atjaunināto objektu.
changesText=Ir modificēti tālāk minētie šajā objektā izmantotie avoti, un šīs modifikācijas ir atzīmētas ar pārbaudes ziņojumiem redaktorā:
sharedEntity=Koplietots
#end of change management (pop-up)

#XFLD
VALIDATEFLD=Pārbaudīt derīgumu
#XFLD
CANCELVALIDATEFLD=Atcelt derīguma pārbaudi
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=Delta tveršanas tabula {0} nav atļauta patēriņam.
#XMSG
DATAVALIDATION=Datu derīguma pārbaude
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=Tabulai vai skatam {0} ir izstrādes laika vai izpildlaika kļūdas.
# analyzer errors
#
superflous_comma=Noņemt komatu
unterminated_path=Atlase nepilnīga
undefined_name_0=Identifikators {0} nav definēts vai nepastāv.
unresolved_col_0=Kolonnu {0} nevar atrast.
unresolved_table_0=Tabulu vai skatu {0} nevar atrast.
ambigous_col_definition_0_1_2=Tas pats kolonnas nosaukums tiek definēts vairāk nekā vienu reizi šeit: {0}, {1} un {2}.
expected_scalar_typed_expression=Ievadiet vienu vērtību.
invalid_datatype_1_operator_2_expected_3=Operators {2} sagaida datu tipu {3}. Maniet datu tipu {1}.
UNRESOLVED_TABLE_OR_MISSING_QUOTE=Tabulu vai skatu {0} nevar atrast, jo tas nepastāv vai trūkst vismaz vienu dubultpēdiņu. 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=Kolonnu {0} nevar atrast, jo tā nepastāv vai trūkst vismaz vienu dubultpēdiņu. 

