#XTIT Save Dialog param
modelNameSQL=SQL-view\
txtSQLFileValidationError=De modelview is ongeldig. Herstel alle validatieproblemen en probeer het opnieuw.

#
# parser errors
#
0_not_supported={0} wordt niet ondersteund.
1_already_defined=De ID {1} is al gedefinieerd.
column_order_and_index_order_specified=Kolom- en indexvolgorde kunnen niet tegelijkertijd worden gespecificeerd.
annotation_0_not_supported_for_1=Annotation {0} wordt niet ondersteund voor {1}.

expected_1_found_0={1} werd verwacht, maar in plaats daarvan is {0} gevonden.
expected_data_type=Gegevenstype verwacht.
expected_expression=Uitdrukking verwacht.
expected_expression_or_subquery=Uitdrukking of subquery verwacht.
expected_first_or_last=FIRST of LAST verwacht.
expected_identifier=ID verwacht.
expected_identifier_or_asterisk=ID of * verwacht.
expected_identifier_or_sql_error_code=ID of SQL-foutcode verwacht.
expected_integer=Geheel getal verwacht.
expected_language=Taal verwacht.
expected_literal=String- of getalsliteral verwacht.
expected_number=Getalsliteral verwacht.
expected_operator=Operator verwacht.
expected_path_or_identifier=Pad of ID verwacht.
expected_select_clause=SELECT verwacht.
expected_select_or_set_operator=SELECT of SET-operator verwacht.
expected_sequence_option=Volgordeoptie verwacht.
expected_set_operator=SET-operator verwacht.
expected_simple_identifier=Eenvoudige ID verwacht.
expected_statement=Instructie verwacht.
expected_string=String verwacht.
expected_subquery=Subquery verwacht.
expected_varref_found_identifier_use_1=Variabele referentie verwacht, maar ID gevonden, gebruik in plaats daarvan {1}.
#YMSG,145:SQL script semantic check warning message
txtSqlError=We kunnen de 'return'-instructie niet volledig uit het script afleiden. Controleer de lijst met kolommen en de gegevenstypen ervan in zijpaneel.
#XFLD: warning dialog title label
lblWarningDialogTitle=Waarsch
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=Wanneer u de taal wijzigt naar "SQL (standaardquery)", resulteert dit in fouten voor eventuele SQLScript-trefwoorden in uw code.
#XBUT:Warning dialog ok button for DB View type
btnOk=OK
#XBUT,14:Warning dialog close button for DB View type
btnClose=Sluiten
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=U kunt gegevens niet zien in een SQLScript-view totdat alle wijzigingen zijn geïmplementeerd. Implementeer de view en probeer opnieuw.
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=U kunt gegevens niet zien in een SQLScript-view totdat alle wijzigingen zijn geïmplementeerd. Implementeer de view en probeer opnieuw.
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=U kunt geen gegevens zien in een view die beschermd is door een gegevenstoegangscontrole totdat alle wijzigingen zijn geïmplementeerd. Implementeer de view en probeer opnieuw. 
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=U kunt gegevens niet zien in een SQLScript-view totdat alle wijzigingen zijn geïmplementeerd. Implementeer de view en probeer opnieuw.
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=U kunt gegevens niet zien in een SQLScript-view totdat alle wijzigingen zijn geïmplementeerd. Implementeer de view en probeer opnieuw.
cannotFormat=De instructie kan niet worden geformatteerd.
shouldHaveGroupBy=De SQL-query moet een GROUP BY-component hebben.
groupByCols=De GROUP BY-component moet alle kolommen hebben die niet in de aggregatiefuncties zijn opgenomen.
@msgTableImported="{0}"."{1}" is geïmporteerd en geïmplementeerd.
@msgTableImportFailed=Probleem met import: de bron is niet in de repository geïmporteerd en kan daarom niet in uw view worden gebruikt. Zet de bron opnieuw op de editor voor een nieuwe poging.
VAL_SAVE_CANCELLED=De import van ''{0}'' is geannuleerd.
#XMSG
uniqueAlias=Kolomaliassen moeten uniek zijn.
#XMSG
ANALYSING_SQL=Bezig met analyseren SQL
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn=Gegevenstypen niet afgeleid voor {0}. Controleer gegevenstypen van kolommen in venster Eigenschappen.
#XBUT : SQL format button
format=Formatteren
#XMSG:Format failure message
formatError=Formatteren is mislukt. Corrigeer de syntaxerror.
#XTOL
validateAndPreview=SQL valideren en gegevenspreview bekijken
#XTOL
validateAndViewData=SQL valideren en gegevens bekijken
#XMSG
unexpected_end=Onverwacht einde
#XTOL
validate=SQL valideren
#XTOL
formattol=SQL formatteren

#begin of change management (pop-up)
#XTIT
changeManagementTitle=Bronupdates
#XCOL
changedOn=Gewijzigd op
#XCOL
changedBy=Gewijzigd door

objectDisplayNameLocation=Bron
reviewText=Controleer de wijzigingen en sla deze op en implementeer het bijgewerkte object opnieuw om de wijzigingen toe te passen.
changesText=De volgende in dit object gebruikte bronnen zijn gewijzigd en deze wijzigingen worden gemarkeerd door validatiemeldingen in de editor:
sharedEntity=Gedeeld
#end of change management (pop-up)

#XFLD
VALIDATEFLD=Valideren
#XFLD
CANCELVALIDATEFLD=Validatie afbreken
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=Tabel {0} deltaopname is niet toegestaan voor verbruik.
#XMSG
DATAVALIDATION=Gegevensvalidatie
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=De tabel of view {0} bevat ontwerptijd- of runtimefouten.
# analyzer errors
#
superflous_comma=Komma verwijderen
unterminated_path=Selectie onvolledig
undefined_name_0=ID {0} is niet gedefinieerd of bestaat niet.
unresolved_col_0=Kolom {0} niet gevonden.
unresolved_table_0=Tabel of view {0} niet gevonden.
ambigous_col_definition_0_1_2=Dezelfde kolomnaam wordt meer dan één keer gedefinieerd in {0}, {1} en {2}.
expected_scalar_typed_expression=Vul één waarde in.
invalid_datatype_1_operator_2_expected_3=De operator {2} verwacht gegevenstype {3}. Wijzig gegevenstype {1}.
UNRESOLVED_TABLE_OR_MISSING_QUOTE=De tabel of view {0} kan niet worden gevonden omdat deze niet bestaat of er ten minste één dubbel aanhalingsteken ontbreekt. 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=De kolom {0} kan niet worden gevonden omdat deze niet bestaat of er ten minste één dubbel aanhalingsteken ontbreekt. 

