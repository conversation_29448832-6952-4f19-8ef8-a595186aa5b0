#XTIT Save Dialog param
modelNameSQL=SQL 视图
txtSQLFileValidationError=模型视图无效。请解决所有验证问题，然后再试。

#
# parser errors
#
0_not_supported={0} 不受支持。
1_already_defined=已定义 ID {1}。
column_order_and_index_order_specified=无法同时指定列顺序和索引顺序。
annotation_0_not_supported_for_1={1} 不支持注释 {0}。

expected_1_found_0=需要 {1}，但找到 {0}。
expected_data_type=需要数据类型。
expected_expression=需要表达式。
expected_expression_or_subquery=需要表达式或子查询。
expected_first_or_last=需要 FIRST 或 LAST。
expected_identifier=需要 ID。
expected_identifier_or_asterisk=需要 ID 或 *。
expected_identifier_or_sql_error_code=需要 ID 或 SQL 错误代码。
expected_integer=需要整数。
expected_language=需要语言。
expected_literal=需要字符串或数字参数。
expected_number=需要数字参数。
expected_operator=需要运算符。
expected_path_or_identifier=需要路径或 ID。
expected_select_clause=需要 SELECT。
expected_select_or_set_operator=需要 SELECT 或 SET 运算符。
expected_sequence_option=需要序列选项。
expected_set_operator=需要 SET 运算符。
expected_simple_identifier=需要简单 ID。
expected_statement=需要语句。
expected_string=需要字符串。
expected_subquery=需要子查询。
expected_varref_found_identifier_use_1=需要变量参考，但找到 ID，改为使用{1}。
#YMSG,145:SQL script semantic check warning message
txtSqlError=无法完全从脚本派生返回语句。请检查侧面板中列的列表及数据类型。
#XFLD: warning dialog title label
lblWarningDialogTitle=警告
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=语言更改为 "SQL（标准查询）" 会让代码中的任何 SQLScript 关键字出错。
#XBUT:Warning dialog ok button for DB View type
btnOk=确定
#XBUT,14:Warning dialog close button for DB View type
btnClose=关闭
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=部署所有更改前，不能在 SQLScript 视图中查看数据。请部署视图并重试。
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=部署所有更改前，不能在 SQLScript 视图中查看数据。请部署视图并重试。
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=部署所有更改前，不能在由数据访问控制保护的视图中查看数据。请部署视图并重试。
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=部署所有更改前，不能在 SQLScript 视图中查看数据。请部署视图并重试。
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=部署所有更改前，不能在 SQLScript 视图中查看数据。请部署视图并重试。
cannotFormat=无法设置语句的格式。
shouldHaveGroupBy=SQL 查询应有 GROUP BY 子句。
groupByCols=GROUP BY 子句应有聚合函数未包含的所有列。
@msgTableImported="{0}", "{1}" 已经成功导入和部署。
@msgTableImportFailed=导入出现问题：源未导入资源库，因此无法用于你的视图。若要重试，请将其重新拖放到编辑器。
VAL_SAVE_CANCELLED="{0}" 的导入已经取消。
#XMSG
uniqueAlias=列别名应唯一。
#XMSG
ANALYSING_SQL=正在分析 SQL
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn=没能为 {0} 派生数据类型。请在 "属性" 面板中查看这些列的数据类型。
#XBUT : SQL format button
format=格式化
#XMSG:Format failure message
formatError=格式化失败。请更正语法错误。
#XTOL
validateAndPreview=验证 SQL 并预览数据
#XTOL
validateAndViewData=验证 SQL 并查看数据
#XMSG
unexpected_end=意外结束
#XTOL
validate=验证 SQL
#XTOL
formattol=格式化 SQL

#begin of change management (pop-up)
#XTIT
changeManagementTitle=源更新
#XCOL
changedOn=更改日期
#XCOL
changedBy=更改者

objectDisplayNameLocation=源
reviewText=请检查修改并保存，然后重新部署更新的对象，以应用修改。
changesText=此对象中所用的以下源已修改，在编辑器中这些修改带有验证消息标记：
sharedEntity=已共享
#end of change management (pop-up)

#XFLD
VALIDATEFLD=验证
#XFLD
CANCELVALIDATEFLD=取消验证
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=不允许使用增量捕获表 {0}。
#XMSG
DATAVALIDATION=数据验证
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=表或视图 {0} 出现设计时或运行时错误。
# analyzer errors
#
superflous_comma=移除逗号
unterminated_path=选择不完整
undefined_name_0=ID {0} 未定义或不存在。
unresolved_col_0=找不到列 {0}。
unresolved_table_0=找不到表或视图 {0}。
ambigous_col_definition_0_1_2=在 {0}、{1} 和 {2} 中多次定义了相同的列名称。
expected_scalar_typed_expression=输入单个值。
invalid_datatype_1_operator_2_expected_3=运算符 {2} 应针对数据类型 {3}。请更改数据类型 {1}。
UNRESOLVED_TABLE_OR_MISSING_QUOTE=找不到表或视图 {0}，因为不存在或至少缺少一个双引号。 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=找不到列 {0}，因为不存在或至少缺少一个双引号。 

