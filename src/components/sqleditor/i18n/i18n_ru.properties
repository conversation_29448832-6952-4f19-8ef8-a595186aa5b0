#XTIT Save Dialog param
modelNameSQL=Ракурс SQL
txtSQLFileValidationError=Ракурс модели недействителен. Исправьте все ошибки проверки и повторите попытку.

#
# parser errors
#
0_not_supported={0} не поддерживается.
1_already_defined=Идентификатор {1} уже определен.
column_order_and_index_order_specified=Последовательность столбцов и последовательность индекса невозможно определить одновременно.
annotation_0_not_supported_for_1=Аннотация {0} не поддерживается для {1}.

expected_1_found_0=Ожидалось: {1}, найдено: {0}.
expected_data_type=Ожидался тип данных.
expected_expression=Ожидалось выражение.
expected_expression_or_subquery=Ожидалось выражение или подзапрос.
expected_first_or_last=Ожидались FIRST или LAST.
expected_identifier=Ожидался идентификатор.
expected_identifier_or_asterisk=Ожидался идентификатор или *.
expected_identifier_or_sql_error_code=Ожидался идентификатор или код ошибки SQL.
expected_integer=Ожидалось целое число.
expected_language=Ожидался язык.
expected_literal=Ожидались стока или числовой литерал.
expected_number=Ожидался числовой литерал.
expected_operator=Ожидался оператор.
expected_path_or_identifier=Ожидался путь или идентификатор.
expected_select_clause=Ожидался SELECT.
expected_select_or_set_operator=Ожидались SELECT или оператор set.
expected_sequence_option=Ожидалась опция последовательности.
expected_set_operator=Ожидался оператор set.
expected_simple_identifier=Ожидался простой идентификатор.
expected_statement=Ожидалась инструкция.
expected_string=Ожидалась строка.
expected_subquery=Ожидался подзапрос.
expected_varref_found_identifier_use_1=Ожидалась ссылка на переменную, но найден идентификатор, вместо нее используйте {1}.
#YMSG,145:SQL script semantic check warning message
txtSqlError=Не удалось полностью найти производную инструкцию по возврату из сценария. Проверьте список столбцов и их типы данных на боковой панели.
#XFLD: warning dialog title label
lblWarningDialogTitle=Предуп.
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=При изменении языка на "SQL (стандартный запрос)" будут вызваны ошибки для всех ключевых слов SQLScript в вашем коде.
#XBUT:Warning dialog ok button for DB View type
btnOk=ОК
#XBUT,14:Warning dialog close button for DB View type
btnClose=Закр.
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=Вы не можете увидеть данные в ракурсе SQLScript, пока не развернуты все изменения. Разверните ракурс и повторите попытку.
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=Вы не можете увидеть данные в ракурсе SQLScript, пока не развернуты все изменения. Разверните ракурс и повторите попытку.
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=Вы не можете увидеть данные в ракурсе, защищенном контролем доступа к данным, пока не развернуты все изменения. Разверните ракурс и повторите попытку.
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=Вы не можете увидеть данные в ракурсе SQLScript, пока не развернуты все изменения. Разверните ракурс и повторите попытку.
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=Вы не можете увидеть данные в ракурсе SQLScript, пока не развернуты все изменения. Разверните ракурс и повторите попытку.
cannotFormat=Форматирование инструкции невозможно.
shouldHaveGroupBy=Запрос SQL должен иметь условие GROUP BY.
groupByCols=В условие GROUP BY должны входить все столбцы, не включенные в функции агрегации.
@msgTableImported="{0}", "{1}" успешно импортированы и развернуты.
@msgTableImportFailed=Проблема при импорте: источник не импортирован в репозитарий и не может использоваться в вашем ракурсе. Чтобы повторить, снова опустите его в редактор.
VAL_SAVE_CANCELLED=Импорт "{0}" отменен.
#XMSG
uniqueAlias=Псевдонимы столбцов должны быть уникальными.
#XMSG
ANALYSING_SQL=Анализирование SQL
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn=Ошибка деривации типов данных для {0}. Проверьте типы данных этих столбцов на панели "Свойства".
#XBUT : SQL format button
format=Формат
#XMSG:Format failure message
formatError=Ошибка формата. Исправьте синтаксис.
#XTOL
validateAndPreview=Проверка SQL и предпросмотр данных
#XTOL
validateAndViewData=Проверка SQL и просмотр данных
#XMSG
unexpected_end=Неожиданный конец
#XTOL
validate=Проверить SQL
#XTOL
formattol=Форматировать SQL

#begin of change management (pop-up)
#XTIT
changeManagementTitle=Обновления источника
#XCOL
changedOn=Дата изменения
#XCOL
changedBy=Изменил

objectDisplayNameLocation=Источник
reviewText=Проверьте изменения, затем сохраните и повторно разверните обновленный объект, чтобы применить их.
changesText=Следующие источники, используемые в этом объекте, были изменены. Изменения отмечены сообщениями о проверке в редакторе:
sharedEntity=Доступ открыт
#end of change management (pop-up)

#XFLD
VALIDATEFLD=Проверка
#XFLD
CANCELVALIDATEFLD=Отменить проверку
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=Таблица дельта-записи {0} не разрешена для использования.
#XMSG
DATAVALIDATION=Проверка данных
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=Таблица или ракурс {0} содержит ошибки времени дизайна или выполнения.
# analyzer errors
#
superflous_comma=Удалить запятую
unterminated_path=Неполный выбор
undefined_name_0=Идентификатор {0} не определен или не существует.
unresolved_col_0=Столбец {0} не найден.
unresolved_table_0=Таблица или ракурс {0} не найдены.
ambigous_col_definition_0_1_2=Одно и то же имя столбца определено более одного раза в {0}, {1} и {2}.
expected_scalar_typed_expression=Введите одно значение.
invalid_datatype_1_operator_2_expected_3=Оператор {2} ожидает тип данных {3}. Измените тип данных {1}.
UNRESOLVED_TABLE_OR_MISSING_QUOTE=Не удается найти таблицу или ракурс {0}, так как они не существуют или отсутствует как минимум одна двойная кавычка. 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=Не удается найти столбец {0}, так как он не существует или отсутствует как минимум одна двойная кавычка. 

