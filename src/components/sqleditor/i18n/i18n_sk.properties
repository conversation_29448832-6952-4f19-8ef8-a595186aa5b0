#XTIT Save Dialog param
modelNameSQL=Zobrazenie SQL
txtSQLFileValidationError=Zobrazenie modelu nie je platné. Opravte všetky problémy overenia a skúste to znova.

#
# parser errors
#
0_not_supported={0} nie je podporované.
1_already_defined=Identifikátor {1} je už definovaný.
column_order_and_index_order_specified=Poradie stĺpcov a poradie indexov súčasne nie je možné zada<PERSON>.
annotation_0_not_supported_for_1=Poznámka {0} nie je podporovaná pre {1}.

expected_1_found_0={1} bolo oč<PERSON>, ale namiesto toho sa našlo {0}.
expected_data_type=Očakávaný typ údajov.
expected_expression=Očakávaný výraz.
expected_expression_or_subquery=Očakávaný výraz alebo poddotaz.
expected_first_or_last=Očakávané FIRST alebo LAST.
expected_identifier=Očakávaný identifikátor.
expected_identifier_or_asterisk=Očakávaný identifikátor alebo *.
expected_identifier_or_sql_error_code=Očakávaný identifikátor alebo kód chyby SQL.
expected_integer=Očakávané celé číslo.
expected_language=Očakávaný jazyk.
expected_literal=Očakávaný reťazec alebo numerický literál.
expected_number=Očakávaný numerický literál.
expected_operator=Očakávaný operátor.
expected_path_or_identifier=Očakávaná cesta alebo identifikátor.
expected_select_clause=Očakávané SELECT.
expected_select_or_set_operator=Očakávané SELECT alebo operátor množiny.
expected_sequence_option=Očakávaná možnosť poradia.
expected_set_operator=Očakávaný operátor Set.
expected_simple_identifier=Očakávaný jednoduchý identifikátor.
expected_statement=Očakávaný príkaz.
expected_string=Očakávaný reťazec.
expected_subquery=Očakávaný poddotaz.
expected_varref_found_identifier_use_1=Očakávaná referencia premennej, ale nájdený identifikátor. Použite namiesto toho {1}.
#YMSG,145:SQL script semantic check warning message
txtSqlError=Nemôžeme kompletne odvodiť príkaz návratu zo skriptu. Pozrite si zoznam stĺpcov a príslušné typy údajov na bočnom paneli.
#XFLD: warning dialog title label
lblWarningDialogTitle=Upozornenie
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=Zmena jazyka na "SQL (štandardný dotaz)" spôsobí chyby pre všetky kľúčové slová SQLScript vo vašom kóde.
#XBUT:Warning dialog ok button for DB View type
btnOk=OK
#XBUT,14:Warning dialog close button for DB View type
btnClose=Zavrieť
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=Údaje v zobrazení SQLScript nemôžete vidieť, kým nebudú nasadené všetky zmeny. Nasaďte zobrazenie a skúste to znova.
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=Údaje v zobrazení SQLScript nemôžete vidieť, kým nebudú nasadené všetky zmeny. Nasaďte zobrazenie a skúste to znova.
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=Údaje v zobrazení chránenom riadením prístupu k údajom nemôžete vidieť, kým nebudú nasadené všetky zmeny. Nasaďte zobrazenie a skúste to znova.
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=Údaje v zobrazení SQLScript nemôžete vidieť, kým nebudú nasadené všetky zmeny. Nasaďte zobrazenie a skúste to znova.
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=Údaje v zobrazení SQLScript nemôžete vidieť, kým nebudú nasadené všetky zmeny. Nasaďte zobrazenie a skúste to znova.
cannotFormat=Príkaz nie je možné formátovať.
shouldHaveGroupBy=Dotaz SQL by mal mať klauzulu GROUP BY.
groupByCols=Klauzula GROUP BY by mala mať všetky stĺpce, ktoré nie sú zahrnuté vo funkciách agregácie.
@msgTableImported=„{0}“.„{1}“ bolo úspešne importované a nasadené.
@msgTableImportFailed=Problém s importom: Zdroj nebol importovaný do úložiska, a preto sa nemôže použiť vo vašom zobrazení. Ak to chcete skúsiť znovu, znovu ho presuňte do editora.
VAL_SAVE_CANCELLED=Import ''{0}''’ bol zrušený.
#XMSG
uniqueAlias=Aliasy stĺpcov by mali byť jednoznačné.
#XMSG
ANALYSING_SQL=Analyzuje sa SQL
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn=Dátové typy nebolo možné odvodiť pre {0}. Skontrolujte dátové typy týchto stĺpcov v paneli Vlastnosti.
#XBUT : SQL format button
format=Formátovať
#XMSG:Format failure message
formatError=Formátovanie zlyhalo. Opravte chybu syntaxe.
#XTOL
validateAndPreview=Overiť SQL a zobraziť ukážku údajov
#XTOL
validateAndViewData=Overiť SQL a zobraziť údaje
#XMSG
unexpected_end=Neočakávaný koniec
#XTOL
validate=Overiť SQL
#XTOL
formattol=Formátovať SQL

#begin of change management (pop-up)
#XTIT
changeManagementTitle=Aktualizácie zdroja
#XCOL
changedOn=Zmenené dňa
#XCOL
changedBy=Zmenil

objectDisplayNameLocation=Zdroj
reviewText=Skontrolujte zmeny a uložením a opätovným nasadením aktualizovaného objektu ich použite.
changesText=Nasledujúce zdroje použité v tomto objektu boli zmenené a tieto zmeny sa označia hláseniami overenia v editore:
sharedEntity=Zdieľané
#end of change management (pop-up)

#XFLD
VALIDATEFLD=Overiť
#XFLD
CANCELVALIDATEFLD=Zrušiť overenie
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=Tabuľka záznamu delta {0} nie je povolená na použitie.
#XMSG
DATAVALIDATION=Overenie údajov
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=Tabuľka alebo zobrazenie {0} vykazuje chyby času dizajnu alebo doby chodu.
# analyzer errors
#
superflous_comma=Odstrániť čiarku
unterminated_path=Výber neúplný
undefined_name_0=Identifikátor {0} nebol definovaný alebo neexistuje.
unresolved_col_0=Stĺpec {0} sa nenašiel.
unresolved_table_0=Tabuľku alebo zobrazenie {0} nie je možné nájsť.
ambigous_col_definition_0_1_2=Rovnaký názov stĺpca je definovaný viac ako raz v {0}, {1} a {2}.
expected_scalar_typed_expression=Zadajte jednu hodnotu.
invalid_datatype_1_operator_2_expected_3=Operátor {2} očakáva dátový typ {3}. Zmeňte dátový typ {1}.
UNRESOLVED_TABLE_OR_MISSING_QUOTE=Tabuľku alebo zobrazenie {0} nemožno nájsť, pretože neexistuje alebo chýba aspoň jedna dvojitá úvodzovka. 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=Stĺpec {0} nemožno nájsť, pretože neexistuje alebo chýba aspoň jedna dvojitá úvodzovka. 

