#XTIT Save Dialog param
modelNameSQL=[[[ŜǬĻ Ʋįēŵ∙∙∙∙∙∙]]]
txtSQLFileValidationError=[[[Ţĥē ɱŏƌēĺ ʋįēŵ įş ŋŏţ ʋąĺįƌ. Ƥĺēąşē ƒįχ ąĺĺ ʋąĺįƌąţįŏŋ įşşűēş ąŋƌ ţŗŷ ąğąįŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#
# parser errors
#
0_not_supported=[[[{0} įş ŋŏţ şűρρŏŗţēƌ.]]]
1_already_defined=[[[Ţĥē įƌēŋţįƒįēŗ {1} įş ąĺŗēąƌŷ ƌēƒįŋēƌ.]]]
column_order_and_index_order_specified=[[[Ĉŏĺűɱŋ ŏŗƌēŗ ąŋƌ įŋƌēχ ŏŗƌēŗ ċąŋŋŏţ ƃē şρēċįƒįēƌ ąţ ţĥē şąɱē ţįɱē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
annotation_0_not_supported_for_1=[[[Āŋŋŏţąţįŏŋ {0} įş ŋŏţ şűρρŏŗţēƌ ƒŏŗ {1}.]]]

expected_1_found_0=[[[{1} ŵąş ēχρēċţēƌ, ƃűţ ƒŏűŋƌ {0} įŋşţēąƌ.]]]
expected_data_type=[[[Ďąţą ţŷρē ēχρēċţēƌ.∙∙∙∙∙]]]
expected_expression=[[[Ĕχρŗēşşįŏŋ ēχρēċţēƌ.∙∙∙∙]]]
expected_expression_or_subquery=[[[Ĕχρŗēşşįŏŋ ŏŗ şűƃƣűēŗŷ ēχρēċţēƌ.∙∙∙∙∙∙∙∙∙∙]]]
expected_first_or_last=[[[ƑĬŘŜŢ ŏŗ ĻĀŜŢ ēχρēċţēƌ.∙∙∙∙∙∙]]]
expected_identifier=[[[Ĭƌēŋţįƒįēŗ ēχρēċţēƌ.∙∙∙∙]]]
expected_identifier_or_asterisk=[[[Ĭƌēŋţįƒįēŗ ŏŗ * ēχρēċţēƌ.∙∙∙∙∙∙∙]]]
expected_identifier_or_sql_error_code=[[[Ĭƌēŋţįƒįēŗ ŏŗ ŜǬĻ ēŗŗŏŗ ċŏƌē ēχρēċţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
expected_integer=[[[Ĭŋţēğēŗ ēχρēċţēƌ.∙∙∙∙∙∙∙]]]
expected_language=[[[Ļąŋğűąğē ēχρēċţēƌ.∙∙∙∙∙∙]]]
expected_literal=[[[Ŝţŗįŋğ ŏŗ ŋűɱēŗįċ ĺįţēŗąĺ ēχρēċţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙]]]
expected_number=[[[Ńűɱēŗįċ ĺįţēŗąĺ ēχρēċţēƌ.∙∙∙∙∙∙∙]]]
expected_operator=[[[Ŏρēŗąţŏŗ ēχρēċţēƌ.∙∙∙∙∙∙]]]
expected_path_or_identifier=[[[Ƥąţĥ ŏŗ įƌēŋţįƒįēŗ ēχρēċţēƌ.∙∙∙∙∙∙∙∙]]]
expected_select_clause=[[[ŜĔĻĔĈŢ ēχρēċţēƌ.∙∙∙∙∙∙∙∙]]]
expected_select_or_set_operator=[[[ŜĔĻĔĈŢ ŏŗ şēţ ŏρēŗąţŏŗ ēχρēċţēƌ.∙∙∙∙∙∙∙∙∙∙]]]
expected_sequence_option=[[[Ŝēƣűēŋċē ŏρţįŏŋ ēχρēċţēƌ.∙∙∙∙∙∙∙]]]
expected_set_operator=[[[Ŝēţ ŏρēŗąţŏŗ ēχρēċţēƌ.∙∙∙∙∙]]]
expected_simple_identifier=[[[Ŝįɱρĺē įƌēŋţįƒįēŗ ēχρēċţēƌ.∙∙∙∙∙∙∙∙]]]
expected_statement=[[[Ŝţąţēɱēŋţ ēχρēċţēƌ.∙∙∙∙∙]]]
expected_string=[[[Ŝţŗįŋğ ēχρēċţēƌ.∙∙∙∙∙∙∙∙]]]
expected_subquery=[[[Ŝűƃƣűēŗŷ ēχρēċţēƌ.∙∙∙∙∙∙]]]
expected_varref_found_identifier_use_1=[[[Ʋąŗįąƃĺē ŗēƒēŗēŋċē ēχρēċţēƌ, ƃűţ ƒŏűŋƌ įƌēŋţįƒįēŗ, űşē {1} įŋşţēąƌ.]]]
#YMSG,145:SQL script semantic check warning message
txtSqlError=[[[!!!Ŵē ąŗē űŋąƃĺē ţŏ ƌēŗįʋē ţĥē ŗēţűŗŋ şţąţēɱēŋţ ċŏɱρĺēţēĺŷ ƒŗŏɱ ţĥē şċŗįρţ. Ƥĺēąşē ŗēʋįēŵ ţĥē ĺįşţ ŏƒ ċŏĺűɱŋş ąŋƌ ţĥēįŗ ƌąţą ţŷρēş įŋ ţĥē şįƌē ρąŋēĺ.]]]
#XFLD: warning dialog title label
lblWarningDialogTitle=[[[Ŵąŗŋįŋğ∙∙∙∙∙∙∙]]]
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=[[[Ĉĥąŋğįŋğ ţĥē ĺąŋğűąğē ţŏ "ŜǬĻ (Ŝţąŋƌąŗƌ Ǭűēŗŷ)" ŵįĺĺ ğįʋē ēŗŗŏŗş ƒŏŗ ąŋŷ ŜǬĻŜċŗįρţ ķēŷŵŏŗƌş įŋ ŷŏűŗ ċŏƌē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XBUT:Warning dialog ok button for DB View type
btnOk=[[[ŎĶ∙∙]]]
#XBUT,14:Warning dialog close button for DB View type
btnClose=[[[Ĉĺŏşē∙∙∙]]]
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=[[[Ŷŏű ċąŋ’ţ şēē ƌąţą įŋ ą ŜǬĻŜċŗįρţ ʋįēŵ űŋţįĺ ąĺĺ ċĥąŋğēş ąŗē ƌēρĺŏŷēƌ. Ƥĺēąşē ƌēρĺŏŷ ţĥē ʋįēŵ ąŋƌ ţŗŷ ąğąįŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=[[[Ŷŏű ċąŋ’ţ şēē ƌąţą įŋ ą ŜǬĻŜċŗįρţ ʋįēŵ űŋţįĺ ąĺĺ ċĥąŋğēş ąŗē ƌēρĺŏŷēƌ. Ƥĺēąşē ƌēρĺŏŷ ţĥē ʋįēŵ ąŋƌ ţŗŷ ąğąįŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=[[[Ŷŏű ċąŋ’ţ şēē ƌąţą įŋ ą ʋįēŵ ρŗŏţēċţēƌ ƃŷ ą ƌąţą ąċċēşş ċŏŋţŗŏĺ űŋţįĺ ąĺĺ ċĥąŋğēş ąŗē ƌēρĺŏŷēƌ. Ƥĺēąşē ƌēρĺŏŷ ţĥē ʋįēŵ ąŋƌ ţŗŷ ąğąįŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=[[[Ŷŏű ċąŋ’ţ şēē ƌąţą įŋ ą ŜǬĻŜċŗįρţ ʋįēŵ űŋţįĺ ąĺĺ ċĥąŋğēş ąŗē ƌēρĺŏŷēƌ. Ƥĺēąşē ƌēρĺŏŷ ţĥē ʋįēŵ ąŋƌ ţŗŷ ąğąįŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=[[[Ŷŏű ċąŋ’ţ şēē ƌąţą įŋ ą ŜǬĻŜċŗįρţ ʋįēŵ űŋţįĺ ąĺĺ ċĥąŋğēş ąŗē ƌēρĺŏŷēƌ. Ƥĺēąşē ƌēρĺŏŷ ţĥē ʋįēŵ ąŋƌ ţŗŷ ąğąįŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
cannotFormat=[[[Ţĥē şţąţēɱēŋţ ċąŋŋŏţ ƃē ƒŏŗɱąţţēƌ.∙∙∙∙∙∙∙∙∙∙∙]]]
shouldHaveGroupBy=[[[Ţĥē ŜǬĻ ƣűēŗŷ şĥŏűĺƌ ĥąʋē ą ĢŘŎŮƤ ƁŶ ċĺąűşē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
groupByCols=[[[Ţĥē ĢŘŎŮƤ ƁŶ ċĺąűşē şĥŏűĺƌ ĥąʋē ąĺĺ ċŏĺűɱŋş ŵĥįċĥ ąŗē ŋŏţ įŋċĺűƌēƌ įŋ ąğğŗēğąţįŏŋ ƒűŋċţįŏŋş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
@msgTableImported=[[["{0}"’, "{1}"’ ĥąʋē ƃēēŋ şűċċēşşƒűĺĺŷ įɱρŏŗţēƌ ąŋƌ ƌēρĺŏŷēƌ.]]]
@msgTableImportFailed=[[[Ƥŗŏƃĺēɱ ŵįţĥ įɱρŏŗţ: Ţĥē şŏűŗċē ŵąş ŋŏţ įɱρŏŗţēƌ įŋţŏ ţĥē ŗēρŏşįţŏŗŷ ąŋƌ ţĥēŗēƒŏŗē ċąŋŋŏţ ƃē űşēƌ įŋ ŷŏűŗ ʋįēŵ. Ţŏ ţŗŷ ąğąįŋ, ρĺēąşē ŗē-ƌŗŏρ įţ ŏŋ ţĥē ēƌįţŏŗ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
VAL_SAVE_CANCELLED=[[[Ţĥē įɱρŏŗţ ŏƒ "{0}"’ ĥąş ƃēēŋ ċąŋċēĺĺēƌ.]]]
#XMSG
uniqueAlias=[[[Ĉŏĺűɱŋ ąĺįąşēş şĥŏűĺƌ ƃē űŋįƣűē.∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
ANALYSING_SQL=[[[Āŋąĺŷžįŋğ ŜǬĻ∙∙∙∙∙∙]]]
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn=[[[Ďąţą ţŷρēş ċŏűĺƌ ŋŏţ ƃē ƌēŗįʋēƌ ƒŏŗ {0}. Ƥĺēąşē ŗēʋįēŵ ţĥē ƌąţą ţŷρēş ŏƒ ţĥēşē ċŏĺűɱŋş įŋ ţĥē Ƥŗŏρēŗţįēş ρąŋēĺ.]]]
#XBUT : SQL format button
format=[[[Ƒŏŗɱąţ∙∙∙∙∙∙∙∙]]]
#XMSG:Format failure message
formatError=[[[Ƒŏŗɱąţ ƒąįĺēƌ. Ĉŏŗŗēċţ ţĥē şŷŋţąχ ēŗŗŏŗ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTOL
validateAndPreview=[[[Ʋąĺįƌąţē ŜǬĻ ąŋƌ Ƥŗēʋįēŵ Ďąţą∙∙∙∙∙∙∙∙∙]]]
#XTOL
validateAndViewData=[[[Ʋąĺįƌąţē ŜǬĻ ąŋƌ Ʋįēŵ Ďąţą∙∙∙∙∙∙∙]]]
#XMSG
unexpected_end=[[[Ůŋēχρēċţēƌ ēŋƌ∙∙∙∙∙]]]
#XTOL
validate=[[[Ʋąĺįƌąţē ŜǬĻ∙∙∙∙∙∙∙]]]
#XTOL
formattol=[[[Ƒŏŗɱąţ ŜǬĻ∙∙∙∙]]]

#begin of change management (pop-up)
#XTIT
changeManagementTitle=[[[Ŝŏűŗċē Ůρƌąţēş∙∙∙∙∙]]]
#XCOL
changedOn=[[[Ĉĥąŋğēƌ Ŏŋ∙∙∙∙]]]
#XCOL
changedBy=[[[Ĉĥąŋğēƌ Ɓŷ∙∙∙∙]]]

objectDisplayNameLocation=[[[Ŝŏűŗċē∙∙∙∙∙∙∙∙]]]
reviewText=[[[Ƥĺēąşē ŗēʋįēŵ ɱŏƌįƒįċąţįŏŋş ąŋƌ şąʋē ąŋƌ ŗēƌēρĺŏŷ ţĥē űρƌąţēƌ ŏƃĵēċţ ţŏ ąρρĺŷ ţĥēɱ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
changesText=[[[Ţĥē ƒŏĺĺŏŵįŋğ şŏűŗċēş űşēƌ įŋ ţĥįş ŏƃĵēċţ ĥąʋē ƃēēŋ ɱŏƌįƒįēƌ, ąŋƌ ţĥēşē ɱŏƌįƒįċąţįŏŋş ąŗē ɱąŗķēƌ ƃŷ ʋąĺįƌąţįŏŋ ɱēşşąğēş įŋ ţĥē ēƌįţŏŗ:∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
sharedEntity=[[[Ŝĥąŗēƌ∙∙∙∙∙∙∙∙]]]
#end of change management (pop-up)

#XFLD
VALIDATEFLD=[[[Ʋąĺįƌąţē∙∙∙∙∙∙]]]
#XFLD
CANCELVALIDATEFLD=[[[Ĉąŋċēĺ Ʋąĺįƌąţįŏŋ∙∙∙∙∙∙∙]]]
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=[[[Ţĥē ƌēĺţą ċąρţűŗē ţąƃĺē {0} įş ŋŏţ ąĺĺŏŵēƌ ƒŏŗ ċŏŋşűɱρţįŏŋ.]]]
#XMSG
DATAVALIDATION=[[[Ďąţą Ʋąĺįƌąţįŏŋ∙∙∙∙]]]
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=[[[Ţĥē ţąƃĺē ŏŗ ʋįēŵ {0} ĥąş ƌēşįğŋ-ţįɱē ŏŗ ŗűŋţįɱē ēŗŗŏŗş.]]]
# analyzer errors
#
superflous_comma=[[[Řēɱŏʋē ċŏɱɱą∙∙∙∙∙∙∙]]]
unterminated_path=[[[Ŝēĺēċţįŏŋ įŋċŏɱρĺēţē∙∙∙∙]]]
undefined_name_0=[[[Ţĥē įƌēŋţįƒįēŗ {0} ĥąş ŋŏţ ƃēēŋ ƌēƒįŋēƌ ŏŗ ƌŏēş ŋŏţ ēχįşţ.]]]
unresolved_col_0=[[[Ţĥē ċŏĺűɱŋ {0} ċąŋŋŏţ ƃē ƒŏűŋƌ.]]]
unresolved_table_0=[[[Ţĥē ţąƃĺē ŏŗ ʋįēŵ {0} ċąŋŋŏţ ƃē ƒŏűŋƌ.]]]
ambigous_col_definition_0_1_2=[[[Ţĥē şąɱē ċŏĺűɱŋ ŋąɱē įş ƃēįŋğ ƌēƒįŋēƌ ɱŏŗē ţĥąŋ ŏŋċē įŋ {0}, {1}, ąŋƌ {2}.]]]
expected_scalar_typed_expression=[[[Ĕŋţēŗ ą şįŋğĺē ʋąĺűē.∙∙∙∙∙]]]
invalid_datatype_1_operator_2_expected_3=[[[Ţĥē ŏρēŗąţŏŗ {2} ēχρēċţş ţĥē ƌąţą ţŷρē {3}. Ĉĥąŋğē ţĥē ƌąţą ţŷρē {1}.]]]
UNRESOLVED_TABLE_OR_MISSING_QUOTE=[[[Ţĥē ţąƃĺē ŏŗ ʋįēŵ {0} ċąŋŋŏţ ƃē ƒŏűŋƌ ƃēċąűşē įţ ƌŏēşŋţ ēχįşţ ŏŗ ţĥēŗē įş ąţ ĺēąşţ ŏŋē ɱįşşįŋğ ƌŏűƃĺē ƣűŏţē. ]]]
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=[[[Ţĥē ċŏĺűɱŋ {0} ċąŋŋŏţ ƃē ƒŏűŋƌ ƃēċąűşē įţ ƌŏēşŋţ ēχįşţ ŏŗ ţĥēŗē įş ąţ ĺēąşţ ŏŋē ɱįşşįŋğ ƌŏűƃĺē ƣűŏţē. ]]]

