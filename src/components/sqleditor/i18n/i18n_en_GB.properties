#XTIT Save Dialog param
modelNameSQL=SQL View
txtSQLFileValidationError=The model view is not valid. Please fix all validation issues and try again.

#
# parser errors
#
0_not_supported={0} is not supported.
1_already_defined=The identifier {1} is already defined.
column_order_and_index_order_specified=Column order and index order cannot be specified at the same time.
annotation_0_not_supported_for_1=Annotation {0} is not supported for {1}.

expected_1_found_0={1} was expected, but found {0} instead.
expected_data_type=Data type expected.
expected_expression=Expression expected.
expected_expression_or_subquery=Expression or subquery expected.
expected_first_or_last=FIRST or LAST expected.
expected_identifier=Identifier expected.
expected_identifier_or_asterisk=Identifier or * expected.
expected_identifier_or_sql_error_code=Identifier or SQL error code expected.
expected_integer=Integer expected.
expected_language=Language expected.
expected_literal=String or numeric literal expected.
expected_number=Numeric literal expected.
expected_operator=Operator expected.
expected_path_or_identifier=Path or identifier expected.
expected_select_clause=SELECT expected.
expected_select_or_set_operator=SELECT or set operator expected.
expected_sequence_option=Sequence option expected.
expected_set_operator=Set operator expected.
expected_simple_identifier=Simple identifier expected.
expected_statement=Statement expected.
expected_string=String expected.
expected_subquery=Subquery expected.
expected_varref_found_identifier_use_1=Variable reference expected, but found identifier, use {1} instead.
#YMSG,145:SQL script semantic check warning message
txtSqlError=We're unable to derive the return statement completely from the script. Please review the list of columns and their data types in the side panel.
#XFLD: warning dialog title label
lblWarningDialogTitle=Warning
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=Changing the language to "SQL (Standard Query)" will give errors for any SQLScript keywords in your code.
#XBUT:Warning dialog ok button for DB View type
btnOk=OK
#XBUT,14:Warning dialog close button for DB View type
btnClose=Close
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=You can’t see data in a SQLScript view until all changes are deployed. Please deploy the view and try again.
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=You can’t see data in a SQLScript view until all changes are deployed. Please deploy the view and try again.
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=You can’t see data in a view protected by a data access control until all changes are deployed. Please deploy the view and try again.
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=You can’t see data in a SQLScript view until all changes are deployed. Please deploy the view and try again.
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=You can’t see data in a SQLScript view until all changes are deployed. Please deploy the view and try again.
cannotFormat=The statement cannot be formatted.
shouldHaveGroupBy=The SQL query should have a GROUP BY clause.
groupByCols=The GROUP BY clause should have all columns that are not included in aggregation functions.
@msgTableImported=''{0}''’, ''{1}''’ have been successfully imported and deployed.
@msgTableImportFailed=Problem with import: The source was not imported into the repository and therefore cannot be used in your view. To try again, please re-drop it on the editor.
VAL_SAVE_CANCELLED=The import of ''{0}''’ has been cancelled.
#XMSG
uniqueAlias=Column aliases should be unique.
#XMSG
ANALYSING_SQL=Analysing SQL
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn=Data types could not be derived for {0}. Please review the data types of these columns in the Properties panel.
#XBUT : SQL format button
format=Format
#XMSG:Format failure message
formatError=Format failed. Correct the syntax error.
#XTOL
validateAndPreview=Validate SQL and Preview Data
#XTOL
validateAndViewData=Validate SQL and View Data
#XMSG
unexpected_end=Unexpected end
#XTOL
validate=Validate SQL
#XTOL
formattol=Format SQL

#begin of change management (pop-up)
#XTIT
changeManagementTitle=Source Updates
#XCOL
changedOn=Changed On
#XCOL
changedBy=Changed By

objectDisplayNameLocation=Source
reviewText=Please review modifications and save and redeploy the updated object to apply them.
changesText=The following sources used in this object have been modified, and these modifications are marked by validation messages in the editor:
sharedEntity=Shared
#end of change management (pop-up)

#XFLD
VALIDATEFLD=Validate
#XFLD
CANCELVALIDATEFLD=Cancel Validation
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=The delta capture table {0} is not allowed for consumption.
#XMSG
DATAVALIDATION=Data Validation
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=The table or view {0} has design-time or runtime errors.
# analyzer errors
#
superflous_comma=Remove comma
unterminated_path=Selection incomplete
undefined_name_0=The identifier {0} has not been defined or does not exist.
unresolved_col_0=The column {0} cannot be found.
unresolved_table_0=The table or view {0} cannot be found.
ambigous_col_definition_0_1_2=The same column name is being defined more than once in {0}, {1} and {2}.
expected_scalar_typed_expression=Enter a single value.
invalid_datatype_1_operator_2_expected_3=The operator {2} expects the data type {3}. Change the data type {1}.
UNRESOLVED_TABLE_OR_MISSING_QUOTE=The table or view {0} cannot be found because it doesn’t exist or there is at least one missing double quote. 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=The column {0} cannot be found because it doesn’t exist or there is at least one missing double quote. 

