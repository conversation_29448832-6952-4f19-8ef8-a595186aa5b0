#XTIT Save Dialog param
modelNameSQL=Vista SQL
txtSQLFileValidationError=La vista de modelo no es válida. Corrija todos los problemas de validación e inténtelo de nuevo.

#
# parser errors
#
0_not_supported=No se admite {0}.
1_already_defined=El identificador {1} ya se ha definido.
column_order_and_index_order_specified=El orden de columna y el orden de índice no pueden especificarse al mismo tiempo.
annotation_0_not_supported_for_1=La anotación {0} no se admite para {1}.

expected_1_found_0=Se esperaba {1}, pero se ha encontrado {0}.
expected_data_type=Se espera un tipo de datos.
expected_expression=Se espera una expresión.
expected_expression_or_subquery=Se espera una expresión o subconsulta.
expected_first_or_last=Se espera FIRST o LAST.
expected_identifier=Se espera un identificador.
expected_identifier_or_asterisk=Se espera un identificador o *.
expected_identifier_or_sql_error_code=Se espera un identificador o código de error SQL.
expected_integer=Se espera un entero.
expected_language=Se espera un lenguaje.
expected_literal=Se espera un literal numérico o de cadena.
expected_number=Se espera un literal numérico.
expected_operator=Se espera un operador.
expected_path_or_identifier=Se espera una ruta o un identificador.
expected_select_clause=Se espera SELECT.
expected_select_or_set_operator=Se espera un operador SET o SELECT.
expected_sequence_option=Se espera una opción de secuencia.
expected_set_operator=Se espera un operador SET.
expected_simple_identifier=Se espera un identificador simple.
expected_statement=Se espera una declaración.
expected_string=Se espera una cadena.
expected_subquery=Se espera una subconsulta.
expected_varref_found_identifier_use_1=Se espera una referencia de variable, pero se ha encontrado un identificador. Utilice {1}.
#YMSG,145:SQL script semantic check warning message
txtSqlError=No podemos derivar la declaración de devolución entera a partir del script. Revise la lista de columnas y sus tipos de datos en el panel lateral.
#XFLD: warning dialog title label
lblWarningDialogTitle=Advertencia
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=Cambiar el idioma a "SQL (consulta estándar)" generará errores para cualquier palabra clave SQLScript en su código.
#XBUT:Warning dialog ok button for DB View type
btnOk=OK
#XBUT,14:Warning dialog close button for DB View type
btnClose=Cerrar
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=No puede ver datos en una vista SQLScript hasta que se desplieguen todas las modificaciones. Despliegue la vista e inténtelo de nuevo.
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=No puede ver datos en una vista SQLScript hasta que se desplieguen todas las modificaciones. Despliegue la vista e inténtelo de nuevo.
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=No puede ver datos en una vista protegida por un control de acceso a datos hasta que se desplieguen todas las modificaciones. Despliegue la vista e inténtelo de nuevo.
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=No puede ver datos en una vista SQLScript hasta que se desplieguen todas las modificaciones. Despliegue la vista e inténtelo de nuevo.
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=No puede ver datos en una vista SQLScript hasta que se desplieguen todas las modificaciones. Despliegue la vista e inténtelo de nuevo.
cannotFormat=No se puede formatear la declaración.
shouldHaveGroupBy=La consulta SQL debería tener una cláusula GROUP BY.
groupByCols=La cláusula GROUP BY debería tener todas las columnas que no están incluidas en las funciones de agregación.
@msgTableImported=''{0}", ''{1}" se han importado y desplegado correctamente.
@msgTableImportFailed=Problema con la importación: La fuente no se ha importado al repositorio y por tanto, no se puede usar en su vista. Inténtelo de nuevo y vuelva a soltarla en el editor.
VAL_SAVE_CANCELLED=El usuario ha cancelado la importación de ''{0}''.
#XMSG
uniqueAlias=Los alias de columna deben ser unívocos.
#XMSG
ANALYSING_SQL=Analizando SQL
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn=No se han podido derivar tipos de datos para {0}. Revise los tipos de datos de estas columnas en el panel Propiedades.
#XBUT : SQL format button
format=Formato
#XMSG:Format failure message
formatError=Error de formato. Corrija el error de sintaxis.
#XTOL
validateAndPreview=Validar datos SQL y de vista previa
#XTOL
validateAndViewData=Validar datos SQL y de vista
#XMSG
unexpected_end=Fin inesperado
#XTOL
validate=Validar SQL
#XTOL
formattol=Dar formato a SQL

#begin of change management (pop-up)
#XTIT
changeManagementTitle=Actualizaciones de origen
#XCOL
changedOn=Modificado el
#XCOL
changedBy=Modificado por

objectDisplayNameLocation=Origen
reviewText=Revise las modificaciones, guárdelas y vuelva a desplegar el objeto actualizado para aplicarlas.
changesText=Los siguientes orígenes utilizados en el objeto se han modificado y las modificaciones se han marcado con mensajes de validación en el editor:
sharedEntity=Compartido
#end of change management (pop-up)

#XFLD
VALIDATEFLD=Validar
#XFLD
CANCELVALIDATEFLD=Cancelar la validación
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=La tabla de captura delta {0} no se permite para el consumo.
#XMSG
DATAVALIDATION=Validación de datos
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=La tabla o vista {0} tiene errores en tiempo de diseño o ejecución.
# analyzer errors
#
superflous_comma=Quitar coma
unterminated_path=Selección incompleta
undefined_name_0=El identificador {0} no se ha definido o no existe.
unresolved_col_0=No se ha encontrado la columna {0}.
unresolved_table_0=No se ha encontrado la tabla o vista {0}.
ambigous_col_definition_0_1_2=Se está definiendo el mismo nombre de columna más de una vez en {0}, {1} y {2}.
expected_scalar_typed_expression=Introduzca un único valor.
invalid_datatype_1_operator_2_expected_3=El operador {2} prevé el tipo de datos {3}. Modifique el tipo de datos {1}.
UNRESOLVED_TABLE_OR_MISSING_QUOTE=No se encuentra la tabla o vista {0} porque no existe o faltan al menos unas comillas dobles. 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=No se encuentra la columna {0} porque no existe o faltan al menos unas comillas dobles. 

