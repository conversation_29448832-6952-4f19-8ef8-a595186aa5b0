#XTIT Save Dialog param
modelNameSQL=SQL 檢視
txtSQLFileValidationError=模型檢視無效。請修正所有驗證問題並再試一次。

#
# parser errors
#
0_not_supported=不支援 {0}。
1_already_defined=已定義識別碼 {1}。
column_order_and_index_order_specified=無法同時指定欄順序和索引順序。
annotation_0_not_supported_for_1=不支援 {1} 的註解 {0}。

expected_1_found_0=預期為 {1}，但卻找到 {0}。
expected_data_type=預期為資料類型。
expected_expression=預期為運算式。
expected_expression_or_subquery=預期為運算式或子查詢。
expected_first_or_last=預期為 FIRST 或 LAST。
expected_identifier=預期為識別碼。
expected_identifier_or_asterisk=預期為識別碼或 *。
expected_identifier_or_sql_error_code=預期為識別碼或 SQL 錯誤代碼。
expected_integer=預期為整數。
expected_language=預期為語言。
expected_literal=預期為字串或數字文字。
expected_number=預期為數字文字。
expected_operator=預期為運算子。
expected_path_or_identifier=預期為路徑或識別碼。
expected_select_clause=預期為 SELECT。
expected_select_or_set_operator=預期為 SELECT 或 SET 運算子。
expected_sequence_option=預期為順序選項。
expected_set_operator=預期為 SET 運算子。
expected_simple_identifier=預期為簡單識別碼。
expected_statement=預期為陳述式。
expected_string=預期為字串。
expected_subquery=預期為子查詢。
expected_varref_found_identifier_use_1=預期為變數參考，但卻找到識別碼；請改用 {1}。
#YMSG,145:SQL script semantic check warning message
txtSqlError=無法自指令碼完全衍生 return 陳述式。請審查欄清單和側邊面板中的資料類型。
#XFLD: warning dialog title label
lblWarningDialogTitle=警告
#XMSG: warning dialog message for DB type view change
txtWarningDialogContent=若將語言更改為 "SQL (標準查詢)"，程式碼中 SQLScript 關鍵字將產生錯誤。
#XBUT:Warning dialog ok button for DB View type
btnOk=確定
#XBUT,14:Warning dialog close button for DB View type
btnClose=關閉
#XMSG: Data preview warning message for not deployed SQLScript View
dataPreivewNotDeployWarning=在部署所有更改之前，您無法在 SQLScript 檢視中查看資料。請部署檢視並再試一次。
#XMSG: Data viewer warning message for not deployed SQLScript View
dataViewerNotDeployWarning=在部署所有更改之前，您無法在 SQLScript 檢視中查看資料。請部署檢視並再試一次。
#XMSG: Data viewer warning message for not deployed view with data access control
dataViewerDacNotDeployWarning=在部署所有更改之前，您無法在受資料存取控制保護的檢視中查看資料。請部署檢視並再試一次。
#XMSG: Data viewer warning message for not deployed SQLScript view
dataViewerNotSaveWarning=在部署所有更改之前，您無法在 SQLScript 檢視中查看資料。請部署檢視並再試一次。
#XMSG: Data preview warning message for not deployed SQLScript view
dataPreivewNotSaveWarning=在部署所有更改之前，您無法在 SQLScript 檢視中查看資料。請部署檢視並再試一次。
cannotFormat=無法格式化陳述式。
shouldHaveGroupBy=SQL 查詢應具有 GROUP BY 子句。
groupByCols=GROUP BY 子句應具有所有加總函式未包含的欄。
@msgTableImported=已成功匯入並部署 "{0}"、"{1}"。
@msgTableImportFailed=匯入發生問題：來源未匯入至儲藏庫，因此無法用於您的檢視中。若要再試一次，請將來源再次拖放至編輯器上。
VAL_SAVE_CANCELLED=已取消匯入 "{0}"。
#XMSG
uniqueAlias=欄別名應是唯一。
#XMSG
ANALYSING_SQL=分析 SQL
#XMSG,100: warning message for columns which data type couldn’t be derived
txtWarningDatatypeMissingColumn=無法衍生 {0} 的資料類型。請在屬性面板中審查這些欄的資料類型。
#XBUT : SQL format button
format=格式
#XMSG:Format failure message
formatError=格式失敗。請更正語法錯誤。
#XTOL
validateAndPreview=驗證 SQL 並預覽資料
#XTOL
validateAndViewData=驗證 SQL 並檢視資料
#XMSG
unexpected_end=未預期的結尾
#XTOL
validate=驗證 SQL
#XTOL
formattol=格式化 SQL

#begin of change management (pop-up)
#XTIT
changeManagementTitle=來源更新
#XCOL
changedOn=更改日期
#XCOL
changedBy=更改者

objectDisplayNameLocation=來源
reviewText=請審查修改，儲存並重新部署更新物件以進行套用。
changesText=此物件中使用的下列來源已修改，且驗證訊息將在編輯器中標記這些修改：
sharedEntity=已共用
#end of change management (pop-up)

#XFLD
VALIDATEFLD=驗證
#XFLD
CANCELVALIDATEFLD=取消驗證
#XMSG
DELTATABLEPROHIBITEDTOBEUSEDWITHINTHISCONTEXT=不允許使用差異擷取表格 {0}。
#XMSG
DATAVALIDATION=資料驗證
#XMSG The table or view has deployment status error
VAL_BASENTITY_STATUS_ERROR=表格或檢視 {0} 發生設計階段或執行時期錯誤。
# analyzer errors
#
superflous_comma=移除逗號
unterminated_path=選擇不完整
undefined_name_0=識別碼 {0} 未定義或不存在。
unresolved_col_0=找不到欄 {0}。
unresolved_table_0=找不到表格或檢視 {0}。
ambigous_col_definition_0_1_2={0}、{1} 和 {2} 中已定義相同欄名稱超過一次。
expected_scalar_typed_expression=請輸入單一直值
invalid_datatype_1_operator_2_expected_3=運算子 {2} 預期為資料類型 {3}。請更改資料類型 {1}。
UNRESOLVED_TABLE_OR_MISSING_QUOTE=由於表格或檢視 {0} 不存在，或至少有一個缺少雙引號，因此找不到。 
UNRESOLVED_COLUMN_OR_MISSING_QUOTE=由於欄 {0} 不存在，或至少有一個缺少雙引號，因此找不到。 

