/** @format */

import { State } from "@sap/dwc-circuit-breaker";
import { analyzeSql } from "../../../services/metadata";
import {
  AbstractController,
  AbstractControllerClass,
} from "../../abstractbuilder/controller/AbstractController.controller";
import { openAndGetSaveDialogResult } from "../../abstractbuilder/utility/WorkbenchUtils";
import { smartExtend } from "../../basecomponent/controller/BaseController.controller";
import { DocumentStorageService } from "../../commonmodel/api/DocumentStorageService";
import {
  cloneJson,
  getEntityAndContextNameFromCSN,
  handleCsnForDataPreview,
  removeCrossSpaceReferencesInCSN,
  updateCsnQueryAliases,
} from "../../commonmodel/csn/csnUtils";
import {
  DBViewType,
  DataCategory,
  ValidationMessageGroup,
  getDataCategoryFromBusinessType,
} from "../../commonmodel/model/types/cds.types";
import { isAnalyticMeasureElement } from "../../commonmodel/utility/CommonUtils";
import { getIsVersioningReadOnlyMode } from "../../databuilder/utility/DatabuilderHelper";
import { ChangeManagementInfoDialog } from "../../reuse/control/changeManagement/ChangeManagementInfoDialog";
import { MessageHandler } from "../../reuse/utility/MessageHandler";
import { ObjectStatus } from "../../reuse/utility/Types";
import { Logger } from "../../reuse/utility/UIHelper";
import { isCircuitBreakerYellowStateEnabled } from "../../shell/featureState/CircuitBreakerYellowStateFeatureState";
import { Crud } from "../../shell/utility/Crud";
import { Repo } from "../../shell/utility/Repo";
import { ISQLEditorExtensions } from "../Component";
import "../css/styles.css";
import { formatSql, getDependentObjects, getParsedResultNew, parseSql } from "../utility/ParserUtil";
import {
  buildSqlFromSelectCqn,
  createCSNDocument,
  createContextObject,
  getAutoCompleteParamMappings,
  getDependencyExtractorHelper,
  getErrorDetails,
  getInfoCommentText,
  getObjectColumns,
  getParamMappings,
  getTooltip,
  loadCrossSpaceEntity,
  parseSqlToCqn,
  prepareSQLScript,
  prepareSelectStatement,
  processSQLScript,
  removeMapping,
  replaceSemiColon,
  updateSubQueryAliases,
} from "../utility/SqlEditorUtility";

export class SQLEditorClass extends AbstractControllerClass {
  public static KEY_WORDS = [
    "SELECT",
    "FROM",
    "WHERE",
    "AS",
    "JOIN",
    "LEFT JOIN",
    "RIGHT JOIN",
    "INNER JOIN",
    "FULL JOIN",
    "CROSS JOIN",
    "UNION",
    "INTERSECT",
    "EXCEPT",
    "GROUP BY",
    "ORDER BY",
    "LIMIT",
    "OFFSET",
    "ON",
    "UNION ALL",
    "HAVING",
    "DISTINCT",
    "MINUS",
    "TOP",
    "PRECENT",
    "ASC",
    "DESC",
    "IN",
    "LIKE",
    "BETWEEN",
    "AND",
    "OR",
    "IS NULL",
    "IS NOT NULL",
    "EXISTS",
    "AVG",
    "COUNT",
    "MIN",
    "MAX",
    "MIXIN",
    "SUM",
    "STDDEV",
    "VAR",
    "BINNING",
    "CORR",
    "CORR_SPEARMAN",
    "FIRST_VALUE",
    "NTH_VALUE",
    "LAST_VALUE",
    "MEDIAN",
    "CUBIC_SPLINE_APPROX",
    "CUME_DIST",
    "DENSE_RANK",
    "LAG",
    "LEAD",
    "LINEAR_APPROX",
    "NTILE",
    "PERCENT_RANK",
    "RANDOM_PARTITION",
    "RANK",
    "ROW_NUMBER",
    "SERIES_FILTER",
    "WEIGHTED_AVG",
    "OVER",
    "PARTITION BY",
  ];
  private codeEditorDomRef: any;
  private oFeatures: any;
  private resourceModel: any;
  private currentDraggingData: any;
  private isDragPropertyStarted: boolean;
  private options: { repositoryObjects; crossSpaceObjects };
  private _pendingDroppedEntities: any;
  private codeEditor: any;
  private innerAceEditor: any;
  private space_id: string;
  private fieldsSuggestion: any;
  private component: any;
  private tokenTooltip: any;
  private languageTool: any;
  private isInitialValue: boolean;
  private workbench: any;
  private warningDialog: any;
  private keyWorkdsSuggestion: any;
  private delayedShowPopup: any;
  private bShowPopup: boolean;
  private notShowPopup: any;
  private formatSqlText: any;
  private parseSQLValue: any;
  private dependentValues: any;
  private bAnalyzeSql: boolean;
  private changeManagementDialogOpeningOrOpen;
  private runTimeErrorViews = [];

  public onInit(): void {
    const bundleName = require("../i18n/i18n.properties");
    this.oFeatures = (sap.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel).getData();
    this.resourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: bundleName,
    });
    this.getView().setModel(this.resourceModel, "i18n");
    const oDBViewTypeModel = new sap.ui.model.json.JSONModel({ tablefunction: DBViewType.TABLEFUNCTION });
    this.getView().setModel(oDBViewTypeModel, "dbviewtype");
    const oSqlEditorModel = new sap.ui.model.json.JSONModel({});
    this.getView().setModel(oSqlEditorModel, "sqlEditorModel");
    sap.ui.getCore().setModel(oSqlEditorModel, "sqlEditorModel");
    this.codeEditor = this.getView().byId("sqlEditor");
    this.innerAceEditor = this.codeEditor.getInternalEditorInstance();
    sap.ui.getCore().getEventBus().subscribe("SOURCES_LIST", "DRAG_ENTER", this.onDefaultSourceDragEnter.bind(this));
    sap.ui.getCore().getEventBus().subscribe("SOURCES_LIST", "DRAG_END", this.onSourceDragEnd.bind(this));
    sap.ui.getCore().getEventBus().subscribe("PROPERTY_PANEL", "DRAG_ENTER", this.onPropertyDragEnter.bind(this));
    sap.ui.getCore().getEventBus().subscribe("PROPERTY_PANEL", "DRAG_END", this.onPropertyDragEnd.bind(this));
    sap.ui.getCore().getEventBus().subscribe("PROBLEM_VIEW", "CELL_CLICK", this.onProblemViewClick.bind(this));
    sap.ui.getCore().getEventBus().subscribe("PROPERTY_PANEL", "CHANGE_DBVIEWTYPE", this.onDBViewTypeChange.bind(this));
    sap.ui
      .getCore()
      .getEventBus()
      .subscribe("OUTPUT_VALIDATION", "REFRESH_VALIDATION", this.updateValidationMessage.bind(this));
    this.getView().setModel(
      new sap.ui.model.json.JSONModel({
        titleVisible: false,
      }),
      "extensions"
    );
  }
  async updateValidationMessage() {
    const problems = this.workbench?.workbenchModel?.getProperty("/detailsPage/problems");
    for (const problem of problems) {
      const nsValidationStatus = sap.cdw.commonmodel.ValidationStatus;
      const oModel = await this.component.getGalileiModel();
      if (problem.type === "Warning" && oModel?.output?.dbViewType !== DBViewType.TABLEFUNCTION && problem.message) {
        nsValidationStatus.createWarnInstance(oModel.output, ValidationMessageGroup.SQLERROR, problem.message, []);
      } else if ((problem.type === "Error" || problem.type === "SemanticError") && problem.message) {
        const aError = problem?.message?.match(/Could not find table\/view\s+(.+)\s+in schema/);
        const isDACError = problem?.message?.match(this.localizeText("dataViewerDacNotDeployWarning"));
        if (!aError && !isDACError) {
          nsValidationStatus.createErrorInstance(oModel.output, ValidationMessageGroup.SQLERROR, problem.message, []);
        }
      }
    }
  }
  /**
   * Localize given text id & parameters
   * @param text property id
   * @param parameters property parameters
   */
  public localizeText(text: string, parameters: any[] = []): string {
    return this.resourceModel.getResourceBundle().getText(text, parameters);
  }

  public setWorkBench(workbench) {
    this.workbench = workbench;
  }

  public setEditorConfiguration(editorConfiguration: ISQLEditorExtensions) {
    super.setEditorConfiguration(editorConfiguration);
    this.handleUiExtensions();
  }

  onPropertyDragEnter(): void {
    this.isDragPropertyStarted = true;
  }
  onPropertyDragEnd(): void {
    this.isDragPropertyStarted = false;
  }

  onProblemViewClick(channelId: string, event: string, oData): void {
    if (oData && oData.locationStart) {
      this.innerAceEditor.gotoLine(oData.locationStart.line, oData.locationStart.column, true);
      this.codeEditor.focus();
    }
  }

  onWarningDialogClosed(): void {
    this.warningDialog.close();
    this.warningDialog.destroy();
  }
  /**
   * On change of DB view type
   */
  onDBViewTypeChange(channelId: string, event: string, dbviewType): void {
    if (dbviewType === DBViewType.SQL) {
      const fragmentId = require("../view/DBViewTypeWarningDialog.fragment.xml");
      this.warningDialog = sap.ui.xmlfragment("", fragmentId, this);
      this.getView().addDependent(this.warningDialog);
      this.warningDialog.open();
    }

    if (dbviewType === DBViewType.TABLEFUNCTION) {
      const helpComment = getInfoCommentText();
      const sqlEditorText = this.innerAceEditor.getValue();
      if (!sqlEditorText.includes(helpComment)) {
        const newScript = helpComment + sqlEditorText;
        this.setValue(newScript, false);
      }
    } else if (this.oFeatures.DWCO_SQL_VIEW_UI_ENHANCEMENTS) {
      const helpComment = getInfoCommentText();
      const sqlEditorText = this.innerAceEditor.getValue();
      const sqlEditorQuery = sqlEditorText?.replace(helpComment, "");
      this.setValue(sqlEditorQuery, false);
    }
  }

  onDefaultSourceDragEnter(channelId: string, event: string, oData): void {
    const bundleName = require("../i18n/i18n.properties");
    const resourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: bundleName,
    });
    const bundle = resourceModel.getResourceBundle();
    if (this.getView().getVisible()) {
      if (oData.isAlreadyDeployed === false) {
        this.buttonEnable(false);
        oData.saveAndDeploy = new Promise((resolve, reject) => {
          oData.resolveSaveAndDeploy = resolve;
          oData.rejectSaveAndDeploy = reject;
        });
        oData.saveAndDeploy
          .then((def) => {
            const csn = def.definitions[def.newEntityId];
            const schemaOrConnection =
              csn["@DataWarehouse.remote.connection"] ||
              csn["@DataWarehouse.external.schema"] ||
              csn["@DataWarehouse.externalSchema.schema"]; // TODO: Remove @DataWarehouse.externalSchema.schema
            const tableId =
              csn["@DataWarehouse.remote.entity"] ||
              csn["@DataWarehouse.external.entity"] ||
              csn["@DataWarehouse.remote.table"] ||
              csn["@DataWarehouse.externalSchema.table"]; // TODO: @DataWarehouse.remote.table, @DataWarehouse.externalSchema.table
            sap.m.MessageToast.show(bundle.getText("@msgTableImported", [schemaOrConnection, tableId]));
            this.refreshData();
          })
          .catch((error) => {
            // DEFAULT CATCH IN CASE IF ERROR HAPPENS BEFORE DROP otherwie we get "uncaught (in promise)" exception
            if (typeof error === "object" && error.type && error.type === "cancel") {
              sap.m.MessageToast.show(error.errorMsg);
            } else {
              MessageHandler.error(
                bundle.getText("@msgTableImportFailed"),
                /* title*/ undefined,
                /* duration*/ undefined,
                error
              );
            }
            this.buttonEnable(true);
          });
      } else {
        oData.isAlreadyDeployed = true;
      }
      this.currentDraggingData = oData;
    }
  }

  async refreshData() {
    const repositoryObjects = await Repo.getModelList(this.space_id, ["id", "name", "#businessType", "qualified_name"]);
    this.options.repositoryObjects = repositoryObjects.filter((repo) => {
      // const definition = repo.csn && repo.csn.definitions;
      // const modelObjects = definition && Object.keys(definition);
      repo["spaceName"] = this.space_id;
      const dataCategory = getDataCategoryFromBusinessType(repo["#businessType"]);
      return !(dataCategory === DataCategory.CUBE);
    });
    this.buttonEnable(true);
  }

  setComponent(component) {
    this.component = component;
  }

  async getRepositoryObjects() {
    if (this.options === undefined) {
      this.options = { repositoryObjects: [], crossSpaceObjects: [] };
    } else if (this.options.repositoryObjects === undefined || this.options.crossSpaceObjects === undefined) {
      this.options.repositoryObjects = [];
      this.options.crossSpaceObjects = [];
    }
    if (this.options.repositoryObjects.length === 0) {
      await this.refreshData();
    }
    return this.options;
  }

  buttonEnable(enable): void {
    if (this.getView().getParent() && this.getView().getParent().getModel("workbenchEnv")) {
      (this.getView().getParent().getModel("workbenchEnv") as sap.ui.model.json.JSONModel).setProperty(
        "/validateButton",
        enable
      );
      (this.getView().getParent().getModel("workbenchEnv") as sap.ui.model.json.JSONModel).setProperty(
        "/dataPreviewButton",
        enable
      );
      (this.getView().getParent().getModel("workbenchEnv") as sap.ui.model.json.JSONModel).setProperty(
        "/deployButton",
        enable
      );
      (this.getView().getParent().getModel("workbenchEnv") as sap.ui.model.json.JSONModel).setProperty(
        "/saveButton",
        enable
      );
    }
  }

  async notifyDropEntity() {
    const oData = this.currentDraggingData;
    const bundleName = require("../i18n/i18n.properties");
    const resourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: bundleName,
    });
    const oFeatures = (sap.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel).getData();
    const bundle = resourceModel.getResourceBundle();
    if (oData && oData.isAlreadyDeployed === false) {
      const self = this;
      const quotedName = '"' + oData.technicalName + '"';
      const range = this.innerAceEditor.selection.getRange();
      // request save & deploy
      const names = (await openAndGetSaveDialogResult(
        this.getWorkbenchController(),
        {
          technicalName: oData.technicalName,
          businessName: oData.businessName,
        },
        oData.existingTechincalIds
      )) as any;

      try {
        this.getView().setBusy(true);
        if (!names || !names.technicalName) {
          oData.rejectSaveAndDeploy({
            type: "cancel",
            errorMsg: bundle.getText("VAL_SAVE_CANCELLED", [oData.technicalName]),
          });
          self.innerAceEditor.session.replace(range, "");
          self.innerAceEditor.selection.setRange(range);
          this.innerAceEditor.clearSelection();
          self.getView().setBusy(false);
          return;
        }
        const pendingDropped = this._pendingDroppedEntities[quotedName];
        if (pendingDropped && pendingDropped !== names.technicalName) {
          self.innerAceEditor.session.replace(range, '"' + names.technicalName + '"');
          self.innerAceEditor.selection.setRange(range);
          this.innerAceEditor.clearSelection();
        }
        self.getView().setBusy(false);
      } catch (e) {
        this.getView().setBusy(false);
      }
      delete this._pendingDroppedEntities[quotedName];
      oData.technicalName = names.technicalName;
      oData.businessName = names.businessName ? names.businessName : names.technicalName;
      oData.folderAssignment = this?.workbench?.folderAssignment ? this?.workbench?.folderAssignment : undefined;
      sap.ui.getCore().getEventBus().publish("SOURCES_LIST", "SAVE_DEPLOY_ENTITY", oData); // request save & deploy
      delete this.currentDraggingData;
    } else if (oData && this.component && oData.definitions) {
      const dropDefinitions = oData.definitions;
      const { entityName, contextName } = getEntityAndContextNameFromCSN(dropDefinitions);
      const oModel = await this.component.getGalileiModel();
      const oErCsnToModel = sap.cdw.ermodeler.ErCsnToModel.getInstance(true);
      // Create contexts
      oErCsnToModel.createContextAndAttachToObject(oModel, undefined, contextName, dropDefinitions[contextName]);
      // Create simple types and attach them to objects
      oErCsnToModel.importObjectsFromCSN(oModel, dropDefinitions, ["type"]);
      if (oData && contextName) {
        // Handle cross space entities
        const copyObj = cloneJson(oData);
        const objFile: any = {};
        objFile.csn = copyObj;
        objFile.name = objFile.qualified_name = entityName;
        objFile["#writeAccess"] = false;
        objFile["@cds.persistence.exists"] = true;
        const { repositoryObjects, crossSpaceObjects } = await this.getRepositoryObjects();
        const crossSpaceObj = crossSpaceObjects.find((obj) => obj.name === entityName);
        if (!crossSpaceObj) {
          crossSpaceObjects.push(objFile);
        }
      }
    }
    const editorConfiguration = this.getEditorConfiguration() as ISQLEditorExtensions;
    if (editorConfiguration?.onDropEntity) {
      (sap.cdw.querybuilder.ModelImpl as any).publishDroppedEntityEvent({
        data: oData,
      });
    }
  }

  onSourceDragEnd(): void {
    // nothing
  }

  setReadOnly(readOnly: boolean) {
    if (this.codeEditor) {
      this.codeEditor.setEditable(!readOnly);
    }
  }

  getResourceBundle(): any {
    return this.resourceModel && (this.resourceModel.getResourceBundle() as UI5TransitionHelper.ResourceBundle);
  }

  getText(sKey: string): string {
    const oBundle = this.getResourceBundle();
    return oBundle && oBundle.getText(sKey);
  }

  async getRepositoryObject(objectName: string): Promise<{
    id: string;
    name: string;
    csn: any;
  }> {
    return (await Repo.getModelDetails(this.space_id, objectName, ["id", "name", "csn"])) as any; // TODO: find a way to avoid any cast
  }

  async getRepositoryObjectDocumentation(objectName: string): Promise<string> {
    let columns = [];
    objectName = objectName.replace(/"/g, "");
    const { repositoryObjects, crossSpaceObjects } = await this.getRepositoryObjects();
    let object =
      repositoryObjects &&
      repositoryObjects.filter(function (obj) {
        return obj.qualified_name === objectName;
      });
    if (object.length === 0) {
      object =
        crossSpaceObjects &&
        crossSpaceObjects.filter(function (obj) {
          return obj.qualified_name === objectName;
        });
    }
    if (object.length > 0) {
      if (!object[0].csn) {
        const csnDefinitions = await Repo.getModelDetails(object[0].spaceName, object[0].name, ["csn"]);
        object[0].csn = csnDefinitions.csn;
      }
      const definition = object[0].csn.definitions && object[0].csn.definitions[objectName];
      if (definition && definition.elements) {
        columns = Object.keys(definition.elements).map((name) => Object.assign({ name }, definition.elements[name]));
      }
    }
    if (columns && columns.length <= 0) {
      return null;
    }
    columns =
      columns &&
      columns.filter(function (key) {
        return !(isAnalyticMeasureElement(key) || key.type === "cds.Association");
      });
    const escape = (str) => str.replace(/`/g, "\\`");

    return (
      `View  ${escape(objectName)}\n\n` +
      columns.map((col) => `${escape(col.name)} ${col.key ? "(*) " : " "}${col.type}\n`).join("")
    );
  }

  getFieldDocumentation(field: { name: string; data: { [owner: string]: any } }): string {
    const escape = (str: string) => str.replace(/`/g, "\\`");
    return (
      `<p><strong>Field <code>${escape(field.name)}</code></strong></p>` +
      "<ul>" +
      Object.keys(field.data)
        .map((name) => `<li>${escape(name)} <code>${field.data[name].type}</code></li>`)
        .join("")
        .concat("</ul>")
    );
  }

  async onAfterRendering() {
    const self = this;
    const oCustomCompleter = {
      getCompletions: async function (callback, context) {
        if (self.space_id == null) {
          return;
        }
        const findQuotes = (str) => {
          const aStrArr = str.split("");
          let quoteCount = 0;
          aStrArr.forEach((element) => {
            if (element === '"') {
              quoteCount++;
            }
          });
          return quoteCount % 2 === 0 || quoteCount === 0 ? true : false;
        };
        const position = self.innerAceEditor.getCursorPosition();
        const line = self.innerAceEditor.getSession().getLine(position.row);
        const textLeftToPosition = line.slice(0, position.column);
        const textRightToPosition = line.slice(position.column, line.length + 1);
        const listofWords = textLeftToPosition && textLeftToPosition.trim().split(" ");
        const lastWord = listofWords && listofWords[listofWords.length - 1];
        const lastChar = textLeftToPosition && textLeftToPosition.trim().substr(-1);
        const insertBeginQuote = findQuotes(textLeftToPosition);
        const insertEndQuote = findQuotes(textRightToPosition);

        const getIdentifierInsertText = (str) => {
          if (typeof str === "string") {
            str = str.replace(/"/g, '""');
            if (insertBeginQuote) {
              str = '"' + str;
            }
            if (insertEndQuote) {
              str = str + '"';
            }
          }
          return str;
        };

        let suggestions = [];
        if (!self.keyWorkdsSuggestion) {
          self.keyWorkdsSuggestion = [];
          for (const keyword of SQLEditorClass.KEY_WORDS) {
            self.keyWorkdsSuggestion.push({
              name: keyword,
              value: keyword,
              meta: "Keyword",
              score: 10000,
            });
          }
        }
        if (
          lastWord &&
          (!SQLEditorClass.KEY_WORDS.includes(lastWord.toUpperCase()) || self.oFeatures.DWCO_MODELING_BROWSER_LAZY) &&
          lastChar &&
          lastChar !== "."
        ) {
          suggestions = suggestions.concat(...self.keyWorkdsSuggestion);
        }

        const aExcludeKind = ["sap.dwc.ermodel", "sap.csn.element", "sap.dis.dataflow"];
        const oModel = await self.component.getGalileiModel();
        const dbViewType = oModel?.output?.dbViewType;
        // Repository object name suggestions
        if (lastChar !== ".") {
          oModel?.output?.parameters?.forEach((parameter: sap.cdw.commonmodel.Parameter) => {
            suggestions.push({
              name: ":" + parameter.name,
              value: ":" + parameter.name,
              meta: "Object",
            });
          });

          if (!self.oFeatures.DWCO_MODELING_BROWSER_LAZY) {
            const { repositoryObjects, crossSpaceObjects } = await self.getRepositoryObjects();
            for (const obj of repositoryObjects || []) {
              const objectValue = getIdentifierInsertText(obj.name);
              if (!aExcludeKind.includes(obj.kind)) {
                suggestions.push({
                  name: obj.name,
                  value: objectValue,
                  meta: "Object",
                  completer: {
                    insertMatch: async function (editor, data) {
                      let filterText = editor.completer.completions.filterText;
                      const removeFilterText = function () {
                        if (filterText) {
                          const ranges = editor.selection.getAllRanges();
                          for (var i = 0, range; (range = ranges[i]); i++) {
                            range.start.column -= filterText.length;
                            editor.session.remove(range);
                          }
                        }
                      };
                      removeFilterText();
                      self.innerAceEditor.execCommand("insertstring", data.value);
                      const objectValue = await getAutoCompleteParamMappings(dbViewType, obj);
                      filterText = data.value;
                      if (objectValue) {
                        removeFilterText();
                        self.innerAceEditor.execCommand("insertstring", objectValue);
                      }
                    },
                  },
                });
              }
            }
            for (const obj of crossSpaceObjects || []) {
              if (!aExcludeKind.includes(obj.kind)) {
                suggestions.push({
                  name: obj.name,
                  value: getIdentifierInsertText(obj.name),
                  meta: "Object",
                });
              }
            }
          }
        }

        function analyzeSQL() {
          try {
            analyzeSql(self.space_id, self.innerAceEditor.getValue()).then(
              async ({ identifiers }: any) => {
                self.fieldsSuggestion = [];
                if (identifiers) {
                  for (const identifier of identifiers) {
                    const columns = await getObjectColumns(identifier, self.options.repositoryObjects);
                    if (columns) {
                      for (const column of columns) {
                        self.fieldsSuggestion.push({
                          name: column,
                          value: getIdentifierInsertText(column),
                          meta: "Column",
                        });
                      }
                    }
                  }
                  // Remaining identifiers must be aliases
                  if (lastChar !== ".") {
                    for (const name of identifiers) {
                      if (self.fieldsSuggestion.some((suggestion) => suggestion.name === name)) {
                        continue;
                      }
                      self.fieldsSuggestion.push({
                        name: name,
                        value: getIdentifierInsertText(name),
                        meta: "Column",
                      });
                    }
                  }
                }
              },
              (err) => {
                Logger.logError(err);
              }
            );
          } catch (error) {
            Logger.logError(error);
          }
        }

        function updateAutoComplete() {
          analyzeSQL();
          if (self.fieldsSuggestion.length > 0) {
            suggestions = suggestions.concat(self.fieldsSuggestion);
          }
          callback(null, suggestions);
        }
        if (self.oFeatures.DWCO_MODELING_BROWSER_LAZY) {
          if (self.bShowPopup) {
            // Delayed show popup
            self.bShowPopup = false;
            if (textLeftToPosition.length && textLeftToPosition[position.column - 1] !== " ") {
              if (self.bAnalyzeSql) {
                analyzeSQL();
                self.bAnalyzeSql = false;
              }
              if (self.fieldsSuggestion.length > 0) {
                suggestions = suggestions.concat(self.fieldsSuggestion);
              }
              const aValues = lastWord.split("");
              let searchValue = "";
              aValues.forEach((v) => {
                searchValue += `*${v}`;
              });
              const listOfInput = textLeftToPosition.split(" ");
              const lastInput = listOfInput && listOfInput[listOfInput.length - 1];
              if (lastInput.length > 2) {
                const filterResult = await DocumentStorageService.getInstance().browseObjects({
                  filter: {
                    technicalTypes: ["DWC_LOCAL_TABLE", "DWC_REMOTE_TABLE", "DWC_VIEW", "DWC_IDT"],
                    spaceID: self.spaceGUID,
                  },
                  searchValue: {
                    ppty: "name",
                    type: "startwith",
                    value: searchValue,
                  },
                  top: 50,
                });
                const filterRepoObjects = filterResult.value;
                for (const obj of filterRepoObjects || []) {
                  const objectValue = getIdentifierInsertText(obj.name);
                  suggestions.push({
                    name: obj.name,
                    value: objectValue,
                    meta: "Object",
                    completer: {
                      insertMatch: async function (editor, data) {
                        let filterText = editor.completer.completions.filterText;
                        const removeFilterText = function () {
                          if (filterText) {
                            const ranges = editor.selection.getAllRanges();
                            for (var i = 0, range; (range = ranges[i]); i++) {
                              range.start.column -= filterText.length;
                              editor.session.remove(range);
                            }
                          }
                        };
                        removeFilterText();
                        self.innerAceEditor.execCommand("insertstring", data.value);
                        const objectValue = await getAutoCompleteParamMappings(dbViewType, obj);
                        filterText = data.value;
                        if (objectValue) {
                          removeFilterText();
                          self.innerAceEditor.execCommand("insertstring", objectValue);
                        }
                      },
                    },
                  });
                }
              }
              callback(null, suggestions);
            } else {
              updateAutoComplete();
            }
          } else {
            updateAutoComplete();
          }
        } else {
          updateAutoComplete();
        }
      },
    };

    sap.ui.require(["sap/ui/codeeditor/js/ace/ace"], function (ace) {
      const langTools = ace.require("ace/ext/language_tools");

      const oop = ace.require("ace/lib/oop");
      const event = ace.require("ace/lib/event");
      const range = ace.require("ace/range");
      const tooltip = ace.require("ace/tooltip");

      const TokenTooltip = getTooltip(event, range, tooltip, oop);
      self.tokenTooltip = new TokenTooltip(self.innerAceEditor, self);

      ace.require("ace/tooltip").Tooltip.prototype.setPosition = function (x, y) {
        y -= $(this.$parentNode).offset().top;
        x -= $(this.$parentNode).offset().left;

        this.getElement().style.left = x + "px";
        this.getElement().style.top = y + "px";
        this.getElement().style.position = "absolute";
      };
      self.languageTool = langTools;
      if (langTools) {
        langTools.setCompleters();
        self.codeEditor.addCustomCompleter(oCustomCompleter);
      }
    });

    this.innerAceEditor.commands.addCommand({
      name: "myCommand",
      bindKey: { win: "Ctrl-S", mac: "Command-S" },
      exec: function () {
        if (self.component) {
          self.component.save();
        }
      },
    });
    if (this.codeEditor !== null) {
      this.codeEditorDomRef = this.getView().getDomRef();
      this.clearDecorations();
      this.codeEditor.attachLiveChange(
        {},
        (evt, oData) => {
          if (self.oFeatures.DWCO_MODELING_BROWSER_LAZY && self.innerAceEditor.completer) {
            const completer = self.innerAceEditor.completer;
            if (!completer.popup && !self.bAnalyzeSql) {
              // Type every word only call once analyzeSql
              self.bAnalyzeSql = true;
            }
            completer.destroy();
          }
          const value = this.innerAceEditor.getValue();
          const prevValue = this.getView().getModel("sqlEditorModel").getProperty("/value");
          if (value !== prevValue) {
            if (!value && prevValue) {
              // When user clears editor text, we no need to ask him for Save
              (this.getView().getModel("sqlEditorModel") as sap.ui.model.json.JSONModel).setProperty(
                "/isSqlEditorDirty",
                false
              );
            } else if (!this.isInitialValue) {
              (this.getView().getModel("sqlEditorModel") as sap.ui.model.json.JSONModel).setProperty(
                "/isSqlEditorDirty",
                true
              );
              (this.getView().getModel("sqlEditorModel") as sap.ui.model.json.JSONModel).setProperty(
                "/isSqlEditorValidate",
                false
              );
            }
          } else {
            /* When user opens editor and which doesn't conatain any text or
          after Save/deploy , it has same previous and current editor values */
            (this.getView().getModel("sqlEditorModel") as sap.ui.model.json.JSONModel).setProperty(
              "/isSqlEditorDirty",
              false
            );
          }
          this.isInitialValue = false;

          if (self.oFeatures.DWCO_MODELING_BROWSER_LAZY) {
            if (!self.notShowPopup) {
              // Format Sql not show popup
              const input = evt.mParameters?.editorEvent?.lines[0];
              let completer = self.innerAceEditor.completer;
              if ((!completer || (completer && !completer.popup)) && input.length === 1) {
                clearTimeout(self.delayedShowPopup);
                self.delayedShowPopup = setTimeout(() => {
                  completer = self.innerAceEditor.completer;
                  if (completer) {
                    self.bShowPopup = true;
                    completer.showPopup(self.innerAceEditor, null);
                  }
                }, 600);
              }
            } else {
              self.notShowPopup = false;
            }
          }
        },
        this
      );

      this.fieldsSuggestion = [];

      // Handle drag & drop
      this.codeEditorDomRef.addEventListener("dragenter", (evt) => {
        if (!this.isDragPropertyStarted) {
          this.codeEditor.focus();
        }
      });

      this.codeEditorDomRef.addEventListener("dragover", (evt) => {
        evt.preventDefault();
      });

      this.codeEditorDomRef.addEventListener("drop", async (evt) => {
        evt.stopImmediatePropagation();
        const insert = evt.dataTransfer.getData("text/plain");
        if (insert != null) {
          const range = this.innerAceEditor.selection.getRange();
          const oModel = this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel;
          const dbViewType = oModel.getData().output.dbViewType;
          const isTFViewWithParmetersSupported = this.getEditorConfiguration()?.viewWithParametersSupported;
          const paramMappings = await getParamMappings(
            insert,
            dbViewType,
            this.options,
            isTFViewWithParmetersSupported
          );
          if (paramMappings) {
            this.innerAceEditor.session.replace(range, paramMappings);
            this.innerAceEditor.selection.setRange(range);
            this.innerAceEditor.clearSelection();
          }
          this.notifyDropEntity();
          if (!this._pendingDroppedEntities) {
            this._pendingDroppedEntities = {};
          }
          this._pendingDroppedEntities[insert] = {
            range: range,
            text: insert,
          };
        }
        this.innerAceEditor.clearSelection();
      });
    }
  }

  onExit() {
    // clearInterval(this.resizeInterval);
  }

  public async setContextObject(spaceId: string, modelId: string) {
    this.space_id = spaceId;
    this.spaceGUID = await Crud.get().getGUID([{ type: Repo.space, name: spaceId }]);
    if (this.options !== undefined) {
      this.options = { repositoryObjects: [], crossSpaceObjects: [] };
    }
  }
  /**
   * SQL validate event
   * @param event
   *
   */
  public onValidate(event: IEvent<sap.m.Button, {}>): Promise<void> {
    return this.component.onValidate(event);
  }
  /**
   * Get the CSN for the current SQL.
   * @param entityName
   * @param options
   *    model
   *    repositoryObjects
   */
  public async getSqlAsCsn(
    entityName: string = "New SQL Definition",
    options: any = {},
    dbViewType: any = DBViewType.SQL,
    isDeploy: boolean = false,
    isDataValidationTriggered: boolean = false,
    query?: any,
    isValidateSQLCall?: boolean
  ) {
    let sql = this.innerAceEditor.getValue();
    const { repositoryObjects, crossSpaceObjects } = await this.getRepositoryObjects();
    options.repositoryObjects = repositoryObjects;
    options.crossSpaceObjects = crossSpaceObjects;
    let text;
    const isSqlScript = dbViewType && dbViewType === DBViewType.TABLEFUNCTION;
    let bodyScript,
      fromPos,
      toPos,
      isStarColumn,
      isSQLDerived = false,
      isComplexQuery = false;
    if (isSqlScript) {
      const dummyTable = "dummy";
      bodyScript = sql;
      const fullScript = prepareSQLScript(sql);
      try {
        const parsedQuery = await parseSql(fullScript, true);
        if (parsedQuery.error) {
          this.logError(parsedQuery.error, false, fullScript);
          throw parsedQuery.error;
        }
        const { isStarColumnScript, selectColumns, tableName } = parsedQuery;
        this.clearDecorations();
        isStarColumn = isStarColumnScript;
        if (Array.isArray(parsedQuery?.selectColumns) && parsedQuery?.selectColumns?.length <= 0 && !isStarColumn) {
          isSQLDerived = false;
        } else {
          isSQLDerived = true;
        }
        const { selectQuery, fPos, tPos } = prepareSelectStatement(selectColumns, tableName);
        fromPos = fPos;
        toPos = tPos;
        sql = selectQuery;
        if (dummyTable === tableName) {
          isComplexQuery = true;
        }
      } catch (err) {
        if (err.error) {
          this.logError(err.error, false, fullScript);
          throw err.error;
        }
      }
    } else if (sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_SQL_INLINE_VALIDATION")) {
      const editorSetting = this.component.getEditorSettingData();
      let isNewEditor = this.component.isNewEditor();
      const isNewVersion =
        editorSetting &&
        Object.keys(editorSetting).length > 0 &&
        editorSetting[Object.keys(editorSetting)[0]]?.editor?.isNew;
      if (isNewVersion) {
        isNewEditor = true;
      }
      this.innerAceEditor.session.clearAnnotations();
      this.clearDecorations();
      const oldMarkers = Object.keys(this.innerAceEditor.session.getMarkers(true));
      for (const oldMarker of oldMarkers) {
        this.innerAceEditor.session.removeMarker(oldMarker);
      }
      if (isNewEditor) {
        const doc = this.innerAceEditor.session.getDocument();

        const values = removeMapping(this.innerAceEditor.getValue(), true);
        const processedSql = values.processedSql ? values.processedSql : this.innerAceEditor.getValue();
        try {
          await parseSql(processedSql, false);
        } catch (err) {
          if (err) {
            if (Array.isArray(err)) {
              await this.logErrors(err, true, false, doc, false);
            } else if (err.error) {
              await this.logErrors([err.error], true, false, doc, false);
            }
            if (isValidateSQLCall) {
              return;
            } else {
              throw Array.isArray(err) ? err : [err.error];
            }
          }
        }
      }
    }
    try {
      const input = { sql, options, isSqlScript, isStarColumn, fromPos, toPos, bodyScript };
      const result = await processSQLScript(input);
      sql = result.sql;
      bodyScript = result.bodyScript;
      if (isSqlScript) {
        text = sql;
      } else {
        const paramMatch = sql.match(/\:(\s+)\:/);
        if (paramMatch) {
          sql = sql.replaceAll(paramMatch[0], "::");
        }
        text = sql;
        const paramMatchAfterFormat = text.match(/\:\:/);
        if (paramMatchAfterFormat) {
          text = text.replaceAll("::", ": :");
        }
      }
    } catch (err) {
      // In case formatter fails, still proceed with unformatted query
      text = sql;
    }

    const processedSQL = replaceSemiColon(text);
    sql = processedSQL ? processedSQL : sql;
    if (!isDataValidationTriggered) {
      if (isSqlScript) {
        this.setValue(bodyScript, false);
      } else {
        this.setValue(sql, false);
      }
    }
    const editorSetting = this.component.getEditorSettingData();
    let isNewEditor = this.component.isNewEditor();
    const isNewVersion =
      editorSetting &&
      Object.keys(editorSetting).length > 0 &&
      editorSetting[Object.keys(editorSetting)[0]]?.editor?.isNew;
    if (isNewVersion) {
      isNewEditor = true;
    }
    if (
      sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_SQL_INLINE_VALIDATION") &&
      !isSqlScript &&
      isNewEditor
    ) {
      try {
        await this.semanticValidationCheck(options, true);
      } catch (err) {
        if (isValidateSQLCall) {
          return;
        } else {
          throw Array.isArray(err) ? err : [err.error];
        }
      }
    }

    let updatedQuery;
    if (
      isDeploy &&
      sql &&
      this.parseSQLValue?.SQL &&
      sql &&
      sql === this.parseSQLValue?.SQL &&
      this.parseSQLValue?.parseSQL
    ) {
      updatedQuery = this.parseSQLValue?.parseSQL && JSON.parse(JSON.stringify(this.parseSQLValue.parseSQL));
    } else {
      // Generate CSN for query
      if (!isDataValidationTriggered) {
        updatedQuery = sql && sql !== "" ? await parseSqlToCqn(sql) : "";
      } else {
        if (!query) {
          updatedQuery = sql && sql !== "" ? await parseSqlToCqn(sql) : "";
        } else {
          updatedQuery = query;
        }
      }
      this.parseSQLValue = { parseSQL: JSON.parse(JSON.stringify(updatedQuery)), SQL: sql };
    }

    updateSubQueryAliases(updatedQuery);
    updateCsnQueryAliases(updatedQuery);

    const oCsnDocument = createCSNDocument(entityName, updatedQuery, sql);

    const oCSnDef = oCsnDocument && oCsnDocument.definitions;
    let tables = [];
    let dependentTables = [];
    if (!isSqlScript) {
      try {
        const values = removeMapping(sql);
        if (values && values.processedSql) {
          sql = values.processedSql;
        }
        if (
          isDeploy &&
          sql &&
          this.dependentValues?.SQL &&
          sql &&
          sql === this.dependentValues?.SQL &&
          this.dependentValues?.dependentObjects
        ) {
          tables = this.dependentValues?.dependentObjects;
        } else {
          // Added in try/catch due to errors from sqlparser-js
          // TODO: Extract dependent objects from repository api
          tables = await getDependentObjects(sql, true);
          this.dependentValues = { dependentObjects: tables, SQL: sql };
        }

        if (values && values.paramMappingName && values.viewNames && values.processedSql) {
          let count = 1;
          for (const viewName of values.viewNames) {
            values.processedSql = values.processedSql.replace(viewName, `{${count++}}`);
          }
          count = 1;
          for (const wholeObjectNameMatch of values.wholeObjectNameMatches) {
            values.processedSql = values.processedSql.replace(`{${count++}}`, wholeObjectNameMatch);
          }
          sql = values.processedSql;
        }
      } catch (err) {
        tables = [];
      }
    } else {
      try {
        let fullScript;
        if (isComplexQuery) {
          fullScript = prepareSQLScript(bodyScript);
        } else {
          fullScript = sql;
        }
        if (fullScript?.trim() && isSQLDerived) {
          if (
            fullScript &&
            this.dependentValues?.SQL &&
            fullScript &&
            fullScript === this.dependentValues?.SQL &&
            this.dependentValues?.dependentObjects
          ) {
            tables = this.dependentValues?.dependentObjects;
          } else {
            tables = await getDependentObjects(fullScript, true);
            this.dependentValues = { dependentObjects: tables, SQL: fullScript };
          }
        }
      } catch (err) {
        tables = [];
      }
    }
    const aSharedObjects = [];
    const aLocalObjects = [];
    options.service = this.getView().data("previewService");

    const keys = Object.keys(oCsnDocument.definitions);
    dependentTables = getDependencyExtractorHelper(oCsnDocument.definitions[keys[0]]);
    for (const table of dependentTables) {
      let oSharedObj;
      const tableName = table;
      const galileiModel = await this.component.getGalileiModel();
      if (tableName.includes(".")) {
        oSharedObj = repositoryObjects.find((obj) => obj.name === tableName);
        if (!oSharedObj) {
          oSharedObj = crossSpaceObjects.find((obj) => obj.name === tableName);
          if (!oSharedObj) {
            oSharedObj = await loadCrossSpaceEntity(tableName, options);
            await createContextObject(tableName, options.model, oSharedObj);
          }
          if (oSharedObj) {
            aSharedObjects.push(oSharedObj);
            if (galileiModel) {
              galileiModel.isCrossSpace = true;
            }
          }
        } else if (typeof oSharedObj === "object") {
          if (!oSharedObj.csn) {
            const csnDefinitions = await Repo.getModelDetails(oSharedObj.spaceName, oSharedObj.name, ["csn"]);
            oSharedObj.csn = csnDefinitions.csn;
          }
          aLocalObjects.push(oSharedObj);
          if (galileiModel) {
            galileiModel.isCrossSpace = false;
          }
        }
      } else {
        if (galileiModel) {
          galileiModel.isCrossSpace = false;
        }
      }
      if (!oSharedObj) {
        const oLocalObj = repositoryObjects.find((obj) => obj.name === tableName);
        if (oLocalObj) {
          if (!oLocalObj.csn) {
            const csnDefinitions = await Repo.getModelDetails(oLocalObj.spaceName, oLocalObj.name, ["csn"]);
            oLocalObj.csn = csnDefinitions.csn;
          }
          aLocalObjects.push(oLocalObj);
          if (galileiModel) {
            galileiModel.isCrossSpace = false;
          }
        }
      }
    }

    aSharedObjects.forEach((obj) => removeCrossSpaceReferencesInCSN(obj.csn.definitions, obj.name));
    aSharedObjects.forEach((obj) => {
      if (
        this.oFeatures.DWCO_SQL_VIEW_UI_ENHANCEMENTS &&
        (Number(obj["#objectStatus"]) === ObjectStatus.designTimeError ||
          Number(obj["#objectStatus"]) === ObjectStatus.runTimeError)
      ) {
        this.runTimeErrorViews.push(obj.name);
      }
      const entities = obj && obj.csn && obj.csn.definitions;
      for (const entity in entities) {
        if (!oCSnDef[entity] && entities.hasOwnProperty(entity)) {
          oCSnDef[entity] = entities[entity];
        }
      }
    });

    // Generate CSN for contexts
    if (options.model) {
      const oViewModelToCsn = sap.cdw.querybuilder.ViewModelToCsn.getInstance();
      oViewModelToCsn.addContexts(oCsnDocument.definitions, options.model.allContexts);
    }

    // Add local tables/views when data preview
    if (options.dataPreview && aLocalObjects.length > 0) {
      aLocalObjects.forEach((obj) => {
        if (
          this.oFeatures.DWCO_SQL_VIEW_UI_ENHANCEMENTS &&
          (Number(obj["#objectStatus"]) === ObjectStatus.designTimeError ||
            Number(obj["#objectStatus"]) === ObjectStatus.runTimeError)
        ) {
          this.runTimeErrorViews.push(obj.name);
        }
        const oCsnEntityClone = obj && obj.csn && obj.csn.definitions && cloneJson(obj.csn.definitions[obj.name]);
        handleCsnForDataPreview(oCsnEntityClone);
        oCSnDef[obj.name] = oCsnEntityClone;
        Object.keys(obj.csn.definitions).forEach((key) => {
          const object = obj.csn.definitions[key];
          if (object.kind === "context") {
            oCSnDef[key] = object;
          }
        });
      });
    }
    return oCsnDocument;
  }

  public async semanticValidationCheck(options: any, isSkipError = false) {
    const { repositoryObjects, crossSpaceObjects } = await this.getRepositoryObjects();
    options.repositoryObjects = repositoryObjects;
    options.crossSpaceObjects = crossSpaceObjects;
    const doc = this.innerAceEditor.session.getDocument();
    this.innerAceEditor.session.clearAnnotations();
    this.clearDecorations();
    const oldMarkers = Object.keys(this.innerAceEditor.session.getMarkers(true));
    for (const oldMarker of oldMarkers) {
      this.innerAceEditor.session.removeMarker(oldMarker);
    }
    const values = removeMapping(this.innerAceEditor.getValue(), true);
    const processedSql = values.processedSql ? values.processedSql : this.innerAceEditor.getValue();
    try {
      await getParsedResultNew(processedSql, true, options);
    } catch (err) {
      let errorArray = [];
      if (err) {
        if (Array.isArray(err)) {
          errorArray = [...err];
        } else if (err.error) {
          errorArray = [err.error];
        }
        errorArray = errorArray.filter((err: any) => {
          const errorCode = err?.code;
          if (errorCode?.includes("unresolved")) {
            return true;
          }
          return false;
        });
        if (!isSkipError) {
          errorArray = errorArray.map((err: any) => {
            const errorCode = err?.code;
            if (errorCode === "unresolved_table_0") {
              err.code = "UNRESOLVED_TABLE_OR_MISSING_QUOTE";
            } else if (errorCode === "unresolved_col_0") {
              err.code = "UNRESOLVED_COLUMN_OR_MISSING_QUOTE";
            }
            return err;
          });
        }
        if (errorArray.length > 0) {
          await this.logErrors(errorArray, true, false, doc, !isSkipError);
        }
      }
      if (isSkipError && errorArray.length > 0) {
        throw err;
      }
    }
  }
  public async getCsnStatus() {
    try {
      await this.getSqlAsCsn();
    } catch (err) {
      return this.getText("txtSQLFileValidationError");
    }
  }

  public async getCsnAsSql(oCsn, modelId) {
    const csnEntity = oCsn && oCsn.definitions && oCsn.definitions[modelId];
    let sqlText: string;

    if (csnEntity) {
      if (csnEntity["@DataWarehouse.sqlEditor.query"]) {
        sqlText = csnEntity["@DataWarehouse.sqlEditor.query"];
      } else if (csnEntity["@DataWarehouse.tableFunction.script"]) {
        sqlText = csnEntity["@DataWarehouse.tableFunction.script"];
      }
      if (!sqlText && csnEntity.query) {
        const isTFViewWithParmetersSupported = this.getEditorConfiguration()?.viewWithParametersSupported;
        sqlText = await buildSqlFromSelectCqn(oCsn, modelId, isTFViewWithParmetersSupported);
      }
    }
    return sqlText || "";
  }

  public setValue(value: string, isInitialValue: boolean) {
    this.isInitialValue = isInitialValue;
    if (this.codeEditor) {
      this.codeEditor.setValue(value);
    }
    if (this.innerAceEditor != null) {
      this.innerAceEditor.setValue(value, 0);
      this.innerAceEditor.clearSelection();
    }
    (this?.getView()?.getModel("sqlEditorModel") as sap.ui.model.json.JSONModel)?.setProperty("/currentValue", value);
  }

  public getValue() {
    return this.innerAceEditor.getValue();
  }

  public setCursorPosition(lineNumber: number, column: number) {
    if (this.innerAceEditor == null) {
      return;
    }
    this.innerAceEditor.moveCursorToPosition({
      row: lineNumber,
      column: column,
    });
  }

  public focus() {
    if (this.innerAceEditor == null) {
      return;
    }
    this.innerAceEditor.focus();
  }

  public clearDecorations() {
    if (this.codeEditor == null) {
      return;
    }
    this.innerAceEditor.session.setOption("useWorker", false);
    if (this.innerAceEditor.session.getAnnotations().length > 0) {
      this.innerAceEditor.session.clearAnnotations();
    }
  }
  /**
   * SQL format event
   * @param event
   */
  public async formatSql(event) {
    this.workbench.workbenchModel.setProperty("/detailsPage/problems", []);
    if (this.oFeatures.DWCO_MODELING_BROWSER_LAZY) {
      this.notShowPopup = true;
    }
    try {
      let sql = this.innerAceEditor.getValue();
      const paramMatch = sql.match(/\:(\s+)\:/);
      if (paramMatch) {
        sql = sql.replaceAll(paramMatch[0], "::");
      }
      const paramWithConst = sql.match(/\:(\s+)\'/);
      if (paramWithConst) {
        const paramMap = sql.match(/\:(\s+)/);
        if (paramMap) {
          sql = sql.replaceAll(paramMap[0], "=>");
        }
      }
      const result = await formatSql(sql, true);
      if (result?.error) {
        this.logError(result.error, true, undefined);
        return;
      } else {
        sql = result;
      }
      const paramMatchAfterFormat = sql.match(/\:\:/);
      if (paramMatchAfterFormat) {
        sql = sql.replaceAll("::", ": :");
      }
      if (paramWithConst) {
        sql = sql.replaceAll("=>", ": ");
      }
      this.innerAceEditor.setValue(sql);
      this.innerAceEditor.clearSelection();
    } catch (err) {
      this.logError(err, true, undefined);
    }
  }

  public async logErrors(errors, isSQL, fullScript?, doc?, isWarning = false) {
    const decoratorMessages = [];
    const problems = [];
    for (const err of errors) {
      const range = this.innerAceEditor.getSelection().getRange();
      const fromPos = doc.indexToPosition(err.fromPos);
      const toPos = doc.indexToPosition(err.toPos);
      range.start.column = fromPos.column;
      range.start.row = fromPos.row;
      range.end.column = toPos.column;
      range.end.row = toPos.row;
      if (err.type === "Error") {
        err.type = "SQLParserError";
      }
      if (isWarning) {
        this.innerAceEditor.session.addMarker(range, "acmark_error errorType_warning", "background", true);
      } else {
        this.innerAceEditor.session.addMarker(range, "acmark_error errorType_error", "background", true);
      }

      let response;
      if (err && err.responseJSON && err.responseJSON.details) {
        response = err.responseJSON.details;
      } else if (err.code) {
        response = err;
      }
      if (response) {
        const errMessage = range?.start?.row
          ? `${this.localizeText(response.code, response.params)} [Line ${range.start.row + 1}]`
          : this.localizeText(response.code, response.params);
        problems.push({
          type: response.type === "SQLParserError" ? (isWarning ? "Warning" : "Error") : response.type,
          message: errMessage,
          location: this.workbench.getText("locationErrorSQL", [range.start.row, range.end.row - range.start.row]),
          isSQLParser: true,
        });
        decoratorMessages.push({
          row: range.start.row,
          column: 0,
          text: errMessage,
          type: isWarning ? "warning" : "error",
        });
      }
    }
    this.clearDecorations();
    this.innerAceEditor.getSession().setAnnotations(decoratorMessages);
    const oModel = await this.component.getGalileiModel();
    if (decoratorMessages.length > 0) {
      this.workbench.workbenchModel.setProperty("/detailsPage/problems", []);
      oModel.clearValidation();
      await oModel.output.validate();
      const validationList = oModel.aggregatedValidations;
      for (const decoratorMessage of decoratorMessages) {
        const isExist =
          Array.isArray(validationList?.validations) &&
          validationList?.validations?.some((obj) => obj?.message?.id === decoratorMessage?.text);
        const nsValidationStatus = sap.cdw.commonmodel.ValidationStatus;
        if (!isExist) {
          if (isWarning) {
            nsValidationStatus.createWarnInstance(
              oModel.output,
              ValidationMessageGroup.SQLERROR,
              decoratorMessage.text,
              []
            );
          } else {
            nsValidationStatus.createErrorInstance(
              oModel.output,
              ValidationMessageGroup.SQLERROR,
              decoratorMessage.text,
              []
            );
          }
        }
      }
    }
    this.workbench.workbenchModel.setProperty("/panels/bottom/visible", true);
    this.workbench.workbenchModel.setProperty("/detailsPage/problems", problems);
    this.workbench.showPreviewError(undefined, undefined, undefined, problems);
  }
  public async logError(err, isSQL, fullScript?) {
    let response;
    if (err && err.responseJSON && err.responseJSON.details) {
      response = err.responseJSON.details;
    } else if (err.code) {
      response = err;
    }
    if (response) {
      const errMessage = this.localizeText(response.code, response.params);
      const innerValue = this.innerAceEditor.getValue();
      const problems = [];
      if (!fullScript) {
        fullScript = innerValue;
      }
      const errorResult = getErrorDetails(fullScript, response, innerValue, isSQL);
      problems.push({
        type: response.type,
        message: errMessage,
        location: this.workbench.getText("locationErrorSQL", [
          errorResult.lineCount,
          errorResult.toPos - errorResult.fromPos,
        ]),
      });
      const featureFlags = (sap.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel).getData();
      if (featureFlags.DWCO_SAVE_WITH_ERROR_SQL_VIEW) {
        const nsValidationStatus = sap.cdw.commonmodel.ValidationStatus;
        const oModel = await this.component.getGalileiModel();
        const problemList = this.workbench?.workbenchModel?.getProperty("/detailsPage/problems");
        if (response.type) {
          oModel.clearValidation();
          oModel.output.validate();
          if (problemList?.length <= 0) {
            if (response.type === "Warning" && oModel?.output?.dbViewType !== DBViewType.TABLEFUNCTION && errMessage) {
              nsValidationStatus.createWarnInstance(oModel.output, ValidationMessageGroup.SQLERROR, errMessage, []);
            } else if (response.type === "Error" && errMessage) {
              const aError = errMessage?.match(/Could not find table\/view\s+(.+)\s+in schema/);
              if (!aError) {
                nsValidationStatus.createErrorInstance(oModel.output, ValidationMessageGroup.SQLERROR, errMessage, []);
              }
            }
          }
        }
      }
      this.clearDecorations();
      this.addDecoration(
        { line: errorResult.lineCount, column: 0 },
        { line: errorResult.lineCount, column: errorResult.toPos - errorResult.fromPos },
        {
          inlineClassName: "sqlErrorDecoration",
          linesDecorationsClassName: "sqlErrorLineDecoration",
          hoverMessage: {
            value: errMessage,
          },
        }
      );
      const datapreviewControls = this.workbench.getPreviewDataUIControls();
      this.workbench.workbenchModel.setProperty("/panels/bottom/visible", true);
      this.workbench.showPreviewError(undefined, undefined, undefined, problems);
    }
  }
  public addDecoration(start: { line: number; column: number }, end: { line: number; column: number }, options: any) {
    this.innerAceEditor.getSession().setAnnotations([
      {
        row: start.line - 1,
        column: 0,
        text: options.hoverMessage.value,
        type: "error",
      },
    ]);
  }

  public openChangeManagementDialog() {
    // versioning readOnly page should not show changeMgmt dialog
    if (!getIsVersioningReadOnlyMode()) {
      // show change management pop-up
      if (this.getView().getModel("galileiModel")?.getProperty("/changeManagement")) {
        this.openChangeManagementInfoDialog(
          "",
          "",
          this.getView().getModel("galileiModel").getProperty("/changeManagement")
        );
      }
    }
  }

  public async openChangeManagementInfoDialog(channel, event, data) {
    if (data.pendingCounters && data.pendingCounters.DAC > 0) {
      setTimeout(() => {
        this.openChangeManagementInfoDialog(channel, event, data);
      }, 100);
      return;
    }
    const model = data?.modifiedObjects?.length > 0 && data?.modifiedObjects[0].modifiedObject?.rootContainer;
    if (!model) {
      return;
    }
    if (this.getView().getVisible()) {
      // user already navigated elsewhere but SQLEditor change management call happening
      // eslint-disable-next-line dot-notation
      if (data.modifiedObjects[0].modifiedObject.rootContainer.name === this.getOwnerComponent()["modelId"]) {
        if (!this.changeManagementDialogOpeningOrOpen) {
          this.changeManagementDialogOpeningOrOpen = true;
          let sourceExists = false;
          data.isSQLEditor = true;
          const newData = { modifiedObjects: [] };
          for (const modifiedObjectItem of data.modifiedObjects) {
            for (const autofixedObjectItem of modifiedObjectItem.autofixedObjects) {
              if (autofixedObjectItem.sources) {
                sourceExists = true;
                for (const autofixedObjectItemSource of autofixedObjectItem.sources) {
                  const obj = {
                    modifiedObject: cloneJson(autofixedObjectItemSource.csnObject),
                  };
                  obj.modifiedObject.modificationDate = autofixedObjectItemSource.repositoryCSN?.modification_date;
                  obj.modifiedObject.displayName = autofixedObjectItemSource.csnObject.name;
                  obj.modifiedObject.isCrossSpace = autofixedObjectItemSource.repositoryCSN?.isCrossSpace;
                  obj.modifiedObject.repositoryCSN = cloneJson(autofixedObjectItemSource.repositoryCSN);
                  obj.modifiedObject.qualifiedClassName = "sap.cdw.querybuilder.SQLOutput";
                  obj.modifiedObject.modifier =
                    autofixedObjectItemSource.repositoryCSN?.modifier ||
                    autofixedObjectItemSource.repositoryCSN?.creator;
                  newData.modifiedObjects.push(obj);
                }
              }
            }
          }
          if (sourceExists) {
            const historicId = "sap-cdw-components-sqleditor-view"; // keep id stable, see descriptor.ts, CHANGE_MANAGEMENT_DIALOG
            const dialogId = historicId + "-ChangeManagementInfo--dialog";
            const oOptions: sap.cdw.components.reuse.control.changeManagement.IChangeManagementInfoDialogSettings = {
              title: this.getText("changeManagementTitle"),
              type: sap.m.DialogType.Message,
              contentWidth: "65%",
              contentHeight: "40%",
              changesText: this.getText("changesText"),
              reviewText: this.getText("reviewText"),
              objectQualifiedClassNames: ["sap.cdw.querybuilder.Entity"],
              objectQualifiedClassNameFallbacks: ["sap.cdw.querybuilder.SQLOutput"],
              changeManagement: newData,
              afterClose: () => {
                this.changeManagementDialogOpeningOrOpen = false;
                dialog.destroy();
              },
            };
            const dialog = new ChangeManagementInfoDialog(dialogId, oOptions);
            await dialog.transformChangeManagementData(); // transform chg.mgt. data and retrieve object icon, ..., modifier displayName from search index
            this.getView().addDependent(dialog);
            dialog.open();
          } else {
            this.changeManagementDialogOpeningOrOpen = false;
          }
        }
      }
    }
  }

  public validateColumns(oQuery: any): any {
    const getColumnAlias = (oSelect) => {
      const aColumns = oSelect.columns;
      if (aColumns && aColumns.length) {
        const aColumnAlias = [];
        let duplAliasExists = false;
        aColumns.forEach((column) => {
          if (column.as) {
            if (aColumnAlias.includes(column.as)) {
              duplAliasExists = true;
              return;
            }
            aColumnAlias.push(column.as);
          }
        });
        if (duplAliasExists) {
          return this.getText("uniqueAlias");
        }
      }
    };
    const oCsnSelect = oQuery.SELECT;
    if (oCsnSelect) {
      return getColumnAlias(oCsnSelect);
    } else {
      // SQL query with union
      const oCsnSet = oQuery.SET;
      const aArgs = (oCsnSet && oCsnSet.args) || [];
      for (let index = 0; index < aArgs.length; index++) {
        const msg = getColumnAlias(aArgs[index] && aArgs[index].SELECT);
        if (msg) {
          return msg;
        }
      }
    }
  }
  public isValidateBtnEnabled(isValidateBtnEnabled, hanaState: State = State.Green) {
    if (hanaState !== State.Green && (hanaState !== State.Yellow || !isCircuitBreakerYellowStateEnabled())) {
      return false;
    }
    return isValidateBtnEnabled;
  }
  public getSQLComponent() {
    return this.component;
  }

  public isFormatBtnEnabled(dbViewType: DBViewType = DBViewType.SQL, hanaState: State = State.Green) {
    if (hanaState !== State.Green && (hanaState !== State.Yellow || !isCircuitBreakerYellowStateEnabled())) {
      return false;
    }
    return dbViewType === DBViewType.TABLEFUNCTION ? false : true;
  }
  public getRunTimeErrorViews() {
    return this.runTimeErrorViews.filter((item, index) => this.runTimeErrorViews.indexOf(item) === index);
  }
  private handleUiExtensions() {
    const configuration = this.getEditorConfiguration() as ISQLEditorExtensions;
    const toolbar = this.getView().byId("sqlEditorToolbar");

    // title & welcome screen visibility extension point
    const extensionsModel = this.getView().getModel("extensions");
    extensionsModel.setProperty("/titleVisible", !!configuration?.toolbarExtensions?.title);
    extensionsModel.setProperty("/title", configuration?.toolbarExtensions?.title);

    // Toolbar left-side extension point: Remove all extensions (may be added by previous usage of the editor)
    // Toolbar left-side extension point: Remove all extensions (may be added by previous usage of the editor)
    toolbar["getContent"]().forEach((control) => {
      if (control.data("sqlEditorExtension")) {
        // is an extension -> destroy it
        control.destroy();
      }
    });

    const length = configuration?.toolbarExtensions?.leftSide ? configuration?.toolbarExtensions?.leftSide.length : 0;
    for (let i = 0; i < length; i++) {
      const extension = configuration.toolbarExtensions.leftSide[i];
      extension.data("sqlEditorExtension", true, false);
      toolbar["insertContent"](extension, i);
    }
  }
}

export const SQLEditor = smartExtend(
  AbstractController,
  "sap.cdw.components.sqleditor.controller.SQLEditor",
  SQLEditorClass
);

sap.ui.define("sap/cdw/components/sqlEditor/controller/SQLEditor.controller", [], function () {
  return SQLEditor;
});
