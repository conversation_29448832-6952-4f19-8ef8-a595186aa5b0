#~~~~~~~~~~~ Space Selection ~~~~~~~~~~~~~~~~~~~
#XTIT: Title for authorizations
spaceSelectionTitle=Добро дошли у генератор дијаграма
#YMSG: Description for authorizations
spaceSelectionDescription=Креирајте онтологије и пресликавања да бисте креирали дијаграме знања који додају пословну вредност вашим подацима.
#XTXT: Info message for a locked space
spaceLockedInfo=Овај простор је доступан само у начину само за читање јер прекорачује своје ресурсе.
#XMSG: Reading spaces from repository (service) is failing. All landing pages and most other DWC UIs cannot be used
readingSpacesFailed=Простори нису доступни (позив услуге није успео).

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XTIT: Title for authorizations
landingTitle=Добро дошли у генератор дијаграма
#YMSG: Description for authorizations
landingDescription=Креирајте онтологије и пресликавања да бисте креирали дијаграме знања који додају пословну вредност вашим подацима.
#XTOL
allFilesTab=Сви фајлови

# --------------------- Ontology Entries --------------------- #
# Note: we can remove those cm_ prefixes when we have our own component
# I added this to avoid conflicts with other keys

#XTIT: Title for the main page of conceptual model
cm_conceptual_model_editor=Уређивач онтологије
#XTIT: Title for the ontology editor breadcrumbs
cm_conceptual_model_editor_breadcrumbs=Онтологија – {0}
#XTIT: Tab title for the visualization of working tree
cm_working_ontology_tab=Онтологија
#XFLD: Concept label
cm_business_name=Пословни назив
#XFLD: Resource description
cm_description=Опис
#XFLD: Concept URI
cm_uri=URI (технички назив)
#XFLD: Concept URI’s Local Name
cm_uri_local_name=Локални назив URI-ја
#XFLD: Ontology Local Name
cm_local_name=Локални назив
#XFLD: Concept URI’s origin location
cm_uri_location=Локација
#XFLD: Concept Parent classes
cm_parent_concepts=Надређени концепти
#XMSG: Feedback to the user when the search characters are not valid
cm_parent_concepts_type_error=Упс! Изгледа сте унели знак који није дозвољен у тражењу.
#XFLD: Concept Imported Ontologies
cm_imported_ontologies=Увезене онтологије
#XFLD: Concept Inherited Properties
cm_inherited_properties=Наслеђена својства
#XFLD: Concept Properties
cm_properties=Својства
#XFLD: Created On
cm_created_on=Креирано
#XFLD: Modified On
cm_modified_on=Измењено
#XFLD: Number of concepts inside a model
cm_number_of_concepts=Број концепата
#XTOL Toggle the toolbar size
cm_expand_or_collapse_toolbar=Прошири или сажми палету са алаткама
#XBUT Toggle the details of the selected node
cm_see_details=Детаљи
#XGRP Group buttons related to edit the current model
cm_edit=Уреди
#XGRP Group buttons related to saving/deploying the current model
cm_general=Опште
#XGRP Group buttons related to the visualization of the current model
cm_view=Прикажи
#XGRP Group buttons related to the available tools of the current model
cm_tools=Алати
#XBUT Save the model
cm_save=Сачувај
#XBUT Save the model as another thing
cm_save_as=Сачувај као
#XBUT Save the model as another thing
cm_create_copy=Креирај копију
#XBUT Deploy the model
cm_deploy=Имплементирај
#XBUT Toggle button to show/hide properties of the model
cm_show_hide_details=Покажи или сакриј детаље
#TOL Toggle button to show/hide the source tree of the model
cm_show_hide_tree=Покажи или сакриј панел стабла
#TOL Open dialog to show the code visualization
cm_open_source_code=Отворени изворни код
#XTOL Show the data visualization
cm_txt_data_viewer=Прегледач података
#XBUT Display a list of errors / warnings from the validation
cm_validation_status=Поруке валидације
#XTOL Undo last change on model
cm_undo=Поништи
#XTOL Redo last change on model
cm_redo=Понови
#XFLD Tab title for visualization of external source concepts
cm_source_concepts_tab=Изворни концепти
#XFLD Popover menu button to add a new string property
cm_string_property_type=Низ
#XFLD Popover menu button to add a new number property
cm_number_property_type=Цео број
#XFLD Popover menu button to add a new date property
cm_date_property_type=Датум
#XFLD Popover menu button to add a property by different means
cm_other=Друго
#XFLD Popover menu button to add a property that already exists in the working model
cm_existing_property=Постојеће
#XFLD Popover menu button to add a new calculated property
cm_calculated_property=Израчунато својство
#XFLD Popover menu button to add a new entity link so that the two entities are connected
cm_relationship=Однос
#XFLD:Label for a multi combo box to select a target concept for a relationship
cm_target_concept=Циљни концепт
#XMSG:Value state text of a multi combo box that allows the user to select target concepts for a relationship among concepts
cm_multiple_target_concepts_warning=Вишеструки циљни концепти могу бити проблематични. Треба их користити само за подршку напреднијим сценаријима моделирања.
#XMSG:Value state text for a multi-combo box when no target concept is selected
cm_no_target_concept_defined_warning=Без дефинисаног циља однос може да указује на било који концепт. Ако нисте то желели, изаберите циљ.
#XMSG:Value state title for missing target concept warning dialog
cm_missing_target_concept_warning_title=Циљни концепт недостаје
#MSG:Value state text for missing target concept warning dialog
cm_missing_target_concept_warning_text1=Нисте одабрали циљни концепт за овај однос.
#XBTN Missing target concept choose target message box button
cm_choose_target=Изабери циљ
#XBTN Missing target concept proceed without target message box button
cm_proceed_without_target=Настави без циља
#XFLD Popover menu button to remove a entity from the sources
cm_remove_resource=Уклони
#XFLD Popover menu button to remove a reused property from a specific node
cm_remove_resource_from_concept=Уклони из концепта
#XFLD Delete a entity node on the model
cm_delete_resource=Избриши
#XFLD Show the available domains for that entry
cm_property_domain=Домен
#XBUT Create a new class inside the model
cm_create_concept=Креирај концепт
#XBUT Create a new property inside the model
cm_create_property=Креирај својство
#XFLD Information Popover changed on field for source concepts tree items
cm_changed_on=Промењено
#XCOL changed by field for the a table oclumn
cm_changed_by=Променио

#XFLD Information Popover field response for empty properties state in source concepts tree items
cm_none=Ниједно
#XBUT Add button for source concepts tree items
cm_add=Додај
#XBUT Add button for source concepts in Model
cm_add_to_model=Додај онтологији
#XBUT Add button for properties under concepts in Model
cm_add_to_concept=Додај концепту
#XBUT Add button for properties under sub concepts in Model
cm_add_to_subconcept=Додај подређеном концепту
#XBUT Tooltip inherited property label
cm_inherited_property_tooltip=Наслеђено од: {0} / {1}
#XBUT Tooltip for more inherited property label
cm_inherited_more_property_tooltip=Наслеђено од: {0} / {1} и још {2}
#XBUT Show more options generic
cm_more_generic_actions=Више радњи
#XBUT Show more options for models
cm_more_model_actions=Више радњи онтологије
#XBUT Show more options for concepts
cm_more_concept_actions=Више радњи концепта
#XBUT Show more options for sub-concepts
cm_more_subconcept_actions=Више радњи подређеног концепта
#XBUT Show more options for properties
cm_more_property_actions=Више радњи својства
#XBUT Show more options for relationships
cm_more_relationship_actions=Више радњи односа
#XBUT Text on a button for creating a new concept
cm_new_concept=Нови концепт
#XBUT Text on a button for browsing for concepts inside the source concepts tab
cm_browse_concepts=Претражи концепте
#XMIT Menu item for Add button placed in source concepts tree items
cm_as_copy=Као копија
#XMIT Menu item for Add button placed in source concepts tree items
cm_as_base_entity=Као основни ентитет
#XTOL See details of the selected node
cm_see_details_tooltip=Детаљи
#XTOL Full uri on the selected nodes tooltip
cm_full_uri=Потпуни URI


#XMSG message strip warning that search is not possible with only one char
cm_minimum_two_characters_warning=Унесите најмање два знака.
#XMSG message strip warning that search results have reached their maximum amount of node results in the tree

#----------------------------------------------------------------
# TODO: we must have a logic to decide we reached full capacity when we’re searching in the working tree
# in the source concepts tree we will receive this info from the backend but we don’t have anything yet for the working tree
cm_tree_full_search_capacity_warning=Резултати су ограничени на првих 2000 подударања. Можда ћете желети да сузите тражење.
#----------------------------------------------------------------

#XMSG Illustrated message description for no results source concepts search
cm_tree_no_search_result_description=Покушајте да подесите тражење.
#XTIT Illustrated message title for no results in source concepts search
cm_tree_no_search_result_title=Нисмо нашли подударања.
#XMSG Illustrated message description for empty source concepts
empty_space=Још увек не постоје друге онтологије у овом простору.

#XTIT Type of the resource displayed
cm_property=Својство
#XTIT Type of the resource displayed
cm_concept=Концепт
#XTIT Type of the resource displayed
cm_sub_concept=Подређени концепт
#XTIT Type of the resource displayed
cm_model=Онтологија
#XTIT Suffix for downloaded ontoloy filename
cm_export_model_suffix=онтологија

#XMSG Message on a messageToast telling the user they copy the source code
cm_source_code_copied=Копирано у прелазну меморију
#XMSG Message on a messageToast telling the user they copy action succeded
cm_uri_copied=URI копиран
#XTOL Tooltip for copy button of a uri
cm_copy_full_uri=Копирај цео URI

#XMSG message strip warning that subconcept has multiple parent concepts
cm_multiple_parent_concepts_warning=Овај подређени концепт може се појавити више пута у стаблу онтологије јер наслеђује својства од вишеструких концепата.
#XMSG message strip warning to indicate a property is used by more than one class
cm_reused_property_warning=Ово својство користе вишеструки концепти у овој онтологији.
#XMSG message strip warning to indicate that a property is not assigned to a concept - detached property
cm_detached_property_warning=Ово својство тренутно није додељено концепту.
#XMSG Text for a hyperlink to open something with more info
cm_learn_more=Сазнајте више
#XFLD Data types label for concept menu dropdown. There are the standard XSD types from the XML Schema spec.
cm_duration=Трајање
cm_datetime=Датум/време
cm_time=Време
cm_yearmonth=Година/месец
cm_year=Година
cm_monthday=Месец/дан
cm_day=Дан
cm_month=Месец
cm_string=Низ
cm_boolean=Булово
cm_base64binary=Бинарно Base64
cm_hexbinary=Бинарно Hex
cm_float=Покретни зарез
cm_decimal=Децимала
cm_double=Двоструко
cm_anyuri=Било који URI
cm_qname=Q назив
cm_notation=Нотација
cm_anysimpletype=Било који једноставни тип
cm_anytype=Било који тип
cm_normalizedstring=Нормализовани низ
cm_token=Токен
cm_language=Језик
cm_nmtoken=NM токен
cm_nmtokens=NM токени
cm_name=Назив
cm_ncname=NCName
cm_id=ID
cm_idref=IDREF
cm_idrefs=IDREFS
cm_entity=ENTITY
cm_entities=ENTITIES
cm_long=Дуго
cm_int=Int
cm_short=Кратко
cm_byte=Бајт
cm_nonpositiveinteger=Непозитивни цео број
cm_negativeinteger=Негативни цео број
cm_nonnegativeinteger=Ненегативни цео број
cm_positiveinteger=Позитивни цео број
cm_unsignedlong=Непотписано-дуго
cm_unsignedint=Непотписани Int
cm_unsignedshort=Непотписано-кратко
cm_unsignedbyte=Непотписано-бајт
cm_daytimeduration=Трајање дан/време
cm_yearmonthduration=Трајање година/месец
cm_date=Датум
cm_integer=Цео број
cm_password=Лозинка
cm_ontology=Онтологија

#XCOL: Column header for the type of the entity
cm_type=Тип

#XMIT:Menu item to export ontology concept
cm_export_model=Извези онтологију
#XMIT:Menu item to choose ontology download type
cm_download_model_trig=Преузми TRIG
#XMIT:Menu item to choose ontology download type
cm_download_model_jsonld=Преузми JSON-LD
#XMIT:Menu item to refresh ontology concept
cm_refresh_model=Освежи
#XMIT:Menu item to open ontology
cm_open_ontology=Отвори онтологију
#XGRP:Title for the cardinality session in a details/property panel
cm_cardinality=Кардиналност
#XFLD:Label for a switch box to define whether a property is required
cm_editable_required_property=Обавезно својство
#XFLD:Label for a switch box to tell the current non-editable property is set as required
cm_non_editable_required_property_on=Ово је обавезно својство.
#XFLD:Label for a switch box to tell the current non-editable property is not set as required
cm_non_editable_required_property_off=Ово није обавезно својство.
#XMSG:Message inside a popover explaining the meaning of ’is Required’ on a property
cm_required_property_explanation=Инстанца овог концепта мора имати ово својство. Ово је слично пољу које није могуће поништити.
#XFLD:Label for a switch box to define whether a property is multi-valued
cm_editable_multi_value_property=Вишеструко својство
#XFLD:Label for a switch box to tell the current non-editable property is set as multi-value
cm_non_editable_multi_value_property_on=Ово је вишеструко својство.
#XFLD:Label for a switch box to tell the current non-editable property is not set as multi-value
cm_non_editable_multi_value_property_off=Ово није вишеструко својство.
#XMSG:Message inside a popover explaining the meaning of ’is MultiValue’ on a property
cm_multi_value_property_explanation=Инстанца овог концепта може имати више од једне вредности за ово својство.
#XBUT,3:Tiny label inside a switch box when the value is On
cm_switch_yes=Да
#XBUT,3:Tiny label inside a switch box when the value is Off
cm_switch_no=Не
#XTOL:Tooltip for information button that shows details about what is a required property
cm_required_property_see_help_information=Сазнајте више о обавезном својству
#XTOL:Tooltip for information button that shows details about what is a multi-value property
cm_multi_value_property_see_help_information=Сазнајте више о вишеструком својству
#XTOL:Tooltip for switch button for the required property when the value is On
cm_required_tooltip_on=Искључите обавезно својство.
#XTOL:Tooltip for switch button for the required property when the value is Off
cm_required_tooltip_off=Укључите обавезно својство.
#XTOL:Tooltip for switch button for the multi-value property when the value is On
cm_multi_value_tooltip_on=Искључите вишеструко својство.
#XTOL:Tooltip for switch button for the multi-value property when the value is Off
cm_multi_value_tooltip_off=Укључите вишеструко својство.
#XMSG Message on a messageToast telling the user the model refresh succeded
cm_is_up_to_date=ажурирано.

#XTOL A tooltip for ellipsis button in source concepts models
cm_model_actions_tooltip=Радње онтологије

#XFLD Uploading
cm_uploading=Увоз...
#XFLD accepted upload file format
cm_accepted_file_format=Одаберите TTL или TRIG фајл
#XTOL:Tooltip for File uploader
cm_fileUpload=Увези онтологију
#XFLD: Import Model dialog title
cm_import_model_dialog_title=Увези онтологију
#XBUT Cancel import model
cm_import_cancel_btn=Откажи
#XBUT import rdf file
cm_import_btn=Увези
#XMSG: File import success
cm_import_success=Увези Success
#XFLD:Loading
cm_loader_title=Креирање онтологије...
#XMSG: Import model busy text split into 3 parts for styling
cm_loader_text=То може потрајати у зависности од величине онтологије.
#XMSG
cm_import_duplicate_uri_same_space=Ова онтологија већ постоји. Замена ће писати преко постојеће онтологије.
#XMSG
cm_import_duplicate_uri_different_folder=Ова онтологија већ постоји у другом фолдеру. Замена ће писати преко постојеће онтологије и преместити је на ову нову локацију.
#XMSG: No file selected message
cm_no_file_selected=Фајл није одабран
#XMSG: No file selected message
cm_error_file_upload=Грешка при отпремању фајла. Проверите фајл и покушајте поново.
#XMSG: Error message when import file limit exceeded
cm_file_size_limit_exceeded=Фајл се не може пренети на сервер јер прекорачује максималну величину од 50 MB.
#XMSG: Error message in case of not supported file type
cm_file_type_missmatch=Нажалост, тип фајла {0} није подржан. Изаберите један од следећих типова: {1}.
#XBUT Busy upload dialog close button
cm_btnClose=Затвори
#XBUT Busy source code dialog copy button
cm_btnCopy=Копирај
#XBUT Upload dialog replace option
cm_btnReplace=Замени
#XMSG: Invalid import response message
cm_invalid_import_response=Неважећи одговор увоза
#XMSG: Download file failure message
cm_export_fail=Преузимање фајла није успело
#XBUT: save and download action button
cm_save_and_download_action=Сачувај и преузми
#XMSG: Save and Dialog message
cm_save_and_download_text=Онтологију треба сачувати пре извоза.
#XMSG: First part of the message when the user cannot sync the ontology
cm_ontology_needs_sync=Ова онтологија се мора синхронизовати да бисте је отворили.
#XMSG: Second part of the message when the user cannot sync the ontology
cm_ontology_sync_needs_full_access=Да бисте синхронизовали онтологију, неко са овлашћењима за креирање, ажурирање и брисање је мора отворити. Немате потребна овлашћења.
#XMSG: First part of the message when the user does not have privileges to sync the space
cm_space_needs_sync=Овај простор мора да буде синхронизован да би објекти генератора дијаграма били ажурирани.
#XMSG: Second part of the message when the user does not have privileges to sync the space
cm_space_sync_needs_full_access=Да бисте синхронизовали, неко са овлашћењима за креирање, ажурирање и брисање мора да отвори ову страницу или да кликне на дугме Синхронизуј у табели. Немате потребна овлашћења.

#XGRP: Save and Download dialog title
cm_save_and_download_title=Сачувати онтологију?
#XTIT: Upload dialog title
cm_upload_file=Отпреми фајл
#XMSG: Warning message informing user to not close the browser during upload
cm_file_upload_warning=Оставите картицу претраживача отворену и немојте освежавати док се подаци учитавају. Обавестићемо вас када се заврши.

#XTOL:Tooltip for a button to add a source concept to the model
cm_add_concept=Додај концепт онтологији
#XTOL:Tooltip for a button to add a source sub-concept to the model
cm_add_sub_concept=Додај подређени концепт онтологији

#XBUT:Button for a tile to create a new conceptual model
cm_create_new_conceptual_model=Нова онтологија
#XBUT:Text for the technical name of graph builder
cm_custom_technical_name=URI (технички назив)
#XBUT:Text for the technical type of graph builder
cm_custom_technical_type=Тип
#XBUT:Button for confirming the creation of a conceptual model
cm_create=Креирај
#XBUT:Button for canceling the creation of a conceptual model
cm_cancel=Одустани
#XBUT:Button for a tab for the conceptual models
cm_conceptual_models=Онтологије
#XBUT:Text for a menu item to create a new conceptual model
cm_import_conceptual_model=Увези онтологију
#XBUT:Text for a menu item to create a copy of a conceptual model
cm_copy_conceptual_model=Копирај онтологију
#XMSG Message on a messageToast if the user tries to save a model with a URI that already exists. The model will not save in this case.
cm_model_duplicate_uri=Овај URI већ постоји у овом или другом простору.
#XMSG Message on a messageToast if the user tries to save a concept with a URI that already exists. The concept will not save in this case.
cm_concept_duplicate_uri=Овај URI већ постоји у овој онтологији.
#XMSG Message on a messageToast telling the user the model could not be saved. This is a catch all case if we don’t have a more specific reason.
cm_error_saving_model=Дошло је до грешке и није могуће креирати онтологију.
#XMSG Message on a messsageToast if a back-end call fails and we don't know why
cm_error_something_went_wrong=Дошло је до неочекиване грешке.
#XMSG:Text to confirm an entity was deleted on the graph builder landing page
cm_entity_deleted=Одабрана онтологија је избрисана.
#XMSG:Text to confirm a few entities were deleted on the graph builder landing page
cm_entities_deleted=Одабране онтологије су избрисане.
#XMSG Message on a messageToast telling the user the resource deletion succeeded, {0} holds the name of a concept
cm_resource_deleted="{0}" избрисано
#XMSG Message on a messageToast telling the user the resource removal succeeded, {0} holds the name of a concept
cm_resource_removed="{0}" уклоњено
#XMSG Message on a messageToast telling the user the resource removal succeeded, {0} holds the name of a property, {1} holds the name of a concept
cm_reused_property_removed="{0}" уклоњено из "{1}"
#XBTN Delete concept and properties message box button
cm_delete_properties_and_concept=Избриши
#XBTN Delete only concept message box button
cm_delete_concept=Избриши само концепт
#XTIT Unknown resource that does not have label or additional information
cm_unknown_resource=Непознато

#XBTN Delete concept in diagram
cm_diagram_delete_concept=Избриши концепт
#XBTN Remove referenced concept in diagram
cm_diagram_remove_referenced_concept=Уклони референцирани концепт
#XBTN Add sub concept to the target concept in diagram
cm_diagram_add_sub_concept=Додај подређени концепт за грануларну контролу
#XBTN Add sub concept to the target referenced concept in diagram
cm_diagram_add_sub_concept_referenced=Додај подређени концепт за грануларну контролу
#XBTN Add relationship to the target concept in diagram
cm_diagram_add_relationship=Кликните и превуците да додате однос
#XBTN Opens the target referenced concept in a new tab
cm_diagram_open_in_new_tab=Отвори концепт у изворној онтологији у новој картици
#XFLD Label of the diagram edges for a subclass relationship between nodes
cm_subconcept_of=Подређени концепт

#XFLD Show the available ranges for that entry
cm_data_types_range=Тип података
#XTIT Dialog title to reuse properties in current working model, {0} holds the name of a concept
cm_reuse_properties_dialog_title=Додај постојеће својство у {0}
#XMSG Message toast saying any property was selected
cm_any_property_selected=Одабир није извршен, стога својства нису додата.
#XMSG Message toast saying a property was selected, {0} holds the name of the property and {1} holds the name of a concept
cm_property_added_to_concept="{0}" додато у "{1}"
#XMSG Message toast saying properties were selected, {0} holds the name of a concept
cm_properties_added_to_concept=Својства додата у "{0}"
#XFLD placeholder for search in add existing properties dialog
cm_search_placeholder_add_existing_prop=Тражи својства и концепте у тренутној онтологији
#XFLD tooltip for already existing properties, {0} holds the name of an URI
cm_uri_tooltip=URI:\n{0}
#XBTN confirmation button label for existing properties dialog
cm_add_existing_property=Додај
#XMSG message toast to say that concept was deleted, {0} holds the name of a concept
cm_deletion_confirmation="{0}" избрисано
#XMSG message toast to tell about successfull copy
cm_object_was_created=\''{0}\'' је креирано
#XMSG message toast to say that concept and its properties were deleted, {0} holds the name of a concept
cm_concept_and_properties_deleted_confirmation="{0}" и припадајућа својства избрисани
#XMSG warning message toast, {0} holds the name of a concept
cm_properties_question_warning_dialog=Избрисати "{0}" и сва припадајућа својства?
#XMSG message that shows in a message toast to inform the model was saved
cm_entity_saved={0} сачувано.
#XMSG message that indicates there was a problem saving the entity
cm_entity_save_error=Грешка при покушају снимања онтологије {0}
#XMSG message that indicates there were either no or more than one ontologies present
cm_unexpected_ont_count_error=Грешка при покушају снимања онтологије. Очекивала се једна онтологија, а није нађена ниједна или је нађено више од једне.
#XMSG message that indicates the system could not parse the data in the graph file
cm_could_not_parse_graph=Није могуће синтаксички анализирати дијаграм.
#XMSG message that indicates the named graph is different than the ontology uri
cm_named_graph_different_model=URI онтологије се разликује од именованог графикона. Ови URI-ји морају се подударати.
#XMSG message saying any property was found in search for existing properties dialog
cm_no_properties_found_search=Својства нису нађена
#XMSG  message to delete reused properties in current working model, {0} holds the name of a property
cm_delete_reused_properties_dialog_text="{0}" користе вишеструки концепти у овој онтологији:
#XTIT Dialog title to delete reused properties
cm_delete_reused_properties_dialog_title=Избрисати својство за све концепте?
#XBUT delete reused properties button
cm_delete_properties=Избриши
#XBUT cancel deletion of reused properties button
cm_cancel_delete_properties=Одустани
#XTIT title of busy dialog for syncing operation
cm_sync_space_dialog_title=Синхронизација података
#XTXT text of busy dialog for syncing operation
cm_sync_space_dialog_text=Ово може потрајати највише минут...
#XTOL tooltip for sync button
cm_sync_space_button_text=Синхронизуј
#XMSG message toast to indicate completion of syncing operation
cm_sync_operation_confirmation_message=Подаци су ажурирани
#XMSG message toast to delete reused properties message confirmation, where {0} is the name of a property and {1} is a number of concepts
cm_confirm_deletion_of_existing_properties="{0}" је избрисано за {1} концепата.
#XTOL:tooltip for the diagram toolbar zoom to fit button
cm_zoom_to_fit=Увећај до уклапања
#XTOL:tooltip for the digram toolbar auto layout button
cm_auto_layout=Аутоматски изглед
#XMSG:diagram empty state title, shows up when the editor is in its initial state
cm_welcome_panel_no_data_title=Изгледа да још немате концепте.
#YMSG:diagram empty state message, shows up when the editor is in its initial state
cm_welcome_panel_no_data_text=Додајте или креирајте концепте да започнете креирање онтологије.
#XMSG:Message on a messageToast telling the user the Browse Source Concept is in the left panel.
cm_browse_concepts_message=Претражујте изворне концепте у левом панелу.
#XMSG warning message title to inform that concept is already added
cm_concept_already_added_warning_title=Концепт је већ додат
#XMSG warning message to inform that concept is already added
cm_concept_already_added_warning_message="{0}" је већ додато онтологији.
#XMSG message to say search brought no result
cm_no_search_result=Нема резултата
#XTIT target concepts dialog title
cm_source_code_dialog_title=Прикажи изворни TriG
#XTIT target concepts dialog title
cm_target_concepts_dialog_title=Одаберите циљни концепт
#XTIT parent concepts dialog title
parent_concept_dialog_title=Одабери надређени концепт
#XMSG message to say uri is empty
cm_uri_empty=Унесите URI.
#XMSG message to say uri not a string
cm_not_valid_uri=Ово је неважећи формат URI-ја.
#XMSG message to say uri has illegal characters
cm_uri_illegal_characters=Овај URI садржи забрањене знакове.
#XMSG message to say uri has invalid hex escape sequence
cm_uri_invalid_escape_sequence=Овај URI садржи неважећи hex escape редослед.
#XMSG message to say uri has invalid scheme part
cm_uri_invalid_scheme=Укључите шему URI-ја (нпр. http://).
#XMSG message to say duplicate uri
cm_duplicate_uri=Овај URI већ постоји у овом или другом простору.
#XTIT title to use on dialog when importing ontology
cm_duplicate_ontology_import=Креирај дупликат URI-ја
#XMSG message to say validating uri
cm_validating_uri=Валидација...
#XMSG URI update success message
cm_uri_update=URI је ажуриран
#XTIT Edit Uri dialog title
cm_edit_uri_title=Уреди URI (технички назив)
#XTOL:Tooltip for edit uri button
cm_edit_uri_tooltip=Уреди потпуни URI
#XTOL:Tooltip for edit uri button unsaved model
cm_edit_unsaved_ontology_uri_tooltip=URI онтологије није могуће уредити без претходног снимања онтологије.
#XTIT table item showing current working ontology available parent concepts, where {0} holds the name of a working ontology
cm_working_model_parent_concept={0} (тренутно)
#XMSG message toast saying a new parent concept was choosen, where {0} holds the name of the parent concept
cm_new_parent_concept_message="{0}" је додат као надређени концепт.
#XMSG parent concepts multi input placeholder
cm_parent_concept_multi_input_placeholder=Додај концепте за наслеђивање својстава
#XMSG parent concepts dialog search placeholder
parent_concept_dialog_search_placeholder=Тражи интерне и екстерне концепте
#XTIT Type of the range property list
cm_range_properties_title_featured=Опште
#XTIT Type of the range property list
cm_range_properties_title_others=Све друго
#XMSG value state warning for property with any selected data types
cm_any_data_type_selected_warning=Одаберите тип података.
#XMSG value state warning for property with multiple selected data types
cm_multiple_data_types_value_state_warning=Вишеструки типови података могу бити проблематични у уобичајеним ситуацијама. Треба их користити само за подршку напреднијим сценаријима моделирања.
#XTIT data types dialog title
cm_data_type_dialog_title=Одаберите тип података
#XMSG message when an ontology does not have concepts
cm_empty_ontology=Нема концепата
#XMSG placeholder text when there is no local name in a URI
cm_no_local_name=Нема локалног назива
#XTOL: Enter the fullscreen mode for the diagram
enter_full_screen=Уђи у цео екран
#XTOL: Exit the fullscreen mode for the diagram
exit_full_screen=Изађи из целог екрана
#XMSG message to say that an entity was added from another ontology
added_from_another_ontology=Додато из:
#XMSG message to say that an entity was inherited from a working concept
inherited_from_a_working_concept=Наслеђено од:
#XTOL tooltip for opening concept in source ontology
source_concept_tag_tooltip=Отворени концепт у изворној онтологији
#XTOL tooltip for opening property in source ontology
source_property_tag_tooltip=Отворено својство у изворној онтологији
#XTOL tooltip for opening property in source concept of the working ontology
source_property_origin_concept_tag_tooltip=Иди на концепт
#XMSG missing ontology warning title
missing_ontology_warning_title=Недостаје онтологија
#XMSG missing ontology warning description
missing_ontology_warning_description=URI ове онтологије је референциран, али не постоји у овом простору. Неки елементи ваше онтологије можда недостају.
#XTOL button tooltip saying to read the warning
imported_ontology_warning_tooltip=Прочитајте упозорење
#XTOL button tooltip saying to open the source ontology in a new tab
open_imported_ontology_tooltip=Отворена изворна онтологија у новој картици
#XMSG message to inform there isn't imported ontologies in the current open ontology
no_imported_ontology=Ниједно
#XMSG separation colon mark, where {0} holds the name of an ontology and {1} holds the name of a class
separation_colon={0}: {1}
#XMSG no results text indication
no_results=Нема резултата
#XMSG no parent concepts found message
no_parent_concepts_found=Концепти нису нађени
#XMSG separation for only a parent in concepts - 1 parent
separated_parent_concepts_1=({0})
#XMSG separation colon mark for a list of parents in concepts - 2 parents
separated_parent_concepts_2=({0}, {1})
#XMSG separation colon mark for a list of parents in concepts - 2 or more parents
separated_parent_concepts_3=({0}, {1}, ...)
#XFLD placeholder in case an ontology resource is not named
cm_untitled_resource=(Без назива)
#XMSG message to request the user to fill up the ontology label
cm_empty_ontology_label_warning=Именујте онтологију.
#XMSG message to request the user to fill up the concept label
cm_empty_concept_label_warning=Именујте концепт.
#XMSG message to request the user to fill up the property label
cm_empty_property_label_warning=Именујте својство.
#XMSG message to request the user to fill up the relationship label
cm_empty_relationship_label_warning=Именујте однос.
#XMSG message to display backend error when searching for parent classes in non working models
cm_parent_class_search_not_found=Нису нађене одговарајуће класе које садрже текст у другим онтологијама
#XMSG message to show bad request error when searching parent classes in non working ontologies
cm_parent_class_search_bad_request=Погрешан захтев послат за тражење надређене класе
#XMSG message to show forbidden error
cm_entity_forbidden_error=Корисник нема дозволу
#XMSG Message to show no models from backend were returned
cm_no_models_found=Нису нађени модели са URI-јима{0}
#XMSG Message to show bad request errors for save model api call
cm_save_model_bad_request_error=Послат је неисправан захтев за снимање API-ја модела
#XMSG Message to show bad request errors for get frames api call
cm_get_frames_bad_request_error=Послат је неисправан захтев за позивање API-ја оквира
#XMSG Message to show bad request errors for get models api call
cm_get_models_bad_request_error=Послат је неисправан захтев за позивање API-ја модела
#XMSG Message to show bad request errors for search model api call
cm_search_models_request_error=Послат је неисправан захтев за тражење API-ја модела
#XMSG Message to indicate a generic bad request with no further details
cm_bad_request=Неисправан захтев
#XMSG Message to indicate an unexpected error occurred
cm_unexpected_error=Неочекивана грешка
#XMSG Message to indicate one or more of the requested graphs were not found
cm_graph_not_found=Није нађен један захтевани дијаграм или више њих.
#XMSG Message to handle http status bad gateway (502)
cm_bad_gateway_error=Није могуће приступити серверу. Покушајте поново касније.
#XMSG Message to handle http status service unavailable (503)
cm_service_unavailable_error=Није могуће приступити серверу. Покушајте поново касније.
#XMSG Message to handle http status service unavailable (503)
cm_internal_server_error=Дошло је до неочекиваног проблема на серверу. Покушајте поново или у наставку проверите додатне детаље.
#XMSG Error message strip to indicate that the URI does not exist or is broken
cm_missing_ontology_error=Не можемо да нађемо ту онтологију. Можда више не постоји или се њен URI променио.
#XMSG Error message strip to indicate that the ontology object does not exist or the URI for it may have changed
cm_missing_ontology_object_error=Не можемо да нађемо тај објекат у овој онтологији. Можда више не постоји или се URI променио.
#XMSG Title to add detached property to Concept dialog
cm_add_detached_property_to_concept_dialog_title=Додај {0} концепту
#XMSG Placeholder to add detached property to concept dialog search
cm_concepts_in_current_ontology_search_placeholder=Тражи концепте у тренутној онтологији
#XMSG Message toast saying any property was selected
cm_any_concepts_selected=Одабир није извршен, стога концепти нису додати.
#XMSG No concepts found in dialog search message
cm_no_concept_found=Концепти нису нађени
#XMSG Confirmation Message toast for detached property added to Concept
cm_add_detached_property_to_concept_message_toast=''{0}'' је додато ''{1}''
#XMSG Confirmation Message toast for detached property added to multiple Concepts
cm_add_detached_property_to_multiple_concepts_message_toast=''{0}'' је додато концепту ''{1}'' и још {2} њих
#XMSG Error message to indicate that the concept being imported has conflict with working ontology entities
cm_uri_conflict_error_start=Упс! Овај концепт се не може увести због конфликта URI-ја.
#XMSG Error message to add details of concept or the ontology conflicting with working ontology.
cm_concept_uri_conflict_details_error=Концепт {0} се не може увести јер је његов URI ({1}) исти као URI за {2} ове онтологије.
#XMSG Error message to indicate that other entities in the concept are conflicting with the working ontology
cm_concept_entities_conflict_error=Концепт {0} се не може увести јер садржи следеће ентитете чији URI-ји су исти као URI-ји који већ постоје у овој онтологији:
#XMSG Unique constraint violation error message
cm_uri_conflict_error_end=Сви URI-ји у онтологији морају бити јединствени.
#XMSG Messages displayed in the save as comboBox default option
cm_generate_from_business_name_option=Генериши из пословних назива (стандардно)
#XMSG Messages displayed in the save as comboBox preserve custom structures option
cm_preserve_custom_option=Сачувај кориснички дефинисане структуре
#XMSG Messages displayed in the save as comboBox keep existing option
cm_keep_existing_option=Задржи постојеће URI-је
#XMSG message to say URI not a string
cm_ontology_uri_generation_instruction=URI-ји свих објеката у вашој онтологији ће се генерисати из њихових пословних назива.
#XMSG message to provide an example
cm_save_as_messages_example=Пример:
#XMSG message to show a concept local name
cm_ontology_uri_generation_sale=Продаја
#XMSG message to show the concept
cm_ontology_uri_generation_concept=концепт
#XMSG message to describe the preservation of custom structure in object URIs
object_uri_preservation_instruction=Систем ће проверити URI-је објекта у вашој онтологији и покушати да сачува све кориснички дефинисане структуре након префикса URI -ја.
#XMSG label for describing an old ontology URI
cm_old_ontology_uri=Стари URI онтологије
#XMSG label for describing an old sale concept URI
cm_old_sale_uri=Стари URI концепта продаје
#XMSG label for describing a new sale URI
cm_new_sale_URI=Нови URI продаје
#XMSG label for describing the local name of a sale URI
cm_sale_uri_local_name=GUID
#XMSG message to warn about the preservation of URIs in ontology
cm_uri_preservation_warning=URI-ји свих објеката у вашој онтологији неће се променити. То може проузроковати конфликте, стога користите само у напреднијим сценаријима моделирања.
#XTIT object uris title for save as dialog
cm_object_uris=URI-ји објекта
#XFLD prefix used when a copy of an ontology is created
cm_copy_of=Копија за
#XTIT
package=Фолдер
#XMSG concept label for detached properties in existing properties dialog
cm_no_concept=[Нема концепта]
#XMSG message to indicate that no selection was made in add to concept dialog
cm_no_concepts_selected=Одабир није извршен, стога својства нису додата.
#XMSG tooltip to indicate that the user needs Delete, Update and Create permissions to edit the ontology URI
cm_uri_edit_permissions_tooltip=Дозволе за брисање, ажурирање и креирање су потребне за уређивање URI-ја онтологије.
