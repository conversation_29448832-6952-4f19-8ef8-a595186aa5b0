#~~~~~~~~~~~ Space Selection ~~~~~~~~~~~~~~~~~~~
#XTIT: Title for authorizations
spaceSelectionTitle=Selamat Datang ke Pembina Graf
#YMSG: Description for authorizations
spaceSelectionDescription=Cipta ontologi dan pemetaan untuk membina graf pengetahuan yang menambah nilai perniagaan kepada data anda.
#XTXT: Info message for a locked space
spaceLockedInfo=Ruang ini hanya tersedia dalam mod baca sahaja kerana ia mempunyai sumber yang berlebihan.
#XMSG: Reading spaces from repository (service) is failing. All landing pages and most other DWC UIs cannot be used
readingSpacesFailed=Ruang tidak tersedia (panggilan perkhidmatan tidak berjaya).

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XTIT: Title for authorizations
landingTitle=Selamat Datang ke Pembina Graf
#YMSG: Description for authorizations
landingDescription=Cipta ontologi dan pemetaan untuk membina graf pengetahuan yang menambah nilai perniagaan kepada data anda.
#XTOL
allFilesTab=Semua Fail

# --------------------- Ontology Entries --------------------- #
# Note: we can remove those cm_ prefixes when we have our own component
# I added this to avoid conflicts with other keys

#XTIT: Title for the main page of conceptual model
cm_conceptual_model_editor=Editor Ontologi
#XTIT: Title for the ontology editor breadcrumbs
cm_conceptual_model_editor_breadcrumbs=Ontologi - {0}
#XTIT: Tab title for the visualization of working tree
cm_working_ontology_tab=Ontologi
#XFLD: Concept label
cm_business_name=Nama Perniagaan
#XFLD: Resource description
cm_description=Perihalan
#XFLD: Concept URI
cm_uri=URI (Nama Teknikal)
#XFLD: Concept URI’s Local Name
cm_uri_local_name=Nama Tempatan URI
#XFLD: Ontology Local Name
cm_local_name=Nama Tempatan
#XFLD: Concept URI’s origin location
cm_uri_location=Lokasi
#XFLD: Concept Parent classes
cm_parent_concepts=Konsep Induk
#XMSG: Feedback to the user when the search characters are not valid
cm_parent_concepts_type_error=Maaf! Nampaknya anda perlu memasukkan aksara yang dibenarkan dalam carian.
#XFLD: Concept Imported Ontologies
cm_imported_ontologies=Ontologi Diimport
#XFLD: Concept Inherited Properties
cm_inherited_properties=Sifat Diwarisi
#XFLD: Concept Properties
cm_properties=Ciri
#XFLD: Created On
cm_created_on=Dicipta pada
#XFLD: Modified On
cm_modified_on=Diubah Suai pada
#XFLD: Number of concepts inside a model
cm_number_of_concepts=Bilangan Konsep
#XTOL Toggle the toolbar size
cm_expand_or_collapse_toolbar=Kembangkan atau runtuhkan bar alat
#XBUT Toggle the details of the selected node
cm_see_details=Butiran
#XGRP Group buttons related to edit the current model
cm_edit=Edit
#XGRP Group buttons related to saving/deploying the current model
cm_general=Umum
#XGRP Group buttons related to the visualization of the current model
cm_view=Papar
#XGRP Group buttons related to the available tools of the current model
cm_tools=Alat
#XBUT Save the model
cm_save=Simpan
#XBUT Save the model as another thing
cm_save_as=Simpan Sebagai
#XBUT Save the model as another thing
cm_create_copy=Cipta Salinan
#XBUT Deploy the model
cm_deploy=Atur Duduk
#XBUT Toggle button to show/hide properties of the model
cm_show_hide_details=Tunjuk atau sembunyikan butiran
#TOL Toggle button to show/hide the source tree of the model
cm_show_hide_tree=Tunjuk atau sembunyikan panel pepohon
#TOL Open dialog to show the code visualization
cm_open_source_code=Buka kod sumber
#XTOL Show the data visualization
cm_txt_data_viewer=Pemapar Data
#XBUT Display a list of errors / warnings from the validation
cm_validation_status=Mesej Pengesahan
#XTOL Undo last change on model
cm_undo=Buat Asal
#XTOL Redo last change on model
cm_redo=Buat Semula
#XFLD Tab title for visualization of external source concepts
cm_source_concepts_tab=Konsep Sumber
#XFLD Popover menu button to add a new string property
cm_string_property_type=Rentetan
#XFLD Popover menu button to add a new number property
cm_number_property_type=Integer
#XFLD Popover menu button to add a new date property
cm_date_property_type=Tarikh
#XFLD Popover menu button to add a property by different means
cm_other=Lain-lain
#XFLD Popover menu button to add a property that already exists in the working model
cm_existing_property=Sedia Ada
#XFLD Popover menu button to add a new calculated property
cm_calculated_property=Sifat Dikira
#XFLD Popover menu button to add a new entity link so that the two entities are connected
cm_relationship=Hubungan
#XFLD:Label for a multi combo box to select a target concept for a relationship
cm_target_concept=Konsep Sasaran
#XMSG:Value state text of a multi combo box that allows the user to select target concepts for a relationship among concepts
cm_multiple_target_concepts_warning=Konsep sasaran berbilang boleh menjadi masalah. Anda hanya boleh gunakannya untuk menyokong senario pemodelan lanjutan.
#XMSG:Value state text for a multi-combo box when no target concept is selected
cm_no_target_concept_defined_warning=Tanpa sasaran yang ditakrifkan, hubungan boleh merujuk kepada mana-mana konsep. Jika ini bukan yang anda maksudkan, pilih sasaran.
#XMSG:Value state title for missing target concept warning dialog
cm_missing_target_concept_warning_title=Tiada Konsep Sasaran
#MSG:Value state text for missing target concept warning dialog
cm_missing_target_concept_warning_text1=Anda belum memilih Konsep Sasaran untuk hubungan ini.
#XBTN Missing target concept choose target message box button
cm_choose_target=Pilih Sasaran
#XBTN Missing target concept proceed without target message box button
cm_proceed_without_target=Teruskan Tanpa Sasaran
#XFLD Popover menu button to remove a entity from the sources
cm_remove_resource=Keluarkan
#XFLD Popover menu button to remove a reused property from a specific node
cm_remove_resource_from_concept=Keluarkan daripada Konsep
#XFLD Delete a entity node on the model
cm_delete_resource=Padam
#XFLD Show the available domains for that entry
cm_property_domain=Domain
#XBUT Create a new class inside the model
cm_create_concept=Cipta Konsep
#XBUT Create a new property inside the model
cm_create_property=Cipta Sifat
#XFLD Information Popover changed on field for source concepts tree items
cm_changed_on=Diubah pada
#XCOL changed by field for the a table oclumn
cm_changed_by=Diubah oleh

#XFLD Information Popover field response for empty properties state in source concepts tree items
cm_none=Tiada
#XBUT Add button for source concepts tree items
cm_add=Tambah
#XBUT Add button for source concepts in Model
cm_add_to_model=Tambah pada Ontologi
#XBUT Add button for properties under concepts in Model
cm_add_to_concept=Tambah ke Konsep
#XBUT Add button for properties under sub concepts in Model
cm_add_to_subconcept=Tambah ke Subkonsep
#XBUT Tooltip inherited property label
cm_inherited_property_tooltip=Diwarisi daripada: {0} / {1}
#XBUT Tooltip for more inherited property label
cm_inherited_more_property_tooltip=Diwarisi daripada: {0} / {1} dan {2} lagi
#XBUT Show more options generic
cm_more_generic_actions=Tindakan Lanjut
#XBUT Show more options for models
cm_more_model_actions=Lebih Banyak Tindakan Ontologi
#XBUT Show more options for concepts
cm_more_concept_actions=Lebih Banyak Tindakan Konsep
#XBUT Show more options for sub-concepts
cm_more_subconcept_actions=Lebih Banyak Tindakan Subkonsep
#XBUT Show more options for properties
cm_more_property_actions=Lebih Banyak Tindakan Sifat
#XBUT Show more options for relationships
cm_more_relationship_actions=Lebih Banyak Tindakan Hubungan
#XBUT Text on a button for creating a new concept
cm_new_concept=Konsep Baharu
#XBUT Text on a button for browsing for concepts inside the source concepts tab
cm_browse_concepts=Layari Konsep
#XMIT Menu item for Add button placed in source concepts tree items
cm_as_copy=Sebagai Salinan
#XMIT Menu item for Add button placed in source concepts tree items
cm_as_base_entity=Sebagai Entiti Asas
#XTOL See details of the selected node
cm_see_details_tooltip=Butiran
#XTOL Full uri on the selected nodes tooltip
cm_full_uri=URl Penuh


#XMSG message strip warning that search is not possible with only one char
cm_minimum_two_characters_warning=Masukkan sekurang-kurangnya dua aksara.
#XMSG message strip warning that search results have reached their maximum amount of node results in the tree

#----------------------------------------------------------------
# TODO: we must have a logic to decide we reached full capacity when we’re searching in the working tree
# in the source concepts tree we will receive this info from the backend but we don’t have anything yet for the working tree
cm_tree_full_search_capacity_warning=Hasil terhad kepada 2,000 padanan. Anda perlu kecilkan carian.
#----------------------------------------------------------------

#XMSG Illustrated message description for no results source concepts search
cm_tree_no_search_result_description=Cuba laraskan carian anda?
#XTIT Illustrated message title for no results in source concepts search
cm_tree_no_search_result_title=Kami tidak menemui apa-apa padanan.
#XMSG Illustrated message description for empty source concepts
empty_space=Tiada ontologi lain yang wujud dalam ruang ini.

#XTIT Type of the resource displayed
cm_property=Properti
#XTIT Type of the resource displayed
cm_concept=Konsep
#XTIT Type of the resource displayed
cm_sub_concept=Subkonsep
#XTIT Type of the resource displayed
cm_model=Ontologi
#XTIT Suffix for downloaded ontoloy filename
cm_export_model_suffix=ontologi

#XMSG Message on a messageToast telling the user they copy the source code
cm_source_code_copied=Disalin ke papan klip
#XMSG Message on a messageToast telling the user they copy action succeded
cm_uri_copied=URI Disalin
#XTOL Tooltip for copy button of a uri
cm_copy_full_uri=Salin URI penuh

#XMSG message strip warning that subconcept has multiple parent concepts
cm_multiple_parent_concepts_warning=Subkonsep ini boleh muncul beberapa kali dalam pepohon ontologi kerana ia mewarisi sifat daripada konsep berbilang.
#XMSG message strip warning to indicate a property is used by more than one class
cm_reused_property_warning=Konsep berbilang menggunakan sifat ini dalam ontologi ini.
#XMSG message strip warning to indicate that a property is not assigned to a concept - detached property
cm_detached_property_warning=Sifat ini tidak diumpukkan kepada konsep pada masa ini.
#XMSG Text for a hyperlink to open something with more info
cm_learn_more=Ketahui lebih lanjut
#XFLD Data types label for concept menu dropdown. There are the standard XSD types from the XML Schema spec.
cm_duration=Jangka masa
cm_datetime=Tarikh/Masa
cm_time=Masa
cm_yearmonth=Tahun/Bulan
cm_year=Tahun
cm_monthday=Bulan/Hari
cm_day=Hari
cm_month=Bulan
cm_string=Rentetan
cm_boolean=Boolean
cm_base64binary=Perduaan Base64
cm_hexbinary=Perduaan Heks
cm_float=Apung
cm_decimal=Perpuluhan
cm_double=Berganda
cm_anyuri=Apa-apa URl
cm_qname=QName
cm_notation=Tatatanda
cm_anysimpletype=Apa-apa Jenis Mudah
cm_anytype=Apa-apa Jenis
cm_normalizedstring=Rentetan Ternormal
cm_token=Token
cm_language=Bahasa
cm_nmtoken=NM Token
cm_nmtokens=NM Tokens
cm_name=Nama
cm_ncname=NCName
cm_id=ID
cm_idref=IDREF
cm_idrefs=DREFS
cm_entity=ENTITY
cm_entities=ENTITIES
cm_long=Panjang
cm_int=Int
cm_short=Pendek
cm_byte=Bait
cm_nonpositiveinteger=Integer Bukan Positif
cm_negativeinteger=Integer Negatif
cm_nonnegativeinteger=Integer Bukan Negatif
cm_positiveinteger=Integer Positif
cm_unsignedlong=Panjang Tidak Bertanda
cm_unsignedint=Int Tidak Bertanda
cm_unsignedshort=Pendek Tidak Bertanda
cm_unsignedbyte=Bait Tidak Bertanda
cm_daytimeduration=Jangka Masa Hari/Masa
cm_yearmonthduration=Jangka Masa Tahun/Bulan
cm_date=Tarikh
cm_integer=Integer
cm_password=Kata Laluan
cm_ontology=Ontologi

#XCOL: Column header for the type of the entity
cm_type=Jenis

#XMIT:Menu item to export ontology concept
cm_export_model=Eksport Ontologi
#XMIT:Menu item to choose ontology download type
cm_download_model_trig=Muat Turun TRIG
#XMIT:Menu item to choose ontology download type
cm_download_model_jsonld=Muat Turun JSON-LD
#XMIT:Menu item to refresh ontology concept
cm_refresh_model=Segar Semula
#XMIT:Menu item to open ontology
cm_open_ontology=Buka Ontologi
#XGRP:Title for the cardinality session in a details/property panel
cm_cardinality=Kekardinalan
#XFLD:Label for a switch box to define whether a property is required
cm_editable_required_property=Sifat Diperlukan
#XFLD:Label for a switch box to tell the current non-editable property is set as required
cm_non_editable_required_property_on=Ini merupakan sifat yang diperlukan.
#XFLD:Label for a switch box to tell the current non-editable property is not set as required
cm_non_editable_required_property_off=Ini bukan sifat yang diperlukan.
#XMSG:Message inside a popover explaining the meaning of ’is Required’ on a property
cm_required_property_explanation=Tika konsep ini mesti mempunyai sifat ini. Ini serupa dengan medan tidak boleh sifar.
#XFLD:Label for a switch box to define whether a property is multi-valued
cm_editable_multi_value_property=Sifat berbilang Nilai
#XFLD:Label for a switch box to tell the current non-editable property is set as multi-value
cm_non_editable_multi_value_property_on=Ini merupakan sifat berbilang nilai.
#XFLD:Label for a switch box to tell the current non-editable property is not set as multi-value
cm_non_editable_multi_value_property_off=Ini bukan sifat berbilang nilai.
#XMSG:Message inside a popover explaining the meaning of ’is MultiValue’ on a property
cm_multi_value_property_explanation=Tika konsep ini boleh mempunyai lebih daripada satu nilai untuk sifat ini.
#XBUT,3:Tiny label inside a switch box when the value is On
cm_switch_yes=Ya
#XBUT,3:Tiny label inside a switch box when the value is Off
cm_switch_no=Tak
#XTOL:Tooltip for information button that shows details about what is a required property
cm_required_property_see_help_information=Ketahui lebih lanjut tentang sifat yang diperlukan
#XTOL:Tooltip for information button that shows details about what is a multi-value property
cm_multi_value_property_see_help_information=Ketahui lebih lanjut tentang sifat berbilang nilai
#XTOL:Tooltip for switch button for the required property when the value is On
cm_required_tooltip_on=Nyahaktifkan sifat yang diperlukan.
#XTOL:Tooltip for switch button for the required property when the value is Off
cm_required_tooltip_off=Aktifkan sifat yang diperlukan.
#XTOL:Tooltip for switch button for the multi-value property when the value is On
cm_multi_value_tooltip_on=Nyahaktifkan sifat berbilang nilai.
#XTOL:Tooltip for switch button for the multi-value property when the value is Off
cm_multi_value_tooltip_off=Aktifkan sifat berbilang nilai.
#XMSG Message on a messageToast telling the user the model refresh succeded
cm_is_up_to_date=adalah yang terkini.

#XTOL A tooltip for ellipsis button in source concepts models
cm_model_actions_tooltip=Tindakan Ontologi

#XFLD Uploading
cm_uploading=Mengimport...
#XFLD accepted upload file format
cm_accepted_file_format=Memilih fail TTL atau TRIG
#XTOL:Tooltip for File uploader
cm_fileUpload=Import Ontologi
#XFLD: Import Model dialog title
cm_import_model_dialog_title=Import Ontologi
#XBUT Cancel import model
cm_import_cancel_btn=Batalkan
#XBUT import rdf file
cm_import_btn=Import
#XMSG: File import success
cm_import_success=Import Berjaya
#XFLD:Loading
cm_loader_title=Mencipta ontologi...
#XMSG: Import model busy text split into 3 parts for styling
cm_loader_text=Ini mungkin mengambil masa seketika bergantung pada saiz ontologi.
#XMSG
cm_import_duplicate_uri_same_space=Ontologi ini telah wujud. Tindakan menggantikan akan menulis ganti ontologi yang sedia ada.
#XMSG
cm_import_duplicate_uri_different_folder=Ontologi ini telah wujud dalam folder lain. Tindakan menggantikan akan menulis ganti ontologi sedia ada dan mengalihkannya ke lokasi baharu ini.
#XMSG: No file selected message
cm_no_file_selected=Pilih fail
#XMSG: No file selected message
cm_error_file_upload=Terdapat ralat semasa memuat naik fail. Sila semak fail dan cuba semula.
#XMSG: Error message when import file limit exceeded
cm_file_size_limit_exceeded=Fail tidak boleh dimuat naik kerana ia melebihi saiz maksimum 50MB.
#XMSG: Error message in case of not supported file type
cm_file_type_missmatch=Maaf, jenis fail {0} tidak disokong. Pilih salah satu daripada jenis berikut: {1}.
#XBUT Busy upload dialog close button
cm_btnClose=Tutup
#XBUT Busy source code dialog copy button
cm_btnCopy=Salin
#XBUT Upload dialog replace option
cm_btnReplace=Gantikan
#XMSG: Invalid import response message
cm_invalid_import_response=Anda perlukan maklum balas import yang sah
#XMSG: Download file failure message
cm_export_fail=Cuba Muat Turun Fail Semula
#XBUT: save and download action button
cm_save_and_download_action=Simpan dan Muat Turun
#XMSG: Save and Dialog message
cm_save_and_download_text=Anda perlu menyimpan ontologi sebelum mengeksportnya.
#XMSG: First part of the message when the user cannot sync the ontology
cm_ontology_needs_sync=Ontologi ini perlu disegerakkan untuk membolehkan anda membukanya.
#XMSG: Second part of the message when the user cannot sync the ontology
cm_ontology_sync_needs_full_access=Untuk menyegerakkan ontologi, individu dengan kebenaran Cipta, Kemas Kini dan Padam perlu membukanya. Anda memerlukan kebenaran yang diperlukan.
#XMSG: First part of the message when the user does not have privileges to sync the space
cm_space_needs_sync=Ruang ini perlu disegerakkan supaya objek Pembina Graf dikemas kini.
#XMSG: Second part of the message when the user does not have privileges to sync the space
cm_space_sync_needs_full_access=Untuk menyegerakkan, individu dengan kebenaran Cipta, Kemas Kini dan Padam perlu membuka halaman ini atau klik butang Segerakkan jadual. Anda memerlukan kebenaran yang diperlukan.

#XGRP: Save and Download dialog title
cm_save_and_download_title=Simpan Ontologi?
#XTIT: Upload dialog title
cm_upload_file=Muat Naik Fail
#XMSG: Warning message informing user to not close the browser during upload
cm_file_upload_warning=Biarkan tab pelayar terbuka dan jangan segar semula semasa data sedang dimuat. Kami akan memaklumkan anda apabila ia selesai.

#XTOL:Tooltip for a button to add a source concept to the model
cm_add_concept=Tambah Konsep pada Ontologi
#XTOL:Tooltip for a button to add a source sub-concept to the model
cm_add_sub_concept=Tambah Subkonsep pada Ontologi

#XBUT:Button for a tile to create a new conceptual model
cm_create_new_conceptual_model=Ontologi Baharu
#XBUT:Text for the technical name of graph builder
cm_custom_technical_name=URI (Nama Teknikal)
#XBUT:Text for the technical type of graph builder
cm_custom_technical_type=Jenis
#XBUT:Button for confirming the creation of a conceptual model
cm_create=Cipta
#XBUT:Button for canceling the creation of a conceptual model
cm_cancel=Batalkan
#XBUT:Button for a tab for the conceptual models
cm_conceptual_models=Ontologi
#XBUT:Text for a menu item to create a new conceptual model
cm_import_conceptual_model=Import Ontologi
#XBUT:Text for a menu item to create a copy of a conceptual model
cm_copy_conceptual_model=Salin Ontologi
#XMSG Message on a messageToast if the user tries to save a model with a URI that already exists. The model will not save in this case.
cm_model_duplicate_uri=URl ini telah wujud dalam ruang ini atau ruang lain.
#XMSG Message on a messageToast if the user tries to save a concept with a URI that already exists. The concept will not save in this case.
cm_concept_duplicate_uri=URl ini telah wujud dalam ontologi ini.
#XMSG Message on a messageToast telling the user the model could not be saved. This is a catch all case if we don’t have a more specific reason.
cm_error_saving_model=Ralat telah berlaku dan ontologi tidak dapat dicipta.
#XMSG Message on a messsageToast if a back-end call fails and we don't know why
cm_error_something_went_wrong=Ralat tidak dijangka berlaku.
#XMSG:Text to confirm an entity was deleted on the graph builder landing page
cm_entity_deleted=Ontologi yang dipilih telah dipadam.
#XMSG:Text to confirm a few entities were deleted on the graph builder landing page
cm_entities_deleted=Ontologi yang dipilih telah dipadam.
#XMSG Message on a messageToast telling the user the resource deletion succeeded, {0} holds the name of a concept
cm_resource_deleted="{0}" dipadam
#XMSG Message on a messageToast telling the user the resource removal succeeded, {0} holds the name of a concept
cm_resource_removed="{0}" dikeluarkan
#XMSG Message on a messageToast telling the user the resource removal succeeded, {0} holds the name of a property, {1} holds the name of a concept
cm_reused_property_removed="{0}" dikeluarkan daripada "{1}"
#XBTN Delete concept and properties message box button
cm_delete_properties_and_concept=Padam
#XBTN Delete only concept message box button
cm_delete_concept=Padam Konsep Sahaja
#XTIT Unknown resource that does not have label or additional information
cm_unknown_resource=Tidak diketahui

#XBTN Delete concept in diagram
cm_diagram_delete_concept=Padam konsep
#XBTN Remove referenced concept in diagram
cm_diagram_remove_referenced_concept=Keluarkan konsep rujukan
#XBTN Add sub concept to the target concept in diagram
cm_diagram_add_sub_concept=Tambah subkonsep untuk kawalan terperinci
#XBTN Add sub concept to the target referenced concept in diagram
cm_diagram_add_sub_concept_referenced=Tambah subkonsep untuk kawalan terperinci
#XBTN Add relationship to the target concept in diagram
cm_diagram_add_relationship=Klik & seret untuk menambah hubungan
#XBTN Opens the target referenced concept in a new tab
cm_diagram_open_in_new_tab=Buka konsep dalam ontologi sumber dalam tab baharu
#XFLD Label of the diagram edges for a subclass relationship between nodes
cm_subconcept_of=Subkonsep

#XFLD Show the available ranges for that entry
cm_data_types_range=Jenis Data
#XTIT Dialog title to reuse properties in current working model, {0} holds the name of a concept
cm_reuse_properties_dialog_title=Tambah Sifat Sedia Ada kepada {0}
#XMSG Message toast saying any property was selected
cm_any_property_selected=Anda perlu lakukan pemilihan untuk menambah sifat.
#XMSG Message toast saying a property was selected, {0} holds the name of the property and {1} holds the name of a concept
cm_property_added_to_concept="{0}" ditambah ke "{1}"
#XMSG Message toast saying properties were selected, {0} holds the name of a concept
cm_properties_added_to_concept=Sifat ditambah ke "{0}"
#XFLD placeholder for search in add existing properties dialog
cm_search_placeholder_add_existing_prop=Cari sifat dan konsep dalam ontologi semasa
#XFLD tooltip for already existing properties, {0} holds the name of an URI
cm_uri_tooltip=URI:\n{0}
#XBTN confirmation button label for existing properties dialog
cm_add_existing_property=Tambah
#XMSG message toast to say that concept was deleted, {0} holds the name of a concept
cm_deletion_confirmation="{0}" dipadam
#XMSG message toast to tell about successfull copy
cm_object_was_created=\''{0}\'' telah dicipta
#XMSG message toast to say that concept and its properties were deleted, {0} holds the name of a concept
cm_concept_and_properties_deleted_confirmation="{0}" dan sifatnya dipadam
#XMSG warning message toast, {0} holds the name of a concept
cm_properties_question_warning_dialog=Padam "{0}" dan semua sifatnya?
#XMSG message that shows in a message toast to inform the model was saved
cm_entity_saved={0} disimpan.
#XMSG message that indicates there was a problem saving the entity
cm_entity_save_error=Ralat berlaku semasa cuba menyimpan ontologi {0}
#XMSG message that indicates there were either no or more than one ontologies present
cm_unexpected_ont_count_error=Ralat berlaku semasa cuba menyimpan ontologi. Menjangkakan satu ontologi dan tidak menemui apa-apa atau menemui lebih daripada satu.
#XMSG message that indicates the system could not parse the data in the graph file
cm_could_not_parse_graph=Cuba huraikan graf semula.
#XMSG message that indicates the named graph is different than the ontology uri
cm_named_graph_different_model=URI ontologi berbeza daripada nama grafnya. URI ini mesti sepadan.
#XMSG message saying any property was found in search for existing properties dialog
cm_no_properties_found_search=Tiada sifat ditemui
#XMSG  message to delete reused properties in current working model, {0} holds the name of a property
cm_delete_reused_properties_dialog_text=Konsep berbilang sedang menggunakan "{0}" dalam ontologi ini:
#XTIT Dialog title to delete reused properties
cm_delete_reused_properties_dialog_title=Padam Sifat pada Semua Konsep?
#XBUT delete reused properties button
cm_delete_properties=Padam
#XBUT cancel deletion of reused properties button
cm_cancel_delete_properties=Batalkan
#XTIT title of busy dialog for syncing operation
cm_sync_space_dialog_title=Menyegerakkan Data
#XTXT text of busy dialog for syncing operation
cm_sync_space_dialog_text=Tindakan ini mungkin mengambil sedikit masa...
#XTOL tooltip for sync button
cm_sync_space_button_text=Segerakkan
#XMSG message toast to indicate completion of syncing operation
cm_sync_operation_confirmation_message=Data adalah terkini
#XMSG message toast to delete reused properties message confirmation, where {0} is the name of a property and {1} is a number of concepts
cm_confirm_deletion_of_existing_properties="{0}" dipadamkan pada {1} konsep.
#XTOL:tooltip for the diagram toolbar zoom to fit button
cm_zoom_to_fit=Zum untuk muat
#XTOL:tooltip for the digram toolbar auto layout button
cm_auto_layout=Tataletak Automatik
#XMSG:diagram empty state title, shows up when the editor is in its initial state
cm_welcome_panel_no_data_title=Nampaknya anda perlu menambah konsep.
#YMSG:diagram empty state message, shows up when the editor is in its initial state
cm_welcome_panel_no_data_text=Tambah atau cipta konsep untuk mula membina ontologi anda.
#XMSG:Message on a messageToast telling the user the Browse Source Concept is in the left panel.
cm_browse_concepts_message=Layari Konsep Sumber dalam panel kiri.
#XMSG warning message title to inform that concept is already added
cm_concept_already_added_warning_title=Konsep telah ditambah
#XMSG warning message to inform that concept is already added
cm_concept_already_added_warning_message="{0}" telah ditambah pada ontologi.
#XMSG message to say search brought no result
cm_no_search_result=Tiada hasil
#XTIT target concepts dialog title
cm_source_code_dialog_title=Paparkan Sumber TriG
#XTIT target concepts dialog title
cm_target_concepts_dialog_title=Pilih Konsep Sasaran
#XTIT parent concepts dialog title
parent_concept_dialog_title=Pilih Konsep Induk
#XMSG message to say uri is empty
cm_uri_empty=Sila masukkan URl.
#XMSG message to say uri not a string
cm_not_valid_uri=Ini ialah format URI yang tidak sah.
#XMSG message to say uri has illegal characters
cm_uri_illegal_characters=URl ini mengandungi aksara dilarang.
#XMSG message to say uri has invalid hex escape sequence
cm_uri_invalid_escape_sequence=URl mengandungi jujukan lepasan heks.
#XMSG message to say uri has invalid scheme part
cm_uri_invalid_scheme=Sila masukkan skema URl (contohnya, http://).
#XMSG message to say duplicate uri
cm_duplicate_uri=URl ini telah wujud dalam ruang ini atau ruang lain.
#XTIT title to use on dialog when importing ontology
cm_duplicate_ontology_import=URI pendua
#XMSG message to say validating uri
cm_validating_uri=Mengesahkan...
#XMSG URI update success message
cm_uri_update=URI telah dikemas kini
#XTIT Edit Uri dialog title
cm_edit_uri_title=Edit URI (Nama Teknikal)
#XTOL:Tooltip for edit uri button
cm_edit_uri_tooltip=Edit URl penuh
#XTOL:Tooltip for edit uri button unsaved model
cm_edit_unsaved_ontology_uri_tooltip=Anda perlu menyimpan ontologi terlebih dahulu untuk mengedit URl.
#XTIT table item showing current working ontology available parent concepts, where {0} holds the name of a working ontology
cm_working_model_parent_concept={0} (semasa)
#XMSG message toast saying a new parent concept was choosen, where {0} holds the name of the parent concept
cm_new_parent_concept_message="{0}" ditambah sebagai konsep induk.
#XMSG parent concepts multi input placeholder
cm_parent_concept_multi_input_placeholder=Tambah konsep untuk sifat diwarisi
#XMSG parent concepts dialog search placeholder
parent_concept_dialog_search_placeholder=Cari konsep dalaman dan luaran
#XTIT Type of the range property list
cm_range_properties_title_featured=Biasa
#XTIT Type of the range property list
cm_range_properties_title_others=Semua yang lain
#XMSG value state warning for property with any selected data types
cm_any_data_type_selected_warning=Sila pilih jenis data.
#XMSG value state warning for property with multiple selected data types
cm_multiple_data_types_value_state_warning=Jenis data berbilang boleh menjadi masalah dalam situasi biasa. Anda perlu gunakannya untuk menyokong senario pemodelan lanjutan sahaja.
#XTIT data types dialog title
cm_data_type_dialog_title=Pilih Jenis Data
#XMSG message when an ontology does not have concepts
cm_empty_ontology=Tiada konsep
#XMSG placeholder text when there is no local name in a URI
cm_no_local_name=Tiada Nama Tempatan
#XTOL: Enter the fullscreen mode for the diagram
enter_full_screen=Masukkan Skrin Penuh
#XTOL: Exit the fullscreen mode for the diagram
exit_full_screen=Keluar Skrin Penuh
#XMSG message to say that an entity was added from another ontology
added_from_another_ontology=Ditambah daripada:
#XMSG message to say that an entity was inherited from a working concept
inherited_from_a_working_concept=Diwarisi daripada:
#XTOL tooltip for opening concept in source ontology
source_concept_tag_tooltip=Buka konsep dalam ontologi sumber
#XTOL tooltip for opening property in source ontology
source_property_tag_tooltip=Buka sifat dalam ontologi sumber
#XTOL tooltip for opening property in source concept of the working ontology
source_property_origin_concept_tag_tooltip=Pergi ke konsep
#XMSG missing ontology warning title
missing_ontology_warning_title=Ontologi tiada
#XMSG missing ontology warning description
missing_ontology_warning_description=URI ontologi ini dirujuk tetapi ia tidak wujud dalam ruang ini. Beberapa elemen ontologi anda mungkin tiada.
#XTOL button tooltip saying to read the warning
imported_ontology_warning_tooltip=Baca amaran
#XTOL button tooltip saying to open the source ontology in a new tab
open_imported_ontology_tooltip=Buka ontologi sumber dalam tab baharu
#XMSG message to inform there isn't imported ontologies in the current open ontology
no_imported_ontology=Tiada
#XMSG separation colon mark, where {0} holds the name of an ontology and {1} holds the name of a class
separation_colon={0}: {1}
#XMSG no results text indication
no_results=Tiada Hasil
#XMSG no parent concepts found message
no_parent_concepts_found=Tiada konsep ditemui
#XMSG separation for only a parent in concepts - 1 parent
separated_parent_concepts_1=({0})
#XMSG separation colon mark for a list of parents in concepts - 2 parents
separated_parent_concepts_2=({0}, {1})
#XMSG separation colon mark for a list of parents in concepts - 2 or more parents
separated_parent_concepts_3=({0}, {1}, ...)
#XFLD placeholder in case an ontology resource is not named
cm_untitled_resource=(Tidak Bertajuk)
#XMSG message to request the user to fill up the ontology label
cm_empty_ontology_label_warning=Namakan ontologi anda.
#XMSG message to request the user to fill up the concept label
cm_empty_concept_label_warning=Namakan konsep anda.
#XMSG message to request the user to fill up the property label
cm_empty_property_label_warning=Namakan sifat anda.
#XMSG message to request the user to fill up the relationship label
cm_empty_relationship_label_warning=Namakan hubungan anda.
#XMSG message to display backend error when searching for parent classes in non working models
cm_parent_class_search_not_found=Tiada kelas sepadan yang mengandungi teks yang ditemui dalam ontologi lain
#XMSG message to show bad request error when searching parent classes in non working ontologies
cm_parent_class_search_bad_request=Permintaan tidak sesuai dihantar ke kelas induk carian
#XMSG message to show forbidden error
cm_entity_forbidden_error=Pengguna perlu mempunyai kebenaran
#XMSG Message to show no models from backend were returned
cm_no_models_found=Tiada model dengan uri {0} ditemui
#XMSG Message to show bad request errors for save model api call
cm_save_model_bad_request_error=Permintaan Tidak Sesuai dihantar untuk menyimpan api model
#XMSG Message to show bad request errors for get frames api call
cm_get_frames_bad_request_error=Permintaan Tidak Sesuai dihantar untuk mendapatkan api bingkai
#XMSG Message to show bad request errors for get models api call
cm_get_models_bad_request_error=Permintaan Tidak Sesuai dihantar untuk mendapatkan api model
#XMSG Message to show bad request errors for search model api call
cm_search_models_request_error=Permintaan Tidak Sesuai dihantar untuk mencari api model
#XMSG Message to indicate a generic bad request with no further details
cm_bad_request=Permintaan tidak sesuai
#XMSG Message to indicate an unexpected error occurred
cm_unexpected_error=Ralat tidak dijangka berlaku
#XMSG Message to indicate one or more of the requested graphs were not found
cm_graph_not_found=Satu atau lebih graf yang diminta tidak ditemui
#XMSG Message to handle http status bad gateway (502)
cm_bad_gateway_error=Tidak dapat mencapai pelayan. Cuba lagi kemudian.
#XMSG Message to handle http status service unavailable (503)
cm_service_unavailable_error=Tidak dapat mencapai pelayan. Cuba lagi kemudian.
#XMSG Message to handle http status service unavailable (503)
cm_internal_server_error=Pelayan mengalami masalah yang tidak dijangka. Cuba sekali lagi atau semak di bawah untuk maklumat lanjut.
#XMSG Error message strip to indicate that the URI does not exist or is broken
cm_missing_ontology_error=Kami tidak berjaya mencari ontologi. Ia mungkin tidak lagi wujud atau URI telah diubah.
#XMSG Error message strip to indicate that the ontology object does not exist or the URI for it may have changed
cm_missing_ontology_object_error=Kami tidak berjaya mencari objek dalam ontologi ini. Ia mungkin tidak lagi wujud atau URI telah diubah.
#XMSG Title to add detached property to Concept dialog
cm_add_detached_property_to_concept_dialog_title=Tambah {0} ke Konsep
#XMSG Placeholder to add detached property to concept dialog search
cm_concepts_in_current_ontology_search_placeholder=Cari konsep dalam ontologi semasa
#XMSG Message toast saying any property was selected
cm_any_concepts_selected=Anda perlu lakukan pemilihan untuk menambah konsep.
#XMSG No concepts found in dialog search message
cm_no_concept_found=Tiada konsep ditemui
#XMSG Confirmation Message toast for detached property added to Concept
cm_add_detached_property_to_concept_message_toast=''{0}'' telah ditambah ke ''{1}''
#XMSG Confirmation Message toast for detached property added to multiple Concepts
cm_add_detached_property_to_multiple_concepts_message_toast=''{0}'' telah ditambah ke ''{1}'' dan {2} lagi konsep
#XMSG Error message to indicate that the concept being imported has conflict with working ontology entities
cm_uri_conflict_error_start=Maaf! Konsep ini perlu diimport semula kerana konflik URI.
#XMSG Error message to add details of concept or the ontology conflicting with working ontology.
cm_concept_uri_conflict_details_error=Konsep {0} perlu diimport semula kerana URI ({1}) adalah URI yang sama dengan {2} bagi ontologi ini.
#XMSG Error message to indicate that other entities in the concept are conflicting with the working ontology
cm_concept_entities_conflict_error=Konsep {0} perlu diimport semula kerana ia mengandungi entiti berikut yang URInya adalah URI yang sama dengan URI yang telah wujud dalam ontologi ini.
#XMSG Unique constraint violation error message
cm_uri_conflict_error_end=Semua URI dalam ontologi mestilah unik.
#XMSG Messages displayed in the save as comboBox default option
cm_generate_from_business_name_option=Janakan daripada Nama Perniagaan (lalai)
#XMSG Messages displayed in the save as comboBox preserve custom structures option
cm_preserve_custom_option=Kekalkan Struktur Tersuai
#XMSG Messages displayed in the save as comboBox keep existing option
cm_keep_existing_option=Simpan URI Sedia Ada
#XMSG message to say URI not a string
cm_ontology_uri_generation_instruction=URI bagi semua objek dalam ontologi anda akan dijana daripada nama perniagaan mereka.
#XMSG message to provide an example
cm_save_as_messages_example=Contoh:
#XMSG message to show a concept local name
cm_ontology_uri_generation_sale=Jualan
#XMSG message to show the concept
cm_ontology_uri_generation_concept=konsep
#XMSG message to describe the preservation of custom structure in object URIs
object_uri_preservation_instruction=Sistem akan menyemak URI objek dalam ontologi anda dan mencuba untuk mengekalkan apa-apa struktur tersuai selepas awalan URI.
#XMSG label for describing an old ontology URI
cm_old_ontology_uri=URI Ontologi Lama
#XMSG label for describing an old sale concept URI
cm_old_sale_uri=URI konsep Jualan Lama
#XMSG label for describing a new sale URI
cm_new_sale_URI=URI Jualan Baharu
#XMSG label for describing the local name of a sale URI
cm_sale_uri_local_name=GUID
#XMSG message to warn about the preservation of URIs in ontology
cm_uri_preservation_warning=URI bagi semua konsep dalam ontologi anda tidak akan berubah. Ini boleh menyebabkan konflik, jadi gunakan dalam senario pemodelan lanjutan sahaja.
#XTIT object uris title for save as dialog
cm_object_uris=URI Objek
#XFLD prefix used when a copy of an ontology is created
cm_copy_of=Salinan bagi
#XTIT
package=Folder
#XMSG concept label for detached properties in existing properties dialog
cm_no_concept=[Tiada konsep]
#XMSG message to indicate that no selection was made in add to concept dialog
cm_no_concepts_selected=Anda perlu lakukan pemilihan untuk menambah Sifat.
#XMSG tooltip to indicate that the user needs Delete, Update and Create permissions to edit the ontology URI
cm_uri_edit_permissions_tooltip=Kebenaran Padam, Kemas Kini dan Cipta diperlukan untuk mengedit URI ontologi.
