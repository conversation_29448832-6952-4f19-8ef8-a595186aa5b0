/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

/* eslint-disable id-blacklist */

import { getArtefactSharesForTarget } from "../../../../services/metadata";
import { getWorkbenchController } from "../../../abstractbuilder/utility/BuilderUtils";
import * as RepositoryUtils from "../../../abstractbuilder/utility/RepositoryUtils";
import { CurrentContextService } from "../../../commonmodel/api/CurrentContextService";
import {
  DeploymentStatus,
  DocumentProperty,
  DocumentStorageService,
} from "../../../commonmodel/api/DocumentStorageService";
import { SupportedFeaturesService } from "../../../commonmodel/api/SupportedFeaturesService";
import { CsnAnnotations } from "../../../commonmodel/csn/csnAnnotations";
import {
  clone<PERSON>son,
  deltaTableReadCsn,
  findInCollectionByName,
  getAllowConsumptionFromCsn,
  getEntityAndContextNameFromCSN,
  handleCsnForDataPreview,
  handlePersistedViewCsnForDataPreview,
  handlePersistedViewCsnForSQLPreview,
  isAnalyticParameter,
  isCsnMeasure,
  partitionsReadCsn,
  updateViewFromDataAccessControlCSN,
} from "../../../commonmodel/csn/csnUtils";
import { DataCategoryAnnotation } from "../../../commonmodel/model/annotations/dataCategoryAnnotation";
import { CommonQualifiedClassNames, ObjectInstanceTypes } from "../../../commonmodel/model/const/model.const";
import { CDSDataType, CardinalityValues, DataCategory } from "../../../commonmodel/model/types/cds.types";
import { getListOfConsumedInputParameters, isAnalyticMeasureElement } from "../../../commonmodel/utility/CommonUtils";
import { CompatibilityContractPropagator } from "../../../commonmodel/utility/CompatibilityContractUtils";
import { GModelHandler } from "../../../commonmodel/utility/GModelHandler";
import { NameUsage } from "../../../commonui/utility/NameInputValidator";
import { Editor } from "../../../databuilder/utility/Constants";
import {
  getIsVersioningReadOnlyMode,
  getIsVersioningRestoreMode,
  openInNewTab,
} from "../../../databuilder/utility/DatabuilderHelper";
import { QualifiedClassNames } from "../../../ermodeler/js/statics/const/er.model";
import { MessageHandler } from "../../../reuse/utility/MessageHandler";
import { NamingHelper } from "../../../reuse/utility/NamingHelper";
import { ObjectStatus } from "../../../reuse/utility/Types";
import { ShellContainer } from "../../../shell/utility/Container";
import { Repo } from "../../../shell/utility/Repo";
import { DWCFeature, EventType } from "../../../shell/utility/ShellUsageCollectionService";
import { createContextObject, loadCrossSpaceEntity } from "../../../sqleditor/utility/SqlEditorUtility";
import { isOutputNodePersisted } from "../../../viewmonitor/viewpersistencyservice/ServiceConsumption";
import { IGVEExtensions } from "../../Component";
import { getProjectedElement, loadUnresolvedAssociations } from "../../utility/AssociationHelper";
import { ElementChangeType, SourceToOutputChangesMonitor } from "./SourceToOutputChangesMonitor";

const USAGE_VIEW_BUILDER = "View Builder";

enum UsageActions {
  OPEN_IN_EDITOR = "openInEditor",
  DATA_PREVIEW = "dataPreview",
  PREVIEW_SQL = "previewSQL",
}

sap.galilei.namespace("sap.cdw.querybuilder", function (nsLocal) {
  const nsCommonModel = sap.cdw.commonmodel;
  const nsErmodeler = sap.cdw.ermodeler;
  /**
   * @class
   * ModelImpl implements all methods related to Model objects
   */
  nsLocal.ModelImpl = sap.galilei.core.defineClass({
    fullClassName: "sap.cdw.querybuilder.ModelImpl",

    statics: {
      // by default we deactivate automatic rename/remove elements
      // in case of join (duplicate elements), the value is reset to
      // true in editor extension
      // we keep it false here to avoid impacting existing tests.
      HANDLE_JOIN_DUPLICATE: false,

      OUTPUT_TYPE: {
        GRAPHIC: "graphicView",
        SQL: "sqlView",
      },

      JOIN_TYPES: [
        // 'left', 'right', 'full', 'inner' or 'cross'
        {
          key: "left",
          displayName: "txtJoinTypeLeft",
        },
        {
          key: "right",
          displayName: "txtJoinTypeRight",
        },
        {
          key: "full",
          displayName: "txtJoinTypeFull",
        },
        {
          key: "inner",
          displayName: "txtJoinTypeInner",
        },
        {
          key: "cross",
          displayName: "txtJoinTypeCross",
        },
      ],

      UNIQUE_CSN_RESOURCE_ID: "uniqueCsnResource",
      CSNMODEL_CHANNEL: "csnModel",
      CSNMODEL_CHANGED_EVENT: "modelChanged",
      CSNMODEL_DELETE_OBJECTANDSYMBOLS: "deleteObjectAndSymbols",
      CSNMODEL_LOADED: "modelLoaded",
      CSNMODEL_LOADED_AND_VALIDATED: "modelLoadedAndValidated",
      CSNMODEL_NEWMODEL_EVENT: "newModel",
      CSNMODEL_REQUEST_USER_ACTION: "requestUserAction",
      CSNMODEL_ASYNC_ELTS_LOADED: "asyncElsLoaded",
      ENTITY_DROPPED: "entityDropped",
      CSNMODEL_REQUEST_PARAMETER_MAPPING: "parameterMapping",

      /**
       * Get current model or create a new on if not
       * @function
       * @name createNewModelAndDiagrams
       * @memberOf sap.cdw.querybuilder.ModelImpl
       */
      getOrCreateModel: function (sName, oLoadingData, sResourceId, oService, sqlView = false, oCsnDocument) {
        const self = this;
        this.oService = oService;
        const oResource = new sap.galilei.model.Resource(sResourceId || nsLocal.ModelImpl.UNIQUE_CSN_RESOURCE_ID);
        let configuration: IGVEExtensions;

        if (!sqlView) {
          configuration = oLoadingData?.configuration;
        }
        let oModelLoadData;
        let bNewModel;
        let oModel;

        function notifyNewModel() {
          // No undo for initialization
          self.clearUndoRedo(oModel);

          nsLocal.ModelImpl.publishNewModelEvent({
            model: oModel,
            loaded: !bNewModel,
            new: bNewModel,
          });
        }

        function simpleTypesCompletedAndSendNotification() {
          oModel.completeProcess("UPDATE_SIMPLE_TYPES");
          oModel.isInLoadingProcess = false;
          notifyNewModel();
        }

        function postGetOrCreateModel() {
          // Load simple types and update base type for simple types and elements
          oModel
            .prepareProcess("UPDATE_SIMPLE_TYPES", self.localizeText("UPDATNG_TYPES"))
            .then(() => {
              self.loadAndUpdateSimpleTypes(oModel).finally(simpleTypesCompletedAndSendNotification);
            })
            .catch(simpleTypesCompletedAndSendNotification);
        }

        const data = GModelHandler.readGVEGalileiModel(oLoadingData);
        oModelLoadData = data.model;

        if (oModelLoadData) {
          if (typeof oModelLoadData === "object") {
            // TODO: Remove it later
            // Convert old version to string
            oModelLoadData = JSON.stringify(oModelLoadData);
          }
          // Autofix galilei model JSON string
          oModelLoadData = nsLocal.ModelImpl.autoFixModelString(oModelLoadData);

          try {
            oModelLoadData = JSON.parse(oModelLoadData);
            // Autofix galilei model JSON string
            oModelLoadData = nsLocal.ModelImpl.autoFixModelJson(oModelLoadData);
          } catch (e) {
            // eslint-disable-next-line no-empty
          }
        }

        const oCSN = oLoadingData && oLoadingData.file && oLoadingData.file.csn;
        // Load existing model
        if (oModelLoadData) {
          bNewModel = false;
          const oReader = new sap.galilei.model.JSONReader(),
            oMetaResource = sap.galilei.model.getResource("sap.cdw.querybuilder.model");

          oReader.load(oResource, oModelLoadData, /* batchMode */ false, [oMetaResource]);
          oModel = oResource.selectObject({
            "classDefinition.name": "Model",
          });
          oModel.spaceId = CurrentContextService.getInstance().getCurrentSpaceId();
          nsLocal.ModelImpl.autoFixGalileiModel(oModel);
          // eslint-disable-next-line no-underscore-dangle
          oModel._isLoading = true;
          // this is used only in Compatibility Contracts changeManagement workflow
          oModel.isInLoadingProcess = true;
          oModel.isNew = false;
          if (oModel.output) {
            oModel.output.type = sqlView ? nsLocal.ModelImpl.OUTPUT_TYPE.SQL : nsLocal.ModelImpl.OUTPUT_TYPE.GRAPHIC;
          }
          if (oModel && oLoadingData.file) {
            RepositoryUtils.updateObjectFileInfo(oModel, oLoadingData.file);
            oResource.model = oModel;
            RepositoryUtils.updateObjectFileInfo(oModel.output, oLoadingData.file);
          }

          this.migrateLevelBasedHierarchy(oModel, oLoadingData.query);

          nsLocal.ModelImpl.requestAdjustDiagramsContent({
            model: oModel,
          });

          // Update sources...
          if (oService) {
            oModel.aMissingSources = [];
            // eslint-disable-next-line no-underscore-dangle
            oModel._disableValidation = true;
            // eslint-disable-next-line no-underscore-dangle
            oModel._skipEvaluateType = true;
            // Rolling back?
            if (oService._rollingBack) {
              oModel._rollingBack = true;
            }
            oResource.isDisableUndoRedo = true;
            this.parseLocallySimpleCalcExpressions(oModel)
              .finally(() => this.updateModelSources(oModel, oService, oCSN))
              .finally(() => {
                // Rolling back?
                // All sources update completed
                // --> change management processings
                // Output
                if (oModel._rollingBack) {
                  delete oModel._rollingBack;
                } else {
                  this.handleOutputChangesAutofix(oModel.output, oModel.output.getRepositoryCSN(), oModel, oService);
                }
                // Expressions (filter, aggregation, calculated)
                // eslint-disable-next-line dot-notation
                window["performanceLogger"]?.enterMethod({ name: "handleExpressionColumnDatatypeChangesAutofix" });
                this.handleExpressionColumnDatatypeChangesAutofix(oModel).finally(() => {
                  // eslint-disable-next-line dot-notation
                  window["performanceLogger"]?.leaveMethod({ name: "handleExpressionColumnDatatypeChangesAutofix" });
                  // eslint-disable-next-line no-underscore-dangle
                  oModel._skipEvaluateType = false;
                });
                // eslint-disable-next-line no-underscore-dangle
                oModel._disableValidation = false;

                // No undo for initialization
                this.clearUndoRedo(oResource);
                oResource.isDisableUndoRedo = false;
                // change management
                // - activate 'dirty mode' (see function 'isDirty'), if there are changes by 'autofix'
                //   -> asterix character at end of new shell's breadcrumb + save popup when leaving editor
                if (this.hasAutofixChanges(oModel)) {
                  oModel.bDirty = true;
                }
                nsLocal.ModelImpl.publishNewModelEvent({
                  model: oModel,
                  loaded: true,
                });
                if (oModel.aMissingSources && oModel.aMissingSources.length) {
                  if (oService.warnMissingModelObjects) {
                    oService.warnMissingModelObjects("missingSources", oModel.aMissingSources);
                  }
                  const isTechnicalVersionRestoreEnabled =
                    SupportedFeaturesService.getInstance().isTechnicalVersionRestoreEnabled();
                  if (!isTechnicalVersionRestoreEnabled) {
                    delete oModel.aMissingSources;
                  }
                }
                postGetOrCreateModel();
              });
          } else {
            postGetOrCreateModel();
          }
        } else {
          // Creates a model
          bNewModel = true;
          oModel = new sap.cdw.querybuilder.Model(oResource, {
            name: configuration?.defaultBusinessName || sName || "View_1",
            label: configuration?.defaultTechnicalName || sName || "View 1",
            isNew: !sName,
          });
          oResource.model = oModel;
          oModel.spaceId = CurrentContextService.getInstance().getCurrentSpaceId();

          // creates output node
          const oOutput = nsLocal.ModelImpl.createObject(
            "sap.cdw.querybuilder.Output",
            {
              name: oModel.name,
              label: oModel.label,
              type: sqlView ? nsLocal.ModelImpl.OUTPUT_TYPE.SQL : nsLocal.ModelImpl.OUTPUT_TYPE.GRAPHIC,
            },
            oModel
          );
          if (oService) {
            if (sqlView) {
              const oCsn = oCsnDocument && oCsnDocument.definitions && oCsnDocument.definitions[sName];
              oOutput.isAllowConsumption = getAllowConsumptionFromCsn(oCsn);
            } else {
              RepositoryUtils.getSpaceAllowConsumption(oService.spaceId).then((result) => {
                oModel.resource.applyUndoableAction(
                  function () {
                    oOutput.isAllowConsumption = result;
                  },
                  "Set isAllowConsumption",
                  true,
                  /* batchMode*/ true
                );
              });
            }
          }
          oModel.output = oOutput;

          nsLocal.ModelImpl.requestAdjustDiagramsContent({
            model: oModel,
          });

          postGetOrCreateModel();
        }

        if (!oModel.isNew) {
          oModel
            .processing()
            .then(() => nsLocal.ModelImpl.notifyModelLoaded(oModel))
            .then(() => oModel.validate())
            .then(() => nsLocal.ModelImpl.clearUndoRedo(oModel))
            .finally(() => {
              // eslint-disable-next-line no-underscore-dangle
              oModel._isLoading = false;
              oModel.isInLoadingProcess = false;
              oModel.nodes.forEach((oNode) => {
                delete oNode.bValidated;
              });
              // Notify that the model has been loaded and validated
              nsLocal.ModelImpl.notifyModelLoadedAndValidated(oModel);
            });
        }
        return {
          model: oModel,
          lastModifier: data.lastModifierEditor,
          defaultEditor: data.defaultEditor,
          processing: oModel.processing(),
        };
      },

      parseLocallySimpleCalcExpressions: async function (model) {
        await model.prepareProcess("ANALYZING", "Analyzing.....");
        // eslint-disable-next-line dot-notation
        window["performanceLogger"]?.enterMethod({
          name: `parseLocallySimpleCalcExpressions (#nodes: ${model.nodes.length})`,
        });
        for (let i = 0, l = model.nodes.length; i < l; i++) {
          const node = model.nodes.get(i);
          (await node.parseLocallySimpleExpressions) && node.parseLocallySimpleExpressions();
        }
        // eslint-disable-next-line dot-notation
        window["performanceLogger"]?.leaveMethod({
          name: `parseLocallySimpleCalcExpressions (#nodes: ${model.nodes.length})`,
        });
        model.completeProcess("ANALYZING");
        return Promise.resolve();
      },

      /**
       * Autofix the galilei model JSON string.
       * @param oModelLoadData
       */
      autoFixModelString: function (oModelLoadData: string) {
        if (oModelLoadData && typeof oModelLoadData === "string") {
          // Change "leftElement":, "rightElement": to "source":, "target":
          oModelLoadData = oModelLoadData.replace(/\"leftElement\":/g, '"source":');
          oModelLoadData = oModelLoadData.replace(/\"rightElement\":/g, '"target":');

          // Change "sourceNode":, "targetNode": to "source":, "target":
          oModelLoadData = oModelLoadData.replace(/\"sourceNode\":/g, '"source":');
          oModelLoadData = oModelLoadData.replace(/\"targetNode\":/g, '"target":');

          // Change "sap.cdw.ermodeler.ValidationStatus" to "sap.cdw.commonmodel.ValidationStatus"
          oModelLoadData = oModelLoadData.replace(
            /sap\.cdw\.ermodeler\.ValidationStatus/g,
            "sap.cdw.commonmodel.ValidationStatus"
          );

          // Change "sap.cdw.ermodeler.BaseHierarchy" to "sap.cdw.commonmodel.BaseHierarchy"
          oModelLoadData = oModelLoadData.replace(
            /sap\.cdw\.ermodeler\.BaseHierarchy/g,
            "sap.cdw.commonmodel.BaseHierarchy"
          );

          // Change "sap.cdw.ermodeler.ParentChildHierarchy" to "sap.cdw.commonmodel.ParentChildHierarchy"
          oModelLoadData = oModelLoadData.replace(
            /sap\.cdw\.ermodeler\.ParentChildHierarchy/g,
            "sap.cdw.commonmodel.ParentChildHierarchy"
          );

          // Change "sap.cdw.ermodeler.LevelBasedHierarchy" to "sap.cdw.commonmodel.LevelBasedHierarchy"
          oModelLoadData = oModelLoadData.replace(
            /sap\.cdw\.ermodeler\.LevelBasedHierarchy/g,
            "sap.cdw.commonmodel.LevelBasedHierarchy"
          );

          // Change "sap.cdw.ermodeler.LevelElement" to "sap.cdw.commonmodel.LevelElement"
          oModelLoadData = oModelLoadData.replace(
            /sap\.cdw\.ermodeler\.LevelElement/g,
            "sap.cdw.commonmodel.LevelElement"
          );

          // Change "sap.cdw.querybuilder.Parameter" to "sap.cdw.commonmodel.Parameter"
          oModelLoadData = oModelLoadData.replace(
            /sap\.cdw\.querybuilder\.Parameter/g,
            "sap.cdw.commonmodel.Parameter"
          );

          // Change "sap.cdw.querybuilder.ReferenceEntity" to "sap.cdw.commonmodel.ReferenceEntity"
          oModelLoadData = oModelLoadData.replace(
            /sap\.cdw\.querybuilder\.ReferenceEntity/g,
            "sap.cdw.commonmodel.ReferenceEntity"
          );

          // Change "sap.cdw.querybuilder.ElementMapping" to "sap.cdw.commonmodel.ElementMapping"
          oModelLoadData = oModelLoadData.replace(
            /sap\.cdw\.querybuilder\.ElementMapping/g,
            "sap.cdw.commonmodel.ElementMapping"
          );
          // Change "sap.cdw.ermodeler.ElementMapping" to "sap.cdw.commonmodel.ElementMapping"
          oModelLoadData = oModelLoadData.replace(
            /sap\.cdw\.ermodeler\.ElementMapping/g,
            "sap.cdw.commonmodel.ElementMapping"
          );

          // Change "sap.cdw.ermodeler.Element" to "sap.cdw.commonmodel.Element"
          oModelLoadData = oModelLoadData.replace(/sap\.cdw\.ermodeler\.Element/g, "sap.cdw.commonmodel.Element");
        }
        return oModelLoadData;
      },

      /**
       *  Auto fix loaded galilei model if needed
       */
      autoFixGalileiModel: function (galileiModel: any): void {
        const fixUndefinedJoinInputs = (join) => {
          let predecessors;
          if (!join.leftInput) {
            predecessors = join.predecessorNodes;
            join.leftInput = predecessors.length > 0 ? predecessors.get(0) : undefined;
          }
          if (!join.rightInput) {
            if (!predecessors) {
              predecessors = join.predecessorNodes;
            }
            join.rightInput = predecessors.length > 1 ? predecessors.get(1) : undefined;
          }
        };

        const fixIncorrectJoinInputs = (join) => {
          const leftInput = join.leftInput;
          const rightInput = join.rightInput;
          const leftok = !leftInput || (leftInput.successorNode && leftInput.successorNode === join);
          const rightok = !rightInput || (rightInput.successorNode && rightInput.successorNode === join);
          if (leftok && rightok) {
            return;
          }
          const predecessorNodes = join.predecessorNodes.toArray();
          if (predecessorNodes.length === 2) {
            if (!leftok && rightok) {
              join.leftInput = predecessorNodes[1 - predecessorNodes.indexOf(rightInput)];
            } else if (leftok && !rightok) {
              join.rightInput = predecessorNodes[1 - predecessorNodes.indexOf(leftInput)];
            }
          }
        };

        // Fix previous JOIN nodes with missing leftInput/rightInput (introduced to fix Jira#FPA101-10102)
        const joins = galileiModel.nodes.selectAllObjects({
          "classDefinition.name": "Join",
        });
        for (let i = 0, l = joins.length; i < l; i++) {
          const join = joins.get(i);
          fixUndefinedJoinInputs(join);
          fixIncorrectJoinInputs(join);
        }
        // End auto fix join nodes
      },

      /**
       * Autofix the galilei model JSON object.
       * @param oModelLoadData
       */
      autoFixModelJson: function (oModelLoadData: any) {
        if (oModelLoadData && typeof oModelLoadData === "object") {
          // No ESLint
        }
        return oModelLoadData;
      },

      migrateLevelBasedHierarchy(model, query) {
        const output = model.output;
        const hierarchies = output.hierarchies;
        if (!hierarchies.length) {
          return;
        }

        hierarchies.toArray().forEach((hierarchy) => {
          if (hierarchy.qualifiedClassName !== CommonQualifiedClassNames.LEVEL_BASED_HIERARCHY) {
            return;
          }
          const firstElement = hierarchy.levels.get(0);
          if (
            !firstElement ||
            firstElement.qualifiedClassName === CommonQualifiedClassNames.LEVEL_BASED_HIERARCHY_ELEMENT
          ) {
            return;
          }

          const hierarchyCsn = query["@Hierarchy.leveled"].find((h) => h.name === hierarchy.name);
          const newLevels = [];
          hierarchy.levels.toArray().forEach((element) => {
            const levelElementClass = sap.galilei.model.getClass(
              CommonQualifiedClassNames.LEVEL_BASED_HIERARCHY_ELEMENT
            );
            const levelElement = levelElementClass.create(model.resource, {
              element,
              indexOrder: hierarchyCsn.levels.findIndex((level) => level.element["="] === element.name),
            });
            newLevels.push(levelElement);
          });

          hierarchy.levels.clear();
          newLevels.forEach((l) => hierarchy.levels.push(l));
        });
      },

      updateAssociationsFromCsn: function (object, csn, model, service) {
        const oErCsnToModel = sap.cdw.ermodeler.ErCsnToModel.getInstance(true);
        if (object && csn && csn.elements) {
          const oCsnToModel = nsLocal.CsnToModel;
          const associationsToDelete = [];
          const associationsFromCsn = [];
          const elements = Object.keys(csn.elements);
          for (const el of elements) {
            const elem = csn.elements[el];
            if (elem.type === CDSDataType.ASSOCIATION) {
              associationsFromCsn.push(el);
            }
          }
          object.sourceAssociations.forEach((association) => {
            let sName = association && (association.newName || association.name);
            if (!sName) {
              sName = `_${association.target.name}`;
              association.name = sName;
            }
            if (sName) {
              const associationCsn = csn.elements[sName];
              if (associationCsn === undefined) {
                // Associations is deleted
                associationsToDelete.push(association);
              } else if (oCsnToModel) {
                // update association and mappings
                oErCsnToModel.updateAssociation(association, associationCsn, false);
              }
              const assocIndex = associationsFromCsn.indexOf(sName);
              if (assocIndex > -1) {
                associationsFromCsn.splice(assocIndex, 1);
              }
            }
          });
          // delete associations
          if (associationsToDelete.length > 0) {
            associationsToDelete.forEach(function (oAssociation) {
              nsLocal.ModelImpl.deleteObject(oAssociation);
            });
          }
          if (associationsFromCsn.length > 0) {
            this.createMissingAssociations(associationsFromCsn, object, csn, model, service);
          }
        }
      },

      updateDataAccessControls: async function (object, csn, service) {
        const dacs = csn[CsnAnnotations.DataWarehouse.dataAccessControl_usage];
        if (dacs?.length > 0) {
          const viewDataAccessControls = object.viewDataAccessControls;
          for (const dac of dacs) {
            const viewDataAccessControl = findInCollectionByName(viewDataAccessControls, dac?.target);
            if (viewDataAccessControl) {
              // Load/Update target if needed
              if (!viewDataAccessControl.dataAccessControl) {
                const repoDAC = await service.getRepositoryObject(dac.target);
                const dacCSN = repoDAC?.csn?.definitions?.[dac.target];
                if (dacCSN) {
                  const oResource = viewDataAccessControl && viewDataAccessControl.resource;
                  oResource?.applyUndoableAction(
                    function () {
                      updateViewFromDataAccessControlCSN(viewDataAccessControl, dacCSN);
                    },
                    "updateViewFromDataAccessControlCSN",
                    /* protected from undo/redo */ true
                  );
                }
              }
            }
          }
        }
      },

      createMissingAssociations: async function (missingAssociations, output, csn, model, service) {
        // Create Associations
        for (const elem of missingAssociations) {
          const associationToCreate = csn.elements[elem];
          if (associationToCreate.type === CDSDataType.ASSOCIATION) {
            const targetName = associationToCreate.target;
            let objectFound;
            if (targetName && targetName.includes(".")) {
              if (SupportedFeaturesService.getInstance().isDotSupportEnabledFF()) {
                // Get from current space first if dot support is enabled
                objectFound = await service.getRepositoryObject(targetName);
              }
              if (!objectFound?.csn) {
                const contextName = nsCommonModel.ObjectImpl.getQualifierName(targetName, /* bFirstQualifier */ true);
                let context = sap.cdw.commonmodel.ModelImpl.getContext(contextName, model) as any;
                const options = { model: model, crossSpaceObjects: [] };
                objectFound = await loadCrossSpaceEntity(targetName, options);
                if (!context) {
                  context = await createContextObject(targetName, model, objectFound);
                }
              }
            } else {
              objectFound = await service.getRepositoryObject(targetName);
            }
            if (objectFound && objectFound.csn) {
              const oTarget = sap.cdw.querybuilder.ModelImpl.createObject(
                QualifiedClassNames.DIMENSION_NODE,
                objectFound,
                model
              );
              sap.cdw.querybuilder.ModelImpl.onDropDimension(oTarget, objectFound, true);
              // NOSONAR <createAssociation returns an association object created>
              const oAssociation = sap.cdw.ermodeler.ModelImpl.createAssociation(
                QualifiedClassNames.QUERY_ASSOCIATION,
                output,
                oTarget,
                model
              ) as any;
              oAssociation.name = elem;
              const oErCsnToModel = sap.cdw.ermodeler.ErCsnToModel.getInstance(true);
              oErCsnToModel.updateAssociation(oAssociation, associationToCreate, false);
            }
          }
        }
      },

      updatePushedAssociation(association, unresolvedAssocation) {
        // Update pushed association by renaming and creating right mappings
        // rename (Check later for i18n?)
        association.label = association?.target?.label ? `to ${association.target.label}` : association.label;
        const mappingLength = unresolvedAssocation?.joinSourceElements?.length;
        if (
          association?.target &&
          mappingLength > 0 &&
          unresolvedAssocation.joinTargetElementNames.length === mappingLength
        ) {
          const oClass = sap.galilei.model.getClass("sap.cdw.commonmodel.ElementMapping");
          if (oClass) {
            // Clear existing mapping that might have been default proposed
            association.mappings.deleteAll();
            for (let index = 0; index < mappingLength; index++) {
              const joinSourceOrigin = unresolvedAssocation.joinSourceElements[index];
              const joinSource = getProjectedElement(joinSourceOrigin);
              const joinTarget = findInCollectionByName(
                association.target.elements,
                unresolvedAssocation.joinTargetElementNames[index]
              );
              // Create and add mapping..
              if (joinSource && joinTarget) {
                const mapping = oClass.create(association.resource, {
                  source: joinSource,
                  target: joinTarget,
                });
                association.mappings.push(mapping);
              }
            }
          }
        }
      },
      pushAssociation(unresolvedAssociation, targetCsn, output, model) {
        let association: any;
        if (model && output && targetCsn?.csn) {
          let target = findInCollectionByName(model.dimensionNodes, unresolvedAssociation.target.name);
          if (target === undefined) {
            target = sap.cdw.querybuilder.ModelImpl.createObject(QualifiedClassNames.DIMENSION_NODE, targetCsn, model);
            sap.cdw.querybuilder.ModelImpl.onDropDimension(target, targetCsn, true);
          }
          if (target) {
            association = sap.cdw.ermodeler.ModelImpl.createAssociation(
              QualifiedClassNames.QUERY_ASSOCIATION,
              output,
              target,
              model,
              undefined,
              undefined,
              true
            );
            this.updatePushedAssociation(association, unresolvedAssociation);
            association.initiatedFrom = unresolvedAssociation.name;
          }
        }
        return association;
      },

      /**
       * Parse all model expressions
       * @param model galilei model
       */
      parseAllModelExpressions: async function (model): Promise<void> {
        if (!model || model.parsingExpressions) {
          return;
        }
        return nsLocal.ViewModelToCsn.parseAllModelExpressions(model);
      },

      // issue might be here for element length 0
      updateEntityFromCsn: async function (object, csn, model, skipChangeManagement?) {
        // model, service not needed here
        // galilei model, source(entity) and columns exist
        if (object && csn && csn.elements) {
          // Load unresolved associations..
          if (object.classDefinition?.name !== "Output" && skipChangeManagement !== true) {
            loadUnresolvedAssociations(object, csn);
            nsLocal.ModelImpl.requestAdjustDiagramsContent();
          }
          // Update output symbol (use 140 as minimum to show both parameters and association counters when available)
          if (object.classDefinition?.name === "Output") {
            const oSymbol = object?.relatedSymbols?.get(0);
            if (oSymbol) {
              const BBox = oSymbol.getBBox();
              // Update only when width less than 140
              if (BBox.width < 140) {
                BBox.width = 140;
                oSymbol.setBBox(BBox);
              }
            }
          }
          // after versiong restore ff is on, wo should always updateEntity
          const isTechnicalVersionRestoreEnabled =
            SupportedFeaturesService.getInstance().isTechnicalVersionRestoreEnabled();
          // Use model's ModificationDate to compare with each source entity's, if source entity is still old than model, do not check for change manangement
          if (
            !isTechnicalVersionRestoreEnabled &&
            object.modificationDate &&
            model.modificationDate &&
            object.modificationDate < model.modificationDate
          ) {
            // Only output's modificationDate == model.modificationDate
            return;
          }
          const postProcessings = [];
          const oCsnToModel = nsLocal.CsnToModel;

          // change management
          // - updated source business name
          // eslint-disable-next-line dot-notation
          window["performanceLogger"]?.enterMethod({
            name: `changeManagement-reporting-updatedSourceBn: ${object.name}`,
          });
          if (object.qualifiedClassName === "sap.cdw.querybuilder.Entity" && skipChangeManagement !== true) {
            // - business name
            if (object.label !== csn["@EndUserText.label"]) {
              const autofixedObjItem = this.getOrCreateChangeManagementAutofixObjectItem(model, object, object, false);
              autofixedObjItem.changeInfo.updatedBusinessNameSource = {
                old: { "@EndUserText.label": object.label, name: object.name },
                new: { "@EndUserText.label": csn["@EndUserText.label"], name: object.name },
              };
            }
          }
          // eslint-disable-next-line dot-notation
          window["performanceLogger"]?.leaveMethod({
            name: `changeManagement-reporting-updatedSourceBn: ${object.name}`,
          });

          // iterate columns
          const aElement2delete = [];
          const aElement2insert = Object.keys(csn.elements);
          const aElement2insertChangeManagement = [];
          // - merge element changes (see updateObjectUsingMapping)
          // - look out for new and deleted columns
          let successorBranch;
          // eslint-disable-next-line dot-notation
          window["performanceLogger"]?.enterMethod({
            name: `update Objects using mapping, elements(${object.elements.length})`,
          });
          for (let m = 0; m < object.elements.length; m++) {
            const element = object.elements.get(m);
            const sName = element && (element.newName || element.name);
            if (sName) {
              const elementCsn = csn.elements[sName];
              // Element has been deleted?
              if (elementCsn === undefined) {
                let elementIsUsed = false;
                // eslint-disable-next-line dot-notation
                window["performanceLogger"]?.enterMethod({ name: `getSuccessorsBranch(element): ${sName}` });
                successorBranch = successorBranch || nsLocal.NodeImpl.getSuccessorsBranch(object);
                // eslint-disable-next-line dot-notation
                window["performanceLogger"]?.leaveMethod({ name: `getSuccessorsBranch(element): ${sName}` });

                // exclude projection elements
                // eslint-disable-next-line dot-notation
                window["performanceLogger"]?.enterMethod({
                  name: `exclude proj. elem., successorBranch.forwardBranch (element): ${sName}`,
                });
                for (const forwardNode of successorBranch.forwardBranch) {
                  const isProjection = forwardNode.objectClassName === "Projection";
                  forwardNode.elements.forEach((forwardElement) => {
                    const sForwardName = forwardElement && (forwardElement.newName || forwardElement.name);
                    if (sName === sForwardName) {
                      if (isProjection && forwardElement.isRemoved) {
                        // projection and excluded -->> not 'used'
                      } else {
                        // not a projection or projection not excluded -->> 'used'
                        elementIsUsed = true;
                      }
                    }
                  });
                }
                // eslint-disable-next-line dot-notation
                window["performanceLogger"]?.leaveMethod({
                  name: `exclude proj. elem., successorBranch.forwardBranch (element): ${sName}`,
                });
                aElement2delete.push({
                  elementName: sName,
                  displayName: element.displayName, // needed, as element instance will be deleted, soon
                  element: element,
                  elementIsUsed: elementIsUsed,
                });
              } else if (oCsnToModel) {
                if (skipChangeManagement !== true) {
                  const ccPropagator = CompatibilityContractPropagator.instance();
                  if (ccPropagator.simpleCheckForFFAndStable(model?.output)) {
                    if (
                      object.qualifiedClassName === "sap.cdw.querybuilder.Entity" &&
                      element.dataType === elementCsn.type
                    ) {
                      // handle the scenario that dataType not change but only length/precision/scale change
                      // the reason why we need to put this check before Object.assign(element, elementCsn) is that
                      // elementCsn is using "type" property but element is using "dataType" property,
                      // so Object.assign(element, elementCsn) will not trigger dataType cascadeChange but trigger
                      // length/precision/scale cascadeChange
                      let propertyChanged = false;
                      if (elementCsn.hasOwnProperty("length") && element.length !== elementCsn.length) {
                        propertyChanged = true;
                      } else if (elementCsn.hasOwnProperty("precision") && element.precision !== elementCsn.precision) {
                        propertyChanged = true;
                      } else if (elementCsn.hasOwnProperty("scale") && element.scale !== elementCsn.scale) {
                        propertyChanged = true;
                      } else if (elementCsn.hasOwnProperty("srid") && element.srid !== elementCsn.srid) {
                        propertyChanged = true;
                      }
                      // add to change management
                      if (propertyChanged) {
                        const sourceElement = element.sourceElement;
                        if (!sourceElement) {
                          return;
                        }
                        const autofixedObjItem = this.getOrCreateChangeManagementAutofixObjectItem(
                          model,
                          sourceElement.container,
                          object,
                          false
                        );
                        autofixedObjItem.changeInfo.updatedDataTypeColumns.push({
                          elementName: sName,
                          element: element,
                          old: {
                            type: elementCsn.type,
                            length: elementCsn.length,
                            precision: elementCsn.precision,
                            scale: elementCsn.scale,
                            srid: elementCsn.srid,
                          },
                          new: {
                            type: element.dataType,
                            length: element.length,
                            precision: element.precision,
                            scale: element.scale,
                            srid: element.srid,
                          },
                        });
                      }
                      SourceToOutputChangesMonitor.instance().updateSourceElement(
                        object,
                        element,
                        ElementChangeType.elementDataType,
                        () => {
                          Object.assign(element, elementCsn);
                        },
                        this.oService
                      );
                    } else {
                      Object.assign(element, elementCsn);
                    }
                  }

                  // change management --- take care, must be processed before 'updateObjectUsingMapping'
                  // - Entity: updated properties (column)
                  // eslint-disable-next-line dot-notation
                  window["performanceLogger"]?.enterMethod({
                    name: `changeManagement - element properties (${sName})`,
                  });
                  if (object.qualifiedClassName === "sap.cdw.querybuilder.Entity") {
                    // Entity: updated primary key
                    if ((element.isKey && !elementCsn.key) || (!element.isKey && elementCsn.key)) {
                      // add to change management
                      const autofixedObjItem = this.getOrCreateChangeManagementAutofixObjectItem(
                        model,
                        element.sourceElement?.container,
                        object,
                        false
                      );
                      autofixedObjItem.changeInfo.updatedKeyColumns.push({
                        elementName: sName,
                        element: element,
                        old: { isKey: element.isKey },
                        new: { isKey: elementCsn.key },
                      });
                    }
                    // when compatibility contracts ff is enabled, the change management behavior is different
                    // at this points, the output will not be changed so here we need to check the source entity changes and then add to change management
                    const ccPropagator = CompatibilityContractPropagator.instance();
                    if (ccPropagator.simpleCheckForFFAndStable(model?.output)) {
                      const modifEntity = {
                        dataType: false,
                      };
                      // - data type
                      if (element.dataType !== elementCsn.type) {
                        modifEntity.dataType = true;
                      } else if (elementCsn.hasOwnProperty("length") && element.length !== elementCsn.length) {
                        modifEntity.dataType = true;
                      } else if (elementCsn.hasOwnProperty("precision") && element.precision !== elementCsn.precision) {
                        modifEntity.dataType = true;
                      } else if (elementCsn.hasOwnProperty("scale") && element.scale !== elementCsn.scale) {
                        modifEntity.dataType = true;
                      } else if (elementCsn.hasOwnProperty("srid") && element.srid !== elementCsn.srid) {
                        modifEntity.dataType = true;
                      }
                      // add to change management
                      if (modifEntity.dataType) {
                        const sourceElement = element.sourceElement;
                        if (!sourceElement) {
                          return;
                        }
                        const autofixedObjItem = this.getOrCreateChangeManagementAutofixObjectItem(
                          model,
                          sourceElement.container,
                          object,
                          false
                        );
                        autofixedObjItem.changeInfo.updatedDataTypeColumns.push({
                          elementName: sName,
                          element: element,
                          old: {
                            type: elementCsn.type,
                            length: elementCsn.length,
                            precision: elementCsn.precision,
                            scale: elementCsn.scale,
                            srid: elementCsn.srid,
                          },
                          new: {
                            type: element.dataType,
                            length: element.length,
                            precision: element.precision,
                            scale: element.scale,
                            srid: element.srid,
                          },
                        });
                      }
                    }
                  } else if (object.qualifiedClassName === "sap.cdw.querybuilder.Output") {
                    // - Output: updated properties (column)
                    const modifOutput = {
                      businessName: false,
                      dataType: false,
                      isKey: false,
                    };
                    // - business name
                    // in some case, one is undefined, another is "", they should be considered as the same
                    if ((element.label || "") !== (elementCsn["@EndUserText.label"] || "")) {
                      modifOutput.businessName = true;
                    }
                    // - updated primary key
                    if ((element.isKey && !elementCsn.key) || (!element.isKey && elementCsn.key)) {
                      modifOutput.isKey = true;
                    }
                    // - data type
                    if (element.dataType !== elementCsn.type) {
                      modifOutput.dataType = true;
                    } else if (elementCsn.hasOwnProperty("length") && element.length !== elementCsn.length) {
                      modifOutput.dataType = true;
                    } else if (elementCsn.hasOwnProperty("precision") && element.precision !== elementCsn.precision) {
                      modifOutput.dataType = true;
                    } else if (elementCsn.hasOwnProperty("scale") && element.scale !== elementCsn.scale) {
                      modifOutput.dataType = true;
                    } else if (elementCsn.hasOwnProperty("srid") && element.srid !== elementCsn.srid) {
                      modifOutput.dataType = true;
                    }
                    // add to change management
                    if (modifOutput.businessName || modifOutput.dataType || modifOutput.isKey) {
                      const sourceElement = element.sourceElement;
                      if (!sourceElement) {
                        return;
                      }
                      const autofixedObjItem = this.getOrCreateChangeManagementAutofixObjectItem(
                        model,
                        sourceElement.container,
                        object,
                        false
                      );
                      const added = autofixedObjItem.changeInfo.updatedBusinessNameColumns.map(
                        (elt) => elt.elementName
                      );
                      if (modifOutput.businessName && !added.includes(sName)) {
                        autofixedObjItem.changeInfo.updatedBusinessNameColumns.push({
                          elementName: sName,
                          element: element,
                          old: { label: elementCsn["@EndUserText.label"] },
                          new: { label: element.label },
                        });
                      }
                      if (modifOutput.dataType) {
                        autofixedObjItem.changeInfo.updatedDataTypeColumns.push({
                          elementName: sName,
                          element: element,
                          old: {
                            type: elementCsn.type,
                            length: elementCsn.length,
                            precision: elementCsn.precision,
                            scale: elementCsn.scale,
                            srid: elementCsn.srid,
                          },
                          new: {
                            type: element.dataType,
                            length: element.length,
                            precision: element.precision,
                            scale: element.scale,
                            srid: element.srid,
                          },
                        });
                      }
                      if (modifOutput.isKey) {
                        autofixedObjItem.changeInfo.updatedKeyColumns.push({
                          elementName: sName,
                          element: element,
                          old: { isKey: elementCsn.isKey },
                          new: { isKey: element.key },
                        });
                      }
                    }
                  }

                  SourceToOutputChangesMonitor.instance().updateSourceElement(
                    object,
                    element,
                    ElementChangeType.unknown,
                    () => {
                      // eslint-disable-next-line dot-notation
                      window["performanceLogger"]?.leaveMethod({
                        name: `changeManagement - element properties (${sName})`,
                      });
                      // Update element from CSN..
                      // Reset isKey, isDimension, isMeasure flags as it is updated from csn
                      //   - for 'isKey', only do it if there was a change
                      if ((element.isKey && !elementCsn.key) || (!element.isKey && elementCsn.key)) {
                        element.isKey = false;
                      }
                      if (element.isVisible !== !elementCsn["@Analytics.hidden"]) {
                        element.isVisible = !elementCsn["@Analytics.hidden"];
                      }
                      element.isDimension = false; // ToDo: Check if we need logic of isKey here, too
                      element.isMeasure = false; // ToDo: Check if we need logic of isKey here, too
                      // remove from objects to insert..
                      // eslint-disable-next-line dot-notation
                      window["performanceLogger"]?.enterMethod({
                        name: `remove from objects to insert, 'a2insert' - (element): ${sName}`,
                      });
                      const elementIndex = aElement2insert.indexOf(element.newName || element.name);
                      if (elementIndex > -1) {
                        aElement2insert.splice(elementIndex, 1);
                      }
                      // eslint-disable-next-line dot-notation
                      window["performanceLogger"]?.leaveMethod({
                        name: `remove from objects to insert, 'a2insert' - (element): ${sName}`,
                      });
                    },
                    this.oService
                  );
                }
                // eslint-disable-next-line dot-notation
                window["performanceLogger"]?.enterMethod({ name: `updateObjectUsingMapping(element): ${sName}` });
                SourceToOutputChangesMonitor.instance().updateSourceElement(
                  object,
                  element,
                  ElementChangeType.unknown,
                  () => {
                    postProcessings.push(oCsnToModel.updateObjectUsingMapping(elementCsn, element));
                  },
                  this.oService
                );

                // eslint-disable-next-line dot-notation
                window["performanceLogger"]?.leaveMethod({ name: `updateObjectUsingMapping(element): ${sName}` });
              }
            }
          }
          // eslint-disable-next-line dot-notation
          window["performanceLogger"]?.leaveMethod({
            name: `update Objects using mapping, elements(${object.elements.length})`,
          });
          // Delete missing elements..
          // eslint-disable-next-line dot-notation
          window["performanceLogger"]?.enterMethod({ name: `delete elements: ${object.name}` });
          for (let i = aElement2delete.length; i > 0; i--) {
            aElement2delete[i - 1].element.deleteObject();
          }
          // eslint-disable-next-line dot-notation
          window["performanceLogger"]?.leaveMethod({ name: `delete elements: ${object.name}` });
          // Add new elements..
          // eslint-disable-next-line dot-notation
          window["performanceLogger"]?.enterMethod({ name: `add new elements: ${object.name}` });
          let projectionNodeInfo, projectionNode;
          for (const elementName of aElement2insert) {
            const elementCsn = csn.elements[elementName];
            if (!elementCsn) {
              continue;
            }
            if (elementCsn.type !== CDSDataType.ASSOCIATION && !isAnalyticMeasureElement(elementCsn)) {
              // Create..
              const elementObject = nsLocal.ModelImpl.createObject(
                "sap.cdw.querybuilder.Element",
                {
                  name: elementName,
                },
                object
              );
              const ccPropagator = CompatibilityContractPropagator.instance();
              if (ccPropagator.simpleCheckForFFAndStable(model?.output)) {
                nsLocal.ModelImpl.setChangeManagementCurrentOperation(model, {
                  type: "newElement",
                  elementObject: elementObject,
                });
              }
              if (elementObject && oCsnToModel) {
                SourceToOutputChangesMonitor.instance().updateSourceElement(
                  object,
                  elementObject,
                  ElementChangeType.unknown,
                  () => {
                    Object.assign(elementObject, elementCsn);
                  },
                  this.oService
                );

                // Update..
                SourceToOutputChangesMonitor.instance().updateSourceElement(
                  object,
                  elementObject,
                  ElementChangeType.unknown,
                  () => {
                    postProcessings.push(oCsnToModel.updateObjectUsingMapping(elementCsn, elementObject));
                  },
                  this.oService
                );
              }
              // change management
              // eslint-disable-next-line dot-notation
              window["performanceLogger"]?.enterMethod({ name: "changeManagement-reporting-new source elements" });
              if (object.qualifiedClassName === "sap.cdw.querybuilder.Entity") {
                // do not just add new element(s) BUT include them into a projection (removed/excluded)
                //   a) entity (object) already has a successor of type projection
                //      -> add element and set to 'removed/excluded'
                //   b) entity (object) has not yet a successor of type projection
                //      -> create projection
                //      -> add element and set to 'removed/excluded'
                // change management infos for validation to create messages (->decorators)
                // only relevant for entity nodes
                // get/create projection
                projectionNodeInfo = projectionNodeInfo || this.getOrCreateSucceedingProjectionNode(object);
                projectionNode = projectionNode || projectionNodeInfo.node;
                let autofixElement;
                // exclude new columns (projection)
                for (let elementIndex = 0; elementIndex < projectionNode.elements.length; elementIndex++) {
                  const projectionNodeElement = projectionNode.elements.get(elementIndex);
                  if (projectionNodeElement.newName === elementName) {
                    projectionNodeElement.isRemoved = true; // exclude/hide column
                    autofixElement = projectionNodeElement;
                    break;
                  }
                }
                const ccPropagator = CompatibilityContractPropagator.instance();
                if (ccPropagator.simpleCheckForFFAndStable(model?.output)) {
                  nsLocal.ModelImpl.setChangeManagementCurrentOperation(model, undefined);
                }
                // update change-management infos
                const autofixedObjItem = this.getOrCreateChangeManagementAutofixObjectItem(
                  model,
                  object,
                  projectionNode,
                  projectionNodeInfo.isNew
                );
                // add autofix info
                autofixedObjItem.changeInfo.newColumns.push({
                  elementName: elementName,
                  element: elementObject,
                  autofixElement: autofixElement, // projection element
                });
                aElement2insertChangeManagement.push({
                  elementName: elementName,
                  element: elementObject,
                });
              }
              // eslint-disable-next-line dot-notation
              window["performanceLogger"]?.leaveMethod({ name: "changeManagement-reporting-new source elements" });
            }
          }
          // eslint-disable-next-line dot-notation
          window["performanceLogger"]?.leaveMethod({ name: `add new elements: ${object.name}` });

          // iterate parameters
          const aParameter2delete = [];
          const aParameter2insert = csn.params ? Object.keys(csn.params) : [];
          const aParameter2insertChangeManagement = [];
          const aParameter2updatedChangeManagement = [];
          // - parameter changes (see updateObjectUsingMapping)
          // - look out for new and deleted parameters
          for (let k = 0; k < object.parameters.length; k++) {
            const parameter = object.parameters.get(k);
            if (ObjectInstanceTypes.isParameter(parameter)) {
              const sName = parameter && (parameter.newName || parameter.name);
              if (sName) {
                const parameterCsn = csn.params[sName];
                // parameter has been deleted?
                if (parameterCsn === undefined) {
                  aParameter2delete.push({
                    parameterName: sName,
                    displayName: parameter.displayName, // needed, as parameter instance will be deleted, soon
                    parameter: parameter,
                  });
                } else if (oCsnToModel) {
                  // change management --- take care, must be processed before 'updateObjectUsingMapping'
                  const parameterIndex = aParameter2insert.indexOf(parameter.newName || parameter.name);
                  if (parameterIndex > -1) {
                    if (skipChangeManagement !== true) {
                      aParameter2insert.splice(parameterIndex, 1);
                      // changed data type
                      let modifParamDataType;
                      if (parameter.dataType !== parameterCsn.type) {
                        modifParamDataType = true;
                      } else if (parameterCsn.hasOwnProperty("length") && parameter.length !== parameterCsn.length) {
                        modifParamDataType = true;
                      } else if (
                        parameterCsn.hasOwnProperty("precision") &&
                        parameter.precision !== parameterCsn.precision
                      ) {
                        modifParamDataType = true;
                      } else if (parameterCsn.hasOwnProperty("scale") && parameter.scale !== parameterCsn.scale) {
                        modifParamDataType = true;
                      } else if (parameterCsn.hasOwnProperty("srid") && parameter.srid !== parameterCsn.srid) {
                        modifParamDataType = true;
                      }
                      if (modifParamDataType) {
                        aParameter2updatedChangeManagement.push({
                          parameterName: sName,
                          parameter: parameter,
                          old: {
                            type: parameter.dataType,
                            length: parameter.length,
                            precision: parameter.precision,
                            scale: parameter.scale,
                            srid: parameter.srid,
                          },
                          new: {
                            type: parameterCsn.type,
                            length: parameterCsn.length,
                            precision: parameterCsn.precision,
                            scale: parameterCsn.scale,
                            srid: parameterCsn.srid,
                          },
                        });
                      }
                    }
                    // update parameter
                    // eslint-disable-next-line dot-notation
                    window["performanceLogger"]?.enterMethod({
                      name: `updateObjectUsingMapping(parameter): ${sName}`,
                    });
                    SourceToOutputChangesMonitor.instance().updateSourceElement(
                      object,
                      parameter,
                      ElementChangeType.unknown,
                      () => {
                        postProcessings.push(oCsnToModel.updateObjectUsingMapping(parameterCsn, parameter));
                      },
                      this.oService
                    );

                    // eslint-disable-next-line dot-notation
                    window["performanceLogger"]?.leaveMethod({
                      name: `updateObjectUsingMapping(parameter): ${sName}`,
                    });
                  }
                }
              }
            } else if (ObjectInstanceTypes.isAnalyticParameter(parameter)) {
              // no update of model so far
            }
          }
          if (skipChangeManagement !== true) {
            // Create new parameters  --- "sap.cdw.commonmodel.AnalyticParameter" are skipped
            for (const parameterName of aParameter2insert) {
              const paramCsn = csn.params[parameterName];
              if (!paramCsn) {
                continue;
              }
              if (isAnalyticParameter(paramCsn)) {
                // no update of model so far
              } else {
                // Create..
                const oClass = sap.galilei.model.getClass("sap.cdw.querybuilder.ViewParameter");
                const paramData = {
                  // displayName:  ToDo: take care, needs to take UI setting BN/TN into account
                  name: parameterName,
                  dataType: paramCsn.dataType,
                  defaultValue: paramCsn.default, // take care, Galilei object also offers property 'default'
                };
                if (typeof paramCsn.default !== "undefined") {
                  // eslint-disable-next-line dot-notation
                  paramData["value"] = paramCsn.default;
                  // eslint-disable-next-line dot-notation
                  paramData["defaultValue"] = paramCsn.default;
                }
                // eslint-disable-next-line dot-notation
                if (typeof paramCsn.length !== "undefined") {
                  // eslint-disable-next-line dot-notation
                  paramData["length"] = paramCsn.length;
                }
                // eslint-disable-next-line dot-notation
                if (typeof paramCsn.precision !== "undefined") {
                  // eslint-disable-next-line dot-notation
                  paramData["precision"] = paramCsn.precision;
                }
                // eslint-disable-next-line dot-notation
                if (typeof paramCsn.scale !== "undefined") {
                  // eslint-disable-next-line dot-notation
                  paramData["scale"] = paramCsn.scale;
                }
                // eslint-disable-next-line dot-notation
                if (typeof paramCsn.srid !== "undefined") {
                  // eslint-disable-next-line dot-notation
                  paramData["srid"] = paramCsn.srid;
                } // srid (geo data n.a. in dropdown of parameters)
                const parameterObject = oClass.create(object.resource, paramData);
                if (parameterObject && oCsnToModel) {
                  SourceToOutputChangesMonitor.instance().updateSourceElement(
                    object,
                    parameterObject,
                    ElementChangeType.unknown,
                    () => {
                      Object.assign(parameterObject, paramCsn);
                      // Update..
                      postProcessings.push(oCsnToModel.updateObjectUsingMapping(paramCsn, parameterObject));
                    },
                    this.oService
                  );
                }
                object.parameters.push(parameterObject);
                // change management
                aParameter2insertChangeManagement.push({
                  parameterName: parameterName,
                  parameter: parameterObject,
                });
              }
            }
            // Delete missing parameters
            for (let i = aParameter2delete.length; i > 0; i--) {
              SourceToOutputChangesMonitor.instance().updateSourceElement(
                object,
                aParameter2delete[i - 1].parameter,
                ElementChangeType.unknown,
                () => {
                  aParameter2delete[i - 1].parameter.deleteObject();
                },
                this.oService
              );
            }
            // Create parameter mapping automatically based on default values
            // a) parameter is new and has a default value
            // b) parameter already exists, has defualt value but somehow has no mapping yet (i.e. default value added to IP later)
            (window as any).performanceLogger?.enterMethod({
              name: `create (auto-fix) input parameter mappings: ${object.name}`,
            });
            this.handleEntityParameterMappingChangesAutofix(object);
            (window as any).performanceLogger?.leaveMethod({
              name: `create (auto-fix) input parameter mappings: ${object.name}`,
            });
          }

          // Update Source from CSN
          if (oCsnToModel) {
            // eslint-disable-next-line dot-notation
            window["performanceLogger"]?.enterMethod({
              name: `update object (updateObjectUsingMapping): ${object.name}`,
            });
            oCsnToModel.updateObjectUsingMapping(csn, object);
            // eslint-disable-next-line dot-notation
            window["performanceLogger"]?.leaveMethod({
              name: `update object (updateObjectUsingMapping): ${object.name}`,
            });
          }

          if (skipChangeManagement !== true) {
            // eslint-disable-next-line dot-notation
            window["performanceLogger"]?.enterMethod({ name: `requestAdjustDiagramsContent: ${object.name}` });
            nsLocal.ModelImpl.requestAdjustDiagramsContent(); // see 'this.onCreateIntermediateNode' (called with bSkipAdjustToContent = true), function getOrCreateSucceedingProjectionNode (around line 835)
            // eslint-disable-next-line dot-notation
            window["performanceLogger"]?.leaveMethod({ name: `requestAdjustDiagramsContent: ${object.name}` });

            // post processing elements (actions that need to be done when all elements are loaded)
            // Process post actions of elements (columnLabel for instance)
            const csnToModel = sap.cdw.commonmodel.CsnToModel.getInstance();
            const length = postProcessings ? postProcessings.length : 0;
            // eslint-disable-next-line dot-notation
            window["performanceLogger"]?.enterMethod({ name: `postProcessElement(${length}): ${object.name}` });
            for (let i = 0; i < length; i++) {
              SourceToOutputChangesMonitor.instance().updateSourceElement(
                object,
                postProcessings[i].object,
                ElementChangeType.unknown,
                () => {
                  csnToModel.postProcessElement(object, postProcessings[i].object, postProcessings[i].postProcessings);
                },
                this.oService
              );
            }
            // eslint-disable-next-line dot-notation
            window["performanceLogger"]?.leaveMethod({ name: `postProcessElement(${length}): ${object.name}` });

            // make change management info accessible for later use (validation messages, decorators)
            // eslint-disable-next-line dot-notation
            window["performanceLogger"]?.enterMethod({
              name: `changeManagement-reporting-new/deleted columns: ${object.name}`,
            });
            if (object.qualifiedClassName === "sap.cdw.querybuilder.Entity") {
              if (
                aElement2insertChangeManagement.length > 0 ||
                aElement2delete.length > 0 ||
                aParameter2insertChangeManagement.length > 0 ||
                aParameter2delete.length > 0 ||
                aParameter2updatedChangeManagement.length > 0
              ) {
                const autofixedObjItem = this.getOrCreateChangeManagementAutofixObjectItem(
                  model,
                  object,
                  object,
                  false
                );
                autofixedObjItem.changeInfo.newColumns = aElement2insertChangeManagement;
                autofixedObjItem.changeInfo.deletedColumns = aElement2delete;
                autofixedObjItem.changeInfo.newParameters = aParameter2insertChangeManagement;
                autofixedObjItem.changeInfo.deletedParameters = aParameter2delete;
                autofixedObjItem.changeInfo.updatedParameters = aParameter2updatedChangeManagement;
              }
            }
            // eslint-disable-next-line dot-notation
            window["performanceLogger"]?.leaveMethod({
              name: `changeManagement-reporting-new/deleted columns: ${object.name}`,
            });
          }
        }
        return;
      },

      handleEntityParameterMappingChangesAutofix: function (object) {
        const parameters = object.parameters; // only input parameters are auto-fixed here -> analytical parameters, see oEntity.analyticParameters
        //               csnToModel.getDefaultPropertyMapping(newTargetViewParameter, param.sourceParameter);

        if (parameters) {
          const unMappedParametersWithDefaultValue = parameters.filter(
            (param) =>
              param.targetParameter === undefined &&
              param.value === undefined &&
              typeof param.defaultValue !== "undefined"
          );
          for (const param of unMappedParametersWithDefaultValue) {
            if (typeof param.defaultValue !== "undefined") {
              param.value = param.defaultValue;
            }
          }
        }
      },

      handleExpressionColumnDatatypeChangesAutofix: function (model) {
        const calculatedElementsNodes = model.nodes.filter(
          (node) => node.qualifiedClassName === "sap.cdw.querybuilder.CalculatedElements"
        );
        const filterNodes = model.nodes.filter((node) => node.qualifiedClassName === "sap.cdw.querybuilder.Filter");
        const aggregatedElementsNodes = model.nodes.filter(
          (node) => node.qualifiedClassName === "sap.cdw.querybuilder.AggregatedElements"
        );
        const addNextLevelExpressionArgs = (parsedExpression, args) => {
          if (parsedExpression?.args) {
            const argsArray = nsLocal.ParsedExpressionImpl.getArgsArray(parsedExpression.args);
            for (const arg of argsArray) {
              args.push(arg);
            }
          }
          if (parsedExpression?.xpr) {
            for (const xpr of parsedExpression.xpr) {
              args = addNextLevelExpressionArgs(xpr, args);
            }
          }
          return args;
        };
        const fnChgMgmn = () => {
          // eslint-disable-next-line dot-notation
          window["performanceLogger"]?.enterMethod({ name: "changeManagement-reporting-expressions" });
          // calculated (expression on element level)
          calculatedElementsNodes.forEach((calcNode) => {
            const calculatedElements = calcNode.orderedElements || calcNode.filteredOrderedElements;
            calculatedElements.forEach((calcElement) => {
              if (calcElement?.isCalculated) {
                const args = addNextLevelExpressionArgs(calcElement.parsedExpression?.expression, []);
                args.forEach((arg) => {
                  if (arg.refElement?.qualifiedClassName === "sap.cdw.querybuilder.ViewParameter") {
                    this.addSourceParameterChangeToChangeManagement(model, calcElement, arg.refElement);
                  } else if (arg.refElement?.qualifiedClassName === "sap.cdw.commonmodel.AnalyticParameter") {
                    // may be added with future story
                    // this.addSourceParameterChangeToChangeManagement(model, aggregatedNode, arg.refElement);
                  } else {
                    this.addSourceElementChangeToChangeManagement(model, calcElement, arg.refElement);
                  }
                });

                if (
                  calcElement.currencyConversion &&
                  (calcElement.precisionsTable === undefined ||
                    calcElement.configurationTable === undefined ||
                    calcElement.prefactorsTable === undefined ||
                    calcElement.ratesTable === undefined ||
                    calcElement.notationsTable === undefined ||
                    calcElement.conversionCodesTable === undefined)
                ) {
                  calcElement.resource.applyUndoableAction(
                    function () {
                      const oldValue = calcElement.resource.isLoading;
                      calcElement.resource.isLoading = true;
                      if (calcElement.precisionsTable === undefined) {
                        calcElement.precisionsTable = "TCURX";
                      }
                      if (calcElement.configurationTable === undefined) {
                        calcElement.configurationTable = "TCURV";
                      }
                      if (calcElement.prefactorsTable === undefined) {
                        calcElement.prefactorsTable = "TCURF";
                      }
                      if (calcElement.ratesTable === undefined) {
                        calcElement.ratesTable = "TCURR";
                      }
                      if (calcElement.notationsTable === undefined) {
                        calcElement.notationsTable = "TCURN";
                      }
                      if (calcElement.conversionCodesTable === undefined) {
                        calcElement.conversionCodesTable = "TCURC";
                      }
                      calcElement.resource.isLoading = oldValue;
                    },
                    "Update conversion tables on old currency conversion column",
                    true /* Protect from Undo */
                  );
                }
              }
            });
          });
          // filter (expression on node level)
          filterNodes.forEach((filterNode) => {
            const parsedExpression = filterNode.parsedExpression;
            if (parsedExpression) {
              const args = addNextLevelExpressionArgs(parsedExpression.expression, []);
              args.forEach((arg) => {
                if (arg.refElement?.qualifiedClassName === "sap.cdw.querybuilder.ViewParameter") {
                  this.addSourceParameterChangeToChangeManagement(model, filterNode, arg.refElement);
                } else if (arg.refElement?.qualifiedClassName === "sap.cdw.commonmodel.AnalyticParameter") {
                  // may be added with future story
                  // this.addSourceParameterChangeToChangeManagement(model, aggregatedNode, arg.refElement);
                } else {
                  this.addSourceElementChangeToChangeManagement(model, filterNode, arg.refElement);
                }
              });
            }
          });
          // aggregated (expression on node level)
          aggregatedElementsNodes.forEach((aggregatedNode) => {
            const parsedExpression = aggregatedNode.parsedExpression;
            if (parsedExpression) {
              const args = addNextLevelExpressionArgs(parsedExpression.expression, []);
              args.forEach((arg) => {
                if (arg.refElement?.qualifiedClassName === "sap.cdw.querybuilder.ViewParameter") {
                  this.addSourceParameterChangeToChangeManagement(model, aggregatedNode, arg.refElement);
                } else if (arg.refElement?.qualifiedClassName === "sap.cdw.commonmodel.AnalyticParameter") {
                  // may be added with future story
                  // this.addSourceParameterChangeToChangeManagement(model, aggregatedNode, arg.refElement);
                } else {
                  this.addSourceElementChangeToChangeManagement(model, aggregatedNode, arg.refElement);
                }
              });
            }
          });
          // eslint-disable-next-line dot-notation
          window["performanceLogger"]?.leaveMethod({ name: "changeManagement-reporting-expressions" });
        };

        return this.parseAllModelExpressions(model).then(fnChgMgmn);
      },

      handleOutputChangesAutofix: function (object, csn, model, oService?) {
        // galilei model, source(entity) and columns exist
        if (object && csn && csn.elements) {
          const oCsnToModel = nsLocal.CsnToModel;
          const a2insert = Object.keys(csn.elements);
          const a2delete = [];
          let isVersioningRestoreMode = false;
          const isTechnicalVersionRestoreEnabled =
            SupportedFeaturesService.getInstance().isTechnicalVersionRestoreEnabled();
          if (isTechnicalVersionRestoreEnabled && getIsVersioningRestoreMode(oService)) {
            isVersioningRestoreMode = true;
          }
          // change management
          // eslint-disable-next-line dot-notation
          window["performanceLogger"]?.enterMethod({ name: "changeManagement-reporting-output" });
          // - look out for deleted associations
          if (!object?.resource?.model?._isSqlEditorModel) {
            for (const elementCsnKey in csn.elements) {
              const elementCsn = csn.elements[elementCsnKey];
              let elementCsnKeyWithoutAlias = elementCsnKey;
              if (elementCsn.type !== "cds.Association") {
                // check if column has been deleted
                let elementExists = false;
                if (isVersioningRestoreMode) {
                  for (const el of object.elements.toArray()) {
                    if (el.newName === elementCsnKey) {
                      // column exists
                      elementExists = true;
                      break;
                    }
                  }
                } else {
                  for (const el of object.elements.toArray()) {
                    if (el.newName === elementCsnKey || el.name === elementCsnKey) {
                      // column exists
                      elementExists = true;
                      break;
                    }
                  }
                }
                if (!elementExists) {
                  // elementCsnKey can have an alias
                  if (csn?.query?.SELECT?.columns) {
                    for (const column of csn.query.SELECT.columns) {
                      if (column?.as === elementCsnKey) {
                        if (column.ref) {
                          // calculated column -> arcs/func
                          elementCsnKeyWithoutAlias = column.ref[column.ref.length - 1];
                        }
                      }
                    }
                  }
                  for (const el of object.elements.toArray()) {
                    if (!el.sourceCalculatedElement && !el.sourceAggregatedElement) {
                      // calculated/filter columns are not target of ch
                      if (el.newName === elementCsnKeyWithoutAlias || el.name === elementCsnKeyWithoutAlias) {
                        // column exists
                        elementExists = true;
                        break;
                      }
                    }
                  }
                }
                if (!elementExists) {
                  // used column deleted from output (unused columns cannot be found on output node)
                  const modifiedObject = this.findModifiedObjectOfDeletedOutputColumn(model, elementCsnKeyWithoutAlias);
                  const autofixedObjItem = this.getOrCreateChangeManagementAutofixObjectItem(
                    model,
                    modifiedObject,
                    object,
                    false
                  );
                  const deletedColumn = {
                    elementName: elementCsnKeyWithoutAlias,
                    displayName: elementCsn["@EndUserText.label"] || elementCsnKeyWithoutAlias,
                    elementIsUsed: true, // not relevant for output node
                  };
                  autofixedObjItem.changeInfo.deletedColumns.push(deletedColumn);
                  a2delete.push(deletedColumn);
                  const isTechnicalVersionRestoreEnabled =
                    SupportedFeaturesService.getInstance().isTechnicalVersionRestoreEnabled();
                  if (isTechnicalVersionRestoreEnabled && getIsVersioningRestoreMode(oService)) {
                    // after versioning restore ff is on, we have new scenario that user restore to
                    // and old version that just contain fewer elments which will break dependents,
                    // so we need load impact here
                    object.loadExternalImpact({
                      onlyElements: [elementCsnKey],
                    });
                  }
                }
              }
              // remove from objects to insert..
              const elementIndex = a2insert.indexOf(elementCsnKey);
              if (elementIndex > -1) {
                a2insert.splice(elementIndex, 1);
              }
            }

            // element changes
            object.elements.forEach((element) => {
              const sName = element && (element.newName || element.name);
              if (sName) {
                const elementCsn = csn.elements[sName];
                // Element is new?
                if (oCsnToModel) {
                  // change management --- take care, must be processed before 'updateObjectUsingMapping'
                  // - updated properties (column)
                  if (object.qualifiedClassName === "sap.cdw.querybuilder.Output") {
                    const modifOutput = {
                      businessName: false,
                      dataType: false,
                      isKey: false,
                    };
                    // - business name
                    // in some case, one is undefined, another is "", they should be considered as the same
                    if (elementCsn && (element.label || "") !== (elementCsn["@EndUserText.label"] || "")) {
                      modifOutput.businessName = true;
                    }
                    // - updated primary key
                    if (
                      (elementCsn && element.isKey && !elementCsn.key) ||
                      (elementCsn && !element.isKey && elementCsn.key)
                    ) {
                      modifOutput.isKey = true;
                    }
                    // - data type
                    if (elementCsn && element.dataType !== elementCsn.type) {
                      modifOutput.dataType = true;
                    } else if (
                      elementCsn &&
                      elementCsn.hasOwnProperty("length") &&
                      element.length !== elementCsn.length
                    ) {
                      modifOutput.dataType = true;
                    } else if (
                      elementCsn &&
                      elementCsn.hasOwnProperty("precision") &&
                      element.precision !== elementCsn.precision
                    ) {
                      modifOutput.dataType = true;
                    } else if (elementCsn && elementCsn.hasOwnProperty("scale") && element.scale !== elementCsn.scale) {
                      modifOutput.dataType = true;
                    } else if (elementCsn && elementCsn.hasOwnProperty("srid") && element.srid !== elementCsn.srid) {
                      modifOutput.dataType = true;
                    }

                    const isTechnicalVersionRestoreEnabled =
                      SupportedFeaturesService.getInstance().isTechnicalVersionRestoreEnabled();
                    if (
                      modifOutput.dataType &&
                      isTechnicalVersionRestoreEnabled &&
                      getIsVersioningRestoreMode(oService)
                    ) {
                      // after versioning restore ff is on, we have new scenario that user restore to
                      // and old version that just contain fewer elments which will break dependents,
                      // so we need load impact here
                      object.loadExternalImpact({
                        onlyElements: [sName],
                      });
                    }
                    // add to change management
                    if (modifOutput.businessName || modifOutput.dataType || modifOutput.isKey) {
                      let autofixedObjItemLocal;
                      if (
                        SupportedFeaturesService.getInstance().isTechnicalVersionRestoreEnabled() &&
                        getIsVersioningRestoreMode(oService)
                      ) {
                        const sourceChangeOrigin =
                          SourceToOutputChangesMonitor.instance().getSourceChangeOrigin(element);
                        autofixedObjItemLocal = this.getOrCreateChangeManagementAutofixObjectItem(
                          model,
                          sourceChangeOrigin || element.container,
                          object,
                          false
                        );
                      } else {
                        autofixedObjItemLocal = this.getOrCreateChangeManagementAutofixObjectItem(
                          model,
                          element.sourceElement?.container,
                          object,
                          false
                        );
                      }
                      const added = autofixedObjItemLocal.changeInfo.updatedBusinessNameColumns.map(
                        (elt) => elt.elementName
                      );
                      if (elementCsn && modifOutput.businessName && !added.includes(sName)) {
                        autofixedObjItemLocal.changeInfo.updatedBusinessNameColumns.push({
                          elementName: sName,
                          element: element,
                          old: { label: elementCsn["@EndUserText.label"] },
                          new: { label: element.label },
                        });
                      }
                      if (elementCsn && modifOutput.dataType) {
                        autofixedObjItemLocal.changeInfo.updatedDataTypeColumns.push({
                          elementName: sName,
                          element: element,
                          old: {
                            type: elementCsn.type,
                            length: elementCsn.length,
                            precision: elementCsn.precision,
                            scale: elementCsn.scale,
                            srid: elementCsn.srid,
                          },
                          new: {
                            type: element.dataType,
                            length: element.length,
                            precision: element.precision,
                            scale: element.scale,
                            srid: element.srid,
                          },
                        });
                      }
                      if (elementCsn && modifOutput.isKey) {
                        autofixedObjItemLocal.changeInfo.updatedKeyColumns.push({
                          elementName: sName,
                          element: element,
                          old: { isKey: elementCsn.key },
                          new: { isKey: element.isKey },
                        });
                      }
                    }
                  }
                  // remove from objects to insert..
                  const elementIndex = a2insert.indexOf(element.newName || element.name);
                  if (elementIndex > -1) {
                    a2insert.splice(elementIndex, 1);
                  }
                }
              }
            });
          }

          // DAC changes
          const dacs = csn[CsnAnnotations.DataWarehouse.dataAccessControl_usage];
          if (dacs?.length > 0) {
            const viewDataAccessControls = object.viewDataAccessControls;
            for (const dac of dacs) {
              const viewDataAccessControl = findInCollectionByName(viewDataAccessControls, dac.target);
              if (viewDataAccessControl) {
                // DAC still exists
                // evaluate the mappings
                // - output element deleted
                const outputElementsDeleted = [];
                for (const dacOnRef of dac.on) {
                  if (dacOnRef.ref) {
                    // skip "=" / "and"
                    if (dacOnRef.ref[0] === dac.target) {
                      // skip, belongs to DAC but we don't have elements at hand here (see below)
                    } else {
                      // output node
                      let sourceElementFound = false;
                      if (dacOnRef.ref.length === 1) {
                        // check if output node element still exists
                        for (let j = 0; j < object.elements.length; j++) {
                          const element = object.elements.get(j);
                          if (element.name === dacOnRef.ref[0] || element.newName === dacOnRef.ref[0]) {
                            sourceElementFound = true;
                            break;
                          }
                        }
                        if (!sourceElementFound) {
                          outputElementsDeleted.push(dacOnRef.ref[0]);
                        }
                        // SQL editor deleted output columns, we dont remove columns in the output node, hence this check
                        const objectCount = object?.resource?.model?.changeManagement?.modifiedObjects?.length;
                        if (
                          object?.resource?.model?._isSqlEditorModel &&
                          object?.resource?.model?.output &&
                          objectCount
                        ) {
                          const autofixedObjItem = this.getOrCreateChangeManagementAutofixObjectItem(
                            model,
                            object.resource.model.output,
                            object.resource.model.output
                          );
                          if (
                            autofixedObjItem.changeInfo.deletedOutputColumnNames?.length &&
                            autofixedObjItem.changeInfo.deletedOutputColumnNames.indexOf(dacOnRef.ref[0]) !== -1
                          ) {
                            outputElementsDeleted.push(dacOnRef.ref[0]);
                          }
                        }
                        // change management:
                        // - we already inform the user about element changes
                        // - there is already an error message displayed on detail of DAC (mapping) but ONLY if you drill down !!!
                      } else {
                        // not possible, Output properties are belonging to current model ('ref' has always length === 1)
                      }
                    }
                  }
                }
                const dacElementsDeleted = [];
                const dacElementsNew = [];
                const spaceName = object.rootContainer.spaceId;
                const dacName = dac.target;
                this.increaseChangeManagementPendingCounter(model, "DAC");
                Repo.getDataAccessControlDetails(spaceName, dacName, ["csn"])
                  .then((prop) => {
                    const dacCsn = prop?.csn?.definitions[dac.target];
                    if (dacCsn?.elements) {
                      // check if element still exists on DAC's csn
                      for (const dacOnRef of dac.on) {
                        // mappings
                        if (dacOnRef.ref) {
                          // skip "=" / "and"
                          if (dacOnRef.ref[0] === dac.target) {
                            if (!dacCsn.elements[dacOnRef.ref[1]]) {
                              dacElementsDeleted.push(dacOnRef.ref[1]);
                              // to improve severity (warning/error), we could check here, if mapped source element still exists
                            }
                          } else {
                            // output element
                          }
                        }
                      }
                      // check if DAC's csn has new elements
                      // eslint-disable-next-line @typescript-eslint/prefer-for-of
                      for (const element in dacCsn.elements) {
                        let dacElementIsNew = true;
                        for (const dacOnRef of dac.on) {
                          if (dacOnRef.ref) {
                            // skip "=" / "and"
                            if (dacOnRef.ref[0] === dac.target) {
                              // DAC element
                              if (dacOnRef.ref[1] === element) {
                                dacElementIsNew = false;
                              }
                            }
                          }
                        }
                        if (dacElementIsNew) {
                          dacElementsNew.push(element);
                        }
                      }
                    }
                    if (dacElementsDeleted.length > 0 || dacElementsNew.length > 0) {
                      const autofixedObjItem = this.getOrCreateChangeManagementAutofixObjectItem(
                        model,
                        viewDataAccessControl,
                        object,
                        false
                      );
                      autofixedObjItem.changeInfo.updatedDacMappings =
                        autofixedObjItem.changeInfo.updatedDacMappings.map((updatedDacMapping) => {
                          if (updatedDacMapping.dacObject.name === viewDataAccessControl.name) {
                            if (outputElementsDeleted.length > 0) {
                              return {
                                dacObject: viewDataAccessControl,
                                reason: "outputAndDacElementChanges",
                                isError: false,
                                isWarning: true,
                              }; // pressing "deploy" leads to error if user does not drill into DAC before (no auto-correct at loading time)
                              // return { dacObject: viewDataAccessControl, reason: "outputAndDacElementChanges", isError: false, isWarning: (dacElementsNew.length > 0) ? true : false };
                            } else {
                              return {
                                dacObject: viewDataAccessControl,
                                reason: "dacChanges",
                                isError: false,
                                isWarning: true,
                              }; // pressing "deploy" leads to error if user does not drill into DAC before (no auto-correct at loading time)
                              // return { dacObject: viewDataAccessControl, reason: "dacChanges", isError: false, isWarning: (dacElementsNew.length > 0) ? true : false };
                            }
                          }
                        });
                      const updatedDacMappingExists = autofixedObjItem.changeInfo.updatedDacMappings.filter(
                        (dacMapping) => dacMapping.dacObject.name === viewDataAccessControl.name
                      );
                      if (updatedDacMappingExists?.length === 0) {
                        autofixedObjItem.changeInfo.updatedDacMappings.push({
                          dacObject: viewDataAccessControl,
                          reason: "dacChanges",
                          isError: false,
                          isWarning: true,
                        });
                        // autofixedObjItem.changeInfo.updatedDacMappings.push({ dacObject: viewDataAccessControl, reason: "dacChanges", isError: false, isWarning: (dacElementsNew.length > 0) ? true : false });
                      }
                      // re-run validation
                      model.validate();
                    }
                    this.decreaseChangeManagementPendingCounter(model, "DAC");
                  })
                  .catch(() => {
                    //
                  });
                if (outputElementsDeleted.length > 0) {
                  // output element deletions lead to unmapped DAC elements (at least if corresponding element still exists on DAC)
                  const autofixedObjItem = this.getOrCreateChangeManagementAutofixObjectItem(
                    model,
                    object,
                    viewDataAccessControl,
                    false
                  );
                  const dacMappings = autofixedObjItem.changeInfo.updatedDacMappings.filter(
                    (dacMapping) => dacMapping.name === viewDataAccessControl.name
                  );
                  if (dacMappings.length === 0) {
                    autofixedObjItem.changeInfo.updatedDacMappings.push({
                      dacObject: viewDataAccessControl,
                      reason: "outputElementChanges",
                      isError: false,
                      isWarning: true,
                    });
                  }
                }
                viewDataAccessControl.csn = dac;
              }
            }
          }

          // associations
          // - mapping
          // - parts of the mapping can break --> all broken lines are automatically removed
          // - each lines contains at least on deleted column -> the whole association is removed
          /* for (const elementCsnKey in csn.elements) {       // deactivated, because assertion changes are not relevant to change management (UX)
            const elementCsn = csn.elements[elementCsnKey];
            if (elementCsn.type === "cds.Association") {
              // find association (output)
              const updatedAssociationObject = object.associations.filter((assoc) => assoc.name === elementCsnKey)[0];
              if (updatedAssociationObject) {
                let sourceColumnDeleted;
                for (const mappingPart of elementCsn.on) {
                  // check if the mapping still exists
                  if (mappingPart.ref && mappingPart.ref.length === 1) { // source
                    sourceColumnDeleted = false;
                    // output column (source column of mapping, left)
                    for (const deletedColumn of a2delete) {
                      if (deletedColumn.elementName === mappingPart.ref[0]) {
                        const modifiedObject = object;
                        const autofixedObjItem = this.getOrCreateChangeManagementAutofixObjectItem(model, modifiedObject, updatedAssociationObject, false);
                        const updatedAssociationFiltered = autofixedObjItem.changeInfo.updatedAssociations.filter((assoc) => assoc.elementName === elementCsnKey);
                        let updatedAssociation;
                        if (updatedAssociationFiltered.length === 0) {
                          updatedAssociation = {
                            association: updatedAssociationObject,
                            elementName: elementCsnKey,
                            displayName: elementCsn["@EndUserText.label"] || elementCsnKey,
                            deletedMappings: [],
                          };
                          autofixedObjItem.changeInfo.updatedAssociations.push(updatedAssociation);
                        } else {
                          updatedAssociation = updatedAssociationFiltered[0];
                        }
                        // add mapping
                        updatedAssociation.deletedMappings.push({
                          isSourceColumn: true,
                          isTargetColumn: false,
                          elementName: deletedColumn.elementName,
                          displayName: deletedColumn.displayName,
                        });
                        sourceColumnDeleted = true;
                      }
                    }
                  }
                  else if (mappingPart.ref && mappingPart.ref.length === 2) { // target
                    // 'external' column (target column of mapping, right)
                    if (!sourceColumnDeleted) {
                      const elementName = mappingPart.ref[1];
                      let mappingExists;
                      updatedAssociationObject.mappings.forEach(mapping => {
                        const eltTarget = mapping.target;
                        const targetElementName = eltTarget.newName || eltTarget.name;
                        if (targetElementName === elementName) {
                          mappingExists = true;
                        }
                      });
                      if (!mappingExists) {
                        const modifiedObject = object;  // external source (diagram has no symbol) -> take output object instead
                        const autofixedObjItem = this.getOrCreateChangeManagementAutofixObjectItem(model, modifiedObject, updatedAssociationObject, false);
                        const updatedAssociationFiltered = autofixedObjItem.changeInfo.updatedAssociations.filter((assoc) => assoc.elementName === elementCsnKey);
                        let updatedAssociation;
                        if (updatedAssociationFiltered.length === 0) {
                          updatedAssociation = {
                            association: updatedAssociationObject,
                            elementName: elementCsnKey,
                            displayName: elementCsn["@EndUserText.label"] || elementCsnKey,
                            deletedMappings: [],
                          };
                          autofixedObjItem.changeInfo.updatedAssociations.push(updatedAssociation);
                        } else {
                          updatedAssociation = updatedAssociationFiltered[0];
                        }
                        // add mapping
                        updatedAssociation.deletedMappings.push({
                          isSourceColumn: false,
                          isTargetColumn: true,
                          elementName: elementName,
                          displayName: elementName,
                        });
                      }
                    }
                  }
                }
              }
            }
          } */
          // - deletions
          // - look out for deleted associations
          /* for (const elementCsnKey in csn.elements) {       // deactivated, because assertion changes are not relevant to change management (UX)
            const elementCsn = csn.elements[elementCsnKey];
            if (elementCsn.type === "cds.Association") {
              const updatedAssociationObject = object.associations.filter((assoc) => assoc.name === elementCsnKey)[0];
              if (!updatedAssociationObject) {
                const modifiedObject = object;
                const autofixedObjItem = this.getOrCreateChangeManagementAutofixObjectItem(model, modifiedObject, object, false);
                autofixedObjItem.changeInfo.deletedAssociations.push({
                  elementName: elementCsnKey,
                  displayName: elementCsn["@EndUserText.label"] || elementCsnKey,
                });
              }
            }
          } */
          // eslint-disable-next-line dot-notation
          window["performanceLogger"]?.leaveMethod({ name: "changeManagement-reporting-output" });
        }
        return;
      },

      hasAutofixChanges: function (model) {
        if (model.changeManagement?.modifiedObjects?.length > 0) {
          return true;
        }
        return false;
      },

      /**
       * Increase counter of pending asynchronous change management service call (show popup only after all service calls finished)
       * @param model Galilei model
       * @param counterId Id of the counter for pending service calls
       */
      increaseChangeManagementPendingCounter: function (model, counterId) {
        if (!model.changeManagement) {
          model.changeManagement = {
            modifiedObjects: [],
            pendingCounters: {},
          };
        } else if (!model.changeManagement.pendingCounters) {
          model.changeManagement.pendingCounters = {};
        }
        if (model.changeManagement.pendingCounters[counterId]) {
          model.changeManagement.pendingCounters[counterId] += 1;
        } else {
          model.changeManagement.pendingCounters[counterId] = 1;
        }
      },

      /**
       * Decrease counter of pending asynchronous change management service call (show popup only after all service calls finished)
       * @param model Galilei model
       * @param counterId Id of the counter for pending service calls
       */
      decreaseChangeManagementPendingCounter: function (model, counterId) {
        if (!model.changeManagement) {
          model.changeManagement = {
            modifiedObjects: [],
            pendingCounters: {},
          };
        } else if (!model.changeManagement.pendingCounters) {
          model.changeManagement.pendingCounters = {};
        }
        if (
          model.changeManagement.pendingCounters[counterId] &&
          model.changeManagement.pendingCounters[counterId] > 0
        ) {
          model.changeManagement.pendingCounters[counterId] -= 1;
        } else {
          model.changeManagement.pendingCounters[counterId] = 0;
        }
      },

      setChangeManagementCurrentOperation: function (model, currentOperation) {
        if (!model.changeManagement) {
          model.changeManagement = { modifiedObjects: [] };
        }
        model.changeManagement.currentOperation = currentOperation;
      },

      /**
       * Get/create change management item instance to add change management info
       * @param model Galilei model
       * @param modifiedObject Source object which has been changed
       * @param autofixedObject Object which has got changed by means of propagation
       * @param autofixedObjectIsNew Object is new (has been created by means of propagation -> see projection object
       */
      getOrCreateChangeManagementAutofixObjectItem: function (
        model,
        modifiedObject,
        autofixedObject,
        autofixedObjectIsNew
      ) {
        if (!model.changeManagement) {
          model.changeManagement = { modifiedObjects: [] };
        } else if (!model.changeManagement.modifiedObjects) {
          model.changeManagement.modifiedObjects = [];
        }
        // look out for existing change infos of object?
        let modifiedObjItem;
        for (const modifItem of model.changeManagement.modifiedObjects) {
          if (modifItem.modifiedObject === modifiedObject) {
            modifiedObjItem = modifItem;
            break;
          }
        }
        if (!modifiedObjItem) {
          modifiedObjItem = {
            modifiedObject: modifiedObject,
            autofixedObjects: [],
          };
          model.changeManagement.modifiedObjects.push(modifiedObjItem);
        }
        // look out for existing autofix object(operation)?
        let autofixedObjItem;
        for (const autofixItem of modifiedObjItem.autofixedObjects) {
          if (autofixItem.autofixedObject === autofixedObject) {
            autofixedObjItem = autofixItem;
            break;
          }
        }
        if (!autofixedObjItem) {
          autofixedObjItem = {
            autofixedObject: autofixedObject, // adjusted by view builder
            autofixedObjectIsNew: autofixedObjectIsNew,
            changeInfo: {
              newColumns: [],
              newParameters: [],
              updatedBusinessNameColumns: [],
              updatedDataTypeColumns: [],
              updatedParameters: [],
              updatedKeyColumns: [],
              // updatedAssociations: [],       // deactivated, because assertion changes are not relevant to change management (UX)
              updatedDacMappings: [],
              deletedSources: [],
              deletedColumns: [],
              deletedParameters: [],
              deletedOutputColumnNames: [],
              // deletedAssociations: [],       // deactivated, because assertion changes are not relevant to change management (UX)
            },
          };
          if (
            SupportedFeaturesService.getInstance().isTechnicalVersionRestoreEnabled() &&
            getIsVersioningRestoreMode(this.oService)
          ) {
            let selfUpdate;
            if (!modifiedObject) {
              // modifiedObject undefined --> output is updated by intermidium node like projection exclude/include
              selfUpdate = true;
            } else {
              // sqlView is different with graphic view here since sqlView model cannot access source entities
              // so selfUpdate has no meaning here, then we set a false to make validation process not skipped
              if (model?.entities?.length === 0) {
                // --> sql view
                selfUpdate = false;
              } else {
                // --> graphic view
                const type = modifiedObject?.classDefinition?.name;
                if (type === "Output") {
                  selfUpdate = modifiedObject === autofixedObject;
                } else {
                  // selfUpdate designs to distinguish output updates from itself or source
                  selfUpdate = false;
                }
              }
            }
            autofixedObjItem.selfUpdate = selfUpdate;
          }
          modifiedObjItem.autofixedObjects.push(autofixedObjItem);
        }
        return autofixedObjItem;
      },

      findModifiedObjectOfDeletedOutputColumn(model, deletedElementName) {
        let modifiedObject;
        if (model?.changeManagement?.modifiedObjects) {
          for (const modifItem of model.changeManagement.modifiedObjects) {
            for (const autofixObjItem of modifItem.autofixedObjects) {
              for (const deletedColumn of autofixObjItem.changeInfo.deletedColumns) {
                if (
                  deletedColumn.elementName === deletedElementName &&
                  autofixObjItem.autofixedObject.classDefinition.name === "Entity"
                ) {
                  modifiedObject = modifItem.modifiedObject;
                  break;
                }
              }
              if (modifiedObject) {
                break;
              }
            }
            if (modifiedObject) {
              break;
            }
          }
        }
        return modifiedObject;
      },

      addSourceElementChangeToChangeManagement(model, changedObject, argRefSourceElement) {
        // changedObject: calc element or filter/aggr. node
        let columnInfoChangedDataType;
        const sName = argRefSourceElement && (argRefSourceElement.newName || argRefSourceElement.name);
        if (sName && model.changeManagement) {
          // find root source element
          let sourceRefElement = argRefSourceElement;
          while (sourceRefElement.objectId !== sourceRefElement.sourceElement.objectId) {
            sourceRefElement = sourceRefElement.sourceElement;
          }
          for (const modifItem of model.changeManagement.modifiedObjects) {
            for (const autofixItem of modifItem.autofixedObjects) {
              for (const column of autofixItem.changeInfo.updatedDataTypeColumns) {
                // find root source element
                let sourceElementCol = column.element;
                while (sourceElementCol.objectId !== sourceElementCol.sourceElement.objectId) {
                  sourceElementCol = sourceElementCol.sourceElement;
                }
                if (sourceElementCol === sourceRefElement) {
                  columnInfoChangedDataType = column;
                }
              }
            }
          }
          if (columnInfoChangedDataType) {
            const autofixedObjItem = this.getOrCreateChangeManagementAutofixObjectItem(
              model,
              sourceRefElement.container,
              changedObject,
              false
            );
            const cols = autofixedObjItem.changeInfo.updatedDataTypeColumns.filter(
              (col) => col.element === argRefSourceElement
            );
            if (cols.length === 0) {
              autofixedObjItem.changeInfo.updatedDataTypeColumns.push({
                elementName: sName,
                element: sourceRefElement,
                old: columnInfoChangedDataType.old,
                new: columnInfoChangedDataType.new,
              });
              return;
            }
          }
        }
      },

      addSourceParameterChangeToChangeManagement(model, changedObject, argParameter) {
        // changedObject: calc element or filter/aggr. node
        let parameterInfo;
        const sName = argParameter && (argParameter.newName || argParameter.name);
        if (sName && model.changeManagement) {
          for (const modifItem of model.changeManagement.modifiedObjects) {
            for (const autofixItem of modifItem.autofixedObjects) {
              for (const parameter of autofixItem.changeInfo.updatedParameters) {
                if (parameter === argParameter) {
                  parameterInfo = parameter;
                }
              }
            }
          }
          if (parameterInfo) {
            const autofixedObjItem = this.getOrCreateChangeManagementAutofixObjectItem(
              model,
              argParameter.container,
              changedObject,
              false
            );
            const params = autofixedObjItem.changeInfo.updatedParameters.filter((param) => param === argParameter);
            if (params.length === 0) {
              autofixedObjItem.changeInfo.updatedParameters.push({ elementName: sName, element: argParameter });
              return;
            }
          }
        }
      },

      getOrCreateSucceedingProjectionNode: function (sourceNode) {
        // check for existing projection node after the source node (ProjectioNode = RenameNode or RemoveNode)
        const successorBranch = nsLocal.NodeImpl.getSuccessorsBranch(sourceNode);
        if (successorBranch.hasProjection) {
          return {
            node: successorBranch.projectionNode,
            isNew: false,
          };
        }
        // create new projection node and insert after source node
        const projectionNode = nsLocal.ModelImpl.createObject(
          "sap.cdw.querybuilder.RenameElements",
          { isProjectionNode: true },
          sourceNode.resource.model
        );
        nsLocal.ModelImpl.setDefaultObjectName(projectionNode);
        this.onCreateIntermediateNode(sourceNode, projectionNode, true); // also see 'nsLocal.ModelImpl.requestAdjustDiagramsContent();' following the section "Add new elements" (around line 695)
        return {
          node: projectionNode,
          isNew: true,
        };
      },

      getOrCreateSucceedingUnionNode: function (sourceNode) {
        // check for existing union node after the source node
        const successorBranch = nsLocal.NodeImpl.getSuccessorsBranch(sourceNode);
        if (successorBranch.hasUnion) {
          return {
            node: successorBranch.unionNode,
            isNew: false,
          };
        }
        // create new union node and insert after source node
        const unionNode = nsLocal.ModelImpl.createObject(
          "sap.cdw.querybuilder.Union",
          { isUnionNode: true },
          sourceNode.resource.model
        );
        nsLocal.ModelImpl.setDefaultObjectName(unionNode);
        this.onCreateIntermediateNode(sourceNode, unionNode, true);
        return {
          node: unionNode,
          isNew: true,
        };
      },

      replaceSource(replacementInfo) {
        const oResource = replacementInfo.oldEntity.resource;
        oResource.applyUndoableAction(() => {
          const sourceEntity = replacementInfo.oldEntity;
          let targetEntity = replacementInfo.newEntity;
          if (replacementInfo.csnNewEntity) {
            targetEntity = nsLocal.ModelImpl.createGalileiEntityFromCSNEntity(oResource, replacementInfo.csnNewEntity);
            // update useAs value for target entity
            targetEntity.useAs = sourceEntity.useAs;
            // create symbol to handle deletion
            nsLocal.DiagramImpl.createSymbol(replacementInfo.editor, targetEntity);
          }

          if (replacementInfo.csnNewEntity) {
            replacementInfo.newOrderedElements = targetEntity.orderedElements;
            replacementInfo.oldOrderedElements = replacementInfo.oldOrderedElements
              ? replacementInfo.oldOrderedElements
              : sourceEntity.orderedElements;
            const getElement = (elementName: string) => {
              for (let i = 0; i < targetEntity.elements.length; i++) {
                const targetElement = targetEntity.elements.get(i);
                if (targetElement.name === elementName) {
                  return targetElement;
                }
              }
            };

            if (Array.isArray(replacementInfo.mappings)) {
              // adjust mappings if new is a CSN and not Galilei Entity
              replacementInfo.mappings = replacementInfo.mappings.map((mapping) => {
                mapping.target = getElement(mapping.target);
                return mapping;
              });
            } else {
              // generate mappings - replacementInfo.mappings is undefined
              replacementInfo.mappings = [];
              for (const sourceElement of sourceEntity.elements.toArray()) {
                const targetElement = targetEntity.elements.selectObject({ name: sourceElement.name });
                if (targetElement) {
                  replacementInfo.mappings.push({ source: sourceElement, target: targetElement });
                }
              }
            }
          }
          const sourceElements = replacementInfo.oldOrderedElements;
          const targetElements = replacementInfo.newOrderedElements;

          nsLocal.ModelImpl.updateEntityProperties(sourceEntity, targetEntity);
          const namesDoNotMatch = nsLocal.ModelImpl.updateElementsProperties(replacementInfo.mappings);
          nsLocal.ModelImpl.deleteOldUnmappedElements(sourceElements, replacementInfo.mappings);
          nsLocal.ModelImpl.addNewUnmappedElementsAndExclude(
            sourceEntity,
            targetElements,
            replacementInfo.mappings,
            namesDoNotMatch,
            replacementInfo.excludeUnmatched
          );
          nsLocal.ModelImpl.updateParameters(sourceEntity, targetEntity);
          nsLocal.ModelImpl.deleteObject(
            targetEntity,
            replacementInfo.editor ? !!replacementInfo.editor.diagram : true
          );
          // Update unresolved Associations
          loadUnresolvedAssociations(sourceEntity, sourceEntity.csn, true);
        }, "Replace Source");

        sap.cdw.querybuilder.ModelImpl.requestAdjustDiagramsContent();
        replacementInfo.oldEntity.resource.model.validate();
        // action to open parameter mapping
        nsLocal.ModelImpl.triggerParameterMapping(replacementInfo.oldEntity);
        if (replacementInfo.oldEntity.relatedSymbols.get(0)) {
          replacementInfo.editor.selectSymbol(replacementInfo.oldEntity.relatedSymbols.get(0));
        }
      },

      createGalileiEntityFromCSNEntity(resource, csn) {
        const oModel = resource.model;
        const sEntityClassName = "sap.cdw.querybuilder.Entity";
        let entityName;
        for (const name in csn.definitions) {
          entityName = name;
          break;
        }
        const oEntity = nsLocal.ModelImpl.createObject(sEntityClassName, { name: entityName }, oModel);
        const csnEntity = csn.definitions[entityName];
        oEntity.csn = csnEntity;
        // columns should be created before reading partitions
        const csnColumns = csnEntity.elements;
        nsLocal.ModelImpl.createElementsObject(csnColumns, oEntity);

        if (csnEntity[CsnAnnotations.DataWarehouse.partition]) {
          // read partition
          partitionsReadCsn(oEntity, csnEntity, csn);
        }

        const csnToModel = new nsLocal.CsnToModel();
        csnToModel.getDefaultPropertyMapping(oEntity, csnEntity);
        nsErmodeler.ModelImpl.updateEntityFromDropDefinitions(oEntity, csnEntity);
        if (oEntity.isDeltaTable) {
          // set cdc columns
          deltaTableReadCsn(oEntity, csnEntity);
        }
        return oEntity;
      },

      deleteOldUnmappedElements: function (elements: any[], mappings: any[]) {
        for (const oldElement of elements) {
          const mappedOldElement = mappings.find(
            (mapping) => (mapping.source.newName || mapping.source.name) === (oldElement.newName || oldElement.name)
          );
          if (typeof mappedOldElement === "undefined") {
            oldElement.deleteObject();
          }
        }
      },

      addNewUnmappedElementsAndExclude: function (
        entity: any,
        elements: any[],
        mappings: any[],
        namesDoNotMatch,
        excludeUnmapped = true
      ) {
        let cachedProjectionNode;
        const getProjectionElement = (element, projectionNode) => {
          let successorElement = element;
          while (!successorElement || successorElement.container !== projectionNode) {
            successorElement = successorElement.successorElement;
          }
          return successorElement;
        };
        const getProjectionNode = () => {
          if (!excludeUnmapped && entity.successorNode) {
            return entity.successorNode;
          }
          if (!cachedProjectionNode) {
            cachedProjectionNode = nsLocal.ModelImpl.getOrCreateSucceedingProjectionNode(entity).node;
          }
          return cachedProjectionNode;
        };

        for (const element of elements) {
          const mapped = mappings.find(
            (mapping) => (mapping.source.newName || mapping.source.name) === (element.newName || element.name)
          );
          if (typeof mapped === "undefined") {
            const projectionNode = getProjectionNode();
            // eslint-disable-next-line no-underscore-dangle
            const targetElementCopy = nsLocal.NodeImpl._duplicateElement(element, /* bAsCalculated*/ undefined, entity);
            entity.elements.push(targetElementCopy);
            if (excludeUnmapped) {
              const projectionElement = getProjectionElement(targetElementCopy, projectionNode);
              projectionElement.isRemoved = true;
            }
          } else if (namesDoNotMatch) {
            const projectionNode = getProjectionNode();
            const projectionElement = getProjectionElement(mapped.source, projectionNode);
            // eslint-disable-next-line no-underscore-dangle
            projectionElement.newName = mapped.source._originalName;
            // eslint-disable-next-line no-underscore-dangle
            projectionElement.label = mapped.source._originalLabel;
            // eslint-disable-next-line no-underscore-dangle
            delete mapped.source._originalName;
          }
        }
      },

      updateParameters: function (entity: any, fromEntity: any): void {
        // remove existing parameters
        if (entity.parameters) {
          entity.parameters.forEach((oldParameter: sap.cdw.commonmodel.Parameter) => {
            oldParameter.deleteObject();
          });
        }
        // add new parameters
        const oClass = sap.galilei.model.getClass("sap.cdw.commonmodel.Parameter");
        if (fromEntity.parameters) {
          fromEntity.parameters.forEach((newParameter: sap.cdw.commonmodel.Parameter) => {
            const parameterCloneData = {
              name: newParameter.name,
              label: newParameter.displayName,
              dataType: newParameter.dataType,
              // targetParameter:,   -> unmapped
              // defaultValue:,      -> unmapped
            };
            // length
            if (typeof newParameter.length !== "undefined") {
              (parameterCloneData as any).length = newParameter.length;
            }
            // precision
            if (typeof newParameter.precision !== "undefined") {
              (parameterCloneData as any).precision = newParameter.precision;
            }
            // scale
            if (typeof newParameter.scale !== "undefined") {
              (parameterCloneData as any).scale = newParameter.scale;
            }
            // srid
            if (typeof newParameter.srid !== "undefined") {
              (parameterCloneData as any).srid = newParameter.srid;
            }
            const newParameterClone = oClass.create(entity.resource, parameterCloneData);
            entity.parameters.push(newParameterClone);
          });
        }
      },

      triggerParameterMapping: function (entity: any): void {
        // action to open parameter mapping
        if (entity?.parameters?.length > 0 && entity.successorNode !== undefined) {
          const unMappedParameters = entity.parameters.filter((parameter) => parameter.targetParameter === undefined);
          const hasUnMappedParams = unMappedParameters.length > 0;
          if (hasUnMappedParams) {
            const oOptions: any = {
              requestId: "parameterMapping",
              oEntityObj: entity,
            };
            // this will publish an event.. csnquerybuilder will react
            nsLocal.ModelImpl.requestParameterMapping(oOptions);
          }
        }
      },

      updateEntityProperties: function (entity: any, fromEntity: any): void {
        const alias = entity.alias;
        entity.csn = fromEntity.csn;
        entity.copyObjectProperties(fromEntity);
        entity.context = fromEntity.context;
        entity.alias = alias;
        fromEntity.updateRemoteReadyStatus = function (status, validate) {
          // eslint-disable-next-line prefer-rest-params
          entity.resource?.applyUndoableAction(
            function () {
              entity.updateRemoteReadyStatus(status, validate);
            },
            "Update remote entity status",
            /* protectedFromUndo*/ true
          );
        };
      },

      updateElementsProperties: function (mappings: any[]): boolean {
        let namesDoNotMatch = false;
        for (const mapping of mappings) {
          const elementNameMatch = nsLocal.ModelImpl.updateElementProperties(mapping.source, mapping.target);
          namesDoNotMatch = namesDoNotMatch || elementNameMatch;
        }
        return namesDoNotMatch;
      },

      updateElementProperties: function (element: any, fromElement: any): boolean {
        const existingName = element.newName || element.name;
        const existingLabel = element.label;
        element.copyObjectProperties(fromElement);
        // eslint-disable-next-line no-underscore-dangle
        element._originalName = existingName;
        // eslint-disable-next-line no-underscore-dangle
        element._originalLabel = existingLabel;
        if ((element.newName || element.name) !== existingName) {
          // transient property to cache the original name used to rename back the element in Projection node
          return true;
        }
        return false;
      },

      cleanViewDataAccessControls: function (oView) {
        // TODO: to replace by an actual update from DACType!
        const oModel = oView && oView.resource && oView.resource.model;
        if (oModel && oView.viewDataAccessControls && oView.viewDataAccessControls.length) {
          oView.viewDataAccessControls.forEach((oVDAC) => {
            const oTarget = oVDAC && oVDAC.dataAccessControl;
            if (oTarget && oTarget.name && oModel.dataAccessControls) {
              if (!findInCollectionByName(oModel.dataAccessControls, oTarget.name)) {
                // target DAC should be deleted!
                oTarget.deleteObject();
              }
            }
          });
          oView.cleaningViewDataAccessControls = true;
          oView.viewDataAccessControls.deleteAll();
        }
      },

      updateModelSources: async function (oModel, oService, oCSN): Promise<any> {
        const self = this;
        const supportConsumptionCSN = SupportedFeaturesService.getInstance().isImproveOutputUpdateEnabled();
        const csnProperty = supportConsumptionCSN ? DocumentProperty.consumptionCsn : DocumentProperty.csn;

        SourceToOutputChangesMonitor.initialize(oModel);

        const doUpdateSources = (resolve, reject) => {
          if (oModel && oService && !oModel._rollingBack) {
            // Update Entities AND Dimension Nodes? and contexts
            const objects = oModel.contexts
              .toArray()
              .concat(
                oModel.nodes
                  .toArray()
                  .filter(
                    (oObject) =>
                      oObject.classDefinition.name === "Entity" || oObject.classDefinition.name === "DimensionNode"
                  )
              );
            const isTechnicalVersionRestoreEnabled =
              SupportedFeaturesService.getInstance().isTechnicalVersionRestoreEnabled();
            if (isTechnicalVersionRestoreEnabled && oModel?.output?.viewDataAccessControls?.length > 0) {
              oModel?.output?.viewDataAccessControls?.forEach((dac) => {
                objects.push(dac);
              });
            }
            const localNames = [];
            const sharedNames = [];
            const sharedFullNames = [];
            objects.forEach((entity) => {
              if (entity.isCrossSpace) {
                // Not space
                if (!entity.isContext) {
                  sharedFullNames.push(entity.name); // name is like spaceName.name
                  sharedNames.push(entity.crossSpaceEntityName); // Pure name
                }
              } else {
                localNames.push(entity.name);
              }
            });
            let localEntityCSN;
            let sharedEntityCSN;

            // Use one service call to get list of objects' csns
            if (localNames.length > 0) {
              localEntityCSN = DocumentStorageService.getInstance().getOrLoadDocumentsList(
                {
                  detailsProperties: [csnProperty, DocumentProperty.repoPackage],
                  filters: { name: localNames }, // Now it includes 'entity' and 'sap.dwc.dac'
                },
                true
              );
              localEntityCSN.finally(() => {
                //
              });
            } else {
              localEntityCSN = Promise.resolve([]);
            }

            if (sharedNames.length > 0) {
              // Get shared objects' csn
              sharedEntityCSN = getArtefactSharesForTarget(
                oService.spaceId,
                [
                  "name",
                  "kind",
                  "#isViewEntity",
                  "creation_date",
                  "creator",
                  "owner",
                  csnProperty,
                  "modification_date",
                  "deployment_date",
                  "#deploymentExecutionStatus",
                  "#objectStatus",
                  "releaseStateValue",
                  "releaseDate",
                  "deprecationDate",
                  "decommissioningDate",
                  "spaceName",
                  "spaceBusinessName",
                  "spaceSchemaName",
                  "#writeAccess",
                ],
                [`name:${sharedNames.join("|")}`]
              ); // It's better to also filter source space on backend, but backend for now only support to filter guid of the source space, and frontend only has space name when the user don't have permission on the source space
            } else {
              sharedEntityCSN = Promise.resolve([]);
            }

            // eslint-disable-next-line dot-notation
            window["performanceLogger"]?.enterMethod({ name: "getOrLoadDocumentsList and getArtefactSharesForTarget" });
            Promise.all([localEntityCSN, sharedEntityCSN]).then((CSNs) => {
              // eslint-disable-next-line dot-notation
              window["performanceLogger"]?.leaveMethod({
                name: "getOrLoadDocumentsList and getArtefactSharesForTarget",
              });
              const initSharedCSNs: any[] = CSNs[1]?.results;
              const sharedCSNs = [];
              const sharedSpaceNames = [];
              const sharedSpaceCSNs = [];
              initSharedCSNs?.forEach((file) => {
                if (file && sharedFullNames.includes(`${file.spaceName}.${file.name}`)) {
                  file.csn = file.content;
                  delete file.content;
                  sharedCSNs.push(file);
                  // Add space to context since it is not up to date in the csn of designObjects
                  if (file.spaceName && !sharedSpaceNames.includes(file.spaceName)) {
                    sharedSpaceCSNs.push({
                      "@DataWarehouse.space.name": file.spaceName,
                      "@DataWarehouse.space.schema": file.spaceSchemaName,
                      "@EndUserText.label": file.spaceBusinessName,
                      kind: "context",
                    });
                    sharedSpaceNames.push(file.spaceName);
                  }
                }
              });
              const allRepoCSNs = CSNs[0].concat(sharedCSNs).concat(sharedSpaceCSNs);
              this.updateModelSource(oService, oModel, objects, resolve, reject, 0, oCSN, allRepoCSNs);
            });
          } else {
            resolve();
            oModel.completeProcess("UPDATE_SOURCES");
          }
        };

        const onlyUpdateSourcesObjectStatus = () => {
          if (oModel && oService && !oModel._rollingBack) {
            setTimeout(() => {
              // Update sources' object status
              oModel.nodes
                .toArray()
                .filter(
                  (oObject) =>
                    oObject.classDefinition.name === "Entity" || oObject.classDefinition.name === "DimensionNode"
                )
                .forEach((entity) => {
                  // Update object status to deployed since it is the only possible status for source entities in versioning readOnly mode
                  if (entity && entity["#objectStatus"] !== ObjectStatus.deployed) {
                    entity["#objectStatus"] = ObjectStatus.deployed;
                  }
                });
            }, 10);
          }
        };

        let goWithUpdateSources = true;
        let workbenchController;
        try {
          workbenchController = getWorkbenchController();
        } catch (e) {
          // eslint-disable-next-line no-empty
        }
        if (workbenchController?.versionId && !workbenchController?.isInVersionRestorePage) {
          // versionId exists and isInVersionRestorePage indicates that in old version readOnly page,
          // we should skip update sources entities
          goWithUpdateSources = false;
        }
        if (goWithUpdateSources) {
          // versioning readOnly page will not update source entities so do not show this
          await oModel.prepareProcess("UPDATE_SOURCES", this.localizeText("UPDATING_SOURCES"));
        }
        return new Promise((resolve, reject) => {
          // start with output
          const output = oModel.output;
          if (output) {
            // eslint-disable-next-line dot-notation
            window["performanceLogger"]?.enterMethod({ name: `getRepositoryObject - output: ${output.technicalName}` });
            oService.getRepositoryObject(output.technicalName).then(async function (obj) {
              // eslint-disable-next-line dot-notation
              window["performanceLogger"]?.leaveMethod({
                name: `getRepositoryObject - output: ${output.technicalName}`,
              });
              const csn = obj && obj.csn;
              if (csn && csn.definitions) {
                const editorSettings =
                  oCSN?.editorSettings?.[output.technicalName] || csn.editorSettings?.[output.technicalName];
                const lastEditorModifier = editorSettings?.editor?.lastModifier;
                let outputCsn;
                if (getIsVersioningReadOnlyMode(oService)) {
                  // for versioning readOnly page, we only show a readOnly input for releaseState so do not need update output.releaseContractCSN
                  // at this time, oCSN comes from /version response and csn comes from /designObjects
                  output.releaseContractCSN = oCSN?.releaseContractDefinitions?.[output.technicalName];
                  outputCsn = oCSN.definitions[output.technicalName] || csn?.definitions?.[output.technicalName];
                } else {
                  outputCsn = oCSN?.definitions?.[output.technicalName] || csn.definitions[output.technicalName];
                  const releaseContractCSN =
                    oCSN?.releaseContractDefinitions?.[output.technicalName] ||
                    csn.releaseContractDefinitions?.[output.technicalName];
                  output.releaseContractCSN = releaseContractCSN;
                }
                // Save DAC usage for future update
                if (outputCsn[CsnAnnotations.DataWarehouse.dataAccessControl_usage]) {
                  self.cleanViewDataAccessControls(output);
                }
                output.repositoryCSN = csn;

                const currentNumberOfElements = output.elements.length;
                const skipChangeManagement = lastEditorModifier === Editor.VIEWBUILDER;
                // eslint-disable-next-line dot-notation
                window["performanceLogger"]?.enterMethod({
                  name: `updateEntityFromCsn - output (${currentNumberOfElements} elements)`,
                });
                self.updateEntityFromCsn(output, outputCsn, oModel, oService, skipChangeManagement);
                // eslint-disable-next-line dot-notation
                window["performanceLogger"]?.leaveMethod({
                  name: `updateEntityFromCsn - output (${currentNumberOfElements} elements)`,
                });
                // eslint-disable-next-line dot-notation
                window["performanceLogger"]?.enterMethod({ name: "updateAssociationsFromCsn - output" });
                const isVersioningRestoreMode = getIsVersioningRestoreMode(oService);
                if (!isVersioningRestoreMode) {
                  self.updateAssociationsFromCsn(output, outputCsn, oModel, oService);
                }
                // eslint-disable-next-line dot-notation
                window["performanceLogger"]?.leaveMethod({ name: "updateAssociationsFromCsn - output" });
                // Load/Update Data Access Controls
                self.updateDataAccessControls(output, outputCsn, oService);
              }
              if (goWithUpdateSources) {
                // versioning readOnly page will not update source entities
                doUpdateSources(resolve, reject);
              } else {
                // versioning readOnly page still need update source entities' status
                onlyUpdateSourcesObjectStatus();
              }
            });
          } else {
            doUpdateSources(resolve, reject);
          }
        });
      },

      updateModelSource(oService, oModel, objects, allResolve, allReject, index = 0, oCSN, aRepoCsns = []) {
        const nextObject = () => {
          setTimeout(() => {
            this.updateModelSource(oService, oModel, objects, allResolve, allReject, index + 1, oCSN, aRepoCsns);
          }, 0);
        };
        if (index >= objects.length) {
          allResolve();
          oModel.completeProcess("UPDATE_SOURCES");
          return;
        }
        const object = objects[index];
        const objectName = object && object.name;

        let performanceLoggingMethodName;
        if (object?.crossSpace?.space) {
          oModel.updateProcessDescription(
            "UPDATE_SOURCES",
            this.localizeText("UPDATING_SOURCE", [objectName]),
            `class: ${object.classDefinition.name}, cross-space: ${object.crossSpace.space}`
          );
          performanceLoggingMethodName = `getCurrentOrCrossSpaceObject: ${objectName}, cross-space: ${object.crossSpace.space}`;
        } else {
          oModel.updateProcessDescription(
            "UPDATE_SOURCES",
            this.localizeText("UPDATING_SOURCE", [objectName]),
            `class: ${object.classDefinition?.name}`
          );
          performanceLoggingMethodName = `getCurrentOrCrossSpaceObject: ${objectName}`;
        }
        // eslint-disable-next-line dot-notation
        window["performanceLogger"]?.enterMethod({ name: performanceLoggingMethodName });
        RepositoryUtils.getCurrentOrCrossSpaceObject(oService, object, oCSN, aRepoCsns)
          .then((objFile) => {
            // eslint-disable-next-line dot-notation
            window["performanceLogger"]?.leaveMethod({ name: performanceLoggingMethodName });
            this.updateModelSourceFromObjectFile(oService, oModel, objectName, object, oCSN, objFile);
            nextObject();
          })
          .catch(function (err) {
            // eslint-disable-next-line dot-notation
            window["performanceLogger"]?.leaveMethod({ name: performanceLoggingMethodName });
            allReject(err);
          });
      },

      updateModelSourceFromObjectFile(oService, oModel, objectName, object, oCSN, objFile) {
        const supportConsumptionCSN = SupportedFeaturesService.getInstance().isImproveOutputUpdateEnabled();
        const csnProperty = supportConsumptionCSN ? DocumentProperty.consumptionCsn : DocumentProperty.csn;
        // eslint-disable-next-line dot-notation
        window["performanceLogger"]?.enterMethod({ name: `getLocalOrSharedObjectCsn: ${objectName}` });
        const objectCsn = RepositoryUtils.getLocalOrSharedObjectCsn(
          objFile,
          object,
          oCSN,
          objectName,
          /* bGetDeploymentHint */ undefined,
          csnProperty
        );
        // eslint-disable-next-line dot-notation
        window["performanceLogger"]?.leaveMethod({ name: `getLocalOrSharedObjectCsn: ${objectName}` });
        if (objectCsn) {
          object.repositoryCSN = objectCsn;
          if (object.isEntity || object.isTable || object.isView) {
            // eslint-disable-next-line dot-notation
            window["performanceLogger"]?.enterMethod({ name: `updateObjectFileInfo: ${objectName}` });
            RepositoryUtils.updateObjectFileInfo(object, objFile); // To get date updated first than updateEntityFromCsn
            // eslint-disable-next-line dot-notation
            window["performanceLogger"]?.leaveMethod({ name: `updateObjectFileInfo: ${objectName}` });
            const currentNumberOfElements = object.elements.length;
            // eslint-disable-next-line dot-notation
            window["performanceLogger"]?.enterMethod({
              name: `updateEntityFromCsn: ${objectName} (${currentNumberOfElements} elements)`,
            });
            this.updateEntityFromCsn(object, objectCsn, oModel, oService);
            // eslint-disable-next-line dot-notation
            window["performanceLogger"]?.leaveMethod({
              name: `updateEntityFromCsn: ${objectName} (${currentNumberOfElements} elements)`,
            });
          } else if (object.isContext && object.isCrossSpace) {
            object.resource?.applyUndoableAction(
              () => {
                const newLabel = objectCsn["@EndUserText.label"];
                if (newLabel !== object.label) {
                  object.label = newLabel;
                }
              },
              "Update label of space",
              /* protectedFromUndoRedo*/ true
            );
          }
        } else if (oModel && oModel.aMissingSources) {
          const isTechnicalVersionRestoreEnabled =
            SupportedFeaturesService.getInstance().isTechnicalVersionRestoreEnabled();
          if (isTechnicalVersionRestoreEnabled) {
            const sType = (object.classDefinition && object.classDefinition.name) || "Object";
            if (sType === "ViewDataAccessControl") {
              // there is special case that viewer user cannot read dac object so at this time we cannot know whether dac exist
              const dacPrivilege = ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_DAC");
              if (dacPrivilege.read) {
                // only add missing sources when user can at lease read dac
                oModel.aMissingSources.push(object);
              }
            } else if (sType !== "Context") {
              oModel.aMissingSources.push(object);
            }
          } else {
            const sType = (object.classDefinition && object.classDefinition.name) || "Object";
            if (sType !== "Context") {
              oModel.aMissingSources.push(`${sType}: '${objectName}'`);
            }
            // change management
            // - deleted source (a pop-up has already been shown when loading the model into graphical view builder)
            // eslint-disable-next-line dot-notation
            window["performanceLogger"]?.enterMethod({ name: "changeManagement-reporting-deletedSource" });
            if (sType === "Entity") {
              const autofixedObjItem = this.getOrCreateChangeManagementAutofixObjectItem(oModel, object, object, false);
              autofixedObjItem.changeInfo.deletedSources.push(object.name);
            }
            // eslint-disable-next-line dot-notation
            window["performanceLogger"]?.leaveMethod({ name: "changeManagement-reporting-deletedSource" });
          }
        }
      },

      /**
       * Load existing simple types from repository and update base type of simple types and elements.
       * @param oModel
       */
      loadAndUpdateSimpleTypes: async function (oModel: any): Promise<any> {
        return new Promise((resolve, reject) => {
          nsCommonModel.ModelImpl.getSimpeTypesAndContexts(oModel)
            .then(() => {
              nsCommonModel.ObjectImpl.attachObjectsToBaseTypes(oModel);
              resolve({});
            })
            .catch(() => {
              reject();
            });
        });
      },

      /**
       * Clear list of actions and undo-stack
       * @function
       * @name clearUndoRedo
       * @param oModel
       */
      clearUndoRedo: function (oModel: any) {
        if (oModel) {
          oModel.resource.clearListOfActions();
          oModel.resource.clearUndoStack();
        }
      },

      /**
       * Model structure has been modified request diagrams adjustments
       * @function
       * @name requestAdjustDiagramsContent
       * @param {*} oOptions the options for instance {
       *  selection: galilei object to select after adjusting symbols
       *  model: in case of loading data
       * }
       */
      requestAdjustDiagramsContent: function (oOptions) {
        if (sap.ui && sap.ui.getCore instanceof Function) {
          sap.ui
            .getCore()
            .getEventBus()
            .publish(nsLocal.ModelImpl.CSNMODEL_CHANNEL, nsLocal.ModelImpl.CSNMODEL_CHANGED_EVENT, oOptions);
        }
      },

      requestDeleteObjectAndSymbols: function (object: any, deleteObject: boolean = true) {
        if (sap.ui && sap.ui.getCore instanceof Function) {
          sap.ui
            .getCore()
            .getEventBus()
            .publish(nsLocal.ModelImpl.CSNMODEL_CHANNEL, nsLocal.ModelImpl.CSNMODEL_DELETE_OBJECTANDSYMBOLS, {
              object: object,
              deleteObject: deleteObject,
            });
        } else {
          object?.deleteObject();
        }
      },

      /**
       * Publishes event of model loaded
       * @function
       * @name notifyModelLoaded
       * @param oModel
       */
      notifyModelLoaded: function (model) {
        if (sap.ui && sap.ui.getCore instanceof Function) {
          sap.ui
            .getCore()
            .getEventBus()
            .publish(nsLocal.ModelImpl.CSNMODEL_CHANNEL, nsLocal.ModelImpl.CSNMODEL_LOADED, { model: model });
        }
      },

      /**
       * Publishes event of model loaded and validated
       * @function
       * @name notifyModelLoadedAndValidated
       * @param oModel
       */
      notifyModelLoadedAndValidated: function (model) {
        if (sap.ui && sap.ui.getCore instanceof Function) {
          sap.ui
            .getCore()
            .getEventBus()
            .publish(nsLocal.ModelImpl.CSNMODEL_CHANNEL, nsLocal.ModelImpl.CSNMODEL_LOADED_AND_VALIDATED, {
              model: model,
            });
        }
      },

      /**
       * Request a user intervention to complete a task
       * receiver will handle options and react accordingly.
       * Actually is used in case of duplicate element when creating a join
       * @param {*} oOptions
       */
      requestUserAction: function (oOptions) {
        if (sap.ui && sap.ui.getCore instanceof Function) {
          sap.ui
            .getCore()
            .getEventBus()
            .publish(nsLocal.ModelImpl.CSNMODEL_CHANNEL, nsLocal.ModelImpl.CSNMODEL_REQUEST_USER_ACTION, oOptions);
        }
      },

      /**
       * Requests a user to perform parameter mapping
       * receiver will handle options and react accordingly.
       * Actually it is used in case if the source used in the creation of view has parameters
       * @param {*} oOptions
       */
      requestParameterMapping: function (oOptions) {
        if (sap.ui && sap.ui.getCore instanceof Function) {
          sap.ui
            .getCore()
            .getEventBus()
            .publish(
              nsLocal.ModelImpl.CSNMODEL_CHANNEL,
              nsLocal.ModelImpl.CSNMODEL_REQUEST_PARAMETER_MAPPING,
              oOptions
            );
        }
      },

      /**
       * A new model is created publish the event
       * @function
       * @name publishNewModelEvent
       * @param {*} oOptions the options for instance {
       *  model: the new model
       * }
       */
      publishNewModelEvent: function (oOptions) {
        if (sap.ui && sap.ui.getCore instanceof Function) {
          sap.ui
            .getCore()
            .getEventBus()
            .publish(nsLocal.ModelImpl.CSNMODEL_CHANNEL, nsLocal.ModelImpl.CSNMODEL_NEWMODEL_EVENT, oOptions);
        }
      },

      /**
       * Publishes an asynchronous loaded elements event
       * @function
       * @name publishasyncElementsLoadedEvent
       * @param {*} oOptions the options for instance {
       *  object: the container object
       * }
       */
      publishasyncElementsLoadedEvent: function (oOptions) {
        if (sap.ui && sap.ui.getCore instanceof Function) {
          sap.ui
            .getCore()
            .getEventBus()
            .publish(nsLocal.ModelImpl.CSNMODEL_CHANNEL, nsLocal.ModelImpl.CSNMODEL_ASYNC_ELTS_LOADED, oOptions);
        }
      },

      /**
       * Publishes a source entity dropped event
       * @function
       * @name publishasyncElementsLoadedEvent
       * @param {*} oOptions the options for instance {
       *  object: the entity object
       *  data: drop data
       * }
       */
      publishDroppedEntityEvent: function (oOptions) {
        if (sap.ui && sap.ui.getCore instanceof Function) {
          sap.ui
            .getCore()
            .getEventBus()
            .publish(nsLocal.ModelImpl.CSNMODEL_CHANNEL, nsLocal.ModelImpl.ENTITY_DROPPED, oOptions);
        }
      },

      /**
       * Imports selected objects to current diagram
       * @param selectedRemoteObjects
       * @param oDiagramEditor
       */
      importObjects: function (selectedRemoteObjects, oDiagramEditor) {
        if (selectedRemoteObjects && selectedRemoteObjects.length > 0 && oDiagramEditor) {
          const oExistingModel = oDiagramEditor.model;
          const oViewer = oDiagramEditor.viewer;
          const oResource = oExistingModel.resource;
          oResource.applyUndoableAction(
            () => {
              // Create objects
              selectedRemoteObjects.forEach((csn) => {
                // eslint-disable-next-line no-underscore-dangle
                const sObjectClassName = nsLocal.DiagramImpl._getObjectClassName(
                  "sap.cdw.querybuilder.ui.EntitySymbol"
                );
                if (sObjectClassName) {
                  const oObjectParam = {
                    name: csn.name,
                  };
                  const oObject = nsLocal.ModelImpl.createObject(sObjectClassName, oObjectParam, oExistingModel);
                  const oElementsLoaded = nsLocal.ModelImpl.onDropDataSource(oObject, csn);
                  jQuery.when(oElementsLoaded).done(function () {
                    nsLocal.ModelImpl.onPostDropDataSource(oObject);
                    if (oObject?.successorNode?.classDefinition?.name === "Output") {
                      setTimeout(() => {
                        const oSymbol = oExistingModel.output?.relatedSymbols?.get(0);
                        oSymbol.updateSymbol(oViewer);
                      }, 1000);
                    }
                  });
                }
              });
              // Auto create symbols and layout
              nsLocal.ModelImpl.requestAdjustDiagramsContent();
            },
            "Import objects",
            undefined,
            /* batchMode*/ true
          );
        }
      },

      /**
       * Helper function to create an object
       * @function
       * @name createObject
       * @param {*} sClassName
       * @param {*} oParam
       */
      createObject: function (sClassName, oParam, oContainer) {
        const oClass = sap.galilei.model.getClass(sClassName),
          oResource = oContainer && oContainer.resource;
        let oNewObject;
        if (oClass && oResource) {
          if (sClassName === "sap.cdw.querybuilder.Union" && oParam?.isUnionAll === undefined) {
            oParam.isUnionAll = true; // Default should be union all
          }
          oNewObject = oClass.create(oResource, oParam);
          if (sClassName === "sap.cdw.querybuilder.Association") {
            if (oNewObject.type === "Unmanaged") {
              oContainer.associations.push(oNewObject);
            }
          } else if (oContainer.nodes) {
            oContainer.nodes.push(oNewObject);
          } else if (oContainer.symbols) {
            oContainer.symbols.push(oNewObject);
          } else if (oContainer.elements) {
            const fnRestore = function (sProperty) {
              // It mainly happened when oParam.scale is 0, but oNewObject.scale is 2
              if (
                oParam &&
                sProperty &&
                oNewObject &&
                oNewObject.hasProperty(sProperty) &&
                oNewObject[sProperty] !== oParam[sProperty]
              ) {
                oNewObject[sProperty] = oParam[sProperty];
              }
            };
            fnRestore("scale");
            fnRestore("precision");
            fnRestore("length");
            fnRestore("srid");
            oNewObject.newName = oNewObject.name;
            oContainer.elements.push(oNewObject);
          } else if (oContainer && sClassName !== QualifiedClassNames.CURRENCY_VALUE) {
            oNewObject.newName = oNewObject.name;
            oContainer.push(oNewObject);
          }
        }
        if (oNewObject) {
          // eslint-disable-next-line no-underscore-dangle
          oNewObject._creationTimestamp = Date.now();
        }
        if (oResource && !oResource.isLoading && oResource.model) {
          oResource.model.validate();
        }
        return oNewObject;
      },

      onCreateIntermediateNode: function (oSourceObject, oNewOperation, bSkipAdjustToContent) {
        const oModel = (oSourceObject && oSourceObject.container) || (oNewOperation && oNewOperation.container);

        // Adding a new node..
        if (oNewOperation) {
          // Adjust new operation container
          if (!oNewOperation.container) {
            oModel.nodes.push(oNewOperation);
          }

          nsLocal.ModelImpl.insertNode(oSourceObject, oNewOperation);

          if (bSkipAdjustToContent !== true) {
            nsLocal.ModelImpl.requestAdjustDiagramsContent();
          }

          // auto remove duplicate elements
          if (oNewOperation.isProjectionNode) {
            nsLocal.ModelImpl.autoRemoveDuplicateElements(oNewOperation, /* bResolveDuplicateByRemove*/ true);
          }
        }
      },

      onPostDropNodeOnUnion: function (oNode, oUnionNode) {
        const bFirstInput = oUnionNode.predecessorNodes.length === 0;

        // attach node to union
        oNode.successorNode = oUnionNode;
        nsLocal.ModelImpl.requestAdjustDiagramsContent();
        if (bFirstInput) {
          // first input of a set operator --> copy elements
          nsLocal.NodeImpl.copyElementsForSetNode(oUnionNode, oNode);
        } else {
          // copy elements in skipCopy mode for further entities to ensure automatic mapping
          nsLocal.NodeImpl.copyElementsForSetNode(
            oUnionNode,
            oNode,
            /* bRemoveExistingElements*/ undefined,
            /* bSkipCopy*/ true
          );
        }
      },

      /**
       * Returns duplicated elements new name between given nodes
       *
       * @param {*} oNode1 first node
       * @param {*} oNode2 second node
       */
      getDuplicatedElements: function (oNode1, oNode2?) {
        const cachedElements = {};
        const duplicate = [];
        let element;
        let length = oNode1.elements.length;
        for (let index = 0; index < length; index++) {
          element = oNode1.elements.get(index);
          if (!element.isRemoved) {
            cachedElements[element.newName] = element;
          }
        }
        if (!oNode2) {
          oNode2 = oNode1;
        }
        length = oNode2.elements.length;
        for (let index = 0; index < length; index++) {
          element = oNode2.elements.get(index);
          const cachedElement = cachedElements[element.newName];
          if (cachedElement && cachedElement !== element) {
            duplicate.push({
              element1: element,
              element2: cachedElement,
            });
          }
        }
        return duplicate;
      },

      removeOneOneIntermediateNode: function (nodeToRemove, predecessor, skipDefaultDelete = false) {
        nsLocal.NodeImpl.stopPropagating = true;
        const successor = nodeToRemove.successorNode;
        if (predecessor && successor) {
          const elements = nodeToRemove.elements;
          const length = elements.length;
          for (let i = 0; i < length; i++) {
            const element = elements.get(i);
            let successorElement = element.successorElement;
            const predecessorElement = nsLocal.NodeImpl.getOnePredecessorElement(predecessor, nodeToRemove, element);
            if (
              nodeToRemove.classDefinition.name === "CalculatedElements" &&
              element.isCalculated &&
              element.isNew &&
              element.successorElement
            ) {
              // remove new calc element
              nsLocal.NodeImpl.stopPropagating = false;
              element.successorElement.deleteObject();
              nsLocal.NodeImpl.stopPropagating = true;
            } else if (
              nodeToRemove.classDefinition.name === "CalculatedElements" &&
              element.isCalculated &&
              element.successorElement
            ) {
              if (!element.isNew && predecessorElement) {
                // restore data type
                let restoredDataType = predecessorElement.dataType;
                if (nsLocal.NodeImpl.isAggregationElement(element.successorElement)) {
                  restoredDataType = nsLocal.NodeImpl.getAggregationDataType(
                    restoredDataType,
                    element.successorElement.defaultAggregation
                  );
                }
                nsLocal.NodeImpl.stopPropagating = false;
                element.successorElement.dataType = restoredDataType;
                element.successorElement.length = predecessorElement.length;
                element.successorElement.scale = predecessorElement.scale;
                element.successorElement.precision = predecessorElement.precision;
                element.successorElement.srid = predecessorElement.srid;
                nsLocal.NodeImpl.stopPropagating = true;
              }
              if (predecessorElement && !predecessorElement.isCalculated) {
                // here, we are removing a CalculatedElements and we set successor element to isCalculated=false only
                // if predecessor element not a calculated one (means there is another CalculatedElements before removed one)
                nsLocal.NodeImpl.stopPropagating = false;
                element.successorElement.isCalculated = false;
                nsLocal.NodeImpl.stopPropagating = true;
              }
            }
            if (
              nodeToRemove.classDefinition.name === "AggregatedElements" &&
              element.isAggregated &&
              element.successorElement
            ) {
              nsLocal.NodeImpl.stopPropagating = false;
              if (successorElement) {
                successorElement.isAggregated = false;
              } else {
                element.isAggregated = false;
                successorElement = element.successorElement;
              }
              nsLocal.NodeImpl.stopPropagating = true;
            }
            if (nodeToRemove.isProjectionNode) {
              nsLocal.NodeImpl.stopPropagating = false;
              // first restore names/isRemoved if nodeToRemove is a Projection
              if (successorElement && successorElement.name === element.name) {
                successorElement.newName = (predecessorElement && predecessorElement.newName) || element.name;
              }
              if (successorElement) {
                successorElement.isRemoved = false;
              } else {
                element.isRemoved = false;
                successorElement = element.successorElement;
              }
              nsLocal.NodeImpl.stopPropagating = true;
            }
            if (predecessorElement) {
              predecessorElement.successorElement = successorElement;
            }
            if (successor && successor.classDefinition.name === "Join") {
              const mappings = successor.mappings;
              mappings.forEach((mapping) => {
                let elt = mapping.source;
                if (elt === element) {
                  mapping.source = predecessorElement;
                }
                elt = mapping.target;
                if (elt === element) {
                  mapping.target = predecessorElement;
                }
              });
            }
          }
          // Switch successor node in a second time
          nodeToRemove.elements.deleteAll();
          nodeToRemove.successorNode = undefined;
          predecessor.successorNode = successor;
        }
        // Call default
        if (!skipDefaultDelete) {
          nodeToRemove.defaultOnBeforeDelete();
        }
        nsLocal.NodeImpl.stopPropagating = false;
      },

      insertNode: function (afterNode, nodeToInsert) {
        nsLocal.NodeImpl.stopPropagating = true;
        const oldElt2NewElt = {};
        const elements = afterNode.elements;
        const length = elements ? elements.length : 0;
        nsLocal.NodeImpl.removeAllElements(nodeToInsert);
        for (let i = 0; i < length; i++) {
          const elt = elements.get(i);
          if (elt.isRemoved) {
            continue;
          }
          const oldSuccessor = elt.successorElement;
          const isCalc = nodeToInsert.classDefinition.name === "CalculatedElements";
          const isProj = nodeToInsert.classDefinition.name === "RenameElements";
          // eslint-disable-next-line no-underscore-dangle
          const ce = nsLocal.NodeImpl._copyElement(elt, nodeToInsert, isCalc);
          oldElt2NewElt[elt.objectId] = ce;
          ce.successorElement = oldSuccessor;
          const supportedFeaturesService = SupportedFeaturesService.getInstance();
          // Fix invalid column names in Projection
          nsLocal.NodeImpl._adjustElementTechnicalName(elt, ce, isProj);
          if (isCalc && ce.successorElement) {
            nsLocal.NodeImpl.stopPropagating = false;
            ce.successorElement.isCalculated = true;
            nsLocal.NodeImpl.stopPropagating = true;
          }
        }
        nodeToInsert.successorNode = afterNode.successorNode;
        afterNode.successorNode = nodeToInsert;

        if (nodeToInsert.successorNode && nodeToInsert.successorNode.classDefinition.name === "Join") {
          const mappings = nodeToInsert.successorNode.mappings;
          mappings.forEach((mapping) => {
            let elt = mapping.source;
            if (elt && oldElt2NewElt[elt.objectId]) {
              mapping.source = oldElt2NewElt[elt.objectId];
            }
            elt = mapping.target;
            if (elt && oldElt2NewElt[elt.objectId]) {
              mapping.target = oldElt2NewElt[elt.objectId];
            }
          });
        }
        nsLocal.NodeImpl.stopPropagating = false;
      },

      onPostDropDataSource: async function (oEntity, oParentEntity, oDropOption, associatedInfo?: any) {
        let oJoinOrUnion,
          // oOldSuccessor,
          aSuccessorsBranch,
          oLastNode,
          oFirstJoinNode;

        const oDropOnOutput = oParentEntity && oParentEntity.classDefinition.name === "Output";
        const sOperation = oDropOption && typeof oDropOption === "object" ? oDropOption.operation : oDropOption;
        const pendingPromises = {};
        const oDeferred = jQuery.Deferred();

        const oModel = oEntity && oEntity.container;
        const fnCreateJoin = function () {
          const oObject = nsLocal.ModelImpl.createObject("sap.cdw.querybuilder.Join", {}, oModel);
          nsLocal.ModelImpl.setDefaultObjectName(oObject);
          // Set default cardinality to many-one for joins when the tables are associated
          if (
            associatedInfo &&
            associatedInfo.isAssociated &&
            oObject.leftCardinality === "" &&
            oObject.rightCardinality === ""
          ) {
            if (associatedInfo.lineage) {
              oObject.leftCardinality = CardinalityValues.MANY;
              oObject.rightCardinality = CardinalityValues.ONE;
            } else if (associatedInfo.impact) {
              oObject.leftCardinality = CardinalityValues.ONE;
              oObject.rightCardinality = CardinalityValues.MANY;
            }
          }
          return oObject;
        };
        const fnCreateUnion = function () {
          const oObject = nsLocal.ModelImpl.createObject("sap.cdw.querybuilder.Union", {}, oModel);
          nsLocal.ModelImpl.setDefaultObjectName(oObject);
          return oObject;
        };
        const fnDoStep2 = function (createdNode) {
          nsLocal.ModelImpl.autoProposeDataCategory(oModel, oEntity);
          if (createdNode) {
            if (createdNode.classDefinition.name === "Join") {
              nsLocal.ModelImpl.postCreateJoinAndElementsReady(createdNode);
            } else {
              nsLocal.ModelImpl.postCreateUnionAndElementsReady(createdNode);
            }
            nsLocal.ModelImpl.requestAdjustDiagramsContent({
              selection: createdNode,
            });
          }
        };
        const fnPostDropStep2 = function (csns?: any) {
          const csn = csns && csns[0];
          let isAsync = false;
          if (csn && pendingPromises[csn.entityId]) {
            oParentEntity = pendingPromises[csn.entityId].parentEntity;
            oEntity = pendingPromises[csn.entityId].entity;
            oJoinOrUnion = oEntity.successorNode;
            isAsync = true;
          }

          if (isAsync) {
            oJoinOrUnion.resource.applyUndoableAction(
              () => {
                fnDoStep2(oJoinOrUnion);
              },
              "Complete join/union creations with elements",
              undefined,
              /* batchMode*/ true
            );
          } else {
            fnDoStep2(oJoinOrUnion);
          }
          // action to open parameter mapping
          nsLocal.ModelImpl.triggerParameterMapping(oEntity);

          if (oEntity) {
            delete oEntity.asyncReadyness;
          }
          if (oParentEntity) {
            delete oParentEntity.asyncReadyness;
          }
          oDeferred.resolve({});
        };
        const fnPostDropStep1 = function () {
          if (oEntity && oParentEntity && !oDropOnOutput) {
            // case of entity created on existing entity
            // oOldSuccessor = oParentEntity.successorNode;
            if (sOperation === "Replace") {
              if (oEntity["#objectStatus"] !== ObjectStatus.notDeployed) {
                // postpone launching the mapping pop-over when dropping new objects (remote table/view)
                const oOptions: any = {
                  requestId: "MapOldSourceToNewSource",
                  oldEntity: oParentEntity,
                  newEntity: oEntity,
                };
                // this will publish an event.. csnquerybuilder will react
                // is needed in case if we want to branch UI to let user do the column-mapping of old source to new source
                // here, subscriber is NOT in editor extension, thus we are not really branching but finally launching the mapping popover
                nsLocal.ModelImpl.requestUserAction(oOptions);
              }
            } else if (sOperation === "Union") {
              oJoinOrUnion = fnCreateUnion();
              nsLocal.ModelImpl.insertNode(oParentEntity, oJoinOrUnion);
            } else {
              oJoinOrUnion = fnCreateJoin();
              nsLocal.ModelImpl.insertNode(oParentEntity, oJoinOrUnion);
            }
            // Move the full path (up to the first join if oEntity is attached to a join, and delete this join) of oEntity
            aSuccessorsBranch = nsLocal.NodeImpl.getSuccessorsBranch(oEntity).forwardBranch;
            if (aSuccessorsBranch.length > 0) {
              oLastNode = aSuccessorsBranch[aSuccessorsBranch.length - 1];
              // Not move Output node
              if (oLastNode.classDefinition.name === "Output") {
                oLastNode = oLastNode.predecessorNodes.get(0);
              }
            } else {
              oLastNode = oEntity;
            }
            if (oLastNode.successorNode && oLastNode.successorNode.classDefinition.name === "Join") {
              oFirstJoinNode = oLastNode.successorNode;
            }
            oLastNode.successorNode = oJoinOrUnion;
            if (oFirstJoinNode) {
              nsLocal.ModelImpl.deleteObject(oFirstJoinNode, /* tryFromRelatedSymbols */ false);
            }
            nsLocal.ModelImpl.requestAdjustDiagramsContent({
              selection: oJoinOrUnion,
            });
            return oJoinOrUnion;
          } else if (oEntity) {
            // a new entity created
            if (oEntity.successorNode) {
              // No action
            } else {
              // Guess entity successor...
              let oOutput;
              if (oDropOnOutput) {
                oOutput = oParentEntity;
              } else {
                oOutput = oModel && oModel.output;
              }
              const oOutputPredecessors =
                oOutput &&
                oModel.nodes.selectAllObjects({
                  "successorNode.objectId": oOutput.objectId,
                });
              if (oOutput && oOutputPredecessors.length === 0) {
                // Link the first entity to the output
                oEntity.successorNode = oOutput;
              } else if (oDropOnOutput) {
                // droping entity on output wich has predecessor
                oEntity.successorNode = oOutput;
                oOutputPredecessors.get(0).successorNode = undefined;
              }

              if (oEntity.successorNode) {
                nsLocal.ModelImpl.requestAdjustDiagramsContent();
              }
            }
          }
        };
        const asyncElementsLoad = [];
        if (oEntity && oEntity.asyncReadyness) {
          asyncElementsLoad.push(oEntity.asyncReadyness);
          pendingPromises[oEntity.name] = {
            parentEntity: oParentEntity,
            entity: oEntity,
          };
        }
        if (oParentEntity && oParentEntity.asyncReadyness) {
          asyncElementsLoad.push(oParentEntity.asyncReadyness);
          pendingPromises[oParentEntity.name] = {
            parentEntity: oParentEntity,
            entity: oEntity,
          };
        }

        if (asyncElementsLoad.length > 0) {
          // at least one entity' elements are being loaded -> wait for elements before processing step 2
          oEntity.resource.applyUndoableAction(
            () => {
              fnPostDropStep1();
            },
            "Create join/union node",
            undefined,
            /* batchMode*/ true
          );
          Promise.all(asyncElementsLoad)
            .then(fnPostDropStep2)
            .catch(() => {
              oDeferred.resolve({});
            });
        } else {
          fnPostDropStep1();
          fnPostDropStep2();
        }
        return oDeferred;
      },

      postCreateUnionAndElementsReady: function (oUnionNode) {
        if (!oUnionNode || oUnionNode.classDefinition.name !== "Union") {
          return;
        }
        // const aPredecessors = oUnionNode.predecessorNodes;
        const nLength = oUnionNode.predecessorNodes.length;
        const firstNode = nLength > 0 ? oUnionNode.predecessorNodes.get(0) : undefined;
        const lastNode = nLength > 1 ? oUnionNode.predecessorNodes.get(nLength - 1) : undefined;
        if (firstNode) {
          // first input of a set operator --> copy elements
          nsLocal.NodeImpl.copyElementsForSetNode(oUnionNode, firstNode);
        }
        if (lastNode) {
          // first input of a set operator --> copy elements
          nsLocal.NodeImpl.copyElementsForSetNode(
            oUnionNode,
            lastNode,
            /* bRemoveExistingElements*/ undefined,
            /* bSkipCopy*/ true
          );
        }
      },

      postCreateJoinAndElementsReady: function (oJoinNode) {
        if (!oJoinNode || oJoinNode.classDefinition.name !== "Join") {
          return;
        }
        const fnGetOrCreateSuccessorNode = function (joinNode) {
          const sClassName = "sap.cdw.querybuilder.RenameElements";
          const branchNodes = joinNode.getBranchNodes();
          const lookForOperator = !!branchNodes.hasRemoveElements || !!branchNodes.hasRenameElements;
          let projection;
          let isNew = false;
          if (lookForOperator) {
            const length = branchNodes.forwardBranch.length;
            for (let i = 0; i < length; i++) {
              if (
                branchNodes.forwardBranch[i].qualifiedClassName === sClassName ||
                branchNodes.forwardBranch[i].qualifiedClassName === "sap.cdw.querybuilder.RemoveElements"
              ) {
                projection = branchNodes.forwardBranch[i];
              }
            }
          }
          if (!projection) {
            projection = nsLocal.ModelImpl.createObject(sClassName, { isProjectionNode: true }, oModel);
            isNew = true;
          }
          return {
            operation: projection,
            new: isNew,
          };
        };
        const fnGetSuccessorElement = function (element, targetNode) {
          let successorElement = element.successorElement;
          while (successorElement && successorElement.container !== targetNode) {
            successorElement = successorElement.successorElement;
          }
          return successorElement;
        };
        const oModel = oJoinNode.resource.model;
        const oJoinPredecessors = oJoinNode.predecessorNodes;
        let oNode1;
        let oNode2;
        let bResolveDuplicateByRemove = true; // tells in case of join and there duplicate we reolve by removing duplicates
        // let bKeepFirstNodeElements = true; // tells in case of join and there duplicate elements we keep those from first entity

        if (oJoinPredecessors && oJoinPredecessors.length > 0) {
          oNode1 = oJoinPredecessors.get(0);
        }
        if (oJoinPredecessors && oJoinPredecessors.length > 1) {
          oNode2 = oJoinPredecessors.get(1);
        }
        if (!oNode1 || !oNode2) {
          return;
        }
        // case of join -> check if there duplicated elements new name
        const aDuplicated = nsLocal.ModelImpl.getDuplicatedElements(oNode1, oNode2);
        if (aDuplicated.length > 0) {
          const oOptions: any = {
            requestId: "JoinDuplicateElements",
            node1: oNode1,
            node2: oNode2,
          };
          // this will publish an event.. actually there is no receiver
          // is needed in case if we want to branch UI to let user choose between remove or rename
          // duplicated elements. subscriber need to be in editor extension.
          nsLocal.ModelImpl.requestUserAction(oOptions);
          // bKeepFirstNodeElements = oOptions.keepFirstNodeElements !== undefined ? oOptions.keepFirstNodeElements : true;
          bResolveDuplicateByRemove =
            oOptions.resolveDuplicateByRemove !== undefined ? oOptions.resolveDuplicateByRemove : true;

          let oRemoveOrRename = fnGetOrCreateSuccessorNode(oJoinNode);
          if (oRemoveOrRename.new) {
            const oOperation = oRemoveOrRename.operation as sap.cdw.commonmodel.BaseObject;
            nsLocal.ModelImpl.setDefaultObjectName(oOperation);
            nsLocal.ModelImpl.insertNode(oJoinNode, oOperation);
          }
          oRemoveOrRename = oRemoveOrRename.operation;
          nsLocal.ModelImpl.autoRemoveDuplicateElements(
            /* oNode*/ undefined,
            bResolveDuplicateByRemove,
            aDuplicated,
            (elt) => fnGetSuccessorElement(elt, oRemoveOrRename)
          );
        }

        // get Association from csn and create join with mappings if defined
        const result = nsErmodeler.ModelImpl.getAssociationsAndMappings(oNode1, oNode2, /* isJoin*/ true);
        if (result.isAssociated && result.mappings && result.mappings.length > 0) {
          nsLocal.ModelImpl.createJoinElementMapping(oJoinNode, result.mappings);
          if (oJoinNode.leftCardinality === "" && oJoinNode.rightCardinality === "") {
            oJoinNode.leftCardinality = CardinalityValues.MANY;
            oJoinNode.rightCardinality = CardinalityValues.ONE;
          }
        }

        oJoinNode.proposeDefaultMappings();
        // when a join is created, index order of elements are not ordered since
        // elements come from two entities --> ensure uniquness of indexOrder property
        nsLocal.ModelImpl.fixElementsIndexOrder(oJoinNode);
      },

      autoRemoveDuplicateElements: function (oNode, bResolveDuplicateByRemove, aDuplicatedElements?, fnElementToFix?) {
        const elements = aDuplicatedElements || nsLocal.ModelImpl.getDuplicatedElements(oNode);
        const nLength = elements.length;
        const fnGetAbsoluteName = function (oElement) {
          const container = oElement.container;
          if (container.isEntity) {
            return `${container.aliasOrShortName}.${oElement.newName}`;
          }
          return `${container.name}.${oElement.newName}`;
        };
        for (let index = 0; index < nLength; index++) {
          const bUpdateFirst = elements[index].element1.isKey && !elements[index].element2.isKey ? false : true; // Fix FPA101-5419
          const element = bUpdateFirst ? elements[index].element1 : elements[index].element2;
          const renameOrDeleteElement = fnElementToFix ? fnElementToFix(element) : element;
          if (
            renameOrDeleteElement &&
            elements[index].element1 &&
            !elements[index].element1.isRemoved &&
            elements[index].element2 &&
            !elements[index].element2.isRemoved
          ) {
            if (bResolveDuplicateByRemove !== false) {
              renameOrDeleteElement.isRemoved = true;
            } else {
              renameOrDeleteElement.newName = fnGetAbsoluteName(renameOrDeleteElement);
            }
          }
        }
      },

      /**
       * Fixes elements index order
       * Makes sure that each element has a unique index order property
       * @param oNode operator node
       */
      fixElementsIndexOrder: function (oNode) {
        const aElements = oNode && oNode.orderedElements;
        let indexOrder = 0;
        aElements.forEach((oElement) => {
          oElement.indexOrder = indexOrder++;
        });
      },

      /**
       * Sets the default name and display name.
       * @function
       * @name setDefaultObjectName
       * @memberOf sap.galilei.ui.editor.DiagramEditorExtension#
       * @param {Object} oObject The object.
       */
      setDefaultObjectName: function (oObject) {
        if (oObject) {
          const model = oObject.resource.model;
          const oSearch: any = {
            qualifiedClassName: oObject.qualifiedClassName,
          };
          if (model) {
            oSearch.container = model;
          }
          const allClassObjectsOfContainer = oObject.resource.selectAllObjects(oSearch);

          let count = 1,
            name;
          while (oObject.name === undefined) {
            name = `${oObject.classDefinition.displayName} ${count}`;
            const isNameAssigned = allClassObjectsOfContainer.filter((object) => object.name === name).length > 0;
            if (!isNameAssigned) {
              oObject.name = name;
            } else {
              count++;
            }
          }
          if (oObject.displayName === undefined) {
            oObject.displayName = `${oObject.classDefinition.displayName} ${count}`;
          }
        }
      },

      setDefaultEntityAlias: function (oEntity, oModel) {
        const aNames = {};
        const sName = oEntity && oEntity.name;
        const setUniqueAlias = function (entity) {
          let n = 1;
          let sAlias = "";
          const name = entity && entity.name;
          do {
            sAlias = `${name}(${n++})`;
          } while (aNames[sAlias]);
          aNames[sAlias] = {
            actualName: sAlias,
          };
          entity.alias = sAlias;
        };

        // Visit all entity names/aliases
        if (oModel && oModel.entities) {
          oModel.entities.forEach((entity) => {
            if (entity !== oEntity) {
              const sActualName = entity && (entity.alias || entity.name);
              aNames[sActualName] = {
                actualName: sActualName,
                entity: entity,
              };
            }
          });
        }
        // should we need suggest an alias?
        const duplicate = aNames[sName];
        if (duplicate) {
          if (duplicate.entity) {
            // we give an alias also to duplicated one
            setUniqueAlias(duplicate.entity);
          }
          setUniqueAlias(oEntity);
        }
      },

      /**
       *
       * @function Post-process the drop action from the catalog tree view
       * @name onDropDataSource
       * @param {*} oEntity The entity object automatically created
       * @param {*} oDropParams additional information retrieved from the drop action
       * @param {Boolean} isDimension oEntity is demension source for Association view diagram
       */
      onDropDataSource: function (oEntity, oDropParams, isDimension) {
        const oResource = oEntity && oEntity.resource;
        const oModel = oResource && oResource.model;
        const oDeferred = jQuery.Deferred();

        // Initialize entity container
        if (oEntity && !oEntity.container && oModel) {
          // Set default alias..
          nsLocal.ModelImpl.setDefaultEntityAlias(oEntity, oModel);
          oModel.nodes.push(oEntity);
        }

        // Copy file info
        if (oEntity && oDropParams) {
          RepositoryUtils.updateObjectFileInfo(oEntity, oDropParams);
        }

        const dropDefinitions = oDropParams.definitions || (oDropParams.csn && oDropParams.csn.definitions);
        // Create elements
        if (oEntity && dropDefinitions && typeof dropDefinitions === "object") {
          const { entityName, contextName } = getEntityAndContextNameFromCSN(dropDefinitions);

          if (entityName && dropDefinitions[entityName]) {
            oEntity.delta = dropDefinitions[entityName]["@DataWarehouse.delta"];
            oEntity.name = entityName;
            oEntity.displayName = entityName;
            if (dropDefinitions[entityName].then instanceof Function) {
              dropDefinitions[entityName].then((definition) => {
                oEntity.resource.applyUndoableAction(
                  () => {
                    extractDropData(definition);
                    nsLocal.ModelImpl.publishasyncElementsLoadedEvent({
                      // needed to refresh the UI
                      object: oEntity,
                    });
                    nsLocal.ModelImpl.publishDroppedEntityEvent({
                      // needed to refresh the UI
                      object: oEntity,
                      data: oDropParams,
                    });
                  },
                  "Complete loading entity data",
                  undefined,
                  /* batchMode*/ true
                );
              });
            } else {
              extractDropData(dropDefinitions[entityName], contextName);
              nsLocal.ModelImpl.publishDroppedEntityEvent({
                // needed to refresh the UI
                object: oEntity,
                data: oDropParams,
              });
            }
          }
        }

        function extractDropData(csnEntity, contextName?) {
          oEntity.csn = csnEntity;
          const dataCategory = csnEntity && DataCategoryAnnotation.readCsn(csnEntity);
          nsLocal.ModelImpl.loadElements(csnEntity.elements, oEntity, oDropParams, dataCategory);
          // Load unresolved associations..
          if (oEntity.classDefinition?.name !== "DimensionNode") {
            loadUnresolvedAssociations(oEntity, csnEntity);
          }
          oDeferred.resolve({});
          const csnToModel = new nsLocal.CsnToModel();
          csnToModel.getDefaultPropertyMapping(oEntity, csnEntity);
          nsErmodeler.ModelImpl.updateEntityFromDropDefinitions(oEntity, csnEntity);

          // Manage aliases
          const shortName = oEntity.shortName;
          if (oModel && oEntity.remote && oEntity.remote.table && !isDimension) {
            if (!oEntity.alias && shortName !== oEntity.qualifiedName) {
              oEntity.alias = nsLocal.ModelImpl.computeUniqueAlias(oModel.entities, shortName);
            }
          }

          if (oModel && contextName && !oEntity.alias && shortName !== oEntity.qualifiedName) {
            oEntity.alias = nsLocal.ModelImpl.computeUniqueAlias(oModel.entities, shortName);
          }

          const oErCsnToModel = sap.cdw.ermodeler.ErCsnToModel.getInstance(true);
          oEntity.isNew = true;
          // Add the entity in the imported objects map
          oErCsnToModel.addObjectMap(oEntity.name, oEntity);
          // Create contexts and simple types and attach them to objects
          oErCsnToModel.importObjectsFromCSN(oModel, dropDefinitions, ["type"]);
          if (contextName) {
            const oErCsnToModel2 = sap.cdw.ermodeler.ErCsnToModel.getInstance(true);
            oErCsnToModel2.createContextAndAttachToObject(
              oModel,
              oEntity,
              contextName,
              dropDefinitions[contextName],
              /* bForce*/ true
            );
          }
        }
        return oDeferred;
      },

      getCsnToModel: function () {
        const csnToModel = new nsLocal.CsnToModel();
        return csnToModel;
      },

      /**
       * @function Post-process the drop action from the catalog tree view
       * @name onDropDimension
       * @param {*} oEntities The list of all entities
       * @param {*} sName base short name for computing the alias
       */
      computeUniqueAlias: function (oEntities, sName) {
        let suffixNumber = 1;
        // Remove non-alphanumreric characters
        let sAlias = nsLocal.ModelImpl.convertToAlphaNumeric(sName);
        sAlias = sAlias.replace(/__/g, "_");
        // Manage uniqueness
        while (
          oEntities.selectObject({
            alias: `${sAlias}${suffixNumber > 1 ? suffixNumber : ""}`,
          })
        ) {
          suffixNumber++;
        }
        return suffixNumber > 1 ? sAlias + suffixNumber : sAlias;
      },

      /**
       * remove non alphanumeirc characters from a name
       */
      convertToAlphaNumeric: function (sName) {
        let sNewName = sName.replace(/[^a-zA-Z0-9_]/g, "_");
        sNewName = sNewName.replace(/__*/g, "_");
        return sNewName;
      },

      /**
       * @function Post-process the drop action from the catalog tree view
       * @name onDropDimension
       * @param {*} oEntity The entity object automatically created
       * @param {*} oDropParams additional information retrieved from the drop action
       */
      onDropDimension: function (oEntity, oDropParams) {
        this.onDropDataSource(oEntity, oDropParams, /* isDimension*/ true);
      },

      onPostDropDimensionLink: function () {
        // No ESLint
      },

      /**
       * Resets the calculated element expression.
       * @param {*} oCalculatedElement calc element
       */
      resetCalcElementExpression: function (oCalculatedElement) {
        const oContainer = oCalculatedElement.container,
          oResource = oContainer.resource,
          aPredecessorNodes = oContainer.predecessorNodes,
          oPredecessorNode = aPredecessorNodes.length > 0 ? aPredecessorNodes.get(0) : undefined,
          oPredecessorElement =
            oPredecessorNode &&
            nsLocal.NodeImpl.getOnePredecessorElement(oPredecessorNode, oContainer, oCalculatedElement);

        if (oPredecessorElement) {
          oResource.applyUndoableAction(
            function () {
              oCalculatedElement.setParsedExpression();
            },
            "Clear parsed expressions",
            undefined,
            /* protectedFromUndo*/ true
          );
          oCalculatedElement.expression = sap.cdw.querybuilder.ViewModelToCsn.quoteName(oPredecessorElement.newName);
          nsLocal.ViewModelToCsn.parseAllNodeExpressions(oPredecessorNode, [
            {
              expression: oCalculatedElement.expression,
              object: oCalculatedElement,
            },
          ]);
        }
      },

      _checkNodeExists: function (oModel, oNode) {
        let targetNode;
        if (oModel && oModel.nodes) {
          const nodes = oModel.nodes;
          const count = nodes.length;
          for (let i = 0; i < count; i++) {
            const node = nodes.get(i);
            if (node.qualifiedName === oNode.originalEntityName) {
              targetNode = node;
              break;
            }
          }
        }
        return targetNode;
      },

      createDimension: function (oModel, params) {
        const oTargetNode = nsLocal.ModelImpl.createObject(
          "sap.cdw.querybuilder.DimensionNode",
          {
            name: params.name,
            qualifiedName: params.originalEntityName,
          },
          oModel
        );
        if (oTargetNode) {
          nsLocal.ModelImpl.createElementsObject(params.elements, oTargetNode);
        }

        return oTargetNode;
      },

      /**
       * Loads elements from drop event data
       * Elements by be a Promise object or an array.
       * Limitation: Since in case of a promise creation of elements is delayed, as a side
       * effect there are two actions and to undo the action user needs to undo twice
       * (first for element and second for the entity)
       */
      loadElements: function (oPromiseElements, oTargetNode, oDropParams, dataCategory?) {
        sap.cdw.ermodeler.ModelImpl.handleEntitySaveAndDeployStatus(oTargetNode, oDropParams);
        if (oPromiseElements.then instanceof Function) {
          // Promise case
          oPromiseElements.then(function (oElements) {
            nsLocal.ModelImpl.createElementsObject(oElements, oTargetNode);
            // nsLocal.ModelImpl.createElementsObject(oElements, oTargetNode, dataCategory);  // dataCategory not used by function's code
            nsLocal.ModelImpl.publishasyncElementsLoadedEvent({
              // needed to refresh the UI
              object: oTargetNode,
            });
          });
        } else {
          nsLocal.ModelImpl.createElementsObject(oPromiseElements, oTargetNode); // dataCategory not used by function's code
          // nsLocal.ModelImpl.createElementsObject(oPromiseElements, oTargetNode, dataCategory);  // dataCategory not used by function's code
        }
      },

      createElementsObject: function (elements, targetNode) {
        // createElementsObject: function(elements, targetNode, dataCategory?) {  // dataCategory not needed?
        const oResource = targetNode.resource,
          aKeys = Object.keys(elements);
        oResource.applyUndoableAction(
          function () {
            const postProcessings = [];
            for (const aKey of aKeys) {
              const bFormulaElement = isAnalyticMeasureElement(elements[aKey]);
              if (bFormulaElement) {
                continue;
              }
              const obj: any = {};
              Object.assign(obj, elements[aKey]);
              obj.isKey = elements[aKey].key;
              obj.dataType = elements[aKey].type || elements[aKey].dataType;
              obj.name = aKey;
              obj.newName = aKey;
              obj.label = elements[aKey]["@EndUserText.label"]?.trim();
              // Skip Associations!!
              if (obj.dataType !== CDSDataType.ASSOCIATION) {
                const oElement = nsLocal.ModelImpl.createObject("sap.cdw.querybuilder.Element", obj, targetNode);
                const instance = new nsLocal.CsnToModel();
                const postProcessingElement = instance.getDefaultPropertyMapping(oElement, elements[obj.name]);
                postProcessings.push({
                  object: oElement,
                  postProcessings: postProcessingElement,
                });
              }
            }
            // post processing elements (actions that need to be done when all elements are loaded)
            // Process post actions of elements (columnLabel for instance)
            const length = postProcessings ? postProcessings.length : 0;
            const csnToModel = sap.cdw.commonmodel.CsnToModel.getInstance();
            for (let i = 0; i < length; i++) {
              csnToModel.postProcessElement(targetNode, postProcessings[i].object, postProcessings[i].postProcessings);
            }
          },
          "Create elements",
          /** protected from undo/redo */ false,
          /* batchMode*/ true
        );
      },

      createOrMergeAssociation: function (parentNode, associtionObj) {
        if (parentNode.associations) {
          const associationExists = nsLocal.ModelImpl.checkObjectExists(parentNode.associations, associtionObj);
          if (!associationExists) {
            parentNode.associations.push(associtionObj);
          }
        }
      },

      processAssociationFromRepository: function (oObject, oEditor) {
        if (oEditor.previewService) {
          return oEditor.previewService.getRepositoryObject(oObject.name).then(function (sourceRepoObj) {
            return oEditor.previewService
              .getDependencies(
                {
                  id: sourceRepoObj.id,
                },
                false,
                false,
                true
              )
              .then(function (dependencies) {
                let result;
                let obj;
                if (dependencies.length && dependencies.length === 1) {
                  obj = dependencies[0];
                }
                const oDependencies = obj && obj.dependencies;
                if (oDependencies && oDependencies.length) {
                  const dependentEntities = [];
                  oDependencies.filter(function (oDependentObj) {
                    if (oDependentObj.dependencyType === "csn.entity.association") {
                      dependentEntities.push(oDependentObj);
                    }
                  });
                  if (dependentEntities.length > 0) {
                    return oEditor.previewService
                      .getRepositoryObject(dependentEntities[0].qualifiedName)
                      .then(function (oSelectedObject) {
                        result = {
                          selectedRepoObj: oSelectedObject,
                          parentObject: oObject,
                          sourceRepoObj: sourceRepoObj,
                        };
                        return result;
                      });
                  } else {
                    return [];
                  }
                }
              });
          });
        }
      },

      performParameterMapping: function (parameters, oNode) {
        // const self = this;
        parameters.forEach((param) => {
          if (
            param.selectedType === "param" &&
            param.sourceParameter !== undefined &&
            param.targetParameter !== undefined
          ) {
            if (typeof param.targetParameter === "string") {
              const uniqueBName = this.getUniqueParameterName(oNode, param.sourceParameter.displayName, false);
              const uniqueTName = this.getUniqueParameterName(oNode, param.sourceParameter.name, true);
              const oClass = sap.galilei.model.getClass("sap.cdw.querybuilder.ViewParameter");
              const oOutputNode = oNode.output;
              const newTargetViewParameter = oClass.create(oOutputNode.resource, {
                displayName: uniqueBName,
                name: uniqueTName,
                length: param.sourceParameter.length,
                dataType: param.sourceParameter.dataType,
                defaultValue: param.sourceParameter.defaultValue,
                valueHelpDefinition: param.sourceParameter.valueHelpDefinition,
              });
              const csnToModel = new nsLocal.CsnToModel();
              csnToModel.getDefaultPropertyMapping(newTargetViewParameter, param.sourceParameter);
              oOutputNode.parameters.push(newTargetViewParameter);
              param.targetParameter = newTargetViewParameter;
            }
            param.targetParameter.sourceParameter = param.sourceParameter;
            param.sourceParameter.targetParameter = param.targetParameter;
            param.targetParameter.defaultValue = param.sourceParameter.defaultValue;
            param.sourceParameter.value = undefined;
            param.targetParameter.valueHelpDefinition = param.sourceParameter.valueHelpDefinition;
          } else if (param.selectedType === "default" && param.value !== undefined) {
            param.sourceParameter.value = param.value;
            param.sourceParameter.targetParameter = undefined;
          }
        });
      },

      checkObjectExists: function (collection, target) {
        return (
          collection.selectObject({
            name: target.name,
          }) !== undefined
        );
      },

      deleteObject: function (obj, tryFromRelatedSymbols: boolean = true) {
        if (tryFromRelatedSymbols) {
          nsLocal.ModelImpl.requestDeleteObjectAndSymbols(obj);
        } else {
          obj?.deleteObject();
        }
      },

      /**
       * Create Join node using suggestion object
       * @param {*} obj - source object - {name, csn, id} parameter
       * @param {*} oParentObj - Parent object to create join
       * @param {*} sourceRepoObj - repoObject {name, id, csn}
       * @param {*} oExistingEntity - entity exists in diagram
       */
      createJoinUsingSuggestion: function (obj, oParentObj, sourceRepoObj, oExistingEntity, oAssociatedInfo) {
        if (!(obj && obj.csn)) {
          return;
        }
        // Create Table Symbol and Create Join
        // eslint-disable-next-line no-underscore-dangle
        const sObjectClassName = nsLocal.DiagramImpl._getObjectClassName("sap.cdw.querybuilder.ui.EntitySymbol");
        if (sObjectClassName) {
          const oObjectParam = {
            name: obj.name,
          };
          const associatedInfo = {
            isAssociated: true,
            impact: oAssociatedInfo.impact,
            lineage: oAssociatedInfo.lineage,
          };
          if (oExistingEntity) {
            nsLocal.ModelImpl.onPostDropDataSource(oParentObj, oExistingEntity, "Join", associatedInfo);
          } else {
            const oObject = nsLocal.ModelImpl.createObject(sObjectClassName, oObjectParam, oParentObj.resource.model);
            oObject.leftCardinality = CardinalityValues.MANY;
            oObject.rightCardinality = CardinalityValues.ONE;
            const oElementsLoaded = nsLocal.ModelImpl.onDropDataSource(oObject, obj.csn);
            RepositoryUtils.updateObjectFileInfo(oObject, obj);
            jQuery.when(oElementsLoaded).done(function () {
              nsLocal.ModelImpl.onPostDropDataSource(oObject, oParentObj, "Join", associatedInfo);
            });
          }
        }
      },

      createJoinElementMapping: function (oJoin, mappingArray) {
        const oJoinPredecessors = oJoin.predecessorNodes;

        let oJoinEntity1;
        if (oJoinPredecessors && oJoinPredecessors.length > 0) {
          oJoinEntity1 = oJoinPredecessors.get(0);
        }

        let oJoinEntity2;
        if (oJoinPredecessors && oJoinPredecessors.length > 1) {
          oJoinEntity2 = oJoinPredecessors.get(1);
        }

        for (const mapping of mappingArray) {
          const srcElems =
            oJoinEntity1 &&
            oJoinEntity1.elements &&
            oJoinEntity1.elements.filter(function (obj) {
              return obj.name === mapping.source;
            });
          const targetElems =
            oJoinEntity2 &&
            oJoinEntity2.elements &&
            oJoinEntity2.elements.filter(function (obj) {
              return obj.name === mapping.target;
            });
          const oElement1 = srcElems && srcElems.length && srcElems[0];
          const oElement2 = targetElems && targetElems.length && targetElems[0];
          if (oElement1 && oElement2) {
            // create element mappins
            const oClass = sap.galilei.model.getClass("sap.cdw.commonmodel.ElementMapping");
            if (oClass) {
              const oElementMapping = oClass.create(oJoin.resource, {
                source: oElement1,
                target: oElement2,
              });
              oJoin.mappings.push(oElementMapping);
            }
          }
        }
      },

      /**
       * Create a New parameter and adds to ouput.
       */
      createNewParameterElement: function (oNode, isForAnalyticParameters?) {
        const businessName = this.getUniqueParameterName(oNode, "PARAM", false, 1);
        const technicalName = this.getUniqueParameterName(oNode, "PARAM", true, 1);
        const oClass = sap.galilei.model.getClass(
          isForAnalyticParameters ? "sap.cdw.commonmodel.AnalyticParameter" : "sap.cdw.querybuilder.ViewParameter"
        );
        const oClassReferenceEntity = sap.galilei.model.getClass("sap.cdw.commonmodel.ReferenceEntity");
        const valueHelpElement = oClassReferenceEntity.create(oNode.resource, {});
        const oParamElement = oClass.create(oNode.resource, {
          name: technicalName,
          label: businessName,
          length: 10,
          valueHelpType: "NoValueHelp",
          default: "",
          valueHelpDefinition: valueHelpElement,
        });
        if (isForAnalyticParameters) {
          const oAttribute = oNode.output.dimensionElements.get(0); // Set the 1st element as ref element by default
          sap.cdw.commonmodel.ObjectImpl.setAnalyticParameterReferenceElement(oParamElement, oAttribute);
          sap.cdw.commonmodel.ObjectImpl.setAnalyticParameterDataTypeByReferenceElement(oParamElement, oAttribute);
          oNode.output.analyticParameters.push(oParamElement);
        } else {
          oNode.output.parameters.push(oParamElement);
        }
        return oParamElement;
      },

      getUniqueParameterName: function (oNode, name: string, isTechnicalName: boolean, nIndex) {
        const nameValidator = NamingHelper.getNameInputValidator();
        const aParamElements = oNode.output.analyticParameters.toArray().concat(oNode.output.parameters.toArray()), // DW101-4190 uniqueness of the parameters (does not matter if it is a parameter or analytical parameter)
          aParameterNames = aParamElements.map(function (oElement) {
            if (oElement) {
              return oElement.name;
            }
          }),
          aElementsName = oNode.output.elements.map(function (oElement) {
            if (oElement) {
              return oElement.name;
            }
          }),
          aAllTechnicalElements = [...aParameterNames, ...aElementsName];
        const aParameterbNames = aParamElements.map(function (oElement) {
            if (oElement) {
              return oElement.displayName;
            }
          }),
          aElementsbName = oNode.output.elements.map(function (oElement) {
            if (oElement) {
              return oElement.displayName;
            }
          }),
          aAllbusinessElements = [...aParameterbNames, ...aElementsbName];

        if (!nIndex) {
          nIndex = 0;
        }
        let sName = name;

        if (nIndex > 0) {
          if (isTechnicalName && name && name.length >= 10) {
            const n = nIndex.toString().length;
            const strToReplace = name.substr(name.length - n, name.length);
            name = name.replace(new RegExp(strToReplace + "$"), nIndex);
            sName = name;
          } else {
            sName = `${name}_${nIndex}`;
            if (isTechnicalName) {
              sName = nameValidator.deriveTechnicalName(sName, NameUsage.parameter);
            }
          }
        }

        if (isTechnicalName && aAllTechnicalElements.includes(sName)) {
          return this.getUniqueParameterName(oNode, name, isTechnicalName, nIndex + 1);
        } else if (aAllbusinessElements.includes(sName)) {
          return this.getUniqueParameterName(oNode, name, isTechnicalName, nIndex + 1);
        }
        if (isTechnicalName) {
          return nameValidator.deriveTechnicalName(sName, NameUsage.parameter);
        } else {
          return sName;
        }
      },

      createNewValueHelpEntityForParameter: function (oNode) {
        const oClass = sap.galilei.model.getClass("sap.cdw.commonmodel.ReferenceEntity");
        const valueHelpElement = oClass.create(oNode.resource, {});
        oNode.valueHelpDefinition.push(valueHelpElement);
      },

      autoProposeDataCategory: function (oModel, oDroppedEntity) {
        const oOutput = oModel.output;
        const projectedElements = oDroppedEntity?.projectedOutputElements;
        const postProcessingsArray = [];
        if (projectedElements) {
          const checkMeasureAndAdd = function () {
            let isMeasureExist = false;
            projectedElements.map((projectedElement) => {
              const oElem = projectedElement.outputElement;
              const oSourceElement = projectedElement.sourceElement;
              const oSourceEntity = oSourceElement && oSourceElement.container;
              const oCsn = oSourceEntity && oSourceEntity.csn;
              const oCsnElement = oCsn && oCsn.elements[oSourceElement.name];
              if (oCsnElement && oSourceEntity === oDroppedEntity) {
                if (isCsnMeasure(oCsnElement)) {
                  if (
                    !oModel.output.isDataCategoryChanged ||
                    oOutput.dataCategory === DataCategory.FACT ||
                    oOutput.dataCategory === DataCategory.SQLFACT
                  ) {
                    isMeasureExist = true;
                    oElem.isMeasure = true;
                  }
                }
                // Add labelElement and unitTypeElement
                const postProcessings = [];
                if (oSourceElement.labelElement !== undefined) {
                  postProcessings.push({
                    labelColumn: oSourceElement.labelElement.name,
                  });
                }
                if (oSourceElement.unitTypeElement !== undefined) {
                  postProcessings.push({
                    unitTypeElement: oSourceElement.unitTypeElement.name,
                  });
                }
                if (postProcessings.length) {
                  postProcessingsArray.push({
                    object: oElem,
                    postProcessings: postProcessings,
                  });
                }
              }
            });
            if (
              isMeasureExist ||
              oOutput.dataCategory === DataCategory.FACT ||
              oOutput.dataCategory === DataCategory.SQLFACT
            ) {
              const length = postProcessingsArray ? postProcessingsArray.length : 0;
              const csnToModel = sap.cdw.commonmodel.CsnToModel.getInstance();
              for (let i = 0; i < length; i++) {
                csnToModel.postProcessElement(
                  oOutput,
                  postProcessingsArray[i].object,
                  postProcessingsArray[i].postProcessings
                );
              }
            }
            return isMeasureExist;
          };
          const droppedEntityCategory = oDroppedEntity?.dataCategory;
          if (droppedEntityCategory === DataCategory.FACT || droppedEntityCategory === DataCategory.SQLFACT) {
            if (checkMeasureAndAdd()) {
              // https://jira.tools.sap/browse/DW101-35840
              // even droppedEntity is a ADS with measure, we still propose output as a Fact
              oOutput.dataCategory = DataCategory.SQLFACT;
            }
          }
        }
      },

      /**
       * onPostPasteEntity -  Remove successor reference from pasted node
       * @param oNode - Entity object
       */
      onPostPasteEntity: function (oNode) {
        nsLocal.NodeImpl.stopPropagating = true;
        if (oNode) {
          if (oNode && oNode.successorNode) {
            oNode.successorNode = undefined;
          }
          oNode.elements?.forEach((el) => (el.successorElement = undefined));
        }
        nsLocal.NodeImpl.stopPropagating = false;
      },

      setEntityAliasForContext: function (oModel) {
        const aNames = {};
        const changedEntities = [];
        const setUniqueAlias = function (entity, name, changedEntities) {
          let n = 1;
          let sAlias = name;
          while (aNames[sAlias]) {
            sAlias = `${name}_${n++}`;
          }
          aNames[sAlias] = {
            actualName: sAlias,
          };
          changedEntities.push({
            entity: entity,
            alias: entity.alias,
          });
          entity.alias = sAlias;
        };

        if (oModel && oModel.entities) {
          oModel.entities.forEach((entity) => {
            const sActualName = entity && (entity.alias || entity.name);
            aNames[sActualName] = {
              actualName: sActualName,
              entity: entity,
            };
          });
          oModel.entities.forEach((entity) => {
            const Name = entity && (entity.alias || entity.name);
            if (Name.indexOf(".") !== -1 && !(entity.alias !== "" && entity.alias !== undefined)) {
              const alias = entity.shortName; // get only entity name
              setUniqueAlias(entity, alias, changedEntities);
            }
          });
        }
        return changedEntities;
      },

      removeEntityAliasForContext: function (changedEntities) {
        if (changedEntities && changedEntities.length) {
          changedEntities.forEach((entry) => {
            entry.entity.alias = entry.alias;
          });
        }
      },

      localizeText: function (textId: string, parameters: string[]) {
        const bundleName = require("../../i18n/i18n.properties");
        const resourceModel =
          sap?.ui?.model?.resource &&
          new sap.ui.model.resource.ResourceModel({
            bundleName: bundleName,
          });
        return resourceModel ? resourceModel.getResourceBundle().getText(textId, parameters) : textId;
      },

      swapJoinInput: function (oJoin) {
        let workbenchController;
        // FIXME: better way to avoid getWorkbenchController() in this file!
        try {
          workbenchController = getWorkbenchController();
        } catch (e) {
          // eslint-disable-next-line no-empty
        }
        if (workbenchController?.isSecondaryEditorActive && workbenchController?.secondaryEditor) {
          const oModel = oJoin?.container;
          oModel?.validate();
        }
        oJoin.resource.applyUndoableAction(function () {
          const oJoinEntity1 = oJoin.leftInput;
          const oJoinEntity2 = oJoin.rightInput;
          oJoin.leftInput = oJoinEntity2;
          oJoin.rightInput = oJoinEntity1;

          const mappings = oJoin.mappings;
          const nLength = mappings ? mappings.length : 0;
          for (let i = 0; i < nLength; i++) {
            const oMapping = mappings.get(i);
            if (!oMapping.source || !oMapping.target) {
              continue;
            }
            if (oMapping.source.container === oJoinEntity1 || oMapping.target.container === oJoinEntity2) {
              const element = oMapping.source;
              oMapping.source = oMapping.target;
              oMapping.target = element;
            }
          }

          const oModel = oJoin.resource.model;
          const oDiagram = oModel && nsLocal.DiagramImpl.getDiagram(oModel);
          if (oDiagram) {
            const aAssociationSymbols = oDiagram.symbols.selectAllObjects({
              "classDefinition.name": "AssociationSymbol",
            });
            for (let i = 0; i < aAssociationSymbols.length; i++) {
              const associationSymbol = aAssociationSymbols.get(i);
              if (
                associationSymbol.sourceSymbol &&
                associationSymbol.targetSymbol &&
                associationSymbol.targetSymbol.object === oJoin
              ) {
                if (associationSymbol.sourceSymbol.object === oJoinEntity1) {
                  associationSymbol.isLeftInput = false;
                } else if (associationSymbol.sourceSymbol.object === oJoinEntity2) {
                  associationSymbol.isLeftInput = true;
                }
              }
            }
          }

          const temp = oJoin.leftCardinality;
          oJoin.leftCardinality = oJoin.rightCardinality;
          oJoin.rightCardinality = temp;
        }, "Swap Join inputs");
      },
    },
  });

  /**
   * Edit with Command for table or view
   */
  nsLocal.EditWithCommand = sap.galilei.core.defineClass({
    fullClassName: "sap.cdw.querybuilder.EditWithCommand",
    parent: sap.galilei.ui.common.command.Command,
    methods: {
      execute: function (oParam) {
        const oObject = oParam && oParam.symbol && oParam.symbol.object;
        const oEditor = oParam && oParam.editor;
        const oWorkbenchController = oEditor && oEditor.previewService;

        if (
          oObject &&
          oObject.classDefinition.name === "Entity" &&
          oWorkbenchController &&
          oWorkbenchController.spaceId
        ) {
          // change the name of the entity to the enclosing object name when it is a delta table
          if (oWorkbenchController?.isSecondaryEditorActive && oObject.isDeltaTable) {
            oObject.name =
              oObject.csn?.["@DataWarehouse.enclosingObject"] ||
              oObject.repositoryCSN?.["@DataWarehouse.enclosingObject"];
          }
          // Open the table or view in new tab
          openInNewTab(undefined, oObject, oObject?.crossSpaceName);
        }
      },
    },
  });
  sap.galilei.ui.common.command.CommandRegistry.add(
    "sap.cdw.querybuilder.EditWithCommand",
    new nsLocal.EditWithCommand()
  );

  /**
   * Data Preview Command
   */
  nsLocal.DataPreviewCommand = sap.galilei.core.defineClass({
    fullClassName: "sap.cdw.querybuilder.DataPreviewCommand",
    parent: sap.galilei.ui.common.command.Command,
    methods: {
      canExecute: function (context: { editor: any; diagram: any; symbol?: any }) {
        return sap.cdw.components.reuse.control.circuitbreaker.hanaEnabledFormatter();
      },
      execute: function (oParam) {
        const oObject = oParam && oParam.symbol && oParam.symbol.object;
        const oModel = oObject && oObject.container;
        const oEditor = oParam && oParam.editor;
        const oPreviewService = oEditor && oEditor.previewService;
        const activeEditor = oPreviewService.getActiveEditor();
        const isModelValidationEnabled = SupportedFeaturesService.getInstance().isModelValidationEnabled();
        if (isModelValidationEnabled && activeEditor?.isNewBottomTabSupported()) {
          if (oObject?.qualifiedClassName === "sap.cdw.querybuilder.Output") {
            oPreviewService.getBottomDetailsManager().updateBottomViewsAndToolbar();
          } else {
            oPreviewService.getBottomDetailsManager().updateBottomViewsAndToolbar(undefined, true);
          }
        }

        if (oPreviewService) {
          // Show datapreview
          oPreviewService.showDetails();
        }

        if (oPreviewService && oModel && oObject.elements) {
          const sEntityName = oObject.alias || oObject.shortName || oObject.technicalName || oObject.name;
          // Loads metadata.js
          if (!nsLocal.ViewModelToCsn.metadata) {
            nsLocal.ViewModelToCsn.metadata = require("../../../../services/metadata");
          }
          const isOldPreview = !!activeEditor?.getDetailsConfiguration()?.useDefault?.useAlwaysDefault;

          nsLocal.ViewModelToCsn.getCSN(sEntityName, oModel, {
            rootObject: oObject,
            dataPreview: true,
            isMDMPreview: !isOldPreview,
          }).then(
            function (oCsn) {
              if (oCsn && oCsn.definitions) {
                const aEntities = oModel.resource.selectAllObjects({ "classDefinition.name": "Entity" });
                aEntities.forEach(function (oEntity) {
                  if (oEntity.csn && !oCsn.definitions[oEntity.name]) {
                    const cloneCsn = cloneJson(oEntity.csn);
                    handleCsnForDataPreview(cloneCsn);
                    oCsn.definitions[oEntity.name] = cloneCsn;
                  }
                });
                const notDeployed =
                  (oObject.isTable || oObject.isView || oObject.isEntity) &&
                  oObject !== (oObject.container && oObject.container.output)
                    ? !(oObject.deploymentStatus === DeploymentStatus.Active || oObject.deploymentDate)
                    : undefined;
                // check ouput view node is persisted or not
                const isOutputPersisted = isOutputNodePersisted(oObject, oModel);
                if (isOutputPersisted) {
                  if (oPreviewService.isDirty() || (oModel && oModel.deploymentStatus === DeploymentStatus.Revised)) {
                    MessageHandler.success(oPreviewService.getText("InfodataPreviewForPersitedView"));
                  } else {
                    oCsn = handlePersistedViewCsnForDataPreview(oCsn, oObject);
                  }
                }
                oPreviewService.previewDataForObject(
                  oObject,
                  oCsn,
                  notDeployed,
                  [oObject].concat(nsLocal.NodeImpl.computeNodePredecessorsRecursive(oObject)),
                  oParam?.symbol
                );
                // Record usage
                ShellContainer.get()
                  .getUsageCollectionService()
                  .recordAction({
                    action: UsageActions.DATA_PREVIEW,
                    feature: DWCFeature.DATA_BUILDER,
                    eventtype: EventType.CLICK,
                    options: [
                      {
                        param: "target",
                        value: USAGE_VIEW_BUILDER,
                      },
                    ],
                  });
                return true;
              }
            },
            function (oError) {
              sap.m.MessageToast.show(oError.error);
            }
          );
        } else if (oPreviewService) {
          oPreviewService.resetPreviewTable(true, true);
        }

        const oContextButtonPad = oParam && oParam.source && oParam.source.contextButtonPad;
        if (oContextButtonPad) {
          setTimeout(() => {
            oContextButtonPad.showWidget();
          }, 0);
        }
      },
    },
  });
  sap.galilei.ui.common.command.CommandRegistry.add(
    "sap.cdw.querybuilder.DataPreviewCommand",
    new nsLocal.DataPreviewCommand()
  );

  /**
   * Preview SQL Command
   */
  nsLocal.PreviewSQLCommand = sap.galilei.core.defineClass({
    fullClassName: "sap.cdw.querybuilder.PreviewSQLCommand",
    parent: sap.galilei.ui.common.command.Command,
    methods: {
      execute: function (oParam) {
        const oObject = oParam && ((oParam.symbol && oParam.symbol.object) || oParam.object);
        const oModel = oObject && oObject.container;
        const oEditor = oParam && oParam.editor;
        const oPreviewService = oEditor && oEditor.previewService;

        if (oPreviewService && oModel && oObject.elements && oObject.elements.length) {
          let sEntityName;
          if (oObject.classDefinition.name === "Entity") {
            const sCoulmns = oObject.elements.toArray().reduce((acc, elem) => {
              acc = (acc !== "" ? `${acc},\n` : acc) + `  "${elem.name}"`;
              return acc;
            }, "");
            let sParams =
              oObject.parameters?.length &&
              oObject.parameters.toArray().reduce((acc, elem) => {
                const paramValue = elem.targetParameter ? `:${elem.targetParameter.name}` : `'${elem.value}'`;
                acc = (acc !== "" ? `${acc}, ` : acc) + `"${elem.name}" => ${paramValue}`;
                return acc;
              }, "");
            if (sParams && sParams !== "") {
              sParams = `(${sParams})`;
            } else {
              sParams = "";
            }
            const oCsn = {
              isEntity: true,
              sqlText: `SELECT\n${sCoulmns}\nFROM "${oObject.qualifiedName}"${sParams};\n`,
            };
            oPreviewService.onPreviewSQL({
              modelName: oObject.qualifiedName,
              oCsn,
              isEntity: true,
              predecessors: [oObject].concat(nsLocal.NodeImpl.computeNodePredecessorsRecursive(oObject)),
            });
          } else {
            sEntityName = oObject.alias || oObject.technicalName || oObject.shortName || oObject.name;
            const entitiesToReset = nsLocal.ModelImpl.setEntityAliasForContext(oModel);

            let consumedParams;
            const oOuput = oObject.resource?.model?.output;
            if (
              oObject.classDefinition.name !== "Output" &&
              oOuput &&
              oOuput.parameters &&
              oOuput.parameters.length &&
              sap?.ui?.getCore instanceof Function
            ) {
              consumedParams = getListOfConsumedInputParameters(oObject, oObject.resource.model.output);
            }

            nsLocal.ViewModelToCsn.getCSN(sEntityName, oModel, {
              rootObject: oObject,
              dataPreview: true,
              previewSQL: true,
              ...(consumedParams && consumedParams.length && { consumedParams: consumedParams }),
            })
              .then(
                async function (oCsn) {
                  if (oCsn && oCsn.definitions) {
                    const aEntities = oModel.resource.selectAllObjects({ "classDefinition.name": "Entity" });
                    aEntities.forEach(async function (oEntity) {
                      // Don't pass SQL Script views, as it will not contain queries in case of input parameter
                      const isSQLScriptView =
                        oEntity.csn &&
                        oEntity.csn["@Analytics.dbViewType"] &&
                        oEntity.csn["@Analytics.dbViewType"] === "TABLE_FUNCTION";
                      if (oEntity.csn && !isSQLScriptView) {
                        oCsn.definitions[oEntity.name] = oEntity.csn;
                      }
                      if (isSQLScriptView && oCsn.definitions[oEntity.name]) {
                        delete oCsn.definitions[oEntity.name];
                      }
                    });
                    // eslint-disable-next-line camelcase
                    oCsn.space_id = oPreviewService.spaceId;
                    ShellContainer.get()
                      .getUsageCollectionService()
                      .recordAction({
                        action: UsageActions.PREVIEW_SQL,
                        feature: DWCFeature.DATA_BUILDER,
                        eventtype: EventType.CLICK,
                        options: [
                          {
                            param: "target",
                            value: USAGE_VIEW_BUILDER,
                          },
                        ],
                      });
                    // check ouput view node is persisted or not
                    const isOutputPersisted = isOutputNodePersisted(oObject, oModel);
                    if (
                      isOutputPersisted &&
                      oModel &&
                      oModel.deploymentStatus === DeploymentStatus.Active &&
                      !oPreviewService.isDirty()
                    ) {
                      oCsn = handlePersistedViewCsnForSQLPreview(oCsn, oObject);
                      const objectName = oObject.technicalName ? oObject.technicalName : oObject.name;
                      // Dummy View name with persisted suffix for preview sql
                      sEntityName = `${objectName}_Persisted`;
                    }
                    oPreviewService.onPreviewSQL({
                      modelName: sEntityName,
                      oCsn,
                      predecessors: [oObject].concat(nsLocal.NodeImpl.computeNodePredecessorsRecursive(oObject)),
                    });
                    return true;
                  }
                },
                function (oError) {
                  sap.m.MessageToast.show(oError.error);
                }
              )
              .finally(() => {
                nsLocal.ModelImpl.removeEntityAliasForContext(entitiesToReset);
              });
          }
        }
      },
    },
  });
  sap.galilei.ui.common.command.CommandRegistry.add(
    "sap.cdw.querybuilder.PreviewSQLCommand",
    new nsLocal.PreviewSQLCommand()
  );
});
