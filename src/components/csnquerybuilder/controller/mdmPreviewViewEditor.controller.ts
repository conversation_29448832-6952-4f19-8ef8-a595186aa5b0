/** @format */

// FILEOWNER: [data-viewer]

/** Copyright 2020 SAP SE or an SAP affiliate company. All rights reserved. */
import { IDetailsController, IWorkbenchController, IWorkbenchEditorComponent } from "../../abstractbuilder/api";
import { BottomDetailsManager } from "../../abstractbuilder/controller/BottomDetailsManager";
import {
  BaseController,
  BaseControllerClass,
  smartExtend,
} from "../../basecomponent/controller/BaseController.controller";
import { DataWarehouse } from "../../commonmodel/csn/csnAnnotations";
import {
  cloneJson,
  getDependentsFromCsn,
  getEntityAndCrossSpaceNameFromCSN,
  getEntityNameFromCSN,
  getNonPersistedRemoteTables,
  getNonPersistedTablesForAdvisorPreview,
} from "../../commonmodel/csn/csnUtils";
import {
  convertCDSToEDM,
  getAdhocInitResponse,
  getListOfConsumedInputParameters,
  onExportCsnBtn,
  updateAlreadyAvailableIPValues,
  updateIPQueryString,
} from "../../commonmodel/utility/CommonUtils";
import { createExportDebugCsnButton } from "../../commonui/utility/CommonUtils";
import { AdvisorUtil } from "../../reuse/utility/AdvisorUtil";
import { NamingHelper } from "../../reuse/utility/NamingHelper";
import { getBaseUrl } from "../../reuse/utility/ServiceCall";
import { ShellContainer } from "../../shell/utility/Container";
import { DWCFeature, EventType } from "../../shell/utility/ShellUsageCollectionService";
import { MdmDataPreviewActions } from "../../shell/utility/UsageActions";
import { CsnQueryBuilderClass } from "./CsnQueryBuilder.controller";

export class mdmPreviewViewEditorClass extends BaseControllerClass implements IDetailsController {
  public DataPreview: any;
  public oResourceModel: sap.ui.model.resource.ResourceModel;
  private isInitialized: boolean = false;
  private isPreviewDataActive: boolean = false;
  public messageManager: any;
  public dataEditorMsgPopover: any;
  previewSqlBtn: sap.m.Button;
  serviceName: string;
  useDragonetAdhocEndpoint: boolean;
  ignoreDirtyCheck: boolean;
  dataPreviewObjectNameFromTree: string;
  previewObjectName: string;
  public validateIconTabFilter: any;
  private oldTabSelection: any;
  private activeEditor: any;

  public onInit(): void {
    super.onInit();
    const bundleName = require("../../abstractbuilder/i18n/i18n.properties");
    this.oResourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: bundleName,
    });
    this.getView().setModel(this.oResourceModel, "i18n");
    this.getView().setModel(new sap.ui.model.json.JSONModel({ editMode: false }));
    this.isInitialized = true;
    this.dataPreviewObjectNameFromTree = undefined;
    this.oldTabSelection = undefined;
  }

  public getIsInitialized() {
    return this.isInitialized;
  }

  public setIsInitialized(isInitialized: boolean) {
    this.isInitialized = isInitialized;
  }

  public switchToDataValidationTab() {
    this.DataPreview.switchTab("validation");
    this.oldTabSelection = this.DataPreview.getIconTabBar().getSelectedKey();
  }

  private getHasPrivileges(): boolean {
    // Check the feature flag
    let hasPrivileges: boolean;
    const workbenchEnvModel = this.getView().getModel("workbenchEnv");
    if (workbenchEnvModel) {
      hasPrivileges = workbenchEnvModel.getProperty("/canCreateOrUpdateModel");
    } else {
      const privileges = (sap.ui.getCore().getModel("privilege") as sap.ui.model.json.JSONModel).getData()
        .DWC_DATABUILDER;
      hasPrivileges = privileges.create || privileges.update;
    }
    return hasPrivileges;
  }
  /**
   * {@inheritdoc}
   * {@implements}
   * @memberof DataMappingClass
   */
  public async setObject(object) {
    this.ignoreDirtyCheck = false;
    const workbench = this.getWorkbenchController();
    this.activeEditor = workbench.getActiveEditor(true);
    const pageContent = this.getView().byId("mdmPreviewPage") as sap.m.Page;
    this.dataPreviewObjectNameFromTree = object?.objFromRepoTree;
    if (this.DataPreview) {
      this.oldTabSelection =
        this.DataPreview.getIconTabBar &&
        this.DataPreview.getIconTabBar().getSelectedKey &&
        this.DataPreview.getIconTabBar().getSelectedKey();
      this.DataPreview.destroy();
      this.DataPreview = null;
    }
    sap.ui.require(["sap/mdm/tableEditor/Editor"], async (PreviewEditor: any) => {
      try {
        if (!this.DataPreview) {
          this.DataPreview = await this.createMDMEditor(workbench, pageContent, PreviewEditor, object);
        }
        pageContent.addContent(this.DataPreview);
      } finally {
        (workbench as any).previewDataPending = false;
      }
    });
  }

  public async getNonPersistedTables(
    entityName: string,
    spaceId: string,
    previewCsn: any,
    selectedObj: any,
    isDirty: boolean,
    isRepoObject: boolean
  ): Promise<any> {
    // to apply remote table restrictions
    if (
      isRepoObject ||
      (selectedObj &&
        (selectedObj.deploymentStatus === 1 || selectedObj.deploymentStatus === 2) &&
        (!isDirty || selectedObj?.classDefinition?.name === "Entity"))
    ) {
      const advisorResponse = await AdvisorUtil.getPersistencyAdvisorDetails(entityName, spaceId);
      if (advisorResponse && advisorResponse.entityStats && advisorResponse.entityStats.length > 0) {
        const nonPersistedEntities = AdvisorUtil.getNonPersistedEntities(advisorResponse, entityName);
        return nonPersistedEntities;
      }
    } else {
      let listofNonPersistedRemoteTables;
      const listOfDependentEntities = await getDependentsFromCsn(
        previewCsn,
        entityName,
        spaceId,
        undefined,
        selectedObj
      );
      if (listOfDependentEntities.length > 0) {
        listofNonPersistedRemoteTables = await getNonPersistedRemoteTables(
          listOfDependentEntities,
          spaceId,
          selectedObj
        );
        return listofNonPersistedRemoteTables;
      }
    }
  }

  updateCSNWithIPValues(csn: any, consumedParams: any[], serviceName: string, oModel, inputParamsInfo) {
    consumedParams.forEach((element) => {
      if (!element.defaultForDataPreview) {
        element.defaultForDataPreview = 0;
      }
    });
    const query = csn.definitions[serviceName].query;
    let queryString = query ? JSON.stringify(query) : "";
    let ribbonContent = "Input Parameters: ";
    const items = [];
    const paramValues = {};
    const inputParams = [];
    consumedParams.forEach((element) => {
      queryString = updateIPQueryString(element, queryString);
      inputParams.push(`${element.displayName}( ${element.defaultForDataPreview} )`);
      // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
      items.push(
        // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
        element.displayName + "( " + element.defaultForDataPreview + " )"
      );
      paramValues[element.name] = element.defaultForDataPreview;
    });
    if (inputParams.length > 0) {
      const params = inputParams
        .join(", ")
        .replace(new RegExp("<", "g"), "&#60;")
        .replace(new RegExp(">", "g"), "&#62;");
      inputParamsInfo.inputParams = `Input Parameters: ${params}`;
      inputParamsInfo.inputParamExist = true;
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    ribbonContent += items.join(", ");

    /* All the mapped parameters should have some value for CDS compiler to work fine (CDS compiler limitation),
    hence setting all other parameters to a dummy value "now" */
    if (oModel?.output?.parameters) {
      oModel?.output.parameters.forEach((element) => {
        // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
        queryString = queryString
          // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
          .split('{"ref":["' + element.name + '"],"param":true}')
          .join('{"val":"now"}');
      });
    }
    csn.definitions[serviceName].query = JSON.parse(queryString);
    return csn;
  }

  async getPreviewParameters(
    workbench: IWorkbenchController,
    selectedObj,
    oModel,
    featureFlags,
    object,
    previewErrors: any[]
  ) {
    let endpoint = "";
    let previewModelName =
      selectedObj?.technicalName || selectedObj?.name || oModel?.output?.name || workbench.modelName;
    const previewModelLabel = selectedObj?.businessName || selectedObj?.label || workbench.modelId;
    this.serviceName = previewModelName;
    let dragonetCsn = {};
    if (this.dataPreviewObjectNameFromTree) {
      ({ previewModelName, endpoint, previewErrors, dragonetCsn } = await this.getRepoTreeDetails(
        this.dataPreviewObjectNameFromTree,
        object,
        workbench,
        previewErrors
      ));
      return { endpoint, previewModelName, dragonetCsn };
    }
    previewErrors = this.mdmPreviewErrors(workbench);
    this.useDragonetAdhocEndpoint = false;
    const options: any = { dataPreview: true, isMdmPreview: true };
    const objType = selectedObj?.classDefinition?.name || "Output";
    const inputParamsInfo = { inputParamExist: false, inputParams: "" };
    if (objType === "Output" && oModel["#objectStatus"] !== "1") {
      this.useDragonetAdhocEndpoint = true;
      this.serviceName = NamingHelper.encodeDataPreviewName(previewModelName);
    } else if (objType === "Output" && (workbench.isDirty() || this.ignoreDirtyCheck)) {
      this.useDragonetAdhocEndpoint = true;
    } else if (objType !== "Output" && objType !== "Entity") {
      this.useDragonetAdhocEndpoint = true;
    }
    let inputParamSet = "";
    options.rootObject = selectedObj;
    const consumedParams = getListOfConsumedInputParameters(selectedObj, oModel?.output);
    updateAlreadyAvailableIPValues(consumedParams, this.getWorkbenchController().getWorkbenchEnvModel());
    // to-do - once we upgrade cds to cds 7 add the output case here
    // condition - (objType === "Entity" || (objType === "Output" && !workbench.isDirty()))
    if (objType === "Entity" && selectedObj?.dbViewType === "TABLE_FUNCTION") {
      this.useDragonetAdhocEndpoint = true;
    } else if (objType === "Entity" && selectedObj?.isCrossSpace) {
      this.useDragonetAdhocEndpoint = true;
    } else if (
      (objType === "Entity" || (objType === "Output" && !workbench.isDirty())) &&
      (selectedObj?.isView ||
        (selectedObj.isTable &&
          ((selectedObj.isRemote && !selectedObj?.isDeltaTable) || selectedObj.isLocalSchema) &&
          selectedObj.parameters.length > 0)) &&
      selectedObj?.deploymentStatus === 1
    ) {
      this.useDragonetAdhocEndpoint = false;
      const params = [];
      const inputParams = [];
      if (selectedObj.parameters.length > 0) {
        if (objType !== "Output") {
          selectedObj.parameters.forEach((parameter) => {
            if (consumedParams.findIndex((consumedParam) => consumedParam.mappedName === parameter.name) === -1) {
              params.push(
                `${parameter.name}=${convertCDSToEDM(parameter.value || parameter.defaultValue, parameter.dataType)}`
              );
            }
          });
        }
        consumedParams.forEach((element) => {
          inputParams.push(`${element.displayName}( ${element.defaultForDataPreview} )`);
          // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
          params.push(
            `${objType !== "Output" ? element.mappedName : element.name}=${convertCDSToEDM(
              element.defaultForDataPreview,
              element.primitiveDataType
            )}`
          );
        });
        inputParamSet = `(${params.join(",")})/Set`;

        if (inputParams.length > 0) {
          const paramString = inputParams
            .join(", ")
            .replace(new RegExp("<", "g"), "&#60;")
            .replace(new RegExp(">", "g"), "&#62;");
          inputParamsInfo.inputParams = `Input Parameters: ${paramString}`;
          inputParamsInfo.inputParamExist = true;
        }
      }
    } else if (objType === "Entity" && selectedObj?.isView && selectedObj?.deploymentStatus !== 1) {
      this.useDragonetAdhocEndpoint = true;
      this.serviceName = NamingHelper.encodeDataPreviewName(previewModelName);
    }

    if (this.useDragonetAdhocEndpoint || (consumedParams.length > 0 && !inputParamSet)) {
      let csn = await sap.cdw.querybuilder.ViewModelToCsn.getCSN(this.serviceName, oModel, options);

      if (objType === "Output") {
        if (oModel["#objectStatus"] === "1") {
          this.serviceName = NamingHelper.encodeDataPreviewName(previewModelName);
        }
      } else if (objType === "Entity" && selectedObj?.dbViewType === "TABLE_FUNCTION") {
        this.serviceName = NamingHelper.encodeDataPreviewName(previewModelName);
      } else if (objType === "Entity" && selectedObj?.isCrossSpace) {
        this.serviceName = getEntityNameFromCSN(csn.definitions);
      }

      if (!csn.definitions.hasOwnProperty(this.serviceName)) {
        this.serviceName = getEntityNameFromCSN(csn.definitions);
      }

      delete csn?.definitions[this.serviceName]?.params;
      //DW101-75860 Data preview of a SQL view in secondary editor gives error due to script property in csn
      if (
        workbench?.isSecondaryEditorActive &&
        objType === "Entity" &&
        selectedObj?.dbViewType === "TABLE_FUNCTION" &&
        csn.definitions &&
        csn.definitions[this.serviceName]
      ) {
        delete csn.definitions[this.serviceName]["@DataWarehouse.tableFunction.script"];
      }

      if (consumedParams.length > 0) {
        csn = this.updateCSNWithIPValues(csn, consumedParams, this.serviceName, oModel, inputParamsInfo);
      }

      // find non persisted dependent entities to add remote table restrictions
      let nonPersistedEntities = [];
      const oViewModelToCsn = sap.cdw.querybuilder.ViewModelToCsn.getInstance();
      oViewModelToCsn.adjustCastFunctionDataType(csn, this.serviceName);

      let repoDBState = sap.ui.getCore().getModel("circuitbreaker").getProperty("/Deepsea");
      const advisorResponse =
        (repoDBState === undefined || repoDBState === "Green") &&
        (await getNonPersistedTablesForAdvisorPreview(
          selectedObj,
          previewModelName,
          workbench.spaceId,
          workbench.isDirty(),
          csn
        ));
      repoDBState =
        (repoDBState === undefined || repoDBState === "Green") &&
        sap.ui.getCore().getModel("circuitbreaker").getProperty("/Deepsea");
      if (repoDBState !== "Green" || (advisorResponse && advisorResponse.errors && advisorResponse.errors.length > 0)) {
        if (repoDBState !== "Green") {
          previewErrors = [
            {
              type: "Error",
              message: this.oResourceModel.getResourceBundle().getText("mdm-try-again-later"),
            },
          ];
          endpoint = "";
          dragonetCsn = previewErrors[0];
          return {
            endpoint,
            previewModelName,
            previewModelLabel,
            inputParamSet,
            inputParamsInfo,
            previewErrors,
            dragonetCsn,
          };
        }
      }
      nonPersistedEntities = advisorResponse.nonReplicatedEntities;

      try {
        if (csn && csn.definitions) {
          for (const key in csn.definitions) {
            if (key.includes("%%END%%")) {
              const definition = csn.definitions[key];
              if (
                definition[DataWarehouse.persistence_hdlf_tableFormat] &&
                (selectedObj?.isCrossSpace ||
                  (definition[DataWarehouse.delta] &&
                    definition[DataWarehouse.delta]["type"] &&
                    definition[DataWarehouse.delta]["type"]["#"] === "ACTIVE"))
              ) {
                delete definition[DataWarehouse.persistence_hdlf_tableFormat];
                delete definition[DataWarehouse.delta];
              }
            }
          }
        }
        const response = await getAdhocInitResponse(workbench.spaceId, this.serviceName, csn, nonPersistedEntities);
        endpoint = `${getBaseUrl()}data-access/adhoc/${response.serviceId}/`;
        dragonetCsn = response?.csn || {};
      } catch (error) {
        previewErrors = [
          {
            type: "Error",
            message:
              error.responseText === "FORBIDDEN_NO_PRIVILEGE" ||
              error.responseText === ("DRAGONET_DOWN" || "CUSTOMER_HANA_DOWN" || "REPO_DB_DOWN")
                ? error.responseText === "FORBIDDEN_NO_PRIVILEGE"
                  ? this.oResourceModel.getResourceBundle().getText("txtNoPrivilegesViewData")
                  : error.responseText
                : error.responseText === ("DRAGONET_DOWN" || "CUSTOMER_HANA_DOWN" || "REPO_DB_DOWN")
                ? this.oResourceModel.getResourceBundle().getText("mdm-try-again-later")
                : error.responseText,
          },
        ];
        endpoint = "";
        dragonetCsn = previewErrors[0];
      }
    } else {
      endpoint = `${getBaseUrl()}data-access/instant/${workbench.spaceId}/${encodeURI(previewModelName)}/`;
    }

    return {
      endpoint,
      previewModelName,
      previewModelLabel,
      inputParamSet,
      inputParamsInfo,
      previewErrors,
      dragonetCsn,
    };
  }

  /**
   * check is current editor is Graphical View
   * @param workbench - global object
   * @returns
   */
  isGraphicalView(editor: IWorkbenchEditorComponent): boolean {
    return editor?.getEditorControl()?.getId() === "csnQueryBuilderEditor";
  }

  /**
   * check is current editor is TransformationFlow
   * @param workbench - global object
   * @returns
   */
  isTransformationFlow(editor: IWorkbenchEditorComponent): boolean {
    return editor?.getEditorControl()?.getId() === "transformationFlowEditor";
  }

  async createMDMEditor(
    workbench: IWorkbenchController,
    pageContent: sap.m.Page,
    PreviewEditor: any,
    object: any
  ): Promise<any> {
    const MDM_FEATURE_LIST = [
      "DWCO_MODELING_DM_DATATYPE_RESTRICTIONS",
      "DWCO_MODEL_VALIDATION",
      "DWCO_ETV_LTF_PERF_RES",
    ];

    let previewErrors = [];
    const featureFlags = (sap.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel).getData();

    const isTechnicalName =
      this.getWorkbenchController().getWorkbenchEnvModel().getData().objectNameDisplay === "technicalName";
    previewErrors = this.mdmPreviewErrors(workbench);
    const currentEditor = workbench["getVisibleEditor"]();
    const oModel = await currentEditor.getGalileiModel();

    let consumer = "data-access-view";
    if (this.isGraphicalView(currentEditor)) {
      consumer = "graphical-view";
    } else if (this.isTransformationFlow(currentEditor)) {
      consumer = "transformation-flow";
    }

    let selectedObj = (currentEditor?.controller() as CsnQueryBuilderClass)
      ?.getDiagramEditor()
      ?.selectedSymbols?.get(0)?.object;
    if (object?.objFromRepoTree) {
      this.getWorkbenchController()?.getWorkbenchEnvModel()?.setProperty("/isSilentPreview", false);
    }
    const isSilentPreview = this.getWorkbenchController()?.getWorkbenchEnvModel()?.getProperty("/isSilentPreview");
    let previewModelName = "";
    let previewModelLabel = "";
    let endpoint = "";
    let inputParamSet = "";
    let inputParamsInfo = {};
    let mdmTableSetting = { columns: [], filters: [], sorters: [], csn: {} };
    let entityType = "";
    try {
      entityType = this.getEntityType(object, selectedObj);
    } catch (error) {
      // It will fail when this.dataPreviewObjectNameFromTree && previewErrors.length > 0
    }
    if (this.dataPreviewObjectNameFromTree && previewErrors.length === 0) {
      let dragonetCsn = {};
      ({ previewModelName, endpoint, previewErrors, dragonetCsn } = await this.getRepoTreeDetails(
        this.dataPreviewObjectNameFromTree,
        object,
        workbench,
        previewErrors
      ));
      const encodedName = NamingHelper.encodeDataPreviewName(this.dataPreviewObjectNameFromTree);
      this.previewObjectName = encodedName;
      if (!(workbench as any).oDataPreviewColumns[this.previewObjectName]) {
        (workbench as any).oDataPreviewColumns[this.previewObjectName] = {};
      }
      mdmTableSetting = (workbench as any).oDataPreviewColumns[this.previewObjectName].mdmSettings || {
        columns: [],
        filters: [],
        sorters: [],
        csn: {},
      };
      mdmTableSetting.csn = dragonetCsn;
    } else if (this.dataPreviewObjectNameFromTree && previewErrors.length > 0) {
      previewModelName = this.dataPreviewObjectNameFromTree;
      workbench.getWorkbenchEnvModel().setProperty("/detailsPage/problems", []);
    } else if (selectedObj && previewErrors.length === 0) {
      const previewParameters = await this.getPreviewParameters(
        workbench,
        selectedObj,
        oModel,
        featureFlags,
        object,
        previewErrors
      );
      previewErrors = previewParameters.previewErrors;
      endpoint = previewParameters.endpoint;
      previewModelName = previewParameters.previewModelName;
      previewModelLabel = previewParameters.previewModelLabel;
      inputParamSet = previewParameters.inputParamSet;
      inputParamsInfo = previewParameters.inputParamsInfo;
      this.previewObjectName = (selectedObj.objectId as string) + "_" + (selectedObj.name as string);
      if (!(workbench as any).oDataPreviewColumns[this.previewObjectName]) {
        (workbench as any).oDataPreviewColumns[this.previewObjectName] = {};
      }
      mdmTableSetting = (workbench as any).oDataPreviewColumns[this.previewObjectName].mdmSettings || {
        columns: [],
        filters: [],
        sorters: [],
        csn: {},
      };
      mdmTableSetting.csn = previewParameters.dragonetCsn;
    } else if (previewErrors.length > 0) {
      previewModelName = selectedObj?.technicalName || selectedObj?.name || oModel?.output?.name || workbench.modelId;
      workbench.getWorkbenchEnvModel().setProperty("/detailsPage/problems", []);
      mdmTableSetting.csn = previewErrors[0];
    }
    const MDM_FEATURE_FLAGS = {};
    MDM_FEATURE_LIST.forEach((el) => {
      MDM_FEATURE_FLAGS[el] = ShellContainer.get().getFeatureFlagService().getFeatureValue(el);
    });
    let isDataValidationAllowed = !selectedObj?.classDefinition?.name;
    if (!isDataValidationAllowed) {
      isDataValidationAllowed = selectedObj?.classDefinition?.name === "Output";
    }
    if (!isDataValidationAllowed) {
      this.getWorkbenchController()?.getWorkbenchEnvModel()?.setProperty("/isSilentPreview", false);
    }
    const isFFModelValidationOn = featureFlags.DWCO_MODEL_VALIDATION ? true : false;

    const MDMEditor = new PreviewEditor({
      i18nModel: this.oResourceModel,
      isSilentPreview: isSilentPreview && isFFModelValidationOn && isDataValidationAllowed,
      consumer: consumer,
      entityType: entityType,
      mdmFeatureFlags: MDM_FEATURE_FLAGS,
      inputParamSet: inputParamSet,
      previewErrors: previewErrors,
      serviceUrl: `${endpoint}`,
      previewModelName: previewModelName,
      previewModelLabel: previewModelLabel,
      isTechnicalName: isTechnicalName,
      maxColumns: 20,
      inputParamsInfo: inputParamsInfo,
      mdmTableSetting: mdmTableSetting,
      isMdmRefreshNeeded: false,
      editable: { path: "/editMode" },
      // entityType,
      // replicated: replicationStatus === "V",
      columnSettingPress: () => {
        // Record Usage for opening column settings
        ShellContainer.get().getUsageCollectionService().recordAction({
          action: MdmDataPreviewActions.OpenColumnSetting,
          feature: DWCFeature.MDMDATAPREVIEW,
          eventtype: EventType.CLICK,
        });
      },
      onTabChange: (event) => {
        if (
          featureFlags.DWCO_MODEL_VALIDATION &&
          (MDMEditor.getIconTabBar().getSelectedKey() === "validation" || this.oldTabSelection === "validation") &&
          MDMEditor.getIconTabBar().getSelectedKey() !== "problems"
        ) {
          if (MDMEditor.getIconTabBar().getSelectedKey() === "validation") {
            this.getWorkbenchController()?.getWorkbenchEnvModel()?.setProperty("/isSilentPreview", true);
            MDMEditor.switchTab("validation");
            this.oldTabSelection = MDMEditor.getIconTabBar().getSelectedKey();
            return;
          } else {
            this.getWorkbenchController()?.getWorkbenchEnvModel()?.setProperty("/isSilentPreview", false);
          }

          MDMEditor.isRefreshInProgress = true;
          const consumedParams = getListOfConsumedInputParameters(selectedObj, oModel.output);
          updateAlreadyAvailableIPValues(consumedParams, this.getWorkbenchController().getWorkbenchEnvModel());
          if (workbench.isPreviewdataActive() && consumedParams.length > 0) {
            (workbench as any).showIPPopUPForDataPreview(
              oModel,
              consumedParams,
              undefined,
              undefined,
              (result) => {
                this.ignoreDirtyCheck = true;
                this.getWorkbenchController()
                  .getWorkbenchEnvModel()
                  .setProperty("/paramDefaults", result.viewData.getData().parameters);
                this.refreshMetadata(workbench, selectedObj, oModel, featureFlags, MDMEditor, object, previewErrors);
              },
              (_) => {
                // if user cancels the preview, do nothing
                MDMEditor.isRefreshInProgress = false;
                const editMode = (this.getView().getModel() as sap.ui.model.json.JSONModel).getProperty("/editMode");
                MDMEditor.busyStateControl(editMode, false);
              }
            );
          }
        }
      },

      refreshPress: async () => {
        // Record Usage for refreshing data preview
        ShellContainer.get().getUsageCollectionService().recordAction({
          action: MdmDataPreviewActions.RefreshDataPreview,
          feature: DWCFeature.MDMDATAPREVIEW,
          eventtype: EventType.CLICK,
        });
        const oPreview = workbench["activeEditor"].canPreviewData(selectedObj);
        if (!oPreview.canPreviewData) {
          previewErrors = [
            {
              type: "Error",
              message: oPreview.message,
            },
          ];
          this.handleMDMErrorCase(MDMEditor, previewErrors);
          return;
        }
        const previewErrorForDac = await this.getWorkbenchController()["getPreviewErrorForDac"]();
        if (previewErrorForDac) {
          this.handleMDMErrorCase(MDMEditor, [{ type: "Error", message: previewErrorForDac }]);
          return;
        }
        MDMEditor.isRefreshInProgress = true;
        const consumedParams = getListOfConsumedInputParameters(selectedObj, oModel.output);
        updateAlreadyAvailableIPValues(consumedParams, this.getWorkbenchController().getWorkbenchEnvModel());
        if (workbench.isPreviewdataActive() && consumedParams.length > 0) {
          (workbench as any).showIPPopUPForDataPreview(
            oModel,
            consumedParams,
            undefined,
            undefined,
            (result) => {
              this.ignoreDirtyCheck = true;
              this.getWorkbenchController()
                .getWorkbenchEnvModel()
                .setProperty("/paramDefaults", result.viewData.getData().parameters);
              this.refreshMetadata(workbench, selectedObj, oModel, featureFlags, MDMEditor, object, previewErrors);
            },
            (_) => {
              // if user cancels the preview, do nothing
              MDMEditor.isRefreshInProgress = false;
              const editMode = (this.getView().getModel() as sap.ui.model.json.JSONModel).getProperty("/editMode");
              MDMEditor.busyStateControl(editMode, false);
            }
          );
        } else {
          this.refreshMetadata(workbench, selectedObj, oModel, featureFlags, MDMEditor, object, previewErrors);
        }
      },
      closePreviewPress: () => {
        // Record Usage for closing data preview
        ShellContainer.get().getUsageCollectionService().recordAction({
          action: MdmDataPreviewActions.CloseDataPreview,
          feature: DWCFeature.MDMDATAPREVIEW,
          eventtype: EventType.CLICK,
        });
        const currentContent = pageContent.getContent().length > 0 && pageContent.getContent()[0];
        if (currentContent) {
          pageContent.removeContent(currentContent);
          currentContent.destroy();
        }
        workbench.onToggleDetails();
      },
      inputParameterConfig: () => {
        const consumedParams = getListOfConsumedInputParameters(selectedObj, oModel.output);
        updateAlreadyAvailableIPValues(consumedParams, this.getWorkbenchController().getWorkbenchEnvModel());
        if (workbench.isPreviewdataActive() && consumedParams.length > 0) {
          (workbench as any).showIPPopUPForDataPreview(oModel, consumedParams, undefined, undefined, (result) => {
            this.ignoreDirtyCheck = true;
            this.getWorkbenchController()
              .getWorkbenchEnvModel()
              .setProperty("/paramDefaults", result.viewData.getData().parameters);
            this.refreshMetadata(workbench, selectedObj, oModel, featureFlags, MDMEditor, object, previewErrors);
          });
        }
      },
      computeLocalErrors: (event) => {
        const updatedModelName = this.getUpdatedModelName(workbench);
        const sError = this.mdmPreviewErrors(workbench);
        event?.getSource()?.setProperty("previewErrors", sError);
        if (updatedModelName) {
          event?.getSource()?.setProperty("previewModelName", updatedModelName);
        }
      },
      beforeRefreshPress: (_event) => {
        if (!selectedObj) {
          return;
        }
        let sError = (workbench as any).getModelErrors(
          [selectedObj].concat(sap.cdw.querybuilder.NodeImpl.computeNodePredecessorsRecursive(selectedObj)),
          []
        );
        if (sError.length > 0) {
          sError = sError.replace(/\r\n/, "");
          sError = workbench.getText("cannotViewInvalidDependencies", sError);
          previewErrors = [
            {
              type: "Error",
              message: sError,
            },
          ];
          MDMEditor.setPreviewErrors(previewErrors);
          workbench.getWorkbenchEnvModel().setProperty("/detailsPage/problems", previewErrors);
          MDMEditor.editorModel.setProperty("/columns", []);
          MDMEditor.editorModel.setProperty("/count", 0);
        } else if (
          this.getWorkbenchController().isSecondaryEditorActive &&
          selectedObj?.classDefinition?.name !== "Entity" &&
          sap.cdw.querybuilder.Extensions?.supportDatapreviewOnIntermediateNode === false
        ) {
          previewErrors = [
            {
              type: "Error",
              message: (currentEditor?.controller() as CsnQueryBuilderClass)?.getText("dataPreviewNotSupp"),
            },
          ];
          MDMEditor.setPreviewErrors(previewErrors);
          workbench.getWorkbenchEnvModel().setProperty("/detailsPage/problems", previewErrors);
          MDMEditor.editorModel.setProperty("/columns", []);
          MDMEditor.editorModel.setProperty("/count", 0);
        } else {
          MDMEditor.setPreviewErrors([]);
          workbench.getWorkbenchEnvModel().setProperty("/detailsPage/problems", []);
        }
      },
      mdmTableSettingChange: (event) => {
        const currentData = (workbench as any).oDataPreviewColumns[this.previewObjectName] || {};
        // eslint-disable-next-line dot-notation
        currentData["mdmSettings"] = event.getParameter("setting");
      },
    });

    const previewSqlBtn = new sap.m.Button({
      icon: "sap-icon://inspection",
      tooltip: "{i18n>txtPreviewSQL}",
      type: sap.m.ButtonType.Transparent,
      visible: true,
    }).addStyleClass("mdmPreviewSqlBtn");
    previewSqlBtn.attachPress(this.onPreviewIntermediateSQL.bind(this));
    if (this.dataPreviewObjectNameFromTree) {
      previewSqlBtn.setEnabled(false);
    }

    // extendPreviewToolbar method will add the controls in preview toolbar at given index (second parameter). By default the value of index is taken as 0.
    // extendPreviewToolbar can also take array of controls as first parameter, if more than one custom control needs to be added.
    MDMEditor.extendPreviewToolbar(previewSqlBtn, 1);

    const exportCsnBtn = createExportDebugCsnButton();
    exportCsnBtn.attachPress(
      onExportCsnBtn.bind(this, [endpoint, mdmTableSetting.csn, workbench.spaceId, previewModelName])
    );
    MDMEditor.extendPreviewToolbar(exportCsnBtn, 2);

    if (selectedObj?.rootContainer?.spaceId && !selectedObj?.isCrossSpace) {
      this.getWorkbenchController().extendImpersonationButton(
        MDMEditor,
        selectedObj?.rootContainer?.spaceId,
        selectedObj?.documentId ?? selectedObj?.rootContainer?.documentId,
        3
      );
    }
    const graphicalEditorController = this.activeEditor?.controller && this.activeEditor?.controller();
    const editorConfiguration = graphicalEditorController && graphicalEditorController.getEditorConfiguration();
    if (featureFlags.DWCO_MODEL_VALIDATION && !this.dataPreviewObjectNameFromTree && !editorConfiguration) {
      if (isDataValidationAllowed) {
        const viewName = require("../../databuilder/view/DataValidation.view.xml");
        sap.ui.getCore().byId("graphicalDataValidationId")?.destroy();

        const dataValidationView = sap.ui.view({
          id: "graphicalDataValidationId",
          type: sap.ui.core.mvc.ViewType.XML,
          viewName: viewName,
          width: "100%",
          height: "100%",
        });
        const isDeployed = selectedObj?.deploymentStatus === 1 || oModel?.deploymentStatus === 1 ? true : false;
        const validationViewModel = new sap.ui.model.json.JSONModel({
          viewName: selectedObj?.technicalName || selectedObj?.name || workbench?.modelName || oModel?.name,
          spaceName: workbench.spaceId,
          isDeployed: isDeployed,
        });
        dataValidationView.setModel(validationViewModel, "validationViewData");

        sap.ui.getCore().byId("graphicalDataValidationTab")?.destroy();
        const iconTabFilter = new sap.m.IconTabFilter({
          id: "graphicalDataValidationTab",
          text: (currentEditor?.controller() as CsnQueryBuilderClass)?.getText("DATAVALIDATION"),
          content: dataValidationView,
          key: "validation",
        });

        this.validateIconTabFilter = iconTabFilter;
        sap.ui.getCore().byId("graphicalDataValidationBtn")?.destroy();
        const isValidateBtnEnabled = isDeployed && this.getHasPrivileges();
        const buttons = [
          new sap.m.Button({
            id: "graphicalDataValidationBtn",
            text: (currentEditor?.controller() as CsnQueryBuilderClass)?.getText("VALIDATEFLD"),
            type: sap.m.ButtonType.Transparent,
            visible: true,
            enabled: isValidateBtnEnabled,
            press: async () => {
              await dataValidationView.getController()["validateView"]();
            },
          }).addStyleClass("mdmPreviewSqlBtn"),
        ];
        if (featureFlags.DWCO_MODELING_VALIDATION_ENABLE_REMOTE_TABLES) {
          sap.ui.getCore().byId("graphicalDataCancelValidationBtn")?.destroy();
          buttons.push(
            new sap.m.Button({
              id: "graphicalDataCancelValidationBtn",
              text: (currentEditor?.controller() as CsnQueryBuilderClass)?.getText("CANCELVALIDATEFLD"),
              type: sap.m.ButtonType.Transparent,
              visible: featureFlags.DWCO_MODELING_VALIDATION_ENABLE_REMOTE_TABLES,
              enabled: false,
              press: async () => {
                await dataValidationView.getController()["cancelValidate"]();
              },
            }).addStyleClass("mdmPreviewSqlBtn")
          );
        }

        MDMEditor.extendPreviewTabs({
          iconTabFilter,
          toolbarButtons: buttons,
        });
        const isPreviewSelected = this.getWorkbenchController()
          ?.getWorkbenchEnvModel()
          ?.getProperty("/isPreviewSelected");
        if (isSilentPreview && isPreviewSelected) {
          iconTabFilter.setVisible(true);
          MDMEditor.switchTab("validation");
          this.oldTabSelection = MDMEditor.getIconTabBar().getSelectedKey();
        } else {
          MDMEditor.switchTabToolbar(MDMEditor.getIconTabBar().getSelectedKey());
        }
        this.getWorkbenchController()?.getWorkbenchEnvModel()?.setProperty("/isPreviewSelected", false);
      }
    }

    return MDMEditor;
  }

  getEntityType(object, selectedObj) {
    if (this.dataPreviewObjectNameFromTree) {
      if (
        object?.definitions[this.dataPreviewObjectNameFromTree]?.hasOwnProperty("@DataWarehouse.sqlEditor.query") ||
        object?.definitions[this.dataPreviewObjectNameFromTree]?.hasOwnProperty("@DataWarehouse.querybuilder.model")
      ) {
        return "view";
      } else {
        if (
          object?.definitions[this.dataPreviewObjectNameFromTree]?.hasOwnProperty("@DataWarehouse.remote.connection")
        ) {
          return "remote";
        } else {
          return "local";
        }
      }
    } else {
      const objType = selectedObj?.classDefinition?.name;
      if (objType === "Entity") {
        if (selectedObj.isView) {
          return "view";
        } else if (selectedObj.isRemote) {
          return "remote";
        } else {
          return "local";
        }
      } else {
        return "view";
      }
    }
  }

  protected async getRepoTreeDetails(
    previewModelName: string,
    object: any,
    workbench: IWorkbenchController,
    previewErrors: any[]
  ) {
    let dragonetCsn = {};
    let endpoint = "";
    const tempEntityName = NamingHelper.encodeDataPreviewName(previewModelName);
    const tempCsn = cloneJson(object);
    delete tempCsn.definitions[tempEntityName];
    const { entityName, spaceName } = getEntityAndCrossSpaceNameFromCSN(tempCsn && tempCsn.definitions);
    // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
    const isCrossEntity = !!object.definitions[spaceName + "." + entityName];
    if (isCrossEntity) {
      this.useDragonetAdhocEndpoint = true;
      const previewEntityName = NamingHelper.encodeDataPreviewName(previewModelName);
      let nonPersistedEntities = [];
      const featureFlags = (sap.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel).getData();

      // find non persisted dependent entities to add remote table restrictions
      /* TO-DO: Add me when advisor supports cross space entities
        nonPersistedEntities = await getNonPersistedTablesForAdvisorPreview(
          undefined,
          entityName,
          spaceName,
          workbench.isDirty(),
          undefined,
          true
        );
      */

      try {
        const response = await getAdhocInitResponse(workbench.spaceId, previewEntityName, object, nonPersistedEntities);
        endpoint = `${getBaseUrl()}data-access/adhoc/${response.serviceId}/`;
        dragonetCsn = response?.csn || {};
      } catch (error) {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        previewErrors = [
          {
            type: "Error",
            message:
              error.responseText === "FORBIDDEN_NO_PRIVILEGE" ||
              error.responseText === ("DRAGONET_DOWN" || "CUSTOMER_HANA_DOWN" || "REPO_DB_DOWN")
                ? error.responseText === "FORBIDDEN_NO_PRIVILEGE"
                  ? this.oResourceModel.getResourceBundle().getText("txtNoPrivilegesViewData")
                  : error.responseText
                : error.responseText === ("DRAGONET_DOWN" || "CUSTOMER_HANA_DOWN" || "REPO_DB_DOWN")
                ? this.oResourceModel.getResourceBundle().getText("mdm-try-again-later")
                : error.responseText,
          },
        ];
        endpoint = "";
        dragonetCsn = previewErrors[0];
      }
    } else {
      this.useDragonetAdhocEndpoint = false;
      endpoint = `${getBaseUrl()}data-access/instant/${workbench.spaceId}/${encodeURI(
        this.dataPreviewObjectNameFromTree
      )}/`;
    }

    return { previewModelName, endpoint, previewErrors, dragonetCsn };
  }

  async refreshMetadata(
    workbench: IWorkbenchController,
    selectedObj,
    oModel,
    featureFlags,
    MDMEditor,
    object,
    previewErrors
  ) {
    const previewParameters = await this.getPreviewParameters(
      workbench,
      selectedObj,
      oModel,
      featureFlags,
      object,
      previewErrors
    );
    previewErrors = previewParameters.previewErrors;
    const endpoint = previewParameters.endpoint;
    const inputParamSet = previewParameters.inputParamSet;
    const inputParamsInfo = previewParameters.inputParamsInfo;
    const mdmTableSetting = (workbench as any).oDataPreviewColumns[this.previewObjectName]?.mdmSettings || {
      columns: [],
      filters: [],
      sorters: [],
      csn: {},
    };
    mdmTableSetting.csn = previewParameters.dragonetCsn;
    if (previewErrors.length > 0) {
      this.handleMDMErrorCase(MDMEditor, previewErrors);
      return;
    }
    MDMEditor.setInputParamsInfo(inputParamsInfo);
    if (this.useDragonetAdhocEndpoint) {
      MDMEditor.setServiceUrl(`${endpoint}`);
    } else {
      const isDeployed = selectedObj?.deploymentStatus === 1 || oModel?.deploymentStatus === 1 ? true : false;
      if (MDMEditor.getServiceUrl() === undefined && endpoint !== "" && isDeployed) {
        MDMEditor.setServiceUrl(`${endpoint}`);
      }
      MDMEditor.setInputParamSet(inputParamSet);
      this.reloadPreviewMetadata();
    }
  }

  onPreviewIntermediateSQL() {
    const activeComponent = this.getWorkbenchController()["getVisibleEditor"]();
    if (activeComponent && activeComponent.isPreviewSQLVisible()) {
      activeComponent.getPreviewSQL({ isIntermediate: true });
    }
  }

  closePreview() {
    const workbench = this.getWorkbenchController();
    const pageContent = this.getView().byId("mdmPreviewPage") as sap.m.Page;
    const currentContent = pageContent.getContent().length > 0 && pageContent.getContent()[0];
    if (currentContent) {
      pageContent.removeContent(currentContent);
      currentContent.destroy();
    }
    workbench.onToggleDetails();
  }

  getPreviewDataActive(): boolean {
    return this.isPreviewDataActive;
  }

  setPreviewDataActive(isPreviewDataActive: boolean): void {
    this.isPreviewDataActive = isPreviewDataActive;
  }

  reloadPreviewMetadata(): void {
    const isTechnicalName =
      this.getWorkbenchController().getWorkbenchEnvModel().getData().objectNameDisplay === "technicalName";
    this.DataPreview.setProperty("isTechnicalName", isTechnicalName);
    setTimeout(() => {
      this.DataPreview.setupODataModel && this.DataPreview.setupODataModel();
    }, 20);
    this.DataPreview.getTable()?.getExtension()[0]?.getModel("filterEditor")?.setProperty("/filterSummary", "");
  }

  mdmPreviewErrors(workbench: IWorkbenchController): any {
    return workbench.getWorkbenchEnvModel().getProperty("/detailsPage/problems") || [];
  }

  getUpdatedModelName(workbench: IWorkbenchController): string {
    if (workbench.getActiveEditor().getEditorControl().getId() === "tableEditor") {
      const updatedModelName =
        workbench.getActiveEditor()?.getEditorControl()?.getModel("galileiModel")?.getData().name || workbench.modelId;
      return updatedModelName;
    }
  }

  handleMDMErrorCase(MDMEditor: any, previewErrors: any[]): void {
    MDMEditor.setPreviewErrors(previewErrors);
    MDMEditor.editorModel.setProperty("/columns", []);
    MDMEditor.editorModel.setProperty("/count", 0);
    MDMEditor.busyStateControl(false, false);
  }
  public getDataPreviewObjectNameFromTree() {
    return this.dataPreviewObjectNameFromTree;
  }

  protected getWorkbenchController(): IWorkbenchController {
    const featureFlags = (sap.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel).getData();
    if (featureFlags.DWCO_MODEL_VALIDATION) {
      return BottomDetailsManager.instance().getWorkbench();
    } else {
      const workbenchView = this.getView().getParent().getParent().getParent().getParent() as sap.ui.core.mvc.View;
      return workbenchView.getController() as IWorkbenchController;
    }
  }
}
export const mdmPreviewViewEditor = smartExtend(
  BaseController,
  "sap.cdw.components.csnquerybuilder.controller.mdmPreviewViewEditor",
  mdmPreviewViewEditorClass
);

sap.ui.define("sap/cdw/components/csnquerybuilder/controller/mdmPreviewViewEditor.controller", [], function () {
  return mdmPreviewViewEditor;
});
