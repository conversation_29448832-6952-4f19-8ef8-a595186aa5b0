/**
 * Copyright 2022 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { DataCategoryAnnotation } from "../../../commonmodel/model/annotations/dataCategoryAnnotation";
import { DataCategory } from "../../../commonmodel/model/types/cds.types";
import {
  IColumn,
  ICsnColumn,
  IDataset,
  IFilter,
  IFilterElement,
  IFlowSetting,
  IProperties,
  IReplicationFlow,
  IReplicationTask,
  ISystem,
  ITaskSetting,
  ITransform,
  IvType,
} from "../../replicationApi";
import Constants from "../utility/constants";
import Utils from "../utils";
const DYNAMIC_VTYPE_PREFIX = "$DYNAMIC.";

export class ModelToJSON {
  static dynamicvTypes: Record<string, IvType>;
  static featureflags;
  static replicationTaskSetting;
  static replicationFlowSetting;

  /**
   *
   * @static
   * @param {sap.cdw.replicationflow.Model} oModel
   * @return {*}  {IReplicationFlow}
   * @memberof ModelToJSON
   */
  static serializeModel(oModel: sap.cdw.replicationflow.Model): IReplicationFlow {
    const replicationFlow: IReplicationFlow = {
      description: oModel.displayName,
      sourceSystem: [],
      targetSystem: [],
      vTypes: {
        scalar: {},
      },
      replicationTasks: [],
      replicationTaskSetting: {},
      replicationFlowSetting: {},
    };
    ModelToJSON.dynamicvTypes = {};
    ModelToJSON.replicationTaskSetting = {};
    oModel.sourceSystems.forEach((oSystem: sap.cdw.replicationflow.System) => {
      ModelToJSON.addSystem(oSystem, replicationFlow.sourceSystem);
    });
    oModel.targetSystems.forEach((oSystem: sap.cdw.replicationflow.System) => {
      ModelToJSON.addSystem(oSystem, replicationFlow.targetSystem);
    });
    oModel.replicationTasks.forEach((oTask: sap.cdw.replicationflow.Task) => {
      ModelToJSON.addReplicationTask(oTask, replicationFlow.replicationTasks);
    });
    replicationFlow.replicationTasks.forEach((task: IReplicationTask) => {
      ModelToJSON.updateDeltaCheckInterval(task, oModel.deltaCheckIntervalMinute, oModel.deltaCheckIntervalHour);
    });
    ModelToJSON.addTaskSettings(oModel.replicationTaskSetting, replicationFlow.replicationTaskSetting, oModel);
    ModelToJSON.addFlowSettings(oModel.replicationFlowSetting, replicationFlow.replicationFlowSetting, oModel);

    const targetSystem = oModel.targetSystems.get(0);
    if (targetSystem) {
      if (
        Utils.isConnTypeObjectStore(targetSystem.connectionType) &&
        !Utils.isConnectionTypeDwcLTF(targetSystem.connectionId, targetSystem.connectionType)
      ) {
        // scenario - for object store duplicate supression is not present at target object properties and
        // its configurable at system properties only
        // so here iterating and updating it to system properties for all target objects
        replicationFlow.replicationTasks.forEach((task: IReplicationTask) => {
          ModelToJSON.updateObjectStoreProperties(task, targetSystem);
        });
      }
    }
    replicationFlow.vTypes.scalar = ModelToJSON.dynamicvTypes;
    return replicationFlow;
  }

  /**
   *
   * @static
   * @param {sap.cdw.replicationflow.System} oSystem
   * @param {ISystem[]} containerArr
   * @memberof ModelToJSON
   */
  static addSystem(oSystem: sap.cdw.replicationflow.System, containerArr: ISystem[]): void {
    const system: ISystem = {
      connectionId: oSystem.connectionId,
      connectionType: oSystem.connectionType,
      container: oSystem.systemContainer,
      maxConnections: oSystem.maxConnections,
      metadata: oSystem.metadata,
    };
    if (oSystem.systemType === "source" && oSystem.systemProperties) {
      ModelToJSON.addSourceProperties(oSystem.systemProperties, system);
    }
    // if sourceSystemProperties exist add sourceProperties
    if (oSystem.systemType === "target" && oSystem.systemProperties) {
      // Updating the system property for Suppress Duplicates for Objectstores to false when undefined
      ModelToJSON.updateSuppressDuplicateOfExistingRFToFalse(oSystem);
      ModelToJSON.addProperties(oSystem.systemProperties, system);
    }
    containerArr.push(system);
  }

  /**
   *
   * @static
   * @param {sap.cdw.replicationflow.FlowSetting} oReplicationFlowSetting
   * @param {IFlowSetting} flowSetting
   * @param {sap.cdw.replicationflow.Model} oModel
   * @memberof ModelToJSON
   */
  static addFlowSettings(
    oReplicationFlowSetting: sap.cdw.replicationflow.FlowSetting,
    flowSetting: IFlowSetting,
    oModel: sap.cdw.replicationflow.Model
  ): void {
    const oSourceSystem = oModel.sourceSystems.get(0);
    const oTargetSystem = oModel.targetSystems.get(0);
    if (Utils.isConnectionTypeABAP(oSourceSystem?.connectionType)) {
      flowSetting.ABAPcontentType = oReplicationFlowSetting.ABAPcontentType;
      flowSetting.ABAPcontentTypeDisabled = oReplicationFlowSetting.ABAPcontentTypeDisabled;
    }
    if (Utils.isConnectionTypeDwcLTF(oTargetSystem?.connectionId, oTargetSystem?.connectionType)) {
      flowSetting.isAutoMergeEnabledForTarget = oReplicationFlowSetting.isAutoMergeEnabledForTarget;
    }
  }
  static addTaskSettings(
    replicationTaskSetting: sap.cdw.replicationflow.TaskSetting,
    taskSetting: ITaskSetting,
    oModel: sap.cdw.replicationflow.Model
  ): void {
    if (oModel.isDeltaReplicationsExistsInFlow && Utils.isDeltaPartitionEnabled(oModel.sourceSystems.get(0))) {
      taskSetting.globalDeltaPartitionValue = parseInt(replicationTaskSetting?.globalDeltaPartitionValue, 10);
    }
    taskSetting.hasSkipMappingCapability = replicationTaskSetting.hasSkipMappingCapability;
  }

  /**
   *
   *
   * @static
   * @param {sap.cdw.replicationflow.SourceProperties} oSourceProperty
   * @param {(ISystem | IDataset)} entity
   * @memberof ModelToJSON
   */
  static addSourceProperties(oProperty: sap.cdw.replicationflow.Properties, entity: ISystem | IDataset): void {
    const properties: IProperties = {};
    if (oProperty.columnDelimiter) {
      properties.columnDelimiter = Utils.textValueDelimiter(oProperty.columnDelimiter);
    }
    if (oProperty.isHeaderIncluded !== undefined) {
      properties.isHeaderIncluded = oProperty.isHeaderIncluded.toString();
    }
    if (oProperty.format) {
      properties.format = oProperty.format;
    }
    if (oProperty.confluentConsumeOtherSchemaVersions) {
      properties["confluent.read.consume-other-schemas"] = oProperty.confluentConsumeOtherSchemaVersions;
    }
    if (oProperty.confluentConsumeOtherSchemaVersions === "true") {
      properties["confluent.read.handle-schema-mismatch"] = oProperty.confluentHandleSchemaMismatch;
    }
    if (oProperty.confluentFailTruncate) {
      properties["confluent.read.fail-or-truncate"] = oProperty.confluentFailTruncate;
    }
    if (oProperty.confluentOffsetMode) {
      properties["confluent.read.offset-mode"] = oProperty.confluentOffsetMode;
    }
    if (oProperty.confluentReadSchemaSubject) {
      properties["confluent.read.schema.subject"] = oProperty.confluentReadSchemaSubject;
    }
    if (oProperty.confluentReadSchemaVersion) {
      properties["confluent.read.schema.version"] = oProperty.confluentReadSchemaVersion;
    }
    if (oProperty.confluentMessageIdColumn && oProperty.includeTechnicalKey === true) {
      properties["confluent.read.message-id-column"] = oProperty.confluentMessageIdColumn;
    }
    if (oProperty.confluentFlatteningConfig) {
      properties["confluent.read.flattening-config"] = oProperty.confluentFlatteningConfig;
    }
    if (oProperty.confluentReadOpCodeConfig) {
      properties["confluent.read.op-code-spec"] = oProperty.confluentReadOpCodeConfig;
    }
    if (oProperty.csvEncoding !== undefined) {
      properties["objectstore.read.csv.encoding"] = oProperty.csvEncoding;
    }
    if (oProperty.failOnIncompatible !== undefined) {
      properties["objectstore.read.csv.failOnIncompatibleData"] = oProperty.failOnIncompatible.toString();
    }
    if (oProperty.fileGlobalPattern !== undefined) {
      properties["objectstore.read.fileGlobPattern"] = oProperty.fileGlobalPattern;
    }
    if (oProperty.maxPartition !== undefined) {
      properties["objectstore.read.maxPartitions"] = Number(oProperty.maxPartition);
    }
    if (oProperty.clampingData !== undefined) {
      properties["objectstore.read.failOnDataTruncation"] = oProperty.clampingData.toString();
    }
    if (oProperty.includeSubfolder !== undefined) {
      properties["objectstore.read.includeSubfolders"] = oProperty.includeSubfolder.toString();
    }
    if (Object.keys(properties).length) {
      entity.properties = properties;
    }
  }

  /**
   *
   *
   * @static
   * @param {sap.cdw.replicationflow.Properties} oProperty
   * @param {(ISystem | IDataset)} entity
   * @memberof ModelToJSON
   */
  static addProperties(oProperty: sap.cdw.replicationflow.Properties, entity: ISystem | IDataset): void {
    const properties: IProperties = {};
    if (oProperty.columnDelimiter) {
      properties.columnDelimiter = oProperty.columnDelimiter;
    }
    if (oProperty.compression) {
      properties.compression = oProperty.compression;
    }
    if (oProperty.format) {
      properties.format = oProperty.format;
    }
    if (oProperty.groupDeltaFilesBy) {
      properties.groupDeltaFilesBy = oProperty.groupDeltaFilesBy;
    }
    if (oProperty.isHeaderIncluded) {
      properties.isHeaderIncluded = oProperty.isHeaderIncluded;
    }
    if (oProperty.orient) {
      properties.orient = oProperty.orient;
    }
    if (oProperty.kafkaNumPartitions !== undefined) {
      properties.kafkaNumPartitions = oProperty.kafkaNumPartitions;
    }
    if (oProperty.kafkaReplicationFactor !== undefined) {
      properties.kafkaReplicationFactor = oProperty.kafkaReplicationFactor;
    }
    if (oProperty.kafkaSerializationType) {
      properties.kafkaSerializationType = oProperty.kafkaSerializationType;
    }
    if (oProperty.kafkaCompressionFormat) {
      properties.kafkaCompressionFormat = oProperty.kafkaCompressionFormat;
    }
    if (oProperty["com.sap.rms.write.forceDecfloatsToTargetValueRange"]) {
      properties["com.sap.rms.write.forceDecfloatsToTargetValueRange"] =
        oProperty["com.sap.rms.write.forceDecfloatsToTargetValueRange"];
    }

    if (oProperty["objectstore.write.useDuplicateSuppressionInitialLoad"]) {
      properties["objectstore.write.useDuplicateSuppressionInitialLoad"] =
        oProperty["objectstore.write.useDuplicateSuppressionInitialLoad"];
    }
    if (oProperty["objectstore.write.parquet.compatibilityMode"]) {
      properties["objectstore.write.parquet.compatibilityMode"] =
        oProperty["objectstore.write.parquet.compatibilityMode"];
    }

    if (oProperty.kafkaUseSchemaRegistry) {
      properties["kafka.write.useSchemaRegistry"] = oProperty.kafkaUseSchemaRegistry.toString();
    }
    if (oProperty.kafkaUseSchemaRegistry && oProperty.kafkaUseSchemaRegistry.toString() === "true") {
      if (oProperty.kafkaSchemaSubjectNameStrategy) {
        properties["kafka.write.schema.subjectNameStrategy"] = oProperty.kafkaSchemaSubjectNameStrategy;
      }
      if (oProperty.kafkaSchemaCompatibilityMode) {
        properties["kafka.write.schema.compatibilityMode"] = oProperty.kafkaSchemaCompatibilityMode;
      }
      // record name can be saved with empty
      if (oProperty.kafkaSchemaRecordName !== undefined) {
        properties["kafka.write.schema.recordName"] = oProperty.kafkaSchemaRecordName;
      }
    }
    if (Object.keys(properties).length) {
      entity.properties = properties;
    }
  }

  /**
   *
   * @static
   * @param {sap.cdw.replicationflow.Properties} oProperty
   * @param {IDataset} dataset
   * @memberof ModelToJSON
   */
  static addDatasetSourceProperties(oProperty: sap.cdw.replicationflow.Properties, dataset: IDataset): void {
    // checks for properties support done at Model level so not doing here.
    ModelToJSON.addSourceProperties(oProperty, dataset);
  }

  /**
   *
   * @static
   * @param {sap.cdw.replicationflow.SourceProperties} oProperty
   * @param {IDataset} dataset
   * @memberof ModelToJSON
   */
  static addDatasetProperties(oProperty: sap.cdw.replicationflow.Properties, dataset: IDataset): void {
    // checks for properties support done at Model level so not doing here.
    ModelToJSON.addProperties(oProperty, dataset);
  }

  /**
   *
   *
   * @static
   * @param {*} aAttributes
   * @param {IDataset} dataset
   * @memberof ModelToJSON
   */
  static addCDCToDatasetProperties(aAttributes: sap.cdw.replicationflow.Attribute[], dataset: IDataset): void {
    const properties: IProperties = {};
    const oModeColumn = aAttributes.find((o) => o.isCDCColumn && o.CDCColumnType === "mode");
    const oTimestampColumn = aAttributes.find((o) => o.isCDCColumn && o.CDCColumnType === "timestamp");
    if (oModeColumn && oTimestampColumn) {
      properties["com.sap.datasuite.cdc.mode.column"] = oModeColumn.name;
      properties["com.sap.datasuite.cdc.timestamp.column"] = oTimestampColumn.name;
      properties["com.sap.datasuite.tableCategory"] = "DELTA";
    }
    if (Object.keys(properties).length === 3) {
      dataset.properties = Object.assign({}, dataset.properties, properties);
    }
  }

  /**
   *
   * @static
   * @param {sap.cdw.replicationflow.Task} oTask
   * @param {IReplicationTask[]} containerArr
   * @memberof ModelToJSON
   */
  static addReplicationTask(oTask: sap.cdw.replicationflow.Task, containerArr: IReplicationTask[]): void {
    const task: IReplicationTask = {
      name: oTask.name,
      loadType: oTask.loadType,
      priority: oTask.priority,
      truncate: oTask.truncate,
      sourceObject: {
        name: "",
        definition: {
          columns: [],
        },
        metadata: {},
      },
      targetObject: {
        name: "",
        definition: {
          columns: [],
        },
        metadata: {},
      },
    };
    if (
      oTask.maxDesiredParallelDeltaTransfers &&
      (oTask.loadType === Constants.LOAD_TYPE.INITIALDELTA || oTask.loadType === Constants.LOAD_TYPE.DELTA)
    ) {
      task.maxDesiredParallelDeltaTransfers = parseInt(oTask.maxDesiredParallelDeltaTransfers, 10);
    }

    if (oTask.sourceObject) {
      ModelToJSON.updateDataset(oTask.sourceObject, task.sourceObject);
    }
    if (oTask.targetObject) {
      ModelToJSON.updateDataset(oTask.targetObject, task.targetObject);
    }
    if (oTask.transform) {
      ModelToJSON.addTransform(oTask.transform, task);
    }

    containerArr.push(task);
  }

  /**
   *
   * @static
   * @param {sap.cdw.replicationflow.Dataset} oDataset
   * @param {IDataset} obj
   * @memberof ModelToJSON
   */
  static updateDataset(oDataset: sap.cdw.replicationflow.Dataset, obj: IDataset) {
    if (oDataset.datasetType === "source") {
      obj.name = oDataset.isDWCDeltaCapture() ? oDataset.deltaCaptureTableName : oDataset.name;
      if (oDataset.type) {
        obj.metadata["type"] = oDataset.type;
      }
      if (oDataset.isDeltaDisabled) {
        obj.metadata["isDeltaDisabled"] = true;
      }
      if (oDataset.invalidColumns?.length > 0) {
        obj.metadata["invalidColumns"] = oDataset.invalidColumns;
      }
      if (oDataset.metadata["ABAPExitList"]?.length > 0) {
        obj.metadata["ABAPExitList"] = oDataset.metadata["ABAPExitList"];
      }
      if (oDataset.datasetProperties) {
        ModelToJSON.addDatasetSourceProperties(oDataset.datasetProperties, obj);
      }
      if (oDataset.metadata?.["filePath"]) {
        obj.metadata["filePath"] = oDataset.metadata["filePath"];
      }
      if (oDataset.datasetProperties?.includeNotExpandedArraysAndMaps != null) {
        obj.metadata["includeNotExpandedArraysAndMaps"] = oDataset.datasetProperties.includeNotExpandedArraysAndMaps;
      }
      if (oDataset.datasetProperties?.includeTechnicalKey != null) {
        obj.metadata["includeTechnicalKey"] = oDataset.datasetProperties.includeTechnicalKey;
      }
      if (oDataset.datasetProperties?.confluentReadExpandedArray) {
        obj.metadata["confluentReadExpandedArray"] = oDataset.datasetProperties.confluentReadExpandedArray;
      }
      if (oDataset.datasetProperties?.confluentReadExpandedMap) {
        obj.metadata["confluentReadExpandedMap"] = oDataset.datasetProperties.confluentReadExpandedMap;
      }

      if (oDataset.metadata?.["isSourceWithoutPK"]) {
        obj.metadata["isSourceWithoutPK"] = oDataset.metadata["isSourceWithoutPK"];
      }
      if (
        oDataset.datasetProperties?.abapTableCategory ||
        oDataset.datasetProperties?.customerExit ||
        oDataset.datasetProperties?.["com.sap.datasuite.tableCategory"]
      ) {
        obj.properties = {};
        if (oDataset.datasetProperties.abapTableCategory) {
          obj.properties["com.sap.abap.tableCategory"] = oDataset.datasetProperties.abapTableCategory;
        }
        if (oDataset.datasetProperties?.customerExit) {
          obj.properties["com.sap.abap.customerExit"] = oDataset.datasetProperties.customerExit;
        }
        // Adding CDC columns to properties , as CDC columns are not present in source object unlike target object.
        // This information is needed by RMS to identify the CDC columns
        if (oDataset.isDWCDeltaCapture()) {
          obj.properties["com.sap.datasuite.tableCategory"] =
            oDataset.datasetProperties["com.sap.datasuite.tableCategory"];
          obj.properties["com.sap.datasuite.cdc.mode.column"] =
            oDataset.datasetProperties["com.sap.datasuite.cdc.mode.column"];
          obj.properties["com.sap.datasuite.cdc.timestamp.column"] =
            oDataset.datasetProperties["com.sap.datasuite.cdc.timestamp.column"];
          obj.properties["com.sap.datasuite.cdc.subscriber.schema"] =
            oDataset.datasetProperties["com.sap.datasuite.cdc.subscriber.schema"];
        }
      }
    }
    if (oDataset.datasetType === "target") {
      obj.name = oDataset.isDWCDeltaCapture() ? oDataset.deltaCaptureTableName : oDataset.name;
      if (oDataset.metadata["isSACArtefact"]) {
        obj.metadata["isSACArtefact"] = true;
      }
      obj.metadata["isNew"] = oDataset.isNew;
      if (oDataset.invalidColumns?.length > 0) {
        obj.metadata["invalidColumns"] = oDataset.invalidColumns;
      }
      if (oDataset.metadata?.["isDPIDDataset"]) {
        obj.metadata["isDPIDDataset"] = oDataset.metadata["isDPIDDataset"];
      }
      if (oDataset.datasetProperties) {
        ModelToJSON.addDatasetProperties(oDataset.datasetProperties, obj);
      }
      if (oDataset.isCDCDataset) {
        ModelToJSON.addCDCToDatasetProperties(oDataset.attributes.toArray(), obj);
      }
    }
    if (oDataset.businessName) {
      obj.businessName = oDataset.businessName;
    }
    oDataset.attributes.forEach((oAttribute: sap.cdw.replicationflow.Attribute) => {
      ModelToJSON.addAttribute(oAttribute, obj.definition.columns);
    });
  }

  /**
   *
   * @static
   * @param {sap.cdw.replicationflow.Attribute} oAttribute
   * @param {IColumn[]} containerArr
   * @memberof ModelToJSON
   */
  static addAttribute(oAttribute: sap.cdw.replicationflow.Attribute, containerArr: IColumn[]): void {
    let length = oAttribute.length;
    let precision = oAttribute.precision;
    let scale = oAttribute.scale;
    let definition, column;

    if (!isNaN(parseInt(oAttribute.length, 10))) {
      length = parseInt(oAttribute.length, 10);
    }
    if (!isNaN(parseInt(oAttribute.precision, 10))) {
      precision = parseInt(oAttribute.precision, 10);
    }
    if (!isNaN(parseInt(oAttribute.scale, 10))) {
      scale = parseInt(oAttribute.scale, 10);
    }

    if (oAttribute.datatype === "decimal") {
      // handle decimal
      // generate inline scalar vtype for decimal with precision and scale
      const name = `${oAttribute.datatype}_${precision}_${scale}`;
      definition = {
        name: name,
        description: `Decimal(${precision},${scale})`,
        "vflow.type": "scalar",
        template: "decimal",
        "value.precision": precision,
        "value.scale": scale,
      };
      column = {
        name: oAttribute.name,
        "vflow.type": "scalar",
        "vtype-ID": `${DYNAMIC_VTYPE_PREFIX}${name}`,
      };
    } else if (oAttribute.datatype === "string" && length) {
      // handle string with length
      // generate inline scalar vtype for string with length
      const name = `${oAttribute.datatype}_${length}`;
      definition = {
        name: name,
        description: `String(${length})`,
        "vflow.type": "scalar",
        template: "string",
        "value.length": length,
      };
      column = {
        name: oAttribute.name,
        "vflow.type": "scalar",
        "vtype-ID": `${DYNAMIC_VTYPE_PREFIX}${name}`,
      };
    } else if (oAttribute.datatype === "binary" && length) {
      // handle binary with length
      // generate inline scalar vtype for binary with length
      const name = `${oAttribute.datatype}_${length}`;
      definition = {
        name: name,
        description: `Binary(${length})`,
        "vflow.type": "scalar",
        template: "binary",
        "value.length": length,
      };
      column = {
        name: oAttribute.name,
        "vflow.type": "scalar",
        "vtype-ID": `${DYNAMIC_VTYPE_PREFIX}${name}`,
      };
    } else {
      column = {
        name: oAttribute.name,
        "vflow.type": "scalar",
        "vtype-ID": `com.sap.core.${oAttribute.datatype}`,
      };
    }
    if (definition) {
      if (!ModelToJSON.dynamicvTypes[definition.name]) {
        ModelToJSON.dynamicvTypes[definition.name] = definition;
      }
    }
    if (column) {
      if (oAttribute.key) {
        column.key = true;
      }
      if (column.filterNotAllowed) {
        column.filterNotAllowed = oAttribute.filterNotAllowed;
      }
      if (oAttribute.businessName) {
        column.businessName = oAttribute.businessName;
      }

      if (oAttribute.metadata) {
        column.metadata = oAttribute.metadata;
      }
      containerArr.push(column);
    }
  }

  /**
   *
   * @static
   * @param {sap.cdw.replicationflow.Transform} oTransform
   * @param {IReplicationTask} task
   * @memberof ModelToJSON
   */
  static addTransform(oTransform: sap.cdw.replicationflow.Transform, task: IReplicationTask) {
    const transform: ITransform = {
      name: oTransform.name,
      attributeMappings: [],
      filters: [],
    };
    oTransform.attributeMappings.forEach((oAttrMap: sap.cdw.replicationflow.AttributeMapping) => {
      let expression;
      if (oAttrMap.source) {
        expression = `"${oAttrMap.source.name}"`;
      } else if (
        oAttrMap.target.datatype === Constants.DATE_TIME_EXP_FUNCTIONS.DATE.dataType ||
        oAttrMap.target.datatype === Constants.DATE_TIME_EXP_FUNCTIONS.TIME.dataType ||
        oAttrMap.target.datatype === Constants.DATE_TIME_EXP_FUNCTIONS.TIMESTAMP.dataType ||
        Constants.IS_NUMBER_DATA_TYPE(oAttrMap.target.datatype)
      ) {
        expression = oAttrMap.expression;
      } else {
        expression = `'${oAttrMap.expression}'`;
      }
      transform.attributeMappings.push({
        target: oAttrMap.target.name,
        expression: expression,
      });
    });
    oTransform.filters.forEach((oFilter: sap.cdw.replicationflow.Filter) => {
      const filter: IFilter = {
        name: oFilter.name,
        elements: [],
      };
      oFilter.filterElements.forEach((oFilterElement: sap.cdw.replicationflow.FilterElement) => {
        if (oFilterElement.low) {
          const filterElement: IFilterElement = { low: oFilterElement.low };
          if (oFilterElement.comparison) {
            filterElement.comparison = oFilterElement.comparison;
          }
          if (oFilterElement.isExcluding !== undefined) {
            filterElement.isExcluding = oFilterElement.isExcluding;
          }
          if (oFilterElement.high) {
            filterElement.high = oFilterElement.high;
          }
          filter.elements.push(filterElement);
        }
      });
      if (filter.name && filter.elements.length) {
        transform.filters.push(filter);
      }
    });
    task.transform = transform;
  }

  /**
   *
   * @static
   * @param {sap.cdw.replicationflow.Model} oModel
   * @param {*} serializedContent
   * @return {*}
   * @memberof ModelToJSON
   */
  static generateRepoJson(oModel: sap.cdw.replicationflow.Model, serializedContent) {
    return {
      kind: "sap.dis.replicationflow",
      "@EndUserText.label": oModel.label,
      contents: serializedContent,
      sources: ModelToJSON.extractDWCDataset(oModel, "source"),
      targets: ModelToJSON.extractDWCDataset(oModel, "target"),
      connections: ModelToJSON.extractAllNonDWCConnection(oModel),
    };
  }

  /**
   *
   * @static
   * @param {sap.cdw.replicationflow.Model} oModel
   * @param {string} systemType
   * @return {*}  {Record<string, any>}
   * @memberof ModelToJSON
   */
  static extractDWCDataset(oModel: sap.cdw.replicationflow.Model, systemType: string): Record<string, any> {
    const entities = {};
    const oSystem = systemType === "source" ? oModel.sourceSystems.get(0) : oModel.targetSystems.get(0);
    if (oSystem && Utils.isConnectionTypeDWC(oSystem.connectionId, oSystem.connectionType)) {
      const replicationTasks = oModel.replicationTasks.toArray();
      for (const oTask of replicationTasks) {
        const oDataset = systemType === "source" ? oTask.sourceObject : oTask.targetObject;
        let entityName = oDataset.name;
        if (oDataset.isDWCDeltaCapture()) {
          entityName = oDataset.deltaCaptureTableName;
        }
        if (entityName) {
          const elem = { elements: {} };
          entities[entityName] = elem;
          const attributes = oDataset.attributes.toArray();
          for (const oAttr of attributes) {
            if (oAttr.name) {
              elem.elements[oAttr.name] = {};
            }
          }
        }
      }
    }
    return entities;
  }

  /**
   *
   * @static
   * @param {sap.cdw.replicationflow.Model} oModel
   * @return {*}  {Record<string, any>}
   * @memberof ModelToJSON
   */
  static extractAllNonDWCConnection(oModel: sap.cdw.replicationflow.Model): Record<string, any> {
    const entities = {};
    const oSourceSystem = oModel.sourceSystems.get(0);
    const oTargetSystem = oModel.targetSystems.get(0);
    if (
      oSourceSystem &&
      oSourceSystem.connectionId &&
      Constants.CONNECTION_TYPES[oSourceSystem.connectionType] &&
      !Utils.isConnectionTypeDWC(oSourceSystem.connectionId, oSourceSystem.connectionType)
    ) {
      entities[oSourceSystem.connectionId] = {};
    }
    if (
      oTargetSystem &&
      oTargetSystem.connectionId &&
      Constants.CONNECTION_TYPES[oTargetSystem.connectionType] &&
      !Utils.isConnectionTypeDWC(oTargetSystem.connectionId, oTargetSystem.connectionType)
    ) {
      entities[oTargetSystem.connectionId] = {};
    }
    return entities;
  }

  static createTargetDwcTablesDefinition(oModel: sap.cdw.replicationflow.Model): Record<string, any> {
    const definitions: Record<string, any> = {};
    oModel.replicationTasks.forEach((oTask: sap.cdw.replicationflow.Task) => {
      const oTargetObject = oTask.targetObject;
      if (oTargetObject?.isNew) {
        const technicalName: string = oTask.targetObject.name;
        if (oTargetObject.isDWCDeltaCapture()) {
          definitions[oTargetObject.deltaCaptureTableName] = ModelToJSON.getDwcTableDefinition(oTargetObject);
        } else {
          definitions[technicalName] = ModelToJSON.getDwcTableDefinition(oTargetObject);
        }
      }
    });
    return definitions;
  }

  static getDwcTableDefinition(oDataset: sap.cdw.replicationflow.Dataset): Record<string, any> {
    const dataCategoryAnnotated = DataCategoryAnnotation.getAnnotatedObjectFromValue(DataCategory.DATASET);
    const technicalName = oDataset.name;
    const businessName = oDataset.businessName;
    const definition: Record<string, any> = {
      kind: "entity",
      "@EndUserText.label": businessName ? businessName : technicalName,
      ...dataCategoryAnnotated,
    };
    /* not sure if needed
    definition["@Analytics.dataCategory"] = {
      "#": "DataSet",
    };*/
    if (oDataset.isDWCDeltaCapture()) {
      const aAttributes = oDataset.attributes.toArray();
      const changeDateColumn = aAttributes.find((o) => o.CDCColumnType === "timestamp");
      const changeTypeColumn = aAttributes.find((o) => o.CDCColumnType === "mode");
      definition["@DataWarehouse.delta"] = {
        type: { "#": "UPSERT" },
        dateTimeElement: { "=": changeDateColumn.name },
        modeElement: { "=": changeTypeColumn.name },
      };
      definition["@DataWarehouse.enclosingObject"] = technicalName;
      if (oDataset.isLTF) {
        definition["@DataWarehouse.persistence.hdlf.tableFormat"] = Constants.LTF_ANNOTATIONS.DELTA_LAKE;
        definition["@Semantics.interval"] = Constants.LTF_ANNOTATIONS.SEMANTIC_INTERVAL;
        definition["@cds.persistence.udf"] = true;
        definition["params"] = Constants.LTF_ANNOTATIONS.PARAMS;
      }
    }

    definition["elements"] = ModelToJSON.getElementsDefinition(oDataset);
    return definition;
  }

  static getElementsDefinition(oDataset: sap.cdw.replicationflow.Dataset): Record<string, any> {
    const elements = {};
    const attributes = oDataset.attributes.toArray();
    for (const oAttr of attributes) {
      const columnObj: ICsnColumn = {
        "@EndUserText.label": oAttr.businessName ? oAttr.businessName : oAttr.name, // use column name since we do not have business name
        type: Constants.VTypeMapping[oAttr.datatype] || "cds.String",
        key: oAttr.key,
        notNull: oAttr.key ? true : undefined,
        length: undefined,
        precision: undefined,
        scale: undefined,
      };
      let length = 50,
        precision = 1,
        scale = 0;

      if (oAttr.isCDCColumn && oAttr.CDCColumnType === "mode") {
        columnObj.default = { val: "I" };
        columnObj.notNull = true;
      }
      if (oAttr.isCDCColumn && oAttr.CDCColumnType === "timestamp") {
        columnObj.default = { func: "CURRENT_UTCTIMESTAMP" };
        columnObj.notNull = true;
      }

      if (!isNaN(parseInt(oAttr.length))) {
        length = parseInt(oAttr.length);
      }

      if (!isNaN(parseInt(oAttr.precision))) {
        precision = parseInt(oAttr.precision);
      }
      if (!isNaN(parseInt(oAttr.scale))) {
        scale = parseInt(oAttr.scale);
      }

      if (oAttr.datatype === "decimal") {
        columnObj.precision = precision;
        columnObj.scale = scale;
      }
      if (oAttr.datatype === "string") {
        columnObj.length = length;
      }
      if (oAttr.datatype === "binary") {
        columnObj.length = length;
      }
      if (oAttr.datatype === "uint64") {
        columnObj.type = "cds.Decimal";
        columnObj.precision = 20;
        columnObj.scale = 0;
      }
      elements[oAttr.name] = columnObj;
    }
    return elements;
  }

  /**
   *
   * @static
   * @param {sap.cdw.replicationflow.Model} oModel
   * @return {*}  {Record<string, any>}
   * @memberof ModelToJSON
   */
  static updateDeltaCheckInterval(task: IReplicationTask, minutes, hour) {
    if ([Constants.LOAD_TYPE.INITIALDELTA, Constants.LOAD_TYPE.DELTA].includes(task.loadType)) {
      if (minutes == "" && hour == "") {
        task.deltaCheckInterval = undefined;
      } else {
        task.deltaCheckInterval = Utils.convertToSeconds(hour, minutes);
      }
    }
    return task;
  }

  /**
   * When the value of Suppress Duplicate for existing RF is undefined , the property will be updated to false in the system properties and 'updateObjectStoreProperties' will update for each task.
   * For scenario when Existing RF (before property is released) is opened and saved without opening and saving the target connection settings dialog
   * @param oSystem
   */
  static updateSuppressDuplicateOfExistingRFToFalse(oSystem: sap.cdw.replicationflow.System) {
    if (Utils.isConnTypeObjectStore(oSystem.connectionType)) {
      if (oSystem.systemProperties["objectstore.write.useDuplicateSuppressionInitialLoad"] === undefined) {
        oSystem.systemProperties["objectstore.write.useDuplicateSuppressionInitialLoad"] =
          Constants.OBJECT_STORE_DUPLICATE_SUPPRESSION.FALSE;
      }
    }
  }

  /**
   * Updates all tasks with global property value of suppress duplicate and resets spark mode to none for other file format
   *
   * @param task
   * @memberof ModelToJSON
   */
  static updateObjectStoreProperties(task: IReplicationTask, targetSystem: sap.cdw.replicationflow.System) {
    // 16/08 - refactor to move it to system setting dialog instead
    //  Updating all tasks with global property value of suppress duplicate
    if (task.targetObject?.properties !== undefined) {
      task.targetObject.properties["objectstore.write.useDuplicateSuppressionInitialLoad"] =
        targetSystem.systemProperties["objectstore.write.useDuplicateSuppressionInitialLoad"];
    }
  }
}
