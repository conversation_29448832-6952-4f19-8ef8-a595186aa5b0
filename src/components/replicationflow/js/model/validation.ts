/**
 * Copyright 2022 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import Constants from "../utility/constants";
import RFBuilderUtils from "../utility/RFBuilderUtils";
import ValidationUtils from "../utility/validationUtils";
import Utils from "../utils";

sap.galilei.namespace("sap.cdw.replicationflow", function () {
  "use strict";
  const nsLocal = sap.cdw.replicationflow;
  const nsValidationStatus = sap.cdw.commonmodel.ValidationStatus;
  const nsCommonValidation = sap.cdw.commonmodel.Validation;
  const sMessageGroupId = "i18n_rf";

  /**
   * @class
   * Validation implements all methods related to the validation of model objects
   */
  sap.cdw.replicationflow.Validation = sap.galilei.core.defineClass({
    fullClassName: "sap.cdw.replicationflow.Validation",

    statics: {
      // Avoid infinit loop in validateModel()
      isValidatingModel: false,

      getAggregatedValidation: function (oObject, aChildren) {
        return nsCommonValidation.getAggregatedValidation(oObject, aChildren);
      },

      validateModel: async function (oModel: sap.cdw.replicationflow.Model): Promise<any> {
        this.doValidateModel(oModel);
      },

      doValidateModel: async function (oModel: sap.cdw.replicationflow.Model): Promise<any> {
        if (!oModel) {
          return;
        }
        let oSourceSystem, oTargetSystem;
        oModel.clearValidation();
        oModel.resource.applyUndoableAction(
          async function () {
            // Source and target connections and containers have been specified
            if (oModel.sourceSystems.length === 0) {
              nsValidationStatus.createErrorInstance(oModel, sMessageGroupId, "validationSourceNonExist", []);
            } else {
              oSourceSystem = oModel.sourceSystems.get(0);
              if (oSourceSystem.systemContainer === "") {
                nsValidationStatus.createErrorInstance(
                  oModel,
                  sMessageGroupId,
                  "validationSourceContainerNonExist",
                  []
                );
              }
            }
            if (oModel.targetSystems.length === 0) {
              nsValidationStatus.createErrorInstance(oModel, sMessageGroupId, "validationTargetNonExist", []);
            } else {
              oTargetSystem = oModel.targetSystems.get(0);
              if (oTargetSystem.systemContainer === "" /* @todo && connectionSupportsContainer() */) {
                nsValidationStatus.createErrorInstance(
                  oModel,
                  sMessageGroupId,
                  "validationTargetContainerNonExist",
                  []
                );
              }
            }

            // Source and target connection and container combinations must be different
            if (oSourceSystem && oTargetSystem) {
              if (
                oSourceSystem.connectionId === oTargetSystem.connectionId &&
                oSourceSystem.connectionType === oTargetSystem.connectionType &&
                oSourceSystem.systemContainer === oTargetSystem.systemContainer &&
                oSourceSystem.systemContainer !== ""
              ) {
                nsValidationStatus.createErrorInstance(
                  oModel,
                  sMessageGroupId,
                  "validateSourceTargetSystemDifference",
                  []
                );
              }
              // connection type is object store and truncate is disabled
              if (
                Utils.isConnTypeObjectStore(oTargetSystem.connectionType) &&
                !Utils.isConnTypeSFTP(oTargetSystem.connectionType) &&
                !Utils.isConnectionTypeDwcLTF(oTargetSystem.connectionId, oTargetSystem.connectionType)
              ) {
                let isTruncatedisabled = false;
                for (let nIndex = 0; nIndex < oModel.replicationTasks.length; nIndex++) {
                  const oReplicationTask = oModel.replicationTasks.get(nIndex);
                  if (!oReplicationTask.truncate) {
                    isTruncatedisabled = true;
                    break;
                  }
                }
                if (isTruncatedisabled) {
                  nsValidationStatus.createInfoInstance(
                    oModel,
                    sMessageGroupId,
                    "validationTruncateDisabledForObjectTitle",
                    undefined,
                    undefined,
                    undefined,
                    "validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget",
                    ["\n"]
                  );
                }
              }
            }

            // At least one task has been configured
            if (oModel.replicationTasks.length === 0) {
              nsValidationStatus.createErrorInstance(oModel, sMessageGroupId, "validationTaskNonExist", []);
            }

            // duplicate source or target object name validation
            const aSourceObjectNames = oModel.replicationTasks.map((o) => o.sourceObject.name);
            const aTargetObjectNames = oModel.replicationTasks
              .filter((o) => o.targetObject?.name)
              .map((o) => o.targetObject.name);
            const aDuplicateSources = Utils.toFindUniqueDuplicates(aSourceObjectNames);
            const aDuplicateTargets = Utils.toFindUniqueDuplicates(aTargetObjectNames);
            if (aDuplicateSources.length > 0) {
              const oError = nsValidationStatus.createErrorInstance(
                oModel,
                sMessageGroupId,
                "validateDuplicateSources",
                [aDuplicateSources.join(", ")]
              );
              oError.isBlocker = true;
            }
            if (aDuplicateTargets.length > 0) {
              const oError = nsValidationStatus.createErrorInstance(
                oModel,
                sMessageGroupId,
                "validateDuplicateTargets",
                [aDuplicateTargets.join(", ")]
              );
              oError.isBlocker = true;
            }

            for (let nIndex = 0; nIndex < oModel.replicationTasks.length; nIndex++) {
              const oReplicationTask = oModel.replicationTasks.get(nIndex);
              if (oReplicationTask.validate instanceof Function) {
                oReplicationTask.validate(true);
              }
            }

            const deltaTasks = oModel.replicationTasks.filter((task) =>
              [Constants.LOAD_TYPE.INITIALDELTA, Constants.LOAD_TYPE.DELTA].includes(task.loadType)
            );
            if (deltaTasks.length && oModel.isDeltaCheckConfigurationAllowed) {
              let minutes: any = oModel.deltaCheckIntervalMinute;
              let hour: any = oModel.deltaCheckIntervalHour;
              let seconds: any = Utils.convertToSeconds(hour, minutes);
              if (seconds > 86400) {
                nsValidationStatus.createErrorInstance(
                  oModel,
                  sMessageGroupId,
                  "maxHourOrMinErr",
                  [24],
                  undefined,
                  undefined,
                  "maxDeltaInterval",
                  ["\n"],
                  "",
                  undefined,
                  false,
                  "lblHour",
                  undefined
                );
                nsValidationStatus.createErrorInstance(
                  oModel,
                  sMessageGroupId,
                  "maxHourOrMinErr",
                  [59],
                  undefined,
                  undefined,
                  "maxDeltaInterval",
                  ["\n"],
                  "",
                  undefined,
                  false,
                  "lblMinutes",
                  undefined
                );
              }
              if (seconds < 86400) {
                if (hour === "" || hour == undefined || hour < 0 || hour > 24) {
                  nsValidationStatus.createErrorInstance(
                    oModel,
                    sMessageGroupId,
                    "maxHourOrMinErr",
                    [24],
                    undefined,
                    undefined,
                    undefined,
                    undefined,
                    "",
                    undefined,
                    false,
                    "lblHour",
                    undefined
                  );
                }
                if (minutes === "" || minutes == undefined || minutes < 0 || minutes > 59) {
                  nsValidationStatus.createErrorInstance(
                    oModel,
                    sMessageGroupId,
                    "maxHourOrMinErr",
                    [59],
                    undefined,
                    undefined,
                    undefined,
                    undefined,
                    "",
                    undefined,
                    false,
                    "lblMinutes",
                    undefined
                  );
                }
              }
            }

            // Validate the package
            if (oModel?.packageStatus?.length > 0) {
              const msg: any = nsValidationStatus.createWarnInstance(oModel, sMessageGroupId, oModel.packageStatus, [
                oModel.name,
                oModel.packageValue,
              ]);
              msg.resource.applyUndoableAction(
                function () {
                  msg.isExternal = true;
                },
                "isExternal",
                true
              );
            }

            // Validate duplicate columns in all datasets
            // Get list of datasets which has atleast one duplicate column
            if (oModel.replicationTasks.length > 0) {
              const isBlocker = Utils.isConnectionTypeDWC(
                oModel.targetSystems.get(0)?.connectionId,
                oModel.targetSystems.get(0)?.connectionType
              );
              const errorForTasksWithDuplicateColumns = [];
              const warnForTasksWithDuplicateColumns = [];
              oModel.replicationTasks.forEach((oTask: sap.cdw.replicationflow.Task) => {
                if (oTask.targetObject && RFBuilderUtils.hasDuplicateColumns(oTask.targetObject.attributes.toArray())) {
                  if (oTask.targetObject.isNew) {
                    errorForTasksWithDuplicateColumns.push(oTask.targetObject.name);
                  } else {
                    warnForTasksWithDuplicateColumns.push(oTask.targetObject.name);
                  }
                }
              });
              // Blocker for new targets
              if (errorForTasksWithDuplicateColumns.length > 0) {
                nsValidationStatus.createErrorInstance(
                  oModel,
                  sMessageGroupId,
                  "validateDuplicateColumnsForNewTargets",
                  undefined,
                  undefined,
                  undefined,
                  "validateDuplicateColumnsForNewTargetsDescription",
                  [errorForTasksWithDuplicateColumns.join(", ")],
                  "",
                  undefined,
                  false,
                  undefined,
                  undefined,
                  isBlocker
                );
              }
              if (warnForTasksWithDuplicateColumns.length > 0) {
                nsValidationStatus.createWarnInstance(
                  oModel,
                  sMessageGroupId,
                  "validateDuplicateColumnsExistingTargets",
                  undefined,
                  undefined,
                  undefined,
                  "validateDuplicateColumnsExistingTargetsDescription",
                  [warnForTasksWithDuplicateColumns.join(", ")]
                );
              }
            }

            nsLocal.Validation.requestRefreshDecorators(oModel);
          },
          "New Validation",
          /* bIsprotectedFromUndo*/ true
        );
      },

      validateReplicationTask: function (
        oTask: sap.cdw.replicationflow.Task,
        bSkipRefreshDecorators,
        bSkipClear,
        bSkipNoAttributes,
        bSkipDuplicate
      ) {
        const sResult = "ok";
        if (oTask) {
          if (bSkipClear !== true) {
            oTask.rowHighlight.isError = false;
            oTask.rowHighlight.isInfo = false;
            oTask.rowHighlight.isWarning = false;
            oTask.clearValidation();
          }
          if (oTask.sourceObject) {
            // NOTE: VALIDATION for specific source objects starts from here
            // - validate<ConnectionType>SourceObject() methods are called for specific source objects
            // only responsible for scenarios specific to source object, ideally UA only talks about source object and how to fix it in source object, not target object

            if (
              Utils.isConnTypeObjectStore(oTask.sourceSystem.connectionType) &&
              !Utils.isConnectionTypeHdlfWithDeltaShare(
                oTask.sourceSystem?.connectionType,
                oTask.sourceSystem?.metadata?.["isSourceHDLFDeltashare"]
              )
            ) {
              this.validateObjectStoreSourceObject(oTask);
            }

            if (
              oTask.sourceObject.type === Constants.SOURCE_OBJECT_TYPE.VIEW &&
              (Utils.isConnectionTypeDwcHANA(oTask.sourceSystem.connectionId, oTask.sourceSystem.connectionType) ||
                Utils.isConnectionTypeHANA(oTask.sourceSystem.connectionId, oTask.sourceSystem.connectionType))
            ) {
              this.validateHanaViewSourceObject(oTask);
            }
            if (Utils.isConnectionTypeABAP(oTask.sourceSystem.connectionType)) {
              this.validateABAPSourceObject(oTask);
            }
            if (Utils.isConnectionTypeCONFLUENT(oTask.sourceSystem.connectionType)) {
              this.validateConfluentSourceObject(oTask);
            }
          }
          if (!oTask.targetObject) {
            nsValidationStatus.createErrorInstance(oTask, sMessageGroupId, "validationTaskTargetMissing", [
              oTask.sourceObject.name,
            ]);
          } else {
            try {
              this.globalTaskValidation(oTask); //Try catch can be removed after all the validation methods are implemented
            } catch (e) {
              console.error(e);
            }
            this.deltaOnlyLoadTypeValidation(oTask);
            //  Validation for HDLF-Datasphere connection
            if (
              Utils.isConnectionTypeHdlfWithDeltaShare(
                oTask.sourceSystem.connectionType,
                oTask.sourceSystem.metadata["isSourceHDLFDeltashare"]
              )
            ) {
              this.validateHDLF(oTask);
              this.validateHDLFTargetObjectPK(oTask);
            } else {
              // For HDLF Delta share the DWC Delta Capture is already validated
              if (oTask.targetObject.isDWCDeltaCapture()) {
                if (oTask.sourceObject.isDeltaDisabled) {
                  nsValidationStatus.createErrorInstance(
                    oTask,
                    sMessageGroupId,
                    "validationTaskSourceObjectTargetObjectDeltaMismatchUpdated",
                    [oTask.sourceObject.name, oTask.targetObject.name]
                  );
                  oTask.rowHighlight.isError = true;
                } else {
                  if (oTask.loadType === Constants.LOAD_TYPE.INITIAL) {
                    nsValidationStatus.createErrorInstance(
                      oTask,
                      sMessageGroupId,
                      "validationTaskTargetObjectLoadTypeMismatch",
                      [oTask.targetObject.name]
                    );
                    oTask.rowHighlight.isError = true;
                  }
                }
              }
            }

            // NOTE: VALIDATION for specific target objects starts from here
            // - validate<ConnectionType>TargetObject() methods are called for specific target objects
            // only responsible for scenarios specific to target object, ideally UA only talks about target object and how to fix it in target object

            if (Utils.isConnectionTypeGBQ(oTask.targetSystem.connectionType)) {
              this.validateGBQTargetObject(oTask);
            }
            if (Utils.isConnectionTypeCONFLUENT(oTask.targetSystem.connectionType)) {
              this.validateConfluentTargetObject(oTask);
            }
            if (Utils.isConnectionTypeKafka(oTask.targetSystem.connectionType)) {
              this.validateKafkaTargetObject(oTask);
            }
            if (Utils.isConnectionTypeDWC(oTask.targetSystem.connectionId, oTask.targetSystem.connectionType)) {
              if (oTask.targetObject.metadata?.["isSACArtefact"]) {
                const oError = nsValidationStatus.createErrorInstance(
                  oTask,
                  sMessageGroupId,
                  "validationTaskTargetIsSAC",
                  [oTask.targetObject.name]
                );
                oError.isBlocker = true;
                oTask.rowHighlight.isError = true;
              }
              if (oTask.targetObject.isSupportedDataset === false) {
                const oError = nsValidationStatus.createErrorInstance(
                  oTask,
                  sMessageGroupId,
                  "validationTaskTargetIsNonDwcTableKey",
                  [oTask.targetObject.name],
                  undefined,
                  undefined,
                  "validationTaskTargetIsNonDwcTableDescription",
                  []
                );
                oError.isBlocker = true;
                oTask.rowHighlight.isError = true;
              }
              if (oTask.targetObject.isUniqueTechnicalName === false) {
                const oError = nsValidationStatus.createErrorInstance(
                  oTask,
                  sMessageGroupId,
                  "validationDuplicateTechnicalNameKey",
                  [oTask.targetObject.name],
                  undefined,
                  undefined,
                  "validationDuplicateTechnicalNameDescription",
                  [oTask.targetObject.deltaCaptureTableName]
                );
                oError.isBlocker = true;
                oTask.rowHighlight.isError = true;
              }

              this.validateDWCTargetObject(oTask);
            }
            if (Utils.isConnectionTypeSignavio(oTask.targetSystem.connectionType)) {
              this.validateSignavioTargetObject(oTask);
            }

            // NOTE: VALIDATION for specific source system with target system combination starts from here
            // 2 combincations possible for each source and target objects combination
            // - validate<ConnectionType>SourceWithAllTarget() methods are called for specific source with any target objects comination
            // - validateAllSourceWith<ConnectionType>Target() methods are called for any source and with specific target objects
            // only responsible for scenarios with both source and target objects, UA can give specific messages to connection combincations here

            // abap -> *
            if (Utils.isConnectionTypeABAP(oTask.sourceSystem.connectionType)) {
              this.validateABAPSourceForAllTargets(oTask);
            }

            // * -> LTF
            if (Utils.isConnectionTypeDwcLTF(oTask.targetSystem.connectionId, oTask.targetSystem.connectionType)) {
              this.validateAllSourceWithDWCLTFTarget(oTask);
            }
          }
          if (!bSkipRefreshDecorators) {
            nsLocal.Validation.requestRefreshDecorators(oTask.container);
          }
        }

        return sResult;
      },

      requestRefreshDecorators: function (oModel) {
        if (!oModel) {
          return;
        }
        if (typeof sap !== "undefined" && sap.ui && sap.ui.getCore instanceof Function) {
          sap.ui
            .getCore()
            .getEventBus()
            .publish(nsCommonValidation.VALIDATION_CHANNEL, nsCommonValidation.REFRESH_VALIDATION_EVENT, {
              model: oModel,
            });
        }
      },

      /**
       * Returns a localized text
       * @param textId id of the text (i18n properties file)
       * @param parameters values for the placeholders of the text resource (see i18n properties file, {0}, {1}, ...)
       */
      localizeText: function (textId: string, parameters: string[]) {
        if (
          typeof sap !== "undefined" &&
          sap.ui &&
          sap.ui.getCore instanceof Function &&
          sap.ui.model &&
          sap.ui.model.resource &&
          sap.ui.model.resource.ResourceModel
        ) {
          const bundleName = require("../../i18n/i18n.properties");
          const resourceModel = new sap.ui.model.resource.ResourceModel({
            bundleName: bundleName,
          });
          return resourceModel.getResourceBundle().getText(textId, parameters);
        }
        return textId;
      },

      validateObjectStoreSourceObject(oTask: sap.cdw.replicationflow.Task) {
        this.validateObjectStoreSourceObjectPK(oTask);
        this.validateMaxPartitionValue(oTask);
        this.validateCsvEncoding(oTask);
      },

      validateHanaViewSourceObject(oTask: sap.cdw.replicationflow.Task) {
        const sourceAttr = oTask.sourceObject.attributes.toArray();
        const keyColCount = RFBuilderUtils.keysInObject(sourceAttr);
        const targetSys = oTask.targetSystem;
        const targetConnType = targetSys?.connectionType;
        const targetConnId = targetSys?.connectionId;
        let isBlocker = false;
        if (targetSys && Utils.isConnectionTypeDwcHANA(targetConnId, targetConnType)) {
          isBlocker = true;
        }

        if (keyColCount === 0) {
          nsValidationStatus.createErrorInstance(
            oTask,
            sMessageGroupId,
            "validateHanaViewAsSourceWithNoPKDatasetError",
            undefined,
            undefined,
            undefined,
            "validateHanaViewAsSourceWithNoPKDatasetErrorDescription",
            [],
            "",
            undefined,
            false,
            "validateHanaViewAsSourceWithNoPKDatasetSubtitle",
            [oTask.sourceObject.name],
            isBlocker
          );
          oTask.rowHighlight.isError = true;
        }
      },

      validateGBQTargetObject: function (oTask: sap.cdw.replicationflow.Task) {
        const targetAttr = oTask.targetObject.attributes.toArray();
        const validations = RFBuilderUtils.validateGBQTargetObjectAttributes(targetAttr);
        if (validations.renamedAttrList.length) {
          const descParam = validations.renamedAttrList.map((name) => `• ${name}`).join("\n");
          nsValidationStatus.createInfoInstance(
            oTask,
            sMessageGroupId,
            "validationAutoRenameTarget",
            undefined,
            undefined,
            undefined,
            "validationAutoRenameTargetDescriptionUpdated",
            [descParam, "\n", "•"],
            "",
            undefined,
            "validationAutoRenameTargetSubTitle",
            [oTask.targetObject.name]
          );
        }
        if (validations.dataTypeConvrtedAttrList.length) {
          const descParam = validations.dataTypeConvrtedAttrList.map((name) => `• ${name}`).join("\n");
          nsValidationStatus.createInfoInstance(
            oTask,
            sMessageGroupId,
            "validationAutoTargetTypeConversion",
            undefined,
            undefined,
            undefined,
            "validationAutoTargetTypeConversionDescriptionUpdated",
            [descParam, "\n"],
            "",
            undefined,
            "validationAutoRenameTargetSubTitle",
            [oTask.targetObject.name]
          );
        }
        if (validations.maxCharAttrList.length) {
          const descParam = validations.maxCharAttrList.map((name) => `• ${name}`).join("\n");
          nsValidationStatus.createErrorInstance(
            oTask,
            sMessageGroupId,
            "validationMaxCharLengthGBQTarget",
            undefined,
            undefined,
            undefined,
            "validationMaxCharLengthGBQTargetDescription",
            [descParam, "\n"],
            "",
            undefined,
            false,
            "validationMaxCharLengthGBQTargetSubTitle",
            [oTask.targetObject.name]
          );
        }
        if (validations.keyColCount > 16) {
          nsValidationStatus.createWarnInstance(
            oTask,
            sMessageGroupId,
            "validationGBQUnableToCreateKey",
            [],
            undefined,
            undefined,
            "validationGBQUnableToCreateKeyMaxKeyCombinationDescription",
            [],
            "",
            undefined,
            "validationGBQUnableToCreateKeySubTitle",
            [oTask.targetObject.name]
          );
        }
        if (validations.keyColCount <= 16 && validations.isUncompatibleKeyType) {
          nsValidationStatus.createWarnInstance(
            oTask,
            sMessageGroupId,
            "validationGBQUnableToCreateKey",
            [],
            undefined,
            undefined,
            "validationIncompatiblePKTypeDescUpdated4",
            ["\n", "•"],
            "",
            undefined,
            "validationGBQUnableToCreateKeySubTitle",
            [oTask.targetObject.name]
          );
        }

        oTask.rowHighlight.isError = validations.maxCharAttrList.length > 0;
        oTask.rowHighlight.isInfo =
          validations.renamedAttrList.length > 0 || validations.dataTypeConvrtedAttrList.length > 0;

        if (!oTask.rowHighlight.isWarning) {
          oTask.rowHighlight.isWarning = validations.keyColCount > 16 || validations.isUncompatibleKeyType;
        }
      },

      validateConfluentTargetObject: function (oTask: sap.cdw.replicationflow.Task) {
        let isInvalidConfluentConnectionSetting = false;
        const kafkaNumPartitions = oTask.targetObject.datasetProperties?.kafkaNumPartitions,
          rRactor = oTask.targetObject.datasetProperties?.kafkaReplicationFactor;
        if (oTask.targetObject.datasetProperties) {
          if (!kafkaNumPartitions) {
            nsValidationStatus.createErrorInstance(
              oTask,
              sMessageGroupId,
              "emptyPartionHeader",
              undefined,
              undefined,
              undefined,
              "invalidpartitionsDesc",
              undefined,
              "",
              undefined,
              false,
              "targetObjectNameSubTitle",
              [oTask.targetObject.name]
            );
            isInvalidConfluentConnectionSetting = true;
          }
          if (kafkaNumPartitions && (kafkaNumPartitions < 1 || kafkaNumPartitions > Constants.MAX_KAFKA_PARTITIONS)) {
            nsValidationStatus.createErrorInstance(
              oTask,
              sMessageGroupId,
              "invalidPartitionsHeader",
              undefined,
              undefined,
              undefined,
              "invalidpartitionsDesc",
              undefined,
              "",
              undefined,
              false,
              "targetObjectNameSubTitle",
              [oTask.targetObject.name]
            );
            isInvalidConfluentConnectionSetting = true;
          }
          if (!rRactor) {
            nsValidationStatus.createErrorInstance(
              oTask,
              sMessageGroupId,
              "emptyrFactorHeader",
              undefined,
              undefined,
              undefined,
              "invalidrFactorDesc",
              undefined,
              "",
              undefined,
              false,
              "targetObjectNameSubTitle",
              [oTask.targetObject.name]
            );
            isInvalidConfluentConnectionSetting = true;
          }
          if (rRactor && (rRactor < 1 || rRactor > Constants.MAX_KAFKA_REPLICATION_FACTOR)) {
            nsValidationStatus.createErrorInstance(
              oTask,
              sMessageGroupId,
              "invalidrFactorHeader",
              undefined,
              undefined,
              undefined,
              "invalidrFactorDesc",
              undefined,
              "",
              undefined,
              false,
              "targetObjectNameSubTitle",
              [oTask.targetObject.name]
            );
            isInvalidConfluentConnectionSetting = true;
          }
        }
        const validationStatus = RFBuilderUtils.validateRecordName(
          oTask.targetObject.datasetProperties?.kafkaSchemaRecordName,
          oTask.targetObject.datasetProperties?.kafkaUseSchemaRegistry,
          oTask.targetObject.datasetProperties?.kafkaSerializationType
        );
        if (validationStatus === "empty") {
          nsValidationStatus.createErrorInstance(
            oTask,
            sMessageGroupId,
            "emptyRecordNameValidationHeaderMsg",
            undefined,
            undefined,
            undefined,
            "emptyRecordNameValidationDescMsg",
            ["\n", "•"],
            "",
            undefined,
            false,
            "targetObjectNameSubTitle",
            [oTask.targetObject.name]
          );
          isInvalidConfluentConnectionSetting = true;
        }
        if (validationStatus === "invalid") {
          nsValidationStatus.createErrorInstance(
            oTask,
            sMessageGroupId,
            "validRecordNameValidationHeaderMsg",
            undefined,
            undefined,
            undefined,
            "validRecordNameValidationDescMsgUpdated",
            [],
            "",
            undefined,
            false,
            "targetObjectNameSubTitle",
            [oTask.targetObject.name]
          );
          isInvalidConfluentConnectionSetting = true;
        }
        oTask.rowHighlight.isError = oTask.rowHighlight.isError || isInvalidConfluentConnectionSetting;
        if (oTask.targetObject.isAutoRenamed === true) {
          nsValidationStatus.createInfoInstance(
            oTask,
            sMessageGroupId,
            "autoRenameInfo",
            undefined,
            undefined,
            undefined,
            "autoRenameInfoDesc",
            ["\n", "• "],
            "",
            undefined,
            "validationAutoRenameTargetSubTitle",
            [oTask.targetObject.name]
          );
        }
        const renamedColumns = oTask.targetObject.attributes.toArray().filter((attr) => attr.isAutoRenamed === true);
        if (renamedColumns.length) {
          const descParam = renamedColumns.map((col) => `• ${col.name}`).join("\n");
          nsValidationStatus.createInfoInstance(
            oTask,
            sMessageGroupId,
            "validationAutoRenameTarget",
            undefined,
            undefined,
            undefined,
            "validationAutoRenameTargetDescriptionConfluent",
            [descParam, "\n", "•"],
            "",
            undefined,
            "validationAutoRenameTargetSubTitle",
            [oTask.targetObject.name]
          );
        }

        if (oTask.targetObject.isAutoRenamed || renamedColumns.length) {
          oTask.rowHighlight.isInfo = true;
        } else {
          oTask.rowHighlight.isInfo = false;
        }
      },

      validateKafkaTargetObject: function (oTask: sap.cdw.replicationflow.Task) {
        let isInvalidKafkaConnectionSetting = false;
        const kafkaNumPartitions = oTask.targetObject.datasetProperties?.kafkaNumPartitions,
          rRactor = oTask.targetObject.datasetProperties?.kafkaReplicationFactor;
        if (oTask.targetObject.datasetProperties) {
          if (!kafkaNumPartitions) {
            nsValidationStatus.createErrorInstance(
              oTask,
              sMessageGroupId,
              "emptyPartionHeader",
              undefined,
              undefined,
              undefined,
              "invalidpartitionsDesc",
              undefined,
              "",
              undefined,
              false,
              "targetObjectNameSubTitle",
              [oTask.targetObject.name]
            );
            isInvalidKafkaConnectionSetting = true;
          }
          if (kafkaNumPartitions && (kafkaNumPartitions < 1 || kafkaNumPartitions > Constants.MAX_KAFKA_PARTITIONS)) {
            nsValidationStatus.createErrorInstance(
              oTask,
              sMessageGroupId,
              "invalidPartitionsHeader",
              undefined,
              undefined,
              undefined,
              "invalidpartitionsDesc",
              undefined,
              "",
              undefined,
              false,
              "targetObjectNameSubTitle",
              [oTask.targetObject.name]
            );
            isInvalidKafkaConnectionSetting = true;
          }
          if (!rRactor) {
            nsValidationStatus.createErrorInstance(
              oTask,
              sMessageGroupId,
              "emptyrFactorHeader",
              undefined,
              undefined,
              undefined,
              "invalidrFactorDesc",
              undefined,
              "",
              undefined,
              false,
              "targetObjectNameSubTitle",
              [oTask.targetObject.name]
            );
            isInvalidKafkaConnectionSetting = true;
          }
          if (rRactor && (rRactor < 1 || rRactor > Constants.MAX_KAFKA_REPLICATION_FACTOR)) {
            nsValidationStatus.createErrorInstance(
              oTask,
              sMessageGroupId,
              "invalidrFactorHeader",
              undefined,
              undefined,
              undefined,
              "invalidrFactorDesc",
              undefined,
              "",
              undefined,
              false,
              "targetObjectNameSubTitle",
              [oTask.targetObject.name]
            );
            isInvalidKafkaConnectionSetting = true;
          }
        }

        oTask.rowHighlight.isError = oTask.rowHighlight.isError || isInvalidKafkaConnectionSetting;
      },

      /**
       * Validates the tasks for Object store as source for Primary keys
       * @param oTask
       */
      validateCsvEncoding: function (oTask: sap.cdw.replicationflow.Task) {
        const csvEncoding = oTask.sourceObject?.datasetProperties?.csvEncoding;
        const sourceConnType = oTask.sourceSystem.connectionType;
        const sourceConnId = oTask.sourceSystem.connectionId;
        const isBlocker = Utils.isConnectionTypeDWC(sourceConnId, sourceConnType) ? true : false;
        if (csvEncoding && !Object.values(Constants.VALID_CSV_ENCODING).includes(csvEncoding)) {
          nsValidationStatus.createErrorInstance(
            oTask,
            sMessageGroupId,
            "validationCSVEncoding",
            [oTask.sourceObject.name],
            undefined,
            undefined,
            "validationCSVEncodingDescription",
            undefined,
            undefined,
            undefined,
            undefined,
            undefined,
            undefined,
            isBlocker
          );
          oTask.rowHighlight.isError = true;
        }
      },
      validateObjectStoreSourceObjectPK: function (oTask: sap.cdw.replicationflow.Task) {
        const sourceAttr = oTask.sourceObject.attributes.toArray();
        const validations = RFBuilderUtils.validateHDLFTargetObjectAttributes(sourceAttr);
        const targetConnType = oTask.targetSystem?.connectionType;
        const targetConnId = oTask.targetSystem?.connectionId;
        let isBlocker;
        if (oTask.targetSystem) {
          isBlocker = Utils.isConnectionTypeDWC(targetConnId, targetConnType) ? true : false;
        } else {
          isBlocker = false;
        }
        if (validations.keyColCount === 0) {
          nsValidationStatus.createErrorInstance(
            oTask,
            sMessageGroupId,
            "validateObjectStoreNoPKDatasetError",
            undefined,
            undefined,
            undefined,
            "validateObjectStoreNoPKDatasetErrorDescription1",
            [],
            "",
            undefined,
            false,
            "validateHDLFNoPKDatasetSubtitle",
            [oTask.sourceObject.name],
            isBlocker
          );
        }
        if (validations.keyColCount === 0) {
          oTask.rowHighlight.isError = true;
        }
      },

      /**
       * Validates the tasks for Object store as source for Primary keys
       * @param oTask
       */

      validateMaxPartitionValue: function (oTask: sap.cdw.replicationflow.Task) {
        const maxPartition = oTask.sourceObject?.datasetProperties?.maxPartition;
        const isValid = RFBuilderUtils.validateMaxPartition(maxPartition);
        if (!isValid) {
          const isBlocker = true;
          nsValidationStatus.createErrorInstance(
            oTask,
            sMessageGroupId,
            "validateMaxPartitionValueError",
            undefined,
            undefined,
            undefined,
            "validateMaxPartitionValueErrorDescriptionUpdated",
            [],
            "",
            undefined,
            false,
            "validateHDLFNoPKDatasetSubtitle",
            [oTask.sourceObject.name],
            isBlocker
          );
          oTask.rowHighlight.isError = true;
        }
      },

      /**
       * Validates the tasks for HDLF as source for Primary keys
       * @param oTask
       */
      validateHDLFTargetObjectPK: function (oTask: sap.cdw.replicationflow.Task) {
        const targetAttr = oTask.targetObject.attributes.toArray();
        const validations = RFBuilderUtils.validateHDLFTargetObjectAttributes(targetAttr);
        const isBlocker = true;

        if (validations.keyColCount === 0) {
          if (oTask.targetObject.isNew) {
            nsValidationStatus.createErrorInstance(
              oTask,
              sMessageGroupId,
              "validateHDLFNoPKDatasetError",
              undefined,
              undefined,
              undefined,
              "validateHDLFNoPKDatasetErrorDescription1",
              [],
              "",
              undefined,
              false,
              "validateHDLFNoPKDatasetSubtitle",
              [oTask.targetObject.name],
              isBlocker
            );
          } else {
            nsValidationStatus.createErrorInstance(
              oTask,
              sMessageGroupId,
              "validateHDLFNoPKExistingDatasetError",
              undefined,
              undefined,
              undefined,
              "validateHDLFNoPKExistingDatasetErrorDescription1",
              ["\n\n", "•"],
              "",
              undefined,
              false,
              "validateHDLFNoPKExistingDatasetSubtitle",
              [oTask.targetObject.name]
            );
          }
        }
        if (validations.keyColCount === 0) {
          oTask.rowHighlight.isError = true;
        }
      },

      validateHDLF: function (oTask: sap.cdw.replicationflow.Task) {
        const isBlocker = true;

        //  Load Type should be Initial + Delta Only
        if (oTask.loadType === Constants.LOAD_TYPE.INITIAL) {
          oTask.rowHighlight.isError = true;
          nsValidationStatus.createErrorInstance(
            oTask,
            sMessageGroupId,
            "sourceHDLFLoadTypeError",
            undefined,
            undefined,
            undefined,
            "sourceHDLFLoadTypeErrorDescription1",
            [],
            "",
            undefined,
            false,
            "sourceHDLFLoadTypeSubtitle",
            [oTask.targetObject.name]
          );
        }
        if (!oTask.targetObject.isCDCDataset) {
          if (oTask.targetObject.isNew) {
            //  For New Targets Delta Capture should be on
            nsValidationStatus.createErrorInstance(
              oTask,
              sMessageGroupId,
              "sourceHDLFDeltaCaptureErrorForNewTarget",
              undefined,
              undefined,
              undefined,
              "sourceHDLFDeltaCaptureErrorForNewTargetDescription1",
              [],
              "",
              undefined,
              false,
              "sourceHDLFDeltaCaptureSubtitleForNewTarget",
              [oTask.targetObject.name],
              isBlocker
            );
            oTask.rowHighlight.isError = true;
          } else {
            // Change Target if Delta Capture Off for Existing Target
            oTask.rowHighlight.isError = true;
            nsValidationStatus.createErrorInstance(
              oTask,
              sMessageGroupId,
              "sourceHDLFDeltaCaptureErrorForExistingTarget",
              undefined,
              undefined,
              undefined,
              "sourceHDLFDeltaCaptureErrorForExistingTargetDescription1",
              [],
              "",
              undefined,
              false,
              "sourceHDLFDeltaCaptureSubtitleForExistingTarget",
              [oTask.targetObject.name]
            );
          }
        }
      },

      validateABAPSourceObject: function (oTask: sap.cdw.replicationflow.Task) {
        if (
          Utils.isDeltaPartitionEnabled(oTask.sourceSystem) &&
          (oTask.loadType === Constants.LOAD_TYPE.INITIALDELTA || oTask.loadType === Constants.LOAD_TYPE.DELTA)
        ) {
          //  Load Type should be Initial + Delta Only
          if (oTask.maxDesiredParallelDeltaTransfers) {
            const deltaPartition = parseInt(oTask.maxDesiredParallelDeltaTransfers, 10);
            if (deltaPartition < Constants.MIN_PARTITION_VALUE || deltaPartition > Constants.MAX_PARTITION_VALUE) {
              nsValidationStatus.createErrorInstance(
                oTask,
                sMessageGroupId,
                "deltaPartitionError",
                undefined,
                undefined,
                undefined,
                "deltaPartitionErrorDescription",
                [],
                "",
                undefined,
                false,
                oTask.sourceObject.name
              );
              oTask.rowHighlight.isError = true;
            }
          }
          if (oTask.maxDesiredParallelDeltaTransfers === "") {
            nsValidationStatus.createErrorInstance(
              oTask,
              sMessageGroupId,
              "deltaPartitionEmptyError",
              undefined,
              undefined,
              undefined,
              "deltaPartitionErrorDescription",
              [],
              "",
              undefined,
              false,
              oTask.sourceObject.name
            );
            oTask.rowHighlight.isError = true;
          }
        }
      },

      /**
       * Validates the task for ABAP (CDS/ODP) without PK
       * @param oTask
       */
      validateABAPSourceForAllTargets: function (oTask: sap.cdw.replicationflow.Task) {
        const isABAPSourceWithoutKey = oTask.sourceObject.metadata["isSourceWithoutPK"] ? true : false;
        const sourceConnectionType = oTask.sourceSystem.connectionType;
        const sourceContainer = oTask.sourceSystem.systemContainer;
        const targetConnType = oTask.targetSystem.connectionType;
        const targetConnId = oTask.targetSystem.connectionId;
        const targetAttr = oTask.targetObject.attributes.toArray();
        const isBlocker = true;

        if (isABAPSourceWithoutKey && Utils.isConnTypeABAPCdsOrOdp(sourceConnectionType, sourceContainer)) {
          // ABAP (without keys) as source -> ltf as target not supported
          if (Utils.isConnectionTypeDwcLTF(targetConnId, targetConnType)) {
            oTask.rowHighlight.isError = true;
            const isBlocker = oTask.targetObject.isNew;
            nsValidationStatus.createErrorInstance(
              oTask,
              sMessageGroupId,
              "sourceABAPWithoutKeyWithLTF",
              undefined,
              undefined,
              undefined,
              "sourceABAPWithoutKeyWithLTFDescription",
              [],
              "",
              undefined,
              false,
              "sourceABAPWithoutKeyWithLTFSubtitle",
              [oTask.sourceObject.name],
              isBlocker
            );
          } else {
            // Loadtype should be Initial Only
            if (oTask.loadType === Constants.LOAD_TYPE.INITIALDELTA || oTask.loadType === Constants.LOAD_TYPE.DELTA) {
              nsValidationStatus.createErrorInstance(
                oTask,
                sMessageGroupId,
                "sourceABAPWithoutKeyLoadTypeError",
                undefined,
                undefined,
                undefined,
                "sourceABAPWithoutKeyLoadTypeErrorDescription1",
                [],
                "",
                undefined,
                false,
                "sourceABAPWithoutKeyLoadTypeSubtitle",
                [oTask.targetObject.name]
              );
              oTask.rowHighlight.isError = true;
            }

            // Delta enabled local tables not allowed
            if (Utils.isConnectionTypeDwcHANA(targetConnId, targetConnType) && oTask.targetObject.isCDCDataset) {
              oTask.rowHighlight.isError = true;
              if (oTask.targetObject.isNew) {
                nsValidationStatus.createErrorInstance(
                  oTask,
                  sMessageGroupId,
                  "sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget",
                  undefined,
                  undefined,
                  undefined,
                  "sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1",
                  [],
                  "",
                  undefined,
                  false,
                  "sourceABAPWithoutKeyDeltaCaptureSubtitle",
                  [oTask.targetObject.name],
                  isBlocker
                );
              } else {
                // Change Target if Delta Capture On for Existing Target
                nsValidationStatus.createErrorInstance(
                  oTask,
                  sMessageGroupId,
                  "sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1",
                  undefined,
                  undefined,
                  undefined,
                  "sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1",
                  [],
                  "",
                  undefined,
                  false,
                  "sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget",
                  [oTask.targetObject.name]
                );
              }
            }

            // Existing Targets Conditions
            if (!oTask.targetObject.isNew) {
              //  dwc local hana or remote HANA Targets
              if (
                Utils.isConnectionTypeDwcHANA(targetConnId, targetConnType) ||
                targetConnType === Constants.CONNECTION_TYPES.HANA
              ) {
                // Primary key not allowed in target
                if (targetAttr.some((item: any) => item.key === true)) {
                  oTask.rowHighlight.isError = true;
                  nsValidationStatus.createErrorInstance(
                    oTask,
                    sMessageGroupId,
                    "sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1",
                    undefined,
                    undefined,
                    undefined,
                    "sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2",
                    [],
                    "",
                    undefined,
                    false,
                    "sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget",
                    [oTask.targetObject.name]
                  );
                } else {
                  // DPID Column present but datatype mismatch
                  if (
                    targetAttr.some(
                      (item: any) =>
                        item.name === Constants.ABAP_DPID_COLUMN.COL_NAME_FOR_HANA_TARGETS &&
                        (item.datatype !== Constants.ABAP_DPID_COLUMN.DATATYPE_FOR_HANA_TARGETS ||
                          item.length !== Constants.ABAP_DPID_COLUMN.LENGTH_FOR_HANA_TARGETS)
                    )
                  ) {
                    oTask.rowHighlight.isError = true;
                    nsValidationStatus.createErrorInstance(
                      oTask,
                      sMessageGroupId,
                      "sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1",
                      undefined,
                      undefined,
                      undefined,
                      "sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2",
                      [],
                      "",
                      undefined,
                      false,
                      "sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle",
                      [oTask.targetObject.name]
                    );
                  } else {
                    // DPID Column not present
                    if (!targetAttr.some((item: any) => item.isDPIDColumn === true)) {
                      oTask.rowHighlight.isError = true;
                      nsValidationStatus.createErrorInstance(
                        oTask,
                        sMessageGroupId,
                        "sourceABAPWithoutKeyNoDPIDColForExistingHANATarget",
                        undefined,
                        undefined,
                        undefined,
                        "sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription",
                        [],
                        "",
                        undefined,
                        false,
                        "sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle",
                        [oTask.targetObject.name]
                      );
                    }
                  }
                }
              }
              //  NON ABAP Targets
              if (
                Utils.isConnectionTypeGBQ(targetConnType) ||
                Utils.isConnTypeObjectStore(targetConnType) ||
                Utils.isConnectionTypeCONFLUENT(targetConnType)
              ) {
                // Primary key not allowed in target
                if (targetAttr.some((item: any) => item.key === true)) {
                  oTask.rowHighlight.isError = true;
                  nsValidationStatus.createErrorInstance(
                    oTask,
                    sMessageGroupId,
                    "sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget",
                    undefined,
                    undefined,
                    undefined,
                    "sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription",
                    [],
                    "",
                    undefined,
                    false,
                    "sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget",
                    [oTask.targetObject.name]
                  );
                } else {
                  // DPID Column present but datatype mismatch
                  if (
                    targetAttr.some(
                      (item: any) =>
                        item.name === Constants.ABAP_DPID_COLUMN.COL_NAME_FOR_NON_ABAP_TARGETS &&
                        (item.datatype !== Constants.ABAP_DPID_COLUMN.DATATYPE_FOR_NON_ABAP_TARGETS ||
                          item.length !== Constants.ABAP_DPID_COLUMN.LENGTH_FOR_NON_ABAP_TARGETS)
                    )
                  ) {
                    oTask.rowHighlight.isError = true;
                    nsValidationStatus.createErrorInstance(
                      oTask,
                      sMessageGroupId,
                      "sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1",
                      undefined,
                      undefined,
                      undefined,
                      "sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1",
                      [],
                      "",
                      undefined,
                      false,
                      "sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle",
                      [oTask.targetObject.name]
                    );
                  } else {
                    // When DPID Column is not present
                    if (!targetAttr.some((item: any) => item.isDPIDColumn === true)) {
                      oTask.rowHighlight.isError = true;
                      nsValidationStatus.createErrorInstance(
                        oTask,
                        sMessageGroupId,
                        "sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1",
                        undefined,
                        undefined,
                        undefined,
                        "sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1",
                        [],
                        "",
                        undefined,
                        false,
                        "sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle",
                        [oTask.targetObject.name]
                      );
                    }
                  }
                }
              }
            }

            // Auto projection
            let renamedAttrList: string[] = [];
            //  Auto projection for GBQ and Confluent will be done in validateConfluentTargetObject && validateGBQTargetObject itself
            if (
              !(
                Utils.isConnectionTypeCONFLUENT(targetConnType) ||
                Utils.isConnectionTypeGBQ(targetConnType) ||
                Utils.isConnectionTypeDwcHANA(targetConnId, targetConnType)
              )
            ) {
              renamedAttrList = RFBuilderUtils.validateABAPAutoprojectionDPID(targetAttr);
            }
            if (renamedAttrList.length > 0) {
              oTask.rowHighlight.isInfo = true;
              const descParam = renamedAttrList.map((name) => `• ${name}`).join("\n");
              nsValidationStatus.createInfoInstance(
                oTask,
                sMessageGroupId,
                "validationAutoRenameTargetDPID",
                undefined,
                undefined,
                undefined,
                "validationAutoRenameTargetDPIDDescription",
                [descParam, "\n", "•"],
                "",
                undefined,
                "validationAutoRenameTargetDPIDSubTitle",
                [oTask.targetObject.name]
              );
            }
          }
        }
      },

      validateDWCTargetObject: function (oTask: sap.cdw.replicationflow.Task) {
        const targetAttr = oTask.targetObject.attributes.toArray();
        const validations = ValidationUtils.getAutoRenameAndAutoDatatypeColumnName(targetAttr);
        // Info for Auto Rename
        if (validations.renamedAttrList.length) {
          const descParam = validations.renamedAttrList.map((name) => `• ${name}`).join("\n");
          nsValidationStatus.createInfoInstance(
            oTask,
            sMessageGroupId,
            "validationAutoRenameTarget",
            undefined,
            undefined,
            undefined,
            "validationAutoRenameTargetDescriptionDWC2",
            [descParam, "\n", "•"],
            "",
            undefined,
            "validationAutoRenameTargetSubTitle",
            [oTask.targetObject.name]
          );
          oTask.rowHighlight.isInfo = true;
        }
        // Info for Auto Datatype
        if (validations.dataTypeConvertedAttrList.length) {
          const descParam = validations.dataTypeConvertedAttrList.map((name) => `• ${name}`).join("\n");
          nsValidationStatus.createInfoInstance(
            oTask,
            sMessageGroupId,
            "validationAutoTargetTypeConversion",
            undefined,
            undefined,
            undefined,
            "validationAutoTargetTypeConversionDescriptionDWC3",
            [descParam, "\n"],
            "",
            undefined,
            "validationAutoRenameTargetSubTitle",
            [oTask.targetObject.name]
          );
          oTask.rowHighlight.isInfo = true;
        }
        // LTF specific conditions
        if (Utils.isConnectionTypeDwcLTF(oTask.targetSystem.connectionId, oTask.targetSystem.connectionType)) {
          this.validateDWCLTFTargetObject(oTask);
        }
      },

      validateDWCLTFTargetObject: function (oTask: sap.cdw.replicationflow.Task) {
        // Info for skipped invalid columns in target with unsupported data types
        // get invalid columns from sourceattr list and also from source object invalid columns
        if (oTask.targetObject.isNew) {
          const targetSkippedColsName = ValidationUtils.getSkippedColumnNamesInTarget(
            oTask.sourceObject.attributes.toArray(),
            oTask.targetSystem.connectionId,
            oTask.targetSystem.connectionType
          );

          const skippedCols = targetSkippedColsName.map((name) => `• ${name}`).join("\n");
          if (skippedCols.length > 0) {
            nsValidationStatus.createInfoInstance(
              oTask,
              sMessageGroupId,
              "TargetColumnSkippedLTF",
              undefined,
              undefined,
              undefined,
              "TargetColumnSkippedLTFDescription",
              ["\n", skippedCols],
              "",
              undefined,
              "TargetColumnSkippedLTFSubtitle",
              [oTask.targetObject.name]
            );
            oTask.rowHighlight.isInfo = true;
          }
          const keysInTargetObject = RFBuilderUtils.keysInObject(oTask.targetObject.attributes.toArray());
          if (keysInTargetObject === 0) {
            const isBlocker = true;
            oTask.rowHighlight.isError = true;
            nsValidationStatus.createErrorInstance(
              oTask,
              sMessageGroupId,
              "validateNoPKInLTFTarget",
              undefined,
              undefined,
              undefined,
              "validateNoPKInLTFTargetDescription",
              [],
              "",
              undefined,
              false,
              "validatePKTimeColumnLTFSubtitle",
              [oTask.targetObject.name],
              isBlocker
            );
          }
        }
      },

      validateAllSourceWithDWCLTFTarget: function (oTask: sap.cdw.replicationflow.Task) {
        // Time column with primary key not possible
        if (oTask.sourceObject.attributes.toArray().find((attr) => attr.datatype === "time" && attr.key === true)) {
          oTask.rowHighlight.isError = true;
          const isBlocker = oTask.targetObject.isNew;
          nsValidationStatus.createErrorInstance(
            oTask,
            sMessageGroupId,
            "validatePKTimeColumnLTF1",
            undefined,
            undefined,
            undefined,
            "validatePKTimeColumnLTFDescription",
            [],
            "",
            undefined,
            false,
            "validatePKTimeColumnLTFSubtitle",
            [oTask.sourceObject.name],
            isBlocker
          );
        }

        // abap -> ltf
        if (Utils.isConnectionTypeABAP(oTask.sourceSystem.connectionType)) {
          if (oTask.sourceObject.datasetProperties.abapTableCategory === Constants.ABAP_CLUSTER_TABLE) {
            oTask.rowHighlight.isError = true;
            const isBlocker = oTask.targetObject.isNew;
            nsValidationStatus.createErrorInstance(
              oTask,
              sMessageGroupId,
              "validateABAPClusterTableLTF",
              undefined,
              undefined,
              undefined,
              "validateABAPClusterTableLTFDescription",
              [],
              "",
              undefined,
              false,
              "validateABAPClusterTableLTFSubtitle",
              [oTask.sourceObject.name],
              isBlocker
            );
          }
        }
      },

      /**
       * Validate confluent source.
       * @param oTask
       */
      validateConfluentSourceObject: function (oTask: sap.cdw.replicationflow.Task) {
        if (!oTask.sourceObject.attributes.length) {
          nsValidationStatus.createErrorInstance(
            oTask,
            sMessageGroupId,
            "validateConfluentEmptySchema",
            undefined,
            undefined,
            undefined,
            "validateConfluentEmptySchemaDescUpdated",
            [],
            "",
            undefined,
            false,
            "validateHDLFNoPKDatasetSubtitle",
            [oTask.sourceObject.name],
            oTask.targetSystem &&
              Utils.isConnectionTypeDWC(oTask.targetSystem.connectionId, oTask.targetSystem.connectionType) &&
              oTask.targetObject.isNew
          );
          oTask.rowHighlight.isError = true;
        }
      },
      /**
       * Validate confluent source.
       * @param oTask
       */
      validateSignavioTargetObject: function (oTask: sap.cdw.replicationflow.Task) {
        const targetAttr = oTask.targetObject.attributes.toArray();
        const validations = ValidationUtils.getAutoRenameAndAutoDatatypeColumnName(targetAttr);
        if (validations.renamedAttrList.length > 0) {
          const descParam = validations.renamedAttrList.map((name) => `• ${name}`).join("\n");
          nsValidationStatus.createInfoInstance(
            oTask,
            sMessageGroupId,
            "validationAutoRenameTarget",
            undefined,
            undefined,
            undefined,
            "validationAutoRenameTargetDescriptionGeneric",
            [descParam, "\n", "•"],
            "",
            undefined,
            "validationAutoRenameTargetSubTitle",
            [oTask.targetObject.name]
          );
          oTask.rowHighlight.isInfo = true;
        }
      },

      globalTaskValidation: function (oTask: sap.cdw.replicationflow.Task) {
        const isValidMappingPresent = this.isValidMappingPresent(oTask);
        if (!isValidMappingPresent) {
          nsValidationStatus.createErrorInstance(
            oTask,
            sMessageGroupId,
            "globalValidateTargetDataType",
            undefined,
            undefined,
            undefined,
            "globalValidateTargetDataTypeDesc",
            [],
            "",
            undefined,
            false,
            "validateTargetDataTypeSubtitle",
            [oTask.targetObject.name]
          );
          oTask.rowHighlight.isError = true;
        }
      },

      isValidMappingPresent: function (oTask: sap.cdw.replicationflow.Task): boolean {
        return ValidationUtils.isCompatibleTypeForTheTargetObject(oTask);
      },

      deltaOnlyLoadTypeValidation: function (oTask: sap.cdw.replicationflow.Task) {
        const notSupportedTargetForDeltaOnlyLoadType =
          oTask.loadType === Constants.LOAD_TYPE.DELTA &&
          (Utils.isConnectionTypeSignavio(oTask.targetSystem.connectionType) ||
            Utils.isConnectionTypeHANA(oTask.targetSystem.connectionId, oTask.targetSystem.connectionType) ||
            Utils.isConnectionTypeDWC(oTask.targetSystem.connectionId, oTask.targetSystem.connectionType) ||
            Utils.isConnTypeMsOneLake(oTask.targetSystem.connectionType) ||
            Utils.isConnTypeSFTP(oTask.targetSystem.connectionType));
        if (notSupportedTargetForDeltaOnlyLoadType) {
          nsValidationStatus.createErrorInstance(
            oTask,
            sMessageGroupId,
            "validationTaskTargetObjectLoadTypeMismatch",
            [oTask.targetObject.name],
            undefined,
            undefined,
            "",
            [],
            "",
            undefined,
            false,
            "",
            [],
            true
          );
          oTask.rowHighlight.isError = true;
        }

        if (
          !notSupportedTargetForDeltaOnlyLoadType &&
          oTask.loadTypeMetadata.isLoadTypeChanged &&
          oTask.loadType === Constants.LOAD_TYPE.DELTA
        ) {
          nsValidationStatus.createWarnInstance(
            oTask,
            sMessageGroupId,
            "deltaOnlyLoadTypeTittle",
            undefined,
            undefined,
            undefined,
            "deltaOnlyLoadTypeDescriptionUpdated",
            undefined,
            undefined,
            undefined,
            "deltaOnlyLoadTypeSubtitle",
            [oTask.targetObject.name]
          );
          oTask.rowHighlight.isWarning = true;
        }
      },
    },
  });
});
