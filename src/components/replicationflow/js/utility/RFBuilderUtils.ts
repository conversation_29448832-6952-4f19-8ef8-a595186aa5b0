/**
 * Copyright 2022 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { CFWContexts, ICFWOptions, ICustomConfig, IProposedEntities, ITransform } from "../../replicationApi";
import Utils from "../utils";
import Constants from "./constants";

export interface IAttributeObj {
  key: boolean;
  name: string;
  datatype: string;
  length: number;
  precision: number;
  scale: number;
  isAutoDatatype: boolean;
  isAutoRenamed: boolean;
}

export interface ISchemaConfigurationPropertyData {
  csvEncoding?: string;
  columnDelimiter?: string;
  isHeaderIncluded?: boolean;
  format?: string;
  confluentReadSchemaSubject?: string;
  confluentReadSchemaVersion?: string;
  includeTechnicalKey?: boolean;
  confluentSchemaType?: string;
  confluentMessageIdColumn?: string;
  includeNotExpandedArraysAndMaps?: boolean;
}

interface IgetDefaultContainerBasedOnConnParams {
  type: string;
  spaceName: string;
  rfTechnicalName: string;
}

export default class RFBuilderUtils {
  private static projectionCntr = 0;
  private static recordNameStartPattern = /^[A-Za-z_]/g; // name can not start with a number
  private static recorNamePattern = /[^A-Za-z0-9_]/g;
  /**
   * Create new GBQ compatible raw attribute list
   * @memberof RFBuilderUtils
   * @param attributeList Array of attributes
   * @returns Compatible attribute Array and if there are any name change then mapping as well
   */
  static createGBQAttributes(
    attributeList: sap.cdw.replicationflow.Attribute[],
    includeCDC: boolean = true
  ): IProposedEntities {
    RFBuilderUtils.projectionCntr += 1;
    const transformObj: ITransform = {
      attributeMappings: [],
      name: `${Constants.AUTO_PROJECTION}${RFBuilderUtils.projectionCntr}`,
      filters: [],
    };
    let attrList: IAttributeObj[] = attributeList.map((attr) => {
      const name = RFBuilderUtils.convertGBQTargetColumnName(attr.name);
      const compatibleType = RFBuilderUtils.convertGBQcompatibleTypes(attr);
      const isAutoRenamed: boolean = name !== attr.name;
      const isAutoDatatype: boolean = compatibleType.isConverted;
      if (isAutoRenamed) {
        transformObj.attributeMappings.push({ expression: `"${attr.name}"`, target: name });
      }
      return {
        key: attr.key,
        name: name,
        datatype: compatibleType.datatype,
        length: attr.length,
        precision: compatibleType.precision,
        scale: compatibleType.scale,
        isAutoDatatype,
        isAutoRenamed,
      };
    });
    if (includeCDC) {
      attrList = attrList.concat(Utils.getCDCAttributes(Constants.CONNECTION_TYPES.GBQ));
    }
    return { attrList, transformObj };
  }

  static getSourceProperties(connectionType: string, sourceDataset): ISchemaConfigurationPropertyData {
    let details: ISchemaConfigurationPropertyData;
    if (Utils.isConnTypeObjectStore(connectionType)) {
      details = {
        columnDelimiter: sourceDataset.colDelimiter,
        csvEncoding: sourceDataset.charset,
        format: sourceDataset.format,
        isHeaderIncluded: sourceDataset.header,
      };
    }
    if (Utils.isConnectionTypeCONFLUENT(connectionType)) {
      details = {
        confluentReadSchemaSubject: sourceDataset.confluentReadSchemaSubject,
        confluentReadSchemaVersion: sourceDataset.confluentReadSchemaVersion,
        confluentSchemaType: sourceDataset.confluentSchemaType,
        confluentMessageIdColumn: sourceDataset.confluentMessageIdColumn,
        includeTechnicalKey: sourceDataset.includeTechnicalKey,
        includeNotExpandedArraysAndMaps: sourceDataset.includeNotExpandedArraysAndMaps,
      };
    }
    return details;
  }
  /**
   * Checks if the column is a DPID Column
   * @param attribute
   * @returns
   */
  static isDPIDColumn(attribute): boolean {
    if (
      attribute.name === Constants.ABAP_DPID_COLUMN.COL_NAME_FOR_HANA_TARGETS &&
      attribute.length >= Constants.ABAP_DPID_COLUMN.LENGTH_FOR_HANA_TARGETS
    ) {
      return true;
    }
    if (
      attribute.name === Constants.ABAP_DPID_COLUMN.COL_NAME_FOR_NON_ABAP_TARGETS &&
      attribute.length === Constants.ABAP_DPID_COLUMN.LENGTH_FOR_NON_ABAP_TARGETS
    ) {
      return true;
    }
    return false;
  }

  /**
   * Creates DPID Columns for source ABAP (CDS/ODP) without keys
   * @param targetConnId
   * @param targetConnType
   * @param attributeList
   * @param transformObj
   * @returns
   */
  static createDPIDColumnForABAPWithoutKey(
    targetConnId: string,
    targetConnType: string,
    attributeList: sap.cdw.replicationflow.Attribute[],
    transformObj: ITransform
  ) {
    if (!(transformObj?.name && transformObj?.attributeMappings.length)) {
      transformObj = {
        attributeMappings: [],
        name: `${Constants.AUTO_PROJECTION}${RFBuilderUtils.projectionCntr}`,
        filters: [],
      };
    }

    // 1. ltf target uses delta table and does not require DPID columns and will not support ABAP source without key scenario
    // 2. kafka target does not require DPID columns
    if (Utils.isConnectionTypeDwcLTF(targetConnId, targetConnType) || Utils.isConnectionTypeKafka(targetConnType)) {
      return { attrList: attributeList, transformObj };
    }

    let attrList: IAttributeObj[] = attributeList.map((attr) => {
      const isAutoRenamedForOtherConn = attr.isAutoRenamed;
      const name = RFBuilderUtils.convertDPIDConflictingTargetColumn(attr.name);
      let isAutoRenamed: boolean = name !== attr.name;
      // Either auto renamed previously or auto renamed in this function
      isAutoRenamed = isAutoRenamed || isAutoRenamedForOtherConn;
      if (isAutoRenamed && !isAutoRenamedForOtherConn) {
        transformObj.attributeMappings.push({ expression: `"${attr.name}"`, target: name });
      }
      return {
        key: attr.key,
        name: name,
        datatype: attr.datatype,
        length: attr.length,
        precision: attr.precision,
        scale: attr.scale,
        isAutoDatatype: false,
        isAutoRenamed,
      };
    });
    attrList = attrList.concat(Utils.getDPIDColumnForABAP(targetConnId, targetConnType));

    return { attrList, transformObj };
  }

  /**
   * Renames column names that contradict with reserved DPID Column name
   * @param name
   * @returns
   */
  static convertDPIDConflictingTargetColumn(name: string): string {
    if (
      [
        Constants.ABAP_DPID_COLUMN.COL_NAME_FOR_HANA_TARGETS,
        Constants.ABAP_DPID_COLUMN.COL_NAME_FOR_NON_ABAP_TARGETS,
      ].includes(name)
    ) {
      name = `${Constants.GBQ_AUTO_PREFIX}${name}`;
    }
    return name;
  }

  /**
   * Checks if target columns have been auto-renamed due to conflicting name with DPID Column
   * @param targetAttr
   * @returns
   */
  static validateABAPAutoprojectionDPID(targetAttr: sap.cdw.replicationflow.Attribute[]): string[] {
    const renamedAttrList: string[] = [];
    targetAttr.forEach((attr) => {
      if (attr.isAutoRenamed) {
        renamedAttrList.push(attr.name);
      }
    });
    return renamedAttrList;
  }

  /**
   * Replaces '/' charecter in column name to '_'
   * Checks for GBQ CDC column name and modifies
   *
   * @private
   * @memberof RFBuilderClass
   * @param name Column name
   * @returns Compatible column name
   */
  static convertGBQTargetColumnName(name: string): string {
    const nameStartPattern = new RegExp(
      `^(${Constants.GBQ_RESERVED_NAMES._COLIDENTIFIER}|${Constants.GBQ_RESERVED_NAMES._FILE_}|${Constants.GBQ_RESERVED_NAMES._PARTITION}|${Constants.GBQ_RESERVED_NAMES._ROW_TIMESTAMP}|${Constants.GBQ_RESERVED_NAMES._TABLE_}|${Constants.GBQ_RESERVED_NAMES.__ROOT__})`,
      "gi"
    );
    if (name) {
      name = name.replace(/\//g, "_");
    }
    if (
      [
        Constants.GBQ_CDC_COLUMNS.operationFlag,
        Constants.GBQ_CDC_COLUMNS.isDeleted,
        Constants.GBQ_CDC_COLUMNS.recordStamp,
      ].includes(name)
    ) {
      name = `${Constants.GBQ_AUTO_PREFIX}${name}`;
    }
    if (nameStartPattern.exec(name)) {
      name = `${Constants.GBQ_AUTO_PREFIX}${name}`;
    }
    return name;
  }

  /**
   * Reads data type compatability object from constant file and converts the result type accordingly
   * @param attr
   * @returns Converted data type
   */
  static convertGBQcompatibleTypes(attr: sap.cdw.replicationflow.Attribute): {
    datatype: string;
    precision: number;
    scale: number;
    isConverted: boolean;
  } {
    const unsupportedCols: string[] = Object.keys(Constants.DATATYPE_CONVERT_RULES.BIGQUERY);
    let convertedType: { datatype: string; precision: number; scale: number; isConverted: boolean };
    if (unsupportedCols.includes(attr.datatype)) {
      const rules = Constants.DATATYPE_CONVERT_RULES.BIGQUERY;
      if (rules && rules[attr.datatype] && rules[attr.datatype].convertTo) {
        convertedType = {
          datatype: rules[attr.datatype].convertTo.templateType,
          precision: rules[attr.datatype].convertTo.precision,
          scale: rules[attr.datatype].convertTo.scale,
          isConverted: true,
        };
      }
    } else {
      convertedType = {
        datatype: attr.datatype,
        precision: attr.precision,
        scale: attr.scale,
        isConverted: false,
      };
    }
    return convertedType;
  }

  static convertConfluentColumnName(name: string): string {
    if (
      [
        Constants.CONFLUENT_CDC_COLUMNS.operation_type,
        Constants.CONFLUENT_CDC_COLUMNS.sequence_number,
        Constants.CONFLUENT_CDC_COLUMNS.timestamp,
      ].includes(name)
    ) {
      return `${Constants.GBQ_AUTO_PREFIX}${name}`;
    }
    return name;
  }

  static createConfluentAttributes(
    attributeList: sap.cdw.replicationflow.Attribute[],
    includeCDC: boolean = true
  ): IProposedEntities {
    RFBuilderUtils.projectionCntr += 1;
    const transformObj: ITransform = {
      attributeMappings: [],
      name: `${Constants.AUTO_PROJECTION}${RFBuilderUtils.projectionCntr}`,
      filters: [],
    };
    let attrList: IAttributeObj[] = attributeList.map((attr) => {
      const name = RFBuilderUtils.convertConfluentColumnName(attr.name);
      const isAutoRenamed: boolean = name !== attr.name;
      if (isAutoRenamed) {
        transformObj.attributeMappings.push({ expression: `"${attr.name}"`, target: name });
      }
      return {
        key: attr.key,
        name: name,
        datatype: attr.datatype,
        length: attr.length,
        precision: attr.precision,
        scale: attr.scale,
        isAutoDatatype: false,
        isAutoRenamed,
      };
    });
    if (includeCDC) {
      attrList = attrList.concat(Utils.getCDCAttributes(Constants.CONNECTION_TYPES.CONFLUENT));
    }
    return { attrList, transformObj };
  }

  static createDWCAttributes(
    attributeList: sap.cdw.replicationflow.Attribute[],
    includeCDC: boolean = true,
    isLTF: boolean = false
  ): IProposedEntities {
    RFBuilderUtils.projectionCntr += 1;
    const transformObj: ITransform = {
      attributeMappings: [],
      name: "",
      filters: [],
    };
    const sourceListNames = attributeList.map((attr) => attr.name);
    const validNames: string[] = [];
    const skippedLTFDataTypeTarget = RFBuilderUtils.appyFeatureFlagForDWCLTFFOrComaptibleDataTypes().SkippedDataTypes;
    const attrListWithoutInvalidCols: sap.cdw.replicationflow.Attribute[] = isLTF
      ? attributeList.filter((attr) => !skippedLTFDataTypeTarget.includes(attr.datatype))
      : attributeList;
    let attrList: IAttributeObj[] = attrListWithoutInvalidCols.map((attr) => {
      // Auto Renamed
      const name = this.getValidUniqueTargetColumnName(attr.name, sourceListNames, validNames);
      validNames.push(name);
      let isAutoRenamed: boolean = name !== attr.name;
      if (isAutoRenamed) {
        transformObj.attributeMappings.push({ expression: `"${attr.name}"`, target: name });
      }
      // Either auto renamed previously or auto renamed in this function
      isAutoRenamed = attr.isAutoRenamed || isAutoRenamed;
      // Auto Datatype conversion
      let isAutoDatatype = false;
      let compatibleType = {
        datatype: attr.datatype,
        precision: attr.precision,
        scale: attr.scale,
        isConverted: false,
      };
      if (isLTF) {
        // Auto Datatype conversion
        compatibleType = RFBuilderUtils.convertDWCHDLFcompatibleTypes(attr);
        isAutoDatatype = compatibleType.isConverted;
      }
      return {
        businessName: attr.businessName ? attr.businessName : name,
        key: attr.key,
        name: name,
        datatype: compatibleType.datatype,
        length: attr.length,
        precision: compatibleType.precision,
        scale: compatibleType.scale,
        isAutoDatatype: isAutoDatatype,
        isAutoRenamed,
      };
    });
    // CDC Columns for DWC Target
    if (includeCDC) {
      attrList = attrList.concat(
        Utils.getCDCAttributes(Constants.getDwcHANAConnection.typeId, Constants.getDwcHANAConnection.name)
      );
    }
    // Auto Projection name
    if (transformObj.attributeMappings.length && transformObj.attributeMappings.length > 0) {
      transformObj.name = `${Constants.AUTO_PROJECTION}${RFBuilderUtils.projectionCntr}`;
    }
    return { attrList, transformObj };
  }

  static validateGBQTargetObjectAttributes(targetAttr: sap.cdw.replicationflow.Attribute[]) {
    const renamedAttrList: string[] = [],
      dataTypeConvrtedAttrList: string[] = [],
      maxCharAttrList: string[] = [];
    let keyColCount = 0,
      isUncompatibleKeyType = false;
    targetAttr.forEach((attr) => {
      if (attr.isAutoRenamed) {
        renamedAttrList.push(attr.name);
      }
      if (attr.isAutoDatatype) {
        dataTypeConvrtedAttrList.push(attr.name);
      }
      if (attr.name?.length > 300) {
        maxCharAttrList.push(attr.name);
      }
      if (attr.key) {
        keyColCount += 1;
      }
      if (
        attr.key &&
        !Constants.GBQ_ALLOWED_PK_TYPES.includes(
          RFBuilderUtils.getGBQTypeFromTemplateType(attr.datatype, attr.precision, attr.scale)
        )
      ) {
        isUncompatibleKeyType = true;
      }
    });
    return {
      renamedAttrList,
      dataTypeConvrtedAttrList,
      maxCharAttrList,
      keyColCount,
      isUncompatibleKeyType,
    };
  }

  static isValidaGBQCDCColmns(columns: any[]): boolean {
    const tempObj = {};
    let isCDCDataset = false;
    columns.forEach((attr) => {
      if (
        [
          Constants.GBQ_CDC_COLUMNS.isDeleted,
          Constants.GBQ_CDC_COLUMNS.operationFlag,
          Constants.GBQ_CDC_COLUMNS.recordStamp,
        ].includes(attr.name)
      ) {
        attr.isCDCColumn = true;
        tempObj[attr.name] = attr.name;
      }
    });
    if (Object.keys(tempObj).length === 3) {
      isCDCDataset = true;
    }

    return isCDCDataset;
  }

  /**
   *
   * @param templateType Traget data type
   * @param precision
   * @param scale
   * @returns GBQ equivalent datatype
   */
  static getGBQTypeFromTemplateType(templateType: string, precisionStr?: string, scaleStr?: string): string {
    if (["decimal", "decfloat16", "decfloat64"].includes(templateType)) {
      const precision = Number(precisionStr),
        scale = Number(scaleStr);
      if (0 <= scale && scale <= 9 && Math.max(1, scale) <= precision && precision <= scale + 29) {
        return Constants.GBQ_TYPE_MAPPING[templateType].numeric;
      } else if (0 <= scale && scale <= 38 && Math.max(1, scale) <= precision && precision <= scale + 38) {
        return Constants.GBQ_TYPE_MAPPING[templateType].bigNumeric;
      } else {
        return "";
      }
    } else {
      return Constants.GBQ_TYPE_MAPPING[templateType];
    }
  }

  static generateConfluentSubjectName(
    subjectStratDataset: string,
    subjectStratSystem: string,
    topicName: string,
    recordName: string
  ): string {
    let subjectName = "",
      subjectStrat = subjectStratDataset ? subjectStratDataset : subjectStratSystem;
    if (!recordName) {
      recordName = RFBuilderUtils.getRecordName(topicName);
    }
    switch (subjectStrat) {
      case Constants.CONFLUENT_SUBJECT_NAME_STRATEGIES_OBJ.TOPIC_KEY:
        subjectName = `${topicName}-value`;
        break;
      case Constants.CONFLUENT_SUBJECT_NAME_STRATEGIES_OBJ.RECORD_KEY:
        subjectName = `${recordName}-value`;
        break;
      case Constants.CONFLUENT_SUBJECT_NAME_STRATEGIES_OBJ.TOPIC_RECORD_KEY:
        subjectName = `${topicName}-${recordName}-value`;
        break;
      default:
        subjectName = "";
        break;
    }
    return subjectName;
  }

  static getRecordName(recordName: string): string {
    if (!recordName) {
      return "";
    }

    if (!/^[A-Za-z_]/g.exec(recordName)) {
      //Name can not start with number
      recordName = `_${recordName}`;
    }
    recordName = recordName.replace(RFBuilderUtils.recorNamePattern, "_");
    return recordName;
  }

  static validateRecordName(recordName: string, isSchemaregSelected, kafkaSerializationType: string): string {
    let status = "";
    if (isSchemaregSelected && recordName === "") {
      status = "empty";
    } else if (
      kafkaSerializationType === "AVRO" &&
      isSchemaregSelected &&
      (!/^[A-Za-z_]/g.exec(recordName) || /[^A-Za-z0-9_]/g.exec(recordName))
    ) {
      // hardcoded regEx because it was passing all the time with private variable !
      status = "invalid";
    }
    return status;
  }

  /**
   * Validates the target objects for Primary keys for HDLF as Source
   * Returns count of Primary key present
   * @param targetAttr
   */
  static validateHDLFTargetObjectAttributes(targetAttr: sap.cdw.replicationflow.Attribute[]) {
    let keyColCount = 0;
    targetAttr.forEach((attr) => {
      if (attr.key) {
        keyColCount += 1;
      }
    });
    return { keyColCount };
  }

  /**
   * Checks for the number of keys in the object
   * @param attributes
   * @returns
   */
  static keysInObject(attributes: sap.cdw.replicationflow.Attribute[]) {
    let keyColCount = 0;
    attributes.forEach((attr) => {
      if (attr.key) {
        keyColCount += 1;
      }
    });
    return keyColCount;
  }

  /**
   *
   * @param maxPartition validating max Partition in object store
   */

  static validateMaxPartition(maxPartition: any) {
    if (
      (maxPartition <= Constants.MAX_MAXPARTITION_VALUE && maxPartition >= Constants.MIN_MAXPARTITION_VALUE) ||
      maxPartition === undefined
    ) {
      return true;
    }
    return false;
  }

  /**
   * Checks if datasets without primary key is supported for the connection type
   * @param connectionType
   * @param isSourceHDLFDeltashare
   * @param sourceContainer
   * @returns boolean
   */
  static isSourceSystemWithoutPKSupported(
    connectionType: string,
    isSourceHDLFDeltashare: boolean,
    sourceContainer: string
  ): boolean {
    if (Utils.isConnectionTypeHdlfWithDeltaShare(connectionType, isSourceHDLFDeltashare)) {
      return true;
    }

    if (Utils.isConnTypeABAPCdsOrOdp(connectionType, sourceContainer)) {
      return true;
    }

    if (Utils.isConnectionTypeCONFLUENT(connectionType)) {
      return true;
    }
    return false;
  }

  static getCustomConfig(
    path: string,
    context: CFWContexts,
    connectionName: string,
    options: ICFWOptions
  ): ICustomConfig {
    if (Utils.isConnectionTypeCONFLUENT(connectionName)) {
      return RFBuilderUtils.getConfluentCustomConfig(path, context, options);
    }
    if (Utils.isConnectionTypeMSSQL(connectionName)) {
      return RFBuilderUtils.getMSSQLCustomConfig(path);
    }
  }

  static getMSSQLCustomConfig(path: string): ICustomConfig {
    const customConfig: ICustomConfig = {
      path: path,
      isCFWCustomConfig: true,
    };
    return customConfig;
  }

  static getConfluentCustomConfig(path: string, context: CFWContexts, options: ICFWOptions): ICustomConfig {
    let customConfig: ICustomConfig,
      cfwPath = "";
    switch (context) {
      case CFWContexts.CONTEXTS:
        if (path == "/") {
          cfwPath = `${CFWContexts.CONTEXTS}`;
        } else {
          cfwPath = `${CFWContexts.CONTEXTS}${path}`;
        }
        break;
      case CFWContexts.TOPICS:
        cfwPath = `${CFWContexts.TOPICS}`;
        break;
      case CFWContexts.TOPICMETADATA:
        cfwPath = path;
        break;
      case CFWContexts.SUBJECTS:
        cfwPath = `${CFWContexts.CONTEXTS}${path}${CFWContexts.SUBJECTS}`;
        break;
      case CFWContexts.TOPICVESRION:
        cfwPath = `${CFWContexts.CONTEXTS}${path}${CFWContexts.SUBJECTS}/${options.subject}${CFWContexts.TOPICVESRION}`;
        break;
      case CFWContexts.VERSIONMETADATA:
        cfwPath = `${CFWContexts.CONTEXTS}${path}${CFWContexts.SUBJECTS}/${options.subject}${CFWContexts.TOPICVESRION}/${options.version}`;
        break;
      default:
        cfwPath = path;
        break;
    }
    customConfig = {
      path: cfwPath,
      isCFWCustomConfig: true,
    };
    if (options) {
      if (options.flattenSchema != undefined) {
        customConfig.flattenSchema = options.flattenSchema;
      }
      if (
        Utils.checkConfluentExtSupportFF() &&
        options.includeTechnicalKey != undefined &&
        options.flattenSchema === true
      ) {
        customConfig.includeTechnicalKey = options.includeTechnicalKey;
      }
      if (!Utils.checkConfluentExtSupportFF() && options.includeTechnicalKey) {
        customConfig.includeTechnicalKey = true;
      }
      if (
        Utils.checkConfluentExtSupportFF() &&
        options.includeNotExpandedArraysAndMaps != undefined &&
        options.flattenSchema === true
      ) {
        customConfig.includeNotExpandedArraysAndMaps = !options.includeNotExpandedArraysAndMaps;
      }
      if (options.container) {
        customConfig.container = options.container;
      }
      if (Utils.checkConfluentExtSupportFF() && options.expandedArray) {
        customConfig.expandedArray = [options.expandedArray];
      }
      if (Utils.checkConfluentExtSupportFF() && options.expandedMap) {
        customConfig.expandedMap = [options.expandedMap];
      }
    }
    return customConfig;
  }

  /**
   * Returns valid name for target column with auto-rename if required
   * @param name
   * @returns
   */
  static getValidTargetColumnName(name: string): string {
    let tempName = name;
    if (tempName) {
      // replace all special characters with '_' followed by
      // replace leading underscores Ex: __abc -> abc , @#$$ -> "" , xyz#$%% -> xyz___
      tempName = tempName.replace(/[^a-zA-Z0-9]/g, "_").replace(/^_+/, "");
    }
    // If the name is empty after sanitization, set a default name
    if (tempName === "") {
      tempName = Constants.COLUMN_NAME_AUTO_FIX;
    }
    return tempName;
  }

  static getValidUniqueTargetColumnName(attrName: string, sourceListNames: string[], validNames: string[]): string {
    let finalUniqueName = attrName;
    let tempRenamedName = RFBuilderUtils.getValidTargetColumnName(attrName);
    let suffixCounter = 0;

    if (tempRenamedName !== attrName) {
      const tempNameWithoutSuffix = tempRenamedName;
      while (validNames.includes(tempRenamedName) || sourceListNames.includes(tempRenamedName)) {
        tempRenamedName = `${tempNameWithoutSuffix}_${suffixCounter}`;
        suffixCounter += 1;
      }
      finalUniqueName = tempRenamedName;
    }
    return finalUniqueName;
  }

  /**
   * Checks if the target has duplicate column names and returns if it finds one duplicate
   * @param targetAttr
   * @returns
   */
  static hasDuplicateColumns(targetAttr: sap.cdw.replicationflow.Attribute[]): boolean {
    const uniqueNames: Set<string> = new Set();
    for (const attr of targetAttr) {
      if (uniqueNames.has(attr.name)) {
        return true;
      }
      uniqueNames.add(attr.name);
    }
    return false;
  }
  /**
   * Converts the data type to DWC HDLF compatible data type
   * @param attr
   * @returns
   */
  static convertDWCHDLFcompatibleTypes(attr: sap.cdw.replicationflow.Attribute): {
    datatype: string;
    precision: number;
    scale: number;
    isConverted: boolean;
  } {
    const dataTypeConvertRuleLTF = RFBuilderUtils.appyFeatureFlagForDWCLTFFOrComaptibleDataTypes().DataTypeConvertRule;
    const unsupportedCols: string[] = Object.keys(dataTypeConvertRuleLTF.LTF);
    let convertedType: { datatype: string; precision: number; scale: number; isConverted: boolean };
    if (unsupportedCols.includes(attr.datatype)) {
      const rules = dataTypeConvertRuleLTF.LTF;
      if (rules && rules[attr.datatype] && rules[attr.datatype].convertTo) {
        convertedType = {
          datatype: rules[attr.datatype].convertTo.templateType,
          precision: rules[attr.datatype].convertTo.precision,
          scale: rules[attr.datatype].convertTo.scale,
          isConverted: true,
        };
      }
    } else {
      convertedType = {
        datatype: attr.datatype,
        precision: attr.precision,
        scale: attr.scale,
        isConverted: false,
      };
    }
    return convertedType;
  }

  /**
   * Gets the deault container based on the connection while creating systems objects
   * @param oSelectedConnection
   * @param params
   * @returns
   */
  static getDefaultContainerBasedOnConn(oSelectedConnection, params: IgetDefaultContainerBasedOnConnParams): string {
    if (params.type === "source") {
      if (Utils.isConnectionTypeDWC(oSelectedConnection.name, oSelectedConnection.typeId)) {
        return "/" + params.spaceName; // SpaceName
      }
    } else if (params.type === "target") {
      if (Utils.isConnectionTypeDWC(oSelectedConnection.name, oSelectedConnection.typeId)) {
        return "/" + params.spaceName; // SpaceName
      }
      if (
        Utils.isConnectionTypeKafka(oSelectedConnection.typeId) ||
        Utils.isConnectionTypeCONFLUENT(oSelectedConnection.typeId)
      ) {
        return "/"; // make target container as "/"
      }
      if (Utils.isConnectionTypeSignavio(oSelectedConnection.typeId)) {
        return "/";
      }
    }
    return "";
  }

  static createSignavioAttributes(
    attributeList: sap.cdw.replicationflow.Attribute[],
    includeCDC: boolean = true
  ): IProposedEntities {
    RFBuilderUtils.projectionCntr += 1;
    const transformObj: ITransform = {
      attributeMappings: [],
      name: `${Constants.AUTO_PROJECTION}${RFBuilderUtils.projectionCntr}`,
      filters: [],
    };
    let attrList: IAttributeObj[] = attributeList.map((attr) => {
      const name = RFBuilderUtils.convertSignavioColumnName(attr.name);
      const isAutoRenamed: boolean = name !== attr.name;
      if (isAutoRenamed) {
        transformObj.attributeMappings.push({ expression: `"${attr.name}"`, target: name });
      }
      return {
        key: attr.key,
        name: name,
        datatype: attr.datatype,
        length: attr.length,
        precision: attr.precision,
        scale: attr.scale,
        isAutoDatatype: false,
        isAutoRenamed,
      };
    });
    if (includeCDC) {
      attrList = attrList.concat(Utils.getCDCAttributes(Constants.CONNECTION_TYPES.SIGNAVIO));
    }
    return { attrList, transformObj };
  }

  static createSFTPAttributes(
    attributeList: sap.cdw.replicationflow.Attribute[],
    includeCDC: boolean = true
  ): IProposedEntities {
    RFBuilderUtils.projectionCntr += 1;
    const transformObj: ITransform = {
      attributeMappings: [],
      name: `${Constants.AUTO_PROJECTION}${RFBuilderUtils.projectionCntr}`,
      filters: [],
    };

    let attrList: IAttributeObj[] = attributeList.map((attr) => {
      const name = RFBuilderUtils.convertSFTPColumnName(attr.name);
      const isAutoRenamed: boolean = name !== attr.name;
      if (isAutoRenamed) {
        transformObj.attributeMappings.push({ expression: `"${attr.name}"`, target: name });
      }
      return {
        key: attr.key,
        name: name,
        datatype: attr.datatype,
        length: attr.length,
        precision: attr.precision,
        scale: attr.scale,
        isAutoDatatype: false,
        isAutoRenamed,
      };
    });
    if (includeCDC) {
      attrList = attrList.concat(Utils.getCDCAttributes(Constants.CONNECTION_TYPES.SFTP));
    }
    return { attrList, transformObj };
  }
  static convertSFTPColumnName(name: string): string {
    if (
      [
        Constants.SFTP_CDC_COLUMNS.operation_type,
        Constants.SFTP_CDC_COLUMNS.sequence_number,
        Constants.SFTP_CDC_COLUMNS.timestamp,
      ].includes(name)
    ) {
      return `${Constants.GBQ_AUTO_PREFIX}${name}`;
    }
    return name;
  }

  static createMsOneLakeAttributes(
    attributeList: sap.cdw.replicationflow.Attribute[],
    includeCDC: boolean = true
  ): IProposedEntities {
    RFBuilderUtils.projectionCntr += 1;
    const transformObj: ITransform = {
      attributeMappings: [],
      name: `${Constants.AUTO_PROJECTION}${RFBuilderUtils.projectionCntr}`,
      filters: [],
    };

    let attrList: IAttributeObj[] = attributeList.map((attr) => {
      const name = RFBuilderUtils.convertMsOneLakeColumnName(attr.name);
      const isAutoRenamed: boolean = name !== attr.name;
      if (isAutoRenamed) {
        transformObj.attributeMappings.push({ expression: `"${attr.name}"`, target: name });
      }
      return {
        key: attr.key,
        name: name,
        datatype: attr.datatype,
        length: attr.length,
        precision: attr.precision,
        scale: attr.scale,
        isAutoDatatype: false,
        isAutoRenamed,
      };
    });
    if (includeCDC) {
      attrList = attrList.concat(Utils.getCDCAttributes(Constants.CONNECTION_TYPES.MSONELAKE));
    }
    return { attrList, transformObj };
  }
  static convertMsOneLakeColumnName(name: string): string {
    if (
      [
        Constants.MSONELAKE_CDC_COLUMNS.operation_type,
        Constants.MSONELAKE_CDC_COLUMNS.sequence_number,
        Constants.MSONELAKE_CDC_COLUMNS.timestamp,
      ].includes(name)
    ) {
      return `${Constants.GBQ_AUTO_PREFIX}${name}`;
    }
    return name;
  }

  static convertSignavioColumnName(name: string): string {
    if (
      [
        Constants.SIGNAVIO_CDC_COLUMNS.operationType,
        Constants.SIGNAVIO_CDC_COLUMNS.sequenceNumber,
        Constants.SIGNAVIO_CDC_COLUMNS.timestamp,
      ].includes(name)
    ) {
      return `${Constants.GBQ_AUTO_PREFIX}${name}`;
    }
    return name;
  }

  static appyFeatureFlagForDWCLTFFOrComaptibleDataTypes() {
    const oFeatures = (sap.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel).getData();
    // LTF_DATATYPE_CONVERT_RULE
    const DataTypeConvertRule = Constants.DATATYPE_CONVERT_RULES;
    // LTF_SKIPPED_DATA_TYPE_IN_TARGET
    const SkippedDataTypes = [...Constants.LTF_SKIPPED_DATA_TYPES_IN_TARGET];
    if (oFeatures.DWCO_LOCAL_TABLE_FILES_HANAQRC4_2024) {
      // REMOVING "uint8" DTYPE FROM DATATYPE_CONVERT_RULES
      delete DataTypeConvertRule.LTF?.uint8;
      // REMOVING "uint8" DTYPE FROM LTF_SKIPPED_DATA_TYPE_IN_TARGET
      const index = SkippedDataTypes.indexOf("uint8");
      if (index > -1) {
        SkippedDataTypes.splice(index, 1);
      }
    }
    return { DataTypeConvertRule, SkippedDataTypes };
  }
}
