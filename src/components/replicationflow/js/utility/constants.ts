/**
 * Copyright 2022 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { RepositoryObjectType } from "@sap/deepsea-types";
export default class Constants {
  static DWC_HANA_CONNECTION_ID = "$DWC";
  static DWC_HANA_BUSINESS_NAME = "SAP Datasphere";
  static DWC_LTF_CONNECTION_ID = "DWC_HDLF";
  static DWC_LTF_BUSINESS_NAME = "SAP Datasphere";
  static DWC_STORAGE_TYPE_FILE = "File";

  static ABAP_CLUSTER_TABLE = "CLUSTER";
  static ODP_CONTAINER = "/ODP";
  static SLT_CONTAINER = "/SLT";
  static CDS_CONTAINER = "/CDS";
  static CDS_EXTRACTION = "/CDS_EXTRACTION";
  static SQL_SERVICE = "/SQL_SERVICE";
  static DIS_REMOTE_TYPE = "remotedis";
  static RF_EDITOR_CAPABILITY = "DisDataflow";
  static BETWEEN_COMPARISON = "BETWEEN";
  static NOT_BETWEEN_COMPARISON = "NOT BETWEEN";
  static USAGE_REPLICATIONFLOW_EDITOR = "REPLICATIONFLOW_EDITOR";
  static IS_RF_REMOTE_SOURCES = "RFRemoteSource";
  static USE_CFW = "useCFW";
  static MESSAGE_ID = "__message_id";

  static DELTA_INTERVAL_DEFAULT = 3600;
  static DEFAULT_CONNECTION_THREAD_VALUE = 10;
  static MIN_CONNECTION_THREAD_VALUE = 1;
  static MAX_CONNECTION_THREAD_VALUE = 100;
  static MIN_MAXPARTITION_VALUE = 1;
  static MAX_MAXPARTITION_VALUE = 2147483647;
  static MIN_PARTITION_VALUE = 1;
  static MAX_PARTITION_VALUE = 10;
  static DEFAULT_DELTA_PARTITION_VALUE = 1;
  static NEW_TARGET_OBJECT_PREFIX = "NewTarget_";
  static MAX_KAFKA_PARTITIONS = 200000;
  static MAX_KAFKA_REPLICATION_FACTOR = 32767;
  static SKIP_MAPPING = "Skip Mapping";
  static USAGE_ACTIONS = {
    EXECUTE_REPLICATIONFLOW: "executeReplicationflow",
  };

  static FILTER_FILE_TYPES_OBJ_STORE = "filterObjStoreFileTypes";
  static TAB_KEYS = {
    TRANSFORMATION: "transformation",
    SETTINGS: "settings",
  };

  static LOAD_TYPE = {
    INITIAL: "INITIAL",
    INITIALDELTA: "REPLICATE",
    DELTA: "DELTA",
  };

  static SOURCE_OBJECT_TYPE = {
    TABLE: "TABLE",
    VIEW: "VIEW",
  };

  static OBJ_STORE_SOURCE_STRING = [
    "date",
    "bool",
    "timetsamp",
    "time",
    "decfloat16",
    "decfloat34",
    "int16",
    "int8",
    "uint8",
    "uint16",
    "uint32",
    "int32",
    "int64",
    "float32",
    "float64",
  ];

  static DWC_CDC_METADATA = {
    MODE_COLUMN_NAME: "Change_Type",
    TIMESTAMP_COLUMN_NAME: "Change_Date",
    MODE_COLUMN_BUSINESS_NAME: "Change Type",
    TIMESTAMP_COLUMN_BUSINESS_NAME: "Change Date",
  };

  static DWC_DELTA_TABLE = {};

  static SUPPORTED_TECHNICAL_TYPES_SOURCE: RepositoryObjectType[] = [RepositoryObjectType.DWC_LOCAL_TABLE];

  static SUPPORTED_TECHNICAL_TYPES_TARGET: RepositoryObjectType[] = [RepositoryObjectType.DWC_LOCAL_TABLE];

  static VTypeMapping = {
    binary: "cds.Binary",
    bool: "cds.Boolean",
    date: "cds.Date",
    decfloat16: "cds.hana.SMALLDECIMAL",
    decfloat34: "cds.DecimalFloat",
    decimal: "cds.Decimal",
    float32: "cds.hana.REAL",
    float64: "cds.Double",
    geometry: "cds.hana.ST_GEOMETRY",
    int16: "cds.hana.SMALLINT",
    int32: "cds.Integer",
    int64: "cds.Integer64",
    int8: "cds.hana.SMALLINT", //check
    string: "cds.String",
    time: "cds.Time",
    timestamp: "cds.Timestamp",
    uint64: "cds.Integer", //check
    uint8: "cds.hana.TINYINT",
  };

  static CdsTypeMapping = {
    "cds.Boolean": "bool",
    "cds.String": "string",
    "cds.LargeString": "string",
    "cds.Date": "date",
    "cds.Time": "time",
    "cds.DateTime": "timestamp",
    "cds.Timestamp": "timestamp",
    "cds.Integer": "int32",
    "cds.Integer64": "int64",
    "cds.Decimal": "decimal",
    "cds.DecimalFloat": "decfloat34",
    "cds.Double": "float64",
    "cds.Binary": "binary",
    "cds.LargeBinary": "binary",
    "cds.UUID": "string",
    "cds.Association": "", // Ignore it
    "cds.Composition": "", // Ignore it
    "cds.hana.SMALLINT": "int16",
    "cds.hana.TINYINT": "int8",
    "cds.hana.SMALLDECIMAL": "float32",
    "cds.hana.REAL": "float32",
    "cds.hana.NCHAR": "string",
    "cds.hana.BINARY": "binary",
  };

  // fix DW101-38273, overrides from rms wiki document
  static rmsCdsTypeMapping = Object.assign({}, Constants.CdsTypeMapping, {
    "cds.hana.TINYINT": "uint8",
    "cds.hana.SMALLDECIMAL": "decfloat16",
  });

  // @todo verify values with real connections
  static CONNECTION_TYPES = {
    SAPS4HANAOP: "SAPS4HANAOP",
    SAPS4HANACLOUD: "SAPS4HANACLOUD",
    ABAP: "ABAP",
    CDI: "CDI", // ?
    HANA: "HANA",
    AZURESQL: "AZURESQL",
    S3: "S3",
    GCS: "GCS",
    ADL: "ADL",
    ADL_GEN1: "ADL_GEN1", // ?
    ADL_GEN2: "ADL_GEN2", // ?
    HDL: "HDL",
    HDL_FILES: "HDL_FILES",
    KAFKA: "KAFKA",
    GBQ: "BIGQUERY",
    CONFLUENT: "CONFLUENT",
    SIGNAVIO: "SIGNAVIO",
    MSSQL: "MSSQL",
    MSONELAKE: "ONELAKE",
    SFTP: "SFTP",
  };

  static CONNECTION_SEGGREGATION = {
    ABAP: [
      Constants.CONNECTION_TYPES.SAPS4HANAOP,
      Constants.CONNECTION_TYPES.SAPS4HANACLOUD,
      Constants.CONNECTION_TYPES.ABAP,
    ],
    DB: [Constants.CONNECTION_TYPES.HANA, Constants.CONNECTION_TYPES.AZURESQL],
    OBJECTSTORES: [
      Constants.CONNECTION_TYPES.S3,
      Constants.CONNECTION_TYPES.GCS,
      Constants.CONNECTION_TYPES.ADL,
      Constants.CONNECTION_TYPES.ADL_GEN1,
      Constants.CONNECTION_TYPES.ADL_GEN2,
      Constants.CONNECTION_TYPES.HDL,
      Constants.CONNECTION_TYPES.HDL_FILES,
      Constants.CONNECTION_TYPES.MSONELAKE,
      Constants.CONNECTION_TYPES.SFTP,
    ],
    OBJECTSTORESSOURCE: [
      Constants.CONNECTION_TYPES.S3,
      Constants.CONNECTION_TYPES.GCS,
      Constants.CONNECTION_TYPES.ADL_GEN2,
      Constants.CONNECTION_TYPES.HDL,
      Constants.CONNECTION_TYPES.HDL_FILES,
    ],
    STREAM: [Constants.CONNECTION_TYPES.KAFKA, Constants.CONNECTION_TYPES.CONFLUENT],
  };

  static ABAP_CONTENT_TYPE = {
    nativeType: "Native Type",
    templateType: "Template Type",
  };

  static TEMPLATE_TYPES = [
    { templateType: "string", datatype: "STRING" },
    { templateType: "int8", datatype: "INTEGER", compatible: ["bool"] },
    { templateType: "uint8", datatype: "INTEGER", compatible: ["bool"] },
    { templateType: "int16", datatype: "INTEGER", compatible: ["int8", "uint8", "bool"] },
    { templateType: "int32", datatype: "INTEGER", compatible: ["int8", "uint8", "int16", "bool"] },
    { templateType: "int64", datatype: "INTEGER", compatible: ["int8", "uint8", "int16", "int32", "bool"] },
    { templateType: "uint64", datatype: "INTEGER", compatible: ["uint8", "bool"] },
    { templateType: "decfloat16", datatype: "DECIMAL" },
    { templateType: "decfloat34", datatype: "DECIMAL", compatible: ["decfloat16"] },
    { templateType: "float32", datatype: "FLOATING" },
    { templateType: "float64", datatype: "FLOATING", compatible: ["float32"] },
    { templateType: "decimal", datatype: "DECIMAL", compatible: ["uint64"] },
    { templateType: "date", datatype: "DATE" },
    { templateType: "time", datatype: "TIME" },
    { templateType: "timestamp", datatype: "DATETIME" },
    { templateType: "bool", datatype: "BOOLEAN" },
    { templateType: "binary", datatype: "BINARY" },
  ];

  static TEMPLATE_TYPES_OBJECT_STORE = [
    { templateType: "bool", datatype: "BOOLEAN", compatible: ["string", "bool"] },
    {
      templateType: "int8",
      datatype: "INTEGER",
      compatible: ["int8", "int16", "int32", "int64", "uint8", "uint64", "string"],
    },
    {
      templateType: "uint8",
      datatype: "INTEGER",
      compatible: ["int8", "uint8", "int16", "int32", "int64", "uint64", "string"],
    },
    {
      templateType: "int16",
      datatype: "INTEGER",
      compatible: ["int8", "int16", "uint8", "int32", "int64", "uint64", "string"],
    },
    {
      templateType: "int32",
      datatype: "INTEGER",
      compatible: ["int8", "int32", "uint8", "int16", "int64", "uint64", "string"],
    },
    {
      templateType: "int64",
      datatype: "INTEGER",
      compatible: ["int8", "uint8", "int64", "int16", "int32", "uint64", "string"],
    },
    {
      templateType: "uint64",
      datatype: "INTEGER",
      compatible: ["int8", "uint64", "uint8", "int16", "int32", "int64", "string"],
    },
    {
      templateType: "float32",
      datatype: "FLOATING",
      compatible: ["float64", "decfloat16", "decfloat34", "decimal", "string", "float32"],
    },
    {
      templateType: "float64",
      datatype: "FLOATING",
      compatible: ["float32", "decfloat16", "decfloat34", "decimal", "string", "float64"],
    },
    {
      templateType: "decimal",
      datatype: "DECIMAL",
      compatible: ["float32", "float64", "decfloat16", "decfloat34", "decimal"],
    },
    { templateType: "date", datatype: "DATE", compatible: ["string", "date"] },
    { templateType: "time", datatype: "TIME", compatible: ["string", "time"] },
    { templateType: "timestamp", datatype: "DATETIME", compatible: ["string", "timestamp"] },
    { templateType: "binary", datatype: "BINARY", compatible: ["string", "binary"] },
    {
      templateType: "string",
      datatype: "STRING",
      compatible: [
        "string",
        "bool",
        "binary",
        "float32",
        "float64",
        "decfloat16",
        "decfloat34",
        "decimal",
        "uint8",
        "uint64",
        "int8",
        "int16",
        "int32",
        "int64",
        "date",
        "timestamp",
        "time",
      ],
    },
  ];

  static DATATYPE_CONVERT_RULES = {
    BIGQUERY: {
      decfloat16: {
        convertTo: {
          templateType: "decimal",
          precision: 38,
          scale: 9,
        },
      },
      decfloat34: {
        convertTo: {
          templateType: "decimal",
          precision: 38,
          scale: 9,
        },
      },
      uint64: {
        convertTo: {
          templateType: "decimal",
          precision: 20,
          scale: 0,
        },
      },
    },
    LTF: {
      decfloat16: {
        convertTo: {
          templateType: "decimal",
          precision: 38,
          scale: 6,
        },
      },
      decfloat34: {
        convertTo: {
          templateType: "decimal",
          precision: 38,
          scale: 6,
        },
      },
      uint64: {
        convertTo: {
          templateType: "decimal",
          precision: 20,
          scale: 0,
        },
      },
      // Time will not be converted
      time: {
        convertTo: {
          templateType: undefined,
          precision: undefined,
          scale: undefined,
        },
      },
      uint8: {
        convertTo: {
          templateType: undefined,
          precision: undefined,
          scale: undefined,
        },
      },
    },
  };

  // Dummy Datasphere HANA Connection
  static getDwcHANAConnection = {
    id: "AAE7FB71307B9BD91790C08EBA5B0758",
    shared: true,
    capabilityPartnerSchema: "false",
    businessName: Constants.DWC_HANA_BUSINESS_NAME,
    technicalName: Constants.DWC_HANA_CONNECTION_ID, // Add for Bugfix: DW101-24988
    name: Constants.DWC_HANA_CONNECTION_ID,
    typeId: Constants.CONNECTION_TYPES.HANA,
    description: "Local Repository",
    connectionMetaschema: {
      sources: ["SAP"],
    },
  };

  // Dummy Datasphere LTF Connection
  static getDwcLTFConnection = {
    id: "AAE7FB71307B9BD91790C08EBA5B0759",
    shared: true,
    capabilityPartnerSchema: "false",
    businessName: Constants.DWC_LTF_BUSINESS_NAME,
    technicalName: Constants.DWC_LTF_CONNECTION_ID,
    name: Constants.DWC_LTF_CONNECTION_ID,
    typeId: Constants.CONNECTION_TYPES.HDL_FILES,
    description: "Local Table (File)",
    connectionMetaschema: {
      sources: ["SAP"],
    },
  };

  // @todo - Remove this (just for dev simplicity)
  // static getKafkaConnection = {
  //   id: "AAE7FB71307B9BD91790C08E58",
  //   shared: true,
  //   capabilityPartnerSchema: "false",
  //   businessName: "Kafka",
  //   name: "My_kafka",
  //   typeId: Constants.CONNECTION_TYPES.KAFKA
  // };

  static KAKFA_DEFAULT_SETTING = {
    kafkaNumPartitions: "1",
    kafkaReplicationFactor: "1",
    kafkaSerializationType: "JSON",
    kafkaCompressionFormat: "NONE",
  };

  static OBJECTSTORES_DEFAULT_SETTING = {
    groupDeltaFilesBy: "NONE",
    format: "PARQUET",
    compression: "NONE",
    "objectstore.write.useDuplicateSuppressionInitialLoad": "true",
    "objectstore.write.parquet.compatibilityMode": "NONE",
  };

  static LTF_DEFAULT_SETTING = {
    groupDeltaFilesBy: "NONE",
    format: "PARQUET",
    compression: "SNAPPY",
    "objectstore.write.useDuplicateSuppressionInitialLoad": "false",
    "objectstore.write.parquet.compatibilityMode": "SPARK",
    "com.sap.rms.write.forceDecfloatsToTargetValueRange": "false",
  };

  static OBJECTSTORES_SOURCE_DEFAULT_SETTING = {
    clampingData: "true",
    failOnIncompatible: "true",
    maxPartition: undefined,
    includeSubfolder: "true",
    fileGlobalPattern: "",
    isMaxPartitionEnabled: false,
  };

  static CONFLUENT_SOURCE_DEFAULT_SETTINGS = {
    confluentConsumeOtherSchemaVersions: "false",
    confluentFailTruncate: "fail",
    confluentOffsetMode: "from-earliest",
  };
  static OBJECT_STORE_FILE_FORMAT = {
    PARQUET: "PARQUET",
  };

  static OBJECT_STORE_APACHE_SPARK_MODE = {
    TRUE: "SPARK",
    FALSE: "NONE",
  };

  static OBJECT_STORE_DUPLICATE_SUPPRESSION = {
    TRUE: "true",
    FALSE: "false",
  };

  static GBQ_DEFAULT_SETTING = {
    "com.sap.rms.write.forceDecfloatsToTargetValueRange": "true",
  };

  static TS_DIALOG_ERROR_CODES = {
    targetMandatory: "TARGET_MANDATORY",
    targetBusinessNameMandatory: "TARGET_BUSINESS_NAME_MANDATORY",
    targetBusinessNameDuplicate: "TARGET_BUSINESS_NAME_DUPLICATE",
    tergetInvalidBusinessName: "INVALID_TARGET_BUSINESS_NAME",
    targetDuplicate: "TARGET_DUPLICATE",
    tergetInvalidName: "INVALID_TARGET",
    targetInvalidChar: "INVALID_TARGET_CHAR",
    targetInvalidLength: "INVALID_TARGET_NAME_LENGTH",
    mappingMandatory: "MAPPING_MANDATORY",
    expression: "EXPRESSION",
    length: "LENGTH",
    precision: "PRECISION",
    scale: "SCALE",
    dataType: "DATA_TYPE",
    colCount: "COLUMN_COUNT",
    transformName: "TRANSFORM_NAME",
    autoRename: "AUTO_RENAME",
    autoDatatype: "AUTO_DATATYPE",
    primaryKey: "PRIMARY_KEY",
    primaryKeyWarning: "PRIMARY_KEY_WARNING",
    scaleAndPrecision: "SCALE_AND_PRECISION",
    duplicateSourceMapping: "DUPLICATE_SRC_MAPPING",
  };
  static EQUALTO_COMPARISON = "=";
  static NOTEQUALTO_COMPARISON = "!=";

  static COMPARISION_OPERATORS = [
    {
      key: this.EQUALTO_COMPARISON,
      text: "equal to",
    },
    {
      key: "<",
      text: "less than",
      notSupported: ["string", "bool", "binary"],
    },
    {
      key: "<=",
      text: "less than or equal to",
      notSupported: ["string", "bool", "binary"],
    },
    {
      key: ">",
      text: "greater than",
      notSupported: ["string", "bool", "binary"],
    },
    {
      key: ">=",
      text: "greater than or equal to",
      notSupported: ["string", "bool", "binary"],
    },
    {
      key: Constants.BETWEEN_COMPARISON,
      text: "between",
      notSupported: ["string", "bool", "binary"],
    },
    {
      key: this.NOTEQUALTO_COMPARISON,
      text: "not equal to",
    },
    {
      key: Constants.NOT_BETWEEN_COMPARISON,
      text: "not between",
      notSupported: ["string", "bool", "binary"],
    },
  ];

  // Check with Abhishek on binary datatype
  static DATATYPES_MAIN_CATEGORIES = {
    DATE: "date",
    DATE_TIME: "dateTime",
    TIME_STAMP: "timestamp",
    TIME: "time",
  };

  static INPUT_TYPE(datatype): sap.m.InputType {
    let inputType = sap.m.InputType.Text;
    if (datatype) {
      switch (datatype.toUpperCase()) {
        case "INTEGER":
        case "INT64":
        case "INT32":
        case "INT16":
        case "INT8":
        case "UINT64":
        case "UINT8":
        case "FLOAT64":
        case "FLOAT32":
        case "FLOAT16":
        case "DECFLOAT34":
        case "DECFLOAT16":
        case "DOUBLE":
        case "FLOAT":
        case "DECIMAL":
        case "DECIMALFLOAT":
          inputType = sap.m.InputType.Number;
          break;
        case "DATE":
          inputType = sap.m.InputType.Date;
          break;
        case "TIMESTAMP":
        case "DATETIME":
          inputType = sap.m.InputType.Datetime;
          break;
        case "TIME":
          inputType = sap.m.InputType.Time;
          break;
        default:
          inputType = sap.m.InputType.Text;
          break;
      }
    }
    return inputType;
  }

  static DATE_TIME_EXP_FUNCTIONS = {
    TIME: { dataType: "time", method: "CURRENT_UTCTIME" },
    DATE: { dataType: "date", method: "CURRENT_UTCDATE" },
    TIMESTAMP: { dataType: "timestamp", method: "CURRENT_UTCTIMESTAMP" },
  };

  static IS_NUMBER_DATA_TYPE(dataType) {
    const type = Constants.TEMPLATE_TYPES.find((x) => x.templateType === dataType);
    const ttype = type ? type.datatype : "";
    if (ttype) {
      return ["INTEGER", "DECIMAL", "FLOATING"].includes(ttype);
    }
    return false;
  }

  static transformationTableHeight = 653;

  static ABAP_DPID_COLUMN = {
    COL_NAME_FOR_HANA_TARGETS: "__load_package_id",
    DATATYPE_FOR_HANA_TARGETS: "binary",
    LENGTH_FOR_HANA_TARGETS: 256,
    COL_NAME_FOR_NON_ABAP_TARGETS: "__load_record_id",
    DATATYPE_FOR_NON_ABAP_TARGETS: "string",
    LENGTH_FOR_NON_ABAP_TARGETS: 44,
  };
  static GBQ_CDC_COLUMNS = {
    operationFlag: "operation_flag",
    isDeleted: "is_deleted",
    recordStamp: "recordstamp",
  };

  static CONFLUENT_CDC_COLUMNS = {
    operation_type: "__operation_type",
    sequence_number: "__sequence_number",
    timestamp: "__timestamp",
  };

  static MSONELAKE_CDC_COLUMNS = {
    operation_type: "__operation_type",
    sequence_number: "__sequence_number",
    timestamp: "__timestamp",
  };

  static SFTP_CDC_COLUMNS = {
    operation_type: "__operation_type",
    sequence_number: "__sequence_number",
    timestamp: "__timestamp",
  };

  static SIGNAVIO_CDC_COLUMNS = {
    operationType: "__operation_type",
    sequenceNumber: "__sequence_number",
    timestamp: "__timestamp",
  };

  static GBQ_AUTO_PREFIX = "AUTOPREFIX_";
  static COLUMN_NAME_AUTO_FIX = "AUTOFIX";
  static GBQ_RESERVED_NAMES = {
    _TABLE_: "_TABLE_",
    _FILE_: "_FILE_",
    _PARTITION: "_PARTITION",
    _ROW_TIMESTAMP: "_ROW_TIMESTAMP",
    __ROOT__: "__ROOT__",
    _COLIDENTIFIER: "_COLIDENTIFIER",
  };

  static AUTO_PROJECTION = "Auto_projection_";

  static GBQ_TYPE_MAPPING = {
    bool: "BOOL",
    string: "STRING",
    date: "DATE",
    time: "TIME",
    timestamp: "TIMESTAMP",
    int8: "INT64",
    int16: "INT64",
    int32: "INT64",
    int64: "INT64",
    uint8: "INT64",
    uint64: "NUMERIC",
    decimal: { numeric: "NUMERIC", bigNumeric: "BIGNUMERIC" },
    decfloat16: { numeric: "NUMERIC", bigNumeric: "BIGNUMERIC" },
    decfloat34: { numeric: "NUMERIC", bigNumeric: "BIGNUMERIC" },
    float32: "FLOAT64",
    float64: "FLOAT64",
    binary: "BYTES",
  };

  static GBQ_ALLOWED_PK_TYPES = ["BIGNUMERIC", "BOOL", "DATE", "INT64", "NUMERIC", "STRING", "TIMESTAMP"];

  static DWC_DELTACAPTURE_TABLENAME_POSTFIX = "_Delta";

  static CONFLUENT_SUBJECT_NAME_STRATEGIES_OBJ = {
    TOPIC_TEXT: "Topic",
    TOPIC_KEY: "TopicNameStrategy",
    RECORD_TEXT: "Record",
    RECORD_KEY: "RecordNameStrategy",
    TOPIC_RECORD_TEXT: "Topic-Record",
    TOPIC_RECORD_KEY: "TopicRecordNameStrategy",
  };

  static CONFLUENT_DEFAULT_SETTINGS = {
    kafkaNumPartitions: "1",
    kafkaReplicationFactor: "3",
    kafkaSerializationType: "AVRO",
    kafkaCompressionFormat: "NONE",
    kafkaUseSchemaRegistry: "true",
    kafkaSchemaCompatibilityMode: "DEFAULT",
    kafkaSchemaSubjectNameStrategy: "TopicNameStrategy",
  };

  static CONFLUENT_COMPATABILITY_MODE = {
    DEFAULT: "DEFAULT",
    NONE: "NONE",
    BACKWARD: "BACKWARD",
    FORWARD: "FORWARD",
    BACKWARD_TRANSITIVE: "BACKWARD_TRANSITIVE",
    FORWARD_TRANSITIVE: "FORWARD_TRANSITIVE",
    FULL: "FULL",
    FULL_TRANSITIVE: "FULL_TRANSITIVE",
  };

  static CONFLUENT_OFFSET_MODES = [
    {
      key: "from-earliest",
      text: "Read from earliest offset",
    },
    {
      key: "from-latest",
      text: "Read from latest offset",
    },
  ];

  static HANDLE_SCHEMA_MISSMATCH = {
    DROP_RECORD: "drop-record",
    FAIL_WITH_ERROR: "fail-with-error",
  };

  static CONFLUENTP_FAIL_TRUNCATE = {
    FAIL: "fail",
    TRUNCATE: "truncate",
  };

  static VALID_CSV_ENCODING = {
    UTF_8: "UTF-8",
    UTF_16: "UTF-16",
    ISO_8859_1: "ISO-8859-1",
  };

  static LTF_SKIPPED_DATA_TYPES_IN_TARGET = ["time", "uint8"];
  static LTF_ANNOTATIONS = {
    DELTA_LAKE: { "#": "DELTA_LAKE" },
    PARAMS: {
      EXTRACTION_MODE: {
        "@EndUserText.label": "Extraction Mode",
        type: "cds.String",
        length: 10,
        default: "FULL",
        "@DataWarehouse.bw.extractionMode": true,
      },
      FROM_CHANGE_TIME: {
        "@EndUserText.label": "From Change Time",
        type: "cds.Timestamp",
        default: "0001-01-01 00:00:00",
      },
      TILL_CHANGE_TIME: {
        "@EndUserText.label": "Till Change Time",
        type: "cds.Timestamp",
        default: "9999-12-31 23:59:59",
      },
    },
    SEMANTIC_INTERVAL: [
      {
        qualifier: "changeTime",
        lowerBoundaryParameter: { "=": "FROM_CHANGE_TIME" },
        lowerBoundaryIncluded: false,
        upperBoundaryParameter: { "=": "TILL_CHANGE_TIME" },
        upperBoundaryIncluded: true,
      },
    ],
  };

  static factoryForSourceObject() {
    return {
      key: false,
      datatype: "string",
      name: Constants.SKIP_MAPPING,
      businessName: Constants.SKIP_MAPPING,
      scale: undefined,
      precision: undefined,
      length: 100,
      isCDCColumn: false,
      attributeMapping: undefined,
    };
  }

  static CONFLUENT_SCHEMA_TYPE = {
    AVRO: "AVRO",
    JSON: "JSON",
  };

  static SIGNAVIO_DEFAULT_SETTINGS = {
    groupDeltaFilesBy: "NONE",
    format: "PARQUET",
    compression: "SNAPPY",
    "objectstore.write.useDuplicateSuppressionInitialLoad": "false",
    "objectstore.write.parquet.compatibilityMode": "NONE",
  };

  static ABAPcontentTypeDisabled = "ABAPcontentTypeDisabled";
}
