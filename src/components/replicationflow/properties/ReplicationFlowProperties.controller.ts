/**
 * Copyright 2022 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { ShellNavigationService } from "@sap/orca-shell";
import { IWorkbenchController, SidepanelMode } from "../../abstractbuilder/api";
import { isDiMonitorImprovementsEnabled } from "../../abstractbuilder/commonservices/FeatureFlagCheck";
import { AbstractControllerClass } from "../../abstractbuilder/controller/AbstractController.controller";
import { getSpaceName } from "../../abstractbuilder/utility/BuilderUtils";
import { BaseController, smartExtend } from "../../basecomponent/controller/BaseController.controller";
import { NameUsage } from "../../commonui/utility/NameInputValidator";
import {
  getDatabuilderWorkbench,
  getIsEditorSupportVersions,
  getIsVersioningReadOnlyMode,
  openDatabuilderValidationsPopoverBy,
} from "../../databuilder/utility/DatabuilderHelper";
import * as commonUtils from "../../ermodeler/js/utility/CommonUtils";
import {
  aggregationValidationCount,
  aggregationValidationType,
  aggregationValidationTypeCustomStyle,
} from "../../ermodeler/js/utility/commonFormatter";
import {
  dateFormatter,
  objectStatusIconFormatter,
  objectStatusTextFormatter,
  objectStatusTooltipFormatter,
  revertObjectStatusIconFormatter,
  revertObjectStatusTextFormatter,
  revertObjectStatusVisible,
} from "../../ermodeler/js/utility/sharedFunctions";
import { Format } from "../../reuse/utility/Format";
import { MessageHandler } from "../../reuse/utility/MessageHandler";
import { NamingHelper } from "../../reuse/utility/NamingHelper";
import { ContentType, HttpMethod, ServiceCall } from "../../reuse/utility/ServiceCall";
import { ObjectStatus } from "../../reuse/utility/Types";
import { Activity, ApplicationId } from "../../tasklog/utility/Constants";
import { ITTaskScheduleController, ITaskScheduleRequest } from "../../taskscheduler/controller/TaskSchedule.controller";
import { getTaskScheduer, openSchedulePopover, recordAction } from "../../taskscheduler/utility/ScheduleUtil";
import { RFBuilderClass } from "../controller/RFBuilder.controller";
import api from "../js/APIService";
import { ModelBuilder } from "../js/model/modelBuilder";
import Constants from "../js/utility/constants";
import Utils from "../js/utils";

enum ForceStatus {
  byOthers = "byOthers",
  disabled = "disabled",
  enabled = "enabled",
}

export class ReplicationFlowPropertiesClass extends AbstractControllerClass {
  static smallWidth: sap.ui.core.CSSSize = "460px";
  private galileiModel;
  private workbenchEnvModel;
  public isEditable: boolean;
  private modelBuilder: ModelBuilder;
  private oResourceModel: any;
  public isDeployedRFSchedulable: boolean = false;
  public contentType: string;
  isReusableTaskScheduleFFEnabled: boolean;
  isAdoptionOfTaskSchedulerEnabled: boolean;

  public onInit(): void {
    super.onInit();
    const bundleName = require("../i18n/i18n.properties");
    this.oResourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: bundleName,
    });
    this.getView().setModel(this.oResourceModel, "i18n");
    // load er modeler resource bundle to avoid duplication of deployment status
    const erBundleName = require("../../ermodeler/i18n/i18n.properties");
    const oERResourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: erBundleName,
    });
    this.getView().setModel(oERResourceModel, "i18n_erd");
    const isVersionReadOnlyMode = getIsVersioningReadOnlyMode() && getIsEditorSupportVersions();

    const oVersionReadOnlyMode = new sap.ui.model.json.JSONModel({
      isVersionReadOnlyMode: isVersionReadOnlyMode,
    });

    this.getView().setModel(oVersionReadOnlyMode, "versionReadOnlyMode");
    sap.ui
      .getCore()
      .getEventBus()
      .subscribe("propertyPanel", "updateStatus", (channel, event, oResponse) => {
        if (oResponse && oResponse.data) {
          this.updateStatus(oResponse);
        } else {
          setTimeout(() => {
            this.fetchStatus();
          }, 1000);
        }
      });
    sap.ui
      .getCore()
      .getEventBus()
      .subscribe("propertyPanel", "updateDeploymentStatus", () => {
        this.getView().getModel("galileiModel")?.refresh(true);
        this.setupScheduleActionsModel();
      });

    sap.ui
      .getCore()
      .getEventBus()
      .subscribe("propertyPanel", "validateReplicationFlowPanel", () => {
        this.validateReplicationFlowPanel();
      });

    const oABAPContentTypesModel = new sap.ui.model.json.JSONModel({
      ABAPcontentTypes: [
        { key: Constants.ABAP_CONTENT_TYPE.nativeType },
        { key: Constants.ABAP_CONTENT_TYPE.templateType },
      ],
    });
    this.getView().setModel(oABAPContentTypesModel, "ABAPContentTypeModel");
    this.isReusableTaskScheduleFFEnabled = sap.ui
      .getCore()
      .getModel("featureflags")
      .getProperty("/DWCO_REUSABLE_TASK_SCHEDULING_UI");
    this.isAdoptionOfTaskSchedulerEnabled = sap.ui
      .getCore()
      .getModel("featureflags")
      .getProperty("/DWCO_REUSABLE_TASK_SCHEDULER_ADOPTION");
  }

  /**
   * Function to handle the change event of the Skip Unmap Column toggle
   * @param oEvent
   */
  public onSkipUnmapColToggle(oEvent) {
    const bState: boolean = oEvent.getParameter("state");
    const oModel = this.getView().getModel("galileiModel");
    // hasSkipMappingCapability will be there for all new and existing RFs before this point
    oModel.setProperty("/replicationTaskSetting/hasSkipMappingCapability", bState);
  }

  /**
   * Function to handle the change event of the auto merge toggle for ltf targets
   * @param oEvent
   */
  public onAutoMergeTargetToggle(oEvent) {
    const bState: boolean = oEvent.getParameter("state");
    const oModel = this.getView().getModel("galileiModel");
    oModel.setProperty("/replicationFlowSetting/isAutoMergeEnabledForTarget", bState);
  }

  /**
   * Function to handle the change of content Type
   * @param oEvent
   */

  changeContentType(oEvent) {
    const newContentType = oEvent.getSource().getSelectedKey();
    const galileiModel = this.getView().getModel("galileiModel");
    sap.m.MessageBox.warning(this.localizeText("confirmChangeContentTypeMessage"), {
      title: "Warning",
      actions: ["Confirm", sap.m.MessageBox.Action.CANCEL],
      emphasizedAction: "Confirm",
      onClose: function (oAction: string) {
        if (oAction === "Confirm") {
          sap.ui.getCore().getEventBus().publish("ABAPcontentType", "updateABAPContentType", {
            newContentType,
          });
        }
      },
    });
    const contentType = galileiModel.getProperty("/replicationFlowSetting/ABAPcontentType");
    oEvent.getSource().setSelectedKey(contentType);
  }

  public formatters = {
    objectStatusIconFormatter,
    objectStatusIconColorFormatter: Format.objectStatusIconColorFormatter.bind(this),
    objectStatusTextFormatter,
    objectStatusTooltipFormatter,
    dateFormatter,
    revertObjectStatusIconFormatter,
    revertObjectStatusTextFormatter,
    revertObjectStatusVisible,
  };

  /**
   * set object model
   * @param {any} oObject
   * @returns {void}
   */
  public setObjectModel(oObject: any): void {
    // check if previous galilei model and new model are same
    // if not , load status and set scheduleActionModel
    this.setHeaderModel(oObject);
    if (this.galileiModel !== oObject) {
      this.galileiModel = oObject;

      this.setupScheduleActionsModel();
      if (!this.galileiModel.isNew) {
        // no need to call api to fetch the status of new data flow
        this.fetchStatus();
      }
    }
    this.setGalileiModel();
    this.setWorkbenchEnvModel();
    this.validateReplicationFlowPanel();
  }

  validateReplicationFlowPanel() {
    const oModel = this.getView().getModel("galileiModel").getData();
    this.validateDeltaCheckInterval(oModel.deltaCheckIntervalHour, oModel.deltaCheckIntervalMinute);
  }

  public async checkSchedulePossible(spaceId: string, rfTechincalName: string) {
    const oStatusPanel: any = this.getView().byId("rfPropertyStatusPanel");
    try {
      oStatusPanel.setBusy(true);
      oStatusPanel.setBusyIndicatorDelay(0);

      const resp = await api.isSchedulePossible(spaceId, rfTechincalName);
      oStatusPanel.setBusy(false);
      return resp.data.schedulable;
    } catch (e) {
      oStatusPanel.setBusy(false);
      MessageHandler.exception({ message: this.getText("SCHEDULE_CHECK_FAILED"), exception: e });
      return false;
    }
  }

  public async openSchedule(oEvent: any) {
    const applicationId = ApplicationId.REPLICATION_FLOWS;
    const spaceId = getSpaceName();
    const isSpaceOpen = this.getView().getModel("galileiModel").getProperty("/isSpaceOpen");
    const rfTechincalName = this.galileiModel.name;
    const link = oEvent.getSource();
    const data: ITaskScheduleRequest = {
      objectId: rfTechincalName,
      applicationId: ApplicationId.REPLICATION_FLOWS,
      activity: Activity.EXECUTE,
      description: "Replication flow details",
      activationStatus: "ENABLED",
    };

    if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
      await this.initScheduleDialog();
      if (isDiMonitorImprovementsEnabled()) {
        await openSchedulePopover(
          link,
          rfTechincalName,
          applicationId,
          spaceId,
          this["newScheduleDialog"],
          data,
          this["view"],
          this.getSchedule.bind(this),
          !isSpaceOpen
        );
      } else {
        await openSchedulePopover(link, rfTechincalName, applicationId, spaceId, this["newScheduleDialog"]);
      }
    } else {
      const scheduleDialog = (
        this.getView().byId("detailSchedulingDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;

      scheduleDialog.openSchedulePopover(link, rfTechincalName, applicationId, spaceId);
    }
  }

  /**
   * set header model
   * @param {object} object
   */
  public async setHeaderModel(object?: any) {
    const modelObject: any = object || {};
    const oModel: sap.galilei.ui5.GalileiModel = new sap.galilei.ui5.GalileiModel(modelObject);
    this.isEditable = modelObject.isReadOnlyObject !== true && this.getHasPrivileges();
    oModel.setProperty("/editable", this.isEditable);
    this.getView().setModel(oModel, "header");
  }

  /**
   * set galilei model
   */
  public async setGalileiModel() {
    const oModel: sap.galilei.ui5.GalileiModel = this.createModel(this.galileiModel);
    this.getView().setModel(oModel, "galileiModel");
    const spaceId = getSpaceName();
    const isSpaceOpen = await this.checkSpaceLockPrivilege(spaceId);
    this.getView().getModel("galileiModel").setProperty("/isSpaceOpen", isSpaceOpen);
  }

  /**
   * set galilei model
   */
  public setWorkbenchEnvModel() {
    // had to set because getModel("workbenchEnv") was undefined when context of the detail changes
    // but callback of previous context wants workbench
    this.workbenchEnvModel = this.getView().getModel("workbenchEnv");
  }
  /**
   * create model
   * @param {object} object
   * @returns {sap.galilei.ui5.GalileiModel}
   */
  public createModel(object: object): sap.galilei.ui5.GalileiModel {
    const oModel: sap.galilei.ui5.GalileiModel = new sap.galilei.ui5.GalileiModel(object);
    oModel.setSizeLimit(500);
    return oModel;
  }

  /**
   * get privileges
   * @returns {boolean}
   */
  private getHasPrivileges(): boolean {
    // Check the feature flag
    let hasPrivileges: boolean;
    const workbenchEnvModel = this.getView().getModel("workbenchEnv");
    if (workbenchEnvModel) {
      hasPrivileges = workbenchEnvModel.getProperty("/canCreateOrUpdateModel");
    } else {
      const privileges = sap.ui.getCore().getModel("privilege").getData().DWC_DATABUILDER;
      hasPrivileges = privileges.create || privileges.update;
    }
    return hasPrivileges;
  }

  /**
   * handler for business name change
   * @param {any} event
   */
  public onBusinessNameChange(event) {
    const nameValidator = NamingHelper.getNameInputValidator();
    const technicalNameInput = this.getView().byId("rfTechnicalName") as sap.m.Input;
    const model = this.getView().getModel("galileiModel").getProperty("/");
    nameValidator.onBusinessNameChange(event, technicalNameInput, NameUsage.entity, undefined, model.name);
  }

  /**
   * handler for technical name change
   * @param {any} event
   */
  public onTechnicalNameChange(event) {
    const nameValidator = NamingHelper.getNameInputValidator();
    nameValidator.onTechnicalNameChange(event, NameUsage.entity);
  }

  public showScheduleFormatter(execute, create, update, deleteSpace, deploymentDate, isSpaceOpen): boolean {
    const isNotDeployed = deploymentDate === 0 || deploymentDate === null || deploymentDate === undefined;
    return execute && create && update && deleteSpace && !isNotDeployed && !!isSpaceOpen;
  }

  public enableContentType(sourceConnType): boolean {
    const galileiModel = this.getView().getModel("galileiModel");
    const ABAPcontentTypeDisabled = galileiModel.getProperty("/replicationFlowSetting/ABAPcontentTypeDisabled");
    if (Utils.isConnectionTypeABAP(sourceConnType) && !ABAPcontentTypeDisabled && this.isEditable) {
      return true;
    }
    return false;
  }

  public isContentTypeVisible(sourceConnType): boolean {
    if (Utils.isConnectionTypeABAP(sourceConnType)) {
      return true;
    }
    return false;
  }

  /**
   * For now its only visible for dwc ltf targets
   * @param {*} targetConnectionId
   * @param {*} targetConnectionType
   * @param {*} isFFenabled
   * @return {*}  {boolean}
   * @memberof ReplicationFlowPropertiesClass
   */
  public isAutoMergeTargetVisible(targetConnectionId, targetConnectionType): boolean {
    if (Utils.isConnectionTypeDwcLTF(targetConnectionId, targetConnectionType)) {
      return true;
    }
    return false;
  }

  public showConfluentExtFields(connectionType, confluentAsSourceFF, confluentAsSourceExtFF): boolean {
    if (Utils.isConnectionTypeCONFLUENT(connectionType) && confluentAsSourceFF && confluentAsSourceExtFF) {
      return true;
    }
    return false;
  }
  /**
   * nav icon formatter
   * @param {string} status
   * @returns {boolean}
   */
  public showNavIconFormatter(status: string): boolean {
    if (!status) {
      return false;
    }
    return true;
  }
  public async setupScheduleActionsModel() {
    if (Utils.isHanaNotProvisioned()) {
      return;
    } else {
      const oScheduleActionModel = new sap.ui.model.json.JSONModel({
        isScheduled: false,
      });
      this.getView().setModel(oScheduleActionModel, "scheduleActionModel");
      const i18nTaskModel = new sap.ui.model.resource.ResourceModel({
        bundleName: require("../../taskscheduler/i18n/i18n.properties"),
      });
      this.getView().setModel(i18nTaskModel, "i18n_task");
      if (getIsVersioningReadOnlyMode() && getIsEditorSupportVersions()) {
        this.getView().getModel("versionReadOnlyMode").setProperty("/isVersionReadOnlyMode", true);
      } else {
        this.getView().getModel("versionReadOnlyMode").setProperty("/isVersionReadOnlyMode", false);
      }
      if (!this.galileiModel.isNew) {
        this.getSchedule();
      }
    }
  }
  /**
   * Setting up the isSchedule Property of schedule Action model.
   * @memberof ModelPropertiesClass
   */
  public async getSchedule() {
    try {
      const sSpaceId = getSpaceName();
      if (!sSpaceId) {
        return;
      }
      const rfTechincalName = this.galileiModel.name;
      const oResponse = await api.getSchedule(sSpaceId, rfTechincalName);

      if (oResponse.data[0] && oResponse.data[0].scheduleId) {
        const scheduleModel = this.getView().getModel("scheduleActionModel") as sap.ui.model.json.JSONModel;
        scheduleModel.setProperty("/isScheduled", true);
        if (oResponse.data[0].activationStatus === "ENABLED") {
          scheduleModel.setProperty(
            "/refresh_frequency",
            //this.getView().getModel("i18n").getResourceBundle().getText("SCHEDULED")
            this.getScheduledText(oResponse.data[0])
          );
          scheduleModel.setProperty("/isSchedulePaused", false);
        } else if (oResponse.data[0].activationStatus === "DISABLED") {
          scheduleModel.setProperty(
            "/refresh_frequency",
            this.getView().getModel("i18n").getResourceBundle().getText("PAUSED")
          );
          scheduleModel.setProperty("/isSchedulePaused", true);
        }
        scheduleModel.setProperty("/refresh_frequency_active", true);
      } else {
        this.resetScheduleModel();
      }
    } catch (e) {
      MessageHandler.exception({
        exception: e,
        message: this.getText("VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE"),
        id: "scheduleGetError",
      });
      this.resetScheduleModel();
    }
  }

  public getScheduledText(schedule) {
    if (isDiMonitorImprovementsEnabled()) {
      //if (schedule) {
      return schedule?.cron !== undefined
        ? schedule?.cron
        : this.getText("everyLabel") +
            " " +
            schedule?.frequency?.interval +
            " " +
            this.getFrequencyText(schedule?.frequency?.type);
      // } else {
      //   return this.i18nModel.getResourceBundle().getText("EMPTY_CELL");
      // }
    }
    return this.getView().getModel("i18n").getResourceBundle().getText("SCHEDULED");
  }

  public getFrequencyText(type) {
    switch (type) {
      case "MINUTES":
        return this.getText("minutesLabel");
      case "HOURLY":
        return this.getText("hoursLabel");
      case "DAILY":
        return this.getText("daysLabel");
      case "WEEKLY":
        return this.getText("weeksLabel");
      case "MONTHLY":
        return this.getText("monthsLabel");
      default:
        return "";
    }
  }

  public showScheduleErrorDialog(): void {
    const isDeltONlyEnabled = sap.ui
      .getCore()
      .getModel("featureflags")
      .getProperty("/DWCO_DS_REPLICATION_FLOW_DELTA_ONLY_LOAD_TYPE");
    const errorKey = isDeltONlyEnabled
      ? "msgReplicationFlowScheduleErrorTextUpdated"
      : "msgReplicationFlowScheduleErrorText";
    sap.m.MessageBox.error(this.getView().getModel("i18n").getResourceBundle().getText(errorKey), {
      id: "idScheduleErrorDialog",
      actions: [sap.m.MessageBox.Action.CLOSE],
    });
  }

  /**
   * Show schedule error when status is deploying
   */
  public showErrorWhendeploying(): void {
    sap.m.MessageBox.error(
      this.getView().getModel("i18n").getResourceBundle().getText("msgReplicationFlowDeployScheduleError", ["\n"]),
      {
        id: "idScheduleDeployErrorDialog",
        actions: [sap.m.MessageBox.Action.CLOSE],
      }
    );
  }

  /**
   * Create a new schedule
   * @memberof ModelPropertiesClass
   */
  public async onCreateSchedule(): Promise<void> {
    const oModel = this.getView().getModel("galileiModel");
    oModel.refresh(true);
    const isDeployed = oModel.getProperty("/deploymentDate") ? true : false;
    const isDeploying = +oModel.getProperty("/#objectStatus") === ObjectStatus.pending ? true : false;
    const spaceId = getSpaceName();
    const rfTechincalName = oModel.getProperty("/name");
    this.isDeployedRFSchedulable = await this.checkSchedulePossible(spaceId, rfTechincalName);
    if (isDeploying) {
      this.showErrorWhendeploying();
    } else if (!this.isDeployedRFSchedulable && isDeployed) {
      this.showScheduleErrorDialog();
    } else {
      const oResourceBundle = this.getView().getModel("i18n_task").getResourceBundle();
      const rfTechincalName = this.galileiModel.name;
      const sSpaceId = getSpaceName();
      const data: ITaskScheduleRequest = {
        objectId: rfTechincalName,
        applicationId: ApplicationId.REPLICATION_FLOWS,
        activity: Activity.EXECUTE,
        description: this.getText("CREATE_REPLICATION_TEXT"),
        activationStatus: "ENABLED",
      };
      let scheduleDialog;
      if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
        await this.initScheduleDialog();
        scheduleDialog = this["newScheduleDialog"];
        recordAction(`createTaskSchedule: ${data.applicationId}`, "taskSchedule", "onCreate");
      } else {
        scheduleDialog = (
          this.getView().byId("detailSchedulingDialog") as sap.ui.core.mvc.View
        ).getController() as ITTaskScheduleController;
      }

      const oDataBuilderWorkbenchController = (
        sap.ui.getCore().byId("shellMainContent---databuilderComponent---databuilderWorkbench") as sap.ui.core.mvc.View
      ).getController() as IWorkbenchController;
      oDataBuilderWorkbenchController.setBusy(true);

      scheduleDialog.createTaskSchedule(
        data,
        sSpaceId,
        ApplicationId.REPLICATION_FLOWS,
        () => {
          if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
            recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
          }
          const msg = oResourceBundle.getText("createScheduleSuccess");
          MessageHandler.success(msg);
          oDataBuilderWorkbenchController.setBusy(false);
          this.getSchedule();
        },
        (error) => {
          if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
            recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
            MessageHandler.exception({
              exception: error.error,
              message: error.message,
            });
          }
          oDataBuilderWorkbenchController.setBusy(false);
        },
        () => {
          oDataBuilderWorkbenchController.setBusy(false);
        }
      );
    }
  }

  public async checkSpaceLockPrivilege(spaceId: string): Promise<boolean> {
    const spaceObject = await ServiceCall.get("resources/spaces?islocked&spaceids=" + spaceId);
    if (spaceObject.data[spaceId]) {
      const data = spaceObject.data[spaceId];
      return !data.isLocked;
    }
    return true;
  }

  async initScheduleDialog() {
    if (!this["newScheduleDialog"]) {
      this["newScheduleDialog"] = await getTaskScheduer("ReplicationFlowEditorTaskScheduler");
    }
  }

  /**
   * Edit an existing schedule
   * @memberof ModelPropertiesClass
   */
  public async onEditSchedule() {
    const rfTechincalName = this.galileiModel.name;
    const sSpaceId = getSpaceName();
    const oResourceBundle = this.getView().getModel("i18n_task").getResourceBundle();
    const data: ITaskScheduleRequest = {
      objectId: rfTechincalName,
      applicationId: ApplicationId.REPLICATION_FLOWS,
      activity: Activity.EXECUTE,
      description: this.getText("EDIT_REPLICATION_TEXT"),
      activationStatus: "ENABLED",
    };
    let scheduleDialog;
    if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
      recordAction(`editTaskSchedule: ${data.applicationId}`, "taskSchedule", "onEdit");
    } else {
      scheduleDialog = (
        this.getView().byId("detailSchedulingDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }

    const oDataBuilderWorkbenchController = (
      sap.ui.getCore().byId("shellMainContent---databuilderComponent---databuilderWorkbench") as sap.ui.core.mvc.View
    ).getController() as IWorkbenchController;
    oDataBuilderWorkbenchController.setBusy(true);

    scheduleDialog.changeTaskSchedule(
      data,
      sSpaceId,
      ApplicationId.REPLICATION_FLOWS,
      () => {
        if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
        }
        const msg = oResourceBundle.getText("updateScheduleSuccess");
        MessageHandler.success(msg);
        oDataBuilderWorkbenchController.setBusy(false);
        this.getSchedule();
      },
      (error) => {
        if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
          MessageHandler.exception({
            exception: error.error,
            message: error.message,
          });
        }
        oDataBuilderWorkbenchController.setBusy(false);
      },
      () => {
        oDataBuilderWorkbenchController.setBusy(false);
      }
    );
  }

  /**
   * Delete an existing schedule
   * @memberof ModelPropertiesClass
   */
  public onDeleteSchedule(): void {
    const rfTechincalName = this.galileiModel.name;
    const oResourceBundle = this.getView().getModel("i18n_task").getResourceBundle();
    const sSpaceId = getSpaceName();
    const data: ITaskScheduleRequest = {
      objectId: rfTechincalName,
      applicationId: ApplicationId.REPLICATION_FLOWS,
      activity: Activity.EXECUTE,
      description: this.getText("DELETE_REPLICATION_TEXT"),
      activationStatus: "ENABLED",
    };
    const scheduleDialog = (
      this.getView().byId("detailSchedulingDialog") as sap.ui.core.mvc.View
    ).getController() as ITTaskScheduleController;
    const oDataBuilderWorkbenchController = (
      sap.ui.getCore().byId("shellMainContent---databuilderComponent---databuilderWorkbench") as sap.ui.core.mvc.View
    ).getController() as IWorkbenchController;
    oDataBuilderWorkbenchController.setBusy(true);

    scheduleDialog.deleteSchedule(
      data,
      sSpaceId,
      ApplicationId.REPLICATION_FLOWS,
      () => {
        const msg = oResourceBundle.getText("deleteScheduleSuccess");
        MessageHandler.success(msg);
        oDataBuilderWorkbenchController.setBusy(false);
        this.getSchedule();
      },
      (error) => {
        oDataBuilderWorkbenchController.setBusy(false);
      },
      () => {
        oDataBuilderWorkbenchController.setBusy(false);
      }
    );
  }

  public resetScheduleModel(): void {
    const scheduleModel = this.getView().getModel("scheduleActionModel") as sap.ui.model.json.JSONModel;
    scheduleModel.setProperty("/", {});
    scheduleModel.setProperty(
      "/refresh_frequency",
      this.getView().getModel("i18n").getResourceBundle().getText("EMPTY_CELL")
    );
    scheduleModel.setProperty("/isScheduled", false);
  }
  /**
   * handles navigating to monitoring
   */
  public navToMonitoring() {
    const params = {
      objectId: this.galileiModel.name,
      spaceId: getSpaceName(),
    };
    const semanticObject = "dataintegration";
    const targetParameter = "routeTo";
    params[targetParameter] = "replicationFlowMonitorDetails";
    ShellNavigationService.toExternal({
      target: {
        semanticObject: semanticObject,
        action: "",
      },
      params,
    });
  }

  /**
   * refresh status
   */
  public refreshStatus() {
    this.fetchStatus();
  }

  /**
   * fullscreen icon formatter
   * @param {SidepanelMode} mode
   * @returns {string}
   */
  public fullScreenIconFormatter(mode: SidepanelMode): string {
    return sap.ui.core.IconPool.getIconURI(mode === SidepanelMode.fullScreen ? "exit-full-screen" : "full-screen");
  }

  /**
   * toggle full screen
   */
  public onToggleFullScreen(/* event: any*/) {
    sap.ui.getCore().getEventBus().publish("propertyPanel", "toggleFullScreen", {
      // put "normal" width here if it is not the default
    });
  }

  /**
   * Sets the default width.
   * @returns {sap.ui.core.CSSSize}
   */
  public getWidth(): sap.ui.core.CSSSize {
    return ReplicationFlowPropertiesClass.smallWidth;
  }

  /**
   * run status formatter
   * @param {string} sStatus
   * @returns {string}
   */
  public runStatusFormatter(sStatus: string, runState: number, activity: string): string {
    const oBundle = this.getView().getModel("i18n").getResourceBundle();
    if (runState === 1002) {
      // When the flow is paused, the run state will be 1002.
      return oBundle.getText("statusPaused");
    }
    if (sStatus === "RUNNING") {
      return activity === "RUN_PERMANENT" || activity === "RUN_PERMANENT_TECHNICAL"
        ? oBundle.getText("statusActive")
        : oBundle.getText("statusRunning");
    }
    if (sStatus) {
      switch (sStatus) {
        case "COMPLETED":
          return oBundle.getText("statusCompleted");
        case "RUNNING":
          return oBundle.getText("statusRunning");
        case "FAILED":
          return oBundle.getText("statusFailed");
        case "STOPPED":
          return oBundle.getText("statusStopped");
        case "STOPPING":
          return oBundle.getText("statusStopping");
        default:
          return oBundle.getText("lblNotExecuted");
      }
    } else {
      return oBundle.getText("lblNotExecuted");
    }
  }
  /**
   * fetch run status
   */
  private fetchStatus() {
    const sSpaceId = getSpaceName();
    const sUrl = `/replicationflow/space/${sSpaceId}/flows/${this.galileiModel.name}/status?updateLock=true`;
    if (!this.galileiModel.isNew) {
      const oStatusPanel: any = this.getView().byId("rfPropertyStatusPanel");
      oStatusPanel.setBusy(true);
      oStatusPanel.setBusyIndicatorDelay(0);
      ServiceCall.request<any>({
        url: sUrl,
        type: HttpMethod.GET,
        contentType: ContentType.APPLICATION_JSON,
      })
        .then(async (oResponse) => {
          this.updateStatus(oResponse);
          this.getView().getModel("galileiModel").refresh(true);
          oStatusPanel.setBusy(false);
        })
        .catch((oError) => {
          this.getView().getModel("galileiModel").refresh(true);
          // eslint-disable-next-line no-console
          oStatusPanel.setBusy(false);
        });
    }
  }

  /**
   * update run status
   * @param {any} oResponse
   */
  updateStatus(oResponse: any) {
    if (oResponse.data && oResponse.data.runs) {
      const oModel = this.getView()?.getModel("galileiModel");
      if (oModel) {
        const currentRfName = oModel["oData"].name;
        //  Checking whether response is for the same RF
        if (currentRfName === oResponse.data.name) {
          const lastRun: any = Utils.getExecutionDetails(oResponse.data.runs);
          if (lastRun) {
            // set formatted time to response
            lastRun.formattedStarted = Format.toLocalDateTime(new Date(lastRun.startTime));
            this.galileiModel.executionDetails = lastRun;
            // updating to enable monitoring nav via toolbar
            if (this.workbenchEnvModel) {
              this.workbenchEnvModel.setProperty("/toolbar/isNavToMonitoringViewEnabled", true);
            }
            // force refresh the model to update the value in the UI
            this.getView().getModel("galileiModel").refresh(true);
          }
        }
      }
    }
  }

  /**
   * showDeploymentDateFormatter
   * @param {any} deploymentDate Deployment date of replication flow
   * @returns {boolean} whether to show deployment date or not
   */
  public showDeploymentDateFormatter(deploymentDate: any): boolean {
    let showDate = false;
    if (deploymentDate) {
      showDate = true;
    }
    return showDate;
  }

  /**
   * Triggered when header validation button is clicked
   * @param {any} oEvent
   */
  public onShowValidations(oEvent: any): void {
    const oControl = oEvent.getParameter("oSource") || oEvent.getSource();
    openDatabuilderValidationsPopoverBy(this.galileiModel, oControl, this.getI18nResources());
  }

  /**
   * get I18n resource
   * @returns {any}
   */
  public getI18nResources(): any {
    const aResourceModels = [];
    const oResourceModel = this.getView().getModel("i18n");
    aResourceModels.push({
      model: oResourceModel,
      name: "i18n_rf",
    });
    return aResourceModels;
  }

  /**
   * aggregation validation type
   * @param {any[]} aValidations
   * @returns {string}
   */
  public aggregationValidationType(aValidations: any[]): string {
    return aggregationValidationType(aValidations);
  }

  /**
   * aggregation validation count
   * @param {any[]} aValidations
   * @returns {string}
   */
  public aggregationValidationCount(aValidations): string {
    return aggregationValidationCount(aValidations);
  }

  /**
   * aggregation validation type custom style
   * @param {any[]} aValidations
   * @returns {string}
   */
  public aggregationValidationTypeCustomStyle(aValidations): string {
    return aggregationValidationTypeCustomStyle(aValidations);
  }

  /**
   * Confirm user to redeploy older version of dataflow
   */
  public openRevertDeploymentDialog() {
    const entity = this.getView().getModel("galileiModel").getData();
    entity.technicalName = entity.name;
    commonUtils.openRevertDeploymentDialog(entity);
  }

  public panelHeaderFormatter(fields: any) {
    return fields ? fields.length : 0;
  }

  public displayPackageSelector(packages) {
    // packages list has at least "none" element, so when the length equals one that means no other package can be selected
    // at this scenario we should hide this package selector
    if (packages?.length > 1) {
      return true;
    } else {
      return false;
    }
  }

  public forceStatusPackageSelector(hasPrivileges, forceStatus) {
    if (!hasPrivileges) {
      return ForceStatus.disabled;
    }
    return forceStatus ?? ForceStatus.byOthers;
  }

  public onPackageSelectionChange() {
    const object = this.getView().getModel("galileiModel") && this.getView().getModel("galileiModel").getData();
    object.validate();
  }

  public cleanseDeltaInterval(oEvent: sap.ui.base.Event) {
    const id = oEvent.getSource().getId(),
      value = oEvent.getSource()["getValue"]();
    if (id.includes("--rfHour")) {
      if ((value && Number(value) > 24) || /\./.exec(value)) {
        const previousValue = oEvent.getSource().getModel("galileiModel").getData().deltaCheckIntervalHour;
        oEvent.getSource().getModel("galileiModel").setProperty("/deltaCheckIntervalHour", previousValue);
        oEvent.getSource().getModel("galileiModel").refresh(true);
        return;
      }
      oEvent.getSource().getModel("galileiModel").setProperty("/deltaCheckIntervalHour", value);
    } else {
      if ((value && Number(value) > 59) || /\./.exec(value)) {
        const previousValue = oEvent.getSource().getModel("galileiModel").getData().deltaCheckIntervalMinute;
        oEvent.getSource().getModel("galileiModel").setProperty("/deltaCheckIntervalMinute", previousValue);
        oEvent.getSource().getModel("galileiModel").refresh(true);
        return;
      }
      oEvent.getSource().getModel("galileiModel").setProperty("/deltaCheckIntervalMinute", value);
    }
    const minutes = oEvent.getSource().getModel("galileiModel").getProperty("/deltaCheckIntervalMinute");
    const hour = oEvent.getSource().getModel("galileiModel").getProperty("/deltaCheckIntervalHour");
    this.getModelBuilder().oResource.applyUndoableAction(() => {
      this.modelBuilder.updateDeltaCheckInterval(hour, minutes);
    }, "Update Deltacheck interval fields");

    this.validateDeltaCheckInterval(hour, minutes);
  }

  public validateDeltaCheckInterval(hour, minutes) {
    const isDeltaCheckConfigurationVisible = this.getView()
      .getModel("galileiModel")
      .getData().isDeltaCheckConfigurationAllowed;
    const seconds = Utils.convertToSeconds(hour, minutes);
    const hourObj = this.getView().byId("rfHour") as sap.m.Input;
    const minObj = this.getView().byId("rfMinutes") as sap.m.Input;

    if (isDeltaCheckConfigurationVisible) {
      if (seconds > 86400) {
        hourObj.setValueState(sap.ui.core.ValueState.Error);
        hourObj.setValueStateText(this.localizeText("maxDeltaInterval", ["\n"]));
        minObj.setValueState(sap.ui.core.ValueState.Error);
        minObj.setValueStateText(this.localizeText("maxDeltaInterval", ["\n"]));
      } else {
        hourObj.setValueState(sap.ui.core.ValueState.None);
        hourObj.setValueStateText(null);
        minObj.setValueState(sap.ui.core.ValueState.None);
        minObj.setValueStateText(null);
      }
      if (seconds < 86400) {
        if (hour === "" || hour === undefined || hour < 0 || hour > 24) {
          hourObj.setValueState(sap.ui.core.ValueState.Error);
          hourObj.setValueStateText(this.localizeText("maxHourOrMinErr", [24]));
        } else {
          hourObj.setValueState(sap.ui.core.ValueState.None);
          hourObj.setValueStateText(null);
        }
        if (minutes === "" || minutes === undefined || minutes < 0 || minutes > 59) {
          minObj.setValueState(sap.ui.core.ValueState.Error);
          minObj.setValueStateText(this.localizeText("maxHourOrMinErr", [59]));
        } else {
          minObj.setValueState(sap.ui.core.ValueState.None);
          minObj.setValueStateText(null);
        }
      }
    } else {
      hourObj.setValueState(sap.ui.core.ValueState.None);
      hourObj.setValueStateText(null);
      minObj.setValueState(sap.ui.core.ValueState.None);
      minObj.setValueStateText(null);
    }

    this.getView().getModel("galileiModel").getData().validate();
  }

  private getModelBuilder(): ModelBuilder {
    if (this.modelBuilder) {
      return this.modelBuilder;
    }
    const databuilderWorkbench = getDatabuilderWorkbench(this.getView())
      ?.getActiveEditor()
      ?.view()
      .getController() as RFBuilderClass;
    this.modelBuilder = databuilderWorkbench.getModelBuilder();
    return this.modelBuilder;
  }

  public localizeText(text: string, parameters: any[] = []): string {
    return this.oResourceModel.getResourceBundle().getText(text, parameters);
  }
}

export const ReplicationFlowProperties = smartExtend(
  BaseController,
  "sap.cdw.components.replicationflow.properties.ReplicationFlowProperties",
  ReplicationFlowPropertiesClass
);

sap.ui.define("sap/cdw/components/replicationflow/properties/ReplicationFlowProperties.controller", [], function () {
  return ReplicationFlowProperties;
});
