/**
 * Copyright 2022 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { RepositoryObjectType } from "@sap/deepsea-types";
import { ShellNavigationService, ShellUIService } from "@sap/orca-shell";
import { SidepanelMode } from "../../abstractbuilder/api";
import {
  AbstractController,
  AbstractControllerClass,
} from "../../abstractbuilder/controller/AbstractController.controller";
import {
  getObjectFilesProperties,
  getRepositoryObject,
  hasNamespace,
  isRepositoryObjectExist,
  updateObjectFileInfo,
} from "../../abstractbuilder/utility/RepositoryUtils";
import { smartExtend } from "../../basecomponent/controller/BaseController.controller";
import { isViewType } from "../../businesscatalogs/utility/BusinessCatalogsUtility";
import { isAnalyticMeasureElement } from "../../commonmodel/utility/CommonUtils";
import { NameUsage } from "../../commonui/utility/NameInputValidator";
import { DataBuilderWorkbench } from "../../databuilder/controller/DataBuilderWorkbench.controller";
import { getDatabuilderWorkbench } from "../../databuilder/utility/DatabuilderHelper";
import { IItem, getCSNDefinition, getEntityNameAndLabel } from "../../datasourcebrowser/utility/DatasourceUtility";
import { PropertyPanelClass } from "../../propertypanel/controller/PropertyPanel.controller";
import { ToolName } from "../../reuse/utility/Constants";
import { MessageHandler } from "../../reuse/utility/MessageHandler";
import { NamingHelper } from "../../reuse/utility/NamingHelper";
import { ContentType, HttpMethod, ServiceCall } from "../../reuse/utility/ServiceCall";
import { ObjectStatus } from "../../reuse/utility/Types";
import { ShellContainer } from "../../shell/utility/Container";
import { Crud } from "../../shell/utility/Crud";
import { Repo } from "../../shell/utility/Repo";
import { getMostRecentHelpConfiguration, setHelpScreenId } from "../../shell/utility/WebAssistantHelper";

import { SupportedFeaturesService } from "../../commonmodel/api/SupportedFeaturesService";
import { notificationsEventChannel, notificationsFetched } from "../../notifications/utility/Types";
import { showDialog } from "../../reuse/utility/UIHelper";
import { DWCFeature, EventType } from "../../shell/utility/ShellUsageCollectionService";
import { RFBuilderFormatter } from "../formatter/RFBuilderFormatter";
import APIService from "../js/APIService";
import { ModelBuilder } from "../js/model/modelBuilder";
import { ModelToJSON } from "../js/model/modelToJSON";
import RFBuilderUtils from "../js/utility/RFBuilderUtils";
import Constants from "../js/utility/constants";
import { GBQQueryBuilder } from "../js/utility/gbqQueryBuilder";
import Utils from "../js/utils";
import {
  CFWContexts,
  IABAPExits,
  IColumnDetail,
  IColumnProperties,
  ICustomConfig,
  IProposedEntities,
  IReplicationFlow,
  IRunReplicationFlowPayload,
  ITransform,
} from "../replicationApi";

interface IFlowagentMetadata {
  businessName?: any;
  name: string;
  qualifiedName: string;
  columns: any[];
  invalidColumns: string[];
  deltaDisabled?: boolean;
  tableCategory?: string;
  ABAPExitList?: IABAPExits[];
  isSACArtefact?: boolean;
  type?: string;
}

interface IDwcMetadata {
  name: string;
  columns: any[];
  invalidColumns: string[];
  isCDCDataset: boolean;
  deltaDisabled?: boolean;
  isSACArtefact?: boolean;
  businessName?: string;
  tableCategory?: string;
  type?: string;
}

interface ISourceConfigdata {
  colDelimiter?: string;
  format?: string;
  header?: boolean;
  charset?: string;
  confluentReadSchemaSubject?: string;
  confluentReadSchemaVersion?: string;
  includeTechnicalKey?: boolean;
  confluentSchemaType?: string;
  confluentMessageIdColumn?: string;
  includeNotExpandedArraysAndMaps?: boolean;
}

export class RFBuilderClass extends AbstractControllerClass {
  private oFeatures: any;
  private i18nResourceModel: any;
  private options: { repositoryObjects; crossSpaceObjects };
  private workbench: any;
  private oResource: sap.galilei.model.Resource;
  private oModel: sap.cdw.replicationflow.Model;
  private modelBuilder: ModelBuilder;
  private sourceInfoPopover: sap.m.Popover;
  private aSelectedReplicationTask: sap.cdw.replicationflow.Task[];
  private propertyPaneController: PropertyPanelClass;
  public formatter = RFBuilderFormatter;
  private currentDatasetBrowserType: "source" | "target"; // required to handle callback for local repository browser in onRepositoryObjectsSelected()
  private oBusyDialog: any;
  private oExecuteConfirmDialog: any;
  private oConnectionDialog: any;
  private oContainerDialog: any;
  private aPath: Array<{ id: string; type: string }>;
  private oLastContextMenuClickedReplicationTask: sap.cdw.replicationflow.Task;
  private oRenameTargetDialog: any;
  public isEditable: boolean;
  private databuilderWorkbench: DataBuilderWorkbench;
  private sqlDialog: any;
  private previousHelpScreenId: string;
  private getDeploymentStatusRetries = 0;
  private updateObjectsFileInfoTimer: any;
  private isLargeSystemSpace: boolean = false;

  public onInit(): void {
    require("../css/style.css");
    super.onInit();
    this.getPropertyPanelController();

    if (this.propertyPaneController) {
      this.registerProperyPanels();
    }
    const bundleName = require("../i18n/i18n.properties");
    this.i18nResourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: bundleName,
    });
    this.getView().setModel(this.i18nResourceModel, "i18n");
    this.spaceName = this.getSpaceName();
    this.oFeatures = (sap.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel).getData();

    this.initializeUiModel();
    this.initializeUiState();
    sap.ui
      .getCore()
      .getEventBus()
      .subscribe("navToRFProperties", "updateTableSelection", this.onRemoveAllSelection, this);
    sap.ui
      .getCore()
      .getEventBus()
      .subscribe("addTransformation", "openTransformationDialog", this.openTransformationDialog, this);
    sap.ui
      .getCore()
      .getEventBus()
      .subscribe(
        "transformationDeleteFromPanel",
        "deleteTransformation",
        this.handleTransformationDeleteFromPanel,
        this
      );
    sap.ui
      .getCore()
      .getEventBus()
      .subscribe("schemaChange", "updateSourceSchema", this.updateTaskAFterSchemaChange, this);
    sap.ui
      .getCore()
      .getEventBus()
      .unsubscribe(notificationsEventChannel, notificationsFetched, this.onTaskNotification.bind(this), this);
    sap.ui
      .getCore()
      .getEventBus()
      .subscribe(notificationsEventChannel, notificationsFetched, this.onTaskNotification.bind(this), this);
    sap.ui
      .getCore()
      .getEventBus()
      .subscribe("ABAPcontentType", "updateABAPContentType", this.publishSubscribeOfABAPContentType, this);
  }

  public onBeforeRendering(): void {
    return;
  }

  public onAfterRendering(): void {
    this.loopToUpdateDeploymentStatus();
    this.bindEventToUpdateCount();
    this.setDeployButtonClickStatus(false);
  }

  private loopToUpdateDeploymentStatus(): void {
    this.getDeploymentStatusRetries = 0;
    if (this.updateObjectsFileInfoTimer) {
      clearTimeout(this.updateObjectsFileInfoTimer);
    }
    const oModel: any = this.getModel();
    if (oModel && !oModel.isNew) {
      this.updateObjectsFileInfo();
    }
  }

  public localizeText(text: string, parameters: any[] = []): string {
    return this.i18nResourceModel.getResourceBundle().getText(text, parameters);
  }

  public localizeMessage(sModelName: string, sMessageId: string): string {
    return Utils.localizeMessage(this.getView(), sModelName, sMessageId);
  }

  public restorePreviousHelpScreenId() {
    if (this.previousHelpScreenId) {
      const subScreenIdList = this.previousHelpScreenId.split(":");
      const screenId = subScreenIdList[subScreenIdList.length - 1];
      setHelpScreenId(screenId);
    }
  }

  public async setContextObject(spaceId: string, modelId: string) {
    super.setContextObject(spaceId, modelId);
    this.spaceGUID = await this.getSpaceGUID();
    this.isLargeSystemSpace = await this.checkLargeSystemSpace();
    if (this.options !== undefined) {
      this.options = { repositoryObjects: [], crossSpaceObjects: [] };
    }
    this.resetUiModel();
  }

  public setWorkBench(workbench) {
    this.workbench = workbench;
  }

  /**
   * Refresh the RF status  when notification received
   * @param channel channel name
   * @param event event object
   * @param oRes response object with notification details
   */
  public async onTaskNotification(channel, event, oRes) {
    const oGalileiUiModel = this.getView()?.getModel("galileiUiModel");
    if (oGalileiUiModel) {
      const rfName = oGalileiUiModel["oData"]?.name;
      const oStatus = this.getNotificationStatus(oRes[0]?.body, rfName);
      if (oStatus.isOwnMessage && (oStatus.isRunMessage || oStatus.isDeployMessage)) {
        sap.ui.getCore().getEventBus().publish("propertyPanel", "updateStatus");
      }
    }
  }

  /**
   * Return the notification status based on action type
   * @param sMsgText notification message text
   * @param rfName rf name
   */
  public getNotificationStatus(sMsgText, rfName) {
    const oStatus = {
      isOwnMessage: false,
      isRunMessage: false,
      isDeployMessage: false,
    };
    if (rfName && sMsgText) {
      oStatus.isOwnMessage = Utils.ContainsExactWord(sMsgText, rfName);
      if (oStatus.isOwnMessage) {
        oStatus.isRunMessage = sMsgText.includes("RUN");
        if (!oStatus.isRunMessage) {
          oStatus.isDeployMessage = sMsgText.includes("deployment");
        }
      }
    }
    return oStatus;
  }

  /**
   * Sets the count of the Datasets in the main canvas
   * @param length - Number of Datasets in string
   */
  private setDatasetCount(length: string): void {
    const txt = this.localizeText("sourceObjectCount", [length]);
    const otext = this.byId("sourceDatasetCount") as sap.m.Text;
    if (otext) {
      otext.setText(txt);
    }
  }

  /**
   * Takes care of updating list count
   *
   * @private
   * @memberof RFBuilderClass
   */
  private bindEventToUpdateCount(): void {
    const oList = this.byId("replicationFlowMainTable") as sap.m.List;
    if (oList) {
      const oBinding = oList.getBinding("items") as sap.ui.model.ListBinding;
      const fn = () => {
        const len = oBinding.getLength().toString();
        this.setDatasetCount(len);
      };
      fn();
      oBinding.attachChange(fn);
    }
  }

  /**
   * one time called to set the property detail controller
   *
   * @private
   * @return {*}  {(PropertyPanelClass | void)}
   * @memberof RFBuilderClass
   */
  private getPropertyPanelController(): PropertyPanelClass | void {
    let oPropertyPanelView;
    if (!this.propertyPaneController) {
      oPropertyPanelView = sap.ui
        .getCore()
        .byId("shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel");
      this.propertyPaneController = oPropertyPanelView && oPropertyPanelView.getController();
    }
    return this.propertyPaneController;
  }

  /**
   * called to notify property detail about the selected object
   *
   * @param {*} object
   * @param {boolean} [hide]
   * @memberof RFBuilderClass
   */
  private notifyPropertyPanel(object: any, hide?: boolean): void {
    this.propertyPaneController.setObjectModel("sap.cdw.replicationflow.ui", object, hide);
  }

  public allLoadtype(initialSupoorted: boolean, deltaSupported: boolean) {
    return initialSupoorted && deltaSupported;
  }
  public onlyInitialSupported(initialSupoorted: boolean, deltaSupported: boolean) {
    return initialSupoorted && !deltaSupported;
  }

  public onlyDeltaSupported(initialSupoorted: boolean, deltaSupported: boolean) {
    return !initialSupoorted && deltaSupported;
  }
  /**
   * on replication object selected
   *
   * @private
   * @param {*} evt
   * @memberof RFBuilderClass
   */
  private onReplicationTaskSelect(evt) {
    this.aSelectedReplicationTask = evt
      .getSource()
      .getSelectedItems()
      .map((o) => o.getBindingContext("galileiUiModel").getObject());
    this.enableOrDisableRFTransformation("select");
    this.changeLabelInToolbarProjectionButton(this.aSelectedReplicationTask);
    this.aSelectedReplicationTask["resp"] = this.notifyPropertyPanel(this.aSelectedReplicationTask);
  }

  /**
   * can be called to remove all replication object selection and select replication flow
   * by default for property detail
   *
   * @memberof RFBuilderClass
   */
  public onRemoveAllSelection() {
    const table: sap.m.Table = this.getView().byId("replicationFlowMainTable") as sap.m.Table;
    table.removeSelections(true);
    this.aSelectedReplicationTask = [];
    this.getWorkbenchController().workbenchModel.setProperty("/toolbar/isRFAddTransformationEnabled", false);
    this.getWorkbenchController().workbenchModel.setProperty("/toolbar/projectionExistsInSelectedDataset", false);
    this.notifyPropertyPanel(this.getModel());
  }

  private registerProperyPanels(): void {
    // // register to properties pane - associates a context and object class name to a view id
    // // Property pane will create the view autmatically when corresponding context and object class name match.
    this.propertyPaneController.registerObjectProperties(
      "sap.cdw.replicationflow.ui",
      "sap.cdw.replicationflow.Task",
      require("../../replicationflow/properties/ReplicationObjectProperties.view.xml")
    );

    this.propertyPaneController.registerObjectProperties(
      "sap.cdw.replicationflow.ui",
      "sap.cdw.replicationflow.Model",
      require("../../replicationflow/properties/ReplicationFlowProperties.view.xml")
    );
  }

  /**
   * expose oModel to component and others
   *
   * @return {*}
   * @memberof RFBuilderClass
   */
  public getModel() {
    return this.oModel;
  }

  /**
   * exposed to be called by component on edit replication flow scenario
   *
   * @param {*} ld
   * @memberof RFBuilderClass
   */
  public async editReplicationFlow(ld: any): Promise<any> {
    const oModel: sap.cdw.replicationflow.Model = this.createModelFromJSON(ld);
    oModel.isNew = false;
    const galileiUiModel = new sap.galilei.ui5.GalileiModel(oModel);
    this.getView().setModel(galileiUiModel, "galileiUiModel");
    const oGalileiUiModel = this.getView().getModel("galileiUiModel");
    oGalileiUiModel.setProperty("/spaceName", this.getSpaceName());
    this.onTabChangeModelUpdate(Constants.TAB_KEYS.TRANSFORMATION);
    await this.checkAndUpdateTargetObject();
    this.clearUndoRedo();
    this.modelBuilder.validateModel();
    this.onRemoveAllSelection();
    this.setHeaderModel(this.getModel());
    this.setDefaultWorkbenchModel();
    this.setPremiumOutBoundModel(false); // setting all properties of model to "false"
    if (Utils.checkEmailNotifFF()) {
      const objectStatus = +oModel["#objectStatus"];
      if (objectStatus === ObjectStatus.deployed) {
        const workbenchEnvModel = this.getWorkbenchController().getWorkbenchEnvModel();
        workbenchEnvModel.setProperty("/toolbar/isEmailNotificationEnabled", true);
      }
    }

    // Display PremiumInBound Message Strip
    oModel.sourceSystems?.get(0)?.metadata?.["isSourceHDLFDeltashare"]
      ? this.setPremiumInBoundModel(true, oModel.sourceSystems.get(0).connectionId)
      : this.setPremiumInBoundModel(false);

    //  Updating Count of the objects after deploy to last version
    if (ld.file.revertLastVersion) {
      this.bindEventToUpdateCount();
    }

    /* Checking the PremiumOutBound eligibility */
    if (this.isTargetConnNonSAP()) {
      this.setBusy(true);
      await this.checksForPremiumOutbound();
      this.setBusy(false);
    }
  }

  /**
   * Call Limiting service and Run Status (All checks required for PremiumOutbound)
   *
   * @memberof RFBuilderClass
   */
  public async checksForPremiumOutbound(isRunTriggered: boolean = false) {
    const isLimitUsage = await this.getLimitUsage();
    if (isLimitUsage && isRunTriggered) {
      // If Run is triggered then show the error alert
      const premiumOutBoundRFRunErrMsg =
        this.localizeText("premiumOutBoundRFCannotStartErrMsgPart1") +
        "\n" +
        this.localizeText("premiumOutBoundRFCannotStartErrMsgPart2") +
        "\n \n" +
        this.localizeText("premiumOutBoundRFAdminErrMsgPart1") +
        "\n" +
        this.localizeText("premiumOutBoundRFAdminErrMsgPart2");
      MessageHandler.show(
        sap.ui.core.MessageType.Error,
        premiumOutBoundRFRunErrMsg,
        this.localizeText("executeError"),
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        "premiumOutBoundRFRunError"
      );
      return false;
    }
    this.setPremiumOutBoundModel(isLimitUsage); // for message strip show/hide
    return true;
  }

  /**
   * exposed to be called by component on new replication flow scenario
   *
   * @memberof RFBuilderClass
   */
  public newReplicationFlow(): void {
    const oModel: sap.cdw.replicationflow.Model = this.createModelFromJSON();
    oModel.isNew = true;
    const galileiUiModel = new sap.galilei.ui5.GalileiModel(oModel);
    this.getView().setModel(galileiUiModel, "galileiUiModel");
    const oGalileiUiModel = this.getView().getModel("galileiUiModel");

    oGalileiUiModel.setProperty("/replicationFlowSetting/ABAPcontentTypeDisabled", false);

    oGalileiUiModel.setProperty("/replicationTaskSetting/hasSkipMappingCapability", oModel.isNew);
    oGalileiUiModel.setProperty("/spaceName", this.getSpaceName());
    this.onTabChangeModelUpdate(Constants.TAB_KEYS.TRANSFORMATION);
    this.clearUndoRedo();
    this.onRemoveAllSelection();
    this.setHeaderModel(this.getModel());
    this.setDefaultWorkbenchModel();
    this.setPremiumOutBoundModel(false); // set all properties of model to "false"
    this.setPremiumInBoundModel(false); // setting all properties of model to "false"
  }

  /**
   *
   * Check if the selected target connection is Non-SAP
   *
   * @returns boolen
   * @memberof RFBuilderClass
   */
  public isTargetConnNonSAP() {
    const oGalileiUiModel = this.getView().getModel("galileiUiModel");
    const selectedTargetConn = oGalileiUiModel.getProperty("/targetSystems/0/connectionId");
    let isTargetConnNonSAP = false;
    if (selectedTargetConn) {
      isTargetConnNonSAP = oGalileiUiModel
        .getProperty("/targetSystems/0/metadata")
        ?.connectionMetaschema?.sources.includes("NonSAP")
        ? true
        : false;
    }
    return isTargetConnNonSAP;
  }

  /**
   * Get the Premium Outbound Limit Usage
   *
   * @returns boolean
   * @memberof RFBuilderClass
   */
  public async getLimitUsage() {
    try {
      const oResponse = await APIService.getPremiumOutboundUsage(this.spaceName);
      return oResponse.limitUsage;
    } catch (error) {
      return false;
    }
  }

  /**
   * PremiumOutBound Model
   * @returns {Object}
   * @memberof RFBuilderClass
   */
  private setPremiumOutBoundModel(bValue) {
    const oModel = this.getView().getModel("premiumOutBoundModel");
    oModel.setProperty("/isLimitUsage", bValue);
    oModel.setProperty("/showWarningStrip", bValue);
  }

  /**
   * Setter PremiumInBound Model
   * @returns {Object}
   * @memberof RFBuilderClass
   */
  private setPremiumInBoundModel(bValue, sSourceConnectionName = "") {
    const oModel = this.getView().getModel("premiumInBoundModel");
    oModel.setProperty("/isSourceHDLFDeltashare", bValue);
    if (bValue) {
      const sMessage = this.localizeText("PremiumInboundWarningMsg", ["\n\n", sSourceConnectionName]);
      oModel.setProperty("/warningMessage", sMessage);
    }
  }
  /**
   * exposed to be called by component during save
   *
   * @return {*}  {Promise<any>}
   * @memberof RFBuilderClass
   */
  public createDocument(): Promise<any> {
    ModelToJSON.featureflags = this.oFeatures;
    const contentJson = ModelToJSON.serializeModel(this.oModel);
    const repoJson = ModelToJSON.generateRepoJson(this.oModel, contentJson);
    const csn: any = {
      content: {
        version: {
          replicationflow: "1.0",
        },
        replicationflows: {},
      },
    };
    csn.content.replicationflows[this.oModel.name] = repoJson;

    // Add dwc target tables definition
    const oTargetSystem = this.oModel.targetSystems.get(0);
    if (oTargetSystem && Utils.isConnectionTypeDWC(oTargetSystem.connectionId, oTargetSystem.connectionType)) {
      const targetDwcTablesDefinition = ModelToJSON.createTargetDwcTablesDefinition(this.oModel);
      csn.content.definitions = targetDwcTablesDefinition;
    }

    // Add revert last version flag
    if (this.oModel.revertLastVersion) {
      // eslint-disable-next-line dot-notation
      csn.content["extensions"] = {
        [this.oModel.name]: {
          revertLastVersion: true,
        },
      };
    }

    return Promise.resolve(csn);
  }

  /**
   * build galilei model from csn
   *
   * @param {*} ld
   * @return {*}  {sap.cdw.replicationflow.Model}
   * @memberof RFBuilderClass
   */
  private createModelFromJSON(ld?: any): sap.cdw.replicationflow.Model {
    this.oResource = new sap.galilei.model.Resource(this.resourceId);
    this.oModel = new sap.cdw.replicationflow.Model(this.oResource);
    this.oResource.model = this.oModel;
    this.modelBuilder = new ModelBuilder(this.oModel, this);
    let content: IReplicationFlow = {
      description: "",
      sourceSystem: [],
      targetSystem: [],
      vTypes: {
        scalar: {},
      },
      replicationTasks: [],
      replicationTaskSetting: {},
      replicationFlowSetting: {},
    };

    if (ld) {
      content = JSON.parse(JSON.stringify(ld.json.replicationflows[ld.file.name].contents));
      this.oModel.label = ld.json.replicationflows[ld.file.name]["@EndUserText.label"];
      this.oModel.name = ld.file.name || undefined;
      this.oModel["#objectStatus"] = ld.file["#objectStatus"] || ObjectStatus.notDeployed;
      this.oModel.deploymentDate = ld.file.deployment_date || null;
      this.oModel.revertLastVersion = ld.file.revertLastVersion || false;
      this.oModel.packageValue = ld.file["#repositoryPackage"] ? ld.file["#repositoryPackage"] : "_NONE_KEY_PACKAGE_";
      if (ld.file.revertLastVersion) {
        // post revert to last deployed version
        // we want user to be able to save the design time replication flow to repository db before executing
        // so setting it to dirty
        this.setEditorToDirtyState();
        //  Updating Count of the objects after deploy to last version
        const len = content.replicationTasks.length.toString();
        this.setDatasetCount(len);
      }
    } else {
      this.oModel.label = "Replication Flow 1";
      this.oModel.name = "Replication_Flow_1";
    }
    return this.modelBuilder.buildModel(content);
  }

  /**
   * hooks on undo click from component.ts
   *
   * @memberof RFBuilderClass
   */
  public onUndo() {
    this.oModel.resource.undo();
    this.modelBuilder.validateModel();
  }

  /**
   * hooks on redo click from component.ts
   *
   * @memberof RFBuilderClass
   */
  public onRedo() {
    this.oModel.resource.redo();
    this.modelBuilder.validateModel();
  }

  /**
   * clears undo redo stack
   *
   * @memberof RFBuilderClass
   */
  public clearUndoRedo() {
    if (this.oModel) {
      this.oModel.resource.clearListOfActions();
      this.oModel.resource.clearUndoStack();
      this.oModel.resource.isLoading = false;
    }
  }

  /**
   * unique resourceid for galilei model
   *
   * @readonly
   * @memberof RFBuilderClass
   */
  private get resourceId() {
    return "UniqueReplicationEditorResource";
  }

  /**
   * any model reset needed on reloads/reopening of replication flow goes here
   *
   * @private
   * @memberof RFBuilderClass
   */
  private resetUiModel() {
    this.getView().getModel().setProperty("/sourceSearchText", "");
    this.getView().getModel().setProperty("/targetSearchText", "");
  }

  /**
   * set the loadtype and tabs model to view
   * @returns {void}
   * @memberof RFBuilderClass
   */
  private initializeUiModel() {
    const constModel = new sap.ui.model.json.JSONModel({
      tabs: Constants.TAB_KEYS,
    });
    const searchProperties = new sap.ui.model.json.JSONModel({
      sourceSearchText: "",
      targetSearchText: "",
    });
    const oConnectionModel = new sap.ui.model.json.JSONModel();

    const oContainerTreeModel = new sap.ui.model.json.JSONModel();
    oContainerTreeModel.setSizeLimit(10000);

    const oContainerDialogOwnModel = new sap.ui.model.json.JSONModel({
      isSourceClicked: false,
      isTargetClicked: false,
      title: this.localizeText("sourceContainernEmptyText"),
      containerPath: "",
      connection: "",
      connectionType: "",
      messageStrip: {
        text: "",
        visible: false,
      },
      isPathContainerClicked: false,
      sourceHdlfDeltaShareProperties: {
        displayConnectionTypeForHDLF: "", // For HDLF , actual connection ($DWC HANA) and display name (HDL_FILES))
        displayConnectionIdForHDLF: "",
        containerPathHDLFDeltashare: "",
        isSourceHDLFDeltashare: false,
        isHDLFSchemaSelected: false,
      },
      isFolderSelection: false,
    });

    const oConnectionDialogOwnModel = new sap.ui.model.json.JSONModel({
      isSourceClicked: false,
      isTargetClicked: false,
      title: this.localizeText("sourceContainernEmptyText"),
    });

    const oTargetRenameModel = new sap.ui.model.json.JSONModel({
      title: this.localizeText("renametargetDialogTitle"),
      targetName: "",
      galiliePath: "",
      targetPath: null,
      maxLen: 255,
      businessNameToDisplay: "",
      showBusinessName: true,
      copySourceColumns: false,
      validation: {
        isValid: true,
        errText: "",
      },
    });

    const oPremiumOutBoundModel = new sap.ui.model.json.JSONModel({
      isLimitUsage: false,
      showWarningStrip: false,
      showWarningStripMsg:
        this.localizeText("premiumOutBoundRFCannotStartWarningMsg") +
        "\n \n" +
        this.localizeText("premiumOutBoundRFAdminWarningMsg"),
    });

    const oPremiumInBoundModel = new sap.ui.model.json.JSONModel({
      isSourceHDLFDeltashare: false,
      warningMessage: "",
    });

    const sqlModel = new sap.ui.model.json.JSONModel({
      ddlContent: "",
      isUncompatibleKeyType: false,
      isMaxKeyColCount: false,
      title: "",
    });

    const oFeatureflagsModel = new sap.ui.model.json.JSONModel(this.oFeatures);

    this.getView().setModel(constModel, "constModel");
    this.getView().setModel(searchProperties);
    this.getView().setModel(oConnectionModel, "connectionModel");
    this.getView().setModel(oContainerTreeModel, "containerTreeModel");
    this.getView().setModel(oContainerDialogOwnModel, "containerDialogOwnModel");
    this.getView().setModel(oConnectionDialogOwnModel, "connectionDialogOwnModel");
    this.getView().setModel(oTargetRenameModel, "TargetRenameModel");
    this.getView().setModel(oPremiumOutBoundModel, "premiumOutBoundModel");
    this.getView().setModel(oPremiumInBoundModel, "premiumInBoundModel");
    this.getView().setModel(sqlModel, "sqlModel");
    this.getView().setModel(oFeatureflagsModel, "featureflagsModel");
  }

  /**
   * store the state which tabs are selected
   * @returns {void}
   * @memberof RFBuilderClass
   */
  private initializeUiState() {
    const uiState = new sap.ui.model.json.JSONModel({
      tabKeySelected: Constants.TAB_KEYS.TRANSFORMATION,
      visibleSettings: false,
      visibleTransform: false,
    });
    this.getView().setModel(uiState, "uiStateModel");
  }

  /**
   * on tab click change the visibility state of models
   * @param {*} evt
   * @memberof RFBuilderClass
   */
  private onTabChange(evt): void {
    const tabKey = evt.getParameter("selectedKey");
    this.onTabChangeModelUpdate(tabKey);
  }

  /**
   * update visibility state of all tokens in the table based on selected tab.
   *
   * @param {*} tabKey
   * @memberof RFBuilderClass
   */
  private onTabChangeModelUpdate(tabKey): void {
    const galileiModel = this.getView().getModel("galileiUiModel").getData();
    const uiStateModel = this.getView().getModel("uiStateModel").getData();
    const isSourceHDLFDeltashare: boolean = this.getView()
      .getModel("galileiUiModel")
      .getProperty("/sourceSystems/0/metadata/isSourceHDLFDeltashare");
    const connectionId: string = this.getView().getModel("galileiUiModel").getProperty("/sourceSystems/0/connectionId");
    const connectionType: string = this.getView()
      .getModel("galileiUiModel")
      .getProperty("/sourceSystems/0/connectionType");
    const isConnectionTypeObjectStore = Utils.isConnTypeObjectStore(connectionType);
    const isNotHDLFDEltaShare = Utils.isConnectionTypeHdlfWithDeltaShare(connectionType, isSourceHDLFDeltashare);
    // To find all the supported Datasets and primary key conditions
    this.getView()
      .getModel("galileiUiModel")
      .setProperty("/isConnTypeObjectStore", isConnectionTypeObjectStore && !isNotHDLFDEltaShare);
    uiStateModel.tabKeySelected = tabKey;
    if (tabKey === Constants.TAB_KEYS.TRANSFORMATION) {
      uiStateModel.visibleTransform = true;
      uiStateModel.visibleSettings = false;
    } else {
      uiStateModel.visibleTransform = false;
      uiStateModel.visibleSettings = true;
    }
    galileiModel.replicationTasks.forEach((oTask) => {
      if (tabKey === Constants.TAB_KEYS.TRANSFORMATION) {
        oTask.uiState.visibleTransform = true;
        oTask.uiState.visibleSettings = false;
      } else {
        oTask.uiState.visibleTransform = false;
        oTask.uiState.visibleSettings = true;
      }
    });
    this.getView().getModel("galileiUiModel").refresh(true);
    this.getView().getModel("uiStateModel").refresh(true);
  }

  /**
   * search source and target dataset
   * when both are present its source AND target search for respective columns
   *
   * @memberof RFBuilderClass
   */
  private onSearchDataset(): void {
    const sourceSearchText = this.getView().getModel().getProperty("/sourceSearchText");
    const targetSearchText = this.getView().getModel().getProperty("/targetSearchText");
    const aFilter = [];
    if (sourceSearchText) {
      aFilter.push(
        new sap.ui.model.Filter("sourceObject/name", sap.ui.model.FilterOperator.Contains, sourceSearchText)
      );
    }
    if (targetSearchText) {
      aFilter.push(
        new sap.ui.model.Filter("targetObject/name", sap.ui.model.FilterOperator.Contains, targetSearchText)
      );
    }
    const oList = this.byId("replicationFlowMainTable") as sap.m.List;
    const oBinding = oList.getBinding("items") as sap.ui.model.ListBinding;
    oBinding.filter(aFilter);
  }

  /**
   * opens opopover for source dataset
   *
   * @param {*} evt
   * @memberof RFBuilderClass
   */
  private onShowSourceInfoPopover(evt): void {
    const oButton = evt.getSource(),
      oView = this.getView();
    const path = evt.getSource().getBindingContext("galileiUiModel").getPath();
    if (!this.sourceInfoPopover) {
      const fragmentId = require("../view/fragment/DatasetInfoPopover.fragment.xml");
      this.sourceInfoPopover = sap.ui.xmlfragment(
        this.createId("sourceInfoPopover"),
        fragmentId,
        this
      ) as sap.m.Popover;
      oView.addDependent(this.sourceInfoPopover);
    }
    this.sourceInfoPopover.bindElement(`galileiUiModel>${path}`);
    this.sourceInfoPopover.openBy(oButton, false);
  }

  /**
   * remove replication objects
   *
   * @param {*} evt
   * @memberof RFBuilderClass
   */
  private onRemoveReplicationTask(evt): void {
    const oTask = evt.getSource().getBindingContext("galileiUiModel").getObject();
    sap.m.MessageBox.show(this.getText("confirmRemoveReplicationObject"), {
      icon: sap.m.MessageBox.Icon.WARNING,
      title: this.getText("deleteHeader"),
      actions: [sap.m.MessageBox.Action.DELETE, sap.m.MessageBox.Action.CANCEL],
      emphasizedAction: sap.m.MessageBox.Action.DELETE,
      initialFocus: sap.m.MessageBox.Action.DELETE,
      onClose: (action: sap.m.MessageBox.Action) => {
        if (action === sap.m.MessageBox.Action.DELETE) {
          oTask.deleteObject();
          this.modelBuilder.validateModel();
          this.getView().getModel("galileiUiModel").refresh(true);
          this.onRemoveAllSelection();
        }
      },
    });
  }

  private removeTaskFromPropPanelAfterSchemaChange(evt, galileiModel?: any): void {
    if (galileiModel) {
      let task = galileiModel;
      task.deleteObject();
      this.modelBuilder.validateModel();
      this.getView().getModel("galileiUiModel").refresh(true);
      this.notifyPropertyPanel(this.aSelectedReplicationTask);
    }
  }
  /**
   * do one way binding and update the loadtype select box
   *
   * @private
   * @param {*} evt
   * @return {*}  {Promise<void>}
   * @memberof RFBuilderClass
   */
  private async onLoadTypeChange(evt): Promise<void> {
    const galileiUiModel = this.getView().getModel("galileiUiModel");
    const connectionId = galileiUiModel.getProperty("/targetSystems/0/connectionId");
    const connectionType = galileiUiModel.getProperty("/targetSystems/0/connectionType");
    const oSourceSystem = galileiUiModel.getProperty("/sourceSystems/0");
    const sourceElement = evt.getSource() as sap.m.Select;
    const oTask = sourceElement.getBindingContext("galileiUiModel").getObject();
    const changedLoadType = sourceElement.getSelectedKey();
    const spaceName = this.spaceName;

    // only for dwc target default to delta-enabled target (if new targets)
    if (
      Utils.isConnectionTypeDWC(connectionId, connectionType) &&
      (changedLoadType === Constants.LOAD_TYPE.INITIALDELTA || changedLoadType === Constants.LOAD_TYPE.DELTA) &&
      oTask.targetObject?.isNew === true &&
      oTask.targetObject?.isCDCDataset === false
    ) {
      // check table(or any related artifact) doesn't exist in repo
      const isUniqueNewTableName = await Utils.validateNewDeltaTableName(spaceName, oTask.targetObject.name);
      this.modelBuilder.oResource.applyUndoableAction(() => {
        oTask.loadType = changedLoadType;
        this.modelBuilder.addCDCAttributes(oTask.targetObject);
        oTask.targetObject.isUniqueTechnicalName = isUniqueNewTableName;
      }, "change loadtype and add cdc columns");
      sap.m.MessageToast.show(this.localizeText("targetCDCColumnAdded"), {
        onClose: () => {
          if (Utils.isDeltaPartitionEnabled(oSourceSystem)) {
            sap.m.MessageToast.show(this.localizeText("deltaPartitionEnable"));
          }
        },
      });
    } else if (
      Utils.isDeltaPartitionEnabled(oSourceSystem) &&
      (changedLoadType === Constants.LOAD_TYPE.INITIALDELTA || changedLoadType === Constants.LOAD_TYPE.DELTA)
    ) {
      oTask.loadType = changedLoadType;
      sap.m.MessageToast.show(this.localizeText("deltaPartitionEnable"));
    } else {
      oTask.loadType = changedLoadType;
    }
    oTask.loadTypeMetadata.isLoadTypeChanged = true;
    oTask.validate();
    this.refreshPropertyPanelColumns(oTask);
    if (changedLoadType === Constants.LOAD_TYPE.INITIALDELTA) {
      this.setDefaultValueForDeltaInterval();
    }
    if (!this.aSelectedReplicationTask[0]) {
      sap.ui.getCore().getEventBus().publish("propertyPanel", "validateReplicationFlowPanel");
    }
  }

  /**
   * update truncate
   *
   * @param {*} evt
   * @memberof RFBuilderClass
   */
  private onTruncateBoxChange(evt): void {
    const sourceElement = evt.getSource() as sap.m.CheckBox;
    const oTask = sourceElement.getBindingContext("galileiUiModel").getObject();
    oTask.truncate = sourceElement.getSelected();
    this.modelBuilder.validateModel();
  }

  /**
   * open transformation dialog for edit
   *
   * @param {*} evt
   * @memberof RFBuilderClass
   */
  private onTransformationPress(evt): void {
    const path = evt.getSource().getBindingContext("galileiUiModel").getPath();
    const galileiModel = this.getView().getModel("galileiUiModel");
    let taskDetails: sap.cdw.replicationflow.Task;
    if (path) {
      taskDetails = galileiModel.getProperty(path);
    } else {
      taskDetails = this.aSelectedReplicationTask[0];
    }
    this.openRFTransformationDialog(taskDetails);
  }

  /**
   * delete transformation object
   *
   * @param {*} evt
   * @memberof RFBuilderClass
   */
  private onTransformationDelete(evt): void {
    const task = evt.getSource().getBindingContext("galileiUiModel").getObject();
    this.deleteTransformation(task);
  }

  private deleteTransformation(oTask): void {
    sap.m.MessageBox.show(this.getText("confirmRemoveTransformObject", [oTask.transform.name]), {
      icon: sap.m.MessageBox.Icon.WARNING,
      title: this.getText("deleteHeader"),
      actions: [sap.m.MessageBox.Action.DELETE, sap.m.MessageBox.Action.CANCEL],
      emphasizedAction: sap.m.MessageBox.Action.DELETE,
      initialFocus: sap.m.MessageBox.Action.DELETE,
      onClose: (action: sap.m.MessageBox.Action) => {
        if (action === sap.m.MessageBox.Action.DELETE) {
          this.oResource.applyUndoableAction(() => {
            if (oTask.targetObject.isNew) {
              const attributesArr = oTask.targetObject.attributes.toArray();
              for (const oAttr of attributesArr) {
                oAttr.deleteObject();
              }
              oTask.transform.deleteObject();
              const proposedEntities = this.getProposedAttributesAndTransformationForNewtarget(
                oTask,
                oTask.targetSystem.connectionId,
                oTask.targetSystem.connectionType,
                false
              );
              this.modelBuilder.createAttributes(proposedEntities.attrList, oTask.targetObject);
              if (oTask.targetObject.isCDCDataset) {
                this.modelBuilder.addCDCAttributes(oTask.targetObject);
              }
            } else {
              oTask.transform.deleteObject();
            }
            oTask.validate();
          }, "Transorm Deleted");
          this.refreshPropertyPanelColumns(oTask);
          this.enableOrDisableRFTransformation("delete");
          this.changeLabelInToolbarProjectionButton(this.aSelectedReplicationTask[0]);
          this.getView().getModel("galileiUiModel").refresh(true);
        }
      },
    });
  }

  /**
   *
   * @private
   * @param {string} action
   * @memberof RFBuilderClass
   */
  private enableOrDisableRFTransformation(action: string): void {
    if (this.aSelectedReplicationTask.length > 0) {
      if (action === "delete" && this.isEditable) {
        this.getWorkbenchController().workbenchModel.setProperty("/toolbar/isRFAddTransformationEnabled", true);
      } else if (
        this.isEditable &&
        action === "select" &&
        this.getWorkbenchController().workbenchModel &&
        this.aSelectedReplicationTask[0].targetObject
      ) {
        this.getWorkbenchController().workbenchModel.setProperty("/toolbar/isRFAddTransformationEnabled", true);
      } else {
        this.getWorkbenchController().workbenchModel.setProperty("/toolbar/isRFAddTransformationEnabled", false);
      }
    } else {
      this.getWorkbenchController().workbenchModel.setProperty("/toolbar/isRFAddTransformationEnabled", false);
    }
  }

  /**
   * getting consumed by the deleteTransformation event
   * @memberof RFBuilderClass
   */
  private handleTransformationDeleteFromPanel(channelId, eventId, dataObject) {
    this.deleteTransformation(dataObject);
  }

  /**
   * getting consumed by the schemaChange event
   * @param {*} updatedSchemaTasks
   * @param {*} evtResponse
   * @returns {object}
   * @memberof RFBuilderClass
   */
  getUpdatedSchemaChangeProperties(updatedSchemaTasks, evtResponse) {
    const sConnId = updatedSchemaTasks.sourceSystem.connectionId;
    const sConnType = updatedSchemaTasks.sourceSystem.connectionType;
    if (Utils.isConnectionTypeDwcHANA(sConnId, sConnType)) {
      return evtResponse.oldTask.datasetProperties;
    } else if (Utils.isConnectionTypeHANA(sConnId, sConnType)) {
      return undefined;
    } else {
      return Object.assign(
        updatedSchemaTasks.sourceObject.datasetProperties,
        evtResponse.schemaChangeResults.sourceConfiguration
      );
    }
  }

  private updateTaskAFterSchemaChange(event, eventId, evtResponse) {
    //remove task
    const updatedSchemaTasks: sap.cdw.replicationflow.Task = this.getModel()
      .replicationTasks.toArray()
      .filter((t) => t.name === evtResponse.panelGalilieModel.name)[0];
    const targetObjectPrev = {
      name: updatedSchemaTasks?.targetObject?.name,
      businessName: updatedSchemaTasks?.targetObject?.businessName,
    };
    const dataset = {
      attributes: evtResponse.schemaChangeResults.aggregatedAttr,
      businessName: evtResponse.oldTask.businessName,
      metadata: {
        invalidColumns: evtResponse.oldTask.invalidColumns,
        filePath: evtResponse.oldTask.filePath,
        includeNotExpandedArraysAndMaps:
          evtResponse.schemaChangeResults.sourceConfiguration?.includeNotExpandedArraysAndMaps,
        includeTechnicalKey: evtResponse.schemaChangeResults.sourceConfiguration?.includeTechnicalKey,
        confluentReadExpandedArray: evtResponse.schemaChangeResults.sourceConfiguration?.confluentReadExpandedArray,
        confluentReadExpandedMap: evtResponse.schemaChangeResults.sourceConfiguration?.confluentReadExpandedMap,
        type: evtResponse.oldTask.type,
        isDeltaDisabled: evtResponse.oldTask.isDeltaDisabled,
      },
      properties: this.getUpdatedSchemaChangeProperties(updatedSchemaTasks, evtResponse),
      name: evtResponse.oldTask.name,
    };
    //create source dataset
    this.modelBuilder.createDataset(dataset, "source", updatedSchemaTasks);

    //update target only if schema has changed and it is a new target
    if (evtResponse.schemaChangeResults.propertyChanged) {
      //delete transformObject
      updatedSchemaTasks?.transform?.deleteObject();

      if (updatedSchemaTasks?.targetObject?.isNew) {
        //delete target object
        updatedSchemaTasks.targetObject.deleteObject();

        //create new target object
        this.updateTasksWithTargetData([updatedSchemaTasks]);
        if (updatedSchemaTasks.targetObject.name !== targetObjectPrev.name) {
          updatedSchemaTasks.targetObject.name = targetObjectPrev.name;
        }
        if (updatedSchemaTasks.targetObject.businessName !== targetObjectPrev.businessName) {
          updatedSchemaTasks.targetObject.businessName = targetObjectPrev.businessName;
        }

        //Retain old target settings
        this.modelBuilder.createSystemProperties(
          evtResponse.panelGalilieModel.targetObject.datasetProperties,
          undefined,
          updatedSchemaTasks.targetObject
        );
      }
    } else if (evtResponse.schemaChangeResults.primaryKeyChange && updatedSchemaTasks?.targetObject?.isNew) {
      this.updateTargetPrimaryKey(
        updatedSchemaTasks.sourceObject,
        updatedSchemaTasks.targetObject,
        updatedSchemaTasks.transform
      );
    }
    this.aSelectedReplicationTask[0] = updatedSchemaTasks;
    this.getView().getModel("galileiUiModel").refresh(true);

    //update prop panel
    this.notifyPropertyPanel(this.aSelectedReplicationTask);
    this.refreshPropertyPanelColumns(updatedSchemaTasks);
    this.modelBuilder.validateModel();
  }

  private updateTargetPrimaryKey(
    sourceDataset: sap.cdw.replicationflow.Dataset,
    targetDataSet: sap.cdw.replicationflow.Dataset,
    transform: sap.cdw.replicationflow.Transform
  ) {
    const srcPrimaryKeyList: string[] = sourceDataset.attributes
      .filter((col) => {
        return col.key === true;
      })
      .map((col) => {
        return col.name;
      });
    targetDataSet.attributes.toArray().forEach((attr) => {
      attr.key = false;
    });
    const transformArray = transform?.attributeMappings?.toArray();
    srcPrimaryKeyList.forEach((name) => {
      let mappedColumnObj;
      if (transformArray) {
        mappedColumnObj = transformArray.find((m) => m.source?.name === name);
      }
      if (!mappedColumnObj) {
        mappedColumnObj = { target: { name } };
      }
      const pktobeUpdated = targetDataSet.attributes.filter((attr) => {
        return attr.name === mappedColumnObj.target.name;
      });
      pktobeUpdated[0].key = true;
    });
  }

  /**
   * Changes the Label of Projection Button in Toolbar to Edit Projection if Selected Dataset has Projections
   */
  private changeLabelInToolbarProjectionButton(aSelectedReplicationTask: any): void {
    if (aSelectedReplicationTask.length > 0) {
      if (
        this.getWorkbenchController().workbenchModel &&
        aSelectedReplicationTask[0].transform &&
        aSelectedReplicationTask[0].targetObject
      ) {
        this.getWorkbenchController().workbenchModel.setProperty("/toolbar/projectionExistsInSelectedDataset", true);
      } else {
        this.getWorkbenchController().workbenchModel.setProperty("/toolbar/projectionExistsInSelectedDataset", false);
      }
    } else {
      this.getWorkbenchController().workbenchModel.setProperty("/toolbar/projectionExistsInSelectedDataset", false);
    }
  }

  /**
   * open rename dialog for target dataset
   * @param {*} evt
   * @memberof RFBuilderClass
   */
  private onRenameTargetDataset(evt): void {
    const btnControl = this.getView().byId(evt.getParameter("id")) as sap.m.Button;
    const path = btnControl.getBindingContext("galileiUiModel").getPath();
    const oTask = this.getView().getModel("galileiUiModel").getProperty(path);
    const oTargetRename = this.getView().getModel("TargetRenameModel");
    const aSplittedEntityPath = oTask.targetObject.name.split("/");
    const sEntityName = aSplittedEntityPath[aSplittedEntityPath.length - 1];
    if (!oTask.targetObject.businessName) {
      oTargetRename.setProperty("/showBusinessName", false);
    } else {
      const aSplittedEntityPathBusinessName = oTask.targetObject.businessName.split("/");
      const sEntityNameBusinessName = aSplittedEntityPathBusinessName[aSplittedEntityPathBusinessName.length - 1];
      oTargetRename.setProperty("/businessNameToDisplay", sEntityNameBusinessName);
      oTargetRename.setProperty("/showBusinessName", true);
    }
    oTargetRename.setProperty("/targetName", sEntityName);
    oTargetRename.setProperty("/galiliePath", path);
    oTargetRename.setProperty("/validation/isValid", true);
    oTargetRename.setProperty("/validation/errText", "");
    oTargetRename.setProperty("/copySourceColumns", false);
    let fullPath = null;
    if (aSplittedEntityPath.length > 1) {
      const pathEndsWithEntityName = oTask.targetObject.name.endsWith(`${sEntityName}`);
      if (pathEndsWithEntityName) {
        fullPath = oTask.targetObject.name.substring(0, oTask.targetObject.name.length - sEntityName.length);
      }
    }
    oTargetRename.setProperty("/targetPath", fullPath);
    const viewName = require("../view/fragment/RenameTarget.fragment.xml");
    this.oRenameTargetDialog = sap.ui.xmlfragment("", viewName, this);
    this.getView().addDependent(this.oRenameTargetDialog);
    this.oRenameTargetDialog.open();
  }

  /**
   *Renames the target object if it does not exist ,otherwise maps to existing target
   * @memberof RFBuilderClass
   */
  private async renameTarget() {
    const oTargetRename = this.getView().getModel("TargetRenameModel");
    const galileiUiModel = this.getView().getModel("galileiUiModel");
    const oTargetRenameData = oTargetRename.getData();
    const connectionId = galileiUiModel.getProperty("/targetSystems/0/connectionId");
    const connectionType = galileiUiModel.getProperty("/targetSystems/0/connectionType");
    if (!this.validateTargetRename(oTargetRenameData.targetName, connectionId, connectionType)) {
      return;
    }

    const oTargetSystem = this.getModel().targetSystems.get(0);
    const task = this.getView().getModel("galileiUiModel").getProperty(oTargetRenameData.galiliePath);
    const aSplittedEntityPath = task.targetObject.name.split("/");
    const sEntityName = aSplittedEntityPath[aSplittedEntityPath.length - 1];
    this.onTargetRenameDialogClose();
    if (oTargetRenameData.targetName !== sEntityName) {
      const finalTargetName = oTargetRenameData.targetPath
        ? `${oTargetRenameData.targetPath}${oTargetRenameData.targetName}`
        : oTargetRenameData.targetName;
      const targetQualifiedName =
        oTargetSystem.systemContainer === "/" || oTargetRenameData.targetPath
          ? `${oTargetSystem.systemContainer}${finalTargetName}`
          : `${oTargetSystem.systemContainer}/${finalTargetName}`; // for scenario when the container is 'abc' and target is 'xyz'
      const item = {
        id: targetQualifiedName,
        name: task.targetObject.name,
        remote: { format: "dis", remoteSource: connectionId, uniqueName: targetQualifiedName },
      };
      this.setBusy(true);
      let targetDefinition = null;
      let isNewTarget = true;
      try {
        if (Utils.isConnectionTypeDWC(connectionId, connectionType)) {
          targetDefinition = await getRepositoryObject(this.spaceName, finalTargetName, { details: ["csn"] });
          if (targetDefinition) {
            if (targetDefinition["#technicalType"] === RepositoryObjectType.DWC_LOCAL_TABLE) {
              // Prevent SAC Exposed Dataset
              if (targetDefinition["#canManipulateData"] === "false") {
                const message = this.localizeText("errorForRenameToSACDataset1", ["\n", `"`]);
                MessageHandler.show(
                  sap.ui.core.MessageType.Error,
                  message,
                  undefined,
                  undefined,
                  undefined,
                  undefined,
                  undefined,
                  undefined,
                  "renameToUnsupportedDatasetFromSAC"
                );
                this.setBusy(false);
                return;
              } else {
                targetDefinition.definitions = targetDefinition.csn.definitions;
              }
            } else {
              const message = this.localizeText("errorForRenameToUnsupportedDWCDataset", ["\n"]);
              MessageHandler.show(
                sap.ui.core.MessageType.Error,
                message,
                undefined,
                undefined,
                undefined,
                undefined,
                undefined,
                undefined,
                "renameToUnsupportedDatasetFromDWCMsgBox"
              );
              this.setBusy(false);
              return;
            }
          } else {
            throw new Error();
          }
        } else if (Utils.isConnTypeObjectStore(connectionType)) {
          const path = this.getRelativeParentPath(targetQualifiedName);
          const targetConnId = connectionId;
          const itemsResp: any = await APIService.getChildren(path, this.spaceName, targetConnId);
          isNewTarget = !this.isFileExistsInsideObjectStoreNode(itemsResp, targetQualifiedName);
        } else if (Utils.isConnectionTypeSignavio(connectionType)) {
          targetDefinition = null;
        } else {
          targetDefinition = await this.getRemoteMetadata(item);
        }
      } catch (e) {
        targetDefinition = null;
      }
      if (targetDefinition) {
        this.updateTaskwithTargetData(connectionId, connectionType, [targetDefinition], task, null, true);
      } else {
        await this.updateTaskwithoutTargetData(
          connectionId,
          connectionType,
          task,
          finalTargetName,
          isNewTarget,
          oTargetRenameData.copySourceColumns
        );
      }
      this.modelBuilder.validateModel();
      this.refreshPropertyPanelColumns(task);
      this.setBusy(false);
    }
  }

  /**
   *
   * @private
   * @param {string} connectionId
   * @param {string} connectionType
   * @param {sap.cdw.replicationflow.Task} task
   * @param {string} newTargetObjectName
   * @param {boolean} [isNewTarget=true]
   * @return {*}  {Promise<void>}
   * @memberof RFBuilderClass
   */
  private async updateTaskwithoutTargetData(
    connectionId: string,
    connectionType: string,
    task: sap.cdw.replicationflow.Task,
    newTargetObjectName: string,
    isNewTarget: boolean = true,
    copySourceColumns?: boolean
  ): Promise<void> {
    const galileiUiModel = this.getView().getModel("galileiUiModel");
    const sourceConnectionType = galileiUiModel.getProperty("/sourceSystems/0/connectionType");
    const sourceContainer = galileiUiModel.getProperty("/sourceSystems/0/systemContainer");

    let isUniqueNewTableName = true;
    if (Utils.isConnectionTypeDWC(connectionId, connectionType) && task.targetObject.isCDCDataset) {
      isUniqueNewTableName = await Utils.validateNewDeltaTableName(this.spaceName, newTargetObjectName);
    }
    this.oResource.applyUndoableAction(() => {
      task.targetObject.isNew = isNewTarget;
      task.targetObject.name = newTargetObjectName;
      task.targetObject.metadata["isDPIDDataset"] = false;
      if (copySourceColumns) {
        task.targetObject.attributes.forEach((attribute: any) => {
          attribute.deleteObject();
        });
        if (task.transform) {
          task.transform.deleteObject();
        }
        const proposedEntities = this.getProposedAttributesAndTransformationForNewtarget(
          task,
          task.targetSystem.connectionId,
          task.targetSystem.connectionType,
          false
        );
        this.modelBuilder.createAttributes(proposedEntities.attrList, task.targetObject);
        if (task.targetObject.isCDCDataset) {
          this.modelBuilder.addCDCAttributes(task.targetObject);
        }
        this.addAutoProjection(task, proposedEntities.transformObj);
      }
      if (Utils.isConnectionTypeDWC(connectionId, connectionType)) {
        task.targetObject.isSupportedDataset = true;
        task.targetObject.metadata["isSACArtefact"] = false;
        task.targetObject.businessName = newTargetObjectName;
        task.targetObject.setObjectStatus(undefined);
        task.targetObject.isUniqueTechnicalName = isUniqueNewTableName;
      }
      if (Utils.isConnectionTypeGBQ(connectionType)) {
        // Sometimes existing target attributes may not contain CDC columns or may not be in order so removing and adding them again.
        this.modelBuilder.removeCDCAttributes(task.targetObject);
        this.modelBuilder.addCDCAttributes(task.targetObject);
      }
      // Remove primary key in target if present and Add DPID Column for keyless ABAP Sources when target is new again
      if (
        Utils.isDatasetABAPSourceWithoutKey(
          sourceConnectionType,
          sourceContainer,
          task.sourceObject.metadata["isSourceWithoutPK"]
        ) &&
        !Utils.isConnectionTypeKafka(connectionType)
      ) {
        this.modelBuilder.removeKeyAndDPIDAttributes(task.targetObject, connectionType);
        if (task.targetObject.isNew) {
          this.modelBuilder.addDPIDAttributes(task.targetObject, connectionId, connectionType);
          Utils.showToastForDPIDColumn(1);
        }
      }
    }, "new target object");
    if (task.transform) {
      for (const mapping of task.transform.attributeMappings?.toArray()) {
        // when expression is there , source is not present
        if (mapping.source?.name === Constants.SKIP_MAPPING) {
          mapping.deleteObject();
        }
      }
      sap.m.MessageToast.show(this.localizeText("attributeMappingRemovalTxt"));
    }
  }

  /**
   * To get the relative path
   * @memberof RFBuilderClass
   * @param {string} fullPath
   * @returns {string}
   */
  getRelativeParentPath(fullPath: string): string {
    const pathList: string[] = fullPath.trim().split("/");
    if (pathList.length > 2) {
      pathList.pop();
    } else {
      return "/";
    }
    return pathList.join("/"); // returns /abc/xyz/p.csv -> /abc/xyz
  }

  /**
   * To get the full file path
   * @memberof RFBuilderClass
   * @param {string} selectedContainerPath
   * @param {object} task
   * @returns {string}
   */
  getFilePath(selectedContainerPath, task) {
    // ex: Assuming previous file path in selected task was /abc/xyz/p1.csv
    // below code will split into array ["abc","xyz","p1.csv"] and set the last index of array as sEntityName [p1.csv]
    const aSplittedEntityPath = task.targetObject.name.split("/");
    const sEntityName = aSplittedEntityPath.pop();
    return `${selectedContainerPath}/${sEntityName}`; // /abc/def/p1.csv
  }

  /**
   * Check if file exists in OS node
   * @memberof RFBuilderClass
   * @param {Array} nodeList
   * @param {string} filePath
   * @returns {boolean}
   */
  isFileExistsInsideObjectStoreNode(nodeList: any[], filePath: string): boolean {
    if (!Array.isArray(nodeList)) {
      return false;
    }
    const containerList: any[] = nodeList.filter((node) => node.id === filePath);
    return containerList.length ? true : false;
  }

  /**
   * Triggered when there is rename button clicked
   * @memberof RFBuilderClass
   */
  private onTargetRename(): void {
    this.renameTarget();
  }

  /**
   * validates rename field for empty and special char
   * @param  {string} value
   * @returns {boolean}
   */
  validateTargetRename(value: string, connectionId: string, connectionType: string): boolean {
    let isValid = true;
    const isDwcConn = Utils.isConnectionTypeDWC(connectionId, connectionType);
    const firstUnderscorePattern = /^_+/g;
    const maxLen = isDwcConn ? 50 : 249;
    const oTargetRename = this.getView().getModel("TargetRenameModel");
    let specialCharPattern;
    if (isDwcConn) {
      if (SupportedFeaturesService.getInstance().isDotSupportEnabledFF() && hasNamespace()) {
        specialCharPattern = /(?:^\W)|[^\w.]|(?:\W$)/g;
      } else {
        specialCharPattern = /[^a-zA-Z0-9_]/g;
      }
    } else {
      specialCharPattern = /[~`@!#$%^&*()+={}\[\]\\|;:'"<>,\?\/\s]/g; // For connections other than dwc type ,other languages,underscore,dot and hyphen are allowed.
    }
    oTargetRename.setProperty("/maxLen", maxLen);
    if (!value) {
      isValid = false;
      oTargetRename.setProperty("/validation/isValid", isValid);
      oTargetRename.setProperty("/validation/errText", this.localizeText("mandatoryTargetName"));
      return isValid;
    } else if (isDwcConn && firstUnderscorePattern.exec(value)) {
      isValid = false;
      oTargetRename.setProperty("/validation/isValid", isValid);
      oTargetRename.setProperty("/validation/errText", this.localizeText("firstUnderscorePattern"));
    } else if (specialCharPattern.exec(value)) {
      isValid = false;
      let errMsg;
      if (isDwcConn) {
        if (SupportedFeaturesService.getInstance().isDotSupportEnabledFF() && hasNamespace()) {
          errMsg = "dwcWithDot";
        } else {
          errMsg = "dwcSpecialChar";
        }
      } else {
        errMsg = "nonDwcSpecialChar";
      }
      oTargetRename.setProperty("/validation/isValid", isValid);
      oTargetRename.setProperty("/validation/errText", this.localizeText(errMsg));
      return isValid;
    } else {
      isValid = true;
      oTargetRename.setProperty("/validation/isValid", isValid);
      oTargetRename.setProperty("/validation/errText", "");
    }
    return isValid;
  }

  /**
   * Triggered when there is change in rename field
   * @param {*} evt
   * @memberof RFBuilderClass
   */
  private onTargetRenamechage(evt): void {
    const galileiUiModel = this.getView().getModel("galileiUiModel");
    const connectionId = galileiUiModel.getProperty("/targetSystems/0/connectionId");
    const connectionType = galileiUiModel.getProperty("/targetSystems/0/connectionType");

    const isValid = this.validateTargetRename(evt.getSource().getValue(), connectionId, connectionType);
    const renameBtn = sap.ui.getCore().byId("renameDialogRenameBtn");
    renameBtn.setEnabled(isValid);
  }
  /**
   * @memberof RFBuilderClass
   */
  private onTargetRenameDialogClose(): void {
    this.oRenameTargetDialog.close();
    this.oRenameTargetDialog.destroy();
  }

  /**
   * This function trigger's change of target object of a RF task(by changing file/table/view name)
   * and updating the Target dataset/object of that RF task with new values.
   *
   * @param {*} evt
   * @memberof RFBuilderClass
   */
  private async onBrowseMapToExistingTarget(evt) {
    const btnControl = this.getView().byId(evt.getParameter("id")) as sap.m.Button;
    const path = btnControl.getBindingContext("galileiUiModel").getPath();
    this.oLastContextMenuClickedReplicationTask = this.getView().getModel("galileiUiModel").getProperty(path);
    this.currentDatasetBrowserType = "target";
    const connectionId: string = this.getView().getModel("galileiUiModel").getProperty("/targetSystems/0/connectionId");
    const connectionType: string = this.getView()
      .getModel("galileiUiModel")
      .getProperty("/targetSystems/0/connectionType");

    if (Utils.isConnectionTypeDWC(connectionId, connectionType)) {
      const filters = {
        "#technicalType": Constants.SUPPORTED_TECHNICAL_TYPES_TARGET,
        "@DataWarehouse.external.schema": ["<NULL>"],
      };

      // As #isToolingHidden is a new computed property older objects will have NULL values.
      // Filtering with #isToolingHidden = false will miss those older objects in the resultset.
      // Therefore if one is passing the filter with false we extend the filter by to also retrieve the older objects.
      filters["#isToolingHidden"] = [false, "<NULL>"];

      // Open Local DWC Browser
      this.openLocalRepositoryBrowser({
        filters: filters,
        isSingleSelectionAllowed: true,
      });
      // After selection from DWC browser control goes to "onRepositoryObjectsSelected(aSelectedObjects)"
    } else {
      // Open Remote Browser
      const aSelectedTargets = await this.openRemoteTargetBrowser();
      if (aSelectedTargets?.length) {
        this.updateTaskwithTargetData(
          connectionId,
          connectionType,
          aSelectedTargets,
          this.oLastContextMenuClickedReplicationTask
        );
        this.modelBuilder.validateModel();
        this.refreshPropertyPanelColumns(this.oLastContextMenuClickedReplicationTask);
      }
    }
  }

  /**
   * This function updates RF task with a new target dataset/object
   * and delete the stale or obsolete transformations (if exists)
   *
   * @param {string} connectionId
   * @param {string} connectionType
   * @param {Array} aSelectedObjects
   * @param {Object} oSelectedTask
   * @param {Object} oDialogModel
   * @param {boolean} isTargetRenamed
   * @return void
   * @memberof RFBuilderClass
   */
  private updateTaskwithTargetData(
    connectionId,
    connectionType,
    aSelectedObjects,
    oSelectedTask,
    oDialogModel = null,
    isTargetRenamed = null
  ) {
    const targetDefinition = aSelectedObjects[0];
    // refactor possible inorder to avoid .ts errors on targetData while removing ":any"

    // getting target data in usable format
    const targetData: any = this.parseMetadata(connectionId, connectionType, targetDefinition);
    const isTransformPresent = oSelectedTask?.transform ? true : false;
    const targetContainer = this.getModel().targetSystems.get(0).systemContainer;
    if (
      this.canUpdateTargetDataInReplication(connectionId, connectionType, targetData, oSelectedTask, isTargetRenamed)
    ) {
      this.oResource.applyUndoableAction(() => {
        const targetDataset = {
          name: oDialogModel?.isPathContainerClicked
            ? targetData.qualifiedName.replace(targetContainer, "")
            : targetData.name, // name = targetData.qualifiedName - this.getModel().container  (/abc/def/p1.csv - /abc)
          metadata: {
            isNew: false,
            invalidColumns: targetData.invalidColumns,
            isSACArtefact: targetData.isSACArtefact,
          }, // for(Replication Task Context Menu items)MapToExisting and Container Path(if Target Found) isNew = false
          attributes: targetData.columns,
          businessName: targetData.businessName,
          // invalid columns and send value
        };
        if (oSelectedTask?.targetObject) {
          oSelectedTask?.targetObject.deleteObject();
        } // if stale Target Object exits on task - delete
        if (oSelectedTask?.transform) {
          oSelectedTask.transform.deleteObject();
        } // if Transformation exits on task - delete
        this.modelBuilder.createDataset(targetDataset, "target", oSelectedTask);
        // update object status for DWC target
        if (Utils.isConnectionTypeDWC(connectionId, connectionType) && oSelectedTask.targetObject) {
          oSelectedTask.targetObject.setObjectStatus(targetDefinition["#objectStatus"]);
        }
      }, "update target dataset");
      if (isTransformPresent) {
        this.openRFTransformationDialog(oSelectedTask);
      }
    }
  }

  /**
   *
   *
   * @private
   * @param {string} connectionId
   * @param {string} connectionType
   * @param {(IDwcMetadata | IFlowagentMetadata)} targetData
   * @param {sap.cdw.replicationflow.Task} oTask
   * @param {boolean} isTargetRenamed
   * @return {*}  {boolean}
   * @memberof RFBuilderClass
   */
  private canUpdateTargetDataInReplication(
    connectionId: string,
    connectionType: string,
    targetData: IDwcMetadata | IFlowagentMetadata,
    oTask: sap.cdw.replicationflow.Task,
    isTargetRenamed = null
  ): boolean {
    if (Utils.isConnectionTypeDWC(connectionId, connectionType)) {
      if (
        "isCDCDataset" in targetData &&
        targetData.isCDCDataset &&
        !(oTask.loadTypeMetadata.isInitialAndDeltaSupported || oTask.loadTypeMetadata.isDeltaOnlySupported)
      ) {
        let sErrorMsg = null;
        let sErrorMsgBoxId = null;
        if (isTargetRenamed) {
          sErrorMsg = this.localizeText("sourceObjectNonDeltaSupportErrorMsgForRenamedTarget", ["\n", "\n\n"]);
          sErrorMsgBoxId = "sourceObjectNonDeltaSupportEditorErrorMsgBoxForRemanedTarget";
        } else {
          sErrorMsg = this.localizeText("sourceObjectNonDeltaSupportErrorMsg", ["\n", "\n\n"]);
          sErrorMsgBoxId = "sourceObjectNonDeltaSupportEditorErrorMsgBox";
        }
        MessageHandler.uiError(sErrorMsg, null, null, null, null, null, null, sErrorMsgBoxId);
        return false;
      }
    }
    if (Utils.isConnectionTypeGBQ(connectionType) && !RFBuilderUtils.isValidaGBQCDCColmns(targetData.columns)) {
      MessageHandler.uiError(
        this.localizeText("targetObjExistingNoCDCColumnUpdated", ["\n", "•"]),
        null,
        null,
        null,
        null,
        null,
        null,
        "TargetObjectNoCDC"
      );
      return false;
    }
    return true;
  }

  /**
   * open path container dialog
   *
   * @param {*} evt
   * @return {void}
   * @memberof RFBuilderClass
   */
  private onBrowsePathContainer(evt): void {
    const btnControl = this.getView().byId(evt.getParameter("id")) as sap.m.Button;
    const path = btnControl.getBindingContext("galileiUiModel").getPath();
    this.oLastContextMenuClickedReplicationTask = this.getView().getModel("galileiUiModel").getProperty(path);
    const oTargetSystem = this.getModel().targetSystems.get(0);
    const oContainerTreeModel = this.getView().getModel("containerTreeModel");

    // @todo aditya: please refactor to reset these oContainerOwnModel properties at one place using resetContainerDialogOwnModel()
    const oContainerOwnModel = {
      isSourceClicked: false,
      isTargetClicked: false,
      title: this.localizeText("targetContainernPathText"),
      containerPath: oTargetSystem.systemContainer,
      connection: oTargetSystem.connectionId,
      connectionType: oTargetSystem.connectionType,
      messageStrip: {
        text: "",
        visible: false,
      },
      isPathContainerClicked: true,
      sourceHdlfDeltaShareProperties: {
        displayConnectionTypeForHDLF: "", // For HDLF , actual connection ($DWC HANA) and display name (HDL_FILES))
        displayConnectionIdForHDLF: "",
        containerPathHDLFDeltashare: "",
        isSourceHDLFDeltashare: false,
        isHDLFSchemaSelected: false,
      },
      isFolderSelection: false,
    };
    this.openContainerDialog(oContainerTreeModel, oContainerOwnModel);
  }

  public async openRFTransformationDialog(taskDetails?: sap.cdw.replicationflow.Task) {
    taskDetails = taskDetails ? taskDetails : this.aSelectedReplicationTask[0] ?? undefined;
    const oModel: sap.cdw.replicationflow.Model = this.getModel();
    const oGalileiUiModel = this.getView().getModel("galileiUiModel");
    const bHasSkipMappingCapability = oGalileiUiModel.getProperty("/replicationTaskSetting/hasSkipMappingCapability");
    const objectStatus = oModel["#objectStatus"];
    const privilege = this.getHasPrivileges();
    const oFeatures = this.oFeatures;
    if (!taskDetails) {
      return;
    }
    await showDialog(
      require("../view/AddTransformationDialog.view.xml"),
      this.localizeText("txtTransformationHeader"),
      // For readonly mode ok button is invisible
      privilege ? this.localizeText("txtOK") : "",
      this.localizeText("txtCancel"),
      {},
      "100%",
      "auto",
      null,
      { taskDetails, privilege, objectStatus, oFeatures, bHasSkipMappingCapability },
      null
    )
      .then((result) => {
        const task = taskDetails;

        if (result.isTransformChanged) {
          this.oResource.applyUndoableAction(() => {
            if (result.isAttrChanged) {
              const attributesArr = task.targetObject.attributes.toArray();
              for (const oAttr of attributesArr) {
                oAttr.deleteObject();
              }
              this.modelBuilder.createAttributes(result.aggregatedAttr, task.targetObject);
            }
            if (result.isMappingChanged || result.isAttrChanged || result.isFilterChanged) {
              task.transform?.deleteObject();
              this.modelBuilder.createTransform(result.transformObj, task);
            }

            if (task.transform) {
              task.transform.name = result.transformDetails.name;
            }
            this.modelBuilder.validateModel();
          }, "Transofrm Updated");
          this.getWorkbenchController().workbenchModel.setProperty("/toolbar/isRFAddTransformationEnabled", true);
          this.getWorkbenchController().workbenchModel.setProperty("/toolbar/projectionExistsInSelectedDataset", true);

          this.refreshPropertyPanelColumns(task);
          this.getView().getModel("galileiUiModel").refresh(true);
          this.getView().getModel("galileiModel").refresh(true);
        }
      })
      .catch(() => {
        // cancel
      })
      .finally(() => {
        // dialog closed
      });
  }

  /**
   * open transformation dialog
   * @memberof RFBuilderClass
   */
  public openTransformationDialog() {
    const taskDetails: sap.cdw.replicationflow.Task = this.aSelectedReplicationTask[0] ?? undefined;
    this.openRFTransformationDialog(taskDetails);
  }

  public navToMonitoringView() {
    const galileiModel = this.getModel();
    const params = {
      objectId: galileiModel.name,
      spaceId: this.getSpaceName(),
    };
    const semanticObject = "dataintegration";
    const targetParameter = "routeTo";
    params[targetParameter] = "replicationFlowMonitorDetails";
    ShellNavigationService.toExternal({
      target: {
        semanticObject: semanticObject,
        action: "",
      },
      params,
    });
  }

  public hasEmailEditPrivilege(): boolean {
    const isSDPEnabled = sap.ui.getCore().getModel("featureflags").getProperty("/DWC_DUMMY_SPACE_PERMISSIONS");
    let privileges;
    if (isSDPEnabled) {
      privileges = sap.ui.getCore().getModel("privilege").getProperty("/DWC_DATAINTEGRATION");
    } else {
      privileges = ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_DATAINTEGRATION");
    }
    if (privileges && privileges.update) {
      return true;
    } else {
      return false;
    }
  }

  public async openEmailNotificationDialog() {
    const width = "33%";
    const height = "auto";
    const galileiModel = this.getModel();
    const oBundle = this.getView().getModel("i18n").getResourceBundle();
    const hasPrivilege = this.hasEmailEditPrivilege() && this.isEditable;
    const isSpaceLocked =
      this.getWorkbenchController().workbenchModel.getProperty("/spaceAccessInfo/status") === "locked";
    const spaceId = this.getSpaceName();
    const objectId = galileiModel.name;

    const params = {
      objectId,
      spaceId,
      hasPrivilege,
      isSpaceLocked,
    };

    try {
      const payload = await showDialog(
        require("../../flowmonitor/view/RFEmailNotification.view.xml"),
        this.i18nResourceModel.getProperty("@RuntimeEmailNotification"),
        this.i18nResourceModel.getProperty("@TXT_SAVE"),
        this.i18nResourceModel.getProperty("@cancel"),
        {},
        width,
        height,
        null,
        { params },
        null,
        null,
        "emailNotificationDialog"
      );

      await APIService.updateNotificationInfo(spaceId, objectId, payload);

      const succMsg = oBundle.getText("@EmailNotificationSuccess");
      sap.m.MessageToast.show(succMsg);
    } catch (err) {
      if (err) {
        const errMsg = oBundle.getText("@emailUpdateError");
        MessageHandler.exception({ exception: err, message: errMsg });
      }
    }
  }

  /**
   * This function is to used to toggle loader
   * @param isBusy
   * @param sTitle
   * @memberof RFBuilderClass
   */
  public setBusy(isBusy: boolean, sTitle?: string) {
    this.getView().setBusy(isBusy);
  }

  /**
   * This function verifies if any replication task exists
   * @returns {boolean}
   * @memberof RFBuilderClass
   */
  private hasReplicationTask() {
    return this.getModel().replicationTasks?.toArray().length > 0 ? true : false;
  }

  /**
   * This function checks if atleast one replication in RF has targetobject
   * @private
   * @return {*}  {boolean}
   * @memberof RFBuilderClass
   */
  private hasReplicationTaskWithTargetObject(): boolean {
    const aReplicationTasks = this.getModel().replicationTasks.toArray();
    return aReplicationTasks.find((o) => o.targetObject) ? true : false;
  }

  /**
   * Browse and select connection
   * @params oEvent
   * @returns Promise
   * @memberof RFBuilderClass
   */
  private async onBrowseConnection(oEvent): Promise<any> {
    const sElementId = oEvent.getSource().getId();
    const model = this.resetConnectionDialogOwnModel();
    const oModel = this.getModel();
    const bHasReplicationTask = this.hasReplicationTask(); // to check if replication task is already present or not
    const bHasReplicationTaskWithTargetObject = this.hasReplicationTaskWithTargetObject();
    let type: string = null;

    if (
      sElementId === "replicationFlowBuilder--browseSourceConnection" ||
      sElementId === "replicationFlowBuilder--initSourceBrowseConnBtn"
    ) {
      // If Source Connection Button is clicked
      type = "source";
      model.isSourceClicked = true;
      model.title = this.localizeText("sourceConnectionEmptyText");
      if (oModel.sourceSystems.get(0)?.connectionId && bHasReplicationTask) {
        const message = this.getText("confirmRemoveReplicationTaskPrompt");
        await this.showConfirmConnectionOrContainerChange(message, type, true);
      }
    } else if (sElementId === "replicationFlowBuilder--browseTargetConnection") {
      // If Target Connection Button is clicked
      type = "target";
      model.isTargetClicked = true;
      model.title = this.localizeText("targetConnectionEmptyText");
      if (oModel.targetSystems.get(0)?.connectionId && bHasReplicationTaskWithTargetObject) {
        const message = this.getText("confirmTargetConnectionChangePrompt");
        await this.showConfirmConnectionOrContainerChange(message, type, true);
      }
    }
    const viewName = require("../view/fragment/ConnectionDialog.fragment.xml");
    this.oConnectionDialog = sap.ui.xmlfragment("", viewName, this);
    this.getView().addDependent(this.oConnectionDialog);
    this.getView().getModel("connectionDialogOwnModel").setProperty("/", model);
    this.oConnectionDialog.setBusy(true);
    this.oConnectionDialog.open();
    this.getConnections(this.spaceName, type);
  }

  /**
   * Reset connection dialog's own model
   * @returns {Object}
   * @memberof RFBuilderClass
   */
  private resetConnectionDialogOwnModel() {
    return {
      isSourceClicked: false,
      isTargetClicked: false,
      title: this.localizeText("sourceConnectionEmptyText"),
    };
  }
  /**
   * Get all remote connections
   * @param {string} spaceName
   * @param {string} type
   * @memberof RFBuilderClass
   */
  public async getConnections(spaceName: string, type: string) {
    let oResponse = [];

    try {
      const oConnectionModel = this.getView().getModel("connectionModel");
      if (oConnectionModel) {
        oConnectionModel.setProperty("/", null);
      }

      // const path = [{ id: "remotes", type: "remotes" }];
      // TODO: Requesting one of the remote source runtime properties (here it is "adapter") would retrieve all runtime properties like location, agentName, etc.
      // If remote source runtime data is not required, please remove "adapter" from the list
      oResponse = await Repo.getRemoteConnectionList(spaceName, [
        "id",
        "typeId",
        "name",
        "businessName",
        "description",
        "capabilityReplicationflowSource",
        "capabilityReplicationflowTarget",
        "adapter",
        "connectionMetaschema",
      ]);
      if (type === "target") {
        oResponse = oResponse.filter((conn) => conn.capabilityReplicationflowTarget === "true");
        if (this.isLargeSystemSpace) {
          oResponse.unshift(Constants.getDwcLTFConnection);
        } else {
          oResponse.unshift(Constants.getDwcHANAConnection);
        }
        oResponse = this.checkTargetConnectionFF(oResponse);
      } else {
        oResponse = oResponse.filter((conn) => conn.capabilityReplicationflowSource === "true");
        oResponse = this.checkSourceConnectionFF(oResponse);
        if (!this.isLargeSystemSpace) {
          oResponse.unshift(Constants.getDwcHANAConnection);
        }
      }

      this.oConnectionDialog.setBusy(false);
      oConnectionModel.setProperty("/", oResponse);
    } catch (error) {
      this.logError(error);
      MessageHandler.uiError(this.localizeText("connectionError"));
      this.oConnectionDialog.setBusy(false);
    }
  }

  /**
   * Filterout connections whose FF is off for target
   * @param connResponse
   * @returns
   */
  private checkTargetConnectionFF(connResponse) {
    return connResponse.filter(
      (conn) =>
        (![Constants.CONNECTION_TYPES.SIGNAVIO].includes(conn.typeId) ||
          (conn.typeId === Constants.CONNECTION_TYPES.SIGNAVIO &&
            this.oFeatures.DWCO_DS_REPLICATION_FLOW_SIGNAVIO_TARGET)) &&
        (![Constants.CONNECTION_TYPES.MSONELAKE].includes(conn.typeId) ||
          (conn.typeId === Constants.CONNECTION_TYPES.MSONELAKE &&
            this.oFeatures.DWCO_DS_REPLICATION_FLOW_MS_ONELAKE_AS_TARGET)) &&
        (![Constants.CONNECTION_TYPES.SFTP].includes(conn.typeId) ||
          (conn.typeId === Constants.CONNECTION_TYPES.SFTP && this.oFeatures.DWCO_DS_REPLICATION_FLOW_SFTP_TARGET))
    );
  }

  /**
   * Filterout connections whose FF is off for source.
   * @param connResponse
   * @returns
   */
  private checkSourceConnectionFF(connResponse) {
    return connResponse.filter(
      (conn) =>
        (!Constants.CONNECTION_SEGGREGATION.OBJECTSTORES.includes(conn.typeId) &&
          conn.typeId !== Constants.CONNECTION_TYPES.CONFLUENT) ||
        this.checkSourceConnectionFFForHDLFDeltaShare(conn) ||
        this.checkSourceConnectionFFForObjectstore(conn) ||
        (conn.typeId === Constants.CONNECTION_TYPES.CONFLUENT &&
          this.oFeatures.DWCO_DS_REPLICATION_FLOW_CONFLUENT_AS_A_SOURCE) ||
        (conn.typeId === Constants.CONNECTION_TYPES.SFTP && this.oFeatures.DWCO_DS_REPLICATION_FLOW_SFTP_SOURCE)
    );
  }

  /**
   * Filter out HDLF Delta share conn if FF is off
   * @param conn
   * @returns
   */
  private checkSourceConnectionFFForHDLFDeltaShare(conn) {
    return (
      this.oFeatures.DWCO_DS_REPLICATION_FLOW_SUPPORT_HDLF_SOURCE &&
      conn.typeId === Constants.CONNECTION_TYPES.HDL_FILES &&
      conn.content?.objects?.[conn.name]?.configuration?.ConnectionProperties?.configurations?.dataAccessLevel ===
        "deltaShare"
    );
  }

  /**
   * Filter out Objectstore conn if FF is off
   * HDL_Files ( With data access level = Deltashare is not objectstore ( internally treated as DWC ) )
   * @param conn
   * @returns
   */
  private checkSourceConnectionFFForObjectstore(conn) {
    if (
      Constants.CONNECTION_SEGGREGATION.OBJECTSTORES.includes(conn.typeId) &&
      conn.typeId !== Constants.CONNECTION_TYPES.SFTP
    ) {
      if (conn.typeId !== Constants.CONNECTION_TYPES.HDL_FILES) {
        return true;
      } else {
        return (
          conn.content?.objects?.[conn.name]?.configuration?.ConnectionProperties?.configurations?.dataAccessLevel !==
          "deltaShare"
        );
      }
    }
  }

  /**
   *  Check the if FF for HDLFSource is off/On
   * @returns {boolean}
   * @memberof RFBuilderClass
   */
  private checkHDLFSourceFF() {
    const HDLFSourceFF = this.oFeatures.DWCO_DS_REPLICATION_FLOW_SUPPORT_HDLF_SOURCE ? true : false;
    return HDLFSourceFF;
  }

  /**
   * Checks if the connection combination is valid , and throws error for invalid combination
   * HDLF delta source only supports DSP HANA target connection
   * @param sourceSys
   * @param targetSys
   * @returns true for valid combination
   */
  private validateTargetForHDLFDeltashareSource(sSelectedObjectType, oSelectedObject, oModel): boolean {
    let validConnection = true;

    // return validConnection;
    let sourceConnectionTypeToValidate,
      targetConnectionIDToValidate,
      targetConnectionTypeToValidate,
      isSourceHDLFDeltashare,
      invalidConnName;
    const sSelectedName = oSelectedObject.name;
    const sSelectedType = oSelectedObject.typeId;
    if (
      oSelectedObject.content?.objects?.[oSelectedObject.name]?.configuration?.ConnectionProperties?.configurations
        ?.dataAccessLevel === "deltaShare" ||
      (sSelectedObjectType === "target" && oModel.sourceSystems.get(0)?.metadata?.isSourceHDLFDeltashare)
    ) {
      isSourceHDLFDeltashare = true;
    }
    if (sSelectedObjectType === "source") {
      const targetSystems = oModel.targetSystems.get(0);
      sourceConnectionTypeToValidate = sSelectedType;
      targetConnectionIDToValidate = targetSystems ? targetSystems.connectionId : undefined;
      targetConnectionTypeToValidate = targetSystems ? targetSystems.connectionType : undefined;
      invalidConnName = targetSystems ? targetSystems.connectionId : "";
    } else {
      const sourceSystems = oModel.sourceSystems.get(0);
      sourceConnectionTypeToValidate = sourceSystems ? sourceSystems.connectionType : undefined;
      targetConnectionIDToValidate = sSelectedName;
      targetConnectionTypeToValidate = sSelectedType;
      invalidConnName = oSelectedObject.name;
    }
    if (
      this.checkHDLFSourceFF() &&
      Utils.isConnectionTypeHdlfWithDeltaShare(sourceConnectionTypeToValidate, isSourceHDLFDeltashare)
    ) {
      if (
        (oModel.targetSystems.get(0) || sSelectedObjectType === "target") &&
        !Utils.isConnectionTypeDwcHANA(targetConnectionIDToValidate, targetConnectionTypeToValidate)
      ) {
        validConnection = false;
        this.oConnectionDialog.destroy();
        //  Throw error for invalid combination of connections
        invalidConnName =
          invalidConnName === Constants.DWC_LTF_CONNECTION_ID
            ? `${Constants.getDwcLTFConnection.businessName} (${Constants.getDwcLTFConnection.typeId})`
            : invalidConnName;
        const title = this.localizeText("invalidTargetforSourceHDLFErrorTitle");
        const message = this.localizeText("invalidTargetforSourceHDLFError", [invalidConnName]);
        MessageHandler.show(
          sap.ui.core.MessageType.Error,
          message,
          title,
          undefined,
          undefined,
          undefined,
          undefined,
          undefined,
          "invalidTargetForHDLF"
        );
      }
    }
    return validConnection;
  }

  /**
   * Search a connection
   * @param oEvent
   * @memberof RFBuilderClass
   */
  public connectionSearch(oEvent: IEvent<any, any>): void {
    const sValue = oEvent.getParameter("value");
    const oFilter = new sap.ui.model.Filter({
      filters: [
        new sap.ui.model.Filter({
          path: "name",
          operator: sap.ui.model.FilterOperator.Contains,
          value1: sValue,
        }),
        new sap.ui.model.Filter({
          path: "businessName",
          operator: sap.ui.model.FilterOperator.Contains,
          value1: sValue,
        }),
      ],
      and: false,
    });
    const oBinding = oEvent.getSource().getBinding("items");
    oBinding.filter([oFilter]);
  }

  /**
   * Create Systems Object based on type
   * @param {Object} aSelectedConnection
   * @param {string} type
   * @memberof RFBuilderClass
   */
  public createSystemsObject(oSelectedConnection, type): void {
    const oModel = this.getModel();
    const connectionMetaschema = oSelectedConnection.connectionMetaschema;
    const metadata: any = {
      ...(type === "target" && connectionMetaschema ? { connectionMetaschema } : {}),
    };

    this.oResource.applyUndoableAction(() => {
      const conn = {
        connectionId:
          oSelectedConnection.name === Constants.DWC_HANA_BUSINESS_NAME &&
          oSelectedConnection.typeId === Constants.CONNECTION_TYPES.HANA
            ? Constants.DWC_HANA_CONNECTION_ID
            : oSelectedConnection.name, // BugFix: DW101-24988
        connectionType: oSelectedConnection.typeId,
        container: RFBuilderUtils.getDefaultContainerBasedOnConn(oSelectedConnection, {
          type,
          spaceName: this.spaceName,
          rfTechnicalName: oModel.name,
        }),
        maxConnections: Constants.DEFAULT_CONNECTION_THREAD_VALUE,
        metadata: metadata,
      };

      if (type === "source") {
        if (oModel.sourceSystems.get(0)) {
          oModel.sourceSystems.get(0).deleteObject();
        }
        if (
          this.checkHDLFSourceFF() &&
          oSelectedConnection.content?.objects?.[oSelectedConnection.name]?.configuration?.ConnectionProperties
            ?.configurations?.dataAccessLevel === "deltaShare"
        ) {
          const containerPathHDLFDeltashare =
            "/" + "_SYS_SAP_DSP_HANA_REMOTE_SOURCES/" + `${oSelectedConnection.remoteSourceName}`;
          conn.metadata.isSourceHDLFDeltashare = true;
          conn.metadata.containerPathHDLFDeltashare = containerPathHDLFDeltashare;
        }
      } else if (type === "target") {
        if (oModel.targetSystems.get(0)) {
          oModel.targetSystems.get(0).deleteObject();
        }
      }
      this.modelBuilder.createSystem(conn, type);
      this.postSystemCreationHook(type);
    }, `create ${type} connection`);
    this.modelBuilder.validateModel();
  }

  /**
   * Add logic to change settings or anything after user manually changes the connections
   *
   * @private
   * @param {string} type
   * @memberof RFBuilderClass
   */
  private postSystemCreationHook(type: string): void {
    const oModel = this.getModel();
    const oTargetSystem = oModel.targetSystems.get(0);
    if (type === "target") {
      if (Utils.isConnectionTypeDwcLTF(oTargetSystem.connectionId, oTargetSystem.connectionType)) {
        this.modelBuilder.updateReplicationFlowSetting("isAutoMergeEnabledForTarget", true);
      }
    }
  }

  /**
   * Close connection dialog
   * @param oEvent
   * @memberof RFBuilderClass
   */
  public async connectionDialogClose(oEvent: IEvent<any, any>): Promise<any> {
    // reset the filter
    const oBinding = oEvent.getSource().getBinding("items");
    oBinding.filter([]);

    const aContexts = oEvent.getParameter("selectedContexts");
    if (aContexts && aContexts.length) {
      const aSelectedConnection = aContexts.map(function (oContext) {
        return oContext.getObject();
      });
      const oDialogModel = this.getView().getModel("connectionDialogOwnModel"); // access own local Model
      const isSourceClicked = oDialogModel.getProperty("/isSourceClicked");
      const isTargetClicked = oDialogModel.getProperty("/isTargetClicked");
      const oModel = this.getModel();

      if (isSourceClicked) {
        // Check for valid connection combination (HDLF to DWC HANA)
        // Refactor: remove below method and use validateSourceTargetCombination()
        if (!this.validateTargetForHDLFDeltashareSource("source", aSelectedConnection[0], oModel)) {
          return false;
        }

        //  Check for valid connection combination
        if (!this.validateSourceTargetCombination("source", aSelectedConnection[0], oModel)) {
          return false;
        }
        this.createSystemsObject(aSelectedConnection[0], "source");

        // Create a PremiumInBound warning strip only after the creation of source system as there are other dialog validations too
        // Create a PremiumInBound warning strip only after the creation of source system as there are other dialog validations too
        oModel.sourceSystems.get(0)?.metadata?.["isSourceHDLFDeltashare"]
          ? this.setPremiumInBoundModel(true, aSelectedConnection[0].name)
          : this.setPremiumInBoundModel(false);
      } else if (isTargetClicked) {
        // Check for valid connection combination (HDLF to DWC HANA)
        // Refactor: remove below method and use validateSourceTargetCombination()
        if (!this.validateTargetForHDLFDeltashareSource("target", aSelectedConnection[0], oModel)) {
          return false;
        }
        //  Check for valid connection combination
        if (!this.validateSourceTargetCombination("target", aSelectedConnection[0], oModel)) {
          return false;
        }
        this.createSystemsObject(aSelectedConnection[0], "target");
        const targetSystems = oModel.targetSystems.get(0);

        // 1. In special case of Local DWC connection ,Validate and Update the task if Target Connection changes to DWC and if replication task exists
        // 2. In Kafka and Confluent also container is set by default so we trigger mapping if possible
        // 3. Signavio Target has default container so task mapping needed
        const hasReplicationTasks = this.hasReplicationTask();
        if (
          (Utils.isConnectionTypeDWC(targetSystems?.connectionId, targetSystems?.connectionType) ||
            Utils.isConnectionTypeKafka(targetSystems?.connectionType) ||
            Utils.isConnectionTypeCONFLUENT(targetSystems?.connectionType) ||
            Utils.isConnectionTypeSignavio(targetSystems?.connectionType)) &&
          hasReplicationTasks
        ) {
          this.triggerTaskRemapping();
        }
        // Initialize Default loadtype on selection of target connection when tasks are present
        if (hasReplicationTasks) {
          this.initializeDefaultLoadTypeForTargetConnection(targetSystems);
        }

        this.configureDeltaLoadIntervalForConnection(targetSystems);

        if (aSelectedConnection[0]?.connectionMetaschema?.sources.includes("NonSAP")) {
          this.setBusy(true);
          await this.checksForPremiumOutbound();
          this.setBusy(false);
        } else {
          this.setPremiumOutBoundModel(false); // setting all properties of model to "false"
        }
      }
    }
    this.getView().getModel("connectionModel").setProperty("/", null);
    this.oConnectionDialog.destroy();
  }

  /**
   * Check if Source and Target Connection and Container is same , then throw error dialog
   * @param oEvent
   * @returns {void}
   * @memberof RFBuilderClass
   */
  private validateSourceTargetCombination(sSelectedObjectType, oSelectedObject, oModel) {
    let sTargetConnectionId, sTargetConnectionType, sTargetConnectionText;
    let sSourceConnectionId, sSourceConnectionType, sSourceConnectionText;
    if (sSelectedObjectType === "source") {
      const targetSystems = oModel.targetSystems.get(0);
      sSourceConnectionId = oSelectedObject.name;
      sSourceConnectionType = oSelectedObject.typeId;
      sTargetConnectionId = targetSystems ? targetSystems.connectionId : null;
      sTargetConnectionType = targetSystems ? targetSystems.connectionType : null;
    } else {
      const sourceSystems = oModel.sourceSystems.get(0);
      sSourceConnectionId = sourceSystems ? sourceSystems.connectionId : null;
      sSourceConnectionType = sourceSystems ? sourceSystems.connectionType : null;
      sTargetConnectionId = oSelectedObject.name;
      sTargetConnectionType = oSelectedObject.typeId;
    }
    sSourceConnectionText = Utils.connectionText(sSourceConnectionId, sSourceConnectionType);
    sTargetConnectionText = Utils.connectionText(sTargetConnectionId, sTargetConnectionType);

    // dsp hana to dsp hana is not allowed
    // object store to dsp ltf is not allowed
    // hdlf delta share to dsp ltf is not allowed
    if (
      (Utils.isConnectionTypeDWC(sSourceConnectionId, sSourceConnectionType) &&
        Utils.isConnectionTypeDWC(sTargetConnectionId, sTargetConnectionType)) ||
      (Utils.isConnTypeObjectStore(sSourceConnectionType) &&
        Utils.isConnectionTypeDwcLTF(sTargetConnectionId, sTargetConnectionType))
    ) {
      // Throw Error dialog
      MessageHandler.uiError(
        this.localizeText("connectionCombinationUnsupportedErrorMsgTxt", [
          sSourceConnectionText,
          sTargetConnectionText,
        ]),
        this.localizeText("connectionCombinationUnsupportedErrorTitle"),
        null,
        null,
        "35%",
        null,
        null,
        "connectionCombinationUnsupportedErrorBox"
      );
      this.getView().getModel("connectionModel").setProperty("/", null);
      this.oConnectionDialog.destroy();
      return false;
    }
    return true;
  }

  /**
   * Browse and select container
   * @param oEvent
   * @returns {void}
   * @memberof RFBuilderClass
   */
  private async onBrowseContainer(oEvent: any) {
    const sElementId = oEvent.getSource().getId();
    const oModel = this.getModel();
    const sourceSystem = oModel.sourceSystems.get(0);
    this.getView()
      .getModel("galileiUiModel")
      .setProperty(
        "/isConnTypeObjectStore",
        Utils.isConnTypeObjectStore(sourceSystem.connectionType) &&
          !Utils.isConnectionTypeHdlfWithDeltaShare(
            sourceSystem.connectionType,
            sourceSystem?.metadata?.["isSourceHDLFDeltashare"]
          )
      );

    const bHasReplicationTask = this.hasReplicationTask(); // to check if replication task is already present or not
    const oContainerTreeModel = this.getView().getModel("containerTreeModel");
    oContainerTreeModel.setProperty("/", null);
    oContainerTreeModel.refresh(true); // fix: previous model binded data was visible for few seconds
    const containerPathHDLFDeltashare = sourceSystem.metadata["containerPathHDLFDeltashare"];
    const oContainerOwnModel = this.resetContainerDialogOwnModel();
    if (
      sElementId === "replicationFlowBuilder--browseSourceContainer" ||
      sElementId === "replicationFlowBuilder--initSourceBrowseContainerBtn"
    ) {
      oContainerOwnModel.isSourceClicked = true;
      oContainerOwnModel.title = Utils.isConnectionTypeCONFLUENT(sourceSystem.connectionType)
        ? this.localizeText("confluentBrowseContext")
        : this.localizeText("sourceContainernEmptyText");
      oContainerOwnModel.connection = sourceSystem.connectionId;
      oContainerOwnModel.connectionType = sourceSystem.connectionType;
      // HDLF Special conditions
      if (
        this.checkHDLFSourceFF() &&
        Utils.isConnectionTypeHdlfWithDeltaShare(
          sourceSystem.connectionType,
          sourceSystem.metadata["isSourceHDLFDeltashare"]
        )
      ) {
        oContainerOwnModel.connectionType = Constants.CONNECTION_TYPES.HANA;
        oContainerOwnModel.containerPath = containerPathHDLFDeltashare;
        oContainerOwnModel.connection = Constants.DWC_HANA_CONNECTION_ID;
        oContainerOwnModel.sourceHdlfDeltaShareProperties.displayConnectionTypeForHDLF =
          Constants.CONNECTION_TYPES.HDL_FILES;
        oContainerOwnModel.sourceHdlfDeltaShareProperties.displayConnectionIdForHDLF = sourceSystem.connectionId;
        oContainerOwnModel.sourceHdlfDeltaShareProperties.isSourceHDLFDeltashare =
          sourceSystem.metadata["isSourceHDLFDeltashare"];
      }
      if (bHasReplicationTask && oModel.sourceSystems.get(0)?.systemContainer) {
        const message = this.getText("confirmRemoveReplicationTaskPrompt");
        await this.showConfirmConnectionOrContainerChange(message, "source", false); // delete replications if any
      }
    } else if (sElementId === "replicationFlowBuilder--browseTargetContainer") {
      oContainerOwnModel.isTargetClicked = true;
      oContainerOwnModel.title = this.localizeText("targetContainernEmptyText");
      oContainerOwnModel.connection = oModel.targetSystems.get(0).connectionId;
      oContainerOwnModel.connectionType = oModel.targetSystems.get(0).connectionType;
      if (bHasReplicationTask && oModel.targetSystems.get(0)?.systemContainer) {
        const message = this.getText("confirmTargetContainerChangePrompt");
        await this.showConfirmConnectionOrContainerChange(message, "target", false); // delete target objects of all replications if any
      }
    }
    this.openContainerDialog(oContainerTreeModel, oContainerOwnModel);
  }

  private openContainerDialog(oContainerTreeModel, oContainerOwnModel) {
    const viewName = require("../view/fragment/ContainerDialog.fragment.xml");
    this.oContainerDialog = sap.ui.xmlfragment("", viewName, this);
    this.getView().addDependent(this.oContainerDialog);
    this.getView().getModel("containerDialogOwnModel").setProperty("/", oContainerOwnModel);
    this.oContainerDialog.open();
    this.oContainerDialog.getButtons()[0].setEnabled(false);
    this.loadData(oContainerTreeModel, undefined, undefined, oContainerOwnModel);
    this.handleObjectStoreSourceInfoForContainer(); // Show info for objectstore source when container is opened
  }

  /**
   *
   * @param oSelectedTask
   * @param dataset
   * @returns GBQ ddl string
   */
  private buildGBQTargetDDL(oSelectedTask: sap.cdw.replicationflow.Task, dataset: string): string {
    const gbqQueryBuilder = new GBQQueryBuilder(oSelectedTask, dataset);
    return gbqQueryBuilder.buildDDL();
  }

  /**
   * Opens a sql editor dialog and shows the generated DDL for selected target
   * @param evt
   * @returns
   */
  private onViewSQLStatement(evt) {
    const path = evt.getSource().getBindingContext("galileiUiModel").getPath();
    const galileiModel = this.getView().getModel("galileiUiModel");
    let taskDetails: sap.cdw.replicationflow.Task;
    if (path) {
      taskDetails = galileiModel.getProperty(path);
    } else {
      taskDetails = this.aSelectedReplicationTask[0];
    }
    if (!taskDetails) {
      return false;
    }
    const viewName = require("../view/fragment/previewDDL.fragment.xml");
    this.sqlDialog = sap.ui.xmlfragment("", viewName, this);
    this.getView().addDependent(this.sqlDialog);
    const validateTarget = RFBuilderUtils.validateGBQTargetObjectAttributes(
      taskDetails.targetObject.attributes.toArray()
    );
    const sqlModelObj: any = {};
    const sqlModel = this.getView().getModel("sqlModel");
    const dataset = galileiModel.getProperty("/targetSystems/0/systemContainer");
    sqlModelObj.title = this.localizeText("sqlTargetDDL", [taskDetails.targetObject.name]);
    sqlModelObj.ddlContent = this.buildGBQTargetDDL(taskDetails, dataset);
    if (validateTarget.keyColCount > 16) {
      sqlModelObj.isMaxKeyColCount = true;
    } else if (validateTarget.isUncompatibleKeyType) {
      sqlModelObj.isUncompatibleKeyType = true;
    } else {
      sqlModelObj.isMaxKeyColCount = false;
      sqlModelObj.isUncompatibleKeyType = false;
    }
    sqlModel.setProperty("/", sqlModelObj);
    this.sqlDialog.open();
  }

  /**
   * Generates DDL and copies it to system clipboard
   * @param evt
   * @returns
   */
  private onCopySQLStatement(evt) {
    let sqlText = "";
    if (this.sqlDialog && this.sqlDialog.isOpen()) {
      sqlText = this.sqlDialog.getModel("sqlModel").getProperty("/ddlContent");
    } else {
      const path = evt.getSource().getBindingContext("galileiUiModel").getPath();
      const galileiModel = this.getView().getModel("galileiUiModel");
      const dataset = galileiModel.getProperty("/targetSystems/0/systemContainer");
      let taskDetails: sap.cdw.replicationflow.Task;
      if (path) {
        taskDetails = galileiModel.getProperty(path);
      } else {
        taskDetails = this.aSelectedReplicationTask[0] ?? undefined;
      }
      if (!taskDetails) {
        return;
      }
      sqlText = this.buildGBQTargetDDL(taskDetails, dataset);
    }
    if (navigator.clipboard) {
      navigator.clipboard.writeText(sqlText);
      sap.m.MessageToast.show(this.getText("copiedToClipboard"));
    }

    if (this.sqlDialog && this.sqlDialog.isOpen()) {
      this.onCloseSqlDialog();
    }
  }
  /**
   * Rest container dialog own model
   * @returns any
   * @memberof RFBuilderClass
   */
  private resetContainerDialogOwnModel(): any {
    return {
      isSourceClicked: false,
      isTargetClicked: false,
      title: this.localizeText("sourceContainernEmptyText"),
      containerPath: "",
      connection: "",
      connectionType: "",
      messageStrip: {
        text: "",
        visible: false,
      },
      isPathContainerClicked: false,
      isSourceHDLFDeltashare: false,
      sourceHdlfDeltaShareProperties: {
        displayConnectionTypeForHDLF: "",
        displayConnectionIdForHDLF: "",
        containerPathHDLFDeltashare: "",
        isSourceHDLFDeltashare: false,
        isHDLFSchemaSelected: false,
      },
      isFolderSelection: false,
    };
  }

  /**
   * All a dummy node for expansion and loading text
   * @param {IItem[]} data
   * @param {string} sPath
   * @param {Object} oModel
   * @returns {Array}
   * @memberof RFBuilderClass
   */
  private filterLeafNodes(data: IItem[], sPath, oModel) {
    const isABAP = Utils.isConnectionTypeABAP(this.getModel().sourceSystems.get(0)?.connectionType);
    const aFilteredResponse = [];

    if (data && data.length > 0) {
      data.forEach((item) => {
        let isHiddenForRMS = false;

        if (item.hasChildren) {
          /** fix (#DW18-1246) **/
          if (isABAP) {
            const parsedProperties = item["diproperties"] ? JSON.parse(item["diproperties"]) : { isHiddenForRMS: "" };
            isHiddenForRMS = parsedProperties.isHiddenForRMS === "X" ? true : false;
            delete item["diproperties"];
          }

          if (!isHiddenForRMS) {
            // we need (isHiddenForRMS === false) to run IF block
            delete item.remote;
            item["namePath"] = sPath ? oModel.getProperty(sPath).namePath + "/" + item.name : item.name; // item.namePath is for searching purposes (see onContainerLiveSearch())

            // For ABAP-CDS: remove drill down and remove "loading..." node
            // For ABAP-CDS: remove drill down and remove "loading..." node
            item["nodes"] =
              isABAP && item.id === "/CDS"
                ? null
                : [{ name: this.localizeText("loading"), namePath: item["namePath"] + "/loading", dummy: true }];
            aFilteredResponse.push(item);
          }
        }
      });
    }
    return aFilteredResponse;
  }

  private updateContainerName(data: IItem[]) {
    return data.map((item) => {
      if (item.id.match(/^\/contexts\//)) {
        item.id = item.id.replace(/^\/contexts/, "");
      }
      return item;
    });
  }
  /**
   * This function is a handler for Object-store root/home icon button
   * @returns {void}
   * @memberof RFBuilderClass
   */
  private handleContainerHome() {
    const oDialogOwnModel = this.getView().getModel("containerDialogOwnModel");
    const isFolderSelection = oDialogOwnModel.getProperty("/isFolderSelection");
    const sContainerPath = "/";
    if (!isFolderSelection) {
      oDialogOwnModel.setProperty("/containerPath", sContainerPath);
    }
  }

  /**
   * Get all conatiners in a connection
   * @param {string} spaceName
   * @param {string} sSelectedItemPath
   * @param {string} sPath
   * @param {Object} oModel
   * @returns {Array}
   * @memberof RFBuilderClass
   */
  public async getContainerData(spaceName, sSelectedItemPath, sPath, oModel, sSelectedConnectionId, isSourceClicked) {
    try {
      const oGlobalModel = this.getModel();
      const sourceSystem = oGlobalModel.sourceSystems.get(0);
      const capabilities: string[] = [];
      let oResponse, customConfig: ICustomConfig;

      if (
        this.checkHDLFSourceFF() &&
        Utils.isConnectionTypeHdlfWithDeltaShare(
          sourceSystem.connectionType,
          sourceSystem.metadata["isSourceHDLFDeltashare"]
        ) &&
        isSourceClicked
      ) {
        capabilities.push(Constants.IS_RF_REMOTE_SOURCES);
        sSelectedItemPath = encodeURIComponent(sSelectedItemPath);
        oResponse = await APIService.getChildren(
          sSelectedItemPath,
          this.spaceName,
          sSelectedConnectionId,
          capabilities
        );
      } else {
        if (isSourceClicked && Utils.isCFWSupported(sourceSystem.connectionType)) {
          let context: CFWContexts;
          if (Utils.isConnectionTypeCONFLUENT(sourceSystem.connectionType)) {
            context = CFWContexts.CONTEXTS;
          }
          customConfig = RFBuilderUtils.getCustomConfig(sSelectedItemPath, context, sourceSystem.connectionType, {});
          capabilities.push(Constants.USE_CFW);
        }

        oResponse = await APIService.getChildren(
          sSelectedItemPath,
          this.spaceName,
          sSelectedConnectionId,
          capabilities,
          customConfig
        );
      }

      if (Utils.isConnectionTypeCONFLUENT(sourceSystem.connectionType)) {
        oResponse = this.updateContainerName(oResponse);
      }
      if (Utils.isConnectionTypeS4HANAOP(sourceSystem.connectionType)) {
        oResponse = oResponse.filter((item) => item.id !== Constants.SLT_CONTAINER);
      }

      return this.filterLeafNodes(oResponse, sPath, oModel);
    } catch (e) {
      const causes = e[0]?.responseJSON?.details?.body?.causes;
      if (causes) {
        const errorMsg = causes
          .map((item, index) => {
            return `Cause ${index}: ${item.message} Code: ${item.code}`;
          })
          .join("; ");

        if (e[0]?.responseJSON?.details?.message) {
          e[0].responseJSON.details.message += errorMsg;
        }
      }
      MessageHandler.exception({
        exception: e,
        message: this.localizeText("msgFetchContainerFail"),
        id: "containerFailedErrorMsgbox",
      });
    }
  }

  /**
   * On pressing container dialog close button
   * @memberof RFBuilderClass
   */
  public onContainerDialogClose(oEvent) {
    const getPressedButton = oEvent?.getSource();
    const oDialogOwnModel = getPressedButton?.getParent()?.getModel("containerDialogOwnModel")?.getProperty("/");
    this.onRemoveContainerDialog();
    oDialogOwnModel.isPathContainerClicked = false; // On closing container dialog , this property must be false
    this.modelBuilder.validateModel();
  }

  /**
   * On pressing container dialog select button
   * @param oEvent
   * @memberof RFBuilderClass
   */
  public async onSelectContainer(oEvent) {
    const getPressedButton = oEvent.getSource();
    const oDialogOwnModel = getPressedButton.getParent().getModel("containerDialogOwnModel").getProperty("/");
    const oModel = this.getModel();
    const sourceConnectionId: string = oModel.sourceSystems.get(0)?.connectionId;
    const sourceConnectionType: string = oModel.sourceSystems.get(0)?.connectionType;
    const isSourceHDLFDeltashare: boolean = oModel.sourceSystems.get(0)?.metadata?.["isSourceHDLFDeltashare"];

    // On Selecting folder (container) for objectstore , metadata file browser ( dataset dialog ) should open
    if (
      oDialogOwnModel.isFolderSelection &&
      Utils.isConnTypeObjectStore(sourceConnectionType) &&
      !Utils.isConnectionTypeHdlfWithDeltaShare(sourceConnectionType, isSourceHDLFDeltashare)
    ) {
      const containerPath = oDialogOwnModel.containerPath;
      this.onRemoveContainerDialog();
      const aSelectedSources = await this.openRemoteSourceBrowser(containerPath);
      this.validationsOnSelectedSourceDataset(sourceConnectionId, sourceConnectionType, aSelectedSources);
    } else if (oDialogOwnModel.isSourceClicked) {
      this.setContainerPath(oDialogOwnModel, oModel, "source");
      this.onRemoveContainerDialog();
    } else if (oDialogOwnModel.isTargetClicked) {
      this.setContainerPath(oDialogOwnModel, oModel, "target");
      this.onRemoveContainerDialog();
      // Validate and Update the task if Target Container changes
      if (this.hasReplicationTask()) {
        this.triggerTaskRemapping();
      }
    } else if (oDialogOwnModel.isPathContainerClicked) {
      // If Container Path change is triggered from task context menu
      this.alterContainerPath(oDialogOwnModel);
    }
  }

  /**
   * path container or changing the path of the existing target dataset is only supported by object stores
   *
   * @private
   * @param {*} oDialogOwnModel
   * @memberof RFBuilderClass
   */
  private async alterContainerPath(oDialogOwnModel) {
    const galileiUiModel = this.getView().getModel("galileiUiModel");
    const task = this.oLastContextMenuClickedReplicationTask;
    const connectionId = galileiUiModel.getProperty("/targetSystems/0/connectionId");
    // const connectionType = galileiUiModel.getProperty("/targetSystems/0/connectionType");
    const selectedContainer = oDialogOwnModel.containerPath; //  ex:  let Target Container be "/abc" and selected container be "/abc/def"
    this.onRemoveContainerDialog();
    this.setBusy(true);
    const targetQualifiedName = this.getFilePath(selectedContainer, task); // get full file path

    try {
      const getChildrenPath = this.getRelativeParentPath(targetQualifiedName);
      const itemsResp: any = await APIService.getChildren(getChildrenPath, this.getSpaceName(), connectionId);
      task.targetObject.isNew = !this.isFileExistsInsideObjectStoreNode(itemsResp, targetQualifiedName); // If we get data , that means target is there at the given path
    } catch (e) {
      task.targetObject.isNew = true;
    }
    // ex: targetName = (selectedContainer - this.getModel().container) + / + tasks.sourceObject.name;  ((/abc/def)-(/abc) + / + p1.csv)
    const path = selectedContainer.replace(this.getModel().targetSystems.get(0).systemContainer, "");
    task.targetObject.name = this.getFilePath(path, task); // get full file path;
    this.modelBuilder.validateModel();
    oDialogOwnModel.isPathContainerClicked = false; // On Pressing Select container dialog , this property must be false
    this.setBusy(false);
  }

  /**
   * set container path to model
   * @param oDialogOwnModel
   * @param oModel
   * @memberof RFBuilderClass
   */
  public setContainerPath(oDialogOwnModel, oModel, type) {
    this.oResource.applyUndoableAction(() => {
      if (type === "source") {
        oModel.sourceSystems.get(0).systemContainer = oDialogOwnModel.containerPath;
      } else {
        oModel.targetSystems.get(0).systemContainer = oDialogOwnModel.containerPath;
      }
    }, `update ${type} system`);
    this.modelBuilder.validateModel();
  }

  /**
   * On pressing container list item
   * @param oEvent
   * @memberof RFBuilderClass
   */
  public onContainerListItemPress(oEvent) {
    const sPath = oEvent.getParameter("listItem").getBindingContextPath();
    const oSourceElement = oEvent.getSource();
    const oItemBoundingModel = oSourceElement.getModel("containerTreeModel").getProperty(sPath);
    const oDialogOwnModel = oSourceElement.getModel("containerDialogOwnModel");
    const sContainerPath = oItemBoundingModel.id;
    if (
      this.checkHDLFSourceFF() &&
      Utils.isConnectionTypeHdlfWithDeltaShare(
        oDialogOwnModel.getData().sourceHdlfDeltaShareProperties.displayConnectionTypeForHDLF,
        oDialogOwnModel.getData().sourceHdlfDeltaShareProperties.isSourceHDLFDeltashare
      )
    ) {
      this.handleHDLFDeltaSelectedItem(oDialogOwnModel, sPath, oItemBoundingModel.nodes);
    }
    if (Utils.isConnectionTypeABAP(oDialogOwnModel.getData().connectionType)) {
      sContainerPath === Constants.SLT_CONTAINER
        ? this.setContainerInfoStripObject(oDialogOwnModel, this.localizeText("infoMessageForSLTSelection"), true)
        : this.setContainerInfoStripObject(oDialogOwnModel, "", false);
    }
    oDialogOwnModel.setProperty("/containerPath", sContainerPath);
  }

  /**
   * Close and destroy container dialog
   * @memberof RFBuilderClass
   */
  public onRemoveContainerDialog() {
    this.oContainerDialog.close();
    this.oContainerDialog.destroy();
  }

  public onCloseSqlDialog() {
    this.sqlDialog.close();
    this.sqlDialog.destroy();
  }

  /**
   * On expanding conatiner list item
   * @param oEvent
   * @memberof RFBuilderClass
   */
  onToggleOpenState(oEvent) {
    const oTree = sap.ui.getCore().byId("containerTreeList") as sap.m.Tree;
    const oModel = oEvent.getSource().getModel("containerTreeModel");
    const iItemIndex = oEvent.getParameter("itemIndex");
    const oItemContext = oEvent.getParameter("itemContext");
    const bExpanded = oEvent.getParameter("expanded");
    const sPath = oItemContext.getPath();
    const bChildIsDummyNode = oModel.getProperty(sPath).hasChildren; // hasChildren is true if folder has folder or files
    const selectedItem = oEvent.getSource().getModel("containerTreeModel").getProperty(sPath);
    const oDialogModel = this.getView().getModel("containerDialogOwnModel").getProperty("/");
    if (bExpanded && bChildIsDummyNode) {
      this.loadData(oModel, sPath, selectedItem, oDialogModel);
    }
  }

  /**
   * Load container API data to model
   * @param oModel
   * @param sPath
   * @param selectedItem
   * @param oDialogModel
   * @memberof RFBuilderClass
   */
  async loadData(oModel, sPath, selectedItem, oDialogModel) {
    const oTree = sap.ui.getCore().byId("containerTreeList") as sap.m.Tree;
    const oGlobalModel = this.getModel();
    const sourceSystem = oGlobalModel.sourceSystems.get(0);
    let sSelectedConnectionId = oDialogModel?.isSourceClicked
      ? oGlobalModel.sourceSystems.get(0).connectionId
      : oGlobalModel.targetSystems.get(0).connectionId;
    let response = [];
    oTree.setBusy(true);
    const isSourceClicked = oDialogModel.isSourceClicked;
    if (sPath && selectedItem) {
      // "IF" condition is active when any container is expanded
      const sSelectedItemPath = selectedItem.id;
      const sourceSystem = oGlobalModel.sourceSystems.get(0);
      if (
        this.checkHDLFSourceFF() &&
        Utils.isConnectionTypeHdlfWithDeltaShare(
          sourceSystem.connectionType,
          sourceSystem.metadata["isSourceHDLFDeltashare"]
        )
      ) {
        sSelectedConnectionId = Constants.DWC_HANA_CONNECTION_ID;
      }
      response = await this.getContainerData(
        this.spaceName,
        sSelectedItemPath,
        sPath,
        oModel,
        sSelectedConnectionId,
        isSourceClicked
      );
      // Filter Sub Container response based on ABAP Connection and Container Name
      // Also check "filterLeafNodes()" to remove drill down on sub-container based on above
      response = Utils.isConnectionTypeABAP(this.getModel().sourceSystems.get(0).connectionType)
        ? this.filterResponseForABAPConn(response, selectedItem)
        : response;
    } else {
      // "ELSE" condition is active first time , when all the containers are collapsed
      let sSelectedItemPath = "/";
      if (oDialogModel.isPathContainerClicked) {
        sSelectedItemPath = oDialogModel.containerPath;
      }
      const isSourceHDLFDeltashare = Utils.isConnectionTypeHdlfWithDeltaShare(
        sourceSystem.connectionType,
        sourceSystem.metadata["isSourceHDLFDeltashare"]
      );
      if (this.checkHDLFSourceFF() && isSourceHDLFDeltashare) {
        sSelectedItemPath = oDialogModel.containerPath;
        sSelectedConnectionId = Constants.DWC_HANA_CONNECTION_ID;
      }
      if (
        Utils.isConnTypeObjectStore(sourceSystem.connectionType) &&
        !isSourceHDLFDeltashare &&
        oDialogModel.isFolderSelection
      ) {
        sSelectedItemPath = oDialogModel.containerPath;
      }
      response = await this.getContainerData(
        this.spaceName,
        sSelectedItemPath,
        sPath,
        oModel,
        sSelectedConnectionId,
        isSourceClicked
      );
    }
    const oContainerDialogOwnModel = this.getView().getModel("containerDialogOwnModel");
    if (Utils.isConnectionTypeS4HANAOP(sourceSystem.connectionType)) {
      this.setContainerInfoStripObject(oContainerDialogOwnModel, this.localizeText("infoMessageForSLTHidden"), true);
    }
    oModel.setProperty(sPath ? `${sPath}/nodes` : "/", response);
    oTree.setBusy(false);
  }

  /**
   * This function filters the response of ABAP Connection's "Sub Container"
   * @param response
   * @param selectedItem
   * @returns {Array} response
   * @memberof RFBuilderClass
   */
  private filterResponseForABAPConn(response, selectedItem) {
    const oContainerDialogOwnModel = this.getView().getModel("containerDialogOwnModel");

    /** isAbapSLTSystemNode Bug fix (#DW101-23221)  !! Don't remove the comments till GA Release
    if (selectedItem.id === Constants.SLT_CONTAINER) {
      // Show Toast (Can only select MassTranferID/isAbapSLTSystemNode=true)
      this.setContainerInfoStripObject(oContainerDialogOwnModel, this.localizeText("infoMessageForSLTSelection"), true);
      return response.filter(item => item.isAbapSLTSystemNode);
    }
    **/

    // Show info message if /SLT is selected saying (Can only select MassTranferID/isAbapSLTSystemNode=true)
    selectedItem.id === Constants.SLT_CONTAINER
      ? this.setContainerInfoStripObject(
          oContainerDialogOwnModel,
          this.localizeText("infoMessageForSLTSelection"),
          true
        )
      : this.setContainerInfoStripObject(oContainerDialogOwnModel, "", false);
    return response;
  }

  /**
   *
   * @param {Object} oContainerDialogOwnModel
   * @param {string} text
   * @param {boolean} visible
   * @return void
   * @memberof RFBuilderClass
   */
  private setContainerInfoStripObject(oContainerDialogOwnModel, text = "", visible = false): void {
    oContainerDialogOwnModel.setProperty("/messageStrip/text", text);
    oContainerDialogOwnModel.setProperty("/messageStrip/visible", visible);
  }

  /**
   *
   * @param sPath
   * @param nodes
   */
  private handleHDLFDeltaSelectedItem(oDialogOwnModel, sPath, nodes) {
    if (sPath.includes("nodes")) {
      this.setContainerEmptyDeltaSchemaInfoStrip(oDialogOwnModel, "", false);
      oDialogOwnModel.setProperty("/sourceHdlfDeltaShareProperties/isHDLFSchemaSelected", true);
    } else {
      oDialogOwnModel.setProperty("/sourceHdlfDeltaShareProperties/isHDLFSchemaSelected", false);
      if (nodes.length === 0) {
        const text = " No Delta Schema in selected Delta Share. You need to select a Delta Schema.";
        this.setContainerEmptyDeltaSchemaInfoStrip(oDialogOwnModel, text, true);
      } else {
        this.setContainerEmptyDeltaSchemaInfoStrip(oDialogOwnModel, "", false);
      }
    }
  }

  private handleObjectStoreSourceInfoForContainer() {
    const oDialogOwnModel = this.getView().getModel("containerDialogOwnModel");
    const isSourceClicked = oDialogOwnModel.getData().isSourceClicked;
    const sSelectedConnectionType = isSourceClicked ? oDialogOwnModel.getData().connectionType : undefined;
    const isSourceHDLFDeltashare = oDialogOwnModel.getData().sourceHdlfDeltaShareProperties.isSourceHDLFDeltashare;
    const isFolderSelection = oDialogOwnModel.getData().isFolderSelection;
    if (isSourceClicked && Utils.isConnTypeObjectStore(sSelectedConnectionType) && !isSourceHDLFDeltashare) {
      if (isFolderSelection) {
        this.setEmptySubFolderInfoStripForObjectstores(oDialogOwnModel, " ", false);
      } else {
        this.setEmptySubFolderInfoStripForObjectstores(
          oDialogOwnModel,
          this.localizeText("infoForHavingSubFoldersForObjectstoreContainer"),
          true
        );
      }
    }
  }

  /**
   *
   * @param oDialogOwnModel
   * @param text
   * @param visible
   */
  private setContainerEmptyDeltaSchemaInfoStrip(oDialogOwnModel, text = "", visible = false): void {
    oDialogOwnModel.setProperty("/messageStrip/text", text);
    oDialogOwnModel.setProperty("/messageStrip/visible", visible);
  }

  private setEmptySubFolderInfoStripForObjectstores(oDialogOwnModel, text = "", visible = false): void {
    oDialogOwnModel.setProperty("/messageStrip/text", text);
    oDialogOwnModel.setProperty("/messageStrip/visible", visible);
  }

  /**
   * @param oEvent
   * @return void
   * @memberof RFBuilderClass
   */
  private onContainerLiveSearch(oEvent) {
    const sValue = oEvent.getParameter("newValue");
    const oFilter = new sap.ui.model.Filter("namePath", sap.ui.model.FilterOperator.Contains, sValue);
    const oTree = sap.ui.getCore().byId("containerTreeList") as sap.m.Tree;
    const oBinding = oTree.getBinding("items") as sap.ui.model.TreeBinding;
    oBinding.filter([oFilter], "Application");
  }

  // ============================================================ <Add source dataset Handlers> ==============================================

  /**
   * Opens local repo / remote connection browser, depending on the source connection selected
   *
   * @private
   * @param {*} oEvent
   * @memberof RFBuilderClass
   */
  private async onPressAddSourceDataset(oEvent) {
    this.currentDatasetBrowserType = "source";
    const galileiUiModel = this.getView().getModel("galileiUiModel");
    const sSourceConnectionId: string = galileiUiModel.getProperty("/sourceSystems/0/connectionId");
    const sSourceConnectionType: string = galileiUiModel.getProperty("/sourceSystems/0/connectionType");
    const isSourceHDLFDeltashare: boolean = galileiUiModel.getProperty(
      "/sourceSystems/0/metadata/isSourceHDLFDeltashare"
    );

    if (
      Utils.isConnTypeObjectStore(sSourceConnectionType) &&
      !Utils.isConnectionTypeHdlfWithDeltaShare(sSourceConnectionType, isSourceHDLFDeltashare)
    ) {
      this.openFolderBrowserForObjectstoreSource(oEvent);
    } else if (Utils.isConnectionTypeDWC(sSourceConnectionId, sSourceConnectionType)) {
      const oParams = {
        filters: {
          "#technicalType": Constants.SUPPORTED_TECHNICAL_TYPES_SOURCE,
          "#objectStatus": ["1", "2", "4"],
          "#isToolingHidden": [false, "<NULL>"],
        },
      };
      // Remove this condition after FF is removed and add DWC_VIEW to the above filters
      if (Utils.checkHanaViewAsSourceSupportFF()) {
        oParams.filters["#technicalType"].push(RepositoryObjectType.DWC_VIEW);
      }
      this.openLocalRepositoryBrowser(oParams); // After selection from DWC browser control goes to "onRepositoryObjectsSelected(aSelectedObjects)"
    } else {
      let aSelectedSources = await this.openRemoteSourceBrowser();
      // To find all the supported Datasets and primary key conditions
      // To find all the supported Datasets and primary key conditions
      this["response"] = aSelectedSources;
      this.validationsOnSelectedSourceDataset(sSourceConnectionId, sSourceConnectionType, aSelectedSources);
    }
  }

  /**
   * This function will validate all the condition/scenarios before a new task is created with source metadata
   * @private
   * @param {string} sSourceConnectionId
   * @param {string} sSourceConnectionType
   * @memberof RFBuilderClass
   */
  private validationsOnSelectedSourceDataset(sSourceConnectionId, sSourceConnectionType, aSelectedSources) {
    const replicationTasks = this.getView().getModel("galileiUiModel").getProperty("/replicationTasks");
    const targetConnectionId: string = this.getView()
      .getModel("galileiUiModel")
      .getProperty("/targetSystems/0/connectionId");
    const targetContainer: string = this.getView()
      .getModel("galileiUiModel")
      .getProperty("/targetSystems/0/systemContainer");
    const isSourceHDLFDeltashare: boolean = this.getView()
      .getModel("galileiUiModel")
      .getProperty("/sourceSystems/0/metadata/isSourceHDLFDeltashare");
    const existingReplicationsCount: number = replicationTasks.length;

    let selectedDatasetsCount = 0;
    if (Array.isArray(aSelectedSources)) {
      selectedDatasetsCount = aSelectedSources.length;
    }
    if (existingReplicationsCount + selectedDatasetsCount > 500) {
      MessageHandler.show(
        sap.ui.core.MessageType.Warning,
        this.localizeText("datasetLimitCrossed"),
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        "maxLimitOfDatasets"
      );
    } else if (Array.isArray(aSelectedSources)) {
      if (Utils.isConnectionTypeDWC(sSourceConnectionId, sSourceConnectionType)) {
        // SAC artefacts from repository browser to be removed
        aSelectedSources = this.preventArtefactsFromSAC(aSelectedSources, false);
        aSelectedSources = this.removeUnsupportedDatasetFromDWCSource(aSelectedSources);
      } else if (
        Utils.isConnectionTypeHdlfWithDeltaShare(sSourceConnectionType, isSourceHDLFDeltashare) ||
        !Utils.isConnTypeObjectStore(sSourceConnectionType)
      ) {
        aSelectedSources = this.removeUnsupportedDataset(aSelectedSources, sSourceConnectionType, sSourceConnectionId);
      }
      this.createTasksFromSourceMetadata(sSourceConnectionId, sSourceConnectionType, aSelectedSources);
      this.onTabChangeModelUpdate(Constants.TAB_KEYS.TRANSFORMATION);
      if (targetConnectionId && targetContainer) {
        this.triggerTaskRemapping();
      }
    }
  }
  /**
   *  Function to prevent datasets from SAC and to show error for the unsupported datasets when added or when used for maptoExisting
   * @param selectedDatasets
   * @param maptoExisting
   * @returns Supported Datasets array
   */
  private preventArtefactsFromSAC(selectedDatasets, maptoExisting: boolean) {
    const unsupportedArtefactsFromSAC = [];
    const supportedDatasets: any[] = [];
    if (selectedDatasets?.length) {
      selectedDatasets.forEach((oDataset) => {
        // To identify a view ,we are using "query" property and not "typeName" property due to inconsistency issues.
        const viewObject = oDataset.definitions[oDataset.id];
        // check for SQL View
        const isSQLView = viewObject?.query ? true : false;
        // check for Script view
        const isScriptView = viewObject?.["@DataWarehouse.tableFunction.script"] ? true : false;

        const isSAC = oDataset["#canManipulateData"] === "false"; // If this is true then it is an SAC artefact
        const isView = isSQLView || isScriptView;
        // For all view #canManipulateData is false , however a view cannot be an SAC artifact so we need to check for view
        const isSupported = !isSAC || isView;

        if (isSupported) {
          supportedDatasets.push(oDataset);
        } else {
          unsupportedArtefactsFromSAC.push(`\n • ${oDataset.name}`);
        }
      });
    }

    // Error message for unsupported SAC artefacts
    if (unsupportedArtefactsFromSAC.length > 0) {
      if (maptoExisting) {
        const message = this.localizeText("errorForMapToExistingOfSACDataset1", ["\n", `"`]);
        MessageHandler.show(
          sap.ui.core.MessageType.Error,
          message,
          undefined,
          undefined,
          undefined,
          undefined,
          undefined,
          undefined,
          "mapToExistToUnsupportedDatasetFromSAC"
        );
      } else {
        const messageNoKeys = this.localizeText("errorForDatasetFromSAC1", ["\n", unsupportedArtefactsFromSAC, `"`]);
        MessageHandler.show(
          sap.ui.core.MessageType.Error,
          messageNoKeys,
          undefined,
          undefined,
          undefined,
          undefined,
          undefined,
          undefined,
          "unsupportedDatasetFromSAC"
        );
      }
    }
    return supportedDatasets;
  }

  /**
   * Check if this DWC source object has primary key
   * @param Dataset
   * @returns boolean
   * @memberof RFBuilderClass
   */
  private isDWCSourceObjectWithPK(Dataset) {
    let hasUniqueKey: boolean = false;
    const oColumns: Object = Dataset.definitions[Dataset.name].elements;
    for (let colName in oColumns) {
      if (oColumns.hasOwnProperty(colName) && oColumns[colName].key) {
        hasUniqueKey = true; // Dataset has unique key
        break;
      }
    }
    return hasUniqueKey;
  }

  /**
   * Remove unsupported datasets from DWC as source and show a info dialog of list of dataset not supported
   * conditions:
   *  1. all dataset with primary keys supported
   * @private
   * @param {*} sourcesArr
   * @param {*} connectionType
   * @return {Array}
   * @memberof RFBuilderClass
   */
  private removeUnsupportedDatasetFromDWCSource(sourcesArr): [] {
    const datasetsWithoutKeys = [];
    const sqlViewWithParamCollection = [];
    // Dataset without primary keys (could be composite keys as well) are not supported as source
    sourcesArr = sourcesArr.filter((oObject) => {
      const viewObject = oObject.definitions[oObject.id];
      // All SQL and Script views are allowed as source for DWC (except SQL with Params)
      const isView =
        oObject.typeName.toUpperCase() === Constants.SOURCE_OBJECT_TYPE.VIEW ||
        viewObject?.["@DataWarehouse.tableFunction.script"]
          ? true
          : false;
      const isSQLViewWithParam = isView && viewObject?.params ? true : false;

      if (isSQLViewWithParam) {
        // source object as SQL view with params is not supported and an error message will be thrown
        sqlViewWithParamCollection.push(oObject.name);
        return false;
      } else if (isView) {
        // If selected source object is a view , then it is supported irrepective of primary key is there or not
        return true;
      } else {
        // Dataset without primary keys (could be composite keys as well) are not supported as source
        const hasUniqueKey: boolean = this.isDWCSourceObjectWithPK(oObject);
        if (hasUniqueKey) {
          return true;
        } else {
          datasetsWithoutKeys.push(oObject.name);
          return false;
        }
      }
    });

    //  Info Message for dataset objects without primary key and dataset objects(all HANA SQL Views) with Params
    if (datasetsWithoutKeys.length > 0 && sqlViewWithParamCollection.length > 0) {
      this.showErrMessage(
        {
          messageKey: "infoForUnsupportedDatasetNoKeys",
          datasets: datasetsWithoutKeys,
          subMessageKey: "new_infoForUnsupportedDatasetNoKeys2",
        },
        "NoKeysAndSourceAsSQLViewWithParamsUnsupportedDatasetWarning",
        { messageKey: "infoForUnsupportedSourceAsSQLViewWithParams", datasets: sqlViewWithParamCollection }
      );
    } else {
      //  Info Messsage for dataset objects without primary key only.
      if (datasetsWithoutKeys.length > 0) {
        this.showErrMessage(
          {
            messageKey: "new_infoForUnsupportedDatasetNoKeys",
            datasets: datasetsWithoutKeys,
            subMessageKey: "new_infoForUnsupportedDatasetNoKeys2",
          },
          "unsupportedDatasetErrorNoKeysDWC"
        );
      }

      //  Info Messsage for dataset objects(all Hana SQL Views) with Params not allowed as source
      if (sqlViewWithParamCollection.length > 0) {
        this.showErrMessage(
          { messageKey: "infoForUnsupportedSourceAsSQLViewWithParams", datasets: sqlViewWithParamCollection },
          "UnsupportedSourceAsSQLViewWithParamsErrorDWC"
        );
      }
    }
    return sourcesArr; // return Datasets with primary key
  }

  /**
   * Remove un supported dataset and show a info dialog of list of dataset not supported
   * conditions:
   *  1. ABAP && isExtractionEnabled===true are supported (enhance later for isExtractionAllowed)
   *  2. all dataset with primary keys supported
   *  3. HDLF Deltashare && ABAP might not have keys ( Conditions applied )
   * @private
   * @param {*} sourcesArr
   * @param {*} connectionType
   * @param {*} connectionId
   * @return {Array}
   * @memberof RFBuilderClass
   */
  private removeUnsupportedDataset(sourcesArr: any[], connectionType: string, connectionId: string): any[] {
    const extractionNotEnabled = [];
    const datasetsWithoutKeys = [];
    const datasetWithInvalidSchemaType = [];
    const datasetWithNoSchema = [];
    const datasetAsView = [];
    const isSourceHDLFDeltashare = this.getView()
      .getModel("galileiUiModel")
      .getProperty("/sourceSystems/0/metadata/isSourceHDLFDeltashare");
    const sourceContainer: string = this.getView()
      .getModel("galileiUiModel")
      .getProperty("/sourceSystems/0/systemContainer");

    // handle ABAP non Extractablale dataset
    // "extraction" not enabled scenarios for ABAP
    if (Utils.isConnectionTypeABAP(connectionType)) {
      sourcesArr = sourcesArr.filter((oDef) => {
        if (
          oDef.properties?.some(
            (p: any) => p.namespace.startsWith("com.sap.abap") && p.name === "isExtractionEnabled" && p.value === "X"
          )
        ) {
          return true;
        } else {
          extractionNotEnabled.push(oDef.remoteObjectReference.name);
          return false;
        }
      });
    }

    if (Utils.isConnectionTypeMSSQL(connectionType)) {
      sourcesArr = sourcesArr.filter((oDef) => {
        if (oDef.remoteObjectReference.remoteObjectType === Constants.SOURCE_OBJECT_TYPE.VIEW) {
          datasetAsView.push(oDef.remoteObjectReference.name); // for mssql as source connection , view is not supported
          return false;
        }
        return true;
      });
    }

    // fix-DW101-23850 dataset without primary keys (could be composite keys as well) are not supported as source
    // Check which all connections allow dataset without primary keys and based on that dont enter in this statement
    // There are connection in which some dataset types(like view) are allowed without primary keys , handle them in isDatasetsWithoutPKSupported().
    if (!RFBuilderUtils.isSourceSystemWithoutPKSupported(connectionType, isSourceHDLFDeltashare, sourceContainer)) {
      sourcesArr = sourcesArr.filter((oDef) => {
        // Check if the dataset without primary key is supported
        if (
          this.isDatasetsWithoutPKSupported(oDef.remoteObjectReference.remoteObjectType, connectionType, connectionId)
        ) {
          return true;
        }

        // PK check for tables
        const aUniqueKeys: string[] = this.getUniqueKeys(oDef.schema);
        const aAttributes: IColumnDetail[] = oDef.schema?.tableBasedRepresentation?.attributes;
        if (
          aUniqueKeys.length &&
          aAttributes?.length &&
          aAttributes.some((oAttr) => aUniqueKeys.includes(oAttr.name))
        ) {
          return true;
        } else {
          datasetsWithoutKeys.push(oDef.remoteObjectReference.name);
          return false;
        }
      });
    }

    // Handle No schema, Confluent Schema Type, only AVRO and JSON are supported
    if (Utils.isConnectionTypeCONFLUENT(connectionType)) {
      sourcesArr = sourcesArr.filter((dataset) => {
        if (dataset.miscellaneousProperties?.isConfluentSchemaNotFound) {
          datasetWithNoSchema.push(dataset.remoteObjectReference.name);
          return false;
        } else if (
          dataset.dataAccessConfiguration?.confluentSchemaType &&
          ![Constants.CONFLUENT_SCHEMA_TYPE.AVRO, Constants.CONFLUENT_SCHEMA_TYPE.JSON].includes(
            dataset.dataAccessConfiguration.confluentSchemaType
          )
        ) {
          datasetWithInvalidSchemaType.push(dataset.remoteObjectReference.name);
          return false;
        } else {
          return true;
        }
      });
    }

    if (extractionNotEnabled.length > 0 && datasetsWithoutKeys.length > 0) {
      this.showErrMessage(
        {
          messageKey: "new_infoForUnsupportedDatasetNoKeys",
          datasets: datasetsWithoutKeys,
          subMessageKey: "new_infoForUnsupportedDatasetNoKeys2",
        },
        "allUnsupportedDatasetWarning",
        { messageKey: "infoForUnsupportedDatasetExtractionDisabled", datasets: extractionNotEnabled }
      );
    } else if (datasetAsView.length > 0 && datasetsWithoutKeys.length > 0) {
      this.showErrMessage(
        {
          messageKey: "new_infoForUnsupportedDatasetNoKeys",
          datasets: datasetsWithoutKeys,
          subMessageKey: "new_infoForUnsupportedDatasetNoKeys2",
        },
        "viewAndNoKeysUnsupportedDatasetWarning",
        { messageKey: "infoForUnsupportedDatasetView", datasets: datasetAsView }
      );
    } else {
      if (datasetsWithoutKeys.length > 0) {
        this.showErrMessage(
          {
            messageKey: "new_infoForUnsupportedDatasetNoKeys",
            datasets: datasetsWithoutKeys,
            subMessageKey: "new_infoForUnsupportedDatasetNoKeys2",
          },
          "unsupportedDatasetWarningNoKeys"
        );
      }
      if (extractionNotEnabled.length > 0) {
        this.showErrMessage(
          { messageKey: "infoForUnsupportedDatasetExtractionDisabled", datasets: extractionNotEnabled },
          "unsupportedDatasetWarningExtractionDisabled"
        );
      }
      if (datasetWithInvalidSchemaType.length > 0) {
        this.showErrMessage(
          { messageKey: "infoForUnsupportedDatasetSchemaTypeUpdated", datasets: datasetWithInvalidSchemaType },
          "unsupportedDatasetWarningSchemaType"
        );
      }
      if (datasetWithNoSchema.length > 0) {
        this.showErrMessage(
          { messageKey: "infoForUnsupportedDatasetSchemaNotFound", datasets: datasetWithNoSchema },
          "unsupportedDatasetWarningSchemaNotFound"
        );
      }
      if (datasetAsView.length > 0) {
        this.showErrMessage(
          { messageKey: "infoForUnsupportedDatasetView", datasets: datasetAsView },
          "unsupportedDatasetWarningView"
        );
      }
    }

    return sourcesArr;
  }

  /**
   * @param sObjectType
   * @param connectionType
   * @param connectionId
   * @description This function checks if the dataset is allowed as source (with/without PK)
   * @returns boolean
   */
  private isDatasetsWithoutPKSupported(sObjectType: string, connectionType: string, connectionId: string): boolean {
    // For(HANA External), Allow views without PK to be added to RF editor
    // On FF Removal : no need to change any condition , just remove the FF
    if (
      Utils.checkHanaViewAsSourceSupportFF() &&
      Utils.isConnectionTypeHANA(connectionId, connectionType) &&
      sObjectType === Constants.SOURCE_OBJECT_TYPE.VIEW
    ) {
      return true;
    }
    return false;
  }

  private showErrMessage(
    msg1: { messageKey: string; datasets: string[]; subMessageKey?: string },
    dialogID: string,
    msg2?: { messageKey: string; datasets: string[] }
  ): void {
    let message = `${this.localizeText(msg1.messageKey).trim()} ${msg1.datasets.join(", ")}.`;

    if (msg1.subMessageKey) {
      message += `\n${this.localizeText(msg1.subMessageKey)}`;
    }

    if (msg2) {
      message += `\n\n${this.localizeText(msg2.messageKey).trim()} ${msg2.datasets.join(", ")}.`;
    }

    MessageHandler.show(
      sap.ui.core.MessageType.Error,
      message,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      dialogID
    );
  }

  // ============================================================ </Add source dataset Handlers> ==============================================

  // ============================================================ <Local Repo Browser Handlers> ======================================================
  /**
   * Existing local repository browser component "MassImportCommand" reused here from ermodeler
   *
   * @private
   * @param {*} oParam - filters, isSingleSelectionAllowed
   * @memberof RFBuilderClass
   */
  private openLocalRepositoryBrowser(oParam): void {
    try {
      this.setBusy(true);
      sap.cdw.ermodeler.MassImportCommand.execute({
        controller: this,
        filters: oParam.filters,
        isSingleSelectionAllowed: oParam?.isSingleSelectionAllowed ? true : false,
      });
    } catch (error) {
      this.setBusy(false);
      this.logError(error);
      MessageHandler.uiError(this.getText("@openRepositoryBrowserError"));
    }
  }

  /**
   * This handler is called by src\components\databuilder\js\commands\importObjectsCommand.ts -> _doImport after dialog is closed
   *
   * @private
   * @param {*} oSelectedObjects
   * @memberof RFBuilderClass
   */
  private onRepositoryObjectsSelected(aSelectedObjects): void {
    this.setBusy(false);
    if (this.currentDatasetBrowserType === "source") {
      const connectionId: string = this.getView()
        .getModel("galileiUiModel")
        .getProperty("/sourceSystems/0/connectionId");
      const connectionType: string = this.getView()
        .getModel("galileiUiModel")
        .getProperty("/sourceSystems/0/connectionType");
      const isSourceHDLFDeltashare: boolean = this.getView()
        .getModel("galileiUiModel")
        .getProperty("/sourceSystems/0/metadata/isSourceHDLFDeltashare");
      const isConnectionTypeObjectStore = Utils.isConnTypeObjectStore(connectionType);
      const isNotHDLFDEltaShare = Utils.isConnectionTypeHdlfWithDeltaShare(connectionType, isSourceHDLFDeltashare);
      // To find all the supported Datasets and primary key conditions
      this.getView()
        .getModel("galileiUiModel")
        .setProperty("/isConnTypeObjectStore", isConnectionTypeObjectStore && !isNotHDLFDEltaShare);
      this.validationsOnSelectedSourceDataset(connectionId, connectionType, aSelectedObjects);
    } else {
      // $dwc selected in target (in map to existing target flow)
      // Update selected task with the selected target dataset information
      const connectionId: string = this.getView()
        .getModel("galileiUiModel")
        .getProperty("/targetSystems/0/connectionId");
      const connectionType: string = this.getView()
        .getModel("galileiUiModel")
        .getProperty("/targetSystems/0/connectionType");
      //  Check if SAC artefact is selected and throw error
      aSelectedObjects = this.preventArtefactsFromSAC(aSelectedObjects, true);
      //  If selected object is not SAC artefact
      if (aSelectedObjects?.length) {
        this.updateTaskwithTargetData(
          connectionId,
          connectionType,
          aSelectedObjects,
          this.oLastContextMenuClickedReplicationTask
        );
        this.modelBuilder.validateModel();
        this.refreshPropertyPanelColumns(this.oLastContextMenuClickedReplicationTask);
      }
    }
  }
  // ============================================================ </Local Repo Browser Handlers> ======================================================

  // ============================================================ <Remote Connection Browser Handlers> ===================================================

  /**
   * Opens remote connection browser for source connection and returns array of selected datasets' metadata in FA format
   *
   * @private
   * @return {*}
   * @memberof RFBuilderClass
   */
  private async openRemoteSourceBrowser(folderPath?) {
    try {
      const connectionType: string = this.getView()
        .getModel("galileiUiModel")
        .getProperty("/sourceSystems/0/connectionType");
      let isSingleSelectionAllowed = false;
      let connectionId: string = this.getView().getModel("galileiUiModel").getProperty("/sourceSystems/0/connectionId");
      const isSourceHDLFDeltashare: boolean = this.getView()
        .getModel("galileiUiModel")
        .getProperty("/sourceSystems/0/metadata/isSourceHDLFDeltashare");
      const connection = { name: connectionId, id: connectionId };
      const isConnectionTypeHdlfWithDeltaShare: boolean = Utils.isConnectionTypeHdlfWithDeltaShare(
        connectionType,
        isSourceHDLFDeltashare
      );
      if (this.checkHDLFSourceFF() && isConnectionTypeHdlfWithDeltaShare) {
        connectionId = Constants.DWC_HANA_CONNECTION_ID;
        connection.id = connectionId;
      }

      let container: string = this.getView().getModel("galileiUiModel").getProperty("/sourceSystems/0/systemContainer");

      // For metadata file selection after folder selection of Objectstore , only sub folders and csv files within the folder must be shown and not of the entire container
      if (Utils.isConnTypeObjectStore(connectionType) && !isConnectionTypeHdlfWithDeltaShare && folderPath) {
        container = folderPath;
        isSingleSelectionAllowed = true;
      }
      // @TODO: Send connectionName from here in name, instead of connectionId
      const aSelectedSources = await this.openRemoteConnectionBrowser({
        connection: connection,
        connectionType: connectionType,
        container: container,
        isSingleSelectionAllowed: isSingleSelectionAllowed,
        isSourceHDLFDeltashare: isSourceHDLFDeltashare,
      });
      return aSelectedSources;
    } catch (error) {
      // MessageHandler.uiError(this.getText("@openRemoteSourceBrowserError")); ToDo - check showDialog() promise handling and update the catch block
    }
  }

  /**
   * Opens remote connection browser for target connection and returns array of selected datasets' metadata in FA format
   *
   * @private
   * @return {*}
   * @memberof RFBuilderClass
   */
  private async openRemoteTargetBrowser() {
    try {
      const connectionId: string = this.getView()
        .getModel("galileiUiModel")
        .getProperty("/targetSystems/0/connectionId");
      const container: string = this.getView()
        .getModel("galileiUiModel")
        .getProperty("/targetSystems/0/systemContainer");
      // @TODO: Send connectionName from here in name, instead of connectionId
      const aSelectedTargets = await this.openRemoteConnectionBrowser({
        connection: { name: connectionId, id: connectionId },
        container: container,
        isSingleSelectionAllowed: true,
      });
      return aSelectedTargets;
    } catch {
      // MessageHandler.uiError(this.getText("@openRemoteTargetBrowserError")); ToDo - check showDialog() promise handling and update the catch block
    }
  }

  /**
   * Opens RemoteObjectsSelector for given connection and returns array of selected datasets' metadata in FA format
   *
   * @private
   * @param {*} oParam
   * @return {*}  {Promise<any>}
   * @memberof RFBuilderClass
   */
  private async openRemoteConnectionBrowser(oParam): Promise<any> {
    try {
      this.setBusy(true);
      const selectedSources = await showDialog(
        require("../../ermodeler/view/RemoteObjectsSelector.view.xml"),
        this.i18nResourceModel.getProperty("@selectSourceObjects"),
        this.i18nResourceModel.getProperty("@ok"),
        this.i18nResourceModel.getProperty("@cancel"),
        "",
        "50%",
        "66%",
        this.i18nResourceModel,
        {
          controller: this,
          selectedRemoteSource: this.getRemoteObjectSelectorOptions({
            connection: { name: oParam.connection.name, id: oParam.connection.id },
            container: oParam.container,
            connectionType: oParam.connectionType,
            isSourceHDLFDeltashare: oParam.isSourceHDLFDeltashare,
          }),
          isSingleSelectionAllowed: oParam.isSingleSelectionAllowed,
          editorCapabilities: oParam.EditorCapabilities,
        }
      );
      this.setBusy(false);
      return selectedSources;
    } catch (error) {
      this.setBusy(false);
      throw new Error("Failed to select datasets");
    }
  }

  /**
   * Creates the parameter in the format as required by RemoteObjectsSelector for browsing remote connections
   * Top level parents are 1. remotes 2. connection id
   * Leaf node has to be a child with loading info & id: loading
   * If browsing has to start from a container, that node needs to be inserted between connection & loading nodes
   * If container info is not provided, browsing will start from root node
   * IMPORTANT: Currently we are using same capabilities as DisDataflow
   *
   * @private
   * @param {*} options
   * @return {*}
   * @memberof RFBuilderClass
   */
  private getRemoteObjectSelectorOptions(options) {
    const dummyLoaderChild = {
      id: "loading",
      namePath: `Connections/${options.connection.name}${options.container}/loading`,
      name: this.getText("@loading"),
      dummy: true,
    };

    const param: any = {
      name: options.connection.name,
      path: "/children/0/children/0",
      expandToPath: "/children/0/children/0/children/0",
      index: 1,
      data: {
        children: [
          {
            id: "remotes",
            name: "Connections",
            type: "remotes",
            hasChildren: true,
            children: [
              {
                id: options.connection.id,
                name: options.connection.name,
                type: Constants.DIS_REMOTE_TYPE,
                namePath: `Connections/${options.connection.name}`,
                hasChildren: true,
                children: [],
              },
            ],
          },
        ],
      },
      editorCapabilities: [Constants.RF_EDITOR_CAPABILITY],
    };
    //  Remote Sources True only for HDLF
    if (this.oFeatures.DWCO_DS_REPLICATION_FLOW_SUPPORT_HDLF_SOURCE) {
      if (Utils.isConnectionTypeHdlfWithDeltaShare(options.connectionType, options.isSourceHDLFDeltashare)) {
        param.editorCapabilities.push(Constants.IS_RF_REMOTE_SOURCES);
      }
    }

    // Filter files for csv only for Objectstores ( HDL- Delta ( data access - deltashare ) not treated as Objectstore)
    if (
      Utils.isConnTypeObjectStore(options.connectionType) &&
      !Utils.isConnectionTypeHdlfWithDeltaShare(options.connectionType, options.isSourceHDLFDeltashare)
    ) {
      param.editorCapabilities.push(Constants.FILTER_FILE_TYPES_OBJ_STORE);
    }
    if (Utils.isCFWSupported(options.connectionType)) {
      let context: CFWContexts,
        configOption: Record<string, any> = {},
        path = "";

      if (Utils.isConnectionTypeCONFLUENT(options.connectionType)) {
        context = CFWContexts.TOPICS;
        configOption["container"] = `/contexts${options.container}`;
        path = options.connectionType;
      } else {
        path = options.container;
      }

      // custom config contains the info needed for CFW api call
      const customConfig = JSON.stringify(
        RFBuilderUtils.getCustomConfig(path, context, options.connectionType, configOption)
      );
      /**
       * In order to send customConfig as an object inside capabilities, we are replacing ',' with ';' in UI,
       * Because capabilities are splitted with ',' in our service.
       * example : "{path:'/abc',flattenschema:false}" --> {path:'/abc';flattenschema:false}
       **/
      param.editorCapabilities.push(customConfig.replace(/\,/g, ";"));
      // useCFW capability informs DWC service to use CFW api instead FA
      param.editorCapabilities.push(Constants.USE_CFW);
    }

    // if container is not available, add dummy loader child as required by browser component,
    // else add container as child & then loader as container's child
    if (!options.container) {
      // check with Aditya why it will be empty
      param.expandToPath = "/children/0/children/0";
      param.data.children[0].children[0].children = [dummyLoaderChild];
    } else {
      let container = options.container;
      if (Utils.isConnectionTypeCONFLUENT(options.connectionType)) {
        // Topics are always at the root in confluent system, previosly selected contexts are not related to listing topics.
        container = "/topics";
      }
      param.data.children[0].children[0].children = [
        {
          id: container,
          name: container,
          type: "node",
          isRemoteNode: true,
          namePath: `Connections/${options.connection.name}${container}`,
          hasChildren: true,
          children: [dummyLoaderChild],
        },
      ];
    }

    return param;
  }
  // ============================================================ </Remote Connection Browser Handlers> ===================================================

  // ============================================================ <Objectstore Source Dataset Browse> ======================================================

  private openFolderBrowserForObjectstoreSource(oEvent) {
    const oModel = this.getModel();
    const sourceSystem = oModel.sourceSystems.get(0);
    const oContainerTreeModel = this.getView().getModel("containerTreeModel");
    oContainerTreeModel.setProperty("/", null);
    oContainerTreeModel.refresh(true); // fix: previous model binded data was visible for few seconds
    const oContainerOwnModel = this.resetContainerDialogOwnModel();
    oContainerOwnModel.isSourceClicked = true;
    oContainerOwnModel.title = this.localizeText("sourceSelectObjectText");
    // oContainerOwnModel.title = "Select Dataset"; Change this if, text title change is needed.
    oContainerOwnModel.connection = sourceSystem.connectionId;
    oContainerOwnModel.connectionType = sourceSystem.connectionType;
    oContainerOwnModel.containerPath = sourceSystem.systemContainer;
    oContainerOwnModel.isFolderSelection = true;
    this.openContainerDialog(oContainerTreeModel, oContainerOwnModel);
  }

  // ============================================================ </Objectstore Source Dataset Browse> ======================================================

  // ============================================================ <Create Task From FA/DWC Metadata> ======================================================

  /**
   * Creates new tasks from selected metadata objects
   * Calls DWC/Flowagent specfic methods based on connection ID
   *
   * @private
   * @param {*} aSelectedObjects
   * @param {*} connectionId
   * @memberof RFBuilderClass
   */
  private async createTasksFromSourceMetadata(connectionId, connectionType, aSelectedObjects) {
    if (aSelectedObjects?.length) {
      const isConnectionTypeDWC = Utils.isConnectionTypeDWC(connectionId, connectionType);
      const isConnectionTypeObjectStore = Utils.isConnTypeObjectStore(connectionType);
      const isSourceHDLFDeltashare: boolean = this.getView()
        .getModel("galileiUiModel")
        .getProperty("/sourceSystems/0/metadata/isSourceHDLFDeltashare");
      const oModel = this.getModel();
      const ABAPcontentType = oModel.replicationFlowSetting.ABAPcontentType;
      const ABAPcontentTypeDisabled = oModel.replicationFlowSetting.ABAPcontentTypeDisabled;
      const newTasks = [];
      const options = {};
      if (Utils.isConnectionTypeABAP(connectionType)) {
        Object.assign(options, {
          ABAPcontentType: ABAPcontentType,
          ABAPcontentTypeDisabled: ABAPcontentTypeDisabled,
        });
      }

      for (const item of aSelectedObjects) {
        const dataset: IFlowagentMetadata | IDwcMetadata = this.parseMetadata(
          connectionId,
          connectionType,
          item,
          undefined,
          options
        );

        let sourceConfig: ISourceConfigdata;
        if (
          (isConnectionTypeObjectStore &&
            !Utils.isConnectionTypeHdlfWithDeltaShare(connectionType, isSourceHDLFDeltashare)) ||
          Utils.isConnectionTypeCONFLUENT(connectionType)
        ) {
          sourceConfig = this.parseConfigSource(item, connectionType);
        }

        if (isConnectionTypeDWC) {
          if (!(dataset as IDwcMetadata).isCDCDataset || dataset.type === "VIEW") {
            // If CDC column are not there or if it is a view , then "Initial only" should be visible
            dataset.deltaDisabled = true; // To make "Initial only" visible
          }
          const aCDCColumns = dataset.columns.filter((col) => {
            if (col.isCDCColumn) {
              dataset.invalidColumns.push(col.name); // Adding CDC columns in source as invalid columns
              return true;
            }
            return false;
          });
          if (aCDCColumns.length === 2) {
            dataset.tableCategory = "DELTA";
          }
        }

        const newTask = this.createNewTask(dataset, sourceConfig);

        if (newTask) {
          newTasks.push(newTask);
        }
      }

      if (newTasks.length) {
        this.modelBuilder.createTasks(newTasks);
        this.modelBuilder.validateModel();
        this.getView().getModel("galileiUiModel").refresh(true);
      }
    }
  }

  /**
   * Updates replication tasks with new/existing target information
   * Existing target -> Populate name, qualifiedName, columns etc from target metadata
   * New target -> copy details from source dataset
   *
   * @private
   * @param {*} tasks
   * @memberof RFBuilderClass
   */
  private updateTasksWithTargetData(tasks) {
    const galileiUiModel = this.getView().getModel("galileiUiModel");
    const replicationTasks = galileiUiModel.getProperty("/replicationTasks");
    const sourceConnectionId = galileiUiModel.getProperty("/sourceSystems/0/connectionId");
    const sourceConnectionType = galileiUiModel.getProperty("/sourceSystems/0/connectionType");
    const sourceContainer = galileiUiModel.getProperty("/sourceSystems/0/systemContainer");
    const targetConnectionId = galileiUiModel.getProperty("/targetSystems/0/connectionId");
    const targetConnectionType = galileiUiModel.getProperty("/targetSystems/0/connectionType");
    const targetContainer = galileiUiModel.getProperty("/targetSystems/0/systemContainer");
    const isTargetConnObjectStore = Utils.isConnTypeObjectStore(targetConnectionType);
    let countOfDatasetsWithDPIDColAdded = 0;
    // we filter out the tasks which don't have oTargetObject, those are oTasks needs to be updated with target oDataset
    const replicationTasksToUpdateTarget = replicationTasks.filter(
      (o: sap.cdw.replicationflow.Task) => !o.targetObject
    );

    replicationTasksToUpdateTarget.forEach((replicationTask: sap.cdw.replicationflow.Task) => {
      const matchingTask = tasks.find((task) => task.name === replicationTask.name);
      let targetDataset: {
        name: string;
        attributes: any[];
        metadata: {
          isNew: boolean;
          invalidColumns: any[];
          isAutoRenamed?: boolean;
          isSACArtefact: boolean;
          isDPIDDataset?: boolean;
        };
        businessName?: any;
      };
      let proposedTransformationObj: ITransform;
      if (matchingTask.targetExists) {
        if (matchingTask.targetDefinition) {
          const targetData = this.parseMetadata(
            targetConnectionId,
            targetConnectionType,
            matchingTask.targetDefinition
          );
          targetDataset = {
            name: targetData.name,
            metadata: {
              isNew: false,
              invalidColumns: targetData.invalidColumns,
              isSACArtefact: targetData.isSACArtefact,
            },
            attributes: targetData.columns,
          };
          if (Utils.isConnectionTypeDWC(targetConnectionId, targetConnectionType)) {
            targetDataset.businessName = targetData.businessName ? targetData.businessName : targetData.name;
          }
        } else {
          // Existing targets for which fetch definition call failed ,then Copy source dataset to target dataset
          const proposedEntities = this.getProposedAttributesAndTransformationForNewtarget(
            replicationTask,
            targetConnectionId,
            targetConnectionType
          );
          proposedTransformationObj = proposedEntities.transformObj;
          targetDataset = {
            name: this.cleanseTargetObjectName(
              replicationTask.sourceObject.name,
              targetConnectionId,
              targetConnectionType
            ),
            metadata: { isNew: false, invalidColumns: [], isSACArtefact: false },
            attributes: proposedEntities.attrList,
          };
          if (Utils.isConnectionTypeDWC(targetConnectionId, targetConnectionType)) {
            targetDataset.businessName = replicationTask.sourceObject.businessName
              ? replicationTask.sourceObject.businessName
              : targetDataset.name;
          } else if (isTargetConnObjectStore) {
            // if targetContainer = "/" then just make name = p1.csv so qualifiedName remains = "/" + p1.csv
            // Code enhancement needed , once object stores are supported as Source
            targetDataset.name = targetContainer === "/" ? targetDataset.name : `/${targetDataset.name}`;
          }
          if (
            Utils.isDatasetABAPSourceWithoutKey(
              sourceConnectionType,
              sourceContainer,
              replicationTask.sourceObject.metadata["isSourceWithoutPK"]
            )
          ) {
            targetDataset.metadata.isDPIDDataset = true;
            countOfDatasetsWithDPIDColAdded++;
          }
        }
      } else {
        // if target doesn't exists on matching task , then Copy source dataset to target dataset
        const proposedEntities = this.getProposedAttributesAndTransformationForNewtarget(
          replicationTask,
          targetConnectionId,
          targetConnectionType
        );
        proposedTransformationObj = proposedEntities.transformObj;
        targetDataset = {
          name: this.cleanseTargetObjectName(
            replicationTask.sourceObject.name,
            targetConnectionId,
            targetConnectionType
          ),
          metadata: { isNew: true, invalidColumns: [], isSACArtefact: false },
          attributes: proposedEntities.attrList,
        };
        if (replicationTask.sourceObject.name !== targetDataset.name) {
          targetDataset.metadata.isAutoRenamed = true;
        }

        if (Utils.isConnectionTypeDWC(targetConnectionId, targetConnectionType)) {
          targetDataset.businessName = replicationTask.sourceObject.businessName
            ? replicationTask.sourceObject.businessName
            : targetDataset.name;
        } else if (isTargetConnObjectStore) {
          // if targetContainer = "/" then just make name = p1.csv so qualifiedName remains = "/" + p1.csv
          // Code enhancement needed , once object stores are supported as Source
          targetDataset.name = targetContainer === "/" ? targetDataset.name : `/${targetDataset.name}`;
        }
        if (
          Utils.isDatasetABAPSourceWithoutKey(
            sourceConnectionType,
            sourceContainer,
            replicationTask.sourceObject.metadata["isSourceWithoutPK"]
          )
        ) {
          targetDataset.metadata.isDPIDDataset = true;
          countOfDatasetsWithDPIDColAdded++;
        }
      }
      this.modelBuilder.createDataset(targetDataset, "target", replicationTask);
      if (Utils.isConnectionTypeDWC(targetConnectionId, targetConnectionType)) {
        if (matchingTask.targetDefinition && replicationTask.targetObject) {
          replicationTask.targetObject.setObjectStatus(matchingTask.targetDefinition["#objectStatus"]);
        }
        if (matchingTask.targeSupported === false) {
          replicationTask.targetObject.isSupportedDataset = false;
        }
      }
      this.addAutoProjection(replicationTask, proposedTransformationObj);
    });

    this.modelBuilder.validateModel();
    this.getView().getModel("galileiUiModel").refresh(true);
    this.refreshPropertyPanelColumns();
    if (
      Utils.isConnTypeABAPCdsOrOdp(sourceConnectionType, sourceContainer) &&
      !Utils.isConnectionTypeKafka(targetConnectionType) &&
      !Utils.isConnectionTypeDwcLTF(targetConnectionId, targetConnectionType)
    ) {
      Utils.showToastForDPIDColumn(countOfDatasetsWithDPIDColAdded);
    }

    if (this.aSelectedReplicationTask.length > 0) {
      sap.ui
        .getCore()
        .getEventBus()
        .publish("refreshPropertyPanelColumns", "updateColumnList", this.aSelectedReplicationTask[0]);
      sap.ui.getCore().getEventBus().publish("refreshDatasetSettings", "updateDatasetSettings");
      this.enableOrDisableRFTransformation("select");
      this.changeLabelInToolbarProjectionButton(this.aSelectedReplicationTask[0]);
    }
  }
  /**
   * Updates column names (specifc to connection type)
   * Converts connection specific compatible datatype
   * Returns mapping list if there is change is column  name
   *
   * @param replicationTask
   * @param targetConnectionType
   * @returns
   */
  private getProposedAttributesAndTransformationForNewtarget(
    replicationTask: sap.cdw.replicationflow.Task,
    targetConnectionId: string,
    targetConnectionType: string,
    includeCDC: boolean = true
  ): IProposedEntities {
    const sourceConnectionType = replicationTask.sourceSystem.connectionType;
    const sourceContainer = replicationTask.sourceSystem.systemContainer;
    let result: IProposedEntities = {
      attrList: replicationTask.sourceObject.attributes.toArray(),
      transformObj: { attributeMappings: [], name: "", filters: [] },
    };
    // Attributes for Target Connection Type
    if (Utils.isConnectionTypeGBQ(targetConnectionType)) {
      result = RFBuilderUtils.createGBQAttributes(result.attrList, includeCDC);
    }
    if (Utils.isConnectionTypeCONFLUENT(targetConnectionType)) {
      result = RFBuilderUtils.createConfluentAttributes(result.attrList, includeCDC);
    }

    if (Utils.isConnectionTypeDWC(targetConnectionId, targetConnectionType)) {
      if (Utils.isConnectionTypeDwcLTF(targetConnectionId, targetConnectionType)) {
        const isLTF = true;
        result = RFBuilderUtils.createDWCAttributes(result.attrList, includeCDC, isLTF);
      } else {
        const isLTF = false;
        let overwriteIncludeCDC = false;
        if (
          this.checkHDLFSourceFF() &&
          Utils.isConnectionTypeHdlfWithDeltaShare(
            sourceConnectionType,
            replicationTask.sourceSystem.metadata["isSourceHDLFDeltashare"]
          ) &&
          includeCDC
        ) {
          overwriteIncludeCDC = true;
        }
        result = RFBuilderUtils.createDWCAttributes(result.attrList, overwriteIncludeCDC, isLTF);
      }
    }
    if (Utils.isConnectionTypeSignavio(targetConnectionType)) {
      result = RFBuilderUtils.createSignavioAttributes(result.attrList, includeCDC);
    }
    if (Utils.isConnTypeMsOneLake(targetConnectionType)) {
      result = RFBuilderUtils.createMsOneLakeAttributes(result.attrList, includeCDC);
    }

    if (Utils.isConnTypeSFTP(targetConnectionType)) {
      result = RFBuilderUtils.createSFTPAttributes(result.attrList, includeCDC);
    }

    if (
      Utils.isDatasetABAPSourceWithoutKey(
        sourceConnectionType,
        sourceContainer,
        replicationTask.sourceObject.metadata["isSourceWithoutPK"]
      )
    ) {
      result = RFBuilderUtils.createDPIDColumnForABAPWithoutKey(
        targetConnectionId,
        targetConnectionType,
        result.attrList,
        result.transformObj
      );
    }
    return result;
  }

  /**
   *
   *Adds auto projection if there is any attribute mapping
   *
   * @param replicationTask
   * @param attributeMapping
   */
  addAutoProjection(replicationTask: sap.cdw.replicationflow.Task, transformObj: ITransform) {
    if (transformObj?.name && transformObj?.attributeMappings.length) {
      this.modelBuilder.createTransform(transformObj, replicationTask);
    }
  }
  /**
   * Creates and returns new task from source dataset, with empty target info
   *
   * @param {*} sourceDataset
   * @return {*}
   * @memberof RFBuilderClass
   */
  private createNewTask(sourceDataset, sourceConfig) {
    if (!sourceDataset) {
      return;
    }

    const task: any = {
      sourceObject: {
        name: sourceDataset.name,
        datasetType: "source",
        attributes: sourceDataset.columns,
        businessName: sourceDataset.businessName,
        metadata: {
          isDeltaDisabled: sourceDataset.deltaDisabled === true,
          invalidColumns: sourceDataset.invalidColumns,
          ABAPExitList: sourceDataset.ABAPExitList,
          isSourceObjectAddedFromUI: true,
          filePath: undefined,
        },
      },
      targetObject: {
        name: "",
        datasetType: "target",
        attributes: [],
        metadata: {
          isNew: true,
          invalidColumns: [],
        },
      },
    };

    const sourceSystem = this.oModel.sourceSystems.get(0);
    const sourceConnectionType = sourceSystem.connectionType;
    const sourceConnectionId = sourceSystem.connectionId;
    const sourceContainer = sourceSystem.systemContainer;
    const targetConnectionId = this.oModel.targetSystems.get(0)?.connectionId;
    const targetConnectionType = this.oModel.targetSystems.get(0)?.connectionType;

    const isSourceHDLFDeltashare = this.oModel.sourceSystems.get(0).metadata["isSourceHDLFDeltashare"];
    const isConnectionTypeHdlfWithDeltaShare = Utils.isConnectionTypeHdlfWithDeltaShare(
      sourceConnectionType,
      isSourceHDLFDeltashare
    );

    //  Source Objectstore ( HDLF Deltashare is not objectstore )
    if (
      (Utils.isConnTypeObjectStore(sourceConnectionType) && !isConnectionTypeHdlfWithDeltaShare) ||
      Utils.isConnectionTypeCONFLUENT(sourceConnectionType)
    ) {
      task.sourceObject.sourceSchemaProperties = RFBuilderUtils.getSourceProperties(
        this.oModel.sourceSystems.get(0).connectionType,
        sourceConfig
      );
      task.sourceObject.properties = {};
      //  Load type should be initial + delta for Confluent Source
      if (Utils.isConnectionTypeCONFLUENT(sourceConnectionType)) {
        task.loadType = Constants.LOAD_TYPE.INITIALDELTA;
      }

      if (Utils.isConnTypeObjectStore(sourceConnectionType)) {
        task.sourceObject.name = Utils.getSourceObjectNameForSourceObjectstore(
          sourceDataset.name,
          sourceDataset.qualifiedName,
          sourceContainer
        );
        task.sourceObject.metadata.filePath = sourceDataset.qualifiedName;
      }
    }

    if (sourceDataset.tableCategory) {
      if (Utils.isConnectionTypeDWC(sourceConnectionId, sourceConnectionType) && sourceDataset.isCDCDataset) {
        task.sourceObject.properties = {};
        task.sourceObject.properties["com.sap.datasuite.tableCategory"] = "DELTA";
        task.sourceObject.properties["com.sap.datasuite.cdc.mode.column"] = sourceDataset.columns.find(
          (col) => col.CDCColumnType === "mode"
        ).name;
        task.sourceObject.properties["com.sap.datasuite.cdc.timestamp.column"] = sourceDataset.columns.find(
          (col) => col.CDCColumnType === "timestamp"
        ).name;
        task.sourceObject.attributes = sourceDataset.columns.filter((col) => {
          return col.isCDCColumn !== true;
        });
        // Adding watermark prop to source property
        task.sourceObject.properties["com.sap.datasuite.cdc.subscriber.schema"] = "true";
      } else {
        task.sourceObject.properties = {};
        task.sourceObject.properties["com.sap.abap.tableCategory"] = sourceDataset.tableCategory;
      }
    }
    //  Load type should be initial + delta for new target for HDLF Source
    if (this.checkHDLFSourceFF() && isConnectionTypeHdlfWithDeltaShare && task.targetObject.metadata.isNew) {
      task.loadType = Constants.LOAD_TYPE.INITIALDELTA;
    }

    // load type should be default to initial + delta for SAP Datasphere -> LTF target
    if (Utils.isConnectionTypeDwcLTF(targetConnectionId, targetConnectionType)) {
      task.loadType = Constants.LOAD_TYPE.INITIALDELTA;
    }

    if (task.targetObject.metadata.isNew && Utils.isConnTypeABAPCdsOrOdp(sourceConnectionType, sourceContainer)) {
      const keysInSource = RFBuilderUtils.keysInObject(task.sourceObject.attributes);
      if (keysInSource < 1) {
        task.sourceObject.metadata.isSourceWithoutPK = true;
        task.loadType = Constants.LOAD_TYPE.INITIAL;
      } else {
        task.sourceObject.metadata.isSourceWithoutPK = false;
      }
    }
    // If source connection is DWC or HANA , then add the type information , else it's default tabular always for rest for the connections
    if (
      Utils.isConnectionTypeDwcHANA(sourceConnectionId, sourceConnectionType) ||
      Utils.isConnectionTypeHANA(sourceConnectionId, sourceConnectionType)
    ) {
      task.sourceObject.metadata.type = sourceDataset.type;
    }
    return task;
  }

  // ============================================================ </Create Task From FA/DWC Metadata> ======================================================

  // ============================================================ <Target Dataset Validation Handlers> =====================================================

  /**
   * Target container change handler
   *
   * @private
   * @memberof RFBuilderClass
   */
  private async triggerTaskRemapping() {
    const tasks = await this.validateTargets();
    if (tasks) {
      this.updateTasksWithTargetData(tasks);
    }
  }

  /**
   * 1. Validates if target with same name as source already exist in target container
   * 2. Also, populates definition for existing targets
   * 3. Updates replication tasks with existing/new target information
   *
   * @private
   * @memberof RFBuilderClass
   */
  private async validateTargets() {
    this.setBusy(true);
    const rejectedPromiseList = [];
    const galileiUiModel = this.getView().getModel("galileiUiModel");
    const targetConnectionId = galileiUiModel.getProperty("/targetSystems/0/connectionId");
    const targetConnectionType = galileiUiModel.getProperty("/targetSystems/0/connectionType");

    try {
      const tasks = await this.findExistingTargets();

      // Update definition for existing remote local targets
      if (!Utils.isConnectionTypeDWC(targetConnectionId, targetConnectionType)) {
        await this.updateMetadataForExistingRemoteTargets(tasks, rejectedPromiseList);
      } else {
        await this.updateMetadataForExistingLocalTargets(tasks, rejectedPromiseList);
      }

      this.setBusy(false);
      return tasks;
    } catch (error) {
      this.logError(error);
      MessageHandler.uiError(this.getText("@validatingTargetsError"));
      this.setBusy(false);
    }
  }

  // @TODO: Currently, targets are searched only within response of first browse call,
  // This logic needs to be enhanced to either keep calling browse API till all target nodes have been checked
  // or make definition call for each target to verify its existence

  /**
   * Makes browse calls for local repo & remote connections
   * Iterates through tasks, and checks if target with same name as source name exists in the browse API response
   * If target is found task.targetExists flag is set to true, lse it is assumed as new target
   *
   * @private
   * @return {*}
   * @memberof RFBuilderClass
   */
  private async findExistingTargets() {
    // @TODO: IMPORTANT: This logic should be updated to either call browse again and again till all target objects are covered
    // or make definition call for each task -> sourceObject name in target container to verify

    const galileiUiModel = this.getView().getModel("galileiUiModel");
    const allTasks = [...galileiUiModel.getProperty("/replicationTasks")];
    const connectionId = galileiUiModel.getProperty("/targetSystems/0/connectionId");
    const connectionType = galileiUiModel.getProperty("/targetSystems/0/connectionType");

    // we filter out the tasks which don't have oTargetObject, those are candidates for evaluation
    const tasks = allTasks.filter((o: sap.cdw.replicationflow.Task) => !o.targetObject);

    try {
      if (Utils.isConnectionTypeDWC(connectionId, connectionType)) {
        for (const task of tasks) {
          const proposedTargetName = this.cleanseTargetObjectName(task.sourceObject.name, connectionId, connectionType);
          task.targetExists = await isRepositoryObjectExist(this.spaceName, proposedTargetName);
        }
      } else if (
        Utils.isConnectionTypeKafka(connectionType) ||
        Utils.isConnectionTypeCONFLUENT(connectionType) ||
        Utils.isConnectionTypeSignavio(connectionType)
      ) {
        // for kafka just marking as new target for now
        tasks.forEach((task) => {
          task.targetExists = false;
        });
      } else {
        const container = galileiUiModel.getProperty("/targetSystems/0/systemContainer"); // Provides the container path
        const nodes = await APIService.getChildren(container, this.getSpaceName(), connectionId);

        tasks.forEach((task) => {
          const proposedTargetName = this.cleanseTargetObjectName(task.sourceObject.name, connectionId, connectionType);
          let nodeFound = false;
          if (Utils.isConnectionTypeGBQ(connectionType)) {
            // Retrieving dataset name from id, since for GBQ its observed that name ~ '<name> <description>', this could be done for other connections as well later but need to test
            nodeFound = nodes.find((node) => node.id?.split("/")?.pop() === proposedTargetName);
          } else {
            nodeFound = nodes.find((node) => node.name === proposedTargetName);
          }
          task.targetExists = nodeFound ? true : false;
        });
      }

      return Promise.resolve(tasks);
    } catch (error) {
      let errDetails;
      if (error && error[0]?.responseJSON?.details) {
        errDetails = error[0].responseJSON.details.body && error[0].responseJSON.details.body.message;
      }
      errDetails = errDetails ? errDetails : this.localizeText("failToFetchData");
      return Promise.reject(errDetails);
    }
  }

  /**
   * Iterates through each task and fetches the metadata for targets
   * that exist in target container and has same name as a source dataset
   *
   * @private
   * @param {*} tasks
   * @return {*}
   * @memberof RFBuilderClass
   */
  private async updateMetadataForExistingRemoteTargets(tasks, rejectedPromiseList: any[]) {
    const galileiUiModel = this.getView().getModel("galileiUiModel");
    const targetConnectionId = galileiUiModel.getProperty("/targetSystems/0/connectionId");
    const targetContainer = galileiUiModel.getProperty("/targetSystems/0/systemContainer");
    const targetConnectionType = galileiUiModel.getProperty("/targetSystems/0/connectionType");

    try {
      return Promise.all(
        tasks.map(async (task) => {
          if (task.targetExists) {
            if (Utils.isConnTypeObjectStore(targetConnectionType)) {
              // DW18-1218 : for object store we never make any metadata calls(irrespective of target existing or not) , thus task.targetDefinition is always empty / undefined
              task.targetDefinition = undefined;
            } else {
              const targetName = this.cleanseTargetObjectName(
                task.sourceObject.name,
                targetConnectionId,
                targetConnectionType
              );
              // @TODO: Verify if this logic of creating target qualified name will always work?
              const targetQualifiedName = `${targetContainer}/${targetName}`;
              const item = {
                id: targetQualifiedName,
                name: targetName,
                remote: { format: "dis", remoteSource: targetConnectionId, uniqueName: targetQualifiedName },
              };
              try {
                // Added additional trycatch block to handle metadata api failure, which results in overall failure of the source data set selection
                task.targetDefinition = await this.getRemoteMetadata(item);
              } catch (error) {
                task.targetDefinition = undefined;
                rejectedPromiseList.push(error);
              }
            }
          }
        })
      );
    } catch (error) {
      this.logError(error);
    }
  }

  /**
   * Iterates through each task and fetches the metadata for targets
   * that exist in target container
   *
   * @private
   * @param {*} tasks
   * @return {*}
   * @memberof RFBuilderClass
   */
  private async updateMetadataForExistingLocalTargets(tasks, rejectedPromiseList) {
    const galileiUiModel = this.getView().getModel("galileiUiModel");
    const connectionId = galileiUiModel.getProperty("/targetSystems/0/connectionId");
    const connectionType = galileiUiModel.getProperty("/targetSystems/0/connectionType");
    try {
      return Promise.all(
        tasks.map(async (task) => {
          if (task.targetExists) {
            const targetName = this.cleanseTargetObjectName(task.sourceObject.name, connectionId, connectionType);
            try {
              const response = await getRepositoryObject(this.spaceName, targetName, { details: ["csn"] });
              if (response) {
                if (response["#technicalType"] === RepositoryObjectType.DWC_LOCAL_TABLE) {
                  task.targetDefinition = response;
                  task.targetDefinition.definitions = response.csn.definitions;
                } else {
                  // for feature parity since ga, we make targetExists as false for non DWC_LOCAL_TABLE targets
                  task.targetExists = false;
                  task.targeSupported = false;
                }
              }
            } catch (error) {
              rejectedPromiseList.push(error);
            }
          }
        })
      );
    } catch (error) {
      this.logError(error);
    }
  }

  /**
   * Fetches metadata for remote objects
   *
   * @param {*} item
   * @return {*}
   * @memberof RFBuilderClass
   */
  public async getRemoteMetadata(item: any) {
    let metadata: any;
    if (item?.remote) {
      try {
        const id = item.originalId ? item.originalId : item.id;
        const nameAndlabel = getEntityNameAndLabel(true, item.name, item.remote ? item.remote.uniqueName : id);
        const newEntityId = nameAndlabel.name;
        metadata = (await getCSNDefinition(
          { ...item.remote, entity: newEntityId, label: nameAndlabel.label },
          this.spaceGUID
        )) as any;
        metadata.definitions = { dummy: {} };
        metadata.getCSNFailed = false;
      } catch (error) {
        let errDetails;
        if (error && error[0]?.responseJSON?.details) {
          errDetails = error[0].responseJSON.details.body && error[0].responseJSON.details.body.message;
        }
        errDetails = errDetails ? errDetails : this.localizeText("failToFetchRemoteMetadata");
        throw new Error(errDetails);
      }
    }
    return Promise.resolve(metadata);
  }

  // ============================================================ </Target Dataset Validation Handlers> ====================================================

  // ===================================================================== <Utility Functions> ============================================================

  /**
   * Pass source object name as param, incase it has "/" then take the last part as target object name
   * fix:DW101-23411
   *
   * @private
   * @param {string} name
   * @return {*}  {string}
   * @memberof RFBuilderClass
   */
  private cleanseTargetObjectName(name: string, connectionId: string, connectionType: string): string {
    const aEntires = name.split("/");
    let splittedName: string = aEntires[aEntires.length - 1];
    if (splittedName) {
      if (Utils.isConnectionTypeDWC(connectionId, connectionType)) {
        splittedName = NamingHelper.getNameInputValidator().deriveTechnicalName(splittedName, NameUsage.entity);
      } else {
        splittedName = Utils.cleanseNonDWCTargentName(connectionId, connectionType, splittedName);
      }
    }
    if (splittedName === "") {
      splittedName = Utils.makeUniqueWithTimestamp(Constants.NEW_TARGET_OBJECT_PREFIX);
    }
    return splittedName;
  }

  /**
   * Calls functions to parse csn/FA metadata basd on connectionId, returns parsed data
   *
   * @private
   * @param {string} connectionId
   * @param {*} dataset
   * @param {string} [objectType]
   * @return {*}
   * @memberof RFBuilderClass
   */
  private parseMetadata(
    connectionId: string,
    connectionType: string,
    dataset: any,
    objectType?: string,
    options: any = {}
  ): IFlowagentMetadata | IDwcMetadata {
    if (Utils.isConnectionTypeDWC(connectionId, connectionType)) {
      return this.parseDWCMetadata(dataset, objectType);
    } else {
      return this.parseFlowagentMetadata(dataset, connectionId, connectionType, options);
    }
  }

  /**
   * Parse csn data for local DWC connection
   *
   * @private
   * @param {*} csnData
   * @param {string} [objectType]
   * @return {*}  {*}
   * @memberof RFBuilderClass
   */
  private parseDWCMetadata(csnData: any, objectType?: string): IDwcMetadata {
    // @TODO: Check src\components\dataflowmodeler\js\utils.ts -> getDWCMetadata() for more validations as per the requriements
    if (objectType === undefined && csnData?.type) {
      objectType = isViewType(csnData.type) ? Constants.SOURCE_OBJECT_TYPE.VIEW : Constants.SOURCE_OBJECT_TYPE.TABLE;
    }

    let name = csnData.technicalName || csnData.name;
    let definition = csnData.definitions[name];
    let businessName = csnData.definitions[name]["@EndUserText.label"]
      ? csnData.definitions[name]["@EndUserText.label"]
      : name;

    const columnData = { columns: [], invalidColumns: [] };
    const cdcColumns = [];

    let deltaAnnotation = definition?.["@DataWarehouse.delta"];
    const isSACArtefact = csnData.definitions[name]["@DataWarehouse.external.schema"] ? true : false;
    if (deltaAnnotation) {
      const isActiveRecordsView = deltaAnnotation.type?.["#"] === "ACTIVE" && deltaAnnotation.deltaFromEntities?.[0];
      if (isActiveRecordsView) {
        // always use definition of delta capture tables
        definition = csnData.definitions[deltaAnnotation.deltaFromEntities[0]];
        deltaAnnotation = definition?.["@DataWarehouse.delta"];
      }
      if (
        deltaAnnotation &&
        deltaAnnotation.modeElement?.["="] &&
        deltaAnnotation.dateTimeElement?.["="] &&
        deltaAnnotation.type?.["#"] === "UPSERT"
      ) {
        // set to name if @DataWarehouse.enclosingObject is not present
        // note the scenario will only come with unreleased leftover delta table
        name = definition["@DataWarehouse.enclosingObject"] ? definition["@DataWarehouse.enclosingObject"] : name;
        cdcColumns.push({ type: "timestamp", columnName: deltaAnnotation.dateTimeElement["="] });
        cdcColumns.push({ type: "mode", columnName: deltaAnnotation.modeElement["="] });
      }
    }

    if (definition?.elements) {
      const elements = definition.elements;
      for (const key in elements) {
        // check for analytical measure and skip column
        if (objectType === Constants.SOURCE_OBJECT_TYPE.VIEW && isAnalyticMeasureElement(elements[key])) {
          continue;
        }

        const item = this.getItemFromDefinition(csnData.definitions, elements[key]);

        if (item) {
          const type = Constants.rmsCdsTypeMapping[item.type];
          if (type) {
            // LOB columns are not supported by RMS (below specs are large types as per DI RMS)
            // fix DW101-25466
            // i.e. cds.LargeBinary, cds.LargeString don't have length
            if (
              (type === "string" || type === "binary") &&
              (!item.length || item.length > 5000) &&
              item.type !== "cds.UUID"
            ) {
              columnData.invalidColumns.push(key);
            } else {
              const column: any = {
                name: key,
                businessName: item["@EndUserText.label"] ? item["@EndUserText.label"] : item.name,
                datatype: type,
                length: item.length,
                precision: item.precision,
                scale: item.scale,
                key: item.key,
                filterNotAllowed: !!item.filterNotAllowed,
              };
              // uuid type should have fixed length
              if (item.type === "cds.UUID") {
                column.length = 36;
              }
              // cdc columns
              if (cdcColumns.length > 1) {
                const col = cdcColumns.find((o) => o.columnName === column.name);
                if (col) {
                  column.isCDCColumn = true;
                  column.CDCColumnType = col.type;
                }
              }
              // DPID Column
              if (RFBuilderUtils.isDPIDColumn(item)) {
                column.isDPIDColumn = true;
              }
              columnData.columns.push(column);
            }
          } else {
            columnData.invalidColumns.push(key);
          }
        }
      }
    }

    const details: IDwcMetadata = {
      name: name,
      businessName: businessName,
      columns: columnData.columns,
      invalidColumns: columnData.invalidColumns,
      isCDCDataset: cdcColumns.length > 1,
      deltaDisabled: false,
      isSACArtefact: isSACArtefact,
      type: objectType,
    };

    return details;
  }

  /**
   * This function will be called recursively to resolve simple type
   * @param definitions
   * @param item
   */
  private getItemFromDefinition(definitions, item) {
    if (item.type && !item.type.startsWith("cds.")) {
      // simple type
      if (definitions[item.type]) {
        const subItem = definitions[item.type];
        return this.getItemFromDefinition(definitions, subItem);
      }
    }
    return item;
  }

  private parseConfigSource(sourceDataset, sourceConnectionType: string): ISourceConfigdata {
    const configSourceProperties = sourceDataset.dataAccessConfiguration;
    let details: ISourceConfigdata = {};
    if (Utils.isConnTypeObjectStore(sourceConnectionType)) {
      details = {
        colDelimiter: configSourceProperties.colDelimiter,
        header: configSourceProperties.header,
        format: "CSV",
        charset: configSourceProperties.charset,
      };
    } else if (Utils.isConnectionTypeCONFLUENT(sourceConnectionType)) {
      details = {
        confluentReadSchemaSubject: configSourceProperties.confluentSubject,
        confluentReadSchemaVersion: configSourceProperties.confluentTopicVersion,
        confluentSchemaType: configSourceProperties.confluentSchemaType,
        confluentMessageIdColumn: configSourceProperties.confluentMessageIdColumn,
        includeTechnicalKey: true,
        includeNotExpandedArraysAndMaps: false,
      };
    }
    return details;
  }

  /**
   *  Parse flowagent metadata for remote objects and returns the data
   * @private
   * @param {*} dataset
   * @param {*} connectionType
   * @return {*}  {IFlowagentMetadata}
   * @memberof RFBuilderClass
   */
  private parseFlowagentMetadata(dataset, connectionId, connectionType, options): IFlowagentMetadata {
    const schema = dataset.schema;
    const columnData = this.getColumnsFromSchema(schema, connectionType, options);

    const details: IFlowagentMetadata = {
      name: dataset.remoteObjectReference.name,
      qualifiedName: dataset.remoteObjectReference.qualifiedName,
      columns: columnData.columns,
      invalidColumns: columnData.invalidColumns,
      deltaDisabled: false,
    };

    // ABAP
    if (Utils.isConnectionTypeABAP(connectionType)) {
      if (dataset.remoteObjectReference?.description) {
        details.businessName = Utils.initCapForBusinessName(dataset.remoteObjectReference.description);
      }

      // if dataset.properties has (p.namespace.startsWith("com.sap.abap") && p.name === 'isDeltaEnabled' && p.value === 'X') => set details.deltaDisabled false
      details.deltaDisabled = dataset.properties?.some(
        (elem) => elem.namespace.startsWith("com.sap.abap") && elem.name === "isDeltaEnabled" && elem.value === "X"
      )
        ? false
        : true;

      // if dataset.properties has {"namespace": "com.sap.abap.*", "name": "tableCategory", "value": "CLUSTER", "valueDescription": "Cluster table"}
      const tableCategory = dataset.properties?.find(
        (elem) => elem.namespace.startsWith("com.sap.abap") && elem.name === "tableCategory" && elem.value !== ""
      );
      if (tableCategory) {
        details.tableCategory = tableCategory.value;
      }
      const ABAPExitList = this.prepareABAPExitList(dataset);
      if (ABAPExitList.length > 0) {
        details.ABAPExitList = ABAPExitList;
      }
    }
    // To get Object Type for HANA (whether it is a table or view)
    if (Utils.isConnectionTypeHANA(connectionId, connectionType)) {
      details.type = dataset.remoteObjectReference.remoteObjectType;
      details.deltaDisabled = details.type === Constants.SOURCE_OBJECT_TYPE.VIEW ? true : details.deltaDisabled;
    }
    return details;
  }

  private prepareABAPExitList(dataset): IABAPExits[] {
    const exitsAvailable = dataset.properties?.find(
      (elem) => elem.namespace.startsWith("com.sap.abap") && elem.name === "customerExits" && elem.value !== ""
    );
    let ABAPExits: IABAPExits[] = [];
    if (exitsAvailable && Array.isArray(JSON.parse(exitsAvailable.value))) {
      ABAPExits = JSON.parse(exitsAvailable.value);
      return ABAPExits;
    }
    return ABAPExits;
  }

  /**
   * Creates columns from schema information and returns it
   *
   * @private
   * @param {*} schema
   * @return {*}
   * @memberof RFBuilderClass
   */
  private getColumnsFromSchema(schema, connectionType, options) {
    const columnData = { columns: [], invalidColumns: [], hiddenColumns: [] };
    const uniqueKeys = this.getUniqueKeys(schema);

    if (schema?.tableBasedRepresentation?.attributes) {
      schema.tableBasedRepresentation.attributes.forEach((item) => {
        const isColumnHidden = this.isHiddenColumn(item);
        const isColumnInvalid = this.isInvalidColumn(item, connectionType);

        if (isColumnHidden) {
          columnData.hiddenColumns.push(item.name);
        } else if (isColumnInvalid) {
          columnData.invalidColumns.push(item.name);
        } else {
          let templateType = item.templateType;
          let length,
            precision,
            scale,
            uniqueKey = false,
            metadata = {};

          const filterNotAllowed = !!item.filterNotAllowed;

          // Set length only for string and binary types
          if (templateType === "string" || templateType === "binary") {
            length = item.length;
          }

          // Set precision and scale only for decimal types
          if (templateType === "decimal") {
            precision = item.precision;
            scale = item.scale;
          }

          // Set unique key if the column name is in uniqueKeys
          if (uniqueKeys.includes(item.name)) {
            uniqueKey = true;
          }

          let isDPIDColumn = false;

          // Handle ABAP content type and retrieve native content type details
          if (Utils.isConnectionTypeABAP(connectionType)) {
            const abapResult = this.handleABAPContentType(item, templateType, options);
            templateType = abapResult.templateType;
            length = abapResult.length ?? length;
            precision = abapResult.precision ?? precision;
            scale = abapResult.scale ?? scale;
            metadata = abapResult.metadata;
            isDPIDColumn = abapResult.isDPIDColumn;
          }

          const column = {
            name: item.name,
            businessName:
              item.descriptions && item.descriptions[0]?.value
                ? Utils.initCapForBusinessName(item.descriptions[0].value)
                : undefined,
            datatype: templateType,
            length: length,
            precision: precision,
            scale: scale,
            key: uniqueKey,
            filterNotAllowed: filterNotAllowed,
            isDPIDColumn: isDPIDColumn,
            metadata: metadata,
          };

          columnData.columns.push(column);
        }
      });
    }

    return columnData;
  }
  /**
   * Handles ABAP-specific content type metadata processing.
   *
   * Applies native content type mapping and ABAP metadata generation based on options.
   *
   * @param item - The attribute (column) object from schema.
   * @param templateType - The original template type.
   * @param options - Optional ABAP-specific options.
   * @returns An object containing updated type/length/scale/precision/metadata/isDPIDColumn.
   */
  private handleABAPContentType(
    item: any,
    templateType: string,
    options?: any
  ): {
    templateType: string;
    length?: number;
    precision?: number;
    scale?: number;
    metadata: Record<string, any>;
    isDPIDColumn: boolean;
  } {
    let length: number | undefined;
    let precision: number | undefined;
    let scale: number | undefined;
    const metadata = {};
    let isDPIDColumn = false;

    const nContentTypeObj = this.getNativeContentType(item, options);
    if (nContentTypeObj) {
      if (
        Object.keys(options).length > 0 &&
        Constants.ABAPcontentTypeDisabled in options &&
        !options.ABAPcontentTypeDisabled
      ) {
        const contentType = this.createMetadata(
          templateType,
          nContentTypeObj.nativeLength,
          nContentTypeObj.nativePrecision,
          nContentTypeObj.nativeScale
        );
        Object.assign(metadata, contentType);
        if (options.ABAPcontentType === Constants.ABAP_CONTENT_TYPE.nativeType) {
          templateType = nContentTypeObj.nativeContentType;
          length = nContentTypeObj.nativeLength;
          precision = nContentTypeObj.nativePrecision;
          scale = nContentTypeObj.nativeScale;
        }
      } else {
        templateType = nContentTypeObj.nativeContentType;
        length = nContentTypeObj.nativeLength;
      }
    }
    if (RFBuilderUtils.isDPIDColumn(item)) {
      isDPIDColumn = true;
    }

    return { templateType, length, precision, scale, metadata, isDPIDColumn };
  }

  private createMetadata(templateType, length, precision, scale) {
    let nativeType = {};
    let TemplateType = {};
    let metadata = {};

    // Adjust native type and TemplateType based on the column's template type
    switch (templateType) {
      case "date":
      case "time":
      case "boolean":
        nativeType = { datatype: "string", length: length };
        TemplateType = { datatype: templateType };
        metadata = { nativeType, TemplateType };
        break;
      case "timestamp":
        nativeType = { datatype: "decimal", precision, scale };
        TemplateType = { datatype: templateType };
        metadata = { nativeType, TemplateType };
        break;
      default:
        metadata = {};
        break;
    }

    return metadata;
  }

  /**
   *
   * @private
   * @param {*} schema
   * @return {*}  {string[]}
   * @memberof RFBuilderClass
   */
  private getUniqueKeys(schema): string[] {
    const uniqueKeys = schema?.tableBasedRepresentation?.uniqueKeys;
    const uniqueKeysList = [];
    if (Array.isArray(uniqueKeys)) {
      const isPrimaryProperty = uniqueKeys.some(
        (uniqueKey: any) => uniqueKey.isPrimary === true || uniqueKey.isPrimary === false
      );
      if (isPrimaryProperty) {
        for (const uniqueKey of uniqueKeys) {
          if (uniqueKey && uniqueKey.isPrimary && Array.isArray(uniqueKey.attributeReferences)) {
            for (const attributeReference of uniqueKey.attributeReferences) {
              uniqueKeysList.push(attributeReference);
            }
          }
        }
      } else {
        // Fallback if the isPrimaryProperty is not present in the csn
        for (const uniqueKey of uniqueKeys) {
          if (uniqueKey && Array.isArray(uniqueKey.attributeReferences)) {
            for (const attributeReference of uniqueKey.attributeReferences) {
              uniqueKeysList.push(attributeReference);
            }
          }
        }
      }
    }
    return uniqueKeysList;
  }

  /**
   * @TODO: Check if this validation is required
   * @param {IColumnDetail} attribute
   * @returns {boolean}
   * @memberof RFBuilderClass
   */
  private isHiddenColumn(column: IColumnDetail): boolean {
    let isHidden = false;
    if (column && column.capabilityProperties) {
      column.capabilityProperties.forEach((elem) => {
        if (elem.name === "hidden" && elem.value === "true") {
          isHidden = true;
        }
      });
    }
    return isHidden;
  }

  /**
   * Conditions for a column is Unsupported or CDC, UI should hide these column
   * More info below
   * LoB Columns => (column.templateType === 'string' || column.templateType === 'binary') && (!column.length || column.length > 5000)
   * ABAP CDC -> p.namespace === 'com.sap.abap.*' && p.name === 'semanticType' && (p.value === '_change_mode')
   * ABAP Technical Unsupported -> p.namespace === 'com.sap.abap.*' && p.name === 'semanticType' && (p.value === '_object_name')
   * Objectstores CDC -> p.namespace === 'com.sap.rms' && p.name === 'semanticType' && p.value === '_change_mode'
   * Objectstores Internal -> p.namespace === 'com.sap.rms' && p.name === 'semanticType' && p.value === 'X'
   * @private
   * @param {IColumnDetail} column
   * @param {string} connectionType
   * @return {*}  {boolean}
   * @memberof RFBuilderClass
   */
  private isInvalidColumn(column: IColumnDetail, connectionType: string): boolean {
    // invalid & LoB columns are not supported
    if (
      !column.templateType ||
      ((column.templateType === "string" || column.templateType === "binary") &&
        (!column.length || column.length > 5000))
    ) {
      return true;
    }
    let isUnsupported = false;
    if (column && Array.isArray(column.properties)) {
      const isABAP = Utils.isConnectionTypeABAP(connectionType);
      const isObjectStore = Utils.isConnTypeObjectStore(connectionType);
      isUnsupported = column.properties.some((p: IColumnProperties) => {
        if (
          isABAP &&
          p.namespace.startsWith("com.sap.abap") &&
          p.name === "semanticType" &&
          (p.value === "_object_name" || p.value === "_change_mode")
        ) {
          return true;
        } else if (
          isObjectStore &&
          p.namespace === "com.sap.rms" &&
          p.name === "semanticType" &&
          (p.value === "_change_mode" || p.value === "X")
        ) {
          return true;
        } else {
          return false;
        }
      });
    }
    return isUnsupported;
  }

  /**
   * Use ("nativeContentType" and "nativeLength") if exists instead of templateType only when below checks passes
   * {namespace: "com.sap.abap.*", name: "nativeContentType", value: "string"} exists in attribute.properties[] & value !== attribute.templateType
   * Note above will be true only for date, time, guid, boolean
   *
   * if the replication flow is new it checks for decimal as well Use ("nativeContentType" and "abapLength" ans "abapDecimal") if exists instead of templateType only when below checks passes
   * {namespace: "com.sap.abap.*", name: "nativeContentType", value: "string"} exists in attribute.properties[] & value !== attribute.templateType
   * Note above will be true only for timestamp as template type
   * @private
   * @param {IColumnDetail} column
   * @return {*}  {({ nativeContentType: string, nativeLength: number } | void)}
   * @memberof RFBuilderClass
   */
  private getNativeContentType(
    column: IColumnDetail,
    options
  ): { nativeContentType?: string; nativeLength?: number; nativePrecision?: number; nativeScale?: number } | void {
    // Initialize the return object
    const result: {
      nativeContentType?: string;
      nativeLength?: number;
      nativePrecision?: number;
      nativeScale?: number;
    } = {};

    // Handle string type
    let stringExecuted = false;
    const nNativeTypeString = column.properties?.find(
      (p: IColumnProperties) =>
        p.namespace.startsWith("com.sap.abap") && p.name === "nativeContentType" && p.value === "string"
    );
    if (nNativeTypeString && nNativeTypeString.value !== column.templateType) {
      stringExecuted = true;
      const nNativeLenString = column.properties?.find(
        (p: IColumnProperties) => p.namespace.startsWith("com.sap.abap") && p.name === "nativeLength" && p.value
      );
      if (nNativeLenString) {
        result.nativeContentType = nNativeTypeString.value;
        result.nativeLength = Number(nNativeLenString.value);
      }
    }

    // Handle decimal type
    if (
      stringExecuted === false &&
      Object.keys(options).length > 0 &&
      Constants.ABAPcontentTypeDisabled in options &&
      !options.ABAPcontentTypeDisabled
    ) {
      const nNativeTypeDecimal = column.properties?.find(
        (p: IColumnProperties) =>
          p.namespace.startsWith("com.sap.abap") && p.name === "nativeContentType" && p.value === "decimal"
      );
      if (nNativeTypeDecimal && nNativeTypeDecimal.value !== column.templateType) {
        const nNativePrecision = column.properties?.find(
          (p: IColumnProperties) => p.namespace.startsWith("com.sap.abap") && p.name === "abapLength" && p.value
        );
        const nNativeScale = column.properties?.find(
          (p: IColumnProperties) => p.namespace.startsWith("com.sap.abap") && p.name === "abapDecimals" && p.value
        );
        if (nNativePrecision) {
          result.nativeContentType = nNativeTypeDecimal.value;
          result.nativePrecision = Number(nNativePrecision.value); // Precision for decimal
          result.nativeScale = nNativeScale ? Number(nNativeScale.value) : 0; // Scale for decimal
        }
      }
    }

    // If we found a valid result, return it; otherwise, return undefined
    return Object.keys(result).length > 0 ? result : undefined;
  }

  private showConfirmConnectionOrContainerChange(message: string, type: string, bTriggeredFromConnection: boolean) {
    return new Promise((resolve) => {
      MessageHandler.confirm(
        message,
        null,
        null,
        null,
        null,
        (action: sap.m.MessageBox.Action) => {
          if (action === sap.m.MessageBox.Action.OK) {
            this.handleConnectionContainerChange(type, bTriggeredFromConnection);
            resolve("Confirm");
          }
        },
        null,
        "confirmChangeConnectionDialog"
      );
    });
  }

  /**
   *
   *
   * @private
   * @param {string} type
   * @param {boolean} bTriggeredFromConnection
   * @memberof RFBuilderClass
   */
  private handleConnectionContainerChange(type: string, bTriggeredFromConnection: boolean) {
    const oModel = this.getModel();
    const modelBuilder = this.modelBuilder;
    if (type === "source") {
      // no matter connection or container, if its from source just remove replications
      // delete all replications
      this.oResource.applyUndoableAction(() => {
        modelBuilder.removeAllReplicationTasks();
        if (Utils.isConnectionTypeSignavio(oModel.targetSystems?.get(0)?.connectionType)) {
          modelBuilder.updateDeltaCheckInterval("24", "0");
        } else {
          modelBuilder.updateDeltaCheckInterval("1", "0");
        }
      }, "delete all replications");
    }

    if (type === "target" && bTriggeredFromConnection) {
      // delete target connection
      // remove all target objects from replications
      this.oResource.applyUndoableAction(() => {
        oModel.targetSystems.get(0).deleteObject();
        modelBuilder.removeAllTargetObjects();
      }, "delete target connection and delete all target objects");
    }

    if (type === "target" && !bTriggeredFromConnection) {
      // delete target system container
      // remove all target objects from replications
      this.oResource.applyUndoableAction(() => {
        oModel.targetSystems.get(0).systemContainer = "";
        modelBuilder.removeAllTargetObjects();
      }, "delete target connection and delete all target objects");
    }
    this.modelBuilder.validateModel();
    this.onRemoveAllSelection();
  }

  /**
   * Sets editor to dirty state
   */
  setEditorToDirtyState() {
    // set to dirty state
    ShellUIService.setDirty(ToolName.DataBuilder, true, false);
  }

  /**
   * Sets editor to non-dirty state
   * note: use when absolute necessary with reasonings why
   */
  setEditorToNonDirtyState() {
    ShellUIService.setDirty(ToolName.DataBuilder, false);
  }

  /**
   * Overwrites the existing delta load interval with new values
   * @param targetSystems
   */
  configureDeltaLoadIntervalForConnection(targetSystems) {
    if (Utils.isConnectionTypeSignavio(targetSystems.connectionType)) {
      this.modelBuilder.updateDeltaCheckInterval("24", "0");
    } else {
      this.modelBuilder.updateDeltaCheckInterval("1", "0");
    }
  }

  /**
   * Configures the default value for delta interval in model when task is added if default value is not present
   */
  public setDefaultValueForDeltaInterval() {
    const galileiModel = this.getView().getModel("galileiUiModel");
    const deltaCheckIntervalHour = galileiModel.getData().deltaCheckIntervalHour;
    const targetConnType = galileiModel.getData().targetSystems?.get(0).connectionType;
    if (deltaCheckIntervalHour === undefined || deltaCheckIntervalHour === null || deltaCheckIntervalHour === "") {
      if (Utils.isConnectionTypeSignavio(targetConnType)) {
        this.modelBuilder.updateDeltaCheckInterval("24", "0");
      } else {
        this.modelBuilder.updateDeltaCheckInterval("1", "0");
      }
    }
  }

  /**
   * Initializes the tasks with desired loadtype based on selected target connection
   * @param targetSystem
   */
  initializeDefaultLoadTypeForTargetConnection(targetSystem) {
    // For DWC LTF connection, set all tasks to initial delta
    if (Utils.isConnectionTypeDwcLTF(targetSystem?.connectionId, targetSystem?.connectionType)) {
      // Declare the const replicationTasks outside the conn type check if other conn types need to be handled
      const replicationTasks = this.getModel().replicationTasks.toArray();
      replicationTasks.forEach((task) => {
        task.loadType = Constants.LOAD_TYPE.INITIALDELTA;
      });
    }
  }

  // ============================================================ </Utility Functions> ============================================================

  /**
   * The function is evoked on press of Source Setting button
   * and opens a config dialog
   * @private
   * @param evt
   * @return {*} {Promise<any>}
   * @memberof RFBuilderClass
   */
  private async onBrowseSourceSetting(evt): Promise<any> {
    const oModel = this.getModel();
    const oSourceSystem = oModel.sourceSystems.get(0);
    const dialogTitle = oSourceSystem.connectionType + ":";
    this.previousHelpScreenId = getMostRecentHelpConfiguration().screenId;
    const width = "50%";
    const height = "auto";

    await showDialog(
      require("../view/SourceConnectionSettingDialog.view.xml"),
      this.localizeText("sourceSettingTitle", [dialogTitle]),
      this.localizeText("connectionSettingSave"),
      this.localizeText("connectionSettingCancel"),
      {},
      width, // width
      height, // height
      null,
      { oModel },
      null,
      null,
      null,
      true
    )
      .then(async (result) => {
        const oSourceSystem = result.oSourceSystem;
        this.oResource.applyUndoableAction(() => {
          if (result.doClearDatasetProperties) {
            this.modelBuilder.resetLocalTaskSetting();
            if (
              (Utils.isConnTypeObjectStore(oSourceSystem.connectionType) &&
                !Utils.isConnectionTypeHdlfWithDeltaShare(
                  oSourceSystem?.connectionType,
                  oSourceSystem?.metadata?.isSourceHDLFDeltashare
                )) ||
              Utils.isConnectionTypeCONFLUENT(oSourceSystem.connectionType)
            ) {
              const oModel = this.getModel();
              this.modelBuilder.resetSourceDatasetProperties(
                result.sourceSystemProperties,
                oModel.replicationTasks.toArray()
              );
            }
          }
          oSourceSystem.maxConnections = result.nSourceMaxConnection;
          this.modelBuilder.updateMaxDesiredDeltaPartition(result.nGlobalDeltaPartitionValue);
          if (
            (Utils.isConnTypeObjectStore(oSourceSystem.connectionType) &&
              !Utils.isConnectionTypeHdlfWithDeltaShare(
                oSourceSystem?.connectionType,
                oSourceSystem?.metadata?.isSourceHDLFDeltashare
              )) ||
            Utils.isConnectionTypeCONFLUENT(oSourceSystem.connectionType)
          ) {
            this.modelBuilder.createSourceSystemProperties(result.sourceSystemProperties, undefined, oSourceSystem);
          }
        }, "update source system max connection");
        sap.ui.getCore().getEventBus().publish("refreshDatasetSettings", "updateDatasetSettings");
      })
      .catch(() => {
        // cancel
      });
    this.restorePreviousHelpScreenId();
  }

  /**
   * The function is evoked on press of Target Setting button
   * and opens a config dialog
   * @private
   * @param evt
   * @return {*} {Promise<any>}
   * @memberof RFBuilderClass
   */
  private async onBrowseTargetSetting(evt): Promise<any> {
    const oModel = this.getModel();
    const oTargetSystem = oModel.targetSystems.get(0);
    const dialogTitle = oTargetSystem.connectionType + ":";
    this.previousHelpScreenId = getMostRecentHelpConfiguration().screenId;
    await showDialog(
      require("../view/TargetConnectionSettingDialog.view.xml"),
      this.localizeText("targetSettingTitle", [dialogTitle]),
      this.localizeText("connectionSettingSave"),
      this.localizeText("connectionSettingCancel"),
      {},
      "50%", // width
      "auto", // height
      null,
      { oTargetSystem: oModel.targetSystems.get(0) },
      null,
      null,
      null,
      true
    )
      .then(async (result) => {
        const oTargetSystem = result.oTargetSystem;
        let properties;
        if (Constants.CONNECTION_TYPES.KAFKA === oTargetSystem.connectionType) {
          properties = result.kafkaProperties;
        }
        if (
          Utils.isConnTypeObjectStore(oTargetSystem.connectionType) &&
          !Utils.isConnectionTypeDwcLTF(oTargetSystem.connectionId, oTargetSystem.connectionType)
        ) {
          properties = result.objectStoreProperties;
        }
        if (Utils.isConnectionTypeGBQ(oTargetSystem.connectionType)) {
          properties = result.gbqProperties;
        }
        if (Utils.isConnectionTypeCONFLUENT(oTargetSystem.connectionType)) {
          properties = result.confluentProperties;
        }
        if (Utils.isConnectionTypeDwcLTF(oTargetSystem.connectionId, oTargetSystem.connectionType)) {
          properties = result.dwcLTFProperties;
        }
        this.oResource.applyUndoableAction(() => {
          if (result.doClearDatasetProperties) {
            this.modelBuilder.removeAllTargetDatasetProperties();
          }
          oTargetSystem.systemProperties?.deleteObject();
          oTargetSystem.maxConnections = result.nTargetMaxConnection;
          this.modelBuilder.createSystemProperties(properties, oTargetSystem); // system properties supported only for OS , GBQ , Kafka, confluent, LTF
        }, "update system properties and target system max connection");
        sap.ui.getCore().getEventBus().publish("refreshDatasetSettings", "updateDatasetSettings");
      })
      .catch(() => {
        // cancel
      });
    this.restorePreviousHelpScreenId();
  }

  /**
   * @return {*}  {ModelBuilder}
   */
  public getModelBuilder(): ModelBuilder {
    return this.modelBuilder;
  }

  /**
   * @return {*}
   * @memberof RFBuilderClass
   */
  public async executeReplicationflow() {
    const oModel: sap.cdw.replicationflow.Model = this.getModel();

    if (+oModel["#objectStatus"] === ObjectStatus.changesToDeploy) {
      // Inform user that deployed version of replicationflow will get executed
      this.showExecuteConfirmDialog(this.localizeText("msgRunDeployedVersion"));
    } else if (oModel && oModel.aggregatedValidations && oModel.aggregatedValidations.status === "error") {
      // validation errors in the model.
      // confirm execution
      this.showExecuteConfirmDialog(this.localizeText("msgExecuteWithValidations"));
    } else {
      this.triggerExecution();
    }
  }

  /**
   * To track the usage of Replication Flow Run
   * @private
   * @memberof RFBuilderClass
   */
  private trackUsageForRFExecution() {
    ShellContainer.get()
      .getUsageCollectionService()
      .recordAction({
        action: Constants.USAGE_ACTIONS.EXECUTE_REPLICATIONFLOW,
        feature: DWCFeature.DATA_BUILDER,
        eventtype: EventType.CLICK,
        options: [
          {
            param: "target",
            value: Constants.USAGE_REPLICATIONFLOW_EDITOR,
          },
        ],
      });
  }

  /**
   *
   *
   * @private
   * @return {*}
   * @memberof RFBuilderClass
   */
  private async triggerExecution() {
    const oWorkbenchModel = this.getView().getModel("workbenchEnv") as sap.ui.model.json.JSONModel;
    const spaceName = this.getSpaceName();
    const oModel: sap.cdw.replicationflow.Model = this.getModel();
    const replicationFlowName = oModel.name;

    this.trackUsageForRFExecution(); // Track Usage of Replicationflow Execution

    if (this.isTargetConnNonSAP()) {
      this.setBusy(true);
      const isEligibleForRun = await this.checksForPremiumOutbound(true);
      this.setBusy(false);
      if (!isEligibleForRun) {
        return false;
      }
    }

    // to open RF context on property panel
    this.onRemoveAllSelection();
    this.showBusyDialog(this.localizeText("titleExecuteBusy"), this.localizeText("msgExecuteBusy"));
    oWorkbenchModel.setProperty("/panels/right/mode", SidepanelMode.normal);

    const runPayload: IRunReplicationFlowPayload = { isDirect: true };
    const url = "./replicationflow/space/" + spaceName + "/flows/" + replicationFlowName + "/run";

    return ServiceCall.post(url, { contentType: ContentType.APPLICATION_JSON }, true, JSON.stringify(runPayload)).then(
      () => {
        if (this.oBusyDialog) {
          this.oBusyDialog.close();
        }
        MessageHandler.success(this.localizeText("msgRunStarted"));
        // update status in model property panel
        sap.ui.getCore().getEventBus().publish("propertyPanel", "updateStatus");
      },
      (error) => {
        if (this.oBusyDialog) {
          this.oBusyDialog.close();
        }
        MessageHandler.exception({
          exception: error,
          message: this.localizeText("msgExecuteFail"),
          id: "executeFailedErrorMsgbox",
        });
      }
    );
  }

  /**
   *
   *
   * @return {*}
   * @memberof RFBuilderClass
   */
  private async getRunStatus() {
    const sSpaceName = this.getSpaceName();
    const oModel: sap.cdw.replicationflow.Model = this.getModel();
    const sUrl = `/replicationflow/space/${sSpaceName}/flows/${oModel.name}/status`;
    return ServiceCall.request<any>({
      url: sUrl,
      type: HttpMethod.GET,
      contentType: ContentType.APPLICATION_JSON,
    });
  }
  /**
   * show error dialog when load has initial and delta and it's scheduled
   */
  public showSchedulerErrorForDeploy() {
    const localizeModel = this.i18nResourceModel.getResourceBundle();
    const schedulingErrKey = this.oFeatures.DWCO_DS_REPLICATION_FLOW_DELTA_ONLY_LOAD_TYPE
      ? "SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED"
      : "SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT";
    sap.m.MessageBox.error(localizeModel.getText(schedulingErrKey, ["\n"]), {
      id: "scheduleDeployError",
      actions: [sap.m.MessageBox.Action.CLOSE],
    });
  }

  public async checkScheduledAndDeltaLoad() {
    try {
      const isDeltaLoadPresent = this.getView()
        .getModel("galileiUiModel")
        .getProperty("/isDeltaReplicationsExistsInFlow");

      if (isDeltaLoadPresent) {
        const resp = await APIService.getSchedule(this.getSpaceName(), this.getModel().name);
        if (resp.data[0] && resp.data[0].scheduleId) {
          return false;
        }
      }
      return true;
    } catch (e) {
      MessageHandler.exception({
        exception: e,
        message: this.localizeText("VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE"),
        id: "scheduleException",
      });
      return false;
    }
  }

  /**
   * Get the status from the repository and check if replication flow is in deploying state
   * @return boolean
   */
  public async checkDeployingState(): Promise<boolean> {
    try {
      const spaceName = this.getSpaceName();
      const rfName = this.getModel()?.name;
      if (spaceName && rfName) {
        // Remove cached data
        // Instead of removing complete cache of a spaceName/rfName replication flow, it is better to clear the cache of #objectStatus in spaceName/rfName
        Crud.get().clearCache([
          { type: Repo.space, name: spaceName },
          { type: Repo.model, name: rfName },
        ]);
        const replicationFlowProperties = await getRepositoryObject(spaceName, rfName, {
          details: ["name", "#objectStatus", "#deploymentExecutionStatus"],
        });
        return (
          replicationFlowProperties &&
          (+replicationFlowProperties["#objectStatus"] as ObjectStatus) === ObjectStatus.pending &&
          replicationFlowProperties["#deploymentExecutionStatus"] !== "failed"
        );
      }
      // eslint-disable-next-line no-empty
    } catch (e) {}
    return false;
  }

  /**
   * Set the deploy button click status. It is used to prevent multiple clicks on deploy button.
   */
  public setDeployButtonClickStatus(status: boolean): void {
    this["isDeployButtonAlreadyClicked"] = status;
  }

  /**
   * Get the deploy button click status
   */
  public getDeployButtonClickStatus(): boolean {
    return this["isDeployButtonAlreadyClicked"];
  }

  /**
   * We check if replication flow is already running
   * The Replication Flow is already running are not deployable
   *
   * @return {*}
   * @memberof RFBuilderClass
   */
  public async checkDeployable() {
    try {
      const oResponse = await this.getRunStatus();
      sap.ui.getCore().getEventBus().publish("propertyPanel", "updateStatus", oResponse);
      if (oResponse.data && oResponse.data.runs) {
        const lastRun: any = Utils.getExecutionDetails(oResponse.data.runs);
        return lastRun && lastRun.status === "RUNNING" ? false : true;
      } else {
        return true;
      }
    } catch (e) {
      // choosing to be silent and allowing deployment if status call check fails.
      // let the runtime throw exception this way
      // const errorMsg = this.localizeText("deployCheckFailException");
      // MessageHandler.exception({ exception: e, message: errorMsg });
      this.logError(e[2], e[0].responseJSON.details.message);
      return true;
    }
  }

  /**
   *
   * @private
   * @param {string} [title]
   * @param {string} [msg]
   * @memberof RFBuilderClass
   */
  private showBusyDialog(title?: string, msg?: string) {
    if (!this.oBusyDialog) {
      const fragmentId = require("../view/fragment/BusyDialog.fragment.xml");
      this.oBusyDialog = sap.ui.xmlfragment("", fragmentId, this);
      this.getView().addDependent(this.oBusyDialog);
      if (title) {
        this.oBusyDialog.setTitle(title);
      }
      if (msg) {
        this.oBusyDialog.setText(msg);
      }
      this.oBusyDialog.open();
    } else {
      if (title) {
        this.oBusyDialog.setTitle(title);
      }
      if (msg) {
        this.oBusyDialog.setText(msg);
      }
      this.oBusyDialog.open();
    }
  }

  /**
   *
   * @private
   * @memberof RFBuilderClass
   */
  private onBusyDialogClosed() {
    this.oBusyDialog.close();
  }

  /**
   *
   * @private
   * @memberof RFBuilderClass
   */
  private showExecuteConfirmDialog(message: string) {
    if (!this.oExecuteConfirmDialog) {
      const fragmentId = require("../view/fragment/ExecuteConfirmDialog.fragment.xml");
      this.oExecuteConfirmDialog = sap.ui.xmlfragment("", fragmentId, this);
      const oTextBox = this.oExecuteConfirmDialog.getContent()[0] as sap.m.Text;
      this.getView().addDependent(this.oExecuteConfirmDialog);
      oTextBox.setText(message);
      this.oExecuteConfirmDialog.open();
    } else {
      const oTextBox = this.oExecuteConfirmDialog.getContent()[0] as sap.m.Text;
      oTextBox.setText(message);
      this.oExecuteConfirmDialog.open();
    }
  }

  /**
   *
   * @private
   * @memberof RFBuilderClass
   */
  private onExecuteAnyway() {
    this.onExecuteConfirmDialogClosed();
    this.triggerExecution();
  }

  /**
   *
   * @private
   * @memberof RFBuilderClass
   */
  private onExecuteConfirmDialogClosed() {
    if (this.oExecuteConfirmDialog) {
      this.oExecuteConfirmDialog.close();
    }
  }

  /**
   * set header model
   * @param {object} object
   */
  public setHeaderModel(object?: object) {
    const modelObject: any = object || {};
    const oModel: sap.galilei.ui5.GalileiModel = new sap.galilei.ui5.GalileiModel(modelObject);
    this.isEditable = modelObject.isReadOnlyObject !== true && this.getHasPrivileges();
    oModel.setProperty("/editable", this.isEditable);

    this.getView().setModel(oModel, "header");
  }

  /**
   * get privileges
   * @returns {boolean}
   */
  private getHasPrivileges(): boolean {
    // Check the feature flag
    let hasPrivileges: boolean;
    const workbenchEnvModel = this.getView().getModel("workbenchEnv");
    if (workbenchEnvModel) {
      hasPrivileges = workbenchEnvModel.getProperty("/canCreateOrUpdateModel");
    } else {
      const privileges = sap.ui.getCore().getModel("privilege").getData().DWC_DATABUILDER;
      hasPrivileges = privileges.create || privileges.update;
    }
    return hasPrivileges;
  }

  /**
   * update replication flow after restore deployed version
   * @param {any} loadingData
   */
  public updateReplicationFlow(loadingData: any) {
    this.editReplicationFlow(loadingData);
    this.getDatabuilderWorkbench().attachResourceListener(this.getModel().resource);
  }

  /**
   * @return {*}  {DataBuilderWorkbench}
   */
  protected getDatabuilderWorkbench(): DataBuilderWorkbench {
    if (this.databuilderWorkbench) {
      return this.databuilderWorkbench;
    }
    this.databuilderWorkbench = getDatabuilderWorkbench(this.getView());
    return this.databuilderWorkbench;
  }

  /**
   *
   * @private
   * @memberof RFBuilderClass
   */
  private setDefaultWorkbenchModel() {
    this.getWorkbenchController().workbenchModel.setProperty("/toolbar/isNavToMonitoringViewEnabled", false);
    this.getWorkbenchController().workbenchModel.setProperty("/panels/left/visible", false);
    this.getWorkbenchController().workbenchModel.setProperty("/toolbar/isEmailNotificationEnabled", false);
  }

  private refreshPropertyPanelColumns(task?) {
    if (this.aSelectedReplicationTask.length) {
      if (!task) {
        sap.ui
          .getCore()
          .getEventBus()
          .publish("refreshPropertyPanelColumns", "updateColumnList", this.aSelectedReplicationTask[0]);
      } else if (task.name === this.aSelectedReplicationTask[0].name) {
        sap.ui.getCore().getEventBus().publish("refreshPropertyPanelColumns", "updateColumnList", task);
      }
    }
  }

  /**
   * Check whether targetObject exist or not
   * This check happens only for DWC targets for now when user opens existing replication flow
   *
   * @return {*}  {Promise<undefined>}
   * @memberof RFBuilderClass
   */
  public async checkAndUpdateTargetObject(): Promise<undefined> {
    const oModel: sap.cdw.replicationflow.Model = this.getModel();
    const oTargetSystem = oModel.targetSystems.get(0);
    if (oTargetSystem && Utils.isConnectionTypeDWC(oTargetSystem.connectionId, oTargetSystem.connectionType)) {
      try {
        const aReplicationTasks = oModel.replicationTasks.toArray();
        const aPromises = aReplicationTasks.map(async (oTask) => {
          if (oTask.targetObject) {
            const repoObj = await getRepositoryObject(this.spaceName, oTask.targetObject.name);
            if (repoObj !== undefined && repoObj["#technicalType"] === RepositoryObjectType.DWC_LOCAL_TABLE) {
              oTask.targetObject.isNew = false;
              oTask.targetObject.setObjectStatus(repoObj["#objectStatus"]);
            } else {
              // this condition is needed for older replication flows where target object is not present in repo before DS00-1604 rel.
              oTask.targetObject.isNew = true;
            }
          }
          return;
        });
        await Promise.all(aPromises);
        return;
      } catch (e) {
        // No ESLint
      }
    }
  }

  /**
   * Update object file info (modificationDate, deploymentDate, ...) and deploying status
   */
  private async updateObjectsFileInfo(): Promise<any> {
    this.getDeploymentStatusRetries++;
    const oModel: any = this.getModel();
    const undeployedStatuses: ObjectStatus[] = [ObjectStatus.notDeployed, ObjectStatus.hasNoObjectStatus];
    if (oModel && this.getDeploymentStatusRetries <= 5) {
      // Remove cached data
      Crud.get().clearCache([
        { type: Repo.space, name: this.spaceName },
        { type: Repo.model, name: oModel.name },
      ]);
      getObjectFilesProperties(this.getSpaceName(), [oModel.name])
        .then((properties) => {
          if (undeployedStatuses.includes(+properties?.[0]?.["#objectStatus"])) {
            // Re-fetch the deployment status
            this.updateObjectsFileInfoTimer = setTimeout(() => {
              this.updateObjectsFileInfo();
            }, this.getDeploymentStatusRetries * 5000);
          } else if (+properties?.[0]?.["#objectStatus"] === ObjectStatus.pending) {
            // Re-fetch the deployment status and also update the properties panel
            updateObjectFileInfo(oModel, properties[0]);
            sap.ui.getCore().getEventBus().publish("propertyPanel", "updateDeploymentStatus");
            this.updateObjectsFileInfoTimer = setTimeout(() => {
              this.updateObjectsFileInfo();
            }, this.getDeploymentStatusRetries * 5000);
          } else if (properties?.[0]) {
            // update the deployment status in properties panel
            updateObjectFileInfo(oModel, properties[0]);
            sap.ui.getCore().getEventBus().publish("propertyPanel", "updateDeploymentStatus");
          }
        })
        .catch(() => {
          // Re-fetch deployment status
          this.updateObjectsFileInfoTimer = setTimeout(() => {
            this.updateObjectsFileInfo();
          }, this.getDeploymentStatusRetries * 5000);
        });
    }
  }

  /* Called from replicationFlowProperties.controller.ts triggers when user chooses yes to delete projection and changes content type */
  private publishSubscribeOfABAPContentType(event, eventID, eventResponse) {
    this.updateContentTypeOnSourceAndTargetABAP(eventResponse.newContentType);
  }

  /*Updates content type on save on source as well as on target side and deletes projection */
  public updateContentTypeOnSourceAndTargetABAP(newContentType) {
    const oModel = this.getModel();
    const replicationTasks = oModel.replicationTasks.toArray();
    this.modelBuilder.oResource.applyUndoableAction(() => {
      oModel.replicationFlowSetting.ABAPcontentType = newContentType;
      replicationTasks.forEach((task) => {
        const sourceObject = task.sourceObject;
        const targetObject = task.targetObject;

        if (task?.transform) {
          task.transform.deleteObject();
        }
        if (targetObject?.isNew) {
          targetObject.attributes.toArray().forEach((attribute) => {
            attribute.deleteObject();
          });
        }

        // Update source object attributes
        if (sourceObject.attributes.toArray().length > 0) {
          sourceObject.attributes.toArray().forEach((attribute) => {
            const metadata = attribute.metadata;
            if (metadata && Object.keys(metadata).length > 0) {
              this.updateAttributeData(attribute, metadata, newContentType);
            }
          });

          if (targetObject?.isNew === true) {
            if (sourceObject.attributes?.length) {
              const proposedEntities = this.getProposedAttributesAndTransformationForNewtarget(
                task,
                task.targetSystem.connectionId,
                task.targetSystem.connectionType,
                false
              );
              this.modelBuilder.createAttributes(proposedEntities.attrList, task.targetObject);
              if (task.targetObject.isCDCDataset) {
                this.modelBuilder.addCDCAttributes(task.targetObject);
              }
            }
          }
        }
      });
    });
  }
  /* called from update Content type updates the datatype of an attribute based on content type
  updates scale and precision and length acc to datatatype as well
   */
  private updateAttributeData(attribute, metadata, newContentType) {
    const typeMetadata =
      newContentType === Constants.ABAP_CONTENT_TYPE.nativeType ? metadata.nativeType : metadata.TemplateType;

    if (typeMetadata?.datatype) {
      attribute.datatype = typeMetadata.datatype;

      if (attribute.datatype === "string") {
        attribute.length = typeMetadata?.length;
      } else if (attribute.datatype === "decimal") {
        attribute.scale = typeMetadata?.scale;
        attribute.precision = typeMetadata?.precision;
      }
    }
  }

  /**
   * Checks if unsaved new dsp target exists
   *
   * @return {*}  {([] | boolean)}
   * @memberof RFBuilderClass
   */
  public checkForUnSavedNewDWCTargetTables(): boolean {
    const oModel: sap.cdw.replicationflow.Model = this.getModel();
    const oTargetSystem = oModel.targetSystems.get(0);
    if (oTargetSystem && Utils.isConnectionTypeDWC(oTargetSystem.connectionId, oTargetSystem.connectionType)) {
      const aReplicationTasks = oModel.replicationTasks.toArray();
      const unsavedDWCTarget = aReplicationTasks.find((oTask) => {
        return oTask.targetObject && oTask.targetObject.isNew;
      });
      if (unsavedDWCTarget) {
        return true;
      }
    }
    return false;
  }

  /**
   * Checks if given space is a large system space
   *
   * @private
   * @async
   * @returns {Promise<boolean>}
   */
  private async checkLargeSystemSpace(): Promise<boolean> {
    let isLargeSystemSpace = false;
    const spaceName = this.getSpaceName();
    try {
      this.setBusy(true);
      isLargeSystemSpace = await Utils.checkSpaceSupportLargeSystem(spaceName);
      this.setBusy(false);
    } catch (error) {
      this.setBusy(false);
      this.logError(`Unable to fetch space details for ${spaceName}`, error);
    }
    return isLargeSystemSpace;
  }
}

export const RFBuilder = smartExtend(
  AbstractController,
  "sap.cdw.components.replicationflow.controller.RFBuilder",
  RFBuilderClass
);

sap.ui.define("sap/cdw/components/replicationflow/controller/RFBuilder.controller", [], function () {
  require("../js/model/model");
  require("../js/model/validation");
  return RFBuilder;
});
