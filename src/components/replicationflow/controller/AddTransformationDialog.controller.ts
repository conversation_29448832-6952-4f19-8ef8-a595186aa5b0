/**
 * Copyright 2022 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { smartExtend } from "../../basecomponent/controller/BaseController.controller";
import { DATATYPE_TO_ICON, ObjectStatus, SIMPLE_TYPE_ICON } from "../../reuse/utility/Types";
import RFBuilderUtils from "../js/utility/RFBuilderUtils";
import Constants from "../js/utility/constants";
import ValidationUtils from "../js/utility/validationUtils";
import Utils from "../js/utils";
import {
  IComparisonOperators,
  IDateTypes,
  IFilter,
  IFilterElement,
  IFormattedFilterColumns,
  IInputFormat,
} from "../replicationApi";
const Message = sap.ui.require("sap/ui/core/message/Message");
/**
 columnNameRegEx - is used to replace special characters from the target column name
 spaces and ~`@!#$%^&*()+={}[]\|;:'"<>,.?  charecters are replaced with empty, allowed special characters are underscore and hypen
  for ex : #abc ._def@-23  -> abc_def-23
 */
const columnNameRegEx = /[~`@!#$%^&*()+={}\[\]\\|;:'"<>,\.\?\/\s]/g;
const columnNameRegExDWCTarget = /[~`@!#$%^&*()+={}\[\]\\|;:'"<>,\.\?\/\s-]/g;
const NUMBER_REGEX = /^[-+]?[0-9]*\.?[0-9]+([eE][-+]?[0-9]+)?$/;
interface ITransformationRow {
  tName: string;
  tBusinessName?: string;
  name: string;
  originalColumnName: string;
  dataType: string;
  srcMap: string;
  sourceList: Array<{ key: string; text: string }>;
  lengthVisible: boolean;
  precisionVisible: boolean;
  scaleVisible: boolean;
  length: string;
  precision: string;
  scale: string;
  enableExpression: boolean;
  isExpression: boolean;
  expression: string;
  isKey: boolean;
  isCDCColumn: boolean;
  isDPIDColumn: boolean;
  CDCColumnType: undefined | string;
  states: any;
  rowState: string;
  isNewColumn: boolean;
  validationStates: ITransformationvalidationStates;
}

interface ITransformationModel {
  target: Array<sap.cdw.replicationflow.Attribute | ITransformationRow>;
  source: sap.cdw.replicationflow.Attribute[];
  tableModel: ITransformationRow[];
  templateTypes: Array<{ key: string; text: string }>;
  tableColumnsCount: string;
  unsupportedColumns: string;
  isNewTarget: boolean;
  isEditable: boolean;
  privilege: boolean;
  transformName: string;
  transformDesc: string;
  formattedFilterColumns: IFormattedFilterColumns[];
  filteredColumnsCount: number;
  selectedFilterColumn: IFormattedFilterColumns;
  filterComparisionOperators: IComparisonOperators[];
  filterExpForm: IFilterElement;
  filterExpression: string;
  columnCount: string;
  mainColumnDataTypes: IDateTypes;
  columnItemSortDescending: boolean;
  inputFormat: IInputFormat;
  betweenComparisonOperator: string;
  errCount: number;
  isValidTransformName: boolean;
  toolBarButtons: ItoolBarBurrons;
  objectStatus: any;
  invalidColumns: string;
  isPrimaryKeycreated: boolean;
}
interface ITransformationvalidationStates {
  isValidName: boolean;
  isValidBusinessName: boolean;
  isValidDataType: boolean;
  isValidLength: boolean;
  isValidPrecision: boolean;
  isValidScale: boolean;
  isValidMapping: boolean;
  isValidExpression: boolean;
  isAutoRenamed: boolean;
  isAutoDatatype: boolean;
  isDifferentKeyInSourceAndTarget: boolean;
}

interface ItoolBarBurrons {
  isRemoveRowEnabled: boolean;
  isMoveUpEnabled: boolean;
  isMoveDownEnabled: boolean;
  isAutoMapEnabled: boolean;
  isAddEnabled: boolean;
}
export class AddTransformationDialogClass extends sap.ui.core.mvc.Controller {
  protected oModel: sap.ui.model.json.JSONModel;
  private i18nResourceModel: any;
  public taskDetails: sap.cdw.replicationflow.Task;
  private objectStatus: any;
  public sourceInvalidColumns: string[];
  public targetInvalidColumns: string[];
  public sourcePKCols: string[];
  public UIModel: ITransformationModel;
  public targetAttr: Array<sap.cdw.replicationflow.Attribute | ITransformationRow>;
  public sourceAttr: sap.cdw.replicationflow.Attribute[];
  public unsupportedColumns: string = "";
  private mappedSrcTargetForDwc: any;
  public changedMappingList: any;
  public changedAttrObjList: any;
  public changedAttrNames: any;
  public transformObj: any;
  public aggregatedAttr: any;
  public transformName: string;
  public transformDesc: string;
  public isNewTarget: boolean;
  private attrMappings: any;
  private isMappingChanged: boolean;
  private isAttrChanged: boolean;
  private isTransformChanged: boolean;
  private transformDetails: any;
  public isValid: boolean;
  public errMessage: string;
  public ttypes = Constants.TEMPLATE_TYPES;
  public templateTypes: any;
  private columnInfoPopover: sap.m.Popover;
  private formattedFilterColumns: IFormattedFilterColumns[] = [];
  private filterComparisionOperators: IComparisonOperators[] = Constants.COMPARISION_OPERATORS;
  private filterExpForm: IFilterElement = null;
  private clearSetTimeoutFilterInp: null | ReturnType<typeof setTimeout> = null;
  private inputFormat: IInputFormat = {
    dateFormat: "yyyy-MM-dd",
    dateTimeFormat: "yyyy-MM-dd HH:mm:ss.SSSSSSS",
    timeFormat: "HH:mm:ss",
  };
  private isFilterChanged: boolean;
  private MessageManager: any;
  private MessagePopOver: sap.m.MessagePopover;
  private PopoverForInvalidColumns: sap.m.Popover;
  private erroCodes = Constants.TS_DIALOG_ERROR_CODES;
  private eventHandler = null;
  private isEditable = false;
  private privilege: boolean;
  private colCounter: number = 0;
  private featureflags: Record<string, boolean>;
  private reservedLength = 0;
  public onInit(): void {
    this.changedMappingList = {};
    this.transformName = "";
    this.transformDesc = "";
    this.transformObj = {
      attributeMappings: [],
      filters: [],
      name: "",
      description: "",
    };
    this.isMappingChanged = false;
    this.isAttrChanged = false;
    this.changedAttrNames = {};
    this.changedAttrObjList = [];
    this.isValid = true;
    this.isTransformChanged = false;
    this.isFilterChanged = false;
    this.transformDetails = {};
    this.aggregatedAttr = [];

    const bundleName = require("../i18n/i18n.properties");
    this.i18nResourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: bundleName,
    });
    this.getView().setModel(this.i18nResourceModel, "i18n");
    this.initMessageManager();
  }

  /**
   * @param {} oParams
   */
  public postInit(oParams) {
    this.taskDetails = oParams.taskDetails;
    this.privilege = oParams.privilege;
    this.featureflags = oParams.oFeatures;
    this.isNewTarget = this.taskDetails.targetObject.isNew;
    this.targetAttr = this.taskDetails.targetObject.attributes.toArray();
    this.sourceAttr = this.taskDetails.sourceObject.attributes.toArray();
    this.objectStatus = oParams.objectStatus;
    this["skipUnmapCols"] = oParams.bHasSkipMappingCapability;
    this.sourceInvalidColumns = this.taskDetails.sourceObject.invalidColumns;
    this.targetInvalidColumns = this.taskDetails.targetObject.invalidColumns;
    this.sourcePKCols = this.sourceAttr.filter((c) => c.key).map((c) => c.name);
    this.isEditable = this.isEditEnabled();
    if (
      Utils.isConnectionTypeDWC(
        this.taskDetails.targetSystem.connectionId,
        this.taskDetails.targetSystem.connectionType
      )
    ) {
      this.mappedSrcTargetForDwc = this.getMappingsofConvertedDWCNames(this.sourceAttr);
    }

    if (this.taskDetails.transform) {
      this.transformName = this.taskDetails.transform.name ?? "";
      // this.transformDesc = this.taskDetails.transform.description ?? '';
      this.attrMappings = this.taskDetails.transform.attributeMappings;
    }
    this.reservedLength = this.getReservedLength();
    // Unsupported Columns validation
    const supportedSourceColumns = this.getSupportedSourceColumns();
    if (supportedSourceColumns.unsupported.length > 0) {
      this.unsupportedColumns = this.localizeText("txtSourceColValidation");
      supportedSourceColumns.unsupported.forEach((col: any, idx: number) => {
        if (idx > 0) {
          this.unsupportedColumns += ", ";
        }
        this.unsupportedColumns += `${col.name}`;
      });
    }

    this.formattedFilterColumns = this.serializeCols(
      supportedSourceColumns.supported,
      this.taskDetails.transform?.filters?.toArray() || []
    );

    const sourceList = [];
    this.sourceAttr.forEach((source) => {
      sourceList.push({
        key: source.name,
        text: source.name,
      });
    });

    if (!this.isNewTarget) {
      sourceList.push({
        key: Constants.SKIP_MAPPING,
        text: Constants.SKIP_MAPPING,
      });
    }

    sourceList.push({
      key: "empty",
      text: "",
    });

    this.templateTypes = this.getCompatibleDatatypes(
      this.taskDetails.targetSystem.connectionType,
      this.taskDetails.targetSystem.connectionId
    );

    const tableModel = [];
    this.targetAttr.forEach((target) => {
      const rowData = this.getTableRow(target, sourceList);
      if (rowData.isKey) {
        rowData.sourceList = rowData.sourceList.filter((o) => o.key !== Constants.SKIP_MAPPING);
      }
      tableModel.push(rowData);
      this.validateAutoNameAndAutoTyping(rowData, false);
    });

    this.filterExpForm = {
      comparison: Constants.COMPARISION_OPERATORS[0].key,
      low: "",
      high: "",
      isExcluding: false,
    };

    this.UIModel = {
      target: this.targetAttr,
      source: this.sourceAttr,
      tableModel,
      templateTypes: this.templateTypes,
      tableColumnsCount: this.localizeText("txtMappingCount", [this.targetAttr.length.toString()]),
      unsupportedColumns: this.unsupportedColumns,
      isNewTarget: this.isNewTarget,
      isEditable: this.isEditable,
      privilege: this.privilege,
      transformName: this.transformName,
      transformDesc: this.transformDesc,
      formattedFilterColumns: this.formattedFilterColumns,
      filteredColumnsCount: this.filterColumnsCount(this.formattedFilterColumns),
      selectedFilterColumn: null,
      filterComparisionOperators: this.filterComparisionOperators,
      filterExpForm: this.filterExpForm,
      filterExpression: this.filterExpression(this.formattedFilterColumns),
      columnCount: this.localizeText("rftdFilterColumnCount", [
        this.taskDetails.sourceObject.name,
        this.formattedFilterColumns.length,
      ]),
      mainColumnDataTypes: Constants.DATATYPES_MAIN_CATEGORIES,
      columnItemSortDescending: false,
      inputFormat: this.inputFormat,
      betweenComparisonOperator: Constants.BETWEEN_COMPARISON,
      errCount: 0,
      isValidTransformName: true,
      toolBarButtons: {
        isRemoveRowEnabled: false,
        isMoveUpEnabled: false,
        isMoveDownEnabled: false,
        isAutoMapEnabled: true,
        isAddEnabled: true,
      },
      objectStatus: this.objectStatus,
      invalidColumns: "",
      isPrimaryKeycreated: true,
    };

    const uiModel = new sap.ui.model.json.JSONModel(this.UIModel);
    this.getView().setModel(uiModel, "UIModel");

    const featureflags = new sap.ui.model.json.JSONModel(this.featureflags);
    this.getView().setModel(featureflags, "featureflags");

    if (this.targetAttr.length > 100 || sourceList.length > 100) {
      this.getView().getModel("UIModel").setSizeLimit(Math.max(this.targetAttr.length, sourceList.length));
    }
    this.validatePrimaryKeyCreation(this.targetAttr, false);
    this.validatePrimaryKeyForHDLFDeltaShare(this.UIModel.tableModel, false);
    this.validateSameKeysInSourceAndTargetForHDLFDeltashare(this.getView().getModel("UIModel"), true);
    this.validateTable();
    this.setErrorCount();
    addEventListener("resize", this.resizeEventHandler());
  }

  public isEditEnabled(): boolean {
    let isEditabled = false;
    if (this.privilege) {
      if (this.taskDetails.targetObject.isNew) {
        // Allow editing schema for new targets
        isEditabled = true;
      } else if (
        Utils.isConnTypeObjectStore(this.taskDetails.targetSystem.connectionType) &&
        this.taskDetails.truncate
      ) {
        // Allow editing schema for Object store conn type with truncate true
        isEditabled = true;
      } else {
        isEditabled = false;
      }
    } else {
      isEditabled = false;
    }
    return isEditabled;
  }

  public onExit(): void {
    removeEventListener("resize", this.resizeEventHandler());
  }

  /**
   * Checks for any automatic rename and adds info message
   * @param row
   * @param isSetErrcount
   */
  public validateAutoNameAndAutoTyping(row: ITransformationRow, isSetErrcount: boolean) {
    const sourceConnectionType = this.taskDetails.sourceSystem.connectionType;
    const sourceContainer = this.taskDetails.sourceSystem.systemContainer;
    const targetConnId = this.taskDetails.targetSystem.connectionId;
    const targetConnectionType = this.taskDetails.targetSystem.connectionType;
    if (row.validationStates.isAutoRenamed) {
      const id: string = this.generateErrId(row.originalColumnName, this.erroCodes.autoRename);
      if (Utils.isConnectionTypeGBQ(targetConnectionType)) {
        this.addMessage(
          this.localizeText("targetAutoRenameUpdated"),
          id,
          row.tName,
          row.originalColumnName,
          this.localizeText("targetAutoRenameDescUpdated1", ["<br>", "<ul>", "<li>", "</li>", "</ul>"]),
          isSetErrcount,
          "Information"
        );
      }
      if (Utils.isConnectionTypeCONFLUENT(targetConnectionType)) {
        this.addMessage(
          this.localizeText("targetAutoRenameUpdated"),
          id,
          row.tName,
          row.originalColumnName,
          this.localizeText("targetConfluentAutoRenameDesc", ["<br>", "<ul>", "<li>", "</li>", "</ul>"]),
          isSetErrcount,
          "Information"
        );
      }
      if (Utils.isConnectionTypeDWC(targetConnId, targetConnectionType)) {
        this.addMessage(
          this.localizeText("targetAutoRenameUpdated"),
          id,
          row.tName,
          row.originalColumnName,
          this.localizeText("targetDatasphereAutoRenameDesc1", ["<br>", "<ul>", "<li>", "</li>", "</ul>"]),
          isSetErrcount,
          "Information"
        );
      }
      if (Utils.isConnectionTypeSignavio(targetConnectionType)) {
        this.addMessage(
          this.localizeText("targetAutoRenameUpdated"),
          id,
          row.tName,
          row.originalColumnName,
          this.localizeText("targetAutoRenameGenericDesc", ["<br>", "<ul>", "<li>", "</li>", "</ul>"]),
          isSetErrcount,
          "Information"
        );
      }

      if (Utils.isConnTypeSFTP(targetConnectionType)) {
        this.addMessage(
          this.localizeText("targetAutoRenameUpdated"),
          id,
          row.tName,
          row.originalColumnName,
          this.localizeText("targetAutoRenameGenericDesc", ["<br>", "<ul>", "<li>", "</li>", "</ul>"]),
          isSetErrcount,
          "Information"
        );
      }

      if (Utils.isConnTypeMsOneLake(targetConnectionType)) {
        this.addMessage(
          this.localizeText("targetAutoRenameUpdated"),
          id,
          row.tName,
          row.originalColumnName,
          this.localizeText("targetAutoRenameGenericDesc", ["<br>", "<ul>", "<li>", "</li>", "</ul>"]),
          isSetErrcount,
          "Information"
        );
      }
      // Source ABAP ( For targets , above condition already provides the info, below condition is needed only if above targets are not)
      if (
        (Utils.isABAPCDS(sourceConnectionType, sourceContainer) ||
          Utils.isABAPODP(sourceConnectionType, sourceContainer)) &&
        !(
          Utils.isConnectionTypeGBQ(targetConnectionType) ||
          Utils.isConnectionTypeCONFLUENT(targetConnectionType) ||
          Utils.isConnectionTypeDWC(targetConnId, targetConnectionType) ||
          Utils.isConnectionTypeSignavio(targetConnectionType) ||
          Utils.isConnTypeMsOneLake(targetConnectionType) ||
          Utils.isConnTypeSFTP(targetConnectionType)
        )
      ) {
        this.addMessage(
          this.localizeText("targetAutoRenameDPID"),
          id,
          row.tName,
          row.originalColumnName,
          this.localizeText("targetAutoRenameDPIDDesc", ["<br>", "<ul>", "<li>", "</li>", "</ul>"]),
          isSetErrcount,
          "Information"
        );
      }
    }
    if (row.validationStates.isAutoDatatype) {
      const id: string = this.generateErrId(row.originalColumnName, this.erroCodes.autoDatatype);
      if (Utils.isConnectionTypeGBQ(targetConnectionType)) {
        this.addMessage(
          this.localizeText("targetAutoDataType"),
          id,
          row.tName,
          row.originalColumnName,
          this.localizeText("targetAutoDataTypeDesc", [row.dataType]),
          isSetErrcount,
          "Information"
        );
      }
      if (Utils.isConnectionTypeDWC(targetConnId, targetConnectionType)) {
        this.addMessage(
          this.localizeText("targetAutoDataType"),
          id,
          row.tName,
          row.originalColumnName,
          this.localizeText("targetAutoDataTypeDWC2", [row.dataType]),
          isSetErrcount,
          "Information"
        );
      }
    }
  }

  /**
   * Validates for primary key creation in target and provides warning message.
   *
   * @param targetAttributes
   * @param isSetErrcount
   */
  public validatePrimaryKeyCreation(targetAttributes: any[], isSetErrcount) {
    if (Utils.isConnectionTypeGBQ(this.taskDetails.targetSystem.connectionType)) {
      const validations = RFBuilderUtils.validateGBQTargetObjectAttributes(targetAttributes);
      if (validations.keyColCount > 16) {
        this.addMessage(
          this.localizeText("projectionGBQUnableToCreateKey"),
          Constants.TS_DIALOG_ERROR_CODES.primaryKey,
          "",
          "",
          this.localizeText("projectionGBQUnableToCreateKeyMaxKeyCombinationDescription"),
          isSetErrcount,
          "Warning"
        );
      } else if (validations.isUncompatibleKeyType) {
        this.addMessage(
          this.localizeText("projectionGBQUnableToCreateKey"),
          Constants.TS_DIALOG_ERROR_CODES.primaryKey,
          "",
          "",
          this.localizeText("validationIncompatiblePKTypeDescProjection3", ["<br>", "<ul>", "<li>", "</li>", "</ul>"]),
          isSetErrcount,
          "Warning"
        );
      }
    }
  }

  /**
   * validates the table model for Primary Keys for HDLF Deltashare
   * @param tableModel
   * @param isSetErrcount
   */
  public validatePrimaryKeyForHDLFDeltaShare(tableModel: ITransformationRow[], isSetErrcount) {
    //  Validate Primary Key for HDLF as Source
    if (
      Utils.isConnectionTypeHdlfWithDeltaShare(
        this.taskDetails.sourceSystem.connectionType,
        this.taskDetails.sourceSystem.metadata["isSourceHDLFDeltashare"]
      )
    ) {
      const validations = this.checkPrimaryKeyCount(tableModel);
      if (validations.keyColCount === 0) {
        if (this.UIModel.isNewTarget) {
          this.addMessage(
            this.localizeText("HDLFNoKeyError"),
            Constants.TS_DIALOG_ERROR_CODES.primaryKey,
            "",
            "",
            this.localizeText("HDLFNoKeyErrorDescription"),
            isSetErrcount,
            "Error"
          );
        } else {
          this.addMessage(
            this.localizeText("HDLFNoKeyErrorExistingTarget"),
            Constants.TS_DIALOG_ERROR_CODES.primaryKey,
            "",
            "",
            this.localizeText("HDLFNoKeyErrorExistingTargetDescription3", ["<br>", "•"]),
            isSetErrcount,
            "Error"
          );
        }
      } else {
        this.removeMessage(Constants.TS_DIALOG_ERROR_CODES.primaryKey);
      }
    }
  }

  /**
   * Returns only compatible datatypes for selection
   * @param connectionType
   * @returns
   */
  private getCompatibleDatatypes(connectionType, connectionId) {
    if (Utils.isConnectionTypeDwcLTF(connectionId, connectionType)) {
      return this.getCompatibleDatatypesForDWCHDLF("LTF");
    }
    if (connectionType && Constants.DATATYPE_CONVERT_RULES[connectionType]) {
      return Constants.TEMPLATE_TYPES.filter(
        (type) => !Constants.DATATYPE_CONVERT_RULES[connectionType][type.templateType]
      ).map((type) => ({ key: type.templateType, text: type.templateType }));
    }
    return Constants.TEMPLATE_TYPES.map((type) => ({ key: type.templateType, text: type.templateType }));
  }

  /**
   * Returns the compatible datatypes for DWC_HDLF
   * @param connectionType
   * @returns
   */
  private getCompatibleDatatypesForDWCHDLF(connectionType) {
    const dataTypeConvertRuleLTF = RFBuilderUtils.appyFeatureFlagForDWCLTFFOrComaptibleDataTypes().DataTypeConvertRule;
    return Constants.TEMPLATE_TYPES.filter((type) => !dataTypeConvertRuleLTF[connectionType][type.templateType]).map(
      (type) => ({ key: type.templateType, text: type.templateType })
    );
  }

  private resizeEventHandler() {
    const thisRef = this;
    if (!this.eventHandler) {
      this.eventHandler = (event) => {
        if ((event.currentTarget as Window).innerHeight <= Constants.transformationTableHeight) {
          (thisRef.getView().byId("transformationTable") as sap.ui.table.Table).setVisibleRowCount(7);
        } else {
          (thisRef.getView().byId("transformationTable") as sap.ui.table.Table).setVisibleRowCount(10);
        }
      };
    }
    return this.eventHandler;
  }

  public localizeText(text: string, parameters: any[] = []): string {
    return this.i18nResourceModel.getResourceBundle().getText(text, parameters);
  }

  buildTransform() {
    const colObj = {};
    // Adding changed mappings
    for (const mapping in this.changedMappingList) {
      const mappingObj = {
        expression: this.changedMappingList[mapping].isExpression
          ? this.changedMappingList[mapping].expression
          : this.changedMappingList[mapping].srcMap,
        target: this.changedMappingList[mapping].tName,
      };
      if (mappingObj.expression !== mappingObj.target || this.changedMappingList[mapping].isExpression) {
        mappingObj.expression = this.generateExpressionSyntax(
          mappingObj.expression,
          this.changedMappingList[mapping].dataType,
          this.changedMappingList[mapping].isExpression
        );
        this.transformObj.attributeMappings.push(mappingObj);
      }
      colObj[this.changedMappingList[mapping].name] = true;
    }

    const tableData: ITransformationModel = this.getView().getModel("UIModel").getData();
    const finalTargetAttributes = tableData.tableModel.map((attr) => attr.tName);
    // Adding previously existing mappings
    if (this.attrMappings) {
      this.attrMappings.toArray().forEach((map) => {
        if (map.target && !colObj[map.target.name] && finalTargetAttributes.includes(map.target.name)) {
          if (map.expression) {
            this.transformObj.attributeMappings.push({
              expression: this.generateExpressionSyntax(map.expression, map.target.datatype, true),
              target: map.target.name,
            });
          } else if (map.source.name) {
            this.transformObj.attributeMappings.push({
              expression: this.generateExpressionSyntax(map.source.name, map.target.datatype, false),
              target: map.target.name,
            });
          }
        }
      });
    }

    this.transformObj.name = this.transformName;
    this.transformObj.description = this.transformDesc;
  }

  generateExpressionSyntax(expression: string, templateType: string, isExpression: boolean): string | number {
    let exp: string | number;
    if (!isExpression) {
      exp = `"${expression}"`;
    } else if (isExpression && (this.isDateTimeType(templateType) || this.isNumberType(templateType))) {
      exp = expression;
    } else {
      exp = `'${expression}'`;
    }
    return exp;
  }

  buildAttributes() {
    const tableModelData: ITransformationModel = this.getView().getModel("UIModel").getData();
    tableModelData.tableModel.forEach((row) => {
      this.aggregatedAttr.push({
        datatype: row.dataType,
        key: row.isKey,
        length: row.length,
        name: row.tName,
        businessName: row.tBusinessName,
        precision: row.precision,
        scale: row.scale,
        isCDCColumn: row.isCDCColumn,
        CDCColumnType: row.CDCColumnType,
        isAutoRenamed: row.validationStates.isAutoRenamed,
        isAutoDatatype: row.validationStates.isAutoDatatype,
        isDifferentKeyInSourceAndTarget: row.validationStates.isDifferentKeyInSourceAndTarget,
      });
    });
  }

  public validate(): Promise<any> {
    this.isValid = true;
    const modelData = this.getView().getModel("UIModel");
    return new Promise(async (resolve, reject) => {
      this.getView().setBusy(true);
      const messagePopoverBtn = this.getView().byId("messagePopoverBtn") as sap.m.Input;
      if (!this.transformName) {
        this.isValid = false;
        modelData.setProperty("/isValidTransformName", false);
        this.addMessage(this.localizeText("invalidTransformName"), this.erroCodes.transformName);
      } else {
        modelData.setProperty("/isValidTransformName", true);
        this.removeMessage(this.erroCodes.transformName);
      }
      let errMsgCnt = this.getErrMessageCount(sap.ui.core.MessageType.Error);
      if (errMsgCnt > 0) {
        this.MessagePopOver.openBy(messagePopoverBtn);
        this.isValid = false;
      } else {
        this.validateTable();
        this.validateFilterExpressions();
        this.setErrorCount();
        errMsgCnt = this.getErrMessageCount(sap.ui.core.MessageType.Error);
        if (errMsgCnt > 0) {
          this.MessagePopOver.openBy(messagePopoverBtn);
          this.isValid = false;
        }
      }
      this.getView().setBusy(false);
      this.isValid ? resolve(true) : reject({ suppress: true });
    });
  }

  replaceNameChar(evt) {
    let adjustedColName = evt.getSource().getValue();
    const path = evt.getSource().getBindingContext("UIModel").getPath();
    const row = evt.getSource().getModel("UIModel").getProperty(path);
    if (
      Utils.isConnectionTypeDWC(
        this.taskDetails.targetSystem.connectionId,
        this.taskDetails.targetSystem.connectionType
      )
    ) {
      adjustedColName = adjustedColName.replaceAll(columnNameRegExDWCTarget, "");
      adjustedColName = this.removeStartingUnderscoreFromName(adjustedColName);
    } else {
      adjustedColName = adjustedColName.replaceAll(columnNameRegEx, "");
    }
    evt.getSource().setValue(adjustedColName);
    const id = this.generateErrId(row.originalColumnName, this.erroCodes.targetInvalidChar);
    this.removeMessage(id);
    row.validationStates.isValidName = true;
  }

  validateNameChar(modelData, path, updateErrorCount) {
    const row: ITransformationRow = modelData.getProperty(`${path}`);
    const id = this.generateErrId(row.originalColumnName, this.erroCodes.targetInvalidChar);
    if (
      Utils.isConnectionTypeDWC(
        this.taskDetails.targetSystem.connectionId,
        this.taskDetails.targetSystem.connectionType
      )
    ) {
      if (columnNameRegExDWCTarget.exec(row.tName)) {
        row.validationStates.isValidName = false;
        this.addMessage(
          this.localizeText("invalidTargetName"),
          id,
          row.tName,
          row.originalColumnName,
          this.localizeText("invalidTargetNameDesc"),
          updateErrorCount
        );
      }
    } else if (columnNameRegEx.exec(row.tName)) {
      row.validationStates.isValidName = false;
      this.addMessage(
        this.localizeText("invalidTargetName"),
        id,
        row.tName,
        row.originalColumnName,
        this.localizeText("invalidTargetNameDesc"),
        updateErrorCount
      );
    }
  }

  private removeStartingUnderscoreFromName(name: string): string {
    if (!/^[a-zA-Z0-9]/.test(name)) {
      // Find the first alphanumeric character in the string
      const firstAlphanumericIndex = name.search(/[a-zA-Z0-9]/);
      if (firstAlphanumericIndex !== -1) {
        return name.slice(firstAlphanumericIndex);
      } else {
        // If no alphanumeric character is found, return an empty string
        return "";
      }
    }
    return name;
  }

  public getResult(): any {
    const oUiModel: sap.ui.model.Model = this.getView().getModel("UIModel");
    const oModelData = oUiModel.getData();
    oModelData.tableModel.forEach((row) => {
      // To check and add default "Skip Mapping" columns to attribute mapping list (as no change event is fired for such default "Skip Mapping" value)
      if (row.srcMap === Constants.SKIP_MAPPING) {
        this.isMappingChanged = true;
        this.changedMappingList[row.originalColumnName] = row;
      }
    });

    if (this.isMappingChanged || this.attrMappings) {
      this.buildTransform();
    }
    if (this.isAttrChanged) {
      this.buildAttributes();
    }

    this.transformObj.filters = this.deSerializeCols(
      this.getView().getModel("UIModel").getProperty("/formattedFilterColumns")
    );

    if (this.isAttrChanged || this.isMappingChanged || this.isFilterChanged) {
      this.isTransformChanged = true;
    }
    return {
      transformObj: this.transformObj,
      task: this.taskDetails,
      isNewTarget: this.isNewTarget,
      isAttrChanged: this.isAttrChanged,
      isMappingChanged: this.isMappingChanged,
      isTransformChanged: this.isTransformChanged,
      aggregatedAttr: this.aggregatedAttr,
      transformDetails: { name: this.transformName },
      isFilterChanged: this.isFilterChanged,
    };
  }
  onTransformChange(oEvent: sap.ui.base.Event) {
    const id = oEvent.getParameter("id");
    const value = oEvent.getParameter("value");

    if (
      (id.includes("transformName") && value !== this.transformName) ||
      (id.includes("transformDesc") && value !== this.transformDesc)
    ) {
      this.isTransformChanged = true;
      this.isMappingChanged = true;
      this.transformDetails = {
        name: id.includes("transformName") ? value : this.transformName,
        desc: id.includes("transformDesc") ? value : this.transformDesc,
      };
    }

    this.transformName = this.transformDetails.name;
    this.transformDesc = this.transformDetails.desc;
    this.transformObj.name = this.transformName;
    this.transformObj.description = this.transformDesc;
    const modelData = this.getView().getModel("UIModel");
    if (!this.transformName) {
      modelData.setProperty("/isValidTransformName", false);
      this.addMessage(this.localizeText("invalidTransformName"), this.erroCodes.transformName);
    } else {
      modelData.setProperty("/isValidTransformName", true);
      this.removeMessage(this.erroCodes.transformName);
    }
  }

  addToChangeList(oEvent, changeObj: any[]) {
    const changedRowPath = oEvent.getSource().getBindingContext("UIModel").getPath();
    const changedRow = oEvent.getSource().getBindingContext("UIModel").getModel("tableModel").getObject(changedRowPath);

    if (changeObj.includes("map")) {
      this.isMappingChanged = true;
      this.changedMappingList[changedRow.originalColumnName] = changedRow;
    }
    if (changeObj.includes("attr")) {
      this.isAttrChanged = true;

      if (this.changedAttrNames[changedRow.originalColumnName]) {
        this.changedAttrObjList = this.changedAttrObjList.filter(
          (x) => x.originalColumnName !== changedRow.originalColumnName
        );
      }

      this.changedAttrNames[changedRow.originalColumnName] = true;
      this.changedAttrObjList.push({
        datatype: changedRow.dataType,
        key: changedRow.isKey,
        length: changedRow.length,
        name: changedRow.tName,
        businessname: changedRow.tBusinessName,
        precision: changedRow.precision,
        scale: changedRow.scale,
        originalColumnName: changedRow.originalColumnName,
        isCDCColumn: changedRow.isCDCColumn,
        isDPIDColumn: changedRow.isDPIDColumn,
        CDCColumnType: changedRow.CDCColumnType,
        isAutoRenamed: changedRow.validationStates.isAutoRenamed,
        isAutoDatatype: changedRow.validationStates.isAutoDatatype,
        isDifferentKeyInSourceAndTarget: changedRow.validationStates.isDifferentKeyInSourceAndTarget,
      });
    }
  }

  onTargetNameChange(oEvent) {
    this.replaceNameChar(oEvent);
    this.validateTargetColumn(oEvent);
    this.addToChangeList(oEvent, ["attr", "map"]);
    const path: string = oEvent.getSource().getBindingContext("UIModel").getPath();
    const row: ITransformationRow = this.getView().getModel("UIModel").getProperty(path);
    this.updateAdditionalText(row.originalColumnName, row.tName);
  }

  onTargetBusinessNameChange(oEvent) {
    const oInput: sap.m.Input = oEvent.getSource() as sap.m.Input;
    const modelData = oInput.getModel("UIModel");
    const path: string = oEvent.getSource().getBindingContext("UIModel").getPath();
    const row: ITransformationRow = this.getView().getModel("UIModel").getProperty(path);
    this.validateTargetColumn(oEvent);
    this.addToChangeList(oEvent, ["attr", "map"]);
  }
  onDatatypeChange(oEvent) {
    const changedRowPath = oEvent.getSource().getBindingContext("UIModel").getPath();
    const row = oEvent.getSource().getBindingContext("UIModel").getModel("tableModel").getObject(changedRowPath);
    row.lengthVisible = Utils.showLength(row.dataType);
    row.precisionVisible = Utils.showPrecision(row.dataType);
    row.scaleVisible = Utils.showPrecision(row.dataType);
    row.length = row.lengthVisible && row.length === undefined ? 1 : row.length;
    row.precision = row.precisionVisible && row.precision === undefined ? 1 : row.precision;
    row.scale = row.scaleVisible && row.scale === undefined ? 0 : row.scale;
    row.enableExpression = this.getEnableExpression(row.dataType, row.isKey);

    if (!row.enableExpression && row.isExpression) {
      row.isExpression = false;
      row.expression = "";
    }
    this.getView().getModel("UIModel").refresh(true);
    const modelData = this.getView().getModel("UIModel");
    const validDataType = this.isValidDataType(modelData, changedRowPath);
    this.isValidLength(modelData, changedRowPath, validDataType);
    this.isValidatePrecision(modelData, changedRowPath, validDataType);
    this.isValidScale(modelData, changedRowPath, validDataType);
    this.validateConvertedDatatype(row);
    this.isValidTargetScalePrecision(modelData, changedRowPath, validDataType);
    if (row.isExpression) {
      if (this.isDateTimeType(row.dataType)) {
        this.handleExpressionChange(oEvent);
        this.setDefaultExpression(row.dataType, changedRowPath, modelData);
      }
      this.isExpressionValid(modelData, changedRowPath);
    }
    this.addToChangeList(oEvent, ["attr"]);
  }

  getSupportedSourceColumns(): any {
    let columns = this.sourceAttr;
    const excludedColumns: string[] = [];
    columns = columns?.filter((c: any) => {
      const allowed = this.isSupportedColumn(c);
      if (!allowed) {
        excludedColumns.push(c);
      }
      return allowed;
    });
    return {
      supported: columns,
      unsupported: excludedColumns,
    };
  }

  isSupportedColumn(column: any): boolean {
    const allowed = true;
    if (
      !column.datatype ||
      ((column.datatype === "string" || column.datatype === "binary") && (!column.length || column.length > 5000))
    ) {
      return false;
    }
    return allowed;
  }

  getDatatype(templateType: string): string {
    const ttype = this.ttypes.find((x) => x.templateType === templateType);
    return ttype ? ttype.datatype : "";
  }

  getEnableExpression(templateType: string, isKey: boolean): boolean {
    if (isKey) {
      return false;
    }
    const datatype = this.getDatatype(templateType);
    if (datatype === "DECIMAL") {
      return templateType !== "decfloat16" && templateType !== "decfloat34";
    }
    return (
      datatype === "STRING" ||
      datatype === "INTEGER" ||
      datatype === "FLOATING" ||
      datatype === "DATE" ||
      datatype === "TIME" ||
      datatype === "DATETIME"
    );
  }

  getExpression(target: any) {
    const mapping = this.taskDetails.transform?.attributeMappings
      .toArray()
      ?.filter((el: any) => el.target.name === target.name);
    if (mapping?.length === 1) {
      return mapping[0].expression;
    }
  }

  private getRowSrcMap(target, sourceList) {
    let srcMap = this["skipUnmapCols"] && !target.isCDCColumn && !this.isNewTarget ? Constants.SKIP_MAPPING : "empty";

    if (!target.isCDCColumn && !target.isDPIDColumn) {
      srcMap = sourceList.find((x) => x.key === target.name) ? target.name : target.key ? "empty" : srcMap;
    }
    return srcMap;
  }

  public handleTransformChange(oEvent) {
    const modelData = this.getView().getModel("UIModel");
    const oSource = oEvent.getSource();
    const path = oSource.getBindingContext("UIModel").getPath();
    const changedRow = oSource.getBindingContext("UIModel").getModel("tableModel").getObject(path);
    const originalColumnName = modelData.getProperty(`${path}/originalColumnName`);
    const uniqueMapping: Set<string> = new Set();
    if (oSource.getSelected()) {
      this.removeMessage(this.generateErrId(originalColumnName, this.erroCodes.mappingMandatory));
      modelData.setProperty(`${path}/validationStates/isValidMapping`, true);
      this.removeMessage(this.generateErrId(originalColumnName, this.erroCodes.length));
      modelData.setProperty(`${path}/validationStates/isValidLength`, true);
      this.removeMessage(this.generateErrId(originalColumnName, this.erroCodes.scale));
      modelData.setProperty(`${path}/validationStates/isValidScale`, true);
      this.removeMessage(this.generateErrId(originalColumnName, this.erroCodes.precision));
      modelData.setProperty(`${path}/validationStates/isValidPrecision`, true);
      this.removeMessage(this.generateErrId(originalColumnName, this.erroCodes.dataType));
      modelData.setProperty(`${path}/validationStates/isValidDataType`, true);
      this.setDefaultExpression(modelData.getProperty(`${path}/dataType`), path, modelData);
      this.removeMessage(this.generateErrId(originalColumnName, this.erroCodes.scaleAndPrecision));
      changedRow.isKey = false;
      modelData.getData().tableModel.forEach((row, index) => {
        this.isMultipleSourceColumnMapped(modelData, `/tableModel/${index}`, uniqueMapping);
      });
      this.isExpressionValid(modelData, path);
    } else {
      const id = this.generateErrId(modelData.getProperty(`${path}/originalColumnName`), this.erroCodes.expression);
      modelData.setProperty(`${path}/validationStates/isValidExpression`, true);
      this.removeMessage(id);
      this.validateMappingColumn(modelData, oEvent);
      const validDataType = this.isValidDataType(modelData, path);
      this.isValidLength(modelData, path, validDataType);
      this.isValidatePrecision(modelData, path, validDataType);
      this.isValidScale(modelData, path, validDataType);
      this.validateConvertedDatatype(modelData.getProperty(path));
      this.isValidTargetScalePrecision(modelData, path, validDataType);
    }

    this.addToChangeList(oEvent, ["map"]);
  }

  public setDefaultExpression(dataType: string, path: string, modelData: sap.ui.model.Model) {
    if (dataType === "time") {
      modelData.setProperty(`${path}/expression`, Constants.DATE_TIME_EXP_FUNCTIONS.TIME.method);
    } else if (dataType === "date") {
      modelData.setProperty(`${path}/expression`, Constants.DATE_TIME_EXP_FUNCTIONS.DATE.method);
    } else if (dataType === "timestamp") {
      modelData.setProperty(`${path}/expression`, Constants.DATE_TIME_EXP_FUNCTIONS.TIMESTAMP.method);
    }
  }
  public handleExpressionChange(oEvent) {
    const modelData = this.getView().getModel("UIModel");
    const path = oEvent.getSource().getBindingContext("UIModel").getPath();
    this.isExpressionValid(modelData, path);
    this.addToChangeList(oEvent, ["map"]);
  }

  public onSourceMappingChange(oEvent) {
    const model = this.getView().getModel("UIModel");
    const oSource = oEvent.getSource();
    const path = oSource.getBindingContext("UIModel").getPath();
    const row = model.getProperty(path);
    if (this.validateMappingColumn(model, oEvent)) {
      if (!row.isExpression) {
        const validDataType = this.isValidDataType(model, path);
        this.isValidLength(model, path, validDataType);
        this.isValidatePrecision(model, path, validDataType);
        this.isValidScale(model, path, validDataType);
        this.validateConvertedDatatype(row);
        this.isValidTargetScalePrecision(model, path, validDataType);
        this.validateSameKeysInSourceAndTargetForHDLFDeltashare(model, true);
      }
    } else {
      //  For empty source mapping and Skip Mapping, datatype error should be removed
      if (row.srcMap === "empty" || row.srcMap === Constants.SKIP_MAPPING) {
        const id = this.generateErrId(row.originalColumnName, this.erroCodes.dataType);
        row.validationStates.isValidDataType = true;
        this.removeMessage(id, true);
      }
    }
    this.addToChangeList(oEvent, ["map"]);
  }

  public getTableRow(target: any, sourceList: any[]): ITransformationRow {
    this.colCounter += 1;
    const rowObj: ITransformationRow = {} as ITransformationRow;
    rowObj.name = target.name;
    rowObj.tName = target.name;
    if (
      Utils.isConnectionTypeDWC(
        this.taskDetails.targetSystem.connectionId,
        this.taskDetails.targetSystem.connectionType
      )
    ) {
      rowObj.tBusinessName = target.businessName ? target.businessName : target.name;
    }
    rowObj.originalColumnName = `${target.name}_${this.colCounter}`;
    rowObj.dataType = this.templateTypes.find((x) => x.key === target.datatype) ? target.datatype : undefined;
    rowObj.srcMap = this.getRowSrcMap(target, sourceList);
    rowObj.sourceList = sourceList;

    // Datatype
    const templateType = target.datatype;
    rowObj.lengthVisible = Utils.showLength(templateType);
    rowObj.precisionVisible = Utils.showPrecision(templateType);
    rowObj.scaleVisible = Utils.showPrecision(templateType);
    rowObj.length = target.length;
    rowObj.precision = target.precision;
    rowObj.scale = target.scale;
    // is target exists or else source key to be considered
    rowObj.isKey = target.key;
    // Expression
    const expression = this.getExpression(target);
    rowObj.enableExpression = this.getEnableExpression(templateType, rowObj.isKey);
    const isExpression = expression ? true : false;
    if (!expression && target.attributeMapping) {
      rowObj.srcMap = target.attributeMapping?.source?.name;
    }
    rowObj.isExpression = rowObj.enableExpression ? isExpression : false;
    rowObj.expression = isExpression ? expression : "";

    // both info for CDC columns are neededß
    rowObj.isCDCColumn = target.isCDCColumn;
    rowObj.CDCColumnType = target.CDCColumnType;

    // DPID Column info
    rowObj.isDPIDColumn = target.isDPIDColumn;

    rowObj.states = {};
    rowObj.rowState = "None";

    // validation objects
    rowObj.validationStates = {
      isValidName: true,
      isValidBusinessName: true,
      isValidDataType: true,
      isValidLength: true,
      isValidPrecision: true,
      isValidScale: true,
      isValidMapping: true,
      isValidExpression: true,
      isAutoDatatype: target.isAutoDatatype || false,
      isAutoRenamed: target.isAutoRenamed || false,
      isDifferentKeyInSourceAndTarget: this.isDifferentKeyInSourceAndTarget(rowObj),
    };
    rowObj.isNewColumn = false;
    return rowObj;
  }

  public addTransformationRow(): void {
    const uIModel = this.getView().getModel("UIModel").getData();
    const columnNames = uIModel.tableModel.map((col) => col.tName);
    const target = {
      datatype: "string",
      name: this.generateUniqColumnName(columnNames),
      length: 50,
      key: false,
      isCDCColumn: false,
      isDPIDColumn: false,
    };
    let sourceList = [];
    if (uIModel.tableModel.length > 0) {
      sourceList = uIModel.tableModel[0].sourceList;
    } else {
      sourceList = this.sourceAttr.map((source) => ({
        key: source.name,
        text: source.name,
      }));
      sourceList.push({
        key: "empty",
        text: "",
      });
    }
    const rowObj = this.getTableRow(target, sourceList);
    rowObj.expression = "";
    rowObj.isExpression = false;
    rowObj.isNewColumn = true;
    // For few connections CDC cols are static and can not be reorderd and always kept at the bottom of the table
    uIModel.tableModel.splice(uIModel.tableModel.length - this.reservedLength, 0, rowObj);
    // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
    uIModel.tableColumnsCount = this.localizeText("txtMappingCount", [uIModel.tableModel.length.toString()]);
    this.isAttrChanged = true;
    this.changedAttrNames[rowObj.originalColumnName] = true;
    this.changedAttrObjList.push({
      datatype: rowObj.dataType,
      key: rowObj.isKey,
      length: rowObj.length,
      name: rowObj.tName,
      businessName: rowObj.tName,
      precision: rowObj.precision,
      scale: rowObj.scale,
      originalColumnName: rowObj.originalColumnName,
      isCDCColumn: rowObj.isCDCColumn,
      CDCColumnType: rowObj.CDCColumnType,
      isDPIDColumn: rowObj.isDPIDColumn,
      isAutoRenamed: rowObj.validationStates.isAutoRenamed,
      isAutoDatatype: rowObj.validationStates.isAutoDatatype,
      isDifferentKeyInSourceAndTarget: rowObj.validationStates.isDifferentKeyInSourceAndTarget,
      isNewColumn: rowObj.isNewColumn,
    });
    this.getView().getModel("UIModel").refresh(true);
    this.removeMessage(this.erroCodes.colCount);
    const id: string = this.generateErrId(rowObj.originalColumnName, this.erroCodes.mappingMandatory);
    rowObj.validationStates.isValidMapping = false;
    this.addMessage(
      this.localizeText("emptySource"),
      id,
      rowObj.tName,
      rowObj.originalColumnName,
      this.localizeText("emptySourceDescription")
    );
  }

  /**
   *
   * @param transformationTable grid table object
   * @param index selected row index
   * @returns selected row object
   */
  public getRowFromBindingContext(transformationTable: sap.ui.table.Table, index: number) {
    const transformBinding = transformationTable.getBinding("rows") as any; // Type casted to any to avoid build failures ,getAllCurrentContexts() is not available in getBinding:sap.ui.model.ListBinding but actually accessible while debugging
    const getCurrentContext = transformBinding.getAllCurrentContexts();
    const currentContextPath: string = getCurrentContext[index].sPath; // sPath is the actual binding context of the row (if search or filter applied then model row indices may vary)
    return transformationTable.getModel("UIModel").getProperty(currentContextPath);
  }

  public removeTransformationRows(): void {
    const transformationTable: sap.ui.table.Table = this.getView().byId("transformationTable") as sap.ui.table.Table;
    const uIModel: ITransformationModel = transformationTable.getModel("UIModel").getData();
    let selectedRows: number[] = transformationTable.getSelectedIndices();
    this.isAttrChanged = true;
    // selected indices comes in ascending order , sorting array to descending
    const rowCount: number = (transformationTable.getBinding("rows") as any).getLength() - selectedRows.length; // Type casted to any to avoid build failures ,getLength() is not available in getBinding:sap.ui.model.Binding but actually accessible while debugging
    selectedRows = selectedRows.sort((a, b) => b - a);
    selectedRows.forEach((i) => {
      const changedRow = this.getRowFromBindingContext(transformationTable, i);
      let indx = this.changedAttrObjList.findIndex((attr) => attr.originalColumnName === changedRow.originalColumnName);
      if (indx >= 0) {
        this.changedAttrObjList.splice(indx, 1);
      }
      delete this.changedAttrNames[changedRow.originalColumnName];
      if (this.changedMappingList[changedRow.originalColumnName]) {
        delete this.changedMappingList[changedRow.originalColumnName];
        this.isMappingChanged = true;
      }
      const errRows = this.MessageManager.getMessageModel()
        .getData()
        .filter((msg) => msg.code.includes(changedRow.originalColumnName));
      errRows.forEach((msg) => {
        this.removeMessage(msg.code);
      });
      indx = uIModel.tableModel.findIndex((attr) => attr.originalColumnName === changedRow.originalColumnName);
      uIModel.tableModel.splice(indx, 1);
    });
    uIModel.tableColumnsCount = this.localizeText("txtMappingCount", [rowCount.toString()]);
    const modelData = this.getView().getModel("UIModel");
    modelData.refresh(true);
    // Don't update bindings when checking duplicate column names
    this.isDuplicateTargetName(modelData, false);
    // setProperty will update all the bindings
    this.validateColumnCount(modelData);
    const uniqueMapping: Set<string> = new Set();
    this.handleToolbarbuttons(modelData);
    modelData.getData().tableModel.forEach((row, index) => {
      this.isMultipleSourceColumnMapped(modelData, `/tableModel/${index}`, uniqueMapping);
    });
    this.validateSameKeysInSourceAndTargetForHDLFDeltashare(modelData, true);
  }

  /**
   * Description
   * @param {sap.ui.base.Event} oEvent
   * @returns {any}
   * */
  onSearchTarget(oEvent: sap.ui.base.Event) {
    const aFilters = [];
    const sQuery = (oEvent.getSource() as sap.m.SearchField).getValue();
    if (sQuery && sQuery.length > 0) {
      const filter = new sap.ui.model.Filter("tName", sap.ui.model.FilterOperator.Contains, sQuery);
      aFilters.push(filter);
    }
    const transformationTable: sap.ui.table.Table = this.getView().byId("transformationTable") as sap.ui.table.Table;
    const uIModel = transformationTable.getModel("UIModel").getData();
    const oBinding: any = transformationTable.getBinding("rows");
    oBinding.filter([aFilters]);
    const count = oBinding.getLength().toString();
    uIModel.tableColumnsCount = this.localizeText("txtMappingCount", [count]);
    this.getView().getModel("UIModel").refresh(true);
    this.handleToolbarbuttons(transformationTable.getModel("UIModel"));
  }

  private generateUniqColumnName(columnNames: string[]): string {
    const columnNamesStr: string = columnNames.join();
    const matchedColumnNames: string[] = columnNamesStr.match(/NewColumn_[0-9]+/g);
    if (!matchedColumnNames) {
      return "NewColumn_1";
    }
    const matchedColumnNamesStr: string = matchedColumnNames.join();
    let cntr = 2;
    let regEx = new RegExp(`NewColumn_${cntr}`);
    while (matchedColumnNamesStr.match(regEx)) {
      cntr += 1;
      regEx = new RegExp(`NewColumn_${cntr}`);
    }
    return `NewColumn_${cntr}`;
  }

  // Taken code from Dataflowmodeler. We should maintain common constant & util file to follow DRY principle
  public dataTypeIconFormatter(datatype: string): string {
    return DATATYPE_TO_ICON[datatype] || SIMPLE_TYPE_ICON;
  }
  // End

  public onColumnSelect(event): void {
    const context = event.getSource().getSelectedItem().getBindingContext("UIModel");
    const path = context.getPath();
    const uiModel = context.getModel("UIModel");
    const operators = this.getSupportedFilterOperator(uiModel.getProperty(path).datatype);
    uiModel.setProperty("/filterComparisionOperators", operators);
    this.clearFilterFormExp();
    uiModel.setProperty("/selectedFilterColumn", uiModel.getObject(path));
  }
  //supporting not between and not equal to operator for dwc/hana/abap as source
  isOperatorSupported(operator: any, dataType: string, sourceConnType: string, sourceConnId: string): boolean {
    const isABAP = Utils.isConnectionTypeABAP(sourceConnType);
    const isHANA = Utils.isConnectionTypeHANA(sourceConnId, sourceConnType);
    const isDwcHana = Utils.isConnectionTypeDwcHANA(sourceConnId, sourceConnType);

    const isNotBetween = operator.key === Constants.NOT_BETWEEN_COMPARISON;
    const isNotEqual = operator.key === Constants.NOTEQUALTO_COMPARISON;

    if (isABAP) {
      if (isNotBetween) return dataType !== "bool";
      if (isNotEqual) return true;
    }

    if (isHANA || isDwcHana) {
      if (isNotBetween) return dataType !== "binary";
      if (isNotEqual) return true;
    }

    return false;
  }

  getSupportedFilterOperator(dataType: string) {
    const sourceConnType = this.taskDetails.sourceSystem.connectionType;
    const sourceConnId = this.taskDetails.sourceSystem.connectionId;
    const filterComparisionOperators = [];
    this.filterComparisionOperators.forEach((operators) => {
      // Allow all filters for (ABAP and HANA) connection type except boolean
      //Adding not equal to and not between operators for ABAP , HANA Cloud , S4Hanaop/cloud
      const isNotBetween = operators.key === Constants.NOT_BETWEEN_COMPARISON;
      const isNotEqual = operators.key === Constants.NOTEQUALTO_COMPARISON;
      if (isNotBetween || isNotEqual) {
        const isSupported = this.isOperatorSupported(operators, dataType, sourceConnType, sourceConnId);
        if (isSupported) {
          filterComparisionOperators.push(operators);
        }
      } else if (
        !operators.notSupported ||
        (Utils.isConnectionTypeABAP(sourceConnType) && dataType !== "bool") ||
        (Utils.isConnectionTypeHANA(sourceConnId, sourceConnType) && dataType !== "binary") ||
        (Utils.isConnectionTypeDwcHANA(sourceConnId, sourceConnType) && dataType !== "binary")
      ) {
        filterComparisionOperators.push(operators);
      } else if (!operators.notSupported.includes(dataType)) {
        filterComparisionOperators.push(operators);
      }
    });
    return filterComparisionOperators;
  }
  public onAddExpression(): void {
    const uiModel = this.getView().getModel("UIModel");
    const addNewExp = uiModel.getProperty("/filterExpForm");
    const colElement = Object.assign({}, addNewExp);
    colElement.isExcluding = false;

    const colElements = [colElement, ...uiModel.getProperty("/selectedFilterColumn/elements")];
    uiModel.setProperty("/selectedFilterColumn/elements", colElements);
    uiModel.setProperty("/filterExpression", this.filterExpression(uiModel.getProperty("/formattedFilterColumns")));
    uiModel.setProperty(
      "/filteredColumnsCount",
      this.filterColumnsCount(uiModel.getProperty("/formattedFilterColumns"))
    );
    this.clearFilterFormExp();
    this.isFilterChanged = true;
  }

  public validateFilterInput(event): void {
    const inpSource = event.getSource();
    const selectedValueItem = this.getView().getModel("UIModel")?.getProperty("/selectedFilterColumn");
    const inputType = Constants.INPUT_TYPE(selectedValueItem?.datatype);
    if (inputType === sap.m.InputType.Date || inputType === sap.m.InputType.Datetime) {
      inpSource.setValueFormat(
        inputType === sap.m.InputType.Date ? this.inputFormat.dateFormat : this.inputFormat.dateTimeFormat
      );
      if (!inpSource.isValidValue()) {
        inpSource.setValue("");
      }
    }
    this.updateFilterExpressionError(selectedValueItem);
  }

  public validateFilterInputAddExp(event): void {
    const selectedValueItem = this.getView().getModel("UIModel")?.getProperty("/selectedFilterColumn");
    const inputType = Constants.INPUT_TYPE(selectedValueItem?.datatype);
    const inpSource = event.getSource();
    if (inputType === sap.m.InputType.Number) {
      this.updateFilterAddExpressionError();
    } else if (inputType === sap.m.InputType.Date || inputType === sap.m.InputType.Datetime) {
      inpSource.setValueFormat(
        inputType === sap.m.InputType.Date ? this.inputFormat.dateFormat : this.inputFormat.dateTimeFormat
      );
      if (!inpSource.isValidValue()) {
        inpSource.setValue("");
      }
    }
  }

  private validateFilterExpressions(): void {
    const columns = this.getView().getModel("UIModel").getProperty("/formattedFilterColumns");
    for (const column of columns) {
      if (column.elements.length > 0) {
        this.updateFilterExpressionError(column, false);
      }
    }
  }

  private updateFilterExpressionError(selectedColumn: IFormattedFilterColumns, updateErrorCount: boolean = true): void {
    const inputType = Constants.INPUT_TYPE(selectedColumn.datatype);
    const columnNameCode = this.getFilterColumnCodeForValidation(selectedColumn.name);
    let emptyCnt = 0;
    let numericInvalidCnt = 0;
    if (inputType === sap.m.InputType.Number) {
      const numericColumnNameCode = this.getFilterColumnCodeForValidation(selectedColumn.name, "-invalid-numeric");
      for (const element of selectedColumn.elements) {
        const emptyValCond = this.checkForEmptyFilterValues(element.low, element.high, element.comparison);
        const numericInCond = this.checkForInvalidNumericFilterValues(element.low, element.high, element.comparison);
        if (!emptyValCond) {
          emptyCnt++;
        } else if (!numericInCond) {
          numericInvalidCnt++;
        }
      }
      if (numericInvalidCnt > 0) {
        this.addMessage(
          this.localizeText("rftdFilterValidateInvalidNumericMsg", [numericInvalidCnt, selectedColumn.name]),
          numericColumnNameCode,
          "",
          "",
          "",
          updateErrorCount
        );
      } else {
        this.removeMessage(numericColumnNameCode, updateErrorCount);
      }
    } else {
      for (const element of selectedColumn.elements) {
        if (!this.checkForEmptyFilterValues(element.low, element.high, element.comparison)) {
          emptyCnt++;
        }
      }
    }
    if (emptyCnt > 0) {
      this.addMessage(
        this.localizeText("rftdFilterValidateEmptyMsg", [emptyCnt, selectedColumn.name]),
        columnNameCode,
        "",
        "",
        "",
        updateErrorCount
      );
    } else {
      this.removeMessage(columnNameCode, updateErrorCount);
    }
  }

  private updateFilterAddExpressionError() {
    const uiModel = this.getView().getModel("UIModel");
    const selectedValueItem = uiModel.getProperty("/selectedFilterColumn");
    const form = uiModel.getProperty("/filterExpForm");
    const numericColumnNameCode = this.getFilterColumnCodeForValidation(
      selectedValueItem.name,
      "-add-exp-invalid-numeric"
    );
    if (
      (form.low && form.low.trim() && !NUMBER_REGEX.test(form.low)) ||
      ((form.comparison === Constants.BETWEEN_COMPARISON || form.comparison === Constants.NOT_BETWEEN_COMPARISON) &&
        form.high &&
        form.high.trim() &&
        !NUMBER_REGEX.test(form.high))
    ) {
      this.addMessage(this.localizeText("rftdFilterValidateAddExpInvalidNumericMsg"), numericColumnNameCode);
    } else {
      this.removeMessage(numericColumnNameCode);
    }
  }

  private getFilterColumnCodeForValidation(columnName: string, additionalText: string = ""): string {
    return `${columnName}-filter-expression${additionalText}`;
  }

  public valueFormatter(value: string): string {
    const selectedValueItem = this.getView().getModel("UIModel")?.getProperty("/selectedFilterColumn");
    const inputType = Constants.INPUT_TYPE(selectedValueItem?.datatype);
    return value &&
      value.trim() &&
      ((inputType === sap.m.InputType.Number && NUMBER_REGEX.test(value)) || inputType !== sap.m.InputType.Number)
      ? sap.ui.core.ValueState.None
      : sap.ui.core.ValueState.Error;
  }

  public valueFormatterAddExp(value: string): string {
    const selectedValueItem = this.getView().getModel("UIModel")?.getProperty("/selectedFilterColumn");
    const inputType = Constants.INPUT_TYPE(selectedValueItem?.datatype);
    return inputType === sap.m.InputType.Number && value && value.trim() && !NUMBER_REGEX.test(value)
      ? sap.ui.core.ValueState.Error
      : sap.ui.core.ValueState.None;
  }

  private checkForEmptyFilterValues(low: string, high: string, comparison: string): boolean {
    const isBetweenComparison =
      comparison === Constants.BETWEEN_COMPARISON || comparison === Constants.NOT_BETWEEN_COMPARISON;

    return low.trim() !== "" && ((high.trim() !== "" && isBetweenComparison) || !isBetweenComparison);
  }

  private checkForInvalidNumericFilterValues(low: string, high: string, comparison: string): boolean {
    const isBetweenComparison =
      comparison === Constants.BETWEEN_COMPARISON || comparison === Constants.NOT_BETWEEN_COMPARISON;

    return NUMBER_REGEX.test(low) && ((NUMBER_REGEX.test(high) && isBetweenComparison) || !isBetweenComparison);
  }

  public checkForValidFilterValues(
    low: string,
    high: string,
    comparison: string,
    column?: IFormattedFilterColumns
  ): boolean {
    const selectedValueItem = column || this.getView().getModel("UIModel")?.getProperty("/selectedFilterColumn");
    const inputType = Constants.INPUT_TYPE(selectedValueItem?.datatype);
    return (
      this.checkForEmptyFilterValues(low, high, comparison) &&
      ((inputType === sap.m.InputType.Number && this.checkForInvalidNumericFilterValues(low, high, comparison)) ||
        inputType !== sap.m.InputType.Number)
    );
  }

  public handleFilterInput(event): void {
    this.validateFilterInput(event);
    this.deBounceFilteredInput();
  }

  public onRemoveExpression(event): void {
    const path = event.getSource().getBindingContext("UIModel").getPath();
    const model = this.getView().getModel("UIModel");
    const removeColExp = model.getObject(path);
    const allFilteredColExps = model.getProperty("/selectedFilterColumn/elements");

    // Check object reference
    const allFilteredColExpsAfterRemove = allFilteredColExps.filter((filterExp) => filterExp !== removeColExp);

    model.setProperty("/selectedFilterColumn/elements", allFilteredColExpsAfterRemove);
    model.setProperty("/filterExpression", this.filterExpression(model.getProperty("/formattedFilterColumns")));
    model.setProperty("/filteredColumnsCount", this.filterColumnsCount(model.getProperty("/formattedFilterColumns")));
    this.updateFilterExpressionError(model.getProperty("/selectedFilterColumn"));
    this.isFilterChanged = true;
  }

  public filterColumnsCount(formattedColumns: IFormattedFilterColumns[]): number {
    // Check with Abhishek on curr.elements.length ( whether we need to check columns or column expression )
    return formattedColumns.reduce((prev: number, curr) => prev + (curr.elements.length > 0 ? 1 : 0), 0);
  }

  public onSearchField(oEvent): void {
    const aFilters = [];
    const sQuery = (oEvent.getSource() as sap.m.SearchField).getValue();
    const model = this.getView().getModel("UIModel");
    if (sQuery && sQuery.length > 0) {
      const filter = new sap.ui.model.Filter("name", sap.ui.model.FilterOperator.Contains, sQuery);
      aFilters.push(filter);
    }
    const columnFilterList = this.getView()
      .byId("rf-filter-token-list")
      .getBinding("items") as sap.ui.model.json.JSONListBinding;
    columnFilterList.filter(aFilters);
    model.setProperty(
      "/columnCount",
      this.localizeText("rftdFilterColumnCount", [this.taskDetails.sourceObject.name, columnFilterList.getLength()])
    );
  }

  public openColumnInfoPopover(event: IEvent<sap.m.Button, { id: string }>): void {
    const uiModel: sap.ui.model.Model = this.getView().getModel("UIModel");
    const uiModelData = uiModel.getData();
    const sourceControl = event.getSource();
    const filterRow = uiModel.getProperty(sourceControl.getBindingContext("UIModel").getPath());
    const matchedSource = uiModelData.source.find((ele) => ele.name === filterRow.name);
    if (!this.columnInfoPopover) {
      const fragmentId = require("../view/fragment/ColumnInfoPopover.fragment.xml");
      this.columnInfoPopover = sap.ui.xmlfragment(this.createId("ColInfoPopover"), fragmentId, this) as sap.m.Popover;
      this.getView().addDependent(this.columnInfoPopover);
    }
    const bindingContext = sourceControl.getBindingContext("UIModel");
    const path = bindingContext.getPath();
    this.columnInfoPopover.setModel(this.getView().getModel("UIModel"), "columnsModel");
    this.columnInfoPopover.getModel("columnsModel").setProperty(path + "/datatypeText", matchedSource.datatypeText);
    this.columnInfoPopover.getModel("columnsModel").setProperty(path + "/businessName", matchedSource.businessName);
    this.columnInfoPopover.bindElement(`columnsModel>${path}`);
    this.columnInfoPopover.openBy(sourceControl, false);
  }

  public handleFilteredComparison(): void {
    this.deBounceFilteredInput();
    this.updateFilterExpressionError(this.getView().getModel("UIModel")?.getProperty("/selectedFilterColumn"));
  }

  public handleFilterComparison(oEvent): void {
    const prevKey = oEvent.getParameters().previousSelectedItem.getProperty("key");
    if (prevKey === Constants.BETWEEN_COMPARISON || prevKey === Constants.NOT_BETWEEN_COMPARISON) {
      oEvent.getSource().getModel("UIModel").setProperty("/filterExpForm/high", "");
    }
    this.updateFilterAddExpressionError();
  }

  public checkVisibility(dataType: string, compareDataType: string, comparison: string): boolean {
    return (
      dataType === compareDataType &&
      (!comparison || comparison === Constants.BETWEEN_COMPARISON || comparison === Constants.NOT_BETWEEN_COMPARISON)
    );
  }

  public checkInpVisibility(dataType: string, comparison: string): boolean {
    const datatypes = Constants.DATATYPES_MAIN_CATEGORIES;
    return (
      ![datatypes.DATE, datatypes.DATE_TIME, datatypes.TIME_STAMP, datatypes.TIME].includes(dataType) &&
      (!comparison || comparison === Constants.BETWEEN_COMPARISON || comparison === Constants.NOT_BETWEEN_COMPARISON)
    );
  }

  public deBounceFilteredInput(): void {
    // It is fine to use single variable clearSetTimeoutFilterInp for filtered input
    if (this.clearSetTimeoutFilterInp) {
      clearTimeout(this.clearSetTimeoutFilterInp);
    }
    this.clearSetTimeoutFilterInp = setTimeout(() => {
      const model = this.getView().getModel("UIModel");
      model.setProperty("/filterExpression", this.filterExpression(model.getProperty("/formattedFilterColumns")));
      this.isFilterChanged = true;
    }, 100);
  }

  public sortItems(): void {
    const model = this.getView().getModel("UIModel");
    const columnList = this.getView()
      .byId("rf-filter-token-list")
      .getBinding("items") as sap.ui.model.json.JSONListBinding;
    const sortOrder = model.getProperty("/columnItemSortDescending");
    columnList.sort(new sap.ui.model.Sorter("name", !sortOrder));
    model.setProperty("/columnItemSortDescending", !sortOrder);
  }

  private serializeCols(
    columns: sap.cdw.replicationflow.Attribute[],
    filteredColumns: sap.cdw.replicationflow.Filter[] = []
  ): IFormattedFilterColumns[] {
    let noOfFilteredCol = 0;
    const newFormattedArray = [];
    for (const col of columns) {
      const newFormattedCol = {
        name: col.name,
        datatype: col.datatype,
        key: col.key,
        filterNotAllowed: !!col.filterNotAllowed,
        elements: [],
      };
      newFormattedArray.push(newFormattedCol);
      for (let i = 0; i < filteredColumns.length - noOfFilteredCol; i++) {
        if (filteredColumns[i].name === newFormattedCol.name) {
          newFormattedCol.elements = this.serializeColElemExprs(
            filteredColumns[i].filterElements?.toArray() || [],
            col.datatype
          );

          // move filtered column to the filteredColumns.length - 1 - noOfFilteredCol
          const swap = filteredColumns[filteredColumns.length - 1 - noOfFilteredCol];

          filteredColumns[filteredColumns.length - 1 - noOfFilteredCol] = filteredColumns[i];
          filteredColumns[i] = swap;
          noOfFilteredCol++;
          break;
        }
      }
    }
    return newFormattedArray;
  }

  private deSerializeCols(formattedColumns: IFormattedFilterColumns[]): IFilter[] {
    const unFormattedArray = [];
    for (const col of formattedColumns) {
      if (col.elements.length > 0) {
        const elements = [];
        col.elements.forEach((elem) => {
          if (this.checkForValidFilterValues(elem.low, elem.high, elem.comparison, col)) {
            const isNotEqual = elem.comparison === Constants.NOTEQUALTO_COMPARISON;
            if (isNotEqual) {
              elem.isExcluding = true;
              elem.comparison = Constants.EQUALTO_COMPARISON;
            } else if (elem.comparison === Constants.NOT_BETWEEN_COMPARISON) {
              elem.isExcluding = true;
              elem.comparison = Constants.BETWEEN_COMPARISON;
            } else {
              elem.isExcluding = false;
            }
            elements.push(elem);
          }
        });
        unFormattedArray.push({
          name: col.name,
          elements,
        });
      }
    }
    return unFormattedArray;
  }

  private serializeColElemExprs(
    colElems: sap.cdw.replicationflow.FilterElement[] = [],
    dataType: string
  ): IFilterElement[] {
    const formattedElems: IFilterElement[] = [];

    for (const elem of colElems) {
      let comparison = elem.comparison;
      const isExcluding = elem.isExcluding;
      const equalTo = comparison === Constants.EQUALTO_COMPARISON;
      if (equalTo && isExcluding) {
        comparison = Constants.NOTEQUALTO_COMPARISON;
      }
      if (comparison === Constants.BETWEEN_COMPARISON && isExcluding) {
        comparison = Constants.NOT_BETWEEN_COMPARISON;
      }

      const newElem: IFilterElement = {
        low: elem.low,
        high: elem.high ? elem.high : "",
        comparison,
        isExcluding,
      };

      formattedElems.push(newElem);
    }

    return formattedElems;
  }

  private clearFilterFormExp(): void {
    const uiModel = this.getView().getModel("UIModel");
    const selectedValueItem = uiModel.getProperty("/selectedFilterColumn");
    uiModel.setProperty("/filterExpForm/comparison", Constants.COMPARISION_OPERATORS[0].key);
    uiModel.setProperty("/filterExpForm/low", "");
    uiModel.setProperty("/filterExpForm/high", "");
    uiModel.setProperty("/filterExpForm/isExcluding", false);
    if (selectedValueItem) {
      const numericColumnNameCode = this.getFilterColumnCodeForValidation(
        selectedValueItem.name,
        "-add-exp-invalid-numeric"
      );
      this.removeMessage(numericColumnNameCode);
    }
  }

  private filterExpression(formattedColumns: IFormattedFilterColumns[]): string {
    let exp = "";
    const andExp = " AND ";
    const orExp = " OR ";
    const openBrace = "( ";
    const closeBrace = " )";
    for (const col of formattedColumns) {
      let colExp = "";
      let colExpFlag = false;
      for (const colElement of col.elements) {
        const colElementCond = this.checkForValidFilterValues(
          colElement.low,
          colElement.high,
          colElement.comparison,
          col
        );
        const low = colElement.low;
        const high = colElement.high;
        if (colElementCond) {
          if (!colExpFlag) {
            colExp += openBrace;
            colExpFlag = true;
          } else {
            colExp += orExp;
          }

          colExp += `${col.name} ${colElement.comparison} ${
            colElement.comparison === Constants.BETWEEN_COMPARISON ||
            colElement.comparison === Constants.NOT_BETWEEN_COMPARISON
              ? `${low},${high}`
              : low
          }`;
        }
      }
      if (colExpFlag) {
        exp += (exp !== "" ? andExp : "") + colExp + closeBrace;
      }
    }
    return exp;
  }

  /**
   * opens opopover for source dataset for skipped cdc and LOB Columns
   */
  /**
   * @param {*} evt
   * @memberof AddTransformationDialogClass
   */
  private onClickInvalidColumnPopover(tab: string, evt): void {
    const oButton = evt.getSource(),
      oView = this.getView();
    const uiModel = this.getView().getModel("UIModel");
    if (!this.PopoverForInvalidColumns) {
      const fragmentId = require("../view/fragment/InvalidColumnsPopover.fragment.xml");
      this.PopoverForInvalidColumns = sap.ui.xmlfragment(
        this.createId("InvalidColsInfoPopover"),
        fragmentId,
        this
      ) as sap.m.Popover;
      oView.addDependent(this.PopoverForInvalidColumns);
    }
    let invalidColumnsMessage: string;
    if (tab === "filter") {
      invalidColumnsMessage = this.localizeText("messageForSourceInvalidColumns", [
        this.sourceInvalidColumns.join(", "),
      ]);
    }
    if (tab === "mapping") {
      invalidColumnsMessage = this.localizeText("messageForTargetInvalidColumns", [
        this.targetInvalidColumns.join(", "),
      ]);
    }

    uiModel.setProperty("/invalidColumns", invalidColumnsMessage);

    this.PopoverForInvalidColumns.openBy(oButton, false);
  }

  /**
   * Initializes message manager and model
   */
  private initMessageManager(): void {
    this.MessageManager = sap.ui.getCore().getMessageManager();
    this.getView().setModel(this.MessageManager.getMessageModel(), "message");
    this.MessageManager.registerObject(this.getView().byId("TransoformationPage"), true);
    this.MessageManager.removeAllMessages();
    this.createMessagePopover();
  }

  /**
   * Creates message popover template
   */
  private createMessagePopover(): void {
    this.MessagePopOver = new sap.m.MessagePopover({
      id: "transformationValidationPopover",
      items: {
        path: "message>/",
        template: new sap.m.MessageItem({
          title: "{message>message}",
          subtitle: "{message>additionalText}",
          type: "{message>type}",
          description: "{message>description}",
          markupDescription: true,
        }),
      },
      groupItems: true,
    });

    this.getView().byId("messagePopoverBtn").addDependent(this.MessagePopOver);
  }

  /* buttonIconFormatter() {
    let sIcon;
    const aMessages = this.MessageManager.getMessageModel().oData;

    aMessages.forEach(function(sMessage) {
      switch (sMessage.type) {
        case "Error":
          sIcon = "sap-icon://error";
          break;
        case "Warning":
          sIcon = sIcon !== "sap-icon://error" ? "sap-icon://alert" : sIcon;
          break;
        case "Success":
          sIcon = "sap-icon://error" && sIcon !== "sap-icon://alert" ? "sap-icon://sys-enter-2" : sIcon;
          break;
        default:
          sIcon = !sIcon ? "sap-icon://information" : sIcon;
          break;
      }
    });

    return sIcon;
  } */

  /**
   *
   * @returns Button type according to message seviarity
   */
  buttonTypeFormatter() {
    let sHighestSeverity = sap.m.ButtonType.Accept;
    const aMessages = this.MessageManager.getMessageModel().oData;
    aMessages.forEach(function (sMessage) {
      switch (sMessage.type) {
        case "Error":
          sHighestSeverity = sap.m.ButtonType.Reject;
          break;
        case "Warning":
          sHighestSeverity = sap.m.ButtonType.Attention;
          break;
        case "Information":
          sHighestSeverity = sap.m.ButtonType.Default;
          break;
        default:
          sHighestSeverity = sap.m.ButtonType.Accept;
          break;
      }
    });

    const messagePopoverBtn = this.getView().byId("messagePopoverBtn") as sap.m.Button;
    messagePopoverBtn.removeStyleClass("statusIconError statusIconWarning statusIconInfo");
    if (sHighestSeverity === sap.m.ButtonType.Accept) {
      messagePopoverBtn.addStyleClass("");
    } else if (sHighestSeverity === sap.m.ButtonType.Reject) {
      messagePopoverBtn.addStyleClass("statusIconError");
    } else if (sHighestSeverity === sap.m.ButtonType.Default) {
      messagePopoverBtn.addStyleClass("statusIconInfo");
    } else if (sHighestSeverity === sap.m.ButtonType.Attention) {
      messagePopoverBtn.addStyleClass("statusIconWarning");
    }
    return sHighestSeverity;
  }
  /**
   * Triggered on click of validation button
   * @param {sap.ui.base.Event} oEvent
   */
  public onpoverclick(oEvent: sap.ui.base.Event): void {
    this.MessagePopOver.toggle(oEvent.getSource() as sap.m.Button);
  }

  /**
   * Generates unique error Id
   * @param {string} originalColumnName
   * @param {string} errType
   * @returns {String}
   */
  generateErrId(originalColumnName: string, errType: string): string {
    return `${originalColumnName}_${errType}`;
  }

  /**
   * Adds error message to the message manager
   * @param {string} message  Short Message
   * @param {string} code
   * @param {string} additionalText
   * @param {string} description  Long Message
   * @param {boolean} updateErrorCount
   */
  addMessage(
    message: string,
    code: string,
    additionalText?: string,
    originalColName?: string,
    description?: string,
    updateErrorCount: boolean = true,
    errorType?: string
  ): void {
    this.removeMessage(code, updateErrorCount);
    this.MessageManager.addMessages(
      new Message({
        message,
        type: errorType || "Error",
        additionalText,
        code,
        description,
        processor: this.getView().getModel(),
        technicalDetails: originalColName,
      })
    );
    this.MessagePopOver.close();
    if (updateErrorCount) {
      this.setErrorCount();
    }
  }

  /**
   * Removes error message from the message manager
   * @param {string} errCode
   * @param {boolean} updateErrorCount
   */
  removeMessage(errCode: string, updateErrorCount: boolean = true): void {
    const msgObj = this.MessageManager.getMessageModel()
      .getData()
      .find((msg) => msg.code === errCode);
    if (msgObj) {
      this.MessageManager.removeMessages(msgObj);
    }

    if (updateErrorCount) {
      const errcnt = this.getErrMessageCount();
      this.setErrorCount();
      if (errcnt === 0) {
        this.MessagePopOver.close();
      }
    }
  }

  updateAdditionalText(originalColName: string, targetName: string) {
    this.MessageManager.getMessageModel()
      .getData()
      .forEach((msg) => {
        if (msg && msg.technicalDetails === originalColName) {
          this.MessageManager.removeMessages(msg);
          msg.setAdditionalText(targetName);
          this.MessageManager.addMessages(msg);
        }
      });
  }

  setErrorCount(): void {
    this.getView().getModel("UIModel").setProperty("/errCount", this.getErrMessageCount());
  }

  /**
   * @returns error count
   */
  getErrMessageCount(type?: sap.ui.core.MessageType): number {
    if (type) {
      return this.MessageManager.getMessageModel()
        .getData()
        .filter((t) => t.type === type).length;
    }
    return this.MessageManager.getMessageModel().getData().length;
  }

  /**
   *
   * @param {sap.ui.model.Model} modelData
   * @param {string} path
   * @returns false if comlumn is empty else true
   */
  isTragetColumnEmpty(modelData: sap.ui.model.Model, path: string, updateErrorCount: boolean = true): boolean {
    const row: ITransformationRow = modelData.getProperty(path);
    const id: string = this.generateErrId(row.originalColumnName, this.erroCodes.targetMandatory);
    if (!row.tName) {
      row.validationStates.isValidName = false;
      this.addMessage(
        this.localizeText("emptyTargetColumn"),
        id,
        row.name,
        row.originalColumnName,
        "",
        updateErrorCount
      );
      // modelData.setProperty(`${path}/validationStates/isValidName`, false);
      return true;
    } else {
      row.validationStates.isValidName = true;
      this.removeMessage(id, updateErrorCount);
      // modelData.setProperty(`${path}/validationStates/isValidName`, true);
      return false;
    }
  }

  isTragetBusinessNameEmpty(modelData: sap.ui.model.Model, path: string, updateErrorCount: boolean = true): boolean {
    const row: ITransformationRow = modelData.getProperty(path);
    const id: string = this.generateErrId(row.originalColumnName, this.erroCodes.targetBusinessNameMandatory);
    if (!row.tBusinessName) {
      row.validationStates.isValidBusinessName = false;
      this.addMessage(
        this.localizeText("emptyTargetColumnBusinessName"),
        id,
        row.tName,
        row.originalColumnName,
        "",
        updateErrorCount
      );
      // modelData.setProperty(`${path}/validationStates/isValidBusinessName`, false);
      this.setErrorCount();
      return true;
    } else {
      row.validationStates.isValidBusinessName = true;
      this.removeMessage(id, updateErrorCount);
      // modelData.setProperty(`${path}/validationStates/isValidBusinessName`, true);
      this.setErrorCount();
      return false;
    }
  }

  /**
   *
   * @param {sap.ui.model.Model} modelData
   * @param {string} name
   */
  isDuplicateTargetName(modelData: sap.ui.model.Model, updateErrorCount: boolean = true) {
    const uniqueNames: Set<string> = new Set();
    let tableModel: ITransformationRow[] = Object.create(modelData.getData().tableModel);
    tableModel.forEach((row, index) => {
      if (index < tableModel.length - this.reservedLength) {
        this.hasDuplicateName(
          row,
          uniqueNames,
          "tName",
          this.erroCodes.targetDuplicate,
          this.erroCodes.tergetInvalidName,
          "isValidName",
          "uniqueColumnName",
          "uniqueColumnNameDescription",
          updateErrorCount
        );
      }
    });
  }

  isDuplicateTargetBusinessName(modelData: sap.ui.model.Model, updateErrorCount: boolean = true) {
    const uniqueBusinessNames: Set<string> = new Set();
    let tableModel: ITransformationRow[] = Object.create(modelData.getData().tableModel);
    tableModel.forEach((row, index) => {
      if (index < tableModel.length - this.reservedLength) {
        this.hasDuplicateName(
          row,
          uniqueBusinessNames,
          "tBusinessName",
          this.erroCodes.targetBusinessNameDuplicate,
          this.erroCodes.tergetInvalidBusinessName,
          "isValidBusinessName",
          "uniqueColumnBusinessName",
          "uniqueColumnBusinessNameDesc",
          updateErrorCount
        );
      }
    });
  }

  hasDuplicateName(
    row: ITransformationRow,
    uniqueNames: Set<string>,
    field: "tName" | "tBusinessName",
    errorCodeDuplicate: string,
    errorCodeInvalid: string,
    validationStateField: "isValidName" | "isValidBusinessName",
    uniqueColumnTextKey: string,
    uniqueColumnDescriptionKey: string,
    updateErrorCount: boolean = true
  ): boolean {
    const fieldValue = row[field];
    if (fieldValue) {
      const id = this.generateErrId(row.originalColumnName, errorCodeDuplicate);
      if (uniqueNames.has(fieldValue)) {
        row.validationStates[validationStateField] = false;
        this.addMessage(
          this.localizeText(uniqueColumnTextKey),
          id,
          fieldValue,
          row.originalColumnName,
          this.localizeText(uniqueColumnDescriptionKey),
          updateErrorCount
        );
        return true;
      } else {
        uniqueNames.add(fieldValue);
        const errCode = this.generateErrId(row.originalColumnName, errorCodeInvalid);
        const msgObj = this.MessageManager.getMessageModel()
          .getData()
          .find((msg) => msg.code === errCode);
        if (!msgObj) {
          row.validationStates[validationStateField] = true;
        }
        this.removeMessage(id, updateErrorCount);
        return false;
      }
    }
  }

  /**
   * @param {sap.ui.base.Event} oEvent
   */
  validateTargetColumn(oEvent: sap.ui.base.Event): void {
    const oInput: sap.m.Input = oEvent.getSource() as sap.m.Input;
    const modelData = oInput.getModel("UIModel");
    const path: string = oEvent.getSource().getBindingContext("UIModel").getPath();
    const isTragetColumnEmpty: boolean = this.isTragetColumnEmpty(modelData, path, false);
    if (
      Utils.isConnectionTypeDWC(
        this.taskDetails.targetSystem.connectionId,
        this.taskDetails.targetSystem.connectionType
      )
    ) {
      const isTragetBusinessNameEmpty: boolean = this.isTragetBusinessNameEmpty(modelData, path, false);
      if (!isTragetBusinessNameEmpty) {
        this.isDuplicateTargetBusinessName(modelData, false);
      }
      const row: ITransformationRow = modelData.getProperty(path);
      const id: string = this.generateErrId(row.originalColumnName, this.erroCodes.tergetInvalidBusinessName);
      this.removeMessage(id);
    }

    if (!isTragetColumnEmpty) {
      this.isDuplicateTargetName(modelData, false);
      const row: ITransformationRow = modelData.getProperty(path);
      const id: string = this.generateErrId(row.originalColumnName, this.erroCodes.tergetInvalidName);
      this.removeMessage(id);
      if (Utils.isConnectionTypeGBQ(this.taskDetails.targetSystem.connectionType)) {
        this.validateGBQTargetName(modelData, path);
      }
      if (Utils.isConnectionTypeCONFLUENT(this.taskDetails.targetSystem.connectionType)) {
        this.validateConfluentTargetName(modelData, path);
      }
      if (Utils.isConnectionTypeSignavio(this.taskDetails.targetSystem.connectionType)) {
        this.validateSignavioTargetName(modelData, path);
      }
      if (Utils.isConnTypeMsOneLake(this.taskDetails.targetSystem.connectionType)) {
        this.validateMsOneLakeTargetName(modelData, path);
      }

      if (Utils.isConnTypeSFTP(this.taskDetails.targetSystem.connectionType)) {
        this.validateSFTPTargetName(modelData, path);
      }

      if (
        Utils.isConnectionTypeABAP(this.taskDetails.sourceSystem.connectionType) &&
        this.taskDetails.sourceObject.metadata?.["isSourceWithoutPK"]
      ) {
        this.validateReservedNameForDPIDColumn(modelData, path);
      }
    }
    this.setErrorCount();
  }

  validateSignavioTargetName(modelData: sap.ui.model.Model, path: string) {
    const row: ITransformationRow = modelData.getProperty(path);
    const id: string = this.generateErrId(row.originalColumnName, this.erroCodes.tergetInvalidName);
    this.removeMessage(id);
    if (
      [
        Constants.SIGNAVIO_CDC_COLUMNS.operationType,
        Constants.SIGNAVIO_CDC_COLUMNS.sequenceNumber,
        Constants.SIGNAVIO_CDC_COLUMNS.timestamp,
      ].includes(row.tName)
    ) {
      row.validationStates.isValidName = false;
      this.addMessage(
        this.localizeText("duplicateColumns"),
        id,
        row.tName,
        row.originalColumnName,
        this.localizeText("duplicateSignavioCDCColumnsDesc"),
        false
      );
    } else {
      this.removeMessage(id);
      const errCode = this.generateErrId(row.originalColumnName, this.erroCodes.targetDuplicate);
      const msgObj = this.MessageManager.getMessageModel()
        .getData()
        .find((msg) => msg.code === errCode);
      if (!msgObj) {
        row.validationStates.isValidName = true;
      }
    }
  }

  validateMsOneLakeTargetName(modelData: sap.ui.model.Model, path: string) {
    const row: ITransformationRow = modelData.getProperty(path);
    const id: string = this.generateErrId(row.originalColumnName, this.erroCodes.tergetInvalidName);
    this.removeMessage(id);
    if (
      [
        Constants.MSONELAKE_CDC_COLUMNS.operation_type,
        Constants.MSONELAKE_CDC_COLUMNS.sequence_number,
        Constants.MSONELAKE_CDC_COLUMNS.timestamp,
      ].includes(row.tName)
    ) {
      row.validationStates.isValidName = false;
      this.addMessage(
        this.localizeText("duplicateColumns"),
        id,
        row.tName,
        row.originalColumnName,
        this.localizeText("duplicateMsOneLakeCDCColumnsDesc"),
        false
      );
    } else {
      this.removeMessage(id);
      const errCode = this.generateErrId(row.originalColumnName, this.erroCodes.targetDuplicate);
      const msgObj = this.MessageManager.getMessageModel()
        .getData()
        .find((msg) => msg.code === errCode);
      if (!msgObj) {
        row.validationStates.isValidName = true;
      }
    }
  }

  validateSFTPTargetName(modelData: sap.ui.model.Model, path: string) {
    const row: ITransformationRow = modelData.getProperty(path);
    const id: string = this.generateErrId(row.originalColumnName, this.erroCodes.tergetInvalidName);
    this.removeMessage(id);
    if (
      [
        Constants.SFTP_CDC_COLUMNS.operation_type,
        Constants.SFTP_CDC_COLUMNS.sequence_number,
        Constants.SFTP_CDC_COLUMNS.timestamp,
      ].includes(row.tName)
    ) {
      row.validationStates.isValidName = false;
      this.addMessage(
        this.localizeText("duplicateColumns"),
        id,
        row.tName,
        row.originalColumnName,
        this.localizeText("duplicateSFTPCDCColumnsDesc"),
        false
      );
    } else {
      this.removeMessage(id);
      const errCode = this.generateErrId(row.originalColumnName, this.erroCodes.targetDuplicate);
      const msgObj = this.MessageManager.getMessageModel()
        .getData()
        .find((msg) => msg.code === errCode);
      if (!msgObj) {
        row.validationStates.isValidName = true;
      }
    }
  }

  validateConfluentTargetName(modelData: sap.ui.model.Model, path: string) {
    const row: ITransformationRow = modelData.getProperty(path);
    let id: string = this.generateErrId(row.originalColumnName, this.erroCodes.tergetInvalidName);
    this.removeMessage(id);
    if (
      [
        Constants.CONFLUENT_CDC_COLUMNS.operation_type,
        Constants.CONFLUENT_CDC_COLUMNS.sequence_number,
        Constants.CONFLUENT_CDC_COLUMNS.timestamp,
      ].includes(row.tName)
    ) {
      row.validationStates.isValidName = false;
      this.addMessage(
        this.localizeText("duplicateColumns"),
        id,
        row.tName,
        row.originalColumnName,
        this.localizeText("duplicateConfluentCDCColumnsDesc"),
        false
      );
    } else {
      this.removeMessage(id);
      const errCode = this.generateErrId(row.originalColumnName, this.erroCodes.targetDuplicate);
      const msgObj = this.MessageManager.getMessageModel()
        .getData()
        .find((msg) => msg.code === errCode);
      if (!msgObj) {
        row.validationStates.isValidName = true;
      }
    }
  }
  /**
   * Validates GBQ target names for cdc cols ,constants and max length
   * @param modelData
   * @param path
   */
  validateGBQTargetName(modelData: sap.ui.model.Model, path: string) {
    const row: ITransformationRow = modelData.getProperty(path);
    let id: string = this.generateErrId(row.originalColumnName, this.erroCodes.tergetInvalidName);
    this.removeMessage(id);
    const nameStartPattern = new RegExp(
      `^(${Constants.GBQ_RESERVED_NAMES._COLIDENTIFIER}|${Constants.GBQ_RESERVED_NAMES._FILE_}|${Constants.GBQ_RESERVED_NAMES._PARTITION}|${Constants.GBQ_RESERVED_NAMES._ROW_TIMESTAMP}|${Constants.GBQ_RESERVED_NAMES._TABLE_}|${Constants.GBQ_RESERVED_NAMES.__ROOT__})`,
      "gi"
    );
    if (nameStartPattern.exec(row.tName)) {
      row.validationStates.isValidName = false;
      this.addMessage(
        this.localizeText("duplicateColumns"),
        id,
        row.tName,
        row.originalColumnName,
        this.localizeText("GBQTargetNameWithPrefixUpdated1", ["<br>", "<ul>", "<li>", "</li>", "</ul>"]),
        false
      );
    } else if (
      [
        Constants.GBQ_CDC_COLUMNS.isDeleted,
        Constants.GBQ_CDC_COLUMNS.operationFlag,
        Constants.GBQ_CDC_COLUMNS.recordStamp,
      ].includes(row.tName)
    ) {
      row.validationStates.isValidName = false;
      this.addMessage(
        this.localizeText("duplicateColumns"),
        id,
        row.tName,
        row.originalColumnName,
        this.localizeText("duplicateGBQCDCColumnsDesc"),
        false
      );
    } else {
      this.removeMessage(id);
      const errCode = this.generateErrId(row.originalColumnName, this.erroCodes.targetDuplicate);
      const msgObj = this.MessageManager.getMessageModel()
        .getData()
        .find((msg) => msg.code === errCode);
      if (!msgObj) {
        row.validationStates.isValidName = true;
      }
    }
    if (row.validationStates.isValidName) {
      id = this.generateErrId(row.originalColumnName, this.erroCodes.targetInvalidLength);
      if (row.validationStates.isValidName && row.tName.length > 300) {
        row.validationStates.isValidName = false;
        this.addMessage(
          this.localizeText("GBQtargetMaxLength"),
          id,
          row.tName,
          row.originalColumnName,
          this.localizeText("GBQtargetMaxLengthDesc"),
          false
        );
      } else {
        this.removeMessage(id);
        row.validationStates.isValidName = true;
      }
    }
  }
  /**
   * @param {sap.ui.model.Model} modelData
   * @param  {sap.ui.base.Event} oEvent
   * @returns True if source column has mapping else False
   */
  validateMappingColumn(modelData: sap.ui.model.Model, oEvent: sap.ui.base.Event): boolean {
    const path: string = oEvent.getSource().getBindingContext("UIModel").getPath();
    const uniqueMapping: Set<string> = new Set();
    let ismultipleSrcMapping = false;
    const isValidSourceMapping: boolean = !this.isSourceMappingEmpty(modelData, path);
    const rowTriggered: ITransformationRow = modelData.getProperty(path);
    const duplicateMappedCols = modelData
      .getData()
      .tableModel.filter((r) => r.srcMap === rowTriggered.srcMap && !r.isExpression);

    if (duplicateMappedCols.length > 1) {
      ismultipleSrcMapping = true;
    }
    modelData.getData().tableModel.forEach((row, index) => {
      this.isMultipleSourceColumnMapped(modelData, `/tableModel/${index}`, uniqueMapping);
    });

    this.setErrorCount();
    return isValidSourceMapping && !ismultipleSrcMapping;
  }

  /**
   *
   * @param {sap.ui.model.Model} modelData
   * @param {string} path
   * @returns True if source column has mapping else False
   */
  isSourceMappingEmpty(modelData: sap.ui.model.Model, path: string, updateErrorCount: boolean = true): boolean {
    const row: ITransformationRow = modelData.getProperty(path);
    const id: string = this.generateErrId(row.originalColumnName, this.erroCodes.mappingMandatory);
    // cdc columns no source mapping allowed
    if (!row.isCDCColumn && !row.isDPIDColumn && row.srcMap === "empty") {
      row.validationStates.isValidMapping = false;
      this.addMessage(
        this.localizeText("emptySource"),
        id,
        row.tName,
        row.originalColumnName,
        this.localizeText("emptySourceDescription"),
        updateErrorCount
      );
      // modelData.setProperty(`${path}/validationStates/isValidMapping`, false);
      return true;
    } else {
      row.validationStates.isValidMapping = true;
      this.removeMessage(id, updateErrorCount);
      // modelData.setProperty(`${path}/validationStates/isValidMapping`, true);
      return false;
    }
  }

  /**
   *
   * @param {string} templateType
   * @returns
   */
  isNumberType(templateType: string): boolean {
    const datatype: string = this.getDatatype(templateType);
    if (datatype) {
      return ["INTEGER", "DECIMAL", "FLOATING"].includes(datatype);
    }
    return false;
  }

  /**
   *
   * @param modelData
   * @param path
   * @param updateErrorCount if true updates the error message with set property , pass true only if all the model object reference to be reflected in ui
   * @param isAutoMap pass true only if method is being called to perform automap operation
   * @returns True if valid expression else False
   */
  isExpressionValid(
    modelData: sap.ui.model.Model,
    path: string,
    updateErrorCount: boolean = true,
    isAutoMap: boolean = false
  ): boolean {
    const row: ITransformationRow = modelData.getProperty(`${path}`);
    const id: string = this.generateErrId(row.originalColumnName, this.erroCodes.expression);
    if (!isAutoMap) {
      // on automap expression will be by default empty
      if (!row.expression) {
        row.validationStates.isValidExpression = false;
        // modelData.setProperty(`${path}/validationStates/isValidExpression`, false);
        this.addMessage(
          this.localizeText("emptyExpression"),
          id,
          row.tName,
          row.originalColumnName,
          this.localizeText("emptyExpressionDescription1", ["<b>", "</b>", "<br>"]),
          updateErrorCount
        );
        return false;
      } else if (this.isNumberType(row.dataType)) {
        const quotesRegex = NUMBER_REGEX; // check it is a number
        if (!quotesRegex.test(row.expression)) {
          row.validationStates.isValidExpression = false;
          // modelData.setProperty(`${path}/validationStates/isValidExpression`, false);
          this.addMessage(
            this.localizeText("numberExpressionErr"),
            id,
            row.tName,
            row.originalColumnName,
            this.localizeText("numberExpressionErrDescription"),
            updateErrorCount
          );
          return false;
        }
      }
    }
    row.validationStates.isValidExpression = true;
    // modelData.setProperty(`${path}/validationStates/isValidExpression`, true);
    this.removeMessage(id, updateErrorCount);
    return true;
  }

  /**
   *
   * @param modelData
   * @param path
   * @returns True if length field has valid value else False
   */
  isValidLength(
    modelData: sap.ui.model.Model,
    path: string,
    isValidDataType: boolean = true,
    updateErrorCount: boolean = true
  ): boolean {
    const row: ITransformationRow = modelData.getProperty(path);
    const id: string = this.generateErrId(row.originalColumnName, this.erroCodes.length);
    if (Utils.showLength(row.dataType) && isValidDataType) {
      const num = parseInt(row.length, 10);
      if (isNaN(num) || (row.length && (num <= 0 || num > 5000))) {
        row.validationStates.isValidLength = false;
        // modelData.setProperty(`${path}/validationStates/isValidLength`, false);
        this.addMessage(
          this.localizeText("invalidLength"),
          id,
          row.tName,
          row.originalColumnName,
          this.localizeText("invalidLengthDescription"),
          updateErrorCount
        );
        return false;
      }
      if (!row.isExpression) {
        const source = this.sourceAttr.find((col) => col.name === row.srcMap);
        if (source?.length && row.length < source.length) {
          row.validationStates.isValidLength = false;
          // modelData.setProperty(`${path}/validationStates/isValidLength`, false);
          this.addMessage(
            this.localizeText("invalidMappedLength"),
            id,
            row.tName,
            row.originalColumnName,
            this.localizeText("invalidMappedLengthDescription", [source.length]),
            updateErrorCount
          );
          return false;
        }
      }
    }
    row.validationStates.isValidLength = true;
    // modelData.setProperty(`${path}/validationStates/isValidLength`, true);
    this.removeMessage(id, updateErrorCount);
    return true;
  }

  /**
   *
   * @param modelData
   * @param path
   * @returns True if precision field has valid value else False
   */
  isValidatePrecision(
    modelData: sap.ui.model.Model,
    path: string,
    isValidDataType: boolean = true,
    updateErrorCount: boolean = true
  ): boolean {
    const row: ITransformationRow = modelData.getProperty(path);
    const id: string = this.generateErrId(row.originalColumnName, this.erroCodes.precision);
    if (Utils.showPrecision(row.dataType) && isValidDataType) {
      const num: number = parseInt(row.precision, 10);
      if (isNaN(num) || (row.precision && num <= 0)) {
        row.validationStates.isValidPrecision = false;
        // modelData.setProperty(`${path}/validationStates/isValidPrecision`, false);
        this.addMessage(
          this.localizeText("invalidPrecision"),
          id,
          row.tName,
          row.originalColumnName,
          this.localizeText("invalidPrecisionDescription", ["<br>", "•"]),
          updateErrorCount
        );
        return false;
      }
      if (!row.isExpression) {
        const source = this.sourceAttr.find((col) => col.name === row.srcMap);
        if (source?.precision && row.precision < source.precision) {
          row.validationStates.isValidPrecision = false;
          // modelData.setProperty(`${path}/validationStates/isValidPrecision`, false);
          this.addMessage(
            this.localizeText("invalidMappedPrecision"),
            id,
            row.tName,
            row.originalColumnName,
            this.localizeText("invalidMappedPrecisionDescription1", ["<br>", "•", source.precision]),
            updateErrorCount
          );
          return false;
        }
      }
    }
    row.validationStates.isValidPrecision = true;
    // modelData.setProperty(`${path}/validationStates/isValidPrecision`, true);
    this.removeMessage(id, updateErrorCount);
    return true;
  }
  /**
   * Validates the scale precision of converted datatypes based on  target connection type
   * @param row
   */
  validateConvertedDatatype(row: ITransformationRow): void {
    let id: string = this.generateErrId(row.originalColumnName, this.erroCodes.dataType);
    const precision = Number(row.precision),
      scale = Number(row.scale);
    const source: sap.cdw.replicationflow.Attribute = this.sourceAttr.find((col) => col.name === row.srcMap);
    if (source) {
      const isDecimal = row.dataType === "decimal";
      const isDecfloat = ["decfloat16", "decfloat34"].includes(source.datatype);
      const isSourceDatatypeUint64 = source.datatype === "uint64";
      // Decfloat validations
      if (isDecimal && isDecfloat) {
        const paramsBasedOnTarget = ValidationUtils.getConvertedDecFloatValidationParams(
          scale,
          precision,
          this.taskDetails.targetSystem.connectionId,
          this.taskDetails.targetSystem.connectionType
        );
        const validScale = paramsBasedOnTarget.validScale;
        const validPrecision = paramsBasedOnTarget.validPrecision;
        const invalidPrecisionOrScaleDescText = paramsBasedOnTarget.descriptionText;
        if (validScale && validPrecision) {
          row.validationStates.isValidScale = true;
          row.validationStates.isValidPrecision = true;
          row.validationStates.isValidDataType = true;
          this.removeMessage(id);
        } else {
          row.validationStates.isValidScale = false;
          row.validationStates.isValidPrecision = false;
          row.validationStates.isValidDataType = true;
          this.addMessage(
            this.localizeText("InvalidPrecisionORScale"),
            id,
            row.tName,
            row.originalColumnName,
            invalidPrecisionOrScaleDescText,
            true
          );
        }
      }
      // uint64 validations
      id = this.generateErrId(row.originalColumnName, this.erroCodes.dataType);
      if (isDecimal && isSourceDatatypeUint64) {
        const validationParams = this.getConvertedUint64ValidationParams();
        const nonCompatibleDataTypeUnit64DescText = validationParams.descriptionText;

        if (20 <= precision - scale) {
          row.validationStates.isValidScale = true;
          row.validationStates.isValidPrecision = true;
          row.validationStates.isValidDataType = true;
          this.removeMessage(id);
        } else {
          row.validationStates.isValidScale = false;
          row.validationStates.isValidPrecision = false;
          row.validationStates.isValidDataType = true;
          this.addMessage(
            this.localizeText("InvalidPrecisionORScale"),
            id,
            row.tName,
            row.originalColumnName,
            nonCompatibleDataTypeUnit64DescText,
            true
          );
        }
      }
    }
  }
  /**
   * Returns the validation parameters for a converted uint64 datatype based on target connection type
   * @returns
   */
  private getConvertedUint64ValidationParams() {
    let descriptionText: string;
    if (Utils.isConnectionTypeGBQ(this.taskDetails.targetSystem.connectionType)) {
      descriptionText = this.localizeText("GBQNonCompatibleDataTypeUnit64Updated1", ["<br>"]);
    }
    if (
      Utils.isConnectionTypeDwcLTF(
        this.taskDetails.targetSystem.connectionId,
        this.taskDetails.targetSystem.connectionType
      )
    ) {
      descriptionText = this.localizeText("DWCLTFNonCompatibleDataTypeUnit64Description1", ["<br>"]);
    }

    return { descriptionText };
  }

  /**
   *
   * @param modelData
   * @param path
   * @returns True if scale field has valid value else False
   */
  isValidScale(
    modelData: sap.ui.model.Model,
    path: string,
    isValidDataType: boolean = true,
    updateErrorCount: boolean = true
  ): boolean {
    const row: ITransformationRow = modelData.getProperty(path);
    const id: string = this.generateErrId(row.originalColumnName, this.erroCodes.scale);
    if (Utils.showPrecision(row.dataType) && isValidDataType) {
      const scale: number = parseInt(row.scale, 10);
      const precision: number = parseInt(row.precision, 10);
      if (isNaN(scale) || (scale && scale < 0) || (!isNaN(precision) && precision < scale)) {
        row.validationStates.isValidScale = false;
        // modelData.setProperty(`${path}/validationStates/isValidScale`, false);
        this.addMessage(
          this.localizeText("invalidScale"),
          id,
          row.tName,
          row.originalColumnName,
          this.localizeText("invalidScaleDescription", ["<br>", "•"]),
          updateErrorCount
        );
        return false;
      }
      if (!row.isExpression) {
        const source: sap.cdw.replicationflow.Attribute = this.sourceAttr.find((col) => col.name === row.srcMap);
        if (source?.scale && row.scale < source.scale) {
          row.validationStates.isValidScale = false;
          // modelData.setProperty(`${path}/validationStates/isValidScale`, false);
          this.addMessage(
            this.localizeText("invalidMappedScale"),
            id,
            row.tName,
            row.originalColumnName,
            this.localizeText("invalidMappedScaleDescription1", ["<br>", "•", source.scale]),
            updateErrorCount
          );
          return false;
        }
      }
    }
    row.validationStates.isValidScale = true;
    // modelData.setProperty(`${path}/validationStates/isValidScale`, true);
    this.removeMessage(id, updateErrorCount);
    return true;
  }

  /**
   *
   * @param modelData
   * @param path
   * @returns True if scale and precision field has valid value else False
   */
  isValidTargetScalePrecision(
    modelData: sap.ui.model.Model,
    path: string,
    isValidDataType: boolean = true,
    updateErrorCount: boolean = true
  ): boolean {
    const row: ITransformationRow = modelData.getProperty(path);
    const id: string = this.generateErrId(row.originalColumnName, this.erroCodes.scaleAndPrecision);
    if (Utils.showPrecision(row.dataType) && isValidDataType) {
      const scale: number = parseInt(row.scale, 10);
      const precision: number = parseInt(row.precision, 10);
      const source: sap.cdw.replicationflow.Attribute = this.sourceAttr.find((col) => col.name === row.srcMap);
      // Some scenario source may not contain precision or scale(ex GBQ, scr -> decfloat ,target->decimal)
      if (
        !row.isExpression &&
        source &&
        !isNaN(scale) &&
        !isNaN(source.scale) &&
        !isNaN(precision) &&
        !isNaN(source.precision)
      ) {
        if (scale < source.scale || precision - scale < source.precision - source.scale) {
          row.validationStates.isValidScale = false;
          row.validationStates.isValidPrecision = false;
          this.addMessage(
            this.localizeText("invalidMappedScalePrecisionShortText"),
            id,
            row.tName,
            row.originalColumnName,
            this.localizeText("invalidMappedScalePrecision"),
            updateErrorCount
          );
          return false;
        } else {
          row.validationStates.isValidScale = true;
          row.validationStates.isValidPrecision = true;
          this.removeMessage(id, updateErrorCount);
        }
      }
      if (row.isExpression) {
        row.validationStates.isValidScale = true;
        row.validationStates.isValidPrecision = true;
        this.removeMessage(id, updateErrorCount);
      }
    }
    return true;
  }

  /**
   *
   * @param modelData
   * @param path
   * @returns True if Target and selected Source Data type are compatible esle False
   */
  isValidDataType(modelData: sap.ui.model.Model, path: string, updateErrorCount: boolean = true): boolean {
    const row: ITransformationRow = modelData.getProperty(path);
    const id: string = this.generateErrId(row.originalColumnName, this.erroCodes.dataType);
    if (!row.isExpression) {
      const source: sap.cdw.replicationflow.Attribute = this.sourceAttr.find((col) => col.name === row.srcMap);
      if (source) {
        if (!ValidationUtils.isCompatibleType(row.dataType, source.datatype, row.precision, row.scale)) {
          this.addMessage(
            this.localizeText("nonCompatibleDataType"),
            id,
            row.tName,
            row.originalColumnName,
            this.localizeText("nonCompatibleDataTypeDescription1", [source.datatype, "<br>"]),
            updateErrorCount
          );
          row.validationStates.isValidDataType = false;
          // modelData.setProperty(`${path}/validationStates/isValidDataType`, false);
          return false;
        } else {
          row.validationStates.isValidDataType = true;
          // modelData.setProperty(`${path}/validationStates/isValidDataType`, true);
          this.removeMessage(id, updateErrorCount);
        }
      }
    }
    return true;
  }

  isMultipleSourceColumnMapped(
    modelData: sap.ui.model.Model,
    path: string,
    uniqueNames: Set<string>,
    updateErrorCount: boolean = true
  ) {
    const modelDataObj: ITransformationModel = modelData.getData();
    const row: ITransformationRow = modelData.getProperty(path);
    const id = this.generateErrId(row.originalColumnName, this.erroCodes.duplicateSourceMapping);
    if (row.srcMap && row.srcMap !== "empty" && row.srcMap !== Constants.SKIP_MAPPING) {
      if (uniqueNames.has(row.srcMap) && !row.isExpression) {
        const duplicateMappedCols = modelDataObj.tableModel.filter((r) => r.srcMap === row.srcMap && !r.isExpression);
        row.validationStates.isValidMapping = false;
        this.addMessage(
          this.localizeText("uniqueSourceMapping"),
          id,
          row.tName,
          row.originalColumnName,
          this.localizeText("uniqueSourceMappingDesc", [
            `"${row.srcMap}"`,
            "<br>",
            duplicateMappedCols.map((c) => c.name).join("<br>"),
          ]),
          updateErrorCount
        );
        const autoMappedMsgObj = this.MessageManager.getMessageModel();
        duplicateMappedCols.forEach((col) => {
          const autoMappedObjErrId = this.generateErrId(col.originalColumnName, this.erroCodes.duplicateSourceMapping);
          const matchedErrObj = autoMappedMsgObj.getData().find((msg) => msg.code === autoMappedObjErrId);
          if (!matchedErrObj) {
            this.addMessage(
              this.localizeText("uniqueSourceMapping"),
              autoMappedObjErrId,
              col.tName,
              col.originalColumnName,
              this.localizeText("uniqueSourceMappingDesc", [
                `"${col.srcMap}"`,
                "<br>",
                duplicateMappedCols.map((c) => c.name).join("<br>"),
              ]),
              updateErrorCount
            );
            col.validationStates.isValidMapping = false;
          }
        });
        return true;
      } else {
        if (!row.isExpression) {
          uniqueNames.add(row.srcMap);
        }
        const errCode = this.generateErrId(row.originalColumnName, this.erroCodes.mappingMandatory);
        const msgObj = this.MessageManager.getMessageModel()
          .getData()
          .find((msg) => msg.code === errCode);
        if (!msgObj) {
          row.validationStates.isValidMapping = true;
        }
        this.removeMessage(id, updateErrorCount);
        return false;
      }
    }
    // Remove error if reset to empty source mapping / Skip Mapping
    if (row.srcMap === "empty" || row.srcMap === Constants.SKIP_MAPPING) {
      this.removeMessage(id, updateErrorCount);
    }
  }

  /**
   *
   * @param modelData
   * @param path
   */
  validateRow(
    modelData: sap.ui.model.Model,
    path: string,
    uniqueNames: Set<string>,
    uniqueMapping: Set<string>,
    updateErrorCount: boolean = true
  ): void {
    const row: ITransformationRow = modelData.getProperty(path);
    if (!this.isTragetColumnEmpty(modelData, path, updateErrorCount)) {
      this.hasDuplicateName(
        row,
        uniqueNames,
        "tName",
        this.erroCodes.targetDuplicate,
        this.erroCodes.tergetInvalidName,
        "isValidName",
        "uniqueColumnName",
        "uniqueColumnNameDescription",
        updateErrorCount
      );
      if (
        this.isNewTarget &&
        !Utils.isConnTypeObjectStore(this.taskDetails.targetSystem.connectionType) &&
        !Utils.isConnectionTypeSignavio(this.taskDetails.targetSystem.connectionType)
      ) {
        // when target connection is object store and signavio validation for special char is relaxed
        this.validateNameChar(modelData, path, updateErrorCount);
      }
    }
    if (row.isExpression) {
      this.isExpressionValid(modelData, path, updateErrorCount);
    } else {
      const validDataType = this.isValidDataType(modelData, path, updateErrorCount);
      if (!this.isSourceMappingEmpty(modelData, path, updateErrorCount)) {
        this.isMultipleSourceColumnMapped(modelData, path, uniqueMapping, updateErrorCount);
      }
      this.isValidLength(modelData, path, validDataType, updateErrorCount);
      this.isValidatePrecision(modelData, path, validDataType, updateErrorCount);
      this.isValidScale(modelData, path, validDataType, updateErrorCount);
      this.validateConvertedDatatype(row);
      this.isValidTargetScalePrecision(modelData, path, validDataType, updateErrorCount);
    }
  }

  /**
   * Validates the complete TS table
   */
  validateTable(): void {
    const modelData = this.getView().getModel("UIModel");
    const tableData: ITransformationRow[] = modelData.getData().tableModel;
    this.validateColumnCount(modelData, false);
    const uniqueNames: Set<string> = new Set();
    const uniqueMapping: Set<string> = new Set();
    tableData.forEach((row, index) => {
      this.validateRow(modelData, `/tableModel/${index}`, uniqueNames, uniqueMapping, false);
    });
  }

  /**
   *
   * @param modelData
   * @returns True if TS table has at leaste one row else False
   */
  validateColumnCount(modelData: sap.ui.model.Model, updateErrorCount: boolean = true): boolean {
    const tableData: ITransformationRow[] = modelData.getData().tableModel;
    if (!tableData.length) {
      this.addMessage(
        this.localizeText("invalidColumnCount"),
        this.erroCodes.colCount,
        "",
        "",
        this.localizeText("invalidColumnCountDescription"),
        updateErrorCount
      );
      return false;
    }
    this.removeMessage(this.erroCodes.colCount, updateErrorCount);
    return true;
  }

  private validRowFormatter(
    isValidDataType,
    isValidName,
    isValidLength,
    isValidMapping,
    isValidPrecision,
    isValidScale,
    isValidExpression,
    isNewColumn,
    isAutoDatatype,
    isAutoRenamed,
    isDifferentKeyInSourceAndTarget,
    isValidBusinessName
  ) {
    if (
      isValidDataType &&
      isValidName &&
      isValidLength &&
      isValidMapping &&
      isValidPrecision &&
      isValidScale &&
      isValidExpression &&
      isValidBusinessName
    ) {
      if (isDifferentKeyInSourceAndTarget) {
        return "Warning";
      }
      if (isNewColumn || isAutoDatatype || isAutoRenamed) {
        return "Information";
      }
      return "None";
    }
    return "Error";
  }
  /**
   * Matches the target column with source column(name based mapping)
   */
  private onAutoMap() {
    const oUiModel: sap.ui.model.Model = this.getView().getModel("UIModel");
    const oModelData = oUiModel.getData();
    this.changedMappingList = [];
    this.isAttrChanged = true;
    this.isMappingChanged = true;
    this.attrMappings = null;
    const uniqueMapping: Set<string> = new Set();
    oModelData.tableModel.forEach((row, index) => {
      // const srcMap = row.sourceList.find(l => l.key === row.tName) ? row.tName : 'empty';
      // oUiModel.setProperty(`/tableModel/${index}/srcMap`, srcMap);
      // oUiModel.setProperty(`/tableModel/${index}/isExpression`, false);
      // oUiModel.setProperty(`/tableModel/${index}/expression`, '');
      // const validDataType = this.isValidDataType(oUiModel, `/tableModel/${index}`);
      // this.isSourceMappingEmpty(oUiModel, `/tableModel/${index}`);
      // this.isValidLength(oUiModel, `/tableModel/${index}`, validDataType);
      // this.isValidatePrecision(oUiModel, `/tableModel/${index}`, validDataType);
      // this.isValidScale(oUiModel, `/tableModel/${index}`, validDataType);

      // automap not supported for cdc columns && DPID Column
      if (!row.isCDCColumn && !row.isDPIDColumn) {
        const srcMap = this.getAutoMappedSourceName(row.sourceList, row.tName);
        row.srcMap = srcMap;
        row.isExpression = false;
        row.expression = "";
        const validDataType = this.isValidDataType(oUiModel, `/tableModel/${index}`, false);
        if (!this.isSourceMappingEmpty(oUiModel, `/tableModel/${index}`, false)) {
          this.isMultipleSourceColumnMapped(oUiModel, `/tableModel/${index}`, uniqueMapping, false);
        }
        this.isExpressionValid(oUiModel, `/tableModel/${index}`, false, true);
        this.isValidLength(oUiModel, `/tableModel/${index}`, validDataType, false);
        this.isValidatePrecision(oUiModel, `/tableModel/${index}`, validDataType, false);
        this.isValidScale(oUiModel, `/tableModel/${index}`, validDataType, false);
        this.validateConvertedDatatype(row);
        if (srcMap !== row.tName) {
          this.changedMappingList[row.originalColumnName] = row;
        }
        this.isValidTargetScalePrecision(oUiModel, `/tableModel/${index}`, validDataType, false);
      }
    });
    this.setErrorCount();
  }

  private getAutoMappedSourceName(sourceList: any[], targetName: string) {
    const targetConnType = this.taskDetails.targetSystem.connectionType;
    const targetConnId = this.taskDetails.targetSystem.connectionId;
    if (Utils.isConnectionTypeGBQ(targetConnType)) {
      return this.getGBQAutomappedSourceName(sourceList, targetName);
    } else if (Utils.isConnectionTypeCONFLUENT(targetConnType)) {
      return this.getConfluentAutomappedSourceName(sourceList, targetName);
    } else if (Utils.isConnectionTypeDWC(targetConnId, targetConnType)) {
      return this.getDWCAutoMappedSourceName(sourceList, targetName);
    } else {
      let mappedSrc: any = sourceList.find((src) => src.key === targetName);

      if (!mappedSrc) {
        mappedSrc = sourceList.find((src) => src.key?.toUpperCase() === targetName.toUpperCase());
      }

      return mappedSrc?.key ?? "empty";
    }
  }

  private getGBQAutomappedSourceName(sourceList: any[], targetName: string): string {
    let mappedSrc = sourceList.find(
      (src) => src.key === targetName || RFBuilderUtils.convertGBQTargetColumnName(src.key) === targetName
    );

    if (!mappedSrc) {
      mappedSrc = sourceList.find(
        (src) =>
          src.key?.toUpperCase() === targetName.toUpperCase() ||
          RFBuilderUtils.convertGBQTargetColumnName(src.key || "").toUpperCase() === targetName.toUpperCase()
      );
    }

    return mappedSrc ? mappedSrc.key : "empty";
  }

  private getConfluentAutomappedSourceName(sourceList: any[], targetName: string): string {
    let mappedSrc = sourceList.find(
      (src) => src.key === targetName || RFBuilderUtils.convertConfluentColumnName(src.key) === targetName
    );

    if (!mappedSrc) {
      mappedSrc = sourceList.find(
        (src) =>
          src.key.toUpperCase() === targetName.toUpperCase() ||
          RFBuilderUtils.convertConfluentColumnName(src.key).toUpperCase() === targetName.toUpperCase()
      );
    }
    return mappedSrc ? mappedSrc.key : "empty";
  }

  private getDWCAutoMappedSourceName(sourceList: any[], targetName: string): string {
    let mappedSrc: string;
    const mappedObjects = this.mappedSrcTargetForDwc;

    if (Utils.isExistingTargetWithInvalidTargetName(targetName, !this.isNewTarget)) {
      // Existing targets that already have invalid characters for which the target was created before auto rename
      let mappedSrcObj = sourceList.find((src) => src.key === targetName);
      if (!mappedSrcObj) {
        mappedSrcObj = sourceList.find((src) => src.key.toUpperCase() === targetName.toUpperCase());
      }
      mappedSrc = mappedSrcObj?.key ?? "empty";
    } else {
      mappedSrc = Object.keys(mappedObjects).find((src) => mappedObjects[src] === targetName);
      if (!mappedSrc) {
        mappedSrc = Object.keys(mappedObjects).find(
          (src) => mappedObjects[src].toUpperCase() === targetName.toUpperCase()
        );
      }
    }

    return mappedSrc ?? "empty";
  }

  private getMappingsofConvertedDWCNames(sourceList) {
    const mappingObj = {};
    const uniqueRenamedNames: string[] = [];
    const validNames: string[] = [];
    const sourceListNames = sourceList.map((attr) => attr.name);
    sourceList.forEach((attr) => {
      let finalUniqueName = attr.name; // Valid columns should have attribute name as final name
      let tempRenamedName = RFBuilderUtils.getValidTargetColumnName(finalUniqueName);
      // Column name is changed
      let suffixCounter = 0;
      if (tempRenamedName !== finalUniqueName) {
        const tempNameWithoutSuffix = tempRenamedName;
        // Changed column name should not be same as any other column name
        // Duplicates can occur if names have invalid chars in same pos
        // e.g : 'column+name' and 'column-name'
        // Duplicates can occur if after removing invalid chars, resulting name is a name which is already present in source
        // e.g : 'column+name' which will be converted to column_name and if column_name is already present in source
        while (validNames.includes(tempRenamedName) || sourceListNames.includes(tempRenamedName)) {
          tempRenamedName = `${tempNameWithoutSuffix}_${suffixCounter}`;
          suffixCounter += 1;
        }
        finalUniqueName = tempRenamedName;
      }
      validNames.push(finalUniqueName);
      mappingObj[attr.name] = finalUniqueName;
      uniqueRenamedNames.push(finalUniqueName);
    });
    return mappingObj;
  }

  /**
   *
   * @param direction direction of row movement(up or down)
   */
  private onMoveRows(direction: string) {
    const oUiModel: sap.ui.model.Model = this.getView().getModel("UIModel");
    const oModelData = oUiModel.getData();
    const transformationTable: sap.ui.table.Table = this.getView().byId("transformationTable") as sap.ui.table.Table;
    let selectedRows: number[] = transformationTable.getSelectedIndices();
    if (direction === "down") {
      selectedRows = selectedRows.sort((a, b) => b - a);
    }
    selectedRows.forEach((sindex, i) => {
      let deletingIndex, addingIndex;
      if (direction === "up") {
        deletingIndex = sindex - 1;
        addingIndex = sindex;
      } else if (direction === "down") {
        deletingIndex = sindex;
        addingIndex = sindex + 1;
      }
      const deleteditem = oModelData.tableModel.splice(deletingIndex, 1);
      oModelData.tableModel.splice(addingIndex, 0, deleteditem[0]);
      if (i === 0 && direction === "up") {
        transformationTable.setSelectedIndex(deletingIndex);
      } else if (i === 0 && direction === "down") {
        transformationTable.setSelectedIndex(addingIndex);
      } else if (direction === "up") {
        transformationTable.addSelectionInterval(deletingIndex, deletingIndex);
      } else if (direction === "down") {
        transformationTable.addSelectionInterval(addingIndex, addingIndex);
      }
    });
    this.getView().getModel("UIModel").refresh(true);
    this.isAttrChanged = true;
  }
  private onRowSelection(evt) {
    this.handleToolbarbuttons(this.getView().getModel("UIModel"));
  }

  /**
   *
   * @param oUIModel
   */
  private handleToolbarbuttons(oUIModel: sap.ui.model.Model) {
    const transformationTable = this.getView().byId("transformationTable") as sap.ui.table.Table;
    let isCDCColumnReorderAllowed = true;
    const tableData = oUIModel.getData().tableModel;
    const selectedIndices = transformationTable.getSelectedIndices();
    const bindingObj = transformationTable.getBinding("rows") as any;
    const allCurrentContext = bindingObj.getAllCurrentContexts();
    if (
      Utils.isConnectionTypeGBQ(this.taskDetails.targetSystem.connectionType) ||
      Utils.isConnectionTypeCONFLUENT(this.taskDetails.targetSystem.connectionType)
    ) {
      isCDCColumnReorderAllowed = !selectedIndices.some(
        (i) => oUIModel.getProperty(`${allCurrentContext[i].sPath}/isCDCColumn`) === true
      );
    }
    const isSearching: boolean = bindingObj.aFilters[0] && bindingObj.aFilters[0].length > 0;
    if (
      selectedIndices.length &&
      !selectedIndices.some(
        (i) =>
          oUIModel.getProperty(`${allCurrentContext[i].sPath}/isKey`) === true ||
          oUIModel.getProperty(`${allCurrentContext[i].sPath}/isCDCColumn`) === true ||
          oUIModel.getProperty(`${allCurrentContext[i].sPath}/isDPIDColumn`) === true
      )
    ) {
      oUIModel.setProperty("/toolBarButtons/isRemoveRowEnabled", true);
    } else {
      oUIModel.setProperty("/toolBarButtons/isRemoveRowEnabled", false);
    }
    if (
      selectedIndices.length &&
      selectedIndices[0] !== 0 &&
      !isSearching &&
      isCDCColumnReorderAllowed &&
      !selectedIndices.some((i) => oUIModel.getProperty(`${allCurrentContext[i].sPath}/isDPIDColumn`) === true)
    ) {
      oUIModel.setProperty("/toolBarButtons/isMoveUpEnabled", true);
    } else {
      oUIModel.setProperty("/toolBarButtons/isMoveUpEnabled", false);
    }
    if (
      selectedIndices.length &&
      selectedIndices[selectedIndices.length - 1] !== tableData.length - 1 - this.reservedLength &&
      !isSearching &&
      isCDCColumnReorderAllowed &&
      !selectedIndices.some((i) => oUIModel.getProperty(`${allCurrentContext[i].sPath}/isDPIDColumn`) === true)
    ) {
      oUIModel.setProperty("/toolBarButtons/isMoveDownEnabled", true);
    } else {
      oUIModel.setProperty("/toolBarButtons/isMoveDownEnabled", false);
    }
    if (!isSearching) {
      if (selectedIndices.some((i) => oUIModel.getProperty(`${allCurrentContext[i].sPath}/isDPIDColumn`) === true)) {
        oUIModel.setProperty("/toolBarButtons/isAutoMapEnabled", false);
      } else {
        oUIModel.setProperty("/toolBarButtons/isAutoMapEnabled", true);
      }
      oUIModel.setProperty("/toolBarButtons/isAddEnabled", true);
    } else {
      oUIModel.setProperty("/toolBarButtons/isAutoMapEnabled", false);
      oUIModel.setProperty("/toolBarButtons/isAddEnabled", false);
    }
  }

  enableExpressionFormatter(isExpression: boolean, dataType: string, privilege: boolean): boolean {
    if (privilege && isExpression && !this.isDateTimeType(dataType)) {
      return true;
    } else {
      return false;
    }
  }

  enableTargetDatatype(isEditable: boolean, isCDCColumn: boolean, isDPIDColumn: boolean): boolean {
    return isEditable === true && isCDCColumn === false && isDPIDColumn !== true ? true : false;
  }

  enableSourceDropdown(isExpression: boolean, privilege: boolean, isDPIDColumn: boolean): boolean {
    return isExpression === false && privilege === true && isDPIDColumn !== true ? true : false;
  }
  /**
   * Visibility of source mapping
   */
  showSourceDropdown(isCDCColumn: boolean, isDPIDColumn: boolean) {
    if (isCDCColumn || isDPIDColumn) {
      return false;
    }
    return true;
  }
  enableEditContent(enableExpression: boolean, privilege: boolean): boolean {
    return enableExpression && privilege ? true : false;
  }

  isDateTimeType(dataType: string): boolean {
    return [
      Constants.DATE_TIME_EXP_FUNCTIONS.DATE.dataType,
      Constants.DATE_TIME_EXP_FUNCTIONS.TIME.dataType,
      Constants.DATE_TIME_EXP_FUNCTIONS.TIMESTAMP.dataType,
    ].includes(dataType);
  }

  enableTargetColumnName(isEditable: boolean, isCDCColumn: boolean, isDPIDColumn: boolean) {
    return (
      isEditable &&
      !isDPIDColumn &&
      !(
        isCDCColumn &&
        (Utils.isConnectionTypeGBQ(this.taskDetails.targetSystem.connectionType) ||
          Utils.isConnTypeSFTP(this.taskDetails.targetSystem.connectionType) ||
          Utils.isConnectionTypeCONFLUENT(this.taskDetails.targetSystem.connectionType) ||
          Utils.isConnectionTypeSignavio(this.taskDetails.targetSystem.connectionType) ||
          Utils.isConnTypeMsOneLake(this.taskDetails.targetSystem.connectionType))
      )
    );
  }

  setTargetLength(isEditable) {
    return Utils.isConnectionTypeGBQ(this.taskDetails.targetSystem.connectionType) ? 300 : 64;
  }

  displayBusinessname(isEditable: boolean): boolean {
    return Utils.isConnectionTypeDWC(
      this.taskDetails.targetSystem.connectionId,
      this.taskDetails.targetSystem.connectionType
    )
      ? true
      : false;
  }

  showMessageForSchemaChange(): boolean {
    const otargetSystem = this.taskDetails.targetSystem;
    if (Utils.isConnectionTypeDWC(otargetSystem.connectionId, otargetSystem.connectionType)) {
      return !this.isNewTarget;
    } else {
      const objectStatusNumber = Number(this.objectStatus);
      if (
        objectStatusNumber === ObjectStatus.notDeployed ||
        objectStatusNumber === ObjectStatus.deployed ||
        objectStatusNumber === ObjectStatus.changesToDeploy
      ) {
        return true;
      } else {
        return false;
      }
    }
  }

  messageForSchemaChange(): string {
    let schemaChangeMessage: string = "";
    const otargetSystem = this.taskDetails.targetSystem;
    if (Utils.isConnectionTypeDWC(otargetSystem.connectionId, otargetSystem.connectionType)) {
      schemaChangeMessage = this.localizeText("messageForExistingDWCTargetModification");
    } else {
      const objectStatusNumber = Number(this.objectStatus);
      if (objectStatusNumber === ObjectStatus.notDeployed) {
        schemaChangeMessage = this.localizeText("messageForNewColumnBeforeDeployment");
      }

      if (objectStatusNumber === ObjectStatus.deployed || objectStatusNumber === ObjectStatus.changesToDeploy) {
        schemaChangeMessage = this.localizeText("messageForNewColumnAfterDeployment");
      }
    }
    return schemaChangeMessage;
  }

  enableEditConstantForSupportedDatatype(
    isPrimaryKey: boolean,
    isCDCColumn: boolean,
    dataType: string,
    isDPIDColumn: boolean
  ): boolean {
    if (
      isPrimaryKey ||
      isCDCColumn ||
      isDPIDColumn ||
      dataType === "bool" ||
      dataType === "binary" ||
      dataType === "decfloat16" ||
      dataType === "decfloat34"
    ) {
      return false;
    } else {
      return true;
    }
  }

  /**
   * Formatter for showing info button for invalid columns in Filter
   */
  showInfoButtonForInvalidColumnsInFilter() {
    if (Array.isArray(this.sourceInvalidColumns) && this.sourceInvalidColumns.length > 0) {
      return true;
    } else {
      return false;
    }
  }

  /**
   * Formatter for showing info button for invalid columns in Mapping
   */
  showInfoButtonForInvalidColumnsInMapping() {
    if (Array.isArray(this.targetInvalidColumns) && this.targetInvalidColumns.length > 0) {
      return true;
    } else {
      return false;
    }
  }

  /**
   * Checks number of primary key present in the table model
   * @param targetAttr
   * @returns number of PK
   */
  checkPrimaryKeyCount(targetAttr) {
    let keyColCount = 0;
    targetAttr.forEach((attr) => {
      if (attr.isKey) {
        keyColCount += 1;
      }
    });
    return { keyColCount };
  }

  /**
   * Formatter for visibility of Primary Key Checkbox
   * @returns boolean
   */
  showPrimaryKeyCheckbox(isCDCColumn: boolean, isNewTarget, isKey, isExpression, isDPIDColumn: boolean): boolean {
    //  Checkbox hidden for CDC Column and when function/constant is checked
    if (isCDCColumn || isExpression || isDPIDColumn) {
      return false;
    }
    //  For existing target checkbox is visible only for column with PK
    if (isNewTarget) {
      return true;
    } else {
      return isKey ? true : false;
    }
  }

  /**
   * For the value Of DisplayOnly property of the checkboxes for PK Column
   * @param isNewTarget
   * @param isKey
   * @returns
   */
  valueOfDisplayOnlyInPKCheckbox(isNewTarget, isKey): boolean {
    // Will be editable only for HDLF as source
    if (
      Utils.isConnectionTypeHdlfWithDeltaShare(
        this.taskDetails.sourceSystem.connectionType,
        this.taskDetails.sourceSystem.metadata["isSourceHDLFDeltashare"]
      )
    ) {
      if (isNewTarget) {
        return false;
      } else {
        return isKey ? true : false;
      }
    }
    //  For other connections as source , PK column will be display only ( non editable )
    return true;
  }

  /**
   * For the value Of valuestate property of the checkboxes for PK Column
   * @param isDifferentKeyInSourceAndTarget
   * @returns
   */
  valuestatePrimaryKeyCheckbox(isDifferentKeyInSourceAndTarget, isValidMapping): string {
    if (isDifferentKeyInSourceAndTarget && isValidMapping) {
      return "Warning";
    }
    return "None";
  }
  /**
   * Handler for primary key checkbox
   * Adds Primary key and validates it
   * @param oEvent
   */
  handlePrimaryKeyForHDLF(oEvent) {
    const modelData = this.getView().getModel("UIModel");
    const tableModel: ITransformationRow[] = modelData.getData().tableModel;
    const oSource = oEvent.getSource();
    const changedRowPath = oSource.getBindingContext("UIModel").getPath();
    const changedRow = oSource.getBindingContext("UIModel").getModel("tableModel").getObject(changedRowPath);
    const isCheckboxChecked = oEvent.getSource().getSelected();

    if (changedRow.srcMap !== "empty") {
      // Check if source has key and target has different configured key
      this.validateSameKeysInSourceAndTargetForHDLFDeltashare(modelData, true);
    }
    //  Validate at least 1 Primary key defined
    this.validatePrimaryKeyForHDLFDeltaShare(tableModel, true);
    //  Delete expression when primary key set , remove row not possible for Primary key
    if (isCheckboxChecked) {
      this.resetExpression(changedRow);
      modelData.setProperty("/toolBarButtons/isRemoveRowEnabled", false);
    } else {
      modelData.setProperty("/toolBarButtons/isRemoveRowEnabled", true);
    }

    this.addToChangeList(oEvent, ["attr"]);
  }

  private resetExpression(changedRow) {
    changedRow.isExpression = false;
    changedRow.expression = "";
  }

  /**
   * Checks if different keys are configured in target as compared to source for HDLF Deltashare as Source
   * Throws warning message and highlights corressponding checkbox and row
   * @param isCheckboxChecked
   * @param modelData
   * @param changedRow
   * @param changedRowPath
   * @param isSetErrcount
   */
  private validateSameKeysInSourceAndTargetForHDLFDeltashare(modelData, isSetErrcount: boolean) {
    const tableModel: ITransformationRow[] = modelData.getData().tableModel;
    let keyWarningFlag = false,
      usedSrcPk = 0;
    if (
      Utils.isConnectionTypeHdlfWithDeltaShare(
        this.taskDetails.sourceSystem.connectionType,
        this.taskDetails.sourceSystem.metadata["isSourceHDLFDeltashare"]
      ) &&
      this.sourcePKCols.length > 0
    ) {
      tableModel.forEach((col) => {
        if (col.validationStates.isValidMapping) {
          const temp = this.sourcePKCols.filter((keyCol) => keyCol === col.srcMap);
          if (temp.length) {
            usedSrcPk += 1;
            if (col.isKey === false) {
              col.validationStates.isDifferentKeyInSourceAndTarget = true;
              keyWarningFlag = true;
            } else {
              col.validationStates.isDifferentKeyInSourceAndTarget = false;
            }
          } else if (col.isKey) {
            col.validationStates.isDifferentKeyInSourceAndTarget = true;
            keyWarningFlag = true;
          } else {
            col.validationStates.isDifferentKeyInSourceAndTarget = false;
          }
        }
      });
      if (keyWarningFlag || usedSrcPk === 0) {
        const messageArray: string = this.sourcePKCols.map((col) => `<br> • ${col}`).join();
        this.addMessage(
          this.localizeText("HDLFSourceTargetDifferentKeysWarning"),
          Constants.TS_DIALOG_ERROR_CODES.primaryKeyWarning,
          "",
          "",
          this.localizeText("HDLFSourceTargetDifferentKeysWarningDescription1", [`<br>`, messageArray]),
          isSetErrcount,
          "Warning"
        );
      } else {
        this.removeMessage(Constants.TS_DIALOG_ERROR_CODES.primaryKeyWarning);
      }

      this.setErrorCount();
    }
  }

  /**
   * Visibility of porperties column ( Primary key and cdc column combined ) ( When HDLF FF is off)
   * @returns boolean
   */
  showPropertiesColumn(): boolean {
    return !this.showColumnsofPrimaryKeyAndCDCcolumns();
  }

  /**
   * Visibility of Primarky column and cdc column separately
   * @returns boolean
   */
  showColumnsofPrimaryKeyAndCDCcolumns(): boolean {
    return this.featureflags["DWCO_DS_REPLICATION_FLOW_SUPPORT_HDLF_SOURCE"] ? true : false;
  }

  /**
   * Checks if the key is different in source and target for the particular row
   * @param rowObj
   * @returns
   */
  private isDifferentKeyInSourceAndTarget(rowObj: ITransformationRow): boolean {
    if (
      Utils.isConnectionTypeHdlfWithDeltaShare(
        this.taskDetails.sourceSystem.connectionType,
        this.taskDetails.sourceSystem.metadata["isSourceHDLFDeltashare"]
      ) &&
      this.sourcePKCols.length > 0 &&
      rowObj.srcMap !== "empty"
    ) {
      const isKeyPresentInSource = this.sourcePKCols.includes(rowObj.srcMap);
      const isKeyPresentInTarget = rowObj.isKey;
      if ((isKeyPresentInSource && isKeyPresentInTarget) || (!isKeyPresentInSource && !isKeyPresentInTarget)) {
        return false;
      }
      return true;
    }
    return false; // For other connections false
  }

  /**
   * Gets the length of the reserved columns which cannot be altered
   * @returns number
   */
  private getReservedLength(): number {
    let reservedLength = 0;
    // GBQ has 3 cdc cols, reserving the length for row movements
    if (
      Utils.isConnectionTypeGBQ(this.taskDetails.targetSystem.connectionType) ||
      Utils.isConnectionTypeCONFLUENT(this.taskDetails.targetSystem.connectionType)
    ) {
      reservedLength += 3;
    }

    // ABAP without keys, DPID Column is 1
    if (
      Utils.isConnectionTypeABAP(this.taskDetails.sourceSystem.connectionType) &&
      this.taskDetails.sourceObject.metadata?.["isSourceWithoutPK"] &&
      !Utils.isConnectionTypeKafka(this.taskDetails.targetSystem.connectionType)
    ) {
      reservedLength += 1;
    }
    return reservedLength;
  }

  private validateReservedNameForDPIDColumn(modelData: sap.ui.model.Model, path: string) {
    const row: ITransformationRow = modelData.getProperty(path);
    let id: string = this.generateErrId(row.originalColumnName, this.erroCodes.tergetInvalidName);
    this.removeMessage(id);
    if (
      [
        Constants.ABAP_DPID_COLUMN.COL_NAME_FOR_HANA_TARGETS,
        Constants.ABAP_DPID_COLUMN.COL_NAME_FOR_NON_ABAP_TARGETS,
      ].includes(row.tName)
    ) {
      row.validationStates.isValidName = false;
      this.addMessage(
        this.localizeText("duplicateDPIDColumns"),
        id,
        row.tName,
        row.originalColumnName,
        this.localizeText("duplicateDPIDDColumnsDesc1"),
        false
      );
    } else {
      this.removeMessage(id);
      const errCode = this.generateErrId(row.originalColumnName, this.erroCodes.targetDuplicate);
      const msgObj = this.MessageManager.getMessageModel()
        .getData()
        .find((msg) => msg.code === errCode);
      if (!msgObj) {
        row.validationStates.isValidName = true;
      }
    }
  }
}
export const AddTransformationDialog = smartExtend(
  sap.ui.core.mvc.Controller,
  "sap.cdw.components.replicationflow.controller.AddTransformationDialog",
  AddTransformationDialogClass
);

sap.ui.define("sap/cdw/components/replicationflow/controller/AddTransformationDialog.controller", [], function () {
  return AddTransformationDialog;
});
