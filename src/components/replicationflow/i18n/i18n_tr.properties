#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Çoğaltma akışı

#XFLD: Edit Schema button text
editSchema=Şemayı düzenle

#XTIT : Properties heading
configSchema=Şemayı konfigüre et

#XFLD: save changed button text
applyChanges=Değişiklikleri uygula


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Kaynak bağlantı seçin
#XFLD
sourceContainernEmptyText=Konteyner seçin
#XFLD
targetConnectionEmptyText=Hedef bağlantı seçin
#XFLD
targetContainernEmptyText=Konteyner seçin
#XFLD
sourceSelectObjectText=Kaynak nesne seç
#XFLD
sourceObjectCount=Kaynak nesneler ({0})
#XFLD
targetObjectText=Hedef nesneler
#XFLD
confluentBrowseContext=Bağlam seçin
#XBUT
@retry=Yeniden dene
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Kiracı yükseltmesi devam ediyor.

#XTOL
browseSourceConnection=Kaynak bağlantıya göz atın
#XTOL
browseTargetConnection=Hedef bağlantıya göz atın
#XTOL
browseSourceContainer=Kaynak konteynere göz atın
#XTOL
browseAndAddSourceDataset=Kaynak nesne ekleyin
#XTOL
browseTargetContainer=Hedef konteynere göz atın
#XTOL
browseTargetSetting=Hedef ayarlarına göz atın
#XTOL
browseSourceSetting=Kaynak ayarlarına göz atın
#XTOL
sourceDatasetInfo=Bilgi
#XTOL
sourceDatasetRemove=Kaldır
#XTOL
mappingCount=Bu, ad tabanlı olmayan eşlemelerin/ifadelerin toplam sayısını gösterir.
#XTOL
filterCount=Bu, toplam filtre koşulu sayısını gösterir.
#XTOL
loading=Yükleniyor...
#XCOL
deltaCapture=Delta yakalaması
#XCOL
deltaCaptureTableName=Delta yakalama tablosu
#XCOL
loadType=Yükleme türü
#XCOL
deleteAllBeforeLoading=Yükleme öncesinde tümünü sil
#XCOL
transformationsTab=Projeksiyonlar
#XCOL
settingsTab=Ayarlar

#XBUT
renameTargetObjectBtn=Hedef nesneyi yeniden adlandır
#XBUT
mapToExistingTargetObjectBtn=Mevcut hedef nesneyle eşle
#XBUT
changeContainerPathBtn=Konteyner yolunu değiştir
#XBUT
viewSQLDDLUpdated=Tablo oluştur SQL deyimini görüntüle
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Kaynak nesne, delta yakalamasını desteklemiyor ancak seçilen hedef nesne, etkinleştirilen delta yakalaması seçeneğine sahip.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Kaynak nesne delta yakalamayı desteklememesine rağmen{0}delta yakalama etkin olduğundan hedef nesne kullanılamıyor.{1}Delta yakalamayı desteklemeyen başka bir hedef nesne seçebilirsiniz.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Bu ada sahip bir hedef nesne zaten mevcut. Bununla birlikte, kaynak nesne{0}delta yakalamayı desteklememesine rağmen delta yakalama{0}etkin olduğundan söz konusu hedef nesne kullanılamıyor.{1}Delta yakalamayı desteklemeyen mevcut bir hedef nesnenin adını veya{0}henüz mevcut olmayan bir adı girebilirsiniz.
#XBUT
copySQLDDLUpdated=Tablo oluştur SQL deyimini kopyala
#XMSG
targetObjExistingNoCDCColumnUpdated=Google BigQuery''deki mevcut tablolar, değişiklik verisi yakalama (CDC) için şu sütunları içermelidir:{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Birincil anahtara sahip olmadığından veya birincil anahtarı almak için koşulları sağlamayan bir bağlantı kullandığından şu kaynak nesneler desteklenmiyor:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Olası bir çözüm için bkz. SAP KBA 3531135.
#XLST: load type list values
initial=Yalnızca ilk
@emailUpdateError=E-posta bildirimi listesi güncellenirken hata oluştu

#XLST
initialDelta=İlk ve delta

#XLST
deltaOnly=Yalnızca Delta
#XMSG
confluentDeltaLoadTypeInfo=Confluent Kafka kaynağı için yalnızca İlk ve delta yükleme türü desteklenir.
#XMSG
confirmRemoveReplicationObject=Çoğaltmayı silmek istediğinizi teyit ediyor musunuz?
#XMSG
confirmRemoveReplicationTaskPrompt=Bu işlem, mevcut çoğaltmaların silinmesine neden olacak. Devam etmek istiyor musunuz?
#XMSG
confirmTargetConnectionChangePrompt=Bu işlem, hedef bağlantıyla hedef konteynerin sıfırlanmasına ve tüm hedef nesnelerin silinmesine neden olacak. Devam etmek istiyor musunuz?
#XMSG
confirmTargetContainerChangePrompt=Bu işlem, hedef konteynerin sıfırlanmasına ve mevcut tüm hedef nesnelerinin sıfırlanmasına neden olacak. Devam etmek istiyor musunuz?
#XMSG
confirmRemoveTransformObject={0} projeksiyonunu silmek istediğinizi teyit ediyor musunuz?
#XMSG
ErrorMsgContainerChange=Konteyner yolu değiştirilirken hata oluştu.
#XMSG
infoForUnsupportedDatasetNoKeys=Şu kaynak nesneler birincil anahtara sahip olmadığından desteklenmiyor:
#XMSG
infoForUnsupportedDatasetView=Görünümler türündeki şu kaynak nesneler desteklenmiyor:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Aşağıdaki kaynak nesne, girdi parametreleri içerdiğinden SQL görünümü olarak desteklenmiyor:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Şu kaynak nesneler için veri çekme devre dışı olduğundan destek sunulmuyor:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Confluent bağlantıları için yalnızca AVRO ve JSON serileştirme biçimlerine izin verilir. Şu nesneler farklı serileştirme biçimlerine sahip olduğundan desteklenmiyor:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Aşağıdaki nesneler için şema getirilemiyor. Uygun bağlamı seçin veya şema kayıt konfigürasyonunu doğrulayın
#XTOL: warning dialog header on deleting replication task
deleteHeader=Sil
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Yükleme öncesinde tümünü sil ayarı Google BigQuery için desteklenmiyor.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Yükleme öncesinde tümünü sil ayarı, her bir çoğaltma öncesinde nesnenin (konu) silinmesini ve yeniden oluşturulmasını sağlar. Bu ayrıca, tayin edilen tüm iletilerin de silinmesine neden olur.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Yükleme öncesinde tümünü sil ayarı, bu hedef türü için desteklenmiyor.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Teknik ad
#XCOL
connBusinessName=İş adı
#XCOL
connDescriptionName=Tanım
#XCOL
connType=Tür
#XMSG
connTblNoDataFoundtxt=Bağlantı bulunamadı
#XMSG
connectionError=Bağlantılar getirilirken hata oluştu.
#XMSG
connectionCombinationUnsupportedErrorTitle=Bağlantı birleşimi desteklenmiyor
#XMSG
connectionCombinationUnsupportedErrorMsgTxt={0} öğesinden {1} öğesine çoğaltma şu anda desteklenmiyor.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Bağlantı türü birleşimi desteklenmiyor
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=SAP HANA Cloud, veri gölü dosyaları bağlantı türüne sahip bir bağlantıdan {0} hedefine çoğaltma desteklenmiyor. Yalnızca SAP Datasphere''e çoğaltabilirsiniz.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Seç
#XBUT
containerCancelBtn=İptal
#XTOL
containerSelectTooltip=Seç
#XTOL
containerCancelTooltip=İptal
#XMSG
containerContainerPathPlcHold=Konteyner yolu
#XFLD
containerContainertxt=Konteyner
#XFLD
confluentContainerContainertxt=Bağlam
#XMSG
infoMessageForSLTSelection=Konteyner olarak yalnızca SLT/toplu aktarım tanıtıcısına izin verilir. SLT altında bir toplu aktarım tanıtıcısı seçin (varsa) ve Gönder'e tıklayın.
#XMSG
msgFetchContainerFail=Konteyner verileri getirilirken hata oluştu.
#XMSG
infoMessageForSLTHidden=SLT klasörleri bu bağlantı tarafından desteklenmediğinden aşağıdaki listede gösterilmiyor.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Alt klasörler içeren bir konteyner seçin.
#XMSG
sftpIncludeSubFolderText=Yanlış
#XMSG
sftpIncludeSubFolderTextNew=Hayır

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Henüz filtre eşlemesi yok)
#XMSG
failToFetchRemoteMetadata=Meta veriler getirilirken hata oluştu.
#XMSG
failToFetchData=Mevcut hedef getirilirken hata oluştu.
#XCOL
@loadType=Yükleme türü
#XCOL
@deleteAllBeforeLoading=Yükleme öncesinde tümünü sil

#XMSG
@loading=Yükleniyor...
#XFLD
@selectSourceObjects=Kaynak nesne seç
#XMSG
@exceedLimit=Tek seferde en fazla {0} nesne içe aktarabilirsiniz. En az {1} nesnenin seçimini kaldırın.
#XFLD
@objects=Nesneler
#XBUT
@ok=Tamam
#XBUT
@cancel=İptal
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Sonraki
#XBUT
btnAddSelection=Seçimi ekle
#XTOL
@remoteFromSelection=Seçimden kaldır
#XMSG
@searchInForSearchField=Arama yeri: {0}

#XCOL
@name=Teknik ad
#XCOL
@type=Tür
#XCOL
@location=Konum
#XCOL
@label=İş adı
#XCOL
@status=Durum

#XFLD
@searchIn=Arama yeri:
#XBUT
@available=Kullanılabilir
#XBUT
@selection=Seçim

#XFLD
@noSourceSubFolder=Tablolar ve görünümler
#XMSG
@alreadyAdded=Diyagramda zaten mevcut
#XMSG
@askForFilter={0} değerinden fazla sayıda öğe var. Öğe sayısını azaltmak için bir filtre dizesi girin.
#XFLD: success label
lblSuccess=Başarılı
#XFLD: ready label
lblReady=Hazır
#XFLD: failure label
lblFailed=Başarısız oldu
#XFLD: fetching status label
lblFetchingDetail=Ayrıntılar getiriliyor

#XMSG Place holder text for tree filter control
filterPlaceHolder=Üst düzey nesneleri filtrelemek için metin yazın
#XMSG Place holder text for server search control
serverSearchPlaceholder=Aramak için yazıp Enter'a basın
#XMSG
@deployObjects={0} nesne içe aktarılıyor...
#XMSG
@deployObjectsStatus=İçe aktarılan nesne sayısı: {0}. İçe aktarılamayan nesne sayısı: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Yerel havuz tarayıcısı açılamadı.
#XMSG
@openRemoteSourceBrowserError=Kaynak nesneler getirilemedi.
#XMSG
@openRemoteTargetBrowserError=Hedef nesneler getirilemedi.
#XMSG
@validatingTargetsError=Hedefler doğrulanırken hata oluştu.
#XMSG
@waitingToImport=İçe aktarım için hazır

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Azami nesne sayısı aşıldı. Bir çoğaltma akışı için en fazla 500 nesne seçin.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Teknik ad
#XFLD
sourceObjectBusinessName=İş adı
#XFLD
sourceNoColumns=Sütun sayısı
#XFLD
containerLbl=Konteyner

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Çoğaltma akışı için kaynak bağlantı seçmeniz gerekir.
#XMSG
validationSourceContainerNonExist=Kaynak bağlantı için konteyner seçmeniz gerekir.
#XMSG
validationTargetNonExist=Çoğaltma akışı için hedef bağlantı seçmeniz gerekir.
#XMSG
validationTargetContainerNonExist=Hedef bağlantı için konteyner seçmeniz gerekir.
#XMSG
validationTruncateDisabledForObjectTitle=Nesne depolamalarına çoğaltma.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Bulut depolamaya çoğaltma işlemi, yalnızca Yükleme öncesinde tümünü sil seçeneği ayarlandıysa veya hedef nesne hedefte mevcut değilse mümkündür.{0}{0} Yükleme öncesinde tümünü sil seçeneğinin ayarlanmadığı nesneler için yine de çoğaltmayı etkinleştirmek adına, çoğaltma akışını çalıştırmadan önce hedef nesnenin sistemde mevcut olmadığından emin olun.
#XMSG
validationTaskNonExist=Çoğaltma akışında en az bir çoğaltmanız bulunmalıdır.
#XMSG
validationTaskTargetMissing=Şu kaynağı içeren çoğaltma için bir hedefiniz olmalıdır: {0}
#XMSG
validationTaskTargetIsSAC=Seçilen hedef bir SAC nesnesi: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Seçilen hedef, desteklenen bir yerel tablo değil: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Bu ada sahip bir nesne hedefte zaten mevcut. Ancak bu nesne yerel bir tablo olmadığından yerel havuza ilişkin bir çoğaltma akışı için hedef nesne olarak kullanılamıyor.
#XMSG
validateSourceTargetSystemDifference=Çoğaltma akışı için farklı kaynak ve hedef bağlantı ile konteyner bileşimleri seçmeniz gerekir.
#XMSG
validateDuplicateSources=Bir veya daha fazla çoğaltma, çift kaynak nesne adları içeriyor: {0}.
#XMSG
validateDuplicateTargets=Bir veya daha fazla çoğaltma, çift hedef nesne adları içeriyor: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated={0} kaynak nesnesi, delta yakalamasını desteklemiyor ancak {1} hedef nesnesi destekliyor. Çoğaltmayı kaldırmanız gerekir.
#XMSG
validationTaskTargetObjectLoadTypeMismatch={0} adlı hedef nesneyi içeren çoğaltma için "İlk ve delta" yükleme türünü seçmeniz gerekir.
#XMSG
validationAutoRenameTarget=Hedef sütunlar yeniden adlandırıldı.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Otomatik projeksiyon eklendi ve hedefe çoğaltma yapılmasına olanak sağlamak üzere şu hedef sütunlar yeniden adlandırıldı:{1}{1} {0} {1}{1}Bu, şu nedenlerden birinden kaynaklanmaktadır:{1}{1}{2} Desteklenmeyen karakterler{1}{2} Rezerve edilen önek
#XMSG
validationAutoRenameTargetDescriptionUpdated=Otomatik projeksiyon eklendi ve Google BigQuery''ye çoğaltma yapılmasına olanak sağlamak üzere şu hedef sütunlar yeniden adlandırıldı:{1}{1} {0} {1}{1}Bu, aşağıdaki nedenlerden birinden kaynaklanmaktadır:{1}{1}{2}Rezerve edilen sütun adı{1}{2} Desteklenmeyen karakterler{1}{2} Rezerve edilen önek
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Otomatik projeksiyon eklendi ve Conflluent''e çoğaltma yapılmasına olanak sağlamak üzere şu hedef sütunlar yeniden adlandırıldı:{1}{1} {0} {1}{1}Bu, aşağıdaki nedenlerden birinden kaynaklanmaktadır:{1}{1}{2}Rezerve edilen sütun adı{1}{2} Desteklenmeyen karakterler{1}{2} Rezerve edilen önek
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Otomatik projeksiyon eklendi ve hedefte çoğaltmalar yapılmasına olanak sağlamak üzere şu hedef sütunlar yeniden adlandırıldı:{1}{1} {0} {1}{1}Bu, şu nedenlerden birinden kaynaklanmaktadır:{1}{1}{2} Rezerve edilen sütun adı{1}{2} Desteklenmeyen karakterler{1}{2} Rezerve edilen önek
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Hedef nesne yeniden adlandırıldı.
#XMSG
autoRenameInfoDesc=Hedef nesne desteklenmeyen karakterler içerdiğinden yeniden adlandırıldı. Yalnızca şu karakterler desteklenir:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(nokta){0}{1}_(altçizgi){0}{1}-(tire)
#XMSG
validationAutoTargetTypeConversion=Hedef veri türleri değiştirildi.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Google BigQuery''de kaynak veri türleri desteklenmediğinden, şu hedef sütunlar için hedef veri türleri değiştirildi:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Hedef bağlantıda kaynak veri türleri desteklenmediğinden şu hedef sütunlar için hedef veri türleri değiştirildi:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Hedef sütun adlarını kısaltın.
#XMSG
validationMaxCharLengthGBQTargetDescription=Google BigQuery''deki sütun adlarında en fazla 300 karakter kullanılabilir. Şu hedef sütun adlarını kısaltmak için projeksiyon kullanın:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Birincil anahtarlar oluşturulamıyor.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=Google BigQuery'de en fazla 16 birincil anahtar desteklenir. Ancak kaynak nesne daha fazla sayıda birincil anahtar içeriyor. Hedef nesnede birincil anahtarlardan hiçbiri oluşturulmayacak.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Bir veya daha fazla kaynak sütun, Google BigQuery''de birincil anahtar olarak tanımlanamayan veri türleri içeriyor. Hedef nesnede birincil anahtarlardan hiçbiri oluşturulmayacak.{0}{0}Şu hedef veri türleri, birincil anahtarı tanımlanabilen Google BigQuery veri türleriyle uyumlu: {0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Birincil anahtar olarak bir veya birden fazla sütun tanımlayın.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Birincil anahtar olarak bir veya birden fazla sütun tanımlamanız gerekir. Bunu yapmak için kaynak şema iletişim kutusunu kullanın.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Birincil anahtar olarak bir veya birden fazla sütun tanımlayın.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Kaynak nesneniz için birincil anahtar kısıtlamalarla eşleşen birincil anahtar olarak bir veya birden fazla sütun tanımlamanız gerekir. Bunu yapmak için kaynak nesne özelliklerinizde Şemayı konfigüre et'e gidin.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Geçerli bir azami bölme değeri girin.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Azami bölme değeri ≥ 1 ve ≤ 2147483647 olmalıdır
#XMSG
validateHDLFNoPKDatasetError=Birincil anahtar olarak bir veya birden fazla sütun tanımlayın.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Bir nesneyi çoğaltmak için birincil anahtar olarak bir veya birden fazla hedef sütun tanımlamanız gerekir. Bunu yapmak için bir projeksiyon kullanın.
#XMSG
validateHDLFNoPKExistingDatasetError=Birincil anahtar olarak bir veya birden fazla sütun tanımlayın.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Mevcut bir hedef nesneye veri çoğaltmak için birincil anahtar olarak tanımlanmış bir veya birden fazla sütunu olması gerekir. {0} Birincil anahtar olarak bir veya birden fazla sütun tanımlamak için şu seçenekleriniz vardır: {0} {1} Mevcut hedef nesneyi değiştirmek için yerel tablo düzenleyicisini kullanın. Ardından çoğaltma akışını tekrar yükleyin.{0}{1} Çoğaltma akışında hedef nesneyi yeniden adlandırın. Bu, çalıştırma başlatılır başlatılmaz yeni bir nesne oluşturulmasını sağlar. Yeniden adlandırma işleminden sonra projeksiyonda bir veya daha fazla sütunu birincil anahtar olarak tanımlayabilirsiniz.{0}{1} Nesneyi, bir veya daha fazla sütunun zaten birincil anahtar olarak tanımlandığı mevcut başka bir hedef nesneyle eşleyin.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Seçilen hedef şu havuzda zaten mevcut: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Delta yakalama tablo adları havuzdaki diğer tablolar tarafından zaten kullanılıyor: {0} Çoğaltma akışını kaydedebilmek için öncelikle ilişkili delta yakalama tablosu adlarının benzersiz olmasını sağlamak üzere bu hedef nesneleri yeniden adlandırmanız gerekir.
#XMSG
validateConfluentEmptySchema=Şema tanımla
#XMSG
validateConfluentEmptySchemaDescUpdated=Kaynak tablo şema içermiyor. Şema tanımlamak için Şemayı konfigüre et seçeneğini belirleyin
#XMSG
validationCSVEncoding=Geçersiz CSV kodlama
#XMSG
validationCSVEncodingDescription=Görevin CSV kodlaması geçerli değil.
#XMSG
validateConfluentEmptySchema=Uyumlu hedef veri türü seçin
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Uyumlu hedef veri türü seçin
#XMSG
globalValidateTargetDataTypeDesc=Sütun eşlemelerinde sorun meydana geldi. Projeksiyon'a gidin ve tüm kaynak sütunların benzersiz bir sütunla, uyumlu bir veri türü içeren bir sütunla eşleştirildiğinden ve tanımlanan tüm ifadelerin geçerli olduğundan emin olun.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Yinelenen sütun adları.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Yinelenen sütun adları desteklenmiyor. Bunları düzeltmek için projeksiyon iletişim kutusunu kullanın. Şu hedef nesneler, yinelenen sütun adları içeriyor: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Yinelenen sütun adları.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Yinelenen sütun adları desteklenmiyor. Şu hedef nesneler, yinelenen sütun adları içeriyor: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Verilerde tutarsızlıklar olabilir.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Yalnızca Delta yükleme türü, son kaydetme ile sonraki çalıştırma arasında kaynakta yapılan değişiklikleri dikkate almaz.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Yükleme türünü "İlk" olarak değiştirin.
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Birincil anahtarı olmayan ABAP temelindeki nesnelerin çoğaltılması yalnızca "Yalnızca ilk" yükleme türü için olanaklıdır.
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Delta yakalamasını devre dışı bırakın.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Birincil anahtarı olmaya bir nesneyi ABAP kaynak bağlantısı türü kullanarak çoğaltmak için öncelikle bu tabloya yönelik delta yakalamasını devre dışı bırakmanız gerekir.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Hedef nesneyi değiştirin.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Delta yakalaması etkinleştirildiğinden hedef nesne kullanılamıyor. Hedef nesneyi yeniden adlandırabilir ve ardından yeni (yeniden adlandırılan) nesne için delta yakalamasını kapatabilir veya kaynak nesneyi delta yakalamasının devre dışı bırakıldığı mevcut bir nesneye eşleyebilirsiniz.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Hedef nesneyi değiştirin.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Hedef nesne, gerekli __load_package_id teknik sütununu içermediğinden kullanılamıyor. Henüz mevcut olmayan bir ad kullanarak hedef nesneyi yeniden adlandırabilirsiniz. Ardından sistem, kaynak nesneyle aynı tanıma sahip ve teknik sütunu içeren yeni bir nesne oluşturur. Alternatif olarak hedef nesneyi, gerekli teknik sütunu (__load_package_id) içeren mevcut bir nesneyle eşleyebilirsiniz.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Hedef nesneyi değiştirin.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Hedef nesne, gerekli __load_record_id teknik sütununu içermediğinden kullanılamıyor. Henüz mevcut olmayan bir ad kullanarak hedef nesneyi yeniden adlandırabilirsiniz. Ardından sistem, kaynak nesneyle aynı tanıma sahip ve teknik sütunu içeren yeni bir nesne oluşturur. Alternatif olarak hedef nesneyi, gerekli teknik sütunu (__load_record_id) içeren mevcut bir nesneyle eşleyebilirsiniz.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Hedef nesneyi değiştirin.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Hedef nesne, __load_record_id teknik sütununun veri türü "string(44)" olmadığından kullanılamıyor. Henüz mevcut olmayan bir ad kullanarak hedef nesneyi yeniden adlandırabilirsiniz. Ardından sistem, kaynak nesneyle aynı tanıma ve dolayısıyla doğru veri türüne sahip yeni bir nesne oluşturur. Alternatif olarak hedef nesneyi, doğru veri türünden gerekli teknik sütunu (__load_record_id) içeren mevcut bir nesneyle eşleyebilirsiniz.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Hedef nesneyi değiştirin.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Hedef nesne birincil anahtar içerirken kaynak nesne içermediğinden hedef nesne kullanılamıyor. Henüz mevcut olmayan bir ad kulanarak hedef nesneyi yeniden adlandırabilirsiniz. Ardından sistem, kaynak nesneyle aynı tanıma sahip ve dolayısıyla birincil anahtar içermeyen yeni bir nesne oluşturur. Alternatif olarak hedef nesneyi, gerekli teknik sütunu (__load_package_id) içeren ve birincil anahtar içermeyen mevcut bir nesneyle eşleyebilirsiniz.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Hedef nesneyi değiştirin.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Hedef nesne birincil anahtar içerirken kaynak nesne içermediğinden hedef nesne kullanılamıyor. Henüz mevcut olmayan bir ad kullanarak hedef nesneyi yeniden adlandırabilirsiniz. Ardından sistem, kaynak nesneyle aynı tanıma sahip ve dolayısıyla birincil anahtar içermeyen yeni bir nesne oluşturur. Alternatif olarak hedef nesneyi, gerekli teknik sütunu (__load_record_id) içeren ve birincil anahtar içermeyen mevcut bir nesneyle eşleyebilirsiniz.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Hedef nesneyi değiştirin.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Hedef nesne, __load_package_id teknik sütununun veri türü "binary(>=256)" olmadığından kullanılamıyor. Henüz mevcut olmayan bir ad kullanarak hedef nesneyi yeniden adlandırabilirsiniz. Ardından sistem, kaynak nesneyle aynı tanıma ve dolayısıyla doğru veri türüne sahip yeni bir nesne oluşturur. Alternatif olarak hedef nesneyi, doğru veri türünden gerekli teknik sütunu (__load_package_id) içeren mevcut bir nesneyle eşleyebilirsiniz.
#XMSG
validationAutoRenameTargetDPID=Hedef sütunlar yeniden adlandırıldı.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Kaynak nesneyi kaldırın.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Kaynak nesne, anahtar sütun içermiyor ve bu, söz konusu bağlamda desteklenmiyor.
#XMSG
validationAutoRenameTargetDPIDDescription=Otomatik projeksiyon eklendi ve anahtarlar olmadan ABAP kaynağından çoğaltma yapılmasına olanak sağlamak üzere şu hedef sütunlar yeniden adlandırıldı:{1}{1} {0} {1}{1}Bu, aşağıdaki nedenlerden birinden kaynaklanmaktadır:{1}{1}{2}Rezerve edilen sütun adı{1}{2} Desteklenmeyen karakterler{1}{2} Rezerve edilen önek
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle={0} öğesine çoğaltma.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Hedefi {0} olan çoğaltma akışlarını kaydetme ve dağıtma, bu işlevde bakım yürütmekte olduğumuz için şu anda olanaklı değil.
#XMSG
TargetColumnSkippedLTF=Hedef sütun atlandı.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Desteklenmeyen veri türü nedeniyle hedef sütun atlandı. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Birincil anahtar olarak zaman sütunu.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Kaynak nesne, birincil anahtar olarak zaman sütunu içeriyor. Bu, bu bağlamda desteklenmiyor.
#XMSG
validateNoPKInLTFTarget=Birincil anahtar eksik.
#XMSG
validateNoPKInLTFTargetDescription=Hedefte birincil anahtar tanımlanmamış ve bu durum, söz konusu bağlamda desteklenmiyor.
#XMSG
validateABAPClusterTableLTF=ABAP küme tablosu.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Kaynak nesne bir ABAP küme tablosu ve bu, söz konusu bağlamda desteklenmiyor. 
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Görünüşe göre henüz hiç veri eklemediniz.
#YINS
welcomeText2=Çoğaltma akışınızı başlatmak için sol taraftan bir bağlantı ve kaynak nesne seçin.

#XBUT
wizStep1=Kaynak bağlantı seç
#XBUT
wizStep2=Kaynak konteyner seç
#XBUT
wizStep3=Kaynak nesne ekle

#XMSG
limitDataset=Azami nesne sayısına ulaşıldı. Yenilerini eklemek için mevcut nesneleri kaldırın veya yeni çoğaltma akışı oluşturun.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Bu ay için kullanılabilir çıkış hacmi olmadığından bu SAP olmayan hedef bağlantıya çoğaltma akışı başlatılamıyor.
#XMSG
premiumOutBoundRFAdminWarningMsg=Bir yönetici, bu kiracı için prim çıkışı bloklarını artırarak çıkış hacmini bu ay için kullanılabilir hale getirebilir.
#XMSG
messageForToastForDPIDColumn2={0} nesne için hedefe yeni sütun eklendi. Birincil anahtar içermeyen ABAP temelindeki kaynak nesnelerle bağlantılı yinelenen kayıtları işlemek için gereklidir.
#XMSG
PremiumInboundWarningMessage=Çoğaltma akışlarının sayısına ve çoğaltılacak veri hacmine bağlı olarak, {1} yoluyla veri çoğaltma için gerekli{0}SAP HANA kaynakları kiracınızın kullanılabilir kapasitesini aşabilir.
#XMSG
PremiumInboundWarningMsg=Çoğaltma akışlarının sayısına ve çoğaltılacak veri hacmine bağlı olarak, "{1}" yoluyla veri çoğaltma için gerekli{0}SAP HANA kaynakları kiracınızın kullanılabilir kapasitesini aşabilir.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Projeksiyon adı girin.
#XMSG
emptyTargetColumn=Hedef sütun adı girin.
#XMSG
emptyTargetColumnBusinessName=Hedef sütun İş adı girin.
#XMSG
invalidTransformName=Projeksiyon adı girin.
#XMSG
uniqueColumnName=Hedef sütunu yeniden adlandırın.
#XMSG
copySourceColumnLbl=Kaynak nesneden sütun kopyala
#XMSG
renameWarning=Hedef tabloyu yeniden adlandırırken benzersiz ad seçtiğinizden emin olun. Yeni ada sahip tablo, alanda zaten mevcutsa söz konusu tablonun tanımını kullanacaktır.

#XMSG
uniqueColumnBusinessName=Hedef sütun iş adını değiştirin.
#XMSG
uniqueSourceMapping=Farklı bir kaynak sütun seçin.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=Kaynak sütun {0}, şu hedef sütunlar tarafından zaten kullanılıyor:{1}{1}{2}{1}{1} Bu hedef sütun veya diğer hedef sütunlar için projeksiyonu kaydetmek üzere, kullanımda olmayan bir kaynak sütun seçin.
#XMSG
uniqueColumnNameDescription=Girdiğiniz hedef sütun adı zaten var. Projeksiyonu kaydedebilmek için  benzersiz bir sütun adı girmelisiniz.
#XMSG
uniqueColumnBusinessNameDesc=Hedef sütun iş adı zaten mevcut. Projeksiyonu kaydedebilmek için benzersiz bir sütun iş adı girmeniz gerekir.
#XMSG
emptySource=Kaynak sütun seçin veya sabit değer girin.
#XMSG
emptySourceDescription=Geçerli eşleme girişi oluşturmak için kaynak sütun seçmeli veya sabit değer girmelisiniz.
#XMSG
emptyExpression=Eşlemeyi tanımlayın.
#XMSG
emptyExpressionDescription1=Hedef sütunu eşlemek istediğiniz kaynak sütunu veya {0} İşlevler / Sabitler {1}. {2} {2} sütunundaki onay kutusunu seçin. İşlevler, hedef veri türüne göre otomatik olarak girilir. Sabit değerler manüel olarak girilebilir.
#XMSG
numberExpressionErr=Sayı girin.
#XMSG
numberExpressionErrDescription=Sayısal veri türünü seçtiniz. Bu, yalnızca sayıları ve uygulanabiliyorsa ondalık noktasını girebileceğiniz anlamına gelir. Tek tırnak işareti kullanmayın.
#XMSG
invalidLength=Geçerli bir uzunluk değeri girin.
#XMSG
invalidLengthDescription=Veri türü uzunluğu, kaynak sütun uzunluğuna eşit veya büyük olmalı ve 1 ile 5000 arasında olmalıdır.
#XMSG
invalidMappedLength=Geçerli bir uzunluk değeri girin.
#XMSG
invalidMappedLengthDescription=Veri türü uzunluğu, {0} kaynak sütun uzunluğuna eşit veya büyük olmalı ve 1 ile 5000 arasında olmalıdır.
#XMSG
invalidPrecision=Geçerli bir doğruluk değeri girin.
#XMSG
invalidPrecisionDescription=Doğruluk toplam rakam sayısını tanımlar. Ölçek, ondalık noktadan sonraki rakam sayısını tanımlar ve 0 ile doğruluk arasında olabilir{0}{0} Örnekler: {0}{1} Doğruluk 6, ölçek 2, 1234,56 {0}{1} gibi sayılara karşılık gelir. Doğruluk 6, ölçek 6 ise 0,123546 gibi sayılara karşılık gelir.{0} {0}Hedef için doğruluk ve ölçek, kaynağın doğruluğu ve ölçeği ile uyumlu olmalıdır, böylece kaynaktaki tüm rakamlar hedef alana sığar. Örneğin, kaynakta doğruluk 6 ve ölçek 2''ye sahipseniz (ve sonuç olarak ondalık noktadan önce 0''dan başka rakamlar varsa), hedefte doğruluk 6 ve ölçek 6''ya sahip olamazsınız.
#XMSG
invalidPrimaryKey=En az bir birincil anahtar girin.
#XMSG
invalidPrimaryKeyDescription=Birincil anahtar bu şema için tanımlanmadı.
#XMSG
invalidMappedPrecision=Geçerli bir doğruluk değeri girin.
#XMSG
invalidMappedPrecisionDescription1=Doğruluk toplam rakam sayısını tanımlar. Ölçek, ondalık noktadan sonraki rakam sayısını tanımlar ve 0 ile doğruluk arasında olabilir{0}{0} Örnekler:{0}{1} Doğruluk 6, ölçek 2, 1234,56 gibi sayılara karşılık gelir.{0}{1} Doğruluk 6, ölçek 6 ise 0,123546 gibi sayılara karşılık gelir.{0}{0}Veri türünün doğruluğu, kaynağın doğruluğuna eşit veya daha büyük olmalıdır ({2}).
#XMSG
invalidScale=Geçerli bir ölçek değeri girin.
#XMSG
invalidScaleDescription=Doğruluk toplam rakam sayısını tanımlar. Ölçek, ondalık noktadan sonraki rakam sayısını tanımlar ve 0 ile doğruluk arasında olabilir{0}{0} Örnekler: {0}{1} Doğruluk 6, ölçek 2, 1234,56 {0}{1} gibi sayılara karşılık gelir. Doğruluk 6, ölçek 6 ise 0,123546 gibi sayılara karşılık gelir.{0} {0}Hedef için doğruluk ve ölçek, kaynağın doğruluğu ve ölçeği ile uyumlu olmalıdır, böylece kaynaktaki tüm rakamlar hedef alana sığar. Örneğin, kaynakta doğruluk 6 ve ölçek 2''ye sahipseniz (ve sonuç olarak ondalık noktadan önce 0''dan başka rakamlar varsa), hedefte doğruluk 6 ve ölçek 6''ya sahip olamazsınız.
#XMSG
invalidMappedScale=Geçerli bir ölçek değeri girin.
#XMSG
invalidMappedScaleDescription1=Doğruluk toplam rakam sayısını tanımlar. Ölçek, ondalık noktadan sonraki rakam sayısını tanımlar ve 0 ile doğruluk arasında olabilir{0}{0} Örnekler:{0}{1} Doğruluk 6, ölçek 2, 1234,56 gibi sayılara karşılık gelir.{0}{1} Doğruluk 6, ölçek 6 ise 0,123546 gibi sayılara karşılık gelir.{0}{0}Veri türünün ölçeği, kaynağın ölçeğine eşit veya daha büyük olmalıdır ({2}).
#XMSG
nonCompatibleDataType=Uyumlu hedef veri türü seçin.
#XMSG
nonCompatibleDataTypeDescription1=Burada belirttiğiniz veri türü ile kaynak veri türü ({0}) uyumlu olmalıdır. {1}{1} Örnek: Kaynak sütununuzun veri türü dizeyse ve harfler içeriyorsa, hedefiniz için ondalık veri türü kullanamazsınız.
#XMSG
invalidColumnCount=Kaynak sütun seçin.
#XMSG
ObjectStoreInvalidScaleORPrecision=Doğruluk ve ölçek için geçerli değer girin.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=İlk değer doğruluktur ve toplam basamak sayısını tanımlar. İkinci değer ölçektir ve ondalık nokta sonrasındaki basamak sayısını tanımlar. Kaynak ölçek değerinden daha büyük bir hedef ölçek değeri girin ve girilen hedef ölçek ile doğruluk değeri arasındaki farkın, kaynak ölçek ile doğruluk değeri arasındaki farktan daha büyük olduğundan emin olun.
#XMSG
InvalidPrecisionORScale=Doğruluk ve ölçek için geçerli değer girin.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=İlk değer doğruluktur ve toplam basamak sayısını tanımlar. İkinci değer ölçektir ve ondalık nokta sonrasındaki basamak sayısını tanımlar.{0}{0}Kaynak veri türü Google BigQuery''de desteklenmediğinden DECIMAL hedef veri türüne dönüştürülür. Bu durumda doğruluk yalnızca 38 ve 76 arasında tanımlanabilir, ölçek ise 9 ve 38 arasında tanımlanabilir. Ayrıca, ondalık nokta öncesindeki basamak sayısını temsil eden doğruluk eksi ölçek sonucu 28 ve 38 arasında olmalıdır.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=İlk değer doğruluktur ve toplam basamak sayısını tanımlar. İkinci değer ölçektir ve ondalık nokta sonrasındaki basamak sayısını tanımlar.{0}{0}Kaynak veri türü Google BigQuery''de desteklenmediğinden DECIMAL hedef veri türüne dönüştürülür. Bu durumda doğruluk 20 veya daha büyük bir değer olarak tanımlanmalıdır. Ayrıca, ondalık nokta öncesindeki basamak sayısını belirten doğruluk eksi ölçek sonucu 20 veya daha büyük bir değer olmalıdır.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=İlk değer doğruluktur ve toplam basamak sayısını tanımlar. İkinci değer ölçektir ve ondalık nokta sonrasındaki basamak sayısını tanımlar.{0}{0}Kaynak veri türü hedefte desteklenmediğinden DECIMAL hedef veri türüne dönüştürülür. Bu durumda, doğruluk 1''den büyük veya 1''e eşit ve 38''den küçük ya da 38''e eşit herhangi bir sayıyla tanımlanmalıdır ve ölçek doğruluktan küçük veya doğruluğa eşit olmalıdır.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=İlk değer doğruluktur ve toplam basamak sayısını tanımlar. İkinci değer ölçektir ve ondalık nokta sonrasındaki basamak sayısını tanımlar.{0}{0}Kaynak veri türü hedefte desteklenmediğinden DECIMAL hedef veri türüne dönüştürülür. Bu durumda doğruluk 20 veya daha büyük bir değer olarak tanımlanmalıdır. Dahası, doğruluktan ölçek çıkarıldığında elde edilen değer (ondalık nokta öncesindeki basamak sayısını yansıtır) 20 veya daha büyük olmalıdır.
#XMSG
invalidColumnCountDescription=Geçerli eşleme girişi oluşturmak için kaynak sütun seçmeli veya sabit değer girmelisiniz.
#XMSG
duplicateColumns=Hedef sütunu yeniden adlandırın.
#XMSG
duplicateGBQCDCColumnsDesc=Hedef sütun adı, Google BigQuery'de rezerve edildi. Projeksiyonu kaydedebilmek için onu yeniden adlandırmalısınız.
#XMSG
duplicateConfluentCDCColumnsDesc=Hedef sütun adı, Confluent'te rezerve edildi. Projeksiyonu kaydedebilmek için hedef sütunu yeniden adlandırmanız gerekir.
#XMSG
duplicateSignavioCDCColumnsDesc=Hedef sütun adı, SAP Signavio'da rezerve edildi. Projeksiyonu kaydedebilmek için hedef sütunu yeniden adlandırmanız gerekir.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Hedef sütun adı, MS OneLake'te rezerve edildi. Projeksiyonu kaydedebilmek için hedef sütunu yeniden adlandırmanız gerekir.
#XMSG
duplicateSFTPCDCColumnsDesc=Hedef sütun adı, SFTP'de rezerve edildi. Projeksiyonu kaydedebilmek için hedef sütunu yeniden adlandırmanız gerekir.
#XMSG
GBQTargetNameWithPrefixUpdated1=Hedef sütun, Google BigQuery''de rezerve edilen bir önek içeriyor. Projeksiyonu kaydedebilmek için onu yeniden adlandırmalısınız.  {0}{0}Hedef sütun adı, aşağıdaki dizilerle başlayamaz:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Hedef sütun adını kısaltın.
#XMSG
GBQtargetMaxLengthDesc=Google BigQuery'de sütun adı azami 300 karakter kullanabilir. Projeksiyonu kaydedebilmek için hedef sütun adını kısaltın.
#XMSG
invalidMappedScalePrecision=Kaynaktaki tüm rakamların hedef alana uymaları için hedefe ilişkin doğruluk ve ölçek ile kaynağa ilişkin doğruluk ve ölçek uyumlu olmalıdır.
#XMSG
invalidMappedScalePrecisionShortText=Geçerli doğruluk ve ölçek değeri girin.
#XMSG
validationIncompatiblePKTypeDescProjection3=Bir veya daha fazla kaynak sütun, Google BigQuery''de birincil anahtar olarak tanımlanamayan veri türleri içeriyor. Hedef nesnede birincil anahtarlardan hiçbiri oluşturulmayacak.{0}{0}Şu hedef veri türleri, birincil anahtarı tanımlanabilecek Google BigQuery veri türleriyle uyumlu: {1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=column __message_id seçimini kaldır.
#XMSG
uncheckColumnMessageIdDesc=Sütun: Birincil anahtar
#XMSG
validationOpCodeInsert=Ekle için bir değer girmeniz gerekir.
#XMSG
recommendDifferentPrimaryKey=Kalem düzeyinde farklı bir birincil anahtar seçmenizi öneririz.
#XMSG
recommendDifferentPrimaryKeyDesc=İşlem kodu zaten tanımlandığında, yinelenen sütun gibi sorunları önlemek üzere dizi dizini ve kalemler için farklı birincil anahtarlar seçmeniz önerilir.
#XMSG
selectPrimaryKeyItemLevel=Hem başlık hem de kalem düzeyi için en az bir birincil anahtar seçmeniz gerekir.
#XMSG
selectPrimaryKeyItemLevelDesc=Bir dizi veya eşleme genişletildiğinde, biri başlık düzeyinde ve biri kalem düzeyinde olmak üzere iki birincil anahtar seçmeniz gerekir.
#XMSG
invalidMapKey=Başlık düzeyinde en az bir birincil anahtar seçmeniz gerekir.
#XMSG
invalidMapKeyDesc=Bir dizi veya eşleme genişletildiğinde, başlık düzeyinde bir birincil anahtar seçmeniz gerekir.
#XFLD
txtSearchFields=Hedef sütun ara
#XFLD
txtName=Ad
#XMSG
txtSourceColValidation=Bir veya daha fazla kaynak sütun desteklenmiyor:
#XMSG
txtMappingCount=Eşlemeler ({0})
#XMSG
schema=Şema
#XMSG
sourceColumn=Kaynak sütunlar
#XMSG
warningSourceSchema=Şemada yapılan her türlü değişiklik, projeksiyon iletişim kutusundaki eşlemeleri etkileyecek.
#XCOL
txtTargetColName=Hedef sütun (teknik ad)
#XCOL
txtDataType=Hedef veri türü
#XCOL
txtSourceDataType=Kaynak veri türü
#XCOL
srcColName=Kaynak sütun (teknik ad)
#XCOL
precision=Doğruluk
#XCOL
scale=Ölçek
#XCOL
functionsOrConstants=İşlevler / kısıtlamalar
#XCOL
txtTargetColBusinessName=Hedef sütun (İş adı)
#XCOL
prKey=Birincil anahtar
#XCOL
txtProperties=Özellikler
#XBUT
txtOK=Kaydet
#XBUT
txtCancel=İptal
#XBUT
txtRemove=Kaldır
#XFLD
txtDesc=Tanım
#XMSG
rftdMapping=Eşleme
#XFLD
@lblColumnDataType=Veri türü
#XFLD
@lblColumnTechnicalName=Teknik ad
#XBUT
txtAutomap=Otomatik olarak eşle
#XBUT
txtUp=Yukarı
#XBUT
txtDown=Aşağı

#XTOL
txtTransformationHeader=Projeksiyon
#XTOL
editTransformation=Düzenle
#XTOL
primaryKeyToolip=Anahtar


#XMSG
rftdFilter=Filtrele
#XMSG
rftdFilterColumnCount=Kaynak: {0}({1})
#XTOL
rftdFilterColSearch=Ara
#XMSG
rftdFilterColNoData=Görüntülenecek sütun yok
#XMSG
rftdFilteredColNoExps=Filtre ifadesi yok
#XMSG
rftdFilterSelectedColTxt=Şunun için filtre ekleyin
#XMSG
rftdFilterTxt=Filtre şunun için kullanılabilir:
#XBUT
rftdFilterSelectedAddColExp=İfade ekle
#YINS
rftdFilterNoSelectedCol=Filtre eklemek için bir sütun seçin.
#XMSG
rftdFilterExp=Filtre ifadesi
#XMSG
rftdFilterNotAllowedColumn=Filtre ekleme, bu sütun için desteklenmiyor.
#XMSG
rftdFilterNotAllowedHead=Desteklenmeyen sütun
#XMSG
rftdFilterNoExp=Filtre tanımlanmadı
#XTOL
rftdfilteredTt=Filtrelendi
#XTOL
rftdremoveexpTt=Filtre ifadesini kaldır
#XTOL
validationMessageTt=Doğrulama iletileri
#XTOL
rftdFilterDateInp=Tarih seçin
#XTOL
rftdFilterDateTimeInp=Tarih ve saat seçin
#XTOL
rftdFilterTimeInp=Saat seçin
#XTOL
rftdFilterInp=Değer gir
#XMSG
rftdFilterValidateEmptyMsg={1} sütunundaki {0} filtre ifadesi boş
#XMSG
rftdFilterValidateInvalidNumericMsg={1} sütunundaki {0} filtre ifadesi geçersiz sayısal değerler içeriyor
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Filtre ifadesinde geçerli sayısal değerler bulunmalıdır
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Hedef nesne şeması değiştiyse değişikliklerin uyarlanması için ana sayfada "Mevcut hedef nesneyle eşle" işlevini kullanın ve hedef nesneyi kaynağıyla yeniden eşleyin.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Hedef tablo zaten mevcutsa ve eşleme bir şema değişikliği içeriyorsa çoğaltma akışını dağıtmadan önce hedef tabloyu uygun şekilde değiştirmeniz gerekir.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Eşlemeniz bir şema değişikliği içeriyorsa çoğaltma akışını dağıtmadan önce hedef tabloyu uygun şekilde değiştirmeniz gerekir.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Desteklenmeyen şu sütunlar kaynak tanımında atlandı: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Desteklenmeyen şu sütunlar hedef tanımında atlandı: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Tüketim için gösterildiklerinden şu nesneler desteklenmiyor: {0} {1} {0} {0} Tabloları bir çoğaltma akışında kullanmak için semantik kullanımın (tablo ayarlarında) {2}Analitik veri kümesi{2} olarak ayarlanmamış olması gerekir.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Tüketim için gösterildiğinden hedef nesne desteklenmiyor: {0} {0} Tabloyu bir çoğaltma akışında kullanmak için semantik kullanımın (tablo ayarlarında) {1}Analitik veri kümesi{1} olarak ayarlanmamış olması gerekir.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Bu ada sahip bir hedef nesne zaten mevcut. Ancak tüketim için gösterildiğinden hedef nesne kullanılamıyor: {0} {0} Tabloyu bir çoğaltma akışında kullanmak için semantik kullanımın (tablo ayarlarında) {1}Analitik veri kümesi{1} olarak ayarlanmamış olması gerekir.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Bu ada sahip bir nesne hedefte zaten mevcut. {0}Ancak bu nesne yerel bir tablo olmadığından yerel havuza ilişkin bir çoğaltma akışı için hedef nesne olarak kullanılamıyor.
#XMSG:
targetAutoRenameUpdated=Hedef sütun yeniden adlandırıldı.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Google BigQuery içinde çoğaltmalara izin vermek için hedef sütun yeniden adlandırıldı. Bu, aşağıdaki nedenlerden birinden kaynaklanıyor: {0} {1}{2}Rezerve edilen sütun adı{3}{2} desteklenmeyen karakterler{3}{2} veya rezerve edilen önek{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Confluent içinde çoğaltmalara izin vermek için hedef sütun yeniden adlandırıldı. Bu, aşağıdaki nedenlerden birinden kaynaklanıyor: {0} {1}{2}Rezerve edilen sütun adı{3}{2} Desteklenmeyen karakterler{3}{2}Rezerve edilen önek{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Hedefte çoğaltmalara izin vermek için hedef sütun yeniden adlandırıldı. Bu, şu nedenlerden birinden kaynaklanıyor: {0} {1}{2}Desteklenmeyen karakterler{3}{2}Rezerve edilen önek{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Hedefte çoğaltmalara izin vermek için hedef sütun yeniden adlandırıldı. Bu, şu nedenlerden birinden kaynaklanıyor:{0} {1}{2}Rezerve edilen sütun adı{3}{2}Desteklenmeyen karakterler{3}{2}Rezerve edilen önek{3}{4}
#XMSG:
targetAutoDataType=Hedef veri türü değiştirildi.
#XMSG:
targetAutoDataTypeDesc=Kaynak veri türü, Google BigQuery''de desteklenmediğinden hedef veri türü, {0} olarak değiştirildi.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Kaynak veri türü hedef bağlantıda desteklenmediğinden hedef veri türü, {0} olarak değiştirildi.
#XMSG
projectionGBQUnableToCreateKey=Birincil anahtarlar oluşturulamıyor.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=Google BigQuery'de en fazla 16 birincil anahtar desteklenir. Ancak kaynak nesne daha fazla sayıda birincil anahtar içeriyor. Hedef nesnede birincil anahtarlardan hiçbiri oluşturulmayacak.
#XMSG
HDLFNoKeyError=Birincil anahtar olarak bir veya birden fazla sütun tanımlayın.
#XMSG
HDLFNoKeyErrorDescription=Bir nesneyi çoğaltmak için birincil anahtar olarak bir veya birden fazla hedef sütun tanımlamanız gerekir.
#XMSG
HDLFNoKeyErrorExistingTarget=Birincil anahtar olarak bir veya birden fazla sütun tanımlayın.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Mevcut bir hedef nesneye veri çoğaltmak için birincil anahtar olarak tanımlanmış bir veya birden fazla sütunu olması gerekir. {0} {0} Birincil anahtar olarak bir veya birden fazla sütun tanımlamak için şu seçenekleriniz vardır: {0}{0}{1} Mevcut hedef nesneyi değiştirmek için yerel tablo düzenleyicisini kullanın. Ardından çoğaltma akışını tekrar yükleyin. {0}{0}{1} Çoğaltma akışında hedef nesneyi yeniden adlandırın. Bu, çalıştırma başlatılır başlatılmaz yeni bir nesne oluşturulmasını sağlar. Yeniden adlandırma işleminden sonra projeksiyonda bir veya daha fazla sütunu birincil anahtar olarak tanımlayabilirsiniz.{0}{0}{1} Nesneyi, bir veya daha fazla sütunun zaten birincil anahtar olarak tanımlandığı mevcut başka bir hedef nesneyle eşleyin.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Birincil anahtar değiştirildi.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=Kaynak nesneyle karşılaştırıldığında, hedef nesne için birincil anahtar olarak farklı sütunlar tanımladınız. Verileri daha sonra çoğaltırken olası veri bozulmalarını önlemek için bu sütunların tüm satırları benzersiz bir şekilde tanımladığından emin olun. {0} {0} Kaynak nesnede, şu sütunlar birincil anahtar olarak tanımlanmıştır: {0} {1}
#XMSG
duplicateDPIDColumns=Hedef sütunu yeniden adlandırın.
#XMSG
duplicateDPIDDColumnsDesc1=Bu hedef sütun adı, teknik sütun için rezerve edilmiştir. Projeksiyonu kaydetmek için farklı bir ad girin.
#XMSG:
targetAutoRenameDPID=Hedef sütun yeniden adlandırıldı.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Anahtarlar olmadan ABAP kaynağından çoğaltmalara izin vermek için hedef sütun yeniden adlandırıldı. Bu, aşağıdaki nedenlerden birinden kaynaklanıyor: {0} {1}{2}Rezerve edilen sütun adı{3}{2} Desteklenmeyen karakterler{3}{2}Rezerve edilen önek{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle={0} hedef ayarları
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle={0} kaynak ayarları
#XBUT
connectionSettingSave=Kaydet
#XBUT
connectionSettingCancel=İptal
#XBUT: Button to keep the object level settings
txtKeep=Tut
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Üzerine yaz
#XFLD
targetConnectionThreadlimit=İlk yükleme için hedef iş parçacığı sınırı (1-100)
#XFLD
connectionThreadLimit=İlk yükleme için kaynak iş parçacığı sınırı (1-100)
#XFLD
maxConnection=Çoğaltma iş parçacığı sınırı (1-100)
#XFLD
kafkaNumberOfPartitions=Bölümleme sayısı
#XFLD
kafkaReplicationFactor=Çoğaltma faktörü
#XFLD
kafkaMessageEncoder=İleti kodlayıcı
#XFLD
kafkaMessageCompression=İleti sıkıştırma
#XFLD
fileGroupDeltaFilesBy=Delta gruplama ölçütü
#XFLD
fileFormat=Dosya türü
#XFLD
csvEncoding=CSV kodlama
#XFLD
abapExitLbl=ABAP çıkışı
#XFLD
deltaPartition=Delta yüklemeler için nesne iş parçacığı sayısı (1-10)
#XFLD
clamping_Data=Veri kesilmesi nedeniyle başarısızlık
#XFLD
fail_On_Incompatible=Uyumsuz veride başarısızlık
#XFLD
maxPartitionInput=Azami bölümleme sayısı
#XFLD
max_Partition=Azami bölümleme sayısını tanımla
#XFLD
include_SubFolder=Alt klasörleri dahil et
#XFLD
fileGlobalPattern=Dosya adı için genel örnek
#XFLD
fileCompression=Dosya sıkıştırması
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Dosya ayırıcı
#XFLD
fileIsHeaderIncluded=Dosya başlığı
#XFLD
fileOrient=Yön
#XFLD
gbqWriteMode=Yazma modu
#XFLD
suppressDuplicate=Yinelemeleri gizle
#XFLD
apacheSpark=Apache Spark uyumluluğunu etkinleştir
#XFLD
clampingDatatypeCb=Ondalık kayar nokta veri türlerini sabitle
#XFLD
overwriteDatasetSetting=Nesne düzeyinde hedef ayarların üzerine yaz
#XFLD
overwriteSourceDatasetSetting=Nesne düzeyinde kaynak ayarların üzerine yaz
#XMSG
kafkaInvalidConnectionSetting=Sayıyı {0} ile {1} arasında girin.
#XMSG
MinReplicationThreadErrorMsg={0} değerinden büyük bir sayı girin.
#XMSG
MaxReplicationThreadErrorMsg={0} değerinden küçük bir sayı girin.
#XMSG
DeltaThreadErrorMsg=1 ile 10 arasında bir değer girin.
#XMSG
MaxPartitionErrorMsg=1 <= x <= 2147483647 arasında değer girin. Varsayılan değer 10'dur.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal={0} ile {1} arasında bir tamsayı girin.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Aracının çoğaltma faktörünü kullan
#XFLD
serializationFormat=Serileştirme biçimi
#XFLD
compressionType=Sıkıştırma türü
#XFLD
schemaRegistry=Şema kayıt defterini kullan
#XFLD
subjectNameStrat=Konu ad stratejisi
#XFLD
compatibilityType=Uyumluluk türü
#XFLD
confluentTopicName=Konu adı
#XFLD
confluentRecordName=Kayıt adı
#XFLD
confluentSubjectNamePreview=Konu ad önizlemesi
#XMSG
serializationChangeToastMsgUpdated2=Şema kayıt defteri kayıtlı olmadığından serileştirme biçimi JSON olarak değiştirildi. Serileştirme biçimini tekrar AVRO olarak değiştirmek için öncelikle şema kayıt defterini etkinleştirmeniz gerekir.
#XBUT
confluentTopicNameInfo=Konu adı her zaman hedef nesne adını temel alır. Hedef nesneyi değiştirerek konu adını değiştirebilirsiniz.
#XMSG
emptyRecordNameValidationHeaderMsg=Kayıt adı girin.
#XMSG
emptyPartionHeader=Bölümleme sayısını girin.
#XMSG
invalidPartitionsHeader=Geçerli bir bölümleme sayısı girin.
#XMSG
invalidpartitionsDesc=1 ile 200.000 arası bir sayı girin.
#XMSG
emptyrFactorHeader=Çoğaltma faktörü girin.
#XMSG
invalidrFactorHeader=Geçerli bir çoğaltma faktörü girin.
#XMSG
invalidrFactorDesc=1 ile 32.767 arası bir sayı girin.
#XMSG
emptyRecordNameValidationDescMsg="AVRO" serileştirme biçimi kullanılırsa yalnızca şu karakterler desteklenir:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(altçizgi)
#XMSG
validRecordNameValidationHeaderMsg=Geçerli bir kayıt adı girin.
#XMSG
validRecordNameValidationDescMsgUpdated="AVRO" serileştirme biçimi kullanıldığından kayıt adı yalnızca alfasayısal (A-Z, a-z, 0-9) ve alt çizgi (_) karakterlerinden oluşmalıdır. Ad, bir harf veya alt çizgi ile başlamalıdır.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled="Delta yüklemeler için nesne iş parçacığı sayısı" bir veya daha fazla nesne "İlk ve delta" yük tipine sahip olur olmaz ayarlanabilir.
#XMSG
invalidTargetName=Geçersiz sütun adı
#XMSG
invalidTargetNameDesc=Hedef sütun adı yalnızca alfasayısal (A-Z, a-z, 0-9) ve alt çizgi (_) karakterlerinden oluşmalıdır.
#XFLD
consumeOtherSchema=Diğer şema versiyonlarını kullan
#XFLD
ignoreSchemamissmatch=Şema uyuşmazlığını yok say
#XFLD
confleuntDatatruncation=Veri kesilmesi nedeniyle başarısızlık
#XFLD
isolationLevel=İzolasyon düzeyi
#XFLD
confluentOffset=Başlangıç noktası
#XFLD
signavioGroupDeltaFilesByText=Hiçbiri
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=Hayır
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=Hayır

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Projeksiyonlar
#XBUT
txtAdd=Ekle
#XBUT
txtEdit=Düzenle
#XMSG
transformationText=Filtrelemeyi veya eşlemeyi ayarlamak için projeksiyon ekleyin.
#XMSG
primaryKeyRequiredText=Şemayı konfigüre et ile birincil anahtar seçin.
#XFLD
lblSettings=Ayarlar
#XFLD
lblTargetSetting={0}: Hedef ayarları
#XMSG
@csvRF=Klasördeki tüm dosyalara uygulamak istediğiniz şema tanımını içeren dosyayı seçin.
#XFLD
lblSourceColumns=Kaynak sütunlar
#XFLD
lblJsonStructure=JSON yapısı
#XFLD
lblSourceSetting={0}: Kaynak ayarları
#XFLD
lblSourceSchemaSetting={0}: Kaynak şeması ayarları
#XBUT
messageSettings=İleti ayarları
#XFLD
lblPropertyTitle1=Nesne özellikleri
#XFLD
lblRFPropertyTitle=Çoğaltma akışı özellikleri
#XMSG
noDataTxt=Görüntülenecek sütun yok.
#XMSG
noTargetObjectText=Hedef nesne seçilmedi.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Hedef sütunlar
#XMSG
searchColumns=Sütun ara
#XTOL
cdcColumnTooltip=Delta yakalaması sütunu
#XMSG
sourceNonDeltaSupportErrorUpdated=Kaynak nesne, delta yakalamasını desteklemiyor.
#XMSG
targetCDCColumnAdded=Delta yakalaması için 2 hedef sütun eklendi.
#XMSG
deltaPartitionEnable=Kaynak ayarlarına delta yüklemeler için nesne iş parçacığı sınırı eklendi.
#XMSG
attributeMappingRemovalTxt=Yeni hedef nesne için desteklenmeyen geçersiz eşlemeler kaldırılıyor.
#XMSG
targetCDCColumnRemoved=Delta yakalaması için kullanılan 2 hedef sütun kaldırıldı.
#XMSG
replicationLoadTypeChanged=Yükleme türü "İlk ve delta" olarak değiştirildi.
#XMSG
sourceHDLFLoadTypeError=Yükleme türünü "İlk ve delta" olarak değiştirin.
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=SAP HANA Cloud, veri gölü dosyaları bağlantı türüne sahip bir kaynak bağlantıdan SAP Datasphere'e nesne çoğaltmak için "ilk ve delta" yükleme türünü kullanmanız gerekir.
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Delta yakalamasını etkinleştirin.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=SAP HANA Cloud, veri gölü dosyaları bağlantı türüne sahip bir kaynak bağlantıdan SAP Datasphere'e bir nesne çoğaltmak için delta yakalamasını etkinleştirmeniz gerekir.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Hedef nesneyi değiştirin.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Delta yakalaması devre dışı olduğundan hedef nesne kullanılamıyor. Hedef nesneyi yeniden adlandırabilir (bu, delta yakalamasıyla yeni bir nesnenin oluşturulmasına olanak sağlar) veya bunu delta yakalamasının etkinleştirildiği mevcut bir nesneye eşleyebilirsiniz.
#XMSG
deltaPartitionError=Delta yüklemeler için geçerli bir nesne iş parçacığı sayısı girin.
#XMSG
deltaPartitionErrorDescription=1 ile 10 arasında bir değer girin.
#XMSG
deltaPartitionEmptyError=Delta yüklemeler için nesne iş parçacığı sayısı girin.
#XFLD
@lblColumnDescription=Tanım
#XMSG
@lblColumnDescriptionText1=Teknik amaçlar doğrultusunda - birincil anahtar içermeyen ABAP temelindeki kaynak nesnelerin yinelenmesi sırasında ortaya çıkan sorunlardan kaynaklanan yinelenen kayıtların işlenmesi.
#XFLD
storageType=Depolama
#XFLD
skipUnmappedColLbl=Eşlenmemiş sütunları atla
#XFLD
abapContentTypeLbl=İçerik türü
#XFLD
autoMergeForTargetLbl=Verileri otomatik olarak birleştir
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Genel
#XFLD
lblBusinessName=İş adı
#XFLD
lblTechnicalName=Teknik ad
#XFLD
lblPackage=Paket
#XFLD
statusPanel=Çalıştırma durumu
#XBTN: Schedule dropdown menu
SCHEDULE=Planlama
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Planlamayı düzenle
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Planlamayı sil
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Planlama oluştur
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Başarısız planlama doğrulama kontrolü
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Çoğaltma akışı şu anda dağıtıldığından çizelge oluşturulamıyor.{0}Çoğaltma akışı dağıtılana kadar bekleyin.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText="İlk ve delta" yükleme türüne sahip nesneler içeren çoğaltma akışları için planlama oluşturulamıyor.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated="İlk ve delta/yalnızca delta" yükleme türüne sahip nesneler içeren çoğaltma akışları için planlama oluşturulamıyor.
#XFLD : Scheduled popover
SCHEDULED=Planlandı
#XFLD
CREATE_REPLICATION_TEXT=Çoğaltma akışı oluştur
#XFLD
EDIT_REPLICATION_TEXT=Çoğaltma akışı düzenle
#XFLD
DELETE_REPLICATION_TEXT=Çoğaltma akışı sil
#XFLD
REFRESH_FREQUENCY=Sıklık
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Mevcut planlama{0}, "İlk ve delta" yükleme türünü henüz desteklemediğinden çoğaltma akışı dağıtılamıyor.{0}{0}Çoğaltma akışını dağıtmak için tüm nesnelerin{0} yükleme türlerini "Yalnızca ilk" olarak ayarlamanız gerekir. Alternatif olarak, planlamayı silebilir, {0}çoğaltma akışını dağıtabilir ve yeni bir çalıştırma başlatabilirsiniz. Bu, {0}sonu olmayan bir çalıştırmayla sonuçlanır ve "İlk ve delta" yükleme türüne sahip nesneler de desteklenir.
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Mevcut planlama{0}, "İlk ve delta/yalnızca delta" yükleme türünü henüz desteklemediğinden çoğaltma akışı dağıtılamıyor.{0}{0}Çoğaltma akışını dağıtmak için tüm nesnelerin{0} yükleme türlerini "Yalnızca ilk" olarak ayarlamanız gerekir. Alternatif olarak, planlamayı silebilir, {0}çoğaltma akışını dağıtabilir ve yeni bir çalıştırma başlatabilirsiniz. Bu, {0}sonu olmayan bir çalıştırmayla sonuçlanır ve "İlk ve delta/yalnızca delta" yükleme türüne sahip nesneler de desteklenir.
#XMSG
SCHEDULE_EXCEPTION=Planlama ayrıntıları alınamadı
#XFLD: Label for frequency column
everyLabel=Her
#XFLD: Plural Recurrence text for Hour
hoursLabel=Saat
#XFLD: Plural Recurrence text for Day
daysLabel=Gün
#XFLD: Plural Recurrence text for Month
monthsLabel=Ay
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Dakika
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Planlama olasılığı hakkında bilgi alınamadı.
#XFLD :Paused field
PAUSED=Duraklatıldı
#XMSG
navToMonitoring=Akış izlemede aç
#XFLD
statusLbl=Durum
#XFLD
lblLastRunExecuted=Son çalıştırma başlangıcı
#XFLD
lblLastExecuted=Son çalıştırma
#XFLD: Status text for Completed
statusCompleted=Tamamlandı
#XFLD: Status text for Running
statusRunning=Çalışıyor
#XFLD: Status text for Failed
statusFailed=Başarısız oldu
#XFLD: Status text for Stopped
statusStopped=Durduruldu
#XFLD: Status text for Stopping
statusStopping=Durduruluyor
#XFLD: Status text for Active
statusActive=Etkin
#XFLD: Status text for Paused
statusPaused=Duraklatıldı
#XFLD: Status text for not executed
lblNotExecuted=Henüz yürütülmedi
#XFLD
messagesSettings=İleti ayarları
#XTOL
@validateModel=Doğrulama iletileri
#XTOL
@hierarchy=Hiyerarşi
#XTOL
@columnCount=Sütun sayısı
#XMSG
VAL_PACKAGE_CHANGED=Bu nesneyi "{1}" paketine tayin ettiniz. Bu değişikliği teyit edip doğrulamak için Kaydet''e tıklayın. Kayıt işlemi gerçekleştirildikten sonra bu düzenleyicide paket tayininin geri alınamayacağını unutmayın.
#XMSG
MISSING_DEPENDENCY="{0}" nesnesinin bağlılıkları "{1}" paketinde çözümlenemiyor.
#XFLD
deltaLoadInterval=Delta yükleme aralığı
#XFLD
lblHour=Saat (0-24)
#XFLD
lblMinutes=Dakika (0-59)
#XMSG
maxHourOrMinErr=0 ile {0} arasında bir değer girin
#XMSG
maxDeltaInterval=Delta yükleme aralığının azami değeri 24 saattir.{0}Dakika veya saat değerini uygun şekilde değiştirin.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Hedef konteyner yolu
#XFLD
confluentSubjectName=Konu adı
#XFLD
confluentSchemaVersion=Şema versiyonu
#XFLD
confluentIncludeTechKeyUpdated=Teknik anahtarı dahil et
#XFLD
confluentOmitNonExpandedArrays=Genişletilmeyen dizileri atla
#XFLD
confluentExpandArrayOrMap=Diziyi veya eşlemeyi genişlet
#XCOL
confluentOperationMapping=İşlem eşleme
#XCOL
confluentOpCode=İşlem kodu
#XFLD
confluentInsertOpCode=Ekle
#XFLD
confluentUpdateOpCode=Güncelle
#XFLD
confluentDeleteOpCode=Sil
#XFLD
expandArrayOrMapNotSelectedTxt=Seçilmedi
#XFLD
confluentSwitchTxtYes=Evet
#XFLD
confluentSwitchTxtNo=Hayır
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Hata
#XTIT
executeWarning=Uyarı
#XMSG
executeunsavederror=Çalıştırmadan önce çoğaltma akışınızı kaydedin.
#XMSG
executemodifiederror=Çoğaltma akışında kaydedilmeyen değişiklikler var. Çoğaltma akışını kaydedin.
#XMSG
executeundeployederror=Çalıştırabilmek için önce çoğaltma akışınızı dağıtmanız gerekir.
#XMSG
executedeployingerror=Dağıtımın bitmesini bekleyin.
#XMSG
msgRunStarted=Çalıştırma başlatıldı
#XMSG
msgExecuteFail=Çoğaltma akışı çalıştırılamadı.
#XMSG
titleExecuteBusy=Lütfen bekleyin.
#XMSG
msgExecuteBusy=Çoğaltma akışını çalıştırmak için verilerinizi hazırlıyoruz.
#XTIT
executeConfirmDialog=Uyarı
#XMSG
msgExecuteWithValidations=Çoğaltma akışı, doğrulama hataları içeriyor. Çoğaltma akışının çalıştırılması başarısızlıkla sonuçlanabilir.
#XMSG
msgRunDeployedVersion=Dağıtılacak değişiklikler mevcut. Çoğaltma akışının son dağıtılan versiyonu çalıştırılacak. Devam etmek istiyor musunuz?
#XBUT
btnExecuteAnyway=Yine de çalıştır
#XBUT
btnExecuteClose=Kapat
#XBUT
loaderClose=Kapat
#XTIT
loaderTitle=Yükleniyor
#XMSG
loaderText=Sunucudan ayrıntılar alınıyor
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Bu ay için kullanılabilir çıkış hacmi olmadığından
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=bu SAP olmayan hedef bağlantıya çoğaltma akışı başlatılamıyor.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Bir yönetici, bu kiracı için prim çıkışı bloklarını artırarak
#XMSG
premiumOutBoundRFAdminErrMsgPart2=çıkış hacmini bu ay için kullanılabilir hale getirebilir.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Hata
#XTIT
deployInfo=Bilgi
#XMSG
deployCheckFailException=Dağıtım sırasında istisna oluştu
#XMSG
deployGBQFFDisabled=Google BigQuery'ye hedef bağlantı ile çoğaltma akışları dağıtma, bu işlev üzerinde bakım gerçekleştirdiğimizden şu anda mümkün değil.
#XMSG
deployKAFKAFFDisabled=Apache Kafka'ya hedef bağlantı ile çoğaltma akışları dağıtma, bu işlev üzerinde bakım gerçekleştirdiğimizden şu anda mümkün değil.
#XMSG
deployConfluentDisabled=Confluent Kafka'ya hedef bağlantı ile çoğaltma akışları dağıtma işlevi, bu işlev üzerinde bakım gerçekleştiriyor olmamız nedeniyle şu anda olanaklı değil.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Aşağıdaki hedef nesneler için delta yakalama tablo adları havuzdaki diğer tablolar tarafından zaten kullanılıyor: {0} Çoğaltma akışını dağıtmadan önce ilişkili delta yakalama tablosu adlarının benzersiz olmasını sağlamak için bu hedef nesneleri yeniden adlandırmanız gerekir.
#XMSG
deployDWCSourceFFDisabled=Kaynağı SAP Datasphere olan çoğaltma akışlarını dağıtma, bu işlevde bakım yürütmekte olduğumuz için şu anda olanaklı değil.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Delta uyumlu yerel tablolar içeren çoğaltma akışlarının kaynak nesne olarak dağıtılması, bu işlevde bakım yürütmekte olduğumuz için şu anda olanaklı değil.
#XMSG
deployHDLFSourceFFDisabled=SAP HANA Cloud, veri gölü dosyaları bağlantı türüne sahip kaynak bağlantıları olan çoğaltma akışlarını dağıtmak, bakım yürütmekte olduğumuz için şu anda olanaklı değil.
#XMSG
deployObjectStoreAsSourceFFDisabled=Kaynak olarak bulut depolama sağlayıcılarına sahip olan çoğaltma akışlarını dağıtmak şu anda olanaklı değil.
#XMSG
deployConfluentSourceFFDisabled=Kaynağı Confluent Kafka olan çoğaltma akışlarını dağıtma işlevi, bu işlevde bakım gerçekleştirmekte olduğumuzdan şu anda olanaklı değil.
#XMSG
deployMaxDWCNewTableCrossed=Büyük çoğaltma akışları için, bunları tek seferde "kaydetmek ve dağıtmak" mümkün değildir. Öncelikle çoğaltma akışınızı kaydedin ve ardından dağıtın.
#XMSG
deployInProgressInfo=Dağıtım zaten işleniyor.
#XMSG
deploySourceObjectInUse={1} çoğaltma akışlarında {0} kaynak nesneleri zaten kullanılıyor.
#XMSG
deployTargetSourceObjectInUse={1} çoğaltma akışlarında {0} kaynak nesneleri zaten kullanılıyor. {3} çoğaltma akışlarında {2} hedef nesneleri zaten kullanılıyor.
#XMSG
deployReplicationFlowCheckError=Çoğaltma akışı doğrulanırken hata: {0}
#XMSG
preDeployTargetObjectInUse={0} hedef nesneleri, {1} çoğaltma akışlarında zaten kullanılıyor ve iki farklı çoğaltma akışında aynı hedef nesneye sahip olamazsınız. Başka bir hedef nesne seçin ve tekrar deneyin.
#XMSG
runInProgressInfo=Çoğaltma akışı zaten devam ediyor.
#XMSG
deploySignavioTargetFFDisabled=Hedefi SAP Signavio olan çoğaltma akışlarını dağıtma, bu işlevde bakım yürütmekte olduğumuz için şu anda olanaklı değil.
#XMSG
deployHanaViewAsSourceFFDisabled=Seçilen kaynak bağlantısı için kaynak nesneler olarak görünümlere sahip çoğaltma akışlarını dağıtma şu anda mümkün değil. Daha sonra tekrar deneyin.
#XMSG
deployMsOneLakeTargetFFDisabled=Hedefi MS OneLake olan çoğaltma akışlarını dağıtma, bu işlevde bakım yürütmekte olduğumuz için şu anda olanaklı değil.
#XMSG
deploySFTPTargetFFDisabled=Hedefi SFTP olan çoğaltma akışlarını dağıtma, bu işlevde bakım yürütmekte olduğumuz için şu anda olanaklı değil.
#XMSG
deploySFTPSourceFFDisabled=Kaynağı SFTP olan çoğaltma akışlarını dağıtma, bu işlevde bakım yürütmekte olduğumuz için şu anda olanaklı değil.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Teknik ad
#XFLD
businessNameInRenameTarget=İş adı
#XTOL
renametargetDialogTitle=Hedef nesneyi yeniden adlandır
#XBUT
targetRenameButton=Yeniden adlandır
#XBUT
targetRenameCancel=İptal
#XMSG
mandatoryTargetName=Ad girmeniz gerekir.
#XMSG
dwcSpecialChar=_ (altçizgi), izin verilen tek özel karakterdir.
#XMSG
dwcWithDot=Hedef tablo adı Latin harfleri, rakam, alt çizgi (_) ve nokta (.) içerebilir. İlk karakter harf, rakam veya alt çizgi (nokta değil) olmalıdır.
#XMSG
nonDwcSpecialChar=İzin verilen özel karakterler şunlardır: _ (altçizgi) - (tire) . (nokta)
#XMSG
firstUnderscorePattern=Ad _ (altçizgi) ile başlamamalıdır

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: Tablo oluştur SQL deyimini görüntüle
#XMSG
sqlDialogMaxPKWarning=Google BigQuery'de azami 16 birincil anahtar destekleniyor ve kaynak nesne, daha büyük bir sayıya sahip. Bu nedenle, bu deyimde birincil anahtar tanımlanmadı.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Bir veya birden fazla kaynak sütun, Google BigQuery'de birincil anahtar olarak tanımlanamayacak veri türlerine sahip. Bu nedenle, bu durumda birincil anahtar tanımlanmıyor. Google BigQuery'de yalnızca aşağıdaki veri türleri, birincil anahtara sahip olabilir: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Kopyala ve kapat
#XBUT
closeDDL=Kapat
#XMSG
copiedToClipboard=Panoya kopyalandı


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Nesne ''{0}'', bitişi olmadığından (yükleme türü "İlk ve delta/yalnızca delta" olan nesneler içerdiğinden) görev zincirinin parçası olamaz.
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Nesne ''{0}'', bitişi olmadığından (yükleme türü İlk ve delta olan nesneler içerdiğinden) görev zincirinin parçası olamaz.
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE="{0}" nesnesi, görev zincirine eklenemiyor.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Kaydedilmeyen hedef nesneler bulunuyor. Lütfen tekrar kaydedin.{0}{0} Bu özelliğin davranışı değiştirildi: Geçmişte, hedef nesneler yalnızca çoğaltma akışı dağıtıldığında hedef ortamda oluşturulurdu.{0} Artık, nesneler çoğaltma akışı kaydedildiğinde halihazırda oluşturulmuş olur. Çoğaltma akışınız bu değişiklikten önce oluşturuldu ve yeni nesneler içeriyor.{0} Yeni nesnelerin doğru şekilde dahil edilebilmesi için dağıtımdan önce çoğaltma akışını bir kez daha kaydetmeniz gerekir.
#XMSG
confirmChangeContentTypeMessage=İçerik türünü değiştirmek üzeresiniz. Bunu yaparsanız tüm mevcut projeksiyonlar silinecek.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Konu adı
#XFLD
schemaDialogVersionName=Şema versiyonu
#XFLD
includeTechKey=Teknik anahtarı dahil et
#XFLD
segementButtonFlat=Düz
#XFLD
segementButtonNested=İç içe
#XMSG
subjectNamePlaceholder=Konu adı ara

#XMSG
@EmailNotificationSuccess=Çalıştırma süresi e-posta bildirimlerinin konfigürasyonu kaydedildi.

#XFLD
@RuntimeEmailNotification=Çalıştırma süresi e-posta bildirimi

#XBTN
@TXT_SAVE=Kaydet


