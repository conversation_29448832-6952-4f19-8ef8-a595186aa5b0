#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Flusso di replicazione

#XFLD: Edit Schema button text
editSchema=Modifica schema

#XTIT : Properties heading
configSchema=Configura schema

#XFLD: save changed button text
applyChanges=Applica modifiche


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Seleziona connessione di origine
#XFLD
sourceContainernEmptyText=Seleziona contenitore
#XFLD
targetConnectionEmptyText=Seleziona connessione di destinazione
#XFLD
targetContainernEmptyText=Seleziona contenitore
#XFLD
sourceSelectObjectText=Seleziona oggetto di origine
#XFLD
sourceObjectCount=Oggetti di origine ({0})
#XFLD
targetObjectText=Oggetti di destinazione
#XFLD
confluentBrowseContext=Seleziona contesto
#XBUT
@retry=Riprova
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Upgrade del tenant in corso.

#XTOL
browseSourceConnection=Sfoglia connessione di origine
#XTOL
browseTargetConnection=Sfoglia connessione di destinazione
#XTOL
browseSourceContainer=Sfoglia contenitore di origine
#XTOL
browseAndAddSourceDataset=Aggiungi oggetti di origine
#XTOL
browseTargetContainer=Sfoglia contenitore di destinazione
#XTOL
browseTargetSetting=Sfoglia impostazioni di destinazione
#XTOL
browseSourceSetting=Sfoglia impostazioni di origine
#XTOL
sourceDatasetInfo=Informazioni
#XTOL
sourceDatasetRemove=Rimuovi
#XTOL
mappingCount=Questo rappresenta il numero totale di mappature/espressioni non basate su nomi.
#XTOL
filterCount=Questo rappresenta il numero totale di condizioni di filtro.
#XTOL
loading=Caricamento in corso...
#XCOL
deltaCapture=Acquisizione delta
#XCOL
deltaCaptureTableName=Tabella di acquisizione delta
#XCOL
loadType=Tipo di caricamento
#XCOL
deleteAllBeforeLoading=Elimina tutto prima di caricare
#XCOL
transformationsTab=Proiezioni
#XCOL
settingsTab=Impostazioni

#XBUT
renameTargetObjectBtn=Rinomina oggetto di destinazione
#XBUT
mapToExistingTargetObjectBtn=Mappa a oggetto di destinazione esistente
#XBUT
changeContainerPathBtn=Modifica percorso contenitore
#XBUT
viewSQLDDLUpdated=Visualizza istruzione tabella di creazione SQL
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=L'oggetto di origine non supporta l'acquisizione delta ma per l'oggetto di destinazione selezionato è attivata l'opzione di acquisizione delta.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=L''oggetto di destinazione non può essere utilizzato perché l''acquisizione delta è abilitata,{0}mentre l''oggetto di origine non supporta l''acquisizione delta.{1}È possibile selezionare un altro oggetto di destinazione che non supporta l''acquisizione delta.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Un oggetto di destinazione con questo nome esiste già. Tuttavia, non può essere utilizzato{0}perché l''acquisizione delta è abilitata, mentre l''oggetto di origine non{0}supporta l''acquisizione delta.{1}È possibile immettere il nome di un oggetto di destinazione esistente che non{0}supporta l''acquisizione delta oppure un nome che non esiste ancora.
#XBUT
copySQLDDLUpdated=Copia istruzione tabella di creazione SQL
#XMSG
targetObjExistingNoCDCColumnUpdated=Le tabelle esistenti in Google BigQuery devono includere le seguenti colonne per l''acquisizione dati di modifica (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=I seguenti oggetti di origine non sono supportati perché non dispongono di una chiave primaria oppure stanno utilizzando una connessione che non soddisfa le condizioni per il recupero della chiave primaria:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Fare riferimento a SAP KBA 3531135 per una possibile soluzione.
#XLST: load type list values
initial=Solo iniziale
@emailUpdateError=Errore di aggiornamento dell'elenco di notifiche di posta elettronica

#XLST
initialDelta=Iniziale e delta

#XLST
deltaOnly=Solo delta
#XMSG
confluentDeltaLoadTypeInfo=Per l'origine Confluent Kafka è supportato solo il tipo di caricamento Iniziale e delta.
#XMSG
confirmRemoveReplicationObject=Confermare l'eliminazione della replicazione?
#XMSG
confirmRemoveReplicationTaskPrompt=Questa azione eliminerà le replicazioni esistenti. Continuare?
#XMSG
confirmTargetConnectionChangePrompt=Questa azione resetterà la connessione di destinazione e il contenitore di destinazione, ed eliminerà tutti gli oggetti di destinazione. Continuare?
#XMSG
confirmTargetContainerChangePrompt=Questa azione resetterà il contenitore di destinazione ed eliminerà tutti gli oggetti di destinazione. Continuare?
#XMSG
confirmRemoveTransformObject=Confermare l''eliminazione della proiezione {0}?
#XMSG
ErrorMsgContainerChange=Si è verificato un errore durante la modifica del percorso del contenitore.
#XMSG
infoForUnsupportedDatasetNoKeys=I seguenti oggetti di origine non sono supportati perché non dispongono di una chiave primaria:
#XMSG
infoForUnsupportedDatasetView=I seguenti oggetti di origine di tipo Viste non sono supportati:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Il seguente oggetto di origine non è supportato perché si tratta di una vista SQL contenente parametri di input:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=I seguenti oggetti di origine non sono supportati perché per essi è disattivata l'estrazione:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Per le connessioni Confluent, gli unici formati di serializzazione consentiti sono AVRO e JSON. I seguenti oggetti non sono supportati perché utilizzano un formato di serializzazione diverso:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Non è stato possibile richiamare lo schema per i seguenti oggetti. Selezionare il contesto appropriato o verificare la configurazione del registro schemi:
#XTOL: warning dialog header on deleting replication task
deleteHeader=Elimina
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=L'impostazione Elimina tutto prima di caricare non è supportata per Google BigQuery.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=L'impostazione Elimina tutto prima di caricare elimina e ricrea l'oggetto (argomento) prima di ogni replicazione ed elimina inoltre tutti i messaggi assegnati.
#XTOL
DeleteAllBeforeLoadingLTFInfo=L'impostazione Elimina tutto prima di caricare non è supportata per questo tipo di destinazione.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Nome tecnico
#XCOL
connBusinessName=Nome aziendale
#XCOL
connDescriptionName=Descrizione
#XCOL
connType=Tipo
#XMSG
connTblNoDataFoundtxt=Nessuna connessione trovata
#XMSG
connectionError=Si è verificato un errore durante il richiamo delle connessioni.
#XMSG
connectionCombinationUnsupportedErrorTitle=Combinazione di connessioni non supportata
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=La replicazione da {0} a {1} non è attualmente supportata.
#XMSG
invalidTargetforSourceHDLFErrorTitle=La combinazione del tipo di connessione non è supportata
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=La replicazione da una connessione con il tipo di connessione SAP HANA Cloud, file data lake in {0} non è supportata. È possibile replicare solo in SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Seleziona
#XBUT
containerCancelBtn=Annulla
#XTOL
containerSelectTooltip=Seleziona
#XTOL
containerCancelTooltip=Annulla
#XMSG
containerContainerPathPlcHold=Percorso contenitore
#XFLD
containerContainertxt=Contenitore
#XFLD
confluentContainerContainertxt=Contesto
#XMSG
infoMessageForSLTSelection=Solo /SLT/ID trasferimento di massa è consentito come contenitore. Selezionare un ID trasferimento di massa in SLT (se disponibile) e fare clic su Invia.
#XMSG
msgFetchContainerFail=Si è verificato un errore durante il richiamo dei dati del contenitore.
#XMSG
infoMessageForSLTHidden=Questa connessione non supporta le cartelle SLT, che quindi non compaiono nell'elenco sottostante.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Selezionare un contenitore che comprenda al suo interno sottocartelle.
#XMSG
sftpIncludeSubFolderText=False
#XMSG
sftpIncludeSubFolderTextNew=No

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Ancora nessuna mappatura di filtri)
#XMSG
failToFetchRemoteMetadata=Si è verificato un errore durante il richiamo dei metadati.
#XMSG
failToFetchData=Si è verificato un errore durante il richiamo della destinazione esistente.
#XCOL
@loadType=Tipo di caricamento
#XCOL
@deleteAllBeforeLoading=Elimina tutto prima di caricare

#XMSG
@loading=Caricamento in corso...
#XFLD
@selectSourceObjects=Seleziona oggetti di origine
#XMSG
@exceedLimit=Impossibile importare contemporaneamente più di {0} oggetti. Deselezionare almeno {1} oggetti.
#XFLD
@objects=Oggetti
#XBUT
@ok=OK
#XBUT
@cancel=Annulla
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Avanti
#XBUT
btnAddSelection=Aggiungi selezione
#XTOL
@remoteFromSelection=Rimuovi da selezione
#XMSG
@searchInForSearchField=Cerca in {0}

#XCOL
@name=Nome tecnico
#XCOL
@type=Tipo
#XCOL
@location=Posizione
#XCOL
@label=Nome aziendale
#XCOL
@status=Stato

#XFLD
@searchIn=Cerca in:
#XBUT
@available=Disponibile
#XBUT
@selection=Selezione

#XFLD
@noSourceSubFolder=Tabelle e viste
#XMSG
@alreadyAdded=Già presente nel diagramma
#XMSG
@askForFilter=Sono presenti più di {0} elementi. Immettere una stringa di filtro per limitare il numero di elementi.
#XFLD: success label
lblSuccess=Operazione riuscita
#XFLD: ready label
lblReady=Pronto
#XFLD: failure label
lblFailed=Operazione non riuscita
#XFLD: fetching status label
lblFetchingDetail=Richiamo dei dettagli

#XMSG Place holder text for tree filter control
filterPlaceHolder=Digitare testo per filtrare gli oggetti di livello superiore
#XMSG Place holder text for server search control
serverSearchPlaceholder=Digitare e premere Invio per avviare la ricerca
#XMSG
@deployObjects=Importazione oggetti ({0}) in corso...
#XMSG
@deployObjectsStatus=Numero di oggetti importati: {0}. Numero di oggetti non importati: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Apertura del browser del repository locale non riuscita.
#XMSG
@openRemoteSourceBrowserError=Richiamo degli oggetti di origine non riuscito.
#XMSG
@openRemoteTargetBrowserError=Richiamo degli oggetti di destinazione non riuscito.
#XMSG
@validatingTargetsError=Si è verificato un errore durante la convalida delle destinazioni.
#XMSG
@waitingToImport=Pronto per importazione

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=È stato superato il numero massimo di oggetti. Selezionare un massimo di 500 oggetti per un flusso di replicazione.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Nome tecnico
#XFLD
sourceObjectBusinessName=Nome aziendale
#XFLD
sourceNoColumns=Numero di colonne
#XFLD
containerLbl=Contenitore

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=È necessario selezionare una connessione di origine per il flusso di replicazione.
#XMSG
validationSourceContainerNonExist=È necessario selezionare un contenitore per la connessione di origine.
#XMSG
validationTargetNonExist=È necessario selezionare una connessione di destinazione per il flusso di replicazione.
#XMSG
validationTargetContainerNonExist=È necessario selezionare un contenitore per la connessione di destinazione.
#XMSG
validationTruncateDisabledForObjectTitle=Replicazione nelle archiviazioni oggetti.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=La replicazione in un archivio cloud è possibile solo se è impostata l''opzione Elimina tutto prima di caricare o se l''oggetto di destinazione non esiste nella destinazione.{0}{0} Per consentire comunque la replicazione di oggetti per cui l''opzione Elimina tutto prima di caricare non è impostata, assicurarsi che l''oggetto di destinazione non esista nel sistema prima di eseguire il flusso di replicazione.
#XMSG
validationTaskNonExist=È necessaria almeno una replicazione nel flusso di replicazione.
#XMSG
validationTaskTargetMissing=È necessaria almeno una destinazione per la replicazione con l''origine: {0}.
#XMSG
validationTaskTargetIsSAC=La destinazione selezionata è un artefatto SAC: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=La destinazione selezionata non è una tabella locale supportata: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Nella destinazione esiste già un oggetto con questo nome. Tuttavia, esso non può essere utilizzato come oggetto di destinazione per un flusso di replicazione al repository locale, poiché non è una tabella locale.
#XMSG
validateSourceTargetSystemDifference=È necessario selezionare diverse combinazioni di connessione di origine e destinazione e contenitore per il flusso di replicazione.
#XMSG
validateDuplicateSources=una o più replicazioni presentano nomi oggetto di origine duplicati: {0}.
#XMSG
validateDuplicateTargets=una o più replicazioni presentano nomi oggetto di destinazione duplicati: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=L''oggetto di origine {0} non supporta l''acquisizione delta, mentre l''oggetto di destinazione {1} sì. È necessario rimuovere la replicazione.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=È necessario selezionare il tipo di caricamento "Iniziale e delta" per la replicazione con nome oggetto di destinazione {0}.
#XMSG
validationAutoRenameTarget=Le colonne di destinazione sono state rinominate.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=È stata aggiunta una proiezione automatica e le seguenti colonne di destinazione sono state rinominate per consentire la replicazione nella destinazione:{1}{1} {0} {1}{1}Questo è dovuto a uno dei seguenti motivi:{1}{1}{2} Caratteri non supportati{1}{2} Prefisso riservato
#XMSG
validationAutoRenameTargetDescriptionUpdated=È stata aggiunta una proiezione automatica e le seguenti colonne di destinazione sono state rinominate per consentire la replicazione in Google BigQuery:{1}{1} {0} {1}{1}Questo è dovuto a uno dei seguenti motivi:{1}{1}{2} Nome colonna riservato{1}{2} Caratteri non supportati{1}{2} Prefisso riservato
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=È stata aggiunta una proiezione automatica e le seguenti colonne di destinazione sono state rinominate per consentire le replicazioni in Confluent:{1}{1} {0} {1}{1}Questo è dovuto a uno dei seguenti motivi:{1}{1}{2} Nome colonna riservato{1}{2} Caratteri non supportati{1}{2} Prefisso riservato
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=È stata aggiunta una proiezione automatica e le seguenti colonne di destinazione sono state rinominate per consentire le replicazioni nella destinazione:{1}{1} {0} {1}{1}Questo è dovuto a uno dei seguenti motivi:{1}{1}{2} Nome colonna riservato{1}{2} Caratteri non supportati{1}{2} Prefisso riservato
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=L'oggetto di destinazione è stato rinominato.
#XMSG
autoRenameInfoDesc=L''oggetto di destinazione è stato rinominato perché conteneva caratteri non supportati. Sono supportati solo i seguenti caratteri:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(punto){0}{1}_(trattino basso){0}{1}-(trattino)
#XMSG
validationAutoTargetTypeConversion=I tipi di dati di destinazione sono stati modificati.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Per le seguenti colonne di destinazione, è stato modificato il tipo di dati di destinazione, perché in Google BigQuery il tipo di dati di origine non è supportato:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Per le seguenti colonne di destinazione sono stati modificati i tipi di dati di destinazione, perché essi non sono supportati nella connessione di destinazione:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Abbreviare i nomi delle colonne di destinazione.
#XMSG
validationMaxCharLengthGBQTargetDescription=In Google BigQuery, i nomi delle colonne devono utilizzare al massimo 300 caratteri. Utilizzare una proiezione per abbreviare i seguenti nomi di colonna di destinazione:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Le chiavi primarie non verranno create.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=In Google BigQuery, è supportato un massimo di 16 chiavi primarie, ma l'oggetto di origine ne contiene un numero maggiore. Nessuna delle chiavi primarie verrà creata nell'oggetto di destinazione.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Una o più colonne di origine presentano tipi di dati che non possono essere definiti come chiavi primarie in Google BigQuery. Nessuna delle chiavi primarie verrà creata nell''oggetto di destinazione.{0}{0}I seguenti tipi di dati di destinazione sono compatibili con i tipi di dati Google BigQuery per cui è possibile definire una chiave primaria:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Definire una o più colonne come chiave primaria.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=È necessario definire una o più colonne come chiave primaria. A tal scopo, utilizzare la finestra di dialogo dello schema di origine.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Definire una o più colonne come chiave primaria.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=È necessario definire una o più colonne come chiave primaria che corrispondano ai vincoli di chiave primaria dell'oggetto di origine. Per farlo, passare a Configura schema nelle proprietà dell'oggetto di origine.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Immettere un valore partizione massimo valido.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Il valore di partizione massimo deve essere ≥ 1 e ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Definire una o più colonne come chiave primaria.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Per replicare un oggetto, è necessario definire una o più colonne di destinazione come chiave primaria. A tale fine, utilizzare una proiezione.
#XMSG
validateHDLFNoPKExistingDatasetError=Definire una o più colonne come chiave primaria.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Per replicarvi i dati, un oggetto di destinazione esistente deve avere una o più colonne definite come chiave primaria. {0} Per la definizione di una o più colonne come chiave primaria, sono disponbili le opzioni seguenti: {0} {1} Utilizzare l''editor tabelle locale per modificare l''oggetto di destinazione esistente; ricaricare quindi il flusso di replicazione.{0}{1} Rinominare l''oggetto di destinazione nel flusso di replicazione, per creare un nuovo oggetto non appena viene avviata un''esecuzione. Dopo la ridenominazione, è possibile definire una o più colonne come chiave primaria in una proiezione.{0}{1} Mappare l''oggetto a un altro oggetto di destinazione esistente in cui sono già definite una o più colonne come chiave primaria.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Destinazione selezionata già esistente nel repository: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=I nomi della tabella di acquisizione delta sono già utilizzati da altre tabelle nel repository: {0}. È necessario rinominare questi oggetti di destinazione per assicurarsi che i nomi della tabella di acquisizione delta siano univoci prima di poter salvare il flusso di replicazione.
#XMSG
validateConfluentEmptySchema=Definisci schema
#XMSG
validateConfluentEmptySchemaDescUpdated=La tabella di origine non ha uno schema. Scegliere Configura schema per definirne uno
#XMSG
validationCSVEncoding=Codifica CSV non valida
#XMSG
validationCSVEncodingDescription=Codifica CSV del task non valida.
#XMSG
validateConfluentEmptySchema=Selezionare un tipo di dati di destinazione compatibile
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Selezionare un tipo di dati di destinazione compatibile
#XMSG
globalValidateTargetDataTypeDesc=Si è verificato un errore con le mappature delle colonne. Passare a Proiezione e accertarsi che tutte le colonne siano mappate a una colonna univoca, a una colonna dal tipo di dati compatibile, e che tutte le espressioni definite siano valide.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Nomi colonna duplicati.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=I nomi colonna duplicati non sono supportati. Utilizzare la finestra di dialogo Proiezione per correggerli. I seguenti oggetti di destinazione hanno nomi colonna duplicati: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Nomi colonna duplicati.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=I nomi colonna duplicati non sono supportati. I seguenti oggetti di destinazione hanno nomi colonna duplicati: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Potrebbero essere presenti incoerenze nei dati.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Il tipo di caricamento Solo delta non considererà le modifiche apportate nell'origine tra l'ultimo salvataggio e l'esecuzione successiva.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Modifica tipo di caricamento in "Iniziale".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=La replicazione di oggetti basati su ABAP senza chiave primaria è possibile solo per il tipo di caricamento "Solo iniziale".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Disattivare acquisizione delta.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Per duplicare un oggetto senza chiave primaria che utilizza il tipo di connessione di origine ABAP, è necessario prima disattivare l'acquisizione delta per questa tabella.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Modificare l'oggetto di destinazione.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=L'oggetto di destinazione non può essere utilizzato perché l'acquisizione delta è attivata. È possibile rinominare l'oggetto di destinazione e successivamente disattivare l'acquisizione delta per il nuovo oggetto (rinominato) o mappare l'oggetto di origine a un oggetto di destinazione per cui è disattiva l'acquisizione delta.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Modificare l'oggetto di destinazione.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Non è possibile utilizzare l'oggetto di destinazione perché non dispone della colonna tecnica richiesta __load_package_id. È possibile rinominare l'oggetto di destinazione utilizzando un nome non ancora esistente. Il sistema creerà quindi un nuovo oggetto con la stessa definizione dell'oggetto di origine e contenente la colonna tecnica. In alternativa, è possibile mappare l'oggetto di destinazione a un oggetto esistente che dispone della colonna tecnica richiesta (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Modificare l'oggetto di destinazione.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Non è possibile utilizzare l'oggetto di destinazione perché non dispone della colonna tecnica richiesta __load_record_id. È possibile rinominare l'oggetto di destinazione utilizzando un nome non ancora esistente. Il sistema creerà quindi un nuovo oggetto con la stessa definizione dell'oggetto di origine e contenente la colonna tecnica. In alternativa, è possibile mappare l'oggetto di destinazione a un oggetto esistente che dispone della colonna tecnica richiesta (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Modificare l'oggetto di destinazione.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Non è possibile utilizzare l'oggetto di destinazione perché il tipo di dati della colonna tecnica __load_record_id non è "string(44)". È possibile rinominare l'oggetto di destinazione utilizzando un nome non ancora esistente. Il sistema creerà quindi un nuovo oggetto con la stessa definizione dell'oggetto di origine e, di conseguenza, il corretto tipo di dati. In alternativa, è possibile mappare l'oggetto di destinazione a un oggetto esistente che dispone della colonna tecnica richiesta (__load_record_id) con il corretto tipo di dati.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Modificare l'oggetto di destinazione.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Non è possibile utilizzare l'oggetto di destinazione perché dispone di una chiave primaria, a differenza dell'oggetto di origine. È possibile rinominare l'oggetto di destinazione utilizzando un nome non ancora esistente. Il sistema creerà quindi un nuovo oggetto con la stessa definizione dell'oggetto di origine e, di conseguenza, senza chiave primaria. In alternativa, è possibile mappare l'oggetto di destinazione a un oggetto esistente che dispone della colonna tecnica richiesta (__load_package_id) e che non ha una chiave primaria.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Modificare l'oggetto di destinazione.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Non è possibile utilizzare l'oggetto di destinazione perché dispone di una chiave primaria, a differenza dell'oggetto di origine. È possibile rinominare l'oggetto di destinazione utilizzando un nome non ancora esistente. Il sistema creerà quindi un nuovo oggetto con la stessa definizione dell'oggetto di origine e, di conseguenza, senza chiave primaria. In alternativa, è possibile mappare l'oggetto di destinazione a un oggetto esistente che dispone della colonna tecnica richiesta (__load_record_id) e che non ha una chiave primaria.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Modificare l'oggetto di destinazione.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Non è possibile utilizzare l'oggetto di destinazione perché il tipo di dati della colonna tecnica __load_package_id non è "binary(>=256)". È possibile rinominare l'oggetto di destinazione utilizzando un nome non ancora esistente. Il sistema creerà quindi un nuovo oggetto con la stessa definizione dell'oggetto di origine e, di conseguenza, il corretto tipo di dati. In alternativa, è possibile mappare l'oggetto di destinazione a un oggetto esistente che dispone della colonna tecnica richiesta (__load_package_id) con il corretto tipo di dati.
#XMSG
validationAutoRenameTargetDPID=Le colonne di destinazione sono state rinominate.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Rimuovere l'oggetto di origine.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=L'oggetto di origine non ha una colonna chiave, il che non è supportato in questo contesto.
#XMSG
validationAutoRenameTargetDPIDDescription=È stata aggiunta una proiezione automatica e le seguenti colonne di destinazione sono state rinominate per consentire la replicazione dall''origine ABAP senza chiavi:{1}{1} {0} {1}{1}Questo è dovuto a uno dei seguenti motivi:{1}{1}{2} Nome colonna riservato{1}{2} Caratteri non supportati{1}{2} Prefisso riservato
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Replicazione in {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Non è attualmente possibile distribuire e salvare i flussi di replicazione la cui destinazione è {0} perché su questa funzione è in corso la manutenzione.
#XMSG
TargetColumnSkippedLTF=La colonna di destinazione è stata saltata.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=La colonna di destinazione è stata saltata a causa di un tipo di dati non supportato. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Colonna Periodo temporale come chiave primaria.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=L'oggetto di origine ha una colonna Periodo temporale come chiave primaria, il che non è supportato in questo contesto.
#XMSG
validateNoPKInLTFTarget=Chiave primaria mancante.
#XMSG
validateNoPKInLTFTargetDescription=La chiave primaria non è definita nella destinazione, il che non è supportato in questo contesto.
#XMSG
validateABAPClusterTableLTF=Tabella cluster ABAP.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=L'oggetto di origine è una tabella cluster ABAP, il che non è supportato in questo contesto.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Sembra che non siano stati ancora aggiunti dati.
#YINS
welcomeText2=Per avviare il flusso di replicazione, seleziona una connessione e un oggetto di origine sul lato sinistro.

#XBUT
wizStep1=Seleziona connessione di origine
#XBUT
wizStep2=Seleziona contenitore di origine
#XBUT
wizStep3=Aggiungi oggetti di origine

#XMSG
limitDataset=È stato raggiunto il numero massimo di oggetti. Rimuovere gli oggetti esistenti per aggiungerne di nuovi o creare un nuovo flusso di replicazione.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Impossibile avviare il flusso di replicazione verso questa connessione di destinazione non SAP perché non è disponibile per questo mese volume in uscita.
#XMSG
premiumOutBoundRFAdminWarningMsg=Un amministratore può aumentare i blocchi in uscita premium per questo tenant, rendendo disponibile volume per questo mese.
#XMSG
messageForToastForDPIDColumn2=Nuova colonna aggiunta alla destinazione per {0} oggetti: necessaria per la gestione di record duplicati in connessione a oggetti di origine basati su ABAP senza chiave primaria.
#XMSG
PremiumInboundWarningMessage=A seconda del numero di flussi di replicazione e del volume di dati da replicare, le risorse SAP HANA {0}richieste per la replicazione dei dati tramite {1} potrebbero superare la capacità disponibile per il tenant.
#XMSG
PremiumInboundWarningMsg=A seconda del numero di flussi di replicazione e del volume di dati da replicare,{0}le risorse SAP HANA richieste per la replicazione dei dati tramite "{1}" potrebbero superare la capacità disponibile per il tenant.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Immettere un nome progetto.
#XMSG
emptyTargetColumn=Immettere un nome colonna di destinazione.
#XMSG
emptyTargetColumnBusinessName=Immettere un nome aziendale colonna di destinazione.
#XMSG
invalidTransformName=Immettere un nome progetto.
#XMSG
uniqueColumnName=Rinominare colonna di destinazione.
#XMSG
copySourceColumnLbl=Copiare le colonne dall'oggetto di origine
#XMSG
renameWarning=Assicurarsi di scegliere un nome univoco quando si rinomina la tabella di destinazione. Se la tabella con il nuovo nome esiste già nello spazio, verrà utilizzata la definizione di quella tabella.

#XMSG
uniqueColumnBusinessName=Modificare il nome aziendale della colonna di destinazione.
#XMSG
uniqueSourceMapping=Selezionare una colonna di origine diversa.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=La colonna di origine {0} è già utilizzata dalle seguenti colonne di destinazione:{1}{1}{2}{1}{1} Per questa o per altre colonne di destinazione, selezionare una colonna di origine che non sia già in uso per salvare la proiezione.
#XMSG
uniqueColumnNameDescription=Il nome colonna di destinazione immesso esiste già. Per poter salvare la proiezione, è necessario immettere un nome colonna univoco.
#XMSG
uniqueColumnBusinessNameDesc=Il nome aziendale della colonna di destinazione esiste già. Per salvare la proiezione, è necessario immettere un nome aziendale univoco per la colonna.
#XMSG
emptySource=Selezionare una colonna di origine o immettere una costante.
#XMSG
emptySourceDescription=Per creare un inserimento mappatura valido, è necessario selezionare una colonna di origine o immettere un valore costante.
#XMSG
emptyExpression=Definire mappatura.
#XMSG
emptyExpressionDescription1=Selezionare la colonna di origine a cui mappare la colonna di destinazione o selezionare la casella di controllo nella colonna {0} Funzioni/Costanti {1}. {2} {2} Le funzioni vengono immesse automaticamente in base al tipo di dati di destinazione. I valore costante possono essere immessi manualmente.
#XMSG
numberExpressionErr=Immettere un numero.
#XMSG
numberExpressionErrDescription=È stato selezionato un tipo di dati numerico, pertanto è possibile immettere solo valori numerici oltre al separatore decimale, se applicabile. Non utilizzare virgolette singole.
#XMSG
invalidLength=Immettere un valore lunghezza valido.
#XMSG
invalidLengthDescription=La lunghezza del tipo di dati deve essere uguale o superiore alla lunghezza della colonna di origine e può essere compresa tra 1 e 5000.
#XMSG
invalidMappedLength=Immettere un valore lunghezza valido.
#XMSG
invalidMappedLengthDescription=La lunghezza del tipo di dati deve essere uguale o superiore alla lunghezza della colonna di origine {0} e può essere compresa tra 1 e 5000.
#XMSG
invalidPrecision=Immettere un valore precisione valido.
#XMSG
invalidPrecisionDescription=La precisione definisce il numero totale di cifre; la scala definisce il numero di cifre dopo il separatore decimale e può essere compresa tra 0 e la precisione.{0}{0} Esempi: {0}{1} Precisione 6, scala 2 corrisponde a numeri come 1234,56.{0}{1} Precisione 6, scala 6 corrisponde a numeri come 0,123546.{0} {0} Precisione e scala della destinazione devono essere compatibili con precisione e scala dell''origine, in modo che tutte le cifre dell''origine siano compatibili con i campi di destinazione. Ad esempio, se nell''origine la precisione è 6 e la scala 2 (e di conseguenza cifre diverse da 0 prima del separatore dei decimali) non è possibile avere precisione 6 e scala 6 nella destinazione.
#XMSG
invalidPrimaryKey=Immettere almeno una chiave primaria.
#XMSG
invalidPrimaryKeyDescription=Chiave primaria non definita per questo schema.
#XMSG
invalidMappedPrecision=Immettere un valore precisione valido.
#XMSG
invalidMappedPrecisionDescription1=La precisione definisce il numero totale di cifre; la scala definisce il numero di cifre dopo il separatore decimale e può essere compresa tra 0 e la precisione.{0}{0} Esempi:{0}{1} Precisione 6, scala 2 corrisponde a numeri come 1234,56.{0}{1} Precisione 6, scala 6 corrisponde a numeri come 0,123546.{0}{0}La precisione del tipo di dati deve essere uguale o superiore alla precisione dell''origine ({2}).
#XMSG
invalidScale=Immettere un valore scala valido.
#XMSG
invalidScaleDescription=La precisione definisce il numero totale di cifre; la scala definisce il numero di cifre dopo il separatore decimale e può essere compresa tra 0 e la precisione.{0}{0} Esempi: {0}{1} Precisione 6, scala 2 corrisponde a numeri come 1234,56.{0}{1} Precisione 6, scala 6 corrisponde a numeri come 0,123546.{0} {0} Precisione e scala della destinazione devono essere compatibili con precisione e scala dell''origine, in modo che tutte le cifre dell''origine siano compatibili con i campi di destinazione. Ad esempio, se nell''origine la precisione è 6 e la scala 2 (e di conseguenza cifre diverse da 0 prima del separatore dei decimali) non è possibile avere precisione 6 e scala 6 nella destinazione.
#XMSG
invalidMappedScale=Immettere un valore scala valido.
#XMSG
invalidMappedScaleDescription1=La precisione definisce il numero totale di cifre; la scala definisce il numero di cifre dopo il separatore decimale e può essere compresa tra 0 e la precisione.{0}{0} Esempi:{0}{1} Precisione 6, scala 2 corrisponde a numeri come 1234,56.{0}{1} Precisione 6, scala 6 corrisponde a numeri come 0,123546.{0}{0}La scala del tipo di dati deve essere uguale o superiore alla scala dell''origine ({2}).
#XMSG
nonCompatibleDataType=Selezionare un tipo di dati di destinazione compatibile.
#XMSG
nonCompatibleDataTypeDescription1=Il tipo di dati specificato qui deve essere compatibile con il tipo di dati di origine ({0}). {1}{1} Esempio: se la colonna di origine ha il tipo di dati stringa e contiene lettere, non è possibile utilizzare un tipo di dati decimale per la destinazione.
#XMSG
invalidColumnCount=Selezionare una colonna di origine.
#XMSG
ObjectStoreInvalidScaleORPrecision=Immettere un valore valido per precisione e scala.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=Il primo valore corrisponde alla precisione, che definisce il numero totale di cifre. Il secondo corrisponde alla scala, che definisce le cifre oltre il separatore dei decimali. Immettere un valore scala di destinazione superiore al valore scala di origine e assicurarsi che la differenza tra il valore di precisione e scala di destinazione immesso sia superiore alla differenza tra il valore precisione e la scala di origine.
#XMSG
InvalidPrecisionORScale=Immettere un valore valido per precisione e scala.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=Il primo valore corrisponde alla precisione, che definisce il numero totale di cifre. Il secondo, invece, corrisponde alla scala, che definisce le cifre oltre il separatore dei decimali.{0}{0}Poiché il tipo di dati di origine non è supportato in Google BigQuery, viene convertito nel tipo di dati di destinazione DECIMAL. In questo caso, la precisione può essere definita solo tra 38 e 76, mentre la scala tra 9 e 38. Inoltre, il risultato della sottrazione della scala alla precisione, che rappresenta le cifre prima del separatore decimale, deve essere tra 29 e 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=Il primo valore corrisponde alla precisione, che definisce il numero totale di cifre. Il secondo, invece, corrisponde alla scala, che definisce le cifre oltre il separatore dei decimali.{0}{0}Poiché il tipo di dati di origine non è supportato in Google BigQuery, viene convertito nel tipo di dati di destinazione DECIMAL. In questo caso, la precisione deve essere definita da 20 o numero maggiore. Inoltre, il risultato della sottrazione della scala alla precisione, che rappresenta le cifre prima del separatore decimale, deve essere 20 o numero maggiore.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=Il primo valore corrisponde alla precisione, che definisce il numero totale di cifre. Il secondo, invece, corrisponde alla scala, che definisce le cifre dopo il separatore decimale.{0}{0}Poiché il tipo di dati di origine non è supportato nella destinazione, viene convertito nel tipo di dati di destinazione DECIMAL. In questo caso, la precisione deve essere definita da un qualsiasi numero maggiore o uguale a 1 e minore o uguale a 38 e la scala deve essere minore o uguale alla precisione.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=Il primo valore corrisponde alla precisione, che definisce il numero totale di cifre. Il secondo, invece, corrisponde alla scala, che definisce le cifre dopo il separatore decimale.{0}{0}Poiché il tipo di dati di origine non è supportato nella destinazione, viene convertito nel tipo di dati di destinazione DECIMAL. In questo caso, la precisione deve essere definita da 20 o numero maggiore. Inoltre, il risultato della sottrazione della scala alla precisione, che rappresenta le cifre prima del separatore decimale, deve essere 20 o numero maggiore.
#XMSG
invalidColumnCountDescription=Per creare un inserimento mappatura valido, è necessario selezionare una colonna di origine o immettere un valore costante.
#XMSG
duplicateColumns=Rinominare colonna di destinazione.
#XMSG
duplicateGBQCDCColumnsDesc=Il nome colonna di destinazione è riservato in Google BigQuery. È necessario rinominarla per poter salvare la proiezione.
#XMSG
duplicateConfluentCDCColumnsDesc=Il nome colonna di destinazione è riservato in Confluent. È necessario rinominarla per poter salvare la proiezione.
#XMSG
duplicateSignavioCDCColumnsDesc=Il nome colonna di destinazione è riservato in SAP Signavio. È necessario rinominarla per poter salvare la proiezione.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Il nome colonna di destinazione è riservato in MS OneLake. È necessario rinominarla per poter salvare la proiezione.
#XMSG
duplicateSFTPCDCColumnsDesc=Il nome colonna di destinazione è riservato in SFTP. È necessario rinominarla per poter salvare la proiezione.
#XMSG
GBQTargetNameWithPrefixUpdated1=Il nome colonna di destinazione contiene un prefisso riservato in Google BigQuery. È necessario rinominarla per poter salvare la proiezione. {0}{0}Il nome colonna di destinazione non può iniziare con una delle seguenti stringhe: {0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Abbreviare il nome colonna di destinazione.
#XMSG
GBQtargetMaxLengthDesc=In Google BigQuery, un nome colonna può utilizzare un massimo di 300 caratteri. Abbreviare il nome colonna di destinazione per poter salvare la proiezione.
#XMSG
invalidMappedScalePrecision=Precisione e scala della destinazione devono essere compatibili con precisione e scala dell'origine, in modo che tutte le cifre dell'origine siano compatibili con i campi di destinazione.
#XMSG
invalidMappedScalePrecisionShortText=Immettere un valore precisione e scala valido.
#XMSG
validationIncompatiblePKTypeDescProjection3=Una o più colonne di origine presentano tipi di dati che non possono essere definiti come chiavi primarie in Google BigQuery. Nessuna delle chiavi primarie verrà creata nell''oggetto di destinazione.{0}{0}I seguenti tipi di dati di destinazione sono compatibili con i tipi di dati Google BigQuery per cui è possibile definire una chiave primaria:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Deselezionare __message_id della colonna.
#XMSG
uncheckColumnMessageIdDesc=Colonna: chiave primaria
#XMSG
validationOpCodeInsert=È necessario immettere un valore per Inserisci.
#XMSG
recommendDifferentPrimaryKey=Si consiglia di selezionare una chiave primaria diversa a livello di elemento.
#XMSG
recommendDifferentPrimaryKeyDesc=Se il codice operazione è già definito, si consiglia di selezionare chiavi primarie diverse per l'indice matrice e per gli elementi per evitare problemi come la duplicazione delle colonne, ad esempio.
#XMSG
selectPrimaryKeyItemLevel=È necessario selezionare almeno una chiave primaria a livello sia di intestazione sia di elemento.
#XMSG
selectPrimaryKeyItemLevelDesc=Se una matrice o una mappa è espansa, è necessario selezionare due chiavi primarie, una a livello di intestazione e una a livello di elemento.
#XMSG
invalidMapKey=È necessario selezionare almeno una chiave primaria a livello di intestazione.
#XMSG
invalidMapKeyDesc=Se una matrice o una mappa è espansa, è necessario selezionare una chiave primaria a livello di intestazione.
#XFLD
txtSearchFields=Cerca colonne di destinazione
#XFLD
txtName=Nome
#XMSG
txtSourceColValidation=Una o più colonne di origine non sono supportate:
#XMSG
txtMappingCount=Mappature ({0})
#XMSG
schema=Schema
#XMSG
sourceColumn=Colonne di origine
#XMSG
warningSourceSchema=Eventuali modifiche apportate allo schema influiranno sulle mappature nella finestra di dialogo della proiezione.
#XCOL
txtTargetColName=Colonna di destinazione (nome tecnico)
#XCOL
txtDataType=Tipo di dati di destinazione
#XCOL
txtSourceDataType=Tipo di dati di origine
#XCOL
srcColName=Colonna di origine (nome tecnico)
#XCOL
precision=Precisione
#XCOL
scale=Scala
#XCOL
functionsOrConstants=Funzioni/Costanti
#XCOL
txtTargetColBusinessName=Colonna di destinazione (nome aziendale)
#XCOL
prKey=Chiave primaria
#XCOL
txtProperties=Proprietà
#XBUT
txtOK=Salva
#XBUT
txtCancel=Annulla
#XBUT
txtRemove=Rimuovi
#XFLD
txtDesc=Descrizione
#XMSG
rftdMapping=Mappatura
#XFLD
@lblColumnDataType=Tipo di dati
#XFLD
@lblColumnTechnicalName=Nome tecnico
#XBUT
txtAutomap=Mappa automaticamente
#XBUT
txtUp=Su
#XBUT
txtDown=Giù

#XTOL
txtTransformationHeader=Proiezione
#XTOL
editTransformation=Modifica
#XTOL
primaryKeyToolip=Chiave


#XMSG
rftdFilter=Filtra
#XMSG
rftdFilterColumnCount=Origine: {0}({1})
#XTOL
rftdFilterColSearch=Cerca
#XMSG
rftdFilterColNoData=Nessuna colonna da visualizzare
#XMSG
rftdFilteredColNoExps=Nessuna espressione filtro
#XMSG
rftdFilterSelectedColTxt=Aggiungi filtro per
#XMSG
rftdFilterTxt=Filtro disponibile per
#XBUT
rftdFilterSelectedAddColExp=Aggiungi espressione
#YINS
rftdFilterNoSelectedCol=Seleziona una colonna per aggiungere un filtro.
#XMSG
rftdFilterExp=Espressione filtro
#XMSG
rftdFilterNotAllowedColumn=L'aggiunta di filtri non è supportata per questa colonna.
#XMSG
rftdFilterNotAllowedHead=Colonna non supportata
#XMSG
rftdFilterNoExp=Nessun filtro definito
#XTOL
rftdfilteredTt=Filtrato
#XTOL
rftdremoveexpTt=Rimuovi espressione filtro
#XTOL
validationMessageTt=Messaggi di convalida
#XTOL
rftdFilterDateInp=Seleziona una data
#XTOL
rftdFilterDateTimeInp=Seleziona una data/ora
#XTOL
rftdFilterTimeInp=Seleziona un orario
#XTOL
rftdFilterInp=Immetti un valore
#XMSG
rftdFilterValidateEmptyMsg={0} espressioni filtro sulla colonna {1} sono vuote
#XMSG
rftdFilterValidateInvalidNumericMsg={0} espressioni filtro sulla colonna {1} contengono valori numerici non validi
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=L'espressione filtro deve contenere valori numerici validi
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Se lo schema oggetto di destinazione è cambiato, utilizzare la funzione "Mappa a oggetto di destinazione esistente" nella pagina principale per adattare le modifiche e rimappare l'oggetto di destinazione alla propria origine.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Se la tabella di destinazione già esiste e la mappatura include una modifica dello schema, è necessario modificare di conseguenza la tabella di destinazione prima di distribuire il flusso di replicazione.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Se la mappatura coinvolge una modifica dello schema, è necessario modificare di conseguenza la tabella di destinazione prima di distribuire il flusso di replicazione.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Le seguenti colonne non supportate vengono ignorate dalla definizione di origine: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Le seguenti colonne non supportate vengono ignorate dalla definizione di destinazione: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=I seguenti oggetti non sono supportati perché esposti per il consumo: {0} {1} {0} {0} Per utilizzare le tabelle in un flusso di replicazione, è necessario non impostare l''utilizzo semantico (nelle impostazioni tabella) su {2}Insieme di dati analitico{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Impossibile utilizzare l''oggetto di destinazione perché esposto per il consumo. {0} {0} Per utilizzare la tabella in un flusso di replicazione, è necessario non impostare l''utilizzo semantico (nelle impostazioni tabella) su {1}Insieme di dati analitico{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Esiste già un oggetto di destinazione con questo nome; tuttavia, non può essere utilizzato perché esposto per il consumo. {0} {0} Per utilizzare la tabella in un flusso di replicazione, è necessario non impostare l''utilizzo semantico (nelle impostazioni tabella) su {1}Insieme di dati analitico{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Nella destinazione esiste già un oggetto con questo nome. {0}Tuttavia, esso non può essere utilizzato come oggetto di destinazione per un flusso di replicazione al repository locale, poiché non è una tabella locale.
#XMSG:
targetAutoRenameUpdated=La colonna di destinazione è stata rinominata.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=La colonna di destinazione è stata rinominata per consentire le replicazioni in Google BigQuery. Ciò è dovuto a una o più ragioni: {0} {1}{2}Nome colonna riservato{3}{2}Caratteri non supportati{3}{2}Prefisso riservato{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=La colonna di destinazione è stata rinominata per consentire le replicazioni in Confluent. Ciò è dovuto a una o più ragioni: {0} {1}{2}Nome colonna riservato{3}{2}Caratteri non supportati{3}{2}Prefisso riservato{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=La colonna di destinazione è stata rinominata per consentire le replicazioni nella destinazione. Ciò è dovuto a uno o più dei seguenti motivi: {0} {1}{2}Caratteri non supportati{3}{2}Prefisso riservato{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=La colonna di destinazione è stata rinominata per consentire le replicazioni nella destinazione. Ciò è dovuto a uno o più dei seguenti motivi:{0} {1}{2}Nome colonna riservato{3}{2}Caratteri non supportati{3}{2}Prefisso riservato{3}{4}
#XMSG:
targetAutoDataType=Il tipo di dati di destinazione è stato modificato.
#XMSG:
targetAutoDataTypeDesc=Il tipo di dati di destinazione è stato modificato in {0} perché il tipo di dati di origine non è supportato in Google BigQuery.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Il tipo di dati di destinazione è stato modificato in {0} perché il tipo di dati di origine non è supportato nella connessione di destinazione.
#XMSG
projectionGBQUnableToCreateKey=Le chiavi primarie non verranno create.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=In Google BigQuery, è supportato un massimo di 16 chiavi primarie, ma l'oggetto di origine ne contiene un numero maggiore. Nessuna delle chiavi primarie verrà creata nell'oggetto di destinazione.
#XMSG
HDLFNoKeyError=Definire una o più colonne come chiave primaria.
#XMSG
HDLFNoKeyErrorDescription=Per replicare un oggetto, è necessario definire una o più colonne come chiave primaria.
#XMSG
HDLFNoKeyErrorExistingTarget=Definire una o più colonne come chiave primaria.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Per replicarvi i dati, un oggetto di destinazione esistente deve avere una o più colonne definite come chiave primaria. {0} {0} Per la definizione di una o più colonne come chiave primaria, sono disponbili le opzioni seguenti:  {0}{0}{1}Utilizzare l''editor tabelle locale per modificare l''oggetto di destinazione esistente; ricaricare quindi il flusso di replicazione.{0}{0}{1} Rinominare l''oggetto di destinazione nel flusso di replicazione, per creare un nuovo oggetto non appena viene avviata un''esecuzione. Dopo la ridenominazione, è possibile definire una o più colonne come chiave primaria in una proiezione. {0}{0}{1} Mappare l''oggetto a un altro oggetto di destinazione esistente in cui sono già definite una o più colonne come chiave primaria.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Chiave primaria modificata
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=In confronto all''oggetto di origine, sono state definite come chiave primaria colonne diverse per l''oggetto di destinazione. Assicurarsi che tali colonne identifichino in maniera univoca tutte le righe per evitare successivamente, durante la replicazione dei dati, una loro possibile corruzione. {0} {0} Nell''oggetto di origine, sono definite come chiave primaria le seguenti colonne: {0} {1}
#XMSG
duplicateDPIDColumns=Rinominare colonna di destinazione.
#XMSG
duplicateDPIDDColumnsDesc1=Il nome colonna di destinazione è riservato per una colonna tecnica. Per salvare la proiezione, immettere un nome diverso.
#XMSG:
targetAutoRenameDPID=La colonna di destinazione è stata rinominata.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=La colonna di destinazione è stata rinominata per consentire le replicazioni da origine ABAP senza chiavi. Ciò è dovuto a una o più ragioni: {0} {1}{2}Nome colonna riservato{3}{2}Caratteri non supportati{3}{2}Prefisso riservato{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle=Impostazioni di destinazione {0}
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle=Impostazioni di origine {0}
#XBUT
connectionSettingSave=Salva
#XBUT
connectionSettingCancel=Annulla
#XBUT: Button to keep the object level settings
txtKeep=Mantieni
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Sovrascrivi
#XFLD
targetConnectionThreadlimit=Limite thread di destinazione per caricamento iniziale (1-100)
#XFLD
connectionThreadLimit=Limite thread di origine per caricamento iniziale (1-100)
#XFLD
maxConnection=Limite thread di replicazione (1-100)
#XFLD
kafkaNumberOfPartitions=Numero di partizioni
#XFLD
kafkaReplicationFactor=Fattore di replicazione
#XFLD
kafkaMessageEncoder=Codificatore di messaggi
#XFLD
kafkaMessageCompression=Compressione messaggio
#XFLD
fileGroupDeltaFilesBy=Raggruppa delta per
#XFLD
fileFormat=Tipo di file
#XFLD
csvEncoding=Codifica CSV
#XFLD
abapExitLbl=Exit ABAP
#XFLD
deltaPartition=Conteggio thread oggetto per caricamenti delta (1-10)
#XFLD
clamping_Data=Errore nel troncamento dati
#XFLD
fail_On_Incompatible=Errore a causa di dati incompatibili
#XFLD
maxPartitionInput=Numero massimo di partizioni
#XFLD
max_Partition=Definisci numero massimo di partizioni
#XFLD
include_SubFolder=Includi sottocartelle
#XFLD
fileGlobalPattern=Pattern globale per nome file
#XFLD
fileCompression=Compressione file
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Delimitatore file
#XFLD
fileIsHeaderIncluded=Intestazione file
#XFLD
fileOrient=Orientamento
#XFLD
gbqWriteMode=Modalità di scrittura
#XFLD
suppressDuplicate=Ometti duplicati
#XFLD
apacheSpark=Abilita compatibilità con Apache Spark
#XFLD
clampingDatatypeCb=Blocca tipi di dati con punto decimale mobile
#XFLD
overwriteDatasetSetting=Sovrascrivi impostazioni di destinazione a livello oggetto
#XFLD
overwriteSourceDatasetSetting=Sovrascrivi impostazioni di origine a livello oggetto
#XMSG
kafkaInvalidConnectionSetting=Inserire un numero tra {0} e {1}.
#XMSG
MinReplicationThreadErrorMsg=Immettere un numero maggiore di {0}.
#XMSG
MaxReplicationThreadErrorMsg=Immettere un numero minore di {0}.
#XMSG
DeltaThreadErrorMsg=Immettere un valore tra 1 e 10.
#XMSG
MaxPartitionErrorMsg=Immettere un valore tra 1 <= x <= 2147483647. Il valore predefinito è 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Immettere un numero intero tra {0} e {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Utilizza fattore di replicazione del broker
#XFLD
serializationFormat=Formato serializzazione
#XFLD
compressionType=Tipo di compressione
#XFLD
schemaRegistry=Utilizza schema Registry
#XFLD
subjectNameStrat=Strategia nome oggetto
#XFLD
compatibilityType=Tipo di compatibilità
#XFLD
confluentTopicName=Nome argomento
#XFLD
confluentRecordName=Nome record
#XFLD
confluentSubjectNamePreview=Anteprima nome oggetto
#XMSG
serializationChangeToastMsgUpdated2=Formato serializzazione modificato in JSON in quanto il registro schemi non è abilitato. Per riportare il formato di serializzazione in AVRO, è prima necessario abilitare il registro schemi.
#XBUT
confluentTopicNameInfo=Il nome argomento è sempre basato sul nome oggetto di destinazione. È possibile modificarlo rinominando l'oggetto di destinazione.
#XMSG
emptyRecordNameValidationHeaderMsg=Immetti un nome record.
#XMSG
emptyPartionHeader=Immettere il numero di partizioni.
#XMSG
invalidPartitionsHeader=Immettere un numero di partizioni valido.
#XMSG
invalidpartitionsDesc=Immettere un numero compreso tra 1 e 200.000.
#XMSG
emptyrFactorHeader=Immettere un fattore di replicazione.
#XMSG
invalidrFactorHeader=Immettere un fattore di replicazione valido.
#XMSG
invalidrFactorDesc=Immettere un numero compreso tra 1 e 32.767.
#XMSG
emptyRecordNameValidationDescMsg=Se viene utilizzato il formato di serializzazione "AVRO", sono supportati solo i seguenti caratteri:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(trattino basso)
#XMSG
validRecordNameValidationHeaderMsg=Immettere un nome record valido.
#XMSG
validRecordNameValidationDescMsgUpdated=Poiché è in uso il formato di serializzazione "AVRO", il nome record deve essere formato solo da caratteri alfanumerici (A-Z, a-z, 0-9) e trattino basso (_). Deve iniziare con una lettera o un trattino basso.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=L'impostazione "Conteggio thread oggetto per caricamenti delta" può essere impostata non appena uno o più oggetti presentano il tipo di caricamento "Iniziale e delta".
#XMSG
invalidTargetName=Nome colonna non valido
#XMSG
invalidTargetNameDesc=Il nome colonna di destinazione deve essere formato da soli caratteri alfanumerici (A-Z, a-z, 0-9) e trattino basso (_).
#XFLD
consumeOtherSchema=Consuma altre versioni schema
#XFLD
ignoreSchemamissmatch=Ignora incongruenza dello schema
#XFLD
confleuntDatatruncation=Errore nel troncamento dati
#XFLD
isolationLevel=Livello di isolamento
#XFLD
confluentOffset=Punto iniziale
#XFLD
signavioGroupDeltaFilesByText=Nessuno
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=No
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=No

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Proiezione
#XBUT
txtAdd=Aggiungi
#XBUT
txtEdit=Modifica
#XMSG
transformationText=Aggiungi una proiezione per configurare il filtro o la mappatura.
#XMSG
primaryKeyRequiredText=Selezionare una chiave primaria con Configura schema.
#XFLD
lblSettings=Impostazioni
#XFLD
lblTargetSetting={0}: impostazioni di destinazione
#XMSG
@csvRF=Selezionare il file che contiene la definizione schema che si desidera applicare a tutti i file della cartella.
#XFLD
lblSourceColumns=Colonne di origine
#XFLD
lblJsonStructure=Struttura JSON
#XFLD
lblSourceSetting={0}: impostazioni di origine
#XFLD
lblSourceSchemaSetting={0}: impostazioni schema di origine
#XBUT
messageSettings=Impostazioni messaggio
#XFLD
lblPropertyTitle1=Proprietà dell'oggetto
#XFLD
lblRFPropertyTitle=Proprietà flusso di replicazione
#XMSG
noDataTxt=Nessuna colonna da visualizzare.
#XMSG
noTargetObjectText=Nessun oggetto di destinazione selezionato.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Colonne di destinazione
#XMSG
searchColumns=Cerca colonne
#XTOL
cdcColumnTooltip=Colonna per acquisizione delta
#XMSG
sourceNonDeltaSupportErrorUpdated=L'oggetto di origine non supporta l'acquisizione delta.
#XMSG
targetCDCColumnAdded=Sono state aggiunte 2 colonne di destinazione per l'acquisizione delta.
#XMSG
deltaPartitionEnable=Limite thread oggetto per caricamenti delta aggiunto alle impostazioni di origine
#XMSG
attributeMappingRemovalTxt=Rimozione mappature non valide che non sono supportate per il nuovo oggetto di destinazione.
#XMSG
targetCDCColumnRemoved=Sono state rimosse 2 colonne di destinazione utilizzate per l'acquisizione delta.
#XMSG
replicationLoadTypeChanged=Tipo di caricamento modificato in "Iniziale e delta".
#XMSG
sourceHDLFLoadTypeError=Modifica tipo di caricamento in "Iniziale e delta".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Per replicare un oggetto da una connessione di origine con il tipo di connessione SAP HANA Cloud, file data lake in SAP Datasphere, è necessario utilizzare il tipo di caricamento "iniziale e delta".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Attivare acquisizione dati.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Per replicare un oggetto da una connessione di origine con il tipo di connessione SAP HANA Cloud, file data lake in SAP Datasphere, è necessario attivare l'acquisizione dati.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Modificare l'oggetto di destinazione.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=L'oggetto di destinazione non può essere utilizzato perché l'acquisizione delta è disattivata. È possibile rinominare l'oggetto di destinazione (che consente la creazione di un nuovo oggetto con acquisizione delta) o mapparlo a un oggetto esistente per cui è attiva l'acquisizione delta.
#XMSG
deltaPartitionError=Immettere un conteggio thread oggetto valido per caricamenti delta.
#XMSG
deltaPartitionErrorDescription=Immettere un valore tra 1 e 10.
#XMSG
deltaPartitionEmptyError=Immettere un conteggio thread oggetto per caricamenti delta.
#XFLD
@lblColumnDescription=Descrizione
#XMSG
@lblColumnDescriptionText1=Per scopi tecnici: gestione di record duplicati causati da problemi durante la replicazione di oggetti di origine basati su ABAP senza una chiave primaria.
#XFLD
storageType=Spazio di archiviazione
#XFLD
skipUnmappedColLbl=Salta colonne non mappate
#XFLD
abapContentTypeLbl=Tipo di contenuto
#XFLD
autoMergeForTargetLbl=Unisci dati automaticamente
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Generale
#XFLD
lblBusinessName=Nome aziendale
#XFLD
lblTechnicalName=Nome tecnico
#XFLD
lblPackage=Pacchetto
#XFLD
statusPanel=Stato esecuzione
#XBTN: Schedule dropdown menu
SCHEDULE=Pianificazione
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Modifica pianificazione
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Elimina pianificazione
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Crea pianificazione
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Controllo di convalida pianificazione non riuscito
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Impossibile creare una pianificazione perché è attualmente in corso la distribuzione del flusso di replicazione.{0}Attendere il completamento della distribuzione del flusso di replicazione.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Impossibile creare una pianificazione per i flussi di replicazione che contengono oggetti con tipo di caricamento "Iniziale e delta".
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Impossibile creare una pianificazione per i flussi di replicazione che contengono oggetti con tipo di caricamento "Iniziale e delta / Solo delta".
#XFLD : Scheduled popover
SCHEDULED=Pianificato
#XFLD
CREATE_REPLICATION_TEXT=Crea un flusso di replicazione
#XFLD
EDIT_REPLICATION_TEXT=Modifica un flusso di replicazione
#XFLD
DELETE_REPLICATION_TEXT=Elimina un flusso di replicazione
#XFLD
REFRESH_FREQUENCY=Frequenza
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Impossibile distribuire il flusso di replicazione perché la pianificazione esistente{0} non supporta ancora il tipo di caricamento "Iniziale e delta".{0}{0}Per distribuire il flusso di replicazione è necessario impostare i tipi di caricamento di tutti gli oggetti{0} su "Solo iniziale". In alternativa, è possibile eliminare la pianificazione, distribuire il flusso di replicazione {0}, quindi avviare una nuova esecuzione. Ciò avrà come risultato un''esecuzione continua{0} e supporterà anche oggetti con tipo di caricamento "Iniziale e delta".
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Impossibile distribuire il flusso di replicazione perché la pianificazione esistente{0} non supporta ancora il tipo di caricamento "Iniziale e delta / Solo delta".{0}{0}Per distribuire il flusso di replicazione è necessario impostare i tipi di caricamento di tutti gli oggetti{0} su "Solo iniziale". In alternativa, è possibile eliminare la pianificazione, distribuire il {0}flusso di replicazione , quindi avviare una nuova esecuzione. Ciò avrà come risultato un''esecuzione continua{0} e supporterà anche oggetti con tipo di caricamento "Iniziale e delta / Solo delta".
#XMSG
SCHEDULE_EXCEPTION=Recupero dettagli della pianificazione non riuscito
#XFLD: Label for frequency column
everyLabel=Ogni
#XFLD: Plural Recurrence text for Hour
hoursLabel=Ore
#XFLD: Plural Recurrence text for Day
daysLabel=Giorni
#XFLD: Plural Recurrence text for Month
monthsLabel=Mesi
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuti
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Impossibile ottenere informazioni sulla possibilità di pianificazione.
#XFLD :Paused field
PAUSED=Sospeso
#XMSG
navToMonitoring=Apri nel monitor dei flussi
#XFLD
statusLbl=Stato
#XFLD
lblLastRunExecuted=Avvio ultima esecuzione
#XFLD
lblLastExecuted=Ultima esecuzione
#XFLD: Status text for Completed
statusCompleted=Completato
#XFLD: Status text for Running
statusRunning=In esecuzione
#XFLD: Status text for Failed
statusFailed=Non riuscito
#XFLD: Status text for Stopped
statusStopped=Interrotto
#XFLD: Status text for Stopping
statusStopping=Interruzione in corso
#XFLD: Status text for Active
statusActive=Attivo
#XFLD: Status text for Paused
statusPaused=In pausa
#XFLD: Status text for not executed
lblNotExecuted=Non ancora eseguito
#XFLD
messagesSettings=Impostazioni messaggi
#XTOL
@validateModel=Messaggi di convalida
#XTOL
@hierarchy=Gerarchia
#XTOL
@columnCount=Numero di colonne
#XMSG
VAL_PACKAGE_CHANGED=Questo oggetto è stato assegnato al pacchetto "{1}". Fare clic su "Salva" per confermare e convalidare questa modifica. Notare che l''assegnazione a un pacchetto non può essere annullata in questo editor dopo il salvataggio.
#XMSG
MISSING_DEPENDENCY=Le dipendenze dell''oggetto "{0}" non possono essere risolte nel pacchetto "{1}".
#XFLD
deltaLoadInterval=Intervallo di caricamento delta
#XFLD
lblHour=Ore (0-24)
#XFLD
lblMinutes=Minuti (0-59)
#XMSG
maxHourOrMinErr=Immettere un valore tra 0 e {0}.
#XMSG
maxDeltaInterval=Il valore massimo dell''intervallo di caricamento delta è 24 ore.{0}Modificare il valore dei minuti o delle ore di conseguenza.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Percorso contenitore di destinazione
#XFLD
confluentSubjectName=Nome oggetto
#XFLD
confluentSchemaVersion=Versione schema
#XFLD
confluentIncludeTechKeyUpdated=Includi chiave tecnica
#XFLD
confluentOmitNonExpandedArrays=Ometti matrici non espanse
#XFLD
confluentExpandArrayOrMap=Espandi matrice o mappa
#XCOL
confluentOperationMapping=Mappatura operazioni
#XCOL
confluentOpCode=Codice operazione
#XFLD
confluentInsertOpCode=Inserisci
#XFLD
confluentUpdateOpCode=Aggiorna
#XFLD
confluentDeleteOpCode=Elimina
#XFLD
expandArrayOrMapNotSelectedTxt=Non selezionato
#XFLD
confluentSwitchTxtYes=Sì
#XFLD
confluentSwitchTxtNo=No
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Errore
#XTIT
executeWarning=Avviso
#XMSG
executeunsavederror=Salvare il flusso di replicazione prima dell'esecuzione.
#XMSG
executemodifiederror=Sono presenti modifiche non salvate nel flusso di replicazione. Salvare il flusso di replicazione.
#XMSG
executeundeployederror=È necessario distribuire il flusso di replicazione prima dell'esecuzione.
#XMSG
executedeployingerror=Attendere la conclusione della distribuzione.
#XMSG
msgRunStarted=Esecuzione avviata
#XMSG
msgExecuteFail=Esecuzione flusso di replicazione non riuscita.
#XMSG
titleExecuteBusy=Attendi.
#XMSG
msgExecuteBusy=Stiamo preparando i dati per l'esecuzione del flusso di replicazione.
#XTIT
executeConfirmDialog=Avviso
#XMSG
msgExecuteWithValidations=Il flusso di replicazione contiene errori di convalida che potrebbero causare un errore di esecuzione.
#XMSG
msgRunDeployedVersion=Sono presenti modifiche da distribuire. Verrà eseguita l'ultima versione distribuita del flusso di replicazione. Continuare?
#XBUT
btnExecuteAnyway=Esegui comunque
#XBUT
btnExecuteClose=Chiudi
#XBUT
loaderClose=Chiudi
#XTIT
loaderTitle=Caricamento in corso
#XMSG
loaderText=Chiamata dettagli dal server
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Il flusso di replicazione a questa connessione di destinazione non SAP non può essere avviato
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=perché non è disponibile volume in uscita per questo mese.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Un amministratore può aumentare i blocchi di uscita premium per questo
#XMSG
premiumOutBoundRFAdminErrMsgPart2=tenant, rendendo disponibile volume in uscita per questo mese.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Errore
#XTIT
deployInfo=Informazioni
#XMSG
deployCheckFailException=Eccezione verificatasi durante la distribuzione
#XMSG
deployGBQFFDisabled=Non è al momento possibile distribuire flussi di replicazione con una connessione di destinazione a Google BigQuery perché su questa funzione è in corso la manutenzione.
#XMSG
deployKAFKAFFDisabled=Non è al momento possibile distribuire flussi di replicazione con una connessione di destinazione ad Apache Kafka perché su questa funzione è in corso la manutenzione.
#XMSG
deployConfluentDisabled=Non è al momento possibile distribuire flussi di replicazione con una connessione di destinazione ad Confluent Kafka perché su questa funzione è in corso la manutenzione.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Per i seguenti oggetti di destinazione, i nomi della tabella di acquisizione delta sono già utilizzati da altre tabelle nel repository: {0} È necessario rinominare questi oggetti di destinazione per assicurarsi che i nomi della tabella di acquisizione delta siano univoci prima di poter distribuire il flusso di replicazione.
#XMSG
deployDWCSourceFFDisabled=Non è attualmente possibile distribuire i flussi di replicazione la cui origine è SAP Datasphere perché su questa funzione è in corso la manutenzione.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Non è attualmente possibile distribuire i flussi di replicazione che contengono tabelle locali con delta attivato come oggetti di origine perché su questa funzione è in corso la manutenzione.
#XMSG
deployHDLFSourceFFDisabled=Non è attualmente possibile distribuire i flussi di replicazione le cui connessioni di origine sono di tipo SAP HANA Cloud, file data lake perché è in corso la manutenzione.
#XMSG
deployObjectStoreAsSourceFFDisabled=Attualmente non è possibile distribuire i flussi di replicazione la cui origine è un provider di archivi cloud.
#XMSG
deployConfluentSourceFFDisabled=Non è attualmente possibile distribuire i flussi di replicazione la cui origine è Confluent Kafka perché su questa funzione è in corso la manutenzione.
#XMSG
deployMaxDWCNewTableCrossed=Per grandi flussi di replicazione, non è possibile "salvarli e distribuirli" in un'unica operazione. Salvare prima il flusso di replicazione, quindi distribuirlo.
#XMSG
deployInProgressInfo=Distribuzione già in corso.
#XMSG
deploySourceObjectInUse=Gli oggetti di origine {0} sono già in uso nei flussi di replicazione {1}.
#XMSG
deployTargetSourceObjectInUse=Gli oggetti di origine {0} sono già in uso nei flussi di replicazione {1}. Gli oggetti di destinazione {2} sono già in uso nei flussi di replicazione {3}.
#XMSG
deployReplicationFlowCheckError=Errore di verifica del flusso di replicazione: {0}
#XMSG
preDeployTargetObjectInUse=Gli oggetti di destinazione {0} sono già in uso nei flussi di replicazione {1}e non è possibile avere lo stesso oggetto di destinazione in due diversi flussi di replicazione. Selezionare un altro oggetto di destinazione e riprovare.
#XMSG
runInProgressInfo=Flusso di replicazione già in esecuzione.
#XMSG
deploySignavioTargetFFDisabled=Non è attualmente possibile distribuire i flussi di replicazione la cui destinazione è SAP Signavio perché su questa funzione è in corso la manutenzione.
#XMSG
deployHanaViewAsSourceFFDisabled=Attualmente non è possibile distribuire flussi di replicazione contenenti viste come oggetti di origine per la connessione di origine selezionata. Riprovare più tardi.
#XMSG
deployMsOneLakeTargetFFDisabled=Non è attualmente possibile distribuire i flussi di replicazione la cui destinazione è MS OneLake perché su questa funzione è in corso la manutenzione.
#XMSG
deploySFTPTargetFFDisabled=Non è attualmente possibile distribuire i flussi di replicazione la cui destinazione è SFTP perché su questa funzione è in corso la manutenzione.
#XMSG
deploySFTPSourceFFDisabled=Non è attualmente possibile distribuire i flussi di replicazione la cui origine è SFTP perché su questa funzione è in corso la manutenzione.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Nome tecnico
#XFLD
businessNameInRenameTarget=Nome aziendale
#XTOL
renametargetDialogTitle=Rinomina oggetto di destinazione
#XBUT
targetRenameButton=Rinomina
#XBUT
targetRenameCancel=Annulla
#XMSG
mandatoryTargetName=È necessario immettere un nome.
#XMSG
dwcSpecialChar=Il trattino basso (_) è l'unico carattere speciale consentito.
#XMSG
dwcWithDot=Il nome della tabella di destinazione può essere composto da lettere latine, numeri, trattini bassi (_) e punti (.). Il primo carattere deve essere una lettera, un numero o un trattino basso (non un punto).
#XMSG
nonDwcSpecialChar=I caratteri speciali consentiti sono: trattino basso (_), trattino (-) e punto (.).
#XMSG
firstUnderscorePattern=Il nome non deve iniziare con _ (trattino basso).

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: istruzioni tabella di creazione vista SQL
#XMSG
sqlDialogMaxPKWarning=In Google BigQuery, è supportato un massimo di 16 chiavi primarie e l'oggetto di origine ne ha un numero maggiore; pertanto in questa istruzione non è definita alcuna chiave primaria.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Una o più colonne di origine presentano tipi di dati che non possono essere definiti come chiavi primarie in Google BigQuery, pertanto in questo caso non è definita alcuna chiave primaria. In Google BigQuery, solo i seguenti tipi di dati possono avere una chiave primaria: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Copia e chiudi
#XBUT
closeDDL=Chiudi
#XMSG
copiedToClipboard=Copiato negli Appunti


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=L''oggetto ''{0}'' non può far parte di una catena di task perché non ha una fine (in quanto include oggetti con tipo di caricamento "Iniziale e delta / Solo delta").
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=L''oggetto ''{0}'' non può far parte di una catena di task perché non ha una fine (in quanto include oggetti con tipo di caricamento "Iniziale e delta").
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Impossibile aggiungere l''oggetto "{0}" alla catena di task.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Sono presenti oggetti di destinazione non salvati. Salvarli di nuovo.{0}{0} Il funzionamento di questa funzionalità è cambiato: in passato, gli oggetti di destinazione venivano creati nell''ambiente di destinazione solo quando il flusso di replicazione veniva distribuito.{0} Ora gli oggetti vengono già creati quando il flusso di replicazione viene salvato. Il flusso di replicazione è stato creato prima di questa modifica e contiene nuovi oggetti.{0} È necessario salvare di nuovo il flusso di replicazione prima di distribuirlo così che gli oggetti vengano inclusi correttamente.
#XMSG
confirmChangeContentTypeMessage=Si sta per modificare il tipo di contenuto. Così facendo, verranno eliminate tutte le proiezioni esistenti.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Nome oggetto
#XFLD
schemaDialogVersionName=Versione schema
#XFLD
includeTechKey=Includi chiave tecnica
#XFLD
segementButtonFlat=Semplice
#XFLD
segementButtonNested=Nidificata
#XMSG
subjectNamePlaceholder=Cerca nome oggetto

#XMSG
@EmailNotificationSuccess=La configurazione delle notifiche di posta elettronica runtime è stata salvata.

#XFLD
@RuntimeEmailNotification=Notifica di posta elettronica runtime

#XBTN
@TXT_SAVE=Salva


