#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=[[[Řēρĺįċąţįŏŋ Ƒĺŏŵ∙∙∙∙∙∙∙∙]]]

#XFLD: Edit Schema button text
editSchema=[[[Ĕƌįţ Ŝċĥēɱą∙∙∙∙∙∙∙∙]]]

#XTIT : Properties heading
configSchema=[[[Ĉŏŋƒįğűŗē Ŝċĥēɱą∙∙∙∙∙∙∙∙]]]

#XFLD: save changed button text
applyChanges=[[[Āρρĺŷ Ĉĥąŋğēş∙∙∙∙∙∙]]]


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=[[[Ŝēĺēċţ Ŝŏűŗċē Ĉŏŋŋēċţįŏŋ∙∙∙∙∙∙]]]
#XFLD
sourceContainernEmptyText=[[[Ŝēĺēċţ Ĉŏŋţąįŋēŗ∙∙∙∙∙∙∙∙]]]
#XFLD
targetConnectionEmptyText=[[[Ŝēĺēċţ Ţąŗğēţ Ĉŏŋŋēċţįŏŋ∙∙∙∙∙∙]]]
#XFLD
targetContainernEmptyText=[[[Ŝēĺēċţ Ĉŏŋţąįŋēŗ∙∙∙∙∙∙∙∙]]]
#XFLD
sourceSelectObjectText=[[[Ŝēĺēċţ Ŝŏűŗċē Ŏƃĵēċţ∙∙∙∙]]]
#XFLD
sourceObjectCount=[[[Ŝŏűŗċē Ŏƃĵēċţş ({0})]]]
#XFLD
targetObjectText=[[[Ţąŗğēţ Ŏƃĵēċţş∙∙∙∙∙]]]
#XFLD
confluentBrowseContext=[[[Ŝēĺēċţ Ĉŏŋţēχţ∙∙∙∙∙]]]
#XBUT
@retry=[[[Řēţŗŷ∙∙∙∙∙∙∙∙∙]]]
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=[[[Ţēŋąŋţ űρğŗąƌē įŋ ρŗŏğŗēşş.∙∙∙∙∙∙∙∙]]]

#XTOL
browseSourceConnection=[[[Ɓŗŏŵşē şŏűŗċē ċŏŋŋēċţįŏŋ∙∙∙∙∙∙]]]
#XTOL
browseTargetConnection=[[[Ɓŗŏŵşē ţąŗğēţ ċŏŋŋēċţįŏŋ∙∙∙∙∙∙]]]
#XTOL
browseSourceContainer=[[[Ɓŗŏŵşē şŏűŗċē ċŏŋţąįŋēŗ∙∙∙∙∙∙]]]
#XTOL
browseAndAddSourceDataset=[[[Āƌƌ şŏűŗċē ŏƃĵēċţş∙∙∙∙∙∙]]]
#XTOL
browseTargetContainer=[[[Ɓŗŏŵşē ţąŗğēţ ċŏŋţąįŋēŗ∙∙∙∙∙∙]]]
#XTOL
browseTargetSetting=[[[Ɓŗŏŵşē ţąŗğēţ şēţţįŋğş∙∙∙∙∙]]]
#XTOL
browseSourceSetting=[[[Ɓŗŏŵşē şŏűŗċē şēţţįŋğş∙∙∙∙∙]]]
#XTOL
sourceDatasetInfo=[[[Ĭŋƒŏ]]]
#XTOL
sourceDatasetRemove=[[[Řēɱŏʋē∙∙∙∙∙∙∙∙]]]
#XTOL
mappingCount=[[[Ţĥįş ŗēρŗēşēŋţş ţĥē ţŏţąĺ ŋűɱƃēŗ ŏƒ ŋŏŋ-ŋąɱē ƃąşēƌ ɱąρρįŋğş/ēχρŗēşşįŏŋş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTOL
filterCount=[[[Ţĥįş ŗēρŗēşēŋţş ţĥē ţŏţąĺ ŋűɱƃēŗ ŏƒ ƒįĺţēŗ ċŏŋƌįţįŏŋş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTOL
loading=[[[Ļŏąƌįŋğ...∙∙∙∙]]]
#XCOL
deltaCapture=[[[Ďēĺţą Ĉąρţűŗē∙∙∙∙∙∙]]]
#XCOL
deltaCaptureTableName=[[[Ďēĺţą Ĉąρţűŗē Ţąƃĺē∙∙∙∙∙]]]
#XCOL
loadType=[[[Ļŏąƌ Ţŷρē∙∙∙∙∙]]]
#XCOL
deleteAllBeforeLoading=[[[Ďēĺēţē Āĺĺ Ɓēƒŏŗē Ļŏąƌįŋğ∙∙∙∙∙∙∙]]]
#XCOL
transformationsTab=[[[Ƥŗŏĵēċţįŏŋş∙∙∙∙∙∙∙∙]]]
#XCOL
settingsTab=[[[Ŝēţţįŋğş∙∙∙∙∙∙]]]

#XBUT
renameTargetObjectBtn=[[[Řēŋąɱē Ţąŗğēţ Ŏƃĵēċţ∙∙∙∙]]]
#XBUT
mapToExistingTargetObjectBtn=[[[Μąρ ţŏ Ĕχįşţįŋğ Ţąŗğēţ Ŏƃĵēċţ∙∙∙∙∙∙∙∙∙]]]
#XBUT
changeContainerPathBtn=[[[Ĉĥąŋğē Ĉŏŋţąįŋēŗ Ƥąţĥ∙∙∙∙∙]]]
#XBUT
viewSQLDDLUpdated=[[[Ʋįēŵ ŜǬĻ Ĉŗēąţē Ţąƃĺē Ŝţąţēɱēŋţ∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=[[[Ţĥē şŏűŗċē ŏƃĵēċţ ƌŏēş ŋŏţ şűρρŏŗţ ƌēĺţą ċąρţűŗē, ƃűţ ţĥē şēĺēċţēƌ ţąŗğēţ ŏƃĵēċţ ĥąş ţĥē ƌēĺţą ċąρţűŗē ŏρţįŏŋ ēŋąƃĺēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
sourceObjectNonDeltaSupportErrorMsg=[[[Ţĥē ţąŗğēţ ŏƃĵēċţ ċąŋŋŏţ ƃē űşēƌ ƃēċąűşē ƌēĺţą ċąρţűŗē įş ēŋąƃĺēƌ,{0}ŵĥēŗēąş ţĥē şŏűŗċē ŏƃĵēċţ ƌŏēş ŋŏţ şűρρŏŗţ ƌēĺţą ċąρţűŗē.{1}Ŷŏű ċąŋ şēĺēċţ ąŋŏţĥēŗ ţąŗğēţ ŏƃĵēċţ ţĥąţ ƌŏēş ŋŏţ şűρρŏŗţ ƌēĺţą ċąρţűŗē.]]]
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=[[[Ā ţąŗğēţ ŏƃĵēċţ ŵįţĥ ţĥįş ŋąɱē ąĺŗēąƌŷ ēχįşţş. Ĥŏŵēʋēŗ, įţ ċąŋŋŏţ ƃē űşēƌ{0}ƃēċąűşē ƌēĺţą ċąρţűŗē įş ēŋąƃĺēƌ, ŵĥēŗēąş ţĥē şŏűŗċē ŏƃĵēċţ ƌŏēş ŋŏţ{0}şűρρŏŗţ ƌēĺţą ċąρţűŗē.{1}Ŷŏű ċąŋ ēįţĥēŗ ēŋţēŗ ţĥē ŋąɱē ŏƒ ąŋ ēχįşţįŋğ ţąŗğēţ ŏƃĵēċţ ţĥąţ ƌŏēş ŋŏţ{0}şűρρŏŗţ ƌēĺţą ċąρţűŗē, ŏŗ ēŋţēŗ ą ŋąɱē ţĥąţ ƌŏēş ŋŏţ ēχįşţ ŷēţ.]]]
#XBUT
copySQLDDLUpdated=[[[Ĉŏρŷ ŜǬĻ Ĉŗēąţē Ţąƃĺē Ŝţąţēɱēŋţ∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
targetObjExistingNoCDCColumnUpdated=[[[Ĕχįşţįŋğ ţąƃĺēş įŋ Ģŏŏğĺē ƁįğǬűēŗŷ ɱűşţ įŋċĺűƌē ţĥē ƒŏĺĺŏŵįŋğ ċŏĺűɱŋş ƒŏŗ ċĥąŋğē ƌąţą ċąρţűŗē (ĈĎĈ):{0}{0}{1} ŏρēŗąţįŏŋ_ƒĺąğ{0}{1} įş_ƌēĺēţēƌ{0}{1} ŗēċŏŗƌşţąɱρ]]]
#XMSG
new_infoForUnsupportedDatasetNoKeys=[[[Ţĥē ƒŏĺĺŏŵįŋğ şŏűŗċē ŏƃĵēċţş ąŗē ŋŏţ şűρρŏŗţēƌ ƃēċąűşē ţĥēŷ ƌŏ ŋŏţ ĥąʋē ą ρŗįɱąŗŷ ķēŷ, ŏŗ ţĥēŷ ąŗē űşįŋğ ą ċŏŋŋēċţįŏŋ ţĥąţ ƌŏēş ŋŏţ ɱēēţ ċŏŋƌįţįŏŋş ţŏ ŗēţŗįēʋē ţĥē ρŗįɱąŗŷ ķēŷ:∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
new_infoForUnsupportedDatasetNoKeys2=[[[Ƥĺēąşē ŗēƒēŗ ţŏ ŜĀƤ ĶƁĀ 3531135 ƒŏŗ ą ρŏşşįƃĺē şŏĺűţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XLST: load type list values
initial=[[[Ĭŋįţįąĺ Ŏŋĺŷ∙∙∙∙∙∙∙]]]
@emailUpdateError=[[[Ĕŗŗŏŗ įŋ űρƌąţįŋğ Ĕɱąįĺ Ńŏţįƒįċąţįŏŋ ĺįşţ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XLST
initialDelta=[[[Ĭŋįţįąĺ ąŋƌ Ďēĺţą∙∙∙∙∙∙∙]]]

#XLST
deltaOnly=[[[Ďēĺţą Ŏŋĺŷ∙∙∙∙]]]
#XMSG
confluentDeltaLoadTypeInfo=[[[Ƒŏŗ Ĉŏŋƒĺűēŋţ Ķąƒķą şŏűŗċē, ŏŋĺŷ ĺŏąƌ ţŷρē Ĭŋįţįąĺ ąŋƌ Ďēĺţą įş şűρρŏŗţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
confirmRemoveReplicationObject=[[[Ďŏ ŷŏű ċŏŋƒįŗɱ ţĥąţ ŷŏű ŵąŋţ ţŏ ƌēĺēţē ţĥē ŗēρĺįċąţįŏŋ?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
confirmRemoveReplicationTaskPrompt=[[[Ţĥįş ąċţįŏŋ ŵįĺĺ ƌēĺēţē ēχįşţįŋğ ŗēρĺįċąţįŏŋş. Ďŏ ŷŏű ŵąŋţ ţŏ ċŏŋţįŋűē?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
confirmTargetConnectionChangePrompt=[[[Ţĥįş ąċţįŏŋ ŵįĺĺ ŗēşēţ ţĥē ţąŗğēţ ċŏŋŋēċţįŏŋ, ţąŗğēţ ċŏŋţąįŋēŗ ąŋƌ ƌēĺēţē ąĺĺ ţąŗğēţ ŏƃĵēċţş. Ďŏ ŷŏű ŵąŋţ ţŏ ċŏŋţįŋűē ?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
confirmTargetContainerChangePrompt=[[[Ţĥįş ąċţįŏŋ ŵįĺĺ ŗēşēţ ţĥē ţąŗğēţ ċŏŋţąįŋēŗ ąŋƌ ƌēĺēţē ąĺĺ ēχįşţįŋğ ţąŗğēţ ŏƃĵēċţş. Ďŏ ŷŏű ŵąŋţ ţŏ ċŏŋţįŋűē?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
confirmRemoveTransformObject=[[[Ďŏ ŷŏű ċŏŋƒįŗɱ ţĥąţ ŷŏű ŵąŋţ ţŏ ƌēĺēţē ρŗŏĵēċţįŏŋ {0}?]]]
#XMSG
ErrorMsgContainerChange=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ċĥąŋğįŋğ ţĥē ċŏŋţąįŋēŗ ρąţĥ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
infoForUnsupportedDatasetNoKeys=[[[Ţĥē ƒŏĺĺŏŵįŋğ şŏűŗċē ŏƃĵēċţş ąŗē ŋŏţ şűρρŏŗţēƌ ƃēċąűşē ţĥēŷ ƌŏ ŋŏţ ĥąʋē ą ρŗįɱąŗŷ ķēŷ:∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
infoForUnsupportedDatasetView=[[[Ţĥē ƒŏĺĺŏŵįŋğ şŏűŗċē ŏƃĵēċţş ŏƒ ţŷρē Ʋįēŵş ąŗē ŋŏţ şűρρŏŗţēƌ:∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=[[[Ţĥē ƒŏĺĺŏŵįŋğ şŏűŗċē ŏƃĵēċţ įş ŋŏţ şűρρŏŗţēƌ ąş įţ’ş ąŋ ŜǬĻ ʋįēŵ ċŏŋţąįŋįŋğ įŋρűţ ρąŗąɱēţēŗş:∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
infoForUnsupportedDatasetExtractionDisabled=[[[Ţĥē ƒŏĺĺŏŵįŋğ şŏűŗċē ŏƃĵēċţş ąŗē ŋŏţ şűρρŏŗţēƌ ƃēċąűşē ēχţŗąċţįŏŋ įş ƌįşąƃĺēƌ ƒŏŗ ţĥēɱ:∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=[[[Ƒŏŗ Ĉŏŋƒĺűēŋţ ċŏŋŋēċţįŏŋş, ţĥē ŏŋĺŷ ąĺĺŏŵēƌ şēŗįąĺįžąţįŏŋ ƒŏŗɱąţş ąŗē ĀƲŘŎ ąŋƌ ĴŜŎŃ. Ţĥē ƒŏĺĺŏŵįŋğ ŏƃĵēċţş ąŗē ŋŏţ şűρρŏŗţēƌ ƃēċąűşē ţĥēŷ űşē ą ƌįƒƒēŗēŋţ şēŗįąĺįžąţįŏŋ ƒŏŗɱąţ:∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
infoForUnsupportedDatasetSchemaNotFound=[[[Ůŋąƃĺē ţŏ ƒēţċĥ ţĥē şċĥēɱą ƒŏŗ ţĥē ƒŏĺĺŏŵįŋğ ŏƃĵēċţş. Ƥĺēąşē şēĺēċţ ţĥē ąρρŗŏρŗįąţē ċŏŋţēχţ ŏŗ ʋēŗįƒŷ ţĥē şċĥēɱą ŗēğįşţŗŷ ċŏŋƒįğűŗąţįŏŋ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTOL: warning dialog header on deleting replication task
deleteHeader=[[[Ďēĺēţē∙∙∙∙∙∙∙∙]]]
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=[[[Ţĥē Ďēĺēţē Āĺĺ Ɓēƒŏŗē Ļŏąƌįŋğ şēţţįŋğ įş ŋŏţ şűρρŏŗţēƌ ƒŏŗ Ģŏŏğĺē ƁįğǬűēŗŷ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XBUT
DeleteAllBeforeLoadingConfluentInfo=[[[Ţĥē Ďēĺēţē Āĺĺ Ɓēƒŏŗē şēţţįŋğ ƌēĺēţēş ąŋƌ ŗēċŗēąţēş ţĥē ŏƃĵēċţ (ţŏρįċ) ƃēƒŏŗē ēąċĥ ŗēρĺįċąţįŏŋ. Ţĥįş ąĺşŏ ƌēĺēţēş ąĺĺ ąşşįğŋēƌ ɱēşşąğēş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTOL
DeleteAllBeforeLoadingLTFInfo=[[[Ţĥē Ďēĺēţē Āĺĺ Ɓēƒŏŗē şēţţįŋğ įş ŋŏţ şűρρŏŗţēƌ ƒŏŗ ţĥįş ţąŗğēţ ţŷρē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=[[[Ţēċĥŋįċąĺ Ńąɱē∙∙∙∙∙]]]
#XCOL
connBusinessName=[[[Ɓűşįŋēşş Ńąɱē∙∙∙∙∙∙]]]
#XCOL
connDescriptionName=[[[Ďēşċŗįρţįŏŋ∙∙∙∙∙∙∙∙]]]
#XCOL
connType=[[[Ţŷρē]]]
#XMSG
connTblNoDataFoundtxt=[[[Ńŏ Ĉŏŋŋēċţįŏŋş Ƒŏűŋƌ∙∙∙∙]]]
#XMSG
connectionError=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ƒēţċĥįŋğ ċŏŋŋēċţįŏŋş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
connectionCombinationUnsupportedErrorTitle=[[[Ĉŏŋŋēċţįŏŋ ċŏɱƃįŋąţįŏŋ įş ŋŏţ şűρρŏŗţēƌ∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=[[[Řēρĺįċąţįŏŋ ƒŗŏɱ {0} ţŏ {1} įş ċűŗŗēŋţĺŷ ŋŏţ şűρρŏŗţēƌ.]]]
#XMSG
invalidTargetforSourceHDLFErrorTitle=[[[Ĉŏŋŋēċţįŏŋ ţŷρē ċŏɱƃįŋąţįŏŋ įş ŋŏţ şűρρŏŗţēƌ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=[[[Řēρĺįċąţįŏŋ ƒŗŏɱ ą ċŏŋŋēċţįŏŋ ŵįţĥ ţĥē ċŏŋŋēċţįŏŋ ţŷρē ŜĀƤ ĤĀŃĀ Ĉĺŏűƌ, Ďąţą Ļąķē Ƒįĺēş ţŏ {0} įş ŋŏţ şűρρŏŗţēƌ. Ŷŏű ċąŋ ŏŋĺŷ ŗēρĺįċąţē ţŏ ŜĀƤ Ďąţąşρĥēŗē.]]]

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=[[[Ŝēĺēċţ∙∙∙∙∙∙∙∙]]]
#XBUT
containerCancelBtn=[[[Ĉąŋċēĺ∙∙∙∙∙∙∙∙]]]
#XTOL
containerSelectTooltip=[[[Ŝēĺēċţ∙∙∙∙∙∙∙∙]]]
#XTOL
containerCancelTooltip=[[[Ĉąŋċēĺ∙∙∙∙∙∙∙∙]]]
#XMSG
containerContainerPathPlcHold=[[[Ĉŏŋţąįŋēŗ Ƥąţĥ∙∙∙∙∙]]]
#XFLD
containerContainertxt=[[[Ĉŏŋţąįŋēŗ∙∙∙∙∙]]]
#XFLD
confluentContainerContainertxt=[[[Ĉŏŋţēχţ∙∙∙∙∙∙∙]]]
#XMSG
infoMessageForSLTSelection=[[[Ŏŋĺŷ /ŜĻŢ/Μąşş Ţŗąŋşƒēŗ ĬĎ įş ąĺĺŏŵēƌ ąş ċŏŋţąįŋēŗ. Ŝēĺēċţ ą Μąşş Ţŗąŋşƒēŗ ĬĎ űŋƌēŗ ŜĻŢ (įƒ ąʋąįĺąƃĺē) ąŋƌ ċĺįċķ Ŝűƃɱįţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
msgFetchContainerFail=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ƒēţċĥįŋğ ċŏŋţąįŋēŗ ƌąţą.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
infoMessageForSLTHidden=[[[Ţĥįş ċŏŋŋēċţįŏŋ ƌŏēş ŋŏţ şűρρŏŗţ ŜĻŢ ƒŏĺƌēŗş, şŏ ţĥēŷ ƌŏ ŋŏţ ąρρēąŗ įŋ ţĥē ĺįşţ ƃēĺŏŵ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=[[[Ŝēĺēċţ ą ċŏŋţąįŋēŗ ţĥąţ ċŏŋţąįŋş şűƃƒŏĺƌēŗş įŋ įţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
sftpIncludeSubFolderText=[[[Ƒąĺşē∙∙∙∙∙∙∙∙∙]]]
#XMSG
sftpIncludeSubFolderTextNew=[[[Ńŏ∙∙]]]

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=[[[(Ńŏ ƒįĺţēŗ ɱąρρįŋğ ŷēţ)∙∙∙∙∙∙]]]
#XMSG
failToFetchRemoteMetadata=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ƒēţċĥįŋğ ɱēţąƌąţą.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
failToFetchData=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ƒēţċĥįŋğ ţĥē ēχįşţįŋğ ţąŗğēţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XCOL
@loadType=[[[Ļŏąƌ Ţŷρē∙∙∙∙∙]]]
#XCOL
@deleteAllBeforeLoading=[[[Ďēĺēţē Āĺĺ Ɓēƒŏŗē Ļŏąƌįŋğ∙∙∙∙∙∙∙]]]

#XMSG
@loading=[[[Ļŏąƌįŋğ...∙∙∙∙]]]
#XFLD
@selectSourceObjects=[[[Ŝēĺēċţ Ŝŏűŗċē Ŏƃĵēċţş∙∙∙∙∙]]]
#XMSG
@exceedLimit=[[[Ŷŏű ċąŋ’ţ įɱρŏŗţ ɱŏŗē ţĥąŋ {0} ŏƃĵēċţş ąţ ą ţįɱē. Ƥĺēąşē ƌēşēĺēċţ ąţ ĺēąşţ {1} ŏƃĵēċţş.]]]
#XFLD
@objects=[[[Ŏƃĵēċţş∙∙∙∙∙∙∙]]]
#XBUT
@ok=[[[ŎĶ∙∙]]]
#XBUT
@cancel=[[[Ĉąŋċēĺ∙∙∙∙∙∙∙∙]]]
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=[[[Ńēχţ]]]
#XBUT
btnAddSelection=[[[Āƌƌ Ŝēĺēċţįŏŋ∙∙∙∙∙∙]]]
#XTOL
@remoteFromSelection=[[[Řēɱŏʋē ƒŗŏɱ Ŝēĺēċţįŏŋ∙∙∙∙∙]]]
#XMSG
@searchInForSearchField=[[[Ŝēąŗċĥ įŋ {0}]]]

#XCOL
@name=[[[Ţēċĥŋįċąĺ Ńąɱē∙∙∙∙∙]]]
#XCOL
@type=[[[Ţŷρē]]]
#XCOL
@location=[[[Ļŏċąţįŏŋ∙∙∙∙∙∙]]]
#XCOL
@label=[[[Ɓűşįŋēşş Ńąɱē∙∙∙∙∙∙]]]
#XCOL
@status=[[[Ŝţąţűş∙∙∙∙∙∙∙∙]]]

#XFLD
@searchIn=[[[Ŝēąŗċĥ įŋ:∙∙∙∙]]]
#XBUT
@available=[[[Āʋąįĺąƃĺē∙∙∙∙∙]]]
#XBUT
@selection=[[[Ŝēĺēċţįŏŋ∙∙∙∙∙]]]

#XFLD
@noSourceSubFolder=[[[Ţąƃĺēş ąŋƌ Ʋįēŵş∙∙∙∙∙∙∙∙]]]
#XMSG
@alreadyAdded=[[[Āĺŗēąƌŷ ρŗēşēŋţ įŋ ţĥē ƌįąğŗąɱ∙∙∙∙∙∙∙∙∙]]]
#XMSG
@askForFilter=[[[Ţĥēŗē ąŗē ɱŏŗē ţĥąŋ {0} įţēɱş. Ƥĺēąşē ēŋţēŗ ą ƒįĺţēŗ şţŗįŋğ ţŏ ŋąŗŗŏŵ ƌŏŵŋ ţĥē ŋűɱƃēŗ ŏƒ įţēɱş.]]]
#XFLD: success label
lblSuccess=[[[Ŝűċċēşş∙∙∙∙∙∙∙]]]
#XFLD: ready label
lblReady=[[[Řēąƌŷ∙∙∙∙∙∙∙∙∙]]]
#XFLD: failure label
lblFailed=[[[Ƒąįĺēƌ∙∙∙∙∙∙∙∙]]]
#XFLD: fetching status label
lblFetchingDetail=[[[Ƒēţċĥįŋğ ƌēţąįĺş∙∙∙∙∙∙∙∙]]]

#XMSG Place holder text for tree filter control
filterPlaceHolder=[[[Ţŷρē ţēχţ ţŏ ƒįĺţēŗ ţŏρ-ĺēʋēĺ ŏƃĵēċţş∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG Place holder text for server search control
serverSearchPlaceholder=[[[Ţŷρē ąŋƌ ρŗēşş Ĕŋţēŗ ţŏ şēąŗċĥ∙∙∙∙∙∙∙∙∙]]]
#XMSG
@deployObjects=[[[Ĭɱρŏŗţįŋğ {0} ŏƃĵēċţş...]]]
#XMSG
@deployObjectsStatus=[[[Ńűɱƃēŗ ŏƒ ŏƃĵēċţş ţĥąţ ĥąʋē ƃēēŋ įɱρŏŗţēƌ: {0}. Ńűɱƃēŗ ŏƒ ŏƃĵēċţş ţĥąţ ċŏűĺƌ ŋŏţ ƃē įɱρŏŗţēƌ: {1}.]]]

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=[[[Ƒąįĺēƌ ţŏ ŏρēŋ ĺŏċąĺ ŗēρŏşįţŏŗŷ ƃŗŏŵşēŗ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
@openRemoteSourceBrowserError=[[[Ƒąįĺēƌ ţŏ ƒēţċĥ şŏűŗċē ŏƃĵēċţş.∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
@openRemoteTargetBrowserError=[[[Ƒąįĺēƌ ţŏ ƒēţċĥ ţąŗğēţ ŏƃĵēċţş.∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
@validatingTargetsError=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ʋąĺįƌąţįŋğ ţąŗğēţş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
@waitingToImport=[[[Řēąƌŷ ţŏ Ĭɱρŏŗţ∙∙∙∙]]]

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=[[[Ţĥē ɱąχįɱűɱ ŋűɱƃēŗ ŏƒ ŏƃĵēċţş ĥąş ƃēēŋ ēχċēēƌēƌ. Ŝēĺēċţ ą ɱąχįɱűɱ ŏƒ 500 ŏƃĵēċţş ƒŏŗ ŏŋē ŗēρĺįċąţįŏŋ ƒĺŏŵ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=[[[Ţēċĥŋįċąĺ Ńąɱē∙∙∙∙∙]]]
#XFLD
sourceObjectBusinessName=[[[Ɓűşįŋēşş Ńąɱē∙∙∙∙∙∙]]]
#XFLD
sourceNoColumns=[[[Ńűɱƃēŗ ŏƒ Ĉŏĺűɱŋş∙∙∙∙∙∙∙]]]
#XFLD
containerLbl=[[[Ĉŏŋţąįŋēŗ∙∙∙∙∙]]]

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=[[[Ŷŏű ɱűşţ şēĺēċţ ą şŏűŗċē ċŏŋŋēċţįŏŋ ƒŏŗ ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validationSourceContainerNonExist=[[[Ŷŏű ɱűşţ şēĺēċţ ą ċŏŋţąįŋēŗ ƒŏŗ ţĥē şŏűŗċē ċŏŋŋēċţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validationTargetNonExist=[[[Ŷŏű ɱűşţ şēĺēċţ ą ţąŗğēţ ċŏŋŋēċţįŏŋ ƒŏŗ ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validationTargetContainerNonExist=[[[Ŷŏű ɱűşţ şēĺēċţ ą ċŏŋţąįŋēŗ ƒŏŗ ţĥē ţąŗğēţ ċŏŋŋēċţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validationTruncateDisabledForObjectTitle=[[[Řēρĺįċąţįŏŋ ţŏ ŏƃĵēċţ şţŏŗąğēş.∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=[[[Řēρĺįċąţįŏŋ įŋţŏ ą ċĺŏűƌ şţŏŗąğē įş ŏŋĺŷ ρŏşşįƃĺē įƒ ēįţĥēŗ ţĥē Ďēĺēţē Āĺĺ Ɓēƒŏŗē Ļŏąƌįŋğ ŏρţįŏŋ įş şēţ ŏŗ ţĥē ţąŗğēţ ŏƃĵēċţ ƌŏēş ŋŏţ ēχįşţ įŋ ţĥē ţąŗğēţ.{0}{0} Ţŏ şţįĺĺ ēŋąƃĺē ŗēρĺįċąţįŏŋ ƒŏŗ ŏƃĵēċţş ƒŏŗ ŵĥįċĥ ţĥē Ďēĺēţē Āĺĺ Ɓēƒŏŗē Ļŏąƌįŋğ ŏρţįŏŋ įş ŋŏţ şēţ, ɱąķē şűŗē ţĥąţ ţĥē ţąŗğēţ ŏƃĵēċţ ƌŏēş ŋŏţ ēχįşţ įŋ ţĥē şŷşţēɱ ƃēƒŏŗē ŷŏű ŗűŋ ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ.]]]
#XMSG
validationTaskNonExist=[[[Ŷŏű ɱűşţ ĥąʋē ąţ ĺēąşţ ŏŋē ŗēρĺįċąţįŏŋ įŋ ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validationTaskTargetMissing=[[[Ŷŏű ɱűşţ ĥąʋē ą ţąŗğēţ ƒŏŗ ţĥē ŗēρĺįċąţįŏŋ ŵįţĥ ţĥē şŏűŗċē: {0}]]]
#XMSG
validationTaskTargetIsSAC=[[[Ŝēĺēċţēƌ ţąŗğēţ įş ą ŜĀĈ Āŗţēƒąċţ: {0}]]]
#XMSG
validationTaskTargetIsNonDwcTableKey=[[[Ŝēĺēċţēƌ ţąŗğēţ įş ŋŏţ ą şűρρŏŗţēƌ ĺŏċąĺ ţąƃĺē: {0}]]]
#XMSG
validationTaskTargetIsNonDwcTableDescription=[[[Āŋ ŏƃĵēċţ ŵįţĥ ţĥįş ŋąɱē ąĺŗēąƌŷ ēχįşţş įŋ ţĥē ţąŗğēţ. Ĥŏŵēʋēŗ, ţĥįş ŏƃĵēċţ ċąŋŋŏţ ƃē űşēƌ ąş ą ţąŗğēţ ŏƃĵēċţ ƒŏŗ ą ŗēρĺįċąţįŏŋ ƒĺŏŵ ţŏ ţĥē ĺŏċąĺ ŗēρŏşįţŏŗŷ, ąş įţ įş ŋŏţ ą ĺŏċąĺ ţąƃĺē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validateSourceTargetSystemDifference=[[[Ŷŏű ɱűşţ şēĺēċţ ƌįƒƒēŗēŋţ şŏűŗċē ąŋƌ ţąŗğēţ ċŏŋŋēċţįŏŋ ąŋƌ ċŏŋţąįŋēŗ ċŏɱƃįŋąţįŏŋş ƒŏŗ ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validateDuplicateSources=[[[ŏŋē ŏŗ ɱŏŗē ŗēρĺįċąţįŏŋş ĥąʋē ƌűρĺįċąţē şŏűŗċē ŏƃĵēċţ ŋąɱēş: {0}.]]]
#XMSG
validateDuplicateTargets=[[[ŏŋē ŏŗ ɱŏŗē ŗēρĺįċąţįŏŋş ĥąʋē ƌűρĺįċąţē ţąŗğēţ ŏƃĵēċţ ŋąɱēş: {0}.]]]
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=[[[Ţĥē şŏűŗċē ŏƃĵēċţ {0} ƌŏēş ŋŏţ şűρρŏŗţ ƌēĺţą ċąρţűŗē, ŵĥįĺē ţĥē ţąŗğēţ ŏƃĵēċţ {1} ƌŏēş. Ŷŏű ɱűşţ ŗēɱŏʋē ţĥē ŗēρĺįċąţįŏŋ.]]]
#XMSG
validationTaskTargetObjectLoadTypeMismatch=[[[Ŷŏű ɱűşţ şēĺēċţ ţĥē ĺŏąƌ ţŷρē "Ĭŋįţįąĺ ąŋƌ Ďēĺţą" ƒŏŗ ţĥē ŗēρĺįċąţįŏŋ ŵįţĥ ţąŗğēţ ŏƃĵēċţ ŋąɱē {0}.]]]
#XMSG
validationAutoRenameTarget=[[[Ţąŗğēţ ċŏĺűɱŋş ĥąʋē ƃēēŋ ŗēŋąɱēƌ.∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=[[[Āŋ ąűţŏ-ρŗŏĵēċţįŏŋ ĥąş ƃēēŋ ąƌƌēƌ, ąŋƌ ţĥē ƒŏĺĺŏŵįŋğ ţąŗğēţ ċŏĺűɱŋş ĥąʋē ƃēēŋ ŗēŋąɱēƌ ţŏ ąĺĺŏŵ ŗēρĺįċąţįŏŋ ţŏ ţĥē ţąŗğēţ:{1}{1} {0} {1}{1}Ţĥįş įş ƌűē ţŏ ŏŋē ŏƒ ţĥē ƒŏĺĺŏŵįŋğ ŗēąşŏŋş:{1}{1}{2} Ńŏţ şűρρŏŗţēƌ ċĥąŗąċţēŗş{1}{2} Řēşēŗʋēƌ ρŗēƒįχ]]]
#XMSG
validationAutoRenameTargetDescriptionUpdated=[[[Āŋ ąűţŏ-ρŗŏĵēċţįŏŋ ĥąş ƃēēŋ ąƌƌēƌ, ąŋƌ ţĥē ƒŏĺĺŏŵįŋğ ţąğēţ ċŏĺűɱŋş ĥąʋē ƃēēŋ ŗēŋąɱēƌ ţŏ ąĺĺŏŵ ŗēρĺįċąţįŏŋ ţŏ Ģŏŏğĺē ƁįğǬűēŗŷ:{1}{1} {0} {1}{1}Ţĥįş įş ƌűē ţŏ ŏŋē ŏƒ ţĥē ƒŏĺĺŏŵįŋğ ŗēąşŏŋş:{1}{1}{2} Řēşēŗʋēƌ ċŏĺűɱŋ ŋąɱē{1}{2} Ńŏţ şűρρŏŗţēƌ ċĥąŗąċţēŗş{1}{2} Řēşēŗʋēƌ ρŗēƒįχ]]]
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=[[[Āŋ ąűţŏ-ρŗŏĵēċţįŏŋ ĥąş ƃēēŋ ąƌƌēƌ, ąŋƌ ţĥē ƒŏĺĺŏŵįŋğ ţąŗğēţ ċŏĺűɱŋş ĥąʋē ƃēēŋ ŗēŋąɱēƌ ţŏ ąĺĺŏŵ ŗēρĺįċąţįŏŋş ţŏ Ĉŏŋƒĺűēŋţ:{1}{1} {0} {1}{1}Ţĥįş įş ƌűē ţŏ ŏŋē ŏƒ ţĥē ƒŏĺĺŏŵįŋğ ŗēąşŏŋş:{1}{1}{2} Řēşēŗʋēƌ ċŏĺűɱŋ ŋąɱē{1}{2} Ńŏţ şűρρŏŗţēƌ ċĥąŗąċţēŗş{1}{2} Řēşēŗʋēƌ ρŗēƒįχ]]]
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=[[[Āŋ ąűţŏ-ρŗŏĵēċţįŏŋ ĥąş ƃēēŋ ąƌƌēƌ, ąŋƌ ţĥē ƒŏĺĺŏŵįŋğ ţąŗğēţ ċŏĺűɱŋş ĥąʋē ƃēēŋ ŗēŋąɱēƌ ţŏ ąĺĺŏŵ ŗēρĺįċąţįŏŋş ţŏ ţĥē ţąŗğēţ:{1}{1} {0} {1}{1}Ţĥįş įş ƌűē ţŏ ŏŋē ŏƒ ţĥē ƒŏĺĺŏŵįŋğ ŗēąşŏŋş:{1}{1}{2} Řēşēŗʋēƌ ċŏĺűɱŋ ŋąɱē{1}{2} Ńŏţ şűρρŏŗţēƌ ċĥąŗąċţēŗş{1}{2} Řēşēŗʋēƌ ρŗēƒįχ]]]
#XMSG
validationAutoRenameTargetSubTitle=[[[{0}]]]
#XMSG
autoRenameInfo=[[[Ţąŗğēţ ŏƃĵēċţ ĥąş ƃēēŋ ŗēŋąɱēƌ.∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
autoRenameInfoDesc=[[[Ţĥē ţąŗğēţ ŏƃĵēċţ ĥąş ƃēēŋ ŗēŋąɱēƌ ƃēċąűşē įţ ċŏŋţąįŋēƌ ŋŏţ şűρρŏŗţēƌ ċĥąŗąċţēŗş. Ŏŋĺŷ ţĥē ƒŏĺĺŏŵįŋğ ċĥąŗąċţēŗş ąŗē şűρŏŏŗţēƌ:{0}{0}{1}Ā-Ż{0}{1}ą-ž{0}{1}0-9{0}{1}.(ƌŏţ){0}{1}_(űŋƌēŗşċŏŗē){0}{1}-(ƌąşĥ)]]]
#XMSG
validationAutoTargetTypeConversion=[[[Ţąŗğēţ ƌąţą ţŷρēş ĥąʋē ƃēēŋ ċĥąŋğēƌ.∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=[[[Ƒŏŗ ţĥē ƒŏĺĺŏŵįŋğ ţąŗğēţ ċŏĺűɱŋş, ţĥē ţąŗğēţ ƌąţą ţŷρēş ĥąʋē ƃēēŋ ċĥąŋğēƌ ƃēċąűşē įŋ Ģŏŏğĺē ƁįğǬűēŗŷ ţĥē şŏűŗċē ƌąţą ţŷρēş ąŗē ŋŏţ şűρρŏŗţēƌ:{1}{1}{0}]]]
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=[[[Ƒŏŗ ţĥē ƒŏĺĺŏŵįŋğ ţąŗğēţ ċŏĺűɱŋş, ţĥē ţąŗğēţ ƌąţą ţŷρēş ĥąʋē ƃēēŋ ċĥąŋğēƌ ƃēċąűşē ţĥē şŏűŗċē ƌąţą ţŷρēş ąŗē ŋŏţ şűρρŏŗţēƌ įŋ ţĥē ţąŗğēţ ċŏŋŋēċţįŏŋ:{1}{1}{0}]]]
#XMSG
validationAutoTargetTypeConversionSubTitle=[[[{0}]]]
#XMSG
validationMaxCharLengthGBQTarget=[[[Ŝĥŏŗţēŋ ţąŗğēţ ċŏĺűɱŋ ŋąɱēş.∙∙∙∙∙∙∙∙]]]
#XMSG
validationMaxCharLengthGBQTargetDescription=[[[Ĭŋ Ģŏŏğĺē ƁįğǬűēŗŷ, ċŏĺűɱŋ ŋąɱēş ċąŋ űşē ą ɱąχįɱűɱ ŏƒ 300 ċĥąŗąċţēŗş. Ůşē ą ρŗŏĵēċţįŏŋ ţŏ şĥŏŗţēŋ ţĥē ƒŏĺĺŏŵįŋğ ţąŗğēţ ċŏĺűɱŋ ŋąɱēş:{1}{0}]]]
#XMSG
validationMaxCharLengthGBQTargetSubTitle=[[[{0}]]]
#XMSG
validationGBQUnableToCreateKey=[[[Ƥŗįɱąŗŷ ķēŷş ŵįĺĺ ŋŏţ ƃē ċŗēąţēƌ.∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=[[[Ĭŋ Ģŏŏğĺē ƁįğǬűēŗŷ, ą ɱąχįɱűɱ ŏƒ 16 ρŗįɱąŗŷ ķēŷş ąŗē şűρρŏŗţēƌ, ƃűţ ţĥē şŏűŗċē ŏƃĵēċţ ĥąş ą ĺąŗğēŗ ŋűɱƃēŗ ŏƒ ρŗįɱąŗŷ ķēŷş. Ńŏŋē ŏƒ ţĥē ρŗįɱąŗŷ ķēŷş ŵįĺĺ ƃē ċŗēąţēƌ įŋ ţĥē ţąŗğēţ ŏƃĵēċţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validationGBQUnableToCreateKeySubTitle=[[[{0}]]]
#XMSG
validationIncompatiblePKTypeDescUpdated4=[[[Ŏŋē ŏŗ ɱŏŗē şŏűŗċē ċŏĺűɱŋş ĥąʋē ƌąţą ţŷρēş ţĥąţ ċąŋŋŏţ ƃē ƌēƒįŋēƌ ąş ρŗįɱąŗŷ ķēŷş įŋ Ģŏŏğĺē ƁįğǬűēŗŷ. Ńŏŋē ŏƒ ţĥē ρŗįɱąŗŷ ķēŷş ŵįĺĺ ƃē ċŗēąţēƌ įŋ ţĥē ţąŗğēţ ŏƃĵēċţ.{0}{0}Ţĥē ƒŏĺĺŏŵįŋğ ţąŗğēţ ƌąţą ţŷρēş ąŗē ċŏɱρąţįƃĺē ŵįţĥ Ģŏŏğĺē ƁįğǬűēŗŷ ƌąţą ţŷρēş ƒŏŗ ŵĥįċĥ ą ρŗįɱąŗŷ ķēŷ ċąŋ ƃē ƌēƒįŋēƌ:{0}{0}{1} ƁŎŎĻĔĀŃ{0}{1} ĎĀŢĔ{0}{1} ĎĔĈĬΜĀĻ{0}{1} ĬŃŢ32{0}{1} ĬŃŢ64{0}{1} ŃŮΜĔŘĬĈ{0}{1} ŜŢŘĬŃĢ{0}{1} ŢĬΜĔŜŢĀΜƤ]]]
#XMSG
validateObjectStoreNoPKDatasetError=[[[Ďēƒįŋē ŏŋē ŏŗ ɱŏŗē ċŏĺűɱŋş ąş ą ρŗįɱąŗŷ ķēŷ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=[[[Ŷŏű ɱűşţ ƌēƒįŋē ŏŋē ŏŗ ɱŏŗē ċŏĺűɱŋ ąş ρŗįɱąŗŷ ķēŷ űşē şŏűŗċē şċĥēɱą ƌįąĺŏğ ţŏ ƌŏ ţĥįş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=[[[Ďēƒįŋē ŏŋē ŏŗ ɱŏŗē ċŏĺűɱŋş ąş ą ρŗįɱąŗŷ ķēŷ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=[[[Ŷŏű ɱűşţ ƌēƒįŋē ŏŋē ŏŗ ɱŏŗē ċŏĺűɱŋş ąş ρŗįɱąŗŷ ķēŷ ţĥąţ ɱąţċĥ ţĥē ρŗįɱąŗŷ ķēŷ ċŏŋşţŗąįŋţş ƒŏŗ ŷŏűŗ şŏűŗċē ŏƃĵēċţ. Ģŏ ţŏ Ĉŏŋƒįğűŗē Ŝċĥēɱą įŋ ŷŏűŗ şŏűŗċē ŏƃĵēċţ ρŗŏρēŗţįēş ţŏ ƌŏ şŏ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle=[[[{0}]]]
#XMSG
validateMaxPartitionValueError=[[[Ĕŋţēŗ ą ʋąĺįƌ ɱąχ ρąŗţįţįŏŋ ʋąĺűē.∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=[[[Μąχ Ƥąŗţįţįŏŋ ʋąĺűē ɱűşţ ƃē ≥ 1 ąŋƌ ≤ 2147483647∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validateHDLFNoPKDatasetError=[[[Ďēƒįŋē ŏŋē ŏŗ ɱŏŗē ċŏĺűɱŋş ąş ą ρŗįɱąŗŷ ķēŷ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validateHDLFNoPKDatasetSubtitle=[[[{0}]]]
#XMSG
validateHDLFNoPKDatasetErrorDescription1=[[[Ţŏ ŗēρĺįċąţē ąŋ ŏƃĵēċţ, ŷŏű ɱűşţ ƌēƒįŋē ŏŋē ŏŗ ɱŏŗē ţąŗğēţ ċŏĺűɱŋş ąş ą ρŗįɱąŗŷ ķēŷ. Ůşē ą ρŗŏĵēċţįŏŋ ţŏ ƌŏ ţĥįş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validateHDLFNoPKExistingDatasetError=[[[Ďēƒįŋē ŏŋē ŏŗ ɱŏŗē ċŏĺűɱŋş ąş ą ρŗįɱąŗŷ ķēŷ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validateHDLFNoPKExistingDatasetSubtitle=[[[{0}]]]
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=[[[Ţŏ ŗēρĺįċąţē ƌąţą ţŏ ąŋ ēχįşţįŋğ ţąŗğēţ ŏƃĵēċţ, įţ ɱűşţ ĥąʋē ŏŋē ŏŗ ɱŏŗē ċŏĺűɱŋş ţĥąţ ąŗē ƌēƒįŋēƌ ąş ţĥē ρŗįɱąŗŷ ķēŷ. {0} Ŷŏű ĥąʋē ţĥē ƒŏĺĺŏŵįŋğ ŏρţįŏŋş ƒŏŗ ƌēƒįŋįŋğ ŏŋē ŏŗ ɱŏŗē ċŏĺűɱŋş ąş ţĥē ρŗįɱąŗŷ ķēŷ: {0} {1} Ůşē ţĥē ĺŏċąĺ ţąƃĺē ēƌįţŏŗ ţŏ ċĥąŋğē ţĥē ēχįşţįŋğ ţąŗğēţ ŏƃĵēċţ. Ţĥēŋ ŗēĺŏąƌ ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ.{0}{1} Řēŋąɱē ţĥē ţąŗğēţ ŏƃĵēċţ įŋ ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ. Ţĥįş ŵįĺĺ ċŗēąţē ą ŋēŵ ŏƃĵēċţ ąş şŏŏŋ ąş ą ŗűŋ įş şţąŗţēƌ. Āƒţēŗ ŗēŋąɱįŋğ, ŷŏű ċąŋ ƌēƒįŋē ŏŋē ŏŗ ɱŏŗē ċŏĺűɱŋş ąş ţĥē ρŗįɱąŗŷ ķēŷ įŋ ą ρŗŏĵēċţįŏŋ.{0}{1} Μąρ ţĥē ŏƃĵēċţ ţŏ ąŋŏţĥēŗ ēχįşţįŋğ ţąŗğēţ ŏƃĵēċţ įŋ ŵĥįċĥ ŏŋē ŏŗ ɱŏŗē ċŏĺűɱŋş ąŗē ąĺŗēąƌŷ ƌēƒįŋēƌ ąş ţĥē ρŗįɱąŗŷ ķēŷ.]]]
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=[[[Ŝēĺēċţēƌ ţąŗğēţ ąĺŗēąƌŷ ēχįşţş įŋ ţĥē ŗēρŏşįţŏŗŷ: {0}.]]]
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=[[[Ţĥē ƌēĺţą ċąρţűŗē ţąƃĺē ŋąɱēş ąŗē ąĺŗēąƌŷ űşēƌ ƃŷ ŏţĥēŗ ţąƃĺēş įŋ ţĥē ŗēρŏşįţŏŗŷ: {0}. Ŷŏű ɱűşţ ŗēŋąɱē ţĥēşē ţąŗğēţ ŏƃĵēċţş ţŏ ēŋşűŗē ţĥąţ ţĥē ąşşŏċįąţēƌ ƌēĺţą ċąρţűŗē ţąƃĺē ŋąɱēş ąŗē űŋįƣűē ƃēƒŏŗē ŷŏű ċąŋ şąʋē ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ.]]]
#XMSG
validateConfluentEmptySchema=[[[Ďēƒįŋē Ŝċĥēɱą∙∙∙∙∙∙]]]
#XMSG
validateConfluentEmptySchemaDescUpdated=[[[Ţĥē şŏűŗċē ţąƃĺē ƌŏēş ŋŏţ ĥąʋē ą şċĥēɱą. Ĉĥŏŏşē Ĉŏŋƒįğűŗē Ŝċĥēɱą ţŏ ƌēƒįŋē ŏŋē∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validationCSVEncoding=[[[Ĭŋʋąĺįƌ ĈŜƲ Ĕŋċŏƌįŋğ∙∙∙∙]]]
#XMSG
validationCSVEncodingDescription=[[[Ţĥē ĈŜƲ ēŋċŏƌįŋğ ŏƒ ţĥē ţąşķ įş ŋŏţ ʋąĺįƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validateConfluentEmptySchema=[[[Ŝēĺēċţ ą ċŏɱρąţįƃĺē ţąŗğēţ ƌąţą ţŷρē∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validateTargetDataTypeSubtitle=[[[{0}]]]
#XMSG:
globalValidateTargetDataType=[[[Ŝēĺēċţ ą ċŏɱρąţįƃĺē ţąŗğēţ ƌąţą ţŷρē∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
globalValidateTargetDataTypeDesc=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵįţĥ ċŏĺűɱŋ ɱąρρįŋğş. Ģŏ ţŏ Ƥŗŏĵēċţįŏŋ ąŋƌ ēŋşűŗē ţĥąţ ąĺĺ şŏűŗċē ċŏĺűɱŋş ąŗē ɱąρρēƌ ŵįţĥ ą űŋįƣűē ċŏĺűɱŋ, ŵįţĥ ą ċŏĺűɱŋ ţĥąţ ĥąş ą ċŏɱρąţįƃĺē ƌąţą ţŷρē, ąŋƌ ţĥąţ ąĺĺ ƌēƒįŋēƌ ēχρŗēşşįŏŋş ąŗē ʋąĺįƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=[[[Ďűρĺįċąţē Ĉŏĺűɱŋ Ńąɱēş.∙∙∙∙∙∙]]]
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=[[[Ďűρĺįċąţē Ĉŏĺűɱŋ ŋąɱēş ąŗē ŋŏţ şűρρŏŗţēƌ. Ůşē ţĥē Ƥŗŏĵēċţįŏŋ Ďįąĺŏğ ţŏ ƒįχ ţĥēɱ. Ţĥē ƒŏĺĺŏŵįŋğ ţąŗğēţ ŏƃĵēċţş ĥąʋē ƌűρĺįċąţē ċŏĺűɱŋ ŋąɱēş: {0}.]]]
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=[[[Ďűρĺįċąţē Ĉŏĺűɱŋ Ńąɱēş.∙∙∙∙∙∙]]]
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=[[[Ďűρĺįċąţē Ĉŏĺűɱŋ ŋąɱēş ąŗē ŋŏţ şűρρŏŗţēƌ. Ţĥē ƒŏĺĺŏŵįŋğ ţąŗğēţ ŏƃĵēċţş ĥąʋē ƌűρĺįċąţē ċŏĺűɱŋ ŋąɱēş: {0}.]]]
#XMSG
deltaOnlyLoadTypeTittle=[[[Ţĥēŗē ɱąŷ ƃē įŋċŏŋşįşţēŋċįēş įŋ ţĥē ƌąţą.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=[[[Ţĥē Ďēĺţą Ŏŋĺŷ ĺŏąƌ ţŷρē ŵįĺĺ ŋŏţ ċŏŋşįƌēŗ ţĥē ċĥąŋğēş ɱąƌē įŋ ţĥē şŏűŗċē ƃēţŵēēŋ ţĥē ĺąşţ şąʋē ąŋƌ ţĥē ŋēχţ ŗűŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
deltaOnlyLoadTypeSubtitle=[[[{0}]]]
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=[[[Ĉĥąŋğē ĺŏąƌ ţŷρē ţŏ "Ĭŋįţįąĺ".∙∙∙∙∙∙∙∙∙]]]
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=[[[Řēρĺįċąţįŋğ ĀƁĀƤ-ƃąşēƌ ŏƃĵēċţş ţĥąţ ƌŏ ŋŏţ ĥąʋē ą ρŗįɱąŗŷ ķēŷ įş ŏŋĺŷ ρŏşşįƃĺē ƒŏŗ ĺŏąƌ ţŷρē "Ĭŋįţįąĺ Ŏŋĺŷ".∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle=[[[{0}]]]
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=[[[Ďįşąƃĺē Ďēĺţą Ĉąρţűŗē.∙∙∙∙∙]]]
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=[[[Ţŏ ŗēρĺįċąţē ąŋ ŏƃĵēċţ ţĥąţ ƌŏēş ŋŏţ ĥąʋē ą ρŗįɱąŗŷ ķēŷ űşįŋğ şŏűŗċē ċŏŋŋēċţįŏŋ ţŷρē ĀƁĀƤ, ŷŏű ɱűşţ ƒįŗşţ ƌįşąƃĺē ƌēĺţą ċąρţűŗįŋğ ƒŏŗ ţĥįş ţąƃĺē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle=[[[{0}]]]
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=[[[Ĉĥąŋğē ţąŗğēţ ŏƃĵēċţ.∙∙∙∙∙]]]
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget=[[[{0}]]]
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=[[[Ţĥē ţąŗğēţ ŏƃĵēċţ ċąŋŋŏţ ƃē űşēƌ ƃēċąűşē ƌēĺţą ċąρţűŗįŋğ įş ēŋąƃĺēƌ. Ŷŏű ċąŋ ēįţĥēŗ ŗēŋąɱē ţĥē ţąŗğēţ ŏƃĵēċţ ąŋƌ ţĥēŋ şŵįţċĥ ŏƒƒ ƌēĺţą ċąρţűŗįŋğ ƒŏŗ ţĥē ŋēŵ (ŗēŋąɱēƌ) ŏƃĵēċţ, ŏŗ ɱąρ ţĥē şŏűŗċē ŏƃĵēċţ ţŏ ą ţąŗğēţ ŏƃĵēċţ ƒŏŗ ŵĥįċĥ ƌēĺţą ċąρţűŗįŋğ įş ƌįşąƃĺēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=[[[Ĉĥąŋğē ţąŗğēţ ŏƃĵēċţ.∙∙∙∙∙]]]
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle=[[[{0}]]]
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=[[[Ţĥē ţąŗğēţ ŏƃĵēċţ ċąŋŋŏţ ƃē űşēƌ ƃēċąűşē įţ ƌŏēş ŋŏţ ĥąʋē ţĥē ŗēƣűįŗēƌ ţēċĥŋįċąĺ ċŏĺűɱŋ __ĺŏąƌ_ρąċķąğē_įƌ. Ŷŏű ċąŋ ŗēŋąɱē ţĥē ţąŗğēţ ŏƃĵēċţ űşįŋğ ą ŋąɱē ţĥąţ ƌŏēş ŋŏţ ēχįşţ ŷēţ. Ţĥē şŷşţēɱ ţĥēŋ ċŗēąţēş ą ŋēŵ ŏƃĵēċţ ţĥąţ ĥąş ţĥē şąɱē ƌēƒįŋįţįŏŋ ąş ţĥē şŏűŗċē ŏƃĵēċţ ąŋƌ ċŏŋţąįŋş ţĥē ţēċĥŋįċąĺ ċŏĺűɱŋ. Āĺţēŗŋąţįʋēĺŷ, ŷŏű ċąŋ ɱąρ ţĥē ţąŗğēţ ŏƃĵēċţ ţŏ ąŋ ēχįşţįŋğ ŏƃĵēċţ ţĥąţ ĥąş ţĥē ŗēƣűįŗēƌ ţēċĥŋįċąĺ ċŏĺűɱŋ (__ĺŏąƌ_ρąċķąğē_įƌ).∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=[[[Ĉĥąŋğē ţąŗğēţ ŏƃĵēċţ.∙∙∙∙∙]]]
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle=[[[{0}]]]
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=[[[Ţĥē ţąŗğēţ ŏƃĵēċţ ċąŋŋŏţ ƃē űşēƌ ƃēċąűşē įţ ƌŏēş ŋŏţ ĥąʋē ţĥē ŗēƣűįŗēƌ ţēċĥŋįċąĺ ċŏĺűɱŋ __ĺŏąƌ_ŗēċŏŗƌ_įƌ. Ŷŏű ċąŋ ŗēŋąɱē ţĥē ţąŗğēţ ŏƃĵēċţ űşįŋğ ą ŋąɱē ţĥąţ ƌŏēş ŋŏţ ēχįşţ ŷēţ. Ţĥē şŷşţēɱ ţĥēŋ ċŗēąţēş ą ŋēŵ ŏƃĵēċţ ţĥąţ ĥąş ţĥē şąɱē ƌēƒįŋįţįŏŋ ąş ţĥē şŏűŗċē ŏƃĵēċţ ąŋƌ ċŏŋţąįŋş ţĥē ţēċĥŋįċąĺ ċŏĺűɱŋ. Āĺţēŗŋąţįʋēĺŷ, ŷŏű ċąŋ ɱąρ ţĥē ţąŗğēţ ŏƃĵēċţ ţŏ ąŋ ēχįşţįŋğ ŏƃĵēċţ ţĥąţ ĥąş ţĥē ŗēƣűįŗēƌ ţēċĥŋįċąĺ ċŏĺűɱŋ (__ĺŏąƌ_ŗēċŏŗƌ_įƌ).∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=[[[Ĉĥąŋğē ţąŗğēţ ŏƃĵēċţ.∙∙∙∙∙]]]
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle=[[[{0}]]]
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=[[[Ţĥē ţąŗğēţ ŏƃĵēċţ ċąŋŋŏţ ƃē űşēƌ ƃēċąűşē ţĥē ƌąţą ţŷρē ŏƒ įţş ţēċĥŋįċąĺ ċŏĺűɱŋ __ĺŏąƌ_ŗēċŏŗƌ_įƌ įş ŋŏţ "şţŗįŋğ(44)". Ŷŏű ċąŋ ŗēŋąɱē ţĥē ţąŗğēţ ŏƃĵēċţ űşįŋğ ą ŋąɱē ţĥąţ ƌŏēş ŋŏţ ēχįşţ ŷēţ. Ţĥē şŷşţēɱ ţĥēŋ ċŗēąţēş ą ŋēŵ ŏƃĵēċţ ţĥąţ ĥąş ţĥē şąɱē ƌēƒįŋįţįŏŋ ąş ţĥē şŏűŗċē ŏƃĵēċţ ąŋƌ ċŏŋşēƣűēŋţĺŷ ţĥē ċŏŗŗēċţ ƌąţą ţŷρē. Āĺţēŗŋąţįʋēĺŷ, ŷŏű ċąŋ ɱąρ ţĥē ţąŗğēţ ŏƃĵēċţ ţŏ ąŋ ēχįşţįŋğ ŏƃĵēċţ ţĥąţ ĥąş ţĥē ŗēƣűįŗēƌ ţēċĥŋįċąĺ ċŏĺűɱŋ (__ĺŏąƌ_ŗēċŏŗƌ_įƌ) ŵįţĥ ţĥē ċŏŗŗēċţ ƌąţą ţŷρē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=[[[Ĉĥąŋğē ţąŗğēţ ŏƃĵēċţ.∙∙∙∙∙]]]
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget=[[[{0}]]]
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=[[[Ţĥē ţąŗğēţ ŏƃĵēċţ ċąŋŋŏţ ƃē űşēƌ ƃēċąűşē įţ ĥąş ą ρŗįɱąŗŷ ķēŷ, ŵĥįĺē ţĥē şŏűŗċē ŏƃĵēċţ ĥąş ŋŏŋē. Ŷŏű ċąŋ ŗēŋąɱē ţĥē ţąŗğēţ ŏƃĵēċţ űşįŋğ ą ŋąɱē ţĥąţ ƌŏēş ŋŏţ ēχįşţ ŷēţ. Ţĥē şŷşţēɱ ţĥēŋ ċŗēąţēş ą ŋēŵ ŏƃĵēċţ ţĥąţ ĥąş ţĥē şąɱē ƌēƒįŋįţįŏŋ ąş ţĥē şŏűŗċē ŏƃĵēċţ ąŋƌ ċŏŋşēƣűēŋţĺŷ ŋŏ ρŗįɱąŗŷ ķēŷ. Āĺţēŗŋąţįʋēĺŷ, ŷŏű ċąŋ ɱąρ ţĥē ţąŗğēţ ŏƃĵēċţ ţŏ ąŋ ēχįşţįŋğ ŏƃĵēċţ ţĥąţ ĥąş ţĥē ŗēƣűįŗēƌ ţēċĥŋįċąĺ ċŏĺűɱŋ (__ĺŏąƌ_ρąċķąğē_įƌ) ąŋƌ ƌŏēş ŋŏţ ĥąʋē ą ρŗįɱąŗŷ ķēŷ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=[[[Ĉĥąŋğē ţąŗğēţ ŏƃĵēċţ.∙∙∙∙∙]]]
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget=[[[{0}]]]
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=[[[Ţĥē ţąŗğēţ ŏƃĵēċţ ċąŋŋŏţ ƃē űşēƌ ƃēċąűşē įţ ĥąş ą ρŗįɱąŗŷ ķēŷ, ŵĥįĺē ţĥē şŏűŗċē ŏƃĵēċţ ĥąş ŋŏŋē. Ŷŏű ċąŋ ŗēŋąɱē ţĥē ţąŗğēţ ŏƃĵēċţ űşįŋğ ą ŋąɱē ţĥąţ ƌŏēş ŋŏţ ēχįşţ ŷēţ. Ţĥē şŷşţēɱ ţĥēŋ ċŗēąţēş ą ŋēŵ ŏƃĵēċţ ţĥąţ ĥąş ţĥē şąɱē ƌēƒįŋįţįŏŋ ąş ţĥē şŏűŗċē ŏƃĵēċţ ąŋƌ ċŏŋşēƣűēŋţĺŷ ŋŏ ρŗįɱąŗŷ ķēŷ. Āĺţēŗŋąţįʋēĺŷ, ŷŏű ċąŋ ɱąρ ţĥē ţąŗğēţ ŏƃĵēċţ ţŏ ąŋ ēχįşţįŋğ ŏƃĵēċţ ţĥąţ ĥąş ţĥē ŗēƣűįŗēƌ ţēċĥŋįċąĺ ċŏĺűɱŋ (__ĺŏąƌ_ŗēċŏŗƌ_įƌ) ąŋƌ ƌŏēş ŋŏţ ĥąʋē ą ρŗįɱąŗŷ ķēŷ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=[[[Ĉĥąŋğē ţąŗğēţ ŏƃĵēċţ.∙∙∙∙∙]]]
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle=[[[{0}]]]
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=[[[Ţĥē ţąŗğēţ ŏƃĵēċţ ċąŋŋŏţ ƃē űşēƌ ƃēċąűşē ţĥē ƌąţą ţŷρē ŏƒ įţş ţēċĥŋįċąĺ ċŏĺűɱŋ __ĺŏąƌ_ρąċķąğē_įƌ įş ŋŏţ "ƃįŋąŗŷ(>=256)". Ŷŏű ċąŋ ŗēŋąɱē ţĥē ţąŗğēţ ŏƃĵēċţ űşįŋğ ą ŋąɱē ţĥąţ ƌŏēş ŋŏţ ēχįşţ ŷēţ. Ţĥē şŷşţēɱ ţĥēŋ ċŗēąţēş ą ŋēŵ ŏƃĵēċţ ţĥąţ ĥąş ţĥē şąɱē ƌēƒįŋįţįŏŋ ąş ţĥē şŏűŗċē ŏƃĵēċţ ąŋƌ ċŏŋşēƣűēŋţĺŷ ţĥē ċŏŗŗēċţ ƌąţą ţŷρē. Āĺţēŗŋąţįʋēĺŷ, ŷŏű ċąŋ ɱąρ ţĥē ţąŗğēţ ŏƃĵēċţ ţŏ ąŋ ēχįşţįŋğ ŏƃĵēċţ ţĥąţ ĥąş ţĥē ŗēƣűįŗēƌ ţēċĥŋįċąĺ ċŏĺűɱŋ (__ĺŏąƌ_ρąċķąğē_įƌ) ŵįţĥ ţĥē ċŏŗŗēċţ ƌąţą ţŷρē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validationAutoRenameTargetDPID=[[[Ţąŗğēţ ċŏĺűɱŋş ĥąʋē ƃēēŋ ŗēŋąɱēƌ.∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validationAutoRenameTargetDPIDSubTitle=[[[{0}]]]
#XMSG
sourceABAPWithoutKeyWithLTF=[[[Řēɱŏʋē şŏűŗċē ŏƃĵēċţ.∙∙∙∙∙]]]
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle=[[[{0}]]]
#XMSG
sourceABAPWithoutKeyWithLTFDescription=[[[Ţĥē şŏűŗċē ŏƃĵēċţ ƌŏēş ŋŏţ ĥąʋē ą ķēŷ ċŏĺűɱŋ, ŵĥįċĥ įş ŋŏţ şűρρŏŗţēƌ įŋ ţĥįş ċŏŋţēχţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validationAutoRenameTargetDPIDDescription=[[[Āŋ ąűţŏ-ρŗŏĵēċţįŏŋ ĥąş ƃēēŋ ąƌƌēƌ, ąŋƌ ţĥē ƒŏĺĺŏŵįŋğ ţąğēţ ċŏĺűɱŋş ĥąʋē ƃēēŋ ŗēŋąɱēƌ ţŏ ąĺĺŏŵ ŗēρĺįċąţįŏŋ ƒŗŏɱ ĀƁĀƤ şŏűŗċē ŵįţĥŏűţ ķēŷş :{1}{1} {0} {1}{1}Ţĥįş įş ƌűē ţŏ ŏŋē ŏƒ ţĥē ƒŏĺĺŏŵįŋğ ŗēąşŏŋş:{1}{1}{2} Řēşēŗʋēƌ ċŏĺűɱŋ ŋąɱē{1}{2} Ńŏţ şűρρŏŗţēƌ ċĥąŗąċţēŗş{1}{2} Řēşēŗʋēƌ ρŗēƒįχ]]]
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=[[[Řēρĺįċąţįŏŋ ţŏ {0}.]]]
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=[[[Ŝąʋįŋğ ąŋƌ ƌēρĺŏŷįŋğ ŗēρĺįċąţįŏŋ ƒĺŏŵş ţĥąţ ĥąʋē {0} ąş ţĥēįŗ ţąŗğēţ įş ċűŗŗēŋţĺŷ ŋŏţ ρŏşşįƃĺē ƃēċąűşē ŵē ąŗē ρēŗƒŏŗɱįŋğ ɱąįŋţēŋąŋċē ŏŋ ţĥįş ƒűŋċţįŏŋ.]]]
#XMSG
TargetColumnSkippedLTF=[[[Ţąŗğēţ ċŏĺűɱŋ ĥąş ƃēēŋ şķįρρēƌ.∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
TargetColumnSkippedLTFSubtitle=[[[{0}]]]
#XMSG
TargetColumnSkippedLTFDescription=[[[Ţąŗğēţ ċŏĺűɱŋ ĥąş ƃēēŋ şķįρρēƌ ƌűē ţŏ űŋşűρρŏŗţēƌ ƌąţą ţŷρē. {0}{1}]]]
#XMSG
validatePKTimeColumnLTF1=[[[Ţįɱē Ĉŏĺűɱŋ ąş Ƥŗįɱąŗŷ Ķēŷ.∙∙∙∙∙∙∙∙]]]
#XMSG
validatePKTimeColumnLTFSubtitle=[[[{0}]]]
#XMSG
validatePKTimeColumnLTFDescription=[[[Ţĥē şŏűŗċē ŏƃĵēċţ ĥąş ą ţįɱē ċŏĺűɱŋ ąş ą ρŗįɱąŗŷ ķēŷ, ŵĥįċĥ įş ŋŏţ şűρρŏŗţēƌ įŋ ţĥįş ċŏŋţēχţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validateNoPKInLTFTarget=[[[Ƥŗįɱąŗŷ Ķēŷ ɱįşşįŋğ.∙∙∙∙]]]
#XMSG
validateNoPKInLTFTargetDescription=[[[Ƥŗįɱąŗŷ ķēŷ įş ŋŏţ ƌēƒįŋēƌ įŋ ţĥē ţąŗğēţ, ŵĥįċĥ įş ŋŏţ şűρρŏŗţēƌ įŋ ţĥįş ċŏŋţēχţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validateABAPClusterTableLTF=[[[ĀƁĀƤ Ĉĺűşţēŗ Ţąƃĺē.∙∙∙∙∙]]]
#XMSG
validateABAPClusterTableLTFSubtitle=[[[{0}]]]
#XMSG
validateABAPClusterTableLTFDescription=[[[Ţĥē şŏűŗċē ŏƃĵēċţ įş ąŋ ĀƁĀƤ ċĺűşţēŗ ţąƃĺē, ŵĥįċĥ įş ŋŏţ şűρρŏŗţēƌ įŋ ţĥįş ċŏŋţēχţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=[[[Ĭţ ĺŏŏķş ĺįķē ŷŏű ĥąʋēŋ’ţ ąƌƌēƌ ąŋŷ ƌąţą ŷēţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#YINS
welcomeText2=[[[Ţŏ şţąŗţ ŷŏűŗ ŗēρĺįċąţįŏŋ ƒĺŏŵ, şēĺēċţ ą ċŏŋŋēċţįŏŋ ąŋƌ ą şŏűŗċē ŏƃĵēċţ ŏŋ ţĥē ĺēƒţ şįƌē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XBUT
wizStep1=[[[Ŝēĺēċţ Ŝŏűŗċē Ĉŏŋŋēċţįŏŋ∙∙∙∙∙∙]]]
#XBUT
wizStep2=[[[Ŝēĺēċţ Ŝŏűŗċē Ĉŏŋţąįŋēŗ∙∙∙∙∙∙]]]
#XBUT
wizStep3=[[[Āƌƌ Ŝŏűŗċē Ŏƃĵēċţş∙∙∙∙∙∙]]]

#XMSG
limitDataset=[[[Ţĥē ɱąχįɱűɱ ŋűɱƃēŗ ŏƒ ŏƃĵēċţş ĥąş ƃēēŋ ŗēąċĥēƌ. Řēɱŏʋē ēχįşţįŋğ ŏƃĵēċţş ţŏ ąƌƌ ŋēŵ ŏŋēş, ŏŗ ċŗēąţē ą ŋēŵ ŗēρĺįċąţįŏŋ ƒĺŏŵ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
premiumOutBoundRFCannotStartWarningMsg=[[[Ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ ţŏ ţĥįş ŋŏŋ-ŜĀƤ ţąŗğēţ ċŏŋŋēċţįŏŋ ċąŋŋŏţ ƃē şţąŗţēƌ ƃēċąűşē ţĥēŗē įş ŋŏ ŏűţƃŏűŋƌ ʋŏĺűɱē ąʋąįĺąƃĺē ƒŏŗ ţĥįş ɱŏŋţĥ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
premiumOutBoundRFAdminWarningMsg=[[[Āŋ ąƌɱįŋįşţŗąţŏŗ ċąŋ įŋċŗēąşē ţĥē Ƥŗēɱįűɱ Ŏűţƃŏűŋƌ ƃĺŏċķş ƒŏŗ ţĥįş ţēŋąŋţ, ɱąķįŋğ ŏűţƃŏűŋƌ ʋŏĺűɱē ąʋąįĺąƃĺē ƒŏŗ ţĥįş ɱŏŋţĥ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
messageForToastForDPIDColumn2=[[[Ńēŵ ċŏĺűɱŋ ąƌƌēƌ ţŏ ţąŗğēţ ƒŏŗ {0} ŏƃĵēċţş - ŋēēƌēƌ ƒŏŗ ĥąŋƌĺįŋğ ƌűρĺįċąţē ŗēċŏŗƌş įŋ ċŏŋŋēċţįŏŋ ŵįţĥ ĀƁĀƤ-ƃąşēƌ şŏűŗċē ŏƃĵēċţş ţĥąţ ƌŏ ŋŏţ ĥąʋē ą ρŗįɱąŗŷ ķēŷ.]]]
#XMSG
PremiumInboundWarningMessage=[[[Ďēρēŋƌįŋğ ŏŋ ţĥē ŋűɱƃēŗ ŏƒ ŗēρĺįċąţįŏŋ ƒĺŏŵş ąŋƌ ţĥē ƌąţą ʋŏĺűɱē ţŏ ƃē ŗēρĺįċąţēƌ, ţĥē ŜĀƤ ĤĀŃĀ ŗēşŏűŗċēş{0}ŗēƣűįŗēƌ ƒŏŗ ŗēρĺįċąţįŋğ ƌąţą ţĥŗŏűğĥ {1} ɱąŷ ēχċēēƌ ţĥē ąʋąįĺąƃĺē ċąρąċįţŷ ƒŏŗ ŷŏűŗ ţēŋąŋţ.]]]
#XMSG
PremiumInboundWarningMsg=[[[Ďēρēŋƌįŋğ ŏŋ ţĥē ŋűɱƃēŗ ŏƒ ŗēρĺįċąţįŏŋ ƒĺŏŵş ąŋƌ ţĥē ƌąţą ʋŏĺűɱē ţŏ ƃē ŗēρĺįċąţēƌ,{0}ţĥē ŜĀƤ ĤĀŃĀ ŗēşŏűŗċēş ŗēƣűįŗēƌ ƒŏŗ ŗēρĺįċąţįŋğ ƌąţą ţĥŗŏűğĥ "{1}" ɱąŷ ēχċēēƌ ţĥē ąʋąįĺąƃĺē ċąρąċįţŷ ƒŏŗ ŷŏűŗ ţēŋąŋţ.]]]
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=[[[Ĕŋţēŗ ą ρŗŏĵēċţįŏŋ ŋąɱē.∙∙∙∙∙∙]]]
#XMSG
emptyTargetColumn=[[[Ĕŋţēŗ ą ţąŗğēţ ċŏĺűɱŋ ŋąɱē.∙∙∙∙∙∙∙∙]]]
#XMSG
emptyTargetColumnBusinessName=[[[Ĕŋţēŗ ą ţąŗğēţ ċŏĺűɱŋ Ɓűşįŋēşş Ńąɱē.∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
invalidTransformName=[[[Ĕŋţēŗ ą ρŗŏĵēċţįŏŋ ŋąɱē.∙∙∙∙∙∙]]]
#XMSG
uniqueColumnName=[[[Řēŋąɱē ţąŗğēţ ċŏĺűɱŋ.∙∙∙∙∙]]]
#XMSG
copySourceColumnLbl=[[[Ĉŏρŷ ċŏĺűɱŋş ƒŗŏɱ şŏűŗċē ŏƃĵēċţ∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
renameWarning=[[[Μąķē şűŗē ţŏ ċĥŏŏşē ą űŋįƣűē ŋąɱē ŵĥįĺē ŗēŋąɱįŋğ ţĥē ţąŗğēţ ţąƃĺē. Ĭƒ ţĥē ţąƃĺē ŵįţĥ ţĥē ŋēŵ ŋąɱē ąĺŗēąƌŷ ēχįşţş įŋ ţĥē şρąċē, įţ ŵįĺĺ űşē ţĥē ƌēƒįŋįţįŏŋ ŏƒ ţĥąţ ţąƃĺē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XMSG
uniqueColumnBusinessName=[[[Řēŋąɱē ţąŗğēţ ċŏĺűɱŋ ƃűşįŋēşş ŋąɱē.∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
uniqueSourceMapping=[[[Ŝēĺēċţ ą ƌįƒƒēŗēŋţ şŏűŗċē ċŏĺűɱŋ.∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=[[[Ţĥē şŏűŗċē ċŏĺűɱŋ {0} įş ąĺŗēąƌŷ űşēƌ ƃŷ ţĥē ƒŏĺĺŏŵįŋğ ţąŗğēţ ċŏĺűɱŋş:{1}{1}{2}{1}{1} Ƒŏŗ ţĥįş ţąŗğēţ ċŏĺűɱŋ ŏŗ ƒŏŗ ţĥē ŏţĥēŗ ţąŗğēţ ċŏĺűɱŋş, şēĺēċţ ą şŏűŗċē ċŏĺűɱŋ ţĥąţ įş ŋŏţ ąĺŗēąƌŷ įŋ űşē ţŏ şąʋē ţĥē ρŗŏĵēċţįŏŋ.]]]
#XMSG
uniqueColumnNameDescription=[[[Ţĥē ţąŗğēţ ċŏĺűɱŋ ŋąɱē ŷŏű ēŋţēŗēƌ ąĺŗēąƌŷ ēχįşţş. Ţŏ ƃē ąƃĺē ţŏ şąʋē ţĥē ρŗŏĵēċţįŏŋ, ŷŏű ŋēēƌ ţŏ ēŋţēŗ ą űŋįƣűē ċŏĺűɱŋ ŋąɱē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
uniqueColumnBusinessNameDesc=[[[Ţĥē ţąŗğēţ ċŏĺűɱŋ ƃűşįŋēşş ŋąɱē ąĺŗēąƌŷ ēχįşţş. Ţŏ şąʋē ţĥē ρŗŏĵēċţįŏŋ, ŷŏű ɱűşţ ēŋţēŗ ą űŋįƣűē ċŏĺűɱŋ ƃűşįŋēşş ŋąɱē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
emptySource=[[[Ŝēĺēċţ ą şŏűŗċē ċŏĺűɱŋ ŏŗ ēŋţēŗ ą ċŏŋşţąŋţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
emptySourceDescription=[[[Ţŏ ċŗēąţē ą ʋąĺįƌ ɱąρρįŋğ ēŋţŗŷ, ŷŏű ŋēēƌ ţŏ şēĺēċţ ą şŏűŗċē ċŏĺűɱŋ ŏŗ ēŋţēŗ ą ċŏŋşţąŋţ ʋąĺűē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
emptyExpression=[[[Ďēƒįŋē ɱąρρįŋğ.∙∙∙∙]]]
#XMSG
emptyExpressionDescription1=[[[Ĕįţĥēŗ şēĺēċţ ţĥē şŏűŗċē ċŏĺűɱŋ ţŏ ŵĥįċĥ ŷŏű ŵąŋţ ţŏ ɱąρ ţĥē ţąŗğēţ ċŏĺűɱŋ, ŏŗ şēĺēċţ ţĥē ċĥēċķƃŏχ įŋ ţĥē ċŏĺűɱŋ {0} Ƒűŋċţįŏŋş / Ĉŏŋşţąŋţş {1}. {2} {2} Ƒűŋċţįŏŋş ąŗē ēŋţēŗēƌ ąűţŏɱąţįċąĺĺŷ ąċċŏŗƌįŋğ ţŏ ţĥē ţąŗğēţ ƌąţą ţŷρē. Ĉŏŋşţąŋţ ʋąĺűēş ċąŋ ƃē ēŋţēŗēƌ ɱąŋűąĺĺŷ.]]]
#XMSG
numberExpressionErr=[[[Ĕŋţēŗ ą ŋűɱƃēŗ.∙∙∙∙]]]
#XMSG
numberExpressionErrDescription=[[[Ŷŏű şēĺēċţēƌ ą ŋűɱēŗįċ ƌąţą ţŷρē. Ţĥįş ɱēąŋş ţĥąţ ŷŏű ċąŋ ŏŋĺŷ ēŋţēŗ ŋűɱēŗąĺş, ρĺűş ţĥē ƌēċįɱąĺ ρŏįŋţ įƒ ąρρĺįċąƃĺē. Ďŏ ŋŏţ űşē şįŋğĺē ƣűŏţąţįŏŋ ɱąŗķş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
invalidLength=[[[Ĕŋţēŗ ą ʋąĺįƌ ĺēŋğţĥ ʋąĺűē.∙∙∙∙∙∙∙∙]]]
#XMSG
invalidLengthDescription=[[[Ţĥē ĺēŋğţĥ ŏƒ ţĥē ƌąţą ţŷρē ɱűşţ ƃē ēƣűąĺ ţŏ ŏŗ ğŗēąţēŗ ţĥąŋ ţĥē ĺēŋğţĥ ŏƒ ţĥē şŏűŗċē ċŏĺűɱŋ ąŋƌ ċąŋ ƃē ƃēţŵēēŋ 1 ąŋƌ 5000.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
invalidMappedLength=[[[Ĕŋţēŗ ą ʋąĺįƌ ĺēŋğţĥ ʋąĺűē.∙∙∙∙∙∙∙∙]]]
#XMSG
invalidMappedLengthDescription=[[[Ţĥē ĺēŋğţĥ ŏƒ ţĥē ƌąţą ţŷρē ɱűşţ ƃē ēƣűąĺ ţŏ ŏŗ ğŗēąţēŗ ţĥąŋ ţĥē ĺēŋğţĥ ŏƒ ţĥē şŏűŗċē ċŏĺűɱŋ {0} ąŋƌ ċąŋ ƃē ƃēţŵēēŋ 1 ąŋƌ 5000.]]]
#XMSG
invalidPrecision=[[[Ĕŋţēŗ ą ʋąĺįƌ ρŗēċįşįŏŋ ʋąĺűē.∙∙∙∙∙∙∙∙∙]]]
#XMSG
invalidPrecisionDescription=[[[Ƥŗēċįşįŏŋ ƌēƒįŋēş ţĥē ţŏţąĺ ŋűɱƃēŗ ŏƒ ƌįğįţş. Ŝċąĺē ƌēƒįŋēş ţĥē ŋűɱƃēŗ ŏƒ ƌįğįţş ąƒţēŗ ţĥē ƌēċįɱąĺ ρŏįŋţ ąŋƌ ċąŋ ƃē ƃēţŵēēŋ 0 ąŋƌ ρŗēċįşįŏŋ.{0}{0} Ĕχąɱρĺēş: {0}{1} Ƥŗēċįşįŏŋ 6, şċąĺē 2 ċŏŗŗēşρŏŋƌş ţŏ ŋűɱƃēŗş ĺįķē 1234.56.{0}{1} Ƥŗēċįşįŏŋ 6, şċąĺē 6 ċŏŗŗēşρŏŋƌş ţŏ ŋűɱƃēŗş ĺįķē 0.123546.{0} {0} Ƥŗēċįşįŏŋ ąŋƌ şċąĺē ƒŏŗ ţĥē ţąŗğēţ ɱűşţ ƃē ċŏɱρąţįƃĺē ŵįţĥ ρŗēċįşįŏŋ ąŋƌ şċąĺē ƒŏŗ ţĥē şŏűŗċē şŏ ţĥąţ ąĺĺ ƌįğįţş ƒŗŏɱ ţĥē şŏűŗċē ƒįţ įŋţŏ ţĥē ţąŗğēţ ƒįēĺƌ. Ƒŏŗ ēχąɱρĺē, įƒ ŷŏű ĥąʋē ρŗēċįşįŏŋ 6 ąŋƌ şċąĺē 2 įŋ ţĥē şŏűŗċē (ąŋƌ ċŏŋşēƣűēŋţĺŷ ƌįğįţş ŏţĥēŗ ţĥąŋ 0 ƃēƒŏŗē ţĥē ƌēċįɱąĺ ρŏįŋţ), ŷŏű ċąŋŋŏţ ĥąʋē ρŗēċįşįŏŋ 6 ąŋƌ şċąĺē 6 įŋ ţĥē ţąŗğēţ.]]]
#XMSG
invalidPrimaryKey=[[[Ĕŋţēŗ ąţ ĺēąşţ ŏŋē ρŗįɱąŗŷ ķēŷ.∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
invalidPrimaryKeyDescription=[[[Ƥŗįɱąŗŷ ķēŷ ŋŏţ ƌēƒįŋēƌ ƒŏŗ ţĥįş şċĥēɱą.∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
invalidMappedPrecision=[[[Ĕŋţēŗ ą ʋąĺįƌ ρŗēċįşįŏŋ ʋąĺűē.∙∙∙∙∙∙∙∙∙]]]
#XMSG
invalidMappedPrecisionDescription1=[[[Ƥŗēċįşįŏŋ ƌēƒįŋēş ţĥē ţŏţąĺ ŋűɱƃēŗ ŏƒ ƌįğįţş. Ŝċąĺē ƌēƒįŋēş ţĥē ŋűɱƃēŗ ŏƒ ƌįğįţş ąƒţēŗ ţĥē ƌēċįɱąĺ ρŏįŋţ ąŋƌ ċąŋ ƃē ƃēţŵēēŋ 0 ąŋƌ ρŗēċįşįŏŋ.{0}{0} Ĕχąɱρĺēş:{0}{1} Ƥŗēċįşįŏŋ 6, şċąĺē 2 ċŏŗŗēşρŏŋƌş ţŏ ŋűɱƃēŗş ĺįķē 1234.56.{0}{1} Ƥŗēċįşįŏŋ 6, şċąĺē 6 ċŏŗŗēşρŏŋƌş ţŏ ŋűɱƃēŗş ĺįķē 0.123546.{0}{0}Ţĥē ρŗēċįşįŏŋ ŏƒ ţĥē ƌąţą ţŷρē ɱűşţ ƃē ēƣűąĺ ţŏ ŏŗ ğŗēąţēŗ ţĥąŋ ţĥē ρŗēċįşįŏŋ ŏƒ ţĥē şŏűŗċē ({2}).]]]
#XMSG
invalidScale=[[[Ĕŋţēŗ ą ʋąĺįƌ şċąĺē ʋąĺűē.∙∙∙∙∙∙∙]]]
#XMSG
invalidScaleDescription=[[[Ƥŗēċįşįŏŋ ƌēƒįŋēş ţĥē ţŏţąĺ ŋűɱƃēŗ ŏƒ ƌįğįţş. Ŝċąĺē ƌēƒįŋēş ţĥē ŋűɱƃēŗ ŏƒ ƌįğįţş ąƒţēŗ ţĥē ƌēċįɱąĺ ρŏįŋţ ąŋƌ ċąŋ ƃē ƃēţŵēēŋ 0 ąŋƌ ρŗēċįşįŏŋ.{0}{0} Ĕχąɱρĺēş: {0}{1} Ƥŗēċįşįŏŋ 6, şċąĺē 2 ċŏŗŗēşρŏŋƌş ţŏ ŋűɱƃēŗş ĺįķē 1234.56.{0}{1} Ƥŗēċįşįŏŋ 6, şċąĺē 6 ċŏŗŗēşρŏŋƌş ţŏ ŋűɱƃēŗş ĺįķē 0.123546.{0} {0} Ƥŗēċįşįŏŋ ąŋƌ şċąĺē ƒŏŗ ţĥē ţąŗğēţ ɱűşţ ƃē ċŏɱρąţįƃĺē ŵįţĥ ρŗēċįşįŏŋ ąŋƌ şċąĺē ƒŏŗ ţĥē şŏűŗċē şŏ ţĥąţ ąĺĺ ƌįğįţş ƒŗŏɱ ţĥē şŏűŗċē ƒįţ įŋţŏ ţĥē ţąŗğēţ ƒįēĺƌ. Ƒŏŗ ēχąɱρĺē, įƒ ŷŏű ĥąʋē ρŗēċįşįŏŋ 6 ąŋƌ şċąĺē 2 įŋ ţĥē şŏűŗċē (ąŋƌ ċŏŋşēƣűēŋţĺŷ ƌįğįţş ŏţĥēŗ ţĥąŋ 0 ƃēƒŏŗē ţĥē ƌēċįɱąĺ ρŏįŋţ), ŷŏű ċąŋŋŏţ ĥąʋē ρŗēċįşįŏŋ 6 ąŋƌ şċąĺē 6 įŋ ţĥē ţąŗğēţ.]]]
#XMSG
invalidMappedScale=[[[Ĕŋţēŗ ą ʋąĺįƌ şċąĺē ʋąĺűē.∙∙∙∙∙∙∙]]]
#XMSG
invalidMappedScaleDescription1=[[[Ƥŗēċįşįŏŋ ƌēƒįŋēş ţĥē ţŏţąĺ ŋűɱƃēŗ ŏƒ ƌįğįţş. Ŝċąĺē ƌēƒįŋēş ţĥē ŋűɱƃēŗ ŏƒ ƌįğįţş ąƒţēŗ ţĥē ƌēċįɱąĺ ρŏįŋţ ąŋƌ ċąŋ ƃē ƃēţŵēēŋ 0 ąŋƌ ρŗēċįşįŏŋ.{0}{0} Ĕχąɱρĺēş: {0}{1} Ƥŗēċįşįŏŋ 6, şċąĺē 2 ċŏŗŗēşρŏŋƌş ţŏ ŋűɱƃēŗş ĺįķē 1234.56.{0}{1} Ƥŗēċįşįŏŋ 6, şċąĺē 6 ċŏŗŗēşρŏŋƌş ţŏ ŋűɱƃēŗş ĺįķē 0.123546.{0}{0} Ţĥē şċąĺē ŏƒ ţĥē ƌąţą ţŷρē ɱűşţ ƃē ēƣűąĺ ţŏ ŏŗ ğŗēąţēŗ ţĥąŋ ţĥē şċąĺē ŏƒ ţĥē şŏűŗċē ({2}).]]]
#XMSG
nonCompatibleDataType=[[[Ŝēĺēċţ ą ċŏɱρąţįƃĺē ţąŗğēţ ƌąţą ţŷρē.∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
nonCompatibleDataTypeDescription1=[[[Ţĥē ƌąţą ţŷρē ţĥąţ ŷŏű şρēċįƒŷ ĥēŗē ɱűşţ ƃē ċŏɱρąţįƃĺē ŵįţĥ ţĥē şŏűŗċē ƌąţą ţŷρē ({0}). {1}{1} Ĕχąɱρĺē: Ĭƒ ŷŏűŗ şŏűŗċē ċŏĺűɱŋ ĥąş ƌąţą ţŷρē şţŗįŋğ ąŋƌ ċŏŋţąįŋş ĺēţţēŗş, ŷŏű ċąŋŋŏţ űşē ą ƌēċįɱąĺ ƌąţą ţŷρē ƒŏŗ ŷŏűŗ ţąŗğēţ.]]]
#XMSG
invalidColumnCount=[[[Ŝēĺēċţ ą şŏűŗċē ċŏĺűɱŋ.∙∙∙∙∙∙]]]
#XMSG
ObjectStoreInvalidScaleORPrecision=[[[Ĕŋţēŗ ą ʋąĺįƌ ʋąĺűē ƒŏŗ ρŗēċįşįŏŋ ąŋƌ şċąĺē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=[[[Ţĥē ƒįŗşţ ʋąĺűē įş ţĥē ρŗēċįşįŏŋ, ŵĥįċĥ ƌēƒįŋēş ţĥē ţŏţąĺ ŋűɱƃēŗ ŏƒ ƌįğįţş. Ţĥē şēċŏŋƌ ʋąĺűē įş ţĥē şċąĺē, ŵĥįċĥ ƌēƒįŋēş ţĥē ƌįğįţş ąƒţēŗ ţĥē ƌēċįɱąĺ ρŏįŋţ. Ĕŋţēŗ ą ţąŗğēţ şċąĺē ʋąĺűē ţĥąţ įş ğŗēąţēŗ ţĥąŋ ţĥē şŏűŗċē şċąĺē ʋąĺűē ąŋƌ ɱąķē şűŗē ţĥąţ ţĥē ƌįƒƒēŗēŋċē ƃēţŵēēŋ ţĥē ēŋţēŗēƌ ţąŗğēţ şċąĺē ąŋƌ ρŗēċįşįŏŋ ʋąĺűē įş ğŗēąţēŗ ţĥąŋ ţĥē ƌįƒƒēŗēŋċē ƃēţŵēēŋ ţĥē şŏűŗċē şċąĺē ąŋƌ ρŗēċįşįŏŋ ʋąĺűē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
InvalidPrecisionORScale=[[[Ĕŋţēŗ ą ʋąĺįƌ ʋąĺűē ƒŏŗ ρŗēċįşįŏŋ ąŋƌ şċąĺē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=[[[Ţĥē ƒįŗşţ ʋąĺűē įş ţĥē ρŗēċįşįŏŋ, ŵĥįċĥ ƌēƒįŋēş ţĥē ţŏţąĺ ŋűɱƃēŗ ŏƒ ƌįğįţş. Ţĥē şēċŏŋƌ ʋąĺűē įş ţĥē şċąĺē, ŵĥįċĥ ƌēƒįŋēş ţĥē ƌįğįţş ąƒţēŗ ţĥē ƌēċįɱąĺ ρŏįŋţ.{0}{0}Ŝįŋċē ţĥē şŏűŗċē ƌąţą ţŷρē įş ŋŏţ şűρρŏŗţēƌ įŋ Ģŏŏğĺē ƁįğǬűēŗŷ, įţ įş ċŏŋʋēŗţēƌ ţŏ ţĥē ţąŗğēţ ƌąţą ţŷρē ĎĔĈĬΜĀĻ. Ĭŋ ţĥįş ċąşē, ţĥē ρŗēċįşįŏŋ ċąŋ ŏŋĺŷ ƃē ƌēƒįŋēƌ ƃēţŵēēŋ 38 ąŋƌ 76, ąŋƌ ţĥē şċąĺē ƃēţŵēēŋ 9 ąŋƌ 38. Μŏŗēŏʋēŗ, ţĥē ŗēşűĺţ ŏƒ ρŗēċįşįŏŋ ɱįŋűş şċąĺē, ŵĥįċĥ ŗēρŗēşēŋţş ţĥē ƌįğįţş ƃēƒŏŗē ţĥē ƌēċįɱąĺ ρŏįŋţ, ɱűşţ ƃē ƃēţŵēēŋ 29 ąŋƌ 38.]]]
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=[[[Ţĥē ƒįŗşţ ʋąĺűē įş ţĥē ρŗēċįşįŏŋ, ŵĥįċĥ ƌēƒįŋēş ţĥē ţŏţąĺ ŋűɱƃēŗ ŏƒ ƌįğįţş. Ţĥē şēċŏŋƌ ʋąĺűē įş ţĥē şċąĺē, ŵĥįċĥ ƌēƒįŋēş ţĥē ƌįğįţş ąƒţēŗ ţĥē ƌēċįɱąĺ ρŏįŋţ.{0}{0}Ŝįŋċē ţĥē şŏűŗċē ƌąţą ţŷρē įş ŋŏţ şűρρŏŗţēƌ įŋ Ģŏŏğĺē ƁįğǬűēŗŷ, įţ įş ċŏŋʋēŗţēƌ ţŏ ţĥē ţąŗğēţ ƌąţą ţŷρē ĎĔĈĬΜĀĻ. Ĭŋ ţĥįş ċąşē, ţĥē ρŗēċįşįŏŋ ɱűşţ ƃē ƌēƒįŋēƌ ąş 20 ŏŗ ğŗēąţēŗ. Ĭŋ ąƌƌįţįŏŋ, ţĥē ŗēşűĺţ ŏƒ ρŗēċįşįŏŋ ɱįŋűş şċąĺē, ŵĥįċĥ ŗēƒĺēċţş ţĥē ƌįğįţş ƃēƒŏŗē ţĥē ƌēċįɱąĺ ρŏįŋţ, ɱűşţ ƃē 20 ŏŗ ğŗēąţēŗ.]]]
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=[[[Ţĥē ƒįŗşţ ʋąĺűē įş ţĥē ρŗēċįşįŏŋ, ŵĥįċĥ ƌēƒįŋēş ţĥē ţŏţąĺ ŋűɱƃēŗ ŏƒ ƌįğįţş. Ţĥē şēċŏŋƌ ʋąĺűē įş ţĥē şċąĺē, ŵĥįċĥ ƌēƒįŋēş ţĥē ƌįğįţş ąƒţēŗ ţĥē ƌēċįɱąĺ ρŏįŋţ.{0}{0}Ŝįŋċē ţĥē şŏűŗċē ƌąţą ţŷρē įş ŋŏţ şűρρŏŗţēƌ įŋ ţĥē ţąŗğēţ, įţ įş ċŏŋʋēŗţēƌ ţŏ ţĥē ţąŗğēţ ƌąţą ţŷρē ĎĔĈĬΜĀĻ. Ĭŋ ţĥįş ċąşē, ţĥē ρŗēċįşįŏŋ ɱűşţ ƃē ƌēƒįŋēƌ ƃŷ ąŋŷ ŋűɱƃēŗ ğŗēąţēŗ ţĥąŋ ŏŗ ēƣűąĺ ţŏ 1 ąŋƌ ĺēşş ţĥąŋ ŏŗ ēƣűąĺ ţŏ 38, ąŋƌ ţĥē şċąĺē ĺēşş ţĥąŋ ŏŗ ēƣűąĺ ţŏ ρŗēċįşįŏŋ.]]]
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=[[[Ţĥē ƒįŗşţ ʋąĺűē įş ţĥē ρŗēċįşįŏŋ, ŵĥįċĥ ƌēƒįŋēş ţĥē ţŏţąĺ ŋűɱƃēŗ ŏƒ ƌįğįţş. Ţĥē şēċŏŋƌ ʋąĺűē įş ţĥē şċąĺē, ŵĥįċĥ ƌēƒįŋēş ţĥē ƌįğįţş ąƒţēŗ ţĥē ƌēċįɱąĺ ρŏįŋţ.{0}{0}Ŝįŋċē ţĥē şŏűŗċē ƌąţą ţŷρē įş ŋŏţ şűρρŏŗţēƌ įŋ ţĥē ţąŗğēţ, įţ įş ċŏŋʋēŗţēƌ ţŏ ţĥē ţąŗğēţ ƌąţą ţŷρē ĎĔĈĬΜĀĻ. Ĭŋ ţĥįş ċąşē, ţĥē ρŗēċįşįŏŋ ɱűşţ ƃē ƌēƒįŋēƌ ąş 20 ŏŗ ğŗēąţēŗ. Ĭŋ ąƌƌįţįŏŋ, ţĥē ŗēşűĺţ ŏƒ ρŗēċįşįŏŋ ɱįŋűş şċąĺē, ŵĥįċĥ ŗēƒĺēċţş ţĥē ƌįğįţş ƃēƒŏŗē ţĥē ƌēċįɱąĺ ρŏįŋţ, ɱűşţ ƃē 20 ŏŗ ğŗēąţēŗ.]]]
#XMSG
invalidColumnCountDescription=[[[Ţŏ ċŗēąţē ą ʋąĺįƌ ɱąρρįŋğ ēŋţŗŷ, ŷŏű ŋēēƌ ţŏ şēĺēċţ ą şŏűŗċē ċŏĺűɱŋ ŏŗ ēŋţēŗ ą ċŏŋşţąŋţ ʋąĺűē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
duplicateColumns=[[[Řēŋąɱē ţąŗğēţ ċŏĺűɱŋ.∙∙∙∙∙]]]
#XMSG
duplicateGBQCDCColumnsDesc=[[[Ţĥē ţąŗğēţ ċŏĺűɱŋ ŋąɱē įş ŗēşēŗʋēƌ įŋ Ģŏŏğĺē ƁįğǬűēŗŷ. Ŷŏű ŋēēƌ ţŏ ŗēŋąɱē įţ ţŏ ƃē ąƃĺē ţŏ şąʋē ţĥē ρŗŏĵēċţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
duplicateConfluentCDCColumnsDesc=[[[Ţĥē ţąŗğēţ ċŏĺűɱŋ ŋąɱē įş ŗēşēŗʋēƌ įŋ Ĉŏŋƒĺűēŋţ. Ŷŏű ŋēēƌ ţŏ ŗēŋąɱē įţ ţŏ ƃē ąƃĺē ţŏ şąʋē ţĥē ρŗŏĵēċţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
duplicateSignavioCDCColumnsDesc=[[[Ţĥē ţąŗğēţ ċŏĺűɱŋ ŋąɱē įş ŗēşēŗʋēƌ įŋ ŜĀƤ Ŝįğŋąʋįŏ. Ŷŏű ŋēēƌ ţŏ ŗēŋąɱē įţ ţŏ ƃē ąƃĺē ţŏ şąʋē ţĥē ρŗŏĵēċţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
duplicateMsOneLakeCDCColumnsDesc=[[[Ţĥē ţąŗğēţ ċŏĺűɱŋ ŋąɱē įş ŗēşēŗʋēƌ įŋ ΜŜ ŎŋēĻąķē. Ŷŏű ŋēēƌ ţŏ ŗēŋąɱē įţ ţŏ ƃē ąƃĺē ţŏ şąʋē ţĥē ρŗŏĵēċţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
duplicateSFTPCDCColumnsDesc=[[[Ţĥē ţąŗğēţ ċŏĺűɱŋ ŋąɱē įş ŗēşēŗʋēƌ įŋ ŜƑŢƤ. Ŷŏű ŋēēƌ ţŏ ŗēŋąɱē įţ ţŏ ƃē ąƃĺē ţŏ şąʋē ţĥē ρŗŏĵēċţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
GBQTargetNameWithPrefixUpdated1=[[[Ţĥē ţąŗğēţ ċŏĺűɱŋ ŋąɱē ċŏŋţąįŋş ą ρŗēƒįχ ţĥąţ įş ŗēşēŗʋēƌ įŋ Ģŏŏğĺē ƁįğǬűēŗŷ. Ŷŏű ŋēēƌ ţŏ ŗēŋąɱē įţ ţŏ ƃē ąƃĺē ţŏ şąʋē ţĥē ρŗŏĵēċţįŏŋ. {0}{0}Ţĥē ţąŗğēţ ċŏĺűɱŋ ŋąɱē ċąŋŋŏţ ƃēğįŋ ŵįţĥ ąŋŷ ŏƒ ţĥē ƒŏĺĺŏŵįŋğ şţŗįŋğş:{0}{0}{1}{2} _ŢĀƁĻĔ_{3}{2}_ƑĬĻĔ_{3}{2}_ƤĀŘŢĬŢĬŎŃ{3}{2} _ŘŎŴ_ŢĬΜĔŜŢĀΜƤ{3}{2} __ŘŎŎŢ__{3}{2} _ĈŎĻĬĎĔŃŢĬƑĬĔŘ{3}{4}]]]
#XMSG
GBQtargetMaxLength=[[[Ŝĥŏŗţēŋ ţąŗğēţ ċŏĺűɱŋ ŋąɱē.∙∙∙∙∙∙∙∙]]]
#XMSG
GBQtargetMaxLengthDesc=[[[Ĭŋ Ģŏŏğĺē ƁįğǬűēŗŷ, ą ċŏĺűɱŋ ŋąɱē ċąŋ űşē ą ɱąχįɱűɱ ŏƒ 300 ċĥąŗąċţēŗş. Ŝĥŏŗţēŋ ţĥē ţąŗğēţ ċŏĺűɱŋ ŋąɱē ţŏ ƃē ąƃĺē ţŏ şąʋē ţĥē ρŗŏĵēċţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
invalidMappedScalePrecision=[[[Ƥŗēċįşįŏŋ ąŋƌ şċąĺē ƒŏŗ ţĥē ţąŗğēţ ɱűşţ ƃē ċŏɱρąţįƃĺē ŵįţĥ ρŗēċįşįŏŋ ąŋƌ şċąĺē ƒŏŗ ţĥē şŏűŗċē şŏ ţĥąţ ąĺĺ ƌįğįţş ƒŗŏɱ ţĥē şŏűŗċē ƒįţ įŋţŏ ţĥē ţąŗğēţ ƒįēĺƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
invalidMappedScalePrecisionShortText=[[[Ĕŋţēŗ ą ʋąĺįƌ ρŗēċįşįŏŋ ąŋƌ şċąĺē ʋąĺűē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
validationIncompatiblePKTypeDescProjection3=[[[Ŏŋē ŏŗ ɱŏŗē şŏűŗċē ċŏĺűɱŋş ĥąʋē ƌąţą ţŷρēş ţĥąţ ċąŋŋŏţ ƃē ƌēƒįŋēƌ ąş ρŗįɱąŗŷ ķēŷş įŋ Ģŏŏğĺē ƁįğǬűēŗŷ. Ńŏŋē ŏƒ ţĥē ρŗįɱąŗŷ ķēŷş ŵįĺĺ ƃē ċŗēąţēƌ įŋ ţĥē ţąŗğēţ ŏƃĵēċţ.{0}{0}Ţĥē ƒŏĺĺŏŵįŋğ ţąŗğēţ ƌąţą ţŷρēş ąŗē ċŏɱρąţįƃĺē ŵįţĥ Ģŏŏğĺē ƁįğǬűēŗŷ ƌąţą ţŷρēş ƒŏŗ ŵĥįċĥ ą ρŗįɱąŗŷ ķēŷ ċąŋ ƃē ƌēƒįŋēƌ:{1}{2} ƁŎŎĻĔĀŃ{3}{2} ĎĀŢĔ{3}{2} ĎĔĈĬΜĀĻ{3}{2} ĬŃŢ32{3}{2} ĬŃŢ64{3}{2} ŃŮΜĔŘĬĈ{3}{2} ŜŢŘĬŃĢ{3}{2} ŢĬΜĔŜŢĀΜƤ]]]
#XMSG
uncheckColumnMessageId=[[[Ůŋċĥēċķ ċŏĺűɱŋ __ɱēşşąğē_įƌ.∙∙∙∙∙∙∙∙]]]
#XMSG
uncheckColumnMessageIdDesc=[[[Ĉŏĺűɱŋ: Ƥŗįɱąŗŷ Ķēŷ∙∙∙∙∙]]]
#XMSG
validationOpCodeInsert=[[[Ŷŏű ɱűşţ ēŋţēŗ ą ʋąĺűē ƒŏŗ Ĭŋşēŗţ.∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
recommendDifferentPrimaryKey=[[[Ŵē ŗēċŏɱɱēŋƌ ţĥąţ ŷŏű şēĺēċţ ą ƌįƒƒēŗēŋţ ρŗįɱąŗŷ ķēŷ ąţ ţĥē įţēɱ ĺēʋēĺ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
recommendDifferentPrimaryKeyDesc=[[[Ŵĥēŋ ţĥē ŏρēŗąţįŏŋ ċŏƌē įş ąĺŗēąƌŷ ƌēƒįŋēƌ, įţ įş ŗēċŏɱɱēŋƌēƌ ţŏ şēĺēċţ ƌįƒƒēŗēŋţ ρŗįɱąŗŷ ķēŷş ƒŏŗ ţĥē ąŗŗąŷ įŋƌēχ ąŋƌ ţĥē įţēɱş, ţŏ ąʋŏįƌ ρŗŏƃĺēɱş şűċĥ ąş ċŏĺűɱŋ ƌűρĺįċąţįŏŋ ƒŏŗ ēχąɱρĺē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
selectPrimaryKeyItemLevel=[[[Ŷŏű ɱűşţ şēĺēċţ ąţ ĺēąşţ ŏŋē ρŗįɱąŗŷ ķēŷ ƒŏŗ ƃŏţĥ ţĥē ĥēąƌēŗ ąŋƌ ţĥē įţēɱ ĺēʋēĺ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
selectPrimaryKeyItemLevelDesc=[[[Ŵĥēŋ ąŋ ąŗŗąŷ ŏŗ ą ɱąρ įş ēχρąŋƌēƌ, ŷŏű ɱűşţ şēĺēċţ ţŵŏ ρŗįɱąŗŷ ķēŷş, ŏŋē ąţ ţĥē ĥēąƌēŗ ĺēʋēĺ ąŋƌ ŏŋē ąţ ţĥē įţēɱ ĺēʋēĺ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
invalidMapKey=[[[Ŷŏű ɱűşţ şēĺēċţ ąţ ĺēąşţ ŏŋē ρŗįɱąŗŷ ķēŷ ąţ ţĥē ĥēąƌēŗ ĺēʋēĺ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
invalidMapKeyDesc=[[[Ŵĥēŋ ąŋ ąŗŗąŷ ŏŗ ą ɱąρ įş ēχρąŋƌēƌ, ŷŏű ɱűşţ şēĺēċţ ą ρŗįɱąŗŷ ķēŷ ąţ ţĥē ĥēąƌēŗ ĺēʋēĺ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD
txtSearchFields=[[[Ŝēąŗċĥ Ţąŗğēţ Ĉŏĺűɱŋş∙∙∙∙∙]]]
#XFLD
txtName=[[[Ńąɱē]]]
#XMSG
txtSourceColValidation=[[[Ŏŋē ŏŗ ɱŏŗē şŏűŗċē ċŏĺűɱŋş ąŗē ŋŏţ şűρρŏŗţēƌ:∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
txtMappingCount=[[[Μąρρįŋğş ({0})]]]
#XMSG
schema=[[[Ŝċĥēɱą∙∙∙∙∙∙∙∙]]]
#XMSG
sourceColumn=[[[Ŝŏűŗċē Ĉŏĺűɱŋş∙∙∙∙∙]]]
#XMSG
warningSourceSchema=[[[Āŋŷ ċĥąŋğē ɱąƌē ţŏ ţĥē şċĥēɱą ŵįĺĺ ąƒƒēċţ ɱąρρįŋğş įŋ ρŗŏĵēċţįŏŋ ƌįąĺŏğ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XCOL
txtTargetColName=[[[Ţąŗğēţ Ĉŏĺűɱŋ (Ţēċĥŋįċąĺ Ńąɱē)∙∙∙∙∙∙∙∙∙]]]
#XCOL
txtDataType=[[[Ţąŗğēţ Ďąţą Ţŷρē∙∙∙∙∙∙∙∙]]]
#XCOL
txtSourceDataType=[[[Ŝŏűŗċē Ďąţą Ţŷρē∙∙∙∙∙∙∙∙]]]
#XCOL
srcColName=[[[Ŝŏűŗċē Ĉŏĺűɱŋ (Ţēċĥŋįċąĺ Ńąɱē)∙∙∙∙∙∙∙∙∙]]]
#XCOL
precision=[[[Ƥŗēċįşįŏŋ∙∙∙∙∙]]]
#XCOL
scale=[[[Ŝċąĺē∙∙∙∙∙∙∙∙∙]]]
#XCOL
functionsOrConstants=[[[Ƒűŋċţįŏŋş / Ĉŏŋşţąŋţş∙∙∙∙∙]]]
#XCOL
txtTargetColBusinessName=[[[Ţąŗğēţ Ĉŏĺűɱŋ (Ɓűşįŋēşş Ńąɱē)∙∙∙∙∙∙∙∙∙]]]
#XCOL
prKey=[[[Ƥŗįɱąŗŷ Ķēŷ∙∙∙∙∙∙∙∙]]]
#XCOL
txtProperties=[[[Ƥŗŏρēŗţįēş∙∙∙∙]]]
#XBUT
txtOK=[[[Ŝąʋē]]]
#XBUT
txtCancel=[[[Ĉąŋċēĺ∙∙∙∙∙∙∙∙]]]
#XBUT
txtRemove=[[[Řēɱŏʋē∙∙∙∙∙∙∙∙]]]
#XFLD
txtDesc=[[[Ďēşċŗįρţįŏŋ∙∙∙∙∙∙∙∙]]]
#XMSG
rftdMapping=[[[Μąρρįŋğ∙∙∙∙∙∙∙]]]
#XFLD
@lblColumnDataType=[[[Ďąţą Ţŷρē∙∙∙∙∙]]]
#XFLD
@lblColumnTechnicalName=[[[Ţēċĥŋįċąĺ Ńąɱē∙∙∙∙∙]]]
#XBUT
txtAutomap=[[[Āűţŏ-Μąρ∙∙∙∙∙∙]]]
#XBUT
txtUp=[[[Ůρ∙∙]]]
#XBUT
txtDown=[[[Ďŏŵŋ]]]

#XTOL
txtTransformationHeader=[[[Ƥŗŏĵēċţįŏŋ∙∙∙∙]]]
#XTOL
editTransformation=[[[Ĕƌįţ]]]
#XTOL
primaryKeyToolip=[[[Ķēŷ∙]]]


#XMSG
rftdFilter=[[[Ƒįĺţēŗ∙∙∙∙∙∙∙∙]]]
#XMSG
rftdFilterColumnCount=[[[Ŝŏűŗċē: {0}({1})]]]
#XTOL
rftdFilterColSearch=[[[Ŝēąŗċĥ∙∙∙∙∙∙∙∙]]]
#XMSG
rftdFilterColNoData=[[[Ńŏ ċŏĺűɱŋş ţŏ ƌįşρĺąŷ∙∙∙∙∙]]]
#XMSG
rftdFilteredColNoExps=[[[Ńŏ ƒįĺţēŗ ēχρŗēşşįŏŋş∙∙∙∙∙]]]
#XMSG
rftdFilterSelectedColTxt=[[[Āƌƌ Ƒįĺţēŗ ƒŏŗ∙∙∙∙∙]]]
#XMSG
rftdFilterTxt=[[[Ƒįĺţēŗ ąʋąįĺąƃĺē ƒŏŗ∙∙∙∙]]]
#XBUT
rftdFilterSelectedAddColExp=[[[Āƌƌ Ĕχρŗēşşįŏŋ∙∙∙∙∙]]]
#YINS
rftdFilterNoSelectedCol=[[[Ŝēĺēċţ ą ċŏĺűɱŋ ţŏ ąƌƌ ƒįĺţēŗ.∙∙∙∙∙∙∙∙∙]]]
#XMSG
rftdFilterExp=[[[Ƒįĺţēŗ Ĕχρŗēşşįŏŋ∙∙∙∙∙∙∙]]]
#XMSG
rftdFilterNotAllowedColumn=[[[Āƌƌįŋğ ƒįĺţēŗş įş ŋŏţ şűρρŏŗţēƌ ƒŏŗ ţĥįş ċŏĺűɱŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
rftdFilterNotAllowedHead=[[[Ńŏţ Ŝűρρŏŗţēƌ Ĉŏĺűɱŋ∙∙∙∙]]]
#XMSG
rftdFilterNoExp=[[[Ńŏ ƒįĺţēŗ ĥąş ƃēēŋ ƌēƒįŋēƌ∙∙∙∙∙∙∙]]]
#XTOL
rftdfilteredTt=[[[Ƒįĺţēŗēƌ∙∙∙∙∙∙]]]
#XTOL
rftdremoveexpTt=[[[Řēɱŏʋē ƒįĺţēŗ ēχρŗēşşįŏŋ∙∙∙∙∙∙]]]
#XTOL
validationMessageTt=[[[Ʋąĺįƌąţįŏŋ Μēşşąğēş∙∙∙∙∙]]]
#XTOL
rftdFilterDateInp=[[[Ŝēĺēċţ ą ƌąţē∙∙∙∙∙∙]]]
#XTOL
rftdFilterDateTimeInp=[[[Ŝēĺēċţ ą ƌąţē ţįɱē∙∙∙∙∙∙]]]
#XTOL
rftdFilterTimeInp=[[[Ŝēĺēċţ ą ţįɱē∙∙∙∙∙∙]]]
#XTOL
rftdFilterInp=[[[Ĕŋţēŗ ą ʋąĺűē∙∙∙∙∙∙]]]
#XMSG
rftdFilterValidateEmptyMsg=[[[{0} ƒįĺţēŗ ēχρŗēşşįŏŋş ŏŋ {1} ċŏĺűɱŋ ąŗē ēɱρţŷ]]]
#XMSG
rftdFilterValidateInvalidNumericMsg=[[[{0} ƒįĺţēŗ ēχρŗēşşįŏŋş ŏŋ {1} ċŏĺűɱŋ ċŏŋţąįŋ įŋʋąĺįƌ ŋűɱēŗįċ ʋąĺűēş]]]
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=[[[Ƒįĺţēŗ ēχρŗēşşįŏŋ ɱűşţ ċŏŋţąįŋ ʋąĺįƌ ŋűɱēŗįċ ʋąĺűēş∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=[[[Ĭƒ ţąŗğēţ ŏƃĵēċţ şċĥēɱą ĥąş ċĥąŋğēƌ, űşē ţĥē ƒűŋċţįŏŋ “Μąρ ţŏ Ĕχįşţįŋğ Ţąŗğēţ Ŏƃĵēċţ” ŏŋ ţĥē ɱąįŋ ρąğē ţŏ ąƌąρţ ţĥē ċĥąŋğēş, ąŋƌ ŗēɱąρ ţĥē ţąŗğēţ ŏƃĵēċţ ŵįţĥ įţş şŏűŗċē ąğąįŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=[[[Ĭƒ ţĥē ţąŗğēţ ţąƃĺē ąĺŗēąƌŷ ēχįşţş ąŋƌ ţĥē ɱąρρįŋğ įŋċĺűƌēş ą şċĥēɱą ċĥąŋğē, ŷŏű ɱűşţ ċĥąŋğē ţĥē ţąŗğēţ ţąƃĺē ąċċŏŗƌįŋğĺŷ ƃēƒŏŗē ƌēρĺŏŷįŋğ ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=[[[Ĭƒ ŷŏűŗ ɱąρρįŋğ įŋʋŏĺʋēş ą şċĥēɱą ċĥąŋğē, ŷŏű ɱűşţ ċĥąŋğē ţĥē ţąŗğēţ ţąƃĺē ąċċŏŗƌįŋğĺŷ ƃēƒŏŗē ƌēρĺŏŷįŋğ ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=[[[Ţĥē ƒŏĺĺŏŵįŋğ űŋşűρρŏŗţēƌ ċŏĺűɱŋş ŵēŗē şķįρρēƌ ƒŗŏɱ şŏűŗċē ƌēƒįŋįţįŏŋ: {0}]]]
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=[[[Ţĥē ƒŏĺĺŏŵįŋğ űŋşűρρŏŗţēƌ ċŏĺűɱŋş ŵēŗē şķįρρēƌ ƒŗŏɱ ţąŗğēţ ƌēƒįŋįţįŏŋ: {0}]]]
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=[[[Ţĥē ƒŏĺĺŏŵįŋğ ŏƃĵēċţş ąŗē ŋŏţ şűρρŏŗţēƌ ƃēċąűşē ţĥēŷ ąŗē ēχρŏşēƌ ƒŏŗ ċŏŋşűɱρţįŏŋ: {0} {1} {0} {0} Ţŏ űşē ţąƃĺēş įŋ ą ŗēρĺįċąţįŏŋ ƒĺŏŵ, ţĥē şēɱąŋţįċ űşąğē (įŋ ţĥē ţąƃĺē şēţţįŋğş) ɱűşţ ŋŏţ ƃē şēţ ţŏ {2}Āŋąĺŷţįċąĺ Ďąţąşēţ{2}.]]]
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=[[[Ţĥē ţąŗğēţ ŏƃĵēċţ ċąŋŋŏţ ƃē űşēƌ ƃēċąűşē įţ įş ēχρŏşēƌ ƒŏŗ ċŏŋşűɱρţįŏŋ. {0} {0}  Ţŏ űşē ţĥē ţąƃĺē įŋ ą ŗēρĺįċąţįŏŋ ƒĺŏŵ, ţĥē şēɱąŋţįċ űşąğē (įŋ ţĥē ţąƃĺē şēţţįŋğş) ɱűşţ ŋŏţ ƃē şēţ ţŏ {1}Āŋąĺŷţįċąĺ Ďąţąşēţ{1}.]]]
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=[[[Ā ţąŗğēţ ŏƃĵēċţ ŵįţĥ ţĥįş ŋąɱē ąĺŗēąƌŷ ēχįşţş. Ĥŏŵēʋēŗ, įţ ċąŋŋŏţ ƃē űşēƌ ƃēċąűşē įţ įş ēχρŏşēƌ ƒŏŗ ċŏŋşűɱρţįŏŋ. {0} {0} Ţŏ űşē ţĥē ţąƃĺē įŋ ą ŗēρĺįċąţįŏŋ ƒĺŏŵ, ţĥē şēɱąŋţįċ űşąğē (įŋ ţĥē ţąƃĺē şēţţįŋğş) ɱűşţ ŋŏţ ƃē şēţ ţŏ {1}Āŋąĺŷţįċąĺ Ďąţąşēţ{1}.]]]
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=[[[Āŋ ŏƃĵēċţ ŵįţĥ ţĥįş ŋąɱē ąĺŗēąƌŷ ēχįşţş įŋ ţĥē ţąŗğēţ. {0}Ĥŏŵēʋēŗ, ţĥįş ŏƃĵēċţ ċąŋŋŏţ ƃē űşēƌ ąş ą ţąŗğēţ ŏƃĵēċţ ƒŏŗ ą ŗēρĺįċąţįŏŋ ƒĺŏŵ ţŏ ţĥē ĺŏċąĺ ŗēρŏşįţŏŗŷ, ąş įţ įş ŋŏţ ą ĺŏċąĺ ţąƃĺē.]]]
#XMSG:
targetAutoRenameUpdated=[[[Ţąŗğēţ ċŏĺűɱŋ ĥąş ƃēēŋ ŗēŋąɱēƌ.∙∙∙∙∙∙∙∙∙∙]]]
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=[[[Ţĥē ţąŗğēţ ċŏĺűɱŋ ĥąş ƃēēŋ ŗēŋąɱēƌ ţŏ ąĺĺŏŵ ŗēρĺįċąţįŏŋş įŋ Ģŏŏğĺē ƁįğǬűēŗŷ. Ţĥįş įş ƌűē ţŏ ŏŋē ŏƒ ţĥē ƒŏĺĺŏŵįŋğ ŗēąşŏŋş:{0} {1}{2}Řēşēŗʋēƌ ċŏĺűɱŋ ŋąɱē{3}{2}Ńŏţ şűρρŏŗţēƌ ċĥąŗąċţēŗş{3}{2}Řēşēŗʋēƌ ρŗēƒįχ{3}{4}]]]
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=[[[Ţĥē ţąŗğēţ ċŏĺűɱŋ ĥąş ƃēēŋ ŗēŋąɱēƌ ţŏ ąĺĺŏŵ ŗēρĺįċąţįŏŋş įŋ Ĉŏŋƒĺűēŋţ. Ţĥįş įş ƌűē ţŏ ŏŋē ŏƒ ţĥē ƒŏĺĺŏŵįŋğ ŗēąşŏŋş:{0} {1}{2}Řēşēŗʋēƌ ċŏĺűɱŋ ŋąɱē{3}{2}Ńŏţ şűρρŏŗţēƌ ċĥąŗąċţēŗş{3}{2}Řēşēŗʋēƌ ρŗēƒįχ{3}{4}]]]
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=[[[Ţĥē ţąŗğēţ ċŏĺűɱŋ ĥąş ƃēēŋ ŗēŋąɱēƌ ţŏ ąĺĺŏŵ ŗēρĺįċąţįŏŋş ţŏ ţĥē ţąŗğēţ. Ţĥįş įş ƌűē ţŏ ŏŋē ŏƒ ţĥē ƒŏĺĺŏŵįŋğ ŗēąşŏŋş:{0} {1}{2}Ńŏţ şűρρŏŗţēƌ ċĥąŗąċţēŗş{3}{2}Řēşēŗʋēƌ ρŗēƒįχ{3}{4}]]]
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=[[[Ţĥē ţąŗğēţ ċŏĺűɱŋ ĥąş ƃēēŋ ŗēŋąɱēƌ ţŏ ąĺĺŏŵ ŗēρĺįċąţįŏŋş ţŏ ţĥē ţąŗğēţ. Ţĥįş įş ƌűē ţŏ ŏŋē ŏƒ ţĥē ƒŏĺĺŏŵįŋğ ŗēąşŏŋş:{0} {1}{2}Řēşēŗʋēƌ ċŏĺűɱŋ ŋąɱē{3}{2}Ńŏţ şűρρŏŗţēƌ ċĥąŗąċţēŗş{3}{2}Řēşēŗʋēƌ ρŗēƒįχ{3}{4}]]]
#XMSG:
targetAutoDataType=[[[Ţąŗğēţ ƌąţą ţŷρē ĥąş ƃēēŋ ċĥąŋğēƌ.∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG:
targetAutoDataTypeDesc=[[[Ţĥē ţąŗğēţ ƌąţą ţŷρē ĥąş ƃēēŋ ċĥąŋğēƌ ţŏ {0} ƃēċąűşē ţĥē şŏűŗċē ƌąţą ţŷρē įş ŋŏţ şűρρŏŗţēƌ įŋ Ģŏŏğĺē ƁįğǬűēŗŷ.]]]
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=[[[Ţĥē ţąŗğēţ ƌąţą ţŷρē ĥąş ƃēēŋ ċĥąŋğēƌ ţŏ {0} ƃēċąűşē ţĥē şŏűŗċē ƌąţą ţŷρē įş ŋŏţ şűρρŏŗţēƌ įŋ ţĥē ţąŗğēţ ċŏŋŋēċţįŏŋ.]]]
#XMSG
projectionGBQUnableToCreateKey=[[[Ƥŗįɱąŗŷ ķēŷş ŵįĺĺ ŋŏţ ƃē ċŗēąţēƌ.∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=[[[Ĭŋ Ģŏŏğĺē ƁįğǬűēŗŷ, ą ɱąχįɱűɱ ŏƒ 16 ρŗįɱąŗŷ ķēŷş ąŗē şűρρŏŗţēƌ, ƃűţ ţĥē şŏűŗċē ŏƃĵēċţ ĥąş ą ĺąŗğēŗ ŋűɱƃēŗ ŏƒ ρŗįɱąŗŷ ķēŷş. Ńŏŋē ŏƒ ţĥē ρŗįɱąŗŷ ķēŷş ŵįĺĺ ƃē ċŗēąţēƌ įŋ ţĥē ţąŗğēţ ŏƃĵēċţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
HDLFNoKeyError=[[[Ďēƒįŋē ŏŋē ŏŗ ɱŏŗē ċŏĺűɱŋş ąş ą ρŗįɱąŗŷ ķēŷ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
HDLFNoKeyErrorDescription=[[[Ţŏ ŗēρĺįċąţē ąŋ ŏƃĵēċţ, ŷŏű ɱűşţ ƌēƒįŋē ŏŋē ŏŗ ɱŏŗē ċŏĺűɱŋş ąş ą ρŗįɱąŗŷ ķēŷ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
HDLFNoKeyErrorExistingTarget=[[[Ďēƒįŋē ŏŋē ŏŗ ɱŏŗē ċŏĺűɱŋş ąş ą ρŗįɱąŗŷ ķēŷ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=[[[Ţŏ ŗēρĺįċąţē ƌąţą ţŏ ąŋ ēχįşţįŋğ ţąŗğēţ ŏƃĵēċţ, įţ ɱűşţ ĥąʋē ŏŋē ŏŗ ɱŏŗē ċŏĺűɱŋş ţĥąţ ąŗē ƌēƒįŋēƌ ąş ţĥē ρŗįɱąŗŷ ķēŷ. {0} {0} Ŷŏű ĥąʋē ţĥē ƒŏĺĺŏŵįŋğ ŏρţįŏŋş ƒŏŗ ƌēƒįŋįŋğ ŏŋē ŏŗ ɱŏŗē ċŏĺűɱŋş ąş ţĥē ρŗįɱąŗŷ ķēŷ: {0}{0}{1} Ůşē ţĥē ĺŏċąĺ ţąƃĺē ēƌįţŏŗ ţŏ ċĥąŋğē ţĥē ēχįşţįŋğ ţąŗğēţ ŏƃĵēċţ. Ţĥēŋ ŗēĺŏąƌ ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ.{0}{0}{1} Řēŋąɱē ţĥē ţąŗğēţ ŏƃĵēċţ įŋ ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ. Ţĥįş ŵįĺĺ ċŗēąţē ą ŋēŵ ŏƃĵēċţ ąş şŏŏŋ ąş ą ŗűŋ įş şţąŗţēƌ. Āƒţēŗ ŗēŋąɱįŋğ, ŷŏű ċąŋ ƌēƒįŋē ŏŋē ŏŗ ɱŏŗē ċŏĺűɱŋş ąş ţĥē ρŗįɱąŗŷ ķēŷ įŋ ą ρŗŏĵēċţįŏŋ.{0}{0}{1} Μąρ ţĥē ŏƃĵēċţ ţŏ ąŋŏţĥēŗ ēχįşţįŋğ ţąŗğēţ ŏƃĵēċţ įŋ ŵĥįċĥ ŏŋē ŏŗ ɱŏŗē ċŏĺűɱŋş ąŗē ąĺŗēąƌŷ ƌēƒįŋēƌ ąş ţĥē ρŗįɱąŗŷ ķēŷ.]]]
#XMSG
HDLFSourceTargetDifferentKeysWarning=[[[Ƥŗįɱąŗŷ ķēŷ ċĥąŋğēƌ.∙∙∙∙]]]
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=[[[Ĉŏɱρąŗēƌ ţŏ ţĥē şŏűŗċē ŏƃĵēċţ, ŷŏű ĥąʋē ƌēƒįŋēƌ ƌįƒƒēŗēŋţ ċŏĺűɱŋş ąş ţĥē ρŗįɱąŗŷ ķēŷ ƒŏŗ ţĥē ţąŗğēţ ŏƃĵēċţ. Μąķē şűŗē ţĥąţ ţĥēşē ċŏĺűɱŋş űŋįƣűēĺŷ įƌēŋţįƒŷ ąĺĺ ŗŏŵş ţŏ ąʋŏįƌ ρŏşşįƃĺē ƌąţą ċŏŗŗűρţįŏŋ ŵĥēŋ ŗēρĺįċąţįŋğ ţĥē ƌąţą ĺąţēŗ. {0} {0} Ĭŋ ţĥē şŏűŗċē ŏƃĵēċţ, ţĥē ƒŏĺĺŏŵįŋğ ċŏĺűɱŋş ąŗē ƌēƒįŋēƌ ąş ţĥē ρŗįɱąŗŷ ķēŷ: {0} {1}]]]
#XMSG
duplicateDPIDColumns=[[[Řēŋąɱē ţąŗğēţ ċŏĺűɱŋ.∙∙∙∙∙]]]
#XMSG
duplicateDPIDDColumnsDesc1=[[[Ţĥįş ţąŗğēţ ċŏĺűɱŋ ŋąɱē įş ŗēşēŗʋēƌ ƒŏŗ ą ţēċĥŋįċąĺ ċŏĺűɱŋ. Ĕŋţēŗ ą ƌįƒƒēŗēŋţ ŋąɱē ţŏ şąʋē ţĥē ρŗŏĵēċţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG:
targetAutoRenameDPID=[[[Ţąŗğēţ ċŏĺűɱŋ ĥąş ƃēēŋ ŗēŋąɱēƌ.∙∙∙∙∙∙∙∙∙∙]]]
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=[[[Ţĥē ţąŗğēţ ċŏĺűɱŋ ĥąş ƃēēŋ ŗēŋąɱēƌ ţŏ ąĺĺŏŵ ŗēρĺįċąţįŏŋş ƒŗŏɱ ĀƁĀƤ şŏűŗċē ŵįţĥŏűţ ķēŷş. Ţĥįş įş ƌűē ţŏ ŏŋē ŏƒ ţĥē ƒŏĺĺŏŵįŋğ ŗēąşŏŋş:{0} {1}{2}Řēşēŗʋēƌ ċŏĺűɱŋ ŋąɱē{3}{2}Ńŏţ şűρρŏŗţēƌ ċĥąŗąċţēŗş{3}{2}Řēşēŗʋēƌ ρŗēƒįχ{3}{4}]]]
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle=[[[{0} Ţąŗğēţ Ŝēţţįŋğş]]]
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle=[[[{0} Ŝŏűŗċē Ŝēţţįŋğş]]]
#XBUT
connectionSettingSave=[[[Ŝąʋē]]]
#XBUT
connectionSettingCancel=[[[Ĉąŋċēĺ∙∙∙∙∙∙∙∙]]]
#XBUT: Button to keep the object level settings
txtKeep=[[[Ķēēρ]]]
#XBUT: Button to overwrite the Object level settings
txtOverwrite=[[[Ŏʋēŗŵŗįţē∙∙∙∙∙]]]
#XFLD
targetConnectionThreadlimit=[[[Ţąŗğēţ Ţĥŗēąƌ Ļįɱįţ ƒŏŗ Ĭŋįţįąĺ Ļŏąƌ (1-100)∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD
connectionThreadLimit=[[[Ŝŏűŗċē Ţĥŗēąƌ Ļįɱįţ ƒŏŗ Ĭŋįţįąĺ Ļŏąƌ (1-100)∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD
maxConnection=[[[Řēρĺįċąţįŏŋ Ţĥŗēąƌ Ļįɱįţ (1-100)∙∙∙∙∙∙∙∙∙∙]]]
#XFLD
kafkaNumberOfPartitions=[[[Ńűɱƃēŗ ŏƒ Ƥąŗţįţįŏŋş∙∙∙∙]]]
#XFLD
kafkaReplicationFactor=[[[Řēρĺįċąţįŏŋ Ƒąċţŏŗ∙∙∙∙∙∙]]]
#XFLD
kafkaMessageEncoder=[[[Μēşşąğē Ĕŋċŏƌēŗ∙∙∙∙]]]
#XFLD
kafkaMessageCompression=[[[Μēşşąğē Ĉŏɱρŗēşşįŏŋ∙∙∙∙∙]]]
#XFLD
fileGroupDeltaFilesBy=[[[Ģŗŏűρ Ďēĺţą ƃŷ∙∙∙∙∙]]]
#XFLD
fileFormat=[[[Ƒįĺē Ţŷρē∙∙∙∙∙]]]
#XFLD
csvEncoding=[[[ĈŜƲ Ĕŋċŏƌįŋğ∙∙∙∙∙∙∙]]]
#XFLD
abapExitLbl=[[[ĀƁĀƤ Ĕχįţ∙∙∙∙∙]]]
#XFLD
deltaPartition=[[[Ŏƃĵēċţ Ţĥŗēąƌ Ĉŏűŋţ ƒŏŗ Ďēĺţą Ļŏąƌş (1-10)∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD
clamping_Data=[[[Ƒąįĺ ŏŋ Ďąţą Ţŗűŋċąţįŏŋ∙∙∙∙∙∙]]]
#XFLD
fail_On_Incompatible=[[[Ƒąįĺ ŏŋ Ĭŋċŏɱρąţįƃĺē Ďąţą∙∙∙∙∙∙∙]]]
#XFLD
maxPartitionInput=[[[Μąχ Ńűɱƃēŗ ŏƒ Ƥąŗţįţįŏŋş∙∙∙∙∙∙]]]
#XFLD
max_Partition=[[[Ďēƒįŋē Μąχ. Ńűɱƃēŗ ŏƒ Ƥąŗţįţįŏŋş∙∙∙∙∙∙∙∙∙∙]]]
#XFLD
include_SubFolder=[[[Ĭŋċĺűƌē Ŝűƃƒŏĺƌēŗş∙∙∙∙∙∙]]]
#XFLD
fileGlobalPattern=[[[Ģĺŏƃąĺ Ƥąţţēŗŋ ƒŏŗ Ƒįĺē Ńąɱē∙∙∙∙∙∙∙∙]]]
#XFLD
fileCompression=[[[Ƒįĺē Ĉŏɱρŗēşşįŏŋ∙∙∙∙∙∙∙∙]]]
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=[[[Ƒįĺē Ďēĺįɱįţēŗ∙∙∙∙∙]]]
#XFLD
fileIsHeaderIncluded=[[[Ƒįĺē Ĥēąƌēŗ∙∙∙∙∙∙∙∙]]]
#XFLD
fileOrient=[[[Ŏŗįēŋţ∙∙∙∙∙∙∙∙]]]
#XFLD
gbqWriteMode=[[[Ŵŗįţē Μŏƌē∙∙∙∙]]]
#XFLD
suppressDuplicate=[[[Ŝűρρŗēşş Ďűρĺįċąţēş∙∙∙∙∙]]]
#XFLD
apacheSpark=[[[Ĕŋąƃĺē Āρąċĥē Ŝρąŗķ Ĉŏɱρąţįƃįĺįţŷ∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD
clampingDatatypeCb=[[[Ĉĺąɱρ Ďēċįɱąĺ Ƒĺŏąţįŋğ Ƥŏįŋţ Ďąţą Ţŷρēş∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD
overwriteDatasetSetting=[[[Ŏʋēŗŵŗįţē Ţąŗğēţ Ŝēţţįŋğş ąţ Ŏƃĵēċţ Ļēʋēĺ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD
overwriteSourceDatasetSetting=[[[Ŏʋēŗŵŗįţē Ŝŏűŗċē Ŝēţţįŋğş ąţ Ŏƃĵēċţ Ļēʋēĺ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
kafkaInvalidConnectionSetting=[[[Ĕŋţēŗ ţĥē ŋűɱƃēŗ ƃēţŵēēŋ {0} ąŋƌ {1}.]]]
#XMSG
MinReplicationThreadErrorMsg=[[[Ĕŋţēŗ ą ŋűɱƃēŗ ğŗēąţēŗ ţĥąŋ {0}.]]]
#XMSG
MaxReplicationThreadErrorMsg=[[[Ĕŋţēŗ ą ŋűɱƃēŗ ĺŏŵēŗ ţĥąŋ {0}.]]]
#XMSG
DeltaThreadErrorMsg=[[[Ĕŋţēŗ ą ʋąĺűē ƃēţŵēēŋ 1 ąŋƌ 10.∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
MaxPartitionErrorMsg=[[[Ĕŋţēŗ ʋąĺűē ƃēţŵēēŋ 1 <= χ <= 2147483647.Ţĥē ƌēƒąűĺţ ʋąĺűē įş 10.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=[[[Ĕŋţēŗ ąŋ įŋţēğēŗ ƃēţŵēēŋ {0} ąŋƌ {1}.]]]
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=[[[Ůşē Řēρĺįċąţįŏŋ Ƒąċţŏŗ ŏƒ ţĥē Ɓŗŏķēŗ∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD
serializationFormat=[[[Ŝēŗįąĺįžąţįŏŋ Ƒŏŗɱąţ∙∙∙∙]]]
#XFLD
compressionType=[[[Ĉŏɱρŗēşşįŏŋ Ţŷρē∙∙∙∙∙∙∙∙]]]
#XFLD
schemaRegistry=[[[Ůşē Ŝċĥēɱą Řēğįşţŗŷ∙∙∙∙∙]]]
#XFLD
subjectNameStrat=[[[Ŝűƃĵēċţ Ńąɱē Ŝţŗąţēğŷ∙∙∙∙∙]]]
#XFLD
compatibilityType=[[[Ĉŏɱρąţįƃįĺįţŷ Ţŷρē∙∙∙∙∙∙]]]
#XFLD
confluentTopicName=[[[Ţŏρįċ Ńąɱē∙∙∙∙]]]
#XFLD
confluentRecordName=[[[Řēċŏŗƌ Ńąɱē∙∙∙∙∙∙∙∙]]]
#XFLD
confluentSubjectNamePreview=[[[Ŝűƃĵēċţ Ńąɱē Ƥŗēʋįēŵ∙∙∙∙]]]
#XMSG
serializationChangeToastMsgUpdated2=[[[Ŝēŗįąĺįžąţįŏŋ ƒŏŗɱąţ ċĥąŋğēƌ ţŏ ĴŜŎŃ ąş şċĥēɱą ŗēğįşţŗŷ įş ŋŏţ ēŋąƃĺēƌ. Ţŏ ċĥąŋğē ţĥē şēŗįąĺįžąţįŏŋ ƒŏŗɱąţ ƃąċķ ţŏ ĀƲŘŎ, ŷŏű ɱűşţ ēŋąƃĺē şċĥēɱą ŗēğįşţŗŷ ƒįŗşţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XBUT
confluentTopicNameInfo=[[[Ţĥē ţŏρįċ ŋąɱē įş ąĺŵąŷş ƃąşēƌ ŏŋ ţĥē ţąŗğēţ ŏƃĵēċţ ŋąɱē. Ŷŏű ċąŋ ċĥąŋğē įţ ƃŷ ŗēŋąɱįŋğ ţĥē ţąŗğēţ ŏƃĵēċţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
emptyRecordNameValidationHeaderMsg=[[[Ĕŋţēŗ ą ŗēċŏŗƌ ŋąɱē.∙∙∙∙]]]
#XMSG
emptyPartionHeader=[[[Ĕŋţēŗ ţĥē ŋűɱƃēŗ ŏƒ ρąŗţįţįŏŋş.∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
invalidPartitionsHeader=[[[Ĕŋţēŗ ą ʋąĺįƌ ŋűɱƃēŗ ŏƒ ρąŗţįţįŏŋş.∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
invalidpartitionsDesc=[[[Ĕŋţēŗ ą ŋűɱƃēŗ ƃēţŵēēŋ 1 ąŋƌ 200,000.∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
emptyrFactorHeader=[[[Ĕŋţēŗ ą ŗēρĺįċąţįŏŋ ƒąċţŏŗ.∙∙∙∙∙∙∙∙]]]
#XMSG
invalidrFactorHeader=[[[Ĕŋţēŗ ą ʋąĺįƌ ŗēρĺįċąţįŏŋ ƒąċţŏŗ.∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
invalidrFactorDesc=[[[Ĕŋţēŗ ą ŋűɱƃēŗ ƃēţŵēēŋ 1 ąŋƌ 32,767.∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
emptyRecordNameValidationDescMsg=[[[Ĭƒ ţĥē şēŗįąĺįžąţįŏŋ ƒŏŗɱąţ "ĀƲŘŎ" įş űşēƌ, ŏŋĺŷ ţĥē ƒŏĺĺŏŵįŋğ ċĥąŗąċţēŗş ąŗē şűρρŏŗţēƌ:{0}{1} Ā-Ż{0}{1} ą-ž{0}{1} 0-9{0}{1} _(űŋƌēŗşċŏŗē)]]]
#XMSG
validRecordNameValidationHeaderMsg=[[[Ĕŋţēŗ ą ʋąĺįƌ ŗēċŏŗƌ ŋąɱē.∙∙∙∙∙∙∙]]]
#XMSG
validRecordNameValidationDescMsgUpdated=[[[Ɓēċąűşē ţĥē şēŗįąĺįžąţįŏŋ ƒŏŗɱąţ "ĀƲŘŎ" įş űşēƌ, ţĥē ŗēċŏŗƌ ŋąɱē ɱűşţ ċŏŋşįşţ ŏƒ ŏŋĺŷ ąĺρĥąŋűɱēŗįċ (Ā-Ż, ą-ž, 0-9) ąŋƌ űŋƌēŗşċŏŗē (_) ċĥąŗąċţēŗş. Ĭţ ɱűşţ şţąŗţ ŵįţĥ ą ĺēţţēŗ ŏŗ ąŋ űŋƌēŗşċŏŗē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
targetObjectNameSubTitle=[[[{0}]]]
#XMSG
deltaPartitionnotEnabled=[[[Ţĥē “Ŏƃĵēċţ Ţĥŗēąƌ Ĉŏűŋţ ƒŏŗ Ďēĺţą Ļŏąƌş” ċąŋ ƃē şēţ ąş şŏŏŋ ąş ŏŋē ŏŗ ɱŏŗē ŏƃĵēċţş ĥąʋē ţĥē ĺŏąƌ ţŷρē “Ĭŋįţįąĺ ąŋƌ Ďēĺţą”.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
invalidTargetName=[[[Ĭŋʋąĺįƌ ċŏĺűɱŋ ŋąɱē∙∙∙∙∙]]]
#XMSG
invalidTargetNameDesc=[[[Ţĥē ţąŗğēţ ċŏĺűɱŋ ŋąɱē ɱűşţ ċŏŋşįşţ ŏƒ ŏŋĺŷ ąĺρĥąŋűɱēŗįċ (Ā-Ż, ą-ž, 0-9) ąŋƌ űŋƌēŗşċŏŗē (_) ċĥąŗąċţēŗş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD
consumeOtherSchema=[[[Ĉŏŋşűɱē Ŏţĥēŗ Ŝċĥēɱą Ʋēŗşįŏŋş∙∙∙∙∙∙∙∙∙]]]
#XFLD
ignoreSchemamissmatch=[[[Ĭğŋŏŗē Ŝċĥēɱą Μįşşɱąţċĥ∙∙∙∙∙∙]]]
#XFLD
confleuntDatatruncation=[[[Ƒąįĺ ŏŋ Ďąţą Ţŗűŋċąţįŏŋ∙∙∙∙∙∙]]]
#XFLD
isolationLevel=[[[Ĭşŏĺąţįŏŋ Ļēʋēĺ∙∙∙∙]]]
#XFLD
confluentOffset=[[[Ŝţąŗţįŋğ Ƥŏįŋţ∙∙∙∙∙]]]
#XFLD
signavioGroupDeltaFilesByText=[[[Ńŏŋē]]]
#XFLD
signavioFileFormatText=[[[Ƥąŗƣűēţ∙∙∙∙∙∙∙]]]
#XFLD
signavioSparkCompatibilityParquetText=[[[Ńŏ∙∙]]]
#XFLD
siganvioFileCompressionText=[[[Ŝŋąρρŷ∙∙∙∙∙∙∙∙]]]
#XFLD
siganvioSuppressDuplicatesText=[[[Ńŏ∙∙]]]

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=[[[Ƥŗŏĵēċţįŏŋş∙∙∙∙∙∙∙∙]]]
#XBUT
txtAdd=[[[Āƌƌ∙]]]
#XBUT
txtEdit=[[[Ĕƌįţ]]]
#XMSG
transformationText=[[[Āƌƌ ą ρŗŏĵēċţįŏŋ ţŏ şēţ űρ ƒįĺţēŗ ŏŗ ɱąρρįŋğ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
primaryKeyRequiredText=[[[Ŝēĺēċţ ą ρŗįɱąŗŷ ķēŷ ŵįţĥ Ĉŏŋƒįğűŗē Ŝċĥēɱą.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD
lblSettings=[[[Ŝēţţįŋğş∙∙∙∙∙∙]]]
#XFLD
lblTargetSetting=[[[{0}: Ţąŗğēţ Ŝēţţįŋğş]]]
#XMSG
@csvRF=[[[Ŝēĺēċţ ţĥē ƒįĺē ţĥąţ ċŏŋţąįŋş ţĥē şċĥēɱą ƌēƒįŋįţįŏŋ ŷŏű ŵąŋţ ţŏ ąρρĺŷ ţŏ ąĺĺ ƒįĺēş įŋ ţĥē ƒŏĺƌēŗ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD
lblSourceColumns=[[[Ŝŏűŗċē Ĉŏĺűɱŋş∙∙∙∙∙]]]
#XFLD
lblJsonStructure=[[[ĴŜŎŃ Ŝţŗűċţűŗē∙∙∙∙∙]]]
#XFLD
lblSourceSetting=[[[{0}: Ŝŏűŗċē Ŝēţţįŋğş]]]
#XFLD
lblSourceSchemaSetting=[[[{0}: Ŝŏűŗċē Ŝċĥēɱą Ŝēţţįŋğş]]]
#XBUT
messageSettings=[[[Μēşşąğē Ŝēţţįŋğş∙∙∙∙∙∙∙∙]]]
#XFLD
lblPropertyTitle1=[[[Ŏƃĵēċţ Ƥŗŏρēŗţįēş∙∙∙∙∙∙∙]]]
#XFLD
lblRFPropertyTitle=[[[Řēρĺįċąţįŏŋ Ƒĺŏŵ Ƥŗŏρēŗţįēş∙∙∙∙∙∙∙∙]]]
#XMSG
noDataTxt=[[[Ţĥēŗē ąŗē ŋŏ ċŏĺűɱŋş ţŏ ƌįşρĺąŷ.∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
noTargetObjectText=[[[Ţĥēŗē įş ŋŏ ţąŗğēţ ŏƃĵēċţ şēĺēċţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XLNK Ignore for translation
txtLessThen=[[[<∙∙∙]]]
#XFLD
targetColumns=[[[Ţąŗğēţ Ĉŏĺűɱŋş∙∙∙∙∙]]]
#XMSG
searchColumns=[[[Ŝēąŗċĥ Ĉŏĺűɱŋş∙∙∙∙∙]]]
#XTOL
cdcColumnTooltip=[[[Ĉŏĺűɱŋ ƒŏŗ Ďēĺţą Ĉąρţűŗē∙∙∙∙∙∙]]]
#XMSG
sourceNonDeltaSupportErrorUpdated=[[[Ţĥē şŏűŗċē ŏƃĵēċţ ƌŏēş ŋŏţ şűρρŏŗţ ƌēĺţą ċąρţűŗē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
targetCDCColumnAdded=[[[2 ţąŗğēţ ċŏĺűɱŋş ŵēŗē ąƌƌēƌ ƒŏŗ ƌēĺţą ċąρţűŗē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
deltaPartitionEnable=[[[Ŏƃĵēċţ Ţĥŗēąƌ Ļįɱįţ ƒŏŗ Ďēĺţą Ļŏąƌş ąƌƌēƌ ţŏ ţĥē şŏűŗċē şēţţįŋğş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
attributeMappingRemovalTxt=[[[Řēɱŏʋįŋğ įŋʋąĺįƌ ɱąρρįŋğş ŵĥįċĥ ąŗē ŋŏţ şűρρŏŗţēƌ ƒŏŗ ŋēŵ ţąŗğēţ ŏƃĵēċţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
targetCDCColumnRemoved=[[[2 ţąŗğēţ ċŏĺűɱŋş űşēƌ ƒŏŗ ƌēĺţą ċąρţűŗē ŵēŗē ŗēɱŏʋēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
replicationLoadTypeChanged=[[[Ļŏąƌ ţŷρē ċĥąŋğēƌ ţŏ "Ĭŋįţįąĺ ąŋƌ Ďēĺţą".∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
sourceHDLFLoadTypeError=[[[Ĉĥąŋğē ĺŏąƌ ţŷρē ţŏ "Ĭŋįţįąĺ ąŋƌ Ďēĺţą".∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
sourceHDLFLoadTypeSubtitle=[[[{0}]]]
#XMSG
sourceHDLFLoadTypeErrorDescription1=[[[Ţŏ ŗēρĺįċąţē ąŋ ŏƃĵēċţ ƒŗŏɱ ą şŏűŗċē ċŏŋŋēċţįŏŋ ŵįţĥ ţĥē ċŏŋŋēċţįŏŋ ţŷρē ŜĀƤ ĤĀŃĀ Ĉĺŏűƌ, Ďąţą Ļąķē Ƒįĺēş ţŏ ŜĀƤ Ďąţąşρĥēŗē, ŷŏű ɱűşţ űşē ţĥē ĺŏąƌ ţŷρē "įŋįţįąĺ ąŋƌ ƌēĺţą".∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=[[[Ĕŋąƃĺē Ďēĺţą Ĉąρţűŗē.∙∙∙∙∙]]]
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget=[[[{0}]]]
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=[[[Ţŏ ŗēρĺįċąţē ąŋ ŏƃĵēċţ ƒŗŏɱ ą şŏűŗċē ċŏŋŋēċţįŏŋ ŵįţĥ ţĥē ċŏŋŋēċţįŏŋ ţŷρē ŜĀƤ ĤĀŃĀ Ĉĺŏűƌ, Ďąţą Ļąķē Ƒįĺēş ţŏ ŜĀƤ Ďąţąşρĥēŗē, ŷŏű ɱűşţ ēŋąƃĺē ƌēĺţą ċąρţűŗē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=[[[Ĉĥąŋğē ţąŗğēţ Ŏƃĵēċţ.∙∙∙∙∙]]]
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget=[[[{0}]]]
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=[[[Ţĥē ţąŗğēţ ŏƃĵēċţ ċąŋŋŏţ ƃē űşēƌ ƃēċąűşē ƌēĺţą ċąρţűŗē įş ƌįşąƃĺēƌ. Ŷŏű ċąŋ ēįţĥēŗ ŗēŋąɱē ţĥē ţąŗğēţ ŏƃĵēċţ (ŵĥįċĥ ąĺĺŏŵş ą ŋēŵ ŏƃĵēċţ ŵįţĥ ƌēĺţą ċąρţűŗē ţŏ ƃē ċŗēąţēƌ) ŏŗ ɱąρ įţ ţŏ ąŋ ēχįşţįŋğ ŏƃĵēċţ ŵįţĥ ƌēĺţą ċąρţűŗē ēŋąƃĺēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
deltaPartitionError=[[[Ĕŋţēŗ ą ʋąĺįƌ ŏƃĵēċţ ţĥŗēąƌ ċŏűŋţ ƒŏŗ ƌēĺţą ĺŏąƌş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
deltaPartitionErrorDescription=[[[Ĕŋţēŗ ą ʋąĺűē ƃēţŵēēŋ 1 ąŋƌ 10.∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
deltaPartitionEmptyError=[[[Ĕŋţēŗ ąŋ ŏƃĵēċţ ţĥŗēąƌ ċŏűŋţ ƒŏŗ ƌēĺţą ĺŏąƌş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD
@lblColumnDescription=[[[Ďēşċŗįρţįŏŋ∙∙∙∙∙∙∙∙]]]
#XMSG
@lblColumnDescriptionText1=[[[Ƒŏŗ ţēċĥŋįċąĺ ρűŗρŏşēş - ĥąŋƌĺįŋğ ŏƒ ƌűρĺįċąţē ŗēċŏŗƌş ċąűşēƌ ƃŷ įşşűēş ƌűŗįŋğ ŗēρĺįċąţįŏŋ ŏƒ ĀƁĀƤ-ƃąşēƌ şŏűŗċē ŏƃĵēċţş ţĥąţ ƌŏ ŋŏţ ĥąʋē ą ρŗįɱąŗŷ ķēŷ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD
storageType=[[[Ŝţŏŗąğē∙∙∙∙∙∙∙]]]
#XFLD
skipUnmappedColLbl=[[[Ŝķįρ Ůŋɱąρρēƌ Ĉŏĺűɱŋş∙∙∙∙∙]]]
#XFLD
abapContentTypeLbl=[[[Ĉŏŋţēŋţ Ţŷρē∙∙∙∙∙∙∙]]]
#XFLD
autoMergeForTargetLbl=[[[Μēŗğē Ďąţą Āűţŏɱąţįċąĺĺŷ∙∙∙∙∙∙]]]
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=[[[Ģēŋēŗąĺ∙∙∙∙∙∙∙]]]
#XFLD
lblBusinessName=[[[Ɓűşįŋēşş Ńąɱē∙∙∙∙∙∙]]]
#XFLD
lblTechnicalName=[[[Ţēċĥŋįċąĺ Ńąɱē∙∙∙∙∙]]]
#XFLD
lblPackage=[[[Ƥąċķąğē∙∙∙∙∙∙∙]]]
#XFLD
statusPanel=[[[Řűŋ Ŝţąţűş∙∙∙∙]]]
#XBTN: Schedule dropdown menu
SCHEDULE=[[[Ŝċĥēƌűĺē∙∙∙∙∙∙]]]
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=[[[Ĕƌįţ Ŝċĥēƌűĺē∙∙∙∙∙∙]]]
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=[[[Ďēĺēţē Ŝċĥēƌűĺē∙∙∙∙]]]
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=[[[Ĉŗēąţē Ŝċĥēƌűĺē∙∙∙∙]]]
#XFLD: empty cell
EMPTY_CELL=[[[---∙]]]
#XMSG:
SCHEDULE_CHECK_FAILED=[[[Ƒąįĺēƌ şċĥēƌűĺē ʋąĺįƌąţįŏŋ ċĥēċķ∙∙∙∙∙∙∙∙∙∙]]]
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=[[[Ā şċĥēƌűĺē ċąŋŋŏţ ƃē ċŗēąţēƌ ƃēċąűşē ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ įş ċűŗŗēŋţĺŷ ƃēįŋğ ƌēρĺŏŷēƌ.{0}Ƥĺēąşē ŵąįţ űŋţįĺ ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ ĥąş ƃēēŋ ƌēρĺŏŷēƌ.]]]
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=[[[Ƒŏŗ ŗēρĺįċąţįŏŋ ƒĺŏŵş ţĥąţ ċŏŋţąįŋ ŏƃĵēċţş ŵįţĥ ĺŏąƌ ţŷρē "Ĭŋįţįąĺ ąŋƌ Ďēĺţą", ŋŏ şċĥēƌűĺē ċąŋ ƃē ċŗēąţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=[[[Ƒŏŗ ŗēρĺįċąţįŏŋ ƒĺŏŵş ţĥąţ ċŏŋţąįŋ ŏƃĵēċţş ŵįţĥ ĺŏąƌ ţŷρē "Ĭŋįţįąĺ ąŋƌ Ďēĺţą/Ďēĺţą Ŏŋĺŷ", ŋŏ şċĥēƌűĺē ċąŋ ƃē ċŗēąţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD : Scheduled popover
SCHEDULED=[[[Ŝċĥēƌűĺēƌ∙∙∙∙∙]]]
#XFLD
CREATE_REPLICATION_TEXT=[[[Ĉŗēąţē ą Řēρĺįċąţįŏŋ Ƒĺŏŵ∙∙∙∙∙∙∙]]]
#XFLD
EDIT_REPLICATION_TEXT=[[[Ĕƌįţ ą Řēρĺįċąţįŏŋ Ƒĺŏŵ∙∙∙∙∙∙]]]
#XFLD
DELETE_REPLICATION_TEXT=[[[Ďēĺēţē ą Řēρĺįċąţįŏŋ Ƒĺŏŵ∙∙∙∙∙∙∙]]]
#XFLD
REFRESH_FREQUENCY=[[[Ƒŗēƣűēŋċŷ∙∙∙∙∙]]]
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=[[[Ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ ċąŋŋŏţ ƃē ƌēρĺŏŷēƌ ƃēċąűşē ţĥē ēχįşţįŋğ şċĥēƌűĺē{0} ƌŏēş ŋŏţ ŷēţ şűρρŏŗţ ţĥē ĺŏąƌ ţŷρē "Ĭŋįţįąĺ ąŋƌ Ďēĺţą".{0}{0}Ţŏ ƌēρĺŏŷ ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ, ŷŏű ɱűşţ şēţ ţĥē ĺŏąƌ ţŷρēş ŏƒ ąĺĺ ŏƃĵēċţş{0} ţŏ "Ĭŋįţįąĺ ŏŋĺŷ". Āĺţēŗŋąţįʋēĺŷ, ŷŏű ċąŋ ƌēĺēţē ţĥē şċĥēƌűĺē, ƌēρĺŏŷ ţĥē {0}ŗēρĺįċąţįŏŋ ƒĺŏŵ, ąŋƌ ţĥēŋ şţąŗţ ą ŋēŵ ŗűŋ. Ţĥįş ŗēşűĺţş įŋ ą ŗűŋ ŵįţĥŏűţ {0}ēŋƌ, ŵĥįċĥ ąĺşŏ şűρρŏŗţş ŏƃĵēċţş ŵįţĥ ţĥē ĺŏąƌ ţŷρē "Ĭŋįţįąĺ ąŋƌ Ďēĺţą".]]]
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=[[[Ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ ċąŋŋŏţ ƃē ƌēρĺŏŷēƌ ƃēċąűşē ţĥē ēχįşţįŋğ şċĥēƌűĺē{0} ƌŏēş ŋŏţ ŷēţ şűρρŏŗţ ţĥē ĺŏąƌ ţŷρē "Ĭŋįţįąĺ ąŋƌ Ďēĺţą/Ďēĺţą Ŏŋĺŷ".{0}{0}Ţŏ ƌēρĺŏŷ ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ, ŷŏű ɱűşţ şēţ ţĥē ĺŏąƌ ţŷρēş ŏƒ ąĺĺ ŏƃĵēċţş{0} ţŏ "Ĭŋįţįąĺ ŏŋĺŷ". Āĺţēŗŋąţįʋēĺŷ, ŷŏű ċąŋ ƌēĺēţē ţĥē şċĥēƌűĺē, ƌēρĺŏŷ ţĥē {0}ŗēρĺįċąţįŏŋ ƒĺŏŵ, ąŋƌ ţĥēŋ şţąŗţ ą ŋēŵ ŗűŋ. Ţĥįş ŗēşűĺţş įŋ ą ŗűŋ ŵįţĥŏűţ {0}ēŋƌ, ŵĥįċĥ ąĺşŏ şűρρŏŗţş ŏƃĵēċţş ŵįţĥ ţĥē ĺŏąƌ ţŷρē "Ĭŋįţįąĺ ąŋƌ Ďēĺţą/Ďēĺţą Ŏŋĺŷ".]]]
#XMSG
SCHEDULE_EXCEPTION=[[[Ƒąįĺēƌ ğēţţįŋğ şċĥēƌűĺē ƌēţąįĺş∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: Label for frequency column
everyLabel=[[[Ĕʋēŗŷ∙∙∙∙∙∙∙∙∙]]]
#XFLD: Plural Recurrence text for Hour
hoursLabel=[[[Ĥŏűŗş∙∙∙∙∙∙∙∙∙]]]
#XFLD: Plural Recurrence text for Day
daysLabel=[[[Ďąŷş]]]
#XFLD: Plural Recurrence text for Month
monthsLabel=[[[Μŏŋţĥş∙∙∙∙∙∙∙∙]]]
#XFLD: Plural Recurrence text for Minutes
minutesLabel=[[[Μįŋűţēş∙∙∙∙∙∙∙]]]
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=[[[Ƒąįĺēƌ ţŏ ğēţ įŋƒŏŗɱąţįŏŋ ąƃŏűţ şċĥēƌűĺē ρŏşşįƃįĺįţŷ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD :Paused field
PAUSED=[[[Ƥąűşēƌ∙∙∙∙∙∙∙∙]]]
#XMSG
navToMonitoring=[[[Ŏρēŋ įŋ Ƒĺŏŵş Μŏŋįţŏŗ∙∙∙∙∙]]]
#XFLD
statusLbl=[[[Ŝţąţűş∙∙∙∙∙∙∙∙]]]
#XFLD
lblLastRunExecuted=[[[Ļąşţ Řűŋ Ŝţąŗţ∙∙∙∙∙]]]
#XFLD
lblLastExecuted=[[[Ļąşţ Řűŋ∙∙∙∙∙∙]]]
#XFLD: Status text for Completed
statusCompleted=[[[Ĉŏɱρĺēţēƌ∙∙∙∙∙]]]
#XFLD: Status text for Running
statusRunning=[[[Řűŋŋįŋğ∙∙∙∙∙∙∙]]]
#XFLD: Status text for Failed
statusFailed=[[[Ƒąįĺēƌ∙∙∙∙∙∙∙∙]]]
#XFLD: Status text for Stopped
statusStopped=[[[Ŝţŏρρēƌ∙∙∙∙∙∙∙]]]
#XFLD: Status text for Stopping
statusStopping=[[[Ŝţŏρρįŋğ∙∙∙∙∙∙]]]
#XFLD: Status text for Active
statusActive=[[[Āċţįʋē∙∙∙∙∙∙∙∙]]]
#XFLD: Status text for Paused
statusPaused=[[[Ƥąűşēƌ∙∙∙∙∙∙∙∙]]]
#XFLD: Status text for not executed
lblNotExecuted=[[[Ńŏţ Řűŋ Ŷēţ∙∙∙∙∙∙∙∙]]]
#XFLD
messagesSettings=[[[Μēşşąğēş Ŝēţţįŋğş∙∙∙∙∙∙∙]]]
#XTOL
@validateModel=[[[Ʋąĺįƌąţįŏŋ Μēşşąğēş∙∙∙∙∙]]]
#XTOL
@hierarchy=[[[Ĥįēŗąŗċĥŷ∙∙∙∙∙]]]
#XTOL
@columnCount=[[[Ńűɱƃēŗ ŏƒ Ĉŏĺűɱŋş∙∙∙∙∙∙∙]]]
#XMSG
VAL_PACKAGE_CHANGED=[[[Ŷŏű ĥąʋē ąşşįğŋēƌ ţĥįş ŏƃĵēċţ ţŏ ρąċķąğē "{1}". Ĉĺįċķ “Ŝąʋē” ţŏ ċŏŋƒįŗɱ ąŋƌ ʋąĺįƌąţē ţĥįş ċĥąŋğē. Ńŏţē ţĥąţ ąşşįğŋɱēŋţ ţŏ ą ρąċķąğē ċąŋŋŏţ ƃē űŋƌŏŋē įŋ ţĥįş ēƌįţŏŗ ąƒţēŗ ŷŏű şąʋē.]]]
#XMSG
MISSING_DEPENDENCY=[[[Ďēρēŋƌēŋċįēş ŏƒ ŏƃĵēċţ "{0}" ċąŋŋŏţ ƃē ŗēşŏĺʋēƌ įŋ ρąċķąğē "{1}".]]]
#XFLD
deltaLoadInterval=[[[Ďēĺţą Ļŏąƌ Ĭŋţēŗʋąĺ∙∙∙∙∙]]]
#XFLD
lblHour=[[[Ĥŏűŗş (0-24)∙∙∙∙∙∙∙]]]
#XFLD
lblMinutes=[[[Μįŋűţēş (0-59)∙∙∙∙∙]]]
#XMSG
maxHourOrMinErr=[[[Ĕŋţēŗ ą ʋąĺűē ƃēţŵēēŋ 0 ąŋƌ {0}]]]
#XMSG
maxDeltaInterval=[[[Ţĥē ɱąχįɱűɱ ʋąĺűē ŏƒ ţĥē ƌēĺţą ĺŏąƌ įŋţēŗʋąĺ įş 24 ĥŏűŗş.{0}Ĉĥąŋğē ţĥē ɱįŋűţē ʋąĺűē ŏŗ ţĥē ĥŏűŗ ʋąĺűē ąċċŏŗƌįŋğĺŷ.]]]
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=[[[Ţąŗğēţ Ĉŏŋţąįŋēŗ Ƥąţĥ∙∙∙∙∙]]]
#XFLD
confluentSubjectName=[[[Ŝűƃĵēċţ Ńąɱē∙∙∙∙∙∙∙]]]
#XFLD
confluentSchemaVersion=[[[Ŝċĥēɱą Ʋēŗşįŏŋ∙∙∙∙∙]]]
#XFLD
confluentIncludeTechKeyUpdated=[[[Ĭŋċĺűƌē Ţēċĥŋįċąĺ Ķēŷ∙∙∙∙∙]]]
#XFLD
confluentOmitNonExpandedArrays=[[[Ŏɱįţ Ńŏŋ-Ĕχρąŋƌēƌ Āŗŗąŷş∙∙∙∙∙∙]]]
#XFLD
confluentExpandArrayOrMap=[[[Ĕχρąŋƌ Āŗŗąŷ ŏŗ Μąρ∙∙∙∙∙]]]
#XCOL
confluentOperationMapping=[[[Ŏρēŗąţįŏŋ Μąρρįŋğ∙∙∙∙∙∙∙]]]
#XCOL
confluentOpCode=[[[Ŏρċŏƌē∙∙∙∙∙∙∙∙]]]
#XFLD
confluentInsertOpCode=[[[Ĭŋşēŗţ∙∙∙∙∙∙∙∙]]]
#XFLD
confluentUpdateOpCode=[[[Ůρƌąţē∙∙∙∙∙∙∙∙]]]
#XFLD
confluentDeleteOpCode=[[[Ďēĺēţē∙∙∙∙∙∙∙∙]]]
#XFLD
expandArrayOrMapNotSelectedTxt=[[[Ńŏţ Ŝēĺēċţēƌ∙∙∙∙∙∙∙]]]
#XFLD
confluentSwitchTxtYes=[[[Ŷēş∙]]]
#XFLD
confluentSwitchTxtNo=[[[Ńŏ∙∙]]]
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=[[[Ĕŗŗŏŗ∙∙∙∙∙∙∙∙∙]]]
#XTIT
executeWarning=[[[Ŵąŗŋįŋğ∙∙∙∙∙∙∙]]]
#XMSG
executeunsavederror=[[[Ŝąʋē ŷŏűŗ ŗēρĺįċąţįŏŋ ƒĺŏŵ ƃēƒŏŗē ŷŏű ŗűŋ įţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
executemodifiederror=[[[Ţĥēŗē ąŗē űŋşąʋēƌ ċĥąŋğēş įŋ ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ. Ƥĺēąşē şąʋē ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
executeundeployederror=[[[Ŷŏű ɱűşţ ƌēρĺŏŷ ŷŏűŗ ŗēρĺįċąţįŏŋ ƒĺŏŵ ƃēƒŏŗē ŷŏű ċąŋ ŗűŋ įţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
executedeployingerror=[[[Ƥĺēąşē ŵąįţ ƒŏŗ ţĥē ƌēρĺŏŷɱēŋţ ţŏ ƒįŋįşĥ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
msgRunStarted=[[[Řűŋ şţąŗţēƌ∙∙∙∙∙∙∙∙]]]
#XMSG
msgExecuteFail=[[[Ƒąįĺēƌ ţŏ ŗűŋ ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
titleExecuteBusy=[[[Ƥĺēąşē ŵąįţ.∙∙∙∙∙∙∙]]]
#XMSG
msgExecuteBusy=[[[Ŵē ąŗē ρŗēρąŗįŋğ ŷŏűŗ ƌąţą ţŏ ŗűŋ ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTIT
executeConfirmDialog=[[[Ŵąŗŋįŋğ∙∙∙∙∙∙∙]]]
#XMSG
msgExecuteWithValidations=[[[Řēρĺįċąţįŏŋ ƒĺŏŵ ĥąş ʋąĺįƌąţįŏŋ ēŗŗŏŗş. Řűŋŋįŋğ ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ ɱąŷ ŗēşűĺţ įŋ ƒąįĺűŗē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
msgRunDeployedVersion=[[[Ţĥēŗē ąŗē ċĥąŋğēş ţŏ ƌēρĺŏŷ. Ţĥē ĺąşţ ƌēρĺŏŷēƌ ʋēŗşįŏŋ ŏƒ ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ ŵįĺĺ ƃē ŗűŋ. Ďŏ ŷŏű ŵąŋţ ţŏ ċŏŋţįŋűē?∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XBUT
btnExecuteAnyway=[[[Řűŋ Āŋŷŵąŷ∙∙∙∙]]]
#XBUT
btnExecuteClose=[[[Ĉĺŏşē∙∙∙∙∙∙∙∙∙]]]
#XBUT
loaderClose=[[[Ĉĺŏşē∙∙∙∙∙∙∙∙∙]]]
#XTIT
loaderTitle=[[[Ļŏąƌįŋğ∙∙∙∙∙∙∙]]]
#XMSG
loaderText=[[[Ƒēţċĥįŋğ ƌēţąįĺş ƒŗŏɱ ţĥē şēŗʋēŗ∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=[[[Ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ ţŏ ţĥįş ŋŏŋ-ŜĀƤ ţąŗğēţ ċŏŋŋēċţįŏŋ ċąŋŋŏţ ƃē şţąŗţēƌ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=[[[ƃēċąűşē ţĥēŗē įş ŋŏ ŏűţƃŏűŋƌ ʋŏĺűɱē ąʋąįĺąƃĺē ƒŏŗ ţĥįş ɱŏŋţĥ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
premiumOutBoundRFAdminErrMsgPart1=[[[Āŋ ąƌɱįŋįşţŗąţŏŗ ċąŋ įŋċŗēąşē ţĥē Ƥŗēɱįűɱ Ŏűţƃŏűŋƌ ƃĺŏċķş ƒŏŗ ţĥįş∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
premiumOutBoundRFAdminErrMsgPart2=[[[ţēŋąŋţ, ɱąķįŋğ ŏűţƃŏűŋƌ ʋŏĺűɱē ąʋąįĺąƃĺē ƒŏŗ ţĥįş ɱŏŋţĥ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=[[[Ĕŗŗŏŗ∙∙∙∙∙∙∙∙∙]]]
#XTIT
deployInfo=[[[Ĭŋƒŏŗɱąţįŏŋ∙∙∙∙∙∙∙∙]]]
#XMSG
deployCheckFailException=[[[Ĕχċēρţįŏŋ ŏċċűŗŗēƌ ƌűŗįŋğ ƌēρĺŏŷɱēŋţ∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
deployGBQFFDisabled=[[[Ďēρĺŏŷįŋğ ŗēρĺįċąţįŏŋ ƒĺŏŵş ŵįţĥ ą ţąŗğēţ ċŏŋŋēċţįŏŋ ţŏ Ģŏŏğĺē ƁįğǬűēŗŷ įş ċűŗŗēŋţĺŷ ŋŏţ ρŏşşįƃĺē ƃēċąűşē ŵē ąŗē ρēŗƒŏŗɱįŋğ ɱąįŋţēŋąŋċē ŏŋ ţĥįş ƒűŋċţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
deployKAFKAFFDisabled=[[[Ďēρĺŏŷįŋğ ŗēρĺįċąţįŏŋ ƒĺŏŵş ŵįţĥ ą ţąŗğēţ ċŏŋŋēċţįŏŋ ţŏ Āρąċĥē Ķąƒķą įş ċűŗŗēŋţĺŷ ŋŏţ ρŏşşįƃĺē ƃēċąűşē ŵē ąŗē ρēŗƒŏŗɱįŋğ ɱąįŋţēŋąŋċē ŏŋ ţĥįş ƒűŋċţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
deployConfluentDisabled=[[[Ďēρĺŏŷįŋğ ŗēρĺįċąţįŏŋ ƒĺŏŵş ŵįţĥ ą ţąŗğēţ ċŏŋŋēċţįŏŋ ţŏ Ĉŏŋƒĺűēŋţ Ķąƒķą įş ċűŗŗēŋţĺŷ ŋŏţ ρŏşşįƃĺē ƃēċąűşē ŵē ąŗē ρēŗƒŏŗɱįŋğ ɱąįŋţēŋąŋċē ŏŋ ţĥįş ƒűŋċţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
deployInValidDWCTargetDeltaCaptureObject=[[[Ƒŏŗ ţĥē ƒŏĺĺŏŵįŋğ ţąŗğēţ ŏƃĵēċţş, ţĥē ƌēĺţą ċąρţűŗē ţąƃĺē ŋąɱēş ąŗē ąĺŗēąƌŷ űşēƌ ƃŷ ŏţĥēŗ ţąƃĺēş įŋ ţĥē ŗēρŏşįţŏŗŷ: {0} Ŷŏű ɱűşţ ŗēŋąɱē ţĥēşē ţąŗğēţ ŏƃĵēċţş ţŏ ēŋşűŗē ţĥąţ ţĥē ąşşŏċįąţēƌ ƌēĺţą ċąρţűŗē ţąƃĺē ŋąɱēş ąŗē űŋįƣűē ƃēƒŏŗē ŷŏű ċąŋ ƌēρĺŏŷ ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ.]]]
#XMSG
deployDWCSourceFFDisabled=[[[Ďēρĺŏŷįŋğ ŗēρĺįċąţįŏŋ ƒĺŏŵş ţĥąţ ĥąʋē ŜĀƤ Ďąţąşρĥēŗē ąş ţĥēįŗ şŏűŗċē įş ċűŗŗēŋţĺŷ ŋŏţ ρŏşşįƃĺē ƃēċąűşē ŵē ąŗē ρēŗƒŏŗɱįŋğ ɱąįŋţēŋąŋċē ŏŋ ţĥįş ƒűŋċţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=[[[Ďēρĺŏŷįŋğ ŗēρĺįċąţįŏŋ ƒĺŏŵş ţĥąţ ċŏŋţąįŋ ƌēĺţą-ēŋąƃĺēƌ ĺŏċąĺ ţąƃĺēş ąş şŏűŗċē ŏƃĵēċţş įş ċűŗŗēŋţĺŷ ŋŏţ ρŏşşįƃĺē ƃēċąűşē ŵē ąŗē ρēŗƒŏŗɱįŋğ ɱąįŋţēŋąŋċē ŏŋ ţĥįş ƒűŋċţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
deployHDLFSourceFFDisabled=[[[Ďēρĺŏŷįŋğ ŗēρĺįċąţįŏŋ ƒĺŏŵş ţĥąţ ĥąʋē şŏűŗċē ċŏŋŋēċţįŏŋş ŵįţĥ ţĥē ċŏŋŋēċţįŏŋ ţŷρē ŜĀƤ ĤĀŃĀ Ĉĺŏűƌ, Ďąţą Ļąķē Ƒįĺēş įş ċűŗŗēŋţĺŷ ŋŏţ ρŏşşįƃĺē ƃēċąűşē ŵē ąŗē ρēŗƒŏŗɱįŋğ ɱąįŋţēŋąŋċē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
deployObjectStoreAsSourceFFDisabled=[[[Ďēρĺŏŷįŋğ ŗēρĺįċąţįŏŋ ƒĺŏŵş ţĥąţ ĥąʋē ą ċĺŏűƌ şţŏŗąğē ρŗŏʋįƌēŗş ąş ţĥēįŗ şŏűŗċē įş ċűŗŗēŋţĺŷ ŋŏţ ρŏşşįƃĺē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
deployConfluentSourceFFDisabled=[[[Ďēρĺŏŷįŋğ ŗēρĺįċąţįŏŋ ƒĺŏŵş ţĥąţ ĥąʋē Ĉŏŋƒĺűēŋţ Ķąƒķą ąş ţĥēįŗ şŏűŗċē įş ċűŗŗēŋţĺŷ ŋŏţ ρŏşşįƃĺē ƃēċąűşē ŵē ąŗē ρēŗƒŏŗɱįŋğ ɱąįŋţēŋąŋċē ŏŋ ţĥįş ƒűŋċţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
deployMaxDWCNewTableCrossed=[[[Ƒŏŗ ĺąŗğē ŗēρĺįċąţįŏŋ ƒĺŏŵş, įţş ŋŏţ ρŏşşįƃĺē ţŏ "şąʋē ąŋƌ ƌēρĺŏŷ" ţĥēɱ įŋ ŏŋē ğŏ. Ƥĺēąşē şąʋē ŷŏűŗ ŗēρĺįċąţįŏŋ ƒĺŏŵ ƒįŗşţ ąŋƌ ţĥēŋ ƌēρĺŏŷ įţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
deployInProgressInfo=[[[Ďēρĺŏŷɱēŋţ įş ąĺŗēąƌŷ įŋ ρŗŏğŗēşş.∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
deploySourceObjectInUse=[[[Ŝŏűŗċē ŏƃĵēċţş {0} ąŗē ąĺŗēąƌŷ ƃēįŋğ űşēƌ įŋ ŗēρĺįċąţįŏŋ ƒĺŏŵş {1}.]]]
#XMSG
deployTargetSourceObjectInUse=[[[Ŝŏűŗċē ŏƃĵēċţş {0} ąŗē ąĺŗēąƌŷ ƃēįŋğ űşēƌ įŋ ŗēρĺįċąţįŏŋ ƒĺŏŵş {1}. Ţąŗğēţ ŏƃĵēċţş {2} ąŗē ąĺŗēąƌŷ ƃēįŋğ űşēƌ įŋ ŗēρĺįċąţįŏŋ ƒĺŏŵş {3}.]]]
#XMSG
deployReplicationFlowCheckError=[[[Ĕŗŗŏŗ ŵĥįĺē ʋēŗįƒŷįŋğ ŗēρĺįċąţįŏŋ ƒĺŏŵ: {0}]]]
#XMSG
preDeployTargetObjectInUse=[[[Ţąŗğēţ ŏƃĵēċţş {0} ąŗē ąĺŗēąƌŷ ƃēįŋğ űşēƌ įŋ ŗēρĺįċąţįŏŋ ƒĺŏŵş {1}, ąŋƌ ŷŏű ċąŋ’ţ ĥąʋē ţĥē şąɱē ţąŗğēţ ŏƃĵēċţ įŋ ţŵŏ ƌįƒƒēŗēŋţ ŗēρĺįċąţįŏŋ ƒĺŏŵş. Ŝēĺēċţ ąŋŏţĥēŗ ţąŗğēţ ŏƃĵēċţ ąŋƌ ţŗŷ ąğąįŋ.]]]
#XMSG
runInProgressInfo=[[[Řēρĺįċąţįŏŋ ƒĺŏŵ įş ąĺŗēąƌŷ ŗűŋŋįŋğ.∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
deploySignavioTargetFFDisabled=[[[Ďēρĺŏŷįŋğ ŗēρĺįċąţįŏŋ ƒĺŏŵş ţĥąţ ĥąʋē ŜĀƤ Ŝįğŋąʋįŏ ąş ţĥēįŗ ţąŗğēţ įş ċűŗŗēŋţĺŷ ŋŏţ ρŏşşįƃĺē ƃēċąűşē ŵē ąŗē ρēŗƒŏŗɱįŋğ ɱąįŋţēŋąŋċē ŏŋ ţĥįş ƒűŋċţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
deployHanaViewAsSourceFFDisabled=[[[Ďēρĺŏŷįŋğ ŗēρĺįċąţįŏŋ ƒĺŏŵş ţĥąţ ĥąʋē ʋįēŵş ąş şŏűŗċē ŏƃĵēċţş ƒŏŗ ţĥē şēĺēċţēƌ şŏűŗċē ċŏŋŋēċţįŏŋ įş ċűŗŗēŋţĺŷ ŋŏţ ρŏşşįƃĺē. Ţŗŷ ąğąįŋ ĺąţēŗ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
deployMsOneLakeTargetFFDisabled=[[[Ďēρĺŏŷįŋğ ŗēρĺįċąţįŏŋ ƒĺŏŵş ţĥąţ ĥąʋē ΜŜ ŎŋēĻąķē ąş ţĥēįŗ ţąŗğēţ įş ċűŗŗēŋţĺŷ ŋŏţ ρŏşşįƃĺē ƃēċąűşē ŵē ąŗē ρēŗƒŏŗɱįŋğ ɱąįŋţēŋąŋċē ŏŋ ţĥįş ƒűŋċţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
deploySFTPTargetFFDisabled=[[[Ďēρĺŏŷįŋğ ŗēρĺįċąţįŏŋ ƒĺŏŵş ţĥąţ ĥąʋē ŜƑŢƤ ąş ţĥēįŗ ţąŗğēţ įş ċűŗŗēŋţĺŷ ŋŏţ ρŏşşįƃĺē ƃēċąűşē ŵē ąŗē ρēŗƒŏŗɱįŋğ ɱąįŋţēŋąŋċē ŏŋ ţĥįş ƒűŋċţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
deploySFTPSourceFFDisabled=[[[Ďēρĺŏŷįŋğ ŗēρĺįċąţįŏŋ ƒĺŏŵş ţĥąţ ĥąʋē ŜƑŢƤ ąş ţĥēįŗ şŏűŗċē įş ċűŗŗēŋţĺŷ ŋŏţ ρŏşşįƃĺē ƃēċąűşē ŵē ąŗē ρēŗƒŏŗɱįŋğ ɱąįŋţēŋąŋċē ŏŋ ţĥįş ƒűŋċţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=[[[Ţēċĥŋįċąĺ Ńąɱē∙∙∙∙∙]]]
#XFLD
businessNameInRenameTarget=[[[Ɓűşįŋēşş Ńąɱē∙∙∙∙∙∙]]]
#XTOL
renametargetDialogTitle=[[[Řēŋąɱē Ţąŗğēţ Ŏƃĵēċţ∙∙∙∙]]]
#XBUT
targetRenameButton=[[[Řēŋąɱē∙∙∙∙∙∙∙∙]]]
#XBUT
targetRenameCancel=[[[Ĉąŋċēĺ∙∙∙∙∙∙∙∙]]]
#XMSG
mandatoryTargetName=[[[Ŷŏű ɱűşţ ēŋţēŗ ą ŋąɱē.∙∙∙∙∙]]]
#XMSG
dwcSpecialChar=[[[_(űŋƌēŗşċŏŗē) įş ţĥē ŏŋĺŷ şρēċįąĺ ċĥąŗąċţēŗ ąĺĺŏŵēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
dwcWithDot=[[[Ţĥē ţąŗğēţ ţąƃĺē ŋąɱē ċąŋ ċŏŋşįşţ ŏƒ Ļąţįŋ ĺēţţēŗş, ŋűɱƃēŗş, űŋƌēŗşċŏŗēş (_), ąŋƌ ρēŗįŏƌş (.). Ţĥē ƒįŗşţ ċĥąŗąċţēŗ ɱűşţ ƃē ą ĺēţţēŗ, ŋűɱƃēŗ, ŏŗ űŋƌēŗşċŏŗē (ŋŏţ ą ρēŗįŏƌ).∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
nonDwcSpecialChar=[[[Āĺĺŏŵēƌ şρēċįąĺ ċĥąŗąċţēŗş ąŗē _(űŋƌēŗşċŏŗē) -(ĥŷρĥēŋ) .(ƌŏţ)∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
firstUnderscorePattern=[[[Ńąɱē ɱűşţ ŋŏţ şţąŗţ ŵįţĥ _(űŋƌēŗşċŏŗē)∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL=[[[{0}: Ʋįēŵ ŜǬĻ Ĉŗēąţē Ţąƃĺē Ŝţąţēɱēŋţ]]]
#XMSG
sqlDialogMaxPKWarning=[[[Ĭŋ Ģŏŏğĺē ƁįğǬűēŗŷ, ą ɱąχįɱűɱ ŏƒ 16 ρŗįɱąŗŷ ķēŷş ąŗē şűρρŏŗţēƌ, ąŋƌ ţĥē şŏűŗċē ŏƃĵēċţ ĥąş ą ĺąŗğēŗ ŋűɱƃēŗ. Ţĥēŗēƒŏŗē, ŋŏ ρŗįɱąŗŷ ķēŷş ąŗē ƌēƒįŋēƌ įŋ ţĥįş şţąţēɱēŋţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XMSG
sqlDialogIncomptiblePKTypeWarning=[[[Ŏŋē ŏŗ ɱŏŗē şŏűŗċē ċŏĺűɱŋş ĥąʋē ƌąţą ţŷρēş ţĥąţ ċąŋŋŏţ ƃē ƌēƒįŋēƌ ąş ρŗįɱąŗŷ ķēŷş įŋ Ģŏŏğĺē ƁįğǬűēŗŷ.Ţĥēŗēƒŏŗē, ŋŏ ρŗįɱąŗŷ ķēŷş ąŗē ƌēƒįŋēƌ įŋ ţĥįş ċąşē. Ĭŋ Ģŏŏğĺē ƁįğǬűēŗŷ, ŏŋĺŷ ţĥē ƒŏĺĺŏŵįŋğ ƌąţą ţŷρēş ċąŋ ĥąʋē ą ρŗįɱąŗŷ ķēŷ: ƁĬĢŃŮΜĔŘĬĈ, ƁŎŎĻĔĀŃ, ĎĀŢĔ, ĎĀŢĔŢĬΜĔ, ĬŃŢ64, ŃŮΜĔŘĬĈ, ŜŢŘĬŃĢ, ŢĬΜĔŜŢĀΜƤ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XBUT
copyAndCloseDDL=[[[Ĉŏρŷ ąŋƌ Ĉĺŏşē∙∙∙∙∙]]]
#XBUT
closeDDL=[[[Ĉĺŏşē∙∙∙∙∙∙∙∙∙]]]
#XMSG
copiedToClipboard=[[[Ĉŏρįēƌ ţŏ ċĺįρƃŏąŗƌ∙∙∙∙∙]]]


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=[[[Ŏƃĵēċţ ''{0}'' ċąŋŋŏţ ƃē ρąŗţ ŏƒ ą ţąşķ ċĥąįŋ ƃēċąűşē įţ ƌŏēş ŋŏţ ĥąʋē ąŋ ēŋƌ (ąş įţ įŋċĺűƌēş ŏƃĵēċţş ŵįţĥ ĺŏąƌ ţŷρē Ĭŋįţįąĺ ąŋƌ Ďēĺţą/Ďēĺţą Ŏŋĺŷ).]]]
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=[[[Ŏƃĵēċţ ''{0}'' ċąŋŋŏţ ƃē ρąŗţ ŏƒ ą ţąşķ ċĥąįŋ ƃēċąűşē įţ ƌŏēş ŋŏţ ĥąʋē ąŋ ēŋƌ (ąş įţ įŋċĺűƌēş ŏƃĵēċţş ŵįţĥ ĺŏąƌ ţŷρē Ĭŋįţįąĺ ąŋƌ Ďēĺţą).]]]
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=[[[Ţĥē ŏƃĵēċţ "{0}" ċąŋŋŏţ ƃē ąƌƌēƌ ţŏ ţĥē ţąşķ ċĥąįŋ.]]]


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=[[[Ţĥēŗē ąŗē űŋşąʋēƌ ţąŗğēţ ŏƃĵēċţş. Ƥĺēąşē şąʋē ąğąįŋ.{0}{0} Ţĥē ƃēĥąʋįŏŗ ŏƒ ţĥįş ƒēąţűŗē ĥąş ċĥąŋğēƌ: Ĭŋ ţĥē ρąşţ, ţąŗğēţ ŏƃĵēċţş ŵēŗē ŏŋĺŷ ċŗēąţēƌ įŋ ţĥē ţąŗğēţ ēŋʋįŗŏŋɱēŋţ ŵĥēŋ ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ ŵąş ƌēρĺŏŷēƌ.{0} Ńŏŵ ţĥē ŏƃĵēċţş ąŗē ąĺŗēąƌŷ ċŗēąţēƌ ŵĥēŋ ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ įş şąʋēƌ. Ŷŏűŗ ŗēρĺįċąţįŏŋ ƒĺŏŵ ŵąş ċŗēąţēƌ ƃēƒŏŗē ţĥįş ċĥąŋğē ąŋƌ ċŏŋţąįŋş ŋēŵ ŏƃĵēċţş.{0} Ŷŏű ŋēēƌ ţŏ şąʋē ţĥē ŗēρĺįċąţįŏŋ ƒĺŏŵ ŏŋċē ąğąįŋ ƃēƒŏŗē ƌēρĺŏŷįŋğ įţ şŏ ţĥąţ ţĥē ŋēŵ ŏƃĵēċţş ąŗē įŋċĺűƌēƌ ċŏŗŗēċţĺŷ.]]]
#XMSG
confirmChangeContentTypeMessage=[[[Ŷŏű ąŗē ąƃŏűţ ţŏ ċĥąŋğē ţĥē ċŏŋţēŋţ ţŷρē. Ĭƒ ŷŏű ƌŏ şŏ, ąĺĺ ēχįşţįŋğ ρŗŏĵēċţįŏŋş ŵįĺĺ ƃē ƌēĺēţēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=[[[Ŝűƃĵēċţ Ńąɱē∙∙∙∙∙∙∙]]]
#XFLD
schemaDialogVersionName=[[[Ŝċĥēɱą Ʋēŗşįŏŋ∙∙∙∙∙]]]
#XFLD
includeTechKey=[[[Ĭŋċĺűƌē Ţēċĥŋįċąĺ Ķēŷ∙∙∙∙∙]]]
#XFLD
segementButtonFlat=[[[Ƒĺąţ]]]
#XFLD
segementButtonNested=[[[Ńēşţēƌ∙∙∙∙∙∙∙∙]]]
#XMSG
subjectNamePlaceholder=[[[Ŝēąŗċĥ Ŝűƃĵēċţ Ńąɱē∙∙∙∙∙]]]

#XMSG
@EmailNotificationSuccess=[[[Ĉŏŋƒįğűŗąţįŏŋ ŏƒ ŗűŋţįɱē ēɱąįĺ ŋŏţįƒįċąţįŏŋş įş şąʋēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XFLD
@RuntimeEmailNotification=[[[Řűŋţįɱē Ĕɱąįĺ Ńŏţįƒįċąţįŏŋ∙∙∙∙∙∙∙]]]

#XBTN
@TXT_SAVE=[[[Ŝąʋē]]]


