#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Flux de réplication

#XFLD: Edit Schema button text
editSchema=Modifier le schéma

#XTIT : Properties heading
configSchema=Configurer le schéma

#XFLD: save changed button text
applyChanges=Appliquer les modifications


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Sélectionner une connexion source
#XFLD
sourceContainernEmptyText=Sélectionner un conteneur
#XFLD
targetConnectionEmptyText=Sélectionner une connexion cible
#XFLD
targetContainernEmptyText=Sélectionner un conteneur
#XFLD
sourceSelectObjectText=Sélectionner un objet source
#XFLD
sourceObjectCount=Objets source ({0})
#XFLD
targetObjectText=Objets cible
#XFLD
confluentBrowseContext=Sélectionner un contexte
#XBUT
@retry=Réessayer
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Mise à niveau du locataire en cours

#XTOL
browseSourceConnection=Parcourir la connexion source
#XTOL
browseTargetConnection=Parcourir la connexion cible
#XTOL
browseSourceContainer=Parcourir le conteneur source
#XTOL
browseAndAddSourceDataset=Ajouter des objets source
#XTOL
browseTargetContainer=Parcourir le conteneur cible
#XTOL
browseTargetSetting=Parcourir les paramètres cible
#XTOL
browseSourceSetting=Parcourir les paramètres source
#XTOL
sourceDatasetInfo=Informations
#XTOL
sourceDatasetRemove=Retirer
#XTOL
mappingCount=Représente le nombre total de mappages/d'expressions non basés sur un nom.
#XTOL
filterCount=Représente le nombre total de conditions de filtre.
#XTOL
loading=Chargement...
#XCOL
deltaCapture=Capture delta
#XCOL
deltaCaptureTableName=Table des captures delta
#XCOL
loadType=Charger le type
#XCOL
deleteAllBeforeLoading=Tout supprimer avant le chargement
#XCOL
transformationsTab=Projections
#XCOL
settingsTab=Paramètres

#XBUT
renameTargetObjectBtn=Renommer l'objet cible
#XBUT
mapToExistingTargetObjectBtn=Mapper à un objet cible existant
#XBUT
changeContainerPathBtn=Modifier le chemin d'accès au conteneur
#XBUT
viewSQLDDLUpdated=Afficher l'instruction SQL Créer une table
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=L'objet source ne prend pas en charge la capture delta, mais l'option Capture delta est activée pour l'objet cible sélectionné.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=L''objet cible ne peut pas être utilisé car la capture delta est activée,{0}alors que l''objet source ne prend pas en charge la capture delta.{1}Vous pouvez sélectionner un autre objet cible qui ne prend pas en charge la capture delta.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Un objet cible portant ce nom existe déjà. Cependant, il ne peut pas être utilisé{0}car la capture delta est activée, alors que l''objet source ne prend pas{0}en charge la capture delta.{1}Vous pouvez saisir le nom d''un objet cible existant qui ne prend pas{0}en charge la capture delta ou bien saisir un nom qui n''existe pas encore.
#XBUT
copySQLDDLUpdated=Copier l'instruction SQL Créer une table
#XMSG
targetObjExistingNoCDCColumnUpdated=Les tables existantes dans Google BigQuery doivent inclure les colonnes suivantes pour une capture des données de modification :{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Les objets source suivants ne sont pas pris en charge car ils ne comportent pas de clé primaire, ou bien ils utilisent une connexion qui ne respecte pas les conditions de récupération de la clé primaire :
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Référez-vous à l'article de la base de connaissances SAP 3531135 pour une solution possible.
#XLST: load type list values
initial=Initial uniquement
@emailUpdateError=Erreur lors de la mise à jour de la liste d'envoi des notifications par courriel

#XLST
initialDelta=Initial et delta

#XLST
deltaOnly=Delta uniquement
#XMSG
confluentDeltaLoadTypeInfo=Pour la source Confluent Kafka, seul le type de chargement Initial et delta est pris en charge.
#XMSG
confirmRemoveReplicationObject=Voulez-vous vraiment supprimer la réplication?
#XMSG
confirmRemoveReplicationTaskPrompt=Cette action entraînera la suppression de toutes les réplications existantes. Voulez-vous poursuivre?
#XMSG
confirmTargetConnectionChangePrompt=Cette action entraînera la réinitialisation de la connexion cible, du conteneur cible et la suppression de tous les objets cibles. Voulez-vous poursuivre?
#XMSG
confirmTargetContainerChangePrompt=Cette action entraînera la réinitialisation du conteneur cible et la suppression de tous les objets cibles existants. Voulez-vous poursuivre?
#XMSG
confirmRemoveTransformObject=Voulez-vous vraiment supprimer la projection {0}?
#XMSG
ErrorMsgContainerChange=Une erreur s'est produite lors de la modification du chemin d'accès au conteneur.
#XMSG
infoForUnsupportedDatasetNoKeys=Les objets source suivants ne sont pas pris en charge car ils n'ont pas de clé primaire :
#XMSG
infoForUnsupportedDatasetView=Les objets source suivants de type Vues ne sont pas pris en charge :
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=L'objet source suivant n'est pas pris en charge car il s'agit d'une vue SQL contenant des paramètres de saisie :
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Les objets source suivants ne sont pas pris en charge car l'extraction en a été désactivée :
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Pour les connexions à Confluent, les seuls formats de sérialisation autorisés sont AVRO et JSON. Les objets suivants ne sont pas pris en charge car ils utilisent un format de sérialisation différent :
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Impossible de récupérer le schéma pour les objets suivants. Sélectionnez le contexte approprié ou vérifiez la configuration de Schema Registry.
#XTOL: warning dialog header on deleting replication task
deleteHeader=Supprimer
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Le paramètre Tout supprimer avant le chargement n'est pas pris en charge pour Google BigQuery.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Le paramètre Tout supprimer avant le chargement supprime et recrée l'objet (sujet) avant chaque réplication. Il supprime aussi tous les messages affectés.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Le paramètre Tout supprimer avant le chargement n'est pas pris en charge pour ce type de cible.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Nom technique
#XCOL
connBusinessName=Appellation
#XCOL
connDescriptionName=Description
#XCOL
connType=Type
#XMSG
connTblNoDataFoundtxt=Aucune connexion trouvée
#XMSG
connectionError=Une erreur s'est produite lors de l'accès aux connexions.
#XMSG
connectionCombinationUnsupportedErrorTitle=La combinaison de connexions n'est pas prise en charge.
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Le système ne prend actuellement pas en charge la réplication depuis {0} vers {1}.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Combinaison de types de connexion non prise en charge
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Le système ne prend pas en charge la réplication à partir d''une connexion de type SAP HANA Cloud, lac de données - Fichiers vers {0}. Seule la réplication vers SAP Datasphere est possible.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Sélectionner
#XBUT
containerCancelBtn=Annuler
#XTOL
containerSelectTooltip=Sélectionner
#XTOL
containerCancelTooltip=Annuler
#XMSG
containerContainerPathPlcHold=Chemin d'accès au conteneur
#XFLD
containerContainertxt=Conteneur
#XFLD
confluentContainerContainertxt=Contexte
#XMSG
infoMessageForSLTSelection=Seul /SLT/ID de transfert en masse est autorisé comme conteneur. Sélectionnez un ID de transfert en masse sous SLT (si disponible) et cliquez sur Soumettre.
#XMSG
msgFetchContainerFail=Une erreur s'est produite lors de l'accès aux données du conteneur.
#XMSG
infoMessageForSLTHidden=Cette connexion ne prend pas en charge les dossiers SLT. Il n'apparaissent donc pas dans la liste ci-dessous.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Sélectionnez un conteneur qui contient des sous-dossiers.
#XMSG
sftpIncludeSubFolderText=Faux
#XMSG
sftpIncludeSubFolderTextNew=Non

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Aucun mappage de filtres pour le moment)
#XMSG
failToFetchRemoteMetadata=Une erreur s'est produite lors de l'accès aux métadonnées.
#XMSG
failToFetchData=Une erreur s'est produite lors de l'accès à la cible existante.
#XCOL
@loadType=Charger le type
#XCOL
@deleteAllBeforeLoading=Tout supprimer avant le chargement

#XMSG
@loading=Chargement...
#XFLD
@selectSourceObjects=Sélectionner des objets source
#XMSG
@exceedLimit=Vous ne pouvez pas importer plus de {0} objets à la fois. Désélectionnez au moins {1} objets.
#XFLD
@objects=Objets
#XBUT
@ok=OK
#XBUT
@cancel=Annuler
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Suivant
#XBUT
btnAddSelection=Ajouter la sélection
#XTOL
@remoteFromSelection=Retirer de la sélection
#XMSG
@searchInForSearchField=Rechercher dans {0}

#XCOL
@name=Nom technique
#XCOL
@type=Type
#XCOL
@location=Emplacement
#XCOL
@label=Appellation
#XCOL
@status=Statut

#XFLD
@searchIn=Rechercher dans :
#XBUT
@available=Disponible
#XBUT
@selection=Sélection

#XFLD
@noSourceSubFolder=Tables et vues
#XMSG
@alreadyAdded=Existe déjà dans le diagramme
#XMSG
@askForFilter=Il existe plus de {0} éléments. Veuillez saisir une chaîne de filtre pour réduire le nombre d''éléments.
#XFLD: success label
lblSuccess=Réussite
#XFLD: ready label
lblReady=Prêt
#XFLD: failure label
lblFailed=Échec
#XFLD: fetching status label
lblFetchingDetail=Accéder aux détails

#XMSG Place holder text for tree filter control
filterPlaceHolder=Saisissez du texte pour filtrer les objets de niveau supérieur.
#XMSG Place holder text for server search control
serverSearchPlaceholder=Saisissez du texte et appuyez sur Entrée pour lancer la recherche.
#XMSG
@deployObjects=Import de {0} objets...
#XMSG
@deployObjectsStatus=Nombre d''objets ayant été importés : {0}. Nombre d''objets n''ayant pas pu être importés : {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Échec de l'ouverture du navigateur du référentiel local.
#XMSG
@openRemoteSourceBrowserError=Échec de l'accès aux objets source.
#XMSG
@openRemoteTargetBrowserError=Échec de l'accès aux objets cible.
#XMSG
@validatingTargetsError=Une erreur s'est produite lors de la validation des cibles.
#XMSG
@waitingToImport=Prêt à importer

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Le nombre maximum d'objets a été dépassé. Sélectionnez un maximum de 500 objets pour un flux de réplication.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Nom technique
#XFLD
sourceObjectBusinessName=Appellation
#XFLD
sourceNoColumns=Nombre de colonnes
#XFLD
containerLbl=Conteneur

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Vous devez sélectionner une connexion source pour le flux de réplication.
#XMSG
validationSourceContainerNonExist=Vous devez sélectionner un conteneur pour la connexion source.
#XMSG
validationTargetNonExist=Vous devez sélectionner une connexion cible pour le flux de réplication.
#XMSG
validationTargetContainerNonExist=Vous devez sélectionner un conteneur pour la connexion cible.
#XMSG
validationTruncateDisabledForObjectTitle=Réplication dans des stockages d'objets
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=La réplication dans un stockage Cloud est uniquement possible si l''option Tout supprimer avant le chargement est activée ou si l''objet cible n''existe pas dans la cible.{0} {0}Pour permettre la réplication des objets pour lesquels l''option Tout supprimer avant le chargement n''est pas activée, assurez-vous que l''objet cible n''existe pas dans le système avant d''exécuter le flux de réplication.
#XMSG
validationTaskNonExist=Vous devez avoir au moins une réplication dans le flux de réplication.
#XMSG
validationTaskTargetMissing=Vous devez avoir une cible pour la réplication avec la source : {0}
#XMSG
validationTaskTargetIsSAC=La cible sélectionnée est un artefact SAP Analytics Cloud : {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=La cible sélectionnée n''est pas une table locale prise en charge : {0}.
#XMSG
validationTaskTargetIsNonDwcTableDescription=Un objet avec ce nom existe déjà dans la cible. Cependant, cet objet ne peut pas être utilisé comme objet cible pour un flux de réplication dans le référentiel local car il ne s'agit pas d'une table locale.
#XMSG
validateSourceTargetSystemDifference=Vous devez sélectionner différentes combinaisons de connexions source et cible et de conteneurs pour le flux de réplication.
#XMSG
validateDuplicateSources=Il y a des doublons parmi les noms d''objet source d''une ou plusieurs réplications : {0}.
#XMSG
validateDuplicateTargets=Il y a des doublons parmi les noms d''objet cible d''une ou plusieurs réplications : {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=L''objet source {0} ne prend pas en charge la capture delta, mais c''est le cas de l''objet cible {1}. Vous devez retirer la réplication.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Vous devez sélectionner le type de chargement "Initial et delta" pour la réplication impliquant l''objet cible appelé {0}.
#XMSG
validationAutoRenameTarget=Les colonnes cible ont été renommées.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Une projection automatique a été ajoutée et les colonnes cible suivantes ont été renommées afin d''autoriser la réplication vers la cicble :{1}{1} {0} {1}{1}Ceci est dû à l''un des motifs suivants :{1}{1}{2} Caractères non pris en charge{1}{2} Préfixe réservé
#XMSG
validationAutoRenameTargetDescriptionUpdated=Une projection automatique a été ajoutée et les colonnes cible suivantes ont été renommées afin d''autoriser la réplication vers Google BigQuery :{1}{1} {0} {1}{1}Ceci est dû à l''un des motifs suivants :{1}{1}{2} Nom de colonne réservé{1}{2} Caractères non pris en charge{1}{2} Préfixe réservé
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Une projection automatique a été ajoutée et les colonnes cible suivantes ont été renommées afin d''autoriser la réplication vers Confluent:{1}{1} {0} {1}{1}Ceci est dû à l''un des motifs suivants :{1}{1}{2} Nom de colonne réservé{1}{2} Caractères non pris en charge{1}{2} Préfixe réservé
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Une projection automatique a été ajoutée et les colonnes cible suivantes ont été renommées afin d''autoriser les réplications vers la cible :{1}{1} {0} {1}{1}Ceci est dû à l''un des motifs suivants :{1}{1}{2} Nom de colonne réservé{1}{2} Caractères non pris en charge{1}{2} Préfixe réservé
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=L'objet cible a été renommé.
#XMSG
autoRenameInfoDesc=L''objet cible a été renommé, car il contenait des caractères non pris en charge. Seuls les caractères suivants sont pris en charge :{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(point){0}{1}_(traite de soulignement){0}{1}-(tiret)
#XMSG
validationAutoTargetTypeConversion=Les types de données cible ont été modifiés.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Les types de données cible des colonnes cible suivantes ont été modifiés car les types de données source ne sont pas pris en charge dans Google BigQuery :{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Les types de données cible des colonnes cible suivantes ont été modifiés car les types de données source n''étaient pas pris en charge dans la connexion cible :{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Raccourcissez les noms des colonnes cible.
#XMSG
validationMaxCharLengthGBQTargetDescription=Dans Google BigQuery, les noms des colonnes peuvent comporter jusqu''à 300 caractères. Utilisez une projection pour raccourcir les noms des colonnes cible suivantes :{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Les clés primaires ne seront pas créées.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=Dans Google BigQuery, 16 clés primaires maximum peuvent être prises en charge, mais l'objet source en contient plus. Aucune des clés primaires ne sera créée dans l'objet cible.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Une ou plusieurs colonnes source comportent des types de données impossibles à définir comme clés primaires dans Google BigQuery. Aucune des clés primaires ne sera créée dans l''objet cible.{0}{0}Les types de données cible suivants sont compatibles avec les types de données Google BigQuery pour lesquels une clé primaire peut être définie :{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Définissez une ou plusieurs colonnes comme clé primaire.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Vous devez définir une ou plusieurs colonnes comme clé primaire. Pour ce faire, utilisez la boîte de dialogue du schéma source.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Définissez une ou plusieurs colonnes comme clé primaire.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Vous devez définir une ou plusieurs colonnes comme clé primaire qui correspondent aux contraintes de clé primaire pour votre objet source. Pour ce faire, accédez à Configurer le schéma dans les propriétés de votre objet source.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Saisissez une valeur de partition maximale valide.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=La valeur de partition maximale doit être ≥ 1 et ≤ 2 147 483 647
#XMSG
validateHDLFNoPKDatasetError=Définissez une ou plusieurs colonnes comme clé primaire.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Pour répliquer un objet, vous devez définir une ou plusieurs colonnes cible comme clé primaire. Pour ce faire, utilisez une projection.
#XMSG
validateHDLFNoPKExistingDatasetError=Définissez une ou plusieurs colonnes comme clé primaire.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Pour répliquer des données dans un objet cible existant, une ou plusieurs de ses colonnes doivent être définies comme clé primaire. {0} Vous avez les options suivantes pour définir une ou plusieurs colonnes comme clé primaire : {0} {1} Utilisez l''éditeur de table local pour modifier l''objet cible existant. Rechargez ensuite le flux de réplication.{0}{1} Renommez l''objet cible dans le flux de réplication. Cette action permettra la création d''un objet dès qu''une exécution est lancée. Après l''attribution d''un nouveau nom, vous pouvez définir une ou plusieurs colonnes comme clé primaire dans une projection.{0}{1} Mappez l''objet à un autre objet cible existant dans lequel une ou plusieurs colonnes sont déjà définies comme clé primaire.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=La cible sélectionnée existe déjà dans le référentiel : {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Les noms de tables des captures delta sont déjà utilisés par d''autres tables dans le référentiel : {0}. Vous devez renommer ces objets cible pour vous assurer que les noms de tables des captures delta associées sont uniques avant d''enregistrer le flux de réplication.
#XMSG
validateConfluentEmptySchema=Définir un schéma
#XMSG
validateConfluentEmptySchemaDescUpdated=La table source ne comporte pas de schéma. Sélectionnez Configurer le schéma pour en définir un.
#XMSG
validationCSVEncoding=Codage CSV non valide
#XMSG
validationCSVEncodingDescription=Le codage CSV de la tâche n'est pas valide.
#XMSG
validateConfluentEmptySchema=Sélectionner un type de données cible compatible
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Sélectionner un type de données cible compatible
#XMSG
globalValidateTargetDataTypeDesc=Une erreur s'est produite avec les mappages de colonne. Accédez à Projection et assurez-vous que toutes les colonnes source sont mappées à une colonne unique, avec une colonne comportant un type de données compatible, et que toutes les expressions définies sont valides.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Noms de colonnes en double
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Les noms de colonnes en double ne sont pas pris en charge. Utilisez la boîte de dialogue de projection pour résoudre ce problème. Les objets cible suivants comportent des noms de colonnes en double : {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Noms de colonnes en double
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Les noms de colonnes en double ne sont pas pris en charge. Les objets cible suivants comportent des noms de colonnes en double : {0}.
#XMSG
deltaOnlyLoadTypeTittle=Il peut y avoir des incohérences dans les données.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Le type de chargement Delta uniquement ne prendra pas en compte les modifications apportées à la source entre le dernier enregistrement et la prochaine exécution.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Modifiez le type de chargement en "Initial".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=La réplication des objets basés sur SAP qui n'ont pas de clé primaire est possible uniquement pour le type de chargement "Initial uniquement".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Désactivez la capture delta.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Pour répliquer un objet qui n'a pas de clé primaire à l'aide d'une connexion source de type ABAP, vous devez commencer par désactiver la capture delta pour cette table.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Modifiez l'objet cible.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Impossible d'utiliser l'objet cible, car la capture delta est activée. Vous pouvez renommer l'objet cible, puis désactiver la capture delta pour le nouvel objet (renommé) ou bien mapper l'objet source à un objet cible pour lequel la capture delta est activée.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Modifiez l'objet cible.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Impossible d'utiliser l'objet cible, car il n'a pas la colonne technique __load_package_id requise. Vous pouvez renommer l'objet cible avec un nom qui n'existe pas encore. Le système crée alors un nouvel objet qui a la même définition que l'objet source et qui contient la colonne technique. Vous pouvez également mapper l'objet cible à un objet existant qui a la colonne technique requise (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Modifiez l'objet cible.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Impossible d'utiliser l'objet cible, car il n'a pas la colonne technique  __load_record_id requise. Vous pouvez renommer l'objet cible avec un nom qui n'existe pas encore. Le système crée alors un nouvel objet qui a la même définition que l'objet source et qui contient la colonne technique. Vous pouvez également mapper l'objet cible à un objet existant qui a la colonne technique requise (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Modifiez l'objet cible.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Impossible d'utiliser l'objet cible, car le type de données de sa colonne technique __load_record_id n'est pas "string(44)". Vous pouvez renommer l'objet cible avec un nom qui n'existe pas encore. Le système crée alors un nouvel objet qui a la même définition que l'objet source et, par conséquent, le type de données correct. Vous pouvez également mapper l'objet cible à un objet existant qui a la colonne technique requise (__load_record_id) avec le type de données correct.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Modifiez l'objet cible.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Impossible d'utiliser l'objet cible, car il a une clé primaire tandis que l'objet source n'en a aucune. Vous pouvez renommer l'objet cible avec un nom qui n'existe pas encore. Le système crée alors un nouvel objet qui a la même définition que l'objet source et, par conséquent, aucune clé primaire. Vous pouvez également mapper l'objet cible à un objet existant qui a la colonne technique requise (__load_package_id) et qui n'a pas de clé primaire.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Modifiez l'objet cible.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Impossible d'utiliser l'objet cible, car il a une clé primaire tandis que l'objet source n'en a aucune. Vous pouvez renommer l'objet cible avec un nom qui n'existe pas encore. Le système crée alors un nouvel objet qui a la même définition que l'objet source et, par conséquent, aucune clé primaire. Vous pouvez également mapper l'objet cible à un objet existant qui a la colonne technique requise (__load_record_id) et qui n'a pas de clé primaire.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Modifiez l'objet cible.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Impossible d'utiliser l'objet cible, car le type de données de sa colonne technique __load_package_id n'est pas "binary(>=256)". Vous pouvez renommer l'objet cible avec un nom qui n'existe pas encore. Le système crée alors un nouvel objet qui a la même définition que l'objet source et, par conséquent, le type de données correct. Vous pouvez également mapper l'objet cible à un objet existant qui a la colonne technique requise (__load_package_id) avec le type de données correct.
#XMSG
validationAutoRenameTargetDPID=Les colonnes cible ont été renommées.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Retirez l'objet source.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=L'objet source n'a pas de colonne clé, ce qui n'est pas pris en charge dans ce contexte.
#XMSG
validationAutoRenameTargetDPIDDescription=Une projection automatique a été ajoutée et les colonnes cible suivantes ont été renommées afin d''autoriser la réplication depuis la source ABAP sans clé :{1}{1} {0} {1}{1}Ceci est dû à l''un des motifs suivants :{1}{1}{2} Nom de colonne réservé{1}{2} Caractères non pris en charge{1}{2} Préfixe réservé
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Réplication vers {0}
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=L''enregistrement et le déploiement de flux de réplication ayant comme cible {0} est actuellement impossible car nous effectuons une maintenance sur cette fonction.
#XMSG
TargetColumnSkippedLTF=La colonne cible a été ignorée.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=La colonne cible a été ignorée car le type de données n''est pas pris en charge. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Colonne Temps avec clé primaire
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=L'objet source a une colonne Temps comme clé primaire, qui n'est pas prise en charge dans ce contexte.
#XMSG
validateNoPKInLTFTarget=Clé primaire manquante
#XMSG
validateNoPKInLTFTargetDescription=La clé primaire n'est pas définie dans la cible, ce qui n'est pas pris en charge dans ce contexte.
#XMSG
validateABAPClusterTableLTF=Table cluster ABAP
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=L'objet source est une table cluster ABAP, ce qui n'est pas pris en charge dans ce contexte.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Il semblerait que vous n'ayez pas encore ajouté de données.
#YINS
welcomeText2=Pour commencer votre flux de réplication, sélectionnez une connexion et un objet source à gauche.

#XBUT
wizStep1=Sélectionner une connexion source
#XBUT
wizStep2=Sélectionner un conteneur source
#XBUT
wizStep3=Ajouter des objets source

#XMSG
limitDataset=Le nombre maximum d'objets a été atteint. Retirez des objets existants pour en ajouter de nouveaux. Sinon, créez un autre flux de réplication.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Impossible de lancer le flux de réplication pour cette connexion cible non SAP car aucun volume de sortie n'est disponible pour ce mois.
#XMSG
premiumOutBoundRFAdminWarningMsg=Un administrateur peut augmenter le nombre de blocs de sortie premium pour ce locataire, ce qui mettrait du volume de sortie à disposition pour ce mois.
#XMSG
messageForToastForDPIDColumn2=Nouvelle colonne ajoutée à la cible pour {0} objets - Requis pour le traitement des enregistrements en double en lien avec les objets source basés sur ABAP qui n''ont pas de clé primaire.
#XMSG
PremiumInboundWarningMessage=Selon le nombre de flux de réplication et le volume de données à répliquer, les ressources SAP HANA{0}requises pour la réplication des données via {1} peuvent dépasser la capacité disponible pour votre locataire.
#XMSG
PremiumInboundWarningMsg=Selon le nombre de flux de réplication et le volume de données à répliquer,{0}les ressources SAP HANA requises pour la réplication des données via "{1}" peuvent dépasser la capacité disponible pour votre locataire.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Saisissez un nom de projection valide.
#XMSG
emptyTargetColumn=Saisissez un nom de colonne cible.
#XMSG
emptyTargetColumnBusinessName=Saisissez un nom de colonne cible.
#XMSG
invalidTransformName=Saisissez un nom de projection.
#XMSG
uniqueColumnName=Renommez la colonne cible.
#XMSG
copySourceColumnLbl=Copier les colonnes de l'objet source
#XMSG
renameWarning=Veillez à choisir un nom unique au moment de renommer la table cible. Si la table avec le nouveau nom existe déjà dans l'espace, il utilisera la définition de cette table.

#XMSG
uniqueColumnBusinessName=Saisissez une nouvelle appellation pour la colonne cible.
#XMSG
uniqueSourceMapping=Sélectionnez une autre colonne source.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=La colonne source {0} est déjà utilisée par les colonnes cible suivantes : {1}{1}{2}{1}{1} Pour cette colonne cible ou pour les autres colonnes cible, sélectionnez une colonne source qui n''est pas déjà utilisée pour enregistrer la projection.
#XMSG
uniqueColumnNameDescription=Le nom de colonne cible saisi existe déjà. Pour pouvoir enregistrer la projection, vous devez saisir un nom de colonne unique.
#XMSG
uniqueColumnBusinessNameDesc=L'appellation de la colonne cible existe déjà. Pour enregistrer la projection, vous devez saisir une appellation de colonne unique.
#XMSG
emptySource=Sélectionnez une colonne source ou saisissez une constante.
#XMSG
emptySourceDescription=Pour créer une entrée de mappage valide, vous devez sélectionner une colonne source ou saisir une valeur de constante.
#XMSG
emptyExpression=Définissez le mappage.
#XMSG
emptyExpressionDescription1=Sélectionnez la colonne source à laquelle vous voulez mapper la colonne cible ou cochez la case dans la colonne {0} Fonctions/Constantes {1}. {2} {2} Les fonctions sont saisies automatiquement en fonction du type de données cible. Les valeurs constantes peuvent être saisies manuellement.
#XMSG
numberExpressionErr=Saisissez un nombre.
#XMSG
numberExpressionErrDescription=Vous avez sélectionné un type de données numérique. Par conséquent, vous pouvez uniquement saisir des chiffres, ainsi que le signe décimal si nécessaire. N'utilisez pas de guillemets simples.
#XMSG
invalidLength=Saisissez une valeur de longueur valide.
#XMSG
invalidLengthDescription=La longueur du type de données doit être supérieure ou égale à la longueur de la colonne source et peut être comprise entre 1 et 5000.
#XMSG
invalidMappedLength=Saisissez une valeur de longueur valide.
#XMSG
invalidMappedLengthDescription=La longueur du type de données doit être supérieure ou égale à la longueur de la colonne source {0} et peut être comprise entre 1 et 5000.
#XMSG
invalidPrecision=Saisissez une valeur de précision valide.
#XMSG
invalidPrecisionDescription=La précision définit le nombre total de chiffres. L''échelle définit le nombre de chiffres après la virgule des décimales et est comprise entre 0 et la valeur de la précision.{0}{0} Exemples : {0}{1} Précision 6, échelle 2 correspond aux nombres comme 1234,56.{0}{1} Précision 6, échelle 6 correspond aux nombres comme 0,123546.{0} {0} La précision et l''échelle de la cible doivent être compatibles avec la précision et l''échelle de la source afin que tous les chiffres de la source apparaissent dans la zone cible. Par exemple, si une précision 6 et une échelle 2 sont indiquées dans la source (et par conséquent des chiffres différents de 0 avant la virgule), vous ne pouvez pas avoir de précision 6 et d''échelle 6 dans la cible.
#XMSG
invalidPrimaryKey=Saisissez au moins une clé primaire.
#XMSG
invalidPrimaryKeyDescription=La clé primaire n'est pas définie pour ce schéma.
#XMSG
invalidMappedPrecision=Saisissez une valeur de précision valide.
#XMSG
invalidMappedPrecisionDescription1=La précision définit le nombre total de chiffres. L''échelle définit le nombre de chiffres après la virgule des décimales et est comprise entre 0 et la valeur de la précision. {0}{0}Exemples :{0}{1} Précision 6, échelle 2 correspond aux nombres comme 1234,56.{0}{1} Précision 6, échelle 6 correspond aux nombres comme 0,123546.{0}{0}La précision du type de données doit être supérieure ou égale à la précision de la source ({2}).
#XMSG
invalidScale=Saisissez une valeur d'échelle valide.
#XMSG
invalidScaleDescription=La précision définit le nombre total de chiffres. L''échelle définit le nombre de chiffres après la virgule des décimales et est comprise entre 0 et la valeur de la précision.{0}{0} Exemples : {0}{1} Précision 6, échelle 2 correspond aux nombres comme 1234,56.{0}{1} Précision 6, échelle 6 correspond aux nombres comme 0,123546.{0} {0} La précision et l''échelle de la cible doivent être compatibles avec la précision et l''échelle de la source afin que tous les chiffres de la source apparaissent dans la zone cible. Par exemple, si une précision 6 et une échelle 2 sont indiquées dans la source (et par conséquent des chiffres différents de 0 avant la virgule), vous ne pouvez pas avoir de précision 6 et d''échelle 6 dans la cible.
#XMSG
invalidMappedScale=Saisissez une valeur d'échelle valide.
#XMSG
invalidMappedScaleDescription1=La précision définit le nombre total de chiffres. L''échelle définit le nombre de chiffres après la virgule des décimales et est comprise entre 0 et la valeur de la précision.{0}{0} Exemples : {0}{1} Précision 6, échelle 2 correspond aux nombres comme 1234,56.{0}{1} Précision 6, échelle 6 correspond aux nombres comme 0,123546.{0}{0}L''échelle du type de données doit être supérieure ou égale à l''échelle de la source ({2}).
#XMSG
nonCompatibleDataType=Sélectionnez un type de données cible compatible.
#XMSG
nonCompatibleDataTypeDescription1=Le type de données indiqué ici doit être compatible avec le type de données source ({0}). {1}{1} Exemple : si votre colonne source comporte une chaîne de type de données et des lettres, vous ne pouvez pas utiliser de type de données DECIMAL dans votre cible.
#XMSG
invalidColumnCount=Sélectionnez une colonne source.
#XMSG
ObjectStoreInvalidScaleORPrecision=Saisissez une valeur valide pour la précision et l'échelle.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=La première valeur est la précision, qui définit le nombre total de chiffres. La deuxième valeur est l'échelle, qui définit le nombre de chiffres après la virgule des décimales. Saisissez une valeur d'échelle cible supérieure à la valeur d'échelle source et assurez-vous que la différence entre la valeur d'échelle cible saisie et la valeur de précision est supérieure à la différence entre la valeur d'échelle source et la valeur de précision.
#XMSG
InvalidPrecisionORScale=Saisissez une valeur valide pour la précision et l'échelle.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=La première valeur est la précision, qui définit le nombre total de chiffres. La deuxième valeur est l''échelle, qui définit le nombre de chiffres après la virgule des décimales.{0}{0}Étant donné que le type de données source n''est pas pris en charge dans Google BigQuery, il est converti en type de données cible DECIMAL. Dans ce cas, la précision peut uniquement être définie entre 38 et 76 et l''échelle entre 9 et 38. De plus, le résultat de la précision moins l''échelle, qui représente les chiffres avant la virgule des décimales, doit être compris entre 29 et 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=La première valeur est la précision, qui définit le nombre total de chiffres. La deuxième valeur est l''échelle, qui définit le nombre de chiffres après la virgule des décimales.{0}{0}Étant donné que le type de données source n''est pas pris en charge dans Google BigQuery, il est converti en type de données cible DECIMAL. Dans ce cas, la précision doit être supérieure ou égale à 20. De plus, le résultat de la précision moins l''échelle, qui reflète les chiffres avant la virgule des décimales, doit être supérieur ou égal à 20.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=La première valeur est la précision, qui définit le nombre total de chiffres. La deuxième valeur est l''échelle, qui définit le nombre de chiffres après la virgule des décimales.{0}{0}Étant donné que le type de données source n''est pas pris en charge dans la cible, il est converti en type de données cible DECIMAL. Dans ce cas, la précision définie doit être supérieure ou égale à 1 et inférieure à 38 et l''échelle doit être inférieure ou égale à la précision.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=La première valeur est la précision, qui définit le nombre total de chiffres. La deuxième valeur est l''échelle, qui définit le nombre de chiffres après la virgule des décimales.{0}{0}Étant donné que le type de données source n''est pas pris en charge dans la cible, il est converti en type de données cible DECIMAL. Dans ce cas, la précision doit être supérieure ou égale à 20. De plus, le résultat de la précision moins l''échelle, qui reflète les chiffres avant la virgule des décimales, doit être supérieur ou égal à 20.
#XMSG
invalidColumnCountDescription=Pour créer une entrée de mappage valide, vous devez sélectionner une colonne source ou saisir une valeur de constante.
#XMSG
duplicateColumns=Renommez la colonne cible.
#XMSG
duplicateGBQCDCColumnsDesc=Le nom de la colonne cible est réservé dans Google BigQuery. Vous devez la renommer pour pouvoir enregistrer la projection.
#XMSG
duplicateConfluentCDCColumnsDesc=Le nom de la colonne cible est réservé dans Confluent. Vous devez la renommer pour pouvoir enregistrer la projection.
#XMSG
duplicateSignavioCDCColumnsDesc=Le nom de la colonne cible est réservé dans SAP Signavio. Vous devez la renommer pour pouvoir enregistrer la projection.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Le nom de la colonne cible est réservé dans MS OneLake. Vous devez la renommer pour pouvoir enregistrer la projection.
#XMSG
duplicateSFTPCDCColumnsDesc=Le nom de la colonne cible est réservé dans SFTP. Vous devez la renommer pour pouvoir enregistrer la projection.
#XMSG
GBQTargetNameWithPrefixUpdated1=Le nom de la colonne cible contient un préfixe qui est réservé dans Google BigQuery. Vous devez la renommer pour pouvoir enregistrer la projection.{0}{0}Le nom de la colonne cible ne peut pas commencer par l''une des chaînes suivantes :{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Raccourcissez le nom de la colonne cible.
#XMSG
GBQtargetMaxLengthDesc=Dans Google BigQuery, un nom de colonne peut utiliser jusqu'à 300 caractères. Raccourcissez le nom de la colonne cible pour pouvoir enregistrer la projection.
#XMSG
invalidMappedScalePrecision=La précision et l'échelle de la cible doivent être compatibles avec la précision et l'échelle de la source afin que tous les chiffres apparaissent dans la zone cible.
#XMSG
invalidMappedScalePrecisionShortText=Saisissez une valeur de précision et d'échelle valide.
#XMSG
validationIncompatiblePKTypeDescProjection3=Une ou plusieurs colonnes source comportent des types de données impossibles à définir comme clés primaires dans Google BigQuery. Aucune des clés primaires ne sera créée dans l''objet cible.{0}{0}Les types de données cible suivants sont compatibles avec les types de données Google BigQuery pour lesquels une clé primaire peut être définie :{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Décochez column __message_id.
#XMSG
uncheckColumnMessageIdDesc=Colonne : clé primaire
#XMSG
validationOpCodeInsert=Vou devez saisir une valeur pour l'insertion.
#XMSG
recommendDifferentPrimaryKey=Il est recommandé de sélectionner une clé primaire différente au niveau de l'élément.
#XMSG
recommendDifferentPrimaryKeyDesc=Lorsque le code d'opération est déjà défini, il est recommandé de sélectionner différentes clés primaires pour l'indice de tableau et les éléments, pour éviter tout problème comme la duplication de colonnes.
#XMSG
selectPrimaryKeyItemLevel=Vous devez sélectionner au moins une clé primaire au niveau de l'en-tête et au niveau de l'élément.
#XMSG
selectPrimaryKeyItemLevelDesc=Lorsqu'un tableau ou une carte est développé(e), vous devez sélectionner deux clés primaires, une au niveau de l'en-tête et une au niveau de l'élément.
#XMSG
invalidMapKey=Vous devez sélectionner au moins une clé primaire au niveau de l'en-tête.
#XMSG
invalidMapKeyDesc=Lorsqu'un tableau ou une carte est développé(e), vous devez sélectionner une clé primaire au niveau de l'en-tête.
#XFLD
txtSearchFields=Rechercher les colonnes cible
#XFLD
txtName=Nom
#XMSG
txtSourceColValidation=Une ou plusieurs colonnes source ne sont pas prises en charge :
#XMSG
txtMappingCount=Mappages ({0})
#XMSG
schema=Schéma
#XMSG
sourceColumn=Colonnes source
#XMSG
warningSourceSchema=Toute modification apportée au schéma affectera les mappages dans la boîte de dialogue de projection.
#XCOL
txtTargetColName=Colonne cible (nom technique)
#XCOL
txtDataType=Type de données cible
#XCOL
txtSourceDataType=Type de données source
#XCOL
srcColName=Colonne source (nom technique)
#XCOL
precision=Précision
#XCOL
scale=Échelle
#XCOL
functionsOrConstants=Fonctions/Constantes
#XCOL
txtTargetColBusinessName=Colonne cible (Appellation)
#XCOL
prKey=Clé primaire
#XCOL
txtProperties=Propriétés
#XBUT
txtOK=Enregistrer
#XBUT
txtCancel=Annuler
#XBUT
txtRemove=Retirer
#XFLD
txtDesc=Description
#XMSG
rftdMapping=Mappage
#XFLD
@lblColumnDataType=Type de données
#XFLD
@lblColumnTechnicalName=Nom technique
#XBUT
txtAutomap=Mappage automatique
#XBUT
txtUp=Vers le haut
#XBUT
txtDown=Vers le bas

#XTOL
txtTransformationHeader=Projection
#XTOL
editTransformation=Modifier
#XTOL
primaryKeyToolip=Clé


#XMSG
rftdFilter=Filtrer
#XMSG
rftdFilterColumnCount=Source : {0}({1})
#XTOL
rftdFilterColSearch=Rechercher
#XMSG
rftdFilterColNoData=Aucune colonne à afficher
#XMSG
rftdFilteredColNoExps=Aucune expression de filtre
#XMSG
rftdFilterSelectedColTxt=Ajouter un filtre pour
#XMSG
rftdFilterTxt=Filtre disponible pour
#XBUT
rftdFilterSelectedAddColExp=Ajouter une expression
#YINS
rftdFilterNoSelectedCol=Sélectionnez une colonne pour ajouter un filtre.
#XMSG
rftdFilterExp=Expression de filtre
#XMSG
rftdFilterNotAllowedColumn=L'ajout de filtres n'est pas pris en charge pour cette colonne.
#XMSG
rftdFilterNotAllowedHead=Aucune colonne prise en charge
#XMSG
rftdFilterNoExp=Aucun filtre n'a été défini.
#XTOL
rftdfilteredTt=Filtré
#XTOL
rftdremoveexpTt=Retirer l'expression de filtre
#XTOL
validationMessageTt=Messages de validation
#XTOL
rftdFilterDateInp=Sélectionner une date
#XTOL
rftdFilterDateTimeInp=Sélectionner une date/heure
#XTOL
rftdFilterTimeInp=Sélectionner une heure
#XTOL
rftdFilterInp=Saisir une valeur
#XMSG
rftdFilterValidateEmptyMsg={0} expressions de filtre de la colonne {1} sont vides.
#XMSG
rftdFilterValidateInvalidNumericMsg={0} expressions de filtre de la colonne {1} contiennent des valeurs numériques non valides.
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Une expression de filtre doit contenir des valeurs numériques valides.
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Si le schéma de l'objet cible a été modifié, utilisez la fonction "Mapper à un objet cible existant" sur la page principale pour adapter les modifications, puis mappez à nouveau l'objet cible avec sa source.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Si la table cible existe déjà et que le mappage inclut une modification de schéma, vous devez modifier la table cible en conséquence avant le déploiement du flux de réplication.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Si votre mappage implique une modification de schéma, vous devez modifier la table cible en conséquence avant le déploiement du flux de réplication.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Les colonnes non prises en charge suivantes ont été ignorées lors de la définition de la source : {0}.
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Les colonnes non prises en charge suivantes ont été ignorées lors de la définition de la cible : {0}.
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Les objets suivants ne sont pas pris en charge car ils sont exposés pour consommation : {0} {1} {0} {0} Pour utiliser les tables dans un flux de réplication, l''utilisation sémantique (dans les paramètres de la table) ne doit pas être définie sur {2}Jeu de données analytique{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=L''objet cible ne peut pas être utilisé car il est exposé pour consommation : {0} {0} Pour utiliser la table dans un flux de réplication, l''utilisation sémantique (dans les paramètres de la table) ne doit pas être définie sur {1}Jeu de données analytique{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Un objet cible portant ce nom existe déjà. Cependant, il ne peut pas être utilisé car il est exposé pour consommation : {0} {0} Pour utiliser la table dans un flux de réplication, l''utilisation sémantique (dans les paramètres de la table) ne doit pas être définie sur {1}Jeu de données analytique{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Un objet avec ce nom existe déjà dans la cible. {0}Cependant, cet objet ne peut pas être utilisé comme objet cible pour un flux de réplication dans le référentiel local car il ne s''agit pas d''une table locale.
#XMSG:
targetAutoRenameUpdated=La colonne a été renommée.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=La colonne cible a été renommée pour permettre les réplications dans Google BigQuery. Ceci est dû à l''un des motifs suivants :{0} {1}{2}Nom de colonne réservé{3}{2}Caractères non pris en charge{3}{2}Préfixe réservé{3}{4}.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=La colonne cible a été renommée pour permettre les réplications dans Confluent. Ceci est dû à l''un des motifs suivants :{0} {1}{2}Nom de colonne réservé{3}{2}Caractères non pris en charge{3}{2}Préfixe réservé{3}{4}.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=La colonne cible a été renommée pour permettre les réplications dans la cible. Ceci est dû à l''un des motifs suivants :{0} {1}{2}Caractères non pris en charge{3}{2}Préfixe réservé{3}{4}.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=La colonne cible a été renommée pour permettre les réplications dans la cible. Ceci est dû à l''un des motifs suivants :{0} {1}{2}Nom de colonne réservé{3}{2}Caractères non pris en charge{3}{2}Préfixe réservé{3}{4}
#XMSG:
targetAutoDataType=Le type de données cible a été modifié.
#XMSG:
targetAutoDataTypeDesc=Le type de données cible a été modifié en {0}, car le type de données source n''est pas pris en charge dans Google BigQuery.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Le type de données cible a été modifié en {0}, car le type de données source n''est pas pris en charge dans la connexion cible.
#XMSG
projectionGBQUnableToCreateKey=Les clés primaires ne seront pas créées.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=Dans Google BigQuery, 16 clés primaires maximum peuvent être prises en charge, mais l'objet source en contient plus. Aucune des clés primaires ne sera créée dans l'objet cible.
#XMSG
HDLFNoKeyError=Définissez une ou plusieurs colonnes comme clé primaire.
#XMSG
HDLFNoKeyErrorDescription=Pour répliquer un objet, vous devez définir une ou plusieurs colonnes comme clé primaire.
#XMSG
HDLFNoKeyErrorExistingTarget=Définissez une ou plusieurs colonnes comme clé primaire.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Pour répliquer des données dans un objet cible existant, une ou plusieurs de ses colonnes doivent être définies comme clé primaire. {0} {0} Vous avez les options suivantes pour définir une ou plusieurs colonnes comme clé primaire : {0}{0}{1} Utilisez l''éditeur de table local pour modifier l''objet cible existant. Rechargez ensuite le flux de réplication.{0}{0}{1} Renommez l''objet cible dans le flux de réplication. Cette action permettra la création d''un objet dès qu''une exécution est lancée. Après l''attribution d''un nouveau nom, vous pouvez définir une ou plusieurs colonnes comme clé primaire dans une projection.{0}{0}{1} Mappez l''objet à un autre objet cible existant dans lequel une ou plusieurs colonnes sont déjà définies comme clé primaire.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Clé primaire modifiée
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=Comparé à l''objet source, vous avez défini différentes colonnes comme clé primaire pour l''objet cible. Assurez-vous que ces colonnes identifient de façon unique toutes les lignes pour éviter une éventuelle corruption des données lors d''une future réplication des données. {0} {0} Dans l''objet source, les colonnes suivantes sont définies comme clé primaire : {0} {1}
#XMSG
duplicateDPIDColumns=Renommez la colonne cible.
#XMSG
duplicateDPIDDColumnsDesc1=Ce nom de colonne cible est réservé pour une colonne technique. Saisissez un autre nom pour enregistrer la projection.
#XMSG:
targetAutoRenameDPID=La colonne a été renommée.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=La colonne cible a été renommée pour permettre les réplications depuis une source ABAP sans clé. Ceci est dû à l''un des motifs suivants :{0} {1}{2}Nom de colonne réservé{3}{2}Caractères non pris en charge{3}{2}Préfixe réservé{3}{4}.
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle=Paramètres cible de {0}
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle=Paramètres source de {0}
#XBUT
connectionSettingSave=Enregistrer
#XBUT
connectionSettingCancel=Annuler
#XBUT: Button to keep the object level settings
txtKeep=Conserver
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Écraser
#XFLD
targetConnectionThreadlimit=Limite de threads cible pour un chargement initial (1-100) 
#XFLD
connectionThreadLimit=Limite de threads source pour un chargement initial (1-100) 
#XFLD
maxConnection=Limite de threads de réplication (1-100)
#XFLD
kafkaNumberOfPartitions=Nombre de partitions
#XFLD
kafkaReplicationFactor=Facteur de réplication
#XFLD
kafkaMessageEncoder=Encodeur du message
#XFLD
kafkaMessageCompression=Compression du message
#XFLD
fileGroupDeltaFilesBy=Groupe Delta par
#XFLD
fileFormat=Type de fichier
#XFLD
csvEncoding=Codage CSV
#XFLD
abapExitLbl=Exit ABAP
#XFLD
deltaPartition=Nombre de threads d'objet pour les chargements delta (1-10)
#XFLD
clamping_Data=Échec dû à la troncation des données
#XFLD
fail_On_Incompatible=Échec dû à l'incompatibilité des données
#XFLD
maxPartitionInput=Nombre maximal de partitions
#XFLD
max_Partition=Définir un nombre maximal de partitions
#XFLD
include_SubFolder=Inclure les sous-dossiers
#XFLD
fileGlobalPattern=Modèle global pour le nom de fichier
#XFLD
fileCompression=Compression du fichier
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Séparateur de fichier
#XFLD
fileIsHeaderIncluded=En-tête du fichier
#XFLD
fileOrient=Orientation
#XFLD
gbqWriteMode=Mode d'écriture
#XFLD
suppressDuplicate=Supprimer les doublons
#XFLD
apacheSpark=Activer la compatibilité Apache Spark
#XFLD
clampingDatatypeCb=Fixer les types de données décimaux en virgule flottante
#XFLD
overwriteDatasetSetting=Écraser les paramètres cible au niveau de l'objet
#XFLD
overwriteSourceDatasetSetting=Écraser les paramètres source au niveau de l'objet
#XMSG
kafkaInvalidConnectionSetting=Saisissez un nombre compris entre {0} et {1}.
#XMSG
MinReplicationThreadErrorMsg=Saisissez un nombre supérieur à {0}.
#XMSG
MaxReplicationThreadErrorMsg=Saisissez un nombre inférieur à {0}.
#XMSG
DeltaThreadErrorMsg=Saisissez une valeur comprise entre 1 et 10.
#XMSG
MaxPartitionErrorMsg=Saisissez une valeur comprise entre 1 <= x <= 2147483647. La valeur par défaut est 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Saisissez un nombre entier compris entre {0} et {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Utiliser le facteur de réplication du courtier
#XFLD
serializationFormat=Format de sérialisation
#XFLD
compressionType=Type de compression
#XFLD
schemaRegistry=Utiliser Schema Registry
#XFLD
subjectNameStrat=Stratégie du nom de sujet
#XFLD
compatibilityType=Type de compatibilité
#XFLD
confluentTopicName=Nom du sujet
#XFLD
confluentRecordName=Nom de l'enregistrement
#XFLD
confluentSubjectNamePreview=Aperçu du nom de sujet
#XMSG
serializationChangeToastMsgUpdated2=Le format de sérialisation a été modifié en JSON, car Schema Registry n'est pas activé. Pour reconfigurer AVRO comme format de sérialisation, vous devez d'abord activer Schema Registry.
#XBUT
confluentTopicNameInfo=Le nom du sujet est toujours basé sur le nom de l'objet cible. Vous pouvez le modifier en renommant l'objet cible.
#XMSG
emptyRecordNameValidationHeaderMsg=Saisissez un nom d'enregistrement.
#XMSG
emptyPartionHeader=Saisissez le nombre de partitions.
#XMSG
invalidPartitionsHeader=Saisissez un nombre de partitions valide.
#XMSG
invalidpartitionsDesc=Saisissez un nombre entre 1 et 200 000.
#XMSG
emptyrFactorHeader=Saisissez un facteur de réplication.
#XMSG
invalidrFactorHeader=Saisissez un facteur de réplication valide.
#XMSG
invalidrFactorDesc=Saisissez un nombre entre 1 et 32 767.
#XMSG
emptyRecordNameValidationDescMsg=Si le format de sérialisation "AVRO" est utilisé, seuls les caractères suivants sont pris en charge :{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(trait de soulignement)
#XMSG
validRecordNameValidationHeaderMsg=Saisissez un nom d'enregistrement valide.
#XMSG
validRecordNameValidationDescMsgUpdated=Le format de sérialisation "AVRO" étant utilisé, le nom de l'enregistrement doit uniquement se composer de caractères alphanumériques (A-Z, a-z, 0-9) et de traits de soulignement (_). Il doit commencer par une lettre ou un trait de soulignement.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=Pour que vous puissiez définir le “Nombre de threads d'objet pour les chargements delta”, au moins un objet doit avoir le type de chargement “Initial et delta”.
#XMSG
invalidTargetName=Nom de colonne non valide
#XMSG
invalidTargetNameDesc=Le nom de la colonne cible doit uniquement se composer de caractères alphanumériques (A-Z, a-z, 0-9) et de traits de soulignement (_).
#XFLD
consumeOtherSchema=Utiliser d'autres versions du schéma
#XFLD
ignoreSchemamissmatch=Ignorer la non-concordance du schéma
#XFLD
confleuntDatatruncation=Échec dû à la troncation des données
#XFLD
isolationLevel=Niveau d'isolation
#XFLD
confluentOffset=Point de départ
#XFLD
signavioGroupDeltaFilesByText=Néant
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=Non
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=Non

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Projections
#XBUT
txtAdd=Ajouter
#XBUT
txtEdit=Modifier
#XMSG
transformationText=Ajoutez une projection pour configurer un filtre ou un mappage.
#XMSG
primaryKeyRequiredText=Sélectionnez une clé primaire avec configuration du schéma.
#XFLD
lblSettings=Paramètres
#XFLD
lblTargetSetting={0} : paramètres cible
#XMSG
@csvRF=Sélectionnez le fichier qui contient la définition de schéma que vous voulez appliquer à tous les fichiers dans le dossier.
#XFLD
lblSourceColumns=Colonnes source
#XFLD
lblJsonStructure=Structure JSON
#XFLD
lblSourceSetting={0} : paramètres source
#XFLD
lblSourceSchemaSetting={0} : paramètres du schéma source
#XBUT
messageSettings=Paramètres de message
#XFLD
lblPropertyTitle1=Propriétés de l'objet
#XFLD
lblRFPropertyTitle=Propriétés du flux de réplication
#XMSG
noDataTxt=Il n'y a aucune colonne à afficher.
#XMSG
noTargetObjectText=Aucun objet cible sélectionné
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Colonnes cible
#XMSG
searchColumns=Rechercher des colonnes
#XTOL
cdcColumnTooltip=Colonne pour capture delta
#XMSG
sourceNonDeltaSupportErrorUpdated=L'objet source ne prend pas en charge la capture delta.
#XMSG
targetCDCColumnAdded=2 colonnes cible ont été ajoutées pour la capture delta.
#XMSG
deltaPartitionEnable=Une limite de threads d'objet a été ajoutée pour les chargements delta dans les paramètres source.
#XMSG
attributeMappingRemovalTxt=Suppression des mappages non valides qui ne sont pas pris en charge pour le nouvel objet cible.
#XMSG
targetCDCColumnRemoved=2 colonnes cible utilisées pour la capture delta ont été retirées.
#XMSG
replicationLoadTypeChanged=Type de chargement modifié en "Initial et delta"
#XMSG
sourceHDLFLoadTypeError=Modifiez le type de chargement en "Initial et delta".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Pour répliquer un objet à partir d'une connexion source de type SAP HANA Cloud, lac de données - Fichiers vers SAP Datasphere, vous devez utiliser le type de chargement "Initial et delta".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Activez la capture delta.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Pour répliquer un objet à partir d'une connexion source de type SAP HANA Cloud, lac de données - Fichiers vers SAP Datasphere, vous devez activer la capture delta.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Modifiez l'objet cible.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Impossible d'utiliser l'objet cible, car la capture delta est désactivée. Vous pouvez renommer l'objet cible (permettant ainsi la création d'un nouvel objet avec la capture delta) ou le mapper à un objet existant avec la capture delta activée.
#XMSG
deltaPartitionError=Saisissez un nombre de threads d'objet valide pour les chargements delta.
#XMSG
deltaPartitionErrorDescription=Saisissez une valeur comprise entre 1 et 10.
#XMSG
deltaPartitionEmptyError=Saisissez un nombre de threads d'objet pour les chargements delta.
#XFLD
@lblColumnDescription=Description
#XMSG
@lblColumnDescriptionText1=À des fins techniques - Traitement des enregistrements en double causés par des problèmes lors de la réplication d'objets source basés sur ABAP qui n'ont pas de clé primaire.
#XFLD
storageType=Stockage
#XFLD
skipUnmappedColLbl=Ignorer les colonnes non mappées
#XFLD
abapContentTypeLbl=Type de contenu
#XFLD
autoMergeForTargetLbl=Fusionner les données automatiquement
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Données générales
#XFLD
lblBusinessName=Appellation
#XFLD
lblTechnicalName=Nom technique
#XFLD
lblPackage=Package
#XFLD
statusPanel=Statut d'exécution
#XBTN: Schedule dropdown menu
SCHEDULE=Planification
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Modifier la planification
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Supprimer la planification
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Créer une planification
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Échec du contrôle de validation de la planification
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Impossible de créer une planification car le flux de réplication est en cours de déploiement.{0}Attendez que le déploiement du flux de réplication soit terminé.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Vous ne pouvez pas créer de planification pour les flux de réplication qui contiennent des objets dont le type de chargement est "Initial et delta".
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Vous ne pouvez pas créer de planification pour les flux de réplication qui contiennent des objets dont le type de chargement est "Initial et delta/Delta uniquement".
#XFLD : Scheduled popover
SCHEDULED=Planifié
#XFLD
CREATE_REPLICATION_TEXT=Créer un flux de réplication
#XFLD
EDIT_REPLICATION_TEXT=Modifier un flux de réplication
#XFLD
DELETE_REPLICATION_TEXT=Supprimer un flux de réplication
#XFLD
REFRESH_FREQUENCY=Fréquence
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Impossible de déployer le flux de réplication, car la planification existante{0} ne prend pas encore en charge le type de chargement "Initial et delta".{0}{0}Pour déployer le flux de réplication, vous devez définir le type de chargement de chaque objet{0} sur "Initial uniquement". Vous pouvez également supprimer la planification, déployer le {0}flux de réplication, puis lancer une nouvelle exécution. Cela entraînera une exécution sans {0}fin qui prendra également en charge les objets avec le type de chargement "Initial et delta".
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Impossible de déployer le flux de réplication, car la planification {0} existante ne prend pas encore en charge le type de chargement "Initial et delta/Delta uniquement".{0}{0}Pour déployer le flux de réplication, vous devez définir le type de chargement de chaque objet{0} sur "Initial uniquement". Vous pouvez également supprimer la planification, déployer le flux de réplication{0}, puis lancer une nouvelle exécution. Cela entraînera une exécution sans {0}fin qui prendra également en charge les objets avec le type de chargement "Initial et delta/Delta uniquement".
#XMSG
SCHEDULE_EXCEPTION=Échec de l'obtention des détails de la planification
#XFLD: Label for frequency column
everyLabel=Tous les/Toutes les
#XFLD: Plural Recurrence text for Hour
hoursLabel=Heures
#XFLD: Plural Recurrence text for Day
daysLabel=Jours
#XFLD: Plural Recurrence text for Month
monthsLabel=Mois
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutes
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Échec de l'obtention d'informations à propos des possibilités de planification
#XFLD :Paused field
PAUSED=Suspendu
#XMSG
navToMonitoring=Ouvrir dans le moniteur des flux
#XFLD
statusLbl=Statut
#XFLD
lblLastRunExecuted=Début de la dernière exécution
#XFLD
lblLastExecuted=Dernière exécution
#XFLD: Status text for Completed
statusCompleted=Terminé
#XFLD: Status text for Running
statusRunning=En cours d'exécution
#XFLD: Status text for Failed
statusFailed=Échec
#XFLD: Status text for Stopped
statusStopped=Arrêté
#XFLD: Status text for Stopping
statusStopping=Arrêt en cours
#XFLD: Status text for Active
statusActive=Actif
#XFLD: Status text for Paused
statusPaused=Suspendu
#XFLD: Status text for not executed
lblNotExecuted=Pas encore exécuté
#XFLD
messagesSettings=Paramètres des messages
#XTOL
@validateModel=Messages de validation
#XTOL
@hierarchy=Hiérarchie
#XTOL
@columnCount=Nombre de colonnes
#XMSG
VAL_PACKAGE_CHANGED=Vous avez affecté cet objet au package "{1}". Cliquez sur "Enregistrer" pour confirmer et valider cette modification. Notez qu''une affectation à un package est irréversible dans cet éditeur une fois l''enregistrement effectué.
#XMSG
MISSING_DEPENDENCY=Les dépendances de l''objet "{0}" ne peuvent pas être résolues dans le package "{1}".
#XFLD
deltaLoadInterval=Intervalle de chargement delta
#XFLD
lblHour=Heures (0-24)
#XFLD
lblMinutes=Minutes (0-59)
#XMSG
maxHourOrMinErr=Saisissez une valeur comprise entre 0 et {0}.
#XMSG
maxDeltaInterval=La valeur maximale de l''intervalle de chargement delta est de 24 heures.{0}Modifiez la valeur des minutes ou la valeur des heures en conséquence.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Chemin d'accès au conteneur cible
#XFLD
confluentSubjectName=Nom du sujet
#XFLD
confluentSchemaVersion=Version du schéma
#XFLD
confluentIncludeTechKeyUpdated=Inclure la clé technique
#XFLD
confluentOmitNonExpandedArrays=Omettre les tableaux non développés
#XFLD
confluentExpandArrayOrMap=Développer le tableau ou la carte
#XCOL
confluentOperationMapping=Opération de mappage
#XCOL
confluentOpCode=Code d'opération
#XFLD
confluentInsertOpCode=Insérer
#XFLD
confluentUpdateOpCode=Mettre à jour
#XFLD
confluentDeleteOpCode=Supprimer
#XFLD
expandArrayOrMapNotSelectedTxt=Non sélectionné
#XFLD
confluentSwitchTxtYes=Oui
#XFLD
confluentSwitchTxtNo=Non
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Erreur
#XTIT
executeWarning=Avertissement
#XMSG
executeunsavederror=Enregistrez votre flux de réplication avant de l'exécuter.
#XMSG
executemodifiederror=Il existe des modifications non enregistrées dans le flux de réplication. Veuillez enregistrer le flux de réplication.
#XMSG
executeundeployederror=Vous devez déployer votre flux de réplication avant de pouvoir l'exécuter.
#XMSG
executedeployingerror=Veuillez attendre que le déploiement soit terminé.
#XMSG
msgRunStarted=Exécution lancée
#XMSG
msgExecuteFail=L'exécution du flux de réplication a échoué.
#XMSG
titleExecuteBusy=Veuillez patienter.
#XMSG
msgExecuteBusy=Nous préparons vos données pour exécuter le flux de réplication.
#XTIT
executeConfirmDialog=Avertissement
#XMSG
msgExecuteWithValidations=Le flux de réplication comporte des erreurs de validation. Cela peut entraîner l'échec de l'exécution.
#XMSG
msgRunDeployedVersion=Il existe des modifications à déployer. La dernière version déployée du flux de réplication sera exécutée. Voulez-vous poursuivre?
#XBUT
btnExecuteAnyway=Exécuter tout de même
#XBUT
btnExecuteClose=Fermer
#XBUT
loaderClose=Fermer
#XTIT
loaderTitle=Chargement
#XMSG
loaderText=Récupération des détails à partir du serveur
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Impossible de lancer le flux de réplication pour cette connexion cible non SAP
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=car aucun volume de sortie n'est disponible pour ce mois.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Un administrateur peut augmenter le nombre de blocs de sortie premium pour ce
#XMSG
premiumOutBoundRFAdminErrMsgPart2=locataire, ce qui mettrait du volume de sortie à disposition pour ce mois.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Erreur
#XTIT
deployInfo=Informations
#XMSG
deployCheckFailException=Une exception s'est produite pendant le déploiement.
#XMSG
deployGBQFFDisabled=Le déploiement des flux de réplication avec une connexion cible à Google BigQuery est actuellement impossible car nous effectuons une maintenance sur cette fonction.
#XMSG
deployKAFKAFFDisabled=Le déploiement des flux de réplication avec une connexion cible à Apache Kafka est actuellement impossible car nous effectuons une maintenance sur cette fonction.
#XMSG
deployConfluentDisabled=Le déploiement des flux de réplication avec une connexion cible à Confluent Kafka est actuellement impossible car nous effectuons une maintenance sur cette fonction.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Pour les objets cible suivants, les noms de tables des captures delta sont déjà utilisés par d''autres tables dans le référentiel : {0} Vous devez renommer ces objets cible pour vous assurer que les noms de tables des captures delta sont uniques avant de déployer le flux de réplication.
#XMSG
deployDWCSourceFFDisabled=Le déploiement de flux de réplication ayant comme source SAP Datasphere est actuellement impossible car nous effectuons une maintenance sur cette fonction.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Le déploiement de flux de réplication comportant des tables locales compatibles delta comme objets source est actuellement impossible, car nous effectuons une maintenance sur cette fonction.
#XMSG
deployHDLFSourceFFDisabled=Le déploiement de flux de réplication ayant des connexions source avec le type de connexion SAP HANA Cloud, lac de données - Fichiers est actuellement impossible car nous effectuons une maintenance.
#XMSG
deployObjectStoreAsSourceFFDisabled=Le déploiement de flux de réplication ayant des fournisseurs de stockage en nuage comme source est actuellement impossible.
#XMSG
deployConfluentSourceFFDisabled=Le déploiement de flux de réplication ayant comme source Confluent Kafka est actuellement impossible car nous effectuons une maintenance sur cette fonction.
#XMSG
deployMaxDWCNewTableCrossed=Vous ne pouvez "enregistrer et déployer" les flux de réplication de grande ampleur en une seule fois. Enregistrez d'abord votre flux de réplication, puis déployez-le.
#XMSG
deployInProgressInfo=Le déploiement est déjà en cours.
#XMSG
deploySourceObjectInUse=Les objets source {0} sont déjà utilisés dans les flux de réplication {1}.
#XMSG
deployTargetSourceObjectInUse=Les objets source {0} sont déjà utilisés dans les flux de réplication {1}. Les objets cible {2} sont déjà utilisés dans les flux de réplication {3}.
#XMSG
deployReplicationFlowCheckError=Erreur lors de la vérification du flux de réplication : {0}
#XMSG
preDeployTargetObjectInUse=Les objets cible {0} sont déjà utilisés dans des flux de réplication {1} et vous ne pouvez pas avoir le même objet cible dans deux flux de réplication différents. Sélectionnez un autre objet cible et réessayez.
#XMSG
runInProgressInfo=Le flux de réplication est déjà en cours d'exécution
#XMSG
deploySignavioTargetFFDisabled=Le déploiement de flux de réplication ayant comme cible SAP Signavio est actuellement impossible car nous effectuons une maintenance sur cette fonction.
#XMSG
deployHanaViewAsSourceFFDisabled=Le déploiement de flux de réplication ayant des vues comme objets source pour la connexion source sélectionnée est actuellement impossible. Réessayez ultérieurement.
#XMSG
deployMsOneLakeTargetFFDisabled=Le déploiement de flux de réplication ayant comme cible MS OneLake est actuellement impossible car nous effectuons une maintenance sur cette fonction.
#XMSG
deploySFTPTargetFFDisabled=Le déploiement de flux de réplication ayant comme cible SFTP est actuellement impossible car nous effectuons une maintenance sur cette fonction.
#XMSG
deploySFTPSourceFFDisabled=Le déploiement de flux de réplication ayant comme source SFTP est actuellement impossible car nous effectuons une maintenance sur cette fonction.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Nom technique
#XFLD
businessNameInRenameTarget=Appellation
#XTOL
renametargetDialogTitle=Renommer l'objet cible
#XBUT
targetRenameButton=Renommer
#XBUT
targetRenameCancel=Annuler
#XMSG
mandatoryTargetName=Vous devez saisir un nom.
#XMSG
dwcSpecialChar=_(trait de soulignement) est le seul caractère spécial autorisé.
#XMSG
dwcWithDot=Le nom de table cible peut contenir des lettres latines, des chiffres, des traits de soulignement (_) et des points (.). Le premier caractère doit être une lettre, un chiffre ou un trait de soulignement (et non un point).
#XMSG
nonDwcSpecialChar=Les caractères spéciaux autorisés sont _(trait de soulignement) -(trait d'union) .(point)
#XMSG
firstUnderscorePattern=Le nom ne doit pas commencer par _(trait de soulignement).

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0} : afficher l''instruction SQL Créer une table
#XMSG
sqlDialogMaxPKWarning=Dans Google BigQuery, seules 16 clés primaires peuvent être prises en charge et l'objet source en compte plus que 16. Par conséquent, aucune clé primaire n'est définie dans cette instruction.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Une ou plusieurs colonnes source comportent des types de données impossibles à définir comme clés primaires dans Google BigQuery. Par conséquent, aucune clé primaire n'est définie dans ce cas. Dans Google BigQuery, seuls les types de données suivants peuvent avoir une clé primaire : BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP.
#XBUT
copyAndCloseDDL=Copier et fermer
#XBUT
closeDDL=Fermer
#XMSG
copiedToClipboard=Copié dans le presse-papiers


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=L''objet "{0}" ne peut pas faire partie d''une chaîne de tâches car il n''a pas de fin (présence d''objets dont le type de chargement est Initial et delta/Delta uniquement).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=L''objet "{0}" ne peut pas faire partie d''une chaîne de tâches car il n''a pas de fin (présence d''objets dont le type de chargement est Initial et delta).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Impossible d''ajouter l''objet "{0}" à la chaîne de tâches.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Il existe des objets cible non enregistrés. Enregistrez à nouveau.{0}{0} Le comportement de cette fonctionnalité a évolué : par le passé, les objets cible étaient uniquement créés dans l''environnement cible lorsque le flux de réplication était déployé.{0} Désormais, les objets sont déjà créés lorsque le flux de réplication est enregistré. Votre flux de réplication a été créé avant cette modification et contient de nouveaux objets.{0} Vous devez enregistrer le flux de réplication une nouvelle fois avant de le déployer pour que les nouveaux objets soient correctement inclus.
#XMSG
confirmChangeContentTypeMessage=Vous êtes sur le point de modifier le type de contenu. Si c'est le cas, toutes les projections existantes seront supprimées.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Nom du sujet
#XFLD
schemaDialogVersionName=Version du schéma
#XFLD
includeTechKey=Inclure la clé technique
#XFLD
segementButtonFlat=Plat
#XFLD
segementButtonNested=Imbriqué
#XMSG
subjectNamePlaceholder=Rechercher le nom du sujet

#XMSG
@EmailNotificationSuccess=La configuration des notifications par courriel d'exécution est enregistrée.

#XFLD
@RuntimeEmailNotification=Notification par courriel de l'exécution

#XBTN
@TXT_SAVE=Enregistrer


