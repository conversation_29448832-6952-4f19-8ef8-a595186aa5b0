#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Replikační tok

#XFLD: Edit Schema button text
editSchema=Upravit schéma

#XTIT : Properties heading
configSchema=Konfigurovat schéma

#XFLD: save changed button text
applyChanges=Použít změny


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Vybrat připojení zdroje
#XFLD
sourceContainernEmptyText=Vybrat kontejner
#XFLD
targetConnectionEmptyText=Vybrat cílový kontejner
#XFLD
targetContainernEmptyText=Vybrat kontejner
#XFLD
sourceSelectObjectText=Vybrat zdrojový objekt
#XFLD
sourceObjectCount=Zdrojové objekty ({0})
#XFLD
targetObjectText=<PERSON><PERSON>lov<PERSON> objekty
#XFLD
confluentBrowseContext=Vybrat kontext
#XBUT
@retry=Opakovat
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Upgrade tenanta probíhá.

#XTOL
browseSourceConnection=Procházet připojení zdroje
#XTOL
browseTargetConnection=Procházet připojení cíle
#XTOL
browseSourceContainer=Procházet zdrojový kontejner
#XTOL
browseAndAddSourceDataset=Přidat zdrojové objekty
#XTOL
browseTargetContainer=Procházet cílový kontejner
#XTOL
browseTargetSetting=Procházet nastavení cíle
#XTOL
browseSourceSetting=Procházet nastavení zdroje
#XTOL
sourceDatasetInfo=Informace
#XTOL
sourceDatasetRemove=Odebrat
#XTOL
mappingCount=Představuje celkový počet mapování/výrazů bez názvu.
#XTOL
filterCount=Představuje celkový počet podmínek filtru.
#XTOL
loading=Načítání...
#XCOL
deltaCapture=Zaznamenání delta
#XCOL
deltaCaptureTableName=Tabulka záznamu delta
#XCOL
loadType=Typ zavedení
#XCOL
deleteAllBeforeLoading=Před načtením odstranit vše
#XCOL
transformationsTab=Projekce
#XCOL
settingsTab=Nastavení

#XBUT
renameTargetObjectBtn=Přejmenovat cílový objekt
#XBUT
mapToExistingTargetObjectBtn=Mapovat na existující cílový objekt
#XBUT
changeContainerPathBtn=Změnit cestu kontejneru
#XBUT
viewSQLDDLUpdated=Zobrazit příkaz SQL Vytvořit tabulku
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Zdrojový objekt nepodporuje zaznamenání delta, ale vybraný cílový objekt možnost zaznamenání delty aktivoval.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Cílový objekt nelze použít, protože je povolen záznam delta,{0}zatímco zdrojový objekt záznam delta snímání.{1}Můžete vybrat jiný cílový objekt, který záznam delta nepodporuje.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Cílový objekt se stejným názvem již existuje. Nelze však použít {0}, protože je aktivován záznam delta, zatímco zdrojový objekt nepodporuje{0}záznam delta.{1}Buď můžete zadat název existujícího cílového objektu, který nepodporuje{0}záznam delta, nebo zadat název, který ještě neexistuje.
#XBUT
copySQLDDLUpdated=Kopírovat příkaz SQL Vytvořit tabulku
#XMSG
targetObjExistingNoCDCColumnUpdated=Stávající tabulky v Google BigQuery musí zahrnovat následující sloupce pro zachycení změněných dat (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Následující zdrojové objekty nejsou podporovány, protože nemají primární klíč nebo používají připojení, které nesplňuje podmínky pro načtení primárního klíče:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Možné řešení viz SAP KBA 3531135.
#XLST: load type list values
initial=Pouze iniciální
@emailUpdateError=Chyba aktualizace seznamu e-mailových oznámení

#XLST
initialDelta=Iniciální a delta

#XLST
deltaOnly=Jen delta
#XMSG
confluentDeltaLoadTypeInfo=Pro zdroj Confluent Kafka je podporován jen typ zavedení Iniciální a delta.
#XMSG
confirmRemoveReplicationObject=Potvrzujete, že chcete odstranit tuto replikaci?
#XMSG
confirmRemoveReplicationTaskPrompt=Tato akce odstraní existující replikace. Chcete pokračovat?
#XMSG
confirmTargetConnectionChangePrompt=Tato akce resetuje připojení cíle, cílový kontejner a odstraní všechny cílové objekty. Chcete pokračovat?
#XMSG
confirmTargetContainerChangePrompt=Tato akce resetuje cílový kontejner a odstraní všechny existující cílové objekty. Chcete pokračovat?
#XMSG
confirmRemoveTransformObject=Potvrzujete, že chcete odstranit tuto projekci {0}?
#XMSG
ErrorMsgContainerChange=Při změně cesty kontejneru došlo k chybě.
#XMSG
infoForUnsupportedDatasetNoKeys=Následující zdrojové objekty nejsou podporovány, protože nemají primární klíč:
#XMSG
infoForUnsupportedDatasetView=Následující zdrojové objekty typu Pohledy nejsou podporovány.
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Následující zdrojový objekt není podporován, protože to je pohled SQL obsahující vstupní parametry:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Následující zdrojové objekty nejsou podporovány, protože je pro ně deaktivována extrakce:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Pro připojení Confluent jsou jedinými povolenými formáty serializace AVRO a JSON. Následující objekty nejsou podporovány, protože používají jiný formát serializace:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Nelze vyvolat schéma pro následující objekty. Vyberte vhodný kontext nebo ověřte konfiguraci registru schématu
#XTOL: warning dialog header on deleting replication task
deleteHeader=Odstranit
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Nastavení Před načtením odstranit vše není pro Google BigQuery podporováno.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Nastavení Odstranit vše předem odstraní a znovu vytvoří objekt (téma) před každou replikací. Odstraní také všechny přiřazené zprávy.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Nastavení Odstranit vše předem není pro tento typ cíle podporováno.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Technický název
#XCOL
connBusinessName=Business název
#XCOL
connDescriptionName=Popis
#XCOL
connType=Typ
#XMSG
connTblNoDataFoundtxt=Žádná připojení nenalezena
#XMSG
connectionError=Při vyvolání připojení došlo k chybě.
#XMSG
connectionCombinationUnsupportedErrorTitle=Kombinace připojení není podporována
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Replikace z {0} do {1} momentálně není podporována.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Kombinace typu připojení není podporována
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Replikace z připojení s typem připojení SAP HANA Cloud, soubory datového jezera do {0} není podporována. Můžete replikovat do SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Vybrat
#XBUT
containerCancelBtn=Zrušit
#XTOL
containerSelectTooltip=Vybrat
#XTOL
containerCancelTooltip=Zrušit
#XMSG
containerContainerPathPlcHold=Cesta kontejneru
#XFLD
containerContainertxt=Kontejner
#XFLD
confluentContainerContainertxt=Kontext
#XMSG
infoMessageForSLTSelection=Jako kontejner je přípustné ID jednotlivého/SLT/hromadného přenosu. Vyberte ID hromadného přenosu pod SLT (je-li dostupné) a klikněte na Odeslat.
#XMSG
msgFetchContainerFail=Při vyvolání dat kontejneru došlo k chybě.
#XMSG
infoMessageForSLTHidden=Toto připojení nepodporuje složky SLT, takže se v níže uvedeném seznamu nezobrazí.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Vyberte kontejner, který obsahuje podsložky.
#XMSG
sftpIncludeSubFolderText=Nepravda
#XMSG
sftpIncludeSubFolderTextNew=Ne

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Dosud žádné mapování filtru)
#XMSG
failToFetchRemoteMetadata=Při vyvolání metadat došlo k chybě.
#XMSG
failToFetchData=Při vyvolání existujícího cíle došlo k chybě.
#XCOL
@loadType=Typ zavedení
#XCOL
@deleteAllBeforeLoading=Před načtením odstranit vše

#XMSG
@loading=Načítání...
#XFLD
@selectSourceObjects=Vybrat zdrojové objekty
#XMSG
@exceedLimit=Nemůžete importovat více než {0} objekty(ů) současně. Zrušte výběr alespoň {1} objektů.
#XFLD
@objects=Objekty
#XBUT
@ok=OK
#XBUT
@cancel=Zrušit
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Další
#XBUT
btnAddSelection=Přidat výběr
#XTOL
@remoteFromSelection=Odebrat z výběru
#XMSG
@searchInForSearchField=Hledat v {0}

#XCOL
@name=Technický název
#XCOL
@type=Typ
#XCOL
@location=Lokace
#XCOL
@label=Business název
#XCOL
@status=Status

#XFLD
@searchIn=Hledat v:
#XBUT
@available=Dostupné
#XBUT
@selection=Výběr

#XFLD
@noSourceSubFolder=Tabulky a pohledy
#XMSG
@alreadyAdded=Již existuje v diagramu
#XMSG
@askForFilter=Existuje více než {0} položek. Pro snížení počtu položek zadejte řetězec filtru.
#XFLD: success label
lblSuccess=Úspěch
#XFLD: ready label
lblReady=Připraveno
#XFLD: failure label
lblFailed=Neúspěšné
#XFLD: fetching status label
lblFetchingDetail=Vyvolání detailů

#XMSG Place holder text for tree filter control
filterPlaceHolder=Zadejte text pro filtr objektů nejvyšší úrovně
#XMSG Place holder text for server search control
serverSearchPlaceholder=Proveďte zadání a pro hledání stiskněte Enter
#XMSG
@deployObjects=Importují se {0} objekty...
#XMSG
@deployObjectsStatus=Počet importovaných objektů: {0}. Počet objektů, které nebylo možné importovat: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Nezdařilo se otevřít prohlížeč lokálního úložiště.
#XMSG
@openRemoteSourceBrowserError=Nezdařilo se vyvolat zdrojové objekty.
#XMSG
@openRemoteTargetBrowserError=Nezdařilo se vyvolat cílové objekty.
#XMSG
@validatingTargetsError=Při ověřování cílů došlo k chybě.
#XMSG
@waitingToImport=Připraveno na import

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Maximální počet objektů byl překročen. Pro tok replikace vyberte maximálně 500 objektů.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Technický název
#XFLD
sourceObjectBusinessName=Business název
#XFLD
sourceNoColumns=Počet sloupců
#XFLD
containerLbl=Kontejner

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Musíte vybrat připojení zdroje pro tok replikace.
#XMSG
validationSourceContainerNonExist=Musíte vybrat kontejner pro připojení zdroje.
#XMSG
validationTargetNonExist=Musíte vybrat připojení cíle pro tok replikace.
#XMSG
validationTargetContainerNonExist=Musíte vybrat kontejner pro připojení cíle.
#XMSG
validationTruncateDisabledForObjectTitle=Replikace do úložišť objektů.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Replikace do cloudového úložiště je možná pouze v případě, že je buď nastavena možnost Před načtením odstranit vše, nebo cílový objekt v cíli neexistuje. {0}{0}Chcete-li stále povolit replikaci pro objekty, pro které není nastavena možnost Před načtením odstranit vše, ujistěte se, že cílový objekt v systému neexistuje, než spustíte replikační tok.
#XMSG
validationTaskNonExist=V toku replikace musíte mít alespoň jednu replikaci.
#XMSG
validationTaskTargetMissing=Musíte mít cíl pro replikaci se zdrojem: {0}
#XMSG
validationTaskTargetIsSAC=Vybraný cíl je artefakt SAC: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Vybraný cíl není podporovaná lokální tabulka: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Objekt s tímto názvem již v cíli existuje. Tento objekt však nelze použít jako cílový objekt pro replikační tok do lokálního úložiště,                                                        protože to není lokální tabulka.
#XMSG
validateSourceTargetSystemDifference=Pro tok replikace musíte vybrat rozdílné připojení zdroje a cíle a kombinace kontejneru.
#XMSG
validateDuplicateSources=jedna nebo více replikací má duplicitní názvy zdrojového objektu: {0}.
#XMSG
validateDuplicateTargets=jedna nebo více replikací má duplicitní názvy cílového objektu: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=Zdrojový objekt  {0} nepodporuje zaznamenání delta, zatímco cílový objekt {1} ano. Musíte odebrat replikaci.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Pro replikaci s názvem cílového objektu {0} musíte vybrat typ zavedení "Iniciální a delta".
#XMSG
validationAutoRenameTarget=Cílové sloupce musí být přejmenovány.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Byla přidána automatická projekce a byly přejmenovány následující cílové sloupce, aby umožnily replikaci na cíl:{1}{1} {0} {1}{1}To je z některého z následujících důvodů:{1}{1}{2} Nepodporované znaky{1}{2} Rezervovaný prefix
#XMSG
validationAutoRenameTargetDescriptionUpdated=Byla přidána automatická projekce a byly přejmenovány následující cílové sloupce, aby umožnily replikaci na Google BigQuery:{1}{1} {0} {1}{1}To je z některého z následujících důvodů:{1}{1}{2} Název rezervovaného sloupce{1}{2} Nepodporované znaky{1}{2} Rezervovaný prefix
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Byla přidána automatická projekce a byly přejmenovány následující cílové sloupce, aby umožnily replikaci na Confluent{1}{1} {0} {1}{1}To je z některého z následujících důvodů:{1}{1}{2} Název rezervovaného sloupce{1}{2} Nepodporované znaky{1}{2} Rezervovaný prefix
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Byla přidána automatická projekce a byly přejmenovány následující cílové sloupce, aby umožnily replikaci na cíl{1}{1} {0} {1}{1}To je z některého z následujících důvodů:{1}{1}{2} Název rezervovaného sloupce{1}{2} Nepodporované znaky{1}{2} Rezervovaný prefix
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Cílový objekt byl přejmenován.
#XMSG
autoRenameInfoDesc=Cílový objekt byl přejmenován, protože obsahoval nepodporované znaky. Podporovány jsou jen tyto znaky:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(tečka){0}{1}_(podtržítko){0}{1}-(pomlčka)
#XMSG
validationAutoTargetTypeConversion=Cílové datové typy byly změněny.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Pro následující cílové sloupce byly změněny datové typy, protože v Google BigQuery nejsou zdrojové datové typy podporovány:\{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Pro následující cílové sloupce byly změněny cílové datové typy, protože zdrojové datové typy nejsou podporovány v cílovém připojení:  {1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Zkrácené názvy cílového sloupce.
#XMSG
validationMaxCharLengthGBQTargetDescription=V Google BigQuery mohou mít názvy sloupců maximálně 300 znaků. Použijte projekci pro zkrácení následujících názvů cílových sloupců:\{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Primární klíče se nevytvoří.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=V Google BigQuery je podporováno maximálně 16 primárních klíčů, ale zdrojový objekt má více než 16 primárních klíčů. Žádný z primárních klíčů nebude vytvořen v cílovém objektu.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Jeden nebo více zdrojových sloupců obsahuje datové typy, které nelze v Google BigQuery definovat jako primární klíče. V cílovém objektu nebude vytvořen žádný primární klíč.{0}{0}Následující cílové datové typy jsou kompatibilní s datovými typy Google BigQuery, pro které lze primární klíč definovat:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Definujte jeden nebo více sloupců jako primární klíč.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Chcete-li to provést, musíte definovat jeden nebo více sloupců jako dialogové okno se zdrojovým schématem primárního klíče.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Definujte jeden nebo více sloupců jako primární klíč.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Jeden nebo více sloupců musíte definovat jako primární klíč, který odpovídá omezením primárního klíče pro váš zdrojový objekt. Abyste tak učinili, přejděte na konfiguraci schématu ve vlastnostech vašeho zdrojového objektu. 
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Zadejte platnou maximální hodnotu oddílu.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Maximální hodnota oddílu musí být ≥ 1 a ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Definujte jeden nebo více sloupců jako primární klíč
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Pro replikaci objektu musíte definovat jeden nebo více cílových sloupců jako primární klíč. K tomu použijte projekci.
#XMSG
validateHDLFNoPKExistingDatasetError=Definujte jeden nebo více sloupců jako primární klíč.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Pro replikaci dat do existujícího cílového objektu musí mít tento objekt jeden nebo více sloupců definovaných jako primární klíč. {0} Pro definování jednoho nebo více sloupců jako primárních klíčů máte následující možnosti: {0} {1}Použijte editor lokální tabulky ke změně existujícího cílového objektu. Potom znovu načtěte replikační tok. {0}{1} Přejmenujte cílový objekt v tomto replikačním toku. Tím se po spuštění běhu vytvoří nový objekt. Po přejmenování můžete definovat jeden nebo více sloupců jako primární klíče v projekci.{0}{1} Namapujte tento objekt na jiný existující cílový objekt, v němž je již jeden nebo více sloupců definováno jako primární klíč.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Vybraný objekt již existuje v úložišti: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Pro následující cílové objekty jsou názvy tabulek záznamů delta již použity v jiných tabulkách v úložišti: {0}. Než budete moci uložit replikační tok, musíte tyto cílové objekty přejmenovat, abyste se ujistili, že přidružené názvy tabulek záznamů delta jsou jedinečné.
#XMSG
validateConfluentEmptySchema=Definovat schéma
#XMSG
validateConfluentEmptySchemaDescUpdated=Zdrojová tabulka nemá schéma. Definujete ho volbou Konfigurovat schéma
#XMSG
validationCSVEncoding=Neplatné kódování CSV
#XMSG
validationCSVEncodingDescription=Kódování úlohy CSV je neplatné.
#XMSG
validateConfluentEmptySchema=Vyberte kompatibilní cílový datový typ
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Vyberte kompatibilní cílový datový typ
#XMSG
globalValidateTargetDataTypeDesc=Došlo k chybě mapování sloupců. Přejděte k Projekci, abyste zajistili, že všechny zdrojové sloupce jsou namapovány s jedinečným sloupcem, se sloupcem kompatibilního datového typu, a že všechny definované výrazy jsou platné.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Duplicitní názvy sloupců.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Duplicitní názvy sloupců nejsou podporovány. K jejich opravě použijte dialogové okno Projekce. Následující cílové objekty mají duplicitní názvy sloupců: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Duplicitní názvy sloupců.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Duplicitní názvy sloupců nejsou podporovány. Následující cílové objekty mají duplicitní názvy sloupců: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Může dojít k nekonzistencím v datech.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Typ zavedení Pouze delta nezohlední změny provedené ve zdroji mezi posledním uložením a příštím během.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Změňte typ zavedení na "Iniciální".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Replikace objektů založených na ABAP, které nemají primární klíč, je možná pouze pro typ zavedení "Pouze iniciální".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Deaktivovat zaznamenání delta.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Chcete-li replikovat objekt, který nemá primární klíč, pomocí zdrojového připojení typu ABAP, musíte nejprve pro tuto tabulku deaktivovat zaznamenání delta.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Změnit cílový objekt.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Cílový objekt nelze použít, protože zaznamenání delta je aktivováno. Můžete buď přejmenovat cílový objekt a poté deaktivovat zaznamenání delta pro nový (přejmenovaný) objekt, nebo namapovat zdrojový objekt na cílový objekt, pro který je zaznamenání delta deaktivováno.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Změnit cílový objekt.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Cílový objekt nelze použít, protože nemá požadovaný technický sloupec __load_package_id. Cílový objekt můžete přejmenovat pomocí názvu, který dosud neexistuje. Systém pak vytvoří nový objekt, který má stejnou definici jako zdrojový objekt a obsahuje technický sloupec. Alternativně můžete namapovat cílový objekt na existující objekt, který má požadovaný technický sloupec (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Změnit cílový objekt.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Cílový objekt nelze použít, protože nemá požadovaný technický sloupec __load_record_id. Cílový objekt můžete přejmenovat pomocí názvu, který dosud neexistuje. Systém pak vytvoří nový objekt, který má stejnou definici jako zdrojový objekt a obsahuje technický sloupec. Alternativně můžete namapovat cílový objekt na existující objekt, který má požadovaný technický sloupec (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Změnit cílový objekt.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Cílový objekt nelze použít, protože datový typ jeho technického sloupce __load_record_id není "string(44)". Cílový objekt můžete přejmenovat pomocí názvu, který dosud neexistuje. Systém pak vytvoří nový objekt, který má stejnou definici jako zdrojový objekt a následně správný datový typ. Alternativně můžete namapovat cílový objekt na existující objekt, který má požadovaný technický sloupec (__load_record_id) se správným datovým typem.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Změnit cílový objekt.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Cílový objekt nelze použít, protože má primární klíč, zatímco zdrojový objekt žádný nemá. Cílový objekt můžete přejmenovat pomocí názvu, který dosud neexistuje. Systém pak vytvoří nový objekt, který má stejnou definici jako zdrojový objekt a následně žádný primární klíč. Alternativně můžete namapovat cílový objekt na existující objekt, který má požadovaný technický sloupec (__load_package_id) a nemá primární klíč.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Změnit cílový objekt.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Cílový objekt nelze použít, protože má primární klíč, zatímco zdrojový objekt žádný nemá. Cílový objekt můžete přejmenovat pomocí názvu, který dosud neexistuje. Cílový objekt můžete přejmenovat pomocí názvu, který dosud neexistuje žádný primární klíč. Alternativně můžete namapovat cílový objekt na existující objekt, který má požadovaný technický sloupec (__load_package_id) a nemá primární klíč.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Změnit cílový objekt.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Cílový objekt nelze použít, protože datový typ jeho technického sloupce __load_record_id není  "binary(>=256)". Cílový objekt můžete přejmenovat pomocí názvu, který dosud neexistuje. Systém pak vytvoří nový objekt, který má stejnou definici jako zdrojový objekt, a následně správný datový typ. Alternativně můžete namapovat cílový objekt na existující objekt, který má požadovaný technický sloupec (__load_record_id) se správným datovým typem.
#XMSG
validationAutoRenameTargetDPID=Cílové sloupce byly přejmenovány.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Odebrat zdrojový objekt.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Zdrojový objekt nemá klíčový sloupec, který v tomto kontextu není podporován. 
#XMSG
validationAutoRenameTargetDPIDDescription=Byla přidána automatická projekce a byly přejmenovány následující cílové sloupce, aby umožnily replikaci ze zdrojů ABAP bez klíčů :{1}{1} {0} {1}{1}To je z některého z následujících důvodů:{1}{1}{2} Název rezervovaného sloupce{1}{2} Nepodporované znaky{1}{2} Rezervovaný prefix
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Replikace do {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Uložení a nasazení replikačních toků, které mají jako svůj cíl {0}, není momentálně možné, protože provádíme údržbu této funkce.
#XMSG
TargetColumnSkippedLTF=Cílový sloupec byl přeskočen.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Cílový sloupec byl přeskočen kvůli nepodporovanému datovému typu. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Sloupec času jako primární klíč.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Zdrojový objekt má jako primární klíč sloupec času, což v tomto kontextu není podporováno.
#XMSG
validateNoPKInLTFTarget=Chybí primární klíč.
#XMSG
validateNoPKInLTFTargetDescription=Primární klíč není definován v cíli, což není v tomto kontextu podporováno.
#XMSG
validateABAPClusterTableLTF=Clusterová tabulka ABAP.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Zdrojový objekt je clusterová tabulka ABAP, což není v tomto kontextu podporováno.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Vypadá to, že jste ještě nepřidali žádná data.
#YINS
welcomeText2=Pro zahájení vašeho toku replikace vyberte připojení a zdrojový objekt na levé straně.

#XBUT
wizStep1=Vybrat připojení zdroje
#XBUT
wizStep2=Vybrat zdrojový kontejner
#XBUT
wizStep3=Přidat zdrojové objekty

#XMSG
limitDataset=Bylo dosaženo maximálního počtu objektů. Odeberte existující objekty a přidejte nové nebo vytvořte nový tok replikace.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Tok replikace k tomuto cílovému připojení mimo SAP nelze spustit, protože pro tento měsíc není k dispozici žádný odchozí objem.
#XMSG
premiumOutBoundRFAdminWarningMsg=Administrátor může zvýšit bloky výstupní prémie pro tohoto tenanta a zpřístupnit tak odchozí objem pro tento měsíc.
#XMSG
messageForToastForDPIDColumn2=K cíli pro {0} objekty přidán nový sloupec - je zapotřebí ke zpracování duplicitních záznamů ve spojení se zdrojovými objekty na bázi ABAP, které nemají primární klíč.
#XMSG
PremiumInboundWarningMessage=V závislosti na počtu replikačních toků a objemu dat, která mají být replikována, mohou zdroje SAP HANA{0}požadované pro replikaci dat prostřednictvím {1} překročit dostupnou kapacitu vašeho tenanta.
#XMSG
PremiumInboundWarningMsg=V závislosti na počtu replikačních toků a objemu dat, která mají být replikována, mohou zdroje SAP HANA {0}požadované pro replikaci dat prostřednictvím "{1}" překročit dostupnou kapacitu vašeho tenanta.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Zadejte název projekce.
#XMSG
emptyTargetColumn=Zadejte název cílového sloupce.
#XMSG
emptyTargetColumnBusinessName=Zadejte business název cílového sloupce.
#XMSG
invalidTransformName=Zadejte název projekce.
#XMSG
uniqueColumnName=Přejmenujte cílový sloupec.
#XMSG
copySourceColumnLbl=Kopírujte sloupce ze zdrojového objektu
#XMSG
renameWarning=Ujistěte se, že jste při přejmenování cílové tabulky zvolili jedinečný název. Pokud tabulka s novým názvem již v prostoru existuje, použije definici této tabulky.

#XMSG
uniqueColumnBusinessName=Přejmenujte business název cílového sloupce.
#XMSG
uniqueSourceMapping=Vyberte jiný zdrojový sloupec.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=Zdrojový sloupec {0} je již používán následujícími cílovými sloupci:{1}{1}{2}{1}{1} Pro tento cílový sloupec nebo pro jiné cílové sloupce vyberte zdrojový sloupec, který se ještě nepoužívá k uložení projekce.
#XMSG
uniqueColumnNameDescription=Vámi zadaný název cílového sloupce již existuje. Abyste mohli uložit projekci, musíte zde zadat jedinečný název sloupce.
#XMSG
uniqueColumnBusinessNameDesc=Tento business název cílového sloupce již existuje. Abyste mohli uložit projekci, musíte zde zadat jedinečný business název sloupce.
#XMSG
emptySource=Vyberte zdrojový sloupec nebo zadejte konstantu.
#XMSG
emptySourceDescription=Pro vytvoření platného záznamu mapování musíte vybrat zdrojový sloupec nebo zadat konstantní hodnotu.
#XMSG
emptyExpression=Definujte mapování.
#XMSG
emptyExpressionDescription1=Buď vyberte zdrojový sloupec, na který chcete namapovat cílový sloupec, nebo vyberte zaškrtávací pole ve sloupci {0} Funkce / Konstanty {1}. {2} {2} Funkce jsou zadávány automaticky podle datového typu cíle. Konstantní hodnoty lze zadávat manuálně.
#XMSG
numberExpressionErr=Zadejte číslo.
#XMSG
numberExpressionErrDescription=Vybrali jste numerický datový typ. To znamená, že můžete zadávat jen čísla a případně desetinnou čárku. Nepoužívejte jednoduché uvozovky.
#XMSG
invalidLength=Zadejte platnou hodnotu délky.
#XMSG
invalidLengthDescription=Délka datového typu musí být rovna nebo větší, než je délka zdrojového sloupce, a může být mezi 1 a 5000.
#XMSG
invalidMappedLength=Zadejte platnou hodnotu délky.
#XMSG
invalidMappedLengthDescription=Délka datového typu musí být rovna nebo větší, než je délka zdrojového sloupce {0}, a může být mezi 1 a 5000.
#XMSG
invalidPrecision=Zadejte platnou hodnotu přesnosti.
#XMSG
invalidPrecisionDescription=Přesnost definuje celkový číslic. Stupnice definuje počet číslic za řádovou čárkou a může být mezi 0 a přesností.{0}{0} Příklady: {0}{1} Přesnost 6, stupnice 2 odpovídá například číslu 1234.56.{0}{1} Přesnost 6, stupnice 6 odpovídá například číslu 0.123546.{0} {0} Přesnost a stupnice pro cíl musí být kompatibilní s přesností a stupnicí pro zdroj, aby se všechny číslice ze zdroje vešly do cílového pole. Když máte například přesnost 6 a stupnici 2 ve zdroji (a následně jiné číslice než 0 před řádovou čárkou), nemůžete mít v cíli přesnost 6 a stupnici 6.
#XMSG
invalidPrimaryKey=Zadejte alespoň jeden primární klíč.
#XMSG
invalidPrimaryKeyDescription=Primární klíč pro toto schéma nedefinován.
#XMSG
invalidMappedPrecision=Zadejte platnou hodnotu přesnosti.
#XMSG
invalidMappedPrecisionDescription1=Přesnost definuje celkový číslic. Stupnice definuje počet číslic za řádovou čárkou a může být mezi 0 a přesností.{0}{0} Příklady:{0}{1} Přesnost 6, stupnice 2 odpovídá například číslu 1234.56.{0}{1} Přesnost 6, stupnice 6 odpovídá například číslu 0.123546.{0}{0}Přesnost datového typu musí být stejná nebo větší než přesnost zdroje ({2}).
#XMSG
invalidScale=Zadejte platnou hodnotu stupnice.
#XMSG
invalidScaleDescription=Přesnost definuje celkový číslic. Stupnice definuje počet číslic za řádovou čárkou a může být mezi 0 a přesností.{0}{0} Příklady: {0}{1} Přesnost 6, stupnice 2 odpovídá například číslu 1234.56.{0}{1} Přesnost 6, stupnice 6 odpovídá například číslu 0.123546.{0} {0} Přesnost a stupnice pro cíl musí být kompatibilní s přesností a stupnicí pro zdroj, aby se všechny číslice ze zdroje vešly do cílového pole. Když máte například přesnost 6 a stupnici 2 ve zdroji (a následně jiné číslice než 0 před řádovou čárkou), nemůžete mít v cíli přesnost 6 a stupnici 6.
#XMSG
invalidMappedScale=Zadejte platnou hodnotu stupnice.
#XMSG
invalidMappedScaleDescription1=Přesnost definuje celkový číslic. Stupnice definuje počet číslic za řádovou čárkou a může být mezi 0 a přesností.{0}{0} Příklady:{0}{1} Přesnost 6, stupnice 2 odpovídá například číslu 1234.56.{0}{1} Přesnost 6, stupnice 6 odpovídá například číslu 0.123546.{0}{0}Stupnice datového typu musí být stejná nebo větší než stupnice zdroje ({2}).
#XMSG
nonCompatibleDataType=Vyberte kompatibilní cílový datový typ.
#XMSG
nonCompatibleDataTypeDescription1=Zde zadaný datový typ musí být kompatibilní se zdrojovým datovým typem ({0}). {1}{1} Příklad: Pokud má váš zdrojový sloupec datový typ řetězec a obsahuje písmena, nemůžete pro svůj cíl použít desítkový datový typ.
#XMSG
invalidColumnCount=Vyberte zdrojový sloupec.
#XMSG
ObjectStoreInvalidScaleORPrecision=Zadejte platnou hodnotu pro přesnost a stupnici.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=První hodnotou je přesnost, která definuje celkový počet číslic. Druhou hodnotou je stupnice, která definuje číslice za desetinnou čárkou. Zadejte cílovou hodnotu stupnice, která je větší než zdrojová hodnota stupnice, a ujistěte se, že rozdíl mezi zadanou cílovou hodnotou stupnice a přesnosti je větší než rozdíl mezi zdrojovou hodnotou stupnice a přesnosti.
#XMSG
InvalidPrecisionORScale=Zadejte platnou hodnotu pro přesnost a stupnici.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=První hodnota je přesnost, která definuje celkový počet číslic. Druhá hodnota je stupnice, která definuje počet číslic za řádovou čárkou.{0}{0}Protože zdrojový datový typ není v Google BigQuery podporován, je zkonvertován na cílový datový typ DECIMAL. V tomto případě může být přesnost definována jen mezi 38 a 76 a stupnice mezi 9 a 38. Navíc výsledek přesnosti minus stupnice, který představuje počet číslic před řádovou čárkou, musí být mezi 29 a 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=První hodnota je přesnost, která definuje celkový počet číslic. Druhá hodnota je stupnice, která definuje počet číslic za řádovou čárkou.{0}{0}Protože zdrojový datový typ není v Google BigQuery podporován, je zkonvertován na cílový datový typ DECIMAL. V tomto případě musí být přesnost definována jako 20 nebo větší. Navíc výsledek přesnosti minus stupnice, který odráží počet číslic před řádovou čárkou, musí být 20 nebo větší.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=První hodnota je přesnost, která definuje celkový počet číslic. Druhá hodnota je stupnice, která definuje počet číslic za řádovou čárkou.{0}{0}Protože zdrojový datový typ není v cíli podporován, je zkonvertován na cílový datový typ DECIMAL. V tomto případě musí být přesnost definována číslem větším nebo rovným 1 a menším nebo rovným 38 a stupnice musí být menší než přesnost nebo rovna přesnosti.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=První hodnota je přesnost, která definuje celkový počet číslic. Druhá hodnota je stupnice, která definuje číslice za řádovou čárkou.{0}{0}Protože zdrojový datový typ není v cíli podporován, je zkonvertován na cílový datový typ DECIMAL. V tomto případě musí být přesnost definována jako 20 nebo větší. Navíc výsledek přesnosti minus stupnice, který odráží počet číslic před řádovou čárkou, musí být 20 nebo větší.
#XMSG
invalidColumnCountDescription=Pro vytvoření platného záznamu mapování musíte vybrat zdrojový sloupec nebo zadat konstantní hodnotu.
#XMSG
duplicateColumns=Přejmenujte cílový sloupec.
#XMSG
duplicateGBQCDCColumnsDesc=Název cílového sloupce je rezervován v Google BigQuery. Abyste mohli uložit projekci, musíte ho přejmenovat.
#XMSG
duplicateConfluentCDCColumnsDesc=Název cílového sloupce je rezervován v Confluent. Abyste mohli uložit projekci, musíte ho přejmenovat.
#XMSG
duplicateSignavioCDCColumnsDesc=Název cílového sloupce je rezervován v SAP Signavio. Abyste mohli uložit projekci, musíte ho přejmenovat.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Název cílového sloupce je rezervován v MS OneLake. Abyste mohli uložit projekci, musíte ho přejmenovat.
#XMSG
duplicateSFTPCDCColumnsDesc=Název cílového sloupce je rezervován v SFTP. Abyste mohli uložit projekci, musíte ho přejmenovat.
#XMSG
GBQTargetNameWithPrefixUpdated1=Název cílového sloupce obsahuje prefix, který je rezervován v Google BigQuery. Abyste mohli uložit projekci, musíte ho přejmenovat. {0}{0}Název cílového sloupce nesmí začínat žádným z následujících řetězců:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Zkrácený název cílového sloupce.
#XMSG
GBQtargetMaxLengthDesc=V Google BigQuery může název sloupce použít maximálně 300 znaků. Zkraťte název cílového sloupce, abyste mohli uložit projekci.
#XMSG
invalidMappedScalePrecision=Přesnost a stupnice pro cíl musí být kompatibilní s přesností a stupnicí pro zdroj, aby se všechny číslice ze zdroje vešly do cílového pole.
#XMSG
invalidMappedScalePrecisionShortText=Zadejte platnou desetinnou hodnotu přesnosti a stupnice.
#XMSG
validationIncompatiblePKTypeDescProjection3=Jeden nebo více zdrojových sloupců obsahuje datové typy, které nelze v Google BigQuery definovat jako primární klíče. V cílovém objektu nebude vytvořen žádný primární klíč.{0}{0}Následující cílové datové typy jsou kompatibilní s datovými typy Google BigQuery, pro které lze primární klíč definovat:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Zrušte označení column __message_id.
#XMSG
uncheckColumnMessageIdDesc=Sloupec: Primární klíč
#XMSG
validationOpCodeInsert=Musíte zadat hodnotu pro vložení.
#XMSG
recommendDifferentPrimaryKey=Doporučujeme, abyste na úrovni položky vybrali jiný primární klíč.
#XMSG
recommendDifferentPrimaryKeyDesc=Když je kód operace již rezervován, doporučuje se vybrat pro index pole a položky jiné klíče, aby se zabránilo problémům, jako je například duplikování klíčů.
#XMSG
selectPrimaryKeyItemLevel=Pro úroveň hlavičky i pro úroveň položky musíte vybrat alespoň jeden primární klíč.
#XMSG
selectPrimaryKeyItemLevelDesc=Když jsou pole nebo mapa rozbaleny, musíte vybrat dva primární klíče, jeden na úrovni hlavičky a jeden na  úrovni položky.
#XMSG
invalidMapKey=Pro úroveň hlavičky musíte vybrat alespoň jeden primární klíč.
#XMSG
invalidMapKeyDesc=Když jsou pole nebo mapa rozbaleny, musíte vybrat primární klíč na úrovni hlavičky.
#XFLD
txtSearchFields=Hledat cílové sloupce
#XFLD
txtName=Název
#XMSG
txtSourceColValidation=Jeden nebo více zdrojových sloupců není podporováno:
#XMSG
txtMappingCount=Mapování ({0})
#XMSG
schema=Schéma
#XMSG
sourceColumn=Zdrojové sloupce
#XMSG
warningSourceSchema=Jakékoli změny schématu ovlivní mapování v dialogu projekce.
#XCOL
txtTargetColName=Cílový sloupec (technický název)
#XCOL
txtDataType=Cílový datový typ
#XCOL
txtSourceDataType=Typ zdroje dat
#XCOL
srcColName=Zdrojový sloupec (technický název)
#XCOL
precision=Přesnost
#XCOL
scale=Stupnice
#XCOL
functionsOrConstants=Funkce / Konstanty
#XCOL
txtTargetColBusinessName=Cílový sloupec (business název)
#XCOL
prKey=Primární klíč
#XCOL
txtProperties=Vlastnosti
#XBUT
txtOK=Uložit
#XBUT
txtCancel=Zrušit
#XBUT
txtRemove=Odebrat
#XFLD
txtDesc=Popis
#XMSG
rftdMapping=Mapování
#XFLD
@lblColumnDataType=Datový typ
#XFLD
@lblColumnTechnicalName=Technický název
#XBUT
txtAutomap=Automaticky mapovat
#XBUT
txtUp=Nahoru
#XBUT
txtDown=Dolů

#XTOL
txtTransformationHeader=Projekce
#XTOL
editTransformation=Upravit
#XTOL
primaryKeyToolip=Klíč


#XMSG
rftdFilter=Filtr
#XMSG
rftdFilterColumnCount=Zdroj: {0}({1})
#XTOL
rftdFilterColSearch=Hledat
#XMSG
rftdFilterColNoData=Žádné sloupce k zobrazení
#XMSG
rftdFilteredColNoExps=Žádné výrazy filtru
#XMSG
rftdFilterSelectedColTxt=Přidat filtr pro
#XMSG
rftdFilterTxt=Filtr dostupný pro
#XBUT
rftdFilterSelectedAddColExp=Přidat výraz
#YINS
rftdFilterNoSelectedCol=Vyberte sloupec pro přidání filtru.
#XMSG
rftdFilterExp=Výraz filtru
#XMSG
rftdFilterNotAllowedColumn=Přidání filtrů není pro tento sloupec podporováno.
#XMSG
rftdFilterNotAllowedHead=Nepodporovaný sloupec
#XMSG
rftdFilterNoExp=Nebyl definován žádný filtr
#XTOL
rftdfilteredTt=Filtrováno
#XTOL
rftdremoveexpTt=Odebrat výraz filtru
#XTOL
validationMessageTt=Ověřovací zprávy
#XTOL
rftdFilterDateInp=Vyberte datum
#XTOL
rftdFilterDateTimeInp=Vyberte datum a čas
#XTOL
rftdFilterTimeInp=Vyberte čas
#XTOL
rftdFilterInp=Zadejte hodnotu
#XMSG
rftdFilterValidateEmptyMsg={0} výrazy filtru ve sloupci {1} jsou prázdné
#XMSG
rftdFilterValidateInvalidNumericMsg={0} výrazy filtru ve sloupci {1} obsahuje neplatné numerické hodnoty
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Výraz filtru musí obsahovat platné numerické hodnoty
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Pokud se schéma cílového objektu změnilo, použijte funkci „Mapovat na existující cílový objekt“ na hlavní stránce k přizpůsobení změn a znovu namapujte cílový objekt s jeho zdrojem.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Pokud cílová tabulka již existuje a mapování zahrnuje změnu schématu, musíte před nasazením toku replikace cílovou tabulku odpovídajícím způsobem změnit.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Pokud vaše mapování zahrnuje změnu schématu, musíte před nasazením toku replikace cílovou tabulku odpovídajícím způsobem změnit.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Následující nepodporované sloupce byly při definování zdroje přeskočeny: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Následující nepodporované sloupce byly při definování cíle přeskočeny: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Následující objekty nejsou podporovány, protože jsou vystaveny pro spotřebu: {0} {1} {0} {0} Pro využití tabulek v replikačním toku musí být sémantické využití (v nastaveních tabulky) nastaveno na {2}Analytická množina dat{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Cílový objekt nelze použít, protože je vystaven pro spotřebu. {0} {0} Pro využití tabulek v replikačním toku musí být sémantické využití (v nastaveních tabulky) nastaveno na {1}Analytická množina dat{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Cílový objekt s tímto názvem již existuje. nelze jej však použít, protože je vystaven pro spotřebu. {0} {0} Pro využití tabulek v replikačním toku musí být sémantické využití (v nastaveních tabulky) nastaveno na {1}Analytická množina dat{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Objekt s tímto názvem již v cíli existuje. {0}Tento objekt však nelze použít jako cílový objekt pro replikační tok do lokálního úložiště,                                                        protože to není lokální tabulka.
#XMSG:
targetAutoRenameUpdated=Cílový sloupec byl přejmenován.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Cílový sloupec byl přejmenován, aby umožnil replikaci do Google BigQuery. To je kvůli některému z následujících důvodů:{0} {1}{2}Rezervovaný název sloupce{3}{2}Nepodporované znaky{3}{2}Rezervovaný prefix{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Cílový sloupec byl přejmenován, aby umožnil replikaci do Confluent. To je kvůli některému z následujících důvodů:{0} {1}{2}Rezervovaný název sloupce{3}{2}Nepodporované znaky{3}{2}Rezervovaný prefix{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Cílový sloupec byl přejmenován, aby umožnil replikaci do cíle. To je kvůli některému z následujících důvodů:{0} {1}{2}Nepodporované znaky{3}{2}Rezervovaný prefix{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Cílový sloupec byl přejmenován, aby umožnil replikaci do cíle. To je kvůli některému z následujících důvodů:{0} {1}{2}Rezervovaný název sloupce{3}{2}Nepodporované znaky{3}{2}Rezervovaný prefix{3}{4}
#XMSG:
targetAutoDataType=Cílový datový typ byl změněn.
#XMSG:
targetAutoDataTypeDesc=Cílový datový typ byl změněn na {0}, protože zdrojový datový typ není v Google BigQuery podporován.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Cílový datový typ byl změněn na {0}, protože zdrojový datový typ není v cílovém připojení podporován.
#XMSG
projectionGBQUnableToCreateKey=Primární klíče se nevytvoří.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=V Google BigQuery je podporováno maximálně 16 primárních klíčů, ale zdrojový objekt má primárních klíčů více. Žádný z primárních klíčů nebude vytvořen v cílovém objektu.
#XMSG
HDLFNoKeyError=Definujte jeden nebo více sloupců jako primární klíč
#XMSG
HDLFNoKeyErrorDescription=Pro replikaci objektu musíte definovat jeden nebo více sloupců jako primární klíč.
#XMSG
HDLFNoKeyErrorExistingTarget=Definujte jeden nebo více sloupců jako primární klíč
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Pro replikaci dat do existujícího cílového objektu musí mít tento objekt jeden nebo více sloupců definovaných jako primární klíč. {0}{0} Pro definování jednoho nebo více sloupců jako primárních klíčů máte následující možnosti: {0}{0}{1} Použijte editor lokální tabulky ke změně existujícího cílového objektu. Potom znovu načtěte replikační tok.{0}{0}{1} Přejmenujte cílový objekt v tomto replikačním toku. Tím se po spuštění běhu vytvoří nový objekt. Po přejmenování můžete definovat jeden nebo více sloupců jako primární klíče v projekci.{0}{0}{1} Namapujte tento objekt na jiný existující cílový objekt, v němž je již jeden nebo více sloupců definováno jako primární klíč.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Primární klíč změněn.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=V porovnání se zdrojovým objektem jste jako primární klíč pro cílový objekt definovali jiné sloupce. Ujistěte se, že tyto sloupce jednoznačně identifikují všechny řádky, abyste předešli možnému poškození dat při pozdější replikaci dat. {0} {0} Ve zdrojovém objektu jsou jako primární klíč definovány následující sloupce: {0} {1}
#XMSG
duplicateDPIDColumns=Přejmenujte cílový sloupec.
#XMSG
duplicateDPIDDColumnsDesc1=Tento název cílového sloupce je rezervován pro technický sloupec. Pro uložení projekce zadejte jiný název.
#XMSG:
targetAutoRenameDPID=Cílový sloupec byl přejmenován.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Cílový sloupec byl přejmenován, aby umožnil replikaci ze zdroje ABAP bez klíčů. Je to z některého z následujících důvodů:{0} {1}{2}Rezervovaný název sloupce{3}{2}Nepodporované znaky{3}{2}Rezervovaný prefix{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle={0} nastavení cíle
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle={0} nastavení zdroje
#XBUT
connectionSettingSave=Uložit
#XBUT
connectionSettingCancel=Zrušit
#XBUT: Button to keep the object level settings
txtKeep=Zachovat
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Přepsat
#XFLD
targetConnectionThreadlimit=Limit cílového vlákna pro iniciální zavedení (1-100)
#XFLD
connectionThreadLimit=Limit zdrojového vlákna pro iniciální zavedení (1-100)
#XFLD
maxConnection=Limit replikačního vlákna (1-100)
#XFLD
kafkaNumberOfPartitions=Počet oddílů
#XFLD
kafkaReplicationFactor=Koeficient replikace
#XFLD
kafkaMessageEncoder=Kodér zpráv
#XFLD
kafkaMessageCompression=Sbalení zpráv
#XFLD
fileGroupDeltaFilesBy=Seskupit deltu podle
#XFLD
fileFormat=Typ souboru
#XFLD
csvEncoding=Kódování CSV
#XFLD
abapExitLbl=Exit ABAP
#XFLD
deltaPartition=Počet vláken objektu pro delta zavedení (1-10)
#XFLD
clamping_Data=Neúspěšné při oříznutí dat
#XFLD
fail_On_Incompatible=Neúspěšné při nekompatibilních datech
#XFLD
maxPartitionInput=Maximální počet oddílů
#XFLD
max_Partition=Definovat maximální počet oddílů
#XFLD
include_SubFolder=Zahrnout podsložky
#XFLD
fileGlobalPattern=Globální vzor pro název souboru
#XFLD
fileCompression=Komprese souboru
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Omezovač souboru
#XFLD
fileIsHeaderIncluded=Hlavička souboru
#XFLD
fileOrient=Orientovat
#XFLD
gbqWriteMode=Zápisový režim
#XFLD
suppressDuplicate=Potlačit duplikáty
#XFLD
apacheSpark=Aktivovat kompatibilitu Apache Spark
#XFLD
clampingDatatypeCb=Svorka datových typů s pohyblivou desetinnou čárkou
#XFLD
overwriteDatasetSetting=Přepsat cílová nastavení na úrovni objektu
#XFLD
overwriteSourceDatasetSetting=Přepsat zdrojová nastavení na úrovni objektu
#XMSG
kafkaInvalidConnectionSetting=Zadejte číslo mezi {0} a {1}.
#XMSG
MinReplicationThreadErrorMsg=Zadejte číslo větší než {0}.
#XMSG
MaxReplicationThreadErrorMsg=Zadejte číslo menší než {0}.
#XMSG
DeltaThreadErrorMsg=Zadejte hodnotu mezi 1 a 10.
#XMSG
MaxPartitionErrorMsg=Zadejte hodnotu mezi 1 <= x <= 2147483647.Standardní hodnota je 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Zadejte celočíselnou hodnotu mezi {0} a {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Použijte replikační koeficient makléře
#XFLD
serializationFormat=Formát serializace
#XFLD
compressionType=Typ komprimace
#XFLD
schemaRegistry=Použijte registr schémat
#XFLD
subjectNameStrat=Strategie názvu předmětu
#XFLD
compatibilityType=Typ kompatibility
#XFLD
confluentTopicName=Název tématu
#XFLD
confluentRecordName=Název záznamu
#XFLD
confluentSubjectNamePreview=Náhled názvu předmětu
#XMSG
serializationChangeToastMsgUpdated2=Formát serializace změněn na JSON, protože registr schémat není povolen. Chcete-li změnit formát serializace zpět na AVRO, musíte nejprve povolit registr schémat.
#XBUT
confluentTopicNameInfo=Název tématu je vždy založen na názvu cílového objektu. Můžete jej změnit přejmenováním cílového objektu.
#XMSG
emptyRecordNameValidationHeaderMsg=Zadejte název záznamu.
#XMSG
emptyPartionHeader=Zadejte počet oddílů.
#XMSG
invalidPartitionsHeader=Zadejte platný počet oddílů.
#XMSG
invalidpartitionsDesc=Zadejte číslo mezi 1 a 200000.
#XMSG
emptyrFactorHeader=Zadejte koeficient replikace.
#XMSG
invalidrFactorHeader=Zadejte platný koeficient replikace.
#XMSG
invalidrFactorDesc=Zadejte číslo mezi 1 a 32,767.
#XMSG
emptyRecordNameValidationDescMsg=Používá-li se formát serializace "AVRO", jsou podporovány jen tyto znaky:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(podtržítko)
#XMSG
validRecordNameValidationHeaderMsg=Zadejte platný název záznamu.
#XMSG
validRecordNameValidationDescMsgUpdated=Protože se používá formát serializace "AVRO", název záznamu musí obsahovat pouze alfanumerické znaky (AZ, az, 0-9) a podtržítka (_). Musí začínat písmenem nebo podtržítkem.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=“Počet vláken objektu pro delta zavedení” lze nastavit, jakmile má jeden nebo více objektů typ zavedení “Iniciální a Delta”.
#XMSG
invalidTargetName=Neplatný název sloupce 
#XMSG
invalidTargetNameDesc=Název cílového sloupce musí sestávat jen z alfanumerických znaků (A-Z, a-z, 0-9) a podtržítek (_).
#XFLD
consumeOtherSchema=Použít jiné verze schématu
#XFLD
ignoreSchemamissmatch=Ignorovat nekonzistenci schémat
#XFLD
confleuntDatatruncation=Neúspěšné při oříznutí dat
#XFLD
isolationLevel=Úroveň izolace
#XFLD
confluentOffset=Počáteční bod
#XFLD
signavioGroupDeltaFilesByText=Nic
#XFLD
signavioFileFormatText=Parkety
#XFLD
signavioSparkCompatibilityParquetText=Ne
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=Ne

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Projekce
#XBUT
txtAdd=Přidat
#XBUT
txtEdit=Upravit
#XMSG
transformationText=Přidejte projekci pro nastavení filtru nebo mapování.
#XMSG
primaryKeyRequiredText=Vyberte primární klíč konfiguračním schématem.
#XFLD
lblSettings=Nastavení
#XFLD
lblTargetSetting={0}: nastavení cíle
#XMSG
@csvRF=Vyberte soubor, který obsahuje definici schématu, kterou chcete použít pro všechny soubory ve složce.
#XFLD
lblSourceColumns=Zdrojové sloupce
#XFLD
lblJsonStructure=Struktura JSON
#XFLD
lblSourceSetting={0}: nastavení zdroje
#XFLD
lblSourceSchemaSetting={0}: nastavení zdrojového schématu
#XBUT
messageSettings=Nastavení zprávy
#XFLD
lblPropertyTitle1=Vlastnosti objektu
#XFLD
lblRFPropertyTitle=Vlastnosti toku replikace
#XMSG
noDataTxt=Nejsou žádné sloupce k zobrazení.
#XMSG
noTargetObjectText=Není vybrán žádný cílový objekt.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Cílové sloupce
#XMSG
searchColumns=Hledat sloupce
#XTOL
cdcColumnTooltip=Sloupec pro zaznamenání delta
#XMSG
sourceNonDeltaSupportErrorUpdated=Zdrojový objekt nepodporuje zaznamenání delta.
#XMSG
targetCDCColumnAdded=Pro zaznamenání delta byly přidány 2 cílové sloupce.
#XMSG
deltaPartitionEnable=Limit počtu vláken v objektu pro delta zavedení přidán do nastavení zdroje.
#XMSG
attributeMappingRemovalTxt=Odstranění neplatných mapování, která nejsou pro nový cílový objekt podporována.
#XMSG
targetCDCColumnRemoved=2 cílové sloupce použité pro zaznamenání delta byly odebrány.
#XMSG
replicationLoadTypeChanged=Typ zavedení změněn na "Iniciální a delta".
#XMSG
sourceHDLFLoadTypeError=Změňte typ zavedení na "Iniciální a delta".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Pro replikování objektu ze zdrojového připojení typu SAP HANA Cloud, soubory datového jezera, do SAP Datasphere, musíte použít typ zavedení "Iniciální a delta".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Aktivovat zaznamenání delta.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Pro replikování objektu ze zdrojového připojení typu SAP HANA Cloud, soubory datového jezera, do SAP Datasphere, musíte aktivovat zaznamenání delta.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Změnit cílový objekt.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Cílový objekt nelze použít, protože zaznamenání delta je deaktivováno. Můžete buď přejmenovat cílový objekt (což umožňuje vytvořit nový objekt se zaznamenáním delta), nebo ho namapovat na existující objekt s aktivovaným zaznamenáním delta.
#XMSG
deltaPartitionError=Zadejte platný počet vláken objektu pro delta zavedení.
#XMSG
deltaPartitionErrorDescription=Zadejte hodnotu mezi 1 a 10.
#XMSG
deltaPartitionEmptyError=Zadejte počet vláken objektu pro delta zavedení.
#XFLD
@lblColumnDescription=Popis
#XMSG
@lblColumnDescriptionText1=Pro technické účely - zpracování duplicitních záznamů způsobených problémy při replikaci zdrojových objektů založených na ABAP, které nemají primární klíč.
#XFLD
storageType=Úložiště
#XFLD
skipUnmappedColLbl=Přeskočit nemapované sloupce
#XFLD
abapContentTypeLbl=Typ obsahu
#XFLD
autoMergeForTargetLbl=Sloučit data automaticky
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Všeobecně
#XFLD
lblBusinessName=Business název
#XFLD
lblTechnicalName=Technický název
#XFLD
lblPackage=Paket
#XFLD
statusPanel=Status běhu
#XBTN: Schedule dropdown menu
SCHEDULE=Naplánovat
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Upravit časový plán
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Odstranit časový plán
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Vytvořit  časový plán
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Neúspěšná kontrola ověření časového plánu
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Časový plán nelze vytvořit, protože replikační tok je právě nasazován.{0}Počkejte, než bude replikační tok nasazen.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Pro replikační toky, které obsahují objekty s typem zavedení "Iniciální a Delta", nelze vytvořit žádný plán.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Pro replikační toky, které obsahují objekty s typem zavedení "Iniciální a Delta/Pouze delta", nelze vytvořit žádný plán.
#XFLD : Scheduled popover
SCHEDULED=Naplánováno
#XFLD
CREATE_REPLICATION_TEXT=Vytvořte replikační tok
#XFLD
EDIT_REPLICATION_TEXT=Upravte replikační tok
#XFLD
DELETE_REPLICATION_TEXT=Odstraňte replikační tok
#XFLD
REFRESH_FREQUENCY=Frekvence
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Replikační tok nelze nasadit, protože existující časový plán {0} nepodportuje typ zavedení "Iniciální a Delta".{0}{0}Pro nasazení replikačního toku musíte nastavit typy zavedení všech objektů{0} na "Pouze iniciální". Alternativně můžete odstranit časový plán, nasadit replikašní běh {0} a poté spustit nový běh. Výsledkem je běh bez konce{0}, který podporuje i objekty s typem zavedení "Iniciální and Delta".
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Replikační tok nelze nasadit, protože existující časový plán {0} nepodportuje typ zavedení "Iniciální a Delta/Pouze delta".{0}{0}Pro nasazení replikačního toku musíte nastavit typy zavedení všech objektů{0} na "Pouze iniciální". Alternativně můžete odstranit časový plán, nasadit replikační běh {0} a poté spustit nový běh. Výsledkem je běh bez konce{0}, který podporuje i objekty s typem zavedení "Iniciální and Delta/Pouze delta".
#XMSG
SCHEDULE_EXCEPTION=Nezdařilo se získat detaily časového plánu
#XFLD: Label for frequency column
everyLabel=Každé
#XFLD: Plural Recurrence text for Hour
hoursLabel=Hodiny
#XFLD: Plural Recurrence text for Day
daysLabel=Dny
#XFLD: Plural Recurrence text for Month
monthsLabel=Měsíce
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuty
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Nezdařilo se získat informace o možnosti časového plánu.
#XFLD :Paused field
PAUSED=Pozastaveno
#XMSG
navToMonitoring=Otevřít v monitoru toků
#XFLD
statusLbl=Status
#XFLD
lblLastRunExecuted=Začátek posledního běhu
#XFLD
lblLastExecuted=Poslední běh
#XFLD: Status text for Completed
statusCompleted=Dokončeno
#XFLD: Status text for Running
statusRunning=Probíhá
#XFLD: Status text for Failed
statusFailed=Neúspěšné
#XFLD: Status text for Stopped
statusStopped=Zastaveno
#XFLD: Status text for Stopping
statusStopping=Zastavuje se
#XFLD: Status text for Active
statusActive=Aktivní
#XFLD: Status text for Paused
statusPaused=Pozastaveno
#XFLD: Status text for not executed
lblNotExecuted=Dosud nespuštěno
#XFLD
messagesSettings=Nastavení zpráv
#XTOL
@validateModel=Ověřovací zprávy
#XTOL
@hierarchy=Hierarchie
#XTOL
@columnCount=Počet sloupců
#XMSG
VAL_PACKAGE_CHANGED=Přiřadili jste tento objekt k paketu ''{1}''. Kliknutím na “Uložit” potvrdíte a ověříte tuto změnu. Uvědomte si, že toto přiřazení k paketu již nelze po uložení v tomto editoru vrátit.
#XMSG
MISSING_DEPENDENCY=Závislosti objektu ''{0}'' nelze vyřešit v paketu ''{1}''.
#XFLD
deltaLoadInterval=Interval delta zavedení
#XFLD
lblHour=Hodiny (0-24)
#XFLD
lblMinutes=Minuty (0-59)
#XMSG
maxHourOrMinErr=Zadejte hodnotu mezi 0 a {0}.
#XMSG
maxDeltaInterval=Maximální hodnota intervalu delta zavedení je 24 hodin.{0}Změňte odpovídajícím způsobem hodnotu minut nebo hodin.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Cesta cílového kontejneru
#XFLD
confluentSubjectName=Název subjektu
#XFLD
confluentSchemaVersion=Verze schématu
#XFLD
confluentIncludeTechKeyUpdated=Zahrnout technický klíč
#XFLD
confluentOmitNonExpandedArrays=Vynechat nerozbalená pole
#XFLD
confluentExpandArrayOrMap=Rozbalit pole nebo mapu
#XCOL
confluentOperationMapping=Mapování operace
#XCOL
confluentOpCode=Kód operace
#XFLD
confluentInsertOpCode=Vložit
#XFLD
confluentUpdateOpCode=Aktualizovat
#XFLD
confluentDeleteOpCode=Odstranit
#XFLD
expandArrayOrMapNotSelectedTxt=Není vybráno
#XFLD
confluentSwitchTxtYes=Ano
#XFLD
confluentSwitchTxtNo=Ne
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Chyba
#XTIT
executeWarning=Upozornění
#XMSG
executeunsavederror=Před spuštěním váš tok replikace uložte.
#XMSG
executemodifiederror=V toku replikace jsou neuložené změny. Uložte tok replikace.
#XMSG
executeundeployederror=Před spuštěním vašeho toku replikace ho musíte nasadit.
#XMSG
executedeployingerror=Počkejte, než se dokončí nasazení.
#XMSG
msgRunStarted=Běh byl spuštěn
#XMSG
msgExecuteFail=Nezdařilo se spustit tok replikace
#XMSG
titleExecuteBusy=Vyčkejte.
#XMSG
msgExecuteBusy=Připravujeme vaše data na spuštění toku replikace.
#XTIT
executeConfirmDialog=Upozornění
#XMSG
msgExecuteWithValidations=Tok replikace má chyby ověření. Spuštění toku replikace může vyústit v chybu.
#XMSG
msgRunDeployedVersion=Existují změny k nasazení. Bude spuštěna poslední nasazená verze toku replikace. Chcete pokračovat?
#XBUT
btnExecuteAnyway=Přesto spustit
#XBUT
btnExecuteClose=Zavřít
#XBUT
loaderClose=Zavřít
#XTIT
loaderTitle=Načítání
#XMSG
loaderText=Získávání detailů ze serveru
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Tok replikace do tohoto cílového připojení jiného typu než SAP nelze spustit,
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=protože pro tento měsíc není k dispozici odchozí objem.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Administrátor může zvýšit bloky výstupní prémie pro tohoto
#XMSG
premiumOutBoundRFAdminErrMsgPart2=tenanta zpřístupněním odchozího objemu pro tento měsíc.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Chyba
#XTIT
deployInfo=Informace
#XMSG
deployCheckFailException=Při nasazení došlo k výjimce
#XMSG
deployGBQFFDisabled=Nasazení replikačních toků s připojením cíle ke Google BigQuery momentálně není možné, protože provádíme údržbu této funkce.
#XMSG
deployKAFKAFFDisabled=Nasazení toků replikace s připojením cíle k Apache Kafka momentálně není možné, protože provádíme údržbu této funkce.
#XMSG
deployConfluentDisabled=Nasazení replikačních toků s připojením cíle k Confluent Kafka momentálně není možné, protože provádíme údržbu této funkce.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Pro následující cílové objekty jsou názvy tabulek záznamů delta již použity v jiných tabulkách v úložišti: {0} Než budete moci nasadit tok replikace, musíte tyto cílové objekty přejmenovat, abyste se ujistili, že přidružené názvy tabulek záznamů delta jsou jedinečné.
#XMSG
deployDWCSourceFFDisabled=Nasazení replikačních toků, které mají jako svůj zdroj SAP Datasphere, není momentálně možné, protože provádíme údržbu této funkce.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Nasazení replikačních toků, které obsahují jako zdrojové objekty lokální tabulky s aktivací delty, není momentálně možné, protože provádíme údržbu této funkce.
#XMSG
deployHDLFSourceFFDisabled=Nasazení replikačních toků, které mají jako svůj zdroj připojení typu SAP HANA Cloud, soubory datového jezera není momentálně možné, protože provádíme údržbu této funkce.
#XMSG
deployObjectStoreAsSourceFFDisabled=Nasazení replikačních toků, které mají jako zdroj poskytovatele cloudového úložiště, momentálně není možné.
#XMSG
deployConfluentSourceFFDisabled=Nasazení replikačních toků, které mají jako svůj zdroj Confluent Kafka, není momentálně možné, protože provádíme údržbu této funkce.
#XMSG
deployMaxDWCNewTableCrossed=Pro velké replikační toky není možné je "uložit a nasadit" v jednom kroku. Svůj replikační tok nejprve uložte a pak jej nasaďte.
#XMSG
deployInProgressInfo=Nasazení již probíhá.
#XMSG
deploySourceObjectInUse=Zdrojové objekty {0} se již používají v replikačních tocích {1}.
#XMSG
deployTargetSourceObjectInUse=Zdrojové objekty {0} se již používají v replikačních tocích {1}. Cílové objekty {2} se již používají v replikačních tocích {3}.
#XMSG
deployReplicationFlowCheckError=Chyba při ověřování replikačního toku: {0}
#XMSG
preDeployTargetObjectInUse=Cílové objekty {0} se již používají v replikačních tocích {1} a stejný cílový objekt nemůžete mít ve dvou různých tocích replikace. Vyberte jiný cílový objekt a zkuste to znovu.
#XMSG
runInProgressInfo=Replikační tok již probíhá
#XMSG
deploySignavioTargetFFDisabled=Nasazení replikačních toků, které mají jako svůj cíl SAP Signavio, není momentálně možné, protože provádíme údržbu této funkce.
#XMSG
deployHanaViewAsSourceFFDisabled=Nasazení replikačních toků, které mají pohledy jako zdrojové objekty, není pro vybrané připojení zdroje momentálně možné. Zkuste to znovu později. 
#XMSG
deployMsOneLakeTargetFFDisabled=Nasazení replikačních toků, které mají jako svůj cíl MS OneLake, není momentálně možné, protože provádíme údržbu této funkce.
#XMSG
deploySFTPTargetFFDisabled=Nasazení replikačních toků, které mají jako svůj cíl SFTP, není momentálně možné, protože provádíme údržbu této funkce.
#XMSG
deploySFTPSourceFFDisabled=Nasazení replikačních toků, které mají jako svůj zdroj SFTP, není momentálně možné, protože provádíme údržbu této funkce.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Technický název
#XFLD
businessNameInRenameTarget=Business název
#XTOL
renametargetDialogTitle=Přejmenovat cílový objekt
#XBUT
targetRenameButton=Přejmenovat
#XBUT
targetRenameCancel=Zrušit
#XMSG
mandatoryTargetName=Musíte zadat název.
#XMSG
dwcSpecialChar=_(podtržítko) je jediný přípustný zvláštní znak.
#XMSG
dwcWithDot=Název cílové tabulky se může skládat z latinských písmen, číslic, podtržítek (_) a teček (.). První znak musí být písmeno, číslo nebo podtržítko (nikoli tečka).
#XMSG
nonDwcSpecialChar=Přípustné zvláštní znaky jsou _(podtržítko) -(pomlčka) a .(tečka)
#XMSG
firstUnderscorePattern=Název objektu nesmí začínat _ (podtržítko).

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: Zobrazit příkaz SQL Vytvořit tabulku
#XMSG
sqlDialogMaxPKWarning=V Google BigQuery je podporováno maximálně 16 primárních klíčů a zdrojový objekt má větší počet. Proto nejsou v tomto příkazu definovány žádné primární klíče.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Jeden nebo více zdrojových sloupců má datové typy, které nelze v Google BigQuery definovat jako primární klíče. Proto nejsou v tomto případě definovány žádné primární klíče. V Google BigQuery mohou mít primární klíč jen následující datové typy: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Kopírovat a zavřít
#XBUT
closeDDL=Zavřít
#XMSG
copiedToClipboard=Zkopírováno do schránky


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Objekt ''{0}'' nemůže být součástí řetězce úloh, protože nemá konec (protože obsahuje objekty s typem načtení Iniciální a Delta/Pouze delta).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Objekt ''{0}'' nemůže být součástí řetězce úloh, protože nemá konec (protože obsahuje objekty s typem načtení Iniciální a Delta).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objekt "{0}" nelze přidat k řetězci úloh.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Existují neuložené cílové objekty. Proveďte uložení znovu.{0}{0} Chování této funkce se změnilo: V minulosti byly cílové objekty vytvářeny v cílovém prostředí, jen když byl replikační tok nasazen.{0} Nyní jsou objekty vytvořeny, již když je replikační tok uložen. Váš replikační tok byl vytvořen před touto změnou a obsahuje nové objekty.{0} Před jeho nasazením je třeba znovu uložit replikační tok, aby byly nové objekty správně zahrnuty.
#XMSG
confirmChangeContentTypeMessage=Chystáte se změnit typ připojení. Pokud to uděláte, všechny existující projekce budou odstraněny.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Název subjektu
#XFLD
schemaDialogVersionName=Verze schématu
#XFLD
includeTechKey=Zahrnout technický klíč
#XFLD
segementButtonFlat=Plochá
#XFLD
segementButtonNested=Vnořeno
#XMSG
subjectNamePlaceholder=Hledat název subjektu

#XMSG
@EmailNotificationSuccess=Konfigurace e-mailových oznámení během je uložena.

#XFLD
@RuntimeEmailNotification=E-mailové oznámení v době běhu

#XBTN
@TXT_SAVE=Uložit


