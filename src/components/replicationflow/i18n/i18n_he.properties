#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=תזרים שכפול

#XFLD: Edit Schema button text
editSchema=ערוך תרשים

#XTIT : Properties heading
configSchema=קבע תצורה לתרשים

#XFLD: save changed button text
applyChanges=החל שינויים


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=בחר חיבור מקור
#XFLD
sourceContainernEmptyText=בחר מכולה
#XFLD
targetConnectionEmptyText=בחר חיבור יעד
#XFLD
targetContainernEmptyText=בחר מכולה
#XFLD
sourceSelectObjectText=בחר אובייקט מקור
#XFLD
sourceObjectCount=אובייקטי מקור ({0})
#XFLD
targetObjectText=אובייקטי יעד
#XFLD
confluentBrowseContext=בחר הקשר
#XBUT
@retry=נסה שוב
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=שדרוג דייר בתהליך.

#XTOL
browseSourceConnection=חפש חיבור מקור
#XTOL
browseTargetConnection=חפש חיבור יעד
#XTOL
browseSourceContainer=חפש מכולת מקור
#XTOL
browseAndAddSourceDataset=הוסף אובייקטי מקור
#XTOL
browseTargetContainer=חפש מכולת יעד
#XTOL
browseTargetSetting=עיין בהגדרות יעד
#XTOL
browseSourceSetting=עיין בהגדרות מקור
#XTOL
sourceDatasetInfo=מידע
#XTOL
sourceDatasetRemove=הסר
#XTOL
mappingCount=זה מייצג את המספר הכולל של ביטויים/מיפויים שאינם מבוססי שם.
#XTOL
filterCount=זה מייצג את המספר הכולל של תנאי סינון.
#XTOL
loading=טוען...
#XCOL
deltaCapture=לכידת דלתא
#XCOL
deltaCaptureTableName=טבלת לכידת דלתא
#XCOL
loadType=טען סוג
#XCOL
deleteAllBeforeLoading=מחק הכול לפני טעינה
#XCOL
transformationsTab=תכנונים
#XCOL
settingsTab=הגדרות

#XBUT
renameTargetObjectBtn=שנה שם של אובייקט יעד
#XBUT
mapToExistingTargetObjectBtn=מפה לאובייקט יעד קיים
#XBUT
changeContainerPathBtn=שנה נתיב מכולה
#XBUT
viewSQLDDLUpdated=הצג משפט SQL 'צור טבלה'
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=אובייקט המקור אינו תומך בלכידת דלתא, אך אובייקט היעד הנבחר מכיל את אפשרות לכידת הדלתא מופעלת.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=לא ניתן להשתמש באובייקט היעד כי לכידת דלתא מופעלת, {0}בעוד שאובייקט המקור אינו תומך בלכידת דלתא.{1}באפשרותך לבחור אובייקט יעד אחר שאינו תומך בלכידת דלתא.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=אובייקט יעד בשם זה כבר קיים. עם זאת, לא ניתן להשתמש בו {0}כי לכידת דלתא מופעלת, בעוד שאובייקט המקור {0}אינו תומך בלכידת דלתא. {1}באפשרותך להזין את השם של אובייקט יעד קיים שאינו {0}תומך בלכידת דלתא, או להזין שם שעדיין לא קיים.
#XBUT
copySQLDDLUpdated=העתק משפט SQL 'צור טבלה'
#XMSG
targetObjExistingNoCDCColumnUpdated=טבלאות קיימות ב- Google BigQuery חייבות לכלול את העמודות הבאות עבור לכידת נתוני שינוי (CDC)‏(CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=אובייקטי המקור הבאים אינם נתמכים מכיוון שאין להם מפתח ראשי, או שהם משתמשים בחיבור שאינו עומד בתנאים לאחזור המפתח הראשי:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=עיין ב-SAP KBA 3531135 לפתרון אפשרי.
#XLST: load type list values
initial=התחלתי בלבד
@emailUpdateError=שגיאה בעדכון רשימת הודעות דוא"ל

#XLST
initialDelta=התחלתי ודלתא

#XLST
deltaOnly=דלתא בלבד
#XMSG
confluentDeltaLoadTypeInfo=עבור מקור Confluent Kafka, רק תחילית ודלתא של סוג טעינה נתמכים.
#XMSG
confirmRemoveReplicationObject=האם הנך מאשר שברצונך למחוק את השכפול הנבחר?
#XMSG
confirmRemoveReplicationTaskPrompt=פעולה זו תמחק שכפולים קיימים. האם ברצונך להמשיך?
#XMSG
confirmTargetConnectionChangePrompt=פעולה זו תאפס את חיבור היעד, מכולת היעד ותמחק את כל אובייקטי היעד. האם ברצונך להמשיך?
#XMSG
confirmTargetContainerChangePrompt=פעולה זו תאפס את מכולת היעד ותמחק את כל אובייקטי היעד. האם ברצונך להמשיך?
#XMSG
confirmRemoveTransformObject=האם אתה מאשר שברצונך למחוק את התכנון {0}?
#XMSG
ErrorMsgContainerChange=אירעה שגיאה במהלך שינוי נתיב המכולה.
#XMSG
infoForUnsupportedDatasetNoKeys=אובייקטי המקור הבאים אינם נתמכים משום שאין להם מפתח ראשי:
#XMSG
infoForUnsupportedDatasetView=אובייקטי המקור הבאים מסוג 'תצוגות' אינם נתמכים.
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=אובייקט המקור הבא לא נתמך מכיוון שהוא תצוגת SQL שמכילה פרמטרי קלט:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=אובייקטי המקור הבאים אינם נתמכים משום ששליפה מושבתת עבורם:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=לחיבורי Confluent פורמטי הסדרות המותרים היחידים הם AVRO ו-JSON. האובייקטים הבאים אינם נתמכים מכיוון שהם משתמשים בפורמט סדרה אחר:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=לא ניתן להביא את התרשים עבור האובייקטים הבאים. בחר את ההקשר המתאים או אמת את תצורת הרישום של הסכמה
#XTOL: warning dialog header on deleting replication task
deleteHeader=מחק
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=הגדרת 'מחק הכול לפני טעינה' לא נתמכת עבור Google BigQuery.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=הגדרת 'מחק הכול לפני' מוחקת ויוצרת מחדש את האובייקט (נושא) לפני כל שכפול. פעולה זו גם מוחקת את כל ההודעות שהוקצו.
#XTOL
DeleteAllBeforeLoadingLTFInfo=הגדרת 'מחק הכול לפני' לא נתמכת עבור סוג היעד הזה.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=שם טכני
#XCOL
connBusinessName=שם עסקי
#XCOL
connDescriptionName=תיאור
#XCOL
connType=סוג
#XMSG
connTblNoDataFoundtxt=לא נמצאו חיבורים
#XMSG
connectionError=אירעה שגיאה במהלך הבאת החיבורים.
#XMSG
connectionCombinationUnsupportedErrorTitle=שילוב חיבור אינו נתמך
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=שכפול מ-{0} ל-{1} אינו נתמך כרגע.
#XMSG
invalidTargetforSourceHDLFErrorTitle=שילוב סוג החיבור אינו נתמך
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=שכפול מחיבור עם סוג החיבור SAP HANA Cloud, קובצי אגם נתונים ל-{0} אינו נתמך. ניתן לשכפל אל SAP Datasphere בלבד.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=בחר
#XBUT
containerCancelBtn=בטל
#XTOL
containerSelectTooltip=בחר
#XTOL
containerCancelTooltip=בטל
#XMSG
containerContainerPathPlcHold=נתיב מכולה
#XFLD
containerContainertxt=מכולה
#XFLD
confluentContainerContainertxt=הקשר
#XMSG
infoMessageForSLTSelection=זיהוי העברה מסיבית/SLT בלבד מותרים כמכולה. בחר זיהוי העברה מסיבית תחת SLT (אם זמין) ולחץ על 'הגש'.
#XMSG
msgFetchContainerFail=אירעה שגיאה במהלך הבאת נתוני מכולה.
#XMSG
infoMessageForSLTHidden=חיבור זה לא תומך בתיקיות SLT, משום כך הן אינן מופיעות ברשימה שלמטה.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=בחר מכולה שמכילה תיקיות משנה.
#XMSG
sftpIncludeSubFolderText=שקר
#XMSG
sftpIncludeSubFolderTextNew=לא

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(אין עדיין מיפוי מסנן)
#XMSG
failToFetchRemoteMetadata=אירעה שגיאה במהלך הבאת מטה-נתונים.
#XMSG
failToFetchData=אירעה שגיאה במהלך הבאת היעד הקיים.
#XCOL
@loadType=טען סוג
#XCOL
@deleteAllBeforeLoading=מחק הכול לפני טעינה

#XMSG
@loading=טוען...
#XFLD
@selectSourceObjects=בחר אובייקטי מקור
#XMSG
@exceedLimit=לא ניתן ליבא יותר מ-{0} אובייקטים בבת אחת. בטל את הבחירה ב-{1} אובייקטים לפחות.
#XFLD
@objects=אובייקטים
#XBUT
@ok=OK
#XBUT
@cancel=בטל
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=הבא
#XBUT
btnAddSelection=הוסף בחירה
#XTOL
@remoteFromSelection=הסר מבחירה
#XMSG
@searchInForSearchField=חפש ב-{0}

#XCOL
@name=שם טכני
#XCOL
@type=סוג
#XCOL
@location=מיקום
#XCOL
@label=שם עסקי
#XCOL
@status=סטאטוס

#XFLD
@searchIn=חפש ב:
#XBUT
@available=זמין
#XBUT
@selection=בחירה

#XFLD
@noSourceSubFolder=טבלאות ותצוגות
#XMSG
@alreadyAdded=כבר מופיע בדיאגרמה
#XMSG
@askForFilter=קיימים יותר מ-{0} פריטים. הזן את מחרוזת המסנן כדי לצמצם את מספר הפריטים.
#XFLD: success label
lblSuccess=הצלחה
#XFLD: ready label
lblReady=מוכן
#XFLD: failure label
lblFailed=נכשל
#XFLD: fetching status label
lblFetchingDetail=שולף נתונים

#XMSG Place holder text for tree filter control
filterPlaceHolder=הקלד טקסט כדי לסנן אובייקטים ברמה עליונה
#XMSG Place holder text for server search control
serverSearchPlaceholder=הקלד והקש Enter כדי לחפש
#XMSG
@deployObjects=מייבא {0} אובייקטים...
#XMSG
@deployObjectsStatus=מספר האובייקטים שיובא: {0}. מספר האובייקטים שלא ניתן היה לייבא: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=הניסיון לפתוח את דפדפן המאגר המקומי נכשל.
#XMSG
@openRemoteSourceBrowserError=הניסיון להביא את אובייקטי המקור נכשל.
#XMSG
@openRemoteTargetBrowserError=הניסיון להביא את אובייקטי היעד נכשל.
#XMSG
@validatingTargetsError=אירעה שגיאה במהלך בדיקת תקינות של יעדים.
#XMSG
@waitingToImport=מוכן ליבוא

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=בוצעה חריגה מהמספר המקסימלי של אובייקטים. בחר 500 אובייקטים מקסימום עבור תזרים שכפול אחד.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=שם טכני
#XFLD
sourceObjectBusinessName=שם עסקי
#XFLD
sourceNoColumns=מספר עמודות
#XFLD
containerLbl=מכולה

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=עליך לבחור חיבור מקור עבור תזרים השכפול.
#XMSG
validationSourceContainerNonExist=עליך לבחור מכולה עבור חיבור המקור.
#XMSG
validationTargetNonExist=עליך לבחור חיבור יעד עבור תזרים השכפול.
#XMSG
validationTargetContainerNonExist=עליך לבחור מכולה עבור חיבור היעד.
#XMSG
validationTruncateDisabledForObjectTitle=שכפול לאחסוני אובייקט.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=שכפול לאחסון בענן אפשרי רק אם אפשרות ''מחק הכול לפני טעינה'' מוגדרת או שאובייקט היעד אינו קיים ביעד.{0}{0} כדי לאפשר שכפול עדיין עבור אובייקטים שעבורם אפשרות ''מחק הכול לפני טעינה'' אינה מוגדרת, ודא שאובייקט היעד אינו קיים במערכת לפני שתפעיל את תזרים השכפול.
#XMSG
validationTaskNonExist=חייב להיות לך שכפול אחד לפחות בתזרים השכפול.
#XMSG
validationTaskTargetMissing=חייב להיות לך יעד עבור השכפול עם המקור: {0}
#XMSG
validationTaskTargetIsSAC=יעד נבחר הוא מכשיר SAC: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=היעד שנבחר אינו טבלה מקומית נתמכת: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=אובייקט בשם זה כבר קיים ביעד. עם זאת, לא ניתן להשתמש באובייקט זה כאובייקט יעד עבור זרימת שכפול למאגר המקומי, מכיוון שהוא אינו טבלה מקומית.
#XMSG
validateSourceTargetSystemDifference=עליך לבחור חיבור מקור ויעד אחרים ושילובי מכולה עבור תזרים השכפול.
#XMSG
validateDuplicateSources=לשכפול אחד או יותר יש שמות אובייקטי מקור כפולים: {0}.
#XMSG
validateDuplicateTargets=לשכפול אחד או יותר יש שמות אובייקטי יעד כפולים: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=אובייקט המקור {0} אינו תומך בלכידת דלתא, כאשר אובייקט היעד {1} כן. עליך להסיר את השכפול.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=עליך לבחור את סוג הטעינה "התחלתי ודלתא" עבור השכפול עם שם אובייקט היעד {0}.
#XMSG
validationAutoRenameTarget=שמות עמודות יעד שונו.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=תכנון אוטומטי נוסף ושמות עמודות היעד הבאות שונו כדי לאפשר שכפול ליעד:{1}{1} {0} {1}{1}הדבר קרה בגלל אחת הסיבות הבאות:{1}{1}{2} תווים לא נתמכים{1}{2} תחילית משוריינת
#XMSG
validationAutoRenameTargetDescriptionUpdated=תכנון אוטומטי נוסף ושמות עמודות היעד הבאות שונו כדי לאפשר שכפול ל- Google BigQuery:{1}{1} {0} {1}{1}הדבר קרה בגלל אחת הסיבות הבאות:{1}{1}{2} שם עמודה משוריין{1}{2} תווים לא נתמכים{1}{2} תחילית משוריינת
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=תכנון אוטומטי נוסף ושמות עמודות היעד הבאות שונו כדי לאפשר שכפול ל- Confluent:{1}{1} {0} {1}{1}הדבר קרה בגלל אחת הסיבות הבאות:{1}{1}{2} שם עמודה משוריין{1}{2} תווים לא נתמכים{1}{2} תחילית משוריינת
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=תכנון אוטומטי נוסף ושמות עמודות היעד הבאות שונו כדי לאפשר שכפול ליעד:{1}{1} {0} {1}{1}הדבר קרה בגלל אחת הסיבות הבאות:{1}{1}{2} שם עמודה משוריין{1}{2} תווים לא נתמכים{1}{2} תחילית משוריינת
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=שם אובייקט היעד שונה.
#XMSG
autoRenameInfoDesc=שם אובייקט היעד שונה מכיוון שהוא הכיל תווים לא נתמכים. רק התווים הבאים נתמכים:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(נקודה){0}{1}_(קו תחתון){0}{1}-(מקף)
#XMSG
validationAutoTargetTypeConversion=סוגים של נתוני יעד השתנו.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=עבור עמודות היעד הבאות, סוגי נתוני היעד השתנו, כיוון שסוגי נתוני המקור לא נתמכים ב-Google BigQuery‏:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=עבור עמודות היעד הבאות, סוגי נתוני היעד השתנו, כיוון שסוגי נתוני המקור לא נתמכים בחיבור היעד:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=קצר שמות של עמודות יעד.
#XMSG
validationMaxCharLengthGBQTargetDescription=שמות עמודות של Google BigQuery, יכולים להכיל עד 300 תווים. השתמש בתכנון כדי לקצר את שמות עמודות היעד הבאים:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=לא ניתן ליצוא מפתחות ראשיים.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=ב-Google BigQuery,עד 16 מפתחות ראשיים נתמכים, אולם אובייקט המקור מכיל מספר גדול יותר של מפתחות ראשיים. אף מפתח ראשי לא ייווצר באובייקט יעד.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=עמודת מקור אחת או יותר כוללות סוגי נתונים שאותם לא ניתן להגדיר כמפתחות ראשיים ב-Google BigQuery. אף מפתח ראשי לא יווצר באובייקט היעד.{0}{0}סוגי הנתונים הבאים של יעד תואמים לסוגי נתונים של Google BigQuery שעבורם ניתן להגדיר מפתח ראשי:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=הגדר עמודה אחת או יותר כמפתח ראשי.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=עליך להגדיר עמודה אחת או יותר כמפתח עיקרי. השתמש בדיאלוג תרשים המקור כדי לעשות זאת.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=הגדר עמודה אחת או יותר כמפתח ראשי.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=חובה להגדיר עמודה אחת או יותר כמפתח ראשי שתואם לאילוצי המפתח הראשי עבור אובייקט המקור שלך. כדי לעשות זאת, עבור ל'קבע תצורה של סכמה' בתכונות אובייקט המקור. 
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=הזן ערך מחיצה מקסימלי חוקי.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=ערך מחיצה מקסימלי חייב להיות >= 1 ו-= 2147483647
#XMSG
validateHDLFNoPKDatasetError=הגדר עמודה אחת או יותר כמפתח ראשי.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=כדי לשכפל אובייקט, עליך להגדיר עמודת יעד אחת או יותר כמפתח ראשי. השתמש בתכנון כדי לבצע את הפעולה.
#XMSG
validateHDLFNoPKExistingDatasetError=הגדר עמודה אחת או יותר כמפתח ראשי.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=כדי לשכפל נתונים לאובייקט יעד קיים, עליו להיות בעל עמודה אחת או יותר המוגדרות כמפתח ראשי. {0} יש לך את האפשרויות הבאות להגדרת עמודה אחת או יותר כמפתח הראשי: {0} {1} השתמש בעורך הטבלה המקומי כדי לשנות את אובייקט היעד הקיים. לאחר מכן טען מחדש את תזרים השכפול.{0}{1}שנה את שם אובייקט היעד בתזרים השכפול. פעולה זו תיצור אובייקט חדש ברגע שהריצה מתחילה. לאחר שינוי השם, ניתן להגדיר עמודה אחת או יותר כמפתח הראשי בתכנון.{0}{1} מפה את האובייקט לאובייקט יעד קיים אחר שבו עמודה אחת או יותר כבר מוגדרות כמפתח ראשי.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=היעד שנבחר כבר קיים במאגר: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=לכידת דלתא כבר נמצאים בשימוש על-ידי טבלאות אחרות במאגר: {0}. עליך לשנות את השם של אובייקטי היעד האלה כדי לוודא ששמות הטבלאות לכידת דלתא ייחודיים לפני שתוכל לשמור את תזרים השכפולים.
#XMSG
validateConfluentEmptySchema=הגדר תרשים
#XMSG
validateConfluentEmptySchemaDescUpdated=לטבלת המקור אין תרשים. בחר תרשים תצורה כדי להגדיר תרשים.
#XMSG
validationCSVEncoding=קידוד CSV לא חוקי
#XMSG
validationCSVEncodingDescription=קידוד CSV של משימה אינו חוקי.
#XMSG
validateConfluentEmptySchema=בחר סוג נתוני יעד תואמים
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=בחר סוג נתוני יעד תואמים
#XMSG
globalValidateTargetDataTypeDesc=אירעה שגיאה במיפויי העמודה. עבור אל החיזוי וודא שכל עמודות המקור ממופות בעמודה ייחודית, עם עמודה מסוג נתונים מתאים ושכל הביטויים המוגדרים חוקיים.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=שכפל שמות עמודה.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=שמות עמודות כפולים אינם נתמכים. השתמש בדיאלוג התכנון כדי לתקן אותם. לאובייקטי היעד הבאים יש שמות עמודות כפולים: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=שכפל שמות עמודה.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=שמות עמודות כפולים אינם נתמכים. לאובייקטי היעד הבאים יש שמות עמודות כפולים: {0}.
#XMSG
deltaOnlyLoadTypeTittle=ייתכנו מקרים של חוסר עקביות בנתונים.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=סוג הטעינה 'דלתא בלבד' לא יביא בחשבון את השינויים שבוצעו במקור בין השמירה האחרונה להפעלה הבאה.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=שנה את סוג הטעינה ל'התחלתית'.
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=שכפול אובייקטים מבוססי ABAP שאין להם מפתח עיקרי אפשרי רק עבור סוג טעינה 'התחלתית בלבד'.
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=השבת לכידת דלתא.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=כדי לשכפל אובייקט שאין לו מפתח עיקרי באמצעות סוג החיבור ABAP עליך קודם כל להשבית את לכידת הדלתא עבור טבלה זו.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=שנה אובייקט יעד.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=לא ניתן להשתמש באובייקט היעד משום שלכידת דלתא מופעלת. אפשר לשנות את שם אובייקט היעד ואז לכבות את לכידת הדלתא עבור האובייקט החדש (ששונה שמו) או למפות את אובייקט המקור לאובייקט יעד שעבורו לכידת דלתא מושבתת.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=שנה אובייקט יעד.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=לא ניתן להשתמש באובייקט היעד מכיוון שאין לו העמודה הטכנית הנדרשת __load_package_id. ניתן לשנות את שם אובייקט היעד באמצעות שם שאינו קיים עדיין. המערכת יוצרת אובייקט חדש שיש לו אותה הגדרה כמו אובייקט המקור ומכיל את העמודה הטכנית. לחלופין ניתן למפות את אובייקט היעד לאובייקט קיים שיש לו העמודה הטכנית הנדרשת (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=שנה אובייקט יעד.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=לא ניתן להשתמש באובייקט היעד מכיוון שאין לו העמודה הטכנית הנדרשת __load_record_id. ניתן לשנות את שם אובייקט היעד באמצעות שם שאינו קיים עדיין. המערכת יוצרת אובייקט חדש שיש לו אותה הגדרה כמו אובייקט המקור ומכיל את העמודה הטכנית. לחלופין ניתן למפות את אובייקט היעד לאובייקט קיים שיש לו העמודה הטכנית הנדרשת (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=שנה אובייקט יעד.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=לא ניתן להשתמש באובייקט היעד מכיוון שסוג הנתונים של העמודה הטכנית שלו __load_record_id אינו "string(44)". ניתן לשנות את שם אובייקט היעד באמצעות שם שאינו קיים עדיין. המערכת יוצרת אובייקט חדש שיש לו אותה הגדרה כמו אובייקט המקור ומכאן את סוג הנתונים הנכון. לחלופין ניתן למפות את אובייקט היעד לאובייקט קיים שיש לו העמודה הטכנית הנדרשת (__load_record_id) עם סוג הנתונים הנכון.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=שנה אובייקט יעד.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=לא ניתן להשתמש באובייקט היעד מכיוון שיש לו מפתח עיקרי ואילו לאובייקט המקור אין כלל. ניתן לשנות את שם אובייקט היעד באמצעות שם שאינו קיים עדיין. המערכת יוצרת אובייקט חדש שיש לו אותה הגדרה כמו אובייקט המקור ומכאן אין לו מפתח עיקרי. לחלופין ניתן למפות את אובייקט היעד לאובייקט קיים שיש לו העמודה הטכנית הנדרשת (__load_package_id) ואין לו מפתח עיקרי.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=שנה אובייקט יעד.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=לא ניתן להשתמש באובייקט היעד מכיוון שיש לו מפתח עיקרי ואילו לאובייקט המקור אין כלל. ניתן לשנות את שם אובייקט היעד באמצעות שם שאינו קיים עדיין. המערכת יוצרת אובייקט חדש שיש לו אותה הגדרה כמו אובייקט המקור ומכאן אין לו מפתח עיקרי. לחלופין ניתן למפות את אובייקט היעד לאובייקט קיים שיש לו העמודה הטכנית הנדרשת (__load_record_id) ואין לו מפתח עיקרי.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=שנה אובייקט יעד.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=לא ניתן להשתמש באובייקט היעד מכיוון שסוג הנתונים של העמודה הטכנית שלו __load_package_id אינו "binary(>=256)". ניתן לשנות את שם אובייקט היעד באמצעות שם שאינו קיים עדיין. המערכת יוצרת אובייקט חדש שיש לו אותה הגדרה כמו אובייקט המקור ומכאן את סוג הנתונים הנכון. לחלופין ניתן למפות את אובייקט היעד לאובייקט קיים שיש לו העמודה הטכנית הנדרשת (__load_package_id) עם סוג הנתונים הנכון.
#XMSG
validationAutoRenameTargetDPID=שמות עמודות יעד שונו.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=הסר את אובייקט המקור.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=לאובייקט המקור אין עמודת מפתח, דבר שאינו נתמך בהקשר זה.
#XMSG
validationAutoRenameTargetDPIDDescription=תכנון אוטומטי נוסף ושמות עמודות היעד הבאות שונו כדי לאפשר שכפול ממקור ABAP ללא מפתחות:{1}{1} {0} {1}{1}הדבר קרה בגלל אחת הסיבות הבאות:{1}{1}{2} שם עמודה משוריין{1}{2} תווים לא נתמכים{1}{2} תחילית משוריינת
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=שכפול ל-{0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=שמירה ופריסת תזרימי שכפול שיעדן {0} לא אפשריות כרגע כיוון שאנחנו מבצעים אחזקה על פונקציה זו.
#XMSG
TargetColumnSkippedLTF=המערכת דילוגה על עמודת יעד.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=המערכת דילוגה על עמודת היעד עקב סוג נתונים לא נתמך. {0}{1}
#XMSG
validatePKTimeColumnLTF1=עמודת זמן כמפתח ראשי.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=לאובייקט המקור יש עמודת זמן כמפתח ראשי, שאינה נתמכת בהקשר זה.
#XMSG
validateNoPKInLTFTarget=מפתח ראשי חסר.
#XMSG
validateNoPKInLTFTargetDescription=מפתח ראשי אינו מוגדר ביעד, והוא אינו נתמך בהקשר זה.
#XMSG
validateABAPClusterTableLTF=טבלת אשכול ABAP.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=אובייקט המקור הינו בטבלת אשכול ABAP, דבר שאינו נתמך בהקשר זה.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=נראה שעוד לא הוספת נתונים.
#YINS
welcomeText2=כדי להתחיל בתזרים השכפול שלך, בחר חיבור ואובייקט מקור בצד שמאל.

#XBUT
wizStep1=בחר חיבור מקור
#XBUT
wizStep2=בחר מכולת מקור
#XBUT
wizStep3=הוסף אובייקטי מקור

#XMSG
limitDataset=המספר המקסימלי של אובייקטים הושג. הסר אובייקטים קיימים כדי להוסיף חדשים, או כדי ליצור תזרים שכפול חדש.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=לא ניתן להתחיל את תזרים השכפולים של חיבור יעד שאינו של SAP מכיוון שאין נפח יוצא זמין לחודש זה.
#XMSG
premiumOutBoundRFAdminWarningMsg=מנהל יכול להגדיל את הבלוקים היוצאים של הפרמייה עבור דייר זה, ובכך להפוך את הנפח היוצא לזמין עבור חודש זה.
#XMSG
messageForToastForDPIDColumn2=נוספה עמודה חדשה ליעד עבור {0} אובייקטים - נצרכים לטיפול בקשומות כפולות בחיבור עם אובייקטי מקור מבוססי ABAP שאין להם מפתח עיקרי.
#XMSG
PremiumInboundWarningMessage=בהתאם למספר של תזרימי השכפול ונפח הנתונים לשכפול, משאבי SAP HANA {0} שנדרשים לשכפול נתונים דרך {1} עשויים לחרוג מהקיבולת הזמינה לדייר שלך.
#XMSG
PremiumInboundWarningMsg=בהתאם למספר של תזרימי השכפול ונפח הנתונים לשכפול, משאבי SAP HANA {0} שנדרשים לשכפול נתונים דרך ''{1}'' עשויים לחרוג מהקיבולת הזמינה לדייר שלך.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=הזן שם תכנון.
#XMSG
emptyTargetColumn=עליך להזין שם עמודת יעד.
#XMSG
emptyTargetColumnBusinessName=עליך להזין שם עמודת שם עסקי.
#XMSG
invalidTransformName=הזן שם תכנון.
#XMSG
uniqueColumnName=שנה שם של עמודת יעד.
#XMSG
copySourceColumnLbl=העתק עמודות מאובייקט מקור
#XMSG
renameWarning=הקפד לבחור שם ייחודי בזמן שינוי שם טבלת היעד. אם הטבלה עם השם החדש כבר קיימת במרחב, היא תשתמש בהגדרה של אותה טבלה.

#XMSG
uniqueColumnBusinessName=שנה את השם עסקי של עמודת יעד. 
#XMSG
uniqueSourceMapping=בחר עמודת מקור אחרת. 
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=עמודת המקור {0} כבר משמשת את עמודות היעד הבאות:{1}{1}{2}{1}{1} עבור עמודת יעד זו או עבור עמודות היעד האחרות, בחר עמודת מקור שאינה כבר בשימוש כדי לשמור את ההטלה.
#XMSG
uniqueColumnNameDescription=שם עמודת היעד שהזנת כבר קיים. כדי שתוכל לשמור את התכנון עליך להזין שם עמודה ייחודי.
#XMSG
uniqueColumnBusinessNameDesc=השם העסקי של עמודת היעד שהזנת כבר קיים. כדי לשמור את התכנון עליך להזין שם עסקי של עמודה ייחודי.
#XMSG
emptySource=בחר עמודת מקור או הזן קבוע.
#XMSG
emptySourceDescription=כדי ליצור הזנת מיפוי חוקית עליך לבחור עמודת מקור או להזין ערך קבוע.
#XMSG
emptyExpression=הגדר מיפוי.
#XMSG
emptyExpressionDescription1=בחר עמודת מקור שאליה ברצונך למפות את עמודת היעד, או סמן את תיבת הסימון בעמודה {0} פונקציות / קבועים {1}. {2} {2} פונקציות מוזנות באופן אוטומטי בהתאם לסוג נתוני היעד.ערכים קבועים ניתנים להזנה באופן ידני.
#XMSG
numberExpressionErr=הזן מספר.
#XMSG
numberExpressionErrDescription=בחרת את סוג הנתונים המספרי. פירוש הדבר שניתן להזין מספרים בלבד ונקודה עשרונית אם יש צורך בכך. אין להשתמש בגרשים.
#XMSG
invalidLength=הזן ערך אורך חוקי.
#XMSG
invalidLengthDescription=אורך סוג הנתונים חייב להיות שווה לאורך עמודת המקור או שווה לו ויכול להיות בין 1 ל-5000.
#XMSG
invalidMappedLength=הזן ערך אורך חוקי.
#XMSG
invalidMappedLengthDescription=אורך סוג הנתונים חייב להיות שווה לאורך עמודת המקור {0} או שווה לו ויכול להיות בין 1 ל-5000.
#XMSG
invalidPrecision=הזן ערך דיוק חוקי.
#XMSG
invalidPrecisionDescription=הדיוק מגדיר את המספר הכולל של ספרות. הסולם מגדיר את מספר הספרות לאחר הנקודה העשרונית והוא יכול להיות בין 0 ומדויק. {0}{0} דוגמאות: {0}{1} דיוק 6, סולם 2 מתאים למספרים כמו 1234.56{0}{1} דיוק 6, סולם 6 מתאים למספרים כמו 0.123456.{0} {0} דיוק וסולם עבור היעד חייב להיות תואם לדיוק ולסולם עבור המקור כך שכל הספרות מהמקור יתאימו בשדה היעד. לדוגמה, אם הדיוק הוא 6 והסולם 2 במקור (וכתוצאה מכך ספרות חוץ מ-0 לפני הנקודה העשרונית), לא יוכלו להיות דיוק 6 וסולם 6 ביעד.
#XMSG
invalidPrimaryKey=הזן מפתח עיקרי אחד לפחות.
#XMSG
invalidPrimaryKeyDescription=מפתח עיקרי לא הוגדר עבור תרשים זה.
#XMSG
invalidMappedPrecision=הזן ערך דיוק חוקי.
#XMSG
invalidMappedPrecisionDescription1=הדיוק מגדיר את המספר הכולל של ספרות. הסולם מגדיר את מספר הספרות לאחר הנקודה העשרונית ויכול להיות בין 0 ומדויק.{0}{0} דוגמאות:{0}{1} דיוק 6, סולם 2 מתאים למספרים כמו 1234.56.{0}{1} דיוק 6, סולם 6 מתאים למספרים כמו 0.123456.{0}{0} הדיוק של סוג הנתונים חייב להיות שווה או גדול מהדיוק של המקור ({2}).
#XMSG
invalidScale=הזן ערך סולם חוקי.
#XMSG
invalidScaleDescription=הדיוק מגדיר את המספר הכולל של ספרות. הסולם מגדיר את מספר הספרות לאחר הנקודה העשרונית והוא יכול להיות בין 0 ומדויק. {0}{0} דוגמאות: {0}{1} דיוק 6, סולם 2 מתאים למספרים כמו 1234.56{0}{1} דיוק 6, סולם 6 מתאים למספרים כמו 0.123456.{0} {0} דיוק וסולם עבור היעד חייב להיות תואם לדיוק ולסולם עבור המקור כך שכל הספרות מהמקור יתאימו בשדה היעד. לדוגמה, אם הדיוק הוא 6 והסולם 2 במקור (וכתוצאה מכך ספרות חוץ מ-0 לפני הנקודה העשרונית), לא יוכלו להיות דיוק 6 וסולם 6 ביעד.
#XMSG
invalidMappedScale=הזן ערך סולם חוקי.
#XMSG
invalidMappedScaleDescription1=הדיוק מגדיר את המספר הכולל של ספרות. הסולם מגדיר את מספר הספרות לאחר הנקודה העשרונית ויכול להיות בין 0 ומדויק.{0}{0} דוגמאות:{0}{1} דיוק 6, סולם 2 מתאים למספרים כמו 1234.56.{0}{1} דיוק 6, סולם 6 מתאים למספרים כמו 0.123456.{0}{0} הסולם של סוג הנתונים חייב להיות שווה או גדול מהסולם של המקור ({2}).
#XMSG
nonCompatibleDataType=בחר סוג נתוני יעד תואמים.
#XMSG
nonCompatibleDataTypeDescription1=סוג הנתונים שאתה מציין כאן חייב להתאים לסוג נתוני המקור ({0}). {1}{1} לדוגמה, אם עמודת המקור כוללת מחרוזת סוג נתונים ומכילה אותיות לא ניתן להשתמש סוג נתונים עשרוניים עבור היעד.
#XMSG
invalidColumnCount=בחר עמודת מקור.
#XMSG
ObjectStoreInvalidScaleORPrecision=הזן ערך חוקי עבור דיוק וסולם .
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=הערך הראשון הוא הדיוק, המגדיר את המספר הכולל של הספרות. הערך השני הוא הסולם, המגדיר את הספרות אחרי הנקודה העשרונית. הזן ערך סולם יעד שגדול מערך סולם המקור וודא שההפרש בין סולם היעד שהוזן לערך הדיוק גדול מההפרש בין סולם המקור לערך הדיוק.
#XMSG
InvalidPrecisionORScale=הזן ערך חוקי עבור דיוק וסולם.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=הערך הראשון הוא הדיוק, המגדיר את המספר הכולל של הספרות. הערך השני הוא הסולם, המגדיר את הספרות אחרי הנקודה העשרונית.{0}{0}מכיוון שסוג נתוני המקור אינו נתמך ב-Google BigQuery, הוא מומר לסוג נתוני היעד DECIMAL. במקרה זה, ניתן להגדיר את הדיוק רק בין 38 ל-76, ואת הסולם בין 9 ל-38. יתרה מכך, התוצאה של דיוק מינוס סולם, המייצג את הספרות לפני הנקודה העשרונית, חייבת להיות בין 29 ל-38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=הערך הראשון הוא הדיוק, המגדיר את המספר הכולל של הספרות. הערך השני הוא הסולם, המגדיר את הספרות אחרי הנקודה העשרונית.{0}{0}מכיוון שסוג נתוני המקור אינו נתמך ב-Google BigQuery, הוא מומר לסוג נתוני היעד DECIMAL. במקרה זה, יש להגדיר את הדיוק כ-20 או יותר. בנוסף, התוצאה של דיוק מינוס סולם, המשקף את הספרות לפני הנקודה העשרונית, חייבת להיות 20 או יותר.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=הערך הראשון הוא הדיוק, המגדיר את המספר הכולל של הספרות. הערך השני הוא הסולם, המגדיר את הספרות אחרי הנקודה העשרונית.{0}{0}מכיוון שסוג נתוני המקור אינו נתמך ביעד, הוא מומר לסוג נתוני היעד DECIMAL. במקרה זה, הדיוק חייב להיות מוגדר על-ידי כל מספר שווה או גדול מ-1 ושווה או קטן מ-38, וסולם שווה או קטן מדיוק.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=הערך הראשון הוא הדיוק, המגדיר את המספר הכולל של הספרות. הערך השני הוא הסולם, המגדיר את הספרות אחרי הנקודה העשרונית.{0}{0}מכיוון שסוג נתוני המקור אינו נתמך ביעד, הוא מומר לסוג נתוני היעד DECIMAL. במקרה זה, יש להגדיר את הדיוק כ-20 או יותר. בנוסף, התוצאה של דיוק מינוס סולם, המשקף את הספרות לפני הנקודה העשרונית, חייבת להיות 20 או יותר.
#XMSG
invalidColumnCountDescription=כדי ליצור הזנת מיפוי חוקית עליך לבחור עמודת מקור או להזין ערך קבוע.
#XMSG
duplicateColumns=שנה שם של עמודת יעד.
#XMSG
duplicateGBQCDCColumnsDesc=שם עמודת היעד משוריין ב-Google BigQuery. יש לשנות אותו כדי לשמור את התכנון.
#XMSG
duplicateConfluentCDCColumnsDesc=שם עמודת היעד משוריין ב-Confluent. יש לשנות אותו כדי לשמור את התכנון.
#XMSG
duplicateSignavioCDCColumnsDesc=שם עמודת היעד משוריין ב-SAP Signavio: יש לשנות אותו כדי לשמור את התכנון.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=שם עמודת היעד משוריין ב-MS OneLake. יש לשנות אותו כדי לשמור את התכנון.
#XMSG
duplicateSFTPCDCColumnsDesc=שם עמודת היעד משוריין ב-SFTP. יש לשנות אותו כדי לשמור את התכנון.
#XMSG
GBQTargetNameWithPrefixUpdated1=שם עמודת היעד כולל תחילית שמשורינת ב-Google BigQuery. עליך לשנות את השם כדי שתוכל לשמור את התכנון. {0}{0}שם עמודת היעד לא יכול להתחיל באף אחת מהמחרוזות הבאות: {0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=קצר שם של עמודת יעד.
#XMSG
GBQtargetMaxLengthDesc=ב-Google BigQuery שם עמודה יכול להשתמש ב-300 תווים מקסימום. קצר את שם עמודת היעד כדי שתוכל לשמור את התכנון.
#XMSG
invalidMappedScalePrecision=דיוק ודירוג עבור היעד חייבים להיות תואמים לדיוק ולדירוג של המקור כך שכל הספרות מהמקור יתאימו לשדה היעד.
#XMSG
invalidMappedScalePrecisionShortText=הזן ערך דיוק וערך דירוג חוקיים.
#XMSG
validationIncompatiblePKTypeDescProjection3=עמודת מקור אחת או יותר כוללות סוגי נתונים שאותם לא ניתן להגדיר כמפתחות ראשיים ב-Google BigQuery. אף מפתח ראשי לא יווצר באובייקט היעד.{0}{0}סוגי הנתונים הבאים של יעד תואמים לסוגי נתונים של Google BigQuery שעבורם ניתן להגדיר מפתח ראשי:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=בטל סימון  של  column __message_id.
#XMSG
uncheckColumnMessageIdDesc=עמודה: מפתח ראשי
#XMSG
validationOpCodeInsert=עליך להזין ערך להוספה.
#XMSG
recommendDifferentPrimaryKey=מומלץ לבחור מפתח ראשי אחר ברמת הפריט.
#XMSG
recommendDifferentPrimaryKeyDesc=כאשר קוד הפעולה כבר מוגדר, מומלץ לבחור מפתחות ראשיים שונים עבור אינדקס המערך והפריטים, כדי למנוע בעיות כמו שכפול עמודות לדוגמה.
#XMSG
selectPrimaryKeyItemLevel=עליך לבחור מפתח ראשי אחד לפחות הן עבור הכותרת והן עבור רמת הפריט.
#XMSG
selectPrimaryKeyItemLevelDesc=כאשר מערך או מפה מורחבים, עליך לבחור שני מפתחות ראשיים, אחד ברמת הכותרת ואחד ברמת הפריט.
#XMSG
invalidMapKey=עליך לבחור מפתח ראשי אחד לפחות ברמת הכותרת.
#XMSG
invalidMapKeyDesc=כאשר מערך או מפה מורחבים, עליך לבחור מפתח ראשי ברמת הכותרת.
#XFLD
txtSearchFields=חפש עמודות יעד
#XFLD
txtName=שם
#XMSG
txtSourceColValidation=עמודת מקור אחת או יותר לא נתמכות:
#XMSG
txtMappingCount=מיפויים ({0})
#XMSG
schema=תרשים
#XMSG
sourceColumn=עמודות מקור
#XMSG
warningSourceSchema=כל שינוי שיעשה לתרשים ישפיע על מיפויים בדיאלוג התכנון.
#XCOL
txtTargetColName=עמודת יעד (שם טכני)
#XCOL
txtDataType=סוג נתוני יעד
#XCOL
txtSourceDataType=סוג נתוני מקור
#XCOL
srcColName=עמודת מקור (שם טכני)
#XCOL
precision=דיוק
#XCOL
scale=סולם
#XCOL
functionsOrConstants=פונקציות / קבועים
#XCOL
txtTargetColBusinessName=עמודת יעד (שם עסקי)
#XCOL
prKey=מפתח ראשי
#XCOL
txtProperties=תכונות
#XBUT
txtOK=שמור
#XBUT
txtCancel=בטל
#XBUT
txtRemove=הסר
#XFLD
txtDesc=תיאור
#XMSG
rftdMapping=מיפוי
#XFLD
@lblColumnDataType=סוג נתונים
#XFLD
@lblColumnTechnicalName=שם טכני
#XBUT
txtAutomap=מפה אוטומטית
#XBUT
txtUp=למעלה
#XBUT
txtDown=למטה

#XTOL
txtTransformationHeader=תכנון
#XTOL
editTransformation=ערוך
#XTOL
primaryKeyToolip=מפתח


#XMSG
rftdFilter=סנן
#XMSG
rftdFilterColumnCount=מקור: {0}({1})
#XTOL
rftdFilterColSearch=חפש
#XMSG
rftdFilterColNoData=אין עמודות לתצוגה
#XMSG
rftdFilteredColNoExps=אין ביטויי מסנן
#XMSG
rftdFilterSelectedColTxt=הוסף מסנן עבור
#XMSG
rftdFilterTxt=מסנן זמין עבור
#XBUT
rftdFilterSelectedAddColExp=הוסף ביטוי
#YINS
rftdFilterNoSelectedCol=בחר עמודה להוספת מסנן.
#XMSG
rftdFilterExp=ביטוי מסנן
#XMSG
rftdFilterNotAllowedColumn=הוספת מסננים אינו נתמך עבור עמודה זו.
#XMSG
rftdFilterNotAllowedHead=עמודת 'לא נתמך'
#XMSG
rftdFilterNoExp=לא הוגדרו מסננים
#XTOL
rftdfilteredTt=מסונן
#XTOL
rftdremoveexpTt=הסר ביטוי מסנן
#XTOL
validationMessageTt=הודעות בדיקת תקינות
#XTOL
rftdFilterDateInp=בחר תאריך
#XTOL
rftdFilterDateTimeInp=בחר שעה ותאריך
#XTOL
rftdFilterTimeInp=בחר שעה
#XTOL
rftdFilterInp=הזן ערך
#XMSG
rftdFilterValidateEmptyMsg={0} ביטויי מסנן בעמודה {1} ריקים
#XMSG
rftdFilterValidateInvalidNumericMsg={0} ביטויי מסנן בעמודה {1} מכילים ערכים מספריים
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=ביטוי מסנן חייב להכיל ערכים מספריים חוקיים
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=אם סכימת אובייקט היעד השתנתה, השתמש בפונקציה "מפה לאובייקט יעד קיים" בדף הראשי כדי להתאים את השינויים, ומפה מחדש את אובייקט היעד עם המקור שלו.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=אם טבלת היעד כבר קיימת והמיפוי כולל שינוי תרשים תהיה חייב לשנות את טבלת היעד בהתאם לפני פריסת תזרים השכפול.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=אם המיפוי כולל שינוי תרשים תהיה חייב לשנות את טבלת היעד בהתאם לפני פריסת תזרים השכפול.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=העמודות הלא נתמכות הבאות לא נלקחו בחשבון בהגדרת המקור: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=העמודות הלא נתמכות הבאות לא נלקחו בחשבון בהגדרת היעד: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=האובייקטים הבאים אינם נתמכים מכיוון שהם חשופים לצריכה: {0} {1} {0} {0} כדי להשתמש בטבלאות בתזרים שכפול, אין להגדיר את השימוש הסמנטי (בהגדרות הטבלה) אל {2}Analytical Dataset{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=לא ניתן להשתמש באובייקט היעד מכיוון שהוא חשוף לצריכה: {0} {0} כדי להשתמש בטבלאות בתזרים שכפול, אין להגדיר את השימוש הסמנטי (בהגדרות הטבלה) אל {1}Analytical Dataset{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=אובייקט יעד עם שם זה כבר קיים. עם זאת, לא ניתן להשתמש בו מכיוון שהוא חשוף לצריכה {0} {0} כדי להשתמש בטבלאות בתזרים שכפול, אין להגדיר את השימוש הסמנטי (בהגדרות הטבלה) אל {1}Analytical Dataset{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=אובייקט בשם זה כבר קיים ביעד. {0}עם זאת, לא ניתן להשתמש באובייקט זה כאובייקט יעד עבור זרימת שכפול למאגר המקומי, מכיוון שהוא אינו טבלה מקומית.
#XMSG:
targetAutoRenameUpdated=שם עמודת יעד שונה.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=שם עמודת היעד שונה כדי לאפשר שכפולים ב-Google BigQuery. השינוי בוצע מהסיבות הבאות:{0} {1}{2}שם עמודה משורין{3}{2}תווים שלא נתמכים{3}{2}תחילית משורינת{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=שם עמודת היעד שונה כדי לאפשר שכפולים ב-Confluent. השינוי בוצע מהסיבות הבאות:{0} {1}{2}שם עמודה משורין{3}{2}תווים שלא נתמכים{3}{2}תחילית משורינת{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=שם עמודת היעד שונה כדי לאפשר שכפולים ליעד. השינוי בוצע מהסיבות הבאות:{0} {1}{2}תווים שאינם נתמכים{3}{2}תחילית משורינת{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=שם עמודת היעד שונה כדי לאפשר שכפולים ליעד. השינוי בוצע מהסיבות הבאות:{0} {1}{2}שם עמודה משוריינת{3}{2}תווים שאינם נתמכים{3}{2}תחילית משוריינת{3}{4}
#XMSG:
targetAutoDataType=סוג נתוני יעד השתנה.
#XMSG:
targetAutoDataTypeDesc=סוג נתוני יעד השתנה ל-{0} כיוון שסוג נתוני מקור לא נתמך ב-Google BigQuery.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=סוג נתוני יעד השתנה ל-{0} כיוון שסוג נתוני מקור לא נתמך בחיבור היעד.
#XMSG
projectionGBQUnableToCreateKey=לא ניתן ליצוא מפתחות ראשיים.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=ב-Google BigQuery,עד 16 מפתחות ראשיים נתמכים, אולם אובייקט המקור מכיל מספר גדול יותר של מפתחות ראשיים. אף מפתח ראשי לא ייווצר באובייקט יעד.
#XMSG
HDLFNoKeyError=הגדר עמודה אחת או יותר כמפתח ראשי.
#XMSG
HDLFNoKeyErrorDescription=כדי לשכפל אובייקט, עליך להגדיר עמודת יעד אחת או יותר כמפתח ראשי.
#XMSG
HDLFNoKeyErrorExistingTarget=הגדר עמודה אחת או יותר כמפתח ראשי.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=כדי לשכפל נתונים לאובייקט יעד קיים, עליו להיות בעל עמודה אחת או יותר המוגדרות כמפתח ראשי. {0} {0} יש לך את האפשרויות הבאות להגדרת עמודה אחת או יותר כמפתח הראשי: {0}{0}{1} השתמש בעורך הטבלה המקומי כדי לשנות את אובייקט היעד הקיים. לאחר מכן טען מחדש את תזרים השכפול.{0}{0}{1}שנה את שם אובייקט היעד בתזרים השכפול. פעולה זו תיצור אובייקט חדש ברגע שהריצה מתחילה. לאחר שינוי השם, ניתן להגדיר עמודה אחת או יותר כמפתח הראשי בתכנון.{0}{0}{1} מפה את האובייקט לאובייקט יעד קיים אחר שבו עמודה אחת או יותר כבר מוגדרות כמפתח ראשי.
#XMSG
HDLFSourceTargetDifferentKeysWarning=המפתח הראשי שונה.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=בהשוואה לאובייקט המקור, הגדרת עמודות שונות כמפתח הראשי של אובייקט היעד. ודא שעמודות אלו מזהות באופן ייחודי את כל השורות כדי למנוע פגיעה אפשרית בנתונים בעת שכפול הנתונים מאוחר יותר. {0} {0} באובייקט המקור, העמודות הבאות מוגדרות כמפתח ראשי: {0} {1}
#XMSG
duplicateDPIDColumns=שנה שם של עמודת יעד.
#XMSG
duplicateDPIDDColumnsDesc1=שם עמודת יעד זו שמורה לעמודה טכנית. הזן שם אחר כדי לשמור את החיזוי.
#XMSG:
targetAutoRenameDPID=שם עמודת יעד שונה.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=שם עמודת היעד שונה כדי לאפשר שכפולים ממקור ABAP ללא מפתחות. השינוי בוצע מהסיבות הבאות:{0} {1}{2}שם עמודה משורין{3}{2}תווים שלא נתמכים{3}{2}תחילית משורינת{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle={0} הגדרות יעד
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle={0} הגדרות מקור
#XBUT
connectionSettingSave=שמור
#XBUT
connectionSettingCancel=בטל
#XBUT: Button to keep the object level settings
txtKeep=שמור
#XBUT: Button to overwrite the Object level settings
txtOverwrite=שכתב
#XFLD
targetConnectionThreadlimit=הגבלת שרשרת יעדים עבור טעינה התחלתית (1-100)
#XFLD
connectionThreadLimit=הגבלת שרשרת מקורות עבור טעינה התחלתית (1-100)
#XFLD
maxConnection=הגבלת שרשרת שכפולים (1-100)
#XFLD
kafkaNumberOfPartitions=מספר המחיצות
#XFLD
kafkaReplicationFactor=גורם שכפול
#XFLD
kafkaMessageEncoder=מצפין הודעות
#XFLD
kafkaMessageCompression=דחיסת הודעות
#XFLD
fileGroupDeltaFilesBy=קבץ דלתא לפי
#XFLD
fileFormat=סוג קובץ
#XFLD
csvEncoding=קידוד CSV
#XFLD
abapExitLbl=יציאת ABAP
#XFLD
deltaPartition=ספירת שרשרת אובייקטים עבור טעינות דלתא (1-10)
#XFLD
clamping_Data=קטיעת נתונים נכשלה
#XFLD
fail_On_Incompatible=נתונים לא שלמים נכשלו
#XFLD
maxPartitionInput=מספר המחיצות המרבי
#XFLD
max_Partition=הגדר את מספר המחיצות המרבי
#XFLD
include_SubFolder=כלול תיקיות משנה
#XFLD
fileGlobalPattern=דפוס גלובלי עבור שם קובץ
#XFLD
fileCompression=דחיסת קבצים
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=תוחם קבצים
#XFLD
fileIsHeaderIncluded=כותרת של קובץ
#XFLD
fileOrient=Orient
#XFLD
gbqWriteMode=מצב כתיבה
#XFLD
suppressDuplicate=הסתר כפילויות
#XFLD
apacheSpark=הפעל תאימות של Apache Spark
#XFLD
clampingDatatypeCb=הצמד סוגי נתונים של נקודה עשרונית צפה
#XFLD
overwriteDatasetSetting=שכתב הגדרות יעד ברמת האובייקט
#XFLD
overwriteSourceDatasetSetting=שכתב הגדרות יעד ברמת האובייקט
#XMSG
kafkaInvalidConnectionSetting=הזן מספר בין {0} ל-{1}
#XMSG
MinReplicationThreadErrorMsg=הזן מספר גדול מ-{0}.
#XMSG
MaxReplicationThreadErrorMsg=הזן מספר קטן מ-{0}.
#XMSG
DeltaThreadErrorMsg=הזן ערך בין 1 ו-10.
#XMSG
MaxPartitionErrorMsg=הזן ערך בין 1 <= x <= 2147483647. ערך ברירת המחדל הוא 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=הזן מספר שלם בין {0} לבין {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=השתמש בגורם השכפול של הברוקר
#XFLD
serializationFormat=פורמט הפצה סדרתית
#XFLD
compressionType=סוג דחיסה
#XFLD
schemaRegistry=השתמש ברישום תרשים
#XFLD
subjectNameStrat=אסטרטגיית שם נושא
#XFLD
compatibilityType=סוג תאימות
#XFLD
confluentTopicName=שם נושא
#XFLD
confluentRecordName=שם רשומה
#XFLD
confluentSubjectNamePreview=תצוגה מקדימה של שם נושא
#XMSG
serializationChangeToastMsgUpdated2=פורמט הסידור בסדרות השתנה ל-JSON מכיוון שרישום התרשים אינו מופעל. כדי לשנות את פורמט הסידור בסדרות בחזרה ל-AVRO, עליך להפעיל את רישום התרשים תחילה.
#XBUT
confluentTopicNameInfo=שם הנושא מבוסס תמיד על שם אובייקט היעד. ניתן לשנות אותו על-ידי שינוי שם אובייקט היעד.
#XMSG
emptyRecordNameValidationHeaderMsg=הזן שם רשומה.
#XMSG
emptyPartionHeader=הזן את מספר המחיצות
#XMSG
invalidPartitionsHeader=הזן מספר מחיצות חוקי.
#XMSG
invalidpartitionsDesc=הזן מספר בין 1 ל-200,000.
#XMSG
emptyrFactorHeader=הזן גורם שכפול
#XMSG
invalidrFactorHeader=הזן גורם שכפול חוקי.
#XMSG
invalidrFactorDesc=הזן מספר בין 1 ל-32,767.
#XMSG
emptyRecordNameValidationDescMsg=אם נעשה שימוש בפורמט הפצה סדרתית "AVRO", רק התווים הבאים נתמכים:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(קו תחתון)
#XMSG
validRecordNameValidationHeaderMsg=הזן שם רשומה חוקי.
#XMSG
validRecordNameValidationDescMsgUpdated=מאחר שפורמט הסידור בסדרות 'AVRO' נמצא בשימוש, שם הרשומה חייב להיות מורכב מתווים אלפאנומריים (A-Z, a-z, 0-9) וקווים תחתונים (_). הוא חייב להתחיל באות או קו תחתון.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=ניתן להגדיר את "ספירת שרשרת אובייקטים עבור טעינות דלתא" מיד לאחר שלאחד או יותר מהאובייקטים יש סוג טעינה "התחלתי ודלתא".
#XMSG
invalidTargetName=שם עמודה לא חוקי
#XMSG
invalidTargetNameDesc=עמודת היעד חייבת לכלול תווים אלפאנומריים (A-Z, a-z, 0-9) וקווים תחתונים (_).
#XFLD
consumeOtherSchema=צריכת גרסאות תרשים אחרות
#XFLD
ignoreSchemamissmatch=התעלמות מחוסר התאמה של תרשים
#XFLD
confleuntDatatruncation=קטיעת נתונים נכשלה
#XFLD
isolationLevel=רמת בידוד
#XFLD
confluentOffset=נקודת התחלה
#XFLD
signavioGroupDeltaFilesByText=ללא
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=לא
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=לא

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=תכנונים
#XBUT
txtAdd=הוסף
#XBUT
txtEdit=ערוך
#XMSG
transformationText=הוסף תכנון להגדרת מסנן או מיפוי.
#XMSG
primaryKeyRequiredText=בחר מפתח עיקרי עם 'קבע תצורה של תרשים'.
#XFLD
lblSettings=הגדרות
#XFLD
lblTargetSetting={0}: הגדרות יעד
#XMSG
@csvRF=בחר את הקובץ המכיל את הגדרת התרשים שברצונך להחיל על כל הקבצים בתיקיה.
#XFLD
lblSourceColumns=עמודות מקור
#XFLD
lblJsonStructure=מבנה JSON
#XFLD
lblSourceSetting={0}: הגדרות מקור
#XFLD
lblSourceSchemaSetting={0}: הגדרות תרשים מקור
#XBUT
messageSettings=הגדרות הודעה
#XFLD
lblPropertyTitle1=תכונות אובייקט
#XFLD
lblRFPropertyTitle=תכונות תזרים שכפול
#XMSG
noDataTxt=אין עמודות להצגה.
#XMSG
noTargetObjectText=לא נבחר אובייקט יעד.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=עמודות יעד
#XMSG
searchColumns=עמודות חיפוש
#XTOL
cdcColumnTooltip=עמודה עבור לכידת דלתא
#XMSG
sourceNonDeltaSupportErrorUpdated=אובייקט המקור לא תומך בלכידת דלתא.
#XMSG
targetCDCColumnAdded=2 עמודות יעד נוספו עבור לכידת דלתא.
#XMSG
deltaPartitionEnable=הגבלת שרשרת אובייקטים עבור טעינות דלתא להגדרות המקור.
#XMSG
attributeMappingRemovalTxt=הסרת מיפויים לא חוקיים שאינם נתמכים עבור אובייקט יעד חדש.
#XMSG
targetCDCColumnRemoved=2 עמודות יעד עבור לכידת דלתא הוסרו.
#XMSG
replicationLoadTypeChanged=סוג טעינה שונה ל"התחלתי ודלתא".
#XMSG
sourceHDLFLoadTypeError=שנה סוג טעינה ל"התחלתי ודלתא".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=כדי לשכפל אובייקט מחיבור מקור עם סוג החיבור SAP HANA Cloud, קובצי אגם נתונים אל SAP Datasphere, עליך להשתמש בסוג הטעינה "התחלתי ודלתא".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=אפשר לכידת דלתא.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=כדי לשכפל אובייקט מחיבור מקור עם סוג החיבור SAP HANA Cloud, קובצי אגם נתונים אל SAP Datasphere, עליך לאפשר לכידת דלתא.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=שנה אובייקט יעד.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=לא ניתן להשתמש באובייקט היעד משום שלכידת דלתא מושבתת. אפשר לשנות את שם אובייקט היעד (מה שמאפשר ליצור אובייקט חדש עם לכידת דלתא) או למפות אותו לאובייקט קיים עם לכידת דלתא מופעלת.
#XMSG
deltaPartitionError=הזן ספירת שרשרת אובייקטים חוקית עבור טעינות דלתא.
#XMSG
deltaPartitionErrorDescription=הזן ערך בין 1 ו-10.
#XMSG
deltaPartitionEmptyError=הזן ספירת שרשרת אובייקטים עבור טעינות דלתא.
#XFLD
@lblColumnDescription=תיאור
#XMSG
@lblColumnDescriptionText1=למטרות טכניות - טיפול ברשומות כפולות שנגרם על-ידי בעיות במהלך שכפול אובייקטי מקור מבוססי ABAP שאין להם מפתח עיקרי.
#XFLD
storageType=אחסון
#XFLD
skipUnmappedColLbl=דלג על עמודות שלא מופו
#XFLD
abapContentTypeLbl=סוג תוכן
#XFLD
autoMergeForTargetLbl=שלב נתונים באופן אוטומטי
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=כללי
#XFLD
lblBusinessName=שם עסקי
#XFLD
lblTechnicalName=שם טכני
#XFLD
lblPackage=חבילה
#XFLD
statusPanel=סטאטוס הפעלה
#XBTN: Schedule dropdown menu
SCHEDULE=לוח זמנים
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=ערוך לוח זמנים
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=מחק לוח זמנים
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=צור לוח זמנים
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=בדיקת אימות לוח זמנים נכשלה
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=לא ניתן ליצור את לוח הזמנים מכיוון שתזרים השכפול נמצא בתהליך פריסה כעת.{0} המתן עד שתזרים השכפול ייפרס.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=עבור תזרימי השכפולים שמכילים אובייקטים עם סוג טעינה 'התחלתי ודלתא', לא ניתן ליצור לו"ז.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=עבור תזרימי השכפולים שמכילים אובייקטים מסוג טעינה 'התחלתי ודלתא/דלתא בלבד', לא ניתן ליצור לו"ז.
#XFLD : Scheduled popover
SCHEDULED=מתוזמן
#XFLD
CREATE_REPLICATION_TEXT=צור תזרים שכפולים
#XFLD
EDIT_REPLICATION_TEXT=ערוך תזרים שכפולים
#XFLD
DELETE_REPLICATION_TEXT=מחק תזרים שכפולים
#XFLD
REFRESH_FREQUENCY=תדירות
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=לא ניתן לפרוס את תזרים השכפולים מפני שהלו"ז הקיים{0} אינו תומך בסוג הטעינה ''התחלתי ודלתא''.{0}{0}כדי לפרוס את תזרים השכפולים, עליך להגדיר את סוגי הטעינה של כל האובייקטים{0} ל''התחלתי בלבד''. לחלופין, באפשרותך למחוק את לוח הזמנים, לפרוס את {0}תזרים השכפולים ואז להתחיל הפעלה חדשה. הדבר מוביל להפעלה ללא {0}סיום, שיכול גם לתמוך באובייקטים עם סוג הטעינה ''התחלתי ודלתא''.
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=לא ניתן לפרוס את תזרים השכפולים מפני שהלו"ז הקיים{0} אינו תומך בסוג הטעינה ''התחלתי ודלתא/דלתא בלבד''.{0}{0}כדי לפרוס את תזרים השכפולים, עליך להגדיר את סוגי הטעינה של כל האובייקטים{0} ל''התחלתי בלבד''. לחלופין, באפשרותך למחוק את לוח הזמנים, לפרוס את {0}תזרים השכפולים ואז להתחיל הפעלה חדשה. הדבר מוביל להפעלה ללא {0}סיום, שיכול גם לתמוך באובייקטים עם סוג הטעינה ''התחלתי ודלתא/דלתא בלבד''.
#XMSG
SCHEDULE_EXCEPTION=קבלת פרטי הלו"ז נכשלה
#XFLD: Label for frequency column
everyLabel=כל
#XFLD: Plural Recurrence text for Hour
hoursLabel=שעות
#XFLD: Plural Recurrence text for Day
daysLabel=ימים
#XFLD: Plural Recurrence text for Month
monthsLabel=חודשים
#XFLD: Plural Recurrence text for Minutes
minutesLabel=דקות
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=קבלת מידע על אפשרות התזמון נכשלה.
#XFLD :Paused field
PAUSED=מושהה
#XMSG
navToMonitoring=פתח במעקב אחר תזרימים
#XFLD
statusLbl=סטאטוס
#XFLD
lblLastRunExecuted=תחילת הפעלה אחרונה
#XFLD
lblLastExecuted=הפעלה אחרונה
#XFLD: Status text for Completed
statusCompleted=הושלם
#XFLD: Status text for Running
statusRunning=פועל
#XFLD: Status text for Failed
statusFailed=נכשל
#XFLD: Status text for Stopped
statusStopped=נעצר
#XFLD: Status text for Stopping
statusStopping=מתבצעת עצירה
#XFLD: Status text for Active
statusActive=פעיל
#XFLD: Status text for Paused
statusPaused=מושהה
#XFLD: Status text for not executed
lblNotExecuted=לא הופעל עדיין
#XFLD
messagesSettings=הגדרות הודעה
#XTOL
@validateModel=הודעות בדיקת תקינות
#XTOL
@hierarchy=היררכיה
#XTOL
@columnCount=מספר עמודות
#XMSG
VAL_PACKAGE_CHANGED=הקצת את האובייקט הזה לחבילה "{1}". לחץ על שמור כדי לאשר ולאמת את השינוי הזה. שים לב שההקצאה לחבילה אינה ניתנת לביטול בעורך זה לאחר השמירה.
#XMSG
MISSING_DEPENDENCY=לא ניתן לפתור יחסי תלות של אובייקט "{0}" בחבילה "{1}".
#XFLD
deltaLoadInterval=מרווח טעינת דלתא
#XFLD
lblHour=שעות (0-24)
#XFLD
lblMinutes=דקות (0-59)
#XMSG
maxHourOrMinErr=הזן ערך בין 0 ל-{0}.
#XMSG
maxDeltaInterval=הערך המקסימלי של מרווח טעינת דלתא הוא 24 שעות.{0}שנה את ערך הדקות או את ערך השעות בהתאם.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=נתיב מכולת יעד
#XFLD
confluentSubjectName=שם נושא
#XFLD
confluentSchemaVersion=גרסת תרשים
#XFLD
confluentIncludeTechKeyUpdated=כלול מפתח טכני
#XFLD
confluentOmitNonExpandedArrays=הסר מערכים לא מורחבים
#XFLD
confluentExpandArrayOrMap=הרחב מערך או מפה
#XCOL
confluentOperationMapping=מיפוי פעולה
#XCOL
confluentOpCode=קוד פעולה
#XFLD
confluentInsertOpCode=הוסף
#XFLD
confluentUpdateOpCode=עדכן
#XFLD
confluentDeleteOpCode=מחק
#XFLD
expandArrayOrMapNotSelectedTxt=לא נבחר
#XFLD
confluentSwitchTxtYes=כן
#XFLD
confluentSwitchTxtNo=לא
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=שגיאה
#XTIT
executeWarning=אזהרה
#XMSG
executeunsavederror=שמור את תזרים השכפול שלך לפני שתפעיל אותו.
#XMSG
executemodifiederror=יש שינויים שלא נשמרו בתזרים השכפול. שמור את תזרים השכפול.
#XMSG
executeundeployederror=עליך לפרוס את תזרים השכפול שלך לפני שתוכל להפעיל אותו.
#XMSG
executedeployingerror=המתן לסיום הפריסה.
#XMSG
msgRunStarted=הפעלה התחילה
#XMSG
msgExecuteFail=כשל בהפעלת תזרים השכפול
#XMSG
titleExecuteBusy=יש להמתין.
#XMSG
msgExecuteBusy=אנחנו מכינים את הנתונים שלך להפעלת תזרים השכפול.
#XTIT
executeConfirmDialog=אזהרה
#XMSG
msgExecuteWithValidations=בתזרים השכפול יש שגיאות בדיקת תקינות. הפעלת תזרים השכפול עלולה להסתיים בכישלון.
#XMSG
msgRunDeployedVersion=קיימים שינויים לפריסה. הגרסה האחרונה שנפרסה של תזרים השכפול תופעל. האם ברצונך להמשיך?
#XBUT
btnExecuteAnyway=הפעל בכל מקרה
#XBUT
btnExecuteClose=סגור
#XBUT
loaderClose=סגור
#XTIT
loaderTitle=טוען
#XMSG
loaderText=שולף פרטים מהשרת
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=לא ניתן להתחיל את תזרים השכפולים לחיבור יעד שאינו של SAP.
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=מכיוון שאין נפח יוצא זמין עבור חודש זה.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=מנהל יכול להגדיל את הבלוקים היוצאים של הפרמייה עבור
#XMSG
premiumOutBoundRFAdminErrMsgPart2=דייר זה, ובכך להפוך את הנפח היוצא לזמין עבור חודש זה.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=שגיאה
#XTIT
deployInfo=מידע
#XMSG
deployCheckFailException=אירעה חריגה במהלך הפריסה
#XMSG
deployGBQFFDisabled=פריסת זרימות שכפול עם חיבור יעד ל-Google BigQuery אינה אפשרית כרגע מכיוון שאנו מבצעים אחזקה בפונקציה זו.
#XMSG
deployKAFKAFFDisabled=פריסת זרימות שכפול עם חיבור יעד ל-Apache Kafka אינה אפשרית כרגע מכיוון שאנו מבצעים אחזקה בפונקציה זו.
#XMSG
deployConfluentDisabled=פריסת זרימות שכפול עם חיבור יעד ל-Confluent Kafka אינה אפשרית כרגע מכיוון שאנו מבצעים אחזקה בפונקציה זו.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=עבור אובייקטי היעד הבאים שמות טבלאות לכידת דלתא כבר נמצאים בשימוש על-ידי טבלאות אחרות במאגר: {0} עליך לשנות את השם של אובייקטי היעד האלה כדי לוודא ששמות הטבלאות לכידת דלתא ייחודיים לפני שתוכל לפרוס את תזרים השכפולים.
#XMSG
deployDWCSourceFFDisabled=פריסת תזרימי שכפול שמקורן SAP Datasphere לא אפשרית כרגע כיוון שאנחנו מבצעים תחזוקה על פונקציה זו.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=פריסת זרימות שכפול המכילות טבלאות מקומיות התומכות בדלתא כאובייקטי מקור אינה אפשרית כעת מכיוון שאנו מבצעים תחזוקה בפונקציה זו.
#XMSG
deployHDLFSourceFFDisabled=פריסת תזרימי שכפול עם חיבורי מקור וסוג החיבור SAP HANA Cloud, קובצי אגם נתונים לא אפשרית כרגע כיוון שאנחנו מבצעים תחזוקה.
#XMSG
deployObjectStoreAsSourceFFDisabled=פריסת תזרימי שכפול שיש להם ספקי אחסון טובים בענן כמקור שלהם אינה אפשרית כרגע.
#XMSG
deployConfluentSourceFFDisabled=פריסת תזרימי שכפול שמקורן Confluent Kafka לא אפשרית כרגע כיוון שאנחנו מבצעים תחזוקה על פונקציה זו.
#XMSG
deployMaxDWCNewTableCrossed=עבור תזרימי שכפול גדולות, לא ניתן "לשמור ולפרוס" אותן במכה אחת. יש לשמור תחילה את תזרים השכפול ולאחר מכן לפרוס אותו.
#XMSG
deployInProgressInfo=הפריסה כבר נמצאת התהליך.
#XMSG
deploySourceObjectInUse=אובייקטי מקור {0} כבר נמצאים בשימוש בתזרימי שכפול {1}.
#XMSG
deployTargetSourceObjectInUse=אובייקטי מקור {0} כבר נמצאים בשימוש בתזרימי שכפול {1}. אובייקטי יעד {2} כבר נמצאים בשימוש בתזרימי שכפול {3}.
#XMSG
deployReplicationFlowCheckError=שגיאה במהלך אימות תזרים שכפול: {0}
#XMSG
preDeployTargetObjectInUse=אובייקטי יעד {0} כבר נמצאים בשימוש בתזרימי שכפול {1}, ולא יכול להיות לך אותו אובייקט יעד בשני תזרימי שכפול שונים. בחר אובייקט יעד אחר ונסה שוב.
#XMSG
runInProgressInfo=תזרים שכפול כבר פועל.
#XMSG
deploySignavioTargetFFDisabled=פריסת תזרימי שכפול שיעדן  SAP Signavio לא אפשריות כרגע כיוון שאנחנו מבצעים אחזקה בפונקציה זו.
#XMSG
deployHanaViewAsSourceFFDisabled=פריסת תזרימי שכפול, בעלי תצוגות כאובייקטי מקור, עבור חיבור המקור שנבחר אינה אפשרית כרגע. נסה שוב מאוחר יותר.
#XMSG
deployMsOneLakeTargetFFDisabled=פריסת תזרימי שכפול שיעדן  MS OneLake לא אפשריות כרגע כיוון שאנחנו מבצעים אחזקה בפונקציה זו.
#XMSG
deploySFTPTargetFFDisabled=פריסת תזרימי שכפול שיעדן  SFTP לא אפשריות כרגע כיוון שאנחנו מבצעים אחזקה בפונקציה זו.
#XMSG
deploySFTPSourceFFDisabled=פריסת תזרימי שכפול שמקורם SFTP לא אפשרית כרגע כיוון שאנחנו מבצעים אחזקה בפונקציה זו.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=שם טכני
#XFLD
businessNameInRenameTarget=שם עסקי
#XTOL
renametargetDialogTitle=שנה שם של אובייקט יעד
#XBUT
targetRenameButton=שנה שם
#XBUT
targetRenameCancel=בטל
#XMSG
mandatoryTargetName=עליך להזין שם.
#XMSG
dwcSpecialChar=_(קו תחתון) הוא התו המיוחד היחיד שמותר.
#XMSG
dwcWithDot=שם היעד של הטבלה יכול לכלול אותיות לטיניות, מספרים, קווים תחתונים (_) ונקודות (.). התו הראשון חייב להיות אות, מספר או קו תחתון (לא נקודה).
#XMSG
nonDwcSpecialChar=התווים המיוחדים המותרים הם '_' (קו תחתון), '-' (מקף) ו-'.' (נקודה)
#XMSG
firstUnderscorePattern=שם לא יכול להתחיל ב-_ (קו תחתון).

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: הצג משפט SQL ''צור טבלה''
#XMSG
sqlDialogMaxPKWarning=ב-Google BigQuery, מקסימום 16 מפתחות ראשיים נתמכים, ואובייקט המקור מכיל מספר גדול יותר. לכן, לא מוגדרים מפתחות ראשיים במשפט זה.
#XMSG
sqlDialogIncomptiblePKTypeWarning=עמודת מקור אחת או יותר מכילות סוגי נתונים שאותם לא ניתן להגדיר כמפתחות ראשיים ב-Google BigQuery. לכן, לא מוגדרים מפתחות ראשיים במקרה זה. ב-Google BigQuery רק סוגי הנתונים הבאים יכולים להיות בעלי מפתח ראשי: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=העתק וסגור
#XBUT
closeDDL=סגור
#XMSG
copiedToClipboard=הועתק ללוח עריכה


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=אובייקט ''{0}'' לא יכול להיות חלק משרשרת משימות מפני שאין לו סוף (כי הוא כולל אובייקטים עם סוג טעינה ''התחלתי ודלתא/דלתא בלבד'').
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=אובייקט ''{0}'' לא יכול להיות חלק משרשרת משימות מפני שאין לו סוף (כי הוא כולל אובייקטים עם סוג טעינה התחלתי ודלתא).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=אובייקט "{0}" לא ניתן להוספה לשרשרת המשימות.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=יש אובייקטי יעד שלא נשמרו. יש לשמור שוב.{0}{0} ההתנהגות של תכונה זו השתנתה: בעבר, אובייקטי יעד נוצרו רק בסביבת היעד כאשר תזרים השכפול נפרס.{0} כעת האובייקטים כבר נוצרים כאשר תזרים השכפול נשמר. תזרים השכפול שלך נוצר לפני השינוי הזה ומכיל אובייקטים חדשים.{0} עליך לשמור את תזרים השכפול שוב לפני פריסתו כך שהאובייקטים החדשים ייכללו כהלכה.
#XMSG
confirmChangeContentTypeMessage=אתה עומד לשנות את סוג התוכן. אם תעשה זאת, כל התכנונים הקיימים יימחקו.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=שם נושא
#XFLD
schemaDialogVersionName=גרסת תרשים
#XFLD
includeTechKey=כלול מפתח טכני
#XFLD
segementButtonFlat=שטוח
#XFLD
segementButtonNested=עם קינון
#XMSG
subjectNamePlaceholder=חפש שם נושא

#XMSG
@EmailNotificationSuccess=תצורה של הודעות דוא"ל בזמן ריצה נשמרה.

#XFLD
@RuntimeEmailNotification=הודעת דוא"ל על זמן ריצה

#XBTN
@TXT_SAVE=שמור


