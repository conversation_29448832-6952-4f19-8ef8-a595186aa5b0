#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Replication Flow

#XFLD: Edit Schema button text
editSchema=Edit Schema

#XTIT : Properties heading
configSchema=Configure Schema

#XFLD: save changed button text
applyChanges=Apply Changes


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Select Source Connection
#XFLD
sourceContainernEmptyText=Select Container
#XFLD
targetConnectionEmptyText=Select Target Connection
#XFLD
targetContainernEmptyText=Select Container
#XFLD
sourceSelectObjectText=Select Source Object
#XFLD
sourceObjectCount=Source Objects ({0})
#XFLD
targetObjectText=Target Objects
#XFLD
confluentBrowseContext=Select Context
#XBUT
@retry=Retry
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Tenant upgrade in progress.

#XTOL
browseSourceConnection=Browse source connection
#XTOL
browseTargetConnection=Browse target connection
#XTOL
browseSourceContainer=Browse source container
#XTOL
browseAndAddSourceDataset=Add source objects
#XTOL
browseTargetContainer=Browse target container
#XTOL
browseTargetSetting=Browse target settings
#XTOL
browseSourceSetting=Browse source settings
#XTOL
sourceDatasetInfo=Info
#XTOL
sourceDatasetRemove=Remove
#XTOL
mappingCount=This represents the total number of non-name based mappings/expressions.
#XTOL
filterCount=This represents the total number of filter conditions.
#XTOL
loading=Loading...
#XCOL
deltaCapture=Delta Capture
#XCOL
deltaCaptureTableName=Delta Capture Table
#XCOL
loadType=Load Type
#XCOL
deleteAllBeforeLoading=Delete All Before Loading
#XCOL
transformationsTab=Projections
#XCOL
settingsTab=Settings

#XBUT
renameTargetObjectBtn=Rename Target Object
#XBUT
mapToExistingTargetObjectBtn=Map to Existing Target Object
#XBUT
changeContainerPathBtn=Change Container Path
#XBUT
viewSQLDDLUpdated=View SQL Create Table Statement
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=The source object does not support delta capture, but the selected target object has the delta capture option enabled.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=The target object cannot be used because delta capture is enabled,{0}whereas the source object does not support delta capture.{1}You can select another target object that does not support delta capture.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=A target object with this name already exists. However, it cannot be used{0}because delta capture is enabled, whereas the source object does not{0}support delta capture.{1}You can either enter the name of an existing target object that does not{0}support delta capture, or enter a name that does not exist yet.
#XBUT
copySQLDDLUpdated=Copy SQL Create Table Statement
#XMSG
targetObjExistingNoCDCColumnUpdated=Existing tables in Google BigQuery must include the following columns for change data capture (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=The following source objects are not supported because they do not have a primary key, or they are using a connection that does not meet conditions to retrieve the primary key:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Please refer to SAP KBA 3531135 for a possible solution.
#XLST: load type list values
initial=Initial Only
@emailUpdateError=Error in updating Email Notification list

#XLST
initialDelta=Initial and Delta

#XLST
deltaOnly=Delta Only
#XMSG
confluentDeltaLoadTypeInfo=For Confluent Kafka source, only load type Initial and Delta is supported.
#XMSG
confirmRemoveReplicationObject=Do you confirm that you want to delete the replication?
#XMSG
confirmRemoveReplicationTaskPrompt=This action will delete existing replications. Do you want to continue?
#XMSG
confirmTargetConnectionChangePrompt=This action will reset the target connection, target container and delete all target objects. Do you want to continue ?
#XMSG
confirmTargetContainerChangePrompt=This action will reset the target container and delete all existing target objects. Do you want to continue?
#XMSG
confirmRemoveTransformObject=Do you confirm that you want to delete projection {0}?
#XMSG
ErrorMsgContainerChange=An error occurred while changing the container path.
#XMSG
infoForUnsupportedDatasetNoKeys=The following source objects are not supported because they do not have a primary key:
#XMSG
infoForUnsupportedDatasetView=The following source objects of type Views are not supported:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=The following source object is not supported as it’s an SQL view containing input parameters:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=The following source objects are not supported because extraction is disabled for them:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=For Confluent connections, the only allowed serialisation formats are AVRO and JSON. The following objects are not supported because they use a different serialisation format:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Unable to fetch the schema for the following objects. Please select the appropriate context or verify the schema registry configuration
#XTOL: warning dialog header on deleting replication task
deleteHeader=Delete
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=The Delete All Before Loading setting is not supported for Google BigQuery.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=The Delete All Before setting deletes and recreates the object (topic) before each replication. This also deletes all assigned messages.
#XTOL
DeleteAllBeforeLoadingLTFInfo=The Delete All Before setting is not supported for this target type.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Technical Name
#XCOL
connBusinessName=Business Name
#XCOL
connDescriptionName=Description
#XCOL
connType=Type
#XMSG
connTblNoDataFoundtxt=No Connections Found
#XMSG
connectionError=An error occurred while fetching connections.
#XMSG
connectionCombinationUnsupportedErrorTitle=Connection combination is not supported
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Replication from {0} to {1} is currently not supported.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Connection type combination is not supported
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Replication from a connection with the connection type SAP HANA Cloud, Data Lake Files to {0} is not supported. You can only replicate to SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Select
#XBUT
containerCancelBtn=Cancel
#XTOL
containerSelectTooltip=Select
#XTOL
containerCancelTooltip=Cancel
#XMSG
containerContainerPathPlcHold=Container Path
#XFLD
containerContainertxt=Container
#XFLD
confluentContainerContainertxt=Context
#XMSG
infoMessageForSLTSelection=Only /SLT/Mass Transfer ID is allowed as container. Select a Mass Transfer ID under SLT (if available) and click Submit.
#XMSG
msgFetchContainerFail=An error occurred while fetching container data.
#XMSG
infoMessageForSLTHidden=This connection does not support SLT folders, so they do not appear in the list below.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Select a container that contains subfolders in it.
#XMSG
sftpIncludeSubFolderText=False
#XMSG
sftpIncludeSubFolderTextNew=No

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(No filter mapping yet)
#XMSG
failToFetchRemoteMetadata=An error occurred while fetching metadata.
#XMSG
failToFetchData=An error occurred while fetching the existing target.
#XCOL
@loadType=Load Type
#XCOL
@deleteAllBeforeLoading=Delete All Before Loading

#XMSG
@loading=Loading...
#XFLD
@selectSourceObjects=Select Source Objects
#XMSG
@exceedLimit=You can’t import more than {0} objects at a time. Please deselect at least {1} objects.
#XFLD
@objects=Objects
#XBUT
@ok=OK
#XBUT
@cancel=Cancel
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Next
#XBUT
btnAddSelection=Add Selection
#XTOL
@remoteFromSelection=Remove from Selection
#XMSG
@searchInForSearchField=Search in {0}

#XCOL
@name=Technical Name
#XCOL
@type=Type
#XCOL
@location=Location
#XCOL
@label=Business Name
#XCOL
@status=Status

#XFLD
@searchIn=Search in:
#XBUT
@available=Available
#XBUT
@selection=Selection

#XFLD
@noSourceSubFolder=Tables and Views
#XMSG
@alreadyAdded=Already present in the diagram
#XMSG
@askForFilter=There are more than {0} items. Please enter a filter string to narrow down the number of items.
#XFLD: success label
lblSuccess=Success
#XFLD: ready label
lblReady=Ready
#XFLD: failure label
lblFailed=Failed
#XFLD: fetching status label
lblFetchingDetail=Fetching details

#XMSG Place holder text for tree filter control
filterPlaceHolder=Type text to filter top-level objects
#XMSG Place holder text for server search control
serverSearchPlaceholder=Type and press Enter to search
#XMSG
@deployObjects=Importing {0} objects...
#XMSG
@deployObjectsStatus=Number of objects that have been imported: {0}. Number of objects that could not be imported: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Failed to open local repository browser.
#XMSG
@openRemoteSourceBrowserError=Failed to fetch source objects.
#XMSG
@openRemoteTargetBrowserError=Failed to fetch target objects.
#XMSG
@validatingTargetsError=An error occurred while validating targets.
#XMSG
@waitingToImport=Ready to Import

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=The maximum number of objects has been exceeded. Select a maximum of 500 objects for one replication flow.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Technical Name
#XFLD
sourceObjectBusinessName=Business Name
#XFLD
sourceNoColumns=Number of Columns
#XFLD
containerLbl=Container

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=You must select a source connection for the replication flow.
#XMSG
validationSourceContainerNonExist=You must select a container for the source connection.
#XMSG
validationTargetNonExist=You must select a target connection for the replication flow.
#XMSG
validationTargetContainerNonExist=You must select a container for the target connection.
#XMSG
validationTruncateDisabledForObjectTitle=Replication to object storages.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Replication into a cloud storage is only possible if either the Delete All Before Loading option is set or the target object does not exist in the target.{0}{0} To still enable replication for objects for which the Delete All Before Loading option is not set, make sure that the target object does not exist in the system before you run the replication flow.
#XMSG
validationTaskNonExist=You must have at least one replication in the replication flow.
#XMSG
validationTaskTargetMissing=You must have a target for the replication with the source: {0}
#XMSG
validationTaskTargetIsSAC=Selected target is a SAC Artefact: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Selected target is not a supported local table: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=An object with this name already exists in the target. However, this object cannot be used as a target object for a replication flow to the local repository, as it is not a local table.
#XMSG
validateSourceTargetSystemDifference=You must select different source and target connection and container combinations for the replication flow.
#XMSG
validateDuplicateSources=one or more replications have duplicate source object names: {0}.
#XMSG
validateDuplicateTargets=one or more replications have duplicate target object names: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=The source object {0} does not support delta capture, while the target object {1} does. You must remove the replication.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=You must select the load type "Initial and Delta" for the replication with target object name {0}.
#XMSG
validationAutoRenameTarget=Target columns have been renamed.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=An auto-projection has been added, and the following target columns have been renamed to allow replication to the target:{1}{1} {0} {1}{1}This is due to one of the following reasons:{1}{1}{2} Not supported characters{1}{2} Reserved prefix
#XMSG
validationAutoRenameTargetDescriptionUpdated=An auto-projection has been added, and the following target columns have been renamed to allow replication to Google BigQuery:{1}{1} {0} {1}{1}This is due to one of the following reasons:{1}{1}{2} Reserved column name{1}{2} Not supported characters{1}{2} Reserved prefix
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=An auto-projection has been added, and the following target columns have been renamed to allow replications to Confluent:{1}{1} {0} {1}{1}This is due to one of the following reasons:{1}{1}{2} Reserved column name{1}{2} Not supported characters{1}{2} Reserved prefix
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=An auto-projection has been added, and the following target columns have been renamed to allow replications to the target:{1}{1} {0} {1}{1}This is due to one of the following reasons:{1}{1}{2} Reserved column name{1}{2} Not supported characters{1}{2} Reserved prefix
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Target object has been renamed.
#XMSG
autoRenameInfoDesc=The target object has been renamed because it contained not supported characters. Only the following characters are supoorted:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(dot){0}{1}_(underscore){0}{1}-(dash)
#XMSG
validationAutoTargetTypeConversion=Target data types have been changed.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=For the following target columns, the target data types have been changed because in Google BigQuery the source data types are not supported:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=For the following target columns, the target data types have been changed because the source data types are not supported in the target connection:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Shorten target column names.
#XMSG
validationMaxCharLengthGBQTargetDescription=In Google BigQuery, column names can use a maximum of 300 characters. Use a projection to shorten the following target column names:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Primary keys will not be created.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=In Google BigQuery, a maximum of 16 primary keys are supported, but the source object has a larger number of primary keys. None of the primary keys will be created in the target object.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=One or more source columns have data types that cannot be defined as primary keys in Google BigQuery. None of the primary keys will be created in the target object.{0}{0}The following target data types are compatible with Google BigQuery data types for which a primary key can be defined:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Define one or more columns as a primary key.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=You must define one or more column as primary key use source schema dialogue to do this.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Define one or more columns as a primary key.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=You must define one or more columns as primary key that match the primary key constraints for your source object. Go to Configure Schema in your source object properties to do so.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Enter a valid max partition value.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Max Partition value must be ≥ 1 and ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Define one or more columns as a primary key.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=To replicate an object, you must define one or more target columns as a primary key. Use a projection to do this.
#XMSG
validateHDLFNoPKExistingDatasetError=Define one or more columns as a primary key.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=To replicate data to an existing target object, it must have one or more columns that are defined as the primary key. {0} You have the following options for defining one or more columns as the primary key: {0} {1} Use the local table editor to change the existing target object. Then reload the replication flow.{0}{1} Rename the target object in the replication flow. This will create a new object as soon as a run is started. After renaming, you can define one or more columns as the primary key in a projection.{0}{1} Map the object to another existing target object in which one or more columns are already defined as the primary key.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Selected target already exists in the repository: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=The delta capture table names are already used by other tables in the repository: {0}. You must rename these target objects to ensure that the associated delta capture table names are unique before you can save the replication flow.
#XMSG
validateConfluentEmptySchema=Define Schema
#XMSG
validateConfluentEmptySchemaDescUpdated=The source table does not have a schema. Choose Configure Schema to define one
#XMSG
validationCSVEncoding=Invalid CSV Encoding
#XMSG
validationCSVEncodingDescription=The CSV encoding of the task is not valid.
#XMSG
validateConfluentEmptySchema=Select a compatible target data type
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Select a compatible target data type
#XMSG
globalValidateTargetDataTypeDesc=An error occurred with column mappings. Go to Projection and ensure that all source columns are mapped with a unique column, with a column that has a compatible data type, and that all defined expressions are valid.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Duplicate Column Names.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Duplicate Column names are not supported. Use the Projection Dialogue to fix them. The following target objects have duplicate column names: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Duplicate Column Names.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Duplicate Column names are not supported. The following target objects have duplicate column names: {0}.
#XMSG
deltaOnlyLoadTypeTittle=There may be inconsistencies in the data.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=The Delta Only load type will not consider the changes made in the source between the last save and the next run.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Change load type to "Initial".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Replicating ABAP-based objects that do not have a primary key is only possible for load type "Initial Only".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Disable Delta Capture.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=To replicate an object that does not have a primary key using source connection type ABAP, you must first disable delta capturing for this table.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Change target object.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=The target object cannot be used because delta capturing is enabled. You can either rename the target object and then switch off delta capturing for the new (renamed) object, or map the source object to a target object for which delta capturing is disabled.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Change target object.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=The target object cannot be used because it does not have the required technical column __load_package_id. You can rename the target object using a name that does not exist yet. The system then creates a new object that has the same definition as the source object and contains the technical column. Alternatively, you can map the target object to an existing object that has the required technical column (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Change target object.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=The target object cannot be used because it does not have the required technical column __load_record_id. You can rename the target object using a name that does not exist yet. The system then creates a new object that has the same definition as the source object and contains the technical column. Alternatively, you can map the target object to an existing object that has the required technical column (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Change target object.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=The target object cannot be used because the data type of its technical column __load_record_id is not "string(44)". You can rename the target object using a name that does not exist yet. The system then creates a new object that has the same definition as the source object and consequently the correct data type. Alternatively, you can map the target object to an existing object that has the required technical column (__load_record_id) with the correct data type.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Change target object.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=The target object cannot be used because it has a primary key, while the source object has none. You can rename the target object using a name that does not exist yet. The system then creates a new object that has the same definition as the source object and consequently no primary key. Alternatively, you can map the target object to an existing object that has the required technical column (__load_package_id) and does not have a primary key.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Change target object.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=The target object cannot be used because it has a primary key, while the source object has none. You can rename the target object using a name that does not exist yet. The system then creates a new object that has the same definition as the source object and consequently no primary key. Alternatively, you can map the target object to an existing object that has the required technical column (__load_record_id) and does not have a primary key.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Change target object.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=The target object cannot be used because the data type of its technical column __load_package_id is not "binary(>=256)". You can rename the target object using a name that does not exist yet. The system then creates a new object that has the same definition as the source object and consequently the correct data type. Alternatively, you can map the target object to an existing object that has the required technical column (__load_package_id) with the correct data type.
#XMSG
validationAutoRenameTargetDPID=Target columns have been renamed.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Remove source object.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=The source object does not have a key column, which is not supported in this context.
#XMSG
validationAutoRenameTargetDPIDDescription=An auto-projection has been added, and the following taget columns have been renamed to allow replication from ABAP source without keys :{1}{1} {0} {1}{1}This is due to one of the following reasons:{1}{1}{2} Reserved column name{1}{2} Not supported characters{1}{2} Reserved prefix
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Replication to {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Saving and deploying replication flows that have {0} as their target is currently not possible because we are performing maintenance on this function.
#XMSG
TargetColumnSkippedLTF=Target column has been skipped.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Target column has been skipped due to unsupported data type. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Time Column as Primary Key.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=The source object has a time column as a primary key, which is not supported in this context.
#XMSG
validateNoPKInLTFTarget=Primary Key missing.
#XMSG
validateNoPKInLTFTargetDescription=Primary key is not defined in the target, which is not supported in this context.
#XMSG
validateABAPClusterTableLTF=ABAP Cluster Table.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=The source object is an ABAP cluster table, which is not supported in this context.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=It looks like you haven’t added any data yet.
#YINS
welcomeText2=To start your replication flow, select a connection and a source object on the left side.

#XBUT
wizStep1=Select Source Connection
#XBUT
wizStep2=Select Source Container
#XBUT
wizStep3=Add Source Objects

#XMSG
limitDataset=The maximum number of objects has been reached. Remove existing objects to add new ones, or create a new replication flow.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=The replication flow to this non-SAP target connection cannot be started because there is no outbound volume available for this month.
#XMSG
premiumOutBoundRFAdminWarningMsg=An administrator can increase the Premium Outbound blocks for this tenant, making outbound volume available for this month.
#XMSG
messageForToastForDPIDColumn2=New column added to target for {0} objects - needed for handling duplicate records in connection with ABAP-based source objects that do not have a primary key.
#XMSG
PremiumInboundWarningMessage=Depending on the number of replication flows and the data volume to be replicated, the SAP HANA resources{0}required for replicating data through {1} may exceed the available capacity for your tenant.
#XMSG
PremiumInboundWarningMsg=Depending on the number of replication flows and the data volume to be replicated,{0}the SAP HANA resources required for replicating data through "{1}" may exceed the available capacity for your tenant.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Enter a projection name.
#XMSG
emptyTargetColumn=Enter a target column name.
#XMSG
emptyTargetColumnBusinessName=Enter a target column Business Name.
#XMSG
invalidTransformName=Enter a projection name.
#XMSG
uniqueColumnName=Rename target column.
#XMSG
copySourceColumnLbl=Copy columns from source object
#XMSG
renameWarning=Make sure to choose a unique name while renaming the target table. If the table with the new name already exists in the space, it will use the definition of that table.

#XMSG
uniqueColumnBusinessName=Rename target column business name.
#XMSG
uniqueSourceMapping=Select a different source column.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=The source column {0} is already used by the following target columns:{1}{1}{2}{1}{1} For this target column or for the other target columns, select a source column that is not already in use to save the projection.
#XMSG
uniqueColumnNameDescription=The target column name you entered already exists. To be able to save the projection, you need to enter a unique column name.
#XMSG
uniqueColumnBusinessNameDesc=The target column business name already exists. To save the projection, you must enter a unique column business name.
#XMSG
emptySource=Select a source column or enter a constant.
#XMSG
emptySourceDescription=To create a valid mapping entry, you need to select a source column or enter a constant value.
#XMSG
emptyExpression=Define mapping.
#XMSG
emptyExpressionDescription1=Either select the source column to which you want to map the target column, or select the checkbox in the column {0} Functions / Constants {1}. {2} {2} Functions are entered automatically according to the target data type. Constant values can be entered manually.
#XMSG
numberExpressionErr=Enter a number.
#XMSG
numberExpressionErrDescription=You selected a numeric data type. This means that you can only enter numerals, plus the decimal point if applicable. Do not use single quotation marks.
#XMSG
invalidLength=Enter a valid length value.
#XMSG
invalidLengthDescription=The length of the data type must be equal to or greater than the length of the source column and can be between 1 and 5000.
#XMSG
invalidMappedLength=Enter a valid length value.
#XMSG
invalidMappedLengthDescription=The length of the data type must be equal to or greater than the length of the source column {0} and can be between 1 and 5000.
#XMSG
invalidPrecision=Enter a valid precision value.
#XMSG
invalidPrecisionDescription=Precision defines the total number of digits. Scale defines the number of digits after the decimal point and can be between 0 and precision.{0}{0} Examples: {0}{1} Precision 6, scale 2 corresponds to numbers like 1234.56.{0}{1} Precision 6, scale 6 corresponds to numbers like 0.123546.{0} {0} Precision and scale for the target must be compatible with precision and scale for the source so that all digits from the source fit into the target field. For example, if you have precision 6 and scale 2 in the source (and consequently digits other than 0 before the decimal point), you cannot have precision 6 and scale 6 in the target.
#XMSG
invalidPrimaryKey=Enter at least one primary key.
#XMSG
invalidPrimaryKeyDescription=Primary key not defined for this schema.
#XMSG
invalidMappedPrecision=Enter a valid precision value.
#XMSG
invalidMappedPrecisionDescription1=Precision defines the total number of digits. Scale defines the number of digits after the decimal point and can be between 0 and precision.{0}{0} Examples:{0}{1} Precision 6, scale 2 corresponds to numbers like 1234.56.{0}{1} Precision 6, scale 6 corresponds to numbers like 0.123546.{0}{0}The precision of the data type must be equal to or greater than the precision of the source ({2}).
#XMSG
invalidScale=Enter a valid scale value.
#XMSG
invalidScaleDescription=Precision defines the total number of digits. Scale defines the number of digits after the decimal point and can be between 0 and precision.{0}{0} Examples: {0}{1} Precision 6, scale 2 corresponds to numbers like 1234.56.{0}{1} Precision 6, scale 6 corresponds to numbers like 0.123546.{0} {0} Precision and scale for the target must be compatible with precision and scale for the source so that all digits from the source fit into the target field. For example, if you have precision 6 and scale 2 in the source (and consequently digits other than 0 before the decimal point), you cannot have precision 6 and scale 6 in the target.
#XMSG
invalidMappedScale=Enter a valid scale value.
#XMSG
invalidMappedScaleDescription1=Precision defines the total number of digits. Scale defines the number of digits after the decimal point and can be between 0 and precision.{0}{0} Examples: {0}{1} Precision 6, scale 2 corresponds to numbers like 1234.56.{0}{1} Precision 6, scale 6 corresponds to numbers like 0.123546.{0}{0} The scale of the data type must be equal to or greater than the scale of the source ({2}).
#XMSG
nonCompatibleDataType=Select a compatible target data type.
#XMSG
nonCompatibleDataTypeDescription1=The data type that you specify here must be compatible with the source data type ({0}). {1}{1} Example: If your source column has data type string and contains letters, you cannot use a decimal data type for your target.
#XMSG
invalidColumnCount=Select a source column.
#XMSG
ObjectStoreInvalidScaleORPrecision=Enter a valid value for precision and scale.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=The first value is the precision, which defines the total number of digits. The second value is the scale, which defines the digits after the decimal point. Enter a target scale value that is greater than the source scale value and make sure that the difference between the entered target scale and precision value is greater than the difference between the source scale and precision value.
#XMSG
InvalidPrecisionORScale=Enter a valid value for precision and scale.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=The first value is the precision, which defines the total number of digits. The second value is the scale, which defines the digits after the decimal point.{0}{0}Since the source data type is not supported in Google BigQuery, it is converted to the target data type DECIMAL. In this case, the precision can only be defined between 38 and 76, and the scale between 9 and 38. Moreover, the result of precision minus scale, which represents the digits before the decimal point, must be between 29 and 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=The first value is the precision, which defines the total number of digits. The second value is the scale, which defines the digits after the decimal point.{0}{0}Since the source data type is not supported in Google BigQuery, it is converted to the target data type DECIMAL. In this case, the precision must be defined as 20 or greater. In addition, the result of precision minus scale, which reflects the digits before the decimal point, must be 20 or greater.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=The first value is the precision, which defines the total number of digits. The second value is the scale, which defines the digits after the decimal point.{0}{0}Since the source data type is not supported in the target, it is converted to the target data type DECIMAL. In this case, the precision must be defined by any number greater than or equal to 1 and less than or equal to 38, and the scale less than or equal to precision.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=The first value is the precision, which defines the total number of digits. The second value is the scale, which defines the digits after the decimal point.{0}{0}Since the source data type is not supported in the target, it is converted to the target data type DECIMAL. In this case, the precision must be defined as 20 or greater. In addition, the result of precision minus scale, which reflects the digits before the decimal point, must be 20 or greater.
#XMSG
invalidColumnCountDescription=To create a valid mapping entry, you need to select a source column or enter a constant value.
#XMSG
duplicateColumns=Rename target column.
#XMSG
duplicateGBQCDCColumnsDesc=The target column name is reserved in Google BigQuery. You need to rename it to be able to save the projection.
#XMSG
duplicateConfluentCDCColumnsDesc=The target column name is reserved in Confluent. You need to rename it to be able to save the projection.
#XMSG
duplicateSignavioCDCColumnsDesc=The target column name is reserved in SAP Signavio. You need to rename it to be able to save the projection.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=The target column name is reserved in MS OneLake. You need to rename it to be able to save the projection.
#XMSG
duplicateSFTPCDCColumnsDesc=The target column name is reserved in SFTP. You need to rename it to be able to save the projection.
#XMSG
GBQTargetNameWithPrefixUpdated1=The target column name contains a prefix that is reserved in Google BigQuery. You need to rename it to be able to save the projection. {0}{0}The target column name cannot begin with any of the following strings:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Shorten target column name.
#XMSG
GBQtargetMaxLengthDesc=In Google BigQuery, a column name can use a maximum of 300 characters. Shorten the target column name to be able to save the projection.
#XMSG
invalidMappedScalePrecision=Precision and scale for the target must be compatible with precision and scale for the source so that all digits from the source fit into the target field.
#XMSG
invalidMappedScalePrecisionShortText=Enter a valid precision and scale value.
#XMSG
validationIncompatiblePKTypeDescProjection3=One or more source columns have data types that cannot be defined as primary keys in Google BigQuery. None of the primary keys will be created in the target object.{0}{0}The following target data types are compatible with Google BigQuery data types for which a primary key can be defined:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Untick column __message_id.
#XMSG
uncheckColumnMessageIdDesc=Column: Primary Key
#XMSG
validationOpCodeInsert=You must enter a value for Insert.
#XMSG
recommendDifferentPrimaryKey=We recommend that you select a different primary key at the item level.
#XMSG
recommendDifferentPrimaryKeyDesc=When the operation code is already defined, it is recommended to select different primary keys for the array index and the items, to avoid problems such as column duplication for example.
#XMSG
selectPrimaryKeyItemLevel=You must select at least one primary key for both the header and the item level.
#XMSG
selectPrimaryKeyItemLevelDesc=When an array or a map is expanded, you must select two primary keys, one at the header level and one at the item level.
#XMSG
invalidMapKey=You must select at least one primary key at the header level.
#XMSG
invalidMapKeyDesc=When an array or a map is expanded, you must select a primary key at the header level.
#XFLD
txtSearchFields=Search Target Columns
#XFLD
txtName=Name
#XMSG
txtSourceColValidation=One or more source columns are not supported:
#XMSG
txtMappingCount=Mappings ({0})
#XMSG
schema=Schema
#XMSG
sourceColumn=Source Columns
#XMSG
warningSourceSchema=Any change made to the schema will affect mappings in projection dialogue.
#XCOL
txtTargetColName=Target Column (Technical Name)
#XCOL
txtDataType=Target Data Type
#XCOL
txtSourceDataType=Source Data Type
#XCOL
srcColName=Source Column (Technical Name)
#XCOL
precision=Precision
#XCOL
scale=Scale
#XCOL
functionsOrConstants=Functions / Constants
#XCOL
txtTargetColBusinessName=Target Column (Business Name)
#XCOL
prKey=Primary Key
#XCOL
txtProperties=Properties
#XBUT
txtOK=Save
#XBUT
txtCancel=Cancel
#XBUT
txtRemove=Remove
#XFLD
txtDesc=Description
#XMSG
rftdMapping=Mapping
#XFLD
@lblColumnDataType=Data Type
#XFLD
@lblColumnTechnicalName=Technical Name
#XBUT
txtAutomap=Auto-Map
#XBUT
txtUp=Up
#XBUT
txtDown=Down

#XTOL
txtTransformationHeader=Projection
#XTOL
editTransformation=Edit
#XTOL
primaryKeyToolip=Key


#XMSG
rftdFilter=Filter
#XMSG
rftdFilterColumnCount=Source: {0}({1})
#XTOL
rftdFilterColSearch=Search
#XMSG
rftdFilterColNoData=No columns to display
#XMSG
rftdFilteredColNoExps=No filter expressions
#XMSG
rftdFilterSelectedColTxt=Add Filter for
#XMSG
rftdFilterTxt=Filter available for
#XBUT
rftdFilterSelectedAddColExp=Add Expression
#YINS
rftdFilterNoSelectedCol=Select a column to add filter.
#XMSG
rftdFilterExp=Filter Expression
#XMSG
rftdFilterNotAllowedColumn=Adding filters is not supported for this column.
#XMSG
rftdFilterNotAllowedHead=Not Supported Column
#XMSG
rftdFilterNoExp=No filter has been defined
#XTOL
rftdfilteredTt=Filtered
#XTOL
rftdremoveexpTt=Remove filter expression
#XTOL
validationMessageTt=Validation Messages
#XTOL
rftdFilterDateInp=Select a date
#XTOL
rftdFilterDateTimeInp=Select a date time
#XTOL
rftdFilterTimeInp=Select a time
#XTOL
rftdFilterInp=Enter a value
#XMSG
rftdFilterValidateEmptyMsg={0} filter expressions on {1} column are empty
#XMSG
rftdFilterValidateInvalidNumericMsg={0} filter expressions on {1} column contain invalid numeric values
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Filter expression must contain valid numeric values
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=If target object schema has changed, use the function “Map to Existing Target Object” on the main page to adapt the changes, and remap the target object with its source again.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=If the target table already exists and the mapping includes a schema change, you must change the target table accordingly before deploying the replication flow.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=If your mapping involves a schema change, you must change the target table accordingly before deploying the replication flow.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=The following unsupported columns were skipped from source definition: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=The following unsupported columns were skipped from target definition: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=The following objects are not supported because they are exposed for consumption: {0} {1} {0} {0} To use tables in a replication flow, the semantic usage (in the table settings) must not be set to {2}Analytical Dataset{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=The target object cannot be used because it is exposed for consumption. {0} {0}  To use the table in a replication flow, the semantic usage (in the table settings) must not be set to {1}Analytical Dataset{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=A target object with this name already exists. However, it cannot be used because it is exposed for consumption. {0} {0} To use the table in a replication flow, the semantic usage (in the table settings) must not be set to {1}Analytical Dataset{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=An object with this name already exists in the target. {0}However, this object cannot be used as a target object for a replication flow to the local repository, as it is not a local table.
#XMSG:
targetAutoRenameUpdated=Target column has been renamed.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=The target column has been renamed to allow replications in Google BigQuery. This is due to one of the following reasons:{0} {1}{2}Reserved column name{3}{2}Not supported characters{3}{2}Reserved prefix{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=The target column has been renamed to allow replications in Confluent. This is due to one of the following reasons:{0} {1}{2}Reserved column name{3}{2}Not supported characters{3}{2}Reserved prefix{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=The target column has been renamed to allow replications to the target. This is due to one of the following reasons:{0} {1}{2}Not supported characters{3}{2}Reserved prefix{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=The target column has been renamed to allow replications to the target. This is due to one of the following reasons:{0} {1}{2}Reserved column name{3}{2}Not supported characters{3}{2}Reserved prefix{3}{4}
#XMSG:
targetAutoDataType=Target data type has been changed.
#XMSG:
targetAutoDataTypeDesc=The target data type has been changed to {0} because the source data type is not supported in Google BigQuery.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=The target data type has been changed to {0} because the source data type is not supported in the target connection.
#XMSG
projectionGBQUnableToCreateKey=Primary keys will not be created.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=In Google BigQuery, a maximum of 16 primary keys are supported, but the source object has a larger number of primary keys. None of the primary keys will be created in the target object.
#XMSG
HDLFNoKeyError=Define one or more columns as a primary key.
#XMSG
HDLFNoKeyErrorDescription=To replicate an object, you must define one or more columns as a primary key.
#XMSG
HDLFNoKeyErrorExistingTarget=Define one or more columns as a primary key.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=To replicate data to an existing target object, it must have one or more columns that are defined as the primary key. {0} {0} You have the following options for defining one or more columns as the primary key: {0}{0}{1} Use the local table editor to change the existing target object. Then reload the replication flow.{0}{0}{1} Rename the target object in the replication flow. This will create a new object as soon as a run is started. After renaming, you can define one or more columns as the primary key in a projection.{0}{0}{1} Map the object to another existing target object in which one or more columns are already defined as the primary key.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Primary key changed.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=Compared to the source object, you have defined different columns as the primary key for the target object. Make sure that these columns uniquely identify all rows to avoid possible data corruption when replicating the data later. {0} {0} In the source object, the following columns are defined as the primary key: {0} {1}
#XMSG
duplicateDPIDColumns=Rename target column.
#XMSG
duplicateDPIDDColumnsDesc1=This target column name is reserved for a technical column. Enter a different name to save the projection.
#XMSG:
targetAutoRenameDPID=Target column has been renamed.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=The target column has been renamed to allow replications from ABAP source without keys. This is due to one of the following reasons:{0} {1}{2}Reserved column name{3}{2}Not supported characters{3}{2}Reserved prefix{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle={0} Target Settings
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle={0} Source Settings
#XBUT
connectionSettingSave=Save
#XBUT
connectionSettingCancel=Cancel
#XBUT: Button to keep the object level settings
txtKeep=Keep
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Overwrite
#XFLD
targetConnectionThreadlimit=Target Thread Limit for Initial Load (1-100)
#XFLD
connectionThreadLimit=Source Thread Limit for Initial Load (1-100)
#XFLD
maxConnection=Replication Thread Limit (1-100)
#XFLD
kafkaNumberOfPartitions=Number of Partitions
#XFLD
kafkaReplicationFactor=Replication Factor
#XFLD
kafkaMessageEncoder=Message Encoder
#XFLD
kafkaMessageCompression=Message Compression
#XFLD
fileGroupDeltaFilesBy=Group Delta by
#XFLD
fileFormat=File Type
#XFLD
csvEncoding=CSV Encoding
#XFLD
abapExitLbl=ABAP Exit
#XFLD
deltaPartition=Object Thread Count for Delta Loads (1-10)
#XFLD
clamping_Data=Fail on Data Truncation
#XFLD
fail_On_Incompatible=Fail on Incompatible Data
#XFLD
maxPartitionInput=Max Number of Partitions
#XFLD
max_Partition=Define Max. Number of Partitions
#XFLD
include_SubFolder=Include Subfolders
#XFLD
fileGlobalPattern=Global Pattern for File Name
#XFLD
fileCompression=File Compression
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=File Delimiter
#XFLD
fileIsHeaderIncluded=File Header
#XFLD
fileOrient=Orient
#XFLD
gbqWriteMode=Write Mode
#XFLD
suppressDuplicate=Suppress Duplicates
#XFLD
apacheSpark=Enable Apache Spark Compatibility
#XFLD
clampingDatatypeCb=Clamp Decimal Floating Point Data Types
#XFLD
overwriteDatasetSetting=Overwrite Target Settings at Object Level
#XFLD
overwriteSourceDatasetSetting=Overwrite Source Settings at Object Level
#XMSG
kafkaInvalidConnectionSetting=Enter the number between {0} and {1}.
#XMSG
MinReplicationThreadErrorMsg=Enter a number greater than {0}.
#XMSG
MaxReplicationThreadErrorMsg=Enter a number lower than {0}.
#XMSG
DeltaThreadErrorMsg=Enter a value between 1 and 10.
#XMSG
MaxPartitionErrorMsg=Enter value between 1 <= x <= 2147483647.The default value is 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Enter an integer between {0} and {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Use Replication Factor of the Broker
#XFLD
serializationFormat=Serialisation Format
#XFLD
compressionType=Compression Type
#XFLD
schemaRegistry=Use Schema Registry
#XFLD
subjectNameStrat=Subject Name Strategy
#XFLD
compatibilityType=Compatibility Type
#XFLD
confluentTopicName=Topic Name
#XFLD
confluentRecordName=Record Name
#XFLD
confluentSubjectNamePreview=Subject Name Preview
#XMSG
serializationChangeToastMsgUpdated2=Serialisation format changed to JSON as schema registry is not enabled. To change the serialisation format back to AVRO, you must enable schema registry first.
#XBUT
confluentTopicNameInfo=The topic name is always based on the target object name. You can change it by renaming the target object.
#XMSG
emptyRecordNameValidationHeaderMsg=Enter a record name.
#XMSG
emptyPartionHeader=Enter the number of partitions.
#XMSG
invalidPartitionsHeader=Enter a valid number of partitions.
#XMSG
invalidpartitionsDesc=Enter a number between 1 and 200,000.
#XMSG
emptyrFactorHeader=Enter a replication factor.
#XMSG
invalidrFactorHeader=Enter a valid replication factor.
#XMSG
invalidrFactorDesc=Enter a number between 1 and 32,767.
#XMSG
emptyRecordNameValidationDescMsg=If the serialisation format "AVRO" is used, only the following characters are supported:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(underscore)
#XMSG
validRecordNameValidationHeaderMsg=Enter a valid record name.
#XMSG
validRecordNameValidationDescMsgUpdated=Because the serialisation format "AVRO" is used, the record name must consist of only alphanumeric (A-Z, a-z, 0-9) and underscore (_) characters. It must start with a letter or an underscore.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=The “Object Thread Count for Delta Loads” can be set as soon as one or more objects have the load type “Initial and Delta”.
#XMSG
invalidTargetName=Invalid column name
#XMSG
invalidTargetNameDesc=The target column name must consist of only alphanumeric (A-Z, a-z, 0-9) and underscore (_) characters.
#XFLD
consumeOtherSchema=Consume Other Schema Versions
#XFLD
ignoreSchemamissmatch=Ignore Schema Missmatch
#XFLD
confleuntDatatruncation=Failed on Data Truncation
#XFLD
isolationLevel=Isolation Level
#XFLD
confluentOffset=Starting Point
#XFLD
signavioGroupDeltaFilesByText=None
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=No
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=No

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Projections
#XBUT
txtAdd=Add
#XBUT
txtEdit=Edit
#XMSG
transformationText=Add a projection to set up filter or mapping.
#XMSG
primaryKeyRequiredText=Select a primary key with Configure Schema.
#XFLD
lblSettings=Settings
#XFLD
lblTargetSetting={0}: Target Settings
#XMSG
@csvRF=Select the file that contains the schema definition you want to apply to all files in the folder.
#XFLD
lblSourceColumns=Source Columns
#XFLD
lblJsonStructure=JSON Structure
#XFLD
lblSourceSetting={0}: Source Settings
#XFLD
lblSourceSchemaSetting={0}: Source Schema Settings
#XBUT
messageSettings=Message Settings
#XFLD
lblPropertyTitle1=Object Properties
#XFLD
lblRFPropertyTitle=Replication Flow Properties
#XMSG
noDataTxt=There are no columns to display.
#XMSG
noTargetObjectText=There is no target object selected.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Target Columns
#XMSG
searchColumns=Search Columns
#XTOL
cdcColumnTooltip=Column for Delta Capture
#XMSG
sourceNonDeltaSupportErrorUpdated=The source object does not support delta capture.
#XMSG
targetCDCColumnAdded=2 target columns were added for delta capture.
#XMSG
deltaPartitionEnable=Object Thread Limit for Delta Loads added to the source settings.
#XMSG
attributeMappingRemovalTxt=Removing invalid mappings which are not supported for new target object.
#XMSG
targetCDCColumnRemoved=2 target columns used for delta capture were removed.
#XMSG
replicationLoadTypeChanged=Load type changed to "Initial and Delta".
#XMSG
sourceHDLFLoadTypeError=Change load type to "Initial and Delta".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=To replicate an object from a source connection with the connection type SAP HANA Cloud, Data Lake Files to SAP Datasphere, you must use the load type "initial and delta".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Enable Delta Capture.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=To replicate an object from a source connection with the connection type SAP HANA Cloud, Data Lake Files to SAP Datasphere, you must enable delta capture.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Change target Object.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=The target object cannot be used because delta capture is disabled. You can either rename the target object (which allows a new object with delta capture to be created) or map it to an existing object with delta capture enabled.
#XMSG
deltaPartitionError=Enter a valid object thread count for delta loads.
#XMSG
deltaPartitionErrorDescription=Enter a value between 1 and 10.
#XMSG
deltaPartitionEmptyError=Enter an object thread count for delta loads.
#XFLD
@lblColumnDescription=Description
#XMSG
@lblColumnDescriptionText1=For technical purposes - handling of duplicate records caused by issues during replication of ABAP-based source objects that do not have a primary key.
#XFLD
storageType=Storage
#XFLD
skipUnmappedColLbl=Skip Unmapped Columns
#XFLD
abapContentTypeLbl=Content Type
#XFLD
autoMergeForTargetLbl=Merge Data Automatically
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=General
#XFLD
lblBusinessName=Business Name
#XFLD
lblTechnicalName=Technical Name
#XFLD
lblPackage=Package
#XFLD
statusPanel=Run Status
#XBTN: Schedule dropdown menu
SCHEDULE=Schedule
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Edit Schedule
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Delete Schedule
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Create Schedule
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Failed schedule validation check
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=A schedule cannot be created because the replication flow is currently being deployed.{0}Please wait until the replication flow has been deployed.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=For replication flows that contain objects with load type "Initial and Delta", no schedule can be created.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=For replication flows that contain objects with load type "Initial and Delta/Delta Only", no schedule can be created.
#XFLD : Scheduled popover
SCHEDULED=Scheduled
#XFLD
CREATE_REPLICATION_TEXT=Create a Replication Flow
#XFLD
EDIT_REPLICATION_TEXT=Edit a Replication Flow
#XFLD
DELETE_REPLICATION_TEXT=Delete a Replication Flow
#XFLD
REFRESH_FREQUENCY=Frequency
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=The replication flow cannot be deployed because the existing schedule{0} does not yet support the load type "Initial and Delta".{0}{0}To deploy the replication flow, you must set the load types of all objects{0} to "Initial only". Alternatively, you can delete the schedule, deploy the {0}replication flow, and then start a new run. This results in a run without {0}end, which also supports objects with the load type "Initial and Delta".
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=The replication flow cannot be deployed because the existing schedule{0} does not yet support the load type "Initial and Delta/Delta Only".{0}{0}To deploy the replication flow, you must set the load types of all objects{0} to "Initial only". Alternatively, you can delete the schedule, deploy the {0}replication flow and then start a new run. This results in a run without {0}end, which also supports objects with the load type "Initial and Delta/Delta Only".
#XMSG
SCHEDULE_EXCEPTION=Failed getting schedule details
#XFLD: Label for frequency column
everyLabel=Every
#XFLD: Plural Recurrence text for Hour
hoursLabel=Hours
#XFLD: Plural Recurrence text for Day
daysLabel=Days
#XFLD: Plural Recurrence text for Month
monthsLabel=Months
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutes
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Failed to get information about schedule possibility.
#XFLD :Paused field
PAUSED=Paused
#XMSG
navToMonitoring=Open in Flows Monitor
#XFLD
statusLbl=Status
#XFLD
lblLastRunExecuted=Last Run Start
#XFLD
lblLastExecuted=Last Run
#XFLD: Status text for Completed
statusCompleted=Completed
#XFLD: Status text for Running
statusRunning=Running
#XFLD: Status text for Failed
statusFailed=Failed
#XFLD: Status text for Stopped
statusStopped=Stopped
#XFLD: Status text for Stopping
statusStopping=Stopping
#XFLD: Status text for Active
statusActive=Active
#XFLD: Status text for Paused
statusPaused=Paused
#XFLD: Status text for not executed
lblNotExecuted=Not Run Yet
#XFLD
messagesSettings=Messages Settings
#XTOL
@validateModel=Validation Messages
#XTOL
@hierarchy=Hierarchy
#XTOL
@columnCount=Number of Columns
#XMSG
VAL_PACKAGE_CHANGED=You have assigned this object to package "{1}". Click “Save” to confirm and validate this change. Note that assignment to a package cannot be undone in this editor after you save.
#XMSG
MISSING_DEPENDENCY=Dependencies of object "{0}" cannot be resolved in package "{1}".
#XFLD
deltaLoadInterval=Delta Load Interval
#XFLD
lblHour=Hours (0-24)
#XFLD
lblMinutes=Minutes (0-59)
#XMSG
maxHourOrMinErr=Enter a value between 0 and {0}
#XMSG
maxDeltaInterval=The maximum value of the delta load interval is 24 hours.{0}Change the minute value or the hour value accordingly.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Target Container Path
#XFLD
confluentSubjectName=Subject Name
#XFLD
confluentSchemaVersion=Schema Version
#XFLD
confluentIncludeTechKeyUpdated=Include Technical Key
#XFLD
confluentOmitNonExpandedArrays=Omit Non-Expanded Arrays
#XFLD
confluentExpandArrayOrMap=Expand Array or Map
#XCOL
confluentOperationMapping=Operation Mapping
#XCOL
confluentOpCode=Opcode
#XFLD
confluentInsertOpCode=Insert
#XFLD
confluentUpdateOpCode=Update
#XFLD
confluentDeleteOpCode=Delete
#XFLD
expandArrayOrMapNotSelectedTxt=Not Selected
#XFLD
confluentSwitchTxtYes=Yes
#XFLD
confluentSwitchTxtNo=No
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Error
#XTIT
executeWarning=Warning
#XMSG
executeunsavederror=Save your replication flow before you run it.
#XMSG
executemodifiederror=There are unsaved changes in the replication flow. Please save the replication flow.
#XMSG
executeundeployederror=You must deploy your replication flow before you can run it.
#XMSG
executedeployingerror=Please wait for the deployment to finish.
#XMSG
msgRunStarted=Run started
#XMSG
msgExecuteFail=Failed to run the replication flow
#XMSG
titleExecuteBusy=Please wait.
#XMSG
msgExecuteBusy=We are preparing your data to run the replication flow.
#XTIT
executeConfirmDialog=Warning
#XMSG
msgExecuteWithValidations=Replication flow has validation errors. Running the replication flow may result in failure.
#XMSG
msgRunDeployedVersion=There are changes to deploy. The last deployed version of the replication flow will be run. Do you want to continue?
#XBUT
btnExecuteAnyway=Run Anyway
#XBUT
btnExecuteClose=Close
#XBUT
loaderClose=Close
#XTIT
loaderTitle=Loading
#XMSG
loaderText=Fetching details from the server
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=The replication flow to this non-SAP target connection cannot be started
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=because there is no outbound volume available for this month.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=An administrator can increase the Premium Outbound blocks for this
#XMSG
premiumOutBoundRFAdminErrMsgPart2=tenant, making outbound volume available for this month.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Error
#XTIT
deployInfo=Information
#XMSG
deployCheckFailException=Exception occurred during deployment
#XMSG
deployGBQFFDisabled=Deploying replication flows with a target connection to Google BigQuery is currently not possible because we are performing maintenance on this function.
#XMSG
deployKAFKAFFDisabled=Deploying replication flows with a target connection to Apache Kafka is currently not possible because we are performing maintenance on this function.
#XMSG
deployConfluentDisabled=Deploying replication flows with a target connection to Confluent Kafka is currently not possible because we are performing maintenance on this function.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=For the following target objects, the delta capture table names are already used by other tables in the repository: {0} You must rename these target objects to ensure that the associated delta capture table names are unique before you can deploy the replication flow.
#XMSG
deployDWCSourceFFDisabled=Deploying replication flows that have SAP Datasphere as their source is currently not possible because we are performing maintenance on this function.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Deploying replication flows that contain delta-enabled local tables as source objects is currently not possible because we are performing maintenance on this function.
#XMSG
deployHDLFSourceFFDisabled=Deploying replication flows that have source connections with the connection type SAP HANA Cloud, Data Lake Files is currently not possible because we are performing maintenance.
#XMSG
deployObjectStoreAsSourceFFDisabled=Deploying replication flows that have a cloud storage providers as their source is currently not possible.
#XMSG
deployConfluentSourceFFDisabled=Deploying replication flows that have Confluent Kafka as their source is currently not possible because we are performing maintenance on this function.
#XMSG
deployMaxDWCNewTableCrossed=For large replication flows, its not possible to "save and deploy" them in one go. Please save your replication flow first and then deploy it.
#XMSG
deployInProgressInfo=Deployment is already in progress.
#XMSG
deploySourceObjectInUse=Source objects {0} are already being used in replication flows {1}.
#XMSG
deployTargetSourceObjectInUse=Source objects {0} are already being used in replication flows {1}. Target objects {2} are already being used in replication flows {3}.
#XMSG
deployReplicationFlowCheckError=Error while verifying replication flow: {0}
#XMSG
preDeployTargetObjectInUse=Target objects {0} are already being used in replication flows {1}, and you can’t have the same target object in two different replication flows. Select another target object and try again.
#XMSG
runInProgressInfo=Replication flow is already running.
#XMSG
deploySignavioTargetFFDisabled=Deploying replication flows that have SAP Signavio as their target is currently not possible because we are performing maintenance on this function.
#XMSG
deployHanaViewAsSourceFFDisabled=Deploying replication flows that have views as source objects for the selected source connection is currently not possible. Try again later.
#XMSG
deployMsOneLakeTargetFFDisabled=Deploying replication flows that have MS OneLake as their target is currently not possible because we are performing maintenance on this function.
#XMSG
deploySFTPTargetFFDisabled=Deploying replication flows that have SFTP as their target is currently not possible because we are performing maintenance on this function.
#XMSG
deploySFTPSourceFFDisabled=Deploying replication flows that have SFTP as their source is currently not possible because we are performing maintenance on this function.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Technical Name
#XFLD
businessNameInRenameTarget=Business Name
#XTOL
renametargetDialogTitle=Rename Target Object
#XBUT
targetRenameButton=Rename
#XBUT
targetRenameCancel=Cancel
#XMSG
mandatoryTargetName=You must enter a name.
#XMSG
dwcSpecialChar=_(underscore) is the only special character allowed.
#XMSG
dwcWithDot=The target table name can consist of Latin letters, numbers, underscores (_) and full stops (.). The first character must be a letter, number or underscore (not a full stop).
#XMSG
nonDwcSpecialChar=Allowed special characters are _(underscore) -(hyphen) .(dot)
#XMSG
firstUnderscorePattern=Name must not start with _(underscore)

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: View SQL Create Table Statement
#XMSG
sqlDialogMaxPKWarning=In Google BigQuery, a maximum of 16 primary keys are supported, and the source object has a larger number. Therefore, no primary keys are defined in this statement.
#XMSG
sqlDialogIncomptiblePKTypeWarning=One or more source columns have data types that cannot be defined as primary keys in Google BigQuery. Therefore, no primary keys are defined in this case. In Google BigQuery, only the following data types can have a primary key: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Copy and Close
#XBUT
closeDDL=Close
#XMSG
copiedToClipboard=Copied to clipboard


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Object ''{0}'' cannot be part of a task chain because it does not have an end (as it includes objects with load type Initial and Delta/Delta Only).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Object ''{0}'' cannot be part of a task chain because it does not have an end (as it includes objects with load type Initial and Delta).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=The object "{0}" cannot be added to the task chain.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=There are unsaved target objects. Please save again.{0}{0} The behaviour of this feature has changed: In the past, target objects were only created in the target environment when the replication flow was deployed.{0} Now the objects are already created when the replication flow is saved. Your replication flow was created before this change and contains new objects.{0} You need to save the replication flow once again before deploying it so that the new objects are included correctly.
#XMSG
confirmChangeContentTypeMessage=You are about to change the content type. If you do so, all existing projections will be deleted.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Subject Name
#XFLD
schemaDialogVersionName=Schema Version
#XFLD
includeTechKey=Include Technical Key
#XFLD
segementButtonFlat=Flat
#XFLD
segementButtonNested=Nested
#XMSG
subjectNamePlaceholder=Search Subject Name

#XMSG
@EmailNotificationSuccess=Configuration of runtime email notifications is saved.

#XFLD
@RuntimeEmailNotification=Runtime Email Notification

#XBTN
@TXT_SAVE=Save


