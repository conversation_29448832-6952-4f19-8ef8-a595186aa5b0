#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Tok replikacije

#XFLD: Edit Schema button text
editSchema=<PERSON><PERSON>i šemu

#XTIT : Properties heading
configSchema=Kon<PERSON>gu<PERSON>ši šemu

#XFLD: save changed button text
applyChanges=Primeni promene


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Odaberi izvornu vezu
#XFLD
sourceContainernEmptyText=Odaberi spremnik
#XFLD
targetConnectionEmptyText=Odaberi ciljnu vezu
#XFLD
targetContainernEmptyText=Odaberi spremnik
#XFLD
sourceSelectObjectText=Odaberi izvorni objekat
#XFLD
sourceObjectCount=Izvorni objekti ({0})
#XFLD
targetObjectText=Ciljni objekti
#XFLD
confluentBrowseContext=Odaberi kontekst
#XBUT
@retry=Pokušaj ponovo
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Nadogradnja klijenta je u toku.

#XTOL
browseSourceConnection=Pretraži izvornu vezu
#XTOL
browseTargetConnection=Pretraži ciljnu vezu
#XTOL
browseSourceContainer=Pretraži izvorni spremnik
#XTOL
browseAndAddSourceDataset=Dodaj izvorne objekte
#XTOL
browseTargetContainer=Pretraži ciljni spremnik
#XTOL
browseTargetSetting=Pretraži ciljna podešavanja
#XTOL
browseSourceSetting=Pretraži izvorna podešavanja
#XTOL
sourceDatasetInfo=Informacije
#XTOL
sourceDatasetRemove=Ukloni
#XTOL
mappingCount=Predstavlja ukupni broj preslikavanja/izraza koji nisu zasnovani na imenu.
#XTOL
filterCount=Predstavlja ukupni broj uslova filtera.
#XTOL
loading=Učitavanje...
#XCOL
deltaCapture=Delta snimanje
#XCOL
deltaCaptureTableName=Tabela delta snimanja
#XCOL
loadType=Učitaj tip
#XCOL
deleteAllBeforeLoading=Izbriši sve pre učitavanja
#XCOL
transformationsTab=Projekcije
#XCOL
settingsTab=Podešavanja

#XBUT
renameTargetObjectBtn=Preimenuj ciljni objekat
#XBUT
mapToExistingTargetObjectBtn=Preslikaj u postojeći ciljni objekat
#XBUT
changeContainerPathBtn=Promeni put spremnika
#XBUT
viewSQLDDLUpdated=Prikaži SQL izjavu Kreiraj tabelu
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Izvorni objekat ne podržava delta snimanje, ali odabrani ciljni objekat ima aktiviranu opciju delta snimanja.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Nije moguće koristiti ciljni objekat jer je aktivirano delta snimanje,{0}dok izvorni objekat ne podržava delta snimanje.{1}Možete da odaberete drugi ciljni objekat koji ne podržava delta snimanje.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Ciljni objekat sa ovim nazivom već postoji. Međutim, ne može se koristiti{0}jer je delta snimanje aktivirano, dok izvorni objekat ne{0}podržava delta snimanje.{1}Možete da unesete naziv postojećeg ciljnog objekta koji ne{0}podržava delta snimanje ili naziv koji još ne postoji.
#XBUT
copySQLDDLUpdated=Kopiraj SQL izjavu Kreiraj tabelu
#XMSG
targetObjExistingNoCDCColumnUpdated=Postojeće tabele u Google BigQuery-ju moraju da uključuju sledeće kolone za snimanje podataka promene (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Sledeći izvorni objekti nisu podržani jer nemaju primarni ključ ili koriste vezu koja ne zadovoljava uslove za pozivanje primarnog ključa:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Potražite eventualno rešenje u članku baze znanja SAP KBA 3531135.
#XLST: load type list values
initial=Samo početno
@emailUpdateError=Greška u ažuriranju liste obaveštenja e-poštom

#XLST
initialDelta=Početno i delta

#XLST
deltaOnly=Samo delta
#XMSG
confluentDeltaLoadTypeInfo=Za izvor Confluent Kafka podržani su samo tipovi učitavanja Početno i Delta.
#XMSG
confirmRemoveReplicationObject=Da li potvrđujete da želite da izbrišete replikaciju?
#XMSG
confirmRemoveReplicationTaskPrompt=Ova radnja će izbrisati postojeće replikacije. Da li želite da nastavite?
#XMSG
confirmTargetConnectionChangePrompt=Ova radnja će ponovo postaviti ciljnu vezu, ciljni spremnik i izbrisati sve ciljne objekte. Da li želite da nastavite?
#XMSG
confirmTargetContainerChangePrompt=Ova radnja će ponovo postaviti ciljni spremnik i izbrisati sve postojeće ciljne objekte. Da li želite da nastavite?
#XMSG
confirmRemoveTransformObject=Da li potvrđujete da želite da izbrišete projekciju {0}?
#XMSG
ErrorMsgContainerChange=Greška pri promeni puta spremnika.
#XMSG
infoForUnsupportedDatasetNoKeys=Sledeći izvorni objekti nisu podržani jer nemaju primarni ključ:
#XMSG
infoForUnsupportedDatasetView=Sledeći izvorni objekti tipa Pogledi nisu podržani:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Sledeći izvorni objekat nije podržan jer predstavlja pogled SQL koji sadrži parametre unosa:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Sledeći izvorni objekti nisu podržani jer je ekstrahovanje za njih deaktivirano:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Za veze s platformom Confluent jedini dozvoljeni formati serijalizacije su AVRO i JSON. Sledeći objekti nisu podržani jer koriste drugi format serijalizacije:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Nije moguće pozvati šemu za sledeće objekte. Odaberite odgovarajući kontekst ili verifikujte konfiguraciju registracije šeme
#XTOL: warning dialog header on deleting replication task
deleteHeader=Izbriši
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Podešavanje Izbriši sve pre učitavanja nije podržano za Google BigQuery.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Podešavanje Izbriši sve pre učitavanja briše i ponovo kreira objekat (temu) pre svake replikacije. Ovo takođe briše sve dodeljene poruke.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Podešavanje Izbriši sve pre učitavanja nije podržano za ovaj tip cilja.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Tehnički naziv
#XCOL
connBusinessName=Poslovni naziv
#XCOL
connDescriptionName=Opis
#XCOL
connType=Tip
#XMSG
connTblNoDataFoundtxt=Veze nisu nađene
#XMSG
connectionError=Greška pri pozivanju veza.
#XMSG
connectionCombinationUnsupportedErrorTitle=Kombinacija veze nije podržana
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Replikacija iz {0} u {1} trenutno nije podržana.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Kombinacija tipa veze nije podržana
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Replikacija iz veze s tipom veze SAP HANA Cloud, fajlovi jezera podataka u {0} nije podržana. Možete replicirati samo u SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Odaberi
#XBUT
containerCancelBtn=Odustani
#XTOL
containerSelectTooltip=Odaberi
#XTOL
containerCancelTooltip=Odustani
#XMSG
containerContainerPathPlcHold=Put spremnika
#XFLD
containerContainertxt=Spremnik
#XFLD
confluentContainerContainertxt=Kontekst
#XMSG
infoMessageForSLTSelection=Samo /SLT/ID masovnog prenosa je dozvoljen kao spremnik. Odaberite ID masovnog prenosa u okviru SLT (ako postoji) i kliknite na Podnesi.
#XMSG
msgFetchContainerFail=Greška pri pozivanju podataka spremnika.
#XMSG
infoMessageForSLTHidden=Ova veza ne podržava SLT foldere pa se oni ne pojavljuju na listi u nastavku.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Odaberite spremnik koji u sebi sadrži potfoldere.
#XMSG
sftpIncludeSubFolderText=Netačno
#XMSG
sftpIncludeSubFolderTextNew=Ne

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Još nema preslikavanja filtera)
#XMSG
failToFetchRemoteMetadata=Greška pri pozivanju metapodataka.
#XMSG
failToFetchData=Greška pri pozivanju postojećeg cilja.
#XCOL
@loadType=Učitaj tip
#XCOL
@deleteAllBeforeLoading=Izbriši sve pre učitavanja

#XMSG
@loading=Učitavanje...
#XFLD
@selectSourceObjects=Odaberi izvorne objekte
#XMSG
@exceedLimit=Ne možete uvesti više od {0} objek(a)ta istovremeno. Poništite odabir najmanje {1} objek(a)ta.
#XFLD
@objects=Objekti
#XBUT
@ok=OK
#XBUT
@cancel=Odustani
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Sledeće
#XBUT
btnAddSelection=Dodaj odabir
#XTOL
@remoteFromSelection=Ukloni iz odabira
#XMSG
@searchInForSearchField=Traži u {0}

#XCOL
@name=Tehnički naziv
#XCOL
@type=Tip
#XCOL
@location=Lokacija
#XCOL
@label=Poslovni naziv
#XCOL
@status=Status

#XFLD
@searchIn=Traži u:
#XBUT
@available=Dostupno
#XBUT
@selection=Odabir

#XFLD
@noSourceSubFolder=Tabele i pogledi
#XMSG
@alreadyAdded=Već se nalazi u dijagramu
#XMSG
@askForFilter=Postoji više od {0} stavki. Unesite niz filtera da biste smanjili broj stavki.
#XFLD: success label
lblSuccess=Uspeh
#XFLD: ready label
lblReady=Spremno
#XFLD: failure label
lblFailed=Nije uspelo
#XFLD: fetching status label
lblFetchingDetail=Pozivanje detalja

#XMSG Place holder text for tree filter control
filterPlaceHolder=Upišite tekst da biste filtrirali objekte najvišeg nivoa
#XMSG Place holder text for server search control
serverSearchPlaceholder=Upišite i pritisnite Enter da biste tražili
#XMSG
@deployObjects=Uvoz {0} objekata...
#XMSG
@deployObjectsStatus=Broj objekata koji su uvezeni: {0}. Broj objekata koji se ne mogu uvesti: {1}

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Nije uspelo otvaranje lokalnog pretraživača repozitorijuma.
#XMSG
@openRemoteSourceBrowserError=Nije uspelo pozivanje izvornih objekata.
#XMSG
@openRemoteTargetBrowserError=Nije uspelo pozivanje ciljnih objekata.
#XMSG
@validatingTargetsError=Greška pri validaciji ciljeva.
#XMSG
@waitingToImport=Spremno za uvoz

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Prekoračen je maksimalni broj objekata. Odaberite najviše 500 objekata za jedan tok replikacije.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Tehnički naziv
#XFLD
sourceObjectBusinessName=Poslovni naziv
#XFLD
sourceNoColumns=Broj kolona
#XFLD
containerLbl=Spremnik

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Morate odabrati izvornu vezu za tok replikacije.
#XMSG
validationSourceContainerNonExist=Morate odabrati spremnik za izvornu vezu.
#XMSG
validationTargetNonExist=Morate odabrati ciljnu vezu za tok replikacije.
#XMSG
validationTargetContainerNonExist=Morate odabrati spremnik za ciljnu vezu.
#XMSG
validationTruncateDisabledForObjectTitle=Replikacija u skladišta objekta.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Replikacija u skladište u oblaku je moguća samo ako je opcija Izbriši sve pre učitavanja postavljena ili ako ciljni objekat ne postoji na cilju.{0}{0} Da biste ipak aktivirali replikaciju za objekte za koje opcija Izbriši sve pre učitavanja nije postavljena, pobrinite se da ciljni objekat ne postoji u sistemu pre nego što pokrenete tok replikacije.
#XMSG
validationTaskNonExist=Morate imati najmanje jednu replikaciju za tok replikacije.
#XMSG
validationTaskTargetMissing=Morate imati cilj za replikaciju sa izvorom: {0}
#XMSG
validationTaskTargetIsSAC=Odabrani cilj je SAC artefakt: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Odabrani cilj nije podržana lokalna tabela: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Objekat sa ovim nazivom već postoji u cilju. Međutim, ovaj objekat se ne može koristiti kao ciljni objekat za tok replikacije u lokalni repozitorijum jer nije lokalna tabela.
#XMSG
validateSourceTargetSystemDifference=Morate odabrati drugu izvornu i ciljnu vezu i kombinacije spremnika za tok replikacije.
#XMSG
validateDuplicateSources=jedna ili više replikacija imaju dvostruke nazive izvornog objekta: {0}.
#XMSG
validateDuplicateTargets=jedna ili više replikacija imaju dvostruke nazive ciljnog objekta: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=Izvorni objekat {0} ne podržava delta snimanje, a ciljni objekat {1} ga podržava. Morate ukloniti replikaciju.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Morate da odaberete tip učitavanja "Početno i delta" za replikaciju s nazivom ciljnog objekta {0}.
#XMSG
validationAutoRenameTarget=Ciljne kolone su preimenovane.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Dodata je automatska projekcija, a sledeće ciljne kolone su preimenovane da bi se dozvolila replikacija u cilj:{1}{1} {0} {1}{1}Ovo je zbog nekog od sledećih razloga:{1}{1}{2} Nepodržani znakovi{1}{2} Rezervisani prefiks
#XMSG
validationAutoRenameTargetDescriptionUpdated=Dodata je automatska projekcija, a sledeće ciljne kolone su preimenovane kako bi se omogućila replikacija u Google BigQuery:{1}{1} {0} {1}{1}To je zbog jednog od sledećih razloga:{1}{1}{2} Naziv kolone je rezervisan{1}{2} Znakovi nisu podržani{1}{2} Prefiks je rezervisan
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Dodata je automatska projekcija, a sledeće ciljne kolone su preimenovane kako bi se omogućile replikacije u Confluent:{1}{1} {0} {1}{1}To je zbog jednog od sledećih razloga:{1}{1}{2} Naziv kolone je rezervisan{1}{2} Znakovi nisu podržani{1}{2} Prefiks je rezervisan
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Dodata je automatska projekcija, a sledeće ciljne kolone su preimenovane kako bi se omogućile replikacije u cilj:{1}{1} {0} {1}{1}To je zbog jednog od sledećih razloga:{1}{1}{2} Naziv kolone je rezervisan{1}{2} Znakovi nisu podržani{1}{2} Prefiks je rezervisan
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Ciljni objekat je preimenovan.
#XMSG
autoRenameInfoDesc=Ciljni objekat je preimenovan jer je sadržao nepodržane znakove. Podržani su samo sledeći znakovi:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(tačka){0}{1}_(donja crta){0}{1}-(crtica)
#XMSG
validationAutoTargetTypeConversion=Ciljni tipovi podataka su promenjeni.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Za sledeće ciljne kolone, promenjeni su ciljni tipovi podataka, jer izvorni tipovi podataka nisu podržani u Google BigQuery-ju:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Za sledeće ciljne kolone promenjeni su ciljni tipovi podataka jer izvorni tipovi podataka nisu podržani u ciljnoj vezi:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Skratite nazive ciljne kolone.
#XMSG
validationMaxCharLengthGBQTargetDescription=U Google BigQuery-ju, nazivi kolone mogu da koriste maksimalno 300 znakova. Koristite projekciju da skratite sledeće nazive ciljne kolone:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Nije moguće kreirati primarne ključeve.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=U Google BigQuery-ju, podržano je maksimalno 16 primarnih ključeva, ali izvorni objekat ima veći broj primarnih ključeva. Nijedan primarni ključ neće biti kreiran u ciljnom objektu.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Jedna ili više izvornih kolona imaju tipove podataka koji se ne mogu definisati kao primarni ključevi u Google BigQuery-ju. Nijedan primarni ključ se neće kreirati u ciljnom objektu.{0}{0}Sledeći ciljni tipovi podataka su kompatibilni sa Google BigQuery tipovima podataka za koje se primarni ključ može definisati: {0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Definišite jednu kolonu ili više njih kao primarni ključ.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Morate definisati jednu kolonu ili više njih kao primarni ključ. Za to koristite dijalog izvorne šeme.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Definišite jednu kolonu ili više njih kao primarni ključ.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Morate definisati jednu kolonu ili više njih kao primarni ključ koje odgovaraju ograničenjima primarnog ključa za vaš izvorni objekat. U tu svrhu idite na Konfiguriši šemu u svojstvima izvornog objekta.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Unesite maksimalnu vrednost particije.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Maksimalna vrednost particije mora biti ≥ 1 i ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Definišite jednu kolonu ili više njih kao primarni ključ.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Da biste replicirali objekat, morate da definišete jednu kolonu ili više njih kao primarni ključ. Koristite projekciju za ovo.
#XMSG
validateHDLFNoPKExistingDatasetError=Definišite jednu kolonu ili više njih kao primarni ključ.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Da bi se replicirali podaci u postojeći ciljni objekat, jedna kolona ili više njih mora se definisati kao primarni ključ. {0} Imate sledeće opcije za definisanje jedne kolone ili više njih kao primarni ključ: {0} {1} Koristite uređivač lokalne tabele za promenu postojećeg ciljnog objekta. Zatim ponovo učitajte tok replikacije.{0}{1} Preimenujte ciljni objekat u toku replikacije. Time će se kreirati novi objekat čim se izvođenje pokrene. Nakon preimenovanja, možete definisati jednu kolonu ili više njih kao primarni ključ u projekciji.{0}{1} Preslikajte objekat u drugi postojeći ciljni objekat u kojem je jedna kolona ili više njih već definisano kao primarni ključ.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Odabrani cilj već postoji u repozitorijumu: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Nazive tabele delta snimanja već koriste druge tabele u repozitorijumu: {0} Morate da preimenujete te ciljne objekte da biste obezbedili da povezani nazivi tabele delta snimanja budu jedinstveni pre nego što sačuvate tok replikacije.
#XMSG
validateConfluentEmptySchema=Definiši šemu
#XMSG
validateConfluentEmptySchemaDescUpdated=Izvorna tabela nema šemu. Izaberite Konfiguriši šemu da biste je definisali
#XMSG
validationCSVEncoding=Nevažeće šifrovanje CSV
#XMSG
validationCSVEncodingDescription=Šifrovanje CSV zadatka nije važeće.
#XMSG
validateConfluentEmptySchema=Odaberite kompatibilni tip ciljnih podataka
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Odaberite kompatibilni tip ciljnih podataka
#XMSG
globalValidateTargetDataTypeDesc=Greška pri preslikavanjima kolone. Idite na Projekciju i uverite se da su sve izvorne kolone preslikane s jedinstvenom kolonom, s kolonom koja ima kompatibilan tip podataka, kao i da su svi definisani izrazi važeći.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Duplikati naziva kolona.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Duplikati naziva kolona nisu podržani. Koristite dijalog projekcije da ih popravite. Sledeći ciljni objekti imaju duplikate naziva kolona: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Duplikati naziva kolona.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Duplikati naziva kolona nisu podržani. Sledeći ciljni objekti imaju duplikate naziva kolona: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Moguće su nedoslednosti u podacima.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Tip učitavanja Samo delta neće uzeti u obzir promene izvršene u izvoru između poslednjeg snimanja i sledećeg izvođenja.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Promenite tip učitavanja u "Početno".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Replikacija objekata zasnovanih na ABAP-u koji nemaju primarni ključ moguće je samo za tip učitavanja "Samo početno".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Deaktiviraj delta snimanje.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Da biste replicirali objekat koji nema primarni ključ koristeći tip izvorne veze ABAP, prvo morate da deaktivirate delta snimanje za ovu tabelu.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Promeni ciljni objekat.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Nije moguće koristiti ciljni objekat jer je aktivirano delta snimanje. Možete da preimenujete ciljni objekat, a zatim isključite delta snimanje za novi (preimenovani) objekat ili da preslikate izvorni objekat u ciljni objekat za koji je delta snimanje deaktivirano.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Promeni ciljni objekat.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Nije moguće koristiti ciljni objekat jer nema potrebnu tehničku kolonu __load_package_id. Možete da preimenujete ciljni objekat koristeći naziv koji još ne postoji. Sistem onda kreira novi objekat koji ima istu definiciju kao izvorni objekat i sadrži tehničku kolonu. U suprotnom, možete da preslikate ciljni objekat u postojeći objekat koji ima potrebnu tehničku kolonu (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Promeni ciljni objekat.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Nije moguće koristiti ciljni objekat jer nema potrebnu tehničku kolonu __load_record_id. Možete da preimenujete ciljni objekat koristeći naziv koji još ne postoji. Sistem onda kreira novi objekat koji ima istu definiciju kao izvorni objekat i sadrži tehničku kolonu. U suprotnom, možete da preslikate ciljni objekat u postojeći objekat koji ima potrebnu tehničku kolonu (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Promeni ciljni objekat.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Nije moguće koristiti ciljni objekat jer tip podataka tehničke kolone __load_record_id nije "string(44)". Možete da preimenujete ciljni objekat koristeći naziv koji još ne postoji. Sistem onda kreira novi objekat koji ima istu definiciju kao izvorni objekat i posledično ispravni tip podataka. U suprotnom, možete da preslikate ciljni objekat u postojeći objekat koji ima potrebnu tehničku kolonu (__load_record_id) sa ispravnim tipom podataka.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Promeni ciljni objekat.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Nije moguće koristiti ciljni objekat jer ima primarni ključ, a izvorni objekat ga nema. Možete da preimenujete ciljni objekat koristeći naziv koji još ne postoji. Sistem onda kreira novi objekat koji ima istu definiciju kao izvorni objekat i posledično nema primarni ključ.  U suprotnom, možete da preslikate ciljni objekat u postojeći objekat koji ima potrebnu tehničku kolonu (__load_package_id) i nema primarni ključ.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Promeni ciljni objekat.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Nije moguće koristiti ciljni objekat jer ima primarni ključ, a izvorni objekat ga nema. Možete da preimenujete ciljni objekat koristeći naziv koji još ne postoji. Sistem onda kreira novi objekat koji ima istu definiciju kao izvorni objekat i posledično nema primarni ključ. U suprotnom, možete da preslikate ciljni objekat u postojeći objekat koji ima potrebnu tehničku kolonu (__load_record_id) i nema primarni ključ.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Promeni ciljni objekat.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Nije moguće koristiti ciljni objekat jer tip podataka tehničke kolone __load_package_id nije "binary(>=256)". Možete da preimenujete ciljni objekat koristeći naziv koji još ne postoji. Sistem onda kreira novi objekat koji ima istu definiciju kao izvorni objekat i posledično ispravni tip podataka. U suprotnom, možete da preslikate ciljni objekat u postojeći objekat koji ima potrebnu tehničku kolonu (__load_package_id) sa ispravnim tipom podataka.
#XMSG
validationAutoRenameTargetDPID=Ciljne kolone su preimenovane.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Uklonite izvorni objekat.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Izvorni objekat nema kolonu ključa, što nije podržano u ovom kontekstu.
#XMSG
validationAutoRenameTargetDPIDDescription=Automatska projekcija je dodata, a sledeće ciljne kolone su preimenovane tako da dozvoljavaju replikaciju iz ABAP izvora bez ključeva :{1}{1} {0} {1}{1}Ovo je zbog jednog od sledećih razloga:{1}{1}{2} Rezervisani naziv kolone{1}{2} Nepodržani znakovi{1}{2} Rezervisani prefiks
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Replikacija u {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Snimanje i implementacija tokova replikacije koji imaju {0} kao cilj trenutno nije moguća jer vršimo održavanje ove funkcije.
#XMSG
TargetColumnSkippedLTF=Ciljna kolona je preskočena.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Ciljna kolona je preskočena zbog nepodržanog tipa podataka. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Kolona vremena kao primarni ključ.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Izvorni objekat ima kolonu vremena kao primarni ključ, što nije podržano u ovom kontekstu.
#XMSG
validateNoPKInLTFTarget=Nedostaje primarni ključ.
#XMSG
validateNoPKInLTFTargetDescription=Primarni ključ nije definisan u cilju, što nije podržano u ovom kontekstu.
#XMSG
validateABAPClusterTableLTF=Tabela ABAP skupa.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Izvorni objekat je tabela ABAP skupa. što nije podržano u ovom kontekstu.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Izgleda da još niste dodali podatke.
#YINS
welcomeText2=Za pokretanje vašeg toka replikacije odaberite vezu i izvorni objekat na levoj strani.

#XBUT
wizStep1=Odaberi izvornu vezu
#XBUT
wizStep2=Odaberi izvorni spremnik
#XBUT
wizStep3=Dodaj izvorne objekte

#XMSG
limitDataset=Dostignut je maksimalni broj objekata. Uklonite postojeće objekte da biste dodali nove ili kreirajte novi tok replikacije.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Tok replikacije za ovu ciljnu vezu koja nije SAP nije moguće pokrenuti zato što izlazni obim nije dostupan za ovaj mesec.
#XMSG
premiumOutBoundRFAdminWarningMsg=Administrator može da poveća izlazne blokove Premium za ovog zakupca i izlazni obim učini dostupnim za ovaj mesec.
#XMSG
messageForToastForDPIDColumn2=Nova kolona je dodata ciljnom objektu za {0} objekata - potrebno za upravljanje dvostrukim zapisima u vezi sa izvornim objektima zasnovanim na ABAP-u koji nemaju primarni ključ.
#XMSG
PremiumInboundWarningMessage=U zavisnosti od broja tokova replikacije i obima podataka za replikaciju, SAP HANA resursi{0}obavezni za replikaciju podataka putem {1} mogu prekoračiti dostupni kapacitet za vaš klijent.
#XMSG
PremiumInboundWarningMsg=U zavisnosti od broja tokova replikacije i obima podataka za replikaciju, SAP HANA resursi{0}obavezni za replikaciju podataka putem "{1}" mogu prekoračiti dostupni kapacitet za vaš klijent.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Unesite naziv projekcije.
#XMSG
emptyTargetColumn=Unesite naziv ciljne kolone.
#XMSG
emptyTargetColumnBusinessName=Popunite ciljnu kolonu Poslovni naziv.
#XMSG
invalidTransformName=Unesite naziv projekcije.
#XMSG
uniqueColumnName=Preimenuj ciljnu kolonu.
#XMSG
copySourceColumnLbl=Kopiraj kolone iz izvornog objekta
#XMSG
renameWarning=Obavezno izaberite jedinstveni naziv prilikom promene naziva ciljne tabele. Ako tabela s novim nazivom već postoji u prostoru, koristiće definiciju te tabele.

#XMSG
uniqueColumnBusinessName=Preimenujte poslovni naziv ciljne kolone.
#XMSG
uniqueSourceMapping=Odaberite drugu izvornu kolonu.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=Izvornu kolonu {0} već koriste sledeće ciljne kolone:{1}{1}{2}{1}{1} Za ovu ciljnu kolonu ili za druge ciljne kolone odaberite izvornu kolonu koja nije već u upotrebi da biste sačuvali projekciju.
#XMSG
uniqueColumnNameDescription=Naziv ciljne kolone koji ste uneli već postoji. Da biste mogli da sačuvate projekciju, morate da unesete jedinstveni naziv kolone.
#XMSG
uniqueColumnBusinessNameDesc=Poslovni naziv ciljne kolone već postoji. Da biste sačuvali projekciju morate uneti jedinstveni poslovni naziv kolone.
#XMSG
emptySource=Odaberite izvornu kolonu ili unesite konstantu.
#XMSG
emptySourceDescription=Da biste kreirali važeći unos preslikavanja, potrebno je da odaberete izvornu kolonu ili unesete vrednost konstante.
#XMSG
emptyExpression=Definišite preslikavanje.
#XMSG
emptyExpressionDescription1=Odaberite ili izvornu kolonu u koju želite da preslikate ciljnu kolonu ili odaberite kvadratić za potvrdu u koloni {0} Funkcije / Konstante {1}. {2} {2} Funkcije se unose automatski u skladu s tipom ciljnih podataka. Konstantne vrednosti se mogu uneti ručno.
#XMSG
numberExpressionErr=Unesite broj.
#XMSG
numberExpressionErrDescription=Odabrali ste numerički tip podataka. To znači da možete unositi samo brojeve, plus decimalni zarez ako je primenljivo. Nemojte koristiti polunavodnike.
#XMSG
invalidLength=Unesite važeću vrednost dužine.
#XMSG
invalidLengthDescription=Dužina tipa podataka mora biti jednaka ili veća od dužine izvorne kolone i može biti između 1 i 5000.
#XMSG
invalidMappedLength=Unesite važeću vrednost dužine.
#XMSG
invalidMappedLengthDescription=Dužina tipa podataka mora biti jednaka ili veća od dužine izvorne kolone {0} i može biti između 1 i 5000.
#XMSG
invalidPrecision=Unesite važeću vrednost preciznosti.
#XMSG
invalidPrecisionDescription=Preciznost definiše ukupni broj cifara. Skala definiše broj cifara nakon decimalnog zareza i može biti između 0 i preciznosti.{0}{0} Primeri: {0}{1} Preciznost 6, skala 2 odgovara brojevima kao što je 1234,56.{0}{1} Preciznost 6, skala 6 odgovara brojevima kao što je 0,123546.{0} {0} Preciznost i skala za ciljni broj moraju biti kompatibilni s preciznošću i skalom za izvorni broj tako da sve cifre izvornog broja mogu da stanu u ciljno polje. Na primer, ako imate preciznost 6 i skalu 2 u izvornom broju (i stoga cifre koje nisu 0 pre decimalnog zareza), ne možete imati preciznost 6 i skalu 6 u ciljnom broju.
#XMSG
invalidPrimaryKey=Unesite najmanje jedan primarni ključ.
#XMSG
invalidPrimaryKeyDescription=Primarni ključ nije definisan za ovu šemu.
#XMSG
invalidMappedPrecision=Unesite važeću vrednost preciznosti.
#XMSG
invalidMappedPrecisionDescription1=Preciznost definiše ukupni broj cifara. Skala definiše broj cifara nakon decimalnog zareza i može biti između 0 i preciznosti.{0}{0} Primeri: {0}{1} Preciznost 6, skala 2 odgovara brojevima kao što je 1234,56.{0}{1} Preciznost 6, skala 6 odgovara brojevima kao što je 0,123546.{0}{0}Preciznost tipa podataka mora biti jednaka ili veća od preciznosti izvornog broja ({2}).
#XMSG
invalidScale=Unesite važeću vrednost skale.
#XMSG
invalidScaleDescription=Preciznost definiše ukupni broj cifara. Skala definiše broj cifara nakon decimalnog zareza i može biti između 0 i preciznosti.{0}{0} Primeri: {0}{1} Preciznost 6, skala 2 odgovara brojevima kao što je 1234,56.{0}{1} Preciznost 6, skala 6 odgovara brojevima kao što je 0,123546.{0} {0} Preciznost i skala za ciljni broj moraju biti kompatibilni s preciznošću i skalom za izvorni broj tako da sve cifre izvornog broja mogu da stanu u ciljno polje. Na primer, ako imate preciznost 6 i skalu 2 u izvornom broju (i stoga cifre koje nisu 0 pre decimalnog zareza), ne možete imati preciznost 6 i skalu 6 u ciljnom broju.
#XMSG
invalidMappedScale=Unesite važeću vrednost skale.
#XMSG
invalidMappedScaleDescription1=Preciznost definiše ukupni broj cifara. Skala definiše broj cifara nakon decimalnog zareza i može biti između 0 i preciznosti.{0}{0} Primeri: {0}{1} Preciznost 6, skala 2 odgovara brojevima kao što je 1234,56.{0}{1} Preciznost 6, skala 6 odgovara brojevima kao što je 0,123546.{0}{0} Skala tipa podataka mora biti jednaka skali izvora ili veća od nje ({2}).
#XMSG
nonCompatibleDataType=Odaberite kompatibilni ciljni tip podataka.
#XMSG
nonCompatibleDataTypeDescription1=Tip podataka koji ovde navedete mora biti kompatibilan sa izvornim tipom podataka ({0}). {1}{1} Primer: ako izvorna kolona ima tip podataka Niz i sadrži slova, ne možete koristiti decimalni tip podataka za ciljnu kolonu.
#XMSG
invalidColumnCount=Odaberite izvornu kolonu.
#XMSG
ObjectStoreInvalidScaleORPrecision=Unesite važeću vrednost za preciznost i skalu.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=Prva vrednost je preciznost, koja definiše ukupni broj cifara. Druga vrednost je skala, koja definiše cifre nakon decimalne tačke. Unesite ciljnu vrednost skale koja je veća od izvorne vrednosti skale i uverite se da je razlika između unete ciljne vrednosti skale i vrednosti preciznosti veća od razlike između izvorne vrednosti skale i vrednosti preciznosti.
#XMSG
InvalidPrecisionORScale=Unesite važeću vrednost za preciznost i skalu.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=Prva vrednost je preciznost koja definiše ukupni broj cifara. Druga vrednost je skala koja definiše cifre nakon decimalnog zareza.{0}{0}Pošto izvorni tip podataka nije podržan u Google BigQuery-ju, konvertuje se u ciljni tip podataka DECIMAL. U ovom slučaju, preciznost se može definisati samo između 38 i 76, a skala između 9 i 38. Osim toga, rezultat preciznosti umanjene za skalu koji predstavlja cifre pre decimalnog zareza, mora biti između 29 i 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=Prva vrednost je preciznost koja definiše ukupni broj cifara. Druga vrednost je skala koja definiše cifre nakon decimalnog zareza.{0}{0}Pošto izvorni tip podataka nije podržan u Google BigQuery-ju, konvertuje se u ciljni tip podataka DECIMAL. U tom slučaju, preciznost se mora definisati kao 20 ili više. Osim toga, rezultat preciznosti umanjene za skalu koji predstavlja cifre pre decimalnog zareza, mora biti 20 ili više.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=Prva vrednost je preciznost, koja definiše ukupni broj cifara. Druga vrednost je skala, koja definiše cifre nakon decimalnog zareza.{0}{0}Pošto izvorni tip podataka nije podržan u cilju, konvertuje se u ciljni tip podataka DECIMAL. U ovom slučaju, preciznost se mora definisati bilo kojim brojem većim od 1 ili jednakim 1 i manjim od 38 ili jednakim 38, a skala je manja od preciznosti ili joj je jednaka.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=Prva vrednost je preciznost, koja definiše ukupni broj cifara. Druga vrednost je skala, koja definiše cifre nakon decimalnog zareza.{0}{0}Pošto izvorni tip podataka nije podržan u cilju, konvertuje se u ciljni tip podataka DECIMAL. U ovom slučaju, preciznost mora biti definisana kao 20 ili veća. Osim toga, rezultat razlike preciznosti i skale, koja odražava cifre pre decimalnog zareza, mora biti 20 ili veća.
#XMSG
invalidColumnCountDescription=Da biste kreirali važeći unos preslikavanja, potrebno je da odaberete izvornu kolonu ili unesete vrednost konstante.
#XMSG
duplicateColumns=Preimenuj ciljnu kolonu.
#XMSG
duplicateGBQCDCColumnsDesc=Naziv ciljne kolone je rezervisan u Google BigQuery-ju. Potrebno je da ga preimenujete da biste mogli da sačuvate projekciju.
#XMSG
duplicateConfluentCDCColumnsDesc=Naziv ciljne kolone je rezervisan u Confluent. Treba da je preimenujete da biste mogli da sačuvate projekciju.
#XMSG
duplicateSignavioCDCColumnsDesc=Naziv ciljne kolone je rezervisan u rešenju SAP Signavio. Treba da je preimenujete da biste mogli da sačuvate projekciju.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Naziv ciljne kolone rezervisan je u MS OneLake. Morate promeniti naziv da biste mogli da sačuvate projekciju.
#XMSG
duplicateSFTPCDCColumnsDesc=Naziv ciljne kolone rezervisan je u SFTP. Morate promeniti naziv da biste mogli da sačuvate projekciju.
#XMSG
GBQTargetNameWithPrefixUpdated1=Naziv ciljne kolone sadrži prefiks koji je rezervisan u Google BigQuery-ju. Potrebno je da ga preimenujete da biste mogli da sačuvate projekciju. {0}{0}Naziv ciljne kolone ne može da počinje bilo kojim od sledećih nizova:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Skratite naziv ciljne kolone.
#XMSG
GBQtargetMaxLengthDesc=U Google BigQuery-ju, naziv kolone može da koristi maksimalno 300 znakova. Skratite naziv ciljne kolone da biste mogli da sačuvate projekciju.
#XMSG
invalidMappedScalePrecision=Preciznost i skala za cilj moraju biti kompatibilni s preciznošću i skalom za izvor, tako da sve cifre iz izvora odgovaraju ciljnom polju.
#XMSG
invalidMappedScalePrecisionShortText=Unesite važeću vrednost preciznosti i skale.
#XMSG
validationIncompatiblePKTypeDescProjection3=Jedna ili više izvornih kolona imaju tipove podataka koji se ne mogu definisati kao primarni ključevi u Google BigQuery-ju. Nijedan primarni ključ se neće kreirati u ciljnom objektu.{0}{0}Sledeći ciljni tipovi podataka su kompatibilni sa Google BigQuery tipovima podataka za koje se primarni ključ može definisati :{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Poništite odabir column __message_id.
#XMSG
uncheckColumnMessageIdDesc=Kolona: Primarni ključ
#XMSG
validationOpCodeInsert=Morate uneti vrednost za unos.
#XMSG
recommendDifferentPrimaryKey=Preporučujemo da odaberete drugi primarni ključ na nivou stavke.
#XMSG
recommendDifferentPrimaryKeyDesc=Kada je šifra operacije već definisana, preporučuje se da odaberete druge primarne ključeve za indeks niza i stavke kako biste izbegli probleme kao što je, na primer, dupliranje kolona.
#XMSG
selectPrimaryKeyItemLevel=Morate odabrati najmanje jedan primarni ključ za nivo zaglavlja i nivo stavke.
#XMSG
selectPrimaryKeyItemLevelDesc=Kada se niz ili mapa prošire, morate odabrati dva primarna ključa, jedan na nivou zaglavlja i jedan na nivou stavke.
#XMSG
invalidMapKey=Morate odabrati najmanje jedan primarni ključ na nivou zaglavlja.
#XMSG
invalidMapKeyDesc=Kada se niz ili mapa prošire, morate odabrati primarni ključ na nivou zaglavlja.
#XFLD
txtSearchFields=Traži ciljne kolone
#XFLD
txtName=Naziv
#XMSG
txtSourceColValidation=Nije podržana jedna izvorna kolona ili više njih:
#XMSG
txtMappingCount=Preslikavanja ({0})
#XMSG
schema=Šema
#XMSG
sourceColumn=Izvorne kolone
#XMSG
warningSourceSchema=Svaka promena šeme uticaće na preslikavanja u dijalogu projekcije.
#XCOL
txtTargetColName=Ciljna kolona (tehnički naziv)
#XCOL
txtDataType=Ciljni tip podataka
#XCOL
txtSourceDataType=Tip izvornih podataka
#XCOL
srcColName=Izvorna kolona (tehnički naziv)
#XCOL
precision=Preciznost
#XCOL
scale=Skala
#XCOL
functionsOrConstants=Funkcije / konstante
#XCOL
txtTargetColBusinessName=Ciljna kolona (Poslovni naziv)
#XCOL
prKey=Primarni ključ
#XCOL
txtProperties=Svojstva
#XBUT
txtOK=Sačuvaj
#XBUT
txtCancel=Odustani
#XBUT
txtRemove=Ukloni
#XFLD
txtDesc=Opis
#XMSG
rftdMapping=Preslikavanje
#XFLD
@lblColumnDataType=Tip podataka
#XFLD
@lblColumnTechnicalName=Tehnički naziv
#XBUT
txtAutomap=Automatski preslikaj
#XBUT
txtUp=Gore
#XBUT
txtDown=Dole

#XTOL
txtTransformationHeader=Projekcija
#XTOL
editTransformation=Uredi
#XTOL
primaryKeyToolip=Ključ


#XMSG
rftdFilter=Filter
#XMSG
rftdFilterColumnCount=Izvor: {0}({1})
#XTOL
rftdFilterColSearch=Traži
#XMSG
rftdFilterColNoData=Nema kolona za prikaz
#XMSG
rftdFilteredColNoExps=Nema izraza filtera
#XMSG
rftdFilterSelectedColTxt=Dodaj filter za
#XMSG
rftdFilterTxt=Filter dostupan za
#XBUT
rftdFilterSelectedAddColExp=Dodaj izraz
#YINS
rftdFilterNoSelectedCol=Odaberite kolonu da biste dodali filter.
#XMSG
rftdFilterExp=Izraz filtera
#XMSG
rftdFilterNotAllowedColumn=Dodavanje filtera nije podržano za ovu kolonu.
#XMSG
rftdFilterNotAllowedHead=Kolona nije podržana
#XMSG
rftdFilterNoExp=Filter nije definisan
#XTOL
rftdfilteredTt=Filtrirano
#XTOL
rftdremoveexpTt=Ukloni izraz filtera
#XTOL
validationMessageTt=Poruke validacije
#XTOL
rftdFilterDateInp=Odaberite datum
#XTOL
rftdFilterDateTimeInp=Odaberite datum i vreme
#XTOL
rftdFilterTimeInp=Odaberite vreme
#XTOL
rftdFilterInp=Unesite vrednost
#XMSG
rftdFilterValidateEmptyMsg={0} izraza filtera u {1} koloni je prazno
#XMSG
rftdFilterValidateInvalidNumericMsg={0} izraza filtera u {1} koloni sadrži nevažeće numeričke vrednosti
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Izraz filtera mora sadržati važeće numeričke vrednosti
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Ako je promenjena šema ciljnog objekta, koristite funkciju "Preslikaj u postojeći ciljni objekat" na glavnoj stranici da bi se izvršilo prilagođavanje za promene i da bi se ponovo izvršilo preslikavanje ciljnog objekta i njegovog izvora.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Ako ciljna tabela već postoji i preslikavanje obuhvata promenu šeme, morate promeniti ciljnu tabelu u skladu s tim pre nego što implementirate tok replikacije.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Ako vaše preslikavanje obuhvata promenu šeme, morate promeniti ciljnu tabelu u skladu s tim pre nego što implementirate tok replikacije.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Sledeće nepodržane kolone su preskočene u izvornoj definiciji: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Sledeće nepodržane kolone su preskočene u ciljnoj definiciji: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Sledeći objekti nisu podržani jer su izloženi za upotrebu: {0} {1} {0} {0} Da biste koristili tabele u toku replikacije, semantička upotreba (u podešavanjima tabele) ne sme biti postavljena na {2}Analitički skup podataka{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Ciljni objekat se ne može koristiti jer je izložen za upotrebu: {0} {0} Da biste koristili tabelu u toku replikacije, semantička upotreba (u podešavanjima tabele) ne sme biti postavljena na {1}Analitički skup podataka{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Ciljni objekat sa ovim nazivom već postoji. Međutim, ne može se koristiti jer je izložen za upotrebu: {0} {0} Da biste koristili tabelu u toku replikacije, semantička upotreba (u podešavanjima tabele) ne sme biti postavljena na {1}Analitički skup podataka{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Objekat sa ovim nazivom već postoji u cilju. {0}Međutim, ovaj objekat se ne može koristiti kao ciljni objekat za tok replikacije u lokalni repozitorijum jer nije lokalna tabela.
#XMSG:
targetAutoRenameUpdated=Ciljna kolona je preimenovana.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Ciljna kolona je preimenovana da bi se dozvolile replikacije u Google BigQuery-ju. To je zbog jednog od sledećih razloga:{0} {1}{2}Naziv kolone je rezervisan{3}{2}Nepodržani znakovi{3}{2}Prefiks je rezervisan{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Ciljna kolona je preimenovana da bi se dozvolile replikacije u Confluent-u. To je zbog jednog od sledećih razloga:{0} {1}{2}Naziv kolone je rezervisan{3}{2}Nepodržani znakovi{3}{2}Prefiks je rezervisan{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Ciljna kolona je preimenovana da bi se dozvolile replikacije u cilj. Ovo je zbog jednog od sledećih razloga:{0} {1}{2}Nepodržani znakovi{3}{2}Rezervisani prefiks{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Ciljna kolona je preimenovana da bi se dozvolile replikacije u cilj. To je zbog jednog od sledećih razloga:{0} {1}{2}Rezervisani naziv kolone{3}{2}Nepodržani znakovi{3}{2}Rezervisani prefiks{3}{4}
#XMSG:
targetAutoDataType=Ciljni tip podataka je promenjen.
#XMSG:
targetAutoDataTypeDesc=Ciljni tip podataka je promenjen u {0} zato što izvorni tip podataka nije podržan u Google BigQuery-ju.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Ciljni tip podataka je promenjen u {0} jer izvorni tip podataka nije podržan u ciljnoj vezi.
#XMSG
projectionGBQUnableToCreateKey=Nije moguće kreirati primarne ključeve.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=U Google BigQuery-ju, podržano je maksimalno 16 primarnih ključeva, ali izvorni objekat ima veći broj primarnih ključeva. Nijedan primarni ključ neće biti kreiran u ciljnom objektu.
#XMSG
HDLFNoKeyError=Definišite jednu kolonu ili više njih kao primarni ključ.
#XMSG
HDLFNoKeyErrorDescription=Da biste replicirali objekat morate definisati jednu kolonu ili više njih kao primapni ključ.
#XMSG
HDLFNoKeyErrorExistingTarget=Definišite jednu kolonu ili više njih kao primarni ključ.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Da bi se replicirali podaci u postojeći ciljni objekat, jedna kolona ili više njih mora se definisati kao primarni ključ.{0} {0} Imate sledeće opcije za definisanje jedne kolone ili više njih kao primarni ključ: {0}{0}{1} Koristite uređivač lokalne tabele za promenu postojećeg ciljnog objekta. Zatim ponovo učitajte tok replikacije.{0}{0}{1} Preimenujte ciljni objekat u toku replikacije. Time će se kreirati novi objekat čim se izvođenje pokrene. Nakon preimenovanja, možete definisati jednu kolonu ili više njih kao primarni ključ u projekciji.{0}{0}{1} Preslikajte objekat u drugi postojeći ciljni objekat u kojem je jedna kolona ili više njih već definisano kao primarni ključ.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Primarni ključ promenjen.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=U poređenju sa izvornim objektom, definisali ste različite kolone kao primarni ključ za ciljni objekat. Osigurajte da ove kolone jedinstveno identifikuju sve redove da biste izbegli moguća oštećenja podataka pri kasnijoj replikaciji podataka. {0} {0} U izvornom objektu, sledeće kolone se definišu kao primarni ključ: {0} {1}
#XMSG
duplicateDPIDColumns=Preimenuj ciljnu kolonu.
#XMSG
duplicateDPIDDColumnsDesc1=Ova ciljna kolona je rezervisana za tehničku kolonu. Unesite drugi naziv za snimanje projekcije.
#XMSG:
targetAutoRenameDPID=Ciljna kolona je preimenovana.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Ciljna kolona je preimenovana tako da dozvoljava replikacije iz ABAP izvora bez ključeva. Ovo je zbog jednog od sledećih razloga:{0} {1}{2}Rezervisani naziv kolone{3}{2}Nepodržani znakovi{3}{2} Rezervisani prefiks{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle=Ciljna podešavanja za {0}
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle=Izvorna podešavanja za {0}
#XBUT
connectionSettingSave=Sačuvaj
#XBUT
connectionSettingCancel=Odustani
#XBUT: Button to keep the object level settings
txtKeep=Zadrži
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Piši preko
#XFLD
targetConnectionThreadlimit=Ograničenje niti cilja za početno učitavanje (1-100)
#XFLD
connectionThreadLimit=Ograničenje niti izvora za početno učitavanje (1-100)
#XFLD
maxConnection=Ograničenje niti replikacije (1-100)
#XFLD
kafkaNumberOfPartitions=Broj particija
#XFLD
kafkaReplicationFactor=Faktor replikacije
#XFLD
kafkaMessageEncoder=Uređaj za kodiranje poruka
#XFLD
kafkaMessageCompression=Sažimanje poruka
#XFLD
fileGroupDeltaFilesBy=Grupiši delta po
#XFLD
fileFormat=Tip fajla
#XFLD
csvEncoding=Šifrovanje CSV
#XFLD
abapExitLbl=ABAP izlaz
#XFLD
deltaPartition=Broj niti objekta za delta učitavanja (1-10)
#XFLD
clamping_Data=Neuspeh pri skraćivanju podataka
#XFLD
fail_On_Incompatible=Neuspeh zbog nekompatibilnih podataka
#XFLD
maxPartitionInput=Maksimalni broj particija
#XFLD
max_Partition=Definiši maksimalni broj particija
#XFLD
include_SubFolder=Uključi potfoldere
#XFLD
fileGlobalPattern=Globalni šablon za naziv fajla
#XFLD
fileCompression=Sažimanje fajla
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Separator fajla
#XFLD
fileIsHeaderIncluded=Zaglavlje fajla
#XFLD
fileOrient=Orient
#XFLD
gbqWriteMode=Način pisanja
#XFLD
suppressDuplicate=Sažmi duplikate
#XFLD
apacheSpark=Aktiviraj kompatibilnost sa Apache Spark
#XFLD
clampingDatatypeCb=Izvrši Clamp za tipove podataka pokretnog decimalnog zareza
#XFLD
overwriteDatasetSetting=Piši preko ciljnih podešavanja na nivou objekta
#XFLD
overwriteSourceDatasetSetting=Piši preko izvornih podešavanja na nivou objekta
#XMSG
kafkaInvalidConnectionSetting=Unesitete broj između {0} i {1}.
#XMSG
MinReplicationThreadErrorMsg=Unesite broj veći od {0}.
#XMSG
MaxReplicationThreadErrorMsg=Unesite broj manji od {0}.
#XMSG
DeltaThreadErrorMsg=Unesite vrednost između 1 i 10.
#XMSG
MaxPartitionErrorMsg=Unesite vrednost između 1 <= x <= 2147483647. Standardna vrednost je 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Unesite ceo broj između {0} i {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Koristite faktor replikacije brokera
#XFLD
serializationFormat=Format serijalizacije
#XFLD
compressionType=Tip kompresije
#XFLD
schemaRegistry=Koristi registar šema
#XFLD
subjectNameStrat=Strategija naziva subjekta
#XFLD
compatibilityType=Tip kompatibilnosti
#XFLD
confluentTopicName=Naziv teme
#XFLD
confluentRecordName=Naziv zapisa
#XFLD
confluentSubjectNamePreview=Prethodni prikaz naziva subjekta
#XMSG
serializationChangeToastMsgUpdated2=Format serijalizacije promenjen u JSON jer registar šema nije aktiviran. Da biste vratili format serijalizacije u AVRO, prvo morate aktivirati registar šema.
#XBUT
confluentTopicNameInfo=Naziv teme se uvek zasniva na nazivu ciljnog objekta. Možete da ga promenite tako što ćete preimenovati ciljni objekat.
#XMSG
emptyRecordNameValidationHeaderMsg=Unesite naziv zapisa.
#XMSG
emptyPartionHeader=Unesite broj particija.
#XMSG
invalidPartitionsHeader=Unesite važeći broj particija.
#XMSG
invalidpartitionsDesc=Unesite broj između 1 i 200.000.
#XMSG
emptyrFactorHeader=Unesite faktor replikacije.
#XMSG
invalidrFactorHeader=Unesite važeći faktor replikacije.
#XMSG
invalidrFactorDesc=Unesite broj između 1 i 32.767.
#XMSG
emptyRecordNameValidationDescMsg=Ako se koristi format serijalizacije "AVRO", podržani su samo sledeći znakovi:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(donja crta)
#XMSG
validRecordNameValidationHeaderMsg=Unesite važeći naziv zapisa.
#XMSG
validRecordNameValidationDescMsgUpdated=Pošto se koristi format serijalizacije "AVRO", naziv zapisa mora da se sastoji samo od alfanumeričkih znakova (A-Z, a-z, 0-9) i donje crte (_). Mora počinjati slovom ili donjom crtom.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled="Broj niti objekta za delta učitavanja" se može postaviti čim jedan objekat ili više njih budu imali tip učitavanja "Početno i delta".
#XMSG
invalidTargetName=Nevažeći naziv kolone
#XMSG
invalidTargetNameDesc=Naziv ciljne kolone mora da se sastoji samo od alfanumeričkih znakova (A-Z, a-z, 0-9) i donje crte (_).
#XFLD
consumeOtherSchema=Upotrebi druge verzije šeme
#XFLD
ignoreSchemamissmatch=Zanemari nepodudaranje šeme
#XFLD
confleuntDatatruncation=Neuspeh pri skraćivanju podataka
#XFLD
isolationLevel=Nivo izolacije
#XFLD
confluentOffset=Početna tačka
#XFLD
signavioGroupDeltaFilesByText=Ništa
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=Ne
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=Ne

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Projekcije
#XBUT
txtAdd=Dodaj
#XBUT
txtEdit=Uredi
#XMSG
transformationText=Dodajte projekciju da biste postavili filter ili preslikavanje.
#XMSG
primaryKeyRequiredText=Odaberite primarni ključ uz Konfiguriši šemu.
#XFLD
lblSettings=Podešavanja
#XFLD
lblTargetSetting={0}: Ciljna podešavanja
#XMSG
@csvRF=Odaberite fajl koji sadrži definiciju šeme koju želite da primenite na sve fajlove u folderu.
#XFLD
lblSourceColumns=Izvorne kolone
#XFLD
lblJsonStructure=Struktura JSON
#XFLD
lblSourceSetting={0}: Izvorna podešavanja
#XFLD
lblSourceSchemaSetting={0}: Podešavanja izvorne šeme
#XBUT
messageSettings=Podešavanja poruke
#XFLD
lblPropertyTitle1=Svojstva objekta
#XFLD
lblRFPropertyTitle=Svojstva toka replikacije
#XMSG
noDataTxt=Nema kolona za prikaz.
#XMSG
noTargetObjectText=Ciljni objekat nije odabran.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Ciljne kolone
#XMSG
searchColumns=Traži kolone
#XTOL
cdcColumnTooltip=Kolona za delta snimanje
#XMSG
sourceNonDeltaSupportErrorUpdated=Izvorni objekat ne podržava delta snimanje.
#XMSG
targetCDCColumnAdded=2 ciljne kolone su dodate za delta snimanje.
#XMSG
deltaPartitionEnable=Ograničenje niti objekta za delta učitavanja dodato u izvorna podešavanja.
#XMSG
attributeMappingRemovalTxt=Uklanjanje nevažećih preslikavanja koja nisu podržana za novi ciljni objekat.
#XMSG
targetCDCColumnRemoved=2 ciljne kolone koje se koriste za delta snimanje su uklonjene.
#XMSG
replicationLoadTypeChanged=Tip učitavanja promenjen u "Početno i delta".
#XMSG
sourceHDLFLoadTypeError=Promenite tip učitavanja u "Početno i delta".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Da biste replicirali objekat iz izvorne veze s tipom veze SAP HANA Cloud, fajlovi jezera podataka u SAP Datasphere, morate koristiti tip učitavanja u "Početno i delta".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Aktivirajte delta snimanje.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Da biste replicirali objekat iz izvorne veze s tipom veze SAP HANA Cloud, fajlovi jezera podataka u SAP Datasphere, morate aktivirati delta snimanje.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Promenite ciljni objekat.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Ciljni objekat se ne može koristiti jer je delta snimanje deaktivirano. Možete preimenovati ciljni objekat (što omogućava kreiranje novog objekta s delta snimanjem) ili ga preslikati u postojeći objekat sa aktiviranim delta snimanjem.
#XMSG
deltaPartitionError=Unesite važeći broj niti objekta za delta učitavanja.
#XMSG
deltaPartitionErrorDescription=Unesite vrednost između 1 i 10.
#XMSG
deltaPartitionEmptyError=Unesite broj niti objekta za delta učitavanja.
#XFLD
@lblColumnDescription=Opis
#XMSG
@lblColumnDescriptionText1=Za tehničke svrhe - upravljanje dvostrukim zapisima prouzrokovanim problemima pri replikaciji izvornih objekata zasnovanih na ABAP-u koji nemaju primarni ključ.
#XFLD
storageType=Skladište
#XFLD
skipUnmappedColLbl=Preskoči nepreslikane kolone
#XFLD
abapContentTypeLbl=Tip sadržaja
#XFLD
autoMergeForTargetLbl=Automatski spoj podatke
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Opšte
#XFLD
lblBusinessName=Poslovni naziv
#XFLD
lblTechnicalName=Tehnički naziv
#XFLD
lblPackage=Paket
#XFLD
statusPanel=Status izvođenja
#XBTN: Schedule dropdown menu
SCHEDULE=Raspored
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Uredi raspored
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Izbriši raspored
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Kreiraj raspored
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Nije uspela provera validacije rasporeda
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Nije moguće kreirati raspored jer se tok replikacije trenutno implementira.{0}Sačekajte da se tok replikacije implementira.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Za tokove replikacije koji sadrže objekte s tipom učitavanja "Početno i delta" ne može se kreirati raspored.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Za tokove replikacije koji sadrže objekte s tipom učitavanja "Početno i delta/Samo delta" ne može se kreirati raspored.
#XFLD : Scheduled popover
SCHEDULED=Planirano
#XFLD
CREATE_REPLICATION_TEXT=Kreiraj tok replikacije
#XFLD
EDIT_REPLICATION_TEXT=Uredi tok replikacije
#XFLD
DELETE_REPLICATION_TEXT=Izbriši tok replikacije
#XFLD
REFRESH_FREQUENCY=Učestalost
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Tok replikacije se ne može implementirati jer postojeći raspored{0} još uvek ne podržava tip učitavanja "Početno i delta".{0}{0}Da biste implementirali tok replikacije morate postaviti tipove učitavanja svih objekata {0} na "Samo početno". Umesto toga, možete da izbrišete raspored, implementirate {0}tok replikacije, pa pokrenete novo izvođenje. Rezultat toga je izvođenje bez kraja{0}, što takođe podržava objekte s tipom učitavanja "Početno i delta".
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Tok replikacije se ne može implementirati jer postojeći raspored{0} još ne podržava tip učitavanja "Početno i delta/Samo delta".{0}{0}Da biste implementirali tok replikacije, morate postaviti tipove učitavanja svih objekata {0} na "Samo početno". Umesto toga, možete da izbrišete raspored, implementirate {0}tok replikacije, a zatim pokrenete novo izvođenje. Rezultat toga je izvođenje bez {0}kraja, što takođe podržava objekte s tipom učitavanja "Početno i delta/Samo delta".
#XMSG
SCHEDULE_EXCEPTION=Nije uspelo pozivanje detalja rasporeda
#XFLD: Label for frequency column
everyLabel=Svakih
#XFLD: Plural Recurrence text for Hour
hoursLabel=sati
#XFLD: Plural Recurrence text for Day
daysLabel=dana
#XFLD: Plural Recurrence text for Month
monthsLabel=meseci
#XFLD: Plural Recurrence text for Minutes
minutesLabel=minuta
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Nije uspelo pozivanje informacija o mogućnosti rasporeda.
#XFLD :Paused field
PAUSED=Pauzirano
#XMSG
navToMonitoring=Otvori u Monitoru tokova
#XFLD
statusLbl=Status
#XFLD
lblLastRunExecuted=Početak poslednjeg izvođenja
#XFLD
lblLastExecuted=Poslednje izvođenje
#XFLD: Status text for Completed
statusCompleted=Završeno
#XFLD: Status text for Running
statusRunning=Izvodi se
#XFLD: Status text for Failed
statusFailed=Nije uspelo
#XFLD: Status text for Stopped
statusStopped=Zaustavljeno
#XFLD: Status text for Stopping
statusStopping=Zaustavlja se
#XFLD: Status text for Active
statusActive=Aktivno
#XFLD: Status text for Paused
statusPaused=Pauzirano
#XFLD: Status text for not executed
lblNotExecuted=Još nije izvedeno
#XFLD
messagesSettings=Podešavanja poruka
#XTOL
@validateModel=Poruke validacije
#XTOL
@hierarchy=Hijerarhija
#XTOL
@columnCount=Broj kolona
#XMSG
VAL_PACKAGE_CHANGED=Dodelili ste ovaj objekat paketu "{1}". Kliknite na “Sačuvaj” da biste potvrdili i vrednovali ovu promenu. Uzmite u obzir da dodelu paketu nije moguće poništiti u ovom uređivaču nakon snimanja.
#XMSG
MISSING_DEPENDENCY=Zavisnosti objekta ''{0}'' ne mogu se rešiti u paketu "{1}".
#XFLD
deltaLoadInterval=Interval delta učitavanja
#XFLD
lblHour=Sati (0-24)
#XFLD
lblMinutes=Minuti (0-59)
#XMSG
maxHourOrMinErr=Unesite vrednosti između 0 i {0}
#XMSG
maxDeltaInterval=Maksimalna vrednost intervala delta učitavanja je 24 sata.{0}Promenite vrednost minuta ili vrednost sata u skladu s tim.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Put ciljnog spremnika
#XFLD
confluentSubjectName=Naziv subjekta
#XFLD
confluentSchemaVersion=Verzija šeme
#XFLD
confluentIncludeTechKeyUpdated=Uključi tehnički ključ
#XFLD
confluentOmitNonExpandedArrays=Izostavi neproširene nizove
#XFLD
confluentExpandArrayOrMap=Proširi niz ili mapu
#XCOL
confluentOperationMapping=Preslikavanje operacije
#XCOL
confluentOpCode=Opcode
#XFLD
confluentInsertOpCode=Unesi
#XFLD
confluentUpdateOpCode=Ažuriraj
#XFLD
confluentDeleteOpCode=Izbriši
#XFLD
expandArrayOrMapNotSelectedTxt=Nije odabrano
#XFLD
confluentSwitchTxtYes=Da
#XFLD
confluentSwitchTxtNo=Ne
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Greška
#XTIT
executeWarning=Upozorenje
#XMSG
executeunsavederror=Sačuvajte tok replikacije pre nego što ga izvedete.
#XMSG
executemodifiederror=Postoje nesačuvane promene u toku replikacije. Sačuvajte tok replikacije.
#XMSG
executeundeployederror=Morate implementirati tok replikacije da biste mogli da ga izvedete.
#XMSG
executedeployingerror=Sačekajte da se završi implementacija.
#XMSG
msgRunStarted=Izvođenje pokrenuto
#XMSG
msgExecuteFail=Nije uspelo izvođenje toka replikacije
#XMSG
titleExecuteBusy=Sačekajte.
#XMSG
msgExecuteBusy=Pripremamo vaše podatke za izvođenje toka replikacije.
#XTIT
executeConfirmDialog=Upozorenje
#XMSG
msgExecuteWithValidations=Tok replikacije ima greške validacije. Izvođenje toka replikacije može dovesti do greške.
#XMSG
msgRunDeployedVersion=Postoje promene za implementaciju. Biće pokrenuta poslednja implementirana verzija toka replikacije. Da li želite da nastavite?
#XBUT
btnExecuteAnyway=Ipak izvedi
#XBUT
btnExecuteClose=Zatvori
#XBUT
loaderClose=Zatvori
#XTIT
loaderTitle=Učitavanje
#XMSG
loaderText=Pozivanje detalja sa servera
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Tok replikacije za ciljnu vezu koja nije SAP nije moguće pokrenuti
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=zato što izlazni obim nije dostupan za ovaj mesec.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Administrator može da poveća izlazne blokove Premium za ovog zakupca
#XMSG
premiumOutBoundRFAdminErrMsgPart2=i izlazni obim učini dostupnim za ovaj mesec.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Greška
#XTIT
deployInfo=Informacije
#XMSG
deployCheckFailException=Izuzetak tokom implementacije
#XMSG
deployGBQFFDisabled=Implementacija tokova replikacije s ciljnom vezom sa Google BigQuery-jem trenutno nije moguća jer izvršavamo održavanje ove funkcije.
#XMSG
deployKAFKAFFDisabled=Implementacija tokova replikacije s ciljnom vezom sa Apache Kafka-om trenutno nije moguća jer izvršavamo održavanje ove funkcije.
#XMSG
deployConfluentDisabled=Implementacija tokova replikacije s ciljnom vezom sa Confluent Kafka trenutno nije moguća jer je u toku održavanje ove funkcije.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Za sledeće ciljne objekte, nazive tabele delta snimanja već koriste druge tabele u repozitorijumu: {0} Morate da preimenujete te ciljne objekte da biste obezbedili da povezani nazivi tabele delta snimanja budu jedinstveni pre nego što implementirate tok replikacije.
#XMSG
deployDWCSourceFFDisabled=Implementacija tokova replikacije koji imaju SAP Datasphere kao izvor trenutno nije moguća jer izvršavamo održavanje ove funkcije.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Implementacija tokova replikacije koji sadrže lokalne tabele kompatibilne s deltom kao izvorne objekte trenutno nije moguća jer vršimo održavanje ove funkcije.
#XMSG
deployHDLFSourceFFDisabled=Implementacija tokova replikacije koji imaju izvorne veze s tipom veze SAP HANA Cloud, fajlovi jezera podataka trenutno nije moguća jer vršimo održavanje.
#XMSG
deployObjectStoreAsSourceFFDisabled=Implementacija tokova replikacije koji imaju dobavljače skladišnog prostora kao izvor trenutno nije moguća.
#XMSG
deployConfluentSourceFFDisabled=Implementacija tokova replikacije koji imaju Confluent Kafka kao izvor trenutno nije moguća jer izvršavamo održavanje ove funkcije.
#XMSG
deployMaxDWCNewTableCrossed=Velike tokove replikacije nije moguće "sačuvati i implementirati" u jednom koraku. Prvo sačuvajte tok replikacije, a zatim ga implementirajte.
#XMSG
deployInProgressInfo=Implementacija je već u toku.
#XMSG
deploySourceObjectInUse=Izvorni objekti {0} se već koriste u tokovima replikacije {1}.
#XMSG
deployTargetSourceObjectInUse=Izvorni objekti {0} se već koriste u tokovima replikacije {1}. Ciljni objekti {2} se već koriste u tokovima replikacije {3}.
#XMSG
deployReplicationFlowCheckError=Greška pri proveri toka replikacije: {0}
#XMSG
preDeployTargetObjectInUse=Ciljni objekti {0} se već koriste u tokovima replikacije {1} i ne možete imati isti ciljni objekat u dva različita toka replikacije. Odaberite drugi ciljni objekat i pokušajte ponovo.
#XMSG
runInProgressInfo=Tok replikacije se već izvodi.
#XMSG
deploySignavioTargetFFDisabled=Implementacija tokova replikacije koji imaju SAP Signavio kao cilj trenutno nije moguća jer vršimo održavanje ove funkcije.
#XMSG
deployHanaViewAsSourceFFDisabled=Implementacija tokova replikacije koji imaju poglede kao izvorne objekte za odabranu izvornu vezu trenutno nije moguća. Pokušajte ponovo kasnije.
#XMSG
deployMsOneLakeTargetFFDisabled=Implementacija tokova replikacije koji imaju MS OneLake kao cilj trenutno nije moguća jer vršimo održavanje ove funkcije.
#XMSG
deploySFTPTargetFFDisabled=Implementacija tokova replikacije koji imaju SFTP kao cilj trenutno nije moguća jer vršimo održavanje ove funkcije.
#XMSG
deploySFTPSourceFFDisabled=Implementacija tokova replikacije koji imaju SFTP kao izvor trenutno nije moguća jer vršimo održavanje ove funkcije.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Tehnički naziv
#XFLD
businessNameInRenameTarget=Poslovni naziv
#XTOL
renametargetDialogTitle=Preimenuj ciljni objekat
#XBUT
targetRenameButton=Preimenuj
#XBUT
targetRenameCancel=Odustani
#XMSG
mandatoryTargetName=Morate uneti naziv.
#XMSG
dwcSpecialChar=_(donja crta) je jedini dozvoljeni poseban znak.
#XMSG
dwcWithDot=Naziv ciljne tabele može da sadrži latinična slova, brojeve, donje crte (_) i tačke (.). Prvi znak mora biti slovo, broj ili donja crta (ne tačka).
#XMSG
nonDwcSpecialChar=Dozvoljeni posebni znakovi su _(donja crta) -(crtica) .(tačka)
#XMSG
firstUnderscorePattern=Naziv ne sme počinjati s _(donjom crtom)

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: Prikaži SQL izjavu Kreiraj tabelu
#XMSG
sqlDialogMaxPKWarning=U Google BigQuery-ju, podržano je maksimalno 16 primarnih ključeva, a izvorni objekat ima veći broj. Stoga primarni ključevi nisu definisani u ovoj izjavi.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Jedna ili više izvornih kolona imaju tipove podataka koji se ne mogu definisati kao primarni ključevi u Google BigQuery-ju. Stoga primarni ključevi nisu definisani u ovom slučaju. U Google BigQuery-ju, samo sledeći tipovi podataka mogu imati primarni ključ: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Kopiraj i zatvori
#XBUT
closeDDL=Zatvori
#XMSG
copiedToClipboard=Kopirano u prelaznu memoriju


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Objekat "{0}" ne može biti deo lanca zadataka jer nema završetak (pošto uključuje objekte s tipom učitavanja Početno i delta/Samo delta).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Objekat "{0}" ne može biti deo lanca zadataka jer nema završetak (pošto uključuje objekte s tipom učitavanja Početno i Delta).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objekat "{0}" se ne može dodati u lanac zadataka.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Postoje nesačuvani ciljni objekti. Prvo ponovo sačuvajte.{0}{0} Ponašanje ove funkcije se promenilo: U prošlosti su se ciljni objekti kreirali samo u ciljnom okruženju kada je implementiran tok replikacije.{0} Sada se objekti kreiraju već kada se tok replikacije sačuva. Vaš tok replikacije je kreiran pre ove promene i sadrži nove objekte.{0} Potrebno je da ponovo sačuvate tok replikacije pre nego što ga implementirate da bi novi objekti bili pravilno uključeni.
#XMSG
confirmChangeContentTypeMessage=Promenićete tip sadržaja. Ako to uradite, sve postojeće projekcije biće izbrisane.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Naziv subjekta
#XFLD
schemaDialogVersionName=Verzija šeme
#XFLD
includeTechKey=Uključi tehnički ključ
#XFLD
segementButtonFlat=Ravno
#XFLD
segementButtonNested=Grupisano
#XMSG
subjectNamePlaceholder=Traži naziv subjekta

#XMSG
@EmailNotificationSuccess=Konfiguracija obaveštenja e-poštom vremena izvođenja je sačuvana.

#XFLD
@RuntimeEmailNotification=Obaveštenje e-poštom vremena izvođenja

#XBTN
@TXT_SAVE=Sačuvaj


