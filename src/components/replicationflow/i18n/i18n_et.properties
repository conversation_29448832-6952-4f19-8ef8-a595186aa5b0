#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Tiražeerimisvoog

#XFLD: Edit Schema button text
editSchema=Redigeeri skeemi

#XTIT : Properties heading
configSchema=Konfigureeri skeem

#XFLD: save changed button text
applyChanges=Rakenda muudatused


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Vali lähteühendus
#XFLD
sourceContainernEmptyText=Vali konteiner
#XFLD
targetConnectionEmptyText=Vali sihtühendus
#XFLD
targetContainernEmptyText=Vali konteiner
#XFLD
sourceSelectObjectText=Vali lähteobjekt
#XFLD
sourceObjectCount=Lähteobjektid ({0})
#XFLD
targetObjectText=Sihtobjektid
#XFLD
confluentBrowseContext=Valige kontekst
#XBUT
@retry=Proovi uuesti
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Rentniku täiendamine on käimas.

#XTOL
browseSourceConnection=Sirvi lähteühendust
#XTOL
browseTargetConnection=Sirvi sihtühendust
#XTOL
browseSourceContainer=Sirvi lähtekonteinerit
#XTOL
browseAndAddSourceDataset=Lisa lähteobjektid
#XTOL
browseTargetContainer=Sirvi sihtkonteinerit
#XTOL
browseTargetSetting=Sirvi sihtsätteid
#XTOL
browseSourceSetting=Sirvi lähtesätteid
#XTOL
sourceDatasetInfo=Teave
#XTOL
sourceDatasetRemove=Eemalda
#XTOL
mappingCount=See tähistab mittenimel põhinevate vastenduste/avaldiste koguarvu.
#XTOL
filterCount=See tähistab filtritingimuste kogurvu.
#XTOL
loading=Laadimine...
#XCOL
deltaCapture=Deltahõive
#XCOL
deltaCaptureTableName=Deltahõive tabel
#XCOL
loadType=Laadi tüüp
#XCOL
deleteAllBeforeLoading=Kustuta kõik enne laadimist
#XCOL
transformationsTab=Projektsioonid
#XCOL
settingsTab=Sätted

#XBUT
renameTargetObjectBtn=Nimeta sihtobjekt ümber
#XBUT
mapToExistingTargetObjectBtn=Vastenda olemasoleva sihtobjektiga
#XBUT
changeContainerPathBtn=Muuda konteineri teed
#XBUT
viewSQLDDLUpdated=Kuva SQL-i tabeli loomise lause
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Lähteobjekt ei toeta deltahõivet, kuid valitud sihtobjektil on deltahõive suvand lubatud.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Sihtobjekti ei saa kasutada, kuna deltahõive on lubatud,{0}kuigi lähteobjekt ei toeta deltahõivet.{1}Võite valida teise sihtobjekti, mis ei toeta deltahõivet.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Selle nimega sihtobjekt on juba olemas, kuid seda ei saa kasutada,{0}sest deltahõive on lubatud, kuigi lähteobjekt ei{0}toeta deltahõivet.{1}Võite sisestada sellise olemasoleva deltaobjekti nime, mis ei{0}toeta deltahõivet, või sisestada nime, mida pole juba olemas.
#XBUT
copySQLDDLUpdated=Kopeeri SQL-i tabeli loomise lause
#XMSG
targetObjExistingNoCDCColumnUpdated=Google BigQuerys olemasolevad tabelid peavad sisaldama järgmisi andmemuudatuste jäädvustuse (CDC) veerge:{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Järgmisi lähteobjekte ei toetata, kuna neil pole primaarvõtit või need kasutavad ühendust, mis ei vasta primaarvõtme toomise tingimustele:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Võimaliku lahenduse leidmiseks vt SAP KBA 3531135.
#XLST: load type list values
initial=Ainult algne
@emailUpdateError=Tõrge meiliteatiste loendi uuendamisel

#XLST
initialDelta=Algne ja delta

#XLST
deltaOnly=Ainult delta
#XMSG
confluentDeltaLoadTypeInfo=Confluent Kafka allika korral toetatakse ainult laadimistüüpi „Algne ja delta“.
#XMSG
confirmRemoveReplicationObject=Kas kinnitate, et soovite replikatsiooni kustutada?
#XMSG
confirmRemoveReplicationTaskPrompt=See toiming kustutab olemasolevad tiražeerimised. Kas soovite jätkata?
#XMSG
confirmTargetConnectionChangePrompt=See toiming lähtestab sihtühenduse, sihtkonteineri ja kustutab kõik sihtobjektid. Kas soovite jätkata?
#XMSG
confirmTargetContainerChangePrompt=See toiming lähtestab sihtkonteineri ja kustutab kõik sihtobjektid. Kas soovite jätkata?
#XMSG
confirmRemoveTransformObject=Kas kinnitate, et soovite projektsiooni {0} kustutada?
#XMSG
ErrorMsgContainerChange=Konteineritee muutmisel ilmnes tõrge.
#XMSG
infoForUnsupportedDatasetNoKeys=Järgmised lähteobjektid on toetuseta, kuna neil pole primaarvõtit:
#XMSG
infoForUnsupportedDatasetView=Järgmisi lähteobjekte, mille tüüp on Vaated, ei toetata:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Järgmist lähteobjekti ei toetata, kuna see on sisendparameetreid sisaldav SQL-vaade:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Järgmised lähteobjektid on toetuseta, kuna ekstraktimine on nende jaoks keelatud:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Confluenti ühenduste jaoks on ainsad lubatud serialiseerimisvormingud AVRO ja JSON. Järgmisi objekte ei toetata, kuna need kasutavad mõnda muud serialiseerimisvormingut:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Järgmiste objektide jaoks ei saa skeemi tuua. Valige asjakohane kontekst või kontrollige skeemi registrikonfiguratsiooni
#XTOL: warning dialog header on deleting replication task
deleteHeader=Kustuta
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Google BigQuery puhul ei ole säte „Kustuta kõik enne laadimist“ toetatud.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Säte „Kustuta kõik enne“ kustutab objekti (teema) ja loob selle enne iga tiražeerimist uuesti. Samuti kustutab see kõik määratud teated.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Selle sihttüübi korral ei ole säte „Kustuta kõik enne“ toetatud.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Tehniline nimi
#XCOL
connBusinessName=Ärinimi
#XCOL
connDescriptionName=Kirjeldus
#XCOL
connType=Tüüp
#XMSG
connTblNoDataFoundtxt=Ühendusi ei leitud
#XMSG
connectionError=Ühenduste toomisel ilmes tõrge.
#XMSG
connectionCombinationUnsupportedErrorTitle=Ühenduse kombinatsiooni ei toetata
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Tiražeerimist lahendusest {0} lahendusse {1} praegu ei toetata.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Ühenduse tüübi kombinatsiooni ei toetata.
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Tiražeerimine ühendusest, mille ühenduse tüüp on „SAP HANA Cloud, andmehoidla failid“, sihtkohta {0} pole lubatud. Tiražeerida saate ainult SAP Datasphere’i.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Vali
#XBUT
containerCancelBtn=Tühista
#XTOL
containerSelectTooltip=Vali
#XTOL
containerCancelTooltip=Tühista
#XMSG
containerContainerPathPlcHold=Konteineritee
#XFLD
containerContainertxt=Konteiner
#XFLD
confluentContainerContainertxt=Kontekst
#XMSG
infoMessageForSLTSelection=Konteinerina on lubatud ainult /SLT/hulgiülekande ID. Valige SLT alt (kui on saadaval hulgiülekande ID ja valige Edasta
#XMSG
msgFetchContainerFail=Ümbrise andmete toomisel ilmnes tõrge.
#XMSG
infoMessageForSLTHidden=Kuna see ühendus ei toeta SLT kaustu, ei kuvata neid allpool loendis.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Valige konteiner, mis sisaldab alamkaustu.
#XMSG
sftpIncludeSubFolderText=Väär
#XMSG
sftpIncludeSubFolderTextNew=Ei

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Filtreid veel ei vastendata)
#XMSG
failToFetchRemoteMetadata=Metaandemte toomisel ilmes tõrge.
#XMSG
failToFetchData=Olemasoleva sihtkoha toomisel ilmnes tõrge.
#XCOL
@loadType=Laadi tüüp
#XCOL
@deleteAllBeforeLoading=Kustuta kõik enne laadimist

#XMSG
@loading=Laadimine...
#XFLD
@selectSourceObjects=Vali lähteobjektid
#XMSG
@exceedLimit=Korraga saab importida ainult kuni {0} objekti. Eemaldage vähemalt {1} objekti valikust.
#XFLD
@objects=Objektid
#XBUT
@ok=OK
#XBUT
@cancel=Tühista
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Järgmine
#XBUT
btnAddSelection=Lisa valik
#XTOL
@remoteFromSelection=Eemalda valikust
#XMSG
@searchInForSearchField=Otsi asukohast {0}

#XCOL
@name=Tehniline nimi
#XCOL
@type=Tüüp
#XCOL
@location=Asukoht
#XCOL
@label=Ärinimi
#XCOL
@status=Olek

#XFLD
@searchIn=Otsi asukohast:
#XBUT
@available=Saadaval
#XBUT
@selection=Valik

#XFLD
@noSourceSubFolder=Tabelid ja vaated
#XMSG
@alreadyAdded=On juba diagrammis olemas
#XMSG
@askForFilter=Ridu on rohkem kui {0}. Ridade vähendamiseks sisestage filtristring.
#XFLD: success label
lblSuccess=Õnnestumine
#XFLD: ready label
lblReady=Valmis
#XFLD: failure label
lblFailed=Nurjunud
#XFLD: fetching status label
lblFetchingDetail=Üksikasjade toomine

#XMSG Place holder text for tree filter control
filterPlaceHolder=Tipi tekst ülataseme objektide filtreerimiseks
#XMSG Place holder text for server search control
serverSearchPlaceholder=Tippige ja vajutage otsimiseks sisestusklahvi Enter
#XMSG
@deployObjects={0} objekti importimine...
#XMSG
@deployObjectsStatus=Imporditud objektide arv: {0}. Objektide arv, mida ei saanud importida: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Kohaliku hoidlabrauseri avamine nurjus.
#XMSG
@openRemoteSourceBrowserError=Lähteobjektide toomine nurjus.
#XMSG
@openRemoteTargetBrowserError=Sihtobjektide toomine nurjus.
#XMSG
@validatingTargetsError=Sihtkohtade valideerimisel ilmnes tõrge.
#XMSG
@waitingToImport=Importimiseks valmis

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Objektide maksimumarv on ületatud. Ühe alliktabelite replikeerimise voo jaoks valige kuni 500 objekti.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Tehniline nimi
#XFLD
sourceObjectBusinessName=Ärinimi
#XFLD
sourceNoColumns=Veergude arv
#XFLD
containerLbl=Konteiner

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Peate tiražeerimisvoo jaoks valima lähteühenduse.
#XMSG
validationSourceContainerNonExist=Peate lähteühenduse jaosk valima konteineri.
#XMSG
validationTargetNonExist=Peate tiražeerimisvoo jaoks valima sihtühenduse.
#XMSG
validationTargetContainerNonExist=Peate sihtühenduse jaosk valima konteineri.
#XMSG
validationTruncateDisabledForObjectTitle=Tiražeerimine objektide salvestusruumidesse.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Tiražeerimine pilvsalvestusruumi on võimalik üksnes juhul, kui määratud on käsk „Kustuta kõik enne laadimist“ või sihtobjekti pole sihtkohas olemas.{0}{0} Kui soovite siiski lubada selliste objektide tiražeerimise, mille jaoks valikut „Kustuta kõik enne laadimist“ pole määratud, siis veenduge enne tiražeerimisvoo käivitamist, et süsteemis poleks sihtobjekti olemas.
#XMSG
validationTaskNonExist=Tiražeerimisvoos peab olema vähemalt üks tiražeerimine.
#XMSG
validationTaskTargetMissing=Teil peab olema sihtkoht allikaga {0} tiražeerimise jaoks.
#XMSG
validationTaskTargetIsSAC=Valitud sihtkoht on SAC artefakt: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Valitud sihtkoht pole toetatud kohalik tabel: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Sellise nimega objekt on sihtkohas juba olemas. Seda objekti ei saa siiski kasutada kohalikku hoidlasse viiva tiražeerimisvoo sihtobjektina, kuna see pole kohalik tabel.
#XMSG
validateSourceTargetSystemDifference=Peate tiražeerimisvoo jaoks valima erineva lähte- ja sihtühenduse ja konteinerikombinatsiooni.
#XMSG
validateDuplicateSources=vähemalt ühel alliktabelite replikeerimisel esineb lähteobjektinime duplikaate: {0}.
#XMSG
validateDuplicateTargets=vähemalt ühel alliktabelite replikeerimisel esineb sihtobjektinime duplikaate: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=Lähteobjekt {0} ei toeta deltahõivet, kuid sihtobjekt {1} toetab. Teil tuleb alliktabelite replikeerimine eemaldada.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Teil tuleb sihtobjekti nimega {0} alliktabelite replikeerimise jaoks valida laadimistüüp „Algne ja delta“.
#XMSG
validationAutoRenameTarget=Sihtveerud on ümber nimetatud.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Automaatne projektsioon on lisatud ja sihtkohta tiražeerimise lubamiseks on järgmised sihtveerud ümber nimetatud:{1}{1} {0} {1}{1}See on tingitud ühest järgmistest põhjustest.{1}{1}{2} Toetuseta märgid {1}{2}Reserveeritud eesliide
#XMSG
validationAutoRenameTargetDescriptionUpdated=Automaatne projektsioon on lisatud ja Google BigQuerysse tiražeerimise lubamiseks on järgmised sihtveerud ümber nimetatud:{1}{1} {0} {1}{1}See võib olla tingitud ühest järgmistest põhjustest.{1}{1}{2} Reserveeritud veerunimi{1}{2} Toetuseta märgid{1}{2} Reserveeritud eesliide
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Automaatne projektsioon on lisatud ja Confluenti tiražeerimise lubamiseks on järgmised sihtveerud ümber nimetatud:{1}{1} {0} {1}{1}See võib olla tingitud ühest järgmistest põhjustest.{1}{1}{2} Reserveeritud veerunimi{1}{2} Toetuseta märgid{1}{2} Reserveeritud eesliide
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Automaatne projektsioon on lisatud ja sihtkohta tiražeerimise lubamiseks on järgmised sihtveerud ümber nimetatud:{1}{1} {0} {1}{1}See võib olla tingitud ühest järgmistest põhjustest.{1}{1}{2} Reserveeritud veerunimi{1}{2} Toetuseta märgid{1}{2} Reserveeritud eesliide
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Sihtobjekt on ümber nimetatud.
#XMSG
autoRenameInfoDesc=Sihtobjekt on ümber nimetatud, kuna see sisaldas toetuseta märke. Toetatud on ainult järgmised märgid:{0}{0}{1}A–Z{0}{1}a–z{0}{1}0–9{0}{1}.(punkt){0}{1}_(allkriips){0}{1}-(sidekriips)
#XMSG
validationAutoTargetTypeConversion=Sihtandmete tüübid on muudetud.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Järgmiste sihtveergude sihtandmete tüüpi on muudetud, kuna Google BigQuerys lähteandmete tüüpi ei toetata:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Järgmiste sihtveergude sihtandmete tüüpi on muudetud, kuna sihtühenduses neid lähteandmete tüüpe ei toetata:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Lühendage sihtveergude nimesid.
#XMSG
validationMaxCharLengthGBQTargetDescription=Google BigQuerys on veerunimede maksimaalne tähemärkide arv 300. Kasutage järgmiste sihtveergude nimede lühendamiseks ekstrapoleerimist:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Primaarvõtmeid ei saa luua.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=Google BigQuery toetab maksimaalselt 16 primaarvõtit, aga lähteobjektis on suurem arv primaarvõtmeid. Sihtobjektis ei looda primaarvõtmeid.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Ühe või enama lähteveeru andmetüüpi ei saa Google BigQuerys määratleda primaarvõtmena. Sihtobjektis ei looda ühtki primaarvõtit.{0}{0}Google BigQuery andmetüüpidega ühilduvad järgmised sihtandmetüübid, mille jaoks saab määratleda primaarvõtme:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Määratlege primaarvõtmena vähemalt üks veerg.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Vähemalt ühe veeru peate määratlema primaarvõtmena. Seda saate teha lähteskeemi dialoogis.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Määratlege primaarvõtmena vähemalt üks veerg.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Peate määratlema primaarvõtmena ühe või mitu veergu, mis vastavad lähteobjekti primaarvõtme piirangutele. Selleks minge lähteobjekti atribuutide jaotisesse Skeemi konfigureerimine.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Sisestage kehtiv sektsiooni maksimumväärtus.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Maksimaalne sektsiooni väärtus peab olema ≥ 1 ja ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Määratlege primaarvõtmena vähemalt üks veerg.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Objekti tiražeerimiseks peate vähemalt ühe sihtveeru määratlema primaarvõtmena. Kasutage selleks projektsiooni.
#XMSG
validateHDLFNoPKExistingDatasetError=Määratlege primaarvõtmena vähemalt üks veerg.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Andmete tiražeerimiseks olemasolevasse sihtobjekti peab sellel olema üks või mitu veergu, mis on määratletud primaarvõtmena. {0} Ühe või mitme veeru esmaseks võtmeks määramiseks on järgmised valikud. {0}{1} Olemasoleva sihtobjekti muutmiseks kasutage kohalikku tabeliredaktorit. Seejärel laadige tiražeerimisvoog uuesti.{0}{1}Nimetage tiražeerimisvoos sihtobjekt ümber. See loob uue objekti kohe, kui käitamine algab. Pärast ümbernimetamist saate projektsioonis primaarvõtmeks määrata ühe või mitu veergu.{0}{1}Vastendage objekt mõne muu olemasoleva sihtobjektiga, milles üks või mitu veergu on juba primaarvõtmena määratletud.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Valitud sihtobjekt on juba hoidlas olemas: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Deltahõive tabelinimesid kasutavad juba teised hoidlas olevad tabelid: {0}. Enne kui saate tiražeerimisvoo juurutada, tuleb teil need sihtobjektid ümber nimetada, et seotud deltahõive tabelinimed oleksid kordumatud.
#XMSG
validateConfluentEmptySchema=Määratlege skeem
#XMSG
validateConfluentEmptySchemaDescUpdated=Lähtetabelil pole skeemi. Skeemi määratlemiseks valige Konfigureeri skeem
#XMSG
validationCSVEncoding=Vale CSV-kodeering
#XMSG
validationCSVEncodingDescription=Ülesande CSV-kodeering ei sobi.
#XMSG
validateConfluentEmptySchema=Valige ühilduv sihtandmetüüp
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Valige ühilduv sihtandmetüüp
#XMSG
globalValidateTargetDataTypeDesc=Veergude vastendamisel ilmnes tõrge. Valige „Ekstrapoleerimine“ ja veenduge, et kõik lähteveerud oleksid vastendatud kordumatu veeruga, ühilduva andmetüübiga veeruga ja et kõik määratletud avaldised oleksid sobivad.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Veerunimed on dubleeritud.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Dubleeritud veerunimesid ei toetata. Kasutage nende parandamiseks projektsioonidialoogi. Järgmistel sihtobjektidel on dubleeritud veerunimed: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Veerunimed on dubleeritud.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Dubleeritud veerunimesid ei toetata. Järgmistel sihtobjektidel on dubleeritud veerunimed: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Andmetes võib olla vastuolusid.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Laadimistüüp „Ainult delta“ ei võta arvesse allikas viimase salvestamise ja järgmise käituse vahel tehtud muudatusi.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Määrake uueks laadimistüübiks „Algne“.
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Selliste ABAP-i põhiste objektide tiražeerimine, millel pole primaarvõtit, on võimalik ainult juhul, kui laadimistüüp on „Ainult algne“.
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Keelake deltahõive.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Primaarvõtmeta objekti tiražeerimiseks ABAP-tüüpi lähteühendust kasutades peate esmalt deltahõive selle tabeli jaoks keelama.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Muutke sihtobjekti.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Sihtobjekti ei saa kasutada, kuna deltahõive on lubatud. Saate sihtobjekti ümber nimetada ja seejärel deltahõive uue (ümber nimetatud) objekti jaoks välja lülitada või vastendada lähteobjekti mõne olemasoleva sihtobjektiga, millel on deltahõive keelatud.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Muutke sihtobjekti.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Sihtobjekti ei saa kasutada, kuna sellel puudub nõutav tehniline veerg __load_package_id. Saate sihtobjekti ümber nimetada, kasutades nime, mida praegu veel ei ole. Süsteem loob seejärel uue objekti, millel on lähteobjektiga sama definitsioon ja mis sisaldab seda tehnilist veergu. Teise võimalusena saate vastendada sihtobjekti mõne olemasoleva objektiga, millel on nõutav tehniline veerg (__load_package_id) olemas.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Muutke sihtobjekti.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Sihtobjekti ei saa kasutada, kuna sellel puudub nõutav tehniline veerg __load_record_id. Saate sihtobjekti ümber nimetada, kasutades nime, mida praegu veel ei ole. Süsteem loob seejärel uue objekti, millel on lähteobjektiga sama definitsioon ja mis sisaldab seda tehnilist veergu. Teise võimalusena saate vastendada sihtobjekti mõne olemasoleva objektiga, millel on nõutav tehniline veerg (__load_record_id) olemas.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Muutke sihtobjekti.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Sihtobjekti ei saa kasutada, kuna selle tehnilise veeru __load_record_id andmetüüp ei ole „string(44)“. Saate sihtobjekti ümber nimetada, kasutades nime, mida praegu veel ei ole. Süsteem loob seejärel uue objekti, millel on lähteobjektiga sama definitsioon ja seega ka õige andmetüüp. Teise võimalusena saate vastendada sihtobjekti mõne olemasoleva objektiga, millel on nõutav õige andmetüübiga tehniline veerg (__load_record_id) olemas.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Muutke sihtobjekti.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Sihtobjekti ei saa kasutada, kuna sellel on primaarvõti, mida lähteobjektil aga ei ole. Saate sihtobjekti ümber nimetada, kasutades nime, mida praegu veel ei ole. Süsteem loob seejärel uue objekti, millel on lähteobjektiga sama definitsioon ja millel seega pole ka primaarvõtit. Teise võimalusena saate vastendada sihtobjekti mõne olemasoleva objektiga, millel on nõutav tehniline veerg (__load_package_id) olemas ja millel pole primaarvõtit.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Muutke sihtobjekti.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Sihtobjekti ei saa kasutada, kuna sellel on primaarvõti, mida lähteobjektil aga ei ole. Saate sihtobjekti ümber nimetada, kasutades nime, mida praegu veel ei ole. Süsteem loob seejärel uue objekti, millel on lähteobjektiga sama definitsioon ja millel seega pole ka primaarvõtit. Teise võimalusena saate vastendada sihtobjekti mõne olemasoleva objektiga, millel on nõutav tehniline veerg (__load_record_id) olemas ja millel pole primaarvõtit.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Muutke sihtobjekti.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Sihtobjekti ei saa kasutada, kuna selle tehnilise veeru __load_package_id andmetüüp ei ole „binary(>=256)“. Saate sihtobjekti ümber nimetada, kasutades nime, mida praegu veel ei ole. Süsteem loob seejärel uue objekti, millel on lähteobjektiga sama definitsioon ja seega ka õige andmetüüp. Teise võimalusena saate vastendada sihtobjekti mõne olemasoleva objektiga, millel on nõutav õige andmetüübiga tehniline veerg (__load_package_id) olemas.
#XMSG
validationAutoRenameTargetDPID=Sihtveerud on ümber nimetatud.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Eemaldage lähteobjekt.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Lähteobjektis pole võtmeveergu, kuid selles kontekstis seda ei toetata.
#XMSG
validationAutoRenameTargetDPIDDescription=Automaatne projektsioon on lisatud ja järgmised sihtveerud on ümber nimetatud, et lubada ilma võtmeteta ABAP-allikast tiražeerimist:{1}{1} {0} {1}{1}See võib olla tingitud ühest järgmistest põhjustest.{1}{1}{2} Reserveeritud veerunimi{1}{2} Toetuseta märgid{1}{2} Reserveeritud eesliide
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Tiražeerimine sihtsüsteemi {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Tiražeerimisvoogusid, mille sihtkohaks on {0}, ei saa praegu salvestada ja juurutada, sest käimas on funktsiooni hooldustööd.
#XMSG
TargetColumnSkippedLTF=Sihtveerg on vahele jäetud.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Sihtveerg on toetuseta andmetüübi tõttu vahele jäetud. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Ajaveerg kui primaarvõti.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Lähteobjektis on ajaveerg määratud primaarvõtmeks, kuid selles kontekstis seda ei toetata.
#XMSG
validateNoPKInLTFTarget=Primaarvõti on puudu.
#XMSG
validateNoPKInLTFTargetDescription=Primaarvõtit pole sihtkohas määratletud, kuid selles kontekstis seda ei toetata.
#XMSG
validateABAPClusterTableLTF=ABAP-klastertabel.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Lähteobjekt on ABAP-klastertabel, kuid selles kontekstis seda ei toetata.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Näib, et te pole veel andmeid lisanud.
#YINS
welcomeText2=Tiražeerimisvoo käivitamiseks valige ühendus ja lähteobjekt vasakult poolt.

#XBUT
wizStep1=Vali lähteühendus
#XBUT
wizStep2=Vali lähtekonteiner
#XBUT
wizStep3=Lisa lähteobjektid

#XMSG
limitDataset=Objektide maksimumarv on täis. Uute lisamiseks eemaldage olemasolevaid objekte või looge uus alliktabelite replikeerimise voog.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Alliktabelite replikeerimise voogu sellele mitte-SAPi sihtühendusele ei saa algatada, sest selle kuu jaoks ei ole saadaval väljaminevat mahtu.
#XMSG
premiumOutBoundRFAdminWarningMsg=Administraator saab suurendada selle rentniku Premium Outboundi plokke, muutes väljamineva mahu selle kuu jaoks saadavaks.
#XMSG
messageForToastForDPIDColumn2={0} objekti sihtkohta on lisatud uus veerg – seda on vaja duplikaatkirjete töötlemiseks seoses ABAP-i põhiste lähteobjektidega, millel pole primaarvõtit.
#XMSG
PremiumInboundWarningMessage=Olenevalt tiražeerimisvoogude arvust ja tiražeeritavast andmemahust võivad SAP HANA ressursid,{0}mida on vaja andmete tiražeerimiseks teenuse {1} kaudu, ületada teie rentniku jaoks saadaoleva võimsuse.
#XMSG
PremiumInboundWarningMsg=Olenevalt tiražeerimisvoogude arvust ja tiražeeritavast andmemahust võivad{0}SAP HANA ressursid, mida on vaja andmete tiražeerimiseks teenuse „{1}“ kaudu, ületada teie rentniku jaoks saadaoleva võimsuse.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Sisestage projektsiooni nimi.
#XMSG
emptyTargetColumn=Sisestage sihtveeru nimi.
#XMSG
emptyTargetColumnBusinessName=Sisestage sihtveeru ärinimi.
#XMSG
invalidTransformName=Sisestage projektsiooni nimi.
#XMSG
uniqueColumnName=Nimetage sihtveerg ümber.
#XMSG
copySourceColumnLbl=Kopeerige veerud lähteobjektist
#XMSG
renameWarning=Sihttabeli ümbernimetamisel valige kindlasti kordumatu nimi. Kui uue nimega tabel on selles ruumis juba olemas, kasutab see tolle tabeli määratlust.

#XMSG
uniqueColumnBusinessName=Nimetage sihtveeru ärinimi ümber.
#XMSG
uniqueSourceMapping=Valige mõni muu lähteveerg.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=Lähteveergu {0} kasutavad juba järgmised sihtveerud:{1}{1}{2}{1}{1} Projektsiooni salvestamiseks valige selle sihtveeru või teiste sihtveergude jaoks lähteveerg, mis pole veel kasutusel.
#XMSG
uniqueColumnNameDescription=Sisestatud sihtveerunimi on juba olemas. Projektsiooni salvestamiseks peate siin sisestama kordumatu veerunime.
#XMSG
uniqueColumnBusinessNameDesc=Sihtveeru ärinimi on juba olemas. Projektsiooni salvestamiseks peate siin sisestama kordumatu veeru ärinime.
#XMSG
emptySource=Valige lähteveerg või sisestage konstant.
#XMSG
emptySourceDescription=Sobiva vastenduskirje loomiseks peate valima lähteveeru või sisestama konstantväärtuse.
#XMSG
emptyExpression=Määratlege vastendus.
#XMSG
emptyExpressionDescription1=Sisestage lähteveerg, millega soovite sihtveeru vastendada, või märkige veerus {0} Funktsioonid/konstandid {1} kuvatav märkeruut. {2} {2} Funktsioonid sisestatakse sihtandmete tüübi alusel automaatselt. Konstantväärtused saab sisestada käsitsi.
#XMSG
numberExpressionErr=Sisestage arv.
#XMSG
numberExpressionErrDescription=Olete valinud arvulise andmetüübi. Seega tohib sisestada ainult numbreid ja kümnendpunkti, kui see on kohaldatav. Ärge kasutage üksikjutumärke.
#XMSG
invalidLength=Sisestage sobiv pikkuse väärtus.
#XMSG
invalidLengthDescription=Andmetüübi pikkus peab võrduma lähteveeru pikkusega või olema sellest suurem ning jääma vahemikku 1–5000.
#XMSG
invalidMappedLength=Sisestage sobiv pikkuse väärtus.
#XMSG
invalidMappedLengthDescription=Andmetüübi pikkus peab võrduma lähteveeru {0} pikkusega või olema sellest suurem ning jääma vahemikku 1–5000.
#XMSG
invalidPrecision=Sisestage sobiv täpsuse väärtus.
#XMSG
invalidPrecisionDescription=Täpsus määratleb numbrikohtade koguarvu. Skaala määratleb kümnendpunktile järgnevate numbrikohtade arvu ning võib olla vahemikus 0 kuni täpsus.{0}{0} Näited {0}{1} Täpsus 6, skaala 2 vastab sellistele arvudele nagu 1234,56.{0}{1} Täpsus 6, skaala 6 vastab sellistele arvudele nagu 0,123546.{0} {0} Sihtkoha täpsus ja mastaap peavad ühilduma allika täpsuse ja mastaabiga, et kõik allikast pärinevad numbrikohad sobiksid sihtväljale. Näiteks kui allika täpsus on 6 ja skaala 2 (ja seega enne kümnendpunkti muud numbrid kui 0), ei tohi sihtkohal olla täpsuseks 6 ja skaalaks 6.
#XMSG
invalidPrimaryKey=Sisestage vähemalt üks primaarvõti.
#XMSG
invalidPrimaryKeyDescription=Primaarvõtit pole selle skeemi jaoks määratletud.
#XMSG
invalidMappedPrecision=Sisestage sobiv täpsuse väärtus.
#XMSG
invalidMappedPrecisionDescription1=Täpsus määratleb numbrikohtade koguarvu. Skaala määratleb kümnendpunktile järgnevate numbrikohtade arvu ning võib olla vahemikus 0 kuni täpsus.{0}{0} Näited{0}{1} Täpsus 6, skaala 2 vastab sellistele arvudele nagu 1234,56.{0}{1} Täpsus 6, skaala 6 vastab sellistele arvudele nagu 0,123546.{0}{0}Andmetüübi täpsus peab olema võrdne allika ({2}) täpsusega või sellest suurem.
#XMSG
invalidScale=Sisestage sobiv mastaabi väärtus.
#XMSG
invalidScaleDescription=Täpsus määratleb numbrikohtade koguarvu. Skaala määratleb kümnendpunktile järgnevate numbrikohtade arvu ning võib olla vahemikus 0 kuni täpsus.{0}{0} Näited {0}{1} Täpsus 6, skaala 2 vastab sellistele arvudele nagu 1234,56.{0}{1} Täpsus 6, skaala 6 vastab sellistele arvudele nagu 0,123546.{0} {0} Sihtkoha täpsus ja mastaap peavad ühilduma allika täpsuse ja mastaabiga, et kõik allikast pärinevad numbrikohad sobiksid sihtväljale. Näiteks kui allika täpsus on 6 ja skaala 2 (ja seega enne kümnendpunkti muud numbrid kui 0), ei tohi sihtkohal olla täpsuseks 6 ja skaalaks 6.
#XMSG
invalidMappedScale=Sisestage sobiv mastaabi väärtus.
#XMSG
invalidMappedScaleDescription1=Täpsus määratleb numbrikohtade koguarvu. Skaala määratleb kümnendpunktile järgnevate numbrikohtade arvu ning võib olla vahemikus 0 kuni täpsus.{0}{0} Näited {0}{1} Täpsus 6, skaala 2 vastab sellistele arvudele nagu 1234,56.{0}{1} Täpsus 6, skaala 6 vastab sellistele arvudele nagu 0,123546.{0}{0} Andmetüübi skaala peab olema võrdne allika ({2}) skaalaga või sellest suurem.
#XMSG
nonCompatibleDataType=Valige ühilduv sihtandmetüüp.
#XMSG
nonCompatibleDataTypeDescription1=Siin määratud andmetüüp peab ühilduma lähteandmete tüübiga ({0}). {1}{1} Näide: kui lähteveeru andmetüübiks on string ja see sisaldab tähti, ei tohi sihtkoha jaoks kasutada kümnendarvu andmetüüpi.
#XMSG
invalidColumnCount=Valige lähteveerg.
#XMSG
ObjectStoreInvalidScaleORPrecision=Sisestage täpsuse ja skaala jaoks sobiv väärtus.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=Esimene väärtus on täpsus, mis määrab numbrite koguarvu. Teine väärtus on skaala, mis määrab kümnendkoha järel olevad numbrid. Sisestage sihtskaala väärtus, mis on suurem kui lähteskaala väärtus, ja veenduge, et erinevus sisestatud sihtskaala ja täpsusväärtuse vahel on suurem kui erinevus lähteskaala ja täpsusväärtuse vahel.
#XMSG
InvalidPrecisionORScale=Sisestage täpsuse ja skaala jaoks sobiv väärtus.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=Esimene väärtus on täpsus, mis määratleb numbrikohtade koguarvu. Teine väärtus on skaala, mis määratleb kümnendpunktile järgnevate numbrikohtade arvu.{0}{0}Kuna lähteandmete tüüp on Google BigQuerys toetuseta, teisendatakse see sihtandmetüübiks DECIMAL. Sellisel juhul saab täpsuse määratleda ainult vahemikus 38–76 ja skaala vahemikus 9–38. Lisaks peab täpsusest skaala lahutamise tulemus (mis tähistab kümnendpunktile eelnevate numbrikohtade arvu) olema vahemikus 29–38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=Esimene väärtus on täpsus, mis määratleb numbrikohtade koguarvu. Teine väärtus on skaala, mis määratleb kümnendpunktile järgnevate numbrikohtade arvu.{0}{0}Kuna lähteandmete tüüp on Google BigQuerys toetuseta, teisendatakse see sihtandmetüübiks DECIMAL. Sellisel juhul tuleb täpsus määratleda väärtusena 20 või suurema väärtusena. Lisaks peab täpsusest skaala lahutamise tulemus (mis tähistab kümnendpunktile eelnevate numbrikohtade arvu) olema vähemalt 20.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=Esimene väärtus on täpsus, mis määratleb numbrikohtade koguarvu. Teine väärtus on skaala, mis määratleb komakohale järgnevate numbrikohtade arvu.{0}{0}Kuna lähteandmete tüüpi sihtkohas ei toetata, teisendatakse see sihtandmetüübiks DECIMAL. Sellisel juhul tuleb täpsus määratleda arvuga, mis on vahemikus 1 kuni 38 (kaasa arvatud), ning skaala peab olema täpsusega võrdne või sellest väiksem.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=Esimene väärtus on täpsus, mis määratleb numbrikohtade koguarvu. Teine väärtus on skaala, mis määratleb komakohale järgnevate numbrikohtade arvu.{0}{0}Kuna lähteandmete tüüpi sihtkohas ei toetata, teisendatakse see sihtandmetüübiks DECIMAL. Sellisel juhul tuleb täpsus määratleda väärtusena 20 või suurema väärtusena. Lisaks peab täpsusest skaala lahutamise tulemus (mis tähistab komakohale eelnevate numbrikohtade arvu) olema vähemalt 20.
#XMSG
invalidColumnCountDescription=Sobiva vastenduskirje loomiseks peate valima lähteveeru või sisestama konstantväärtuse.
#XMSG
duplicateColumns=Nimetage sihtveerg ümber.
#XMSG
duplicateGBQCDCColumnsDesc=Sihtveeru nimi on Google BigQuerys reserveeritud. Projektsiooni salvestamiseks peate selle ümber nimetama.
#XMSG
duplicateConfluentCDCColumnsDesc=Sihtveeru nimi on Confluentis reserveeritud. Projektsiooni salvestamiseks peate selle ümber nimetama.
#XMSG
duplicateSignavioCDCColumnsDesc=Sihtveeru nimi on SAP Signavios reserveeritud. Projektsiooni salvestamiseks peate selle ümber nimetama.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Sihtveeru nimi on MS OneLake’is reserveeritud. Projektsiooni salvestamiseks peate selle ümber nimetama.
#XMSG
duplicateSFTPCDCColumnsDesc=Sihtveeru nimi on SFTP-s reserveeritud. Projektsiooni salvestamiseks peate selle ümber nimetama.
#XMSG
GBQTargetNameWithPrefixUpdated1=Sihtveeru nimi sisaldab Google BigQuerys reserveeritud eesliidet. Projektsiooni salvestamiseks peate selle ümber nimetama. {0}{0}Sihtveeru nime alguses ei tohi olla ühtki järgmistest stringidest.{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Lühendage sihtveeru nime.
#XMSG
GBQtargetMaxLengthDesc=Google BigQuerys tohib veerunimi sisaldada kuni 300 märki. Projektsiooni salvestamiseks lühendage sihtveeru nime.
#XMSG
invalidMappedScalePrecision=Sihtkoha täpsus ja mastaap peavad ühilduma allika täpsuse ja mastaabiga, et kõik allikast pärinevad numbrikohad sobiksid sihtväljale.
#XMSG
invalidMappedScalePrecisionShortText=Sisestage sobiv täpsuse ja mastaabi väärtus.
#XMSG
validationIncompatiblePKTypeDescProjection3=Ühe või enama lähteveeru andmetüüpi ei saa Google BigQuerys määratleda primaarvõtmena. Sihtobjektis ei looda ühtki primaarvõtit.{0}{0}Google BigQuery andmetüüpidega ühilduvad järgmised sihtandmetüübid, mille jaoks saab määratleda primaarvõtme:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Tühjendage column __message_id.
#XMSG
uncheckColumnMessageIdDesc=Veerg: primaarvõti
#XMSG
validationOpCodeInsert=Lisamistoimingu jaoks peate sisestama väärtuse.
#XMSG
recommendDifferentPrimaryKey=Soovitame reatasemel valida mõne muu primaarvõtme.
#XMSG
recommendDifferentPrimaryKeyDesc=Kui toimingu kood on juba määratletud, on probleemide (nt veergude dubleerimise) vältimiseks soovitatav valida massiiviindeksi ja üksuste jaoks erinevad primaarvõtmed.
#XMSG
selectPrimaryKeyItemLevel=Nii päise- kui ka reataseme jaoks peate valima vähemalt ühe primaarvõtme.
#XMSG
selectPrimaryKeyItemLevelDesc=Massiivi või vastenduse laiendamise korral peate valima kaks primaarvõtit – ühe päise- ja ühe reatasemel.
#XMSG
invalidMapKey=Päisetasemel peate valima vähemalt ühe primaarvõtme.
#XMSG
invalidMapKeyDesc=Massiivi või vastenduse laiendamise korral peate valima primaarvõtme päisetasemel.
#XFLD
txtSearchFields=Otsi sihtveerge
#XFLD
txtName=Nimi
#XMSG
txtSourceColValidation=Ühte või mitut lähteveergu ei toetata:
#XMSG
txtMappingCount=Vastendused ({0})
#XMSG
schema=Skeem
#XMSG
sourceColumn=Lähteveerud
#XMSG
warningSourceSchema=Skeemis tehtud muudatused mõjutavad vastendusi projektsioonidialoogis.
#XCOL
txtTargetColName=Sihtveerg (tehniline nimi)
#XCOL
txtDataType=Sihtandmete tüüp
#XCOL
txtSourceDataType=Lähteandmete tüüp
#XCOL
srcColName=Lähteveerg (tehniline nimi)
#XCOL
precision=Täpsus
#XCOL
scale=Skaala
#XCOL
functionsOrConstants=Funktsioonid/konstandid
#XCOL
txtTargetColBusinessName=Sihtveerg (ärinimi)
#XCOL
prKey=Primaarvõti
#XCOL
txtProperties=Atribuudid
#XBUT
txtOK=Salvesta
#XBUT
txtCancel=Tühista
#XBUT
txtRemove=Eemalda
#XFLD
txtDesc=Kirjeldus
#XMSG
rftdMapping=Vastendamine
#XFLD
@lblColumnDataType=Andmetüüp
#XFLD
@lblColumnTechnicalName=Tehniline nimi
#XBUT
txtAutomap=Automaatne vastendamine
#XBUT
txtUp=Üles
#XBUT
txtDown=Alla

#XTOL
txtTransformationHeader=Projektsioon
#XTOL
editTransformation=Redigeeri
#XTOL
primaryKeyToolip=Võti


#XMSG
rftdFilter=Filtreeri
#XMSG
rftdFilterColumnCount=Allikas: {0}({1})
#XTOL
rftdFilterColSearch=Otsi
#XMSG
rftdFilterColNoData=Kuvamiseks veerge pole
#XMSG
rftdFilteredColNoExps=Ühtegi filtriavaldist ei ole
#XMSG
rftdFilterSelectedColTxt=Lisa filter:
#XMSG
rftdFilterTxt=Filter on saadaval järgmisele:
#XBUT
rftdFilterSelectedAddColExp=Lisa avaldis
#YINS
rftdFilterNoSelectedCol=Valige veerg filtri lisamiseks.
#XMSG
rftdFilterExp=Filtriavaldis
#XMSG
rftdFilterNotAllowedColumn=Selles veerus ei toetata filtrite lisamist.
#XMSG
rftdFilterNotAllowedHead=Toetuseta veerg
#XMSG
rftdFilterNoExp=Ühtegi filtrit pole määratletud
#XTOL
rftdfilteredTt=Filtreeritud
#XTOL
rftdremoveexpTt=Eemalda filtriavaldis
#XTOL
validationMessageTt=Valideerimisteated
#XTOL
rftdFilterDateInp=Vali kuupäev
#XTOL
rftdFilterDateTimeInp=Vali kuupäev kellaaeg
#XTOL
rftdFilterTimeInp=Valige kellaaeg
#XTOL
rftdFilterInp=Sisestage arvväärtus
#XMSG
rftdFilterValidateEmptyMsg={0} filtriavaldist veerus {1} on tühjad
#XMSG
rftdFilterValidateInvalidNumericMsg={0} filtriavaldist veerus {1} sisaldavad kehtetuid arvväärtusi
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Filtriavaldis peab sisaldama kehtivaid arvväärtusi
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Kui sihtobjekti skeemi on muudetud, kasutage muutuste kohandamiseks põhilehel funktsiooni „Vastenda olemasoleva sihtobjektiga“ ning seejärel vastendage sihtobjekt uuesti selle allikaga.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Kui sihttabel on juba olemas ja vastendus sisaldab skeemimuudatust, tuleb enne alliktabelite replikeerimise voo juurutamist tabelit vastavalt muuta.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Kui vastendus hõlmab skeemimuudatust, tuleb enne alliktabelite replikeerimise voo juurutamist tabelit vastavalt muuta.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Allika määratluses jäeti vahele järgmised toetamata veerud: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Sihtkoha määratluses jäeti vahele järgmised toetamata veerud: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Järgmisi objekte ei toetata, kuna need on tarbimiseks avatud: {0} {1} {0} {0} Tabelite kasutamiseks tiražeerimisvoos ei tohi semantiline kasutus (tabeli seadetes) olla seatud väärtusele {2}Analüütiline andmekogum{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Sihtobjekti ei saa kasutada, kuna see on tarbimiseks avatud. {0} {0} Tabeli kasutamiseks tiražeerimisvoos ei tohi semantiline kasutus (tabeli seadetes) olla seatud väärtusele {1}Analüütiline andmekogum{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Selle nimega sihtobjekt on juba olemas. Seda ei saa aga kasutada, kuna see on tarbimiseks avatud. {0} {0} Tabeli kasutamiseks tiražeerimisvoos ei tohi semantiline kasutus (tabeli seadetes) olla seatud väärtusele {1}Analüütiline andmekogum{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Sellise nimega objekt on sihtkohas juba olemas. {0}Seda objekti ei saa siiski kasutada kohalikku hoidlasse viiva tiražeerimisvoo sihtobjektina, kuna see pole kohalik tabel.
#XMSG:
targetAutoRenameUpdated=Sihtveerg on ümber nimetatud.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Sihtveerg on ümber nimetatud, et lubada Google BigQuerys alliktabelite tiražeerimist. Seda ühel järgmistest põhjustest.{0} {1}{2}Reserveeritud veerunimi{3}{2}Toetuseta märgid{3}{2}Reserveeritud eesliide{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Sihtveerg on ümber nimetatud, et lubada Confluentis alliktabelite tiražeerimist. Seda ühel järgmistest põhjustest.{0} {1}{2}Reserveeritud veerunimi{3}{2}Toetuseta märgid{3}{2}Reserveeritud eesliide{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Sihtveerg on ümber nimetatud, et lubada tiražeerimist sihtkohta. Seda ühel järgmistest põhjustest.{0} {1}{2}Toetuseta märgid{3}{2}Reserveeritud eesliide{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Sihtveerg on ümber nimetatud, et lubada tiražeerimist sihtkohta. Seda ühel järgmistest põhjustest.{0} {1}{2}Reserveeritud veerunimi{3}{2}Toetuseta märgid{3}{2}Reserveeritud eesliide{3}{4}
#XMSG:
targetAutoDataType=Sihtandmete tüüp on muudetud.
#XMSG:
targetAutoDataTypeDesc=Sihtandmete tüübiks on määratud {0}, kuna lähteandmete tüüp on Google BigQuerys toetuseta.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Sihtandmete tüübiks on määratud {0}, kuna lähteandmete tüüpi sihtühenduses ei toetata.
#XMSG
projectionGBQUnableToCreateKey=Primaarvõtmeid ei saa luua.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=Google BigQuery toetab maksimaalselt 16 primaarvõtit, aga lähteobjektis on suurem arv primaarvõtmeid. Sihtobjektis ei looda primaarvõtmeid.
#XMSG
HDLFNoKeyError=Määratlege primaarvõtmena vähemalt üks veerg.
#XMSG
HDLFNoKeyErrorDescription=Objekti tiražeerimiseks peate vähemalt ühe veeru määratlema primaarvõtmena.
#XMSG
HDLFNoKeyErrorExistingTarget=Määratlege primaarvõtmena vähemalt üks veerg.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Andmete tiražeerimiseks olemasolevasse sihtobjekti peab sellel olema üks või mitu veergu, mis on määratletud primaarvõtmena. {0} {0} Ühe või mitme veeru esmaseks võtmeks määramiseks on järgmised valikud. {0}{0}{1} Olemasoleva sihtobjekti muutmiseks kasutage kohalikku tabeliredaktorit. Seejärel laadige tiražeerimisvoog uuesti.{0}{0}{1}Nimetage tiražeerimisvoos sihtobjekt ümber. See loob uue objekti kohe, kui käitamine algab. Pärast ümbernimetamist saate projektsioonis primaarvõtmeks määrata ühe või mitu veergu.{0}{0}{1}Vastendage objekt mõne muu olemasoleva sihtobjektiga, milles üks või mitu veergu on juba primaarvõtmena määratletud.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Primaarvõti on muudetud.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=Võrreldes lähteobjektiga olete sihtobjekti primaarvõtmena määranud erinevad veerud. Veenduge, et need veerud tuvastaksid kõik read kordumatult, et vältida võimalikku andmete rikkumist andmete hilisemal tiražeerimisel. {0} {0} Lähteobjektis on primaarvõtmena määratletud järgmised veerud: {0} {1}
#XMSG
duplicateDPIDColumns=Nimetage sihtveerg ümber.
#XMSG
duplicateDPIDDColumnsDesc1=See sihtveeru nimi on reserveeritud tehnilise veeru jaoks. Projektsiooni salvestamiseks sisestage mõni muu nimi.
#XMSG:
targetAutoRenameDPID=Sihtveerg on ümber nimetatud.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Sihtveerg on ümber nimetatud, et lubada ilma võtmeteta ABAP-i allikast tiražeerimist. See on tingitud ühest järgmistest põhjustest.{0} {1}{2}Reserveeritud veerunimi{3}{2}Toetuseta märgid{3}{2}Reserveeritud eesliide{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle={0} sihtsätted
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle={0} lähtesätted
#XBUT
connectionSettingSave=Salvesta
#XBUT
connectionSettingCancel=Tühista
#XBUT: Button to keep the object level settings
txtKeep=Säilita
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Kirjuta üle
#XFLD
targetConnectionThreadlimit=Sihtlõimelimiit alglaadimise jaoks (1–100)
#XFLD
connectionThreadLimit=Lähtelõimelimiit alglaadimise jaoks (1–100)
#XFLD
maxConnection=Tiražeerimise lõimelimiit (1–100)
#XFLD
kafkaNumberOfPartitions=Partitsioonide arv
#XFLD
kafkaReplicationFactor=Tiražeerimistegur
#XFLD
kafkaMessageEncoder=Sõnumi kodeerija
#XFLD
kafkaMessageCompression=Sõnumi tihendus
#XFLD
fileGroupDeltaFilesBy=Delta gurupeerimialus
#XFLD
fileFormat=Failitüüp
#XFLD
csvEncoding=CSV-faili kodeering
#XFLD
abapExitLbl=ABAP-i utiliit
#XFLD
deltaPartition=Objektilõimede arv deltalaadimiste jaoks (1–10)
#XFLD
clamping_Data=Nurjumine andmete kärpimisel
#XFLD
fail_On_Incompatible=Nurjumine ühildumatute andmete tõttu
#XFLD
maxPartitionInput=Sektsioonide arvu ülempiir
#XFLD
max_Partition=Määratlege sektsioonide arvu ülempiir
#XFLD
include_SubFolder=Kaasa alamkaustad
#XFLD
fileGlobalPattern=Failinime globaalne muster
#XFLD
fileCompression=Faili tihendamine
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Faili eraldaja
#XFLD
fileIsHeaderIncluded=Failipäis
#XFLD
fileOrient=Orientiir
#XFLD
gbqWriteMode=Kirjutusrežiim
#XFLD
suppressDuplicate=Peida duplikaadid
#XFLD
apacheSpark=Luba Apache Sparki ühilduvus
#XFLD
clampingDatatypeCb=Kinnita ujukümnendpunktiga andmetüübid
#XFLD
overwriteDatasetSetting=Kirjuta sihtsätted üle objekti tasemel
#XFLD
overwriteSourceDatasetSetting=Kirjuta lähtesätted üle objekti tasemel
#XMSG
kafkaInvalidConnectionSetting=Sisestage arv vahemikus {0}-{1}.
#XMSG
MinReplicationThreadErrorMsg=Sisestage arv, mis on suurem kui {0}.
#XMSG
MaxReplicationThreadErrorMsg=Sisestage arv, mis on väiksem kui {0}.
#XMSG
DeltaThreadErrorMsg=Sisestage väärtus vahemikus 1–10.
#XMSG
MaxPartitionErrorMsg=Sisestage väärtus vahemikus 1 <= x <= 2147483647. Vaikeväärtus on 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Sisestage täisarv vahemikus {0}–{1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Kasuta maakleri tiražeerimistegurit
#XFLD
serializationFormat=Serialiseerimisvorming
#XFLD
compressionType=Tihenduse tüüp
#XFLD
schemaRegistry=Kasuta skeemiregistrit
#XFLD
subjectNameStrat=Subjekti nime strateegia
#XFLD
compatibilityType=Ühilduvuse tüüp
#XFLD
confluentTopicName=Teema nimi
#XFLD
confluentRecordName=Kirje nimi
#XFLD
confluentSubjectNamePreview=Subjekti nime eelvaade
#XMSG
serializationChangeToastMsgUpdated2=Serialiseerimisvorming on muutunud ja see on nüüd JSON, kuna skeemiregister pole lubatud. Serialiseerimisvormingu AVRO kasutamiseks peate esmalt lubama skeemiregistri.
#XBUT
confluentTopicNameInfo=Teema nimi põhineb alati sihtobjekti nimel. Selle muutmiseks saate sihtobjekti ümber nimetada.
#XMSG
emptyRecordNameValidationHeaderMsg=Sisestage kirje nimi.
#XMSG
emptyPartionHeader=Sisestage sektsioonide arv.
#XMSG
invalidPartitionsHeader=Sisestage sobiv sektsioonide arv.
#XMSG
invalidpartitionsDesc=Sisestage arv vahemikus 1–200 000.
#XMSG
emptyrFactorHeader=Sisestage tiražeerimistegur.
#XMSG
invalidrFactorHeader=Sisestage sobiv tiražeerimistegur.
#XMSG
invalidrFactorDesc=Sisestage arv vahemikus 1–32 767.
#XMSG
emptyRecordNameValidationDescMsg=Serialiseerimisvormingu „AVRO“ kasutamise korral on toetatud üksnes järgmised märgid:{0}{1} A–Z{0}{1} a–z{0}{1} 0–9{0}{1} _(allkriips)
#XMSG
validRecordNameValidationHeaderMsg=Sisestage sobiv kirje nimi.
#XMSG
validRecordNameValidationDescMsgUpdated=Kuna kasutusel on serialiseerimisvorming AVRO, tohib kirje nimi koosneda ainult tähtedest ja numbritest (A–Z, a–z, 0–9) ning allkriipsudest (_). Nimi peab algama tähe või allkriipsuga.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=Sätte „Objektilõimede arv deltalaadimiste jaoks“ saab määrata kohe, kui vähemalt ühe objekti laadimistüüp on „Algne ja delta“.
#XMSG
invalidTargetName=Sobimatu veerunimi
#XMSG
invalidTargetNameDesc=Sihtveeru nimi peab koosnema ainult tähtedest ja numbritest (A–Z, a–z, 0–9) ning allkriipsudest (_).
#XFLD
consumeOtherSchema=Kasuta muid skeemiversioone
#XFLD
ignoreSchemamissmatch=Ignoreeri skeemide vastuolu
#XFLD
confleuntDatatruncation=Nurjumine andmete kärpimisel
#XFLD
isolationLevel=Isoleerimistase
#XFLD
confluentOffset=Alguspunkt
#XFLD
signavioGroupDeltaFilesByText=Pole
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=Ei
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=Ei

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Projektsioonid
#XBUT
txtAdd=Lisa
#XBUT
txtEdit=Redigeeri
#XMSG
transformationText=Lisa projektsioon filtri seadistamiseks või vastendamiseks.
#XMSG
primaryKeyRequiredText=Valige skeemi konfigureerimisega primaarvõti.
#XFLD
lblSettings=Sätted
#XFLD
lblTargetSetting={0}: sihtsätted
#XMSG
@csvRF=Valige fail, mis sisaldab skeemimääratlust, mille soovite rakendada kõigile failidele kaustas.
#XFLD
lblSourceColumns=Lähteveerud
#XFLD
lblJsonStructure=JSON-i struktuur
#XFLD
lblSourceSetting={0}: lähtesätted
#XFLD
lblSourceSchemaSetting={0}: lähteskeemi sätted
#XBUT
messageSettings=Sõnumisätted
#XFLD
lblPropertyTitle1=Objekti atribuudid
#XFLD
lblRFPropertyTitle=Tiražeerimisvoo omadused
#XMSG
noDataTxt=Kuvatavaid veerge pole.
#XMSG
noTargetObjectText=Sihtobjekti pole valitud.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Sihtveerud
#XMSG
searchColumns=Otsi veerge
#XTOL
cdcColumnTooltip=Deltahõive veerg
#XMSG
sourceNonDeltaSupportErrorUpdated=Lähteobjekt ei toeta deltahõivet.
#XMSG
targetCDCColumnAdded=2 sihtveergu on deltahõive jaoks lisatud.
#XMSG
deltaPartitionEnable=Objektilõimede limiit deltalaadimiste jaoks on allika sätetesse lisatud.
#XMSG
attributeMappingRemovalTxt=Sobimatud vastendused, mida uue sihtobjekti jaoks ei toetata, eemaldatakse.
#XMSG
targetCDCColumnRemoved=Deltahõiveks kasutatud 2 sihtveergu on eemaldatud.
#XMSG
replicationLoadTypeChanged=Uueks laadimistüübiks määrati „Algne ja delta“.
#XMSG
sourceHDLFLoadTypeError=Määrake uueks laadimistüübiks „Algne ja delta“.
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Objekti tiražeerimiseks lähteühendusest, mille ühenduse tüüp on „SAP HANA Cloud, andmehoidla failid“, SAP Datasphere’i peate kasutama laadimistüüpi „Algne ja delta“.
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Lubage deltahõive.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Objekti tiražeerimiseks lähteühendusest, mille ühenduse tüüp on „SAP HANA Cloud, andmehoidla failid“, SAP Datasphere’i peate lubama deltahõive.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Muutke sihtobjekti.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Sihtobjekti ei saa kasutada, kuna deltahõive on keelatud. Saate sihtobjekti ümber nimetada (see võimaldab luua uue deltahõivega objekti) või vastendada selle mõne olemasoleva objektiga, millel on deltahõive lubatud.
#XMSG
deltaPartitionError=Sisestage deltalaadimiste jaoks sobiv objektilõimede arv.
#XMSG
deltaPartitionErrorDescription=Sisestage väärtus vahemikus 1–10.
#XMSG
deltaPartitionEmptyError=Sisestage deltalaadimiste jaoks objektilõimede arv.
#XFLD
@lblColumnDescription=Kirjeldus
#XMSG
@lblColumnDescriptionText1=Tehnilise otstarbega – selliste duplikaatkirjete käitlemine, mille on põhjustanud primaarvõtmeta ABAP-i põhiste lähteobjektide tiražeerimisel tekkinud probleemid.
#XFLD
storageType=Salvestusruum
#XFLD
skipUnmappedColLbl=Jäta vastendamata veerud vahele
#XFLD
abapContentTypeLbl=Sisutüüp
#XFLD
autoMergeForTargetLbl=Ühenda andmed automaatselt
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Üldist
#XFLD
lblBusinessName=Ärinimi
#XFLD
lblTechnicalName=Tehniline nimi
#XFLD
lblPackage=Pakett
#XFLD
statusPanel=Käituse olek
#XBTN: Schedule dropdown menu
SCHEDULE=Graafik
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Redigeeri graafikut
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Kustuta graafik
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Loo graafik
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Graafiku valideerimise kontroll nurjus
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Graafikut ei saa luua, kuna tiražeerimisvoo juurutamine on praegu käimas.{0}Palun oodake, kuni tiražeerimisvoog on juurutatud.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Selliste tiražeerimisvoogude jaoks, mis sisaldavad objekte laadimistüübiga „Algne ja delta“, ei saa ajakava luua.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Selliste tiražeerimisvoogude jaoks, mis sisaldavad objekte laadimistüübiga „Algne ja delta / Ainult delta“, ei saa ajakava luua.
#XFLD : Scheduled popover
SCHEDULED=Ajastatud
#XFLD
CREATE_REPLICATION_TEXT=Loo tiražeerimisvoog
#XFLD
EDIT_REPLICATION_TEXT=Redigeeri tiražeerimisvoogu
#XFLD
DELETE_REPLICATION_TEXT=Kustuta tiražeerimisvoog
#XFLD
REFRESH_FREQUENCY=Sagedus
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Tiražeerimisvoogu ei saa juurutada, kuna olemasolev ajakava{0} ei toeta veel laadimistüüpi „Algne ja delta“.{0}{0}Tiražeerimisvoo juurutamiseks peate kõigi objektide laadimistüübiks määrama{0} „Ainult algne“. Teise võimalusena võite ajakava kustutada, juurutada {0}tiražeerimisvoo ja seejärel käivitada uue käituse. See käitus töötaks seejärel pidevalt {0}ja toetaks ka objekte, mille laadimistüüp on „Algne ja delta“.
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Tiražeerimisvoogu ei saa juurutada, kuna olemasolev ajakava{0} ei toeta veel laadimistüüpi „Algne ja delta / Ainult delta“.{0}{0}Tiražeerimisvoo juurutamiseks peate kõigi objektide laadimistüübiks määrama{0} „Ainult algne“. Teise võimalusena võite ajakava kustutada, juurutada {0}tiražeerimisvoo ja seejärel käivitada uue käituse. See käitus töötaks seejärel pidevalt {0}ja toetaks ka objekte, mille laadimistüüp on „Algne ja delta / Ainult delta“.
#XMSG
SCHEDULE_EXCEPTION=Ajakava üksikasju ei saanud tuua
#XFLD: Label for frequency column
everyLabel=Iga
#XFLD: Plural Recurrence text for Hour
hoursLabel=tunni järel
#XFLD: Plural Recurrence text for Day
daysLabel=päeva järel
#XFLD: Plural Recurrence text for Month
monthsLabel=kuu järel
#XFLD: Plural Recurrence text for Minutes
minutesLabel=minuti järel
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Ajastamisvõimaluse kohta ei saanud teavet tuua.
#XFLD :Paused field
PAUSED=Peatatud
#XMSG
navToMonitoring=Ava voogude seiretööriistas
#XFLD
statusLbl=Olek
#XFLD
lblLastRunExecuted=Viimase käituse algus
#XFLD
lblLastExecuted=Viimati käivitatud
#XFLD: Status text for Completed
statusCompleted=Lõpetatud
#XFLD: Status text for Running
statusRunning=Käitamine
#XFLD: Status text for Failed
statusFailed=Nurjunud
#XFLD: Status text for Stopped
statusStopped=Peatatud
#XFLD: Status text for Stopping
statusStopping=Peatamine
#XFLD: Status text for Active
statusActive=Aktiivne
#XFLD: Status text for Paused
statusPaused=Peatatud
#XFLD: Status text for not executed
lblNotExecuted=Pole veel käivitatud
#XFLD
messagesSettings=Sõnumite sätted
#XTOL
@validateModel=Valideerimisteated
#XTOL
@hierarchy=Hierarhia
#XTOL
@columnCount=Veergude arv
#XMSG
VAL_PACKAGE_CHANGED=Olete selle objekti määranud paketile „{1}“. Selle muudatuse kinnitamiseks ja valideerimiseks klõpsake nuppu „Salvesta“. Võtke arvesse, et pärast salvestamist ei saa paketile määramist selles redaktoris tagasi võtta.
#XMSG
MISSING_DEPENDENCY=Objekti „{0}“ sõltuvusseoseid ei saa paketis „{1}“ lahendada.
#XFLD
deltaLoadInterval=Deltalaadimise intervall
#XFLD
lblHour=Tunnid (0–24)
#XFLD
lblMinutes=Minutid (0–59)
#XMSG
maxHourOrMinErr=Sisestage väärtus vahemikus 0–{0}
#XMSG
maxDeltaInterval=Deltalaadimise intervalli maksimumväärtus on 24 tundi.{0}Muutke vastavalt minutite või tundide väärtust.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Sihtkonteineri tee
#XFLD
confluentSubjectName=Subjekti nimi
#XFLD
confluentSchemaVersion=Skeemi versioon
#XFLD
confluentIncludeTechKeyUpdated=Kaasa tehniline võti
#XFLD
confluentOmitNonExpandedArrays=Jäta laiendamata massiivid välja
#XFLD
confluentExpandArrayOrMap=Laienda massiivi või vastendust
#XCOL
confluentOperationMapping=Toimingute vastendamine
#XCOL
confluentOpCode=Toimingukood
#XFLD
confluentInsertOpCode=Lisa
#XFLD
confluentUpdateOpCode=Värskenda
#XFLD
confluentDeleteOpCode=Kustuta
#XFLD
expandArrayOrMapNotSelectedTxt=Pole valitud
#XFLD
confluentSwitchTxtYes=Jah
#XFLD
confluentSwitchTxtNo=Ei
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Tõrge
#XTIT
executeWarning=Hoiatus
#XMSG
executeunsavederror=Salvestage oma tiražeerimisvoog enne selle käivitamist.
#XMSG
executemodifiederror=Tiražeerimisvoos on salvestamata muudatusi. Salvestage tiražeerimisvoog.
#XMSG
executeundeployederror=Peate juurutama tiražeerimisvoo enne selle käitamist.
#XMSG
executedeployingerror=Oodake, kuni juurutamine jõuab lõpule.
#XMSG
msgRunStarted=Käitus on käivitatud
#XMSG
msgExecuteFail=Tiražeerimisvoo käivitamine nurjus.
#XMSG
titleExecuteBusy=Palun oodake.
#XMSG
msgExecuteBusy=Valmistame ette teie andmeid tiražeerimisvoo käitamiseks.
#XTIT
executeConfirmDialog=Hoiatus
#XMSG
msgExecuteWithValidations=Tiražeerimisvoos on valideerimistõrkeid. Tiražeerimisvoo käivitamine võib põhjustada tõrke.
#XMSG
msgRunDeployedVersion=Leidub juurutatavaid muudatusi. Käivitatakse tiražeerimisvoo viimane juurutatud versioon. Kas soovite jätkata?
#XBUT
btnExecuteAnyway=Käivita ikka
#XBUT
btnExecuteClose=Sule
#XBUT
loaderClose=Sule
#XTIT
loaderTitle=Laadimine
#XMSG
loaderText=Üksikasjade toomine serverist
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Alliktabelite replikeerimise voogu sellele mitte-SAPi sihtühendusele ei saa algatada
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=sest selle kuu jaoks ei ole saadaval väljaminevat mahtu.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Administraator saab suurendada selle rentniku Premium Outboundi
#XMSG
premiumOutBoundRFAdminErrMsgPart2=plokke, muutes väljamineva mahu selle kuu jaoks saadavaks.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Tõrge
#XTIT
deployInfo=Teave
#XMSG
deployCheckFailException=Juurutamisel ilmnes tõrge
#XMSG
deployGBQFFDisabled=Alliktabelite replikeerimise voo juurutamine sihtühendusega Google BigQuerysse ei ole hetkel võimalik, sest me teostame sellel funktsioonil hooldust.
#XMSG
deployKAFKAFFDisabled=Alliktabelite replikeerimise voo juurutamine sihtühendusega Apache Kafkasse ei ole hetkel võimalik, sest me teostame sellel funktsioonil hooldust.
#XMSG
deployConfluentDisabled=Selliste lähtetabelite tiražeerimisvoogude juurutamine, mille sihtühendus on Confluent Kafkaga, pole praegu võimalik, sest käimas on selle funktsiooni hooldustööd.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Järgmiste sihtobjektide puhul kasutavad deltahõive tabelinimesid juba teised hoidlas olevad tabelid: {0}. Enne kui saate alliktabelite replikeerimise voo juurutada, tuleb teil need sihtobjektid ümber nimetada tagamaks, et seotud deltahõive tabelinimed oleksid kordumatud.
#XMSG
deployDWCSourceFFDisabled=Alliktabelite replikeerimise voogusid, millel on allikaks SAP Datasphere, ei ole hetkel võimalik juurutada, sest me teostame sellel funktsioonil hooldust.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Selliste tiražeerimisvoogude juurutamine, mis sisaldavad lähteobjektidena deltaloaga kohalikke tabeleid, pole praegu võimalik, sest käimas on selle funktsiooni hooldus.
#XMSG
deployHDLFSourceFFDisabled=Selliste tiražeerimisvoogude juurutamine, mille lähteühenduste tüüp on „SAP HANA Cloud, andmehoidla failid“, pole praegu võimalik, sest käimas on selle funktsiooni hooldus.
#XMSG
deployObjectStoreAsSourceFFDisabled=Praegu ei saa juurutada tiražeerimisvooge, mille allikaks on pilvsalvestusruumi pakkuja.
#XMSG
deployConfluentSourceFFDisabled=Tiražeerimisvooge, mille allikas on Confluent Kafka, ei saa praegu juurutada, kuna käimas on selle funktsiooni hooldus.
#XMSG
deployMaxDWCNewTableCrossed=Suuri tiražeerimisvooge ei saa korraga salvestada ja juurutada. Esmalt salvestage tiražeerimisvoog ja seejärel juurutage see.
#XMSG
deployInProgressInfo=Juurutamine on juba käimas.
#XMSG
deploySourceObjectInUse=Lähteobjektid {0} on juba kasutusel tiražeerimisvoogudes {1}.
#XMSG
deployTargetSourceObjectInUse=Lähteobjektid {0} on juba kasutusel tiražeerimisvoogudes {1}. Sihtobjektid {2} on juba kasutusel tiražeerimisvoogudes {3}.
#XMSG
deployReplicationFlowCheckError=Tõrge tiražeerimisvoo kontrollimisel: {0}
#XMSG
preDeployTargetObjectInUse=Sihtobjekte {0} kasutatakse juba tiražeerimisvoogudes {1} ja sama sihtobjekti ei saa korraga kahes erinevas tiražeerimisvoos olla. Valige mõni muu sihtobjekt ja proovige uuesti.
#XMSG
runInProgressInfo=Tiražeerimisvoog juba töötab.
#XMSG
deploySignavioTargetFFDisabled=Tiražeerimisvoogusid, mille sihtkohaks on SAP Signavio, ei saa praegu juurutada, sest käimas on funktsiooni hooldustööd.
#XMSG
deployHanaViewAsSourceFFDisabled=Valitud lähteühenduse lähteobjektidena vaateid sisaldavate tiražeerimisvoogude juurutamine ei ole praegu võimalik. Proovige hiljem uuesti.
#XMSG
deployMsOneLakeTargetFFDisabled=Tiražeerimisvoogusid, mille sihtkohaks on MS OneLake, ei saa praegu juurutada, sest käimas on funktsiooni hooldustööd.
#XMSG
deploySFTPTargetFFDisabled=Tiražeerimisvoogusid, mille sihtkohaks on SFTP, ei saa praegu juurutada, sest käimas on funktsiooni hooldustööd.
#XMSG
deploySFTPSourceFFDisabled=Tiražeerimisvoogusid, mille lähtekohaks on SFTP, ei saa praegu juurutada, sest käimas on funktsiooni hooldustööd.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Tehniline nimi
#XFLD
businessNameInRenameTarget=Ärinimi
#XTOL
renametargetDialogTitle=Nimeta sihtobjekt ümber
#XBUT
targetRenameButton=Nimeta ümber
#XBUT
targetRenameCancel=Tühista
#XMSG
mandatoryTargetName=Peate sisestama nime.
#XMSG
dwcSpecialChar=_(allkriips) on ainus lubatud märk.
#XMSG
dwcWithDot=Sihttabeli nimi tohib koosneda ladina tähestiku tähtedest, numbritest, allkriipsudest (_) ja punktidest (.). Esimene märk peab olema täht, number või allkriips (mitte punkt).
#XMSG
nonDwcSpecialChar=Lubatud erimärgid on _(allkriips) -(sidekriips) .(punkt)
#XMSG
firstUnderscorePattern=Nimi ei tohi alata märgiga _(allkriips)

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: kuva SQL-i tabeli loomise lause
#XMSG
sqlDialogMaxPKWarning=Google BigQuerys on toetatud kuni 16 primaarvõtit ja lähteobjektis on neid rohkem. Seetõttu ei määratleta selles lauses ühtki primaarvõtit.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Ühe või mitme lähteveeru andmetüüpi ei saa Google BigQuerys määratleda primaarvõtmeks. Seetõttu ei määratleta sellel juhul ühtki primaarvõtit. Google BigQuerys tohib primaarvõti olla ainult järgmistel andmetüüpidel: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Kopeeri ja sule
#XBUT
closeDDL=Sule
#XMSG
copiedToClipboard=Kopeeritud lõikelauale


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Objekt „{0}“ ei saa olla osa tegumiahelast, kuna sellel pole lõppu (see hõlmab objekte, mille laadimistüüp on „Algne ja delta / Ainult delta“).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Objekt „{0}“ ei saa olla osa tegumiahelast, kuna sellel pole lõppu (see hõlmab objekte, mille laadimistüüp on „Algne ja delta“).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objekti „{0}“ ei saa tegumiahelasse lisada.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Leidub salvestamata sihtobjekte. Salvestage uuesti.{0}{0} Selle funktsiooni käitumine on muutunud: varem loodi sihtobjektid sihtkeskkonnas üksnes tiražeerimisvoo juurutamisel.{0} Nüüd luuakse objektid juba tiražeerimisvoo salvestamisel. Teie tiražeerimisvoog loodi enne seda muutust ja sisaldab uusi objekte.{0} Peate tiražeerimisvoo enne selle juurutamist uuesti salvestama, et uued objektid kaasataks õigesti.
#XMSG
confirmChangeContentTypeMessage=Olete sisutüüpi muutmas. See toob kaasa kõigi olemasolevate projektsioonide kustutamise.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Subjekti nimi
#XFLD
schemaDialogVersionName=Skeemi versioon
#XFLD
includeTechKey=Kaasa tehniline võti
#XFLD
segementButtonFlat=Lame
#XFLD
segementButtonNested=Pesastatud
#XMSG
subjectNamePlaceholder=Otsige subjekti nime

#XMSG
@EmailNotificationSuccess=Käitusaja meiliteatiste konfiguratsioon on salvestatud.

#XFLD
@RuntimeEmailNotification=Käitusaja meiliteatis

#XBTN
@TXT_SAVE=Salvesta


