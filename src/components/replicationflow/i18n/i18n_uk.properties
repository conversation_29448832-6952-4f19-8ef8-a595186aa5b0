#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Потік реплікації

#XFLD: Edit Schema button text
editSchema=Редагувати схему

#XTIT : Properties heading
configSchema=Конфігурація схеми

#XFLD: save changed button text
applyChanges=Застосувати зміни


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Виберіть з’єднання з джерелом
#XFLD
sourceContainernEmptyText=Виберіть контейнер
#XFLD
targetConnectionEmptyText=Виберіть цільове з’єднання
#XFLD
targetContainernEmptyText=Виберіть контейнер
#XFLD
sourceSelectObjectText=Виберіть вихідний об'єкт
#XFLD
sourceObjectCount=Вихідні об''єкти ({0})
#XFLD
targetObjectText=Цільові об'єкти
#XFLD
confluentBrowseContext=Вибрати контекст
#XBUT
@retry=Повторити спробу
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Триває апгрейд орендатора.

#XTOL
browseSourceConnection=Переглянути з’єднання з джерелом
#XTOL
browseTargetConnection=Переглянути цільове з’єднання
#XTOL
browseSourceContainer=Переглянути вихідний контейнер
#XTOL
browseAndAddSourceDataset=Додати вихідні об’єкти
#XTOL
browseTargetContainer=Переглянути цільовий контейнер
#XTOL
browseTargetSetting=Проглянути настройки цілі
#XTOL
browseSourceSetting=Проглянути настройки джерела
#XTOL
sourceDatasetInfo=Інформація
#XTOL
sourceDatasetRemove=Вилучити
#XTOL
mappingCount=Це загальна кількість зіставлень/виразів, не заснованих на іменах.
#XTOL
filterCount=Це загальна кількість умов фільтра.
#XTOL
loading=Завантаження…
#XCOL
deltaCapture=Дельта-захоплення
#XCOL
deltaCaptureTableName=Таблиця дельта-захоплення
#XCOL
loadType=Завантажити тип
#XCOL
deleteAllBeforeLoading=Видалити все перед завантаженням
#XCOL
transformationsTab=Екстраполяція
#XCOL
settingsTab=Настройки

#XBUT
renameTargetObjectBtn=Перейменувати цільовий об'єкт
#XBUT
mapToExistingTargetObjectBtn=Зіставити з існуючим цільовим об'єктом
#XBUT
changeContainerPathBtn=Змінити шлях до контейнера
#XBUT
viewSQLDDLUpdated=Переглянути інструкцію створення таблиці SQL
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Вихідний об'єкт не підтримує дельта-захоплення, але для вибраного цільового об'єкта опцію дельта-захоплення ввімкнуто.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Цей цільовий об''єкт використати не можна, оскільки активовано дельта-захоплення,{0}а вихідний об''єкт не підтримує дельта-захоплення.{1}Можна вибрати інший цільовий об’єкт, який не підтримує дельта-захоплення.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Цільовий об''єкт із таким іменем уже існує. Утім, його не можна використати,{0}оскільки активовано дельта-захоплення, а вихідний об''єкт не{0}підтримує дельта-захоплення.{1}Можна ввести ім''я наявного цільового об''єкта, який не{0}підтримує дельта-захоплення, або ввести таке ім''я, якого ще не існує.
#XBUT
copySQLDDLUpdated=Копіювати інструкцію створення таблиці SQL
#XMSG
targetObjExistingNoCDCColumnUpdated=Наявні таблиці в Google BigQuery мають містити такі стовпчики для захоплення змін у даних (CDC):{0}{0}{1}• operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Наведені нижче вихідні об'єкти не підтримуються, оскільки вони не мають первинного ключа або використовують з'єднання, що не відповідає умовам для отримання первинного ключа:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Можливе рішення див. в статті SAP KBA 3531135.
#XLST: load type list values
initial=Тільки початкове
@emailUpdateError=Помилка в оновленні списку сповіщень електронною поштою

#XLST
initialDelta=Початкове і дельта

#XLST
deltaOnly=Лише дельта
#XMSG
confluentDeltaLoadTypeInfo=Для джерела Confluent Kafka підтримується тільки тип завантаження "Початкове і дельта".
#XMSG
confirmRemoveReplicationObject=Справді видалити цю реплікацію?
#XMSG
confirmRemoveReplicationTaskPrompt=Ця дія видалить наявні копії. Продовжити?
#XMSG
confirmTargetConnectionChangePrompt=Ця дія призведе до скидання цільового з’єднання, цільового контейнера та видалення всіх цільових об’єктів. Продовжити?
#XMSG
confirmTargetContainerChangePrompt=Ця дія призведе до скидання цільового контейнера та видалення всіх наявних цільових об’єктів. Продовжити?
#XMSG
confirmRemoveTransformObject=Справді видалити {0} екстраполяції?
#XMSG
ErrorMsgContainerChange=Під час зміни шляху контейнера сталася помилка.
#XMSG
infoForUnsupportedDatasetNoKeys=Ці вихідні об'єкти не підтримуються, оскільки вони не мають первинного ключа:
#XMSG
infoForUnsupportedDatasetView=Ці вихідні об'єкти типу "Подання" не підтримуються:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Зазначений далі вихідний об'єкт не підтримується, оскільки це подання SQL, що містить параметри введення:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Ці вихідні об'єкти не підтримуються, оскільки для них вимкнуто екстракцію:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Для з'єднань Confluent дозволено тільки формати серіалізації AVRO і JSON. Наведені нижче об'єкти не підтримуються, оскільки вони використовують інші формати серіалізації:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Не вдалося викликати схему для зазначених нижче об'єктів. Виберіть відповідний контекст або перевірте конфігурацію реєстру схем.
#XTOL: warning dialog header on deleting replication task
deleteHeader=Видалити
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Настройка "Видалити все перед завантаженням" для Google BigQuery не підтримується.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Настройка "Видалити все перед завантаженням" забезпечує видалення і повторне створення об'єкта (теми) перед кожною реплікацією. Її активація також призводить до видалення всіх присвоєних повідомлень.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Настройка "Видалити все перед завантаженням" для такого цільового типу не підтримується.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Технічне ім’я
#XCOL
connBusinessName=Бізнес-ім'я
#XCOL
connDescriptionName=Опис
#XCOL
connType=Тип
#XMSG
connTblNoDataFoundtxt=Не знайдено з’єднань
#XMSG
connectionError=Під час отримання з’єднань сталася помилка.
#XMSG
connectionCombinationUnsupportedErrorTitle=Комбінація з'єднань не підтримується
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Реплікація з {0} до {1}наразі не підтримується.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Комбінація типів з'єднання не підтримується
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Реплікація зі з''єднання з типом з''єднання "SAP HANA Cloud, файли озера даних" до {0} не підтримується. Реплікація можлива тільки до SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Виділити
#XBUT
containerCancelBtn=Скасувати
#XTOL
containerSelectTooltip=Виділити
#XTOL
containerCancelTooltip=Скасувати
#XMSG
containerContainerPathPlcHold=Шлях до контейнера
#XFLD
containerContainertxt=Контейнер
#XFLD
confluentContainerContainertxt=Контекст
#XMSG
infoMessageForSLTSelection=Використовувати як контейнер можна лише ідентифікатор масової передачі /SLT/. Виберіть ідентифікатор масової передачі в розділі SLT (якщо доступно) і натисніть «Надіслати».
#XMSG
msgFetchContainerFail=Під час отримання даних контейнера сталася помилка.
#XMSG
infoMessageForSLTHidden=Це з'єднання не підтримує папки SLT, тому вони не відображаються в списку нижче.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Виберіть контейнер, що містить підпапки.
#XMSG
sftpIncludeSubFolderText=False
#XMSG
sftpIncludeSubFolderTextNew=Ні

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Ще немає зіставлення фільтрів)
#XMSG
failToFetchRemoteMetadata=Під час отримання метаданих сталася помилка.
#XMSG
failToFetchData=Під час отримання наявної цілі сталася помилка.
#XCOL
@loadType=Тип завантаження
#XCOL
@deleteAllBeforeLoading=Видалити все перед завантаженням

#XMSG
@loading=Завантаження…
#XFLD
@selectSourceObjects=Вибрати вихідні об’єкти
#XMSG
@exceedLimit=Не можна імпортувати більше такої кількості об''єктів одночасно: {0}. Скасуйте вибір принаймні такої кількості об''єктів: {1}.
#XFLD
@objects=Об’єкти
#XBUT
@ok=OK
#XBUT
@cancel=Скасувати
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Далі
#XBUT
btnAddSelection=Додати виділення
#XTOL
@remoteFromSelection=Видалити з вибраних
#XMSG
@searchInForSearchField=Шукати в {0}

#XCOL
@name=Технічне ім’я
#XCOL
@type=Тип
#XCOL
@location=Місцезнаходження
#XCOL
@label=Бізнес-ім'я
#XCOL
@status=Статус

#XFLD
@searchIn=Шукати в:
#XBUT
@available=Доступно
#XBUT
@selection=Вибір

#XFLD
@noSourceSubFolder=Таблиці та подання
#XMSG
@alreadyAdded=Уже є на схемі
#XMSG
@askForFilter=Елементів більше, ніж {0}. Введіть рядок фільтра, щоб скоротити кількість елементів.
#XFLD: success label
lblSuccess=Успішно
#XFLD: ready label
lblReady=Готовий
#XFLD: failure label
lblFailed=Помилка
#XFLD: fetching status label
lblFetchingDetail=Докладна інформація про отримання

#XMSG Place holder text for tree filter control
filterPlaceHolder=Введіть текст, щоб фільтрувати об’єкти верхнього рівня
#XMSG Place holder text for server search control
serverSearchPlaceholder=Введіть текст і натисніть клавішу ENTER, щоб почати пошук
#XMSG
@deployObjects=Імпортуються об’єкти ({0})…
#XMSG
@deployObjectsStatus=Кількість імпортованих об’єктів: {0}. Кількість об’єктів, які не вдалося імпортувати: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Не вдалося відкрити браузер локального репозиторію.
#XMSG
@openRemoteSourceBrowserError=Не вдалося отримати вихідні об’єкти.
#XMSG
@openRemoteTargetBrowserError=Не вдалося отримати цільові об’єкти.
#XMSG
@validatingTargetsError=Під час перевірки цілей сталася помилка.
#XMSG
@waitingToImport=Готово до імпорту

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Перевищено максимальну кількість об’єктів. Вибирайте щонайбільше 500 об’єктів для одного потоку реплікації.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Технічне ім'я
#XFLD
sourceObjectBusinessName=Бізнес-ім'я
#XFLD
sourceNoColumns=Кількість стовпчиків
#XFLD
containerLbl=Контейнер

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Необхідно вибрати з’єднання з джерелом для потоку реплікації.
#XMSG
validationSourceContainerNonExist=Необхідно вибрати контейнер для з’єднання з джерелом.
#XMSG
validationTargetNonExist=Необхідно вибрати цільове з’єднання для потоку реплікації.
#XMSG
validationTargetContainerNonExist=Необхідно вибрати контейнер для цільового з’єднання.
#XMSG
validationTruncateDisabledForObjectTitle=Реплікація до сховищ об'єктів.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Реплікація в хмарне сховище можлива, тільки якщо задано опцію "Видалити все перед завантаженням" або цільового об''єкта немає в цільовій системі.{0}{0} Щоб усе одно активувати реплікацію для об''єктів, для яких не задано опцію "Видалити все перед завантаженням", перш ніж запускати потік реплікації, переконайтеся, що цільовий об''єкт відсутній у системі.
#XMSG
validationTaskNonExist=Ви повинні мати принаймні одну реплікацію в потоці реплікації.
#XMSG
validationTaskTargetMissing=Ви повинні мати ціль для реплікації з джерелом: {0}
#XMSG
validationTaskTargetIsSAC=Вибраний цільовий об''єкт є артефактом SAC: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Вибраний цільовий об''єкт не є підтримуваною локальною таблицею: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Об'єкт із таким ім'ям уже існує в цільовій системі. Утім, цей об'єкт не можна використати як цільовий для потоку реплікації до локального репозиторію, оскільки він не є локальною таблицею.
#XMSG
validateSourceTargetSystemDifference=Для потоку реплікації потрібно вибрати різні комбінації цільового з’єднання та з’єднання з джерелом і контейнерів.
#XMSG
validateDuplicateSources=одна або кілька реплікацій мають повторювані імена вихідних об’єктів: {0}.
#XMSG
validateDuplicateTargets=одна або кілька реплікацій мають повторювані імена цільових об’єктів: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=Вихідний об''єкт "{0}" не підтримує дельта-захоплення, але цільовий об''єкт "{1}" його підтримує. Слід вилучити реплікацію.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Для реплікації з іменем цільового об''єкта "{0}" слід вибрати тип завантаження "Початкове і дельта".
#XMSG
validationAutoRenameTarget=Цільові стовпчики перейменовано.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Оскільки додано автоматичне проєктування, ці цільові стовпчики перейменовано, щоб забезпечити можливість реплікації до цільової системи:{1}{1} {0} {1}{1}Це сталося з однієї з таких причин:{1}{1}{2} використовуються непідтримувані символи;{1}{2} префікс зарезервовано.
#XMSG
validationAutoRenameTargetDescriptionUpdated=Оскільки додано автоматичне прогнозування, ці цільові стовпчики перейменовано, щоб забезпечити можливість реплікації до Google BigQuery:{1}{1} {0} {1}{1}Це сталося з однієї з таких причин:{1}{1}{2} ім''я стовпчика зарезервоване;{1}{2} використовуються непідтримувані символи;{1}{2} префікс зарезервовано.
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Оскільки додано автоматичне прогнозування, ці цільові стовпчики перейменовано, щоб забезпечити можливість реплікації до Confluent:{1}{1} {0} {1}{1}Це сталося з однієї з таких причин:{1}{1}{2} ім''я стовпчика зарезервоване;{1}{2} використовуються непідтримувані символи;{1}{2} префікс зарезервовано.
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Оскільки додано автоматичне прогнозування, ці цільові стовпчики перейменовано, щоб забезпечити можливість реплікації до цільової системи:{1}{1} {0} {1}{1}Це сталося з однієї з таких причин:{1}{1}{2} ім''я стовпчика зарезервоване;{1}{2} використовуються непідтримувані символи;{1}{2} префікс зарезервовано.
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Цільовий об'єкт перейменовано.
#XMSG
autoRenameInfoDesc=Цільовий об''єкт перейменовано, оскільки він містить непідтримувані символи. Підтримуються тільки такі символи :{0}{0}{1}A–Z{0}{1}a–z{0}{1}0–9{0}{1}.(крапка){0}{1}_(підкреслення){0}{1}-(дефіс)
#XMSG
validationAutoTargetTypeConversion=Змінено типи цільових даних.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Для цих цільових стовпчиків змінено типи цільових даних, оскільки типи вихідних даних не підтримуються в Google BigQuery:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Для цих цільових стовпчиків змінено типи цільових даних, оскільки типи вихідних даних не підтримуються в цільовому з''єднанні:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Скорочено імена цільових стовпчиків.
#XMSG
validationMaxCharLengthGBQTargetDescription=У Google BigQuery в іменах стовпчиків можна використовувати щонайбільше 300 символів. Скористайтеся прогнозуванням, щоб скоротити імена цих цільових стовпчиків:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Первинні ключі не буде створено.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=У Google BigQuery підтримується щонайбільше 16 первинних ключів, але вихідний об'єкт містить більшу кількість первинних ключів. У цільовому об'єкті не буде створено жоден із первинних ключів.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Один або кілька вихідних стовпчиків мають типи даних, які не визначено як первинні ключі в Google BigQuery. У цільовому об''єкті не буде створено жодного з первинних ключів.{0}{0}Наведені далі цільові типи даних сумісні з типами даних Google BigQuery, для яких можна визначити первинний ключ: {0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Визначте один або кілька стовпчиків як первинний ключ.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Слід визначити один або кілька стовпчиків як первинний ключ. Щоб це зробити, скористайтеся діалоговим вікном вихідної схеми.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Визначте один або кілька стовпчиків як первинний ключ.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Слід визначити один або кілька стовпчиків як первинний ключ, що відповідає обмеженням для первинного ключа вихідного об'єкта. Щоб це зробити, перейдіть у розділ "Конфігурація схеми" у властивостях вихідного об'єкта.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Введіть дійсну максимальну кількість розділів.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Максимальна кількість розділів має бути ≥ 1 і ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Визначте один або кілька стовпчиків як первинний ключ.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Щоб реплікувати об'єкт, потрібно визначити один або кілька цільових стовпчиків як первинний ключ. Використайте для цього проекцію.
#XMSG
validateHDLFNoPKExistingDatasetError=Визначте один або кілька стовпчиків як первинний ключ.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Щоб реплікувати дані до наявного цільового об''єкта, у нього має бути один або кілька цільових стовпчиків, визначених як первинний ключ. {0} Визначити один або кілька стовпчиків як первинний ключ можна кількома способами: {0}{1} Скористайтеся локальним редактором таблиць, щоб змінити наявний цільовий об''єкт. Потім перезавантажте потік реплікації.{0}{1}Перейменуйте цільовий об''єкт у потоці реплікації. У наслідок цього після початку прогону буде створено новий об''єкт. Після перейменування можна визначити один або кілька стовпчиків як первинний ключ у проєкції.{0}{1} Зіставте шляхом меппінга об''єкт з іншим наявним цільовим об''єктом, у якого один або кілька стовпчиків уже визначено як первинний ключ.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Вибраний цільовий об''єкт уже існує в репозиторії: {0}
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Імена таблиць дельта-захоплення уже використовуються для інших таблиць у репозиторії: {0} Щоб мати змогу зберегти потік реплікації, потрібно перейменувати ці цільові об''єкти, щоб забезпечити унікальність пов''язаних із ними імен таблиць дельта-захоплення.
#XMSG
validateConfluentEmptySchema=Визначення схеми
#XMSG
validateConfluentEmptySchemaDescUpdated=Вихідна таблиця не має схеми. Виберіть "Конфігурація схеми", щоб її визначити.
#XMSG
validationCSVEncoding=Недійсне кодування CSV
#XMSG
validationCSVEncodingDescription=Кодування CSV завдання недійсне.
#XMSG
validateConfluentEmptySchema=Виберіть сумісний цільовий тип даних
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Виберіть сумісний цільовий тип даних
#XMSG
globalValidateTargetDataTypeDesc=Сталася помилка, пов'язана з меппінгами стовпчиків. Перейдіть у розділ "Проєкція" і переконайтеся, що кожен вихідний стовпчик зіставлено шляхом меппінгу з унікальним стовпчиком із сумісним типом даних, а всі визначені вирази дійсні.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Дублікати імен стовпчиків.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Дублікати імен стовпчиків не підтримуються. Скористайтеся діалоговим вікном "Проєкція", щоб це виправити. Дублюються імена стовпчиків для таких цільових об''єктів: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Дублікати імен стовпчиків.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Дублікати імен стовпчиків не підтримуються. Дублюються імена стовпчиків для таких цільових об''єктів: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Можливі розбіжності в даних.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=У випадку типу завантаження "Лише дельта" зміни, внесені в джерело між останнім збереженням і наступним прогоном, не враховуватимуться.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Змініть тип завантаження на "Початкове".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Реплікація об'єктів на базі ABAP, що не мають первинного ключа, можлива тільки для типу завантаження "Тільки початкове".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Вимкніть дельта-захоплення.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Щоб реплікувати об'єкт, що не має первинного ключа, використовуючи тип з'єднання з джерелом ABAP, потрібно спочатку вимкнути дельта-захоплення для цієї таблиці.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Змініть цільовий об'єкт.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Цільовий об'єкт використати не можна, оскільки активовано дельта-захоплення. Можна або перейменувати цільовий об'єкт, а потім вимкнути дельта-захоплення для нового (перейменованого) об'єкта, або створити мепінг вихідного об'єкта з цільовим об'єктом, для якого дельта-захоплення вимкнено.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Змініть цільовий об'єкт.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Цільовий об'єкт використати не можна, оскільки він не має обов'язкового технічного стовпчика __load_package_id. Можна перейменувати цільовий об'єкт, використавши ім'я, якого наразі не існує. Тоді система створить новий об'єкт, що матиме таке саме визначення, як і вихідний об'єкт, і міститиме технічний стовпчик. Крім того, можна створити мепінг цільового об'єкта з наявним об'єктом, що має обов'язковий технічний стовпчик (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Змініть цільовий об'єкт.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Цільовий об'єкт використати не можна, оскільки він не має обов'язкового технічного стовпчика __load_record_id. Можна перейменувати цільовий об'єкт, використавши ім'я, якого наразі не існує. Тоді система створить новий об'єкт, що матиме таке саме визначення, як і вихідний об'єкт, і міститиме технічний стовпчик. Крім того, можна створити мепінг цільового об'єкта з наявним об'єктом, що має обов'язковий технічний стовпчик (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Змініть цільовий об'єкт.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Цільовий об'єкт використати не можна, оскільки тип даних його технічного стовпчика __load_record_id відмінний від "string(44)". Можна перейменувати цільовий об'єкт, використавши ім'я, якого наразі не існує. Тоді система створить новий об'єкт, що матиме таке саме визначення, як і вихідний об'єкт, і, відповідно, правильний тип даних. Крім того, можна створити мепінг цільового об'єкта з наявним об'єктом, що має обов'язковий технічний стовпчик (__load_record_id) з правильним типом даних.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Змініть цільовий об'єкт.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Цільовий об'єкт використати не можна, оскільки він має первинний ключ, а вихідний об'єкт його не має. Можна перейменувати цільовий об'єкт, використавши ім'я, якого наразі не існує. Тоді система створить новий об'єкт, що матиме таке саме визначення, як і вихідний об'єкт, і, відповідно, не матиме первинного ключа. Крім того, можна створити мепінг цільового об'єкта з наявним об'єктом, що має обов'язковий технічний стовпчик (__load_package_id) і не має первинного ключа.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Змініть цільовий об'єкт.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Цільовий об'єкт використати не можна, оскільки він має первинний ключ, а вихідний об'єкт його не має. Можна перейменувати цільовий об'єкт, використавши ім'я, якого наразі не існує. Тоді система створить новий об'єкт, що матиме таке саме визначення, як і вихідний об'єкт, і, відповідно, не матиме первинного ключа. Крім того, можна створити мепінг цільового об'єкта з наявним об'єктом, що має обов'язковий технічний стовпчик (__load_record_id) і не має первинного ключа.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Змініть цільовий об'єкт.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Цільовий об'єкт використати не можна, оскільки тип даних його технічного стовпчика __load_package_id відмінний від "binary(>=256)". Можна перейменувати цільовий об'єкт, використавши ім'я, якого наразі не існує. Тоді система створить новий об'єкт, що матиме таке саме визначення, як і вихідний об'єкт, і, відповідно, правильний тип даних. Крім того, можна створити мепінг цільового об'єкта з наявним об'єктом, що має обов'язковий технічний стовпчик (__load_package_id) з правильним типом даних.
#XMSG
validationAutoRenameTargetDPID=Цільові стовпчики перейменовано.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Вилучіть вихідний об'єкт.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Вихідний об'єкт не має ключового стовпчика, а в такому контексті це не підтримується.
#XMSG
validationAutoRenameTargetDPIDDescription=Оскільки додано автоматичне проєктування, ці цільові стовпчики перейменовано, щоб забезпечити можливість реплікації з джерела ABAP без ключів:{1}{1} {0} {1}{1}Це сталося з однієї з таких причин:{1}{1}{2} ім''я стовпчика зарезервоване;{1}{2} використовуються непідтримувані символи;{1}{2} префікс зарезервовано.
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Реплікація до {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Збереження та розгортання потоків реплікації, цільовим об''єктом яких є {0}, наразі неможливе, оскільки триває технічне обслуговування цієї функції.
#XMSG
TargetColumnSkippedLTF=Цільовий стовпчик пропущено.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Цільовий стовпчик пропущено через непідтримуваний тип даних. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Стовпчик часу як первинний ключ.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Вихідний об'єкт має стовпчик часу, що слугує первинним ключем, але в такому контексті це не підтримується.
#XMSG
validateNoPKInLTFTarget=Бракує первинного ключа.
#XMSG
validateNoPKInLTFTargetDescription=Первинний ключ не визначено в цільовому об'єкті, що не підтримується в цьому контексті.
#XMSG
validateABAPClusterTableLTF=Таблиця кластера ABAP.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Вихідний об'єкт – це таблиця кластера ABAP, а в такому контексті це не підтримується.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Схоже, ви ще не додали жодних даних.
#YINS
welcomeText2=Щоб розпочати процес реплікації, виберіть з’єднання та вихідний об’єкт ліворуч.

#XBUT
wizStep1=Виберіть з’єднання з джерелом
#XBUT
wizStep2=Вибрати вихідний контейнер
#XBUT
wizStep3=Додати вихідні об’єкти

#XMSG
limitDataset=Досягнуто максимальної кількості об’єктів. Видаліть існуючі об’єкти, щоб додати нові, або створіть новий потік реплікації.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Не вдалося запустити потік реплікації до цього цільового з'єднання із системою, відмінною від SAP, через відсутність доступного вихідного обсягу на цей місяць.
#XMSG
premiumOutBoundRFAdminWarningMsg=Адміністратор може збільшити кількість блоків платного вихідного обсягу для цього орендатора, надавши вихідний обсяг на цей місяць.
#XMSG
messageForToastForDPIDColumn2=До цільового об''єкта додано новий стовпчик для об''єктів ({0}). Це необхідно, щоб обробити дублікати записів у з''єднанні з об''єктами джерела на базі ABAP, що не мають первинного ключа.
#XMSG
PremiumInboundWarningMessage=Залежно від кількості потоків реплікації та обсягів даних, які слід реплікувати, ресурси SAP HANA,{0}необхідні для реплікації даних за допомогою {1}, можуть перевищувати доступні обсяги для вашого орендатора.
#XMSG
PremiumInboundWarningMsg=Залежно від кількості потоків реплікації та обсягів даних, які слід реплікувати, ресурси SAP HANA,{0}необхідні для реплікації даних за допомогою "{1}", можуть перевищувати доступні обсяги для вашого орендатора.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Введіть ім'я проекції.
#XMSG
emptyTargetColumn=Введіть ім'я цільового стовпчика.
#XMSG
emptyTargetColumnBusinessName=Введіть бізнес-ім'я цільового стовпчика.
#XMSG
invalidTransformName=Введіть ім'я проекції.
#XMSG
uniqueColumnName=Перейменуйте цільовий стовпчик.
#XMSG
copySourceColumnLbl=Копіювання стовпчиків із вихідного об'єкта
#XMSG
renameWarning=Перейменовуючи цільову таблицю, переконайтеся, що вибрано унікальне ім'я. Якщо таблиця з новим ім'ям уже існує в просторі, для неї використовуватиметься визначення цієї таблиці.

#XMSG
uniqueColumnBusinessName=Змініть бізнес-ім'я цільового стовпчика.
#XMSG
uniqueSourceMapping=Виберіть інший вихідний стовпчик.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=Вихідний стовпчик "{0}" уже використовується такими цільовими стовпчиками:{1}{1}{2}{1}{1} Щоб зберегти проєкцію, виберіть для цього або для інших цільових стовпчиків такий вихідний стовпчик, який ще не використовується.
#XMSG
uniqueColumnNameDescription=Введене ім'я цільового стовпчика вже існує. Щоб мати змогу зберегти проекцію, потрібно ввести унікальне ім'я стовпчика.
#XMSG
uniqueColumnBusinessNameDesc=Бізнес-ім'я цільового стовпчика вже існує. Щоб зберегти проекцію, слід ввести унікальне бізнес-ім'я стовпчика.
#XMSG
emptySource=Виберіть вихідний стовпчик або введіть константу.
#XMSG
emptySourceDescription=Щоб створити припустимий запис меппінгу, необхідно вибрати вихідний стовпчик або ввести значення константи.
#XMSG
emptyExpression=Визначте меппінг.
#XMSG
emptyExpressionDescription1=Виберіть вихідний стовпчик, у який ви хочете відобразити цільовий стовпчик, або встановіть прапорець у стовпчику {0} "Функції / контакти" {1}. {2} {2} Функції вводяться автоматично відповідно до типу цільових даних. Значення констант можна ввести вручну.
#XMSG
numberExpressionErr=Введіть число.
#XMSG
numberExpressionErrDescription=Вибрано числовий тип даних. Це означає, що можна вводити тільки числові значення і за потреби десяткову крапку. Не використовуйте одинарні лапки.
#XMSG
invalidLength=Введіть дійсне значення довжини.
#XMSG
invalidLengthDescription=Довжина типу даних має бути не меншою за довжину вихідного стовпчика й може мати значення від 1 до 5000.
#XMSG
invalidMappedLength=Введіть дійсне значення довжини.
#XMSG
invalidMappedLengthDescription=Довжина типу даних має бути не меншою за довжину вихідного стовпчика "{0}" й може мати значення від 1 до 5000.
#XMSG
invalidPrecision=Введіть дійсне значення точності.
#XMSG
invalidPrecisionDescription=Точність визначає загальну кількість десяткових знаків. Масштаб визначає кількість цифр після десяткової крапки й може бути в діапазоні від 0 до значення точності.{0}{0} Приклади. {0}{1} Точність 6, масштаб 2 відповідає такому числу, як 1234,56.{0}{1} Точність 6, масштаб 6 відповідає такому числу, як 0,123546.{0} {0} Точність і масштаб цільового значення мають бути сумісними з точністю та масштабом вихідного значення, щоб усі цифри вихідного значення вмістилися в цільове поле. Наприклад, якщо ви маєте точність 6 і масштаб 2 у вихідному полі (і, відповідно, цифри, відмінні від 0, перед десятковою крапкою), ви не можете мати точність 6 і масштаб 6 у цільовому полі.
#XMSG
invalidPrimaryKey=Введіть принаймні один первинний ключ.
#XMSG
invalidPrimaryKeyDescription=Для цієї схеми не визначено первинний ключ.
#XMSG
invalidMappedPrecision=Введіть дійсне значення точності.
#XMSG
invalidMappedPrecisionDescription1=Точність визначає загальну кількість десяткових знаків. Масштаб визначає кількість цифр після десяткової крапки й може бути в діапазоні від 0 до значення точності.{0}{0} Приклади.{0}{1} Точність 6, масштаб 2 відповідає такому числу, як 1234,56.{0}{1} Точність 6, масштаб 6 відповідає такому числу, як 0,123546.{0}{0}Точність типу даних має бути не меншою за точність джерела ({2}).
#XMSG
invalidScale=Введіть дійсне значення масштабу.
#XMSG
invalidScaleDescription=Точність визначає загальну кількість десяткових знаків. Масштаб визначає кількість цифр після десяткової крапки й може бути в діапазоні від 0 до значення точності.{0}{0} Приклади. {0}{1} Точність 6, масштаб 2 відповідає такому числу, як 1234,56.{0}{1} Точність 6, масштаб 6 відповідає такому числу, як 0,123546.{0} {0} Точність і масштаб цільового значення мають бути сумісними з точністю та масштабом вихідного значення, щоб усі цифри вихідного значення вмістилися в цільове поле. Наприклад, якщо ви маєте точність 6 і масштаб 2 у вихідному полі (і, відповідно, цифри, відмінні від 0, перед десятковою крапкою), ви не можете мати точність 6 і масштаб 6 у цільовому полі.
#XMSG
invalidMappedScale=Введіть дійсне значення масштабу.
#XMSG
invalidMappedScaleDescription1=Точність визначає загальну кількість десяткових знаків. Масштаб визначає кількість цифр після десяткової крапки й може бути в діапазоні від 0 до значення точності.{0}{0} Приклади.{0}{1} Точність 6, масштаб 2 відповідає такому числу, як 1234,56.{0}{1} Точність 6, масштаб 6 відповідає такому числу, як 0,123546.{0}{0}Масштаб типу даних має бути не меншим за масштаб джерела ({2}).
#XMSG
nonCompatibleDataType=Виберіть сумісний цільовий тип даних.
#XMSG
nonCompatibleDataTypeDescription1=Тип даних, який ви тут укажете, має бути сумісним із вихідним типом даних ({0}). {1}{1} Наприклад, якщо вихідний стовпчик має рядковий тип даних і містить букви, для цільового стовпчика не можна використати десятковий тип даних.
#XMSG
invalidColumnCount=Виберіть вихідний стовпчик.
#XMSG
ObjectStoreInvalidScaleORPrecision=Введіть дійсне значення для точності та масштабу.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=Перше значення – це точність. Воно визначає загальну кількість цифр. Друге значення – це масштаб. Воно визначає кількість знаків після десяткової крапки. Введіть значення цільового масштабу більше за значення вихідного масштабу й переконайтеся, що різниця між введеним цільовим масштабом і значенням точності більша, ніж різниця між вихідним масштабом і значенням точності.
#XMSG
InvalidPrecisionORScale=Введіть дійсне значення для точності та масштабу.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=Перше значення – точність, що визначає загальну кількість десяткових розрядів. Друге значення – масштаб, що визначає кількість знаків після десяткової крапки.{0}{0}Оскільки вихідний тип даних не підтримується в Google BigQuery, його перетворено на цільовий тип даних DECIMAL. У цьому випадку точність можна визначити тільки в діапазоні від 38 до 76, а масштаб – у діапазоні від 9 до 38. Крім того, різниця між точністю та масштабом, що відповідає кількості знаків перед десятковою крапкою, має бути в діапазоні від 29 до 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=Перше значення – точність, що визначає загальну кількість десяткових розрядів. Друге значення – масштаб, що визначає кількість знаків після десяткової крапки.{0}{0}Оскільки вихідний тип даних не підтримується в Google BigQuery, його перетворено на цільовий тип даних DECIMAL. У цьому випадку визначена точність має бути не меншою ніж 20. Крім того, різниця між точністю та масштабом, що відповідає кількості знаків перед десятковою крапкою, має бути не меншою ніж 20.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=Перше значення – точність, що визначає загальну кількість десяткових розрядів. Друге значення – масштаб, що визначає кількість знаків після десяткової крапки.{0}{0}Оскільки вихідний тип даних не підтримується в цільовій системі, його перетворено на цільовий тип даних DECIMAL. У цьому випадку точність має бути визначена як будь-яке число, більше або рівне 1 й менше або рівне 38, а масштаб має бути меншим або рівним точності.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=Перше значення – точність, що визначає загальну кількість десяткових розрядів. Друге значення – масштаб, що визначає кількість знаків після десяткової крапки.{0}{0}Оскільки вихідний тип даних не підтримується в цільовій системі, його перетворено на цільовий тип даних DECIMAL. У цьому випадку визначена точність має бути не меншою ніж 20. Крім того, різниця між точністю та масштабом, що відповідає кількості знаків перед десятковою крапкою, має бути не меншою ніж 20.
#XMSG
invalidColumnCountDescription=Щоб створити припустимий запис меппінгу, необхідно вибрати вихідний стовпчик або ввести значення константи.
#XMSG
duplicateColumns=Перейменуйте цільовий стовпчик.
#XMSG
duplicateGBQCDCColumnsDesc=Ім'я цільового стовпчика зарезервовано в Google BigQuery. Його потрібно перейменувати, щоб мати змогу зберегти проекцію.
#XMSG
duplicateConfluentCDCColumnsDesc=Ім'я цільового стовпчика зарезервовано в Confluent. Його потрібно перейменувати, щоб мати змогу зберегти проекцію.
#XMSG
duplicateSignavioCDCColumnsDesc=Ім'я цільового стовпчика зарезервовано в SAP Signavio. Його потрібно перейменувати, щоб мати змогу зберегти проекцію.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Ім'я цільового стовпчика зарезервовано в MS OneLake. Його потрібно перейменувати, щоб мати змогу зберегти проекцію.
#XMSG
duplicateSFTPCDCColumnsDesc=Ім'я цільового стовпчика зарезервовано в SFTP. Його потрібно перейменувати, щоб мати змогу зберегти проекцію.
#XMSG
GBQTargetNameWithPrefixUpdated1=Ім''я цільового стовпчика містить префікс, зарезервований у Google BigQuery. Його потрібно перейменувати, щоб мати змогу зберегти проекцію. {0}{0}Ім''я цільового стовпчика не може починатися з жодного з цих рядків:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Скоротіть ім'я цільового стовпчика.
#XMSG
GBQtargetMaxLengthDesc=У Google BigQuery ім'я стовпчика може містити щонайбільше 300 символів. Скоротіть ім'я цільового стовпчика, щоб мати змогу зберегти проекцію.
#XMSG
invalidMappedScalePrecision=Точність і масштаб цілі мають бути сумісними з точністю та масштабом джерела, щоб усі цифри з джерела вмістилися в цільове поле.
#XMSG
invalidMappedScalePrecisionShortText=Введіть дійсне значення точності та масштабу.
#XMSG
validationIncompatiblePKTypeDescProjection3=Один або кілька вихідних стовпчиків мають типи даних, які не визначено як первинні ключі в Google BigQuery. У цільовому об''єкті не буде створено жодного з первинних ключів.{0}{0}Наведені далі цільові типи даних сумісні з типами даних Google BigQuery, для яких можна визначити первинний ключ: {1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Зніміть прапорець column __message_id.
#XMSG
uncheckColumnMessageIdDesc=Стовпчик: первинний ключ
#XMSG
validationOpCodeInsert=Слід ввести значення для вставлення.
#XMSG
recommendDifferentPrimaryKey=Радимо вибрати інший первинний ключ на рівні елемента.
#XMSG
recommendDifferentPrimaryKeyDesc=Якщо код операції вже визначено, радимо вибрати інші первинні ключі для індексу масиву та елементів, щоб уникнути таких проблем, як, наприклад, дублювання стовпчика.
#XMSG
selectPrimaryKeyItemLevel=Слід вибрати принаймні один первинний ключ для рівня заголовка й рівня елементів.
#XMSG
selectPrimaryKeyItemLevelDesc=Якщо масив або меппінг розгорнуто, слід вибрати два первинних ключа: по одному для рівня заголовка й рівня елементів.
#XMSG
invalidMapKey=Слід вибрати принаймні один первинний ключ на рівні заголовка.
#XMSG
invalidMapKeyDesc=Якщо масив або меппінг розгорнуто, слід вибрати первинний ключ на рівні заголовка.
#XFLD
txtSearchFields=Пошук цільових стовпчиків
#XFLD
txtName=Ім’я
#XMSG
txtSourceColValidation=Один або кілька вихідних стовпчиків не підтримуються:
#XMSG
txtMappingCount=Меппінг ({0})
#XMSG
schema=Схема
#XMSG
sourceColumn=Вихідні стовпчики
#XMSG
warningSourceSchema=Будь-які зміни, внесені до схеми, вплинуть на меппінги в діалоговому вікні проекції.
#XCOL
txtTargetColName=Цільовий стовпчик (технічне ім'я)
#XCOL
txtDataType=Тип даних цілі
#XCOL
txtSourceDataType=Тип вихідних даних
#XCOL
srcColName=Вихідний стовпчик (технічне ім'я)
#XCOL
precision=Точність
#XCOL
scale=Масштаб
#XCOL
functionsOrConstants=Функції / контакти
#XCOL
txtTargetColBusinessName=Цільовий стовпчик (бізнес-ім'я)
#XCOL
prKey=Первинний ключ
#XCOL
txtProperties=Властивості
#XBUT
txtOK=Зберегти
#XBUT
txtCancel=Скасувати
#XBUT
txtRemove=Вилучити
#XFLD
txtDesc=Опис
#XMSG
rftdMapping=Меппінг
#XFLD
@lblColumnDataType=Тип даних
#XFLD
@lblColumnTechnicalName=Технічне ім'я
#XBUT
txtAutomap=Автопризначення
#XBUT
txtUp=Вгору
#XBUT
txtDown=Вниз

#XTOL
txtTransformationHeader=Екстраполяція
#XTOL
editTransformation=Редагувати
#XTOL
primaryKeyToolip=Ключ


#XMSG
rftdFilter=Фільтр
#XMSG
rftdFilterColumnCount=Джерело: {0}({1})
#XTOL
rftdFilterColSearch=Пошук
#XMSG
rftdFilterColNoData=Немає стовпчиків для відображення
#XMSG
rftdFilteredColNoExps=Немає виразів фільтрів
#XMSG
rftdFilterSelectedColTxt=Додати фільтр для
#XMSG
rftdFilterTxt=Фільтр, доступний для
#XBUT
rftdFilterSelectedAddColExp=Додати вираз
#YINS
rftdFilterNoSelectedCol=Виберіть стовпчик, щоб додати фільтр.
#XMSG
rftdFilterExp=Вираз фільтра
#XMSG
rftdFilterNotAllowedColumn=Додавання фільтрів для цього стовпчика не підтримується.
#XMSG
rftdFilterNotAllowedHead=Непідтримуваний стовпчик
#XMSG
rftdFilterNoExp=Фільтр не визначено
#XTOL
rftdfilteredTt=Відфільтровано
#XTOL
rftdremoveexpTt=Вилучити вираз фільтра
#XTOL
validationMessageTt=Повідомлення про перевірку
#XTOL
rftdFilterDateInp=Вибрати дату
#XTOL
rftdFilterDateTimeInp=Вибрати дату й час
#XTOL
rftdFilterTimeInp=Виберіть час
#XTOL
rftdFilterInp=Введіть значення
#XMSG
rftdFilterValidateEmptyMsg=Вирази фільтрів ({0}) у стовпчику {1} порожні
#XMSG
rftdFilterValidateInvalidNumericMsg=Вираз фільтра ({0}) у стовпчику {1} порожні містять недійсні числові значення
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Вираз фільтра має містити дійсні числові значення
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Якщо схему цільового об'єкт змінено, скористайтеся функцією "Зіставити з існуючим цільовим об'єктом" на головній сторінці, щоб врахувати ці змін, і знову виконайте мепінг цільового об'єкта з його джерелом.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Якщо таблиця призначення вже існує і зіставлення містить зміну схеми, ви маєте належним чином змінити таблицю призначення перед розгортанням потоку реплікації.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Якщо ваше зіставлення передбачає зміну схеми, ви маєте належним чином змінити таблицю призначення перед розгортанням потоку реплікації.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=У вихідному визначенні пропущено такі непідтримувані стовпчики: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=У цільовому визначенні пропущено такі непідтримувані стовпчики: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Наведені далі об''єкти не підтримуються, оскільки їх розкрито для споживання: {0} {1} {0} {0} Щоб скористатися таблицями в потоці реплікації, для семантичного використання (у настройках таблиці) не має бути задано значення {2}Аналітичний набір даних{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Цільовий об''єкт не можна використовувати, оскільки його розкрито для споживання: {0} {0} Щоб скористатися таблицею в потоці реплікації, для семантичного використання (у настройках таблиці) не має бути задано значення {1}Аналітичний набір даних{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Цільовий об''єкт із таким іменем уже існує. Однак його не можна використовувати, оскільки його розкрито для споживання: {0} {0} Щоб скористатися таблицею в потоці реплікації, для семантичного використання (у настройках таблиці) не має бути задано значення {1}Аналітичний набір даних{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Об''єкт із таким ім''ям уже існує в цільовій системі. {0}Утім, цей об''єкт не можна використати як цільовий для потоку реплікації до локального репозиторію, оскільки він не є локальною таблицею.
#XMSG:
targetAutoRenameUpdated=Цільовий стовпчик перейменовано.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Цільовий стовпчик перейменовано, щоб забезпечити можливість реплікації до Google BigQuery. Це сталося з однієї з таких причин:{0} {1}{2}ім''я стовпчика зарезервоване;{3}{2}використовуються непідтримувані символи;{3}{2}префікс зарезервовано.{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Цільовий стовпчик перейменовано, щоб забезпечити можливість реплікації до Confluent. Це сталося з однієї з таких причин:{0} {1}{2}ім''я стовпчика зарезервоване;{3}{2}використовуються непідтримувані символи;{3}{2}префікс зарезервовано.{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Цільовий стовпчик перейменовано, щоб забезпечити можливість реплікації до цільової системи. Це сталося з однієї з таких причин:{0} {1}{2}використовуються непідтримувані символи;{3}{2}префікс зарезервовано.{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Цільовий стовпчик перейменовано, щоб забезпечити можливість реплікації до цільової системи. Це сталося з однієї з таких причин:{0} {1}{2}ім''я стовпчика зарезервоване;{3}{2}використовуються непідтримувані символи;{3}{2}префікс зарезервовано.{3}{4}
#XMSG:
targetAutoDataType=Змінено тип цільових даних.
#XMSG:
targetAutoDataTypeDesc=Тип цільових даних змінено на "{0}", оскільки вихідний тип даних у Google BigQuery не підтримується.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Тип цільових даних змінено на "{0}", оскільки вихідний тип даних у цільовому з''єднанні не підтримується.
#XMSG
projectionGBQUnableToCreateKey=Первинні ключі не буде створено.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=У Google BigQuery підтримується щонайбільше 16 первинних ключів, але вихідний об'єкт містить більшу кількість первинних ключів. У цільовому об'єкті не буде створено жоден із первинних ключів.
#XMSG
HDLFNoKeyError=Визначте один або кілька стовпчиків як первинний ключ.
#XMSG
HDLFNoKeyErrorDescription=Щоб реплікувати об'єкт, потрібно визначити один або кілька стовпчиків як первинний ключ. 
#XMSG
HDLFNoKeyErrorExistingTarget=Визначте один або кілька стовпчиків як первинний ключ.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Щоб реплікувати дані до наявного цільового об''єкта, у нього має бути один або кілька цільових стовпчиків, визначених як первинний ключ. {0} {0} Визначити один або кілька стовпчиків як первинний ключ можна кількома способами: {0}{0}{1} Скористайтеся локальним редактором таблиць, щоб змінити наявний цільовий об''єкт. Потім перезавантажте потік реплікації.{0}{0}{1} Перейменуйте цільовий об''єкт у потоці реплікації. У наслідок цього після початку прогону буде створено новий об''єкт. Після перейменування можна визначити один або кілька стовпчиків як первинний ключ у проєкції.{0}{0}{1} Зіставте шляхом меппінга об''єкт з іншим наявним цільовим об''єктом, у якого один або кілька стовпчиків уже визначено як первинний ключ.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Первинний ключ змінено.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=Порівняно з вихідним об''єктом для цільового об''єкта визначено іншу кількість стовпчиків як первинний ключ. Щоб уникнути можливого пошкодження даних під час їх реплікації згодом, переконайтеся, що ці стовпчики дають змогу однозначно ідентифікувати всі рядки. {0} {0} У вихідному об''єкті як первинний ключ визначено такі стовпчики: {0} {1}
#XMSG
duplicateDPIDColumns=Перейменуйте цільовий стовпчик.
#XMSG
duplicateDPIDDColumnsDesc1=Це ім'я цільового стовпчика зарезервовано для технічного стовпчика. Введіть інше ім'я, щоб зберегти проєкцію.
#XMSG:
targetAutoRenameDPID=Цільовий стовпчик перейменовано.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Цільовий стовпчик перейменовано, щоб забезпечити можливість реплікації з джерела ABAP без ключів. Це сталося з однієї з таких причин:{0} {1}{2}ім''я стовпчика зарезервоване;{3}{2}використовуються непідтримувані символи;{3}{2}префікс зарезервовано.{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle=Настройки цілі {0}
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle=Настройки джерела {0}
#XBUT
connectionSettingSave=Зберегти
#XBUT
connectionSettingCancel=Скасувати
#XBUT: Button to keep the object level settings
txtKeep=Зберігати
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Перезаписати
#XFLD
targetConnectionThreadlimit=Ліміт кількості тредів цільового об'єкта для початкового завантаження (1–100)
#XFLD
connectionThreadLimit=Ліміт кількості тредів джерела для початкового завантаження (1–100)
#XFLD
maxConnection=Ліміт кількості тредів реплікації (1–100)
#XFLD
kafkaNumberOfPartitions=Кількість розділів
#XFLD
kafkaReplicationFactor=Коефіцієнт реплікації
#XFLD
kafkaMessageEncoder=Кодер повідомлень
#XFLD
kafkaMessageCompression=Стиснення повідомлень
#XFLD
fileGroupDeltaFilesBy=Групувати дельту за
#XFLD
fileFormat=Тип файлу
#XFLD
csvEncoding=Кодування CSV
#XFLD
abapExitLbl=Вихід ABAP
#XFLD
deltaPartition=Кількість тредів об'єкта для дельта-завантажень (1–10)
#XFLD
clamping_Data=Виводити помилку в разі скорочення даних
#XFLD
fail_On_Incompatible=Виводити помилку в разі несумісності даних
#XFLD
maxPartitionInput=Максимальна кількість розділів
#XFLD
max_Partition=Визначте максимальну кількість розділів
#XFLD
include_SubFolder=Включити підпапки
#XFLD
fileGlobalPattern=Глобальний шаблон для імені файлу
#XFLD
fileCompression=Стиснення файлу
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Роздільник у файлі
#XFLD
fileIsHeaderIncluded=Заголовок файлу
#XFLD
fileOrient=Orient
#XFLD
gbqWriteMode=Режим записування
#XFLD
suppressDuplicate=Приховати дублікати
#XFLD
apacheSpark=Активувати сумісність з Apache Spark
#XFLD
clampingDatatypeCb=Фіксувати типи даних із плаваючою десятковою крапкою
#XFLD
overwriteDatasetSetting=Перезаписати цільові настройки на рівні об'єкта
#XFLD
overwriteSourceDatasetSetting=Перезаписати вихідні настройки на рівні об'єкта
#XMSG
kafkaInvalidConnectionSetting=Введіть число від {0} до {1}.
#XMSG
MinReplicationThreadErrorMsg=Введіть число більше за {0}.
#XMSG
MaxReplicationThreadErrorMsg=Введіть число менше за {0}.
#XMSG
DeltaThreadErrorMsg=Введіть значення від 1 до 10.
#XMSG
MaxPartitionErrorMsg=Введіть значення в діапазоні 1 <= x <= 2147483647. Усталене значення – 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Введіть ціле число в діапазоні від {0} до {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Використовувати коефіцієнт реплікації брокера
#XFLD
serializationFormat=Формат серіалізації
#XFLD
compressionType=Тип стискання
#XFLD
schemaRegistry=Використовувати реєстр схем
#XFLD
subjectNameStrat=Стратегія для імені суб'єкта
#XFLD
compatibilityType=Тип сумісності
#XFLD
confluentTopicName=Ім'я теми
#XFLD
confluentRecordName=Ім'я запису
#XFLD
confluentSubjectNamePreview=Попередній перегляд імені суб'єкта
#XMSG
serializationChangeToastMsgUpdated2=Формат серіалізації змінено на JSON, оскільки реєстр схем не активовано. Щоб повернути формат серіалізації AVRO, потрібно спочатку активувати реєстр схем.
#XBUT
confluentTopicNameInfo=В основі імені теми завжди лежить ім'я цільового об'єкта. Його можна змінити, перейменувавши цільовий об'єкт.
#XMSG
emptyRecordNameValidationHeaderMsg=Введіть ім'я запису.
#XMSG
emptyPartionHeader=Введіть кількість розділів.
#XMSG
invalidPartitionsHeader=Введіть дійсну кількість розділів.
#XMSG
invalidpartitionsDesc=Введіть число від 1 до 200 000.
#XMSG
emptyrFactorHeader=Введіть коефіцієнт реплікації.
#XMSG
invalidrFactorHeader=Введіть дійсний коефіцієнт реплікації.
#XMSG
invalidrFactorDesc=Введіть число від 1 до 32 767.
#XMSG
emptyRecordNameValidationDescMsg=Якщо використовується формат серіалізації "AVRO", підтримуються тільки такі символи:{0}{1} A–Z{0}{1} a–z{0}{1} 0–9{0}{1} _(підкреслення)
#XMSG
validRecordNameValidationHeaderMsg=Введіть дійсне ім'я запису.
#XMSG
validRecordNameValidationDescMsgUpdated=Оскільки використовується формат серіалізації "AVRO", ім'я запису має містити тільки букви та цифри (A–Z, a–z, 0–9), а також символи підкреслення (_). Воно має починатися з букви або підкреслення.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=Параметр “Кількість тредів об'єкта для дельта-завантажень” можна буде задати, щойно один або кілька об'єктів матимуть тип завантаження “Початкове і дельта”.
#XMSG
invalidTargetName=Недійсне ім'я стовпчика
#XMSG
invalidTargetNameDesc=Ім'я цільового стовпчика має містити тільки букви та цифри (A–Z, a–z, 0–9), а також символи підкреслення (_).
#XFLD
consumeOtherSchema=Споживати інші версії схеми
#XFLD
ignoreSchemamissmatch=Ігнорувати невідповідність схем
#XFLD
confleuntDatatruncation=Виводити помилку в разі скорочення даних
#XFLD
isolationLevel=Рівень ізоляції
#XFLD
confluentOffset=Початкова точка
#XFLD
signavioGroupDeltaFilesByText=Немає
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=Ні
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=Ні

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Екстраполяція
#XBUT
txtAdd=Додати
#XBUT
txtEdit=Редагувати
#XMSG
transformationText=Додайте екстраполяцію, щоб налаштувати фільтр або зіставлення.
#XMSG
primaryKeyRequiredText=Виберіть первинний ключ із параметром "Конфігурація схеми".
#XFLD
lblSettings=Настройки
#XFLD
lblTargetSetting={0}: настройки цільового об''єкта
#XMSG
@csvRF=Виберіть файл, що містить визначення схеми, яке потрібно застосувати до всіх файлів у папці.
#XFLD
lblSourceColumns=Вихідні стовпчики
#XFLD
lblJsonStructure=Структура JSON
#XFLD
lblSourceSetting={0}: настройки джерела
#XFLD
lblSourceSchemaSetting={0}: настройки схеми джерела
#XBUT
messageSettings=Настройки повідомлення
#XFLD
lblPropertyTitle1=Властивості об'єкта
#XFLD
lblRFPropertyTitle=Властивості потоку реплікації
#XMSG
noDataTxt=Немає стовпчиків для відображення
#XMSG
noTargetObjectText=Не вибрано цільовий об'єкт.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Цільові стовпчики
#XMSG
searchColumns=Шукати стовпчики
#XTOL
cdcColumnTooltip=Стовпчик для дельта-захоплення
#XMSG
sourceNonDeltaSupportErrorUpdated=Вихідний об'єкт не підтримує дельта-захоплення.
#XMSG
targetCDCColumnAdded=Для дельта-захоплення додано 2 цільові стовпчики.
#XMSG
deltaPartitionEnable=Ліміт тредів об'єкта для дельта-завантажень додано до вихідних настройок.
#XMSG
attributeMappingRemovalTxt=Вилучення недійсних меппінгів, що не підтримуються для нового цільового об'єкта.
#XMSG
targetCDCColumnRemoved=Вилучено 2 цільових стовпчика, що використовувалися для дельта-захоплення.
#XMSG
replicationLoadTypeChanged=Тип завантаження змінено на "Початкове і дельта".
#XMSG
sourceHDLFLoadTypeError=Змініть тип завантаження на "Початкове і дельта".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Щоб реплікувати об'єкт зі з'єднання з джерелом із типом з'єднання "SAP HANA Cloud, файли озера даних" до SAP Datasphere, треба використовувати тип завантаження "Початкове і дельта".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Активуйте дельта-захоплення.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Щоб реплікувати об'єкт зі з'єднання з джерелом із типом з'єднання "SAP HANA Cloud, файли озера даних" до SAP Datasphere, треба активувати дельта-захоплення.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Змініть цільовий об'єкт.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Цільовий об'єкт використати не можна, оскільки вимкнуто дельта-захоплення. Можна або перейменувати цільовий об'єкт (що дасть змогу створити новий об'єкт із дельта-захопленням) або створити його мепінг із наявним об'єктом, для якого активовано дельта-захоплення.
#XMSG
deltaPartitionError=Введіть дійсну кількість тредів об'єкта для дельта-завантажень.
#XMSG
deltaPartitionErrorDescription=Введіть значення від 1 до 10.
#XMSG
deltaPartitionEmptyError=Введіть кількість тредів об'єкта для дельта-завантажень.
#XFLD
@lblColumnDescription=Опис
#XMSG
@lblColumnDescriptionText1=Для технічних цілей: для обробки дублікатів записів, які виникли внаслідок проблем під час реплікації об'єктів на базі ABAP, що не мають первинного ключа.
#XFLD
storageType=Сховище
#XFLD
skipUnmappedColLbl=Пропустити стовпчики без меппінгу
#XFLD
abapContentTypeLbl=Тип вмісту
#XFLD
autoMergeForTargetLbl=Автоматично об'єднувати дані
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Загальне
#XFLD
lblBusinessName=Бізнес-ім'я
#XFLD
lblTechnicalName=Технічне ім’я
#XFLD
lblPackage=Пакет
#XFLD
statusPanel=Статус прогону
#XBTN: Schedule dropdown menu
SCHEDULE=Розклад
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Змінити розклад
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Видалити розклад
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Створити розклад
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Помилка контрольної перевірки розкладу
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Розклад не можна створити, оскільки зараз триває розгортання потоку реплікації.{0}Дочекайтеся на завершення розгортання потоку реплікації.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Для потоків реплікації, що містять об'єкти з типом завантаження "Початкове і дельта" розклад створити не можна.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Для потоків реплікації, що містять об'єкти з типом завантаження "Початкове і дельта / Лише дельта" розклад створити не можна.
#XFLD : Scheduled popover
SCHEDULED=За розкладом
#XFLD
CREATE_REPLICATION_TEXT=Створення потоку реплікації
#XFLD
EDIT_REPLICATION_TEXT=Редагування потоку реплікації
#XFLD
DELETE_REPLICATION_TEXT=Видалення потоку реплікації
#XFLD
REFRESH_FREQUENCY=Частота
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Потік реплікації не можна розгорнути, оскільки наявний розклад{0} ще не підтримує тип завантаження "Початкове і дельта".{0}{0}Щоб розгорнути потік реплікації, слід задати для всіх об''єктів{0} тип завантаження "Тільки початкове". Крім того, можна видалити розклад, розгорнути {0}потік реплікації, а потім почати новий прогін. У такому разі прогін не матиме {0}кінця і підтримуватиме також об''єкти з типом завантаження "Початкове і дельта".
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Потік реплікації не можна розгорнути, оскільки наявний розклад{0} ще не підтримує тип завантаження "Початкове і дельта / Лише дельта".{0}{0}Щоб розгорнути потік реплікації, слід задати для всіх об''єктів{0} тип завантаження "Тільки початкове". Крім того, можна видалити розклад, розгорнути {0}потік реплікації, а потім почати новий прогін. У такому разі прогін не матиме {0}кінця і підтримуватиме також об''єкти з типом завантаження "Початкове і дельта / Лише дельта".
#XMSG
SCHEDULE_EXCEPTION=Не вдалося отримати подробиці розкладу
#XFLD: Label for frequency column
everyLabel=Кожні
#XFLD: Plural Recurrence text for Hour
hoursLabel=Години
#XFLD: Plural Recurrence text for Day
daysLabel=Дні
#XFLD: Plural Recurrence text for Month
monthsLabel=Місяці
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Хвилини
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Не вдалося отримати інформацію про можливості виконання за розкладом.
#XFLD :Paused field
PAUSED=Призупинено
#XMSG
navToMonitoring=Відкрити в моніторі потоків
#XFLD
statusLbl=Статус
#XFLD
lblLastRunExecuted=Початок останнього прогону
#XFLD
lblLastExecuted=Останній прогін
#XFLD: Status text for Completed
statusCompleted=Завершено
#XFLD: Status text for Running
statusRunning=Виконується
#XFLD: Status text for Failed
statusFailed=Помилка
#XFLD: Status text for Stopped
statusStopped=Зупинено
#XFLD: Status text for Stopping
statusStopping=Зупиняється
#XFLD: Status text for Active
statusActive=Активний
#XFLD: Status text for Paused
statusPaused=Призупинено
#XFLD: Status text for not executed
lblNotExecuted=Прогін ще не відбувся
#XFLD
messagesSettings=Настройки повідомлень
#XTOL
@validateModel=Повідомлення про перевірку
#XTOL
@hierarchy=Ієрархія
#XTOL
@columnCount=Кількість стовпчиків
#XMSG
VAL_PACKAGE_CHANGED=Ви присвоїли цей об''єкт пакету "{1}". Натисніть кнопку "Зберегти", щоб підтвердити та схвалити цю зміну. Зверніть увагу, що після збереження в цьому редакторі не можна скасувати присвоєння пакету.
#XMSG
MISSING_DEPENDENCY=Залежності об''єкта "{0}" не можна розв''язати в пакеті "{1}".
#XFLD
deltaLoadInterval=Інтервал дельта-завантаження
#XFLD
lblHour=Години (0–24)
#XFLD
lblMinutes=Хвилини (0–59)
#XMSG
maxHourOrMinErr=Введіть значення від 0 до {0}
#XMSG
maxDeltaInterval=Максимальне значення інтервалу дельта-завантаження – 24 години.{0}Змініть відповідно значення у хвилинах або годинах.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Шлях до цільового контейнера
#XFLD
confluentSubjectName=Ім'я суб'єкта
#XFLD
confluentSchemaVersion=Версія схеми
#XFLD
confluentIncludeTechKeyUpdated=Включити технічний ключ
#XFLD
confluentOmitNonExpandedArrays=Пропустити не розгорнуті масиви
#XFLD
confluentExpandArrayOrMap=Розгорнути масив або меппінг
#XCOL
confluentOperationMapping=Меппінг операцій
#XCOL
confluentOpCode=Код операції
#XFLD
confluentInsertOpCode=Вставити
#XFLD
confluentUpdateOpCode=Оновити
#XFLD
confluentDeleteOpCode=Видалити
#XFLD
expandArrayOrMapNotSelectedTxt=Не вибрано
#XFLD
confluentSwitchTxtYes=Так
#XFLD
confluentSwitchTxtNo=Ні
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Помилка
#XTIT
executeWarning=Застереження
#XMSG
executeunsavederror=Збережіть потік реплікації перед прогоном.
#XMSG
executemodifiederror=У потоці реплікації є незбережені зміни. Збережіть потік реплікації.
#XMSG
executeundeployederror=Перш ніж використовувати потік реплікації, його потрібно розгорнути.
#XMSG
executedeployingerror=Дочекайтеся завершення розгортання.
#XMSG
msgRunStarted=Прогін запущено
#XMSG
msgExecuteFail=Не вдалося прогнати потік реплікації
#XMSG
titleExecuteBusy=Зачекайте.
#XMSG
msgExecuteBusy=Ми готуємо ваші дані для прогону потоку реплікації.
#XTIT
executeConfirmDialog=Застереження
#XMSG
msgExecuteWithValidations=У потоці реплікації є помилки перевірки. Це може спричинити збій виконання.
#XMSG
msgRunDeployedVersion=Існують зміни для розгортання. Буде запущено останню розгорнуту версію потоку реплікації. Продовжити?
#XBUT
btnExecuteAnyway=Усе одно виконати
#XBUT
btnExecuteClose=Закрити
#XBUT
loaderClose=Закрити
#XTIT
loaderTitle=Завантаження
#XMSG
loaderText=Отримання докладної інформації із сервера
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Не вдалося запустити потік реплікації до цього цільового з'єднання із системою, відмінною від SAP,
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=через відсутність доступного вихідного обсягу на цей місяць.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Адміністратор може збільшити кількість блоків платного вихідного обсягу для цього
#XMSG
premiumOutBoundRFAdminErrMsgPart2=орендатора, надавши вихідний обсяг на цей місяць.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Помилка
#XTIT
deployInfo=Інформація
#XMSG
deployCheckFailException=Під час розгортання сталася особлива ситуація
#XMSG
deployGBQFFDisabled=Розгортання потоків реплікації з цільовим з'єднанням із Google BigQuery наразі неможливе, оскільки триває технічне обслуговування цієї функції.
#XMSG
deployKAFKAFFDisabled=Розгортання потоків реплікації з цільовим з'єднанням із Apache Kafka наразі неможливе, оскільки триває технічне обслуговування цієї функції.
#XMSG
deployConfluentDisabled=Розгортання потоків реплікації з цільовим з'єднанням із Confluent Kafka наразі неможливе, оскільки триває технічне обслуговування цієї функції.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Імена таблиць дельта-захоплення для наведених далі цільових об''єктів уже використовуються іншими об''єктами в репозиторії: {0} Щоб мати змогу розгорнути потік реплікації, потрібно перейменувати ці цільові об''єкти, щоб забезпечити унікальність пов''язаних із ними імен таблиць дельта-захоплення.
#XMSG
deployDWCSourceFFDisabled=Розгортання потоків реплікації, джерелом яких є SAP Datasphere, наразі неможливе, оскільки триває технічне обслуговування цієї функції.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Розгортання потоків реплікації, що містять локальні таблиці з можливістю дельта як вихідні об'єкти, наразі неможливе, оскільки триває технічне обслуговування цієї функції.
#XMSG
deployHDLFSourceFFDisabled=Розгортання потоків реплікації, що мають з'єднання з джерелом типу "SAP HANA Cloud, файли озера даних", наразі неможливе, оскільки триває технічне обслуговування.
#XMSG
deployObjectStoreAsSourceFFDisabled=Розгортання потоків реплікації, для яких джерелом є провайдери хмарного сховища, наразі неможливе.
#XMSG
deployConfluentSourceFFDisabled=Розгортання потоків реплікації, джерелом яких є Confluent Kafka, наразі неможливе, оскільки триває технічне обслуговування цієї функції.
#XMSG
deployMaxDWCNewTableCrossed=Великі потоки реплікації не можна відразу "зберегти та розгорнути". Спочатку збережіть потік реплікації, а потім розгорніть його.
#XMSG
deployInProgressInfo=Уже триває розгортання.
#XMSG
deploySourceObjectInUse=Вихідні об''єкти {0} уже використовуються в потоках реплікації {1}.
#XMSG
deployTargetSourceObjectInUse=Вихідні об''єкти {0} уже використовуються в потоках реплікації {1}. Цільові об''єкти {2} уже використовуються в потоках реплікації {3}.
#XMSG
deployReplicationFlowCheckError=Помилка перевірки потоку реплікації: {0}
#XMSG
preDeployTargetObjectInUse=Цільові об''єкти {0} вже використовуються в потоках реплікації {1}, а у двох різних потоках реплікації не можуть бути однакові цільові об''єкти. Виберіть інший цільовий об’єкт і спробуйте ще раз.
#XMSG
runInProgressInfo=Потік реплікації вже запущено.
#XMSG
deploySignavioTargetFFDisabled=Розгортання потоків реплікації, цільовим об'єктом яких є SAP Signavio, наразі неможливе, оскільки триває технічне обслуговування цієї функції.
#XMSG
deployHanaViewAsSourceFFDisabled=Розгортання потоків реплікації, вихідними об'єктами яких для вибраного з'єднаннях з джерелом є подання, наразі неможливе. Спробуйте ще раз пізніше.
#XMSG
deployMsOneLakeTargetFFDisabled=Розгортання потоків реплікації, цільовим об'єктом яких є MS OneLake, наразі неможливе, оскільки триває технічне обслуговування цієї функції.
#XMSG
deploySFTPTargetFFDisabled=Розгортання потоків реплікації, цільовим об'єктом яких є SFTP, наразі неможливе, оскільки триває технічне обслуговування цієї функції.
#XMSG
deploySFTPSourceFFDisabled=Розгортання потоків реплікації, джерелом яких є SFTP, наразі неможливе, оскільки триває технічне обслуговування цієї функції.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Технічне ім'я
#XFLD
businessNameInRenameTarget=Бізнес-ім'я
#XTOL
renametargetDialogTitle=Перейменувати цільовий об'єкт
#XBUT
targetRenameButton=Перейменувати
#XBUT
targetRenameCancel=Скасувати
#XMSG
mandatoryTargetName=Потрібно вказати ім’я.
#XMSG
dwcSpecialChar=_ (підкреслення) – єдиний дозволений спеціальний символ.
#XMSG
dwcWithDot=Ім'я цільової таблиці може складатися з латинських букв, чисел, символів підкреслення (_) і крапок (.). Першим символом може бути буква, число або підкреслення (і не може бути крапка).
#XMSG
nonDwcSpecialChar=Дозволені спеціальні символи: _ (підкреслення); –(дефіс); . (крапка)
#XMSG
firstUnderscorePattern=Ім’я не повинно починатися із символу підкреслення (_).

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: перегляд інструкції створення таблиці SQL
#XMSG
sqlDialogMaxPKWarning=У Google BigQuery підтримується щонайбільше 16 первинних ключів, але вихідний об'єкт містить більшу кількість. Тому в цій інструкції не визначено жодного первинного ключа.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Один або кілька вихідних стовпчиків мають типи даних, які не визначено як первинні ключі в Google BigQuery. Тому в цьому випадку не визначено жодного первинного ключа. У Google BigQuery первинний ключ можуть мати тільки такі типи даних: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Скопіювати та закрити
#XBUT
closeDDL=Закрити
#XMSG
copiedToClipboard=Скопійовано до буфера обміну


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Об''єкт ''''{0}'''' не може бути частиною ланцюжка завдань, тому що він не має кінця (оскільки містить об''єкти з типом завантаження ''''Початкове і дельта / Лише дельта'''').
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Об''єкт ''''{0}'''' не може бути частиною ланцюжка завдань, тому що він не має кінця (оскільки містить об''єкти з типом завантаження ''''Початкове і дельта'''').
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Об''єкт "{0}" не можна додати до ланцюжка завдань.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Деякі цільові об''єкти не збережено. Збережіть їх знову.{0}{0} Поведінка цієї функції змінилася. Раніше цільові об''єкти створювалися в цільовому середовищі лише в разі розгортання потоку реплікації.{0} Тепер на момент збереження потоку реплікації об''єкти вже створено. Ваш потік реплікації створено до внесення цієї зміни, і він містить нові об''єкти.{0} Вам слід ще раз зберегти потік реплікації, перш ніж його розгорнути, щоб нові об''єкти було включено до нього правильно.
#XMSG
confirmChangeContentTypeMessage=Ви збираєтеся змінити тип вмісту. Якщо це зробити, усі наявні проєкції буде видалено.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Ім'я суб'єкта
#XFLD
schemaDialogVersionName=Версія схеми
#XFLD
includeTechKey=Включити технічний ключ
#XFLD
segementButtonFlat=Фіксовано
#XFLD
segementButtonNested=Вкладено
#XMSG
subjectNamePlaceholder=Пошук імені суб'єкта

#XMSG
@EmailNotificationSuccess=Конфігурацію сповіщень електронною поштою під час виконання збережено.

#XFLD
@RuntimeEmailNotification=Сповіщення електронною поштою під час виконання

#XBTN
@TXT_SAVE=Зберегти


