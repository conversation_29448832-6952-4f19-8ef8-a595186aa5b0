#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Replikeringsflöde

#XFLD: Edit Schema button text
editSchema=Redigera schema

#XTIT : Properties heading
configSchema=Konfigurera schema

#XFLD: save changed button text
applyChanges=Tillämpa ändringar


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Välj källanslutning
#XFLD
sourceContainernEmptyText=Välj container
#XFLD
targetConnectionEmptyText=Välj målanslutning
#XFLD
targetContainernEmptyText=Välj container
#XFLD
sourceSelectObjectText=Välj källobjekt
#XFLD
sourceObjectCount=Källobjekt ({0})
#XFLD
targetObjectText=Målobjekt
#XFLD
confluentBrowseContext=Välj kontext
#XBUT
@retry=Försök igen
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Tenantuppgradering pågår.

#XTOL
browseSourceConnection=Genomsök källanslutning
#XTOL
browseTargetConnection=Genomsök målanslutning
#XTOL
browseSourceContainer=Genomsök källcontainer
#XTOL
browseAndAddSourceDataset=Lägg till källobjekt
#XTOL
browseTargetContainer=Genomsök målcontainer
#XTOL
browseTargetSetting=Genomsök målinställningar
#XTOL
browseSourceSetting=Genomsök källinställningar
#XTOL
sourceDatasetInfo=Info
#XTOL
sourceDatasetRemove=Ta bort
#XTOL
mappingCount=Detta representerar det totala antalet icke namnbaserade mappningar/uttryck.
#XTOL
filterCount=Detta representerar det totala antalet filtervillkor.
#XTOL
loading=Läser in...
#XCOL
deltaCapture=Deltaregistrering
#XCOL
deltaCaptureTableName=Deltaregistreringstabell
#XCOL
loadType=Inläsningstyp
#XCOL
deleteAllBeforeLoading=Radera allt före inläsning
#XCOL
transformationsTab=Projektioner
#XCOL
settingsTab=Inställningar

#XBUT
renameTargetObjectBtn=Byt namn på målobjekt
#XBUT
mapToExistingTargetObjectBtn=Mappa till befintligt målobjekt
#XBUT
changeContainerPathBtn=Ändra containersökväg
#XBUT
viewSQLDDLUpdated=Visa SQL-instruktion för skapa tabell
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Källobjektet medger ej deltaregistrering, men valt målobjekt har alternativet deltaregistrering aktiverat.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Målobjektet kan inte användas eftersom deltaregistrering har aktiverats,{0}men källobjektet medger inte deltaregistrering.{1}Du kan välja ett annat målobjekt som inte medger deltaregistrering.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Ett målobjekt med detta namn finns redan. Det kan dock inte användas{0}eftersom deltaregistrering har aktiverats, men källobjektet medger inte{0}deltaregistrering.{1}Du kan ange namnet på ett befintligt målobjekt som inte medger{0}deltaregistrering eller ett namn som inte finns ännu.
#XBUT
copySQLDDLUpdated=Kopiera SQL-instruktion för skapa tabell
#XMSG
targetObjExistingNoCDCColumnUpdated=Befintliga tabeller i Google BigQuery måste inkludera följande kolumner för ändringsdataregistrering (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Följande källobjekt medges ej då de inte har någon primärnyckel eller använder en anslutning som inte uppfyller villkor för att hämta primärnyckel:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Se SAP KBA 3531135 för en möjlig lösning.
#XLST: load type list values
initial=Endast initial
@emailUpdateError=Fel vid uppdatering av e-postaviseringslistan

#XLST
initialDelta=Initial och delta

#XLST
deltaOnly=Endast delta
#XMSG
confluentDeltaLoadTypeInfo=Endast inläsningstyp Initial och delta medges för Confluent Kafka-källa.
#XMSG
confirmRemoveReplicationObject=Vill du radera replikeringen?
#XMSG
confirmRemoveReplicationTaskPrompt=Denna åtgärd raderar befintliga replikeringar. Fortsätta?
#XMSG
confirmTargetConnectionChangePrompt=Denna åtgärd återställer målanslutning och målcontainer och raderar alla målobjekt. Fortsätta?
#XMSG
confirmTargetContainerChangePrompt=Denna åtgärd återställer målcontainern och raderar alla befintliga målobjekt. Fortsätta?
#XMSG
confirmRemoveTransformObject=Vill du radera projektionen {0}?
#XMSG
ErrorMsgContainerChange=Ett fel inträffade vid ändring av sökväg för container.
#XMSG
infoForUnsupportedDatasetNoKeys=Följande källobjekt medges ej eftersom de saknar en primärnyckel:
#XMSG
infoForUnsupportedDatasetView=Följande källobjekt av typ Vyer medges ej:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Följande källobjekt medges ej eftersom det är en SQL-vy som innehåller inparametrar:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Följande källobjekt medges ej eftersom extraktion är inaktiverat för dem:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=För Confluent-anslutningar är de enda tillåtna serialieringsformaten AVRO och JSON. Följande objekt medges inte eftersom de använder ett annat serialiseringsformat:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Schema kunde inte hämtas för följande objekt. Välj lämplig kontext eller verifiera konfigurationen för schemaregistrering
#XTOL: warning dialog header on deleting replication task
deleteHeader=Radera
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Inställningen Radera allt före inläsning medges ej för Google BigQuery.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Inställningen Radera allt före inläsning raderar och återskapar objektet (ämnet) före varje replikering. Detta raderar även alla allokerade meddelanden.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Inställningen Radera allt före inläsning medges ej för denna måltyp.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Tekniskt namn
#XCOL
connBusinessName=Affärsnamn
#XCOL
connDescriptionName=Beskrivning
#XCOL
connType=Typ
#XMSG
connTblNoDataFoundtxt=Inga anslutningar hittades
#XMSG
connectionError=Ett fel inträffade vid hämtning av anslutningar.
#XMSG
connectionCombinationUnsupportedErrorTitle=Anslutningskombination medges ej
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Replikering från {0} till {1} medges för närvarande inte.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Anslutningstypkombination medges ej
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Replikering från en anslutning med anslutningstyp SAP HANA Cloud, datasjöfiler till {0} medges ej. Du kan endast replikera till SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Välj
#XBUT
containerCancelBtn=Avbryt
#XTOL
containerSelectTooltip=Välj
#XTOL
containerCancelTooltip=Avbryt
#XMSG
containerContainerPathPlcHold=Sökväg för container
#XFLD
containerContainertxt=Container
#XFLD
confluentContainerContainertxt=Kontext
#XMSG
infoMessageForSLTSelection=Endast /SLT/massöverförings-ID tillåts som container. Välj en massöverförings-ID under SLT (om tillgänglig) och klicka på Skicka.
#XMSG
msgFetchContainerFail=Ett fel inträffade vid hämtning av containerdata.
#XMSG
infoMessageForSLTHidden=Anslutningen medger inte SLT-mappar, så de visas inte i listan nedan.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Välj en container som innehåller underordnade mappar.
#XMSG
sftpIncludeSubFolderText=Falskt
#XMSG
sftpIncludeSubFolderTextNew=Nej

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Ingen filtermappning än)
#XMSG
failToFetchRemoteMetadata=Ett fel inträffade vid hämtning av metadata.
#XMSG
failToFetchData=Ett fel inträffade vid hämtning av befintligt mål.
#XCOL
@loadType=Läs in typ
#XCOL
@deleteAllBeforeLoading=Radera allt före inläsning

#XMSG
@loading=Läser in...
#XFLD
@selectSourceObjects=Välj källobjekt
#XMSG
@exceedLimit=Du kan inte importera mer än {0} objekt åt gången. Avmarkera minst {1} objekt.
#XFLD
@objects=Objekt
#XBUT
@ok=OK
#XBUT
@cancel=Avbryt
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Nästa
#XBUT
btnAddSelection=Lägg till urval
#XTOL
@remoteFromSelection=Ta bort från urval
#XMSG
@searchInForSearchField=Sök i {0}

#XCOL
@name=Tekniskt namn
#XCOL
@type=Typ
#XCOL
@location=Plats
#XCOL
@label=Affärsnamn
#XCOL
@status=Status

#XFLD
@searchIn=Sök i:
#XBUT
@available=Tillgängliga
#XBUT
@selection=Urval

#XFLD
@noSourceSubFolder=Tabeller och vyer
#XMSG
@alreadyAdded=Finns redan i diagrammet
#XMSG
@askForFilter=Det finns fler än {0} element. Ange en filtersträng för att begränsa antalet element.
#XFLD: success label
lblSuccess=OK
#XFLD: ready label
lblReady=Klar
#XFLD: failure label
lblFailed=Misslyckades
#XFLD: fetching status label
lblFetchingDetail=Hämtar detaljer

#XMSG Place holder text for tree filter control
filterPlaceHolder=Ange text för att filtrera toppnivåobjekt
#XMSG Place holder text for server search control
serverSearchPlaceholder=Skriv och tryck på Retur för att söka
#XMSG
@deployObjects=Importerar {0} objekt...
#XMSG
@deployObjectsStatus=Antal objekt som har importerats: {0}. Antal objekt som inte kunde importeras: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Lokal repository-browser kunde inte öppnas.
#XMSG
@openRemoteSourceBrowserError=Källobjekt kunde inte hämtas.
#XMSG
@openRemoteTargetBrowserError=Målobjekt kunde inte hämtas.
#XMSG
@validatingTargetsError=Ett fel inträffade vid validering av mål.
#XMSG
@waitingToImport=Klar för import

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Maximalt antal objekt har överskridits. Välj högst 500 objekt för ett replikeringsflöde.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Tekniskt namn
#XFLD
sourceObjectBusinessName=Affärsnamn
#XFLD
sourceNoColumns=Antal kolumner
#XFLD
containerLbl=Container

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Du måste välja en källanslutning för replikeringsflödet.
#XMSG
validationSourceContainerNonExist=Du måste välja en container för källanslutningen.
#XMSG
validationTargetNonExist=Du måste välja en målanslutning för replikeringsflödet.
#XMSG
validationTargetContainerNonExist=Du måste välja en container för målanslutningen.
#XMSG
validationTruncateDisabledForObjectTitle=Replikering till utrymmen för objektlagring.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Replikering till molnlagring är endast möjlig om antingen alternativet Radera allt före inläsning har ställts in eller om målobjektet inte finns i målet.{0}{0} Om du ändå vill aktivera replikering av objekt som alternativet Radera allt före inläsning inte har ställts in för ser du till att målobjektet inte finns i systemet innan du kör replikeringsflödet.
#XMSG
validationTaskNonExist=Du måste ha minst en replikering i replikeringsflödet.
#XMSG
validationTaskTargetMissing=Du måste ha ett mål för replikeringen med källan: {0}
#XMSG
validationTaskTargetIsSAC=Valt mål är en SAC-artefakt: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Valt mål är inte en tillåten lokal tabell: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Ett objekt med detta namn finns redan i målet. Detta objekt kan dock inte användas som ett målobjekt för ett replikeringsflöde till lokal repository eftersom det inte är en lokal tabell.
#XMSG
validateSourceTargetSystemDifference=Du måste välja olika kombinationer av käll- och målanslutning och container för replikeringsflödet.
#XMSG
validateDuplicateSources=en eller flera replikeringar har källobjektnamn som är dubbletter: {0}.
#XMSG
validateDuplicateTargets=en eller flera replikeringar har målobjektnamn som är dubbletter: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=Källobjekt {0} medger ej deltaregistrering men målobjekt {1} gör det. Du måste ta bort replikeringen.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Du måste välja inläsningstypen "Initial och delta" för replikeringen med målobjektnamn {0}.
#XMSG
validationAutoRenameTarget=Målkolumner har bytt namn.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=En automatprojektion har lagts till och följande målkolumner har bytt namn för att tillåta replikering till målet:{1}{1} {0} {1}{1}Detta beror på något av följande: {1}{1}{2} Tecken som inte medges{1}{2} Reserverat prefix
#XMSG
validationAutoRenameTargetDescriptionUpdated=En automatprojektion har lagts till och följande målkolumner har bytt namn för att tillåta replikering till Google BigQuery:{1}{1} {0} {1}{1}Detta beror på något av följande:{1}{1}{2} Reserverat kolumnnamn{1}{2} Tecken som inte medges{1}{2} Reserverat prefix
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=En automatprojektion har lagts till och följande målkolumner har bytt namn för att tillåta replikeringar till Confluent:{1}{1} {0} {1}{1}Detta beror på något av följande:{1}{1}{2} Reserverat kolumnnamn{1}{2} Tecken som inte medges{1}{2} Reserverat prefix
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=En automatprojektion har lagts till och följande målkolumner har bytt namn för att tillåta replikeringar till målet:{1}{1} {0} {1}{1}Detta beror på något av följande:{1}{1}{2} Reserverat kolumnnamn{1}{2} Tecken som inte medges{1}{2} Reserverat prefix
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Målobjekt har bytt namn.
#XMSG
autoRenameInfoDesc=Målobjekt har bytt namn eftersom det innehöll tecken som inte medges. Endast följande tecken medges:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(punkt){0}{1}_(understreck){0}{1}-(tankstreck)
#XMSG
validationAutoTargetTypeConversion=Måldatatyper har ändrats.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=För följande målkolumner har måldatatyperna ändrats eftersom källdatatyperna inte medges i Google BigQuery:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=För följande målkolumner har måldatatyperna ändrats eftersom källdatatyperna inte medges i målet:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Förkorta målkolumnnamn.
#XMSG
validationMaxCharLengthGBQTargetDescription=I Google BigQuery kan kolumnnamn ha maximalt 300 tecken. Använd en projektion för att förkorta följande målkolumnnamn:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Primärnycklar kommer inte skapas.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=I Google BigQuery medges maximalt 16 primärnycklar, men källobjektet har ett större antal primärnycklar. Ingen av primärnycklarna kommer att skapas i målobjektet.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=En eller flera källkolumner har datatyper som inte kan definieras som primärnycklar i Google BigQuery. Ingen av primärnycklarna kommer att skapas i målobjektet.{0}{0}Följande måldatatyper är kompatibla med Google BigQuery-datatyper för vilka primärnycklar kan definieras:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Definiera en eller flera kolumner som primärnyckel.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Du måste definiera en eller flera kolumner som primärnyckel. Använd dialogrutan för källschema för att göra detta.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Definiera en eller flera kolumner som primärnyckel.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Du måste definierar en eller flera kolumner som primärnyckel som matchar primärnyckelbegränsningar för ditt källobjekt. Gå till Konfigurera schema i källobjektegenskaper för att göra det.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Ange ett giltigt värde för maximala partitioner.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Värde för maximala partitioner måste vara ≥ 1 och ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Definiera en eller flera kolumner som primärnyckel.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=För att replikera ett objekt måste du definiera en eller flera målkolumner som primärnyckel. Använd en projektion för att göra det.
#XMSG
validateHDLFNoPKExistingDatasetError=Definiera en eller flera kolumner som primärnyckel.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=För att replikera data till ett befintligt målobjekt så måste det ha en eller flera kolumner definierade som primärnyckel. {0} Följande alternativ finns för definition av en eller flera kolumner som primärnyckel: {0} {1}Använd den lokala tabelleditorn för att ändra befintligt målobjekt. Läs sedan in replikeringsflödet på nytt.{0}{1}Byt namn på målobjektet i replikeringsflödet. Då skapas ett nytt objekt när en körning startas. Efter namnbytet kan du definiera en eller flera kolumner som primärnyckel i en projektion.{0}{1}Mappa objektet till ett annat befintligt målobjekt där en eller flera kolumner redan har definierats som primärnyckel.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Valt mål finns redan i repository: {0}
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Namn på deltaregistreringstabeller används redan av andra tabeller i repository: {0}. Du måste byta namn på dessa målobjekt för att säkerställa att associerade namn på deltaregistreringstabeller är unika innan du kan spara replikeringsflödet.
#XMSG
validateConfluentEmptySchema=Definiera schema
#XMSG
validateConfluentEmptySchemaDescUpdated=Källtabell saknar ett schema. Välj Konfigurera schema för att definiera ett
#XMSG
validationCSVEncoding=Ogiltig CSV-kodning
#XMSG
validationCSVEncodingDescription=CSV-kodningen för uppgiften är inte giltig.
#XMSG
validateConfluentEmptySchema=Välj en kompatibel måldatatyp
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Välj en kompatibel måldatatyp
#XMSG
globalValidateTargetDataTypeDesc=Ett fel inträffade med kolumnmappningar. Gå till Projektion och kontrollera att alla källkolumner är mappade med en unik kolumn, med en kolumn som har en kompatibel datatyp och att alla definierade uttryck är giltiga.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Duplicerade kolumnnamn.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Duplicerade kolumnnamn medges ej. Använd dialogrutan för projektion för att åtgärda dem. Följande målobjekt har duplicerade kolumnnamn: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Duplicerade kolumnnamn.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Duplicerade kolumnnamn medges ej. Följande målobjekt har duplicerade kolumnnamn: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Det kan finnas inkonsistenser i data.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Inläsningstypen Endast delta beaktar inte ändringar som gjorts i källan mellan senaste lagring och nästa körning.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Ändra inläsningstyp till "Initial".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Replikering av ABAP-baserade objekt som inte har en primärnyckel är endast tillgänglig för inläsningstyp "Endast initial".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Inaktivera deltaregistrering.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=För att replikera ett objekt som inte har en primärnyckel via källanslutningstyp ABAP måste du först inaktivera deltaregistrering för den här tabellen.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Ändra målobjekt.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Målobjektet kan inte användas eftersom deltaregistrering är aktiverad. Du kan antingen byta namn på målobjektet och sedan slå av deltaregistrering för det nya objektet (med det nya namnet), eller mappa källobjektet till ett målobjekt som har deltaregistrering inaktiverad.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Ändra målobjekt.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Målobjektet kan inte användas eftersom det inte har den obligatoriska tekniska kolumnen __load_package_id. Du kan byta namn på målobjektet genom att använda ett namn som inte finns ännu. Systemet skapar sedan ett nytt objekt som har samma definition som källobjektet och innehåller den tekniska kolumnen. Alternativt kan du mappa målobjektet till ett befintligt objekt som har den obligatoriska tekniska kolumnen (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Ändra målobjekt.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Målobjektet kan inte användas eftersom det inte har den obligatoriska tekniska kolumnen __load_record_id. Du kan byta namn på målobjektet genom att använda ett namn som inte finns ännu. Systemet skapar sedan ett nytt objekt som har samma definition som källobjektet och innehåller den tekniska kolumnen. Alternativt kan du mappa målobjektet till ett befintligt objekt som har den obligatoriska tekniska kolumnen (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Ändra målobjekt.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Målobjektet kan inte användas eftersom datatypen för dess tekniska kolumn __load_record_id inte är "string(44)". Du kan byta namn på målobjektet genom att använda ett namn som inte finns ännu. Systemet skapar sedan ett nytt objekt som har samma definition som källobjektet och följaktligen korrekt datatyp. Alternativt kan du mappa målobjektet till ett befintligt objekt som har den obligatoriska tekniska kolumnen (__load_record_id) med korrekt datatyp.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Ändra målobjekt.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Målobjektet kan inte användas eftersom det har en primärnyckel medan källobjektet inte har någon. Du kan byta namn på målobjektet genom att använda ett namn som inte finns ännu. Systemet skapar sedan ett nytt objekt som har samma definition som källobjektet och följaktligen ingen primärnyckel. Alternativt kan du mappa målobjektet till ett befintligt objekt som har den obligatoriska tekniska kolumnen (__load_package_id) och som inte har en primärnyckel.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Ändra målobjekt.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Målobjektet kan inte användas eftersom det har en primärnyckel medan källobjektet inte har någon. Du kan byta namn på målobjektet genom att använda ett namn som inte finns ännu. Systemet skapar sedan ett nytt objekt som har samma definition som källobjektet och följaktligen ingen primärnyckel. Alternativt kan du mappa målobjektet till ett befintligt objekt som har den obligatoriska tekniska kolumnen (__load_record_id) och som inte har en primärnyckel.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Ändra målobjekt.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Målobjektet kan inte användas eftersom datatypen för dess tekniska kolumn __load_package_id inte är "binary(>=256)". Du kan byta namn på målobjektet genom att använda ett namn som inte finns ännu. Systemet skapar sedan ett nytt objekt som har samma definition som källobjektet och följaktligen korrekt datatyp. Alternativt kan du mappa målobjektet till ett befintligt objekt som har den obligatoriska tekniska kolumnen (__load_package_id) med korrekt datatyp.
#XMSG
validationAutoRenameTargetDPID=Målkolumner har bytt namn.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Ta bort källobjekt.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Källobjektet har inte en nyckelkolumn, vilket inte medges i den här kontexten.
#XMSG
validationAutoRenameTargetDPIDDescription=En automatprojektion har lagts till och följande målkolumner har bytt namn för att tillåta replikering från ABAP-källa utan nycklar:{1}{1} {0} {1}{1}Detta beror på något av följande:{1}{1}{2} Reserverat kolumnnamn{1}{2} Tecken som inte medges{1}{2} Reserverat prefix
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Replikering till {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Det går för tillfället inte att spara och distribuera replikeringsflöden som har {0} som mål eftersom vi utför underhåll av den här funktionen.
#XMSG
TargetColumnSkippedLTF=Målkolumn har hoppats över.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Målkolumn har hoppats över på grund av otillåten datatyp. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Tidskolumn som primärnyckel.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Källobjektet har en tidskolumn som primärnyckel, vilket inte medges i den här kontexten.
#XMSG
validateNoPKInLTFTarget=Primärnyckel
#XMSG
validateNoPKInLTFTargetDescription=Primärnyckel har inte definierats i målet, vilket inte medges i den här kontexten.
#XMSG
validateABAPClusterTableLTF=ABAP-klustertabell.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Källobjektet är en ABAP-klustertabell som inte medges i denna kontext.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Du verkar inte ha lagt till några data än.
#YINS
welcomeText2=Starta replikeringsflödet genom att välja en anslutning och ett källobjekt till vänster.

#XBUT
wizStep1=Välj källanslutning
#XBUT
wizStep2=Välj källcontainer
#XBUT
wizStep3=Lägg till källobjekt

#XMSG
limitDataset=Maximalt antal objekt har nåtts. Ta bort befintliga objekt för att lägga till nya eller skapa ett nytt replikeringsflöde.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Replikeringsflöde till denna icke-SAP-målanslutning kan inte startas eftersom det inte finns någon utgående volym för den här månaden.
#XMSG
premiumOutBoundRFAdminWarningMsg=En administratör kan öka blocken för premium utgående för den här tenanten vilket gör utgående volym tillgänglig för den här månaden.
#XMSG
messageForToastForDPIDColumn2=Ny kolumn har lagts till i mål för {0} objekt – vilket behövs för hantering av dubblettposter i anslutning till ABAP-baserade källobjekt som inte har en primärnyckel.
#XMSG
PremiumInboundWarningMessage=Beroende på det antal replikeringsflöden och den datavolym som ska replikeras kan de SAP HANA-resurser{0} som krävs för replikering av data via {1} överskrida tillgänglig kapacitet för din tenant.
#XMSG
PremiumInboundWarningMsg=Beroende på det antal replikeringsflöden och den datavolym som ska replikeras kan de SAP HANA-resurser{0} som krävs för replikering av data via "{1}" överskrida tillgänglig kapacitet för din tenant.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Ange ett namn på projektionen.
#XMSG
emptyTargetColumn=Ange ett målkolumnnamn.
#XMSG
emptyTargetColumnBusinessName=Ange affärsnamn för målkolumn.
#XMSG
invalidTransformName=Ange ett namn på projektionen.
#XMSG
uniqueColumnName=Byt namn på målkolumn.
#XMSG
copySourceColumnLbl=Kopiera kolumner från källobjekt
#XMSG
renameWarning=Välj ett unikt namn när du byter namn på måltabellen. Om tabellen med det nya namnet redan finns i utrymmet används definitionen för den tabellen.

#XMSG
uniqueColumnBusinessName=Byt namn på affärsnamn för kolumn.
#XMSG
uniqueSourceMapping=Välj en annan källkolumn.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=Källkolumnen {0} används redan av följande målkolumner:{1}{1}{2}{1}{1} För denna målkolumn eller för andra målkolumner, välj en källkolumn som inte redan används för att spara projektionen.
#XMSG
uniqueColumnNameDescription=Målkolumnnamn som du har angett finns redan. För att kunna spara projektionen behöver du ange ett unikt kolumnnamn.
#XMSG
uniqueColumnBusinessNameDesc=Affärsnamn för målkolumn finns redan. För att spara projektionen behöver du ange ett unikt affärsnamn för kolumnen.
#XMSG
emptySource=Välj en källkolumn eller ange en konstant.
#XMSG
emptySourceDescription=För att skapa en giltig mappningsuppgift behöver du välja en källkolumn eller ange ett konstantvärde.
#XMSG
emptyExpression=Definiera mappning.
#XMSG
emptyExpressionDescription1=Välj antingen en källkolumn som du vill mappa målkolumnen till eller markera kryssrutan i kolumnen {0} Funktioner/konstanter {1}. {2} {2} Funktioner anges automatiskt enligt måldatatypen. Konstantvärden kan anges manuellt.
#XMSG
numberExpressionErr=Ange ett tal.
#XMSG
numberExpressionErrDescription=Du har valt en numerisk datatyp. Det innebär att du endast kan ange siffror och decimaltecken om tillämpligt. Använd inte enkelt citationstecken.
#XMSG
invalidLength=Ange ett giltigt längdvärde.
#XMSG
invalidLengthDescription=Länden på datatypen måste vara lika med eller större än längden på källkolumnen och kan vara mellan 1 och 5000.
#XMSG
invalidMappedLength=Ange ett giltigt längdvärde.
#XMSG
invalidMappedLengthDescription=Länden på datatypen måste vara lika med eller större än längden på källkolumnen {0} och kan vara mellan 1 och 5000.
#XMSG
invalidPrecision=Ange ett giltigt precisionsvärde.
#XMSG
invalidPrecisionDescription=Precision definierar det totala antalet siffror. Skala definierar antalet siffror efter decimaltecknet och kan vara mellan 0 och precision.{0}{0} Exempel: {0}{1} Precision 6, skala 2 motsvarar tal som 1234,56.{0}{1} Precision 6, skala 6 motsvarar tal som 0,123546.{0} {0} Precision och skala för målet måste vara kompatibla med precision och skala för källan så att alla siffror från källan får plats i målfältet. Exempel: Om du har precision 6 och skala 2 i källan (och följaktligen andra siffror än 0 före decimaltecknet) kan du inte ha precision 6 och skala 6 i målet.
#XMSG
invalidPrimaryKey=Ange minst en primärnyckel.
#XMSG
invalidPrimaryKeyDescription=Primärnyckel har inte definierats för detta schema.
#XMSG
invalidMappedPrecision=Ange ett giltigt precisionsvärde.
#XMSG
invalidMappedPrecisionDescription1=Precision definierar det totala antalet siffror. Skala definierar antalet siffror efter decimaltecknet och kan vara mellan 0 och precision.{0}{0} Exempel:{0}{1} Precision 6, skala 2 motsvarar tal som 1234,56.{0}{1} Precision 6, skala 6 motsvarar tal som 0,123546.{0}{0}Precisionen för datatypen måste vara lika med eller större än precisionen för källan ({2}).
#XMSG
invalidScale=Ange ett giltigt skalavärde.
#XMSG
invalidScaleDescription=Precision definierar det totala antalet siffror. Skala definierar antalet siffror efter decimaltecknet och kan vara mellan 0 och precision.{0}{0} Exempel: {0}{1} Precision 6, skala 2 motsvarar tal som 1234,56.{0}{1} Precision 6, skala 6 motsvarar tal som 0,123546.{0} {0} Precision och skala för målet måste vara kompatibla med precision och skala för källan så att alla siffror från källan får plats i målfältet. Exempel: Om du har precision 6 och skala 2 i källan (och följaktligen andra siffror än 0 före decimaltecknet) kan du inte ha precision 6 och skala 6 i målet.
#XMSG
invalidMappedScale=Ange ett giltigt skalavärde.
#XMSG
invalidMappedScaleDescription1=Precision definierar det totala antalet siffror. Skala definierar antalet siffror efter decimaltecknet och kan vara mellan 0 och precision.{0}{0} Exempel: {0}{1} Precision 6, skala 2 motsvarar tal som 1234,56.{0}{1} Precision 6, skala 6 motsvarar tal som 0,123546.{0}{0}Skalan för datatypen måste vara lika med eller större än skalan för källan ({2}).
#XMSG
nonCompatibleDataType=Välj en kompatibel måldatatyp.
#XMSG
nonCompatibleDataTypeDescription1=Den datatyp som du anger här måste vara kompatibel med källdatatypen ({0}). {1}{1} Exempel: Om källkolumnen innehåller en datatypsträng med bokstäver kan du inte använda en decimaldatatyp för målet.
#XMSG
invalidColumnCount=Välj en källkolumn.
#XMSG
ObjectStoreInvalidScaleORPrecision=Ange ett giltigt värde för precision och skala.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=Det första värdet är precisionen, vilket definierar det totala antalet siffror. Det andra värdet är skalan, vilket definierar siffrorna efter decimaltecknet. Ange ett målvärde för skala som är större än källvärdet för skala och se till att differensen mellan det angivna målvärdet för skala och precision är större än differensen mellan källvärdet för skala och precision.
#XMSG
InvalidPrecisionORScale=Ange ett giltigt värde för precision och skala.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=Det första värdet är precisionen, vilket definierar det totala antalet siffror. Det andra värdet är skalan, vilket definierar siffrorna efter decimaltecknet.{0}{0}Eftersom källdatatypen inte medges i Google BigQuery konverteras den till måldatatypen DECIMAL. I det här fallet kan precisionen endast definieras mellan 38 och 76 och skalan mellan 9 och 38. Dessutom måste resultatet av precision minus skala, som representerar siffrorna före decimaltecknet, vara mellan 29 och 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=Det första värdet är precisionen, vilket definierar det totala antalet siffror. Det andra värdet är skalan, vilket definierar siffrorna efter decimaltecknet.{0}{0}Eftersom källdatatypen inte medges i Google BigQuery konverteras den till måldatatypen DECIMAL. I det här fallet måste precisionen definieras som 20 eller större. Dessutom måste resultatet av precision minus skala, som representerar siffrorna före decimaltecknet, vara 20 eller större.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=Det första värdet är precisionen, vilket definierar det totala antalet siffror. Det andra värdet är skalan, vilket definierar siffrorna efter decimaltecknet.{0}{0}Eftersom källdatatypen inte medges i målet konverteras den till måldatatypen DECIMAL. I det här fallet måste precisionen definieras av ett nummer större än eller lika med 1 och mindre än eller lika med 38, och skalan mindre än eller lika med precisionen.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=Det första värdet är precisionen, vilket definierar det totala antalet siffror. Det andra värdet är skalan, vilket definierar siffrorna efter decimaltecknet.{0}{0}Eftersom källdatatypen inte medges i målet konverteras den till måldatatypen DECIMAL. I det här fallet måste precisionen definieras som 20 eller större. Dessutom måste resultatet av precision minus skala, som representerar siffrorna före decimaltecknet, vara 20 eller större.
#XMSG
invalidColumnCountDescription=För att skapa en giltig mappningsuppgift behöver du välja en källkolumn eller ange ett konstantvärde.
#XMSG
duplicateColumns=Byt namn på målkolumn.
#XMSG
duplicateGBQCDCColumnsDesc=Målkolumnnamn är reserverat i Google BigQuery. Du behöver byta namn på den för att kunna spara projektionen.
#XMSG
duplicateConfluentCDCColumnsDesc=Målkolumnnamn är reserverat i Confluent. Du behöver byta namn på den för att kunna spara projektionen.
#XMSG
duplicateSignavioCDCColumnsDesc=Målkolumnnamn är reserverat i SAP Signavio. Du behöver byta namn på den för att kunna spara projektionen.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Målkolumnnamn är reserverat i MS OneLake. Du behöver byta namn på den för att kunna spara projektionen.
#XMSG
duplicateSFTPCDCColumnsDesc=Målkolumnnamn är reserverat i SFTP. Du behöver byta namn på den för att kunna spara projektionen.
#XMSG
GBQTargetNameWithPrefixUpdated1=Målkolumnnamnet innehåller ett prefix som är reserverat i Google BigQuery. Du behöver byta namn för att kunna spara projektionen. {0}{0}Målkolumnnamn får inte börja med någon av följande strängar:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Förkorta målkolumnnamn.
#XMSG
GBQtargetMaxLengthDesc=I Google BigQuery kan ett kolumnnamn använda maximalt 300 tecken. Förkorta målkolumnnamnet för att kunna spara projektionen.
#XMSG
invalidMappedScalePrecision=Precision och skala för målet måste vara kompatibla med precision och skala för källa så att alla siffror från källan passar i målfältet.
#XMSG
invalidMappedScalePrecisionShortText=Ange ett giltigt precisions- och skalavärde.
#XMSG
validationIncompatiblePKTypeDescProjection3=En eller flera källkolumner har datatyper som inte kan definieras som primärnycklar i Google BigQuery. Ingen av primärnycklarna kommer att skapas i målobjektet.{0}{0}Följande måldatatyper är kompatibla med Google BigQuery-datatyper för vilka primärnycklar kan definieras:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Avmarkera column __message_id.
#XMSG
uncheckColumnMessageIdDesc=Kolumn: Primärnyckel
#XMSG
validationOpCodeInsert=Du måste ange ett värde för Infoga.
#XMSG
recommendDifferentPrimaryKey=Vi rekommenderar att du väljer en annan primärnyckel på positionsnivå.
#XMSG
recommendDifferentPrimaryKeyDesc=När operationskoden redan har definierats rekommenderar vi att du väljer andra primärnycklar för vektorindex och positioner för att undvika problem med exempelvis dubbla kolumner.
#XMSG
selectPrimaryKeyItemLevel=Du måste välja minst en primärnyckel för både huvud- och positionsnivå.
#XMSG
selectPrimaryKeyItemLevelDesc=När en vektor eller mapp expanderas måste du välja två primärnycklar, en på huvudnivå och en på positionsnivå.
#XMSG
invalidMapKey=Du måste välja minst en primärnyckel på huvudnivå.
#XMSG
invalidMapKeyDesc=När en vektor eller mapp expanderas måste du välja en primärnyckel på huvudnivå.
#XFLD
txtSearchFields=Sök målkolumner
#XFLD
txtName=Namn
#XMSG
txtSourceColValidation=En eller flera källkolumner medges ej:
#XMSG
txtMappingCount=Mappningar ({0})
#XMSG
schema=Schema
#XMSG
sourceColumn=Källkolumner
#XMSG
warningSourceSchema=Ändringar av schema kommer att påverka mappningar i dialogrutan för projektion.
#XCOL
txtTargetColName=Målkolumn (tekniskt namn)
#XCOL
txtDataType=Måldatatyp
#XCOL
txtSourceDataType=Källdatatyp
#XCOL
srcColName=Källkolumn (tekniskt namn)
#XCOL
precision=Precision
#XCOL
scale=Skala
#XCOL
functionsOrConstants=Funktioner/konstanter
#XCOL
txtTargetColBusinessName=Målkolumn (affärsnamn)
#XCOL
prKey=Primärnyckel
#XCOL
txtProperties=Egenskaper
#XBUT
txtOK=Spara
#XBUT
txtCancel=Avbryt
#XBUT
txtRemove=Ta bort
#XFLD
txtDesc=Beskrivning
#XMSG
rftdMapping=Mappning
#XFLD
@lblColumnDataType=Datatyp
#XFLD
@lblColumnTechnicalName=Tekniskt namn
#XBUT
txtAutomap=Mappa automatiskt
#XBUT
txtUp=Upp
#XBUT
txtDown=Ned

#XTOL
txtTransformationHeader=Projektion
#XTOL
editTransformation=Redigera
#XTOL
primaryKeyToolip=Nyckel


#XMSG
rftdFilter=Filter
#XMSG
rftdFilterColumnCount=Källa: {0}({1})
#XTOL
rftdFilterColSearch=Sök
#XMSG
rftdFilterColNoData=Inga kolumner att visa
#XMSG
rftdFilteredColNoExps=Inga filteruttryck
#XMSG
rftdFilterSelectedColTxt=Lägg till filter för
#XMSG
rftdFilterTxt=Filter tillgängligt för
#XBUT
rftdFilterSelectedAddColExp=Lägg till uttryck
#YINS
rftdFilterNoSelectedCol=Välj en kolumn för att lägga till filter.
#XMSG
rftdFilterExp=Filteruttryck
#XMSG
rftdFilterNotAllowedColumn=Tillägg av filter medges inte för den här kolumnen.
#XMSG
rftdFilterNotAllowedHead=Kolumn medges ej
#XMSG
rftdFilterNoExp=Inget filter har definierats
#XTOL
rftdfilteredTt=Filtrerad
#XTOL
rftdremoveexpTt=Ta bort filteruttryck
#XTOL
validationMessageTt=Valideringsmeddelanden
#XTOL
rftdFilterDateInp=Välj ett datum
#XTOL
rftdFilterDateTimeInp=Välj en tidpunkt
#XTOL
rftdFilterTimeInp=Välj en tid
#XTOL
rftdFilterInp=Ange värde
#XMSG
rftdFilterValidateEmptyMsg={0} filteruttryck i {1} kolumn är tomma
#XMSG
rftdFilterValidateInvalidNumericMsg={0} filteruttryck i {1} kolumn innehåller ogiltiga numeriska värden
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Filteruttryck måste innehålla giltiga numeriska värden
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Om ett målobjektschema har ändrats, använd funktionen "Mappa till befintligt målobjekt" på huvudsidan för att överta ändringarna, och mappa om målobjektet med dess källa igen.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Om måltabellen redan finns och mappningen innehåller en schemaändring så måste du ändra måltabellen på motsvarande sätt före distribuering av replikeringsflödet.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Om mappningen innehåller en schemaändring så måste du ändra måltabellen på motsvarande sätt före distribuering av replikeringsflödet.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Följande otillåtna kolumner hoppades över från källdefinitionen: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Följande otillåtna kolumner hoppades över från måldefinitionen: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Följande objekt medges ej då de är exponerade för förbrukning: {0} {1} {0} {0} För att tabeller ska kunna användas i ett replikeringsflöde så får den semantiska användningen (i tabellinställningarna) inte vara inställd på {2}Analytiskt dataset{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Målobjektet kan inte användas då det är exponerat för förbrukning: {0} {0} För att tabellen ska kunna användas i ett replikeringsflöde så får den semantiska användningen (i tabellinställningarna) inte vara inställd på {1}Analytiskt dataset{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Ett målobjekt med detta namn finns redan, men kan inte användas då det är exponerat för förbrukning: {0} {0} För att tabellen ska kunna användas i ett replikeringsflöde så får den semantiska användningen (i tabellinställningarna) inte vara inställd på {1}Analytiskt dataset{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Ett objekt med detta namn finns redan i målet. {0}Detta objekt kan dock inte användas som ett målobjekt för ett replikeringsflöde till lokal repository eftersom det inte är en lokal tabell.
#XMSG:
targetAutoRenameUpdated=Målkolumn har bytt namn.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Målkolumnen har bytt namn för att tillåta replikeringar i Google BigQuery. Det beror på något av följande:{0} {1}{2}Reserverat kolumnnamn{3}{2}Tecken som inte medges{3}{2}Reserverat prefix{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Målkolumnen har bytt namn för att tillåta replikeringar i Confluent. Det beror på något av följande:{0} {1}{2}Reserverat kolumnnamn{3}{2}Tecken som inte medges{3}{2}Reserverat prefix{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Målkolumnen har bytt namn för att tillåta replikeringar i målet. Det beror på något av följande:{0} {1}{2}Tecken som inte medges{3}{2}Reserverat prefix{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Målkolumnen har bytt namn för att tillåta replikeringar i målet. Det beror på något av följande:{0} {1}{2}Reserverat kolumnnamn{3}{2}Tecken som inte medges{3}{2}Reserverat prefix{3}{4}
#XMSG:
targetAutoDataType=Måldatatyp har ändrats.
#XMSG:
targetAutoDataTypeDesc=Måldatatypen har ändrats till {0} eftersom källdatatypen inte medges i Google BigQuery.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Måldatatypen har ändrats till {0} eftersom källdatatypen inte medges i målanslutningen.
#XMSG
projectionGBQUnableToCreateKey=Primärnycklar kommer inte skapas.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=I Google BigQuery medges maximalt 16 primärnycklar, men källobjektet har ett större antal primärnycklar. Ingen av primärnycklarna kommer att skapas i målobjektet.
#XMSG
HDLFNoKeyError=Definiera en eller flera kolumner som primärnyckel.
#XMSG
HDLFNoKeyErrorDescription=För att replikera ett objekt måste du definiera en eller flera kolumner som primärnyckel.
#XMSG
HDLFNoKeyErrorExistingTarget=Definiera en eller flera kolumner som primärnyckel.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=För att replikera data till ett befintligt målobjekt så måste det ha en eller flera kolumner definierade som primärnyckel. {0}{0} Följande alternativ finns för definition av en eller flera kolumner som primärnyckel: {0}{0}{1} Använd den lokala tabelleditorn för att ändra befintligt målobjekt. Läs sedan in replikeringsflödet på nytt.{0}{0}{1} Byt namn på målobjektet i replikeringsflödet. Då skapas ett nytt objekt när en körning startas. Efter namnbytet kan du definiera en eller flera kolumner som primärnyckel i en projektion.{0}{0}{1} Mappa objektet till ett annat befintligt målobjekt där en eller flera kolumner redan har definierats som primärnyckel.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Primärnyckel har ändrats.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=Jämfört med källobjektet har du definierat andra kolumner som primärnyckel för målobjektet. Se till att dessa kolumner unikt identifierar alla rader för att undvika att data skadas vid senare replikering av data. {0} {0} I källobjektet har följande kolumner definierats som primärnyckel: {0} {1}
#XMSG
duplicateDPIDColumns=Byt namn på målkolumn.
#XMSG
duplicateDPIDDColumnsDesc1=Detta målkolumnnamn är reserverat för en teknisk kolumn. Ange ett annat namn för att spara projektion.
#XMSG:
targetAutoRenameDPID=Målkolumn har bytt namn.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Målkolumnen har bytt namn för att tillåta replikeringar från ABAP-källa utan nycklar. Det beror på något av följande:{0} {1}{2}Reserverat kolumnnamn{3}{2}Tecken som inte medges{3}{2}Reserverat prefix{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle=Målinställningar för {0}
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle=Källinställningar för {0}
#XBUT
connectionSettingSave=Spara
#XBUT
connectionSettingCancel=Avbryt
#XBUT: Button to keep the object level settings
txtKeep=Behåll
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Skriv över
#XFLD
targetConnectionThreadlimit=Trådgräns för mål för initial inläsning (1-100)
#XFLD
connectionThreadLimit=Trådgräns för källa för initial inläsning (1-100)
#XFLD
maxConnection=Trådgräns för replikering (1-100)
#XFLD
kafkaNumberOfPartitions=Antal partitioner
#XFLD
kafkaReplicationFactor=Replikeringsfaktor
#XFLD
kafkaMessageEncoder=Meddelandeavkodare
#XFLD
kafkaMessageCompression=Meddelandekomprimering
#XFLD
fileGroupDeltaFilesBy=Gruppera delta efter
#XFLD
fileFormat=Filtyp
#XFLD
csvEncoding=CSV-kodning
#XFLD
abapExitLbl=ABAP Exit
#XFLD
deltaPartition=Antal objekttrådar för deltainläsningar (1-10)
#XFLD
clamping_Data=Fel vid datatrunkering
#XFLD
fail_On_Incompatible=Fel vid inkompatibla data
#XFLD
maxPartitionInput=Maximalt antal partitioner
#XFLD
max_Partition=Definiera maximalt antal partitioner
#XFLD
include_SubFolder=inkludera underordnade mappar
#XFLD
fileGlobalPattern=Globalt mönster för filnamn
#XFLD
fileCompression=Filkomprimering
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Filavgränsare
#XFLD
fileIsHeaderIncluded=Filhuvud
#XFLD
fileOrient=Orient
#XFLD
gbqWriteMode=Skrivläge
#XFLD
suppressDuplicate=Undertryck dubbletter
#XFLD
apacheSpark=Aktivera Apache Spark-kompatibilitet
#XFLD
clampingDatatypeCb=Begränsa datatyper för decimala flyttal
#XFLD
overwriteDatasetSetting=Skriv över målinställningar på objektnivå
#XFLD
overwriteSourceDatasetSetting=Skriv över källinställningar på objektnivå
#XMSG
kafkaInvalidConnectionSetting=Ange ett tal mellan {0} och {1}.
#XMSG
MinReplicationThreadErrorMsg=Ange ett tal som är större än {0}.
#XMSG
MaxReplicationThreadErrorMsg=Ange ett tal som är mindre än {0}.
#XMSG
DeltaThreadErrorMsg=Ange ett värde mellan 1 och 10.
#XMSG
MaxPartitionErrorMsg=Ange värde mellan 1 <= x <= 2147483647. Standardvärdet är 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Ange ett heltal mellan {0} och {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Använd replikeringsfaktor för broker
#XFLD
serializationFormat=Serialiseringsformat
#XFLD
compressionType=Komprimeringstyp
#XFLD
schemaRegistry=Använd schemaregistrering
#XFLD
subjectNameStrat=Strategi för ämnesnamn
#XFLD
compatibilityType=Kompatibilitetstyp
#XFLD
confluentTopicName=Namn på ämne
#XFLD
confluentRecordName=Namn på post
#XFLD
confluentSubjectNamePreview=Förhandsgranskning för ämnesnamn
#XMSG
serializationChangeToastMsgUpdated2=Serialiseringsformatet har ändrats till JSON då schemaregistrering inte har aktiverats. Du måste aktivera schemaregistrering innan du kan ändra tillbaka serialiseringsformatet till AVRO.
#XBUT
confluentTopicNameInfo=Namn på ämne baseras alltid på målobjektnamn. Du kan ändra det genom att byta namn på målobjektet.
#XMSG
emptyRecordNameValidationHeaderMsg=Ange ett namn på post.
#XMSG
emptyPartionHeader=Ange antal partitioner.
#XMSG
invalidPartitionsHeader=Ange ett giltigt antal partitioner.
#XMSG
invalidpartitionsDesc=Ange ett tal mellan 1 och 200 000.
#XMSG
emptyrFactorHeader=Ange en replikeringsfaktor.
#XMSG
invalidrFactorHeader=Ange en giltig replikeringsfaktor.
#XMSG
invalidrFactorDesc=Ange ett tal mellan 1 och 32 767.
#XMSG
emptyRecordNameValidationDescMsg=Om serialiseringsformatet "AVRO" används medges endast följande tecken:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(understreck)
#XMSG
validRecordNameValidationHeaderMsg=Ange ett giltigt namn på post.
#XMSG
validRecordNameValidationDescMsgUpdated=Eftersom serialiseringsformatet "AVRO" används får namnet på posten endast bestå av alfanumeriska tecken (A-Z, a-z, 0-9) och understreck (_). Det måste starta med en bokstav eller ett understreck.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled="Antal objekttrådar för deltainläsningar" kan ställas in så snart ett eller flera objekt har inläsningstypen "Initial och delta".
#XMSG
invalidTargetName=Ogiltigt kolumnnamn
#XMSG
invalidTargetNameDesc=Namnet på målkolumnen får endast bestå av alfanumeriska tecken (A-Z, a-z, 0-9) och understreck (_).
#XFLD
consumeOtherSchema=Förbruka andra schemaversioner
#XFLD
ignoreSchemamissmatch=Ignorera schema som inte matchar
#XFLD
confleuntDatatruncation=Fel vid datatrunkering
#XFLD
isolationLevel=Isoleringsnivå
#XFLD
confluentOffset=Startpunkt
#XFLD
signavioGroupDeltaFilesByText=Ingen
#XFLD
signavioFileFormatText=Parkett
#XFLD
signavioSparkCompatibilityParquetText=Nej
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=Nej

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Projektioner
#XBUT
txtAdd=Lägg till
#XBUT
txtEdit=Redigera
#XMSG
transformationText=Lägg till en projektion för att ställa in filtrering eller mappning.
#XMSG
primaryKeyRequiredText=Välj en primärnyckel med Konfigurera schema.
#XFLD
lblSettings=Inställningar
#XFLD
lblTargetSetting={0}: Målinställningar
#XMSG
@csvRF=Välj filen som innehåller den schemadefinition som du vill tillämpa på alla filer i mappen.
#XFLD
lblSourceColumns=Källkolumner
#XFLD
lblJsonStructure=JSON-struktur
#XFLD
lblSourceSetting={0}: Källinställningar
#XFLD
lblSourceSchemaSetting={0}: Källschemainställningar
#XBUT
messageSettings=Meddelandeinställningar
#XFLD
lblPropertyTitle1=Objektegenskaper
#XFLD
lblRFPropertyTitle=Egenskaper för replikeringsflöde
#XMSG
noDataTxt=Det finns inga kolumner att visa.
#XMSG
noTargetObjectText=Inget målobjekt har valts.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Målkolumner
#XMSG
searchColumns=Sök kolumner
#XTOL
cdcColumnTooltip=Kolumn för deltaregistrering
#XMSG
sourceNonDeltaSupportErrorUpdated=Källobjektet medger inte deltaregistrering.
#XMSG
targetCDCColumnAdded=Två målkolumner har lagts till för deltaregistrering.
#XMSG
deltaPartitionEnable=Objekttrådgräns för deltainläsningar har lagts till i källinställningar.
#XMSG
attributeMappingRemovalTxt=Tar bort ogiltiga mappningar som inte medges för nya målobjekt.
#XMSG
targetCDCColumnRemoved=Två målkolumner som används för deltaregistrering har tagits bort.
#XMSG
replicationLoadTypeChanged=Inläsningstyp har ändrats till "Initial och delta".
#XMSG
sourceHDLFLoadTypeError=Ändra inläsningstyp till "Initial och delta".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=För att replikera ett objekt från en källanslutning med anslutningstyp SAP HANA Cloud, datasjöfiler till SAP Datasphere måste du använda inläsningstypen "Initial och delta".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Aktivera deltaregistrering.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=För att replikera ett objekt från en källanslutning med anslutningstyp SAP HANA Cloud, datasjöfiler till SAP Datasphere måste du aktivera deltaregistrering.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Ändra målobjekt.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Målobjektet kan inte användas eftersom deltaregistrering har inaktiverats. Du kan antingen byta namn på målobjektet (vilket tillåter att ett nytt objekt med deltaregistrering skapas) eller mappa det till ett befintligt objekt som har aktiverat deltaregistrering.
#XMSG
deltaPartitionError=Ange ett giltigt antal objekttrådar för deltainläsningar.
#XMSG
deltaPartitionErrorDescription=Ange ett värde mellan 1 och 10.
#XMSG
deltaPartitionEmptyError=Ange ett antal objekttrådar för deltainläsningar.
#XFLD
@lblColumnDescription=Beskrivning
#XMSG
@lblColumnDescriptionText1=För tekniska ändamål – hantering av dubblettposter som orsakats av problem vid replikering av ABAP-baserade målobjekt som inte har en primärnyckel.
#XFLD
storageType=Lagring
#XFLD
skipUnmappedColLbl=Hoppa över ej mappade kolumner
#XFLD
abapContentTypeLbl=Innehållstyp
#XFLD
autoMergeForTargetLbl=Slå samman data automatiskt
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Allmänt
#XFLD
lblBusinessName=Affärsnamn
#XFLD
lblTechnicalName=Tekniskt namn
#XFLD
lblPackage=Paket
#XFLD
statusPanel=Körningsstatus
#XBTN: Schedule dropdown menu
SCHEDULE=Schema
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Redigera schema
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Radera schema
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Skapa schema
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Valideringskontroll för schema misslyckades
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Ett schema kan inte skapas eftersom replikeringsflödet för närvarande distribueras.{0}Vänta tills replikeringsflödet har distribuerats.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Inget schema kan skapas för replikeringsflöden som innehåller objekt med inläsningstypen "Initial och delta".
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Inget schema kan skapas för replikeringsflöden som innehåller objekt med inläsningstypen "Initial och delta/Endast delta".
#XFLD : Scheduled popover
SCHEDULED=Inplanerad
#XFLD
CREATE_REPLICATION_TEXT=Skapa ett replikeringsflöde
#XFLD
EDIT_REPLICATION_TEXT=Redigera ett replikeringsflöde
#XFLD
DELETE_REPLICATION_TEXT=Radera ett replikeringsflöde
#XFLD
REFRESH_FREQUENCY=Frekvens
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Replikeringsflödet kan inte distribueras eftersom befintligt schema{0} ännu inte medger inläsningstypen "Initial och delta".{0}{0}För att distribuera replikeringsflödet måste du sätta inläsningstypen för alla objekt{0} till "Endast initial". Alternativt kan du radera schemat, distribuera {0}replikeringsflödet och sedan starta en ny körning. Det leder till en körning utan {0}slut vilket också medger objekt med inläsningstypen "Initial och delta".
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Replikeringsflödet kan inte distribueras eftersom befintligt schema{0} ännu inte medger inläsningstypen "Initial och delta/Endast delta".{0}{0}För att distribuera replikeringsflödet måste du sätta inläsningstypen för alla objekt{0} till "Endast initial". Alternativt kan du radera schemat, distribuera {0}replikeringsflödet och sedan starta en ny körning. Det leder till en körning utan {0}slut vilket också medger objekt med inläsningstypen "Initial och delta/Endast delta".
#XMSG
SCHEDULE_EXCEPTION=Schemadetaljer kan inte hämtas
#XFLD: Label for frequency column
everyLabel=Var
#XFLD: Plural Recurrence text for Hour
hoursLabel=Timme
#XFLD: Plural Recurrence text for Day
daysLabel=Dag
#XFLD: Plural Recurrence text for Month
monthsLabel=Månad
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minut
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Information om schemamöjlighet kunde inte hämtas.
#XFLD :Paused field
PAUSED=Pausad
#XMSG
navToMonitoring=Öppna i flödesmonitorn
#XFLD
statusLbl=Status
#XFLD
lblLastRunExecuted=Start för senaste körning
#XFLD
lblLastExecuted=Senaste körning
#XFLD: Status text for Completed
statusCompleted=Slutförd
#XFLD: Status text for Running
statusRunning=Körs
#XFLD: Status text for Failed
statusFailed=Misslyckades
#XFLD: Status text for Stopped
statusStopped=Stoppad
#XFLD: Status text for Stopping
statusStopping=Stoppas
#XFLD: Status text for Active
statusActive=Aktiv
#XFLD: Status text for Paused
statusPaused=Pausad
#XFLD: Status text for not executed
lblNotExecuted=Ej utförd än
#XFLD
messagesSettings=Meddelandeinställningar
#XTOL
@validateModel=Valideringsmeddelanden
#XTOL
@hierarchy=Hierarki
#XTOL
@columnCount=Antal kolumner
#XMSG
VAL_PACKAGE_CHANGED=Du har allokerat objektet till paket "{1}". Klicka på Spara för att bekräfta och validera ändringen. Observera att allokering till ett paket inte kan ångras i den här editorn efter lagring.
#XMSG
MISSING_DEPENDENCY=Beroenden för objekt "{0}" kan inte åtgärdas i paket "{1}".
#XFLD
deltaLoadInterval=Deltainläsningsintervall
#XFLD
lblHour=Timmar (0–24)
#XFLD
lblMinutes=Minuter (0–59)
#XMSG
maxHourOrMinErr=Ange ett värde mellan 0 och {0}
#XMSG
maxDeltaInterval=Maximivärde för deltainläsningsintervall är 24 timmar.{0}Ändra värdet för minut eller timme.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Målsökväg för container
#XFLD
confluentSubjectName=Ämnesnamn
#XFLD
confluentSchemaVersion=Schemaversion
#XFLD
confluentIncludeTechKeyUpdated=Inkludera teknisk nyckel
#XFLD
confluentOmitNonExpandedArrays=Utelämna ej expanderade vektorer
#XFLD
confluentExpandArrayOrMap=Expandera vektor eller karta
#XCOL
confluentOperationMapping=Operationsmappning
#XCOL
confluentOpCode=Operationskod
#XFLD
confluentInsertOpCode=Infoga
#XFLD
confluentUpdateOpCode=Uppdatera
#XFLD
confluentDeleteOpCode=Radera
#XFLD
expandArrayOrMapNotSelectedTxt=Inte vald
#XFLD
confluentSwitchTxtYes=Ja
#XFLD
confluentSwitchTxtNo=Nej
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Fel
#XTIT
executeWarning=Varning
#XMSG
executeunsavederror=Spara ditt replikeringsflöde före utförande.
#XMSG
executemodifiederror=Det finns ändringar som inte sparats i replikeringsflödet. Spara replikeringsflödet.
#XMSG
executeundeployederror=Du måste distribuera replikeringsflödet innan du kan köra det.
#XMSG
executedeployingerror=Vänta tills distributionen slutförts.
#XMSG
msgRunStarted=Körning har startat
#XMSG
msgExecuteFail=Replikeringsflöde kunde inte utföras
#XMSG
titleExecuteBusy=Vänta.
#XMSG
msgExecuteBusy=Dina data förbereds för körning av replikeringsflödet.
#XTIT
executeConfirmDialog=Varning
#XMSG
msgExecuteWithValidations=Replikeringsflöde har valideringsfel. Utförande av replikeringsflödet kan leda till fel.
#XMSG
msgRunDeployedVersion=Det finns ändringar att distribuera. Den senast distribuerade versionen av replikeringsflödet utförs. Vill du fortsätta?
#XBUT
btnExecuteAnyway=Kör ändå
#XBUT
btnExecuteClose=Stäng
#XBUT
loaderClose=Stäng
#XTIT
loaderTitle=Läser in
#XMSG
loaderText=Hämtar detaljer från servern
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Replikeringsflöde till denna icke-SAP-målanslutning kan inte startas
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=eftersom det inte finns någon utgående volym för den här månaden.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=En administratör kan öka blocken för premium utgående för den här
#XMSG
premiumOutBoundRFAdminErrMsgPart2=tenanten vilket gör utgående volym tillgänglig för den här månaden.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Fel
#XTIT
deployInfo=Information
#XMSG
deployCheckFailException=Undantag uppstod vid distribution
#XMSG
deployGBQFFDisabled=Det går inte att distribuera replikeringsflöden med en målanslutning till Google BigQuery för tillfället eftersom vi utför underhåll av den här funktionen.
#XMSG
deployKAFKAFFDisabled=Det går inte att distribuera replikeringsflöden med en målanslutning till Apache Kafka för tillfället eftersom vi utför underhåll av den här funktionen.
#XMSG
deployConfluentDisabled=Det går inte att distribuera replikeringsflöden med en målanslutning till Confluent Kafka för tillfället eftersom vi utför underhåll av den här funktionen.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Namn på deltaregistreringstabeller används redan av andra tabeller i repository för följande målobjekt: {0} Du måste byta namn på dessa målobjekt för att säkerställa att associerade namn på deltaregistreringstabeller är unika innan du kan distribuera replikeringsflödet.
#XMSG
deployDWCSourceFFDisabled=Det går för tillfället inte att distribuera replikeringsflöden som har SAP Datasphere som sin källa eftersom vi utför underhåll av den här funktionen.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Det går för tillfället inte att distribuera replikeringsflöden som innehåller deltaaktiverade lokala tabeller som källobjekt eftersom vi utför underhåll av den här funktionen.
#XMSG
deployHDLFSourceFFDisabled=Det går för tillfället inte att distribuera replikeringsflöden som har källanslutningar med anslutningstyp SAP HANA Cloud, datasjöfiler eftersom vi utför underhåll.
#XMSG
deployObjectStoreAsSourceFFDisabled=Det går för tillfället inte att distribuera replikeringsflöden som har en molnlagringsleverantör som källa.
#XMSG
deployConfluentSourceFFDisabled=Det går för tillfället inte att distribuera replikeringsflöden som har Confluent Kafka som sin källa eftersom vi utför underhåll av den här funktionen.
#XMSG
deployMaxDWCNewTableCrossed=Stora replikeringsflöden kan inte sparas och distribueras samtidigt. Spara replikeringsflödet först och distribuera det sedan.
#XMSG
deployInProgressInfo=Distribution pågår redan.
#XMSG
deploySourceObjectInUse=Källobjekt {0} används redan i replikeringsflöden {1}.
#XMSG
deployTargetSourceObjectInUse=Källobjekt {0} används redan i replikeringsflöden {1}. Målobjekt {2} används redan i replikeringsflöden {3}.
#XMSG
deployReplicationFlowCheckError=Fel vid verifiering av replikeringsflöde: {0}
#XMSG
preDeployTargetObjectInUse=Målobjekt {0} används redan i replikeringsflöden {1} och du kan inte ha samma målobjekt i två olika replikeringsflöden. Välj ett annat målobjekt och försök igen.
#XMSG
runInProgressInfo=Replikeringsflöde körs redan.
#XMSG
deploySignavioTargetFFDisabled=Det går för tillfället inte att distribuera replikeringsflöden som har SAP Signavio som mål eftersom vi utför underhåll av den här funktionen.
#XMSG
deployHanaViewAsSourceFFDisabled=Det går för tillfället inte att distribuera replikeringsflöden som har vyer som källobjekt för vald källanslutning. Försök igen senare.
#XMSG
deployMsOneLakeTargetFFDisabled=Det går för tillfället inte att distribuera replikeringsflöden som har MS OneLake som mål eftersom vi utför underhåll av den här funktionen.
#XMSG
deploySFTPTargetFFDisabled=Det går för tillfället inte att distribuera replikeringsflöden som har SFTP som mål eftersom vi utför underhåll av den här funktionen.
#XMSG
deploySFTPSourceFFDisabled=Det går för tillfället inte att distribuera replikeringsflöden som har SFTP som källa eftersom vi utför underhåll av den här funktionen.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Tekniskt namn
#XFLD
businessNameInRenameTarget=Affärsnamn
#XTOL
renametargetDialogTitle=Byt namn på målobjekt
#XBUT
targetRenameButton=Byt namn
#XBUT
targetRenameCancel=Avbryt
#XMSG
mandatoryTargetName=Du måste ange ett namn.
#XMSG
dwcSpecialChar=_(underscore) är det enda specialtecken som tillåts.
#XMSG
dwcWithDot=Måltabellnamn kan bestå av latinska bokstäver, siffror, understreck (_) och punkter (.). Första tecknet måste vara en bokstav, en siffra eller ett understreck (inte en punkt).
#XMSG
nonDwcSpecialChar=Tillåtna specialtecken är _ (understreck), - (bindestreck) och . (punkt)
#XMSG
firstUnderscorePattern=Namn får inte börja med _ (understreck)

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: Visa SQL-instruktion för skapa tabell
#XMSG
sqlDialogMaxPKWarning=I Google BigQuery medges maximalt 16 primärnycklar och källobjektet har fler. Därför definieras inga primärnycklar i den här instruktionen.
#XMSG
sqlDialogIncomptiblePKTypeWarning=En eller flera källkolumner har datatyper som inte kan definieras som primärnycklar i Google BigQuery. Därför har inga primärnycklar definierats i det här fallet. I Google BigQuery kan endast följande datatyper ha en primärnyckel: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Kopiera och stäng
#XBUT
closeDDL=Stäng
#XMSG
copiedToClipboard=Kopierad till urklipp


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Objekt ''{0}'' kan inte vara en del av en uppgiftskedja eftersom den inte har ett slut (då den innehåller objekt med inläsningstyp Initial och delta/Endast delta).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Objekt ''{0}'' kan inte vara en del av en uppgiftskedja eftersom den inte har ett slut (då den innehåller objekt med inläsningstyp Initial och delta).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objekt "{0}" kan inte läggas till i uppgiftskedja.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Det finns målobjekt som inte sparats. Spara igen.{0}{0} Beteendet för den här funktionen har ändrats: Tidigare skapades målobjekt endast i målmiljön när replikeringsflödet distribuerades.{0} Nu skapas objekten redan när replikeringsflödet sparas. Ditt replikeringsflöde skapades före denna ändring och innehåller nya objekt.{0} Du måste spara replikeringsflödet igen innan du distribuerar det så att de nya objekten inkluderas korrekt.
#XMSG
confirmChangeContentTypeMessage=Du är på väg att ändra innehållstypen. Om du gör det kommer alla befintliga projektioner att raderas.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Ämnesnamn
#XFLD
schemaDialogVersionName=Schemaversion
#XFLD
includeTechKey=Inkludera teknisk nyckel
#XFLD
segementButtonFlat=Platt
#XFLD
segementButtonNested=Kapslad
#XMSG
subjectNamePlaceholder=Sök ämnesnamn

#XMSG
@EmailNotificationSuccess=Konfiguration av körtids-e-postaviseringar har sparats.

#XFLD
@RuntimeEmailNotification=Körtids-e-postavisering

#XBTN
@TXT_SAVE=Spara


