#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Ток репликације

#XFLD: Edit Schema button text
editSchema=Уреди шему

#XTIT : Properties heading
configSchema=Конфигуриши шему

#XFLD: save changed button text
applyChanges=Примени промене


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Одабери изворну везу
#XFLD
sourceContainernEmptyText=Одабери спремник
#XFLD
targetConnectionEmptyText=Одабери циљну везу
#XFLD
targetContainernEmptyText=Одабери спремник
#XFLD
sourceSelectObjectText=Одабери изворни објекат
#XFLD
sourceObjectCount=Изворни објекти ({0})
#XFLD
targetObjectText=Циљни објекти
#XFLD
confluentBrowseContext=Одабери контекст
#XBUT
@retry=Покушај поново
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Надоградња клијента је у току.

#XTOL
browseSourceConnection=Претражи изворну везу
#XTOL
browseTargetConnection=Претражи циљну везу
#XTOL
browseSourceContainer=Претражи изворни спремник
#XTOL
browseAndAddSourceDataset=Додај изворне објекте
#XTOL
browseTargetContainer=Претражи циљни спремник
#XTOL
browseTargetSetting=Претражи циљна подешавања
#XTOL
browseSourceSetting=Претражи изворна подешавања
#XTOL
sourceDatasetInfo=Информације
#XTOL
sourceDatasetRemove=Уклони
#XTOL
mappingCount=Представља укупни број пресликавања/израза који нису засновани на имену.
#XTOL
filterCount=Представља укупни број услова филтера.
#XTOL
loading=Учитавање...
#XCOL
deltaCapture=Делта снимање
#XCOL
deltaCaptureTableName=Табела делта снимања
#XCOL
loadType=Учитај тип
#XCOL
deleteAllBeforeLoading=Избриши све пре учитавања
#XCOL
transformationsTab=Пројекције
#XCOL
settingsTab=Подешавања

#XBUT
renameTargetObjectBtn=Преименуј циљни објекат
#XBUT
mapToExistingTargetObjectBtn=Пресликај у постојећи циљни објекат
#XBUT
changeContainerPathBtn=Промени пут спремника
#XBUT
viewSQLDDLUpdated=Прикажи SQL изјаву Креирај табелу
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Изворни објекат не подржава делта снимање, али одабрани циљни објекат има активирану опцију делта снимања.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Није могуће користити циљни објекат јер је активирано делта снимање,{0}док изворни објекат не подржава делта снимање.{1}Можете да одаберете други циљни објекат који не подржава делта снимање.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Циљни објекат са овим називом већ постоји. Међутим, не може се користити{0}јер је делта снимање активирано, док изворни објекат не{0}подржава делта снимање.{1}Можете да унесете назив постојећег циљног објекта који не{0}подржава делта снимање или назив који још не постоји.
#XBUT
copySQLDDLUpdated=Копирај SQL изјаву Креирај табелу
#XMSG
targetObjExistingNoCDCColumnUpdated=Постојеће табеле у Google BigQuery-ју морају да укључују следеће колоне за снимање података промене (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Следећи изворни објекти нису подржани јер немају примарни кључ или користе везу која не задовољава услове за позивање примарног кључа:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Потражите евентуално решење у чланку базе знања SAP KBA 3531135.
#XLST: load type list values
initial=Само почетно
@emailUpdateError=Грешка у ажурирању листе обавештења е-поштом

#XLST
initialDelta=Почетно и делта

#XLST
deltaOnly=Само делта
#XMSG
confluentDeltaLoadTypeInfo=За извор Confluent Kafka подржани су само типови учитавања Почетно и Делта.
#XMSG
confirmRemoveReplicationObject=Да ли потврђујете да желите да избришете репликацију?
#XMSG
confirmRemoveReplicationTaskPrompt=Ова радња ће избрисати постојеће репликације. Да ли желите да наставите?
#XMSG
confirmTargetConnectionChangePrompt=Ова радња ће поново поставити циљну везу, циљни спремник и избрисати све циљне објекте. Да ли желите да наставите?
#XMSG
confirmTargetContainerChangePrompt=Ова радња ће поново поставити циљни спремник и избрисати све постојеће циљне објекте. Да ли желите да наставите?
#XMSG
confirmRemoveTransformObject=Да ли потврђујете да желите да избришете пројекцију {0}?
#XMSG
ErrorMsgContainerChange=Грешка при промени пута спремника.
#XMSG
infoForUnsupportedDatasetNoKeys=Следећи изворни објекти нису подржани јер немају примарни кључ:
#XMSG
infoForUnsupportedDatasetView=Следећи изворни објекти типа Погледи нису подржани:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Следећи изворни објекат није подржан јер представља поглед SQL који садржи параметре уноса:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Следећи изворни објекти нису подржани јер је екстраховање за њих деактивирано:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=За везе с платформом Confluent једини дозвољени формати серијализације су AVRO и JSON. Следећи објекти нису подржани јер користе други формат серијализације:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Није могуће позвати шему за следеће објекте. Одаберите одговарајући контекст или верификујте конфигурацију регистрације шеме
#XTOL: warning dialog header on deleting replication task
deleteHeader=Избриши
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Подешавање Избриши све пре учитавања није подржано за Google BigQuery.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Подешавање Избриши све пре учитавања брише и поново креира објекат (тему) пре сваке репликације. Ово такође брише све додељене поруке.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Подешавање Избриши све пре учитавања није подржано за овај тип циља.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Технички назив
#XCOL
connBusinessName=Пословни назив
#XCOL
connDescriptionName=Опис
#XCOL
connType=Тип
#XMSG
connTblNoDataFoundtxt=Везе нису нађене
#XMSG
connectionError=Грешка при позивању веза.
#XMSG
connectionCombinationUnsupportedErrorTitle=Комбинација везе није подржана
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Репликација из {0} у {1} тренутно није подржана.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Комбинација типа везе није подржана
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Репликација из везе с типом везе SAP HANA Cloud, фајлови језера података у {0} није подржана. Можете реплицирати само у SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Одабери
#XBUT
containerCancelBtn=Одустани
#XTOL
containerSelectTooltip=Одабери
#XTOL
containerCancelTooltip=Одустани
#XMSG
containerContainerPathPlcHold=Пут спремника
#XFLD
containerContainertxt=Спремник
#XFLD
confluentContainerContainertxt=Контекст
#XMSG
infoMessageForSLTSelection=Само /SLT/ID масовног преноса је дозвољен као спремник. Одаберите ID масовног преноса у оквиру SLT (ако постоји) и кликните на Поднеси.
#XMSG
msgFetchContainerFail=Грешка при позивању података спремника.
#XMSG
infoMessageForSLTHidden=Ова веза не подржава SLT фолдере па се они не појављују на листи у наставку.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Одаберите спремник који у себи садржи потфолдере.
#XMSG
sftpIncludeSubFolderText=Нетачно
#XMSG
sftpIncludeSubFolderTextNew=Не

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Још нема пресликавања филтера)
#XMSG
failToFetchRemoteMetadata=Грешка при позивању метаподатака.
#XMSG
failToFetchData=Грешка при позивању постојећег циља.
#XCOL
@loadType=Учитај тип
#XCOL
@deleteAllBeforeLoading=Избриши све пре учитавања

#XMSG
@loading=Учитавање...
#XFLD
@selectSourceObjects=Одабери изворне објекте
#XMSG
@exceedLimit=Не можете увести више од {0} објек(а)та истовремено. Поништите одабир најмање {1} објек(а)та.
#XFLD
@objects=Објекти
#XBUT
@ok=ОК
#XBUT
@cancel=Одустани
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Следеће
#XBUT
btnAddSelection=Додај одабир
#XTOL
@remoteFromSelection=Уклони из одабира
#XMSG
@searchInForSearchField=Тражи у {0}

#XCOL
@name=Технички назив
#XCOL
@type=Тип
#XCOL
@location=Локација
#XCOL
@label=Пословни назив
#XCOL
@status=Статус

#XFLD
@searchIn=Тражи у:
#XBUT
@available=Доступно
#XBUT
@selection=Одабир

#XFLD
@noSourceSubFolder=Табеле и погледи
#XMSG
@alreadyAdded=Већ се налази у дијаграму
#XMSG
@askForFilter=Постоји више од {0} ставки. Унесите низ филтера да бисте смањили број ставки.
#XFLD: success label
lblSuccess=Успех
#XFLD: ready label
lblReady=Спремно
#XFLD: failure label
lblFailed=Није успело
#XFLD: fetching status label
lblFetchingDetail=Позивање детаља

#XMSG Place holder text for tree filter control
filterPlaceHolder=Упишите текст да бисте филтрирали објекте највишег нивоа
#XMSG Place holder text for server search control
serverSearchPlaceholder=Упишите и притисните Enter да бисте тражили
#XMSG
@deployObjects=Увоз {0} објеката...
#XMSG
@deployObjectsStatus=Број објеката који су увезени: {0}. Број објеката који се не могу увести: {1}

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Није успело отварање локалног претраживача репозиторијума.
#XMSG
@openRemoteSourceBrowserError=Није успело позивање изворних објеката.
#XMSG
@openRemoteTargetBrowserError=Није успело позивање циљних објеката.
#XMSG
@validatingTargetsError=Грешка при валидацији циљева.
#XMSG
@waitingToImport=Спремно за увоз

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Прекорачен је максимални број објеката. Одаберите највише 500 објеката за један ток репликације.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Технички назив
#XFLD
sourceObjectBusinessName=Пословни назив
#XFLD
sourceNoColumns=Број колона
#XFLD
containerLbl=Спремник

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Морате одабрати изворну везу за ток репликације.
#XMSG
validationSourceContainerNonExist=Морате одабрати спремник за изворну везу.
#XMSG
validationTargetNonExist=Морате одабрати циљну везу за ток репликације.
#XMSG
validationTargetContainerNonExist=Морате одабрати спремник за циљну везу.
#XMSG
validationTruncateDisabledForObjectTitle=Репликација у складишта објекта.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Репликација у складиште у облаку је могућа само ако је опција Избриши све пре учитавања постављена или ако циљни објекат не постоји на циљу.{0}{0} Да бисте ипак активирали репликацију за објекте за које опција Избриши све пре учитавања није постављена, побрините се да циљни објекат не постоји у систему пре него што покренете ток репликације.
#XMSG
validationTaskNonExist=Морате имати најмање једну репликацију за ток репликације.
#XMSG
validationTaskTargetMissing=Морате имати циљ за репликацију са извором: {0}
#XMSG
validationTaskTargetIsSAC=Одабрани циљ је SAC артефакт: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Одабрани циљ није подржана локална табела: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Објекат са овим називом већ постоји у циљу. Међутим, овај објекат се не може користити као циљни објекат за ток репликације у локални репозиторијум јер није локална табела.
#XMSG
validateSourceTargetSystemDifference=Морате одабрати другу изворну и циљну везу и комбинације спремника за ток репликације.
#XMSG
validateDuplicateSources=једна или више репликација имају двоструке називе изворног објекта: {0}.
#XMSG
validateDuplicateTargets=једна или више репликација имају двоструке називе циљног објекта: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=Изворни објекат {0} не подржава делта снимање, а циљни објекат {1} га подржава. Морате уклонити репликацију.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Морате да одаберете тип учитавања "Почетно и делта" за репликацију с називом циљног објекта {0}.
#XMSG
validationAutoRenameTarget=Циљне колоне су преименоване.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Додата је аутоматска пројекција, а следеће циљне колоне су преименоване да би се дозволила репликација у циљ:{1}{1} {0} {1}{1}Ово је због неког од следећих разлога:{1}{1}{2} Неподржани знакови{1}{2} Резервисани префикс
#XMSG
validationAutoRenameTargetDescriptionUpdated=Додата је аутоматска пројекција, а следеће циљне колоне су преименоване како би се омогућила репликација у Google BigQuery:{1}{1} {0} {1}{1}То је због једног од следећих разлога:{1}{1}{2} Назив колоне је резервисан{1}{2} Знакови нису подржани{1}{2} Префикс је резервисан
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Додата је аутоматска пројекција, а следеће циљне колоне су преименоване како би се омогућиле репликације у Confluent:{1}{1} {0} {1}{1}То је због једног од следећих разлога:{1}{1}{2} Назив колоне је резервисан{1}{2} Знакови нису подржани{1}{2} Префикс је резервисан
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Додата је аутоматска пројекција, а следеће циљне колоне су преименоване како би се омогућиле репликације у циљ:{1}{1} {0} {1}{1}То је због једног од следећих разлога:{1}{1}{2} Назив колоне је резервисан{1}{2} Знакови нису подржани{1}{2} Префикс је резервисан
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Циљни објекат је преименован.
#XMSG
autoRenameInfoDesc=Циљни објекат је преименован јер је садржао неподржане знакове. Подржани су само следећи знакови:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(тачка){0}{1}_(доња црта){0}{1}-(цртица)
#XMSG
validationAutoTargetTypeConversion=Циљни типови података су промењени.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=За следеће циљне колоне, промењени су циљни типови података, јер изворни типови података нису подржани у Google BigQuery-ју:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=За следеће циљне колоне промењени су циљни типови података јер изворни типови података нису подржани у циљној вези:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Скратите називе циљне колоне.
#XMSG
validationMaxCharLengthGBQTargetDescription=У Google BigQuery-ју, називи колоне могу да користе максимално 300 знакова. Користите пројекцију да скратите следеће називе циљне колоне:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Није могуће креирати примарне кључеве.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=У Google BigQuery-ју, подржано је максимално 16 примарних кључева, али изворни објекат има већи број примарних кључева. Ниједан примарни кључ неће бити креиран у циљном објекту.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Једна или више изворних колона имају типове података који се не могу дефинисати као примарни кључеви у Google BigQuery-ју. Ниједан примарни кључ се неће креирати у циљном објекту.{0}{0}Следећи циљни типови података су компатибилни са Google BigQuery типовима података за које се примарни кључ може дефинисати: {0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Дефинишите једну колону или више њих као примарни кључ.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Морате дефинисати једну колону или више њих као примарни кључ. За то користите дијалог изворне шеме.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Дефинишите једну колону или више њих као примарни кључ.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Морате дефинисати једну колону или више њих као примарни кључ које одговарају ограничењима примарног кључа за ваш изворни објекат. У ту сврху идите на Конфигуриши шему у својствима изворног објекта.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Унесите максималну вредност партиције.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Максимална вредност партиције мора бити ≥ 1 и ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Дефинишите једну колону или више њих као примарни кључ.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Да бисте реплицирали објекат, морате да дефинишете једну колону или више њих као примарни кључ. Користите пројекцију за ово.
#XMSG
validateHDLFNoPKExistingDatasetError=Дефинишите једну колону или више њих као примарни кључ.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Да би се реплицирали подаци у постојећи циљни објекат, једна колона или више њих мора се дефинисати као примарни кључ. {0} Имате следеће опције за дефинисање једне колоне или више њих као примарни кључ: {0} {1} Користите уређивач локалне табеле за промену постојећег циљног објекта. Затим поново учитајте ток репликације.{0}{1} Преименујте циљни објекат у току репликације. Тиме ће се креирати нови објекат чим се извођење покрене. Након преименовања, можете дефинисати једну колону или више њих као примарни кључ у пројекцији.{0}{1} Пресликајте објекат у други постојећи циљни објекат у којем је једна колона или више њих већ дефинисано као примарни кључ.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Одабрани циљ већ постоји у репозиторијуму: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Називе табеле делта снимања већ користе друге табеле у репозиторијуму: {0} Морате да преименујете те циљне објекте да бисте обезбедили да повезани називи табеле делта снимања буду јединствени пре него што сачувате ток репликације.
#XMSG
validateConfluentEmptySchema=Дефиниши шему
#XMSG
validateConfluentEmptySchemaDescUpdated=Изворна табела нема шему. Изаберите Конфигуриши шему да бисте је дефинисали
#XMSG
validationCSVEncoding=Неважеће шифровање CSV
#XMSG
validationCSVEncodingDescription=Шифровање CSV задатка није важеће.
#XMSG
validateConfluentEmptySchema=Одаберите компатибилни тип циљних података
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Одаберите компатибилни тип циљних података
#XMSG
globalValidateTargetDataTypeDesc=Грешка при пресликавањима колоне. Идите на Пројекцију и уверите се да су све изворне колоне пресликане с јединственом колоном, с колоном која има компатибилан тип података, као и да су сви дефинисани изрази важећи.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Дупликати назива колона.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Дупликати назива колона нису подржани. Користите дијалог пројекције да их поправите. Следећи циљни објекти имају дупликате назива колона: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Дупликати назива колона.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Дупликати назива колона нису подржани. Следећи циљни објекти имају дупликате назива колона: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Могуће су недоследности у подацима.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Тип учитавања Само делта неће узети у обзир промене извршене у извору између последњег снимања и следећег извођења.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Промените тип учитавања у "Почетно".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Репликација објеката заснованих на ABAP-у који немају примарни кључ могуће је само за тип учитавања "Само почетно".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Деактивирај делта снимање.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Да бисте реплицирали објекат који нема примарни кључ користећи тип изворне везе ABAP, прво морате да деактивирате делта снимање за ову табелу.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Промени циљни објекат.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Није могуће користити циљни објекат јер је активирано делта снимање. Можете да преименујете циљни објекат, а затим искључите делта снимање за нови (преименовани) објекат или да пресликате изворни објекат у циљни објекат за који је делта снимање деактивирано.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Промени циљни објекат.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Није могуће користити циљни објекат јер нема потребну техничку колону __load_package_id. Можете да преименујете циљни објекат користећи назив који још не постоји. Систем онда креира нови објекат који има исту дефиницију као изворни објекат и садржи техничку колону. У супротном, можете да пресликате циљни објекат у постојећи објекат који има потребну техничку колону (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Промени циљни објекат.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Није могуће користити циљни објекат јер нема потребну техничку колону __load_record_id. Можете да преименујете циљни објекат користећи назив који још не постоји. Систем онда креира нови објекат који има исту дефиницију као изворни објекат и садржи техничку колону. У супротном, можете да пресликате циљни објекат у постојећи објекат који има потребну техничку колону (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Промени циљни објекат.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Није могуће користити циљни објекат јер тип података техничке колоне __load_record_id није "string(44)". Можете да преименујете циљни објекат користећи назив који још не постоји. Систем онда креира нови објекат који има исту дефиницију као изворни објекат и последично исправни тип података. У супротном, можете да пресликате циљни објекат у постојећи објекат који има потребну техничку колону (__load_record_id) са исправним типом података.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Промени циљни објекат.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Није могуће користити циљни објекат јер има примарни кључ, а изворни објекат га нема. Можете да преименујете циљни објекат користећи назив који још не постоји. Систем онда креира нови објекат који има исту дефиницију као изворни објекат и последично нема примарни кључ.  У супротном, можете да пресликате циљни објекат у постојећи објекат који има потребну техничку колону (__load_package_id) и нема примарни кључ.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Промени циљни објекат.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Није могуће користити циљни објекат јер има примарни кључ, а изворни објекат га нема. Можете да преименујете циљни објекат користећи назив који још не постоји. Систем онда креира нови објекат који има исту дефиницију као изворни објекат и последично нема примарни кључ. У супротном, можете да пресликате циљни објекат у постојећи објекат који има потребну техничку колону (__load_record_id) и нема примарни кључ.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Промени циљни објекат.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Није могуће користити циљни објекат јер тип података техничке колоне __load_package_id није "binary(>=256)". Можете да преименујете циљни објекат користећи назив који још не постоји. Систем онда креира нови објекат који има исту дефиницију као изворни објекат и последично исправни тип података. У супротном, можете да пресликате циљни објекат у постојећи објекат који има потребну техничку колону (__load_package_id) са исправним типом података.
#XMSG
validationAutoRenameTargetDPID=Циљне колоне су преименоване.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Уклоните изворни објекат.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Изворни објекат нема колону кључа, што није подржано у овом контексту.
#XMSG
validationAutoRenameTargetDPIDDescription=Аутоматска пројекција је додата, а следеће циљне колоне су преименоване тако да дозвољавају репликацију из ABAP извора без кључева :{1}{1} {0} {1}{1}Ово је због једног од следећих разлога:{1}{1}{2} Резервисани назив колоне{1}{2} Неподржани знакови{1}{2} Резервисани префикс
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Репликација у {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Снимање и имплементација токова репликације који имају {0} као циљ тренутно није могућа јер вршимо одржавање ове функције.
#XMSG
TargetColumnSkippedLTF=Циљна колона је прескочена.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Циљна колона је прескочена због неподржаног типа података. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Колона времена као примарни кључ.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Изворни објекат има колону времена као примарни кључ, што није подржано у овом контексту.
#XMSG
validateNoPKInLTFTarget=Недостаје примарни кључ.
#XMSG
validateNoPKInLTFTargetDescription=Примарни кључ није дефинисан у циљу, што није подржано у овом контексту.
#XMSG
validateABAPClusterTableLTF=Табела ABAP скупа.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Изворни објекат је табела ABAP скупа. што није подржано у овом контексту.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Изгледа да још нисте додали податке.
#YINS
welcomeText2=За покретање вашег тока репликације одаберите везу и изворни објекат на левој страни.

#XBUT
wizStep1=Одабери изворну везу
#XBUT
wizStep2=Одабери изворни спремник
#XBUT
wizStep3=Додај изворне објекте

#XMSG
limitDataset=Достигнут је максимални број објеката. Уклоните постојеће објекте да бисте додали нове или креирајте нови ток репликације.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Ток репликације за ову циљну везу која није SAP није могуће покренути зато што излазни обим није доступан за овај месец.
#XMSG
premiumOutBoundRFAdminWarningMsg=Администратор може да повећа излазне блокове Premium за овог закупца и излазни обим учини доступним за овај месец.
#XMSG
messageForToastForDPIDColumn2=Нова колона је додата циљном објекту за {0} објеката - потребно за управљање двоструким записима у вези са изворним објектима заснованим на ABAP-у који немају примарни кључ.
#XMSG
PremiumInboundWarningMessage=У зависности од броја токова репликације и обима података за репликацију, SAP HANA ресурси{0}обавезни за репликацију података путем {1} могу прекорачити доступни капацитет за ваш клијент.
#XMSG
PremiumInboundWarningMsg=У зависности од броја токова репликације и обима података за репликацију, SAP HANA ресурси{0}обавезни за репликацију података путем "{1}" могу прекорачити доступни капацитет за ваш клијент.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Унесите назив пројекције.
#XMSG
emptyTargetColumn=Унесите назив циљне колоне.
#XMSG
emptyTargetColumnBusinessName=Попуните циљну колону Пословни назив.
#XMSG
invalidTransformName=Унесите назив пројекције.
#XMSG
uniqueColumnName=Преименуј циљну колону.
#XMSG
copySourceColumnLbl=Копирај колоне из изворног објекта
#XMSG
renameWarning=Обавезно изаберите јединствени назив приликом промене назива циљне табеле. Ако табела с новим називом већ постоји у простору, користиће дефиницију те табеле.

#XMSG
uniqueColumnBusinessName=Преименујте пословни назив циљне колоне.
#XMSG
uniqueSourceMapping=Одаберите другу изворну колону.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=Изворну колону {0} већ користе следеће циљне колоне:{1}{1}{2}{1}{1} За ову циљну колону или за друге циљне колоне одаберите изворну колону која није већ у употреби да бисте сачували пројекцију.
#XMSG
uniqueColumnNameDescription=Назив циљне колоне који сте унели већ постоји. Да бисте могли да сачувате пројекцију, морате да унесете јединствени назив колоне.
#XMSG
uniqueColumnBusinessNameDesc=Пословни назив циљне колоне већ постоји. Да бисте сачували пројекцију морате унети јединствени пословни назив колоне.
#XMSG
emptySource=Одаберите изворну колону или унесите константу.
#XMSG
emptySourceDescription=Да бисте креирали важећи унос пресликавања, потребно је да одаберете изворну колону или унесете вредност константе.
#XMSG
emptyExpression=Дефинишите пресликавање.
#XMSG
emptyExpressionDescription1=Одаберите или изворну колону у коју желите да пресликате циљну колону или одаберите квадратић за потврду у колони {0} Функције / Константе {1}. {2} {2} Функције се уносе аутоматски у складу с типом циљних података. Константне вредности се могу унети ручно.
#XMSG
numberExpressionErr=Унесите број.
#XMSG
numberExpressionErrDescription=Одабрали сте нумерички тип података. То значи да можете уносити само бројеве, плус децимални зарез ако је применљиво. Немојте користити полунаводнике.
#XMSG
invalidLength=Унесите важећу вредност дужине.
#XMSG
invalidLengthDescription=Дужина типа података мора бити једнака или већа од дужине изворне колоне и може бити између 1 и 5000.
#XMSG
invalidMappedLength=Унесите важећу вредност дужине.
#XMSG
invalidMappedLengthDescription=Дужина типа података мора бити једнака или већа од дужине изворне колоне {0} и може бити између 1 и 5000.
#XMSG
invalidPrecision=Унесите важећу вредност прецизности.
#XMSG
invalidPrecisionDescription=Прецизност дефинише укупни број цифара. Скала дефинише број цифара након децималног зареза и може бити између 0 и прецизности.{0}{0} Примери: {0}{1} Прецизност 6, скала 2 одговара бројевима као што је 1234,56.{0}{1} Прецизност 6, скала 6 одговара бројевима као што је 0,123546.{0} {0} Прецизност и скала за циљни број морају бити компатибилни с прецизношћу и скалом за изворни број тако да све цифре изворног броја могу да стану у циљно поље. На пример, ако имате прецизност 6 и скалу 2 у изворном броју (и стога цифре које нису 0 пре децималног зареза), не можете имати прецизност 6 и скалу 6 у циљном броју.
#XMSG
invalidPrimaryKey=Унесите најмање један примарни кључ.
#XMSG
invalidPrimaryKeyDescription=Примарни кључ није дефинисан за ову шему.
#XMSG
invalidMappedPrecision=Унесите важећу вредност прецизности.
#XMSG
invalidMappedPrecisionDescription1=Прецизност дефинише укупни број цифара. Скала дефинише број цифара након децималног зареза и може бити између 0 и прецизности.{0}{0} Примери: {0}{1} Прецизност 6, скала 2 одговара бројевима као што је 1234,56.{0}{1} Прецизност 6, скала 6 одговара бројевима као што је 0,123546.{0}{0}Прецизност типа података мора бити једнака или већа од прецизности изворног броја ({2}).
#XMSG
invalidScale=Унесите важећу вредност скале.
#XMSG
invalidScaleDescription=Прецизност дефинише укупни број цифара. Скала дефинише број цифара након децималног зареза и може бити између 0 и прецизности.{0}{0} Примери: {0}{1} Прецизност 6, скала 2 одговара бројевима као што је 1234,56.{0}{1} Прецизност 6, скала 6 одговара бројевима као што је 0,123546.{0} {0} Прецизност и скала за циљни број морају бити компатибилни с прецизношћу и скалом за изворни број тако да све цифре изворног броја могу да стану у циљно поље. На пример, ако имате прецизност 6 и скалу 2 у изворном броју (и стога цифре које нису 0 пре децималног зареза), не можете имати прецизност 6 и скалу 6 у циљном броју.
#XMSG
invalidMappedScale=Унесите важећу вредност скале.
#XMSG
invalidMappedScaleDescription1=Прецизност дефинише укупни број цифара. Скала дефинише број цифара након децималног зареза и може бити између 0 и прецизности.{0}{0} Примери: {0}{1} Прецизност 6, скала 2 одговара бројевима као што је 1234,56.{0}{1} Прецизност 6, скала 6 одговара бројевима као што је 0,123546.{0}{0} Скала типа података мора бити једнака скали извора или већа од ње ({2}).
#XMSG
nonCompatibleDataType=Одаберите компатибилни циљни тип података.
#XMSG
nonCompatibleDataTypeDescription1=Тип података који овде наведете мора бити компатибилан са изворним типом података ({0}). {1}{1} Пример: ако изворна колона има тип података Низ и садржи слова, не можете користити децимални тип података за циљну колону.
#XMSG
invalidColumnCount=Одаберите изворну колону.
#XMSG
ObjectStoreInvalidScaleORPrecision=Унесите важећу вредност за прецизност и скалу.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=Прва вредност је прецизност, која дефинише укупни број цифара. Друга вредност је скала, која дефинише цифре након децималне тачке. Унесите циљну вредност скале која је већа од изворне вредности скале и уверите се да је разлика између унете циљне вредности скале и вредности прецизности већа од разлике између изворне вредности скале и вредности прецизности.
#XMSG
InvalidPrecisionORScale=Унесите важећу вредност за прецизност и скалу.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=Прва вредност је прецизност која дефинише укупни број цифара. Друга вредност је скала која дефинише цифре након децималног зареза.{0}{0}Пошто изворни тип података није подржан у Google BigQuery-ју, конвертује се у циљни тип података DECIMAL. У овом случају, прецизност се може дефинисати само између 38 и 76, а скала између 9 и 38. Осим тога, резултат прецизности умањене за скалу који представља цифре пре децималног зареза, мора бити између 29 и 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=Прва вредност је прецизност која дефинише укупни број цифара. Друга вредност је скала која дефинише цифре након децималног зареза.{0}{0}Пошто изворни тип података није подржан у Google BigQuery-ју, конвертује се у циљни тип података DECIMAL. У том случају, прецизност се мора дефинисати као 20 или више. Осим тога, резултат прецизности умањене за скалу који представља цифре пре децималног зареза, мора бити 20 или више.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=Прва вредност је прецизност, која дефинише укупни број цифара. Друга вредност је скала, која дефинише цифре након децималног зареза.{0}{0}Пошто изворни тип података није подржан у циљу, конвертује се у циљни тип података DECIMAL. У овом случају, прецизност се мора дефинисати било којим бројем већим од 1 или једнаким 1 и мањим од 38 или једнаким 38, а скала је мања од прецизности или јој је једнака.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=Прва вредност је прецизност, која дефинише укупни број цифара. Друга вредност је скала, која дефинише цифре након децималног зареза.{0}{0}Пошто изворни тип података није подржан у циљу, конвертује се у циљни тип података DECIMAL. У овом случају, прецизност мора бити дефинисана као 20 или већа. Осим тога, резултат разлике прецизности и скале, која одражава цифре пре децималног зареза, мора бити 20 или већа.
#XMSG
invalidColumnCountDescription=Да бисте креирали важећи унос пресликавања, потребно је да одаберете изворну колону или унесете вредност константе.
#XMSG
duplicateColumns=Преименуј циљну колону.
#XMSG
duplicateGBQCDCColumnsDesc=Назив циљне колоне је резервисан у Google BigQuery-ју. Потребно је да га преименујете да бисте могли да сачувате пројекцију.
#XMSG
duplicateConfluentCDCColumnsDesc=Назив циљне колоне је резервисан у Confluent. Треба да је преименујете да бисте могли да сачувате пројекцију.
#XMSG
duplicateSignavioCDCColumnsDesc=Назив циљне колоне је резервисан у решењу SAP Signavio. Треба да је преименујете да бисте могли да сачувате пројекцију.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Назив циљне колоне резервисан је у MS OneLake. Морате променити назив да бисте могли да сачувате пројекцију.
#XMSG
duplicateSFTPCDCColumnsDesc=Назив циљне колоне резервисан је у SFTP. Морате променити назив да бисте могли да сачувате пројекцију.
#XMSG
GBQTargetNameWithPrefixUpdated1=Назив циљне колоне садржи префикс који је резервисан у Google BigQuery-ју. Потребно је да га преименујете да бисте могли да сачувате пројекцију. {0}{0}Назив циљне колоне не може да почиње било којим од следећих низова:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Скратите назив циљне колоне.
#XMSG
GBQtargetMaxLengthDesc=У Google BigQuery-ју, назив колоне може да користи максимално 300 знакова. Скратите назив циљне колоне да бисте могли да сачувате пројекцију.
#XMSG
invalidMappedScalePrecision=Прецизност и скала за циљ морају бити компатибилни с прецизношћу и скалом за извор, тако да све цифре из извора одговарају циљном пољу.
#XMSG
invalidMappedScalePrecisionShortText=Унесите важећу вредност прецизности и скале.
#XMSG
validationIncompatiblePKTypeDescProjection3=Једна или више изворних колона имају типове података који се не могу дефинисати као примарни кључеви у Google BigQuery-ју. Ниједан примарни кључ се неће креирати у циљном објекту.{0}{0}Следећи циљни типови података су компатибилни са Google BigQuery типовима података за које се примарни кључ може дефинисати :{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Поништите одабир column __message_id.
#XMSG
uncheckColumnMessageIdDesc=Колона: Примарни кључ
#XMSG
validationOpCodeInsert=Морате унети вредност за унос.
#XMSG
recommendDifferentPrimaryKey=Препоручујемо да одаберете други примарни кључ на нивоу ставке.
#XMSG
recommendDifferentPrimaryKeyDesc=Када је шифра операције већ дефинисана, препоручује се да одаберете друге примарне кључеве за индекс низа и ставке како бисте избегли проблеме као што је, на пример, дуплирање колона.
#XMSG
selectPrimaryKeyItemLevel=Морате одабрати најмање један примарни кључ за ниво заглавља и ниво ставке.
#XMSG
selectPrimaryKeyItemLevelDesc=Када се низ или мапа прошире, морате одабрати два примарна кључа, један на нивоу заглавља и један на нивоу ставке.
#XMSG
invalidMapKey=Морате одабрати најмање један примарни кључ на нивоу заглавља.
#XMSG
invalidMapKeyDesc=Када се низ или мапа прошире, морате одабрати примарни кључ на нивоу заглавља.
#XFLD
txtSearchFields=Тражи циљне колоне
#XFLD
txtName=Назив
#XMSG
txtSourceColValidation=Није подржана једна изворна колона или више њих:
#XMSG
txtMappingCount=Пресликавања ({0})
#XMSG
schema=Шема
#XMSG
sourceColumn=Изворне колоне
#XMSG
warningSourceSchema=Свака промена шеме утицаће на пресликавања у дијалогу пројекције.
#XCOL
txtTargetColName=Циљна колона (технички назив)
#XCOL
txtDataType=Циљни тип података
#XCOL
txtSourceDataType=Тип изворних података
#XCOL
srcColName=Изворна колона (технички назив)
#XCOL
precision=Прецизност
#XCOL
scale=Скала
#XCOL
functionsOrConstants=Функције / константе
#XCOL
txtTargetColBusinessName=Циљна колона (Пословни назив)
#XCOL
prKey=Примарни кључ
#XCOL
txtProperties=Својства
#XBUT
txtOK=Сачувај
#XBUT
txtCancel=Одустани
#XBUT
txtRemove=Уклони
#XFLD
txtDesc=Опис
#XMSG
rftdMapping=Пресликавање
#XFLD
@lblColumnDataType=Тип података
#XFLD
@lblColumnTechnicalName=Технички назив
#XBUT
txtAutomap=Аутоматски пресликај
#XBUT
txtUp=Горе
#XBUT
txtDown=Доле

#XTOL
txtTransformationHeader=Пројекција
#XTOL
editTransformation=Уреди
#XTOL
primaryKeyToolip=Кључ


#XMSG
rftdFilter=Филтер
#XMSG
rftdFilterColumnCount=Извор: {0}({1})
#XTOL
rftdFilterColSearch=Тражи
#XMSG
rftdFilterColNoData=Нема колона за приказ
#XMSG
rftdFilteredColNoExps=Нема израза филтера
#XMSG
rftdFilterSelectedColTxt=Додај филтер за
#XMSG
rftdFilterTxt=Филтер доступан за
#XBUT
rftdFilterSelectedAddColExp=Додај израз
#YINS
rftdFilterNoSelectedCol=Одаберите колону да бисте додали филтер.
#XMSG
rftdFilterExp=Израз филтера
#XMSG
rftdFilterNotAllowedColumn=Додавање филтера није подржано за ову колону.
#XMSG
rftdFilterNotAllowedHead=Колона није подржана
#XMSG
rftdFilterNoExp=Филтер није дефинисан
#XTOL
rftdfilteredTt=Филтрирано
#XTOL
rftdremoveexpTt=Уклони израз филтера
#XTOL
validationMessageTt=Поруке валидације
#XTOL
rftdFilterDateInp=Одаберите датум
#XTOL
rftdFilterDateTimeInp=Одаберите датум и време
#XTOL
rftdFilterTimeInp=Одаберите време
#XTOL
rftdFilterInp=Унесите вредност
#XMSG
rftdFilterValidateEmptyMsg={0} израза филтера у {1} колони је празно
#XMSG
rftdFilterValidateInvalidNumericMsg={0} израза филтера у {1} колони садржи неважеће нумеричке вредности
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Израз филтера мора садржати важеће нумеричке вредности
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Ако је промењена шема циљног објекта, користите функцију "Пресликај у постојећи циљни објекат" на главној страници да би се извршило прилагођавање за промене и да би се поново извршило пресликавање циљног објекта и његовог извора.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Ако циљна табела већ постоји и пресликавање обухвата промену шеме, морате променити циљну табелу у складу с тим пре него што имплементирате ток репликације.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Ако ваше пресликавање обухвата промену шеме, морате променити циљну табелу у складу с тим пре него што имплементирате ток репликације.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Следеће неподржане колоне су прескочене у изворној дефиницији: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Следеће неподржане колоне су прескочене у циљној дефиницији: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Следећи објекти нису подржани јер су изложени за употребу: {0} {1} {0} {0} Да бисте користили табеле у току репликације, семантичка употреба (у подешавањима табеле) не сме бити постављена на {2}Аналитички скуп података{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Циљни објекат се не може користити јер је изложен за употребу: {0} {0} Да бисте користили табелу у току репликације, семантичка употреба (у подешавањима табеле) не сме бити постављена на {1}Аналитички скуп података{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Циљни објекат са овим називом већ постоји. Међутим, не може се користити јер је изложен за употребу: {0} {0} Да бисте користили табелу у току репликације, семантичка употреба (у подешавањима табеле) не сме бити постављена на {1}Аналитички скуп података{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Објекат са овим називом већ постоји у циљу. {0}Међутим, овај објекат се не може користити као циљни објекат за ток репликације у локални репозиторијум јер није локална табела.
#XMSG:
targetAutoRenameUpdated=Циљна колона је преименована.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Циљна колона је преименована да би се дозволиле репликације у Google BigQuery-ју. То је због једног од следећих разлога:{0} {1}{2}Назив колоне је резервисан{3}{2}Неподржани знакови{3}{2}Префикс је резервисан{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Циљна колона је преименована да би се дозволиле репликације у Confluent-у. То је због једног од следећих разлога:{0} {1}{2}Назив колоне је резервисан{3}{2}Неподржани знакови{3}{2}Префикс је резервисан{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Циљна колона је преименована да би се дозволиле репликације у циљ. Ово је због једног од следећих разлога:{0} {1}{2}Неподржани знакови{3}{2}Резервисани префикс{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Циљна колона је преименована да би се дозволиле репликације у циљ. То је због једног од следећих разлога:{0} {1}{2}Резервисани назив колоне{3}{2}Неподржани знакови{3}{2}Резервисани префикс{3}{4}
#XMSG:
targetAutoDataType=Циљни тип података је промењен.
#XMSG:
targetAutoDataTypeDesc=Циљни тип података је промењен у {0} зато што изворни тип података није подржан у Google BigQuery-ју.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Циљни тип података је промењен у {0} јер изворни тип података није подржан у циљној вези.
#XMSG
projectionGBQUnableToCreateKey=Није могуће креирати примарне кључеве.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=У Google BigQuery-ју, подржано је максимално 16 примарних кључева, али изворни објекат има већи број примарних кључева. Ниједан примарни кључ неће бити креиран у циљном објекту.
#XMSG
HDLFNoKeyError=Дефинишите једну колону или више њих као примарни кључ.
#XMSG
HDLFNoKeyErrorDescription=Да бисте реплицирали објекат морате дефинисати једну колону или више њих као примапни кључ.
#XMSG
HDLFNoKeyErrorExistingTarget=Дефинишите једну колону или више њих као примарни кључ.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Да би се реплицирали подаци у постојећи циљни објекат, једна колона или више њих мора се дефинисати као примарни кључ.{0} {0} Имате следеће опције за дефинисање једне колоне или више њих као примарни кључ: {0}{0}{1} Користите уређивач локалне табеле за промену постојећег циљног објекта. Затим поново учитајте ток репликације.{0}{0}{1} Преименујте циљни објекат у току репликације. Тиме ће се креирати нови објекат чим се извођење покрене. Након преименовања, можете дефинисати једну колону или више њих као примарни кључ у пројекцији.{0}{0}{1} Пресликајте објекат у други постојећи циљни објекат у којем је једна колона или више њих већ дефинисано као примарни кључ.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Примарни кључ промењен.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=У поређењу са изворним објектом, дефинисали сте различите колоне као примарни кључ за циљни објекат. Осигурајте да ове колоне јединствено идентификују све редове да бисте избегли могућа оштећења података при каснијој репликацији података. {0} {0} У изворном објекту, следеће колоне се дефинишу као примарни кључ: {0} {1}
#XMSG
duplicateDPIDColumns=Преименуј циљну колону.
#XMSG
duplicateDPIDDColumnsDesc1=Ова циљна колона је резервисана за техничку колону. Унесите други назив за снимање пројекције.
#XMSG:
targetAutoRenameDPID=Циљна колона је преименована.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Циљна колона је преименована тако да дозвољава репликације из ABAP извора без кључева. Ово је због једног од следећих разлога:{0} {1}{2}Резервисани назив колоне{3}{2}Неподржани знакови{3}{2} Резервисани префикс{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle=Циљна подешавања за {0}
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle=Изворна подешавања за {0}
#XBUT
connectionSettingSave=Сачувај
#XBUT
connectionSettingCancel=Одустани
#XBUT: Button to keep the object level settings
txtKeep=Задржи
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Пиши преко
#XFLD
targetConnectionThreadlimit=Ограничење нити циља за почетно учитавање (1-100)
#XFLD
connectionThreadLimit=Ограничење нити извора за почетно учитавање (1-100)
#XFLD
maxConnection=Ограничење нити репликације (1-100)
#XFLD
kafkaNumberOfPartitions=Број партиција
#XFLD
kafkaReplicationFactor=Фактор репликације
#XFLD
kafkaMessageEncoder=Уређај за кодирање порука
#XFLD
kafkaMessageCompression=Сажимање порука
#XFLD
fileGroupDeltaFilesBy=Групиши делта по
#XFLD
fileFormat=Тип фајла
#XFLD
csvEncoding=Шифровање CSV
#XFLD
abapExitLbl=ABAP излаз
#XFLD
deltaPartition=Број нити објекта за делта учитавања (1-10)
#XFLD
clamping_Data=Неуспех при скраћивању података
#XFLD
fail_On_Incompatible=Неуспех због некомпатибилних података
#XFLD
maxPartitionInput=Максимални број партиција
#XFLD
max_Partition=Дефиниши максимални број партиција
#XFLD
include_SubFolder=Укључи потфолдере
#XFLD
fileGlobalPattern=Глобални шаблон за назив фајла
#XFLD
fileCompression=Сажимање фајла
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Сепаратор фајла
#XFLD
fileIsHeaderIncluded=Заглавље фајла
#XFLD
fileOrient=Orient
#XFLD
gbqWriteMode=Начин писања
#XFLD
suppressDuplicate=Сажми дупликате
#XFLD
apacheSpark=Активирај компатибилност са Apache Spark
#XFLD
clampingDatatypeCb=Изврши Clamp за типове података покретног децималног зареза
#XFLD
overwriteDatasetSetting=Пиши преко циљних подешавања на нивоу објекта
#XFLD
overwriteSourceDatasetSetting=Пиши преко изворних подешавања на нивоу објекта
#XMSG
kafkaInvalidConnectionSetting=Унеситеte број између {0} и {1}.
#XMSG
MinReplicationThreadErrorMsg=Унесите број већи од {0}.
#XMSG
MaxReplicationThreadErrorMsg=Унесите број мањи од {0}.
#XMSG
DeltaThreadErrorMsg=Унесите вредност између 1 и 10.
#XMSG
MaxPartitionErrorMsg=Унесите вредност између 1 <= x <= 2147483647. Стандардна вредност је 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Унесите цео број између {0} и {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Користите фактор репликације брокера
#XFLD
serializationFormat=Формат серијализације
#XFLD
compressionType=Тип компресије
#XFLD
schemaRegistry=Користи регистар шема
#XFLD
subjectNameStrat=Стратегија назива субјекта
#XFLD
compatibilityType=Тип компатибилности
#XFLD
confluentTopicName=Назив теме
#XFLD
confluentRecordName=Назив записа
#XFLD
confluentSubjectNamePreview=Претходни приказ назива субјекта
#XMSG
serializationChangeToastMsgUpdated2=Формат серијализације промењен у JSON јер регистар шема није активиран. Да бисте вратили формат серијализације у AVRO, прво морате активирати регистар шема.
#XBUT
confluentTopicNameInfo=Назив теме се увек заснива на називу циљног објекта. Можете да га промените тако што ћете преименовати циљни објекат.
#XMSG
emptyRecordNameValidationHeaderMsg=Унесите назив записа.
#XMSG
emptyPartionHeader=Унесите број партиција.
#XMSG
invalidPartitionsHeader=Унесите важећи број партиција.
#XMSG
invalidpartitionsDesc=Унесите број између 1 и 200.000.
#XMSG
emptyrFactorHeader=Унесите фактор репликације.
#XMSG
invalidrFactorHeader=Унесите важећи фактор репликације.
#XMSG
invalidrFactorDesc=Унесите број између 1 и 32.767.
#XMSG
emptyRecordNameValidationDescMsg=Ако се користи формат серијализације "AVRO", подржани су само следећи знакови:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(доња црта)
#XMSG
validRecordNameValidationHeaderMsg=Унесите важећи назив записа.
#XMSG
validRecordNameValidationDescMsgUpdated=Пошто се користи формат серијализације "AVRO", назив записа мора да се састоји само од алфанумеричких знакова (A-Z, a-z, 0-9) и доње црте (_). Мора почињати словом или доњом цртом.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled="Број нити објекта за делта учитавања" се може поставити чим један објекат или више њих буду имали тип учитавања "Почетно и делта".
#XMSG
invalidTargetName=Неважећи назив колоне
#XMSG
invalidTargetNameDesc=Назив циљне колоне мора да се састоји само од алфанумеричких знакова (A-Z, a-z, 0-9) и доње црте (_).
#XFLD
consumeOtherSchema=Употреби друге верзије шеме
#XFLD
ignoreSchemamissmatch=Занемари неподударање шеме
#XFLD
confleuntDatatruncation=Неуспех при скраћивању података
#XFLD
isolationLevel=Ниво изолације
#XFLD
confluentOffset=Почетна тачка
#XFLD
signavioGroupDeltaFilesByText=Ништа
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=Не
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=Не

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Пројекције
#XBUT
txtAdd=Додај
#XBUT
txtEdit=Уреди
#XMSG
transformationText=Додајте пројекцију да бисте поставили филтер или пресликавање.
#XMSG
primaryKeyRequiredText=Одаберите примарни кључ уз Конфигуриши шему.
#XFLD
lblSettings=Подешавања
#XFLD
lblTargetSetting={0}: Циљна подешавања
#XMSG
@csvRF=Одаберите фајл који садржи дефиницију шеме коју желите да примените на све фајлове у фолдеру.
#XFLD
lblSourceColumns=Изворне колоне
#XFLD
lblJsonStructure=Структура JSON
#XFLD
lblSourceSetting={0}: Изворна подешавања
#XFLD
lblSourceSchemaSetting={0}: Подешавања изворне шеме
#XBUT
messageSettings=Подешавања поруке
#XFLD
lblPropertyTitle1=Својства објекта
#XFLD
lblRFPropertyTitle=Својства тока репликације
#XMSG
noDataTxt=Нема колона за приказ.
#XMSG
noTargetObjectText=Циљни објекат није одабран.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Циљне колоне
#XMSG
searchColumns=Тражи колоне
#XTOL
cdcColumnTooltip=Колона за делта снимање
#XMSG
sourceNonDeltaSupportErrorUpdated=Изворни објекат не подржава делта снимање.
#XMSG
targetCDCColumnAdded=2 циљне колоне су додате за делта снимање.
#XMSG
deltaPartitionEnable=Ограничење нити објекта за делта учитавања додато у изворна подешавања.
#XMSG
attributeMappingRemovalTxt=Уклањање неважећих пресликавања која нису подржана за нови циљни објекат.
#XMSG
targetCDCColumnRemoved=2 циљне колоне које се користе за делта снимање су уклоњене.
#XMSG
replicationLoadTypeChanged=Тип учитавања промењен у "Почетно и делта".
#XMSG
sourceHDLFLoadTypeError=Промените тип учитавања у "Почетно и делта".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Да бисте реплицирали објекат из изворне везе с типом везе SAP HANA Cloud, фајлови језера података у SAP Datasphere, морате користити тип учитавања у "Почетно и делта".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Активирајте делта снимање.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Да бисте реплицирали објекат из изворне везе с типом везе SAP HANA Cloud, фајлови језера података у SAP Datasphere, морате активирати делта снимање.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Промените циљни објекат.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Циљни објекат се не може користити јер је делта снимање деактивирано. Можете преименовати циљни објекат (што омогућава креирање новог објекта с делта снимањем) или га пресликати у постојећи објекат са активираним делта снимањем.
#XMSG
deltaPartitionError=Унесите важећи број нити објекта за делта учитавања.
#XMSG
deltaPartitionErrorDescription=Унесите вредност између 1 и 10.
#XMSG
deltaPartitionEmptyError=Унесите број нити објекта за делта учитавања.
#XFLD
@lblColumnDescription=Опис
#XMSG
@lblColumnDescriptionText1=За техничке сврхе - управљање двоструким записима проузрокованим проблемима при репликацији изворних објеката заснованих на ABAP-у који немају примарни кључ.
#XFLD
storageType=Складиште
#XFLD
skipUnmappedColLbl=Прескочи непресликане колоне
#XFLD
abapContentTypeLbl=Тип садржаја
#XFLD
autoMergeForTargetLbl=Аутоматски спој податке
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Опште
#XFLD
lblBusinessName=Пословни назив
#XFLD
lblTechnicalName=Технички назив
#XFLD
lblPackage=Пакет
#XFLD
statusPanel=Статус извођења
#XBTN: Schedule dropdown menu
SCHEDULE=Распоред
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Уреди распоред
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Избриши распоред
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Креирај распоред
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Није успела провера валидације распореда
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Није могуће креирати распоред јер се ток репликације тренутно имплементира.{0}Сачекајте да се ток репликације имплементира.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=За токове репликације који садрже објекте с типом учитавања "Почетно и делта" не може се креирати распоред.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=За токове репликације који садрже објекте с типом учитавања "Почетно и делта/Само делта" не може се креирати распоред.
#XFLD : Scheduled popover
SCHEDULED=Планирано
#XFLD
CREATE_REPLICATION_TEXT=Креирај ток репликације
#XFLD
EDIT_REPLICATION_TEXT=Уреди ток репликације
#XFLD
DELETE_REPLICATION_TEXT=Избриши ток репликације
#XFLD
REFRESH_FREQUENCY=Учесталост
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Ток репликације се не може имплементирати јер постојећи распоред{0} још увек не подржава тип учитавања "Почетно и делта".{0}{0}Да бисте имплементирали ток репликације морате поставити типове учитавања свих објеката {0} на "Само почетно". Уместо тога, можете да избришете распоред, имплементирате {0}ток репликације, па покренете ново извођење. Резултат тога је извођење без краја{0}, што такође подржава објекте с типом учитавања "Почетно и делта".
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Ток репликације се не може имплементирати јер постојећи распоред{0} још не подржава тип учитавања "Почетно и делта/Само делта".{0}{0}Да бисте имплементирали ток репликације, морате поставити типове учитавања свих објеката {0} на "Само почетно". Уместо тога, можете да избришете распоред, имплементирате {0}ток репликације, а затим покренете ново извођење. Резултат тога је извођење без {0}краја, што такође подржава објекте с типом учитавања "Почетно и делта/Само делта".
#XMSG
SCHEDULE_EXCEPTION=Није успело позивање детаља распореда
#XFLD: Label for frequency column
everyLabel=Сваких
#XFLD: Plural Recurrence text for Hour
hoursLabel=сати
#XFLD: Plural Recurrence text for Day
daysLabel=дана
#XFLD: Plural Recurrence text for Month
monthsLabel=месеци
#XFLD: Plural Recurrence text for Minutes
minutesLabel=минута
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Није успело позивање информација о могућности распореда.
#XFLD :Paused field
PAUSED=Паузирано
#XMSG
navToMonitoring=Отвори у Монитору токова
#XFLD
statusLbl=Статус
#XFLD
lblLastRunExecuted=Почетак последњег извођења
#XFLD
lblLastExecuted=Последње извођење
#XFLD: Status text for Completed
statusCompleted=Завршено
#XFLD: Status text for Running
statusRunning=Изводи се
#XFLD: Status text for Failed
statusFailed=Није успело
#XFLD: Status text for Stopped
statusStopped=Заустављено
#XFLD: Status text for Stopping
statusStopping=Зауставља се
#XFLD: Status text for Active
statusActive=Активно
#XFLD: Status text for Paused
statusPaused=Паузирано
#XFLD: Status text for not executed
lblNotExecuted=Још није изведено
#XFLD
messagesSettings=Подешавања порука
#XTOL
@validateModel=Поруке валидације
#XTOL
@hierarchy=Хијерархија
#XTOL
@columnCount=Број колона
#XMSG
VAL_PACKAGE_CHANGED=Доделили сте овај објекат пакету "{1}". Кликните на “Сачувај” да бисте потврдили и вредновали ову промену. Узмите у обзир да доделу пакету није могуће поништити у овом уређивачу након снимања.
#XMSG
MISSING_DEPENDENCY=Зависности објекта ''{0}'' не могу се решити у пакету "{1}".
#XFLD
deltaLoadInterval=Интервал делта учитавања
#XFLD
lblHour=Сати (0-24)
#XFLD
lblMinutes=Минути (0-59)
#XMSG
maxHourOrMinErr=Унесите вредности између 0 и {0}
#XMSG
maxDeltaInterval=Максимална вредност интервала делта учитавања је 24 сата.{0}Промените вредност минута или вредност сата у складу с тим.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Пут циљног спремника
#XFLD
confluentSubjectName=Назив субјекта
#XFLD
confluentSchemaVersion=Верзија шеме
#XFLD
confluentIncludeTechKeyUpdated=Укључи технички кључ
#XFLD
confluentOmitNonExpandedArrays=Изостави непроширене низове
#XFLD
confluentExpandArrayOrMap=Прошири низ или мапу
#XCOL
confluentOperationMapping=Пресликавање операције
#XCOL
confluentOpCode=Opcode
#XFLD
confluentInsertOpCode=Унеси
#XFLD
confluentUpdateOpCode=Ажурирај
#XFLD
confluentDeleteOpCode=Избриши
#XFLD
expandArrayOrMapNotSelectedTxt=Није одабрано
#XFLD
confluentSwitchTxtYes=Да
#XFLD
confluentSwitchTxtNo=Не
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Грешка
#XTIT
executeWarning=Упозорење
#XMSG
executeunsavederror=Сачувајте ток репликације пре него што га изведете.
#XMSG
executemodifiederror=Постоје несачуване промене у току репликације. Сачувајте ток репликације.
#XMSG
executeundeployederror=Морате имплементирати ток репликације да бисте могли да га изведете.
#XMSG
executedeployingerror=Сачекајте да се заврши имплементација.
#XMSG
msgRunStarted=Извођење покренуто
#XMSG
msgExecuteFail=Није успело извођење тока репликације
#XMSG
titleExecuteBusy=Сачекајте.
#XMSG
msgExecuteBusy=Припремамо ваше податке за извођење тока репликације.
#XTIT
executeConfirmDialog=Упозорење
#XMSG
msgExecuteWithValidations=Ток репликације има грешке валидације. Извођење тока репликације може довести до грешке.
#XMSG
msgRunDeployedVersion=Постоје промене за имплементацију. Биће покренута последња имплементирана верзија тока репликације. Да ли желите да наставите?
#XBUT
btnExecuteAnyway=Ипак изведи
#XBUT
btnExecuteClose=Затвори
#XBUT
loaderClose=Затвори
#XTIT
loaderTitle=Учитавање
#XMSG
loaderText=Позивање детаља са сервера
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Ток репликације за циљну везу која није SAP није могуће покренути
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=зато што излазни обим није доступан за овај месец.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Администратор може да повећа излазне блокове Premium за овог закупцa
#XMSG
premiumOutBoundRFAdminErrMsgPart2=и излазни обим учини доступним за овај месец.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Грешка
#XTIT
deployInfo=Информације
#XMSG
deployCheckFailException=Изузетак током имплементације
#XMSG
deployGBQFFDisabled=Имплементација токова репликације с циљном везом са Google BigQuery-јем тренутно није могућа јер извршавамо одржавање ове функције.
#XMSG
deployKAFKAFFDisabled=Имплементација токова репликације с циљном везом са Apache Kafka-ом тренутно није могућа јер извршавамо одржавање ове функције.
#XMSG
deployConfluentDisabled=Имплементација токова репликације с циљном везом са Confluent Kafka тренутно није могућа јер је у току одржавање ове функције.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=За следеће циљне објекте, називе табеле делта снимања већ користе друге табеле у репозиторијуму: {0} Морате да преименујете те циљне објекте да бисте обезбедили да повезани називи табеле делта снимања буду јединствени пре него што имплементирате ток репликације.
#XMSG
deployDWCSourceFFDisabled=Имплементација токова репликације који имају SAP Datasphere као извор тренутно није могућа јер извршавамо одржавање ове функције.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Имплементација токова репликације који садрже локалне табеле компатибилне с делтом као изворне објекте тренутно није могућа јер вршимо одржавање ове функције.
#XMSG
deployHDLFSourceFFDisabled=Имплементација токова репликације који имају изворне везе с типом везе SAP HANA Cloud, фајлови језера података тренутно није могућа јер вршимо одржавање.
#XMSG
deployObjectStoreAsSourceFFDisabled=Имплементација токова репликације који имају добављаче складишног простора као извор тренутно није могућа.
#XMSG
deployConfluentSourceFFDisabled=Имплементација токова репликације који имају Confluent Kafka као извор тренутно није могућа јер извршавамо одржавање ове функције.
#XMSG
deployMaxDWCNewTableCrossed=Велике токове репликације није могуће "сачувати и имплементирати" у једном кораку. Прво сачувајте ток репликације, а затим га имплементирајте.
#XMSG
deployInProgressInfo=Имплементација је већ у току.
#XMSG
deploySourceObjectInUse=Изворни објекти {0} се већ користе у токовима репликације {1}.
#XMSG
deployTargetSourceObjectInUse=Изворни објекти {0} се већ користе у токовима репликације {1}. Циљни објекти {2} се већ користе у токовима репликације {3}.
#XMSG
deployReplicationFlowCheckError=Грешка при провери тока репликације: {0}
#XMSG
preDeployTargetObjectInUse=Циљни објекти {0} се већ користе у токовима репликације {1} и не можете имати исти циљни објекат у два различита тока репликације. Одаберите други циљни објекат и покушајте поново.
#XMSG
runInProgressInfo=Ток репликације се већ изводи.
#XMSG
deploySignavioTargetFFDisabled=Имплементација токова репликације који имају SAP Signavio као циљ тренутно није могућа јер вршимо одржавање ове функције.
#XMSG
deployHanaViewAsSourceFFDisabled=Имплементација токова репликације који имају погледе као изворне објекте за одабрану изворну везу тренутно није могућа. Покушајте поново касније.
#XMSG
deployMsOneLakeTargetFFDisabled=Имплементација токова репликације који имају MS OneLake као циљ тренутно није могућа јер вршимо одржавање ове функције.
#XMSG
deploySFTPTargetFFDisabled=Имплементација токова репликације који имају SFTP као циљ тренутно није могућа јер вршимо одржавање ове функције.
#XMSG
deploySFTPSourceFFDisabled=Имплементација токова репликације који имају SFTP као извор тренутно није могућа јер вршимо одржавање ове функције.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Технички назив
#XFLD
businessNameInRenameTarget=Пословни назив
#XTOL
renametargetDialogTitle=Преименуј циљни објекат
#XBUT
targetRenameButton=Преименуј
#XBUT
targetRenameCancel=Одустани
#XMSG
mandatoryTargetName=Морате унети назив.
#XMSG
dwcSpecialChar=_(доња црта) је једини дозвољени посебан знак.
#XMSG
dwcWithDot=Назив циљне табеле може да садржи латинична слова, бројеве, доње црте (_) и тачке (.). Први знак мора бити слово, број или доња црта (не тачка).
#XMSG
nonDwcSpecialChar=Дозвољени посебни знакови су _(доња црта) -(цртица) .(тачка)
#XMSG
firstUnderscorePattern=Назив не сме почињати с _(доњом цртом)

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: Прикажи SQL изјаву Креирај табелу
#XMSG
sqlDialogMaxPKWarning=У Google BigQuery-ју, подржано је максимално 16 примарних кључева, a изворни објекат има већи број. Стога примарни кључеви нису дефинисани у овој изјави.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Једна или више изворних колона имају типове података који се не могу дефинисати као примарни кључеви у Google BigQuery-ју. Стога примарни кључеви нису дефинисани у овом случају. У Google BigQuery-ју, само следећи типови података могу имати примарни кључ: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Копирај и затвори
#XBUT
closeDDL=Затвори
#XMSG
copiedToClipboard=Копирано у прелазну меморију


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Објекат "{0}" не може бити део ланца задатака јер нема завршетак (пошто укључује објекте с типом учитавања Почетно и делта/Само делта).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Објекат "{0}" не може бити део ланца задатака јер нема завршетак (пошто укључује објекте с типом учитавања Почетно и Делта).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Објекат "{0}" се не може додати у ланац задатака.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Постоје несачувани циљни објекти. Прво поново сачувајте.{0}{0} Понашање ове функције се променило: У прошлости су се циљни објекти креирали само у циљном окружењу када је имплементиран ток репликације.{0} Сада се објекти креирају већ када се ток репликације сачува. Ваш ток репликације је креиран пре ове промене и садржи нове објекте.{0} Потребно је да поново сачувате ток репликације пре него што га имплементирате да би нови објекти били правилно укључени.
#XMSG
confirmChangeContentTypeMessage=Променићете тип садржаја. Ако то урадите, све постојеће пројекције биће избрисане.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Назив субјекта
#XFLD
schemaDialogVersionName=Верзија шеме
#XFLD
includeTechKey=Укључи технички кључ
#XFLD
segementButtonFlat=Равно
#XFLD
segementButtonNested=Груписано
#XMSG
subjectNamePlaceholder=Тражи назив субјекта

#XMSG
@EmailNotificationSuccess=Конфигурација обавештења е-поштом времена извођења је сачувана.

#XFLD
@RuntimeEmailNotification=Обавештење е-поштом времена извођења

#XBTN
@TXT_SAVE=Сачувај


