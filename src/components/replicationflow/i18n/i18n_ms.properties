#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Aliran Replikasi

#XFLD: Edit Schema button text
editSchema=Edit skema

#XTIT : Properties heading
configSchema=Konfigurasikan Skema

#XFLD: save changed button text
applyChanges=Gunakan Perubahan


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Pilih Sumber Sambungan
#XFLD
sourceContainernEmptyText=Pilih Kontena
#XFLD
targetConnectionEmptyText=Pilih Sambungan Sasaran
#XFLD
targetContainernEmptyText=Pilih Kontena
#XFLD
sourceSelectObjectText=Pilih Objek Sumber
#XFLD
sourceObjectCount=Objek Sumber ({0})
#XFLD
targetObjectText=Objek Sasaran
#XFLD
confluentBrowseContext=Pilih Konteks
#XBUT
@retry=Cuba semula
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Naik taraf penyewa sedang berjalan.

#XTOL
browseSourceConnection=Layari sambungan sumber
#XTOL
browseTargetConnection=Layari sambungan sasaran
#XTOL
browseSourceContainer=Layari kontena sumber
#XTOL
browseAndAddSourceDataset=Tambah objek sumber
#XTOL
browseTargetContainer=Layari kontena sasaran
#XTOL
browseTargetSetting=Layari tetapan sasaran
#XTOL
browseSourceSetting=Layari tetapan sumber
#XTOL
sourceDatasetInfo=Maklumat
#XTOL
sourceDatasetRemove=Keluarkan
#XTOL
mappingCount=Ini mewakili jumlah bilangan pemetaan/ungkapan bukan berasaskan nama.
#XTOL
filterCount=Ini mewakili jumlah bilangan syarat penapis.
#XTOL
loading=Memuat...
#XCOL
deltaCapture=Tangkapan Delta
#XCOL
deltaCaptureTableName=Jadual Tangkapan Delta
#XCOL
loadType=Muatkan Jenis
#XCOL
deleteAllBeforeLoading=Padam Semua Sebelum Memuatkan
#XCOL
transformationsTab=Unjuran
#XCOL
settingsTab=Tetapan

#XBUT
renameTargetObjectBtn=Namakan Semula Objek Sasaran
#XBUT
mapToExistingTargetObjectBtn=Peta kepada Objek Sasaran Sedia Ada
#XBUT
changeContainerPathBtn=Ubah Laluan Kontena
#XBUT
viewSQLDDLUpdated=Paparan Penyata Jadual Cipta SQL
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Objek sumber tidak menyokong tangkapan delta, tetapi terdapat pendayaan pilihan tangkapan delta bagi objek sasaran terpilih.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Gunakan objek sasaran kemudian kerana tangkapan delta didayakan,{0}manakala objek sumber tidak menyokong tangkapan delta.{1}Anda boleh memilih objek sasaran lain yang tidak menyokong tangkapan delta.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Objek sasaran dengan nama ini telah wujud. Walau bagaimanapun, anda tidak boleh gunakannya{0}kerana tangkapan delta didayakan manakala objek sumber tidak{0}menyokong tangkapan delta.{1}Anda boleh masukkan nama objek sasaran sedia ada yang tidak{0}menyokong tangkapan delta atau masukkan nama yang belum wujud lagi.
#XBUT
copySQLDDLUpdated=Salin Penyata Jadual Cipta SQL
#XMSG
targetObjExistingNoCDCColumnUpdated=Jadual sedia ada dalam Google BigQuery mesti mengandungi lajur berikut untuk tangkapan data perubahan (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Objek sumber berikut tidak disokong kerana ia perlu mempunyai kod utama, atau menggunakan sambungan yang memenuhi syarat untuk mendapatkan semula kod utama:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Rujukan SAP KBA 3531135 untuk penyelesaian yang boleh dilakukan.
#XLST: load type list values
initial=Awal Sahaja
@emailUpdateError=Ralat dalam mengemas kini senarai Pemberitahuan E-mel

#XLST
initialDelta=Awal dan Delta

#XLST
deltaOnly=Delta Sahaja
#XMSG
confluentDeltaLoadTypeInfo=Untuk sumber Confluent Kafka, hanya jenis muatan Awal dan Delta disokong.
#XMSG
confirmRemoveReplicationObject=Adakah anda mengesahkan bahawa anda ingin memadamkan replikasi?
#XMSG
confirmRemoveReplicationTaskPrompt=Tindakan ini akan memadamkan replikasi sedia ada. Adakah anda mahu meneruskan?
#XMSG
confirmTargetConnectionChangePrompt=Tindakan ini akan menetapkan semula sambungan sasaran, kontena sasaran dan memadam semua objek sasaran. Adakah anda mahu meneruskan?
#XMSG
confirmTargetContainerChangePrompt=Tindakan ini akan menetapkan semula, kontena sasaran dan memadam semua objek sasaran sedia ada. Adakah anda mahu meneruskan?
#XMSG
confirmRemoveTransformObject=Adakah anda mengesahkan bahawa anda mahu memadamkan unjuran {0}?
#XMSG
ErrorMsgContainerChange=Ralat berlaku semasa menukar laluan kontena.
#XMSG
infoForUnsupportedDatasetNoKeys=Tiada sokongan untuk objek sumber berikut kerana ia tiada kod utama:
#XMSG
infoForUnsupportedDatasetView=Objek sumber jenis Paparan berikut tidak disokong:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Objek sumber berikut tidak disokong kerana ia merupakan paparan SQL yang mengandungi parameter input:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Tiada sokongan untuk objek sumber berikut kerana pengekstrakan dinyahdaya untuknya:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Untuk sambungan Confluent, satu-satunya format bersiri yang dibenarkan ialah AVRO dan JSON. Objek berikut tidak disokong kerana ia menggunakan format penyirian yang berbeza:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Skema untuk objek berikut tidak berjaya didapatkan. Pilih konteks yang sesuai atau sahkan konfigurasi pendaftaran skema
#XTOL: warning dialog header on deleting replication task
deleteHeader=Padam
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Tetapan Padam Semua Sebelum Memuatkan tidak disokong untuk Google BigQuery.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Tetapan Padam Semua Sebelum memadam dan mencipta semula objek (topik) sebelum setiap replikasi. Ini juga memadam semua mesej yang diumpukkan.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Tetapan Padam Semua Sebelum tidak disokong untuk jenis sasaran ini.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Nama Teknikal
#XCOL
connBusinessName=Nama Perniagaan
#XCOL
connDescriptionName=Perihalan
#XCOL
connType=Jenis
#XMSG
connTblNoDataFoundtxt=Tiada Sambungan Ditemui
#XMSG
connectionError=Ralat berlaku semasa mengambil sambungan.
#XMSG
connectionCombinationUnsupportedErrorTitle=Gabungan sambungan tidak disokong
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Replikasi daripada {0} kepada {1} tidak disokong pada masa ini.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Gabungan jenis sambungan tidak disokong
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Replikasi daripada sambungan dengan jenis sambungan SAP HANA Cloud, Fail Data Lake ke {0} tidak disokong. Anda hanya boleh replikakan kepada SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Pilih
#XBUT
containerCancelBtn=Batalkan
#XTOL
containerSelectTooltip=Pilih
#XTOL
containerCancelTooltip=Batalkan
#XMSG
containerContainerPathPlcHold=Laluan Kontena
#XFLD
containerContainertxt=Bekas
#XFLD
confluentContainerContainertxt=Konteks
#XMSG
infoMessageForSLTSelection=Hanya ID /SLT/Pindahan Besar-besaran dibenarkan sebagai kontena. Pilih ID Pemindahan Besar-besaran di bawah SLT (jika ada) dan klik Serah.
#XMSG
msgFetchContainerFail=Ralat berlaku semasa mengambil data kontena.
#XMSG
infoMessageForSLTHidden=Folder SLT akan muncul dalam senarai di bawah jika mempunyai sambungan yang menyokong.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Pilih bekas yang mengandungi subfolder di dalamnya.
#XMSG
sftpIncludeSubFolderText=Palsu
#XMSG
sftpIncludeSubFolderTextNew=Tidak

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Belum ada pemetaan penapis)
#XMSG
failToFetchRemoteMetadata=Ralat berlaku semasa mengambil metadata.
#XMSG
failToFetchData=Ralat berlaku semasa mengambil sasaran sedia ada.
#XCOL
@loadType=Muatkan Jenis
#XCOL
@deleteAllBeforeLoading=Padam Semua Sebelum Memuatkan

#XMSG
@loading=Memuat...
#XFLD
@selectSourceObjects=Pilih Objek Sumber
#XMSG
@exceedLimit=Tidak boleh mengimport lebih daripada {0} objek pada satu masa. Nyahpilih sekurang-kurangnya {1} objek.
#XFLD
@objects=Objek
#XBUT
@ok=OK
#XBUT
@cancel=Batalkan
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Seterusnya
#XBUT
btnAddSelection=Tambah Pemilihan
#XTOL
@remoteFromSelection=Keluarkan daripada Pemilihan
#XMSG
@searchInForSearchField=Carian dalam {0}

#XCOL
@name=Nama Teknikal
#XCOL
@type=Jenis
#XCOL
@location=Lokasi
#XCOL
@label=Nama Perniagaan
#XCOL
@status=Status

#XFLD
@searchIn=Carian dalam:
#XBUT
@available=Tersedia
#XBUT
@selection=Pilihan

#XFLD
@noSourceSubFolder=Jadual dan Paparan
#XMSG
@alreadyAdded=Terdapat dalam gambar rajah
#XMSG
@askForFilter=Terdapat lebih daripada {0} item. Sila masukkan rentetan penapis untuk mengecilkan bilangan item.
#XFLD: success label
lblSuccess=Berjaya
#XFLD: ready label
lblReady=Sedia
#XFLD: failure label
lblFailed=Gagal
#XFLD: fetching status label
lblFetchingDetail=Mendapatkan butiran

#XMSG Place holder text for tree filter control
filterPlaceHolder=Taip teks untuk menapis objek aras atas
#XMSG Place holder text for server search control
serverSearchPlaceholder=Taip dan tekan Masukkan untuk carian
#XMSG
@deployObjects=Import {0} objek...
#XMSG
@deployObjectsStatus=Bilangan objek yang telah diimport: {0}. Bilangan objek yang tidak dapat diimport: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Gagal membuka penyemak imbas repositori tempatan.
#XMSG
@openRemoteSourceBrowserError=Gagal mendapatkan objek sumber.
#XMSG
@openRemoteTargetBrowserError=Gagal mendapatkan objek sasaran.
#XMSG
@validatingTargetsError=Ralat berlaku semasa mengesahkan sasaran.
#XMSG
@waitingToImport=Sedia untuk Import

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Bilangan maksimum objek terlebih. Pilih maksimum 500 objek untuk satu aliran replikasi.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Nama Teknikal
#XFLD
sourceObjectBusinessName=Nama Perniagaan
#XFLD
sourceNoColumns=Bilangan Lajur
#XFLD
containerLbl=Bekas

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Anda mesti memilih sambungan sumber untuk aliran replikasi.
#XMSG
validationSourceContainerNonExist=Anda mesti memilih kontena untuk sambungan sumber.
#XMSG
validationTargetNonExist=Anda mesti memilih sambungan sasaran untuk aliran replikasi.
#XMSG
validationTargetContainerNonExist=Anda mesti memilih kontena untuk sambungan sasaran.
#XMSG
validationTruncateDisabledForObjectTitle=Replikasi ke storan objek.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Replikasi ke dalam storan cloud hanya boleh dilakukan jika pilihan Padam Semua Sebelum Memuatkan ditetapkan atau objek sasaran tidak wujud dalam sasaran.{0}{0} Untuk kekal mendayakan replikasi bagi objek yang pilihan Padam Semua Sebelum Memuatkan tidak ditetapkan, pastikan objek sasaran tidak wujud dalam sistem sebelum anda menjalankan aliran replikasi.
#XMSG
validationTaskNonExist=Anda mesti mempunyai sekurang-kurangnya satu replikasi dalam aliran replikasi.
#XMSG
validationTaskTargetMissing=Anda mesti mempunyai sasaran untuk replikasi dengan sumber: {0}
#XMSG
validationTaskTargetIsSAC=Sasaran dipilih ialah SAC Artifak : {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Sasaran dipilih bukan jadual tempatan yang disokong: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Objek dengan nama ini telah wujud dalam sasaran. Walau bagaimanapun, jangan gunakan objek ini sebagai objek sasaran untuk aliran replikasi kepada repositori tempatan kerana ia bukan jadual tempatan.
#XMSG
validateSourceTargetSystemDifference=Anda mesti memilih sambungan sumber dan sasaran yang berbeza serta kombinasi kontena untuk aliran replikasi.
#XMSG
validateDuplicateSources=satu atau beberapa replikasi mempunyai nama objek sumber pendua: {0}.
#XMSG
validateDuplicateTargets=satu atau beberapa replikasi mempunyai nama objek sasaran pendua: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=Objek sumber {0} tidak menyokong tangkapan delta, manakala objek sasaran {1} menyokongnya. Anda mesti mengeluarkan replikasi.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Anda mesti pilih jenis muatan "Awal dan Delta" untuk replikasi dengan nama objek sasaran {0}.
#XMSG
validationAutoRenameTarget=Anda telah namakan semula lajur sasaran.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Unjuran automatik telah ditambah, dan lajur sasaran berikut telah dinamakan semula untuk membenarkan replikasi kepada sasaran:{1}{1} {0} {1}{1}Ini disebabkan oleh salah satu sebab berikut:{1}{1}{2} Aksara yang tiada sokongan{1}{2} Awalan khusus
#XMSG
validationAutoRenameTargetDescriptionUpdated=Terdapat tambahan unjuran automatik dan lajur sasaran berikut tidak dinamakan semula untuk membenarkan replikasi kepada Google BigQuery:{1}{1} {0} {1}{1}Ini disebabkan oleh salah satu sebab berikut:{1}{1}{2} Nama lajur khusus{1}{2} Aksara yang tiada sokongan{1}{2} Awalan khusus
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Terdapat tambahan unjuran automatik dan lajur sasaran berikut tidak dinamakan semula untuk membenarkan replikasi kepada Confluent:{1}{1} {0} {1}{1}Ini disebabkan oleh salah satu sebab berikut:{1}{1}{2} Nama lajur khusus{1}{2} Aksara yang tiada sokongan{1}{2} Awalan khusus
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Terdapat tambahan unjuran automatik dan lajur sasaran berikut tidak dinamakan semula untuk membenarkan replikasi kepada sasaran:{1}{1} {0} {1}{1}Ini disebabkan oleh salah satu sebab berikut:{1}{1}{2} Nama lajur khusus{1}{2} Aksara yang tiada sokongan{1}{2} Awalan khusus
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Anda telah namakan semula objek sasaran.
#XMSG
autoRenameInfoDesc=Objek sasaran telah dinamakan semula kerana ia mengandungi aksara yang tidak disokong. Hanya aksara berikut disokong:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(titik){0}{1}_(garis bawah){0}{1}-(sempang)
#XMSG
validationAutoTargetTypeConversion=Anda telah ubah jenis data sasaran.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Untuk lajur sasaran berikut, anda telah ubah jenis data sasaran kerana dalam Google BigQuery, tiada sokongan untuk jenis data sumber:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Untuk lajur sasaran berikut, anda telah ubah jenis data sasaran kerana tiada sokongan untuk jenis data sumber dalam sambungan sasaran:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Pendekkan nama lajur sasaran.
#XMSG
validationMaxCharLengthGBQTargetDescription=Dalam Google BigQuery, nama lajur boleh menggunakan maksimum 300 aksara. Guna unjuran untuk memendekkan nama lajur sasaran berikut:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Tiada kod utama yang anda akan cipta.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=Dalam Google BigQuery, terdapat sokongan untuk maksimum 16 kod utama tetapi objek sumber mempunyai bilangan kod utama yang lebih besar. Tiada kod utama yang anda akan cipta dalam objek sasaran.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Satu atau lebih lajur sumber mempunyai jenis data yang tidak boleh ditakrifkan sebagai kod utama dalam Google BigQuery. Tiada kod utama yang anda akan ciptakan dalam objek sasaran.{0}{0}Jenis data sasaran berikut sesuai dengan jenis data Google BigQuery yang kod utama boleh ditakrifkan:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Takrifkan satu atau lebih lajur sebagai kod utama.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Anda mesti mentakrifkan satu atau lebih lajur sebagai dialog skema sumber penggunaan kod utama untuk melakukan ini.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Takrifkan satu atau lebih lajur sebagai kod utama.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Anda mesti takrifkan satu atau lebih lajur sebagai kod utama yang sepadan dengan kekangan kod utama untuk objek sumber anda. Pergi ke Konfigurasikan Skema dalam sifat objek sumber anda untuk melakukannya.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Masukkan nilai bahagian maksimum yang sah.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Nilai Bahagian Maksimum mestilah ≥ 1 dan ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Takrifkan satu atau lebih lajur sebagai kod utama.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Untuk mereplikakan objek, anda mesti mentakrifkan satu atau lebih lajur sasaran sebagai kod utama. Gunakan unjuran untuk melakukan ini.
#XMSG
validateHDLFNoPKExistingDatasetError=Takrifkan satu atau lebih lajur sebagai kod utama.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Untuk mereplikasi data ke objek sasaran sedia ada, data mesti mempunyai satu atau beberapa lajur yang ditakrifkan sebagai kod utama. {0} Anda mempunyai pilihan yang berikut untuk mentakrifkan satu atau beberapa lajur sebagai kod utama: {0} {1} Gunakan editor jadual tempatan untuk mengubah objek sasaran sedia ada. Kemudian muatkan semula aliran replikasi.{0}{1} Namakan semula objek sasaran dalam aliran replikasi. Ini akan mencipta objek baharu sebaik sahaja jalanan bermula. Selepas menamakan semula, anda boleh mentakrifkan satu atau beberapa lajur sebagai kod utama dalam unjuran.{0}{1} Petakan objek kepada objek sasaran sedia ada lain yang satu atau beberapa lajur ditakrifkan sebagai kod utama.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Sasaran dipilih telah wujud dalam repositori: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Nama jadual tangkapan delta telah digunakan oleh jadual lain dalam repositori: {0}. Anda mesti namakan semula objek sasaran ini untuk memastikan yang nama jadual tangkapan delta berkaitan adalah unik sebelum anda boleh menyimpan aliran replikasi.
#XMSG
validateConfluentEmptySchema=Takrifkan Skema
#XMSG
validateConfluentEmptySchemaDescUpdated=Jadual sumber tidak mempunyai skema. Pilih Konfigurasikan Skema untuk mentakrifkan satu
#XMSG
validationCSVEncoding=Pengekodan CSV Tidak Sah
#XMSG
validationCSVEncodingDescription=Pengekodan CSV tugas tidak sah.
#XMSG
validateConfluentEmptySchema=Pilih jenis data sasaran yang sesuai
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Pilih jenis data sasaran yang sesuai
#XMSG
globalValidateTargetDataTypeDesc=Ralat berlaku dengan pemetaan lajur. Pergi ke Unjuran dan pastikan semua lajur sumber dipetakan dengan lajur unik, dengan lajur yang mempunyai jenis data yang serasi dan semua ungkapan yang ditakrifkan adalah sah.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Nama Lajur Pendua.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Nama Lajur Pendua tidak disokong. Gunakan Dialog Unjuran untuk membetulkannya. Objek sasaran berikut mempunyai nama lajur pendua: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Nama Lajur Pendua.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Nama Lajur Pendua tidak disokong. Objek sasaran berikut mempunyai nama lajur pendua: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Mungkin terdapat ketakkonsistenan dalam data.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Jenis muatan Delta Sahaja tidak akan mempertimbangkan perubahan yang dibuat dalam sumber antara simpanan terakhir dan jalanan seterusnya.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Ubah jenis muatan ke "Awal".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Replikasi objek berdasarkan ABAP yang tiada kod utama hanya boleh dilakukan untuk jenis muatan "Awal Sahaja".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Nyahdayakan Tangkapan Delta.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Untuk mereplikasi objek yang tiada kod utama menggunakan jenis sambungan sumber ABAP, anda mesti terlebih dahulu menyahdayakan tangkapan delta untuk jadual ini.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Ubah objek sasaran.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Tidak boleh gunakan objek sasaran kerana tangkapan delta didayakan. Anda boleh menamakan semula objek sasaran kemudian matikan tangkapan delta untuk objek yang baharu (dinamakan semula) atau memetakan objek sumber kepada objek sasaran yang tangkapan delta didayakan.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Ubah objek sasaran.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Objek sasaran tidak boleh digunakan kerana ia tiada lajur teknikal __load_package_id yang diperlukan. Anda boleh menamakan semula objek sasaran menggunakan nama yang tidak wujud lagi. Sistem kemudiannya mencipta objek baharu yang mempunyai takrifan yang sama seperti objek sumber dan mengandungi lajur teknikal. Secara alternatif, anda boleh petakan objek sasaran kepada objek sedia ada yang mempunyai lajur teknikal (__load_package_id) yang diperlukan.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Ubah objek sasaran.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Objek sasaran tidak boleh digunakan kerana ia tiada lajur teknikal __load_record_id yang diperlukan. Anda boleh menamakan semula objek sasaran menggunakan nama yang tidak wujud lagi. Sistem kemudiannya mencipta objek baharu yang mempunyai takrifan yang sama seperti objek sumber dan mengandungi lajur teknikal. Secara alternatif, anda boleh petakan objek sasaran kepada objek sedia ada yang mempunyai lajur teknikal (__load_record_id) yang diperlukan.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Ubah objek sasaran.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Objek sasaran tidak boleh digunakan kerana jenis data bagi lajur teknikal __load_record_id bukan "rentetan(44)". Anda boleh menamakan semula objek sasaran menggunakan nama yang tidak wujud lagi. Sistem kemudiannya mencipta objek baharu yang mempunyai takrifan yang sama seperti objek sumber dan seterusnya jenis data yang betul. Secara alternatif, anda boleh petakan objek sasaran kepada objek sedia ada yang mempunyai lajur teknikal (__load_record_id) yang diperlukan dengan jenis data yang betul.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Ubah objek sasaran.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Objek sasaran tidak boleh digunakan kerana ia mempunyai kod utama manakala objek sumber tiada apa-apa. Anda boleh menamakan semula objek sasaran menggunakan nama yang tidak wujud lagi. Sistem kemudiannya mencipta objek baharu yang mempunyai tarikh yang sama seperti objek sumber dan menyebabkan tiada kod utama. Secara alternatif, anda boleh petakan objek sasaran kepada objek sedia ada yang mempunyai lajur teknikal (__load_package_id) yang diperlukan dan tidak mempunyai kod utama.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Ubah objek sasaran.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Objek sasaran tidak boleh digunakan kerana ia mempunyai kod utama manakala objek sumber tiada apa-apa. Anda boleh menamakan semula objek sasaran menggunakan nama yang tidak wujud lagi. Sistem kemudiannya mencipta objek baharu yang mempunyai tarikh yang sama seperti objek sumber dan menyebabkan tiada kod utama. Secara alternatif, anda boleh petakan objek sasaran kepada objek sedia ada yang mempunyai lajur teknikal (__load_record_id) yang diperlukan dan tidak mempunyai kod utama.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Ubah objek sasaran.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Objek sasaran tidak boleh digunakan kerana jenis data bagi lajur teknikal __load_package_id bukan "binari(>=256)". Anda boleh menamakan semula objek sasaran menggunakan nama yang tidak wujud lagi. Sistem kemudiannya mencipta objek baharu yang mempunyai takrifan yang sama seperti objek sumber dan seterusnya jenis data yang betul. Secara alternatif, anda boleh petakan objek sasaran kepada objek sedia ada yang mempunyai lajur teknikal (__load_package_id) yang diperlukan dengan jenis data yang betul.
#XMSG
validationAutoRenameTargetDPID=Anda telah namakan semula lajur sasaran.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Keluarkan objek sumber.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Objek sumber tidak mempunyai lajur utama, yang tidak disokong dalam konteks ini.
#XMSG
validationAutoRenameTargetDPIDDescription=Terdapat tambahan unjuran automatik dan lajur sasaran berikut tidak dinamakan semula untuk membenarkan replikasi daripada sumber ABAP tanpa kod :{1}{1} {0} {1}{1}Ini disebabkan oleh salah satu sebab berikut:{1}{1}{2} Nama lajur khusus{1}{2} Aksara yang tiada sokongan{1}{2} Awalan khusus
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Replikasi kepada {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Menyimpan dan mengatur duduk aliran replikasi yang mempunyai {0} sebagai sasarannya pada masa ini tidak dapat dilakukan kerana kami sedang melaksanakan penyelenggaraan pada fungsi ini.
#XMSG
TargetColumnSkippedLTF=Anda telah melangkau lajur sasaran.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Anda telah melangkau lajur sasaran kerana jenis data tiada sokongan. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Lajur Masa sebagai Kod Utama.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Objek sumber mempunyai lajur masa sebagai kod utama, yang tiada sokongan dalam konteks ini.
#XMSG
validateNoPKInLTFTarget=Kod Utama tiada.
#XMSG
validateNoPKInLTFTargetDescription=Kod utama tidak ditakrifkan dalam sasaran, yang tidak disokong dalam konteks ini.
#XMSG
validateABAPClusterTableLTF=Jadual Kluster ABAP.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Objek sumber ialah jadual kluster ABAP, yang tidak disokong dalam konteks ini.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Tambahkan mana-mana data.
#YINS
welcomeText2=Untuk memulakan aliran replikasi anda, pilih sambungan dan objek sumber di sebelah kiri.

#XBUT
wizStep1=Pilih Sumber Sambungan
#XBUT
wizStep2=Pilih Kontena Sumber
#XBUT
wizStep3=Tambah Objek Sumber

#XMSG
limitDataset=Bilangan maksimum objek terlebih. Keluarkan objek sedia ada untuk menambah yang baharu, atau cipta aliran replikasi baharu.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Anda perlu mulakan aliran replikasi kepada sambungan sasaran bukan SAP ini kemudian kerana tiada jumlah keluar tersedia untuk bulan ini.
#XMSG
premiumOutBoundRFAdminWarningMsg=Pentadbir boleh meningkatkan blok Keluar Premium untuk penyewa ini, menjadikan jumlah keluar tersedia untuk bulan ini.
#XMSG
messageForToastForDPIDColumn2=Lajur baharu ditambah pada sasaran untuk {0} objek - diperlukan untuk mengendalikan rekod pendua dalam sambungan dengan objek sumber berdasarkan ABAP yang tiada kod utama.
#XMSG
PremiumInboundWarningMessage=Bergantung pada bilangan aliran replikasi dan jumlah data untuk direplikasi, sumber SAP HANA{0}yang diperlukan untuk mereplikasi data melalui {1} mungkin melebihi kapasiti tersedia untuk penyewa anda.
#XMSG
PremiumInboundWarningMsg=Bergantung pada bilangan aliran replikasi dan jumlah data untuk direplikasi,{0}sumber SAP HANA yang diperlukan untuk mereplikasi data melalui "{1}" mungkin melebihi kapasiti yang tersedia untuk penyewa anda.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Masukkan nama unjuran.
#XMSG
emptyTargetColumn=Masukkan nama lajur sasaran.
#XMSG
emptyTargetColumnBusinessName=Masukkan Nama Perniagaan lajur sasaran.
#XMSG
invalidTransformName=Masukkan nama unjuran.
#XMSG
uniqueColumnName=Namakan semula lajur sasaran.
#XMSG
copySourceColumnLbl=Salin lajur daripada objek sumber
#XMSG
renameWarning=Pastikan anda memilih nama unik semasa menamakan semula jadual sasaran. Jika jadual dengan nama baharu telah wujud dalam ruang, ia akan menggunakan takrifan jadual tersebut.

#XMSG
uniqueColumnBusinessName=Namakan semula nama perniagaan lajur sasaran.
#XMSG
uniqueSourceMapping=Pilih lajur sumber berbeza.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=Lajur sumber {0} telah digunakan oleh lajur sasaran berikut:{1}{1}{2}{1}{1} Bagi lajur sasaran ini atau lajur sasaran lain, pilih lajur sumber yang belum digunakan untuk menyimpan unjuran.
#XMSG
uniqueColumnNameDescription=Nama lajur sasaran yang anda masukkan telah wujud. Untuk membolehkan simpanan unjuran anda, anda perlu masukkan nama lajur yang unik.
#XMSG
uniqueColumnBusinessNameDesc=Nama perniagaan lajur sasaran telah wujud. Untuk menyimpan unjuran, anda mesti memasukkan nama perniagaan lajur yang unik.
#XMSG
emptySource=Pilih lajur sumber atau masukkan pemalar.
#XMSG
emptySourceDescription=Untuk mencipta entri pemetaan yang sah, anda perlu memilih lajur sumber atau masukkan nilai pemalar.
#XMSG
emptyExpression=Takrifkan pemetaan.
#XMSG
emptyExpressionDescription1=Sama ada pilih lajur sumber yang anda ingin petakan lajur sasaran atau pilih kotak semak dalam lajur {0} Fungsi / Pemalar {1}. {2} {2} Anda masukkan Fungsi secara automatik mengikut jenis data sasaran. Nilai pemalar boleh dimasukkan secara manual.
#XMSG
numberExpressionErr=Masukkan nombor.
#XMSG
numberExpressionErrDescription=Anda telah pilih jenis data berangka. Ini bermakna yang anda hanya boleh memasukkan angka dan titik perpuluhan jika berkenaan. Jangan gunakan tanda petikan tunggal.
#XMSG
invalidLength=Masukkan nilai panjang yang sah.
#XMSG
invalidLengthDescription=Panjang jenis data mesti sama atau lebih besar daripada panjang lajur sumber dan boleh antara 1 dan 5000.
#XMSG
invalidMappedLength=Masukkan nilai panjang yang sah.
#XMSG
invalidMappedLengthDescription=Panjang jenis data mesti sama atau lebih besar daripada panjang lajur sumber {0} dan boleh antara 1 dan 5000.
#XMSG
invalidPrecision=Masukkan nilai ketepatan yang sah.
#XMSG
invalidPrecisionDescription=Ketepatan mentakrifkan jumlah bilangan digit. Skala mentakrifkan bilangan digit selepas titik perpuluhan dan boleh antara 0 dan ketepatan.{0}{0} Contoh: {0}{1} Ketepatan 6, skala 2 sepadan dengan nombor seperti 1234.56.{0}{1} Ketepatan 6, skala 6 sepadan dengan nombor seperti 0.123546.{0} {0} Ketepatan dan skala untuk sasaran mesti sepadan dengan ketepatan dan skala untuk sumber supaya semua digit daripada sumber sesuai dengan medan sasaran. Contohnya, jika anda mempunyai ketetapan 6 dan skala 2 dalam sumber (dan digit selain 0 sebelum titik perpuluhan), anda tidak boleh mempunyai ketepatan 6 dan skala 6 dalam sasaran.
#XMSG
invalidPrimaryKey=Masukkan sekurang-kurangnya satu kod utama.
#XMSG
invalidPrimaryKeyDescription=Kod utama tidak ditakrifkan untuk skema ini.
#XMSG
invalidMappedPrecision=Masukkan nilai ketepatan yang sah.
#XMSG
invalidMappedPrecisionDescription1=Ketepatan mentakrifkan jumlah bilangan digit. Skala mentakrifkan bilangan digit selepas titik perpuluhan dan boleh antara 0 dan ketepatan.{0}{0} Contoh:{0}{1} Ketepatan 6, skala 2 sepadan dengan nombor seperti 1234.56.{0}{1} Ketepatan 6, skala 6 sepadan dengan nombor seperti 0.123546.{0}{0} Ketepatan jenis data mesti sama dengan atau lebih besar daripada ketepatan sumber ({2}).
#XMSG
invalidScale=Masukkan nilai skala yang sah.
#XMSG
invalidScaleDescription=Ketepatan mentakrifkan jumlah bilangan digit. Skala mentakrifkan bilangan digit selepas titik perpuluhan dan boleh antara 0 dan ketepatan.{0}{0} Contoh: {0}{1} Ketepatan 6, skala 2 sepadan dengan nombor seperti 1234.56.{0}{1} Ketepatan 6, skala 6 sepadan dengan nombor seperti 0.123546.{0} {0} Ketepatan dan skala untuk sasaran mesti sepadan dengan ketepatan dan skala untuk sumber supaya semua digit daripada sumber sesuai dengan medan sasaran. Contohnya, jika anda mempunyai ketetapan 6 dan skala 2 dalam sumber (dan digit selain 0 sebelum titik perpuluhan), anda tidak boleh mempunyai ketepatan 6 dan skala 6 dalam sasaran.
#XMSG
invalidMappedScale=Masukkan nilai skala yang sah.
#XMSG
invalidMappedScaleDescription1=Ketepatan mentakrifkan jumlah bilangan digit. Skala mentakrifkan bilangan digit selepas titik perpuluhan dan boleh antara 0 dan ketepatan.{0}{0} Contoh:{0}{1} Ketepatan 6, skala 2 sepadan dengan nombor seperti 1234.56.{0}{1} Ketepatan 6, skala 6 sepadan dengan nombor seperti 0.123546.{0}{0} Skala jenis data mesti sama dengan atau lebih besar daripada skala sumber ({2}).
#XMSG
nonCompatibleDataType=Pilih jenis data sasaran yang sesuai.
#XMSG
nonCompatibleDataTypeDescription1=Jenis data yang anda tentukan di sini mesti sesuai dengan jenis data sumber ({0}). {1}{1} Contoh: Jika lajur sumber anda mempunyai rentetan jenis data dan mengandungi huruf, anda tidak boleh gunakan jenis data perpuluhan untuk sasaran anda.
#XMSG
invalidColumnCount=Pilih lajur sumber.
#XMSG
ObjectStoreInvalidScaleORPrecision=Masukkan nilai yang sah untuk ketepatan dan skala.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=Nilai pertama ialah ketepatan, yang mentakrifkan jumlah bilangan digit. Nilai kedua ialah skala, yang mentakrifkan digit selepas titik perpuluhan. Masukkan nilai skala sasaran yang lebih besar daripada nilai skala sumber dan pastikan perbezaan antara skala sasaran yang dimasukkan dan nilai ketepatan adalah lebih besar daripada perbezaan antara skala sumber dan nilai ketepatan.
#XMSG
InvalidPrecisionORScale=Masukkan nilai yang sah untuk ketepatan dan skala.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=Nilai pertama ialah ketepatan, yang mentakrifkan jumlah bilangan digit. Nilai kedua ialah skala, yang mentakrifkan digit selepas titik perpuluhan.{0}{0}Memandangkan tiada sokongan untuk jenis data sumber dalam Google BigQuery, anda tukarkannya kepada jenis data sasaran DECIMAL. Dalam kes ini, anda hanya boleh takrifkan ketepatan antara 38 dan 76, dan skala antara 9 dan 38. Selain itu, hasil skala tolak ketepatan, yang mewakili digit sebelum titik perpuluhan, mestilah antara 29 dan 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=Nilai pertama ialah ketepatan, yang mentakrifkan jumlah bilangan digit. Nilai kedua ialah skala, yang mentakrifkan digit selepas titik perpuluhan.{0}{0}Memandangkan tiada sokongan untuk jenis data sumber dalam Google BigQuery, anda tukarkannya kepada jenis data sasaran DECIMAL. Dalam kes ini, anda mestilah takrifkan ketepatan sebagai 20 atau lebih besar. Selain itu, hasil skala tolak ketepatan, yang menunjukkan digit sebelum titik perpuluhan, mestilah antara 20 atau lebih besar.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=Nilai pertama ialah ketepatan, yang mentakrifkan jumlah bilangan digit. Nilai kedua ialah skala, yang mentakrifkan digit selepas titik perpuluhan.{0}{0}Memandangkan tiada sokongan untuk jenis data sumber dalam sasaran, anda tukarkannya kepada jenis data sasaran DECIMAL. Dalam kes ini, anda mestilah takrifkan ketepatan dengan apa-apa nombor yang lebih besar daripada atau sama dengan 1 dan kurang daripada atau sama dengan 38, dan skalanya kurang daripada atau sama dengan ketepatan.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=Nilai pertama ialah ketepatan, yang mentakrifkan jumlah bilangan digit. Nilai kedua ialah skala, yang mentakrifkan digit selepas titik perpuluhan.{0}{0}Memandangkan tiada sokongan untuk jenis data sumber dalam sasaran, anda tukarkannya kepada jenis data sasaran DECIMAL. Dalam kes ini, anda mestilah takrifkan ketepatan sebagai 20 atau lebih besar. Selain itu, hasil skala tolak ketepatan, yang menunjukkan digit sebelum titik perpuluhan, mestilah antara 20 atau lebih besar.
#XMSG
invalidColumnCountDescription=Untuk mencipta entri pemetaan yang sah, anda perlu memilih lajur sumber atau masukkan nilai pemalar.
#XMSG
duplicateColumns=Namakan semula lajur sasaran.
#XMSG
duplicateGBQCDCColumnsDesc=Nama lajur sasaran dikhaskan dalam Google BigQuery. Anda perlu namakannya semula untuk menyimpan unjuran.
#XMSG
duplicateConfluentCDCColumnsDesc=Nama lajur sasaran dikhaskan dalam Confluent. Anda perlu namakannya semula untuk menyimpan unjuran.
#XMSG
duplicateSignavioCDCColumnsDesc=Nama lajur sasaran dikhaskan dalam SAP Signavio. Anda perlu namakannya semula untuk menyimpan unjuran.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Nama lajur sasaran dikhaskan dalam MS OneLake. Anda perlu namakannya semula untuk menyimpan unjuran.
#XMSG
duplicateSFTPCDCColumnsDesc=Nama lajur sasaran dikhaskan dalam SFTP. Anda perlu namakannya semula untuk menyimpan unjuran.
#XMSG
GBQTargetNameWithPrefixUpdated1=Nama lajur sasaran mengandungi awalan yang dikhaskan dalam Google BigQuery. Anda perlu namakannya semula untuk menyimpan unjuran. {0}{0}Nama lajur sasaran tidak boleh bermula dengan mana-mana rentetan berikut:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Pendekkan nama lajur sasaran.
#XMSG
GBQtargetMaxLengthDesc=Dalam Google BigQuery, nama lajur boleh menggunakan maksimum 300 aksara. Pendekkan nama lajur sasaran untuk menyimpan unjuran.
#XMSG
invalidMappedScalePrecision=Ketepatan dan skala untuk sasaran mesti sesuai dengan ketepatan dan skala untuk sumber supaya semua digit daripada sumber sesuai dengan medan sasaran.
#XMSG
invalidMappedScalePrecisionShortText=Masukkan nilai ketepatan dan skala yang sah.
#XMSG
validationIncompatiblePKTypeDescProjection3=Satu atau lebih lajur sumber mempunyai jenis data yang tidak boleh ditakrifkan sebagai kod utama dalam Google BigQuery. Tiada kod utama yang anda akan ciptakan dalam objek sasaran.{0}{0}Jenis data sasaran berikut sesuai dengan jenis data Google BigQuery yang kod utama boleh ditakrifkan:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Batalkan tanda column __message_id.
#XMSG
uncheckColumnMessageIdDesc=Lajur: Kod Utama
#XMSG
validationOpCodeInsert=Anda mesti masukkan nilai untuk Masukkan.
#XMSG
recommendDifferentPrimaryKey=Kami mencadangkan supaya anda memilih kod utama yang berbeza pada tahap item.
#XMSG
recommendDifferentPrimaryKeyDesc=Apabila kod operasi telah ditakrifkan, adalah dicadangkan untuk memilih kod utama yang berbeza untuk indeks tatasusunan dan item, bagi mengelakkan masalah seperti penduaan lajur sebagai contoh.
#XMSG
selectPrimaryKeyItemLevel=Anda mesti memilih sekurang-kurangnya satu kod utama untuk kedua-dua pengepala dan tahap item.
#XMSG
selectPrimaryKeyItemLevelDesc=Apabila tatasusunan atau peta dikembangkan, anda mesti memilih dua kod utama, satu di tahap pengepala dan satu di tahap item.
#XMSG
invalidMapKey=Anda mesti memilih sekurang-kurangnya satu kod utama di tahap pengepala.
#XMSG
invalidMapKeyDesc=Apabila tatasusunan atau peta dikembangkan, anda mesti memilih kod utama di tahap pengepala.
#XFLD
txtSearchFields=Cari Lajur Sasaran
#XFLD
txtName=Nama
#XMSG
txtSourceColValidation=Satu atau lebih lajur sumber tidak disokong:
#XMSG
txtMappingCount=Pemetaan ({0})
#XMSG
schema=Skema
#XMSG
sourceColumn=Lajur Sumber
#XMSG
warningSourceSchema=Apa-apa perubahan yang dibuat pada skema akan menjejaskan pemetaan dalam dialog unjuran.
#XCOL
txtTargetColName=Lajur Sasaran (Nama Teknikal)
#XCOL
txtDataType=Jenis Data Sasaran
#XCOL
txtSourceDataType=Jenis Data Sumber
#XCOL
srcColName=Lajur Sumber (Nama Teknikal)
#XCOL
precision=Ketepatan
#XCOL
scale=Skala
#XCOL
functionsOrConstants=Fungsi / Pemalar
#XCOL
txtTargetColBusinessName=Lajur Sasaran (Nama Perniagaan)
#XCOL
prKey=Kod Utama
#XCOL
txtProperties=Ciri
#XBUT
txtOK=Simpan
#XBUT
txtCancel=Batalkan
#XBUT
txtRemove=Keluarkan
#XFLD
txtDesc=Perihalan
#XMSG
rftdMapping=Pemetaan
#XFLD
@lblColumnDataType=Jenis Data
#XFLD
@lblColumnTechnicalName=Nama Teknikal
#XBUT
txtAutomap=Peta Automatik
#XBUT
txtUp=Atas
#XBUT
txtDown=Bawah

#XTOL
txtTransformationHeader=Unjuran
#XTOL
editTransformation=Edit
#XTOL
primaryKeyToolip=Kod


#XMSG
rftdFilter=Penapis
#XMSG
rftdFilterColumnCount=Sumber: {0}({1})
#XTOL
rftdFilterColSearch=Cari
#XMSG
rftdFilterColNoData=Tiada lajur untuk dipaparkan
#XMSG
rftdFilteredColNoExps=Tiada ungkapan penapis
#XMSG
rftdFilterSelectedColTxt=Tambah Penapis untuk
#XMSG
rftdFilterTxt=Penapis tersedia untuk
#XBUT
rftdFilterSelectedAddColExp=Tambah Ungkapan
#YINS
rftdFilterNoSelectedCol=Pilih lajur untuk menambah penapis.
#XMSG
rftdFilterExp=Ungkapan Penapis
#XMSG
rftdFilterNotAllowedColumn=Lajur ini tidak menyokong penambahan penapis.
#XMSG
rftdFilterNotAllowedHead=Lajur Tidak Disokong
#XMSG
rftdFilterNoExp=Penapis belum ditakrifkan
#XTOL
rftdfilteredTt=Ditapis
#XTOL
rftdremoveexpTt=Keluarkan ungkapan penapis
#XTOL
validationMessageTt=Mesej Pengesahan
#XTOL
rftdFilterDateInp=Pilih tarikh
#XTOL
rftdFilterDateTimeInp=Pilih masa tarikh
#XTOL
rftdFilterTimeInp=Pilih masa
#XTOL
rftdFilterInp=Masukkan nilai
#XMSG
rftdFilterValidateEmptyMsg=Ungkapan penapis {0} pada lajur {1} kosong
#XMSG
rftdFilterValidateInvalidNumericMsg=Ungkapan penapis {0} pada lajur {1} mengandungi nilai angka yang tidak sah
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Ungkapan penapis mesti mengandungi nilai angka yang sah
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Jika skema objek sasaran telah berubah, gunakan fungsi “Petakan kepada Objek Sasaran Sedia Ada” pada halaman utama untuk menyesuaikan perubahan dan memetakan semula objek sasaran dengan sumbernya semula.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Jika jadual sasaran telah wujud dan pemetaan termasuk perubahan skema, anda mesti ubah jadual sasaran dengan sewajarnya sebelum mengatur duduk aliran replikasi.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Jika pemetaan anda melibatkan perubahan skema, anda mesti ubah jadual sasaran dengan sewajarnya sebelum mengatur duduk aliran replikasi.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Anda langkau lajur yang tiada sokongan berikut daripada takrifan sumber: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Anda langkau lajur yang tiada sokongan berikut daripada takrifan sasaran: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Tiada sokongan untuk objek yang berikut kerana objek telah terdedah untuk penggunaan: {0} {1} {0} {0} Untuk menggunakan jadual dalam aliran replikasi, tidak boleh tetapkan penggunaan semantik (dalam tetapan jadual) kepada {2}Dataset Analisis{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Tidak boleh gunakan objek sasaran kerana objek telah terdedah untuk penggunaan: {0} {0}  Untuk menggunakan jadual dalam aliran replikasi, tidak boleh tetapkan penggunaan semantik (dalam tetapan jadual) kepada {1}Dataset Analisis{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Objek sasaran dengan nama ini telah wujud. Walau bagaimanapun, tidak boleh gunakan objek sasaran kerana objek telah terdedah untuk penggunaan: {0} {0} Untuk menggunakan jadual dalam aliran replikasi, tidak boleh tetapkan penggunaan semantik (dalam tetapan jadual) kepada {1}Dataset Analisis{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Objek dengan nama ini telah wujud dalam sasaran. {0}Walau bagaimanapun, jangan gunakan objek ini sebagai objek sasaran untuk aliran replikasi kepada repositori tempatan kerana ia bukan jadual tempatan.
#XMSG:
targetAutoRenameUpdated=Anda telah namakan semula lajur sasaran.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Anda telah namakan semula lajur sasaran untuk membolehkan replikasi dalam Google BigQuery. Ini disebabkan salah satu sebab berikut: {0} {1}{2}Nama lajur khas{3}{2}Aksara yang tiada sokongan{3}{2}Awalan khas{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Anda telah namakan semula lajur sasaran untuk membolehkan replikasi dalam Confluent. Ini disebabkan salah satu sebab berikut: {0} {1}{2}Nama lajur khas{3}{2}Aksara yang tiada sokongan{3}{2}Awalan khas{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Anda telah namakan semula lajur sasaran untuk membolehkan replikasi dalam sasaran. Ini disebabkan salah satu sebab berikut: {0} {1}{2}Aksara yang tiada sokongan{3}{2}Awalan khusus{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Anda telah namakan semula lajur sasaran untuk membolehkan replikasi dalam sasaran. Ini disebabkan salah satu sebab berikut: {0} {1}{2}Nama lajur khusus{3}{2}Aksara yang tiada sokongan{3}{2}Awalan khusus{3}{4}
#XMSG:
targetAutoDataType=Anda telah ubah jenis data sasaran.
#XMSG:
targetAutoDataTypeDesc=Anda telah ubah jenis data sasaran kepada {0} kerana jenis data sumber tiada sokongan dalam Google BigQuery.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Anda telah ubah jenis data sasaran kepada {0} kerana jenis data sumber tiada sokongan dalam sambungan sasaran.
#XMSG
projectionGBQUnableToCreateKey=Tiada kod utama yang anda akan cipta.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=Dalam Google BigQuery, terdapat sokongan untuk maksimum 16 kod utama tetapi objek sumber mempunyai bilangan kod utama yang lebih besar. Tiada kod utama yang anda akan cipta dalam objek sasaran.
#XMSG
HDLFNoKeyError=Takrifkan satu atau lebih lajur sebagai kod utama.
#XMSG
HDLFNoKeyErrorDescription=Untuk mereplikakan objek, anda mesti mentakrifkan satu atau lebih lajur sebagai kod utama.
#XMSG
HDLFNoKeyErrorExistingTarget=Takrifkan satu atau lebih lajur sebagai kod utama.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Untuk mereplikasi data ke objek sasaran sedia ada, data mesti mempunyai satu atau beberapa lajur yang ditakrifkan sebagai kod utama. {0} {0} Anda mempunyai pilihan yang berikut untuk mentakrifkan satu atau beberapa lajur sebagai kod utama: {0}{0}{1} Gunakan editor jadual tempatan untuk mengubah objek sasaran sedia ada. Kemudian muatkan semula aliran replikasi.{0}{0}{1} Namakan semula objek sasaran dalam aliran replikasi. Ini akan mencipta objek baharu sebaik sahaja jalanan bermula. Selepas menamakan semula, anda boleh mentakrifkan satu atau beberapa lajur sebagai kod utama dalam unjuran.{0}{0}{1} Petakan objek kepada objek sasaran sedia ada lain yang satu atau beberapa lajur ditakrifkan sebagai kod utama.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Kod utama berubah.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=Berbanding objek sumber, anda telah mentakrifkan lajur yang berbeza sebagai kod utama untuk objek sasaran. Pastikan bahawa lajur ini mengenal pasti secara unik semua baris untuk mengelakkan kerosakan data semasa mereplikasi data kemudian. {0} {0} Dalam objek sumber, lajur yang berikut ditakrifkan sebagai kod utama: {0} {1}
#XMSG
duplicateDPIDColumns=Namakan semula lajur sasaran.
#XMSG
duplicateDPIDDColumnsDesc1=Lajur sasaran ini disimpan untuk lajur teknikal. Masukkan nama berbeza untuk menyimpan unjuran.
#XMSG:
targetAutoRenameDPID=Anda telah namakan semula lajur sasaran.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Anda telah namakan semula lajur sasaran untuk membolehkan replikasi daripada sumber ABAP tanpa kod. Ini disebabkan salah satu sebab berikut: {0} {1}{2}Nama lajur khas{3}{2}Aksara yang tiada sokongan{3}{2}Awalan khas{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle={0} Tetapan Sasaran
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle={0} Tetapan Sumber
#XBUT
connectionSettingSave=Simpan
#XBUT
connectionSettingCancel=Batalkan
#XBUT: Button to keep the object level settings
txtKeep=Simpan
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Tulis ganti
#XFLD
targetConnectionThreadlimit=Had Jaluran Sasaran untuk Muatan Awal (1-100)
#XFLD
connectionThreadLimit=Had Jaluran Sumber untuk Muatan Awal (1-100)
#XFLD
maxConnection=Had Jaluran Replikasi (1-100)
#XFLD
kafkaNumberOfPartitions=Bilangan Bahagian
#XFLD
kafkaReplicationFactor=Faktor Replikasi
#XFLD
kafkaMessageEncoder=Pengekod Mesej
#XFLD
kafkaMessageCompression=Pemampatan Mesej
#XFLD
fileGroupDeltaFilesBy=Kumpul Delta dengan
#XFLD
fileFormat=Jenis Fail
#XFLD
csvEncoding=Pengekodan CSV
#XFLD
abapExitLbl=Keluar ABAP
#XFLD
deltaPartition=Kiraan Jaluran Objek untuk Muatan Delta (1-10)
#XFLD
clamping_Data=Masukkan Pemangkasan Data Semula
#XFLD
fail_On_Incompatible=Masukkan Data yang Sesuai Semula
#XFLD
maxPartitionInput=Bilangan Bahagian Maksimum
#XFLD
max_Partition=Takrifkan Bilangan Bahagian Maksimum
#XFLD
include_SubFolder=Termasuk Subfolder
#XFLD
fileGlobalPattern=Corak Global untuk Nama Fail
#XFLD
fileCompression=Pemampatan Fail
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Pembatas Fail
#XFLD
fileIsHeaderIncluded=Pengepala Fail
#XFLD
fileOrient=Orient
#XFLD
gbqWriteMode=Mod Tulis
#XFLD
suppressDuplicate=Tahan Penduaan
#XFLD
apacheSpark=Dayakan Keserasian Apache Spark
#XFLD
clampingDatatypeCb=Pengapit Jenis Data Titik Apungan Perpuluhan
#XFLD
overwriteDatasetSetting=Tulis Ganti Tetapan Sasaran pada Tahap Objek
#XFLD
overwriteSourceDatasetSetting=Tulis Ganti Tetapan Sumber pada Tahap Objek
#XMSG
kafkaInvalidConnectionSetting=Masukkan nombor antara {0} hingga {1}.
#XMSG
MinReplicationThreadErrorMsg=Masukkan nombor yang lebih besar daripada {0}.
#XMSG
MaxReplicationThreadErrorMsg=Masukkan nombor yang lebih kecil daripada {0}.
#XMSG
DeltaThreadErrorMsg=Masukkan nilai antara 1 dan 10.
#XMSG
MaxPartitionErrorMsg=Masukkan nilai antara 1 <= x <= 2147483647.Nilai lalai ialah 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Masukkan integer antara {0} dan {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Gunakan Faktor Replikasi Broker
#XFLD
serializationFormat=Format Penyirian
#XFLD
compressionType=Jenis Pemampatan
#XFLD
schemaRegistry=Gunakan Pendaftaran Skema
#XFLD
subjectNameStrat=Strategi Nama Subjek
#XFLD
compatibilityType=Jenis Keserasian
#XFLD
confluentTopicName=Nama Topik
#XFLD
confluentRecordName=Nama Rekod
#XFLD
confluentSubjectNamePreview=Pratonton Nama Subjek
#XMSG
serializationChangeToastMsgUpdated2=Format penyirian diubah kepada JSON kerana daftar skema belum didayakan. Untuk mengubah format penyirian kembali kepada AVRO, anda mesti mendayakan daftar skema terlebih dahulu.
#XBUT
confluentTopicNameInfo=Nama topik sentiasa berdasarkan nama objek sasaran. Anda boleh mengubahnya dengan menamakan semula objek sasaran.
#XMSG
emptyRecordNameValidationHeaderMsg=Masukkan nama rekod.
#XMSG
emptyPartionHeader=Masukkan bilangan bahagian.
#XMSG
invalidPartitionsHeader=Masukkan bilangan bahagian yang sah.
#XMSG
invalidpartitionsDesc=Masukkan nombor antara 1 dan 200,000.
#XMSG
emptyrFactorHeader=Masukkan faktor replikasi.
#XMSG
invalidrFactorHeader=Masukkan faktor replikasi yang sah.
#XMSG
invalidrFactorDesc=Masukkan nombor antara 1 dan 32,767.
#XMSG
emptyRecordNameValidationDescMsg=Jika format penyirian "AVRO" digunakan, hanya aksara berikut disokong: {0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(garis bawah)
#XMSG
validRecordNameValidationHeaderMsg=Masukkan nama rekod yang sah.
#XMSG
validRecordNameValidationDescMsgUpdated=Disebabkan format penyirian "AVRO" digunakan, nama rekod mesti terdiri daripada aksara abjad angka (A-Z, a-z, 0-9) dan garis bawah (_). Ia mesti bermula dengan huruf atau garis bawah.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled="Kiraan Jaluran Objek untuk Muatan Delta" boleh ditetapkan sebaik sahaja satu atau beberapa objek mempunyai jenis muatan "Awal dan Delta".
#XMSG
invalidTargetName=Nama lajur tidak sah
#XMSG
invalidTargetNameDesc=Nama lajur sasaran mesti terdiri daripada aksara abjad angka (A-Z, a-z, 0-9) dan garis bawah (_) sahaja.
#XFLD
consumeOtherSchema=Gunakan Versi Skema Lain
#XFLD
ignoreSchemamissmatch=Abaikan Skema Tidak Sepadan
#XFLD
confleuntDatatruncation=Masukkan Pemangkasan Data semula
#XFLD
isolationLevel=Tahap Pemencilan
#XFLD
confluentOffset=Titik Permulaan
#XFLD
signavioGroupDeltaFilesByText=Tiada
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=Tidak
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=Tidak

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Unjuran
#XBUT
txtAdd=Tambah
#XBUT
txtEdit=Edit
#XMSG
transformationText=Tambahkan unjuran untuk menyediakan penapis atau pemetaan.
#XMSG
primaryKeyRequiredText=Pilih kod utama dengan Konfigurasikan Skema.
#XFLD
lblSettings=Tetapan
#XFLD
lblTargetSetting={0}: Tetapan Sasaran
#XMSG
@csvRF=Pilih fail yang mengandungi takrifan skema yang anda ingin gunakan pada semua fail dalam folder.
#XFLD
lblSourceColumns=Lajur Sumber
#XFLD
lblJsonStructure=Struktur JSON
#XFLD
lblSourceSetting={0}: Tetapan Sumber
#XFLD
lblSourceSchemaSetting={0}: Tetapan Skema Sumber
#XBUT
messageSettings=Tetapan Mesej
#XFLD
lblPropertyTitle1=Sifat Objek
#XFLD
lblRFPropertyTitle=Sifat Aliran Replikasi
#XMSG
noDataTxt=Tiada lajur untuk dipaparkan.
#XMSG
noTargetObjectText=Tiada objek sasaran dipilih.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Lajur Sasaran
#XMSG
searchColumns=Cari Lajur
#XTOL
cdcColumnTooltip=Lajur untuk Tangkapan Delta
#XMSG
sourceNonDeltaSupportErrorUpdated=Objek sasaran tidak menyokong tangkapan delta.
#XMSG
targetCDCColumnAdded=Anda telah menambah 2 lajur sasaran untuk tangkapan delta.
#XMSG
deltaPartitionEnable=Ambang Jaluran Objek untuk Muatan Delta ditambah pada tetapan sumber.
#XMSG
attributeMappingRemovalTxt=Mengeluarkan pemetaan tidak sah yang tidak disokong untuk objek sasaran baharu.
#XMSG
targetCDCColumnRemoved=Anda telah mengeluarkan 2 lajur sasaran yang digunakan untuk tangkapan delta.
#XMSG
replicationLoadTypeChanged=Jenis muatan berubah ke "Awal dan Delta".
#XMSG
sourceHDLFLoadTypeError=Ubah jenis muatan ke "Awal dan Delta".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Untuk mereplikakan objek daripada sambungan sumber dengan jenis sambungan SAP HANA Cloud, Fail Data Lake kepada SAP Datasphere, anda mesti menggunakan jenis muatan "awal dan delta".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Dayakan Tangkapan Delta.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Untuk mereplikakan objek daripada sambungan sumber dengan jenis sambungan SAP HANA Cloud, Fail Data Lake kepada SAP Datasphere, anda mesti mendayakan tangkapan delta.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Ubah Objek sasaran.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Dayakan tangkapan delta untuk menggunakan objek sasaran. Anda boleh menamakan semula objek sasaran (yang membenarkan objek baharu dengan tangkapan delta dicipta) atau memetakannya kepada objek sedia ada dengan tangkapan delta didayakan.
#XMSG
deltaPartitionError=Masukkan kiraan jaluran objek yang sah untuk muatan delta.
#XMSG
deltaPartitionErrorDescription=Masukkan nilai antara 1 dan 10.
#XMSG
deltaPartitionEmptyError=Masukkan kiraan jaluran objek untuk muatan delta.
#XFLD
@lblColumnDescription=Perihalan
#XMSG
@lblColumnDescriptionText1=Untuk tujuan teknikal - pengendalian rekod pendua disebabkan oleh isu semasa replikasi objek sumber berdasarkan ABAP yang tiada kod utama.
#XFLD
storageType=Storan
#XFLD
skipUnmappedColLbl=Langkau Lajur Tidak Dipetakan
#XFLD
abapContentTypeLbl=Jenis Kandungan
#XFLD
autoMergeForTargetLbl=Gabungkan Data Secara Automatik
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Umum
#XFLD
lblBusinessName=Nama Perniagaan
#XFLD
lblTechnicalName=Nama Teknikal
#XFLD
lblPackage=Pakej
#XFLD
statusPanel=Status Jalanan
#XBTN: Schedule dropdown menu
SCHEDULE=Jadual
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Edit Jadual
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Padam Jadual
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Cipta Jadual
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Cuba semakan pengesahan jadual semula
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Cuba atur duduk jadual kemudian kerana aliran replikasi sedang diatur duduk.{0}Tunggu sehingga aliran replikasi diatur duduk.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Bagi aliran replikasi yang mengandungi objek dengan jenis muatan "Awal dan Delta", tidak boleh mencipta jadual.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Bagi aliran replikasi yang mengandungi objek dengan jenis muatan "Awal dan Delta/Delta Sahaja", tidak boleh mencipta jadual.
#XFLD : Scheduled popover
SCHEDULED=Dijadualkan
#XFLD
CREATE_REPLICATION_TEXT=Cipta Aliran Replikasi
#XFLD
EDIT_REPLICATION_TEXT=Edit Aliran Replikasi
#XFLD
DELETE_REPLICATION_TEXT=Padam Aliran Replikasi
#XFLD
REFRESH_FREQUENCY=Kekerapan
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Tidak boleh atur duduk aliran replikasi kerana jadual sedia ada{0} belum menyokong jenis muatan "Awal dan Delta".{0}{0}Untuk mengatur duduk aliran replikasi, anda mesti menetapkan jenis muatan bagi semua objek{0} kepada "Awal sahaja". Secara alternatif, anda boleh memadam jadual, mengatur duduk {0}aliran replikasi dan kemudian memulakan jalanan baharu. Ini menghasilkan jalanan tanpa {0}akhir, yang juga menyokong objek dengan jenis muatan "Awal dan Delta".
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Aliran replikasi tidak berjaya diatur duduk kerana jadual sedia ada{0} belum menyokong jenis muatan "Awal dan Delta/Delta Sahaja".{0}{0}Untuk mengatur duduk aliran replikasi, anda mesti menetapkan jenis muatan bagi semua objek{0} kepada "Awal sahaja". Secara alternatif, anda boleh memadam jadual, mengatur duduk {0}aliran replikasi dan kemudian memulakan jalanan baharu. Ini menghasilkan jalanan tanpa {0}akhir, yang juga menyokong objek dengan jenis muatan "Awal dan Delta/Delta Sahaja".
#XMSG
SCHEDULE_EXCEPTION=Cuba dapatkan semula butiran jadual
#XFLD: Label for frequency column
everyLabel=Setiap
#XFLD: Plural Recurrence text for Hour
hoursLabel=Jam
#XFLD: Plural Recurrence text for Day
daysLabel=Hari
#XFLD: Plural Recurrence text for Month
monthsLabel=Bulan
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minit
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Cuba dapatkan semula maklumat tentang kemungkinan jadual.
#XFLD :Paused field
PAUSED=Dihentikan Sementara
#XMSG
navToMonitoring=Buka dalam Pemantau Aliran
#XFLD
statusLbl=Status
#XFLD
lblLastRunExecuted=Permulaan Jalanan Akhir
#XFLD
lblLastExecuted=Jalanan Akhir
#XFLD: Status text for Completed
statusCompleted=Selesai
#XFLD: Status text for Running
statusRunning=Sedang Berjalan
#XFLD: Status text for Failed
statusFailed=Gagal
#XFLD: Status text for Stopped
statusStopped=Dihentikan
#XFLD: Status text for Stopping
statusStopping=Pemberhentian
#XFLD: Status text for Active
statusActive=Aktif
#XFLD: Status text for Paused
statusPaused=Dihentikan Sementara
#XFLD: Status text for not executed
lblNotExecuted=Belum Dilaksanakan Lagi
#XFLD
messagesSettings=Tetapan Mesej
#XTOL
@validateModel=Mesej Pengesahan
#XTOL
@hierarchy=Hierarki
#XTOL
@columnCount=Bilangan Lajur
#XMSG
VAL_PACKAGE_CHANGED=Anda telah umpukkan objek ini kepada pakej "{1}". Klik “Simpan” untuk mengesahkan perubahan ini. Ambil perhatian bahawa umpukan kepada pakej dalam editor ini tidak boleh dibatalkan selepas anda menyimpannya.
#XMSG
MISSING_DEPENDENCY=Tidak boleh selesaikan semula kebersandaran objek "{0}" dalam pakej "{1}".
#XFLD
deltaLoadInterval=Selang Muatan Delta
#XFLD
lblHour=Jam (0-24)
#XFLD
lblMinutes=Minit (0-59)
#XMSG
maxHourOrMinErr=Masukkan nilai antara 0 dan {0}
#XMSG
maxDeltaInterval=Nilai maksimum selang muatan delta ialah 24 jam.{0}Ubah nilai minit atau nilai jam sewajarnya.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Laluan Kontena Sasaran
#XFLD
confluentSubjectName=Nama Subjek
#XFLD
confluentSchemaVersion=Versi Skema
#XFLD
confluentIncludeTechKeyUpdated=Termasuk Kod Teknikal
#XFLD
confluentOmitNonExpandedArrays=Keluarkan Tatasusunan Tidak Dikembangkan
#XFLD
confluentExpandArrayOrMap=Kembangkan Tatasusunan atau Peta
#XCOL
confluentOperationMapping=Pemetaan Operasi
#XCOL
confluentOpCode=Kod operasi
#XFLD
confluentInsertOpCode=Masukkan
#XFLD
confluentUpdateOpCode=Kemas Kini
#XFLD
confluentDeleteOpCode=Padam
#XFLD
expandArrayOrMapNotSelectedTxt=Tidak Dipilih
#XFLD
confluentSwitchTxtYes=Ya
#XFLD
confluentSwitchTxtNo=Tidak
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Ralat
#XTIT
executeWarning=Amaran
#XMSG
executeunsavederror=Simpan aliran replikasi anda sebelum anda menjalankannya.
#XMSG
executemodifiederror=Tiada perubahan disimpan dalam aliran replikasi. Simpan aliran replikasi.
#XMSG
executeundeployederror=Anda mesti kerahkan aliran replikasi sebelum anda boleh menjalankannya.
#XMSG
executedeployingerror=Sila tunggu untuk pengerahan selesai.
#XMSG
msgRunStarted=Jalanan dimulakan
#XMSG
msgExecuteFail=Tidak berjaya menjalankan aliran replikasi
#XMSG
titleExecuteBusy=Tunggu.
#XMSG
msgExecuteBusy=Kami menyediakan data anda untuk menjalankan aliran replikasi.
#XTIT
executeConfirmDialog=Amaran
#XMSG
msgExecuteWithValidations=Aliran replikasi mempunyai ralat pengesahan. Jalanan aliran data mungkin tidak berjaya.
#XMSG
msgRunDeployedVersion=Terdapat perubahan untuk atur duduk. Versi terakhir atur duduk bagi aliran replikasi akan dijalankan. Adakah anda ingin teruskan?
#XBUT
btnExecuteAnyway=Jalankan Juga
#XBUT
btnExecuteClose=Tutup
#XBUT
loaderClose=Tutup
#XTIT
loaderTitle=Memuat
#XMSG
loaderText=Mendapatkan butiran dari pelayan
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Anda perlu mulakan aliran replikasi kepada sambungan sasaran bukan SAP ini kemudian
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=kerana tiada jumlah keluar tersedia untuk bulan ini.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Pentadbir boleh meningkatkan blok Keluar Premium untuk penyewa
#XMSG
premiumOutBoundRFAdminErrMsgPart2=ini, menjadikan jumlah keluar tersedia untuk bulan ini.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Ralat
#XTIT
deployInfo=Maklumat
#XMSG
deployCheckFailException=Pengecualian berlaku semasa pengerahan
#XMSG
deployGBQFFDisabled=Mengatur duduk aliran replikasi dengan sambungan sasaran ke Google BigQuery pada masa ini tidak dapat dilakukan kerana kami sedang melaksanakan penyelenggaraan pada fungsi ini.
#XMSG
deployKAFKAFFDisabled=Mengatur duduk aliran replikasi dengan sambungan sasaran ke Apache Kafka pada masa ini tidak dapat dilakukan kerana kami sedang melaksanakan penyelenggaraan pada fungsi ini.
#XMSG
deployConfluentDisabled=Mengatur duduk aliran replikasi dengan sambungan sasaran ke Confluent Kafka pada masa ini tidak dapat dilakukan kerana kami sedang melaksanakan penyelenggaraan pada fungsi ini.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Untuk objek sasaran berikut, jadual tangkapan delta telah digunakan oleh jadual lain dalam repositori: {0} Anda mesti namakan semula objek sasaran ini untuk memastikan yang nama jadual tangkapan delta berkaitan adalah unik sebelum anda boleh mengatur duduk aliran replikasi.
#XMSG
deployDWCSourceFFDisabled=Mengatur duduk aliran replikasi yang mempunyai SAP Datasphere sebagai sumbernya pada masa ini tidak dapat dilakukan kerana kami sedang melaksanakan penyelenggaraan pada fungsi ini.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Mengatur duduk aliran replikasi yang mengandungi jadual tempatan didayakan delta sebagai sumber pada masa ini tidak dapat dilakukan kerana kami sedang melaksanakan penyelenggaraan pada fungsi ini.
#XMSG
deployHDLFSourceFFDisabled=Mengatur duduk aliran replikasi yang mempunyai sambungan sumber dengan jenis sambungan SAP HANA Cloud, Fail Data Lake pada masa ini tidak dapat dilakukan kerana kami sedang melaksanakan penyelenggaraan.
#XMSG
deployObjectStoreAsSourceFFDisabled=Mengatur duduk aliran replikasi yang mempunyai penyedia storan cloud sebagai sumber tidak dapat dilakukan pada masa ini.
#XMSG
deployConfluentSourceFFDisabled=Mengatur duduk aliran replikasi yang mempunyai Confluent Kafka sebagai sumbernya pada masa ini tidak dapat dilakukan kerana kami sedang melaksanakan penyelenggaraan pada fungsi ini.
#XMSG
deployMaxDWCNewTableCrossed=Bagi aliran replikasi yang besar, tidak boleh "simpan dan atur duduk" mereka dalam satu masa. Simpan aliran replikasi anda terlebih dahulu kemudian atur duduknya.
#XMSG
deployInProgressInfo=Pengerahan sedang berjalan.
#XMSG
deploySourceObjectInUse=Objek sumber {0} telah digunakan dalam aliran replikasi {1}.
#XMSG
deployTargetSourceObjectInUse=Objek sumber {0} telah digunakan dalam aliran replikasi {1}. Objek sasaran {2} telah digunakan dalam aliran replikasi {3}.
#XMSG
deployReplicationFlowCheckError=Cuba lagi untuk mengesahkan aliran replikasi: {0}
#XMSG
preDeployTargetObjectInUse=Objek sasaran {0} sedang digunakan dalam aliran replikasi {1}, dan anda tidak boleh mempunyai objek sasaran yang sama dalam dua aliran replikasi yang berbeza. Pilih objek sasaran lain dan cuba lagi.
#XMSG
runInProgressInfo=Aliran replikasi telah berjalan.
#XMSG
deploySignavioTargetFFDisabled=Mengatur duduk aliran replikasi yang mempunyai SAP Signavio sebagai sasarannya pada masa ini tidak dapat dilakukan kerana kami sedang melaksanakan penyelenggaraan pada fungsi ini.
#XMSG
deployHanaViewAsSourceFFDisabled=Pada masa ini, tidak boleh atur duduk aliran replikasi yang mempunyai paparan sebagai objek sumber bagi sambungan sumber yang dipilih. Cuba lagi kemudian.
#XMSG
deployMsOneLakeTargetFFDisabled=Mengatur duduk aliran replikasi yang mempunyai MS OneLake sebagai sasarannya pada masa ini tidak dapat dilakukan kerana kami sedang melaksanakan penyelenggaraan pada fungsi ini.
#XMSG
deploySFTPTargetFFDisabled=Mengatur duduk aliran replikasi yang mempunyai SFTP sebagai sasarannya pada masa ini tidak dapat dilakukan kerana kami sedang melaksanakan penyelenggaraan pada fungsi ini.
#XMSG
deploySFTPSourceFFDisabled=Mengatur duduk aliran replikasi yang mempunyai SFTP sebagai sumbernya pada masa ini tidak dapat dilakukan kerana kami sedang melaksanakan penyelenggaraan pada fungsi ini.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Nama Teknikal
#XFLD
businessNameInRenameTarget=Nama Perniagaan
#XTOL
renametargetDialogTitle=Namakan Semula Objek Sasaran
#XBUT
targetRenameButton=Namakan Semula
#XBUT
targetRenameCancel=Batalkan
#XMSG
mandatoryTargetName=Anda mesti memasukkan nama.
#XMSG
dwcSpecialChar=_(garis bawah).hanya membenarkan aksara khas dibenarkan.
#XMSG
dwcWithDot=Nama jadual sasaran boleh terdiri daripada huruf Latin, nombor, garis bawah (_), dan noktah (.). Aksara pertama mestilah huruf, nombor atau garis bawah (bukan noktah).
#XMSG
nonDwcSpecialChar=Aksara khas yang dibenarkan ialah _(garis bawah) -(sempang) .(titik)
#XMSG
firstUnderscorePattern=Nama mestilah tidak bermula dengan _(garis bawah)

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: Paparan Penyata Jadual Cipta SQL
#XMSG
sqlDialogMaxPKWarning=Dalam Google BigQuery, maksimum 16 kod utama mempunyai sokongan dan objek sumber mempunyai nombor yang lebih besar. Oleh itu, tiada kod utama yang anda takrifkan dalam penyata ini.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Satu atau lebih lajur sumber mempunyai jenis data yang tidak boleh ditakrifkan sebagai kod utama dalam Google BigQuery. Oleh itu, tiada kod utama yang anda takrifkan dalam kes ini. Dalam Google BigQuery, hanya jenis data berikut boleh mempunyai kod utama: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Salin dan Tutup
#XBUT
closeDDL=Tutup
#XMSG
copiedToClipboard=Disalin ke papan klip


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Objek ''{0}'' perlu mempunyai akhir untuk menjadi sebahagian daripada rantaian tugas (kerana ia termasuk objek dengan jenis muatan Awal dan Delta/Delta Sahaja).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Objek ''{0}'' perlu mempunyai akhir untuk menjadi sebahagian daripada rantaian tugas (kerana ia termasuk objek dengan jenis muatan Awal dan Delta).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objek "{0}" tidak boleh ditambah ke rantaian tugas.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Terdapat objek sasaran yang tidak disimpan. Simpan semula.{0}{0} Tingkah laku ciri ini telah berubah: Pada masa lalu, objek sasaran hanya dicipta dalam persekitaran sasaran apabila aliran replikasi diatur duduk.{0} Kini objek telah dicipta apabila aliran replikasi disimpan. Aliran replikasi anda dicipta sebelum perubahan ini dan mengandungi objek baharu.{0} Anda perlu menyimpan aliran replikasi sekali lagi sebelum mengatur duduknya supaya objek baharu dimasukkan dengan betul.
#XMSG
confirmChangeContentTypeMessage=Anda akan mengubah jenis kandungan. Jika anda berbuat demikian, semua unjuran sedia ada akan dipadam.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Nama Subjek
#XFLD
schemaDialogVersionName=Versi Skema
#XFLD
includeTechKey=Termasuk Kod Teknikal
#XFLD
segementButtonFlat=Rata
#XFLD
segementButtonNested=Bersarang
#XMSG
subjectNamePlaceholder=Cari Nama Subjek

#XMSG
@EmailNotificationSuccess=Konfigurasi pemberitahuan e-mel masa jalanan disimpan.

#XFLD
@RuntimeEmailNotification=Pemberitahuan E-mel Masa Jalanan

#XBTN
@TXT_SAVE=Simpan


