#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Replikointivirta

#XFLD: Edit Schema button text
editSchema=<PERSON>ok<PERSON><PERSON> kaaviota

#XTIT : Properties heading
configSchema=<PERSON><PERSON><PERSON><PERSON><PERSON> kaavio

#XFLD: save changed button text
applyChanges=Ota muutokset käyttöön


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Valitse lähdeyhteys
#XFLD
sourceContainernEmptyText=Valitse säilö
#XFLD
targetConnectionEmptyText=Valitse kohdeyhteys
#XFLD
targetContainernEmptyText=Valitse säilö
#XFLD
sourceSelectObjectText=Valitse lähdeobjekti
#XFLD
sourceObjectCount=Lähdeobjektit ({0})
#XFLD
targetObjectText=Kohdeobjektit
#XFLD
confluentBrowseContext=Valitse konteksti
#XBUT
@retry=Yritä uudelleen
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Vuokralaisen päivitys käynnissä.

#XTOL
browseSourceConnection=Selaa lähdeyhteyttä
#XTOL
browseTargetConnection=Selaa kohdeyhteyttä
#XTOL
browseSourceContainer=Selaa lähdesäilöä
#XTOL
browseAndAddSourceDataset=Lisää lähdeobjektit
#XTOL
browseTargetContainer=Selaa kohdesäilöä
#XTOL
browseTargetSetting=Selaa kohdeasetuksia
#XTOL
browseSourceSetting=Selaa lähdeasetuksia
#XTOL
sourceDatasetInfo=Tiedot
#XTOL
sourceDatasetRemove=Poista
#XTOL
mappingCount=Tämä edustaa muuhun kuin nimeen perustuvien kohdistusten/lausekkeiden kokonaismäärää.
#XTOL
filterCount=Tämä edustaa suodatinehtojen kokonaismäärää.
#XTOL
loading=Ladataan...
#XCOL
deltaCapture=Deltasieppaus
#XCOL
deltaCaptureTableName=Deltasieppaustaulu
#XCOL
loadType=Lataa tyyppi
#XCOL
deleteAllBeforeLoading=Poista kaikki ennen latausta
#XCOL
transformationsTab=Ennusteet
#XCOL
settingsTab=Asetukset

#XBUT
renameTargetObjectBtn=Nimeä kohdeobjekti uudelleen
#XBUT
mapToExistingTargetObjectBtn=Kohdista olemassa olevaan kohdeobjektiin
#XBUT
changeContainerPathBtn=Muuta säilön polkua
#XBUT
viewSQLDDLUpdated=Näytä SQL-lause Luo taulu
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Lähdeobjekti ei tue deltasieppausta, mutta valitun kohdeobjektiin deltasieppaus on aktivoituna.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Kohdeobjektia ei voi käyttää, koska deltasieppaus on aktivoitu,{0}kun taas lähdeobjekti ei tue deltasieppausta.{1}Voit valita toisen kohdeobjektin, joka ei tue deltasieppausta.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Tämänniminen kohdeobjekti on jo olemassa. Sitä ei kuitenkaan voi käyttää,{0}koska deltasieppaus on aktivoitu, kun taas lähdeobjekti ei tue{0}deltasieppausta.{1}Voit joko syöttää sellaisen olemassa olevan kohdeobjektin nimen,{0}joka ei tue deltasieppausta, tai syöttää vielä olemattoman objektin nimen.
#XBUT
copySQLDDLUpdated=Kopioi SQL-lause Luo taulu
#XMSG
targetObjExistingNoCDCColumnUpdated=Google BigQueryssa olemassa olevien taulujen täytyy sisältää seuraavat sarakkeet muutostietojen rekisteröintiä (CDC) varten:{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Seuraavia lähdeobjekteja ei tueta, koska niillä ei ole ensisijaista avainta tai ne käyttävät yhteyttä, joka ei vastaa ensisijaisen avaimen noudon ehtoja:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Katso mahdollinen ratkaisu SAP KBA:sta 3531135.
#XLST: load type list values
initial=Vain ensimmäinen
@emailUpdateError=Virhe sähköposti-ilmoitusluettelon päivityksessä

#XLST
initialDelta=Ensimmäinen ja delta

#XLST
deltaOnly=Vain delta
#XMSG
confluentDeltaLoadTypeInfo=Confluent Kafka -lähdettä varten tuetaan vain lataustyyppiä Ensimmäinen ja Delta.
#XMSG
confirmRemoveReplicationObject=Vahvistatko, että haluat poistaa replikoinnin?
#XMSG
confirmRemoveReplicationTaskPrompt=Tämä toimi poistaa valitut replikoinnit. Haluatko jatkaa?
#XMSG
confirmTargetConnectionChangePrompt=Tämä toimi nollaa kohdeyhteyden, kohdesäilön ja poistaa kaikki kohdeobjektit. Haluatko jatkaa?
#XMSG
confirmTargetContainerChangePrompt=Tämä toimi nollaa kohdesäilön ja poistaa kaikki olemassa olevat kohdeobjektit. Haluatko jatkaa?
#XMSG
confirmRemoveTransformObject=Vahvistatko, että haluat poistaa projektion {0}?
#XMSG
ErrorMsgContainerChange=On tapahtunut virhe muutettaessa säilöpolkua.
#XMSG
infoForUnsupportedDatasetNoKeys=Seuraavia lähdeobjekteja ei tueta, koska niillä ei ole ensisijaista avainta:
#XMSG
infoForUnsupportedDatasetView=Seuraavia Näkymät-tyyppisiä lähdeobjekteja ei tueta:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Seuraavaa lähdeobjektia ei tueta, koska se on syöttöparametreja sisältävä SQL-näkymä:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Seuraavia lähdeobjekteja ei tueta, koska niissä on poistettu käytöstä poiminta:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Confluent-yhteyksissä ainoat sallitut sarjallistamismuodot ovat AVRO ja JSON. Seuraavia objekteja ei tueta, koska niissä käytetään eri sarjallistamismuotoa:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Seuraavien objektien kaaviota ei voi noutaa. Valitse sopiva konteksti tai tarkista kaaviorekisterin konfiguraatio
#XTOL: warning dialog header on deleting replication task
deleteHeader=Poista
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Poista kaikki ennen latausta -asetusta ei tueta Google BigQuerya varten.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Poista kaikki ennen latausta -asetus poistaa objektin (aiheen) ja luo sen uudelleen ennen jokaista replikointia. Se myös poistaa kaikki kohdistetut ilmoitukset.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Poista kaikki ennen latausta -asetusta ei tueta tässä kohdetyypissä.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Tekninen nimi
#XCOL
connBusinessName=Liiketoiminnallinen nimi
#XCOL
connDescriptionName=Kuvaus
#XCOL
connType=Tyyppi
#XMSG
connTblNoDataFoundtxt=Yhteyksiä ei löytynyt
#XMSG
connectionError=On tapahtunut virhe noudettaessa yhteyksiä.
#XMSG
connectionCombinationUnsupportedErrorTitle=Yhteysyhdistelmää ei tueta
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Replikointia {0} - {1} ei tueta tällä hetkellä.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Yhteystyypin yhdistelmää ei tueta
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Replikointia kohteeseen {0} ei tueta yhteydestä, jonka yhteystyyppi on SAP HANA Cloud -tietojärven tiedostot. Voit replikoida vain SAP Datasphereen.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Valitse
#XBUT
containerCancelBtn=Peruuta
#XTOL
containerSelectTooltip=Valitse
#XTOL
containerCancelTooltip=Peruuta
#XMSG
containerContainerPathPlcHold=Säilöpolku
#XFLD
containerContainertxt=Säilö
#XFLD
confluentContainerContainertxt=Konteksti
#XMSG
infoMessageForSLTSelection=Vain /SLT/Joukkosiirron tunnus on sallittu säilönä. Valitse Joukkosiirron tunnus kohdassa SLT (jos käytettävissä) ja valitse Lähetä.
#XMSG
msgFetchContainerFail=On tapahtunut virhe noudettaessa säilön tietoja.
#XMSG
infoMessageForSLTHidden=Tämä yhteys ei tue SLT-kansioita, joten niitä ei näytetä alla olevassa luettelossa.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Valitse säilö, joka sisältää alikansioita.
#XMSG
sftpIncludeSubFolderText=Epätosi
#XMSG
sftpIncludeSubFolderTextNew=Ei

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Suodatinkohdistusta ei vielä ole)
#XMSG
failToFetchRemoteMetadata=On tapahtunut virhe noudettaessa metatietoja.
#XMSG
failToFetchData=On tapahtunut virhe noudettaessa olemassa olevaa kohdetta.
#XCOL
@loadType=Lataa tyyppi
#XCOL
@deleteAllBeforeLoading=Poista kaikki ennen latausta

#XMSG
@loading=Ladataan...
#XFLD
@selectSourceObjects=Valitse lähdeobjektit
#XMSG
@exceedLimit=Voit tuoda enintään {0} objektia kerrallaan. Poista vähintään {1} objektin valinta.
#XFLD
@objects=Objektit
#XBUT
@ok=OK
#XBUT
@cancel=Peruuta
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Seuraava
#XBUT
btnAddSelection=Lisää valinta
#XTOL
@remoteFromSelection=Poista valinnasta
#XMSG
@searchInForSearchField=Hae kohteesta {0}

#XCOL
@name=Tekninen nimi
#XCOL
@type=Tyyppi
#XCOL
@location=Sijainti
#XCOL
@label=Liiketoiminnallinen nimi
#XCOL
@status=Tila

#XFLD
@searchIn=Hae kohteesta:
#XBUT
@available=Käytettävissä
#XBUT
@selection=Valinta

#XFLD
@noSourceSubFolder=Taulut ja näkymät
#XMSG
@alreadyAdded=On jo kaaviossa
#XMSG
@askForFilter=Enemmän kuin {0} kohdetta. Rajoita kohteiden lukumäärää syöttämällä suodatinmerkkijono.
#XFLD: success label
lblSuccess=Onnistuminen
#XFLD: ready label
lblReady=Valmis
#XFLD: failure label
lblFailed=Epäonnistui
#XFLD: fetching status label
lblFetchingDetail=Haetaan lisätietoja

#XMSG Place holder text for tree filter control
filterPlaceHolder=Kirjoita tekstiä ylimmän tason objektien suodatusta varten
#XMSG Place holder text for server search control
serverSearchPlaceholder=Kirjoita ja käynnistä haku painamalla Enter
#XMSG
@deployObjects=Tuodaan objekteja ({0})...
#XMSG
@deployObjectsStatus=Tuotujen objektien lukumäärä: {0}. Niiden objektien lukumäärä, joita ei voitu tuoda: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Paikallisen Repository Browserin avaaminen epäonnistui.
#XMSG
@openRemoteSourceBrowserError=Lähdeobjektien noutaminen epäonnistui.
#XMSG
@openRemoteTargetBrowserError=Kohdeobjektien noutaminen epäonnistui.
#XMSG
@validatingTargetsError=On tapahtunut virhe kohteiden validoinnissa.
#XMSG
@waitingToImport=Valmis tuontia varten

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Objektien enimmäismäärä on ylitetty. Valitse enintään 500 objektia yhdelle replikointivirralle.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Tekninen nimi
#XFLD
sourceObjectBusinessName=Liiketoiminnallinen nimi
#XFLD
sourceNoColumns=Sarakkeiden lukumäärä
#XFLD
containerLbl=Säilö

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Sinun pitää valita lähdeyhteys replikointivirtaa varten.
#XMSG
validationSourceContainerNonExist=Sinun pitää valita säilö lähdeyhteyttä varten.
#XMSG
validationTargetNonExist=Sinun pitää valita kohdeyhteys replikointivirtaa varten.
#XMSG
validationTargetContainerNonExist=Sinun pitää valita säilö kohdeyhteyttä varten.
#XMSG
validationTruncateDisabledForObjectTitle=Replikointi objektivarastoihin.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Replikointi pilvitallennustilaan on mahdollinen vain silloin, jos joko Poista kaikki ennen latausta -vaihtoehto on asetettu tai kohdeobjektia ei enää ole kohteessa.{0}{0} Jos haluat yhä aktivoida replikoinnin objekteille, joille ei ole asetettu Poista kaikki ennen latausta -vaihtoehtoa, varmista ennen replikointivirran ajoa, ettei kohdeobjektia ole järjestelmässä.
#XMSG
validationTaskNonExist=Sinulla pitää olla vähintään yksi replikointi replikointivirrassa.
#XMSG
validationTaskTargetMissing=Sinulla pitää olla kohde replikointia varten lähteen kanssa: {0}
#XMSG
validationTaskTargetIsSAC=Valittu kohde on SAC-artefakti: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Valittua kohdetta ei tueta paikallisessa taulussa: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Kohteessa on jo objekti, jolla on sama nimi. Objektia ei voida käyttää replikointivirran kohdeobjektina paikalliseen tietohakemistoon, koska se ei ole paikallinen taulu.
#XMSG
validateSourceTargetSystemDifference=Sinun pitää valita eri lähde- ja kohdeyhteys- ja säilöyhdistelmät replikointivirtaa varten.
#XMSG
validateDuplicateSources=yhdellä tai useammalla replikoinnilla on kaksinkertaiset lähdeobjektin nimet: {0}.
#XMSG
validateDuplicateTargets=yhdellä tai useammalla replikoinnilla on kaksinkertaiset kohdeobjektin nimet: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=Lähdeobjekti {0} ei tue deltasieppausta, kohdeobjekti {1} puolestaan tukee. Replikointi on poistettava.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Sinun on valittava lataustyyppi "Ensimmäinen ja delta" replikointiin kohdeobjektin {0} kanssa.
#XMSG
validationAutoRenameTarget=Kohdesarakkeet on nimetty uudelleen.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Automaattinen projektio on lisätty, ja seuraavat kohdesarakkeet on nimetty uudelleen, jotta replikointi kohteeseen voidaan suorittaa:{1}{1} {0} {1}{1}Tämä johtuu jostakin seuraavista syistä:{1}{1}{2} Ei-tuettuja merkkejä {1}{2} Varattu etuliite
#XMSG
validationAutoRenameTargetDescriptionUpdated=Automaattinen projektio on lisätty, ja seuraavat kohdesarakkeet on nimetty uudelleen, jotta replikointi Google BigQueryyn voidaan suorittaa:{1}{1} {0} {1}{1}Tämä johtuu jostakin seuraavista syistä:{1}{1}{2} Varattu sarakkeen nimi{1}{2} Ei-tuettuja merkkejä{1}{2} Varattu etuliite
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Automaattinen projektio on lisätty, ja seuraavat kohdesarakkeet on nimetty uudelleen, jotta replikoinnit Confluentiin voidaan suorittaa:{1}{1} {0} {1}{1}Tämä johtuu jostakin seuraavista syistä:{1}{1}{2} Varattu sarakkeen nimi{1}{2} Ei-tuettuja merkkejä{1}{2} Varattu etuliite
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Automaattinen projektio on lisätty, ja seuraavat kohdesarakkeet on nimetty uudelleen, jotta replikoinnit kohteeseen voidaan suorittaa:{1}{1} {0} {1}{1}Tämä johtuu jostakin seuraavista syistä:{1}{1}{2} Varattu sarakkeen nimi{1}{2} Ei-tuettuja merkkejä{1}{2} Varattu etuliite
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Kohdeobjektin nimi on muutettu.
#XMSG
autoRenameInfoDesc=Kohdeobjektin nimi on muutettu, koska se sisälsi ei-tuettuja merkkejä. Vain seuraavia merkkejä tuetaan:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(piste){0}{1}_(alaviiva){0}{1}-(ajatusviiva)
#XMSG
validationAutoTargetTypeConversion=Kohdetietotyypit on muutettu.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Seuraavien kohdesarakkeiden kohdetietotyyppejä on muutettu, koska Google BigQuery ei tue lähdetietotyyppejä:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Seuraavien kohdesarakkeiden kohdetietotyyppejä on muutettu, koska lähdetietotyyppejä ei tueta kohdeyhteydessä:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Lyhennä kohdesarakkeiden nimet
#XMSG
validationMaxCharLengthGBQTargetDescription=Google BigQueryssa sarakkeiden nimien enimmäispituus on 300 merkkiä. Käytä projektiota seuraavien kohdesarakkeiden nimien lyhentämiseen:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Ensisijaisia avaimia ei luoda.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=Google BigQuery tukee enintään 16 ensisijaista avainta, mutta lähdeobjektilla on sitä suurempi määrä ensisijaisia avaimia. Yhtäkään ensisijaisista avaimista ei luoda kohdeobjektissa.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Yhdessä tai useammassa lähdesarakkeessa on tietotyyppejä, joita ei voi määrittää ensisijaisiksi avaimiksi Google BigQueryssä. Kohdeobjektissa ei luoda yhtään ensisijaista avainta.{0}{0}Seuraavat kohdetietotyypit ovat yhteensopivia Google BigQueryn tietotyyppien kanssa, joille voidaan määrittää ensisijainen avain:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Määritä vähintään yksi sarake ensisijaiseksi avaimeksi.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Vähintään yksi sarake on määritettävä ensisijaiseksi avaimeksi. Käytä siihen lähdekaavion valintaikkunaa.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Määritä vähintään yksi sarake ensisijaiseksi avaimeksi.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Sinun on määritettävä vähintään yksi sarake ensisijaiseksi avaimeksi, joka vastaa lähdeobjektin ensisijaisen avaimen rajoitteita. Voit tehdä tämän siirtymällä lähdeobjektin ominaisuuksien Määritä kaavio -kohtaan.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Syötä kelvollinen osioiden enimmäisarvo.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Osion maksimiarvon on oltava ≥ 1 ja ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Määritä vähintään yksi sarake ensisijaiseksi avaimeksi.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Objektin replikointi edellyttää vähintään yhden kohdesarakkeen määrittämistä ensisijaiseksi avaimeksi. Käytä siihen projektiota.
#XMSG
validateHDLFNoPKExistingDatasetError=Määritä vähintään yksi sarake ensisijaiseksi avaimeksi.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Jotta tiedot voi replikoida olemassa olevaan kohdeobjektiin, sillä täytyy olla vähintään yksi ensisijaiseksi avaimeksi määritetty sarake. {0} Seuraavat vaihtoehdot ovat olemassa, kun ensisijaiseksi avaimeksi halutaan määrittää vähintään yksi sarake: {0}{1} Muuta olemassa olevaa kohdeobjektia käyttämällä paikallista taulun muokkausohjelmaa. Lataa sen jälkeen replikointivirta uudelleen.{0}{1}Nimeä kohdeobjekti uudelleen replikointivirrassa. Tällöin uusi objekti luodaan heti, kun ajo käynnistetään. Uudelleennimeämisen jälkeen voit määrittää vähintään yhden sarakkeen ensisijaiseksi avaimeksi projektiossa.{0}{1}Kohdista objekti toiseen olemassa olevaan kohdeobjektiin, jossa vähintään yksi sarake on jo määritetty ensisijaiseksi avaimeksi.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Valittu kohde on jo tietohakemistossa: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Muut tietohakemiston taulut käyttävät jo deltasieppaustaulun nimiä: {0} Nämä kohdeobjektit on nimettävä uudelleen, jotta voidaan varmistaa, että liittyvät deltasieppaustaulun nimet ovat yksiselitteisiä, ennen kuin replikointivirran voi tallentaa.
#XMSG
validateConfluentEmptySchema=Määritä kaavio
#XMSG
validateConfluentEmptySchemaDescUpdated=Lähdetaulussa ei ole kaaviota. Määritä kaavio valitsemalla Määritä kaavio
#XMSG
validationCSVEncoding=Virheellinen CSV-koodaus
#XMSG
validationCSVEncodingDescription=Tehtävän CSV-koodaus on virheellinen.
#XMSG
validateConfluentEmptySchema=Valitse yhteensopiva kohdetietotyyppi
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Valitse yhteensopiva kohdetietotyyppi
#XMSG
globalValidateTargetDataTypeDesc=Virhe sarakkeiden kohdistuksissa. Siirry projektioon ja varmista, että kaikki lähdesarakkeet on kohdistettu yksiselitteiseen sarakkeeseen, sarakkeeseen, jonka tietotyyppi on yhteensopiva, ja että kaikki määritetyt lausekkeet ovat kelvollisia.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Sarakenimien kaksoiskappaleita.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Sarakenimien kaksoiskappaleita ei tueta. Korjaa ne projektion valintaikkunassa. Seuraavissa kohdeobjekteissa on sarakenimien kaksoiskappaleita: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Sarakenimien kaksoiskappaleita.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Sarakenimien kaksoiskappaleita ei tueta. Seuraavissa kohdeobjekteissa on sarakenimien kaksoiskappaleita: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Tiedoissa voi olla ristiriitoja.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Vain delta -lataustyyppi ei ota huomioon muutoksia, jotka on tehty lähteeseen viimeisen tallennuksen ja seuraavan ajon välillä.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Vaihda lataustyypiksi "Ensimmäinen".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=ABAP-pohjaisten objektien, joilla ei ole ensisijaista avainta, replikointi mahdollista vain lataustyypillä "Vain ensimmäinen".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Poista deltasieppaus käytöstä.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Objektien, joilla ei ole ABAP-tyyppistä lähdeyhteyttä käyttävää ensisijaista avainta, replikoimiseksi on ensin poistettava käytöstä tämän taulun deltasieppaus.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Muuta kohdeobjektia.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Kohdeobjektia ei voi käyttää, koska deltasieppaus on otettu käyttöön. Voit joko nimetä kohdeobjektin uudelleen ja poistaa uuden (uudelleen nimetyn) objektin deltasieppauksen käytöstä tai kohdistaa lähdeobjektin kohdeobjektiin, jonka deltasieppaus on poistettu käytöstä.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Muuta kohdeobjektia.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Kohdeobjektia ei voi käyttää, koska siinä ei ole tarvittavaa teknistä saraketta __load_package_id. Kohdeobjektin voi nimetä uudelleen nimellä, jota ei ole vielä olemassa. Tällöin järjestelmä luo uuden objektin, jolla on sama määritelmä kuin lähdeobjektilla ja joka sisältää teknisen sarakkeen. Vaihtoehtoisesti kohdeobjektin voi kohdistaa olemassa olemaan objektiin, jolla on tarvittava tekninen sarake (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Muuta kohdeobjektia.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Kohdeobjektia ei voi käyttää, koska siinä ei ole tarvittavaa teknistä saraketta __load_record_id. Kohdeobjektin voi nimetä uudelleen nimellä, jota ei ole vielä olemassa. Tällöin järjestelmä luo uuden objektin, jolla on sama määritelmä kuin lähdeobjektilla ja joka sisältää teknisen sarakkeen. Vaihtoehtoisesti kohdeobjektin voi kohdistaa olemassa olemaan objektiin, jolla on tarvittava tekninen sarake (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Muuta kohdeobjektia.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Kohdeobjektia ei voi käyttää, koska sen teknisen sarakkeen __load_record_id tietotyyppi ei ole "string(44)". Kohdeobjektin voi nimetä uudelleen nimellä, jota ei ole vielä olemassa. Tällöin järjestelmä luo uuden objektin, jolla on sama määritelmä kuin lähdeobjektilla ja näin ollen oikea tietotyyppi. Vaihtoehtoisesti kohdeobjektin voi kohdistaa olemassa olemaan objektiin, jolla on tarvittava tekninen sarake (__load_record_id), jolla on oikea tietotyyppi.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Muuta kohdeobjektia.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Kohdeobjektia ei voi käyttää, koska sillä on ensisijainen avain ja lähdeobjektilla ei. Kohdeobjektin voi nimetä uudelleen nimellä, jota ei ole vielä olemassa. Tällöin järjestelmä luo uuden objektin, jolla on sama määritelmä kuin lähdeobjektilla ja näin ollen ei ensisijaista avainta. Vaihtoehtoisesti kohdeobjektin voi kohdistaa olemassa olemaan objektiin, jolla on tarvittava tekninen sarake (__load_package_id) eikä ensisijaista avainta.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Muuta kohdeobjektia.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Kohdeobjektia ei voi käyttää, koska sillä on ensisijainen avain ja lähdeobjektilla ei. Kohdeobjektin voi nimetä uudelleen nimellä, jota ei ole vielä olemassa. Tällöin järjestelmä luo uuden objektin, jolla on sama määritelmä kuin lähdeobjektilla ja näin ollen ei ensisijaista avainta. Vaihtoehtoisesti kohdeobjektin voi kohdistaa olemassa olemaan objektiin, jolla on tarvittava tekninen sarake (__load_record_id) eikä ensisijaista avainta.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Muuta kohdeobjektia.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Kohdeobjektia ei voi käyttää, koska sen teknisen sarakkeen __load_package_id tietotyyppi ei ole "binary(>=256)". Kohdeobjektin voi nimetä uudelleen nimellä, jota ei ole vielä olemassa. Tällöin järjestelmä luo uuden objektin, jolla on sama määritelmä kuin lähdeobjektilla ja näin ollen oikea tietotyyppi. Vaihtoehtoisesti kohdeobjektin voi kohdistaa olemassa olemaan objektiin, jolla on tarvittava tekninen sarake (__load_package_id), jolla on oikea tietotyyppi.
#XMSG
validationAutoRenameTargetDPID=Kohdesarakkeet on nimetty uudelleen.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Poista lähdeobjekti.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Lähdeobjektilla ei ole avainsaraketta, mitä ei tueta tässä kontekstissa.
#XMSG
validationAutoRenameTargetDPIDDescription=Automaattinen projektio on lisätty, ja seuraavat kohdesarakkeet on nimetty uudelleen, jotta replikointi ABAP-lähteestä voidaan suorittaa ilman avaimia:{1}{1} {0} {1}{1}Tämä johtuu jostakin seuraavista syistä:{1}{1}{2} Varattu sarakkeen nimi{1}{2} Ei-tuettuja merkkejä{1}{2} Varattu etuliite
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Replikointi järjestelmään {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Replikointivirtoja, joiden lähteenä on {0}, ei voi tällä hetkellä tallentaa ja ottaa käyttöön, koska tämän toiminnon ylläpito on käynnissä.
#XMSG
TargetColumnSkippedLTF=Kohdesarake on ohitettu.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Kohdesarake on ohitettu ei-tuetun tietotyypin vuoksi. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Aikasarake ensisijaisena avaimena.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Lähdeobjektilla on aikasarake ensisijaisena avaimena, mitä ei tueta tässä kontekstissa.
#XMSG
validateNoPKInLTFTarget=Ensisijainen avain puuttuu.
#XMSG
validateNoPKInLTFTargetDescription=Ensisijaista avainta ei ole määritetty kohteessa, mitä ei tueta tässä kontekstissa.
#XMSG
validateABAPClusterTableLTF=ABAP-ryvästaulu.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Lähdeobjekti on ABAP-ryvästaulu, mitä ei tueta tässä kontekstissa.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Vaikuttaa siltä, ettet ole vielä lisännyt tietoja.
#YINS
welcomeText2=Voit käynnistää replikointivirran valitsemalla vasemmalla yhteyden ja lähdeobjektin.

#XBUT
wizStep1=Valitse lähdeyhteys
#XBUT
wizStep2=Valitse lähdesäilö
#XBUT
wizStep3=Lisää lähdeobjektit

#XMSG
limitDataset=Objektien enimmäismäärä on saavutettu. Poista olemassa olevia objekteja lisätäksesi uusia, tai luo uusi replikointivirta.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Replikointivirtaa tähän ei-SAP-kohdeyhteyteen ei voi käynnistää, koska tässä kuussa ei ole käytettävissä lähtevää volyymiä.
#XMSG
premiumOutBoundRFAdminWarningMsg=Pääkäyttäjä voi kasvattaa lähteviä premium-lohkoja tämän vuokralaisen osalta, jolloin tälle kuulle saadaan käyttöön lähtevää volyymiä.
#XMSG
messageForToastForDPIDColumn2=Uusi sarake lisätty {0} objektin kohteeseen - tarvitaan kaksoiskappalevirheiden käsittelyyn sellaisten ABAP-pohjaisten lähdeobjektien yhteydessä, joilla ei ole ensisijaista avainta.
#XMSG
PremiumInboundWarningMessage=Replikointivirtojen lukumäärästä ja tietojen määrästä riippuen SAP HANA -resurssit {0}, joita tarvitaan tietojen replikointiin {1} -palvelun kautta, saattavat ylittää vuokralaisesi käytettävissä olevan kapasiteetin.
#XMSG
PremiumInboundWarningMsg=Replikointivirtojen lukumäärästä ja tietojen määrästä riippuen {0} SAP HANA -resurssit, joita tarvitaan tietojen replikointiin {1} -palvelun kautta, saattavat ylittää vuokralaisesi käytettävissä olevan kapasiteetin.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Syötä projektion nimi.
#XMSG
emptyTargetColumn=Syötä kohdesarakkeen nimi.
#XMSG
emptyTargetColumnBusinessName=Syötä kohdesarakkeen liiketoiminnallinen nimi.
#XMSG
invalidTransformName=Syötä projektion nimi.
#XMSG
uniqueColumnName=Nimeä kohdesarake uudelleen.
#XMSG
copySourceColumnLbl=Kopioi sarakkeet lähdeobjektista
#XMSG
renameWarning=Muista valita yksilöivä nimi nimetessäsi kohdetaulua uudelleen. Jos tilassa on jo entuudestaan samanniminen taulu, käytetään sen määritystä.

#XMSG
uniqueColumnBusinessName=Nimeä kohdesarakkeen liiketoiminnallinen nimi uudelleen.
#XMSG
uniqueSourceMapping=Valitse toinen lähdesarake.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=Lähdesarake {0} on jo käytössä seuraavissa kohdesarakkeissa:{1}{1}{2}{1}{1} Tallenna projektio valitsemalla tälle kohdesarakkeelle tai muille kohdesarakkeille lähdesarake, joka ei ole vielä käytössä.
#XMSG
uniqueColumnNameDescription=Syöttämäsi kohdesarakkeen nimi on jo olemassa. Jotta voit tallentaa projektion, sinun pitää syöttää yksiselitteinen sarakkeen nimi.
#XMSG
uniqueColumnBusinessNameDesc=Kohdesarakkeen liiketoiminnallinen nimi on jo olemassa. Jotta voit tallentaa projektion, sinun on syötettävä yksiselitteinen sarakkeen liiketoiminnallinen nimi.
#XMSG
emptySource=Valitse lähdesarake tai syötä vakio.
#XMSG
emptySourceDescription=Jotta voit luoda kelpaavan kohdistusmerkinnän, sinun pitää valita lähdesarake tai syöttää vakioarvo.
#XMSG
emptyExpression=Määritä kohdistus.
#XMSG
emptyExpressionDescription1=Joko valitse lähdesarake, johon haluat kohdistaa kohdesarakkeen, tai valitse valintaruutu sarakkeessa {0} Funktiot/vakiot {1}. {2} {2} Funktiot syötetään automaattisesti kohdetietotyypin mukaan. Vakioarvot voidaan syöttää manuaalisesti.
#XMSG
numberExpressionErr=Syötä luku.
#XMSG
numberExpressionErrDescription=Valitsit numeerisen tietotyypin. Tämä tarkoittaa, että voit syöttää vain numeroita ja tarvittaessa desimaalipisteen. Älä käytä puolilainausmerkkejä.
#XMSG
invalidLength=Syötä kelpaava pituusarvo.
#XMSG
invalidLengthDescription=Tietotyypin pituuden pitää olla yhtä suuri tai suurempi kuin lähdesarakkeen pituus, ja se voi olla 1–5 000.
#XMSG
invalidMappedLength=Syötä kelpaava pituusarvo.
#XMSG
invalidMappedLengthDescription=Tietotyypin pituuden pitää olla yhtä suuri tai suurempi kuin lähdesarakkeen {0} pituus, ja se voi olla 1–5 000.
#XMSG
invalidPrecision=Syötä kelpaava tarkkuusarvo.
#XMSG
invalidPrecisionDescription=Tarkkuus määrittää numeroiden kokonaislukumäärän. Asteikko määrittää numeroiden lukumäärän desimaalipisteen jälkeen, ja se voi olla nollan ja tarkkuuden väliltä.{0}{0} Esimerkkejä: {0}{1} Tarkkuus 6, asteikko 2 vastaa numeroita, kuten 1234.56.{0}{1} Tarkkuus 6, asteikko 6 vastaa numeroita, kuten 0.123456.{0} {0} Kohteen tarkkuuden ja asteikon pitää sopia yhteen lähteen tarkkuuden ja asteikon kanssa, jotta kaikki numerot lähteestä sopivat kohdekenttään. Jos lähteessä esimerkiksi on tarkkuus 6 ja asteikko 2 (ja vastaavasti muita numeroita kuin 0 ennen desimaalipistettä), kohteessa ei voi olla tarkkuutta 6 ja asteikkoa 6.
#XMSG
invalidPrimaryKey=Syötä vähintään yksi ensisijainen avain
#XMSG
invalidPrimaryKeyDescription=Tälle kaaviolle ei ole määritetty ensisijaista avainta.
#XMSG
invalidMappedPrecision=Syötä kelpaava tarkkuusarvo.
#XMSG
invalidMappedPrecisionDescription1=Tarkkuus määrittää numeroiden kokonaislukumäärän. Asteikko määrittää numeroiden lukumäärän desimaalipisteen jälkeen, ja se voi olla nollan ja tarkkuuden väliltä.{0}{0}Esimerkkejä:{0}{1} Tarkkuus 6, asteikko 2 vastaa numeroita, kuten 1234.56.{0}{1} Tarkkuus 6, asteikko 6 vastaa numeroita, kuten 0.123456.{0}{0}Tietotyypin tarkkuuden pitää olla yhtä suuri tai suurempi kuin lähteen ({2}) tarkkuus.
#XMSG
invalidScale=Syötä kelpaava asteikkoarvo.
#XMSG
invalidScaleDescription=Tarkkuus määrittää numeroiden kokonaislukumäärän. Asteikko määrittää numeroiden lukumäärän desimaalipisteen jälkeen, ja se voi olla nollan ja tarkkuuden väliltä.{0}{0} Esimerkkejä: {0}{1} Tarkkuus 6, asteikko 2 vastaa numeroita, kuten 1234.56.{0}{1} Tarkkuus 6, asteikko 6 vastaa numeroita, kuten 0.123456.{0} {0} Kohteen tarkkuuden ja asteikon pitää sopia yhteen lähteen tarkkuuden ja asteikon kanssa, jotta kaikki numerot lähteestä sopivat kohdekenttään. Jos lähteessä esimerkiksi on tarkkuus 6 ja asteikko 2 (ja vastaavasti muita numeroita kuin 0 ennen desimaalipistettä), kohteessa ei voi olla tarkkuutta 6 ja asteikkoa 6.
#XMSG
invalidMappedScale=Syötä kelpaava asteikkoarvo.
#XMSG
invalidMappedScaleDescription1=Tarkkuus määrittää numeroiden kokonaislukumäärän. Asteikko määrittää numeroiden lukumäärän desimaalipisteen jälkeen, ja se voi olla nollan ja tarkkuuden väliltä.{0}{0}Esimerkkejä:{0}{1} Tarkkuus 6, asteikko 2 vastaa numeroita, kuten 1234.56.{0}{1} Tarkkuus 6, asteikko 6 vastaa numeroita, kuten 0.123456.{0}{0}Tietotyypin asteikon pitää olla yhtä suuri tai suurempi kuin lähteen ({2}) asteikko.
#XMSG
nonCompatibleDataType=Valitse yhteensopiva kohdetietotyyppi.
#XMSG
nonCompatibleDataTypeDescription1=Tässä määrittämäsi tietotyypin pitää olla yhteensopiva lähdetietotyypin ({0}) kanssa. {1}{1} Jos lähdesarakkeessa esimerkiksi on tietotyyppi Merkkijono ja kirjaimia, kohteessa ei voi käyttää tietotyyppiä Desimaali.
#XMSG
invalidColumnCount=Valitse lähdesarake.
#XMSG
ObjectStoreInvalidScaleORPrecision=Syötä sallittu arvo tarkkuutta ja asteikkoa varten.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=Ensimmäinen arvo on tarkkuus, joka määrittää numeroiden kokonaismäärän. Toinen arvo on asteikko, joka määrittää numeroiden lukumäärän desimaalierottimen jälkeen. Syötä kohteen asteikkoarvo, joka on suurempi kuin lähteen asteikkoarvo, ja varmista, että kohteen syötetyn asteikko- ja tarkkuusarvon ero on suurempi kuin lähteen asteikko- ja tarkkuusarvon ero.
#XMSG
InvalidPrecisionORScale=Syötä sallittu arvo tarkkuutta ja asteikkoa varten.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=Ensimmäinen arvo on tarkkuus, joka määrittää numeroiden kokonaislukumäärän. Toinen arvo on asteikko, joka määrittää numeroiden lukumäärän desimaalierottimen jälkeen.{0}{0}Koska Google BigQuery ei tue lähdetietotyyppiä, se muunnetaan kohdetietotyypiksi DECIMAL. Tällöin tarkkuudeksi voidaan määrittää vain 38–76 ja asteikoksi 9–38. Lisäksi laskennan "tarkkuus miinus asteikko", joka edustaa numeroita ennen desimaalierotinta, tuloksen on oltava 29–38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=Ensimmäinen arvo on tarkkuus, joka määrittää numeroiden kokonaislukumäärän. Toinen arvo on asteikko, joka määrittää numeroiden lukumäärän desimaalierottimen jälkeen.{0}{0}Koska Google BigQuery ei tue lähdetietotyyppiä, se muunnetaan kohdetietotyypiksi DECIMAL. Tällöin tarkkuudeksi pitää määrittää 20 tai sitä suurempi luku. Lisäksi laskennan "tarkkuus miinus asteikko", joka edustaa numeroita ennen desimaalierotinta, tuloksen on oltava vähintään 20.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=Ensimmäinen arvo on tarkkuus, joka määrittää numeroiden kokonaislukumäärän. Toinen arvo on asteikko, joka määrittää numeroiden lukumäärän desimaalierottimen jälkeen.{0}{0}Koska kohde ei tue lähdetietotyyppiä, se muunnetaan kohdetietotyypiksi DECIMAL. Tällöin tarkkuudeksi on määritettävä luku, joka on suurempi tai yhtä suuri kuin 1 ja pienempi tai yhtä suuri kuin 38, ja asteikko on määritettävä pienemmäksi tai yhtä suureksi kuin tarkkuus.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=Ensimmäinen arvo on tarkkuus, joka määrittää numeroiden kokonaislukumäärän. Toinen arvo on asteikko, joka määrittää numeroiden lukumäärän desimaalierottimen jälkeen.{0}{0}Koska kohde ei tue lähdetietotyyppiä, se muunnetaan kohdetietotyypiksi DECIMAL. Tällöin tarkkuudeksi on määritettävä 20 tai sitä suurempi luku. Lisäksi laskennan "tarkkuus miinus asteikko", joka edustaa numeroita ennen desimaalierotinta, tuloksen on oltava vähintään 20.
#XMSG
invalidColumnCountDescription=Jotta voit luoda kelpaavan kohdistusmerkinnän, sinun pitää valita lähdesarake tai syöttää vakioarvo.
#XMSG
duplicateColumns=Nimeä kohdesarake uudelleen.
#XMSG
duplicateGBQCDCColumnsDesc=Kohdesarakkeen nimi on varattu Google BigQueryssä. Sinun pitää nimetä se uudelleen, jotta voit tallentaa projektion.
#XMSG
duplicateConfluentCDCColumnsDesc=Kohdesarakkeen nimi on varattu Confluentissa. Sinun pitää nimetä se uudelleen, jotta voit tallentaa projektion.
#XMSG
duplicateSignavioCDCColumnsDesc=Kohdesarakkeen nimi on varattu SAP Signaviossa. Sinun pitää nimetä se uudelleen, jotta voit tallentaa projektion.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Kohdesarakkeen nimi on varattu MS OneLakessa. Sinun pitää nimetä se uudelleen, jotta voit tallentaa projektion.
#XMSG
duplicateSFTPCDCColumnsDesc=Kohdesarakkeen nimi on varattu SFTP:ssä. Sinun pitää nimetä se uudelleen, jotta voit tallentaa projektion.
#XMSG
GBQTargetNameWithPrefixUpdated1=Kohdesarakkeen nimessä on etuliite, joka on varattu Google BigQueryssä. Sinun pitää nimetä se uudelleen, jotta voit tallentaa projektion. {0}{0}Kohdesarakkeen nimi ei voi alkaa yhdelläkään seuraavista merkkijonoista:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2}_ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Lyhennä kohdesarakkeen nimeä.
#XMSG
GBQtargetMaxLengthDesc=Google BigQueryssä sarakkeen nimessä voi olla enintään 300 merkkiä. Lyhennä kohdesarakkeen nimeä, jotta voit tallentaa projektion.
#XMSG
invalidMappedScalePrecision=Kohteen tarkkuuden ja asteikon pitää sopia yhteen lähteen tarkkuuden ja asteikon kanssa, jotta kaikki numerot lähteestä sopivat kohdekenttään.
#XMSG
invalidMappedScalePrecisionShortText=Syötä kelpaava tarkkuus- ja asteikkoarvo.
#XMSG
validationIncompatiblePKTypeDescProjection3=Yhdessä tai useammassa lähdesarakkeessa on tietotyyppejä, joita ei voi määrittää ensisijaisiksi avaimiksi Google BigQueryssä. Kohdeobjektissa ei luoda yhtään ensisijaista avainta.{0}{0}Seuraavat kohdetietotyypit ovat yhteensopivia Google BigQueryn tietotyyppien kanssa, joille voidaan määrittää ensisijainen avain:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Poista sarakkeen __message_id valinta.
#XMSG
uncheckColumnMessageIdDesc=Sarake: ensisijainen avain
#XMSG
validationOpCodeInsert=Lisää-vaihtoehdolle on syötettävä arvo.
#XMSG
recommendDifferentPrimaryKey=Suosittelemme valitsemaan eri ensisijaisen avaimen rivitasolla. 
#XMSG
recommendDifferentPrimaryKeyDesc=Kun operaatiokoodi on jo määritetty, on suositeltavaa valita eri ensisijaiset avaimet taulukkoindeksille ja riveille. Siten vältetään kaksinkertaisten sarakkeiden kaltaiset ongelmat.
#XMSG
selectPrimaryKeyItemLevel=Sinun on valittava vähintään yksi ensisijainen avain sekä otsikko- että rivitasolla.
#XMSG
selectPrimaryKeyItemLevelDesc=Kun taulukko tai kartta on laajennettu, sinun on valittava kaksi ensisijaista avainta: yksi otsikkotasolla ja toinen rivitasolla.
#XMSG
invalidMapKey=Sinun on valittava vähintään yksi ensisijainen avain otsikkotasolla.
#XMSG
invalidMapKeyDesc=Kun taulukko tai kartta on laajennettu, sinun on valittava ensisijainen avain otsikkotasolla.
#XFLD
txtSearchFields=Hae kohdesarakkeita
#XFLD
txtName=Nimi
#XMSG
txtSourceColValidation=Yhtä tai useampaa lähdesaraketta ei tueta:
#XMSG
txtMappingCount=Kohdistukset ({0})
#XMSG
schema=Kaavio
#XMSG
sourceColumn=Lähdesarakkeet
#XMSG
warningSourceSchema=Kaavioon tehdyt muutokset vaikuttavat kohdistuksiin projektion valintaikkunassa.
#XCOL
txtTargetColName=Kohdesarakkeen nimi (tekninen nimi)
#XCOL
txtDataType=Kohdetietotyyppi
#XCOL
txtSourceDataType=Lähdetietojen tyyppi
#XCOL
srcColName=Lähdesarake (tekninen nimi)
#XCOL
precision=Tarkkuus
#XCOL
scale=Asteikko
#XCOL
functionsOrConstants=Funktiot/vakiot
#XCOL
txtTargetColBusinessName=Kohdesarake (liiketoiminnallinen nimi)
#XCOL
prKey=Ensisijainen avain
#XCOL
txtProperties=Ominaisuudet
#XBUT
txtOK=Tallenna
#XBUT
txtCancel=Peruuta
#XBUT
txtRemove=Poista
#XFLD
txtDesc=Kuvaus
#XMSG
rftdMapping=Kohdistus
#XFLD
@lblColumnDataType=Tietotyyppi
#XFLD
@lblColumnTechnicalName=Tekninen nimi
#XBUT
txtAutomap=Kohdista automaattisesti
#XBUT
txtUp=Ylös
#XBUT
txtDown=Alas

#XTOL
txtTransformationHeader=Ennuste
#XTOL
editTransformation=Muokkaa
#XTOL
primaryKeyToolip=Avain


#XMSG
rftdFilter=Suodatin
#XMSG
rftdFilterColumnCount=Lähde: {0}({1})
#XTOL
rftdFilterColSearch=Hae
#XMSG
rftdFilterColNoData=Ei näytettäviä sarakkeita
#XMSG
rftdFilteredColNoExps=Ei suodatinlausekkeita
#XMSG
rftdFilterSelectedColTxt=Lisää suodatin
#XMSG
rftdFilterTxt=Suodatin käytettävissä
#XBUT
rftdFilterSelectedAddColExp=Lisää lauseke
#YINS
rftdFilterNoSelectedCol=Valitse sarake ja lisää suodatin.
#XMSG
rftdFilterExp=Suodatinlauseke
#XMSG
rftdFilterNotAllowedColumn=Suodatinten lisäämistä ei tueta tälle sarakkeelle.
#XMSG
rftdFilterNotAllowedHead=Ei tuettu sarake
#XMSG
rftdFilterNoExp=Suodatinta ei ole määritetty
#XTOL
rftdfilteredTt=Suodatettu
#XTOL
rftdremoveexpTt=Poista suodatinlauseke
#XTOL
validationMessageTt=Validointisanomat
#XTOL
rftdFilterDateInp=Valitse päivämäärä
#XTOL
rftdFilterDateTimeInp=Valitse päivämäärä ja kellonaika
#XTOL
rftdFilterTimeInp=Valitse aika
#XTOL
rftdFilterInp=Syötä arvo
#XMSG
rftdFilterValidateEmptyMsg={0} suodatinlauseketta sarakkeessa {1} ovat tyhjiä
#XMSG
rftdFilterValidateInvalidNumericMsg={0} suodatinlauseketta sarakkeessa {1} sisältävät virheellisiä numeerisia arvoja
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Suodatinlausekkeen täytyy sisältää kelpaavia numeerisia arvoja
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Jos kohdeobjektin kaavio on muuttunut, käytä toimintoa "Kohdista olemassa olevaan kohdeobjektiin" etusivulla mukauttaaksesi muutoksia, ja kohdista kohdeobjekti sen lähteeseen uudelleen.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Jos kohdetaulu on jo olemassa  ja jos kohdistukseen liittyy skeeman muutos, sinun on muutettava kohdetaulukkoa vastaavasti ennen replikointivirran käyttöönottoa.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Jos kohdistukseen liittyy skeeman muutos, sinun on muutettava kohdetaulukkoa vastaavasti ennen replikointivirran käyttöönottoa.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Seuraavat sarakkeet, joita ei tueta, ohitettiin lähdemäärityksessä: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Seuraavat sarakkeet, joita ei tueta, ohitettiin kohdemäärityksessä: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Seuraavia objekteja ei tueta, koska ne näytetään kulutusta varten: {0} {1} {0} {0} Kun haluat käyttää tauluja replikointivirrassa, semanttisen käytön (taulun asetuksissa) määritys ei saa olla {2}analyyttinen tietojoukko{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Kohdeobjektia ei voi käyttää, koska se näytetään kulutusta varten. {0} {0}  Kun haluat käyttää taulua replikointivirrassa, semanttisen käytön (taulun asetuksissa) määritys ei saa olla {1}analyyttinen tietojoukko{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Samanniminen kohdeobjekti on jo olemassa. Sitä ei kuitenkaan voi käyttää, koska se näytetään kulutusta varten. {0} {0} Kun haluat käyttää taulua replikointivirrassa, semanttisen käytön (taulun asetuksissa) määritys ei saa olla {1}analyyttinen tietojoukko{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Kohteessa on jo objekti, jolla on sama nimi. {0}Objektia ei voida käyttää replikointivirran kohdeobjektina paikalliseen tietohakemistoon, koska se ei ole paikallinen taulu.
#XMSG:
targetAutoRenameUpdated=Kohdesarake on nimetty uudelleen.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Kohdesarake on nimetty uudelleen, jotta Google BigQueryssä voidaan suorittaa replikointeja. Tämä johtuu jostakin seuraavista syistä:{0} {1}{2}varattu sarakkeen nimi{3}{2}ei-tuetut merkit{3}{2}varattu etuliite{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Kohdesarake on nimetty uudelleen, jotta Confluentissa voidaan suorittaa replikointeja. Tämä johtuu jostakin seuraavista syistä:{0} {1}{2}varattu sarakkeen nimi{3}{2}ei-tuetut merkit{3}{2}varattu etuliite{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Kohdesarake on nimetty uudelleen, jotta kohteessa voidaan suorittaa replikointeja. Tämä johtuu jostakin seuraavista syistä:{0} {1}{2}ei-tuetut merkit{3}{2}varattu etuliite{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Kohdesarake on nimetty uudelleen, jotta kohteessa voidaan suorittaa replikointeja. Tämä johtuu jostakin seuraavista syistä:{0} {1}{2}varattu sarakkeen nimi{3}{2}ei-tuetut merkit{3}{2}varattu etuliite {3}{4}
#XMSG:
targetAutoDataType=Kohdetietotyyppiä on muutettu.
#XMSG:
targetAutoDataTypeDesc=Kohdetietotyyppi on muutettu tyypiksi {0}, koska lähdetietotyyppiä ei tueta Google BigQueryssä.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Kohdetietotyyppi on muutettu tyypiksi {0}, koska lähdetietotyyppiä ei tueta kohdeyhteydessä.
#XMSG
projectionGBQUnableToCreateKey=Ensisijaisia avaimia ei luoda.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=Google BigQuery tukee enintään 16 ensisijaista avainta, mutta lähdeobjektilla on sitä suurempi määrä ensisijaisia avaimia. Yhtäkään ensisijaisista avaimista ei luoda kohdeobjektissa.
#XMSG
HDLFNoKeyError=Määritä vähintään yksi sarake ensisijaiseksi avaimeksi.
#XMSG
HDLFNoKeyErrorDescription=Objektin replikointi edellyttää vähintään yhden sarakkeen määrittämistä ensisijaiseksi avaimeksi.
#XMSG
HDLFNoKeyErrorExistingTarget=Määritä vähintään yksi sarake ensisijaiseksi avaimeksi.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Jotta tiedot voi replikoida olemassa olevaan kohdeobjektiin, sillä täytyy olla vähintään yksi ensisijaiseksi avaimeksi määritetty sarake. {0}{0} Seuraavat vaihtoehdot ovat olemassa, kun ensisijaiseksi avaimeksi halutaan määrittää vähintään yksi sarake: {0}{0}{1}Muuta olemassa olevaa kohdeobjektia käyttämällä paikallista taulun muokkausohjelmaa. Lataa sen jälkeen replikointivirta uudelleen.{0}{0}{1}Nimeä kohdeobjekti uudelleen replikointivirrassa. Tällöin uusi objekti luodaan heti, kun ajo käynnistetään. Uudelleennimeämisen jälkeen voit määrittää vähintään yhden sarakkeen ensisijaiseksi avaimeksi projektiossa.{0}{0}{1}Kohdista objekti toiseen olemassa olevaan kohdeobjektiin, jossa vähintään yksi sarake on jo määritetty ensisijaiseksi avaimeksi.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Ensisijainen avain muutettu.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=Olet määrittänyt kohdeobjektille eri sarakkeet ensisijaiseksi avaimeksi lähdeobjektiin verrattuna. Varmista, että kyseiset sarakkeet määrittävät yksiselitteisesti kaikki rivit, jotta vältyttäisiin mahdolliselta tietojen vioittumiselta myöhemmin tehtävän tietojen replikoinnin yhteydessä. {0} {0} Seuraavat sarakkeet on määritetty ensisijaiseksi avaimeksi lähdeobjektissa: {0} {1}
#XMSG
duplicateDPIDColumns=Nimeä kohdesarake uudelleen.
#XMSG
duplicateDPIDDColumnsDesc1=Tämä kohdesarakkeen nimi on varattu tekniselle sarakkeelle. Syötä eri nimi projektion tallentamista varten.
#XMSG:
targetAutoRenameDPID=Kohdesarake on nimetty uudelleen.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Kohdesarake on nimetty uudelleen, jotta voidaan suorittaa replikointeja ABAP-lähteestä ilman avaimia. Tämä johtuu jostakin seuraavista syistä:{0} {1}{2}varattu sarakkeen nimi{3}{2}ei-tuetut merkit{3}{2}varattu etuliite{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle={0} - kohdeasetukset
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle={0} - lähdeasetukset
#XBUT
connectionSettingSave=Tallenna
#XBUT
connectionSettingCancel=Peruuta
#XBUT: Button to keep the object level settings
txtKeep=Säilytä
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Korvaa
#XFLD
targetConnectionThreadlimit=Alkulatauksen kohdesäieraja (1–100)
#XFLD
connectionThreadLimit=Alkulatauksen lähdesäieraja (1–100)
#XFLD
maxConnection=Replikointisäieraja (1–100)
#XFLD
kafkaNumberOfPartitions=Osioiden lukumäärä
#XFLD
kafkaReplicationFactor=Replikointikerroin
#XFLD
kafkaMessageEncoder=Ilmoitusten koodaaja
#XFLD
kafkaMessageCompression=Ilmoituksen tiivistys
#XFLD
fileGroupDeltaFilesBy=Ryhmittele delta perusteella
#XFLD
fileFormat=Tiedostotyyppi
#XFLD
csvEncoding=CSV-koodaus
#XFLD
abapExitLbl=ABAP-liiteohjelma
#XFLD
deltaPartition=Deltalatausten objektisäikeiden lukumäärä (1–10)
#XFLD
clamping_Data=Virhe tietojen lyhennyksessä
#XFLD
fail_On_Incompatible=Yhteensopimaton tieto epäonnistui
#XFLD
maxPartitionInput=Osioiden enimmäislukumäärä
#XFLD
max_Partition=Määritä osioiden enimmäislukumäärä
#XFLD
include_SubFolder=Sisällytä alikansiot
#XFLD
fileGlobalPattern=Tiedostonimen globaali malli
#XFLD
fileCompression=Tiedoston tiivistys
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Tiedostoerotin
#XFLD
fileIsHeaderIncluded=Tiedoston otsikko
#XFLD
fileOrient=Muoto
#XFLD
gbqWriteMode=Kirjoitustila
#XFLD
suppressDuplicate=Piilota kaksoiskappaleet
#XFLD
apacheSpark=Aktivoi Apache Spark -yhteensopivuus
#XFLD
clampingDatatypeCb=Kiinnitä tietotyypit liukuvan desimaalipisteen kanssa
#XFLD
overwriteDatasetSetting=Korvaa kohdeasetukset objektitasolla
#XFLD
overwriteSourceDatasetSetting=Korvaa lähdeasetukset objektitasolla
#XMSG
kafkaInvalidConnectionSetting=Syötä luku väliltä {0}-{1}.
#XMSG
MinReplicationThreadErrorMsg=Syötä numero, joka on suurempi kuin {0}.
#XMSG
MaxReplicationThreadErrorMsg=Syötä numero, joka on pienempi kuin {0}.
#XMSG
DeltaThreadErrorMsg=Syötä arvo väliltä 1–10.
#XMSG
MaxPartitionErrorMsg=Syötä arvo välillä 1–2147483647. Oletusarvo on 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Syötä kokonaisluku väliltä {0}–{1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Käytä välittäjän replikointikerrointa
#XFLD
serializationFormat=Sarjallistamismuoto
#XFLD
compressionType=Tiivistystyyppi
#XFLD
schemaRegistry=Käytä kaaviorekisteriä
#XFLD
subjectNameStrat=Kohteen nimen strategia
#XFLD
compatibilityType=Yhteensopivuustyyppi
#XFLD
confluentTopicName=Aiheen nimi
#XFLD
confluentRecordName=Tietueen nimi
#XFLD
confluentSubjectNamePreview=Kohteen nimen esikatselu
#XMSG
serializationChangeToastMsgUpdated2=Sarjallistamismuoto muutettu JSON:ksi, koska kaaviorekisteriä ei ole otettu käyttöön. Jos sarjallistamismuoto halutaan muuttaa takaisin AVRO:ksi, kaaviorekisteri pitää ensin ottaa käyttöön.
#XBUT
confluentTopicNameInfo=Aiheen nimi perustuu aina kohdeobjektin nimeen. Voit muuttaa sitä nimeämällä kohdeobjektin uudelleen.
#XMSG
emptyRecordNameValidationHeaderMsg=Syötä tietueen nimi.
#XMSG
emptyPartionHeader=Syötä osioiden lukumäärä.
#XMSG
invalidPartitionsHeader=Syötä sallittu osioiden lukumäärä.
#XMSG
invalidpartitionsDesc=Syötä numero väliltä 1 - 200 000.
#XMSG
emptyrFactorHeader=Syötä replikointikerroin.
#XMSG
invalidrFactorHeader=Syötä sallittu replikointikerroin.
#XMSG
invalidrFactorDesc=Syötä numero väliltä 1 - 32 767.
#XMSG
emptyRecordNameValidationDescMsg=Jos käytetään sarjallistamismuotoa "AVRO", vain seuraavia merkkejä tuetaan:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(alaviiva)
#XMSG
validRecordNameValidationHeaderMsg=Syötä kelpaava tietueen nimi.
#XMSG
validRecordNameValidationDescMsgUpdated=Koska käytössä on sarjallistamismuoto "AVRO", tietueen nimen pitää koostua vain aakkosnumeerisista merkeistä (A - Z, a - Z, 0 - 9) ja alaviivoista (_). Tietueen nimen pitää alkaa kirjaimella tai alaviivalla.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled="Deltalatausten objektisäikeiden lukumäärä" on määritettävissä, kun yhden tai useamman objektin lataustyyppi on "Ensimmäinen ja delta".
#XMSG
invalidTargetName=Virheellinen sarakkeen nimi
#XMSG
invalidTargetNameDesc=Kohdesarakkeen nimen pitää koostua vain aakkosnumeerisista merkeistä (A - Z, a - Z, 0 - 9) ja alaviivoista (_).
#XFLD
consumeOtherSchema=Käytä muita kaavion versioita
#XFLD
ignoreSchemamissmatch=Ohita kaavioristiriita
#XFLD
confleuntDatatruncation=Virhe tietojen lyhennyksessä
#XFLD
isolationLevel=Eristystaso
#XFLD
confluentOffset=Aloituspiste
#XFLD
signavioGroupDeltaFilesByText=Ei mitään
#XFLD
signavioFileFormatText=Parketti
#XFLD
signavioSparkCompatibilityParquetText=Ei
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=Ei

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Ennusteet
#XBUT
txtAdd=Lisää
#XBUT
txtEdit=Muokkaa
#XMSG
transformationText=Lisää projektio ja määritä suodatin tai kohdistus.
#XMSG
primaryKeyRequiredText=Valitse ensisijainen avain Määritä kaavio -toiminnon avulla.
#XFLD
lblSettings=Asetukset
#XFLD
lblTargetSetting={0}: kohdeasetukset
#XMSG
@csvRF=Valitse tiedosto, joka sisältää skeemamäärityksen, jota haluat käyttää kaikkiin kansion tiedostoihin.
#XFLD
lblSourceColumns=Lähdesarakkeet
#XFLD
lblJsonStructure=JSON-rakenne
#XFLD
lblSourceSetting={0}: lähdeasetukset
#XFLD
lblSourceSchemaSetting={0}: lähdekaavion asetukset
#XBUT
messageSettings=Ilmoitusasetukset
#XFLD
lblPropertyTitle1=Objektin ominaisuudet
#XFLD
lblRFPropertyTitle=Replikointivirran ominaisuudet
#XMSG
noDataTxt=Näytettäviä sarakkeita ei ole.
#XMSG
noTargetObjectText=Kohdeobjektia ei ole valittu.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Kohdesarakkeet
#XMSG
searchColumns=Hae sarakkeista
#XTOL
cdcColumnTooltip=Sarake deltasieppausta varten
#XMSG
sourceNonDeltaSupportErrorUpdated=Lähdeobjekti ei tue deltasieppausta.
#XMSG
targetCDCColumnAdded=2 kohdesaraketta lisättiin deltasieppausta varten.
#XMSG
deltaPartitionEnable=Deltalatausten objektisäieraja lisätty lähdeasetuksiin.
#XMSG
attributeMappingRemovalTxt=Poistetaan virheellisiä kohdistuksia, joita ei tueta uudessa kohdeobjektissa.
#XMSG
targetCDCColumnRemoved=2 deltasieppausta varten käytettyä kohdesaraketta poistettiin.
#XMSG
replicationLoadTypeChanged=Lataustyypiksi muutettiin "Ensimmäinen ja delta".
#XMSG
sourceHDLFLoadTypeError=Vaihda lataustyypiksi "Ensimmäinen ja delta".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Jos haluat replikoida SAP Datasphereen objektin lähdeyhteydestä, jonka yhteystyyppi on SAP HANA Cloud - tietojärven tiedostot, sinun on käytettävä lataustyyppiä "Ensimmäinen ja delta".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Ota deltasieppaus käyttöön.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Jos haluat replikoida SAP Datasphereen objektin lähdeyhteydestä, jonka yhteystyyppi on SAP HANA Cloud - tietojärven tiedostot, sinun on otettava deltasieppaus käyttöön.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Muuta kohdeobjektia.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Kohdeobjektia ei voi käyttää, koska deltasieppaus on poistettu käytöstä. Voit joko nimetä kohdeobjektin uudelleen (jolloin uuden objektin voi luoda deltasieppauksen avulla) tai kohdistaa sen olemassa olevaan objektiin, jossa deltasieppaus on otettu käyttöön.
#XMSG
deltaPartitionError=Syötä kelpaava deltalatausten objektisäikeiden lukumäärä.
#XMSG
deltaPartitionErrorDescription=Syötä arvo väliltä 1–10.
#XMSG
deltaPartitionEmptyError=Syötä deltalatausten objektisäikeiden lukumäärä.
#XFLD
@lblColumnDescription=Kuvaus
#XMSG
@lblColumnDescriptionText1=Teknisiä tarkoituksia varten - ABAP-pohjaisten lähdeobjektien, joilla ei ole ensisijaista avainta, replikointiongelmien aiheuttamien kaksinkertaisten tietueiden käsittely.
#XFLD
storageType=Muisti
#XFLD
skipUnmappedColLbl=Ohita kohdistamattomat sarakkeet
#XFLD
abapContentTypeLbl=Sisältötyyppi
#XFLD
autoMergeForTargetLbl=Yhdistä tiedot automaattisesti
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Yleinen
#XFLD
lblBusinessName=Liiketoiminnallinen nimi
#XFLD
lblTechnicalName=Tekninen nimi
#XFLD
lblPackage=Paketti
#XFLD
statusPanel=Ajon tila
#XBTN: Schedule dropdown menu
SCHEDULE=Aikataulu
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Muokkaa aikataulua
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Poista aikataulu
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Luo aikataulu
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Epäonnistunut aikataulun validointitarkistus
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Aikataulua ei voi luoda, koska replikointivirtaa otetaan parhaillaan käyttöön.{0}Odota, kunnes replikointivirta on otettu käyttöön.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Sellaisia replikointivirtoja varten ei voi luoda aikataulua, jotka sisältävät objekteja, joiden lataustyyppi on "Ensimmäinen ja delta".
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Sellaisia replikointivirtoja varten ei voi luoda aikataulua, jotka sisältävät objekteja, joiden lataustyyppi on "Ensimmäinen ja delta / vain delta"
#XFLD : Scheduled popover
SCHEDULED=Ajoitettu
#XFLD
CREATE_REPLICATION_TEXT=Luo replikointivirta
#XFLD
EDIT_REPLICATION_TEXT=Muokkaa replikointivirtaa
#XFLD
DELETE_REPLICATION_TEXT=Poista replikointivirta
#XFLD
REFRESH_FREQUENCY=Tiheys
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Replikointivirtaa ei voi ottaa käyttöön, koska olemassa oleva aikataulu{0} ei tue lataustyyppiä "Ensimmäinen ja delta".{0}{0}Jos haluat ottaa replikointivirran käyttöön, sinun on asetettava kaikkien{0} objektien lataustyypiksi "Vain ensimmäinen". Vaihtoehtoisesti voit poistaa aikataulun, ottaa {0}replikointivirran käyttöön ja käynnistää uuden ajon. Se suoritetaan jatkuvasti{0}, mikä tukee myös objekteja, joiden lataustyyppi on "Ensimmäinen ja delta".
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Replikointivirtaa ei voi ottaa käyttöön, koska olemassa oleva aikataulu{0} ei tue lataustyyppiä "Ensimmäinen ja delta / vain delta".{0}{0}Jos haluat ottaa replikointivirran käyttöön, sinun on asetettava kaikkien{0} objektien lataustyypiksi "Vain ensimmäinen". Vaihtoehtoisesti voit poistaa aikataulun, ottaa {0}replikointivirran käyttöön ja käynnistää uuden ajon. Se suoritetaan jatkuvasti{0}, mikä tukee myös objekteja, joiden lataustyyppi on "Ensimmäinen ja delta".
#XMSG
SCHEDULE_EXCEPTION=Aikataulun lisätietojen haku epäonnistui
#XFLD: Label for frequency column
everyLabel=Joka
#XFLD: Plural Recurrence text for Hour
hoursLabel=Tunti
#XFLD: Plural Recurrence text for Day
daysLabel=Pv
#XFLD: Plural Recurrence text for Month
monthsLabel=Kk
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Min
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Ajoitusmahdollisuuden tietojen haku epäonnistui.
#XFLD :Paused field
PAUSED=Keskeytetty
#XMSG
navToMonitoring=Avaa virtojen valvonnassa
#XFLD
statusLbl=Tila
#XFLD
lblLastRunExecuted=Edellisen ajon käynnistys
#XFLD
lblLastExecuted=Viimeinen ajo
#XFLD: Status text for Completed
statusCompleted=Päätetty
#XFLD: Status text for Running
statusRunning=Käynnissä
#XFLD: Status text for Failed
statusFailed=Epäonnistui
#XFLD: Status text for Stopped
statusStopped=Pysäytetty
#XFLD: Status text for Stopping
statusStopping=Pysäytetään
#XFLD: Status text for Active
statusActive=Aktiivinen
#XFLD: Status text for Paused
statusPaused=Keskeytetty
#XFLD: Status text for not executed
lblNotExecuted=Ei vielä ajettu
#XFLD
messagesSettings=Ilmoitusasetukset
#XTOL
@validateModel=Validointi-ilmoitukset
#XTOL
@hierarchy=Hierarkia
#XTOL
@columnCount=Sarakkeiden lukumäärä
#XMSG
VAL_PACKAGE_CHANGED=Olet kohdistanut tämän objektin pakettiin "{1}". Napsauttamalla Tallenna voit vahvistaa ja validoida tämän muutoksen. Ota huomioon, että kohdistusta pakettiin ei voi kumota tässä muokkausohjelmassa tallentamisen jälkeen.
#XMSG
MISSING_DEPENDENCY=Objektin "{0}" sidonnaisuuksia ei voi ratkaista paketissa "{1}".
#XFLD
deltaLoadInterval=Deltalatausväli
#XFLD
lblHour=Tunteja (0–24)
#XFLD
lblMinutes=Minuutteja (0–59)
#XMSG
maxHourOrMinErr=Syötä arvo väliltä 0–{0}.
#XMSG
maxDeltaInterval=Deltalatausvälin maksimiarvo on 24 tuntia.{0}Muuta minuutti- tai tuntiarvoa sen mukaisesti.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Kohdesäilöpolku
#XFLD
confluentSubjectName=Kohteen nimi
#XFLD
confluentSchemaVersion=Kaavioversio
#XFLD
confluentIncludeTechKeyUpdated=Sisällytä tekninen avain
#XFLD
confluentOmitNonExpandedArrays=Jätä ei-laajennetut taulukot pois
#XFLD
confluentExpandArrayOrMap=Laajenna taulukko tai kartta
#XCOL
confluentOperationMapping=Operaation kohdistus
#XCOL
confluentOpCode=Operaatiokoodi
#XFLD
confluentInsertOpCode=Lisää
#XFLD
confluentUpdateOpCode=Päivitä
#XFLD
confluentDeleteOpCode=Poista
#XFLD
expandArrayOrMapNotSelectedTxt=Ei valittu
#XFLD
confluentSwitchTxtYes=Kyllä
#XFLD
confluentSwitchTxtNo=Ei
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Virhe
#XTIT
executeWarning=Varoitus
#XMSG
executeunsavederror=Tallenna replikointivirta ennen kuin ajat sen.
#XMSG
executemodifiederror=Replikointivirrassa on tallentamattomia muutoksia. Tallenna replikointivirta.
#XMSG
executeundeployederror=Replikointivirta on otettava käyttöön ennen kuin voit ajaa sen.
#XMSG
executedeployingerror=Odota, että käyttöönotto päättyy.
#XMSG
msgRunStarted=Ajo käynnistetty
#XMSG
msgExecuteFail=Replikointivirran ajo epäonnistui
#XMSG
titleExecuteBusy=Odota.
#XMSG
msgExecuteBusy=Valmistelemme tietojasi replikointivirran ajoa varten.
#XTIT
executeConfirmDialog=Varoitus
#XMSG
msgExecuteWithValidations=Replikointivirrassa on validointivirheitä. Replikointivirta-ajo saattaa johtaa virheeseen.
#XMSG
msgRunDeployedVersion=Käyttöön otettavia muutoksia on olemassa. Viimeksi käyttöön otettu replikointivirtaversio ajetaan. Haluatko jatkaa?
#XBUT
btnExecuteAnyway=Aja silti
#XBUT
btnExecuteClose=Sulje
#XBUT
loaderClose=Sulje
#XTIT
loaderTitle=Ladataan
#XMSG
loaderText=Haetaan lisätietoja palvelimesta
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Replikointivirtaa tähän ei-SAP-kohdeyhteyteen ei voi käynnistää,
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=koska tässä kuussa ei ole käytettävissä lähtevää volyymiä.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Pääkäyttäjä voi kasvattaa lähteviä premium-lohkoja tämän
#XMSG
premiumOutBoundRFAdminErrMsgPart2=vuokralaisen osalta, jolloin tälle kuulle saadaan käyttöön lähtevää volyymiä.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Virhe
#XTIT
deployInfo=Tiedot
#XMSG
deployCheckFailException=Käyttöönoton aikana tapahtui poikkeus
#XMSG
deployGBQFFDisabled=Replikointivirtoja, joilla on kohdeyhteys Google BigQueryyn, ei voi tällä hetkellä ottaa käyttöön, koska tämän toiminnon ylläpito on käynnissä.
#XMSG
deployKAFKAFFDisabled=Replikointivirtoja, joilla on kohdeyhteys Apache Kafkaan, ei voi tällä hetkellä ottaa käyttöön, koska tämän toiminnon ylläpito on käynnissä.
#XMSG
deployConfluentDisabled=Replikointivirtoja, joilla on kohdeyhteys Confluent Kafkaan, ei voi tällä hetkellä ottaa käyttöön, koska tämän toiminnon ylläpito on käynnissä.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Seuraavien kohdeobjektien osalta muut tietohakemiston taulut käyttävät jo deltasieppaustaulun nimiä: {0} Sinun pitää nimetä nämä kohdeobjektit uudelleen, jotta voidaan varmistaa, että liittyvät deltasieppaustaulun nimet ovat yksiselitteisiä, ennen kuin voit ottaa replikointivirran käyttöön.
#XMSG
deployDWCSourceFFDisabled=Replikointivirtoja, joiden lähteenä on SAP Datasphere, ei voi tällä hetkellä ottaa käyttöön, koska tämän toiminnon ylläpito on käynnissä.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Replikointivirtoja, joiden lähdeobjekteina on deltayhteensopivia paikallisia tauluja, ei voi tällä hetkellä ottaa käyttöön, koska tämän toiminnon ylläpito on käynnissä
#XMSG
deployHDLFSourceFFDisabled=Replikointivirtoja, joiden lähdeyhteyksien tyyppinä on SAP HANA Cloud - tietojärven tiedostot, ei voi tällä hetkellä ottaa käyttöön, koska ylläpito on käynnissä.
#XMSG
deployObjectStoreAsSourceFFDisabled=Replikointivirtojen, joiden lähteenä ovat pilvitallennustilan tarjoajat, käyttöönotto ei ole tällä hetkellä mahdollista.
#XMSG
deployConfluentSourceFFDisabled=Replikointivirtoja, joiden lähteenä on Confluent Kafka, ei voi tällä hetkellä ottaa käyttöön, koska tämän toiminnon ylläpito on käynnissä.
#XMSG
deployMaxDWCNewTableCrossed=Suurissa replikointivirroissa ei ole mahdollista "tallentaa ja ottaa käyttöön" kerralla. Tallenna replikointivirta ensin, ja ota se sitten käyttöön.
#XMSG
deployInProgressInfo=Käyttöönotto on jo käynnissä.
#XMSG
deploySourceObjectInUse=Lähdeobjekteja {0} käytetään jo replikointivirroissa {1}.
#XMSG
deployTargetSourceObjectInUse=Lähdeobjekteja {0} käytetään jo replikointivirroissa {1}. Kohdeobjekteja {2} käytetään jo replikointivirroissa {3}.
#XMSG
deployReplicationFlowCheckError=Virhe replikointivirran verifikoinnissa: {0}
#XMSG
preDeployTargetObjectInUse=Kohdeobjekteja {0} käytetään jo replikointivirroissa {1}, eikä sinulla voi olla samaa kohdeobjektia kahdessa eri replikointivirrassa. Valitse toinen kohdeobjekti ja yritä uudelleen.
#XMSG
runInProgressInfo=Replikointivirta on jo käynnissä.
#XMSG
deploySignavioTargetFFDisabled=Replikointivirtoja, joiden kohteena on SAP Signavio, ei voi tällä hetkellä ottaa käyttöön, koska tämän toiminnon ylläpito on käynnissä.
#XMSG
deployHanaViewAsSourceFFDisabled=Replikointivirtoja, joissa käytetään näkymiä valitun lähdeyhteyden lähdeobjekteina, ei voi tällä hetkellä ottaa käyttöön. Yritä myöhemmin uudelleen.
#XMSG
deployMsOneLakeTargetFFDisabled=Replikointivirtoja, joiden kohteena on MS OneLake, ei voi tällä hetkellä ottaa käyttöön, koska tämän toiminnon ylläpito on käynnissä.
#XMSG
deploySFTPTargetFFDisabled=Replikointivirtoja, joiden kohteena on SFTP, ei voi tällä hetkellä ottaa käyttöön, koska tämän toiminnon ylläpito on käynnissä.
#XMSG
deploySFTPSourceFFDisabled=Replikointivirtoja, joiden lähteenä on SFTP, ei voi tällä hetkellä ottaa käyttöön, koska tämän toiminnon ylläpito on käynnissä.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Tekninen nimi
#XFLD
businessNameInRenameTarget=Liiketoiminnallinen nimi
#XTOL
renametargetDialogTitle=Nimeä kohdeobjekti uudelleen
#XBUT
targetRenameButton=Nimeä uudelleen
#XBUT
targetRenameCancel=Peruuta
#XMSG
mandatoryTargetName=Sinun pitää syöttää nimi.
#XMSG
dwcSpecialChar=_ (alaviiva) on ainut sallittu erikoismerkki.
#XMSG
dwcWithDot=Kohdetaulun nimi voi koostua latinalaisista aakkosista, numeroista, alaviivoista (_) ja pisteistä (.). Ensimmäisen merkin tulee olla kirjain, numero tai alaviiva (ei piste).
#XMSG
nonDwcSpecialChar=Sallittuja erikoismerkkejä ovat _ (alaviiva), - (yhdysmerkki), . (piste)
#XMSG
firstUnderscorePattern=Nimi ei saa alkaa _(alaviivalla)

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: näytä SQL-lause Luo taulu
#XMSG
sqlDialogMaxPKWarning=Google BigQuery tukee enintään 16 ensisijaista avainta, ja lähdeobjektissa niitä on enemmän. Sen vuoksi ensisijaisia avaimia ei määritetä tässä lauseessa.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Yhdessä tai useammassa lähdesarakkeessa on tietotyyppejä, joita ei voi määrittää ensisijaisiksi avaimiksi Google BigQueryssä. Tässä tapauksessa ei sen vuoksi luoda ensisijaisia avaimia. Google BigQueryssä vain seuraavilla tietotyypeillä voi olla ensisijainen avain: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Kopioi ja sulje
#XBUT
closeDDL=Sulje
#XMSG
copiedToClipboard=Kopioitu leikepöydälle


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Objekti ''{0}'' ei voi olla tehtäväketjun osa, koska sillä ei ole loppua (sillä se sisältää objekteja, joiden lataustyyppi on Ensimmäinen ja delta / vain delta).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Objekti ''{0}'' ei voi olla tehtäväketjun osa, koska sillä ei ole loppua (sillä se sisältää objekteja, joiden lataustyyppi on Ensimmäinen ja delta).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objektia "{0}" ei voi lisätä tehtäväketjuun.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Tallentamattomia kohdeobjekteja. Tallenna uudelleen.{0}{0} Ominaisuuden käyttäytyminen on muuttunut: Ennen kohdeobjektit luotiin vain kohdeympäristössä, kun replikointivirta oli otettu käyttöön.{0} Nyt objektit luodaan jo, kun replikointivirta tallennetaan. Replikointivirtasi luotiin ennen muutosta, ja se sisältää uusia objekteja.{0} Tallenna replikointivirta uudelleen ennen sen käyttöönottoa, jotta uudet objektit sisällytetään oikein.
#XMSG
confirmChangeContentTypeMessage=Olet muuttamassa sisältötyyppiä. Jos teet niin, kaikki olemassa olevat projektiot poistetaan.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Kohteen nimi
#XFLD
schemaDialogVersionName=Kaavioversio
#XFLD
includeTechKey=Sisällytä tekninen avain
#XFLD
segementButtonFlat=Rakenteeton
#XFLD
segementButtonNested=Sisäkkäinen
#XMSG
subjectNamePlaceholder=Hae kohteen nimi

#XMSG
@EmailNotificationSuccess=Ajonaikaisten sähköposti-ilmoitusten konfiguraatio on tallennettu.

#XFLD
@RuntimeEmailNotification=Ajonaikainen sähköposti-ilmoitus

#XBTN
@TXT_SAVE=Tallenna


