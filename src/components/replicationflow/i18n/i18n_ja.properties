#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=複製フロー

#XFLD: Edit Schema button text
editSchema=スキーマを編集

#XTIT : Properties heading
configSchema=スキーマの設定

#XFLD: save changed button text
applyChanges=変更を適用


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=ソース接続選択
#XFLD
sourceContainernEmptyText=コンテナ選択
#XFLD
targetConnectionEmptyText=ターゲット接続選択
#XFLD
targetContainernEmptyText=コンテナ選択
#XFLD
sourceSelectObjectText=ソースオブジェクトの選択
#XFLD
sourceObjectCount=ソースオブジェクト ({0})
#XFLD
targetObjectText=ターゲットオブジェクト
#XFLD
confluentBrowseContext=コンテキストを選択
#XBUT
@retry=再試行
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=テナントアップグレードが進行中です。

#XTOL
browseSourceConnection=ソース接続参照
#XTOL
browseTargetConnection=ターゲット接続参照
#XTOL
browseSourceContainer=ソースコンテナ参照
#XTOL
browseAndAddSourceDataset=ソースオブジェクトを追加
#XTOL
browseTargetContainer=ターゲットコンテナ参照
#XTOL
browseTargetSetting=ターゲット設定参照
#XTOL
browseSourceSetting=ソース設定参照
#XTOL
sourceDatasetInfo=情報
#XTOL
sourceDatasetRemove=削除
#XTOL
mappingCount=これは、名前に基づかないマッピング/式の合計数を表しています。
#XTOL
filterCount=これは、フィルタ条件の合計数を表してします。
#XTOL
loading=ロードしています...
#XCOL
deltaCapture=デルタキャプチャ
#XCOL
deltaCaptureTableName=デルタキャプチャテーブル
#XCOL
loadType=タイプをロード
#XCOL
deleteAllBeforeLoading=ロード前にすべて削除
#XCOL
transformationsTab=射影
#XCOL
settingsTab=設定

#XBUT
renameTargetObjectBtn=ターゲットオブジェクト名を変更
#XBUT
mapToExistingTargetObjectBtn=既存のターゲットオブジェクトへマッピング
#XBUT
changeContainerPathBtn=コンテナパスを変更
#XBUT
viewSQLDDLUpdated=SQL の Create Table 文を表示
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=ソースオブジェクトではデルタキャプチャはサポートされていませんが、選択したターゲットオブジェクトではデルタキャプチャオプションが有効になっています。
#XMSG
sourceObjectNonDeltaSupportErrorMsg=ターゲットオブジェクトでデルタキャプチャが有効化されているのに、{0}ソースオブジェクトでデルタキャプチャがサポートされていないため、ターゲットオブジェクトを使用できません。{1}デルタキャプチャがサポートされていない別のターゲットを選択することができます。
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=この名前のターゲットオブジェクトはすでに存在します。ただし、{0}ターゲットオブジェクトでデルタキャプチャが有効化されているのに、ソースオブジェクトでデルタキャプチャが{0}サポートされていないため、ターゲットオブジェクトを使用できません。{1}デルタキャプチャが{0}サポートされていない既存のターゲットオブジェクトの名前を入力するか、またはまだ存在していないターゲットオブジェクトの名前を入力することができます。
#XBUT
copySQLDDLUpdated=SQL の Create Table 文をコピー
#XMSG
targetObjExistingNoCDCColumnUpdated=Google BigQuery の既存のテーブルには、変更データキャプチャ (CDC) のために以下の列が含まれている必要があります:{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=次のソースオブジェクトは、一次キーがないか、一次キーを取得するための条件を満たさない接続が使用されているため、サポートされません: 
#XMSG
new_infoForUnsupportedDatasetNoKeys2=可能なソリューションについては、SAP KBA 3531135 を参照してください。
#XLST: load type list values
initial=初期のみ
@emailUpdateError=電子メール通知一覧の更新中にエラーが発生しました

#XLST
initialDelta=初期およびデルタ

#XLST
deltaOnly=デルタのみ
#XMSG
confluentDeltaLoadTypeInfo=Confluent Kafka ソースでは、"初期およびデルタ" ロードタイプのみがサポートされます。
#XMSG
confirmRemoveReplicationObject=複製の削除を確認しますか?
#XMSG
confirmRemoveReplicationTaskPrompt=このアクションは既存の複製を削除します。続行しますか?
#XMSG
confirmTargetConnectionChangePrompt=このアクションは、ターゲット接続、ターゲットコンテナをリセットし、すべてのターゲットオブジェクトを削除します。続行しますか?
#XMSG
confirmTargetContainerChangePrompt=このアクションは、ターゲットコンテナをリセットし、既存のすべてのターゲットオブジェクトを削除します。続行しますか?
#XMSG
confirmRemoveTransformObject=射影 {0} の削除を確認しますか?
#XMSG
ErrorMsgContainerChange=コンテナパスの変更中にエラーが発生しました。
#XMSG
infoForUnsupportedDatasetNoKeys=次のソースオブジェクトは、一次キーがないため、サポートされていません:
#XMSG
infoForUnsupportedDatasetView=ビュータイプの次のソースオブジェクトはサポートされていません: 
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=次のソースオブジェクトは、入力パラメータを含む SQL ビューであるため、サポートされません。
#XMSG
infoForUnsupportedDatasetExtractionDisabled=次のソースオブジェクトは、抽出が無効化されているため、サポートされていません:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Confluent 接続では、許可されるシリアル化形式は AVRO および JSON のみです。次のオブジェクトは、異なるシリアル化形式が使用されているため、サポートされません: 
#XMSG
infoForUnsupportedDatasetSchemaNotFound=以下のオブジェクトのスキーマをフェッチできません。適切なコンテキストを選択するか、Schema Registry 設定を確認してください
#XTOL: warning dialog header on deleting replication task
deleteHeader=削除
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Google BigQuery で "ロード前にすべて削除" 設定はサポートされていません。
#XBUT
DeleteAllBeforeLoadingConfluentInfo=各複製前に、"ロード前にすべて削除" 設定によってオブジェクト (トピック) が削除、再作成されます。これにより、割り当てられているメッセージもすべて削除されます。
#XTOL
DeleteAllBeforeLoadingLTFInfo=このターゲットタイプで "ロード前にすべて削除" 設定はサポートされていません。
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=技術名
#XCOL
connBusinessName=ビジネス名
#XCOL
connDescriptionName=説明
#XCOL
connType=タイプ
#XMSG
connTblNoDataFoundtxt=接続が見つかりません
#XMSG
connectionError=接続のフェッチ中にエラーが発生しました。
#XMSG
connectionCombinationUnsupportedErrorTitle=接続の組み合わせはサポートされていません
#XMSG
connectionCombinationUnsupportedErrorMsgTxt={0} から {1} への複製は現在サポートされていません。
#XMSG
invalidTargetforSourceHDLFErrorTitle=接続タイプの組み合わせはサポートされていません
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=接続タイプが SAP HANA Cloud データレイクファイルの接続から {0} への複製はサポートされていません。SAP Datasphere にのみ複製できます。

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=選択
#XBUT
containerCancelBtn=キャンセル
#XTOL
containerSelectTooltip=選択
#XTOL
containerCancelTooltip=キャンセル
#XMSG
containerContainerPathPlcHold=コンテナパス
#XFLD
containerContainertxt=コンテナ
#XFLD
confluentContainerContainertxt=コンテキスト
#XMSG
infoMessageForSLTSelection=コンテナとして許可されているのは、/SLT/一括転送 ID のみです。SLT の下で一括転送 ID を選択し (利用可能な場合)、送信をクリックします。
#XMSG
msgFetchContainerFail=コンテナデータのフェッチ中にエラーが発生しました。
#XMSG
infoMessageForSLTHidden=この接続で SLT フォルダはサポートされていないため、リストビューに表示されません。

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=サブフォルダが含まれるコンテナを選択してください。
#XMSG
sftpIncludeSubFolderText=False
#XMSG
sftpIncludeSubFolderTextNew=いいえ

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(フィルタマッピングはまだありません)
#XMSG
failToFetchRemoteMetadata=メタデータのフェッチ中にエラーが発生しました。
#XMSG
failToFetchData=既存のターゲットのフェッチ中にエラーが発生しました。
#XCOL
@loadType=タイプをロード
#XCOL
@deleteAllBeforeLoading=ロード前にすべて削除

#XMSG
@loading=ロードしています...
#XFLD
@selectSourceObjects=ソースデータオブジェクトの選択
#XMSG
@exceedLimit=一度に {0} 個を超えるオブジェクトをインポートすることはできません。少なくとも {1} 個のオブジェクトを選択解除してください。
#XFLD
@objects=オブジェクト
#XBUT
@ok=OK
#XBUT
@cancel=キャンセル
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=次
#XBUT
btnAddSelection=選択を追加
#XTOL
@remoteFromSelection=選択から削除
#XMSG
@searchInForSearchField={0} で検索

#XCOL
@name=技術名
#XCOL
@type=タイプ
#XCOL
@location=ロケーション
#XCOL
@label=ビジネス名
#XCOL
@status=ステータス

#XFLD
@searchIn=検索範囲:
#XBUT
@available=利用可能
#XBUT
@selection=選択

#XFLD
@noSourceSubFolder=テーブルおよびビュー
#XMSG
@alreadyAdded=すでに図に存在します
#XMSG
@askForFilter=アイテムの数が {0} を超えています。フィルタ文字列を入力して、アイテムの数を減らしてください。
#XFLD: success label
lblSuccess=正常終了
#XFLD: ready label
lblReady=準備完了
#XFLD: failure label
lblFailed=失敗
#XFLD: fetching status label
lblFetchingDetail=詳細をフェッチしています

#XMSG Place holder text for tree filter control
filterPlaceHolder=最上位オブジェクトをフィルタするためのテキストを入力してください
#XMSG Place holder text for server search control
serverSearchPlaceholder=入力したら Enter を押して検索
#XMSG
@deployObjects={0} 件のオブジェクトをインポート...
#XMSG
@deployObjectsStatus=インポートされたオブジェクトの数は {0} です。インポートできなかったオブジェクトの数は {1} です。

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=ローカルのリポジトリブラウザを開けませんでした。
#XMSG
@openRemoteSourceBrowserError=ソースオブジェクトをフェッチできませんでした。
#XMSG
@openRemoteTargetBrowserError=ターゲットオブジェクトをフェッチできませんでした。
#XMSG
@validatingTargetsError=ターゲットのチェック中にエラーが発生しました。
#XMSG
@waitingToImport=インポート可能

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=オブジェクトの最大数を超えています。1 つの複製フローに対して最大 500 のオブジェクトを選択してください。

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=技術名
#XFLD
sourceObjectBusinessName=ビジネス名
#XFLD
sourceNoColumns=列数
#XFLD
containerLbl=コンテナ

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=複製フローのソース接続を選択する必要があります。
#XMSG
validationSourceContainerNonExist=ソース接続のコンテナを選択する必要があります。
#XMSG
validationTargetNonExist=複製フローのターゲット接続を選択する必要があります。
#XMSG
validationTargetContainerNonExist=ターゲット接続のコンテナを選択する必要があります。
#XMSG
validationTruncateDisabledForObjectTitle=オブジェクトストレージへの複製。
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=クラウドストレージへの複製は、"ロード前にすべて削除" オプションが設定されているか、ターゲットにターゲットオブジェクトが存在しない場合にのみ可能です。{0}{0}"ロード前にすべて削除" オプションが設定されていないオブジェクトへの複製を有効にするには、複製フローを実行する前にシステムにターゲットオブジェクトが存在しないことを確認してください。
#XMSG
validationTaskNonExist=複製フローには複製が少なくとも 1 つは存在している必要があります。
#XMSG
validationTaskTargetMissing=ソース {0} に複製のターゲットが存在している必要があります。
#XMSG
validationTaskTargetIsSAC=選択したターゲットは SAC アーティファクトです: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=選択したターゲットはサポートされているローカルテーブルではありません: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=この名前のオブジェクトはターゲットにすでに存在します。ただし、ターゲットがローカルテーブルでないため、このオブジェクトをローカルリポジトリに対する複製フローのターゲットオブジェクトとして使用することはできません。
#XMSG
validateSourceTargetSystemDifference=この複製フローに対しては、ソース接続とターゲット接続、およびコンテナの別の組み合わせを選択する必要があります。
#XMSG
validateDuplicateSources=1 つ以上の複製でソースオブジェクト名が重複しています: {0}。
#XMSG
validateDuplicateTargets=1 つ以上の複製でターゲットオブジェクト名が重複しています: {0}。
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=ソースオブジェクト {0} ではデルタキャプチャはサポートされていませんが、ターゲットオブジェクト {1} ではサポートされています。複製を削除する必要があります。
#XMSG
validationTaskTargetObjectLoadTypeMismatch=ターゲットオブジェクト名 {0} の複製に対して、ロードタイプ "初期およびデルタ" を選択する必要があります。
#XMSG
validationAutoRenameTarget=ターゲット列の名前が変更されました。
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=自動射影が追加され、ターゲットへの複製を可能にするために、以下のターゲット列の名前が変更されました:{1}{1} {0} {1}{1}これは、以下のいずれかの理由によるものです:{1}{1}{2} 文字がサポートされていない{1}{2} 接頭辞が予約済み
#XMSG
validationAutoRenameTargetDescriptionUpdated=自動射影が追加され、Google BigQuery への複製を可能にするために、以下のターゲット列の名前が変更されました:{1}{1} {0} {1}{1}これは、以下のいずれかの理由によるものです:{1}{1}{2} 列名が予約済み{1}{2} 文字がサポートされていない{1}{2} 接頭辞が予約済み
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=自動射影が追加され、Confluent への複製を可能にするために、以下のターゲット列の名前が変更されました:{1}{1} {0} {1}{1}これは、以下のいずれかの理由によるものです:{1}{1}{2} 列名が予約済み{1}{2} 文字がサポートされていない{1}{2} 接頭辞が予約済み
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=自動射影が追加され、ターゲットへの複製を可能にするために、以下のターゲット列の名前が変更されました:{1}{1} {0} {1}{1}これは、以下のいずれかの理由によるものです:{1}{1}{2} 列名が予約済み{1}{2} 文字がサポートされていない{1}{2} 接頭辞が予約済み
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=ターゲットオブジェクトの名前が変更されました。
#XMSG
autoRenameInfoDesc=ターゲットオブジェクトにサポートされていない文字が含まれていたため、ターゲットオブジェクトの名前が変更されました。次の文字のみサポートされます:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(ドット){0}{1}_(アンダースコア){0}{1}-(ダッシュ)
#XMSG
validationAutoTargetTypeConversion=ターゲットデータ型が変更されました。
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=以下のターゲット列について、Google BigQuery でソースデータ型がサポートされていないため、ターゲットデータ型が変更されました:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=以下のターゲット列について、ターゲット接続でソースデータ型がサポートされていないため、ターゲットデータ型が変更されました:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=ターゲット列名の短縮
#XMSG
validationMaxCharLengthGBQTargetDescription=Google BigQuery では、列名の長さは最大で 300 文字です。射影を使用して以下のターゲット列名を短縮してください:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=一次キーは作成されません。
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=Google BigQuery では、最大で 16 個の一次キーがサポートされていますが、ソースオブジェクトにはこれよりも多くの一次キーがあります。ターゲットオブジェクトに一次キーは 1 つも作成されません。
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=1 つまたは複数のソース列に、Google BigQuery で一次キーとして定義できないデータ型があります。ターゲットオブジェクトに一次キーは 1 つも作成されません。{0}{0}以下の各ターゲットデータ型は、一次キーを定義できる Google BigQuery データ型と互換性があります:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=1 つまたは複数の列を一次キーとして定義してください。
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=1 つまたは複数の列を一次キーとして定義する必要があります。これを行うには、ソーススキーマダイアログを使用してください。
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=1 つまたは複数の列を一次キーとして定義してください。
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=ソースオブジェクトの一次キー制約に一致する一次キーとして、1 つまたは複数の列を定義する必要があります。それを行うには、ソースオブジェクトプロパティの "スキーマの設定" に移動してください。
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=有効な最大パーティション値を入力してください。
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=最大パーティション値は 1 以上 2147483647 以下である必要があります
#XMSG
validateHDLFNoPKDatasetError=1 つまたは複数の列を一次キーとして定義してください。
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=オブジェクトを複製するには、1 つまたは複数のターゲット列を一次キーとして定義する必要があります。これを行うには、射影を使用してください。
#XMSG
validateHDLFNoPKExistingDatasetError=1 つまたは複数の列を一次キーとして定義してください。
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=既存のターゲットオブジェクトにデータを複製するには、一次キーとして定義されている 1 つまたは複数の列が必要です。{0}1 つまたは複数の列を一次キーとして定義するオプションは次のとおりです: {0} {1}ローカルテーブルエディタを使用して、既存のターゲットオブジェクトを変更します。次に、複製フローをリロードします。{0}{1}複製フローでターゲットオブジェクトの名前を変更します。実行が開始されるとすぐに、新規オブジェクトが作成されます。名前の変更後、射影において 1 つまたは複数の列を一次キーとして定義できます。{0}{1}1 つまたは複数の列がすでに一次キーとして定義されている別の既存ターゲットオブジェクトに、オブジェクトをマッピングします。
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=選択したターゲットはリポジトリ: {0} にすでに存在します。
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=デルタキャプチャテーブル名がリポジトリの他のテーブルによってすでに使用されています: {0} 。複製フローを保存する前に、これらのターゲットオブジェクト名を変更して、関連付けられているデルタキャプチャテーブル名が一意であることを確認する必要があります。
#XMSG
validateConfluentEmptySchema=スキーマを定義
#XMSG
validateConfluentEmptySchemaDescUpdated=ソーステーブルにスキーマがありません。"スキーマの設定" を選択してスキーマを定義してください
#XMSG
validationCSVEncoding=無効な CSV エンコーディング
#XMSG
validationCSVEncodingDescription=タスクの CSV エンコーディングが無効です。
#XMSG
validateConfluentEmptySchema=互換性のあるターゲットデータ型を選択してください
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=互換性のあるターゲットデータ型を選択してください
#XMSG
globalValidateTargetDataTypeDesc=列のマッピングでエラーが発生しました。射影に移動し、すべてのソース列が一意の列とマッピングされていること、互換性のあるデータ型の列とマッピングされていること、定義されたすべての式が有効であることを確認してください。
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=重複列名。
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=重複列名はサポートされていません。列名を修正するには、射影ダイアログを使用してください。次のターゲットオブジェクトに重複列名があります: {0}。
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=重複列名。
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=重複列名はサポートされていません。次のターゲットオブジェクトに重複列名があります: {0}。
#XMSG
deltaOnlyLoadTypeTittle=データに不整合がある可能性があります。
#XMSG
deltaOnlyLoadTypeDescriptionUpdated="デルタのみ" ロードタイプでは、最後の保存から次回の実行までの間にソースで行われた変更は考慮されません。
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=ロードタイプを "初期" に変更してください。
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=一次キーがない ABAP ベースオブジェクトの複製は "初期のみ" ロードタイプでのみ可能です。
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=デルタキャプチャを無効化してください。
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=ソース接続タイプ ABAP を使用して一次キーのないオブジェクトを複製するには、最初にこのテーブルのデルタキャプチャを無効化する必要があります。
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=ターゲットオブジェクトを変更してください。
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=デルタキャプチャが有効化されているため、ターゲットオブジェクトは使用できません。ターゲットオブジェクトの名前を変更してから新規 (名前が変更された) オブジェクトのデルタキャプチャをオフに切り替えるか、デルタキャプチャが無効化されたターゲットオブジェクトにソースオブジェクトをマッピングすることができます。
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=ターゲットオブジェクトを変更してください。
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=ターゲットオブジェクトに必要な技術列 __load_package_id がないため、ターゲットオブジェクトは使用できません。まだ存在しない名前を使用してターゲットオブジェクトの名前を変更することができます。それにより、ソースオブジェクトと同じ定義の新規オブジェクトが作成され、必要な技術列が含まれます。また、必要な技術列 (__load_package_id) がある既存オブジェクトにターゲットオブジェクトをマッピングすることができます。
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=ターゲットオブジェクトを変更してください。
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=ターゲットオブジェクトに必要な技術列 __load_record_id がないため、ターゲットオブジェクトは使用できません。まだ存在しない名前を使用してターゲットオブジェクトの名前を変更することができます。それにより、ソースオブジェクトと同じ定義の新規オブジェクトが作成され、必要な技術列が含まれます。また、必要な技術列 (__load_record_id) がある既存オブジェクトにターゲットオブジェクトをマッピングすることができます。その
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=ターゲットオブジェクトを変更してください。
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=ターゲットオブジェクトの技術列 __load_record_id のデータ型が "string(44)" でないため、ターゲットオブジェクトは使用できません。まだ存在しない名前を使用してターゲットオブジェクトの名前を変更することができます。それにより、ソースオブジェクトと同じ定義 (結果として正しいデータ型) の新規オブジェクトが作成されます。また、正しいデータ型の必要な技術列 (__load_record_id) がある既存オブジェクトにターゲットオブジェクトをマッピングすることができます。
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=ターゲットオブジェクトを変更してください。
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=ソースオブジェクトには一次キーがありませんが、ターゲットオブジェクトにはあるため、ターゲットオブジェクトは使用できません。まだ存在しない名前を使用してターゲットオブジェクトの名前を変更することができます。それにより、ソースオブジェクトと同じ定義の (結果として一次キーのない) 新規オブジェクトが作成されます。また、一次キーのない必要な技術列 (__load_package_id) がある既存オブジェクトにターゲットオブジェクトをマッピングすることができます。
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=ターゲットオブジェクトを変更してください。
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=ソースオブジェクトには一次キーがありませんが、ターゲットオブジェクトにはあるため、ターゲットオブジェクトは使用できません。まだ存在しない名前を使用してターゲットオブジェクトの名前を変更することができます。それにより、ソースオブジェクトと同じ定義の (結果として一次キーのない) 新規オブジェクトが作成されます。また、一次キーのない必要な技術列 (__load_record_id) がある既存オブジェクトにターゲットオブジェクトをマッピングすることができます。
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=ターゲットオブジェクトを変更してください。
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=ターゲットオブジェクトの技術列 __load_package_id のデータ型が "binary(>=256)" でないため、ターゲットオブジェクトは使用できません。まだ存在しない名前を使用してターゲットオブジェクトの名前を変更することができます。それにより、ソースオブジェクトと同じ定義 (結果として正しいデータ型) の新規オブジェクトが作成されます。また、正しいデータ型の必要な技術列 (__load_package_id) がある既存オブジェクトにターゲットオブジェクトをマッピングすることができます。
#XMSG
validationAutoRenameTargetDPID=ターゲット列の名前が変更されました。
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=ソースオブジェクトを削除してください。
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=ソースオブジェクトにキー列がありません。キー列はこのコンテキストではサポートされていません。
#XMSG
validationAutoRenameTargetDPIDDescription=自動射影が追加され、キーのない ABAP ソースからの複製を可能にするために、次のターゲット列の名前が変更されました: {1}{1} {0} {1}{1}これは次のいずれかの理由によるものです: {1}{1}{2} 列名が予約済み{1}{2} 文字がサポートされていない{1}{2} 接頭辞が予約済み
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle={0} への複製。
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=ターゲットとして {0} がある複製フローを保存してデプロイすることは現在できません。これは、この機能について保全活動が実施中であるためです。
#XMSG
TargetColumnSkippedLTF=ターゲット列がスキップされました。
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=サポートされていないデータ型であるため、ターゲット列はスキップされました。{0}{1}
#XMSG
validatePKTimeColumnLTF1=一次キーとしての時間列。
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=ソースオブジェクトに一次キーとして時間列があります。時間列はこのコンテキストではサポートされていません。
#XMSG
validateNoPKInLTFTarget=一次キーがありません。
#XMSG
validateNoPKInLTFTargetDescription=ターゲットで一次キーが定義されていません。一次キーはこのコンテキストではサポートされていません。
#XMSG
validateABAPClusterTableLTF=ABAP クラスタテーブル。
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=ソースオブジェクトは ABAP クラスタテーブルです。クラスタテーブルはこのコンテキストではサポートされていません。
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=データがまだ追加されていないようです。
#YINS
welcomeText2=複製フローを開始するには、左側で接続とソースオブジェクトを選択します。

#XBUT
wizStep1=ソース接続選択
#XBUT
wizStep2=ソースコンテナ選択
#XBUT
wizStep3=ソースオブジェクト追加

#XMSG
limitDataset=オブジェクトの最大数に達しました。既存のオブジェクトを削除して新しいオブジェクトを追加するか、新しい複製フローを作成してください。
#XMSG
premiumOutBoundRFCannotStartWarningMsg=今月利用可能な送信ボリュームがないため、この非 SAP ターゲット接続に対する複製フローを開始できません。
#XMSG
premiumOutBoundRFAdminWarningMsg=管理者は、このテナントのプレミアム送信ブロックを増やして、今月の送信ボリュームを利用できるようにすることができます。
#XMSG
messageForToastForDPIDColumn2={0} 個のオブジェクトのターゲットに追加された新規列 - 一次キーのない ABAP ベースソースオブジェクトとの接続において重複レコードを処理するために必要です。
#XMSG
PremiumInboundWarningMessage=複製フローの数および複製するデータボリュームによっては、{1} を介してデータを複製するために必要な SAP HANA リソース {0} がテナントで利用可能なキャパシティを超過する可能性があります。
#XMSG
PremiumInboundWarningMsg=複製フローの数および複製するデータボリュームによっては、"{1}" を介してデータを複製するために必要な SAP HANA リソース {0} がテナントで利用可能なキャパシティを超過する可能性があります。
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=射影名を入力してください。
#XMSG
emptyTargetColumn=ターゲット列名を入力してください。
#XMSG
emptyTargetColumnBusinessName=ターゲット列のビジネス名を入力してください。
#XMSG
invalidTransformName=射影名を入力してください。
#XMSG
uniqueColumnName=ターゲット列名を変更してください。
#XMSG
copySourceColumnLbl=ソースオブジェクトから列をコピーしてください
#XMSG
renameWarning=ターゲットテーブル名の変更時には一意の名前を選択するようにしてください。新規名のテーブルがスペースにすでに存在する場合は、そのテーブルの定義が使用されます。

#XMSG
uniqueColumnBusinessName=ターゲット列のビジネス名を変更してください。
#XMSG
uniqueSourceMapping=別のソース列を選択してください。
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=ソース列 {0} は次のターゲット列ですでに使用されています:{1}{1}{2}{1}{1} 射影を保存するには、このターゲット列または他のターゲット列に対して、まだ使用されていないソース列を選択してください。
#XMSG
uniqueColumnNameDescription=入力したターゲット列名はすでに存在します。射影を保存できるようにするには、一意の列名を指定する必要があります。
#XMSG
uniqueColumnBusinessNameDesc=ターゲット列のビジネス名はすでに存在します。射影を保存するには、一意の列ビジネス名を入力する必要があります。
#XMSG
emptySource=ソース列を選択するか、定数を入力してください。
#XMSG
emptySourceDescription=有効なマッピングエントリを作成するには、ソース列を選択するか、定数値を入力する必要があります。
#XMSG
emptyExpression=マッピングを定義してください。
#XMSG
emptyExpressionDescription1=ターゲット列にマッピングするソース列を選択するか、または {0} 関数/定数 {1} 列のチェックボックスを選択します。{2} {2} 関数はターゲットのデータ型に応じて自動的に入力されます。定数値はマニュアルで入力できます。
#XMSG
numberExpressionErr=数値を入力してください。
#XMSG
numberExpressionErrDescription=数値データ型を選択しました。そのため、入力できるのは数字、さらに該当する場合は小数点のみになります。一重引用符は使用しないでください。
#XMSG
invalidLength=有効な長さ値を入力してください。
#XMSG
invalidLengthDescription=データ型の長さはソース列の長さ以上にする必要があり、1 から 5000 までが可能です。
#XMSG
invalidMappedLength=有効な長さ値を入力してください。
#XMSG
invalidMappedLengthDescription=データ型の長さはソース列 {0} の長さ以上にする必要があり、1 から 5000 までが可能です。
#XMSG
invalidPrecision=有効な精度値を入力してください。
#XMSG
invalidPrecisionDescription=精度によって合計桁数が定義されます。スケールによって小数点以下の桁数が定義され、スケールは 0 と精度の間にすることができます。{0}{0} 例: {0}{1} 精度 6、スケール 2 は 1234.56 のような数値に対応します。{0}{1} 精度 6、スケール 6 は 0.123546 のような数値に対応します。{0} {0} ソースの桁がすべてターゲットフィールドに収まるために、ターゲットの精度とスケールは、ソースの精度とスケールと互換性がある必要があります。たとえば、ソースの精度が 6 でスケールが 2 である場合 (そのため 0 以外の数字が小数点の左側にある場合) に、ターゲットを精度 6 およびスケール 6 にすることはできません。
#XMSG
invalidPrimaryKey=少なくとも 1 つの一次キーを入力してください。
#XMSG
invalidPrimaryKeyDescription=このスキーマでは一次キーが定義されていません。
#XMSG
invalidMappedPrecision=有効な精度値を入力してください。
#XMSG
invalidMappedPrecisionDescription1=精度によって合計桁数が定義されます。スケールによって小数点以下の桁数が定義され、スケールは 0 と精度の間にすることができます。{0}{0} 例:{0}{1} 精度 6、スケール 2 は 1234.56 のような数値に対応します。{0}{1} 精度 6、スケール 6 は 0.123546 のような数値に対応します。{0}{0}データ型の精度はソース ({2}) の精度以上にする必要があります。
#XMSG
invalidScale=有効なスケール値を入力してください。
#XMSG
invalidScaleDescription=精度によって合計桁数が定義されます。スケールによって小数点以下の桁数が定義され、スケールは 0 と精度の間にすることができます。{0}{0} 例: {0}{1} 精度 6、スケール 2 は 1234.56 のような数値に対応します。{0}{1} 精度 6、スケール 6 は 0.123546 のような数値に対応します。{0} {0} ソースの桁がすべてターゲットフィールドに収まるために、ターゲットの精度とスケールは、ソースの精度とスケールと互換性がある必要があります。たとえば、ソースの精度が 6 でスケールが 2 である場合 (そのため 0 以外の数字が小数点の左側にある場合) に、ターゲットを精度 6 およびスケール 6 にすることはできません。
#XMSG
invalidMappedScale=有効なスケール値を入力してください。
#XMSG
invalidMappedScaleDescription1=精度によって合計桁数が定義されます。スケールによって小数点以下の桁数が定義され、スケールは 0 と精度の間にすることができます。{0}{0} 例: {0}{1} 精度 6、スケール 2 は 1234.56 のような数値に対応します。{0}{1} 精度 6、スケール 6 は 0.123546 のような数値に対応します。{0}{0} データ型のスケールはソース ({2}) のスケール以上にする必要があります。
#XMSG
nonCompatibleDataType=互換性のあるターゲットデータ型を選択してください。
#XMSG
nonCompatibleDataTypeDescription1=ここで指定したデータ型は、ソースのデータ型 ({0}) と互換性がある必要があります。{1}{1}たとえば、ソース列のデータ型が文字列であり文字が含まれているのに、ターゲットに 10 進数のデータ型を使用することはできません。
#XMSG
invalidColumnCount=ソース列を選択してください。
#XMSG
ObjectStoreInvalidScaleORPrecision=精度およびスケールの有効な値を入力してください。
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=最初の値は精度です。精度によって合計桁数が定義されます。2 番目の値はスケールです。スケールによって小数点以下の桁数が定義されます。ソーススケール値よりも大きいターゲットスケール値を入力し、入力したターゲットスケール値とターゲット精度値の差異が、ソーススケール値とソース精度値の差異よりも大きいことを確認してください。
#XMSG
InvalidPrecisionORScale=精度およびスケールの有効な値を入力してください。
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=最初の値は制度です。これは桁数の合計を定義します。2 番目の値はスケールです。これは小数点以下の桁数を定義します。{0}{0}このソースデータ型は Google BigQuery でサポートされていないため、ターゲットデータ型で DECIMAL に変換されます。この場合、精度は 38 から 76 の間でのみ定義可能で、またスケールは 9 から 38 の間でのみ定義可能です。さらに、小数点よりも上の桁数を表す精度からスケールを引いた結果が 29 から 38 の間である必要があります。
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=最初の値は精度です。これは桁数の合計を定義します。2 番目の値はスケールです。これは小数点以下の桁数を定義します。{0}{0}このソースデータ型は Google BigQuery でサポートされていないため、ターゲットデータ型で DECIMAL に変換されます。この場合、精度は 20 よりも大きいと定義される必要があります。さらに、小数点よりも上の桁数を表す精度からスケールを引いた結果が 20 以上である必要があります。
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=最初の値は精度です。これは桁数の合計を定義します。2 番目の値はスケールです。これは小数点以下の桁数を定義します。{0}{0}このソースデータ型はターゲットでサポートされていないため、ターゲットデータ型で DECIMAL に変換されます。この場合、精度は 1 以上で 38 以下の数、および精度以下のスケールによって定義される必要があります。
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=最初の値は精度です。これは桁数の合計を定義します。2 番目の値はスケールです。これは小数点以下の桁数を定義します。{0}{0}このソースデータ型はターゲットでサポートされていないため、ターゲットデータ型で DECIMAL に変換されます。この場合、精度は 20 以上と定義される必要があります。さらに、小数点よりも上の桁数を表す精度からスケールを引いた結果が 20 以上である必要があります。
#XMSG
invalidColumnCountDescription=有効なマッピングエントリを作成するには、ソース列を選択するか、定数値を入力する必要があります。
#XMSG
duplicateColumns=ターゲット列名を変更してください。
#XMSG
duplicateGBQCDCColumnsDesc=ターゲット列名が Google BigQuery で予約されています。射影を保存できるようにするには、ターゲット列名を変更する必要があります。
#XMSG
duplicateConfluentCDCColumnsDesc=ターゲット列名が Confluent で予約されています。射影を保存できるようにするには、ターゲット列名を変更する必要があります。
#XMSG
duplicateSignavioCDCColumnsDesc=ターゲット列名が SAP Signavio で予約されています。射影を保存できるようにするには、ターゲット列名を変更する必要があります。
#XMSG
duplicateMsOneLakeCDCColumnsDesc=ターゲット列名が MS OneLake で予約されています。射影を保存できるようにするには、ターゲット列名を変更する必要があります。
#XMSG
duplicateSFTPCDCColumnsDesc=ターゲット列名が SFTP で予約されています。射影を保存できるようにするには、ターゲット列名を変更する必要があります。
#XMSG
GBQTargetNameWithPrefixUpdated1=ターゲット列名に Google BigQuery で予約されている接頭辞が含まれています。射影を保存できるようにするには、ターゲット列名を変更する必要があります。{0}{0}ターゲット列名の先頭を以下の各文字列のいずれかにすることはできません:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=ターゲット列名を短縮してください。
#XMSG
GBQtargetMaxLengthDesc=Google BigQuery では、列名の長さは最大で 300 文字です。射影を保存できるようにするには、ターゲット列名を短縮してください。
#XMSG
invalidMappedScalePrecision=ソースのすべての桁をターゲットフィールドに合わせるには、ターゲットの精度およびスケールがソースの精度およびスケールと互換性がある必要があります。
#XMSG
invalidMappedScalePrecisionShortText=有効な精度値およびスケール値を入力してください。
#XMSG
validationIncompatiblePKTypeDescProjection3=1 つまたは複数のソース列に、Google BigQuery で一次キーとして定義できないデータ型があります。ターゲットオブジェクトに一次キーは 1 つも作成されません。{0}{0}以下の各ターゲットデータ型は、一次キーを定義できる Google BigQuery データ型と互換性があります:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=column __message_id のチェックを解除してください。
#XMSG
uncheckColumnMessageIdDesc=列: 一次キー
#XMSG
validationOpCodeInsert=挿入の値を入力する必要があります。
#XMSG
recommendDifferentPrimaryKey=項目レベルで別の一次キーを選択することをお奨めします。
#XMSG
recommendDifferentPrimaryKeyDesc=操作コードがすでに定義されている場合は、列の重複などの問題を回避するために、配列インデックスおよび項目に別の一次キーを選択することをお奨めします。
#XMSG
selectPrimaryKeyItemLevel=ヘッダレベルと項目レベルの両方に少なくとも 1 つの一次キーを選択する必要があります。
#XMSG
selectPrimaryKeyItemLevelDesc=配列またはマップが展開されている場合は、ヘッダレベルに 1 つ、項目レベルに 1 つの、2 つの一次キーを選択する必要があります。
#XMSG
invalidMapKey=ヘッダレベルに少なくとも 1 つの一次キーを選択する必要があります。
#XMSG
invalidMapKeyDesc=配列またはマップが展開されている場合は、ヘッダレベルで一次キーを選択する必要があります。
#XFLD
txtSearchFields=ターゲット列の検索
#XFLD
txtName=名前
#XMSG
txtSourceColValidation=サポートされていないソース列が 1 つ以上あります。
#XMSG
txtMappingCount=マッピング ({0})
#XMSG
schema=スキーマ
#XMSG
sourceColumn=ソース列
#XMSG
warningSourceSchema=スキーマに加えられた任意の変更は射影ダイアログのマッピングに影響します。
#XCOL
txtTargetColName=ターゲット列 (技術名)
#XCOL
txtDataType=ターゲットデータ型
#XCOL
txtSourceDataType=データソースタイプ
#XCOL
srcColName=ソース列 (技術名)
#XCOL
precision=精度
#XCOL
scale=スケール
#XCOL
functionsOrConstants=関数/定数
#XCOL
txtTargetColBusinessName=ターゲット列 (ビジネス名)
#XCOL
prKey=一次キー
#XCOL
txtProperties=プロパティ
#XBUT
txtOK=保存
#XBUT
txtCancel=キャンセル
#XBUT
txtRemove=削除
#XFLD
txtDesc=説明
#XMSG
rftdMapping=マッピング
#XFLD
@lblColumnDataType=データ型
#XFLD
@lblColumnTechnicalName=技術名
#XBUT
txtAutomap=自動マッピング
#XBUT
txtUp=上
#XBUT
txtDown=下

#XTOL
txtTransformationHeader=射影
#XTOL
editTransformation=編集
#XTOL
primaryKeyToolip=キー


#XMSG
rftdFilter=フィルタ
#XMSG
rftdFilterColumnCount=ソース: {0}({1})
#XTOL
rftdFilterColSearch=検索
#XMSG
rftdFilterColNoData=表示する列がありません
#XMSG
rftdFilteredColNoExps=フィルタ式がありません
#XMSG
rftdFilterSelectedColTxt=フィルタの追加
#XMSG
rftdFilterTxt=利用可能なフィルタ
#XBUT
rftdFilterSelectedAddColExp=式の追加
#YINS
rftdFilterNoSelectedCol=フィルタを追加する列の選択
#XMSG
rftdFilterExp=フィルタ式
#XMSG
rftdFilterNotAllowedColumn=この列でフィルタの追加はサポートされていません。
#XMSG
rftdFilterNotAllowedHead=サポートされない列
#XMSG
rftdFilterNoExp=定義されているフィルタがありません
#XTOL
rftdfilteredTt=フィルタ済み
#XTOL
rftdremoveexpTt=フィルタ式の削除
#XTOL
validationMessageTt=チェックメッセージ
#XTOL
rftdFilterDateInp=日付選択
#XTOL
rftdFilterDateTimeInp=日付/時刻の選択
#XTOL
rftdFilterTimeInp=時刻の選択
#XTOL
rftdFilterInp=値の入力
#XMSG
rftdFilterValidateEmptyMsg={1} 列に対する {0} フィルタ式が空です
#XMSG
rftdFilterValidateInvalidNumericMsg={1} 列に対する {0} フィルタに特定の無効な数値があります
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=フィルタ式には有効な数値を入力する必要があります。
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=ターゲットオブジェクトスキーマが変更された場合は、メインページの "既存のターゲットオブジェクトへマッピング" 機能を使用して変更を適用し、ターゲットオブジェクトとそのソースを再度マッピングしてください。
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=ターゲットテーブルがすでに存在し、マッピングにスキーマ変更が含まれる場合は、複製フローをデプロイする前にターゲットテーブルを適宜変更する必要があります。
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=マッピングにスキーマ変更が含まれる場合は、複製フローをデプロイする前にターゲットテーブルを適宜変更する必要があります。
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=次のサポートされていない列はソース定義からスキップされました: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=次のサポートされていない列はターゲット定義からスキップされました: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=次のオブジェクトは、利用のために公開されているため、サポートされません: {0} {1} {0} {0} 複製フローでテーブルを使用するには、セマンティック用途 (テーブル設定) が{2}分析データセット{2}に設定されていてはなりません。
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=ターゲットオブジェクトは、利用のために公開されているため、使用できません。{0} {0}  複製フローでテーブルを使用するには、セマンティック用途 (テーブル設定) が{1}分析データセット{1}に設定されていてはなりません。
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=この名前のターゲットオブジェクトはすでに存在しますが、利用のために公開されているため、使用できません。{0} {0}  複製フローでテーブルを使用するには、セマンティック用途 (テーブル設定) が{1}分析データセット{1}に設定されていてはなりません。
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=この名前のオブジェクトはターゲットにすでに存在します。{0}ただし、ターゲットがローカルテーブルでないため、このオブジェクトをローカルリポジトリに対する複製フローのターゲットオブジェクトとして使用することはできません。
#XMSG:
targetAutoRenameUpdated=ターゲット列の名前が変更されました。
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Google BigQuery での複製を可能にするために、ターゲット列の名前が変更されました。これは、次のいずれかの理由によるものです:{0} {1}{2}列名が予約済み{3}{2}文字がサポートされていない{3}{2}接頭辞が予約済み{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Confluent での複製を可能にするために、ターゲット列の名前が変更されました。これは、次のいずれかの理由によるものです:{0} {1}{2}列名が予約済み{3}{2}文字がサポートされていない{3}{2}接頭辞が予約済み{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=ターゲットでの複製を可能にするために、ターゲット列の名前が変更されました。これは、次のいずれかの理由によるものです:{0} {1}{2}文字がサポートされていない{3}{2}接頭辞が予約済み{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=ターゲットでの複製を可能にするために、ターゲット列の名前が変更されました。これは、次のいずれかの理由によるものです:{0} {1}{2}列名が予約済み{3}{2}文字がサポートされていない{3}{2}接頭辞が予約済み{3}{4}
#XMSG:
targetAutoDataType=ターゲットデータ型が変更されました。
#XMSG:
targetAutoDataTypeDesc=Google BigQuery でソースデータ型がサポートされていないため、ターゲットデータ型が {0} に変更されました。
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=ターゲット接続でソースデータ型がサポートされていないため、ターゲットデータ型が {0} に変更されました。
#XMSG
projectionGBQUnableToCreateKey=一次キーは作成されません。
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=Google BigQuery では、最大で 16 個の一次キーがサポートされていますが、ソースオブジェクトにはこれよりも多くの一次キーがあります。ターゲットオブジェクトに一次キーは 1 つも作成されません。
#XMSG
HDLFNoKeyError=1 つまたは複数の列を一次キーとして定義してください。
#XMSG
HDLFNoKeyErrorDescription=オブジェクトを複製するには、1 つまたは複数の列を一次キーとして定義する必要があります。
#XMSG
HDLFNoKeyErrorExistingTarget=1 つまたは複数の列を一次キーとして定義してください。
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=既存のターゲットオブジェクトにデータを複製するには、一次キーとして定義されている 1 つまたは複数の列が必要です。{0} {0}1 つまたは複数の列を一次キーとして定義するオプションは次のとおりです: {0}{0}{1}ローカルテーブルエディタを使用して、既存のターゲットオブジェクトを変更します。次に、複製フローをリロードします。{0}{0}{1}複製フローでターゲットオブジェクトの名前を変更します。実行が開始されるとすぐに、新規オブジェクトが作成されます。名前の変更後、射影において 1 つまたは複数の列を一次キーとして定義できます。{0}{0}{1}1 つまたは複数の列がすでに一次キーとして定義されている別の既存ターゲットオブジェクトに、オブジェクトをマッピングします。
#XMSG
HDLFSourceTargetDifferentKeysWarning=一次キーが変更されました。
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=ターゲットオブジェクトの一次キーとしてソースオブジェクトの一次キーとは異なる列を定義しました。後でデータを複製する際に起こり得るデータ破損を回避するために、これらの列によってすべての行が一意に特定されることを確認してください。{0} {0}ソースオブジェクトでは、次の列が一次キーとして定義されています: {0} {1}
#XMSG
duplicateDPIDColumns=ターゲット列名を変更してください。
#XMSG
duplicateDPIDDColumnsDesc1=このターゲット列名は技術列名用に予約されています。別の名前を入力して射影を保存してください。
#XMSG:
targetAutoRenameDPID=ターゲット列の名前が変更されました。
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=キーのない ABAP ソースでの複製を可能にするために、ターゲット列の名前が変更されました。これは、次のいずれかの理由によるものです:{0} {1}{2}列名が予約済み{3}{2}文字がサポートされていない{3}{2}接頭辞が予約済み{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle={0} ターゲット設定
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle={0} ソース設定
#XBUT
connectionSettingSave=保存
#XBUT
connectionSettingCancel=キャンセル
#XBUT: Button to keep the object level settings
txtKeep=保持
#XBUT: Button to overwrite the Object level settings
txtOverwrite=上書き
#XFLD
targetConnectionThreadlimit=初期ロードのターゲットしきい値の制限 (1 から 100)
#XFLD
connectionThreadLimit=初期ロードのソースしきい値の制限 (1 から 100)
#XFLD
maxConnection=複製スレッド数の制限 (1 から 100)
#XFLD
kafkaNumberOfPartitions=パーティションの数
#XFLD
kafkaReplicationFactor=複製係数
#XFLD
kafkaMessageEncoder=メッセージエンコーダ
#XFLD
kafkaMessageCompression=メッセージ圧縮
#XFLD
fileGroupDeltaFilesBy=グループデルタ基準
#XFLD
fileFormat=ファイルタイプ
#XFLD
csvEncoding=CSV エンコーディング
#XFLD
abapExitLbl=ABAP Exit
#XFLD
deltaPartition=デルタロードのオブジェクトスレッド数 (1 から 10)
#XFLD
clamping_Data=データ切り捨てでの失敗
#XFLD
fail_On_Incompatible=互換性がないデータでの失敗
#XFLD
maxPartitionInput=パーティションの最大数
#XFLD
max_Partition=パーティションの最大数を定義
#XFLD
include_SubFolder=サブフォルダを含める
#XFLD
fileGlobalPattern=ファイル名のグローバルパターン
#XFLD
fileCompression=ファイル圧縮
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=ファイル区切り記号
#XFLD
fileIsHeaderIncluded=ファイルヘッダ
#XFLD
fileOrient=方向 (Orient)
#XFLD
gbqWriteMode=書き込みモード
#XFLD
suppressDuplicate=重複を非表示
#XFLD
apacheSpark=Apache Spark 互換性を有効化
#XFLD
clampingDatatypeCb=10 進浮動小数点データ型をクランプ
#XFLD
overwriteDatasetSetting=オブジェクトレベルのターゲット設定を上書き
#XFLD
overwriteSourceDatasetSetting=オブジェクトレベルのソース設定を上書き
#XMSG
kafkaInvalidConnectionSetting={0} から {1} までの数を入力してください。
#XMSG
MinReplicationThreadErrorMsg={0} より大きい数値を入力してください。
#XMSG
MaxReplicationThreadErrorMsg={0} より小さい数値を入力してください。
#XMSG
DeltaThreadErrorMsg=1 から 10 の範囲の値を入力してください。
#XMSG
MaxPartitionErrorMsg=1 <= x <= 2147483647 の値を入力してください。デフォルト値は 10 です。
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal={0} から {1} の範囲の整数を入力してください。
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=ブローカの複製係数を使用
#XFLD
serializationFormat=シリアル化形式
#XFLD
compressionType=圧縮タイプ
#XFLD
schemaRegistry=Schema Registry を使用
#XFLD
subjectNameStrat=件名方針
#XFLD
compatibilityType=互換性タイプ
#XFLD
confluentTopicName=トピック名
#XFLD
confluentRecordName=レコード名
#XFLD
confluentSubjectNamePreview=件名プレビュー
#XMSG
serializationChangeToastMsgUpdated2=Schema Registry が有効化されていないため、シリアル化形式が JSON に変更されました。シリアル化形式を AVRO に戻すには、最初に Schema Registry を有効化する必要があります。
#XBUT
confluentTopicNameInfo=トピック名は常にターゲットオブジェクト名に基づきます。トピック名はターゲットオブジェクト名を変更することで変更できます。
#XMSG
emptyRecordNameValidationHeaderMsg=レコード名を入力してください。
#XMSG
emptyPartionHeader=パーティションの数を入力してください。
#XMSG
invalidPartitionsHeader=パーティションの有効な数を入力してください。
#XMSG
invalidpartitionsDesc=1 から 200,000 までの数値を入力してください。
#XMSG
emptyrFactorHeader=複製係数を入力してください。
#XMSG
invalidrFactorHeader=有効な複製係数を入力してください。
#XMSG
invalidrFactorDesc=1 から 32,767 までの数値を入力してください。
#XMSG
emptyRecordNameValidationDescMsg=シリアル化形式 "AVRO" が使用されている場合は、次の文字のみサポートされます:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(アンダースコア)
#XMSG
validRecordNameValidationHeaderMsg=有効なレコード名を入力してください。
#XMSG
validRecordNameValidationDescMsgUpdated=シリアル化形式 "AVRO" が使用されているため、レコード名は、英数字 (A-Z、a-z、0-9) およびアンダースコア (_) 文字のみで構成する必要があり、文字またはアンダースコアで始まる必要があります。
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=1 つまたは複数のオブジェクトのロードタイプを "初期およびデルタ" に設定するとすぐに、"デルタロードのオブジェクトスレッド数" を設定できます。
#XMSG
invalidTargetName=無効な列名
#XMSG
invalidTargetNameDesc=ターゲット列名は英数字 (A-Z、a-z、0-9) およびアンダースコア (_) 文字のみで構成する必要があります。
#XFLD
consumeOtherSchema=他のスキーマバージョンを利用
#XFLD
ignoreSchemamissmatch=スキーマの不一致を無視
#XFLD
confleuntDatatruncation=データ切り捨てでの失敗
#XFLD
isolationLevel=分離レベル
#XFLD
confluentOffset=開始ポイント
#XFLD
signavioGroupDeltaFilesByText=なし
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=いいえ
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=いいえ

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=射影
#XBUT
txtAdd=追加
#XBUT
txtEdit=編集
#XMSG
transformationText=フィルタまたはマッピング設定のための射影の追加
#XMSG
primaryKeyRequiredText="スキーマの設定" で一次キーを選択してください。
#XFLD
lblSettings=設定
#XFLD
lblTargetSetting={0}: ターゲット設定
#XMSG
@csvRF=フォルダ内のすべてのファイルに適用するスキーマ定義が含まれるファイルを選択してください。
#XFLD
lblSourceColumns=ソース列
#XFLD
lblJsonStructure=JSON 構造
#XFLD
lblSourceSetting={0}: ソース設定
#XFLD
lblSourceSchemaSetting={0}: ソーススキーマ設定
#XBUT
messageSettings=メッセージ設定
#XFLD
lblPropertyTitle1=オブジェクトプロパティ
#XFLD
lblRFPropertyTitle=複製フロープロパティ
#XMSG
noDataTxt=表示する列がありません。
#XMSG
noTargetObjectText=ターゲットオブジェクトが選択されていません。
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=ターゲット列
#XMSG
searchColumns=列の検索
#XTOL
cdcColumnTooltip=デルタキャプチャの列
#XMSG
sourceNonDeltaSupportErrorUpdated=ソースオブジェクトでデルタキャプチャはサポートされていません。
#XMSG
targetCDCColumnAdded=デルタキャプチャに 2 つのターゲット列が追加されました。
#XMSG
deltaPartitionEnable=ソース設定にデルタロードのオブジェクトスレッド制限が追加されました。
#XMSG
attributeMappingRemovalTxt=新しいターゲットオブジェクトでサポートされていない無効なマッピングを削除しています。
#XMSG
targetCDCColumnRemoved=デルタキャプチャに使用されている 2 つのターゲット列が削除されました。
#XMSG
replicationLoadTypeChanged=ロードタイプが "初期およびデルタ" に変更されました。
#XMSG
sourceHDLFLoadTypeError=ロードタイプを "初期およびデルタ" に変更してください。
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=接続タイプが SAP HANA Cloud データレイクファイルのソース接続から SAP Datasphere にオブジェクトを複製するには、"初期およびデルタ" ロードタイプを使用する必要があります。
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=デルタキャプチャを有効化してください。
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=接続タイプが SAP HANA Cloud データレイクファイルのソース接続から SAP Datasphere にオブジェクトを複製するには、デルタキャプチャを有効化する必要があります。
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=ターゲットオブジェクトを変更してください。
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=デルタキャプチャが無効化されているため、ターゲットオブジェクトを使用できません。ターゲットオブジェクトの名前を変更するか (これによってデルタキャプチャが有効化された新規オブジェクトを作成できるようになります)、デルタキャプチャが有効化された既存のオブジェクトにターゲットオブジェクトをマッピングしてください。
#XMSG
deltaPartitionError=デルタロードの有効なオブジェクトスレッド数を入力してください。
#XMSG
deltaPartitionErrorDescription=1 から 10 の範囲の値を入力してください。
#XMSG
deltaPartitionEmptyError=デルタロードのオブジェクトスレッド数を入力してください。
#XFLD
@lblColumnDescription=説明
#XMSG
@lblColumnDescriptionText1=技術目的 - 一次キーのない ABAP ベースソースオブジェクトの複製時の問題によって発生した重複レコードの処理。
#XFLD
storageType=ストレージ
#XFLD
skipUnmappedColLbl=マッピングされていない列をスキップ
#XFLD
abapContentTypeLbl=コンテンツタイプ
#XFLD
autoMergeForTargetLbl=データを自動的にマージ
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=一般
#XFLD
lblBusinessName=ビジネス名
#XFLD
lblTechnicalName=技術名
#XFLD
lblPackage=パッケージ
#XFLD
statusPanel=実行ステータス
#XBTN: Schedule dropdown menu
SCHEDULE=スケジュール
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=スケジュールを編集
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=スケジュールを削除
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=スケジュールを作成
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=妥当性チェックのスケジュールに失敗しました
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=複製フローを現在デプロイ中であるため、スケジュールを作成できません。{0}複製フローがデプロイされるまでお待ちください。
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=ロードタイプが "初期およびデルタ" のオブジェクトが含まれる複製フローでは、スケジュールを作成できません。
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=ロードタイプが "初期およびデルタ/デルタのみ" のオブジェクトが含まれる複製フローでは、スケジュールを作成できません。
#XFLD : Scheduled popover
SCHEDULED=スケジュール済み
#XFLD
CREATE_REPLICATION_TEXT=複製フローを作成
#XFLD
EDIT_REPLICATION_TEXT=複製フローを編集
#XFLD
DELETE_REPLICATION_TEXT=複製フローを削除
#XFLD
REFRESH_FREQUENCY=頻度
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=既存のスケジュールで{0} "初期およびデルタ" ロードタイプがまだサポートされていないため、複製フローをデプロイできません。{0}{0}複製フローをデプロイするには、すべてのオブジェクトのロードタイプを{0} "初期のみ" に設定する必要があります。また、スケジュールを削除し、{0}複製フローをデプロイしてから新規実行を開始することができます。これにより、{0}終了なしで実行され、"初期およびデルタ" ロードタイプのオブジェクトもサポートされます。
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=既存のスケジュールで{0} "初期およびデルタ/デルタのみ" ロードタイプがまだサポートされていないため、複製フローをデプロイできません。{0}{0}複製フローをデプロイするには、すべてのオブジェクトのロードタイプを{0} "初期のみ" に設定する必要があります。また、スケジュールを削除し、{0}複製フローをデプロイしてから新規実行を開始することができます。これにより、{0}終了なしで実行され、"初期およびデルタ/デルタのみ" ロードタイプのオブジェクトもサポートされます。
#XMSG
SCHEDULE_EXCEPTION=スケジュール詳細の取得に失敗しました
#XFLD: Label for frequency column
everyLabel=間隔
#XFLD: Plural Recurrence text for Hour
hoursLabel=時間
#XFLD: Plural Recurrence text for Day
daysLabel=日
#XFLD: Plural Recurrence text for Month
monthsLabel=月
#XFLD: Plural Recurrence text for Minutes
minutesLabel=分
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=スケジュール可能かどうかに関する情報の取得に失敗しました。
#XFLD :Paused field
PAUSED=一時停止
#XMSG
navToMonitoring=フローモニタで開く
#XFLD
statusLbl=ステータス
#XFLD
lblLastRunExecuted=最終実行の開始
#XFLD
lblLastExecuted=前回実行
#XFLD: Status text for Completed
statusCompleted=完了
#XFLD: Status text for Running
statusRunning=実行中
#XFLD: Status text for Failed
statusFailed=失敗
#XFLD: Status text for Stopped
statusStopped=停止
#XFLD: Status text for Stopping
statusStopping=停止中
#XFLD: Status text for Active
statusActive=有効
#XFLD: Status text for Paused
statusPaused=一時停止
#XFLD: Status text for not executed
lblNotExecuted=未実行
#XFLD
messagesSettings=メッセージ設定
#XTOL
@validateModel=チェックメッセージ
#XTOL
@hierarchy=階層
#XTOL
@columnCount=列数
#XMSG
VAL_PACKAGE_CHANGED=このオブジェクトをパッケージ "{1}" に割り当てました。"保存" をクリックしてこの変更を確認および有効化してください。保存後はこのエディタでパッケージへの割り当てを元に戻すことができないので注意してください。
#XMSG
MISSING_DEPENDENCY=パッケージ "{1}" でオブジェクト "{0}" の依存関係を解決できません。
#XFLD
deltaLoadInterval=デルタロード間隔
#XFLD
lblHour=時間 (0-24)
#XFLD
lblMinutes=分 (0-59)
#XMSG
maxHourOrMinErr=0 から {0} の範囲の値を入力してください。
#XMSG
maxDeltaInterval=デルタロード間隔の最大値は 24 時間です。{0}分の値、または時間の値を適宜変更してください。
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=ターゲットコンテナパス
#XFLD
confluentSubjectName=件名
#XFLD
confluentSchemaVersion=スキーマバージョン
#XFLD
confluentIncludeTechKeyUpdated=技術キーを含める
#XFLD
confluentOmitNonExpandedArrays=未展開配列を省略
#XFLD
confluentExpandArrayOrMap=配列またはマップを展開
#XCOL
confluentOperationMapping=操作マッピング
#XCOL
confluentOpCode=操作コード
#XFLD
confluentInsertOpCode=挿入
#XFLD
confluentUpdateOpCode=更新
#XFLD
confluentDeleteOpCode=削除
#XFLD
expandArrayOrMapNotSelectedTxt=未選択
#XFLD
confluentSwitchTxtYes=はい
#XFLD
confluentSwitchTxtNo=いいえ
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=エラー
#XTIT
executeWarning=警告
#XMSG
executeunsavederror=実行前に複製フローを保存してください。
#XMSG
executemodifiederror=複製フローに保存されていない変更があります。複製フローを保存してください。
#XMSG
executeundeployederror=複製フローを実行するには、事前にデプロイしておく必要があります。
#XMSG
executedeployingerror=デプロイメントが終了するまでお待ちください。
#XMSG
msgRunStarted=実行が開始されました
#XMSG
msgExecuteFail=複製フローの実行が失敗しました。
#XMSG
titleExecuteBusy=お待ちください。
#XMSG
msgExecuteBusy=複製フローを実行するためにデータを準備しています。
#XTIT
executeConfirmDialog=警告
#XMSG
msgExecuteWithValidations=複製フローにチェックエラーがあります。複製フローを実行すると、エラーが発生する可能性があります。
#XMSG
msgRunDeployedVersion=デプロイ対象の変更があります。最後にデプロイされたバージョンの複製フローが実行されます。続行しますか?
#XBUT
btnExecuteAnyway=そのまま実行
#XBUT
btnExecuteClose=閉じる
#XBUT
loaderClose=閉じる
#XTIT
loaderTitle=ロードしています
#XMSG
loaderText=サーバから詳細をフェッチしています
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=今月利用可能な送信ボリュームがないため、
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=この非 SAP ターゲット接続に対する複製フローを開始できません。
#XMSG
premiumOutBoundRFAdminErrMsgPart1=管理者は、このテナントのプレミアム送信ブロックを増やして、
#XMSG
premiumOutBoundRFAdminErrMsgPart2=今月の送信ボリュームを利用できるようにすることができます。


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=エラー
#XTIT
deployInfo=情報
#XMSG
deployCheckFailException=デプロイメント中に例外が発生しました
#XMSG
deployGBQFFDisabled=ターゲット接続が Google BigQuery である複製フローのデプロイは現在使用できません。これは、この機能について保全活動が実施中であるためです。
#XMSG
deployKAFKAFFDisabled=ターゲット接続が Apache Kafka である複製フローのデプロイは現在使用できません。これは、この機能について保全活動が実施中であるためです。
#XMSG
deployConfluentDisabled=ターゲット接続が Confluent Kafka である複製フローのデプロイは現在使用できません。これは、この機能について保全活動が実施中であるためです。
#XMSG
deployInValidDWCTargetDeltaCaptureObject=ターゲットオブジェクト {0} について、そのデルタキャプチャテーブル名がリポジトリの他のテーブルによってすでに使用されています。複製フローをデプロイする前に、これらのターゲットオブジェクト名を変更して、関連付けられているデルタキャプチャテーブル名が一意であることを確認する必要があります。
#XMSG
deployDWCSourceFFDisabled=ソースとして SAP Datasphere がある複製フローのデプロイ現在使用できません。これは、この機能について保全活動が実施中であるためです。
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=ソースオブジェクトとしてデルタが有効化されたローカルテーブルを含む複製フローのデプロイは現在使用できません。これは、この機能について保全活動が実施中であるためです。
#XMSG
deployHDLFSourceFFDisabled=接続タイプが SAP HANA Cloud データレイクファイルのソース接続の複製フローのデプロイは現在使用できません。これは、保全活動が実施中であるためです。
#XMSG
deployObjectStoreAsSourceFFDisabled=クラウドストレージプロバイダをソースとして持つ複製フローをデプロイすることは現在できません。
#XMSG
deployConfluentSourceFFDisabled=ソースとして Confluent Kafka がある複製フローのデプロイは現在使用できません。これは、この機能について保全活動が実施中であるためです。
#XMSG
deployMaxDWCNewTableCrossed=大きな複製フローの場合、それを 1 回で "保存してデプロイ" することはできません。最初に複製フローを保存してからデプロイしてください。
#XMSG
deployInProgressInfo=デプロイメントがすでに進行中です。
#XMSG
deploySourceObjectInUse=ソースオブジェクト {0} は複製フロー {1} ですでに使用されています。
#XMSG
deployTargetSourceObjectInUse=ソースオブジェクト {0} は複製フロー {1} ですでに使用されています。ターゲットオブジェクト {2} は複製フロー {3} ですでに使用されています。
#XMSG
deployReplicationFlowCheckError=複製フローの確認中にエラーが発生しました: {0}
#XMSG
preDeployTargetObjectInUse=ターゲットオブジェクト {0} は複製フロー {1} ですでに使用されており、2 つの異なる複製フローに同じターゲットオブジェクトを含めることはできません。別のターゲットオブジェクトを選択し、もう一度実行してください。
#XMSG
runInProgressInfo=複製フローはすでに実行中です。
#XMSG
deploySignavioTargetFFDisabled=ターゲットとして SAP Signavio がある複製フローをデプロイすることは現在できません。これは、この機能について保全活動が実施中であるためです。
#XMSG
deployHanaViewAsSourceFFDisabled=選択したソース接続のソースオブジェクトとしてビューがある複製フローをデプロイすることは現在できません。後でもう一度実行してください。
#XMSG
deployMsOneLakeTargetFFDisabled=ターゲットとして MS OneLake がある複製フローをデプロイすることは現在できません。これは、この機能について保全活動が実施中であるためです。
#XMSG
deploySFTPTargetFFDisabled=ターゲットとして SFTP がある複製フローをデプロイすることは現在できません。これは、この機能について保全活動が実施中であるためです。
#XMSG
deploySFTPSourceFFDisabled=ソースとして SFTP がある複製フローをデプロイすることは現在できません。これは、この機能について保全活動が実施中であるためです。
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=技術名
#XFLD
businessNameInRenameTarget=ビジネス名
#XTOL
renametargetDialogTitle=ターゲットオブジェクト名を変更
#XBUT
targetRenameButton=名前変更
#XBUT
targetRenameCancel=キャンセル
#XMSG
mandatoryTargetName=名前を入力する必要があります。
#XMSG
dwcSpecialChar=許可されている特殊文字は、_ (アンダースコア) のみです。
#XMSG
dwcWithDot=ターゲットテーブル名は、ラテン文字、数字、アンダースコア (_)、およびピリオド (.) で構成できます。最初の文字は、(ピリオドではなく) 文字、数字、またはアンダースコアである必要があります。
#XMSG
nonDwcSpecialChar=許可されている特殊文字は、_ (アンダースコア) -(ハイフン) .(ドット) です。
#XMSG
firstUnderscorePattern=名前を _ (アンダースコア) で開始してはなりません

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: SQL の Create Table 文を表示
#XMSG
sqlDialogMaxPKWarning=Google BigQuery では、最大で 16 個の一次キーがサポートされていますが、ソースオブジェクトに 17 個以上の一次キーがあります。そのため、この文で一次キーは定義されません。
#XMSG
sqlDialogIncomptiblePKTypeWarning=1 つ以上のソース列に、Google BigQuery で一次キーとして定義できないデータ型があります。そのため、この場合、一次キーは定義されません。Google BigQuery では、BIGNUMERIC、BOOLEAN、DATE、DATETIME、INT64、NUMERIC、STRING、TIMESTAMP の各ターゲットデータ型のみ、一次キーを持つことができます。
#XBUT
copyAndCloseDDL=コピーして閉じる
#XBUT
closeDDL=閉じる
#XMSG
copiedToClipboard=クリップボードにコピーされました


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=オブジェクト ''{0}'' に終点がないため (ロードタイプが "初期およびデルタ/デルタのみ" であるオブジェクトが含まれているため)、このオブジェクトをタスクチェーンに含めることはできません。
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=オブジェクト ''{0}'' に終点がないため (ロードタイプが "初期およびデルタ" であるオブジェクトが含まれているため)、このオブジェクトをタスクチェーンに含めることはできません。
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=オブジェクト "{0}" をタスクチェーンに追加できません。


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=未保存のターゲットオブジェクトがあります。もう一度保存してください。{0}{0} この機能の動作が変更されました。以前は、ターゲットオブジェクトは複製フローをデプロイして初めてターゲット環境で作成されました。{0} 現在は、ターゲットオブジェクトは複製フローの保存時にすでに作成されています。複製フローはこの変更前に作成されており、新規オブジェクトが含まれています。{0} 新規オブジェクトが正しく含まれるようにするには、デプロイ前に複製フローをもう一度保存する必要があります。
#XMSG
confirmChangeContentTypeMessage=接続タイプを変更しようとしています。変更すると、既存のすべての射影が削除されます。

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=件名
#XFLD
schemaDialogVersionName=スキーマバージョン
#XFLD
includeTechKey=技術キーを含める
#XFLD
segementButtonFlat=フラット
#XFLD
segementButtonNested=ネスト
#XMSG
subjectNamePlaceholder=件名を検索

#XMSG
@EmailNotificationSuccess=実行時電子メール通知の設定が保存されます。

#XFLD
@RuntimeEmailNotification=実行時電子メール通知

#XBTN
@TXT_SAVE=保存


