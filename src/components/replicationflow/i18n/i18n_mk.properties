#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Репликациски тек

#XFLD: Edit Schema button text
editSchema=Уреди ја шемата

#XTIT : Properties heading
configSchema=Конфигурирај ја шемата

#XFLD: save changed button text
applyChanges=Примени ги промените


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Избери изворна врска
#XFLD
sourceContainernEmptyText=Избери контејнер
#XFLD
targetConnectionEmptyText=Избери целна врска
#XFLD
targetContainernEmptyText=Избери контејнер
#XFLD
sourceSelectObjectText=Избери изворен објект
#XFLD
sourceObjectCount=Изворни објекти ({0})
#XFLD
targetObjectText=Целни објекти
#XFLD
confluentBrowseContext=Избери контекст
#XBUT
@retry=Обиди се повторно
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Надградбата на закупецот е во тек.

#XTOL
browseSourceConnection=Прелистај изворна врска
#XTOL
browseTargetConnection=Прелистај целна врска
#XTOL
browseSourceContainer=Прелистај изворен контејнер
#XTOL
browseAndAddSourceDataset=Додај изворни објекти
#XTOL
browseTargetContainer=Прелистај целен контејнер
#XTOL
browseTargetSetting=Прелистај поставки за целта
#XTOL
browseSourceSetting=Прелистај поставки за изворот
#XTOL
sourceDatasetInfo=Информации
#XTOL
sourceDatasetRemove=Отстрани
#XTOL
mappingCount=Ова го претставува вкупниот број мапирања/изрази што не се засновани на назив.
#XTOL
filterCount=Ова го претставува вкупниот број услови на филтерот.
#XTOL
loading=Се вчитува...
#XCOL
deltaCapture=Делта-снимање
#XCOL
deltaCaptureTableName=Табела со делта-снимање
#XCOL
loadType=Тип вчитување
#XCOL
deleteAllBeforeLoading=Избриши ги сите пред вчитувањето
#XCOL
transformationsTab=Проекции
#XCOL
settingsTab=Поставки

#XBUT
renameTargetObjectBtn=Преименувај целен објект
#XBUT
mapToExistingTargetObjectBtn=Мапирај во постоен целен објект
#XBUT
changeContainerPathBtn=Промени патека на контејнер
#XBUT
viewSQLDDLUpdated=Прикажи SQL-наредба Создај табела 
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Изворниот објект не поддржува делта-снимање, но избраниот целен објект има овозможена опција за делта-снимање.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Целниот објект не може да се користи бидејќи е овозможено делта-снимање, {0}а изворниот објект не поддржува делта-снимање. {1}Може да изберете друг целен објект што не поддржува делта-снимање.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Целен објект со овој назив веќе постои. Сепак, не може да се користи {0}бидејќи делта-снимањето е овозможено, додека изворниот објект не поддржува {0}делта-снимање. {1}Може или да го внесете називот на постојниот целен објект што не поддржува {0}делта-снимање, или да внесете назив што сè уште не постои.
#XBUT
copySQLDDLUpdated=Копирај SQL-наредба Создај табела
#XMSG
targetObjExistingNoCDCColumnUpdated=Постојните табели во Google BigQuery мора да ги вклучуваат следниве колони за снимање податоци на промени (CDC): {0}{0}{1}Operation_flag {0}{1}is_deleted {0}{1}recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Следниве изворни објекти не се поддржани бидејќи немаат примарен клуч, или користат врска што не ги исполнува условите за враќање на примарниот клуч:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Побарајте решение во SAP KBA 3531135.
#XLST: load type list values
initial=Само почетно
@emailUpdateError=Грешка при ажурирањето на списокот со известувања по е-пошта

#XLST
initialDelta=Почетно и Делта

#XLST
deltaOnly=Само Делта
#XMSG
confluentDeltaLoadTypeInfo=За изворот на Confluent Kafka, поддржан е само типот вчитување „почетно и делта“.
#XMSG
confirmRemoveReplicationObject=Дали потврдувате дека сакате да ја избришете репликацијата?
#XMSG
confirmRemoveReplicationTaskPrompt=Ова дејство ќе ги избрише постојните репликации. Дали сакате да продолжите?
#XMSG
confirmTargetConnectionChangePrompt=Ова дејство ќе ја ресетира целната врска, целниот контејнер и ќе ги избрише сите целни објекти. Дали сакате да продолжите?
#XMSG
confirmTargetContainerChangePrompt=Ова дејство ќе го ресетира целниот контејнер и ќе ги избрише сите постојни целни објекти. Дали сакате да продолжите?
#XMSG
confirmRemoveTransformObject=Дали потврдувате дека сакате да ја избришете проекцијата {0}?
#XMSG
ErrorMsgContainerChange=Настана грешка при промената на патеката на контејнерот.
#XMSG
infoForUnsupportedDatasetNoKeys=Следниве изворни објекти не се поддржани бидејќи немаат примарен клуч:
#XMSG
infoForUnsupportedDatasetView=Следниве изворни објекти од типот Прикази не се поддржани:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Следниов изворен објект не е поддржан бидејќи е SQL-приказ што содржи параметри на внес:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Следниве изворни објекти не се поддржани бидејќи извлекувањето е оневозможено за нив:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=За врските на Confluent, единствените дозволени формати се AVRO и JSON. Следниве објекти не се поддржани бидејќи користат различен формат на серијализација:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Не може да се земе шемата за следниве објекти. Изберете го соодветниот контекст или верифицирајте ја конфигурацијата за регистрација на шемата
#XTOL: warning dialog header on deleting replication task
deleteHeader=Избриши
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Поставката „Избриши сè пред вчитување“ не е поддржана за Google BigQuery.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Поставката „Избриши сè пред“ го брише и повторно го создава објектот (темата) пред секоја репликација. Ова, исто така, ги брише сите доделени пораки.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Поставката „Избриши сè пред“ не е поддржана за цел од овој тип.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Технички назив
#XCOL
connBusinessName=Деловен назив
#XCOL
connDescriptionName=Опис
#XCOL
connType=Тип
#XMSG
connTblNoDataFoundtxt=Не се пронајдени врски
#XMSG
connectionError=Настана грешка при земањето на врските.
#XMSG
connectionCombinationUnsupportedErrorTitle=Комбинацијата на врските не е поддржана
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Репликацијата од {0} во {1} не е поддржана во моментов.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Комбинацијата на типот врска не е поддржана
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Репликацијата од врската со врската од тип SAP HANA Cloud, Датотеките на езерото со податоци до {0} не е поддржана. Можете да реплицирате само во SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Избери
#XBUT
containerCancelBtn=Откажи
#XTOL
containerSelectTooltip=Избери
#XTOL
containerCancelTooltip=Откажи
#XMSG
containerContainerPathPlcHold=Патека на контејнер
#XFLD
containerContainertxt=Контејнер
#XFLD
confluentContainerContainertxt=Контекст
#XMSG
infoMessageForSLTSelection=Само /SLT/ИД на масовен пренос е дозволен како контејнер. Изберете ИД на масовен пренос во SLT (ако е достапно) и кликнете Поднеси.
#XMSG
msgFetchContainerFail=Настана грешка при земањето податоци за контејнерот.
#XMSG
infoMessageForSLTHidden=Оваа врска не поддржува SLT-папки, така што тие не се појавуваат на списокот подолу.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Изберете контејнер што содржи потпапки.
#XMSG
sftpIncludeSubFolderText=Неточно
#XMSG
sftpIncludeSubFolderTextNew=Не

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Сè уште нема мапирање на филтер)
#XMSG
failToFetchRemoteMetadata=Настана грешка при земањето метаподатоци.
#XMSG
failToFetchData=Настана грешка при земањето на постојната цел.
#XCOL
@loadType=Тип вчитување
#XCOL
@deleteAllBeforeLoading=Избриши ги сите пред вчитувањето

#XMSG
@loading=Се вчитува...
#XFLD
@selectSourceObjects=Избери изворни објекти
#XMSG
@exceedLimit=Не можете да увезете повеќе од {0} објекти истовремено. Поништете го изборот на најмалку {1} објекти.
#XFLD
@objects=Објекти
#XBUT
@ok=Во ред
#XBUT
@cancel=Откажи
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Следно
#XBUT
btnAddSelection=Додај избор
#XTOL
@remoteFromSelection=Отстрани од избор
#XMSG
@searchInForSearchField=Пребарувај во {0}

#XCOL
@name=Технички назив
#XCOL
@type=Тип
#XCOL
@location=Локација
#XCOL
@label=Деловен назив
#XCOL
@status=Статус

#XFLD
@searchIn=Пребарувај во:
#XBUT
@available=Достапно
#XBUT
@selection=Избор

#XFLD
@noSourceSubFolder=Табели и прикази
#XMSG
@alreadyAdded=Веќе се наоѓа во дијаграмот
#XMSG
@askForFilter=Има повеќе од {0} ставки. Внесете низа на филтер за да го намалите бројот на ставки.
#XFLD: success label
lblSuccess=Успешно
#XFLD: ready label
lblReady=Подготвено
#XFLD: failure label
lblFailed=Неуспешно
#XFLD: fetching status label
lblFetchingDetail=Земање детали

#XMSG Place holder text for tree filter control
filterPlaceHolder=Внесете текст за да ги филтрирате објектите од највисоко ниво
#XMSG Place holder text for server search control
serverSearchPlaceholder=Внесете и притиснете Enter за да пребарате
#XMSG
@deployObjects=Увезување на {0} објекти...
#XMSG
@deployObjectsStatus=Број на предмети што се увезени: {0}. Број на предмети што не може да се увезат: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Отворањето на локалниот прелистувач на складот е неуспешно.
#XMSG
@openRemoteSourceBrowserError=Земањето изворни објекти не успеа.
#XMSG
@openRemoteTargetBrowserError=Земањето целни објекти успеа.
#XMSG
@validatingTargetsError=Настана грешка при потврдувањето на целта.
#XMSG
@waitingToImport=Подготвенo за увоз

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Максималниот број објекти е надминат. Изберете максимум 500 објекти за еден репликациски тек.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Технички назив
#XFLD
sourceObjectBusinessName=Деловен назив
#XFLD
sourceNoColumns=Број колони
#XFLD
containerLbl=Контејнер

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Мора да изберете изворна врска за репликацискиот тек.
#XMSG
validationSourceContainerNonExist=Мора да изберете контејнер за изворната врска.
#XMSG
validationTargetNonExist=Мора да изберете целна врска за репликацискиот тек.
#XMSG
validationTargetContainerNonExist=Мора да изберете контејнер за целната врска.
#XMSG
validationTruncateDisabledForObjectTitle=Репликација на објекти во складишта.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Репликацијата во складиште на облак е можна само ако е поставена опцијата „Избриши сè пред вчитување“ или ако целниот објект не постои во целта.{0}{0} За да можете сè уште да овозможите репликација за објекти за коишто не е поставена опцијата „Избриши сè пред вчитување“, проверете дали целниот објект постои во системот пред да го извршите репликацискиот тек.
#XMSG
validationTaskNonExist=Мора да имате барем една репликација во репликацискиот тек.
#XMSG
validationTaskTargetMissing=Мора да имате цел за репликација со изворот: {0}
#XMSG
validationTaskTargetIsSAC=Избраната цел е SAC-артефакт: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Избраната цел не е поддржана локална табела: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Веќе постои објект со овој назив во целта. Сепак, овој објект не може да се користи како целен објект за репликацискиот тек до локалниот репозиториум бидејќи не е локална табела.
#XMSG
validateSourceTargetSystemDifference=Мора да изберете различна изворна и целна врска, и комбинации на контејнери за репликацискиот тек.
#XMSG
validateDuplicateSources=една или повеќе репликации имаат дупликат називи на изворни објекти: {0}.
#XMSG
validateDuplicateTargets=една или повеќе репликации имаат дупликат називи на целни објекти: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=Изворниот објект {0} не поддржува делта-снимање, додека целниот објект {1} го поддржува. Мора да ја отстраните репликацијата.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Мора да го изберете типот вчитување „Почетно и Делта“ за репликацијата со назив {0} на целниот објект.
#XMSG
validationAutoRenameTarget=Целните колони се преименувани.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Додадена е автоматска проекција, а следниве целни колони се преименувани за да се дозволат репликации во целта:{1}{1} {0} {1}{1}Тоа се должи на една од следниве причини:{1}{1}{2} неподдржани знаци{1}{2} резервиран префикс
#XMSG
validationAutoRenameTargetDescriptionUpdated=Додадена е автоматска проекција, а следниве целни колони се преименувани за да се дозволи репликација на Google BigQuery: {1}{1}{0}{1}{1}Тоа се должи на една од следниве причини: {1}{1}{2} називот на колона е резервиран{1}{2} неподдржани знаци{1}{2} резервиран префикс
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Додадена е автоматска проекција, а следниве целни колони се преименувани за да се дозволат репликации на Confluent: {1}{1}{0}{1}{1}Тоа се должи на една од следниве причини: {1}{1}{2} називот на колона е резервиран{1}{2} неподдржани знаци{1}{2} резервиран префикс
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Додадена е автоматска проекција, а следниве целни колони се преименувани за да се дозволат репликации на целта: {1}{1}{0}{1}{1}Тоа се должи на една од следниве причини: {1}{1}{2} називот на колона е резервиран{1}{2} неподдржани знаци{1}{2} резервиран префикс
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Целниот објект е преименуван.
#XMSG
autoRenameInfoDesc=Целниот објект е преименуван бидејќи содржи неподдржани знаци. Поддржани се само следниве знаци:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(точка){0}{1}_(долна црта){0}{1}-(цртичка)
#XMSG
validationAutoTargetTypeConversion=Целните типови податоци се променети.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=За следниве целни колони, целните типови податоци се променети бидејќи во Google BigQuery изворните типови податоци не се поддржани:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=За следниве целни колони, целните типови податоци се променети бидејќи изворните типови податоци не се поддржани во целната врска: {1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Скратете ги називите на целните колони.
#XMSG
validationMaxCharLengthGBQTargetDescription=Во Google BigQuery, називите на колоните може да користат најмногу 300 знаци. Употребете ја проекцијата за да ги скратите следниве називи на целни колони:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Примарните клучеви нема да бидат создадени.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=Во Google BigQuery се поддржани најмногу 16 примарни клучеви, но изворниот објект има поголем број на примарни клучеви. Ниту еден од примарните клучеви нема да биде создаден во целниот објект.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Една или повеќе изворни колони имаат типови податоци што не може да се дефинираат како примарни клучеви во Google BigQuery. Ниту еден од примарните клучеви нема да се создаде во целниот објект. {0}{0}Следниве целни типови податоци се компатибилни со типовите на податоци на Google BigQuery за кои може да се дефинира примарен клуч: {0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Дефинирајте една или повеќе колони како примарен клуч.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Мора да дефинирате една или повеќе колони како примарен клуч. За тоа користете дијалог на изворна шема.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Дефинирајте една или повеќе колони како примарен клуч.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Мора да дефинирате една или повеќе колони како примарен клуч што се совпаѓа со ограничувањата за примарен клуч за изворниот објект. Одете во Конфигурирање на шемата во својствата на изворниот објект за да го сторите тоа.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Внесете максимална вредност на партиција.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Макс. вредност на партицијата мора да биде  ≥ 1 и  ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Дефинирајте една или повеќе колони како примарен клуч.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=За да реплицирате објект, мора да дефинирате една или повеќе целни колони како примарен клуч. Користете проекција за да го направите ова.
#XMSG
validateHDLFNoPKExistingDatasetError=Дефинирајте една или повеќе колони како примарен клуч.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=За да реплицирате податоци во постоен целен објект, тој мора да има една или повеќе колони кои ќе бидат дефинирани како примарен клуч. {0} Ги имате следниве опции за дефинирање на една или повеќе колони како примарен клуч: {0} {1} Користете го локалниот уредувач на табели за да го промените постојниот целен објект. Потоа, повторно вчитајте го репликацискиот тек.{0}{1} Преименувајте го целниот објект во репликацискиот тек. Така ќе се создаде нов објект веднаш штом ќе започне извршувањето. Откако ќе го преименувате, може да дефинирате една или повеќе колони како примарен клуч во проекцијата.{0}{1} Мапирајте го објектот со друг постоен целен објект во кој една или повеќе колони се веќе дефинирани како примарен клуч.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Избраната цел веќе постои во репозиториумот: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Називите на табелите за делта-снимање веќе се користат од други табели во репозиториумот: {0}мора да ги преименувате целниве објекти за да се уверите дека поврзаните имиња на табелите за делта-снимање се уникатни пред да можете да го зачувате репликацискиот тек.
#XMSG
validateConfluentEmptySchema=Дефинирај шема
#XMSG
validateConfluentEmptySchemaDescUpdated=Изворната табела нема шема. Изберете Конфигурирај шема за да дефинирате една
#XMSG
validationCSVEncoding=Неважечко CSV-шифрирање
#XMSG
validationCSVEncodingDescription=CSV-шифрирањето на задачата е неважечко.
#XMSG
validateConfluentEmptySchema=Изберете компатибилен тип целни податоци
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Изберете компатибилен тип целни податоци
#XMSG
globalValidateTargetDataTypeDesc=Настана грешка во мапирањата на колоната. Одете во Проектирање и уверете се дека сите изворни колони се мапирани со уникатна колона, со колона која има компатибилен тип податоци, и дека сите дефинирани изрази се важечки.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Дуплирани називи на колони.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Дуплираните називи на колоните не се поддржани. Користете го дијалогот за проекција за да ги поправите. Следниве целни објекти имаат дуплирани називи на колони: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Дуплирани називи на колони.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Дуплираните називи на колоните не се поддржани. Следниве целни објекти имаа дуплирани називи на колони: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Можно е да има недоследности во податоците.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Типот вчитување Само Делта нема да ги земе предвид промените во изворот направени меѓу последното зачувување и следното извршување.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Променете го типот вчитување во „Почетно“.
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Реплицирањето на објектите засновани на ABAP коишто немаат примарен клуч е можно само за типот вчитување „Само почетно“.
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Оневозможи делта-снимање.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=За да реплицирате објект што нема примарен клуч користејќи изворна врска од типот ABAP, прво мора да го оневозможите делта-снимањето за оваа табела.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Променете го целниот објект.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Целниот објект не може да се искористи бидејќи делта-снимањето е овозможено. Можете или да го преименувате целниот објект па да го исклучите делта-снимањето за новиот (преименуван) објект или да го мапирате изворниот објект со целен објект со оневозможено делта-снимање.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Променете го целниот објект.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Целниот објект не може да се користи бидејќи ја нема потребната техничка колона __load_package_id. Можете да го преименувате целниот објект користејќи име што сè уште не постои. Системот потоа создава нов објект кој ја има истата дефиниција како изворниот објект и ја содржи техничката колона. Алтернативно, може да го мапирате целниот објект со постоен објект што ја има потребната техничка колона (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Променете го целниот објект.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Целниот објект не може да се користи бидејќи ја нема потребната техничка колона __load_record_id. Можете да го преименувате целниот објект користејќи име што сè уште не постои. Системот потоа создава нов објект кој ја има истата дефиниција како изворниот објект и ја содржи техничката колона. Алтернативно, може да го мапирате целниот објект со постоен објект што ја има потребната техничка колона (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Променете го целниот објект.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Целниот објект не може да се користи бидејќи типот податоци на неговата техничка колона __load_record_id не е „string(44)“. Можете да го преименувате целниот објект користејќи име што сè уште не постои. Системот потоа создава нов објект кој ја има истата дефиниција и точниот тип податоци како резултат на тоа. Алтернативно, може да го мапирате целниот објект со постоен објект што ја има потребната техничка колона (__load_record_id) со точниот тип податоци.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Променете го целниот објект.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Целниот објект не може да се користи бидејќи има примарен клуч, а изворниот објект нема. Можете да го преименувате целниот објект користејќи име што сè уште не постои. Системот потоа создава нов објект кој ја има истата дефиниција како изворниот објект, но нема примарен клуч како резултат на тоа. Алтернативно, може да го мапирате целниот објект со постоен објект што ја има потребната техничка колона (__load_package_id) без примарен клуч.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Променете го целниот објект.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Целниот објект не може да се користи бидејќи има примарен клуч, а изворниот објект нема. Можете да го преименувате целниот објект користејќи име што сè уште не постои. Системот потоа создава нов објект кој ја има истата дефиниција како изворниот објект, но нема примарен клуч како резултат на тоа. Алтернативно, може да го мапирате целниот објект со постоен објект што ја има потребната техничка колона (__load_record_id) без примарен клуч.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Променете го целниот објект.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Целниот објект не може да се користи бидејќи типот податоци на неговата техничка колона __load_package_id не е „binary(>=256)“. Може да го преименувате целниот објект користејќи име коешто сè уште не постои. Системот потоа создава нов објект што ја има истата дефиниција како изворниот објект и точниот тип податоци како резултат на тоа. Алтернативно, може да го мапирате целниот објект со постоен објект што ја има потребната техничка колона (__load_package_id) со точниот тип податоци.
#XMSG
validationAutoRenameTargetDPID=Целните колони се преименувани.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Отстрани го изворниот објект.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Изворниот објект нема колона со клуч, што не е поддржано во овој контекст.
#XMSG
validationAutoRenameTargetDPIDDescription=Додадена е автоматска проекција, а следниве целни колони се преименувани за да се дозволи репликација од изворот на ABAP без клучеви: {1}{1}{0}{1}{1}Тоа се должи на една од следниве причини: {1}{1}{2} називот на колона е резервиран{1}{2} неподдржани знаци{1}{2} резервиран префикс
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Репликација во {{0}}
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Зачувувањето и примената на репликациските текови што имаат {0} како цел не е можно во моментов бидејќи вршиме одржување на функцијава.
#XMSG
TargetColumnSkippedLTF=Целната колона е прескокната.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Целната колона е прескокната поради неподдржан тип податоци. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Колона за време како примарен клуч.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Изворниот објект има колона за време како примарен клуч, што не е поддржано во овој контекст.
#XMSG
validateNoPKInLTFTarget=Недостига примарен клуч.
#XMSG
validateNoPKInLTFTargetDescription=Примарниот клуч не е дефиниран во целта, што не е поддржано во овој контекст.
#XMSG
validateABAPClusterTableLTF=Табела со кластери во ABAP
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Изворниот објект е табела со кластери во ABAP, а тоа не е поддржано во овој контекст.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Изгледа дека сè уште не сте додале податоци.
#YINS
welcomeText2=За да го започнете репликацискиот тек, изберете врска и изворен објект на левата страна.

#XBUT
wizStep1=Избери изворна врска
#XBUT
wizStep2=Избери изворен контејнер
#XBUT
wizStep3=Додај изворни објекти

#XMSG
limitDataset=Достигнат е максималниот број објекти. Отстранете ги постојните објекти за да додадете нови или создадете нов репликациски тек.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Репликацискиот тек за целнава врска што не е од SAP не може да се стартува бидејќи излезниот волумен не е достапен за овој месец.
#XMSG
premiumOutBoundRFAdminWarningMsg=Администраторот може да ги зголеми блоковите Premium Outbound за овој закупец, со што излезниот волумен за овој месец ќе стане достапен.
#XMSG
messageForToastForDPIDColumn2=Додадена е нова колона во целта за {0} објекти – потребна е за управување со дуплираните записи во врска со изворните објекти засновани на ABAP кои немаат примарен клуч.
#XMSG
PremiumInboundWarningMessage=Во зависност од бројот на репликациски текови и обемот на податоци што треба да се реплицираат, ресурсите од SAP HANA {0} потребни за реплицирање на податоците преку {1} можат да го надминат достапниот капацитет за вашиот закупец.
#XMSG
PremiumInboundWarningMsg=Во зависност од бројот на репликациски текови и обемот на податоци што треба да се реплицираат,{0}ресурсите од SAP HANA потребни за реплицирање на податоците преку „{1}“ можат да го надминат достапниот капацитет за вашиот закупец.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Внесете назив на проекција.
#XMSG
emptyTargetColumn=Внесете назив на целна колона.
#XMSG
emptyTargetColumnBusinessName=Внесете деловен назив на целната колона.
#XMSG
invalidTransformName=Внесете назив на проекција.
#XMSG
uniqueColumnName=Преименувајте ја целната колона.
#XMSG
copySourceColumnLbl=Копирајте колони од изворен објект
#XMSG
renameWarning=Задолжително изберете единствено име при преименувањето на целната табела. Ако табелата со новото име веќе постои во просторот, ќе ја користи дефиницијата од таа табела.

#XMSG
uniqueColumnBusinessName=Сменете го деловниот назив на целната колона.
#XMSG
uniqueSourceMapping=Изберете друга изворна колона.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=Изворната колона {0} веќе се користи од страна на следниве целни колони:{1}{1}{2}{1}{1} За оваа целна колона или за другите целни колони, изберете изворна колона што сѐ уште не се користи за да ја зачувате проекцијата.
#XMSG
uniqueColumnNameDescription=Називот на целната колона што го внесовте веќе постои. За да можете да ја зачувате проекцијата, треба да внесете единствен назив на колона.
#XMSG
uniqueColumnBusinessNameDesc=Деловниот назив на целната колона веќе постои. За да ја зачувате проекцијата, треба да внесете единствен деловен назив на колоната.
#XMSG
emptySource=Изберете изворна колона или внесете константа.
#XMSG
emptySourceDescription=За да создадете важечки запис за мапирање, треба да изберете изворна колона или да внесете константна вредност.
#XMSG
emptyExpression=Дефинирајте мапирање.
#XMSG
emptyExpressionDescription1=Изберете ја изворната колона на која сакате да ја мапирате целната колона или изберете го полето за означување во колоната {0} Функции/Константи {1}. {2}{2} Функциите се внесуваат автоматски според типот на целни податоци. Постојните вредности може да се внесат рачно.
#XMSG
numberExpressionErr=Внесете број.
#XMSG
numberExpressionErrDescription=Избравте нумерички тип податоци. Ова значи дека можете да внесувате само бројки, плус децимална точка ако е применливо. Не користете единечни наводници.
#XMSG
invalidLength=Внесете важечка вредност за должина.
#XMSG
invalidLengthDescription=Должината на типот податоци мора да биде еднаква или поголема од должината на изворната колона и може да биде меѓу 1 и 5000.
#XMSG
invalidMappedLength=Внесете важечка вредност за должина.
#XMSG
invalidMappedLengthDescription=Должината на типот податоци мора да биде еднаква или поголема од должината на изворната колона {0} и може да биде меѓу 1 и 5000.
#XMSG
invalidPrecision=Внесете важечка вредност за прецизност.
#XMSG
invalidPrecisionDescription=Прецизноста го дефинира вкупниот број на цифри. Скалата го дефинира бројот на цифри пo децималната точка и може да биде меѓу 0 и прецизноста. {0}{0}Примери: {0}{1} прецизност 6, скала 2 одговара на броеви како што се 1234,56. {0}{1} Прецизност 6, скала 6 одговара на броеви како што се 0,123546. {0}{0} Прецизноста и скалата за целта мора да бидат компатибилни со прецизноста и скалата за изворот, така што сите цифри од изворот влегуваат во целното поле. На пример, ако имате прецизност 6 и скала 2 во изворот (и следствено на тоа, цифри различни од 0 пред децималната точка), не може да имате прецизност 6 и скала 6 во целта.
#XMSG
invalidPrimaryKey=Внесете најмалку еден примарен клуч.
#XMSG
invalidPrimaryKeyDescription=Не е дефиниран примарниот клуч за оваа шема.
#XMSG
invalidMappedPrecision=Внесете важечка вредност за прецизност.
#XMSG
invalidMappedPrecisionDescription1=Прецизноста го дефинира вкупниот број на цифри. Скалата го дефинира бројот на цифри по децималната точка и може да биде меѓу 0 и прецизноста. {0}{0}Примери: {0}{1} прецизност 6, скала 2 одговара на броеви како што се 1234,56. {0}{1} Прецизност 6, скала 6 одговара на броеви како што се 0,123546. {0}{0} Прецизноста на типот податоци мора да биде еднаква или поголема од прецизноста на изворот ({2}).
#XMSG
invalidScale=Внесете важечка вредност за скала. 
#XMSG
invalidScaleDescription=Прецизноста го дефинира вкупниот број на цифри. Скалата го дефинира бројот на цифри пo децималната точка и може да биде меѓу 0 и прецизноста. {0}{0}Примери: {0}{1} прецизност 6, скала 2 одговара на броеви како што се 1234,56. {0}{1} Прецизност 6, скала 6 одговара на броеви како што се 0,123546. {0}{0} Прецизноста и скалата за целта мора да бидат компатибилни со прецизноста и скалата за изворот, така што сите цифри од изворот влегуваат во целното поле. На пример, ако имате прецизност 6 и скала 2 во изворот (и следствено на тоа, цифри различни од 0 пред децималната точка), не може да имате прецизност 6 и скала 6 во целта.
#XMSG
invalidMappedScale=Внесете важечка вредност за скала. 
#XMSG
invalidMappedScaleDescription1=Прецизноста го дефинира вкупниот број на цифри. Скалата го дефинира бројот на цифри по децималната точка и може да биде меѓу 0 и прецизноста. {0}{0}Примери: {0}{1} прецизност 6, скала 2 одговара на броеви како што се 1234,56. {0}{1} Прецизност 6, скала 6 одговара на броеви како што се 0,123546. {0}{0} Скалата на типот податоци мора да биде еднаква или поголема од скалата на изворот ({2}).
#XMSG
nonCompatibleDataType=Изберете компатибилен тип целни податоци.
#XMSG
nonCompatibleDataTypeDescription1=Типот податоци што ќе го наведете овде мора да биде компатибилен со изворниот тип податоци ({0}). {1}{1} Пример: ако вашата изворна колона има тип податоци Низа и содржи букви, не може да користите децимален тип податоци за целта.
#XMSG
invalidColumnCount=Изберете изворна колона.
#XMSG
ObjectStoreInvalidScaleORPrecision=Внесете важечка вредност за прецизност и скала.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=Првата вредност е прецизност, која го дефинира вкупниот број цифри. Втората вредност е скалата, која ги дефинира цифрите по децималната точка. Внесете целна вредност на скалата што е поголема од изворната вредност на скалата и уверете се дека разликата помеѓу внесената целна вредност на скалата и  вредноста на прецизноста е поголема од разликата помеѓу изворната вредност на скалата и вредноста на прецизноста.
#XMSG
InvalidPrecisionORScale=Внесете важечка вредност за прецизност и скала.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=Првата вредност е прецизноста, која го дефинира вкупниот број цифри. Втората вредност е скалата, која ги дефинира цифрите по децималната запирка.{0}{0}Бидејќи изворниот тип податоци не е поддржан во Google BigQuery, тој се претвора во целен тип податоци DECIMAL. Во овој случај, прецизноста може да се дефинира само меѓу 38 и 76, а скалата меѓу 9 и 38. Освен тоа, резултатот од прецизноста на минус скалата, којашто ги претставува цифрите пред децималната точка, мора да биде меѓу 29 и 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=Првата вредност е прецизноста, која го дефинира вкупниот број цифри. Втората вредност е скалата, која ги дефинира цифрите по децималната запирка.{0}{0}Бидејќи изворниот тип податоци не е поддржан во Google BigQuery, тој се претвора во целниот тип на податоци DECIMAL. Во овој случај, прецизноста мора да се дефинира како 20 или поголема. Покрај тоа, резултатот од прецизноста на минус скалата, којашто ги одразува цифрите пред децималната точка, мора да биде 20 или повеќе.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=Првата вредност е прецизноста, која го дефинира вкупниот број цифри. Втората вредност е скалата, која ги дефинира цифрите по децималната запирка.{0}{0}Бидејќи изворниот тип податоци не е поддржан во целта, тој се претвора во целниот тип податоци DECIMAL. Во овој случај, прецизноста мора да се дефинира како 20 или поголема. Во овој случај, прецизноста мора да се дефинира со кој било број поголем од или еднаков на 1 и помал од или еднаков на 38, а скалата помала од или еднаква на прецизноста.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=Првата вредност е прецизноста, која го дефинира вкупниот број цифри. Втората вредност е скалата, која ги дефинира цифрите по децималната запирка.{0}{0}Бидејќи изворниот тип податоци не е поддржан во целта, тој се претвора во целниот тип на податоци DECIMAL. Во овој случај, прецизноста мора да се дефинира како 20 или поголема. Покрај тоа, резултатот од прецизноста на минус скалата, којашто ги одразува цифрите пред децималната точка, мора да биде 20 или повеќе.
#XMSG
invalidColumnCountDescription=За да создадете важечки запис за мапирање, треба да изберете изворна колона или да внесете константна вредност.
#XMSG
duplicateColumns=Преименувајте ја целната колона.
#XMSG
duplicateGBQCDCColumnsDesc=Називот на целната колона е резервиран во Google BigQuery. Треба да го преименувате за да можете да ја зачувате проекцијата.
#XMSG
duplicateConfluentCDCColumnsDesc=Називот на целната колона е резервиран во Confluent. Треба да го преименувате за да можете да ја зачувате проекцијата.
#XMSG
duplicateSignavioCDCColumnsDesc=Називот на целната колона е резервиран во SAP Signavio. Треба да го преименувате за да можете да ја зачувате проекцијата.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Називот на целната колона е резервиран во MS OneLake. Треба да го преименувате за да можете да ја зачувате проекцијата.
#XMSG
duplicateSFTPCDCColumnsDesc=Називот на целната колона е резервиран во SFTP. Треба да го преименувате за да можете да ја зачувате проекцијата.
#XMSG
GBQTargetNameWithPrefixUpdated1=Називот на целната колона содржи префикс што е резервиран во Google BigQuery. Треба да го преименувате за да можете да ја зачувате проекцијата. {0}{0}Називот на целната колона не може да започне со која било од следниве низи: {0}{0}{1}{2}{3}{2}{3}{2}_TABLE__FILE__PARTITION {3}{2}_ROW_TIMESTAMP {3}{2}__ROOT__ {3}{2}_COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Скратете го називот на целните колони.
#XMSG
GBQtargetMaxLengthDesc=Во Google BigQuery, називот на колоната може да користи најмногу 300 знаци. Скратете го називот на целната колона за да можете да ја зачувате проекцијата.
#XMSG
invalidMappedScalePrecision=Прецизноста и скалата за целта мора да бидат компатибилни со прецизноста и скалата за изворот, така што сите цифри од изворот влегуваат во целното поле.
#XMSG
invalidMappedScalePrecisionShortText=Внесете важечка вредност за прецизност и скала. 
#XMSG
validationIncompatiblePKTypeDescProjection3=Една или повеќе изворни колони имаат типови податоци што не можат да се дефинираат како примарни клучеви во Google BigQuery. Ниту еден од примарните клучеви нема да се создаде во целниот објект. {0}{0}Следниве целни типови податоци се компатибилни со типовите на податоци на Google BigQuery за кои може да се дефинира примарен клуч: {1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Отштиклирајте ја колоната __message_id.
#XMSG
uncheckColumnMessageIdDesc=Колона: примарен клуч
#XMSG
validationOpCodeInsert=Мора да внесете вредност за внесот.
#XMSG
recommendDifferentPrimaryKey=Ви препорачуваме да изберете различен примарен клуч на ниво на ставка.
#XMSG
recommendDifferentPrimaryKeyDesc=Кога веќе ќе биде дефиниран оперативниот код, се препорачува да изберете различни примарни клучеви за индексот на низа и за ставките за да избегнете проблеми како што е на пример удвојување на колоната.
#XMSG
selectPrimaryKeyItemLevel=Мора да изберете најмалку еден примарен клуч и за заглавјето и за нивото на ставката.
#XMSG
selectPrimaryKeyItemLevelDesc=Кога ќе се рашири низа или мапа, мора да изберете два примарни клуча, еден на ниво на заглавје, друг на ниво на ставка.
#XMSG
invalidMapKey=Мора да изберете најмалку еден примарен клуч на нивото на заглавјето.
#XMSG
invalidMapKeyDesc=Кога ќе се рашири низа или мапа, мора да изберете примарен клуч на ниво на заглавјето.
#XFLD
txtSearchFields=Пребарајте целни колони
#XFLD
txtName=Назив
#XMSG
txtSourceColValidation=Една или повеќе изворни колони не се поддржани:
#XMSG
txtMappingCount=Mапирања ({0}) 
#XMSG
schema=Шема
#XMSG
sourceColumn=Изворни колони
#XMSG
warningSourceSchema=Секоја промена на шемата ќе влијае врз мапирањата во дијалогот за проекција.
#XCOL
txtTargetColName=Целна колона (технички назив)
#XCOL
txtDataType=Целен тип податоци
#XCOL
txtSourceDataType=Тип изворни податоци
#XCOL
srcColName=Изворна колона (технички назив)
#XCOL
precision=Прецизност
#XCOL
scale=Скала
#XCOL
functionsOrConstants=Функции/Константи
#XCOL
txtTargetColBusinessName=Целна колона (деловен назив)
#XCOL
prKey=Примарен клуч
#XCOL
txtProperties=Својства
#XBUT
txtOK=Зачувај
#XBUT
txtCancel=Откажи
#XBUT
txtRemove=Отстрани
#XFLD
txtDesc=Опис
#XMSG
rftdMapping=Мапирање
#XFLD
@lblColumnDataType=Тип податоци
#XFLD
@lblColumnTechnicalName=Технички назив
#XBUT
txtAutomap=Мапирај автоматски
#XBUT
txtUp=Нагоре
#XBUT
txtDown=Надолу

#XTOL
txtTransformationHeader=Проекција
#XTOL
editTransformation=Уреди
#XTOL
primaryKeyToolip=Клуч


#XMSG
rftdFilter=Филтер
#XMSG
rftdFilterColumnCount=Извор: {0}({1})
#XTOL
rftdFilterColSearch=Пребарај
#XMSG
rftdFilterColNoData=Нема колони за прикажување
#XMSG
rftdFilteredColNoExps=Нема изрази на филтер
#XMSG
rftdFilterSelectedColTxt=Додај филтер за
#XMSG
rftdFilterTxt=Достапен е филтер за
#XBUT
rftdFilterSelectedAddColExp=Додај израз
#YINS
rftdFilterNoSelectedCol=Изберете колона за да додадете филтер.
#XMSG
rftdFilterExp=Израз на филтер
#XMSG
rftdFilterNotAllowedColumn=Додавањето филтри не е поддржано за оваа колона.
#XMSG
rftdFilterNotAllowedHead=Колоната не е поддржана. 
#XMSG
rftdFilterNoExp=Не е дефиниран ниту еден филтер. 
#XTOL
rftdfilteredTt=Филтрирано
#XTOL
rftdremoveexpTt=Отстрани го изразот на филтер
#XTOL
validationMessageTt=Пораки за потврдување
#XTOL
rftdFilterDateInp=Изберете датум
#XTOL
rftdFilterDateTimeInp=Изберете датум и време
#XTOL
rftdFilterTimeInp=Изберете време
#XTOL
rftdFilterInp=Внеси вредност
#XMSG
rftdFilterValidateEmptyMsg={0}изразите на филтер во колоната {1} се празни
#XMSG
rftdFilterValidateInvalidNumericMsg={0}изразите на филтер во колоната {1} содржат неважечки нумерички вредности
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Изразот на филтерот мора да содржи важечки нумерички вредности
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Ако шемата на целниот објект е променета, користете ја функцијата „Мапирај до постоен целен објект“ на главната страница за да ги приспособите промените и повторно мапирајте го целниот објект со изворот.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Ако целната табела веќе постои и мапирањето вклучува промена на шемата, мора да ја промените целната табела соодветно пред да го примените репликацискиот тек.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Ако мапирањето вклучува промена на шемата, мора да ја промените целната табела соодветно пред да го примените репликацискиот тек.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Следниве неподдржани колони се прескокнати од дефиницијата на изворот: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Следниве неподдржани колони се прескокнати од дефиницијата на целта: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Следниве објекти не се поддржани затоа што се изложени за потрошувачка: {0} {1} {0} {0} За да ги користите табелите во репликациски тек, семантичката употреба (во поставките на табелата) не смее да биде поставена на {2}Аналитичка група податоци{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Целниот објект не може да се користи затоа што е изложен за потрошувачка. {0} {0} За да ја користите табелата во репликациски тек, семантичката употреба (во поставките на табелата) не смее да биде поставена на {1}Аналитичка група податоци{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Веќе постои целен објект со овој назив. Сепак, не може да се користи затоа што е изложен за потрошувачка. {0} {0} За да ја користите табелата во репликациски тек, семантичката употреба (во поставките на табелата) не смее да биде поставена на {1}Аналитичка група податоци{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Веќе постои објект со овој назив во целта. {0}Сепак, овој објект не може да се користи како целен објект за репликацискиот тек до локалниот репозиториум бидејќи не е локална табела.
#XMSG:
targetAutoRenameUpdated=Целната колона е преименувана.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Целната колона е преименувана за да дозволи репликации во Google BigQuery. Ова се должи на една од следниве причини: {0}{1}{2}називот на колоната е резервиран{3} {2}неподдржани знаци{3}{2}Префиксот е резервиран{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Целната колона е преименувана за да се дозволат репликации во Confluent. Ова се должи на една од следниве причини: {0}{1}{2}називот на колоната е резервиран{3} {2}неподдржани знаци{3}{2}префиксот е резервиран{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Целната колона е преименувана за да се дозволат репликации во целта. Ова е поради една од следниве причини: {0}{1}{2}Неподдржани знаци{3} {2}Резервиран префикс{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Целната колона е преименувана за да се дозволат репликации во целта. Ова е поради една од следниве причини: {0}{1}{2}Називот на колоната е резервиран{3}{2}Неподдржани знаци{3}{2}Резервиран префикс{3}{4}
#XMSG:
targetAutoDataType=Целниот тип податоци е променет.
#XMSG:
targetAutoDataTypeDesc=Целниот тип податоци е променет во {0} бидејќи изворниот тип податоци не е поддржан во Google BigQuery.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Целниот тип податоци е променет во {0} бидејќи изворниот тип податоци не е поддржан во целната врска.
#XMSG
projectionGBQUnableToCreateKey=Примарните клучеви нема да бидат создадени.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=Во Google BigQuery се поддржани најмногу 16 примарни клучеви, но изворниот објект има поголем број на примарни клучеви. Ниту еден од примарните клучеви нема да биде создаден во целниот објект.
#XMSG
HDLFNoKeyError=Дефинирајте една или повеќе колони како примарен клуч.
#XMSG
HDLFNoKeyErrorDescription=За да реплицирате објект, мора да дефинирате една или повеќе колони како примарен клуч.
#XMSG
HDLFNoKeyErrorExistingTarget=Дефинирајте една или повеќе колони како примарен клуч.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=За да реплицирате податоци во постоен целен објект, тој мора да има една или повеќе колони кои ќе бидат дефинирани како примарен клуч. {0} {0} Ги имате следниве опции за дефинирање на една или повеќе колони како примарен клуч: {0}{0}{1} Користете го локалниот уредувач на табели за да го промените постојниот целен објект. Потоа, повторно вчитајте го репликацискиот тек.{0}{0}{1} Преименувајте го целниот објект во репликацискиот тек. Така ќе се создаде нов објект веднаш штом ќе започне извршувањето. Откако ќе го преименувате, може да дефинирате една или повеќе колони како примарен клуч во проекцијата.{0}{0}{1} Мапирајте го објектот со друг постоен целен објект во кој една или повеќе колони се веќе дефинирани како примарен клуч.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Примарниот клуч е променет.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=Имате дефинирано различни колони како примарен клуч за целниот објект во споредба со изворниот објект. Погрижете се овие колони уникатно да ги идентификуваат сите редови за да избегнете можно оштетување на податоците при реплицирање на податоците подоцна. {0} {0} Следниве колони се дефинирани како примарен клуч во изворниот објект: {0} {1}
#XMSG
duplicateDPIDColumns=Преименувајте ја целната колона.
#XMSG
duplicateDPIDDColumnsDesc1=Називот на оваа целна колона е резервиран за техничка колона. Внесете друг назив за да ја зачувате проекцијата.
#XMSG:
targetAutoRenameDPID=Целната колона е преименувана.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Целната колона е преименувана за да се дозволат репликации од изворот на ABAP без клучеви. Ова се должи на една од следниве причини: {0} {1}{2}називот на колоната е резервиран{3} {2}неподдржани знаци{3}{2}префиксот е резервиран{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle={0} поставки за цел
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle={0} поставки за извор
#XBUT
connectionSettingSave=Зачувај
#XBUT
connectionSettingCancel=Откажи
#XBUT: Button to keep the object level settings
txtKeep=Задржи
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Замени
#XFLD
targetConnectionThreadlimit=Ограничување на целната нишка за почетно вчитување (1 – 100)
#XFLD
connectionThreadLimit=Ограничување на изворната нишка за почетно вчитување (1 – 100)
#XFLD
maxConnection=Ограничување на нишката за репликација (1–100)
#XFLD
kafkaNumberOfPartitions=Број партиции
#XFLD
kafkaReplicationFactor=Фактор на репликација
#XFLD
kafkaMessageEncoder=Уред за кодирање пораки
#XFLD
kafkaMessageCompression=Компресија на порака
#XFLD
fileGroupDeltaFilesBy=Групирај делта според
#XFLD
fileFormat=Тип датотека
#XFLD
csvEncoding=CSV-кодирање
#XFLD
abapExitLbl=ABAP Exit
#XFLD
deltaPartition=Број на нишки со објекти за делта-вчитувања (1 – 10)
#XFLD
clamping_Data=Не успеа кратењето на податоците
#XFLD
fail_On_Incompatible=Неуспех со некомпатибилни податоци
#XFLD
maxPartitionInput=Макс. број партиции
#XFLD
max_Partition=Дефинирајте макс. број партиции
#XFLD
include_SubFolder=Опфати потпапки
#XFLD
fileGlobalPattern=Глобална шема за назив на датотека
#XFLD
fileCompression=Компресија на датотека
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Разделувач на датотеки
#XFLD
fileIsHeaderIncluded=Заглавие на датотека
#XFLD
fileOrient=Orient
#XFLD
gbqWriteMode=Режим на пишување
#XFLD
suppressDuplicate=Спречи дупликати
#XFLD
apacheSpark=Овозможи компатибилност со Apache Spark
#XFLD
clampingDatatypeCb=Изврши Clamp за типови податоци со лебдечка децимална запирка
#XFLD
overwriteDatasetSetting=Заменете ги поставките за цел на ниво на објект
#XFLD
overwriteSourceDatasetSetting=Заменете ги поставките за изворот на ниво на објект
#XMSG
kafkaInvalidConnectionSetting=Внесете број меѓу {0} и {1}.
#XMSG
MinReplicationThreadErrorMsg=Внесете број поголем од {0}.
#XMSG
MaxReplicationThreadErrorMsg=Внесете број помал од {0}.
#XMSG
DeltaThreadErrorMsg=Внесете вредност меѓу 1 и 10.
#XMSG
MaxPartitionErrorMsg=Внесете вредност помеѓу 1 <= x <= 2147483647. Стандардната вредност е 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Внесете цел број меѓу {0} и {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Користи фактор на репликација на брокерот
#XFLD
serializationFormat=Формат на серијализација
#XFLD
compressionType=Тип компресија
#XFLD
schemaRegistry=Користи регистар на шеми
#XFLD
subjectNameStrat=Тема „Стратегија за име“
#XFLD
compatibilityType=Тип компатибилност
#XFLD
confluentTopicName=Назив на темата
#XFLD
confluentRecordName=Назив на записот
#XFLD
confluentSubjectNamePreview=Преглед на називот на предметот
#XMSG
serializationChangeToastMsgUpdated2=Форматот на серијализација се промени во JSON бидејќи регистарот на шеми не е овозможен. За да го промените форматот на серијализација назад во AVRO, мора прво да го овозможите регистарот на шеми.
#XBUT
confluentTopicNameInfo=Називот на темата секогаш се заснова врз називот на целниот објект. Можете да го промените ако го преименувате целниот објект.
#XMSG
emptyRecordNameValidationHeaderMsg=Внесете назив на записот.
#XMSG
emptyPartionHeader=Внесете го бројот на партиции.
#XMSG
invalidPartitionsHeader=Внесете важечки број на партиции.
#XMSG
invalidpartitionsDesc=Внесете број меѓу 1 и 200.000.
#XMSG
emptyrFactorHeader=Внесете фактор на репликација.
#XMSG
invalidrFactorHeader=Внесете важечки фактор на репликација.
#XMSG
invalidrFactorDesc=Внесете број меѓу 1 и 32.767.
#XMSG
emptyRecordNameValidationDescMsg=Ако се користи форматот на серијализација „AVRO“, поддржани се само следниве знаци:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(долна црта)
#XMSG
validRecordNameValidationHeaderMsg=Внесете важечки назив на записот.
#XMSG
validRecordNameValidationDescMsgUpdated=Бидејќи се користи форматот на серијализација „AVRO“, називот на записот мора да содржи само алфанумерички знаци (A-Z, a-z, 0-9) и долна црта (_). Мора да започнува со буква или долна црта.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=„Бројот на нишки со објекти за делта-вчитувања“ може да се постави штом еден или повеќе објекти имаат тип вчитување „почетно и делта“.
#XMSG
invalidTargetName=Неважечки назив на колоната
#XMSG
invalidTargetNameDesc=Називот на целната колона мора да содржи само алфанумерички знаци (A-Z, a-z, 0-9) и долна црта (_).
#XFLD
consumeOtherSchema=Користи други верзии на шемата
#XFLD
ignoreSchemamissmatch=Игнорирај го несовпаѓањето на шемата
#XFLD
confleuntDatatruncation=Не успеа кратењето на податоците
#XFLD
isolationLevel=Ниво на изолација
#XFLD
confluentOffset=Почетна точка
#XFLD
signavioGroupDeltaFilesByText=Ништо
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=Не
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=Не

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Проекции
#XBUT
txtAdd=Додај
#XBUT
txtEdit=Уреди
#XMSG
transformationText=Додајте проекција за да поставите филтер или мапирање.
#XMSG
primaryKeyRequiredText=Изберете примарен клуч со Конфигурирај ја шемата.
#XFLD
lblSettings=Поставки
#XFLD
lblTargetSetting={0}: поставки за цел
#XMSG
@csvRF=Изберете ја датотеката што содржи дефиниција за шема што сакате да ја примените врз сите датотеки во папката.
#XFLD
lblSourceColumns=Изворни колони
#XFLD
lblJsonStructure=JSON-структура
#XFLD
lblSourceSetting={0}: поставки за извор
#XFLD
lblSourceSchemaSetting={0}: поставки за изворната шема
#XBUT
messageSettings=Поставки за порака
#XFLD
lblPropertyTitle1=Својства на објектот
#XFLD
lblRFPropertyTitle=Својства на репликациски тек
#XMSG
noDataTxt=Нема колони за прикажување.
#XMSG
noTargetObjectText=Нема избрано целен објект.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Целни колони
#XMSG
searchColumns=Пребарувај колони
#XTOL
cdcColumnTooltip=Колона за делта-снимање
#XMSG
sourceNonDeltaSupportErrorUpdated=Изворниот објект не поддржува делта-снимање.
#XMSG
targetCDCColumnAdded=Додадени се 2 целни колони за делта-снимање.
#XMSG
deltaPartitionEnable=Ограничувањето за нишки со објекти за делта-вчитувања е додадено во поставките за изворот.
#XMSG
attributeMappingRemovalTxt=Отстранување неважечки мапирања што не се поддржани за нови целни објекти.
#XMSG
targetCDCColumnRemoved=2 целни колони за делта-снимање се отстранети.
#XMSG
replicationLoadTypeChanged=Типот вчитување е променет во „Почетно и делта“.
#XMSG
sourceHDLFLoadTypeError=Променете го типот вчитување во „Почетно и делта“.
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=За да реплицирате објект од изворна врска со врската од тип SAP HANA Cloud, датотеки на езерото од податоци во SAP Datasphere, мора да го користите типот вчитување „почетно и делта“.
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Овозможи снимање Delta.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=За да реплицирате објект од изворна врска со врската од тип SAP HANA Cloud, датотеки на езерото од податоци во SAP Datasphere, мора да овозможите делта-снимање.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Променете го целниот објект.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Не може да се искористи целниот објект бидејќи делта-снимањето е оневозможено. Можете или да го преименувате целниот објект (којшто дозволува да се создаде нов објект со делта-снимање) или да го мапирате со постоен објект со овозможено делта-снимање.
#XMSG
deltaPartitionError=Внесете важечки број на нишки со објекти за делта-вчитувања.
#XMSG
deltaPartitionErrorDescription=Внесете вредност меѓу 1 и 10.
#XMSG
deltaPartitionEmptyError=Внесете број на нишки со објекти за делта-вчитувања.
#XFLD
@lblColumnDescription=Опис
#XMSG
@lblColumnDescriptionText1=За технички цели – управување со дуплираните записи предизвикани од проблеми при репликација на изворните објекти засновани на ABAP кои немаат примарен клуч.
#XFLD
storageType=Склад
#XFLD
skipUnmappedColLbl=Прескокни ги немапираните колони
#XFLD
abapContentTypeLbl=Тип содржина
#XFLD
autoMergeForTargetLbl=Спој ги податоците автоматски
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Општо
#XFLD
lblBusinessName=Деловен назив
#XFLD
lblTechnicalName=Технички назив
#XFLD
lblPackage=Пакет
#XFLD
statusPanel=Статус на извршување
#XBTN: Schedule dropdown menu
SCHEDULE=Распоред
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Уреди распоред
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Избриши распоред
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Создај распоред
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Проверката на потврдувањето на распоредот не успеа
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Распоредот не може да се создаде бидејќи репликацискиот тек се применува во моментов. {0}Почекајте додека не заврши применувањето на репликацискиот тек.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=За репликациските текови што содржат објекти со тип оптоварување „Почетно и Делта“, не може да се создаде распоред.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=За репликациските текови што содржат објекти со тип оптоварување „Почетно и Делта“, не може да се создаде распоред.
#XFLD : Scheduled popover
SCHEDULED=Закажано
#XFLD
CREATE_REPLICATION_TEXT=Создај репликациски тек
#XFLD
EDIT_REPLICATION_TEXT=Уреди репликациски тек
#XFLD
DELETE_REPLICATION_TEXT=Избриши репликациски тек
#XFLD
REFRESH_FREQUENCY=Зачестеност
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Репликацискиот тек не може да се примени бидејќи постојниот распоред{0} не го поддржува типот вчитување „Почетно и Делта“.{0}{0}За да го примените репликацискиот тек, мора да ги поставите типовите вчитување на сите објекти{0} на „Само почетни“. Инаку, може да го избришете распоредот, да го примените {0}репликацискиот тек повторно и да започнете ново извршување. Ова ќе предизвика извршување без крај{0} кое исто така поддржува објекти со тип оптоварување „Почетно и Делта“.
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Репликацискиот тек не може да се примени бидејќи постојниот распоред{0} не го поддржува типот вчитување „Почетно и Делта/Само Делта“.{0}{0}За да го примените репликацискиот тек, мора да ги поставите типовите вчитување на сите објекти{0} на „Само почетни“. Инаку, може да го избришете распоредот, да го примените {0}репликацискиот тек повторно и да започнете ново извршување. Ова ќе предизвика извршување без {0}крај, кое исто така поддржува објекти со тип вчитување „Почетно и Делта/Само Делта“.
#XMSG
SCHEDULE_EXCEPTION=Добивањето детали за распоредот не успеа.
#XFLD: Label for frequency column
everyLabel=Секој
#XFLD: Plural Recurrence text for Hour
hoursLabel=Часови
#XFLD: Plural Recurrence text for Day
daysLabel=Денови
#XFLD: Plural Recurrence text for Month
monthsLabel=Месеци
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Минути
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Добивањето информации за можноста за распоред не е успешно.
#XFLD :Paused field
PAUSED=Паузирано
#XMSG
navToMonitoring=Отворете во Монитор за текови
#XFLD
statusLbl=Статус
#XFLD
lblLastRunExecuted=Почеток на последното извршување
#XFLD
lblLastExecuted=Последно извршување
#XFLD: Status text for Completed
statusCompleted=Завршено
#XFLD: Status text for Running
statusRunning=Се извршува
#XFLD: Status text for Failed
statusFailed=Неуспешно
#XFLD: Status text for Stopped
statusStopped=Запрено
#XFLD: Status text for Stopping
statusStopping=Се запира
#XFLD: Status text for Active
statusActive=Активно
#XFLD: Status text for Paused
statusPaused=Паузирано
#XFLD: Status text for not executed
lblNotExecuted=Сè уште не е извршено
#XFLD
messagesSettings=Поставки за пораки
#XTOL
@validateModel=Пораки за потврдување
#XTOL
@hierarchy=Хиерархија
#XTOL
@columnCount=Број колони
#XMSG
VAL_PACKAGE_CHANGED=Го доделивте овој објект на пакетот „{1}“. Кликнете „Зачувај“ за да ја потврдите променава. Имајте предвид дека доделувањето пакет не може да се поништи во овој уредувач по зачувувањето.
#XMSG
MISSING_DEPENDENCY=Зависностите на објектот „{0}“ не може да се решат во пакетот „{1}“.
#XFLD
deltaLoadInterval=Интервал на делта-вчитување
#XFLD
lblHour=Часа (0–24)
#XFLD
lblMinutes=Минути (0–59)
#XMSG
maxHourOrMinErr=Внесете вредност меѓу 0 и {0} 
#XMSG
maxDeltaInterval=Максималната вредност на интервалот на делта-оптоварување е 24 часа. {0}Променете ја вредноста на минутата или вредноста на часот соодветно.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Патека на целен контејнер
#XFLD
confluentSubjectName=Назив на субјектот
#XFLD
confluentSchemaVersion=Верзија на шемата
#XFLD
confluentIncludeTechKeyUpdated=Вклучи технички клуч
#XFLD
confluentOmitNonExpandedArrays=Испуштете нераширени низи
#XFLD
confluentExpandArrayOrMap=Рашири низа или мапа
#XCOL
confluentOperationMapping=Пресликување на операцијата
#XCOL
confluentOpCode=Код на пресликувањето
#XFLD
confluentInsertOpCode=Вметни
#XFLD
confluentUpdateOpCode=Ажурирај
#XFLD
confluentDeleteOpCode=Избриши
#XFLD
expandArrayOrMapNotSelectedTxt=Не е избрано
#XFLD
confluentSwitchTxtYes=Да
#XFLD
confluentSwitchTxtNo=Не
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Грешка
#XTIT
executeWarning=Предупредување
#XMSG
executeunsavederror=Зачувајте го репликацискиот тек пред да го извршите.
#XMSG
executemodifiederror=Има незачувани промени во репликацискиот тек. Зачувајте го репликацискиот тек.
#XMSG
executeundeployederror=Мора да го примените репликацискиот тек пред да го извршите.
#XMSG
executedeployingerror=Почекајте да заврши применувањето.
#XMSG
msgRunStarted=Извршувањето започна
#XMSG
msgExecuteFail=Извршувањето на репликацискиот тек не успеа
#XMSG
titleExecuteBusy=Почекајте.
#XMSG
msgExecuteBusy=Ги подготвуваме податоците за извршување на репликацискиот тек.
#XTIT
executeConfirmDialog=Предупредување
#XMSG
msgExecuteWithValidations=Репликацискиот тек има грешки при проверка. Извршувањето на репликацискиот тек може да доведе до грешка.
#XMSG
msgRunDeployedVersion=Има промени за применување. Ќе се изврши последната применета верзија на репликацискиот тек. Дали сакате да продолжите?
#XBUT
btnExecuteAnyway=Изврши во секој случај
#XBUT
btnExecuteClose=Затвори
#XBUT
loaderClose=Затвори
#XTIT
loaderTitle=Се вчитува
#XMSG
loaderText=Зема детали од серверот
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Репликацискиот тек на целнава врска што не е од SAP не може да стартува
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=бидејќи излезниот волумен за овој месец не е достапен.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Администраторот може да ги зголеми блоковите Premium Outbound за
#XMSG
premiumOutBoundRFAdminErrMsgPart2=овој закупец, со што излезниот волумен за овој месец ќе стане достапен.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Грешка
#XTIT
deployInfo=Информации
#XMSG
deployCheckFailException=Настана исклучок за време на применувањето
#XMSG
deployGBQFFDisabled=Применувањето репликациски текови со целна врска со Google BigQuery во моментов не е можно бидејќи вршиме одржување на функцијава.
#XMSG
deployKAFKAFFDisabled=Применувањето репликациски текови со целна врска со Apache Kafka во моментов не е можно бидејќи вршиме одржување на функцијава.
#XMSG
deployConfluentDisabled=Применувањето репликациски текови со целна врска со Confluent Kafka во моментов не е можно бидејќи вршиме одржување на функцијава.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=За следниве целни објекти, називите на табелите за делта-снимање веќе се користат од други табели во репозиториумот: {0}мора да ги преименувате целниве објекти за да се уверите дека поврзаните имиња на табелите за делта-снимање се единствени пред да можете да го примените репликацискиот тек.
#XMSG
deployDWCSourceFFDisabled=Применувањето репликациски текови што имаат извор SAP Datasphere во моментов не е можно бидејќи вршиме одржување на функцијава.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Применувањето репликациски текови што содржат локални табели со овозможена делта како изворни објекти во моментов не е можно бидејќи вршиме одржување на функцијава.
#XMSG
deployHDLFSourceFFDisabled=Применувањето репликациски текови што имаат изворни врски со врската од тип SAP HANA Cloud, датотеки од езеро на податоци во моментов не е можно бидејќи вршиме одржување.
#XMSG
deployObjectStoreAsSourceFFDisabled=Применувањето репликациски текови што имаат давател на простор за складирање на облак како нивен извор не е можно во моментов.
#XMSG
deployConfluentSourceFFDisabled=Применувањето репликациски текови што имаат извор Confluent Kafka во моментов не е можно бидејќи вршиме одржување на функцијава.
#XMSG
deployMaxDWCNewTableCrossed=За големи репликациски текови, не е можно да се „зачуваат и применат“ наеднаш. Прво зачувајте го репликацискиот тек и потоа применете го.
#XMSG
deployInProgressInfo=Примената веќе е во тек.
#XMSG
deploySourceObjectInUse=Изворните објекти {0} веќе се користат во репликациските текови {1}.
#XMSG
deployTargetSourceObjectInUse=Изворните објекти {0} веќе се користат во репликациските текови {1}. Целните објекти {2} веќе се користат во репликациските текови {3}.
#XMSG
deployReplicationFlowCheckError=Грешка при потврдување на репликацискиот тек: {0}
#XMSG
preDeployTargetObjectInUse=Целните објекти {0} веќе се користат во репликациските текови {1} и не може да го имате истиот целен објект во два различни репликациски тека. Изберете друг целен објект и обидете се повторно.
#XMSG
runInProgressInfo=Репликацискиот тек веќе се извршува.
#XMSG
deploySignavioTargetFFDisabled=Примената на репликациските текови што имаат SAP Signavio како цел не е можна во моментов бидејќи вршиме одржување на функцијава.
#XMSG
deployHanaViewAsSourceFFDisabled=Примената на репликациски текови што имаат прикази како изворни објекти за избраната изворна врска не е можна во моментов. Обидете се повторно подоцна.
#XMSG
deployMsOneLakeTargetFFDisabled=Примената на репликациските текови што имаат MS OneLake како цел не е можно во моментов бидејќи вршиме одржување на функцијава.
#XMSG
deploySFTPTargetFFDisabled=Примената на репликациските текови што имаат SFTP како цел не е можно во моментов бидејќи вршиме одржување на функцијава.
#XMSG
deploySFTPSourceFFDisabled=Примената на репликациските текови што имаат SFTP како извор не е можно во моментов бидејќи вршиме одржување на функцијава.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Технички назив
#XFLD
businessNameInRenameTarget=Деловен назив
#XTOL
renametargetDialogTitle=Преименувај целен објект
#XBUT
targetRenameButton=Преименувај
#XBUT
targetRenameCancel=Откажи
#XMSG
mandatoryTargetName=Мора да внесете назив.
#XMSG
dwcSpecialChar=_(долна црта) е единствениот дозволен специјален знак.
#XMSG
dwcWithDot=Називот на целната табела може да содржи латинични букви, броеви, долни црти (_) и точки (.). Првиот знак мора да биде буква, број или долна црта (не може да биде точка).
#XMSG
nonDwcSpecialChar=Дозволените специјални знаци се _(долна црта) -(цртичка) .(точка)
#XMSG
firstUnderscorePattern=Називот не смее да започнува со _(долна црта)

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: Прикажи SQL-наредба Создај табела
#XMSG
sqlDialogMaxPKWarning=Во Google BigQuery се поддржани најмногу 16 примарни клучеви, а изворниот објект има поголем број. Затоа, во оваа изјава не се дефинирани примарни клучеви.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Една или повеќе изворни колони имаат типови податоци што не можат да се дефинираат како примарни клучеви во Google BigQuery. Затоа, во овој случај не се дефинирани примарни клучеви. Во Google BigQuery, само следниве типови податоци може да имаат примарен клуч: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Копирај и затвори
#XBUT
closeDDL=Затвори
#XMSG
copiedToClipboard=Ископирано во складот


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Објектот „{0}“ не може да биде дел од синџирот на задачи затоа што нема крај (бидејќи вклучува објекти со тип вчитување Почетно и Делта/Само Делта).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Објектот „{0}“ не може да биде дел од синџирот на задачи затоа што нема крај (бидејќи вклучува објекти со тип вчитување Почетно и Делта).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Објектот „{0}“ не може да се додаде во синџирот со задачи.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Постојат незачувани целни објекти. Зачувајте повторно.{0}{0} Однесувањето на оваа карактеристика е променето: Во минатото, целните објекти се создаваа само во целната околина кога репликацискиот тек е применет.{0} Сега објектите се веќе создадени кога ќе се зачува репликацискиот тек. Репликацискиот тек е создаден пред оваа промена и содржи нови објекти.{0} Пред да го примените, треба повторно да го зачувате репликацискиот тек за правилно да се опфатат новите објекти.
#XMSG
confirmChangeContentTypeMessage=Ќе го промените типот содржина. Ако го направите тоа, ќе се избришат сите постојни проекции.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Назив на субјектот
#XFLD
schemaDialogVersionName=Верзија на шемата
#XFLD
includeTechKey=Вклучи технички клуч
#XFLD
segementButtonFlat=Рамно
#XFLD
segementButtonNested=Вгнездено
#XMSG
subjectNamePlaceholder=Пребарај назив на субјектот

#XMSG
@EmailNotificationSuccess=Конфигурацијата на известувањата по е-пошта за времето на извршување се зачува.

#XFLD
@RuntimeEmailNotification=Известување за времето на извршување

#XBTN
@TXT_SAVE=Зачувај


