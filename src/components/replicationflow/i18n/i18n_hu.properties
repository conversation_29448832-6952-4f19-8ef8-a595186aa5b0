#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Replikációs folyamat

#XFLD: Edit Schema button text
editSchema=<PERSON>éma szerkesztése

#XTIT : Properties heading
configSchema=Séma konfigurálása

#XFLD: save changed button text
applyChanges=Módosítások alkalmazása


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Forráskapcsolat kiválasztása
#XFLD
sourceContainernEmptyText=T<PERSON>roló kiválasztása
#XFLD
targetConnectionEmptyText=Célkapcsolat kiválasztása
#XFLD
targetContainernEmptyText=T<PERSON><PERSON>ó kiválasztása
#XFLD
sourceSelectObjectText=Forrásobjektum kiválasztása
#XFLD
sourceObjectCount=Forrásobjektumok ({0})
#XFLD
targetObjectText=Célobjektumok
#XFLD
confluentBrowseContext=Kontextus kiválasztása
#XBUT
@retry=Újra
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=A bérlő frissítése folyamatban van.

#XTOL
browseSourceConnection=Tallózás a forráskapcsolatok közt
#XTOL
browseTargetConnection=Tallózás a célkapcsolatok közt
#XTOL
browseSourceContainer=Tallózás a forrástárolók közt
#XTOL
browseAndAddSourceDataset=Forrásobjektumok hozzáadása
#XTOL
browseTargetContainer=Tallózás a céltárolók közt
#XTOL
browseTargetSetting=Tallózás a célbeállítások közt
#XTOL
browseSourceSetting=Tallózás a forrásbeállítások közt
#XTOL
sourceDatasetInfo=Infó
#XTOL
sourceDatasetRemove=Eltávolítás
#XTOL
mappingCount=Ez a nem névalapú hozzárendelések/kifejezések száma összesen.
#XTOL
filterCount=Ez a szűrési feltételek száma összesen.
#XTOL
loading=Betöltés...
#XCOL
deltaCapture=Deltarögzítés
#XCOL
deltaCaptureTableName=Deltarögzítési tábla
#XCOL
loadType=Adatátvétel típusa
#XCOL
deleteAllBeforeLoading=Összes törlése adatátvétel előtt
#XCOL
transformationsTab=Projekciók
#XCOL
settingsTab=Beállítások

#XBUT
renameTargetObjectBtn=Célobjektum átnevezése
#XBUT
mapToExistingTargetObjectBtn=Hozzárendelés meglévő célobjektumhoz
#XBUT
changeContainerPathBtn=Tároló elérési útjának módosítása
#XBUT
viewSQLDDLUpdated=Create Table SQL-utasítás megtekintése
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=A forrásobjektum nem támogatja a deltarögzítést, de a kiválasztott célobjektumnál engedélyezett.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=A célobjektum nem használható, mert engedélyezett a deltarögzítés,{0}viszont a forrásobjektum nem támogatja a deltarögzítést.{1}Választhat másik célobjektumot, ami nem támogatja a deltarögzítést.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Már van ilyen nevű célobjektum, de nem használható,{0}mert engedélyezett a deltarögzítés, viszont a forrásobjektum nem támogatja{0}a deltarögzítést.{1}Megadhatja egy meglévő célobjektum nevét, ami nem támogatja{0}a deltarögzítést, vagy adjon meg még nem létező nevet.
#XBUT
copySQLDDLUpdated=Create Table SQL-utasítás másolása
#XMSG
targetObjExistingNoCDCColumnUpdated=A Google BigQueryben meglévő táblázatoknak tartalmazniuk kell a következő oszlopokat az adatváltozás-rögzítéshez (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=A következő forrásobjektumok nem támogatottak, mert nincs eldősleges kulcsuk, vagy olyan kapcsolatot használnak, amely nem felel meg az elsődleges kulcs lehívásához szükséges feltételeknek:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=A lehetséges megoldásról az SAP KBA 3531135-ös számú cikkében olvashat.
#XLST: load type list values
initial=Csak kezdeti
@emailUpdateError=Hiba az e-mail-értesítési lista frissítésekor

#XLST
initialDelta=Kezdeti és delta

#XLST
deltaOnly=Csak delta
#XMSG
confluentDeltaLoadTypeInfo=Confluent Kafka forrás esetén csak a Kezdet és a Delta adatátvétel-típus támogatott.
#XMSG
confirmRemoveReplicationObject=Biztosan törli a replikációt?
#XMSG
confirmRemoveReplicationTaskPrompt=Ezzel törli a meglévő replikációkat. Folytatja?
#XMSG
confirmTargetConnectionChangePrompt=Ezzel alaphelyzetbe állítja a célkapcsolatot, a céltárolót, és törli az összes célobjektumot. Folytatja?
#XMSG
confirmTargetContainerChangePrompt=Ezzel alaphelyzetbe állítja a céltárolót, és törli az összes meglévő célobjektumot. Folytatja?
#XMSG
confirmRemoveTransformObject=Biztosan törli a következő projekciót: {0}?
#XMSG
ErrorMsgContainerChange=Hiba történt a tároló elérési útjának módosításakor.
#XMSG
infoForUnsupportedDatasetNoKeys=A következő forrásobjektumok nem támogatottak, mert nincs elsődleges kulcsuk:
#XMSG
infoForUnsupportedDatasetView=A következő Nézet típusú forrásobjektumok nem támogatottak:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=A következő forrásobjektum nem támogatott, mert nem bemeneti paramétereket tartalmazó SQL-nézet:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=A következő forrásobjektumok nem támogatottak, mert le van tiltva náluk az adatkinyerés:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Confluent-kapcsolatok esetén csak az AVRO és a JSON szerializálási formátum engedélyezett. A következő objektumok nem támogatottak, mert más szerializálási formátumot használnak:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Nem lehet lehívni a sémát a következő objektumokhoz. Válassza ki a megfelelő kontextust, vagy ellenőrizze a sémajegyzék konfigurációját
#XTOL: warning dialog header on deleting replication task
deleteHeader=Törlés
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Az Összes törlése adatátvétel előtt beállítás Google BigQuery esetén nem támogatott.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Az Összes törlése adatátvétel előtt beállítás mindegyik replikáció előtt törli és újra létrehozza az objektumot (topicot). Ekkor a hozzárendelt üzenetek is törlődnek.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Az Összes törlése adatátvétel előtt beállítás ennél a céltípusnál nem támogatott.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Technikai név
#XCOL
connBusinessName=Üzleti név
#XCOL
connDescriptionName=Leírás
#XCOL
connType=Típus
#XMSG
connTblNoDataFoundtxt=Nem találhatók kapcsolatok
#XMSG
connectionError=Hiba történt a kapcsolatok lehívásakor.
#XMSG
connectionCombinationUnsupportedErrorTitle=A kapcsolatkombináció nem támogatott
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=A(z) {0} – {1} történő replikáció jelenleg nem támogatott.
#XMSG
invalidTargetforSourceHDLFErrorTitle=A kapcsolattípus-kombináció nem támogatott
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Nem támogatott a replikációtípus SAP HANA Cloud, Data Lake Files kapcsolattípusú kapcsolatból az {0} rendszerbe. Csak az SAP Datasphere-be replikálhat.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Kiválasztás
#XBUT
containerCancelBtn=Mégse
#XTOL
containerSelectTooltip=Kiválasztás
#XTOL
containerCancelTooltip=Mégse
#XMSG
containerContainerPathPlcHold=Tároló elérési útja
#XFLD
containerContainertxt=Tároló
#XFLD
confluentContainerContainertxt=Kontextus
#XMSG
infoMessageForSLTSelection=Csak az /SLT/tömegesátvitel-azonosító támogatott tárolóként. Válasszon tömegesátvitel-azonosítót az SLT alatt (ha van), és kattintson a Beküldés gombra.
#XMSG
msgFetchContainerFail=Hiba történt a tárolóadatok lehívásakor.
#XMSG
infoMessageForSLTHidden=Ez a kapcsolat nem támogatja az SLT-mappákat, ezért nem szerepelnek az alábbi listában.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Válasszon ki egy tárolót, amely almappákat tartalmaz.
#XMSG
sftpIncludeSubFolderText=Hamis
#XMSG
sftpIncludeSubFolderTextNew=Nem

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Még nincs szűrő-hozzárendelés)
#XMSG
failToFetchRemoteMetadata=Hiba történt a metaadatok lehívásakor.
#XMSG
failToFetchData=Hiba történt a meglévő cél lehívásakor.
#XCOL
@loadType=Adatátvétel típusa
#XCOL
@deleteAllBeforeLoading=Összes törlése adatátvétel előtt

#XMSG
@loading=Betöltés...
#XFLD
@selectSourceObjects=Forrásobjektumok kiválasztása
#XMSG
@exceedLimit=Egyszerre legfeljebb {0} objektumot importálhat. Szüntesse meg legalább {1} objektum kiválasztását.
#XFLD
@objects=Objektumok
#XBUT
@ok=OK
#XBUT
@cancel=Mégse
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Tovább
#XBUT
btnAddSelection=Kiválasztás hozzáadása
#XTOL
@remoteFromSelection=Eltávolítás a kiválasztásból
#XMSG
@searchInForSearchField=Keresés helye: {0}

#XCOL
@name=Technikai név
#XCOL
@type=Típus
#XCOL
@location=Hely
#XCOL
@label=Üzleti név
#XCOL
@status=Állapot

#XFLD
@searchIn=Keresés helye:
#XBUT
@available=Elérhető
#XBUT
@selection=Kiválasztás

#XFLD
@noSourceSubFolder=Táblák és nézetek
#XMSG
@alreadyAdded=Már szerepel a diagramon
#XMSG
@askForFilter=Több mint {0} elem van. Adjon meg szűrő-karakterláncot az elemek számának csökkentéséhez.
#XFLD: success label
lblSuccess=Sikeres
#XFLD: ready label
lblReady=Készen áll
#XFLD: failure label
lblFailed=Sikertelen
#XFLD: fetching status label
lblFetchingDetail=Részletek lehívása

#XMSG Place holder text for tree filter control
filterPlaceHolder=Írjon be szöveget a legfelső szintű objektumok szűréséhez
#XMSG Place holder text for server search control
serverSearchPlaceholder=Kereséshez írja be a szöveget, és nyomja le az Enter billentyűt
#XMSG
@deployObjects={0} objektum importálása...
#XMSG
@deployObjectsStatus=Importált objektumok száma: {0}. Azon objektumok száma, amelyeket nem sikerült importálni: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Nem sikerült megnyitni a helyi tárházböngészőt.
#XMSG
@openRemoteSourceBrowserError=Nem sikerült lehívni a forrásobjektumokat.
#XMSG
@openRemoteTargetBrowserError=Nem sikerült lehívni a célobjektumokat.
#XMSG
@validatingTargetsError=Hiba történt a célok validálásakor.
#XMSG
@waitingToImport=Importálásra kész

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Túllépte az objektumok maximális számát. Egy replikációs folyamatnál legfeljebb 500 objektum választható ki.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Technikai név
#XFLD
sourceObjectBusinessName=Üzleti név
#XFLD
sourceNoColumns=Oszlopok száma
#XFLD
containerLbl=Tároló

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Ki kell választania egy forráskapcsolatot a replikációs folyamat számára.
#XMSG
validationSourceContainerNonExist=Ki kell választania egy tárolót a forráskapcsolat számára.
#XMSG
validationTargetNonExist=Ki kell választania egy célkapcsolatot a replikációs folyamat számára.
#XMSG
validationTargetContainerNonExist=Ki kell választania egy tárolót a célkapcsolat számára.
#XMSG
validationTruncateDisabledForObjectTitle=Replikáció objektumtárolókba.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Csak akkor lehet felhőalapú tárolóba replikálni, ha az Összes törlése adatátvétel előtt beállítás aktiválva van, vagy a célobjektum nem létezik a célban.{0}{0} Ha továbbra is engedélyezni szeretné a replikációt olyan objektumoknál, amelyeknél nincs aktiválva a Összes törlése adatátvétel előtt beállítás, a replikációs folyamat futtatása előtt gondoskodjon róla, hogy a célobjektum ne létezzen a rendszerben.
#XMSG
validationTaskNonExist=Legalább egy replikációnak lennie kell a replikációs folyamatban.
#XMSG
validationTaskTargetMissing=Rendelkeznie kell céllal a következő forrású replikációnál: {0}
#XMSG
validationTaskTargetIsSAC=A kiválasztott cél SAC-műtermék: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=A kiválasztott cél nem támogatott helyi tábla: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Már van ilyen nevű objektum a célban. Az azonban nem használható a helyi tárházba való replikációs folyamat céljaként, mert nem helyi tábla.
#XMSG
validateSourceTargetSystemDifference=Különböző forrás- és célkapcsolatot és tárolókombinációkat kell választania a replikációs folyamat számára.
#XMSG
validateDuplicateSources=egy vagy több replikációnál ismétlődik a következő forrásobjektumnév: {0}.
#XMSG
validateDuplicateTargets=egy vagy több replikációnál ismétlődik a következő célobjektumnév: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=A forrásobjektum ({0}) nem támogatja a deltarögzítést, de a célobjektum ({1}) igen. El kell távolítania a replikációt.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=A Kezdeti és delta adatátvétel-típust kell választania a(z) {0} célobjektumnévvel rendelkező replikációnál.
#XMSG
validationAutoRenameTarget=A céloszlopok át lettek nevezve.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Automatikus projekció lett hozzáadva, és a következő céloszlopok át lettek nevezve, hogy lehetővé váljon a replikáció a célba:{1}{1} {0} {1}{1}Ennek okai a következők lehetnek:{1}{1}{2} Nem támogatott karakterek{1}{2} Fenntartott előtag
#XMSG
validationAutoRenameTargetDescriptionUpdated=Automatikus projekció lett hozzáadva, és a következő céloszlopok át lettek nevezve, hogy lehetővé váljon a replikáció a Google BigQuerybe:{1}{1} {0} {1}{1}Ennek okai a következők lehetnek:{1}{1}{2} Fenntartott oszlopnév{1}{2} Nem támogatott karakterek{1}{2} Fenntartott előtag
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Automatikus projekció lett hozzáadva, és a következő céloszlopok át lettek nevezve, hogy lehetővé váljon a replikáció a Confluentbe:{1}{1} {0} {1}{1}Ennek okai a következők lehetnek:{1}{1}{2} Fenntartott oszlopnév{1}{2} Nem támogatott karakterek{1}{2} Fenntartott előtag
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Automatikus projekció lett hozzáadva, és a következő céloszlopok át lettek nevezve, hogy lehetővé váljon a replikáció a célba:{1}{1} {0} {1}{1}Ennek okai a következők lehetnek:{1}{1}{2} Fenntartott oszlopnév{1}{2} Nem támogatott karakterek{1}{2} Fenntartott előtag
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=A célobjektum át lett nevezve.
#XMSG
autoRenameInfoDesc=A célobjektum át lett nevezve, mert a neve nem támogatott karaktereket tartalmazott. Csak a következő karakterek támogatottak:{0}{0}{1}A–Z{0}{1}a–z{0}{1}0–9{0}{1}.(pont){0}{1}_(aláhúzásjel){0}{1}-(kötőjel)
#XMSG
validationAutoTargetTypeConversion=A céladattípusok módosultak.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=A következő céloszlopoknál módosultak a céladattípusok, mert a Google BigQueryben nem támogatottak a forrásadattípusok:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=A következő céloszlopoknál módosultak a céladattípusok, mert a célkapcsolatban nem támogatottak a forrásadattípusok:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Rövidítse le a céloszlopneveket.
#XMSG
validationMaxCharLengthGBQTargetDescription=A Google BigQueryben az oszlopnevek legfeljebb 300 karakterből állhatnak. Projekció használatával rövidítse le a következő céloszlopneveket:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Nem jönnek létre elsődleges kulcsok.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=A Google BigQueryben legfeljebb 16 elsődleges kulcs támogatott, de a forrásobjektumnak ennél több elsődleges kulcsa van. Egyik elsődleges kulcs sem fog létrejönni a célobjektumban.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Egy vagy több forrásoszlopnak olyan adattípusa van, ami nem határozható meg elsődleges kulcsként a Google BigQueryben. Egyik elsődleges kulcs sem fog létrejönni a célobjektumban.{0}{0}A következő céladattípusok kompatibilisek azokkal a Google BigQuery-adattípusokkal, amelyekhez meghatározható elsődleges kulcs: {0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Határozzon meg egy vagy több oszlopot elsődleges kulcsként.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Elsődleges kulcsként kell meghatároznia egy vagy több oszlopot. Ehhez használja a forrásséma párbeszédpanelét.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Határozzon meg egy vagy több oszlopot elsődleges kulcsként.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Egy vagy több olyan oszlopot kell meghatároznia elsődleges kulcsként, amely megfelel a forrásobjektum elsődlegeskulcs-korlátozásainak. Ehhez nyissa meg a Séma konfigurálása szakaszt a forrásobjektum tulajdonságaiban.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Adjon meg érvényes maximális partícióértéket.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=A partíció maximális értéke legalább 1 és legfeljebb 2 147 483 647 lehet
#XMSG
validateHDLFNoPKDatasetError=Határozzon meg egy vagy több oszlopot elsődleges kulcsként.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Objektum replikálásához elsődleges kulcsként kell meghatároznia egy vagy több céloszlopot. Ezt projekció használatával teheti meg.
#XMSG
validateHDLFNoPKExistingDatasetError=Határozzon meg egy vagy több oszlopot elsődleges kulcsként.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Ha egy meglévő célobjektumba szeretne adatokat replikálni, akkor annak egy vagy több oszlopát elsődleges kulcsként kell meghatározni. {0} A következő módszerekkel határozhat meg egy vagy több oszlopot elsődleges kulcsként: {0}{1} A helyi táblaszerkesztővel módosítsa a meglévő célobjektumot, azután töltse újra a replikációs folyamatot.{0}{1} Nevezze át a célobjektumot a replikációs folyamatban. Így a futás elindításakor új objektum jön létre. Átnevezés után meghatározhat egy vagy több oszlopot elsődleges kulcsként egy projekcióban.{0}{1} Rendelje hozzá az objektumot egy másik meglévő célobjektumhoz, amelynek egy vagy több oszlopa már elsődleges kulcsként van meghatározva.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=A kiválasztott cél már létezik a tárházban: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=A deltarögzítésitábla-neveket már használják a tárház más táblái: {0} A replikációs folyamat mentéséhez át kell neveznie ezeket a célobjektumokat, hogy a társított deltarögzítésitábla-nevek egyediek legyenek.
#XMSG
validateConfluentEmptySchema=Séma meghatározása
#XMSG
validateConfluentEmptySchemaDescUpdated=A forrástáblának nincs sémája. Kattintson a Séma konfigurálása lehetőségre, és adjon meg egyet
#XMSG
validationCSVEncoding=Érvénytelen CSV-kódolás
#XMSG
validationCSVEncodingDescription=A feladat CSV-kódolása nem érvényes.
#XMSG
validateConfluentEmptySchema=Válasszon kompatibilis céladattípust
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Válasszon kompatibilis céladattípust
#XMSG
globalValidateTargetDataTypeDesc=Hiba történt az oszlop-hozzárendelésekben. A Projekciók szakaszban ellenőrizze, hogy mindegyik forrásoszlop egyedi oszlophoz van-e hozzárendelve, amelynek kompatibilis az adattípusa, és hogy érvényes-e mindegyik megadott kifejezés.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Duplikált oszlopnevek.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=A duplikált oszlopnevek nem támogatottak. A projekció párbeszédpanelén javíthatja ki őket. A következő célobjektumok rendelkeznek duplikált névvel: {0}
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Duplikált oszlopnevek.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=A duplikált oszlopnevek nem támogatottak. A következő célobjektumok rendelkeznek duplikált névvel: {0}
#XMSG
deltaOnlyLoadTypeTittle=Az adatok inkonzisztensek lehetnek.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=A Csak delta adatátvételtípus nem veszi figyelembe a forrásban a legutóbbi mentés és a legközelebbi futás között végrehajtott módosításokat.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Módosítsa az adatátvételt Kezdeti típusúra.
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Az elsődleges kulccsal nem rendelkező ABAP-alapú objektumok replikálása kizárólag "Csak kezdeti" adatátvétel-típusnál lehetséges.
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Tiltsa le a deltarögzítést.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Ha elsődleges kulcs nélküli objektumot szeretne replikálni ABAP forráskapcsolat-típussal, előbb le kell tiltania a detarögzítést ennél a táblánál.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Módosítsa a célobjektumot.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=A célobjektum nem használható, mert engedélyezve van a deltarögzítés. Átnevezheti a célobjektumot, azután kikapcsolhatja a deltarögzítést az új (átnevezett) objektumnál, vagy hozzárendelheti a forrásobjektumot egy olyan célobjektumhoz, amelynél le van tiltva a deltarögzítés.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Módosítsa a célobjektumot.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=A célobjektum nem használható, mert nem rendelkezik a __load_package_id nevű kötelező technikai oszloppal. Átnevezheti a célobjektumot egy olyan névre, ami még nem létezik. Ekkor a rendszer új objektumot hoz létre, amelynek ugyanaz a definíciója, mint a forrásobjektumé, és tartalmazza a technikai oszlopot. A másik lehetőség, hogy egy olyan meglévő objektumhoz rendeli hozzá a célobjektumot , amely rendelkezik a kötelező technikai oszloppal (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Módosítsa a célobjektumot.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=A célobjektum nem használható, mert nem rendelkezik a __load_record_id nevű kötelező technikai oszloppal. Átnevezheti a célobjektumot egy olyan névre, ami még nem létezik. Ekkor a rendszer új objektumot hoz létre, amelynek ugyanaz a definíciója, mint a forrásobjektumé, és tartalmazza a technikai oszlopot. A másik lehetőség, hogy egy olyan meglévő objektumhoz rendeli hozzá a célobjektumot , amely rendelkezik a kötelező technikai oszloppal (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Módosítsa a célobjektumot.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=A célobjektum nem használható, mert a __load_record_id nevű technikai oszlopának nem string(44) az adattípusa. Átnevezheti a célobjektumot egy olyan névre, ami még nem létezik. Ekkor a rendszer új objektumot hoz létre, amelynek ugyanaz a definíciója, mint a forrásobjektumé, következésképpen helyes az adattípusa is. A másik lehetőség, hogy egy olyan meglévő objektumhoz rendeli hozzá a célobjektumot , amelyben a kötelező technikai oszlopnak (__load_record_id) helyes az adattípusa.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Módosítsa a célobjektumot.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=A célobjektum nem használható, mert van elsődleges kulcsa, a forrásobjektumnak pedig nincs. Átnevezheti a célobjektumot egy olyan névre, ami még nem létezik. Ekkor a rendszer új objektumot hoz létre, amelynek ugyanaz a definíciója, mint a forrásobjektumé, következésképpen nincs elsődleges kulcsa. A másik lehetőség, hogy egy olyan meglévő objektumhoz rendeli hozzá a célobjektumot , amely rendelkezik a kötelező technikai oszloppal (__load_package_id), és nincs elsődleges kulcsa.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Módosítsa a célobjektumot.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=A célobjektum nem használható, mert van elsődleges kulcsa, a forrásobjektumnak pedig nincs. Átnevezheti a célobjektumot egy olyan névre, ami még nem létezik. Ekkor a rendszer új objektumot hoz létre, amelynek ugyanaz a definíciója, mint a forrásobjektumé, következésképpen nincs elsődleges kulcsa. A másik lehetőség, hogy egy olyan meglévő objektumhoz rendeli hozzá a célobjektumot , amely rendelkezik a kötelező technikai oszloppal (__load_record_id), és nincs elsődleges kulcsa.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Módosítsa a célobjektumot.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=A célobjektum nem használható, mert a __load_package_id nevű technikai oszlopának nem binary(>=256) az adattípusa. Átnevezheti a célobjektumot egy olyan névre, ami még nem létezik. Ekkor a rendszer új objektumot hoz létre, amelynek ugyanaz a definíciója, mint a forrásobjektumé, következésképpen helyes az adattípusa is. A másik lehetőség, hogy egy olyan meglévő objektumhoz rendeli hozzá a célobjektumot , amelyben a kötelező technikai oszlopnak (__load_package_id) helyes az adattípusa.
#XMSG
validationAutoRenameTargetDPID=A céloszlopok át lettek nevezve.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Távolítsa el a forrásobjektumot.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=A forrásobjektumnak nincs kulcsoszlopa, amely ebben a kontextusban nem támogatott.
#XMSG
validationAutoRenameTargetDPIDDescription=Automatikus projekció lett hozzáadva, és a következő céloszlopok át lettek nevezve, hogy lehetővé váljon a replikáció az ABAP-forrásból kulcsok nélkül:{1}{1} {0} {1}{1}Ennek okai a következők:{1}{1}{2} Fenntartott oszlopnév{1}{2} Nem támogatott karakterek{1}{2} Fenntartott előtag
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Replikáció ide: {0}
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Jelenleg nem lehet olyan replikációs folyamatokat menteni és üzembe helyezni, amelyek célja a(z) {0}, mert éppen karbantartjuk ezt a funkciót.
#XMSG
TargetColumnSkippedLTF=A céloszlop ki lett hagyva.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=A céloszlop nem támogatott adattípus miatt ki lett hagyva. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Időoszlop elsődleges kulcsként
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=A forrásobjektum elsődleges kulcsa időoszlop, amely ebben a kontextusban nem támogatott.
#XMSG
validateNoPKInLTFTarget=Hiányzik az elsődleges kulcs.
#XMSG
validateNoPKInLTFTargetDescription=Nincs megadva az elsődleges kulcs a célban, és ez nem támogatott ebben a kontextusban.
#XMSG
validateABAPClusterTableLTF=ABAP-clustertábla
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=A forrásobjektum egy ABAP-clustertábla, ami nem támogatott ebben a kontextusban.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Úgy tűnik, még nem adott hozzá adatokat.
#YINS
welcomeText2=A replikációs folyamat indításához válasszon kapcsolatot és forrásobjektumot a bal oldalon.

#XBUT
wizStep1=Forráskapcsolat kiválasztása
#XBUT
wizStep2=Forrástároló kiválasztása
#XBUT
wizStep3=Forrásobjektumok hozzáadása

#XMSG
limitDataset=Elérte az objektumok maximális számát. Távolítson el meglévő objektumokat újak hozzáadásához, vagy hozzon létre új replikációs folyamatot.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Nem indítható el a replikációs folyamat ehhez a nem SAP-s célkapcsolathoz, mert nem áll rendelkezésre kimenő adatmennyiség ebben a hónapban.
#XMSG
premiumOutBoundRFAdminWarningMsg=Az adminisztrátor növelheti a prémium kimenő blokkok számát ennél a bérlőnél, hogy rendelkezésre álljon kimenő adatmennyiség ebben a hónapban.
#XMSG
messageForToastForDPIDColumn2=Új oszlop hozzáadva a célhoz {0} objektumnál - az elsődleges kulccsal nem rendelkező, ABAP-alapú forrásobjektumokhoz kapcsódó duplikált rekordokhoz szükséges.
#XMSG
PremiumInboundWarningMessage=A replikációs folyamatok számától és a replikálandó adatok mennyiségétől függően {0} a(z) {1} általi replikáláshoz szükséges SAP HANA-erőforrások túlléphetik a bérlője számára elérhető kapacitást.
#XMSG
PremiumInboundWarningMsg=A replikációs folyamatok számától és a replikálandó adatok mennyiségétől függően{0}a(z) {1} általi replikáláshoz szükséges SAP HANA-erőforrások túlléphetik a bérlője számára elérhető kapacitást.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Adja meg a projekció nevét.
#XMSG
emptyTargetColumn=Adja meg a céloszlop nevét.
#XMSG
emptyTargetColumnBusinessName=Adja meg az Üzleti név céloszlopot.
#XMSG
invalidTransformName=Adja meg a projekció nevét.
#XMSG
uniqueColumnName=Nevezze át a céloszlopot.
#XMSG
copySourceColumnLbl=Oszlopok másolása a forrásobjektumból
#XMSG
renameWarning=Válasszon egyedi nevet a céltábla átnevezésekor. Ha az új névvel rendelkező tábla már létezik a térben, akkor a tábla definícióját fogja használni.

#XMSG
uniqueColumnBusinessName=Változtassa meg a céloszlop üzleti nevét.
#XMSG
uniqueSourceMapping=Válasszon másik forrásoszlopot.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=A(z) {0} forrásoszlopot már a következő céloszlopok használják:{1}{1}{2}{1}{1} A projekció mentéséhez válasszon olyan forrásoszlopot ennél a céloszlopnál vagy a többi céloszlopnál, ami még nem használatos.
#XMSG
uniqueColumnNameDescription=A megadott céloszlopnév már létezik. Csak akkor mentheti a projekciót, ha egyedi oszlopnevet ad meg.
#XMSG
uniqueColumnBusinessNameDesc=A céloszlop üzleti neve már létezik. Csak akkor mentheti a projekciót, ha egyedi üzleti nevet ad meg az oszlophoz.
#XMSG
emptySource=Válasszon egy forrásoszlopot vagy adjon meg egy konstanst.
#XMSG
emptySourceDescription=Érvényes hozzárendelési bejegyzés létrehozásához ki kell választania egy forrásoszlopot vagy meg kell adnia egy konstans értéket.
#XMSG
emptyExpression=Határozza meg a hozzárendelést.
#XMSG
emptyExpressionDescription1=Válassza ki azt a forrásoszlopot, amelyhez hozzá szeretné rendelni a céloszlopot, vagy jelölje be a jelölőnégyzetet a {0} Függvények / konstansok {1}oszlopban. {2} {2} A függvények automatikusan be lesznek írva a céladattípusnak megfelelően. A konstans értékek manuálisan adhatók meg.
#XMSG
numberExpressionErr=Adjon meg számot.
#XMSG
numberExpressionErrDescription=Numerikus adattípust választott. Ez azt jelenti, hogy csak számokat és adott esetben tizedesjelet írhat be. Ne használjon aposztrófot.
#XMSG
invalidLength=Adjon meg érvényes hosszértéket.
#XMSG
invalidLengthDescription=Az adattípus hossza nem lehet kisebb a forrásoszlop hosszánál, és 1 és 5000 között lehet.
#XMSG
invalidMappedLength=Adjon meg érvényes hosszértéket.
#XMSG
invalidMappedLengthDescription=Az adattípus hossza nem lehet kisebb a(z) {0} forrásoszlop hosszánál, és 1 és 5000 között lehet.
#XMSG
invalidPrecision=Adjon meg érvényes pontosságértéket.
#XMSG
invalidPrecisionDescription=A pontosság a számjegyek számát határozza meg összesen. A skála a tizedesjel utáni számjegyek számát határozza meg, és az értéke 0 és a pontosság értéke között lehet.{0}{0} Példák: {0}{1} Ha a pontosság 6, a skála 2, akkor az 1234.56-hoz hasonlóak lesznek a számok.{0}{1} Ha a pontosság 6 és a skála is 6, akkor pedig a 0.123546-hoz hasonlóak.{0} {0} A cél pontosságának és skálájának kompatibilisnek kell lennie a forráséval, hogy a forrásban lévő összes számjegy beférjen a célmezőbe. Ha például a pontosság 6 és a skála 2 a forrásban (következésképpen vannak a 0-tól különböző számjegyek a tizedesjel előtt), akkor a célban nem lehet a pontosság 6 és a skála is 6.
#XMSG
invalidPrimaryKey=Adjon meg legalább egy elsődleges kulcsot.
#XMSG
invalidPrimaryKeyDescription=Nincs meghatározva elsődleges kulcs ehhez a sémához.
#XMSG
invalidMappedPrecision=Adjon meg érvényes pontosságértéket.
#XMSG
invalidMappedPrecisionDescription1=A pontosság a számjegyek számát határozza meg összesen. A skála a tizedesjel utáni számjegyek számát határozza meg, és az értéke 0 és a pontosság értéke között lehet.{0}{0} Példák:{0}{1} Ha a pontosság 6, a skála 2, akkor az 1234.56-hoz hasonlóak lesznek a számok.{0}{1} Ha a pontosság 6 és a skála is 6, akkor pedig a 0.123546-hoz hasonlóak.{0}{0}Az adattípus pontossága nem lehet kisebb a forrás ({2}) pontosságánál.
#XMSG
invalidScale=Adjon meg érvényes skálaértéket.
#XMSG
invalidScaleDescription=A pontosság a számjegyek számát határozza meg összesen. A skála a tizedesjel utáni számjegyek számát határozza meg, és az értéke 0 és a pontosság értéke között lehet.{0}{0} Példák: {0}{1} Ha a pontosság 6, a skála 2, akkor az 1234.56-hoz hasonlóak lesznek a számok.{0}{1} Ha a pontosság 6 és a skála is 6, akkor pedig a 0.123546-hoz hasonlóak.{0} {0} A cél pontosságának és skálájának kompatibilisnek kell lennie a forráséval, hogy a forrásban lévő összes számjegy beférjen a célmezőbe. Ha például a pontosság 6 és a skála 2 a forrásban (következésképpen vannak a 0-tól különböző számjegyek a tizedesjel előtt), akkor a célban nem lehet a pontosság 6 és a skála is 6.
#XMSG
invalidMappedScale=Adjon meg érvényes skálaértéket.
#XMSG
invalidMappedScaleDescription1=A pontosság a számjegyek számát határozza meg összesen. A skála a tizedesjel utáni számjegyek számát határozza meg, és az értéke 0 és a pontosság értéke között lehet.{0}{0} Példák:{0}{1} Ha a pontosság 6, a skála 2, akkor az 1234.56-hoz hasonlóak lesznek a számok.{0}{1} Ha a pontosság 6 és a skála is 6, akkor pedig a 0.123546-hoz hasonlóak.{0}{0}Az adattípus skálája nem lehet kisebb a forrás ({2}) skálájánál.
#XMSG
nonCompatibleDataType=Válasszon kompatibilis céladattípust.
#XMSG
nonCompatibleDataTypeDescription1=Az itt megadott adattípusnak kompatibilisnek kell lennie a forrás adattípusával ({0}). {1}{1} Például ha a forrásoszlop karakterlánc adattípusú, és betűket tartalmaz, akkor a célnál nem használható a decimális adattípus.
#XMSG
invalidColumnCount=Válasszon forrásoszlopot.
#XMSG
ObjectStoreInvalidScaleORPrecision=Adjon meg érvényes pontosság- és skálaértéket.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=Az első érték a pontosság, amely a számjegyek számát határozza meg. A második érték a skála, amely a tizedesjel utáni számjegyek számát határozza meg. A forrásskálaértéknél nagyobb célskálaértéket adjon meg, és ügyeljen arra, hogy a megadott célskálaérték és a pontossági érték közötti különbség nagyobb legyen a forrásskálaérték és a pontossági érték közötti különbségnél.
#XMSG
InvalidPrecisionORScale=Adjon meg érvényes pontosság- és skálaértéket.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=Az első érték a pontosság, amely a számjegyek számát határozza meg. A második érték a skála, amely a tizedesjel utáni számjegyek számát határozza meg.{0}{0}Mivel a forrásadattípus nem támogatott a Google BigQueryben, a DECIMAL céladattípusra konvertálódik. Ebben az esetben csak 38 és 76 közötti pontosság, illetve 9 és 38 közötti skálaérték adható meg. Ezen kívül a pontossági mínuszskála eredménye, amely a tizedesjel előtti számjegyek számának felel meg, csak 29 és 38 között lehet.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=Az első érték a pontosság, amely a számjegyek számát határozza meg. A második érték a skála, amely a tizedesjel utáni számjegyek számát határozza meg.{0}{0}Mivel a forrásadattípus nem támogatott a Google BigQueryben, a DECIMAL céladattípusra konvertálódik. Ebben az esetben a pontosságnak legalább 20-nak kell lennie. Ezen kívül a pontossági mínuszskála eredményének, amely a tizedesjel előtti számjegyek számának felel meg, szintén legalább 20-nak kell lennie.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=Az első érték a pontosság, amely a számjegyek számát határozza meg. A második érték a skála, amely a tizedesjel utáni számjegyek számát határozza meg.{0}{0}Mivel a forrásadattípus nem támogatott a célban, a DECIMAL céladattípusra konvertálódik. Ebben az esetben a pontosságnak 1-nél nem kisebb és 38-nál nem nagyobb közötti számnak kell lennie, valamint a skálának kisebbnek kell lennie a pontosságnál.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=Az első érték a pontosság, amely a számjegyek számát határozza meg. A második érték a skála, amely a tizedesjel utáni számjegyek számát határozza meg.{0}{0}Mivel a forrásadattípus nem támogatott a célban, a DECIMAL céladattípusra konvertálódik. Ebben az esetben a pontosságnak legalább 20-nak kell lennie. Ezen kívül a pontossági mínuszskála eredményének, amely a tizedesjel előtti számjegyek számának felel meg, szintén legalább 20-nak kell lennie.
#XMSG
invalidColumnCountDescription=Érvényes hozzárendelési bejegyzés létrehozásához ki kell választania egy forrásoszlopot vagy meg kell adnia egy konstans értéket.
#XMSG
duplicateColumns=Nevezze át a céloszlopot.
#XMSG
duplicateGBQCDCColumnsDesc=A céloszlop neve fenntartott a Google BigQueryben. Át kell neveznie ahhoz, hogy menthesse a projekciót.
#XMSG
duplicateConfluentCDCColumnsDesc=A céloszlop neve fenntartott a Confluentben. Át kell neveznie ahhoz, hogy menthesse a projekciót.
#XMSG
duplicateSignavioCDCColumnsDesc=A céloszlop neve fenntartott az SAP Signavióban. Át kell neveznie ahhoz, hogy menthesse a projekciót.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=A céloszlop neve fenntartott az MS OneLake-ben. Át kell neveznie ahhoz, hogy menthesse a projekciót.
#XMSG
duplicateSFTPCDCColumnsDesc=A céloszlop neve fenntartott az SFTP-ben. Át kell neveznie ahhoz, hogy menthesse a projekciót.
#XMSG
GBQTargetNameWithPrefixUpdated1=A céloszlop neve olyan előtagot tartalmaz, ami fenntartott a Google BigQueryben. Át kell neveznie ahhoz, hogy menthesse a projekciót. {0}{0}A céloszlop neve nem kezdődhet a következő karakterláncokkal:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Rövidítse le a céloszlopnevet.
#XMSG
GBQtargetMaxLengthDesc=A Google BigQueryben az oszlopnevek legfeljebb 300 karakterből állhatnak. Rövidítse le a céloszlopnevet ahhoz, hogy menthesse a projekciót.
#XMSG
invalidMappedScalePrecision=A cél pontosságának és skálájának kompatibilisnek kell lennie a forrás pontosságával és skálájával, hogy a forrás minden számjegye beférjen a célmezőbe.
#XMSG
invalidMappedScalePrecisionShortText=Adjon meg érvényes pontosság- és skálaértéket.
#XMSG
validationIncompatiblePKTypeDescProjection3=Egy vagy több forrásoszlopnak olyan adattípusa van, ami nem határozható meg elsődleges kulcsként a Google BigQueryben. Egyik elsődleges kulcs sem fog létrejönni a célobjektumban.{0}{0}A következő céladattípusok kompatibilisek azokkal a Google BigQuery-adattípusokkal, amelyekhez meghatározható elsődleges kulcs: {1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Oszlop jelölésének törlése __message_id.
#XMSG
uncheckColumnMessageIdDesc=Oszlop: elsődleges kulcs
#XMSG
validationOpCodeInsert=Meg kell adni értéket a beszúráshoz.
#XMSG
recommendDifferentPrimaryKey=Javasoljuk, hogy más elsődleges kulcsot válasszon elemszinten.
#XMSG
recommendDifferentPrimaryKeyDesc=Ha már meg van adva a műveletkód, ajánlott eltérő elsődleges kulcsokat választani a tömbindex és az elemek esetében, így elkerülve az olyan problémákat, mint például az oszlopduplikálódás.
#XMSG
selectPrimaryKeyItemLevel=Legalább egy elsődleges kulcsot ki kell választania mind a fejléc-, mind az elemszint számára.
#XMSG
selectPrimaryKeyItemLevelDesc=Tömb vagy térkép kiterjesztésekor két elsődleges kulcsot kell választania: egyet fejlécszinten, egyet elemszinten.
#XMSG
invalidMapKey=Legalább egy elsődleges kulcsot ki kell választania fejlécszinten.
#XMSG
invalidMapKeyDesc=Tömb vagy térkép kiterjesztésekor elsődleges kulcsot kell választania a fejlécszinten.
#XFLD
txtSearchFields=Céloszlopok keresése
#XFLD
txtName=Név
#XMSG
txtSourceColValidation=Egy vagy több forrásoszlop nem támogatott:
#XMSG
txtMappingCount=Hozzárendelések ({0})
#XMSG
schema=Séma
#XMSG
sourceColumn=Forrásoszlopok
#XMSG
warningSourceSchema=A sémán végzett módosítások hatással lesznek a projekció párbeszédpanelén megadott hozzárendelésekre.
#XCOL
txtTargetColName=Céloszlop (technikai név)
#XCOL
txtDataType=Céladattípus
#XCOL
txtSourceDataType=Forrásadattípus
#XCOL
srcColName=Forrásoszlop (technikai név)
#XCOL
precision=Pontosság
#XCOL
scale=Skála
#XCOL
functionsOrConstants=Függvények / konstansok
#XCOL
txtTargetColBusinessName=Céloszlop (Üzleti név)
#XCOL
prKey=Elsődleges kulcs
#XCOL
txtProperties=Tulajdonságok
#XBUT
txtOK=Mentés
#XBUT
txtCancel=Mégse
#XBUT
txtRemove=Eltávolítás
#XFLD
txtDesc=Leírás
#XMSG
rftdMapping=Hozzárendelés
#XFLD
@lblColumnDataType=Adattípus
#XFLD
@lblColumnTechnicalName=Technikai név
#XBUT
txtAutomap=Automatikus hozzárendelés
#XBUT
txtUp=Fel
#XBUT
txtDown=Le

#XTOL
txtTransformationHeader=Projekció
#XTOL
editTransformation=Szerkesztés
#XTOL
primaryKeyToolip=Kulcs


#XMSG
rftdFilter=Szűrés
#XMSG
rftdFilterColumnCount=Forrás: {0}({1})
#XTOL
rftdFilterColSearch=Keresés
#XMSG
rftdFilterColNoData=Nincs megjeleníthető oszlop
#XMSG
rftdFilteredColNoExps=Nincs szűrőkifejezés
#XMSG
rftdFilterSelectedColTxt=Szűrő hozzáadása:
#XMSG
rftdFilterTxt=Szűrő elérhetősége
#XBUT
rftdFilterSelectedAddColExp=Kifejezés hozzáadása
#YINS
rftdFilterNoSelectedCol=Válasszon oszlopot a szűrő hozzáadásához.
#XMSG
rftdFilterExp=Szűrőkifejezés
#XMSG
rftdFilterNotAllowedColumn=A szűrők hozzáadása nem támogatott ennél az oszlopnál.
#XMSG
rftdFilterNotAllowedHead=Nem támogatott oszlop
#XMSG
rftdFilterNoExp=Nincs megadva szűrő
#XTOL
rftdfilteredTt=Szűrt
#XTOL
rftdremoveexpTt=Szűrőkifejezés eltávolítása
#XTOL
validationMessageTt=Validálási üzenetek
#XTOL
rftdFilterDateInp=Válasszon dátumot
#XTOL
rftdFilterDateTimeInp=Válasszon dátumot és időt
#XTOL
rftdFilterTimeInp=Válaszon időt
#XTOL
rftdFilterInp=Adjon meg értéket
#XMSG
rftdFilterValidateEmptyMsg={0} szűrőkifejezés üres {1} oszlopnál
#XMSG
rftdFilterValidateInvalidNumericMsg={0} szűrőkifejezés {1} oszlopnál érvénytelen numerikus értéket tartalmaz
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=A szűrőkifejezésnek érvényes numerikus értékeket kell tartalmaznia
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Ha módosult a célobjektumséma, használja a Hozzárendelés meglévő célobjektumhoz funkciót a főoldalon a módosítások adaptálásához, és rendelje össze újra a célobjektumot a forrásával.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Ha a céltábla már létezik, és a hozzárendelés sémamódosítást tartalmaz, akkor a replikációs folyamat üzembe helyezése előtt ennek megfelelően módosítania kell a céltáblát.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Ha a hozzárendelés sémamódosítást tartalmaz, akkor a replikációs folyamat üzembe helyezése előtt ennek megfelelően módosítania kell a céltáblát.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=A következő nem támogatott oszlopok kimaradtak a forrásdefinícióból: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=A következő nem támogatott oszlopok kimaradtak a céldefinícióból: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=A következő objektumok nem támogatottak, mert elérhetővé vannak téve felhasználásra: {0} {1} {0} {0} Ha táblákat szeretne használni egy replikációs folyamatban, akkor a szemantikai használat beállítása (a táblabeállításokban) nem lehet {2}Elemzési adathalmaz{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=A célobjektum nem használható, mert elérhetővé van téve felhasználásra. {0} {0} Ha használni szeretné a táblát egy replikációs folyamatban, akkor a szemantikai használat beállítása (a táblabeállításokban) nem lehet {1}Elemzési adathalmaz{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Már van ilyen nevű célobjektum, de az nem használható, mert elérhetővé van téve felhasználásra. {0} {0} Ha használni szeretné a táblát egy replikációs folyamatban, akkor a szemantikai használat beállítása (a táblabeállításokban) nem lehet {1}Elemzési adathalmaz{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Már van ilyen nevű objektum a célban. {0}Az azonban nem használható a helyi tárházba való replikációs folyamat céljaként, mert nem helyi tábla.
#XMSG:
targetAutoRenameUpdated=A céloszlop át lett nevezve.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=A céloszlop át lett nevezve, hogy lehetővé váljanak a replikációk a Google BigQueryben. Ez az alábbi okokból fordulhat elő:{0} {1}{2}Fenntartott oszlopnév{3}{2}Nem támogatott karakterek{3}{2}Fenntartott előtag{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=A céloszlop át lett nevezve, hogy lehetővé váljanak a replikációk a Confluentben. Ez az alábbi okokból fordulhat elő:{0} {1}{2}Fenntartott oszlopnév{3}{2}Nem támogatott karakterek{3}{2}Fenntartott előtag{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=A céloszlop át lett nevezve, hogy lehetővé váljanak a replikációk a célba. Ez az alábbi okokból fordulhat elő:{0} {1}{2}Nem támogatott karakterek{3}{2}Fenntartott előtag{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=A céloszlop át lett nevezve, hogy lehetővé váljanak a replikációk a célba. Ez az alábbi okokból fordulhat elő:{0} {1}{2}Fenntartott oszlopnév{3}{2}Nem támogatott karakterek{3}{2}Fenntartott előtag{3}{4}
#XMSG:
targetAutoDataType=A céladattípus módosult.
#XMSG:
targetAutoDataTypeDesc=A céladattípus {0} adattípusra módosult, mert a forrásadattípus nem támogatott a Google BigQueryben.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=A céladattípus {0} adattípusra módosult, mert a forrásadattípus nem támogatott a célkapcsolatban.
#XMSG
projectionGBQUnableToCreateKey=Nem jönnek létre elsődleges kulcsok.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=A Google BigQueryben legfeljebb 16 elsődleges kulcs támogatott, de a forrásobjektumnak ennél több elsődleges kulcsa van. Egyik elsődleges kulcs sem fog létrejönni a célobjektumban.
#XMSG
HDLFNoKeyError=Adjon meg egy vagy több oszlopot elsődleges kulcsként.
#XMSG
HDLFNoKeyErrorDescription=Objektum replikálásához meg kell adnia egy vagy több oszlopot elsődleges kulcsként.
#XMSG
HDLFNoKeyErrorExistingTarget=Határozzon meg egy vagy több oszlopot elsődleges kulcsként.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Ha egy meglévő célobjektumba szeretne adatokat replikálni, akkor annak egy vagy több oszlopát elsődleges kulcsként kell meghatározni. {0}{0} A következő módszerekkel határozhat meg egy vagy több oszlopot elsődleges kulcsként: {0}{0}{1} A helyi táblaszerkesztővel módosítsa a meglévő célobjektumot, azután töltse újra a replikációs folyamatot. {0}{0}{1}Nevezze át a célobjektumot a replikációs folyamatban. Így a futás elindításakor új objektum jön létre. Átnevezés után meghatározhat egy vagy több oszlopot elsődleges kulcsként egy projekcióban.{0}{0}{1} Rendelje hozzá az objektumot egy másik meglévő célobjektumhoz, amelynek egy vagy több oszlopa már elsődleges kulcsként van meghatározva.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Az elsődleges kulcs módosult.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=A forrásobjektumhoz képest eltérő oszlopokat adott meg elsődleges kulcsként a célobjektumnál. Ügyeljen rá, hogy ezek az oszlopok minden sort egyedileg határozzanak meg, mert így elkerülhető az adatok esetleges károsodása a későbbi replikálásuk során. {0} {0} A forrásobjektumban a következő oszlopok vannak elsődleges kulcsként meghatározva: {0} {1}
#XMSG
duplicateDPIDColumns=Nevezze át a céloszlopot.
#XMSG
duplicateDPIDDColumnsDesc1=Ez a céloszlopnév egy technikai oszlop számára van fenntartva. A projekció mentéséhez adjon meg más nevet.
#XMSG:
targetAutoRenameDPID=A céloszlop át lett nevezve.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=A céloszlop át lett nevezve, hogy lehetővé váljanak a replikációk ABAP-forrásból kulcsok nélkül. Ez az alábbi okokból fordulhat elő:{0} {1}{2}Fenntartott oszlopnév{3}{2}Nem támogatott karakterek{3}{2}Fenntartott előtag{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle={0} célbeállításai
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle={0} forrásbeállításai
#XBUT
connectionSettingSave=Mentés
#XBUT
connectionSettingCancel=Mégse
#XBUT: Button to keep the object level settings
txtKeep=Megtartás
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Felülírás
#XFLD
targetConnectionThreadlimit=Célszálkorlát kezdeti adatátvételnél (1–100)
#XFLD
connectionThreadLimit=Forrásszálkorlát kezdeti adatátvételnél (1–100)
#XFLD
maxConnection=Replikációsszál-korlát (1–100)
#XFLD
kafkaNumberOfPartitions=Partíciók száma
#XFLD
kafkaReplicationFactor=Replikációs tényező
#XFLD
kafkaMessageEncoder=Üzenetkódoló
#XFLD
kafkaMessageCompression=Üzenettömörítés
#XFLD
fileGroupDeltaFilesBy=Delta csoportosítási szempontja
#XFLD
fileFormat=Fájltípus
#XFLD
csvEncoding=CSV-kódolás
#XFLD
abapExitLbl=ABAP-exit
#XFLD
deltaPartition=Objektumszálak száma delta adatátvételnél (1–10)
#XFLD
clamping_Data=Hiba: csonkolt adatok
#XFLD
fail_On_Incompatible=Hiba: nem kompatibilis adatok
#XFLD
maxPartitionInput=Partíciók maximális száma
#XFLD
max_Partition=Adja meg a partíciók maximális számát
#XFLD
include_SubFolder=Almappákkal együtt
#XFLD
fileGlobalPattern=Globális fájlnévminta
#XFLD
fileCompression=Fájltömörítés
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Fájlhatároló
#XFLD
fileIsHeaderIncluded=Fájlfejléc
#XFLD
fileOrient=Tájolás
#XFLD
gbqWriteMode=Írási mód
#XFLD
suppressDuplicate=Duplikátumok letiltása
#XFLD
apacheSpark=Apache Spark-kompatibilitás engedélyezése
#XFLD
clampingDatatypeCb=Decimális lebegőpontos adattípusok rögzítése
#XFLD
overwriteDatasetSetting=Célbeállítások felülírása objektumszinten
#XFLD
overwriteSourceDatasetSetting=Forrásbeállítások felülírása objektumszinten
#XMSG
kafkaInvalidConnectionSetting={0} és {1} közötti számot adjon meg
#XMSG
MinReplicationThreadErrorMsg=Adjon meg olyan számot, amely nagyobb, mint {0}.
#XMSG
MaxReplicationThreadErrorMsg=Adjon meg olyan számot, amely kisebb, mint {0}.
#XMSG
DeltaThreadErrorMsg=1 és 10 közötti értéket adjon meg.
#XMSG
MaxPartitionErrorMsg=Adjon meg 1 és 2 147 483 647 közötti értéket. Az alapértelmezett érték 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Adjon meg {0} és {1} közötti egész számot.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Közvetítő replikációs tényezőjének használata
#XFLD
serializationFormat=Szerializálási formátum
#XFLD
compressionType=Tömörítéstípus
#XFLD
schemaRegistry=Sémajegyzék használata
#XFLD
subjectNameStrat=Tárgynévstratégia
#XFLD
compatibilityType=Kompatibilitástípus
#XFLD
confluentTopicName=Téma neve
#XFLD
confluentRecordName=Rekord neve
#XFLD
confluentSubjectNamePreview=Tárgynév előnézete
#XMSG
serializationChangeToastMsgUpdated2=A szerializálási formátum JSON-ra módosult, mert nincs engedélyezve a sémajegyzék. Ha vissza szeretne váltani AVRO szerializálási formátumra, előbb engedélyezze a sémajegyzéket.
#XBUT
confluentTopicNameInfo=A téma neve mindig a célobjektum nevén alapul. A módosításához nevezze át a célobjektumot.
#XMSG
emptyRecordNameValidationHeaderMsg=Adja meg a rekord nevét.
#XMSG
emptyPartionHeader=Adja meg a partíciók számát.
#XMSG
invalidPartitionsHeader=Érvényes számú partíciót adjon meg.
#XMSG
invalidpartitionsDesc=1 és 200 000 közti számot adjon meg.
#XMSG
emptyrFactorHeader=Adja meg a replikációs tényezőt.
#XMSG
invalidrFactorHeader=Érvényes replikációs tényezőt adjon meg.
#XMSG
invalidrFactorDesc=1 és 32 767 közti számot adjon meg.
#XMSG
emptyRecordNameValidationDescMsg=Az AVRO szerializálási formátum használata esetén csak a következő karakterek támogatottak:{0}{1} A–Z{0}{1} a–z{0}{1} 0–9{0}{1} _(aláhúzásjel)
#XMSG
validRecordNameValidationHeaderMsg=Érvényes rekordnevet adjon meg.
#XMSG
validRecordNameValidationDescMsgUpdated=Mivel az AVRO szerializálási formátum van használhatban, a rekordnév csak alfanumerikus karaktereket (A–Z, a–z, 0–9) és aláhúzásjelet (_) tartalmazhat. Csak betűvel vagy aláhúzásjellel kezdődhez.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=Az Objektumszálak száma delta adatátvételnél beállítás értéke akkor adható meg, ha már van legalább egy Kezdeti és delta adatátvétel-típusú objektum.
#XMSG
invalidTargetName=Érvénytelen oszlopnév
#XMSG
invalidTargetNameDesc=A céloszlop neve csak alfanumerikus karaktereket (A–Z, a–z, 0–9) és aláhúzásjelet (_) tartalmazhat.
#XFLD
consumeOtherSchema=Más sémaverziók felhasználása
#XFLD
ignoreSchemamissmatch=Sémaeltérés ignorálása
#XFLD
confleuntDatatruncation=Hiba csonkolt adatok esetén
#XFLD
isolationLevel=Elkülönítési szint
#XFLD
confluentOffset=Kiindulópont
#XFLD
signavioGroupDeltaFilesByText=Egyik sem
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=Nem
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=Nem

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Projekciók
#XBUT
txtAdd=Hozzáadás
#XBUT
txtEdit=Szerkesztés
#XMSG
transformationText=A szűrés vagy hozzárendelés beállításához adjon hozzá projekciót.
#XMSG
primaryKeyRequiredText=Válasszon elsődleges kulcsot sémakonfigurálással.
#XFLD
lblSettings=Beállítások
#XFLD
lblTargetSetting={0}: célbeállítások
#XMSG
@csvRF=Válassza ki azt a fájlt, amely tartalmazza a mappában lévő fájlokra alkalmazni kívánt sémadefiníciót.
#XFLD
lblSourceColumns=Forrásoszlopok
#XFLD
lblJsonStructure=JSON-struktúra
#XFLD
lblSourceSetting={0}: forrásbeállítások
#XFLD
lblSourceSchemaSetting={0}: forrásséma-beállítások
#XBUT
messageSettings=Üzenetbeállítások
#XFLD
lblPropertyTitle1=Objektumtulajdonságok
#XFLD
lblRFPropertyTitle=Replikációs folyamat tulajdonságai
#XMSG
noDataTxt=Nincs megjeleníthető oszlop.
#XMSG
noTargetObjectText=Nincs kiválasztva célobjektum.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Céloszlopok
#XMSG
searchColumns=Oszlopok keresése
#XTOL
cdcColumnTooltip=Oszlop a deltarögzítéshez
#XMSG
sourceNonDeltaSupportErrorUpdated=A forrásobjektum nem támogatja a deltarögzítést.
#XMSG
targetCDCColumnAdded=2 céloszlop lett hozzáadva a deltarögzítéshez.
#XMSG
deltaPartitionEnable=Az objektumszálkorlát delta adatátvételnél hozzáadva a forrásbeállításokhoz.
#XMSG
attributeMappingRemovalTxt=Az új célobjektumnál nem támogatott érvénytelen hozzárendelések eltávolítása.
#XMSG
targetCDCColumnRemoved=A deltarögzítéshez használt 2 oszlop eltávolítva.
#XMSG
replicationLoadTypeChanged=Az adatátvétel Kezdeti és delta típusúra módosult.
#XMSG
sourceHDLFLoadTypeError=Módosítsa az adatátvételt Kezdeti és delta típusúra.
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Ha az SAP HANA Cloud, Data Lake Files kapcsolattípussal szeretne objektumot replikálni egy forráskapcsolatból az SAP Datasphere-be, akkor a Kezdeti és delta adatátvétel-típust kell használnia.
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Engedélyezze a deltarögzítést.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Ha az SAP HANA Cloud, Data Lake Files kapcsolattípussal szeretne objektumot replikálni egy forráskapcsolatból az SAP Datasphere-be, akkor engedélyeznie kell a deltarögzítést.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Módosítsa a célobjektumot.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=A célobjektum nem használható, mert le van tiltva a deltarögzítés. Átnevezheti a célobjektumot (így új objektum hozható létre deltarögzítéssel), vagy hozzárendelheti egy olyan meglévő objektumhoz, amelynél engedélyezett a deltarögzítés.
#XMSG
deltaPartitionError=Adjon meg érvényes számú objektumszálat delta adatátvételnél.
#XMSG
deltaPartitionErrorDescription=1 és 10 közötti értéket adjon meg.
#XMSG
deltaPartitionEmptyError=Adja meg az objektumszálak számát delta adatátvételnél.
#XFLD
@lblColumnDescription=Leírás
#XMSG
@lblColumnDescriptionText1=Technikai okokból - az elsődleges kulccsal nem rendelkező ABAP-alapú forrásobjektumok replikációja során történt problémákból eredő duplikált rekordok kezeléséhez
#XFLD
storageType=Tároló
#XFLD
skipUnmappedColLbl=Nem hozzárendelt oszlopok kihagyása
#XFLD
abapContentTypeLbl=Tartalomtípus
#XFLD
autoMergeForTargetLbl=Adatok automatikus összevonása
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Általános
#XFLD
lblBusinessName=Üzleti név
#XFLD
lblTechnicalName=Technikai név
#XFLD
lblPackage=Csomag
#XFLD
statusPanel=Futás állapota
#XBTN: Schedule dropdown menu
SCHEDULE=Ütemezés
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Ütemezés szerkesztése
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Ütemezés törlése
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Ütemezés létrehozása
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Nem sikerült beütemezni az érvényesség-ellenőrzést
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Nem hozható létre ütemezés, mert a replikációs folyamat üzembe helyezése éppen folyamatban van.{0}Várjon a replikációs folyamat üzembe helyezéséig.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=A Kezdeti és delta adatátvétel-típusú objektumokat tartalmazó replikációs folyamatokhoz nem hozható létre ütemezés.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=A Kezdeti és delta / Csak delta adatátvétel-típusú objektumokat tartalmazó replikációs folyamatokhoz nem hozható létre ütemezés.
#XFLD : Scheduled popover
SCHEDULED=Beütemezve
#XFLD
CREATE_REPLICATION_TEXT=Replikációs folyamat létrehozása
#XFLD
EDIT_REPLICATION_TEXT=Replikációs folyamat szerkesztése
#XFLD
DELETE_REPLICATION_TEXT=Replikációs folyamat törlése
#XFLD
REFRESH_FREQUENCY=Gyakoriság
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Nem lehet üzembe helyezni a replikációs folyamatot, mert a meglévő{0} ütemezés még nem támogatja a Kezdeti és delta típusú adatátvételt.{0}{0}A replikációs folyamat üzembe helyezéséhez állítsa az összes objektum{0} adatátvétel-típusát Csak kezdeti értékre. Vagy törölheti az ütemezést, üzembe {0} helyezheti a replikációs folyamatot, és új futást indíthat. {0} Ez folyamatos futást eredményez, ami a Kezdeti és delta adatátvétel-típusú objektumokat is támogatja.
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Nem lehet üzembe helyezni a replikációs folyamatot, mert a meglévő{0} ütemezés még nem támogatja a Kezdeti és delta / Csak delta típusú adatátvételt.{0}{0}A replikációs folyamat üzembe helyezéséhez állítsa az összes objektum{0} adatátvétel-típusát Csak kezdeti értékre. Vagy törölheti az ütemezést, üzembe {0} helyezheti a replikációs folyamatot, és új futást indíthat. {0} Ez folyamatos futást eredményez, ami a Kezdeti és delta / Csak delta adatátvétel-típusú objektumokat is támogatja.
#XMSG
SCHEDULE_EXCEPTION=Nem sikerült lehívni az ütemezés részleteit
#XFLD: Label for frequency column
everyLabel=Gyakoriság
#XFLD: Plural Recurrence text for Hour
hoursLabel=óra
#XFLD: Plural Recurrence text for Day
daysLabel=nap
#XFLD: Plural Recurrence text for Month
monthsLabel=hónap
#XFLD: Plural Recurrence text for Minutes
minutesLabel=perc
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Nem sikerült információt lehívni az ütemezés lehetségességéről.
#XFLD :Paused field
PAUSED=Szünetel
#XMSG
navToMonitoring=Megnyitás az áramlásfigyelőben
#XFLD
statusLbl=Állapot
#XFLD
lblLastRunExecuted=Legutóbbi futás kezdete
#XFLD
lblLastExecuted=Utolsó futás
#XFLD: Status text for Completed
statusCompleted=Befejeződött
#XFLD: Status text for Running
statusRunning=Fut
#XFLD: Status text for Failed
statusFailed=Sikertelen
#XFLD: Status text for Stopped
statusStopped=Leállítva
#XFLD: Status text for Stopping
statusStopping=Leállítás
#XFLD: Status text for Active
statusActive=Aktív
#XFLD: Status text for Paused
statusPaused=Szünetel
#XFLD: Status text for not executed
lblNotExecuted=Még nem futott
#XFLD
messagesSettings=Üzenetek beállításai
#XTOL
@validateModel=Validálási üzenetek
#XTOL
@hierarchy=Hierarchia
#XTOL
@columnCount=Oszlopok száma
#XMSG
VAL_PACKAGE_CHANGED=Hozzárendelte ezt az objektumot a(z) {1} csomaghoz. Kattintson a Mentés gombra a módosítás megerősítéséhez és validálásához. Ne feledje, hogy a csomaghoz való hozzárendelés mentés után nem vonható vissza ebben a szerkesztőben.
#XMSG
MISSING_DEPENDENCY=A(z) {0} objektum függőségei nem szüntethetők meg a(z) {1} csomagban.
#XFLD
deltaLoadInterval=Delta adatátvétel intervalluma
#XFLD
lblHour=Órák (0–24)
#XFLD
lblMinutes=Percek (0–59)
#XMSG
maxHourOrMinErr=Adjon meg 0 és {0} közötti értéket
#XMSG
maxDeltaInterval=A delta adatátvétel maximális értéke 24 óra.{0}Módosítsa a perc vagy az óra értékét eszerint.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Céltároló elérési útja
#XFLD
confluentSubjectName=Tárgy neve
#XFLD
confluentSchemaVersion=Sémaverzió
#XFLD
confluentIncludeTechKeyUpdated=Technikai kulccsal együtt
#XFLD
confluentOmitNonExpandedArrays=Nem kiterjesztett tömbök kihagyása
#XFLD
confluentExpandArrayOrMap=Tömb vagy térkép kiterjesztése
#XCOL
confluentOperationMapping=Művelet-hozzárendelés
#XCOL
confluentOpCode=Műveletkód
#XFLD
confluentInsertOpCode=Beszúrás
#XFLD
confluentUpdateOpCode=Frissítés
#XFLD
confluentDeleteOpCode=Törlés
#XFLD
expandArrayOrMapNotSelectedTxt=Nincs kiválasztva
#XFLD
confluentSwitchTxtYes=Igen
#XFLD
confluentSwitchTxtNo=Nem
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Hiba
#XTIT
executeWarning=Figyelmeztetés
#XMSG
executeunsavederror=Futtatás előtt mentse a replikációs folyamatot.
#XMSG
executemodifiederror=A replikációs folyamat nem mentett módosításokat tartalmaz. Mentse a replikációs folyamatot.
#XMSG
executeundeployederror=Előbb üzembe kell helyeznie a replikációs folyamatot ahhoz, hogy futtathassa.
#XMSG
executedeployingerror=Kis türelmet, az üzembe helyezés hamarosan befejeződik.
#XMSG
msgRunStarted=A futás elindult
#XMSG
msgExecuteFail=Nem sikerült futtatni a replikációs folyamatot.
#XMSG
titleExecuteBusy=Kis türelmet.
#XMSG
msgExecuteBusy=Előkészítjük az adatokat a replikációs folyamat futtatására.
#XTIT
executeConfirmDialog=Figyelmeztetés
#XMSG
msgExecuteWithValidations=A replikációs folyamat validálási hibákat tartalmaz. Ez sikertelen futást okozhat.
#XMSG
msgRunDeployedVersion=Módosítások várnak üzembe helyezésre. A replikációs folyamat utoljára üzembe helyezett verziója fog futni. Folytatja?
#XBUT
btnExecuteAnyway=Futtatás mindenképp
#XBUT
btnExecuteClose=Bezárás
#XBUT
loaderClose=Bezárás
#XTIT
loaderTitle=Betöltés
#XMSG
loaderText=Adatok lehívása a szerverről
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Nem indítható el a replikációs folyamat ehhez a nem SAP-s célkapcsolathoz.
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=mert nem áll rendelkezésre kimenő adatmennyiség ebben a hónapban.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Az adminisztrátor növelheti a prémium kimenő blokkok számát ennél a bérlőnél,
#XMSG
premiumOutBoundRFAdminErrMsgPart2=hogy rendelkezésre álljon kimenő adatmennyiség ebben a hónapban.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Hiba
#XTIT
deployInfo=Információ
#XMSG
deployCheckFailException=Kivétel történt az üzembe helyezés közben
#XMSG
deployGBQFFDisabled=Jelenleg nem lehet olyan replikációs folyamatokat üzembe helyezni, amelyek célkapcsolata a Google BigQuerybe irányul, mert éppen karbantartjuk ezt a funkciót.
#XMSG
deployKAFKAFFDisabled=Jelenleg nem lehet olyan replikációs folyamatokat üzembe helyezni, amelyek célkapcsolata az Apache Kafkába irányul, mert éppen karbantartjuk ezt a funkciót.
#XMSG
deployConfluentDisabled=Jelenleg nem lehet olyan replikációs folyamatokat üzembe helyezni, amelyek célkapcsolata a Confluent Kafkába irányul, mert éppen karbantartjuk ezt a funkciót.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=A következő célobjektumoknál a deltarögzítésitábla-neveket már használják a tárház más táblái: {0} A replikációs folyamat üzembe helyezéséhez át kell neveznie ezeket a célobjektumokat, hogy a társított deltarögzítésitábla-nevek egyediek legyenek.
#XMSG
deployDWCSourceFFDisabled=Jelenleg nem lehet olyan replikációs folyamatokat üzembe helyezni, amelyek forrása az SAP Datasphere, mert éppen karbantartjuk ezt a funkciót.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Jelenleg nem lehet olyan replikációs folyamatokat üzembe helyezni, amelyek deltaképes táblákat használnak forrásobjektumként, mert éppen karbantartjuk ezt a funkciót.
#XMSG
deployHDLFSourceFFDisabled=Jelenleg nem lehet olyan replikációs folyamatokat üzembe helyezni, amelyek forráskapcsolatának kapcsolattípusa SAP HANA Cloud, Data Lake Files, mert éppen karbantartást végzünk.
#XMSG
deployObjectStoreAsSourceFFDisabled=Jelenleg nem lehet üzembe helyezni olyan replikációs folyamatokat, amelyek forrása felhőtárhely-szolgáltató.
#XMSG
deployConfluentSourceFFDisabled=Jelenleg nem lehet olyan replikációs folyamatokat üzembe helyezni, amelyek forrása a Confluent Kafka, mert éppen karbantartjuk ezt a funkciót.
#XMSG
deployMaxDWCNewTableCrossed=Nagy replikációs folyamatoknál nem lehet egy lépésben menteni és üzembe helyezni. Előbb mentse a replikációs folyamatot, és utána helyezze üzembe.
#XMSG
deployInProgressInfo=Az üzembe helyezés már folyamatban van.
#XMSG
deploySourceObjectInUse=A forrásobjektumok ({0}) már használatban vannak replikációs folyamatokban ({1}).
#XMSG
deployTargetSourceObjectInUse=A forrásobjektumok ({0}) már használatban vannak replikációs folyamatokban ({1}). A célobjektumok ({2}) már használatban vannak replikációs folyamatokban ({3}).
#XMSG
deployReplicationFlowCheckError=Hiba a replikációs folyamat ellenőrzésekor: {0}
#XMSG
preDeployTargetObjectInUse=A célobjektumok ({0}) már használatban vannak a replikációs folyamatokban ({1}), és nem használhatja ugyanazt a célobjektumot két különböző replikációs folyamatban. Válasszon másik célobjektumot, és próbálkozzon újra.
#XMSG
runInProgressInfo=A replikációs folyamat már fut.
#XMSG
deploySignavioTargetFFDisabled=Jelenleg nem lehet olyan replikációs folyamatokat üzembe helyezni, amelyek célja az SAP Signavio, mert éppen karbantartjuk ezt a funkciót.
#XMSG
deployHanaViewAsSourceFFDisabled=Jelenleg nem lehet olyan replikációs folyamatokat üzembe helyezni a kiválasztott forráskapcsolatnál, amelyek nézeteket használnak forrásobjektumként. Próbálkozzon újra később.
#XMSG
deployMsOneLakeTargetFFDisabled=Jelenleg nem lehet olyan replikációs folyamatokat üzembe helyezni, amelyek célja az MS OneLake, mert éppen karbantartjuk ezt a funkciót.
#XMSG
deploySFTPTargetFFDisabled=Jelenleg nem lehet olyan replikációs folyamatokat üzembe helyezni, amelyek célja az SFTP, mert éppen karbantartjuk ezt a funkciót.
#XMSG
deploySFTPSourceFFDisabled=Jelenleg nem lehet olyan replikációs folyamatokat üzembe helyezni, amelyek forrása az SFTP, mert éppen karbantartjuk ezt a funkciót.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Technikai név
#XFLD
businessNameInRenameTarget=Üzleti név
#XTOL
renametargetDialogTitle=Célobjektum átnevezése
#XBUT
targetRenameButton=Átnevezés
#XBUT
targetRenameCancel=Mégse
#XMSG
mandatoryTargetName=Meg kell adni a nevet.
#XMSG
dwcSpecialChar=Csak az _(aláhúzásjel) speciális karakter engedélyezett.
#XMSG
dwcWithDot=A céltábla neve latin betűket, számokat, aláhúzásjeleket (_) és pontokat (.) tartalmazhat. Az első karakternek betűnek, számnak vagy aláhúzásjelnek kell lennie (nem lehet pont).
#XMSG
nonDwcSpecialChar=Engedélyezett speciális karakterek: _(aláhúzásjel) -(kötőjel) .(pont)
#XMSG
firstUnderscorePattern=A név nem kezdődhet _ jellel (aláhúzásjellel).

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: Create Table SQL-utasítás megtekintése
#XMSG
sqlDialogMaxPKWarning=A Google BigQueryben legfeljebb 16 elsődleges kulcs támogatott, és a forrásobjektumban ennél több van. Ezért nincsenek meghatározva elsődleges kulcsok ebben az utasításban.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Egy vagy több forrásoszlopnak olyan adattípusa van, ami nem határozható meg elsődleges kulcsként a Google BigQueryben. Ezért ebben az eseben nincsenek meghatározva elsődleges kulcsok. A Google BigQueryben csak a következő adattípusoknak lehet elsődleges kulcsa: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Másolás és bezárás
#XBUT
closeDDL=Bezárás
#XMSG
copiedToClipboard=Vágólapra másolva


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=A(z) {0} objektum nem lehet feladatlánc része, mert nincs vége (mivel Kezdeti és delta / Csak delta adatátvétel-típusú objektumokat tartalmaz).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=A(z) {0} objektum nem lehet feladatlánc része, mert nincs vége (mivel Kezdeti és delta adatátvétel-típusú objektumokat tartalmaz).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=A(z) {0} objektum nem adható hozzá a feladatlánchoz.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Nincsenek mentve a célobjektumok. Mentsen újra.{0}{0} Ennek a funkciónak megváltozott a viselkedése: korábban csak akkor jöttek létre célobjektumok a célkörnyezetben, amikor üzembe helyezték a replikációs folyamatot.{0} Mostantól az objektumok már a replikációs folyamat mentésekor létrejönnek. A replikációs folyamata a funkció változása előtt jött létre, és új objektumokat tartalmaz.{0} Ezért újra mentenie kell a replikációs folyamatot, hogy megfelelően belekerüljenek az új objektumok.
#XMSG
confirmChangeContentTypeMessage=A tartalomtípus módosítására készül. Ha így tesz, minden meglévő projekció törlődni fog.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Tárgy neve
#XFLD
schemaDialogVersionName=Sémaverzió
#XFLD
includeTechKey=Technikai kulccsal együtt
#XFLD
segementButtonFlat=Egyszerű
#XFLD
segementButtonNested=Beágyazott
#XMSG
subjectNamePlaceholder=Tárgynév keresése

#XMSG
@EmailNotificationSuccess=A futásidejű e-mail-értesítések konfigurációja mentve.

#XFLD
@RuntimeEmailNotification=Futásidejű e-mail-értesítés

#XBTN
@TXT_SAVE=Mentés


