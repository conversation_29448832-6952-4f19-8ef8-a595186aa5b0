#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Тираждау ағыны

#XFLD: Edit Schema button text
editSchema=Схеманы өңдеу

#XTIT : Properties heading
configSchema=Схеманы теңшеу

#XFLD: save changed button text
applyChanges=Өзгерістерді қолдану


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Бастапқы қосылымды таңдау
#XFLD
sourceContainernEmptyText=Контейнерді таңдау
#XFLD
targetConnectionEmptyText=Мақсатты қосылымды таңдау
#XFLD
targetContainernEmptyText=Контейнерді таңдау
#XFLD
sourceSelectObjectText=Бастапқы нысанды таңдау
#XFLD
sourceObjectCount=Бастапқы нысандар ({0})
#XFLD
targetObjectText=Мақсатты нысандар
#XFLD
confluentBrowseContext=Рөл мәнмәтінін таңдау
#XBUT
@retry=Қайталау
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Тенантты жаңарту орындалуда.

#XTOL
browseSourceConnection=Бастапқы қосылымды шолу
#XTOL
browseTargetConnection=Мақсатты қосылымды шолу
#XTOL
browseSourceContainer=Бастапқы контейнерді шолу
#XTOL
browseAndAddSourceDataset=Бастапқы нысандарды қосу
#XTOL
browseTargetContainer=Мақсатты контейнерді шолу
#XTOL
browseTargetSetting=Мақсатты параметрлерді шолу
#XTOL
browseSourceSetting=Бастапқы параметрлерді шолу
#XTOL
sourceDatasetInfo=Ақпарат
#XTOL
sourceDatasetRemove=Жою
#XTOL
mappingCount=Бұл атауға негізделмеген салыстырулардың/өрнектердің жалпы санын көрсетеді.
#XTOL
filterCount=Бұл сүзгі шарттарының жалпы санын көрсетеді.
#XTOL
loading=Жүктелуде...
#XCOL
deltaCapture=Дельта қадағалау
#XCOL
deltaCaptureTableName=Дельта қадағалау кестесі
#XCOL
loadType=Жүктеу түрі
#XCOL
deleteAllBeforeLoading=Жүктемес бұрын барлығын жою
#XCOL
transformationsTab=Проекциялар
#XCOL
settingsTab=Параметрлер

#XBUT
renameTargetObjectBtn=Мақсатты нысанның атын өзгерту
#XBUT
mapToExistingTargetObjectBtn=Бұрыннан бар мақсатты нысанмен салыстыру
#XBUT
changeContainerPathBtn=Контейнер жолын өзгерту
#XBUT
viewSQLDDLUpdated=SQL кесте жасау операторын көру
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Бастапқы нысан дельта қадағалауға қолдау көрсетпейді, бірақ таңдалған мақсатты нысанда дельта қадағалау опциясы қосылған.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Мақсатты нысанды пайдалану мүмкін емес, себебі дельта қадағалау қосулы, {0}ал бастапқы нысан дельта қадағалауға қолдау көрсетпейді.{1}Дельта қадағалауға қолдау көрсетпейтін басқа мақсатты нысанды таңдауға болады.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Осы атауға ие мақсатты нысан бұрыннан бар. Дегенмен, оны пайдалану мүмкін емес,{0} себебі дельта қадағалау қосулы, ал бастапқы нысан дельта қадағалауға {0}қолдау көрсетпейді.{1}Дельта қадағалауға {0}қолдау көрсетпейтін бұрыннан бар мақсатты нысанның атауын енгізуге немесе әлі жоқ атауды енгізуге болады.
#XBUT
copySQLDDLUpdated=SQL кесте жасау операторын көшіру
#XMSG
targetObjExistingNoCDCColumnUpdated=Google BigQuery ішінде бұрынна бар кестелер өзгеріс деректерін жинауға (CDC) арналған келесі бағандарды қамтиды:{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Келесі бастапқы нысандарға қолдау көрсетілмейді, себебі оларда бастапқы кілт жоқ немесе олар бастапқы кілтті шығару шарттарына сәйкес келмейтін қосылымды пайдаланады:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Ықтимал шешім үшін SAP KBA 3531135 қараңыз.
#XLST: load type list values
initial=Тек бастапқы
@emailUpdateError=Эл. пошта хабарландыруларының тізімін жаңарту кезінде қате орын алды

#XLST
initialDelta=Бастапқы және дельта

#XLST
deltaOnly=Тек дельта
#XMSG
confluentDeltaLoadTypeInfo=Confluent Kafka дереккөзі үшін тек "Бастапқы және дельта" жүктеу түріне қолдау көрсетіледі.
#XMSG
confirmRemoveReplicationObject=Тираждауды жою қажеттігін растайсыз ба?
#XMSG
confirmRemoveReplicationTaskPrompt=Осы операция бұрыннан бар тираждауды жояды. Жалғастыру қажет пе?
#XMSG
confirmTargetConnectionChangePrompt=Бұл операция мақсатты қосылымды, мақсатты контейнерді қайта орнатады және барлық мақсатты нысандарды жояды. Жалғастыру қажет пе?
#XMSG
confirmTargetContainerChangePrompt=Бұл операция мақсатты контейнерді қайта орнатады және барлық бұрыннан бар мақсатты нысандарды жояды. Жалғастыру қажет пе?
#XMSG
confirmRemoveTransformObject={0} проекциясын жою қажеттігін растайсыз ба?
#XMSG
ErrorMsgContainerChange=Контейнер жолын өзгерту кезінде қате орын алды.
#XMSG
infoForUnsupportedDatasetNoKeys=Келесі бастапқы нысандарға қолдау көрсетілмейді, себебі оларда негізгі кілт жоқ.
#XMSG
infoForUnsupportedDatasetView="Көріністер" түріне жататын келесі бастапқы нысандарға қолдау көрсетілмейді:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Келесі бастапқы нысанға қолдау көрсетілмейді, себебі ол кіріс параметрлерін қамтитын SQL көрінісі:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Келесі бастапқы нысандарға қолдау көрсетілмейді, себебі олар үшін экстракция өшірілген:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Confluent қосылымдары үшін тек рұқсат етілген сериялау пішімдері AVRO және JSON болып табылады. Келесі нысандарға қолдау көрсетілмейді, себебі олар басқа сериялау пішімін пайдаланады:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Келесі нысандарға арналған схеманы алу мүмкін емес. Тиісті мәнмәтінді таңдаңыз немесе схема тіркелімінің конфигурациясын тексеріңіз
#XTOL: warning dialog header on deleting replication task
deleteHeader=Жою
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Google BigQuery үшін "Жүктемес бұрын барлығын жою" параметріне қолдау көрсетілмейді.
#XBUT
DeleteAllBeforeLoadingConfluentInfo="Жүктемес бұрын барлығын жою" параметрі әр тираждаудың алдында нысанды (тақырып) жойып, қайта жасайды. Сондай-ақ бұл барлық тағайындалған хабарларды жояды.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Осы мақсат түрі үшін "Жүктемес бұрын барлығын жою" параметріне қолдау көрсетілмейді.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Техникалық атау
#XCOL
connBusinessName=Бизнес атау
#XCOL
connDescriptionName=Сипаттама
#XCOL
connType=Түрі
#XMSG
connTblNoDataFoundtxt=Қосылымдар табылмады
#XMSG
connectionError=Қосылымдарды алу кезінде қате орын алды.
#XMSG
connectionCombinationUnsupportedErrorTitle=Қосылымдар тіркесіміне қолдау көрсетілмейді
#XMSG
connectionCombinationUnsupportedErrorMsgTxt={0}-{1} тираждауына қазір қолдау көрсетілмейді.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Қосылым түрлері тіркесіміне қолдау көрсетілмейді
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=SAP HANA Cloud, Data Lake Files қосылым түріне ие қосылымнан {0} қосылымына тираждауға қолдау көрсетілмейді. Сіз тек SAP Datasphere-ге тираждай аласыз.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Таңдау
#XBUT
containerCancelBtn=Бас тарту
#XTOL
containerSelectTooltip=Таңдау
#XTOL
containerCancelTooltip=Бас тарту
#XMSG
containerContainerPathPlcHold=Контейнер жолы
#XFLD
containerContainertxt=Контейнер
#XFLD
confluentContainerContainertxt=Мәнмәтін
#XMSG
infoMessageForSLTSelection=Контейнер ретінде тек /SLT/Жаппай тасымалдау идентификаторына рұқсат етіледі. SLT (бар болса) астындағы жаппай тасымалдау идентификаторын таңдап, "Жіберу" түймесін басыңыз.
#XMSG
msgFetchContainerFail=Контейнер деректерін іріктеу кезінде қате орын алды.
#XMSG
infoMessageForSLTHidden=Бұл қосылым SLT қалталарына қолдау көрсетпейді, сол себепті олар төмендегі тізімде көрсетілмейді.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Ішінде ішкі қалталары бар контейнерді таңдаңыз.
#XMSG
sftpIncludeSubFolderText=Жалған
#XMSG
sftpIncludeSubFolderTextNew=Жоқ

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Сүзгілерді салыстыру әлі орындалммады)
#XMSG
failToFetchRemoteMetadata=Метадеректі іріктеу кезінде қате орын алды.
#XMSG
failToFetchData=Бұрыннан бар мақсатты алу кезінде қате орын алды.
#XCOL
@loadType=Жүктеу түрі
#XCOL
@deleteAllBeforeLoading=Жүктемес бұрын барлығын жою

#XMSG
@loading=Жүктелуде...
#XFLD
@selectSourceObjects=Бастапқы нысандарды таңдау
#XMSG
@exceedLimit=Бір уақытта {0} нысаннан көп нысанды импорттау мүмкін емес. Кемінде {1} нысанның таңдауын алыңыз.
#XFLD
@objects=Нысандар
#XBUT
@ok=OK
#XBUT
@cancel=Бас тарту
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Келесі
#XBUT
btnAddSelection=Таңдауды қосу
#XTOL
@remoteFromSelection=Таңдаудан жою
#XMSG
@searchInForSearchField={0} ішінен іздеу

#XCOL
@name=Техникалық атау
#XCOL
@type=Түрі
#XCOL
@location=Орналасқан жері
#XCOL
@label=Бизнес атау
#XCOL
@status=Күйі

#XFLD
@searchIn=Мынаның ішінен іздеу:
#XBUT
@available=Қолжетімді
#XBUT
@selection=Таңдау

#XFLD
@noSourceSubFolder=Кестелер және көріністер
#XMSG
@alreadyAdded=Диаграммада бұрыннан бар
#XMSG
@askForFilter={0} элементтен көп элемент бар. Элементтер санын нақтылап қысқарту үшін сүзгі жолын енгізіңіз.
#XFLD: success label
lblSuccess=Сәтті
#XFLD: ready label
lblReady=Дайын
#XFLD: failure label
lblFailed=Сәтсіз аяқталды
#XFLD: fetching status label
lblFetchingDetail=Мәліметтер алынуда

#XMSG Place holder text for tree filter control
filterPlaceHolder=Жоғары деңгейлі нысандарды сүзгілеу үшін мәтінді теріңіз
#XMSG Place holder text for server search control
serverSearchPlaceholder=Іздеу үшін мәтін теріңіз және "Енгізу" түмесін басыңыз
#XMSG
@deployObjects={0} нысан импортталуда...
#XMSG
@deployObjectsStatus=Импортталған нысандар саны: {0}. Импортталмаған нысандар саны: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Жергілікті репозитарий браузерін ашу сәтсіз аяқталды.
#XMSG
@openRemoteSourceBrowserError=Бастапқы нысандарды алу сәтсіз аяқталды.
#XMSG
@openRemoteTargetBrowserError=Мақсатты нысандарды алу сәтсіз аяқталды.
#XMSG
@validatingTargetsError=Мақсаттарды тексеру кезінде қате орын алды.
#XMSG
@waitingToImport=Импорттауға дайын

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Нысандардың саны максималды шектен асып кетті. Бір тираждау ағыны үшін ең көбі 500 нысанды таңдаңыз.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Техникалық атау
#XFLD
sourceObjectBusinessName=Бизнес атау
#XFLD
sourceNoColumns=Бағандар саны
#XFLD
containerLbl=Контейнер

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Тираждау ағыны үшін бастапқы қосылымды таңдауыңыз керек.
#XMSG
validationSourceContainerNonExist=Бастапқы қосылым үшін контейнерді таңдауыңыз керек.
#XMSG
validationTargetNonExist=Тираждау ағыны үшін мақсатты қосылымды таңдауыңыз керек.
#XMSG
validationTargetContainerNonExist=Мақсатты қосылым үшін контейнерді таңдауыңыз керек.
#XMSG
validationTruncateDisabledForObjectTitle=Нысанның сақтау орындарына тираждау.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Бұлттық сақтау орнына тираждау әрекеті "Жүктемес бұрын барлығын жою" опциясы орнатылғанда немесе мақсатты нысан мақсатта жоқ болса ғана орындалады.{0}{0} "Жүктемес бұрын барлығын жою" опциясы орнатылмаған нысандар үшін тираждауды әлі де қосу үшін, тираждау ағынын іске қоспас бұрын мақсатты нысанның жүйеде жоқ екеніне көз жеткізіңіз.
#XMSG
validationTaskNonExist=Тираждау ағынында кем дегенде бір тираждау болуы керек.
#XMSG
validationTaskTargetMissing=Сізде {0} бастапқы нысанымен тираждауға арналған мақсат болуы керек.
#XMSG
validationTaskTargetIsSAC=Таңдалған мақсат — SAC артефактісі: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey={0} жергілікті кестесі үшін таңдалған мақсатқа қолдау көрсетілмейді
#XMSG
validationTaskTargetIsNonDwcTableDescription=Бұндай атауға ие нысан мақсатта бұрыннн бар. Дегенмен осы нысанды жергілікті репозитарийге тираждау ағынына арналған мақсатты нысан ретінде пайдалану мүмкін емес, себебі ол жергілікті кесте емес.
#XMSG
validateSourceTargetSystemDifference=Тираждау ағыны үшін әртүрлі бастапқы және мақсатты қосылымды және контейнер тіркесімдерін таңдауыңыз керек.
#XMSG
validateDuplicateSources=Бір немесе бірнеше тираждау ағынында қайталанатын бастапқы нысан атаулары бар: {0}.
#XMSG
validateDuplicateTargets=Бір немесе бірнеше тираждау ағынында қайталанатын мақсатты нысан атаулары бар: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated={0} бастапқы нысан дельта қадағалауға қолдау көрсетпейді, ал {1} мақсатты нысаны қолдау көрсетеді. Тираждауды алып тастауыңыз керек.
#XMSG
validationTaskTargetObjectLoadTypeMismatch={0} мақсатты нысан атауы бар тираждау үшін "Бастапқы және Дельта" жүктеу түрін таңдау керек.
#XMSG
validationAutoRenameTarget=Мақсатты бағандардың атаулары өзгертілді.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Автоматты проекция қосылды және келесі мақсатты бағандардың атаулары мақсатты нысанға тираждауға рұқсат беру үшін өзгертілді:{1}{1} {0} {1}{1}Бұл келесі себептердің біріне байланысты:{1}{1}{2} Қолдау көрсетілмейтін таңбалар{1}{2} Резервтелген префикс
#XMSG
validationAutoRenameTargetDescriptionUpdated=Автоматты проекция қосылды және келесі мақсатты бағандардың атаулары Google BigQuery-ге тираждауға рұқсат беру үшін өзгертілді:{1}{1} {0} {1}{1}Бұл келесі себептердің біріне байланысты:{1}{1}{2} Резервтелген баған атауы{1}{2} Қолдау көрсетілмейтін таңбалар{1}{2} Резервтелген префикс
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Автоматты проекция қосылды және келесі мақсатты бағандардың атаулары Confluent-ке тираждауға рұқсат беру үшін өзгертілді:{1}{1} {0} {1}{1}Бұл келесі себептердің біріне байланысты:{1}{1}{2} Резервтелген баған атауы{1}{2} Қолдау көрсетілмейтін таңбалар{1}{2} Резервтелген префикс
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Автоматты проекция қосылды және келесі мақсатты бағандардың атаулары мақсатты нысанға тираждауға рұқсат беру үшін өзгертілді:{1}{1} {0} {1}{1}Бұл келесі себептердің біріне байланысты:{1}{1}{2} Резервтелген баған атауы{1}{2} Қолдау көрсетілмейтін таңбалар{1}{2} Резервтелген префикс
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Мақсатты нысанның атауы өзгертілді.
#XMSG
autoRenameInfoDesc=Мақсатты нысанның атауы өзгертілді, себебі оның атауында қолдау көрсетілмейтін таңбалар болды. Тек мына таңбаларға қолдау көрсетіледі:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(dot){0}{1}_(underscore){0}{1}-(dash)
#XMSG
validationAutoTargetTypeConversion=Мақсатты дерек түрлері өзгертілді.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Келесі мақсатты бағандар үшін, мақсатты дерек түрлері өзгертілді, себебі Google BigQuery-де бастапқы дерек түрлеріне қолдау көрсетілмейді:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Келесі мақсатты бағандар үшін, мақсатты дерек түрлері өзгертілді, себебі мақсатты қосылымда бастапқы дерек түрлеріне қолдау көрсетілмейді:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Мақсатты баған атауларын қысқартыңыз.
#XMSG
validationMaxCharLengthGBQTargetDescription=Google BigQuery-де баған атаулары ең көбі 300 таңба пайдалана алады. Келесі мақсатты баған атауларын қысқарту үшін проекцияны пайдаланыңыз:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Негізгі кілттер жасалмайды.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=Google BigQuery-де ең көбі 16 бастапқы кілтке қолдау көрсетіледі, бірақ бастапқы нысанда бастапқы кілттердің көбірек саны бар. Бастапқы кілттердің ешқайсысы мақсатты нысанда жасалмайды.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Бір немесе бірнеше бастапқы бағандарда Google BigQuery-де бастапқы кілттер ретінде анықталмайтын дерек түрлері бар. Негізгі кілттердің ешқайсысы мақсатты нысанда жасалмайды.{0}{0}Келесі мақсатты деректер түрлері бастапқы кілт анықталатын Google BigQuery деректер түрлерімен үйлесімді:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Бір немесе бірнеше бағанды бастапқы кілт ретінде анықтаңыз.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Мұны істеу үшін бір немесе бірнеше бағанды бастапқы кілтті пайдаланудың бастапқы схемасы диалогтық терезесі ретінде анықтауыңыз керек.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Бір немесе бірнеше бағанды бастапқы кілт ретінде анықтаңыз.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Бастапқы нысан үшін бастапқы кілт ретінде бастапқы кілт шектеулеріне сәйкес келетін бір немесе бірнеше бағанды анықтау қажет. Ол үшін бастапқы нысан сипаттарындағы "Схеманы теңшеу" бөліміне өтіңіз.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Жарамды максималды бөлік мәнін енгізіңіз.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Максималды бөлік мәні ≥ 1 және ≤ 2147483647 болуға тиіс
#XMSG
validateHDLFNoPKDatasetError=Бір немесе бірнеше бағанды бастапқы кілт ретінде анықтаңыз.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Нысанды тираждау үшін, бір немесе бірнеше мақсатты бағанды бастапқы кілт ретінде анықтауыңыз керек. Мұны орындау үшін проекцияны пайдаланыңыз.
#XMSG
validateHDLFNoPKExistingDatasetError=Бір немесе бірнеше бағанды бастапқы кілт ретінде анықтаңыз.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Деректерді бұрыннан бар мақсатты нысанға тираждау үшін, оның ішінде бстапқы кілт ретінде анықталған бір немесе бірнеше баған болуы керек. {0} Сізде бір немесе бірнеше бағанды бастапқы кілт ретінде анықтау үшін келесідей опциялар бар: {0} {1} Бұрыннан бар мақсатты нысанды өзгерту үшін, жергілікті кесте редакторын пайдаланыңыз. Содан кейін тираждау ағынын қайта жүктеңіз.{0}{1} Тираждау ағынындағы мақсатты нысанның атауын өзгертіңіз. Бұл әрекет сеанс бастала салысымен жаңа нысанды жасайды. Атауын өзгерткеннен кейін, бір немесе бірнеше бағанды бастапқы кілтті проекцияда анықтай аласыз.{0}{1} Нысанды ішінде бір немесе бірнеше баған бастапқы кілт ретінде анықталып қойған бұрыннан бар мақсатты нысанмен салыстырыңыз.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Таңдалған мақсатты нысан репозитарийде бұрыннан бар: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Дельта қадағалау кестесі репозитарийдегі басқа кестелермен пайдаланылуда: {0}. Тираждау ағынын сақтамас бұрын, байланысты дельта қадағалау кестесінің атаулары бірегей екеніне көз жеткізу үшін, осы мақсатты нысандардың атауларын өзгертуіңіз керек.
#XMSG
validateConfluentEmptySchema=Схеманы анықтау
#XMSG
validateConfluentEmptySchemaDescUpdated=Бастапқы кестеде схема жоқ. Схеманы анықтау үшін "Схеманы теңшеу" опциясын таңдаңыз
#XMSG
validationCSVEncoding=CSV кодтауы жарамсыз
#XMSG
validationCSVEncodingDescription=Тапсырманың CSV кодтауы жарамсыз.
#XMSG
validateConfluentEmptySchema=Үйлесімді мақсатты деректер түрін таңдаңыз
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Үйлесімді мақсатты деректер түрін таңдаңыз
#XMSG
globalValidateTargetDataTypeDesc=Баған салыстырулары кезінде қате орын алды. Проекцияға өтіп, барлық бастапқы бағандардың бірегей бағанмен, үйлесімді деректер түрі бар бағанмен салыстырылғанына және барлық анықталған өрнектердің жарамды екеніне көз жеткізіңіз.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Қайталанатын баған атаулары.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Қайталанатын баған атауларына қолдау көрсетілмейді. Оларды түзету үшін, "Проекция" диалогтік терезесін пайдаланыңыз. Келесі мақсатты нысандарда қайталанатын баған атаулары бар: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Қайталанатын баған атаулары.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Қайталанатын баған атауларына қолдау көрсетілмейді. Келесі мақсатты нысандарда қайталанатын баған атаулары бар: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Деректе сәйкессіздіктер болуы мүмкін.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated="Тек дельта" жүктеу түрі соңғы сақтау және келесі сеанс арасында бастапқы көзде жасалған өзгерістерді ескермейді.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Жүктеу түрін "Бастапқы" түріне өзгертіңіз.
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Бастапқы кілті жоқ ABAP негізіндегі нысандарды тираждау әрекетін "Тек бастапқы" жүктеу түрі үшін ғана орындауға болады.
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Дельта қадағалауды өшіріңіз.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=ABAP бастапқы қосылым түрін пайдаланып бастапқы кілті жоқ нысанды тираждау үшін алдымен осы кесте үшін дельта қадағалауды өшіру керек.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Мақсатты нысанды өзгертіңіз.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Мақсатты нысанды пайдалану мүмкін емес, себебі дельта қадағалау қосылған. Мақсатты нысанның атын өзгертуге, содан кейін жаңа (атауы өзгертілген) нысан үшін дельта қадағалауды өшіруге немесе бастапқы нысанды дельта қадағалау мүмкіндігі өшірілген мақсатты нысанмен салыстыруға болады.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Мақсатты нысанды өзгертіңіз.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Мақсатты нысанды пайдалану мүмкін емес, себебі оның қажетті техникалық бағаны (__load_package_id) жоқ. Әлі жоқ атауды пайдаланып мақсатты нысанның атын өзгертуге болады. Содан кейін жүйе бастапқы нысанмен бірдей анықтамаға ие және техникалық бағанды қамтитын жаңа нысанды жасайды. Немесе мақсатты нысанды қажетті техникалық бағаны (__load_package_id) бар бұрыннан бар нысанмен салыстыруға болады.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Мақсатты нысанды өзгертіңіз.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Мақсатты нысанды пайдалану мүмкін емес, себебі оның қажетті техникалық бағаны (__load_record_id) жоқ. Әлі жоқ атауды пайдаланып мақсатты нысанның атын өзгертуге болады. Содан кейін жүйе бастапқы нысанмен бірдей анықтамаға ие және техникалық бағанды қамтитын жаңа нысанды жасайды. Немесе мақсатты нысанды қажетті техникалық бағаны (__load_record_id) бар бұрыннан бар нысанмен салыстыруға болады.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Мақсатты нысанды өзгертіңіз.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Мақсатты нысанды пайдалану мүмкін емес, себебі оның техникалық бағанының (__load_record_id) дерек түрі "string(44)" емес. Әлі жоқ атауды пайдаланып мақсатты нысанның атын өзгертуге болады. Содан кейін жүйе бастапқы нысанмен бірдей анықтамаға және сәйкес деректер түріне ие жаңа нысанды жасайды. Немесе мақсатты нысанды дерек түрі дұрыс әрі қажетті техникалық бағаны (__load_record_id) бар бұрыннан бар нысанмен салыстыруға болады.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Мақсатты нысанды өзгертіңіз.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Мақсатты нысанды пайдалану мүмкін емес, себебі оның бастапқы кілті бар, бірақ бастапқы нысанда ол жоқ. Әлі жоқ атауды пайдаланып мақсатты нысанның атын өзгертуге болады. Содан кейін жүйе бастапқы нысанмен бірдей анықтамаға ие және тиісінше бастапқы кілті жоқ жаңа нысанды жасайды. Немесе мақсатты нысанды қажетті техникалық бағаны (__load_package_id) бар және бастапқы кілті жоқ бұрыннан бар нысанмен салыстыруға болады.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Мақсатты нысанды өзгертіңіз.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Мақсатты нысанды пайдалану мүмкін емес, себебі оның бастапқы кілті бар, бірақ бастапқы нысанда ол жоқ. Әлі жоқ атауды пайдаланып мақсатты нысанның атын өзгертуге болады. Содан кейін жүйе бастапқы нысанмен бірдей анықтамаға ие және тиісінше бастапқы кілті жоқ жаңа нысанды жасайды. Немесе мақсатты нысанды қажетті техникалық бағаны (__load_record_id) бар және бастапқы кілті жоқ бұрыннан бар нысанмен салыстыруға болады.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Мақсатты нысанды өзгертіңіз.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Мақсатты нысанды пайдалану мүмкін емес, себебі оның техникалық бағанының (__load_package_id) дерек түрі "binary(>=256)" емес. Әлі жоқ атауды пайдаланып мақсатты нысанның атын өзгертуге болады. Содан кейін жүйе бастапқы нысанмен бірдей анықтамаға және сәйкес деректер түріне ие жаңа нысанды жасайды. Немесе мақсатты нысанды дерек түрі дұрыс әрі қажетті техникалық бағаны (__load_package_id) бар бұрыннан бар нысанмен салыстыруға болады.
#XMSG
validationAutoRenameTargetDPID=Мақсатты бағандардың атаулары өзгертілді.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Бастапқы нысанды жойыңыз.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Бастапқы нысанда осы мәнмәтінде қолдау көрсетілмейтін негізгі баған жоқ.
#XMSG
validationAutoRenameTargetDPIDDescription=Автоматты проекция қосылды және келесі мақсатты бағандардың атаулары ABAP бастапқы қосылымынан кілттерсіз тираждауға рұқсат беру үшін өзгертілді:{1}{1} {0} {1}{1}Бұл келесі себептердің біріне байланысты:{1}{1}{2} Резервтелген баған атауы{1}{2} Қолдау көрсетілмейтін таңбалар{1}{2} Резервтелген префикс
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle={0} жүйесіне тираждау.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Мақсаты {0} болатын тираждау ағындарын сақтау және қолданысқа енгізу қазіргі уақытта мүмкін емес, себебі біз бұл функцияға техникалық қызмет көрсету жұмыстарын орындап жатырмыз.
#XMSG
TargetColumnSkippedLTF=Мақсатты баған өткізіп жіберілді.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Мақсатты баған қолдау көрсетілмейтін дерек түріне байланысты өткізіп жіберілді. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Бастапқы кілт ретіндегі уақыт бағаны.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Бастапқы нысанда осы мәнмәтінде қолдау көрсетілмейтін бастапқы кілт ретіндегі уақыт бағаны бар.
#XMSG
validateNoPKInLTFTarget=Бастапқы кілт жоқ.
#XMSG
validateNoPKInLTFTargetDescription=Мақсатты нысаннан осы мәнмәтінде қолдау көрсетілмейтін бастапқы кілт анықталмады. 
#XMSG
validateABAPClusterTableLTF=ABAP кластерлік кестесі.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Бастапқы нысан ABAP кластерлік кестесі болып табылады, ал оған осы мәнмәтінде қолдау көрсетілмейді.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Әлі ешқандай дерек қоспаған сияқтысыз.
#YINS
welcomeText2=Тираждау ағынын бастау үшін, сол жақтан қосылымды және бастапқы нысанды таңдаңыз.

#XBUT
wizStep1=Бастапқы қосылымды таңдау
#XBUT
wizStep2=Бастапқы контейнерді таңдау
#XBUT
wizStep3=Бастапқы нысандарды қосу

#XMSG
limitDataset=Нысандардың рұқсат етілген максималды санына жетті. Жаңасын қосу үшін бұрыннан бар нысандарды жойыңыз немесе жаңа тираждау ағынын жасаңыз.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=SAP-қа қатысты емес мақсатты қосылымға тираждау ағынын бастау мүмкін емес, себебі осы ай үшін қолжетімді шығыс көлем жоқ.
#XMSG
premiumOutBoundRFAdminWarningMsg=Әкімші шығыс көлемін осы айда қолжетімді ете отырып, осы тенант үшін премиум шығыс блоктарын көбейте алады.
#XMSG
messageForToastForDPIDColumn2={0} нысанға арналған мақсатты кестеге жаңа баған қосылды. Ол бастапқы кілті жоқ ABAP негізіндегі бастапқы нысандарға ие қосылымда қайталанатын жазбаларды өңдеу үшін қажет.
#XMSG
PremiumInboundWarningMessage=Тираждау ағындарының санына және тираждалатын деректер көлеміне байланысты {0}деректерді {1} арқылы тираждау үшін қажет SAP HANA ресурстары тенант үшін қолжетімді мүмкіндіктен асып кетуі мүмкін.
#XMSG
PremiumInboundWarningMsg=Тираждау ағындарының санына және тираждалатын деректер көлеміне байланысты {0}деректерді "{1}" арқылы тираждау үшін қажет SAP HANA ресурстары тенант үшін қолжетімді мүмкіндіктен асып кетуі мүмкін.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Проекция атауын енгізіңіз.
#XMSG
emptyTargetColumn=Мақсатты баған атауын енгізіңіз.
#XMSG
emptyTargetColumnBusinessName=Мақсатты бағанның бизнес атауын енгізіңіз.
#XMSG
invalidTransformName=Проекция атауын енгізіңіз.
#XMSG
uniqueColumnName=Мақсатты баған атауын өзгертіңіз.
#XMSG
copySourceColumnLbl=Бағандарды бастапқы нысаннан көшіру
#XMSG
renameWarning=Мақсатты кестенің атауын өзгерту кезінде бірегей атауды таңдағаныңызға көз жеткізіңіз. Атауы жаңа кесте кеңістікте бұрыннан бар болса, онда ол сол кестенің анықтамасын пайдаланады.

#XMSG
uniqueColumnBusinessName=Мақсатты бағанның бизнес атауын өзгертіңіз.
#XMSG
uniqueSourceMapping=Басқа бастапқы бағанды таңдаңыз.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc={0} бастапқы бағанын келесі мақсатты бағандар қолдануда:{1}{1}{2}{1}{1} Осы мақсатты баған немесе басқа мақсатты бағандар үшін проекцияны сақтау үшін қолданыста жоқ бастапқы бағанды таңдаңыз.
#XMSG
uniqueColumnNameDescription=Сіз енгізген мақсатты баған атауы бұрыннан бар. Проекцияны сақтай алу үшін, бірегей баған атауын енгізуіңіз керек.
#XMSG
uniqueColumnBusinessNameDesc=Мақсатты бағанның бизнес атауы бұрыннан бар. Проекцияны сақтау үшін, бірегей бағанның бизнес атауын енгізуіңіз керек.
#XMSG
emptySource=Бастапқы бағанды таңдаңыз немесе тұрақты мәнді енгізіңіз.
#XMSG
emptySourceDescription=Жарамды салыстыру енгізілімін жасау үшін, бастапқы бағанды таңдауыңыз немесе тұрақты мәнді енгізуіңіз керек.
#XMSG
emptyExpression=Салыстыруды анықтаңыз.
#XMSG
emptyExpressionDescription1=Мақсатты бағанды салыстырғыңыз келген бастапқы бағанды таңдаңыз немесе {0} Функциялар / Тұрақты мәндер {1} бағанында құсбелгі қойыңыз. {2} {2} Функциялар мақсатты дерек түріне сәйкес автоматты түрде енгізіледі. Тұрақты мәндерді қолмен енгізуге болады.
#XMSG
numberExpressionErr=Санды енгізіңіз.
#XMSG
numberExpressionErrDescription=Сандық дерек түрін таңдадыңыз. Бұл дегеніміз Бұл тек сандарды және қажет болса, ондық бөлшек үтірін енгізуге болатынын білдіреді. Жалғыз тырнақшаларды қолданбаңыз.
#XMSG
invalidLength=Жарамды ұзындық мәнін енгізіңіз.
#XMSG
invalidLengthDescription=Дерек түрінің ұзындығы бастапқы бағанның ұзындығымен бірдей немесе одан ұзын болуы керек және 1 және 5000 аралығында болуы керек. 
#XMSG
invalidMappedLength=Жарамды ұзындық мәнін енгізіңіз.
#XMSG
invalidMappedLengthDescription=Дерек түрінің ұзындығы {0} бастапқы бағанының ұзындығымен бірдей немесе одан ұзын болуы керек және 1 және 5000 аралығында болуы керек. 
#XMSG
invalidPrecision=Жарамды дәлдік мәнін енгізіңіз.
#XMSG
invalidPrecisionDescription=Дәлдік мәні цифрлардың жалпы санын анықтайды. Шкала ондық бөлшек үтірінен кейінгі сандар санын анықтайды және 0 мен дәлдік мәні арасында болуы мүмкін.{0}{0} Мысалдары: {0}{1} Дәлдік 6, шкала 2 болса, ол 1234,56 сияқты сандарға сәйкес келеді.{0}{1} Дәлдік 6, шкала 6 болса, ол 0,123546 сияқты сандарға сәйкес келеді.{0} {0} Мақсатты нысанның дәлдігі мен шкаласы бастапқы нысанның дәлдігі мен шкаласымен үйлесімді болуы керек, осылайша бастапқы нысаннан алынған барлық сандар мақсатты өріске орналастырылады. Мысалы, мақсатты нысанда дәлдік 6 және шкала 2 болса (сондықтан ондық бөлшек үтірінің алдындағы 0-ден басқа сандар), мақсатты нысанда дәлдік 6 және шкала 6 болмайды.
#XMSG
invalidPrimaryKey=Кем дегенде бір бастапқы кілтті енгізіңіз.
#XMSG
invalidPrimaryKeyDescription=Осы схема үшін бастапқы кілт анықталмады.
#XMSG
invalidMappedPrecision=Жарамды дәлдік мәнін енгізіңіз.
#XMSG
invalidMappedPrecisionDescription1=Дәлдік мәні цифрлардың жалпы санын анықтайды. Шкала ондық бөлшектен кейінгі сандар санын анықтайды және 0 мен дәлдік мәні арасында болуы мүмкін.{0}{0} Мысалдары: {0}{1} Дәлдік 6, шкала 2 болса, ол 1234,56 сияқты сандарға сәйкес келеді.{0}{1} Дәлдік 6, шкала 6 болса, ол 0,123546 сияқты сандарға сәйкес келеді.{0}{0}Дерек түрінің дәлдігі бастапқы нысанның ({2}) дәлдігіне тең немесе одан үлкен болуы керек.
#XMSG
invalidScale=Жарамды шкала мәнін енгізіңіз.
#XMSG
invalidScaleDescription=Дәлдік мәні цифрлардың жалпы санын анықтайды. Шкала ондық бөлшектен кейінгі сандар санын анықтайды және 0 мен дәлдік мәні арасында болуы мүмкін.{0}{0} Мысалдары: {0}{1} Дәлдік 6, шкала 2 болса, ол 1234,56 сияқты сандарға сәйкес келеді.{0}{1} Дәлдік 6, шкала 6 болса, ол 0,123546 сияқты сандарға сәйкес келеді.{0} {0} Мақсатты нысанның дәлдігі мен шкаласы бастапқы нысанның дәлдігі мен шкаласымен үйлесімді болуы керек, осылайша бастапқы нысаннан алынған барлық сандар мақсатты өріске орналастырылады. Мысалы, мақсатты нысанда дәлдік 6 және шкала 2 болса (сондықтан ондық бөлшек үтірінің алдындағы 0-ден басқа сандар), мақсатты нысанда дәлдік 6 және шкала 6 болмайды.
#XMSG
invalidMappedScale=Жарамды шкала мәнін енгізіңіз.
#XMSG
invalidMappedScaleDescription1=Дәлдік мәні цифрлардың жалпы санын анықтайды. Шкала ондық бөлшектен кейінгі сандар санын анықтайды және 0 мен дәлдік мәні арасында болуы мүмкін.{0}{0} Мысалдары: {0}{1} Дәлдік 6, шкала 2 болса, ол 1234,56 сияқты сандарға сәйкес келеді.{0}{1} Дәлдік 6, шкала 6 болса, ол 0,123546 сияқты сандарға сәйкес келеді.{0}{0}Дерек түрінің шкаласы бастапқы нысанның ({2}) шкаласына тең немесе одан үлкен болуы керек.
#XMSG
nonCompatibleDataType=Үйлесімді мақсатты деректер түрін таңдаңыз.
#XMSG
nonCompatibleDataTypeDescription1=Мұнда сіз көрсеткен деректер түрі бастапқы деректер түрімен ({0}) үйлесімді болуы керек. {1}{1} Мысалы: Егер бастапқы бағанда STRING деректер түрі болса және әріптерді қамтыса, мақсатты нысан үшін DECIMAL деректер түрін пайдалана алмайсыз.
#XMSG
invalidColumnCount=Бастапқы бағанды таңдаңыз.
#XMSG
ObjectStoreInvalidScaleORPrecision=Дәлдік және шкала үшін жарамды мәнді енгізіңіз.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=Бірінші мән цифрлардың жалпы санын анықтайтын дәлдік болып табылады. Екінші мән ондық бөлшек үтірінен кейінгі сандарды анықтайтын шкала болып табылады. Бастапқы шкала мәнінен үлкен мақсатты шкала мәнін енгізіңіз және енгізілген мақсатты шкала мен дәлдік мәні арасындағы айырмашылық бастапқы шкала мен дәлдік мәні арасындағы айырмашылықтан үлкенірек екеніне көз жеткізіңіз.
#XMSG
InvalidPrecisionORScale=Дәлдік және шкала үшін жарамды мәнді енгізіңіз.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=Бірінші мән цифрлардың жалпы санын анықтайтын дәлдік болып табылады. Екінші мән ондық бөлшек үтірінен кейінгі сандарды анықтайтын шкала болып табылады.{0}{0}Google BigQuery-де бастапқы деректер түріне қолдау көрсетілмегендіктен, ол DECIMAL мақсатты деректер түріне түрлендіріледі. Бұл жағдайда дәлдікті тек 38 бен 76, ал шкаланы 9 бен 38 арасында анықтауға болады. Сонымен қатар ондық бөлшек үтірінің алдындағы сандарды көрсететін дәлдік минус шкаласының нәтижесі 29 бен 38 арасында болуы керек.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=Бірінші мән цифрлардың жалпы санын анықтайтын дәлдік болып табылады. Екінші мән ондық бөлшек үтірінен кейінгі сандарды анықтайтын шкала болып табылады.{0}{0}Google BigQuery-де бастапқы деректер түріне қолдау көрсетілмегендіктен, ол DECIMAL мақсатты деректер түріне түрлендіріледі. Бұл жағдайда дәлдік 20 немесе одан жоғары болуы керек. Сонымен қатар ондық бөлшек үтірінің алдындағы сандарды көрсететін дәлдік минус шкаласының нәтижесі 20 немесе одан үлкен болуы керек.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=Бірінші мән цифрлардың жалпы санын анықтайтын дәлдік болып табылады. Екінші мән ондық бөлшек үтірінен кейінгі сандарды анықтайтын шкала болып табылады.{0}{0}Мақсатты нысанда бастапқы деректер түріне қолдау көрсетілмегендіктен, ол DECIMAL мақсатты деректер түріне түрлендіріледі. Бұл жағдайда дәлдік 1-ден үлкен немесе оған тең және 38-ден кіші немесе оған тең кез келген санмен, ал шкала дәлдіктен кіші немесе оған тең болуы керек.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=Бірінші мән цифрлардың жалпы санын анықтайтын дәлдік болып табылады. Екінші мән ондық бөлшек үтірінен кейінгі сандарды анықтайтын шкала болып табылады.{0}{0}Мақсатты нысанда бастапқы деректер түріне қолдау көрсетілмегендіктен, ол DECIMAL мақсатты деректер түріне түрлендіріледі. Бұл жағдайда дәлдік 20 немесе одан жоғары болуы керек. Сонымен қатар ондық бөлшек үтірінің алдындағы сандарды көрсететін дәлдік минус шкаласының нәтижесі 20 немесе одан үлкен болуы керек.
#XMSG
invalidColumnCountDescription=Жарамды салыстыру енгізілімін жасау үшін, бастапқы бағанды таңдауыңыз немесе тұрақты мәнді енгізуіңіз керек.
#XMSG
duplicateColumns=Мақсатты баған атауын өзгертіңіз.
#XMSG
duplicateGBQCDCColumnsDesc=Мақсатты баған атауы Google BigQuery-де резервке сақталған. Проекцияны сақтау үшін оның атын өзгерту керек.
#XMSG
duplicateConfluentCDCColumnsDesc=Мақсатты баған атауы Confluent-те резервке сақталған. Проекцияны сақтау үшін оның атын өзгерту керек.
#XMSG
duplicateSignavioCDCColumnsDesc=Мақсатты баған атауы SAP Signavio-да резервке сақталған. Проекцияны сақтау үшін оның атын өзгерту керек.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Мақсатты баған атауы MS OneLake-те резервке сақталған. Проекцияны сақтау үшін оның атын өзгерту керек.
#XMSG
duplicateSFTPCDCColumnsDesc=Мақсатты баған атауы SFTP-де резервке сақталған. Проекцияны сақтау үшін оның атын өзгерту керек.
#XMSG
GBQTargetNameWithPrefixUpdated1=Мақсатты баған атауында Google BigQuery-де резервке сақталған префикс бар. Проекцияны сақтау үшін оның атын өзгерту керек. {0}{0}Мақсатты баған атауы келесі жолдармен басталмауы керек:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Мақсатты баған атауын қысқартыңыз.
#XMSG
GBQtargetMaxLengthDesc=Google BigQuery-де баған атауы ең көбі 300 таңба пайдалана алады. Проекцияны сақтау үшін, мақсатты баған атауын қысқартыңыз.
#XMSG
invalidMappedScalePrecision=Мақсатқа арналған дәлдік пен шкала мақсатты нысанның дәлдігімен және шкаласымен үйлесімді болуы керек, осылайша бастапқы нысандағы барлық сандар мақсатты өріске сәйкес келеді.
#XMSG
invalidMappedScalePrecisionShortText=Жарамды дәлдік және шкала мәнін енгізіңіз.
#XMSG
validationIncompatiblePKTypeDescProjection3=Бір немесе бірнеше бастапқы бағандарда Google BigQuery-де бастапқы кілттер ретінде анықталмайтын дерек түрлері бар. Негізгі кілттердің ешқайсысы мақсатты нысанда жасалмайды.{0}{0}Келесі мақсатты деректер түрлері бастапқы кілт анықталатын Google BigQuery деректер түрлерімен үйлесімді:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=__message_id бағанындағы құсбелгіні алып тастаңыз.
#XMSG
uncheckColumnMessageIdDesc=Баған: бастапқы кілт
#XMSG
validationOpCodeInsert="Енгізу" үшін мән енгізуіңіз қажет.
#XMSG
recommendDifferentPrimaryKey=Тармақ деңгейінде басқа негізгі кілтті таңдауды ұсынамыз.
#XMSG
recommendDifferentPrimaryKeyDesc=Операция коды әлдеқашан анықталған кезде, бағанның қайталануы сияқты мәселелерді болдырмау үшін жиым индексі мен тармақтар үшін әртүрлі негізгі кілттерді таңдау ұсынылады.
#XMSG
selectPrimaryKeyItemLevel=Тақырып деңгейі үшін де, тармақ деңгейі үшін де кемінде бір негізгі кілтті таңдау керек.
#XMSG
selectPrimaryKeyItemLevelDesc=Жиым немесе карта кеңейтілген кезде екі негізгі кілтті таңдау керек: біреуін тақырып деңгейінде және екіншісін тармақ деңгейінде.
#XMSG
invalidMapKey=Тақырып деңгейінде кемінде бір негізгі кілтті таңдау керек.
#XMSG
invalidMapKeyDesc=Жиым немесе карта кеңейтілген кезде тақырып деңгейінде негізгі кілтті таңдау керек.
#XFLD
txtSearchFields=Мақсатты бағандарды іздеу
#XFLD
txtName=Атауы
#XMSG
txtSourceColValidation=Бір немесе бірнеше бастапқы бағанға қолдау көрсетілмейді:
#XMSG
txtMappingCount=Салыстырулар ({0})
#XMSG
schema=Схема
#XMSG
sourceColumn=Бастапқы бағандар
#XMSG
warningSourceSchema=Схемаға жасалған кез келген өзгеріс проекция диалогтік терезесіндегі салыстыруларға әсер етеді.
#XCOL
txtTargetColName=Мақсатты баған (техникалық атау)
#XCOL
txtDataType=Мақсатты дерек түрі
#XCOL
txtSourceDataType=Бастапқы дерек тұрі
#XCOL
srcColName=Мақсатты баған (техникалық атау)
#XCOL
precision=Дәлдік
#XCOL
scale=Шкала
#XCOL
functionsOrConstants=Функциялар / Тұрақты мәндер
#XCOL
txtTargetColBusinessName=Мақсатты баған (Бизнес атау)
#XCOL
prKey=Бастапқы кілт
#XCOL
txtProperties=Сипаттар
#XBUT
txtOK=Сақтау
#XBUT
txtCancel=Бас тарту
#XBUT
txtRemove=Жою
#XFLD
txtDesc=Сипаттама
#XMSG
rftdMapping=Салыстыру
#XFLD
@lblColumnDataType=Дерек түрі
#XFLD
@lblColumnTechnicalName=Техникалық атау
#XBUT
txtAutomap=Автоматты салыстыру
#XBUT
txtUp=Жоғары
#XBUT
txtDown=Төмен

#XTOL
txtTransformationHeader=Проекция
#XTOL
editTransformation=Өңдеу
#XTOL
primaryKeyToolip=Кілт


#XMSG
rftdFilter=Сүзгі
#XMSG
rftdFilterColumnCount=Бастапқы: {0} ({1})
#XTOL
rftdFilterColSearch=Іздеу
#XMSG
rftdFilterColNoData=Көрсетілетін бағандар жоқ
#XMSG
rftdFilteredColNoExps=Сүзгі өрнектері жоқ
#XMSG
rftdFilterSelectedColTxt=Сүзгі қосу:
#XMSG
rftdFilterTxt=Сүзгі қолжетімді:
#XBUT
rftdFilterSelectedAddColExp=Өрнек қосу
#YINS
rftdFilterNoSelectedCol=Сүзгі қосу үшін бағанды таңдаңыз.
#XMSG
rftdFilterExp=Сүзгі өрнегі
#XMSG
rftdFilterNotAllowedColumn=Осы баған үшін сүзгілер қосуға қолдау көрсетілмейді.
#XMSG
rftdFilterNotAllowedHead=Қолдау көрсетілмейтін баған
#XMSG
rftdFilterNoExp=Сүзгі анықталмады
#XTOL
rftdfilteredTt=Сүзгіленген
#XTOL
rftdremoveexpTt=Сүзгі өрнегін жою
#XTOL
validationMessageTt=Тексеру туралы хабарлар
#XTOL
rftdFilterDateInp=Күн таңдаңыз
#XTOL
rftdFilterDateTimeInp=Күн мен уақытты таңдаңыз
#XTOL
rftdFilterTimeInp=Уақытты таңдаңыз
#XTOL
rftdFilterInp=Мән енгізіңіз
#XMSG
rftdFilterValidateEmptyMsg={1} бағанындағы {0} сүзгі өрнегі бос
#XMSG
rftdFilterValidateInvalidNumericMsg={1} бағанындағы {0} сүзгі өрнегінде жарамсыз сандық мәндер бар
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Сүзгі өрнегінде жарамды сандық мәндер бар
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Мақсатты нысан схемасы өзгерген болса, өзгертулерді бейімдеу үшін негізгі бетте "Бұрыннан бар мақсатты нысанмен салыстыру" функциясын пайдаланыңыз және мақсатты нысанды оның бастапқы нысанымен қайта салыстырыңыз.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Егер мақсатты кесте бұрыннан бар болса және салыстыру схема өзгерісін қамтыса, тираждау ағынын қолданысқа енгізбес бұрын мақсатты кестені сәйкесінше өзгерту керек.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Егер салыстыру схема өзгерісін қамтыса, тираждау ағынын қолданысқа енгізбес бұрын мақсатты кестені сәйкесінше өзгерту керек.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Келесі қолдау көрсетілмейтін бағандар бастапқы анықтамадан өткізіп жіберілді: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Келесі қолдау көрсетілмейтін бағандар мақсатты анықтамадан өткізіп жіберілді: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Келесі нысандарға қолдау көрсетілмейді, себебі олар тұтыну үшін ашылған: {0} {1} {0} {0} Тираждау ағынында кестелерді пайдалану үшін, семантикалық пайдалану (кесте параметрлерінде) {2}Аналитикалық деректер жиыны{2} мәніне орнатылмауы керек.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Мақсатты нысанды пайдалану мүмкін емес, себебі ол тұтыну үшін ашылған: {0} {0} Тираждау ағынында кестені пайдалану үшін, семантикалық пайдалану (кесте параметрлерінде) {1}Аналитикалық деректер жиыны{1} мәніне орнатылмауы керек.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Бұндай атауға ие мақсатты нысан қолданыста бар. Дегенмен оны пайдалану мүмкін емес, себебі ол тұтыну үшін ашылған: {0} {0} Тираждау ағынында кестені пайдалану үшін, семантикалық пайдалану (кесте параметрлерінде) {1}Аналитикалық деректер жиыны{1} мәніне орнатылмауы керек.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Бұндай атауға ие нысан мақсатта бұрыннан бар. {0}Дегенмен осы нысанды жергілікті репозитарийге тираждау ағынына арналған мақсатты нысан ретінде пайдалану мүмкін емес, себебі ол жергілікті кесте емес.
#XMSG:
targetAutoRenameUpdated=Мақсатты бағанның атауы өзгертілді.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Мақсатты бағанның атауы Google BigQuery-де тираждауға рұқсат беру үшін өзгертілді. Бұл келесі себептердің біріне байланысты:{0} {1}{2}Резервтелген баған атауы{3}{2}Қолдау көрсетілмейтін таңбалар{3}{2}Резервтелген префикс{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Мақсатты бағанның атауы Confluent-те тираждауға рұқсат беру үшін өзгертілді. Бұл келесі себептердің біріне байланысты:{0} {1}{2}Резервтелген баған атауы{3}{2}Қолдау көрсетілмейтін таңбалар{3}{2}Резервтелген префикс{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Мақсатты бағанның атауы мақсатты нысанға тираждауға рұқсат беру үшін өзгертілді. Бұл келесі себептердің біріне байланысты:{0} {1}{2}Қолдау көрсетілмейтін таңбалар{3}{2}Резервтелген префикс{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Мақсатты бағанның атауы мақсатты қосылымға тираждауға рұқсат беру үшін өзгертілді. Бұл келесі себептердің біріне байланысты:{0} {1}{2}Резервтелген баған атауы{3}{2}Қолдау көрсетілмейтін таңбалар{3}{2}Резервтелген префикс{3}{4}
#XMSG:
targetAutoDataType=Мақсатты дерек түрі өзгертілді.
#XMSG:
targetAutoDataTypeDesc=Мақсатты дерек түрі {0} болып өзгертілді, себебі бастапқы дерек түріне Google BigQuery-де қолдау көрсетілмейді.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Мақсатты дерек түрі {0} болып өзгертілді, себебі бастапқы дерек түріне мақсатты қосылымда қолдау көрсетілмейді.
#XMSG
projectionGBQUnableToCreateKey=Негізгі кілттер жасалмайды.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=Google BigQuery-де ең көбі 16 негізгі кілтке қолдау көрсетіледі, бірақ бастапқы нысанда негізгі кілттердің көбірек саны бар. Негізгі кілттердің ешқайсысы мақсатты нысанда жасалмайды.
#XMSG
HDLFNoKeyError=Бір немесе бірнеше бағанды бастапқы кілт ретінде анықтаңыз.
#XMSG
HDLFNoKeyErrorDescription=Нысанды тираждау үшін, бір немесе бірнеше бағанды бастапқы кілт ретінде анықтауыңыз керек.
#XMSG
HDLFNoKeyErrorExistingTarget=Бір немесе бірнеше бағанды бастапқы кілт ретінде анықтаңыз.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Деректерді бұрыннан бар мақсатты нысанға тираждау үшін, оның ішінде бстапқы кілт ретінде анықталған бір немесе бірнеше баған болуы керек. {0}{0} Сізде бір немесе бірнеше бағанды бастапқы кілт ретінде анықтау үшін келесідей опциялар бар: {0}{0}{1} Бұрыннан бар мақсатты нысанды өзгерту үшін, жергілікті кесте редакторын пайдаланыңыз. Содан кейін тираждау ағынын қайта жүктеңіз.{0}{0}{1} Тираждау ағынындағы мақсатты нысанның атауын өзгертіңіз. Бұл әрекет сеанс бастала салысымен жаңа нысанды жасайды. Атауын өзгерткеннен кейін, бір немесе бірнеше бағанды бастапқы кілтті проекцияда анықтай аласыз.{0}{0}{1} Нысанды ішінде бір немесе бірнеше баған бастапқы кілт ретінде анықталып қойған бұрыннан бар мақсатты нысанмен салыстырыңыз.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Бастапқы кілт өзгертілді.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=Бастапқы нысанмен салыстырғанда сіз мақсатты нысан үшін бастапқы кілт ретінде әртүрлі бағандарды анықтадыңыз. Деректерді кейінірек тираждау кезінде ықтимал деректердің бүлінуін болдырмау үшін бұл бағандар барлық жолдарды бірегей түрде анықтайтынына көз жеткізіңіз. {0} {0} Бастапқы нысанда келесі бағандар бастапқы кілт ретінде анықталады: {0} {1}
#XMSG
duplicateDPIDColumns=Мақсатты баған атауын өзгертіңіз.
#XMSG
duplicateDPIDDColumnsDesc1=Бұл мақсатты баған атауы техникалық баған үшін резервтелген. Проекцияны сақтау үшін басқа атау енгізіңіз.
#XMSG:
targetAutoRenameDPID=Мақсатты бағанның атауы өзгертілді.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Мақсатты бағанның атауы  ABAP бастапқы қосылымынан кілттерсіз тираждауға рұқсат беру үшін өзгертілді. Бұл келесі себептердің біріне байланысты:{0} {1}{2}Резервтелген баған атауы{3}{2}Қолдау көрсетілмейтін таңбалар{3}{2}Резервтелген префикс{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle={0} мақсатты параметрлері
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle={0} бастапқы параметрлері
#XBUT
connectionSettingSave=Сақтау
#XBUT
connectionSettingCancel=Бас тарту
#XBUT: Button to keep the object level settings
txtKeep=Сақтау
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Қайта жазу
#XFLD
targetConnectionThreadlimit=Мақсатты жүктеуге арналған мақсатты тред шегі (1-100)
#XFLD
connectionThreadLimit=Бастапқы жүктеуге арналған мақсатты тред шегі (1-100)
#XFLD
maxConnection=Тираждау тредінің шегі (1-100)
#XFLD
kafkaNumberOfPartitions=Бөліктер саны
#XFLD
kafkaReplicationFactor=Тираждау коэффициенті
#XFLD
kafkaMessageEncoder=Хабар кодтаушысы
#XFLD
kafkaMessageCompression=Хабарды сығу
#XFLD
fileGroupDeltaFilesBy=Дельтаны топтау шарты:
#XFLD
fileFormat=Файл түрі
#XFLD
csvEncoding=CSV кодтауы
#XFLD
abapExitLbl=ABAP қосылымынан шығу
#XFLD
deltaPartition=Дельта жүктеу түрлері үшін нысан тредтерінің саны (1-10)
#XFLD
clamping_Data=Деректерді қысқарту кезіндегі қате
#XFLD
fail_On_Incompatible=Үйлесімсіз деректер бойынша қате
#XFLD
maxPartitionInput=Бөліктердің максималды саны
#XFLD
max_Partition=Бөліктердің максималды санын анықтау
#XFLD
include_SubFolder=Ішкі қалталарды қосу
#XFLD
fileGlobalPattern=Файл атауына арналған глобалдық үлгі
#XFLD
fileCompression=Файлды сығу
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Файл бөлгіші
#XFLD
fileIsHeaderIncluded=Файл тақырыбы
#XFLD
fileOrient=Бағдар
#XFLD
gbqWriteMode=Жазу режимі
#XFLD
suppressDuplicate=Көшірмелерді жасыру
#XFLD
apacheSpark=Apache Spark үйлесімділігін қосу
#XFLD
clampingDatatypeCb=Өзгермелі үтірі бар Clamp Decimal дерек түрлері
#XFLD
overwriteDatasetSetting=Мақсатты параметрлерді нысан деңгейінде қайта жазу
#XFLD
overwriteSourceDatasetSetting=Бастапқы параметрлерді нысан деңгейінде қайта жазу
#XMSG
kafkaInvalidConnectionSetting={0} және {1} арасындағы санды енгізіңіз
#XMSG
MinReplicationThreadErrorMsg=Мынадан үлкен санды енгізіңіз: {0}
#XMSG
MaxReplicationThreadErrorMsg=Мынадан кіші санды енгізіңіз: {0}
#XMSG
DeltaThreadErrorMsg=1 және 10 арасындағы мәнді енгізіңіз.
#XMSG
MaxPartitionErrorMsg=1 <= x <= 2147483647 арасындағы мәнді енгізіңіз. Әдепкі мән — 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal={0} және {1} аралығындағы бүтін санды енгізіңіз.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Брокердің тираждау коэффициентін пайдалану
#XFLD
serializationFormat=Сериалау пішімі
#XFLD
compressionType=Сығу түрі
#XFLD
schemaRegistry=Схема тіркелімін пайдалану
#XFLD
subjectNameStrat=Субъект аты стратегиясы
#XFLD
compatibilityType=Үйлесімділік түрі
#XFLD
confluentTopicName=Бөлім аты
#XFLD
confluentRecordName=Жазба аты
#XFLD
confluentSubjectNamePreview=Субъект атын алдын ала көру
#XMSG
serializationChangeToastMsgUpdated2=Схема тіркелімі іске қосылмағандықтан, сериалау пішімі JSON болып өзгертілді. Сериалау пішімін AVRO-ға қайта өзгерту үшін алдымен схема тіркелімін қосу керек.
#XBUT
confluentTopicNameInfo=Тақырып атауы әрқашан мақсатты нысан атауына негізделеді. Оны мақсатты нысанның атауын өзгерту арқылы өзгертуге болады.
#XMSG
emptyRecordNameValidationHeaderMsg=Жазба атауын енгізіңіз.
#XMSG
emptyPartionHeader=Бөліктер санын енгізіңіз.
#XMSG
invalidPartitionsHeader=Жарамды бөліктер санын енгізіңіз.
#XMSG
invalidpartitionsDesc=1 және 200,000 арасындағы санды енгізіңіз.
#XMSG
emptyrFactorHeader=Тираждау коэффициентін енгізіңіз.
#XMSG
invalidrFactorHeader=Жарамды тираждау коэффициентін енгізіңіз.
#XMSG
invalidrFactorDesc=1 және 32,767 арасындағы санды енгізіңіз.
#XMSG
emptyRecordNameValidationDescMsg=Егер "AVRO" сериалау пішімі пайдаланылса, тек келесі таңбаларға қолдау көрсетіледі:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(астыңғы сызықша)
#XMSG
validRecordNameValidationHeaderMsg=Жарамды жазба атауын енгізіңіз.
#XMSG
validRecordNameValidationDescMsgUpdated="AVRO" сериалау пішімі пайдаланылғандықтан, жазба атауында тек әріптік-сандық (A-Z, a-z, 0-9) таңбалар және астыңғы сызықша (_) болуы керек. Ол әріппен немесе астыңғы сызықшамен басталуы керек.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled="Дельта жүктемелер үшін нысан тредінің саны" мәнін бір немесе бірнеше нысанда "Бастапқы және дельта" жүктеу түрі болған кезде орнатуға болады.
#XMSG
invalidTargetName=Баған атауы жарамсыз
#XMSG
invalidTargetNameDesc=Мақсатты баған атауы әріптік-сандық (A-Z, a-z, 0-9) таңбадан және астыңғы сызықшадан (_) тұруы керек. 
#XFLD
consumeOtherSchema=Басқа схема нұсқаларын тұтыну
#XFLD
ignoreSchemamissmatch=Схема сәйкессіздігін елемеу
#XFLD
confleuntDatatruncation=Деректерді қысқарту кезіндегі қате
#XFLD
isolationLevel=Оқшаулау деңгейі
#XFLD
confluentOffset=Іске қосу нүктесі
#XFLD
signavioGroupDeltaFilesByText=Ешқайсысы
#XFLD
signavioFileFormatText=Паркет
#XFLD
signavioSparkCompatibilityParquetText=Жоқ
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=Жоқ

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Проекциялар
#XBUT
txtAdd=Қосу
#XBUT
txtEdit=Өңдеу
#XMSG
transformationText=Сүзгіні немесе салыстыруды реттеу үшін проекция қосыңыз.
#XMSG
primaryKeyRequiredText=Конфигурация схемасы бар бастапқы кілтті таңдаңыз.
#XFLD
lblSettings=Параметрлер
#XFLD
lblTargetSetting={0}: мақсатты параметрлер
#XMSG
@csvRF=Қалтадағы барлық файлдарға қолдану қажет схема анықтамасын қамтитын файлды таңдаңыз.
#XFLD
lblSourceColumns=Бастапқы бағандар
#XFLD
lblJsonStructure=JSON құрылымы
#XFLD
lblSourceSetting={0}: бастапқы параметрлер
#XFLD
lblSourceSchemaSetting={0}: бастапқы схема параметрлері
#XBUT
messageSettings=Хабар параметрлері
#XFLD
lblPropertyTitle1=Нысан сипаттары
#XFLD
lblRFPropertyTitle=Тираждау ағыны сипаттары
#XMSG
noDataTxt=Көрсетілетін бағандар жоқ.
#XMSG
noTargetObjectText=Таңдалған мақсатты нысан жоқ.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Мақсатты бағандар
#XMSG
searchColumns=Бағандарды іздеу
#XTOL
cdcColumnTooltip=Дельта қадағалауға арналған баған
#XMSG
sourceNonDeltaSupportErrorUpdated=Бастапқы нысан дельта қадағалауға қолдау көрсетпейді.
#XMSG
targetCDCColumnAdded=Дельта қадағалау үшін 2 мақсатты баған қосылды.
#XMSG
deltaPartitionEnable=Дельта жүктеулерге арналған нысан тредінің шегі бастапқы параметрлерге қосылды.
#XMSG
attributeMappingRemovalTxt=Жаңа мақсатты нысан үшін қолдау көрсетілмейтін жарамсыз салыстырулар жойылуда.
#XMSG
targetCDCColumnRemoved=Дельта қадағалау үшін пайдаланылған 2 мақсатты баған жойылды.
#XMSG
replicationLoadTypeChanged=Жүктеу түрі "Бастапқы және дельта" болып өзгертілді.
#XMSG
sourceHDLFLoadTypeError=Жүктеу түрін "Бастапқы және дельта" етіп өзгертіңіз.
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=SAP HANA Cloud, Data Lake Files қосылым түрімен бастапқы қосылымнан нысанды SAP Datasphere-ге тираждау үшін "бастапқы және дельта" жүктеу түрін пайдалану керек.
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Дельта қадағалауды қосыңыз.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=SAP HANA Cloud, Data Lake Files қосылым түрімен бастапқы қосылымнан нысанды SAP Datasphere-ге тираждау үшін дельта қадағалауды қосуыңыз керек.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Мақсатты нысанды өзгертіңіз.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Мақсатты нысанды пайдалану мүмкін емес, себебі дельта қадағалау өшірілген. Мақсатты нысанның атын өзгертуге болады (ол дельта қадағалау мүмкіндігі бар жаңа нысанды жасауға мүмкіндік береді) немесе оны дельта қадағалау мүмкіндігі қосылған бұрыннан бар нысанмен салыстыруға болады.
#XMSG
deltaPartitionError=Дельта жүктеу түрлері үшін нысан тредтерінің дұрыс санын енгізіңіз.
#XMSG
deltaPartitionErrorDescription=1 және 10 арасындағы мәнді енгізіңіз.
#XMSG
deltaPartitionEmptyError=Дельта жүктеу түрлері үшін нысан тредтерінің санын енгізіңіз.
#XFLD
@lblColumnDescription=Сипаттама
#XMSG
@lblColumnDescriptionText1=Техникалық мақсаттар үшін – бастапқы кілті жоқ ABAP негізіндегі бастапқы нысандарды тираждау кезінде орын алған мәселелерден туындаған қайталанатын жазбаларды өңдеу.
#XFLD
storageType=Сақтау
#XFLD
skipUnmappedColLbl=Салыстырылмаған бағандарды өткізіп жіберу
#XFLD
abapContentTypeLbl=Контент түрі
#XFLD
autoMergeForTargetLbl=Деректерді автоматты түрде біріктіру
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Жалпы
#XFLD
lblBusinessName=Бизнес атау
#XFLD
lblTechnicalName=Техникалық атау
#XFLD
lblPackage=Бума
#XFLD
statusPanel=Сеанс күйі
#XBTN: Schedule dropdown menu
SCHEDULE=Кесте
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Кестені өңдеу
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Кестені жою
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Кесте жасау
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Кестені тексеру сәтсіз аяқталды
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Кесте жасау мүмкін емес, себебі тираждау ағыны қазір қолданысқа енгізілуде.{0}Тираждау ағыны қолданысқа енгізіліп біткенше күтіңіз.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText="Бастапқы және дельта" жүктеу түріне ие нысандарды қамтитын тираждау ағыны үшін кестені жасау мүмкін емес.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated="Бастапқы және дельта/Тек дельта" жүктеу түріне ие нысандарды қамтитын тираждау ағыны үшін кестені жасау мүмкін емес.
#XFLD : Scheduled popover
SCHEDULED=Жоспарланды
#XFLD
CREATE_REPLICATION_TEXT=Тираждау ағынын жасаңыз
#XFLD
EDIT_REPLICATION_TEXT=Тираждау ағынын өңдеңіз
#XFLD
DELETE_REPLICATION_TEXT=Тираждау ағынын жойыңыз
#XFLD
REFRESH_FREQUENCY=Жиілік
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Тираждау ағынын қолдану мүмкін емес, себебі бұрыннан бар кесте{0} әлі "Бастапқы және дельта" жүктеу түріне қолдау көрсетпейді.{0}{0}Тираждау ағынын қолданысқа енгізу үшін барлық нысандардың жүктеу түрлерін{0} "Тек бастапқы" жүктеу түріне орнату керек. Балама түрде, кестені жоюға, {0}тираждау ағынын қолданысқа енгізуге, содан кейін жаңа сеансты бастауға болады. Бұл "Бастапқы және дельта" жүктеу түрі бар нысандарға да қолдау көрсететін{0} аяқталмайтын сеансқа әкеледі.
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Тираждау ағынын қолдану мүмкін емес, себебі бұрыннан бар кесте{0} әлі "Бастапқы және дельта/Тек дельта" жүктеу түріне қолдау көрсетпейді.{0}{0}Тираждау ағынын қолданысқа енгізу үшін барлық нысандардың жүктеу түрлерін{0} "Тек бастапқы" жүктеу түріне орнату керек. Балама түрде, кестені жоюға, {0}тираждау ағынын қолданысқа енгізуге, содан кейін жаңа сеансты бастауға болады. Бұл "Бастапқы және дельта/Тек дельта" жүктеу түрі бар нысандарға да қолдау көрсететін{0} аяқталмайтын сеансқа әкеледі.
#XMSG
SCHEDULE_EXCEPTION=Кесте мәліметтерін алу сәтсіз аяқталды
#XFLD: Label for frequency column
everyLabel=Әр
#XFLD: Plural Recurrence text for Hour
hoursLabel=Сағат
#XFLD: Plural Recurrence text for Day
daysLabel=Күн
#XFLD: Plural Recurrence text for Month
monthsLabel=Ай
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Минут
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Ықтимал кесте туралы ақпаратты алу мүмкін болмады.
#XFLD :Paused field
PAUSED=Кідіртілді
#XMSG
navToMonitoring=Ағындар мониторында ашу
#XFLD
statusLbl=Күйі
#XFLD
lblLastRunExecuted=Соңғы сеанстың басталуы
#XFLD
lblLastExecuted=Соңғы сеанс
#XFLD: Status text for Completed
statusCompleted=Орындалды
#XFLD: Status text for Running
statusRunning=Орындалуда
#XFLD: Status text for Failed
statusFailed=Сәтсіз аяқталды
#XFLD: Status text for Stopped
statusStopped=Тоқтатылды
#XFLD: Status text for Stopping
statusStopping=Тоқтатылуда
#XFLD: Status text for Active
statusActive=Белсенді
#XFLD: Status text for Paused
statusPaused=Кідіртілді
#XFLD: Status text for not executed
lblNotExecuted=Әлі іске қосылған жоқ
#XFLD
messagesSettings=Хабарлар параметрлері
#XTOL
@validateModel=Тексеру туралы хабарлар
#XTOL
@hierarchy=Иерархия
#XTOL
@columnCount=Бағандар саны
#XMSG
VAL_PACKAGE_CHANGED=Осы нысанды "{1}" бумасына тағайындадыңыз. Осы өзгерісті растау және бағалау үшін "Сақтау" түймесін басыңыз. Сақтағаннан кейін редакторда бумаға тағайындау әрекетінен бас тарту мүмкін емес екенін ескеріңіз.
#XMSG
MISSING_DEPENDENCY="{0}" нысанының тәуелділіктерін "{1}" бумасында шешу мүмкін емес.
#XFLD
deltaLoadInterval=Дельта жүктеу аралығы
#XFLD
lblHour=Сағаттар (0-24)
#XFLD
lblMinutes=Минуттар (0-59)
#XMSG
maxHourOrMinErr=0 мен {0} арасындағы мәнді енгізіңіз
#XMSG
maxDeltaInterval=Дельта жүктеу аралығының максималды мәні — 24 сағат.{0}Сәйкесінше минут мәнін немесе сағат мәнін өзгертіңіз.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Мақсатты контейнер жолы
#XFLD
confluentSubjectName=Субъект аты
#XFLD
confluentSchemaVersion=Схема нұсқасы
#XFLD
confluentIncludeTechKeyUpdated=Техникалық кілтті қосу
#XFLD
confluentOmitNonExpandedArrays=Кеңейтілмейтін жиымдарды алып тастау
#XFLD
confluentExpandArrayOrMap=Жиымды немесе картаны кеңейту
#XCOL
confluentOperationMapping=Операцияны салыстыру
#XCOL
confluentOpCode=Операция коды
#XFLD
confluentInsertOpCode=Енгізу
#XFLD
confluentUpdateOpCode=Жаңарту
#XFLD
confluentDeleteOpCode=Жою
#XFLD
expandArrayOrMapNotSelectedTxt=Таңдалмаған
#XFLD
confluentSwitchTxtYes=Иә
#XFLD
confluentSwitchTxtNo=Жоқ
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Қате
#XTIT
executeWarning=Ескерту
#XMSG
executeunsavederror=Тираждау ағынын орындамас бұрын, оны сақтаңыз.
#XMSG
executemodifiederror=Тираждау ағынында сақталмаған өзгерістер жоқ. Тираждау ағынын сақтаңыз.
#XMSG
executeundeployederror=Тираждау ағынын орындамас бұрын, оны қолданысқа енгізуіңіз керек.
#XMSG
executedeployingerror=Қолданысқа енгізудің аяқталғанын күтіңіз.
#XMSG
msgRunStarted=Сеанс басталды
#XMSG
msgExecuteFail=Тираждау ағынын орындау сәтсіз аяқталды
#XMSG
titleExecuteBusy=Күте тұрыңыз.
#XMSG
msgExecuteBusy=Тираждау ағынын орындау үшін деректер дайындалуда.
#XTIT
executeConfirmDialog=Ескерту
#XMSG
msgExecuteWithValidations=Тираждау ағынында тексеру қателері бар. Тираждау ағынын орындау ақаулықтың орын алуына әкелуі мүмкін.
#XMSG
msgRunDeployedVersion=Қолданысқа енгізілетін өзгерістер бар. Тираждау ағынының соңғы қолданысқа енгізілген нұсқасы орындалады. Жалғастырғыңыз келе ме?
#XBUT
btnExecuteAnyway=Сонда да орындау
#XBUT
btnExecuteClose=Жабу
#XBUT
loaderClose=Жабу
#XTIT
loaderTitle=Жүктелуде...
#XMSG
loaderText=Серверден мәліметтер алынуда
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Осы SAP-қа қатысты емес мақсатты қосылымға тираждау ағынын бастау мүмкін емес,
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=себебі осы айда қолжетімді шығыс көлем жоқ.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Әкімші осы тенант үшін премиум шығыс блоктарын көбейтіп,
#XMSG
premiumOutBoundRFAdminErrMsgPart2=осы ай үшін қосымша шығыс көлемін қолжетімді ете алады.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Қате
#XTIT
deployInfo=Ақпарат
#XMSG
deployCheckFailException=Қолданысқа енгізу кезінде ерекше жағдай орын алды
#XMSG
deployGBQFFDisabled=Google BigQuery-ге мақсатты қосылыммен тираждау ағындарын қолданысқа енгізу қазіргі уақытта мүмкін емес, себебі біз бұл функцияға техникалық қызмет көрсету жұмыстарын жүргізіп жатырмыз.
#XMSG
deployKAFKAFFDisabled=Apache Kafka-ға мақсатты қосылыммен тираждау ағындарын қолданысқа енгізу қазіргі уақытта мүмкін емес, себебі біз бұл функцияға техникалық қызмет көрсету жұмыстарын жүргізіп жатырмыз.
#XMSG
deployConfluentDisabled=Confluent Kafka-ға мақсатты қосылыммен тираждау ағындарын қолданысқа енгізу қазіргі уақытта мүмкін емес, себебі біз бұл функцияға техникалық қызмет көрсету жұмыстарын жүргізіп жатырмыз.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Келесі мақсатты нысандар үшін дельта қадағалау кестесі репозитарийдегі басқа кестелермен пайдаланылуда: {0}. Тираждау ағынын қолданысқа енгізбес бұрын, байланысты дельта қадағалау кестесінің атаулары бірегей екеніне көз жеткізу үшін, осы мақсатты нысандардың атауларын өзгертуіңіз керек.
#XMSG
deployDWCSourceFFDisabled=Бастапқы қосылымы SAP Datasphere болатын тираждау ағындарын қолданысқа енгізу қазіргі уақытта мүмкін емес, себебі біз бұл функцияға техникалық қызмет көрсету жұмыстарын орындап жатырмыз.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Дельта қадағалау қосылған жергілікті кестелерді бастапқы нысандар ретінде қамтитын тираждау ағындарын қолданысқа енгізу қазіргі уақытта мүмкін емес, себебі біз бұл функцияға техникалық қызмет көрсету жұмыстарын орындап жатырмыз.
#XMSG
deployHDLFSourceFFDisabled=SAP HANA Cloud, Data Lake Files қосылым түрімен бастапқы қосылымдары бар тираждау ағындарын қолданысқа енгізу қазіргі уақытта мүмкін емес, себебі біз техникалық қызмет көрсету жұмыстарын орындап жатырмыз.
#XMSG
deployObjectStoreAsSourceFFDisabled=Бұлттық сақтау провайдерлері бар тираждау ағындарын олардың бастапқы нысаны ретінде қолданысқа енгізу қазіргі уақытта мүмкін емес.
#XMSG
deployConfluentSourceFFDisabled=Бастапқы қосылымы Confluent Kafka болатын тираждау ағындарын қолданысқа енгізу қазіргі уақытта мүмкін емес, себебі біз бұл функцияға техникалық қызмет көрсету жұмыстарын орындап жатырмыз.
#XMSG
deployMaxDWCNewTableCrossed=Үлкен тираждау ағындары үшін оларды бір ретте "сақтау және қолданысқа енгізу" мүмкін емес. Алдымен тираждау ағынын сақтаңыз, содан кейін оны қолданысқа енгізіңіз.
#XMSG
deployInProgressInfo=Қолданысқа енгізу орындалып жатыр.
#XMSG
deploySourceObjectInUse={0} бастапқы нысандары {1} тираждау ағындарында бұрыннан пайдаланылуда.
#XMSG
deployTargetSourceObjectInUse={0} бастапқы нысандары {1} тираждау ағындарында бұрыннан пайдаланылуда. {2} мақсатты нысандары {3} тираждау ағындарында бұрыннан пайдаланылуда.
#XMSG
deployReplicationFlowCheckError=Тираждау ағынын тексеру кезінде қате орын алды: {0}
#XMSG
preDeployTargetObjectInUse={0} мақсатты нысандары {1} тираждау ағындарында әлдеқашан пайдаланылады және екі түрлі тираждау ағынында бірдей мақсатты нысан болуы мүмкін емес. Басқа мақсатты нысанды таңдап, әрекетті қайталаңыз.
#XMSG
runInProgressInfo=Тираждау ағыны іске қосылып қойған.
#XMSG
deploySignavioTargetFFDisabled=Мақсатты нысаны SAP Signavio болатын тираждау ағындарын қолданысқа енгізу қазіргі уақытта мүмкін емес, себебі біз бұл функцияға техникалық қызмет көрсету жұмыстарын орындап жатырмыз.
#XMSG
deployHanaViewAsSourceFFDisabled=Таңдалған бастапқы қосылым үшін бастапқы нысандар ретінде көріністері бар тираждау ағындарын қолданысқа енгізу қазіргі уақытта мүмкін емес. Әрекетті кейінірек қайталаңыз.
#XMSG
deployMsOneLakeTargetFFDisabled=Мақсатты нысаны MS OneLake болатын тираждау ағындарын қолданысқа енгізу қазіргі уақытта мүмкін емес, себебі біз бұл функцияға техникалық қызмет көрсету жұмыстарын орындап жатырмыз.
#XMSG
deploySFTPTargetFFDisabled=Мақсатты нысаны SFTP болатын тираждау ағындарын қолданысқа енгізу қазіргі уақытта мүмкін емес, себебі біз бұл функцияға техникалық қызмет көрсету жұмыстарын орындап жатырмыз.
#XMSG
deploySFTPSourceFFDisabled=Бастапқы нысаны SFTP болатын тираждау ағындарын қолданысқа енгізу қазіргі уақытта мүмкін емес, себебі біз бұл функцияға техникалық қызмет көрсету жұмыстарын орындап жатырмыз.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Техникалық атау
#XFLD
businessNameInRenameTarget=Бизнес атау
#XTOL
renametargetDialogTitle=Мақсатты нысанның атын өзгерту
#XBUT
targetRenameButton=Атын өзгерту
#XBUT
targetRenameCancel=Бас тарту
#XMSG
mandatoryTargetName=Атауды енгізуіңіз керек.
#XMSG
dwcSpecialChar=_(астыңғы сызықша) рұқсат етілетін жалғыз таңба болып табылады. 
#XMSG
dwcWithDot=Мақсатты кесте атауы латын әріптерінен, сандардан, астыңғы сызықшалардан (_) және нүктелерден (.) тұруы мүмкін. Бірінші таңба әріп, сан немесе астыңғы сызықша (нүкте емес) болуы керек.
#XMSG
nonDwcSpecialChar=Рұқсат етілген арнайы таңбалар: _(астыңғы сызықша) -(дефис) .(нүкте)
#XMSG
firstUnderscorePattern=Атау _(астыңғы сызықша) таңбасынан басталмауы керек

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: SQL кесте жасау операторын көру
#XMSG
sqlDialogMaxPKWarning=Google BigQuery-де ең көбі 16 бастапқы кілтке қолдау көрсетіледі және бастапқы нысанда көбірек сан бар. Сондықтан бұл мәлімдемеде бастапқы кілттер анықталмаған.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Бір немесе бірнеше бастапқы бағанда Google BigQuery-де бастапқы кілттер ретінде анықталмайтын деректер түрлері бар. Сондықтан бұл жағдайда бастапқы кілттер анықталмайды. Google BigQuery-де тек келесі деректер түрлерінің бастапқы кілті болуы мүмкін: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Көшіру және жабу
#XBUT
closeDDL=Жабу
#XMSG
copiedToClipboard=Аралық сақтағышқа көшірілді


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=''{0}'' нысаны тапсырмалар тізбегінің бөлігі бола алмайды, себебі оның соңы жоқ (өйткені ол "Бастапқы және дельта/Тек дельта" жүктеу түрі бар нысандарды қамтиды).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=''{0}'' нысаны тапсырмалар тізбегінің бөлігі бола алмайды, себебі оның соңы жоқ (өйткені ол "Бастапқы және дельта" жүктеу түрі бар нысандарды қамтиды).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE="{0}" нысанын тапсырмалар тізбегіне қосу мүмкін емес.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Сақталмаған мақсатты нысандар бар. Қайтадан сақтаңыз.{0}{0} Бұл функцияның әрекеті өзгерді: Бұрын мақсатты нысандар тираждау ағыны қолданысқа енгізілген кезде ғана мақсатты ортада жасалған.{0} Енді нысандар тираждау ағыны сақталған кезде жасалған. Тираждау ағыны осы өзгертуге дейін жасалған және жаңа нысандарды қамтиды.{0} Жаңа нысандарды дұрыс қосу үшін тираждау ағынын қолданысқа енгізу алдында тағы бір рет сақтау керек.
#XMSG
confirmChangeContentTypeMessage=Контент түрін өзгерткелі жатырсыз. Егер солай болса, барлық бұрыннан бар проекция жойылады.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Субъект аты
#XFLD
schemaDialogVersionName=Схема нұсқасы
#XFLD
includeTechKey=Техникалық кілтті қосу
#XFLD
segementButtonFlat=Тегіс
#XFLD
segementButtonNested=Кірістірілген
#XMSG
subjectNamePlaceholder=Субъект атауын іздеу

#XMSG
@EmailNotificationSuccess=Орындау уақыты эл. пошта хабарландыруларының конфигурациясы сақталды.

#XFLD
@RuntimeEmailNotification=Орындау уақыты туралы эл. хабарландыру

#XBTN
@TXT_SAVE=Сақтау


