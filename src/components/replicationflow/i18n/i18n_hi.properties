#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=प्रतिकृति प्रवाह

#XFLD: Edit Schema button text
editSchema=स्कीमा संपादित करें

#XTIT : Properties heading
configSchema=स्कीमा कॉन्फ़िगर करें

#XFLD: save changed button text
applyChanges=परिवर्तन लागू करें


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=स्रोत और कनेक्शन का चयन करें
#XFLD
sourceContainernEmptyText=कंटेनर का चयन करें
#XFLD
targetConnectionEmptyText=लक्ष्य कनेक्शन का चयन करें
#XFLD
targetContainernEmptyText=कंटेनर का चयन करें
#XFLD
sourceSelectObjectText=स्रोत ऑब्जेक्ट का चयन करें
#XFLD
sourceObjectCount=स्रोत ऑब्जेक्ट ({0})
#XFLD
targetObjectText=लक्षित ऑब्जेक्ट
#XFLD
confluentBrowseContext=संदर्भ चुनें
#XBUT
@retry=पुन: प्रयास करें
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=किराएदार अपग्रेड प्रगति में है.

#XTOL
browseSourceConnection=ब्राउजर स्रोत कनेक्शन
#XTOL
browseTargetConnection=ब्राउज़र लक्ष्य कनेक्शन
#XTOL
browseSourceContainer=ब्राउज़र स्रोत कंटेनर
#XTOL
browseAndAddSourceDataset=स्रोत ऑब्जेक्ट जोड़ें
#XTOL
browseTargetContainer=ब्राउज़र लक्ष्य कंटेनर
#XTOL
browseTargetSetting=ब्राउज़र लक्ष्य सेटिंग
#XTOL
browseSourceSetting=स्रोत सेटिंग ब्राउज़ करें
#XTOL
sourceDatasetInfo=जानकारी
#XTOL
sourceDatasetRemove=निकालें
#XTOL
mappingCount=यह गैर-नाम आधारित मैपिंग/अभिव्यक्तियों की कुल संख्या का प्रतिनिधित्व करता है.
#XTOL
filterCount=यह गैर-नाम आधारित मैपिंग/अभिव्यक्तियों की कुल संख्या का प्रतिनिधित्व करता है.
#XTOL
loading=लोड हो रहा है...
#XCOL
deltaCapture=डेल्टा कैप्चर
#XCOL
deltaCaptureTableName=डेल्टा कैप्चर तालिका
#XCOL
loadType=लोड प्रकार
#XCOL
deleteAllBeforeLoading=लोड करने से पहले सभी हटाएँ
#XCOL
transformationsTab=प्रोजेक्शन
#XCOL
settingsTab=सेटिंग

#XBUT
renameTargetObjectBtn=लक्ष्य ऑब्जेक्ट का नाम बदलें
#XBUT
mapToExistingTargetObjectBtn=मौजूदा लक्ष्य ऑब्जेक्टके लिए मैप करें
#XBUT
changeContainerPathBtn=कंटेनर पथ बदलें
#XBUT
viewSQLDDLUpdated=दृश्य SQL तालिका विवरण बनाएं
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=स्रोत ऑब्जेक्ट डेल्टा कैप्चर का समर्थन नहीं करता है, लेकिन चयनित लक्ष्य ऑब्जेक्ट में डेल्टा कैप्चर विकल्प सक्षम है.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=लक्ष्य ऑब्जेक्ट का उपयोग नहीं किया जा सकता क्योंकि डेल्टा कैप्चर सक्षम है,{0}जबकि स्रोत ऑब्जेक्ट डेल्टा कैप्चर का समर्थन नहीं करता है.{1}आप किसी अन्य लक्ष्य ऑब्जेक्ट का चयन कर सकते हैं जो डेल्टा कैप्चर का समर्थन नहीं करता है.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=इस नाम की एक लक्ष्य ऑब्जेक्ट पहले से मौजूद है. हालांकि, इसका उपयोग नहीं {0} किया जा सकता {0} क्योंकि डेल्टा कैप्चर सक्षम है, जबकि स्रोत ऑब्जेक्ट डेल्टा कैप्चर का समर्थन नहीं करता है. {1}आप या तो मौजूदा लक्ष्य ऑब्जेक्ट का नाम दर्ज कर सकते हैं जो डेल्टा कैप्चर का समर्थन नहीं {0} करता है, या ऐसा नाम दर्ज कर सकते हैं जो अभी तक मौजूद नहीं है.
#XBUT
copySQLDDLUpdated=SQL तालिका विवरण बनाएं की प्रति बनाएं
#XMSG
targetObjExistingNoCDCColumnUpdated=Google BigQuery में मौजूदा तालिकाओं में परिवर्तन डेटा कैप्चर (CDC) के लिए निम्नलिखित स्तंभ शामिल होने चाहिए:{0}{0}{1} परिचालन_ध्वज{0}{1} मिटाया गया_है{0}{1} रिकॉर्ड स्टैम्प
#XMSG
new_infoForUnsupportedDatasetNoKeys=निम्न स्रोत ऑब्जेक्ट समर्थित नहीं हैं क्योंकि उनके पास प्राथमिक कुंजी नहीं है, या वे ऐसे कनेक्शन का उपयोग कर रहे हैं जो प्राथमिक कुंजी पुनर्प्राप्त करने के लिए शर्तों को पूरा नहीं करता है:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=संभावित समाधान के लिए कृपया SAP KBA 3531135 देखें.
#XLST: load type list values
initial=केवल आरंभिक
@emailUpdateError=ईमेल अधिसूचना सूची को अपडेटिंग में त्रुटि

#XLST
initialDelta=आरंभिक और डेल्टा

#XLST
deltaOnly=केवल डेल्टा
#XMSG
confluentDeltaLoadTypeInfo=Confluent Kafka स्रोत के लिए, केवल लोड प्रकार आरंभिक और डेल्टा समर्थित है.
#XMSG
confirmRemoveReplicationObject=क्या आप पुष्टि करते हैं कि आप प्रतिकृति को हटाना चाहते हैं?
#XMSG
confirmRemoveReplicationTaskPrompt=यह क्रिया मौजूदा प्रतिकृति हटाई जाएगी. क्या आप जारी करना चाहते हैं?
#XMSG
confirmTargetConnectionChangePrompt=यह क्रिया लक्ष्य कनेक्शन को रीसेट कर देगी, कंटेनर को लक्षित करेगी और सभी लक्षित ऑब्जेक्ट को हटा देगी. क्या आप जारी रखना चाहते हैं ?
#XMSG
confirmTargetContainerChangePrompt=यह क्रिया लक्ष्य कनेक्शन को रीसेट कर देगी, कंटेनर को लक्षित करेगी और सभी लक्षित ऑब्जेक्ट को हटा देगी. क्या आप जारी रखना चाहते हैं?
#XMSG
confirmRemoveTransformObject=क्या आप पुष्टि करते हैं कि आप प्रोजेक्शन {0} को हटाना चाहते हैं?
#XMSG
ErrorMsgContainerChange=कंटेनर पथ बदलने के दौरान एक त्रुटि हुई.
#XMSG
infoForUnsupportedDatasetNoKeys=उनके पास प्राथमिक कुंजी नहीं होने के कारण निम्न स्रोत ऑब्जेक्ट समर्थित नहीं हैं:
#XMSG
infoForUnsupportedDatasetView=दृश्य प्रकार की निम्न स्रोत ऑब्जेक्ट समर्थित नहीं हैं:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=निम्न स्रोत ऑब्जेक्ट समर्थित नहीं है क्योंकि यह कोई SQL दृश्य है जिसमें इनपुट पैरामीटर शामिल हैं:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=उनके लिए निष्कर्षण अक्षम होने के कारण निम्न स्रोत ऑब्जेक्ट समर्थित नहीं हैं:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Confluent कनेक्शन के लिए, अनुमत क्रमांकन स्वरूप केवल AVRO और JSON हैं. निम्न ऑब्जेक्ट समर्थित नहीं हैं, क्योंकि वे किसी भिन्न क्रमांकन स्वरूप का उपयोग करते हैं:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=निम्न ऑब्जेक्ट के लिए स्कीमा प्राप्त करने में असमर्थ. कृपया उचित संदर्भ चुनें या स्कीमा रजिस्ट्री कॉन्फ़िगरेशन सत्यापित करें
#XTOL: warning dialog header on deleting replication task
deleteHeader=हटाएं
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Google BigQuery के लिए लोड होने से पहले सभी हटाएं सेटिंग समर्थित नहीं है.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=सेटिंग से पहले सभी हटाएं प्रत्येक प्रतिकृति से पहले ऑब्जेक्ट (विषय) को हटाता है और दोबारा बनाता है. यह सभी असाइन किए गए संदेशों को भी हटा देता है.
#XTOL
DeleteAllBeforeLoadingLTFInfo=इस लक्ष्य प्रकार के लिए 'पहले सभी हटाएं सेटिंग समर्थित नहीं है.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=तकनीकी नाम
#XCOL
connBusinessName=बिज़नेस का नाम
#XCOL
connDescriptionName=वर्णन
#XCOL
connType=प्रकार
#XMSG
connTblNoDataFoundtxt=कोई कनेक्शन नहीं मिला
#XMSG
connectionError=फेचिंग कनेक्शन के दौरान एक त्रुटि उत्पन्न हुई.
#XMSG
connectionCombinationUnsupportedErrorTitle=कनेक्शन संयोजन समर्थित नहीं है
#XMSG
connectionCombinationUnsupportedErrorMsgTxt={0} से {1} में प्रतिकृति वर्तमान में समर्थित नहीं है.
#XMSG
invalidTargetforSourceHDLFErrorTitle=कनेक्शन प्रकार संयोजन समर्थित नहीं है
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=कनेक्शन प्रकार SAP HANA Cloud, डेटा लेक फ़ाइल से {0} तक प्रतिकृति समर्थित नहीं है. आप केवल SAP Datasphere की प्रतिकृति बना सकते हैं.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=चयन करें
#XBUT
containerCancelBtn=रद्द करें
#XTOL
containerSelectTooltip=चयन करें
#XTOL
containerCancelTooltip=रद्द करें
#XMSG
containerContainerPathPlcHold=कंटेनर पथ
#XFLD
containerContainertxt=कंटेनर
#XFLD
confluentContainerContainertxt=प्रसंग
#XMSG
infoMessageForSLTSelection=कंटेनर के रूप में केवल/एसएलटी/मास ट्रांसफर ID की अनुमति है SLT (यदि उपलब्ध हो) के तहत मास स्थानांतरण ID चयन करें और सबमिट पर क्लिक करें.
#XMSG
msgFetchContainerFail=फ़ेचिंग कंटेनर डेटा के दौरान एक त्रुटि उत्पन्न हुई.
#XMSG
infoMessageForSLTHidden=यह कनेक्शन SLT फ़ोल्डरों का समर्थन नहीं करता है, इसलिए वे नीचे दी गई सूची में दिखाई नहीं देते हैं.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=एक कंटेनर का चयन करें जिसमें उप फ़ोल्डर शामिल है.
#XMSG
sftpIncludeSubFolderText=गलत
#XMSG
sftpIncludeSubFolderTextNew=नहीं

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(अभी तक कोई फ़िल्टर मैपिंग नहीं)
#XMSG
failToFetchRemoteMetadata=फेचिंग मेटाडेटा के दौरान एक त्रुटि उत्पन्न हुई.
#XMSG
failToFetchData=फेचिंग मौजूदा लक्ष्य के दौरान एक त्रुटि उत्पन्न हुई.
#XCOL
@loadType=लोड प्रकार
#XCOL
@deleteAllBeforeLoading=लोड करने से पहले सभी हटाएँ

#XMSG
@loading=लोड हो रहा है...
#XFLD
@selectSourceObjects=स्रोत ऑब्जेक्ट का चयन करें
#XMSG
@exceedLimit=आप एक समय में {0} से अधिक ऑब्जेक्ट आयात नहीं कर सकते. कृपया कम से कम {1} ऑब्जेक्ट अचयनित करें.
#XFLD
@objects=ऑब्जेक्ट
#XBUT
@ok=ठीक
#XBUT
@cancel=रद्द करें
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=अगला
#XBUT
btnAddSelection=चयन जोड़ें
#XTOL
@remoteFromSelection=चयन से निकालें
#XMSG
@searchInForSearchField=इसमें खोजें: {0}

#XCOL
@name=तकनीकी नाम
#XCOL
@type=प्रकार
#XCOL
@location=स्थान
#XCOL
@label=बिज़नेस का नाम
#XCOL
@status=स्थिति

#XFLD
@searchIn=इसमें खोजें:
#XBUT
@available=उपलब्ध
#XBUT
@selection=चयन

#XFLD
@noSourceSubFolder=तालिका और दृश्य
#XMSG
@alreadyAdded=पहले से ही डाइग्राम में मौजूद है
#XMSG
@askForFilter={0} आइटम से अधिक हैं. आइटम की संख्या को कम करने के लिए कृपया एक फ़िल्टर स्ट्रिंग दर्ज करें.
#XFLD: success label
lblSuccess=सफलता
#XFLD: ready label
lblReady=तैयार
#XFLD: failure label
lblFailed=विफल
#XFLD: fetching status label
lblFetchingDetail=विवरण लाया जा रहा है

#XMSG Place holder text for tree filter control
filterPlaceHolder=शीर्ष-स्तरीय ऑब्जेक्ट फिल्टर करने के लिए टेक्स्ट टाइप करें
#XMSG Place holder text for server search control
serverSearchPlaceholder=खोज करने के लिए दर्ज दबाएं और टाइप करें
#XMSG
@deployObjects={0} ऑब्जेक्ट आयात किया जा रहा है...
#XMSG
@deployObjectsStatus=आयात की गई ऑब्जेक्ट की संख्या: {0}. ऑब्जेक्ट की संख्या जो आयात नहीं किया जा सकता: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=स्थानीय कोष ब्राउज़र खोलने में विफल हुआ.
#XMSG
@openRemoteSourceBrowserError=स्रोत ऑब्जेक्ट फेच करने में विफल हुआ.
#XMSG
@openRemoteTargetBrowserError=लक्ष्य ऑब्जेक्ट फेच करने में विफल हुआ.
#XMSG
@validatingTargetsError=लक्ष्यों को सत्यापन करने के दौरान एक त्रुटि उत्पन्न हुई.
#XMSG
@waitingToImport=आयात करने के लिए तैयार

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=ऑब्जेक्ट की अधिकतम संख्या के पार गो गई है. एक प्रतिकृति प्रवाह के लिए 500 ऑब्जेक्ट की अधिकतम संख्या का चयन करें.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=तकनीकी नाम
#XFLD
sourceObjectBusinessName=बिज़नेस का नाम
#XFLD
sourceNoColumns=स्तंभो की संख्या
#XFLD
containerLbl=कंटेनर

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=आपको प्रतिकृति प्रवाह के लिए स्रोत कनेक्शन का चयन किया जाना चाहिए.
#XMSG
validationSourceContainerNonExist=आपको स्रोत कनेक्शन के लिए कंटेनर का चयन किया जाना चाहिए
#XMSG
validationTargetNonExist=आपको प्रतिकृति प्रवाह के लिए लक्ष्य कनेक्शन का चयन किया जाना चाहिए.
#XMSG
validationTargetContainerNonExist=आपको लक्ष्य कनेक्शन के लिए कंटेनर का चयन किया जाना चाहिए.
#XMSG
validationTruncateDisabledForObjectTitle=ऑब्जेक्ट संग्रहण के लिए प्रतिकृति.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=क्लाउड स्टोरेज में प्रतिकृति केवल तभी संभव है जब या तो लोड करने से पहले सभी को हटाएँ विकल्प सेट किया गया हो या लक्ष्य ऑब्जेक्ट लक्ष्य में मौजूद न हो.{0}{0} उन ऑब्जेक्ट के लिए प्रतिकृति को अभी भी सक्षम करने के लिए जिनके लिए लोड करने से पहले सभी को हटाएँ विकल्प सेट नहीं किया गया है, सुनिश्चित करें कि प्रतिकृति प्रवाह चलाने से पहले लक्ष्य ऑब्जेक्ट सिस्टम में मौजूद न हो.
#XMSG
validationTaskNonExist=आपके पास प्रतिकृति प्रवाह में कम से कम एक प्रतिकृति होनी चाहिए.
#XMSG
validationTaskTargetMissing=आपके पास स्रोत के साथ प्रतिकृति के लिए एक लक्ष्य होना चाहिए: {0}
#XMSG
validationTaskTargetIsSAC=चयनित लक्ष्य कोई SAC आर्टिफ़ैक्ट है: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=चयनित लक्ष्य समर्थित स्थानीय तालिका नहीं है: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=इस नाम का एक ऑब्जेक्ट लक्ष्य में पहले से मौजूद है. हालांकि, इस ऑब्जेक्ट को स्थानीय रिपॉजिटरी में प्रतिकृति प्रवाह के लिए लक्ष्य ऑब्जेक्ट के रूप में उपयोग नहीं किया जा सकता, क्योंकि यह स्थानीय तालिका नहीं है.
#XMSG
validateSourceTargetSystemDifference=आपको प्रतिकृति प्रवाह के लिए विभिन्न स्रोत और लक्ष्य कनेक्शन और कंटेनर संयोजनों का चयन करना होगा.
#XMSG
validateDuplicateSources=एक या अधिक प्रतिकृति में डुप्लिकेट स्रोत ऑब्जेक्ट नाम हैं: {0}.
#XMSG
validateDuplicateTargets=एक या अधिक प्रतिकृति में डुप्लिकेट लक्ष्य ऑब्जेक्ट नाम हैं: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=स्रोत ऑब्जेक्ट {0} डेल्टा कैप्चर का समर्थन नहीं करता है, जबकि लक्ष्य ऑब्जेक्ट {1} करता है. आपको प्रतिकृति हटानी होगी.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=लक्ष्य ऑब्जेक्ट नाम {0} के साथ प्रतिकृति के लिए आपको लोड प्रकार "आरंभिक और डेल्टा" का चयन करना होगा.
#XMSG
validationAutoRenameTarget=लक्ष्य स्तंभों के नाम बदल दिए गए हैं.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=एक ऑटो-प्रोजेक्शन जोड़ा गया है और लक्ष्य में प्रतिकृति बनाने की अनुमति देने के लिए निम्नलिखित लक्ष्य स्तंभों का नाम बदल दिया गया है:{1}{1} {0} {1}{1}ऐसा निम्नलिखित कारणों में से एक के कारण है:{1}{1}{2} असमर्थित वर्ण{1}{2} आरक्षित उपसर्ग
#XMSG
validationAutoRenameTargetDescriptionUpdated=एक ऑटो-प्रोजेक्शन जोड़ा गया है और Google BigQuery की प्रतिकृति की अनुमति देने के लिए, निम्नलिखित लक्ष्य स्तंभ के नाम बदल दिए गए हैं:{1}{1} {0} {1}{1}ऐसा निम्नलिखित कारणों में से एक के कारण है:{1}{1}{2} आरक्षित स्तंभ नाम{1}{2} समर्थित वर्ण नहीं{1}{2} आरक्षित उपसर्ग
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=एक ऑटो-प्रोजेक्शन जोड़ा गया है और कंफ्लुएंट की प्रतिकृति की अनुमति देने के लिए, निम्नलिखित लक्ष्य स्तंभ के नाम बदल दिए गए हैं:{1}{1} {0} {1}{1}ऐसा निम्नलिखित कारणों में से एक के कारण है:{1}{1}{2} आरक्षित स्तंभ नाम{1}{2} समर्थित वर्ण नहीं{1}{2} आरक्षित उपसर्ग
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=एक स्वचालित प्रक्षेपण जोड़ा गया है, और लक्ष्य पर प्रतिकृति की अनुमति देने के लिए निम्नलिखित लक्ष्य स्तंभों का नाम बदला गया है:{1}{1} {0} {1}{1} यह निम्न में से किसी एक कारण से है:{1}{1}{2} आरक्षित स्तंभ नाम{1}{2}समर्थित वर्ण नहीं{1}{2}आरक्षित उपसर्ग
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=लक्ष्य ऑब्जेक्ट का नाम बदल दिया गया है.
#XMSG
autoRenameInfoDesc=लक्ष्य ऑब्जेक्ट का नाम बदल दिया गया है क्योंकि इसमें समर्थित वर्ण नहीं हैं. केवल निम्न वर्ण समर्थित हैं:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(डॉट){0}{1}_(अंडरस्कोर){0}{1}-(डैश)
#XMSG
validationAutoTargetTypeConversion=लक्ष्य डेटा प्रकार बदल दिए गए हैं.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=निम्नलिखित लक्ष्य स्तंभों के लिए, लक्ष्य डेटा प्रकार परिवर्तित कर दिए गए हैं, क्योंकि Google BigQuery में स्रोत डेटा प्रकार समर्थित नहीं हैं:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=निम्नलिखित लक्ष्य स्तंभों के लिए, लक्ष्य डेटा प्रकार परिवर्तित कर दिए गए हैं, क्योंकि स्रोत डेटा प्रकार लक्ष्य कनेक्शन में समर्थित नहीं हैं:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=लक्ष्य स्तंभ के नामों को छोटा करें.
#XMSG
validationMaxCharLengthGBQTargetDescription=Google BigQuery में, स्तंभ नामों में अधिकतम 300 वर्ण उपयोग हो सकते हैं. निम्नलिखित लक्ष्य स्तंभ नामों को छोटा करने के लिए, किसी प्रोजेक्शन का उपयोग करें:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=प्राथमिक कुंजियां नहीं बनाई जाएंगी.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=Google BigQuery में, अधिकतम 16 प्राथमिक कुंजियां समर्थित हैं, लेकिन स्रोत ऑब्जेक्ट में प्राथमिक कुंजियों की एक बड़ी संख्या है. लक्ष्य ऑब्जेक्ट में कोई भी प्राथमिक कुंजी नहीं बनाई जाएगी.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=एक या एकाधिक स्रोत स्तंभ में ऐसे डेटा प्रकार हैं जिन्हें Google BigQuery में प्राथमिक कुंजी के रूप में परिभाषित नहीं किया जा सकता. लक्ष्य ऑब्जेक्ट में कोई भी प्राथमिक कुंजी नहीं बनाई जाएगी.{0}{0}निम्नलिखित लक्ष्य डेटा प्रकार Google BigQuery डेटा प्रकारों के साथ संगत हैं जिनके लिए प्राथमिक कुंजी परिभाषित की जा सकती है:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=एक या अधिक स्तंभ को प्राथमिक कुंजी के रूप में निर्धारित करें.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=ऐसा करने के लिए, आपको एक या अधिक स्तंभ को प्राथमिक कुंजी उपयोग स्रोत स्कीमा डायलॉग के रूप में परिभाषित करना होगा.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=एक या अधिक स्तंभ को प्राथमिक कुंजी के रूप में निर्धारित करें.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=आपको प्राथमिक कुंजी के रूप में एक या अधिक स्तंभ निर्धारित करने होंगे जो आपके स्रोत ऑब्जेक्ट के लिए प्राथमिक कुंजी बाध्यता से मिलान करते हों. ऐसा करने के लिए अपने स्रोत ऑब्जेक्ट गुणों में "स्कीमा कॉन्फ़िगर करें" पर जाएं.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=मान्य अधिकतम विभाजन मान दर्ज करें.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=अधिकतम विभाजन मान ≥ 1 और ≤ 2147483647 होना चाहिए
#XMSG
validateHDLFNoPKDatasetError=एक या अधिक स्तंभ को प्राथमिक कुंजी के रूप में निर्धारित करें.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=किसी ऑब्जेक्ट को प्रतिकृति करने के लिए, आपको एक या अधिक लक्ष्य स्तंभ को प्राथमिक कुंजी के रूप में निर्धारित करना होगा. ऐसा करने के लिए किसी प्रक्षेपण का उपयोग करें.
#XMSG
validateHDLFNoPKExistingDatasetError=एक या अधिक स्तंभ को प्राथमिक कुंजी के रूप में निर्धारित करें.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=मौजूदा लक्ष्य ऑब्जेक्ट में डेटा को दोहराने के लिए, इसमें एक या अधिक कॉलम होने चाहिए जिन्हें प्राथमिक कुंजी के रूप में परिभाषित किया गया है. {0} आपके पास एक या अधिक स्तंभ को प्राथमिक कुंजी के रूप में परिभाषित करने के लिए निम्नलिखित विकल्प हैं: {0} {1} मौजूदा लक्ष्य ऑब्जेक्ट को बदलने के लिए स्थानीय तालिका संपादक का उपयोग करें. फिर प्रतिकृति प्रवाह को पुनः लोड करें.{0}{1} प्रतिकृति प्रवाह में लक्ष्य ऑब्जेक्ट का नाम बदलें. यह रन शुरू होते ही एक नई ऑब्जेक्ट बनाएगा. नाम बदलने के बाद, आप प्रक्षेपण में एक या अधिक स्तंभ को प्राथमिक कुंजी के रूप में परिभाषित कर सकते हैं.{0}{1} ऑब्जेक्ट को किसी अन्य मौजूदा लक्ष्य ऑब्जेक्ट पर मैप करें जिसमें एक या अधिक स्तंभ पहले से ही प्राथमिक कुंजी के रूप में परिभाषित हैं.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=चयनित लक्ष्य पहले से ही कोष में मौजूद है: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=डेल्टा कैप्चर तालिका नाम पहले से ही कोष में अन्य तालिकाओं द्वारा उपयोग किए गए हैं: {0}. प्रतिकृति प्रवाह को सहेजने से पहले, यह सुनिश्चित करने के लिए आपको इन लक्ष्य ऑब्जेक्ट का नाम बदलना होगा कि संबद्ध डेल्टा कैप्चर तालिका नाम अद्वितीय हैं.
#XMSG
validateConfluentEmptySchema=स्कीमा को परिभाषित करें
#XMSG
validateConfluentEmptySchemaDescUpdated=स्रोत तालिका में कोई स्कीमा नहीं है. किसी एक को परिभाषित करने के लिए, स्कीमा कॉन्फ़िगर करें का चयन करें.
#XMSG
validationCSVEncoding=अमान्य CSV एनकोडिंग
#XMSG
validationCSVEncodingDescription=कार्य की CSV एनकोडिंग मान्य नहीं है.
#XMSG
validateConfluentEmptySchema=एक संगत लक्ष्य डेटा प्रकार का चयन करें
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=एक संगत लक्ष्य डेटा प्रकार का चयन करें
#XMSG
globalValidateTargetDataTypeDesc=स्तंभ मैपिंग में कोई त्रुटि उत्पन्न हुई. प्रोजेक्शन पर जाएं और सुनिश्चित करें कि सभी स्रोत स्तंभ एक अद्वितीय स्तंभ के साथ मैप किए गए हैं, जिसमें एक संगत डेटा प्रकार वाला स्तंभ है, और सभी परिभाषित व्यंजक मान्य हैं.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=डुप्लीकेट स्तंभ नाम.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=डुप्लिकेट स्तंभ नाम समर्थित नहीं हैं. उन्हें ठीक करने के लिए प्रोजेक्शन डायलॉग का उपयोग करें. निम्न लक्ष्य ऑब्जेक्ट में डुप्लिकेट स्तंभ नाम हैं: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=डुप्लीकेट स्तंभ नाम.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=डुप्लिकेट स्तंभ नाम समर्थित नहीं हैं. निम्न लक्ष्य ऑब्जेक्ट में डुप्लिकेट स्तंभ नाम हैं: {0}.
#XMSG
deltaOnlyLoadTypeTittle=हो सकता है डेटा में असंगतताएं हों.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=यह केवल डेल्टा लोड प्रकार, अंतिम सहेजने और अगले रन के बीच स्रोत में किए गए परिवर्तनों पर विचार नहीं करेगा.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=लोड प्रकार को "आरंभिक" में बदलें.
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=ABAP-आधारित ऑब्जेक्ट की प्रतिकृति बनाना, जिसमें प्राथमिक कुंजी नहीं है, केवल लोड प्रकार "केवल आरंभिक" के लिए ही संभव है.
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=डेल्टा कैप्चर अक्षम करें.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=स्रोत कनेक्शन प्रकार ABAP का उपयोग करके किसी ऐसे ऑब्जेक्ट की प्रतिकृति बनाने के लिए, जिसमें प्राथमिक कुंजी नहीं है, आपको पहले इस तालिका के लिए डेल्टा कैप्चरिंग को अक्षम करना होगा.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=लक्ष्य ऑब्जेक्ट बदलें.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=लक्ष्य ऑब्जेक्ट का उपयोग नहीं किया जा सकता क्योंकि डेल्टा कैप्चरिंग सक्षम है. आप या तो लक्ष्य ऑब्जेक्ट का नाम बदल सकते हैं और फिर नए (बदले हुए) ऑब्जेक्ट के लिए डेल्टा कैप्चरिंग बंद कर सकते हैं, या स्रोत ऑब्जेक्ट को उस लक्ष्य ऑब्जेक्ट पर मैप कर सकते हैं जिसके लिए डेल्टा कैप्चरिंग अक्षम है.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=लक्ष्य ऑब्जेक्ट बदलें.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=लक्ष्य ऑब्जेक्ट का उपयोग नहीं किया जा सकता क्योंकि इसमें आवश्यक तकनीकी स्तंभ __load_package_id नहीं है. आप लक्ष्य ऑब्जेक्ट का नाम बदलकर ऐसा नाम रख सकते हैं जो अभी तक मौजूद नहीं है. सिस्टम तब एक नया ऑब्जेक्ट बनाता है जिसकी परिभाषा स्रोत ऑब्जेक्ट के समान होती है और जिसमें तकनीकी स्तंभ होता है. वैकल्पिक रूप से, आप लक्ष्य ऑब्जेक्ट को किसी मौजूदा ऑब्जेक्ट से मैप कर सकते हैं जिसमें आवश्यक तकनीकी स्तंभ (__load_package_id) हो.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=लक्ष्य ऑब्जेक्ट बदलें.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=लक्ष्य ऑब्जेक्ट का उपयोग नहीं किया जा सकता क्योंकि इसमें आवश्यक तकनीकी स्तंभ  __load_record_id नहीं है. आप लक्ष्य ऑब्जेक्ट का नाम बदलकर ऐसा नाम रख सकते हैं जो अभी तक मौजूद नहीं है. सिस्टम तब एक नया ऑब्जेक्ट बनाता है जिसकी परिभाषा स्रोत ऑब्जेक्ट के समान होती है और जिसमें तकनीकी स्तंभ  होता है. वैकल्पिक रूप से, आप लक्ष्य ऑब्जेक्ट को किसी मौजूदा ऑब्जेक्ट से मैप कर सकते हैं जिसमें आवश्यक तकनीकी कॉलम (__load_record_id) हो.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=लक्ष्य ऑब्जेक्ट बदलें.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=लक्ष्य ऑब्जेक्ट का उपयोग नहीं किया जा सकता क्योंकि इसके तकनीकी स्तंभ __load_record_id का डेटा प्रकार "स्ट्रिंग(44)" नहीं है. आप लक्ष्य ऑब्जेक्ट का नाम बदलकर ऐसा नाम रख सकते हैं जो अभी तक मौजूद नहीं है. सिस्टम तब एक नया ऑब्जेक्ट बनाता है जिसकी परिभाषा स्रोत ऑब्जेक्ट के समान होती है और परिणामस्वरूप सही डेटा प्रकार होता है. वैकल्पिक रूप से, आप लक्ष्य ऑब्जेक्ट को किसी मौजूदा ऑब्जेक्ट से मैप कर सकते हैं जिसमें सही डेटा प्रकार के साथ आवश्यक तकनीकी स्तंभ (__load_record_id) हो.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=लक्ष्य ऑब्जेक्ट बदलें.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=लक्ष्य ऑब्जेक्ट का उपयोग नहीं किया जा सकता क्योंकि इसमें एक प्राथमिक कुंजी है, जबकि स्रोत ऑब्जेक्ट में कोई नहीं है. आप लक्ष्य ऑब्जेक्ट का नाम बदलकर ऐसा नाम रख सकते हैं जो अभी तक मौजूद नहीं है. सिस्टम तब एक नया ऑब्जेक्ट बनाता है जिसकी परिभाषा स्रोत ऑब्जेक्ट के समान होती है और परिणामस्वरूप कोई प्राथमिक कुंजी नहीं होती है. वैकल्पिक रूप से, आप लक्ष्य ऑब्जेक्ट को किसी मौजूदा ऑब्जेक्ट से मैप कर सकते हैं जिसमें आवश्यक तकनीकी कॉलम (__load_package_id) हो और जिसमें कोई प्राथमिक कुंजी न हो.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=लक्ष्य ऑब्जेक्ट बदलें.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=लक्ष्य ऑब्जेक्ट का उपयोग नहीं किया जा सकता क्योंकि इसमें एक प्राथमिक कुंजी है, जबकि स्रोत ऑब्जेक्ट में कोई नहीं है. आप लक्ष्य ऑब्जेक्ट का नाम बदलकर ऐसा नाम रख सकते हैं जो अभी तक मौजूद नहीं है. सिस्टम तब एक नया ऑब्जेक्ट बनाता है जिसकी परिभाषा स्रोत ऑब्जेक्ट के समान होती है और परिणामस्वरूप कोई प्राथमिक कुंजी नहीं होती है. वैकल्पिक रूप से, आप लक्ष्य ऑब्जेक्ट को किसी मौजूदा ऑब्जेक्ट से मैप कर सकते हैं जिसमें आवश्यक तकनीकी कॉलम (__load_record_id) हो और जिसमें कोई प्राथमिक कुंजी न हो.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=लक्ष्य ऑब्जेक्ट बदलें.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=लक्ष्य ऑब्जेक्ट का उपयोग नहीं किया जा सकता क्योंकि इसके तकनीकी स्तंभ __load_package_id का डेटा प्रकार "बाइनरी(>=256)" नहीं है. आप लक्ष्य ऑब्जेक्ट का नाम बदलकर ऐसा नाम रख सकते हैं जो अभी तक मौजूद नहीं है. सिस्टम तब एक नया ऑब्जेक्ट बनाता है जिसकी परिभाषा स्रोत ऑब्जेक्ट के समान होती है और परिणामस्वरूप सही डेटा प्रकार होता है. वैकल्पिक रूप से, आप लक्ष्य ऑब्जेक्ट को किसी मौजूदा ऑब्जेक्ट से मैप कर सकते हैं जिसमें सही डेटा प्रकार के साथ आवश्यक तकनीकी स्तंभ  (__load_package_id) हो.
#XMSG
validationAutoRenameTargetDPID=लक्ष्य स्तंभों के नाम बदल दिए गए हैं.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=स्रोत ऑब्जेक्ट निकालें.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=स्रोत ऑब्जेक्ट में कोई कुंजी स्तंभ नहीं है, जो इस संदर्भ में समर्थित नहीं है.
#XMSG
validationAutoRenameTargetDPIDDescription=एक ऑटो-प्रोजेक्शन जोड़ा गया है, और निम्नलिखित लक्ष्य स्तंभों का नाम बदल दिया गया है ताकि ABAP स्रोत से बिना कुंजियों के प्रतिकृतिकरण की अनुमति दी जा सके:{1}{1} {0} {1}{1}ऐसा निम्नलिखित कारणों में से एक के कारण है:{1}{1}{2} आरक्षित स्तंभ नाम{1}{2} समर्थित वर्ण नहीं{1}{2} आरक्षित उपसर्ग
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle={0} के लिए प्रतिकृति.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=उनके लक्ष्य के रूप में {0} वाले प्रतिकृति प्रवाह को सहेजना और परिनियोजित करना वर्तमान में संभव नहीं है क्योंकि हम इस फ़ंक्शन पर रखरखाव निष्पादित कर रहे हैं.
#XMSG
TargetColumnSkippedLTF=लक्ष्य स्तंभ छोड़ दिया गया है.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=असमर्थित डेटा प्रकार के कारण लक्ष्य स्तंभ छोड़ दिया गया है. {0}{1}
#XMSG
validatePKTimeColumnLTF1=प्राथमिक कुंजी के रूप में समय स्तंभ.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=स्रोत ऑब्जेक्ट के पास प्राथमिक कुंजी के रूप में समय स्तंभ है, जो इस संदर्भ में समर्थित नहीं है.
#XMSG
validateNoPKInLTFTarget=प्राथमिक कुंजी गुम है.
#XMSG
validateNoPKInLTFTargetDescription=लक्ष्य में प्राथमिक कुंजी परिभाषित नहीं है, जो इस संदर्भ में समर्थित नहीं है.
#XMSG
validateABAPClusterTableLTF=ABAP क्लस्टर तालिका.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=स्रोत ऑब्जेक्ट एक ABAP क्लस्टर तालिका है, जो इस संदर्भ में समर्थित नहीं है.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=ऐसा लगता है कि आपने अभी तक कोई डेटा नहीं जोड़ा है.
#YINS
welcomeText2=अपना प्रतिकृति प्रवाह प्रारंभ करने के लिए, बाईं ओर एक कनेक्शन और स्रोत ऑब्जेक्ट का चयन करें.

#XBUT
wizStep1=स्रोत कनेक्शन का चयन करें
#XBUT
wizStep2=कंटेनर स्रोत का चयन करें
#XBUT
wizStep3=स्रोत ऑब्जेक्ट जोड़ें

#XMSG
limitDataset=ऑब्जेक्ट की अधिकतम संख्या पहुंच गई है. एक नए जोड़ने के लिए मौजूदा ऑब्जेक्ट को निकालें, या नई प्रतिकृति प्रवाह बनाएं.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=इस गैर-SAP लक्ष्य कनेक्शन के लिए प्रतिकृति प्रवाह प्रारंभ नहीं किया जा सकता, क्योंकि इस महीने के लिए कोई आउटबाउंड वॉल्यूम उपलब्ध नहीं है.
#XMSG
premiumOutBoundRFAdminWarningMsg=कोई व्यवस्थापक इस किराएदार के लिए प्रीमियम आउटबाउंड ब्लॉक की वृद्धि कर सकता है, जिससे इस महीने के लिए आउटबाउंड वॉल्यूम उपलब्ध हो जाएगा.
#XMSG
messageForToastForDPIDColumn2={0} ऑब्जेक्ट के लिए लक्ष्य में नया स्तंभ जोड़ा गया - ABAP-आधारित स्रोत ऑब्जेक्ट के संबंध में डुप्लिकेट रिकॉर्ड की हैंडलिंग के लिए आवश्यक है जिसमें प्राथमिक कुंजी नहीं है.
#XMSG
PremiumInboundWarningMessage=प्रतिकृति प्रवाह की संख्या और प्रतिकृति किए जाने वाले डेटा की मात्रा के आधार पर, {1} के माध्यम से डेटा की प्रतिकृति के लिए आवश्यक SAP HANA संसाधन{0} आपके किरायेदार के लिए उपलब्ध क्षमता से अधिक हो सकता है.
#XMSG
PremiumInboundWarningMsg=प्रतिकृति प्रवाह की संख्या और प्रतिकृति किए जाने वाले डेटा वॉल्यूम के आधार पर, {0} "{1}" के माध्यम से डेटा की प्रतिकृति बनाने के लिए आवश्यक SAP HANA संसाधन आपके टेनेंट के लिए उपलब्ध क्षमता से अधिक हो सकते हैं.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=प्रक्षेपण नाम दर्ज करें.
#XMSG
emptyTargetColumn=लक्ष्य स्तंभ नाम दर्ज करें.
#XMSG
emptyTargetColumnBusinessName=लक्ष्य स्तंभ व्यवसाय नाम दर्ज करें.
#XMSG
invalidTransformName=प्रक्षेपण नाम दर्ज करें.
#XMSG
uniqueColumnName=लक्ष्य स्तंभ का नाम बदलें
#XMSG
copySourceColumnLbl=स्रोत ऑब्जेक्ट से स्तंभों की प्रतिलिपि बनाएं
#XMSG
renameWarning=लक्ष्य तालिका का नाम बदलते समय एक अद्वितीय नाम चुनना सुनिश्चित करें. यदि नए नाम वाली तालिका पहले से ही स्थान पर मौजूद है, तो वह उस तालिका की परिभाषा का उपयोग करेगी.

#XMSG
uniqueColumnBusinessName=लक्ष्य स्तंभ व्यवसाय नाम का नाम बदलें.
#XMSG
uniqueSourceMapping=कोई भिन्न स्रोत स्तंभ का चयन करें.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=स्रोत स्तंभ {0} का उपयोग पहले से ही निम्न लक्ष्य स्तंभों द्वारा किया जा रहा है:{1}{1}{2}{1}{1} इस लक्ष्य स्तंभ के लिए या अन्य लक्ष्य स्तंभों के हेतु, किसी स्रोत स्तंभ का चयन करें जो प्रक्षेपण को सहेजने के लिए पहले से उपयोग में नहीं है.
#XMSG
uniqueColumnNameDescription=आपके द्वारा दर्ज किया गया लक्ष्य स्तंभ नाम पहले से मौजूद है. अपनी प्रक्षेपण को सहेजने में सक्षम होने के लिए, आपको यहां एक अद्वितीय स्तंभ नाम दर्ज करना होगा.
#XMSG
uniqueColumnBusinessNameDesc=लक्ष्य स्तंभ व्यवसाय नाम पहले से मौजूद है. प्रक्षेपण को सहेजने के लिए, आपको एक अद्वितीय स्तंभ व्यवसाय नाम दर्ज करना होगा.
#XMSG
emptySource=स्रोत स्तंभ का चयन करें या प्रत्यक्ष राशी दर्ज करें.
#XMSG
emptySourceDescription=एक मान्य मैपिंग प्रविष्टि बनाने के लिए, आपको एक स्रोत स्तंभ का चयन करना होगा या एक स्थिर मान दर्ज करना होगा.
#XMSG
emptyExpression=मैपिंग परिभाषित करें.
#XMSG
emptyExpressionDescription1=या तो उस स्रोत स्तंभ का चयन करें, जिसमे आप लक्ष्य स्तंभ को मैप करना चाहते हैं, या कॉलम {0} फंक्शन / स्थिरांक {1} में चेकबॉक्स का चयन करें. लक्ष्य डेटा प्रकार के अनुसार {2} {2} फ़ंक्शन स्वचालित रूप से दर्ज किए जाते हैं. स्थिरांक मान मैन्युअल रूप से दर्ज किए जा सकते हैं.
#XMSG
numberExpressionErr=संख्या दर्ज करें.
#XMSG
numberExpressionErrDescription=आपने अंकीय डेटा प्रकार का चयन किया है. इसका मतलब है कि आप यहां केवल अंक दर्ज कर सकते हैं, धन चिन्ह हो तो दशमलव बिंदु भी दर्ज कर सकते हैं. एकल उद्धरण चिह्नों का उपयोग न करें.
#XMSG
invalidLength=मान्य लंबाई मान दर्ज करें.
#XMSG
invalidLengthDescription=डेटा प्रकार की लंबाई स्रोत स्तंभ की लंबाई के बराबर या उससे अधिक होनी चाहिए और 1 से 5000 के बीच हो सकती है.
#XMSG
invalidMappedLength=मान्य लंबाई मान दर्ज करें.
#XMSG
invalidMappedLengthDescription=डेटा प्रकार की लंबाई स्रोत स्तंभ {0}की लंबाई के बराबर या उससे अधिक होनी चाहिए और 1 से 5000 के बीच हो सकती है.
#XMSG
invalidPrecision=मान्य परिशुद्धता मान दर्ज करें.
#XMSG
invalidPrecisionDescription=अंकों की कुल संख्या को परिशुद्धता परिभाषित करती है. दशमलव बिंदु के बाद अंकों की संख्या को पैमाना परिभाषित करता है और 0 और परिशुद्धता के बीच हो सकता है.{0}{0} उदाहरण: {0}{1} 1234.56 जैसी संख्याओं से परिशुद्धता 6 और पैमाना 2 मेल खाता है.{0}{1} 0.123546 जैसी संख्याओं से परिशुद्धता 6 और पैमाना 6 मेल खाता है.{0} {0} लक्ष्य के लिए परिशुद्धता और पैमाना किसी स्रोत हेतु परिशुद्धता और पैमाने के साथ संगत होना चाहिए, ताकि स्रोत से सभी अंक लक्ष्य फ़ील्ड में फिट हो जाएं. उदाहरण के लिए, यदि आपके पास स्रोत में परिशुद्धता 6 और पैमाना 2 (और परिणामस्वरूप दशमलव बिंदु से पहले 0 के अलावा अन्य अंक) है, तो आपके पास लक्ष्य में परिशुद्धता 6 और पैमाना 6 नहीं हो सकते हैं.
#XMSG
invalidPrimaryKey=कम से कम एक प्राथमिक कुंजी दर्ज करें.
#XMSG
invalidPrimaryKeyDescription=प्राथमिक कुंजी इस स्कीमा के लिए परिभाषित नहीं है.
#XMSG
invalidMappedPrecision=मान्य परिशुद्धता मान दर्ज करें.
#XMSG
invalidMappedPrecisionDescription1=अंकों की कुल संख्या को परिशुद्धता परिभाषित करती है. दशमलव बिंदु के बाद अंकों की संख्या को पैमाना परिभाषित करता है और 0 और परिशुद्धता के बीच हो सकता है.{0}{0} उदाहरण:{0}{1} 1234.56 जैसी संख्याओं से परिशुद्धता 6 और पैमाना 2 मेल खाता है.{0}{1} 0.123546 जैसी संख्याओं से परिशुद्धता 6 और पैमाना 6 मेल खाता है.{0}{0}डेटा प्रकार की परिशुद्धता किसी स्रोत ({2}) की परिशुद्धता के समान या उससे अधिक होना चाहिए.
#XMSG
invalidScale=मान्य पैमाना मूल्य दर्ज करें.
#XMSG
invalidScaleDescription=अंकों की कुल संख्या को परिशुद्धता परिभाषित करती है. दशमलव बिंदु के बाद अंकों की संख्या को पैमाना परिभाषित करता है और 0 और परिशुद्धता के बीच हो सकता है.{0}{0} उदाहरण: {0}{1} 1234.56 जैसी संख्याओं से परिशुद्धता 6 और पैमाना 2 मेल खाता है.{0}{1} 0.123546 जैसी संख्याओं से परिशुद्धता 6 और पैमाना 6 मेल खाता है.{0} {0} लक्ष्य के लिए परिशुद्धता और पैमाना किसी स्रोत हेतु परिशुद्धता और पैमाने के साथ संगत होना चाहिए, ताकि स्रोत से सभी अंक लक्ष्य फ़ील्ड में फिट हो जाएं. उदाहरण के लिए, यदि आपके पास स्रोत में परिशुद्धता 6 और पैमाना 2 (और परिणामस्वरूप दशमलव बिंदु से पहले 0 के अलावा अन्य अंक) है, तो आपके पास लक्ष्य में परिशुद्धता 6 और पैमाना 6 नहीं हो सकते हैं.
#XMSG
invalidMappedScale=मान्य पैमाना मूल्य दर्ज करें.
#XMSG
invalidMappedScaleDescription1=अंकों की कुल संख्या को परिशुद्धता परिभाषित करती है. दशमलव बिंदु के बाद अंकों की संख्या को पैमाना परिभाषित करता है और 0 और परिशुद्धता के बीच हो सकता है.{0}{0} उदाहरण:{0}{1} 1234.56 जैसी संख्याओं से परिशुद्धता 6 और पैमाना 2 मेल खाता है.{0}{1} 0.123546 जैसी संख्याओं से परिशुद्धता 6 और पैमाना 6 मेल खाता है.{0}{0} डेटा प्रकार का पैमाना किसी स्रोत ({2}) के पैमाने के समान या उससे अधिक होना चाहिए.
#XMSG
nonCompatibleDataType=एक संगत लक्ष्य डेटा प्रकार का चयन करें.
#XMSG
nonCompatibleDataTypeDescription1=आपके द्वारा यहां निर्दिष्ट डेटा प्रकार स्रोत डेटा प्रकार ({0}) के साथ संगत होना चाहिए. {1}{1} उदाहरण: यदि आपके स्रोत स्तंभ में डेटा प्रकार स्ट्रिंग है और इसमें अक्षर हैं, तो आप अपने लक्ष्य के लिए दशमलव डेटा प्रकार का उपयोग नहीं कर सकते.
#XMSG
invalidColumnCount=एक स्रोत स्तंभ का चयन करें.
#XMSG
ObjectStoreInvalidScaleORPrecision=परिशुद्धता और पैमाने के लिए कोई मान्य मान दर्ज करें.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=पहला मान परिशुद्धता है, जो अंकों की कुल संख्या को परिभाषित करता है. दूसरा मान पैमाना है, जो दशमलव बिंदु के बाद के अंकों को परिभाषित करता है. एक लक्ष्य पैमाना मान दर्ज करें जो स्रोत पैमाना मान से बड़ा हो और सुनिश्चित करें कि दर्ज किए गए लक्ष्य पैमाने और परिशुद्धता मान के बीच का अंतर स्रोत पैमाने और परिशुद्धता मान के बीच के अंतर से अधिक हो.
#XMSG
InvalidPrecisionORScale=परिशुद्धता और पैमाने के लिए कोई मान्य मान दर्ज करें.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=पहला मान परिशुद्धता है, जो अंकों की कुल संख्या को परिभाषित करता है. दूसरा मान पैमाना है, जो दशमलव बिंदु के बाद अंकों को परिभाषित करता है.{0}{0}चूंकि स्रोत डेटा प्रकार, Google BigQuery में समर्थित नहीं है, इसलिए इसे लक्ष्य डेटा प्रकार दशमलव में रूपांतरित किया जाता है. इस मामले में, परिशुद्धता को केवल 38 और 76 के बीच परिभाषित किया जा सकता है और पैमाने को 9 और 38 के बीच. इसके अलावा, परिशुद्धता माइनस पैमाने का परिणाम, जो दशमलव बिंदु से पहले के अंकों का प्रतिनिधित्व करता है, वह 29 और 38 के बीच होना चाहिए.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=पहला मान परिशुद्धता है, जो अंकों की कुल संख्या को परिभाषित करता है. दूसरा मान पैमाना है, जो दशमलव बिंदु के बाद अंकों को परिभाषित करता है.{0}{0}चूंकि स्रोत डेटा प्रकार, Google BigQuery में समर्थित नहीं है, इसलिए इसे लक्ष्य डेटा प्रकार दशमलव में रूपांतरित किया जाता है. इस मामले में, परिशुद्धता को 20 या उससे अधिक के तौर पर परिभाषित किया जाना चाहिए. इसके अलावा, परिशुद्धता माइनस पैमाने का परिणाम, जो दशमलव बिंदु से पहले के अंकों को दर्शाता है, वह 20 या उससे अधिक होना चाहिए.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=पहला मान परिशुद्धता है, जो अंकों की कुल संख्या को परिभाषित करता है. दूसरा मान पैमाना है, जो दशमलव बिंदु के बाद के अंकों को परिभाषित करता है.{0}{0}चूंकि स्रोत डेटा प्रकार लक्ष्य में समर्थित नहीं है, इसलिए इसे लक्ष्य डेटा प्रकार दशमलव में रूपांतरित किया जाता है. इस मामले में, परिशुद्धता को 1 से अधिक या उसके बराबर और 38 से कम या उसके बराबर किसी भी संख्या और परिशुद्धता से कम या उसके बराबर के पैमाने से परिभाषित किया जाना चाहिए.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=पहला मान परिशुद्धता है, जो अंकों की कुल संख्या को परिभाषित करता है. दूसरा मान पैमाना है, जो दशमलव बिंदु के बाद अंकों को परिभाषित करता है.{0}{0}चूंकि स्रोत डेटा प्रकार लक्ष्य में समर्थित नहीं है, इसलिए इसे लक्ष्य डेटा प्रकार दशमलव में रूपांतरित किया जाता है. इस मामले में, परिशुद्धता को 20 या उससे अधिक के तौर पर परिभाषित किया जाना चाहिए. इसके अलावा, परिशुद्धता माइनस पैमाने का परिणाम, जो दशमलव बिंदु से पहले के अंकों को दर्शाता है, वह 20 या उससे अधिक होना चाहिए.
#XMSG
invalidColumnCountDescription=एक मान्य मैपिंग प्रविष्टि बनाने के लिए, आपको एक स्रोत स्तंभ का चयन करना होगा या एक स्थिर मान दर्ज करना होगा.
#XMSG
duplicateColumns=लक्ष्य स्तंभ का नाम बदलें
#XMSG
duplicateGBQCDCColumnsDesc=लक्ष्य स्तंभ नाम Google BigQuery में आरक्षित है. प्रक्षेपण सहेजने में सक्षम होने के लिए आपको इसका नाम बदलने की आवश्यकता है.
#XMSG
duplicateConfluentCDCColumnsDesc=लक्ष्य स्तंभ नाम Confluent में आरक्षित है. प्रक्षेपण सहेजने में सक्षम होने के लिए आपको इसका नाम बदलने की आवश्यकता है.
#XMSG
duplicateSignavioCDCColumnsDesc=लक्ष्य स्तंभ नाम SAP Signavio में आरक्षित है. प्रक्षेपण सहेजने में सक्षम होने के लिए आपको इसका नाम बदलने की आवश्यकता है.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=लक्ष्य स्तंभ का नाम MS OneLake में आरक्षित है. प्रोजेक्शन को सहेजने में सक्षम होने के लिए आपको इसका नाम बदलना होगा.
#XMSG
duplicateSFTPCDCColumnsDesc=लक्ष्य स्तंभ का नाम SFTP में आरक्षित है. प्रक्षेपण को सहेजने में सक्षम होने के लिए आपको इसका नाम बदलने की आवश्यकता है.
#XMSG
GBQTargetNameWithPrefixUpdated1=लक्ष्य स्तंभ नाम में एक उपसर्ग है जो Google BigQuery में आरक्षित है. प्रक्षेपण सहेजने में सक्षम होने के लिए आपको इसका नाम बदलने की आवश्यकता है. {0}{0}लक्ष्य स्तंभ का नाम निम्न में से किसी भी स्ट्रिंग से शुरू नहीं हो सकता:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=लक्ष्य स्तंभ का नाम छोटा करें.
#XMSG
GBQtargetMaxLengthDesc=Google BigQuery में, एक स्तंभ नाम में अधिकतम 300 वर्णों का उपयोग किया जा सकता है. प्रक्षेपण सहेजने में सक्षम होने के लिए लक्ष्य स्तंभ नाम को छोटा करें.
#XMSG
invalidMappedScalePrecision=लक्ष्य के लिए परिशुद्धता और पैमाने को स्रोत के लिए परिशुद्धता और पैमाने के साथ संगत होना चाहिए ताकि स्रोत से सभी अंक लक्ष्य क्षेत्र में फिट हो जाएं.
#XMSG
invalidMappedScalePrecisionShortText=एक मान्य परिशुद्धता और पैमाना मान दर्ज करें.
#XMSG
validationIncompatiblePKTypeDescProjection3=एक या एकाधिक स्रोत स्तंभ में ऐसे डेटा प्रकार हैं जिन्हें Google BigQuery में प्राथमिक कुंजी के रूप में परिभाषित नहीं किया जा सकता. लक्ष्य ऑब्जेक्ट में कोई भी प्राथमिक कुंजी नहीं बनाई जाएगी.{0}{0}निम्नलिखित लक्ष्य डेटा प्रकार Google BigQuery डेटा प्रकारों के साथ संगत हैं जिनके लिए प्राथमिक कुंजी परिभाषित की जा सकती है:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=स्तंभ __message_id को अनचेक करें.
#XMSG
uncheckColumnMessageIdDesc=स्तंभ: प्राथमिक कुंजी
#XMSG
validationOpCodeInsert=आपको सम्मिलित करने के लिए एक मान दर्ज करना होगा।
#XMSG
recommendDifferentPrimaryKey=हम अनुशंसा करते हैं कि आप आइटम स्तर पर एक अलग प्राथमिक कुंजी चुनें.
#XMSG
recommendDifferentPrimaryKeyDesc=जब ऑपरेशन कोड पहले से ही परिभाषित है, तो उदाहरण के लिए स्तंभ दोहराव जैसी समस्याओं से बचने के लिए, सरणी अनुक्रमणिका और आइटम के लिए अलग-अलग प्राथमिक कुंजियों का चयन करने की अनुशंसा की जाती है.
#XMSG
selectPrimaryKeyItemLevel=आपको शीर्षलेख और आइटम स्तर दोनों के लिए कम से कम एक प्राथमिक कुंजी का चयन करना होगा.
#XMSG
selectPrimaryKeyItemLevelDesc=जब किसी सारणी या मानचित्र का विस्तार किया जाता है, तो आपको दो प्राथमिक कुंजियों का चयन करना होगा, एक शीर्षलेख  स्तर पर और एक आइटम स्तर पर.
#XMSG
invalidMapKey=आपको शीर्षलेख स्तर पर कम से कम एक प्राथमिक कुंजी का चयन करना होगा.
#XMSG
invalidMapKeyDesc=जब कोई सारणी या मानचित्र विस्तारित किया जाता है, तो आपको शीर्षलेख स्तर पर एक प्राथमिक कुंजी का चयन करना होगा.
#XFLD
txtSearchFields=खोज लक्ष्य स्तंभ
#XFLD
txtName=नाम
#XMSG
txtSourceColValidation=एक या अधिक स्रोत स्तंभो को समर्थित नहीं किया गयाः
#XMSG
txtMappingCount=मैपिंग ({0})
#XMSG
schema=स्कीमा
#XMSG
sourceColumn=स्रोत स्तंभ
#XMSG
warningSourceSchema=स्कीमा में किया गया कोई भी परिवर्तन प्रोजेक्शन डायलॉग में मैपिंग को प्रभावित करेगा.
#XCOL
txtTargetColName=लक्ष्य स्तंभ (तकनीकी नाम)
#XCOL
txtDataType=लक्ष्य डेटा प्रकार
#XCOL
txtSourceDataType=स्रोत डेटा प्रकार
#XCOL
srcColName=स्त्रोत मान (तकनीकी नाम)
#XCOL
precision=निवारण
#XCOL
scale=पैमाना
#XCOL
functionsOrConstants=फ़ंक्शन/स्थिरांक
#XCOL
txtTargetColBusinessName=लक्ष्य स्तंभ (व्यवसाय नाम)
#XCOL
prKey=प्राथमिक कुंजी
#XCOL
txtProperties=गुण
#XBUT
txtOK=सहेजें
#XBUT
txtCancel=रद्द करें
#XBUT
txtRemove=निकालें
#XFLD
txtDesc=वर्णन
#XMSG
rftdMapping=मैपिंग
#XFLD
@lblColumnDataType=डेटा प्रकार
#XFLD
@lblColumnTechnicalName=तकनीकी नाम
#XBUT
txtAutomap=ऑटो-मैप
#XBUT
txtUp=ऊपर
#XBUT
txtDown=डाउन

#XTOL
txtTransformationHeader=प्रोजेक्शन
#XTOL
editTransformation=संपादित करें
#XTOL
primaryKeyToolip=कुंजी


#XMSG
rftdFilter=फ़िल्टर करें
#XMSG
rftdFilterColumnCount=स्रोत: {0}({1})
#XTOL
rftdFilterColSearch=खोजें
#XMSG
rftdFilterColNoData=प्रदर्शन के लिए कोई स्तंभ नहीं
#XMSG
rftdFilteredColNoExps=कोई फ़िल्टर व्यंजक नहीं
#XMSG
rftdFilterSelectedColTxt=इसके लिए फिल्टर जोड़ें
#XMSG
rftdFilterTxt=इसके लिए फ़िल्टर उपलब्ध है
#XBUT
rftdFilterSelectedAddColExp=व्यंजक जोड़ें
#YINS
rftdFilterNoSelectedCol=फिल्टर जोड़ने के लिए स्तंभ का चयन करें.
#XMSG
rftdFilterExp=व्यंजक फिल्टर करें
#XMSG
rftdFilterNotAllowedColumn=इस स्तंभ के लिए फ़िल्टर जोड़ना समर्थित नहीं है.
#XMSG
rftdFilterNotAllowedHead=समर्थित स्तंभ नहीं है.
#XMSG
rftdFilterNoExp=कोई फिल्टर परिभाषित किया गया
#XTOL
rftdfilteredTt=फ़िल्टर किया गया
#XTOL
rftdremoveexpTt=फिल्टर व्यंजक निकालें
#XTOL
validationMessageTt=सत्यापन संदेश
#XTOL
rftdFilterDateInp=दिनांक का चयन करें
#XTOL
rftdFilterDateTimeInp=दिनांक समय का चयन करें
#XTOL
rftdFilterTimeInp=समय चयन करें
#XTOL
rftdFilterInp=मान दर्ज करें
#XMSG
rftdFilterValidateEmptyMsg={1} स्तंभ पर {0} फ़िल्टर व्यंजक खाली है
#XMSG
rftdFilterValidateInvalidNumericMsg={1} स्तंभ पर {0} फ़िल्टर व्यंजकों में अमान्य अंकीय मान शामिल हैं
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=फ़िल्टर व्यंजक में मान्य अंकीय मान शामिल होने चाहिए
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=यदि लक्ष्य ऑब्जेक्ट स्कीमा परिवर्तित किया गया है, तो परिवर्तनों को अनुकूलित करने के लिए मुख्य पृष्ठ पर फ़ंक्शन "मौजूदा लक्ष्य ऑब्जेक्ट को मैप करें" का उपयोग करें और लक्ष्य ऑब्जेक्ट को उसके स्रोत के साथ फिर से रीमैप करें.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=यदि लक्ष्य तालिका पहले मौजूद है और मैपिंग में एक स्कीमा परिवर्तन शामिल है, आपको प्रतिकृति प्रवाह को परिनियोजित करने से पहले तदनुसार लक्ष्य तालिका को बदला जाएगा.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=यदि आपकी मैपिंग में स्कीमा परिवर्तन शामिल है, तो आपको प्रतिकृति प्रवाह को परिनियोजित करने से पहले लक्ष्य तालिका को उसके अनुसार बदलना होगा.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=निम्न असमर्थित स्तंभों को स्रोत परिभाषा से छोड़ दिया गया: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=निम्न असमर्थित स्तंभों को लक्ष्य परिभाषा से छोड़ दिया गया: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=निम्नलिखित ऑब्जेक्ट समर्थित नहीं हैं क्योंकि वे उपभोग के लिए एक्सपोज किए गए हैं:  {0} {1} {0} {0}प्रतिकृति प्रवाह में तालिकाओं का उपयोग करने के लिए, सिमेंटिक उपयोग (तालिका सेटिंग में) {2} विश्लेषणात्मक {2} डेटासेट पर सेट नहीं किया जाना चाहिए.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=लक्ष्य ऑब्जेक्ट का उपयोग नहीं किया जा सकता क्योंकि वह उपभोग के लिए एक्सपोज किए गए हैं.  {0} {0} प्रतिकृति प्रवाह में तालिका का उपयोग करने के लिए, सिमेंटिक उपयोग (तालिका सेटिंग में) को {1} विश्लेषणात्मक डेटासेट {1}पर सेट नहीं किया जाना चाहिए.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=इस नाम का एक लक्ष्य ऑब्जेक्ट पहले से मौजूद है. हालाँकि, इसका उपयोग नहीं किया जा सकता क्योंकि यह उपभोग के लिए एक्सपोज किए गए है. {0} {0}प्रतिकृति प्रवाह में तालिका का उपयोग करने के लिए, सिमेंटिक उपयोग (तालिका सेटिंग में) को  {1}विश्लेषणात्मक डेटासेट{1} पर सेट नहीं किया जाना चाहिए.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=इस नाम का एक ऑब्जेक्ट लक्ष्य में पहले से मौजूद है. {0}हालांकि, इस ऑब्जेक्ट को स्थानीय रिपॉजिटरी में प्रतिकृति प्रवाह के लिए लक्ष्य ऑब्जेक्ट के रूप में उपयोग नहीं किया जा सकता, क्योंकि यह स्थानीय तालिका नहीं है.
#XMSG:
targetAutoRenameUpdated=लक्ष्य स्तंभ का नाम बदल दिया गया है.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Google BigQuery में प्रतिकृति की अनुमति देने के लिए लक्ष्य स्तंभ का नाम बदल दिया गया है. यह निम्न कारणों में से एक के कारण है:{0} {1}{2}आरक्षित स्तंभ नाम{3}{2}समर्थित वर्ण नहीं{3}{2}आरक्षित उपसर्ग{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=डेटास्फीयर में प्रतिकृति की अनुमति देने के लिए लक्ष्य कॉलम का नाम बदल दिया गया है. यह निम्न में से किसी एक कारण से है:{0} {1}{2}आरक्षित स्तंभ नाम{3}{2}समर्थित वर्ण नहीं{3}{2}आरक्षित उपसर्ग{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=लक्ष्य में प्रतिकृति की अनुमति देने के लिए लक्ष्य कॉलम का नाम बदल दिया गया है. यह निम्न में से किसी एक कारण से है: {0} {1}{2}असमर्थित वर्ण{3}{2}आरक्षित उपसर्ग{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=लक्ष्य में प्रतिकृति की अनुमति देने के लिए लक्ष्य कॉलम का नाम बदल दिया गया है. यह निम्न में से किसी एक कारण से है: {0} {1}{2}आरक्षित स्तंभ नाम {3}{2}समर्थित वर्ण नहीं{3}{2}आरक्षित उपसर्ग{3}{4}
#XMSG:
targetAutoDataType=लक्ष्य डेटा प्रकार बदल दिए गए हैं.
#XMSG:
targetAutoDataTypeDesc=लक्ष्य डेटा प्रकार को बदल दिया गया है {0}क्योंकि स्रोत डेटा प्रकार Google BigQuery में समर्थित नहीं है.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=लक्ष्य डेटा प्रकार को {0} में परिवर्तित कर दिया गया है, क्योंकि स्रोत डेटा प्रकार लक्ष्य कनेक्शन में समर्थित नहीं है.
#XMSG
projectionGBQUnableToCreateKey=प्राथमिक कुंजियां नहीं बनाई जाएंगी.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=Google BigQuery में, अधिकतम 16 प्राथमिक कुंजियां समर्थित हैं, लेकिन स्रोत ऑब्जेक्ट में प्राथमिक कुंजियों की एक बड़ी संख्या है. लक्ष्य ऑब्जेक्ट में कोई भी प्राथमिक कुंजी नहीं बनाई जाएगी.
#XMSG
HDLFNoKeyError=एक या अधिक स्तंभ को प्राथमिक कुंजी के रूप में निर्धारित करें.
#XMSG
HDLFNoKeyErrorDescription=किसी ऑब्जेक्ट को प्रतिकृति करने के लिए, आपको एक या अधिक स्तंभ को प्राथमिक कुंजी के रूप में निर्धारित करना होगा.
#XMSG
HDLFNoKeyErrorExistingTarget=एक या अधिक स्तंभ को प्राथमिक कुंजी के रूप में निर्धारित करें.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=मौजूदा लक्ष्य ऑब्जेक्ट में डेटा को दोहराने के लिए, इसमें एक या अधिक कॉलम होने चाहिए जिन्हें प्राथमिक कुंजी के रूप में परिभाषित किया गया है. {0} {0} आपके पास एक या अधिक स्तंभ को प्राथमिक कुंजी के रूप में परिभाषित करने के लिए निम्नलिखित विकल्प हैं: {0}{0}{1} मौजूदा लक्ष्य ऑब्जेक्ट को बदलने के लिए स्थानीय तालिका संपादक का उपयोग करें. फिर प्रतिकृति प्रवाह को पुनः लोड करें.{0}{0}{1} प्रतिकृति प्रवाह में लक्ष्य ऑब्जेक्ट का नाम बदलें. यह रन शुरू होते ही एक नई ऑब्जेक्ट बनाएगा. नाम बदलने के बाद, आप प्रक्षेपण में एक या अधिक स्तंभ को प्राथमिक कुंजी के रूप में परिभाषित कर सकते हैं.{0}{0}{1} ऑब्जेक्ट को किसी अन्य मौजूदा लक्ष्य ऑब्जेक्ट पर मैप करें जिसमें एक या अधिक स्तंभ पहले से ही प्राथमिक कुंजी के रूप में परिभाषित हैं.
#XMSG
HDLFSourceTargetDifferentKeysWarning=प्राथमिक कुंजी परिवर्तित.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=स्रोत ऑब्जेक्ट की तुलना में, आपने लक्ष्य ऑब्जेक्ट के लिए प्राथमिक कुंजी के रूप में विभिन्न स्तंभ को परिभाषित किया है. सुनिश्चित करें कि ये कॉलम बाद में डेटा की प्रतिलिपि बनाते समय संभावित डेटा भ्रष्टाचार से बचने के लिए सभी पंक्तियों की विशिष्ट रूप से पहचान करते हैं. {0} {0} स्रोत ऑब्जेक्ट में, निम्नलिखित स्तंभ को प्राथमिक कुंजी के रूप में परिभाषित किया गया है: {0} {1}
#XMSG
duplicateDPIDColumns=लक्ष्य स्तंभ का नाम बदलें
#XMSG
duplicateDPIDDColumnsDesc1=यह लक्ष्य स्तंभ नाम एक तकनीकी स्तंभ के लिए आरक्षित है. प्रक्षेपण को सहेजने के लिए कोई दूसरा नाम दर्ज करें.
#XMSG:
targetAutoRenameDPID=लक्ष्य स्तंभ का नाम बदल दिया गया है.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=बिना कुंजियों के ABAP स्रोत से प्रतिकृति की अनुमति देने के लिए लक्ष्य स्तंभ का नाम बदल दिया गया है. ऐसा निम्न कारणों में से एक के कारण है:{0} {1}{2}आरक्षित स्तंभ नाम{3}{2}समर्थित वर्ण नहीं{3}{2}आरक्षित उपसर्ग{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle={0} लक्ष्य सेटिंग्स
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle={0} स्रोत सेटिंग
#XBUT
connectionSettingSave=सहेजें
#XBUT
connectionSettingCancel=रद्द करें
#XBUT: Button to keep the object level settings
txtKeep=रखें
#XBUT: Button to overwrite the Object level settings
txtOverwrite=ओवरराइट करें
#XFLD
targetConnectionThreadlimit=आरंभिक भार के लिए लक्ष्य थ्रेड सीमा (1-100)
#XFLD
connectionThreadLimit=आरंभिक भार के लिए स्रोत थ्रेड सीमा (1-100)
#XFLD
maxConnection=प्रतिकृति थ्रेड सीमा (1-100)
#XFLD
kafkaNumberOfPartitions=विभाजन की संख्या
#XFLD
kafkaReplicationFactor=प्रतिकृति कारक
#XFLD
kafkaMessageEncoder=संदेश एन्कोडर
#XFLD
kafkaMessageCompression=संदेश संपीड़न
#XFLD
fileGroupDeltaFilesBy=इसके द्वारा समूह डेल्टा
#XFLD
fileFormat=फ़ाइल प्रकार
#XFLD
csvEncoding=CSV एन्कोडिंग
#XFLD
abapExitLbl=ABAP निकास
#XFLD
deltaPartition=डेल्टा लोड के लिए ऑब्जेक्ट थ्रेड गणना (1-10)
#XFLD
clamping_Data=डेटा छंटाई पर विफलता
#XFLD
fail_On_Incompatible=असंगत डेटा पर विफल हुआ
#XFLD
maxPartitionInput=विभाजनों की अधिकतम संख्या
#XFLD
max_Partition=विभाजनों की अधिकतम संख्या परिभाषित करें
#XFLD
include_SubFolder=उप फ़ोल्डर शामिल करें
#XFLD
fileGlobalPattern=फ़ाइल नाम के लिए वैश्विक पैटर्न
#XFLD
fileCompression=फ़ाइल संपीड़न
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=फ़ाइल परिसीमक
#XFLD
fileIsHeaderIncluded=फ़ाइल शीर्षलेख
#XFLD
fileOrient=उन्मुख
#XFLD
gbqWriteMode=मोड लिखें
#XFLD
suppressDuplicate=डुप्लिकेट को दबाएँ
#XFLD
apacheSpark=अपाचे स्पार्क अनुकूलता सक्षम करें
#XFLD
clampingDatatypeCb=दशमलव फ़्लोटिंग पॉइंट डेटा प्रकार को कस कर रखें
#XFLD
overwriteDatasetSetting=ऑब्जेक्ट स्तर पर लक्ष्य सेटिंग को अधिलेखित करें
#XFLD
overwriteSourceDatasetSetting=ऑब्जेक्ट स्तर पर लक्ष्य सेटिंग को अधिलेखित करें
#XMSG
kafkaInvalidConnectionSetting={0} और {1} के बीच संख्या दर्ज करें.
#XMSG
MinReplicationThreadErrorMsg={0} से बड़ी संख्या दर्ज करें.
#XMSG
MaxReplicationThreadErrorMsg={0} से कम संख्या दर्ज करें.
#XMSG
DeltaThreadErrorMsg=1 और 10 के बीच कोई मान दर्ज करें.
#XMSG
MaxPartitionErrorMsg=1 <= x <= 2147483647 के बीच मान दर्ज करें. डिफ़ॉल्ट मान 10 है.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal={0} और {1} के बीच कोई पूर्णांक दर्ज करें.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=ब्रोकर के प्रतिकृति कारक का उपयोग करें
#XFLD
serializationFormat=क्रमांकन स्वरूप
#XFLD
compressionType=संपीड़न प्रकार
#XFLD
schemaRegistry=स्कीमा रजिस्ट्री का उपयोग करें
#XFLD
subjectNameStrat=विषय नाम रणनीति
#XFLD
compatibilityType=संगतता प्रकार
#XFLD
confluentTopicName=विषय का नाम
#XFLD
confluentRecordName=रिकार्ड नाम
#XFLD
confluentSubjectNamePreview=विषय का नाम पूर्वावलोकन
#XMSG
serializationChangeToastMsgUpdated2=स्कीमा रजिस्ट्री सक्षम नहीं है, इसलिए क्रमांकन स्वरूप JSON में परिवर्तित किया गया. क्रमांकन स्वरूप को वापस AVRO में परिवर्तित करने के लिए, आपको पहले स्कीमा रजिस्ट्री को सक्षम करना होगा.
#XBUT
confluentTopicNameInfo=विषय का नाम हमेशा लक्ष्य ऑब्जेक्ट के नाम पर आधारित होता है. आप लक्ष्य ऑब्जेक्ट का नाम बदलकर इसमें परिवर्तन कर सकते हैं.
#XMSG
emptyRecordNameValidationHeaderMsg=कोई रिकॉर्ड नाम दर्ज करें.
#XMSG
emptyPartionHeader=विभाजनों की संख्या दर्ज करें.
#XMSG
invalidPartitionsHeader=विभाजनों की मान्य संख्या दर्ज करें.
#XMSG
invalidpartitionsDesc=1 और 200,000 के बीच की कोई संख्या दर्ज करें.
#XMSG
emptyrFactorHeader=कोई प्रतिकृति कारक दर्ज करें.
#XMSG
invalidrFactorHeader=कोई मान्य प्रतिकृति कारक दर्ज करें.
#XMSG
invalidrFactorDesc=1 और 32,767 के बीच की कोई संख्या दर्ज करें.
#XMSG
emptyRecordNameValidationDescMsg=यदि क्रमांकन स्वरूप "AVRO" का उपयोग किया जाता है, तो केवल निम्न वर्ण समर्थित हैं:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(अंडरस्कोर)
#XMSG
validRecordNameValidationHeaderMsg=कोई मान्य रिकॉर्ड नाम दर्ज करें.
#XMSG
validRecordNameValidationDescMsgUpdated=चूंकि क्रमांकन स्वरूप "AVRO" का उपयोग किया गया है, इसलिए रिकॉर्ड नाम केवल अल्फ़ान्यूमेरिक (A-Z, a-z, 0-9) और अंडरस्कोर (_) वर्णों से मिलकर बना होना चाहिए. यह एक अक्षर या अंडरस्कोर से प्रारंभ होना चाहिए.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=जैसे ही एक या अधिक ऑब्जेक्ट में लोड प्रकार "आरंभिक और डेल्टा" होता है, "डेल्टा लोड के लिए ऑब्जेक्ट थ्रेड गणना" सेट की जा सकती है,
#XMSG
invalidTargetName=अमान्य स्तंभ नाम
#XMSG
invalidTargetNameDesc=लक्ष्य स्तंभ नाम में केवल अल्फ़ान्यूमेरिक (A-Z, a-z, 0-9) और अंडरस्कोर (_) वर्ण शामिल होने चाहिए.
#XFLD
consumeOtherSchema=अन्य स्कीमा संस्करणों का उपभोग करें
#XFLD
ignoreSchemamissmatch=स्कीमा बेमेल को अनदेखा करें
#XFLD
confleuntDatatruncation=डेटा छंटाई पर विफलता
#XFLD
isolationLevel=आइसोलेशन स्तर
#XFLD
confluentOffset=प्रारंभ बिंदु
#XFLD
signavioGroupDeltaFilesByText=कोई नहीं
#XFLD
signavioFileFormatText=पार्केट 
#XFLD
signavioSparkCompatibilityParquetText=नहीं
#XFLD
siganvioFileCompressionText=स्न्यापी 
#XFLD
siganvioSuppressDuplicatesText=नहीं

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=प्रोजेक्शन
#XBUT
txtAdd=जोड़ें
#XBUT
txtEdit=संपादित करें
#XMSG
transformationText=फ़िल्टर या मैपिंग सेट अप के लिए प्रोजेक्शन जोड़ें.
#XMSG
primaryKeyRequiredText=स्कीमा कॉन्फ़िगर करें के साथ एक प्राथमिक कुंजी का चयन करें.
#XFLD
lblSettings=सेटिंग
#XFLD
lblTargetSetting={0}: लक्ष्य सेटिंग
#XMSG
@csvRF=उस फ़ाइल का चयन करें जिसमें वह स्कीमा परिभाषा है जिसे आप फ़ोल्डर की सभी फ़ाइलों पर लागू करना चाहते हैं.
#XFLD
lblSourceColumns=स्रोत स्तंभ
#XFLD
lblJsonStructure=JSON संरचना
#XFLD
lblSourceSetting={0}: स्रोत सेटिंग
#XFLD
lblSourceSchemaSetting={0}: स्रोत स्कीमा सेटिंग
#XBUT
messageSettings=संदेश सेटिंग
#XFLD
lblPropertyTitle1=ऑब्जेक्ट गुण
#XFLD
lblRFPropertyTitle=प्रतिकृति प्रवाह गुण
#XMSG
noDataTxt=प्रदर्शित करने के लिए कोई कॉलम नहीं है.
#XMSG
noTargetObjectText=कोई लक्ष्य ऑब्जेक्ट चयनित नहीं है.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=लक्ष्य स्तंभ
#XMSG
searchColumns=स्तंभ खोजें
#XTOL
cdcColumnTooltip=डेल्टा कैप्चर के लिए स्तंभ
#XMSG
sourceNonDeltaSupportErrorUpdated=स्रोत ऑब्जेक्ट डेल्टा कैप्चर का समर्थन नहीं करता है.
#XMSG
targetCDCColumnAdded=डेल्टा कैप्चर के लिए 2 लक्ष्य स्तंभ जोड़े गए.
#XMSG
deltaPartitionEnable=डेल्टा लोड के लिए ऑब्जेक्ट थ्रेड सीमा स्रोत सेटिंग में जोड़ी गई.
#XMSG
attributeMappingRemovalTxt=अमान्य मैपिंग को हटाना जो नए लक्ष्य ऑब्जेक्ट के लिए समर्थित नहीं हैं.
#XMSG
targetCDCColumnRemoved=डेल्टा कैप्चर के लिए उपयोग किए गए 2 लक्ष्य स्तंभ निकाल दिए गए.
#XMSG
replicationLoadTypeChanged=लोड प्रकार "आरंभिक और डेल्टा" में परिवर्तित किया गया.
#XMSG
sourceHDLFLoadTypeError=लोड प्रकार को "आरंभिक और डेल्टा" में बदलें.
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=कनेक्शन प्रकार SAP HANA Cloud, डेटा लेक फ़ाइल से SAP Datasphere के साथ किसी स्रोत कनेक्शन से किसी ऑब्जेक्ट को प्रतिकृति करने के लिए, आपको लोड प्रकार "आरंभिक और डेल्टा" का उपयोग करना होगा.
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=डेल्टा कैप्चर सक्षम करें.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=कनेक्शन प्रकार SAP HANA Cloud, डेटा लेक फ़ाइल से SAP Datasphere के साथ किसी स्रोत कनेक्शन से किसी ऑब्जेक्ट को प्रतिकृति करने के लिए, आपको डेल्टा कैप्चर सक्षम करना होगा.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=लक्ष्य ऑब्जेक्ट बदलें.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=लक्ष्य ऑब्जेक्ट का उपयोग नहीं किया जा सकता क्योंकि डेल्टा कैप्चर अक्षम है. आप या तो लक्ष्य ऑब्जेक्ट का नाम बदल सकते हैं (जो डेल्टा कैप्चर के साथ कोई नया ऑब्जेक्ट बनाने की अनुमति देता है) या इसे डेल्टा कैप्चर सक्षम के साथ मौजूदा ऑब्जेक्ट में मैप कर सकते हैं.
#XMSG
deltaPartitionError=डेल्टा लोड के लिए मान्य ऑब्जेक्ट थ्रेड गिनती दर्ज करें.
#XMSG
deltaPartitionErrorDescription=1 और 10 के बीच कोई मान दर्ज करें.
#XMSG
deltaPartitionEmptyError=डेल्टा लोड के लिए ऑब्जेक्ट थ्रेड गिनती दर्ज करें.
#XFLD
@lblColumnDescription=वर्णन
#XMSG
@lblColumnDescriptionText1=तकनीकी उद्देश्य के लिए - ABAP-आधारित स्रोत ऑब्जेक्ट्स की प्रतिकृति के दौरान उत्पन्न समस्याओं के कारण डुप्लिकेट रिकॉर्डों का प्रबंधन, जिनमें प्राथमिक कुंजी नहीं होती है.
#XFLD
storageType=संग्रहण
#XFLD
skipUnmappedColLbl=अप्रयुक्त स्तंभ छोड़ दें
#XFLD
abapContentTypeLbl=सामग्री प्रकार
#XFLD
autoMergeForTargetLbl=डेटा स्वचालित रूप से मर्ज करें
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=सामान्य
#XFLD
lblBusinessName=बिज़नेस का नाम
#XFLD
lblTechnicalName=तकनीकी नाम
#XFLD
lblPackage=पैकेज
#XFLD
statusPanel=स्थिति रन करें
#XBTN: Schedule dropdown menu
SCHEDULE=शेड्यूल
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=शेड्यूल संपादित करें
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=शेड्यूल हटाएं
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=शेड्यूल बनाएं
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=शेड्यूल सत्यापन जांच विफल
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=शेड्यूल नहीं बनाया जा सकता क्योंकि प्रतिकृति प्रवाह वर्तमान में नियोजित किया जा रहा है. {0}कृपया प्रतिकृति प्रवाह नियोजित होने तक प्रतीक्षा करें.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=प्रतिकृति प्रवाह के लिए जिसमें लोड प्रकार "प्रारंभिक और डेल्टा" वाले ऑब्जेक्ट शामिल हैं, कोई शेड्यूल नहीं बनाया जा सकता है.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=उन प्रतिकृति प्रवाह, जिनमें लोड प्रकार "आरंभिक और डेल्टा/केवल डेल्टा" वाले ऑब्जेक्ट शामिल हैं, इनके लिए कोई शेड्यूल नहीं बनाया जा सकता.
#XFLD : Scheduled popover
SCHEDULED=शेड्यूल किया गया
#XFLD
CREATE_REPLICATION_TEXT=एक प्रतिकृति प्रवाह बनाएं
#XFLD
EDIT_REPLICATION_TEXT=प्रतिकृति प्रवाह संपादित करें
#XFLD
DELETE_REPLICATION_TEXT=एक प्रतिकृति प्रवाह हटाएं
#XFLD
REFRESH_FREQUENCY=आवृत्ति
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=प्रतिकृति प्रवाह को तैनात नहीं किया जा सकता क्योंकि मौजूदा शेड्यूल {0} अभी तक लोड प्रकार "प्रारंभिक और डेल्टा" का समर्थन नहीं करता है. {0}{0}प्रतिकृति प्रवाह को तैनात करने के लिए, आपको सभी ऑब्जेक्ट{0} के लोड प्रकार को "केवल प्रारंभिक" पर सेट करना होगा. वैकल्पिक रूप से, आप शेड्यूल को हटा सकते हैं, {0}प्रतिकृति प्रवाह को तैनात कर सकते हैं और फिर कोई नया रन प्रारंभ कर सकते हैं. इसके परिणामस्वरूप {0}समाप्ति के बिना कोई रन होता है, जो लोड प्रकार "प्रारंभिक और डेल्टा" वाले ऑब्जेक्ट का भी समर्थन करता है.
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=प्रतिकृति प्रवाह को तैनात नहीं किया जा सकता क्योंकि मौजूदा शेड्यूल {0} अभी तक लोड प्रकार "केवल प्रारंभिक और डेल्टा/डेल्टा" का समर्थन नहीं करता है. {0}{0}प्रतिकृति प्रवाह को तैनात करने के लिए, आपको सभी ऑब्जेक्ट{0} के लोड प्रकार को "केवल प्रारंभिक" पर सेट करना होगा. वैकल्पिक रूप से, आप शेड्यूल को हटा सकते हैं, {0}प्रतिकृति प्रवाह को तैनात कर सकते हैं और फिर कोई नया रन प्रारंभ कर सकते हैं. इसके परिणामस्वरूप {0}समाप्ति के बिना कोई रन होता है, जो लोड प्रकार "प्रारंभिक और डेल्टा" वाले ऑब्जेक्ट का भी समर्थन करता है.
#XMSG
SCHEDULE_EXCEPTION=शेड्यूल विवरण प्राप्त करने में विफल
#XFLD: Label for frequency column
everyLabel=प्रत्येक
#XFLD: Plural Recurrence text for Hour
hoursLabel=घंटे
#XFLD: Plural Recurrence text for Day
daysLabel=दिन
#XFLD: Plural Recurrence text for Month
monthsLabel=महीने
#XFLD: Plural Recurrence text for Minutes
minutesLabel=मिनट
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=शेड्यूल संभावना के बारे में जानकारी प्राप्त करने में विफल.
#XFLD :Paused field
PAUSED=रोका गया
#XMSG
navToMonitoring=प्रवाह मॉनिटर में खोलें
#XFLD
statusLbl=स्थिति
#XFLD
lblLastRunExecuted=अंतिम बार रन प्रारंभ करें
#XFLD
lblLastExecuted=अंतिम रन
#XFLD: Status text for Completed
statusCompleted=पूर्ण किया गया
#XFLD: Status text for Running
statusRunning=चल रहा है
#XFLD: Status text for Failed
statusFailed=विफल
#XFLD: Status text for Stopped
statusStopped=रोका गया
#XFLD: Status text for Stopping
statusStopping=रोका जा रहा है
#XFLD: Status text for Active
statusActive=सक्रिय करें
#XFLD: Status text for Paused
statusPaused=रोका गया
#XFLD: Status text for not executed
lblNotExecuted=अभी तक नहीं चला
#XFLD
messagesSettings=संदेश सेटिंग
#XTOL
@validateModel=सत्यापन संदेश
#XTOL
@hierarchy=पदानुक्रम
#XTOL
@columnCount=स्तंभो की संख्या
#XMSG
VAL_PACKAGE_CHANGED=आपने इस ऑब्जेक्ट को पैकेज ''{1}'' को असाइन किया है. इस परिवर्तन की पुष्टि और सत्यापन करने के लिए “सहेजें” पर क्लिक करें. ध्यान दें कि आपके द्वारा सहेजे जाने के बाद, इस संपादक में किसी पैकेज का असाइनमेंट पूर्ववत नहीं किया जा सकता.
#XMSG
MISSING_DEPENDENCY=ऑब्जेक्ट ''{0}''’ की निर्भरता को पैकेज ''{1}''’ में हल नहीं किया जा सकता.
#XFLD
deltaLoadInterval=डेल्टा लोड अंतराल
#XFLD
lblHour=घंटे (0-24)
#XFLD
lblMinutes=मिनट (0-59)
#XMSG
maxHourOrMinErr=0 और {0} के बीच मान दर्ज करें.
#XMSG
maxDeltaInterval=डेल्टा लोड अंतराल का अधिकतम मान 24 घंटे है.{0}तदनुसार मिनट मान या घंटे का मान बदलें.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=लक्ष्य कंटेनर पथ
#XFLD
confluentSubjectName=विषय का नाम
#XFLD
confluentSchemaVersion=स्कीमा संस्करण
#XFLD
confluentIncludeTechKeyUpdated=तकनीकी कुंजी शामिल करें
#XFLD
confluentOmitNonExpandedArrays=गैर-विस्तारित सारणी को छोड़ें
#XFLD
confluentExpandArrayOrMap=सारणी या मानचित्र का विस्तार करें
#XCOL
confluentOperationMapping=संचालन मैपिंग
#XCOL
confluentOpCode=ऑपकोड
#XFLD
confluentInsertOpCode=सम्मिलित करें
#XFLD
confluentUpdateOpCode=अपडेट करें
#XFLD
confluentDeleteOpCode=हटाएं
#XFLD
expandArrayOrMapNotSelectedTxt=चयनित नहीं किया गया
#XFLD
confluentSwitchTxtYes=हां
#XFLD
confluentSwitchTxtNo=नहीं
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=त्रुटि
#XTIT
executeWarning=चेतावनी
#XMSG
executeunsavederror=इसे रन करने से पहले अपने प्रतिकृति प्रवाह को सहेजें.
#XMSG
executemodifiederror=प्रतिकृति प्रवाह में सहेजे नहीं गए परिवर्तन हैं। कृपया प्रतिकृति प्रवाह सहेजें.
#XMSG
executeundeployederror=इससे पहले कि आप इसे रन कर सकें, आपको अपने प्रतिकृति प्रवाह को परिनियोजित करना होगा.
#XMSG
executedeployingerror=कृपया परिनियोजन समाप्त होने तक प्रतीक्षा करें.
#XMSG
msgRunStarted=रन प्रारंभ किया गया
#XMSG
msgExecuteFail=प्रतिकृति प्रवाह रन करने में विफल हुआ
#XMSG
titleExecuteBusy=कृपया प्रतीक्षा करें.
#XMSG
msgExecuteBusy=प्रतिकृति प्रवाह रन करने के लिए हम आपका डेटा तैयार कर रहे हैं.
#XTIT
executeConfirmDialog=चेतावनी
#XMSG
msgExecuteWithValidations=प्रतिकृति प्रवाह में सत्यापन त्रुटियां हैं. प्रतिकृति प्रवाह रन करने से विफलता हो सकती है.
#XMSG
msgRunDeployedVersion=परिनियोजन के लिए परिवर्तन हैं. प्रतिकृति प्रवाह का अंतिम परिनियोजित संस्करण रन किया जाएगा. क्या आप जारी रखना चाहते हैं?
#XBUT
btnExecuteAnyway=फिर भी रन करें
#XBUT
btnExecuteClose=बंद करें
#XBUT
loaderClose=बंद करें
#XTIT
loaderTitle=लोडिंग
#XMSG
loaderText=सर्वर से विवरण फ़ेच करना
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=इस गैर-SAP लक्ष्य कनेक्शन के लिए प्रतिकृति प्रवाह प्रारंभ नहीं किया जा सकता
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=क्योंकि इस महीने के लिए कोई आउटबाउंड वॉल्यूम उपलब्ध नहीं है.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=कोई व्यवस्थापक इस किराएदार के लिए, प्रीमियम आउटबाउंड ब्लॉक की वृद्धि कर सकता है,
#XMSG
premiumOutBoundRFAdminErrMsgPart2=जिससे इस महीने के लिए आउटबाउंड वॉल्यूम उपलब्ध हो जाएगा.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=त्रुटि
#XTIT
deployInfo=जानकारी
#XMSG
deployCheckFailException=परिनियोजन के दौरान अपवाद उपस्थित हुआ
#XMSG
deployGBQFFDisabled=Google BigQuery के लिए लक्ष्य कनेक्शन के साथ प्रतिकृति प्रवाह को परिनियोजित करना वर्तमान में संभव नहीं है, क्योंकि हम इस फ़ंक्शन पर रखरखाव कर रहे हैं.
#XMSG
deployKAFKAFFDisabled=Apache Kafka के लिए लक्ष्य कनेक्शन के साथ प्रतिकृति प्रवाह को परिनियोजित करना वर्तमान में संभव नहीं है, क्योंकि हम इस फ़ंक्शन पर रखरखाव कर रहे हैं.
#XMSG
deployConfluentDisabled=Confluent Kafka के लिए लक्ष्य कनेक्शन के साथ प्रतिकृति प्रवाह को परिनियोजित करना वर्तमान में संभव नहीं है, क्योंकि हम इस फ़ंक्शन पर रखरखाव कर रहे हैं.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=निम्न लक्ष्य ऑब्जेक्ट के लिए, डेल्टा कैप्चर तालिका नाम पहले से ही संग्राहक में अन्य तालिकाओं द्वारा उपयोग किए जाते हैं: {0} प्रतिकृति प्रवाह को परिनियोजित करने से पहले आपको यह सुनिश्चित करने के लिए इन लक्ष्य ऑब्जेक्ट का नाम बदलना होगा कि संबंधित डेल्टा कैप्चर तालिका नाम अद्वितीय हैं.
#XMSG
deployDWCSourceFFDisabled=उनके स्रोत के रूप में SAP Datasphere वाले प्रतिकृति प्रवाह को परिनियोजित करना वर्तमान में संभव नहीं है क्योंकि हम इस फ़ंक्शन पर रखरखाव कर रहे हैं.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=स्रोत ऑब्जेक्ट के रूप में डेल्टा-सक्षम स्थानीय तालिका वाले प्रतिकृति प्रवाह को परिनियोजित करना वर्तमान में संभव नहीं है क्योंकि हम इस फ़ंक्शन पर रखरखाव कर रहे हैं.
#XMSG
deployHDLFSourceFFDisabled=कनेक्शन प्रकार SAP HANA क्लाउड, डेटा लेक फ़ाइलों के साथ स्रोत कनेक्शन वाले प्रतिकृति प्रवाह को परिनियोजित करना वर्तमान में संभव नहीं है क्योंकि हम रखरखाव कर रहे हैं.
#XMSG
deployObjectStoreAsSourceFFDisabled=प्रतिकृति प्रवाहों को परिनियोजित करना जिनके स्रोत के रूप में क्लाउड संग्रहण प्रदाता हैं, वर्तमान में संभव नहीं है.
#XMSG
deployConfluentSourceFFDisabled=उनके स्रोत के रूप में Confluent Kafka वाले प्रतिकृति प्रवाह को परिनियोजित करना वर्तमान में संभव नहीं है क्योंकि हम इस फ़ंक्शन पर रखरखाव निष्पादित कर रहे हैं.
#XMSG
deployMaxDWCNewTableCrossed=बड़े प्रतिकृति प्रवाहों के लिए, उन्हें एक बार में "सहेजना और परिनियोजित" करना संभव नहीं है. कृपया पहले अपना प्रतिकृति प्रवाह सहेजें और फिर उसे परिनियोजित करें.
#XMSG
deployInProgressInfo=परिनियोजन पहले से ही प्रगति पर है.
#XMSG
deploySourceObjectInUse=स्रोत ऑब्जेक्ट {0} का उपयोग प्रतिकृति प्रवाह {1} में पहले से किया जा रहा है.
#XMSG
deployTargetSourceObjectInUse=स्रोत ऑब्जेक्ट {0} का उपयोग पहले से ही प्रतिकृति प्रवाह {1} में किया जा रहा है. लक्ष्य ऑब्जेक्ट {2} का उपयोग पहले से ही प्रतिकृति प्रवाह {3} में किया जा रहा है.
#XMSG
deployReplicationFlowCheckError=प्रतिकृति प्रवाह सत्यापित करते समय त्रुटि: {0}
#XMSG
preDeployTargetObjectInUse=लक्ष्य ऑब्जेक्ट{0} पहले से ही प्रतिकृति प्रवाह{1} में उपयोग किए जा रहे हैं, और आप दो अलग-अलग प्रतिकृति प्रवाह में एक ही लक्ष्य ऑब्जेक्ट नहीं रख सकते. कोई अन्य लक्ष्य ऑब्जेक्ट चुनें और फिर से प्रयास करें.
#XMSG
runInProgressInfo=प्रतिकृति प्रवाह पहले से ही रनिंग है.
#XMSG
deploySignavioTargetFFDisabled=SAP Signavio को लक्ष्य के रूप में रखने वाले प्रतिकृति प्रवाहों को परिनियोजित करना वर्तमान में संभव नहीं है, क्योंकि हम इस फ़ंक्शन पर रखरखाव कर रहे हैं.
#XMSG
deployHanaViewAsSourceFFDisabled=चयनित स्रोत कनेक्शन के लिए स्रोत ऑब्जेक्ट के रूप में दृश्यों वाले प्रतिकृति प्रवाह को परिनियोजित करना वर्तमान में संभव नहीं है. बाद में पुन: प्रयास करें.
#XMSG
deployMsOneLakeTargetFFDisabled=MS OneLake को लक्ष्य मानकर प्रतिकृति प्रवाहों को तैनात करना वर्तमान में संभव नहीं है, क्योंकि हम इस फ़ंक्शन पर रखरखाव कर रहे हैं.
#XMSG
deploySFTPTargetFFDisabled=SFTP को लक्ष्य बनाकर प्रतिकृति प्रवाहों को परिनियोजित करना वर्तमान में संभव नहीं है, क्योंकि हम इस फ़ंक्शन पर रखरखाव कर रहे हैं.
#XMSG
deploySFTPSourceFFDisabled=SFTP को स्रोत बनाकर प्रतिकृति प्रवाहों को परिनियोजित करना वर्तमान में संभव नहीं है, क्योंकि हम इस फ़ंक्शन पर रखरखाव कर रहे हैं.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=तकनीकी नाम
#XFLD
businessNameInRenameTarget=बिज़नेस का नाम
#XTOL
renametargetDialogTitle=लक्ष्य ऑब्जेक्ट का नाम बदलें
#XBUT
targetRenameButton=नाम बदलें
#XBUT
targetRenameCancel=रद्द करें
#XMSG
mandatoryTargetName=आपको नाम डालना होगा.
#XMSG
dwcSpecialChar=_(अंडरस्कोर) केवल विशेष वर्ण अनुमत किया गया है.
#XMSG
dwcWithDot=लक्ष्य तालिका नाम में लैटिन अक्षर, संख्याएं, अंडरस्कोर (_) और विराम चिह्न (.) शामिल हो सकते हैं. पहला वर्ण अक्षर, संख्या या अंडरस्कोर होना चाहिए (विराम चिह्न नहीं).
#XMSG
nonDwcSpecialChar=अनुमत विशेष वर्ण हैं _(अंडरस्कोर) -(हाइफ़न) .(डॉट)
#XMSG
firstUnderscorePattern=नाम _(अंडरस्कोर) से आरंभ होना चाहिए

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: तालिका विवरण बनाने के SQL दृश्य
#XMSG
sqlDialogMaxPKWarning=Google BigQuery में, अधिकतम 16 प्राथमिक कुंजी समर्थित हैं, और स्रोत ऑब्जेक्ट की संख्या बड़ी है. इसलिए, इस विवरण में कोई प्राथमिक कुंजी परिभाषित नहीं की गई है-
#XMSG
sqlDialogIncomptiblePKTypeWarning=एक या अधिक स्रोत स्तंभ में डेटा प्रकार होते हैं जिन्हें Google BigQuery में प्राथमिक कुंजी के रूप में परिभाषित नहीं किया जा सकता है. इसलिए, इस मामले में कोई प्राथमिक कुंजी परिभाषित नहीं की गई है Google BigQuery में, केवल निम्न डेटा प्रकारों में प्राथमिक कुंजी हो सकती है: BIGNUMERIC, बूलियन, दिनांक, DATETIME, INT64, अंकीय, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=कॉपी करें और बंद करें
#XBUT
closeDDL=बंद करें
#XMSG
copiedToClipboard=क्लिपबोर्ड पर प्रति बनाएं


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=ऑब्जेक्ट ''{0}'' कार्य श्रृंखला का हिस्सा नहीं हो सकता क्योंकि इसका कोई अंत नहीं है (क्योंकि इसमें लोड प्रकार प्रारंभिक और डेल्टा/केवल डेल्टा वाले ऑब्जेक्ट शामिल हैं).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=ऑब्जेक्ट ''{0}'' कार्य श्रृंखला का हिस्सा नहीं हो सकता क्योंकि इसका कोई अंत नहीं है (क्योंकि इसमें लोड प्रकार प्रारंभिक और डेल्टा वाले ऑब्जेक्ट शामिल हैं).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=ऑब्जेक्ट ''{0}'' को कार्य श्रृंखला में जोड़ा नहीं जा सकता.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=यहां न सहेजे गए लक्ष्य ऑब्जेक्ट मौजूद हैं. कृपया फिर से सहेजें.{0}{0} इस सुविधा का व्यवहार परिवर्तित हो गया है: अतीत में, लक्ष्य ऑब्जेक्ट केवल लक्ष्य परिवेश में बनाए जाते थे, जब प्रतिकृति प्रवाह लागू परिनियोजित किया जाता था.{0} प्रतिकृति प्रवाह सहेजे जाने पर, अब ऑब्जेक्ट पहले से ही बनाए जाते हैं. आपका प्रतिकृति प्रवाह इस परिवर्तन से पहले बनाया गया था और इसमें नए ऑब्जेक्ट शामिल हैं.{0} आपको परिनियोजित करने से पहले, प्रतिकृति प्रवाह को एक बार फिर से सहेजने की आवश्यकता है, ताकि नई ऑब्जेक्ट को सही ढंग से शामिल किया जा सके.
#XMSG
confirmChangeContentTypeMessage=आप सामग्री प्रकार बदलने वाले हैं. यदि आप ऐसा करते हैं, तो सभी मौजूदा प्रक्षेपण हटा दिए जाएँगे.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=विषय का नाम
#XFLD
schemaDialogVersionName=स्कीमा संस्करण
#XFLD
includeTechKey=तकनीकी कुंजी शामिल करें
#XFLD
segementButtonFlat=फ़्लैट
#XFLD
segementButtonNested=नेस्टेड
#XMSG
subjectNamePlaceholder=विषय का नाम खोजें

#XMSG
@EmailNotificationSuccess=रनटाइम ईमेल सूचनाओं का कॉन्फ़िगरेशन सहेजा जाता है।

#XFLD
@RuntimeEmailNotification=रनटाइम ईमेल अधिसूचना

#XBTN
@TXT_SAVE=सहेजें


