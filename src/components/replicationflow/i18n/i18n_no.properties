#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Replikeringsflyt

#XFLD: Edit Schema button text
editSchema=Rediger skjema

#XTIT : Properties heading
configSchema=Konfigurer skjema

#XFLD: save changed button text
applyChanges=Bruk endringer


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Velg kildeforbindelse
#XFLD
sourceContainernEmptyText=Velg container
#XFLD
targetConnectionEmptyText=Velg målforbindelse
#XFLD
targetContainernEmptyText=Velg container
#XFLD
sourceSelectObjectText=Velg kildeobjekt
#XFLD
sourceObjectCount=Kildeobjekter ({0})
#XFLD
targetObjectText=Målobjekter
#XFLD
confluentBrowseContext=Velg kontekst
#XBUT
@retry=Prøv på nytt
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Tenantoppgradering pågår.

#XTOL
browseSourceConnection=Bla gjennom kildeforbindelse
#XTOL
browseTargetConnection=Bla gjennom målforbindelse
#XTOL
browseSourceContainer=Bla gjennom kildecontainer
#XTOL
browseAndAddSourceDataset=Legg til kildeobjekter
#XTOL
browseTargetContainer=Bla gjennom målcontainer
#XTOL
browseTargetSetting=Bla gjennom målinnstillinger
#XTOL
browseSourceSetting=Bla gjennom kildeinnstillinger
#XTOL
sourceDatasetInfo=Info
#XTOL
sourceDatasetRemove=Fjern
#XTOL
mappingCount=Dette representerer det totale antallet ikke-navnebaserte koblinger/uttrykk.
#XTOL
filterCount=Dette representerer det totale antallet filterbetingelser.
#XTOL
loading=Laster ...
#XCOL
deltaCapture=Deltaregistrering
#XCOL
deltaCaptureTableName=Dataregistreingstabell
#XCOL
loadType=Lastetype
#XCOL
deleteAllBeforeLoading=Slett alle før lasting
#XCOL
transformationsTab=Projeksjoner
#XCOL
settingsTab=Innstillinger

#XBUT
renameTargetObjectBtn=Endre navn på målobjekt
#XBUT
mapToExistingTargetObjectBtn=Koble til eksisterende målobjekt
#XBUT
changeContainerPathBtn=Endre containerbane
#XBUT
viewSQLDDLUpdated=Vis SQL-setning for oppretting av tabell
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Kildeobjektet støtter ikke deltaregistrering, men det valgte målobjektet har deltaregistreringsalternativet aktivert.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Målobjektet kan ikke brukes fordi deltaregistrering er aktivert,{0} mens kildeobjektet ikke støtter deltaregistrering.{1}Du kan velge et annet målobjekt som ikke støtter deltaregistrering.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Det finnes allerede et målobjekt med dette navnet. Det kan imidlertid ikke brukes{0}fordi deltaregistrering er aktivert, mens kildeobjektet ikke{0}støtter deltaregistrering{1}Du kan oppgi navnet på et eksisterende målobjekt som ikke{0}støtter deltaregistrering, eller oppgi et navn som ikke finnes ennå.
#XBUT
copySQLDDLUpdated=Kopier SQL-setning for oppretting av tabell
#XMSG
targetObjExistingNoCDCColumnUpdated=Eksisterende tabeller i Google BigQuery må inneholde følgende kolonner for endringsdataregistrering (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Følgende kildeobjekter støttes ikke fordi de ikke har en primærnøkkel, eller de bruker en forbindelse som ikke oppfyller betingelsene for henting av primærnøkkelen:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Se SAP KBA 3531135 for å finne en mulig løsning.
#XLST: load type list values
initial=Bare initial
@emailUpdateError=Feil ved oppdatering av e-postvarselliste

#XLST
initialDelta=Initial og delta

#XLST
deltaOnly=Bare delta
#XMSG
confluentDeltaLoadTypeInfo=For Confluent Kafka-kilde støttes bare lastetypen Initial og delta.
#XMSG
confirmRemoveReplicationObject=Bekrefter du at du vil slette replikeringen?
#XMSG
confirmRemoveReplicationTaskPrompt=Denne handlingen sletter alle eksisterende replikeringer. Vil du fortsette?
#XMSG
confirmTargetConnectionChangePrompt=Denne handlingen tilbakestiller målforbindelsen, målcontaineren og sletter alle målobjekter. Vil du fortsette?
#XMSG
confirmTargetContainerChangePrompt=Denne handlingen tilbakestiller målcontaineren og sletter alle eksisterende objekter. Vil du fortsette?
#XMSG
confirmRemoveTransformObject=Bekrefter du at du vil slette projeksjon {0}?
#XMSG
ErrorMsgContainerChange=Det oppstod en feil under endring av containerbanen.
#XMSG
infoForUnsupportedDatasetNoKeys=Følgende kildeobjekter støttes ikke fordi de mangler en primærnøkkel:
#XMSG
infoForUnsupportedDatasetView=Følgende kildeobjekter av typen Visninger støttes ikke:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Følgende kildeobjekt støttes ikke fordi det er en SQL-visning som inneholder inndataparameterne:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Følgende kildeobjekter støttes ikke fordi ekstraksjon er deaktivert for dem:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=De eneste serialiseringsformatene som tillates for Confluent-forbindelser, er AVRO og JSON. Det er ikke støtte for de følgende objektene fordi de bruker et annet serialiseringsformat.
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Kan ikke hente skjemaet for de følgende objektene. Velg den riktige konteksten eller verifiser skjemaregisterkonfigurasjonen
#XTOL: warning dialog header on deleting replication task
deleteHeader=Slett
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Innstillingen Slett alle før lasting støttes ikke for Google BigQuery.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Innstillingen Slett alle før sletter og oppretter objektet (emnet) på nytt før hver replikering. Dette sletter også alle tilordnede meldinger.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Innstillingen Slett alle før støttes ikke for denne måltypen.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Teknisk navn
#XCOL
connBusinessName=Forretningsnavn
#XCOL
connDescriptionName=Beskrivelse
#XCOL
connType=Type
#XMSG
connTblNoDataFoundtxt=Finner ingen forbindelser
#XMSG
connectionError=Det oppstod en feil under henting av forbindelser.
#XMSG
connectionCombinationUnsupportedErrorTitle=Forbindelseskombinasjon støttes ikke
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Replikering fra {0} til {1} støttes ikke for øyeblikket.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Forbindelsestypekombinasjon støttes ikke.
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Replikering fra en forbindelse med forbindelsestypen SAP HANA Cloud, datasjøfiler til {0} støttes ikke. Du kan bare replikere til SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Velg
#XBUT
containerCancelBtn=Avbryt
#XTOL
containerSelectTooltip=Velg
#XTOL
containerCancelTooltip=Avbryt
#XMSG
containerContainerPathPlcHold=Containerbane
#XFLD
containerContainertxt=Container
#XFLD
confluentContainerContainertxt=Kontekst
#XMSG
infoMessageForSLTSelection=Bare /SLT/masseoverførings-ID er tillatt som container. Velg en masseoverførings-ID under SLT (hvis tilgjengelig), og klikk på Send.
#XMSG
msgFetchContainerFail=Det oppstod en feil ved henting av containerdata.
#XMSG
infoMessageForSLTHidden=Denne forbindelsen støtter ikke SLT-mapper, så de vises ikke i listen nedenfor.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Velg en container som inneholder undermapper.
#XMSG
sftpIncludeSubFolderText=Usann
#XMSG
sftpIncludeSubFolderTextNew=Nei

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Ingen filterkobling ennå)
#XMSG
failToFetchRemoteMetadata=Det oppstod en feil ved henting av metadata.
#XMSG
failToFetchData=Det oppstod en feil ved henting av eksisterende mål.
#XCOL
@loadType=Lastetype
#XCOL
@deleteAllBeforeLoading=Slett alle før lasting

#XMSG
@loading=Laster ...
#XFLD
@selectSourceObjects=Velg kildeobjekter
#XMSG
@exceedLimit=Du kan ikke importere mer enn {0} objekter om gangen. Fjern merking av minst {1} objekter.
#XFLD
@objects=Objekter
#XBUT
@ok=OK
#XBUT
@cancel=Avbryt
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Neste
#XBUT
btnAddSelection=Legg til utvalg
#XTOL
@remoteFromSelection=Fjern fra utvalg
#XMSG
@searchInForSearchField=Søk i {0}

#XCOL
@name=Teknisk navn
#XCOL
@type=Type
#XCOL
@location=Plassering
#XCOL
@label=Forretningsnavn
#XCOL
@status=Status

#XFLD
@searchIn=Søk i:
#XBUT
@available=Tilgjengelig
#XBUT
@selection=Utvalg

#XFLD
@noSourceSubFolder=Tabeller og visninger
#XMSG
@alreadyAdded=Finnes allerede i diagrammet
#XMSG
@askForFilter=Det er flere enn {0} elementer. Oppgi en filterstreng for å begrense antallet elementer.
#XFLD: success label
lblSuccess=Vellykket
#XFLD: ready label
lblReady=Klar
#XFLD: failure label
lblFailed=Mislykket
#XFLD: fetching status label
lblFetchingDetail=Henter detaljer

#XMSG Place holder text for tree filter control
filterPlaceHolder=Skriv inn tekst for å filtrere objekter på øverste nivå
#XMSG Place holder text for server search control
serverSearchPlaceholder=Skriv og trykk Enter for å søke
#XMSG
@deployObjects=Importerer {0} objekter ...
#XMSG
@deployObjectsStatus=Antall objekter som er importert: {0}. Antall objekter som ikke kan importeres: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Kan ikke åpne lokal repositorynavigator.
#XMSG
@openRemoteSourceBrowserError=Kan ikke åpne kildeobjekter.
#XMSG
@openRemoteTargetBrowserError=Kan ikke åpne målobjekter.
#XMSG
@validatingTargetsError=Det oppstod en feil under validering av mål.
#XMSG
@waitingToImport=Klar for import

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Du har overskredet det maksimale antallet objekter. Velg maksimalt 500 objekter for én replikeringsflyt.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Teknisk navn
#XFLD
sourceObjectBusinessName=Forretningsnavn
#XFLD
sourceNoColumns=Antall kolonner
#XFLD
containerLbl=Container

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Du må velge en kildeforbindelse for replikeringsflyten.
#XMSG
validationSourceContainerNonExist=Du må velge en container for kildeforbindelsen.
#XMSG
validationTargetNonExist=Du må velge en målforbindelse for replikeringsflyten.
#XMSG
validationTargetContainerNonExist=Du må velge en container for målforbindelsen.
#XMSG
validationTruncateDisabledForObjectTitle=Replikering til objektlagringsplasser.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Replikering til en nettskylagringsplass er bare mulig hvis enten alternativet Slett alle før lasting er satt, eller hvis målobjektet ikke finnes i målet. {0}{0} Hvis replikering likevel skal aktiveres for objekter som alternativet Slett alle før lasting ikke er satt for, må du forsikre deg om at målobjektet ikke finnes i systemet før du kjører replikeringsflyten.
#XMSG
validationTaskNonExist=Du må ha minst én replikering i replikeringsflyten.
#XMSG
validationTaskTargetMissing=Du må ha et mål for replikering med kilden: {0}
#XMSG
validationTaskTargetIsSAC=Valgt mål er en SAC-artefakt: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Det valgte målet er en lokal tabell som ikke støttes: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Et objekt med dette navnet finnes allerede i målet. Dette objektet kan imidlertid ikke brukes som målobjekt for en replikeringsflyt til det lokale repositoryet, ettersom det ikke er en lokal tabell.
#XMSG
validateSourceTargetSystemDifference=Du må velg en annen kilde- og målforbindelse og containerkombinasjoner for replikeringsflyten.
#XMSG
validateDuplicateSources=en eller flere replikeringer har dupliserte kildeobjektnavn: {0}.
#XMSG
validateDuplicateTargets=en eller flere replikeringer har dupliserte målobjektnavn: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=Kildeobjektet {0} støtter ikke deltaregistrering, men målobjektet {1} gjør det. Du må fjerne replikeringen.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Du må velge lastetypen "Initial og delta" for replikeringen med målobjektnavnet {0}.
#XMSG
validationAutoRenameTarget=Målkolonner har fått nye navn.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Det er lagt til en auto-projeksjon, og følgende målkolonner har fått nye navn for å tillate replikering til målet:{1}{1} {0} {1}{1}Dette skyldes en av følgende årsaker:{1}{1}{2} Tegn som ikke støttes{1}{2} Reservert prefiks
#XMSG
validationAutoRenameTargetDescriptionUpdated=Det er lagt til en auto-projeksjon, og følgende målkolonner har fått nye navn for å kunne replikere til Google BigQuery:{1}{1} {0} {1}{1}Dette skyldes en av følgende årsaker:{1}{1}{2} Reservert kolonnenavn{1}{2} Tegn som ikke støttes{1}{2} Reservert prefiks
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Det er lagt til en auto-projeksjon, og følgende målkolonner har fått nye navn for å tillate replikeringer til Confluent:{1}{1} {0} {1}{1}Dette skyldes en av følgende årsaker:{1}{1}{2} Reservert kolonnenavn{1}{2} Tegn som ikke støttes{1}{2} Reservert prefiks
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Det er lagt til en auto-projeksjon, og følgende målkolonner har fått nye navn for å tillate replikeringer til målet:{1}{1} {0} {1}{1}Dette skyldes en av følgende årsaker:{1}{1}{2} Reservert kolonnenavn{1}{2} Tegn som ikke støttes{1}{2} Reservert prefiks
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Målobjektet har fått nytt navn.
#XMSG
autoRenameInfoDesc=Målobjektet har fått nytt navn fordi den inneholder tegn som ikke støttes. Bare de følgende tegnene støttes:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(punktum){0}{1}_(understrek){0}{1}-(tankestrek)
#XMSG
validationAutoTargetTypeConversion=Måldatatyper er endret.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=For følgende målkolonner er måldatatype endret, fordi kildedatatypene ikke støttes i Google BigQuery:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=For følgende målkolonner er måldatatypen endret, fordi kildedatatypene ikke støttes i målforbindelsen:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Forkorte målkolonnenavn.
#XMSG
validationMaxCharLengthGBQTargetDescription=I Google BigQuery kan kolonnenavn ha inntil 300 tegn. Bruk en projeksjon til å forkorte følgende målkolonnenavn:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Kan ikke opprette primærnøkler.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=Maksimalt 16 primærnøkler støttes i Google BigQuery, men kildeobjektet har et høyere antall primærnøkler. Ingen av primærnøklene opprettes i målobjektet.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=En eller flere kildekolonner har datatyper som ikke kan kan defineres som primærnøkler i Google BigQuery. Ingen av primærnøklene vil bli opprettet i målobjektet.{0}{0}Følgende måldatatyper er kompatible med Google BigQuery-datatyper som det kan defineres en primærnøkkel for: {0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Definer én eller flere kolonner som en primærnøkkel.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Du må definere én eller flere kolonner som primærnøkkel. Bruk kildeskjemadialogen til å gjøre dette
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Definer én eller flere kolonner som en primærnøkkel.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Du må definere én eller flere kolonner som primærnøkkel som samsvarer med begrensningene for primærnøkkelen for kildeobjektet. Gå til "Konfigurer skjema" i egenskapene for kildeobjektet for å gjøre dette.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Oppgi en gyldig maks. partisjonsverdi.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Maks. partisjonsverdi må være ≥ 1 og ≤ **********
#XMSG
validateHDLFNoPKDatasetError=Definer én eller flere kolonner som en primærnøkkel.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=For å replikere et objekt, må du definere én eller flere målkolonner som en primærnøkkel. Gjør dette ved hjelp av projeksjon.
#XMSG
validateHDLFNoPKExistingDatasetError=Definer én eller flere kolonner som en primærnøkkel.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=For å replikere data til et eksisterende målobjekt, må det ha én eller flere kolonner definert som primærnøkkelen. {0} Du har følgende alternativer for å definere én eller flere kolonner som primærnøkkelen: {0}{1} Bruk det lokale tabellredigeringsprogrammet til å endre det eksisterende målobjektet. Last deretter replikeringsflyten på nytt.{0}{1}Endre navn på målobjektet i replikeringsflyten. Dette vil opprette et nytt objekt så snart kjøringen har startet. Når du har endret navnet, kan du definere én eller flere kolonner som primærnøkkelen i en projeksjon.{0}{1}Koble objektet til et annet eksisterende målobjekt hvor én eller flere kolonner allerede er definert som primærnøkkelen.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Det valgte målet finnes allerede i repositoryet: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Navnene på deltaregistreringstabellene brukes allerede av andre tabeller i repositoryet for følgende målobjekter: {0}. Du må endre navnene på disse målobjektene for å sikre at de tilknyttede navnene på deltaregistreringstabellene er unike før du kan distribuere replikeringsflyten.
#XMSG
validateConfluentEmptySchema=Definer skjema
#XMSG
validateConfluentEmptySchemaDescUpdated=Kildetabellen har ikke et skjema. Velg "Konfigurer skjema" for å definere et skjema.
#XMSG
validationCSVEncoding=Ugyldig CSV-koding
#XMSG
validationCSVEncodingDescription=CSV-kodingen for oppgaven er ikke gyldig.
#XMSG
validateConfluentEmptySchema=Velg en kompatibel måldatatype
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Velg en kompatibel måldatatype
#XMSG
globalValidateTargetDataTypeDesc=Det oppstod en feil med kolonnekoblingene. Gå til "Projeksjon" for å sikre alle kildekolonner er koblet med en unik kolonne, med en kolonne som har en kompatibel datatype, og at alle definerte uttrykk er gyldige,
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Duplikate kolonnenavn.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Duplikate kolonnenavn støttes ikke. Bruk projeksjonsdialogen til å fikse dem. De følgende målobjektene har duplikate kolonnenavn: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Duplikate kolonnenavn.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Duplikate kolonnenavn støttes ikke. De følgende målobjektene har duplikate kolonnenavn: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Det kan finnes inkonsistenser i dataene.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Lastetypen Bare delta tar ikke hensyn til endringene som er utført i kilden mellom siste lagring og neste kjøring.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Endre lastetype til "Initial".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Replikering av ABAP-baserte objekter som ikke har en primærnøkkel er bare mulig for lastetype "Bare initial".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Deaktiver deltaregistrering.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=For å replikere et objekt som ikke har en primærnøkkel som bruker kildeforbindelsestype ABAP, må du først deaktivere deltaregistrering for denne tabellen.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Endre målobjekt.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Målobjektet kan ikke brukes fordi deltaregistrering er aktivert. Du kan enten endre navn på målobjektet og deretter slå av dataregistrering for det nye objektet (med det nye navnet) eller koble kildeobjektet til et målobjekt hvor deltaregistrering er deaktivert.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Endre målobjekt.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Målobjektet kan ikke brukes fordi det ikke har den obligatoriske tekniske kolonnen __load_package_id. Du kan endre navn på målobjektet ved å bruk et navn som ikke finnes ennå. Systemet oppretter deretter et nytt objekt som har den samme definisjonen som kildeobjektet og inneholder den tekniske kolonnen. Alternativt kan du koble målobjektet til et eksisterende objekt som har den obligatoriske tekniske kolonnen(__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Endre målobjekt.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Målobjektet kan ikke brukes fordi det ikke har den obligatoriske tekniske kolonnen __load_record_id. Du kan endre navn på målobjektet ved å bruk et navn som ikke finnes ennå. Systemet oppretter deretter et nytt objekt som har den samme definisjonen som kildeobjektet og inneholder den tekniske kolonnen. Alternativt kan du koble målobjektet til et eksisterende objekt som har den obligatoriske tekniske kolonnen(__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Endre målobjekt.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Målobjektet kan ikke brukes fordi datatypen for dens tekniske kolonne __load_record_id ikke er "string(44)". Du kan endre navn på målobjektet ved å bruk et navn som ikke finnes ennå. Systemet oppretter deretter et nytt objekt som har den samme definisjonen som kildeobjektet og derfor den korrekte datatypen. Alternativt kan du koble målobjektet til et eksisterende objekt som har den obligatoriske tekniske kolonnen(__load_record_id) med den korrekte datatypen.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Endre målobjekt.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Målobjektet kan ikke brukes fordi det har en primærnøkkel mens kildeobjektet har ingen. Du kan endre navn på målobjektet ved å bruk et navn som ikke finnes ennå. Systemet oppretter deretter et nytt objekt som har den samme definisjonen som kildeobjektet og derfor ingen primærnøkkel. Alternativt kan du koble målobjektet til et eksisterende objekt som har den obligatoriske tekniske kolonnen(__load_package_id) og ikke har en primærnøkkel.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Endre målobjekt.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Målobjektet kan ikke brukes fordi det har en primærnøkkel mens kildeobjektet har ingen. Du kan endre navn på målobjektet ved å bruk et navn som ikke finnes ennå. Systemet oppretter deretter et nytt objekt som har den samme definisjonen som kildeobjektet og derfor ingen primærnøkkel. Alternativt kan du koble målobjektet til et eksisterende objekt som har den obligatoriske tekniske kolonnen (__load_record_id) og ikke har en primærnøkkel.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Endre målobjekt.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Målobjektet kan ikke brukes fordi datatypen for dens tekniske kolonne  __load_package_id er ikke "binary(>=256)". Du kan endre navn på målobjektet ved å bruk et navn som ikke finnes ennå. Systemet oppretter deretter et nytt objekt som har den samme definisjonen som kildeobjektet og derfor den korrekte datatypen. Alternativt kan du koble målobjektet til et eksisterende objekt som har den obligatoriske tekniske kolonnen(__load_package_id) med den korrekte datatypen.
#XMSG
validationAutoRenameTargetDPID=Målkolonner har fått nye navn.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Fjern kildeobjekt.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Kildeobjektet har ikke en nøkkelkolonne, noe som ikke støttes i denne konteksten.
#XMSG
validationAutoRenameTargetDPIDDescription=Det er lagt til en auto-projeksjon, og følgende målkolonner har fått nye navn for å kunne replikere fra ABAP-kilde uten nøkler:{1}{1} {0} {1}{1}Dette skyldes en av følgende årsaker:{1}{1}{2} Reservert kolonnenavn{1}{2} Tegn som ikke støttes{1}{2} Reservert prefiks
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Replikering til {0}
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Lagring og implementering av replikeringsflyter som har {0} som sitt mål er ikke mulig for øyeblikket, fordi vi utfører vedlikehold på denne funksjonen.
#XMSG
TargetColumnSkippedLTF=Målkolonne er hoppet over
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Målkolonne er hoppet over på grunn av datatype som ikke støttes. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Tidskolonne som primærnøkkel.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Kildeobjektet har en tidskolonne som primærnøkkel som ikke støttes i denne konteksten.
#XMSG
validateNoPKInLTFTarget=Primærnøkkel mangler.
#XMSG
validateNoPKInLTFTargetDescription=Primærnøkkelen er ikke definert i målet, dette støttes ikke i denne konteksten.
#XMSG
validateABAPClusterTableLTF=ABAP-klyngetabell.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Kildeobjektet er en ABAP-klyngetabell, noe som ikke støttes i denne konteksten.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Det ser ut til at du ikke har lagt til noen data ennå.
#YINS
welcomeText2=Du starter replikeringsflyten ved å velge en forbindelse og et kildeobjekt på venstre side.

#XBUT
wizStep1=Velg kildeforbindelse
#XBUT
wizStep2=Velg kildecontainer
#XBUT
wizStep3=Legg til kildeobjekter

#XMSG
limitDataset=Du har nådd det maksimale antallet objekter. Fjern eksisterende objekter for å legge til nye, eller opprett en ny replikeringsflyt.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Replikeringsflyten til denne ikke-SAP-målforbindelsen kan ikke startes fordi det ikke finnes utgående volum for denne måneden.
#XMSG
premiumOutBoundRFAdminWarningMsg=En administrator kan øke premium utgående blokker for denne tenanten slik at utgående volum blir tilgjengelig for denne måneden.
#XMSG
messageForToastForDPIDColumn2=Ny kolonne er lagt til mål for {0} objekter - nødvendig for å håndtere duplikate poster i forbindelse med ABAP-baserte kildeobjekter som ikke har en primærnøkkel.
#XMSG
PremiumInboundWarningMessage=Avhengig av antall replikeringsflyter og datavolumet som skal replikeres, kan SAP HANA-ressursene {0} som kreves for replikering av data via  {1} overskride den tilgjengelige kapasiteten for tenanten.
#XMSG
PremiumInboundWarningMsg=Avhengig av antall replikeringsflyter og datavolumet som skal replikeres, kan SAP HANA-ressursene {0} som kreves for replikering av data via  "{1}" overskride den tilgjengelige kapasiteten for tenanten.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Oppgi et projeksjonsnavn.
#XMSG
emptyTargetColumn=Oppgi et målkolonnenavn.
#XMSG
emptyTargetColumnBusinessName=Oppgi et målkolonne Forretningsnavn.
#XMSG
invalidTransformName=Oppgi et projeksjonsnavn.
#XMSG
uniqueColumnName=Endre navn på målkolonnen.
#XMSG
copySourceColumnLbl=Kopier kolonner fra kildeobjekt
#XMSG
renameWarning=Forsikre deg om at du velger et unikt navn når måltabellen skal få nytt navn. Hvis tabellen med det nye navnet allerede finnes i rommet, brukes definisjonen for denne tabellen.

#XMSG
uniqueColumnBusinessName=Endre navn på målkolonne for forretningsnavn.
#XMSG
uniqueSourceMapping=Velg en annen kildekolonne.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=Kildekolonne {0} brukes allerede av følgende målkolonner:{1}{1}{2}{1}{1} Velg en kildekolonne som ikke allerede er i bruk for denne målkolonnen eller andre målkolonner for å lagre projeksjonen.
#XMSG
uniqueColumnNameDescription=Navnet på målkolonnen du har oppgitt, finnes allerede. Du må oppgi et unikt kolonnenavn her før du kan lagre projeksjonen.
#XMSG
uniqueColumnBusinessNameDesc=Forretningsnavnet på målkolonnen som du har oppgitt, finnes allerede. For å lagre projeksjonen, må du oppgi et unikt forretningskolonnenavn.
#XMSG
emptySource=Velg en kildekolonne eller oppgi en konstant.
#XMSG
emptySourceDescription=Du må velge en kildekolonne eller oppgi en konstantverdi for å opprette en gyldig koblingsoppføring.
#XMSG
emptyExpression=Definer kobling.
#XMSG
emptyExpressionDescription1=Velg enten kildekolonnen som du vil koble målkolonnen til, eller merk av i boksen i kolonnen {0} Funksjoner/Konstanter  {1}. {2} {2}. Funksjoner settes inn automatisk i henhold til måldatatypen. Konstantverdier kan oppgis manuelt.
#XMSG
numberExpressionErr=Oppgi et tall.
#XMSG
numberExpressionErrDescription=Du har valgt en numerisk datatype. Det betyr at du bare kan oppgi tall, pluss desimaltegnet der det er relevant. Ikke bruk enkle anførselstegn.
#XMSG
invalidLength=Oppgi en gyldig lengdeverdi.
#XMSG
invalidLengthDescription=Lengden på datatypen må være lik eller større enn lengden på kildekolonnen, og kan være mellom 1 og 5000.
#XMSG
invalidMappedLength=Oppgi en gyldig lengdeverdi.
#XMSG
invalidMappedLengthDescription=Lengden på datatypen må være lik eller større enn lengden på kildekolonnen {0}, og kan være mellom 1 og 5000.
#XMSG
invalidPrecision=Oppgi en gyldig presisjonsverdi.
#XMSG
invalidPrecisionDescription=Presisjon definerer totalt antall sifre. Skala definerer antallet sifre etter desimaltegnet og kan være mellom 0 og presisjon.{0}{0} Eksempler: {0}{1} Presisjon 6, skala 2 tilsvarer tall som 1234,56.{0}{1} Presisjon 6, skala 6 tilsvarer tall som 0,123546.{0} {0} Presisjon og skala for målet må være kompatibelt med presisjon og skala for kilden slik at alle sifre fra kilden passer i målfeltet. Hvis du for eksempel har presisjon 6 og skala 2 i kilden (og derfor andre sifre enn 0 foran desimaltegnet), kan du ikke ha presisjon 6 og skala 6 i målet.
#XMSG
invalidPrimaryKey=Oppgi minst én primærnøkkel.
#XMSG
invalidPrimaryKeyDescription=Primærnøkkel er ikke definert for dette skjemaet.
#XMSG
invalidMappedPrecision=Oppgi en gyldig presisjonsverdi.
#XMSG
invalidMappedPrecisionDescription1=Presisjon definerer totalt antall sifre. Skala definerer antallet sifre etter desimaltegnet og kan være mellom 0 og presisjon.{0}{0} Eksempler:{0}{1} Presisjon 6, skala 2 tilsvarer tall som 1234,56.{0}{1} Presisjon 6, skala 6 tilsvarer tall som 0,123546.{0}{0}Presisjonen til datatypen må være lik eller større enn presisjonen til kilden ({2}).
#XMSG
invalidScale=Oppgi en gyldig skalaverdi.
#XMSG
invalidScaleDescription=Presisjon definerer totalt antall sifre. Skala definerer antallet sifre etter desimaltegnet og kan være mellom 0 og presisjon.{0}{0} Eksempler: {0}{1} Presisjon 6, skala 2 tilsvarer tall som 1234,56.{0}{1} Presisjon 6, skala 6 tilsvarer tall som 0,123546.{0} {0} Presisjon og skala for målet må være kompatibelt med presisjon og skala for kilden slik at alle sifre fra kilden passer i målfeltet. Hvis du for eksempel har presisjon 6 og skala 2 i kilden (og derfor andre sifre enn 0 foran desimaltegnet), kan du ikke ha presisjon 6 og skala 6 i målet.
#XMSG
invalidMappedScale=Oppgi en gyldig skalaverdi.
#XMSG
invalidMappedScaleDescription1=Presisjon definerer totalt antall sifre. Skala definerer antallet sifre etter desimaltegnet og kan være mellom 0 og presisjon.{0}{0} Eksempler:{0}{1} Presisjon 6, skala 2 tilsvarer tall som 1234,56.{0}{1} Presisjon 6, skala 6 tilsvarer tall som 0,123546.{0}{0}Skalaen til datatypen må være lik eller større enn skalaen til kilden ({2}).
#XMSG
nonCompatibleDataType=Velg en kompatibel måldatatype.
#XMSG
nonCompatibleDataTypeDescription1=Datatypen du oppgir her, må være kompatibel med kildedatatypen ({0}). {1}{1} Eksempel: Hvis kildekolonnen har datatypen "STRING" og inneholder bokstaver, kan du ikke bruke datatypen "DECIMAL" for målet.
#XMSG
invalidColumnCount=Velg en kildekolonne.
#XMSG
ObjectStoreInvalidScaleORPrecision=Oppgi en gyldig verdi for presisjon og skala.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=Den første verdien er presisjonen som definerer det totale antallet sifre. Den andre verdien er skalaen som definerer sifrene etter desimaltegnet. Oppgi en målskalaverdi som er større enn kildeskalaverdien og forsikre deg om at forskjellen mellom den oppgitte verdien for målskala og -presisjon er større enn forskjellen mellom verdien for kildeskala og -presisjon.
#XMSG
InvalidPrecisionORScale=Oppgi en gyldig verdi for presisjon og skala.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=Den første verdien er presisjonen, som definerer totalt antall sifre. Den andre verdien er skalaen, som definerer sifrene etter desimaltegnet.{0}{0}Fordi kildedatatypen ikke støttes i Google BigQuery, konverteres den til måldatatypen DECIMAL. I dette tilfellet kan presisjonen bare defineres mellom 38 og 76, og skalaen mellom 9 og 38. Dessuten må resultatet av presisjonen minus skala, som representerer sifrene før desimaltegnet, være mellom 29 og 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=Den første verdien er presisjonen, som definerer totalt antall sifre. Den andre verdien er skalaen, som definerer sifrene etter desimaltegnet..{0}{0}Fordi kildedatatypen ikke støttes i Google BigQuery, konverteres den til måldatatypen DECIMAL. I dette tilfellet må presisjonen defineres som 20 eller større. I tillegg må resultatet av presisjon minus skala, som reflekterer sifrene før desimaltegnet, være 20 eller større.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=Den første verdien er presisjonen, som definerer totalt antall sifre. Den andre verdien er skalaen, som definerer sifrene etter desimaltegnet.{0}{0}Fordi kildedatatypen ikke støttes i målet, konverteres den til måldatatypen DECIMAL. I dette tilfellet må presisjonen defineres som et hvert tall som er større enn eller lik 1 og mindre enn eller lik 38 og skalaen mindre enn eller lik presisjonen.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=Den første verdien er presisjonen, som definerer totalt antall sifre. Den andre verdien er skalaen, som definerer sifrene etter desimaltegnet.{0}{0}Fordi kildedatatypen ikke støttes i målet, konverteres den til måldatatypen DECIMAL. I dette tilfellet må presisjonen defineres som 20 eller større. I tillegg må resultatet av presisjon minus skala, som reflekterer sifrene før desimaltegnet, være 20 eller større.
#XMSG
invalidColumnCountDescription=Du må velge en kildekolonne eller oppgi en konstantverdi for å opprette en gyldig koblingsoppføring.
#XMSG
duplicateColumns=Endre navn på målkolonnen.
#XMSG
duplicateGBQCDCColumnsDesc=Navnet på målkolonnen er reservert i Google BigQuery. Du må endre navnet på målkolonnene før du kan lagre projeksjonen.
#XMSG
duplicateConfluentCDCColumnsDesc=Navnet på målkolonnen er reservert i Confluent. Du må endre navnet på målkolonnene før du kan lagre projeksjonen.
#XMSG
duplicateSignavioCDCColumnsDesc=Navnet på målkolonnen er reservert i SAP Signavio. Du må endre navnet på målkolonnene før du kan lagre projeksjonen.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Navnet på målkolonnen er reservert i MS OneLake. Du må endre navnet på målkolonnene før du kan lagre projeksjonen.
#XMSG
duplicateSFTPCDCColumnsDesc=Navnet på målkolonnen er reservert i SFTP. Du må endre navnet på målkolonnene før du kan lagre projeksjonen.
#XMSG
GBQTargetNameWithPrefixUpdated1=Navnet på målkolonnen inneholder et prefiks som er reservert i MS OneLake. Du må endre kolonnenavnet før du kan lagre projeksjonen. {0}{0}Navnet på målkolonnen kan ikke starte med følgende strenger:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Du må forkorte navnet på målkolonnen.
#XMSG
GBQtargetMaxLengthDesc=Kolonnenavn i Google BigQuery kan ha opptil 300 tegn. Du må forkorte navnet på målkolonnen før du kan lagre projeksjonen.
#XMSG
invalidMappedScalePrecision=Presisjon og skala for målet må være kompatibelt med presisjon og skala for kilden slik at alle sifre fra kilden passer i målfeltet.
#XMSG
invalidMappedScalePrecisionShortText=Oppgi en gyldig presisjon- og skalaverdi.
#XMSG
validationIncompatiblePKTypeDescProjection3=En eller flere kildekolonner har datatyper som ikke kan kan defineres som primærnøkler i Google BigQuery. Ingen av primærnøklene vil bli opprettet i målobjektet.{0}{0}Følgende måldatatyper er kompatible med Google BigQuery-datatyper som det kan defineres en primærnøkkel for: {1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Fjern merking av kolonne __message_id.
#XMSG
uncheckColumnMessageIdDesc=Kolonne: Primærnøkkel
#XMSG
validationOpCodeInsert=Du må oppgi en verdi for Innsetting.
#XMSG
recommendDifferentPrimaryKey=Vi anbefaler at du velger en annen primær nøkkel på posisjonsnivå.
#XMSG
recommendDifferentPrimaryKeyDesc=Når operasjonskoden allerede er definert, anbefales det at du velger en annen primær nøkkel for matriseindeksen slik at du unngår problemer som for eksempel kolonneduplisering.
#XMSG
selectPrimaryKeyItemLevel=Du må velge minst én primær nøkkel for både topp- og posisjonsnivå.
#XMSG
selectPrimaryKeyItemLevelDesc=Når en matrise eller et kart utvides, må du velge to primære nøkler, én på toppnivå og én på posisjonsnivå.
#XMSG
invalidMapKey=Du må velge minst én primær nøkkel på toppnivå.
#XMSG
invalidMapKeyDesc=Når en matrise eller et kart utvides, må du velge én primær nøkkel på toppnivå.
#XFLD
txtSearchFields=Søk etter målkolonner
#XFLD
txtName=Navn
#XMSG
txtSourceColValidation=En eller flere kildekolonner støttes ikke.
#XMSG
txtMappingCount=Koblinger ({0})
#XMSG
schema=Skjema
#XMSG
sourceColumn=Kildekolonner
#XMSG
warningSourceSchema=Alle endringer i skjemaet vil påvirke tilordningene i projeksjonsdialogen.
#XCOL
txtTargetColName=Målkolonne (teknisk navn)
#XCOL
txtDataType=Måldatatype
#XCOL
txtSourceDataType=Kildedatatype
#XCOL
srcColName=Kildekolonne (teknisk navn)
#XCOL
precision=Presisjon
#XCOL
scale=Skala
#XCOL
functionsOrConstants=Funksjoner/konstanter
#XCOL
txtTargetColBusinessName=Målkolonne (Forretningsnavn)
#XCOL
prKey=Primærnøkkel
#XCOL
txtProperties=Egenskaper
#XBUT
txtOK=Lagre
#XBUT
txtCancel=Avbryt
#XBUT
txtRemove=Fjern
#XFLD
txtDesc=Beskrivelse
#XMSG
rftdMapping=Kobling
#XFLD
@lblColumnDataType=Datatype
#XFLD
@lblColumnTechnicalName=Teknisk navn
#XBUT
txtAutomap=Automatisk kobling
#XBUT
txtUp=Opp
#XBUT
txtDown=Ned

#XTOL
txtTransformationHeader=Projeksjon
#XTOL
editTransformation=Rediger
#XTOL
primaryKeyToolip=Nøkkel


#XMSG
rftdFilter=Filter
#XMSG
rftdFilterColumnCount=Kilde: {0}({1})
#XTOL
rftdFilterColSearch=Søk
#XMSG
rftdFilterColNoData=Ingen kolonner å vise
#XMSG
rftdFilteredColNoExps=Ingen filteruttrykk
#XMSG
rftdFilterSelectedColTxt=Legg til filter for
#XMSG
rftdFilterTxt=Filter tilgjengelig for
#XBUT
rftdFilterSelectedAddColExp=Legg til uttrykk
#YINS
rftdFilterNoSelectedCol=Velg en kolonne for å legge til filter.
#XMSG
rftdFilterExp=Filteruttrykk
#XMSG
rftdFilterNotAllowedColumn=Tilføying av filtre støttes ikke for denne kolonnen.
#XMSG
rftdFilterNotAllowedHead=Kolonne støttes ikke.
#XMSG
rftdFilterNoExp=Ingen filtre er definert
#XTOL
rftdfilteredTt=Filtrert
#XTOL
rftdremoveexpTt=Fjern filteruttrykk
#XTOL
validationMessageTt=Valideringsmeldinger
#XTOL
rftdFilterDateInp=Velg en dato
#XTOL
rftdFilterDateTimeInp=Velg dato/klokkeslett
#XTOL
rftdFilterTimeInp=Velg et klokkeslett
#XTOL
rftdFilterInp=Oppgi en verdi
#XMSG
rftdFilterValidateEmptyMsg={0} filteruttrykk på {1}-kolonne er tomme
#XMSG
rftdFilterValidateInvalidNumericMsg={0} filteruttrykk på {1}-kolonne inneholder ugyldige numeriske verdier
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Filteruttrykket må inneholde gyldige numeriske verdier
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Hvis målobjektskjemaet er endret, bruker du funksjonen “Koble til eksisterende målobjekt” på hovedsiden til å implementere endringene og tilordne målobjektet til kilden på nytt.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Hvis måltabellen finnes allerede og koblingen inkluderer en skjemaendring, må du endre måltabellen tilsvarende før distribusjon av replikeringsflyten.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Hvis koblingen involverer en skjemaendring, må du endre måltabellen tilsvarende før distribusjon av replikeringsflyten.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Følgende kolonner som ikke støttes, ble utelatt fra kildedefinisjonen: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Følgende kolonner som ikke støttes, ble utelatt fra måldefinisjonen: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Følgende objekter støttes ikke fordi de er eksponert for forbruk: {0} {1} {0} {0} For å bruke tabeller i en replikeringsflyt, må den semantiske bruken (i tabellinnstillingene) ikke være satt til {2}Analytisk datasett{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Målobjektet støttes ikke fordi det er eksponert for forbruk: {0} {0} For å bruke tabellen i en replikeringsflyt, må den semantiske bruken (i tabellinnstillingene) ikke være satt til {1}Analytisk datasett{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Det finnes allerede et målobjekt med det samme navnet. Det kan imidlertid ikke brukes fordi det er eksponert for forbruk: {0} {0} For å bruke tabellen i en replikeringsflyt, må den semantiske bruken (i tabellinnstillingene) ikke være satt til {1}Analytisk datasett{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Et objekt med dette navnet finnes allerede i målet. {0}Dette objektet kan imidlertid ikke brukes som målobjekt for en replikeringsflyt til det lokale repositoryet, ettersom det ikke er en lokal tabell.
#XMSG:
targetAutoRenameUpdated=Målkolonnen har fått nytt navn.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Navnet på målkolonnen er endret for å tillate replikeringer i Google BigQuery. Dette skyldes ett av følgende: {0} {1}{2}Reservert kolonnenavn{3}{2}Tegn som ikke støttes{3}{2} Reservert prefiks{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Navnet på målkolonnen er endret for å tillate replikeringer i Confluent. Dette skyldes én av følgende årsaker: {0} {1}{2}Reservert kolonnenavn{3}{2}Tegn som ikke støttes{3}{2} Reservert prefiks{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Navnet på målkolonnen er endret for å tillate replikeringer til målet. Dette skyldes én av følgende årsaker: {0} {1}{2}Tegn som ikke støttes{3}{2}Reservert prefiks{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Navnet på målkolonnen er endret for å tillate replikeringer til målet. Dette skyldes én av følgende årsaker: {0} {1}{2}Reservert kolonnenavn{3}{2}Tegn som ikke støttes{3}{2}Reservert prefiks{3}{4}
#XMSG:
targetAutoDataType=Måldatatype er endret.
#XMSG:
targetAutoDataTypeDesc=Måldatatypen er endret til {0} fordi kildedatatypen ikke støttes i Google BigQuery.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Måldatatypen er endret til {0} fordi kildedatatypen ikke støttes i målforbindelsen.
#XMSG
projectionGBQUnableToCreateKey=Kan ikke opprette primærnøkler.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=Maksimalt 16 primærnøkler støttes i Google BigQuery, men kildeobjektet har et høyere antall primærnøkler. Ingen av primærnøklene opprettes i målobjektet.
#XMSG
HDLFNoKeyError=Definer én eller flere kolonner som en primærnøkkel.
#XMSG
HDLFNoKeyErrorDescription=For å replikere et objekt, må du definere én eller flere kolonner som en primærnøkkel.
#XMSG
HDLFNoKeyErrorExistingTarget=Definer én eller flere kolonner som en primærnøkkel.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=For å replikere data til et eksisterende målobjekt, må det ha én eller flere kolonner definert som primærnøkkelen. {0} {0} Du har følgende alternativer for å definere én eller flere kolonner som primærnøkkelen: {0}{0}{1} Bruk det lokale tabellredigeringsprogrammet til å endre det eksisterende målobjektet. Last deretter replikeringsflyten på nytt.{0}{0}{1} Endre navn på målobjektet i replikeringsflyten. Dette vil opprette et nytt objekt så snart kjøringen har startet. Når du har endret navnet, kan du definere én eller flere kolonner som primærnøkkelen i en projeksjon.{0}{0}{1} Koble objektet til et annet eksisterende målobjekt hvor én eller flere kolonner allerede er definert som primærnøkkelen.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Primærnøkkel er endret.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=Sammenlignet med kildeobjektet har du definert forskjellige kolonner som primærnøkkel for målobjektet. Forsikre deg om at disse kolonnene entydig identifiserer alle rader for å unngå mulig datafeil senere ved replikering av dataene. {0} {0} I kildeobjektet er følgende kolonner definert som primærnøkkelen: {0} {1}
#XMSG
duplicateDPIDColumns=Endre navn på målkolonnen.
#XMSG
duplicateDPIDDColumnsDesc1=Dette målkolonnenavnet er reservert for en teknisk kolonne. Oppgi et annet navn for å lagre projeksjonen.
#XMSG:
targetAutoRenameDPID=Målkolonnen har fått nytt navn.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Navnet på målkolonnen er endret for å tillate replikeringer fra ABAP-kilde uten nøkler. Dette skyldes én av følgende årsaker: {0} {1}{2}Reservert kolonnenavn{3}{2}Tegn som ikke støttes{3}{2} Reservert prefiks{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle={0} målinnstillinger
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle={0} kildeinnstillinger
#XBUT
connectionSettingSave=Lagre
#XBUT
connectionSettingCancel=Avbryt
#XBUT: Button to keep the object level settings
txtKeep=Behold
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Overskriv
#XFLD
targetConnectionThreadlimit=Måltrådgrense for initial dataoverføring (1-100)
#XFLD
connectionThreadLimit=Kildetrådgrense for initial dataoverføring (1-100)
#XFLD
maxConnection=Replikeringstrådgrense (1-100)
#XFLD
kafkaNumberOfPartitions=Antall partisjoner
#XFLD
kafkaReplicationFactor=Replikeringsfaktor
#XFLD
kafkaMessageEncoder=Meldingskoder
#XFLD
kafkaMessageCompression=Meldingskomprimering
#XFLD
fileGroupDeltaFilesBy=Grupper delta etter
#XFLD
fileFormat=Filtype
#XFLD
csvEncoding=CSV-koding
#XFLD
abapExitLbl=ABAP-exit
#XFLD
deltaPartition=Antall objekttråder for deltadataoverføringer (1-10)
#XFLD
clamping_Data=Mislyktes ved avkorting av data
#XFLD
fail_On_Incompatible=Mislyktes ved inkompatible data
#XFLD
maxPartitionInput=Maks. antall partisjoner
#XFLD
max_Partition=Definer maks. antall partisjoner
#XFLD
include_SubFolder=Inkluder undermapper
#XFLD
fileGlobalPattern=Globalt mønster for filnavn
#XFLD
fileCompression=Filkomprimering
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Filskilletegn
#XFLD
fileIsHeaderIncluded=Filtopptekst
#XFLD
fileOrient=Orient
#XFLD
gbqWriteMode=Skrivemodus
#XFLD
suppressDuplicate=Undertrykk duplikater
#XFLD
apacheSpark=Aktiver Apache Spark-kompatibilitet
#XFLD
clampingDatatypeCb=Fikser datatyper med desimalflyttall
#XFLD
overwriteDatasetSetting=Overskriv målinnstillinger på objektnivå
#XFLD
overwriteSourceDatasetSetting=Overskriv kildeinnstillinger på objektnivå
#XMSG
kafkaInvalidConnectionSetting=Oppgi et tall mellom {0} og {1}.
#XMSG
MinReplicationThreadErrorMsg=Oppgi et tall større enn {0}.
#XMSG
MaxReplicationThreadErrorMsg=Oppgi et tall lavere enn {0}.
#XMSG
DeltaThreadErrorMsg=Oppgi en verdi mellom 1 og 10.
#XMSG
MaxPartitionErrorMsg=Oppgi en verdi mellom 1 <= x <= **********. Standardverdien er 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Oppgi et heltall mellom {0} og {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Bruk replikeringsfaktor for megleren.
#XFLD
serializationFormat=Serialiseringsformat
#XFLD
compressionType=Komprimeringstype
#XFLD
schemaRegistry=Bruk skjemaregister
#XFLD
subjectNameStrat=Strategi for emnenavn
#XFLD
compatibilityType=Kompatibilitetstype
#XFLD
confluentTopicName=Navn på emne
#XFLD
confluentRecordName=Navn på post
#XFLD
confluentSubjectNamePreview=Forhåndsvisning av emnenavn
#XMSG
serializationChangeToastMsgUpdated2=Serialiseringsformat er endret til JSON fordi skjemaregister ikke er aktivert. Hvis du vil endre serialiseringsformatet tilbake til AVRO, må du først aktivere skjemaregister.
#XBUT
confluentTopicNameInfo=Navnet på emnet er alltid basert på navnet på målobjektet. Du kan endre det ved å endre navn på målobjektet.
#XMSG
emptyRecordNameValidationHeaderMsg=Oppgi et postnavn.
#XMSG
emptyPartionHeader=Oppgi antall partisjoner.
#XMSG
invalidPartitionsHeader=Oppgi et gyldig antall partisjoner.
#XMSG
invalidpartitionsDesc=Oppgi et tall mellom 1 og 200000.
#XMSG
emptyrFactorHeader=Oppgi en replikeringsfaktor.
#XMSG
invalidrFactorHeader=Oppgi en gyldig replikeringsfaktor.
#XMSG
invalidrFactorDesc=Oppgi et tall mellom 1 og 32767.
#XMSG
emptyRecordNameValidationDescMsg=Hvis serialiseringsformatet "AVRO" brukes, støttes bare de følgende tegnene:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(understrek)
#XMSG
validRecordNameValidationHeaderMsg=Oppgi et gyldig postnavn.
#XMSG
validRecordNameValidationDescMsgUpdated=Fordi serialiseringsformatet "AVRO" brukes, kan postnavnet bare inneholde alfanumeriske tegn (A-Z, a-z, 0-9) og understrek (_). Det må begynne med en bokstav eller en understrek.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled="Antall objekttråder for deltadataoverføringer" kan fastsettes så snart ett eller flere objekter har lastestypen "Initial og delta".
#XMSG
invalidTargetName=Ugyldig kolonnenavn 
#XMSG
invalidTargetNameDesc=Navnet på målkolonnen kan bare inneholde alfanumeriske tegn (A-Z, a-z, 0-9) og understrek (_).
#XFLD
consumeOtherSchema=Bruk andre skjemaversjoner
#XFLD
ignoreSchemamissmatch=Ignorer manglende skjemasamsvar
#XFLD
confleuntDatatruncation=Mislyktes ved avkorting av data
#XFLD
isolationLevel=Isolasjonsnivå
#XFLD
confluentOffset=Startpunkt
#XFLD
signavioGroupDeltaFilesByText=Ingen
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=Nei
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=Nei

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Projeksjoner
#XBUT
txtAdd=Legg til
#XBUT
txtEdit=Rediger
#XMSG
transformationText=Legg til en projeksjon for å konfigurere filter eller kobling.
#XMSG
primaryKeyRequiredText=Velg en primærnøkkel med Konfigurer skjema.
#XFLD
lblSettings=Innstillinger
#XFLD
lblTargetSetting={0}: Målinnstillinger
#XMSG
@csvRF=Velg filen som inneholder skjemadefinisjonen du vil legge ved alle filer i mappen.
#XFLD
lblSourceColumns=Kildekolonner
#XFLD
lblJsonStructure=JSON-struktur
#XFLD
lblSourceSetting={0}: Kildeinnstillinger
#XFLD
lblSourceSchemaSetting={0}: Innstillinger for kildeskjema
#XBUT
messageSettings=Meldingsinnstillinger
#XFLD
lblPropertyTitle1=Objektegenskaper
#XFLD
lblRFPropertyTitle=Replikeringsflytegenskaper
#XMSG
noDataTxt=Det er ingen kolonner å vise.
#XMSG
noTargetObjectText=Ingen målobjekter er valgt.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Målkolonner
#XMSG
searchColumns=Søk i kolonner
#XTOL
cdcColumnTooltip=Kolonne for deltaregistrering
#XMSG
sourceNonDeltaSupportErrorUpdated=Kildeobjektet støtter ikke deltaregistrering.
#XMSG
targetCDCColumnAdded=To målkolonner er lagt til for deltaregistrering.
#XMSG
deltaPartitionEnable=Objekttrådgrense for deltadataoverføringer er lagt til i kildeinnstillingene.
#XMSG
attributeMappingRemovalTxt=Fjerner ugyldige koblinger som ikke støttes for det nye målobjektet.
#XMSG
targetCDCColumnRemoved=To målkolonner som brukes til deltaregistrering, er fjernet.
#XMSG
replicationLoadTypeChanged=Lastetypen er endret til "Initial og delta".
#XMSG
sourceHDLFLoadTypeError=Endre lastetype til "Initial og delta".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=For å replikere et objekt fra en kildeforbindelse med forbindelsestypen SAP HANA Cloud, datasjøfiler til SAP Datasphere, må du bruke lastetypen "initial og delta".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Aktiver deltaregistrering.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=For å replikere et objekt fra en kildeforbindelse med forbindelsestypen SAP HANA Cloud, datasjøfiler til SAP Datasphere, må du aktivere deltaregistrering.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Endre målobjekt.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Målobjektet kan ikke brukes fordi deltaregistrering er deaktivert. Du kan enten endre navn på målobjektet (som gjør at et nytt objekt med deltaregistrering kan opprettes), eller koble det til et eksisterende objekt hvor deltaregistrering er aktivert.
#XMSG
deltaPartitionError=Oppgi et gyldig antall objekttråder for deltadataoverføringer.
#XMSG
deltaPartitionErrorDescription=Oppgi en verdi mellom 1 og 10.
#XMSG
deltaPartitionEmptyError=Oppgi et antall objekttråder for deltadataoverføringer.
#XFLD
@lblColumnDescription=Beskrivelse
#XMSG
@lblColumnDescriptionText1=For tekniske formål -  håndtering av duplikate poster forårsaket feil under replikering av ABAP-baserte kildeobjekter som ikke har en primærnøkkel.
#XFLD
storageType=Lagring
#XFLD
skipUnmappedColLbl=Hopp over kolonner uten tilordning
#XFLD
abapContentTypeLbl=Innholdstype
#XFLD
autoMergeForTargetLbl=Slå sammen data automatisk
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Generelt
#XFLD
lblBusinessName=Forretningsnavn
#XFLD
lblTechnicalName=Teknisk navn
#XFLD
lblPackage=Pakke
#XFLD
statusPanel=Kjøringsstatus
#XBTN: Schedule dropdown menu
SCHEDULE=Tidsplan
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Rediger tidsplan
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Slett tidsplan
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Opprett tidsplan
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Valideringskontroll for tidsplan mislyktes
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=En tidsplan kan ikke opprettes fordi replikeringsflyten implementeres for øyeblikket.{0}Vent til replikeringsflyten er implementert.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Tidsplan kan ikke opprettes for replikeringsflyter som inneholder objekter med lastetype "initial og delta".
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Tidsplan kan ikke opprettes for replikeringsflyter som inneholder objekter med lastetype "Initial og delta / Bare delta".
#XFLD : Scheduled popover
SCHEDULED=Planlagt
#XFLD
CREATE_REPLICATION_TEXT=Opprett en replikeringsflyt
#XFLD
EDIT_REPLICATION_TEXT=Rediger en replikeringsflyt
#XFLD
DELETE_REPLICATION_TEXT=Slett en replikeringsflyt
#XFLD
REFRESH_FREQUENCY=Frekvens
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Replikeringsflyten kan ikke distribueres fordi den eksisterende tidsplanen {0} ikke støtter lastetypen "initial og delta" ennå.{0}{0} For å distribuere replikeringsflyten, må du sette lastetypene for alle objekter {0} til "Bare initial". Alternativt kan du slette tidsplanen og distribuere {0}replikeringsflyten og deretter starte en ny kjøring. Dette resulterer i en kjøring uten {0}slutt som støtter objekter med lastetypen "Initial og delta".
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Replikeringsflyten kan ikke distribueres fordi den eksisterende tidsplanen {0} ikke støtter lastetypen "Initial og delta / Bare delta" ennå.{0}{0} For å distribuere replikeringsflyten må du sette lastetypene for alle objekter {0} til "Bare initial". Alternativt kan du slette tidsplanen og distribuere {0}replikeringsflyten og deretter starte en ny kjøring. Dette resulterer i en kjøring uten {0}slutt som støtter objekter med lastetypen "Initial og delta / Bare delta".
#XMSG
SCHEDULE_EXCEPTION=Kan ikke hente tidsplandetaljer
#XFLD: Label for frequency column
everyLabel=Hver
#XFLD: Plural Recurrence text for Hour
hoursLabel=Timer
#XFLD: Plural Recurrence text for Day
daysLabel=Dager
#XFLD: Plural Recurrence text for Month
monthsLabel=Måneder
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutter
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Kan ikke hente informasjon om planleggingsmuligheter.
#XFLD :Paused field
PAUSED=Satt på pause
#XMSG
navToMonitoring=Åpne i flytmonitor
#XFLD
statusLbl=Status
#XFLD
lblLastRunExecuted=Start for siste kjøring
#XFLD
lblLastExecuted=Siste kjøring
#XFLD: Status text for Completed
statusCompleted=Fullført
#XFLD: Status text for Running
statusRunning=Kjører
#XFLD: Status text for Failed
statusFailed=Mislykket
#XFLD: Status text for Stopped
statusStopped=Stoppet
#XFLD: Status text for Stopping
statusStopping=Stopper
#XFLD: Status text for Active
statusActive=Aktiv
#XFLD: Status text for Paused
statusPaused=Satt på pause
#XFLD: Status text for not executed
lblNotExecuted=Ikke kjørt ennå
#XFLD
messagesSettings=Meldingsinnstillinger
#XTOL
@validateModel=Valideringsmeldinger
#XTOL
@hierarchy=Hierarki
#XTOL
@columnCount=Antall kolonner
#XMSG
VAL_PACKAGE_CHANGED=Du har tilordnet dette objektet til pakke "{1}". Klikk på "Lagre" for å bekrefte og validere denne endringen. Vær oppmerksom på at du ikke kan angre tilordningen til en pakke i dette redigeringsprogrammet etter at du har lagret.
#XMSG
MISSING_DEPENDENCY=Avhengigheter for objekt ''{0}'' kan ikke løses i pakke {1}.
#XFLD
deltaLoadInterval=Intervall for deltadataoverføring
#XFLD
lblHour=Timer (0-24)
#XFLD
lblMinutes=Minutter (0-59)
#XMSG
maxHourOrMinErr=Oppgi en verdi mellom 0 og {0}
#XMSG
maxDeltaInterval=Maksimumsverdien for intervallet for deltadataoverføring er 24 timer.{0}Endre minuttverdien eller timeverdien tilsvarende.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Bane for målcontainer
#XFLD
confluentSubjectName=Emnenavn
#XFLD
confluentSchemaVersion=Skjemaversjon
#XFLD
confluentIncludeTechKeyUpdated=Inkluder teknisk nøkkel
#XFLD
confluentOmitNonExpandedArrays=Utelat ikke-utvidede matriser
#XFLD
confluentExpandArrayOrMap=Utvis matrise eller kart
#XCOL
confluentOperationMapping=Operasjonstilordning
#XCOL
confluentOpCode=Opcode
#XFLD
confluentInsertOpCode=Sett inn
#XFLD
confluentUpdateOpCode=Oppdater
#XFLD
confluentDeleteOpCode=Slett
#XFLD
expandArrayOrMapNotSelectedTxt=Ikke valgt
#XFLD
confluentSwitchTxtYes=Ja
#XFLD
confluentSwitchTxtNo=Nei
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Feil
#XTIT
executeWarning=Advarsel
#XMSG
executeunsavederror=Lagre replikeringsflyten før du kjører den.
#XMSG
executemodifiederror=Det er endringer som ikke er lagret, i replikeringsflyten. Lagre replikeringsflyten.
#XMSG
executeundeployederror=Du må implementere replikeringsflyten før du kan kjøre det.
#XMSG
executedeployingerror=Vent til implementeringen er ferdig.
#XMSG
msgRunStarted=Kjøring startet
#XMSG
msgExecuteFail=Kan ikke kjøre replikeringsflyten
#XMSG
titleExecuteBusy=Vent litt.
#XMSG
msgExecuteBusy=Vi klargjør dataene dine for kjøring av replikeringsflyten.
#XTIT
executeConfirmDialog=Advarsel
#XMSG
msgExecuteWithValidations=Replikeringsflyten har valideringsfeil. Dette kan føre til at kjøring av replikeringsflyten mislykkes.
#XMSG
msgRunDeployedVersion=Det finnes endringer som må implementeres. Den sist implementerte versjonen av replikeringsflyten vil bli kjørt. Vil du fortsette?
#XBUT
btnExecuteAnyway=Kjør likevel
#XBUT
btnExecuteClose=Lukk
#XBUT
loaderClose=Lukk
#XTIT
loaderTitle=Laster
#XMSG
loaderText=Henter detaljer fra serveren
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Replikeringsflyten til denne ikke-SAP-målforbindelsen kan ikke startes
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=Fordi det ikke finnes utgående volum for denne måneden.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=En administrator kan øke premium utgående blokker for denne
#XMSG
premiumOutBoundRFAdminErrMsgPart2=Tenanten slik at utgående volum blir tilgjengelig for denne måneden.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Feil
#XTIT
deployInfo=Informasjon
#XMSG
deployCheckFailException=Det oppstod et unntak under distribusjonen
#XMSG
deployGBQFFDisabled=Å distribuere replikeringsflyter med en målforbindelse til Google BigQuery er for øyeblikket ikke mulig, fordi vi utfører vedlikehold på denne funksjonen.
#XMSG
deployKAFKAFFDisabled=Å distribuere replikeringsflyter med en målforbindelse til Apache Kafka er for øyeblikket ikke mulig, fordi vi utfører vedlikehold på denne funksjonen.
#XMSG
deployConfluentDisabled=Å distribuere replikeringsflyter med en målforbindelse til Confluent Kafka er ikke mulig for øyeblikket, fordi vi utfører vedlikehold på denne funksjonen.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Navnene på deltaregistreringstabellene brukes allerede av andre tabeller i repository for følgende målobjekter: {0} Du må endre navnene på disse målobjektene for å sikre at de tilknyttede navnene på deltaregistreringstabellene er unike før du kan distribuere replikeringsflyten.
#XMSG
deployDWCSourceFFDisabled=Å distribuere replikeringsflyter med SAP Datasphere som kilde er for øyeblikket ikke mulig, fordi vi utfører vedlikehold på denne funksjonen.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Det er for øyeblikket ikke mulig å distribuere replikeringsflyter som inneholder deltaaktiverte lokale tabeller som kildeobjekter, fordi vi utfører vedlikehold på denne funksjonen.
#XMSG
deployHDLFSourceFFDisabled=Å distribuere replikeringsflyter som har kildeforbindelser med forbindelsestypen SAP HANA Cloud, datasjøfiler er ikke mulig for øyeblikket fordi vi utfører vedlikehold.
#XMSG
deployObjectStoreAsSourceFFDisabled=Å distribuere replikeringsflyter som har skylagringsleverandører, er for øyeblikket ikke mulig.
#XMSG
deployConfluentSourceFFDisabled=Det er for øyeblikket ikke mulig å distribuere replikeringsflyter som har Confluent Kafka som kilde, fordi vi utfører vedlikehold på denne funksjonen.
#XMSG
deployMaxDWCNewTableCrossed=For store replikeringsflyter er det ikke mulig å "lagre og distribuere" dem i én operasjon. Lagre replikeringsflyten først, og deretter kan du distribuere den.
#XMSG
deployInProgressInfo=Distribusjon pågår allerede
#XMSG
deploySourceObjectInUse=Kildeobjekter {0} brukes allerede i replikeringsflyter {1}.
#XMSG
deployTargetSourceObjectInUse=Kildeobjekter {0} brukes allerede i replikeringsflyter {1}. Målobjekter {2} brukes allerede i replikeringsflyter {3}.
#XMSG
deployReplicationFlowCheckError=Feil ved verifisering av replikeringsflyt: {0}
#XMSG
preDeployTargetObjectInUse=Målobjektene {0} brukes allerede i replikeringsflytene {1}, og du kan ikke ha det samme målobjektet i to forskjellige replikeringsflyter. Velg et annet målobjekt og prøv på nytt.
#XMSG
runInProgressInfo=Replikeringsflyt kjører allerede.
#XMSG
deploySignavioTargetFFDisabled=Implementering av replikeringsflyter som har SAP Signavio som sitt mål, er ikke mulig for øyeblikket, fordi vi utfører vedlikehold på denne funksjonen.
#XMSG
deployHanaViewAsSourceFFDisabled=Implementering av replikeringsflyter som har visninger som kildeobjekter for den valgte kildeforbindelsen, er for øyeblikket ikke mulig. Prøv igjen senere.
#XMSG
deployMsOneLakeTargetFFDisabled=Implementering av replikeringsflyter som har MS OneLake som mål, er ikke mulig for øyeblikket fordi vi utfører vedlikehold på denne funksjonen.
#XMSG
deploySFTPTargetFFDisabled=Implementering av replikeringsflyter som har SFTP som mål, er ikke mulig for øyeblikket fordi vi utfører vedlikehold på denne funksjonen.
#XMSG
deploySFTPSourceFFDisabled=Implementering av replikeringsflyter som har SFTP som kilde, er ikke mulig for øyeblikket fordi vi utfører vedlikehold på denne funksjonen.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Teknisk navn
#XFLD
businessNameInRenameTarget=Forretningsnavn
#XTOL
renametargetDialogTitle=Endre navn på målobjekt
#XBUT
targetRenameButton=Endre navn
#XBUT
targetRenameCancel=Avbryt
#XMSG
mandatoryTargetName=Du må oppgi et navn.
#XMSG
dwcSpecialChar=_(understrek) er det eneste spesialtegn som er tillatt.
#XMSG
dwcWithDot=Måltabellnavnet kan bestå av latinske bokstaver, tall, understreking (_) og punktum (.). Det første tegnet må være en bokstav, et tall eller understrek (ikke punktum).
#XMSG
nonDwcSpecialChar=Tillatte spesialtegn er _(understrek) -(bindestrek) .(punktum)
#XMSG
firstUnderscorePattern=Navn kan ikke starte med _(understrek)

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: Vis SQL-setning for oppretting av tabell
#XMSG
sqlDialogMaxPKWarning=Maksimalt 16 primærnøkler støttes i Google BigQuery. Kildeobjektet har mer enn 16 primærnøkler. Ingen primærnøkler er derfor definert i denne setningen.
#XMSG
sqlDialogIncomptiblePKTypeWarning=En eller flere kildekolonner har datatyper som ikke kan defineres som primærnøkler i Google BigQuery. Derfor er ingen primærnøkler definert i dette tilfellet. Bare følgende datatyper kan ha en primærnøkkel i Google BigQuery: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Kopier og lukk
#XBUT
closeDDL=Lukk
#XMSG
copiedToClipboard=Kopiert til utklippstavle


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Objektet ''{0}'' kan ikke være en del av oppgavekjeden fordi det ikke har en slutt (det inkluderer objekter med lastetypen Initial og delta / Bare delta).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Objektet ''{0}'' kan ikke være en del av oppgavekjeden fordi det ikke har en slutt (det inkluderer objekter med lastetypen Initial og Delta).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objektet "{0}" kan ikke legges til i oppgavekjeden.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Det finnes ulagrede målobjekter. Lagre på nytt.{0}{0} Atferden til denne funksjonen er endret: Tidligere ble målobjekter bare opprettet i målmiljøet da replikeringsflyten ble distribuert.{0} Nå blir objektene opprettet allerede under lagring av replikeringsflyten. Replikeringsflyten ble opprettet før denne endringen, og den inneholder nye objekter.{0} Du må lagre replikeringsflyten en gang til før du distribuerer den, slik at de nye objektene blir inkludert.
#XMSG
confirmChangeContentTypeMessage=Du er i ferd med å endre innholdstypen. Hvis du gjør dette, vil alle eksisterende projeksjoner slettes.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Emnenavn
#XFLD
schemaDialogVersionName=Skjemaversjon
#XFLD
includeTechKey=Inkluder teknisk nøkkel
#XFLD
segementButtonFlat=Flat
#XFLD
segementButtonNested=Nestet
#XMSG
subjectNamePlaceholder=Søk etter emnenavn

#XMSG
@EmailNotificationSuccess=Konfigurasjon av e-postvarsling under kjøringstid er lagret.

#XFLD
@RuntimeEmailNotification=E-postvarsling under kjøringstid

#XBTN
@TXT_SAVE=Lagre


