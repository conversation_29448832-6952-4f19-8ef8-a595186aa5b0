#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Ροή Αντιγραφής

#XFLD: Edit Schema button text
editSchema=Επεξεργασία σχήματος

#XTIT : Properties heading
configSchema=Διαμόρφωση Σχήματος

#XFLD: save changed button text
applyChanges=Εφαρμογή Αλλαγών


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Επιλογή Σύνδεσης Πηγής
#XFLD
sourceContainernEmptyText=Επιλογή Αποθηκευτικής Μονάδας
#XFLD
targetConnectionEmptyText=Επιλογή Σύνδεσης Στόχου
#XFLD
targetContainernEmptyText=Επιλογή Αποθηκευτικής Μονάδας
#XFLD
sourceSelectObjectText=Επιλογή Αντικειμένου Πηγής
#XFLD
sourceObjectCount=Αρχικά Αντικείμενα ({0})
#XFLD
targetObjectText=Τελικά Αντικείμενα
#XFLD
confluentBrowseContext=Επιλογή Γενικού Πλαισίου
#XBUT
@retry=Νέα είσοδος
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Αναβάθμιση μισθωτή σε εξέλιξη.

#XTOL
browseSourceConnection=Αναζήτηση σύνδεσης πηγής
#XTOL
browseTargetConnection=Αναζήτηση σύνδεσης στόχου
#XTOL
browseSourceContainer=Αναζήτηση αποθηκευτικής μονάδας πηγής
#XTOL
browseAndAddSourceDataset=Προσθήκη Αντικειμένων Πηγής
#XTOL
browseTargetContainer=Αναζήτηση αποθηκευτικής μονάδας στόχου
#XTOL
browseTargetSetting=Αναζήτηση ρυθμίσεων στόχου
#XTOL
browseSourceSetting=Αναζήτηση ρυθμίσεων πηγής
#XTOL
sourceDatasetInfo=Πληροφορίες
#XTOL
sourceDatasetRemove=Διαγραφή
#XTOL
mappingCount=Αυτό αντιπρσσωπεύει τον συνολικό αριθμό αντιστοιχίσεων/εκφράσεων που δεν βασίζονται σε όνομα.
#XTOL
filterCount=Αυτό αντιπροσωπεύει τον συνολικό αριθμό συνθηκών φίλτρου.
#XTOL
loading=Φόρτωση...
#XCOL
deltaCapture=Λήψη Delta
#XCOL
deltaCaptureTableName=Πίνακας Λήψης Delta
#XCOL
loadType=Τύπος Φόρτωσης
#XCOL
deleteAllBeforeLoading=Διαγραφή Όλων πριν την Φόρτωση
#XCOL
transformationsTab=Προβολές
#XCOL
settingsTab=Ρυθμίσεις

#XBUT
renameTargetObjectBtn=Μετονομασία Αντικειμένου Στόχου
#XBUT
mapToExistingTargetObjectBtn=Αντιστοίχιση σε Υπάρχον Αντικείμενο Στόχου
#XBUT
changeContainerPathBtn=Αλλαγή Διαδρομής Αποδέκτη
#XBUT
viewSQLDDLUpdated=Προβολή Δημιουργίας Δήλωσης Πίνακα SQL
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Το πηγαίο αντικείμενο δεν υποστηρίζει λήψη delta,αλλά το επιλεγμένο αντικείμενο στόχου έχει ενεργή την λήψη delta.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Το αντικείμενο στόχου δεν μπορεί να χρησιμοποιηθεί γιατί είναι ενεργό ένα delta,{0}ενώ το αντικείμενο πηγής δεν υποστηρίζει το delta.{1}Μπορείτε να επιλέξετε άλλο αντικείμενο στόχου που δεν υποστηρίζει το delta.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Ένα αντικείμενο στόχου με το ίδιο όνομα υπάρχει ήδη. Ωστόσο, δεν μπορεί να χρησιμοποιηθεί{0}γιατί το delta είναι ενεργό ενώ το αντικείμενο στόχου δεν{0}υποστηρίζει το delta.{1}Μπορείτε να εισάγετε το όνομα ενός υπάρχοντος αντικειμένου στόχου που δεν{0}υποστηρίζει το delta ή να εισάγετε ένα όνομα που δεν υπάρχει ακόμα.
#XBUT
copySQLDDLUpdated=Αντιγραφή Δημιουργίας Δήλωσης Πίνακα SQL
#XMSG
targetObjExistingNoCDCColumnUpdated=Οι υπάρχοντες πίνακες στο Google BigQuery πρέπει να περιλαμβάνουν τις παρακάτω στήλες για αλλαγή τίτλου δεδομένων (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Τα ακόλουθα αντικείμενα πηγής δεν υποστηρίζονται γιατί δεν έχουν αρχικό κωδικό ή χρησιμοποιούν μία σύνδεση που δεν πληροί τους όρους για ανάκτησή του:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Δείτε το SAP KBA 3531135 για μία πιθανή λύση.
#XLST: load type list values
initial=Αρχικό Μόνο
@emailUpdateError=Σφάλμα στην ενημέρωση λίστας Ηλεκτρονικής Ειδοποίησης

#XLST
initialDelta=Αρχικό και Delta

#XLST
deltaOnly=Delta Μόνο
#XMSG
confluentDeltaLoadTypeInfo=Για την πηγή Confluent Kafka, υποστηρίζεται μόνο ο τύπος φόρτωσης Αρχικό και Delta.
#XMSG
confirmRemoveReplicationObject=Θέλετε να επιβεβαιώσετε ότι θέλετε να διαγράψετε την αντιγραφή;
#XMSG
confirmRemoveReplicationTaskPrompt=Αυτή η ενέργεια θα διαγράψει υπάρχουσες αντιγραφές. Θέλετε να συνεχίσετε;
#XMSG
confirmTargetConnectionChangePrompt=Αυτή η ενέργεια θα επανακαθορίσει την σύνδεση στόχου, την αποθηκευτική μονάδα στοχου και θα διαγράψει όλα τα αντικείμενα στόχου. Θέλετε να συνεχίσετε;
#XMSG
confirmTargetContainerChangePrompt=Αυτή η ενέργεια θα επανακαθορίσει την αποθηκευτική μονάδα στόχου και θα διαγράψει όλα τα υπάρχοντα αντικείμενα στόχου. Θέλετε να συνεχίσετε;
#XMSG
confirmRemoveTransformObject=Θέλετε να επιβεβαιώσετε ότι θέλετε να διαγράψετε την προβολή {0};
#XMSG
ErrorMsgContainerChange=Σφάλμα κατά την αλλαγή διαδρομής αποθηκευτικής μονάδας.
#XMSG
infoForUnsupportedDatasetNoKeys=Τα παρακάτω αντικείμενα πηγής δεν υποστηρίζονται γιατί δεν έχουν αρχικό κωδικό:
#XMSG
infoForUnsupportedDatasetView=Τα παρακάτω αντικείμενα πηγής με τύπο Προβολές δεν υποστηρίζονται:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Το παρακάτω αντικείμενο πηγής δεν υποστηρίζεται καθώς δεν είναι προβολή SQL που περιέχει παραμέτρους εισόδου:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Τα παρακάτω αντικείμενα πηγής δεν υποστηρίζονται γιατί η εξαγωγή είναι ανενεργή για αυτά:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Για συνδέσεις Confluent, οι μόνες επιτρεπτές μορφές σειριοποίησης είναι AVRO και JSON. Τα παρακάτω αντικείμενα δεν υποστηρίζονται γιατί χρησιμοποιούν διαφορετική μορφή σειριοποίησης:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Αδύνατη λήψη σχήματος για τα ακόλουθα αντικείμενα. Επιλέξτε το κατάλληλο πλαίσιο ή επικυρώστε τη διαμόρφωση μητρώου σχήματος
#XTOL: warning dialog header on deleting replication task
deleteHeader=Διαγραφή
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Η ρύθμιση Διαγραφή Όλων πριν τη Φόρτωση δεν υποστηρίζεται στο Google BigQuery.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Η ρύθμιση Διαγραφή Όλων πριν τη Φόρτωση διαγράφει και αναδημιουργεί το αντικείμενο (θέμα) πριν από κάθε αντιγραφή. Αυτό διαγράφει όλα τα αντιστοιχισμένα μηνύματα.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Η ρύθμιση Διαγραφή Όλων Πριν δεν υποστηρίζεται για αυτό τον τύπο στόχου.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Τεχνικό Ονομα
#XCOL
connBusinessName=Επιχειρηματικό Όνομα
#XCOL
connDescriptionName=Περιγραφή
#XCOL
connType=Τύπος
#XMSG
connTblNoDataFoundtxt=Δεν Βρέθηκαν Συνδέσεις
#XMSG
connectionError=Σφάλμα κατά την λήψη συνδέσεων.
#XMSG
connectionCombinationUnsupportedErrorTitle=Συνδυασμός στόχων δεν υποστηρίζεται
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Αντιγραφή από {0} σε {1} δεν υποστηρίζεται.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Συνδυασμός τύπων σύνδεσης δεν υποστηρίζεται
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Αντιγραφή από μία σύνδεση με τύπο σύνδεσης SAP HANA Cloud, Αρχεία Data Lake σε {0} δεν υποστηρίζεται. Μπορείτε μόνο να αντιγράψετε στο SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Επιλογή
#XBUT
containerCancelBtn=Ακύρωση
#XTOL
containerSelectTooltip=Επιλογή
#XTOL
containerCancelTooltip=Ακύρωση
#XMSG
containerContainerPathPlcHold=Διαδρομή Αποθηκευτικής Μονάδας
#XFLD
containerContainertxt=Αποθηκευτική μονάδα
#XFLD
confluentContainerContainertxt=Πλαίσιο
#XMSG
infoMessageForSLTSelection=Μόνο το SLT/ID Μαζικής Μεταφοράς επιτρέπεται ως αποθηκευτική μονάδα. Επιλέξτε ένα ID Μαζικής Μεταφοράς κάτω από το SLT (αν είναι διαθέσιμο) και πατήστε υποβολή.
#XMSG
msgFetchContainerFail=Σφάλμα κατά την λήψη δεδομένων αποθηκευτικής μονάδας.
#XMSG
infoMessageForSLTHidden=Αυτή η σύνδεση δεν υποστηρίζει φακέλους SLT, έτσι δεν εμφανίζονται στην παρακάτω λίστα.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Επιλέξτε έναν αποδέκτη που περιέχει υποφακέλους.
#XMSG
sftpIncludeSubFolderText=Λάθος
#XMSG
sftpIncludeSubFolderTextNew=Οχι

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Καμία αντιστοίχιση φίλτρου ακόμα)
#XMSG
failToFetchRemoteMetadata=Σφάλμα κατά την λήψη μεταδεδομένων.
#XMSG
failToFetchData=Σφάλμα κατά την λήψη υπάρχοντος στόχου.
#XCOL
@loadType=Τύπος Φόρτωσης
#XCOL
@deleteAllBeforeLoading=Διαγραφή Όλων πριν την Φόρτωση

#XMSG
@loading=Φόρτωση...
#XFLD
@selectSourceObjects=Επιλογή Αντικειμένων Πηγής
#XMSG
@exceedLimit=Δεν μπορείτε να καταχωρίσετε περισσότερα από {0} αντικείμενα ταυτόχρονα. Αποεπιλέξτε τουλάχιστον {1} αντικείμενα.
#XFLD
@objects=Αντικείμενα
#XBUT
@ok=ΟΚ
#XBUT
@cancel=Ακύρωση
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Επόμενο
#XBUT
btnAddSelection=Προσθήκη Επιλογής
#XTOL
@remoteFromSelection=Διαγραφή από Επιλογή
#XMSG
@searchInForSearchField=Αναζήτηση σε {0}

#XCOL
@name=Τεχνικό Ονομα
#XCOL
@type=Τύπος
#XCOL
@location=Τοποθεσία
#XCOL
@label=Επωνυμία Επιχείρησης
#XCOL
@status=Κατάσταση

#XFLD
@searchIn=Αναζήτηση σε:
#XBUT
@available=Διαθέσιμο
#XBUT
@selection=Επιλογή

#XFLD
@noSourceSubFolder=Πίνακες και Προβολές
#XMSG
@alreadyAdded=Υπάρχει ήδη στο διάγραμμα
#XMSG
@askForFilter=Υπάρχουν περισσότερα από {0} στοιχεία. Εισάγετε ένα φίλτρο για να περιορίσετε τον αριθμό στοιχείων.
#XFLD: success label
lblSuccess=Επιτυχία
#XFLD: ready label
lblReady=Ετοιμο
#XFLD: failure label
lblFailed=Απέτυχε
#XFLD: fetching status label
lblFetchingDetail=Χρήση στοιχείων

#XMSG Place holder text for tree filter control
filterPlaceHolder=Εισάγετε κείμενο για φιλτράρισμα αντικειμένων ανώτερου επιπέδου
#XMSG Place holder text for server search control
serverSearchPlaceholder=Εισάγετε και πατήστε Enter για αναζήτηση
#XMSG
@deployObjects=Εισαγωγή {0} αντικειμένων...
#XMSG
@deployObjectsStatus=Αριθμός αντικειμένων που καταχωρίστηκαν : {0}. Αριθμός αντικειμένων που δεν μπόρεσαν να εισαχθούν : {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Αδύνατο άνοιγμα τοπικού προγράμματος περιήγησης αποθήκης.
#XMSG
@openRemoteSourceBrowserError=Αδύνατη λήψη αντικειμένων πηγής.
#XMSG
@openRemoteTargetBrowserError=Αδύνατη λήψη αντικειμένων στόχου.
#XMSG
@validatingTargetsError=Σφάλμα κατά την επαλήθευση στόχων.
#XMSG
@waitingToImport=Έτοιμο για Εισαγωγή

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Ο μέγιστος αριθμός αντικειμένων ξεπεράστηκε. Επιλέξτε μέχρι 500 αντικείμενα για μία ροή αντιγραφής.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Τεχνικό Ονομα
#XFLD
sourceObjectBusinessName=Επιχειρηματικό Όνομα
#XFLD
sourceNoColumns=Αριθμός Στηλών
#XFLD
containerLbl=Αποθηκευτική μονάδα

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Πρέπει να επιλέξετε μία σύνδεση πηγής για την ροή αντιγραφής.
#XMSG
validationSourceContainerNonExist=Πρέπει να επιλέξετε μία αποθηκευτική μονάδα για την σύνδεση πηγής.
#XMSG
validationTargetNonExist=Πρέπει να επιλέξετε μία σύνδεση στόχου για την ροή αντιγραφής.
#XMSG
validationTargetContainerNonExist=Πρέπει να επιλέξετε μία αποθηκευτική μονάδα για την σύνδεση στόχου.
#XMSG
validationTruncateDisabledForObjectTitle=Αντιγραφή στις αποθήκες αντικειμένων.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Αντιγραφή σε αποθήκη cloud είναι εφικτή μόνο αν έχει οριστεί η επιλογή Διαγραφή Όλων πριν τη Φόρτωση ή αν το αντικείμενο στόχου δεν υπάρχει στον στόχο.{0}{0} Για να ενεργοποιήσετε την αντιγραφή για αντικείμενα για τα οποία δεν έχει οριστεί η επιλογή Διαγραφή Όλων πριν τη Φόρτωση, βεβαιωθείτε ότι το αντικείμενο στόχος δεν υπάρχει στο σύστημα πριν εκτελέσετε τη ροή της αντιγραφής. 
#XMSG
validationTaskNonExist=Πρέπει να έχετε τουλάχιστον μία αντιγραφή στη ροή αντιγραφής.
#XMSG
validationTaskTargetMissing=Πρέπει να έχετε έναν στόχο για την αντιγραφή με την πηγή: {0}
#XMSG
validationTaskTargetIsSAC=Ο επιλεγμένος στόχος είναι ένα SAC Artefact: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Ο επιλεγμένος στόχος δεν είναι υποστηριζόμενος τοπικός πίνακας: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Ενα αντικείμενο με αυτό το όνομα υπάρχει ήδη στον στόχο. Ωστόσο, αυτό το αντικείμενο δεν μπορεί να χρησιμοποιηθεί ως αντικείμενο στόχος για μία ροή αντιγραφής στην τοπική αποθήκη καθώς δεν είναι τοπικός πίνακας.
#XMSG
validateSourceTargetSystemDifference=Πρέπει να επιλέξετε διαφορετική σύνδεση πηγής και στόχου και συνδυασμούς αποθηκευτικών μονάδων για την ροή αντιγραφής.
#XMSG
validateDuplicateSources=μία ή περισσότερες αντιγραφές έχουν διπλά ονόματα αντικειμένων πηγής: {0}.
#XMSG
validateDuplicateTargets=μία ή περισσότερες αντιγραφές έχουν διπλά ονόματα αντικειμένων στόχου: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=Το πηγαίο αντικείμενο {0} δεν υποστηρίζει λήψη delta, ενώ το αντικείμενο στόχου {1} όχι. Πρέπει να διαγράψετε την αντιγραφή.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Πρέπει να επιλέξετε τον τύπο φόρτωσης "Αρχικό και Delta" για την αντιγραφή με όνομα αντικειμένου στόχου {0}.
#XMSG
validationAutoRenameTarget=Οι στήλες στόχου μετονομάστηκαν.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Μία αυτόματη προβολή προστέθηκε και οι παρακάτω τελικές στήλες μετονομάστηκαν για να επιτρέπεται αντιγραφή στον στόχο:{1}{1} {0} {1}{1}Αυτό οφείλεται σε έναν από τους παρακάτω λόγους:{1}{1}{2} Μη υποστηριζόμενοι χαρακτήρες{1}{2} Δεσμευμένο πρόθεμα
#XMSG
validationAutoRenameTargetDescriptionUpdated=Μία αυτόματη προβολή προστέθηκε και οι παρακάτω τελικές στήλες μετονομάστηκαν ώστε να επιτρέπεται η αντιγραφή στο Google BigQuery:{1}{1} {0} {1}{1}Αυτό οφείλεται σε έναν από τους εξής λόγους:{1}{1}{2} Δεσμευμένο όνομα στήλης{1}{2} Μη υποστηριζόμενοι χαρακτήρες{1}{2} Δεσμευμένο πρόθεμα
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Μία αυτόματη προβολή προστέθηκε και οι παρακάτω τελικές στήλες μετονομάστηκαν ώστε να επιτρέπονται αντιγραφές στο Confluent:{1}{1} {0} {1}{1}Αυτό οφείλεται σε έναν από τους παρακάτω λόγους:{1}{1}{2}Δεσμευμένο όνομα στήλης{1}{2} Μη υποστηριζόμενοι χαρακτήρες{1}{2} Δεσμευμένο πρόθεμα
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Μία αυτόματη προβολή προστέθηκε και οι παρακάτω τελικές στήλες μετονομάστηκαν ώστε να επιτρέπονται αντιγραφές στο στόχο:{1}{1} {0} {1}{1}Αυτό οφείλεται στα:{1}{1}{2} Δεσμευμένο όνομα στήλης{1}{2} μη υποστηριζόμενοι χαρακτήρες{1}{2} Δεσμευμένο πρόθεμα
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Το αντικείμενο στόχου μετονομάστηκε.
#XMSG
autoRenameInfoDesc=Το αντικείμενο στόχου μετονομάστηκε γιατί περιείχε μη υποστηριζόμενους χαρακτήρες. Μόνο οι παρακάτω χαρακτήρες υποστηρίζονται:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(τελεία){0}{1}_(κάτω παύλα){0}{1}-(μεσαία παύλα)
#XMSG
validationAutoTargetTypeConversion=Οι τύποι δεδομένων στόχου άλλαξαν.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Για τις παρακάτω τελικές στήλες, οι τύποι τελικών δεδομένων άλλαξαν γιατί στο Google BigQuery οι τύποι δεδομένων πηγής δεν υποστηρίζονται:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Για τις παρακάτω τελικές στήλες, οι τύποι δεδομένων στόχου άλλαξαν γιατί δεν υποστηρίζονται οι τύποι δεδομένων πηγής στην σύνδεση στόχου:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Υποστηριζόμενα ονόματα στηλών στόχου.
#XMSG
validationMaxCharLengthGBQTargetDescription=Στο Google BigQuery, τα ονόματα στήλης μπορούν να έχουν μέχρι 300 χαρακτήρες. Χρησιμοποιήστε μία προβολή για να μικρύνετε τα παρακάτω ονόματα τελικών στηλών:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Αδύνατη δημιουργία αρχικών κωδικών.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=Στο Google BigQuery, ένας μέγιστος αριθμός 16 αρχικών κωδικών υποστηρίζεται, αλλά το αντικείμενο πηγής έχει έναν μεγαλύτερο αριθμό αρχικών κωδικών. Δεν θα δημιουργηθούν αρχικοί κωδικοί στο αντικείμενο στόχου.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Μία ή περισσότερες στήλες έχουν τύπους δεδομένων που δεν μπορούν να καθοριστούν ως αρχικοί κωδικοί στο Google BigQuery. Δεν θα δημιουργηθούν αρχικοί κωδικοί στο τελικό αντικείμενο.{0}{0}Οι παρακάτω τελικοί τύποι δεδομένων είναι συμβατοί με τύπους δεδομένων Google BigQuery για τους οποίους μπορεί να καθοριστεί αρχικός κωδικός :{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Καθορίστε μία ή περισσότερες στήλες ως αρχικό κωδικό.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Πρέπει να καθορίσετε μία ή περισσότερες ως αρχικό κωδικό, χρησιμοποιήστε τον διάλογο σχήματος πηγής για να το κάνετε.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Καθορίστε μία ή περισσότερες στήλες ως αρχικό κωδικό.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Πρέπει να καθορίσετε μία ή περισσότερες στήλες ως αρχικό κωδικό που ταιριάζει με τους περιορισμούς αρχικών κωδικών για το αντικείμενο πηγής. Μεταβείτε στο Διαμόρφωση Σχήματος στις ιδιότητες αντικειμένου πηγής για να το κάνετε.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Εισάγετε έγκυρη τιμή τμήματος
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Η μέγιστη τιμή διαχωρισμού πρέπει να είναι ≥ 1 και ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Καθορίστε μία ή περισσότερες στήλες ως αρχικό κωδικό.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Για να αντιγράψετε ένα αντικείμενο πρέπει να καθορίσετε μία ή περισσότερες στήλες ως αρχικό κωδικό. Χρήση προβολής για να γίνει αυτό.
#XMSG
validateHDLFNoPKExistingDatasetError=Καθορίστε μία ή περισσότερες στήλες ως αρχικό κωδικό.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Για να αντιγράψετε δεδομένα σε ένα υπάρχον αντικείμενο στόχου, πρέπει το αντικείμενο να έχει μία ή περισσότερες στήλες που καθορίστηκαν ως αρχικός κωδικός. {0} Έχετε τις παρακάτω επιλογές για να καθορίσετε μία ή περισσότερες στήλες ως αρχικό κωδικό: {0} {1} Χρήση τοπικού επεξεργαστή πίνακα για αλλαγή υπάρχοντος αντικειμένου στόχου. Επειτα επαναφορτώστε τη ροή αντιγραφής.{0}{1} Μετονομάστε το αντικείμενο στόχου στη ροή αντιγραφής. Αυτό θα δημιουργήσει ένα νέο αντικείμενο μόλις ξεκινήσει η εκτέλεση. Μετά τη μετονομασία μπορείτε να καθορίσετε μία ή περισσότερες στήλες ως αρχικό κωδικό σε μία προβολή.{0}{1} Αντιστοιχίστε το αντικείμενο σε άλλο αντικείμενο στόχου στο οποίο μία ή περισσότερε στήλες ορίστηκαν ήδη ως αρχικός κωδικός.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Επιλεγμένος στόχος υπάρχει ήδη στην αποθήκη: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Τα ονόματα πίνακα λήψης delta χρησιμοποιούνται ήδη από άλλους πίνακες στην αποθήκη: {0}. Πρέπει να μετονομάσετε αυτά τα αντικείμενα στόχου για να βεβαιωθείτε ότι τα σχετικά ονόματα πίνακα λήψης delta είναι μοναδικά πριν αποθηκεύσετε την ροή αντιγραφής.
#XMSG
validateConfluentEmptySchema=Καθορισμός Σχήματος
#XMSG
validateConfluentEmptySchemaDescUpdated=Ο πίνακας πηγής δεν έχει σχέδιο. Επιλέξτε Διαμόρφωση Σχεδίου για να καθορίσετε ένα.
#XMSG
validationCSVEncoding=Ακυρη Κωδικοποίηση CSV 
#XMSG
validationCSVEncodingDescription=Η κωδικοποίηση CSV της εργασίας είναι άκυρη.
#XMSG
validateConfluentEmptySchema=Επιλέξτε ένα συμβατό τύπο δεδομένων στόχου
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Επιλέξτε ένα συμβατό τύπο δεδομένων στόχου
#XMSG
globalValidateTargetDataTypeDesc=Σφάλμα με αντιστοιχίσεις στήλης. Άνοιγμα Προβολής και βεβαίωση ότι όλες οι στήλες πηγής είναι αντιστοιχισμένες με μία μοναδική στήλη, με μία στήλη που έχει ασύμβατο τύπο δεδομένων και όλες οι καθορισμένες εκφράσεις είναι έγκυρες.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Διπλά Ονόματα Στήλης.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Διπλά ονόματα στήλης δεν υποστηρίζονται. Χρήση Διαλόγου Προβολής για να τα διορθώσετε. Τα παρακάτω αντικείμενα αστόχου έχουν διπλά ονόματα στόχου:{0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Διπλά Ονόματα Στήλης.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Διπλά ονόματα στήλης δεν υποστηρίζονται. Τα παρακάτω αντικείμενα στόχου έχουν διπλά αντικείμενα στήλης: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Μπορεί να υπάρχουν ασυνέπειες στα δεδομένα.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Ο τύπος φόρτωσης Delta Only δεν θα λάβει υπόψη τις αλλαγές που έγιναν στην πηγή μεταξύ της τελευταίας αποθήκευσης και της επόμενης εκτέλεσης.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Αλλαγή τύπου φόρτωσης σε "Αρχικό".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Η αντιγραφή αντικειμένων βάσει ΑΒΑΡ που δεν έχουν αρχικό κωδικό είναι εφικτή μόνο για τύπο φόρτωσης "Αρχικό Μόνο".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Απενεργοποίηση Λήψης Delta.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Για να αντιγράψετε ένα αντικείμενο που δεν έχει αρχικό κωδικό χρησιμοποιώντας ΑΒΑΡ τύπου σύνδεσης πηγής πρέπει να απενεργοποιήσετε την λήψη delta για αυτό τον πίνακα.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Αλλαγή τελικού αντικειμένου.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Το τελικό αντικείμενο δεν μπορεί να χρησιμοποιηθεί γιατί είναι ενεργή η λήψη delta. Μπορείτε είτε να μετονομάσετε το τελικό αντικείμενο κι έπειτα να απενεργοποιήσετε τη λήψη delta για το νέο (μετονομασμένο ) αντικείμενο ή να αντιστοιχίσετε το αρχικό αντικείμενο σε ένα τελικό για το οποίο δεν είναι ενεργή η λήψη delta.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Αλλαγή τελικού αντικειμένου.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Tο τελικό αντικείμενο δεν μπορεί να χρησιμοποιηθεί γιατί δεν έχει το απαιτούμενο technical column __load_package_id. Μπορείτε να μετονομάσετε το τελικό αντικείμενο χρησιμοποιώντας ένα όνομα που δεν υπάρχει ακόμα. Το σύστημα θα δημιουργήσει ένα νέο αντικείμενο που έχει τον ίδιο ορισμό με το αρχικό αντικείμενο και περιέχει την τεχνική στήλη. Διαφορετικά, μπορείτε να αντιστοιχίσετε το τελικό αντικέιμενο σε ένα υπάρχον που έχει την απαιτούμενη στήλη (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Αλλαγή τελικού αντικειμένου.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Το τελικό αντικείμενο δεν μπορεί να χρησιμοποιηθεί γιατί δεν έχει το απαιτούμενο technical column __load_record_id. Μπορείτε να μετονομάσετε το τελικό αντικείμενο με ένα όνομα που δεν υπάρχει ακόμα. Το σύστημα θα δημιουργήσει ένα νέο αντικείμενο που έχει τον ίδιο ορισμό με το πηγαίο αντικείμενο και περιέχει την τεχνική στήλη. Διαφορετικά, μπορείτε να αντιστοιχίσετε το τελικό αντικείμενο με ένα υπάρχον που έχει την απαιτούμενη στήλη (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Αλλαγή τελικού αντικειμένου.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Το τελικό αντικείμενο δεν μπορεί να χρησιμοποιηθεί γιατί ο τύπος δεδομένων του technical column __load_record_id δεν είναι "string(44)". Μπορείτε να μετονομάσετε το τελικό αντικείμενο με ένα όνομα που δεν υπάρχει. Επειτα το σύστημα δημιουργεί ένα νέο αντικείμενο που έχει ίδιο ορισμό με το αρχικό και τον σωστό τύπο δεδομένων. Διαφορετικά, μπορείτε να αντιστοιχίσετε το τελικό αντικείμενο σε ένα υπάρχον που έχει την απαιτούμενη τεχνική στήλη (__load_record_id) με τον σωστό τύπο δεδομένων.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Αλλαγή τελικού αντικειμένου.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Το τελικό αντικείμενο δεν μπορεί να χρησιμοποιηθεί γιατί έχει αρχικό κωδικό, ενώ το αρχικό αντικείμενο δεν έχει. Μπορείτε να μετονομάσετε το τελικό αντικείμενο με ένα όνομα που δεν υπάρχει ακόμα. Το σύστημα τότε θα δημιουργήσει ένα νέο αντικείμενο με ίδιο ορισμό με το αρχικό αντικείμενο και χωρίς αρχικό κωδικό. Διαφορετικά, μπορείτε να αντιστοιχίσετε το τελικό αντικείμενο με ένα υπάρχον που έχει την απαιτούμενη τεχνική στήλη  (__load_package_id) και δεν έχει αρχικό κωδικό.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Αλλαγή τελικού αντικειμένου.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Το τελικό αντικείμενο δεν μπορεί να χρησιμοποιηθεί γιατί έχει αρχικό κωδικό, ενώ το αρχικό αντικείμενο δεν έχει. Μπορείτε να μετονομάσετε το τελικό αντικείμενο με ένα όνομα που δεν υπάρχει ακόμα. Το σύστημα τότε θα δημιουργήσει ένα νέο αντικείμενο με ίδιο ορισμό με το αρχικό αντικείμενο και χωρίς αρχικό κωδικό. Διαφορετικά, μπορείτε να αντιστοιχίσετε το τελικό αντικείμενο με ένα υπάρχον που έχει την απαιτούμενη τεχνική στήλη(__load_record_id) και δεν έχει αρχικό κωδικό.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Αλλαγή τελικού αντικειμένου.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Το τελικό αντικείμενο δεν μπορεί να χρησμοποιηθεί γιατί ο τύπος δεδομένων του technical column __load_package_id δεν είναι "binary(>=256)". Μπορείτε να μετονομάσετε το τελικό αντικείμενο με ένα όνομα που δεν υπάρχει ακόμα. Το σύστημα τότε θα δημιουργήσει ένα νέο αντικείμενο με ίδιο ορισμό με το αρχικό αντικείμενο και συνεπώς το σωστό τύπο δεδομένων. Διαφορετικά, μπορείτε να αντιστοιχίσετε το τελικό αντικείμενο με ένα υπάρχον που έχει την απαιτούμενη τεχνική στήλη (__load_package_id) με σωστό τύπο δεδομένων.
#XMSG
validationAutoRenameTargetDPID=Οι στήλες στόχου μετονομάστηκαν.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Διαγραφή αντικειμένου πηγής.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Το αντικείμενο πηγής δεν έχει στήλη κωδικού, δεν υποστηρίζεται σε αυτό το πλαίσιο.
#XMSG
validationAutoRenameTargetDPIDDescription=Μία αυτόματη προβολή προστέθηκε και οι παρακάτω τελικές στήλες μετονομάστηκαν ώστε να επιτρέπεται η αντιγραφή από την πηγή ABAP χωρίς κωδικούς :{1}{1} {0} {1}{1}Αυτό οφείλεται στους εξής λόγους:{1}{1}{2} Ονομα δεσμευμένης στήλης{1}{2} Μη υποστηριζόμενοι χαρακτήρες{1}{2} Δεσμευμένο πρόθεμα
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Αντιγραφή σε {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Η αποθήκευση και ανάπτυξη ροών αντιγραφής που έχουν {0} ως στόχο τους είναι αδύνατη γιατί εκτελούμε συντήρηση σε αυτή την λειτουργία.
#XMSG
TargetColumnSkippedLTF=Η στήλη στόχου παραβλέφθηκε.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Η στήλη στόχου παραβλέφθηκε λόγω μη υποστηριζόμενου τύπου δεδομένων.{0}{1}
#XMSG
validatePKTimeColumnLTF1=Στήλη Χρόνου ως Αρχικός Κωδικός
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Το αντικείμενο πηγής έχει στήλη χρόνου ως αρχικό κωδικό που δεν υποστηρίζεται σε αυτό το πλαίσιο.
#XMSG
validateNoPKInLTFTarget=Αρχικός Κωδικός λείπει
#XMSG
validateNoPKInLTFTargetDescription=Αρχικός κωδικός δεν καθορίστηκε στον στόχο, κάτι που δεν υποστηρίζεται σε αυτό το πλαίσιο.
#XMSG
validateABAPClusterTableLTF=Πίνακας Συστοιχίας ΑΒΑΡ.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Το αντικείμενο πηγής είναι ένας πίνακας συστοιχίας ΑΒΑΡ, αυτό δεν υποστηρίζεται στο συγκεκριμένο πλαίσιο.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Μάλλον δεν έχετε προσθέσει ακόμα δεδομένα.
#YINS
welcomeText2=Για να ξεκινήσετε την ροή αντιγραφής, επιλέξτε μία σύνδεση και ένα αντικείμενο πηγής στα αριστερά.

#XBUT
wizStep1=Επιλογή Σύνδεσης Πηγής
#XBUT
wizStep2=Επιλογή Αποθηκευτικής Μονάδας Πηγής
#XBUT
wizStep3=Προσθήκη Αντικειμένων Πηγής

#XMSG
limitDataset=Ο μέγσιτος αριθμός αντικειμένων καλύφθηκε. Διαγράψτε υπάρχοντα αντικείμενα για να προσθέσετε νέα ή δημιουργήστε μία νέα ροή αντιγραφής.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Η ροή αντιγραφής σε αυτή την μη SAP σύνδεση στόχου δεν μπορεί να ξεκινήσει γιατί δεν υπάρχει διαθέσιμος εξωτερικός όγκος για αυτό τον μήνα.
#XMSG
premiumOutBoundRFAdminWarningMsg=Ενας διαχειριστής μπορεί να αυξήσει τις ομάδες Premium Outbound για αυτό τον μισθωτή, καθιστώντας διαθέσιμο τον εξωτερικό όγκο για αυτό τον μήνα.
#XMSG
messageForToastForDPIDColumn2=Νέα στήλη προστέθηκε στον στόχο για {0} αντικείμενα - που απαιτούνται για τη διαχείριση διπλοεγγραφών σε συνδυασμό με τα αρχικά αντικείμενα βάσει ABAP που δεν έχουν αρχικό κωδικό.
#XMSG
PremiumInboundWarningMessage=Ανάλογα με τον αριθμό ροών αντιγραφής και τον όγκο δεδομένων προς αντιγραφή, οι πόροι SAP HANA{0}που απαιτούνται για την αντιγραφή δεδομένων μέσω {1} μπορεί να υπερβαίνουν την διαθέσιμη χωρητικότητα του μισθωτή σας.
#XMSG
PremiumInboundWarningMsg=Ανάλογα με τον αριθμό ροών αντιγραφής και τον όγκο δεδομένων προς αντιγραφή,{0}οι πόροι SAP HANA που απαιτούνται για την αντιγραφή δεδομένων μέσω "{1}" ενδέχεται να υπερβαίνουν τη διαθέσιμη δυναμικότητα του μισθωτή.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Εισαγωγή ονόματος προβολής.
#XMSG
emptyTargetColumn=Πρέπει να εισάγετε ένα όνομα στήλης.
#XMSG
emptyTargetColumnBusinessName=Εισάγετε τελική στήλη Επιχειρηματικό Ονομα.
#XMSG
invalidTransformName=Εισαγωγή ονόματος προβολής.
#XMSG
uniqueColumnName=Μετονομασία τελικής στήλης.
#XMSG
copySourceColumnLbl=Αντιγραφή στηλών από αντικείμενο πηγής
#XMSG
renameWarning=Βεβαιωθείτε ότι επιλέξατε ένα μοναδικό όνομα κατά τη μετονομασία του πίνακα στόχου. Αν ο πίνακας με το νέο όνομα υπάρχει ήδη στον χώρο, θα χρησιμοποιήσει τον ορισμό του εν λόγω πίνακα.

#XMSG
uniqueColumnBusinessName=Μετονομασία επωνυμίας εταιρίας τελικής στήλης.
#XMSG
uniqueSourceMapping=Επιλέξτε μία διαφορετική στήλη πηγής.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=Η στήλη πηγής {0} χρησιμοποιείται ήδη από τις παρακάτω τελικές στήλες:{1}{1}{2}{1}{1} Για αυτή την τελική στήλη ή για τις άλλες, επιλέξτε μία στήλη πηγής που δεν χρησιμοποιείται για αποθήκευση της προβολής.
#XMSG
uniqueColumnNameDescription=Το όνομα τελικής στήλης που καταχωρίσατε υπάρχει ήδη. Για να αποθηκεύετε την προβολή πρέπει να καθορίσετε ένα μοναδικό όνομα στήλης.
#XMSG
uniqueColumnBusinessNameDesc=Η επωνυμία εταιρίας τελικής στήλης που καταχωρίσατε υπάρχει ήδη. Για να αποθηκεύετε την προβολή πρέπει να καθορίσετε μία μοναδική επωνυμία εταιρίας στήλης.
#XMSG
emptySource=Πρέπει να επιλέξετε μία αρχική στήλη ή να εισάγετε μία σταθερά.
#XMSG
emptySourceDescription=Για να δημιουργήσετε μία έγκυρη καταχώριση αντιστοίχισης πρέπει να επιλέξετε μία αρχική στήλη ή να εισάγετε μία σταθερή τιμή.
#XMSG
emptyExpression=Καθορισμός αντιστοίχισης.
#XMSG
emptyExpressionDescription1=Επιλέξτε την αρχική στήλη στην οποία θέλετε να αντιστοιχίσετε την τελική στήλη ή επιλέξτε το πλαίσιο ελέγχου στη στήλη {0} Λειτουργίες / Σταθερές {1}. {2} {2} Οι λειτουργίες καταχωρίστηκαν αυτόματα σύμφωνα με τον τύπο τελικών δεδομένων. Οι σταθερές τιμές μπορούν να καταχωριστούν μη αυτόματα.
#XMSG
numberExpressionErr=Εισάγετε αριθμό.
#XMSG
numberExpressionErrDescription=Επιλέξατε τύπο αριθμητικών δεδομένων. Αυτό σημαίνει ότι μπορείτε να εισάγετε μόνο αριθμούς εδώ συν την υποδιαστολή αν υπάρχει. Μην χρησιμοποιήσετε μονά εισαγωγικά.
#XMSG
invalidLength=Εισάγετε μία έγκυρη τιμή μήκους.
#XMSG
invalidLengthDescription=Το μήκος τύπου δεδομένων πρέπει να είναι ίδιο ή μεγαλύτερο από το μήκος της αρχικής στήλης και ανάμεσα στο 1 και το 5000.
#XMSG
invalidMappedLength=Εισάγετε μία έγκυρη τιμή μήκους.
#XMSG
invalidMappedLengthDescription=Το μήκος τύπου δεδομένων πρέπει να είναι ίσο ή μεγαλύτερο από το μήκος της αρχικής στήλης {0} και ανάμεσα στο 1 και το 5000.
#XMSG
invalidPrecision=Εισάγετε μία έγκυρη τιμή ακρίβειας.
#XMSG
invalidPrecisionDescription=Η ακρίβεια καθορίζει τον συνολικό αριθμό ψηφίων. Η κλίμακα καθορίζει τον αριθμό ψηφίων μετά την υποδιαστολή και μπορεί να είναι από 0 και ακρίβεια.{0}{0} Παραδείγματα: {0}{1} Ακρίβεια 6, η κλίμακα 2 αντιστοιχεί σε αριθμούς όπως 1234.56.{0}{1} Η recision 6, κλίμακα 6 αντιστοιχεί σε αριθμούς όπως 0.123546.{0} {0} Η ακρίβεια και η κλίμακα για τον στόχο πρέπει να είναι συμβατή με ακρίβεια και κλίμακα για την πηγή έτσι ώστε όλα τα ψηφία από την πηγή να ταιριάζουν με το πεδίο στόχου. Για παράδειγμα, αν έχετε ακρίβεια 6 και κλίμακα 2 στην πηγή (και συνεπώς ψηφία διαφορετικά από 0 πριν από την υποδιαστολή), δεν μπορείτε να έχετε ακρίβεια 6 και κλίμακα 6 στον στόχο.
#XMSG
invalidPrimaryKey=Εισάγετε τουλάχιστον έναν αρχικό κωδικό.
#XMSG
invalidPrimaryKeyDescription=Ο αρχικός κωδικός δεν καθορίστηκε για αυτό το σχήμα.
#XMSG
invalidMappedPrecision=Εισάγετε μία έγκυρη τιμή ακρίβειας.
#XMSG
invalidMappedPrecisionDescription1=Η ακρίβεια καθορίζει τον συνολικό αριθμό ψηφίων. Η κλίμακα καθορίζει τον αριθμό ψηφίων μετά την υποδιαστολή και μπορεί να είναι μεταξύ 0 και ακρίβεια.{0}{0} Παραδείγματα:{0}{1} Η ακρίβεια 6, κλίμακα 2 αντιστοιχεί σε αριθμούς όπως 1234.56.{0}{1} Η ακρίβεια 6, κλίμακα 6 αντιστοιχεί σε αριθμούς όπως 0.123546.{0}{0}Η ακρίβεια του τύπου δεδομένων πρέπει να ισούται ή να είναι μεγαλύτερη από την ακρίβεια της πηγής ({2}).
#XMSG
invalidScale=Εισάγετε μία έγκυρη τιμή κλίμακας.
#XMSG
invalidScaleDescription=Η ακρίβεια καθορίζει τον συνολικό αριθμό ψηφίων. Η κλίμακα καθορίζει τον αριθμό ψηφίων μετά την υποδιαστολή και μπορεί να είναι από 0 και ακρίβεια.{0}{0} Παραδείγματα: {0}{1} Ακρίβεια 6, η κλίμακα 2 αντιστοιχεί σε αριθμούς όπως 1234.56.{0}{1} Η recision 6, κλίμακα 6 αντιστοιχεί σε αριθμούς όπως 0.123546.{0} {0} Η ακρίβεια και η κλίμακα για τον στόχο πρέπει να είναι συμβατή με ακρίβεια και κλίμακα για την πηγή έτσι ώστε όλα τα ψηφία από την πηγή να ταιριάζουν με το πεδίο στόχου. Για παράδειγμα, αν έχετε ακρίβεια 6 και κλίμακα 2 στην πηγή (και συνεπώς ψηφία διαφορετικά από 0 πριν από την υποδιαστολή), δεν μπορείτε να έχετε ακρίβεια 6 και κλίμακα 6 στον στόχο.
#XMSG
invalidMappedScale=Εισάγετε μία έγκυρη τιμή κλίμακας.
#XMSG
invalidMappedScaleDescription1=Η ακρίβεια καθορίζει τον συνολικό αριθμό ψηφίων. Η κλίμακα καθορίζει τον αριθμό ψηφίων μετά την υποδιαστολή και μπορεί να είναι μεταξύ 0 και ακρίβεια.{0}{0} Παραδείγματα:{0}{1} Η ακρίβεια 6, κλίμακα 2 αντιστοιχεί σε αριθμούς όπως 1234.56.{0}{1} Η ακρίβεια 6, κλίμακα 6 αντιστοιχεί σε αριθμούς όπως 0.123546.{0}{0}Η κλίμακα του τύπου δεδομένων πρέπει να ισούται ή να είναι μεγαλύτερη από την ακρίβεια της πηγής ({2}).
#XMSG
nonCompatibleDataType=Επιλέξτε ένα συμβατό τύπο δεδομένων.
#XMSG
nonCompatibleDataTypeDescription1=Ο τύπος δεδομένων που καθορίσατε εδώ πρέπει να είναι συμβατός με τον αρχικό τύπο δεδομένων ({0}). {1}{1} Παράδειγμα: αν η αρχική στήλη σας έχει τύπο δεδομένων αλφαριθμητικό και περιέχει γράμματα δεν μπορείτε να χρησιμοποιήσετε τύπο δεκαδικών στο στόχο σας.
#XMSG
invalidColumnCount=Πρέπει να επιλέξετε μία αρχική στήλη.
#XMSG
ObjectStoreInvalidScaleORPrecision=Εισάγετε μία έγκυρη τιμή με ακρίβεια  και κλίμακα.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=Η πρώτη τιμή είναι η ακρίβεια, που καθορίζει τον συνολικό αριθμό των ψηφίων. Η δεύτερη τιμή είναι η κλίμακα που καθορίζει τα ψηφία μετά την υποδιαστολή. Εισάγετε μία τιμή κλίμακας στόχου μεγαλύτερη από την τιμή κλίμακας πηγής και βεβαιωθείτε ότι η διαφορά μεταξύ της καταχωρισμένης τιμής στόχου και της τιμής ακρίβειας είναι μεγαλύτερη από την διαφορά μεταξύ της κλίμακας πηγής και τιμής ακρίβειας.
#XMSG
InvalidPrecisionORScale=Εισάγετε μία έγκυρη τιμή με ακρίβεια  και κλίμακα.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=Η πρώτη τιμή είναι η ακρίβεια που καθορίζει το σύνολο των ψηφίων. Η δεύτερη τιμή είναι η κλίμακα, που καθορίζει τα ψηφία μετά την υποδιαστολή.{0}{0}Εφόσον ο τύπος δεδομένων πηγής δεν υποστηρίζεται στο Google BigQuery, μετατρέπεται στον τελικό τύπο δεδομένων DECIMAL. Σε αυτή την περίπτωση, η ακρίβεια καθορίζεται μόνο μεταξύ 38 και 76, και η κλίμακα από 9 ως 38. Επίσης, το αποτέλεσμα της ακρίβειας μείον την κλίμακα, που αντιπροσωπεύει τα ψηφία πριν την υποδιαστολή πρέπει να είναι από 20 ως 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=Η πρώτη τιμή είναι η ακρίβεια που καθορίζει το σύνολο των ψηφίων. Η δεύτερη τιμή είναι η κλίμακα, που καθορίζει τα ψηφία μετά την υποδιαστολή.{0}{0}Εφόσον ο τύπος δεδομένων πηγής δεν υποστηρίζεται στο Google BigQuery, μετατρέπεται στον τελικό τύπο δεδομένων DECIMAL. Σε αυτή την περίπτωση, η ακρίβεια καθορίζεται μόνο από 20 και πάνω. Επίσης, το αποτέλεσμα της ακρίβειας μείον την κλίμακα, που αντιπροσωπεύει τα ψηφία πριν την υποδιαστολή πρέπει να είναι από 20 και πάνω.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=Η πρώτη τιμή είναι η ακρίβεια που καθορίζει τον τελικό αριθμό ψηφίων. Η δεύτερη τιμή είναι η κλίμακα, που καθορίζει τα ψηφία μετά την υποδιαστολή.{0}{0}Αφού τύπος δεδομένων πηγής δεν υποστηρίζεται στον στόχο, μετατρέπεται στον τύπο δεδομένων στόχου ΔΕΚΑΔΙΚΟ. Σε αυτή την περίπτωση, η ακρίβεια πρέπει να καθοριστεί από οποιονδήποτε αριθμό που είναι 1 ή μεγαλύτερος από 1 και μικρότερος από 38, και η κλίμακα μικρότερη ή ίση με την ακρίβεια.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=Η πρώτη τιμή είναι η ακρίβεια που καθορίζει τον τελικό αριθμό ψηφίων. Η δεύτερη τιμή είναι η κλίμακα, που καθορίζει τα ψηφία μετά την υποδιαστολή.{0}{0}Αφού τύπος δεδομένων πηγής δεν υποστηρίζεται στον στόχο, μετατρέπεται στον τύπο δεδομένων στόχου ΔΕΚΑΔΙΚΟ. Σε αυτή την περίπτωση, η ακρίβεια πρέπει να καθοριστεί ως 20 ή πάνω από 20. Επίσης, το αποτέλεσμα της ακρίβειας μείον την κλίμακα που εκφράζει τα ψηφία πριν από την υποδιαστολή πρέπει να είναι 20 ή πάνω παό 20.
#XMSG
invalidColumnCountDescription=Για να δημιουργήσετε μία έγκυρη καταχώριση αντιστοίχισης πρέπει να επιλέξετε μία αρχική στήλη ή να εισάγετε μία σταθερή τιμή.
#XMSG
duplicateColumns=Μετονομασία τελικής στήλης.
#XMSG
duplicateGBQCDCColumnsDesc=Το όνομα της τελικής στήλης είναι δεσμευμένο στο Google BigQuery. Πρέπει να την μετονομάσετε για να την αποθηκεύσετε στην προβολή.
#XMSG
duplicateConfluentCDCColumnsDesc=Το όνομα στήλης στόχου είναι δεσμευμένο στο Confluent. Πρέπει να το μετονομάσετε για να αποθηκεύσετε την προβολή.
#XMSG
duplicateSignavioCDCColumnsDesc=Το όνομα τελικής στήλης είναι δεσμευμένο στο SAP Signavio. Πρέπει να την μετονομάσετε ώστε να αποθηκευτεί η προβολή.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Το όνομα στήλης στόχου είναι δεσμευμένο στο MS OneLake. Πρέπει να το μετονομάσετε για να αποθηκεύσετε την προβολή.
#XMSG
duplicateSFTPCDCColumnsDesc=Το όνομα στήλης στόχου είναι δεσμευμένο στο SFTP. Πρέπει να το μετονομάσετε για να αποθηκεύσετε την προβολή.
#XMSG
GBQTargetNameWithPrefixUpdated1=Το όνομα στήλης στόχου περιέχει ένα πρόθεμα που είναι δεσμευμένο στο Google BigQuery. Θα πρέπει να την μετονομάσετε για να αποθηκεύσετε την προβολή. {0}{0}Το όνομα της στήλης στόχου δεν πρέπει να ξεκινά με κάποιο από ατα παρακάτω αλφαριθμητικά:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Συντομευμένο όνομα τελικής στήλης.
#XMSG
GBQtargetMaxLengthDesc=Στο Google BigQuery, ένα όνομα στήλης μπορεί να χρησιμοποιήσει μέχρι 300 χαρακτήρες. Μικρύνετε το όνομα της τελικής στήλης για να αποθηκεύσετε την προβολή.
#XMSG
invalidMappedScalePrecision=Η ακρίβεια και η κλίμακα για το στόχο πρέπει να είναι συμβατή με ακρίβεια και κλίμακα για την πηγή έτσι ώστε όλα τα ψηφία από την πηγή να ταιριάζουν με το τελικό πεδίο.
#XMSG
invalidMappedScalePrecisionShortText=Εισάγετε μία έγκυρη τιμή για ακρίβεια και κλίμακα.
#XMSG
validationIncompatiblePKTypeDescProjection3=Μία ή περισσότερες στήλες έχουν τύπους δεδομένων που δεν μπορούν να καθοριστούν ως αρχικοί κωδικοί στο Google BigQuery. Δεν θα δημιουργηθούν αρχικοί κωδικοί στο τελικό αντικείμενο.{0}{0}Οι παρακάτω τελικοί τύποι δεδομένων είναι συμβατοί με τύπους δεδομένων Google BigQuery για τους οποίους μπορεί να καθοριστεί αρχικός κωδικός :{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Αποεπιλέξτε column __message_id.
#XMSG
uncheckColumnMessageIdDesc=Στήλη: Αρχικός Κωδικός
#XMSG
validationOpCodeInsert=Καταχωρίστε μία τιμή για Εισαγωγή.
#XMSG
recommendDifferentPrimaryKey=Σας προτείνουμε να επιλέξετε έναν διαφορετικό αρχικό κωδικό σε επίπεδο στοιχείου.
#XMSG
recommendDifferentPrimaryKeyDesc=Όταν ο κωδικός λειτουργίας είναι ήδη καθορισμένος, προτείνουμε να επιλέξετε τους διαφορετικούς αρχικούς κωδικούς για το ευρετήριο δίσκου και τα στοιχεία, προς αποφυγή προβλημάτων όπως αντιγραφή στήλης, για παράδειγμα.
#XMSG
selectPrimaryKeyItemLevel=Πρέπει να επιλέξετε τουλάχιστον έναν αρχικό κωδικό για επίπεδο κεφαλίδας και στοιχείου.
#XMSG
selectPrimaryKeyItemLevelDesc=Όταν ένας δίσκος ή χάρτης έχει επεκταθεί, πρέπει να επιλέξετε δύο αρχικούς κωδικούς, έναν σε επίπεδο κεφαλίδας και έναν σε επίπεδο στοιχείου.
#XMSG
invalidMapKey=Πρέπει να επιλέξετε τουλάχιστον έναν αρχικό κωδικό για επίπεδο κεφαλίδας.
#XMSG
invalidMapKeyDesc=Όταν ένας δίσκος ή χάρτης έχει επεκταθεί, πρέπει να επιλέξετε έναν αρχικό κωδικό σε επίπεδο κεφαλίδας.
#XFLD
txtSearchFields=Αναζήτηση Στηλών Στόχου
#XFLD
txtName=Ονομα
#XMSG
txtSourceColValidation=Μία ή περισσότερες στήλες πηγής δεν υποστηρίζονται:
#XMSG
txtMappingCount=Αντιστοιχίσεις ({0})
#XMSG
schema=Σχήμα
#XMSG
sourceColumn=Στήλες Πηγής
#XMSG
warningSourceSchema=Οι αλλαγές στο σχήμα θα επηρεάσουν αντιστοιχίσεις στον διάλογο προβολής.
#XCOL
txtTargetColName=Τελική Στήλη (Τεχνικό Ονομα)
#XCOL
txtDataType=Τύπος Δεδομένων Στόχου
#XCOL
txtSourceDataType=Τύπος Δεδομένων Πηγής
#XCOL
srcColName=Αρχική Στήλη (Τεχνικό Ονομα)
#XCOL
precision=Ακρίβεια
#XCOL
scale=Κλίμακα
#XCOL
functionsOrConstants=Λειτουργίες / Σταθερές
#XCOL
txtTargetColBusinessName=Τελική Στήλη (Επιχειρηματικό Ονομα)
#XCOL
prKey=Αρχικός Κωδικός
#XCOL
txtProperties=Ιδιότητες
#XBUT
txtOK=Αποθήκευση
#XBUT
txtCancel=Ακύρωση
#XBUT
txtRemove=Διαγραφή
#XFLD
txtDesc=Περιγραφή
#XMSG
rftdMapping=Απεικόνιση
#XFLD
@lblColumnDataType=Τύπος Δεδομένων
#XFLD
@lblColumnTechnicalName=Τεχνικό Ονομα
#XBUT
txtAutomap=Αυτόματη Αντιστοίχιση
#XBUT
txtUp=Πάνω
#XBUT
txtDown=Κάτω

#XTOL
txtTransformationHeader=Προβολή
#XTOL
editTransformation=Επεξεργασία
#XTOL
primaryKeyToolip=Κλειδί


#XMSG
rftdFilter=Φίλτρο
#XMSG
rftdFilterColumnCount=Πηγή: {0}({1})
#XTOL
rftdFilterColSearch=Αναζήτηση
#XMSG
rftdFilterColNoData=Καμία στήλη για εμφάνιση
#XMSG
rftdFilteredColNoExps=Δεν υπάρχουν εκφράσεις φίλτρου
#XMSG
rftdFilterSelectedColTxt=Προσθήκη Φίλτρου για
#XMSG
rftdFilterTxt=Φίλτρο διαθέσιμο για
#XBUT
rftdFilterSelectedAddColExp=Προσθήκη Εκφρασης
#YINS
rftdFilterNoSelectedCol=Επιλογή στήλης για προσθήκη φίλτρου.
#XMSG
rftdFilterExp=Φίλτρο Εκφρασης
#XMSG
rftdFilterNotAllowedColumn=Προσθήκη φίλτρων δεν υποστηρίζεται για αυτή τη στήλη.
#XMSG
rftdFilterNotAllowedHead=Μη Υποστηριζόμενη Στήλη
#XMSG
rftdFilterNoExp=Δεν καθορίστηκε φίλτρο
#XTOL
rftdfilteredTt=Φιλτραρίστηκε
#XTOL
rftdremoveexpTt=Διαγραφή έκφρασης φίλτρου
#XTOL
validationMessageTt=Μηνύματα Επικύρωσης
#XTOL
rftdFilterDateInp=Επιλογή ημερομηνίας
#XTOL
rftdFilterDateTimeInp=Επιλογή ημερομηνίας ώρας
#XTOL
rftdFilterTimeInp=Επιλέξτε μία ώρα
#XTOL
rftdFilterInp=Καταχώριση τιμής
#XMSG
rftdFilterValidateEmptyMsg={0} εκφράσεις φίλτρου σε {1} στήλη είναι κενές
#XMSG
rftdFilterValidateInvalidNumericMsg={0} εκφράσεις φίλτρου σε {1} στήλη περιέχουν άκυρες αριθημτικέ τιμές
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Η έκφραση φίλτρου πρέπει να περιέχει έγκυρες αριθμητικές τιμές
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Αν το σχήμα αντικειμένου στόχου άλλαξε, χρησιμοποιήστε την λειτουργία "Αντιστοίχιση σε Υπάρχον Αντικείμενο Στόχου" στην κύρια σελίδα για την προσαρμογή των αλλαγών και την επαναντιστοίχιση του αντικειμένου στόχου με την πηγή του ξανά.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Αν ο πίνακας στόχος υπάρχει ήδη και η αντιστοίχιση περιλαμβάνει μία αλλαγή σχήματος, θα πρέπει να τον αλλάξετε πριν αναπτύξετε την ροή αντιγραφής.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Αν η αντιστοίχιση περιλαμβάνει μία αλλαγή σχήματος, θα πρέπει να αλλάξετε τον πίνακα στόχο πριν αναπτύξετε την ροή αντιγραφής.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Οι παρακάτω μη υποστηριζόμενες στήλες παραβλέφθηκαν από τον ορισμό πηγής: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Οι παρακάτω μη υποστηριζόμενες στήλες παραβλέφθηκαν από τον ορισμό στόχου: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Τα παρακάτω αντικείμενα δεν υποστηρίζονται γιατί είναι εκτεθειμένα για ανάλωση: {0} {1} {0} {0} Για να χρησιμοποιήσετε πίνακες σε μία ροή αντιγραφής, η σημασιολογική χρήση  (στις ρυθμίσεις πίνακα) δεν πρέπει να καθοριστεί ΄στην {2}Αναλυτική Ομάδα Δεδομένων{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Το αντικείμενο στόχου δεν μπορεί να χρησιμοποιηθεί γιατί είναι εκτεθειμένο για ανάλωση. {0} {0}  Για να χρησιμοποιήσετε τον πίνακα σε μία ροή αντιγραφής, η σημασιολογική χρήση (στις ρυθμίσεις πίνακα) δεν πρέπει να καθοριστεί σε {1}Αναλυτική Ομάδα Δεδομένων{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Ενα αντικείμενο στόχου με αυτό το όνομα υπάρχει ήδη. Ωστόσο, δεν μπορεί να χρησιμοποιηθεί γιατί είναι εκτεθειμένο για ανάλωση. {0} {0} Για να χρησιμοποιήσετε τον πίνακα σε μία ροή αντιγραφής, η σημασιολογική χρήση (στις ρυθμίσεις πίνακα) δεν πρέπει  να καθοριστεί σε {1}Αναλυτική Ομάδα Δεδομένων{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Ενα αντικείμενο στόχου με αυτό το όνομα υπάρχει ήδη. {0}Ωστόσο, δεν μπορεί να χρησιμοποιηθεί ως αντικείμενο στόχου για μία ροή αντιγραφής στην τοπική αποθήκη καθώς δεν είναι τοπικός πίνακας.
#XMSG:
targetAutoRenameUpdated=Η τελική στήλη μετονομάστηκε.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Η τελική στήλη μετονομάστηκε ώστε να επιτρέπονται οι αντιγραφές στο Google BigQuery. Αυτό οφείλεται σε έναν από τους παρακάτω λόγους:{0} {1}{2}Δεσμευμένο όνομα στήλης{3}{2}Μη υποστηριζόμενοι χαρακτήρες{3}{2}Δεσμευμένο πρόθεμα{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Η τελική στήλη μετονομάστηκε ώστε να επιτρέπονται αντιγραφές στο Confluent. Αυτό οφείλεται στους παρακάτω λόγους:{0} {1}{2}Δεσμευμένο όνομα στήλης{3}{2}Μη υποστηριζόμενοι χαρακτήρες{3}{2}Δεσμευμένο πρόθεμα{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Η στήλη στόχου μετονομάστηκε ώστε να επιτρέπει τις αντιγραφές στον στόχο. Αυτό συμβαίνει λόγω των παρακάτω:{0} {1}{2}Μη υποστηριζόμενοι χαρακτήρες{3}{2}Δεσμευμένο πρόθεμα{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Η τελική στήλη μετονομάστηκε ώστε να επιτρέπονται αντιγραφές στο στόχο. Αυτό οφείλεται στα εξής:{0} {1}{2}Δεσμευμένο όνομα στήλης{3}{2}Μη υποστηριζόμενοι χαρακτήρες{3}{2}Δεσμευμένο πρόθεμα{3}{4}
#XMSG:
targetAutoDataType=Ο τύπος δεδομένων στόχου άλλαξε.
#XMSG:
targetAutoDataTypeDesc=Ο τύπος δεδομένων στόχου άλλαξε σε {0} γιατί δεν υποστηρίζεται στο Google BigQuery.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Ο τύπος δεδομένων στόχου άλλαξε σε {0} γιατί ο τύπος δεδομένων πηγής δεν υποστηρίζεται στην σύνδεση στόχου.
#XMSG
projectionGBQUnableToCreateKey=Αδύνατη δημιουργία αρχικών κωδικών.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=Στο Google BigQuery, ένας μέγιστος αριθμός 16 αρχικών κωδικών υποστηρίζεται, αλλά το αντικείμενο πηγής έχει έναν μεγαλύτερο αριθμό αρχικών κωδικών. Δεν θα δημιουργηθούν αρχικοί κωδικοί στο αντικείμενο στόχου.
#XMSG
HDLFNoKeyError=Καθορίστε μία ή περισσότερες στήλες ως αρχικό κωδικό.
#XMSG
HDLFNoKeyErrorDescription=Για να αντιγράψετε ένα αντικείμενο, πρέπει να καθορίσετε μία ή περισσότερες στήλες ως αρχικό κωδικό.
#XMSG
HDLFNoKeyErrorExistingTarget=Καθορίστε μία ή περισσότερες στήλες ως αρχικό κωδικό.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Για να αντιγράψετε δεδομένα σε υπάρχον αντικείμενο στόχου, πρέπει να έχουν μία ή περισσότερες στήλες καθορισμένες ως αρχικό κωδικό. {0} {0} Εχετε τις παρακάτω επιλογές για καθορισμό μία ή περισσοτέρων στηλών ως αρχικό κωδικό: {0}{0}{1} Χρησιμοποιήστε τοπικό επεξεργαστή πίνακα για να αλλάξετε το αντικείμενο στόχου. Επειτα επαναφορτώστε τη ροή αντιγραφής.{0}{0}{1} Μετονομάστε το τελικό αντικείμενο στη ροή αντιγραφής. Αυτό θα δημιουργήσει ένα νέο αντικείμενο μόλις ξεκινήσει η εκτέλεση. Μετά τη μετονομασία, μπορείτε να καθορίσετε μία ή περισσότερες στήλες ως αρχικό κωδικό σε μία προβολή.{0}{0}{1} Αντιστοιχίστε το αντικείμενο σε άλλο υπάρχον τελικό αντικείμενο όπου ορίστηκαν μία ή περισσότερες στήλες ως αρχικός κωδικός.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Αρχικός κωδικός άλλαξε.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=Συγκριτικά με το αντικείμενο πηγής, καθορίσατε διαφορετικές στήλες ως αρχικό κωδικό για το αντικείμενο στόχου. Βεβαιωθείτε ότι αυτές οι στήλες αναγνωρίζουν μοναδικά όλες τις σειρές για αποφυγή απώλειας δεδομένων κατά την αντιγραφή δεδομένων αργότερα. {0} {0}Στο αντικείμενο πηγής καθορίστηκαν οι ακόλουθες στήλες ως αρχικός κωδικός: {0} {1}
#XMSG
duplicateDPIDColumns=Μετονομασία τελικής στήλης.
#XMSG
duplicateDPIDDColumnsDesc1=Αυτό το όνομα τελικής στήλης είναι δεσμευμένο για μία τεχνική στήλη. Εισάγετε διαφορετικό όνομα για αποθήκευση προβολής.
#XMSG:
targetAutoRenameDPID=Η τελική στήλη μετονομάστηκε.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Η τελική στήλη μετονομάστηκε για να επιτρέπονται αντιγραφές από την πηγή ABAP χωρίς κωδικούς. Αυτό οφείλεται σε έναν από τους εξής λόγους:{0} {1}{2}Ονομα δεσμευμένης στήλης{3}{2}Μη υποστηριζόμενοι χαρακτήρες{3}{2}Δεσμευμένο πρόθεμα{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle={0} Ρυθμίσεις Στόχου
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle={0} Ρυθμίσεις Πηγής
#XBUT
connectionSettingSave=Αποθήκευση
#XBUT
connectionSettingCancel=Ακύρωση
#XBUT: Button to keep the object level settings
txtKeep=Διατήρηση
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Αντικατάσταση
#XFLD
targetConnectionThreadlimit=Όριο Νήματος Στόχου για Εσωτερική Φόρτωση (1-100)
#XFLD
connectionThreadLimit=Όριο Νημάτων Πηγής για Αρχική Φόρτωση (1-100)
#XFLD
maxConnection=Όριο Νήματος Αντιγραφής (1-100)
#XFLD
kafkaNumberOfPartitions=Αριθμός κατανομών
#XFLD
kafkaReplicationFactor=Παράγοντας Αντιγραφής
#XFLD
kafkaMessageEncoder=Κωδικοποιητής Μηνυμάτων
#XFLD
kafkaMessageCompression=Συμπίεση Μηνύματος
#XFLD
fileGroupDeltaFilesBy=Ομαδοποίηση Delta βάσει
#XFLD
fileFormat=Τύπος Αρχείου
#XFLD
csvEncoding=Κωδικοποίηση CSV 
#XFLD
abapExitLbl=ABAP Exit
#XFLD
deltaPartition=Αριθμός Νημάτων Αντικειμένων για Φορτώσεις Delta (1-10)
#XFLD
clamping_Data=Αδύνατη Αποκοπή Δεδομένων
#XFLD
fail_On_Incompatible=Αποτυχία για Ασύμβατα Δεδομένα
#XFLD
maxPartitionInput=Μέγιστος Αριθμός Κατανομών
#XFLD
max_Partition=Καθορισμός Μέγιστου Αριθμού Κατανομών
#XFLD
include_SubFolder=Συμπερίληψη Υποφακέλων
#XFLD
fileGlobalPattern=Παγκόσμιο Πρότυπο για Ονομα Αρχείου
#XFLD
fileCompression=Συμπίεση Φίλτρου
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Οριοθέτης Αρχείου
#XFLD
fileIsHeaderIncluded=Κεφαλίδα Αρχείου
#XFLD
fileOrient=Προσανατολισμός
#XFLD
gbqWriteMode=Λειτουργία Εγγραφής
#XFLD
suppressDuplicate=Κατάργηση Αντιγράφων
#XFLD
apacheSpark=Ενεργοποίηση Συμβατότητας Apache Spark
#XFLD
clampingDatatypeCb=Δέσμευση Τύπων Δεδομένων Κινητής Υποδιαστολής
#XFLD
overwriteDatasetSetting=Αντικατάσταση Ρυθμίσεων Στόχου σε Επίπεδο Αντικειμένου
#XFLD
overwriteSourceDatasetSetting=Αντικατάσταση Ρυθμίσεων Πηγής σε Επίπεδο Αντικειμένου
#XMSG
kafkaInvalidConnectionSetting=Εισαγωγή ενός αριθμού μεταξύ {0} και {1}
#XMSG
MinReplicationThreadErrorMsg=Εισάγετε αριθμό μεγαλύτερο από {0}.
#XMSG
MaxReplicationThreadErrorMsg=Εισάγετε αριθμό μικρότερο από {0}.
#XMSG
DeltaThreadErrorMsg=Εισάγετε μία τιμή από 1 έως 10.
#XMSG
MaxPartitionErrorMsg=Εισάγετε τιμή μεταξύ 1 <= x <= 2147483647.Η προεπιλογή είναι 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Εισάγετε έναν ακέραιο από {0} ως {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Χρήση Παράγοντα Αντιγραφής του Χρηματιστή
#XFLD
serializationFormat=Μορφή Σειριοποίησης
#XFLD
compressionType=Τύπος Συμπίεσης
#XFLD
schemaRegistry=Χρήση Μητρώου Σχεδίου
#XFLD
subjectNameStrat=Στρατηγική Ονόματος Υποκειμένου
#XFLD
compatibilityType=Τύπος Συμβατότητας
#XFLD
confluentTopicName=Όνομα θέματος
#XFLD
confluentRecordName=Όνομα Εγγραφής
#XFLD
confluentSubjectNamePreview=Προεπισκόπηση Ονόματος Υποκειμένου
#XMSG
serializationChangeToastMsgUpdated2=Η μορφή σειριοποίησης άλλαξε σε JSON γιατί το μητρώο σχήματος δεν υποστηρίζεται. Για να αλλάξετε τη μορφή σειριοποίησης σε AVRO, ενεργοποιήστε πρώτα το μητρώο σχήματος.
#XBUT
confluentTopicNameInfo=Το όνομα θέματος βασίζεται πάντα στο όνομα αντικειμένου στόχου. Μπορείτε να το αλλάξετε μετονομάζοντας το αντικείμενο στόχου. 
#XMSG
emptyRecordNameValidationHeaderMsg=Εισάγετε το όνομα εγγραφής.
#XMSG
emptyPartionHeader=Εισάγετε έγκυρο αριθμό κατανομών.
#XMSG
invalidPartitionsHeader=Εισάγετε έγκυρο αριθμό κατανομών.
#XMSG
invalidpartitionsDesc=Εισάγετε αριθμό από 1 έως 200.000.
#XMSG
emptyrFactorHeader=Εισάγετε συντελεστή αντιγραφής.
#XMSG
invalidrFactorHeader=Εισάγετε έγκυρο συντελεστή αντιγραφής.
#XMSG
invalidrFactorDesc=Εισάγετε αριθμό από 1 έως 32.767.
#XMSG
emptyRecordNameValidationDescMsg=Αν η μορφή σειριοποίησης "AVRO" χρησιμοποιείται μόνο τα παρακάτω χαρακτηριστικά υποστηρίζονται:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(κάτω παύλα)
#XMSG
validRecordNameValidationHeaderMsg=Εισάγετε ένα έγκυρο όνομα εγγραφής.
#XMSG
validRecordNameValidationDescMsgUpdated=Καθώς χρησιμοποιείται η μορφή σειριοποίησης "AVRO", το όνομα εγγραφής πρέπει να περιέχει μόνο αλφαριθμητικούς χαρακτήρες (A-Z, a-z, 0-9) και κάτω παύλα (_). Πρέπει να αρχίζει με γράμμα ή κάτω παύλα.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=Το "Αριθμός Νημάτων Αντικειμένων για Φορτώσεις Delta" μπορεί να καθοριστεί εφόσον ένα ή περισσότερα αντικείμενα έχουν τύπο φόρτωσης "Αρχικό και Delta".
#XMSG
invalidTargetName=Άκυρο όνομα στήλης
#XMSG
invalidTargetNameDesc=Το όνομα της στήλης στόχου πρέπει να περιέχει μόνο αλφαριθμητικούς χαρακτήρες (A-Z, a-z, 0-9) και κάτω παύλα (_) .
#XFLD
consumeOtherSchema=Χρήση Αλλων Εκδόσεων Σχήματος
#XFLD
ignoreSchemamissmatch=Παράβλεψη Αναντιστοιχίας Σχημάτων
#XFLD
confleuntDatatruncation=Αδύνατη Αποκοπή Δεδομένων
#XFLD
isolationLevel=Επίπεδο Απομόνωσης
#XFLD
confluentOffset=Σημείο Εναρξης
#XFLD
signavioGroupDeltaFilesByText=Κανένα
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=Οχι
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=Οχι

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Προβολές
#XBUT
txtAdd=Προσθήκη
#XBUT
txtEdit=Επεξεργασία
#XMSG
transformationText=Προσθήκη προβολής για διαμόρφωση φίλτρου ή αντιστοίχισης.
#XMSG
primaryKeyRequiredText=Επιλέξτε έναν αρχικό κωδικό με Σχήμα Διαμόρφωσης.
#XFLD
lblSettings=Ρυθμίσεις
#XFLD
lblTargetSetting={0}: Ρυθμίσεις Στόχου
#XMSG
@csvRF=Επιλέξτε το αρχείο που περιέχει τον ορισμό σχήματος που θέλετε να εφαρμόσετε σε όλα τα αρχεία στον φάκελο.
#XFLD
lblSourceColumns=Στήλες Πηγής
#XFLD
lblJsonStructure=Δομή JSON 
#XFLD
lblSourceSetting={0}: Ρυθμίσεις Πηγής
#XFLD
lblSourceSchemaSetting={0}: Ρυθμίσεις Σχήματο Πηγής
#XBUT
messageSettings=Ρυθμίσεις Μηνυμάτων
#XFLD
lblPropertyTitle1=Ιδιότητες Αντικειμένου
#XFLD
lblRFPropertyTitle=Ιδιότητες Ροής Αντιγραφής
#XMSG
noDataTxt=Δεν υπάρχουν στήλες για εμφάνιση.
#XMSG
noTargetObjectText=Δεν υπάρχει αντικείμενο στόχου επιλεγμένο.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Στήλες Στόχου
#XMSG
searchColumns=Αναζήτηση Στηλών
#XTOL
cdcColumnTooltip=Στήλη για Λήψη Delta
#XMSG
sourceNonDeltaSupportErrorUpdated=Το αντικείμενο πηγής δεν υποστηρίζει φόρτωση delta.
#XMSG
targetCDCColumnAdded=2 στήλες στόχου προστέθηκαν για λήψη delta.
#XMSG
deltaPartitionEnable=Το Όριο Νημάτων Αντικειμένων για Φορτώσεις Delta προστέθηκε στις ρυθμίσεις πηγής.
#XMSG
attributeMappingRemovalTxt=Η διαγραφή άκυρων αντιστοιχίσεων δεν υποστηρίζεται για νέο αντικείμενο στόχο.
#XMSG
targetCDCColumnRemoved=2 στήλες στόχου που χρησιμοποιήθηκαν για λήψη delta διαγράφηκαν.
#XMSG
replicationLoadTypeChanged=Τύπος φόρτωσης άλλαξε σε "Αρχικό και Delta".
#XMSG
sourceHDLFLoadTypeError=Αλλάξτε τον τύπο φόρτωσης σε "Αρχικό και Delta".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Για να αντιγράψετε ένα αντικείμενο από μία πηγαία σύνδεση με τύπο σύνδεσης με το Αρχεία SAP HANA Cloud, Data Lake στο SAP Datasphere, χρησιμοποιήστε τον τύπο φόρτωσης "αρχικό και delta".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Ενεργοποίηση Λήψης Delta.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Για να αντιγράψετε ένα αντικείμενο από μία πηγαία σύνδεση με τύπο σύνδεσης με το Αρχεία SAP HANA Cloud, Data Lake στο SAP Datasphere, ενεργοποιήστε την λήψη delta.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Αλλαγή τελικού Αντικειμένου.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Το αντικείμενο στόχου δεν μπορεί να χρησιμοποιηθεί γιατί είναι ανενεργή  η λήψη delta. Μετονομάστε το τελικό αντικείμενο (που επιτρέπει τη δημιουργία νέου αντικειμένου με λήψη delta) ή αντιστοιχίστε το με υπάρχον αντικείμενο με ενεργή λήψη delta.
#XMSG
deltaPartitionError=Εισάγετε έγκυρο αριθμό νημάτων αντικειμένων για φορτώσεις delta.
#XMSG
deltaPartitionErrorDescription=Εισάγετε μία τιμή από 1 έως 10.
#XMSG
deltaPartitionEmptyError=Εισάγετε αριθμό νημάτων αντικειμένων για φορτώσεις delta.
#XFLD
@lblColumnDescription=Περιγραφή
#XMSG
@lblColumnDescriptionText1=Για τεχνικούς σκοπούς η διαχείριση των διπλοεγγραφών που προκλήθηκε από προβλήματα κατά την αντιγραφή αντικειμένων πηγής βάσει ΑΒΑΡ που δεν έχυον αρχικό κωδικό.
#XFLD
storageType=Αποθήκευση
#XFLD
skipUnmappedColLbl=Παράβλεψη Μη Αντιστοιχισμένων Στηλών
#XFLD
abapContentTypeLbl=Τύπος περιεχομένου
#XFLD
autoMergeForTargetLbl=Συγχώνευση Δεδομένων Αυτόματα
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Γενικό
#XFLD
lblBusinessName=Επωνυμία Επιχείρησης
#XFLD
lblTechnicalName=Τεχνικό Ονομα
#XFLD
lblPackage=Πακέτο
#XFLD
statusPanel=Κατάσταση Εκτέλεσης
#XBTN: Schedule dropdown menu
SCHEDULE=Χρονοδ/μα
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Επεξεργασία  Προγράμματος
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Διαγραφή  Προγράμματος
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Δημιουργία  Προγράμματος
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Αδύνατος έλεγχος επαλήθευσης προγράμματος.
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Ενα πρόγραμμα δεν μπορεί να δημιουργηθεί γιατί η ροή αντιγραφής αναπτύσσεται ήδη.{0}Περιμένετε μέχρι να αναπτυχθεί η ροή αντιγραφής.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Για ροές αντιγραφής που περιέχουν αντικείμενα με τύπο φόρτωσης "Αρχικό και Delta", δεν μπορεί να αντιγραφεί το πρόγραμμα.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Για ροές αντιγραφής που περιέχουν αντικείμενα με τύπο φόρτωσης "Αρχικό και Delta", δεν μπορεί να αντιγραφεί το πρόγραμμα.
#XFLD : Scheduled popover
SCHEDULED=Προγρ/μένο
#XFLD
CREATE_REPLICATION_TEXT=Δημιουργία Ροής Αντιγραφής
#XFLD
EDIT_REPLICATION_TEXT=Επεξεργασία Ροής Αντιγραφής
#XFLD
DELETE_REPLICATION_TEXT=Διαγραφή Ροής Αντιγραφής
#XFLD
REFRESH_FREQUENCY=Συχνότητα
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Η ροή αντιγραφής δεν μπορεί να αναπτυχθεί γιατί το υπάρχον πρόγραμμα{0} δεν υποστηρίζει ακόμα τον τύπο φόρτωσης "Αρχικό και Delta".{0}{0}Για να αναπτύξετε τη ροή αντιγραφής, πρέπει να καθορίσετε τους τύπους φόρτωσης όλων των αντικειμένων{0} σε "Αρχικό μόνο". Διαφορετικά, μπορείτε να διαγράψετε το πρόγραμμα, να αναπτύξετε την ροή αντιγραφής {0}, κι έπειτα να ξεκινήσετε μία νέα εκτέλεση. Αυτό θα οδηγήσει σε εκτέλεση χωρίς {0}λήξη, που υποστηρίζει επίσης αντικείμενα με τύπο φόρτωσης "Αρχικό και Delta".
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Η ροή αντιγραφής δεν μπορεί να αναπτυχθεί γιατί το υπάρχον πρόγραμμα{0} δεν υποστηρίζει ακόμα τον τύπο φόρτωσης "Αρχικό και Delta".{0}{0}Για να αναπτύξετε τη ροή αντιγραφής, πρέπει να καθορίσετε τους τύπους φόρτωσης όλων των αντικειμένων{0} σε "Αρχικό μόνο". Διαφορετικά, μπορείτε να διαγράψετε το πρόγραμμα, να αναπτύξετε την ροή αντιγραφής {0}, κι έπειτα να ξεκινήσετε μία νέα εκτέλεση. Αυτό θα οδηγήσει σε εκτέλεση χωρίς {0}λήξη, που υποστηρίζει επίσης αντικείμενα με τύπο φόρτωσης "Αρχικό και Delta".
#XMSG
SCHEDULE_EXCEPTION=Αδύνατη λήψη λεπτομερειών προγράμματος
#XFLD: Label for frequency column
everyLabel=Κάθε
#XFLD: Plural Recurrence text for Hour
hoursLabel=Ωρες
#XFLD: Plural Recurrence text for Day
daysLabel=Ημέρες
#XFLD: Plural Recurrence text for Month
monthsLabel=Μήνες
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Λεπτά
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Αποτυχία λήψης πληροφοριών σχετικά με την πιθανότητα σχεδιασμού.
#XFLD :Paused field
PAUSED=Διακόπηκε
#XMSG
navToMonitoring=Ανοιγμα σε Οθόνη Ροών
#XFLD
statusLbl=Κατάσταση
#XFLD
lblLastRunExecuted=Έναρξη Τελευταίας Εκτέλεσης
#XFLD
lblLastExecuted=Τελευταία Εκτέλεση
#XFLD: Status text for Completed
statusCompleted=Ολοκληρωμένο
#XFLD: Status text for Running
statusRunning=Εκτελείται
#XFLD: Status text for Failed
statusFailed=Απέτυχε
#XFLD: Status text for Stopped
statusStopped=Διακόπηκε
#XFLD: Status text for Stopping
statusStopping=Διακόπηκε
#XFLD: Status text for Active
statusActive=Ενεργό
#XFLD: Status text for Paused
statusPaused=Διακόπηκε
#XFLD: Status text for not executed
lblNotExecuted=Δεν Εκτελέστηκε Ακόμα
#XFLD
messagesSettings=Ρυθμίσεις Μηνυμάτων
#XTOL
@validateModel=Μηνύματα Επικύρωσης
#XTOL
@hierarchy=Ιεραρχία
#XTOL
@columnCount=Αριθμός Στηλών
#XMSG
VAL_PACKAGE_CHANGED=Αντιστοιχίσατε αυτό το αντικείμενο στο πακέτο {1}. Πατήστε Αποθήκευση για να επιβεβαιώσετε και να επαληθεύσετε αυτή την αλλαγή. Η αντιστοίχιση σε ένα πακέτο δεν ανακαλείται σε αυτό τον επεξεργαστή μετά την αποθήκευση.
#XMSG
MISSING_DEPENDENCY=Οι εξαρτήσεις του αντικειμένου ''{0}'' δεν μπορούν να επιλυθούν στο πακέτο {1}.
#XFLD
deltaLoadInterval=Διάστημα Φόρτωσης Delta
#XFLD
lblHour=Ωρες (0-24)
#XFLD
lblMinutes=Λεπτά (0-59)
#XMSG
maxHourOrMinErr=Εισάγετε μία τιμή από 0 ως {0}
#XMSG
maxDeltaInterval=Η μέγιστη τιμή του διαστήματος φόρτωσης delta είναι 24 ώρες.{0}Αλλάξτε την τιμή λεπτών ή ωρών ανάλογα.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Διαδρομή Αποθηκευτικής Μονάδας Στόχου
#XFLD
confluentSubjectName=Ονομα Υποκειμένου
#XFLD
confluentSchemaVersion=Εκδοση Σχήματος
#XFLD
confluentIncludeTechKeyUpdated=Συμπερίληψη Τεχνικού Κωδικού
#XFLD
confluentOmitNonExpandedArrays=Παράβλεψη Μη Διευρυμένων Δίσκων
#XFLD
confluentExpandArrayOrMap=Επέκταση Δίσκου ή Χάρτη
#XCOL
confluentOperationMapping=Αντιστοίχιση Λειτουργίας
#XCOL
confluentOpCode=Opcode
#XFLD
confluentInsertOpCode=Εισαγωγή
#XFLD
confluentUpdateOpCode=Ενημέρωση
#XFLD
confluentDeleteOpCode=Διαγραφή
#XFLD
expandArrayOrMapNotSelectedTxt=Δεν επιλέχτηκε
#XFLD
confluentSwitchTxtYes=Ναι
#XFLD
confluentSwitchTxtNo=Οχι
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Σφάλμα
#XTIT
executeWarning=Προειδοποίηση
#XMSG
executeunsavederror=Αποθηκεύστε την ροή αντιγραφής σας πριν την εκτέλεση.
#XMSG
executemodifiederror=Υπάρχουν μη αποθηκευμένες αλλαγές στη ροή αντιγραφής. Αποθηκεύστε τη ροή αντιγραφής.
#XMSG
executeundeployederror=Πρέπει να αναπτύξετε την ροή αντιγραφής πριν την εκτελέσετε.
#XMSG
executedeployingerror=Περιεμένετε μέχρι να ολοκληρωθεί η ανάπτυξη.
#XMSG
msgRunStarted=Εκτέλεση άρχισε
#XMSG
msgExecuteFail=Αδύνατη εκτέλεση ροής αντιγραφής.
#XMSG
titleExecuteBusy=Περιμένετε.
#XMSG
msgExecuteBusy=Προετοιμάζουμε τα δεδομένα σας για εκτέλεση της ροής αντιγραφής.
#XTIT
executeConfirmDialog=Προειδοποίηση
#XMSG
msgExecuteWithValidations=Η ροή αντιγραφής έχει σφάλματα επαλήθευσης. Αυτό ίσως δημιουργήσει προβλήματα στην εκτέλεση.
#XMSG
msgRunDeployedVersion=Υπάρχουν αλλαγές για ανάπτυξη. Η τελευταία αναπτυγμένη έκδοση της ροής αντιγραφής θα εκτελεστεί. Θέλετε να συνεχίσετε;
#XBUT
btnExecuteAnyway=Εκτέλεση
#XBUT
btnExecuteClose=Κλείσιμο
#XBUT
loaderClose=Κλείσιμο
#XTIT
loaderTitle=Φόρτωση
#XMSG
loaderText=Προσκόμιση λεπτομερειών από τον διακομιστή
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Η ροή αντιγραφής σε αυτή την μη SAP σύνδεση στόχου δεν μπορεί να ξεκινήσει
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=γιατί δεν υπάρχει εξωτερικός όγκος διαθέσιμος για αυτό τον μήνα.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Ενας διαχειριστής μπορεί να αυξήσει τις ομάδες Premium Outbound για αυτό
#XMSG
premiumOutBoundRFAdminErrMsgPart2=μισθωτή, καθιστώντας διαθέσιμο τον εξωτερικό όγκο για αυτό τον μήνα.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Σφάλμα
#XTIT
deployInfo=Πληροφορίες
#XMSG
deployCheckFailException=Εξαίρεση κατά την ανάπτυξη
#XMSG
deployGBQFFDisabled=Η ανάπτυξη ροών αντιγραφής με τελική σύνδεση στο Google BigQuery είναι προσωρινά αδύνατη γιατί εκτελούμε συντήρηση σε αυτή την λειτουργία.
#XMSG
deployKAFKAFFDisabled=Η ανάπτυξη ροών αντιγραφής με τελική σύνδεση στο Apache Kafka είναι προσωρινά αδύνατη γιατί εκτελούμε συντήρηση σε αυτή την λειτουργία.
#XMSG
deployConfluentDisabled=Η ανάπτυξη ροών αντιγραφής με σύνδεση στόχου στο Confluent Kafka είναι προσωρινά αδύνατη γιατί εκτελούμε συντήρηση σε αυτή τη λειτουργία.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Για τα παρακάτω τελικά αντικείμενα, τα ονόματα πίνακα τίτλου delta χρησιμοποιούνται από άλλους πίνακες στην αποθήκη: {0} Πρέπει να μετονομάσετε αυτά τα αντικείμενα ώστε να βεβαιωθείτε ότι τα σχετικά ονόματα πίνακα είναι μοναδικά πριν αναπτύξετε τη ροή αντιγραφής.
#XMSG
deployDWCSourceFFDisabled=Η ανάπτυξη ροών αντιγραφής που έχουν SAP Datasphere καθώς η πηγή δεν είναι εφικτή γιατί εκτελούμε συντήρηση σε αυτή τη λειτουργία.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Η ανάπτυξη ροών αντιγραφής που περιέχει τοπικούς πίνακες delta ως αντικείμενα πηγής δεν είναι εφικτή γιατί εκτελούμε συντήρηση σε αυτή την λειτουργία.
#XMSG
deployHDLFSourceFFDisabled=Αναπτύσσοντας τις ροές αντιγραφής που έχουν πηγαίες συνδέσεις με τον τύπο σύνδεσης SAP HANA Cloud, τα Αρχεία Data Lake είναι προσωρινά αδύναταη γιατί εκτελούμε συντήρηση.
#XMSG
deployObjectStoreAsSourceFFDisabled=Η ανάπτυξη ροών αντιγραφής που έχουν παρόχους αποθήκευσης cloud ως πηγή τους είναι προσωρινά ανέφικτη. 
#XMSG
deployConfluentSourceFFDisabled=Η ανάπτυξη ροών αντιγραφής που έχουν το Confluent Kafka ως πηγή είναι αδύνατη προσωρινά γιατί εκτελούμε συντήρηση σε αυτή την λειτουργία.
#XMSG
deployMaxDWCNewTableCrossed=Για μεγάλες ροές αντιγραφής, δεν είναι εφικτή η αποθήκευση και ανάπτυξη ταυτόχρονα. Αποθηκεύστε τη ροή αντιγραφής πρώτα κι έπειτα αναπτύξτε την.
#XMSG
deployInProgressInfo=Η ανάπτυξη είναι σε εξέλιξη.
#XMSG
deploySourceObjectInUse=Τα αντικείμενα πηγής {0} χρησιμοποιούνται ήδη σε ροές αντιγραφής {1}.
#XMSG
deployTargetSourceObjectInUse=Τα αντικείμενα πηγής {0} χρησιμοποιούνται ήδη σε ροές αντιγραφής {1}. Τα αντικείμενα στόχου {2} χρησιμοποιούνται ήδη σε ροές αντιγραφής {3}.
#XMSG
deployReplicationFlowCheckError=Σφάλμα στην επικύρωση ροής αντιγραφής: {0} 
#XMSG
preDeployTargetObjectInUse=Τα αντικείμενα στόχου {0} χρησιμοποιούνται ήδη σε ροές αντιγραφής {1}, και δεν μπορείτε να έχετε το ίδιο αντικείμενο στόχου σε δύο διαφορετικές ροές αντιγραφής. Επιλέξτε άλλο αντικείμενο στόχου και προσπαθήστε ξανά.
#XMSG
runInProgressInfo=Ροή αντιγραφής εκτελείται ήδη.
#XMSG
deploySignavioTargetFFDisabled=Η ανάπτυξη ροών αντιγραφής που έχει το SAP Signavio ως στόχο δεν είναι εφικτή γιατί μία συντήρηση σε αυτή τη λειτουργία είναι ήδη σε εξέλιξη.
#XMSG
deployHanaViewAsSourceFFDisabled=Η ανάπτυξη ροών αντιγραφής που έχουν προβολές ως αντικείμενα πηγής για την επιλεγμένη σύνδεση πηγής δεν είναι εφικτή. Δοκιμάστε ξανά.
#XMSG
deployMsOneLakeTargetFFDisabled=Η ανάπτυξη ροών αντιγραφής που έχουν το MS OneLake ως στόχο δεν είναι εφικτή γιατί μία συντήρηση σε αυτή τη λειτουργία είναι ήδη σε εξέλιξη.
#XMSG
deploySFTPTargetFFDisabled=Η ανάπτυξη ροών αντιγραφής που έχουν το SFTP ως στόχο δεν είναι εφικτή γιατί μία συντήρηση σε αυτή τη λειτουργία είναι ήδη σε εξέλιξη.
#XMSG
deploySFTPSourceFFDisabled=Η ανάπτυξη ροών αντιγραφής που έχουν το SFTP ως στόχο δεν είναι εφικτή γιατί μία συντήρηση σε αυτή τη λειτουργία είναι ήδη σε εξέλιξη.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Τεχνικό Ονομα
#XFLD
businessNameInRenameTarget=Επωνυμία Εταιρίας
#XTOL
renametargetDialogTitle=Μετονομασία Αντικειμένου Στόχου
#XBUT
targetRenameButton=Μετονομασία
#XBUT
targetRenameCancel=Ακύρωση
#XMSG
mandatoryTargetName=Πρέπει να εισάγετε ένα όνομα.
#XMSG
dwcSpecialChar=_(κάτω παύλα) είναι ο μόνος χαρακτήρας που επιτρέπεται.
#XMSG
dwcWithDot=Το όνομα του πίνακα στόου μπορεί να περιέχει Λατινικούς χαρακτήρες, κάτω παύλες (_) και τελείες (.). Ο πρώτος χαρακτήρας πρέπει να είναι γράμμα, αριθμός ή κάτω παύλα (όχι τελεία).
#XMSG
nonDwcSpecialChar=Οι χαρακτήρες που επιτρέπονται είναι _(κάτω παύλα) -(παύλα) .(τελεία)
#XMSG
firstUnderscorePattern=Το όνομα δεν πρέπει να αρχίζει με _(κάτω παύλα)

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: Προβολή δήλωσης Πίνακα Δημιουργίας SQL
#XMSG
sqlDialogMaxPKWarning=Στο Google BigQuery, υποστηρίζονται μέχρι 16 αρχικοί κωδικοί και το αρχικό αντικείμενο έχει έναν μεγαλύτερο αριθμό. Επομένως, δεν ορίστηκαν αρχικοί κωδικοί σε αυτή τη δήλωση.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Μία ή περισσότερες αρχικές στήλες έχουν τύπους δεδομένων που δεν καθορίστηκαν ως αρχικοί κωδικοί στο Google BigQuery.Επομένως, δεν καθορίστηκαν αρχικοί κωδικοί σε αυτή την περίπτωση. Στο Google BigQuery, μόνο οι παρακάτω τύποι δεδομένων μπορούν να έχουν αρχικό κωδικό: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Αντιγραφή και Κλείσιμο
#XBUT
closeDDL=Κλείσιμο
#XMSG
copiedToClipboard=Αντιγραμμένο σε πίνακα σημειώσεων


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Το αντικείμενο ''{0}'' δεν μπορεί να αποτελεί μέρος μιας αλυσίδας εργασιών επειδή δεν έχει τέλος (καθώς περιλαμβάνει αντικείμενα με τύπο φορτίου Αρχικό και Delta).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Το αντικείμενο ''{0}'' δεν μπορεί να αποτελεί μέρος μιας αλυσίδας εργασιών επειδή δεν έχει τέλος (καθώς περιλαμβάνει αντικείμενα με τύπο φορτίου Αρχικό και Delta).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Το αντικείμενο ''{0}''’ δεν μπορεί να προστεθεί στην αλυσίδα εργασιών.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Υπάρχουν μη αποθηκευμένα αντικείμενα στόχου. Αποθηκεύστε τα ξανά. {0}{0} Η συμπεριφορά αυτού του χαρακτηριστικού άλλαξε: στο παρελθόν τα αντικείμενα στόχου δημιουργούνταν μόνο στο περιβάλλον στόχου όταν αναπτύσσονταν η ροή αντιγραφής.{0} Τώρα τα αντικείμενα δημιουργήθηκαν ήδη μόλις αποθηκεύτηκε η ροή αντιγραφής. Η ροή αντιγραφής δημιουργήθηκε πριν από αυτή την αλλαγή και περιέχει νέα αντικείμενα.{0} Πρέπει να αποθηκεύσετε τη ροή αντιγραφής ξανά πριν την αναπτύξετε ώστε τα νέα αντικείμενα να περιλαμβάνονται σωστά.
#XMSG
confirmChangeContentTypeMessage=Θέλετε να αλλάξετε τον τύπο περιεχομένου. Αν το κάνετε, θα διαγραφούν όλες οι υπάρχουσες προβολές.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Ονομα Υποκειμένου
#XFLD
schemaDialogVersionName=Εκδοση Σχήματος
#XFLD
includeTechKey=Συμπερίληψη Τεχνικού Κωδικού
#XFLD
segementButtonFlat=Επίπεδο
#XFLD
segementButtonNested=Ενθετο
#XMSG
subjectNamePlaceholder=Αναζήτηση Ονόματος Υποκειμένου 

#XMSG
@EmailNotificationSuccess=Η διαμόρφωση των ηλεκτρονικών ειδοποιήσεων χρόνου εκτέλεσης αποθηκεύτηκε.

#XFLD
@RuntimeEmailNotification=Ηλεκτρονική Ειδοποίηση Χρόνου Εκτέλεσης

#XBTN
@TXT_SAVE=Αποθήκευση


