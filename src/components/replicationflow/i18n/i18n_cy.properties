#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Llif Dyblygu

#XFLD: Edit Schema button text
editSchema=Golygu'r Sgema

#XTIT : Properties heading
configSchema=Ffurfweddu'r Sgema

#XFLD: save changed button text
applyChanges=Rhoi'r newidiadau ar waith


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Dewis <PERSON>tiad Ffynhonnell
#XFLD
sourceContainernEmptyText=Dewis <PERSON>dd
#XFLD
targetConnectionEmptyText=Dewis Cysylltiad Targed
#XFLD
targetContainernEmptyText=Dewis Cynhwysydd
#XFLD
sourceSelectObjectText=Dewis Gwrthrychau Ffynhonnell
#XFLD
sourceObjectCount=Gwrthrychau Ffynhonnell ({0})
#XFLD
targetObjectText=Gwrthrychau Targed
#XFLD
confluentBrowseContext=<PERSON><PERSON><PERSON><PERSON>des<PERSON>
#XBUT
@retry=Ailgynnig
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Wrthi'n diweddaru'r tenant.

#XTOL
browseSourceConnection=Pori cysylltiad ffynhonnell
#XTOL
browseTargetConnection=Pori cysylltiad targed
#XTOL
browseSourceContainer=Pori cynhwysydd ffynhonnell
#XTOL
browseAndAddSourceDataset=Ychwanegu gwrthrychau ffynhonnell
#XTOL
browseTargetContainer=Pori cynhwysydd targed
#XTOL
browseTargetSetting=Pori gosodiadau targed
#XTOL
browseSourceSetting=Pori gosodiadau ffynhonnell
#XTOL
sourceDatasetInfo=Gwybodaeth
#XTOL
sourceDatasetRemove=Tynnu
#XTOL
mappingCount=Mae hwn yn cynrychioli cyfanswm y mapiadau/mynegiannau sydd ddim yn seiliedig ar enw.
#XTOL
filterCount=Mae hwn yn cynrychioli cyfanswm yr amodau hidlo.
#XTOL
loading=Wrthi'n Llwytho...
#XCOL
deltaCapture=Cipio Delta
#XCOL
deltaCaptureTableName=Tabl Cipio Delta
#XCOL
loadType=Math o Lwytho
#XCOL
deleteAllBeforeLoading=Dileu pob un cyn llwytho
#XCOL
transformationsTab=Tafluniadau
#XCOL
settingsTab=Gosodiadau

#XBUT
renameTargetObjectBtn=Ailenwi Gwrthrych Targed
#XBUT
mapToExistingTargetObjectBtn=Map i Wrthrych Targed Cyfredol
#XBUT
changeContainerPathBtn=Newid Llwybr Cynhwysydd
#XBUT
viewSQLDDLUpdated=Gweld Datganiad Creu Tabl SQL
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Nid yw’r gwrthrych ffynhonnell yn gallu delio â chipio delta, ond mae’r opsiwn cipio delta wedi'i alluogi ar gyfer y gwrthrych targed dan sylw.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Does dim modd defnyddio gwrthrych y targed gan fod proses cipio delta wedi''i galluogi,{0}a dydy gwrthrych y ffynhonnell ddim yn gallu delio â phroses cipio delta.{1}Gallwch ddewis gwrthrych targed arall sydd ddim yn gallu delio â phroses cipio delta.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Mae gwrthrych targed â''r enw hwn yn bodoli''n barod. Ond, does dim modd ei ddefnyddio{0}gan fod proses cipio delta wedi''i galluogi, a dydy gwrthrych y ffynhonnell ddim yn gallu{0}delio â phroses cipio delta.{1}Gallwch naill ai roi''r enw gwrthrych targed presennol sydd ddim yn{0}gallu delio â phroses cipio delta, neu rhowch enw sydd ddim yn bodoli eto.
#XBUT
copySQLDDLUpdated=Copïo Datganiad Creu Tabl SQL
#XMSG
targetObjExistingNoCDCColumnUpdated=Rhaid i''r tablau presennol yn Google BigQuery gynnwys y colofnau canlynol ar gyfer cipio data newid (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Does dim modd delio â'r gwrthrychau ffynhonnell canlynol gan nad oes ganddynt brif allwedd, neu eu bod yn defnyddio cysylltiad sydd ddim yn bodloni'r amodau i nôl y brif allwedd:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Cyfeiriwch at SAP KBA 3531135 am ddatrysiad posibl.
#XLST: load type list values
initial=Cychwynnol yn Unig
@emailUpdateError=Gwall wrth ddiweddaru'r rhestr Hysbysiadau E-bost

#XLST
initialDelta=Cychwynnol a Delta

#XLST
deltaOnly=Delta yn Unig
#XMSG
confluentDeltaLoadTypeInfo=Ar gyfer ffynhonnell Kafka Cyfunol, dim ond math llwytho Cychwynnol a Delta y gellir delio â nhw.
#XMSG
confirmRemoveReplicationObject=Ydych chi'n cadarnhau eich bod am ddileu'r dyblygiad?
#XMSG
confirmRemoveReplicationTaskPrompt=Bydd y cam gweithredu hwn yn dileu dyblygiadau sydd eisoes yn bodoli. Ydych chi eisiau bwrw ymlaen?
#XMSG
confirmTargetConnectionChangePrompt=Bydd y cam gweithredu hwn yn ailosod y cysylltiad targed, y cynhwysydd targed ac yn dileu pob gwrthrych targed. Ydych chi eisiau bwrw ymlaen?
#XMSG
confirmTargetContainerChangePrompt=Bydd y cam gweithredu hwn yn ailosod y cynhwysydd targed ac yn dileu pob gwrthrych targed sydd eisoes yn bodoli. Bwrw ymlaen?
#XMSG
confirmRemoveTransformObject=A ydych yn cadarnhau eich bod am ddileu tafluniad {0}?
#XMSG
ErrorMsgContainerChange=Gwall wrth newid llwybr y cynhwysydd.
#XMSG
infoForUnsupportedDatasetNoKeys=Does dim modd delio â'r gwrthrychau ffynhonnell canlynol oherwydd does ganddyn nhw ddim allwedd gynradd:
#XMSG
infoForUnsupportedDatasetView=Does dim modd delio â'r gwrthrychau ffynhonnell canlynol o fath Gweddau:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Does dim modd delio â'r gwrthrych ffynhonnell canlynol gan ei fod yn wedd SQL sy'n cynnwys paramedrau mewnbwn:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Does dim modd delio â'r gwrthrychau ffynhonell canlynol oherwydd mae echdynnu wedi'i analluogi ar eu cyfer:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Ar gyfer cysylltiadau Cyfunol, yr unig fformatau cyfresu a ganiateir yw AVRO a JSON. Does dim modd delio â'r gwrthrychau canlynol oherwydd eu bod yn defnyddio fformat cyfresu gwahanol:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Does dim modd nôl y sgema ar gyfer y gwrthrychau canlynol. Ewch ati i ddewis y cyd-destun priodol neu gwiriwch ffurfweddiad y gofrestr sgema
#XTOL: warning dialog header on deleting replication task
deleteHeader=Dileu
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Dydy Google BigQuery ddim yn delio â'r gosodiad Dileu Popeth Cyn Llwytho.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Mae'r gosodiad Dileu Popeth Cyn Llwytho yn dileu ac yn ail-greu'r gwrthrych (pwnc) cyn pob dyblygiad. Mae hyn hefyd yn dileu pob neges sydd wedi'i neilltuo.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Dydy'r math targed hwn ddim yn gallu delio â'r gosodiad Dileu Popeth Cyn Llwytho.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Enw Technegol
#XCOL
connBusinessName=Enw Busnes
#XCOL
connDescriptionName=Disgrifiad
#XCOL
connType=Math
#XMSG
connTblNoDataFoundtxt=Heb Ganfod Unrhyw Gysylltiadau
#XMSG
connectionError=Gwall wrth nôl cysylltiadau.
#XMSG
connectionCombinationUnsupportedErrorTitle=Does dim modd delio â chyfuniad cysylltiadau
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Ar hyn o bryd does dim modd dyblygu o {0} i {1}.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Dim modd delio â chyfuniad math o gysylltiad
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Does dim modd delio â dyblygu o gysylltiad gyda''r math o gysylltiad SAP HANA Cloud, Ffeiliau Llyn Data i {0}. Dim ond i SAP Datasphere y gallwch ddyblygu hyn.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Dewis
#XBUT
containerCancelBtn=Canslo
#XTOL
containerSelectTooltip=Dewis
#XTOL
containerCancelTooltip=Canslo
#XMSG
containerContainerPathPlcHold=Llwybr Cynhwysydd
#XFLD
containerContainertxt=Cynhwysydd
#XFLD
confluentContainerContainertxt=Cyd-destun
#XMSG
infoMessageForSLTSelection=Dim ond ID Trosglwyddo Torfol/SLT sy'n cael ei ganiatáu fel cynhwysydd. Dewiswch ID Trosglwyddo Torfol o dan SLT (os yw ar gael) a chlicio Cyflwyno.
#XMSG
msgFetchContainerFail=Gwall wrth nôl data cynhwysydd.
#XMSG
infoMessageForSLTHidden=Dydy'r cysylltiad hwn ddim yn delio â ffolderi SLT, felly dydyn nhw ddim yn ymddangos yn y rhestr isod.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Dewiswch gynhwysydd sy'n cynnwys is-ffolderi.
#XMSG
sftpIncludeSubFolderText=Gau
#XMSG
sftpIncludeSubFolderTextNew=Na

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Dim mapio hidlydd eto)
#XMSG
failToFetchRemoteMetadata=Gwall wrth nôl metaddata.
#XMSG
failToFetchData=Gwall wrth nôl y targed presennol.
#XCOL
@loadType=Math o Lwytho
#XCOL
@deleteAllBeforeLoading=Dileu pob un cyn llwytho

#XMSG
@loading=Wrthi'n Llwytho...
#XFLD
@selectSourceObjects=Dewis Gwrthrychau Ffynhonnell
#XMSG
@exceedLimit=Ni allwch chi fewngludo mwy na {0} gwrthrych ar y tro. Dad-ddewiswch o leiaf {1} gwrthrych.
#XFLD
@objects=Gwrthrychau
#XBUT
@ok=Iawn
#XBUT
@cancel=Canslo
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Nesaf
#XBUT
btnAddSelection=Ychwanegu Dewisiad
#XTOL
@remoteFromSelection=Tynnu o'r Dewisiad
#XMSG
@searchInForSearchField=Chwilio yn {0}

#XCOL
@name=Enw Technegol
#XCOL
@type=Math
#XCOL
@location=Lleoliad
#XCOL
@label=Enw Busnes
#XCOL
@status=Statws

#XFLD
@searchIn=Chwilio yn:
#XBUT
@available=Ar Gael
#XBUT
@selection=Dewisiad

#XFLD
@noSourceSubFolder=Tablau a Gweddau
#XMSG
@alreadyAdded=Eisoes yn bresennol yn y diagram
#XMSG
@askForFilter=Mae yna fwy na {0} eitem. Rhowch linyn hidlydd i gyfyngu ar nifer yr eitemau.
#XFLD: success label
lblSuccess=Llwyddiant
#XFLD: ready label
lblReady=Parod
#XFLD: failure label
lblFailed=Wedi Methu
#XFLD: fetching status label
lblFetchingDetail=Wrthi'n cyrchu manylion

#XMSG Place holder text for tree filter control
filterPlaceHolder=Rhowch destun i hidlo gwrthrychau lefel-uchaf
#XMSG Place holder text for server search control
serverSearchPlaceholder=Teipiwch a phwyso Enter i chwilio
#XMSG
@deployObjects=Wrthi''n mewngludo {0} gwrthrych...
#XMSG
@deployObjectsStatus=Nifer y gwrthrychau sydd wedi cael eu mewngludo: {0}. Nifer y gwrthrychau nad oedd modd eu mewngludo: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Wedi methu agor porwr ystorfa lleol.
#XMSG
@openRemoteSourceBrowserError=Wedi methu nôl gwrthrychau ffynhonnell.
#XMSG
@openRemoteTargetBrowserError=Wedi methu nôl gwrthrychau targed.
#XMSG
@validatingTargetsError=Gwall wrth ddilysu targedau.
#XMSG
@waitingToImport=Parod i Fewngludo

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Rhagorwyd ar uchafswm nifer y gwrthrychau. Dewiswch uchafswm o 500 o wrthrychau ar gyfer un llif atgynhyrchu.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Enw Technegol
#XFLD
sourceObjectBusinessName=Enw Busnes
#XFLD
sourceNoColumns=Nifer y Colofnau
#XFLD
containerLbl=Cynhwysydd

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Rhaid i chi ddewis cysylltiad ffynhonnell ar gyfer y llif dyblygu.
#XMSG
validationSourceContainerNonExist=Rhaid i chi ddewis cynhwysydd ar gyfer y cysylltiad ffynhonnell.
#XMSG
validationTargetNonExist=Rhaid i chi ddewis cysylltiad targed ar gyfer y llif dyblygu.
#XMSG
validationTargetContainerNonExist=Rhaid i chi ddewis cynhwysydd ar gyfer y cysylltiad targed.
#XMSG
validationTruncateDisabledForObjectTitle=Dyblygu i storfeydd gwrthrych.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Dim ond os yw''r opsiwn Dileu Popeth cyn Llwytho wedi''i osod neu os nad yw''r gwrthrych targed yn bodoli yn y targed y mae modd dyblygu i le storio yn y cwmwl.{0}{0} I alluogi proses dyblygu o hyd ar gyfer gwrthrychau lle nad yw''r opsiwn Dileu Popeth cyn Llwytho wedi''i osod, gwnewch yn siŵr nad yw''r gwrthrych targed yn bodoli yn y system cyn i chi redeg y llif dyblygu.
#XMSG
validationTaskNonExist=Rhaid i chi fod ag o leiaf un dyblygiad yn y llif dyblygu.
#XMSG
validationTaskTargetMissing=Rhaid i chi fod â tharged ar gyfer y dyblygiad gyda’r ffynhonnell: {0}
#XMSG
validationTaskTargetIsSAC=Mae targed dan sylw yn Arteffact SAC: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Nid yw''r targed a ddewiswyd yn dabl lleol a gefnogir: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Mae gwrthrych gyda'r enw hwn eisoes yn bodoli yn y targed. Fodd bynnag, ni ellir defnyddio'r gwrthrych hwn fel gwrthrych targed ar gyfer llif dyblygu i'r ystorfa leol, gan nad yw'n dabl lleol.
#XMSG
validateSourceTargetSystemDifference=Rhaid i chi ddewis gwahanol ffynhonnell a chysylltiad targed a chyfuniadau cynhwysydd ar gyfer y llif dyblygu.
#XMSG
validateDuplicateSources=mae gan un neu fwy o ddyblygiadau enwau gwrthrych ffynhonnell dyblyg: {0}.
#XMSG
validateDuplicateTargets=mae gan un neu fwy o ddyblygiadau enwau gwrthrych targed dyblyg: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=Nid yw’r gwrthrych ffynhonnell {0} yn gallu delio â chipio delta, ond mae’r gwrthrych targed {1}. Rhaid i chi dynnu''r dyblygiad.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Rhaid i chi ddewis y math o lwyth “Cychwynnol a Delta” ar gyfer y dyblygiad gyda’r enw gwrthrych targed {0}.
#XMSG
validationAutoRenameTarget=Mae’r colofnau targed wedi cael eu hailenwi.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Mae awto daflunio wedi''i ychwanegu, ac mae’r golofn darged ganlynol wedi cael ei hail-enwi i ganiatáu dyblygiadau i''r targed:{1}{1}{0}{1}{1}Mae hyn oherwydd un o’r rhesymau canlynol:{1}{1}{2}Nodau nad oes modd delio â nhw{1}{2} Rhagddodiad wedi’i gadw
#XMSG
validationAutoRenameTargetDescriptionUpdated=Mae awto daflunio wedi''i ychwanegu ac mae’r golofn darged ganlynol wedi cael ei hail-enwi i ganiatáu dyblygiad yn Google BigQuery:{1}{1}{0}{1}{1}Mae hyn oherwydd un o’r rhesymau canlynol:{1}{1}{2}Enw colofn wedi’i gadw{1}{2}Nodau nad oes modd delio â nhw{1}{2}Rhagddodiad wedi’i gadw
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Mae awto daflunio wedi''i ychwanegu ac mae’r golofn darged ganlynol wedi cael ei hail-enwi i ganiatáu dyblygiadau i Confluent:{1}{1}{0}{1}{1}Mae hyn oherwydd un o’r rhesymau canlynol:{1}{1}{2}Enw colofn wedi’i gadw{1}{2}Nodau nad oes modd delio â nhw{1}{2}Rhagddodiad wedi’i gadw
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Mae awto daflunio wedi''i ychwanegu ac mae’r golofn darged ganlynol wedi cael ei hail-enwi i ganiatáu dyblygiadau i''r targed:{1}{1}{0}{1}{1}Mae hyn oherwydd un o’r rhesymau canlynol:{1}{1}{2}Enw colofn wedi’i gadw{1}{2}Nodau nad oes modd delio â nhw{1}{2}Rhagddodiad wedi’i gadw
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Mae’r gwrthrych targed wedi cael ei ailenwi.
#XMSG
autoRenameInfoDesc=Mae''r gwrthrych targed wedi cael ei ailenwi am ei fod yn cynnwys nodau anghydnaws. Dim ond y nodau canlynol sy''n gydnaws:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(dot){0}{1}_(tanlinell){0}{1}-(dash)
#XMSG
validationAutoTargetTypeConversion=Mae’r mathau o ddata targed wedi cael eu newid.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Ar gyfer y colofnau targed canlynol, mae’r mathau o ddata targed wedi cael eu newid, gan nad oes modd delio â’r mathau o ddata ffynhonnell yn Google BigQuery:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Ar gyfer y colofnau targed canlynol, mae’r mathau o ddata targed wedi cael eu newid, gan nad oes modd delio â’r mathau o ddata ffynhonnell yn y cysylltiad targed: {1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Byrhau enwau colofnau targed.
#XMSG
validationMaxCharLengthGBQTargetDescription=Yn Google BigQuery, mae enwau colofnau yn gallu defnyddio 300 nod. Defnyddiwch dafluniad i fyrhau’r enwau colofnau targed canlynol:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Ni fydd allweddi cynradd yn cael eu creu.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=Yn Google BigQuery, mae modd delio ag uchafswm o 16 allwedd gynradd ond mae gan y gwrthrych ffynhonnell fwy o allweddi cynradd. Ni fydd yr allweddi cynradd yn cael eu creu yn y gwrthrych targed.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Mae gan un neu ragor o golofnau ffynhonnell fathau o ddata nad oes modd eu diffinio fel allweddi cynradd yn Google BigQuery. Ni fydd unrhyw un o’r allweddi cynradd yn cael eu creu yn y gwrthrych targed.{0}{0}Mae’r mathau o ddata targed canlynol yn gydnaws â’r math o ddata Google BigQuery y mae modd diffinio allwedd gynradd ar ei gyfer:{0}{0}{1} BOOLEAIDD{0}{1} DYDDIAD{0}{1} DEGOL{0}{1} INT32{0}{1} INT64{0}{1} RHIFOL{0}{1} LLINYN{0}{1} STAMPAMSER
#XMSG
validateObjectStoreNoPKDatasetError=Diffiniwch un neu ragor o golofnau fel prif allwedd.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Er mwyn gwneud hyn,  rhaid diffinio un neu ragor o golofnau fel prif ddeialog sgema ffynhonnell defnydd.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Diffiniwch un neu ragor o golofnau fel prif allwedd.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Mae'n rhaid i chi ddiffinio un golofn neu fwy fel y brif allwedd sy'n cyfateb i gyfyngiadau'r brif allwedd ar gyfer eich gwrthrych ffynhonnell. Ewch i'r Sgema Ffurfweddu ym mhriodwedd eich gwrthrych ffynhonnell er mwyn gwneud hynny.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Rhowch gyfanswm gwerth rhannu dilys.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Rhaid i'r gwerth uchaf y Rhaniad fod yn ≥ 1 ac yn ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Diffiniwch un neu ragor o golofnau fel prif allwedd.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=I ddyblygu gwrthrych, rhaid i chi ddiffinio un neu ragor o golofnau targed fel prif allwedd. Defnyddiwch dafluniad i wneud hyn.
#XMSG
validateHDLFNoPKExistingDatasetError=Diffiniwch un neu ragor o golofnau fel prif allwedd.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=I ddyblygu data i wrthrych targed sy''n bodoli''n barod, rhaid iddo fod ag un neu ragor o golofnau sydd wedi''u diffinio fel y brif allwedd. {0} Mae gennych chi''r opsiynau canlynol ar gyfer diffinio un neu ragor o golofnau fel y brif allwedd: {0} {1} Defnyddiwch y golygydd tabl lleol i newid y gwrthrych targed sy''n bodoli''n barod. Yna, ail-lwythwch y llif dyblygu.{0}{1} Ailenwch y gwrthrych targed yn y llif dyblygu. Bydd hyn yn creu gwrthrych newydd cyn gynted ag y mae''r broses rhedeg wedi cychwyn. Ar ôl ailenwi, gallwch ddiffinio un neu ragor o golofnau fel y brif allwedd mewn rhagamcan.{0}{1} Mapiwch y gwrthrych i wrthrych targed arall sy''n bodoli''n barod lle mae un neu ragor o golofnau wedi''u diffinio''n barod fel y brif allwedd.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Mae''r targed a ddewiswyd eisoes yn bodoli yn yr ystorfa: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Mae''r enwau tabl cipio delta eisoes yn cael eu defnyddio gan dablau eraill yn yr ystorfa: {0} Rhaid i chi ailenwi''r gwrthrychau targed hyn i sicrhau bod enwau tabl cipio delta cysylltiedig yn unigryw cyn i chi allu arbed y llif dyblygu.
#XMSG
validateConfluentEmptySchema=Diffinio'r Sgema
#XMSG
validateConfluentEmptySchemaDescUpdated=Does dim sgema yn y tabl ffynhonnell. Ewch ati i ddewis Ffurfweddiad Sgema er mwyn diffinio un
#XMSG
validationCSVEncoding=Amgodio CSV Annilys
#XMSG
validationCSVEncodingDescription=Mae proses amgodio CSV y dasg yn annilys.
#XMSG
validateConfluentEmptySchema=Dewiswch fath o ddata targed cydnaws
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Dewiswch fath o ddata targed cydnaws
#XMSG
globalValidateTargetDataTypeDesc=Gwall wrth fapio'r colofnau. Ewch i'r Tafluniadau a sicrhau bod pob colofn ffynhonnell wedi'u mapio â cholofn unigryw, ac â cholofn sydd â math cydnaws o data, a bod yr holl fynegiannau sydd wedi'u diffinio yn ddilys.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Enwau Colofnau Dyblyg
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Does dim modd delio ag enwau Colofnau Dyblyg. Defnyddiwch y Deialog Taflunio i''w trwsio. Mae gan y gwrthrychau targed canlynol enwau colofnau dyblyg: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Enwau Colofnau Dyblyg.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Does dim modd delio ag enwau Colofnau Dyblyg. Mae gan y gwrthrychau targed canlynol enwau colofnau dyblyg: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Mae'n bosibl bod y data'n cynnwys anghysondebau.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Ni fydd y math llwytho Delta yn Unig yn ystyried y newidiadau a wnaed yn y ffynhonnell rhwng y broses gadw ddiwethaf a'r rhediad nesaf.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Newidiwch fath o lwyth i “Cychwynnol”.
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Dim ond ar gyfer math o lwyth "Cychwynnol" y gellir dyblygu gwrthrychau sy'n seiliedig ar ABAP ac sydd heb allwedd gynradd.
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Ewch ati i analluogi Cipio Delta.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=I ddyblygu gwrthrych sydd heb allwedd gynradd gan ddefnyddio math o gysylltiad ffynhonnell ABAP, rhaid i chi analluogi cipio delta yn gyntaf ar gyfer y tabl hwn.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Newid gwrthrych targed.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Does dim modd defnyddio gwrthrych targed gan fod cipio delta wedi'i alluogi. Gallwch chi naill ai ailenwi'r gwrthrych targed a diffodd cipio delta ar gyfer gwrthrych newydd (wedi'i ailenwi), neu fapio'r gwrthrych ffynhonnell i wrthrych targed lle mae cipio delta wedi'i analluogi.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Newid gwrthrych targed.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Does dim modd defnyddio'r gwrthrych targed oherwydd nad oes ganddo'r golofn dechnegol ofynnol __load_package_id. Gallwch chi ailenwi'r gwrthrych targed gan ddefnyddio enw sydd ddim yn bodoli'n barod. Wedyn, mae'r system yn creu gwrthrych newydd sydd â'r un diffiniad â'r gwrthrych ffynhonnell, ac mae'n cynnwys y golofn dechnegol. Fel arall, gallwch chi fapio'r gwrthrych targed i wrthrych sy'n bodoli'n barod ac sydd â'r golofn dechnegol ofynnol (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Newid gwrthrych targed.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Does dim modd defnyddio'r gwrthrych targed oherwydd nad oes ganddo'r golofn dechnegol ofynnol __load_record_id. Gallwch chi ailenwi'r gwrthrych targed gan ddefnyddio enw sydd ddim yn bodoli'n barod. Wedyn, mae'r system yn creu gwrthrych newydd sydd â'r un diffiniad â'r gwrthrych ffynhonnell, ac mae'n cynnwys y golofn dechnegol. Fel arall, gallwch chi fapio'r gwrthrych targed i wrthrych sy'n bodoli'n barod ac sydd â'r golofn dechnegol ofynnol (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Newid gwrthrych targed.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Does dim modd defnyddio'r gwrthrych targed oherwydd nad yw math y data yn y golofn dechnegol __load_record_id yn "string(44)". Gallwch chi ailenwi'r gwrthrych targed gan ddefnyddio enw sydd ddim yn bodoli'n barod. Wedyn, mae'r system yn creu gwrthrych newydd sydd â'r un diffiniad â'r gwrthrych ffynhonnell, ac felly'r math data cywir. Fel arall, gallwch chi fapio'r gwrthrych targed i wrthrych sy'n bodoli'n barod ac sydd â'r golofn dechnegol ofynnol (__load_record_id) gyda'r math data cywir.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Newid gwrthrych targed.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Does dim modd defnyddio'r gwrthrych targed oherwydd bod ganddo allwedd gynradd, ac oherwydd nad oes gan y gwrthrych targed allwedd gynradd. Gallwch chi ailenwi'r gwrthrych targed gan ddefnyddio enw sydd ddim yn bodoli'n barod. Wedyn, mae'r system yn creu gwrthrych newydd sydd â'r un diffiniad â'r gwrthrych ffynhonnell, ac sydd heb allwedd gynradd. Fel arall, gallwch chi fapio'r gwrthrych targed i wrthrych sy'n bodoli'n barod ac sydd â'r golofn dechnegol ofynnol (__load_package_id), ac sydd heb allwedd gynradd
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Newid gwrthrych targed.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Does dim modd defnyddio'r gwrthrych targed oherwydd bod ganddo allwedd gynradd, ac oherwydd nad oes gan y gwrthrych targed allwedd gynradd. Gallwch chi ailenwi'r gwrthrych targed gan ddefnyddio enw sydd ddim yn bodoli'n barod. Wedyn, mae'r system yn creu gwrthrych newydd sydd â'r un diffiniad â'r gwrthrych ffynhonnell, ac sydd heb allwedd gynradd. Fel arall, gallwch chi fapio'r gwrthrych targed i wrthrych sy'n bodoli'n barod ac sydd â'r golofn dechnegol ofynnol (__load_record_id), ac sydd heb allwedd gynradd.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Newid gwrthrych targed.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Does dim modd defnyddio'r gwrthrych targed oherwydd nad yw math y data yn y golofn dechnegol __load_package_id yn "binary(>=256)". Gallwch chi ailenwi'r gwrthrych targed gan ddefnyddio enw sydd ddim yn bodoli'n barod. Wedyn, mae'r system yn creu gwrthrych newydd sydd â'r un diffiniad â'r gwrthrych ffynhonnell, ac felly'r math data cywir. Fel arall, gallwch chi fapio'r gwrthrych targed i wrthrych sy'n bodoli'n barod ac sydd â'r golofn dechnegol ofynnol (__load_package_id) gyda'r math data cywir.
#XMSG
validationAutoRenameTargetDPID=Mae’r colofnau targed wedi cael eu hailenwi.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Tynnu'r gwrthrych ffynhonnell.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Nid oes gan y gwrthrych ffynhonnell golofn allweddol - nid oes modd delio â hyn yn y cyd-destun hwn.
#XMSG
validationAutoRenameTargetDPIDDescription=Mae awto daflunio wedi''i ychwanegu ac mae’r golofn darged ganlynol wedi cael ei hail-enwi i ganiatáu dyblygiad o ffynhonnell ABAP heb allweddi:{1}{1}{0}{1}{1}Mae hyn oherwydd un o’r rhesymau canlynol:{1}{1}{2}Enw colofn wedi’i gadw{1}{2}Nodau nad oes modd delio â nhw{1}{2}Rhagddodiad wedi’i gadw
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Dyblygu i {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Does dim modd arbed a gosod llifau dyblygu sydd â {0} fel eu targed ar hyn o bryd gan ein bod yn gwneud gwaith cynnal a chadw ar y nodwedd hon.
#XMSG
TargetColumnSkippedLTF=Mae’r golofn darged wedi cael ei hepgor.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Mae''r golofn darged wedi cael ei hepgor oherwydd math data anghydnaws. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Colofn Amser fel Prif Allwedd.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Mae gan wrthrych ffynhonnell y golofn amser fel prif allwedd, sy'n anchydnaws yn y cyd-destun hwn.
#XMSG
validateNoPKInLTFTarget=Mae'r Brif Allwedd ar goll.
#XMSG
validateNoPKInLTFTargetDescription=Nid yw'r brif allwedd wedi'i diffinio yn y targed, a does dim modd delio â hynny yn y cyd-destun hwn.
#XMSG
validateABAPClusterTableLTF=Tabl Clwstwr ABAP
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Mae'r gwrthrych ffynhonnell yn dabl clwstwr ABAP, ac nid oes modd delio ag ef yn y cyd-destun hwn.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Dydych chi heb ychwanegu unrhyw ddata ar hyn o bryd.
#YINS
welcomeText2=I ddechrau eich llif dyblygu, dewiswch gysylltiad a gwrthrych ffynhonnell ar yr ochr chwith.

#XBUT
wizStep1=Dewis Cysylltiad Ffynhonnell
#XBUT
wizStep2=Dewis Cynhwysydd Ffynhonnell
#XBUT
wizStep3=Ychwanegu Gwrthrychau Ffynhonnell

#XMSG
limitDataset=Wedi cyrraedd y nifer uchaf o wrthrychau. Tynnwch wrthrychau presennol i ychwanegu rhai newydd, neu greu llif atgynhyrchu newydd.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Does dim modd cychwyn y llif dyblygu i’r cysylltiad targed hwn sydd ddim yn SAP gan nad oes swm allbwn ar gael ar gyfer y mis hwn.
#XMSG
premiumOutBoundRFAdminWarningMsg=Gall gweinydd gynyddu’r blociau allbwn premiwm ar gyfer y tenant hwn, gan sicrhau bod swm allbwn ar gael ar gyfer y mis hwn.
#XMSG
messageForToastForDPIDColumn2=Colofn newydd wedi''i hychwanegu at y targed ar gyfer {0} gwrthrych - mae ei angen er mwyn trin cofnodion dyblyg mewn cysylltiad â gwrthrychau ffynhonnell sy''n seiliedig ar ABAP ac sydd heb allwedd gynradd.
#XMSG
PremiumInboundWarningMessage=Yn dibynnu ar nifer y llifau dyblygu a chyfaint y data sydd angen eu dyblygu, mae''r adnoddau SAP HANA{0}sydd eu hangen i ddyblygu data drwy {1} o bosibl yn mynd i fod yn fwy na''r capasiti sydd ar gael ar gyfer eich tenant.
#XMSG
PremiumInboundWarningMsg=Yn dibynnu ar nifer y llifau dyblygu a chyfaint y data sydd angen eu dyblygu,{0}mae''r adnoddau SAP HANA sydd eu hangen i ddyblygu data drwy "{1}" o bosibl yn mynd i fod yn fwy na''r capasiti sydd ar gael ar gyfer eich tenant.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Rhowch enw tafluniad.
#XMSG
emptyTargetColumn=Rhowch enw colofn darged.
#XMSG
emptyTargetColumnBusinessName=Rhowch Enw Busnes y golofn darged.
#XMSG
invalidTransformName=Rhowch enw tafluniad.
#XMSG
uniqueColumnName=Ailenwch golofn darged.
#XMSG
copySourceColumnLbl=Copïo colofnau o wrthrych ffynhonnell
#XMSG
renameWarning=Gwnewch yn siŵr eich bod chi'n dewis enw unigryw wrth ailenwi'r tabl targed. Os yw'r tabl gyda'r enw newydd eisoes yn bodoli yn y gofod, bydd yn defnyddio diffiniad y tabl hwnnw.

#XMSG
uniqueColumnBusinessName=Ail-enwi enw busnes y golofn darged.
#XMSG
uniqueSourceMapping=Dewiswch golofn ffynhonnell.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=Mae''r golofn ffynhonnell {0} eisoes yn cael ei defnyddio gan y colofnau targed canlynol:{1}{1}{2}{1}{1} Ar gyfer y golon darged hon neu ar gyfer colofnau targed eraill, dewiswch golofn ffynhonnell sydd ddim yn cael ei defnyddio''n barod i gadw''r tafluniad.
#XMSG
uniqueColumnNameDescription=Mae enw’r golofn darged rydych chi wedi'i roi eisoes yn bodoli. I allu cadw'r tafluniad, rhaid i chi nodi enw colofn unigryw.
#XMSG
uniqueColumnBusinessNameDesc=Mae enw busnes y golofn darged eisoes yn bodoli. I gadw'r tafluniad, rhaid i chi nodi enw busnes unigryw.
#XMSG
emptySource=Rhaid i chi ddewis colofn ffynhonnell neu roi cysonyn.
#XMSG
emptySourceDescription=I greu cofnod mapio dilys, rhaid i chi ddewis colofn ffynhonnell neu roi gwerth cysonyn.
#XMSG
emptyExpression=Diffinio mapio.
#XMSG
emptyExpressionDescription1=Dewiswch y golofn ffynhonnell lle rydych chi eisiau mapio’r golofn darged iddi, neu dicio’r blwch ticio yn y golofn {0}Swyddogaethau / Cysonion{1}.{2} {2} Bydd swyddogaethau''n cael eu rhoi''n awtomatig yn ôl y math o ddata targed. Mae modd i rywun roi gwerthoedd cysonyn ei hun.
#XMSG
numberExpressionErr=Rhowch rif.
#XMSG
numberExpressionErrDescription=Rydych chi wedi dewis math o ddata rhifol. Mae hyn yn golygu mai dim ond rhifau y gallwch chi eu rhoi, a phwynt degol os yw’n berthnasol. Peidiwch â defnyddio dyfynodau sengl.
#XMSG
invalidLength=Rhowch werth hyd dilys.
#XMSG
invalidLengthDescription=Rhaid i hyd y math o ddata fod yn hafal â neu’n fwy na hyd y golofn ffynhonnell a gall fod rhwng 1 a 5000.
#XMSG
invalidMappedLength=Rhowch werth hyd dilys.
#XMSG
invalidMappedLengthDescription=Rhaid i hyd y math o ddata fod yn hafal â neu’n fwy na hyd y golofn ffynhonnell {0} a gall fod rhwng 1 a 5000.
#XMSG
invalidPrecision=Rhowch werth manylder dilys.
#XMSG
invalidPrecisionDescription=Mae manylder yn diffinio cyfanswm y digidau. Mae graddfa’n diffinio nifer y digidau ar ôl y pwynt degol a gall fod rhwng 0 a manylder.{0}{0} Enghreifftiau: {0}{1} Mae manylder 6, graddfa 2 yn cyfateb i rifau fel 1234.56.{0}{1} Mae manylder 6, graddfa 6 yn cyfateb i rifau fel 0.123546.{0} {0} Mae''n rhaid i''r manylder a''r raddfa ar gyfer y targed fod yn gydnaws â''r manylder a''r raddfa ar gyfer y ffynhonnell fel bod pob digid o''r ffynhonnell yn ffitio i faes y targed. Er enghraifft, os oes manylder 6 a graddfa 2 yn y ffynhonnell (ac felly, digidau eraill heblaw 0 cyn y pwynt degol), does dim modd i chi gael manylder 6 a graddfa 6 yn y targed.
#XMSG
invalidPrimaryKey=Rhowch o leiaf un brif ffynhonnell.
#XMSG
invalidPrimaryKeyDescription=Nid oes prif ffynhonnell wedi'i diffinio ar gyfer y sgema hwn.
#XMSG
invalidMappedPrecision=Rhowch werth manylder dilys.
#XMSG
invalidMappedPrecisionDescription1=Mae manylder yn diffinio cyfanswm y digidau. Mae graddfa’n diffinio nifer y digidau ar ôl y pwynt degol a gall fod rhwng 0 a manylder.{0}{0} Enghreifftiau: {0}{1} Mae manylder 6, graddfa 2 yn cyfateb i rifau fel 1234.56.{0}{1} Mae manylder 6, graddfa 6 yn cyfateb i rifau fel 0.123546.{0} {0}Rhaid i fanylder y math o ddata fod yn hafal i fanylder y ffynhonnell neu fod yn fwy na hynny ({2})
#XMSG
invalidScale=Rhowch werth graddfa dilys.
#XMSG
invalidScaleDescription=Mae manylder yn diffinio cyfanswm y digidau. Mae graddfa’n diffinio nifer y digidau ar ôl y pwynt degol a gall fod rhwng 0 a manylder.{0}{0} Enghreifftiau: {0}{1} Mae manylder 6, graddfa 2 yn cyfateb i rifau fel 1234.56.{0}{1} Mae manylder 6, graddfa 6 yn cyfateb i rifau fel 0.123546.{0} {0} Mae''n rhaid i''r manylder a''r raddfa ar gyfer y targed fod yn gydnaws â''r manylder a''r raddfa ar gyfer y ffynhonnell fel bod pob digid o''r ffynhonnell yn ffitio i faes y targed. Er enghraifft, os oes manylder 6 a graddfa 2 yn y ffynhonnell (ac felly, digidau eraill heblaw 0 cyn y pwynt degol), does dim modd i chi gael manylder 6 a graddfa 6 yn y targed.
#XMSG
invalidMappedScale=Rhowch werth graddfa dilys.
#XMSG
invalidMappedScaleDescription1=Mae manylder yn diffinio cyfanswm y digidau. Mae graddfa’n diffinio nifer y digidau ar ôl y pwynt degol a gall fod rhwng 0 a manylder.{0}{0} Enghreifftiau: {0}{1} Mae manylder 6, graddfa 2 yn cyfateb i rifau fel 1234.56.{0}{1} Mae manylder 6, graddfa 6 yn cyfateb i rifau fel 0.123546.{0} {0} Rhaid i raddfa''r math o ddata fod yn hafal i raddfa''r ffynhonnell neu fod yn fwy na hynny ({2}).
#XMSG
nonCompatibleDataType=Dewiswch fath o ddata targed cydnaws.
#XMSG
nonCompatibleDataTypeDescription1=Rhaid i’r math o ddata rydych chi’n ei nodi yma fod yn gydnaws â’r math o ddata ffynhonnell ({0}). {1}{1} Enghraifft: os oes gan golofn eich ffynhonnell linyn math o ddata a''i bod yn cynnwys llythrennau, chewch chi ddim defnyddio math o ddata degol ar gyfer eich targed.
#XMSG
invalidColumnCount=Dewiswch golofn ffynhonnell.
#XMSG
ObjectStoreInvalidScaleORPrecision=Rhowch werth dilys ar gyfer y manylder a’r raddfa .
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=Y gwerth cyntaf yw'r manylder, sy'n diffinio cyfanswm nifer y digidau. Yr ail werth yw'r raddfa, sy'n diffinio'r digidau a ddaw ar ôl y pwynt degol. Rhowch werth graddfa darged sy'n fwy na gwerth y raddfa ffynhonnell a gwnewch yn siŵr fod y gwahaniaeth rhwng y raddfa darged a'r gwerth manylder yn fwy na'r gwahaniaeth rhwng y raddfa ffynhonnel a'r gwerth manylder.
#XMSG
InvalidPrecisionORScale=Rhowch werth dilys ar gyfer y manylder a’r raddfa .
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=Y gwerth cyntaf yw’r manylder, sy’n diffinio cyfanswm y digidau. Yr ail werth yw’r raddfa, sy’n diffinio’r digidau ar ôl y pwynt degol.{0}{0}Gan nad yw Google BigQuery yn gallu delio â’r data ffynhonnell, mae’n cael ei drosi i’r math o ddata targed DEGOL. Yn yr achos hwn, does dim ond modd diffinio’r manylder rhwng 38 a 76, a’r raddfa rhwng 9 a 38. Yn ogystal, rhaid i ganlyniad manylder minws graddfa, sy’n cynrychioli’r digidau cyn y pwynt degol, fod rhwng 29 a 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=Y gwerth cyntaf yw’r manylder, sy’n diffinio cyfanswm y digidau. Yr ail werth yw’r raddfa, sy’n diffinio’r digidau ar ôl y pwynt degol.{0}{0}Gan nad yw Google BigQuery yn gallu delio â’r data ffynhonnell, mae’n cael ei drosI i’r math o ddata targed DEGOL. Yn yr achos hwn, rhaid i’r manylder gael ei ddiffinio fel 20 neu fwy. Yn ogystal, rhaid i ganlyniad manylder minws graddfa, sy’n cynrychioli’r digidau cyn y pwynt degol, fod yn 20 neu fwy.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=Y gwerth cyntaf yw’r manylder, sy’n diffinio cyfanswm y digidau. Yr ail werth yw’r raddfa, sy’n diffinio’r digidau ar ôl y pwynt degol.{0}{0}Gan nad yw''r targed yn gallu delio â’r data ffynhonnell, mae’n cael ei drosI i’r math o ddata targed DEGOL. Yn yr achos hwn, rhaid i''r manylder gael ei ddiffinio gan rif sy''n fwy neu''n hafal i 1 ac sy''n llai na 38, a rhaid i''r raddfa fod yn llai neu''n hafal i''r manylder.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=Y gwerth cyntaf yw’r manylder, sy’n diffinio cyfanswm y digidau. Yr ail werth yw’r raddfa, sy’n diffinio’r digidau ar ôl y pwynt degol.{0}{0}Gan nad yw''r targed yn gallu delio â’r data ffynhonnell, mae’n cael ei drosI i’r math o ddata targed DEGOL. Yn yr achos hwn, rhaid i’r manylder gael ei ddiffinio fel 20 neu fwy. Yn ogystal, rhaid i ganlyniad manylder minws graddfa, sy’n cynrychioli’r digidau cyn y pwynt degol, fod yn 20 neu fwy.
#XMSG
invalidColumnCountDescription=I greu cofnod mapio dilys, rhaid i chi ddewis colofn ffynhonnell neu roi gwerth cysonyn.
#XMSG
duplicateColumns=Ailenwi colofn darged.
#XMSG
duplicateGBQCDCColumnsDesc=Mae enw’r golofn darged wedi’i gadw yn Google BigQuery. Bydd angen i chi ei ail-enwi i allu cadw’r tafluniad.
#XMSG
duplicateConfluentCDCColumnsDesc=Mae enw’r golofn darged wedi’i gadw yn Cyfunol. Bydd angen i chi ei ail-enwi i allu cadw’r tafluniad.
#XMSG
duplicateSignavioCDCColumnsDesc=Mae enw’r golofn darged wedi’i gadw yn SAP Signavio. Bydd angen i chi ei ail-enwi i allu cadw’r tafluniad.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Mae enw’r golofn darged wedi’i gadw yn MS OneLake. Bydd angen i chi ei ail-enwi i allu cadw’r tafluniad.
#XMSG
duplicateSFTPCDCColumnsDesc=Mae enw’r golofn darged wedi’i gadw yn SFTP. Bydd angen i chi ei ail-enwi i allu cadw’r tafluniad.
#XMSG
GBQTargetNameWithPrefixUpdated1=Mae enw’r golofn darged yn cynnwys rhagddodiad sydd wedi’i gadw yn Google BigQuery. Bydd angen i chi ei ail-enwi i allu cadw’r tafluniad.{0}{0}Does dim modd i enw colofn darged ddechrau gyda’r llinynnau canlynol:{0}{0}{1}{2}  _TABLE_{3}{2} _FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2}__ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Byrhau enw colofn darged.
#XMSG
GBQtargetMaxLengthDesc=Yn Google BigQuery, mae enw colofn yn gallu defnyddio hyd at 300 nod. Gwnewch enw’r golofn darged yn fyrach i allu cadw’r tafluniad.
#XMSG
invalidMappedScalePrecision=Rhaid i’r manylder a’r raddfa ar gyfer targed fod yn gydnaws â’r manylder a’r raddfa ar gyfer y ffynhonnell fel bod yr holl rifau o’r ffynhonnell yn ffitio yn y maes targed.
#XMSG
invalidMappedScalePrecisionShortText=Rhowch werth manylder a graddfa dilys.
#XMSG
validationIncompatiblePKTypeDescProjection3=Mae gan un neu ragor o golofnau ffynhonnell fathau o ddata nad oes modd eu diffinio fel allweddi cynradd yn Google BigQuery. Ni fydd unrhyw un o’r allweddi cynradd yn cael eu creu yn y gwrthrych targed.{0}{0}Mae’r mathau o ddata targed canlynol yn gydnaws â’r math o ddata Google BigQuery y mae modd diffinio allwedd gynradd ar ei gyfer:{1}{2} BOOLEAIDD{3}{2} DYDDIAD{3}{2} DEGOL{3}{2} INT32{3}{2} INT64{3}{2} RHIFOL{3}{2} LLINYN{3}{2} STAMPAMSER
#XMSG
uncheckColumnMessageId=Dad-dicio column __message_id.
#XMSG
uncheckColumnMessageIdDesc=Colofn: Allwedd Gynradd
#XMSG
validationOpCodeInsert=Rhaid rhoi gwerth ar gyfer Mewnosod.
#XMSG
recommendDifferentPrimaryKey=Rydym yn argymell eich bod yn dewis prif allwedd wahanol ar lefel yr eitem.
#XMSG
recommendDifferentPrimaryKeyDesc=Pan fydd y cod gweithredu eisoes wedi'i ddiffinio, argymhellir dewis prif allweddi gwahanol ar gyfer y mynegai arae a'r eitemau, a hynny er mwyn osgoi problemau megis dyblygu colofnnau, er enghraifft.
#XMSG
selectPrimaryKeyItemLevel=Rhaid i chi ddewis o leiaf un prif allwedd ar gyfer y pennyn a lefel yr eitem.
#XMSG
selectPrimaryKeyItemLevelDesc=Pan fydd arae neu fap yn cael ei ehangu, rhaid i chi ddewis dwy brif allwedd, un ar lefel y pennawd ac un ar lefel yr eitem.
#XMSG
invalidMapKey=Rhaid i chi ddewis o leiaf un prif allwedd ar gyfer lefel y pennyn.
#XMSG
invalidMapKeyDesc=Pan fydd arae neu fap yn cael ei ehangu, rhaid i chi ddewis prif allwedd ar gyfer lefel y pennyn.
#XFLD
txtSearchFields=Chwilio Colofnau Targed
#XFLD
txtName=Enw
#XMSG
txtSourceColValidation=Nid oes modd delio ag un neu ragor o golofnau ffynhonnell:
#XMSG
txtMappingCount=Mapio ({0})
#XMSG
schema=Sgema
#XMSG
sourceColumn=Colofnau Ffynhonnell
#XMSG
warningSourceSchema=Bydd unrhyw newidiadau a wneir i'r sgema yn effeithio ar fapio yn neialog y prosiect.
#XCOL
txtTargetColName=Colofn Darged (Enw Technegol)
#XCOL
txtDataType=Math Data Targed
#XCOL
txtSourceDataType=Math o Ddata Ffynhonnell
#XCOL
srcColName=Colofn Ffynhonnell (Enw Technegol)
#XCOL
precision=Manyldra
#XCOL
scale=Graddfa
#XCOL
functionsOrConstants=Swyddogaethau / Cysonion
#XCOL
txtTargetColBusinessName=Colofn Darged (Enw Busnes).
#XCOL
prKey=Allwedd Gynradd
#XCOL
txtProperties=Priodweddau
#XBUT
txtOK=Cadw
#XBUT
txtCancel=Canslo
#XBUT
txtRemove=Tynnu
#XFLD
txtDesc=Disgrifiad
#XMSG
rftdMapping=Mapio
#XFLD
@lblColumnDataType=Math o Ddata
#XFLD
@lblColumnTechnicalName=Enw Technegol
#XBUT
txtAutomap=Map Awtomatig
#XBUT
txtUp=I Fyny
#XBUT
txtDown=I Lawr

#XTOL
txtTransformationHeader=Tafluniad
#XTOL
editTransformation=Golygu
#XTOL
primaryKeyToolip=Allwedd


#XMSG
rftdFilter=Hidlo
#XMSG
rftdFilterColumnCount=Ffynhonnell: {0}({1})
#XTOL
rftdFilterColSearch=Chwilio
#XMSG
rftdFilterColNoData=Dim colofnau i’w dangos
#XMSG
rftdFilteredColNoExps=Dim mynegiant hidlo
#XMSG
rftdFilterSelectedColTxt=Ychwanegu Hidlydd ar gyfer
#XMSG
rftdFilterTxt=Hidlydd ar gael ar gyfer
#XBUT
rftdFilterSelectedAddColExp=Ychwanegu Mynegiant
#YINS
rftdFilterNoSelectedCol=Dewiswch golofn i ychwanegu hidlydd.
#XMSG
rftdFilterExp=Mynegiant Hidlo
#XMSG
rftdFilterNotAllowedColumn=Does dim modd ychwanegu hidlyddion ar gyfer y golofn hon.
#XMSG
rftdFilterNotAllowedHead=Colofn nad oes modd delio â hi
#XMSG
rftdFilterNoExp=Nid oes hidlydd wedi’i ddiffinio
#XTOL
rftdfilteredTt=Wedi hidlo
#XTOL
rftdremoveexpTt=Tynnu mynegiant hidlo
#XTOL
validationMessageTt=Negeseuon Dilysu
#XTOL
rftdFilterDateInp=Dewiswch ddyddiad
#XTOL
rftdFilterDateTimeInp=Dewiswch ddyddiad amser
#XTOL
rftdFilterTimeInp=Dewiswch amser
#XTOL
rftdFilterInp=Rhowch werth
#XMSG
rftdFilterValidateEmptyMsg=Mae {0} mynegiant hidlo ar {1} colofn yn wag
#XMSG
rftdFilterValidateInvalidNumericMsg=Mae {0} mynegiant hidlo ar {1} colofn yn cynnwys gwerthoedd rhifol annilys
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Rhaid i fynegiant hidlo gynnwys gwerthoedd rhifol dilys
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Os yw'r sgema gwrthrych targed wedi newid, defnyddiwch y swyddogaeth "Map i Wrthrych Targed Cyfredol" ar y brif dudalen i addasu'r newidiadau, ac i ail-fapio'r gwrthrych targed â'i ffynhonnell unwaith eto.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Os yw'r tabl targed eisoes yn bodoli a bod y mapio'n cynnwys newid sgema, rhaid i chi newid y tabl targed yn unol â hynny cyn defnyddio'r llif atgynhyrchu.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Os yw eich mapio yn cynnwys newid sgema, rhaid i chi newid y tabl targed yn unol â hynny cyn defnyddio'r llif atgynhyrchu.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Cafodd y colofnau canlynol nad oes modd delio â nhw eu hepgor o ddiffiniad y ffynhonnell: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Cafodd y colofnau canlynol nad oes modd delio â nhw eu hepgor o ddiffiniad y targed: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Does dim modd delio â''r gwrthrychau canlynol oherwydd eu bod wedi''i datgelu ar gyfer defnyddio: {0} {1} {0} {0} I ddefnyddio tablau yn y llif dyblygu, ni all y defnydd semantig (yn ngosodiadau''r tabl) fod wedi''i osod i {2}Set Ddata Ddadansoddol{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Does dim modd defnyddio''r gwrthrych targed oherwydd ei fod wedi''i ddatgelu ar gyfer defnyddio: {0} {0} I ddefnyddio''r tabl yn y llif dyblygu, ni all y defnydd semantig (yn ngosodiadau''r tabl) fod wedi''i osod i {1}Set Ddata Ddadansoddol{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Mae gwrthrych targed gyda''r enw hwn eisoes yn bodoli. Ond, does dim modd defnyddio''r gwrthrych targed oherwydd ei fod wedi''i ddatgelu ar gyfer defnyddio: {0} {0} I ddefnyddio''r tabl yn y llif dyblygu, ni all y defnydd semantig (yn ngosodiadau''r tabl) fod wedi''i osod i {1}Set Ddata Ddadansoddol{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Mae gwrthrych gyda''r enw hwn eisoes yn bodoli yn y targed. {0}Fodd bynnag, ni ellir defnyddio''r gwrthrych hwn fel gwrthrych targed ar gyfer llif dyblygu i''r ystorfa leol, gan nad yw''n dabl lleol.
#XMSG:
targetAutoRenameUpdated=Mae’r golofn darged wedi cael ei hailenwi.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Mae’r golofn darged wedi cael ei hail-enwi i ganiatáu dyblygiadau yn Google BigQuery. Mae hyn oherwydd un o’r rhesymau canlynol: {0}{1}{2}Enw colofn wedi’i gadw{3}{2}Nodau nad oes modd delio â nhw{3}{2}Rhagddodiad wedi’i gadw{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Mae’r golofn darged wedi cael ei hail-enwi i ganiatáu dyblygiadau yn Confluent. Mae hyn oherwydd un o’r rhesymau canlynol: {0}{1}{2}Enw colofn wedi’i gadw{3}{2}Nodau nad oes modd delio â nhw{3}{2}Rhagddodiad wedi’i gadw{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Mae’r golofn darged wedi cael ei hail-enwi i ganiatáu dyblygiadau i''r targed. Mae hyn oherwydd un o’r rhesymau canlynol: {0}{1}{2} Nodau nad oes modd delio â nhw {3}{2}Rhagddodiad wedi''i gadw{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Mae’r golofn darged wedi cael ei hail-enwi i ganiatáu dyblygiadau i''r targed. Mae hyn oherwydd un o’r rhesymau canlynol: {0}{1}{2}Enw''r golofn wedi''i wrthdroi {3}{2}Nodau nad oes modd delio â nhw{3}{2}Rhagddodiad wedi''i wrthdroi{3}{4}
#XMSG:
targetAutoDataType=Mae’r math o ddata targed wedi cael ei newid.
#XMSG:
targetAutoDataTypeDesc=Mae’r math o ddata targed wedi cael ei newid i {0} gan nad yw Google BigQuery yn gallu delio â’r math o ddata ffynhonnell.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Mae’r math o ddata targed wedi''i newid i {0} gan nad yw''r cysylltiad targed yn gallu delio â’r math o ddata ffynhonnell.
#XMSG
projectionGBQUnableToCreateKey=Ni fydd allweddi cynradd yn cael eu creu.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=Yn Google BigQuery, mae modd delio ag uchafswm o 16 allwedd gynradd ond mae gan y gwrthrych ffynhonnell fwy o allweddi cynradd. Ni fydd yr allweddi cynradd yn cael eu creu yn y gwrthrych targed.
#XMSG
HDLFNoKeyError=Diffiniwch un neu ragor o golofnau fel prif allwedd.
#XMSG
HDLFNoKeyErrorDescription=I ddyblygu gwrthrych, rhaid i chi ddiffinio un neu ragor o golofnau targed fel prif allwedd.
#XMSG
HDLFNoKeyErrorExistingTarget=Diffiniwch un neu ragor o golofnau fel prif allwedd.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=I ddyblygu data i wrthrych targed sy''n bodoli''n barod, rhaid iddo fod ag un neu ragor o golofnau sydd wedi''u diffinio fel y brif allwedd. {0} {0} Mae gennych chi''r opsiynau canlynol ar gyfer diffinio un neu ragor o golofnau fel y brif allwedd: {0}{0}{1} Defnyddiwch y golygydd tabl lleol i newid y gwrthrych targed sy''n bodoli''n barod. Yna, ail-lwythwch y llif dyblygu.{0}{0}{1} Ailenwch y gwrthrych targed yn y llif dyblygu. Bydd hyn yn creu gwrthrych newydd cyn gynted ag y mae''r broses rhedeg wedi cychwyn. Ar ôl ailenwi, gallwch ddiffinio un neu ragor o golofnau fel y brif allwedd mewn rhagamcan.{0}{0}{1} Mapiwch y gwrthrych i wrthrych targed arall sy''n bodoli''n barod lle mae un neu ragor o golofnau wedi''u diffinio''n barod fel y brif allwedd.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Prif allwedd wedi newid.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=O''i gymharu â''r gwrthrych ffynhonnell, mae gennych wahanol golofnau wedi''u diffinio fel y brif allwedd ar gyfer y gwrthrych targed. Gwnewch yn siŵr fod y colofnau hyn yn adnabod pob rhes yn unigryw i osgoi llygru data posibl wrth ddyblygu data yn ddiweddarach. {0} {0} Yn y gwrthrych ffynhonnell, mae''r colofnau canlynol wedi''u diffinio fel y brif allwedd: {0} {1}
#XMSG
duplicateDPIDColumns=Ailenwi colofn darged.
#XMSG
duplicateDPIDDColumnsDesc1=Mae enw'r golofn darged wedi'i gadw ar gyfer colofn dechnegol. Rhowch enw gwahanol i arbed y tafluniad.
#XMSG:
targetAutoRenameDPID=Mae’r golofn darged wedi cael ei hailenwi.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Mae’r golofn darged wedi cael ei hailenwi i ganiatáu dyblygiadau o ffynhonnell ABAP sydd heb allweddi. Mae hyn oherwydd un o’r rhesymau canlynol: {0}{1}{2}Enw colofn wedi’i gadw{3}{2}Nodau nad oes modd delio â nhw{3}{2}Rhagddodiad wedi’i gadw{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle=Gosodiadau targed {0}
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle=Gosodiadau Ffynhonnell {0}
#XBUT
connectionSettingSave=Cadw
#XBUT
connectionSettingCancel=Canslo
#XBUT: Button to keep the object level settings
txtKeep=Cadw
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Trosysgrifo
#XFLD
targetConnectionThreadlimit=Terfyn Edefyn Targed ar gyfer Llwyth Cychwynnol (1-100)
#XFLD
connectionThreadLimit=Terfyn Edefyn Ffynhonnell ar gyfer Llwyth Cychwynnol (1-100)
#XFLD
maxConnection=Terfyn Edefyn Dyblygu (1-100)
#XFLD
kafkaNumberOfPartitions=Nifer y Rhaniadau
#XFLD
kafkaReplicationFactor=Ffactor Dyblygu
#XFLD
kafkaMessageEncoder=Amgodiwr Negeseuon
#XFLD
kafkaMessageCompression=Cywasgu Neges
#XFLD
fileGroupDeltaFilesBy=Grwpio Delta yn ôl
#XFLD
fileFormat=Math o Ffeil
#XFLD
csvEncoding=Amgodio CSV
#XFLD
abapExitLbl=Gadael ABAP
#XFLD
deltaPartition=Cyfrif Edefyn Gwrthrych ar gyfer Llwythau Delta (1-10)
#XFLD
clamping_Data=Wedi Methu Tocio'r Data
#XFLD
fail_On_Incompatible=Data anghydnaws wedi methu
#XFLD
maxPartitionInput=Uchafswm Nifer y Cyfranogwyr
#XFLD
max_Partition=Diffiniwch Uchafswm Nifer y Cyfranogwyr
#XFLD
include_SubFolder=Cynnwys Is-ffolderi
#XFLD
fileGlobalPattern=Patrwm Byd-eang ar gyfer Enw'r Ffeil
#XFLD
fileCompression=Cywasgu Ffeil
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Amffinydd Ffeil
#XFLD
fileIsHeaderIncluded=Pennyn Ffeil
#XFLD
fileOrient=Cyfeiriad
#XFLD
gbqWriteMode=Modd Ysgrifennu
#XFLD
suppressDuplicate=Atal Rhai Dyblyg
#XFLD
apacheSpark=Galluogi Cydnawsedd Apache Spark
#XFLD
clampingDatatypeCb=Clampio Mathau o Ddata Pwynt Arnofio Degol
#XFLD
overwriteDatasetSetting=Disodli Gosodiadau Targed ar Lefel y Gwrthrych
#XFLD
overwriteSourceDatasetSetting=Disodli Gosodiadau Ffynhonnell ar Lefel y Gwrthrych
#XMSG
kafkaInvalidConnectionSetting=Rhowch y rhif rhwng {0} a {1}
#XMSG
MinReplicationThreadErrorMsg=Rhowch rif sy’n fwy na {0}.
#XMSG
MaxReplicationThreadErrorMsg=Rhowch rif sy’n is na {0}.
#XMSG
DeltaThreadErrorMsg=Rhowch werth rhwng 1 a 10.
#XMSG
MaxPartitionErrorMsg=Rhowch werth sydd rhwng 1 <= x <= 2147483647. Y gwerth diofyn yw 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Rhowch gyfanrif rhwng {0} a {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Defnyddio Ffactor Dyblygu'r Brocer
#XFLD
serializationFormat=Fformat Cyfresu
#XFLD
compressionType=Math o Gywasgu
#XFLD
schemaRegistry=Defnyddio Cofrestr Sgema
#XFLD
subjectNameStrat=Strategaeth Enw Gwrthrych
#XFLD
compatibilityType=Math o Gydnawsedd
#XFLD
confluentTopicName=Enw'r Pwnc
#XFLD
confluentRecordName=Enw'r Cofnod
#XFLD
confluentSubjectNamePreview=Rhagolwg o Enw Gwrthrych
#XMSG
serializationChangeToastMsgUpdated2=Mae'r fformat cyfresu wedi newid i JSON gan nad yw'r gofrestr sgema wedi'i galluogi. I newid y fformat cyfresu yn ôl i AVRO, rhaid i chi alluogi'r gofrestr sgema yn gyntaf.
#XBUT
confluentTopicNameInfo=Mae enw'r pwnc yn cael ei seilio bob amser ar enw gwrthrych targed. Gallwch ei newid drwy ailenwi'r gwrthrych targed.
#XMSG
emptyRecordNameValidationHeaderMsg=Rhowch enw cofnod.
#XMSG
emptyPartionHeader=Rhowch nifer y rhaniadau.
#XMSG
invalidPartitionsHeader=Rhowch nifer dilys o raniadau.
#XMSG
invalidpartitionsDesc=Rhowch rif rhwng 1 a 200,000.
#XMSG
emptyrFactorHeader=Rhowch ffactor dyblygu.
#XMSG
invalidrFactorHeader=Rhowch ffactor dyblygu dilys.
#XMSG
invalidrFactorDesc=Rhowch rif rhwng 1 a 32,767
#XMSG
emptyRecordNameValidationDescMsg=Os bydd y fformat cyfresu "AVRO" yn cael ei ddefnyddio, dim ond y nodau canlynol sy''n gydnaws:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(tanlinell)
#XMSG
validRecordNameValidationHeaderMsg=Rhowch enw cofnod dilys.
#XMSG
validRecordNameValidationDescMsgUpdated=Oherwydd fod fformat cyfresu "AVRO" wedi'i ddefnyddio, dim ond llythrennau a rhifau (A-Z, a-z, 0-9) a thanlinellau (_) y dylai gael eu cynnwys yn enw'r cofnod. Rhaid dechrau gyda llythyren neu rif, neu danlinell.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=Mae modd gosod y "Cyfrif Edefyn Gwrthrych ar gyfer Llwythau Delta" cyn gynted â bod gan un neu ragor o wrthrychau'r math o lwyth "Cychwynnol a Delta".
#XMSG
invalidTargetName=Enw colofn annilys
#XMSG
invalidTargetNameDesc=Dim ond llythrennau a rhifau (A-Z, a-z, 0-9) a thanlinellau (_) y dylai gael eu cynnwys yn enw'r golofn darged.
#XFLD
consumeOtherSchema=Defnyddio Fersiynau Sgema Eraill
#XFLD
ignoreSchemamissmatch=Anwybyddu'r Anghysondeb rhwng Sgema
#XFLD
confleuntDatatruncation=Wedi Methu Tocio'r Data
#XFLD
isolationLevel=Lefel Ynysu
#XFLD
confluentOffset=Pwynt Cychwyn
#XFLD
signavioGroupDeltaFilesByText=Dim
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=Na
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=Na

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Tafluniadau
#XBUT
txtAdd=Ychwanegu
#XBUT
txtEdit=Golygu
#XMSG
transformationText=Ychwanegwch dafluniad i osod hidlydd neu fapiad.
#XMSG
primaryKeyRequiredText=Dewiswch brif allwedd gyda Sgema Ffurfweddu.
#XFLD
lblSettings=Gosodiadau
#XFLD
lblTargetSetting={0}: Gosodiadau Targed
#XMSG
@csvRF=Dewiswch y ffeil sy'n cynnwys y diffiniad sgema rydych chi am ei ddefnyddio ar gyfer pob ffeil yn y ffolder.
#XFLD
lblSourceColumns=Colofnau Ffynhonnell
#XFLD
lblJsonStructure=Strwythur JSON
#XFLD
lblSourceSetting={0}: Gosodiadau Ffynhonnell
#XFLD
lblSourceSchemaSetting={0}: Gosodiadau Sgema Ffynhonnell
#XBUT
messageSettings=Gosodiadau Negeseuon
#XFLD
lblPropertyTitle1=Priodweddau Gwrthrych
#XFLD
lblRFPropertyTitle=Priodweddau Llif Dyblygu
#XMSG
noDataTxt=Does dim colofnau i'w dangos ar hyn o bryd.
#XMSG
noTargetObjectText=Does dim gwrthrych targed wedi'i ddewis.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Colofnau Targed
#XMSG
searchColumns=Chwilio Colofnau
#XTOL
cdcColumnTooltip=Colofn ar gyfer Cipio Delta
#XMSG
sourceNonDeltaSupportErrorUpdated=Nid yw’r gwrthrych ffynhonnell yn gallu delio â chipio delta.
#XMSG
targetCDCColumnAdded=Mae 2 golofn darged wedi’u hychwanegu ar gyfer cipio delrta.
#XMSG
deltaPartitionEnable=Terfyn Edefyn Gwrthrych ar gyfer Llwythi Delta wedi'i ychwanegu i'r gosodiadau ffynhonnell.
#XMSG
attributeMappingRemovalTxt=Wrthi'n tynnu mapiau annilys nad oes modd delio â nhw ar gyfer gwrthrych targed newydd.
#XMSG
targetCDCColumnRemoved=Cafodd 2 o’r colofnau targed a ddefnyddiwyd ar gyfer cipio delta eu tynnu.
#XMSG
replicationLoadTypeChanged=Math o lwyth wedi’i newid i “Cychwynnol a Delta”.
#XMSG
sourceHDLFLoadTypeError=Newidiwch fath o lwyth i “Cychwynnol a Delta”.
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=I ddyblygu gwrthrych o gysylltiad ffynhonnell gyda'r math o gysylltiad SAP HANA Cloud, Ffeiliau Llyn Data i SAP Datasphere, rhaid i chi ddefnyddio'r math llwyth "cychwynnol a delta".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Ewch ati i alluogi Cipio Delta.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=I ddyblygu gwrthrych o gysylltiad ffynhonnell gyda'r math o gysylltiad SAP HANA Cloud, Ffeiliau Llyn Data i SAP Datasphere, rhaid i chi alluogi cipio delta.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Newidiwch Wrthrych Targed.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Does dim modd defnyddio gwrthrych targed gan fod cipio delta wedi'i analluogi. Gallwch naill ai ailenwi'r gwrthrych targed ( sy'n caniatáu creu gwrthrych newydd gyda chipio delta) neu ei fapio i wrthrych sy'n bodoli lle mae cipio delta wedi'i alluogi.
#XMSG
deltaPartitionError=Rhowch gyfrif edefyn gwrthrych dilys ar gyfer llwythau delta.
#XMSG
deltaPartitionErrorDescription=Rhowch werth rhwng 1 a 10.
#XMSG
deltaPartitionEmptyError=Rhowch gyfrif edefyn gwrthrych ar gyfer llwythau delta.
#XFLD
@lblColumnDescription=Disgrifiad
#XMSG
@lblColumnDescriptionText1=At ddibenion technegol - trin cofnodion dyblyg sy'n cael eu hachosi gan broblemau yn ystod y broses o ddyblygu gwrthrychau ffynhonnell sy'n seiliedig ar ABAP ac sydd heb allwedd gynradd.
#XFLD
storageType=Lle Storio
#XFLD
skipUnmappedColLbl=Sgipio Colofnau Heb eu Mapio
#XFLD
abapContentTypeLbl=Math o gynnwys
#XFLD
autoMergeForTargetLbl=Cyfuno Data yn Awtomatig
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Cyffredinol
#XFLD
lblBusinessName=Enw Busnes
#XFLD
lblTechnicalName=Enw Technegol
#XFLD
lblPackage=Pecyn
#XFLD
statusPanel=Statws Rhedeg
#XBTN: Schedule dropdown menu
SCHEDULE=Amserlen
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Golygu Amserlen
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Dileu Amserlen
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Creu Amserlen
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Proses wirio dilysiad amserlen wedi methu
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Does dim modd creu amserlen oherwydd bod y llif dyblygu yn cael ei osod ar hyn o bryd.{0}Arhoswch nes bydd y llif dyblygu wedi cael ei osod.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Ar gyfer llifau dyblygu sy'n cynnwys gwrthrychau sydd â math o lwyth "Cychwynnol a Delta", does dim modd creu amserlen.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Ar gyfer llifau dyblygu sy'n cynnwys gwrthrychau sydd â math o lwyth "Cychwynnol a Delta/Delta yn Unig", does dim modd creu amserlen.
#XFLD : Scheduled popover
SCHEDULED=Wedi'i Amserlennu
#XFLD
CREATE_REPLICATION_TEXT=Creu Llif Dyblygu
#XFLD
EDIT_REPLICATION_TEXT=Golygu Llif Dyblygu
#XFLD
DELETE_REPLICATION_TEXT=Dileu Llif Dyblygu
#XFLD
REFRESH_FREQUENCY=Amlder
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Does dim modd gosod y llif dyblygu oherwydd nad yw''r amserlen bresennol {0} yn gallu delio â math o lwyth "Cychwynnol a Delta".{0}{0}I osod y llif dyblygu, rhaid i chi osod mathau llwyth pob gwrthrych {0} i "Cychwynnol yn unig". Neu, gallwch ddileu''r amserlen, ailosod llif dyblygu  {0} a dechrau proses rhedeg newydd. Mae hyn yn arwain at broses rhedeg heb ddiwedd{0}, sydd hefyd yn gallu delio â gwrthrychau sydd â math llwyth "Cychwynnol a Delta".
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Does dim modd gosod y llif dyblygu oherwydd nad yw''r amserlen bresennol {0} yn gallu delio â math o lwyth "Cychwynnol a Delta/Delta yn Unig".{0}{0}I osod y llif dyblygu, rhaid i chi osod mathau llwyth pob gwrthrych {0} i "Cychwynnol yn unig". Neu, gallwch ddileu''r amserlen, ailosod llif dyblygu  {0} a dechrau proses rhedeg newydd. Mae hyn yn arwain at broses rhedeg heb ddiwedd{0}, sydd hefyd yn gallu delio â gwrthrychau sydd â math llwyth "Cychwynnol a Delta/Delta yn Unig".
#XMSG
SCHEDULE_EXCEPTION=Wedi methu cael manylion amserlen
#XFLD: Label for frequency column
everyLabel=Pob
#XFLD: Plural Recurrence text for Hour
hoursLabel=Oriau
#XFLD: Plural Recurrence text for Day
daysLabel=Diwrnod
#XFLD: Plural Recurrence text for Month
monthsLabel=Mis
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Munud
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Wedi methu cael gwybodaeth am bosibilrwydd amserlennu.
#XFLD :Paused field
PAUSED=Wedi'i Rewi
#XMSG
navToMonitoring=Agor yn y Monitor Llifau
#XFLD
statusLbl=Statws
#XFLD
lblLastRunExecuted=Dechrau Rhediad Diwethaf
#XFLD
lblLastExecuted=Rhediad Diwethaf
#XFLD: Status text for Completed
statusCompleted=Wedi Cwblhau
#XFLD: Status text for Running
statusRunning=Yn Rhedeg
#XFLD: Status text for Failed
statusFailed=Wedi Methu
#XFLD: Status text for Stopped
statusStopped=Wedi'i Stopio
#XFLD: Status text for Stopping
statusStopping=Wrthi'n Stopio
#XFLD: Status text for Active
statusActive=Gweithredol
#XFLD: Status text for Paused
statusPaused=Wedi'i Rewi
#XFLD: Status text for not executed
lblNotExecuted=Heb ei Redeg
#XFLD
messagesSettings=Gosodiadau Negeseuon
#XTOL
@validateModel=Negeseuon Dilysu
#XTOL
@hierarchy=Hierarchaeth
#XTOL
@columnCount=Nifer y Colofnau
#XMSG
VAL_PACKAGE_CHANGED=Rydych chi wedi neilltuo’r gwrthrych hwn i’r pecyn "{1}". Cliciwch "Cadw" i gadarnhau a dilysu’r newid hwn. Sylwch nad oes modd dadwneud neilltuo i becyn yn y golygydd hwn ar ôl i chi gadw.
#XMSG
MISSING_DEPENDENCY=Does dim modd datrys dibyniaethau gwrthrych "{0}" ym mhecyn "{1}".
#XFLD
deltaLoadInterval=Cyfnod Llwyth Delta
#XFLD
lblHour=Oriau (0-24)
#XFLD
lblMinutes=Munudau (0-59)
#XMSG
maxHourOrMinErr=Rhowch werth rhwng 0 a {0}
#XMSG
maxDeltaInterval=Gwerth mwyaf cyfnod llwyth delta yw 24 awr.{0}Newidiwch y gwerth munud neu''r gwerth awr yn unol â hynny.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Llwybr Cynhwysydd Targed
#XFLD
confluentSubjectName=Enw Pwnc
#XFLD
confluentSchemaVersion=Fersiwn y Sgema
#XFLD
confluentIncludeTechKeyUpdated=Cynnwys Allwedd Technegol
#XFLD
confluentOmitNonExpandedArrays=Hepgor Araeau sydd heb eu Hehangu
#XFLD
confluentExpandArrayOrMap=Ehangu Map neu Arae
#XCOL
confluentOperationMapping=Mapio Gweithrediadau
#XCOL
confluentOpCode=Cod Gweithredu
#XFLD
confluentInsertOpCode=Mewnosod
#XFLD
confluentUpdateOpCode=Diweddaru
#XFLD
confluentDeleteOpCode=Dileu
#XFLD
expandArrayOrMapNotSelectedTxt=Heb Ddewis
#XFLD
confluentSwitchTxtYes=Ie
#XFLD
confluentSwitchTxtNo=Na
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Gwall
#XTIT
executeWarning=Rhybudd
#XMSG
executeunsavederror=Cadwch eich llif dyblygu cyn ei redeg.
#XMSG
executemodifiederror=Mae newidiadau sydd heb eu cadw yn y llif dyblygu. Dylech gadw'r llif dyblygu.
#XMSG
executeundeployederror=Rhaid i chi osod eich llif dyblygu cyn y gallwch ei redeg.
#XMSG
executedeployingerror=Arhoswch nes ei fod wedi gorffen gosod.
#XMSG
msgRunStarted=Proses rhedeg wedi cychwyn
#XMSG
msgExecuteFail=Wedi methu rhedeg y llif dyblygu
#XMSG
titleExecuteBusy=Arhoswch.
#XMSG
msgExecuteBusy=Rydym yn paratoi eich data i redeg y llif dyblygu.
#XTIT
executeConfirmDialog=Rhybudd
#XMSG
msgExecuteWithValidations=Mae gwallau dilysu yn y llif dyblygu. Gall rhedeg y llif dyblygu arwain at fethu.
#XMSG
msgRunDeployedVersion=Mae newidiadau i’w gosod. Bydd y fersiwn diwethaf i gael ei gosod o’r llif dyblygu yn cael ei rhedeg. Ydych chi am fwrw ymlaen?
#XBUT
btnExecuteAnyway=Rhedeg Beth Bynnag
#XBUT
btnExecuteClose=Cau
#XBUT
loaderClose=Cau
#XTIT
loaderTitle=Wrthi'n Llwytho
#XMSG
loaderText=Wrthi'n cyrchu manylion o'r gweinydd
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Does dim modd cychwyn y llif dyblygu i’r cysylltiad targed hwn sydd ddim yn SAP
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=gan nad oes swm allbwn ar gael ar gyfer y mis hwn
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Gall gweinydd gynyddu’r blociau allbwn premiwm ar gyfer y tenant hwn,
#XMSG
premiumOutBoundRFAdminErrMsgPart2=gan sicrhau bod swm allbwn ar gael ar gyfer y mis hwn.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Gwall
#XTIT
deployInfo=Gwybodaeth
#XMSG
deployCheckFailException=Eithriad yn ystod y gosod
#XMSG
deployGBQFFDisabled=Does dim modd gosod llifau dyblygu gyda chysylltiad targed i Google BigQuery ar hyn o bryd gan ein bod yn gwneud gwaith cynnal a chadw ar y nodwedd hon.
#XMSG
deployKAFKAFFDisabled=Does dim modd gosod llifau dyblygu gyda chysylltiad targed i Apache Kafka ar hyn o bryd gan ein bod yn gwneud gwaith cynnal a chadw ar y nodwedd hon.
#XMSG
deployConfluentDisabled=Does dim modd gosod llifau dyblygu gyda chysylltiad targed i Kafka Cyfunol ar hyn o bryd gan ein bod yn gwneud gwaith cynnal a chadw ar y nodwedd hon.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Ar gyfer y gwrthrychau targed canlynol, mae enwau tabl cipio delta eisoes yn cael eu defnyddio gan dablau eraill yn yr ystorfa: {0} Rhaid i chi ailenwi''r gwrthrychau targed hyn i sicrhau bod enwau tabl cipio delta cysylltiedig yn unigryw cyn i chi allu gosod y llif dyblygu.
#XMSG
deployDWCSourceFFDisabled=Does dim modd gosod llifau dyblygu sydd â SAP Datasphere fel eu ffynhonnell ar hyn o bryd gan ein bod yn gwneud gwaith cynnal a chadw ar y nodwedd hon.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Does dim modd gosod llifau dyblygu sy'n cynnwys tablau lleol wedi'u galluogi â delta fel gwrthrychau ffynhonnell ar hyn o bryd gan ein bod yn gwneud gwaith cynnal a chadw ar y nodwedd hon.
#XMSG
deployHDLFSourceFFDisabled=Does dim modd gosod llifau dyblygu sydd â chysylltiadau ffynhonnell â'r math o gysylltiad SAP HANA Cloud, Data Lake Files ar hyn o bryd gan ein bod yn gwneud gwaith cynnal a chadw.
#XMSG
deployObjectStoreAsSourceFFDisabled=Ar hyn o bryd, nid oes modd defnyddio llifau dyblygu sydd â darparwyr storfa cwmwl fel eu ffynhonnell.
#XMSG
deployConfluentSourceFFDisabled=Does dim modd gosod llifau dyblygu sydd â Kafka Cyfunol fel eu ffynhonnell ar hyn o bryd gan ein bod yn gwneud gwaith cynnal a chadw ar y nodwedd hon.
#XMSG
deployMaxDWCNewTableCrossed=Ar gyfer llifau dyblygu mawr, does dim modd eu "cadw a'u rhoi ar waith" ar un cynnig. Cadwch eich llifau dyblygu yn gyntaf ac yna eu rhoi ar waith.
#XMSG
deployInProgressInfo=Mae'r broses o osod eisoes ar waith.
#XMSG
deploySourceObjectInUse=Mae gwrthrychau ffynhonnell {0} eisoes yn cael eu defnyddio mewn llifau dyblygu {1}.
#XMSG
deployTargetSourceObjectInUse=Mae gwrthrychau ffynhonnell {0} eisoes yn cael eu defnyddio mewn llifau dyblygu {1}. Mae gwrthychau targed {2} eisoes yn cael eu defnyddio mewn llifau dyblygu {3}.
#XMSG
deployReplicationFlowCheckError=Gwall wrth ddilysu llif dyblygu: {0}
#XMSG
preDeployTargetObjectInUse=Mae''r gwrthrychau targed {0} eisoes yn cael eu defnyddio mewn llifau dyblygu {1}, ac ni allwch gael yr un gwrthrych targed mewn dau lif dyblygu gwahanol. Dewiswch wrthrych targed arall a rhowch gynnig arall arni.
#XMSG
runInProgressInfo=Mae'r llif dyblygu ar waith yn barod.
#XMSG
deploySignavioTargetFFDisabled=Nid yw'n bosibl defnyddio llifau dyblygu sydd â SAP Signavio fel eu targed oherwydd ein bod ni wrthi'n cynnal a chadw'r swyddogaeth hon.
#XMSG
deployHanaViewAsSourceFFDisabled=Does dim modd gosod llifau dyblygu sydd â gweddau fel gwrthrychau ffynhonnell ar gyfer y cysylltiad ffynhonnell dan sylw ar hyn o bryd. Rhowch gynnig arall arni yn nes ymlaen.
#XMSG
deployMsOneLakeTargetFFDisabled=Nid yw'n bosibl defnyddio llifau dyblygu sydd â MS OneLake fel eu targed oherwydd ein bod ni wrthi'n cynnal a chadw'r swyddogaeth hon.
#XMSG
deploySFTPTargetFFDisabled=Nid yw'n bosibl defnyddio llifau dyblygu sydd â SFTP fel eu targed oherwydd ein bod ni wrthi'n cynnal a chadw'r swyddogaeth hon.
#XMSG
deploySFTPSourceFFDisabled=Nid yw'n bosibl defnyddio llifau dyblygu sydd â SFTP fel eu ffynhonnell oherwydd ein bod ni wrthi'n cynnal a chadw'r swyddogaeth hon.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Enw Technegol
#XFLD
businessNameInRenameTarget=Enw Busnes
#XTOL
renametargetDialogTitle=Ailenwi Gwrthrych Targed
#XBUT
targetRenameButton=Ailenwi
#XBUT
targetRenameCancel=Canslo
#XMSG
mandatoryTargetName=Rhaid i chi roi enw.
#XMSG
dwcSpecialChar=_(underscore) yw’r unig nod arbennig a ganiateir.
#XMSG
dwcWithDot=Gall enw'r tabl targed gynnwys llythrennau Lladin, rhifau, tanlinellau (_), a chyfnodau (.). Rhaid i'r nod cyntaf fod yn llythyren, rhif, neu'n danlinell (nid cyfnod).
#XMSG
nonDwcSpecialChar=Y nodau arbennig a ganiateir yw _(underscore) -(hyphen) .(dot)
#XMSG
firstUnderscorePattern=Ddylai'r enw ddim dechrau gyda _(tanlinell)

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: Gweld Datganiad Creu Tabl SQL
#XMSG
sqlDialogMaxPKWarning=Yn Google BigQuery, mae modd delio ag uchafswm o 16 allwedd gynradd, ac mae gan y gwrthrych ffynhonnell fwy na hyn. Felly, nid oes allweddi cynradd wedi’u diffinio yn y datganiad hwn.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Mae gan un neu ragor o golofnau ffynhonnell fathau o ddata nad oes modd eu diffinio fel allweddi cynradd Google BigQuery. Felly, nid oes allweddi cynradd wedi’u diffinio yn yr achos hwn. Yn Google BigQuery, dim ond y mathau o ddata canlynol all fod ag allwedd gynradd: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Copïo a Chadw
#XBUT
closeDDL=Cau
#XMSG
copiedToClipboard=Wedi'i gopïo i'r clipfwrdd


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Does dim modd i wrthrych ''{0}'' fod yn rhan o gadwyn tasgau am nad oes diwedd iddo (mae''n cynnwys gwrthrychau sydd â math llwyth Cychwynnol a Delta/Delta yn Unig).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Does dim modd i wrthrych ''{0}'' fod yn rhan o gadwyn tasgau am nad oes diwedd iddo (mae''n cynnwys gwrthrychau sydd â math llwyth Cychwynnol a Delta).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Dim modd ychwanegu''r gwrthrych "{0}" at y gadwyn tasgau.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Mae rhai gwrthrychau targed heb eu cadw. Cadwch nhw eto. {0}{0} Mae ymddygiad y nodwedd hon wedi newid: Yn y gorffennol, dim ond pan roddwyd y llif dyblygu ar waith yr oedd gwrthrychau targed yn cael eu creu yn yr amgylchedd targed. {0} Erbyn hyn, mae''r gwrthrychau''n cael eu creu pan mae''r llif dyblygu yn cael ei gadw. Cafodd eich llif dyblygu ei greu cyn y newid hwn ac mae''n cynnwys gwrthrychau newydd.{0} Er mwyn sicrhau bod y gwrthrychau newydd wedi''u cynnwys yn gywir, mae angen i chi gadw''r llif dyblygu eto cyn ei roi ar waith.
#XMSG
confirmChangeContentTypeMessage=Rydych chi ar fin newid y math o gynnwys. Os gwnewch chi hynny, bydd yr holl dafluniadau presennol yn cael eu dileu.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Enw Pwnc
#XFLD
schemaDialogVersionName=Fersiwn y Sgema
#XFLD
includeTechKey=Cynnwys Allwedd Technegol
#XFLD
segementButtonFlat=Fflat
#XFLD
segementButtonNested=Wedi nythu
#XMSG
subjectNamePlaceholder=Chwilio am Enw'r Pwnc

#XMSG
@EmailNotificationSuccess=Wedi cadw ffurfweddiad hysbysiadau e-bost amser rhedeg.

#XFLD
@RuntimeEmailNotification=Hysbysiad Amser Rhedeg drwy E-bost

#XBTN
@TXT_SAVE=Cadw


