#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Flux de replicare

#XFLD: Edit Schema button text
editSchema=Editare schemă

#XTIT : Properties heading
configSchema=Configurare schemă

#XFLD: save changed button text
applyChanges=Aplicare modificări


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Selectare conexiune sursă
#XFLD
sourceContainernEmptyText=Selectare container
#XFLD
targetConnectionEmptyText=Selectare conexiune țintă
#XFLD
targetContainernEmptyText=Selectare container
#XFLD
sourceSelectObjectText=Selectare obiect sursă
#XFLD
sourceObjectCount=Obiecte sursă ({0})
#XFLD
targetObjectText=Obiecte țintă
#XFLD
confluentBrowseContext=Selectare context
#XBUT
@retry=Reîncercare
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Upgrade-ul tenantului este în desfășurare.

#XTOL
browseSourceConnection=Răsfoire conexiune sursă
#XTOL
browseTargetConnection=Răsfoire conexiune țintă
#XTOL
browseSourceContainer=Răsfoire container sursă
#XTOL
browseAndAddSourceDataset=Adăugare obiecte sursă
#XTOL
browseTargetContainer=Răsfoire container țintă
#XTOL
browseTargetSetting=Răsfoire setări țintă
#XTOL
browseSourceSetting=Răsfoire setări sursă
#XTOL
sourceDatasetInfo=Info
#XTOL
sourceDatasetRemove=Eliminare
#XTOL
mappingCount=Acesta reprezintă numărul total de mapări/expresii care nu sunt bazate pe nume.
#XTOL
filterCount=Acesta reprezintă numărul total de condiții de filtru.
#XTOL
loading=Încărcare...
#XCOL
deltaCapture=Captură delta
#XCOL
deltaCaptureTableName=Tabel de captură delta
#XCOL
loadType=Tip de încărcare
#XCOL
deleteAllBeforeLoading=Ștergere tot înainte de încărcare
#XCOL
transformationsTab=Proiecții
#XCOL
settingsTab=Setări

#XBUT
renameTargetObjectBtn=Redenumire obiect țintă
#XBUT
mapToExistingTargetObjectBtn=Mapare la obiect țintă existent
#XBUT
changeContainerPathBtn=Modificare cale container
#XBUT
viewSQLDDLUpdated=Vizualizare instrucțiune SQL Creare tabel
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Obiectul sursă nu suportă captura delta, dar obiectul țintă selectat are activată opțiunea de captură delta.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Obiectul țintă nu poate fi utilizat deoarece captura delta este activată,{0}în timp ce obiectul sursă nu suportă captura delta.{1}Puteți selecta alt obiect țintă care nu suportă captura delta.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Există deja un obiect țintă cu acest nume. Cu toate acestea, nu poate fi utilizat{0}deoarece captura delta este activată, în timp ce obiectul sursă nu{0}suportă captura delta.{1}Puteți fie să introduceți numele unui obiect țintă existent care nu{0}suportă captura delta, fie să introduceți un nume care nu există încă.
#XBUT
copySQLDDLUpdated=Copiere instrucțiune SQL Creare tabel
#XMSG
targetObjExistingNoCDCColumnUpdated=Tabelele existente în Google BigQuery trebuie să includă următoarele coloane pentru modificarea capturii de date (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Următoarele obiecte sursă nu sunt suportate deoarece nu au o cheie primară sau utilizează o conexiune care nu îndeplinește condițiile pentru regăsirea cheii primare:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Consultați SAP KBA 3531135 pentru o posibilă soluție.
#XLST: load type list values
initial=Doar inițial
@emailUpdateError=Eroare la actualizare listă de notificări prin e-mail

#XLST
initialDelta=Inițial și delta

#XLST
deltaOnly=Doar delta
#XMSG
confluentDeltaLoadTypeInfo=Pentru sursa Confluent Kafka, este suportat doar tipul de încărcare inițial sau delta.
#XMSG
confirmRemoveReplicationObject=Confirmați că doriți să ștergeți replicarea?
#XMSG
confirmRemoveReplicationTaskPrompt=Această acțiune va șterge replicările existente. Doriți să continuați?
#XMSG
confirmTargetConnectionChangePrompt=Această acțiune va reseta conexiunea țintă, containerul țintă și va șterge toate obiectele țintă. Doriți să continuați?
#XMSG
confirmTargetContainerChangePrompt=Această acțiune va reseta containerul țintă și va șterge toate obiectele țintă existente. Doriți să continuați?
#XMSG
confirmRemoveTransformObject=Confirmați că doriți să ștergeți proiecția {0}?
#XMSG
ErrorMsgContainerChange=A apărut o eroare la modificare cale container.
#XMSG
infoForUnsupportedDatasetNoKeys=Următoarele obiecte sursă nu sunt suportate, deoarece nu au o cheie primară:
#XMSG
infoForUnsupportedDatasetView=Următoarele obiecte sursă de tip Imagini nu sunt suportate:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Următorul obiect sursă nu este suportat deoarece este o imagine SQL care conține parametri de intrare:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Următoarele obiecte sursă nu sunt suportate, deoarece extragerea este dezactivată pentru acestea:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Pentru conexiunile Confluent, singurele formate de serializare sunt AVRO și JSON. Următoarele obiecte nu sunt suportate deoarece utilizează un format de serializare diferit.
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Imposibil de obținut schema pentru următoarele obiecte. Selectați contextul corespunzător sau verificați configurarea registrului de schemă
#XTOL: warning dialog header on deleting replication task
deleteHeader=Ștergere
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Setarea „Ștergere tot înainte de încărcare” nu este suportată pentru Google BigQuery.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Setarea „Ștergere tot înainte” șterge și recreează obiectul (tema) înainte de fiecare replicare. Aceasta șterge, de asemenea, toate mesajele alocate.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Setarea „Ștergere tot înainte” nu este suportată pentru acest tip de țintă.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Nume tehnic
#XCOL
connBusinessName=Nume comercial
#XCOL
connDescriptionName=Descriere
#XCOL
connType=Tip
#XMSG
connTblNoDataFoundtxt=Nicio conexiune găsită
#XMSG
connectionError=A apărut o eroare la obținere conexiuni.
#XMSG
connectionCombinationUnsupportedErrorTitle=Combinarea conexiunii nu este suportată
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Replicarea din {0} în {1} nu este suportată în prezent.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Combinarea tipului de conexiune nu este suportată
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Replicarea de la o conexiune sursă cu tipul de conexiune Fișiere SAP HANA Cloud, lac de date, în {0} nu este suportată. Puteți replica doar în SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Selectare
#XBUT
containerCancelBtn=Anulare
#XTOL
containerSelectTooltip=Selectare
#XTOL
containerCancelTooltip=Anulare
#XMSG
containerContainerPathPlcHold=Cale container
#XFLD
containerContainertxt=Container
#XFLD
confluentContainerContainertxt=Context
#XMSG
infoMessageForSLTSelection=Doar /SLT/ID transfer în masă este permis drept container. Selectați un ID de transfer în masă sub SLT (dacă este disponibil) și faceți click pe Transmitere.
#XMSG
msgFetchContainerFail=A apărut o eroare la obținere date container.
#XMSG
infoMessageForSLTHidden=Această conexiune nu suportă foldere SLT, deci nu apar în lista de mai jos.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Selectați un container ce conține subfoldere.
#XMSG
sftpIncludeSubFolderText=Fals
#XMSG
sftpIncludeSubFolderTextNew=Nu

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Nicio mapare de filtru încă)
#XMSG
failToFetchRemoteMetadata=A apărut o eroare la obținere metadate.
#XMSG
failToFetchData=A apărut o eroare la obținere țintă existentă.
#XCOL
@loadType=Tip de încărcare
#XCOL
@deleteAllBeforeLoading=Ștergere tot înainte de încărcare

#XMSG
@loading=Încărcare...
#XFLD
@selectSourceObjects=Selectare obiecte sursă
#XMSG
@exceedLimit=Nu puteți importa mai mult de {0} obiecte simultan. Deselectați cel puțin {1} obiecte.
#XFLD
@objects=Obiecte
#XBUT
@ok=OK
#XBUT
@cancel=Anulare
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Următor
#XBUT
btnAddSelection=Adăugare selecție
#XTOL
@remoteFromSelection=Eliminare din selecție
#XMSG
@searchInForSearchField=Căutare în {0}

#XCOL
@name=Nume tehnic
#XCOL
@type=Tip
#XCOL
@location=Locație
#XCOL
@label=Nume comercial
#XCOL
@status=Stare

#XFLD
@searchIn=Căutare în:
#XBUT
@available=Disponibil
#XBUT
@selection=Selecție

#XFLD
@noSourceSubFolder=Tabele și imagini
#XMSG
@alreadyAdded=Deja prezent în diagramă
#XMSG
@askForFilter=Există mai mult de {0} elemente. Introduceți un șir de filtrare pentru a restrânge numărul de elemente.
#XFLD: success label
lblSuccess=Succes
#XFLD: ready label
lblReady=Pregătit
#XFLD: failure label
lblFailed=Nereușit
#XFLD: fetching status label
lblFetchingDetail=Obținere detalii

#XMSG Place holder text for tree filter control
filterPlaceHolder=Tastați textul pentru a filtra obiectele de nivel superior
#XMSG Place holder text for server search control
serverSearchPlaceholder=Tastați și apăsați pe Enter pentru a căuta
#XMSG
@deployObjects=Importare {0} obiecte...
#XMSG
@deployObjectsStatus=Numărul de obiecte care au fost importate: {0}. Numărul de obiecte care nu au putut fi importate: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Eroare la deschidere browser de registru local.
#XMSG
@openRemoteSourceBrowserError=Eroare la obținere obiecte sursă.
#XMSG
@openRemoteTargetBrowserError=Eroare la obținere obiecte țintă.
#XMSG
@validatingTargetsError=A apărut o eroare la validare ținte.
#XMSG
@waitingToImport=Gata de import

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Numărul maxim de obiecte a fost depășit. Selectați maximum 500 de obiecte pentru un flux de replicare.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Nume tehnic
#XFLD
sourceObjectBusinessName=Nume comercial
#XFLD
sourceNoColumns=Număr de coloane
#XFLD
containerLbl=Container

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Trebuie să selectați o conexiune sursă pentru fluxul de replicare.
#XMSG
validationSourceContainerNonExist=Trebuie să selectați un container pentru conexiunea sursă.
#XMSG
validationTargetNonExist=Trebuie să selectați o conexiune țintă pentru fluxul de replicare.
#XMSG
validationTargetContainerNonExist=Trebuie să selectați un container pentru conexiunea țintă.
#XMSG
validationTruncateDisabledForObjectTitle=Replicare la arhivele de obiecte.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Replicarea într-o arhivă cloud este posibilă numai dacă este setată opțiunea „Ștergere tot înainte de încărcare” sau dacă obiectul țintă nu există în țintă. {0}{0} Pentru a activa în continuare replicarea pentru obiectele pentru care nu este setată opțiunea „Ștergere tot înainte de încărcare”, asigurați-vă că obiectul țintă nu există în sistem înainte de a executa fluxul de replicare.
#XMSG
validationTaskNonExist=Trebuie să aveți cel puțin o replicare în fluxul de replicare.
#XMSG
validationTaskTargetMissing=Trebuie să aveți o țintă pentru replicarea cu aceeași sursă: {0}
#XMSG
validationTaskTargetIsSAC=Ținta selectată este un artefact SAC: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Ținta selectată nu este un tabel local suportat: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Există deja un obiect cu acest nume în țintă. Cu toate acestea, acest obiect nu poate fi utilizat ca obiect țintă pentru un flux de replicare în registrul local deoarece nu este un tabel local.
#XMSG
validateSourceTargetSystemDifference=Trebuie să selectați combinații diferite de conexiune sursă și țintă și container pentru fluxul de replicare.
#XMSG
validateDuplicateSources=cel puțin o replicare are nume de obiect sursă duplicate: {0}.
#XMSG
validateDuplicateTargets=cel puțin o replicare are nume de obiect țintă duplicate: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=Obiectul sursă {0} nu suportă captura delta, în timp ce obiectul țintă {1} o suportă. Trebuie să eliminați replicarea.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Trebuie să selectați tipul de încărcare "Inițial și delta" pentru replicarea cu numele de obiect țintă {0}.
#XMSG
validationAutoRenameTarget=Coloanele țintă au fost redenumite.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Proiecția automată a fost adăugată și următoarele coloane țintă au fost redenumite pentru a permite replicarea în țintă:{1}{1} {0} {1}{1}Acest lucru apare din cauza unuia dintre următoarele motive:{1}{1}{2} Caractere nesuportate{1}{2} Prefix rezervat
#XMSG
validationAutoRenameTargetDescriptionUpdated=Proiecția automată a fost adăugată și următoarele coloane țintă au fost redenumite pentru a permite replicarea în Google BigQuery:{1}{1} {0} {1}{1}Acest lucru apare din cauza unuia dintre următoarele motive:{1}{1}{2} Nume coloană rezervată{1}{2} Caractere nesuportate{1}{2} Prefix rezervat
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Proiecția automată a fost adăugată și următoarele coloane țintă au fost redenumite pentru a permite replicările în Confluent:{1}{1} {0} {1}{1}Acest lucru apare din cauza unuia dintre următoarele motive:{1}{1}{2} Nume coloană rezervată{1}{2} Caractere nesuportate{1}{2} Prefix rezervat
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Proiecția automată a fost adăugată și următoarele coloane țintă au fost redenumite pentru a permite replicările în țintă:{1}{1} {0} {1}{1}Acest lucru apare din cauza unuia dintre următoarele motive:{1}{1}{2} Nume coloană rezervată{1}{2} Caractere nesuportate{1}{2} Prefix rezervat
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Obiectul țintă a fost redenumit.
#XMSG
autoRenameInfoDesc=Obiectul țintă a fost redenumit, deoarece nu conținea caractere suportate. Sunt suportate doar următoarele caractere:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(punct){0}{1}_(underscore){0}{1}-(cratimă)
#XMSG
validationAutoTargetTypeConversion=Tipurile de date țintă au fost modificate.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Pentru următoarele coloane țintă, tipurile de date țintă au fost modificate deoarece tipurile de date sursă nu sunt suportate în Google BigQuery:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Pentru următoarele coloane țintă, tipurile de date țintă au fost modificate deoarece tipurile de date sursă nu sunt suportate în conexiunea țintă:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Nume de coloană țintă prescurtate.
#XMSG
validationMaxCharLengthGBQTargetDescription=În Google BigQuery, numele de coloane pot utiliza maximum 300 de caractere. Utilizați o proiecție pentru a prescurta următoarele nume de coloane țintă:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Cheile primare nu vor fi create.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=În Google BigQuery, este suportat un număr maxim de 16 chei primare, dar obiectul sursă are un număr mai mare de chei primare. Niciuna dintre cheile primare nu va fi creată în obiectul țintă.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Una sau mai multe coloane sursă au tipuri de date care nu pot fi definite drept chei primare în Google BigQuery. Niciuna dintre cheile primare nu va fi creată în obiectul țintă.{0}{0}Următoarele tipuri de date țintă sunt compatibile cu tipurile de date Google BigQuery pentru care poate fi definită o cheie primară:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Definiți una sau mai multe coloane ca o cheie primară.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Trebuie să definiți una sau mai multe coloane drept cheie primară utilizând dialogul de schemă sursă pentru aceasta.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Definiți una sau mai multe coloane ca o cheie primară.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Trebuie să definiți una sau mai multe coloane drept cheie primară care să corespundă cu restricțiile de cheie primară pentru obiectul dvs. sursă. Navigați la Configurare schemă în proprietățile dvs. de obiect sursă pentru a face acest lucru.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Introduceți o valoare de partiție maximă valabilă.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Valoarea de partiție maximă trebuie să fie ≥ 1 și ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Definiți una sau mai multe coloane ca o cheie primară.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Pentru a replica un obiect, trebuie să definiți una sau mai multe coloane țintă ca o cheie primară. Utilizați o proiecție în acest scop.
#XMSG
validateHDLFNoPKExistingDatasetError=Definiți una sau mai multe coloane ca o cheie primară.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Pentru a replica datele într-un obiect țintă existent, acesta trebuie să aibă una sau mai multe coloane care sunt definite drept cheie primară. {0} Aveți următoarele opțiuni pentru a defini una sau mai multe coloane drept cheie primară: {0} {1} Utilizați editorul de tabel local pentru a modifica obiectul țintă existent. Apoi reîncărcați fluxul de replicare.{0}{1} Redenumiți obiectul țintă în fluxul de replicare. Acest lucru va crea un obiect nou de îndată ce este lansată o execuție. După redenumire, puteți defini una sau mai multe coloane drept cheie primară într-o proiecție.{0}{1} Mapați obiectul la un alt obiect țintă existent în care una sau mai multe coloane sunt deja definite drept cheie primară.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Ținta selectată există deja în registru: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Numele de tabel de captură delta sunt deja utilizate de alte tabele din registru: {0} Trebuie să redenumiți aceste obiecte țintă pentru a vă asigura că numele de tabel de captură delta asociate sunt univoce înainte să puteți salva fluxul de replicare.
#XMSG
validateConfluentEmptySchema=Definire schemă
#XMSG
validateConfluentEmptySchemaDescUpdated=Tabelul sursă nu are o schemă. Alegeți Configurare schemă pentru a defini una
#XMSG
validationCSVEncoding=Codificare CSV nevalabilă
#XMSG
validationCSVEncodingDescription=Codificarea CSV a sarcinii nu este valabilă.
#XMSG
validateConfluentEmptySchema=Selectați un tip date țintă compatibil
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Selectați un tip date țintă compatibil
#XMSG
globalValidateTargetDataTypeDesc=A apărut o eroare cu mapările de coloană. Mergeți la Proiecție și asigurați-vă că toate coloanele sursă sunt mapate cu o coloană unică, cu o coloană care are un tip de date compatibil și că toate expresiile definite sunt valabile.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Nume de coloane duplicate.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Numele de coloane duplicate nu sunt suportate. Utilizați dialogul de proiecție pentru a le corecta. Următoarele obiecte țintă au nume de coloane duplicate: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Nume de coloane duplicate.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Numele de coloane duplicate nu sunt suportate. Următoarele obiecte țintă au nume de coloane duplicate: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Pot exista inconsistențe în date.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Tipul de încărcare Doar delta nu va lua în considerare modificările efectuate în sursă între ultima salvare și următoarea execuție.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Modificați tipul de încărcare la "Inițial".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Replicarea obiectelor pe bază de ABAP care nu au cheie primară este posibilă doar pentru tipul de încărcare "Doar inițial".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Dezactivați captura Delta.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Pentru a replica un obiect care nu are o cheie primară utilizând tipul de conexiune sursă ABAP, trebuie mai întâi să dezactivați capturarea Delta pentru acest tabel.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Modificați obiectul țintă.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Obiectul țintă nu poate fi utilizat deoarece capturarea Delta este activată. Puteți fie redenumi obiectul țintă și apoi opri capturarea Delta pentru obiectul nou (redenumit), fie mapa obiectul sursă la un obiect țintă pentru care capturarea Delta este dezactivată.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Modificați obiectul țintă.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Obiectul țintă nu poate fi utilizat deoarece nu are coloana tehnică obligatorie __load_package_id. Puteți redenumi obiectul țintă utilizând un nume care nu există încă. Sistemul creează apoi un obiect nou care are aceeași definiție ca și obiectul sursă și conține coloana tehnică. Alternativ, puteți mapa obiectul țintă la un obiect existent care are coloana tehnică obligatorie (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Modificați obiectul țintă.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Obiectul țintă nu poate fi utilizat deoarece nu are coloana tehnică obligatorie __load_record_id. Puteți redenumi obiectul țintă utilizând un nume care nu există încă. Sistemul creează apoi un obiect nou care are aceeași definiție ca și obiectul sursă și conține coloana tehnică. Alternativ, puteți mapa obiectul țintă la un obiect existent care are coloana tehnică obligatorie (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Modificați obiectul țintă.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Obiectul țintă nu poate fi utilizat pentru că tipul de date al coloanei sale tehnice column __load_record_id nu este "string(44)". Puteți redenumi obiectul țintă utilizând un nume care nu există încă. Sistemul creează apoi un obiect nou care are aceeași definiție ca și obiectul sursă și, prin urmare, tipul de date corect. Alternativ, puteți mapa obiectul țintă la un obiect existent care are coloana tehnică obligatorie (__load_record_id) cu tipul de date corect.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Modificați obiectul țintă.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Obiectul țintă nu poate fi utilizat deoarece are o cheie primară, în timp ce obiectul sursă nu are niciuna. Puteți redenumi obiectul țintă utilizând un nume care nu există încă. Sistemul creează apoi un obiect nou care are aceeași definiție ca și obiectul sursă și, prin urmare, nicio cheie primară. Alternativ, puteți mapa obiectul țintă la un obiect existent care nu are coloana tehnică obligatorie (__load_package_id) și nu are o cheie primară.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Modificați obiectul țintă.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Obiectul țintă nu poate fi utilizat deoarece are o cheie primară, în timp ce obiectul sursă nu are niciuna. Puteți redenumi obiectul țintă utilizând un nume care nu există încă. Sistemul creează apoi un obiect nou care are aceeași definiție ca și obiectul sursă și, prin urmare, nicio cheie primară. Alternativ, puteți mapa obiectul țintă la un obiect existent care nu are coloana tehnică obligatorie (__load_record_id) și nu are o cheie primară.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Modificați obiectul țintă.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Obiectul țintă nu poate fi utilizat pentru că tipul de date al coloanei sale tehnice obligatorii __load_package_id nu este "binary(>=256)". Puteți redenumi obiectul țintă utilizând un nume care nu există încă. Sistemul creează apoi un obiect nou care are aceeași definiție ca și obiectul sursă și, prin urmare, nicio cheie primară. Alternativ, puteți mapa obiectul țintă la un obiect existent care nu are coloana tehnică obligatorie (__load_package_id) și nu are o cheie primară.
#XMSG
validationAutoRenameTargetDPID=Coloanele țintă au fost redenumite.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Eliminați obiectul sursă.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Obiectul sursă nu are o coloană cheie, lucru care nu este suportat în acest context.
#XMSG
validationAutoRenameTargetDPIDDescription=O proiecție automată a fost adăugată și următoarele coloane țintă au fost redenumite pentru a permite replicarea dintr-o sursă ABAP fără chei:{1}{1} {0} {1}{1}Acest lucru din cauza unuia dintre următoarele motive:{1}{1}{2} Nume coloană rezervată{1}{2} Caractere nesuportate{1}{2} Prefix rezervat
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Replicare la {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Salvarea și implementarea fluxurilor care au {0} drept țintă nu este posibilă în prezent deoarece efectuăm întreținerea acestei funcții.
#XMSG
TargetColumnSkippedLTF=Coloana țintă a fost omisă.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Coloana țintă a fost omisă din cauza tipului de date nesuportat. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Coloană de timp drept cheie primară.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Obiectul sursă are o coloană de timp drept cheie primară, lucru care nu este suportat în acest context.
#XMSG
validateNoPKInLTFTarget=Cheie primară lipsă.
#XMSG
validateNoPKInLTFTargetDescription=Cheia primară nu este definită în țintă, ceea ce nu este suportat în acest context.
#XMSG
validateABAPClusterTableLTF=Tabel cluster ABAP.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Obiectul sursă este un tabel cluster ABAP, lucru care nu este suportat în acest context.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Se pare că încă nu ați adăugat date.
#YINS
welcomeText2=Pentru a lansa fluxul dvs. de replicare, selectați o conexiune și un obiect sursă din partea stângă.

#XBUT
wizStep1=Selectare conexiune sursă
#XBUT
wizStep2=Selectare container sursă
#XBUT
wizStep3=Adăugare obiecte sursă

#XMSG
limitDataset=Numărul maxim de obiecte a fost atins. Eliminați obiectele existente pentru a adăuga unele noi sau creați un flux de replicare nou.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Fluxul de replicare la această conexiune țintă non-SAP nu poate fi lansat, deoarece nu există niciun volum de ieșire disponibil pentru această lună.
#XMSG
premiumOutBoundRFAdminWarningMsg=Un administrator poate crește blocările de ieșire premium pentru acest tenant, punând la dispoziție volumul de ieșire pentru această lună.
#XMSG
messageForToastForDPIDColumn2=Coloană nouă adăugată la țintă pentru {0} obiecte - necesară pentru gestionarea înregistrărilor duplicate în conexiune cu obiectele sursă pe bază de ABAP care nu au o cheie primară.
#XMSG
PremiumInboundWarningMessage=În funcție de numărul de fluxuri de replicare și de volumul de date de replicat, resursele SAP HANA {0} necesare pentru replicarea datelor în {1} pot depăși capacitatea disponibilă pentru tenantul dvs.
#XMSG
PremiumInboundWarningMsg=În funcție de numărul de fluxuri de replicare și de volumul de date de replicat, {0}resursele SAP HANA necesare pentru replicarea datelor în "{1}" pot depăși capacitatea disponibilă pentru tenantul dvs.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Introduceți un nume de proiecție.
#XMSG
emptyTargetColumn=Introduceți un nume de coloană țintă.
#XMSG
emptyTargetColumnBusinessName=Introduceți o coloană țintă Nume comercial.
#XMSG
invalidTransformName=Introduceți un nume de proiecție.
#XMSG
uniqueColumnName=Redenumiți coloana țintă.
#XMSG
copySourceColumnLbl=Copiați coloane din obiectul sursă
#XMSG
renameWarning=Asigurați-vă că alegeți un nume univoc la redenumirea tabelului țintă. Dacă tabelul cu noul nume există deja în spațiu, va utiliza definiția acelui tabel.

#XMSG
uniqueColumnBusinessName=Redenumiți numele comercial al coloanei țintă.
#XMSG
uniqueSourceMapping=Selectați o coloană sursă diferită.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=Coloana sursă {0} este deja utilizată de următoarele coloane țintă:{1}{1}{2}{1}{1} Pentru această coloană țintă sau pentru celelalte coloane țintă, selectați o coloană sursă care nu este deja utilizată pentru a salva proiecția.
#XMSG
uniqueColumnNameDescription=Numele de coloană țintă pe care l-ați introdus există deja. Pentru a putea salva proiecția, trebuie să introduceți un nume de coloană univoc.
#XMSG
uniqueColumnBusinessNameDesc=Numele comercial de coloană țintă există deja. Pentru a salva proiecția, trebuie să introduceți un nume comercial de coloană univoc.
#XMSG
emptySource=Selectați o coloană sursă sau introduceți o constantă.
#XMSG
emptySourceDescription=Pentru a crea o intrare de mapare valabilă, trebuie să selectați o coloană sursă sau să introduceți o valoare de constantă.
#XMSG
emptyExpression=Definiți maparea.
#XMSG
emptyExpressionDescription1=Fie selectați coloana sursă la care doriți să mapați coloana țintă, fie selectați caseta de marcaj din coloana {0}Funcții/constante{1}. {2} {2}Funcțiile sunt introduse automat în conformitate cu tipul de date țintă. Valorile constante pot fi introduse manual.
#XMSG
numberExpressionErr=Introduceți un număr.
#XMSG
numberExpressionErrDescription=Ați selectat un tip date numeric. Aceasta înseamnă că puteți introduce doar numerale, plus virgula zecimală, dacă este aplicabilă. Nu utilizați ghilimelele simple.
#XMSG
invalidLength=Introduceți o valoare de lungime valabilă.
#XMSG
invalidLengthDescription=Lungimea tipului de date trebuie să fie egală cu sau mai mare decât lungimea coloanei sursă și poate fi cuprinsă între 1 și 5000.
#XMSG
invalidMappedLength=Introduceți o valoare de lungime valabilă.
#XMSG
invalidMappedLengthDescription=Lungimea tipului de date trebuie să fie egală cu sau mai mare decât lungimea coloanei sursă {0} și poate fi cuprinsă între 1 și 5000.
#XMSG
invalidPrecision=Introduceți o valoare de precizie valabilă.
#XMSG
invalidPrecisionDescription=Precizia definește numărul total de cifre. Scara definește numărul de cifre după virgula zecimală și poate fi cuprins între 0 și precizie.{0}{0} Exemple: {0}{1} Precizia 6, scara 2 corespunde cu numere precum 1234,56.{0}{1} Precizia 6, scara 6 corespunde cu numere precum 0,123546.{0} {0} Precizia și scara pentru țintă trebuie să fie compatibile cu precizia și scara pentru sursă, pentru ca toate cifrele din sursă să corespundă în câmpul țintă. De exemplu, dacă aveți precizia 6 și scara 2 în sursă (și, în consecință, cifre diferite de 0 înainte de virgula zecimală), nu puteți avea precizia 6 și scara 6 în țintă.
#XMSG
invalidPrimaryKey=Introduceți cel puțin o cheie primară.
#XMSG
invalidPrimaryKeyDescription=Cheia primară nu este definită pentru această schemă.
#XMSG
invalidMappedPrecision=Introduceți o valoare de precizie valabilă.
#XMSG
invalidMappedPrecisionDescription1=Precizia definește numărul total de cifre. Scara definește numărul de cifre după virgula zecimală și poate fi cuprinsă între 0 și precizie.{0}{0} Exemple:{0}{1} Precizia 6, scara 2 corespunde cu numere precum 1234,56.{0}{1} Precizia 6, scara 6 corespunde cu numere precum 0,123546.{0}{0}Precizia tipului de date trebuie să fie egală cu sau mai mare decât precizia sursei ({2}).
#XMSG
invalidScale=Introduceți o valoare de scară valabilă.
#XMSG
invalidScaleDescription=Precizia definește numărul total de cifre. Scara definește numărul de cifre după virgula zecimală și poate fi cuprins între 0 și precizie.{0}{0} Exemple: {0}{1} Precizia 6, scara 2 corespunde cu numere precum 1234,56.{0}{1} Precizia 6, scara 6 corespunde cu numere precum 0,123546.{0} {0} Precizia și scara pentru țintă trebuie să fie compatibile cu precizia și scara pentru sursă, pentru ca toate cifrele din sursă să corespundă în câmpul țintă. De exemplu, dacă aveți precizia 6 și scara 2 în sursă (și, în consecință, cifre diferite de 0 înainte de virgula zecimală), nu puteți avea precizia 6 și scara 6 în țintă.
#XMSG
invalidMappedScale=Introduceți o valoare de scară valabilă.
#XMSG
invalidMappedScaleDescription1=Precizia definește numărul total de cifre. Scara definește numărul de cifre după virgula zecimală și poate fi cuprinsă între 0 și precizie.{0}{0} Exemple:{0}{1} Precizia 6, scara 2 corespunde cu numere precum 1234,56.{0}{1} Precizia 6, scara 6 corespunde cu numere precum 0,123546.{0}{0} Scara tipului de date trebuie să fie egală cu sau mai mare decât scara sursei ({2}).
#XMSG
nonCompatibleDataType=Selectați un tip date țintă compatibil.
#XMSG
nonCompatibleDataTypeDescription1=Tipul de date pe care îl specificați aici trebuie să fie compatibil cu tipul de date sursă ({0}). {1}{1} Exemplu: Dacă coloana dvs. sursă are șir de tip de date și conține litere, nu puteți utiliza tipul de date zecimale pentru ținta dvs.
#XMSG
invalidColumnCount=Selectați o coloană sursă.
#XMSG
ObjectStoreInvalidScaleORPrecision=Introduceți o valoare valabilă pentru precizie și scară.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=Prima valoare este precizia, care definește numărul total de cifre. A doua valoare este scara, care definește cifrele de după virgula zecimală. Introduceți o valoare de scară țintă care este mai mare decât valoarea de scară sursă și asigurați-vă că diferența dintre scara țintă introdusă și valoarea de precizie este mai mare decât diferența dintre scara sursă și valoarea de precizie.
#XMSG
InvalidPrecisionORScale=Introduceți o valoare valabilă pentru precizie și scară.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=Prima valoare este precizia, care definește numărul total de cifre. A doua valoare este scara, care definește cifrele după virgula zecimală.{0}{0}Deoarece tipul de date sursă nu este suportat în Google BigQuery, este convertit în tipul date țintă DECIMAL. În acest caz, precizia poate fi definită doar între 38 și 76 și scara între 9 și 38. În plus, rezultatul preciziei minus scara, care reprezintă cifrele înainte de virgula zecimală, trebuie să fie între 29 și 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=Prima valoare este precizia, care definește numărul total de cifre. A doua valoare este scara, care definește cifrele după virgula zecimală.{0}{0}Deoarece tipul de date sursă nu este suportat în Google BigQuery, este convertit în tipul date țintă DECIMAL. În acest caz, precizia trebuie definită ca 20 sau mai mare. În plus, rezultatul preciziei minus scara, care reflectă cifrele înainte de virgula zecimală, trebuie să fie 20 sau mai mare.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=Prima valoare este precizia, care definește numărul total de cifre. A doua valoare este scara, care definește cifrele după virgula zecimală.{0}{0}Deoarece tipul de date sursă nu este suportat în țintă, este convertit în tipul date țintă DECIMAL. În acest caz, precizia trebuie definită de orice număr mai mare decât sau egal cu 1 și mai mic decât sau egal cu 38, iar scara să fie mai mică decât sau egală cu precizia.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=Prima valoare este precizia, care definește numărul total de cifre. A doua valoare este scara, care definește cifrele după virgula zecimală.{0}{0}Deoarece tipul de date sursă nu este suportat în țintă, este convertit în tipul date țintă DECIMAL. În acest caz, precizia trebuie definită ca 20 sau mai mare. În plus, rezultatul preciziei minus scara, care reflectă cifrele înainte de virgula zecimală, trebuie să fie 20 sau mai mare.
#XMSG
invalidColumnCountDescription=Pentru a crea o intrare de mapare valabilă, trebuie să selectați o coloană sursă sau să introduceți o valoare de constantă.
#XMSG
duplicateColumns=Redenumiți coloana țintă.
#XMSG
duplicateGBQCDCColumnsDesc=Numele coloanei țintă este rezervat în Google BigQuery. Trebuie să îl redenumiți pentru a putea salva proiecția.
#XMSG
duplicateConfluentCDCColumnsDesc=Numele coloanei țintă este rezervat în Confluent. Trebuie să îl redenumiți pentru a putea salva proiecția.
#XMSG
duplicateSignavioCDCColumnsDesc=Numele coloanei țintă este rezervat în SAP Signavio. Trebuie să îl redenumiți pentru a putea salva proiecția.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Numele coloanei țintă este rezervat în MS OneLake. Trebuie să îl redenumiți pentru a putea salva proiecția.
#XMSG
duplicateSFTPCDCColumnsDesc=Numele coloanei țintă este rezervat în SFTP. Trebuie să îl redenumiți pentru a putea salva proiecția.
#XMSG
GBQTargetNameWithPrefixUpdated1=Numele coloanei țintă conține un prefix care este rezervat în Google BigQuery. Trebuie să o redenumiți pentru a putea salva proiecția. {0}{0}Numele coloanei țintă nu poate începe cu niciunul dintre următoarele șiruri:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Scurtați numele de coloană țintă.
#XMSG
GBQtargetMaxLengthDesc=În Google BigQuery, un nume de coloană poate utiliza maximum 300 de caractere. Scurtați numele coloanei țintă pentru a putea salva proiecția.
#XMSG
invalidMappedScalePrecision=Precizia și scara pentru țintă trebuie să fie compatibile cu precizia și scara pentru sursă, astfel încât toate cifrele din sursă să corespundă în câmpul țintă.
#XMSG
invalidMappedScalePrecisionShortText=Introduceți o valoare valabilă pentru precizie și scară.
#XMSG
validationIncompatiblePKTypeDescProjection3=Una sau mai multe coloane sursă au tipuri de date care nu pot fi definite drept chei primare în Google BigQuery. Niciuna dintre cheile primare nu va fi creată în obiectul țintă.{0}{0}Următoarele tipuri de date țintă sunt compatibile cu tipurile de date Google BigQuery pentru care poate fi definită o cheie primară:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Debifați column __message_id.
#XMSG
uncheckColumnMessageIdDesc=Coloana: cheie primară
#XMSG
validationOpCodeInsert=Trebuie să introduceți o valoare pentru Inserare.
#XMSG
recommendDifferentPrimaryKey=Vă recomandăm să selectați o cheie primară diferită la nivelul de poziție.
#XMSG
recommendDifferentPrimaryKeyDesc=Când codul de operație este deja definit, este recomandată selectarea de chei primare diferite pentru indexul de matrice și poziții, pentru a evita probleme precum duplicarea coloanei, de exemplu.
#XMSG
selectPrimaryKeyItemLevel=Trebuie să selectați cel puțin o cheie primară pentru nivel de antet și poziție.
#XMSG
selectPrimaryKeyItemLevelDesc=Când o matrice sau o hartă este extinsă, trebuie să selectați două chei primare, una la nivel de antet și una la nivel de poziție.
#XMSG
invalidMapKey=Trebuie să selectați cel puțin o cheie primară la nivelul de antet.
#XMSG
invalidMapKeyDesc=Când o matrice sau o hartă este extinsă, trebuie să selectați o cheie primară la nivelul de antet.
#XFLD
txtSearchFields=Căutare în coloane țintă
#XFLD
txtName=Nume
#XMSG
txtSourceColValidation=Cel puțin o coloană sursă nu este suportată:
#XMSG
txtMappingCount=Mapări ({0})
#XMSG
schema=Schemă
#XMSG
sourceColumn=Coloane sursă
#XMSG
warningSourceSchema=Orice modificare efectuată la schemă va afecta mapările în dialogul de proiecție.
#XCOL
txtTargetColName=Coloană țintă (nume tehnic)
#XCOL
txtDataType=Tip de date țintă
#XCOL
txtSourceDataType=Tip de date sursă
#XCOL
srcColName=Coloană sursă (nume tehnic)
#XCOL
precision=Precizie
#XCOL
scale=Scară
#XCOL
functionsOrConstants=Funcții/constante
#XCOL
txtTargetColBusinessName=Coloană țintă (Nume comercial)
#XCOL
prKey=Cheie primară
#XCOL
txtProperties=Proprietăți
#XBUT
txtOK=Salvare
#XBUT
txtCancel=Anulare
#XBUT
txtRemove=Eliminare
#XFLD
txtDesc=Descriere
#XMSG
rftdMapping=Mapare
#XFLD
@lblColumnDataType=Tip date
#XFLD
@lblColumnTechnicalName=Nume tehnic
#XBUT
txtAutomap=Mapare automată
#XBUT
txtUp=În sus
#XBUT
txtDown=În jos

#XTOL
txtTransformationHeader=Proiecție
#XTOL
editTransformation=Editare
#XTOL
primaryKeyToolip=Cheie


#XMSG
rftdFilter=Filtrare
#XMSG
rftdFilterColumnCount=Sursă: {0}({1})
#XTOL
rftdFilterColSearch=Căutare
#XMSG
rftdFilterColNoData=Nicio coloană de afișat
#XMSG
rftdFilteredColNoExps=Nicio expresie de filtru
#XMSG
rftdFilterSelectedColTxt=Adăugare filtru pentru
#XMSG
rftdFilterTxt=Filtru disponibil pentru
#XBUT
rftdFilterSelectedAddColExp=Adăugare expresie
#YINS
rftdFilterNoSelectedCol=Selectați o coloană pentru a adăuga filtrul.
#XMSG
rftdFilterExp=Expresie de filtru
#XMSG
rftdFilterNotAllowedColumn=Adăugarea filtrelor nu este suportată pentru această coloană.
#XMSG
rftdFilterNotAllowedHead=Nicio coloană suportată
#XMSG
rftdFilterNoExp=Niciun filtru nu a fost definit
#XTOL
rftdfilteredTt=Filtrat
#XTOL
rftdremoveexpTt=Eliminare expresie de filtru
#XTOL
validationMessageTt=Mesaje de validare
#XTOL
rftdFilterDateInp=Selectați o dată
#XTOL
rftdFilterDateTimeInp=Selectați o dată/oră
#XTOL
rftdFilterTimeInp=Selectați o oră
#XTOL
rftdFilterInp=Introduceți o valoare
#XMSG
rftdFilterValidateEmptyMsg={0} expresii de filtru de pe coloana {1} sunt goale
#XMSG
rftdFilterValidateInvalidNumericMsg={0} expresii de filtru de pe coloana {1} conțin valori numerice nevalabile
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Expresia de filtru trebuie să conțină valori numerice valabile
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Dacă schema de obiecte țintă s-a modificat, utilizați funcția „Mapare la obiect țintă existent” pe pagina principală pentru a adapta modificările și remapați obiectul țintă cu sursa sa.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Dacă tabelul țintă există deja și maparea include o modificare a schemei, trebuie să modificați tabelul țintă în consecință, înainte de implementarea fluxului de replicare.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Dacă maparea dvs. implică o modificare a schemei, trebuie să modificați tabelul țintă în consecință, înainte de implementarea fluxului de replicare.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Următoarele coloane nesuportate au fost omise din definiția sursă: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Următoarele coloane nesuportate au fost omise din definiția țintă: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Următoarele obiecte nu sunt suportate deoarece sunt expuse pentru consum: {0} {1} {0} {0} Pentru a utiliza tabele într-un flux de replicare, utilizarea semantică (în setările de tabel) nu trebuie setată la {2}Set de date analitice{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Obiectul țintă nu poate fi utilizat deoarece este expus pentru consum. {0} {0}  Pentru a utiliza tabelul într-un flux de replicare, utilizarea semantică (în setările de tabel) nu trebuie setată la {1}Set de date analitice{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Un obiect țintă cu acest nume există deja. Cu toate acestea, nu poate fi utilizat deoarece este expus pentru consum. {0} {0} Pentru a utiliza tabelul într-un flux de replicare, utilizarea semantică (în setările de tabel) nu trebuie setată la {1}Set de date analitice{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Există deja un obiect cu acest nume în țintă. {0}Cu toate acestea, acest obiect nu poate fi utilizat ca obiect țintă pentru un flux de replicare în registrul local deoarece nu este un tabel local.
#XMSG:
targetAutoRenameUpdated=Coloana țintă a fost redenumită.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Coloana țintă a fost redenumită pentru a permite replicările în Google BigQuery. Aceasta se datorează unuia dintre următoarele motive:{0} {1}{2}Nume de coloană rezervat{3}{2}Caractere nesuportate{3}{2}Prefix rezervat{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Coloana țintă a fost redenumită pentru a permite replicările în Confluent. Aceasta se datorează unuia dintre următoarele motive:{0} {1}{2}Nume de coloană rezervat{3}{2}Caractere nesuportate{3}{2}Prefix rezervat{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Coloana țintă a fost redenumită pentru a permite replicările la țintă. Aceasta se datorează unuia dintre următoarele motive:{0} {1}{2}Caractere nesuportate{3}{2}Prefix rezervat{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Coloana țintă a fost redenumită pentru a permite replicările la țintă. Aceasta se datorează unuia dintre următoarele motive:{0} {1}{2}Nume de coloană rezervat{3}{2}Caractere nesuportate{3}{2}Prefix rezervat{3}{4}
#XMSG:
targetAutoDataType=Tipul de date țintă a fost modificat.
#XMSG:
targetAutoDataTypeDesc=Tipul de date țintă a fost modificat în {0}, deoarece tipul de date surse nu este suportat în Google BigQuery.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Tipul de date țintă a fost modificat în {0}, deoarece tipul de date surse nu este suportat în conexiunea țintă.
#XMSG
projectionGBQUnableToCreateKey=Cheile primare nu vor fi create.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=În Google BigQuery, este suportat un număr maxim de 16 chei primare, dar obiectul sursă are un număr mai mare de chei primare. Niciuna dintre cheile primare nu va fi creată în obiectul țintă.
#XMSG
HDLFNoKeyError=Definiți una sau mai multe coloane ca o cheie primară.
#XMSG
HDLFNoKeyErrorDescription=Pentru a replica un obiect, trebuie să definiți una sau mai multe coloane ca o cheie primară.
#XMSG
HDLFNoKeyErrorExistingTarget=Definiți una sau mai multe coloane ca o cheie primară.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Pentru a replica datele într-un obiect țintă existent, acesta trebuie să aibă una sau mai multe coloane care sunt definite drept cheie primară. {0} {0} Aveți următoarele opțiuni pentru a defini una sau mai multe coloane drept cheie primară: {0}{0}{1} Utilizați editorul de tabel local pentru a modifica obiectul țintă existent. Apoi reîncărcați fluxul de replicare.{0}{0}{1} Redenumiți obiectul țintă în fluxul de replicare. Acest lucru va crea un obiect nou de îndată ce este lansată o execuție. După redenumire, puteți defini una sau mai multe coloane drept cheie primară într-o proiecție.{0}{0}{1} Mapați obiectul la un alt obiect țintă existent în care una sau mai multe coloane sunt deja definite drept cheie primară.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Cheie primară modificată.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=Comparativ cu obiectul sursă, ați definit coloane diferite drept cheie primară pentru obiectul țintă. Asigurați-vă că aceste coloane identifică univoc toate liniile pentru a evita posibila corupere a datelor la replicarea ulterioară a acestora. {0} {0} În obiectul sursă, următoarele coloane sunt definite drept cheie primară: {0} {1}
#XMSG
duplicateDPIDColumns=Redenumiți coloana țintă.
#XMSG
duplicateDPIDDColumnsDesc1=Numele coloanei țintă este rezervat pentru o coloană tehnică. Introduceți un nume diferit pentru a salva proiecția.
#XMSG:
targetAutoRenameDPID=Coloana țintă a fost redenumită.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Coloana țintă a fost redenumită pentru a permite replicări de la sursa ABAP fără chei. Acest lucru se datorează unuia dintre următoare motive:{0} {1}{2}Nume de coloană rezervat{3}{2}Caractere nesuportate{3}{2}Prefix rezervat{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle=Setări țintă {0}
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle=Setări sursă {0}
#XBUT
connectionSettingSave=Salvare
#XBUT
connectionSettingCancel=Anulare
#XBUT: Button to keep the object level settings
txtKeep=Păstrare
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Suprascriere
#XFLD
targetConnectionThreadlimit=Limită de thread țintă pentru încărcare inițială (1-100)
#XFLD
connectionThreadLimit=Limită de thread sursă pentru încărcare inițială (1-100)
#XFLD
maxConnection=Limită de thread replicare (1-100)
#XFLD
kafkaNumberOfPartitions=Număr de partiții
#XFLD
kafkaReplicationFactor=Factor de replicare
#XFLD
kafkaMessageEncoder=Codificator de mesaje
#XFLD
kafkaMessageCompression=Comprimare mesaj
#XFLD
fileGroupDeltaFilesBy=Delta grup după
#XFLD
fileFormat=Tip fișier
#XFLD
csvEncoding=Codificare CSV
#XFLD
abapExitLbl=Ieșire ABAP
#XFLD
deltaPartition=Contor limită de thread obiect pentru încărcări delta (1-10)
#XFLD
clamping_Data=Eroare la trunchiere date
#XFLD
fail_On_Incompatible=Eroare la date incompatibile
#XFLD
maxPartitionInput=Număr maxim de partiții
#XFLD
max_Partition=Definire număr maxim de partiții
#XFLD
include_SubFolder=Includere subfoldere
#XFLD
fileGlobalPattern=Model global pentru nume fișier
#XFLD
fileCompression=Comprimare fișier
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Separator fișier
#XFLD
fileIsHeaderIncluded=Antet fișier
#XFLD
fileOrient=Orient
#XFLD
gbqWriteMode=Mod de scriere
#XFLD
suppressDuplicate=Suprimare duplicate
#XFLD
apacheSpark=Activare compatibilitate Apache Spark
#XFLD
clampingDatatypeCb=Fixare tipuri de date cu virgulă mobilă zecimală
#XFLD
overwriteDatasetSetting=Suprascriere setări țintă la nivel de obiect
#XFLD
overwriteSourceDatasetSetting=Suprascriere setări sursă la nivel de obiect
#XMSG
kafkaInvalidConnectionSetting=Introduceți un număr între {0} și {1}.
#XMSG
MinReplicationThreadErrorMsg=Introduceți un număr mai mare decât {0}.
#XMSG
MaxReplicationThreadErrorMsg=Introduceți un număr mai mic decât {0}.
#XMSG
DeltaThreadErrorMsg=Introduceți o valoare între 1 și 10.
#XMSG
MaxPartitionErrorMsg=Introduceți o valoare între 1 <= x <= 2147483647. Valoarea implicită este 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Introduceți un număr întreg între {0} și {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Utilizare factor de replicare broker
#XFLD
serializationFormat=Format de serializare
#XFLD
compressionType=Tip de comprimare
#XFLD
schemaRegistry=Utilizare registru de schemă
#XFLD
subjectNameStrat=Strategie nume subiect
#XFLD
compatibilityType=Tip de compatibilitate
#XFLD
confluentTopicName=Nume subiect
#XFLD
confluentRecordName=Nume înregistrare
#XFLD
confluentSubjectNamePreview=Previzualizare nume subiect
#XMSG
serializationChangeToastMsgUpdated2=Format serializare modificat în JSON deoarece registrul de schemă nu este activat. Pentru a modifica formatul de serializare înapoi la AVRO, trebuie să activați mai întâi registrul de schemă.
#XBUT
confluentTopicNameInfo=Numele de temă este bazat întotdeauna pe numele obiectului țintă. Îl puteți modifica redenumind obiectul țintă.
#XMSG
emptyRecordNameValidationHeaderMsg=Introduceți un nume de înregistrare.
#XMSG
emptyPartionHeader=Introduceți numărul de partiții.
#XMSG
invalidPartitionsHeader=Introduceți un număr valabil de partiții.
#XMSG
invalidpartitionsDesc=Introduceți un număr între 1 și 200.000.
#XMSG
emptyrFactorHeader=Introduceți un factor de replicare.
#XMSG
invalidrFactorHeader=Introduceți un factor de replicare valabil.
#XMSG
invalidrFactorDesc=Introduceți un număr între 1 și 32.767.
#XMSG
emptyRecordNameValidationDescMsg=Dacă este utilizat formatul de serializare "AVRO", sunt suportate doar următoarele caractere:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(underscore)
#XMSG
validRecordNameValidationHeaderMsg=Introduceți un nume de înregistrare valabil.
#XMSG
validRecordNameValidationDescMsgUpdated=Deoarece este utilizat formatul de serializare "AVRO", numele înregistrării trebuie să conțină doar caractere alfanumerice (A-Z, a-z, 0-9) și underscore (_). Trebuie să înceapă cu o literă sau cu un underscore.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=„Contor limită de thread obiect pentru încărcări delta” poate fi setat imediat ce unul sau mai multe obiecte au tipul de încărcare „Inițial și delta”.
#XMSG
invalidTargetName=Nume coloană nevalabil
#XMSG
invalidTargetNameDesc=Numele coloanei țintă trebuie să conțină doar caractere alfanumerice (A-Z, a-z, 0-9) și underscore (_).
#XFLD
consumeOtherSchema=Consumare alte versiuni de schemă
#XFLD
ignoreSchemamissmatch=Ignorare neconcordanță scheme
#XFLD
confleuntDatatruncation=Eroare la trunchiere date
#XFLD
isolationLevel=Nivel de izolare
#XFLD
confluentOffset=Punct de pornire
#XFLD
signavioGroupDeltaFilesByText=Niciunul
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=Nu
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=Nu

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Proiecții
#XBUT
txtAdd=Adăugare
#XBUT
txtEdit=Editare
#XMSG
transformationText=Adăugați o proiecție pentru a configura filtrarea sau maparea.
#XMSG
primaryKeyRequiredText=Selectați o cheie primară cu Configurare schemă.
#XFLD
lblSettings=Setări
#XFLD
lblTargetSetting={0}: setări țintă
#XMSG
@csvRF=Selectați fișierul care conține definiția de schemă pe care doriți să o aplicați tuturor fișierelor din folder.
#XFLD
lblSourceColumns=Coloane sursă
#XFLD
lblJsonStructure=Structură JSON
#XFLD
lblSourceSetting={0}: setări sursă
#XFLD
lblSourceSchemaSetting={0}: setări schemă sursă
#XBUT
messageSettings=Setări mesaj
#XFLD
lblPropertyTitle1=Proprietăți obiect
#XFLD
lblRFPropertyTitle=Proprietăți flux de replicare
#XMSG
noDataTxt=Nu există coloane de afișat.
#XMSG
noTargetObjectText=Niciun obiect țintă nu este selectat.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Coloane țintă
#XMSG
searchColumns=Căutare în coloane
#XTOL
cdcColumnTooltip=Coloană pentru captură delta
#XMSG
sourceNonDeltaSupportErrorUpdated=Obiectul sursă nu suportă captura delta.
#XMSG
targetCDCColumnAdded=2 coloane țintă au fost adăugate pentru captura delta.
#XMSG
deltaPartitionEnable=Limită de thread obiect pentru încărcări delta adăugată la setări sursă.
#XMSG
attributeMappingRemovalTxt=Se elimină mapările nevalabile care nu sunt suportate pentru noul obiect țintă.
#XMSG
targetCDCColumnRemoved=2 coloane țintă utilizate pentru captura delta au fost eliminate.
#XMSG
replicationLoadTypeChanged=Tipul de încărcare a fost modificat în "Inițial și delta".
#XMSG
sourceHDLFLoadTypeError=Modificați tipul de încărcare în "Inițial și delta".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Pentru a replica un obiect de la o conexiune sursă cu tipul de conexiune Fișiere SAP HANA Cloud, lac de date în SAP Datasphere, trebuie să utilizați tipul de încărcare "inițial și delta".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Activați captura delta.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Pentru a replica un obiect de la o conexiune sursă cu tipul de conexiune Fișiere SAP HANA Cloud, lac de date în SAP Datasphere, trebuie să activați captura delta.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Modificați obiectul țintă.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Obiectul țintă nu poate fi utilizat, deoarece captura delta este dezactivată. Puteți redenumi obiectul țintă (care permite crearea unui obiect nou cu captură delta) sau îl puteți mapa la un obiect existent cu captura delta activată.
#XMSG
deltaPartitionError=Introduceți un contor limită de thread obiect valabil pentru încărcări delta.
#XMSG
deltaPartitionErrorDescription=Introduceți o valoare între 1 și 10.
#XMSG
deltaPartitionEmptyError=Introduceți un contor limită de thread obiect pentru încărcări delta.
#XFLD
@lblColumnDescription=Descriere
#XMSG
@lblColumnDescriptionText1=În scopuri tehnice - gestionarea înregistrărilor duplicate cauzate de probleme la replicarea obiectelor sursă pe bază ABAP care nu au o cheie primară.
#XFLD
storageType=Spațiu de stocare
#XFLD
skipUnmappedColLbl=Omitere coloane nemapate
#XFLD
abapContentTypeLbl=Tip de conținut
#XFLD
autoMergeForTargetLbl=Concatenare automată date
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Generalități
#XFLD
lblBusinessName=Nume comercial
#XFLD
lblTechnicalName=Nume tehnic
#XFLD
lblPackage=Pachet
#XFLD
statusPanel=Stare execuție
#XBTN: Schedule dropdown menu
SCHEDULE=Programare
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Editare programare
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Ștergere programare
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Creare programare
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Eroare la verificare validare programare
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=O programare nu poate fi creată, deoarece fluxul de replicare este în curs de implementare.{0}Așteptați până când a fost implementat fluxul de replicare.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Pentru fluxurile de replicare ce conțin obiecte cu tipul de încărcare "Inițial și delta", nu poate fi creată nicio programare.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Pentru fluxurile de replicare ce conțin obiecte cu tipul de încărcare „Inițial și delta/Dor delta”, nu poate fi creată nicio programare.
#XFLD : Scheduled popover
SCHEDULED=Programat
#XFLD
CREATE_REPLICATION_TEXT=Creați un flux de replicare
#XFLD
EDIT_REPLICATION_TEXT=Editați un flux de replicare
#XFLD
DELETE_REPLICATION_TEXT=Ștergeți un flux de replicare
#XFLD
REFRESH_FREQUENCY=Frecvență
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Fluxul de replicare nu poate fi implementat, deoarece programarea existentă{0} nu suportă încă tipul de încărcare "Inițial și delta".{0}{0}Pentru a implementa fluxul de replicare, trebuie să setați tipurile de încărcare ale tuturor obiectelor{0} la "Doar inițial". Alternativ, puteți șterge programarea, puteți implementa {0}fluxul de replicare și apoi puteți lansa o execuție nouă. Aceasta rezultă într-o execuție fără {0}sfârșit, care suportă și obiectele cu tipul de încărcare "Inițial și delta".
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Fluxul de replicare nu poate fi implementat, deoarece programarea existentă{0} nu suportă încă tipul de încărcare „Inițial și delta/Doar delta”.{0}{0}Pentru a implementa fluxul de replicare, trebuie să setați tipurile de încărcare ale tuturor obiectelor{0} la „Doar inițial”. Alternativ, puteți șterge programarea, puteți implementa {0}fluxul de replicare și apoi puteți lansa o execuție nouă. Aceasta rezultă într-o execuție fără {0}sfârșit, care suportă și obiectele cu tipul de încărcare „Inițial și delta/Doar delta”.
#XMSG
SCHEDULE_EXCEPTION=Eroare la obținere detalii programare
#XFLD: Label for frequency column
everyLabel=La fiecare
#XFLD: Plural Recurrence text for Hour
hoursLabel=Ore
#XFLD: Plural Recurrence text for Day
daysLabel=Zile
#XFLD: Plural Recurrence text for Month
monthsLabel=Luni
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minute
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Eroare la obținere informații despre posibilitatea de programare.
#XFLD :Paused field
PAUSED=Întrerupt
#XMSG
navToMonitoring=Deschidere în monitor de fluxuri
#XFLD
statusLbl=Stare
#XFLD
lblLastRunExecuted=Început ultima execuție
#XFLD
lblLastExecuted=Ultima execuție
#XFLD: Status text for Completed
statusCompleted=Terminat
#XFLD: Status text for Running
statusRunning=În execuție
#XFLD: Status text for Failed
statusFailed=Nereușit
#XFLD: Status text for Stopped
statusStopped=Oprit
#XFLD: Status text for Stopping
statusStopping=În curs de oprire
#XFLD: Status text for Active
statusActive=Activ
#XFLD: Status text for Paused
statusPaused=Întrerupt
#XFLD: Status text for not executed
lblNotExecuted=Neexecutat încă
#XFLD
messagesSettings=Setări mesaje
#XTOL
@validateModel=Mesaje de validare
#XTOL
@hierarchy=Ierarhie
#XTOL
@columnCount=Număr de coloane
#XMSG
VAL_PACKAGE_CHANGED=Ați alocat acest obiect la pachetul "{1}". Efectuați click pe “Salvare” pentru a confirma și a valida această modificare. Rețineți faptul că alocarea la un pachet nu poate fi anulată în acest editor după salvare.
#XMSG
MISSING_DEPENDENCY=Dependențele obiectului "{0}" nu pot fi rezolvate în pachetul "{1}".
#XFLD
deltaLoadInterval=Interval încărcare delta
#XFLD
lblHour=Ore (0-24)
#XFLD
lblMinutes=Minute (0-59)
#XMSG
maxHourOrMinErr=Introduceți o valoare între 0 și {0}
#XMSG
maxDeltaInterval=Valoarea maximă a intervalului de încărcare delta este de 24 de ore.{0}Modificați valoarea în minute sau valoarea în ore corespunzător.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Cale container țintă
#XFLD
confluentSubjectName=Nume subiect
#XFLD
confluentSchemaVersion=Versiune schemă
#XFLD
confluentIncludeTechKeyUpdated=Includere cheie tehnică
#XFLD
confluentOmitNonExpandedArrays=Omitere matrici neextinse
#XFLD
confluentExpandArrayOrMap=Extindere matrice sau hartă
#XCOL
confluentOperationMapping=Mapare operație
#XCOL
confluentOpCode=Opcode
#XFLD
confluentInsertOpCode=Inserare
#XFLD
confluentUpdateOpCode=Actualizare
#XFLD
confluentDeleteOpCode=Ștergere
#XFLD
expandArrayOrMapNotSelectedTxt=Neselectat
#XFLD
confluentSwitchTxtYes=Da
#XFLD
confluentSwitchTxtNo=Nu
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Eroare
#XTIT
executeWarning=Avertizare
#XMSG
executeunsavederror=Salvați fluxul dvs. de replicare înainte de a-l executa.
#XMSG
executemodifiederror=Există modificări nesalvate în fluxul de replicare. Salvați fluxul de replicare.
#XMSG
executeundeployederror=Trebuie să implementați fluxul dvs. de replicare înainte de a-l putea executa.
#XMSG
executedeployingerror=Așteptați finalizarea implementării.
#XMSG
msgRunStarted=Execuție lansată
#XMSG
msgExecuteFail=Eroare la executare flux de replicare
#XMSG
titleExecuteBusy=Așteptați.
#XMSG
msgExecuteBusy=Pregătim datele dvs. pentru execuția fluxului de replicare.
#XTIT
executeConfirmDialog=Avertizare
#XMSG
msgExecuteWithValidations=Fluxul de replicare are erori de validare. Executarea fluxului de replicare poate duce la eroare.
#XMSG
msgRunDeployedVersion=Există modificări de implementat. Va fi executată ultima versiune implementată a fluxului de replicare. Doriți să continuați?
#XBUT
btnExecuteAnyway=Executare în orice caz
#XBUT
btnExecuteClose=Închidere
#XBUT
loaderClose=Închidere
#XTIT
loaderTitle=Încărcare
#XMSG
loaderText=Obținere detalii de la server
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Fluxul de replicare la această conexiune țintă non-SAP nu poate fi lansat
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=deoarece nu există niciun volum de ieșire disponibil pentru această lună.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Un administrator poate crește blocările de ieșire premium pentru
#XMSG
premiumOutBoundRFAdminErrMsgPart2=acest tenant, punând la dispoziție volumul de ieșire pentru această lună.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Eroare
#XTIT
deployInfo=Informații
#XMSG
deployCheckFailException=Excepție apărută la implementare
#XMSG
deployGBQFFDisabled=Implementarea fluxurilor de replicare cu o conexiune țintă în Google BigQuery nu este posibilă în prezent deoarece efectuăm o întreținere a acestei funcții.
#XMSG
deployKAFKAFFDisabled=Implementarea fluxurilor de replicare cu o conexiune țintă în Apache Kafka nu este posibilă în prezent deoarece efectuăm o întreținere a acestei funcții.
#XMSG
deployConfluentDisabled=Implementarea fluxurilor de replicare cu o conexiune țintă în Confluent Kafka nu este posibilă în prezent deoarece efectuăm o întreținere a acestei funcții.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Pentru următoarele obiecte țintă, numele de tabel de captură delta sunt deja utilizate de alte tabele din registru: {0} Trebuie să redenumiți aceste obiecte țintă pentru a vă asigura că numele de tabel de captură delta sunt univoce înainte să puteți implementa fluxul de replicare.
#XMSG
deployDWCSourceFFDisabled=Implementarea fluxurilor de replicare care au SAP Datasphere ca sursă nu este posibilă în prezent deoarece efectuăm întreținerea acestei funcții.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Implementarea fluxurilor de replicare care conțin tabele locale activate pentru delta ca obiecte sursă nu este posibilă în prezent deoarece efectuăm întreținerea acestei funcții.
#XMSG
deployHDLFSourceFFDisabled=Implementarea fluxurilor de replicare care au conexiuni sursă cu tipul de conexiune fișiere SAP HANA Cloud, lac de date, nu este posibilă în prezent deoarece efectuăm întreținerea.
#XMSG
deployObjectStoreAsSourceFFDisabled=Implementarea fluxurilor de replicare care au furnizori de stocare cloud ca sursă nu este posibilă în prezent.
#XMSG
deployConfluentSourceFFDisabled=Implementarea fluxurilor de replicare care au Confluent Kafka ca sursă nu este posibilă în prezent deoarece efectuăm întreținerea acestei funcții.
#XMSG
deployMaxDWCNewTableCrossed=Pentru fluxurile de replicare mari, nu este posibil să le „salvați și implementați” simultan. Salvați întâi fluxul dvs. de replicare și apoi implementați-l.
#XMSG
deployInProgressInfo=Implementarea este deja în desfășurare.
#XMSG
deploySourceObjectInUse=Obiectele sursă {0} sunt deja utilizate în fluxurile de replicare {1}.
#XMSG
deployTargetSourceObjectInUse=Obiectele sursă {0} sunt deja utilizate în fluxurile de replicare {1}. Obiectele țintă {2} sunt deja utilizate în fluxurile de replicare {3}.
#XMSG
deployReplicationFlowCheckError=Eroare la verificare flux de replicare: {0}
#XMSG
preDeployTargetObjectInUse=Obiectele țintă {0} sunt deja utilizate în fluxurile de replicare {1} și nu puteți avea același obiect țintă în două fluxuri de replicare diferite. Selectați alt obiect țintă și încercați din nou.
#XMSG
runInProgressInfo=Fluxul de replicare este deja în execuție.
#XMSG
deploySignavioTargetFFDisabled=Implementarea fluxurilor de replicare care au SAP Signavio drept țintă nu este posibilă în prezent deoarece efectuăm întreținerea acestei funcții.
#XMSG
deployHanaViewAsSourceFFDisabled=Implementarea fluxurilor de replicare care au imagini drept obiecte sursă pentru conexiunea sursă selectată nu este posibilă în prezent. Reîncercați mai târziu.
#XMSG
deployMsOneLakeTargetFFDisabled=Implementarea fluxurilor de replicare care au MS OneLake drept țintă nu este posibilă în prezent deoarece efectuăm întreținerea acestei funcții.
#XMSG
deploySFTPTargetFFDisabled=Implementarea fluxurilor de replicare care au SFTP drept țintă nu este posibilă în prezent deoarece efectuăm întreținerea acestei funcții.
#XMSG
deploySFTPSourceFFDisabled=Implementarea fluxurilor de replicare care au SFTP drept sursă nu este posibilă în prezent deoarece efectuăm întreținerea acestei funcții.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Nume tehnic
#XFLD
businessNameInRenameTarget=Nume comercial
#XTOL
renametargetDialogTitle=Redenumire obiect țintă
#XBUT
targetRenameButton=Redenumire
#XBUT
targetRenameCancel=Anulare
#XMSG
mandatoryTargetName=Trebuie să introduceți un nume.
#XMSG
dwcSpecialChar=_(underscore) este singurul caracter special permis.
#XMSG
dwcWithDot=Numele tabelului țintă poate conține litere latine, cifre, underscore-uri (_) și puncte (.). Primul caracter trebuie să fie o literă, o cifră sau un underscore (nu un punct).
#XMSG
nonDwcSpecialChar=Caracterele speciale permise sunt _(underscore) -(cratimă) .(punct)
#XMSG
firstUnderscorePattern=Numele nu poate începe cu _(underscore)

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: Vizualizare instrucțiune SQL Creare tabel
#XMSG
sqlDialogMaxPKWarning=În Google BigQuery, sunt suportate maximum 16 chei primare și obiectul sursă are un număr mai mare. În consecință, nicio cheie primară nu este definită în această instrucțiune.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Una sau mai multe coloane sursă au tipuri de date care nu pot fi definite drept chei primare în Google BigQuery.În consecință, nicio cheie primară nu a fost definită în acest caz. În Google BigQuery, doar următoarele tipuri de date pot avea o cheie primară: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Copiere și închidere
#XBUT
closeDDL=Închidere
#XMSG
copiedToClipboard=Copiat în clipboard


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Obiectul „{0}” nu poate face parte dintr-un lanț de sarcini deoarece nu are un sfârșit (deoarece include obiecte cu tipul de încărcare Inițial și delta/Doar delta).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Obiectul „{0}” nu poate face parte dintr-un lanț de sarcini deoarece nu are un sfârșit (deoarece include obiecte cu tipul de încărcare Inițial și Delta).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Obiectul "{0}" nu poate fi adăugat la lanț de sarcini.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Există obiecte țintă nesalvate. Salvați din nou.{0}{0} Comportamentul acestei caracteristici s-a modificat: în trecut, obiectele țintă erau create doar în mediul țintă când era implementat fluxul de replicare.{0} Acum, obiectele sunt deja create când este salvat fluxul de replicare. Fluxul dvs. de replicare a fost creat înainte de această modificare și conține obiecte noi.{0} Trebuie să salvați fluxul de replicare din nou înainte de a-l implementa, astfel încât obiectele noi să fie incluse în mod corect.
#XMSG
confirmChangeContentTypeMessage=Sunteți pe cale să modificați tipul de conținut. Dacă faceți acest lucru, toate proiecțiile existente vor fi șterse.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Nume subiect
#XFLD
schemaDialogVersionName=Versiune schemă
#XFLD
includeTechKey=Includere cheie tehnică
#XFLD
segementButtonFlat=Plată
#XFLD
segementButtonNested=Imbricată
#XMSG
subjectNamePlaceholder=Căutați nume subiect

#XMSG
@EmailNotificationSuccess=Configurarea notificărilor prin e-mail privind timpul de execuție este salvată.

#XFLD
@RuntimeEmailNotification=Notificare e-mail timp de execuție

#XBTN
@TXT_SAVE=Salvare


