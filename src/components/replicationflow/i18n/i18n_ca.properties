#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Flux de replicació

#XFLD: Edit Schema button text
editSchema=Editar esquema

#XTIT : Properties heading
configSchema=Configurar esquema

#XFLD: save changed button text
applyChanges=Aplica canvis


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Seleccionar la connexió d’origen
#XFLD
sourceContainernEmptyText=Seleccioneu contenidor
#XFLD
targetConnectionEmptyText=Seleccioneu connexió de destinació
#XFLD
targetContainernEmptyText=Seleccioneu contenidor
#XFLD
sourceSelectObjectText=Seleccionar objecte font
#XFLD
sourceObjectCount=Objectes font ({0})
#XFLD
targetObjectText=Objectes font
#XFLD
confluentBrowseContext=Selecciona context
#XBUT
@retry=Reintentar
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Actualització d'arrendatari en curs.

#XTOL
browseSourceConnection=Exploreu la connexió d'origen
#XTOL
browseTargetConnection=Navega per la connexió de destinació
#XTOL
browseSourceContainer=Explorar contenidor d'origen
#XTOL
browseAndAddSourceDataset=Afegir objectes font
#XTOL
browseTargetContainer=Explorar contenidor de destinació
#XTOL
browseTargetSetting=Explorar opcions de destinació
#XTOL
browseSourceSetting=Explorar opcions d'origen
#XTOL
sourceDatasetInfo=Informació
#XTOL
sourceDatasetRemove=Eliminar
#XTOL
mappingCount=Això representa el nombre total de mapes/expressions no basades en noms.
#XTOL
filterCount=Això representa el nombre total de condicions de filtre.
#XTOL
loading=Carregant...
#XCOL
deltaCapture=Captura delta
#XCOL
deltaCaptureTableName=Taula de captura delta
#XCOL
loadType=Carrregar tipus
#XCOL
deleteAllBeforeLoading=Suprimir-ho tot abans de carregar
#XCOL
transformationsTab=Projeccions
#XCOL
settingsTab=Configuració

#XBUT
renameTargetObjectBtn=Reanomenar objecte de destinació
#XBUT
mapToExistingTargetObjectBtn=Assignar a l'objecte de destinació existent
#XBUT
changeContainerPathBtn=Modificar via d'accés de contenidor
#XBUT
viewSQLDDLUpdated=Visualitzar sentència de taula de creació SQL
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=L'objecte d'origen no admet una captura delta, però l'objecte de destinació seleccionat té activada l'opció de captura delta.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=L''objecte de destinació no es pot utilitzar perquè la captura delta està activada,{0}però l''objecte d''origen no admet la captura delta.{1}Podeu seleccionar un altre objecte de destinació que no admeti captures delta.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Ja existeix un objecte de destinació amb aquest nom. Tanmateix, no es pot utilitzar{0}perquè hi ha habilitada la captura delta i l''objecte d''origen no{0}l''admet.{1}Podeu introduir el nom d''un objecte de destinació existent que no{0}admeti la captura delta o introduir un nom que encara no existeixi.
#XBUT
copySQLDDLUpdated=Copiar sentència de taula de creació SQL
#XMSG
targetObjExistingNoCDCColumnUpdated=Les taules existents a Google BigQuery han d''incloure les columnes següents per a la captura de dades de modificació (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Els objectes font següents no són compatibles perquè no tenen una clau primària o utilitzen una connexió que no compleix les condicions per recuperar la clau primària:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Consulteu SAP KBA 3531135 per trobar una possible solució.
#XLST: load type list values
initial=Només inicial
@emailUpdateError=Error en actualitzar la llista de notificacions per correu electrònic

#XLST
initialDelta=Inicial i delta

#XLST
deltaOnly=Només delta
#XMSG
confluentDeltaLoadTypeInfo=Per a la font de Confluent Kafka, només es permet el tipus de càrrega Inicial i Delta.
#XMSG
confirmRemoveReplicationObject=Confirmeu que voleu suprimir la replicació?
#XMSG
confirmRemoveReplicationTaskPrompt=Aquesta acció suprimirà les rèpliques existents. Voleu continuar?
#XMSG
confirmTargetConnectionChangePrompt=Aquesta acció restablirà la connexió de destinació, el contenidor de destinació i suprimirà tots els objectes de destinació. Voleu continuar?
#XMSG
confirmTargetContainerChangePrompt=Aquesta acció restablirà el contenidor de destinació i suprimirà tots els objectes de destinació existents. Voleu continuar?
#XMSG
confirmRemoveTransformObject=Confirmeu que voleu suprimir la projecció {0}?
#XMSG
ErrorMsgContainerChange=S'ha produït un error en canviar el camí del contenidor.
#XMSG
infoForUnsupportedDatasetNoKeys=Els objectes d'origen següents no s'admeten perquè no tenen cap clau primària:
#XMSG
infoForUnsupportedDatasetView=Els següents objectes d'origen del tipus Vistes no són compatibles:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=No es permet el següent objecte font perquè és una vista SQL que conté paràmetres d'entrada:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Els objectes d'origen següents no s'admeten perquè no tenen activada l'extracció:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Per a les connexions Confluent, els únics formats de serialització permesos són AVRO i JSON. No es permeten els objectes següents perquè utilitzen un format de serialització diferent:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=No es pot obtenir l'esquema pels objectes següents. Seleccioneu el context adequat o verifiqueu la configuració de registre d'esquema
#XTOL: warning dialog header on deleting replication task
deleteHeader=Suprimir
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=No es permet l'opció Suprimir-ho tot abans de carregar per a Google BigQuery.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=L'opció Suprimir-ho tot abans de suprimeix i recrea l'objecte (tema) abans de cada replicació. També suprimeix tots els missatges assignats.
#XTOL
DeleteAllBeforeLoadingLTFInfo=No es permet l'opció Suprimir-ho tot abans de per aquest tipus de destinació.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Nom tècnic
#XCOL
connBusinessName=Nom empresarial
#XCOL
connDescriptionName=Descripció
#XCOL
connType=Tipus
#XMSG
connTblNoDataFoundtxt=No existeixen connexions
#XMSG
connectionError=S'ha produït un error en recuperar les connexions.
#XMSG
connectionCombinationUnsupportedErrorTitle=No es permet la combinació de connexió
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Actualment no es permet la replicació de {0} a {1}.
#XMSG
invalidTargetforSourceHDLFErrorTitle=No s'admet la combinació de tipus de connexió
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=No s''admet la replicació d''una connexió amb el tipus de connexió SAP HANA Cloud, Data Lake Files, a {0}. Només es pot replicar a SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Seleccionar
#XBUT
containerCancelBtn=Cancel·lar
#XTOL
containerSelectTooltip=Seleccionar
#XTOL
containerCancelTooltip=Cancel·lar
#XMSG
containerContainerPathPlcHold=Via del contenidor
#XFLD
containerContainertxt=Contenidor
#XFLD
confluentContainerContainertxt=Context
#XMSG
infoMessageForSLTSelection=Només es permet /SLT/ID de transferència massiva com a contenidor. Seleccioneu un ID de transferència massiva a SLT (si està disponible) i feu clic a Enviar.
#XMSG
msgFetchContainerFail=S'ha produït un error en obtenir les dades de contenidor.
#XMSG
infoMessageForSLTHidden=Aquesta connexió no admet carpetes SLT, de manera que no es mostren a la llista següent.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Seleccioneu un contenidor que contingui subcarpetes.
#XMSG
sftpIncludeSubFolderText=Fals
#XMSG
sftpIncludeSubFolderTextNew=No

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Encara no hi ha mapes de filtres)
#XMSG
failToFetchRemoteMetadata=S'ha produït un error en obtenir les metadades.
#XMSG
failToFetchData=S'ha produït un error en obtenir l'objectiu existent.
#XCOL
@loadType=Carrregar tipus
#XCOL
@deleteAllBeforeLoading=Suprimir-ho tot abans de carregar

#XMSG
@loading=Carregant...
#XFLD
@selectSourceObjects=Seleccionar objecte font
#XMSG
@exceedLimit=No podeu importar més de {0} objectes a la vegada. Desmarqueu {1} objectes, com a mínim.
#XFLD
@objects=Objectes
#XBUT
@ok=D'acord
#XBUT
@cancel=Cancel·lar
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Següent
#XBUT
btnAddSelection=Afegir selecció
#XTOL
@remoteFromSelection=Eliminar de la selecció
#XMSG
@searchInForSearchField=Cerca a {0}

#XCOL
@name=Nom tècnic
#XCOL
@type=Tipus
#XCOL
@location=Ubicació
#XCOL
@label=Nom empresarial
#XCOL
@status=Estat

#XFLD
@searchIn=Cercar a:
#XBUT
@available=Disponible
#XBUT
@selection=Selecció

#XFLD
@noSourceSubFolder=Taules i vistes
#XMSG
@alreadyAdded=Ja està present al diagrama
#XMSG
@askForFilter=Hi ha més de {0} articles. Introduïu una cadena de filtre per reduir el nombre d''elements.
#XFLD: success label
lblSuccess=Èxit
#XFLD: ready label
lblReady=Preparat
#XFLD: failure label
lblFailed=Error
#XFLD: fetching status label
lblFetchingDetail=Obtenint detalls

#XMSG Place holder text for tree filter control
filterPlaceHolder=Introduir text per filtrar objectes de nivell superior
#XMSG Place holder text for server search control
serverSearchPlaceholder=Escriviu i premeu Retorn per cercar
#XMSG
@deployObjects=Importar {0} objectes...
#XMSG
@deployObjectsStatus=Nombre d''objectes que s''han importat: {0}. Nombre d''objectes que no s''han pogut importar: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=No s'ha pogut obrir el navegador del dipòsit local.
#XMSG
@openRemoteSourceBrowserError=No s'han pogut obtenir els objectes font.
#XMSG
@openRemoteTargetBrowserError=No s'han pogut obtenir els objectes de destinació.
#XMSG
@validatingTargetsError=S'ha produït un error en validar els objectius.
#XMSG
@waitingToImport=A punt per importar

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=S'ha superat el nombre màxim d'objectes. Seleccioneu-ne com a màxim 500 per a un flux de replicació.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Nom tècnic
#XFLD
sourceObjectBusinessName=Nom empresarial
#XFLD
sourceNoColumns=Número de columnes
#XFLD
containerLbl=Contenidor

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Heu de seleccionar una connexió d'origen per al flux de rèplica.
#XMSG
validationSourceContainerNonExist=Heu de seleccionar un contenidor per a la connexió d'origen.
#XMSG
validationTargetNonExist=Heu de seleccionar una connexió de destinació per al flux de rèplica.
#XMSG
validationTargetContainerNonExist=Heu de seleccionar un contenidor per a la connexió de destinació.
#XMSG
validationTruncateDisabledForObjectTitle=Replicació a emmagatzematges d'objecte.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Només és possible replicar a l''emmagatzematge al núvol si es fixa l''opció de Suprimir-ho tot abans de carregar o si l''objecte de destinació no existeix a la destinació.{0}{0} Per activar la replicació de totes maneres per a objectes pels quals no s''ha fixat l''opció de Suprimir-ho tot abans de carregar, assegureu-vos que no existeixi l''objecte de destinació al sistema abans d''executar el flux de replicació.
#XMSG
validationTaskNonExist=Heu de tenir almenys una rèplica al flux de rèplica.
#XMSG
validationTaskTargetMissing=Heu de tenir un objectiu per a la replicació amb la font: {0}
#XMSG
validationTaskTargetIsSAC=La destinació seleccionada és un artefacte SAC: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=La destinació seleccionada no és una taula local compatible: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Ja existeix un objecte amb aquest nom a la destinació. Tanmateix, aquest objecte no es pot utilitzar com a objecte de destinació per a un flux de replicació en el dipòsit local, atès que no és una taula local.
#XMSG
validateSourceTargetSystemDifference=Heu de seleccionar diferents combinacions de connexió d'origen i de destinació i contenidor per al flux de rèplica.
#XMSG
validateDuplicateSources=una o més replicacions tenen noms d''objecte font duplicats: {0}.
#XMSG
validateDuplicateTargets=una o més replicacions tenen noms d''objecte de destinació duplicats: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=L''objecte d''origen {0} no admet una captura delta, mentre que l''objecte de destinació {1}, sí. Heu d''eliminar la replicació.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Heu de seleccionar el tipus de càrrega "Inicial i delta" per a la replicació amb el nom de l''objecte de destinació {0}.
#XMSG
validationAutoRenameTarget=S'ha canviat el nom de les columnes de destinació.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=S''ha afegit una projecció automàtica i s''ha canviat el nom de les següents columnes de destinació per permetre''n la replicació al destí:{1}{1} {0} {1}{1}La causa és una de les següents:{1}{1}{2} Caràcters no permesos{1}{2}Prefix reservat
#XMSG
validationAutoRenameTargetDescriptionUpdated=S''ha afegit una projecció automàtica i s''ha canviat el nom de les següents columnes de destinació per permetre''n la replicació a Google BigQuery:{1}{1} {0} {1}{1}La causa és una de les següents:{1}{1}{2} Nom de columna reservat{1}{2} Caràcters no permesos{1}{2} Prefix reservat
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=S''ha afegit una projecció automàtica i s''ha canviat el nom de les següents columnes de destinació per permetre''n la replicació a Confluent:{1}{1} {0} {1}{1}La causa és una de les següents:{1}{1}{2} Nom de columna reservat{1}{2} Caràcters no permesos{1}{2} Prefix reservat
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=S''ha afegit una projecció automàtica i s''ha canviat el nom de les següents columnes de destinació per permetre''n la replicació a la destinació:{1}{1} {0} {1}{1}La causa és una de les següents:{1}{1}{2} Nom de columna reservat{1}{2} Caràcters no permesos{1}{2} Prefix reservat
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=S'ha reanomenat l'objecte de destinació.
#XMSG
autoRenameInfoDesc=S''ha reanomenat l''objecte de destinació perquè contenia caràcters no admesos. Només s''admeten els caràcters següents:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(punt){0}{1}_(guionet baix){0}{1}-(guionet)
#XMSG
validationAutoTargetTypeConversion=S'han modificat el tipus de dades de destinació.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Per a les columnes de destinació següents, s''han modificat els tipus de dades de destinació perquè a Google BigQuery, no s''admeten els tipus de dades d''origen:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Per a les columnes de destinació següents, s''han modificat els tipus de dades de destinació perquè no s''admeten els tipus de dades d''origen a la connexió destí:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Escurceu els noms de columnes de destinació.
#XMSG
validationMaxCharLengthGBQTargetDescription=A Google BigQuery, els noms de columna poden tenir un màxim de 300 caràcters. Utilitzeu una projecció per escurçar els noms de columna de destinació següents:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=No es crearan claus primàries.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=A Google BigQuery, s'admeten un màxim de 16 claus primàries, però l'objecte d'origen en té més. No es crearà cap de les claus primàries a l'objecte de destinació.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Una o més columnes d''origen tenen tipus de dades que no es poden definir com a claus primàries a Google BigQuery. No es crearà cap clau primària a l''objecte de destinació.{0}{0}Els tipus de dades de destinació següents són compatibles amb els tipus de dades de Google BigQuery per als quals es pot definir una clau primària:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Definiu una o més columnes com a clau primària.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Heu de definir una o més columnes com a clau primària. Feu servir un diàleg d'esquema d'origen per fer-ho.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Definiu una o més columnes com a clau primària.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Heu de definir una o més columnes com a clau primària que coincideixin amb les restriccions de clau primària del vostre objecte font. Aneu a Configurar l'esquema a les propietats del vostre objecte font per fer-ho.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Introduïu un valor de partició màxim vàlid.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=El valor màxim de partició ha de ser ≥ 1 i ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Definiu una o més columnes com a clau primària.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Per replicar un objecte, heu de definir una o més columnes de destinació com a clau primària. Per fer-ho, utilitzeu una projecció.
#XMSG
validateHDLFNoPKExistingDatasetError=Definiu una o més columnes com a clau primària.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Per replicar dades en un objecte de destinació existent, aquest ha de tenir una o més columnes definides com a clau primària. {0} Teniu les opcions següents per definir una o més columnes com a clau primària: {0}{1} Utilitzeu l''editor de taules local per modificar l''objecte de destinació existent. En acabat, torneu a carregar el flux de replicació.{0}{1} Canvieu el nom de l''objecte de destinació en el flux de replicació. D''aquesta manera es crearà un objecte nou tal com s''iniciï una execució. Un cop canviat el nom, podeu definir una o més columnes com a clau primària a la projecció.{0}{1} Assigneu l''objecte a un altre objecte de destinació existent en el qual hi hagi una o més columnes ja definides com a clau primària.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=La destinació seleccionada ja existeix en el dipòsit: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Els noms de les taules de captura delta ja s''utilitzen en altres taules del dipòsit: {0}. Heu de canviar el nom d''aquests objectes de destinació per assegurar-vos que els noms de les taules de captura delta associats siguin unívocs abans de poder desar el flux de replicació.
#XMSG
validateConfluentEmptySchema=Defineix esquema
#XMSG
validateConfluentEmptySchemaDescUpdated=La taula font no té cap esquema. Seleccioneu Configura esquema per a definir-ne un
#XMSG
validationCSVEncoding=Codificació CSV no vàlida
#XMSG
validationCSVEncodingDescription=La codificació CSV de la tasca no és vàlida.
#XMSG
validateConfluentEmptySchema=Seleccioneu un tipus de dades de destinació compatible
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Seleccioneu un tipus de dades de destinació compatible
#XMSG
globalValidateTargetDataTypeDesc=S'ha produït un error amb les assignacions de columna. Aneu a Projecció i comproveu que totes les columnes d'origen estiguin assignades a una columna unívoca, a una columna que tingui un tipus de dades compatible i que totes les expressions definides siguin vàlides.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Noms de columna duplicats.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=No s''admeten noms de columna duplicats. Utilitzeu el diàleg de projecció per corregir-ho. Els següents objectes de destinació tenen noms de columna duplicats: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Noms de columna duplicats.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=No s''admeten noms de columna duplicats. Els següents objectes de destinació tenen noms de columna duplicats: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Hi pot haver inconsistències en les dades.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=El tipus de càrrega Només delta no tindrà en compte els canvis realitzats a l'origen entre l'últim desament i la següent execució.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Modifiqueu el tipus de càrrega a "Inicial".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Només es poden replicar objectes basats en ABAP que no tenen una clau primària per al tipus de càrrega "Només inicial".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Desactiveu la captura delta.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Per replicar un objecte que no té una clau primària i que utilitzi un tipus de connexió font ABAP, abans heu de desactivar la captura delta per a aquesta taula.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Modifiqueu l'objecte de destinació.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=L'objecte de destinació no es pot utilitzar perquè la captura delta està activada. Podeu canviar el nom de l'objecte de destinació i després desactivar la captura delta per a l'objecte nou (amb el nom canviat) o assignar l'objecte d'origen a un objecte de destinació amb la captura delta desactivada.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Modifiqueu l'objecte de destinació.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=L'objecte de destinació no es pot utilitzar perquè no té la columna tècnica obligatòria __load_package_id. Podeu canviar el nom de l'objecte de destinació fent servir un nom que encara no existeixi. Aleshores, el sistema crea un objecte nou que té la mateixa definició que l'objecte d'origen i que conté la columna tècnica. Alternativament, podeu assignar l'objecte de destinació a un objecte existent que tingui la columna tècnica obligatòria (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Modifiqueu l'objecte de destinació.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=L'objecte de destinació no es pot utilitzar perquè no té la columna tècnica obligatòria __load_record_id. Podeu canviar el nom de l'objecte de destinació fent servir un nom que encara no existeixi. Aleshores, el sistema crea un objecte nou que té la mateixa definició que l'objecte d'origen i que conté la columna tècnica. Alternativament, podeu assignar l'objecte de destinació a un objecte existent que tingui la columna tècnica obligatòria (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Modifiqueu l'objecte de destinació.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=L'objecte de destinació no es pot utilitzar perquè el tipus de dades de la columna tècnica __load_record_id no és "string(44)". Podeu canviar el nom de l'objecte de destinació fent servir un nom que encara no existeixi. Aleshores, el sistema crea un objecte nou que té la mateixa definició que l'objecte d'origen i, per tant, el tipus de dades correcte. Alternativament, podeu assignar l'objecte de destinació a un objecte existent que tingui la columna tècnica obligatòria (__load_record_id) amb el tipus de dades correcte.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Modifiqueu l'objecte de destinació.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=L'objecte de destinació no es pot utilitzar perquè té una clau primària, mentre que l'objecte d'origen no en té cap. Podeu canviar el nom de l'objecte de destinació fent servir un nom que encara no existeixi. Aleshores, el sistema crea un objecte nou que té la mateixa definició que l'objecte d'origen i, per tant, cap clau primària. Alternativament, podeu assignar l'objecte de destinació a un objecte existent que tingui la columna tècnica obligatòria (__load_package_id) i no tingui una clau primària.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Modifiqueu l'objecte de destinació.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=L'objecte de destinació no es pot utilitzar perquè té una clau primària, mentre que l'objecte d'origen no en té cap. Podeu canviar el nom de l'objecte de destinació fent servir un nom que encara no existeixi. Aleshores, el sistema crea un objecte nou que té la mateixa definició que l'objecte d'origen i, per tant, cap clau primària. Alternativament, podeu assignar l'objecte de destinació a un objecte existent que tingui la columna tècnica obligatòria (__load_record_id) i no tingui una clau primària.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Modifiqueu l'objecte de destinació.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=L'objecte de destinació no es pot utilitzar perquè el tipus de dades de la columna tècnica __load_package_id no és "binary(>=256)". Podeu canviar el nom de l'objecte de destinació fent servir un nom que encara no existeixi. Aleshores, el sistema crea un objecte nou que té la mateixa definició que l'objecte d'origen i, per tant, el tipus de dades correcte. Alternativament, podeu assignar l'objecte de destinació a un objecte existent que tingui la columna tècnica obligatòria (__load_package_id) amb el tipus de dades correcte.
#XMSG
validationAutoRenameTargetDPID=S'ha canviat el nom de les columnes de destinació.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Suprimeix l'objecte font.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=L'objecte font no té una columna clau, que no es permet en aquest context.
#XMSG
validationAutoRenameTargetDPIDDescription=S''ha afegit una projecció automàtica i s''ha canviat el nom de les següents columnes de destinació per permetre''n la replicació desde una font ABAP sense claus:{1}{1} {0} {1}{1}La causa és una de les següents:{1}{1}{2} Nom de columna reservat{1}{2} Caràcters no permesos{1}{2} Prefix reservat
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Replicació a {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Desar i desplegar els fluxos de replicació que tenen {0} com al seu destí actualment no és possible perquè estem realitzant el manteniment d''aquesta funció.
#XMSG
TargetColumnSkippedLTF=S'ha omès la columna destí.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=La columna destí s''ha omès a causa d''un tipus de dades no compatible. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Columna de temps com a clau primària.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=L'objecte font té una columna de temps com a clau primària, que no es permet en aquest context.
#XMSG
validateNoPKInLTFTarget=Falta la clau primària.
#XMSG
validateNoPKInLTFTargetDescription=La clau primària no està definida al destí, que no es permet en aquest context.
#XMSG
validateABAPClusterTableLTF=Taula de clúster ABAP.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=L'objecte font es una taula de clúster ABAP, que no es permet en aquest context.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Sembla que encara no has afegit cap dada.
#YINS
welcomeText2=Per iniciar el vostre flux de rèplica, seleccioneu una connexió i un objecte font al costat esquerre.

#XBUT
wizStep1=Seleccionar la connexió d’origen
#XBUT
wizStep2=Seleccioneu el contenidor d'origen
#XBUT
wizStep3=Afegeix objectes font

#XMSG
limitDataset=S'ha arribat al nombre màxim d'objectes. Elimineu els objectes existents per afegir-ne de nous o creeu un flux de replicació.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=El flux de replicació per a aquesta connexió de destinació no de SAP no es pot iniciar perquè no hi ha cap volum de sortida disponible per a aquest mes.
#XMSG
premiumOutBoundRFAdminWarningMsg=Un administrador pot incrementar els blocs de sortida prèmium per a aquest arrendatari, fent que hi hagi volum de sortida disponible per a aquest mes.
#XMSG
messageForToastForDPIDColumn2=S''ha afegit una columna nova per a {0} objectes - que és necessària per a gestionar registres duplicats relacionats amb objectes font basats en ABAP que no tenen una clau primària.
#XMSG
PremiumInboundWarningMessage=Segons el nombre de fluxos de replicació i el volum de dades que s''hagin de replicar, els recursos de SAP HANA{0} necessaris per replicar dades a {1} poden superar la capacitat disponible per al vostre arrendatari.
#XMSG
PremiumInboundWarningMsg=Segons el nombre de fluxos de replicació i el volum de dades que s''hagin de replicar, els recursos de SAP HANA{0} necessaris per replicar dades a "{1}" poden superar la capacitat disponible per al vostre arrendatari.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Introduïu un nom de projecció.
#XMSG
emptyTargetColumn=Introduïu un nom de columna de destinació.
#XMSG
emptyTargetColumnBusinessName=Introduïu un nom empresarial de columna de destinació.
#XMSG
invalidTransformName=Introduïu un nom de projecció.
#XMSG
uniqueColumnName=Canvieu el nom de la columna de destinació.
#XMSG
copySourceColumnLbl=Copia les columnes a partir de l'objecte font
#XMSG
renameWarning=Assegureu-vos que escolliu un nom únic quan canvieu el nom de la taula de destinació. Si la taula amb el nom nou ja existeix a l'espai, s'utilitzarà la definició d'aquesta taula.

#XMSG
uniqueColumnBusinessName=Redefinir el nom empresarial de la columna de destinació
#XMSG
uniqueSourceMapping=Seleccioneu una altra columna d'origen.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=La columna d''origen {0} ja s''utilitza a les columnes de destinació següents:{1}{1}{2}{1}{1} Per a aquesta columna de destinació o per a les altres, seleccioneu una columna d''origen que no s''estigui utilitzant per desar la projecció.
#XMSG
uniqueColumnNameDescription=El nom de la columna de destinació que heu indicat ja existeix. Per poder desar la projecció, cal que introduïu un nom de columna unívoc.
#XMSG
uniqueColumnBusinessNameDesc=El nom empresarial de la columna de destinació ja existeix. Per desar la projecció, heu d'introduir un nom empresarial de columna unívoc.
#XMSG
emptySource=Seleccioneu una columna d'origen o introduïu una constant.
#XMSG
emptySourceDescription=Per crear una entrada d'assignació vàlida, heu de seleccionar una columna d'origen o introduir un valor de constant.
#XMSG
emptyExpression=Definiu l'assignació.
#XMSG
emptyExpressionDescription1=Seleccioneu la columna d''origen a la qual voleu assignar la columna de destinació o marqueu la casella de selecció de la columna {0} Funcions/Constants {1}. {2} {2} Les funcions s''introdueixen automàticament segons el tipus de dades de destinació. Els valors constants es poden introduir manualment.
#XMSG
numberExpressionErr=Introduïu un número.
#XMSG
numberExpressionErrDescription=Heu seleccionat un tipus de dades numèriques. Això implica que només podeu introduir números, més la coma decimal, si escau. No feu servir cometes senzilles.
#XMSG
invalidLength=Introduïu un valor de longitud vàlid.
#XMSG
invalidLengthDescription=La longitud del tipus de dades ha ser igual o més gran que la longitud de la columna d'origen i pot estar entre 1 i 5000.
#XMSG
invalidMappedLength=Introduïu un valor de longitud vàlid.
#XMSG
invalidMappedLengthDescription=La longitud del tipus de dades ha ser igual o més gran que la longitud de la columna d''origen {0} i pot estar entre 1 i 5000.
#XMSG
invalidPrecision=Introduïu un valor de precisió vàlid.
#XMSG
invalidPrecisionDescription=La precisió defineix el nombre de dígits. L''escala defineix el nombre de dígits després de la coma decimal, que pot estar entre 0 i la precisió definida.{0}{0} Exemples: {0}{1} Per a una precisió 6, l''escala 2 es correspon a números com ara 1234,56.{0}{1} Per a una precisió 6, l''escala 6 es correspon a números com ara 0,123546.{0} {0} La precisió i l''escala de la destinació han de ser compatibles amb la precisió i l''escala d''origen, de manera que tots els dígits de l''origen encaixin al camp de destinació. Per exemple, si tenim una precisió 6 i una escala 2 a l''origen (i, per tant, dígits que no són 0 davant de la coma decimal), no podem tenir una precisió 6 i una escala 6 a la destinació.
#XMSG
invalidPrimaryKey=Introduïu una clau primària, com a mínim.
#XMSG
invalidPrimaryKeyDescription=No s'ha definit una clau primària per a aquest esquema.
#XMSG
invalidMappedPrecision=Introduïu un valor de precisió vàlid.
#XMSG
invalidMappedPrecisionDescription1=La precisió defineix el nombre total de dígits. L''escala defineix el nombre de dígits després de la coma decimal, que pot estar entre 0 i la precisió definida.{0}{0} Exemples:{0}{1} Per a una precisió 6, l''escala 2 es correspon amb números com ara 1234,56.{0}{1} Per a una precisió de 6, l''escala 6 es correspon a números com ara 0,123546.{0}{0}La precisió del tipus de dades ha de ser igual o més gran que la precisió de l''origen ({2}).
#XMSG
invalidScale=Introduïu un valor d'escala vàlid.
#XMSG
invalidScaleDescription=La precisió defineix el nombre de dígits. L''escala defineix el nombre de dígits després de la coma decimal, que pot estar entre 0 i la precisió definida.{0}{0} Exemples: {0}{1} Per a una precisió 6, l''escala 2 es correspon a números com ara 1234,56.{0}{1} Per a una precisió 6, l''escala 6 es correspon a números com ara 0,123546.{0} {0} La precisió i l''escala de la destinació han de ser compatibles amb la precisió i l''escala d''origen, de manera que tots els dígits de l''origen encaixin al camp de destinació. Per exemple, si tenim una precisió 6 i una escala 2 a l''origen (i, per tant, dígits que no són 0 davant de la coma decimal), no podem tenir una precisió 6 i una escala 6 a la destinació.
#XMSG
invalidMappedScale=Introduïu un valor d'escala vàlid.
#XMSG
invalidMappedScaleDescription1=La precisió defineix el nombre total de dígits. L''escala defineix el nombre de dígits després de la coma decimal, que pot estar entre 0 i la precisió definida.{0}{0} Exemples:{0}{1} Per a una precisió 6, l''escala 2 es correspon amb números com ara 1234,56.{0}{1} Per a una precisió de 6, l''escala 6 es correspon a números com ara 0,123546.{0}{0}L''escala del tipus de dades ha de ser igual o més gran que l''escala de l''origen ({2}).
#XMSG
nonCompatibleDataType=Seleccioneu un tipus de dades de destinació compatible.
#XMSG
nonCompatibleDataTypeDescription1=El tipus de dades que especifiqueu aquí ha de ser compatible amb el tipus de dades d''origen ({0}). {1}{1}Exemple: si la columna d''origen té una cadena de tipus de dades i conté lletres, no podeu utilitzar un tipus de dades decimal per a la destinació.
#XMSG
invalidColumnCount=Seleccioneu una columna d'origen.
#XMSG
ObjectStoreInvalidScaleORPrecision=Introduïu un valor vàlid per a precisió i escala.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=El primer valor és la precisió, que determina el nombre total de dígits. El segon valor és l'escala, que defineix els dígits després de la coma decimal. Introduïu un valor d'escala de destinació més gran que el valor d'escala d'origen i comproveu que la diferència entre l'escala de destinació indicada i el valor de precisió sigui més gran que la diferència entre l'escala d'origen i el valor de precisió.
#XMSG
InvalidPrecisionORScale=Introduïu un valor vàlid per a precisió i escala.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=El primer valor és la precisió, que defineix el nombre total de dígits. El segon és l''escala, que defineix els dígits posteriors a la coma decimal.{0}{0}Com que el tipus de dades font no és compatible amb Google BigQuery, es converteix al tipus de dades de destinació DECIMAL. En aquest cas, la precisió només es pot definir entre 38 i 76, i l''escala entre 9 i 38. A més, el resultat de la precisió menys l''escala (que representa els dígits anteriors a la coma decimal) ha d''estar entre 29 i 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=El primer valor és la precisió, que defineix el nombre total de dígits. El segon és l''escala, que defineix els dígits posteriors a la coma decimal.{0}{0}Com que el tipus de dades font no és compatible amb Google BigQuery, es converteix al tipus de dades de destinació DECIMAL. En aquest cas, la precisió s''ha de definir com a 20 o superior. A més, el resultat de la precisió menys l''escala (que representa els dígits anteriors a la coma decimal) ha de ser 20 o més.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=El primer valor es la precisió, que defineix el nombre total de dígits. El segon valor es l''escala, que defineix els dígits després del punt de decimal.{0}{0}Com que no es permet el tipus de dades font al destí, es converteix al tipus de dades destí DECIMAL. En aquest cas, la precisió cal definir-se amb qualsevol nombre superior o igual a 1 i menor o igual a 38, i l''escala és menor o igual a la precisió.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=El primer valor es la precisió, que defineix el nombre total de dígits. El segon valor es l''escala, que defineix els dígits després del punt de decimal.{0}{0}Com que no es permet el tipus de dades font al destí, es converteix al tipus de dades destí DECIMAL. En aquest cas, la precisió cal definir-se amb qualsevol nombre superior o igual a 20. A més, el resultat de la precisió menys l''escala, que reflecteix els dígits abans del punt de decimal, ha de ser superior o igual a 20.
#XMSG
invalidColumnCountDescription=Per crear una entrada d'assignació vàlida, heu de seleccionar una columna d'origen o introduir un valor de constant.
#XMSG
duplicateColumns=Canvieu el nom de la columna de destinació.
#XMSG
duplicateGBQCDCColumnsDesc=El nom de la columna de destinació està reservat a Google BigQuery. Cal que el modifiqueu per poder desar la projecció.
#XMSG
duplicateConfluentCDCColumnsDesc=El nom de la columna de destinació està reservat a Confluent. Cal que el modifiqueu per poder desar la projecció.
#XMSG
duplicateSignavioCDCColumnsDesc=El nom de la columna de destinació està reservat a SAP Signavio. Cal que el modifiqueu per poder desar la projecció.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Es reserva el nom de columna de destinació a MS OneLake. Cal canviar-li el nom per a poder desar la projecció.
#XMSG
duplicateSFTPCDCColumnsDesc=El nom de la columna de destinació està reservat a SFTP. Cal canviar-li el nom per poder desar la projecció.
#XMSG
GBQTargetNameWithPrefixUpdated1=La columna de destinació inclou un prefix que està reservat a Google BigQuery. Cal que la reanomeneu per poder desar la projecció.{0}{0}El nom de la columna de destinació no pot començar amb cap de les cadenes següents:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Escurceu el nom de la columna de destinació.
#XMSG
GBQtargetMaxLengthDesc=A Google BigQuery, un nom de columna pot utilitzar un màxim de 300 caràcters. Escurceu el nom de la columna de destinació per poder desar la projecció.
#XMSG
invalidMappedScalePrecision=La precisió i l'escala de la destinació han de ser compatibles amb la precisió i l'escala de l'origen, de manera que tots els dígits de l'origen encaixin en el camp de destinació.
#XMSG
invalidMappedScalePrecisionShortText=Introduïu un valor de precisió i escala vàlid.
#XMSG
validationIncompatiblePKTypeDescProjection3=Una o més columnes d''origen tenen tipus de dades que no es poden definir com a claus primàries a Google BigQuery. No es crearà cap clau primària a l''objecte de destinació.{0}{0}Els tipus de dades de destinació següents són compatibles amb els tipus de dades de Google BigQuery per als quals es pot definir una clau primària:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Desmarqueu column __message_id.
#XMSG
uncheckColumnMessageIdDesc=Columna: clau primària
#XMSG
validationOpCodeInsert=Heu d'introduir un valor per a Inserir.
#XMSG
recommendDifferentPrimaryKey=Us recomanem que seleccioneu una clau primària diferent a nivell de posició.
#XMSG
recommendDifferentPrimaryKeyDesc=Quan ja s'ha definit el codi d'operació, es recomana seleccionar claus primàries diferents per a l'índex de matriu i les posicions, per evitar problemes com la duplicació de columnes, per exemple.
#XMSG
selectPrimaryKeyItemLevel=Cal que seleccioneu com a mínim una clau primària per als nivells de capçalera i de posició.
#XMSG
selectPrimaryKeyItemLevelDesc=Quan una matriu o un mapa s'expandeix, cal que seleccioneu dues claus primàries, una a nivell de capçalera i una altra a nivell de posició.
#XMSG
invalidMapKey=Cal que seleccioneu com a mínim una clau primària a nivell de capçalera.
#XMSG
invalidMapKeyDesc=Quan una matriu o un mapa s'expandeix, cal que seleccioneu una clau primària a nivell de capçalera.
#XFLD
txtSearchFields=Cerqueu columnes de destinació
#XFLD
txtName=Nom
#XMSG
txtSourceColValidation=No s'admeten una o més columnes font:
#XMSG
txtMappingCount=Assignacions ({0})
#XMSG
schema=Esquema
#XMSG
sourceColumn=Columnes font
#XMSG
warningSourceSchema=Qualsevol modificació aplicada a l'esquema afectarà a les assignacions del diàleg de projecció.
#XCOL
txtTargetColName=Columna de destinació (nom tècnic)
#XCOL
txtDataType=Tipus de dades de destinació
#XCOL
txtSourceDataType=Tipus de dades d'origen
#XCOL
srcColName=Columna d’origen (nom tècnic)
#XCOL
precision=Precisió
#XCOL
scale=Escala
#XCOL
functionsOrConstants=Funcions / Constants
#XCOL
txtTargetColBusinessName=Columna de destinació (nom empresarial)
#XCOL
prKey=Clau primària
#XCOL
txtProperties=Propietats
#XBUT
txtOK=Desar
#XBUT
txtCancel=Cancel·lar
#XBUT
txtRemove=Eliminar
#XFLD
txtDesc=Descripció
#XMSG
rftdMapping=Assignació
#XFLD
@lblColumnDataType=Tipus de dades
#XFLD
@lblColumnTechnicalName=Nom tècnic
#XBUT
txtAutomap=Assignació automàtica
#XBUT
txtUp=Amunt
#XBUT
txtDown=Avall

#XTOL
txtTransformationHeader=Projecció
#XTOL
editTransformation=Editar
#XTOL
primaryKeyToolip=Clau


#XMSG
rftdFilter=Filtre
#XMSG
rftdFilterColumnCount=Font: {0}({1})
#XTOL
rftdFilterColSearch=Cercar
#XMSG
rftdFilterColNoData=No hi ha columnes per mostrar
#XMSG
rftdFilteredColNoExps=Cap expressió de filtre
#XMSG
rftdFilterSelectedColTxt=Afegeix un filtre per
#XMSG
rftdFilterTxt=Filtre disponible per
#XBUT
rftdFilterSelectedAddColExp=Afegeix expressió
#YINS
rftdFilterNoSelectedCol=Seleccioneu una columna per afegir un filtre.
#XMSG
rftdFilterExp=Expressió de filtre
#XMSG
rftdFilterNotAllowedColumn=No es permet afegir filtres per a aquesta columna.
#XMSG
rftdFilterNotAllowedHead=Columna no permesa
#XMSG
rftdFilterNoExp=No s'ha definit cap filtre
#XTOL
rftdfilteredTt=Filtrat
#XTOL
rftdremoveexpTt=Elimina l'expressió del filtre
#XTOL
validationMessageTt=Missatges de validació
#XTOL
rftdFilterDateInp=Seleccioneu una data
#XTOL
rftdFilterDateTimeInp=Seleccioneu una data i hora
#XTOL
rftdFilterTimeInp=Seleccioneu una hora
#XTOL
rftdFilterInp=Introduïu un valor
#XMSG
rftdFilterValidateEmptyMsg={0} expressions de filtre de la columna {1} estan buides
#XMSG
rftdFilterValidateInvalidNumericMsg={0} expressions de filtre de la columna {1} contenen valors numèrics no vàlids
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=L'expressió de filtre ha de contenir valors de filtre vàlids
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Si l'esquema d'objecte de destinació ha canviat, utilitzeu la funció "Assignar a objecte de destinació existent" a la pàgina principal per adaptar el canvis i tornar a assignar l'objecte de destinació amb la seva font.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Si la taula de destinació ja existeix i l'assignació inclou un canvi d'esquema, heu de modificar la taula de destinació en conseqüència abans de desplegar el flux de replicació.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Si l'assignació implica un canvi d'esquema, heu de modificar la taula de destinació en conseqüència abans de desplegar el flux de replicació.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Les següents columnes no admeses s''han omès a la definició d''origen: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Les següents columnes no admeses s''han omès a la definició de destinació: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Els objectes següents no s''admeten perquè estan exposats per al consum: {0} {1} {0} {0} Per utilitzar taules en un flux de replicació, l''ús semàntic (a les opcions de taula) no pot estar fixat en {2}Conjunt de dades analítiques{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=L''objecte de destinació no es pot utilitzar perquè està exposat per al consum: {0} {0} Per utilitzar la taula en un flux de replicació, l''ús semàntic (a les opcions de taula) no pot estar fixat en {1}Conjunt de dades analítiques{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Ja existeix un objecte de destinació amb aquest nom. Tanmateix, no es pot utilitzar perquè està exposat per al consum: {0} {0} Per utilitzar la taula en un flux de replicació, l''ús semàntic (a les opcions de taula) no pot estar fixat en {1}Conjunt de dades analítiques{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Ja existeix un objecte amb aquest nom a la destinació. {0}Tanmateix, aquest objecte no es pot utilitzar com a objecte de destinació per a un flux de replicació en el dipòsit local, atès que no és una taula local.
#XMSG:
targetAutoRenameUpdated=S'ha reanomenat la columna de destinació.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=S''ha modificat el nom de la columna de destinació per tal d''admetre les aplicacions de Google BigQuery. Això és per un dels motius següents: {0} {1}{2}Nom de columna reservat{3}{2}Caràcters no admesos{3}{2}Prefix reservat{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=S''ha modificat el nom de la columna de destinació per tal d''admetre replicacions a Confluent. Això és per un dels motius següents: {0} {1}{2}Nom de columna reservat{3}{2}Caràcters no admesos{3}{2}Prefix reservat{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=S''ha modificat el nom de la columna de destinació per tal d''admetre replicacions al destí. Això és per un dels motius següents: {0} {1}{2}Caràcters no admesos{3}{2}Prefix reservat{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=S''ha modificat el nom de la columna de destinació per tal d''admetre replicacions al destí. Això és per un dels motius següents: {0} {1}{2}Nom de columna resevat{3}{2}Caràcters no admesos{3}{2}Prefix reservat{3}{4}
#XMSG:
targetAutoDataType=S'ha modificat el tipus de dades de destinació.
#XMSG:
targetAutoDataTypeDesc=El tipus de dades de destinació s''ha modificat a {0} perquè el tipus de dades de destinació no és compatible amb Google BigQuery.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=El tipus de dades de destinació s''ha modificat a {0} perquè el tipus de dades de destinació no és compatible amb la connexió destí.
#XMSG
projectionGBQUnableToCreateKey=No es crearan claus primàries.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=A Google BigQuery, s'admeten un màxim de 16 claus primàries, però l'objecte d'origen en té més. No es crearà cap de les claus primàries a l'objecte de destinació.
#XMSG
HDLFNoKeyError=Definiu una o més columnes com a clau primària.
#XMSG
HDLFNoKeyErrorDescription=Per replicar un objecte, heu de definir una o més columnes com a clau primària.
#XMSG
HDLFNoKeyErrorExistingTarget=Definiu una o més columnes com a clau primària.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Per replicar dades en un objecte de destinació existent, aquest ha de tenir una o més columnes definides com a clau primària. {0} {0} Teniu les opcions següents per definir una o més columnes com a clau primària: {0}{0}{1} Utilitzeu l''editor de taules local per modificar l''objecte de destinació existent. En acabat, torneu a carregar el flux de replicació.{0}{0}{1} Canvieu el nom de l''objecte de destinació en el flux de replicació. D''aquesta manera es crearà un objecte nou tal com s''iniciï una execució. Un cop canviat el nom, podeu definir una o més columnes com a clau primària a la projecció.{0}{0}{1} Assigneu l''objecte a un altre objecte de destinació existent en el qual hi hagi una o més columnes ja definides com a clau primària.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Clau primària modificada.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=En comparació amb l''objecte d''origen, heu definit diferents columnes com a clau primària per a l''objecte de destinació. Comproveu que aquestes columnes identifiquin de forma unívoca totes les files per evitar que es puguin malmetre les dades en replicar les dades més endavant. {0} {0} A l''objecte d''origen, les columnes següents estan definides com a clau primària: {0} {1}
#XMSG
duplicateDPIDColumns=Canvieu el nom de la columna de destinació.
#XMSG
duplicateDPIDDColumnsDesc1=Aquesta columna de destinació està reservada per a una columna tècnica. Introduïu un altre nom per desar la projecció.
#XMSG:
targetAutoRenameDPID=S'ha canviat el nom de la columna de destinació.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=S''ha modificat el nom de la columna de destinació per tal d''admetre replicacions des d''una font ABAP sense claus. Això és per un dels motius següents: {0} {1}{2}Nom de columna reservat{3}{2}Caràcters no admesos{3}{2}Prefix reservat{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle=Opcions de destinació de {0}
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle=Opcions d''origen de {0}
#XBUT
connectionSettingSave=Desar
#XBUT
connectionSettingCancel=Cancel·lar
#XBUT: Button to keep the object level settings
txtKeep=Conservar
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Sobreescriure
#XFLD
targetConnectionThreadlimit=Límit de fils de destinació per a càrrega inicial (1-100)
#XFLD
connectionThreadLimit=Límit de fils d'origen per a càrrega inicial (1-100)
#XFLD
maxConnection=Límit de fils de replicació (1-100)
#XFLD
kafkaNumberOfPartitions=Número de particions
#XFLD
kafkaReplicationFactor=Factor de replicació
#XFLD
kafkaMessageEncoder=Codificador de missatges
#XFLD
kafkaMessageCompression=Compressió de missatges
#XFLD
fileGroupDeltaFilesBy=Grup Delta per
#XFLD
fileFormat=Tipus de fitxer
#XFLD
csvEncoding=Codificació CSV
#XFLD
abapExitLbl=Exit ABAP
#XFLD
deltaPartition=Recompte de fils d'objecte per a càrregues delta (1-10)
#XFLD
clamping_Data=Error en truncar les dades
#XFLD
fail_On_Incompatible=Error per dades incompatibles
#XFLD
maxPartitionInput=Número màxim de particions
#XFLD
max_Partition=Definir el número màxim de particions
#XFLD
include_SubFolder=Incloure subcarpetes
#XFLD
fileGlobalPattern=Patró global per a nom de fitxer
#XFLD
fileCompression=Compressió de fitxers
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Delimitador de fitxers
#XFLD
fileIsHeaderIncluded=Capçalera del fitxer
#XFLD
fileOrient=Orientació
#XFLD
gbqWriteMode=Mode d'escriptura
#XFLD
suppressDuplicate=Suprimir duplicats
#XFLD
apacheSpark=Activar compatibilitat amb Apache Spark
#XFLD
clampingDatatypeCb=Restringir tipus de dades de coma flotant decimal
#XFLD
overwriteDatasetSetting=Sobreescriure opcions de destinació en el nivell d'objecte
#XFLD
overwriteSourceDatasetSetting=Sobreescriure opcions d'origen en el nivell d'objecte
#XMSG
kafkaInvalidConnectionSetting=Introduïu un nombre entre {0} i {1}.
#XMSG
MinReplicationThreadErrorMsg=Introduïu un número més gran que {0}.
#XMSG
MaxReplicationThreadErrorMsg=Introduïu un número més petit que {0}.
#XMSG
DeltaThreadErrorMsg=Introduïu un valor entre 1 i 10.
#XMSG
MaxPartitionErrorMsg=Introduïu un valor entre 1 <= x <= 2147483647. El valor predeterminat és 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Introduïu un enter entre {0} i {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Utilitzar factor de replicació del corredor
#XFLD
serializationFormat=Format de serialització
#XFLD
compressionType=Tipus de compressió
#XFLD
schemaRegistry=Utilitzar registre d'esquemes
#XFLD
subjectNameStrat=Estratègia per al nom d'assumpte
#XFLD
compatibilityType=Tipus de compatibilitat
#XFLD
confluentTopicName=Nom del tema
#XFLD
confluentRecordName=Nom del registre
#XFLD
confluentSubjectNamePreview=Previsualització del nom de l'assumpte
#XMSG
serializationChangeToastMsgUpdated2=El format de serialització s'ha modificat a JSON perquè el registre d'esquemes no està activat. Per tornar a fixar el format de serialització en AVRO, abans heu d'activar el registre d'esquemes.
#XBUT
confluentTopicNameInfo=El nom del tema sempre es basa en el nom de l'objecte de destinació. Podeu modificar-lo canviant el nom de l'objecte de destinació.
#XMSG
emptyRecordNameValidationHeaderMsg=Introduïu un nom per al registre.
#XMSG
emptyPartionHeader=Indiqueu el número de particions.
#XMSG
invalidPartitionsHeader=Indiqueu un número de particions vàlid.
#XMSG
invalidpartitionsDesc=Introduïu un nombre entre 1 i 200.000.
#XMSG
emptyrFactorHeader=Introduïu un factor de replicació.
#XMSG
invalidrFactorHeader=Introduïu un factor de replicació vàlid.
#XMSG
invalidrFactorDesc=Introduïu un número entre 1 i 32.767.
#XMSG
emptyRecordNameValidationDescMsg=Si s''utilitza el format de serialització "AVRO", només s''admeten els caràcters següents:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(guionet baix)
#XMSG
validRecordNameValidationHeaderMsg=Introduïu un nom de registre vàlid.
#XMSG
validRecordNameValidationDescMsgUpdated=Com que s'utilitza el format de serialització "AVRO", el nom del registre ha d'estar format només per caràcters alfanumèrics (A-Z, a-z, 0-9) i de subratllat (_). Ha de començar per una lletra o un caràcter de subratllat.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=El “Recompte de fils d'objecte per a càrregues delta" es pot fixar tal com un o més objectes tenen el tipus de càrrega “Inicial i delta”.
#XMSG
invalidTargetName=Nom de columna no vàlid
#XMSG
invalidTargetNameDesc=El nom de la columna de destinació només pot contenir caràcters alfanumèrics (A-Z, a-z, 0-9) i de subratllat (_).
#XFLD
consumeOtherSchema=Consum d'altres versions d'esquema
#XFLD
ignoreSchemamissmatch=Ignora la manca de coincidència d'esquema
#XFLD
confleuntDatatruncation=Error en truncar les dades
#XFLD
isolationLevel=Nivell d'aïllament
#XFLD
confluentOffset=Punt inicial
#XFLD
signavioGroupDeltaFilesByText=Cap
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=No
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=No

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Projeccions
#XBUT
txtAdd=Afegir
#XBUT
txtEdit=Editar
#XMSG
transformationText=Afegiu una projecció per configurar el filtre o l'assignació.
#XMSG
primaryKeyRequiredText=Seleccioneu una clau primària amb Configurar esquema
#XFLD
lblSettings=Configuració
#XFLD
lblTargetSetting={0}: Opcions de destinació
#XMSG
@csvRF=Seleccioneu el fitxer que conté la definició d'esquema que voleu aplicar a tots els fitxers de la carpeta.
#XFLD
lblSourceColumns=Columnes font
#XFLD
lblJsonStructure=Estructura JSON
#XFLD
lblSourceSetting={0}: opcions d''origen
#XFLD
lblSourceSchemaSetting={0}: opcions d''esquema d''origen
#XBUT
messageSettings=Configuració dels missatges
#XFLD
lblPropertyTitle1=Propietats d'objecte
#XFLD
lblRFPropertyTitle=Propietats del flux de replicació
#XMSG
noDataTxt=No hi ha columnes per visualitzar.
#XMSG
noTargetObjectText=No hi ha cap objecte de destinació seleccionat.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Columnes de destinació
#XMSG
searchColumns=Cercar columnes
#XTOL
cdcColumnTooltip=Columna per a la captura delta
#XMSG
sourceNonDeltaSupportErrorUpdated=L'objecte d'origen no admet una captura delta.
#XMSG
targetCDCColumnAdded=S'han afegit dues columnes de destinació per a la captura delta.
#XMSG
deltaPartitionEnable=Límit de fils d'objecte per a càrregues delta afegit a les opcions d'origen.
#XMSG
attributeMappingRemovalTxt=S'estan eliminant les assignacions no vàlides que no són compatibles amb el nou objecte de destinació.
#XMSG
targetCDCColumnRemoved=S'han eliminat dues columnes de destinació utilitzades per a la captura delta.
#XMSG
replicationLoadTypeChanged=El tipus de càrrega s'ha modificat a "Inicial i delta".
#XMSG
sourceHDLFLoadTypeError=Modifiqueu el tipus de càrrega a "Inicial i delta".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Per replicar un objecte des d'una connexió d'origen amb el tipus de connexió SAP HANA Cloud, Data Lake Files, a SAP Datasphere, heu d'utilitzar el tipus de càrrega "inicial i delta".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Activeu la captura delta.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Per replicar un objecte des d'una connexió d'origen amb el tipus de connexió SAP HANA Cloud, Data Lake Files, a SAP Datasphere, heu d'activar la captura delta.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Modifiqueu l'objecte de destinació.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=L'objecte de destinació no es pot utilitzar perquè la captura delta està desactivada. Podeu reanomenar l'objecte de destinació (que permet crear un objecte nou amb captura delta) o assignar-lo a un objecte existent amb la captura delta activada.
#XMSG
deltaPartitionError=Introduïu un recompte de fils d'objecte vàlid per a càrregues delta.
#XMSG
deltaPartitionErrorDescription=Introduïu un valor entre 1 i 10.
#XMSG
deltaPartitionEmptyError=Introduïu un recompte de fils d'objecte per a càrregues delta.
#XFLD
@lblColumnDescription=Descripció
#XMSG
@lblColumnDescriptionText1=Amb finalitats tècniques: gestió de registres duplicats generats per problemes durant la replicació d'objectes d'origen basat en ABAP que no tenen una clau primària.
#XFLD
storageType=Emmagatzematge
#XFLD
skipUnmappedColLbl=Ometre columnes no assignades
#XFLD
abapContentTypeLbl=Tipus de contingut
#XFLD
autoMergeForTargetLbl=Fusionar dades automàticament
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=General
#XFLD
lblBusinessName=Nom empresarial
#XFLD
lblTechnicalName=Nom tècnic
#XFLD
lblPackage=Paquet
#XFLD
statusPanel=Estat d’execució
#XBTN: Schedule dropdown menu
SCHEDULE=Programació
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Editar programació
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Suprimir programació
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Crear programació
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=No s'ha pogut verificar la validació de la programació
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=No es pot crear una programació perquè el flux de replicació s''està desplegant.{0}Espereu que s''hagi desplegat el flux de replicació.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=No es pot crear cap programació per als fluxos de replicació que contenen objectes amb el tipus de càrrega "Inicial i delta".
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=No es pot crear cap programació per als fluxos de replicació que contenen objectes amb el tipus de càrrega "Inicial i delta/Només delta".
#XFLD : Scheduled popover
SCHEDULED=Programat
#XFLD
CREATE_REPLICATION_TEXT=Crear un flux de replicació
#XFLD
EDIT_REPLICATION_TEXT=Editar un flux de replicació
#XFLD
DELETE_REPLICATION_TEXT=Suprimir un flux de replicació
#XFLD
REFRESH_FREQUENCY=Freqüència
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=El flux de replicació no es pot desplegar perquè la programació{0} encara no admet el tipus de càrrega "Inicial i delta".{0}{0}Per desplegar-lo, heu d''establir els tipus de càrrega de tots els objectes{0} en "Només inicial". Com a alternativa, podeu suprimir la programació, desplegar el {0}flux de replicació i, després, iniciar una nova execució. Això donaria lloc a una execució sense {0}fi, cosa que també admet objectes amb el tipus de càrrega "Inicial i delta".
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=El flux de replicació no es pot desplegar perquè la programació{0} encara no admet el tipus de càrrega "Inicial i delta/Només delta".{0}{0}Per desplegar-lo, heu d''establir els tipus de càrrega de tots els objectes{0} en "Només inicial". Com a alternativa, podeu suprimir la programació, desplegar el {0}flux de replicació i, després, iniciar una nova execució. Això donaria lloc a una execució sense {0}fi, cosa que també admet objectes amb el tipus de càrrega "Inicial i delta/Només delta".
#XMSG
SCHEDULE_EXCEPTION=No s'han pogut obtenir els detalls de la programació
#XFLD: Label for frequency column
everyLabel=Cada
#XFLD: Plural Recurrence text for Hour
hoursLabel=Hores
#XFLD: Plural Recurrence text for Day
daysLabel=Dies
#XFLD: Plural Recurrence text for Month
monthsLabel=Mesos
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuts
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Error en obtenir informació de la possibilitat de programació.
#XFLD :Paused field
PAUSED=En pausa
#XMSG
navToMonitoring=Obrir en el monitor de fluxos
#XFLD
statusLbl=Estat
#XFLD
lblLastRunExecuted=Inici de l’última execució
#XFLD
lblLastExecuted=Última execució
#XFLD: Status text for Completed
statusCompleted=Conclòs
#XFLD: Status text for Running
statusRunning=En execució
#XFLD: Status text for Failed
statusFailed=Erroni
#XFLD: Status text for Stopped
statusStopped=Aturat
#XFLD: Status text for Stopping
statusStopping=Aturant
#XFLD: Status text for Active
statusActive=Actiu
#XFLD: Status text for Paused
statusPaused=En pausa
#XFLD: Status text for not executed
lblNotExecuted=Encara no s’ha executat
#XFLD
messagesSettings=Configuració dels missatges
#XTOL
@validateModel=Missatges de validació
#XTOL
@hierarchy=Jerarquia
#XTOL
@columnCount=Número de columnes
#XMSG
VAL_PACKAGE_CHANGED=Heu assignat aquest objecte al paquet "{1}". Feu clic a "Desar" per confirmar i validar aquesta modificació. Tingueu en compte que l''assignació a un paquet no es pot desfer en aquest editor un cop s''ha desat.
#XMSG
MISSING_DEPENDENCY=Les dependències de l''objecte "{0}" no es poden resoldre en el paquet "{1}".
#XFLD
deltaLoadInterval=Interval de càrrega delta
#XFLD
lblHour=Hores (0-24)
#XFLD
lblMinutes=Minuts (0-59)
#XMSG
maxHourOrMinErr=Indiqueu un valor comprès entre 0 i {0}
#XMSG
maxDeltaInterval=El valor màxim de l''interval de càrrega delta és de 24 hores.{0}Modifiqueu el valor del minut o de l''hora en conseqüència.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Ruta del contenidor objectiu
#XFLD
confluentSubjectName=Nom d'assumpte
#XFLD
confluentSchemaVersion=Versió d'esquema
#XFLD
confluentIncludeTechKeyUpdated=Clau tècnica d'include
#XFLD
confluentOmitNonExpandedArrays=Ometre matrius no expandides
#XFLD
confluentExpandArrayOrMap=Expandir matriu o mapa
#XCOL
confluentOperationMapping=Assignació d'operació
#XCOL
confluentOpCode=Opcode
#XFLD
confluentInsertOpCode=Inserir
#XFLD
confluentUpdateOpCode=Actualitzar
#XFLD
confluentDeleteOpCode=Suprimir
#XFLD
expandArrayOrMapNotSelectedTxt=No seleccionat
#XFLD
confluentSwitchTxtYes=Sí
#XFLD
confluentSwitchTxtNo=No
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Error
#XTIT
executeWarning=Advertència
#XMSG
executeunsavederror=Deseu el flux de replicació abans d’executar-lo.
#XMSG
executemodifiederror=Hi ha modificacions no desades al flux de replicació. Deseu el flux de replicació.
#XMSG
executeundeployederror=Heu de desplegar el vostre flux de rèplica abans de poder executar-lo.
#XMSG
executedeployingerror=Espereu que conclogui el desplegament.
#XMSG
msgRunStarted=Execució iniciada
#XMSG
msgExecuteFail=Error en executar el flux de replicació
#XMSG
titleExecuteBusy=Espereu.
#XMSG
msgExecuteBusy=Estem preparant les vostres dades per executar el flux de replicació.
#XTIT
executeConfirmDialog=Advertència
#XMSG
msgExecuteWithValidations=El flux de replicació té errors de validació. L’execució pot causar errors.
#XMSG
msgRunDeployedVersion=Hi ha modificacions per desplegar. S'executarà la darrera versió desplegada del flux de replicació. Voleu continuar?
#XBUT
btnExecuteAnyway=Executar igualment
#XBUT
btnExecuteClose=Tancar
#XBUT
loaderClose=Tancar
#XTIT
loaderTitle=Carregant
#XMSG
loaderText=S’estan obtenint detalls del servidor
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=El flux de replicació per a aquesta connexió de destinació no de SAP no es pot iniciar
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=perquè no hi ha un volum de sortida disponible per a aquest mes.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Un administrador pot incrementar els blocs de sortida prèmium per a aquest
#XMSG
premiumOutBoundRFAdminErrMsgPart2=arrendatari i fer que hi hagi volum de sortida disponible per a aquest mes.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Error
#XTIT
deployInfo=Informació
#XMSG
deployCheckFailException=S'ha produït una excepció durant el desplegament.
#XMSG
deployGBQFFDisabled=Actualment, no es poden implementar fluxos de replicació amb una connexió de destinació a Google BigQuery perquè estem fent tasques de manteniment en aquesta funció.
#XMSG
deployKAFKAFFDisabled=Actualment, no es poden implementar fluxos de replicació amb una connexió de destinació a Apache Kafka perquè estem fent tasques de manteniment en aquesta funció.
#XMSG
deployConfluentDisabled=Actualment, no es poden implementar fluxos de replicació amb una connexió de destinació a Confluent Kafka, perquè estem fent tasques de manteniment en aquesta funció.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Per als objectes de destinació següents, els noms de les taules de captura delta ja s''utilitzen en altres taules del dipòsit: {0}. Heu de canviar el nom d''aquests objectes de destinació per assegurar-vos que els noms de les taules de captura delta associats siguin unívocs abans de poder desplegar el flux de replicació.
#XMSG
deployDWCSourceFFDisabled=Actualment no es poden desplegar fluxos de replicació que tinguin SAP Datasphere com a font perquè estem fent tasques de manteniment en aquesta funció.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Actualment no es poden desplegar fluxos de replicació que tinguin taules locals compatibles amb delta com a objectes d'origen perquè estem fent tasques de manteniment en aquesta funció.
#XMSG
deployHDLFSourceFFDisabled=Actualment no es poden desplegar fluxos de replicació que tinguin connexions d'origen amb el tipus de connexió SAP HANA Cloud, data lake files perquè estem fent tasques de manteniment.
#XMSG
deployObjectStoreAsSourceFFDisabled=Actualment no es poden desplegar fluxos de replicació que tenen proveïdors d'emmagatzematge al núvol com a font.
#XMSG
deployConfluentSourceFFDisabled=Desplegar els fluxos de replicació que tenen Confluent Kafka com a la seva font actualment no és possible perquè estem realitzant el manteniment d'aquesta funció.
#XMSG
deployMaxDWCNewTableCrossed=En el cas de fluxos de replicació grans, no es poden "desar i desplegar" d'un sol cop. Primer deseu el flux de replicació i, després, desplegueu-lo.
#XMSG
deployInProgressInfo=El desplegament ja està en curs.
#XMSG
deploySourceObjectInUse=Els objectes font {0} ja s''estan utilitzant en fluxos de replicació {1}.
#XMSG
deployTargetSourceObjectInUse=Els objectes font {0} ja s''utilitzen en fluxos de replicació {1}. Els objectes de destinació {2} ja s''utilitzen en fluxos de replicació {3}.
#XMSG
deployReplicationFlowCheckError=Error en verificar el flux de replicació: {0}
#XMSG
preDeployTargetObjectInUse=Els objectes de destinació {0} ja s''estan utilitzant en fluxos de replicació {1} i no podeu tenir el mateix objecte de destinació en dos fluxos de replicació diferents. Seleccioneu un altre objecte de destinació i torneu a provar-ho.
#XMSG
runInProgressInfo=El flux de replicació ja s'està executant.
#XMSG
deploySignavioTargetFFDisabled=En aquests moments no es poden desplegar els fluxos de replicació que tenen SAP Signavio com el seu destí perquè estem realitzant el manteniment d'aquesta funció.
#XMSG
deployHanaViewAsSourceFFDisabled=Actualment no és possible desplegar fluxos de replicació que tinguin vistes com a objectes font per a la connexió font seleccionada. Intenteu-ho de nou més tard.
#XMSG
deployMsOneLakeTargetFFDisabled=Actualment no és possible desplegar fluxos de replicació que tinguin MS OneLake com a la seva destinació perquè estem realitzant un manteniment en aquesta funció.
#XMSG
deploySFTPTargetFFDisabled=Actualment no és possible implementar fluxos de replicació que tenen SFTP com a objectiu perquè estem realitzant tasques de manteniment en aquesta funció.
#XMSG
deploySFTPSourceFFDisabled=Actualment no és possible implementar fluxos de replicació que tenen SFTP com a origen perquè estem realitzant tasques de manteniment en aquesta funció.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Nom tècnic
#XFLD
businessNameInRenameTarget=Nom empresarial
#XTOL
renametargetDialogTitle=Reanomenar objecte de destinació
#XBUT
targetRenameButton=Reanomenar
#XBUT
targetRenameCancel=Cancel·lar
#XMSG
mandatoryTargetName=Heu d'introduir un nom.
#XMSG
dwcSpecialChar=_(subratllat) és l'únic caràcter especial permès.
#XMSG
dwcWithDot=El nom de la taula de destinació pot estar format per lletres llatines, números, caràcters de subratllat (_) i punts (.). El primer caràcter ha de ser una lletra, un número o un caràcter de subratllat (no un punt).
#XMSG
nonDwcSpecialChar=Els caràcters especials permesos són _(guió baix) - (guionet) .(punt)
#XMSG
firstUnderscorePattern=El nom no pot començar amb _ (caràcter de subratllat).

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: Visualitzar la sentència Taula de creació SQL
#XMSG
sqlDialogMaxPKWarning=A Google BigQuery, s'admeten un màxim de 16 claus primàries i l'objecte d'origen en té un gran nombre. Per tant, no hi ha claus primàries definides en aquesta sentència.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Una o més columnes d'origen tenen tipus de dades que no es poden definir com a claus primàries a Google BigQuery. Per tant, en aquest cas no hi ha definida cap clau primària. A Google BigQuery, només poden tenir clau primària els següents tipus de dades: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Copiar i tancar
#XBUT
closeDDL=Tancar
#XMSG
copiedToClipboard=Copiat a porta-retalls


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=L''objecte "{0}" no pot formar part de la cadena de tasques perquè no té un final (ja que inclou objectes amb els tipus de càrrega Inicial i delta/Només delta).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=L''objecte "{0}" no pot formar part de la cadena de tasques perquè no té un final (ja que inclou objectes amb els tipus de càrrega Inicial i delta).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=L''objecte "{0}" no es pot afegir a la cadena de tasques.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Hi ha objectes de destinació no desats. Torneu a provar-ho.{0}{0} El comportament d''aquesta funció ha canviat: En el passat, els objectes de destinació només es creaven en l''entorn de destinació quan es desplegava el flux de replicació.{0} Ara els objectes ja es creen quan es desa el flux de replicació. El vostre flux de replicació s''ha creat abans d''aquest canvi i conté objectes nous.{0} Heu de tornar a desar el flux de replicació abans de desplegar-lo per tal que contingui els objectes nous correctament.
#XMSG
confirmChangeContentTypeMessage=Esteu a punt de modificar el tipus de contingut. Si ho feu, s'eliminaran totes les projeccions existents.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Nom d'assumpte
#XFLD
schemaDialogVersionName=Versió d'esquema
#XFLD
includeTechKey=Clau tècnica d'include
#XFLD
segementButtonFlat=Pla
#XFLD
segementButtonNested=Anidat
#XMSG
subjectNamePlaceholder=Cercar nom d'assumpte

#XMSG
@EmailNotificationSuccess=S’ha desat la configuració de les notificacions per correu electrònic en temps d’execució.

#XFLD
@RuntimeEmailNotification=Notificació per correu electrònic en temps d'execució

#XBTN
@TXT_SAVE=Desar


