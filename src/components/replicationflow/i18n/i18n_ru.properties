#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Поток тиражирования

#XFLD: Edit Schema button text
editSchema=Редактировать схему

#XTIT : Properties heading
configSchema=Настроить схему

#XFLD: save changed button text
applyChanges=Применить изменения


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Выбрать исходное соединение
#XFLD
sourceContainernEmptyText=Выбрать контейнер
#XFLD
targetConnectionEmptyText=Выбрать целевое соединение
#XFLD
targetContainernEmptyText=Выбрать контейнер
#XFLD
sourceSelectObjectText=Выбрать исходный объект
#XFLD
sourceObjectCount=Исходные объекты ({0})
#XFLD
targetObjectText=Целевые объекты
#XFLD
confluentBrowseContext=Выбрать контекст
#XBUT
@retry=Повторить
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Выполняется апгрейд арендатора.

#XTOL
browseSourceConnection=Обзор исходных соединений
#XTOL
browseTargetConnection=Обзор целевых соединений
#XTOL
browseSourceContainer=Обзор исходных контейнеров
#XTOL
browseAndAddSourceDataset=Добавить исходные объекты
#XTOL
browseTargetContainer=Обзор целевых контейнеров
#XTOL
browseTargetSetting=Обзор целевых настроек
#XTOL
browseSourceSetting=Обзор исходных настроек
#XTOL
sourceDatasetInfo=Информация
#XTOL
sourceDatasetRemove=Удалить
#XTOL
mappingCount=Указывает общее число не основанных на имени сопоставлений/выражений.
#XTOL
filterCount=Указывает общее число условий фильтрации.
#XTOL
loading=Загрузка...
#XCOL
deltaCapture=Дельта-запись
#XCOL
deltaCaptureTableName=Таблица дельта-записи
#XCOL
loadType=Загрузить тип
#XCOL
deleteAllBeforeLoading=Удалить все перед загрузкой
#XCOL
transformationsTab=Проекции
#XCOL
settingsTab=Настройки

#XBUT
renameTargetObjectBtn=Переименовать целевой объект
#XBUT
mapToExistingTargetObjectBtn=Сопоставить с существующим целевым объектом
#XBUT
changeContainerPathBtn=Изменить путь к контейнеру
#XBUT
viewSQLDDLUpdated=Просмотреть инструкцию SQL создания таблицы
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Исходный объект не поддерживает дельта-запись, но выбранный целевой объект имеет включенную опцию дельта-записи.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Невозможно использовать целевой объект, так как активирована дельта-запись,{0}тогда как исходный объект не поддерживает дельта-запись.{1}Можете выбрать другой целевой объект, который не поддерживает дельта-запись.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Целевой объект с таким именем уже есть. Однако его нельзя использовать,{0}так как активирована дельта-запись, тогда как исходный объект не{0}поддерживает дельта-запись.{1}Можно ввести имя существующего целевого объекта, который не{0}поддерживает дельта-запись, или имя, которое еще не существует.
#XBUT
copySQLDDLUpdated=Скопировать инструкцию SQL создания таблицы
#XMSG
targetObjExistingNoCDCColumnUpdated=Существующие таблицы в Google BigQuery должны включать следующие столбцы для записи данных изменений (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Следующие исходные объекты не поддерживаются, так как у них нет первичного ключа или они используют соединение, которое не удовлетворяет условиям получения первичного ключа:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Возможное решение см. в статье SAP KBA 3531135.
#XLST: load type list values
initial=Только начальная
@emailUpdateError=Ошибка при обновлении списка уведомлений по электронной почте

#XLST
initialDelta=Начальная и дельта

#XLST
deltaOnly=Только дельта
#XMSG
confluentDeltaLoadTypeInfo=Для источника Confluent Kafka поддерживается только тип загрузки "Начальная и дельта".
#XMSG
confirmRemoveReplicationObject=Действительно удалить тиражирование?
#XMSG
confirmRemoveReplicationTaskPrompt=Эта операция удалит существующие тиражирования. Продолжить?
#XMSG
confirmTargetConnectionChangePrompt=Эта операция сбросит целевое соединение, целевой контейнер и удалит все целевые объекты. Продолжить?
#XMSG
confirmTargetContainerChangePrompt=Эта операция сбросит целевой контейнер и удалит все существующие целевые объекты. Продолжить?
#XMSG
confirmRemoveTransformObject=Действительно удалить проекцию {0}?
#XMSG
ErrorMsgContainerChange=Ошибка при изменении пути к контейнеру.
#XMSG
infoForUnsupportedDatasetNoKeys=Следующие исходные объекты не поддерживаются, так как не имеют первичного ключа:
#XMSG
infoForUnsupportedDatasetView=Следующие исходные объекты типа "Ракурсы" не поддерживаются:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Следующий исходный объект не поддерживается, так как это ракурс SQL, содержащий параметры ввода:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Следующие исходные объекты не поддерживаются, так как для них деактивирована экстракция:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Для соединений Confluent разрешены только форматы сериализации AVRO и JSON. Следующие объекты не поддерживаются, так как используют другой формат сериализации:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Невозможно вызвать схему для следующих объектов. Выберите соответствующий контекст или проверьте конфигурацию реестра схем.
#XTOL: warning dialog header on deleting replication task
deleteHeader=Удалить
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Настройка "Удалить все перед загрузкой" не поддерживается для Google BigQuery.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Настройка "Удалить все перед загрузкой" удаляет и повторно создает объект (тему) перед каждым тиражированием. Также удаляются все присвоенные сообщения.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Настройка "Удалить все перед загрузкой" не поддерживается для этого типа цели.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Техническое имя
#XCOL
connBusinessName=Бизнес-имя
#XCOL
connDescriptionName=Описание
#XCOL
connType=Тип
#XMSG
connTblNoDataFoundtxt=Соединения не найдены
#XMSG
connectionError=Ошибка при вызове соединений.
#XMSG
connectionCombinationUnsupportedErrorTitle=Комбинация соединений не поддерживается
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Тиражирование из {0} в {1} сейчас не поддерживается.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Комбинация типов соединений не поддерживается
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Тиражирование из соединения с типом соединения SAP HANA Cloud, файлы озера данных, в {0} не поддерживается. Вы можете тиражировать только в SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Выбрать
#XBUT
containerCancelBtn=Отменить
#XTOL
containerSelectTooltip=Выбрать
#XTOL
containerCancelTooltip=Отменить
#XMSG
containerContainerPathPlcHold=Путь к контейнеру
#XFLD
containerContainertxt=Контейнер
#XFLD
confluentContainerContainertxt=Контекст
#XMSG
infoMessageForSLTSelection=Только ид. массового переноса под SLT разрешен как контейнер. Выберите ид. массового переноса под SLT (при наличии) и нажмите "Отправить".
#XMSG
msgFetchContainerFail=Ошибка при вызове данных контейнера.
#XMSG
infoMessageForSLTHidden=Соединение не поддерживает папки SLT, поэтому они не отображаются в списке ниже.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Выберите контейнер, содержащий подпапки.
#XMSG
sftpIncludeSubFolderText=Ложь
#XMSG
sftpIncludeSubFolderTextNew=Нет

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(еще нет сопоставления фильтров)
#XMSG
failToFetchRemoteMetadata=Ошибка при вызове метаданных.
#XMSG
failToFetchData=Ошибка при вызове существующей цели.
#XCOL
@loadType=Загрузить тип
#XCOL
@deleteAllBeforeLoading=Удалить все перед загрузкой

#XMSG
@loading=Загрузка...
#XFLD
@selectSourceObjects=Выбрать исходные объекты
#XMSG
@exceedLimit=Вы не можете одновременно импортировать больше {0} объектов. Отмените выбор хотя бы {1} объектов.
#XFLD
@objects=Объекты
#XBUT
@ok=ОК
#XBUT
@cancel=Отменить
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Дальше
#XBUT
btnAddSelection=Добавить выбор
#XTOL
@remoteFromSelection=Удалить из выборки
#XMSG
@searchInForSearchField=Поиск в {0}

#XCOL
@name=Техническое имя
#XCOL
@type=Тип
#XCOL
@location=Местоположение
#XCOL
@label=Бизнес-имя
#XCOL
@status=Статус

#XFLD
@searchIn=Поиск в:
#XBUT
@available=Доступно
#XBUT
@selection=Выбор

#XFLD
@noSourceSubFolder=Таблицы и ракурсы
#XMSG
@alreadyAdded=Уже есть на диаграмме
#XMSG
@askForFilter=Элементов больше {0}. Введите строку фильтра, чтобы ограничить число элементов.
#XFLD: success label
lblSuccess=Успешно
#XFLD: ready label
lblReady=Готово
#XFLD: failure label
lblFailed=Не выполнено
#XFLD: fetching status label
lblFetchingDetail=Вызов сведений

#XMSG Place holder text for tree filter control
filterPlaceHolder=Введите текст для фильтрации объектов верхнего уровня
#XMSG Place holder text for server search control
serverSearchPlaceholder=Введите и нажмите Enter для поиска
#XMSG
@deployObjects=Импорт объектов: {0}...
#XMSG
@deployObjectsStatus=Число импортированных объектов: {0}. Число объектов, которые не удалось импортировать: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Не удалось открыть браузер локального репозитария.
#XMSG
@openRemoteSourceBrowserError=Не удалось вызвать исходные объекты.
#XMSG
@openRemoteTargetBrowserError=Не удалось вызвать целевые объекты.
#XMSG
@validatingTargetsError=Ошибка при проверке целей.
#XMSG
@waitingToImport=Готово к импорту

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Превышено максимальное число объектов. Выберите максимум 500 объектов для одного потока тиражирования.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Техническое имя
#XFLD
sourceObjectBusinessName=Бизнес-имя
#XFLD
sourceNoColumns=Число столбцов
#XFLD
containerLbl=Контейнер

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Выберите исходное соединение для потока тиражирования.
#XMSG
validationSourceContainerNonExist=Выберите контейнер для исходного соединения.
#XMSG
validationTargetNonExist=Выберите целевое соединение для потока тиражирования.
#XMSG
validationTargetContainerNonExist=Выберите контейнер для целевого соединения.
#XMSG
validationTruncateDisabledForObjectTitle=Тиражирование в хранилища объектов.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Тиражирование в облачное хранилище возможно, только если опция "Удалить все перед загрузкой" установлена или целевой объект не существует в цели.{0}{0} Чтобы все равно активировать тиражирование для объектов без установленной опции "Удалить все перед загрузкой", убедитесь, что целевой объект не существует в системе, перед запуском потока тиражирования.
#XMSG
validationTaskNonExist=В потоке тиражирования должно быть хотя бы одно тиражирование.
#XMSG
validationTaskTargetMissing=Требуется цель для тиражирования с источником: {0}
#XMSG
validationTaskTargetIsSAC=Выбранная цель - артефакт SAC: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Выбранная цель не является поддерживаемой локальной таблицей: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Объект с таким именем уже существует в цели. Однако этот объект невозможно использовать как целевой объект для потока тиражирвоания в локальный репозитарий, так как он не является локальной таблицей.
#XMSG
validateSourceTargetSystemDifference=Выберите разные комбинации исходных и целевых соединений и контейнеров для потока тиражирования.
#XMSG
validateDuplicateSources=Одно или несколько тиражирований имеют дпублирующиеся имена исходных объектов: {0}.
#XMSG
validateDuplicateTargets=Одно или несколько тиражирований имеют дпублирующиеся имена целевых объектов: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=Исходный объект {0} не поддерживает дельта-запись, а целевой объект {1} поддерживает. Удалите тиражирование.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Выберите тип загрузки "Начальная и дельта" для тиражирования с именем целевого объекта {0}.
#XMSG
validationAutoRenameTarget=Целевые столбцы переименованы.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Добавлена автоматическая проекция, и следующие целевые столбцы переименованы для возможности тиражирования в цель:{1}{1} {0} {1}{1}Это вызвано одной из следующих причин:{1}{1}{2} Неразрешенные символы.{1}{2} Зарезервированный префикс
#XMSG
validationAutoRenameTargetDescriptionUpdated=Добавлена автоматическая проекция, и следующие целевые столбцы переименованы для возможности тиражирования в Google BigQuery:{1}{1} {0} {1}{1}Это вызвано одной из следующих причин:{1}{1}{2} Зарезервированное имя столбца.{1}{2} Неразрешенные символы.{1}{2} Зарезервированный префикс.
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Добавлена автоматическая проекция, и следующие целевые столбцы переименованы для возможности тиражирования в Confluent:{1}{1} {0} {1}{1}Это вызвано одной из следующих причин:{1}{1}{2} Зарезервированное имя столбца.{1}{2} Неразрешенные символы.{1}{2} Зарезервированный префикс.
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Добавлена автоматическая проекция, и следующие целевые столбцы переименованы для возможности тиражирования в цель:{1}{1} {0} {1}{1}Это вызвано одной из следующих причин:{1}{1}{2} Зарезервированное имя столбца.{1}{2} Неразрешенные символы.{1}{2} Зарезервированный префикс.
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Целевой объект переименован.
#XMSG
autoRenameInfoDesc=Целевой объект переименован, так как он содержал не поддерживаемые символы. Поддерживаются только следующие символы:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}. (точка){0}{1}_ (подчеркивание){0}{1}- (дефис)
#XMSG
validationAutoTargetTypeConversion=Целевые типы данных изменены.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Для следующих целевых столбцов целевые типы данных изменены, так как в Google BigQuery исходные типы данных не поддерживаются:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Для следующих целевых столбцов целевые типы данных изменены, так как исходные типы данных не поддерживаются в целевом соединении:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Сократите имена целевых столбцов.
#XMSG
validationMaxCharLengthGBQTargetDescription=В Google BigQuery имена столбцов могут содержать максимум 300 символов. Используйте проекцию, чтобы сократить следующие имена целевых столбцов:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Первичные ключи не будут созданы.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=В Google BigQuery поддерживается максимум 16 первичных ключей, но исходный объект имеет больше первичных ключей. Ни один из первичных ключей не будет создан в целевом объекте.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Один или несколько исходных столбцов имеют типы данных, которые не могут быть определены как первичные ключи в Google BigQuery. Никакие первичные ключи не будут созданы в целевом объекте.{0}{0}Следующие целевые типы данных совместимы с типами данных Google BigQuery, для которых можно определить первичный ключ:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Определите один или несколько столбцов как первичный ключ.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Необходимо определить один или несколько столбцов как первичный ключ; для этого воспользуйтесь диалоговым окном исходной схемы.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Определите один или несколько столбцов как первичный ключ.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Необходимо определить один или несколько столбцов как первичный ключ, который соответствует ограничениям первичного ключа для вашего исходного объекта. Чтобы это сделать, перейдите в раздел "Настроить схему" в свойствах исходного объекта.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Введите действительное значение максимального раздела.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Значение максимального раздела должно быть ≥ 1 и ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Определите один или несколько столбцов как первичный ключ.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Чтобы тиражировать объект, определите один или несколько целевых столбцов как первичный ключ. Для этого воспользуйтесь проекцией.
#XMSG
validateHDLFNoPKExistingDatasetError=Определите один или несколько столбцов как первичный ключ.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Чтобы тиражировать данные в существующий целевой объект, он должен иметь один или несколько столбцов, определенных как первичный ключ. {0} У вас есть следующие опции для определения одного или нескольких столбцов как первичного ключа: {0} {1} Воспользуйтесь редактором локальных таблиц, чтобы изменить существующий целевой объект. Затем перезагрузите поток тиражирования.{0}{1} Переименуйте целевой объект в потоке тиражирования. Это создаст новый объект при запуске прогона. После переименования вы можете определить один или несколько столбцов как первичный ключ в проекции.{0}{1} Сопоставьте объект с другим существующим целевым объектом, в котором один или несколько столбцов уже определены как первичный ключ.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Выбранная цель уже есть в репозитарии: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Имена таблиц дельта-записи уже используются другими таблицами в репозитарии: {0}. Переименуйте эти целевые объекты, чтобы связанные имена таблиц дельта-записи были уникальными, перед сохранением потока тиражирования.
#XMSG
validateConfluentEmptySchema=Определить схему
#XMSG
validateConfluentEmptySchemaDescUpdated=Исходная таблица не имеет схемы. Выберите "Настроить схему", чтобы определить схему.
#XMSG
validationCSVEncoding=Недействительная кодировка CSV
#XMSG
validationCSVEncodingDescription=Кодировка CSV задачи недействительна.
#XMSG
validateConfluentEmptySchema=Выберите совместимый целевой тип данных
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Выберите совместимый целевой тип данных
#XMSG
globalValidateTargetDataTypeDesc=Произошла ошибка с сопоставлениями столбцов. Перейдите в проекцию и убедитесь, что все исходные столбцы сопоставлены с уникальным столбцом, со столбцом, имеющим совместимый тип данных, и что все определенные выражения действительны.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Дублирующиеся имена столбцов.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Дублирующиеся имена столбцов не поддерживаются. Используйте для их устранения диалоговое окно проекции. Следующие целевые объекты имеют дублирующиеся имена столбцов: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Дублирующиеся имена столбцов.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Дублирующиеся имена столбцов не поддерживаются. Следующие целевые объекты имеют дублирующиеся имена столбцов: {0}.
#XMSG
deltaOnlyLoadTypeTittle=В данных могут быть противоречия.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Тип загрузки "Только дельта" не будет учитывать изменения, внесенные в источник между последним сохранением и следующим прогоном.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Измените тип загрузки на "Начальная".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Тиражирование объектов на базе ABAP, не имеющих первичного ключа, возможно только для типа загрузки "Только начальная".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Деактивируйте дельта-запись.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Чтобы тиражировать объект, который не имеет первичного ключа, использующего тип соединения с источником ABAP, необходимо сначала деактивировать дельта-запись для этой таблицы.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Измените целевой объект.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Невозможно использовать целевой объект, так как дельта-запись активирована. Переименуйте целевой объект и отключите дельта-запись для нового (переименованного) объекта либо сопоставьте исходный объект с целевым объектом, для которого деактивирована дельта-запись.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Измените целевой объект.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Невозможно использовать целевой объект, так как он не имеет требуемого технического столбца __load_package_id. Переименуйте целевой объект с использованием еще не существующего имени. Тогда система создаст новый объект, который будет иметь такое же определение, как исходный объект, и содержать требуемый технический столбец. Также можно сопоставить целевой объект с существующим объектом, имеющим требуемый технический столбец (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Измените целевой объект.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Невозможно использовать целевой объект, так как он не имеет требуемого технического столбца __load_record_id. Переименуйте целевой объект с использованием еще не существующего имени. Тогда система создаст новый объект, который будет иметь такое же определение, как исходный объект, и содержать требуемый технический столбец. Также можно сопоставить целевой объект с существующим объектом, имеющим требуемый технический столбец (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Измените целевой объект.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Невозможно использовать целевой объект, так как тип данных его технического столбца __load_record_id отличен от "string(44)". Переименуйте целевой объект с использованием еще не существующего имени. Тогда система создаст новый объект, который будет иметь такое же определение, как исходный объект, а значит и правильный тип данных. Также можно сопоставить целевой объект с существующим объектом, имеющим требуемый технический столбец (__load_record_id) с правильным типом данных.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Измените целевой объект.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Невозможно использовать целевой объект, так как он имеет первичный ключ, а исходный объект нет. Переименуйте целевой объект с использованием еще не существующего имени. Тогда система создаст новый объект, который будет иметь такое же определение, как исходный объект, а значит не будет иметь первичного ключа. Также можно сопоставить целевой объект с существующим объектом, имеющим требуемый технический столбец (__load_package_id) и не имеющим первичного ключа.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Измените целевой объект.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Невозможно использовать целевой объект, так как он имеет первичный ключ, а исходный объект нет. Переименуйте целевой объект с использованием еще не существующего имени. Тогда система создаст новый объект, который будет иметь такое же определение, как исходный объект, а значит не будет иметь первичного ключа. Также можно сопоставить целевой объект с существующим объектом, имеющим требуемый технический столбец (__load_record_id) и не имеющим первичного ключа.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Измените целевой объект.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Невозможно использовать целевой объект, так как тип данных его технического столбца __load_package_id отличен от "binary(>=256)". Переименуйте целевой объект с использованием еще не существующего имени. Тогда система создаст новый объект, который будет иметь такое же определение, как исходный объект, а значит и правильный тип данных. Также можно сопоставить целевой объект с существующим объектом, имеющим требуемый технический столбец (__load_package_id) с правильным типом данных.
#XMSG
validationAutoRenameTargetDPID=Целевые столбцы переименованы.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Удалите исходный объект.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Исходный объект не имеет ключевого столбца, что не поддерживается в этом контексте.
#XMSG
validationAutoRenameTargetDPIDDescription=Добавлена автоматическая проекция, и следующие целевые столбцы переименованы для возможности тиражирования из источника ABAP без ключей:{1}{1} {0} {1}{1}Это вызвано одной из следующих причин:{1}{1}{2} Зарезервированное имя столбца.{1}{2} Неразрешенные символы.{1}{2} Зарезервированный префикс.
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Тиражирование в {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Сохранить и развернуть потоки тиражирования с {0} в качестве цели сейчас невозможно, так как выполняется техническое обслуживание этой функции.
#XMSG
TargetColumnSkippedLTF=Целевой столбец пропущен.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Целевой столбец пропущен из-за неподдерживаемого типа данных. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Столбец времени в качестве первичного ключа.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Исходный объект имеет столбец времени в качестве первичного ключа, что не поддерживается в этом контексте.
#XMSG
validateNoPKInLTFTarget=Отсутствует первичный ключ.
#XMSG
validateNoPKInLTFTargetDescription=Первичный ключ не определен в цели, что не поддерживается в этом контексте.
#XMSG
validateABAPClusterTableLTF=Таблица кластера ABAP.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Исходный объект является таблицей кластера ABAP, что не поддерживается в этом контексте.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Вы еще не добавили данные.
#YINS
welcomeText2=Чтобы запустить поток тиражирования, выберите соединение и исходный объект слева.

#XBUT
wizStep1=Выбрать исходное соединение
#XBUT
wizStep2=Выбрать исходный контейнер
#XBUT
wizStep3=Добавить исходные объекты

#XMSG
limitDataset=Достигнуто максимальное число объектов. Удалите существующие объекты, чтобы добавить новые, или создайте новый поток тиражирования.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Невозможно запустить поток тиражирования в это целевое соединение не SAP, так как на данный месяц нет доступного исходящего объема.
#XMSG
premiumOutBoundRFAdminWarningMsg=Администратор может увеличить премиальные исходящие блоки для этого арендатора, чтобы на месяц появился доступный исходящий объем.
#XMSG
messageForToastForDPIDColumn2=Новый столбец добавлен в целевой объект для {0} объектов - необходимо для обработки дубликатов записей в соединении с исходными объектами на базе ABAPб не имеющими первичного ключа.
#XMSG
PremiumInboundWarningMessage=В зависимости от числа потоков тиражирования и объема данных для тиражирования, ресурсы SAP HANA,{0}необходимые для тиражирования данных через {1}, могут превышать доступную мощность арендатора.
#XMSG
PremiumInboundWarningMsg=В зависимости от числа потоков тиражирования и объема данных для тиражирования, ресурсы SAP HANA,{0}необходимые для тиражирования данных через "{1}," могут превышать доступную мощность арендатора.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Введите действительное имя.
#XMSG
emptyTargetColumn=Введите имя целевого столбца.
#XMSG
emptyTargetColumnBusinessName=Введите бизнес-имя целевого столбца.
#XMSG
invalidTransformName=Введите действительное имя.
#XMSG
uniqueColumnName=Переименуйте целевой столбец.
#XMSG
copySourceColumnLbl=Скопировать столбцы из исходного объекта
#XMSG
renameWarning=При переименовании целевой таблицы выберите уникальное имя. Если таблица с новым именем уже существует в пространстве, она будет использовать определение этой таблицы.

#XMSG
uniqueColumnBusinessName=Измените бизнес-имя целевого столбца.
#XMSG
uniqueSourceMapping=Выберите другой исходный столбец.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=Исходный столбец {0} уже используется следующими целевыми столбцами:{1}{1}{2}{1}{1} Чтобы сохранить проекцию, для этого целевого столбца или для других целевых столбцов выберите исходный столбец, который еще не используется.
#XMSG
uniqueColumnNameDescription=Введенное имя целевого столбца уже существует. Чтобы сохранить проекцию, введите уникальное имя столбца.
#XMSG
uniqueColumnBusinessNameDesc=Это бизнес-имя целевого столбца уже существует. Чтобы сохранить проекцию, введите уникальное бизнес-имя столбца.
#XMSG
emptySource=Выберите исходный столбец или введите константу.
#XMSG
emptySourceDescription=Чтобы создать действительную запись сопоставления, выберите исходный столбец или введите значение константы.
#XMSG
emptyExpression=Определите сопоставление.
#XMSG
emptyExpressionDescription1=Выберите исходный столбец, с которым сопоставить целевой столбец, или установите флажок в столбце {0}"Функции/константы"{1}. {2} {2} Функции вводятся автоматически согласно целевому типу данных. Значения констант можно вводить вручную.
#XMSG
numberExpressionErr=Введите число.
#XMSG
numberExpressionErrDescription=Выбран числовой тип данных. Это значит, что вы можете ввести только числа и разделитель десятичных. Не используйте одинарные кавычки.
#XMSG
invalidLength=Введите действительное значение длины.
#XMSG
invalidLengthDescription=Длина типа данных должна быть равна или больше длины исходного столбца и может быть от 1 до 5000.
#XMSG
invalidMappedLength=Введите действительное значение длины.
#XMSG
invalidMappedLengthDescription=Длина типа данных должна быть равна или больше длины исходного столбца {0} и может быть от 1 до 5000.
#XMSG
invalidPrecision=Введите действительное значение точности.
#XMSG
invalidPrecisionDescription=Точность определяет общее количество цифр. Масштаб определяет количество цифр после разделителя десятичных и может быть от 0 до значения точности.{0}{0} Примеры: {0}{1} Точность 6, масштаб 2 дают такие числа, как 1234,56.{0}{1} Точность 6, масштаб 6 дают такие числа, как 0,123546.{0} {0} Точность и масштаб цели должны быть совместимы с точностью и масштабом источника, чтобы все цифры из источника помещались в целевое поле. Например, если в источнике используются точность 6 и масштаб 2 (то есть перед разделителем десятичных не только 0), в цели нельзя использовать точность 6 и масштаб 6.
#XMSG
invalidPrimaryKey=Введите хотя бы один первичный ключ.
#XMSG
invalidPrimaryKeyDescription=Первичный ключ не определен для этой схемы.
#XMSG
invalidMappedPrecision=Введите действительное значение точности.
#XMSG
invalidMappedPrecisionDescription1=Точность определяет общее количество цифр. Масштаб определяет количество цифр после разделителя десятичных и может быть от 0 до значения точности.{0}{0} Примеры: {0}{1} Точность 6, масштаб 2 дают такие числа, как 1234,56.{0}{1} Точность 6, масштаб 6 дают такие числа, как 0,123546.{0} {0}Точность типа данных должна быть не меньше точности источника ({2}).
#XMSG
invalidScale=Введите действительное значение масштаба.
#XMSG
invalidScaleDescription=Точность определяет общее количество цифр. Масштаб определяет количество цифр после разделителя десятичных и может быть от 0 до значения точности.{0}{0} Примеры: {0}{1} Точность 6, масштаб 2 дают такие числа, как 1234,56.{0}{1} Точность 6, масштаб 6 дают такие числа, как 0,123546.{0} {0} Точность и масштаб цели должны быть совместимы с точностью и масштабом источника, чтобы все цифры из источника помещались в целевое поле. Например, если в источнике используются точность 6 и масштаб 2 (то есть перед разделителем десятичных не только 0), в цели нельзя использовать точность 6 и масштаб 6.
#XMSG
invalidMappedScale=Введите действительное значение масштаба.
#XMSG
invalidMappedScaleDescription1=Точность определяет общее количество цифр. Масштаб определяет количество цифр после разделителя десятичных и может быть от 0 до значения точности.{0}{0} Примеры: {0}{1} Точность 6, масштаб 2 дают такие числа, как 1234,56.{0}{1} Точность 6, масштаб 6 дают такие числа, как 0,123546.{0}{0} Масштаб типа данных должен быть не меньше масштаба источника ({2}).
#XMSG
nonCompatibleDataType=Выберите совместимый целевой тип данных.
#XMSG
nonCompatibleDataTypeDescription1=Указанный здесь тип данных должен быть совместим с исходным типом данных ({0}). {1}{1} Пример: если ваш исходный столбец имеет строковый тип данных и содержит буквы, вы не можете использовать в цели десятичный тип данных.
#XMSG
invalidColumnCount=Выберите исходный столбец.
#XMSG
ObjectStoreInvalidScaleORPrecision=Введите действительное значение для точности и масштаба.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=Первое значение – точность, определяет общее число знаков. Второе значение – масштаб, определяет число знаков после запятой. Введите целевое значение масштаба, которое больше исходного значения масштаба, и убедитесь, что разница между введенным целевым масштабом и значением точности больше разницы между исходным масштабом и значением точности.
#XMSG
InvalidPrecisionORScale=Введите действительное значение для точности и масштаба.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=Первое значение - точность, которая определяет общее количество цифр. Второе значение - масштаб, который определяет цифры после разделителя десятичных.{0}{0}Так как исходный тип данных не поддерживается в Google BigQuery, он преобразуется в целевой тип данных DECIMAL. В этом случае можно определить точность только от 38 до 76 и масштаб только от 9 до 38. Кроме того, разность точности и масштаба, которая определяет цифры до разделителя десятичных, должна быть от 29 до 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=Первое значение - точность, которая определяет общее количество цифр. Второе значение - масштаб, который определяет цифры после разделителя десятичных.{0}{0}Так как исходный тип данных не поддерживается в Google BigQuery, он преобразуется в целевой тип данных DECIMAL. В этом случае точность должна быть не меньше 20. Кроме того, разность точности и масштаба, которая определяет цифры до разделителя десятичных, должна быть не меньше 20.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=Первое значение - точность, которая определяет общее количество цифр. Второе значение - масштаб, который определяет цифры после разделителя десятичных.{0}{0}Так как исходный тип данных не поддерживается в цели, он преобразуется в целевой тип данных DECIMAL. В этом случае точность должна быть определена любым числом, которое не меньше 1 и не больше 38, а масштаб должен быть не больше точности.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=Первое значение - точность, которая определяет общее количество цифр. Второе значение - масштаб, который определяет цифры после разделителя десятичных.{0}{0}Так как исходный тип данных не поддерживается в цели, он преобразуется в целевой тип данных DECIMAL. В этом случае точность должна быть не меньше 20. Кроме того, разность точности и масштаба, которая определяет цифры до разделителя десятичных, должна быть не меньше 20.
#XMSG
invalidColumnCountDescription=Чтобы создать действительную запись сопоставления, выберите исходный столбец или введите значение константы.
#XMSG
duplicateColumns=Переименуйте целевой столбец.
#XMSG
duplicateGBQCDCColumnsDesc=Имя целевого столбца зарезервировано в Google BigQuery. Переименуйте его для сохранения проекции.
#XMSG
duplicateConfluentCDCColumnsDesc=Имя целевого столбца зарезервировано в Confluent. Переименуйте его для сохранения проекции.
#XMSG
duplicateSignavioCDCColumnsDesc=Имя целевого столбца зарезервировано в SAP Signavio. Переименуйте его для сохранения проекции.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Имя целевого столбца зарезервировано в MS OneLake. Переименуйте его для сохранения проекции.
#XMSG
duplicateSFTPCDCColumnsDesc=Имя целевого столбца зарезервировано в SFTP. Переименуйте его для сохранения проекции.
#XMSG
GBQTargetNameWithPrefixUpdated1=Имя целевого столбца содержит префикс, зарезервированный в Google BigQuery. Переименуйте его для сохранения проекции. {0}{0}Имя целевого столбца не может начинаться с любой из следующих строк:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Сократите имя целевого столбца.
#XMSG
GBQtargetMaxLengthDesc=В Google BigQuery имя столбца может быть максимум из 300 символов. Сократите имя целевого столбца для сохранения проекции.
#XMSG
invalidMappedScalePrecision=Точность и масштаб цели должны быть совместимы с точностью и масштабом источника, чтобы все цифры из источника помещались в поле цели.
#XMSG
invalidMappedScalePrecisionShortText=Введите действительные значения точности и масштаба.
#XMSG
validationIncompatiblePKTypeDescProjection3=Один или несколько исходных столбцов имеют типы данных, которые не могут быть определены как первичные ключи в Google BigQuery. Никакие первичные ключи не будут созданы в целевом объекте.{0}{0}Следующие целевые типы данных совместимы с типами данных Google BigQuery, для которых можно определить первичный ключ:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Отмените выбор column __message_id.
#XMSG
uncheckColumnMessageIdDesc=Столбец: первичный ключ
#XMSG
validationOpCodeInsert=Необходимо ввести значение для поля "Вставить".
#XMSG
recommendDifferentPrimaryKey=Рекомендуется выбрать другой первичный ключ на уровне элемента.
#XMSG
recommendDifferentPrimaryKeyDesc=Когда код операции уже определен, рекомендуется выбрать разные первичные ключи для индекса массива и элементов, чтобы избежать проблем, таких как, например, дублирование столбцов.
#XMSG
selectPrimaryKeyItemLevel=Необходимо выбрать хотя бы один первичный ключ для уровня заголовка и уровня элемента.
#XMSG
selectPrimaryKeyItemLevelDesc=Когда развернут массив или сопоставление, необходимо выбрать два первичных ключа: один на уровне заголовка и один на уровне элемента.
#XMSG
invalidMapKey=Необходимо выбрать хотя бы один первичный ключ на уровне заголовка.
#XMSG
invalidMapKeyDesc=Когда развернут массив или сопоставление, необходимо выбрать первичный ключ на уровне заголовка.
#XFLD
txtSearchFields=Поиск целевых столбцов
#XFLD
txtName=Имя
#XMSG
txtSourceColValidation=Один или несколько исходных столбцов не поддерживаются:
#XMSG
txtMappingCount=Сопоставления ({0})
#XMSG
schema=Схема
#XMSG
sourceColumn=Исходные столбцы
#XMSG
warningSourceSchema=Любое изменение, внесенное в схему, повлияет на сопоставления в диалоговом окне проекции. 
#XCOL
txtTargetColName=Целевой столбец (техническое имя)
#XCOL
txtDataType=Целевой тип данных
#XCOL
txtSourceDataType=Исходный тип данных
#XCOL
srcColName=Исходный столбец (техническое имя)
#XCOL
precision=Точность
#XCOL
scale=Масштаб
#XCOL
functionsOrConstants=Функции/константы
#XCOL
txtTargetColBusinessName=Целевой столбец (бизнес-имя).
#XCOL
prKey=Первичный ключ
#XCOL
txtProperties=Свойства
#XBUT
txtOK=Сохранить
#XBUT
txtCancel=Отменить
#XBUT
txtRemove=Удалить
#XFLD
txtDesc=Описание
#XMSG
rftdMapping=Сопоставление
#XFLD
@lblColumnDataType=Тип данных
#XFLD
@lblColumnTechnicalName=Техническое имя
#XBUT
txtAutomap=Автоматическое сопоставление
#XBUT
txtUp=Вверх
#XBUT
txtDown=Вниз

#XTOL
txtTransformationHeader=Проекция
#XTOL
editTransformation=Редактировать
#XTOL
primaryKeyToolip=Ключ


#XMSG
rftdFilter=Фильтр
#XMSG
rftdFilterColumnCount=Источник: {0}({1})
#XTOL
rftdFilterColSearch=Поиск
#XMSG
rftdFilterColNoData=Нет столбцов для просмотра
#XMSG
rftdFilteredColNoExps=Нет выражений фильтра
#XMSG
rftdFilterSelectedColTxt=Добавить фильтр для
#XMSG
rftdFilterTxt=Фильтр доступен для
#XBUT
rftdFilterSelectedAddColExp=Добавить выражение
#YINS
rftdFilterNoSelectedCol=Выберите столбец для добавления фильтра.
#XMSG
rftdFilterExp=Выражение фильтра
#XMSG
rftdFilterNotAllowedColumn=Добавление фильтров не поддерживается для этого столбца.
#XMSG
rftdFilterNotAllowedHead=Не поддерживаемый столбец
#XMSG
rftdFilterNoExp=Фильтр не определен
#XTOL
rftdfilteredTt=Отфильтровано
#XTOL
rftdremoveexpTt=Удалить выражение фильтра
#XTOL
validationMessageTt=Сообщения о проверке
#XTOL
rftdFilterDateInp=Выберите дату
#XTOL
rftdFilterDateTimeInp=Выберите дату и время
#XTOL
rftdFilterTimeInp=Выберите время
#XTOL
rftdFilterInp=Введите значение
#XMSG
rftdFilterValidateEmptyMsg={0} выражения(й) фильтра в столбце {1} пустые
#XMSG
rftdFilterValidateInvalidNumericMsg={0} выражения(й) фильтра в столбце {1} содержат недействительные числовые значения
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Выражение фильтра должно содержать действительные числовые значения
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Если схема целевого объекта изменилась, используйте функцию "Сопоставить с существующим целевым объектом" на главной странице, чтобы адаптировать изменения, и повторно сопоставьте целевой объект с источником.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Если целевая таблица уже существует и сопоставление включает изменение схемы, измените целевую таблицу соответствующим образом перед развертыванием потока тиражирования.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Если сопоставление включает изменение схемы, измените целевую таблицу соответствующим образом перед развертыванием потока тиражирования.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Следующие не поддерживаемые столбцы из определения источника пропущены: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Следующие не поддерживаемые столбцы из определения цели пропущены: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Следующие объекты не поддерживаются, так как они экспонированы для потребления: {0} {1} {0} {0} Чтобы использовать таблицы в потоке тиражирования, семантическое использование (в настройках таблицы) не должно быть установлено на {2}Аналитический набор данных{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Целевой объект невозможно использовать, так как он экспонирован для потребления: {0} {0} Чтобы использовать таблицу в потоке тиражирования, семантическое использование (в настройках таблицы) не должно быть установлено на {1}Аналитический набор данных{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Целевой объект с таким именем уже есть. Однако его невозможно использовать, так как он экспонирован для потребления: {0} {0} Чтобы использовать таблицу в потоке тиражирования, семантическое использование (в настройках таблицы) не должно быть установлено на {1}Аналитический набор данных{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Объект с таким именем уже существует в цели. {0}Однако этот объект невозможно использовать как целевой объект для потока тиражирвоания в локальный репозитарий, так как он не является локальной таблицей.
#XMSG:
targetAutoRenameUpdated=Целевой столбец переименован.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Целевой столбец переименован, чтобы разрешить тиражирование в Google BigQuery. Это связано с одной из следующих причин: {0} {1}{2}Зарезервированное имя столбца.{3}{2}Не поддерживаемые символы.{3}{2}Зарезервированный префикс.{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Целевой столбец переименован, чтобы разрешить тиражирование в Confluent. Это связано с одной из следующих причин: {0} {1}{2}Зарезервированное имя столбца.{3}{2}Не поддерживаемые символы.{3}{2}Зарезервированный префикс.{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Целевой столбец переименован, чтобы разрешить тиражирование в цель. Это связано с одной из следующих причин:{0} {1}{2}Не поддерживаемые символы.{3}{2}Зарезервированный префикс.{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Целевой столбец переименован, чтобы разрешить тиражирование в цель. Это связано с одной из следующих причин:{0} {1}{2}Зарезервированное имя столбца{3}{2}Неподдерживаемые символы{3}{2}Зарезервированный префикс{3}{4}
#XMSG:
targetAutoDataType=Целевой тип данных изменен.
#XMSG:
targetAutoDataTypeDesc=Целевой тип данных изменен на {0}, так как исходный тип данных не поддерживается в Google BigQuery.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Целевой тип данных изменен на {0}, так как исходный тип данных не поддерживается в целевом соединении.
#XMSG
projectionGBQUnableToCreateKey=Первичные ключи не будут созданы.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=В Google BigQuery поддерживается максимум 16 первичных ключей, но исходный объект имеет больше первичных ключей. Ни один из первичных ключей не будет создан в целевом объекте.
#XMSG
HDLFNoKeyError=Определите один или несколько столбцов как первичный ключ.
#XMSG
HDLFNoKeyErrorDescription=Чтобы тиражировать объект, определите один и более столбцов как первичный ключ.
#XMSG
HDLFNoKeyErrorExistingTarget=Определите один или несколько столбцов как первичный ключ.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Чтобы тиражировать данные в существующий целевой объект, он должен иметь один или несколько столбцов, определенных как первичный ключ. {0} {0} У вас есть следующие опции для определения одного или нескольких столбцов как первичного ключа: {0}{0}{1} Воспользуйтесь редактором локальных таблиц, чтобы изменить существующий целевой объект. Затем перезагрузите поток тиражирования.{0}{0}{1} Переименуйте целевой объект в потоке тиражирования. Это создаст новый объект при запуске прогона. После переименования вы можете определить один или несколько столбцов как первичный ключ в проекции.{0}{0}{1} Сопоставьте объект с другим существующим целевым объектом, в котором один или несколько столбцов уже определены как первичный ключ.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Первичный ключ изменен.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=По сравнению с исходным объектом вы определили другие столбцы как первичный ключ для целевого объекта. Убедитесь, что эти столбцы уникально идентифицируют все строки, во избежание возможного повреждения данных при последующем тиражировании данных. {0} {0} В исходном объекте следующие столбцы определены как первичный ключ: {0} {1}
#XMSG
duplicateDPIDColumns=Переименуйте целевой столбец.
#XMSG
duplicateDPIDDColumnsDesc1=Это имя целевого столбца зарезервировано для технического столбца. Введите другое имя. чтобы сохранить проекцию.
#XMSG:
targetAutoRenameDPID=Целевой столбец переименован.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Целевой столбец переименован, чтобы разрешить тиражирование из источника ABAP без ключей. Это связано с одной из следующих причин: {0} {1}{2}Зарезервированное имя столбца.{3}{2}Не поддерживаемые символы.{3}{2}Зарезервированный префикс.{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle=Целевые настройки {0}
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle=Исходные настройки {0}
#XBUT
connectionSettingSave=Сохранить
#XBUT
connectionSettingCancel=Отменить
#XBUT: Button to keep the object level settings
txtKeep=Оставить
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Перезаписать
#XFLD
targetConnectionThreadlimit=Целевой лимит тредов для начальной загрузки (1-100)
#XFLD
connectionThreadLimit=Исходный лимит тредов для начальной загрузки (1-100)
#XFLD
maxConnection=Лимит тредов тиражирования (1-100)
#XFLD
kafkaNumberOfPartitions=Число разделов
#XFLD
kafkaReplicationFactor=Коэффициент тиражирования
#XFLD
kafkaMessageEncoder=Кодировка сообщения
#XFLD
kafkaMessageCompression=Сжатие сообщения
#XFLD
fileGroupDeltaFilesBy=Группировать дельту по
#XFLD
fileFormat=Тип файла
#XFLD
csvEncoding=Кодировка CSV
#XFLD
abapExitLbl=Exit ABAP
#XFLD
deltaPartition=Число тредов объекта для дельта-загрузок (1-10)
#XFLD
clamping_Data=Ошибка усечения данных
#XFLD
fail_On_Incompatible=Ошибка из-за несовместимых данных
#XFLD
maxPartitionInput=Макс. число разделов
#XFLD
max_Partition=Определить макс. число разделов
#XFLD
include_SubFolder=Включить подпапки
#XFLD
fileGlobalPattern=Глобальный шаблон для имени файла
#XFLD
fileCompression=Сжатие файла
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Разделитель файла
#XFLD
fileIsHeaderIncluded=Заголовок файла
#XFLD
fileOrient=Упорядочить
#XFLD
gbqWriteMode=Режим записи
#XFLD
suppressDuplicate=Подавить дубликаты
#XFLD
apacheSpark=Активировать совместимость Apache Spark
#XFLD
clampingDatatypeCb=Ограничить типы данных десятичных с плавающей запятой
#XFLD
overwriteDatasetSetting=Перезаписать настройки цели на уровне объекта
#XFLD
overwriteSourceDatasetSetting=Перезаписать настройки источника на уровне объекта
#XMSG
kafkaInvalidConnectionSetting=Введите число от {0} до {1}.
#XMSG
MinReplicationThreadErrorMsg=Введите число больше {0}.
#XMSG
MaxReplicationThreadErrorMsg=Введите число меньше {0}.
#XMSG
DeltaThreadErrorMsg=Введите значение от 1 до 10.
#XMSG
MaxPartitionErrorMsg=Введите значение в интервале 1 <= x <= 2147483647. Значение по умолчанию: 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Введите целое число от {0} до {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Использовать коэффициент тиражирования брокера
#XFLD
serializationFormat=Формат сериализации
#XFLD
compressionType=Тип сжатия
#XFLD
schemaRegistry=Использовать реестр схем
#XFLD
subjectNameStrat=Стратегия имени субъекта
#XFLD
compatibilityType=Тип совместимости
#XFLD
confluentTopicName=Имя темы
#XFLD
confluentRecordName=Имя записи
#XFLD
confluentSubjectNamePreview=Предпросмотр имени субъекта
#XMSG
serializationChangeToastMsgUpdated2=Формат сериализации изменен на JSON, так как не активирован реестр схем. Чтобы изменить формат сериализации обратно на AVRO, сначала необходимо активировать реестр схем.
#XBUT
confluentTopicNameInfo=Имя темы всегда основано на имени целевого объекта. Вы можете изменить его, переименовав целевой объект.
#XMSG
emptyRecordNameValidationHeaderMsg=Введите имя записи.
#XMSG
emptyPartionHeader=Введите число разделов.
#XMSG
invalidPartitionsHeader=Введите действительное число разделов.
#XMSG
invalidpartitionsDesc=Введите число от 1 до 200 000.
#XMSG
emptyrFactorHeader=Введите коэффициент тиражирования.
#XMSG
invalidrFactorHeader=Введите действительный коэффициент тиражирования.
#XMSG
invalidrFactorDesc=Введите число от 1 до 32 767.
#XMSG
emptyRecordNameValidationDescMsg=Если используется формат сериализации "AVRO", поддерживаются только следующие символы:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _ (подчеркивание)
#XMSG
validRecordNameValidationHeaderMsg=Введите действительное имя записи.
#XMSG
validRecordNameValidationDescMsgUpdated=Так как используется формат сериализации "AVRO", имя записи должно состоять только из букв и цифр (A-Z, a-z, 0-9), а также знаков подчеркивания (_). Оно должно начинаться с буквы или знака подчеркивания.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled="Число тредов объекта для дельта-загрузок" можно установить, когда один или несколько объектов имеют тип загрузки "Начальная и дельта".
#XMSG
invalidTargetName=Недействительное имя столбца
#XMSG
invalidTargetNameDesc=Имя целевого столбца должно состоять только из букв и цифр (A-Z, a-z, 0-9), а также знаков подчеркивания (_).
#XFLD
consumeOtherSchema=Использовать другие версии схемы
#XFLD
ignoreSchemamissmatch=Игнорировать несоответствие схемы
#XFLD
confleuntDatatruncation=Ошибка усечения данных
#XFLD
isolationLevel=Уровень изоляции
#XFLD
confluentOffset=Начальная точка
#XFLD
signavioGroupDeltaFilesByText=Нет
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=Нет
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=Нет

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Проекции
#XBUT
txtAdd=Добавить
#XBUT
txtEdit=Редактировать
#XMSG
transformationText=Добавьте проекцию для настройки фильтра или сопоставления.
#XMSG
primaryKeyRequiredText=Выберите первичный ключ с помощью опции "Настроить схему".
#XFLD
lblSettings=Настройки
#XFLD
lblTargetSetting={0}: целевые настройки
#XMSG
@csvRF=Выберите файл, содержащий определение схемы, которую вы хотите применить ко всем файлам в папке.
#XFLD
lblSourceColumns=Исходные столбцы
#XFLD
lblJsonStructure=Структура JSON
#XFLD
lblSourceSetting={0}: исходные настройки
#XFLD
lblSourceSchemaSetting={0}: настройки исходной схемы
#XBUT
messageSettings=Настройки сообщений
#XFLD
lblPropertyTitle1=Свойства объекта
#XFLD
lblRFPropertyTitle=Свойства потока тиражирования
#XMSG
noDataTxt=Нет столбцов для просмотра.
#XMSG
noTargetObjectText=Не выбран целевой объект.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Целевые столбцы
#XMSG
searchColumns=Поиск столбцов
#XTOL
cdcColumnTooltip=Столбец для дельта-записи
#XMSG
sourceNonDeltaSupportErrorUpdated=Исходный объект не поддерживает дельта-запись.
#XMSG
targetCDCColumnAdded=2 целевых столбца добавлены для дельта-записи.
#XMSG
deltaPartitionEnable=Число тредов объекта для дельта-загрузок добавлено в настройки источника.
#XMSG
attributeMappingRemovalTxt=Удаление недействительных сопоставлений не поддерживается для нового целевого объекта.
#XMSG
targetCDCColumnRemoved=2 целевых столбца, используемых для дельта-записи, удалены.
#XMSG
replicationLoadTypeChanged=Тип загрузки изменен на "Начальная и дельта".
#XMSG
sourceHDLFLoadTypeError=Измените тип загрузки на "Начальная и дельта".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Чтобы тиражировать объект из соединения с источником типа SAP HANA Cloud, файлы озера данных, в SAP Datasphere, используйте тип загрузки "Начальная и дельта".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Активируйте дельта-запись.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Чтобы тиражировать объект из соединения с источником типа SAP HANA Cloud, файлы озера данных, в SAP Datasphere, активируйте дельта-запись.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Измените целевой объект.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Невозможно использовать целевой объект, так как дельта-запись деактивирована. Переименуйте целевой объект (что позволит создать новый объект с дельта-записью) или сопоставьте его с существующим объектом с активированной дельта-записью.
#XMSG
deltaPartitionError=Введите действительное число тредов объекта для дельта-загрузок.
#XMSG
deltaPartitionErrorDescription=Введите значение от 1 до 10.
#XMSG
deltaPartitionEmptyError=Введите число тредов объекта для дельта-загрузок.
#XFLD
@lblColumnDescription=Описание
#XMSG
@lblColumnDescriptionText1=Для технических целей - обработка дубликатов записей, вызванных проблемами при тиражировании исходных объектов на базе ABAP, не имеющих первичного ключа.
#XFLD
storageType=Память
#XFLD
skipUnmappedColLbl=Пропустить несопоставленные столбцы
#XFLD
abapContentTypeLbl=Тип контента
#XFLD
autoMergeForTargetLbl=Объединить данные автоматически
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Общее
#XFLD
lblBusinessName=Бизнес-имя
#XFLD
lblTechnicalName=Техническое имя
#XFLD
lblPackage=Пакет
#XFLD
statusPanel=Статус выполнения
#XBTN: Schedule dropdown menu
SCHEDULE=Планирование
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Редактировать планирование
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Удалить планирование
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Создать планирование
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Проверка планирования не пройдена
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Невозможно создать планирование, так как выполняется развертывание потока тиражирования.{0}Ожидайте завершения развертывания потока тиражирования.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Для потоков тиражирования, которые содержат объекты с типом загрузки "Начальная и дельта", невозможно создать планирование.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Для потоков тиражирования, которые содержат объекты с типом загрузки "Начальная и дельта/только дельта", невозможно создать планирование.
#XFLD : Scheduled popover
SCHEDULED=Запланировано
#XFLD
CREATE_REPLICATION_TEXT=Создать поток тиражирования
#XFLD
EDIT_REPLICATION_TEXT=Редактировать поток тиражирования
#XFLD
DELETE_REPLICATION_TEXT=Удалить поток тиражирования
#XFLD
REFRESH_FREQUENCY=Частота
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Невозможно развернуть поток тиражирования, так как существующее планирование{0} еще не поддерживает тип загрузки "Начальная и дельта".{0}{0}Чтобы развернуть поток тиражирования, установите типы загрузки всех объектов{0} на "Только начальная". Или удалите планирование, разверните {0}поток тиражирования и запустите новый прогон. Он будет выполняться {0}бесконечно и поддерживать объекты с типом загрузки "Начальная и дельта".
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Невозможно развернуть поток тиражирования, так как существующее планирование{0} еще не поддерживает тип загрузки "Начальная и дельта/только дельта".{0}{0}Чтобы развернуть поток тиражирования, установите типы загрузки всех объектов{0} на "Только начальная". Или удалите планирование, разверните {0}поток тиражирования и запустите новый прогон. Он будет выполняться {0}бесконечно и поддерживать объекты с типом загрузки "Начальная и дельта/только дельта".
#XMSG
SCHEDULE_EXCEPTION=Не удалось получить сведения о планировании
#XFLD: Label for frequency column
everyLabel=Кажд.
#XFLD: Plural Recurrence text for Hour
hoursLabel=ч
#XFLD: Plural Recurrence text for Day
daysLabel=дн.
#XFLD: Plural Recurrence text for Month
monthsLabel=мес.
#XFLD: Plural Recurrence text for Minutes
minutesLabel=мин
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Не удалось получить информацию о возможности планирования.
#XFLD :Paused field
PAUSED=Приостановлено
#XMSG
navToMonitoring=Открыть в мониторе потоков
#XFLD
statusLbl=Статус
#XFLD
lblLastRunExecuted=Начало последнего прогона
#XFLD
lblLastExecuted=Последний прогон
#XFLD: Status text for Completed
statusCompleted=Выполнено
#XFLD: Status text for Running
statusRunning=Выполняется
#XFLD: Status text for Failed
statusFailed=Не выполнено
#XFLD: Status text for Stopped
statusStopped=Остановлено
#XFLD: Status text for Stopping
statusStopping=Останавливается
#XFLD: Status text for Active
statusActive=Активно
#XFLD: Status text for Paused
statusPaused=Приостановлено
#XFLD: Status text for not executed
lblNotExecuted=Еще не выполнено
#XFLD
messagesSettings=Настройки сообщений
#XTOL
@validateModel=Сообщения о проверке
#XTOL
@hierarchy=Иерархия
#XTOL
@columnCount=Число столбцов
#XMSG
VAL_PACKAGE_CHANGED=Вы присвоили этот объект пакету "{1}". Нажмите "Сохранить" для подтверждения и проверки этого изменения. Присвоение пакету невозможно отменить в этом редакторе после сохранения.
#XMSG
MISSING_DEPENDENCY=Зависимости объекта "{0}" невозможно развернуть в пакете "{1}".
#XFLD
deltaLoadInterval=Интервал дельта-загрузки
#XFLD
lblHour=Часы (0-24)
#XFLD
lblMinutes=Минуты (0-59)
#XMSG
maxHourOrMinErr=Введите значение в диапазоне от 0 до {0}
#XMSG
maxDeltaInterval=Максимальное значение интервала дельта-загрузки - 24 часа.{0}Измените значение минут или часов.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Путь к целевому контейнеру
#XFLD
confluentSubjectName=Имя субъекта
#XFLD
confluentSchemaVersion=Версия схемы
#XFLD
confluentIncludeTechKeyUpdated=Включить технический ключ
#XFLD
confluentOmitNonExpandedArrays=Пропустить неразвернутые массивы
#XFLD
confluentExpandArrayOrMap=Развернуть массив или сопоставление
#XCOL
confluentOperationMapping=Сопоставление операций
#XCOL
confluentOpCode=Код операции
#XFLD
confluentInsertOpCode=Вставить
#XFLD
confluentUpdateOpCode=Обновить
#XFLD
confluentDeleteOpCode=Удалить
#XFLD
expandArrayOrMapNotSelectedTxt=Не выбрано
#XFLD
confluentSwitchTxtYes=Да
#XFLD
confluentSwitchTxtNo=Нет
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Ошибка
#XTIT
executeWarning=Предупреждение
#XMSG
executeunsavederror=Сохраните поток тиражирования перед выполнением.
#XMSG
executemodifiederror=В потоке тиражирования есть несохраненные изменения. Сохраните поток тиражирования.
#XMSG
executeundeployederror=Разверните поток тиражирования перед его выполнением.
#XMSG
executedeployingerror=Ожидайте завершения развертывания.
#XMSG
msgRunStarted=Выполнение начато
#XMSG
msgExecuteFail=Не удалось выполнить поток тиражирования
#XMSG
titleExecuteBusy=Ожидайте.
#XMSG
msgExecuteBusy=Данные готовятся к выполнению потока тиражирования.
#XTIT
executeConfirmDialog=Предупреждение
#XMSG
msgExecuteWithValidations=Поток тиражирования имеет ошибки проверки. Это может помешать выполнению потока тиражирования.
#XMSG
msgRunDeployedVersion=Есть изменения для развертывания. Будет выполнена последняя развернутая версия потока тиражирования. Продолжить?
#XBUT
btnExecuteAnyway=Все равно выполнить
#XBUT
btnExecuteClose=Закрыть
#XBUT
loaderClose=Закрыть
#XTIT
loaderTitle=Загрузка
#XMSG
loaderText=Вызываем сведения с сервера
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Невозможно запустить поток тиражирования в это целевое соединение не SAP,
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=так как на данный месяц нет доступного исходящего объема.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Администратор может увеличить премиальные исходящие блоки для этого
#XMSG
premiumOutBoundRFAdminErrMsgPart2=арендатора, чтобы на месяц появился доступный исходящий объем.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Ошибка
#XTIT
deployInfo=Информация
#XMSG
deployCheckFailException=Особая ситуация при развертывании
#XMSG
deployGBQFFDisabled=Развернуть потоки тиражирования с целевым соединением с Google BigQuery сейчас невозможно, так как выполняется техническое обслуживание этой функции.
#XMSG
deployKAFKAFFDisabled=Развернуть потоки тиражирования с целевым соединением с Apache Kafka сейчас невозможно, так как выполняется техническое обслуживание этой функции.
#XMSG
deployConfluentDisabled=Развернуть потоки тиражирования с целевым соединением с Confluent Kafka сейчас невозможно, так как выполняется техническое обслуживание этой функции.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Для следующих целевых объектов имена таблиц дельта-записи уже используются другими таблицами в репозитарии: {0}. Переименуйте эти целевые объекты, чтобы связанные имена таблиц дельта-записи были уникальными, перед развертыванием потока тиражирования.
#XMSG
deployDWCSourceFFDisabled=Развернуть потоки тиражирования с SAP Datasphere как источником сейчас невозможно, так как выполняется техническое обслуживание этой функции.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Развернуть потоки тиражирования, содержащие локальные таблицы с поддержкой дельты как исходные объекты, сейчас невозможно, так как выполняется техническое обслуживание этой функции.
#XMSG
deployHDLFSourceFFDisabled=Развернуть потоки тиражирования, имеющие исходные соединения с типом соединения SAP HANA Cloud, файлы озера данных, сейчас невозможно, так как выполняется техническое обслуживание.
#XMSG
deployObjectStoreAsSourceFFDisabled=Развертывание потоков тиражирования, имеющих провайдера облачного хранилища в качестве источника, сейчас невозможно.
#XMSG
deployConfluentSourceFFDisabled=Развернуть потоки тиражирования с Confluent Kafka как источником сейчас невозможно, так как выполняется техническое обслуживание этой функции.
#XMSG
deployMaxDWCNewTableCrossed=Большие потоки тиражирования невозможно сохранить и развернуть одновременно. Сначала сохраните поток тиражирования, а потом разверните его.
#XMSG
deployInProgressInfo=Развертывание уже выполняется.
#XMSG
deploySourceObjectInUse=Исходные объекты {0} уже используются в потоках тиражирования {1}.
#XMSG
deployTargetSourceObjectInUse=Исходные объекты {0} уже используются в потоках тиражирования {1}. Целевые объекты {2} уже используются в потоках тиражирования {3}.
#XMSG
deployReplicationFlowCheckError=Ошибка при проверке потока тиражирования: {0}
#XMSG
preDeployTargetObjectInUse=Целевые объекты {0} уже используются в потоках тиражирования {1}, и вы не можете иметь один и тот же целевой объект в двух разных потоках тиражирования. Выберите другой целевой объект и повторите попытку.
#XMSG
runInProgressInfo=Поток тиражирования уже выполняется.
#XMSG
deploySignavioTargetFFDisabled=Развернуть потоки тиражирования, имеющие SAP Signavio в качестве цели, сейчас невозможно, так как выполняется техническое обслуживание этой функции.
#XMSG
deployHanaViewAsSourceFFDisabled=Развернуть потоки тиражирования, имеющие ракурсы в качестве исходных объектов для выбранного соединения с источником, сейчас невозможно. Повторите попытку позже.
#XMSG
deployMsOneLakeTargetFFDisabled=Развернуть потоки тиражирования, имеющие MS OneLake в качестве цели, сейчас невозможно, так как выполняется техническое обслуживание этой функции.
#XMSG
deploySFTPTargetFFDisabled=Развернуть потоки тиражирования, имеющие SFTP в качестве цели, сейчас невозможно, так как выполняется техническое обслуживание этой функции.
#XMSG
deploySFTPSourceFFDisabled=Развернуть потоки тиражирования, имеющие SFTP в качестве источника, сейчас невозможно, так как выполняется техническое обслуживание этой функции.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Техническое имя
#XFLD
businessNameInRenameTarget=Бизнес-имя
#XTOL
renametargetDialogTitle=Переименовать целевой объект
#XBUT
targetRenameButton=Переименовать
#XBUT
targetRenameCancel=Отменить
#XMSG
mandatoryTargetName=Введите имя.
#XMSG
dwcSpecialChar=Разрешен только специальный символ _ (подчеркивание).
#XMSG
dwcWithDot=Имя целевой таблицы может состоять из латинских букв, цифр, подчеркиваний (_) и точек (.). Первым символом должна быть буква, цифра или подчеркивание (не точка).
#XMSG
nonDwcSpecialChar=Разрешены специальные символы _ (подчеркивание), - (дефис) и . (точка)
#XMSG
firstUnderscorePattern=Имя не может начинаться с _ (подчеркивание).

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: просмотреть инструкцию SQL создания таблицы
#XMSG
sqlDialogMaxPKWarning=В Google BigQuery поддерживается максимум 16 первичных ключей, а исходный объект имеет больше. Поэтому в данной инструкции не определены первичные ключи.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Один или несколько исходных столбцов имеют типы данных, которые не могут быть определены как первичные ключи в Google BigQuery. Поэтому в данном случае первичные ключи не определены. В Google BigQuery только следующие типы данных могут иметь первичный ключ: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Скопировать и закрыть
#XBUT
closeDDL=Закрыть
#XMSG
copiedToClipboard=Скопировано в буфер обмена


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Объект "{0}" не может быть частью цепочки задач, так как он не имеет конца (поскольку включает объекты с типом загрузки "Начальная и дельта/только дельта").
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Объект "{0}" не может быть частью цепочки задач, так как он не имеет конца (поскольку включает объекты с типом загрузки "Начальная и дельта").
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Объект "{0}" невозможно добавить в цепочку задач.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Имеются несохраненные целевые объекты. Сохраните еще раз.{0}{0} Поведение этой функции изменилось: ранее целевые объекты создавались в целевой среде после развертывания потока тиражирования.{0} Теперь объекты уже существуют при сохранении потока тиражирования. Ваш поток тиражирования было создан до этого изменения и содержит новые объекты.{0} Сохраните поток тиражирования еще раз перед развертыванием, чтобы новые объекты были включены.
#XMSG
confirmChangeContentTypeMessage=Вы собираетесь изменить тип контента. При этом все существующие проекции будут удалены.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Имя субъекта
#XFLD
schemaDialogVersionName=Версия схемы
#XFLD
includeTechKey=Включить технический ключ
#XFLD
segementButtonFlat=Плоский
#XFLD
segementButtonNested=Вложенный
#XMSG
subjectNamePlaceholder=Поиск имени субъекта

#XMSG
@EmailNotificationSuccess=Конфигурация уведомлений по электронной почте во время выполнения сохранена.

#XFLD
@RuntimeEmailNotification=Уведомление по электронной почте во время выполнения

#XBTN
@TXT_SAVE=Сохранить


