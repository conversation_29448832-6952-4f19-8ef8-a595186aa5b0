#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Replikavimo srautas

#XFLD: Edit Schema button text
editSchema=Redaguoti schemą

#XTIT : Properties heading
configSchema=Konfigūruoti schemą

#XFLD: save changed button text
applyChanges=Taikyti keitimus


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Pasirinkti šaltinio ryšį
#XFLD
sourceContainernEmptyText=Pasirinkti konteinerį
#XFLD
targetConnectionEmptyText=Pasirinkti tikslinį ryšį
#XFLD
targetContainernEmptyText=Pasirinkti konteinerį
#XFLD
sourceSelectObjectText=Pasirinkti šaltinio objektą
#XFLD
sourceObjectCount=Šaltinio objektai ({0})
#XFLD
targetObjectText=Tiksliniai objektai
#XFLD
confluentBrowseContext=<PERSON>sirinkti kontekstą
#XBUT
@retry=Bandyti iš naujo
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Kliento naujinimas vykdomas.

#XTOL
browseSourceConnection=Naršyti šaltinio ryšį
#XTOL
browseTargetConnection=Naršyti tikslinį ryšį
#XTOL
browseSourceContainer=Naršyti šaltinio konteinerį
#XTOL
browseAndAddSourceDataset=Pridėti šaltinio objektų
#XTOL
browseTargetContainer=Naršyti tikslinį konteinerį
#XTOL
browseTargetSetting=Naršyti tikslinius parametrus
#XTOL
browseSourceSetting=Naršyti šaltinio parametrus
#XTOL
sourceDatasetInfo=Informacija
#XTOL
sourceDatasetRemove=Pašalinti
#XTOL
mappingCount=Rodo bendrą ne pavadinimu pagrįstų susiejimų / išraiškų skaičių.
#XTOL
filterCount=Rodo bendrą filtro sąlygų skaičių.
#XTOL
loading=Įkeliama...
#XCOL
deltaCapture=Delta įvedimas
#XCOL
deltaCaptureTableName=Delta įvedimo lentelė
#XCOL
loadType=Įkelti tipą
#XCOL
deleteAllBeforeLoading=Panaikinti viską prieš įkeliant
#XCOL
transformationsTab=Ekstrapoliavimai
#XCOL
settingsTab=Parametrai

#XBUT
renameTargetObjectBtn=Pervardyti tikslinį objektą
#XBUT
mapToExistingTargetObjectBtn=Susieti su esamu tiksliniu objektu
#XBUT
changeContainerPathBtn=Keisti konteinerio kelią
#XBUT
viewSQLDDLUpdated=Peržiūrėti SQL kūrimo lentelės išrašą
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Šaltinio objektas nepalaiko delta įvedimo, bet pasirinktam tiksliniam objektui įgalinta delta įvedimo parinktis.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Tikslinio objekto negalima naudoti, nes įgalintas delta fiksavimas,{0}tuo tarpu kai šaltinio objektas nepalaiko delta fiksavimo.{1}galite pasirinkti kitą tikslinį objektą, kuris nepalaiko delta fiksavimo.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Tikslinis objektas tokiu pavadinimu jau yra. Tačiau jo negalima naudoti,{0}nes įgalintas delta fiksavimas, tuo tarpu kai šaltinio objektas nepalaiko{0}delta fiksavimo.{1}Galite įvesti pavadinimą esamo tikslinio objekto, kuris nepalaiko{0}delta fiksavimo, arba pavadinimą, kurio dar nėra.
#XBUT
copySQLDDLUpdated=Kopijuoti SQL kūrimo lentelės išrašą
#XMSG
targetObjExistingNoCDCColumnUpdated=„Google BigQuery“ esančias lenteles turi sudaryti šie duomenų keitimo fiksavimo (CDC) stulpeliai:{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Toliau pateikti šaltinio objektai nepalaikomi, nes jie yra be pirminio rakto arba naudoja ryšį, kuris neatitinka pirminio rakto gavimo sąlygų:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Galimo sprendimo ieškokite SAP KBA 3 531 135.
#XLST: load type list values
initial=Tik pradinis
@emailUpdateError=Klaida naujinant pranešimų el. paštu sąrašą

#XLST
initialDelta=Pradinis ir delta

#XLST
deltaOnly=Tik delta
#XMSG
confluentDeltaLoadTypeInfo=„Confluent Kafka“ šaltiniui palaikomas tik įkėlimo tipo inicialas ir delta.
#XMSG
confirmRemoveReplicationObject=Ar norite patvirtinti, kad norite panaikinti replikavimą?
#XMSG
confirmRemoveReplicationTaskPrompt=Šis veiksmas panaikins esamus replikavimus. Ar norite tęsti?
#XMSG
confirmTargetConnectionChangePrompt=Šis veiksmas iš naujo nustatys tikslinį ryšį, tikslinį konteinerį ir panaikins visus tikslinius objektus. Ar norite tęsti?
#XMSG
confirmTargetContainerChangePrompt=Šis veiksmas iš naujo nustatys tikslinį konteinerį ir panaikins visus esamus tikslinius objektus. Ar norite tęsti?
#XMSG
confirmRemoveTransformObject=Ar norite patvirtinti, kad norite panaikinti ekstrapoliavimą {0}?
#XMSG
ErrorMsgContainerChange=Keičiant konteinerio kelią įvyko klaida.
#XMSG
infoForUnsupportedDatasetNoKeys=Šie šaltinio objektai nepalaikomi, nes jie neturi pirminio rakto:
#XMSG
infoForUnsupportedDatasetView=Šie tipo „Rakursai“ šaltinio objektai nepalaikomi:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Toliau pateiktas šaltinio objektas nepalaikomas, nes jis yra SQL rakursas, su įvesties parametrais:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Šie šaltinio objektai nepalaikomi, nes jų neleidžiama išgauti:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=„Confluent“ ryšiams leidžiami tik AVRO ir JSON keitimo į nuosekliąją formą formatai. Toliau pateikti objektai nepalaikomi, nes jie naudoja kitą keitimo į nuosekliąją formą formatą.
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Negalima gauti toliau pateiktų objektų schemos. Pasirinkite tinkamą kontekstą arba patikrinkite schemų registro konfigūraciją
#XTOL: warning dialog header on deleting replication task
deleteHeader=Naikinti
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=„Google BigQuery“ nepalaiko parametro „Panaikinti viską prieš įkeliant“.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Parametras „Panaikinti viską prieš vykdant“ panaikina ir iš naujo sukuria objektą (temą) prieš kiekvieną replikavimą. Taip pat panaikinami visi priskirti pranešimai.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Šis tikslinis tipas nepalaiko parametro „Panaikinti viską prieš vykdant“.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Techninis pavadinimas
#XCOL
connBusinessName=Verslo pavadinimas
#XCOL
connDescriptionName=Aprašas
#XCOL
connType=Tipas
#XMSG
connTblNoDataFoundtxt=Ryšių nerasta
#XMSG
connectionError=Iškviečiant ryšius įvyko klaida.
#XMSG
connectionCombinationUnsupportedErrorTitle=Ryšių derinys nepalaikomas
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Replikavimas iš {0} į {1} šiuo metu nepalaikomas.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Ryšio tipo derinys nepalaikomas
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Replikavimas iš ryšio su ryšio tipu „SAP HANA Cloud“, duomenų telkinio failai į {0} nepalaikomas. Galite replikuoti tik į „SAP Datasphere“.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Pasirinkti
#XBUT
containerCancelBtn=Atšaukti
#XTOL
containerSelectTooltip=Pasirinkti
#XTOL
containerCancelTooltip=Atšaukti
#XMSG
containerContainerPathPlcHold=Konteinerio kelias
#XFLD
containerContainertxt=Konteineris
#XFLD
confluentContainerContainertxt=Kontekstas
#XMSG
infoMessageForSLTSelection=Kaip konteineris leidžiamas tik /SLT/Masinio perkėlimo ID. SLT pasirinkite masinio perkėlimo ID (jei yra) ir spustelėkite Pateikti.
#XMSG
msgFetchContainerFail=Iškviečiant konteinerio duomenis įvyko klaida.
#XMSG
infoMessageForSLTHidden=Šis ryšys nepalaiko SLT aplankų, todėl jie nepateikiami toliau esančiame sąraše.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Pasirinkite konteinerį, kuriame yra antriniai aplankai.
#XMSG
sftpIncludeSubFolderText=Klaidinga
#XMSG
sftpIncludeSubFolderTextNew=Ne

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Filtro susiejimo dar nėra)
#XMSG
failToFetchRemoteMetadata=Iškviečiant metaduomenis įvyko klaida.
#XMSG
failToFetchData=Iškviečiant esamą tikslą įvyko klaida.
#XCOL
@loadType=Įkelti tipą
#XCOL
@deleteAllBeforeLoading=Panaikinti viską prieš įkeliant

#XMSG
@loading=Įkeliama...
#XFLD
@selectSourceObjects=Pasirinkti šaltinio objektus
#XMSG
@exceedLimit=Negalite importuoti daugiau nei {0} objektų vienu metu. Atžymėkite bent {1} objektus (-ų).
#XFLD
@objects=Objektai
#XBUT
@ok=Gerai
#XBUT
@cancel=Atšaukti
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Paskesnis
#XBUT
btnAddSelection=Pridėti pasirinkimą
#XTOL
@remoteFromSelection=Pašalinti iš pasirinkimo
#XMSG
@searchInForSearchField=Kur ieškoti: {0}

#XCOL
@name=Techninis pavadinimas
#XCOL
@type=Tipas
#XCOL
@location=Vieta
#XCOL
@label=Verslo pavadinimas
#XCOL
@status=Būsena

#XFLD
@searchIn=Kur ieškoti:
#XBUT
@available=Prieinama
#XBUT
@selection=Pasirinkimas

#XFLD
@noSourceSubFolder=Lentelės ir rakursai
#XMSG
@alreadyAdded=Jau yra diagramoje
#XMSG
@askForFilter=Yra daugiau nei {0} elementų. Įveskite filtro eilutę, kad susiaurintumėte elementų skaičių.
#XFLD: success label
lblSuccess=Pavyko
#XFLD: ready label
lblReady=Paruošta
#XFLD: failure label
lblFailed=Nepavyko
#XFLD: fetching status label
lblFetchingDetail=Iškvietimo išsami informacija

#XMSG Place holder text for tree filter control
filterPlaceHolder=Įveskite tekstą, kad filtruotumėte aukščiausio lygio objektus
#XMSG Place holder text for server search control
serverSearchPlaceholder=Įveskite ir paspauskite „Įvesti“, kad būtų vykdoma paieška
#XMSG
@deployObjects=Importuojami {0} objektai...
#XMSG
@deployObjectsStatus=Importuotų objektų skaičius : {0}. Objektų, kurių nepavyko importuoti, skaičius: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Nepavyko atidaryti vietinę saugyklos naršyklę.
#XMSG
@openRemoteSourceBrowserError=Nepavyko iškviesti šaltinio objektų.
#XMSG
@openRemoteTargetBrowserError=Nepavyko iškviesti tikslinių objektų.
#XMSG
@validatingTargetsError=Tvirtinant tikslus įvyko klaida.
#XMSG
@waitingToImport=Paruošta importuoti

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Viršytas didžiausias objektų skaičius. Vienam replikavimo srautui pasirinkite ne daugiau nei 500 objektų.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Techninis pavadinimas
#XFLD
sourceObjectBusinessName=Verslo pavadinimas
#XFLD
sourceNoColumns=Stulpelių skaičius
#XFLD
containerLbl=Konteineris

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Privalote pasirinkti replikavimo srauto šaltinio ryšį.
#XMSG
validationSourceContainerNonExist=Privalote pasirinkti šaltinio ryšio konteinerį.
#XMSG
validationTargetNonExist=Privalote pasirinkti replikavimo srauto tikslinį ryšį.
#XMSG
validationTargetContainerNonExist=Privalote pasirinkti tikslinio ryšio konteinerį.
#XMSG
validationTruncateDisabledForObjectTitle=Replikavimas į objektų saugyklas.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Replikuoti į debesies saugyklą galima, tik jei nustatyta parinktis „Panaikinti viską prieš įkeliant“ arba tikslinėje vietoje nėra tikslinio objekto.{0}{0} Norėdami vis tiek įjungti objektų, kuriems nenustatyta parinktis „Panaikinti viską prieš įkeliant“, replikavimą, prieš vykdydami replikavimo srautą įsitikinkite, kad sistemoje nėra tikslinio objekto.
#XMSG
validationTaskNonExist=Replikavimo sraute privalote turėti bent vieną replikavimą.
#XMSG
validationTaskTargetMissing=Privalote turėti replikavimo tikslą su šaltiniu: {0}
#XMSG
validationTaskTargetIsSAC=Pasirinktas tikslas yra SAC artefaktas: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Pasirinktas tikslas nėra palaikomas vietinėje lentelėje: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Objektas tokiu pavadinimu jau yra tiksle. Tačiau šis objektas negali būti naudojamas kaip replikavimo srauto į vietinę saugyklą, nes ji nėra vietinė lentelė, tikslinis objektas.
#XMSG
validateSourceTargetSystemDifference=Privalote pasirinkti kitą šaltinio ir tikslinio ryšio bei konteinerio derinius, skirtus replikavimo srautui.
#XMSG
validateDuplicateSources=vieno ar daugiau replikavimų turi pasikartojančius šaltinio objektų pavadinimus: {0}.
#XMSG
validateDuplicateTargets=vieno ar daugiau replikavimų turi pasikartojančius tikslinių objektų pavadinimus: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=Šaltinio objektas {0} nepalaiko delta įvedimo, o tikslinis objektas {1} palaiko. Turite pašalinti replikavimą.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Replikacijai su tikslinio objekto pavadinimu {0} turite pasirinkti įkėlimo tipą „Pradinis ir delta“ .
#XMSG
validationAutoRenameTarget=Tiksliniai stulpeliai pervardyti.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Pridėtas automatinis projektavimas ir šie tiksliniai stulpeliai pervardyti, kad būtų galima atlikti replikavimą į tikslinę vietą:{1}{1} {0} {1}{1}Taip yra dėl vienos iš šių priežasčių:{1}{1}{2} Nepalaikomi simboliai{1}{2} Rezervuotas prefiksas
#XMSG
validationAutoRenameTargetDescriptionUpdated=Pridėtas automatinis projektavimas ir šie tiksliniai stulpeliai pervardyti, kad būtų galima atlikti „Google BigQuery“ replikavimą:{1}{1} {0} {1}{1}Taip yra dėl vienos iš šių priežasčių:{1}{1}{2} Rezervuotas stulpelio pavadinimas{1}{2} Nepalaikomi simboliai{1}{2} Rezervuotas prefiksas
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Pridėtas automatinis projektavimas ir šie tiksliniai stulpeliai pervardyti, kad būtų galima atlikti „Confluent“ replikavimus:{1}{1} {0} {1}{1}Taip yra dėl vienos iš šių priežasčių:{1}{1}{2} Rezervuotas stulpelio pavadinimas{1}{2} Nepalaikomi simboliai{1}{2} Rezervuotas prefiksas
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Pridėtas automatinis projektavimas ir šie tiksliniai stulpeliai pervardyti, kad būtų galima atlikti tikslo replikavimus:{1}{1} {0} {1}{1}Taip yra dėl vienos iš šių priežasčių:{1}{1}{2} Rezervuotas stulpelio pavadinimas{1}{2} Nepalaikomi simboliai{1}{2} Rezervuotas prefiksas
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Tikslinis objektas pervardytas.
#XMSG
autoRenameInfoDesc=Tikslinis objektas buvo pervardytas, nes jame yra nepalaikomų simbolių. Palaikomi tik šie simboliai: {0}{0}{1}A–Z{0}{1}a–z{0}{1}0–9{0}{1}.(taškas){0}{1}_(pabraukimo brūkšnys){0}{1}-(brūkšnelis)
#XMSG
validationAutoTargetTypeConversion=Tikslinių duomenų tipai pakeisti.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Toliau nurodytų tikslinių stulpelių tipas pakeistas, nes „Google BigQuery“ šaltinio duomenų tipai nepalaikomi:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Toliau nurodytų tikslinių stulpelių tiksliniai duomenų tipai pakeisti, nes šaltinio duomenų tipai tiksliniame ryšyje nepalaikomi:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Sutrumpinti tikslinių stulpelių pavadinimai.
#XMSG
validationMaxCharLengthGBQTargetDescription=„Google BigQuery“ stulpelių pavadinimus gali sudaryti iki 300 simbolių. Naudokite projekciją, norėdami sutrumpinti šių tikslinių stulpelių pavadinimus:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Pirminiai raktai nebus kuriami.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=„Google BigQuery“ daugiausia palaikoma 16 pirminių raktų, bet šaltinio objekte jų yra daugiau. Nei vienas pirminis raktas nebus sukurtas tiksliniame objekte.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Viename ar daugiau šaltinio stulpelių yra duomenų tipų, kurių negalima apibrėžti kaip pirminių raktų „Google BigQuery“. Nė vienas pirminis raktas nebus sukurtas tiksliniame objekte.{0}{0}Šie tikslinių duomenų tipai yra suderinami su „Google BigQuery“ duomenų tipais, kuriems galima apibrėžti pirminį raktą:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Apibrėžkite vieną ar kelis stulpelius kaip pirminį raktą.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Turite apibrėžti vieną ar kelis stulpelius kaip pirminį raktą; tam naudokite šaltinio schemos dialogą.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Apibrėžkite vieną ar kelis stulpelius kaip pirminį raktą.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Turite apibrėžti vieną ar daugiau stulpelių kaip pirminį raktą, kuris atitinka pirminio rakto apribojimus, taikomus jūsų šaltinio objektui. Norėdami tai padaryti, eikite į savo šaltinio objekto konfigūravimo schemą.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Įveskite tinkamą maksimalią skaidinio reikšmę.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Maksimali skaidinio reikšmė turi būti ≥ 1 ir ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Apibrėžkite vieną ar kelis stulpelius kaip pirminį raktą.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Norėdami replikuoti objektą, turite apibrėžti vieną ar kelis stulpelius kaip pirminį raktą. Tai galite padaryti naudodami projekciją.
#XMSG
validateHDLFNoPKExistingDatasetError=Apibrėžkite vieną ar kelis stulpelius kaip pirminį raktą.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Norint replikuoti duomenis į esamą tikslinį objektą, jame turi būti vienas ar keli stulpeliai, apibrėžti kaip pirminis raktas. {0} Vieną ar kelis stulpelius galima apibrėžti kaip pirminį raktą keliais būdais: {0} {1} Pakeiskite esamą tikslinį objektą naudodami vietinę lentelių rengyklę. Tuomet iš naujo įkelkite replikavimo srautą.{0}{1} Pervardykite tikslinį objektą replikavimo sraute. Tuomet pradėjus vykdymą bus iš karto sukurtas naujas objektas. Baigę pervardyti galite apibrėžti vieną ar kelis stulpelius kaip pirminį raktą projekcijoje.{0}{1} Susiekite objektą su kitu esamu tiksliniu objektu, kuriame vienas ar keli stulpeliai jau apibrėžti kaip pirminis raktas.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Pasirinktas tikslas jau yra saugykloje: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Delta fiksavimo lentelių pavadinimus jau naudoja kitos saugyklos lentelės: {0}. Turite pervardyti šiuos tikslinius objektus, kad įsitikintumėte, jog susiję delta fiksavimo lentelių pavadinimai yra unikalūs, kad galėtumėte diegti replikavimo srautą.
#XMSG
validateConfluentEmptySchema=Apibrėžti schemą
#XMSG
validateConfluentEmptySchemaDescUpdated=Šaltinio lentelė neturi schemos. Kad ją nustatytumėte, pasirinkite „Konfigūruoti schemą“
#XMSG
validationCSVEncoding=Netinkamas CSV kodavimas
#XMSG
validationCSVEncodingDescription=Užduoties CSV kodavimas netinkamas.
#XMSG
validateConfluentEmptySchema=Pasirinkite suderinamą tikslinį stulpelių duomenų tipą
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Pasirinkite suderinamą tikslinį stulpelių duomenų tipą
#XMSG
globalValidateTargetDataTypeDesc=Įvyko stulpelių susiejimo klaida. Eikite į „Projekcija“ ir įsitikinkite, kad visi šaltinio stulpeliai yra susieti su unikaliu stulpeliu, kurio duomenų tipas suderinamas ir visos apibrėžtos išraiškos tinkamos.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Dubliuoti stulpelių pavadinimai.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Dubliuoti stulpelių pavadinimai nepalaikomi. Norėdami tai išspręsti, naudokite projekcijos dialogo langą. Šie tiksliniai objektai turi dubliuotus stulpelių pavadinimus: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Dubliuoti stulpelių pavadinimai.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Dubliuoti stulpelių pavadinimai nepalaikomi. Šie tiksliniai objektai turi dubliuotus stulpelių pavadinimus: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Duomenyse gali būti nenuoseklumų.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Įkėlimo tipas „Tik delta“ neatsižvelgs į šaltinyje atliktus pakeitimus nuo paskutinio įrašymo iki kito vykdymo.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Pakeiskite įkėlimo tipą į „Pradinis“.
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=ABAP pagrįstus objektus, kurie yra be pirminio rakto, replikuoti galima tik įkėlimo tipui „Tik pradinis“.
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Išjunkite delta įvedimą.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Norėdami replikuoti objektą, kuris yra be pirminio rakto, naudodami šaltinio ryšio tipą ABAP, pirma išjunkite šiai lentelei deltą įvedimą.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Pakeiskite tikslinį objektą.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Tikslinio objekto naudoti negalima, nes įgalintas delta įvedimas. Galite pervardyti tikslinį objektą ir išjungti naujo (pervardyto) objekto delta įvedimą arba susieti šaltinio objektą su tiksliniu objektu, kuriam išjungtas delta įvedimas.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Pakeiskite tikslinį objektą.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Negalima naudoti tikslinio objekto, nes jis yra be būtino techninio stulpelio __load_package_id. Galite tiksliniam objektui suteikti naują pavadinimą, kuris dar nenaudojamas. Tuomet sistema sukurs naują objektą, kurio apibrėžimas sutaps su šaltinio objekto ir kuris bus su techniniu stulpeliu. Arba galite susieti tikslinį objektą su esamu objektu, kuris yra su būtinu techniniu stulpeliu (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Pakeiskite tikslinį objektą.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Negalima naudoti tikslinio objekto, nes jis yra be būtino techninio stulpelio __load_record_id. Galite tiksliniam objektui suteikti naują pavadinimą, kuris dar nenaudojamas. Tuomet sistema sukurs naują objektą, kurio apibrėžimas sutaps su šaltinio objekto ir kuris bus su techniniu stulpeliu. Arba galite susieti tikslinį objektą su esamu objektu, kuris yra su būtinu techniniu stulpeliu (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Pakeiskite tikslinį objektą.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Negalima naudoti tikslinio objekto, nes jo techninio stulpelio __load_record_id duomenų tipas nėra „string(44)“. Galite tiksliniam objektui suteikti naują pavadinimą, kuris dar nenaudojamas. Tuomet sistema sukurs naują objektą, kurio apibrėžimas sutaps su šaltinio objekto, todėl jis bus su tinkamu duomenų tipu. Arba galite susieti tikslinį objektą su esamu objektu, kuris yra su būtinu tinkamo duomenų tipo techniniu stulpeliu (__load_record_id).
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Pakeiskite tikslinį objektą.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Negalima naudoti tikslinio objekto, nes jis yra su pirminiu raktu, o šaltinio objektas – be jo. Galite tiksliniam objektui suteikti naują pavadinimą, kuris dar nenaudojamas. Tuomet sistema sukurs naują objektą, kurio apibrėžimas sutaps su šaltinio objekto, todėl jis bus be pirminio rakto. Arba galite susieti tikslinį objektą su esamu objektu, kuris yra su būtinu techniniu stulpeliu (__load_package_id) ir be pirminio rakto.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Pakeiskite tikslinį objektą.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Negalima naudoti tikslinio objekto, nes jis yra su pirminiu raktu, o šaltinio objektas – be jo. Galite tiksliniam objektui suteikti naują pavadinimą, kuris dar nenaudojamas. Tuomet sistema sukurs naują objektą, kurio apibrėžimas sutaps su šaltinio objekto, todėl jis bus be pirminio rakto. Arba galite susieti tikslinį objektą su esamu objektu, kuris yra su būtinu techniniu stulpeliu (__load_record_id) ir be pirminio rakto.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Pakeiskite tikslinį objektą.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Negalima naudoti tikslinio objekto, nes jo techninio stulpelio __load_package_id duomenų tipas nėra „binary(>=256)“. Galite tiksliniam objektui suteikti naują pavadinimą, kuris dar nenaudojamas. Tuomet sistema sukurs naują objektą, kurio apibrėžimas sutaps su šaltinio objekto, todėl jis bus su tinkamu duomenų tipu. Arba galite susieti tikslinį objektą su esamu objektu, kuris yra su būtinu tinkamo duomenų tipo techniniu stulpeliu (__load_package_id).
#XMSG
validationAutoRenameTargetDPID=Tiksliniai stulpeliai pervardyti.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Pašalinti šaltinio objektą.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Šaltinio objektas neturi raktinio stulpelio, kuris šiame kontekste nepalaikomas.
#XMSG
validationAutoRenameTargetDPIDDescription=Pridėtas automatinis projektavimas ir šie tiksliniai stulpeliai pervardyti, kad būtų galima atlikti replikavimą iš ABAP šaltinio be raktų:{1}{1} {0} {1}{1}Taip yra dėl vienos iš šių priežasčių:{1}{1}{2} Rezervuotas stulpelio pavadinimas{1}{2} Nepalaikomi simboliai{1}{2} Rezervuotas prefiksas
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Replikavimas į {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Šiuo metu negalima įrašyti ir įdiegti replikavimo srautų, kurių tikslas yra {0}, nes atliekame šios funkcijos priežiūrą.
#XMSG
TargetColumnSkippedLTF=Tikslinis stulpelis praleistas.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Tikslinis stulpelis praleistas dėl nepalaikomo duomenų tipo. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Laiko stulpelis kaip pirminis raktas.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Šaltinio objektas kaip pirminį raktą turi laiko stulpelį, kuris šiame kontekste nepalaikomas.
#XMSG
validateNoPKInLTFTarget=Trūksta pirminio rakto.
#XMSG
validateNoPKInLTFTargetDescription=Tiksliniame objekte pirminis raktas nenustatytas, o tai šiame kontekste nepalaikoma.
#XMSG
validateABAPClusterTableLTF=ABAP sankaupos lentelė.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Šaltinio objektas yra ABAP sankaupos lentelė, kuri šiame kontekste nepalaikoma.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Regis, dar nepridėjote jokių duomenų.
#YINS
welcomeText2=Jei norite pradėti replikavimo srautą, kairėje pasirinkite ryšį ir šaltinio objektą.

#XBUT
wizStep1=Pasirinkti šaltinio ryšį
#XBUT
wizStep2=Pasirinkite šaltinio konteinerį
#XBUT
wizStep3=Pridėkite šaltinio objektus

#XMSG
limitDataset=Pasiektas didžiausias objektų skaičius. Pašalinkite esamus objektus, kad galėtumėte pridėti naujų, arba sukurkite naują replikavimo srautą.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Negalima pradėti replikavimo srauto į šį ne SAP tikslinį ryšį, nes šį mėnesį nėra prieinamo siunčiamo kiekio.
#XMSG
premiumOutBoundRFAdminWarningMsg=Administratorius gali padidinti šio kliento „Premium“ siunčiamus blokus, kad šį mėnesį būtų prieinamas siunčiamas kiekis.
#XMSG
messageForToastForDPIDColumn2=Naujas stulpelis įtrauktas į {0} objekto (-ų) tikslą. Tai reikalinga apdoroti pasikartojantiems įrašams, susijusiems su ABAP pagrįstais šaltinio objektais, kurie yra be pirminio rakto.
#XMSG
PremiumInboundWarningMessage=Priklausomai nuo replikavimo srautų skaičiaus ir replikuotinų duomenų apimties, SAP HANA resursai {0}, reikalingi replikuoti duomenis per {1}, gali viršyti jūsų nuomininko turimus pajėgumus.
#XMSG
PremiumInboundWarningMsg=Priklausomai nuo replikavimo srautų skaičiaus ir replikuotinų duomenų apimties,{0}SAP HANA ištekliai, reikalingi duomenims replikuoti per „{1}“, gali viršyti jūsų kliento pajėgumus.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Įveskite projekcijos pavadinimą.
#XMSG
emptyTargetColumn=Įveskite tikslinio stulpelio pavadinimą.
#XMSG
emptyTargetColumnBusinessName=Įveskite tikslinio stulpelio verslo pavadinimą.
#XMSG
invalidTransformName=Įveskite projekcijos pavadinimą.
#XMSG
uniqueColumnName=Pervardykite tikslinį stulpelį.
#XMSG
copySourceColumnLbl=Kopijuoti stulpelius iš šaltinio objekto
#XMSG
renameWarning=Pervardydami tikslinę lentelę parinkite unikalų pavadinimą. Jei srityje jau yra lentelė su nauju pavadinimu, ji naudos tos lentelės apibrėžimą.

#XMSG
uniqueColumnBusinessName=Pervardykite tikslinio stulpelio verslo pavadinimą.
#XMSG
uniqueSourceMapping=Pasirinkite kitą šaltinio stulpelį.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=Šaltinio stulpelis {0} jau naudojamas toliau pateiktiems tiksliniams stulpeliams:{1}{1}{2}{1}{1} Norėdami įrašyti projekciją, šiam tiksliniam stulpeliui arba kitiems tiksliniams stulpeliams pasirinkite dar nenaudojamą šaltinio stulpelį.
#XMSG
uniqueColumnNameDescription=Įvestas tikslinio stulpelio pavadinimas jau yra. Kad galėtumėte išsaugoti projekciją, turite įvesti unikalų stulpelio pavadinimą.
#XMSG
uniqueColumnBusinessNameDesc=Tikslinio stulpelio verslo pavadinimas jau yra. Kad galėtumėte išsaugoti projekciją, turite įvesti unikalų stulpelio verslo pavadinimą.
#XMSG
emptySource=Pasirinkite bent vieną stulpelį arba įveskite konstantą.
#XMSG
emptySourceDescription=Norėdami sukurti galiojantį susiejimo įrašą, turėsite pasirinkti šaltinio stulpelį arba įvesti konstantos reikšmę.
#XMSG
emptyExpression=Apibrėžti susiejimą.
#XMSG
emptyExpressionDescription1=Pasirinkite pradinį stulpelį, su kuriuo norite susieti tikslinį stulpelį, arba pažymėkite žymimąjį laukelį stulpelyje {0} Funkcijos / konstantos {1}. {2} {2} Funkcijos įvedamos automatiškai pagal tikslinių duomenų tipą. Pastovias vertes galima įvesti rankiniu būdu.
#XMSG
numberExpressionErr=Įveskite numerį.
#XMSG
numberExpressionErrDescription=Pasirinkote skaitmeninių duomenų tipą. Tai reiškia, kad galite įvesti tik skaičius ir, jei reikia, dešimtainį skyriklį. Nenaudokite viengubų kabučių.
#XMSG
invalidLength=Įveskite galiojančią ilgio reikšmę.
#XMSG
invalidLengthDescription=Duomenų tipo ilgis turi būti lygus arba didesnis už šaltinio stulpelio ilgį ir gali būti nuo 1 iki 5 000.
#XMSG
invalidMappedLength=Įveskite galiojančią ilgio reikšmę.
#XMSG
invalidMappedLengthDescription=Duomenų tipo ilgis turi būti lygus arba didesnis už šaltinio stulpelio „{0}“ ilgį ir gali būti nuo 1 iki 5 000.
#XMSG
invalidPrecision=Įveskite galiojančią tikslumo reikšmę.
#XMSG
invalidPrecisionDescription=Tikslumas apibrėžia bendrą skaitmenų skaičių. Skalė apibrėžia skaitmenų skaičių po kablelio ir gali būti nuo 0 iki tikslumo.{0}{0} Pavyzdžiui: {0}{1} Tikslumas 6 , skalė 2 atitinka tokius skaičius kaip 1234,56.{0}{1} Tikslumas 6, skalė 6 atitinka skaičius kaip 0,123546.{0} {0} Tikslumas ir skalė turi būti suderinami su šaltinio tikslumu ir skale, kad visi šaltinio skaitmenys tilptų į tikslinį lauką. Pavyzdžiui, jei šaltinio tikslumas yra 6 ir skalė yra 2 (ir dėl to prieš dešimtainį kablelį yra kitų skaitmenų nei 0), tikslinis laukas negali turėti tikslumo 6 ir skalės 6.
#XMSG
invalidPrimaryKey=Įveskite bent vieną pirminį raktą.
#XMSG
invalidPrimaryKeyDescription=Šiai schemai neapibrėžtas pirminis raktas.
#XMSG
invalidMappedPrecision=Įveskite galiojančią tikslumo reikšmę.
#XMSG
invalidMappedPrecisionDescription1=Tikslumas apibrėžia bendrą skaitmenų skaičių. Skalė apibrėžia skaitmenų skaičių po kablelio ir gali būti nuo 0 iki tikslumo.{0}{0} Pavyzdžiui:{0}{1} Tikslumas 6 , skalė 2 atitinka tokius skaičius kaip 1234,56.{0}{1} Tikslumas 6, skalė 6 atitinka skaičius kaip 0,123546.{0}{0}Duomenų tipo tikslumas turi būti lygus arba didesnis už šaltinio tikslumą ({2}).
#XMSG
invalidScale=Įveskite galiojančią skalės reikšmę.
#XMSG
invalidScaleDescription=Tikslumas apibrėžia bendrą skaitmenų skaičių. Skalė apibrėžia skaitmenų skaičių po kablelio ir gali būti nuo 0 iki tikslumo.{0}{0} Pavyzdžiui: {0}{1} Tikslumas 6 , skalė 2 atitinka tokius skaičius kaip 1234,56.{0}{1} Tikslumas 6, skalė 6 atitinka skaičius kaip 0,123546.{0} {0} Tikslumas ir skalė turi būti suderinami su šaltinio tikslumu ir skale, kad visi šaltinio skaitmenys tilptų į tikslinį lauką. Pavyzdžiui, jei šaltinio tikslumas yra 6 ir skalė yra 2 (ir dėl to prieš dešimtainį kablelį yra kitų skaitmenų nei 0), tikslinis laukas negali turėti tikslumo 6 ir skalės 6.
#XMSG
invalidMappedScale=Įveskite galiojančią skalės reikšmę.
#XMSG
invalidMappedScaleDescription1=Tikslumas apibrėžia bendrą skaitmenų skaičių. Skalė apibrėžia skaitmenų skaičių po kablelio ir gali būti nuo 0 iki tikslumo.{0}{0} Pavyzdžiui:{0}{1} Tikslumas 6 , skalė 2 atitinka tokius skaičius kaip 1234,56.{0}{1} Tikslumas 6, skalė 6 atitinka skaičius kaip 0,123546.{0}{0}Duomenų tipo skalė turi būti lygi arba didesnė už šaltinio skalę ({2}).
#XMSG
nonCompatibleDataType=Pasirinkite suderinamą tikslinį stulpelių duomenų tipą.
#XMSG
nonCompatibleDataTypeDescription1=Čia nurodytas duomenų tipas turi būti suderinamas su šaltinio duomenų tipu ({0}). {1}{1} Pavyzdžiui: jei šaltinio stulpelyje yra duomenų tipo eilutė ir joje yra raidžių, negalite naudoti dešimtainio duomenų tipo.
#XMSG
invalidColumnCount=Pasirinkite šaltinio stulpelį.
#XMSG
ObjectStoreInvalidScaleORPrecision=Įveskite tinkamą tikslumo ir skalės reikšmę.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=Pirmoji reikšmė yra tikslumas, kuris apibrėžia bendrosios sumos skaitmenų skaičių. Antroji reikšmė yra skalė, kuri apibrėžia skaitmenis po dešimtainių skilčių kablelio. Įveskite tikslinės skalės reikšmę, didesnę nei šaltinio skalės reikšmė ir įsitikinkite, kad skirtumas tarp įvestos tikslinės skalės ir tikslumo reikšmės yra didesnis nei skirtumas tarp šaltinio skalės ir tikslumo reikšmės.
#XMSG
InvalidPrecisionORScale=Įveskite tinkamą tikslumo ir skalės reikšmę.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=Pirmoji reikšmė yra tikslumas, apibrėžiantis bendrą skaitmenų skaičių. Antroji reikšmė yra skalė, apibrėžianti skaitmenis po dešimtainio skyriklio.{0}{0}Kadangi „Google BigQuery“ nepalaiko šaltinio duomenų tipo, jis konvertuojamas į tikslinių duomenų tipą DECIMAL. Šiuo atveju tikslumą galima apibrėžti tik nuo 38 iki 76, o skalę – nuo 9 iki 38. Be to, rezultatas iš tikslumo atėmus skalę, nurodantis skaitmenis prieš dešimtainį skyriklį, turi būti nuo 29 iki 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=Pirmoji reikšmė yra tikslumas, apibrėžiantis bendrą skaitmenų skaičių. Antroji reikšmė yra skalė, apibrėžianti skaitmenis po dešimtainio skyriklio.{0}{0}Kadangi „Google BigQuery“ nepalaiko šaltinio duomenų tipo, jis konvertuojamas į tikslinių duomenų tipą DECIMAL. Šiuo atveju tikslumą reikia apibrėžti tik kaip 20 ar didesnį. Be to, rezultatas iš tikslumo atėmus skalę, nurodantis skaitmenis prieš dešimtainį skyriklį, turi būti 20 ar daugiau.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=Pirmoji reikšmė yra tikslumas, apibrėžiantis bendrą skaitmenų skaičių. Antroji reikšmė yra skalė, apibrėžianti skaitmenis po dešimtainio skyriklio.{0}{0}Kadangi tikslas nepalaiko šaltinio duomenų tipo, jis konvertuojamas į tikslinių duomenų tipą DECIMAL. Šiuo atveju tikslumą reikia apibrėžti bet kokiu numeriu, didesniu arba lygiu 1 ir mažesniu arba lygiu 38, o skalė turi būti mažesnė arba lygi tikslumui.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=Pirmoji reikšmė yra tikslumas, apibrėžiantis bendrą skaitmenų skaičių. Antroji reikšmė yra skalė, apibrėžianti skaitmenis po dešimtainio skyriklio.{0}{0}Kadangi „Google BigQuery“ nepalaiko šaltinio duomenų tipo, jis konvertuojamas į tikslinių duomenų tipą DECIMAL. Šiuo atveju tikslumą reikia apibrėžti tik kaip 20 ar didesnį. Be to, rezultatas iš tikslumo atėmus skalę, nurodantis skaitmenis prieš dešimtainį skyriklį, turi būti 20 ar daugiau.
#XMSG
invalidColumnCountDescription=Norėdami sukurti galiojantį susiejimo įrašą, turėsite pasirinkti šaltinio stulpelį arba įvesti konstantos reikšmę.
#XMSG
duplicateColumns=Pervardykite tikslinį stulpelį.
#XMSG
duplicateGBQCDCColumnsDesc=Tikslinio stulpelio pavadinimas rezervuotas „Google BigQuery“. Turite jį pervardyti, kad galėtumėte įrašyti projekciją.
#XMSG
duplicateConfluentCDCColumnsDesc=Tikslinio stulpelio pavadinimas rezervuotas „Confluent“. Turite jį pervardyti, kad galėtumėte įrašyti projekciją.
#XMSG
duplicateSignavioCDCColumnsDesc=Tikslinio stulpelio pavadinimas rezervuotas „SAP Signavio“. Turite jį pervardyti, kad galėtumėte įrašyti projekciją.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Tikslinio stulpelio pavadinimas rezervuotas „MS OneLake“. Turite jį pervardyti, kad galėtumėte įrašyti projekciją.
#XMSG
duplicateSFTPCDCColumnsDesc=Tikslinio stulpelio pavadinimas rezervuotas SFTP. Turite jį pervardyti, kad galėtumėte įrašyti projekciją.
#XMSG
GBQTargetNameWithPrefixUpdated1=Tikslinio stulpelio pavadinime yra prefiksas, rezervuotas „Google BigQuery“. Turite jį pervardyti, kad galėtumėte įrašyti projekciją.{0}{0}Tikslinio stulpelio pavadinimas negali prasidėti jokia iš šių eilučių: {0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Sutrumpinti tikslinio stulpelio pavadinimą.
#XMSG
GBQtargetMaxLengthDesc=„Google BigQuery“ stulpelio pavadinime gali būti daugiausia 300 simbolių. Sutrumpinkite tikslinio stulpelio pavadinimą, kad galėtumėte įrašyti projekciją.
#XMSG
invalidMappedScalePrecision=Tikslumas ir skalė turi būti suderinami su šaltinio tikslumu ir skale, kad visi šaltinio skaitmenys tilptų į tikslinį lauką.
#XMSG
invalidMappedScalePrecisionShortText=Įveskite tinkamą tikslumo ir skalės reikšmę.
#XMSG
validationIncompatiblePKTypeDescProjection3=Viename ar dagiau šaltinio stulpelių yra duomenų tipų, kurių negalima apibrėžti kaip pirminių raktų „Google BigQuery“. Nė vienas pirminis raktas nebus sukurtas tiksliniame objekte.{0}{0}Šie tikslinių duomenų tipai yra suderinami su „Google BigQuery“ duomenų tipais, kuriems galima apibrėžti pirminį raktą:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Panaikinkite stulpelio __message_id žymėjimą.
#XMSG
uncheckColumnMessageIdDesc=Stulpelis: pirminis raktas
#XMSG
validationOpCodeInsert=Turite įvesti reikšmę įterpimui.
#XMSG
recommendDifferentPrimaryKey=Rekomenduojame elemento lygmenyje pasirinkti kitą pirminį raktą.
#XMSG
recommendDifferentPrimaryKeyDesc=Kai operacijos kodas jau apibrėžtas, rekomenduojama masyvo indeksui ir elementams pasirinkti skirtingus pirminius raktus, kad išvengtumėte problemų, pvz., stulpelių kartojimosi.
#XMSG
selectPrimaryKeyItemLevel=Antraštės ir elemento lygmeniui reikia pasirinkti bent vieną pirminį raktą.
#XMSG
selectPrimaryKeyItemLevelDesc=Kai masyvas arba susiejimas yra išplėstas, turite pasirinkti du pirminius raktus: vieną antraštės lygmenyje ir vieną elemento lygmenyje.
#XMSG
invalidMapKey=Antraštės lygmeniui reikia pasirinkti bent vieną pirminį raktą.
#XMSG
invalidMapKeyDesc=Kai masyvas arba susiejimas yra išplėstas, turite pasirinkti pirminį raktą antraštės lygmenyje.
#XFLD
txtSearchFields=Ieškoti tikslinių stulpelių
#XFLD
txtName=Pavadinimas
#XMSG
txtSourceColValidation=Vienas ar keli šaltinio stulpeliai nepalaikomi:
#XMSG
txtMappingCount=Susiejimai ({0})
#XMSG
schema=Schema
#XMSG
sourceColumn=Šaltinio stulpeliai
#XMSG
warningSourceSchema=Bet kokie schemos pakeitimai paveiks projekcijos dialogo susiejimus.
#XCOL
txtTargetColName=Tikslinis stulpelis (techninis pavadinimas)
#XCOL
txtDataType=Tikslinių duomenų tipas
#XCOL
txtSourceDataType=Duomenų šaltinio tipas
#XCOL
srcColName=Šaltinio stulpelis (techninis pavadinimas)
#XCOL
precision=Tikslumas
#XCOL
scale=Skalė
#XCOL
functionsOrConstants=Funkcijos / konstantos
#XCOL
txtTargetColBusinessName=Tikslinis stulpelis (verslo pavadinimas)
#XCOL
prKey=Pirminis raktas
#XCOL
txtProperties=Ypatybės
#XBUT
txtOK=Įrašyti
#XBUT
txtCancel=Atšaukti
#XBUT
txtRemove=Pašalinti
#XFLD
txtDesc=Aprašas
#XMSG
rftdMapping=Susiejimas
#XFLD
@lblColumnDataType=Duomenų tipas
#XFLD
@lblColumnTechnicalName=Techninis pavadinimas
#XBUT
txtAutomap=Automatinis susiejimas
#XBUT
txtUp=Aukštyn
#XBUT
txtDown=Žemyn

#XTOL
txtTransformationHeader=Ekstrapoliavimas
#XTOL
editTransformation=Redaguoti
#XTOL
primaryKeyToolip=Raktas


#XMSG
rftdFilter=Filtruoti
#XMSG
rftdFilterColumnCount=Šaltinis: {0}({1})
#XTOL
rftdFilterColSearch=Ieškoti
#XMSG
rftdFilterColNoData=Nėra rodytinų stulpelių
#XMSG
rftdFilteredColNoExps=Nėra filtro išraiškų
#XMSG
rftdFilterSelectedColTxt=Pridėti filtrą, skirtą
#XMSG
rftdFilterTxt=Filtras prieinamas
#XBUT
rftdFilterSelectedAddColExp=Pridėti išraišką
#YINS
rftdFilterNoSelectedCol=Pasirinkite stulpelį filtrui pridėti.
#XMSG
rftdFilterExp=Filtro išraiška
#XMSG
rftdFilterNotAllowedColumn=Šiame stulpelyje nepalaikomas filtrų pridėjimas.
#XMSG
rftdFilterNotAllowedHead=Nepalaikomas stulpelis
#XMSG
rftdFilterNoExp=Nėra apibrėžtų filtrų
#XTOL
rftdfilteredTt=Filtruota
#XTOL
rftdremoveexpTt=Pašalinti filtro išraišką
#XTOL
validationMessageTt=Tikrinimo pranešimai
#XTOL
rftdFilterDateInp=Pasirinkite datą
#XTOL
rftdFilterDateTimeInp=Pasirinkite datą ir laiką
#XTOL
rftdFilterTimeInp=Pasirinkite laiką
#XTOL
rftdFilterInp=Įveskite reikšmę
#XMSG
rftdFilterValidateEmptyMsg={1} stulpelio {0} filtro išraiškos yra tuščios
#XMSG
rftdFilterValidateInvalidNumericMsg={1} stulpelio {0} filtro išraiškose yra netinkamų skaitinių reikšmių
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Filtro išraiškoje turi būti tinkamos skaitinės reikšmės
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Jei tikslinio objekto schema pasikeitė, pagrindiniame puslapyje naudokite funkciją „Susieti su esamu tiksliniu objektu“, kad pritaikytumėte pakeitimus, ir vėl susiekite tikslinį objektą su jo šaltiniu.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Jei tikslinė lentelė jau yra ir pakeičiama susiejimo schema, turite atitinkamai pakeisti tikslinę lentelę prieš diegdami replikavimo srautą.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Savo susiejimo schemoje turite atitinkamai pakeisti tikslinę lentelę prieš diegdami replikavimo srautą.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Toliau pateikti nepalaikomi stulpeliai praleisti šaltinio apibrėžime: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Toliau pateikti nepalaikomi stulpeliai praleisti tiksliniame apibrėžime: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Toliau pateikti objektai nepalaikomi, nes jie pateikti sunaudoti: {0} {1} {0} {0} Norint naudoti lenteles replikavimo sraute, negali būti nustatytas semantinis naudojimas {2}Analitinė duomenų aibė{2} (lentelių parametruose).
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Tikslinio objekto negalima naudoti, nes jis pateiktas sunaudoti. {0} {0} Norint naudoti lentelę replikavimo sraute, negali būti nustatytas semantinis naudojimas {1}Analitinė duomenų aibė{1} (lentelės parametruose).
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Jau yra tikslinis objektas šiuo pavadinimu. Tačiau jo negalima naudoti, nes jis pateiktas sunaudoti. {0} {0} Norint naudoti lentelę replikavimo sraute, negali būti nustatytas semantinis naudojimas {1}Analitinė duomenų aibė{1} (lentelės parametruose).
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Objektas tokiu pavadinimu jau yra tiksle. {0} Tačiau šis objektas negali būti naudojamas kaip replikavimo srauto į vietinę saugyklą, nes ji nėra vietinė lentelė, tikslinis objektas.
#XMSG:
targetAutoRenameUpdated=Tikslinis stulpelis pervardytas.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Tikslinis stulpelis buvo pervardytas, kad būtų galima leisti replikavimus sistemoje „Google BigQuery“. Taip yra dėl vienos iš šių priežasčių:{0} {1}{2}rezervuoto stulpelio pavadinimas{3}{2}, nepalaikomi simboliai{3}{2}rezervuotas prefiksas{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Tikslinis stulpelis buvo pervardytas, kad būtų galima leisti replikavimus sistemoje „Confluent“. Taip yra dėl vienos iš šių priežasčių:{0} {1}{2}Rezervuotas stulpelio pavadinimas{3}{2}Nepalaikomi simboliai{3}{2}Rezervuotas prefiksas{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Tikslinis stulpelis buvo pervardytas, kad būtų galima leisti replikavimus į tikslinę lentelę. Taip yra dėl vienos iš šių priežasčių:{0} {1}{2}Nepalaikomi simboliai{3}{2}Rezervuotas prefiksas{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Tikslinis stulpelis buvo pervardytas, kad būtų galima leisti replikavimus į tikslinę lentelę. Taip yra dėl vienos iš šių priežasčių:{0} {1}{2}Rezervuotas stulpelio pavadinimas{3}{2}Nepalaikomi simboliai{3}{2}Rezervuotas prefiksas{3}{4}
#XMSG:
targetAutoDataType=Tikslinių duomenų tipas pakeistas.
#XMSG:
targetAutoDataTypeDesc=Tikslinių duomenų tipas pakeistas į {0}, nes šaltinio duomenų tipas nepalaikomas sistemoje „Google BigQuery“.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Tikslinių duomenų tipas pakeistas į {0}, nes šaltinio duomenų tipas tikslinio ryšio nepalaikomas.
#XMSG
projectionGBQUnableToCreateKey=Pirminiai raktai nebus kuriami.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=„Google BigQuery“ daugiausia palaikoma 16 pirminių raktų, bet šaltinio objekte jų yra daugiau. Nei vienas pirminis raktas nebus sukurtas tiksliniame objekte.
#XMSG
HDLFNoKeyError=Apibrėžkite vieną ar kelis stulpelius kaip pirminį raktą.
#XMSG
HDLFNoKeyErrorDescription=Norėdami replikuoti objektą, turite apibrėžti vieną ar kelis stulpelius kaip pirminį raktą.
#XMSG
HDLFNoKeyErrorExistingTarget=Apibrėžkite vieną ar kelis stulpelius kaip pirminį raktą.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Norint replikuoti duomenis į esamą tikslinį objektą, jame turi būti vienas ar keli stulpeliai, apibrėžti kaip pirminis raktas. {0} {0} Vieną ar kelis stulpelius galima apibrėžti kaip pirminį raktą keliais būdais: {0}{0}{1} Pakeiskite esamą tikslinį objektą naudodami vietinę lentelių rengyklę. Tuomet iš naujo įkelkite replikavimo srautą.{0}{0}{1} Pervardykite tikslinį objektą replikavimo sraute. Tuomet pradėjus vykdymą bus iš karto sukurtas naujas objektas. Baigę pervardyti galite apibrėžti vieną ar kelis stulpelius kaip pirminį raktą projekcijoje.{0}{0}{1} Susiekite objektą su kitu esamu tiksliniu objektu, kuriame vienas ar keli stulpeliai jau apibrėžti kaip pirminis raktas.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Pirminis raktas pakeistas.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=Tiksliniam objektui apibrėžėte kitus stulpelius kaip pirminį raktą nei šaltinio objektui. Įsitikinkite, kad šie stulpeliai unikaliai identifikuoja visas eilutes, kad vėliau replikuodami duomenis išvengtumėte galimo duomenų sugadinimo. {0} {0} Šaltinio objekte toliau pateikiami stulpeliai apibrėžti kaip pirminis raktas: {0} {1}
#XMSG
duplicateDPIDColumns=Pervardykite tikslinį stulpelį.
#XMSG
duplicateDPIDDColumnsDesc1=Šis tikslinio stulpelio pavadinimas rezervuotas techniniam stulpeliui. Norėdami įrašyti projekciją įveskite kitą pavadinimą.
#XMSG:
targetAutoRenameDPID=Tikslinis stulpelis pervardytas.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Tikslinis stulpelis buvo pervardytas, kad būtų galima leisti replikavimus iš ABAP šaltinio be raktų. Taip yra dėl vienos iš šių priežasčių:{0} {1}{2}Rezervuotas stulpelio pavadinimas{3}{2}Nepalaikomi simboliai{3}{2}Rezervuotas prefiksas{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle={0} tiksliniai parametrai
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle={0} šaltinio parametrai
#XBUT
connectionSettingSave=Įrašyti
#XBUT
connectionSettingCancel=Atšaukti
#XBUT: Button to keep the object level settings
txtKeep=Palikti
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Perrašyti
#XFLD
targetConnectionThreadlimit=Pradinės apkrovos (1–100) tikslinės gijos apribojimas
#XFLD
connectionThreadLimit=Pradinės apkrovos (1–100) šaltinio gijos apribojimą
#XFLD
maxConnection=Replikavimo gijos riba (1–100)
#XFLD
kafkaNumberOfPartitions=Skaidinių skaičius
#XFLD
kafkaReplicationFactor=Replikavimo faktorius
#XFLD
kafkaMessageEncoder=Pranešimo koduotojas
#XFLD
kafkaMessageCompression=Pranešimo glaudinimas
#XFLD
fileGroupDeltaFilesBy=Grupuoti delta pagal
#XFLD
fileFormat=Failo tipas
#XFLD
csvEncoding=CSV kodavimas
#XFLD
abapExitLbl=ABAP „Exit“
#XFLD
deltaPartition=Delta apkrovų (1–10) objekto gijų skaičių
#XFLD
clamping_Data=Duomenų trumpinimas nepavyko
#XFLD
fail_On_Incompatible=Nepavyko dėl nesuderinamų duomenų
#XFLD
maxPartitionInput=Maks. skaidinių skaičius
#XFLD
max_Partition=Nurodyti maks. skaidinių skaičių
#XFLD
include_SubFolder=įtraukti poaplankius
#XFLD
fileGlobalPattern=Visuotinis failo pavadinimo šablonas
#XFLD
fileCompression=Failo glaudinimas
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Failo skyriklis
#XFLD
fileIsHeaderIncluded=Failo antraštė
#XFLD
fileOrient=„Orient“
#XFLD
gbqWriteMode=Rašymo režimas
#XFLD
suppressDuplicate=Papildyti dublikatus
#XFLD
apacheSpark=Įgalinti „Apache Spark“ suderinamumą
#XFLD
clampingDatatypeCb=Užfiksuoti dešimtainių skilčių slankiojo kablelio duomenų tipus
#XFLD
overwriteDatasetSetting=Perrašyti tikslinius nustatymus objekto lygmeniu
#XFLD
overwriteSourceDatasetSetting=Perrašyti šaltinio nustatymus objekto lygmeniu
#XMSG
kafkaInvalidConnectionSetting=Įveskite skaičių nuo {0} iki {1}
#XMSG
MinReplicationThreadErrorMsg=Įveskite skaičių, didesnį nei {0}.
#XMSG
MaxReplicationThreadErrorMsg=Įveskite skaičių, mažesnį nei {0}.
#XMSG
DeltaThreadErrorMsg=Įveskite reikšmę nuo 1 iki 10.
#XMSG
MaxPartitionErrorMsg=Įveskite vertę tarp 1 <= x <= 2 147 483 647. Numatytoji vertė yra 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Įveskite skaičių nuo {0} iki {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Naudokite brokerio replikavimo veiksnį
#XFLD
serializationFormat=Keitimo į nuosekliąją formą formatas
#XFLD
compressionType=Glaudinimo tipas
#XFLD
schemaRegistry=Naudoti schemos registrą
#XFLD
subjectNameStrat=Dalyko pavadinimo strategija
#XFLD
compatibilityType=Suderinamumo tipas
#XFLD
confluentTopicName=Temos pavadinimas
#XFLD
confluentRecordName=Įrašo pavadinimas
#XFLD
confluentSubjectNamePreview=Dalyko pavadinimo peržiūra
#XMSG
serializationChangeToastMsgUpdated2=Keitimo į nuosekliąją formą formatas pakeistas į JSON kaip schemos registrą neįjungtas. Norėdami pakeisti keitimo į nuosekliąją formą formatą atgal į AVRO, pirmiausia turite įjungti schemos registrą.
#XBUT
confluentTopicNameInfo=Temos pavadinimas visada pagrįstas tiksliniu objekto pavadinimu. Galite pakeisti jį pervadindami tikslinį objektą.
#XMSG
emptyRecordNameValidationHeaderMsg=Įveskite įrašo pavadinimą.
#XMSG
emptyPartionHeader=Įveskite skaidinių skaičių.
#XMSG
invalidPartitionsHeader=Įveskite tinkamą skaidinių skaičių.
#XMSG
invalidpartitionsDesc=Įveskite skaičių nuo 1 iki 200 000.
#XMSG
emptyrFactorHeader=Įveskite replikavimo faktorių.
#XMSG
invalidrFactorHeader=Įveskite tinkamą replikavimo faktorių.
#XMSG
invalidrFactorDesc=Įveskite skaičių nuo 1 iki 32 767.
#XMSG
emptyRecordNameValidationDescMsg=Jei naudojamas keitimo į nuosekliąją formą formatas „AVRO“, palaikomi tik šie simboliai: {0}{1} A–Z{0}{1} a–z{0}{1} 0–9{0}{1} _(pabraukimo brūkšnys)
#XMSG
validRecordNameValidationHeaderMsg=Įveskite galiojantį įrašo pavadinimą.
#XMSG
validRecordNameValidationDescMsgUpdated=Naudojamas keitimo į nuosekliąją formą formatas „AVRO“, todėl įrašo pavadinimą turi sudaryti tik raidžių ar skaičių (A–Z, a–z, 0–9) ir pabraukimo brūkšnio (_) simboliai. Jis turi prasidėti raide arba pabraukimo brūkšniu.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=„Delta apkrovų objekto gijų skaičių“ galima nustatyti iš karto, kai tik vieno ar kelių objektų apkrovos tipas bus „Pradinis ir Delta“.
#XMSG
invalidTargetName=Netinkamas stulpelio pavadinimas
#XMSG
invalidTargetNameDesc=Tikslinį stulpelio pavadinimą turi sudaryti tik raidžių ar skaičių (A–Z, a–z, 0–9) ir pabraukimo brūkšnio (_) simboliai.
#XFLD
consumeOtherSchema=Naudoti kitas schemos versijas
#XFLD
ignoreSchemamissmatch=Ignoruoti schemos neatitikimą
#XFLD
confleuntDatatruncation=Duomenų trumpinimas nepavyko
#XFLD
isolationLevel=Izoliacijos lygis
#XFLD
confluentOffset=Pradžios taškas
#XFLD
signavioGroupDeltaFilesByText=Nėra
#XFLD
signavioFileFormatText=„Parquet“
#XFLD
signavioSparkCompatibilityParquetText=Ne
#XFLD
siganvioFileCompressionText=„Snappy“
#XFLD
siganvioSuppressDuplicatesText=Ne

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Ekstrapoliavimai
#XBUT
txtAdd=Pridėti
#XBUT
txtEdit=Redaguoti
#XMSG
transformationText=Pridėkite ekstrapoliavimą, kad nustatytumėte filtrą arba susiejimą.
#XMSG
primaryKeyRequiredText=Pasirinkite pirminį raktą su konfigūravimo schema.
#XFLD
lblSettings=Parametrai
#XFLD
lblTargetSetting={0}: tiksliniai parametrai
#XMSG
@csvRF=Pasirinkite failą, kuriame yra schemos apibrėžimas, kurį norite pritaikyti visiems aplanke esantiems failams.
#XFLD
lblSourceColumns=Šaltinio stulpeliai
#XFLD
lblJsonStructure=JSON struktūra
#XFLD
lblSourceSetting={0}: šaltinio parametrai
#XFLD
lblSourceSchemaSetting={0}: šaltinio schemos parametrai
#XBUT
messageSettings=Pranešimo parametrai
#XFLD
lblPropertyTitle1=Objekto ypatybės
#XFLD
lblRFPropertyTitle=Replikavimo srauto ypatybės
#XMSG
noDataTxt=Nėra rodytinų stulpelių.
#XMSG
noTargetObjectText=Nepasirinktas tikslinis objektas.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Tiksliniai stulpeliai
#XMSG
searchColumns=Ieškos stulpeliai
#XTOL
cdcColumnTooltip=Delta įvedimo stulpelis
#XMSG
sourceNonDeltaSupportErrorUpdated=Šaltinio objektas nepalaiko delta įvedimo.
#XMSG
targetCDCColumnAdded=Delta įvedimui buvo pridėti 2 tiksliniai stulpeliai.
#XMSG
deltaPartitionEnable=Delta įkėlimų objektų gijos apribojimas pridėtas prie šaltinio parametrų.
#XMSG
attributeMappingRemovalTxt=Šalinami negaliojantys susiejimai, nepalaikomi naujam tiksliniam objektui.
#XMSG
targetCDCColumnRemoved=Buvo pašalinti 2 tiksliniai stulpeliai, naudojami delta įvedimui.
#XMSG
replicationLoadTypeChanged=Apkrovos tipas pakeistas į „Pradinis ir delta“.
#XMSG
sourceHDLFLoadTypeError=Pakeisti įkėlimo tipą į „Pradinis ir delta“.
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Norėdami replikuoti objektą iš šaltinio ryšio su ryšio tipu „SAP HANA Cloud“, duomenų telkinio failai į „SAP Datasphere“, turite naudoti įkėlimo tipą „pradinis ir delta“.
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Įjungti delta įvedimą.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Norėdami replikuoti objektą iš šaltinio ryšio su ryšio tipu „SAP HANA Cloud“, duomenų telkinio failai į „SAP Datasphere“, turite įjungti delta įvedimą.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Keisti tikslinį objektą.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Tikslinio objekto naudoti negalima, nes delta įvedimas išjungtas. Galite pervadinti tikslinį objektą (tai leidžia sukurti naują objektą su delta įvedimu) arba susieti jį su esamu objektu su įjungtu delta įvedimu.
#XMSG
deltaPartitionError=Įveskite leistiną delta apkrovų objekto gijų skaičių.
#XMSG
deltaPartitionErrorDescription=Įveskite reikšmę nuo 1 iki 10.
#XMSG
deltaPartitionEmptyError=Įveskite delta apkrovų objekto gijų skaičių.
#XFLD
@lblColumnDescription=Aprašas
#XMSG
@lblColumnDescriptionText1=Techniniais tikslais apdorojami pasikartojantys įrašai, kuriuos lėmė problemos replikuojant ABAP pagrįstus šaltinio objektus be pirminio rakto.
#XFLD
storageType=Saugykla
#XFLD
skipUnmappedColLbl=Praleisti nesusietus stulpelius
#XFLD
abapContentTypeLbl=Turinio tipas
#XFLD
autoMergeForTargetLbl=Sulieti duomenis automatiškai
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Bendra
#XFLD
lblBusinessName=Verslo pavadinimas
#XFLD
lblTechnicalName=Techninis pavadinimas
#XFLD
lblPackage=Paketas
#XFLD
statusPanel=Vykdymo būsena
#XBTN: Schedule dropdown menu
SCHEDULE=Tvarkaraštis
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Redaguoti tvarkaraštį
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Naikinti tvarkaraštį
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Kurti tvarkaraštį
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Nepavyko tvarkaraščio patvirtinimo patikra
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Tvarkaraščio sukurti negalima, nes šiuo metu diegiamas replikavimo srautas.{0}Palaukite, kol replikavimo srautas bus įdiegtas.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Replikavimo srautams, kuriuose yra objektų su įkėlimo tipu „Pradinis ir delta“, tvarkaraščio sukurti negalima.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Replikavimo srautams, kuriuose yra objektų su įkėlimo tipu „Pradinis“ ir „Delta“ / „Tik delta“, tvarkaraščio sukurti negalima.
#XFLD : Scheduled popover
SCHEDULED=Suplanuota
#XFLD
CREATE_REPLICATION_TEXT=Kurti replikavimo srautą
#XFLD
EDIT_REPLICATION_TEXT=Redaguoti replikavimo srautą
#XFLD
DELETE_REPLICATION_TEXT=Naikinti replikavimo srautą
#XFLD
REFRESH_FREQUENCY=Dažnumas
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Šio replikavimo srauto įdiegti negalima, nes esamas tvarkaraštis{0} dar nepalaiko įkėlimo tipo „Pradinis ir delta“.{0}{0}Norėdami įdiegti replikavimo srautą, turite nustatyti visų objektų{0} įkėlimo tipus kaip „Tik pradinis“. Arba galite panaikinti tvarkaraštį, įdiegti {0}replikavimo srautą, ir paleisti naują vykdymą. Tuomet jis vyks nuolat {0}ir palaikys objektus, kurių įkėlimo tipas „Pradinis ir delta“.
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Šio replikavimo srauto įdiegti negalima, nes esamas tvarkaraštis{0} dar nepalaiko įkėlimo tipo „Pradinis“ ir „Delta“ / „Tik delta“.{0}{0}Norėdami įdiegti replikavimo srautą, turite nustatyti visų objektų įkėlimo tipus{0} kaip „Tik pradinis“. Arba galite panaikinti tvarkaraštį, įdiegti {0}replikavimo srautą, ir paleisti naują vykdymą. Tuomet jis vyks nuolat {0}ir palaikys objektus, kurių įkėlimo tipas „Pradinis“ ir „Delta“ / „Tik delta“.
#XMSG
SCHEDULE_EXCEPTION=Nepavyko gauti tvarkaraščio išsamios informacijos
#XFLD: Label for frequency column
everyLabel=Kas
#XFLD: Plural Recurrence text for Hour
hoursLabel=Valandos
#XFLD: Plural Recurrence text for Day
daysLabel=Dienos
#XFLD: Plural Recurrence text for Month
monthsLabel=Mėnesiai
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutės
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Nepavyko gauti informacijos apie planavimo galimybę.
#XFLD :Paused field
PAUSED=Pristabdyta
#XMSG
navToMonitoring=Atidaryti srautų stebėjimo priemonėje
#XFLD
statusLbl=Būsena
#XFLD
lblLastRunExecuted=Paskutinio vykdymo pradžia
#XFLD
lblLastExecuted=Paskutinis vykdymas
#XFLD: Status text for Completed
statusCompleted=Užbaigta
#XFLD: Status text for Running
statusRunning=Vykdoma
#XFLD: Status text for Failed
statusFailed=Nepavyko
#XFLD: Status text for Stopped
statusStopped=Sustabdyta
#XFLD: Status text for Stopping
statusStopping=Stabdoma
#XFLD: Status text for Active
statusActive=Aktyvus
#XFLD: Status text for Paused
statusPaused=Pristabdyta
#XFLD: Status text for not executed
lblNotExecuted=Neįvykdyta
#XFLD
messagesSettings=Pranešimų parametrai
#XTOL
@validateModel=Tikrinimo pranešimai
#XTOL
@hierarchy=Hierarchija
#XTOL
@columnCount=Stulpelių skaičius
#XMSG
VAL_PACKAGE_CHANGED=Šį objektą priskyrėte paketui „{1}“. Spustelėkite „Įrašyti“, norėdami patvirtinti ir tikrinti šį keitimą. Atkreipkite dėmesį, kad įrašius šioje rengyklėje negalima anuliuoti priskyrimo paketui.
#XMSG
MISSING_DEPENDENCY=Objekto „{0}“ priklausomybių negalima išspręsti pakete „{1}“.
#XFLD
deltaLoadInterval=Delta įkėlimo intervalas
#XFLD
lblHour=Valandos (0–24)
#XFLD
lblMinutes=Minutės (0–59)
#XMSG
maxHourOrMinErr=Įveskite reikšmę nuo 0 iki {0}
#XMSG
maxDeltaInterval=Maksimali duomenų įkėlimo intervalo reikšmė yra 24 valandos.{0}Atitinkamai pakeiskite minučių arba valandų reikšmę.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Tikslinio konteinerio kelias
#XFLD
confluentSubjectName=Subjekto pavadinimas
#XFLD
confluentSchemaVersion=Schemos versija
#XFLD
confluentIncludeTechKeyUpdated=Įtraukti techninį raktą
#XFLD
confluentOmitNonExpandedArrays=Neįtraukti neišplėstų masyvų
#XFLD
confluentExpandArrayOrMap=Išplėsti masyvą arba susiejimą
#XCOL
confluentOperationMapping=Operacijų susiejimas
#XCOL
confluentOpCode=Operacijos kodas
#XFLD
confluentInsertOpCode=Įterpti
#XFLD
confluentUpdateOpCode=Naujinti
#XFLD
confluentDeleteOpCode=Naikinti
#XFLD
expandArrayOrMapNotSelectedTxt=Nepasirinkta
#XFLD
confluentSwitchTxtYes=Taip
#XFLD
confluentSwitchTxtNo=Ne
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Klaida
#XTIT
executeWarning=Įspėjimas
#XMSG
executeunsavederror=Įrašykite replikavimą prieš jį vykdydami.
#XMSG
executemodifiederror=Replikavimo sraute yra neįrašytų pakeitimų. Įrašykite replikavimo srautą.
#XMSG
executeundeployederror=Turite įdiegti savo replikavimo srautą, kad galėtumėte jį vykdyti.
#XMSG
executedeployingerror=Palaukite, kol pasibaigs diegimas.
#XMSG
msgRunStarted=Vykdymas pradėtas
#XMSG
msgExecuteFail=Nepavyko įvykdyti replikavimo srauto.
#XMSG
titleExecuteBusy=Palaukite.
#XMSG
msgExecuteBusy=Ruošiame jūsų duomenis replikavimo srautui vykdyti.
#XTIT
executeConfirmDialog=Įspėjimas
#XMSG
msgExecuteWithValidations=Replikavimo sraute yra tikrinimo klaidų. Gali nepavykti vykdyti replikavimo srautą.
#XMSG
msgRunDeployedVersion=Yra diegiamų keitimų. Bus vykdoma paskutinė įdiegta replikavimo srauto versija. Ar norite tęsti?
#XBUT
btnExecuteAnyway=Vis tiek vykdyti
#XBUT
btnExecuteClose=Uždaryti
#XBUT
loaderClose=Uždaryti
#XTIT
loaderTitle=Įkeliama
#XMSG
loaderText=Iškviečiama informacija iš serverio
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Negalima pradėti replikavimo srauto į šį ne SAP tikslinį ryšį,
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=nes šį mėnesį nėra prieinamo siunčiamo kiekio.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Administratorius gali padidinti šio kliento „Premium“ siunčiamus blokus,
#XMSG
premiumOutBoundRFAdminErrMsgPart2=kad šį mėnesį būtų prieinamas siunčiamas kiekis.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Klaida
#XTIT
deployInfo=Informacija
#XMSG
deployCheckFailException=Diegiant įvyko klaida
#XMSG
deployGBQFFDisabled=Šiuo metu negalima diegti replikavimo srautų su tiksliniu ryšiu į „Google BigQuery“, nes atliekame šios funkcijos techninę priežiūrą.
#XMSG
deployKAFKAFFDisabled=Šiuo metu negalima diegti replikavimo srautų su tiksliniu ryšiu į „Apache Kafka“, nes atliekame šios funkcijos techninę priežiūrą.
#XMSG
deployConfluentDisabled=Šiuo metu negalima diegti replikavimo srautų su tiksliniu ryšiu į „Confluent Kafka“, nes atliekame šios funkcijos techninę priežiūrą.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Šių tikslinių objektų delta fiksavimo lentelių pavadinimus jau naudoja kitos saugyklos lentelės: {0} Turite pervardyti šiuos tikslinius objektus, kad įsitikintumėte, jog susiję delta fiksavimo lentelių pavadinimai yra unikalūs, kad galėtumėte diegti replikacijos srautą.
#XMSG
deployDWCSourceFFDisabled=Šiuo metu negalima įdiegti replikavimo srautų, kurių šaltinis yra „SAP Datasphere“, nes atliekame šios funkcijos priežiūrą.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Šiuo metu negalima įdiegti replikavimo srautų, kuriuose vietinės lentelės su delta galimybe naudojamos kaip šaltinio objektai, nes atliekame šios funkcijos priežiūrą.
#XMSG
deployHDLFSourceFFDisabled=Šiuo metu negalima įdiegti replikavimo srautų su „SAP HANA Cloud“, duomenų ežero failų ryšio tipo šaltinio ryšiais, nes atliekame techninę priežiūrą.
#XMSG
deployObjectStoreAsSourceFFDisabled=Šiuo metu negalima diegti replikavimo srautų, kuriems debesies saugyklos teikėjai nustatyti kaip šaltinis.
#XMSG
deployConfluentSourceFFDisabled=Šiuo metu negalima įdiegti replikavimo srautų, kurių šaltinis yra „Confluent Kafka“, nes atliekame šios funkcijos priežiūrą.
#XMSG
deployMaxDWCNewTableCrossed=Esant dideliems replikavimo srautams, neįmanoma jų „išsaugoti ir įdiegti“ vienu kartu. Pirmiausia išsaugokite replikavimo srautą ir tada įdiekite.
#XMSG
deployInProgressInfo=Diegimas jau vykdomas.
#XMSG
deploySourceObjectInUse=Šaltinio objektai {0} jau naudojami replikavimo srautuose {1}.
#XMSG
deployTargetSourceObjectInUse=Šaltinio objektai {0} jau naudojami replikavimo srautuose {1}. Tiksliniai objektai {2} jau naudojami replikavimo srautuose {3}.
#XMSG
deployReplicationFlowCheckError=Tikrinant replikavimo srautą {0} įvyko klaida
#XMSG
preDeployTargetObjectInUse=Tiksliniai objektai {0} jau naudojami replikavimo srautuose {1} ir negalite turėti to paties tikslinio objekto dviejuose skirtinguose replikavimo srautuose. Pasirinkite kitą tikslinį objektą ir bandykite dar kartą.
#XMSG
runInProgressInfo=Jau vykdomas replikavimo srautas.
#XMSG
deploySignavioTargetFFDisabled=Šiuo metu negalima įdiegti replikavimo srautų, kurių tikslas yra „SAP Signavio“, nes atliekame šios funkcijos priežiūrą.
#XMSG
deployHanaViewAsSourceFFDisabled=Šiuo metu negalima įdiegti replikavimo srautų, kuriuose pasirinktam šaltinio ryšiui kaip šaltinio objektai naudojami rakursai. Bandykite dar kartą vėliau.
#XMSG
deployMsOneLakeTargetFFDisabled=Šiuo metu negalima įdiegti replikavimo srautų, kurių tikslas yra „MS OneLake“, nes atliekame šios funkcijos priežiūrą.
#XMSG
deploySFTPTargetFFDisabled=Šiuo metu negalima įdiegti replikavimo srautų, kurių tikslas yra SFTP, nes atliekame šios funkcijos priežiūrą.
#XMSG
deploySFTPSourceFFDisabled=Šiuo metu negalima įdiegti replikavimo srautų, kurių šaltinis yra SFTP, nes atliekame šios funkcijos priežiūrą.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Techninis pavadinimas
#XFLD
businessNameInRenameTarget=Verslo pavadinimas
#XTOL
renametargetDialogTitle=Pervardyti tikslinį objektą
#XBUT
targetRenameButton=Pervardyti
#XBUT
targetRenameCancel=Atšaukti
#XMSG
mandatoryTargetName=Turite įvesti pavadinimą.
#XMSG
dwcSpecialChar=_(pabraukimo brūkšnys) yra vienintelis leidžiamas specialusis simbolis.
#XMSG
dwcWithDot=Tikslinės lentelės pavadinimą gali sudaryti lotyniškos raidės, skaičiai, pabraukimo brūkšniai (_) ir taškai (.). Pirmasis simbolis turi būti raidė, skaičius arba pabraukimo brūkšnys (ne taškas).
#XMSG
nonDwcSpecialChar=Leidžiami specialieji simboliai yra _(pabraukimo brūkšnys) -(brūkšnelis) .(taškas)
#XMSG
firstUnderscorePattern=Pavadinimas negali prasidėti _ (pabraukimo brūkšniu).

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: peržiūrėti SQL kūrimo lentelės išrašą
#XMSG
sqlDialogMaxPKWarning=„Google BigQuery“ palaiko ne daugiau kaip 16 pirminių raktų, o šaltinio objekto skaičius yra didesnis. Todėl šiame išraše pirminiai raktai neapibrėžti.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Viename ar keliuose šaltinio stulpeliuose yra duomenų tipų, kurių negalima apibrėžti kaip pirminių „Google BigQuery“ raktų. Todėl šiuo atveju pirminiai raktai neapibrėžti. „Google BigQuery“ pirminį raktą gali turėti tik šie duomenų tipai: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Kopijuoti ir uždaryti
#XBUT
closeDDL=Uždaryti
#XMSG
copiedToClipboard=Nukopijuota į mainų sritį


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Objektas „{0}“ negali būti užduočių grandinės dalis, nes jis neturi pabaigos (nes jis apima objektus, kurių įkėlimo tipas „Pradinis“ ir „Delta“ / „Tik delta“).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Objektas „{0}“ negali būti užduočių grandinės dalis, nes jis neturi pabaigos (nes jis apima objektus, kurių įkėlimo tipas „Pradinis“ ir „Delta“).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objekto „{0}“ negalima pridėti prie užduočių grandinės.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Yra neišsaugotų tikslinių objektų. Išsaugokite dar kartą.{0}{0} Šios funkcijos veikimas pasikeitė: anksčiau tiksliniai objektai buvo kuriami tikslinėje aplinkoje tik tada, kai buvo įdiegtas replikavimo srautas.{0} Dabar objektai jau sukurti, kai įrašomas replikavimo srautas. Jūsų replikavimo srautas buvo sukurtas prieš šį pakeitimą ir jame yra naujų objektų.{0} Prieš diegdami replikavimo srautą turite dar kartą išsaugoti, kad nauji objektai būtų tinkamai įtraukti.
#XMSG
confirmChangeContentTypeMessage=Ketinate pakeisti turinio tipą. Tai padarius, visos esamos projekcijos bus panaikintos.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Subjekto pavadinimas
#XFLD
schemaDialogVersionName=Schemos versija
#XFLD
includeTechKey=Įtraukti techninį raktą
#XFLD
segementButtonFlat=Fiksuotas
#XFLD
segementButtonNested=Įdėtasis
#XMSG
subjectNamePlaceholder=Ieškoti subjekto pavadinimo

#XMSG
@EmailNotificationSuccess=Pranešimų el. paštu vykdymo laiko konfigūracija įrašyta.

#XFLD
@RuntimeEmailNotification=Pranešimo el. paštu vykdymo laikas

#XBTN
@TXT_SAVE=Įrašyti


