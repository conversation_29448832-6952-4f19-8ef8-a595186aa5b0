#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Поток на репликация

#XFLD: Edit Schema button text
editSchema=Редактиране на схема

#XTIT : Properties heading
configSchema=Конфигуриране на схема

#XFLD: save changed button text
applyChanges=Прилагане на промените


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Избор на източник на връзка
#XFLD
sourceContainernEmptyText=Избор на контейнер
#XFLD
targetConnectionEmptyText=Избор на целева връзка
#XFLD
targetContainernEmptyText=Избор на контейнер
#XFLD
sourceSelectObjectText=Избор на изходен обект
#XFLD
sourceObjectCount=Изходен обект ({0})
#XFLD
targetObjectText=Целеви обекти
#XFLD
confluentBrowseContext=Избор на контекст
#XBUT
@retry=Повторен опит
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Извършва се ъпгрейд на наемател.

#XTOL
browseSourceConnection=Браузване за изходна връзка
#XTOL
browseTargetConnection=Браузване за целева връзка
#XTOL
browseSourceContainer=Браузване за изходен контейнер
#XTOL
browseAndAddSourceDataset=Добавяне на изходни обекти
#XTOL
browseTargetContainer=Браузване за целеви контейнер
#XTOL
browseTargetSetting=Браузване за целеви настройки
#XTOL
browseSourceSetting=Браузване за изходни настройки
#XTOL
sourceDatasetInfo=Информация
#XTOL
sourceDatasetRemove=Премахване
#XTOL
mappingCount=Тук е представен общият брой мапирания/изрази, които не са базирани на име.
#XTOL
filterCount=Тук е представен общият брой условия на филтър.
#XTOL
loading=Зареждане...
#XCOL
deltaCapture=Делта заснемане
#XCOL
deltaCaptureTableName=Таблица с делта заснемане
#XCOL
loadType=Зареждане на вид
#XCOL
deleteAllBeforeLoading=Изтриване на всички, преди зареждане
#XCOL
transformationsTab=Проекции
#XCOL
settingsTab=Настройки

#XBUT
renameTargetObjectBtn=Преименуване на целеви обект
#XBUT
mapToExistingTargetObjectBtn=Мапиране към съществуващ целеви обект
#XBUT
changeContainerPathBtn=Промяна на пътя на контейнера
#XBUT
viewSQLDDLUpdated=Преглед на SQL инструкция Create Table
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Изходният обект не поддържа делта заснемане, но избраният целеви обект има активирана опция за делта заснемане.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Целевият обект не може да се използва, защото е активирано делта записването,{0}а изходният обект не поддържа такова.{1}Може да изберете друг целеви обект, който не поддържа делта записване.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Вече има целеви обект с това име. Той обаче не може да се използва,{0}защото е активирано делта записването, а изходният обект не{0}поддържа такова.{1}Може да въведете името на съществуващ целеви обект, който не{0}поддържа делта записване, или да въведете неизползвано име.
#XBUT
copySQLDDLUpdated=Копиране на SQL инструкция Create Table
#XMSG
targetObjExistingNoCDCColumnUpdated=Съществуващите таблици в Google BigQuery трябва да включват следните колони за заснемане на промени на данни (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Следните изходни обекти не се поддържат, защото нямат първичен ключ или използват връзка, която не отговаря на условията за извличане на първичния ключ:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=На SAP KBA 3531135 ще намерите възможни решения.
#XLST: load type list values
initial=Само начални
@emailUpdateError=Грешка при актуализиране на списъка за имейл известия

#XLST
initialDelta=Начално и делта

#XLST
deltaOnly=Само делта
#XMSG
confluentDeltaLoadTypeInfo=За източник Confluent Kafka се поддържат само начално и Delta зареждане.
#XMSG
confirmRemoveReplicationObject=Потвърждавате ли, че искате да изтриете тиражирането?
#XMSG
confirmRemoveReplicationTaskPrompt=Това действие ще изтрие съществуващите репликации. Желаете ли да продължите?
#XMSG
confirmTargetConnectionChangePrompt=Това действие ще зададе повторно целевата връзка и целевия контейнер и ще изтрие всички целеви обекти. Желаете ли да продължите?
#XMSG
confirmTargetContainerChangePrompt=Това действие ще зададе повторно целевия контейнер и ще изтрие всички съществуващи целеви обекти. Желаете ли да продължите?
#XMSG
confirmRemoveTransformObject=Потвърждавате ли, че искате да изтриете проекцията {0}?
#XMSG
ErrorMsgContainerChange=Възникна грешка при промяна на пътя на контейнера.
#XMSG
infoForUnsupportedDatasetNoKeys=Следните изходни обекти не се поддържат, тъй като нямат първичен ключ:
#XMSG
infoForUnsupportedDatasetView=Следните изходни обекти от вида „Изгледи“ не се поддържат:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Следният изходен обект не се поддържа, тъй като е SQL изглед, съдържащ входни параметри:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Следните изходни обекти не се поддържат, тъй като за тях извличането е дезактивирано:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=За връзките за Confluent единствените разрешени формати за сериализация са AVRO и JSON. Следните обекти не се поддържат, защото използват различен формат за сериализация:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Невъзможно извличане на схемата за следните обекти. Моля, изберете подходящ контекст или проверете конфигурацията на регистъра на схеми.
#XTOL: warning dialog header on deleting replication task
deleteHeader=Изтриване
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Настройката „Изтриване на всичко преди зареждане“ не се поддържа за Google BigQuery.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Настройката „Изтриване на всичко преди“ изтрива и създава повторно обекта (темата) преди всяка репликация. Тя също изтрива всички присъединени съобщения.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Настройката „Изтриване на всичко преди“ не се поддържа за този вид цел.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Техническо име
#XCOL
connBusinessName=Бизнес наименование
#XCOL
connDescriptionName=Описание
#XCOL
connType=Вид
#XMSG
connTblNoDataFoundtxt=Не са намерени връзки
#XMSG
connectionError=Възникна грешка при вземане на връзки.
#XMSG
connectionCombinationUnsupportedErrorTitle=Комбинацията от връзки не се поддържа
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=За момента не поддържаме репликация от {0} в {1}.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Комбинацията от видовете връзки не се поддържа.
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Репликацията от връзка от вида файлове на SAP HANA Cloud, Data Lake до {0} не се поддържа. Можете да репликирате само до SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Избор
#XBUT
containerCancelBtn=Отказ
#XTOL
containerSelectTooltip=Избор
#XTOL
containerCancelTooltip=Отказ
#XMSG
containerContainerPathPlcHold=Път на контейнер
#XFLD
containerContainertxt=Контейнер
#XFLD
confluentContainerContainertxt=Контекст
#XMSG
infoMessageForSLTSelection=Разрешен е само /SLT/ИД на масов пренос като контейнер. Изберете ИД на масов пренос под SLT (ако е наличен) и кликнете върху „Изпращане“.
#XMSG
msgFetchContainerFail=Възникна грешка при вземане на данни за контейнер.
#XMSG
infoMessageForSLTHidden=Тази връзка не поддържа SLT папки и по тази причина те не присъстват в списъка по-долу.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Изберете контейнер, който съдържа подпапки.
#XMSG
sftpIncludeSubFolderText=Невярно
#XMSG
sftpIncludeSubFolderTextNew=Не

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Още няма мапиране на филтър)
#XMSG
failToFetchRemoteMetadata=Възникна грешка при вземане на метаданни.
#XMSG
failToFetchData=Възникна грешка при вземане на съществуващата цел.
#XCOL
@loadType=Вид на зареждане
#XCOL
@deleteAllBeforeLoading=Изтриване на всички, преди зареждане

#XMSG
@loading=Зареждане...
#XFLD
@selectSourceObjects=Избор на изходни обекти
#XMSG
@exceedLimit=Не може да импортирате повече от {0} обекта наведнъж. Отменете избора за поне {1} обекта.
#XFLD
@objects=Обекти
#XBUT
@ok=OK
#XBUT
@cancel=Отказ
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Напред
#XBUT
btnAddSelection=Добавяне на избор
#XTOL
@remoteFromSelection=Премахване от избраните
#XMSG
@searchInForSearchField=Търсене в {0}

#XCOL
@name=Техническо име
#XCOL
@type=Вид
#XCOL
@location=Местоположение
#XCOL
@label=Бизнес наименование
#XCOL
@status=Статус

#XFLD
@searchIn=Търсене в:
#XBUT
@available=Наличен
#XBUT
@selection=Избор

#XFLD
@noSourceSubFolder=Таблици и изгледи
#XMSG
@alreadyAdded=Вече присъства в диаграмата
#XMSG
@askForFilter=Има повече от {0} позиции. Моля, въведете низ за филтриране, за да ограничите броя на позициите.
#XFLD: success label
lblSuccess=Успех
#XFLD: ready label
lblReady=Готово
#XFLD: failure label
lblFailed=Неуспешно
#XFLD: fetching status label
lblFetchingDetail=Извличане на подробни данни

#XMSG Place holder text for tree filter control
filterPlaceHolder=Въведете текст, за да филтрирате обектите от най-високо ниво
#XMSG Place holder text for server search control
serverSearchPlaceholder=Въведете и натиснете Enter за търсене
#XMSG
@deployObjects=Импортиране на {0} обекти...
#XMSG
@deployObjectsStatus=Брой обекти, които са импортирани: {0}. Брой обекти, които не са импортирани: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Неуспешно отваряне на локален браузър на репозитар.
#XMSG
@openRemoteSourceBrowserError=Неуспешно вземане на изходни обекти.
#XMSG
@openRemoteTargetBrowserError=Неуспешно вземане на целеви обекти.
#XMSG
@validatingTargetsError=Възникна грешк апри валидиране на цели.
#XMSG
@waitingToImport=Готовност за импортиране

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Максималният брой символи е надвишен. Изберете най-много 500 обекта за един поток на репликация.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Техническо име
#XFLD
sourceObjectBusinessName=Бизнес наименование
#XFLD
sourceNoColumns=Брой колони
#XFLD
containerLbl=Контейнер

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Трябва да изберете изходна връзка за потока на репликация.
#XMSG
validationSourceContainerNonExist=Трябва да изберете контейнер за изходната връзка.
#XMSG
validationTargetNonExist=Трябва да изберете целева връзка за потока на репликация.
#XMSG
validationTargetContainerNonExist=Трябва да изберете контейнер за целевата връзка.
#XMSG
validationTruncateDisabledForObjectTitle=Репликация в съхранение в обекти.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Репликацията в облачно съхранение е възможна само ако опцията „Изтриване на всичко преди зареждане“ е зададена или целевият обект не съществува в целта.{0}{0} За да активирате репликацията за обекти, за които не е зададена опцията „Изтриване на всичко преди зареждане“, преди да изпълните потока на репликация, се уверете, че целевият обект не съществува в системата.
#XMSG
validationTaskNonExist=Трябва да имате поне една репликация в потока на репликация.
#XMSG
validationTaskTargetMissing=Трябва да имате цел за репликацията с източника: {0}
#XMSG
validationTaskTargetIsSAC=Избраната цел е артефакт на SAC: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Избраната цел не е поддържана локална таблица: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Вече има обект с това име в целта. Но този обект не може да бъде използван като целеви обект за потока на репликация до локалното хранилище, тъй като не е локална таблица.
#XMSG
validateSourceTargetSystemDifference=Трябва да изберете различни комбинации от изходна и целева връзка и контейнер за потока на репликация.
#XMSG
validateDuplicateSources=една или повече репликации имат дублирани имена на изходни обекти: {0}.
#XMSG
validateDuplicateTargets=една или повече репликации имат дублирани имена на целеви обекти: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=Изходният обект {0} не поддържа делта заснемане, докато целевият обект {1} поддържа. Трябва да премахнете репликацията.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Трябва да изберете вид зареждане „Начално и делта“ за репликацията с име на целеви обект {0}.
#XMSG
validationAutoRenameTarget=Целевите колони са преименувани.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Добавена е автоматична проекция и следните целеви колони са преименувани, за да се позволи репликация в целта:{1}{1} {0} {1}{1}Това се дължи на една от следните причини:{1}{1}{2} Неподдържани символи{1}{2} Запазен префикс
#XMSG
validationAutoRenameTargetDescriptionUpdated=Добавена е автоматична проекция и следните целеви колони са преименувани, за да се позволи репликация към Google BigQuery:{1}{1} {0} {1}{1}Това се дължи на една от следните причини:{1}{1}{2} Запазено име на колона{1}{2} Неподдържани символи{1}{2} Запазен префикс
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Добавена е автоматична проекция и следните целеви колони са преименувани, за да се позволи репликация към Confluent:{1}{1} {0} {1}{1}Това се дължи на една от следните причини:{1}{1}{2} Запазено име на колона{1}{2} Неподдържани символи{1}{2} Запазен префикс
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Добавена е автоматична проекция и следните целеви колони са преименувани, за да се позволи репликация към целта:{1}{1} {0} {1}{1}Това се дължи на една от следните причини:{1}{1}{2} Запазено име на колона{1}{2} Неподдържани символи{1}{2} Запазен префикс
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Целевият обект е преименуван.
#XMSG
autoRenameInfoDesc=Целевият обект е преименуван, защото е съдържал неподдържани символи. Поддържат се само следните символи: {0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(точка){0}{1}_(долна черта){0}{1}-(тире)
#XMSG
validationAutoTargetTypeConversion=Видовете целеви данни са променени.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Видовете целеви данни са променени за следните целеви колони, защото в Google BigQuery видовете изходни данни не се поддържат:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Видовете целеви данни са променени за следните целеви колони, защото видовете изходни данни не се поддържат в целевата връзка:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Съкратете имената на целевите колони.
#XMSG
validationMaxCharLengthGBQTargetDescription=Имената на колоните в Google BigQuery, могат да съдържат максимум 300 символа. Използвайте проекция, за да съкратите следните имена на целевите колони:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Няма да се създадат първични ключове.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=В Google BigQuery се поддържат максимум 16 първични ключа, а в изходния обект има повече. Нито един от първичните ключове няма да бъде създаден в целевия обект.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Видът данни на поне една от колоните на източника не може да бъде дефиниран като първичен ключ в Google BigQuery. В целевия обект няма да бъдат създадени никакви първични ключове.{0}{0}По-долу са изброени целевите видове данни, съвместими с Google BigQuery, за които може да бъде генериран първичен ключ:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Дефинирайте поне една колона като първичен ключ.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Трябва да дефинирате поне една колона като първичен ключ. Използвайте диалоговия прозорец на изходната схема за целта.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Дефинирайте поне една колона като първичен ключ.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Трябва да дефинирате поне една колона като първичен ключ, съответстваща на ограниченията за първичен ключ за изходния обект. За целта отидете в „Конфигуриране на схема“ в свойствата на изходния обект.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Въведете валидна максимална стойност на дяла.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Максималната стойност на дяла трябва да е ≥ 1 и ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Дефинирайте поне една колона като първичен ключ.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=За да направите репликация на обект, трябва да дефинирате поне една от целевите колони като такава за първичен ключ. За целта използвайте проекция.
#XMSG
validateHDLFNoPKExistingDatasetError=Дефинирайте поне една колона като първичен ключ.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=За да направите репликация на данни в съществуващ целеви обект, той трябва да има поне една дефинирана колона за първичен ключ. {0} За целта разполагате със следите опции: {0} {1} Променете целевия обект чрез редактора на локални таблици. Щом приключите, презаредете потока за репликация.{0}{1} Преименувайте целевия обект в потока за репликация. Това ще доведе до създаването на нов обект в момента на стартиране на изпълнението. След преименуването може в проекция да посочите една или няколко колони за първичен ключ.{0}{1} Мапирайте обекта към друг съществуващ целеви обект, където вече има дефинирани колони за първичен ключ.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Избраната цел вече съществува в хранилището: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Имената на таблиците за делта заснемане вече се използват от други таблици в хранилището: {0}. Трябва да преименувате тези обекти, за да гарантирате, че свързаните с тях имена на таблици за делта заснемане са уникални, преди да запазите потока на репликацията.
#XMSG
validateConfluentEmptySchema=Определяне на схема
#XMSG
validateConfluentEmptySchemaDescUpdated=Изходната таблица няма схема. Изберете „Конфигуриране на схема“, за да дефинирате такава.
#XMSG
validationCSVEncoding=Невалидно CSV кодиране
#XMSG
validationCSVEncodingDescription=CSV кодирането на задачата е невалидно.
#XMSG
validateConfluentEmptySchema=Изберете съвместим вид целеви данни
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Изберете съвместим вид целеви данни
#XMSG
globalValidateTargetDataTypeDesc=Възникна грешка, свързана с мапирането на колони. Отидете в „Проекция“ и се уверете, че всички изходни колони са мапирани с уникална колона и с такава със съвместим вид данни, както и че всички дефинирани изрази са валидни.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Дублирани имена на колони.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Не се поддържат дублирани имена на колони. Използвайте диалоговия прозорец на проекцията, за да ги коригирате. Следните целеви обекти имат дублирани имена на колони: {0}
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Дублирани имена на колони.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Не се поддържат дублирани имена на колони. Следните целеви обекти имат дублирани имена на колони: {0}
#XMSG
deltaOnlyLoadTypeTittle=Възможно е да има несъответствия в данните.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Видът делта зареждане няма да вземе предвид промените, направени в източника, между последното запазване и следващото изпълнение.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Променете вида зареждане на „Начално“.
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Репликирането на обекти, базирани на ABAP,  които нямат първичен ключ, е възможно само за вид зареждане „Само начално“.
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Дезактивиране на делта заснемане.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=За да репликирате обект без първичен ключ посредством вид изходна връзка ABAP, първо трябва да дезактивирате делта заснемането за тази таблица.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Променете целевия обект.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Целевият обект не може да се използва, защото делта заснемането е активирано. Може да преименувате целевия обект и след това да изключите делта заснемането за новия (преименувания) обект или да мапирате изходния обект към целеви обект, за който делта заснемането е дезактивирано.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Променете целевия обект.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Целевият обект не може да се използва, защото няма необходимата техническа колона  __load_package_id. Може да преименувате целевия обект, като използвате име, което още не съществува. Системата след това създава нов обект, който има същата дефиниция като изходния обект и съдържа техническата колона. Също така може да мапирате целевия обект към съществуващ обект, който има необходимата техническа колона (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Променете целевия обект.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Целевият обект не може да се използва, защото няма необходимата техническа колона  __load_record_id. Може да преименувате целевия обект, като използвате име, което още не съществува. Системата след това създава нов обект, който има същата дефиниция като изходния обект и съдържа техническата колона. Също така може да мапирате целевия обект към съществуващ обект, който има необходимата техническа колона (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Променете целевия обект.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Целевият обект не може да се използва, защото видът данни на техническата му колона  __load_record_id не е „string(44)“. Може да преименувате целевия обект, като използвате име, което още не съществува. Системата след това създава нов обект, който има същата дефиниция като изходния обект и съответно правилния вид данни. Също така може да мапирате целевия обект към съществуващ обект, който има необходимата техническа колона (__load_record_id) с правилния вид данни.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Променете целевия обект.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Целевият обект не може да се използва, защото има първичен ключ, а изходният обект няма такъв. Може да преименувате целевия обект, като използвате име, което още не съществува. Системата след това създава нов обект, който има същата дефиниция като изходния обект и съответно няма първичен ключ. Също така може да мапирате целевия обект към съществуващ обект, който има необходимата техническа колона (__load_package_id) и няма първичен ключ.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Променете целевия обект.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Целевият обект не може да се използва, защото има първичен ключ, а изходният обект няма такъв. Може да преименувате целевия обект, като използвате име, което още не съществува. Системата след това създава нов обект, който има същата дефиниция като изходния обект и съответно няма първичен ключ. Също така може да мапирате целевия обект към съществуващ обект, който има необходимата техническа колона (__load_record_id) и няма първичен ключ.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Променете целевия обект.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Целевият обект не може да се използва, защото видът данни на техническата му колона __load_package_id не е „binary(>=256)“. Може да преименувате целевия обект, като използвате име, което още не съществува. Системата след това създава нов обект, който има същата дефиниция като изходния обект и съответно правилния вид данни. Също така може да мапирате целевия обект към съществуващ обект, който има необходимата техническа колона (__load_package_id) с правилния вид данни.
#XMSG
validationAutoRenameTargetDPID=Целевите колони са преименувани.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Премахнете изходния обект.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Изходният обект няма ключова колона, което не се поддържа в този контекст.
#XMSG
validationAutoRenameTargetDPIDDescription=Добавена е автоматична проекция и следните целеви колони са преименувани, за да се позволи репликация от ABAP източник без ключове:{1}{1} {0} {1}{1}Това се дължи на една от следните причини:{1}{1}{2} Запазено име на колона{1}{2} Неподдържани символи{1}{2} Запазен префикс
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Репликиране в {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Запазването и разгръщането на потоци на репликация, чиято цел е {0} в момента не е възможно, защото се изпълняваме поддръжка на тази функция.
#XMSG
TargetColumnSkippedLTF=Целевата колона е пропусната.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Целевата колона е пропусната поради неподдържан вид данни. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Времева колона като първичен ключ.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Изходният обект има времева колона като първичен ключ, което не се поддържа в този контекст.
#XMSG
validateNoPKInLTFTarget=Липсва първичен ключ.
#XMSG
validateNoPKInLTFTargetDescription=Не е дефиниран първичен ключ в целта, която не се поддържа в този контекст.
#XMSG
validateABAPClusterTableLTF=Таблица клъстери ABAP.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Изходният обект е таблица клъстери ABAP, която не се поддържа в този контекст.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Изглежда още не сте добавили данни.
#YINS
welcomeText2=За стартиране на вашия поток на репликация, изберете връзка и изходен обект отляво.

#XBUT
wizStep1=Избор на източник на връзка
#XBUT
wizStep2=Изберете изходен контейнер
#XBUT
wizStep3=Изберете изходни обекти

#XMSG
limitDataset=Максималният брой обекти е достигнат. Премахнете съществуващите такива, за да добавите нови или създайте нов поток на репликация.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Потокът на репликация към тази външна за SAP целева връзка не може да бъде стартиран, тъй като няма наличен изходящ обем за този месец.
#XMSG
premiumOutBoundRFAdminWarningMsg=Администратор може да увеличи блокирането на премията за изходящи данни за този наемател, с което да създаде наличен изходящ обем за този месец.
#XMSG
messageForToastForDPIDColumn2=Към целта е добавена нова колона за {0} обекта - необходимо е за обработката на дублирани записи във връзка с изходни обекти, базирани на ABAP,  които нямат първичен ключ.
#XMSG
PremiumInboundWarningMessage=В зависимост от броя на потоците на репликация и обема на данните за репликация, ресурсите на SAP HANA {0}, необходими за репликирането на данни с {1} може да надхвърлят наличния капацитет за вашия наемател.
#XMSG
PremiumInboundWarningMsg=В зависимост от броя на потоците на репликация и обема на данните за репликация, ресурсите на SAP HANA {0}, необходими за репликирането на данни с „{1}” може да надхвърлят наличния капацитет за вашия наемател.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Въведете име на проекция.
#XMSG
emptyTargetColumn=Трябва да въведете име на целева колона.
#XMSG
emptyTargetColumnBusinessName=Въведете целева колона „Бизнес име“
#XMSG
invalidTransformName=Въведете име на проекция.
#XMSG
uniqueColumnName=Преименувайте целевата колона.
#XMSG
copySourceColumnLbl=Копиране на колоните от изходния обект
#XMSG
renameWarning=Уверете се, че сте избрали уникално име при преименуването на целевата таблица. Ако в пространството вече съществува таблица с новото име, се използва дефиницията на тази таблица.

#XMSG
uniqueColumnBusinessName=Промяна на бизнес името на целевата колона.
#XMSG
uniqueSourceMapping=Изберете друга изходна колона.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=Изходната колона „{0}“ вече се използва от следните целеви колони: {1}{1}{2}{1}{1} За да запазите проекцията, изберете изходна колона, която не се използва, или за тази, или за останалите целеви колони.
#XMSG
uniqueColumnNameDescription=Името на целевата колона, което сте въвели, вече съществува. За да може да запазите проекцията, трябва да въведете уникално име на колона.
#XMSG
uniqueColumnBusinessNameDesc=Бизнес името на целевата колона вече съществува. За да запазите проекцията, трябва да въведете уникално бизнес име на колона.
#XMSG
emptySource=Изберете изходна колона или въведете константа.
#XMSG
emptySourceDescription=За да създадете валиден запис за мапиране, трябва да изберете изходна колона или да въведете константна стойност.
#XMSG
emptyExpression=Дефинирайте мапиране.
#XMSG
emptyExpressionDescription1=Изберете изходната колона, до която искате да мапирате целевата колона, или поставете отметка на колоната {0} Функции / константи {1}. {2} {2} Функциите се въвеждат автоматично в зависимост от вида целеви данни. Стойностите на константите може да се въвеждат ръчно.
#XMSG
numberExpressionErr=Въведете число.
#XMSG
numberExpressionErrDescription=Избрали сте числов вид данни. Това означава, че може да въвеждате само цифри и десетичен знак, ако е приложимо. Не използвайте единични кавички.
#XMSG
invalidLength=Въведете валидна стойност за дължина.
#XMSG
invalidLengthDescription=Дължината на вида данни трябва да е равна на или по-голяма от дължината на изходната колона и може да бъде между 1 и 5000.
#XMSG
invalidMappedLength=Въведете валидна стойност за дължина.
#XMSG
invalidMappedLengthDescription=Дължината на вида данни трябва да е равна на или по-голяма от дължината на изходната колона {0} и може да бъде между 1 и 5000.
#XMSG
invalidPrecision=Въведете валидна стойност за точност.
#XMSG
invalidPrecisionDescription=Точността дефинира общия брой на цифрите. Скалата дефинира броя на цифрите след десетичния знак и може да бъде между 0 и точността.{0}{0} Примери: {0}{1} Точност 6, скала 2 съответства на числа като 1234,56.{0}{1} Точност 6, скала 6 съответства на числа като 0,123546.{0} {0} Точността и скалата за целта трябва да са съвместими с точността и скалата на източника, така че всички цифри от източника да се поберат в целевото поле. Например, ако имате точност 6 и скала 2 в източника (и цифри, различни от 0 преди десетичния знак), не може да имате точност 6 и скала 6 в целевото поле.
#XMSG
invalidPrimaryKey=Въведете поне един първичен ключ.
#XMSG
invalidPrimaryKeyDescription=Няма дефиниран първичен ключ за тази схема.
#XMSG
invalidMappedPrecision=Въведете валидна стойност за точност.
#XMSG
invalidMappedPrecisionDescription1=Точността дефинира общия брой на цифрите. Скалата дефинира броя на цифрите след десетичния знак и може да бъде между 0 и точността.{0}{0} Примери:{0}{1} Точност 6, скала 2 съответства на числа като 1234,56.{0}{1} Точност 6, скала 6 съответства на числа като 0,123546.{0}{0}Точността на вида данни трябва да е равна на или по-голяма от точността в източника ({2}).
#XMSG
invalidScale=Въведете валидна стойност за скалата.
#XMSG
invalidScaleDescription=Точността дефинира общия брой на цифрите. Скалата дефинира броя на цифрите след десетичния знак и може да бъде между 0 и точността.{0}{0} Примери: {0}{1} Точност 6, скала 2 съответства на числа като 1234,56.{0}{1} Точност 6, скала 6 съответства на числа като 0,123546.{0} {0} Точността и скалата за целта трябва да са съвместими с точността и скалата на източника, така че всички цифри от източника да се поберат в целевото поле. Например, ако имате точност 6 и скала 2 в източника (и цифри, различни от 0 преди десетичния знак), не може да имате точност 6 и скала 6 в целевото поле.
#XMSG
invalidMappedScale=Въведете валидна стойност за скалата.
#XMSG
invalidMappedScaleDescription1=Точността дефинира общия брой на цифрите. Скалата дефинира броя на цифрите след десетичния знак и може да бъде между 0 и точността.{0}{0} Примери:{0}{1} Точност 6, скала 2 съответства на числа като 1234,56.{0}{1} Точност 6, скала 6 съответства на числа като 0,123546.{0}{0}Скалата на вида данни трябва да е равна на или по-голяма от скалата в източника ({2}).
#XMSG
nonCompatibleDataType=Изберете съвместим вид целеви данни.
#XMSG
nonCompatibleDataTypeDescription1=Видът данни, които посочите тук, трябва да е съвместим с вида изходни данни ({0}). {1}{1} Пример: Ако изходната колона е с вид на даннните „низ” и съдържа букви, не може да използвате вид на данните „десетичен знак” за целевата.
#XMSG
invalidColumnCount=Изберете изходна колона.
#XMSG
ObjectStoreInvalidScaleORPrecision=Въведете валидни стойности за точност и скала.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=Първата стойност е точността. Тя определя общия брой на знаците. Втората стойност е скалата. Тя определя броя на знаците след десетичната запетая. Въведете целева стойност на скалата, по-голяма от изходната стойност на скалата, а разликата между въведените целеви стойности за точност и скала трябва да е по-голяма от тази между съответните изходни стойности.
#XMSG
InvalidPrecisionORScale=Въведете валидни стойности за точност и скала.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=Първата стойност е точността дефинираща общия брой цифри. Втората стойност е скалата, която дефинира цифрите след десетичната запетая.{0}{0}Тъй като видът на изходните данни не се поддържа в Google BigQuery, той се преобразува във вида целеви данни DECIMAL. В този случай точността може да се дефинира само между 38 и 76, а скалата между 9 и 38. Освен това резултатът от точността минус скалата, която представлява цифрите преди десетичната запетая, трябва да е между 29 и 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=Първата стойност е точността дефинираща общия брой цифри. Втората стойност е скалата, която дефинира цифрите след десетичната запетая.{0}{0}Тъй като видът на изходните данни не се поддържа в Google BigQuery, той се преобразува във вида целеви данни DECIMAL. В този случай точността трябва да се дефинира с 20 или по-голяма. Освен това резултатът от точността минус скалата, която отразява цифрите преди десетичната запетая, трябва да е 20 или по-голям.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=Първата стойност е точността, дефинираща общия брой цифри. Втората стойност е скалата, която дефинира цифрите след десетичната запетая.{0}{0}Тъй като видът на изходните данни не се поддържа в целта, той се преобразува във вида целеви данни DECIMAL. В този случай точността трябва да се дефинира с число между 1 и 38 и скала, по-ниска от или равна на точността.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=Първата стойност е точността, дефинираща общия брой цифри. Втората стойност е скалата, която дефинира цифрите след десетичната запетая.{0}{0}Тъй като видът на изходните данни не се поддържа в целта, той се преобразува във вида целеви данни DECIMAL. В този случай точността трябва да се дефинира като 20 или по-голяма. Освен това резултатът от точността минус скалата, която отразява цифрите преди десетичната запетая, трябва да е 20 или по-голям.
#XMSG
invalidColumnCountDescription=За да създадете валиден запис за мапиране, трябва да изберете изходна колона или да въведете константна стойност.
#XMSG
duplicateColumns=Преименувайте целевата колона.
#XMSG
duplicateGBQCDCColumnsDesc=Името на целевата колона е резервирано в Google BigQuery. Трябва да я преименувате, за да можете да запазите проекцията.
#XMSG
duplicateConfluentCDCColumnsDesc=Името на целевата колона е резервирано в Confluent. Трябва да я преименувате, за да можете да запазите проекцията.
#XMSG
duplicateSignavioCDCColumnsDesc=Името на целевата колона е резервирано в SAP Signavio. Трябва да я преименувате, за да можете да запазите проекцията.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Името на целевата колона е резервирано в MS OneLake. Трябва да я преименувате, за да можете да запазите проекцията.
#XMSG
duplicateSFTPCDCColumnsDesc=Името на целевата колона е резервирано в SFTP. Трябва да я преименувате, за да можете да запазите проекцията.
#XMSG
GBQTargetNameWithPrefixUpdated1=Името на целевата колона съдържа префикс, запазен в Google BigQuery. Трябва да го преименувате, за да можете да запазите проекцията. {0}{0}Името на целевата колона не може да започва с нито един от следните низове: {0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Съкратете името на целевата колона.
#XMSG
GBQtargetMaxLengthDesc=В Google BigQuery името на колона може да има най-много 300 символа. Съкратете името на целевата колона, за да можете да запазите проекцията.
#XMSG
invalidMappedScalePrecision=Точността и скалата за целта трябва да бъдат съвместими с точността и скалата за източника, така че всички цифри от източника да се поберат в целевото поле.
#XMSG
invalidMappedScalePrecisionShortText=Въведете валидни стойности за точност и скала.
#XMSG
validationIncompatiblePKTypeDescProjection3=Видът данни на поне една от колоните на източника не може да бъде дефиниран като първичен ключ в Google BigQuery. В целевия обект няма да бъдат създадени никакви първични ключове.{0}{0}По-долу са изброени целевите видове данни, съвместими с Google BigQuery, за които може да бъде генериран първичен ключ:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Премахнете отметката за column __message_id
#XMSG
uncheckColumnMessageIdDesc=Колона: Първичен ключ
#XMSG
validationOpCodeInsert=Трябва да въведете стойност за вмъкване.
#XMSG
recommendDifferentPrimaryKey=Препоръчваме да изберете друг първичен ключ на ниво позиция.
#XMSG
recommendDifferentPrimaryKeyDesc=След като кодът на операцията вече е дефиниран, се препоръчва да се изберат различни първични ключове за индекса на масива и позициите, за да се избегнат проблеми като например дублиране на колони.
#XMSG
selectPrimaryKeyItemLevel=Трябва да изберете поне един първичен ключ както на ниво заглавка, така и на ниво позиция.
#XMSG
selectPrimaryKeyItemLevelDesc=Когато разширявате масив или карта, трябва да изберете два първични ключа - един на ниво заглавка и един на ниво позиция.
#XMSG
invalidMapKey=Трябва да изберете поне един първичен ключ на ниво заглавка.
#XMSG
invalidMapKeyDesc=Когато разширявате масив или карта, трябва да изберете първичен ключ на ниво заглавка.
#XFLD
txtSearchFields=Търсене на целеви колони
#XFLD
txtName=Име
#XMSG
txtSourceColValidation=Една или повече изходни колони не се поддържат:
#XMSG
txtMappingCount=Мапирания ({0})
#XMSG
schema=Схема
#XMSG
sourceColumn=Изходни колони
#XMSG
warningSourceSchema=Промените, направени по схемата, ще повлияят мапиранията в диалоговия прозорец на проекцията.
#XCOL
txtTargetColName=Целева колона (техническо име)
#XCOL
txtDataType=Вид целеви данни
#XCOL
txtSourceDataType=Вид източник на данни
#XCOL
srcColName=Изходна колона (техническо име)
#XCOL
precision=Точност
#XCOL
scale=Скала
#XCOL
functionsOrConstants=Функции/константи
#XCOL
txtTargetColBusinessName=Целева колона (Бизнес име)
#XCOL
prKey=Първоначален ключ
#XCOL
txtProperties=Свойства
#XBUT
txtOK=Запазване
#XBUT
txtCancel=Отказ
#XBUT
txtRemove=Премахване
#XFLD
txtDesc=Описание
#XMSG
rftdMapping=Мапиране
#XFLD
@lblColumnDataType=Вид данни
#XFLD
@lblColumnTechnicalName=Техническо име
#XBUT
txtAutomap=Автоматично мапиране
#XBUT
txtUp=Нагоре
#XBUT
txtDown=Надолу

#XTOL
txtTransformationHeader=Проекция
#XTOL
editTransformation=Редактиране
#XTOL
primaryKeyToolip=Ключ


#XMSG
rftdFilter=Филтър
#XMSG
rftdFilterColumnCount=Източник: {0}({1})
#XTOL
rftdFilterColSearch=Търсене
#XMSG
rftdFilterColNoData=Няма колони за показване
#XMSG
rftdFilteredColNoExps=Няма изрази на филтър
#XMSG
rftdFilterSelectedColTxt=Добавяне на филтър за
#XMSG
rftdFilterTxt=Филтриране на наличните за
#XBUT
rftdFilterSelectedAddColExp=Добавяне на израз
#YINS
rftdFilterNoSelectedCol=Избор на колона за добавяне на филтър.
#XMSG
rftdFilterExp=Израз за филтър
#XMSG
rftdFilterNotAllowedColumn=Добавянето на филтри не се поддържа за тази колона.
#XMSG
rftdFilterNotAllowedHead=Колоната не се поддържа
#XMSG
rftdFilterNoExp=Няма определен филтър
#XTOL
rftdfilteredTt=Филтрирани
#XTOL
rftdremoveexpTt=Премахване на израз за филтър
#XTOL
validationMessageTt=Съобщения от проверката
#XTOL
rftdFilterDateInp=Избор на дата
#XTOL
rftdFilterDateTimeInp=Избор на дата и час
#XTOL
rftdFilterTimeInp=Изберете час
#XTOL
rftdFilterInp=Въведете стойност
#XMSG
rftdFilterValidateEmptyMsg={0} израза на филтър в колона {1} са празни
#XMSG
rftdFilterValidateInvalidNumericMsg={0} израза на филтър в колона {1} съдържат невалидни цифрови стойности
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Израз на филтър трябва да съдържа валидни цифрови стойности
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Ако схемата на целевите обекти е променена, използвайте функцията „Мапиране до съществуващи целеви обекти“ на главната страница, за да адаптирате промените и мапирате отново целевия обект с източника му.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Ако вече има целева таблица и мапирането включва промяна в схемата, трябва да направите съответните промени по таблицата, преди да разгърнете потока на репликацията.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Ако мапирането включва промяна в схемата, трябва да направите съответните промени по таблицата, преди да разгърнете потока на репликацията.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Следните неподдържани колони са пропуснати от определението за източник: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Следните неподдържани колони са пропуснати от определението за цел: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Следните обекти не се поддържат, защото са разкрити за потребление:  {0} {1} {0} {0} За да използвате таблици в поток на репликация, за семантичното използване (в настройките на таблицата) трябва да е зададено {2}Аналитичен набор данни{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Целевият обект не може да бъде използван, защото е разкрит за потребление:  {0} {0} За да използвате таблицата в поток на репликация, за семантичното използване (в настройките на таблицата) трябва да е зададено {1}Аналитичен набор данни{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Вече има целеви обект с това име. Той обаче не може да бъде използван, защото е разкрит за потребление:  {0} {0} За да използвате таблицата в поток на репликация, за семантичното използване (в настройките на таблицата) трябва да е зададено {1}Аналитичен набор данни{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Вече има обект с това име в целта. {0}Но този обект не може да бъде използван като целеви обект за потока на репликация до локалното хранилище, тъй като не е локална таблица.
#XMSG:
targetAutoRenameUpdated=Целевата колона е преименувана.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Целевата колона е преименувана, за да се разреши репликацията в Google BigQuery. Това се дължи на една от следните причини: {0} {1}{2}Запазено име на колона{3}{2}Неподдържани символи{3}{2}Запазен префикс{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Целевата колона е преименувана, за да се разреши репликацията в Confluent. Това се дължи на една от следните причини: {0} {1}{2}Запазено име на колона{3}{2}Неподдържани символи{3}{2}Запазен префикс{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Целевата колона е преименувана, за да се разреши репликацията в целта. Това се дължи на една от следните причини: {0} {1}{2}Неподдържани символи{3}{2}Запазен префикс{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Целевата колона е преименувана, за да се разреши репликацията в целта. Това се дължи на една от следните причини: {0} {1}{2}Запазено име на колона{3}{2}Неподдържани символи{3}{2}Запазен префикс{3}{4}
#XMSG:
targetAutoDataType=Видът целеви данни е променен.
#XMSG:
targetAutoDataTypeDesc=Видът на целевите данни е променен на {0}, защото видът на изходните данни не се поддържа в Google BigQuery.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Видът на целевите данни е променен на {0}, защото видът на изходните данни не се поддържа в целевата връзка.
#XMSG
projectionGBQUnableToCreateKey=Няма да се създадат първични ключове.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=В Google BigQuery се поддържат максимум 16 първични ключа, а в изходния обект има повече. Нито един от първичните ключове няма да бъде създаден в целевия обект.
#XMSG
HDLFNoKeyError=Дефинирайте поне една колона като първичен ключ.
#XMSG
HDLFNoKeyErrorDescription=За да репликирате обект, трябва да дефинирате една или повече колони като първичен ключ.
#XMSG
HDLFNoKeyErrorExistingTarget=Дефинирайте поне една колона като първичен ключ.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=За да направите репликация на данни в съществуващ целеви обект, той трябва да има поне една дефинирана колона за първичен ключ. {0} {0} За целта разполагате със следите опции: {0}{0}{1} Променете целевия обект чрез редактора на локални таблици. Щом приключите, презаредете потока за репликация.{0}{0}{1} Преименувайте целевия обект в потока за репликация. Това ще доведе до създаването на нов обект в момента на стартиране на изпълнението. След преименуването може в проекция да посочите една или няколко колони за първичен ключ.{0}{0}{1} Мапирайте обекта към друг съществуващ целеви обект, където вече има дефинирани колони за първичен ключ.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Първичният ключ е променен.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=Дефинираните в целевия обект колони за първичен ключ се различават от тези в изходния. За да избегнете повреда на данните при репликацията, се уверете, че тези колони съдържат уникален идентификатор за всеки ред. {0} {0} В изходния обект са дефинирани следните колони за първичен ключ: {0} {1}
#XMSG
duplicateDPIDColumns=Преименувайте целевата колона.
#XMSG
duplicateDPIDDColumnsDesc1=Това име на целева колона е запазено за техническа колона. Въведете друго име, за да запазите проекцията.
#XMSG:
targetAutoRenameDPID=Целевата колона е преименувана.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Целевата колона е преименувана, за да се разреши репликацията от ABAP източник без ключове. Това се дължи на една от следните причини: {0} {1}{2}Запазено име на колона{3}{2}Неподдържани символи{3}{2}Запазен префикс{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle=Целеви настройки за {0}
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle=Изходни настройки за {0}
#XBUT
connectionSettingSave=Запазване
#XBUT
connectionSettingCancel=Отказ
#XBUT: Button to keep the object level settings
txtKeep=Запазване
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Заместване
#XFLD
targetConnectionThreadlimit=Ограничение на целта на нишката за начално зареждане (1-100)
#XFLD
connectionThreadLimit=Ограничение на източника на нишката за начално зареждане (1-100)
#XFLD
maxConnection=Лимит за нишки за репликация (1–100)
#XFLD
kafkaNumberOfPartitions=Брой дялове
#XFLD
kafkaReplicationFactor=Коефициент на репликация
#XFLD
kafkaMessageEncoder=Средство за кодиране на съобщения
#XFLD
kafkaMessageCompression=Компресия на съобщение
#XFLD
fileGroupDeltaFilesBy=Групиране на делта по
#XFLD
fileFormat=Вид файл
#XFLD
csvEncoding=Кодиране CSV
#XFLD
abapExitLbl=ABAP Exit
#XFLD
deltaPartition=Броят на нишките на обекта за делта зареждания (1-10)
#XFLD
clamping_Data=Неуспешно изпълнение поради отрязване на данни
#XFLD
fail_On_Incompatible=Неуспешно изпълнение поради несъвместими данни
#XFLD
maxPartitionInput=Максимален брой дялове
#XFLD
max_Partition=Определяне на максималния брой дялове
#XFLD
include_SubFolder=Включване на подпапки
#XFLD
fileGlobalPattern=Глобален модел за име на файл
#XFLD
fileCompression=Компресия на файл
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Разграничителен знак за файлове
#XFLD
fileIsHeaderIncluded=Заглавие на файл
#XFLD
fileOrient=Ориентация
#XFLD
gbqWriteMode=Режим на писане
#XFLD
suppressDuplicate=Потискане на дубликатите
#XFLD
apacheSpark=Активиране на съвместимост с Apache Spark
#XFLD
clampingDatatypeCb=Фиксиране на видове данни с плаваща десетична запетая
#XFLD
overwriteDatasetSetting=Презаписване на целевите стойности на ниво на обект
#XFLD
overwriteSourceDatasetSetting=Презаписване на изходните настройки на ниво на обект
#XMSG
kafkaInvalidConnectionSetting=Въведете число между {0} и {1}.
#XMSG
MinReplicationThreadErrorMsg=Въведете число, по-голямо от {0}.
#XMSG
MaxReplicationThreadErrorMsg=Въведете число, по-малко от {0}.
#XMSG
DeltaThreadErrorMsg=Въведете стойност между 1 и 10.
#XMSG
MaxPartitionErrorMsg=Въведете стойност между 1 <= x <= 2147483647. Стойността по подразбиране е 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Въведете цяло число между {0} и {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Използване на коефициента на репликация на брокера
#XFLD
serializationFormat=Формат на сериализация
#XFLD
compressionType=Вид компресиране
#XFLD
schemaRegistry=Използване на регистър на схеми
#XFLD
subjectNameStrat=Тема „Стратегия за име“
#XFLD
compatibilityType=Вид съвместимост
#XFLD
confluentTopicName=Име на тема
#XFLD
confluentRecordName=Име на запис
#XFLD
confluentSubjectNamePreview=Преглед на име на предмет
#XMSG
serializationChangeToastMsgUpdated2=Форматът на сериализацията е променен на JSON, тъй като регистърът на схемите не е разрешен. За да промените формата на сериализация обратно на AVRO, първо трябва да активирате регистъра на схемите.
#XBUT
confluentTopicNameInfo=Името на темата винаги се базира на името на целевия обект. Можете да го промените, като преименувате целевия обект.
#XMSG
emptyRecordNameValidationHeaderMsg=Въведете име на запис.
#XMSG
emptyPartionHeader=Въведете броя дялове.
#XMSG
invalidPartitionsHeader=Въведете валиден брой дялове.
#XMSG
invalidpartitionsDesc=Въведете число между 1 и 200 000.
#XMSG
emptyrFactorHeader=Въведете коефициент за репликация.
#XMSG
invalidrFactorHeader=Въведете валиден коефициент за репликация.
#XMSG
invalidrFactorDesc=Въведете число между 1 и 32 767.
#XMSG
emptyRecordNameValidationDescMsg=Ако се използва форматът на сериализация „AVRO“, се поддържат само следните символи: {0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(долна черта)
#XMSG
validRecordNameValidationHeaderMsg=Въведете валидно име на запис.
#XMSG
validRecordNameValidationDescMsgUpdated=Тъй като се използва форматът на сериализация „AVRO“, името на записа трябва да се състои само от букви и цифри (A-Z, a-z, 0-9) и долни черти (_). То трябва да започва с буква или с долна черта.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=„Броят на нишките на обекта за делта зареждания“ може да се зададе веднага след като един или няколко обекта имат вид зареждане „Начално и делта“.
#XMSG
invalidTargetName=Невалидно име на колона
#XMSG
invalidTargetNameDesc=Името на целевата колона трябва да се състои само от букви, цифри (A-Z, a-z, 0-9) и долни черти (_).
#XFLD
consumeOtherSchema=Използване на други версии на схема
#XFLD
ignoreSchemamissmatch=Игнориране на несъответствие на схема
#XFLD
confleuntDatatruncation=Неуспешно изпълнение при съкращаване на данни
#XFLD
isolationLevel=Ниво на изолация
#XFLD
confluentOffset=Начална точка
#XFLD
signavioGroupDeltaFilesByText=Няма
#XFLD
signavioFileFormatText=Паркет
#XFLD
signavioSparkCompatibilityParquetText=Не
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=Не

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Проекции
#XBUT
txtAdd=Добавяне
#XBUT
txtEdit=Редактиране
#XMSG
transformationText=Добавяне на проекция за конфигуриране на филтър или мапиране.
#XMSG
primaryKeyRequiredText=Избор на първичен ключ с Конфигуриране на схема.
#XFLD
lblSettings=Настройки
#XFLD
lblTargetSetting={0}: Целеви настройки
#XMSG
@csvRF=Изберете файла, който съдържа определението на схемата, което желаете да приложите към всички файлове в папката.
#XFLD
lblSourceColumns=Изходни колони
#XFLD
lblJsonStructure=Структура JSON
#XFLD
lblSourceSetting={0}: Изходни настройки 
#XFLD
lblSourceSchemaSetting={0}: Настройки на изходна схема
#XBUT
messageSettings=Настройки на съобщения
#XFLD
lblPropertyTitle1=Свойства на обект
#XFLD
lblRFPropertyTitle=Свойства на поток на репликация
#XMSG
noDataTxt=Няма колони за показване
#XMSG
noTargetObjectText=Не е избран целеви обект.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Целеви колони
#XMSG
searchColumns=Търсене на колони
#XTOL
cdcColumnTooltip=Колона за делта заснемане
#XMSG
sourceNonDeltaSupportErrorUpdated=Изходният обект не поддържа делта заснемане.
#XMSG
targetCDCColumnAdded=2 целеви колони са добавени за делта заснемане.
#XMSG
deltaPartitionEnable=Ограничение на нишката на обекта за делта зареждания е добавено към изходните настройки. 
#XMSG
attributeMappingRemovalTxt=Премахване на невалидни мапирания, които не се поддържат за новия целеви обект.
#XMSG
targetCDCColumnRemoved=2 целеви колони, използвани за делта заснемане, са премахнати.
#XMSG
replicationLoadTypeChanged=Видът зареждане е променен на „Начално и делта“.
#XMSG
sourceHDLFLoadTypeError=Промяна на вида зареждане на „Начално и делта“.
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=За да направите репликация на обект от връзка с източник от вида „Файлове от SAP HANA Cloud, Data Lake“ в SAP Datasphere, трябва да използвате вида зареждане „Начално и делта“.
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Активиране на делта заснемане.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=За да направите репликация на обект от връзка с източник от вида „Файлове от SAP HANA Cloud, Data Lake“ в SAP Datasphere, трябва да активирате делта заснемането.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Променете целевия обект.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Целевият обект не може да се използва, тъй като делта заснемането е дезактивирано. Може да преименувате целевия обект (което ви позволява да създадете нов обект с делта заснемане) или да направите мапиране към съществуващ обект с активно делата заснемане.
#XMSG
deltaPartitionError=Въведете валиден брой на нишките на обекта за делта зареждания.
#XMSG
deltaPartitionErrorDescription=Въведете стойност между 1 и 10.
#XMSG
deltaPartitionEmptyError=Въведете броя на нишките на обекта за делта зареждания.
#XFLD
@lblColumnDescription=Описание
#XMSG
@lblColumnDescriptionText1=За технически цели - обработка на дублирани записи, причинени от проблеми при репликацията на изходни обекти, базирани на ABAP, които нямат първичен ключ.
#XFLD
storageType=Съхранение
#XFLD
skipUnmappedColLbl=Пропускане на немапираните колони
#XFLD
abapContentTypeLbl=Вид съдържание
#XFLD
autoMergeForTargetLbl=Автоматично сливане на данни
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Общи
#XFLD
lblBusinessName=Бизнес наименование
#XFLD
lblTechnicalName=Техническо име
#XFLD
lblPackage=Пакет
#XFLD
statusPanel=Статус на изпълнение
#XBTN: Schedule dropdown menu
SCHEDULE=График
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Редактиране на график
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Изтриване на график
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Създаване на график
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Неуспешна проверка на валидирането на графика
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Даден график не може да бъде създаден, тъй като потокът на репликация в момента е разгърнат.{0}Моля, изчакайте докато потокът на репликацията е разгърнат. 
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=За потоци на репликация, които съдържат обект с вид натоварване „начално и делта“, не може да бъде създаден график.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=За потоци на репликация, които съдържат обекти с вид зареждане „Начално и делта/Само делта“, не може да бъде създаден график.
#XFLD : Scheduled popover
SCHEDULED=Насрочено
#XFLD
CREATE_REPLICATION_TEXT=Създаване на поток на репликация
#XFLD
EDIT_REPLICATION_TEXT=Редактиране на поток на репликация
#XFLD
DELETE_REPLICATION_TEXT=Изтриване на поток на репликация
#XFLD
REFRESH_FREQUENCY=Честота
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Потокът на репликация не може да бъде разгърнат, тъй като съществуващият график{0} все още не поддържа вида зареждане „начално и делта“.{0}{0} За да разгърнете потока на репликация, трябва да зададете за видовете зареждане на всички обекти {0} „само начално“. Друг вариант е да изтриете графика, да разгърнете {0} потока на репликация и да започнете  ново изпълнение. Това води до непрекъснато {0} изпълнение, което поддържа  също и обекти с вид зареждане „начално и делта“.
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Потокът на репликация не може да бъде разгърнат, защото съществуващият график{0} все още не поддържа вида зареждане „Начално и делта/Само делта“.{0}{0} За да разгърнете потока на репликация, трябва да зададете за видовете зареждане на всички обекти {0} „Само начално“. Друг вариант е да изтриете графика, да разгърнете {0} потока на репликация и да започнете  ново изпълнение. Това води до непрекъснато {0} изпълнение, което поддържа и обекти от вид зареждане „Начално и делта/ Само делта“.
#XMSG
SCHEDULE_EXCEPTION=Неуспешно извличане на подробни данни за график
#XFLD: Label for frequency column
everyLabel=на всеки
#XFLD: Plural Recurrence text for Hour
hoursLabel=часа
#XFLD: Plural Recurrence text for Day
daysLabel=дни
#XFLD: Plural Recurrence text for Month
monthsLabel=месеца
#XFLD: Plural Recurrence text for Minutes
minutesLabel=минути
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Неуспешно получаване на информация за възможността за насрочване.
#XFLD :Paused field
PAUSED=На пауза
#XMSG
navToMonitoring=Отваряне в Монитор на потоци
#XFLD
statusLbl=Статус
#XFLD
lblLastRunExecuted=Начало на последното изпълнение
#XFLD
lblLastExecuted=Последно изпълнение
#XFLD: Status text for Completed
statusCompleted=Завършено
#XFLD: Status text for Running
statusRunning=Изпълнява се
#XFLD: Status text for Failed
statusFailed=Неуспешно
#XFLD: Status text for Stopped
statusStopped=Спряно
#XFLD: Status text for Stopping
statusStopping=Спира
#XFLD: Status text for Active
statusActive=Активно
#XFLD: Status text for Paused
statusPaused=Спряно
#XFLD: Status text for not executed
lblNotExecuted=Все още няма изпълнение
#XFLD
messagesSettings=Настройки на съобщения
#XTOL
@validateModel=Съобщения от проверката
#XTOL
@hierarchy=Йерархия
#XTOL
@columnCount=Брой колони
#XMSG
VAL_PACKAGE_CHANGED=Присъединили сте този обект към пакета „{1}“. Кликнете върху „Запазване“, за да потвърдите и валидирате тази промяна. Обърнете внимание, че след запазването присъединяването към пакет повече не може да бъде отменено през този редактор.
#XMSG
MISSING_DEPENDENCY=Не успяхме да разчетем зависимостите на обект „{0}“ в пакет „{1}“.
#XFLD
deltaLoadInterval=Интервал за делта зареждане
#XFLD
lblHour=Часове (0-24)
#XFLD
lblMinutes=Минути (0-59)
#XMSG
maxHourOrMinErr=Въведете стойност между 0 и {0}
#XMSG
maxDeltaInterval=Максималната стойност на интервала за делта зареждането е 24 часа.{0}Нанесете съответните промени по стойността за минутите или часовете.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Път за целеви контейнер
#XFLD
confluentSubjectName=Име на тема
#XFLD
confluentSchemaVersion=Версия на схема
#XFLD
confluentIncludeTechKeyUpdated=Включване на технически ключ
#XFLD
confluentOmitNonExpandedArrays=Пропускане на неразширени масиви
#XFLD
confluentExpandArrayOrMap=Разширяване на масив или карта
#XCOL
confluentOperationMapping=Мапиране на операция
#XCOL
confluentOpCode=Код на операцията
#XFLD
confluentInsertOpCode=Вмъкване
#XFLD
confluentUpdateOpCode=Актуализиране
#XFLD
confluentDeleteOpCode=Изтриване
#XFLD
expandArrayOrMapNotSelectedTxt=Не е избрано
#XFLD
confluentSwitchTxtYes=Да
#XFLD
confluentSwitchTxtNo=Не
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Грешка
#XTIT
executeWarning=Предупреждение
#XMSG
executeunsavederror=Запазете потока си на репликация преди да го изпълните.
#XMSG
executemodifiederror=В потока на репликация има незапазени промени. Моля, запазете го.
#XMSG
executeundeployederror=Трябва да разгърнете вашия поток на репликация преди да е възможно да го изпълните.
#XMSG
executedeployingerror=Моля, изчакайте разгръщането да завърши.
#XMSG
msgRunStarted=Изпълнението е стартирано
#XMSG
msgExecuteFail=Неуспешно изпълнение на потока на репликация
#XMSG
titleExecuteBusy=Моля, изчакайте.
#XMSG
msgExecuteBusy=Подготвяме данните ви за изпълнение на потока на репликация.
#XTIT
executeConfirmDialog=Предупреждение
#XMSG
msgExecuteWithValidations=Потокът на репликация има грешки при проверка. Изпълнението му може да е причина за неуспех.
#XMSG
msgRunDeployedVersion=Има промени за разгръщане. Ще бъде изпълнена последната разгърната версия на потока на репликация. Желаете ли да продължите?
#XBUT
btnExecuteAnyway=Да се изпълни въпреки това
#XBUT
btnExecuteClose=Затваряне
#XBUT
loaderClose=Затваряне
#XTIT
loaderTitle=Зареждане
#XMSG
loaderText=Подробните данни се получават от сървъра
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Не може да се стартира потокът на репликация към тази външна за SAP целева връзка,
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=тъй като няма наличен изходящ обем за този месец.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Администратор може да увеличи блокирането на премията за изходящи данни за този
#XMSG
premiumOutBoundRFAdminErrMsgPart2=наемател, с което да създаде наличен изходящ обем за този месец.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Грешка
#XTIT
deployInfo=Информация
#XMSG
deployCheckFailException=Възникна изключение по време на разгръщането
#XMSG
deployGBQFFDisabled=Разгръщането на потоци на репликация с целева връзка към Google BigQuery в момента е невъзможно, защото изпълняваме поддръжка на тази функция.
#XMSG
deployKAFKAFFDisabled=Разгръщането на потоци на репликация с целева връзка към Apache Kafka в момента е невъзможно, защото изпълняваме поддръжка на тази функция.
#XMSG
deployConfluentDisabled=Разгръщането на потоци на репликация с целева връзка към  Confluent Kafka в момента е невъзможно, защото изпълняваме поддръжка на тази функция.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=За следните целеви обекти имената на таблиците за делта заснемане вече се използват от други таблици в хранилището: {0}. Трябва да преименувате тези обекти, за да гарантирате, че свързаните с тях имена на таблици за делта заснемане са уникални, преди да разгърнете потока на репликацията.
#XMSG
deployDWCSourceFFDisabled=Разгръщането на потоци на репликация, чийто източник е SAP Datasphere в момента не е възможно, защото се изпълняваме поддръжка на тази функция.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Разгръщането на потоци за репликация, които съдържат локални таблици с активирано делта заснемане в качеството на изходни обекти, за момента не е възможно, тъй като тече поддръжка на функцията.
#XMSG
deployHDLFSourceFFDisabled=Разгръщането на потоци на репликация, които имат изходни връзки от вида файлове на SAP HANA Cloud, Data Lake в момента не е възможно, защото се изпълняваме поддръжка.
#XMSG
deployObjectStoreAsSourceFFDisabled=В момента не е възможно разгръщането на потоци на репликация, които имат доставчици на съхранение в облак като източник.
#XMSG
deployConfluentSourceFFDisabled=Разгръщането на потоци на репликация с източник Confluent Kafka в момента не е възможно, защото се извършва поддръжка на тази функция.
#XMSG
deployMaxDWCNewTableCrossed=Не е възможно да „запазите и разгърнете“ големи потоци на репликация в една стъпка. Моля, първо запазете своя поток на репликация и след това го разгърнете.
#XMSG
deployInProgressInfo=Разгръщането вече се изпълнява.
#XMSG
deploySourceObjectInUse=Изходните обекти {0} вече се използват в потоците на репликация {1}.
#XMSG
deployTargetSourceObjectInUse=Изходните обекти {0} вече се използват в потоците на репликация {1}. Целевите обекти {2} вече се използват в потоците на репликация {3}.
#XMSG
deployReplicationFlowCheckError=Грешка при проверката на потока на репликация: {0}
#XMSG
preDeployTargetObjectInUse=Целевите обекти {0} вече се използват в потоци на репликация {1} и не може да имате един и същ целеви обект в два различни потока на репликация. Изберете друг целеви обект и опитайте отново.
#XMSG
runInProgressInfo=Потокът на репликация вече се изпълнява.
#XMSG
deploySignavioTargetFFDisabled=Разгръщането на потоци на репликация, чиято цел е SAP Signavio, в момента не е възможно, защото се изпълняваме поддръжка на тази функция.
#XMSG
deployHanaViewAsSourceFFDisabled=За момента разгръщането на потоци на репликация, които имат изгледи като изходни обекти за избраната изходна връзка не е възможно. Опитайте отново по-късно.
#XMSG
deployMsOneLakeTargetFFDisabled=Разгръщането на потоци на репликация, чиято цел е MS OneLake, в момента не е възможно, защото изпълняваме поддръжка на тази функция.
#XMSG
deploySFTPTargetFFDisabled=Разгръщането на потоци на репликация, чиято цел е SFTP, в момента не е възможно, защото изпълняваме поддръжка на тази функция.
#XMSG
deploySFTPSourceFFDisabled=Разгръщането на потоци на репликация, чийто източник е SFTP, в момента не е възможно, защото изпълняваме поддръжка на тази функция.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Техническо име
#XFLD
businessNameInRenameTarget=Бизнес наименование
#XTOL
renametargetDialogTitle=Преименуване на целеви обект
#XBUT
targetRenameButton=Преименуване
#XBUT
targetRenameCancel=Отказ
#XMSG
mandatoryTargetName=Трябва да въведете име.
#XMSG
dwcSpecialChar=_(подчертаване) е единственият позволен специален символ.
#XMSG
dwcWithDot=Името на целевата таблица може да съдържа латински букви, цифри, долни черти (_) и точки (.). Първият символ трябва да е буква, цифра или долна черта (не точка).
#XMSG
nonDwcSpecialChar=Позволените специални символи са _(подчертаване) -(тире) .(точка)
#XMSG
firstUnderscorePattern=Името на обекта не може да започва с _(долна черта)

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: Преглед на SQL оператор Create Table
#XMSG
sqlDialogMaxPKWarning=В Google BigQuery се поддържат най-много 16 първични ключа, а изходният обект има повече. Следователно в този оператор не се дефинират първични ключове.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Една или повече изходни колони имат видове данни, които не могат да бъдат дефинирани като първични ключове в Google BigQuery. Следователно в този случай не се дефинират първични ключове. В Google BigQuery само следните видове данни могат да имат първичен ключ: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Копиране и затваряне
#XBUT
closeDDL=Затваряне
#XMSG
copiedToClipboard=Копирано в клипборда


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Обект „{0}“ не може да бъде част от веригата задачи, защото няма край (тъй като включва обекти със зареждане от вид Начално и делта/Само делта).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Обект „{0}“ не може да бъде част от веригата задачи, защото няма край (тъй като включва обекти с начално и делта вид зареждане).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Обектът „{0}“ не може да бъде добавен към веригата от задачи.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Има незапазени целеви обекти. Моля, запазете отново.{0}{0} Поведението на тази функция е променено: В миналото целевите обекти се създаваха  в целевата среда само когато потокът на репликация бъде разгърнат.{0} Сега обектите са вече създадени, когато потокът на репликация се запази. Вашият поток е създаден преди тази промяна и съдържа нови обекти.{0} Трябва да запазите потока на репликация отново, преди да го разгърнете, така че новите обекти да бъдат включени правилно.
#XMSG
confirmChangeContentTypeMessage=Ще промените вида на съдържанието. Ако го направите, всички съществуващи проекции ще бъдат изтрити.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Име на тема
#XFLD
schemaDialogVersionName=Версия на схема
#XFLD
includeTechKey=Включване на технически ключ
#XFLD
segementButtonFlat=Плоско
#XFLD
segementButtonNested=Вложено
#XMSG
subjectNamePlaceholder=Търсене име на тема

#XMSG
@EmailNotificationSuccess=Конфигурацията на имейл известията във време на изпълнение е запазена.

#XFLD
@RuntimeEmailNotification=Имейл известие във времето на изпълнение

#XBTN
@TXT_SAVE=Запазване


