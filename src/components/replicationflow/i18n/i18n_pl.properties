#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Przepływ replikacji

#XFLD: Edit Schema button text
editSchema=Edytuj schemat

#XTIT : Properties heading
configSchema=Konfiguruj schemat

#XFLD: save changed button text
applyChanges=Zastosuj zmiany


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Wybierz połączenie źródła
#XFLD
sourceContainernEmptyText=W<PERSON><PERSON><PERSON> kontener
#XFLD
targetConnectionEmptyText=Wybierz połączenie celu
#XFLD
targetContainernEmptyText=Wy<PERSON>rz kontener
#XFLD
sourceSelectObjectText=Wybierz obiekt źródłowy
#XFLD
sourceObjectCount=Obiekty źródłowe ({0})
#XFLD
targetObjectText=Obie<PERSON><PERSON> docelowe
#XFLD
confluentBrowseContext=<PERSON><PERSON><PERSON><PERSON> kontekst
#XBUT
@retry=Spróbuj ponownie
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Upgrade tenanta jest w toku.

#XTOL
browseSourceConnection=Przeglądaj połączenie źródła
#XTOL
browseTargetConnection=Przeglądaj połączenie celu
#XTOL
browseSourceContainer=Przeglądaj kontener źródłowy
#XTOL
browseAndAddSourceDataset=Dodaj obiekty źródłowe
#XTOL
browseTargetContainer=Przeglądaj kontener docelowy
#XTOL
browseTargetSetting=Przeglądaj ustawienie docelowe
#XTOL
browseSourceSetting=Przeglądaj ustawienie źródłowe
#XTOL
sourceDatasetInfo=Informacje
#XTOL
sourceDatasetRemove=Usuń
#XTOL
mappingCount=Oznacza łączną liczbę mapowań/wyrażeń nieopartych na nazwie.
#XTOL
filterCount=Oznacza łączną liczbę warunków filtra.
#XTOL
loading=Wczytywanie...
#XCOL
deltaCapture=Rejestrowanie delty
#XCOL
deltaCaptureTableName=Tabela rejestrowania delty
#XCOL
loadType=Typ wczytywania
#XCOL
deleteAllBeforeLoading=Usuń wszystko przed wczytaniem
#XCOL
transformationsTab=Projekcje
#XCOL
settingsTab=Ustawienia

#XBUT
renameTargetObjectBtn=Zmień nazwę obiektu docelowego
#XBUT
mapToExistingTargetObjectBtn=Przypisz do istniejącego obiektu docelowego
#XBUT
changeContainerPathBtn=Zmień ścieżkę kontenera
#XBUT
viewSQLDDLUpdated=Wyświetl instrukcję SQL dot. tworzenia tabel
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Obiekt źródłowy nie obsługuje pobrania danych delta, ale wybrany obiekt docelowy ma włączoną opcję rejestrowania delty.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Nie można użyć obiektu docelowego, ponieważ włączono rejestrowanie delty,{0}podczas gdy obiekt źródłowy nie obsługuje rejestrowania delty.{1}Możesz wybrać inny obiekt docelowy, który nie obsługuje rejestrowania delty.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Istnieje już obiekt docelowy z tą samą nazwą. Nie można go jednak użyć{0}ponieważ włączono rejestrowanie delty, podczas gdy obiekt źródłowy nie{0}obsługuje rejestrowania delty.{1}Możesz wprowadzić nazwę istniejącego obiektu docelowego, który nie{0}obsługuje rejestrowania delty, lub wprowadzić nazwę, która jeszcze nie istnieje.
#XBUT
copySQLDDLUpdated=Kopiuj instrukcję SQL dot. tworzenia tabel
#XMSG
targetObjExistingNoCDCColumnUpdated=Istniejące tabele w Google BigQuery muszą zawierać poniższe kolumny dla rejestrowania danych zmiany (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Następujące obiekty źródłowe nie są obsługiwane, ponieważ nie mają klucza głównego lub korzystają z połączenia, które nie spełnia warunków pobierania klucza głównego:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Możliwe rozwiązanie można znaleźć w artykule SAP KBA 3531135.
#XLST: load type list values
initial=Tylko początkowe
@emailUpdateError=Błąd podczas aktualizacji listy powiadomień e-mail

#XLST
initialDelta=Początkowe i delta

#XLST
deltaOnly=Tylko delta
#XMSG
confluentDeltaLoadTypeInfo=W przypadku źródła Confluent Kafka obsługiwany jest tylko typ wczytania Początkowe i delta.
#XMSG
confirmRemoveReplicationObject=Czy potwierdzasz, że chcesz usunąć replikację?
#XMSG
confirmRemoveReplicationTaskPrompt=Ta czynność spowoduje usunięcie istniejących replikacji. Czy chcesz kontynuować?
#XMSG
confirmTargetConnectionChangePrompt=Ta czynność spowoduje zresetowanie połączenia celu, docelowego kontenera i usunie wszystkie obiekty docelowe. Czy chcesz kontynuować?
#XMSG
confirmTargetContainerChangePrompt=Ta czynność spowoduje zresetowanie docelowego kontenera i usunie wszystkie istniejące obiekty docelowe. Czy chcesz kontynuować?
#XMSG
confirmRemoveTransformObject=Czy potwierdzasz, że chcesz usunąć projekcję {0}?
#XMSG
ErrorMsgContainerChange=Wystąpił błąd podczas zmiany ścieżki kontenera.
#XMSG
infoForUnsupportedDatasetNoKeys=Poniższe obiekty źródłowe nie są obsługiwane, ponieważ nie mają klucza podstawowego:
#XMSG
infoForUnsupportedDatasetView=Poniższe obiekty źródłowe typu Widoki nie są obsługiwane:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Następujący obiekt źródłowy nie jest obsługiwany, ponieważ jest widokiem SQL zawierającym parametry wejściowe:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Poniższe obiekty źródłowe nie są obsługiwane, ponieważ ekstrakcja została dla nich wyłączona:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=W przypadku połączeń Confluent jedyne dozwolone formaty serializacji to AVRO oraz JSON. Poniższe obiekty nie są obsługiwane, ponieważ korzystają z innego formatu serializacji:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Nie można pobrać schematu dla następujących obiektów. Wybierz odpowiedni kontekst lub zweryfikuj konfigurację rejestru schematów
#XTOL: warning dialog header on deleting replication task
deleteHeader=Usuń
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Ustawienie Usuń wszystko przed wczytaniem nie jest obsługiwane dla Google BigQuery.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Ustawienie Usuń wszystko przed wczytaniem powoduje usuwanie i ponowne tworzenie obiektu (tematu) przed każdą replikacją. Powoduje również usuwanie wszystkich przypisanych komunikatów.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Ustawienie Usuń wszystko przed wczytaniem nie jest obsługiwane dla tego typu celu.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Nazwa techniczna
#XCOL
connBusinessName=Nazwa biznesowa
#XCOL
connDescriptionName=Opis
#XCOL
connType=Typ
#XMSG
connTblNoDataFoundtxt=Nie znaleziono połączeń
#XMSG
connectionError=Wystąpił błąd podczas pobierania połączeń.
#XMSG
connectionCombinationUnsupportedErrorTitle=Kombinacja połączenia nie jest obsługiwana
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Replikacja z {0} do {1} nie jest obecnie obsługiwana.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Kombinacja typu połączenia nie jest obsługiwana
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Replikacja z połączenia z typem połączenia SAP HANA Cloud, Data Lake Files do {0} nie jest obsługiwana. Możliwa jest replikacja tylko do SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Wybierz
#XBUT
containerCancelBtn=Anuluj
#XTOL
containerSelectTooltip=Wybierz
#XTOL
containerCancelTooltip=Anuluj
#XMSG
containerContainerPathPlcHold=Ścieżka kontenera
#XFLD
containerContainertxt=Kontener
#XFLD
confluentContainerContainertxt=Kontekst
#XMSG
infoMessageForSLTSelection=Tylko /SLT/ ID transferu zbiorczego jest dozwolony jako kontener. Wybierz ID transferu zbiorczego w SLT (jeśli jest dostępny) i kliknij opcję Prześlij.
#XMSG
msgFetchContainerFail=Wystąpił błąd podczas pobierania danych kontenera.
#XMSG
infoMessageForSLTHidden=To połączenie nie obsługuje folderów SLT, więc nie ma ich na poniższej liście.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Wybierz kontener zawierający podfoldery.
#XMSG
sftpIncludeSubFolderText=Fałsz
#XMSG
sftpIncludeSubFolderTextNew=Nie

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Brak mapowania filtru)
#XMSG
failToFetchRemoteMetadata=Wystąpił błąd podczas pobierania metadanych.
#XMSG
failToFetchData=Wystąpił błąd podczas pobierania istniejącego celu.
#XCOL
@loadType=Typ pobrania
#XCOL
@deleteAllBeforeLoading=Usuń wszystko przed wczytaniem

#XMSG
@loading=Wczytywanie...
#XFLD
@selectSourceObjects=Wybierz obiekty źródłowe
#XMSG
@exceedLimit=Nie można zaimportować więcej niż {0} obiektów jednocześnie. Anuluj wybór co najmniej {1} obiektów.
#XFLD
@objects=Obiekty
#XBUT
@ok=OK
#XBUT
@cancel=Anuluj
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Następny
#XBUT
btnAddSelection=Dodaj wybór
#XTOL
@remoteFromSelection=Usuń z wyboru
#XMSG
@searchInForSearchField=Szukaj w: {0}

#XCOL
@name=Nazwa techniczna
#XCOL
@type=Typ
#XCOL
@location=Lokalizacja
#XCOL
@label=Nazwa biznesowa
#XCOL
@status=Status

#XFLD
@searchIn=Szukaj w:
#XBUT
@available=Dostępne
#XBUT
@selection=Wybór

#XFLD
@noSourceSubFolder=Tabele i widoki
#XMSG
@alreadyAdded=Już istnieje na wykresie
#XMSG
@askForFilter=Liczba pozycji jest większa od {0}. Wprowadź ciąg znaków filtra, aby zawęzić liczbę pozycji.
#XFLD: success label
lblSuccess=Powodzenie
#XFLD: ready label
lblReady=Gotowe
#XFLD: failure label
lblFailed=Niepowodzenie
#XFLD: fetching status label
lblFetchingDetail=Pobieranie szczegółów

#XMSG Place holder text for tree filter control
filterPlaceHolder=Wpisz tekst, aby odfiltrować obiekty najwyższego poziomu
#XMSG Place holder text for server search control
serverSearchPlaceholder=Wpisz i naciśnij Enter, aby wyszukać
#XMSG
@deployObjects=Importowanie {0} objiektów...
#XMSG
@deployObjectsStatus=Liczba importowanych obiektów: {0}. Liczba obiektów, których nie można było importować: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Nie można było otworzyć w przeglądarce lokalnego repozytorium.
#XMSG
@openRemoteSourceBrowserError=Nie można było pobrać obiektów źródłowych.
#XMSG
@openRemoteTargetBrowserError=Nie można było pobrać obiektów docelowych.
#XMSG
@validatingTargetsError=Wystąpił błąd podczas walidacji celów.
#XMSG
@waitingToImport=Gotowe do importu

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Przekroczono maksymalną liczbę obiektów. Wybierz maksymalnie 500 obiektów dla jednego przebiegu replikacji.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Nazwa techniczna
#XFLD
sourceObjectBusinessName=Nazwa biznesowa
#XFLD
sourceNoColumns=Liczba kolumn
#XFLD
containerLbl=Kontener

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Musisz wybrać połączenie źródła dla przepływu replikacji.
#XMSG
validationSourceContainerNonExist=Musisz wybrać kontener dla połączenia źródła.
#XMSG
validationTargetNonExist=Musisz wybrać połączenie celu dla przepływu replikacji.
#XMSG
validationTargetContainerNonExist=Musisz wybrać kontener dla połączenia celu.
#XMSG
validationTruncateDisabledForObjectTitle=Replikacja do pamięci obiektów.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Replikacja do pamięci w chmurze jest możliwa tylko wtedy, gdy ustawiono opcję Usuń wszystko przed wczytaniem lub obiekt docelowy nie istnieje w miejscu docelowym.{0}{0} Aby mimo to aktywować replikację dla obiektów, dla których nie ustawiono opcji Usuń wszystko przed wczytaniem, przed wykonaniem przepływu replikacji upewnij się, że obiekt docelowy nie istnieje w systemie.
#XMSG
validationTaskNonExist=Musisz mieć co najmniej jedną replikację w przepływie replikacji.
#XMSG
validationTaskTargetMissing=Musisz mieć cel dla replikacji ze źródłem: {0}
#XMSG
validationTaskTargetIsSAC=Wybrany cel jest artefaktem SAC: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Wybrany cel nie jest obsługiwaną tabelą lokalną: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Obiekt o tej nazwie już istnieje w miejscu docelowym, ale nie można go użyć jako obiektu docelowego dla przepływu replikacji do lokalnego repozytorium, ponieważ nie jest to tabela lokalna.
#XMSG
validateSourceTargetSystemDifference=Musisz wybrać inne kombinacje połączenia źródła i celu oraz kontenera dla przepływu replikacji.
#XMSG
validateDuplicateSources=conajmniej jedna replikacja zawiera zduplikowane nazwy obiektów źródłowych: {0}.
#XMSG
validateDuplicateTargets=conajmniej jedna replikacja zawiera zduplikowane nazwy obiektów docelowych: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=Obiekt źródłowy {0} nie obsługuje rejestrowania danych, natomiast obiekt docelowy {1} obsługuje. Musisz usunąć replikację.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Musisz wybrać typ wczytania "Początkowe i delta" dla replikacji z nazwą obiektu docelowego {0}.
#XMSG
validationAutoRenameTarget=Zmieniono nazwę kolumn docelowych.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Dodano automatyczną projekcję i zmieniono nazwę poniższych kolumn docelowych w celu umożliwienia replikacji do miejsca docelowego:{1}{1} {0} {1}{1}Wynika to z jednego z następujących powodów:{1}{1}{2} Nieobsługiwane znaki{1}{2} Zarezerwowany prefiks
#XMSG
validationAutoRenameTargetDescriptionUpdated=Dodano automatyczną projekcję i zmieniono nazwę poniższych kolumn docelowych w celu umożliwienia replikacji do Google BigQuery:{1}{1} {0} {1}{1}Wynika to z jednego z następujących powodów:{1}{1}{2} Zarezerwowana nazwa kolumny{1}{2} Nieobsługiwane znaki{1}{2} Zarezerwowany prefiks
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Dodano automatyczną projekcję i zmieniono nazwę poniższych kolumn docelowych w celu umożliwienia replikacji do Confluent:{1}{1} {0} {1}{1}Wynika to z jednego z następujących powodów:{1}{1}{2} Zarezerwowana nazwa kolumny{1}{2} Nieobsługiwane znaki{1}{2} Zarezerwowany prefiks
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Dodano automatyczną projekcję i zmieniono nazwę poniższych kolumn docelowych w celu umożliwienia replikacji do obiektu docelowego:{1}{1} {0} {1}{1}Wynika to z jednego z następujących powodów:{1}{1}{2} Zarezerwowana nazwa kolumny{1}{2} Nieobsługiwane znaki{1}{2} Zarezerwowany prefiks
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Zmieniono nazwę obiektu docelowego.
#XMSG
autoRenameInfoDesc=Zmieniono nazwę obiektu docelowego, ponieważ zawierał nieobsługiwane znaki. Obsługiwane są tylko następujące znaki:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(kropka){0}{1}_(podkreślenie){0}{1}-(łącznik)
#XMSG
validationAutoTargetTypeConversion=Zmieniono docelowe typy danych.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Dla poniższych kolumn docelowych zmieniono docelowe typy danych, ponieważ źródłowe typy danych nie są obsługiwane w Google BigQuery:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Dla poniższych kolumn docelowych zmieniono docelowe typy danych, ponieważ źródłowe typy danych nie są obsługiwane w połączeniu docelowym:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Skróć nazwy kolumn docelowych.
#XMSG
validationMaxCharLengthGBQTargetDescription=W Google BigQuery nazwy kolumn mogą zawierać maksymalnie 300 znaków. Użyj projekcji, aby skrócić poniższe nazwy kolumn docelowych:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Kucze główne nie zostaną utworzone.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=W Google BigQuery obsługiwanych jest maksymalnie 16 kluczy głównych, ale obiekt źródłowy zawiera więcej niż 16 kluczy głównych. Żaden z kluczy głównych nie zostanie utworzony w obiekcie docelowym.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Co najmniej jedna kolumna źródłowa ma typy danych, których nie można zdefiniować jako klucze główne w Google BigQuery. W obiekcie docelowym nie zostanie wygenerowany żaden klucz główny.{0}{0}Poniższe typy danych docelowych są zgodne z typami danych Google BigQuery, dla których można zdefiniować klucz główny: {0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Zdefiniuj co najmniej jedną kolumnę jako klucz główny.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Musisz zdefiniować co najmniej jedną kolumnę jako klucz główny. Użyj w tym celu okna dialogowego schematu źródłowego.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Zdefiniuj co najmniej jedną kolumnę jako klucz główny.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Musisz zdefiniować co najmniej jedną kolumnę jako klucz główny, który pasuje do ograniczeń klucza głównego dla obiektu źródłowego. Aby to zrobić, przejdź do konfiguracji schematu we właściwościach obiektu źródłowego.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Wprowadź prawidłową maksymalną wartość partycji.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Maksymalna wartość partycji musi być ≥ 1 i ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Zdefiniuj co najmniej jedną kolumnę jako klucz główny.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Aby zreplikować obiekt, musisz zdefiniować co najmniej jedną kolumnę docelową jako klucz główny. W tym celu użyj projekcji.
#XMSG
validateHDLFNoPKExistingDatasetError=Zdefiniuj co najmniej jedną kolumnę jako klucz główny.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Aby dane mogły zostać zreplikowane do istniejącego obiektu docelowego, muszą zawierać co najmniej jedną kolumnę zdefiniowaną jako klucz główny. {0} Dostępne są następujące opcje definiowania jednej lub kilku kolumn jako klucza głównego: {0} {1} Użyj edytora tabeli lokalnych, aby zmienić istniejący obiekt docelowy. Następnie ponownie wczytaj przepływ replikacji.{0}{1} Zmień nazwę obiektu docelowego w przepływie replikacji. To spowoduje utworzenie nowego obiektu w chwili rozpoczęcia wykonania. Po zmianie nazwy możesz zdefiniować kolumny jako klucz główny w projekcji.{0}{1} Przypisz obiekt do innego istniejącego obiektu docelowego, w którym przynajmniej jedną kolumnę zdefiniowano jako klucz główny.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Wybrany obiekt docelowy już istnieje w repozytorium: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Nazwy tabeli rejestrowania delty są już wykorzystywane przez inne tabele w repozytorium: {0}. Przed zapisaniem przepływu replikacji musisz zmienić nazwę tych obiektów docelowych, aby mieć pewność, że powiązane nazwy tabeli rejestrowania delty są unikalne.
#XMSG
validateConfluentEmptySchema=Definiuj schemat
#XMSG
validateConfluentEmptySchemaDescUpdated=Tabela źródłowa nie ma schematu. Wybierz opcję Konfiguruj schemat, aby go zdefiniować.
#XMSG
validationCSVEncoding=Nieprawidłowe kodowanie CSV
#XMSG
validationCSVEncodingDescription=Kodowanie CSV dla zadania jest nieprawidłowe.
#XMSG
validateConfluentEmptySchema=Wybierz kompatybilny docelowy typ danych
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Wybierz kompatybilny docelowy typ danych
#XMSG
globalValidateTargetDataTypeDesc=Wystąpił błąd mapowania kolumn. Przejdź do projekcji i upewnij się, że każda kolumna źródłowa jest mapowana do unikalnej kolumny z kompatybilnym typem danych i że wszystkie zdefiniowane wyrażenia są prawidłowe.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Zduplikowane nazwy kolumn.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Zduplikowane nazwy kolumn nie są obsługiwane. Użyj okna dialogowego projekcji, aby je poprawić. Następujące obiekty docelowe mają zduplikowane nazwy kolumn: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Zduplikowane nazwy kolumn.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Zduplikowane nazwy kolumn nie są obsługiwane. Następujące obiekty docelowe mają zduplikowane nazwy kolumn: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Mogą wystąpić niespójności w danych.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Typ wczytania Tylko delta nie będzie uwzględniał zmian dokonanych w źródle między ostatnim zapisaniem a następnym przebiegiem.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Zmień typ wczytania na "Początkowe".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Replikacja obiektów opartych na ABAP, które nie mają klucza głównego, jest możliwa tylko dla typu wczytania "Tylko początkowe".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Dezaktywuj rejestrowanie delty.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Aby zreplikować obiekt, który nie ma klucza głównego, przy użyciu typu połączenia źródła ABAP, należy najpierw dezaktywować rejestrowanie delty dla tej tabeli.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Zmień obiekt docelowy.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Nie można użyć obiektu docelowego, ponieważ rejestracja delty jest aktywna. Możesz zmienić nazwę obiektu docelowego i wyłączyć rejestrowanie delty dla nowego obiektu (o zmienionej nazwie) lub przypisać obiekt źródłowy do obiektu docelowego z dezaktywowaną rejestracją delty.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Zmień obiekt docelowy.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Nie można użyć obiektu docelowego, ponieważ nie ma on wymaganej kolumny technicznej __load_package_id. Możesz zmienić nazwę obiektu docelowego, używając nazwy, która jeszcze nie istnieje. System utworzy wówczas nowy obiekt z tą samą definicją co obiekt źródłowy i z kolumną techniczną. Alternatywnie możesz przypisać obiekt docelowy do istniejącego obiektu, który ma wymaganą kolumnę techniczną (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Zmień obiekt docelowy.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Nie można użyć obiektu docelowego, ponieważ nie ma on wymaganej kolumny technicznej __load_record_id. Możesz zmienić nazwę obiektu docelowego, używając nazwy, która jeszcze nie istnieje. System utworzy wówczas nowy obiekt z tą samą definicją co obiekt źródłowy i z kolumną techniczną. Alternatywnie możesz przypisać obiekt docelowy do istniejącego obiektu, który ma wymaganą kolumnę techniczną (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Zmień obiekt docelowy.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Nie można użyć obiektu docelowego, ponieważ typ danych jego kolumny technicznej __load_record_id to nie "string(44)". Możesz zmienić nazwę obiektu docelowego, używając nazwy, która jeszcze nie istnieje. System utworzy wówczas nowy obiekt z tą samą definicją co obiekt źródłowy i prawidłowym typem danych. Alternatywnie możesz przypisać obiekt docelowy do istniejącego obiektu, który ma wymaganą kolumnę techniczną (__load_record_id) z prawidłowym typem danych.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Zmień obiekt docelowy.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Nie można użyć obiektu docelowego, ponieważ ma on klucz główny, podczas gdy obiekt źródłowy nie ma. Możesz zmienić nazwę obiektu docelowego, używając nazwy, która jeszcze nie istnieje. System utworzy wówczas nowy obiekt z tą samą definicją co obiekt źródłowy i bez klucza głównego. Alternatywnie możesz przypisać obiekt docelowy do istniejącego obiektu, który ma wymaganą kolumnę techniczną (__load_package_id) i nie ma klucza głównego.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Zmień obiekt docelowy.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Nie można użyć obiektu docelowego, ponieważ ma on klucz główny, podczas gdy obiekt źródłowy nie ma. Możesz zmienić nazwę obiektu docelowego, używając nazwy, która jeszcze nie istnieje. System utworzy wówczas nowy obiekt z tą samą definicją co obiekt źródłowy i bez klucza głównego. Alternatywnie możesz przypisać obiekt docelowy do istniejącego obiektu, który ma wymaganą kolumnę techniczną (__load_record_id) i nie ma klucza głównego.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Zmień obiekt docelowy.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Nie można użyć obiektu docelowego, ponieważ typ danych jego kolumny technicznej __load_package_id to nie "binary(>=256)". Możesz zmienić nazwę obiektu docelowego, używając nazwy, która jeszcze nie istnieje. System utworzy wówczas nowy obiekt z tą samą definicją co obiekt źródłowy i prawidłowym typem danych. Alternatywnie możesz przypisać obiekt docelowy do istniejącego obiektu, który ma wymaganą kolumnę techniczną (__load_package_id) z prawidłowym typem danych.
#XMSG
validationAutoRenameTargetDPID=Zmieniono nazwy kolumn docelowych.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Usuń obiekt źródłowy.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Obiekt źródłowy nie zawiera kolumny klucza, co nie jest obsługiwane w tym kontekście.
#XMSG
validationAutoRenameTargetDPIDDescription=Dodano automatyczną projekcję i zmieniono nazwy następujących kolumn docelowych w celu umożliwienia replikacji ze źródła ABAP bez kluczy:{1}{1} {0} {1}{1}Wynika to z jednego z następujących powodów:{1}{1}{2} Zarezerwowana nazwa kolumny{1}{2} Nieobsługiwane znaki{1}{2} Zarezerwowany prefiks
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Replikacja do {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Zapisywanie i wdrażania przepływów replikacji, które mają {0} jako swoje źródło, jest obecnie niemożliwe z powodu prac utrzymaniowych dot. tej funkcji.
#XMSG
TargetColumnSkippedLTF=Pominięto kolumnę docelową.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Pominięto kolumnę docelową z powodu nieobsługiwanego typu danych. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Kolumna Czas jako klucz główny.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Obiekt źródłowy ma kolumnę czasu jako klucz główny, co nie jest obsługiwane w tym kontekście.
#XMSG
validateNoPKInLTFTarget=Brak klucza głównego.
#XMSG
validateNoPKInLTFTargetDescription=Klucz główny nie jest zdefiniowany w elemencie docelowym, co nie jest obsługiwane w tym kontekście.
#XMSG
validateABAPClusterTableLTF=Tabela klastra ABAP.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Obiekt źródłowy jest tabelą klastra ABAP, co nie jest obsługiwane w tym kontekście.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Wygląda na to, że jeszcze nie dodano danych.
#YINS
welcomeText2=Aby rozpocząć przepływ replikacji, wybierz połączenie oraz obiekt źródłowy po lewej stronie.

#XBUT
wizStep1=Wybierz połączenie źródła
#XBUT
wizStep2=Wybierz kontener źródłowy
#XBUT
wizStep3=Dodaj obiekty źródłowe

#XMSG
limitDataset=Osiągnięto maksymalną liczbę obiektów. Usuń istniejące obiekty, aby dodać nowe lub utwórz nowy przebieg replikacji.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Nie można uruchomić przepływu replikacji do tego połączenia z celem innym niż SAP, ponieważ nie ma dostępnego wolumenu wychodzącego dla tego miesiąca
#XMSG
premiumOutBoundRFAdminWarningMsg=Administrator może zwiększyć bloki kalkulacji dot. wyprowadzania danych dla tego tenanta, udostępniając wolumen wychodzący dla tego miesiąca.
#XMSG
messageForToastForDPIDColumn2=Dodano nową kolumnę do celu dla {0} obiektów - wymagane do obsługi zduplikowanych rekordów w połączeniu z obiektami źródłowymi opartymi na ABAP, które nie mają klucza głównego.
#XMSG
PremiumInboundWarningMessage=W zależności od liczby przepływów replikacji i ilości danych do replikacji zasoby SAP HANA {0}wymagane do replikacji danych przez {1} mogą przekraczać pojemność dostępną dla tenanta.
#XMSG
PremiumInboundWarningMsg=W zależności od liczby przepływów replikacji i ilości danych do replikacji zasoby SAP HANA{0}wymagane do replikacji danych przez "{1}" mogą przekraczać pojemność dostępną dla tenanta.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Wprowadź nazwę projekcji.
#XMSG
emptyTargetColumn=Wprowadź docelową nazwę kolumny.
#XMSG
emptyTargetColumnBusinessName=Wprowadź nazwę biznesową kolumny docelowej.
#XMSG
invalidTransformName=Wprowadź nazwę projekcji.
#XMSG
uniqueColumnName=Zmień nazwę kolumny docelowej.
#XMSG
copySourceColumnLbl=Kopiuj kolumny z obiektu źródłowego
#XMSG
renameWarning=Pamiętaj, aby podczas zmiany nazwy tabeli docelowej wybrać unikatową nazwę. Jeśli tabela o tej nowej nazwie już istnieje w przestrzeni, zostanie użyta definicja tej tabeli.

#XMSG
uniqueColumnBusinessName=Zmień nazwę biznesową kolumny docelowej.
#XMSG
uniqueSourceMapping=Wybierz inną kolumnę źródłową.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=Kolumna źródłowa {0} jest już wykorzystywana przez poniższe kolumny docelowe:{1}{1}{2}{1}{1} Dla tej kolumny docelowej lub dla pozostałych kolumn docelowych wybierz kolumnę źródłową, która nie jest jeszcze używana, aby zapisać projekcję.
#XMSG
uniqueColumnNameDescription=Wprowadzona nazwa kolumny docelowej już istnieje. Aby zapisać projekcję, musisz wprowadzić unikalną nazwą kolumny.
#XMSG
uniqueColumnBusinessNameDesc=Ta nazwa biznesowa kolumny docelowej już istnieje. Aby zapisać projekcję, musisz wprowadzić unikalną nazwę biznesową kolumny.
#XMSG
emptySource=Wybierz kolumnę źródłową lub wprowadź stałą.
#XMSG
emptySourceDescription=Aby utworzyć prawidłowy wpis mapowania, musisz wybrać kolumnę źródłową lub wprowadzić wartość stałą.
#XMSG
emptyExpression=Określ mapowanie.
#XMSG
emptyExpressionDescription1=Wybierz kolumnę źródłową, do której chcesz przypisać kolumnę docelową, lub zaznacz pole wyboru w kolumnie {0} Funkcje / stałe. {1}. {2} {2}Funkcje są wprowadzane automatycznie zgodnie z docelowym typem danych. Wartości stałe można wprowadzać ręcznie.
#XMSG
numberExpressionErr=Wprowadź liczbę.
#XMSG
numberExpressionErrDescription=Wybrano numeryczny typ danych. Oznacza to, że możesz wprowadzić tylko cyfry oraz ewentualnie znak dziesiętny. Nie używaj pojedynczych cudzysłowów.
#XMSG
invalidLength=Wprowadź prawidłową wartość długości.
#XMSG
invalidLengthDescription=Długość typu danych nie może być mniejsza od długości kolumny źródłowej i może mieścić się w przedziale od 1 do 5000.
#XMSG
invalidMappedLength=Wprowadź prawidłową wartość długości.
#XMSG
invalidMappedLengthDescription=Długość typu danych nie może być mniejsza od długości kolumny źródłowej {0} i może mieścić się w przedziale od 1 do 5000.
#XMSG
invalidPrecision=Wprowadź prawidłową wartość dokładności.
#XMSG
invalidPrecisionDescription=Dokładność określa łączną liczbę cyfr. Skala definiuje liczbę cyfr po znaku dziesiętnym i może mieścić się w przedziale od 0 do dokładności.{0}{0} Przykłady: {0}{1} Dokładność 6, skala 2 odnosi się do liczb takich jak 1234.56.{0}{1} Dokładność 6, skala 6 odnosi się do liczb takich jak 0.123546.{0} {0} Dokładność i skala dla celu muszą być zgodne z dokładnością i skalą dla źródła, tak aby wszystkie cyfry ze źródła pasowały do pola docelowego. Na przykład, jeśli w źródle masz dokładność 6 i skalę 2 (i w związku z tym cyfry inne niż 0 przed znakiem dziesiętnym), w celu nie możesz mieć dokładności 6 i skali 6.
#XMSG
invalidPrimaryKey=Wprowadź co najmniej jeden klucz główny.
#XMSG
invalidPrimaryKeyDescription=Nie zdefiniowano klucza głównego dla tego schematu.
#XMSG
invalidMappedPrecision=Wprowadź prawidłową wartość dokładności.
#XMSG
invalidMappedPrecisionDescription1=Dokładność określa łączną liczbę cyfr. Skala definiuje liczbę cyfr po znaku dziesiętnym i może mieścić się w przedziale od 0 do dokładności.{0}{0} Przykłady:{0}{1} Dokładność 6, skala 2 odnosi się do liczb takich jak 1234.56.{0}{1} Dokładność 6, skala 6 odnosi się do liczb takich jak 0.123546.{0}{0}Dokładność typu danych nie może być mniejsza od dokładności źródła ({2}).
#XMSG
invalidScale=Wprowadź prawidłową wartość skali.
#XMSG
invalidScaleDescription=Dokładność określa łączną liczbę cyfr. Skala definiuje liczbę cyfr po znaku dziesiętnym i może mieścić się w przedziale od 0 do dokładności.{0}{0} Przykłady: {0}{1} Dokładność 6, skala 2 odnosi się do liczb takich jak 1234.56.{0}{1} Dokładność 6, skala 6 odnosi się do liczb takich jak 0.123546.{0} {0} Dokładność i skala dla celu muszą być zgodne z dokładnością i skalą dla źródła, tak aby wszystkie cyfry ze źródła pasowały do pola docelowego. Na przykład, jeśli w źródle masz dokładność 6 i skalę 2 (i w związku z tym cyfry inne niż 0 przed znakiem dziesiętnym), w celu nie możesz mieć dokładności 6 i skali 6.
#XMSG
invalidMappedScale=Wprowadź prawidłową wartość skali.
#XMSG
invalidMappedScaleDescription1=Dokładność określa łączną liczbę cyfr. Skala definiuje liczbę cyfr po znaku dziesiętnym i może mieścić się w przedziale od 0 do dokładności.{0}{0} Przykłady:{0}{1} Dokładność 6, skala 2 odnosi się do liczb takich jak 1234.56.{0}{1} Dokładność 6, skala 6 odnosi się do liczb takich jak 0.123546.{0}{0}Skala typu danych nie może być mniejsza od skali źródła ({2}).
#XMSG
nonCompatibleDataType=Wybierz kompatybilny docelowy typ danych.
#XMSG
nonCompatibleDataTypeDescription1=Typ danych wskazany w tym miejscu musi być zgodny ze źródłowym typem danych ({0}). {1}{1} Przykład: Jeśli kolumna źródłowa ma typ danych STRING i zawiera litery, nie możesz użyć typu danych DECIMAL dla celu.
#XMSG
invalidColumnCount=Wybierz kolumnę źródłową.
#XMSG
ObjectStoreInvalidScaleORPrecision=Wprowadź prawidłową wartość dla dokładności i skali.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=Pierwsza wartość to dokładność, która określa łączną liczbę cyfr. Druga wartość to skala, która określa liczbę cyfr po znaku dziesiętnym. Wprowadź docelową wartość skali, która jest większa od źródłowej wartości skali, i upewnij się, że różnica między wprowadzoną docelową wartością skali i dokładności jest większa niż różnica między źródłową wartością skali i dokładności.
#XMSG
InvalidPrecisionORScale=Wprowadź prawidłową wartość dla dokładności i skali.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=Pierwsza wartość to dokładność, która definiuje łączną liczbę cyfr. Druga wartość to skala, która definiuje cyfry po znaku dziesiętnym.{0}{0}Ponieważ źródłowy typ danych nie jest obsługiwany w Google BigQuery, jest on konwertowany na docelowy typ danych DECIMAL. W tym przypadku dokładność można zdefiniować w zakresie od 38 do 76, a skalę pomiędzy 9 a 38. Ponadto wynik dokładności minus skala, co reprezentuje cyfry przed znakiem dziesiętnym, musi się zawierać pomiędzy 29 a 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=Pierwsza wartość to dokładność, która definiuje łączną liczbę cyfr. Druga wartość to skala, która definiuje cyfry po znaku dziesiętnym.{0}{0}Ponieważ źródłowy typ danych nie jest obsługiwany w Google BigQuery, jest on konwertowany na docelowy typ danych DECIMAL. W tym przypadku dokładność należy zdefiniować jako 20 lub większą. Ponadto wynik dokładności minus skala, co reprezentuje cyfry przed znakiem dziesiętnym, musi wynosić 20 lub więcej.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=Pierwsza wartość to dokładność, która definiuje łączną liczbę cyfr. Druga wartość to skala, która definiuje cyfry po znaku dziesiętnym.{0}{0}Ponieważ źródłowy typ danych nie jest obsługiwany w rozwiązaniu docelowym, jest on konwertowany na docelowy typ danych DECIMAL. W tym przypadku dokładność należy zdefiniować jako dowolną liczbę nie mniejszą niż 1 i nie większą niż 38, a skalę nie większą niż dokładność.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=Pierwsza wartość to dokładność, która definiuje łączną liczbę cyfr. Druga wartość to skala, która definiuje cyfry po znaku dziesiętnym.{0}{0}Ponieważ źródłowy typ danych nie jest obsługiwany w rozwiązaniu docelowym, jest on konwertowany na docelowy typ danych DECIMAL. W tym przypadku dokładność należy zdefiniować jako 20 lub większą. Ponadto wynik dokładności minus skala, co reprezentuje cyfry przed znakiem dziesiętnym, musi wynosić 20 lub więcej.
#XMSG
invalidColumnCountDescription=Aby utworzyć prawidłowy wpis mapowania, musisz wybrać kolumnę źródłową lub wprowadzić wartość stałą.
#XMSG
duplicateColumns=Zmień nazwę kolumny docelowej.
#XMSG
duplicateGBQCDCColumnsDesc=Nazwa kolumny docelowej jest zarezerwowana w Google BigQuery. Musisz ją zmienić, aby zapisać projekcję.
#XMSG
duplicateConfluentCDCColumnsDesc=Nazwa kolumny docelowej jest zarezerwowana w Confluent. Musisz ją zmienić, aby zapisać projekcję.
#XMSG
duplicateSignavioCDCColumnsDesc=Nazwa kolumny docelowej jest zarezerwowana w SAP Signavio. Musisz ją zmienić, aby zapisać projekcję.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Nazwa kolumny docelowej jest zarezerwowana w Microsoft OneLake. Musisz ją zmienić, aby móc zapisać projekcję.
#XMSG
duplicateSFTPCDCColumnsDesc=Nazwa kolumny docelowej jest zarezerwowana w SFTP. Musisz ją zmienić, aby móc zapisać projekcję.
#XMSG
GBQTargetNameWithPrefixUpdated1=Nazwa kolumny docelowej zawiera prefiks zarezerwowany w Google BigQuery. Musisz zmienić tę nazwę, aby zapisać projekcję. {0}{0}Nazwa kolumny docelowej nie może zaczynać się żadnym z następujących ciągów:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Skróć nazwę kolumny docelowej.
#XMSG
GBQtargetMaxLengthDesc=W Google BigQuery nazwa kolumny może się składać z maksymalnie 300 znaków. Skróć nazwę kolumny docelowej, aby zapisać projekcję.
#XMSG
invalidMappedScalePrecision=Dokładność i skala dla celu muszą być zgodne z dokładnością i skalą dla źródła, tak aby wszystkie cyfry ze źródła pasowały do pola docelowego.
#XMSG
invalidMappedScalePrecisionShortText=Wprowadź prawidłową wartość dokładności i skali.
#XMSG
validationIncompatiblePKTypeDescProjection3=Co najmniej jedna kolumna źródłowa ma typy danych, których nie można zdefiniować jako klucze główne w Google BigQuery. W obiekcie docelowym nie zostanie wygenerowany żaden klucz główny.{0}{0}Poniższe typy danych docelowych są zgodne z typami danych Google BigQuery, dla których można zdefiniować klucz główny: {1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Usuń zaznaczenie kolumny __message_id.
#XMSG
uncheckColumnMessageIdDesc=Kolumna: Klucz podstawowy
#XMSG
validationOpCodeInsert=Należy wprowadzić wartość do wstawienia.
#XMSG
recommendDifferentPrimaryKey=Zalecamy wybór innego klucza głównego na poziomie pozycji.
#XMSG
recommendDifferentPrimaryKeyDesc=Kiedy kod operacji jest już określony, zalecane jest wybranie innych kluczy głównych dla indeksu tablicy i dla pozycji, aby uniknąć problemów, takich jak np. duplikacja kolumny. 
#XMSG
selectPrimaryKeyItemLevel=Musisz wybrać co najmniej jeden klucz główny zarówno na poziomie nagłówka, jak i na poziomie pozycji.
#XMSG
selectPrimaryKeyItemLevelDesc=Kiedy tablica lub mapa jest rozwinięta, trzeba wybrać dwa klucze główne - jeden na poziomie nagłówka i jeden na poziomie pozycji.
#XMSG
invalidMapKey=Musisz wybrać co najmniej jeden klucz główny na poziomie nagłówka.
#XMSG
invalidMapKeyDesc=Kiedy tablica lub mapa jest rozwinięta, trzeba wybrać klucz główny na poziomie nagłówka.
#XFLD
txtSearchFields=Wyszukaj kolumny docelowe
#XFLD
txtName=Nazwa
#XMSG
txtSourceColValidation=Co najmniej jedna kolumna źródłowa nie jest obsługiwana:
#XMSG
txtMappingCount=Przypisania ({0})
#XMSG
schema=Schemat
#XMSG
sourceColumn=Kolumny źródłowe
#XMSG
warningSourceSchema=Wszelkie zmiany wprowadzone w schemacie będą miały wpływ na mapowania w oknie dialogowym projekcji.
#XCOL
txtTargetColName=Kolumna docelowa (nazwa techniczna)
#XCOL
txtDataType=Docelowy typ danych
#XCOL
txtSourceDataType=Typ danych źródłowych
#XCOL
srcColName=Kolumna źródłowa (nazwa techniczna)
#XCOL
precision=Dokładność
#XCOL
scale=Skala
#XCOL
functionsOrConstants=Funkcje / stałe
#XCOL
txtTargetColBusinessName=Kolumna docelowa (nazwa biznesowa)
#XCOL
prKey=Klucz podstawowy
#XCOL
txtProperties=Właściwości
#XBUT
txtOK=Zapisz
#XBUT
txtCancel=Anuluj
#XBUT
txtRemove=Usuń
#XFLD
txtDesc=Opis
#XMSG
rftdMapping=Mapowanie
#XFLD
@lblColumnDataType=Typ danych
#XFLD
@lblColumnTechnicalName=Nazwa techniczna
#XBUT
txtAutomap=Mapuj automatycznie
#XBUT
txtUp=W górę
#XBUT
txtDown=W dół

#XTOL
txtTransformationHeader=Projekcja
#XTOL
editTransformation=Edytuj
#XTOL
primaryKeyToolip=Klucz


#XMSG
rftdFilter=Filtruj
#XMSG
rftdFilterColumnCount=Źródło: {0}({1})
#XTOL
rftdFilterColSearch=Wyszukaj
#XMSG
rftdFilterColNoData=Brak kolumn do wyświetlenia
#XMSG
rftdFilteredColNoExps=Brak wyrażeń filtra
#XMSG
rftdFilterSelectedColTxt=Dodaj filtr dla
#XMSG
rftdFilterTxt=Filtr dostępny dla
#XBUT
rftdFilterSelectedAddColExp=Dodaj wyrażenie
#YINS
rftdFilterNoSelectedCol=Wybierz kolumnę, aby dodać filtr
#XMSG
rftdFilterExp=Wyrażenie filtra
#XMSG
rftdFilterNotAllowedColumn=Dodawanie filtrów nie jest obsługiwane dla tej kolumny.
#XMSG
rftdFilterNotAllowedHead=Nieobsługiwana kolumna
#XMSG
rftdFilterNoExp=Nie zdefiniowano filtra
#XTOL
rftdfilteredTt=Odfiltrowane
#XTOL
rftdremoveexpTt=Usuń wyrażenie filtra
#XTOL
validationMessageTt=Komunikaty walidacji
#XTOL
rftdFilterDateInp=Wybierz datę
#XTOL
rftdFilterDateTimeInp=Wybierz datę i godzinę
#XTOL
rftdFilterTimeInp=Wybierz godzinę
#XTOL
rftdFilterInp=Wpisz wartość
#XMSG
rftdFilterValidateEmptyMsg=Wyrażenia filtra {0} dla kolumny {1} są puste
#XMSG
rftdFilterValidateInvalidNumericMsg=Wyrażenia filtra {0} dla kolumny {1} zawierają nieprawidłowe wartości numeryczne
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Wyrażenie filtra musi zawierać prawidłowe wartości numeryczne
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Jeśli schemat obiektu docelowego został zmieniony, użyj funkcji “Przypisz do istniejącego obiektu docelowego” na stronie głównej, aby dostosować zmiany i ponownie przypisać obiekt docelowy do jego źródła.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Jeśli tabela docelowa już istnieje, a mapowanie obejmuje zmianę schematu, musisz odpowiednio zmienić tabelę docelową przed wdrożeniem przepływu replikacji.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Jeśli mapowanie obejmuje zmianę schematu, musisz odpowiednio zmienić tabelę docelową przed wdrożeniem przepływu replikacji.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Poniższe nieobsługiwane kolumny zostały pominięte w definicji źródłowej: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Poniższe nieobsługiwane kolumny zostały pominięte w definicji docelowej: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Następujące obiekty nie są obsługiwane, ponieważ zostały udostępnione do wykorzystania: {0} {1} {0} {0} Aby tabele mogły być użyte w przepływie replikacji, wykorzystanie semantyczne (w ustawieniach tabeli) nie może być ustawione na {2}Analityczny zbiór danych{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Obiekt docelowy nie może zostać użyty, ponieważ został udostępniony do wykorzystania: {0} {0} Aby tabela mogła być użyta w przepływie replikacji, wykorzystanie semantyczne (w ustawieniach tabeli) nie może być ustawione na {1}Analityczny zbiór danych{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Obiekt docelowy o tej nazwie już istnieje. Jednak nie może zostać użyty, ponieważ został udostępniony do wykorzystania: {0} {0} Aby tabela mogła być użyta w przepływie replikacji, wykorzystanie semantyczne (w ustawieniach tabeli) nie może być ustawione na {1}Analityczny zbiór danych{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Obiekt o tej nazwie już istnieje w miejscu docelowym. {0}Nie można go użyć jako obiektu docelowego dla przepływu replikacji do lokalnego repozytorium, ponieważ nie jest to tabela lokalna.
#XMSG:
targetAutoRenameUpdated=Zmieniono nazwę kolumny docelowej.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Zmieniono nazwę kolumny docelowej, aby umożliwić replikacje w Google BigQuery. Wynika to z jednego z następujących powodów:{0} {1}{2}Zarezerwowana nazwa kolumny{3}{2}Nieobsługiwane znaki{3}{2}Zarezerwowany prefiks{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Zmieniono nazwę kolumny docelowej, aby umożliwić replikacje w Confluent. Wynika to z jednego z następujących powodów:{0} {1}{2}Zarezerwowana nazwa kolumny{3}{2}Nieobsługiwane znaki{3}{2}Zarezerwowany prefiks{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Zmieniono nazwę kolumny docelowej, aby umożliwić replikacje w rozwiązaniu docelowym. Wynika to z jednego z następujących powodów:{0} {1}{2}Nieobsługiwane znaki{3}{2}Zarezerwowany prefiks{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Zmieniono nazwę kolumny docelowej, aby umożliwić replikacje w rozwiązaniu docelowym. Wynika to z jednego z następujących powodów:{0} {1}{2}Zarezerwowana nazwa kolumny{3}{2}Nieobsługiwane znaki{3}{2}Zarezerwowany prefiks{3}{4}
#XMSG:
targetAutoDataType=Zmieniono docelowy typ danych.
#XMSG:
targetAutoDataTypeDesc=Docelowy typ danych zmieniono na {0}, ponieważ źródłowy typ danych nie jest obsługiwany w Google BigQuery.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Docelowy typ danych zmieniono na {0}, ponieważ źródłowy typ danych nie jest obsługiwany w połączeniu docelowym.
#XMSG
projectionGBQUnableToCreateKey=Kucze główne nie zostaną utworzone.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=W Google BigQuery obsługiwanych jest maksymalnie 16 kluczy głównych, ale obiekt źródłowy zawiera więcej niż 16 kluczy głównych. Żaden z kluczy głównych nie zostanie utworzony w obiekcie docelowym.
#XMSG
HDLFNoKeyError=Zdefiniuj co najmniej jedną kolumnę jako klucz główny.
#XMSG
HDLFNoKeyErrorDescription=Aby zreplikować obiekt, musisz zdefiniować co najmniej jedną kolumnę docelową jako klucz główny.
#XMSG
HDLFNoKeyErrorExistingTarget=Zdefiniuj co najmniej jedną kolumnę jako klucz główny.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Aby dane mogły zostać zreplikowane do istniejącego obiektu docelowego, muszą zawierać co najmniej jedną kolumnę zdefiniowaną jako klucz główny. {0} {0} Dostępne są następujące opcje definiowania jednej lub kilku kolumn jako klucza głównego: {0}{0}{1} Użyj edytora tabeli lokalnych, aby zmienić istniejący obiekt docelowy. Następnie ponownie wczytaj przepływ replikacji.{0}{0}{1} Zmień nazwę obiektu docelowego w przepływie replikacji. To spowoduje utworzenie nowego obiektu w chwili rozpoczęcia wykonania. Po zmianie nazwy możesz zdefiniować kolumny jako klucz główny w projekcji.{0}{0}{1} Przypisz obiekt do innego istniejącego obiektu docelowego, w którym przynajmniej jedną kolumnę zdefiniowano jako klucz główny.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Zmieniono klucz główny.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=Zdefiniowano inne kolumny jako klucz główny dla obiektu docelowego niż w obiekcie źródłowym. Upewnij się, że te kolumny w unikalny sposób identyfikują wszystkie wiersze, aby uniknąć uszkodzenia danych podczas późniejszej replikacji danych. {0} {0} W obiekcie źródłowym następujące kolumny zdefiniowano jako klucz główny: {0} {1}
#XMSG
duplicateDPIDColumns=Zmień nazwę kolumny docelowej.
#XMSG
duplicateDPIDDColumnsDesc1=Ta nazwa kolumny docelowej jest zarezerwowana dla kolumny technicznej. Wprowadź inną nazwę, aby zapisać projekcję.
#XMSG:
targetAutoRenameDPID=Zmieniono nazwę kolumny docelowej.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Zmieniono nazwę kolumny docelowej, aby umożliwić replikacje ze źródła ABAP bez kluczy. Wynika to z jednego z następujących powodów:{0} {1}{2}Zarezerwowana nazwa kolumny{3}{2}Nieobsługiwane znaki{3}{2}Zarezerwowany prefiks{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle=Ustawienia docelowe {0}
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle=Ustawienia źródłowe {0}
#XBUT
connectionSettingSave=Zapisz
#XBUT
connectionSettingCancel=Anuluj
#XBUT: Button to keep the object level settings
txtKeep=Zachowaj
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Nadpisz
#XFLD
targetConnectionThreadlimit=Limit wątków docelowych dla początkowego wczytania (1-100)
#XFLD
connectionThreadLimit=Limit wątków źródłowych dla początkowego wczytania (1-100)
#XFLD
maxConnection=Limit wątków replikacji (1-100)
#XFLD
kafkaNumberOfPartitions=Liczba partycji
#XFLD
kafkaReplicationFactor=Współczynnik replikacji
#XFLD
kafkaMessageEncoder=Program kodujący wiadomości
#XFLD
kafkaMessageCompression=Kompresja wiadomości
#XFLD
fileGroupDeltaFilesBy=Grupowanie delta według
#XFLD
fileFormat=Typ pliku
#XFLD
csvEncoding=Kodowanie CSV
#XFLD
abapExitLbl=Zamknięcie ABAP
#XFLD
deltaPartition=Liczba wątków obiektu dla wczytań delty (1-10)
#XFLD
clamping_Data=Błąd z powodu obcięcia danych
#XFLD
fail_On_Incompatible=Błąd z powodu niezgodnych danych
#XFLD
maxPartitionInput=Maksymalna liczba partycji
#XFLD
max_Partition=Zdefiniuj maksymalną liczbę partycji
#XFLD
include_SubFolder=Uwzględnij foldery podrzędne
#XFLD
fileGlobalPattern=Globalny wzorzec dla nazwy pliku
#XFLD
fileCompression=Kompresja pliku
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Separator pliku
#XFLD
fileIsHeaderIncluded=Nagłówek pliku
#XFLD
fileOrient=Orientacja
#XFLD
gbqWriteMode=Tryb zapisu
#XFLD
suppressDuplicate=Ukryj duplikaty
#XFLD
apacheSpark=Aktywuj kompatybilność Apache Spark
#XFLD
clampingDatatypeCb=Ogranicz dziesiętne zmiennoprzecinkowe typy danych
#XFLD
overwriteDatasetSetting=Nadpisz ustawienia docelowe na poziomie obiektu
#XFLD
overwriteSourceDatasetSetting=Nadpisz ustawienia źródłowe na poziomie obiektu
#XMSG
kafkaInvalidConnectionSetting=Wprowadź liczbę w przedziale od {0} do {1}
#XMSG
MinReplicationThreadErrorMsg=Wprowadź liczbę większą niż {0}.
#XMSG
MaxReplicationThreadErrorMsg=Wprowadź liczbę mniejszą niż {0}.
#XMSG
DeltaThreadErrorMsg=Wprowadź wartość z zakresu od 1 do 10.
#XMSG
MaxPartitionErrorMsg=Wprowadź wartość z zakresu 1 <= x <= 2147483647.Wartość domyślna to 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Wprowadź liczbę całkowitą w przedziale od {0} do {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Użyj współczynnika replikacji brokera
#XFLD
serializationFormat=Format serializacji
#XFLD
compressionType=Typ kompresji
#XFLD
schemaRegistry=Użyj rejestru schematów
#XFLD
subjectNameStrat=Strategia nazw podmiotu
#XFLD
compatibilityType=Typ kompatybilności
#XFLD
confluentTopicName=Nazwa tematu
#XFLD
confluentRecordName=Nazwa rekordu
#XFLD
confluentSubjectNamePreview=Podgląd nazwy podmiotu
#XMSG
serializationChangeToastMsgUpdated2=Format serializacji został zmieniony na JSON, ponieważ nie aktywowano rejestru schematów. Aby zmienić format serializacji z powrotem na AVRO, należy najpierw aktywować rejestr schematów.
#XBUT
confluentTopicNameInfo=Nazwa tematu zawsze bazuje na nazwie obiektu docelowego. Możesz ją zmienić poprzez zmianę nazwy obiektu docelowego.
#XMSG
emptyRecordNameValidationHeaderMsg=Wprowadź nazwę rekordu.
#XMSG
emptyPartionHeader=Wprowadź liczbę partycji.
#XMSG
invalidPartitionsHeader=Wprowadź prawidłową liczbę partycji.
#XMSG
invalidpartitionsDesc=Wprowadź liczbę z przedziału od 1 do 200 000.
#XMSG
emptyrFactorHeader=Wprowadź współczynnik replikacji.
#XMSG
invalidrFactorHeader=Wprowadź prawidłowy współczynnik replikacji.
#XMSG
invalidrFactorDesc=Wprowadź liczbę z przedziału od 1 do 32 767.
#XMSG
emptyRecordNameValidationDescMsg=W przypadku stosowania formatu serializacji "AVRO" obsługiwane są tylko następujące znaki:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(podkreślenie)
#XMSG
validRecordNameValidationHeaderMsg=Wprowadź prawidłową nazwę rekordu.
#XMSG
validRecordNameValidationDescMsgUpdated=Jako że używany jest format serializacji "AVRO", nazwa rekordu musi składać się tylko ze znaków alfanumerycznych (A-Z, a-z, 0-9) i podkreśleń (_) oraz zaczynać się od litery lub podkreślenia.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=Wartość "Liczba wątków obiektu dla wczytań delty" można ustawić, kiedy co najmniej jeden obiekt ma typ wczytania "Początkowe i delta".
#XMSG
invalidTargetName=Nieprawidłowa nazwa kolumny
#XMSG
invalidTargetNameDesc=Nazwa kolumny docelowej musi składać się tylko ze znaków alfanumerycznych (A-Z, a-z, 0-9) i podkreśleń (_).
#XFLD
consumeOtherSchema=Wykorzystaj inne wersje schematu
#XFLD
ignoreSchemamissmatch=Ignoruj niezgodność schematu
#XFLD
confleuntDatatruncation=Błąd obcinania danych
#XFLD
isolationLevel=Poziom izolacji
#XFLD
confluentOffset=Punkt początkowy
#XFLD
signavioGroupDeltaFilesByText=Brak
#XFLD
signavioFileFormatText=Parkiet
#XFLD
signavioSparkCompatibilityParquetText=Nie
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=Nie

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Projekcje
#XBUT
txtAdd=Dodaj
#XBUT
txtEdit=Edytuj
#XMSG
transformationText=Dodaj projekcję, aby skonfigurować filtr lub mapowanie.
#XMSG
primaryKeyRequiredText=Wybierz klucz główny z opcją Konfiguruj schemat.
#XFLD
lblSettings=Ustawienia
#XFLD
lblTargetSetting={0}: ustawienia docelowe
#XMSG
@csvRF=Wybierz plik zawierający definicję schematu, którą chcesz zastosować do wszystkich plików w folderze.
#XFLD
lblSourceColumns=Kolumny źródłowe
#XFLD
lblJsonStructure=Struktura JSON
#XFLD
lblSourceSetting={0}: ustawienia źródłowe
#XFLD
lblSourceSchemaSetting={0}: ustawienia źródłowe schematu
#XBUT
messageSettings=Ustawienia komunikatów
#XFLD
lblPropertyTitle1=Właściwości obiektu
#XFLD
lblRFPropertyTitle=Właściwości przepływu replikacji
#XMSG
noDataTxt=Brak kolumn do wyświetlenia.
#XMSG
noTargetObjectText=Nie wybrano obiektu docelowego.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Kolumny docelowe
#XMSG
searchColumns=Wyszukaj kolumny
#XTOL
cdcColumnTooltip=Kolumna dla rejestrowania delty
#XMSG
sourceNonDeltaSupportErrorUpdated=Obiekt źródłowy nie obsługuje rejestrowania delty.
#XMSG
targetCDCColumnAdded=Dodano 2 kolumny docelowe dla rejestrowania delty.
#XMSG
deltaPartitionEnable=Limit wątków obiektu dla wczytań delty dodano do ustawień źródłowych.
#XMSG
attributeMappingRemovalTxt=Usuwanie nieprawidłowych mapowań, które nie są obsługiwane dla nowego obiektu docelowego.
#XMSG
targetCDCColumnRemoved=2 kolumny docelowe wykorzystane dla rejestrowania delty zostały usunięte.
#XMSG
replicationLoadTypeChanged=Typ wczytania zmieniono na "Początkowe i delta".
#XMSG
sourceHDLFLoadTypeError=Zmień typ wczytania na "Początkowe i delta".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Aby zreplikować obiekt z połączenia źródłowego z typem połączenia SAP HANA Cloud, Data Lake Files do SAP Datasphere, musisz użyć typu wczytania "Początkowe i delta".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Aktywuj rejestrowanie delty.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Aby zreplikować obiekt z połączenia źródłowego z typem połączenia SAP HANA Cloud, Data Lake Files do SAP Datasphere, musisz aktywować rejestrację delty.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Zmień obiekt docelowy.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Nie można użyć obiektu docelowego, ponieważ rejestracja delty nie jest aktywna. Możesz zmienić nazwę obiektu docelowego (co umożliwia utworzenie nowego obiektu z rejestracją delty) lub przypisać ją do istniejącego obiektu z aktywowaną rejestracją delty.
#XMSG
deltaPartitionError=Wprowadź prawidłową liczbę wątków obiektu dla wczytań delty.
#XMSG
deltaPartitionErrorDescription=Wprowadź wartość z zakresu od 1 do 10.
#XMSG
deltaPartitionEmptyError=Wprowadź liczbę wątków obiektu dla wczytań delty.
#XFLD
@lblColumnDescription=Opis
#XMSG
@lblColumnDescriptionText1=Do celów technicznych - obsługa zduplikowanych rekordów spowodowała problemy podczas replikacji obiektów źródłowych opartych na ABAP, które nie mają klucza głównego.
#XFLD
storageType=Pamięć
#XFLD
skipUnmappedColLbl=Pomiń nieprzypisane kolumny
#XFLD
abapContentTypeLbl=Typ zawartości
#XFLD
autoMergeForTargetLbl=Połącz dane automatycznie
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Ogólne
#XFLD
lblBusinessName=Nazwa biznesowa
#XFLD
lblTechnicalName=Nazwa techniczna
#XFLD
lblPackage=Pakiet
#XFLD
statusPanel=Status uruchomienia
#XBTN: Schedule dropdown menu
SCHEDULE=Harmonogram
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Edytuj harmonogram
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Usuń harmonogram
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Utwórz harmonogram
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Kontrola walidacji harmonogramu nie powiodła się
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Nie można utworzyć harmonogramu, ponieważ przepływ replikacji jest obecnie wdrażany.{0}Poczekaj na zakończenie wdrażania przepływu replikacji.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=W przypadku przepływów replikacji zawierających obiekty z typem wczytania "Początkowe i delta" nie można utworzyć harmonogramu.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=W przypadku przepływów replikacji zawierających obiekty z typem wczytania "Początkowe i delta/Tylko delta" nie można utworzyć harmonogramu.
#XFLD : Scheduled popover
SCHEDULED=Zaplanowane
#XFLD
CREATE_REPLICATION_TEXT=Utwórz przepływ replikacji
#XFLD
EDIT_REPLICATION_TEXT=Edytuj przepływ replikacji
#XFLD
DELETE_REPLICATION_TEXT=Usuń przepływ replikacji
#XFLD
REFRESH_FREQUENCY=Częstotliwość
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Przepływu replikacji nie można wdrożyć, ponieważ istniejący harmonogram{0} nie obsługuje jeszcze typu wczytania "Początkowe i delta".{0}{0}Aby wdrożyć przepływ replikacji, musisz ustawić typy wczytywania wszystkich obiektów{0} na "Tylko początkowe". Alternatywnie możesz usunąć harmonogram oraz przepływ replikacji{0}, a następnie rozpocząć nowy przebieg. Będzie to skutkować przebiegiem bez końca{0}, który obsługuje również obiekty z typem wczytania "Początkowe i delta".
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Przepływu replikacji nie można wdrożyć, ponieważ istniejący harmonogram{0} nie obsługuje jeszcze typu wczytania "Początkowe i delta/Tylko delta".{0}{0}Aby wdrożyć przepływ replikacji, musisz ustawić typy wczytywania wszystkich obiektów{0} na "Tylko początkowe". Alternatywnie możesz usunąć harmonogram oraz przepływ replikacji{0}, a następnie rozpocząć nowy przebieg. Będzie to skutkować przebiegiem bez końca{0}, który obsługuje również obiekty z typem wczytania "Początkowe i delta/Tylko delta".
#XMSG
SCHEDULE_EXCEPTION=Wywołanie szczegółów harmonogramu nie powiodło się
#XFLD: Label for frequency column
everyLabel=Co
#XFLD: Plural Recurrence text for Hour
hoursLabel=godz.
#XFLD: Plural Recurrence text for Day
daysLabel=dni
#XFLD: Plural Recurrence text for Month
monthsLabel=mies.
#XFLD: Plural Recurrence text for Minutes
minutesLabel=min
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Nie udało się pobrać informacji dotyczących możliwości harmonogramowania.
#XFLD :Paused field
PAUSED=Wstrzymane
#XMSG
navToMonitoring=Otwórz w monitorze przepływów
#XFLD
statusLbl=Status
#XFLD
lblLastRunExecuted=Rozpoczęcie ostatniego przebiegu
#XFLD
lblLastExecuted=Ostatni przebieg
#XFLD: Status text for Completed
statusCompleted=Zakończone
#XFLD: Status text for Running
statusRunning=Aktywne
#XFLD: Status text for Failed
statusFailed=Niepowodzenie
#XFLD: Status text for Stopped
statusStopped=Zatrzymane
#XFLD: Status text for Stopping
statusStopping=Zatrzymywanie
#XFLD: Status text for Active
statusActive=Aktywne
#XFLD: Status text for Paused
statusPaused=Wstrzymane
#XFLD: Status text for not executed
lblNotExecuted=Jeszcze nie uruchomiono
#XFLD
messagesSettings=Ustawienia komunikatów
#XTOL
@validateModel=Komunikaty walidacji
#XTOL
@hierarchy=Hierarchia
#XTOL
@columnCount=Liczba kolumn
#XMSG
VAL_PACKAGE_CHANGED=Ten obiekt został przypisany do pakietu "{1}". Kliknij opcję "Zapisz", aby potwierdzić tę zmianę i przeprowadzić jej walidację. Zauważ, że po zapisaniu nie będzie można cofnąć przypisania do pakietu w tym edytorze.
#XMSG
MISSING_DEPENDENCY=Zależności obiektu "{0}" nie mogą zostać rozwinięte w pakiecie "{1}".
#XFLD
deltaLoadInterval=Przedział pobrania danych delta
#XFLD
lblHour=Godziny (0-24)
#XFLD
lblMinutes=Minuty (0-59)
#XMSG
maxHourOrMinErr=Wprowadź wartość z zakresu od 0 do {0}.
#XMSG
maxDeltaInterval=Wartość maksymalna przedziału pobrania danych to 24 godziny.{0}Zmień odpowiednio wartość minut lub wartość godzin.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Ścieżka kontenera docelowego
#XFLD
confluentSubjectName=Nazwa podmiotu
#XFLD
confluentSchemaVersion=Wersja schematu
#XFLD
confluentIncludeTechKeyUpdated=Uwzględnij klucz techniczny
#XFLD
confluentOmitNonExpandedArrays=Pomiń nierozwinięte tablice
#XFLD
confluentExpandArrayOrMap=Rozwiń tablicę lub mapę
#XCOL
confluentOperationMapping=Mapowanie operacji
#XCOL
confluentOpCode=Kod operacji
#XFLD
confluentInsertOpCode=Wstaw
#XFLD
confluentUpdateOpCode=Aktualizuj
#XFLD
confluentDeleteOpCode=Usuń
#XFLD
expandArrayOrMapNotSelectedTxt=Nie wybrano
#XFLD
confluentSwitchTxtYes=Tak
#XFLD
confluentSwitchTxtNo=Nie
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Błąd
#XTIT
executeWarning=Ostrzeżenie
#XMSG
executeunsavederror=Zapisz przepływ replikacji przed jego uruchomieniem.
#XMSG
executemodifiederror=Istnieją niezapisane zmiany w przepływie replikacji. Zapisz przepływ replikacji.
#XMSG
executeundeployederror=Musisz wdrożyć przepływ replikacji, aby można było je wykonać.
#XMSG
executedeployingerror=Zaczekaj na zakończenie wdrażania.
#XMSG
msgRunStarted=Uruchomiono przebieg
#XMSG
msgExecuteFail=Uruchomienie przepływu replikacji nie powiodło się
#XMSG
titleExecuteBusy=Czekaj.
#XMSG
msgExecuteBusy=Przygotowujemy dane do uruchomienia przepływu replikacji.
#XTIT
executeConfirmDialog=Ostrzeżenie
#XMSG
msgExecuteWithValidations=Przepływ replikacji zawiera błędy walidacji. Uruchomienie przepływu replikacji może skutkować niepowodzeniem.
#XMSG
msgRunDeployedVersion=Istnieją zmiany wymagające wdrożenia. Uruchomiona zostanie ostatnia wdrożona wersja przepływu replikacji. Czy chcesz kontynuować?
#XBUT
btnExecuteAnyway=Uruchom mimo to
#XBUT
btnExecuteClose=Zamknij
#XBUT
loaderClose=Zamknij
#XTIT
loaderTitle=Wczytywanie
#XMSG
loaderText=Pobieranie szczegółów z serwera
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Nie można uruchomić przepływu replikacji do tego połączenia z celem innym niż SAP,
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=ponieważ nie ma dostępnego wolumenu wychodzącego dla tego miesiąca.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Administrator może zwiększyć bloki kalkulacji dot. wyprowadzania danych dla tego
#XMSG
premiumOutBoundRFAdminErrMsgPart2=tenanta, udostępniając wolumen wychodzący dla tego miesiąca.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Błąd
#XTIT
deployInfo=Informacje
#XMSG
deployCheckFailException=Wystąpił wyjątek podczas wdrażania
#XMSG
deployGBQFFDisabled=Wdrożenie przepływów replikacji z połączeniem docelowym z Google BigQuery jest obecnie niemożliwe z powodu prac utrzymaniowych dot. tej funkcji.
#XMSG
deployKAFKAFFDisabled=Wdrożenie przepływów replikacji z połączeniem docelowym z Apache Kafka jest obecnie niemożliwe z powodu prac utrzymaniowych dot. tej funkcji.
#XMSG
deployConfluentDisabled=Wdrożenie przepływów replikacji z połączeniem docelowym z Confluent Kafka jest obecnie niemożliwe z powodu prac serwisowych dot. tej funkcji.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Dla poniższych obiektów docelowych nazwy tabeli rejestrowania delty są już wykorzystywane przez inne tabele w repozytorium: {0} Przed wdrożeniem przepływu replikacji musisz zmienić nazwę tych obiektów docelowych, aby mieć pewność, że powiązane nazwy tabeli rejestrowania delty są unikalne.
#XMSG
deployDWCSourceFFDisabled=Wdrożenie przepływów replikacji z SAP Datasphere jako źródłem jest obecnie niemożliwe z powodu prac utrzymaniowych dot. tej funkcji.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Wdrożenie przepływów replikacji, które zawierają tabele lokalne kompatybilne z deltą jako obiekty źródłowe, jest obecnie niemożliwe z powodu prac serwisowych dotyczących tej funkcji.
#XMSG
deployHDLFSourceFFDisabled=Wdrożenie przepływów replikacji, które mają połączenia źródła z typem połączenia SAP HANA Cloud, Data Lake Files jest obecnie niemożliwe z powodu trwających prac serwisowych.
#XMSG
deployObjectStoreAsSourceFFDisabled=Wdrożenie przepływów replikacji, w przypadku których źródłem są dostawcy magazynu w chmurze, nie jest obecnie możliwe.
#XMSG
deployConfluentSourceFFDisabled=Wdrożenie przepływów replikacji z Confluent Kafka jako źródłem jest obecnie niemożliwe z powodu prac utrzymaniowych dot. tej funkcji.
#XMSG
deployMaxDWCNewTableCrossed=W przypadku dużych przepływów replikacji nie istnieje możliwość "zapisania i wdrożenia" przepływu za jednym razem. Najpierw zapisz przepływ replikacji, a później go wdróż.
#XMSG
deployInProgressInfo=Wdrożenie jest już w toku.
#XMSG
deploySourceObjectInUse=Obiekty źródłowe {0} są już używane w przepływach replikacji {1}.
#XMSG
deployTargetSourceObjectInUse=Obiekty źródłowe {0} są już używane w przepływach replikacji {1}. Obiekty docelowe {2} są już używane w przepływach replikacji {3}.
#XMSG
deployReplicationFlowCheckError=Błąd podczas weryfikowania przepływu replikacji: {0}
#XMSG
preDeployTargetObjectInUse=Obiekty docelowe {0} są już używane w przepływach replikacji {1} i nie możesz mieć tego samego obiektu docelowego w dwóch różnych przepływach replikacji. Wybierz inny obiekt docelowy i spróbuj ponownie.
#XMSG
runInProgressInfo=Przepływ replikacji jest już uruchomiony.
#XMSG
deploySignavioTargetFFDisabled=Wdrożenie przepływów replikacji, które mają SAP Signavio jako swoje źródło, jest obecnie niemożliwe z powodu prac utrzymaniowych dot. tej funkcji.
#XMSG
deployHanaViewAsSourceFFDisabled=Wdrożenie przepływów replikacji, które mają widoki jako obiekty źródłowe, dla wybranego połączenia źródła, jest obecnie niemożliwe. Spróbuj ponownie później.
#XMSG
deployMsOneLakeTargetFFDisabled=Wdrożenie przepływów replikacji, które mają Microsoft OneLake jako swoje źródło, jest obecnie niemożliwe z powodu prac utrzymaniowych dot. tej funkcji.
#XMSG
deploySFTPTargetFFDisabled=Wdrożenie przepływów replikacji, które mają SFTP jako swój cel, jest obecnie niemożliwe z powodu prac utrzymaniowych dot. tej funkcji.
#XMSG
deploySFTPSourceFFDisabled=Wdrożenie przepływów replikacji, które mają SFTP jako swoje źródło, jest obecnie niemożliwe z powodu prac utrzymaniowych dot. tej funkcji.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Nazwa techniczna
#XFLD
businessNameInRenameTarget=Nazwa biznesowa
#XTOL
renametargetDialogTitle=Zmień nazwę obiektu docelowego
#XBUT
targetRenameButton=Zmień nazwę
#XBUT
targetRenameCancel=Anuluj
#XMSG
mandatoryTargetName=Musisz wprowadzić nazwę.
#XMSG
dwcSpecialChar=_(podkreślenie) to jedyny dozwolony znak specjalny.
#XMSG
dwcWithDot=Nazwa tabeli docelowej może składać się z liter alfabetu łacińskiego, cyfr, podkreśleń (_) i kropek (.). Pierwszym znakiem musi być litera, cyfra lub podkreślenie (nie kropka).
#XMSG
nonDwcSpecialChar=Dozwolone znaki specjalne to _(podkreślenie) -(łącznik) .(kropka)
#XMSG
firstUnderscorePattern=Nazwa nie może zaczynać się od _(podkreślenia).

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: Wyświetl instrukcję SQL Create Table
#XMSG
sqlDialogMaxPKWarning=W Google BigQuery obsługiwanych jest maksymalnie 16 kluczy podstawowych, a obiekt źródłowy zawiera większą liczbę. W związku z tym w tej instrukcji nie zdefiniowano kluczy podstawowych.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Co najmniej jedna kolumna źródłowa zawiera typy danych, których nie można zdefiniować jako kluczy podstawowych w Google BigQuery. W związku z tym nie zdefiniowano danych podstawowych. W Google BigQuery klucz podstawowy mogą mieć tylko następujące typy danych: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Kopiuj i zamknij
#XBUT
closeDDL=Zamknij
#XMSG
copiedToClipboard=Skopiowano do schowka


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Obiekt ''{0}'' nie może być częścią łańcucha zadań, ponieważ nie ma końca (ponieważ zawiera obiekty z typem wczytywania Początkowe i delta/Tylko delta).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Obiekt ''{0}'' nie może być częścią łańcucha zadań, ponieważ nie ma końca (ponieważ zawiera obiekty z typem wczytywania Początkowe i delta).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Nie można dodać obiektu "{0}" do łańcucha zadań.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Istnieją niezapisane obiekty docelowe. Zapisz ponownie.{0}{0} Działanie tej funkcji zostało zmienione: w przeszłości obiekty docelowe były tworzone w środowisku docelowym dopiero po wdrożeniu przepływu replikacji.{0} Obecnie obiekty są już utworzone, gdy zapisuje się przepływ replikacji. Twój przepływ replikacji został utworzony przed wprowadzeniem tej zmiany i zawiera nowe obiekty.{0} Musisz jeszcze raz zapisać przepływ replikacji przed jego wdrożeniem, tak aby nowe obiekty zostały poprawnie uwzględnione.
#XMSG
confirmChangeContentTypeMessage=Zamierzasz zmienić typ zawartości. Jeśli to zrobisz, wszystkie istniejące projekcje zostaną usunięte.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Nazwa podmiotu
#XFLD
schemaDialogVersionName=Wersja schematu
#XFLD
includeTechKey=Uwzględnij klucz techniczny
#XFLD
segementButtonFlat=Ustalona
#XFLD
segementButtonNested=Zagnieżdżona
#XMSG
subjectNamePlaceholder=Wyszukaj nazwę podmiotu

#XMSG
@EmailNotificationSuccess=Konfiguracja powiadomień e-mail w czasie wykonania została zapisana.

#XFLD
@RuntimeEmailNotification=Powiadomienie e-mail w czasie wykonania

#XBTN
@TXT_SAVE=Zapisz


