#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=複製流程

#XFLD: Edit Schema button text
editSchema=編輯綱要

#XTIT : Properties heading
configSchema=組態綱要

#XFLD: save changed button text
applyChanges=套用變更


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=選擇來源連線
#XFLD
sourceContainernEmptyText=選擇容器
#XFLD
targetConnectionEmptyText=選擇目標連線
#XFLD
targetContainernEmptyText=選擇容器
#XFLD
sourceSelectObjectText=選擇來源物件
#XFLD
sourceObjectCount=來源物件 ({0})
#XFLD
targetObjectText=目標物件
#XFLD
confluentBrowseContext=選擇內容
#XBUT
@retry=重試
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=租用戶升級進行中。

#XTOL
browseSourceConnection=瀏覽來源連線
#XTOL
browseTargetConnection=瀏覽目標連線
#XTOL
browseSourceContainer=瀏覽來源容器
#XTOL
browseAndAddSourceDataset=新增來源物件
#XTOL
browseTargetContainer=瀏覽目標容器
#XTOL
browseTargetSetting=瀏覽目標設定
#XTOL
browseSourceSetting=瀏覽來源設定
#XTOL
sourceDatasetInfo=資訊
#XTOL
sourceDatasetRemove=移除
#XTOL
mappingCount=此表示非基於名稱的對映/運算式總數量。
#XTOL
filterCount=此表示篩選條件總數量。
#XTOL
loading=載入中...
#XCOL
deltaCapture=差異擷取
#XCOL
deltaCaptureTableName=差異擷取表格
#XCOL
loadType=載入類型
#XCOL
deleteAllBeforeLoading=載入前刪除全部
#XCOL
transformationsTab=投影
#XCOL
settingsTab=設定

#XBUT
renameTargetObjectBtn=重新命名目標物件
#XBUT
mapToExistingTargetObjectBtn=對映至現有目標物件
#XBUT
changeContainerPathBtn=更改容器路徑
#XBUT
viewSQLDDLUpdated=檢視 SQL 建立表格陳述式
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=來源物件不支援差異擷取，但所選目標物件已啟用差異擷取選項。
#XMSG
sourceObjectNonDeltaSupportErrorMsg=由於已啟用差異擷取，因此無法使用目標物件，{0}而來源物件不支援差異擷取。{1}您可選擇其他不支援差異擷取的目標物件。
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=具有此名稱的目標物件已存在。但由於已啟用差異擷取，因此無法使用{0}，而來源物件不{0}支援差異擷取。{1}您可輸入不{0}支援差異擷取的現有目標物件名稱，或輸入尚未存在的名稱。
#XBUT
copySQLDDLUpdated=複製 SQL 建立表格陳述式
#XMSG
targetObjExistingNoCDCColumnUpdated=Google BigQuery 中的現有表格必須包含下列欄，才能更改資料擷取 (CDC)：{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=由於下列來源物件沒有主鍵值，或者使用不符合檢索主鍵值條件的連線，因此不支援。
#XMSG
new_infoForUnsupportedDatasetNoKeys2=請參閱 SAP KBA 3531135 瞭解可能的解決方案。
#XLST: load type list values
initial=僅初始
@emailUpdateError=更新電子郵件通知清單時發生錯誤

#XLST
initialDelta=初始和差異

#XLST
deltaOnly=僅差異
#XMSG
confluentDeltaLoadTypeInfo=Confluent Kafka 來源僅支援載入類型「初始和差異」。
#XMSG
confirmRemoveReplicationObject=您確定要刪除複製嗎？
#XMSG
confirmRemoveReplicationTaskPrompt=此動作將刪除現有複製，您要繼續嗎？
#XMSG
confirmTargetConnectionChangePrompt=此動作將重設目標連線、目標容器並刪除所有目標物件。您要繼續嗎？
#XMSG
confirmTargetContainerChangePrompt=此動作將重設目標容器並刪除所有現有目標物件。您要繼續嗎？
#XMSG
confirmRemoveTransformObject=您確定要刪除投影 {0} 嗎？
#XMSG
ErrorMsgContainerChange=更改容器路徑時發生錯誤。
#XMSG
infoForUnsupportedDatasetNoKeys=由於下列來源物件沒有主鍵值，因此不支援：
#XMSG
infoForUnsupportedDatasetView=不支援下列類型為檢視的來源物件：
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=由於下列來源物件為包含輸入參數的 SQL 檢視，因此不支援：
#XMSG
infoForUnsupportedDatasetExtractionDisabled=由於下列來源物件的萃取工作已停用，因此不支援：
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Confluent 連線唯一允許的序列化格式為 AVRO 和 JSON。由於下列物件使用不同序列化格式，因此不支援：
#XMSG
infoForUnsupportedDatasetSchemaNotFound=無法取得下列物件的綱要。請選擇適當內容，或驗證綱要登錄組態
#XTOL: warning dialog header on deleting replication task
deleteHeader=刪除
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=針對 Google BigQuery 不支援「載入前刪除全部」設定。
#XBUT
DeleteAllBeforeLoadingConfluentInfo=「載入前刪除全部」設定會在每次複製前，刪除和重新建立物件 (主題)。這也會刪除所有指派的訊息。
#XTOL
DeleteAllBeforeLoadingLTFInfo=此目標類型不支援載入前刪除全部設定。
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=技術名稱
#XCOL
connBusinessName=業務名稱
#XCOL
connDescriptionName=說明
#XCOL
connType=類型
#XMSG
connTblNoDataFoundtxt=找不到連線
#XMSG
connectionError=取得連線時發生錯誤。
#XMSG
connectionCombinationUnsupportedErrorTitle=不支援連線組合
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=目前不支援從 {0} 複製到 {1}。
#XMSG
invalidTargetforSourceHDLFErrorTitle=不支援連線類型組合
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=不支援從連線類型為 SAP HANA Cloud (資料湖檔案) 的連線複製到 {0}。您只能複製到 SAP Datasphere。

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=選擇
#XBUT
containerCancelBtn=取消
#XTOL
containerSelectTooltip=選擇
#XTOL
containerCancelTooltip=取消
#XMSG
containerContainerPathPlcHold=容器路徑
#XFLD
containerContainertxt=容器
#XFLD
confluentContainerContainertxt=內容
#XMSG
infoMessageForSLTSelection=僅允許 /SLT/大量傳輸 ID 作為容器。請選擇 SLT (若有) 之下的大量傳輸 ID，並按一下提交。
#XMSG
msgFetchContainerFail=取得容器資料時發生錯誤。
#XMSG
infoMessageForSLTHidden=此連線不支援 SLT 資料夾，因此未出現在下方清單。

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=選擇其中包含子資料夾的容器。
#XMSG
sftpIncludeSubFolderText=False
#XMSG
sftpIncludeSubFolderTextNew=否

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(尚無篩選對映)
#XMSG
failToFetchRemoteMetadata=取得中繼資料時發生錯誤。
#XMSG
failToFetchData=取得現有目標時發生錯誤。
#XCOL
@loadType=載入類型
#XCOL
@deleteAllBeforeLoading=載入前刪除全部

#XMSG
@loading=載入中...
#XFLD
@selectSourceObjects=選擇來源物件
#XMSG
@exceedLimit=您一次無法匯入超過 {0} 個物件。請取消選擇至少 {1} 個物件。
#XFLD
@objects=物件
#XBUT
@ok=確定
#XBUT
@cancel=取消
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=下一步
#XBUT
btnAddSelection=新增選擇
#XTOL
@remoteFromSelection=自選擇移除
#XMSG
@searchInForSearchField=搜尋：{0}

#XCOL
@name=技術名稱
#XCOL
@type=類型
#XCOL
@location=位置
#XCOL
@label=業務名稱
#XCOL
@status=狀態

#XFLD
@searchIn=搜尋：
#XBUT
@available=可用
#XBUT
@selection=選擇

#XFLD
@noSourceSubFolder=表格和檢視
#XMSG
@alreadyAdded=已存在於圖表中
#XMSG
@askForFilter=有超過 {0} 個項目。請輸入篩選字串以縮小項目數量。
#XFLD: success label
lblSuccess=成功
#XFLD: ready label
lblReady=就緒
#XFLD: failure label
lblFailed=失敗
#XFLD: fetching status label
lblFetchingDetail=取得明細

#XMSG Place holder text for tree filter control
filterPlaceHolder=輸入內文以篩選最上層物件
#XMSG Place holder text for server search control
serverSearchPlaceholder=輸入並按下 Enter 以搜尋
#XMSG
@deployObjects=正在匯入 {0} 個物件...
#XMSG
@deployObjectsStatus=已匯入的物件數量：{0}。無法匯入的物件數量：{1}。

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=無法開啟本端儲藏庫瀏覽器。
#XMSG
@openRemoteSourceBrowserError=無法取得來源物件。
#XMSG
@openRemoteTargetBrowserError=無法取得目標物件。
#XMSG
@validatingTargetsError=驗證目標時發生錯誤。
#XMSG
@waitingToImport=就緒可匯入

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=已超過物件最大數量。請針對一個複製流程選擇最多 500 個物件。

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=技術名稱
#XFLD
sourceObjectBusinessName=業務名稱
#XFLD
sourceNoColumns=欄數量
#XFLD
containerLbl=容器

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=您必須選擇複製流程的來源連線。
#XMSG
validationSourceContainerNonExist=您必須選擇來源連線的容器。
#XMSG
validationTargetNonExist=您必須選擇複製流程的目標連線。
#XMSG
validationTargetContainerNonExist=您必須選擇目標連線的容器。
#XMSG
validationTruncateDisabledForObjectTitle=複製到物件儲存。
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=在雲端儲存的複製僅在已設定「載入前刪除全部」選項或目標下無目標物件時可用。{0}{0}若仍要針對未設定「載入前刪除全部」選項的物件啟用複製，請確保您在執行複製流程之前，系統內無目標物件。
#XMSG
validationTaskNonExist=複製流程中必須至少有一個複製。
#XMSG
validationTaskTargetMissing=您必須有要與來源複製的目標：{0}
#XMSG
validationTaskTargetIsSAC=選擇的目標為 SAC Artefact：{0}
#XMSG
validationTaskTargetIsNonDwcTableKey=所選目標不是支援的本端表格：{0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=目標中具有此名稱的物件已存在，然而由於此物件不是本端表格，因此無法針對本端儲藏庫的複製流程，作為目標物件使用。
#XMSG
validateSourceTargetSystemDifference=您必須針對複製流程，選擇不同來源和目標連線和容器組合。
#XMSG
validateDuplicateSources=一或多個複製有重複來源物件名稱：{0}。
#XMSG
validateDuplicateTargets=一或多個複製有重複目標物件名稱：{0}。
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=來源物件 {0} 不支援差異擷取，但目標物件 {1} 支援。您必須移除複製。
#XMSG
validationTaskTargetObjectLoadTypeMismatch=您必須針對目標物件名稱為 {0} 的複製作業，選擇載入類型「初始和差異」。
#XMSG
validationAutoRenameTarget=已重新命名目標欄。
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=已新增自動投影，且已重新命名下列目標欄以允許複製到目標：{1}{1} {0} {1}{1}這是因為下列其中一個原因：{1}{1}{2}不支援的字元{1}{2}保留的前置字元
#XMSG
validationAutoRenameTargetDescriptionUpdated=已新增自動投影，且已重新命名下列目標欄以允許複製到 Google BigQuery：{1}{1} {0} {1}{1}這是因為下列其中一個原因：{1}{1}{2} 保留的欄名稱{1}{2} 不支援的字元{1}{2} 保留的前置字元
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=已新增自動投影，且已重新命名下列目標欄以允許複製到 Confluent：{1}{1} {0} {1}{1}這是因為下列其中一個原因：{1}{1}{2}保留的欄名稱{1}{2}不支援的字元{1}{2}保留的前置字元
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=已新增自動投影，且已重新命名下列目標欄以允許複製到目標：{1}{1} {0} {1}{1}這是因為下列其中一個原因：{1}{1}{2}保留的欄名稱{1}{2}不支援的字元{1}{2}保留的前置字元
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=已重新命名目標物件。
#XMSG
autoRenameInfoDesc=目標物件已重新命名，因為其中包含不支援的字元。僅支援下列字元：{0}{0}{1}A-Z {0}{1} a-z {0}{1} 0-9 {0}{1} . (點) {0}{1} _ (底線) {0}{1} - (連字號)
#XMSG
validationAutoTargetTypeConversion=已更改目標資料類型。
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=已更改下列目標欄的目標資料類型，因為 Google BigQuery 中不支援來源資料類型：{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=已更改下列目標欄的目標資料類型，因為目標連線中不支援來源資料類型：{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=縮短目標欄名稱。
#XMSG
validationMaxCharLengthGBQTargetDescription=Google BigQuery 中的欄名稱最多可使用 300 個字元。請使用投影以縮短下列目標欄名稱：{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=將不會建立主鍵值。
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=Google BigQuery 中支援最多 16 個主鍵值，但來源物件有較大數量的主鍵值。目標物件中不會建立任何主鍵值。
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=一或多個來源欄的資料類型無法定義為 Google BigQuery 中的主鍵值。不會在目標物件中建立任何主鍵值。{0}{0}下列目標資料類型與 Google BigQuery 資料類型相容，可定義主鍵值：{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=將一或多欄定義為主鍵值。
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=由於主鍵值使用來源綱要對話執行此動作，因此您必須定義一或多欄。
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=將一或多欄定義為主鍵值。
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=您必須將一或多欄定義為符合來源物件主鍵值限制條件的主鍵值。請移至來源物件屬性中的「組態綱要」以執行此動作。
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=請輸入有效的最大分割值。
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=最大分割值必須 ≥ 1 且 ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=將一或多欄定義為主鍵值。
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=若要複製物件，您必須將一或多個目標欄定義為主鍵值。使用投影以執行此動作。
#XMSG
validateHDLFNoPKExistingDatasetError=將一或多欄定義為主鍵值。
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=若要將資料複製到現有目標物件，必須具有一或多個定義為主鍵值的欄。{0} 您有下列選項將一或多欄定義為主鍵值：{0}{1}使用本端表格編輯器以更改現有目標物件，然後重新載入複製流程。{0}{1}重新命名複製流程中的目標物件。這會在執行開始時建立新物件。重新命名後，即可在投影時將一或多欄定義為主鍵值。{0}{1}將物件對映至一或多欄已定義為主鍵值的其他現有目標物件。
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=儲藏庫中已有所選目標：「{0}」。
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=儲藏庫中的其他表格已使用差異擷取表格名稱：{0}。在您可儲存複製流程之前，必須重新命名這些目標物件，確保相關差異擷取表格名稱為唯一。
#XMSG
validateConfluentEmptySchema=定義綱要
#XMSG
validateConfluentEmptySchemaDescUpdated=來源表格沒有綱要，請選擇「組態綱要」進行定義
#XMSG
validationCSVEncoding=CSV 編碼無效
#XMSG
validationCSVEncodingDescription=工作細項的 CSV 編碼無效。
#XMSG
validateConfluentEmptySchema=請選擇相容的目標資料類型
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=請選擇相容的目標資料類型
#XMSG
globalValidateTargetDataTypeDesc=欄對映發生錯誤。請移至「投影」，並確保所有來源欄皆已與唯一欄、與具有相容資料類型欄對映，且所有定義的運算式皆有效。
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=欄名稱重複。
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=不支援重複的欄名稱，請使用投影對話修正。下列目標物件的欄名稱重複：{0}。
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=欄名稱重複。
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=不支援重複的欄名稱。下列目標物件的欄名稱重複：{0}。
#XMSG
deltaOnlyLoadTypeTittle=資料中可能有不一致。
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=「僅差異」載入類型不會考量上次儲存和下一次執行之間的來源更改內容。
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=將載入類型更改為「初始」。
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=僅可針對載入類型「僅初始」複製未具備主鍵值以 ABAP 為基礎的物件。
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=停用差異擷取。
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=若要使用來源連線類型 ABAP 複製未具備主鍵值的物件，則必須先停用此表格的差異擷取。
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=更改目標物件。
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=由於差異擷取已啟用，因此無法使用目標物件。您可重新命名目標物件並關閉新 (重新命名) 物件的差異擷取，或者將來源物件對映至已停用差異擷取的目標物件。
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=更改目標物件。
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=由於目標物件沒有所需技術欄 __load_package_id，因此無法使用。您可使用尚未存在的名稱來重新命名目標物件。系統便會建立具有與來源物件相同定義且包含技術欄的新物件，或者可將目標物件對映至具有所需技術欄 (__load_package_id) 的現有物件。
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=更改目標物件。
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=由於目標物件沒有所需技術欄 __load_record_id，因此無法使用。您可使用尚未存在的名稱來重新命名目標物件。系統便會建立具有與來源物件相同定義且包含技術欄的新物件，或者可將目標物件對映至具有所需技術欄 (__load_record_id) 的現有物件。
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=更改目標物件。
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=由於目標物件技術欄 __load_record_id 的資料類型不是 "string(44)"，因此無法使用。您可使用尚未存在的名稱來重新命名目標物件。系統便會建立具有與來源物件相同定義且資料類型正確的新物件，或者可將目標物件對映至具有所需技術欄 (__load_record_id) 和正確資料類型的現有物件。
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=更改目標物件。
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=由於目標物件具有主鍵值但來源物件沒有，因此無法使用。您可使用尚未存在的名稱來重新命名目標物件。系統便會建立具有與來源物件相同定義且無主鍵值的新物件，或者可將目標物件對映至具有所需技術欄 (__load_package_id) 且沒有主鍵值的現有物件。
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=更改目標物件。
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=由於目標物件具有主鍵值但來源物件沒有，因此無法使用。您可使用尚未存在的名稱來重新命名目標物件。系統便會建立具有與來源物件相同定義且無主鍵值的新物件，或者可將目標物件對映至具有所需技術欄 (__load_record_id) 且沒有主鍵值的現有物件。
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=更改目標物件。
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=由於目標物件技術欄 __load_package_id 的資料類型不是 "binary(>=256)"，因此無法使用。您可使用尚未存在的名稱來重新命名目標物件。系統便會建立具有與來源物件相同定義且資料類型正確的新物件，或者可將目標物件對映至具有所需技術欄 (__load_package_id) 和正確資料類型的現有物件。
#XMSG
validationAutoRenameTargetDPID=已重新命名目標欄。
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=移除來源物件。
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=來源物件沒有主鍵值，這在此內容中不支援。
#XMSG
validationAutoRenameTargetDPIDDescription=已新增自動投影，且已重新命名下列目標欄以允許從 ABAP 來源不含鍵值進行複製：{1}{1} {0} {1}{1}這是因為下列其中一個原因：{1}{1}{2} 保留的欄名稱{1}{2} 不支援的字元{1}{2} 保留的前置字元
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=複製到 {0}。
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=目前無法儲存和部署具備 {0} 作為目標的複製流程，因為我們正於此功能執行維護作業。
#XMSG
TargetColumnSkippedLTF=已跳過目標欄。
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=由於資料類型不受支援，因此已跳過目標欄。{0}{1}
#XMSG
validatePKTimeColumnLTF1=時間欄作為主鍵值。
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=來源物件具備時間欄作為主鍵值，這在此內容中不支援。
#XMSG
validateNoPKInLTFTarget=缺少主鍵值。
#XMSG
validateNoPKInLTFTargetDescription=未定義目標中的主鍵值，這在此內容中不支援。
#XMSG
validateABAPClusterTableLTF=ABAP 叢集表格。
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=來源物件為 ABAP 叢集表格，這在此內容中不支援。
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=您尚未新增資料。
#YINS
welcomeText2=若要開始複製流程，請在左側選擇連線和來源物件。

#XBUT
wizStep1=選擇來源連線
#XBUT
wizStep2=選擇來源容器
#XBUT
wizStep3=新增來源物件

#XMSG
limitDataset=已達到物件最大數量。請移除現有物件以新增，或建立新複製流程。
#XMSG
premiumOutBoundRFCannotStartWarningMsg=無法開始此非 SAP 目標連線的複製流程，因為本月沒有可用的外傳量。
#XMSG
premiumOutBoundRFAdminWarningMsg=管理員可增加此租用戶的高階外傳區塊，使本月有外傳量可供使用。
#XMSG
messageForToastForDPIDColumn2=已將新欄新增至 {0} 個物件的目標 - 處理沒有主鍵值且以 ABAP 為基礎的來源物件連線時需要。
#XMSG
PremiumInboundWarningMessage=根據複製流程數量和待複製的資料量而定，透過 {1} 複製所需的 SAP HANA 資源{0}可能超過您租用戶的可用產能。
#XMSG
PremiumInboundWarningMsg=根據複製流程數量和要複製的資料量而定，{0}透過 "{1}" 複製資料所需的 SAP HANA 資源可能超過您租用戶的可用產能。
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=請輸入投影名稱。
#XMSG
emptyTargetColumn=請輸入目標欄名稱。
#XMSG
emptyTargetColumnBusinessName=請輸入目標欄業務名稱。
#XMSG
invalidTransformName=請輸入投影名稱。
#XMSG
uniqueColumnName=請重新命名目標欄。
#XMSG
copySourceColumnLbl=自來源物件複製欄
#XMSG
renameWarning=重新命名目標表格時，確保選擇唯一名稱。若空間中已有新名稱表格，則將使用該表格的定義。

#XMSG
uniqueColumnBusinessName=請重新命名目標欄業務名稱。
#XMSG
uniqueSourceMapping=請選擇不同來源欄。
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=下列目標欄已使用來源欄 {0}：{1}{1}{2}{1}{1} 請針對此目標欄或其他目標欄，選擇尚未使用的來源欄以儲存投影。
#XMSG
uniqueColumnNameDescription=您輸入的目標欄名稱已存在。若要儲存投影，則必須輸入唯一欄名稱。
#XMSG
uniqueColumnBusinessNameDesc=目標欄業務名稱已存在。若要儲存投影，您必須輸入唯一欄業務名稱。
#XMSG
emptySource=請選擇來源欄或輸入常數。
#XMSG
emptySourceDescription=若要建立有效對映輸入項，則必須選擇來源欄或輸入常數值。
#XMSG
emptyExpression=請定義對映。
#XMSG
emptyExpressionDescription1=選擇您要對映目標欄的來源欄，或者在欄 {0} 函式/常數{1}中勾選核取方塊。{2}{2} 函式將依據目標資料類型自動輸入。可手動輸入常數值。
#XMSG
numberExpressionErr=請輸入數字。
#XMSG
numberExpressionErrDescription=您已選擇數值資料類型。這表示您只能輸入數值，以及小數點 (若適用)。請勿使用單引號。
#XMSG
invalidLength=請輸入有效的長度值。
#XMSG
invalidLengthDescription=資料類型長度必須等於或大於來源欄的長度，且可介於 1 到 5000 之間。
#XMSG
invalidMappedLength=請輸入有效的長度值。
#XMSG
invalidMappedLengthDescription=資料類型長度必須等於或大於來源欄 {0} 的長度，且可介於 1 到 5000 之間。
#XMSG
invalidPrecision=請輸入有效的精確度值。
#XMSG
invalidPrecisionDescription=精確度會定義總位數。小數位數定義小數點後的位數，並可介於 0 到精確度之間。{0}{0} 範例：{0}{1} 精確度 6、小數位數 2 對應至數字，如：1234.56。{0}{1} 精確度 6、小數位數 6 對應至數字，如：0.123546。{0} {0} 目標的精確度和小數位數必須與來源的精確度和小數位數相容，來源的所有位數才能符合目標欄位。例如，若來源中有精確度 6 和小數位數 2 (因此小數點前的數字不是 0)，則目標中不能有精確度 6 和小數位數 6。
#XMSG
invalidPrimaryKey=請輸入至少一個主鍵值。
#XMSG
invalidPrimaryKeyDescription=未定義此綱要的主鍵值。
#XMSG
invalidMappedPrecision=請輸入有效的精確度值。
#XMSG
invalidMappedPrecisionDescription1=精確度會定義總位數。小數位數定義小數點後的位數，並可介於 0 到精確度之間。{0}{0} 範例：{0}{1} 精確度 6、小數位數 2 對應至數字，如：1234.56。{0}{1} 精確度 6、小數位數 6 對應至數字，如：0.123546。{0}{0}資料類型的精確度必須等於或大於來源 ({2}) 的精確度。
#XMSG
invalidScale=請輸入有效的小數位數值。
#XMSG
invalidScaleDescription=精確度會定義總位數。小數位數定義小數點後的位數，並可介於 0 到精確度之間。{0}{0} 範例：{0}{1} 精確度 6、小數位數 2 對應至數字，如：1234.56。{0}{1} 精確度 6、小數位數 6 對應至數字，如：0.123546。{0} {0} 目標的精確度和小數位數必須與來源的精確度和小數位數相容，來源的所有位數才能符合目標欄位。例如，若來源中有精確度 6 和小數位數 2 (因此小數點前的數字不是 0)，則目標中不能有精確度 6 和小數位數 6。
#XMSG
invalidMappedScale=請輸入有效的小數位數值。
#XMSG
invalidMappedScaleDescription1=精確度會定義總位數。小數位數定義小數點後的位數，並可介於 0 到精確度之間。{0}{0} 範例：{0}{1} 精確度 6、小數位數 2 對應至數字，如：1234.56。{0}{1} 精確度 6、小數位數 6 對應至數字，如：0.123546。{0}{0}資料類型的小數位數必須等於或大於來源 ({2}) 的小數位數。
#XMSG
nonCompatibleDataType=請選擇相容的目標資料類型。
#XMSG
nonCompatibleDataTypeDescription1=您在此指定的資料類型必須與來源資料類型 ({0}) 相容。{1}{1}範例：若來源欄具有資料類型字串且包含字母，則無法將小數資料類型用於目標。
#XMSG
invalidColumnCount=請選擇來源欄。
#XMSG
ObjectStoreInvalidScaleORPrecision=請針對精確度和小數位數輸入有效值。
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=第一個值為精確度，定義總位數。第二個值為小數位數，定義小數點後的位數。輸入大於來源小數位值的目標小數位值，並確保輸入的目標小數位和精確度值之間的差異，大於來源小數位和精確度值之間的差異。
#XMSG
InvalidPrecisionORScale=請針對精確度和小數位數輸入有效值。
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=第一個值為精確度，定義總位數。第二個值為小數位數，定義小數點後的位數。{0}{0}由於 Google BigQuery 中不支援來源資料類型，其將轉換為目標資料類型 DECIMAL。在此情況下，只能將精確度定義介於 38 到 76 之間，而小數位數則介於 9 到 38 之間。此外，精確度減小數位數 (表示小數點前的位數) 的結果必須介於 29 到 38 之間。
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=第一個值為精確度，定義總位數。第二個值為小數位數，定義小數點後的位數。{0}{0}由於 Google BigQuery 中不支援來源資料類型，其將轉換為目標資料類型 DECIMAL。在此情況下，只能將精確度定義為 20 或以上。此外，精確度減小數位數 (反映小數點前的位數) 的結果必須為 20 或以上。
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=第一個值為精確度，定義總位數。第二個值為小數位數，定義小數點後的位數。{0}{0}由於目標中不支援來源資料類型，其將轉換為目標資料類型 DECIMAL。在此情況下，精確度必須由大於或等於 1 且小於或等於 38 ，以及小數位數小於或等於精確度的任何數字定義。
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=第一個值為精確度，定義總位數。第二個值為小數位數，定義小數點後的位數。{0}{0}由於目標中不支援來源資料類型，其將轉換為目標資料類型 DECIMAL。在此情況下，只能將精確度定義為 20 或以上。此外，精確度減小數位數 (反映小數點前的位數) 的結果必須為 20 或以上。
#XMSG
invalidColumnCountDescription=若要建立有效對映輸入項，則必須選擇來源欄或輸入常數值。
#XMSG
duplicateColumns=請重新命名目標欄。
#XMSG
duplicateGBQCDCColumnsDesc=目標欄名稱已於 Google BigQuery 中保留。您必須重新命名才能儲存投影。
#XMSG
duplicateConfluentCDCColumnsDesc=目標欄名稱已於 Confluent 中保留。您必須重新命名才能儲存投影。
#XMSG
duplicateSignavioCDCColumnsDesc=目標欄名稱已於 SAP Signavio 中保留。您必須重新命名才能儲存投影。
#XMSG
duplicateMsOneLakeCDCColumnsDesc=目標欄名稱已於 MS OneLake 中保留。您必須重新命名才能儲存投影。
#XMSG
duplicateSFTPCDCColumnsDesc=目標欄名稱已於 SFTP 中保留。您必須重新命名才能儲存投影。
#XMSG
GBQTargetNameWithPrefixUpdated1=目標欄名稱包含 Google BigQuery 中保留的前置字元。您必須重新命名才能儲存投影。{0}{0}目標欄名稱不能以任一下列字串開頭：{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=請縮短目標欄名稱。
#XMSG
GBQtargetMaxLengthDesc=在 Google BigQuery 中，欄名稱可使用最多 300 個字元。請縮短目標欄名稱才能儲存投影。
#XMSG
invalidMappedScalePrecision=目標的精確度和小數位數必須與來源的精確度和小數位數相容，來源的所有數字才適合目標欄位。
#XMSG
invalidMappedScalePrecisionShortText=請輸入有效的精確度和小數位數值。
#XMSG
validationIncompatiblePKTypeDescProjection3=一或多個來源欄的資料類型無法定義為 Google BigQuery 中的主鍵值。不會在目標物件中建立任何主鍵值。{0}{0}下列目標資料類型與 Google BigQuery 資料類型相容，可定義主鍵值：{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=取消勾選欄 __message_id。
#XMSG
uncheckColumnMessageIdDesc=欄：主鍵值
#XMSG
validationOpCodeInsert=您必須輸入要插入的值。
#XMSG
recommendDifferentPrimaryKey=建議您於項目層次選擇不同主鍵值。
#XMSG
recommendDifferentPrimaryKeyDesc=作業代碼已定義時，建議針對陣列索引和項目選擇不同主鍵值，以避免例如欄重複的問題。
#XMSG
selectPrimaryKeyItemLevel=您必須針對表頭和項目層次選擇至少一個主鍵值。
#XMSG
selectPrimaryKeyItemLevelDesc=展開陣列或對映時，您必須選擇兩個主鍵值，一個位於表頭層次且一個位於項目層次。
#XMSG
invalidMapKey=您必須於表頭層次選擇至少一個主鍵值。
#XMSG
invalidMapKeyDesc=展開陣列或對映時，您必須於表頭層次選擇主鍵值。
#XFLD
txtSearchFields=搜尋目標欄
#XFLD
txtName=名稱
#XMSG
txtSourceColValidation=一或多個來源欄不支援：
#XMSG
txtMappingCount=對映 ({0})
#XMSG
schema=綱要
#XMSG
sourceColumn=來源欄
#XMSG
warningSourceSchema=對綱要進行更改將影響投影對話中的對映。
#XCOL
txtTargetColName=目標欄 (技術名稱)
#XCOL
txtDataType=目標資料類型
#XCOL
txtSourceDataType=來源資料類型
#XCOL
srcColName=來源欄 (技術名稱)
#XCOL
precision=總位數
#XCOL
scale=小數位數
#XCOL
functionsOrConstants=函式/常數
#XCOL
txtTargetColBusinessName=目標欄 (業務名稱)
#XCOL
prKey=主鍵值
#XCOL
txtProperties=屬性
#XBUT
txtOK=儲存
#XBUT
txtCancel=取消
#XBUT
txtRemove=移除
#XFLD
txtDesc=說明
#XMSG
rftdMapping=對映
#XFLD
@lblColumnDataType=資料類型
#XFLD
@lblColumnTechnicalName=技術名稱
#XBUT
txtAutomap=自動對映
#XBUT
txtUp=上移
#XBUT
txtDown=下移

#XTOL
txtTransformationHeader=投影
#XTOL
editTransformation=編輯
#XTOL
primaryKeyToolip=鍵值


#XMSG
rftdFilter=篩選
#XMSG
rftdFilterColumnCount=來源：{0}({1})
#XTOL
rftdFilterColSearch=搜尋
#XMSG
rftdFilterColNoData=沒有可顯示的欄
#XMSG
rftdFilteredColNoExps=沒有篩選運算式
#XMSG
rftdFilterSelectedColTxt=新增篩選：
#XMSG
rftdFilterTxt=篩選可用於
#XBUT
rftdFilterSelectedAddColExp=新增運算式
#YINS
rftdFilterNoSelectedCol=選擇欄以新增篩選。
#XMSG
rftdFilterExp=篩選運算式
#XMSG
rftdFilterNotAllowedColumn=此欄不支援新增篩選。
#XMSG
rftdFilterNotAllowedHead=不支援的欄
#XMSG
rftdFilterNoExp=未定義篩選
#XTOL
rftdfilteredTt=已篩選
#XTOL
rftdremoveexpTt=移除篩選運算式
#XTOL
validationMessageTt=驗證訊息
#XTOL
rftdFilterDateInp=選擇日期
#XTOL
rftdFilterDateTimeInp=選擇日期時間
#XTOL
rftdFilterTimeInp=選擇時間
#XTOL
rftdFilterInp=輸入值
#XMSG
rftdFilterValidateEmptyMsg={1} 欄的 {0} 篩選運算式空白
#XMSG
rftdFilterValidateInvalidNumericMsg={1} 欄的 {0} 篩選運算式包含無效的數值
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=篩選運算式必須包含有效的數值
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=若目標物件綱要已更改，則在主頁面使用功能「對映至現有目標物件」以套用更改，並再次重新對映包含其來源的目標物件。
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=若目標表格已存在，且對映包含綱要更改，則您必須相應更改目標表格，再部署複製流程。
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=若對映包含綱要更改，則您必須相應更改目標表格，再部署複製流程。
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=下列不支援的欄已跳過來源定義：{0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=下列不支援的欄已跳過目標定義：{0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=由於下列物件已公開使用，因此不受支援：{0} {1} {0} {0}若要使用複製流程中的表格，則不可將語意使用 (位於表格設定) 設為「{2}分析資料集{2}」。
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=由於目標物件已公開使用，因此無法使用。{0} {0}若要使用複製流程中的表格，則不可將語意使用 (位於表格設定) 設為「{1}分析資料集{1}」。
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=具有此名稱的目標物件已存在。然而，由於已公開使用，因此無法使用。{0} {0}若要在複製流程中使用表格，則語意使用 (位於表格設定) 不可設為「{1}分析資料集{1}」。
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=目標中具有此名稱的物件已存在。{0}然而由於此物件不是本端表格，因此無法針對本端儲藏庫的複製流程，作為目標物件使用。
#XMSG:
targetAutoRenameUpdated=已重新命名目標欄。
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=已重新命名目標欄，以允許在 Google BigQuery 中進行複製。這是因為下列其中一個原因：{0} {1}{2}保留的欄名稱{3}{2}不支援的字元{3}{2}保留的前置字元{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=已重新命名目標欄，以允許在 Confluent 中進行複製。這是因為下列其中一個原因：{0} {1}{2}保留的欄名稱{3}{2}不支援的字元{3}{2}保留的前置字元{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=已重新命名目標欄，以允許在目標中進行複製。這是因為下列其中一個原因：{0}{1}{2}不支援的字元{3}{2}保留的前置字元{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=已重新命名目標欄，以允許在目標中進行複製。這是因為下列其中一個原因：{0}{1}{2}保留的欄名稱{3}{2}保留的前置字元{3}{2}保留的前置字元{3}{4}
#XMSG:
targetAutoDataType=已更改目標資料類型。
#XMSG:
targetAutoDataTypeDesc=由於 Google BigQuery 中不支援來源資料類型，因此已將目標資料類型更改為 {0}。
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=由於目標連線中不支援來源資料類型，因此已將目標資料類型更改為 {0}。
#XMSG
projectionGBQUnableToCreateKey=將不會建立主鍵值。
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=Google BigQuery 中支援最多 16 個主鍵值，但來源物件有較大數量的主鍵值。目標物件中不會建立任何主鍵值。
#XMSG
HDLFNoKeyError=將一或多欄定義為主鍵值。
#XMSG
HDLFNoKeyErrorDescription=若要複製物件，您必須將一或多欄定義為主鍵值。
#XMSG
HDLFNoKeyErrorExistingTarget=將一或多欄定義為主鍵值。
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=若要將資料複製到現有目標物件，必須具有一或多個定義為主鍵值的欄。{0} {0}您有下列選項將一或多欄定義為主鍵值：{0}{0}{1}使用本端表格編輯器以更改現有目標物件，然後重新載入複製流程。{0}{0}{1}重新命名複製流程中的目標物件。這會在執行開始時建立新物件。重新命名後，即可在投影時將一或多欄定義為主鍵值。{0}{0}{1}將物件對映至一或多欄已定義為主鍵值的其他現有目標物件。
#XMSG
HDLFSourceTargetDifferentKeysWarning=已更改主鍵值。
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=相較於來源物件，您已將不同欄定義為目標物件的主鍵值。請確認這些欄唯一識別所有列，避免之後複製資料時可能的資料毀損。{0} {0}在來源物件中，下列欄會定義為主鍵值：{0} {1}
#XMSG
duplicateDPIDColumns=請重新命名目標欄。
#XMSG
duplicateDPIDDColumnsDesc1=此目標欄名稱已針對技術欄保留。請輸入不同名稱以儲存投影。
#XMSG:
targetAutoRenameDPID=已重新命名目標欄。
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=已重新命名欄，允許從 ABAP 來源不含鍵值進行複製。這是因為下列其中一個原因：{0} {1}{2}保留的欄名稱{3}{2}不支援的字元{3}{2}保留的前置字元{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle={0} 目標設定
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle={0} 來源設定
#XBUT
connectionSettingSave=儲存
#XBUT
connectionSettingCancel=取消
#XBUT: Button to keep the object level settings
txtKeep=保留
#XBUT: Button to overwrite the Object level settings
txtOverwrite=覆寫
#XFLD
targetConnectionThreadlimit=初始載入的目標執行緒限制 (1-100)
#XFLD
connectionThreadLimit=初始載入的來源執行緒限制 (1-100)
#XFLD
maxConnection=複製執行緒限制 (1-100)
#XFLD
kafkaNumberOfPartitions=分割數量
#XFLD
kafkaReplicationFactor=複製係數
#XFLD
kafkaMessageEncoder=訊息編碼器
#XFLD
kafkaMessageCompression=訊息壓縮
#XFLD
fileGroupDeltaFilesBy=差異分組依據
#XFLD
fileFormat=檔案類型
#XFLD
csvEncoding=CSV 編碼
#XFLD
abapExitLbl=ABAP Exit
#XFLD
deltaPartition=差異載入的物件執行緒計數 (1-10)
#XFLD
clamping_Data=資料截斷失敗
#XFLD
fail_On_Incompatible=不相容資料失敗
#XFLD
maxPartitionInput=最大分割數量
#XFLD
max_Partition=定義最大分割數量
#XFLD
include_SubFolder=包含子資料夾
#XFLD
fileGlobalPattern=檔案名稱的通用模式
#XFLD
fileCompression=檔案壓縮
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=檔案分隔符號
#XFLD
fileIsHeaderIncluded=檔案表頭
#XFLD
fileOrient=格式
#XFLD
gbqWriteMode=寫入模式
#XFLD
suppressDuplicate=隱藏重複
#XFLD
apacheSpark=啟用 Apache Spark 相容性
#XFLD
clampingDatatypeCb=限制小數浮點資料類型
#XFLD
overwriteDatasetSetting=於物件層次覆寫目標設定
#XFLD
overwriteSourceDatasetSetting=於物件層次覆寫來源設定
#XMSG
kafkaInvalidConnectionSetting=請輸入介於 {0} 到 {1} 之間的數字。
#XMSG
MinReplicationThreadErrorMsg=請輸入大於 {0} 的數字。
#XMSG
MaxReplicationThreadErrorMsg=請輸入小於 {0} 的數字。
#XMSG
DeltaThreadErrorMsg=輸入介於 1 到 10 之間的值。
#XMSG
MaxPartitionErrorMsg=輸入介於 1 <= x <= 2147483647 之間的值。預設值為 10。
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=輸入 {0} 到 {1} 之間的整數。
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=使用經紀商的複製係數
#XFLD
serializationFormat=序列化格式
#XFLD
compressionType=壓縮類型
#XFLD
schemaRegistry=使用綱要登錄
#XFLD
subjectNameStrat=主旨名稱策略
#XFLD
compatibilityType=相容性類型
#XFLD
confluentTopicName=主題名稱
#XFLD
confluentRecordName=記錄名稱
#XFLD
confluentSubjectNamePreview=主旨名稱預覽
#XMSG
serializationChangeToastMsgUpdated2=由於未啟用綱要登錄，因此序列化格式已更改為 JSON。若要將序列化格式更改回 AVRO，則必須先啟用綱要登錄。
#XBUT
confluentTopicNameInfo=主題名稱一律根據目標物件名稱而定。您可重新命名目標物件來更改。
#XMSG
emptyRecordNameValidationHeaderMsg=輸入記錄名稱。
#XMSG
emptyPartionHeader=請輸入分割數量。
#XMSG
invalidPartitionsHeader=請輸入有效分割數量。
#XMSG
invalidpartitionsDesc=請輸入介於 1 到 200,000 之間的數字。
#XMSG
emptyrFactorHeader=請輸入複製係數。
#XMSG
invalidrFactorHeader=請輸入有效複製係數。
#XMSG
invalidrFactorDesc=請輸入介於 1 到 32,767 之間的數字。
#XMSG
emptyRecordNameValidationDescMsg=若已使用序列化格式 "AVRO"，則僅支援下列字元：{0}{1} A-Z {0}{1} a-z {0}{1} 0-9 {0}{1} _ (底線)
#XMSG
validRecordNameValidationHeaderMsg=請輸入有效記錄名稱。
#XMSG
validRecordNameValidationDescMsgUpdated=由於已使用序列化格式 "AVRO"，因此記錄名稱必須僅包含英數字元 (A-Z, a-z, 0-9) 和底線 (_) 字元，且必須以字母或底線開頭。
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=一或多個物件具有載入類型「初始和差異」時，才能設定「差異載入的物件執行緒計數」。
#XMSG
invalidTargetName=欄名稱無效
#XMSG
invalidTargetNameDesc=目標欄名稱必須僅包含英數字元 (A-Z, a-z, 0-9) 和底線 (_) 字元。
#XFLD
consumeOtherSchema=使用其他綱要版本
#XFLD
ignoreSchemamissmatch=忽略綱要不相符
#XFLD
confleuntDatatruncation=資料截斷失敗
#XFLD
isolationLevel=隔離層級
#XFLD
confluentOffset=開始點
#XFLD
signavioGroupDeltaFilesByText=無
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=否
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=否

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=投影
#XBUT
txtAdd=新增
#XBUT
txtEdit=編輯
#XMSG
transformationText=新增投影以設定篩選或對映。
#XMSG
primaryKeyRequiredText=選擇含組態綱要的主鍵值。
#XFLD
lblSettings=設定
#XFLD
lblTargetSetting={0}：目標設定
#XMSG
@csvRF=選擇包含您要套用至資料夾中所有檔案的綱要定義檔案。
#XFLD
lblSourceColumns=來源欄
#XFLD
lblJsonStructure=JSON 結構
#XFLD
lblSourceSetting={0}：來源設定
#XFLD
lblSourceSchemaSetting={0}：來源綱要設定
#XBUT
messageSettings=訊息設定
#XFLD
lblPropertyTitle1=物件屬性
#XFLD
lblRFPropertyTitle=複製流程屬性
#XMSG
noDataTxt=沒有可顯示的欄。
#XMSG
noTargetObjectText=未選擇目標物件。
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=目標欄
#XMSG
searchColumns=搜尋欄
#XTOL
cdcColumnTooltip=差異擷取欄
#XMSG
sourceNonDeltaSupportErrorUpdated=來源物件不支援差異擷取。
#XMSG
targetCDCColumnAdded=已新增 2 個目標欄進行差異擷取。
#XMSG
deltaPartitionEnable=已將差異載入的物件執行緒限制新增至來源設定。
#XMSG
attributeMappingRemovalTxt=移除新目標物件不支援的無效對映。
#XMSG
targetCDCColumnRemoved=已移除用於差異擷取的 2 個目標欄。
#XMSG
replicationLoadTypeChanged=已將載入類型更改為「初始和差異」。
#XMSG
sourceHDLFLoadTypeError=將載入類型更改為「初始和差異」。
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=若要將物件從連線類型為 SAP HANA Cloud (資料湖檔案) 的來源連線，複製到 SAP Datasphere，則必須使用載入類型「初始和差異」。
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=啟用差異擷取。
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=若要將物件從連線類型為 SAP HANA Cloud (資料湖檔案) 的來源連線，複製到 SAP Datasphere，則必須啟用差異擷取。
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=更改目標物件。
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=由於差異擷取已停用，因此無法使用目標物件。您可重新命名目標物件 (這會允許以差異擷取建立新物件)，或啟用差異擷取對映至現有物件。
#XMSG
deltaPartitionError=輸入差異載入的有效物件執行緒計數。
#XMSG
deltaPartitionErrorDescription=輸入介於 1 到 10 之間的值。
#XMSG
deltaPartitionEmptyError=輸入差異載入的物件執行緒計數。
#XFLD
@lblColumnDescription=說明
#XMSG
@lblColumnDescriptionText1=針對技術用途 - 處理在以 ABAP 為基礎來源無主鍵值物件的複製作業期間，因發生問題造成的重複記錄。
#XFLD
storageType=儲存
#XFLD
skipUnmappedColLbl=跳過未對映的欄
#XFLD
abapContentTypeLbl=內容類型
#XFLD
autoMergeForTargetLbl=自動合併資料
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=一般
#XFLD
lblBusinessName=業務名稱
#XFLD
lblTechnicalName=技術名稱
#XFLD
lblPackage=套件
#XFLD
statusPanel=執行狀態
#XBTN: Schedule dropdown menu
SCHEDULE=排程
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=編輯排程 
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=刪除排程 
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=建立排程 
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=排程驗證檢查失敗
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=由於複製流程目前正在部署中，因此無法建立排程。{0}複製流程部署前，請稍候。
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=針對包含載入類型為「初始和差異」物件的複製流程，無法建立排程。
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=針對包含載入類型為「初始和差異/僅差異」物件的複製流程，無法建立排程。
#XFLD : Scheduled popover
SCHEDULED=已排程
#XFLD
CREATE_REPLICATION_TEXT=建立複製流程
#XFLD
EDIT_REPLICATION_TEXT=編輯複製流程
#XFLD
DELETE_REPLICATION_TEXT=刪除複製流程
#XFLD
REFRESH_FREQUENCY=頻率
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=由於現有排程{0}尚不支援載入類型「初始和差異」，因此無法佈署複製流程。{0}{0}若要佈署複製流程，您必須將所有物件{0}的載入類型設定為「僅初始」。或者，您可刪除排程、重新佈署{0}複製流程，並開始新執行。這會無止境{0}執行，並支援載入類型為「初始和差異」的物件。
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=由於現有排程{0}尚不支援載入類型「初始和差異/僅差異」，因此無法佈署複製流程。{0}{0}若要佈署複製流程，您必須將所有物件{0}的載入類型設定為「僅初始」。或者，您可刪除排程、重新佈署{0}複製流程，並開始新執行。這會無止境{0}執行，並支援載入類型為「初始和差異/僅差異」的物件。
#XMSG
SCHEDULE_EXCEPTION=取得排程明細失敗
#XFLD: Label for frequency column
everyLabel=每
#XFLD: Plural Recurrence text for Hour
hoursLabel=小時
#XFLD: Plural Recurrence text for Day
daysLabel=天
#XFLD: Plural Recurrence text for Month
monthsLabel=個月
#XFLD: Plural Recurrence text for Minutes
minutesLabel=分鐘
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=取得排程可能性資訊失敗。
#XFLD :Paused field
PAUSED=已暫停
#XMSG
navToMonitoring=於流程監控器中開啟
#XFLD
statusLbl=狀態
#XFLD
lblLastRunExecuted=最後執行開始
#XFLD
lblLastExecuted=最後執行
#XFLD: Status text for Completed
statusCompleted=已完成
#XFLD: Status text for Running
statusRunning=執行中
#XFLD: Status text for Failed
statusFailed=失敗
#XFLD: Status text for Stopped
statusStopped=已停止
#XFLD: Status text for Stopping
statusStopping=正在停止
#XFLD: Status text for Active
statusActive=啟用中
#XFLD: Status text for Paused
statusPaused=已暫停
#XFLD: Status text for not executed
lblNotExecuted=尚未執行
#XFLD
messagesSettings=訊息設定
#XTOL
@validateModel=驗證訊息
#XTOL
@hierarchy=階層
#XTOL
@columnCount=欄數量
#XMSG
VAL_PACKAGE_CHANGED=您已將此物件指派給套件 "{1}"。按一下「儲存」確認並驗證此更改。請注意，儲存後即無法復原此編輯器中的套件指派。
#XMSG
MISSING_DEPENDENCY=無法解析套件 "{1}" 中物件 "{0}" 的相關性。
#XFLD
deltaLoadInterval=差異載入間隔
#XFLD
lblHour=時數 (0-24)
#XFLD
lblMinutes=分鐘數 (0-59)
#XMSG
maxHourOrMinErr=請輸入介於 0 到 {0} 之間的值
#XMSG
maxDeltaInterval=差異載入間隔的最大值為 24 小時。{0}請依此更改分鐘數值或時數值。
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=目標容器路徑
#XFLD
confluentSubjectName=主旨名稱
#XFLD
confluentSchemaVersion=綱要版本
#XFLD
confluentIncludeTechKeyUpdated=包含技術金鑰
#XFLD
confluentOmitNonExpandedArrays=省略未展開的陣列
#XFLD
confluentExpandArrayOrMap=展開陣列或對映
#XCOL
confluentOperationMapping=作業對映
#XCOL
confluentOpCode=Opcode
#XFLD
confluentInsertOpCode=插入
#XFLD
confluentUpdateOpCode=更新
#XFLD
confluentDeleteOpCode=刪除
#XFLD
expandArrayOrMapNotSelectedTxt=未選擇
#XFLD
confluentSwitchTxtYes=是
#XFLD
confluentSwitchTxtNo=否
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=錯誤
#XTIT
executeWarning=警告
#XMSG
executeunsavederror=執行前請先儲存複製流程。
#XMSG
executemodifiederror=複製流程中有未儲存的更改，請儲存複製流程。
#XMSG
executeundeployederror=您必須部署複製流程才能執行。
#XMSG
executedeployingerror=請稍等部署結束。
#XMSG
msgRunStarted=已開始執行
#XMSG
msgExecuteFail=無法執行複製流程。
#XMSG
titleExecuteBusy=請稍候。
#XMSG
msgExecuteBusy=系統正在準備資料以便執行複製流程。
#XTIT
executeConfirmDialog=警告
#XMSG
msgExecuteWithValidations=複製流程發生驗證錯誤。執行複製流程可能導致失敗。
#XMSG
msgRunDeployedVersion=有更改待部署。將執行複製流程的最後部署版本。您要繼續嗎？
#XBUT
btnExecuteAnyway=一律執行
#XBUT
btnExecuteClose=關閉
#XBUT
loaderClose=關閉
#XTIT
loaderTitle=正在載入
#XMSG
loaderText=正在從伺服器取得明細
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=無法開始此非 SAP 目標連線的複製流程，
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=因為本月沒有可用的外傳量。
#XMSG
premiumOutBoundRFAdminErrMsgPart1=管理員可增加此租用戶的高階外傳區塊，
#XMSG
premiumOutBoundRFAdminErrMsgPart2=使本月有外傳量可供使用。


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=錯誤
#XTIT
deployInfo=資訊
#XMSG
deployCheckFailException=部署期間發生異常
#XMSG
deployGBQFFDisabled=由於我們正於此函式執行維護，因此目前無法部署包含 Google BigQuery 目標連線的複製流程。
#XMSG
deployKAFKAFFDisabled=由於我們正於此函式執行維護，因此目前無法部署包含 Apache Kafka 目標連線的複製流程。
#XMSG
deployConfluentDisabled=由於我們正於此函式執行維護，因此目前無法部署包含 Confluent Kafka 目標連線的複製流程。
#XMSG
deployInValidDWCTargetDeltaCaptureObject=針對下列目標物件，儲藏庫中的其他表格已使用差異擷取表格名稱：{0} 在您可部署複製流程之前，必須重新命名這些目標物件，確保相關差異擷取表格名稱為唯一。
#XMSG
deployDWCSourceFFDisabled=部署具備 SAP Datasphere 作為來源的複製流程目前無法進行，因為我們正於此功能執行維護作業。
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=由於我們正於此函式執行維護，因此目前無法部署包含已啟用差異本端表格作為來源物件的複製流程。
#XMSG
deployHDLFSourceFFDisabled=目前無法部署具備連線類型為 SAP HANA Cloud：資料湖檔案來源連線的複製流程，因為我們正在執行維護。
#XMSG
deployObjectStoreAsSourceFFDisabled=目前無法將具有雲端儲存提供者的複製流程，部署為其來源。
#XMSG
deployConfluentSourceFFDisabled=目前無法部署具備 Confluent Kafka 作為來源的複製流程，因為我們正於此功能執行維護作業。
#XMSG
deployMaxDWCNewTableCrossed=較大資料流程無法一次執行「儲存並部署」。請先儲存複製流程，再進行部署。
#XMSG
deployInProgressInfo=部署已在進行中。
#XMSG
deploySourceObjectInUse=來源物件 {0} 已在複製流程 {1} 中使用。
#XMSG
deployTargetSourceObjectInUse=來源物件 {0} 已在複製流程 {1} 中使用。目標物鍵 {2} 已在複製流程 {3} 中使用。
#XMSG
deployReplicationFlowCheckError=驗證複製流程時發生錯誤：{0}
#XMSG
preDeployTargetObjectInUse=目標物件 {0} 已用於複製流程 {1}，且兩個不同複製流程中的目標物件不可相同。請選擇其他目標物件並再試一次。
#XMSG
runInProgressInfo=複製流程已在執行中。
#XMSG
deploySignavioTargetFFDisabled=目前無法部署具備 SAP Signavio 作為目標的複製流程，因為我們正於此功能執行維護作業。
#XMSG
deployHanaViewAsSourceFFDisabled=目前無法針對所選來源連線，部署具有檢視作為來源物件複製流程。請稍後再試一次。
#XMSG
deployMsOneLakeTargetFFDisabled=目前無法部署具備 MS OneLake 作為目標的複製流程，因為我們正於此功能執行維護作業。
#XMSG
deploySFTPTargetFFDisabled=目前無法部署具備 SFTP 作為目標的複製流程，因為我們正於此功能執行維護作業。
#XMSG
deploySFTPSourceFFDisabled=目前無法部署具備 SFTP 作為來源的複製流程，因為我們正於此功能執行維護作業。
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=技術名稱
#XFLD
businessNameInRenameTarget=業務名稱
#XTOL
renametargetDialogTitle=重新命名目標物件
#XBUT
targetRenameButton=重新命名
#XBUT
targetRenameCancel=取消
#XMSG
mandatoryTargetName=您必須輸入名稱。
#XMSG
dwcSpecialChar=_ (底線) 是唯一允許的特殊字元。
#XMSG
dwcWithDot=目標表格名稱可包含字母、數字、底線 (_) 和句點 (.)。第一個字元必須是字母、數字或底線 (而非句點)。
#XMSG
nonDwcSpecialChar=允許的特殊字元為 _ (底線) - (連字號) . (點)
#XMSG
firstUnderscorePattern=名稱不得以 _ (底線) 開頭。

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}：檢視 SQL 建立表格陳述式
#XMSG
sqlDialogMaxPKWarning=Google BigQuery 中支援最多 16 個主鍵值，但來源物件有較大數量。因此未在此陳述式中定義主鍵值。
#XMSG
sqlDialogIncomptiblePKTypeWarning=一或多個來源欄的資料類型無法定義為 Google BigQuery 中的主鍵值。因此，此案例未定義主鍵值。Google BigQuery 中僅下列資料類型可具有主鍵值：BIGNUMERIC、BOOLEAN、DATE、DATETIME、INT64、NUMERIC、STRING、TIMESTAMP
#XBUT
copyAndCloseDDL=複製並關閉
#XBUT
closeDDL=關閉
#XMSG
copiedToClipboard=已複製到剪貼簿


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=物件 ''{0}'' 無法成為工作細項鏈一部份，因為此物件沒有結束 (由於包含載入類型為「初始和差異/僅差異」的物件)。
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=物件 ''{0}'' 無法成為工作細項鏈一部份，因為此物件沒有結束 (由於包含載入類型為「初始和差異」的物件)。
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=無法將物件 "{0}" 新增至工作細項鏈。


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=有未儲存的目標物件，請再次儲存。{0}{0}此功能的行為已更改：過去在部署複製流程時，只會在目標環境中建立目標物件。{0}現在物件於儲存複製流程前已建立。您的複製流程在此更改前建立，且包含新物件。{0}您必須再次儲存複製流程後部署，才會正確包含新物件。
#XMSG
confirmChangeContentTypeMessage=您即將更改內容類型。執行此動作將刪除所有現有投影。

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=主旨名稱
#XFLD
schemaDialogVersionName=綱要版本
#XFLD
includeTechKey=包含技術金鑰
#XFLD
segementButtonFlat=平面
#XFLD
segementButtonNested=巢狀
#XMSG
subjectNamePlaceholder=搜尋主旨名稱

#XMSG
@EmailNotificationSuccess=已儲存執行時期電子郵件通知的組態。

#XFLD
@RuntimeEmailNotification=執行時期電子郵件通知

#XBTN
@TXT_SAVE=儲存


