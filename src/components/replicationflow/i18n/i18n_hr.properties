#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Tok replikacije

#XFLD: Edit Schema button text
editSchema=Uredi shemu

#XTIT : Properties heading
configSchema=Kon<PERSON>guriraj shemu

#XFLD: save changed button text
applyChanges=Primijeni promjene


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Odaberi izvornu vezu
#XFLD
sourceContainernEmptyText=Odaberi spremnik
#XFLD
targetConnectionEmptyText=Odaberi ciljnu vezu
#XFLD
targetContainernEmptyText=Odaberi spremnik
#XFLD
sourceSelectObjectText=Odaberi izvorni objekt
#XFLD
sourceObjectCount=Izvorni objekti ({0})
#XFLD
targetObjectText=Ciljni objekti
#XFLD
confluentBrowseContext=Odaberi kontekst
#XBUT
@retry=Ponovno pokušaj
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Nadogradnja zakupca u tijeku.

#XTOL
browseSourceConnection=Pregledaj izvornu vezu
#XTOL
browseTargetConnection=Pregledaj ciljnu vezu
#XTOL
browseSourceContainer=Pregledaj izvorni spremnik
#XTOL
browseAndAddSourceDataset=Dodaj izvorne objekte
#XTOL
browseTargetContainer=Pregledaj ciljni spremnik
#XTOL
browseTargetSetting=Pregledaj ciljne postavke
#XTOL
browseSourceSetting=Pregledaj izvorne postavke
#XTOL
sourceDatasetInfo=Informacije
#XTOL
sourceDatasetRemove=Uklanjanje
#XTOL
mappingCount=Ovo predstavlja ukupni broj mapiranja/izraza koji se ne zasnivaju na nazivu.
#XTOL
filterCount=Ovo predstavlja ukupni broj uvjeta filtra.
#XTOL
loading=Učitavanje...
#XCOL
deltaCapture=Snimanje delte
#XCOL
deltaCaptureTableName=Tablica snimanja delte
#XCOL
loadType=Učitaj tip
#XCOL
deleteAllBeforeLoading=Izbrišite sve prije učitavanja
#XCOL
transformationsTab=Projekcije
#XCOL
settingsTab=Postavke

#XBUT
renameTargetObjectBtn=Preimenuj ciljni objekt
#XBUT
mapToExistingTargetObjectBtn=Mapiraj na postojeći ciljni objekt
#XBUT
changeContainerPathBtn=Promijeni stazu spremnika
#XBUT
viewSQLDDLUpdated=Prikaži SQL naredbu Stvori tablicu
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Izvorni objekt ne podržava snimanje delte, ali odabrani ciljni objekt ima omogućenu mogućnost snimanja delte.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Ciljni objekt ne može se upotrijebiti jer je omogućeno delta snimanje,{0} s obzirom da izvorni objekt ne podržava delta snimanje.{1}Možete odabrati drugi ciljni objekt koji ne podržava delta snimanje.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Ciljni objekt s istim nazivom već postoji. Međutim, ne može se upotrijebiti{0}jer je omogućeno delta snimanje, s obzirom da izvorni objekt ne{0}podržava delta snimanje.{1}Možete unijeti naziv postojećeg ciljnog objekta koji ne{0}podržava delta snimanje ili unijeti naziv koji još ne postoji.
#XBUT
copySQLDDLUpdated=Kopiraj SQL naredbu Stvori tablicu
#XMSG
targetObjExistingNoCDCColumnUpdated=Postojeće tablice u Google BigQueryju moraju uključivati sljedeće stupce za snimanje podataka promjene (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Sljedeći izvorni objekti nisu podržani jer nemaju primarni ključ ili upotrebljavaju vezu koja ne ispunjava uvjete dohvaćanja primarnog ključa:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Za moguće rješenje pogledajte SAP KBA 3531135.
#XLST: load type list values
initial=Samo početno
@emailUpdateError=Pogreška pri ažuriranju popisa obavijesti e-poštom.

#XLST
initialDelta=Početno i delta

#XLST
deltaOnly=Samo delta
#XMSG
confluentDeltaLoadTypeInfo=Za izvor Confluent Kafka podržan je samo tip učitavanja Početno i delta
#XMSG
confirmRemoveReplicationObject=Potvrđujete li da želite izbrisati replikaciju?
#XMSG
confirmRemoveReplicationTaskPrompt=Ova će radnja izbrisati postojeće replikacije. Želite li nastaviti?
#XMSG
confirmTargetConnectionChangePrompt=Ova će radnja ponovo postaviti ciljnu vezu, ciljni spremnik i izbrisati sve ciljne objekte. Želite li nastaviti?
#XMSG
confirmTargetContainerChangePrompt=Ova će radnja ponovo postaviti ciljni spremnik i izbrisati sve postojeće ciljne objekte. Želite li nastaviti?
#XMSG
confirmRemoveTransformObject=Potvrđujete li da želite izbrisati projekciju {0}?
#XMSG
ErrorMsgContainerChange=Došlo je do pogreške pri promjeni staze spremnika.
#XMSG
infoForUnsupportedDatasetNoKeys=Sljedeći izvorni objekti nisu podržani jer nemaju primarni ključ:
#XMSG
infoForUnsupportedDatasetView=Sljedeći izvorni objekti tipa Prikazi nisu podržani:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Sljedeći izvorni objekt nije podržan jer je to SQL prikaz koji sadržava ulazne parametre:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Sljedeći izvorni objekti nisu podržani jer je izdvajanje za njih onemogućeno:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Za veze s Confluentom jedini dopušteni formati serijalizacije jesu AVRO i JSON. Sljedeći objekti nisu podržani jer upotrebljavaju drukčiji format serijalizacije:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Nije moguće dohvatiti shemu za sljedeće objekte. Odaberite odgovarajući kontekst ili verificirajte konfiguraciju registra shema
#XTOL: warning dialog header on deleting replication task
deleteHeader=Brisanje
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Postavka Izbriši sve prije učitavanja nije podržana za Google BigQuery.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Postavka Izbriši sve prije učitavanja briše i ponovo stvara objekt (temu) prije svake replikacije. Time se brišu i sve dodijeljene poruke.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Postavka Izbriši sve prije učitavanja nije podržana za ovaj tip cilja.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Tehnički naziv
#XCOL
connBusinessName=Poslovni naziv
#XCOL
connDescriptionName=Opis
#XCOL
connType=Tip
#XMSG
connTblNoDataFoundtxt=Veze nisu pronađene
#XMSG
connectionError=Došlo je do pogreške pri dohvaćanju veza.
#XMSG
connectionCombinationUnsupportedErrorTitle=Kombinacija veza nije podržana
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Replikacija iz {0} u {1} trenutačno nije podržana.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Kombinacija tipa veze nije podržana
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Replikacija iz veze s tipom veze SAP HANA Cloud, datoteke jezera podataka u {0} nije podržana. Možete replicirati samo u SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Odaberi
#XBUT
containerCancelBtn=Odustani
#XTOL
containerSelectTooltip=Odabir
#XTOL
containerCancelTooltip=Odustajanje
#XMSG
containerContainerPathPlcHold=Staza spremnika
#XFLD
containerContainertxt=Spremnik
#XFLD
confluentContainerContainertxt=Kontekst
#XMSG
infoMessageForSLTSelection=Samo je /SLT/ID masovnog prijenosa dopušten kao spremnik. Odaberite ID masovnog prijenosa pod SLT (ako je dostupno) i kliknite Pošalji.
#XMSG
msgFetchContainerFail=Došlo je do pogreške pri dohvaćanju podataka spremnika.
#XMSG
infoMessageForSLTHidden=Ova veza ne podržava mape SLT pa se one ne pojavljuju na popisu u nastavku.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Odaberite spremnik koji u sebi sadržava podmape.
#XMSG
sftpIncludeSubFolderText=Netočno
#XMSG
sftpIncludeSubFolderTextNew=Ne

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Još nema mapiranja filtra)
#XMSG
failToFetchRemoteMetadata=Došlo je do pogreške pri dohvaćanju metapodataka.
#XMSG
failToFetchData=Došlo je do pogreške pri dohvaćanju postojećeg cilja.
#XCOL
@loadType=Tip učitavanja
#XCOL
@deleteAllBeforeLoading=Izbrišite sve prije učitavanja

#XMSG
@loading=Učitavanje...
#XFLD
@selectSourceObjects=Odaberi izvorne objekte
#XMSG
@exceedLimit=Odjednom ne možete uvesti više od ovoliko objekata: {0}. Poništite odabir najmanje ovoliko objekata: {1}.
#XFLD
@objects=Objekti
#XBUT
@ok=OK
#XBUT
@cancel=Odustani
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Sljedeće
#XBUT
btnAddSelection=Dodaj odabir
#XTOL
@remoteFromSelection=Ukloni iz odabira
#XMSG
@searchInForSearchField=Tražite u {0}

#XCOL
@name=Tehnički naziv
#XCOL
@type=Tip
#XCOL
@location=Lokacija
#XCOL
@label=Poslovni naziv
#XCOL
@status=Status

#XFLD
@searchIn=Traži u:
#XBUT
@available=Dostupno
#XBUT
@selection=Odabir

#XFLD
@noSourceSubFolder=Tablice i prikazi
#XMSG
@alreadyAdded=Već postoji u grafikonu
#XMSG
@askForFilter=Postoji više od ovoliko stavki: {0}. Unesite niz filtra za sužavanje broja stavki.
#XFLD: success label
lblSuccess=Uspjeh
#XFLD: ready label
lblReady=Spremno
#XFLD: failure label
lblFailed=Nije uspjelo
#XFLD: fetching status label
lblFetchingDetail=Dohvaćanje pojedinosti

#XMSG Place holder text for tree filter control
filterPlaceHolder=Upišite tekst za filtriranje objekata najviše razine
#XMSG Place holder text for server search control
serverSearchPlaceholder=Upišite i pritisnite Enter za pretraživanje
#XMSG
@deployObjects=Uvoz ovoliko objekata: {0}...
#XMSG
@deployObjectsStatus=Broj objekata koji su uvezeni: {0}. Broj objekata koje nije moguće uvesti: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Otvaranje lokalnog preglednika spremišta nije uspjelo.
#XMSG
@openRemoteSourceBrowserError=Dohvaćanje izvornih objekata nije uspjelo.
#XMSG
@openRemoteTargetBrowserError=Dohvaćanje ciljnih objekata nije uspjelo.
#XMSG
@validatingTargetsError=Došlo je do pogreške pri validaciji ciljeva.
#XMSG
@waitingToImport=Spremno za uvoz

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Maksimalni broj objekata prekoračen je. Odaberite maksimalno 500 objekata za jedan tok replikacije.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Tehnički naziv
#XFLD
sourceObjectBusinessName=Poslovni naziv
#XFLD
sourceNoColumns=Broj stupaca
#XFLD
containerLbl=Spremnik

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Morate odabrati izvornu vezu za tok replikacije.
#XMSG
validationSourceContainerNonExist=Morate odabrati spremnik za izvornu vezu.
#XMSG
validationTargetNonExist=Morate odabrati ciljnu vezu za tok replikacije.
#XMSG
validationTargetContainerNonExist=Morate odabrati spremnik za ciljnu vezu.
#XMSG
validationTruncateDisabledForObjectTitle=Replikacija pohrana objekata.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Replikacija u pohranu u oblaku moguća je samo ako je postavljena mogućnost Izbriši sve prije učitavanja ili ciljni objekt ne postoji u cilju.{0}{0} Kako biste omogućili replikaciju objekata za koje mogućnost Izbriši sve prije učitavanja nije postavljena, provjerite da ciljni objekt ne postoji u sustavu prije izvođenja toka replikacije.
#XMSG
validationTaskNonExist=Morate imati najmanje jednu replikaciju u toku replikacije.
#XMSG
validationTaskTargetMissing=Morate imati cilj za replikaciju s izvorom: {0}
#XMSG
validationTaskTargetIsSAC=Odabrani cilj je artefakt SAC-a: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Odabrani cilj nije podržana lokalna tablica: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Objekt ovog naziva već postoji u cilju. Međutim, taj objekt nije moguće upotrijebiti kao ciljni objekt za tok replikacije u lokalno spremište, jer se ne radi o lokalnoj tablici.
#XMSG
validateSourceTargetSystemDifference=Morate odabrati različite kombinacije izvorne i ciljne veze i spremnika za tok replikacije.
#XMSG
validateDuplicateSources=jedna ili više aplikacija imaju dvostruke nazive izvornog objekta: {0}.
#XMSG
validateDuplicateTargets=jedna ili više aplikacija imaju dvostruke nazive ciljnog objekta: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=Izvorni objekt {0} ne podržava snimanje delte, a ciljni objekt {1} ga podržava. Morate ukloniti replikaciju.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Morate odabrati tip učitavanja "Početno i delta" za replikaciju s nazivom ciljnog objekta {0}.
#XMSG
validationAutoRenameTarget=Ciljni stupci preimenovani.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Dodana je automatska projekcija i sljedeći ciljni stupci preimenovani su kako bi se omogućila replikacija u cilj:{1}{1} {0} {1}{1}To je zbog jednog od sljedećih razloga:{1}{1}{2} nepodržani znakovi{1}{2} rezervirani prefiks
#XMSG
validationAutoRenameTargetDescriptionUpdated=Dodana je automatska projekcija i sljedeći ciljni stupci preimenovani su za dopuštanje replikacije u Google BigQuery:{1}{1} {0} {1}{1}to je zbog jednog od sljedećih razloga:{1}{1}{2} rezervirani naziv stupca{1}{2} nepodržani znakovi{1}{2} rezervirani prefiks
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Dodana je automatska projekcija i sljedeći ciljni stupci preimenovani su za dopuštanje replikacije u Confluentu{1}{1} {0} {1}{1}to je zbog jednog od sljedećih razloga:{1}{1}{2} rezervirani naziv stupca{1}{2} nepodržani znakovi{1}{2} rezervirani prefiks
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Dodana je automatska projekcija i sljedeći ciljni stupci preimenovani su za dopuštanje replikacije u cilju:{1}{1} {0} {1}{1}to je zbog jednog od sljedećih razloga:{1}{1}{2} rezervirani naziv stupca{1}{2} nepodržani znakovi{1}{2} rezervirani prefiks
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Ciljni objekt preimenovan je.
#XMSG
autoRenameInfoDesc=Ciljni objekt preimenovan je jer je sadržavao nepodržane znakove. Podržani su samo sljedeći znakovi:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(točka){0}{1}_(donja crta){0}{1}-(crtica)
#XMSG
validationAutoTargetTypeConversion=Tipovi ciljnih podataka promijenjeni.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Za sljedeće ciljne stupce promijenjeni su tipovi ciljnih podataka jer nisu podržani u Google BigQueryju:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Za sljedeće ciljne stupce promijenjeni su tipovi ciljnih podataka jer izvorni tipovi podataka nisu podržani u ciljnoj vezi:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Skrati nazive ciljnih stupaca.
#XMSG
validationMaxCharLengthGBQTargetDescription=Nazivi stupaca u Google BigQueryju mogu upotrebljavati maksimalno 300 znakova. Upotrijebite projekciju za skraćivanje sljedećih naziva ciljnih stupaca:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Primarni ključevi neće biti stvoreni.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=U Google BigQueryju podržano je maksimalno 16 primarnih ključeva, ali izvorni objekt ima veći broj primarnih ključeva. U ciljnom objektu neće biti stvoren nijedan od primarnih ključeva.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Jedan ili više izvornih stupaca imaju tipove podataka koji se ne mogu definirati kao primarni ključevi u Google BigQueryju. Nijedan primarni ključ neće se stvoriti u ciljnom objektu.{0}{0} Sljedeći ciljni tipovi podataka kompatibilni su s tipovima podataka Google BigQueryja za koje se može definirati primarni ključ:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Definirajte jedan ili više stupaca kao primarni ključ.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Kako biste to učinili morate definirati jedan ili više stupaca kao dijalog izvorne sheme upotrebe primarnog ključa. 
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Definirajte jedan ili više stupaca kao primarni ključ.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Morate definirati jedan ili više stupaca kao primarni ključ koji odgovara ograničenjima primarnog ključa za vaš izvorni objekt. Da biste to učinili, idite na Konfiguriraj shemu u svojstvima izvornog objekta.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Unesite valjanu maksimalnu vrijednost particije.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Maksimalna vrijednost particije mora biti ≥ 1 i ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Definirajte jedan ili više stupaca kao primarni ključ.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Da biste replicirali objekt, morate definirati jedan ili više ciljnih stupaca kao primarni ključ. Za to upotrijebite projekciju.
#XMSG
validateHDLFNoPKExistingDatasetError=Definirajte jedan ili više stupaca kao primarni ključ.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Za repliciranje podataka na postojeći ciljni objekt, on mora imati jedan ili više stupaca koji su definirani kao primarni ključ. {0} Imate sljedeće mogućnosti za definiranje jednog ili više stupaca kao primarnog ključa: {0} {1} Upotrijebite uređivač lokalne tablice da promijenite postojeći ciljni objekt. Zatim ponovo učitajte tok replikacije.{0}{1} Preimenujte ciljni objekt u toku replikacije. Ovo će stvoriti novi objekt čim se pokrene izvođenje. Nakon preimenovanja, možete definirati jedan ili više stupaca kao primarni ključ u projekciji.{0}{1} Mapirajte objekt na drugi postojeći ciljni objekt u kojem je jedan ili više stupaca već definirano kao primarni ključ.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Odabrani cilj već postoji u spremištu: "{0}".
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Nazivima tablica snimanja delte već se koriste druge tablice u spremištu: {0} morate preimenovati te ciljne objekte kako biste osigurali da su pridruženi nazivi tablica snimanja delte jedinstveni prije nego što spremite tok replikacije.
#XMSG
validateConfluentEmptySchema=Definirajte shemu
#XMSG
validateConfluentEmptySchemaDescUpdated=Izvorna tablica nema shemu. Odaberite Konfiguriraj shemu kako biste je definirali
#XMSG
validationCSVEncoding=Nevaljano kodiranje CSV
#XMSG
validationCSVEncodingDescription=Kodiranje CSV ovog zadatka nije valjano.
#XMSG
validateConfluentEmptySchema=Odaberite kompatibilan tip ciljnih podataka
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Odaberite kompatibilan tip ciljnih podataka
#XMSG
globalValidateTargetDataTypeDesc=Došlo je do pogreške pri mapiranju stupaca. Idite na Projekcija i provjerite jesu li svi izvorni stupci mapirani s jedinstvenim stupcem, sa stupcem koji ima kompatibilan tip podataka i da su svi definirani izrazi valjani.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Dvostruki nazivi stupaca.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Dvostruki nazivi stupaca nisu podržani. Ispravite ih u dijalogu projekcija. Sljedeći ciljni objekti imaju dvostruke nazive stupaca: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Dvostruki nazivi stupaca.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Dvostruki nazivi stupaca nisu podržani. Ispravite ih u dijalogu projekcija. Sljedeći ciljni objekti imaju dvostruke nazive stupaca: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Možda postoje nedosljednosti u podacima.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Tip učitavanja Samo delta neće uzeti u obzir promjene izvršene u izvoru između zadnjeg spremanja i sljedećeg izvođenja.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Promijenite tip učitavanja u "Početno".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Repliciranje objekata zasnovanih na ABAP-u koji nemaju primarni ključ moguće je samo za tip učitavanja "Samo početno".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Onemogućite snimanje delte.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Kako biste s pomoću tipa veze s udaljenim izvorom ABAP-a replicirali objekt koji nema primarni ključ, prvo morate onemogućiti snimanje delte za ovu tablicu.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Promijenite ciljni objekt.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Ciljni objekt ne može se upotrijebiti jer je omogućeno snimanje delte. Možete preimenovati ciljni objekt i zatim isključiti snimanje delte za novi (preimenovani) objekt ili mapirati izvorni objekt na ciljni objekt za koji je snimanje delte onemogućeno.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Promijenite ciljni objekt.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Ciljni objekt ne može se upotrijebiti jer nema potreban tehnički stupac __load_package_id. Možete preimenovati ciljni objekt nazivom koji ne postoji još. Sustav zatim stvara novi objekt koji ima istu definiciju kao izvorni objekt i sadržava tehnički stupac. Druga je mogućnost da mapirate ciljni objekt na postojeći objekt koji ima potreban tehnički stupac (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Promijenite ciljni objekt.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Ciljni objekt ne može se upotrijebiti jer nema potreban tehnički stupac __load_record_id. Možete preimenovati ciljni objekt nazivom koji ne postoji još. Sustav zatim stvara novi objekt koji ima istu definiciju kao izvorni objekt i sadržava tehnički stupac. Druga je mogućnost da mapirate ciljni objekt na postojeći objekt koji ima potreban tehnički stupac (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Promijenite ciljni objekt.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Ciljni objekt ne može se upotrijebiti jer tip podataka njegovog tehničkog stupca __load_record_id nije "string(44)". Možete preimenovati ciljni objekt nazivom koji ne postoji još. Sustav zatim stvara novi objekt koji ima istu definiciju kao izvorni objekt i stoga točan tip podataka. Druga je mogućnost da mapirate ciljni objekt na postojeći objekt koji ima potreban tehnički stupac (__load_record_id) s točnim tipom podataka.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Promijenite ciljni objekt.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Ciljni objekt ne može se upotrijebiti jer ima primarni ključ dok izvorni objekt nema nijedan. Možete preimenovati ciljni objekt nazivom koji ne postoji još. Sustav zatim stvara novi objekt koji ima istu definiciju kao izvorni objekt i stoga nema primarni ključ. Druga je mogućnost da mapirate ciljni objekt na postojeći objekt koji ima potreban tehnički stupac (__load_package_id) i nema primarni ključ.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Promijenite ciljni objekt.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Ciljni objekt ne može se upotrijebiti jer ima primarni ključ dok izvorni objekt nema nijedan. Možete preimenovati ciljni objekt nazivom koji ne postoji još. Sustav zatim stvara novi objekt koji ima istu definiciju kao izvorni objekt i stoga nema primarni ključ. Druga je mogućnost da mapirate ciljni objekt na postojeći objekt koji ima potreban tehnički stupac (__load_record_id) i nema primarni ključ.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Promijenite ciljni objekt.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Ciljni objekt ne može se upotrijebiti jer tip podataka njegovog tehničkog stupca __load_package_id nije "binary(>=256)". Možete preimenovati ciljni objekt nazivom koji ne postoji još. Sustav zatim stvara novi objekt koji ima istu definiciju kao izvorni objekt i stoga točan tip podataka. Druga je mogućnost da mapirate ciljni objekt na postojeći objekt koji ima potreban tehnički stupac (__load_package_id) s točnim tipom podataka.
#XMSG
validationAutoRenameTargetDPID=Ciljni stupci preimenovani.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Uklonite izvorni objekt
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Izvorni objekt nema stupac ključa, što nije podržano u ovom kontekstu.
#XMSG
validationAutoRenameTargetDPIDDescription=Dodana je automatska projekcija i sljedeći ciljni stupci preimenovani su za dopuštanje replikacije iz izvora ABAP-a bez ključeva:{1}{1} {0} {1}{1}to je zbog jednog od sljedećih razloga:{1}{1}{2} Rezervirani naziv stupca{1}{2} Nepodržani znakovi{1}{2} Rezervirani prefiks
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Replikacija u {0}
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Spremanje i uvođenje tokova replikacije koji imaju {0} kao cilj trenutačno nije moguće jer izvodimo održavanje te funkcije.
#XMSG
TargetColumnSkippedLTF=Ciljni stupac preskočen je.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Ciljni stupac preskočen je zbog nepodržanog tipa podataka. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Vremenski stupac kao primarni ključ.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Izvorni objekt ima vremenski stupac kao primarni ključ, što nije podržano u ovom kontekstu.
#XMSG
validateNoPKInLTFTarget=Primarni ključ nedostaje.
#XMSG
validateNoPKInLTFTargetDescription=Primarni ključ nije definiran u cilju, što nije podržano u ovom kontekstu.
#XMSG
validateABAPClusterTableLTF=ABAP klasterska tablica
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Izvorni objekt je ABAP klasterska tablica, što nije podržano u ovom kontekstu.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Čini se da niste još dodali podatke.
#YINS
welcomeText2=Kako biste pokrenuli svoj tok replikacije, odaberite vezu i izvorni objekt s lijeve strane.

#XBUT
wizStep1=Odaberi izvornu vezu
#XBUT
wizStep2=Odaberi izvorni spremnik
#XBUT
wizStep3=Dodaj izvorne objekte

#XMSG
limitDataset=Dosegnut je maksimalni broj objekata. Uklonite postojeće objekte kako biste dodali nove ili stvorite novi tok replikacije.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Tok replikacije na ovu ciljnu vezu koja nije SAP-ova ne može se pokrenuti jer za ovaj mjesec nema dostupnog izlaznog obujma.
#XMSG
premiumOutBoundRFAdminWarningMsg=Administrator može povećati izlazne blokove Premium za ovog zakupca i tako učiniti izlazni obujam dostupnim za ovaj mjesec.
#XMSG
messageForToastForDPIDColumn2=Novi stupac dodan cilju za ovoliko objekata: {0} - potrebno za rukovanje dvostrukim zapisima s izvornim objektima zasnovanim na ABAP-u koji nemaju primarni ključ.
#XMSG
PremiumInboundWarningMessage=Zavisno od broja tokova replikacije i količini podataka koji se repliciraju, SAP HANA resursi{0}potrebni za replikaciju podataka putem {1} mogu prekoračiti raspoloživi kapacitet za vašeg zakupca.
#XMSG
PremiumInboundWarningMsg=Zavisno od broja tokova replikacije i količini podataka koji se repliciraju, SAP HANA resursi{0}potrebni za replikaciju podataka putem "{1}" mogu prekoračiti raspoloživi kapacitet za vašeg zakupca.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Unesite naziv projekcije.
#XMSG
emptyTargetColumn=Unesite naziv ciljnog stupca.
#XMSG
emptyTargetColumnBusinessName=Unesite poslovni naziv ciljnog stupca.
#XMSG
invalidTransformName=Unesite naziv projekcije.
#XMSG
uniqueColumnName=Preimenujte ciljni stupac.
#XMSG
copySourceColumnLbl=Kopiraj stupce iz izvornog objekta
#XMSG
renameWarning=Pripazite da tijekom preimenovanja ciljne tablice odaberete jedinstven naziv. Ako tablica s novim nazivom već postoji u prostoru, upotrijebit će definiciju te tablice.

#XMSG
uniqueColumnBusinessName=Preimenujte poslovni naziv ciljnog stupca.
#XMSG
uniqueSourceMapping=Odaberite drukčiji izvorni stupac.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=Izvorni stupac {0} već upotrebljavaju sljedeći ciljni stupci:{1}{1}{2}{1}{1} Za taj ciljni stupac ili za ostale ciljne stupce odaberite izvorni stupac koji se još ne upotrebljava, kako biste spremili projekciju.
#XMSG
uniqueColumnNameDescription=Naziv ciljnog stupca koji ste unijeli već postoji. Kako biste mogli spremiti projekciju, trebate unijeti jedinstveni naziv stupca.
#XMSG
uniqueColumnBusinessNameDesc=Poslovni naziv ciljnog stupca već postoji. Da biste spremili projekciju, morate unijeti jedinstveni poslovni naziv stupca.
#XMSG
emptySource=Odaberite ciljni stupac ili unesite konstantu.
#XMSG
emptySourceDescription=Kako biste stvorili valjan unos mapiranja, trebate odabrati izvorni stupac ili unijeti vrijednost konstante.
#XMSG
emptyExpression=Definirajte mapiranje.
#XMSG
emptyExpressionDescription1=Odaberite izvorni stupac u koji želite mapirati ciljni stupac ili odaberite potvrdni okvir u stupcu {0} Funkcije / Konstante {1}. {2} {2} Funkcije se unose automatski prema ciljnom tipu podataka. Vrijednosti konstante mogu se unijeti ručno.
#XMSG
numberExpressionErr=Unesite broj.
#XMSG
numberExpressionErrDescription=Odabrali ste numerički tip podataka. To znači da ovdje možete unijeti samo brojeve, plus decimalni zarez ako je primjenjivo. Ne upotrebljavajte jednostruke navodnike.
#XMSG
invalidLength=Unesite valjanu vrijednost dužine.
#XMSG
invalidLengthDescription=Dužina tipa podataka mora biti jednaka ili veća od dužine izvornog stupca i može biti između 1 i 5000.
#XMSG
invalidMappedLength=Unesite valjanu vrijednost dužine.
#XMSG
invalidMappedLengthDescription=Dužina tipa podataka mora biti jednaka ili veća od dužine izvornog stupca {0} i može biti između 1 i 5000.
#XMSG
invalidPrecision=Unesite valjanu vrijednost preciznosti.
#XMSG
invalidPrecisionDescription=Preciznost definira ukupan broj znamenki. Ljestvica definira broj znamenki nakon decimalnog zareza i može biti između 0 i preciznosti.{0}{0} Primjeri: {0}{1} preciznost 6, ljestvica 2 odgovara brojevima poput 234,56.{0}{1} Preciznost 6, ljestvica 6 odgovara brojevima poput 0,123546.{0} {0} Preciznost i ljestvica za cilj moraju biti kompatibilni s preciznošću i ljestvicom za izvor tako da sve znamenke iz izvora stanu u ciljno polje. Na primjer, ako imate preciznost 6 i ljestvicu 2 u izvoru (i posljedično znamenke osim 0 prije decimalnog zareza), ne možete imati preciznost 6 i ljestvicu 6 u cilju.
#XMSG
invalidPrimaryKey=Unesite barem jedan primarni ključ.
#XMSG
invalidPrimaryKeyDescription=Primarni ključ nije definiran za ovu shemu.
#XMSG
invalidMappedPrecision=Unesite valjanu vrijednost preciznosti.
#XMSG
invalidMappedPrecisionDescription1=Preciznost definira ukupan broj znamenki. Ljestvica definira broj znamenki nakon decimalnog zareza i može biti između 0 i preciznosti.{0}{0} Primjeri:{0}{1} Preciznost 6, ljestvica 2 odgovara brojevima poput 1234,56.{0}{1} Preciznost 6, ljestvica 6 odgovara brojevima poput 0,123546.{0}{0}Preciznost tipa podataka mora biti jednaka ili veća od preciznosti izvora ({2}).
#XMSG
invalidScale=Unesite valjanu vrijednost ljestvice.
#XMSG
invalidScaleDescription=Preciznost definira ukupan broj znamenki. Ljestvica definira broj znamenki nakon decimalnog zareza i može biti između 0 i preciznosti.{0}{0} Primjeri: {0}{1} preciznost 6, ljestvica 2 odgovara brojevima poput 234,56.{0}{1} Preciznost 6, ljestvica 6 odgovara brojevima poput 0,123546.{0} {0} Preciznost i ljestvica za cilj moraju biti kompatibilni s preciznošću i ljestvicom za izvor tako da sve znamenke iz izvora stanu u ciljno polje. Na primjer, ako imate preciznost 6 i ljestvicu 2 u izvoru (i posljedično znamenke osim 0 prije decimalnog zareza), ne možete imati preciznost 6 i ljestvicu 6 u cilju.
#XMSG
invalidMappedScale=Unesite valjanu vrijednost ljestvice.
#XMSG
invalidMappedScaleDescription1=Preciznost definira ukupan broj znamenki. Ljestvica definira broj znamenki nakon decimalnog zareza i može biti između 0 i preciznosti.{0}{0} Primjeri:{0}{1} Preciznost 6, ljestvica 2 odgovara brojevima poput 1234,56.{0}{1} Preciznost 6, ljestvica 6 odgovara brojevima poput 0,123546.{0}{0}Ljestvica tipa podataka mora biti jednaka ili veća od izvora ({2}).
#XMSG
nonCompatibleDataType=Odaberite kompatibilan tip ciljnih podataka.
#XMSG
nonCompatibleDataTypeDescription1=Tip podataka koji navedete ovdje mora biti kompatibilan s izvornim tipom podataka ({0}). {1}{1} Primjer: ako vaš izvorni stupac ima tip podataka Niz i sadržava slova, za cilj ne možete upotrebljavati decimalni tip podataka.
#XMSG
invalidColumnCount=Odaberite izvorni stupac.
#XMSG
ObjectStoreInvalidScaleORPrecision=Unesite valjanu vrijednost za preciznost i ljestvicu.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=Prva vrijednost jest preciznost, kojom se definira ukupan broj znamenki. Druga vrijednost jest ljestvica, kojom se definiraju znamenke nakon decimalnog zareza. Unesite ciljnu vrijednost ljestvice koja je veća od izvorne vrijednosti ljestvice i osigurajte da je razlika između vrijednosti unesenih za ciljnu ljestvicu i preciznost veća od razlike između vrijednosti za izvornu ljestvicu i preciznost.
#XMSG
InvalidPrecisionORScale=Unesite valjanu vrijednost za preciznost i ljestvicu.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=Prva vrijednost je preciznost koja definira ukupan broj znamenki. Druga vrijednost je ljestvica koja definira znamenke nakon decimalnog zareza.{0}{0}Budući da izvorni tip podataka nije podržan u Google BigQueryju, pretvara se u ciljni tip podataka DECIMAL. U tom se slučaju preciznost može definirati samo između 38 i 76, a ljestvica između 9 i 38. Osim toga, rezultat preciznosti minus ljestvice, što predstavlja znamenke prije decimalnog zareza, mora biti između 29 i 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=Prva vrijednost je preciznost koja definira ukupan broj znamenki. Druga vrijednost je ljestvica koja definira znamenke nakon decimalnog zareza.{0}{0}Budući da izvorni tip podataka nije podržan u Google BigQueryju, pretvara se u ciljni tip podataka DECIMAL. U tom se slučaju preciznost mora definirati kao 20 ili više. Osim toga, rezultat preciznosti minus ljestvice, što odražava znamenke prije decimalnog zareza, mora biti 20 ili veći.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=Prva vrijednost je preciznost koja definira ukupan broj znamenki. Druga vrijednost je ljestvica koja definira znamenke nakon decimalnog zareza.{0}{0}Budući da izvorni tip podataka nije podržan u cilju, pretvara se u ciljni tip podataka DECIMAL. U tom se slučaju preciznost mora definirati bilo kojim bojem većim od ili jednakim 1 i manjim od ili jednakim 38 te ljestvicom manjom od ili jednakom preciznosti.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=Prva vrijednost je preciznost koja definira ukupan broj znamenki. Druga vrijednost je ljestvica koja definira znamenke nakon decimalnog zareza.{0}{0}Budući da izvorni tip podataka nije podržan u cilju, pretvara se u ciljni tip podataka DECIMAL. U tom se slučaju preciznost mora definirati kao 20 ili više. Osim toga, rezultat preciznosti minus ljestvice, što odražava znamenke prije decimalnog zareza, mora biti 20 ili veći.
#XMSG
invalidColumnCountDescription=Kako biste stvorili valjan unos mapiranja, trebate odabrati izvorni stupac ili unijeti vrijednost konstante.
#XMSG
duplicateColumns=Preimenujte ciljni stupac.
#XMSG
duplicateGBQCDCColumnsDesc=Naziv ciljnog stupca rezerviran je u Google BigQueryju. Trebate ga preimenovati kako biste mogli spremiti projekciju.
#XMSG
duplicateConfluentCDCColumnsDesc=Naziv ciljnog stupca rezerviran je u Confluentu. Trebate ga preimenovati kako biste mogli spremiti projekciju.
#XMSG
duplicateSignavioCDCColumnsDesc=Naziv ciljnog stupca rezerviran je u rješenjima SAP Signavio. Trebate ga preimenovati kako biste mogli spremiti projekciju.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Naziv ciljnog stupca rezerviran je u MS OneLake. Trebate ga preimenovati kako biste mogli spremiti projekciju.
#XMSG
duplicateSFTPCDCColumnsDesc=Naziv ciljnog stupca rezerviran je u SFTP. Trebate ga preimenovati kako biste mogli spremiti projekciju.
#XMSG
GBQTargetNameWithPrefixUpdated1=Naziv ciljnog stupca sadržava prefiks koji je rezerviran u Google BigQueryju. Trebate ga preimenovati kako biste mogli spremiti projekciju. {0}{0}Naziv ciljnog stupca ne može počinjati s nekim od sljedećih nizova:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Skratite naziv ciljnog stupca.
#XMSG
GBQtargetMaxLengthDesc=U Google BigQueryju naziv stupca može upotrebljavati maksimalno 300 znakova. Skratite naziv ciljnog stupca kako biste mogli spremiti projekciju.
#XMSG
invalidMappedScalePrecision=Preciznost i ljestvica za cilj moraju biti kompatibilni s preciznošću i ljestvicom za izvor kako bi sve znamenke iz izvora stale u ciljno polje.
#XMSG
invalidMappedScalePrecisionShortText=Unesite valjanu vrijednost preciznosti i ljestvice.
#XMSG
validationIncompatiblePKTypeDescProjection3=Jedan ili više izvornih stupaca imaju tipove podataka koji se ne mogu definirati kao primarni ključevi u Google BigQueryju. Nijedan primarni ključ neće se stvoriti u ciljnom objektu.{0}{0} Sljedeći ciljni tipovi podataka kompatibilni su s tipovima podataka Google BigQueryja za koje se može definirati primarni ključ:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Poništite oznaku stupca __message_id.
#XMSG
uncheckColumnMessageIdDesc=Stupac: primarni ključ
#XMSG
validationOpCodeInsert=Morate unijeti vrijednost za Umetni.
#XMSG
recommendDifferentPrimaryKey=Preporučujemo da odaberete različit primarni ključ na razini stavke.
#XMSG
recommendDifferentPrimaryKeyDesc=Kad je šifra operacije već definirana, preporučujemo da odaberete različite primarne ključeve za indeks polja i stavke, kako biste izbjegli probleme kao što su, na primjer, udvostručavanja stupaca.
#XMSG
selectPrimaryKeyItemLevel=Morate odabrati najmanje jedan primarni ključ i za razinu zaglavlja i za razinu stavke.
#XMSG
selectPrimaryKeyItemLevelDesc=Kad je polje ili mapa proširena, morate odabrati dva primarna ključa, jedan na razini zaglavlja i jedan na razini stavke.
#XMSG
invalidMapKey=Morate odabrati najmanje jedan primarni ključ na razini zaglavlja.
#XMSG
invalidMapKeyDesc=Kad je polje ili mapa proširena, morate odabrati primarni ključ na razini zaglavlja.
#XFLD
txtSearchFields=Odaberi ciljne stupce
#XFLD
txtName=Naziv
#XMSG
txtSourceColValidation=Jedan ili više izvornih stupaca nisu podržani:
#XMSG
txtMappingCount=Mapiranja ({0})
#XMSG
schema=Shema
#XMSG
sourceColumn=Izvorni stupci
#XMSG
warningSourceSchema=Svaka promjena u shemi utjecat će na mapiranja u dijaloškom okviru projekcija.
#XCOL
txtTargetColName=Ciljni stupac (tehnički naziv)
#XCOL
txtDataType=Ciljni tip podataka
#XCOL
txtSourceDataType=Tip izvornih podataka
#XCOL
srcColName=Izvor stupca (tehnički naziv)
#XCOL
precision=Preciznost
#XCOL
scale=Ljestvica
#XCOL
functionsOrConstants=Funkcije / konstante
#XCOL
txtTargetColBusinessName=CIljni stupac (poslovni naziv)
#XCOL
prKey=Primarni ključ
#XCOL
txtProperties=Svojstva
#XBUT
txtOK=Spremi
#XBUT
txtCancel=Odustani
#XBUT
txtRemove=Ukloni
#XFLD
txtDesc=Opis
#XMSG
rftdMapping=Mapiranje
#XFLD
@lblColumnDataType=Tip podataka
#XFLD
@lblColumnTechnicalName=Tehnički naziv
#XBUT
txtAutomap=Automatski mapiraj
#XBUT
txtUp=Gore
#XBUT
txtDown=Dolje

#XTOL
txtTransformationHeader=Projekcija
#XTOL
editTransformation=Uredi
#XTOL
primaryKeyToolip=Ključ


#XMSG
rftdFilter=Filtar
#XMSG
rftdFilterColumnCount=Izvor: {0}({1})
#XTOL
rftdFilterColSearch=Pretraživanje
#XMSG
rftdFilterColNoData=Nema stupaca za prikaz
#XMSG
rftdFilteredColNoExps=Nema izraza filtra
#XMSG
rftdFilterSelectedColTxt=Dodajte filtar za
#XMSG
rftdFilterTxt=Filtar dostupan za
#XBUT
rftdFilterSelectedAddColExp=Dodaj izraz
#YINS
rftdFilterNoSelectedCol=Odaberite stupac za dodavanje filtra.
#XMSG
rftdFilterExp=Izraz filtra
#XMSG
rftdFilterNotAllowedColumn=Dodavanje filtra nije podržano za ovaj stupac.
#XMSG
rftdFilterNotAllowedHead=Nepodržani stupac
#XMSG
rftdFilterNoExp=Filtar nije definiran
#XTOL
rftdfilteredTt=Filtrirano
#XTOL
rftdremoveexpTt=Uklonite izraz filtra
#XTOL
validationMessageTt=Poruke validacije
#XTOL
rftdFilterDateInp=Odaberite datum
#XTOL
rftdFilterDateTimeInp=Odaberite datum/vrijeme
#XTOL
rftdFilterTimeInp=Odaberite vrijeme
#XTOL
rftdFilterInp=Unesite vrijednost
#XMSG
rftdFilterValidateEmptyMsg={0} izraza filtra u {1} stupcu prazno
#XMSG
rftdFilterValidateInvalidNumericMsg={0} izraza filtra u {1} stupcu sadrži nevaljane numeričke vrijednosti
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Izraz filtra mora sadržavati valjane numeričke vrijednosti
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Ako je schema ciljnog objekta promijenjena, upotrijebite funkciju "Mapiraj na postojeći ciljni objekt" na glavnoj stranici za prilagodbu promjena i ponovo mapirajte ciljni objekt s njegovim izvorom.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Ako ciljna tablica već postoji i mapiranje uključuje promjenu sheme, morate promijeniti ciljnu tablicu u skladu s time prije uvođenja toka replikacije.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Ako vaše mapiranje uključuje promjenu sheme, morate promijeniti ciljnu tablicu u skladu s time prije uvođenja toka replikacije.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Sljedeći nepodržani stupci preskočeni su iz definicije izvora: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Sljedeći nepodržani stupci preskočeni su iz definicije cilja: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Sljedeći objekti nisu podržani jer su izloženi potrošnji: {0} {1} {0} {0} Za upotrebu tablica u toku replikacije, semantička upotreba (u postavkama tablice) ne smije biti postavljena na {2}Analitički skup podataka{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Ciljni se objekt ne može upotrebljavati jer je izložen za potrošnju. {0}{0} Za upotrebu tablice u toku replikacije, semantička upotreba (u postavkama tablice) ne smije biti postavljena na {1}Analitički skup podataka{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Ciljni objekt s istim nazivom već postoji. Međutim, ne može upotrebljavati jer je izložen za potrošnju. {0}{0} Za upotrebu tablice u toku replikacije, semantička upotreba (u postavkama tablice) ne smije biti postavljena na {1}Analitički skup podataka{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Objekt ovog naziva već postoji u cilju. {0}Međutim, taj objekt nije moguće upotrijebiti kao ciljni objekt za tok replikacije u lokalno spremište, jer se ne radi o lokalnoj tablici.
#XMSG:
targetAutoRenameUpdated=Ciljni stupac preimenovan.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Ciljni stupac preimenovan je kako bi se omogućile replikacije u Google BigQueryju. To je zbog jednog od sljedećih razloga:{0} {1}{2}rezervirani naziv stupca{3}{2}nepodržani znakovi{3}{2}rezervirani prefiks{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Ciljni stupac preimenovan je kako bi se omogućile replikacije u Confluentu. To je zbog jednog od sljedećih razloga:{0} {1}{2}rezervirani naziv stupca{3}{2}nepodržani znakovi{3}{2}rezervirani prefiks{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Ciljni stupac preimenovan je kako bi se omogućile replikacije u cilju. To je zbog jednog od sljedećih razloga:{0} {1}{2}nepodržani znakovi{3}{2}rezervirani prefiks{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Ciljni stupac preimenovan je kako bi se omogućile replikacije u cilju. To je zbog jednog od sljedećih razloga:{0} {1}{2}rezerviran naziv stupca{3}{2}nepodržani znakovi{3}{2}rezervirani prefiks{3}{4}
#XMSG:
targetAutoDataType=Ciljni tip podataka promijenjen.
#XMSG:
targetAutoDataTypeDesc=Ciljni tip podataka promijenjen je u {0} jer izvorni tip podataka nije podržan u Google BigQueryju.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Ciljni tip podataka promijenjen je u {0} jer izvorni tip podataka nije podržan u ciljnoj vezi.
#XMSG
projectionGBQUnableToCreateKey=Primarni ključevi neće biti stvoreni.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=U Google BigQueryju podržano je maksimalno 16 primarnih ključeva, ali izvorni objekt ima veći broj primarnih ključeva. U ciljnom objektu neće biti stvoren nijedan od primarnih ključeva.
#XMSG
HDLFNoKeyError=Definirajte jedan ili više stupaca kao primarni ključ.
#XMSG
HDLFNoKeyErrorDescription=Da biste replicirali objekt, morate definirati jedan ili više stupaca kao primarni ključ.
#XMSG
HDLFNoKeyErrorExistingTarget=Definirajte jedan ili više stupaca kao primarni ključ.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Za repliciranje podataka na postojeći ciljni objekt, on mora imati jedan ili više stupaca koji su definirani kao primarni ključ. {0} {0} Imate sljedeće mogućnosti za definiranje jednog ili više stupaca kao primarnog ključa: {0}{0}{1} Upotrijebite uređivač lokalne tablice da promijenite postojeći ciljni objekt. Zatim ponovo učitajte tok replikacije.{0}{0}{1} Preimenujte ciljni objekt u toku replikacije. Ovo će stvoriti novi objekt čim se pokrene izvođenje. Nakon preimenovanja, možete definirati jedan ili više stupaca kao primarni ključ u projekciji.{0}{0}{1} Mapirajte objekt na drugi postojeći ciljni objekt u kojem je jedan ili više stupaca već definirano kao primarni ključ.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Promijenjen primarni ključ.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=U usporedbi s izvornim objektom, definirali ste različite stupce kao primarni ključ za ciljni objekt. Uvjerite se da ti stupci jedinstveno identificiraju sve retke kako biste izbjegli moguće oštećenje podataka prilikom kasnijeg repliciranja podataka. {0} {0} U izvornom objektu sljedeći su stupci definirani kao primarni ključ: {0} {1}
#XMSG
duplicateDPIDColumns=Preimenujte ciljni stupac.
#XMSG
duplicateDPIDDColumnsDesc1=Ovaj je naziv ciljnog stupca rezerviran za tehnički stupac. Unesite drukčiji naziv kako biste spremili projekciju.
#XMSG:
targetAutoRenameDPID=Ciljni stupac preimenovan.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Ciljni stupac preimenovan je kako bi se omogućile replikacije iz izvora ABAP-a bez ključeva. To je zbog jednog od sljedećih razloga:{0} {1}{2}Rezervirani naziv stupca{3}{2}Nepodržani znakovi{3}{2}Rezervirani prefiks{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle=Broj ciljnih postavki: {0}
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle=Broj ciljnih postavki: {0}
#XBUT
connectionSettingSave=Spremi
#XBUT
connectionSettingCancel=Odustani
#XBUT: Button to keep the object level settings
txtKeep=Zadrži
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Piši preko
#XFLD
targetConnectionThreadlimit=Ograničenje ciljnih niti za početno učitavanje (1-100)
#XFLD
connectionThreadLimit=Ograničenje izvornih niti za početno učitavanje (1-100)
#XFLD
maxConnection=Ograničenje niti replikacije (1-100)
#XFLD
kafkaNumberOfPartitions=Broj particija
#XFLD
kafkaReplicationFactor=Faktor replikacije
#XFLD
kafkaMessageEncoder=Program za kodiranje poruka
#XFLD
kafkaMessageCompression=Sažimanje poruke
#XFLD
fileGroupDeltaFilesBy=Grupiranje delte po
#XFLD
fileFormat=Tip datoteke
#XFLD
csvEncoding=Kodiranje CSV-a
#XFLD
abapExitLbl=Izlaz iz ABAP-a
#XFLD
deltaPartition=Broj niti objekata za delta učitavanja (1-10)
#XFLD
clamping_Data=Neuspjeh u skraćivanju podataka
#XFLD
fail_On_Incompatible=Neuspjeh u nekompatibilnim podacima
#XFLD
maxPartitionInput=Maksimalan broj particija
#XFLD
max_Partition=Definiraj maksimalan broj particija
#XFLD
include_SubFolder=Uključi podmape
#XFLD
fileGlobalPattern=Globalni uzorak za naziv datoteke
#XFLD
fileCompression=Sažimanje datoteke
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Graničnik datoteke
#XFLD
fileIsHeaderIncluded=Zaglavlje datoteke
#XFLD
fileOrient=Orijentacija
#XFLD
gbqWriteMode=Način pisanja
#XFLD
suppressDuplicate=Sažmi duplikate
#XFLD
apacheSpark=Omogući kompatibilnost Apache Spark
#XFLD
clampingDatatypeCb=Sreži tipove podataka pomičnog decimalnog zareza
#XFLD
overwriteDatasetSetting=Prebriši ciljne postavke na razini objekta
#XFLD
overwriteSourceDatasetSetting=Prebriši izvorne postavke na razini objekta
#XMSG
kafkaInvalidConnectionSetting=Unesite broj između {0} i {1}.
#XMSG
MinReplicationThreadErrorMsg=Unesite broj veći od {0}.
#XMSG
MaxReplicationThreadErrorMsg=Unesite broj manji od {0}.
#XMSG
DeltaThreadErrorMsg=Unesite vrijednost između 1 i 10.
#XMSG
MaxPartitionErrorMsg=Unesite vrijednost između 1 <= x <= 2147483647. Zadana vrijednost je 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Unesite cijeli broj između {0} i {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Upotrijebite faktor replikacije brokera
#XFLD
serializationFormat=Format serijalizacije
#XFLD
compressionType=Tip sažimanja
#XFLD
schemaRegistry=Upotrijebite registar shema
#XFLD
subjectNameStrat=Strategija naziva predmeta
#XFLD
compatibilityType=Tip kompatibilnosti
#XFLD
confluentTopicName=Naziv teme
#XFLD
confluentRecordName=Naziv zapisa
#XFLD
confluentSubjectNamePreview=Pretpregled naziva predmeta
#XMSG
serializationChangeToastMsgUpdated2=Format serijalizacije promijenjen je u JSON jer registar sheme nije omogućen. Za promjenu formata serijalizacije natrag u AVRO prvo morate omogućiti registar sheme.
#XBUT
confluentTopicNameInfo=Naziv teme uvijek se zasniva na nazivu ciljnog objekta. Možete ga promijeniti preimenovanjem ciljnog objekta.
#XMSG
emptyRecordNameValidationHeaderMsg=Unesite naziv zapisa.
#XMSG
emptyPartionHeader=Unesite broj particija.
#XMSG
invalidPartitionsHeader=Unesite valjan broj particija.
#XMSG
invalidpartitionsDesc=Unesite broj između 1 i 200.000.
#XMSG
emptyrFactorHeader=Unesite faktor replikacije.
#XMSG
invalidrFactorHeader=Unesite valjan faktor replikacije.
#XMSG
invalidrFactorDesc=Unesite broj između 1 i 32.767.
#XMSG
emptyRecordNameValidationDescMsg=Ako se upotrebljava format serijalizacije "AVRO", podržani su samo sljedeći znakovi:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(donja crta)
#XMSG
validRecordNameValidationHeaderMsg=Unesite valjan naziv zapisa.
#XMSG
validRecordNameValidationDescMsgUpdated=Budući da se upotrebljava format serijalizacije "AVRO", naziv zapisa mora se sastojati samo od alfanumeričkih znakova (A-Z, a-z, 0-9) i donje crte (_). Mora započeti slovom ili donjom crtom.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=“Broj niti objekata za delta učitavanja” može se postaviti čim jedan ili više objekata imaju tip učitavanja “Početno i delta”.
#XMSG
invalidTargetName=Nevaljan naziv stupca 
#XMSG
invalidTargetNameDesc=Naziv ciljnog stupca mora se sastojati samo od alfanumeričkih znakova (A-Z, a-z, 0-9) i donje crte (_).
#XFLD
consumeOtherSchema=Troši druge verzije sheme
#XFLD
ignoreSchemamissmatch=Zanemari nepodudaranje shema
#XFLD
confleuntDatatruncation=Neuspjeh u skraćivanju podataka
#XFLD
isolationLevel=Razina izolacije
#XFLD
confluentOffset=Početna točka
#XFLD
signavioGroupDeltaFilesByText=Nijedno
#XFLD
signavioFileFormatText=Parket
#XFLD
signavioSparkCompatibilityParquetText=Ne
#XFLD
siganvioFileCompressionText=Oštro
#XFLD
siganvioSuppressDuplicatesText=Ne

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Projekcije
#XBUT
txtAdd=Dodavanje
#XBUT
txtEdit=Uredi
#XMSG
transformationText=Dodajte projekciju za postavljanje filtra ili mapiranja.
#XMSG
primaryKeyRequiredText=Odaberite primarni ključ uz Konfiguriraj shemu.
#XFLD
lblSettings=Postavke
#XFLD
lblTargetSetting={0}: ciljne postavke
#XMSG
@csvRF=Odaberite datoteku koja sadržava definiciju sheme koju želite primijeniti na sve datoteke u mapi.
#XFLD
lblSourceColumns=Izvorni stupci
#XFLD
lblJsonStructure=JSON struktura
#XFLD
lblSourceSetting={0}: izvorne postavke
#XFLD
lblSourceSchemaSetting={0}: postavke izvorne sheme
#XBUT
messageSettings=Postavke poruke
#XFLD
lblPropertyTitle1=Svojstva objekta
#XFLD
lblRFPropertyTitle=Svojstva toka replikacije
#XMSG
noDataTxt=Nema stupaca za prikaz.
#XMSG
noTargetObjectText=Nije odabran ciljni objekt.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Ciljni stupci
#XMSG
searchColumns=Pretražite stupce
#XTOL
cdcColumnTooltip=Stupac za snimanje delte
#XMSG
sourceNonDeltaSupportErrorUpdated=Izvorni objekt ne podržava snimanje delte.
#XMSG
targetCDCColumnAdded=Dodana su 2 ciljna stupca za snimanje delte.
#XMSG
deltaPartitionEnable=Ograničenje niti objekata za delta učitavanja dodano je u izvorne postavke.
#XMSG
attributeMappingRemovalTxt=Uklanjanje nevaljanih mapiranja koja nisu podržana za novi ciljni objekt.
#XMSG
targetCDCColumnRemoved=Uklonjena su 2 ciljna stupca koja su upotrebljavana za snimanje delte.
#XMSG
replicationLoadTypeChanged=Tip učitavanja promijenjen u "Početno i delta".
#XMSG
sourceHDLFLoadTypeError=Promijenite tip učitavanja u "Početno i delta".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Da biste replicirali objekt iz izvorne veze s tipom veze datoteka SAP HANA Cloud, jezero podataka u SAP Datasphere, morate upotrijebiti tip učitavanja "početno i delta".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Omogućite snimanje delte
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Da biste replicirali objekt iz izvorne veze s tipom veze datoteka SAP HANA Cloud, jezero podataka u SAP Datasphere, morate omogućiti snimanje delte.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Promijenite ciljni objekt.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Ciljni objekt ne može se upotrijebiti jer je onemogućeno snimanje delte. Možete preimenovati ciljni objekt (što omogućuje stvaranje novog objekta sa snimanjem delte) ili ga mapirati u postojeći objekt s omogućenim snimanjem delte.
#XMSG
deltaPartitionError=Unesite valjan broj niti objekata za delta učitavanja.
#XMSG
deltaPartitionErrorDescription=Unesite vrijednost između 1 i 10.
#XMSG
deltaPartitionEmptyError=Unesite broj niti objekata za delta učitavanja.
#XFLD
@lblColumnDescription=Opis
#XMSG
@lblColumnDescriptionText1=U tehničke svrhe - obrada dvostrukih zapisa koje uzrokuju problemi pri replikaciji izvornih objekata zasnovanih na ABAP-u koji nemaju primarni ključ.
#XFLD
storageType=Pohrana
#XFLD
skipUnmappedColLbl=Preskoči nemapirane stupce
#XFLD
abapContentTypeLbl=Tip sadržaja
#XFLD
autoMergeForTargetLbl=Automatski spoji podatke
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Općenito
#XFLD
lblBusinessName=Poslovni naziv
#XFLD
lblTechnicalName=Tehnički naziv
#XFLD
lblPackage=Paket
#XFLD
statusPanel=Status izvođenja
#XBTN: Schedule dropdown menu
SCHEDULE=Raspored
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Uredi raspored
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Izbriši raspored
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Stvori raspored
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Neuspješna provjera validacije rasporeda
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Raspored se ne može stvoriti jer se trenutačno uvodi tok replikacije.{0}Pričekajte dok se ne uvede tok replikacije.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Za tokove replikacije koji sadrže objekte s tipom učitavanja "Početno i delta" ne može se stvoriti raspored.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Za tokove replikacije koji sadržavaju objekte s tipom učitavanja "Početno i delta/Samo delta" ne može se stvoriti raspored.
#XFLD : Scheduled popover
SCHEDULED=Raspoređeno
#XFLD
CREATE_REPLICATION_TEXT=Stvorite tok replikacije.
#XFLD
EDIT_REPLICATION_TEXT=Uredite tok replikacije.
#XFLD
DELETE_REPLICATION_TEXT=Izbrišite tok replikacije.
#XFLD
REFRESH_FREQUENCY=Učestalost
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Tok replikacije ne može se uvesti jer postojeći raspored{0} još ne podržava tip učitavanja "Početno i delta".{0}{0}Da biste uveli tok replikacije, tipove učitavanja svih objekata{0} morate postaviti na "Samo početno". Umjesto toga, možete izbrisati raspored, uvesti tok replikacije {0}, a zatim pokrenuti novo izvođenje. To bi dovelo do izvođenja bez završetka {0} što podržava i objekte s tipom učitavanja "Početno i delta".
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Tok replikacije ne može se uvesti jer postojeći raspored{0} još ne podržava tip učitavanja "Početno i delta/Samo delta".{0}{0}Da biste uveli tok replikacije, tipove učitavanja svih objekata{0} morate postaviti na "Samo početno". Umjesto toga, možete izbrisati raspored, uvesti tok replikacije {0}, a zatim pokrenuti novo izvođenje. To bi dovelo do izvođenja bez završetka {0} što podržava i objekte s tipom učitavanja "Početno i delta/Samo delta".
#XMSG
SCHEDULE_EXCEPTION=Pozivanje pojedinosti o rasporedu nije uspjelo
#XFLD: Label for frequency column
everyLabel=Svakih
#XFLD: Plural Recurrence text for Hour
hoursLabel=h
#XFLD: Plural Recurrence text for Day
daysLabel=d.
#XFLD: Plural Recurrence text for Month
monthsLabel=mj.
#XFLD: Plural Recurrence text for Minutes
minutesLabel=min
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Dobivanje informacija o mogućnosti rasporeda nije uspjelo.
#XFLD :Paused field
PAUSED=Pauzirano
#XMSG
navToMonitoring=Otvori u nadzoru tokova
#XFLD
statusLbl=Status
#XFLD
lblLastRunExecuted=Početak zadnjeg izvođenja
#XFLD
lblLastExecuted=Zadnje izvođenje
#XFLD: Status text for Completed
statusCompleted=Dovršeno
#XFLD: Status text for Running
statusRunning=Izvodi se
#XFLD: Status text for Failed
statusFailed=Nije uspjelo
#XFLD: Status text for Stopped
statusStopped=Zaustavljeno
#XFLD: Status text for Stopping
statusStopping=Zaustavljanje
#XFLD: Status text for Active
statusActive=Aktivno
#XFLD: Status text for Paused
statusPaused=Pauzirano
#XFLD: Status text for not executed
lblNotExecuted=Još nije izvedeno
#XFLD
messagesSettings=Postavke poruka
#XTOL
@validateModel=Poruke validacije
#XTOL
@hierarchy=Hijerarhija
#XTOL
@columnCount=Broj stupaca
#XMSG
VAL_PACKAGE_CHANGED=Ovaj ste objekt dodijelili paketu "{1}". Kliknite “Spermi”za potvrdu i validaciju te promjene. Uzmite u obzir da dodjelu paketu nakon spremanja nije moguće poništiti u ovom uređivaču.
#XMSG
MISSING_DEPENDENCY=Zavisnosti objekta ''{0}'' nije moguće riješiti u paketu "{1}"
#XFLD
deltaLoadInterval=Delta interval učitavanja
#XFLD
lblHour=Sati (0-24)
#XFLD
lblMinutes=Minute (0-59)
#XMSG
maxHourOrMinErr=Unesite vrijednost između 0 i {0}
#XMSG
maxDeltaInterval=Maksimalna vrijednost delta interval učitavanja je 24 sata.{0}U skladu s tim promijenite vrijednost minuta ili sati.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Staza ciljnog spremnika
#XFLD
confluentSubjectName=Naziv predmeta
#XFLD
confluentSchemaVersion=Verzija sheme
#XFLD
confluentIncludeTechKeyUpdated=Uključi tehnički ključ
#XFLD
confluentOmitNonExpandedArrays=Izostavi neproširena polja
#XFLD
confluentExpandArrayOrMap=Proširi polje ili mapu
#XCOL
confluentOperationMapping=Mapiranje operacije
#XCOL
confluentOpCode=Šifra operacije
#XFLD
confluentInsertOpCode=Umetni
#XFLD
confluentUpdateOpCode=Ažuriraj
#XFLD
confluentDeleteOpCode=Izbriši
#XFLD
expandArrayOrMapNotSelectedTxt=Nije odabrano
#XFLD
confluentSwitchTxtYes=Da
#XFLD
confluentSwitchTxtNo=Ne
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Pogreška
#XTIT
executeWarning=Upozorenje
#XMSG
executeunsavederror=Prije izvođenja spremite svoj tok replikacije.
#XMSG
executemodifiederror=Postoje nespremljene promjene u toku replikacije. Spremite tok replikacije.
#XMSG
executeundeployederror=Prije izvođenja morate uvesti tok replikacije.
#XMSG
executedeployingerror=Pričekajte završetak uvođenja.
#XMSG
msgRunStarted=Izvođenje pokrenuto
#XMSG
msgExecuteFail=Izvođenje toka replikacije nije uspjelo
#XMSG
titleExecuteBusy=Pričekajte.
#XMSG
msgExecuteBusy=Pripremamo vaše podatke za izvođenje toka replikacije.
#XTIT
executeConfirmDialog=Upozorenje
#XMSG
msgExecuteWithValidations=Postoje pogreške validacije u toku replikacije. Izvođenje toka replikacije može dovesti do pogreške.
#XMSG
msgRunDeployedVersion=Postoje promjene za uvođenje. Bit će izvedena zadnja uvedena verzija toka replikacije. Želite li nastaviti?
#XBUT
btnExecuteAnyway=Ipak izvedi
#XBUT
btnExecuteClose=Zatvori
#XBUT
loaderClose=Zatvori
#XTIT
loaderTitle=Učitavanje
#XMSG
loaderText=Dohvaćanje pojedinosti s poslužitelja
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Tok replikacije na ovu ciljnu vezu koja nije SAP-ova ne može se pokrenuti
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=jer za ovaj mjesec nema dostupnog izlaznog obujma.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Administrator može povećati izlazne blokove Premium za ovog
#XMSG
premiumOutBoundRFAdminErrMsgPart2=zakupca i tako učiniti izlazni obujam dostupnim za ovaj mjesec.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Pogreška
#XTIT
deployInfo=Informacije
#XMSG
deployCheckFailException=Došlo je do iznimke tijekom uvođenja
#XMSG
deployGBQFFDisabled=Uvođenje tokova replikacije s ciljnom vezom na Google BigQuery trenutačno nije moguće jer izvodimo održavanje ove funkcije.
#XMSG
deployKAFKAFFDisabled=Uvođenje tokova replikacije s ciljnom vezom na Apache Kafka trenutačno nije moguće jer izvodimo održavanje ove funkcije.
#XMSG
deployConfluentDisabled=Uvođenje tokova replikacije s ciljnom vezom na Confluent Kafka trenutačno nije moguće jer izvodimo održavanje te funkcije.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Za sljedeće ciljne objekte, nazivima tablica snimanja delte već se koriste druge tablice u spremištu: {0} morate preimenovati te ciljne objekte kako biste osigurali da su pridruženi nazivi tablica snimanja delte jedinstveni prije nego što uvedete tok replikacije.
#XMSG
deployDWCSourceFFDisabled=Uvođenje tokova replikacije koji imaju rješenje SAP Datasphere kao izvor trenutačno nije moguće jer izvodimo održavanje te funkcije.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Uvođenje tokova replikacije koji sadržavaju lokalne tablice omogućene za deltu kao izvorne objekte trenutačno nije moguće jer izvodimo održavanje te funkcije.
#XMSG
deployHDLFSourceFFDisabled=Implementacija tokova replikacije koji imaju izvorne veze s tipom veze SAP HANA Cloud, Data Lake Files trenutačno nije moguća jer izvodimo održavanje.
#XMSG
deployObjectStoreAsSourceFFDisabled=Implementacija tokova replikacije koji imaju davatelje pohrane u oblaku kao izvor trenutačno nije moguća.
#XMSG
deployConfluentSourceFFDisabled=Uvođenje tokova replikacije koji imaju rješenje Confluent Kafka kao izvor trenutačno nije moguće jer izvodimo održavanje te funkcije.
#XMSG
deployMaxDWCNewTableCrossed=Za velike tokove replikacije nije moguće spremanje i uvođenje odjednom. Prvo spremite svoj tok replikacije, a zatim ga uvedite.
#XMSG
deployInProgressInfo=Uvođenje je već u tijeku.
#XMSG
deploySourceObjectInUse=Izvorni objekti {0} već se upotrebljavaju u tokovima replikacije {1}.
#XMSG
deployTargetSourceObjectInUse=Izvorni objekti {0} već se upotrebljavaju u tokovima replikacije {1}. Ciljni objekti {2} eć se upotrebljavaju u tokovima replikacije {3}.
#XMSG
deployReplicationFlowCheckError=Pogreška pri verifikaciji toka replikacije: {0}
#XMSG
preDeployTargetObjectInUse=Ciljni objekti {0} već se upotrebljavaju u tokovima replikacija {1} i ne možete imati isti ciljni objekt u dva različita toka replikacije. Odaberite drugi ciljni objekt i pokušajte ponovo.
#XMSG
runInProgressInfo=Tijek replikacije već se izvodi.
#XMSG
deploySignavioTargetFFDisabled=Uvođenje tokova replikacije koji imaju SAP Signavio kao cilj trenutačno nije moguća jer izvodimo održavanje ove funkcije.
#XMSG
deployHanaViewAsSourceFFDisabled=Uvođenje tokova replikacije koji imaju prikaze kao izvorne objekte za odabranu izvornu vezu trenutačno nije moguće. Pokušajte ponovo poslije.
#XMSG
deployMsOneLakeTargetFFDisabled=Uvođenje tokova replikacije koji imaju MS OneLake kao cilj trenutačno nije moguća jer izvodimo održavanje ove funkcije.
#XMSG
deploySFTPTargetFFDisabled=Uvođenje tokova replikacije koji imaju SFTP kao cilj trenutačno nije moguća jer izvodimo održavanje ove funkcije.
#XMSG
deploySFTPSourceFFDisabled=Uvođenje tokova replikacije koji imaju SFTP kao izvor trenutačno nije moguće jer izvodimo održavanje ove funkcije.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Tehnički naziv
#XFLD
businessNameInRenameTarget=Poslovni naziv
#XTOL
renametargetDialogTitle=Preimenuj ciljni objekt
#XBUT
targetRenameButton=Preimenuj
#XBUT
targetRenameCancel=Odustani
#XMSG
mandatoryTargetName=Morate unijeti naziv.
#XMSG
dwcSpecialChar=_ (donja crta) jedini je dopušteni posebni znak.
#XMSG
dwcWithDot=Naziv ciljne tablice može se sastojati od latiničnih slova, brojeva, donje crte (_), i točke (.). Prvi znak mora biti slovo, broj ili donja crta (ne točka).
#XMSG
nonDwcSpecialChar=Dopušteni posebni znakovi jesu _ (donja crta), - (spojnica) i .(točka)
#XMSG
firstUnderscorePattern=Naziv ne smije započeti s _ (donja crta)

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: prikaži SQL naredbu Stvori tablicu
#XMSG
sqlDialogMaxPKWarning=U Google BigQueryju podržano je maksimalno 16 primarnih ključeva, a izvorni objekt ima veći broj. Stoga primarni ključevi nisu definirani u ovoj naredbi.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Jedan ili više izvornih stupaca imaju tipove podataka koji se ne mogu definirati kao primarni ključevi u Google BigQueryju. Stoga primarni ključevi nisu definirani u ovom slučaju. U Google BigQueryju samo sljedeći tipovi podataka mogu imati primarni ključ: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Kopiraj i zatvori
#XBUT
closeDDL=Zatvori
#XMSG
copiedToClipboard=Kopirano u međuspremnik


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Objekt ''{0}'' ne može biti dio lanca zadataka jer nema završetak (jer uključuje objekte s tipom učitavanja Početno i delta/Samo delta).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Objekt ''{0}'' ne može biti dio lanca zadataka jer nema završetak (jer uključuje objekte s tipom učitavanja Početno i delta/Samo delta).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objekt "{0}" nije moguće dodati lancu zadataka.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Postoje ciljni objekti koji nisu spremljeni. Spremite ponovo.{0}{0} Ponašanje ove značajke promijenjeno je: u prošlosti su se ciljni objekti stvarali samo u ciljnom okruženju kada je tok replikacije uveden. {0} Sada su objekti već stvoreni kada se sprema tok replikacije. Vaš je tok replikacije stvoren prije ove promjene i sadržava nove objekte.{0} Morate ponovo spremiti tok replikacije prije njegova uvođenja tako da se novi objekti pravilno uključe.
#XMSG
confirmChangeContentTypeMessage=Promijenit ćete tip sadržaja. Ako to učinite, sve postojeće projekcije će se izbrisati.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Naziv predmeta
#XFLD
schemaDialogVersionName=Verzija sheme
#XFLD
includeTechKey=Uključi tehnički ključ
#XFLD
segementButtonFlat=Plošno
#XFLD
segementButtonNested=Ugniježđeno
#XMSG
subjectNamePlaceholder=Pretraži naziv predmeta

#XMSG
@EmailNotificationSuccess=Konfiguracija obavijesti e-poštom o vremenu izvođenja spremljena je.

#XFLD
@RuntimeEmailNotification=Obavijest e-poštom o vremenu izvođenja

#XBTN
@TXT_SAVE=Spremi


