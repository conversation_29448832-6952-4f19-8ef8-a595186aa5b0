#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Luồng sao chép

#XFLD: Edit Schema button text
editSchema=Hiệu chỉnh biểu đồ

#XTIT : Properties heading
configSchema=Cấu hình biểu đồ

#XFLD: save changed button text
applyChanges=Áp dụng các thay đổi


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Chọn kết nối nguồn
#XFLD
sourceContainernEmptyText=Chọn vùng chứa
#XFLD
targetConnectionEmptyText=Chọn kết nối đích
#XFLD
targetContainernEmptyText=Chọn vùng chứa
#XFLD
sourceSelectObjectText=Chọn đối tượng nguồn
#XFLD
sourceObjectCount=Đối tượng nguồn ({0})
#XFLD
targetObjectText=Đối tượng đích
#XFLD
confluentBrowseContext=Chọn ngữ cảnh
#XBUT
@retry=Thử lại
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Đang tiến hành nâng cấp đối tượng thuê.

#XTOL
browseSourceConnection=Duyệt kết nối nguồn
#XTOL
browseTargetConnection=Duyệt kết nối đích
#XTOL
browseSourceContainer=Duyệt vùng chứa nguồn
#XTOL
browseAndAddSourceDataset=Thêm đối tượng nguồn
#XTOL
browseTargetContainer=Duyệt vùng chứa đích
#XTOL
browseTargetSetting=Duyệt thiết lập đích
#XTOL
browseSourceSetting=Duyệt thiết lập nguồn
#XTOL
sourceDatasetInfo=Thông tin
#XTOL
sourceDatasetRemove=Loại bỏ
#XTOL
mappingCount=Điều này đại diện cho tổng số ánh xạ/biểu thức không dựa trên tên.
#XTOL
filterCount=Điều này đại diện cho tổng số điều kiện bộ lọc.
#XTOL
loading=Đang tải...
#XCOL
deltaCapture=Thu thập chênh lệch
#XCOL
deltaCaptureTableName=Bảng thu thập chênh lệch
#XCOL
loadType=Kiểu tải
#XCOL
deleteAllBeforeLoading=Xóa tất cả trước khi tải
#XCOL
transformationsTab=Hình chiếu
#XCOL
settingsTab=Thiết lập

#XBUT
renameTargetObjectBtn=Đổi tên đối tượng đích
#XBUT
mapToExistingTargetObjectBtn=Ánh xạ đến đối tượng Đích hiện tại
#XBUT
changeContainerPathBtn=Thay đổi đường dẫn vùng chứa
#XBUT
viewSQLDDLUpdated=Xem SQL Create Table statement
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Đối tượng nguồn không hỗ trợ thu thập chênh lệch, nhưng đối tượng đích được chọn đã bật tùy chọn thu thập chênh lệch.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Không thể sử dụng đối tượng đích vì tính năng thu thập chênh lệch được bật,{0}trong khi đối tượng nguồn không hỗ trợ thu thập chênh lệch.{1}Bạn có thể chọn một đối tượng đích khác không hỗ trợ thu thập chênh lệch.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Đối tượng đích có tên này đã tồn tại. Tuy nhiên, nó không thể được sử dụng{0}vì tính năng thu thập chênh lệch được bật, trong khi đối tượng nguồn không{0}hỗ trợ thu thập chênh lệch.{1}Bạn có thể nhập tên của đối tượng đích hiện có không{0}hỗ trợ thu thập chênh lệch hoặc nhập tên chưa tồn tại.
#XBUT
copySQLDDLUpdated=Sao chép SQL Create Table statement
#XMSG
targetObjExistingNoCDCColumnUpdated=Các bảng hiện có trong Google BigQuery phải bao gồm các cột sau đây để thu thập dữ liệu thay đổi (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Các đối tượng nguồn sau đây không được hỗ trợ vì chúng không có khóa chính, hoặc chúng đang sử dụng kết nối không đáp ứng các điều kiện để truy xuất khóa chính:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Vui lòng tham khảo SAP KBA 3531135 để biết giải pháp khả thi.
#XLST: load type list values
initial=Chỉ ban đầu
@emailUpdateError=Lỗi khi cập nhật danh sách thông báo qua email

#XLST
initialDelta=Ban đầu và chênh lệch

#XLST
deltaOnly=Chỉ chênh lệch
#XMSG
confluentDeltaLoadTypeInfo=Đối với nguồn Confluent Kafka, chỉ loại tải Ban đầu và Chênh lệch được hỗ trợ.
#XMSG
confirmRemoveReplicationObject=Bạn có xác nhận rằng bạn muốn xóa sao chép không?
#XMSG
confirmRemoveReplicationTaskPrompt=Thao tác này sẽ xóa bản sao hiện có. Bạn có muốn tiếp tục không?
#XMSG
confirmTargetConnectionChangePrompt=Hành động này sẽ thiết lập lại kết nối đích, vùng chứa đích và xóa tất cả đối tượng đích. Bạn có muốn tiếp tục không?
#XMSG
confirmTargetContainerChangePrompt=Hành động này sẽ thiết lập lại vùng chứa đích và xóa tất cả đối tượng đích hiện có. Bạn có muốn tiếp tục không?
#XMSG
confirmRemoveTransformObject=Bạn có xác nhận rằng bạn muốn xóa hình chiếu {0} không?
#XMSG
ErrorMsgContainerChange=Đã xảy ra lỗi khi thay đổi đường dẫn vùng chứa.
#XMSG
infoForUnsupportedDatasetNoKeys=Các đối tượng nguồn sau đây không được hỗ trợ vì chúng không có khóa chính:
#XMSG
infoForUnsupportedDatasetView=Các đối tượng nguồn sau thuộc loại Màn hình không được hỗ trợ:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Đối tượng nguồn sau đây không được hỗ trợ vì nó là màn hình SQL chứa các tham số đầu vào:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Các đối tượng nguồn sau đây không được hỗ trợ vì trích xuất bị vô hiệu hóa đối với chúng:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Đối với kết nối Confluent, các định dạng chuỗi hóa chỉ được phép là AVRO và JSON. Các đối tượng sau đây không được hỗ trợ vì chúng sử dụng định dạng chuỗi hóa khác:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Không thể tìm nạp biểu đồ cho các đối tượng sau. Vui lòng chọn ngữ cảnh phù hợp hoặc xác minh cấu hình kho lưu trữ lược đồ.
#XTOL: warning dialog header on deleting replication task
deleteHeader=Xóa
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Thiết lập Xóa tất cả trước khi tải không được hỗ trợ đối với Google BigQuery.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Thiết lập Xóa tất cả trước sẽ xóa và tạo lại đối tượng (chủ đề) trước mỗi lần sao chép. Điều này cũng xóa tất cả các thông báo được gán.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Thiết lập Xóa tất cả trước không được hỗ trợ đối với loại đích này.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Tên kỹ thuật
#XCOL
connBusinessName=Tên doanh nghiệp
#XCOL
connDescriptionName=Mô tả
#XCOL
connType=Kiểu
#XMSG
connTblNoDataFoundtxt=Không tìm thấy kết nối
#XMSG
connectionError=Đã xảy ra lỗi khi tìm nạp kết nối.
#XMSG
connectionCombinationUnsupportedErrorTitle=Kết hợp kết nối không được hỗ trợ
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Sao chép từ {0} sang {1} hiện không được hỗ trợ.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Không hỗ trợ kết hợp kiểu kết nối.
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Không hỗ trợ sao chép từ kết nối có kiểu kết nối SAP HANA Cloud, tập tin hồ dữ liệu sang {0} . Bạn chỉ có thể sao chép sang SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Chọn
#XBUT
containerCancelBtn=Hủy
#XTOL
containerSelectTooltip=Chọn
#XTOL
containerCancelTooltip=Hủy
#XMSG
containerContainerPathPlcHold=Đường dẫn vùng chứa
#XFLD
containerContainertxt=Vùng chứa
#XFLD
confluentContainerContainertxt=Ngữ cảnh
#XMSG
infoMessageForSLTSelection=Chỉ ID chuyển hàng loạt/SLT được phép làm vùng chứa. Chọn ID chuyển hàng loạt trong SLT (nếu có) và nhấp vào Gửi.
#XMSG
msgFetchContainerFail=Đã xảy ra lỗi khi tìm nạp dữ liệu vùng chứa.
#XMSG
infoMessageForSLTHidden=Kết nối này không hỗ trợ thư mục SLT, do đó chúng không xuất hiện trong danh sách bên dưới.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Chọn vùng chứa chứa các thư mục phụ trong đó.
#XMSG
sftpIncludeSubFolderText=Sai
#XMSG
sftpIncludeSubFolderTextNew=Không

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Chưa có ánh xạ bộ lọc)
#XMSG
failToFetchRemoteMetadata=Đã xảy ra lỗi khi tìm nạp siêu dữ liệu.
#XMSG
failToFetchData=Đã xảy ra lỗi khi tìm nạp đích hiện có.
#XCOL
@loadType=Kiểu tải
#XCOL
@deleteAllBeforeLoading=Xóa tất cả trước khi tải

#XMSG
@loading=Đang tải...
#XFLD
@selectSourceObjects=Chọn đối tượng nguồn
#XMSG
@exceedLimit=Bạn không thể nhập nhiều hơn {0} đối tượng cùng một lúc. Vui lòng bỏ chọn ít nhất {1} đối tượng.
#XFLD
@objects=Đối tượng
#XBUT
@ok=OK
#XBUT
@cancel=Hủy
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Kế tiếp
#XBUT
btnAddSelection=Thêm lựa chọn
#XTOL
@remoteFromSelection=Loại bỏ khỏi lựa chọn
#XMSG
@searchInForSearchField=Tìm kiếm trong {0}

#XCOL
@name=Tên kỹ thuật
#XCOL
@type=Kiểu
#XCOL
@location=Địa điểm
#XCOL
@label=Tên doanh nghiệp
#XCOL
@status=Trạng thái

#XFLD
@searchIn=Tìm kiếm trong:
#XBUT
@available=Có sẵn
#XBUT
@selection=Lựa chọn

#XFLD
@noSourceSubFolder=Bảng và màn hình
#XMSG
@alreadyAdded=Đã tồn tại trong sơ đồ
#XMSG
@askForFilter=Có nhiều hơn {0} mục. Vui lòng nhập chuỗi bộ lọc để thu hẹp số lượng mục.
#XFLD: success label
lblSuccess=Thành công
#XFLD: ready label
lblReady=Sẵn sàng
#XFLD: failure label
lblFailed=Không thành công
#XFLD: fetching status label
lblFetchingDetail=Tìm nạp chi tiết

#XMSG Place holder text for tree filter control
filterPlaceHolder=Gõ văn bản để lọc đối tượng cấp cao nhất
#XMSG Place holder text for server search control
serverSearchPlaceholder=Gõ và nhấn Enter để tìm kiếm
#XMSG
@deployObjects=Đang nhập {0} đối tượng...
#XMSG
@deployObjectsStatus=Số đối tượng đã được nhập: {0}. Số đối tượng không thể nhập: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Không thể mở trình duyệt vùng chứa cục bộ.
#XMSG
@openRemoteSourceBrowserError=Không thể tìm nạp đối tượng nguồn.
#XMSG
@openRemoteTargetBrowserError=Không thể tìm nạp đối tượng đích.
#XMSG
@validatingTargetsError=Đã xảy ra lỗi khi xác thực đích.
#XMSG
@waitingToImport=Sẵn sàng nhập

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Đã vượt quá số lượng đối tượng tối đa. Chọn tối đa 500 đối tượng cho một luồng sao chép.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Tên kỹ thuật
#XFLD
sourceObjectBusinessName=Tên doanh nghiệp
#XFLD
sourceNoColumns=Số lượng cột
#XFLD
containerLbl=Vùng chứa

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Bạn phải chọn kết nối nguồn cho luồng sao chép.
#XMSG
validationSourceContainerNonExist=Bạn phải chọn vùng chứa cho kết nối nguồn.
#XMSG
validationTargetNonExist=Bạn phải chọn kết nối đích cho luồng sao chép.
#XMSG
validationTargetContainerNonExist=Bạn phải chọn vùng chứa cho kết nối đích.
#XMSG
validationTruncateDisabledForObjectTitle=Sao chép vào kho lưu trữ đối tượng.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Chỉ có thể sao chép vào kho lưu trữ đám mây nếu hoặc tùy chọn Xóa hết trước khi tải được thiết lập hoặc đối tượng đích không tồn tại trong đích.{0}{0} Để vẫn bật sao chép cho các đối tượng mà tùy chọn Xóa hết trước khi tải không được thiết lập, hãy đảm bảo rằng đối tượng đích không tồn tại trong hệ thống trước khi bạn thực hiện luồng sao chép.
#XMSG
validationTaskNonExist=Bạn phải có ít nhất một sao chép trong luồng sao chép.
#XMSG
validationTaskTargetMissing=Bạn phải có đích cho sao chép với nguồn: {0}
#XMSG
validationTaskTargetIsSAC=Đích được chọn là tạo tác SAC: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Đích được chọn không phải bảng cục bộ được hỗ trợ: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Một đối tượng có tên này đã tồn tại trong đích. Tuy nhiên, không thể sử dụng đối tượng này làm đối tượng đích cho luồng sao chép đến kho lưu trữ cục bộ vì nó không phải là bảng cục bộ.
#XMSG
validateSourceTargetSystemDifference=Bạn phải chọn kết hợp vùng chứa và kết nối nguồn và đích khác nhau cho luồng sao chép.
#XMSG
validateDuplicateSources=một hoặc nhiều bản sao có tên đối tượng nguồn trùng lặp: {0}.
#XMSG
validateDuplicateTargets=một hoặc nhiều bản sao có tên đối tượng đích trùng lặp: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=Đối tượng nguồn {0} không hỗ trợ thu thập chênh lệch trong khi đối tượng đích {1} hỗ trợ. Bạn phải loại bỏ sao chép.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Bạn phải chọn loại tải "Ban đầu và chênh lệch" cho sao chép với tên đối tượng đích {0}.
#XMSG
validationAutoRenameTarget=Cột đích đã được đổi tên.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Phép chiếu tự động đã được thêm và các cột đích sau đây đã được đổi tên để cho phép sao chép vào đích:{1}{1} {0} {1}{1}Điều này là bởi một trong những lý do sau đây:{1}{1}{2} Ký tự không được hỗ trợ{1}{2} Tiền tố dành riêng
#XMSG
validationAutoRenameTargetDescriptionUpdated=Tự động chiếu đã được thêm và các cột đích sau đây đã được đổi tên để cho phép sao chép vào Google BigQuery:{1}{1} {0} {1}{1}Điều này là do một trong những lý do sau đây:{1}{1}{2} Tên cột dành riêng{1}{2} Ký tự không được hỗ trợ{1}{2} Tiền tố dành riêng
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Tự động chiếu đã được thêm và các cột đích sau đây đã được đổi tên để cho phép sao chép vào Confluent:{1}{1} {0} {1}{1}Điều này là do một trong những lý do sau đây:{1}{1}{2} Tên cột dành riêng{1}{2} Ký tự không được hỗ trợ{1}{2} Tiền tố dành riêng
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Phép chiếu tự động đã được thêm và các cột đích sau đây đã được đổi tên để cho phép sao chép vào đích:{1}{1} {0} {1}{1}Điều này là bởi một trong những lý do sau đây:{1}{1}{2} Tên cột dành riêng{1}{2} Ký tự không được hỗ trợ{1}{2} Tiền tố dành riêng
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Đã đổi tên đối tượng đích.
#XMSG
autoRenameInfoDesc=Đối tượng đích đã được đổi tên vì nó chứa các ký tự không được hỗ trợ. Chỉ các ký tự sau đây được hỗ trợ:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(dấu chấm){0}{1}_(gạch dưới){0}{1}-(gạch nối)
#XMSG
validationAutoTargetTypeConversion=Loại dữ liệu đích đã được thay đổi.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Đối với các cột đích sau đây, loại dữ liệu đích đã được thay đổi, vì trong Google BigQuery loại dữ liệu nguồn không được hỗ trợ:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Đối với các cột đích sau đây, loại dữ liệu đích đã được thay đổi bởi vì loại dữ liệu nguồn không được hỗ trợ trong kết nối đích:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Rút ngắn tên cột đích.
#XMSG
validationMaxCharLengthGBQTargetDescription=Trong Google BigQuery, tên cột có thể có tối đa 300 ký tự. Sử dụng phép chiếu để rút ngắn tên cột đích sau đây:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Khóa chính sẽ không được tạo.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=Trong Google BigQuery, tối đa 16 khóa chính được hỗ trợ, nhưng đối tượng nguồn có số lượng khóa chính lớn hơn. Không có khóa chính nào được tạo trong đối tượng đích.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Một hoặc nhiều cột nguồn có kiểu dữ liệu không thể xác định là khóa chính trong Google BigQuery. Không có khóa chính nào sẽ được tạo trong đối tượng đích.{0}{0}Các kiểu dữ liệu đích sau tương thích với kiểu dữ liệu Google BigQuery mà khóa chính có thể được xác định:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Xác định một hoặc nhiều cột làm khóa chính.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Bạn phải xác định một hoặc nhiều cột làm khóa chính bằng cách sử dụng hộp thoại biểu đồ nguồn để thực hiện việc này.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Xác định một hoặc nhiều cột làm khóa chính.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Bạn phải xác định một hoặc nhiều cột làm khóa chính khớp với các ràng buộc khóa chính cho đối tượng nguồn của bạn. Hãy Đi đến Cấu hình sơ đồ trong đặc tính đối tượng nguồn của bạn để thực hiện việc này.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Nhập giá trị phân vùng tối đa hợp lệ.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Giá trị phân vùng tối đa phải là ≥ 1 và ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Xác định một hoặc nhiều cột làm khóa chính.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Để sao chép một đối tượng, bạn phải xác định một hoặc nhiều cột đích làm khóa chính. Sử dụng phép chiếu để thực hiện điều này.
#XMSG
validateHDLFNoPKExistingDatasetError=Xác định một hoặc nhiều cột làm khóa chính.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1= Để sao chép dữ liệu sang đối tượng đích hiện có, nó phải có một hoặc nhiều cột được xác định làm khóa chính. {0} Bạn có các tùy chọn sau để xác định một hoặc nhiều cột làm khóa chính: {0} {1} Sử dụng trình soạn thảo bảng cục bộ để thay đổi đối tượng đích hiện có. Sau đó tải lại luồng sao chép.{0}{1} Đổi tên đối tượng đích trong luồng sao chép. Điều này sẽ tạo một đối tượng mới ngay khi bắt đầu chạy. Sau khi đổi tên, bạn có thể xác định một hoặc nhiều cột làm khóa chính trong phép chiếu.{0}{1} Ánh xạ đối tượng tới một đối tượng đích hiện có khác trong đó một hoặc nhiều cột đã được xác định làm khóa chính.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Đích được chọn đã tồn tại trong kho lưu trữ: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Tên bảng thu thập chênh lệch đã được các bảng khác trong kho lưu trữ sử dụng: {0}. Bạn phải đổi tên các đối tượng đích này để đảm bảo rằng tên bảng thu thập chênh lệch đã liên kết là duy nhất trước khi bạn có thể lưu luồng sao chép.
#XMSG
validateConfluentEmptySchema=Định nghĩa biểu đồ
#XMSG
validateConfluentEmptySchemaDescUpdated=Bảng nguồn không có biểu đồ. Hãy chọn Định cấu hình biểu đồ để định nghĩa một biểu đồ
#XMSG
validationCSVEncoding=Mã hóa CSV không hợp lệ
#XMSG
validationCSVEncodingDescription=Mã hóa CSV của tác vụ không hợp lệ.
#XMSG
validateConfluentEmptySchema=Chọn loại dữ liệu đích tương thích
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Chọn loại dữ liệu đích tương thích
#XMSG
globalValidateTargetDataTypeDesc=Đã xảy ra lỗi với ánh xạ cột. Đi đến Phép chiếu và đảm bảo rằng tất cả các cột nguồn được ánh xạ với một cột duy nhất, với một cột có loại dữ liệu tương thích và tất cả các biểu thức được xác định đều hợp lệ.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Tên cột trùng lặp.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Tên cột trùng lặp không được hỗ trợ. Sử dụng hộp thoại phép chiếu để sửa chúng. Các đối tượng đích sau đây có tên cột trùng lặp: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Tên cột trùng lặp.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Tên cột trùng lặp không được hỗ trợ. Các đối tượng đích sau đây có tên cột trùng lặp: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Có thể có sự không nhất quán trong dữ liệu.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Kiểu tải Chỉ chênh lệch sẽ không xem xét các thay đổi được thực hiện trong nguồn giữa lần lưu cuối và lần thực hiện tiếp theo.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Thay đổi kiểu tải thành "Ban đầu".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Việc sao chép các đối tượng dựa trên ABAP không có khóa chính chỉ khả thi đối với kiểu tải "Chỉ ban đầu".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Tắt tính năng thu thập chênh lệch.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Để sao chép một đối tượng không có khóa chính bằng cách sử dụng loại kết nối nguồn ABAP, trước tiên bạn phải tắt tính năng thu thập chênh lệch cho bảng này.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Thay đổi đối tượng đích.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Không thể sử dụng đối tượng đích vì tính năng thu thập chênh lệch được bật. Bạn có thể đổi tên đối tượng đích rồi tắt tính năng thu thập chênh lệch cho đối tượng mới (đã đổi tên) hoặc ánh xạ đối tượng nguồn sang đối tượng đích mà tính năng thu thập chênh lệch bị tắt.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Thay đổi đối tượng đích.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Không thể sử dụng đối tượng đích vì nó không có cột kỹ thuật bắt buộc __load_package_id. Bạn có thể đổi tên đối tượng đích bằng cách sử dụng tên chưa tồn tại. Sau đó, hệ thống sẽ tạo một đối tượng mới có cùng định nghĩa với đối tượng nguồn và chứa cột kỹ thuật. Ngoài ra, bạn có thể ánh xạ đối tượng đích đến một đối tượng hiện có và có cột kỹ thuật bắt buộc (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Thay đổi đối tượng đích.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Không thể sử dụng đối tượng đích vì nó không có cột kỹ thuật bắt buộc __load_record_id. Bạn có thể đổi tên đối tượng đích bằng cách sử dụng tên chưa tồn tại. Sau đó, hệ thống sẽ tạo một đối tượng mới có cùng định nghĩa với đối tượng nguồn và chứa cột kỹ thuật. Ngoài ra, bạn có thể ánh xạ đối tượng đích đến một đối tượng hiện có và có cột kỹ thuật bắt buộc (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Thay đổi đối tượng đích.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Không thể sử dụng đối tượng đích vì loại dữ liệu của cột kỹ thuật __load_record_id không phải là "string(44)". Bạn có thể đổi tên đối tượng đích bằng cách sử dụng tên chưa tồn tại. Sau đó, hệ thống sẽ tạo một đối tượng mới có cùng định nghĩa với đối tượng nguồn và do đó có loại dữ liệu chính xác. Ngoài ra, bạn có thể ánh xạ đối tượng đích đến một đối tượng hiện có và có cột kỹ thuật bắt buộc (__load_record_id) với loại dữ liệu chính xác.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Thay đổi đối tượng đích.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Không thể sử dụng đối tượng đích vì nó có khóa chính, trong khi đối tượng nguồn không có. Bạn có thể đổi tên đối tượng đích bằng cách sử dụng tên chưa tồn tại. Sau đó, hệ thống sẽ tạo một đối tượng mới có cùng định nghĩa với đối tượng nguồn và do đó không có khóa chính. Ngoài ra, bạn có thể ánh xạ đối tượng đích đến một đối tượng hiện có, có cột kỹ thuật bắt buộc (__load_package_id) và không có khóa chính.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Thay đổi đối tượng đích.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Không thể sử dụng đối tượng đích vì nó có khóa chính, trong khi đối tượng nguồn không có. Bạn có thể đổi tên đối tượng đích bằng cách sử dụng tên chưa tồn tại. Sau đó, hệ thống sẽ tạo một đối tượng mới có cùng định nghĩa với đối tượng nguồn và do đó không có khóa chính. Ngoài ra, bạn có thể ánh xạ đối tượng đích đến một đối tượng hiện có, có cột kỹ thuật bắt buộc (__load_record_id) và không có khóa chính.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Thay đổi đối tượng đích.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Không thể sử dụng đối tượng đích vì loại dữ liệu của cột kỹ thuật __load_package_id không phải là "binary(>=256)". Bạn có thể đổi tên đối tượng đích bằng cách sử dụng tên chưa tồn tại. Sau đó, hệ thống sẽ tạo một đối tượng mới có cùng định nghĩa với đối tượng nguồn và do đó có loại dữ liệu chính xác. Ngoài ra, bạn có thể ánh xạ đối tượng đích đến một đối tượng hiện có và có cột kỹ thuật bắt buộc (__load_package_id) với loại dữ liệu chính xác.
#XMSG
validationAutoRenameTargetDPID=Cột đích đã được đổi tên.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Gỡ bỏ đối tượng nguồn.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Đối tượng nguồn không có cột khóa, điều này không được hỗ trợ trong ngữ cảnh này.
#XMSG
validationAutoRenameTargetDPIDDescription=Đã thêm tính năng tự động chiếu và các cột đích sau đây đã được đổi tên để cho phép sao chép từ nguồn ABAP mà không có khóa :{1}{1} {0} {1}{1}Điều này là do một trong những lý do sau đây:{1}{1}{2} Tên cột được dành riêng{1}{2} Các ký tự không được hỗ trợ{1}{2} Tiền tố được dành riêng
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Sao chép vào {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Hiện tại không thể lưu và triển khai các luồng sao chép mà có {0} làm đích của chúng vì chúng tôi đang thực hiện bảo trì chức năng này.
#XMSG
TargetColumnSkippedLTF=Cột đích đã được bỏ qua.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Cột đích đã được bỏ qua do loại dữ liệu không được hỗ trợ. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Cột thời gian dưới dạng khóa sơ cấp.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Đối tượng nguồn có cột thời gian làm khóa chính, vốn không được hỗ trợ trong ngữ cảnh này.
#XMSG
validateNoPKInLTFTarget=Thiếu Khóa sơ cấp.
#XMSG
validateNoPKInLTFTargetDescription=Khóa sơ cấp không được định nghĩa trong đích, điều này không được hỗ trợ trong bối cảnh này.
#XMSG
validateABAPClusterTableLTF=Bảng cụm ABAP.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Đối tượng nguồn là bảng cụm ABAP vốn không được hỗ trợ trong ngữ cảnh này.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Có vẻ như bạn chưa thêm dữ liệu.
#YINS
welcomeText2=Để bắt đầu luồng sao chép của bạn, hãy chọn một kết nối và một đối tượng nguồn ở phía bên trái.

#XBUT
wizStep1=Chọn kết nối nguồn
#XBUT
wizStep2=Chọn vùng chứa nguồn
#XBUT
wizStep3=Thêm đối tượng nguồn

#XMSG
limitDataset=Đã đạt đến số lượng đối tượng tối đa. Xóa các đối tượng hiện có để thêm các đối tượng mới hoặc tạo luồng sao chép mới.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Luồng sao chép tới kết nối đích không phải SAP này không thể bắt đầu vì không có khối lượng đầu ra nào khả dụng trong tháng này.
#XMSG
premiumOutBoundRFAdminWarningMsg=Quản trị viên có thể tăng khối đầu ra cao cấp cho đối tượng thuê này, khiến khối lượng đầu ra trở nên khả dụng trong tháng này.
#XMSG
messageForToastForDPIDColumn2=Cột mới được thêm vào đích cho {0} đối tượng - cần thiết để xử lý các bản ghi trùng lặp trong kết nối với các đối tượng nguồn dựa trên ABAP không có khóa chính.
#XMSG
PremiumInboundWarningMessage=Phụ thuộc vào số lượng luồng sao chép và khối lượng dữ liệu sẽ được sao chép, tài nguyên SAP HANA{0} được yêu cầu để sao chép dữ liệu qua {1} có thể vượt quá công suất khả dụng đối với đối tượng thuê của bạn.
#XMSG
PremiumInboundWarningMsg=Phụ thuộc vào số lượng luồng sao chép và khối lượng dữ liệu sẽ được sao chép,{0}tài nguyên SAP HANA được yêu cầu để sao chép dữ liệu qua "{1}" có thể vượt quá công suất khả dụng đối với đối tượng thuê của bạn.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Nhập tên phép chiếu.
#XMSG
emptyTargetColumn=Nhập tên cột đích.
#XMSG
emptyTargetColumnBusinessName=Nhập tên kinh doanh của cột đích.
#XMSG
invalidTransformName=Nhập tên phép chiếu.
#XMSG
uniqueColumnName=Đổi tên cột đích.
#XMSG
copySourceColumnLbl=Sao chép cột từ đối tượng nguồn
#XMSG
renameWarning=Đảm bảo chọn tên duy nhất khi đổi tên bảng mục tiêu. Nếu bảng có tên mới đã tồn tại trong vùng dữ liệu, nó sẽ sử dụng định nghĩa của bảng đó.

#XMSG
uniqueColumnBusinessName=Đổi tên doanh nghiệp trong cột đích.
#XMSG
uniqueSourceMapping=Chọn một cột nguồn khác.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=Cột nguồn {0} đã được sử dụng bởi các cột đích sau:{1}{1}{2}{1}{1} Đối với cột đích này hoặc các cột đích khác, hãy chọn cột nguồn chưa được sử dụng để lưu phép chiếu.
#XMSG
uniqueColumnNameDescription=Tên cột đích mà bạn nhập đã tồn tại. Để có thể lưu phép chiếu, bạn cần nhập tên cột duy nhất.
#XMSG
uniqueColumnBusinessNameDesc=Tên doanh nghiệp trong cột đích đã tồn tại. Để lưu phép chiếu, bạn phải nhập tên doanh nghiệp trong cột duy nhất.
#XMSG
emptySource=Chọn cột nguồn hoặc nhập giá trị không đổi.
#XMSG
emptySourceDescription=Để tạo một mục nhập ánh xạ hợp lệ, bạn cần chọn một cột nguồn hoặc nhập một giá trị không đổi.
#XMSG
emptyExpression=Xác định ánh xạ.
#XMSG
emptyExpressionDescription1=Chọn cột nguồn mà bạn muốn ánh xạ cột đích hoặc chọn hộp kiểm trong cột {0} Hàm / Hằng số {1}. {2} {2} Các hàm được nhập tự động theo kiểu dữ liệu đích. Giá trị hằng số có thể được nhập thủ công.
#XMSG
numberExpressionErr=Nhập số.
#XMSG
numberExpressionErrDescription=Bạn đã chọn kiểu dữ liệu số. Trường hợp này có nghĩa là bạn chỉ có thể nhập số, cộng với dấu thập phân nếu có. Không sử dụng dấu ngoặc kép.
#XMSG
invalidLength=Nhập giá trị độ dài hợp lệ.
#XMSG
invalidLengthDescription=Độ dài của kiểu dữ liệu phải bằng hoặc lớn hơn độ dài của cột nguồn và có thể nằm trong khoảng từ 1 đến 5000.
#XMSG
invalidMappedLength=Nhập giá trị độ dài hợp lệ.
#XMSG
invalidMappedLengthDescription=Độ dài của kiểu dữ liệu phải bằng hoặc lớn hơn độ dài của cột nguồn {0} và có thể nằm trong khoảng từ 1 đến 5000.
#XMSG
invalidPrecision=Nhập giá trị độ chính xác hợp lệ.
#XMSG
invalidPrecisionDescription=Độ chính xác xác định tổng số chữ số. Thang đo xác định số chữ số sau dấu thập phân và có thể nằm trong khoảng từ 0 đến độ chính xác.{0}{0} Ví dụ: {0}{1} Độ chính xác 6, thang đo 2 tương ứng với các số như 1234,56.{0}{1} Độ chính xác 6, thang đo 6 tương ứng với các số như 0,123546.{0} {0} Độ chính xác và thang đo cho đích phải tương thích với độ chính xác và thang đo cho nguồn sao cho tất cả các chữ số từ nguồn khớp với trường đích. Ví dụ: nếu có độ chính xác 6 và thang đo 2 trong nguồn (và do đó các chữ số khác 0 trước dấu thập phân), bạn không thể có độ chính xác 6 và thang đo 6 trong đích.
#XMSG
invalidPrimaryKey=Nhập ít nhất một khóa chính.
#XMSG
invalidPrimaryKeyDescription=Không xác định khóa chính cho biểu đồ này.
#XMSG
invalidMappedPrecision=Nhập giá trị độ chính xác hợp lệ.
#XMSG
invalidMappedPrecisionDescription1=Độ chính xác xác định tổng số chữ số. Thang đo xác định số chữ số sau dấu thập phân và có thể nằm trong khoảng từ 0 đến độ chính xác.{0}{0} Ví dụ:{0}{1} Độ chính xác 6, thang đo 2 tương ứng với các số như 1234,56.{0}{1} Độ chính xác 6, thang đo 6 tương ứng với các số như 0,123546.{0}{0}Độ chính xác của kiểu dữ liệu phải bằng hoặc lớn hơn độ chính xác của nguồn ({2}).
#XMSG
invalidScale=Nhập giá trị thang đo hợp lệ.
#XMSG
invalidScaleDescription=Độ chính xác xác định tổng số chữ số. Thang đo xác định số chữ số sau dấu thập phân và có thể nằm trong khoảng từ 0 đến độ chính xác.{0}{0} Ví dụ: {0}{1} Độ chính xác 6, thang đo 2 tương ứng với các số như 1234,56.{0}{1} Độ chính xác 6, thang đo 6 tương ứng với các số như 0,123546.{0} {0} Độ chính xác và thang đo cho đích phải tương thích với độ chính xác và thang đo cho nguồn sao cho tất cả các chữ số từ nguồn khớp với trường đích. Ví dụ: nếu có độ chính xác 6 và thang đo 2 trong nguồn (và do đó các chữ số khác 0 trước dấu thập phân), bạn không thể có độ chính xác 6 và thang đo 6 trong đích.
#XMSG
invalidMappedScale=Nhập giá trị thang đo hợp lệ.
#XMSG
invalidMappedScaleDescription1=Độ chính xác xác định tổng số chữ số. Thang đo xác định số chữ số sau dấu thập phân và có thể nằm trong khoảng từ 0 đến độ chính xác.{0}{0} Ví dụ:{0}{1} Độ chính xác 6, thang đo 2 tương ứng với các số như 1234,56.{0}{1} Độ chính xác 6, thang đo 6 tương ứng với các số như 0,123546.{0}{0}Thang đo của kiểu dữ liệu phải bằng hoặc lớn hơn thang đo của nguồn ({2}).
#XMSG
nonCompatibleDataType=Chọn kiểu liệu đích tương thích.
#XMSG
nonCompatibleDataTypeDescription1=Kiểu dữ liệu mà bạn chỉ định ở đây phải tương thích với kiểu dữ liệu nguồn ({0}). {1}{1} Ví dụ: Nếu cột nguồn của bạn có chuỗi kiểu dữ liệu và chứa các chữ cái, bạn không thể sử dụng kiểu dữ liệu thập phân cho đích.
#XMSG
invalidColumnCount=Chọn một cột nguồn.
#XMSG
ObjectStoreInvalidScaleORPrecision=Nhập giá trị hợp lệ cho độ chính xác và thang đo.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=Giá trị đầu tiên là độ chính xác, xác định tổng số chữ số. Giá trị thứ hai là thang đo, xác định các chữ số sau dấu thập phân. Nhập giá trị thang đo đích lớn hơn giá trị thang đo nguồn và đảm bảo rằng chênh lệch giữa thang đo đích đã nhập và giá trị độ chính xác lớn hơn chênh lệch giữa thang đo nguồn và giá trị độ chính xác.
#XMSG
InvalidPrecisionORScale=Nhập giá trị hợp lệ cho độ chính xác và thang đo.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=Giá trị đầu tiên là độ chính xác, xác định tổng số chữ số. Giá trị thứ hai là thang đo, xác định các chữ số sau dấu thập phân.{0}{0}Vì kiểu dữ liệu nguồn không được hỗ trợ trong Google BigQuery nên nó được chuyển đổi thành kiểu dữ liệu đích DECIMAL. Trong trường hợp này, độ chính xác chỉ có thể được xác định trong khoảng từ 38 đến 76 và thang đo từ 9 đến 38. Ngoài ra, kết quả của độ chính xác trừ thang đo, thể hiện các chữ số trước dấu thập phân, phải nằm trong khoảng từ 29 đến 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=Giá trị đầu tiên là độ chính xác, xác định tổng số chữ số. Giá trị thứ hai là thang đo, xác định các chữ số sau dấu thập phân.{0}{0}Vì kiểu dữ liệu nguồn không được hỗ trợ trong Google BigQuery nên nó được chuyển đổi thành kiểu dữ liệu đích DECIMAL. Trong trường hợp này, độ chính xác phải được xác định là 20 hoặc lớn hơn. Ngoài ra, kết quả của độ chính xác trừ thang đo, phản ánh các chữ số trước dấu thập phân, phải là 20 hoặc lớn hơn.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=Giá trị thứ nhất là độ chính xác, vốn định nghĩa tổng số chữ số. Giá trị thứ hai là thang đo, định nghĩa các chữ số sau dấu thập phân.{0}{0}Vì loại dữ liệu nguồn không được hỗ trợ trong đích, nó được chuyển đổi thành loại dữ liệu đích DECIMAL. Trong trường hợp này, độ chính xác phải được định nghĩa bằng bất kỳ số nào lớn hơn hoặc bằng 1 và nhỏ hơn hoặc bằng 38, và thang đo nhỏ hơn hoặc bằng độ chính xác.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=Giá trị thứ nhất là độ chính xác, vốn định nghĩa tổng số chữ số. Giá trị thứ hai là thang đo, định nghĩa các chữ số sau dấu thập phân.{0}{0}Vì loại dữ liệu nguồn không được hỗ trợ trong đích, nó được chuyển đổi thành loại dữ liệu đích DECIMAL. Trong trường hợp này, độ chính xác phải được định nghĩa là 20 hoặc lớn hơn. Ngoài ra, kết quả của độ chính xác trừ thang đo, vốn phản ánh các chữ số trước dấu thập phân, phải là 20 hoặc lớn hơn.
#XMSG
invalidColumnCountDescription=Để tạo một mục nhập ánh xạ hợp lệ, bạn cần chọn một cột nguồn hoặc nhập một giá trị không đổi.
#XMSG
duplicateColumns=Đổi tên cột đích.
#XMSG
duplicateGBQCDCColumnsDesc=Tên cột đích được đặt trước trong Google BigQuery. Bạn cần đổi tên nó để có thể lưu phép chiếu.
#XMSG
duplicateConfluentCDCColumnsDesc=Tên cột đích được đặt trước trong Confluent. Bạn cần đổi tên để có thể lưu phép chiếu.
#XMSG
duplicateSignavioCDCColumnsDesc=Tên cột đích được đặt trước trong SAP Signavio. Bạn cần đổi tên để có thể lưu phép chiếu.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Tên cột đích được đặt trước trong MS OneLake. Bạn cần đổi tên để có thể lưu phép chiếu.
#XMSG
duplicateSFTPCDCColumnsDesc=Tên cột đích được đặt trước trong SFTP. Bạn cần đổi tên để có thể lưu phép chiếu.
#XMSG
GBQTargetNameWithPrefixUpdated1=Tên cột đích chứa một tiền tố được đặt trước trong Google BigQuery. Bạn cần đổi tên nó để có thể lưu phép chiếu. {0}{0}Tên cột đích không thể bắt đầu với bất kỳ chuỗi nào sau đây:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Rút ngắn tên cột đích.
#XMSG
GBQtargetMaxLengthDesc=Trong Google BigQuery, tên cột có thể sử dụng tối đa 300 ký tự. Rút gọn tên cột đích để có thể lưu phép chiếu.
#XMSG
invalidMappedScalePrecision=Độ chính xác và thang đo cho đích phải tương thích với độ chính xác và thang đo cho nguồn để tất cả các chữ số từ nguồn phù hợp với trường đích.
#XMSG
invalidMappedScalePrecisionShortText=Nhập giá trị độ chính xác và thang đo hợp lệ.
#XMSG
validationIncompatiblePKTypeDescProjection3=Một hoặc nhiều cột nguồn có loại dữ liệu không thể được xác định làm khóa chính trong Google BigQuery. Không có khóa chính nào sẽ được tạo trong đối tượng đích.{0}{0}Các kiểu dữ liệu đích sau đây tương thích với kiểu dữ liệu Google BigQuery mà khóa chính có thể được xác định:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Bỏ chọn column __message_id.
#XMSG
uncheckColumnMessageIdDesc=Cột: Khóa chính
#XMSG
validationOpCodeInsert=Bạn phải nhập giá trị cho Chèn.
#XMSG
recommendDifferentPrimaryKey=Chúng tôi khuyên bạn nên chọn khóa chính khác ở cấp mục.
#XMSG
recommendDifferentPrimaryKeyDesc=Khi mã hoạt động đã được xác định, bạn nên chọn các khóa chính khác nhau cho chỉ mục mảng và các mục để tránh các vấn đề như trùng lặp cột chẳng hạn.
#XMSG
selectPrimaryKeyItemLevel=Bạn phải chọn ít nhất một khóa chính cho cả tiêu đề và cấp mục.
#XMSG
selectPrimaryKeyItemLevelDesc=Khi mảng hoặc bản đồ được mở rộng, bạn phải chọn hai khóa chính, một ở cấp tiêu đề và một ở cấp mục.
#XMSG
invalidMapKey=Bạn phải chọn ít nhất một khóa chính ở cấp tiêu đề.
#XMSG
invalidMapKeyDesc=Khi mảng hoặc bản đồ được mở rộng, bạn phải chọn khóa chính ở cấp tiêu đề.
#XFLD
txtSearchFields=Tìm kiếm cột đích
#XFLD
txtName=Tên
#XMSG
txtSourceColValidation=Một hoặc nhiều cột nguồn không được hỗ trợ:
#XMSG
txtMappingCount=Ánh xạ ({0})
#XMSG
schema=Biểu đồ
#XMSG
sourceColumn=Cột nguồn
#XMSG
warningSourceSchema=Bất kỳ thay đổi nào được thực hiện đối với biểu đồ sẽ ảnh hưởng đến ánh xạ trong hộp thoại phép chiếu.
#XCOL
txtTargetColName=Cột đích (Tên kỹ thuật)
#XCOL
txtDataType=Loại dữ liệu đích
#XCOL
txtSourceDataType=Loại dữ liệu nguồn
#XCOL
srcColName=Cột nguồn (Tên kỹ thuật)
#XCOL
precision=Độ chính xác
#XCOL
scale=Tỷ lệ
#XCOL
functionsOrConstants=Hàm / Hằng số
#XCOL
txtTargetColBusinessName=Cột đích (Tên doanh nghiệp)
#XCOL
prKey=Khóa chính
#XCOL
txtProperties=Đặc tính
#XBUT
txtOK=Lưu
#XBUT
txtCancel=Hủy
#XBUT
txtRemove=Loại bỏ
#XFLD
txtDesc=Mô tả
#XMSG
rftdMapping=Ánh xạ
#XFLD
@lblColumnDataType=Kiểu dữ liệu
#XFLD
@lblColumnTechnicalName=Tên kỹ thuật
#XBUT
txtAutomap=Ánh xạ tự động
#XBUT
txtUp=Lên
#XBUT
txtDown=Xuống

#XTOL
txtTransformationHeader=Hình chiếu
#XTOL
editTransformation=Hiệu chỉnh
#XTOL
primaryKeyToolip=Khóa


#XMSG
rftdFilter=Bộ lọc
#XMSG
rftdFilterColumnCount=Nguồn: {0}({1})
#XTOL
rftdFilterColSearch=Tìm kiếm
#XMSG
rftdFilterColNoData=Không có cột nào để hiển thị
#XMSG
rftdFilteredColNoExps=Không có biểu thức bộ lọc
#XMSG
rftdFilterSelectedColTxt=Thêm bộ lọc cho
#XMSG
rftdFilterTxt=Bộ lọc có sẵn cho
#XBUT
rftdFilterSelectedAddColExp=Thêm biểu thức
#YINS
rftdFilterNoSelectedCol=Chọn một cột để thêm bộ lọc.
#XMSG
rftdFilterExp=Biểu thức bộ lọc
#XMSG
rftdFilterNotAllowedColumn=Không hỗ trợ thêm bộ lọc cho cột này.
#XMSG
rftdFilterNotAllowedHead=Không hỗ trợ cột
#XMSG
rftdFilterNoExp=Không có bộ lọc nào được xác định
#XTOL
rftdfilteredTt=Đã được lọc
#XTOL
rftdremoveexpTt=Xóa biểu thức bộ lọc
#XTOL
validationMessageTt=Thông báo xác thực
#XTOL
rftdFilterDateInp=Chọn ngày
#XTOL
rftdFilterDateTimeInp=Chọn ngày giờ
#XTOL
rftdFilterTimeInp=Chọn thời gian
#XTOL
rftdFilterInp=Nhập giá trị
#XMSG
rftdFilterValidateEmptyMsg={0} biểu thức bộ lọc trên cột {1} trống
#XMSG
rftdFilterValidateInvalidNumericMsg={0} biểu thức bộ lọc trên cột {1} chứa giá trị bằng số không hợp lệ
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Biểu thức bộ lọc phải chứa giá trị bằng số hợp lệ
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Nếu sơ đồ đối tượng đích đã thay đổi, hãy sử dụng chức năng “Ánh xạ đến Đối tượng đích hiện có” trên trang chính để điều chỉnh các thay đổi và ánh xạ lại đối tượng đích với nguồn của nó một lần nữa.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Nếu bảng đích đã tồn tại và ánh xạ bao gồm thay đổi lược đồ, bạn phải thay đổi bảng đích tương ứng trước khi triển khai luồng sao chép.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Nếu ánh xạ của bạn bao gồm thay đổi lược đồ, bạn phải thay đổi bảng đích tương ứng trước khi triển khai luồng sao chép.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Các cột không được hỗ trợ sau bị bỏ qua khỏi xác định nguồn: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Các cột không được hỗ trợ sau bị bỏ qua khỏi xác định nguồn: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Các đối tượng sau đây không được hỗ trợ vì chúng được sử dụng: {0} {1} {0} {0} Để sử dụng các bảng trong luồng sao chép, không được đặt cách sử dụng ngữ nghĩa (trong cài đặt bảng) thành {2}Bộ dữ liệu phân tích{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Đối tượng mục tiêu không thể được sử dụng vì nó được sử dụng để tiêu thụ. {0} {0}  Để sử dụng bảng trong luồng sao chép, không được đặt cách sử dụng ngữ nghĩa (trong cài đặt bảng) thành {1}Bộ dữ liệu phân tích{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Đối tượng mục tiêu có tên này đã tồn tại. Tuy nhiên, nó không thể được sử dụng vì nó được hiển thị để sử dụng. {0} {0} Để sử dụng bảng trong luồng sao chép, không được đặt cách sử dụng ngữ nghĩa (trong cài đặt bảng) thành {1}Bộ dữ liệu phân tích{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Một đối tượng có tên này đã tồn tại trong đích. {0}Tuy nhiên, không thể sử dụng đối tượng này làm đối tượng đích cho luồng sao chép đến kho lưu trữ cục bộ vì nó không phải là bảng cục bộ.
#XMSG:
targetAutoRenameUpdated=Cột đích đã được đổi tên.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Cột đích đã được đổi tên để cho phép sao chép trong Google BigQuery. Trường hợp này là do một trong những lý do sau:{0} {1}{2}Tên cột đặt trước{3}{2}Ký tự không được hỗ trợ{3}{2}Tiền tố đặt trước{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Cột đích đã được đổi tên để cho phép sao chép trong Confluent. Trường hợp này là do một trong những lý do sau:{0} {1}{2}Tên cột đặt trước{3}{2}Ký tự không được hỗ trợ{3}{2}Tiền tố đặt trước{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Cột đích đã được đổi tên để cho phép sao chép vào đích. Điều này là bởi một trong những lý do sau:{0} {1}{2}Ký tự không được hỗ trợ{3}{2}Tiền tố dành riêng{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Cột đích đã được đổi tên để cho phép sao chép vào đích. Điều này là bởi một trong những lý do sau đây:{0} {1}{2}Tên cột dành riêng{3}{2}Ký tự không được hỗ trợ{3}{2}Tiền tố dành riêng{3}{4}
#XMSG:
targetAutoDataType=Kiểu dữ liệu đích đã được thay đổi.
#XMSG:
targetAutoDataTypeDesc=Kiểu dữ liệu đích đã được thay đổi thành {0} vì kiểu dữ liệu nguồn không được hỗ trợ trong Google BigQuery.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Loại dữ liệu đích đã được thay đổi thành {0} vì loại dữ liệu nguồn không được hỗ trợ trong kết nối đích.
#XMSG
projectionGBQUnableToCreateKey=Khóa chính sẽ không được tạo.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=Trong Google BigQuery, tối đa 16 khóa chính được hỗ trợ, nhưng đối tượng nguồn có số lượng khóa chính lớn hơn. Không có khóa chính nào được tạo trong đối tượng đích.
#XMSG
HDLFNoKeyError=Xác định một hoặc nhiều cột làm khóa chính.
#XMSG
HDLFNoKeyErrorDescription=Để sao chép một đối tượng, bạn phải xác định một hoặc nhiều cột làm khóa chính.
#XMSG
HDLFNoKeyErrorExistingTarget=Xác định một hoặc nhiều cột làm khóa chính.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3= Để sao chép dữ liệu sang đối tượng đích hiện có, nó phải có một hoặc nhiều cột được xác định làm khóa chính. {0}{0} Bạn có các tùy chọn sau để xác định một hoặc nhiều cột làm khóa chính: {0}{0}{1} Sử dụng trình soạn thảo bảng cục bộ để thay đổi đối tượng đích hiện có. Sau đó tải lại luồng sao chép.{0}{0}{1} Đổi tên đối tượng đích trong luồng sao chép. Điều này sẽ tạo một đối tượng mới ngay khi bắt đầu chạy. Sau khi đổi tên, bạn có thể xác định một hoặc nhiều cột làm khóa chính trong phép chiếu.{0}{0}{1} Ánh xạ đối tượng tới một đối tượng đích hiện có khác trong đó một hoặc nhiều cột đã được xác định làm khóa chính.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Khóa chính đã thay đổi.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=So với đối tượng nguồn, bạn đã xác định các cột khác nhau làm khóa chính cho đối tượng đích. Hãy đảm bảo rằng các cột này xác định duy nhất tất cả các hàng để tránh lỗi dữ liệu có thể xảy ra khi sao chép dữ liệu sau này. {0} {0} Trong đối tượng nguồn, các cột sau được xác định là khóa chính: {0} {1}
#XMSG
duplicateDPIDColumns=Đổi tên cột đích.
#XMSG
duplicateDPIDDColumnsDesc1=Tên cột đích này được dành riêng cho cột kỹ thuật. Nhập tên khác để lưu phép chiếu.
#XMSG:
targetAutoRenameDPID=Cột đích đã được đổi tên.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Cột đích đã được đổi tên để cho phép sao chép từ nguồn ABAP không có khóa. Trường hợp này là do một trong những lý do sau đây:{0} {1}{2}Tên cột được dành riêng{3}{2}Ký tự không được hỗ trợ{3}{2}Tiền tố được đặt trước{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle={0} Thiết lập đích
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle={0} Thiết lập nguồn
#XBUT
connectionSettingSave=Lưu
#XBUT
connectionSettingCancel=Hủy
#XBUT: Button to keep the object level settings
txtKeep=Giữ
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Ghi đè
#XFLD
targetConnectionThreadlimit=Giới hạn chuỗi đích cho tải ban đầu (1-100)
#XFLD
connectionThreadLimit=Giới hạn chuỗi nguồn cho tải ban đầu (1-100)
#XFLD
maxConnection=Giới hạn chuỗi sao chép (1-100)
#XFLD
kafkaNumberOfPartitions=Số phân vùng
#XFLD
kafkaReplicationFactor=Hệ số sao chép
#XFLD
kafkaMessageEncoder=Bộ mã hóa thông báo
#XFLD
kafkaMessageCompression=Nén thông báo
#XFLD
fileGroupDeltaFilesBy=Chênh lệch nhóm theo
#XFLD
fileFormat=Kiểu tập tin
#XFLD
csvEncoding=Mã hóa CSV
#XFLD
abapExitLbl=ABAP Exit
#XFLD
deltaPartition=Số lượng chuỗi đối tượng cho tải chênh lệch (1-10)
#XFLD
clamping_Data=Không thành công khi xén bớt dữ liệu
#XFLD
fail_On_Incompatible=Không thành công trên dữ liệu không tương thích
#XFLD
maxPartitionInput=Số phân vùng tối đa
#XFLD
max_Partition=Xác định số lượng phân vùng tối đa
#XFLD
include_SubFolder=Bao gồm thư mục con
#XFLD
fileGlobalPattern=Mẫu toàn cục cho tên tập tin
#XFLD
fileCompression=Nén tập tin
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Dấu phân cách tập tin
#XFLD
fileIsHeaderIncluded=Tiêu đề tập tin
#XFLD
fileOrient=Định hướng
#XFLD
gbqWriteMode=Chế độ ghi
#XFLD
suppressDuplicate=Chặn các trùng lặp
#XFLD
apacheSpark=Bật chế độ tương thích Apache Spark
#XFLD
clampingDatatypeCb=Chốt giữ kiểu dữ liệu điểm động thập phân
#XFLD
overwriteDatasetSetting=Ghi đè thiết lập đích tại cấp đối tượng
#XFLD
overwriteSourceDatasetSetting=Ghi đè thiết lập nguồn tại cấp đối tượng
#XMSG
kafkaInvalidConnectionSetting=Nhập số từ {0} đến {1}.
#XMSG
MinReplicationThreadErrorMsg=Nhập số lớn hơn {0}.
#XMSG
MaxReplicationThreadErrorMsg=Nhập số nhỏ hơn {0}.
#XMSG
DeltaThreadErrorMsg=Nhập giá trị từ 1 đến 10.
#XMSG
MaxPartitionErrorMsg=Nhập giá trị giữa 1 <= x <= 2147483647. Giá trị mặc định là 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Nhập số nguyên từ {0} đến {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Sử dụng hệ số sao chép của người môi giới
#XFLD
serializationFormat=Định dạng chuỗi hóa
#XFLD
compressionType=Kiểu nén
#XFLD
schemaRegistry=Sử dụng đăng ký sơ đồ
#XFLD
subjectNameStrat=Chiến lược tên chủ đề
#XFLD
compatibilityType=Kiểu tương thích
#XFLD
confluentTopicName=Tên chủ đề
#XFLD
confluentRecordName=Tên bản ghi
#XFLD
confluentSubjectNamePreview=Xem trước tên chủ đề
#XMSG
serializationChangeToastMsgUpdated2=Định dạng chuỗi hóa được thay đổi thành JSON do đăng ký lược đồ không được bật. Để thay đổi định dạng chuỗi hóa trở lại AVRO, trước tiên bạn phải bật đăng ký lược đồ.
#XBUT
confluentTopicNameInfo=Tên chủ đề luôn dựa trên tên đối tượng đích. Bạn có thể thay đổi tên chủ đề bằng cách đổi tên đối tượng đích.
#XMSG
emptyRecordNameValidationHeaderMsg=Nhập tên bản ghi.
#XMSG
emptyPartionHeader=Nhập số phân vùng.
#XMSG
invalidPartitionsHeader=Nhập số phân vùng hợp lệ.
#XMSG
invalidpartitionsDesc=Nhập một số từ 1 đến 200.000.
#XMSG
emptyrFactorHeader=Nhập hệ số sao chép.
#XMSG
invalidrFactorHeader=Nhập hệ số sao chép hợp lệ.
#XMSG
invalidrFactorDesc=Nhập một số từ 1 đến 32.767.
#XMSG
emptyRecordNameValidationDescMsg=Nếu định dạng chuỗi hóa "AVRO" được sử dụng thì chỉ hỗ trợ các ký tự sau đây:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(gạch dưới)
#XMSG
validRecordNameValidationHeaderMsg=Nhập tên bản ghi hợp lệ.
#XMSG
validRecordNameValidationDescMsgUpdated=Vì định dạng chuỗi hóa "AVRO" được sử dụng nên tên bản ghi chỉ được bao gồm các ký tự chữ và số (A-Z, a-z, 0-9) và dấu gạch dưới (_). Nó phải bắt đầu bằng một chữ cái hoặc dấu gạch dưới.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=Có thể thiết lập “Số lượng chuỗi đối tượng cho tải chênh lệch” ngay khi một hoặc nhiều đối tượng có kiểu tải “Ban đầu và Chênh lệch”.
#XMSG
invalidTargetName=Tên cột không hợp lệ
#XMSG
invalidTargetNameDesc=Tên cột đích chỉ được bao gồm các ký tự chữ và số (A-Z, a-z, 0-9) và dấu gạch dưới (_).
#XFLD
consumeOtherSchema=Tiêu thụ các phiên bản biểu đồ khác
#XFLD
ignoreSchemamissmatch=Bỏ qua biểu đồ không khớp
#XFLD
confleuntDatatruncation=Không thành công khi xén bớt dữ liệu
#XFLD
isolationLevel=Mức cô lập
#XFLD
confluentOffset=Điểm bắt đầu
#XFLD
signavioGroupDeltaFilesByText=Không có
#XFLD
signavioFileFormatText=Sàn gỗ
#XFLD
signavioSparkCompatibilityParquetText=Không
#XFLD
siganvioFileCompressionText=Có gờ
#XFLD
siganvioSuppressDuplicatesText=Không

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Hình chiếu
#XBUT
txtAdd=Thêm vào
#XBUT
txtEdit=Hiệu chỉnh
#XMSG
transformationText=Thêm hình chiếu để thiết lập bộ lọc hoặc ánh xạ.
#XMSG
primaryKeyRequiredText=Chọn khóa chính bằng Cấu hình sơ đồ.
#XFLD
lblSettings=Thiết lập
#XFLD
lblTargetSetting={0}: Thiết lập đích
#XMSG
@csvRF=Chọn tập tin chứa định nghĩa biểu đồ mà bạn muốn áp dụng cho tất cả tập tin trong thư mục.
#XFLD
lblSourceColumns=Cột nguồn
#XFLD
lblJsonStructure=Cấu trúc JSON
#XFLD
lblSourceSetting={0}: Thiết lập nguồn
#XFLD
lblSourceSchemaSetting={0}: Thiết lập biểu đồ nguồn
#XBUT
messageSettings=Thiết lập thông báo
#XFLD
lblPropertyTitle1=Thuộc tính đối tượng
#XFLD
lblRFPropertyTitle=Thuộc tính luồng sao chép
#XMSG
noDataTxt=Không có cột để hiển thị.
#XMSG
noTargetObjectText=Không có đối tượng đích nào được chọn.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Cột đích
#XMSG
searchColumns=Tìm kiếm cột
#XTOL
cdcColumnTooltip=Cột để thu thập chênh lệch
#XMSG
sourceNonDeltaSupportErrorUpdated=Đối tượng nguồn không hỗ trợ thu thập chênh lệch.
#XMSG
targetCDCColumnAdded=2 cột đích được thêm để thu thập chênh lệch.
#XMSG
deltaPartitionEnable=Giới hạn chuỗi đối tượng cho tải chênh lệch được thêm vào thiết lập nguồn.
#XMSG
attributeMappingRemovalTxt=Xóa các ánh xạ không hợp lệ không được hỗ trợ cho đối tượng đích mới.
#XMSG
targetCDCColumnRemoved=2 cột đích được sử dụng để thu thập chênh lệch đã bị loại bỏ.
#XMSG
replicationLoadTypeChanged=Loại tải được thay đổi thành "Ban đầu và chênh lệch".
#XMSG
sourceHDLFLoadTypeError=Thay đổi kiểu tải thành "Ban đầu và chênh lệch".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Để sao chép một đối tượng từ kết nối nguồn có kiểu kết nối SAP HANA Cloud, Tập tin hồ dữ liệu sang SAP Datasphere, bạn phải sử dụng kiểu tải "ban đầu và chênh lệch".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Bật tính năng thu thập chênh lệch.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Để sao chép một đối tượng từ kết nối nguồn có kiểu kết nối SAP HANA Cloud, Tập tin hồ dữ liệu sang SAP Datasphere, bạn phải bật tính năng thu thập chênh lệch.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Thay đổi đối tượng đích.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Không thể sử dụng đối tượng đích vì tính năng thu thập chênh lệch bị tắt. Bạn có thể hoặc đổi tên đối tượng đích (cho phép tạo một đối tượng mới với tính năng thu thập chênh lệch sẽ được tạo) hoặc ánh xạ đến một đối tượng hiện có với tính năng thu thập chênh lệch được bật.
#XMSG
deltaPartitionError=Nhập số lượng chuỗi đối tượng hợp lệ cho tải chênh lệch.
#XMSG
deltaPartitionErrorDescription=Nhập giá trị từ 1 đến 10.
#XMSG
deltaPartitionEmptyError=Nhập số lượng chuỗi đối tượng cho tải chênh lệch.
#XFLD
@lblColumnDescription=Mô tả
#XMSG
@lblColumnDescriptionText1=Vì mục đích kỹ thuật - xử lý các bản ghi trùng lặp do sự cố trong lúc sao chép các đối tượng nguồn dựa trên ABAP không có khóa chính.
#XFLD
storageType=Lưu trữ
#XFLD
skipUnmappedColLbl=Bỏ qua cột không được ánh xạ
#XFLD
abapContentTypeLbl=Loại nội dung
#XFLD
autoMergeForTargetLbl=Tự động sáp nhập dữ liệu
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Chung
#XFLD
lblBusinessName=Tên doanh nghiệp
#XFLD
lblTechnicalName=Tên kỹ thuật
#XFLD
lblPackage=Gói
#XFLD
statusPanel=Trạng thái thực hiện
#XBTN: Schedule dropdown menu
SCHEDULE=Lập lịch
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Hiệu chỉnh lịch
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Xóa lịch
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Tạo lịch
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Kiểm tra xác thực lịch không thành công
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Không thể tạo lịch vì luồng sao chép hiện thời đang được triển khai.{0}Vui lòng chờ đến khi luồng sao chép đã được triển khai.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Đối với luồng sao chép có chứa các đối tượng có kiểu tải "Ban đầu và Chênh lệch", bạn không thể tạo bất kỳ lịch nào.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Đối với luồng sao chép có chứa các đối tượng có kiểu tải "Ban đầu và Chênh lệch/Chỉ chênh lệch", bạn không thể tạo bất kỳ lịch nào.
#XFLD : Scheduled popover
SCHEDULED=Đã lập lịch
#XFLD
CREATE_REPLICATION_TEXT=Tạo luồng sao chép
#XFLD
EDIT_REPLICATION_TEXT=Hiệu chỉnh luồng sao chép
#XFLD
DELETE_REPLICATION_TEXT=Xóa luồng sao chép
#XFLD
REFRESH_FREQUENCY=Tần suất
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Không thể triển khai luồng sao chép vì lịch hiện có{0} chưa hỗ trợ kiểu tải "Ban đầu và Chênh lệch".{0}{0}Để triển khai luồng sao chép, bạn phải đặt kiểu tải của tất cả các đối tượng{0} thành "Chỉ ban đầu". Ngoài ra, bạn có thể xóa lịch, triển khai luồng sao chép {0}, sau đó bắt đầu thực hiện mới. Điều này dẫn đến việc thực hiện {0} liên tục, cũng như hỗ trợ các đối tượng có kiểu tải "Ban đầu và chênh lệch".
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Không thể triển khai luồng sao chép vì lịch hiện có{0} chưa hỗ trợ kiểu tải "Ban đầu và Chênh lệch/Chỉ chênh lệch".{0}{0}Để triển khai luồng sao chép, bạn phải đặt kiểu tải của tất cả các đối tượng{0} thành "Chỉ ban đầu". Ngoài ra, bạn có thể xóa lịch, triển khai luồng sao chép {0}, sau đó bắt đầu thực hiện mới. Điều này dẫn đến việc thực hiện {0} liên tục, cũng như hỗ trợ các đối tượng có kiểu tải "Ban đầu và Chênh lệch/Chỉ chênh lệch".
#XMSG
SCHEDULE_EXCEPTION=Không lấy được chi tiết về lịch
#XFLD: Label for frequency column
everyLabel=Mỗi
#XFLD: Plural Recurrence text for Hour
hoursLabel=Giờ
#XFLD: Plural Recurrence text for Day
daysLabel=Ngày
#XFLD: Plural Recurrence text for Month
monthsLabel=Tháng
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Phút
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Không thể lấy thông tin về khả năng lập lịch.
#XFLD :Paused field
PAUSED=Đã tạm dừng
#XMSG
navToMonitoring=Mở trong công cụ giám sát luồng
#XFLD
statusLbl=Trạng thái
#XFLD
lblLastRunExecuted=Bắt đầu thực hiện lần cuối
#XFLD
lblLastExecuted=Thực hiện lần cuối
#XFLD: Status text for Completed
statusCompleted=Đã hoàn tất
#XFLD: Status text for Running
statusRunning=Chạy
#XFLD: Status text for Failed
statusFailed=Bị lỗi
#XFLD: Status text for Stopped
statusStopped=Đã dừng
#XFLD: Status text for Stopping
statusStopping=Đang dừng
#XFLD: Status text for Active
statusActive=Đang hoạt động
#XFLD: Status text for Paused
statusPaused=Đã tạm dừng
#XFLD: Status text for not executed
lblNotExecuted=Chưa thực hiện
#XFLD
messagesSettings=Thiết lập thông báo
#XTOL
@validateModel=Thông báo xác thực
#XTOL
@hierarchy=Phân cấp
#XTOL
@columnCount=Số lượng cột
#XMSG
VAL_PACKAGE_CHANGED=Bạn đã gán đối tượng này cho gói “{1}”. Nhấp vào “Lưu” để xác nhận và xác thực thay đổi này. Lưu ý rằng không thể hoàn tác gán cho gói trong trình soạn thảo này sau khi lưu.
#XMSG
MISSING_DEPENDENCY=Phần phụ thuộc của đối tượng ''{0}'' không thể được giải quyết trong gói “{1}”.
#XFLD
deltaLoadInterval=Khoảng tải chênh lệch
#XFLD
lblHour=Giờ (0-24)
#XFLD
lblMinutes=Phút (0-59)
#XMSG
maxHourOrMinErr=Nhập giá trị từ 0 đến {0}
#XMSG
maxDeltaInterval=Giá trị tối đa của khoảng thời gian tải chênh lệch là 24 giờ.{0}Thay đổi giá trị phút hoặc giá trị giờ tương ứng.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Đường dẫn vùng chứa đích
#XFLD
confluentSubjectName=Tên chủ thể
#XFLD
confluentSchemaVersion=Phiên bản biểu đồ
#XFLD
confluentIncludeTechKeyUpdated=Bao gồm khóa kỹ thuật
#XFLD
confluentOmitNonExpandedArrays=Bỏ qua các mảng không mở rộng
#XFLD
confluentExpandArrayOrMap=Mở rộng mảng hoặc bản đồ
#XCOL
confluentOperationMapping=Ánh xạ hoạt động
#XCOL
confluentOpCode=Mã lệnh
#XFLD
confluentInsertOpCode=Chèn
#XFLD
confluentUpdateOpCode=Cập nhật
#XFLD
confluentDeleteOpCode=Xóa
#XFLD
expandArrayOrMapNotSelectedTxt=Không được chọn
#XFLD
confluentSwitchTxtYes=Có
#XFLD
confluentSwitchTxtNo=Không
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Lỗi
#XTIT
executeWarning=Cảnh báo
#XMSG
executeunsavederror=Lưu luồng sao chép của bạn trước khi thực hiện.
#XMSG
executemodifiederror=Có những thay đổi chưa lưu trong luồng sao chép. Vui lòng lưu luồng dữ liệu.
#XMSG
executeundeployederror=Bạn phải triển khai luồng sao chép của bạn trước khi có thể chạy.
#XMSG
executedeployingerror=Vui lòng đợi triển khai kết thúc.
#XMSG
msgRunStarted=Lần chạy đã bắt đầu
#XMSG
msgExecuteFail=Không thể chạy luồng sao chép
#XMSG
titleExecuteBusy=Vui lòng chờ.
#XMSG
msgExecuteBusy=Chúng tôi đang chuẩn bị dữ liệu của bạn để chạy luồng sao chép.
#XTIT
executeConfirmDialog=Cảnh báo
#XMSG
msgExecuteWithValidations=Luồng sao chép có lỗi xác thực. Việc chạy luồng sao chép có thể dẫn đến lỗi.
#XMSG
msgRunDeployedVersion=Có những thay đổi để triển khai. Phiên bản được triển khai cuối của luồng sao chép sẽ được thực hiện. Bạn có muốn tiếp tục không?
#XBUT
btnExecuteAnyway=Vẫn tiếp tục thực hiện
#XBUT
btnExecuteClose=Đóng
#XBUT
loaderClose=Đóng
#XTIT
loaderTitle=Đang tải
#XMSG
loaderText=Tìm nhận chi tiết từ máy chủ
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Luồng sao chép tới kết nối đích không phải SAP này không thể bắt đầu.
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=vì không có khối lượng đầu ra nào khả dụng trong tháng này.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Quản trị viên có thể tăng khối đầu ra cao cấp cho đối tượng thuê này,
#XMSG
premiumOutBoundRFAdminErrMsgPart2=làm cho khối lượng đầu ra trở nên khả dụng trong tháng này.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Lỗi
#XTIT
deployInfo=Thông tin
#XMSG
deployCheckFailException=Đã xảy ra ngoại lệ trong quá trình triển khai
#XMSG
deployGBQFFDisabled=Hiện không thể triển khai luồng sao chép với kết nối đích tới Google BigQuery vì chúng tôi đang tiến hành bảo trì chức năng này.
#XMSG
deployKAFKAFFDisabled=Hiện không thể triển khai luồng sao chép với kết nối đích tới Apache Kafka vì chúng tôi đang tiến hành bảo trì chức năng này.
#XMSG
deployConfluentDisabled=Hiện thời không thể triển khai luồng sao chép với kết nối đích đến Confluent Kafka vì chúng tôi đang thực hiện bảo trì chức năng này.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Đối với các đối tượng đích sau, tên bảng thu thập chênh lệch đã được sử dụng bởi các bảng khác trong kho lưu trữ: {0} Bạn phải đổi tên các đối tượng đích này để đảm bảo rằng tên bảng thu thập chênh lệch được liên kết là duy nhất trước khi bạn có thể triển khai luồng sao chép.
#XMSG
deployDWCSourceFFDisabled=Hiện không thể triển khai luồng sao chép có SAP Datasphere làm nguồn vì chúng tôi đang tiến hành bảo trì chức năng này.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Hiện không thể triển khai các luồng sao chép chứa các bảng cục bộ hỗ trợ chênh lệch làm đối tượng nguồn vì chúng tôi đang tiến hành bảo trì chức năng này.
#XMSG
deployHDLFSourceFFDisabled=Hiện không thể thực hiện triển khai các luồng sao chép có loại kết nối nguồn với loại kết nối SAP HANA Cloud, Tập tin hồ dữ liệu vì chúng tôi đang tiến hành bảo trì.
#XMSG
deployObjectStoreAsSourceFFDisabled=Hiện tại không thể triển khai các luồng sao chép có nhà cung cấp lưu trữ đám mây làm nguồn của họ.
#XMSG
deployConfluentSourceFFDisabled=Hiện tại không thể triển khai luồng sao chép có Confluent Kafka làm nguồn vì chúng tôi đang tiến hành bảo trì chức năng này.
#XMSG
deployMaxDWCNewTableCrossed=Đối với các luồng sao chép lớn, không thể "lưu và triển khai" chúng cùng một lúc. Trước tiên, vui lòng lưu luồng sao chép của bạn, sau đó hãy triển khai.
#XMSG
deployInProgressInfo=Triển khai đã được tiến hành.
#XMSG
deploySourceObjectInUse=Đối tượng nguồn {0} đang được sử dụng trong các luồng sao chép {1}.
#XMSG
deployTargetSourceObjectInUse=Đối tượng nguồn {0} đang được sử dụng trong luồng sao chép {1}. Đối tượng đích {2} đang được sử dụng trong các luồng sao chép {3}.
#XMSG
deployReplicationFlowCheckError=Lỗi khi xác thực luồng sao chép: {0}
#XMSG
preDeployTargetObjectInUse=Đối tượng đích {0} đang được sử dụng trong luồng sao chép {1}, và bạn không thể có cùng đối tượng đích trong hai luồng sao chép khác nhau. Chọn đối tượng đích khác và thử lại.
#XMSG
runInProgressInfo=Luồng sao chép đang chạy rồi.
#XMSG
deploySignavioTargetFFDisabled=Hiện tại không thể triển khai các luồng sao chép mà có SAP Signavio làm đích của chúng vì chúng tôi đang thực hiện bảo trì chức năng này.
#XMSG
deployHanaViewAsSourceFFDisabled=Hiện không thể triển khai luồng sao chép có màn hình là đối tượng nguồn cho kết nối nguồn đã chọn. Hãy thử lại sau.
#XMSG
deployMsOneLakeTargetFFDisabled=Hiện tại không thể triển khai các luồng sao chép mà có MS OneLake làm đích của chúng vì chúng tôi đang thực hiện bảo trì chức năng này.
#XMSG
deploySFTPTargetFFDisabled=Hiện tại không thể triển khai các luồng sao chép mà có SFTP làm đích của chúng vì chúng tôi đang thực hiện bảo trì chức năng này.
#XMSG
deploySFTPSourceFFDisabled=Hiện tại không thể triển khai các luồng sao chép mà có SFTP làm nguồn của chúng vì chúng tôi đang thực hiện bảo trì chức năng này.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Tên kỹ thuật
#XFLD
businessNameInRenameTarget=Tên doanh nghiệp
#XTOL
renametargetDialogTitle=Đổi tên đối tượng đích
#XBUT
targetRenameButton=Đổi tên
#XBUT
targetRenameCancel=Hủy
#XMSG
mandatoryTargetName=Bạn phải nhập tên.
#XMSG
dwcSpecialChar=_(dấu gạch dưới) là ký tự đặc biệt duy nhất được phép.
#XMSG
dwcWithDot=Tên bảng đích có thể bao gồm các chữ cái Latinh, số, dấu gạch dưới (_) và dấu chấm (.). Ký tự đầu tiên phải là một chữ cái, số hoặc dấu gạch dưới (không phải dấu chấm).
#XMSG
nonDwcSpecialChar=Các ký tự đặc biệt được phép là _(dấu gạch dưới) - (dấu gạch nối). (dấu chấm)
#XMSG
firstUnderscorePattern=Tên đối tượng không được bắt đầu bằng dấu gạch dưới (_).

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: Màn hình SQL Create Table statement
#XMSG
sqlDialogMaxPKWarning=Trong Google BigQuery, tối đa 16 khóa chính được hỗ trợ và đối tượng nguồn có số lượng lớn hơn. Do đó, không có khóa chính nào được xác định trong câu lệnh này.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Một hoặc nhiều cột nguồn có kiểu dữ liệu không thể được xác định là khóa chính trong Google BigQuery. Do đó, không có khóa chính nào được xác định trong trường hợp này. Trong Google BigQuery, chỉ những kiểu dữ liệu sau mới có thể có khóa chính: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Sao chép và đóng
#XBUT
closeDDL=Đóng
#XMSG
copiedToClipboard=Đã sao chép vào clipboard


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Đối tượng ''{0}'' không thể là một phần của chuỗi tác vụ vì nó không có kết thúc (vì nó bao gồm các đối tượng có kiểu tải Ban đầu và Chênh lệch).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Đối tượng ''{0}'' không thể là một phần của chuỗi tác vụ vì nó không có kết thúc (vì nó bao gồm các đối tượng có kiểu tải Ban đầu và Chênh lệch).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Không thể thêm đối tượng "{0}" vào chuỗi tác vụ.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Có các đối tượng đích chưa được lưu. Vui lòng lưu lại.{0}{0} Hành vi của tính năng này đã thay đổi: Trước đây, các đối tượng đích chỉ được tạo trong môi trường đích khi luồng sao chép được triển khai.{0} Bây giờ các đối tượng đã được tạo khi luồng sao chép được lưu. Luồng sao chép của bạn đã được tạo trước thay đổi này và chứa các đối tượng mới.{0} Bạn cần lưu luồng sao chép một lần nữa trước khi triển khai để các đối tượng mới được bao gồm chính xác.
#XMSG
confirmChangeContentTypeMessage=Bạn sắp thay đổi loại nội dung. Nếu bạn làm vậy, tất cả các phép chiếu hiện có sẽ bị xóa.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Tên chủ thể
#XFLD
schemaDialogVersionName=Phiên bản biểu đồ
#XFLD
includeTechKey=Bao gồm khóa kỹ thuật
#XFLD
segementButtonFlat=Phẳng
#XFLD
segementButtonNested=Lồng nhau
#XMSG
subjectNamePlaceholder=Tìm kiếm tên chủ thể

#XMSG
@EmailNotificationSuccess=Đã lưu cấu hình thông báo qua email thời gian chạy.

#XFLD
@RuntimeEmailNotification=Thông báo qua email thời gian chạy

#XBTN
@TXT_SAVE=Lưu


