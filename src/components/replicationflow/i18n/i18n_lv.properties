#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Rep<PERSON><PERSON>šanas plūsma

#XFLD: Edit Schema button text
editSchema=Rediģēt shēmu

#XTIT : Properties heading
configSchema=Konfigurēt shēmu

#XFLD: save changed button text
applyChanges=Lietot izmaiņas


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Atlasīt avota savienojumu
#XFLD
sourceContainernEmptyText=Atlasīt konteineru
#XFLD
targetConnectionEmptyText=Atlasīt mērķa savienojumu
#XFLD
targetContainernEmptyText=Atlasīt konteineru
#XFLD
sourceSelectObjectText=Atlasīt avota objektu
#XFLD
sourceObjectCount=Avota objekti ({0})
#XFLD
targetObjectText=Mērķa objekti
#XFLD
confluentBrowseContext=Atlasīt kontekstu
#XBUT
@retry=Mēģināt vēlreiz
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Notiek nomnieka jaunin<PERSON>šana.

#XTOL
browseSourceConnection=Pārlūkot avota savienojumu
#XTOL
browseTargetConnection=Pārlūkot mērķa savienojumu
#XTOL
browseSourceContainer=Pārlūkot avota konteineru
#XTOL
browseAndAddSourceDataset=Pievienot avota objektus
#XTOL
browseTargetContainer=Pārlūkot mērķa konteineru
#XTOL
browseTargetSetting=Pārlūkot mērķa iestatījumus
#XTOL
browseSourceSetting=Pārlūkot avota iestatījumus
#XTOL
sourceDatasetInfo=Informācija
#XTOL
sourceDatasetRemove=Noņemt
#XTOL
mappingCount=Tas apzīmē tādu kartēšanu/izteiksmju kopējo skaitu, kas nav balstītas nosaukumā
#XTOL
filterCount=Šis apzīmē filtra nosacījumu kopējo skaitu.
#XTOL
loading=Notiek ielāde...
#XCOL
deltaCapture=Delta tveršana
#XCOL
deltaCaptureTableName=Delta tveršanas tabula
#XCOL
loadType=Ielādes tips
#XCOL
deleteAllBeforeLoading=Dzēst visus pirms ielādēšanas
#XCOL
transformationsTab=Projekcijas
#XCOL
settingsTab=Iestatījumi

#XBUT
renameTargetObjectBtn=Pārdēvēt mērķa objektu
#XBUT
mapToExistingTargetObjectBtn=Kartēt uz esošo mērķa objektu
#XBUT
changeContainerPathBtn=Mainīt konteinera ceļu
#XBUT
viewSQLDDLUpdated=Skatīt priekšrakstu SQL Izveidot tabulu
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Avota objekts neatbalsta delta tveršanu, taču atlasītajam mērķa objektam delta tveršana ir iespējota.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Mērķa objektu nevar izmantot, jo delta tveršana ir iespējota,{0}lai gan avota objekts neatbalsta delta tveršanu.{1}Varat atlasīt citu mērķa objektu, kas neatbalsta delta tveršanu.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Mērķa objekts ar šo nosaukumu jau pastāv. Tomēr to nevar izmantot,{0}jo delta tveršana ir iespējota, lai gan avota objekts neatbalsta{0}delta tveršanu.{1}Varat ievadīt vai nu tāda esoša mērķa objekta nosaukumu, kas{0}neatbalsta delta tveršanu, vai tādu nosaukumu, kas vēl nepastāv.
#XBUT
copySQLDDLUpdated=Kopēt priekšrakstu SQL Izveidot tabulu
#XMSG
targetObjExistingNoCDCColumnUpdated=Esošajām tabulām pakalpojumā Google BigQuery jāietver šādas kolonnas izmaiņu datu tveršanai (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Nav atbalstīti tālāk minētie avota objekti, jo tiem nav primārās atslēgas vai arī tie izmantot savienojumu, kas neatbilst nosacījumiem, lai izgūtu primāro atslēgu:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Iespējamo risinājumu, lūdzu, skatiet SAP KBA 3531135.
#XLST: load type list values
initial=Tikai sākotnējais
@emailUpdateError=Kļūda, atjauninot e-pasta paziņojumu sarakstu

#XLST
initialDelta=Sākotnējais un delta

#XLST
deltaOnly=Tikai delta
#XMSG
confluentDeltaLoadTypeInfo=Confluent Kafka avotam ir atbalstīts tikai ielādes tips Sākotnējs un Delta.
#XMSG
confirmRemoveReplicationObject=Vai apstiprināt, ka vēlaties izdzēst replicēšanu?
#XMSG
confirmRemoveReplicationTaskPrompt=Šī darbība izdzēsīs esošās replikācijas. Vai vēlaties turpināt?
#XMSG
confirmTargetConnectionChangePrompt=Šī darbība atiestatīs mērķa savienojumu, mērķa konteineru un izdzēsīs visus mērķa objektus. Vai vēlaties turpināt?
#XMSG
confirmTargetContainerChangePrompt=Šī darbība atiestatīs mērķa konteineru un izdzēsīs visus esošos mērķa objektus. Vai vēlaties turpināt?
#XMSG
confirmRemoveTransformObject=Vai apstiprināt, ka vēlaties dzēst projekciju {0}?
#XMSG
ErrorMsgContainerChange=Mainot konteinera ceļu, radās kļūda.
#XMSG
infoForUnsupportedDatasetNoKeys=Šādi avota objekti nav atbalstīti, jo tiem nav primārās atslēgas:
#XMSG
infoForUnsupportedDatasetView=Netiek atbalstīti šādi avota objekti ar tipu “Skati”:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Šāds avota projekts netiek atbalstīts, jo tas ir SQL skats, kurā ir ievades parametri:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Šādi avota objekti nav atbalstīti, jo tiem ir atspējota izvilkšana:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Confluent savienojumiem vienīgie atļautie serializēšanas formāti ir AVRO un JSON. Netiek atbalstīti šādi objekti, jo tie izmanto citu serializēšanas formātu:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Nevar ienest shēmu tālāk norādītajiem objektiem. Lūdzu, atlasiet piemēroto kontekstu vai pārbaudiet shēmu reģistra konfigurāciju
#XTOL: warning dialog header on deleting replication task
deleteHeader=Dzēst
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Iestatījums “Dzēst visus pirms ielādēšanas” netiek atbalstīts pakalpojumam Google BigQuery.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Iestatījums “Dzēst visus pirms” izdzēš un atkārtoti izveido objektu (tēmu) pirms katras replicēšanas. Tas izdzēš arī visus piešķirtos ziņojumus.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Iestatījums “Dzēst visus pirms” netiek atbalstīts šim mērķa tipam.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Tehniskais nosaukums
#XCOL
connBusinessName=Biznesa nosaukums
#XCOL
connDescriptionName=Apraksts
#XCOL
connType=Tips
#XMSG
connTblNoDataFoundtxt=Nav atrasts neviens savienojums
#XMSG
connectionError=Ienesot savienojumus, radās kļūda.
#XMSG
connectionCombinationUnsupportedErrorTitle=Savienojumu kombinācija netiek atbalstīta
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Replicēšana no {0} uz {1} pašlaik netiek atbalstīta.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Savienojuma tipu kombinācija nav atbalstīta
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Replicēšana no savienojuma ar savienojuma tipu SAP HANA Cloud, datu ezera faili uz vienumu {0} nav atbalstīta. Varat replicēt tikai uz SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Atlasīt
#XBUT
containerCancelBtn=Atcelt
#XTOL
containerSelectTooltip=Atlasīt
#XTOL
containerCancelTooltip=Atcelt
#XMSG
containerContainerPathPlcHold=Konteinera ceļš
#XFLD
containerContainertxt=Konteiners
#XFLD
confluentContainerContainertxt=Konteksts
#XMSG
infoMessageForSLTSelection=Tikai /SLT/Masveida pārnešanas ID ir atļauts kā konteiners. Sadaļā SLT atlasiet Masveida pārnešanas ID (ja pieejams) un noklikšķiniet uz Iesniegt.
#XMSG
msgFetchContainerFail=Ienesot konteinera datus, radās kļūda.
#XMSG
infoMessageForSLTHidden=Šis savienojums neatbalsta SLT mapes, tāpēc tās nav redzamas sarakstā zemāk.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Atlasiet konteineru, kurā ir apakšmapes.
#XMSG
sftpIncludeSubFolderText=False
#XMSG
sftpIncludeSubFolderTextNew=Nē

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Vēl nav filtra kartēšanas)
#XMSG
failToFetchRemoteMetadata=Ienesot metadatus, radās kļūda.
#XMSG
failToFetchData=Ienesot esošo mērķi, radās kļūda.
#XCOL
@loadType=Ielādes tips
#XCOL
@deleteAllBeforeLoading=Dzēst visus pirms ielādēšanas

#XMSG
@loading=Notiek ielāde...
#XFLD
@selectSourceObjects=Atlasīt avota objektus
#XMSG
@exceedLimit=Vienlaikus nevar importēt vairāk par {0} objektiem. Lūdzu, noņemiet atlasi vismaz {1} objektiem.
#XFLD
@objects=Objekti
#XBUT
@ok=Labi
#XBUT
@cancel=Atcelt
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Nākamais
#XBUT
btnAddSelection=Pievienot atlasi
#XTOL
@remoteFromSelection=Noņemt no atlases
#XMSG
@searchInForSearchField=Meklēt šeit: {0}

#XCOL
@name=Tehniskais nosaukums
#XCOL
@type=Tips
#XCOL
@location=Atrašanās vieta
#XCOL
@label=Biznesa nosaukums
#XCOL
@status=Statuss

#XFLD
@searchIn=Meklēt šeit:
#XBUT
@available=Pieejams
#XBUT
@selection=Atlase

#XFLD
@noSourceSubFolder=Tabulas un skati
#XMSG
@alreadyAdded=Jau ir diagrammā
#XMSG
@askForFilter=Ir vairāk nekā {0} pozīcijas. Lūdzu, ievadiet filtra virkni, lai samazinātu pozīciju skaitu.
#XFLD: success label
lblSuccess=Izdevās
#XFLD: ready label
lblReady=Gatavs
#XFLD: failure label
lblFailed=Neizdevās
#XFLD: fetching status label
lblFetchingDetail=Detalizētās informācijas ienešana

#XMSG Place holder text for tree filter control
filterPlaceHolder=Ierakstiet tekstu, lai filtrētu augstākā līmeņa objektus
#XMSG Place holder text for server search control
serverSearchPlaceholder=Ierakstiet un nospiediet taustiņu Enter, lai meklētu
#XMSG
@deployObjects={0} objektu importēšana...
#XMSG
@deployObjectsStatus=Objektu skaits, kas tika importēti: {0}. Objektu skaits, kurus nevarēja importēt: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Neizdevās atvērt lokālā repozitorija pārlūku.
#XMSG
@openRemoteSourceBrowserError=Neizdevās ienest avota objektus.
#XMSG
@openRemoteTargetBrowserError=Neizdevās ienest mērķa objektus.
#XMSG
@validatingTargetsError=Pārbaudot mērķus, radās kļūda.
#XMSG
@waitingToImport=Gatavs importēšanai

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Pārsniegts maksimālais objektu skaits. Atlasiet ne vairāk kā 500 objektu vienai replicēšanas plūsmai.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Tehniskais nosaukums
#XFLD
sourceObjectBusinessName=Biznesa nosaukums
#XFLD
sourceNoColumns=Kolonnu skaits
#XFLD
containerLbl=Konteiners

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Jums replicēšanas plūsmai ir jāatlasa avota savienojums.
#XMSG
validationSourceContainerNonExist=Jums avota savienojumam ir jāatlasa konteiners.
#XMSG
validationTargetNonExist=Jums replicēšanas plūsmai ir jāatlasa mērķa savienojums.
#XMSG
validationTargetContainerNonExist=Jums mērķa savienojumam ir jāatlasa konteiners.
#XMSG
validationTruncateDisabledForObjectTitle=Replicēšana uz objektu krātuvēm. 
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Replicēšana mākoņkrātuvē ir iespējama tikai tad, ja vai nu ir iestatīta iespēja “Dzēst visus pirms ielādēšanas”, vai ja mērķī nepastāv mērķa objekts.{0}{0} Lai joprojām iespējotu replicēšanu tiem objektiem, kuriem nav iestatīta iespēja “Dzēst visus pirms ielādēšanas”, nodrošiniet, lai pirms replicēšanas plūsmas izpildes sistēmā nepastāvētu mērķa objekts.
#XMSG
validationTaskNonExist=Jums replicēšanas plūsmā ir jābūt vismaz vienam replicēšanai.
#XMSG
validationTaskTargetMissing=Jums ir jābūt tās replicēšanas mērķim, kuras avots ir: {0}
#XMSG
validationTaskTargetIsSAC=Atlasītais mērķis ir SAC artefakts: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Atlasītais mērķis nav atbalstīta lokālā tabula: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Objekts ar šādu nosaukumu mērķī jau pastāv. Taču šo objektu nevar izmantot kā mērķa objektu replicēšanas plūsmai uz lokālo repozitoriju, jo tas nav lokāla tabula.
#XMSG
validateSourceTargetSystemDifference=Jums replicēšanas plūsmai ir jāatlasa atšķirīgas avota un mērķa savienojumu un konteinera kombinācijas.
#XMSG
validateDuplicateSources=vienam vai vairākiem replicēšanas gadījumiem ir dublēti avota objekta nosaukumi: {0}.
#XMSG
validateDuplicateTargets=vienam vai vairākiem replicēšanas gadījumiem ir dublēti mērķa objekta nosaukumi: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=Avota objekts {0} neatbalsta delta tveršanu, savukārt mērķa objekts {1} to atbalsta. Vienkārši noņemiet replikāciju.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Replicēšanai ar mērķa objekta nosaukumu {0} jums ir jāatlasa ielādes tips “Sākotnējais un delta”.
#XMSG
validationAutoRenameTarget=Mērķa kolonnas ir pārdēvētas.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Ir pievienota automātiska projekcija, un tālāk norādītās mērķa kolonnas ir pārdēvētas, lai ļautu replicēšanu uz mērķi:{1}{1} {0} {1}{1}Tas tiek darīts šādu iemeslu dēļ:{1}{1}{2} Neatbalstītas rakstzīmes{1}{2} Rezervēts prefikss
#XMSG
validationAutoRenameTargetDescriptionUpdated=Ir pievienota automātiskā projekcija un tālāk norādītās mērķa kolonnas ir pārdēvētas, lai atļautu replicēšanu uz pakalpojumu Google BigQuery:{1}{1} {0} {1}{1}Tas ir saistīts ar kādu no tālāk norādītajiem iemesliem: {1}{1}{2}Rezervēts kolonnas nosaukums{1}{2} Neatbalstītas rakstzīmes{1}{2} Rezervēts prefikss
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Ir pievienota automātiska projekcija, un tālāk norādītās mērķa kolonnas ir pārdēvētas, lai ļautu replicēšanu uz Confluent:{1}{1} {0} {1}{1}Tas tiek darīts šādu iemeslu dēļ:{1}{1}{2} Rezervēts kolonnas nosaukums{1}{2} Neatbalstītas rakstzīmes{1}{2} Rezervēts prefikss
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Ir pievienota automātiska projekcija, un tālāk norādītās mērķa kolonnas ir pārdēvētas, lai ļautu replicēšanu uz mērķi:{1}{1} {0} {1}{1}Tas tiek darīts šādu iemeslu dēļ:{1}{1}{2} Rezervēts kolonnas nosaukums{1}{2} Neatbalstītas rakstzīmes{1}{2} Rezervēts prefikss
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Mērķa objekts tika pārdēvēts. 
#XMSG
autoRenameInfoDesc=Mērķa objekts tika pārdēvēts, jo tajā neietilpa neviena atbalstītā rakstzīme. Ir atbalstītas tikai šādas rakstzīmes:{0}{0}{1}A–Z{0}{1}a–z{0}{1}0–9{0}{1}.(punkts){0}{1}_(pasvītrojums){0}{1}-(defise)
#XMSG
validationAutoTargetTypeConversion=Mērķa datu tipi ir mainīti.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Tālāk norādītajām mērķa kolonnām ir mainīti mērķa datu tipi, jo pakalpojumā Google BigQuery netiek atbalstīti avota datu tipi:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Tālāk norādītajām mērķa kolonnām ir mainīti mērķa datu tipi, jo šie avota datu tipi netiek atbalstīti mērķa savienojumā:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Saīsināt mērķa kolonnas nosaukumus.
#XMSG
validationMaxCharLengthGBQTargetDescription=Pakalpojumā Google BigQuery kolonnu nosaukumu garums nedrīkst pārsniegt 300 rakstzīmes. Izmantojiet projekciju, lai saīsinātu tālāk norādītos mērķa kolonnu nosaukumus:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Primārās atslēgas netiks izveidotas.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=Pakalpojumā Google BigQuery maksimālais atbalstīto primāro atslēgu skaits ir 16, taču avota objektam ir lielāks primāro atslēgu skaits. Neviena no primārajām atslēgām netiks izveidota mērķa objektā.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Vienai vai vairākām avota kolonnām ir datu tipi, kurus pakalpojumā Google BigQuery nevar definēt kā primārās atslēgas. Neviena no primārajām atslēgām netiks izveidota mērķa objektā. {0}{0}Šādi mērķa datu tipi ir saderīgi ar Google BigQuery datu tipiem, kuriem var definēt primāro atslēgu:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Definējiet vienu vai vairākas kolonnas kā primāro atslēgu.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Viena vai vairākas kolonnas ir jādefinē kā primārā atslēga; lai to izdarītu, izmantojiet avota shēmas dialogu.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Definējiet vienu vai vairākas kolonnas kā primāro atslēgu.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Kā primārā atslēga jums ir jādefinē viena vai vairākas kolonnas, kas atbilst primārās atslēgas ierobežojumiem jūsu avota objektam. Lai to izdarītu, sava avota objekta rekvizītos dodieties uz “Konfigurēt shēmu”.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Ievadiet derīgu maks. nodalījumu vērtību.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Maks. nodalījuma vērtībai ir jābūt ≥ 1 un ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Definējiet vienu vai vairākas kolonnas kā primāro atslēgu.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Lai replicētu objektu, jums viena vai vairākas mērķa kolonnas ir jādefinē kā primārā atslēga. Lai to paveiktu, izmantojiet projekciju.
#XMSG
validateHDLFNoPKExistingDatasetError=Definējiet vienu vai vairākas kolonnas kā primāro atslēgu.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Lai replicētu datus uz pastāvošu mērķa objektu, tajā ir jābūt vienai vai vairākām kolonnām, kas ir definētas kā primārā atslēga. {0} Lai vienu vai vairākas kolonnas definētu kā primāro atslēgu, varat izmantot šādas iespējas: {0} {1} Izmantojiet lokālo tabulu redaktoru, lai mainītu pastāvošo mērķa objektu. Pēc tam ielādējiet replicēšanas plūsmu vēlreiz.{0} {1} Pārdēvējiet mērķa objektu replicēšanas plūsmā. Tādējādi uzreiz pēc izpildes startēšanas tiek izveidots jauns objekts. Pēc pārdēvēšanas vienu vai vairākas kolonnas varat definēt kā primāro atslēgu kādā projekcijā.{0}{1} Kartējiet objektu uz citu pastāvošu mērķa objektu, kur viena vai vairākas kolonnas jau ir definētas kā primārā atslēga.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Atlasītais mērķis jau pastāv repozitorijā: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Delta tveršanas tabulu nosaukumus jau izmanto citas tabulas repozitorijā: {0}. Jums šie mērķa objekti ir jāpārdēvē, lai nodrošinātu, ka saistīto delta tveršanas tabulu nosaukumi ir unikāli, pirms varat saglabāt replicēšanas plūsmu.
#XMSG
validateConfluentEmptySchema=Definēt shēmu
#XMSG
validateConfluentEmptySchemaDescUpdated=Avota tabulai nav shēmas. Izvēlieties “Konfigurēt shēmu”, lai tādu definētu
#XMSG
validationCSVEncoding=Nederīgs CSV kodējums
#XMSG
validationCSVEncodingDescription=Uzdevuma CSV kodējums nav derīgs.
#XMSG
validateConfluentEmptySchema=Atlasiet saderīgu mērķa datu tipu
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Atlasiet saderīgu mērķa datu tipu
#XMSG
globalValidateTargetDataTypeDesc=Radās kļūda ar kolonnu kartējumiem. Dodieties uz “Projekcija” un nodrošiniet, ka visas avota kolonnas ir kartētas ar unikālu kolonnu, ar kolonnu, kurai ir saderīgs datu tips, un ka visas definētās izteiksmes ir derīgas.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Dublēti kolonnu nosaukumi.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Dublēti kolonnu nosaukumi netiek atbalstīti. Izmantojiet projekcijas dialogu, lai tos izlabotu. Dublēti kolonnu nosaukumi ir šādiem mērķa objektiem: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Dublēti kolonnu nosaukumi.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Dublēti kolonnu nosaukumi netiek atbalstīti. Dublēti kolonnu nosaukumi ir šādiem mērķa objektiem: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Datos varētu būt pretrunas.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Ielādes tips “Tikai delta” neņems vērā izmaiņas, kuras avotā ir veiktas starp pēdējo saglabāšanu un nākamo izpildi.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Mainiet ielādes tipu uz “Sākotnējais”.
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Replicēt objektus uz ABAP bāzes, kuriem nav primārās atslēgas, ir iespējams tikai ielādes tipam “Tikai sākotnējais”.
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Atspējojiet delta tveršanu.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Lai replicētu objektu, kuram nav primārās atslēgas, izmantojot avota savienojuma tipu ABAP, vispirms ir jāatspējo delta tveršana šai tabulai.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Mainiet mērķa objektu.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Mērķa objektu nevar izmantot, jo ir iespējota delta tveršana. Varat vai nu pārdēvēt mērķa objektu un pēc tam izslēgt delta tveršanu jaunajam (pārdēvētajam) objektam, vai kartēt avota objektu uz mērķa objektu, kuram delta tveršana ir atspējota.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Mainiet mērķa objektu.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Mērķa objektu nevar izmantot, jo tam nav obligātās tehniskās kolonnas __load_package_id. Šo mērķa objektu varat pārdēvēt, izmantojot nosaukumu, kāds vēl nepastāv. Pēc tam sistēma izveido jaunu objektu, kuram ir tāda pati definīcija kā avota objektam un kurā ir šī tehniskā kolonna. Vai arī mērķa objektu varat kartēt uz pastāvošu objektu, kuram ir šī obligātā tehniskā kolonna (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Mainiet mērķa objektu.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Mērķa objektu nevar izmantot, jo tam nav obligātās tehniskās kolonnas __load_record_id. Šo mērķa objektu varat pārdēvēt, izmantojot nosaukumu, kāds vēl nepastāv. Pēc tam sistēma izveido jaunu objektu, kuram ir tāda pati definīcija kā avota objektam un kurā ir šī tehniskā kolonna. Vai arī mērķa objektu varat kartēt uz pastāvošu objektu, kuram ir šī obligātā tehniskā kolonna (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Mainiet mērķa objektu.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Mērķa objektu nevar izmantot, jo tā tehniskās kolonnas __load_record_id datu tips nav "string(44)". Šo mērķa objektu varat pārdēvēt, izmantojot nosaukumu, kāds vēl nepastāv. Pēc tam sistēma izveido jaunu objektu, kuram ir tāda pati definīcija kā avota objektam un līdz ar to arī pareizais datu tips. Vai arī mērķa objektu varat kartēt uz pastāvošu objektu, kuram ir šī obligātā tehniskā kolonna (__load_record_id) ar pareizo datu tipu.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Mainiet mērķa objektu.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Mērķa objektu nevar izmantot, jo tam ir primārā atslēga, bet avota objektam tādas nav. Šo mērķa objektu varat pārdēvēt, izmantojot nosaukumu, kāds vēl nepastāv. Pēc tam sistēma izveido jaunu objektu, kuram ir tāda pati definīcija kā avota objektam un līdz ar to nav primārās atslēgas. Vai arī mērķa objektu varat kartēt uz pastāvošu objektu, kuram ir šī obligātā tehniskā kolonna (__load_package_id) un nav primārās atslēgas.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Mainiet mērķa objektu.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Mērķa objektu nevar izmantot, jo tam ir primārā atslēga, bet avota objektam tādas nav. Šo mērķa objektu varat pārdēvēt, izmantojot nosaukumu, kāds vēl nepastāv. Pēc tam sistēma izveido jaunu objektu, kuram ir tāda pati definīcija kā avota objektam un līdz ar to nav primārās atslēgas. Vai arī mērķa objektu varat kartēt uz pastāvošu objektu, kuram ir šī obligātā tehniskā kolonna (__load_record_id) un nav primārās atslēgas.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Mainiet mērķa objektu.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Mērķa objektu nevar izmantot, jo tā tehniskās kolonnas __load_package_id datu tips nav "binary(>=256)". Šo mērķa objektu varat pārdēvēt, izmantojot nosaukumu, kāds vēl nepastāv. Pēc tam sistēma izveido jaunu objektu, kuram ir tāda pati definīcija kā avota objektam un līdz ar to arī pareizais datu tips. Vai arī mērķa objektu varat kartēt uz pastāvošu objektu, kuram ir šī obligātā tehniskā kolonna (__load_package_id) ar pareizo datu tipu.
#XMSG
validationAutoRenameTargetDPID=Mērķa kolonnas ir pārdēvētas.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Noņemiet avota objektu.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Avota objektam nav atslēgas kolonnas, kas šajā kontekstā nav atbalstīta.
#XMSG
validationAutoRenameTargetDPIDDescription=Ir pievienota automātiskā projekcija un tālāk norādītās mērķa kolonnas ir pārdēvētas, lai atļautu replicēšanu no ABAP avota bez atslēgām:{1}{1} {0} {1}{1}Tas tiek darīts šādu iemeslu dēļ:{1}{1}{2} Rezervēts kolonnas nosaukums{1}{2} Neatbalstītas rakstzīmes{1}{2} Rezervēts prefikss
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Replicēšana uz {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Pašlaik nav iespējams saglabāt un izvietot replicēšanas plūsmas, kurām kā mērķis ir {0}, jo mēs veicam šīs funkcijas uzturēšanu.
#XMSG
TargetColumnSkippedLTF=Mērķa kolonna ir izlaista.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Mērķa kolonna ir izlaista neatbalstīta datu tipa dēļ. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Laika kolonna kā primārā atslēga.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Avota objektam ir laika kolonna kā primārā atslēga, bet tas netiek atbalstīts šajā kontekstā.
#XMSG
validateNoPKInLTFTarget=Trūkst primārās atslēgas.
#XMSG
validateNoPKInLTFTargetDescription=Mērķī nav definēta primārā atslēga, un šajā kontekstā tas netiek atbalstīts.
#XMSG
validateABAPClusterTableLTF=ABAP klasteru tabula.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Avota objekts ir ABAP klasteru tabula, kas šajā kontekstā nav atbalstīta.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Šķiet, jūs neesat vēl pievienojis nekādus datus.
#YINS
welcomeText2=Lai sāktu replicēšanas plūsmu, kreisajā pusē atlasiet savienojumu un avota objektu.

#XBUT
wizStep1=Atlasīt avota savienojumu
#XBUT
wizStep2=Atlasīt avota konteineru
#XBUT
wizStep3=Pievienot avota objektus

#XMSG
limitDataset=Sasniegts maksimālais objektu skaits. Noņemiet esošos objektus, lai pievienotu jaunus, vai izveidojiet jaunu replicēšanas plūsmu.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Replicēšanas plūsmu uz šo mērķa savienojumu, kas nav SAP mērķa savienojums, nevar startēt, jo šim mēnesim nav pieejams neviens izejošais apjoms.
#XMSG
premiumOutBoundRFAdminWarningMsg=Administrators var šim nomniekam palielināt premium izejošos blokus, padarot pieejamu izejošo apjomu šim mēnesim.
#XMSG
messageForToastForDPIDColumn2=Pievienota jauna kolonna mērķim {0} objektiem – tas ir nepieciešams darbam ar ierakstu dublikātiem saistībā ar avota objektiem uz ABAP bāzes, kuriem nav primārās atslēgas.
#XMSG
PremiumInboundWarningMessage=Atkarībā no replicēšanas plūsmu skaita un no replicējamā datu apjoma SAP HANA resursu daudzums, kas {0}nepieciešams datu replicēšanai, izmantojot {1}, var pārsniegt jūsu nomniekam pieejamo jaudu.
#XMSG
PremiumInboundWarningMsg=Atkarībā no replicēšanas plūsmu skaita un no replicējamā datu apjoma{0} SAP HANA resursu daudzums, kas nepieciešams datu replicēšanai, izmantojot "{1}", var pārsniegt jūsu nomniekam pieejamo jaudu.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Ievadiet projekcijas nosaukumu.
#XMSG
emptyTargetColumn=Ievadiet mērķa kolonnas nosaukumu.
#XMSG
emptyTargetColumnBusinessName=Ievadiet mērķa kolonnas biznesa nosaukumu.
#XMSG
invalidTransformName=Ievadiet projekcijas nosaukumu.
#XMSG
uniqueColumnName=Pārdēvējiet mērķa kolonnu.
#XMSG
copySourceColumnLbl=Kopēt kolonnas no avota objekta
#XMSG
renameWarning=Noteikti izvēlieties unikālu nosaukumu, kamēr pārdēvējat mērķa tabulu. Ja vietā jau pastāv tabula ar jauno nosaukumu, tā izmantos šīs tabulas definīciju.

#XMSG
uniqueColumnBusinessName=Pārdēvējiet mērķa kolonnas biznesa nosaukumu.
#XMSG
uniqueSourceMapping=Atlasiet citu avota kolonnu.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=Avota kolonnu {0} jau izmanto šādas mērķa kolonnas:{1}{1}{2}{1}{1} Šai mērķa kolonnai vai citām mērķa kolonnām atlasiet avota kolonnu, kura vēl netiek izmantota, lai saglabātu projekciju.
#XMSG
uniqueColumnNameDescription=Jūsu ievadītais mērķa kolonnas nosaukums jau pastāv. Lai šo projekciju varētu saglabāt, ievadiet unikālu kolonnas nosaukumu.
#XMSG
uniqueColumnBusinessNameDesc=Mērķa kolonnas biznesa nosaukums jau pastāv. Lai šo projekciju saglabātu, ir jāievada unikāls kolonnas biznesa nosaukums.
#XMSG
emptySource=Atlasiet avota kolonnu vai ievadiet konstanti.
#XMSG
emptySourceDescription=Lai izveidotu derīgu kartēšanas ierakstu, atlasiet avota kolonnu vai ievadiet konstantu vērtību.
#XMSG
emptyExpression=Definējiet kartēšanu.
#XMSG
emptyExpressionDescription1=Vai nu atlasiet avota kolonnu, uz kuru vēlaties kartēt mērķa kolonnu, vai atzīmējiet izvēles rūtiņu kolonnā {0} Funkcijas/konstantes {1}. {2} {2} Funkcijas tiek ievadītas automātiski atbilstoši mērķa datu tipam. Konstantas vērtības var ievadīt manuāli.
#XMSG
numberExpressionErr=Ievadiet numuru.
#XMSG
numberExpressionErrDescription=Jūs atlasījāt skaitlisku datu tipu. Tas nozīmē, ka varat ievadīt tikai skaitliskas vērtības, kā arī decimālzīmes, ja attiecas. Neizmantojiet vienpēdiņas.
#XMSG
invalidLength=Ievadiet derīgu garuma vērtību.
#XMSG
invalidLengthDescription=Datu tipa garumam ir jābūt vienādam ar vai lielākam par avota kolonnas garumu, un tas var būt no 1 līdz 5000.
#XMSG
invalidMappedLength=Ievadiet derīgu garuma vērtību.
#XMSG
invalidMappedLengthDescription=Datu tipa garumam ir jābūt vienādam ar vai lielākam par avota kolonnas {0} garumu, un tas var būt no 1 līdz 5000.
#XMSG
invalidPrecision=Ievadiet derīgu precizitātes vērtību.
#XMSG
invalidPrecisionDescription=Precizitāte definē ciparu kopskaitu. Skala definē ciparu skaitu aiz decimālzīmes un var būt no 0 līdz precizitātei.{0}{0} Piemēri: {0}{1} precizitāte 6, skala 2 atbilst tādiem skaitļiem kā 1234.56.{0}{1} Precizitāte 6, skala 6 atbilst tādiem skaitļiem kā 0,123546.{0} {0} Mērķim paredzētajai precizitātei un skalai ir jābūt saderīgai ar avota precizitāti un skalu, lai visi cipari no avota ietilptu mērķa laukā. Piemēram, ja jūsu avotā ir precizitāte 6 un skala 2 (un secīgi pirms decimālzīmes ir citi cipari, nevis 0), mērķī nevar būt precizitāte 6 un skala 6.
#XMSG
invalidPrimaryKey=Ievadiet vismaz vienu primāro atslēgu.
#XMSG
invalidPrimaryKeyDescription=Primārā atslēga šai shēmai nav definēta.
#XMSG
invalidMappedPrecision=Ievadiet derīgu precizitātes vērtību.
#XMSG
invalidMappedPrecisionDescription1=Precizitāte definē ciparu kopskaitu. Skala definē ciparu skaitu aiz decimālzīmes un var būt no 0 līdz precizitātei.{0}{0} Piemēri:{0}{1} precizitāte 6, skala 2 atbilst tādiem skaitļiem kā 1234.56.{0}{1} Precizitāte 6, skala 6 atbilst tādiem skaitļiem kā 0,123546.{0}{0}Datu tipa precizitātei ir jābūt vienādai ar vai lielākai par avota precizitāti ({2}).
#XMSG
invalidScale=Ievadiet derīgu skalas vērtību.
#XMSG
invalidScaleDescription=Precizitāte definē ciparu kopskaitu. Skala definē ciparu skaitu aiz decimālzīmes un var būt no 0 līdz precizitātei.{0}{0} Piemēri: {0}{1} precizitāte 6, skala 2 atbilst tādiem skaitļiem kā 1234.56.{0}{1} Precizitāte 6, skala 6 atbilst tādiem skaitļiem kā 0,123546.{0} {0} Mērķim paredzētajai precizitātei un skalai ir jābūt saderīgai ar avota precizitāti un skalu, lai visi cipari no avota ietilptu mērķa laukā. Piemēram, ja jūsu avotā ir precizitāte 6 un skala 2 (un secīgi pirms decimālzīmes ir citi cipari, nevis 0), mērķī nevar būt precizitāte 6 un skala 6.
#XMSG
invalidMappedScale=Ievadiet derīgu skalas vērtību.
#XMSG
invalidMappedScaleDescription1=Precizitāte definē ciparu kopskaitu. Skala definē ciparu skaitu aiz decimālzīmes un var būt no 0 līdz precizitātei.{0}{0} Piemēri:{0}{1} precizitāte 6, skala 2 atbilst tādiem skaitļiem kā 1234.56.{0}{1} Precizitāte 6, skala 6 atbilst tādiem skaitļiem kā 0,123546.{0}{0}Datu tipa skalai ir jābūt vienādai ar vai lielākai par avota skalu ({2}).
#XMSG
nonCompatibleDataType=Atlasiet saderīgu mērķa datu tipu.
#XMSG
nonCompatibleDataTypeDescription1=Šeit norādāmajam datu tipam ir jābūt saderīgam ar avota datu tipu ({0}). {1}{1} Piemērs: ja avota kolonnā ir datu tipa virkne ar burtiem, jūs nevarat mērķī izmantot decimālo datu tipu.
#XMSG
invalidColumnCount=Atlasiet avota kolonnu.
#XMSG
ObjectStoreInvalidScaleORPrecision=Ievadiet derīgu vērtību precizitātei un mērogam.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=Pirmā vērtība ir precizitāte, un tā definē ciparu kopējo skaitu. Otrā vērtība ir mērogs, un tā definē ciparu skaitu aiz komata. Ir jāievada tāda mērķa mēroga vērtība, kas ir lielāka par avota mēroga vērtību, un starpībai starp ievadītajām mērķa mēroga un precizitātes vērtībām ir jābūt lielākai par starpību starp avota mēroga un precizitātes vērtībām.
#XMSG
InvalidPrecisionORScale=Ievadiet derīgu vērtību precizitātei un mērogam.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=Pirmā vērtība ir precizitāte, kas nosaka ciparu kopējo skaitu. Otrā vērtība ir skala, kas nosaka ciparus aiz decimālzīmes.{0}{0}Ņemot vērā to, ka avota datu tips pakalpojumā Google BigQuery nav atbalstīts, tas tiek konvertēts uz mērķa datu tipu DECIMAL. Šajā gadījumā precizitāti var definēt tikai no 38 līdz 76 un skalu no 9 līdz 38. Turklāt “precizitāte mīnus skala” rezultātam, kas apzīmē skaitļus pirms decimālzīmes, ir jābūt no 29 līdz 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=Pirmā vērtība ir precizitāte, kas nosaka ciparu kopējo skaitu. Otrā vērtība ir skala, kas nosaka ciparus aiz decimālzīmes.{0}{0}Ņemot vērā to, ka avota datu tips pakalpojumā Google BigQuery nav atbalstīts, tas tiek konvertēts uz mērķa datu tipu DECIMAL. Šajā gadījumā precizitāte ir jādefinē kā 20 vai lielāku. Turklāt “precizitāte mīnus skala” rezultātam, kas apzīmē skaitļus pirms decimālzīmes, ir jābūt vienādam ar 20 vai lielākam.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=Pirmā vērtība ir precizitāte, kas definē ciparu kopējo skaitu. Otrā vērtība ir skala, kas definē ciparu skaitu aiz decimālā komata.{0}{0}Šis avota datu tips netiek atbalstīts mērķī, tāpēc tas tiek konvertēts uz mērķa datu tipu DECIMAL. Šādā gadījumā precizitāte ir jādefinē ar jebkādu skaitli, kas ir lielāks par vai vienāds ar 1 un mazāks par vai vienāds ar 38, un skala ir mazāka par vai vienāda ar precizitāti.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=Pirmā vērtība ir precizitāte, kas nosaka ciparu kopējo skaitu. Otrā vērtība ir skala, kas nosaka ciparus aiz decimālzīmes.{0}{0}Ņemot vērā to, ka avota datu tips nav atbalstīts mērķī, tas tiek konvertēts uz mērķa datu tipu DECIMAL. Šajā gadījumā precizitāte ir jādefinē kā 20 vai lielāku. Turklāt “precizitāte mīnus skala” rezultātam, kas apzīmē skaitļus pirms decimālzīmes, ir jābūt vienādam ar 20 vai lielākam.
#XMSG
invalidColumnCountDescription=Lai izveidotu derīgu kartēšanas ierakstu, atlasiet avota kolonnu vai ievadiet konstantu vērtību.
#XMSG
duplicateColumns=Pārdēvējiet mērķa kolonnu.
#XMSG
duplicateGBQCDCColumnsDesc=Mērķa kolonnas nosaukums ir rezervēts vienumā Google BigQuery. Pārdēvējiet to, lai varētu saglabāt projekciju.
#XMSG
duplicateConfluentCDCColumnsDesc=Mērķa kolonnas nosaukums ir rezervēts vienumā Confluent. Pārdēvējiet to, lai varētu saglabāt projekciju.
#XMSG
duplicateSignavioCDCColumnsDesc=Mērķa kolonnas nosaukums risinājumā SAP Signavio ir rezervēts. Lai varētu saglabāt projekciju, jums šis nosaukums ir jāpārdēvē.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Mērķa kolonnas nosaukums ir rezervēts produktā MS OneLake. Pārdēvējiet to, lai varētu saglabāt projekciju.
#XMSG
duplicateSFTPCDCColumnsDesc=Mērķa kolonnas nosaukums ir rezervēts produktā SFTP. Pārdēvējiet to, lai varētu saglabāt projekciju.
#XMSG
GBQTargetNameWithPrefixUpdated1=Mērķa kolonnas nosaukumā ir prefikss, kas ir rezervēts vienumā Google BigQuery. Pārdēvējiet to, lai varētu saglabāt projekciju. {0}{0}Mērķa kolonnas nosaukums nevar sākties ar nevienu no šīm virknēm: {0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Saīsiniet mērķa kolonnas nosaukumu.
#XMSG
GBQtargetMaxLengthDesc=Vienumā Google BigQuery kolonnas nosaukumā var izmantot maks. 300 rakstzīmes. Saīsiniet mērķa kolonnas nosaukumu, lai varētu saglabāt projekciju.
#XMSG
invalidMappedScalePrecision=Mērķim paredzētajai precizitātei un skalai ir jābūt saderīgai ar avotu, lai visi cipari no avota ietilptu mērķa laukā.
#XMSG
invalidMappedScalePrecisionShortText=Ievadiet derīgu precizitāti un skalas vērtību.
#XMSG
validationIncompatiblePKTypeDescProjection3=Vienai vai vairākām avota kolonnām ir datu tipi, kurus pakalpojumā Google BigQuery nevar definēt kā primārās atslēgas. Neviena no primārajām atslēgām netiks izveidota mērķa objektā. {0}{0}Šādi mērķa datu tipi ir saderīgi ar Google BigQuery datu tipiem, kuriem var definēt primāro atslēgu:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Noņemiet atzīmi kolonnai __message_id.
#XMSG
uncheckColumnMessageIdDesc=Kolonna: Primārā atslēga
#XMSG
validationOpCodeInsert=Jums ir jāievada vērtība darbībai Ievietot.
#XMSG
recommendDifferentPrimaryKey=Iesakām jums atlasīt citu primāro atslēgu pozīcijas līmenī.
#XMSG
recommendDifferentPrimaryKeyDesc=Kad operācijas kods jau ir definēts, masīva indeksam un pozīcijām ir ieteicams atlasīt atšķirīgas primārās atslēgas, lai izvairītos no tādām problēmām kā, piemēram, kolonnu dublēšana.
#XMSG
selectPrimaryKeyItemLevel=Jums ir jāatlasa vismaz viena primārā atslēga gan galvenes, gan pozīcijas līmenī.
#XMSG
selectPrimaryKeyItemLevelDesc=Kad masīvs vai karte ir izvērsts, jums ir jāatlasa divas primārās atslēgas, viena – galvenes līmenī un viena – pozīcijas līmenī.
#XMSG
invalidMapKey=Jums ir jāatlasa vismaz viena primārā atslēga galvenes līmenī.
#XMSG
invalidMapKeyDesc=Kad masīvs vai karte ir izvērsts, jums ir jāatlasa primārā atslēga galvenes līmenī.
#XFLD
txtSearchFields=Meklēt mērķa kolonnās
#XFLD
txtName=Nosaukums
#XMSG
txtSourceColValidation=Viena vai vairākas avota kolonnas nav atbalstītas:
#XMSG
txtMappingCount=Kartējumi ({0})
#XMSG
schema=Shēma
#XMSG
sourceColumn=Avota kolonnas
#XMSG
warningSourceSchema=Jebkādas shēmā veiktās izmaiņas ietekmē kartējumus projekcijas dialogā.
#XCOL
txtTargetColName=Mērķa kolonna (tehniskais nosaukums)
#XCOL
txtDataType=Mērķa datu tips
#XCOL
txtSourceDataType=Avota datu tips
#XCOL
srcColName=Avota kolonna (tehniskais nosaukums)
#XCOL
precision=Precizitāte
#XCOL
scale=Mērogs
#XCOL
functionsOrConstants=Funkcijas/konstantes
#XCOL
txtTargetColBusinessName=Mērķa kolonna (biznesa nosaukums)
#XCOL
prKey=Primārā atslēga
#XCOL
txtProperties=Rekvizīti
#XBUT
txtOK=Saglabāt
#XBUT
txtCancel=Atcelt
#XBUT
txtRemove=Noņemt
#XFLD
txtDesc=Apraksts
#XMSG
rftdMapping=Kartēšana
#XFLD
@lblColumnDataType=Datu tips
#XFLD
@lblColumnTechnicalName=Tehniskais nosaukums
#XBUT
txtAutomap=Automātiskā kartēšana
#XBUT
txtUp=Uz augšu
#XBUT
txtDown=Uz leju

#XTOL
txtTransformationHeader=Projekcija
#XTOL
editTransformation=Rediģēt
#XTOL
primaryKeyToolip=Atslēga


#XMSG
rftdFilter=Filtrēt
#XMSG
rftdFilterColumnCount=Avots: {0}({1})
#XTOL
rftdFilterColSearch=Meklēt
#XMSG
rftdFilterColNoData=Nav parādāmu kolonnu
#XMSG
rftdFilteredColNoExps=Nav filtra izteiksmes
#XMSG
rftdFilterSelectedColTxt=Pievienot filtru šim:
#XMSG
rftdFilterTxt=Filtrs pieejams šim:
#XBUT
rftdFilterSelectedAddColExp=Pievienot izteiksmi
#YINS
rftdFilterNoSelectedCol=Atlasiet kolonnu, lai pievienotu filtru.
#XMSG
rftdFilterExp=Filtrēšanas izteiksme
#XMSG
rftdFilterNotAllowedColumn=Šai kolonnai nav atbalstīta filtru pievienošana.
#XMSG
rftdFilterNotAllowedHead=Neatbalstīta kolonna
#XMSG
rftdFilterNoExp=Nav definēts neviens filtrs
#XTOL
rftdfilteredTt=Filtrēts
#XTOL
rftdremoveexpTt=Noņemt filtra izteiksmi
#XTOL
validationMessageTt=Pārbaudes ziņojumi
#XTOL
rftdFilterDateInp=Atlasīt datumu
#XTOL
rftdFilterDateTimeInp=Atlasīt datumu/laiku
#XTOL
rftdFilterTimeInp=Atlasīt laiku
#XTOL
rftdFilterInp=Ievadīt vērtību
#XMSG
rftdFilterValidateEmptyMsg={0} filtra izteiksmes ir tukšas {1} kolonnās
#XMSG
rftdFilterValidateInvalidNumericMsg={0} filtra izteiksmēm {1} kolonnā ir nederīgas skaitliskās vērtības
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Filtra izteiksmē ir jāietilpst derīgām skaitliskajām vērtībām
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Ja mērķa objekta shēma ir mainījusies, izmantojiet funkciju “Kartēt uz esošu mērķa objektu” galvenajā lapā, lai šīs izmaiņas pielāgotu, un mērķa objektu atkal pārkartējiet ar tā avotu.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Ja mērķa tabula jau pastāv un kartējumā ir iekļautas shēmas izmaiņas, mērķa tabula ir atbilstoši jāmaina pirms replicēšanas plūsmas izvietošanas.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Ja jūsu kartējumā ir shēmas izmaiņas, mērķa tabula ir atbilstoši jāmaina pirms replicēšanas plūsmas izvietošanas.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Šādas neatbalstītas kolonnas tika izlaistas no avota definīcijas: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Šādas neatbalstītas kolonnas tika izlaistas no mērķa definīcijas: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Tālāk norādītie objekti netiek atbalstīti, jo tie ir padarīti pieejami patēriņam: {0} {1} {0} {0} Lai tabulas izmantotu replicēšanas plūsmā, semantisko lietojumu (tabulu iestatījumos) nedrīkst iestatīt uz {2}Analītiskā datu kopa{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Mērķa objektu nevar lietot, jo tas ir padarīts pieejams patēriņam. {0} {0}  Lai tabulu izmantotu replicēšanas plūsmā, semantisko lietojumu (tabulas iestatījumos) nedrīkst iestatīt uz {1}Analītiskā datu kopa{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Mērķa objekts ar šādu nosaukumu jau pastāv. Taču to nevar lietot, jo tas ir padarīts pieejams patēriņam. {0} {0} Lai tabulu izmantotu replicēšanas plūsmā, semantisko lietojumu (tabulas iestatījumos) nedrīkst iestatīt uz {1}Analītiskā datu kopa{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Objekts ar šādu nosaukumu mērķī jau pastāv. {0}Taču šo objektu nevar izmantot kā mērķa objektu replicēšanas plūsmai uz lokālo repozitoriju, jo tā nav lokāla tabula.
#XMSG:
targetAutoRenameUpdated=Mērķa kolonna ir pārdēvēta.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Mērķa kolonna tika pārdēvēta, lai vienumā Google BigQuery atļautu replicēšanas. Tam pamatā ir viens no šādiem iemesliem: {0} {1}{2}Rezervēts kolonnas nosaukums{3}{2}Neatbalstītas rakstzīmes{3}{2}Rezervēts prefikss{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Mērķa kolonna ir pārdēvēta, lai atļautu replicēšanas vienumā Confluent. Tas tiek darīts šādu iemeslu dēļ:{0} {1}{2}Rezervēts kolonnas nosaukums{3}{2}Neatbalstītas rakstzīmes{3}{2}Rezervēts prefikss{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Mērķa kolonna ir pārdēvēta, lai atļautu replicēšanu uz mērķi. Tas tiek darīts šādu iemeslu dēļ: {0} {1}{2}Neatbalstītas rakstzīmes{3}{2}Rezervēts prefikss{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Mērķa kolonna ir pārdēvēta, lai atļautu replicēšanu uz mērķi. Tas tiek darīts šādu iemeslu dēļ:{0} {1}{2}Rezervēts kolonnas nosaukums{3}{2}Neatbalstītas rakstzīmes{3}{2}Rezervēts prefikss{3}{4}
#XMSG:
targetAutoDataType=Mērķa datu tips tika mainīts.
#XMSG:
targetAutoDataTypeDesc=Mērķa datu tips tika mainīts uz {0}, jo vienumā Google BigQuery nav atbalstīts avota datu tips.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Mērķa datu tips ir mainīts uz {0}, jo risinājumā mērķa savienojumā šis avota datu tips netiek atbalstīts.
#XMSG
projectionGBQUnableToCreateKey=Primārās atslēgas netiks izveidotas.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=Pakalpojumā Google BigQuery maksimālais atbalstīto primāro atslēgu skaits ir 16, taču avota objektam ir lielāks primāro atslēgu skaits. Neviena no primārajām atslēgām netiks izveidota mērķa objektā.
#XMSG
HDLFNoKeyError=Definējiet vienu vai vairākas kolonnas kā primāro atslēgu.
#XMSG
HDLFNoKeyErrorDescription=Lai replicētu objektu, definējiet vienu vai vairākas mērķa kolonnas kā primāro atslēgu.
#XMSG
HDLFNoKeyErrorExistingTarget=Definējiet vienu vai vairākas kolonnas kā primāro atslēgu.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Lai replicētu datus uz pastāvošu mērķa objektu, tajā ir jābūt vienai vai vairākām kolonnām, kas ir definētas kā primārā atslēga. {0} {0} Lai vienu vai vairākas kolonnas definētu kā primāro atslēgu, varat izmantot šādas iespējas: {0}{0}{1} Izmantojiet lokālo tabulu redaktoru, lai mainītu pastāvošo mērķa objektu. Pēc tam ielādējiet replicēšanas plūsmu vēlreiz.{0}{0}{1} Pārdēvējiet mērķa objektu replicēšanas plūsmā. Tādējādi uzreiz pēc izpildes startēšanas tiek izveidots jauns objekts. Pēc pārdēvēšanas vienu vai vairākas kolonnas varat definēt kā primāro atslēgu kādā projekcijā.{0}{0}{1} Kartējiet objektu uz citu pastāvošu mērķa objektu, kur viena vai vairākas kolonnas jau ir definētas kā primārā atslēga.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Primārā atslēga mainīta.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=Salīdzinot ar avota objektu, kā primārā atslēga mērķa objektam jums ir definētas atšķirīgas kolonnas. Šīm kolonnām noteikti ir unikāli jāidentificē visas rindas, lai izvairītos no iespējas sabojāt datus, kad šos datus vēlāk replicējat. {0} {0} Avota objektā kā primārā atslēga ir definētas šādas kolonnas: {0} {1}
#XMSG
duplicateDPIDColumns=Pārdēvējiet mērķa kolonnu.
#XMSG
duplicateDPIDDColumnsDesc1=Šis mērķa kolonnas nosaukums ir rezervēts tehniskai kolonnai. Ievadiet citādu nosaukumu, lai saglabātu šo projekciju.
#XMSG:
targetAutoRenameDPID=Mērķa kolonna ir pārdēvēta.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Mērķa kolonna ir pārdēvēta, lai atļautu replicēšanas no ABAP avota bez atslēgām. Tas tiek darīts šādu iemeslu dēļ:{0} {1}{2}Rezervēts kolonnas nosaukums{3}{2}Neatbalstītas rakstzīmes{3}{2}Rezervēts prefikss{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle={0} Mērķa iestatījumi
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle={0} Avota iestatījumi
#XBUT
connectionSettingSave=Saglabāt
#XBUT
connectionSettingCancel=Atcelt
#XBUT: Button to keep the object level settings
txtKeep=Paturēt
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Pārrakstīt
#XFLD
targetConnectionThreadlimit=Mērķa pavedienu limits sākotnējai ielādei (1–100)
#XFLD
connectionThreadLimit=Avota pavedienu limits sākotnējai ielādei (1–100)
#XFLD
maxConnection=Replicēšanas pavedienu limits (1–100)
#XFLD
kafkaNumberOfPartitions=Nodalījumu skaits
#XFLD
kafkaReplicationFactor=Replicēšanas koeficients
#XFLD
kafkaMessageEncoder=Ziņojumu kodētājs
#XFLD
kafkaMessageCompression=Ziņojuma saspiešana
#XFLD
fileGroupDeltaFilesBy=Grupēt deltu pēc
#XFLD
fileFormat=Faila tips
#XFLD
csvEncoding=CSV kodējums
#XFLD
abapExitLbl=ABAP Exit
#XFLD
deltaPartition=Objekta pavedienu skaits delta ielādēm (1–10)
#XFLD
clamping_Data=Neizdošanās, veicot datu noīsināšanu
#XFLD
fail_On_Incompatible=Neizdošanās nesaderīgu datu dēļ
#XFLD
maxPartitionInput=Maks. nodalījumu skaits
#XFLD
max_Partition=Definēt maks. nodalījumu skaitu
#XFLD
include_SubFolder=Iekļaut apakšmapes
#XFLD
fileGlobalPattern=Globāls modelis faila nosaukumam
#XFLD
fileCompression=Faila saspiešana
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Faila norobežotājs
#XFLD
fileIsHeaderIncluded=Faila galvene
#XFLD
fileOrient=Orientētājs
#XFLD
gbqWriteMode=Rakstīšanas režīms
#XFLD
suppressDuplicate=Apspiest dublikātus
#XFLD
apacheSpark=Iespējot Apache Spark saderību
#XFLD
clampingDatatypeCb=Aptvert decimālzīmju peldošo punktu datu tipus
#XFLD
overwriteDatasetSetting=Pārrakstīt mērķa iestatījumus objekta līmenī
#XFLD
overwriteSourceDatasetSetting=Pārrakstīt avota iestatījumus objekta līmenī
#XMSG
kafkaInvalidConnectionSetting=Ievadiet skaitli no {0} līdz {1}.
#XMSG
MinReplicationThreadErrorMsg=Ievadiet skaitli, kas ir lielāks par {0}.
#XMSG
MaxReplicationThreadErrorMsg=Ievadiet skaitli, kas ir mazāks par {0}.
#XMSG
DeltaThreadErrorMsg=Ievadiet vērtību no 1 līdz 10.
#XMSG
MaxPartitionErrorMsg=Ievadiet vērtību diapazonā 1 <= x <= 2147483647. Noklusējuma vērtība ir 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Ievadiet veselu skaitli no {0} līdz {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Izmantot starpnieka replicēšanas koeficientu
#XFLD
serializationFormat=Serializēšanas formāts
#XFLD
compressionType=Saspiešanas veids
#XFLD
schemaRegistry=Izmantot shēmas reģistru
#XFLD
subjectNameStrat=Temata nosaukuma stratēģija
#XFLD
compatibilityType=Saderības veids
#XFLD
confluentTopicName=Tēmas nosaukums
#XFLD
confluentRecordName=Ieraksta nosaukums
#XFLD
confluentSubjectNamePreview=Temata nosaukuma priekšskatījums
#XMSG
serializationChangeToastMsgUpdated2=Serializēšanas formāts mainīts uz JSON, jo shēmu reģistrs nav iespējots. Lai serializēšanas formātu mainītu atpakaļ uz AVRO, jums vispirms ir jāiespējo shēmu reģistrs.
#XBUT
confluentTopicNameInfo=Tēmas nosaukuma pamatā vienmēr ir mērķa objekta nosaukums. Lai to mainītu, pārdēvējiet mērķa objektu.
#XMSG
emptyRecordNameValidationHeaderMsg=Ievadiet ieraksta nosaukumu.
#XMSG
emptyPartionHeader=Ievadiet nodalījumu skaitu.
#XMSG
invalidPartitionsHeader=Ievadiet derīgu nodalījumu skaitu.
#XMSG
invalidpartitionsDesc=Ievadiet skaitli no 1 līdz 200 000.
#XMSG
emptyrFactorHeader=Ievadiet replicēšanas koeficientu.
#XMSG
invalidrFactorHeader=Ievadiet derīgu replicēšanas koeficientu.
#XMSG
invalidrFactorDesc=Ievadiet skaitli no 1 līdz 32 767.
#XMSG
emptyRecordNameValidationDescMsg=Ja tiek izmantots serializēšanas formāts “AVRO”, ir atbalstītas tikai šādas rakstzīmes:{0}{1} A–Z{0}{1} a–z{0}{1} 0-9{0}{1} _(pasvītrojums)
#XMSG
validRecordNameValidationHeaderMsg=Ievadiet derīgu ieraksta nosaukumu.
#XMSG
validRecordNameValidationDescMsgUpdated=Tā kā tiek izmantots serializēšanas formāts “AVRO”, ieraksta nosaukumā drīkst būt tikai burti un cipari (A–Z, a–z, 0–9) un pasvītras (_) rakstzīmes. Tam ir jāsākas ar burtu vai pasvītru.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=Elementu “Objekta pavedienu skaits delta ielādēm” var iestatīt, līdzko vienam vai vairākiem objektiem ir ielādes tips “Sākotnējais un delta”.
#XMSG
invalidTargetName=Nederīgs kolonnas nosaukums
#XMSG
invalidTargetNameDesc=Mērķa kolonnas nosaukumā drīkst būt tikai burti un cipari (A–Z, a–z, 0–9) un pasvītras (_) rakstzīmes.
#XFLD
consumeOtherSchema=Patērēt citas shēmas versijas
#XFLD
ignoreSchemamissmatch=Ignorēt shēmu neatbilstību
#XFLD
confleuntDatatruncation=Neizdošanās, veicot datu noīsināšanu
#XFLD
isolationLevel=Izolācijas līmenis
#XFLD
confluentOffset=Sākuma punkts
#XFLD
signavioGroupDeltaFilesByText=Nav
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=Nē
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=Nē

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Projekcijas
#XBUT
txtAdd=Pievienot
#XBUT
txtEdit=Rediģēt
#XMSG
transformationText=Pievienot projekciju iestatīšanas filtram vai kartēšanai.
#XMSG
primaryKeyRequiredText=Atlasiet primāro atslēgu ar “Konfigurēt shēmu”.
#XFLD
lblSettings=Iestatījumi
#XFLD
lblTargetSetting={0}: mērķa iestatījumi
#XMSG
@csvRF=Atlasiet failu, kur ir tā shēmas definīcija, kuru vēlaties lietot visiem mapē esošajiem failiem.
#XFLD
lblSourceColumns=Avota kolonnas
#XFLD
lblJsonStructure=JSON struktūra
#XFLD
lblSourceSetting={0}: avota iestatījumi
#XFLD
lblSourceSchemaSetting={0}: avota shēmas iestatījumi
#XBUT
messageSettings=Ziņojuma iestatījumi
#XFLD
lblPropertyTitle1=Objekta rekvizīti
#XFLD
lblRFPropertyTitle=Replicēšanas plūsmas rekvizīti
#XMSG
noDataTxt=Nav kolonnu, ko parādīt.
#XMSG
noTargetObjectText=Nav atlasīts neviens mērķa objekts.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Mērķa kolonnas
#XMSG
searchColumns=Meklēt kolonnas
#XTOL
cdcColumnTooltip=Kolonna delta tveršanai
#XMSG
sourceNonDeltaSupportErrorUpdated=Šis avota objekts neatbalsta delta tveršanu.
#XMSG
targetCDCColumnAdded=Delta tveršanai tika pievienotas 2 mērķa kolonnas.
#XMSG
deltaPartitionEnable=Objekta pavedienu limits delta ielādēm pievienots avota iestatījumiem.
#XMSG
attributeMappingRemovalTxt=Tiek noņemti nederīgi kartējumi, kuri netiek atbalstīti jaunajam mērķa objektam.
#XMSG
targetCDCColumnRemoved=2 mērķa kolonnas, kas tika izmantotas delta tveršanai, tika noņemtas.
#XMSG
replicationLoadTypeChanged=Ielādes tips mainīts uz “Sākotnējais un delta”.
#XMSG
sourceHDLFLoadTypeError=Mainiet ielādes tipu uz “Sākotnējais un Delta”.
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Lai replicētu objektu no avota savienojuma ar savienojuma tipu SAP HANA Cloud, datu ezera faili uz SAP Datasphere, ir jāizmanto ielādes tips “sākotnējais un delta”.
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Iespējojiet Delta tveršanu.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Lai replicētu objektu no avota savienojuma ar savienojuma tipu SAP HANA Cloud, datu ezera faili uz SAP Datasphere, ir jāiespējo delta tveršana.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Mainiet mērķa objektu.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Mērķa objektu nevar izmantot, jo ir atspējota delta tveršana. Varat vai nu pārdēvēt mērķa objektu (kas ļauj izveidot jaunu objektu ar delta tveršanu), vai arī to kartēt uz esošu objektu ar iespējotu delta tveršanu.
#XMSG
deltaPartitionError=Ievadiet derīgu objekta pavedienu skaitu delta ielādēm.
#XMSG
deltaPartitionErrorDescription=Ievadiet vērtību no 1 līdz 10.
#XMSG
deltaPartitionEmptyError=Ievadiet objekta pavedienu skaitu delta ielādēm.
#XFLD
@lblColumnDescription=Apraksts
#XMSG
@lblColumnDescriptionText1=Tehniskiem nolūkiem – darbam ar ierakstu dublikātiem, kurus izraisa problēmas tādu avota objektu uz ABAP bāzes replicēšanas laikā, kuriem nav primārās atslēgas.
#XFLD
storageType=Krātuve
#XFLD
skipUnmappedColLbl=Izlaist nekartētās kolonnas
#XFLD
abapContentTypeLbl=Satura tips
#XFLD
autoMergeForTargetLbl=Sapludināt datus automātiski
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Vispārīgi
#XFLD
lblBusinessName=Biznesa nosaukums
#XFLD
lblTechnicalName=Tehniskais nosaukums
#XFLD
lblPackage=Pakotne
#XFLD
statusPanel=Izpildes statuss
#XBTN: Schedule dropdown menu
SCHEDULE=Grafiks
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Rediģēt grafiku
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Dzēst grafiku
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Izveidot grafiku
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Neizdevās ieplānot validēšanas pārbaudi
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Grafiku nevar izveidot, jo replicēšanas plūsma pašlaik tiek izvietota.{0}Lūdzu, uzgaidiet, līdz replicēšanas plūsma ir izvietota.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Replicēšanas plūsmām, kurās ir objekti ar ielādes tipu "Sākotnējais un Delta", grafiku izveidot nevar.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Replicēšanas plūsmām, kurās ir objekti ar ielādes tipu “Sākotnējais un delta/Tikai delta”, grafiku izveidot nevar.
#XFLD : Scheduled popover
SCHEDULED=Ieplānots
#XFLD
CREATE_REPLICATION_TEXT=Izveidot replicēšanas plūsmu
#XFLD
EDIT_REPLICATION_TEXT=Rediģēt replicēšanas plūsmu
#XFLD
DELETE_REPLICATION_TEXT=Dzēst replicēšanas plūsmu
#XFLD
REFRESH_FREQUENCY=Biežums
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Replicēšanas plūsmu nevar izvietot, jo pastāvošais grafiks{0} vēl neatbalsta ielādes tipu “Sākotnējais un Delta”.{0}{0}Lai izvietotu šo replicēšanas plūsmu, jums visu objektu{0} ielādes tipi ir jāiestata uz “Tikai sākotnējais”. Vai arī varat dzēst šo grafiku, izvietot {0}replicēšanas plūsmu un pēc tam sākt jaunu izpildi. Rezultātā tiks iegūta izpilde bez {0}beigām, kas arī atbalsta objektus ar ielādes tipu “Sākotnējs un Delta”.
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Replicēšanas plūsmu nevar izvietot, jo pastāvošais grafiks{0} vēl neatbalsta ielādes tipu “Sākotnējais un delta/Tikai delta”.{0}{0}Lai izvietotu šo replicēšanas plūsmu, jums visu objektu{0} ielādes tipi ir jāiestata uz “Tikai sākotnējais”. Vai arī varat dzēst šo grafiku, izvietot {0}replicēšanas plūsmu un pēc tam sākt jaunu izpildi. Rezultātā tiks iegūta izpilde bez {0}beigām, kas arī atbalsta objektus ar ielādes tipu “Sākotnējs un delta/Tikai delta”.
#XMSG
SCHEDULE_EXCEPTION=Neizdevās iegūt grafika informāciju
#XFLD: Label for frequency column
everyLabel=Ik pēc
#XFLD: Plural Recurrence text for Hour
hoursLabel=Stundām
#XFLD: Plural Recurrence text for Day
daysLabel=Dienām
#XFLD: Plural Recurrence text for Month
monthsLabel=Mēnešiem
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minūtēm
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Neizdevās iegūt informāciju par grafika iespējamību.
#XFLD :Paused field
PAUSED=Pauzēts
#XMSG
navToMonitoring=Atvērt plūsmu pārraugā
#XFLD
statusLbl=Statuss
#XFLD
lblLastRunExecuted=Pēdējās izpildes sākums
#XFLD
lblLastExecuted=Pēdējā izpilde
#XFLD: Status text for Completed
statusCompleted=Pabeigts
#XFLD: Status text for Running
statusRunning=Tiek izpildīts
#XFLD: Status text for Failed
statusFailed=Neizdevās
#XFLD: Status text for Stopped
statusStopped=Apturēts
#XFLD: Status text for Stopping
statusStopping=Tiek apturēts
#XFLD: Status text for Active
statusActive=Aktīvs
#XFLD: Status text for Paused
statusPaused=Pauzēts
#XFLD: Status text for not executed
lblNotExecuted=Vēl nav izpildīts
#XFLD
messagesSettings=Ziņojumu iestatījumi
#XTOL
@validateModel=Pārbaudes ziņojumi
#XTOL
@hierarchy=Hierarhija
#XTOL
@columnCount=Kolonnu skaits
#XMSG
VAL_PACKAGE_CHANGED=Jūs esat piešķīris šo objektu pakotnei “{1}”. Noklikšķiniet uz “Saglabāt”, lai apstiprinātu un pārbaudītu šīs izmaiņas. Ņemiet vērā, ka šajā redaktorā pēc saglabāšanas nevarēsit atsaukt objektu piešķiri pakotnei.
#XMSG
MISSING_DEPENDENCY=Objekta "{0}” atkarības nevar atrisināt pakotnē “{1}”.
#XFLD
deltaLoadInterval=Delta ielādes intervāls
#XFLD
lblHour=Stundas (0–24)
#XFLD
lblMinutes=Minūtes (0–59)
#XMSG
maxHourOrMinErr=Ievadiet vērtību no 0 līdz {0}
#XMSG
maxDeltaInterval=Delta ielādes intervāla maksimālā vērtība ir 24 stundas.{0}Attiecīgi nomainiet minūtes vērtību vai stundas vērtību.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Mērķa konteinera ceļš
#XFLD
confluentSubjectName=Subjekta nosaukums
#XFLD
confluentSchemaVersion=Shēmas versija
#XFLD
confluentIncludeTechKeyUpdated=Iekļaut tehnisko atslēgu
#XFLD
confluentOmitNonExpandedArrays=Izlaist neizvērstos masīvus
#XFLD
confluentExpandArrayOrMap=Izvērst masīvu vai karti
#XCOL
confluentOperationMapping=Operāciju kartēšana
#XCOL
confluentOpCode=Op. kods
#XFLD
confluentInsertOpCode=Ievietot
#XFLD
confluentUpdateOpCode=Atjaunināt
#XFLD
confluentDeleteOpCode=Dzēst
#XFLD
expandArrayOrMapNotSelectedTxt=Nav atlasīts
#XFLD
confluentSwitchTxtYes=Jā
#XFLD
confluentSwitchTxtNo=Nē
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Kļūda
#XTIT
executeWarning=Brīdinājums
#XMSG
executeunsavederror=Pirms replicēšanas plūsmas izpildes saglabājiet to.
#XMSG
executemodifiederror=Replicēšanas plūsmā ir nesaglabātas izmaiņas. Lūdzu, saglabājiet replicēšanas plūsmu.
#XMSG
executeundeployederror=Lai varētu izpildīt replicēšanas plūsmu, jums tā vispirms ir jāizvieto.
#XMSG
executedeployingerror=Lūdzu, uzgaidiet, kamēr tiek pabeigta izvietošana.
#XMSG
msgRunStarted=Izpilde ir sākta
#XMSG
msgExecuteFail=Neizdevās izpildīt replicēšanas plūsmu.
#XMSG
titleExecuteBusy=Lūdzu, uzgaidiet.
#XMSG
msgExecuteBusy=Jūsu dati tiek sagatavoti, lai izpildītu replicēšanas plūsmu.
#XTIT
executeConfirmDialog=Brīdinājums
#XMSG
msgExecuteWithValidations=Replicēšanas plūsmai ir pārbaudes kļūdas. Replicēšanas plūsmas izpilde var neizdoties.
#XMSG
msgRunDeployedVersion=Ir izvietojamas izmaiņas. Tiks izpildīta replicēšanas plūsmas pēdējā izvietotā versija. Vai vēlaties turpināt?
#XBUT
btnExecuteAnyway=Vienalga izpildīt
#XBUT
btnExecuteClose=Aizvērt
#XBUT
loaderClose=Aizvērt
#XTIT
loaderTitle=Ielāde
#XMSG
loaderText=Detalizētās informācijas ienese no servera
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Replicēšanas plūsmu uz šo mērķa savienojumu, kas nav SAP mērķa savienojums, nevar startēt,
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=jo šim mēnesim nav pieejams neviens izejošais apjoms.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Administrators var šim nomniekam palielināt premium izejošos blokus,
#XMSG
premiumOutBoundRFAdminErrMsgPart2=padarot pieejamu izejošo apjomu šim mēnesim.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Kļūda
#XTIT
deployInfo=Informācija
#XMSG
deployCheckFailException=Izvietošanas laikā radās izņēmums
#XMSG
deployGBQFFDisabled=Pašlaik replicēšanas plūsmu, kurām ir mērķa savienojums ar pakalpojumu Google BigQuery, izvietošana nav iespējama, jo veicam šīs funkcijas uzturēšanu.
#XMSG
deployKAFKAFFDisabled=Pašlaik replicēšanas plūsmu, kurām ir mērķa savienojums ar pakalpojumu Apache Kafka, izvietošana nav iespējama, jo veicam šīs funkcijas uzturēšanu.
#XMSG
deployConfluentDisabled=Pašlaik replicēšanas plūsmu, kurām ir mērķa savienojums ar pakalpojumu Confluent Kafka, izvietošana nav iespējama, jo veicam šīs funkcijas uzturēšanu.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Mērķa objektu delta tveršanas tabulu nosaukumi jau ir izmantoti citās repozitorija tabulās: {0}. Jums šie mērķa objekti ir jāpārdēvē, lai nodrošinātu saistīto delta tveršanas tabulu nosaukumu unikalitāti, pirms varat tos izvietot replicēšanas plūsmā.
#XMSG
deployDWCSourceFFDisabled=Tādu replicēšanas plūsmu izvietošana, kam kā avots ir SAP Datasphere, pašlaik nav iespējama, jo mēs veicam šīs funkcijas apkopi.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Pašlaik nevar izvietot tādas replicēšanas plūsmas, kam kā avota objekti ir delta iespējotas lokālās tabulas, jo mēs veicam šīs funkcijas uzturēšanu.
#XMSG
deployHDLFSourceFFDisabled=Pašlaik nav iespējams izvietot tādas replicēšanas plūsmas, kurām ir avota savienojumi ar savienojuma tipu “SAP HANA Cloud, datu ezera faili”, jo mēs veicam uzturēšanu.
#XMSG
deployObjectStoreAsSourceFFDisabled=Pašlaik nav iespējams izvietot tādas replicēšanas plūsmas, kurām kā to avots ir mākoņkrātuves nodrošinātāji.
#XMSG
deployConfluentSourceFFDisabled=Tādu replicēšanas plūsmu izvietošana, kam kā avots ir Confluent Kafka, pašlaik nav iespējama, jo mēs veicam šīs funkcijas apkopi.
#XMSG
deployMaxDWCNewTableCrossed=Lielas replicēšanas plūsmas nav iespējams “saglabāt un izvietot” vienā piegājienā. Lūdzu, vispirms saglabājiet savu replicēšanas plūsmu un pēc tam izvietojiet to.
#XMSG
deployInProgressInfo=Izvietošana jau notiek.
#XMSG
deploySourceObjectInUse=Avota objekti {0} jau tiek lietoti replicēšanas plūsmās {1}.
#XMSG
deployTargetSourceObjectInUse=Avota objekti {0} jau tiek lietoti replicēšanas plūsmās {1}. Mērķa objekti {2} jau tiek lietoti replicēšanas plūsmās {3}.
#XMSG
deployReplicationFlowCheckError=Kļūda, pārbaudot replicēšanas plūsmu: {0}
#XMSG
preDeployTargetObjectInUse=Mērķa objekti {0} jau tiek izmantoti replicēšanas plūsmās {1}, un jums nevar būt viens un tas pats mērķa objekts divās dažādās replicēšanas plūsmās. Atlasiet citu mērķa objektu un mēģiniet vēlreiz.
#XMSG
runInProgressInfo=Replicēšanas plūsma jau darbojas.
#XMSG
deploySignavioTargetFFDisabled=Tādu replicēšanas plūsmu izvietošana, kam kā mērķis ir SAP Signavio, pašlaik nav iespējama, jo mēs veicam šīs funkcijas apkopi.
#XMSG
deployHanaViewAsSourceFFDisabled=Pašlaik nav iespējams izvietot replicēšanas plūsmas, kurām ir skati kā avota objekti atlasītajam avota savienojumam. Vēlāk mēģiniet vēlreiz.
#XMSG
deployMsOneLakeTargetFFDisabled=Tādu replicēšanas plūsmu izvietošana, kam kā mērķis ir MS OneLake, pašlaik nav iespējama, jo mēs veicam šīs funkcijas uzturēšanu.
#XMSG
deploySFTPTargetFFDisabled=Tādu replicēšanas plūsmu izvietošana, kam kā mērķis ir SFTP, pašlaik nav iespējama, jo mēs veicam šīs funkcijas uzturēšanu.
#XMSG
deploySFTPSourceFFDisabled=Tādu replicēšanas plūsmu izvietošana, kam kā avots ir SFTP, pašlaik nav iespējama, jo mēs veicam šīs funkcijas uzturēšanu.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Tehniskais nosaukums
#XFLD
businessNameInRenameTarget=Biznesa nosaukums
#XTOL
renametargetDialogTitle=Pārdēvēt mērķa objektu
#XBUT
targetRenameButton=Pārdēvēt
#XBUT
targetRenameCancel=Atcelt
#XMSG
mandatoryTargetName=Jums ir jāievada nosaukums.
#XMSG
dwcSpecialChar=_(pasvītrojums) ir vienīgā speciālā rakstzīme, kas ir atļauta.
#XMSG
dwcWithDot=Mērķa tabulas nosaukumā var būt latīņu alfabēta burti, cipari, pasvītras (_) un punkti (.). Pirmajai rakstzīmei ir jābūt burtam, ciparam vai pasvītrai (tā nedrīkst būt punkts).
#XMSG
nonDwcSpecialChar=Atļautās speciālās rakstzīmes ir _(pasvītrojums) -(defise) .(punkts)
#XMSG
firstUnderscorePattern=Nosaukums nedrīkst sākties ar _ (pasvītru).

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: skatīt priekšrakstu SQL Izveidot tabulu
#XMSG
sqlDialogMaxPKWarning=Vienumā Google BigQuery ir atbalstītas maks. 16 primārās atslēgas, un avota objekts ir lielāks skaitlis. Tāpēc šajā priekšrakstā nav definēta neviena primārā atslēga.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Vienai vai vairākām avota kolonnām ir datu tipi, kurus vienumā Google BigQuery nevar definēt kā primārās atslēgas. Tāpēc šajā gadījumā nav definēta neviena primārā atslēga. Vienumā Google BigQuery tikai šādiem datu tipiem var būt primārā atslēga: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Kopēt un aizvērt
#XBUT
closeDDL=Aizvērt
#XMSG
copiedToClipboard=Kopēts uz starpliktuvi


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Objekts ''{0}'' nevar būt uzdevumu ķēdes daļa, jo tam nav beigu (tā kā tajā ietilpst objekti ar ielādes tipu Sākotnējs un delta/Tikai delta).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Objekts ''{0}'' nevar būt uzdevumu ķēdes daļa, jo tam nav beigu (tā kā tajā ietilpst objekti ar ielādes tipu Sākotnējs un delta).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objektu "{0}" nevar pievienot uzdevumu ķēdei.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Pastāv nesaglabāti mērķa objekti. Lūdzu, saglabājiet vēlreiz.{0}{0} Šī līdzekļa norise ir mainījusies: agrāk mērķa objekti mērķa vidē tika izveidoti tikai pēc replicēšanas plūsmas izvietošanas.{0} Tagad objekti tiek izveidoti jau replicēšanas plūsmas saglabāšanas brīdī. Jūsu replicēšanas plūsma tika izvietota pirms šīm izmaiņām, un tajā ir jauni objekti.{0} Jums šī replicēšanas plūsma ir jāsaglabā vēlreiz, pirms to izvietojat, lai jaunie objekti tiktu iekļauti pareizi.
#XMSG
confirmChangeContentTypeMessage=Jūs grasāties mainīt satura tipu. Ja to darāt, visas esošās projekcijas tiek dzēstas.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Subjekta nosaukums
#XFLD
schemaDialogVersionName=Shēmas versija
#XFLD
includeTechKey=Iekļaut tehnisko atslēgu
#XFLD
segementButtonFlat=Plakana
#XFLD
segementButtonNested=Ligzdota
#XMSG
subjectNamePlaceholder=Meklēt subjekta nosaukumu

#XMSG
@EmailNotificationSuccess=Izpildlaika e-pasta paziņojumu konfigurācija ir saglabāta.

#XFLD
@RuntimeEmailNotification=Izpildlaika e-pasta paziņojums

#XBTN
@TXT_SAVE=Saglabāt


