#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Aliran Replikasi

#XFLD: Edit Schema button text
editSchema=Edit Skema

#XTIT : Properties heading
configSchema=Konfigurasi Skema

#XFLD: save changed button text
applyChanges=Terapkan Perubahan


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Pilih Koneksi Sumber
#XFLD
sourceContainernEmptyText=Pilih Kontainer
#XFLD
targetConnectionEmptyText=Pilih Koneksi Target
#XFLD
targetContainernEmptyText=Pilih Ko<PERSON>
#XFLD
sourceSelectObjectText=Pilih Objek Sumber
#XFLD
sourceObjectCount=Objek Sumber ({0})
#XFLD
targetObjectText=Objek Target
#XFLD
confluentBrowseContext=Pilih konteks
#XBUT
@retry=Coba lagi
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Pemutakhiran penyewa sedang diproses.

#XTOL
browseSourceConnection=Telusuri koneksi sumber
#XTOL
browseTargetConnection=Telusuri koneksi target
#XTOL
browseSourceContainer=Telusuri kontainer sumber
#XTOL
browseAndAddSourceDataset=Tambah objek sumber
#XTOL
browseTargetContainer=Telusuri kontainer target
#XTOL
browseTargetSetting=Telusuri pengaturan target
#XTOL
browseSourceSetting=Telusuri pengaturan sumber
#XTOL
sourceDatasetInfo=Info
#XTOL
sourceDatasetRemove=Hapus
#XTOL
mappingCount=Tampilan ini menunjukkan jumlah total pemetaan/pernyataan berbasis selain nama.
#XTOL
filterCount=Tampilan ini menunjukkan jumlah total kondisi filter.
#XTOL
loading=Memuat...
#XCOL
deltaCapture=Pemindaian Delta
#XCOL
deltaCaptureTableName=Tabel Pemindaian Delta
#XCOL
loadType=Muat Tipe
#XCOL
deleteAllBeforeLoading=Hapus Permanen Semua Sebelum Memuat
#XCOL
transformationsTab=Proyeksi
#XCOL
settingsTab=Pengaturan

#XBUT
renameTargetObjectBtn=Namai Ulang Objek Target
#XBUT
mapToExistingTargetObjectBtn=Petakan ke Objek Target yang Sudah Ada
#XBUT
changeContainerPathBtn=Ubah Jalur Kontainer
#XBUT
viewSQLDDLUpdated=Tampilkan Pernyataan SQL untuk Membuat Tabel
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Objek sumber tidak mendukung pemindaian delta, tetapi objek target yang dipilih memiliki opsi pemindaian delta yang diaktifkan.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Objek target tidak dapat digunakan karena pemindaian delta diaktifkan,{0}sedangkan objek sumber tidak mendukung pemindaian delta.{1}Anda dapat memilih objek target lain yang tidak mendukung pemindaian delta.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Sudah ada objek target dengan nama ini. Namun, objek target ini tidak dapat digunakan{0}karena pemindaian delta diaktifkan, sedangkan objek sumber tidak{0}mendukung pemindaian delta.{1}Anda dapat memasukkan nama objek target yang sudah ada, tetapi tidak{0}mendukung pemindaian delta, atau memasukkan nama yang belum ada.
#XBUT
copySQLDDLUpdated=Salin Pernyataan SQL untuk Membuat Tabel
#XMSG
targetObjExistingNoCDCColumnUpdated=Tabel yang ada di Google BigQuery harus menyertakan kolom berikut untuk pengambilan data perubahan (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Objek sumber berikut tidak didukung karena tidak memiliki kunci utama atau menggunakan koneksi yang tidak memenuhi ketentuan untuk mengakses kunci utama:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Silakan merujuk ke SAP KBA 3531135 untuk informasi penyelesaian.
#XLST: load type list values
initial=Hanya Awal
@emailUpdateError=Terjadi kesalahan saat memperbarui daftar Pemberitahuan Email

#XLST
initialDelta=Awal dan Delta

#XLST
deltaOnly=Delta Saja
#XMSG
confluentDeltaLoadTypeInfo=Untuk sumber Confluent Kafka, hanya tipe muatan Awal dan Delta yang didukung.
#XMSG
confirmRemoveReplicationObject=Apakah Anda mengonfirmasi bahwa Anda ingin menghapus permanen replikasi?
#XMSG
confirmRemoveReplicationTaskPrompt=Tindakan ini akan menghapus permanen replikasi yang sudah ada. Apakah Anda ingin melanjutkan?
#XMSG
confirmTargetConnectionChangePrompt=Tindakan ini akan mengatur ulang koneksi target, kontainer target, dan menghapus permanen semua objek target. Apakah Anda ingin melanjutkan?
#XMSG
confirmTargetContainerChangePrompt=Tindakan ini akan mengatur ulang kontainer target dan menghapus permanen semua objek target yang sudah ada. Apakah Anda ingin melanjutkan?
#XMSG
confirmRemoveTransformObject=Apakah Anda mengonfirmasi bahwa Anda ingin menghapus permanen proyeksi {0}?
#XMSG
ErrorMsgContainerChange=Terjadi kesalahan saat mengubah jalur kontainer.
#XMSG
infoForUnsupportedDatasetNoKeys=Objek sumber berikut tidak didukung karena tidak memiliki kunci utama:
#XMSG
infoForUnsupportedDatasetView=Objek sumber dengan tipe Tampilan berikut tidak didukung:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Objek sumber berikut tidak didukung karena merupakan tampilan SQL yang berisi parameter input:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Objek sumber berikut tidak didukung karena ekstraksi dinonaktifkan untuk objek tersebut:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Untuk koneksi Confluent, satu-satunya format serialisasi yang diperbolehkan adalah AVRO dan JSON. Objek-objek berikut tidak didukung karena menggunakan format serialisasi yang berbeda:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Tidak dapat mengambil skema untuk beberapa objek berikut. Silakan pilih konteks yang sesuai atau verifikasi konfigurasi registri skema
#XTOL: warning dialog header on deleting replication task
deleteHeader=Hapus Permanen
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Pengaturan Hapus Permanen Semua Sebelum Memuat tidak didukung di Google BigQuery.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Pengaturan Hapus Permanen Semua Sebelum Memuat akan menghapus permanen dan membuat ulang objek tersebut (topik) sebelum setiap replikasinya. Pengaturan ini juga menghapus permanen semua pesan yang ditetapkan.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Pengaturan Hapus Permanen Semua Sebelum Memuat tidak didukung untuk tipe target ini.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Nama Teknis
#XCOL
connBusinessName=Nama Bisnis
#XCOL
connDescriptionName=Deskripsi
#XCOL
connType=Tipe
#XMSG
connTblNoDataFoundtxt=Koneksi Tidak Ditemukan
#XMSG
connectionError=Terjadi kesalahan saat mengambil koneksi.
#XMSG
connectionCombinationUnsupportedErrorTitle=Kombinasi koneksi tidak didukung
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Replikasi dari {0} ke {1} saat ini tidak didukung.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Kombinasi tipe koneksi tidak didukung
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Replikasi dari koneksi dengan tipe koneksi SAP HANA Cloud, Data Lake Files ke {0} tidak didukung. Anda hanya dapat melakukan replikasi ke SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Pilih
#XBUT
containerCancelBtn=Batalkan
#XTOL
containerSelectTooltip=Pilih
#XTOL
containerCancelTooltip=Batalkan
#XMSG
containerContainerPathPlcHold=Jalur Kontainer
#XFLD
containerContainertxt=Kontainer
#XFLD
confluentContainerContainertxt=Konteks
#XMSG
infoMessageForSLTSelection=ID Transfer Only/SLT/Mass diizinkan sebagai kontainer. Pilih ID Transfer Mass dalam SLT (jika tersedia) dan klik Kirim.
#XMSG
msgFetchContainerFail=Terjadi kesalahan saat mengambil data kontainer.
#XMSG
infoMessageForSLTHidden=Koneksi ini tidak mendukung folder SLT, sehingga tidak ditampilkan dalam daftar di bawah.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Pilih kontainer yang memiliki subfolder di dalamnya.
#XMSG
sftpIncludeSubFolderText=Salah
#XMSG
sftpIncludeSubFolderTextNew=Tidak

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Belum ada pemetaan filter)
#XMSG
failToFetchRemoteMetadata=Terjadi kesalahan saat mengambil metadata.
#XMSG
failToFetchData=Terjadi kesalahan saat mengambil target yang ada.
#XCOL
@loadType=Muat Tipe
#XCOL
@deleteAllBeforeLoading=Hapus Permanen Semua Sebelum Memuat

#XMSG
@loading=Memuat...
#XFLD
@selectSourceObjects=Pilih Objek Sumber
#XMSG
@exceedLimit=Anda tidak dapat mengimpor lebih dari {0} objek dalam satu waktu. Harap batalkan pilihan setidaknya {1} objek.
#XFLD
@objects=Objek
#XBUT
@ok=OKE
#XBUT
@cancel=Batalkan
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Selanjutnya
#XBUT
btnAddSelection=Tambahkan Pilihan
#XTOL
@remoteFromSelection=Hapus dari Pilihan
#XMSG
@searchInForSearchField=Cari di {0}

#XCOL
@name=Nama Teknis
#XCOL
@type=Tipe
#XCOL
@location=Lokasi
#XCOL
@label=Nama Bisnis
#XCOL
@status=Status

#XFLD
@searchIn=Cari di:
#XBUT
@available=Tersedia
#XBUT
@selection=Pilihan

#XFLD
@noSourceSubFolder=Tabel dan Tampilan
#XMSG
@alreadyAdded=Telah ada di dalam diagram
#XMSG
@askForFilter=Ada lebih dari {0} item. Silakan masukkan string filter untuk mempersempit jumlah item.
#XFLD: success label
lblSuccess=Berhasil
#XFLD: ready label
lblReady=Siap
#XFLD: failure label
lblFailed=Gagal
#XFLD: fetching status label
lblFetchingDetail=Mengambil rincian

#XMSG Place holder text for tree filter control
filterPlaceHolder=Ketik teks untuk memfilter objek tingkat teratas
#XMSG Place holder text for server search control
serverSearchPlaceholder=Ketik dan tekan Enter untuk mencari
#XMSG
@deployObjects=Mengimpor {0} objek...
#XMSG
@deployObjectsStatus=Jumlah objek yang telah diimpor: {0}. Jumlah objek yang tidak dapat diimpor: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Gagal membuka browser repositori lokal.
#XMSG
@openRemoteSourceBrowserError=Gagal mengambil objek sumber.
#XMSG
@openRemoteTargetBrowserError=Gagal mengambil objek target.
#XMSG
@validatingTargetsError=Terjadi kesalahan saat memvalidasi target.
#XMSG
@waitingToImport=Siap Diimpor

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Jumlah maksimum objek telah terlampaui. Pilih maksimum 500 objek untuk satu aliran replikasi.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Nama Teknis
#XFLD
sourceObjectBusinessName=Nama Bisnis
#XFLD
sourceNoColumns=Jumlah Kolom
#XFLD
containerLbl=Kontainer

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Anda harus memilih koneksi sumber untuk aliran replikasi.
#XMSG
validationSourceContainerNonExist=Anda harus memilih kontainer untuk koneksi sumber.
#XMSG
validationTargetNonExist=Anda harus memilih koneksi target untuk aliran replikasi.
#XMSG
validationTargetContainerNonExist=Anda harus memilih kontainer untuk koneksi target.
#XMSG
validationTruncateDisabledForObjectTitle=Replikasi ke penyimpanan objek.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Replikasi ke dalam penyimpanan cloud hanya dapat dilakukan jika opsi Hapus Permanen Semua Sebelum Memuat diatur atau objek target tidak ada di target.{0}{0} Untuk tetap mengaktifkan replikasi pada objek yang tidak menetapkan opsi Hapus Permanen Semua Sebelum Memuat, pastikan objek target tidak ada di sistem sebelum Anda mengeksekusi aliran replikasi.
#XMSG
validationTaskNonExist=Anda harus memiliki setidaknya satu replikasi di aliran replikasi.
#XMSG
validationTaskTargetMissing=Anda harus memiliki target untuk replikasi dengan sumber: {0}
#XMSG
validationTaskTargetIsSAC=Target yang dipilih adalah Artefak SAC: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Target yang dipilih bukan tabel lokal yang didukung: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Objek dengan nama ini sudah ada di target. Namun, objek ini tidak dapat digunakan sebagai objek target untuk aliran replikasi ke repositori lokal, karena objek ini bukan tabel lokal.
#XMSG
validateSourceTargetSystemDifference=Anda harus memilih kombinasi koneksi dan kontainer sumber dan target yang berbeda untuk aliran replikasi.
#XMSG
validateDuplicateSources=satu atau beberapa replikasi memiliki nama objek sumber duplikat: {0}.
#XMSG
validateDuplicateTargets=satu atau beberapa replikasi memiliki nama objek target duplikat: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=Objek sumber {0} tidak mendukung pemindaian delta, sebaliknya objek target {1} mendukung pemindaian delta. Anda harus menghapus replikasi.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Anda harus memilih tipe muatan "Awal dan Delta" untuk replikasi dengan nama objek target {0}.
#XMSG
validationAutoRenameTarget=Kolom target telah dinamai ulang.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Proyeksi otomatis telah ditambahkan, dan kolom target berikut telah dinamai ulang agar direplikasi ke target:{1}{1} {0} {1}{1}Penamaan ulang ini dilakukan karena salah satu alasan berikut:{1}{1}{2} Karakter yang tidak didukung{1}{2} Prefiks yang dicadangkan
#XMSG
validationAutoRenameTargetDescriptionUpdated=Proyeksi otomatis telah ditambahkan dan kolom target berikut telah dinamai ulang untuk mengizinkan replikasi ke Google BigQuery:{1}{1} {0} {1}{1}Penamaan ulang dilakukan karena salah satu alasan berikut:{1}{1}{2} Nama kolom yang dicadangkan{1}{2} Karakter yang tidak didukung{1}{2} Prefiks yang dicadangkan
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Proyeksi otomatis telah ditambahkan, dan kolom target berikut telah dinamai ulang agar direplikasi ke Confluent:{1}{1} {0} {1}{1}Penamaan ulang ini dilakukan karena salah satu alasan berikut:{1}{1}{2} Nama kolom yang dicadangkan{1}{2} Karakter yang tidak didukung{1}{2} Prefiks yang dicadangkan
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Proyeksi otomatis telah ditambahkan, dan kolom target berikut telah dinamai ulang agar direplikasi ke target:{1}{1} {0} {1}{1}Penamaan ulang ini dilakukan karena salah satu alasan berikut:{1}{1}{2} Nama kolom yang dicadangkan{1}{2} Karakter yang tidak didukung{1}{2} Prefiks yang dicadangkan
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Objek target telah dinamai ulang.
#XMSG
autoRenameInfoDesc=Objek target telah dinamai ulang karena berisi karakter yang tidak didukung. Hanya karakter berikut yang didukung:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(titik){0}{1}_(tanda hubung bawah){0}{1}-(tanda hubung)
#XMSG
validationAutoTargetTypeConversion=Tipe data target telah diubah.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Untuk kolom target berikut, tipe data target telah diubah karena tipe data sumber tidak didukung di Google BigQuery:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Untuk kolom target berikut, tipe data target telah diubah karena tipe data sumber tidak didukung dalam koneksi target:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Persingkat nama kolom target.
#XMSG
validationMaxCharLengthGBQTargetDescription=Di Google BigQuery, nama kolom dapat menggunakan maksimum 300 karakter. Gunakan proyeksi untuk mempersingkat nama kolom target berikut:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Kunci utama tidak akan dibuat.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=Di Google BigQuery, maksimum 16 kunci utama didukung, tetapi objek sumber memiliki angka yang lebih besar dari kunci utama. Tidak ada kunci utama yang akan dibuat di objek target.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Satu atau beberapa kolom sumber memiliki tipe data yang tidak dapat ditetapkan sebagai kunci utama di Google BigQuery. Oleh karena itu, tidak ada kunci utama yang akan dibuat di objek target.{0}{0}Berikut adalah tipe data target yang kompatibel dengan tipe data Google BigQuery, sehingga dapat ditetapkan sebagai kunci utama:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Tentukan satu atau beberapa kolom sebagai kunci utama.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Anda harus menentukan satu atau beberapa kolom sebagai kunci utama menggunakan dialog skema sumber.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Tentukan satu atau beberapa kolom sebagai kunci utama.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Anda harus menentukan satu atau beberapa kolom sebagai kunci utama yang memenuhi batasan kunci utama pada objek sumber Anda. Buka bagian Konfigurasi Skema di properti objek sumber untuk menetapkan kunci utama tersebut.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Masukkan nilai partisi maksimum yang valid.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Nilai Partisi maksimum harus ≥ 1 dan ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Tentukan satu atau beberapa kolom sebagai kunci utama.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Untuk mereplikasi objek, Anda harus menentukan satu atau beberapa kolom target sebagai kunci utama. Gunakan proyeksi untuk melakukannya.
#XMSG
validateHDLFNoPKExistingDatasetError=Tentukan satu atau beberapa kolom sebagai kunci utama.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Untuk mereplikasi data ke objek target yang sudah ada, objek target harus memiliki satu atau beberapa kolom yang ditentukan sebagai kunci utama. {0} Anda memiliki opsi berikut untuk menentukan satu atau beberapa kolom sebagai kunci utama: {0} {1} Gunakan editor tabel lokal untuk mengubah objek target yang sudah ada. Kemudian muat ulang aliran replikasinya.{0}{1} Namai ulang objek target di aliran replikasi. Penamaan ulang ini akan membuat objek baru segera setelah eksekusi dimulai. Setelah menamai ulang, Anda dapat menentukan satu atau beberapa kolom sebagai kunci utama dalam proyeksi.{0}{1} Petakan objek ke objek target lain yang sudah ada, yang satu atau beberapa kolomnya sudah ditentukan sebagai kunci utama.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Target yang dipilih sudah ada di repositori: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Nama tabel pemindaian delta telah digunakan oleh tabel lain di repositori: {0}. Anda harus menamai ulang objek target ini untuk memastikan bahwa nama tabel pemindaian delta terkait sudah memenuhi kriteria unik sebelum Anda dapat menyimpan alur replikasi.
#XMSG
validateConfluentEmptySchema=Tentukan Skema
#XMSG
validateConfluentEmptySchemaDescUpdated=Tabel sumber tidak memiliki skema. Pilih Konfigurasi Skema untuk menentukan satu skema
#XMSG
validationCSVEncoding=Pembuatan Kode CSV Tidak Valid
#XMSG
validationCSVEncodingDescription=Pembuatan kode CSV dari tugas tidak valid.
#XMSG
validateConfluentEmptySchema=Pilih tipe data target yang kompatibel
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Pilih tipe data target yang kompatibel
#XMSG
globalValidateTargetDataTypeDesc=Terjadi kesalahan dalam pemetaan kolom. Buka Proyeksi dan pastikan semua kolom sumber dipetakan ke kolom yang unik, memiliki tipe data yang sesuai, dan menggunakan semua ekspresi yang valid.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Nama Kolom Duplikat.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Nama Kolom Duplikat tidak didukung. Gunakan Dialog Proyeksi untuk memperbaikinya. Objek target berikut memiliki nama kolom duplikat: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Nama Kolom Duplikat.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Nama Kolom Duplikat tidak didukung. Objek target berikut memiliki nama kolom duplikat: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Kemungkinan terdapat inkonsistensi dalam data.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Tipe muatan Delta Saja akan mengabaikan perubahan yang dibuat dalam sumber antara penyimpanan terakhir dan eksekusi selanjutnya.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Ubah tipe muatan menjadi "Awal".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Mereplikasi objek berbasis ABAP yang tidak memiliki kunci utama hanya dapat dilakukan untuk tipe muatan "Hanya Awal".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Nonaktifkan Pemindaian Delta.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Untuk mereplikasi objek yang tidak memiliki kunci utama menggunakan koneksi sumber tipe ABAP, Anda harus menonaktifkan pemindaian delta untuk tabel ini terlebih dahulu.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Ubah objek target.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Objek target tidak dapat digunakan karena pemindaian delta diaktifkan. Anda dapat menamai ulang objek target kemudian menonaktifkan pemindaian delta untuk objek baru (yang telah dinamai ulang), atau memetakan objek sumber ke objek target yang pemindaian deltanya dinonaktifkan.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Ubah objek target.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Objek target tidak dapat digunakan karena tidak memiliki kolom teknis yang diperlukan, __load_package_id. Anda dapat menamai ulang objek target menggunakan nama yang belum ada. Dengan ini, sistem akan membuat objek baru dengan definisi yang sama dengan objek sumber dan berisi kolom teknis. Atau, Anda dapat memetakan objek target ke objek yang sudah ada, yang memiliki kolom teknis yang diperlukan (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Ubah objek target.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Objek target tidak dapat digunakan karena tidak memiliki kolom teknis yang diperlukan, __load_record_id. Anda dapat menamai ulang objek target menggunakan nama yang belum ada. Dengan ini, sistem akan membuat objek baru dengan definisi yang sama dengan objek sumber dan berisi kolom teknis. Atau, Anda dapat memetakan objek target ke objek yang sudah ada, yang memiliki kolom teknis yang diperlukan (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Ubah objek target.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Objek target tidak dapat digunakan karena tipe data dari kolom teknisnya, __load_record_id, bukan "string(44)". Anda dapat menamai ulang objek target menggunakan nama yang belum ada. Dengan ini, sistem akan membuat objek baru dengan definisi yang sama dengan objek sumber dan memiliki tipe data yang benar. Atau, Anda dapat memetakan objek target ke objek yang sudah ada, yang memiliki kolom teknis yang diperlukan (__load_record_id) dengan tipe data yang benar.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Ubah objek target.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Objek target tidak dapat digunakan karena memiliki kunci utama, sementara objek sumber sebaliknya. Anda dapat menamai ulang objek target menggunakan nama yang belum ada. Dengan ini, sistem akan membuat objek baru dengan definisi yang sama dengan objek sumber dan tidak memiliki kunci utama. Atau, Anda dapat memetakan objek target ke objek yang sudah ada, yang memiliki kolom teknis yang diperlukan (__load_package_id) dan tidak memiliki kunci utama.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Ubah objek target.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Objek target tidak dapat digunakan karena memiliki kunci utama, sementara objek sumber sebaliknya. Anda dapat menamai ulang objek target menggunakan nama yang belum ada. Dengan ini, sistem akan membuat objek baru dengan definisi yang sama dengan objek sumber dan tidak memiliki kunci utama. Atau, Anda dapat memetakan objek target ke objek yang sudah ada, yang memiliki kolom teknis yang diperlukan (__load_record_id) dan tidak memiliki kunci utama.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Ubah objek target.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Objek target tidak dapat digunakan karena tipe data dari kolom teknisnya, __load_record_id, bukan "binary(256)". Anda dapat menamai ulang objek target menggunakan nama yang belum ada. Sistem akan membuat objek baru dengan definisi yang sama seperti objek sumber sehingga memiliki tipe data yang benar. Atau, Anda dapat memetakan objek target ke objek yang sudah ada, yang memiliki kolom teknis yang diperlukan (__load_record_id) dengan tipe data yang benar.
#XMSG
validationAutoRenameTargetDPID=Kolom target telah dinamai ulang.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Hapus objek sumber.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Objek sumber tidak memiliki kolom kunci, yang tidak didukung dalam konteks ini.
#XMSG
validationAutoRenameTargetDPIDDescription=Proyeksi otomatis telah ditambahkan, dan kolom target berikut telah dinamai ulang untuk mengizinkan replikasi dari sumber ABAP tanpa kunci :{1}{1} {0} {1}{1}Penamaan ulang ini dilakukan karena salah satu alasan berikut:{1}{1}{2} Nama kolom yang dicadangkan{1}{2} Karakter yang tidak didukung{1}{2} Prefiks yang dicadangkan
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Replikasi ke {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Saat ini tidak mungkin untuk melakukan penyimpanan dan penyebaran aliran replikasi dengan {0} sebagai targetnya karena sedang dilakukan pemeliharaan pada fungsi ini.
#XMSG
TargetColumnSkippedLTF=Kolom target telah dilewati.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Kolom target telah dilewati karena terdapat tipe data yang tidak didukung. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Kolom Waktu sebagai Kunci Utama.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Objek sumber memiliki kolom waktu sebagai kunci utama, yang tidak didukung dalam konteks ini.
#XMSG
validateNoPKInLTFTarget=Kunci Utama tidak ditemukan.
#XMSG
validateNoPKInLTFTargetDescription=Kunci utama tidak ditentukan di target yang tidak didukung dalam konteks ini.
#XMSG
validateABAPClusterTableLTF=Tabel Kluster ABAP.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Objek sumber merupakan tabel kluster ABAP, yang tidak didukung dalam konteks ini.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Tampaknya Anda belum menambahkan data apa pun.
#YINS
welcomeText2=Untuk memulai aliran replikasi Anda, pilih koneksi dan objek sumber di sisi kiri.

#XBUT
wizStep1=Pilih Koneksi Sumber
#XBUT
wizStep2=Pilih Kontainer Sumber
#XBUT
wizStep3=Tambah Objek Sumber

#XMSG
limitDataset=Jumlah maksimum objek telah tercapai. Hapus objek yang ada untuk menambahkan objek baru, atau buat aliran replikasi baru.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Aliran replikasi ke koneksi target non-SAP ini tidak dapat dimulai karena tidak ada volume keluar yang tersedia untuk bulan ini.
#XMSG
premiumOutBoundRFAdminWarningMsg=Administrator dapat meningkatkan blok Keluaran Premium untuk penyewa ini, sehingga volume keluar tersedia untuk bulan ini.
#XMSG
messageForToastForDPIDColumn2=Kolom baru ditambahkan ke target untuk objek {0} - diperlukan untuk menangani catatan duplikat sehubungan dengan objek sumber berbasis ABAP yang tidak memiliki kunci utama.
#XMSG
PremiumInboundWarningMessage=Bergantung pada jumlah aliran replikasi dan volume data yang akan direplikasi, sumber daya SAP HANA{0}yang diperlukan untuk mereplikasi data melalui {1} mungkin melebihi kapasitas yang tersedia untuk penyewa Anda.
#XMSG
PremiumInboundWarningMsg=Bergantung pada jumlah aliran replikasi dan volume data yang akan direplikasi, sumber daya SAP HANA{0}yang diperlukan untuk mereplikasi data melalui "{1}" mungkin melebihi kapasitas yang tersedia untuk penyewa Anda.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Masukkan nama proyeksi.
#XMSG
emptyTargetColumn=Masukkan nama kolom target.
#XMSG
emptyTargetColumnBusinessName=Masukkan Nama Bisnis kolom target.
#XMSG
invalidTransformName=Masukkan nama proyeksi.
#XMSG
uniqueColumnName=Namai ulang kolom target.
#XMSG
copySourceColumnLbl=Salin kolom dari objek sumber
#XMSG
renameWarning=Pastikan untuk memilih nama yang unik saat menamai ulang nama tabel target. Jika tabel dengan nama baru sudah ada dalam ruang, sistem akan menggunakan definisi tabel tersebut.

#XMSG
uniqueColumnBusinessName=Namai ulang nama bisnis dari kolom target
#XMSG
uniqueSourceMapping=Pilih kolom sumber yang berbeda.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=Kolom sumber {0} sudah digunakan oleh kolom target berikut:{1}{1}{2}{1}{1} Untuk kolom target ini atau untuk kolom target lain, pilih kolom sumber yang belum digunakan untuk menyimpan proyeksi.
#XMSG
uniqueColumnNameDescription=Nama kolom target yang Anda masukkan sudah ada. Untuk dapat menyimpan proyeksi, Anda perlu memasukkan nama kolom yang unik.
#XMSG
uniqueColumnBusinessNameDesc=Nama bisnis kolom target sudah ada. Untuk menyimpan proyeksi, Anda harus memasukkan nama bisnis kolom yang unik.
#XMSG
emptySource=Pilih kolom sumber atau masukkan konstanta.
#XMSG
emptySourceDescription=Untuk membuat entri pemetaan yang valid, Anda perlu memilih kolom sumber atau memasukkan nilai konstanta.
#XMSG
emptyExpression=Tentukan pemetaan.
#XMSG
emptyExpressionDescription1=Pilih kolom sumber yang ingin Anda petakan ke kolom target, atau tandai kotak centang di kolom {0} Fungsi / Konstanta {1}. {2} {2} Fungsi dimasukkan secara otomatis menurut tipe data target. Nilai konstanta dapat dimasukkan secara manual.
#XMSG
numberExpressionErr=Masukkan angka.
#XMSG
numberExpressionErrDescription=Anda memilih tipe data numerik. Artinya, Anda hanya dapat memasukkan angka, ditambah dengan titik desimal jika ada. Jangan gunakan tanda kutip tunggal.
#XMSG
invalidLength=Masukkan nilai panjang yang valid.
#XMSG
invalidLengthDescription=Panjang tipe data harus sama atau lebih besar dari panjang kolom sumber dan dapat berada di antara 1 hingga 5000.
#XMSG
invalidMappedLength=Masukkan nilai panjang yang valid.
#XMSG
invalidMappedLengthDescription=Panjang tipe data harus sama atau lebih besar dari panjang kolom sumber {0} dan dapat berada di antara 1 hingga 5000.
#XMSG
invalidPrecision=Masukkan nilai presisi yang valid.
#XMSG
invalidPrecisionDescription=Presisi menentukan jumlah total digit. Skala menentukan jumlah digit setelah titik desimal dan dapat berada di antara 0 dan presisinya.{0}{0} Contoh: {0}{1} Presisi 6, skala 2 sesuai dengan angka seperti 1234,56.{0}{1} Presisi 6, skala 6 sesuai dengan angka seperti 0,123546.{0} {0} Presisi dan skala untuk target harus sesuai dengan presisi dan skala untuk sumber, sehingga semua digit dari sumber sesuai dengan bidang target. Misalnya, jika Anda memiliki presisi 6 dan skala 2 pada sumber (dan akibatnya digit selain 0 sebelum titik desimal), Anda tidak dapat memiliki presisi 6 dan skala 6 pada target.
#XMSG
invalidPrimaryKey=Masukkan setidaknya satu kunci utama.
#XMSG
invalidPrimaryKeyDescription=Kunci utama untuk skema ini tidak ditentukan.
#XMSG
invalidMappedPrecision=Masukkan nilai presisi yang valid.
#XMSG
invalidMappedPrecisionDescription1=Presisi menentukan jumlah total digit. Skala menentukan jumlah digit setelah titik desimal dan dapat berada di antara 0 dan presisinya.{0}{0} Contoh:{0}{1} Presisi 6, skala 2 sesuai dengan angka seperti 1234,56.{0}{1} Presisi 6, skala 6 sesuai dengan angka seperti 0,123546.{0}{0}Presisi tipe data harus sama atau lebih besar dari presisi sumbernya ({2}).
#XMSG
invalidScale=Masukkan nilai skala yang valid.
#XMSG
invalidScaleDescription=Presisi menentukan jumlah total digit. Skala menentukan jumlah digit setelah titik desimal dan dapat berada di antara 0 dan presisinya.{0}{0} Contoh: {0}{1} Presisi 6, skala 2 sesuai dengan angka seperti 1234,56.{0}{1} Presisi 6, skala 6 sesuai dengan angka seperti 0,123546.{0} {0} Presisi dan skala untuk target harus sesuai dengan presisi dan skala untuk sumber, sehingga semua digit dari sumber sesuai dengan bidang target. Misalnya, jika Anda memiliki presisi 6 dan skala 2 pada sumber (dan akibatnya digit selain 0 sebelum titik desimal), Anda tidak dapat memiliki presisi 6 dan skala 6 pada target.
#XMSG
invalidMappedScale=Masukkan nilai skala yang valid.
#XMSG
invalidMappedScaleDescription1=Presisi menentukan jumlah total digit. Skala menentukan jumlah digit setelah titik desimal dan dapat berada di antara 0 dan presisinya.{0}{0} Contoh:{0}{1} Presisi 6, skala 2 sesuai dengan angka seperti 1234,56.{0}{1} Presisi 6, skala 6 sesuai dengan angka seperti 0,123546.{0}{0} Skala tipe data harus sama atau lebih besar dari skala sumbernya ({2}).
#XMSG
nonCompatibleDataType=Pilih tipe data target yang kompatibel.
#XMSG
nonCompatibleDataTypeDescription1=Tipe data yang Anda tentukan di sini harus kompatibel dengan tipe data sumber ({0}). {1}{1} Contoh: Jika kolom sumber Anda memiliki string tipe data dan berisi huruf, Anda tidak dapat menggunakan tipe data desimal untuk target Anda.
#XMSG
invalidColumnCount=Pilih kolom sumber.
#XMSG
ObjectStoreInvalidScaleORPrecision=Masukkan nilai yang valid untuk presisi dan skala.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=Nilai yang pertama adalah presisi, yang menentukan jumlah total digit. Nilai yang kedua adalah skala, yang menentukan jumlah digit setelah titik desimal. Masukkan nilai skala target yang lebih besar dari nilai skala sumber dan pastikan selisih antara nilai skala target yang dimasukkan dan nilai presisi lebih besar dari selisih antara nilai skala sumber dan nilai presisi.
#XMSG
InvalidPrecisionORScale=Masukkan nilai yang valid untuk presisi dan skala.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=Nilai pertama adalah presisi, yang menentukan total jumlah digit. Nilai kedua adalah skala, yang menentukan digit-digit setelah titik desimal.{0}{0}Berhubung tipe data sumber tidak didukung dalam Google BigQuery, maka diubah menjadi tipe data target DECIMAL. Dalam hal ini, presisi hanya dapat diatur antara 38 dan 76, dan skala antara 9 dan 38. Selain itu, hasil dari presisi dikurangi skala, yang mewakili digit-digit sebelum titik desimal, harus berada di antara 29 dan 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=Nilai pertama adalah presisi, yang menentukan total jumlah digit. Nilai kedua adalah skala, yang menentukan digit-digit setelah titik desimal.{0}{0}Berhubung tipe data sumber tidak didukung dalam Google BigQuery, maka diubah menjadi tipe data target DECIMAL. Dalam hal ini, presisi harus diatur sebagai 20 atau lebih besar. Selain itu, hasil dari presisi dikurangi skala, yang mencerminkan digit-digit sebelum titik desimal, harus berada di 20 atau lebih besar.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=Nilai pertama adalah presisi, yang menentukan total jumlah digit. Nilai kedua adalah skala, yang menentukan digit-digit setelah titik desimal.{0}{0}Berhubung tipe data sumber tidak didukung di target, maka tipe data tersebut dikonversi menjadi tipe data target DECIMAL. Dalam hal ini, presisi harus diatur menggunakan angka yang lebih besar dari atau sama dengan 1 dan kurang dari atau sama dengan 38, dan skala harus kurang dari atau sama dengan presisi.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=Nilai pertama adalah presisi, yang menentukan total jumlah digit. Nilai kedua adalah skala, yang menentukan digit-digit setelah titik desimal.{0}{0}Berhubung tipe data sumber tidak didukung di target, maka tipe data tersebut dikonversi menjadi tipe data target DECIMAL. Dalam hal ini, presisi harus diatur sebagai 20 atau lebih besar. Selain itu, hasil dari presisi dikurangi skala, yang mencerminkan digit-digit sebelum titik desimal, harus berada di 20 atau lebih besar.
#XMSG
invalidColumnCountDescription=Untuk membuat entri pemetaan yang valid, Anda perlu memilih kolom sumber atau memasukkan nilai konstanta.
#XMSG
duplicateColumns=Namai ulang kolom target.
#XMSG
duplicateGBQCDCColumnsDesc=Nama kolom target telah digunakan di Google BigQuery. Anda perlu menamai ulang kolom targetnya agar dapat menyimpan proyeksi.
#XMSG
duplicateConfluentCDCColumnsDesc=Nama kolom target digunakan di Confluent. Anda perlu menamai ulang kolom targetnya agar dapat menyimpan proyeksi.
#XMSG
duplicateSignavioCDCColumnsDesc=Nama kolom target digunakan di SAP Signavio. Anda perlu menamai ulang kolom targetnya agar dapat menyimpan proyeksi.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Nama kolom target digunakan di MS OneLake. Anda perlu menamai ulang kolom targetnya agar dapat menyimpan proyeksi.
#XMSG
duplicateSFTPCDCColumnsDesc=Nama kolom target digunakan di SFTP. Anda perlu menamai ulang kolom targetnya agar dapat menyimpan proyeksi.
#XMSG
GBQTargetNameWithPrefixUpdated1=Nama kolom target berisi prefiks yang telah digunakan di Google BigQuery. Anda perlu menamai ulang kolom targetnya agar dapat menyimpan proyeksi. {0}{0}Nama kolom target tidak boleh diawali dengan salah satu string berikut:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Persingkat nama kolom target.
#XMSG
GBQtargetMaxLengthDesc=Di Google BigQuery, nama kolom dapat menggunakan maksimal 300 karakter. Persingkat nama kolom target agar dapat menyimpan proyeksi.
#XMSG
invalidMappedScalePrecision=Presisi dan skala antara target dan sumber harus kompatibel sehingga semua digit dari sumber dapat masuk ke dalam bidang targetnya.
#XMSG
invalidMappedScalePrecisionShortText=Masukkan nilai presisi dan skala yang valid.
#XMSG
validationIncompatiblePKTypeDescProjection3=Satu atau beberapa kolom sumber memiliki tipe data yang tidak dapat ditetapkan sebagai kunci utama di Google BigQuery. Oleh karena itu, tidak ada kunci utama yang akan dibuat di objek target.{0}{0}Berikut adalah tipe data target yang kompatibel dengan tipe data Google BigQuery, sehingga dapat ditetapkan sebagai kunci utama:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Hapus centang pada kolom __message_id.
#XMSG
uncheckColumnMessageIdDesc=Kolom: Kunci Utama
#XMSG
validationOpCodeInsert=Anda harus memasukkan nilai untuk Insert.
#XMSG
recommendDifferentPrimaryKey=Disarankan untuk memilih kunci utama yang berbeda di tingkat item.
#XMSG
recommendDifferentPrimaryKeyDesc=Saat kode operasi telah ditentukan, disarankan untuk memilih kunci utama yang berbeda untuk indeks larik dan item untuk menghindari masalah, misalnya duplikasi kolom.
#XMSG
selectPrimaryKeyItemLevel=Anda harus memilih setidaknya satu kunci utama untuk tingkat header dan item.
#XMSG
selectPrimaryKeyItemLevelDesc=Saat larik atau peta diperluas, Anda harus memilih dua kunci utama: satu di tingkat header dan satu di tingkat item.
#XMSG
invalidMapKey=Anda harus memilih setidaknya satu kunci utama di tingkat header.
#XMSG
invalidMapKeyDesc=Saat larik atau peta diperluas, Anda harus memilih satu kunci utama di tingkat header.
#XFLD
txtSearchFields=Cari Kolom Target
#XFLD
txtName=Nama
#XMSG
txtSourceColValidation=Satu atau beberapa kolom sumber tidak didukung:
#XMSG
txtMappingCount=Pemetaan ({0})
#XMSG
schema=Skema
#XMSG
sourceColumn=Kolom Sumber
#XMSG
warningSourceSchema=Setiap perubahan yang dilakukan pada skema akan memengaruhi pemetaan dalam dialog proyeksi.
#XCOL
txtTargetColName=Kolom Target (Nama Teknis)
#XCOL
txtDataType=Tipe Data Target
#XCOL
txtSourceDataType=Tipe Data Sumber
#XCOL
srcColName=Kolom Sumber (Nama Teknis)
#XCOL
precision=Presisi
#XCOL
scale=Skala
#XCOL
functionsOrConstants=Fungsi / Konstanta
#XCOL
txtTargetColBusinessName=Kolom Target (Nama Bisnis)
#XCOL
prKey=Kunci Utama
#XCOL
txtProperties=Properti
#XBUT
txtOK=Simpan
#XBUT
txtCancel=Batalkan
#XBUT
txtRemove=Hapus
#XFLD
txtDesc=Deskripsi
#XMSG
rftdMapping=Pemetaan
#XFLD
@lblColumnDataType=Tipe Data
#XFLD
@lblColumnTechnicalName=Nama Teknis
#XBUT
txtAutomap=Petakan Otomatis
#XBUT
txtUp=Naik
#XBUT
txtDown=Turun

#XTOL
txtTransformationHeader=Proyeksi
#XTOL
editTransformation=Edit
#XTOL
primaryKeyToolip=Kunci


#XMSG
rftdFilter=Filter
#XMSG
rftdFilterColumnCount=Sumber: {0}({1})
#XTOL
rftdFilterColSearch=Pencarian
#XMSG
rftdFilterColNoData=Tidak ada kolom yang ditampilkan
#XMSG
rftdFilteredColNoExps=Tidak ada pernyataan filter
#XMSG
rftdFilterSelectedColTxt=Tambahkan Filter untuk
#XMSG
rftdFilterTxt=Filter tersedia untuk
#XBUT
rftdFilterSelectedAddColExp=Tambahkan Pernyataan
#YINS
rftdFilterNoSelectedCol=Pilih kolom untuk menambah filter.
#XMSG
rftdFilterExp=Filter Pernyataan
#XMSG
rftdFilterNotAllowedColumn=Penambahan filter tidak didukung pada kolom ini.
#XMSG
rftdFilterNotAllowedHead=Kolom Tidak Didukung
#XMSG
rftdFilterNoExp=Tidak ada filter yang ditentukan
#XTOL
rftdfilteredTt=Difilter
#XTOL
rftdremoveexpTt=Hapus pernyataan filter
#XTOL
validationMessageTt=Pesan Validasi
#XTOL
rftdFilterDateInp=Pilih tanggal
#XTOL
rftdFilterDateTimeInp=Pilih waktu tanggal
#XTOL
rftdFilterTimeInp=Pilih waktu
#XTOL
rftdFilterInp=Masukkan nilai
#XMSG
rftdFilterValidateEmptyMsg={0} pernyataan filter pada kolom {1} kosong
#XMSG
rftdFilterValidateInvalidNumericMsg={0} pernyataan filter pada kolom {1} berisi nilai numerik yang tidak valid
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Pernyataan filter harus berisi nilai numerik yang valid
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Jika skema objek target telah berubah, gunakan fungsi “Petakan ke Objek Target yang Ada” di halaman utama untuk menyesuaikan perubahan, dan petakan kembali objek target dengan sumbernya lagi.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Jika tabel target sudah ada dan pemetaan menyertakan perubahan skema, Anda harus mengubah tabel target yang sesuai sebelum menyebarkan aliran replikasi.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Jika pemetaan Anda memerlukan perubahan skema, Anda harus mengubah tabel target yang sesuai sebelum menyebarkan aliran replikasi.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Kolom yang tidak didukung berikut dilewati dari definisi sumber: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Kolom yang tidak didukung berikut dilewati dari definisi target: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Objek berikut tidak didukung karena dipaparkan untuk pemakaian: {0} {1} {0} {0} Untuk menggunakan tabel di aliran replikasi, penggunaan semantik (di pengaturan tabel) tidak boleh ditetapkan sebagai {2}Himpunan Data Analitis{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Objek target tidak dapat digunakan karena dipaparkan untuk pemakaian: {0} {0}  Untuk menggunakan tabel di aliran replikasi, penggunaan semantik (di pengaturan tabel) harus ditetapkan sebagai {1}Himpunan Data Analitis{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Sudah ada objek target dengan nama ini. Namun, objek target ini tidak dapat digunakan karena dipaparkan untuk pemakaian: {0} {0} Untuk menggunakan tabel di aliran replikasi, penggunaan semantik (di pengaturan tabel) tidak boleh ditetapkan sebagai {1}Himpunan Data Analitis{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Objek dengan nama ini sudah ada di target. {0}Namun, objek ini tidak dapat digunakan sebagai objek target untuk aliran replikasi ke repositori lokal, karena objek ini bukan tabel lokal.
#XMSG:
targetAutoRenameUpdated=Kolom target telah dinamai ulang.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Kolom target telah dinamai ulang agar direplikasi di Google BigQuery. Penamaan ulang ini dilakukan karena salah satu alasan berikut:{0} {1}{2}Nama kolom yang dicadangkan{3}{2}Karakter yang tidak didukung{3}{2}Prefiks yang dicadangkan{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Kolom target telah dinamai ulang agar direplikasi di Confluent. Penamaan ulang ini dilakukan karena salah satu alasan berikut:{0} {1}{2}Nama kolom yang dicadangkan{3}{2}Karakter yang tidak didukung{3}{2}Prefiks yang dicadangkan{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Kolom target telah dinamai ulang agar direplikasi ke target. Penamaan ulang ini dilakukan karena salah satu alasan berikut:{0} {1}{2}Karakter yang tidak didukung{3}{2}Prefiks yang dicadangkan{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Kolom target telah dinamai ulang agar direplikasi di target. Penamaan ulang ini dilakukan karena salah satu alasan berikut:{0} {1}{2}Nama kolom yang dicadangkan{3}{2}Karakter yang tidak didukung{3}{2}Prefiks yang dicadangkan{3}{4}
#XMSG:
targetAutoDataType=Tipe data target telah diubah.
#XMSG:
targetAutoDataTypeDesc=Tipe data target telah diubah menjadi {0} karena tipe data sumber tidak didukung di Google BigQuery.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Tipe data target telah diubah menjadi {0} karena tipe data sumber tidak didukung di koneksi target.
#XMSG
projectionGBQUnableToCreateKey=Kunci utama tidak akan dibuat.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=Di Google BigQuery, maksimum 16 kunci utama didukung, tetapi objek sumber memiliki angka yang lebih besar dari kunci utama. Tidak ada kunci utama yang akan dibuat di objek target.
#XMSG
HDLFNoKeyError=Tentukan satu atau beberapa kolom sebagai kunci utama.
#XMSG
HDLFNoKeyErrorDescription=Untuk mereplikasi objek, Anda harus menentukan satu atau beberapa kolom sebagai kunci utama.
#XMSG
HDLFNoKeyErrorExistingTarget=Tentukan satu atau beberapa kolom sebagai kunci utama.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Untuk mereplikasi data ke objek target yang sudah ada, objek target harus memiliki satu atau beberapa kolom yang ditentukan sebagai kunci utama. {0} {0} Anda memiliki opsi berikut untuk menentukan satu atau beberapa kolom sebagai kunci utama: {0}{0}{1} Gunakan editor tabel lokal untuk mengubah objek target yang sudah ada. Kemudian muat ulang aliran replikasi.{0}{0}{1} Namai ulang objek target di aliran replikasi. Penamaan ulang ini akan membuat objek baru segera setelah eksekusi dimulai. Setelah menamai ulang, Anda dapat menentukan satu atau beberapa kolom sebagai kunci utama dalam proyeksi.{0}{0}{1} Petakan objek ke objek target lain yang sudah ada, yang satu atau beberapa kolomnya sudah ditentukan sebagai kunci utama.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Kunci utama diubah.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=Dibandingkan dengan objek sumber, Anda telah menentukan kolom yang berbeda sebagai kunci utama untuk objek target. Pastikan bahwa kolom ini mengidentifikasi semua baris secara unik guna menghindari kemungkinan rusaknya data saat mereplikasi data nanti. {0} {0} Di objek sumber, kolom berikut ini ditentukan sebagai kunci utama: {0} {1}
#XMSG
duplicateDPIDColumns=Namai ulang kolom target.
#XMSG
duplicateDPIDDColumnsDesc1=Nama kolom target ini dicadangkan untuk kolom teknis. Masukkan nama yang lain untuk menyimpan proyeksi.
#XMSG:
targetAutoRenameDPID=Kolom target telah dinamai ulang.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Kolom target telah dinamai ulang untuk mengizinkan replikasi dari sumber ABAP tanpa kunci. Penamaan ulang ini dilakukan karena salah satu alasan berikut:{0} {1}{2}Nama kolom yang dicadangkan{3}{2}Karakter yang tidak didukung{3}{2}Prefiks yang dicadangkan{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle=Pengaturan Target {0}
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle=Pengaturan Sumber {0}
#XBUT
connectionSettingSave=Simpan
#XBUT
connectionSettingCancel=Batalkan
#XBUT: Button to keep the object level settings
txtKeep=Simpan
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Timpa
#XFLD
targetConnectionThreadlimit=Batas Utas Target untuk Muatan Awal (1-100)
#XFLD
connectionThreadLimit=Batas Utas Sumber untuk Muatan Awal (1-100)
#XFLD
maxConnection=Batas Utas Replikasi (1-100)
#XFLD
kafkaNumberOfPartitions=Jumlah Partisi
#XFLD
kafkaReplicationFactor=Faktor Replikasi
#XFLD
kafkaMessageEncoder=Pembuat Kode Pesan
#XFLD
kafkaMessageCompression=Kompresi Pesan
#XFLD
fileGroupDeltaFilesBy=Kelompokkan Delta berdasarkan
#XFLD
fileFormat=Tipe File
#XFLD
csvEncoding=Pembuatan Kode CSV
#XFLD
abapExitLbl=ABAP Exit
#XFLD
deltaPartition=Jumlah Utas Objek untuk Muatan Delta (1-10)
#XFLD
clamping_Data=Kegagalan Akibat Data Terpotong
#XFLD
fail_On_Incompatible=Gagal karena Data Tidak Kompatibel
#XFLD
maxPartitionInput=Jumlah Partisi Maksimal
#XFLD
max_Partition=Tentukan Jumlah Partisi Maksimal
#XFLD
include_SubFolder=Sertakan SubFolder
#XFLD
fileGlobalPattern=Pola Global untuk Nama File
#XFLD
fileCompression=Kompresi File
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Pembatas File
#XFLD
fileIsHeaderIncluded=Header File
#XFLD
fileOrient=Orient
#XFLD
gbqWriteMode=Mode Penulisan
#XFLD
suppressDuplicate=Tekan Duplikat
#XFLD
apacheSpark=Aktifkan Kompatibilitas Apache Spark
#XFLD
clampingDatatypeCb=Kunci Tipe Data ke Batas Titik Desimal yang Mengambang
#XFLD
overwriteDatasetSetting=Timpa Pengaturan Target di Tingkat Objek
#XFLD
overwriteSourceDatasetSetting=Timpa Pengaturan Sumber di Tingkat Objek
#XMSG
kafkaInvalidConnectionSetting=Masukkan angka antara {0} dan {1}.
#XMSG
MinReplicationThreadErrorMsg=Masukkan angka yang lebih besar dari {0}.
#XMSG
MaxReplicationThreadErrorMsg=Masukkan angka yang lebih rendah dari {0}.
#XMSG
DeltaThreadErrorMsg=Masukkan nilai antara 1 dan 10.
#XMSG
MaxPartitionErrorMsg=Masukkan nilai antara 1 <= x <= 2147483647. Nilai default adalah 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Masukkan bilangan bulat antara {0} dan {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Gunakan Faktor Replikasi Pialang
#XFLD
serializationFormat=Format Serialisasi
#XFLD
compressionType=Tipe Kompresi
#XFLD
schemaRegistry=Gunakan Pendaftaran Skema
#XFLD
subjectNameStrat=Strategi Nama Subjek
#XFLD
compatibilityType=Tipe Kompatibilitas
#XFLD
confluentTopicName=Nama Topik
#XFLD
confluentRecordName=Nama Catatan
#XFLD
confluentSubjectNamePreview=Pratinjau Nama Subjek
#XMSG
serializationChangeToastMsgUpdated2=Format serialisasi berubah menjadi JSON karena registri skema tidak diaktifkan. Untuk mengubah format serialisasi kembali ke AVRO, Anda harus mengaktifkan registri skema terlebih dahulu.
#XBUT
confluentTopicNameInfo=Nama topik selalu berdasarkan nama objek target. Anda dapat mengubahnya dengan menamai ulang objek target.
#XMSG
emptyRecordNameValidationHeaderMsg=Masukkan nama catatan.
#XMSG
emptyPartionHeader=Masukkan jumlah partisi.
#XMSG
invalidPartitionsHeader=Masukkan jumlah partisi yang valid.
#XMSG
invalidpartitionsDesc=Masukkan angka antara 1 sampai 200.000.
#XMSG
emptyrFactorHeader=Masukkan faktor replikasi.
#XMSG
invalidrFactorHeader=Masukkan faktor replikasi yang valid.
#XMSG
invalidrFactorDesc=Masukkan angka antara 1 sampai 32.767.
#XMSG
emptyRecordNameValidationDescMsg=Jika format serialisasi "AVRO" digunakan, hanya karakter berikut yang didukung:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(tanda hubung bawah)
#XMSG
validRecordNameValidationHeaderMsg=Masukkan nama catatan yang valid.
#XMSG
validRecordNameValidationDescMsgUpdated=Apabila format serialisasi "AVRO" digunakan, nama catatan hanya boleh terdiri dari karakter alfanumerik (A-Z, a-z, 0-9) dan garis bawah (_). Nama tersebut harus dimulai dengan huruf atau garis bawah.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=“Jumlah Utas Objek untuk Muatan Delta” dapat diatur segera setelah satu atau beberapa objek memiliki tipe muatan “Awal dan Delta”.
#XMSG
invalidTargetName=Nama kolom tidak valid
#XMSG
invalidTargetNameDesc=Nama kolom target hanya boleh terdiri dari karakter alfanumerik (A-Z, a-z, 0-9) dan garis bawah (_).
#XFLD
consumeOtherSchema=Gunakan Versi Skema yang Lain
#XFLD
ignoreSchemamissmatch=Abaikan Ketidakcocokan Skema
#XFLD
confleuntDatatruncation=Kegagalan Akibat Data Terpotong
#XFLD
isolationLevel=Tingkat Isolasi
#XFLD
confluentOffset=Titik Awal
#XFLD
signavioGroupDeltaFilesByText=Tidak Ada
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=Tidak
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=Tidak

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Proyeksi
#XBUT
txtAdd=Tambahkan
#XBUT
txtEdit=Edit
#XMSG
transformationText=Tambahkan proyeksi untuk menyiapkan filter atau pemetaan.
#XMSG
primaryKeyRequiredText=Pilih kunci utama dengan Konfigurasikan Skema.
#XFLD
lblSettings=Pengaturan
#XFLD
lblTargetSetting={0}: Pengaturan Target
#XMSG
@csvRF=Pilih file yang berisi definisi skema yang ingin Anda terapkan ke semua file di dalam folder.
#XFLD
lblSourceColumns=Kolom Sumber
#XFLD
lblJsonStructure=Struktur JSON
#XFLD
lblSourceSetting={0}: Pengaturan Sumber
#XFLD
lblSourceSchemaSetting={0}: Pengaturan Skema Sumber
#XBUT
messageSettings=Pengaturan Pesan
#XFLD
lblPropertyTitle1=Properti Objek
#XFLD
lblRFPropertyTitle=Properti Aliran Replikasi
#XMSG
noDataTxt=Tidak ada kolom untuk ditampilkan.
#XMSG
noTargetObjectText=Tidak ada objek target yang dipilih.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Kolom Target
#XMSG
searchColumns=Kolom Pencarian
#XTOL
cdcColumnTooltip=Kolom untuk Pemindaian Delta
#XMSG
sourceNonDeltaSupportErrorUpdated=Objek sumber tidak mendukung pemindaian delta.
#XMSG
targetCDCColumnAdded=2 kolom target ditambah untuk pemindaian delta.
#XMSG
deltaPartitionEnable=Batas Utas Objek untuk Muatan Delta ditambahkan ke pengaturan sumber.
#XMSG
attributeMappingRemovalTxt=Menghapus pemetaan yang tidak valid yang tidak didukung untuk objek target yang baru.
#XMSG
targetCDCColumnRemoved=2 kolom target yang digunakan untuk pemindaian delta dihapus.
#XMSG
replicationLoadTypeChanged=Tipe muatan diubah menjadi "Awal dan Delta".
#XMSG
sourceHDLFLoadTypeError=Ubah tipe muatan menjadi "Awal dan Delta".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Untuk mereplikasi objek dari koneksi sumber dengan tipe koneksi SAP HANA Cloud, Data Lake Files ke SAP Datasphere, Anda harus menggunakan tipe muatan "awal dan delta".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Aktifkan Pemindaian Delta.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Untuk mereplikasi objek dari koneksi sumber dengan tipe koneksi SAP HANA Cloud, Data Lake Files ke SAP Datasphere, Anda harus mengaktifkan pemindaian delta.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Ubah Objek target.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Objek target tidak dapat digunakan karena pemindaian delta dinonaktifkan. Anda dapat menamai ulang objek target (yang mengizinkan pembuatan objek baru dengan pemindaian delta) atau memetakannya ke objek yang sudah ada dengan pemindaian delta yang diaktifkan.
#XMSG
deltaPartitionError=Masukkan jumlah utas objek yang valid untuk muatan delta.
#XMSG
deltaPartitionErrorDescription=Masukkan nilai antara 1 dan 10.
#XMSG
deltaPartitionEmptyError=Masukkan jumlah utas objek untuk muatan delta.
#XFLD
@lblColumnDescription=Deskripsi
#XMSG
@lblColumnDescriptionText1=Untuk tujuan teknis - penanganan catatan duplikat yang disebabkan oleh masalah selama replikasi objek sumber berbasis ABAP yang tidak memiliki kunci utama.
#XFLD
storageType=Penyimpanan
#XFLD
skipUnmappedColLbl=Lewati Kolom yang Tidak Dipetakan
#XFLD
abapContentTypeLbl=Tipe Konten
#XFLD
autoMergeForTargetLbl=Gabungkan Data secara Otomatis
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Umum
#XFLD
lblBusinessName=Nama Bisnis
#XFLD
lblTechnicalName=Nama Teknis
#XFLD
lblPackage=Paket
#XFLD
statusPanel=Status Eksekusi
#XBTN: Schedule dropdown menu
SCHEDULE=Jadwal
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Edit Jadwal
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Hapus Permanen Jadwal
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Buat Jadwal
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Gagal menjadwalkan pemeriksaan validasi
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Jadwal tidak dapat dibuat karena aliran replikasi saat ini sedang disebarkan.{0}Silakan tunggu hingga aliran replikasi telah disebarkan.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Untuk aliran replikasi yang berisi objek dengan tipe muatan "Awal dan Delta", tidak ada jadwal yang dapat dibuat.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Untuk aliran replikasi yang berisi objek dengan tipe muatan "Awal dan Delta/Delta Saja", tidak ada jadwal yang dapat dibuat.
#XFLD : Scheduled popover
SCHEDULED=Dijadwalkan
#XFLD
CREATE_REPLICATION_TEXT=Buat Aliran Replikasi
#XFLD
EDIT_REPLICATION_TEXT=Edit Aliran Replikasi
#XFLD
DELETE_REPLICATION_TEXT=Hapus Permanen Aliran Replikasi
#XFLD
REFRESH_FREQUENCY=Frekuensi
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Aliran replikasi tidak dapat disebarkan karena jadwal{0} yang sudah ada belum mendukung tipe muatan "Awal dan Delta".{0}{0}Untuk menyebarkan aliran replikasi, Anda harus menetapkan tipe muatan dari semua objek{0} menjadi "Hanya awal". Atau, Anda dapat menghapus permanen jadwal, menyebarkan aliran replikasi {0}, lalu memulai eksekusi baru. Tindakan ini menghasilkan eksekusi terus-menerus {0}, yang juga mendukung objek dengan tipe muatan "Awal dan Delta".
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Aliran replikasi tidak dapat disebarkan karena jadwal{0} yang sudah ada belum mendukung tipe muatan "Awal dan Delta/Delta Saja".{0}{0}Untuk menyebarkan aliran replikasi, Anda harus menetapkan tipe muatan dari semua objek{0} menjadi "Hanya awal". Atau, Anda dapat menghapus permanen jadwal, menyebarkan aliran replikasi{0}, lalu memulai eksekusi baru. Tindakan ini menghasilkan eksekusi terus menerus{0}, yang juga mendukung objek dengan tipe muatan "Awal dan Delta/Delta Saja".
#XMSG
SCHEDULE_EXCEPTION=Gagal mendapatkan rincian jadwal
#XFLD: Label for frequency column
everyLabel=Setiap
#XFLD: Plural Recurrence text for Hour
hoursLabel=Jam
#XFLD: Plural Recurrence text for Day
daysLabel=Hari
#XFLD: Plural Recurrence text for Month
monthsLabel=Bulan
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Menit
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Gagal mendapatkan informasi mengenai kemungkinan jadwal.
#XFLD :Paused field
PAUSED=Dijeda
#XMSG
navToMonitoring=Buka di Pemantau Aliran
#XFLD
statusLbl=Status
#XFLD
lblLastRunExecuted=Waktu Mulai Eksekusi Terakhir
#XFLD
lblLastExecuted=Eksekusi Terakhir
#XFLD: Status text for Completed
statusCompleted=Selesai
#XFLD: Status text for Running
statusRunning=Sedang Dieksekusi
#XFLD: Status text for Failed
statusFailed=Gagal
#XFLD: Status text for Stopped
statusStopped=Berhenti
#XFLD: Status text for Stopping
statusStopping=Menghentikan
#XFLD: Status text for Active
statusActive=Aktif
#XFLD: Status text for Paused
statusPaused=Dijeda
#XFLD: Status text for not executed
lblNotExecuted=Belum Dieksekusi
#XFLD
messagesSettings=Pengaturan Pesan
#XTOL
@validateModel=Pesan Validasi
#XTOL
@hierarchy=Hierarki
#XTOL
@columnCount=Jumlah Kolom
#XMSG
VAL_PACKAGE_CHANGED=Anda telah menetapkan objek ini ke paket "{1}". Klik "Simpan" untuk mengonfirmasi dan memvalidasi perubahan ini. Perhatikan bahwa penetapan ke paket tidak dapat dibatalkan di editor ini setelah Anda simpan.
#XMSG
MISSING_DEPENDENCY=Dependensi objek ''{0}'' tidak dapat diselesaikan dalam paket "{1}".
#XFLD
deltaLoadInterval=Interval Muatan Delta
#XFLD
lblHour=Jam (0-24)
#XFLD
lblMinutes=Menit (0-59)
#XMSG
maxHourOrMinErr=Masukkan nilai antara 0 dan {0}
#XMSG
maxDeltaInterval=Nilai maksimum interval muatan delta adalah 24 jam.{0}Ubah nilai menit atau nilai jam dengan benar.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Jalur Kontainer Target
#XFLD
confluentSubjectName=Nama Subjek
#XFLD
confluentSchemaVersion=Versi Skema
#XFLD
confluentIncludeTechKeyUpdated=Sertakan Kunci Teknis
#XFLD
confluentOmitNonExpandedArrays=Lewati Larik yang Tidak Diperluas 
#XFLD
confluentExpandArrayOrMap=Perluas Larik atau Peta
#XCOL
confluentOperationMapping=Pemetaan Operasi
#XCOL
confluentOpCode=Kode Operasi
#XFLD
confluentInsertOpCode=Sisipkan
#XFLD
confluentUpdateOpCode=Perbarui
#XFLD
confluentDeleteOpCode=Hapus Permanen
#XFLD
expandArrayOrMapNotSelectedTxt=Tidak Dipilih
#XFLD
confluentSwitchTxtYes=Ya
#XFLD
confluentSwitchTxtNo=Tidak
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Kesalahan
#XTIT
executeWarning=Peringatan
#XMSG
executeunsavederror=Simpan aliran replikasi Anda sebelum mengeksekusinya.
#XMSG
executemodifiederror=Ada perubahan yang belum disimpan dalam aliran replikasi. Harap simpan aliran replikasi.
#XMSG
executeundeployederror=Anda harus menyebarkan aliran replikasi Anda sebelum dapat mengeksekusinya.
#XMSG
executedeployingerror=Harap tunggu penyebaran selesai.
#XMSG
msgRunStarted=Eksekusi dimulai
#XMSG
msgExecuteFail=Gagal mengeksekusi aliran replikasi
#XMSG
titleExecuteBusy=Silakan tunggu.
#XMSG
msgExecuteBusy=Kami sedang mempersiapkan data Anda untuk mengeksekusi aliran replikasi.
#XTIT
executeConfirmDialog=Peringatan
#XMSG
msgExecuteWithValidations=Aliran replikasi memiliki kesalahan validasi. Mengeksekusi aliran replikasi dapat mengakibatkan kegagalan.
#XMSG
msgRunDeployedVersion=Terdapat perubahan untuk disebarkan. Versi aliran replikasi yang disebarkan terakhir akan dieksekusi. Apakah Anda ingin melanjutkan?
#XBUT
btnExecuteAnyway=Tetap Eksekusi
#XBUT
btnExecuteClose=Tutup
#XBUT
loaderClose=Tutup
#XTIT
loaderTitle=Memuat
#XMSG
loaderText=Mengambil rincian dari server
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Aliran replikasi ke koneksi target non-SAP ini tidak dapat dimulai
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=karena tidak ada volume keluar yang tersedia untuk bulan ini.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Administrator dapat meningkatkan blok Keluaran Premium untuk penyewa
#XMSG
premiumOutBoundRFAdminErrMsgPart2=ini, sehingga volume keluar tersedia untuk bulan ini.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Kesalahan
#XTIT
deployInfo=Informasi
#XMSG
deployCheckFailException=Pengecualian terjadi selama penyebaran
#XMSG
deployGBQFFDisabled=Saat ini tidak mungkin untuk melakukan penyebaran aliran replikasi dengan koneksi target ke Google BigQuery karena sedang dilakukan pemeliharaan pada fungsi ini.
#XMSG
deployKAFKAFFDisabled=Saat ini tidak mungkin untuk melakukan penyebaran aliran replikasi dengan koneksi target ke Apache Kafka karena sedang dilakukan pemeliharaan pada fungsi ini.
#XMSG
deployConfluentDisabled=Saat ini tidak mungkin untuk melakukan penyebaran aliran replikasi dengan koneksi target ke Confluent Kafka karena sedang dilakukan pemeliharaan pada fungsi ini.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Untuk objek target berikut, nama tabel pemindaian delta sudah digunakan oleh tabel lain dalam repositori: {0} Anda harus menamai ulang objek target ini untuk memastikan bahwa nama tabel pemindaian delta yang terkait bersifat unik sebelum Anda dapat menyebarkan aliran replikasi.
#XMSG
deployDWCSourceFFDisabled=Saat ini tidak mungkin untuk melakukan penyebaran aliran replikasi dengan SAP Datasphere sebagai sumbernya karena sedang dilakukan pemeliharaan pada fungsi ini.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Saat ini tidak mungkin untuk melakukan penyebaran aliran replikasi yang berisi tabel lokal yang diaktifkan delta sebagai objek sumber karena sedang dilakukan pemeliharaan pada fungsi ini.
#XMSG
deployHDLFSourceFFDisabled=Saat ini tidak mungkin untuk melakukan penyebaran aliran replikasi yang memiliki koneksi sumber dengan tipe koneksi SAP HANA Cloud, Data Lake Files karena sedang dilakukan pemeliharaan.
#XMSG
deployObjectStoreAsSourceFFDisabled=Saat ini penyebaran aliran replikasi yang memiliki penyedia penyimpanan cloud sebagai sumber tidak memungkinkan untuk dilakukan.
#XMSG
deployConfluentSourceFFDisabled=Saat ini penyebaran aliran replikasi yang memiliki Confluent Kafka sebagai sumber tidak dapat dilakukan karena kami sedang menjalankan pemeliharaan pada fungsi ini.
#XMSG
deployMaxDWCNewTableCrossed=Jika aliran replikasi besar, tindakan "simpan dan sebarkan" tidak dapat dilakukan secara bersamaan. Silakan simpan aliran replikasi Anda terlebih dahulu, lalu terapkan.
#XMSG
deployInProgressInfo=Penyebaran sedang diproses.
#XMSG
deploySourceObjectInUse=Objek sumber {0} sudah digunakan di aliran replikasi {1}.
#XMSG
deployTargetSourceObjectInUse=Objek sumber {0} sudah digunakan di aliran replikasi {1}. Objek target {2} sudah digunakan di aliran replikasi {3}.
#XMSG
deployReplicationFlowCheckError=Terjadi kesalahan saat memverifikasi aliran replikasi: {0}
#XMSG
preDeployTargetObjectInUse=Objek target {0} sudah digunakan dalam aliran replikasi {1}, dan Anda tidak dapat memiliki objek target yang sama dalam dua aliran replikasi yang berbeda. Pilih objek target lain lalu coba lagi.
#XMSG
runInProgressInfo=Aliran replikasi sedang dieksekusi.
#XMSG
deploySignavioTargetFFDisabled=Saat ini tidak mungkin untuk melakukan penyebaran aliran replikasi dengan SAP Signavio sebagai targetnya karena sedang dilakukan pemeliharaan pada fungsi ini.
#XMSG
deployHanaViewAsSourceFFDisabled=Penyebaran aliran replikasi yang menggunakan tampilan sebagai objek sumber untuk koneksi sumber yang dipilih saat ini belum didukung. Coba lagi nanti.
#XMSG
deployMsOneLakeTargetFFDisabled=Saat ini tidak mungkin untuk melakukan penyebaran aliran replikasi dengan MS OneLake sebagai targetnya karena sedang dilakukan pemeliharaan pada fungsi ini.
#XMSG
deploySFTPTargetFFDisabled=Saat ini tidak mungkin untuk melakukan penyebaran aliran replikasi dengan SFTP sebagai targetnya karena sedang dilakukan pemeliharaan pada fungsi ini.
#XMSG
deploySFTPSourceFFDisabled=Saat ini tidak mungkin untuk melakukan penyebaran aliran replikasi dengan SFTP sebagai sumbernya karena sedang dilakukan pemeliharaan pada fungsi ini.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Nama Teknis
#XFLD
businessNameInRenameTarget=Nama Bisnis
#XTOL
renametargetDialogTitle=Namai Ulang Objek Target
#XBUT
targetRenameButton=Ubah Nama
#XBUT
targetRenameCancel=Batalkan
#XMSG
mandatoryTargetName=Anda harus memasukkan nama.
#XMSG
dwcSpecialChar=_(garis bawah) adalah satu-satunya karakter khusus yang diizinkan.
#XMSG
dwcWithDot=Nama tabel target dapat berisi huruf Latin, angka, garis bawah (_), dan titik (.). Karakter pertama harus berupa huruf, angka, atau garis bawah (bukan titik).
#XMSG
nonDwcSpecialChar=Karakter khusus yang diizinkan adalah _(garis bawah) -(tanda hubung) .(titik)
#XMSG
firstUnderscorePattern=Nama tidak boleh diawali dengan _(garis bawah)

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: Tampilkan Pernyataan SQL untuk Membuat Tabel
#XMSG
sqlDialogMaxPKWarning=Di Google BigQuery, mendukung maksimum 16 kunci utama, tetapi objek sumber memiliki jumlah kunci utama yang lebih besar daripada itu. Oleh karena itu, dalam pernyataan ini, tidak ada kunci utama yang ditetapkan.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Satu atau beberapa kolom sumber memiliki tipe data yang tidak dapat ditetapkan sebagai kunci utama di Google BigQuery. Oleh karena itu, tidak ada kunci utama yang akan ditetapkan dalam kasus ini. Di Google BigQuery, hanya tipe data berikut yang dapat memiliki kunci utama: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Salin dan Tutup
#XBUT
closeDDL=Tutup
#XMSG
copiedToClipboard=Disalin ke clipboard


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Objek ''{0}'' tidak dapat menjadi bagian dari rantai tugas karena tidak memiliki titik akhir (karena objek ini termasuk objek dengan tipe muatan Awal dan Delta/Delta Saja).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Objek ''{0}'' tidak dapat menjadi bagian dari rantai tugas karena tidak memiliki titik akhir (karena objek ini termasuk objek dengan tipe muatan Awal dan Delta).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objek "{0}" tidak dapat ditambahkan ke rantai tugas.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Terdapat objek target yang belum disimpan. Silakan simpan lagi.{0}{0} Perilaku fitur ini telah berubah: Sebelumnya, objek target hanya dibuat di lingkungan target saat aliran replikasi disebarkan.{0} Kini, objek sudah dibuat saat aliran replikasi disimpan. Aliran replikasi Anda dibuat sebelum perubahan ini dan berisi objek baru.{0} Anda perlu menyimpan aliran replikasi sekali lagi sebelum menyebarkannya agar objek baru disertakan dengan benar.
#XMSG
confirmChangeContentTypeMessage=Anda akan mengubah tipe konten. Jika Anda melanjutkannya, tindakan ini akan menghapus permanen semua proyeksi yang ada.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Nama Subjek
#XFLD
schemaDialogVersionName=Versi Skema
#XFLD
includeTechKey=Sertakan Kunci Teknis
#XFLD
segementButtonFlat=Datar
#XFLD
segementButtonNested=Bertingkat
#XMSG
subjectNamePlaceholder=Cari Nama Subjek

#XMSG
@EmailNotificationSuccess=Konfigurasi pemberitahuan email runtime disimpan.

#XFLD
@RuntimeEmailNotification=Pemberitahuan Email Runtime

#XBTN
@TXT_SAVE=Simpan


