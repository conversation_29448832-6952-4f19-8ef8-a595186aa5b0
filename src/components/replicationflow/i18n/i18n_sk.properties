#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Tok replikácie

#XFLD: Edit Schema button text
editSchema=Upraviť schému

#XTIT : Properties heading
configSchema=Konfigurovať schému

#XFLD: save changed button text
applyChanges=Použiť zmeny


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Vybrať zdrojové pripojenie
#XFLD
sourceContainernEmptyText=Vybrať kontajner
#XFLD
targetConnectionEmptyText=Vybrať cieľové pripojenie
#XFLD
targetContainernEmptyText=Vybrať kontajner
#XFLD
sourceSelectObjectText=Vybrať zdrojový objekt
#XFLD
sourceObjectCount=Zdrojové objekty ({0})
#XFLD
targetObjectText=Cieľové objekty
#XFLD
confluentBrowseContext=Vybrať kontext
#XBUT
@retry=Znova
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Prebieha upgrade klienta.

#XTOL
browseSourceConnection=Prehľadávať zdrojové pripojenie
#XTOL
browseTargetConnection=Prehľadávať cieľové pripojenie
#XTOL
browseSourceContainer=Prehľadávať zdrojový kontajner
#XTOL
browseAndAddSourceDataset=Pridať zdrojové objekty
#XTOL
browseTargetContainer=Prehľadávať cieľový kontajner
#XTOL
browseTargetSetting=Prehľadávať nastavenia cieľa
#XTOL
browseSourceSetting=Prehľadávať nastavenia zdroja
#XTOL
sourceDatasetInfo=Informácie
#XTOL
sourceDatasetRemove=Odstrániť
#XTOL
mappingCount=Toto predstavuje celkový počet mapovaní/výrazov, ktoré nie sú založené na názve.
#XTOL
filterCount=Toto predstavuje celkový počet podmienok filtrovania.
#XTOL
loading=Načítava sa...
#XCOL
deltaCapture=Delta Capture
#XCOL
deltaCaptureTableName=Tabuľka Delta Capture
#XCOL
loadType=Typ načítania
#XCOL
deleteAllBeforeLoading=Odstrániť všetko pred načítaním
#XCOL
transformationsTab=Projekcie
#XCOL
settingsTab=Nastavenia

#XBUT
renameTargetObjectBtn=Premenovať cieľový objekt
#XBUT
mapToExistingTargetObjectBtn=Mapovať na existujúci cieľový objekt
#XBUT
changeContainerPathBtn=Zmeniť cestu kontajnera
#XBUT
viewSQLDDLUpdated=Zobraziť príkaz SQL Vytvoriť tabuľku
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Zdrojový objekt nepodporuje delta zaznamenávanie, ale vybratý cieľový objekt má povolenú možnosť delta zaznamenávanie.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Cieľový objekt nemožno použiť, pretože je povolené delta zaznamenávanie,{0}zatiaľ čo zdrojový objekt nepodporuje delta zaznamenávanie.{1}Môžete vybrať iný cieľový objekt, ktorý nepodporuje delta zaznamenávanie.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Cieľový objekt s týmto názvom už existuje. Nemožno ho však použiť,{0}pretože je povolené delta zachytávanie, zatiaľ čo zdrojový objekt{0}nepodporuje delta zaznamenávanie.{1}Môžete buď zadať názov existujúceho cieľového objektu, ktorý{0}nepodporuje delta zaznamenávanie, alebo zadať názov, ktorý ešte neexistuje.
#XBUT
copySQLDDLUpdated=Kopírovať príkaz SQL Vytvoriť tabuľku
#XMSG
targetObjExistingNoCDCColumnUpdated=Existujúce tabuľky v Google BigQuery musia zahŕňať nasledujúce stĺpce pre zadávanie zmenených dát (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Nasledujúce zdrojové objekty nie sú podporované, pretože nemajú primárny kľúč alebo používajú pripojenie, ktoré nespĺňa podmienky na získanie primárneho kľúča:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Možné riešenie nájdete v SAP KBA 3531135.
#XLST: load type list values
initial=Len iniciálne
@emailUpdateError=Chyba pri aktualizácii zoznamu oznámení e-mailom

#XLST
initialDelta=Iniciálne a delta

#XLST
deltaOnly=Len delta
#XMSG
confluentDeltaLoadTypeInfo=Pre zdroj Confluent Kafka je podporovaný iba typ zavedenia Initial a Delta.
#XMSG
confirmRemoveReplicationObject=Potvrdzujete, že chcete odstrániť vybratú replikáciu?
#XMSG
confirmRemoveReplicationTaskPrompt=Táto akcia odstráni existujúce replikácie. Chcete pokračovať?
#XMSG
confirmTargetConnectionChangePrompt=Táto akcia resetuje cieľové pripojenie, cieľový kontajner a odstráni všetky cieľové objekty. Chcete pokračovať?
#XMSG
confirmTargetContainerChangePrompt=Táto akcia resetuje cieľový kontajner a odstráni všetky cieľové objekty. Chcete pokračovať?
#XMSG
confirmRemoveTransformObject=Potvrdzujete, že chcete odstrániť projekciu {0}?
#XMSG
ErrorMsgContainerChange=Pri zmene cesty ku kontajneru sa vyskytla chyba.
#XMSG
infoForUnsupportedDatasetNoKeys=Nasledujúce zdrojové objekty nie sú podporované, pretože nemajú primárny kľúč:
#XMSG
infoForUnsupportedDatasetView=Nasledujúce zdrojové objekty typu Zobrazenia nie sú podporované:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Nasledujúci zdrojový objekt nie je podporovaný, pretože ide o SQL zobrazenie obsahujúce vstupné parametre:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Nasledujúce zdrojové objekty nie sú podporované, pretože je pre objekty deaktivovaná extrakcia:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Pre pripojenia Confluent sú jedinými povolenými formátmi serializácie AVRO a JSON. Nasledujúce objekty nie sú podporované, pretože používajú iný formát serializácie:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Nie je možné načítať schému pre nasledujúce objekty. Vyberte vhodný kontext alebo overte konfiguráciu registra schém
#XTOL: warning dialog header on deleting replication task
deleteHeader=Odstrániť
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Nastavenie Odstrániť všetky pred načítaním nie je podporované pre Google BigQuery.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Nastavenie Odstrániť všetky pred odstráni a znova vytvorí objekt (tému) pred každou replikáciou. Tým sa odstránia aj všetky priradené správy.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Nastavenie Odstrániť všetky pred načítaním nie je podporované pre tento cieľový typ.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Technický názov
#XCOL
connBusinessName=Podnikový názov
#XCOL
connDescriptionName=Popis
#XCOL
connType=Typ
#XMSG
connTblNoDataFoundtxt=Nenájdené žiadne pripojenia
#XMSG
connectionError=Pri načítavaní pripojení sa vyskytla chyba.
#XMSG
connectionCombinationUnsupportedErrorTitle=Kombinácia pripojenia nie je podporovaná
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Replikácia z {0} do {1} momentálne nie je podporovaná.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Kombinácia typov pripojenia nie je podporovaná
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Replikácia z pripojenia s typom pripojenia SAP HANA Cloud, Data Lake Files na {0} nie je podporovaná. Môžete replikovať iba do SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Vybrať
#XBUT
containerCancelBtn=Zrušiť
#XTOL
containerSelectTooltip=Vybrať
#XTOL
containerCancelTooltip=Zrušiť
#XMSG
containerContainerPathPlcHold=Cesta ku kontajneru
#XFLD
containerContainertxt=Kontajner
#XFLD
confluentContainerContainertxt=Kontext
#XMSG
infoMessageForSLTSelection=Ako kontajner je povolené iba ID /SLT/ID hromadného prenosu. Vyberte ID hromadného prenosu pod SLT (ak je k dispozícii) a kliknite na Odoslať.
#XMSG
msgFetchContainerFail=Pri načítavaní dátového kontajnera sa vyskytla chyba.
#XMSG
infoMessageForSLTHidden=Toto pripojenie nepodporuje priečinky SLT, takže sa nezobrazujú v zozname nižšie.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Vyberte kontajner, ktorý obsahuje podpriečinky.
#XMSG
sftpIncludeSubFolderText=Nepravda
#XMSG
sftpIncludeSubFolderTextNew=Nie

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Zatiaľ neexistuje žiadne mapovanie filtra)
#XMSG
failToFetchRemoteMetadata=Pri načítavaní metadát sa vyskytla chyba.
#XMSG
failToFetchData=Pri načítavaní existujúceho cieľa sa vyskytla chyba.
#XCOL
@loadType=Typ načítania
#XCOL
@deleteAllBeforeLoading=Odstrániť všetko pred načítaním

#XMSG
@loading=Načítava sa...
#XFLD
@selectSourceObjects=Vybrať zdrojové objekty
#XMSG
@exceedLimit=Naraz nemôžete importovať viac ako {0} objektov. Zrušte výber aspoň {1} objektov.
#XFLD
@objects=Objekty
#XBUT
@ok=OK
#XBUT
@cancel=Zrušiť
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Ďalej
#XBUT
btnAddSelection=Pridať výber
#XTOL
@remoteFromSelection=Odstrániť z výberu
#XMSG
@searchInForSearchField=Hľadať v {0}

#XCOL
@name=Technický názov
#XCOL
@type=Typ
#XCOL
@location=Miesto
#XCOL
@label=Podnikový názov
#XCOL
@status=Status

#XFLD
@searchIn=Hľadať v:
#XBUT
@available=K dispozícii
#XBUT
@selection=Výber

#XFLD
@noSourceSubFolder=Tabuľky a zobrazenia
#XMSG
@alreadyAdded=Už sa nachádza v diagrame
#XMSG
@askForFilter=Existuje viac ako {0} položiek. Zadajte reťazec filtra na zníženie počtu položiek.
#XFLD: success label
lblSuccess=Úspešné
#XFLD: ready label
lblReady=Pripravené
#XFLD: failure label
lblFailed=Neúspešné
#XFLD: fetching status label
lblFetchingDetail=Načítavajú sa detaily

#XMSG Place holder text for tree filter control
filterPlaceHolder=Zadajte text na filtrovanie objektov najvyššej úrovne
#XMSG Place holder text for server search control
serverSearchPlaceholder=Zadajte text a stlačením klávesu Enter spustite vyhľadávanie
#XMSG
@deployObjects=Importuje sa {0} objektov...
#XMSG
@deployObjectsStatus=Počet objektov, ktoré boli importované: {0}. Počet objektov, ktoré nebolo možné importovať : {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Nepodarilo sa otvoriť prehliadač lokálneho ukladacieho priestoru.
#XMSG
@openRemoteSourceBrowserError=Nepodarilo sa načítať zdrojové objekty.
#XMSG
@openRemoteTargetBrowserError=Nepodarilo sa načítať cieľové objekty.
#XMSG
@validatingTargetsError=Pri overovaní cieľov sa vyskytla chyba.
#XMSG
@waitingToImport=Pripravené na import

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Maximálny počet objektov bol prekročený. Vyberte maximálne 500 objektov pre jeden tok replikácie.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Technický názov
#XFLD
sourceObjectBusinessName=Podnikový názov
#XFLD
sourceNoColumns=Počet stĺpcov
#XFLD
containerLbl=Kontajner

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Musíte vybrať zdrojové pripojenie pre tok replikácie.
#XMSG
validationSourceContainerNonExist=Musíte vybrať kontajner pre zdrojové pripojenie.
#XMSG
validationTargetNonExist=Musíte vybrať cieľové pripojenie pre tok replikácie.
#XMSG
validationTargetContainerNonExist=Musíte vybrať kontajner pre cieľové pripojenie.
#XMSG
validationTruncateDisabledForObjectTitle=Replikácia do objektových úložísk.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Replikácia do cloudového úložiska je možná len vtedy, ak je nastavená buď možnosť Odstrániť všetko pred načítaním, alebo cieľový objekt v cieli neexistuje.{0}{0} Ak chcete stále povoliť replikáciu pre objekty, pre ktoré nie je nastavená voľba Odstrániť všetky pred načítaním, pred spustením toku replikácie sa uistite, že cieľový objekt v systéme neexistuje.
#XMSG
validationTaskNonExist=V toku replikácie musíte mať aspoň jednu replikáciu.
#XMSG
validationTaskTargetMissing=Musíte mať cieľ replikácie so zdrojom: {0}
#XMSG
validationTaskTargetIsSAC=Vybraným cieľom je artefakt SAC: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Vybraný cieľ nie je podporovaná lokálna tabuľka: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Objekt s týmto názvom už v cieli existuje. Tento objekt však nemožno použiť ako cieľový objekt pre tok replikácie do lokálneho archívu, keďže nejde o lokálnu tabuľku.
#XMSG
validateSourceTargetSystemDifference=Pre tok replikácie musíte vybrať rôzne kombinácie zdrojového a cieľového pripojenia a kontajnera.
#XMSG
validateDuplicateSources=jedna alebo viaceré replikácie majú duplicitné názvy zdrojového objektu: {0}.
#XMSG
validateDuplicateTargets=jedna alebo viaceré replikácie majú duplicitné názvy cieľového objektu: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=Zdrojový objekt {0} nepodporuje delta zaznamenávanie, zatiaľ čo cieľový objekt {1} áno. Musíte odstrániť replikáciu.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Pre replikáciu s názvom cieľového objektu musíte vybrať typ načítania "Iniciálne a delta“ {0}.
#XMSG
validationAutoRenameTarget=Cieľové stĺpce boli premenované.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Bola pridaná automatická projekcia a nasledujúce cieľové stĺpce boli premenované, aby umožnili replikáciu na cieľ:{1}{1} {0} {1}{1}Toto je spôsobené jedným z nasledujúcich dôvodov:{1}{1}{2} Nepodporované znaky{1}{2} Rezervovaný prefix
#XMSG
validationAutoRenameTargetDescriptionUpdated=Bola pridaná automatická projekcia a nasledovné cieľové stĺpce boli premenované, aby sa umožnila replikácia do Google BigQuery:{1}{1} {0} {1}{1}Toto je spôsobené jedným z nasledujúcich dôvodov:{1}{1}{2} Rezervovaný názov stĺpca{1}{2} Nepodporované znaky{1}{2} Rezervovaný prefix
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Bola pridaná automatická projekcia a nasledujúce cieľové stĺpce boli premenované, aby umožnili replikácie na Confluent:{1}{1} {0} {1}{1}Toto je spôsobené jedným z nasledujúcich dôvodov:{1}{1}{2} Rezervovaný názov stĺpca{1}{2} Nepodporované znaky{1}{2} Rezervovaný prefix
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Bola pridaná automatická projekcia a nasledujúce cieľové stĺpce boli premenované, aby umožnili replikácie na cieľ:{1}{1} {0} {1}{1}Toto je spôsobené jedným z nasledujúcich dôvodov:{1}{1}{2} Rezervovaný názov stĺpca{1}{2} Nepodporované znaky{1}{2} Rezervovaný prefix
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Cieľový objekt bol premenovaný.
#XMSG
autoRenameInfoDesc=Cieľový objekt bol premenovaný, pretože obsahoval nepodporované znaky. Podporované sú iba nasledujúce znaky:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(bodka){0}{1}_(podčiarkovník){0}{1}-(pomlčka)
#XMSG
validationAutoTargetTypeConversion=Typy cieľových údajov boli zmenené.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Pre nasledujúce cieľové stĺpce boli zmenené typy cieľových údajov, pretože v službe Google BigQuery nie sú podporované typy zdrojových údajov:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Pre nasledujúce cieľové stĺpce boli cieľové typy údajov zmenené, pretože zdrojové typy údajov nie sú podporované v cieľovom pripojení:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Skráťte názvy cieľových stĺpcov.
#XMSG
validationMaxCharLengthGBQTargetDescription=V službe Google BigQuery môžu názvy stĺpcov využívať maximálne 300 znakov. Použite projekciu a skráťte nasledujúce názvy cieľových stĺpcov:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Primárne kľúče sa nevytvoria.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=V službe Google BigQuery je podporovaných maximálne 16 primárnych kľúčov. Zdrojový objekt má väčší počet primárnych kľúčov. V cieľovom objekte sa nevytvorí žiaden z primárnych kľúčov.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Jeden alebo viacero zdrojových stĺpcov obsahuje typy údajov, ktoré nemožno definovať ako primárne kľúče v službe Google BigQuery. V cieľovom objekte sa nevytvorí žiadny z primárnych kľúčov.{0}{0}Nasledovné cieľové typy údajov sú kompatibilné s typmi údajov služby Google BigQuery, pre ktoré možno definovať primárny kľúč:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Definujte jeden alebo viac stĺpcov ako primárny kľúč.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Na to musíte definovať jeden alebo viac stĺpcov ako dialógové okno so zdrojovou schémou primárneho kľúča.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Definujte jeden alebo viac stĺpcov ako primárny kľúč.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Musíte definovať jeden alebo viac stĺpcov ako primárny kľúč, ktoré zodpovedajú obmedzeniam primárneho kľúča pre váš zdrojový objekt. Prejdite na Konfigurovať schému vo vlastnostiach zdrojového objektu.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Zadajte platnú maximálnu hodnotu segmentu.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Maximálna hodnota segmentu musí byť ≥ 1 a ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Definujte jeden alebo viac stĺpcov ako primárny kľúč.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Ak chcete replikovať objekt, musíte definovať jeden alebo viac cieľových stĺpcov ako primárny kľúč. Použite na to projekciu.
#XMSG
validateHDLFNoPKExistingDatasetError=Definujte jeden alebo viac stĺpcov ako primárny kľúč.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Ak chcete replikovať údaje do existujúceho cieľového objektu, musí mať jeden alebo viac stĺpcov, ktoré sú definované ako primárny kľúč. {0} Na definovanie jedného alebo viacerých stĺpcov ako primárneho kľúča máte nasledujúce voľby: {0} {1} Na zmenu existujúceho cieľového objektu použite editor lokálnej tabuľky. Potom znova načítajte tok replikácie.{0}{1} Premenujte cieľový objekt v toku replikácie. Tým sa vytvorí nový objekt hneď po spustení chodu. Po premenovaní môžete definovať jeden alebo viac stĺpcov ako primárny kľúč v projekcii.{0}{1} Priraďte objekt k inému existujúcemu cieľovému objektu, v ktorom je jeden alebo viac stĺpcov už definovaných ako primárny kľúč.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Vybraný cieľ už existuje v repository: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Názvy tabuliek delta capture sú už používané inými tabuľkami v repository: {0} Tieto cieľové objekty musíte premenovať, aby ste sa uistili, že priradené názvy tabuliek delta capture sú jedinečné, skôr než budete môcť nasadiť tok replikácie.
#XMSG
validateConfluentEmptySchema=Definovať schému
#XMSG
validateConfluentEmptySchemaDescUpdated=Zdrojová tabuľka nemá schému. Vyberte Konfigurovať schému a definujte ju
#XMSG
validationCSVEncoding=Neplatné kódovanie CSV
#XMSG
validationCSVEncodingDescription=Kódovanie CSV úlohy nie je platné.
#XMSG
validateConfluentEmptySchema=Vyberte kompatibilný cieľový typ údajov
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Vyberte kompatibilný cieľový typ údajov
#XMSG
globalValidateTargetDataTypeDesc=Pri priradení stĺpcov sa vyskytla chyba. Prejdite do Projekcia a uistite sa, že všetky zdrojové stĺpce sú priradené s jedinečným stĺpcom, so stĺpcom, ktorý má kompatibilný typ údajov, a že všetky definované výrazy sú platné.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Duplicitné názvy stĺpcov.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Duplicitné názvy stĺpcov nie sú podporované. Na ich opravu použite dialógové okno projekcie. Nasledujúce cieľové objekty majú duplicitné názvy stĺpcov: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Duplicitné názvy stĺpcov.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Duplicitné názvy stĺpcov nie sú podporované. Nasledujúce cieľové objekty majú duplicitné názvy stĺpcov: {0}.
#XMSG
deltaOnlyLoadTypeTittle=V dátach môžu byť nekonzistencie.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Typ načítania Len delta nebude brať do úvahy zmeny vykonané v zdroji medzi posledným uložením a ďalším chodom.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Zmeniť typ načítania na „Iniciálne“.
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Replikovanie objektov založených na ABAP, ktoré nemajú primárny kľúč, je možné len pre typ načítania "Iba iniciálne".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Zakázať Delta Capture.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Ak chcete replikovať objekt, ktorý nemá primárny kľúč, pomocou zdrojového pripojenia typu ABAP, musíte najprv zakázať záznam delta pre túto tabuľku.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Zmeniť cieľový objekt.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Cieľový objekt nemožno použiť, pretože je povolené zaznamenávanie delta. Môžete buď premenovať cieľový objekt a potom vypnúť zaznamenávanie delta pre nový (premenovaný) objekt, alebo priradiť zdrojový objekt k cieľovému objektu, pre ktorý je zaznamenávanie delta zakázané.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Zmeniť cieľový objekt.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Cieľový objekt nemožno použiť, pretože nemá požadovaný technický stĺpec __load_package_id. Cieľový objekt môžete premenovať pomocou názvu, ktorý ešte neexistuje. Systém potom vytvorí nový objekt, ktorý má rovnakú definíciu ako zdrojový objekt a obsahuje technický stĺpec. Alternatívne môžete priradiť cieľový objekt k existujúcemu objektu, ktorý má požadovaný technický stĺpec (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Zmeniť cieľový objekt.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Cieľový objekt nemožno použiť, pretože nemá požadovaný technický stĺpec __load_record_id. Cieľový objekt môžete premenovať pomocou názvu, ktorý ešte neexistuje. Systém potom vytvorí nový objekt, ktorý má rovnakú definíciu ako zdrojový objekt a obsahuje technický stĺpec. Alternatívne môžete priradiť cieľový objekt k existujúcemu objektu, ktorý má požadovaný technický stĺpec (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Zmeniť cieľový objekt.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Cieľový objekt nemožno použiť, pretože typ údajov jeho technického stĺpca __load_record_id nie je "string(44)". Cieľový objekt môžete premenovať pomocou názvu, ktorý ešte neexistuje. Systém potom vytvorí nový objekt, ktorý má rovnakú definíciu ako zdrojový objekt a následne správny dátový typ. Alternatívne môžete priradiť cieľový objekt k existujúcemu objektu, ktorý má požadovaný technický stĺpec (__load_record_id) so správnym typom údajov.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Zmeniť cieľový objekt.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Cieľový objekt nemožno použiť, pretože má primárny kľúč, zatiaľ čo zdrojový objekt nemá žiadny. Cieľový objekt môžete premenovať pomocou názvu, ktorý ešte neexistuje. Systém potom vytvorí nový objekt, ktorý má rovnakú definíciu ako zdrojový objekt a teda žiadny primárny kľúč. Alternatívne môžete priradiť cieľový objekt k existujúcemu objektu, ktorý má požadovaný technický stĺpec (__load_package_id) a nemá primárny kľúč.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Zmeniť cieľový objekt.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Cieľový objekt nemožno použiť, pretože má primárny kľúč, zatiaľ čo zdrojový objekt nemá žiadny. Cieľový objekt môžete premenovať pomocou názvu, ktorý ešte neexistuje. Systém potom vytvorí nový objekt, ktorý má rovnakú definíciu ako zdrojový objekt a teda žiadny primárny kľúč. Alternatívne môžete priradiť cieľový objekt k existujúcemu objektu, ktorý má požadovaný technický stĺpec (__load_record_id) a nemá primárny kľúč.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Zmeniť cieľový objekt.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Cieľový objekt nemožno použiť, pretože typ údajov jeho technického stĺpca __load_package_id nie je "binary(>=256)". Cieľový objekt môžete premenovať pomocou názvu, ktorý ešte neexistuje. Systém potom vytvorí nový objekt, ktorý má rovnakú definíciu ako zdrojový objekt a následne správny dátový typ. Alternatívne môžete priradiť cieľový objekt k existujúcemu objektu, ktorý má požadovaný technický stĺpec (__load_package_id) so správnym typom údajov.
#XMSG
validationAutoRenameTargetDPID=Cieľové stĺpce boli premenované.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Odstráňte zdrojový objekt.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Zdrojový objekt nemá kľúčový stĺpec, ktorý v tomto kontexte nie je podporovaný.
#XMSG
validationAutoRenameTargetDPIDDescription=Bola pridaná automatická projekcia a nasledovné cieľové stĺpce boli premenované, aby sa umožnila replikácia zo zdroja ABAP bez kľúčov:{1}{1} {0} {1}{1}Toto je spôsobené jedným z nasledujúcich dôvodov:{1}{1}{2} Rezervovaný názov stĺpca{1}{2} Nepodporované znaky{1}{2} Rezervovaný prefix
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Replikácia do {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Ukladanie a nasadzovanie replikačných tokov, ktoré majú ako cieľ {0}, momentálne nie je možné, pretože na tejto funkcii vykonávame údržbu.
#XMSG
TargetColumnSkippedLTF=Cieľový stĺpec bol preskočený.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Cieľový stĺpec bol preskočený z dôvodu nepodporovaného dátového typu. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Časový stĺpec ako primárny kľúč.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Zdrojový objekt má ako primárny kľúč stĺpec času, ktorý v tomto kontexte nie je podporovaný.
#XMSG
validateNoPKInLTFTarget=Chýba primárny kľúč.
#XMSG
validateNoPKInLTFTargetDescription=Primárny kľúč nie je definovaný v cieli, čo v tomto kontexte nie je podporované.
#XMSG
validateABAPClusterTableLTF=Tabuľka klastrov ABAP.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Zdrojovým objektom je klastrová tabuľka ABAP, ktorá v tomto kontexte nie je podporovaná.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Zdá sa, že ste ešte nepridali žiadne údaje.
#YINS
welcomeText2=\ Ak chcete spustiť tok replikácie, vyberte pripojenie a zdrojový objekt na ľavej strane.

#XBUT
wizStep1=Vybrať zdrojové pripojenie
#XBUT
wizStep2=Vybrať zdrojový kontajner
#XBUT
wizStep3=Pridať zdrojové objekty

#XMSG
limitDataset=Maximálny počet objektov bol dosiahnutý. Odstráňte existujúce objekty, aby mohli byť pridané nové, alebo vytvorte nový tok replikácie.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Tok replikácie do tohto non-SAP cieľového pripojenia nemožno spustiť, pretože pre tento mesiac nie je k dispozícii žiaden objem výstupu.
#XMSG
premiumOutBoundRFAdminWarningMsg=Správca môže navýšiť bloky prémiového výstupu pre tohto nájomcu tým, že poskytne objem výstupu pre tento mesiac.
#XMSG
messageForToastForDPIDColumn2=Do cieľa bol pridaný nový stĺpec pre {0} objektov - potrebný na spracovanie duplicitných záznamov v spojení so zdrojovými objektmi založenými na ABAP, ktoré nemajú primárny kľúč.
#XMSG
PremiumInboundWarningMessage=V závislosti od počtu tokov replikácie a objemu údajov, ktoré sa majú replikovať, môžu prostriedky SAP HANA{0},potrebné na replikáciu údajov prostredníctvom {1}, presiahnuť dostupnú kapacitu pre vášho klienta.
#XMSG
PremiumInboundWarningMsg=V závislosti od počtu tokov replikácie a objemu údajov, ktoré sa majú replikovať, môžu prostriedky SAP HANA{0}, potrebné na replikáciu údajov prostredníctvom "{1}", presiahnuť dostupnú kapacitu pre vášho klienta.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Zadajte názov projekcie.
#XMSG
emptyTargetColumn=Zadajte názov cieľového stĺpca.
#XMSG
emptyTargetColumnBusinessName=Zadajte podnikový názov cieľového stĺpca.
#XMSG
invalidTransformName=Zadajte názov projekcie.
#XMSG
uniqueColumnName=Premenujte cieľový stĺpec.
#XMSG
copySourceColumnLbl=Skopírovať stĺpce zo zdrojového objektu
#XMSG
renameWarning=Pri premenovávaní cieľovej tabuľky sa uistite, že ste zvolili jedinečný názov. Ak tabuľka s novým názvom už v priestore existuje, použije definíciu tejto tabuľky.

#XMSG
uniqueColumnBusinessName=Premenovať obchodný názov cieľového stĺpca.
#XMSG
uniqueSourceMapping=Vyberte iný zdrojový stĺpec.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=Zdrojový stĺpec {0} už používajú nasledujúce cieľové stĺpce:{1}{1}{2}{1}{1} Pre tento cieľový stĺpec alebo pre ostatné cieľové stĺpce vyberte na uloženie projekcie zdrojový stĺpec, ktorý sa ešte nepoužíva.
#XMSG
uniqueColumnNameDescription=Zadaný názov cieľového stĺpca už existuje. Aby ste mohli uložiť projekciu, musíte zadať jedinečný názov stĺpca.
#XMSG
uniqueColumnBusinessNameDesc=Obchodný názov cieľového stĺpca už existuje. Ak chcete uložiť projekciu, musíte zadať jedinečný obchodný názov stĺpca.
#XMSG
emptySource=Vyberte zdrojový stĺpec alebo zadajte konštantu.
#XMSG
emptySourceDescription=Ak chcete vytvoriť platný záznam mapovania, musíte vybrať zdrojový stĺpec alebo zadať hodnotu konštanty.
#XMSG
emptyExpression=Definujte mapovanie.
#XMSG
emptyExpressionDescription1=Buď vyberte zdrojový stĺpec, na ktorý chcete mapovať cieľový stĺpec, alebo začiarknite pole v stĺpci {0} Funkcie / Konštanty {1}. {2} {2} Funkcie sa zadávajú automaticky podľa cieľového údajového typu. Konštantné hodnoty je možné zadať manuálne.
#XMSG
numberExpressionErr=Zadajte číslo.
#XMSG
numberExpressionErrDescription=Vybrali ste si numerický typ údajov. To znamená že môžete zadať len číslice a v prípade potreby aj desatinnú čiarku. Nepoužívajte jednoduché úvodzovky.
#XMSG
invalidLength=Zadajte platnú hodnotu dĺžky.
#XMSG
invalidLengthDescription=Dĺžka typu údajov musí byť rovnaká alebo väčšia ako dĺžka zdrojového stĺpca a môže byť v rozsahu od 1 do 5 000.
#XMSG
invalidMappedLength=Zadajte platnú hodnotu dĺžky.
#XMSG
invalidMappedLengthDescription=Dĺžka typu údajov musí byť rovnaká alebo väčšia ako dĺžka zdrojového stĺpca {0} a môže byť v rozsahu od 1 do 5 000.
#XMSG
invalidPrecision=Zadajte platnú hodnotu presnosti.
#XMSG
invalidPrecisionDescription=Presnosť definuje celkový počet číslic. Mierka definuje počet číslic za desatinnou čiarkou a môže byť medzi 0 a presnosťou.{0}{0} Príklady: {0}{1} Príklady: Presnosť 6, mierka 2 zodpovedá číslam ako 1234,56.{0}{1} Presnosť 6, mierka 6 zodpovedá číslam ako 0,123546.{0} {0} Presnosť a mierka pre cieľ musia byť kompatibilné s presnosťou a mierkou pre zdroj, aby sa všetky číslice zo zdroja zmestili do cieľového poľa. Napríklad, ak máte v zdroji presnosť 6 a mierku 2 (a následne iné číslice ako 0 pred desatinnou čiarkou), nemôžete mať v cieli presnosť 6 a mierku 6.
#XMSG
invalidPrimaryKey=Zadajte aspoň jeden primárny kľúč.
#XMSG
invalidPrimaryKeyDescription=Primárny kľúč nie je definovaný pre túto schému.
#XMSG
invalidMappedPrecision=Zadajte platnú hodnotu presnosti.
#XMSG
invalidMappedPrecisionDescription1=Presnosť definuje celkový počet číslic. Mierka definuje počet číslic za desatinnou čiarkou a môže byť medzi 0 a presnosťou.{0}{0} Príklady:{0}{1} Presnosť 6, mierka 2 zodpovedá číslam ako 1234,56.{0}{1} Presnosť 6, mierka 6 zodpovedá číslam ako 0,123546.{0}{0}Presnosť typu údajov musí byť rovnaká alebo väčšia ako presnosť zdroja ({2}).
#XMSG
invalidScale=Zadajte platnú hodnotu stupnice.
#XMSG
invalidScaleDescription=Presnosť definuje celkový počet číslic. Mierka definuje počet číslic za desatinnou čiarkou a môže byť medzi 0 a presnosťou.{0}{0} Príklady: {0}{1} Príklady: Presnosť 6, mierka 2 zodpovedá číslam ako 1234,56.{0}{1} Presnosť 6, mierka 6 zodpovedá číslam ako 0,123546.{0} {0} Presnosť a mierka pre cieľ musia byť kompatibilné s presnosťou a mierkou pre zdroj, aby sa všetky číslice zo zdroja zmestili do cieľového poľa. Napríklad, ak máte v zdroji presnosť 6 a mierku 2 (a následne iné číslice ako 0 pred desatinnou čiarkou), nemôžete mať v cieli presnosť 6 a mierku 6.
#XMSG
invalidMappedScale=Zadajte platnú hodnotu stupnice.
#XMSG
invalidMappedScaleDescription1=Presnosť definuje celkový počet číslic. Mierka definuje počet číslic za desatinnou čiarkou a môže byť medzi 0 a presnosťou.{0}{0} Príklady:{0}{1} Presnosť 6, mierka 2 zodpovedá číslam ako 1234,56.{0}{1} Presnosť 6, mierka 6 zodpovedá číslam ako 0,123546.{0}{0}Mierka typu údajov musí byť rovnaká alebo väčšia ako mierka zdroja ({2}).
#XMSG
nonCompatibleDataType=Vyberte kompatibilný cieľový typ údajov.
#XMSG
nonCompatibleDataTypeDescription1=Typ údajov, ktorý tu zadáte, musí byť kompatibilný so zdrojovým údajovým typom ({0}). {1}{1} Príklad: Ak má váš zdrojový stĺpec údajový typ reťazec a obsahuje písmená, pre svoj cieľ nemôžete použiť desatinný údajový typ.
#XMSG
invalidColumnCount=Vyberte zdrojový stĺpec.
#XMSG
ObjectStoreInvalidScaleORPrecision=Zadajte platnú hodnotu pre presnosť a stupnicu.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=Prvá hodnota je presnosť, ktorá definuje celkový počet číslic. Druhou hodnotou je stupnica, ktorá definuje číslice za desatinnou čiarkou. Zadajte cieľovú hodnotu stupnice, ktorá je väčšia ako hodnota zdrojovej stupnice a uistite sa, že rozdiel medzi zadanou cieľovou mierkou a hodnotou presnosti je väčší ako rozdiel medzi hodnotou zdrojovej stupnice a hodnotou presnosti.
#XMSG
InvalidPrecisionORScale=Zadajte platnú hodnotu pre presnosť a stupnicu.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=Prvou hodnotou je presnosť, ktorá definuje celkový počet číslic. Druhou hodnotou je stupnica, ktorá definuje číslice po desatinnej bodke.{0}{0}Keďže nie je typ zdrojových údajov podporovaný v službe Google BigQuery, skonvertuje sa na typ cieľových údajov DECIMAL. V tomto prípade možno presnosť definovať len v rozmedzí 38 a 76 a stupnicu medzi 9 a 38. Okrem toho, výsledok presnosť mínus stupnica, ktorý predstavuje číslice pred desatinnou bodkou, musí byť medzi 29 a 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=Prvou hodnotou je presnosť, ktorá definuje celkový počet číslic. Druhou hodnotou je stupnica, ktorá definuje číslice po desatinnej bodke.{0}{0}Keďže nie je typ zdrojových údajov podporovaný v službe Google BigQuery, skonvertuje sa na typ cieľových údajov DECIMAL. V tomto prípade musí byť presnosť definovaná ako 20 alebo viac. Okrem toho, výsledok presnosť mínus stupnica, ktorý odráža číslice pred desatinnou bodkou, musí byť 20 alebo viac.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=Prvá hodnota je presnosť, ktorá definuje celkový počet číslic. Druhou hodnotou je mierka, ktorá definuje číslice za desatinnou čiarkou.{0}{0}Keďže zdrojový typ údajov nie je v cieli podporovaný, skonvertuje sa na cieľový typ údajov DECIMAL. V tomto prípade musí byť presnosť definovaná ľubovoľným číslom väčším alebo rovným 1 a menším alebo rovným 38 a mierka menšia alebo rovná presnosti.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=Prvá hodnota je presnosť, ktorá definuje celkový počet číslic. Druhou hodnotou je mierka, ktorá definuje číslice za desatinnou čiarkou.{0}{0}Keďže zdrojový typ údajov nie je v cieli podporovaný, skonvertuje sa na cieľový typ údajov DECIMAL. V tomto prípade musí byť presnosť definovaná ako 20 alebo väčšia. Okrem toho výsledok presnosti mínus stupnica, ktorá odráža číslice pred desatinnou čiarkou, musí byť 20 alebo väčší.
#XMSG
invalidColumnCountDescription=Ak chcete vytvoriť platný záznam mapovania, musíte vybrať zdrojový stĺpec alebo zadať hodnotu konštanty.
#XMSG
duplicateColumns=Premenujte cieľový stĺpec.
#XMSG
duplicateGBQCDCColumnsDesc=Názov cieľového stĺpca je rezervovaný v službe Google BigQuery. Ak chcete projekciu uložiť, musíte ho premenovať.
#XMSG
duplicateConfluentCDCColumnsDesc=Názov cieľového stĺpca je rezervovaný v službe Confluent. Ak chcete projekciu uložiť, musíte ho premenovať.
#XMSG
duplicateSignavioCDCColumnsDesc=Názov cieľového stĺpca je rezervovaný v službe SAP Signavio. Ak chcete projekciu uložiť, musíte ho premenovať.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Názov cieľového stĺpca je rezervovaný v službe MS OneLake. Ak chcete projekciu uložiť, musíte ho premenovať.
#XMSG
duplicateSFTPCDCColumnsDesc=Názov cieľového stĺpca je rezervovaný v službe SFTP. Ak chcete projekciu uložiť, musíte ho premenovať.
#XMSG
GBQTargetNameWithPrefixUpdated1=Názov cieľového stĺpca obsahuje prefix, ktorý je rezervovaný v službe Google BigQuery. Ak chcete projekciu uložiť, musíte ho premenovať. {0}{0}Názov cieľového stĺpca nesmie začínať žiadnym z nasledujúcich reťazcov:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Skráťte názov cieľového stĺpca.
#XMSG
GBQtargetMaxLengthDesc=V službe Google BigQuery môže maž názov stĺpca maximálne 300 znakov. Skráťte názov cieľového stĺpca, aby ste mohli uložiť projekciu.
#XMSG
invalidMappedScalePrecision=Presnosť a stupnica cieľa musia byť kompatibilné s presnosťou a stupnicou zdroja tak, aby všetky číslice zo zdroja vošli do cieľového poľa.
#XMSG
invalidMappedScalePrecisionShortText=Zadajte platnú hodnotu presnosti a stupnice.
#XMSG
validationIncompatiblePKTypeDescProjection3=Jeden alebo viacero zdrojových stĺpcov obsahuje typy údajov, ktoré nemožno definovať ako primárne kľúče v službe Google BigQuery. V cieľovom objekte sa nevytvorí žiadny z primárnych kľúčov.{0}{0}Nasledovné cieľové typy údajov sú kompatibilné s typmi údajov služby Google BigQuery, pre ktoré možno definovať primárny kľúč:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Zrušiť označenie column __message_id.
#XMSG
uncheckColumnMessageIdDesc=Stĺpec: Primárny kľúč
#XMSG
validationOpCodeInsert=Musíte zadať hodnotu pre Vložiť.
#XMSG
recommendDifferentPrimaryKey=Odporúčame vám vybrať iný primárny kľúč na úrovni položky.
#XMSG
recommendDifferentPrimaryKeyDesc=Keď je operačný kód už definovaný, odporúča sa vybrať rôzne primárne kľúče pre index poľa a položky, aby sa predišlo problémom, ako je napríklad duplikácia stĺpcov.
#XMSG
selectPrimaryKeyItemLevel=Musíte vybrať aspoň jeden primárny kľúč pre hlavičku aj pre úroveň položky.
#XMSG
selectPrimaryKeyItemLevelDesc=Keď sa pole alebo mapa rozbalia, musíte vybrať dva primárne kľúče, jeden na úrovni hlavičky a jeden na úrovni položky.
#XMSG
invalidMapKey=Musíte vybrať aspoň jeden primárny kľúč na úrovni hlavičky.
#XMSG
invalidMapKeyDesc=Keď sa pole alebo mapa rozbalia, musíte vybrať primárny kľúč na úrovni hlavičky.
#XFLD
txtSearchFields=Hľadať cieľové stĺpce
#XFLD
txtName=Názov
#XMSG
txtSourceColValidation=Jeden alebo viac zdrojových stĺpcov nie je podporovaných:
#XMSG
txtMappingCount=Mapovania ({0})
#XMSG
schema=Schéma
#XMSG
sourceColumn=Zdrojové stĺpce
#XMSG
warningSourceSchema=Akákoľvek zmena vykonaná v schéme ovplyvní mapovania v dialógovom okne projekcie.
#XCOL
txtTargetColName=Cieľový stĺpec (technický názov)
#XCOL
txtDataType=Typ cieľových údajov
#XCOL
txtSourceDataType=Zdrojový typ údajov
#XCOL
srcColName=Zdrojový stĺpec (technický názov)
#XCOL
precision=Presnosť
#XCOL
scale=Stupnica
#XCOL
functionsOrConstants=Funkcie/konštanty
#XCOL
txtTargetColBusinessName=Cieľový stĺpec (podnikový názov)
#XCOL
prKey=Primárny kľúč
#XCOL
txtProperties=Vlastnosti
#XBUT
txtOK=Uložiť
#XBUT
txtCancel=Zrušiť
#XBUT
txtRemove=Odstrániť
#XFLD
txtDesc=Popis
#XMSG
rftdMapping=Mapovanie
#XFLD
@lblColumnDataType=Typ údajov
#XFLD
@lblColumnTechnicalName=Technický názov
#XBUT
txtAutomap=Automatické mapovanie
#XBUT
txtUp=Nahor
#XBUT
txtDown=Nadol

#XTOL
txtTransformationHeader=Projekcia
#XTOL
editTransformation=Upraviť
#XTOL
primaryKeyToolip=Kľúč


#XMSG
rftdFilter=Filter
#XMSG
rftdFilterColumnCount=Zdroj: {0}({1})
#XTOL
rftdFilterColSearch=Hľadať
#XMSG
rftdFilterColNoData=Žiadne stĺpce na zobrazenie
#XMSG
rftdFilteredColNoExps=Žiadne výrazy filtra
#XMSG
rftdFilterSelectedColTxt=Pridať filter pre
#XMSG
rftdFilterTxt=Filter k dispozícii pre
#XBUT
rftdFilterSelectedAddColExp=Pridať výraz
#YINS
rftdFilterNoSelectedCol=Vyberte stĺpec na pridanie filtra.
#XMSG
rftdFilterExp=Výraz filtra
#XMSG
rftdFilterNotAllowedColumn=Pridávanie filtrov nie je pre tento stĺpec podporované.
#XMSG
rftdFilterNotAllowedHead=Nepodporovaný stĺpec
#XMSG
rftdFilterNoExp=Nebol definovaný žiadny filter
#XTOL
rftdfilteredTt=Filtrované
#XTOL
rftdremoveexpTt=Odstrániť výraz filtra
#XTOL
validationMessageTt=Hlásenia overenia
#XTOL
rftdFilterDateInp=Vybrať dátum
#XTOL
rftdFilterDateTimeInp=Vybrať dátum a čas
#XTOL
rftdFilterTimeInp=Vyberte čas
#XTOL
rftdFilterInp=Zadať hodnotu
#XMSG
rftdFilterValidateEmptyMsg={0} výrazov filtra v stĺpci {1} je prázdnych
#XMSG
rftdFilterValidateInvalidNumericMsg={0} výrazov filtra v stĺpci {1} obsahuje neplatnú číselnú hodnotu
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Výraz filtra musí obsahovať platné číselné hodnoty
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Ak sa zmenila schéma cieľového objektu, použite funkciu “Priradenie k existujúcemu cieľovému objektu” na hlavnej stránke na prispôsobenie zmien a znova priraďte cieľový objekt s jeho zdrojom.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Ak cieľová tabuľka už existuje a mapovanie zahŕňa zmenu schémy, pred nasadením replikačného toku musíte príslušne zmeniť cieľovú tabuľku.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Ak vaše mapovanie zahŕňa zmenu schémy, pred nasadením replikačného toku musíte zodpovedajúcim spôsobom zmeniť cieľovú tabuľku.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Nasledujúce nepodporované stĺpce boli z definície zdroja vynechané: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Nasledujúce nepodporované stĺpce boli z definície cieľa vynechané: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Nasledujúce objekty nie sú podporované, pretože sú vystavené na spotrebu: {0} {1} {0} {0} Ak chcete použiť tabuľky v toku replikácie, sémantické použitie (v nastaveniach tabuľky) nesmie byť nastavené na {2}Analytická množina údajov{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Cieľový objekt nemožno použiť, pretože je vystavený na spotrebu. {0} {0} Ak chcete použiť tabuľku v toku replikácie, sémantické použitie (v nastaveniach tabuľky) nesmie byť nastavené na {1}Analytická množina údajov{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Cieľový objekt s týmto názvom už existuje. Nemožno ho však použiť, pretože je vystavený na spotrebu. {0} {0} Ak chcete použiť tabuľku v toku replikácie, sémantické použitie (v nastaveniach tabuľky) nesmie byť nastavené na {1}Analytical Dataset{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Objekt s týmto názvom už v cieli existuje. {0}Tento objekt však nemožno použiť ako cieľový objekt pre tok replikácie do lokálneho archívu, keďže nejde o lokálnu tabuľku.
#XMSG:
targetAutoRenameUpdated=Cieľový stĺpec bol premenovaný.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Cieľový stĺpec bol premenovaný, aby sa umožnili replikácie v službe Google BigQuery. Toto je spôsobené jedným z nasledujúcich dôvodov:{0} {1}{2}Rezervovaný názov stĺpca{3}{2}Nepodporované znaky{3}{2}Rezervovaný prefix{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Cieľový stĺpec bol premenovaný, aby sa umožnili replikácie v službe Confluent. Toto je spôsobené jedným z nasledujúcich dôvodov:{0} {1}{2}Rezervovaný názov stĺpca{3}{2}Nepodporované znaky{3}{2}Rezervovaný prefix{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Cieľový stĺpec bol premenovaný, aby umožňoval replikácie do cieľa. Je to spôsobené jedným z nasledujúcich dôvodov:{0} {1}{2}Nepodporované znaky{3}{2}Vyhradený prefix{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Cieľový stĺpec bol premenovaný, aby umožňoval replikácie do cieľa. Je to spôsobené jedným z nasledujúcich dôvodov:{0} {1}{2}Vyhradený názov stĺpca{3}{2}Nepodporované znaky{3}{2}Vyhradený prefix{3}{4}
#XMSG:
targetAutoDataType=Typ cieľových údajov bol zmenený.
#XMSG:
targetAutoDataTypeDesc=Typ cieľových údajov bol zmenený na {0}, pretože typ zdrojových údajov nie je v službe Google BigQuery podporovaný.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Typ cieľových údajov bol zmenený na {0}, pretože typ zdrojových údajov nie je podporovaný v cieľovom pripojení.
#XMSG
projectionGBQUnableToCreateKey=Primárne kľúče sa nevytvoria.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=V službe Google BigQuery je podporovaných maximálne 16 primárnych kľúčov. Zdrojový objekt má väčší počet primárnych kľúčov. V cieľovom objekte sa nevytvorí žiaden z primárnych kľúčov.
#XMSG
HDLFNoKeyError=Definujte jeden alebo viac stĺpcov ako primárny kľúč.
#XMSG
HDLFNoKeyErrorDescription=Ak chcete replikovať objekt, musíte definovať jeden alebo viac stĺpcov ako primárny kľúč.
#XMSG
HDLFNoKeyErrorExistingTarget=Definujte jeden alebo viac stĺpcov ako primárny kľúč.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Ak chcete replikovať údaje do existujúceho cieľového objektu, musí mať jeden alebo viac stĺpcov, ktoré sú definované ako primárny kľúč. {0} {0} Na definovanie jedného alebo viacerých stĺpcov ako primárneho kľúča máte nasledujúce voľby: {0}{0}{1} Na zmenu existujúceho cieľového objektu použite editor lokálnej tabuľky. Potom znova načítajte tok replikácie.{0}{0}{1} Premenujte cieľový objekt v toku replikácie. Tým sa vytvorí nový objekt hneď po spustení chodu. Po premenovaní môžete definovať jeden alebo viac stĺpcov ako primárny kľúč v projekcii.{0}{0}{1} Priraďte objekt k inému existujúcemu cieľovému objektu, v ktorom je jeden alebo viac stĺpcov už definovaných ako primárny kľúč.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Primárny kľúč zmenený.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=V porovnaní so zdrojovým objektom ste definovali rôzne stĺpce ako primárny kľúč pre cieľový objekt. Uistite sa, že tieto stĺpce jedinečne identifikujú všetky riadky, aby ste predišli možnému poškodeniu údajov pri neskoršej replikácii údajov. {0} {0} V zdrojovom objekte sú ako primárny kľúč definované nasledujúce stĺpce: {0} {1}
#XMSG
duplicateDPIDColumns=Premenujte cieľový stĺpec.
#XMSG
duplicateDPIDDColumnsDesc1=Tento názov cieľového stĺpca je vyhradený pre technický stĺpec. Ak chcete uložiť projekciu, zadajte iný názov.
#XMSG:
targetAutoRenameDPID=Cieľový stĺpec bol premenovaný.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Cieľový stĺpec bol premenovaný, aby umožňoval replikácie zo zdroja ABAP bez kľúčov. Toto je spôsobené jedným z nasledujúcich dôvodov:{0} {1}{2}Rezervovaný názov stĺpca{3}{2}Nepodporované znaky{3}{2}Rezervovaný prefix{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle=Cieľové nastavenia {0}
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle=Nastavenia zdroja {0}
#XBUT
connectionSettingSave=Uložiť
#XBUT
connectionSettingCancel=Zrušiť
#XBUT: Button to keep the object level settings
txtKeep=Zachovať
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Prepísať
#XFLD
targetConnectionThreadlimit=Limit cieľové vlákna pre iniciálne načítanie údajov (1 – 100)
#XFLD
connectionThreadLimit=Limit zdrojového vlákna pre iniciálne načítanie údajov (1 – 100)
#XFLD
maxConnection=Limit replikačných vlákien (1-100)
#XFLD
kafkaNumberOfPartitions=Počet segmentov
#XFLD
kafkaReplicationFactor=Faktor replikácie
#XFLD
kafkaMessageEncoder=Kódovač správ
#XFLD
kafkaMessageCompression=Komprimácia správ
#XFLD
fileGroupDeltaFilesBy=Zoskupenie delta podľa
#XFLD
fileFormat=Typ súboru
#XFLD
csvEncoding=Kódovanie CSV
#XFLD
abapExitLbl=ABAP Exit
#XFLD
deltaPartition=Počet vlákien objektu pre načítania delta (1-10)
#XFLD
clamping_Data=Zlyhanie pri skrátení údajov
#XFLD
fail_On_Incompatible=Zlyhalo na nekompatibilných údajoch
#XFLD
maxPartitionInput=Max.počet segmentov
#XFLD
max_Partition=Definovať max.počet segmentov
#XFLD
include_SubFolder=Zahrnúť podradené priečinky
#XFLD
fileGlobalPattern=Globálny vzor pre názov súboru
#XFLD
fileCompression=Komprimácia súboru
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Oddeľovač súborov
#XFLD
fileIsHeaderIncluded=Hlavička súboru
#XFLD
fileOrient=Orientácia
#XFLD
gbqWriteMode=Režim zápisu
#XFLD
suppressDuplicate=Potlačiť duplikáty
#XFLD
apacheSpark=Aktivovať kompatibilitu Apache Spark
#XFLD
clampingDatatypeCb=Svorkové typy údajov s pohyblivou desatinnou čiarkou
#XFLD
overwriteDatasetSetting=Prepísať cieľové nastavenia na úrovni objektu
#XFLD
overwriteSourceDatasetSetting=Prepísať zdrojové nastavenia na úrovni objektu
#XMSG
kafkaInvalidConnectionSetting=Zadajte číslo medzi {0} a {1}
#XMSG
MinReplicationThreadErrorMsg=Zadajte číslo väčšie ako {0}.
#XMSG
MaxReplicationThreadErrorMsg=Zadajte číslo menšie ako {0}.
#XMSG
DeltaThreadErrorMsg=Zadajte hodnotu medzi 1 a 10.
#XMSG
MaxPartitionErrorMsg=Zadajte hodnotu medzi 1 <= x <= 2147483647. Predvolená hodnota je 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Zadajte celé číslo medzi {0} a {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Použite replikačný koeficient makléra
#XFLD
serializationFormat=Formát serializácie
#XFLD
compressionType=Typ kompresie
#XFLD
schemaRegistry=Použiť register schém
#XFLD
subjectNameStrat=Stratégia názvu subjektu
#XFLD
compatibilityType=Typ kompatibility
#XFLD
confluentTopicName=Názov témy
#XFLD
confluentRecordName=Názov záznamu
#XFLD
confluentSubjectNamePreview=Ukážka názvu predmetu
#XMSG
serializationChangeToastMsgUpdated2=Formát serializácie sa zmenil na JSON, pretože register schém nie je povolený. Ak chcete zmeniť formát serializácie späť na AVRO, musíte najskôr povoliť register schém.
#XBUT
confluentTopicNameInfo=Názov témy je vždy založený na názve cieľového objektu. Môžete to zmeniť premenovaním cieľového objektu.
#XMSG
emptyRecordNameValidationHeaderMsg=Zadajte názov záznamu.
#XMSG
emptyPartionHeader=Zadajte počet segmentov.
#XMSG
invalidPartitionsHeader=Zadajte platný počet segmentov.
#XMSG
invalidpartitionsDesc=Zadajte číslo od 1 do 200 000.
#XMSG
emptyrFactorHeader=Zadajte faktor replikácie.
#XMSG
invalidrFactorHeader=Zadajte platný faktor replikácie.
#XMSG
invalidrFactorDesc=Zadajte číslo od 1 do 32 767.
#XMSG
emptyRecordNameValidationDescMsg=Ak sa používa formát serializácie "AVRO", sú podporované iba nasledujúce znaky:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(podčiarkovník)
#XMSG
validRecordNameValidationHeaderMsg=Zadajte platný názov záznamu.
#XMSG
validRecordNameValidationDescMsgUpdated=Pretože sa používa formát serializácie "AVRO", názov záznamu musí pozostávať iba z alfanumerických (A-Z, a-z, 0-9) a znakov podčiarknutia (_). Musí začínať písmenom alebo podčiarkovníkom.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=„Počet vlákien objektov pre načítania delta“ je možné nastaviť hneď, ako jeden alebo viacero objektov má typ načítania „počiatočné a delta“.
#XMSG
invalidTargetName=Neplatný názov stĺpca
#XMSG
invalidTargetNameDesc=Názov cieľového stĺpca musí pozostávať iba z alfanumerických (A-Z, a-z, 0-9) a znakov podčiarknutia (_).
#XFLD
consumeOtherSchema=Použiť ďalšie verzie schém
#XFLD
ignoreSchemamissmatch=Ignorovať nesúlad schémy
#XFLD
confleuntDatatruncation=Zlyhanie pri skrátení údajov
#XFLD
isolationLevel=Úroveň izolácie
#XFLD
confluentOffset=Bod spustenia
#XFLD
signavioGroupDeltaFilesByText=Žiadne
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=Nie
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=Nie

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Projekcie
#XBUT
txtAdd=Pridať
#XBUT
txtEdit=Upraviť
#XMSG
transformationText=Pridajte projekciu pre nastavenie filtra alebo mapovania.
#XMSG
primaryKeyRequiredText=Vyberte primárny kľúč pomocou funkcie Konfigurovať schému.
#XFLD
lblSettings=Nastavenia
#XFLD
lblTargetSetting={0}: Cieľové nastavenia
#XMSG
@csvRF=Vyberte súbor, ktorý obsahuje definíciu schémy, ktorú chcete použiť na všetky súbory v priečinku.
#XFLD
lblSourceColumns=Zdrojové stĺpce
#XFLD
lblJsonStructure=Štruktúra JSON
#XFLD
lblSourceSetting={0}: Nastavenia zdroja
#XFLD
lblSourceSchemaSetting={0}: Nastavenia zdrojovej schémy
#XBUT
messageSettings=Nastavenia správ
#XFLD
lblPropertyTitle1=Vlastnosti objektu
#XFLD
lblRFPropertyTitle=Vlastnosti replikačného toku
#XMSG
noDataTxt=Neexistujú žiadne stĺpce na zobrazenie.
#XMSG
noTargetObjectText=Nie je vybraný žiadny cieľový objekt.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Cieľové stĺpce
#XMSG
searchColumns=Vyhľadať stĺpce
#XTOL
cdcColumnTooltip=Stĺpec pre záznam delta
#XMSG
sourceNonDeltaSupportErrorUpdated=Zdrojový objekt nepodporuje delta zaznamenávanie.
#XMSG
targetCDCColumnAdded=Na zaznamenanie delta boli pridané 2 cieľové stĺpce.
#XMSG
deltaPartitionEnable=Do nastavení zdroja bol pridaný limit objektového vlákna pre načítania delta.
#XMSG
attributeMappingRemovalTxt=Odstránenie neplatných priradení, ktoré nie sú podporované pre nový cieľový objekt.
#XMSG
targetCDCColumnRemoved=2 cieľové stĺpce používané pre záznam delta boli odstránené.
#XMSG
replicationLoadTypeChanged=Typ načítania bol zmenený na „Iniciálne a delta“.
#XMSG
sourceHDLFLoadTypeError=Zmeniť typ načítania na „Iniciálne a delta“.
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Ak chcete replikovať objekt zo zdrojového pripojenia s typom pripojenia SAP HANA Cloud, Data Lake Files do SAP Datasphere, musíte použiť typ načítania „Iniciálne a delta“.
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Povoliť Delta Capture.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Ak chcete replikovať objekt zo zdrojového pripojenia s typom pripojenia SAP HANA Cloud, Data Lake Files do SAP Datasphere, musíte povoliť zaznamenávanie delta.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Zmeniť cieľový objekt.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Cieľový objekt nemožno použiť, pretože zaznamenávanie delta je vypnuté. Cieľový objekt môžete buď premenovať (čo umožňuje vytvorenie nového objektu so zaznamenávaním delta), alebo ho priradiť k existujúcemu objektu s povoleným zaznamenávaním delta.
#XMSG
deltaPartitionError=Zadajte platný počet vlákien objektu pre načítania delta.
#XMSG
deltaPartitionErrorDescription=Zadajte hodnotu medzi 1 a 10.
#XMSG
deltaPartitionEmptyError=Zadajte počet vlákien objektu pre načítania delta.
#XFLD
@lblColumnDescription=Popis
#XMSG
@lblColumnDescriptionText1=Na technické účely - spracovanie duplicitných záznamov spôsobených problémami počas replikácie zdrojových objektov založených na ABAP, ktoré nemajú primárny kľúč.
#XFLD
storageType=Ukladací priestor
#XFLD
skipUnmappedColLbl=Preskočiť nepriradené stĺpce
#XFLD
abapContentTypeLbl=Typ obsahu
#XFLD
autoMergeForTargetLbl=Automaticky zlúčiť dáta
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Všeobecne
#XFLD
lblBusinessName=Podnikový názov
#XFLD
lblTechnicalName=Technický názov
#XFLD
lblPackage=Balík
#XFLD
statusPanel=Status chodu
#XBTN: Schedule dropdown menu
SCHEDULE=Plán
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Upraviť plán
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Odstrániť plán
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Vytvoriť plán
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Neúspešná kontrola overenia kontroly
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Plán nemožno vytvoriť, pretože práve prebieha nasadzovanie toku replikácie.{0} Počkajte, kým sa tok replikácie nenasadí.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Pre toky replikácie, ktoré obsahujú objekt s typom dátovej časti "počiatočné a delta", nie je možné vytvoriť plán.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Pre toky replikácie, ktoré obsahujú objekt s typom dátovej časti "počiatočné a delta/iba delta", nie je možné vytvoriť plán.
#XFLD : Scheduled popover
SCHEDULED=Naplánované
#XFLD
CREATE_REPLICATION_TEXT=Vytvorte tok replikácie
#XFLD
EDIT_REPLICATION_TEXT=Upravte tok replikácie
#XFLD
DELETE_REPLICATION_TEXT=Odstráňte tok replikácie
#XFLD
REFRESH_FREQUENCY=Frekvencia
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Tok replikácie nie je možné nasadiť, pretože existujúci plán{0} ešte nepodporuje typ načítania „Iniciálne a delta“.{0}{0}Ak chcete nasadiť tok replikácie, musíte nastaviť typy načítania všetkých objektov{0} na "Iba iniciálne". Prípadne môžete odstrániť plán, nasadiť tok replikácie {0}a potom spustiť nové spustenie. Výsledkom je chod bez konca {0}, ktorý podporuje aj objekty s typom načítania „Iniciálne a delta“.
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Tok replikácie nie je možné nasadiť, pretože existujúci plán{0} ešte nepodporuje typ načítania „Iniciálne a delta/iba delta“.{0}{0}Ak chcete nasadiť tok replikácie, musíte nastaviť typy načítania všetkých objektov{0} na "Iba iniciálne". Prípadne môžete odstrániť plán, nasadiť tok replikácie {0}a potom spustiť nové spustenie. Výsledkom je chod bez konca {0}, ktorý podporuje aj objekty s typom načítania „Iniciálne a delta/iba delta“.
#XMSG
SCHEDULE_EXCEPTION=Nepodarilo sa načítať detaily plánu
#XFLD: Label for frequency column
everyLabel=Každých
#XFLD: Plural Recurrence text for Hour
hoursLabel=Hodiny
#XFLD: Plural Recurrence text for Day
daysLabel=Dni
#XFLD: Plural Recurrence text for Month
monthsLabel=Mesiace
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minúty
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Nepodarilo sa získať informácie o možnosti plánu.
#XFLD :Paused field
PAUSED=Pozastavené
#XMSG
navToMonitoring=Otvoriť v monitore tokov
#XFLD
statusLbl=Status
#XFLD
lblLastRunExecuted=Začiatok posledného chodu
#XFLD
lblLastExecuted=Posledný chod
#XFLD: Status text for Completed
statusCompleted=Dokončené
#XFLD: Status text for Running
statusRunning=Spustené
#XFLD: Status text for Failed
statusFailed=Neúspešné
#XFLD: Status text for Stopped
statusStopped=Zastavené
#XFLD: Status text for Stopping
statusStopping=Zastavuje sa
#XFLD: Status text for Active
statusActive=Aktívne
#XFLD: Status text for Paused
statusPaused=Pozastavené
#XFLD: Status text for not executed
lblNotExecuted=Ešte nevykonané
#XFLD
messagesSettings=Nastavenia správ
#XTOL
@validateModel=Hlásenia overenia
#XTOL
@hierarchy=Hierarchia
#XTOL
@columnCount=Počet stĺpcov
#XMSG
VAL_PACKAGE_CHANGED=Tento objekt ste priradili k paketu "{1}". Kliknutím na možnosť Uložiť túto zmenu potvrdíte a overíte. Upozorňujeme, že priradenie k paketu nemožno v tomto editore po uložení vrátiť späť.
#XMSG
MISSING_DEPENDENCY=Závislosti objektu "{0}" nie je možné vyriešiť v balíku "{1}".
#XFLD
deltaLoadInterval=Interval delta prevzatia dát
#XFLD
lblHour=Hodiny (0-24)
#XFLD
lblMinutes=Minúty (0-59)
#XMSG
maxHourOrMinErr=Zadajte hodnotu od 0 do {0}
#XMSG
maxDeltaInterval=Maximálna hodnota intervalu delta prevzatia dát je 24 hodín.{0}Podľa toho zmeňte hodnotu minút alebo hodín.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Cesta k cieľovému kontajneru
#XFLD
confluentSubjectName=Názov predmetu
#XFLD
confluentSchemaVersion=Verzia schémy
#XFLD
confluentIncludeTechKeyUpdated=Zahrnúť technický kľúč
#XFLD
confluentOmitNonExpandedArrays=Vynechať nerozbalené polia
#XFLD
confluentExpandArrayOrMap=Rozbaľte Pole alebo Mapu
#XCOL
confluentOperationMapping=Priradenie operácie
#XCOL
confluentOpCode=Opcode
#XFLD
confluentInsertOpCode=Vložiť
#XFLD
confluentUpdateOpCode=Aktualizovať
#XFLD
confluentDeleteOpCode=Odstrániť
#XFLD
expandArrayOrMapNotSelectedTxt=Nevybrané
#XFLD
confluentSwitchTxtYes=Áno
#XFLD
confluentSwitchTxtNo=Nie
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Chyba
#XTIT
executeWarning=Upozornenie
#XMSG
executeunsavederror=Pred spustením uložte tok replikácie.
#XMSG
executemodifiederror=V toku replikácie sa vyskytujú neuložené zmeny. Uložte tok replikácie.
#XMSG
executeundeployederror=Pred spustením musíte nasadiť tok replikácie.
#XMSG
executedeployingerror=Čakajte na dokončenie nasadenia.
#XMSG
msgRunStarted=Chod bol spustený
#XMSG
msgExecuteFail=Nepodarilo sa vykonať tok replikácie.
#XMSG
titleExecuteBusy=Čakajte.
#XMSG
msgExecuteBusy=Pripravujeme vaše údaje na vykonanie toku replikácie.
#XTIT
executeConfirmDialog=Upozornenie
#XMSG
msgExecuteWithValidations=Tok replikácie vykazuje chyby overenia. Vykonanie toku replikácie môže skončiť zlyhaním.
#XMSG
msgRunDeployedVersion=Neexistujú žiadne zmeny na nasadenie. Spustí sa posledná nasadená verzia toku replikácie. Chcete pokračovať?
#XBUT
btnExecuteAnyway=Napriek tomu vykonať
#XBUT
btnExecuteClose=Zavrieť
#XBUT
loaderClose=Zavrieť
#XTIT
loaderTitle=Načítava sa
#XMSG
loaderText=Načítavajú sa detaily zo servera
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Tok replikácie do tohto non-SAP cieľového pripojenia nemožno spustiť,
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=pretože pre tento mesiac nie je k dispozícii žiaden objem výstupu.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Správca môže navýšiť bloky prémiového výstupu pre tohto
#XMSG
premiumOutBoundRFAdminErrMsgPart2=nájomcu tým, že poskytne objem výstupu pre tento mesiac.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Chyba
#XTIT
deployInfo=Informácie
#XMSG
deployCheckFailException=Počas nasadenia sa vyskytla výnimka
#XMSG
deployGBQFFDisabled=Nasadenie tokov replikácie s cieľovým pripojením do služby Google BigQuery nie je momentálne možné, pretože práve vykonávame údržbu tejto funkcie.
#XMSG
deployKAFKAFFDisabled=Nasadenie tokov replikácie s cieľovým pripojením do služby Apache Kafka nie je momentálne možné, pretože práve vykonávame údržbu tejto funkcie.
#XMSG
deployConfluentDisabled=Nasadenie tokov replikácie s cieľovým pripojením do služby Confluent Kafka nie je momentálne možné, pretože práve vykonávame údržbu tejto funkcie.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Pre nasledujúce cieľové objekty sú názvy tabuliek delta capture už používané inými tabuľkami v repository: {0} Tieto cieľové objekty musíte premenovať, aby ste sa uistili, že priradené názvy tabuliek delta capture sú jedinečné, skôr než budete môcť nasadiť tok replikácie.
#XMSG
deployDWCSourceFFDisabled=Nasadenie tokov replikácie, ktoré majú ako zdroj SAP Datasphere, momentálne nie je možné, pretože vykonávame údržbu tejto funkcie.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Nasadenie replikačných tokov, ktoré obsahujú lokálne tabuľky s povolenou delta ako zdrojové objekty, momentálne nie je možné, pretože na tejto funkcii vykonávame údržbu.
#XMSG
deployHDLFSourceFFDisabled=Nasadenie tokov replikácie, ktoré majú zdrojové pripojenia s typom pripojenia SAP HANA Cloud, Data Lake Files, momentálne nie je možné, pretože vykonávame údržbu.
#XMSG
deployObjectStoreAsSourceFFDisabled=Nasadenie replikačných tokov, ktorých zdrojom je poskytovateľ cloudového úložiska, momentálne nie je možné.
#XMSG
deployConfluentSourceFFDisabled=Nasadenie tokov replikácie, ktoré majú ako zdroj Confluent Kafka, momentálne nie je možné, pretože vykonávame údržbu tejto funkcie.
#XMSG
deployMaxDWCNewTableCrossed=V prípade veľkých replikačných tokov nie je možné ich „uložiť a nasadiť“ naraz. Najprv uložte svoj tok replikácie a potom ho nasaďte.
#XMSG
deployInProgressInfo=Nasadzovanie už prebieha.
#XMSG
deploySourceObjectInUse=Zdrojové objekty {0} sa už používajú v replikačných tokoch {1}.
#XMSG
deployTargetSourceObjectInUse=Zdrojové objekty {0} sa už používajú v replikačných tokoch {1}. Cieľové objekty {2} sa už používajú v replikačných tokoch {3}.
#XMSG
deployReplicationFlowCheckError=Chyba pri overení toku replikácie: {0}
#XMSG
preDeployTargetObjectInUse=Cieľové objekty {0} sa už používajú v tokoch replikácie {1} a ten istý cieľový objekt nemôžete mať v dvoch rôznych tokoch replikácie. Vyberte iný cieľový objekt a skúste to znova.
#XMSG
runInProgressInfo=Replikačný tok je už spustený.
#XMSG
deploySignavioTargetFFDisabled=Nasadenie replikačných tokov, ktoré majú ako cieľ SAP Signavio, momentálne nie je možné, pretože na tejto funkcii vykonávame údržbu.
#XMSG
deployHanaViewAsSourceFFDisabled=Nasadenie replikačných tokov, ktoré majú zobrazenia ako zdrojové objekty pre vybraté zdrojové pripojenie, momentálne nie je možné. Skúste to znova neskôr.
#XMSG
deployMsOneLakeTargetFFDisabled=Nasadenie replikačných tokov, ktorých cieľom je MS OneLake, momentálne nie je možné, pretože na tejto funkcii vykonávame údržbu.
#XMSG
deploySFTPTargetFFDisabled=Nasadenie replikačných tokov, ktorých cieľom je SFTP, momentálne nie je možné, pretože na tejto funkcii vykonávame údržbu.
#XMSG
deploySFTPSourceFFDisabled=Nasadenie replikačných tokov, ktorých zdrojom je SFTP, momentálne nie je možné, pretože na tejto funkcii vykonávame údržbu.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Technický názov
#XFLD
businessNameInRenameTarget=Podnikový názov
#XTOL
renametargetDialogTitle=Premenovať cieľový objekt
#XBUT
targetRenameButton=Premenovať
#XBUT
targetRenameCancel=Zrušiť
#XMSG
mandatoryTargetName=Zadajte názov.
#XMSG
dwcSpecialChar=_(znak podčiarknutia) je jediný povolený špeciálny znak.
#XMSG
dwcWithDot=Názov cieľovej tabuľky môže pozostávať z latinských písmen, číslic, podčiarkovníkov (_) a bodiek (.). Prvý znak musí byť písmeno, číslo alebo podčiarkovník (nie bodka).
#XMSG
nonDwcSpecialChar=Povolené špeciálne znaky sú _(znak podčiarknutia) -(pomlčka) .(bodka)
#XMSG
firstUnderscorePattern=Názov nesmie začínať _(znakom podčiarknutia)

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: Zobraziť príkaz SQL Vytvoriť tabuľku
#XMSG
sqlDialogMaxPKWarning=V službe Google BigQuery sa podporuje maximálne 16 primárnych kľúčov a zdrojový objekt má väčší počet kľúčov. Preto nie sú v tomto príkaze definované žiadne primárne kľúče.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Jeden alebo viacero zdrojových stĺpcov má typy údajov, ktoré v službe Google BigQuery nemožno definovať ako primárne kľúče. Z toho dôvodu nie sú v tomto prípade definované žiadne primárne kľúče. V službe Google BigQuery môžu mať primárny kľúč iba nasledujúce typy údajov: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Kopírovať a zavrieť
#XBUT
closeDDL=Zavrieť
#XMSG
copiedToClipboard=Skopírované do schránky


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Objekt ''{0}'' nemôže byť súčasťou reťazca úloh, pretože nemá koniec (keďže zahŕňa objekty s typom načítania Iniciálne a delta/iba delta).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Objekt ''{0}'' nemôže byť súčasťou reťazca úloh, pretože nemá koniec (keďže zahŕňa objekty s typom načítania Initial a Delta).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objekt „{0}“ nie je možné pridať do reťazca úloh.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Existujú neuložené cieľové objekty. Uložte znova.{0}{0} Správanie tejto funkcie sa zmenilo: V minulosti sa cieľové objekty vytvárali iba v cieľovom prostredí, keď bol nasadený tok replikácie.{0} Teraz sú objekty už vytvorené pri ukladaní toku replikácie. Váš tok replikácie bol vytvorený pred touto zmenou a obsahuje nové objekty.{0} Pred nasadením musíte tok replikácie ešte raz uložiť, aby boli nové objekty správne zahrnuté.
#XMSG
confirmChangeContentTypeMessage=Chystáte sa zmeniť typ obsahu. Ak tak urobíte, všetky existujúce projekcie sa odstránia.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Názov predmetu
#XFLD
schemaDialogVersionName=Verzia schémy
#XFLD
includeTechKey=Zahrnúť technický kľúč
#XFLD
segementButtonFlat=Ploché
#XFLD
segementButtonNested=Vnorené
#XMSG
subjectNamePlaceholder=Vyhľadať názov predmetu

#XMSG
@EmailNotificationSuccess=Konfigurácia e-mailových upozornení doby chodu sa uloží.

#XFLD
@RuntimeEmailNotification=Oznámenie doby chodu e-mailom

#XBTN
@TXT_SAVE=Uložiť


