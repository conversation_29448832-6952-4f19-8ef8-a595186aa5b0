#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Replikeringsflow

#XFLD: Edit Schema button text
editSchema=Rediger skema

#XTIT : Properties heading
configSchema=Konfigurer skema

#XFLD: save changed button text
applyChanges=Anvend ændringer


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Vælg kildeforbindelse
#XFLD
sourceContainernEmptyText=Vælg container
#XFLD
targetConnectionEmptyText=Vælg målforbindelse
#XFLD
targetContainernEmptyText=Vælg container
#XFLD
sourceSelectObjectText=Vælg kildeobjekt
#XFLD
sourceObjectCount=Kildeobjekter ({0})
#XFLD
targetObjectText=Målobjekter
#XFLD
confluentBrowseContext=Vælg kontekst
#XBUT
@retry=Forsøg igen
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Opgradering af tenant i gang.

#XTOL
browseSourceConnection=Gennemse kildeforbindelse
#XTOL
browseTargetConnection=Gennemse målforbindelse
#XTOL
browseSourceContainer=Gennemse kildecontainer
#XTOL
browseAndAddSourceDataset=Tilføj kildeobjekter
#XTOL
browseTargetContainer=Gennemse målcontainer
#XTOL
browseTargetSetting=Gennemse målindstillinger
#XTOL
browseSourceSetting=Gennemse kildeindstillinger
#XTOL
sourceDatasetInfo=Oplysninger
#XTOL
sourceDatasetRemove=Fjern
#XTOL
mappingCount=Dette repræsenterer det samlede antal ikke-navnebaserede tilknytninger/udtryk.
#XTOL
filterCount=Dette repræsenterer det samlede antal filterbetingelser.
#XTOL
loading=Indlæser...
#XCOL
deltaCapture=Deltaregistrering
#XCOL
deltaCaptureTableName=Tabel med deltaregistrering
#XCOL
loadType=Indlæsningstype
#XCOL
deleteAllBeforeLoading=Slet alle før indlæsning
#XCOL
transformationsTab=Projektioner
#XCOL
settingsTab=Indstillinger

#XBUT
renameTargetObjectBtn=Omdøb målobjekt
#XBUT
mapToExistingTargetObjectBtn=Knyt til eksisterende målobjekt
#XBUT
changeContainerPathBtn=Ændr containersti
#XBUT
viewSQLDDLUpdated=Vis SQL Create Table-sætning
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Kildeobjektet understøtter ikke deltaregistrering, men det valgte målobjekt har deltaregistrering aktiveret.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Målobjektet kan ikke bruges, fordi deltaregistrering er aktiveret,{0}hvorimod kildeobjektet ikke understøtter deltaregistrering.{1}Du kan vælge et andet målobjekt, der ikke understøtter deltaregistrering.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Der findes allerede et målobjekt med dette navn. Det kan dog ikke bruges,{0}fordi deltaregistrering er aktiveret, hvorimod kildeobjektet ikke{0}understøtter deltaregistrering.{1}Du kan enten indtaste navnet på et eksisterende målobjekt, der ikke{0}understøtter deltaregistrering, eller indtaste et navn, der ikke findes endnu.
#XBUT
copySQLDDLUpdated=Kopier SQL Create Table-sætning
#XMSG
targetObjExistingNoCDCColumnUpdated=Eksisterende tabeller i Google BigQuery skal indeholde følgende kolonner for ændringsdataregistrering (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Følgende kildeobjekter understøttes ikke, fordi de ikke har en primær nøgle, eller de bruger en forbindelse, der ikke opfylder betingelserne for hentning af den primære nøgle:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Se SAP Knowledge Base Article 3531135 for en mulig løsning.
#XLST: load type list values
initial=Kun initial
@emailUpdateError=Fejl ved opdatering af liste over e-mailmeddelelser

#XLST
initialDelta=Initial og delta

#XLST
deltaOnly=Kun delta
#XMSG
confluentDeltaLoadTypeInfo=For Confluent Kafka-kilden understøttes kun indlæsningstyperne Initial og Delta.
#XMSG
confirmRemoveReplicationObject=Bekræfter du, at du vil slette replikeringen?
#XMSG
confirmRemoveReplicationTaskPrompt=Denne handling vil slette eksisterende replikeringer. Vil du fortsætte?
#XMSG
confirmTargetConnectionChangePrompt=Denne handling nulstiller målforbindelsen, målcontaineren og sletter alle målobjekter. Vil du fortsætte?
#XMSG
confirmTargetContainerChangePrompt=Denne handling nulstiller målcontaineren og sletter alle eksisterende målobjekter. Vil du fortsætte?
#XMSG
confirmRemoveTransformObject=Bekræfter du, at du vil slette projektion {0}?
#XMSG
ErrorMsgContainerChange=Der opstod en fejl ved ændring af containerstien.
#XMSG
infoForUnsupportedDatasetNoKeys=Følgende kildeobjekter understøttes ikke, fordi de ikke har en primær nøgle:
#XMSG
infoForUnsupportedDatasetView=Følgende kildeobjekter af typen Visninger understøttes ikke:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Følgende kildeobjekt understøttes ikke, da det er en SQL-visning, der indeholder inputparametre:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Følgende kildeobjekter understøttes ikke, fordi udtrækning er deaktiveret for dem:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=For Confluent-forbindelser er de eneste tilladte serialiseringsformater AVRO og JSON. Følgende objekter understøttes ikke, da de bruger et andet serialiseringsformat:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Kunne ikke hente skemaet for følgende objekter. Vælg en passende kontekst, eller verificer konfigurationen af skemaregisteret
#XTOL: warning dialog header on deleting replication task
deleteHeader=Slet
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Indstillingen Slet alle før indlæsning understøttes ikke for Google BigQuery.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Indstillingen Slet alle før sletter og genopretter objektet (emnet) før hver replikering. Dette sletter også alle tildelte meddelelser.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Indstillingen Slet alle før understøttes ikke for denne måltype.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Teknisk navn
#XCOL
connBusinessName=Forretningsnavn
#XCOL
connDescriptionName=Beskrivelse
#XCOL
connType=Type
#XMSG
connTblNoDataFoundtxt=Ingen forbindelser fundet
#XMSG
connectionError=Der opstod en fejl ved hentning af forbindelser.
#XMSG
connectionCombinationUnsupportedErrorTitle=Kombinationen af forbindelser understøttes ikke
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Replikering fra {0} til {1} understøttes ikke på nuværende tidspunkt.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Kombinationen af forbindelsestyper understøttes ikke
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Replikering fra en forbindelse med forbindelsestypen SAP HANA Cloud, datasøfiler til {0} understøttes ikke. Du kan kun replikere til SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Vælg
#XBUT
containerCancelBtn=Annuller
#XTOL
containerSelectTooltip=Vælg
#XTOL
containerCancelTooltip=Annuller
#XMSG
containerContainerPathPlcHold=Containersti
#XFLD
containerContainertxt=Container
#XFLD
confluentContainerContainertxt=Kontekst
#XMSG
infoMessageForSLTSelection=Kun /SLT/masseoverførsels-id er tilladt som container. Vælg et masseoverførsels-id under SLT (hvis tilgængeligt), og klik på Send.
#XMSG
msgFetchContainerFail=Der opstod en fejl ved hentning af containerdata.
#XMSG
infoMessageForSLTHidden=Denne forbindelse understøtter ikke SLT-mapper, så de vises ikke på listen nedenfor.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Vælg en container, der har undermapper.
#XMSG
sftpIncludeSubFolderText=Falsk
#XMSG
sftpIncludeSubFolderTextNew=Nej

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Endnu ingen filtertilknytning)
#XMSG
failToFetchRemoteMetadata=Der opstod en fejl ved hentning af metadata.
#XMSG
failToFetchData=Der opstod en fejl ved hentning af det eksisterende mål.
#XCOL
@loadType=Indlæsningstype
#XCOL
@deleteAllBeforeLoading=Slet alle før indlæsning

#XMSG
@loading=Indlæser...
#XFLD
@selectSourceObjects=Vælg kildeobjekter
#XMSG
@exceedLimit=Du kan ikke importere mere end {0} objekter ad gangen. Fjern markeringen af mindst {1} objekter.
#XFLD
@objects=Objekter
#XBUT
@ok=OK
#XBUT
@cancel=Annuller
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Næste
#XBUT
btnAddSelection=Tilføj valg
#XTOL
@remoteFromSelection=Fjern fra valg
#XMSG
@searchInForSearchField=Søg i {0}

#XCOL
@name=Teknisk navn
#XCOL
@type=Type
#XCOL
@location=Placering
#XCOL
@label=Forretningsnavn
#XCOL
@status=Status

#XFLD
@searchIn=Søg i:
#XBUT
@available=Tilgængelig
#XBUT
@selection=Valg

#XFLD
@noSourceSubFolder=Tabeller og visninger
#XMSG
@alreadyAdded=Findes allerede i diagrammet
#XMSG
@askForFilter=Der er mere end {0} elementer. Indtast en filterstreng for at begrænse antallet af elementer.
#XFLD: success label
lblSuccess=Resultat
#XFLD: ready label
lblReady=Klar
#XFLD: failure label
lblFailed=Mislykkedes
#XFLD: fetching status label
lblFetchingDetail=Henter detaljer

#XMSG Place holder text for tree filter control
filterPlaceHolder=Skriv tekst for at filtrere objekter på topniveau
#XMSG Place holder text for server search control
serverSearchPlaceholder=Skriv, og tryk på Enter for at søge
#XMSG
@deployObjects=Importerer {0} objekter...
#XMSG
@deployObjectsStatus=Antal objekter, der er importeret: {0}. Antal objekter, der ikke kunne importeres: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Kunne ikke åbne lokal lagerbrowser.
#XMSG
@openRemoteSourceBrowserError=Kunne ikke hente kildeobjekter.
#XMSG
@openRemoteTargetBrowserError=Kunne ikke hente målobjekter.
#XMSG
@validatingTargetsError=Der opstod en fejl ved validering af mål.
#XMSG
@waitingToImport=Klar til import

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Det maksimale antal objekter er overskredet. Vælg maks. 500 objekter til ét replikeringsflow.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Teknisk navn
#XFLD
sourceObjectBusinessName=Forretningsnavn
#XFLD
sourceNoColumns=Antal kolonner
#XFLD
containerLbl=Container

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Du skal vælge en kildeforbindelse til replikeringsflowet.
#XMSG
validationSourceContainerNonExist=Du skal vælge en container til kildeforbindelsen.
#XMSG
validationTargetNonExist=Du skal vælge en målforbindelse til replikeringsflowet.
#XMSG
validationTargetContainerNonExist=Du skal vælge en container til målforbindelsen.
#XMSG
validationTruncateDisabledForObjectTitle=Replikering til objektlagerpladser.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Replikering til en cloud-lagerplads er kun mulig, hvis enten valgmuligheden Slet alle før indlæsning er indstillet, eller hvis målobjektet ikke findes i målet.{0}{0} Hvis du stadig vil aktivere replikering for objekter, hvor valgmuligheden Slet alle før indlæsning ikke er indstillet, skal du sørge for, at målobjektet ikke findes i systemet, inden du kører replikeringsflowet.
#XMSG
validationTaskNonExist=Du skal have mindst en replikering i replikeringsflowet.
#XMSG
validationTaskTargetMissing=Du skal have et mål for replikeringen med kilden: {0}
#XMSG
validationTaskTargetIsSAC=Det valgte mål er et SAC-artefakt: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Det valgte mål er ikke en understøttet lokal tabel: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Der findes allerede et objekt med dette navn i målet. Dette objekt kan dog ikke bruges som målobjekt for et replikeringsflow til det lokale lager, da det ikke er en lokal tabel.
#XMSG
validateSourceTargetSystemDifference=Du skal vælge andre kombinationer af kilde- og målforbindelse og container for replikeringsflowet.
#XMSG
validateDuplicateSources=en eller flere replikeringer har dobbelte kildeobjektnavne: {0}.
#XMSG
validateDuplicateTargets=en eller flere replikeringer har dobbelte målobjektnavne: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=Kildeobjektet {0} understøtter ikke deltaregistrering som {1}. Du skal fjerne replikeringen.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Du skal vælge indlæsningstypen "Initial og delta" for replikeringen med målobjektnavn {0}.
#XMSG
validationAutoRenameTarget=Målkolonner er omdøbt.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=En automatisk projektion er tilføjet, og følgende målkolonner er omdøbt med henblik på at tillade replikering til målet:{1}{1} {0} {1}{1}Dette sker af en af følgende årsager:{1}{1}{2} Ikke-understøttede tegn{1}{2} Reserveret præfiks
#XMSG
validationAutoRenameTargetDescriptionUpdated=En automatisk projektion er tilføjet, og følgende målkolonner er omdøbt med henblik på at tillade replikering til Google BigQuery:{1}{1} {0} {1}{1}Dette sker af en af følgende årsager:{1}{1}{2} Reserveret kolonnenavn{1}{2} Ikke understøttede tegn{1}{2} Reserveret præfiks
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=En automatisk projektion er tilføjet, og følgende målkolonner er omdøbt med henblik på at tillade replikering til Confluent:{1}{1} {0} {1}{1}Dette sker af en af følgende årsager:{1}{1}{2} Reserveret kolonnenavn{1}{2} Ikke understøttede tegn{1}{2} Reserveret præfiks
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=En automatisk projektion er tilføjet, og følgende målkolonner er omdøbt med henblik på at tillade replikering til målet:{1}{1} {0} {1}{1}Dette sker af en af følgende årsager:{1}{1}{2} Reserveret kolonnenavn{1}{2} Ikke understøttede tegn{1}{2} Reserveret præfiks
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Målobjekt blev omdøbt.
#XMSG
autoRenameInfoDesc=Målobjektet blev omdøbt, da det indeholdte ikke-understøttede tegn. Kun følgende tegn understøttes:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(punktum){0}{1}_(understregning){0}{1}-(bindestreg)
#XMSG
validationAutoTargetTypeConversion=Måldatatyper er blevet ændret.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=For følgende målkolonner er måldatatyperne ændret, fordi kildedatatyperne i Google BigQuery ikke understøttes:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=For følgende målkolonner er måldatatyperne ændret, fordi kildedatatyperne ikke understøttes i målforbindelsen:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Forkort målkolonnenavne.
#XMSG
validationMaxCharLengthGBQTargetDescription=I Google BigQuery kan kolonnenavne have maks. 300 tegn. Brug en projektion til at forkorte følgende målkolonnenavne:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Primærnøgler oprettes ikke.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=I Google BigQuery understøttes maks. 16 primærnøgler, men kildeobjektet har et større antal primærnøgler. Ingen af primærnøglerne oprettes i målobjektet.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=En eller flere kildekolonner har datatyper, der ikke kan defineres som primære nøgler i Google BigQuery. Ingen af de primære nøgler vil blive oprettet i målobjektet.{0}{0}Følgende måldatatyper er kompatible med Google BigQuery-datatyper, for hvilke en primær nøgle kan defineres:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Definer en eller flere kolonner som en primær nøgle.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Du skal definere en eller flere kolonner som primærnøgle. Brug kildeskemadialogen til at gøre dette.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Definer en eller flere kolonner som en primær nøgle.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Du skal definere en eller flere kolonner som primær nøgle, der stemmer overens med begrænsningerne for den primære nøgle for dit kildeobjekt. Det gør du ved at gå til Konfigurer skema i egenskaberne for dit kildeobjekt.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Indtast en gyldig maksimal værdi for partition.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Maks. partitionsværdi skal være ≥ 1 og  ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Definer en eller flere kolonner som en primær nøgle.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Hvis du vil replikere et objekt, skal du definere en eller flere målkolonner som en primær nøgle. Brug en projektion til at gøre dette.
#XMSG
validateHDLFNoPKExistingDatasetError=Definer en eller flere kolonner som en primær nøgle.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=For at data kan replikeres til et eksisterende målobjekt skal det have en eller flere kolonner, der er defineret som primær nøgle. {0} Du har følgende valgmuligheder for at definere en eller flere kolonner som primær nøgle: {0} {1} Brug den lokale tabeleditor til at ændre det eksisterende målobjekt. Derefter skal replikeringsflowet indlæses igen.{0}{1} Omdøb målobjektet i replikeringsflowet. Dette opretter et nyt objekt, så snart der startes en kørsel. Efter omdøbningen kan du definere en eller flere kolonner som den primære nøgle i en projektion.{0}{1} Tilknyt objektet til et andet eksisterende objekt, hvori en eller flere kolonner allerede er defineret som den primære nøgle.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Det valgte mål findes allerede i lageret: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Navnene på tabeller med deltaregistrering bruges allerede af andre tabeller i lageret: {0}. Du skal omdøbe disse målobjekter for at sikre, at de tilknyttede navne på tabeller med deltaregistrering er entydige, inden du kan gemme replikeringsflowet.
#XMSG
validateConfluentEmptySchema=Definer skema
#XMSG
validateConfluentEmptySchemaDescUpdated=Kildetabellen har ikke et skema. Vælg Konfigurer skema for at definere et
#XMSG
validationCSVEncoding=Ugyldig CSV-kodning
#XMSG
validationCSVEncodingDescription=CSV-kodningen af opgaven er ikke gyldig.
#XMSG
validateConfluentEmptySchema=Vælg en kompatibel måldatatype
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Vælg en kompatibel måldatatype
#XMSG
globalValidateTargetDataTypeDesc=Der opstod en fejl med kolonnetilknytningerne. Gå til Projektion, og sørg for, at alle kildekolonner knyttes til en entydig kolonne, med en kolonne, der har en kompatibel datatype, og at alle definerede udtryk er gyldige.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Dobbelte kolonnenavne
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Dobbelte kolonnenavne understøttes ikke. Brug dialogboksen Projektion til at rette dem. Følgende målobjekter har dobbelte kolonnenavne: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Dobbelte kolonnenavne.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Dobbelte kolonnenavne understøttes ikke. Følgende målobjekter har dobbelte kolonnenavne: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Der kan være inkonsistenser i dataene.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Indlæsningstypen Kun delta tager ikke højde for de ændringer, der er foretaget i kilden mellem den sidste lagring og den næste kørsel.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Ændr indlæsningstypen til "Initial".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Replikering af ABAP-baserede objekter, der ikke har en primærnøgle, er kun mulig for indlæsningstypen "Kun initial".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Deaktiver deltaregistrering.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Hvis du vil replikere et objekt, der ikke har en primærnøgle, ved hjælp af kildeforbindelsestypen ABAP, skal du først deaktivere deltaregistrering for denne tabel.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Ændr målobjekt.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Målobjektet kan ikke bruges, da deltaregistrering er aktiveret. Du kan enten omdøbe målobjektet og derefter slå deltaregistrering fra for det nye (omdøbte) objekt eller tildele kildeobjektet til et målobjekt, hvor deltaregistrering er deaktiveret.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Ændr målobjekt.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Målobjektet kan ikke bruges, fordi det ikke har den påkrævede tekniske kolonne __load_package_id. Du kan omdøbe målobjektet med et navn, som ikke findes endnu. Systemet opretter så et nyt objekt, der har samme definition som kildeobjektet og indeholder den tekniske kolonne. Alternativt kan du tildele målobjektet til et eksisterende objekt, der har den påkrævede tekniske kolonne (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Ændr målobjekt.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Målobjektet kan ikke bruges, fordi det ikke har den påkrævede tekniske kolonne __load_record_id. Du kan omdøbe målobjektet med et navn, som ikke findes endnu. Systemet opretter så et nyt objekt, der har samme definition som kildeobjektet og indeholder den tekniske kolonne. Alternativt kan du tildele målobjektet til et eksisterende objekt, der har den påkrævede tekniske kolonne (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Ændr målobjekt.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Målobjektet kan ikke bruges, fordi datatypen i dens tekniske kolonne __load_record_id ikke er "string(44)". Du kan omdøbe målobjektet med et navn, som ikke findes endnu. Systemet opretter så et nyt objekt, der har samme definition som kildeobjektet og dermed den korrekte datatype. Alternativt kan du tildele målobjektet til et eksisterende objekt, som har den påkrævede tekniske kolonne (__load_record_id) med den korrekte datatype.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Ændr målobjekt.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Målobjektet kan ikke bruges, fordi det har en primær nøgle, mens kildeobjektet ikke har nogen. Du kan omdøbe målobjektet med et navn, som ikke findes endnu. Systemet opretter så et nyt objekt, der har samme definition som kildeobjektet og dermed ingen primærnøgle. Alternativt kan du tildele målobjektet til et eksisterende objekt, der har den påkrævede tekniske kolonne (__load_package_id) og ikke har en primær nøgle.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Ændr målobjekt.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Målobjektet kan ikke bruges, fordi det har en primær nøgle, mens kildeobjektet ikke har nogen. Du kan omdøbe målobjektet med et navn, som ikke findes endnu. Systemet opretter så et nyt objekt, der har samme definition som kildeobjektet og dermed ingen primærnøgle. Alternativt kan du tildele målobjektet til et eksisterende objekt, der har den påkrævede tekniske kolonne (__load_record_id) og ikke har en primær nøgle.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Ændr målobjekt.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Målobjektet kan ikke bruges, fordi datatypen i dens tekniske kolonne __load_package_id ikke er "binary(>=256)". Du kan omdøbe målobjektet med et navn, som ikke findes endnu. Systemet opretter så et nyt objekt, der har samme definition som kildeobjektet og dermed den korrekte datatype. Alternativt kan du tildele målobjektet til et eksisterende objekt, som har den påkrævede tekniske kolonne (__load_package_id) med den korrekte datatype.
#XMSG
validationAutoRenameTargetDPID=Målkolonner er omdøbt.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Fjern kildeobjekt.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Kildeobjektet har ikke en nøglekolonne, hvilket ikke understøttes i denne kontekst.
#XMSG
validationAutoRenameTargetDPIDDescription=En automatisk projektion er tilføjet, og følgende målkolonner er omdøbt med henblik på at tillade replikering fra ABAP-kilden uden nøgler:{1}{1} {0} {1}{1}Dette sker af en af følgende årsager:{1}{1}{2} Reserveret kolonnenavn{1}{2} Ikke understøttede tegn{1}{2} Reserveret præfiks
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Replikering til {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Det er ikke muligt, at gemme og implementere replikeringsflow, der har {0} som mål, er i øjeblikket ikke mulig, fordi vi udfører vedligeholdelse på denne funktion.
#XMSG
TargetColumnSkippedLTF=Målkolonnen blev sprunget over.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Målkolonnen blev sprunget over på grund af ikke-understøttet datatype. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Tidskolonne som primær nøgle.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Kildeobjektet har en tidskolonne som en primær nøgle, hvilket ikke understøttes i denne kontekst.
#XMSG
validateNoPKInLTFTarget=Primær nøgle mangler.
#XMSG
validateNoPKInLTFTargetDescription=Primær nøgle er ikke defineret i målet, hvilket ikke understøttes i denne kontekst.
#XMSG
validateABAPClusterTableLTF=ABAP-klyngetabel.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Kildeobjektet er en ABAP-klyngetabel, hvilket ikke understøttes i denne kontekst.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Det ser ud til, at du endnu ikke har tilføjet nogen data.
#YINS
welcomeText2=Dit replikeringsflow startes ved at vælge en forbindelse og et kildeobjekt i venstre side.

#XBUT
wizStep1=Vælg kildeforbindelse
#XBUT
wizStep2=Vælg kildecontainer
#XBUT
wizStep3=Tilføj kildeobjekter

#XMSG
limitDataset=Det maksimale antal objekter er nået. Fjern eksisterende objekter for at tilføje nye, eller opret et nyt replikeringsflow.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Replikeringsflowet til denne målforbindelse (ikke en SAP-forbindelse) kan ikke startes, da der ikke er udgående volumen tilgængelig for denne måned.
#XMSG
premiumOutBoundRFAdminWarningMsg=En administrator kan øge antallet blokke af udgående premium for denne tenant, så udgående volumen er tilgængelig for denne måned.
#XMSG
messageForToastForDPIDColumn2=Ny kolonne tilføjet til mål for {0} objekter - nødvendig for at håndtere dobbelte poster i forbindelse med ABAP-baserede kildeobjekter, der ikke har en primær nøgle.
#XMSG
PremiumInboundWarningMessage=Afhængigt af antallet af replikeringsflows og den datamængde, der skal replikeres, kan de SAP HANA-ressourcer{0}, der kræves for at replikere data via {1}, overstige den tilgængelige kapacitet for din tenant.
#XMSG
PremiumInboundWarningMsg=Afhængigt af antallet af replikeringsflows og den datamængde, der skal replikeres,{0}kan de SAP HANA-ressourcer, der kræves for at replikere data via "{1}", overstige den tilgængelige kapacitet for din tenant.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Indtast et projektionsnavn.
#XMSG
emptyTargetColumn=Indtast et målkolonnenavn.
#XMSG
emptyTargetColumnBusinessName=Indtast en målkolonne Forretningsnavn.
#XMSG
invalidTransformName=Indtast et projektionsnavn.
#XMSG
uniqueColumnName=Omdøb målkolonne
#XMSG
copySourceColumnLbl=Kopier kolonner fra kildeobjekt
#XMSG
renameWarning=Sørg for at vælge et entydigt navn, mens måltabellen omdøbes. Hvis tabellen med det nye navn allerede findes i spacet, vil den bruge den pågældende tabels definition.

#XMSG
uniqueColumnBusinessName=Omdøb forretningsnavn for målkolonne
#XMSG
uniqueSourceMapping=Vælg en anden kildekolonne.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=Kildekolonnen {0} bruges allerede af følgende målkolonner:{1}{1}{2}{1}{1} For denne målkolonne eller de andre målkolonner skal du vælge en kildekolonne, der ikke allerede bruges, til at gemme projektionen.
#XMSG
uniqueColumnNameDescription=Navnet på målkolonnen findes allerede. Du skal angive et entydigt kolonnenavn her for at kunne gemme dine poster.
#XMSG
uniqueColumnBusinessNameDesc=Forretningsnavnet på målkolonnen findes allerede. Du skal angive et entydigt forretningsnavn for kolonnen for at gemme projektionen.
#XMSG
emptySource=Vælg en kildekolonne, eller angiv en konstant.
#XMSG
emptySourceDescription=Du skal vælge en kildekolonne eller angive en konstantværdi for at oprette en gyldig tilknytningspost.
#XMSG
emptyExpression=Definer tilknytning.
#XMSG
emptyExpressionDescription1=Du skal vælge den kildekolonne, som målkolonnen skal knyttes til, eller markere afkrydsningsfeltet i kolonnen {0} Funktioner/konstanter {1}. {2} {2} Funktioner indtastes automatisk i henhold til måldatatypen. Konstantværdier kan indtastes manuelt.
#XMSG
numberExpressionErr=Indtast et tal.
#XMSG
numberExpressionErrDescription=Du valgte en numerisk datatype. Det betyder, at du kun kan indtaste numeriske tegn her og et decimalpunkt, hvis det er nødvendigt. Du skal ikke bruge enkelte anførselstegn.
#XMSG
invalidLength=Indtast en gyldig længdeværdi.
#XMSG
invalidLengthDescription=Længden af datatypen skal være lig med eller større end længden af kildekolonnen og kan være mellem 1 og 5000.
#XMSG
invalidMappedLength=Indtast en gyldig længdeværdi.
#XMSG
invalidMappedLengthDescription=Længden af datatypen skal være lig med eller større end længden af kildekolonnen {0} og kan være mellem 1 og 5000.
#XMSG
invalidPrecision=Indtast en gyldig præcisionsværdi.
#XMSG
invalidPrecisionDescription=Præcision definerer det samlede antal cifre. Skala definerer antal cifre efter decimaltegnet og kan være mellem 0 og præcision.{0}{0} Eksempler: {0}{1} Præcision 6, skala 2 svarer til tal som 1234,56.{0}{1} Præcision 6, skala 6 svarer til tal som 0,123456.{0} {0} Præcision og skala for målet skal være kompatible med præcision og skala for kilden, så alle cifre fra kilden passer ind i målfeltet. Hvis du for eksempel har præcision 6 og skala 2 i kilden (og derfor cifre udover 0 før decimaltegnet), kan du ikke have præcision 6 og skala 6 i målet.
#XMSG
invalidPrimaryKey=Indtast mindst én primær nøgle.
#XMSG
invalidPrimaryKeyDescription=Primær nøgle er ikke defineret for dette skema.
#XMSG
invalidMappedPrecision=Indtast en gyldig præcisionsværdi.
#XMSG
invalidMappedPrecisionDescription1=Præcision definerer det samlede antal cifre. Skala definerer antallet af cifre efter decimaltegnet og kan være mellem 0 og præcision.{0}{0} Eksempler:{0}{1} Præcision 6, skala 2 svarer til tal som 1234,56.{0}{1} Præcision 6, skala 6 svarer til tal som 0,123546.{0}{0}Datatypens præcision skal være lig med eller større end kildens præcision ({2}).
#XMSG
invalidScale=Indtast en gyldig skalaværdi.
#XMSG
invalidScaleDescription=Præcision definerer det samlede antal cifre. Skala definerer antal cifre efter decimaltegnet og kan være mellem 0 og præcision.{0}{0} Eksempler: {0}{1} Præcision 6, skala 2 svarer til tal som 1234,56.{0}{1} Præcision 6, skala 6 svarer til tal som 0,123456.{0} {0} Præcision og skala for målet skal være kompatible med præcision og skala for kilden, så alle cifre fra kilden passer ind i målfeltet. Hvis du for eksempel har præcision 6 og skala 2 i kilden (og derfor cifre udover 0 før decimaltegnet), kan du ikke have præcision 6 og skala 6 i målet.
#XMSG
invalidMappedScale=Indtast en gyldig skalaværdi.
#XMSG
invalidMappedScaleDescription1=Præcision definerer det samlede antal cifre. Skala definerer antallet af cifre efter decimaltegnet og kan være mellem 0 og præcision.{0}{0} Eksempler:{0}{1} Præcision 6, skala 2 svarer til tal som 1234,56.{0}{1} Præcision 6, skala 6 svarer til tal som 0,123546.{0}{0}Datatypens skala skal være lig med eller større end kildens skala ({2}).
#XMSG
nonCompatibleDataType=Vælg en kompatibel måldatatype.
#XMSG
nonCompatibleDataTypeDescription1=Den datatype, du angiver her, skal være kompatibel med kildedatatypen ({0}). {1}{1} Eksempel: Hvis din kildekolonne har datatypen streng og indeholder bogstaver, kan du ikke bruge en decimal-datatype til dit mål.
#XMSG
invalidColumnCount=Vælg en kildekolonne.
#XMSG
ObjectStoreInvalidScaleORPrecision=Indtast en gyldig værdi for præcision og skala.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=Den første værdi er præcisionen, der definerer det samlede antal cifre. Den anden værdi er skalaen, der definerer cifrene efter decimalpunktummet. Indtast en målskalaværdi, der er større end kildeskalaværdien, og sørg for, at differencen mellem den indtastede målskala- og præcisionsværdi er større end differencen mellem kildeskala- og præcisionsværdien.
#XMSG
InvalidPrecisionORScale=Indtast en gyldig værdi for præcision og skala.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=Den første værdi er præcisionen, som definerer det samlede antal cifre. Den anden værdi er skalaen, der definerer cifrene efter decimalpunktummet.{0}{0}Da kildedatatypen ikke understøttes i Google BigQuery, konverteres den til måldatatypen DECIMAL. I tilfælde heraf kan præcisionen kun defineres mellem 38 og 76, og skalaen mellem 9 og 38. Desuden skal resultatet af præcision minus skala, der repræsenterer cifrene før decimalpunktummet, være mellem 29 og 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=Den første værdi er præcisionen, som definerer det samlede antal cifre. Den anden værdi er skalaen, der definerer cifrene efter decimalpunktummet.{0}{0}Da kildedatatypen ikke understøttes i Google BigQuery, konverteres den til måldatatypen DECIMAL. I tilfælde heraf skal præcisionen defineres som 20 eller større. Desuden skal resultatet af præcision minus skala, der afspejler cifrene før decimalpunktummet, være 20 eller større.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=Den første værdi er præcisionen, som definerer det samlede antal cifre. Den anden værdi er skalaen, der definerer cifrene efter decimalpunktummet.{0}{0}Da kildedatatypen ikke understøttes i målet, konverteres den til måldatatypen DECIMAL. I dette tilfælde skal præcisionen defineres som et tal, der er større end eller lig med 1 og mindre end eller lig med 38, og skalaen er mindre end eller lig med præcisionen.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=Den første værdi er præcisionen, som definerer det samlede antal cifre. Den anden værdi er skalaen, der definerer cifrene efter decimalpunktummet.{0}{0}Da kildedatatypen ikke understøttes i målet, konverteres den til måldatatypen DECIMAL. I tilfælde heraf skal præcisionen defineres som 20 eller større. Desuden skal resultatet af præcision minus skala, der afspejler cifrene før decimalpunktummet, være 20 eller større.
#XMSG
invalidColumnCountDescription=Du skal vælge en kildekolonne eller angive en konstantværdi for at oprette en gyldig tilknytningspost.
#XMSG
duplicateColumns=Omdøb målkolonne.
#XMSG
duplicateGBQCDCColumnsDesc=Målkolonnens navn er reserveret i Google BigQuery. Du skal omdøbe den for at kunne gemme projektionen.
#XMSG
duplicateConfluentCDCColumnsDesc=Målkolonnens navn er reserveret i Confluent. Du skal omdøbe den for at kunne gemme projektionen.
#XMSG
duplicateSignavioCDCColumnsDesc=Målkolonnens navn er reserveret i SAP Signavio. Du skal omdøbe den for at kunne gemme projektionen.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Målkolonnens navn er reserveret i MS OneLake. Du skal omdøbe den for at kunne gemme projektionen.
#XMSG
duplicateSFTPCDCColumnsDesc=Målkolonnens navn er reserveret i SFTP. Du skal omdøbe den for at kunne gemme projektionen.
#XMSG
GBQTargetNameWithPrefixUpdated1=Målkolonnens navn indeholder et præfiks, der er reserveret i Google BigQuery. Du skal omdøbe den for at kunne gemme projektionen. {0}{0}Målkolonnens navn må ikke begynde med følgende strenge:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Du skal forkorte målkolonnens navn.
#XMSG
GBQtargetMaxLengthDesc=I Google BigQuery kan et kolonnenavn være på maks. 300 tegn. Du skal forkorte målkolonnens navn for at kunne gemme projektionen.
#XMSG
invalidMappedScalePrecision=Præcision og skala for målet skal være kompatibel med præcision og skala for kilden, så alle cifre fra kilden passer til målfeltet.
#XMSG
invalidMappedScalePrecisionShortText=Indtast en gyldig præcisions- og skalaværdi.
#XMSG
validationIncompatiblePKTypeDescProjection3=En eller flere kildekolonner har datatyper, der ikke kan defineres som primære nøgler i Google BigQuery. Ingen af de primære nøgler vil blive oprettet i målobjektet.{0}{0}Følgende måldatatyper er kompatible med Google BigQuery-datatyper, for hvilke en primær nøgle kan defineres:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Fjern markering af column __message_id.
#XMSG
uncheckColumnMessageIdDesc=Kolonne: Primær nøgle
#XMSG
validationOpCodeInsert=Du skal angive en værdi for Indsæt.
#XMSG
recommendDifferentPrimaryKey=Vi anbefaler, at du vælger en anden primær nøgle på elementniveauet.
#XMSG
recommendDifferentPrimaryKeyDesc=Når handlingskoden allerede er defineret, anbefales det at vælge forskellige primære nøgler til array-indekset og elementerne for at undgå problemer såsom f.eks. kolonneduplikering.
#XMSG
selectPrimaryKeyItemLevel=Du skal vælge mindst en primær nøgle til både sidehoved- og elementniveauet.
#XMSG
selectPrimaryKeyItemLevelDesc=Når en matrix eller et kort udvides, skal du vælge to primære nøgler, en på sidehovedniveau og en på elementniveau.
#XMSG
invalidMapKey=Du skal vælge mindst en primær nøgle på sidehovedniveauet.
#XMSG
invalidMapKeyDesc=Når en matrix eller et kort udvides, skal du vælge en primær nøgle på sidehovedniveauet.
#XFLD
txtSearchFields=Søg målkolonner
#XFLD
txtName=Navn
#XMSG
txtSourceColValidation=En eller flere kildekolonner understøttes ikke:
#XMSG
txtMappingCount=Tilknytninger ({0})
#XMSG
schema=Skema
#XMSG
sourceColumn=Kildekolonner
#XMSG
warningSourceSchema=Enhver ændring i skemaet vil påvirke tildelingerne i projektionsdialogen.
#XCOL
txtTargetColName=Målkolonne (teknisk navn)
#XCOL
txtDataType=Måldatatype
#XCOL
txtSourceDataType=Kildedatatype
#XCOL
srcColName=Kildekolonne (teknisk navn)
#XCOL
precision=Præcision
#XCOL
scale=Skala
#XCOL
functionsOrConstants=Funktioner/konstanter
#XCOL
txtTargetColBusinessName=Målkolonne (Forretningsnavn)
#XCOL
prKey=Primær nøgle
#XCOL
txtProperties=Egenskaber
#XBUT
txtOK=Gem
#XBUT
txtCancel=Annuller
#XBUT
txtRemove=Fjern
#XFLD
txtDesc=Beskrivelse
#XMSG
rftdMapping=Tilknytning
#XFLD
@lblColumnDataType=Datatype
#XFLD
@lblColumnTechnicalName=Teknisk navn
#XBUT
txtAutomap=Tilknyt automatisk
#XBUT
txtUp=Op
#XBUT
txtDown=Ned

#XTOL
txtTransformationHeader=Projektion
#XTOL
editTransformation=Rediger
#XTOL
primaryKeyToolip=Nøgle


#XMSG
rftdFilter=Filter
#XMSG
rftdFilterColumnCount=Kilde: {0}({1})
#XTOL
rftdFilterColSearch=Søg
#XMSG
rftdFilterColNoData=Ingen kolonner at vise
#XMSG
rftdFilteredColNoExps=Ingen filterudtryk
#XMSG
rftdFilterSelectedColTxt=Tilføj filter for
#XMSG
rftdFilterTxt=Filter tilgængeligt for
#XBUT
rftdFilterSelectedAddColExp=Tilføj udtryk
#YINS
rftdFilterNoSelectedCol=Vælg en kolonne for at tilføje filter.
#XMSG
rftdFilterExp=Filterudtryk
#XMSG
rftdFilterNotAllowedColumn=Du kan ikke tilføje filtre for denne kolonne.
#XMSG
rftdFilterNotAllowedHead=Ikke understøttet kolonne
#XMSG
rftdFilterNoExp=Intet filter er defineret
#XTOL
rftdfilteredTt=Filtreret
#XTOL
rftdremoveexpTt=Fjern filterudtryk
#XTOL
validationMessageTt=Valideringsmeddelelser
#XTOL
rftdFilterDateInp=Vælg en dato
#XTOL
rftdFilterDateTimeInp=Vælg en dato og et klokkeslæt
#XTOL
rftdFilterTimeInp=Vælg et klokkeslæt
#XTOL
rftdFilterInp=Indtast værdi
#XMSG
rftdFilterValidateEmptyMsg={0} filterudtryk i {1} kolonne er tomme
#XMSG
rftdFilterValidateInvalidNumericMsg={0} filterudtryk i {1} kolonne indeholder ugyldige numeriske værdier
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Filterudtryk skal indeholde gyldige numeriske værdier
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Hvis målobjektskemaet er ændret, skal du bruge funktionen "Knyt til eksisterende målobjekt" på hovedsiden for at implementere ændringerne og knytte målobjektet til dets kilde igen.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Hvis måltabellen allerede findes, og tilknytningen omfatter en skemaændring, skal du ændre måltabellen tilsvarende inden implementering af replikeringsflowet.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Hvis din tilknytning omfatter en skemaændring, skal du ændre måltabellen tilsvarende inden implementering af replikeringsflowet.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Følgende ikke-understøttede kolonner blev sprunget over fra kildedefinition: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Følgende ikke-understøttede kolonner blev sprunget over fra måldefinition: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Følgende objekter understøttes ikke, fordi de er eksponeret for forbrug: {0} {1} {0} {0} For, at tabeller kan bruges i et replikeringsflow, må den semantiske brug (i tabelindstillingerne) ikke være indstillet til {2}Analytisk datasæt{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Målobjektet kan ikke bruges, fordi det er eksponeret for forbrug: {0} {0} For, at tabellen kan bruges i et replikeringsflow, må den semantiske brug (i tabelindstillingerne) ikke være indstillet til {1}Analytisk datasæt{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Der findes allerede et målobjekt med dette navn. Det kan dog ikke bruges, fordi det er eksponeret for forbrug. {0} {0} For, at tabellen kan bruges i et replikeringsflow, må den semantiske brug (i tabelindstillingerne) ikke være indstillet til {1}Analytisk datasæt{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Der findes allerede et objekt med dette navn i målet. {0}Dette objekt kan dog ikke bruges som målobjekt for et replikeringsflow til det lokale lager, da det ikke er en lokal tabel.
#XMSG:
targetAutoRenameUpdated=Målkolonnen er omdøbt.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Målkolonnen er omdøbt for at tillade replikering i Google BigQuery. Dette skyldes en af følgende årsager:{0} {1}{2}Reserveret kolonnenavn{3}{2}Ikke-understøttede tegn{3}{2}Reserveret præfiks{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Målkolonnen er omdøbt for at tillade replikering i Confluent. Dette skyldes en af følgende årsager:{0} {1}{2}Reserveret kolonnenavn{3}{2}Ikke-understøttede tegn{3}{2}Reserveret præfiks{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Målkolonnen er omdøbt for at tillade replikeringer i målet. Dette skyldes en af følgende årsager:{0} {1}{2}Ikke-understøttede tegn{3}{2}Reserveret præfiks{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Målkolonnen er omdøbt for at tillade replikeringer i målet. Dette skyldes en af følgende årsager:{0} {1}{2}Reserveret kolonnenavn{3}{2}Ikke-understøttede tegn{3}{2}Reserveret præfiks{3}{4}
#XMSG:
targetAutoDataType=Måldatatype er ændret.
#XMSG:
targetAutoDataTypeDesc=Måldatatypen er ændret til {0}, da kildedatatypen ikke understøttes i Google BigQuery.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Måldatatypen er ændret til {0}, da kildedatatypen ikke understøttes i målforbindelsen.
#XMSG
projectionGBQUnableToCreateKey=Primærnøgler oprettes ikke.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=I Google BigQuery understøttes maks. 16 primærnøgler, men kildeobjektet har et større antal primærnøgler. Ingen af primærnøglerne oprettes i målobjektet.
#XMSG
HDLFNoKeyError=Definer en eller flere kolonner som en primær nøgle.
#XMSG
HDLFNoKeyErrorDescription=Hvis du vil replikere et objekt, skal du definere en eller flere kolonner som en primær nøgle.
#XMSG
HDLFNoKeyErrorExistingTarget=Definer en eller flere kolonner som en primær nøgle.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=For at data kan replikeres til et eksisterende målobjekt skal det have en eller flere kolonner, der er defineret som primær nøgle. {0} {0} Du har følgende valgmuligheder for at definere en eller flere kolonner som primær nøgle: {0}{0}{1} Brug den lokale tabeleditor til at ændre det eksisterende målobjekt. Derefter skal replikeringsflowet indlæses igen.{0}{0}{1} Omdøb målobjektet i replikeringsflowet. Dette opretter et nyt objekt, så snart der startes en kørsel. Efter omdøbningen kan du definere en eller flere kolonner som den primære nøgle i en projektion.{0}{0}{1} Tilknyt objektet til et andet eksisterende målobjekt, hvori en eller flere kolonner allerede er defineret som den primære nøgle.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Primær nøgle ændret.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=Du har defineret forskellige kolonner som den primære nøgle for målobjektet sammenlignet med kildeobjektet. Sørg for, at disse kolonner entydigt identificerer alle rækker for at undgå mulig databeskadigelse ved replikering af dataene senere. {0} {0} I kildeobjektet er følgende kolonner defineret som den primære nøgle: {0} {1}
#XMSG
duplicateDPIDColumns=Omdøb målkolonne.
#XMSG
duplicateDPIDDColumnsDesc1=Dette navn på målkolonne er reserveret til en teknisk kolonne. Indtast et andet navn for at gemme projektionen.
#XMSG:
targetAutoRenameDPID=Målkolonnen er omdøbt.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Målkolonnen er omdøbt for at tillade replikering fra ABAP-kilden uden nøgler. Dette skyldes en af følgende årsager:{0} {1}{2}Reserveret kolonnenavn{3}{2}Ikke-understøttede tegn{3}{2}Reserveret præfiks{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle={0} målindstillinger
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle={0} kildeindstillinger
#XBUT
connectionSettingSave=Gem
#XBUT
connectionSettingCancel=Annuller
#XBUT: Button to keep the object level settings
txtKeep=Behold
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Overskriv
#XFLD
targetConnectionThreadlimit=Måltrådgrænse for initial indlæsning (1-100)
#XFLD
connectionThreadLimit=Kildetrådgrænse for initial indlæsning (1-100)
#XFLD
maxConnection=Grænse for replikeringstråde (1-100)
#XFLD
kafkaNumberOfPartitions=Antal partitioner
#XFLD
kafkaReplicationFactor=Replikeringsfaktor
#XFLD
kafkaMessageEncoder=Meddelelseskoder
#XFLD
kafkaMessageCompression=Komprimering af meddelelser
#XFLD
fileGroupDeltaFilesBy=Grupper delta efter
#XFLD
fileFormat=Filtype
#XFLD
csvEncoding=CSV-kodning
#XFLD
abapExitLbl=Afslut ABAP
#XFLD
deltaPartition=Antal objekttråde for deltaindlæsninger (1-10)
#XFLD
clamping_Data=Mislykket grundet dataafkortning
#XFLD
fail_On_Incompatible=Mislykkedes grundet inkompatible data
#XFLD
maxPartitionInput=Maks. antal partitioner
#XFLD
max_Partition=Definer maks. antal partitioner
#XFLD
include_SubFolder=Inkluder undermapper
#XFLD
fileGlobalPattern=Globalt mønster for filnavn
#XFLD
fileCompression=Filkomprimering
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Filskilletegn
#XFLD
fileIsHeaderIncluded=Filheader
#XFLD
fileOrient=Retning
#XFLD
gbqWriteMode=Skrivetilstand
#XFLD
suppressDuplicate=Undertryk dubletter
#XFLD
apacheSpark=Aktiver Apache Spark-kompatibilitet
#XFLD
clampingDatatypeCb=Fastgør datatyper med flydende decimaltal
#XFLD
overwriteDatasetSetting=Overskriv målindstillinger på objektniveau
#XFLD
overwriteSourceDatasetSetting=Overskriv kildeindstillinger på objektniveau
#XMSG
kafkaInvalidConnectionSetting=Indtast et tal mellem {0} og {1}.
#XMSG
MinReplicationThreadErrorMsg=Indtast et tal, der er større end {0}.
#XMSG
MaxReplicationThreadErrorMsg=Indtast et tal, der er mindre end {0}.
#XMSG
DeltaThreadErrorMsg=Indtast en værdi mellem 1 og 10.
#XMSG
MaxPartitionErrorMsg=Indtast værdi mellem 1 <= x <= 2147483647. Standardværdien er 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Indtast et heltal mellem {0} og {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Brug replikeringsfaktor for mægler
#XFLD
serializationFormat=Serialiseringsformat
#XFLD
compressionType=Komprimeringstype
#XFLD
schemaRegistry=Brug skemaregister
#XFLD
subjectNameStrat=Strategi for emnenavn
#XFLD
compatibilityType=Kompatibilitetstype
#XFLD
confluentTopicName=Emnenavn
#XFLD
confluentRecordName=Postnavn
#XFLD
confluentSubjectNamePreview=Eksempel på emnenavn
#XMSG
serializationChangeToastMsgUpdated2=Serialiseringsformat blev ændret til JSON, idet skemaregister ikke er aktiveret. For at ændre serialiseringsformatet tilbage til AVRO skal du først aktivere skemaregister.
#XBUT
confluentTopicNameInfo=Emnenavnet er altid baseret på målobjektets navn. Du kan ændre det ved at omdøbe målobjektet.
#XMSG
emptyRecordNameValidationHeaderMsg=Indtast et postnavn.
#XMSG
emptyPartionHeader=Angiv antal partitioner.
#XMSG
invalidPartitionsHeader=Angiv et gyldigt antal partitioner.
#XMSG
invalidpartitionsDesc=Indtast et tal mellem 1 og 200.000.
#XMSG
emptyrFactorHeader=Angiv en replikeringsfaktor.
#XMSG
invalidrFactorHeader=Angiv en gyldig replikeringsfaktor.
#XMSG
invalidrFactorDesc=Indtast et tal mellem 1 og 32.767.
#XMSG
emptyRecordNameValidationDescMsg=Hvis serialiseringsformatet "AVRO" bruges, understøttes kun følgende tegn:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(understregning)
#XMSG
validRecordNameValidationHeaderMsg=Indtast et gyldigt postnavn.
#XMSG
validRecordNameValidationDescMsgUpdated=Idet serialiseringsformatet "AVRO" bruges, må navnet på posten kun bestå af alfanumeriske tegn (A-Z, a-z, 0-9) og underscores (_). Det skal begynde med et bogstav eller en underscore.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled="Antal objekttråde for deltaindlæsninger" kan indstilles, så snart et eller flere objekter har indlæsningstypen "Initial og delta".
#XMSG
invalidTargetName=Ugyldigt kolonnenavn
#XMSG
invalidTargetNameDesc=Målkolonnenavnet må kun bestå af alfanumeriske tegn (A-Z, a-z, 0-9) og underscores (_).
#XFLD
consumeOtherSchema=Forbrug andre skemaversioner
#XFLD
ignoreSchemamissmatch=Ignorer uoverensstemmende skema
#XFLD
confleuntDatatruncation=Mislykket grundet dataafkortning
#XFLD
isolationLevel=Isolationsniveau
#XFLD
confluentOffset=Udgangspunkt
#XFLD
signavioGroupDeltaFilesByText=Ingen
#XFLD
signavioFileFormatText=Parket
#XFLD
signavioSparkCompatibilityParquetText=Nej
#XFLD
siganvioFileCompressionText=Hurtig
#XFLD
siganvioSuppressDuplicatesText=Nej

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Projektioner
#XBUT
txtAdd=Tilføj
#XBUT
txtEdit=Rediger
#XMSG
transformationText=Tilføj en projektion for at konfigurere filter eller tilknytning.
#XMSG
primaryKeyRequiredText=Vælg en primær nøgle med Konfigurer skema.
#XFLD
lblSettings=Indstillinger
#XFLD
lblTargetSetting={0}: Målindstillinger
#XMSG
@csvRF=Vælg den fil, der indeholder den skemadefinition, du vil anvende på alle filer i mappen.
#XFLD
lblSourceColumns=Kildekolonner
#XFLD
lblJsonStructure=JSON-struktur
#XFLD
lblSourceSetting={0}: Kildeindstillinger
#XFLD
lblSourceSchemaSetting={0}: Kildeskemaindstillinger
#XBUT
messageSettings=Meddelelsesindstillinger
#XFLD
lblPropertyTitle1=Objektegenskaber
#XFLD
lblRFPropertyTitle=Egenskaber for replikeringsflow
#XMSG
noDataTxt=Der er ingen kolonner at vise.
#XMSG
noTargetObjectText=Der er ikke valgt noget målobjekt.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Målkolonner
#XMSG
searchColumns=Søg kolonner
#XTOL
cdcColumnTooltip=Kolonne til deltaregistrering
#XMSG
sourceNonDeltaSupportErrorUpdated=Kildeobjektet understøtter ikke deltaregistrering.
#XMSG
targetCDCColumnAdded=Der blev tilføjet 2 målkolonner til deltaregistrering.
#XMSG
deltaPartitionEnable=Grænse for objekttråde for deltaindlæsninger føjet til kildeindstillingerne.
#XMSG
attributeMappingRemovalTxt=Fjerner ugyldige tilknytninger, som ikke understøttes for nyt målobjekt.
#XMSG
targetCDCColumnRemoved=Der blev fjernet 2 målkolonner til deltaregistrering.
#XMSG
replicationLoadTypeChanged=Indlæsningstype blev ændret til "Initial og delta".
#XMSG
sourceHDLFLoadTypeError=Ændr indlæsningstypen til "Initial and Delta".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Hvis du vil replikere et objekt fra en kildeforbindelse med forbindelsestypen SAP HANA Cloud, datasøfiler til SAP Datasphere, skal du bruge indlæsningstypen "Initial and Delta".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Aktiver deltaregistrering.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Hvis du vil replikere et objekt fra en kildeforbindelse med forbindelsestypen SAP HANA Cloud, datasøfiler til SAP Datasphere, skal du aktivere deltaregistrering.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Ændr målobjekt.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Målobjektet kan ikke bruges, da deltaregistrering er deaktiveret. Du kan omdøbe målobjektet (hvilket medfører oprettelse af et nyt objekt med deltaregistrering) eller knytte det til et eksisterende objekt med deltaregistrering aktiveret.
#XMSG
deltaPartitionError=Indtast et gyldigt objekttrådantal for deltaindlæsninger.
#XMSG
deltaPartitionErrorDescription=Indtast en værdi mellem 1 og 10.
#XMSG
deltaPartitionEmptyError=Indtast et objekttrådantal for deltaindlæsninger.
#XFLD
@lblColumnDescription=Beskrivelse
#XMSG
@lblColumnDescriptionText1=Til tekniske formål - håndtering af duplikerede poster forårsaget af problemer under replikering af ABAP-baserede kildeobjekter, der ikke har en primær nøgle.
#XFLD
storageType=Lager
#XFLD
skipUnmappedColLbl=Spring over ikke-tilknyttede
#XFLD
abapContentTypeLbl=Indholdstype
#XFLD
autoMergeForTargetLbl=Flet data automatisk
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Generelt
#XFLD
lblBusinessName=Forretningsnavn
#XFLD
lblTechnicalName=Teknisk navn
#XFLD
lblPackage=Pakke
#XFLD
statusPanel=Kørselsstatus
#XBTN: Schedule dropdown menu
SCHEDULE=Tidsplan
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Rediger tidsplan
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Slet tidsplan
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Opret tidsplan
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Kontrol af validering af tidsplan blev ikke udført
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Der kan ikke oprettes en tidsplan, da replikeringsflowet er ved at blive implementeret.{0}Vent, til replikeringsflowet er blevet implementeret.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Der kan ikke oprettes en tidsplan for replikeringsflows, der indeholder objekter med indlæsningstypen "Initial and Delta".
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Der kan ikke oprettes en tidsplan for replikeringsflows, der indeholder objekter med indlæsningstypen "Initial og delta/Kun delta".
#XFLD : Scheduled popover
SCHEDULED=Planlagt
#XFLD
CREATE_REPLICATION_TEXT=Opret et replikeringsflow
#XFLD
EDIT_REPLICATION_TEXT=Rediger et replikeringsflow
#XFLD
DELETE_REPLICATION_TEXT=Slet et replikeringsflow
#XFLD
REFRESH_FREQUENCY=Frekvens
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Replikeringsflowet kan ikke implementeres , da den eksisterende tidsplan{0} ikke understøtter indlæsningstypen "Initial and Delta".{0}{0}For at implementere replikeringsflowet skal du indstille indlæsningstypen for alle objekter{0} til "Initial only". Alternativt kan du slette tidsplanen, implementere {0}replikeringsflowet og derefter starte en ny kørsel. Dette resulterer i en kontinuerlig {0}kørsel, der også understøtter objekter med indlæsningstypen "Initial and Delta".
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Replikeringsflowet kan ikke implementeres , da den eksisterende tidsplan{0} ikke understøtter indlæsningstypen "Initial og delta/Kun delta".{0}{0}For at implementere replikeringsflowet skal du indstille indlæsningstypen for alle objekter{0} til "Kun initial". Alternativt kan du slette tidsplanen, implementere {0}replikeringsflowet og derefter starte en ny kørsel. Dette resulterer i en kontinuerlig {0}kørsel, der også understøtter objekter med indlæsningstypen "Initial og delta/Kun delta".
#XMSG
SCHEDULE_EXCEPTION=Kunne ikke hente detaljer for tidsplan
#XFLD: Label for frequency column
everyLabel=Hver
#XFLD: Plural Recurrence text for Hour
hoursLabel=Timer
#XFLD: Plural Recurrence text for Day
daysLabel=Dage
#XFLD: Plural Recurrence text for Month
monthsLabel=Måneder
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutter
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Kunne ikke hente oplysninger om mulighed for tidsplan.
#XFLD :Paused field
PAUSED=Sat på pause
#XMSG
navToMonitoring=Åbn i Flowmonitor
#XFLD
statusLbl=Status
#XFLD
lblLastRunExecuted=Start for seneste kørsel
#XFLD
lblLastExecuted=Sidste kørsel
#XFLD: Status text for Completed
statusCompleted=Fuldført
#XFLD: Status text for Running
statusRunning=Kører
#XFLD: Status text for Failed
statusFailed=Mislykkedes
#XFLD: Status text for Stopped
statusStopped=Stoppet
#XFLD: Status text for Stopping
statusStopping=Stopper
#XFLD: Status text for Active
statusActive=Aktiv
#XFLD: Status text for Paused
statusPaused=Sat på pause
#XFLD: Status text for not executed
lblNotExecuted=Ikke kørt endnu
#XFLD
messagesSettings=Indstillinger for meddelelser
#XTOL
@validateModel=Valideringsmeddelelser
#XTOL
@hierarchy=Hierarki
#XTOL
@columnCount=Antal kolonner
#XMSG
VAL_PACKAGE_CHANGED=Du har tildelt dette objekt til pakken "{1}". Klik på "Gem" for at bekræfte og validere denne ændring. Bemærk, at tildelingen til en pakke ikke kan fortrydes i denne editor, efter du har gemt.
#XMSG
MISSING_DEPENDENCY=Afhængigheder for objektet "{0}" kan ikke løses i pakken "{1}".
#XFLD
deltaLoadInterval=Interval for deltaindlæsning
#XFLD
lblHour=Timer (0-24)
#XFLD
lblMinutes=Minutter (0-59)
#XMSG
maxHourOrMinErr=Angiv en værdi mellem 0 og {0}
#XMSG
maxDeltaInterval=Maksimumværdien for interval for deltaindlæsning er 24 timer.{0}Ændr minut- eller timeværdien tilsvarende.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Målcontainersti
#XFLD
confluentSubjectName=Emnenavn
#XFLD
confluentSchemaVersion=Skemaversion
#XFLD
confluentIncludeTechKeyUpdated=Inkluder teknisk nøgle
#XFLD
confluentOmitNonExpandedArrays=Udelad ikke-udvidede matrixer
#XFLD
confluentExpandArrayOrMap=Udvid matrix eller kort
#XCOL
confluentOperationMapping=Handlingstilknytning
#XCOL
confluentOpCode=Handlingskode
#XFLD
confluentInsertOpCode=Indsæt
#XFLD
confluentUpdateOpCode=Opdater
#XFLD
confluentDeleteOpCode=Slet
#XFLD
expandArrayOrMapNotSelectedTxt=Ikke valgt
#XFLD
confluentSwitchTxtYes=Ja
#XFLD
confluentSwitchTxtNo=Nej
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Fejl
#XTIT
executeWarning=Advarsel
#XMSG
executeunsavederror=Gem din replikering, inden du kører den.
#XMSG
executemodifiederror=Der er ikke-gemte ændringer i replikeringsflowet. Gem replikeringsflowet.
#XMSG
executeundeployederror=Du skal implementere dit replikeringsflow, før du kan køre det.
#XMSG
executedeployingerror=Vent til implementeringen er afsluttet.
#XMSG
msgRunStarted=Kørsel startet
#XMSG
msgExecuteFail=Kunne ikke køre replikeringsflowet.
#XMSG
titleExecuteBusy=Vent.
#XMSG
msgExecuteBusy=Vi forbereder dine data til at køre replikeringsflowet.
#XTIT
executeConfirmDialog=Advarsel
#XMSG
msgExecuteWithValidations=Replikeringsflowet har valideringsfejl. Kørsel af replikeringsflowet kan resultere i fejl.
#XMSG
msgRunDeployedVersion=Der er ændringer, der skal implementeres. Den senest implementerede version af replikeringsflowet køres. Vil du fortsætte?
#XBUT
btnExecuteAnyway=Kør alligevel
#XBUT
btnExecuteClose=Luk
#XBUT
loaderClose=Luk
#XTIT
loaderTitle=Indlæser
#XMSG
loaderText=Henter detaljer fra serveren
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Replikeringsflowet til denne målforbindelse (ikke en SAP-forbindelse) kan ikke startes,
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=da der ikke er udgående volumen tilgængelig for denne måned.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=En administrator kan øge antallet blokke af udgående premium for denne
#XMSG
premiumOutBoundRFAdminErrMsgPart2=tenant, så udgående volumen er tilgængelig for denne måned.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Fejl
#XTIT
deployInfo=Oplysninger
#XMSG
deployCheckFailException=Undtagelse opstod under implementeringen
#XMSG
deployGBQFFDisabled=Implementering af replikeringsflows med en målforbindelse til Google BigQuery er p.t. ikke mulig, fordi vi udfører vedligeholdelse i denne funktion.
#XMSG
deployKAFKAFFDisabled=Implementering af replikeringsflows med en målforbindelse til Apache Kafka er p.t. ikke mulig, fordi vi udfører vedligeholdelse i denne funktion.
#XMSG
deployConfluentDisabled=Implementering af replikeringsflows med en målforbindelse til Confluent Kafka er på nuværende tidspunkt ikke mulig, da vi udfører vedligeholdelse af denne funktion.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=For de følgende målobjekter bruges navnene på tabeller med deltaregistrering allerede af andre tabeller i lageret: {0} Du skal omdøbe disse målobjekter for at sikre, at de tilknyttede navne på tabeller med deltaregistrering er entydige, inden du kan implementere replikeringsflowet.
#XMSG
deployDWCSourceFFDisabled=Implementering af replikationsflows, der har SAP Datasphere som kilde, er i øjeblikket ikke mulig, fordi vi udfører vedligeholdelse på denne funktion.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Implementering af replikationsflows, der indeholder deltakompatible lokale tabeller som kildeobjekter, er i øjeblikket ikke mulig, da vi udfører vedligeholdelse på denne funktion.
#XMSG
deployHDLFSourceFFDisabled=Implementering af replikationsflows, der har kildeforbindelser med forbindelsestypen SAP HANA Cloud, datasøfiler, er i øjeblikket ikke mulig, fordi vi udfører vedligeholdelse.
#XMSG
deployObjectStoreAsSourceFFDisabled=Det er i øjeblikket ikke muligt at implementere replikeringsstrømme, der har en cloud storage-leverandør som kilde.
#XMSG
deployConfluentSourceFFDisabled=Implementering af replikeringsflows, der har Confluent Kafka som kilde, er i øjeblikket ikke mulig, da vi udfører vedligeholdelse på denne funktion.
#XMSG
deployMaxDWCNewTableCrossed=Det er ikke muligt at gemme og implementere store replikeringsflows samtidigt. Gem dit replikeringsflow først, og implementer det så.
#XMSG
deployInProgressInfo=Implementering er allerede i gang.
#XMSG
deploySourceObjectInUse=Kildeobjekter {0} bruges allerede i replikeringsflows {1}.
#XMSG
deployTargetSourceObjectInUse=Kildeobjekter {0} bruges allerede i replikeringsflows {1}. Målobjekter {2} bruges allerede i replikeringsflows {3}.
#XMSG
deployReplicationFlowCheckError=Fejl ved verificering af replikeringsflow: {0}
#XMSG
preDeployTargetObjectInUse=Målobjekter {0} bruges allerede i replikeringsflows {1}, og du kan ikke have det samme målobjekt i to forskellige replikeringsflows. Vælg et andet målobjekt, og prøv igen.
#XMSG
runInProgressInfo=Replikationsflowet kører allerede.
#XMSG
deploySignavioTargetFFDisabled=Implementering af replikeringsflows, der har SAP Signavio som mål, er i øjeblikket ikke mulig, da vi udfører vedligeholdelse på denne funktion.
#XMSG
deployHanaViewAsSourceFFDisabled=Implementering af replikeringsflows, der har visninger som kildeobjekter for den valgte kildeforbindelse, er i øjeblikket ikke mulig. Prøv igen senere.
#XMSG
deployMsOneLakeTargetFFDisabled=Implementering af replikeringsflows, der har MS OneLake som deres mål, er ikke mulig i øjeblikket, da vi er i gang med at udføre vedligeholdelse på denne funktion.
#XMSG
deploySFTPTargetFFDisabled=Implementering af replikeringsflows, der har SFTP som deres mål, er ikke mulig i øjeblikket, da vi er i gang med at udføre vedligeholdelse på denne funktion.
#XMSG
deploySFTPSourceFFDisabled=Implementering af replikeringsflows, der har SFTP som deres kilde, er ikke mulig i øjeblikket, da vi er i gang med at udføre vedligeholdelse på denne funktion.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Teknisk navn
#XFLD
businessNameInRenameTarget=Forretningsnavn
#XTOL
renametargetDialogTitle=Omdøb målobjekt
#XBUT
targetRenameButton=Omdøb
#XBUT
targetRenameCancel=Annuller
#XMSG
mandatoryTargetName=Du skal indtaste et navn.
#XMSG
dwcSpecialChar=_(underscore) er det eneste tilladte specialtegn.
#XMSG
dwcWithDot=Måltabelnavnet kan bestå af latinske bogstaver, tal, underscores (_) og punktummer (.). Det første tegn skal være et bogstav, et tal eller et underscore (ikke et punktum).
#XMSG
nonDwcSpecialChar=Tilladte specialtegn er _(underscore) -(bindestreg) .(punktum)
#XMSG
firstUnderscorePattern=Navnet må ikke starte med _(underscore)

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: Vis SQL Create Table-sætning
#XMSG
sqlDialogMaxPKWarning=I Google BigQuery understøttes maks. 16 primære nøgler, og kildeobjektet har et større antal. Derfor defineres der ikke primære nøgler i denne sætning.
#XMSG
sqlDialogIncomptiblePKTypeWarning=En eller flere kildekolonner har datatyper, der ikke kan defineres som primære nøgler i Google BigQuery. Derfor defineres der ikke primære nøgler i dette tilfælde. I Google BigQuery kan kun følgende datatyper have en primær nøgle: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Kopiér og luk
#XBUT
closeDDL=Luk
#XMSG
copiedToClipboard=Kopieret til udklipsholder


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Objektet "{0}" kan ikke være en del af en opgavekæde, da den ikke har en afslutning (da den indeholder objekter med indlæsningstypen Initial og delta/Kun delta).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Objektet "{0}" kan ikke være en del af en opgavekæde, da den ikke har en afslutning (da den indeholder objekter med indlæsningstypen Initial og delta).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objektet "{0}" kan ikke føjes til opgavekæden.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Der er ikke-gemte målobjekter. Gem igen.{0}{0} Adfærden af denne funktion er ændret: Tidligere blev målobjekter kun oprettet i destinationsmiljøet, når replikeringsflowet blev implementeret.{0} Nu er objekterne allerede oprettet, når replikeringsflowet gemmes. Dit replikeringsflow blev oprettet inden denne ændring og indeholder nye objekter.{0} Du skal gemme replikeringsflowet igen inden implementering, så de nye objekter inkluderes korrekt.
#XMSG
confirmChangeContentTypeMessage=Du er ved at ændre indholdstypen. Hvis du gør det, vil alle eksisterende projektioner blive slettet.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Emnenavn
#XFLD
schemaDialogVersionName=Skemaversion
#XFLD
includeTechKey=Inkluder teknisk nøgle
#XFLD
segementButtonFlat=Flad
#XFLD
segementButtonNested=Indlejret
#XMSG
subjectNamePlaceholder=Søg efter

#XMSG
@EmailNotificationSuccess=Konfiguration af e-mailmeddelelser om kørselstid er gemt.

#XFLD
@RuntimeEmailNotification=E-mailmeddelelse om kørs

#XBTN
@TXT_SAVE=Gem


