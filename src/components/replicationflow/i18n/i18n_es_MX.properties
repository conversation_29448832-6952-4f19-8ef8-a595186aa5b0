#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Flujo de replicación

#XFLD: Edit Schema button text
editSchema=Editar esquema

#XTIT : Properties heading
configSchema=Configurar esquema

#XFLD: save changed button text
applyChanges=Aplicar cambios


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Seleccionar conexión de origen
#XFLD
sourceContainernEmptyText=Seleccionar contenedor
#XFLD
targetConnectionEmptyText=Seleccionar conexión de destino
#XFLD
targetContainernEmptyText=Seleccionar contenedor
#XFLD
sourceSelectObjectText=Seleccionar objeto de origen
#XFLD
sourceObjectCount=Objetos de origen ({0})
#XFLD
targetObjectText=Objetos de destino
#XFLD
confluentBrowseContext=Seleccionar contexto
#XBUT
@retry=Reintentar
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=La actualización de inquilino está en curso.

#XTOL
browseSourceConnection=Examinar conexión de origen
#XTOL
browseTargetConnection=Examinar conexión de destino
#XTOL
browseSourceContainer=Examinar contenedor de origen
#XTOL
browseAndAddSourceDataset=Agregar objetos de origen
#XTOL
browseTargetContainer=Examinar contenedor de destino
#XTOL
browseTargetSetting=Examinar opciones de destino
#XTOL
browseSourceSetting=Examinar opciones de origen
#XTOL
sourceDatasetInfo=Información
#XTOL
sourceDatasetRemove=Quitar
#XTOL
mappingCount=Esto representa el número total de asignaciones/expresiones sin nombre.
#XTOL
filterCount=Esto representa el número total de condiciones de filtro.
#XTOL
loading=Cargando…
#XCOL
deltaCapture=Captura delta
#XCOL
deltaCaptureTableName=Tabla de captura delta
#XCOL
loadType=Tipo de carga
#XCOL
deleteAllBeforeLoading=Eliminar todo antes de la carga
#XCOL
transformationsTab=Proyecciones
#XCOL
settingsTab=Opciones

#XBUT
renameTargetObjectBtn=Renombrar objeto de destino
#XBUT
mapToExistingTargetObjectBtn=Asignar a objeto de destino existente
#XBUT
changeContainerPathBtn=Cambiar ruta de contenedor
#XBUT
viewSQLDDLUpdated=Ver instrucción SQL de crear tabla
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=El objeto de origen no admite la captura delta, pero el objeto de destino seleccionado tiene la opción de captura delta seleccionada.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=El objeto de destino no se puede usar porque la captura delta está habilitada,{0}mientras que le objeto de origen no admite captura delta.{1}Puede seleccionar otro objeto de destino que no admita captura delta.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Ya existe un objeto de destino con este nombre. Sin embargo, no se puede usar{0}porque la captura delta está habilitada, mientras el objeto de origen no{0}admite la captura delta.{1}Puede ingresar el nombre de un objeto de destino existente que no{0}admita la captura delta, o bien ingresar un nombre que aún no exista.
#XBUT
copySQLDDLUpdated=Copiar instrucción SQL de crear tabla
#XMSG
targetObjExistingNoCDCColumnUpdated=Las tablas existentes en Google BigQuery deben incluir las siguientes columnas para captura de datos (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Los siguientes objetos de origen no se admiten porque no tienen una clave principal o porque usan una conexión que no cumple con las condiciones para recuperar la clave principal:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Consulte una posible solución en SAP KBA 3531135.
#XLST: load type list values
initial=Inicial únicamente
@emailUpdateError=Error al actualizar la lista de notificaciones de correo electrónico

#XLST
initialDelta=Inicial y delta

#XLST
deltaOnly=Solo delta
#XMSG
confluentDeltaLoadTypeInfo=En el origen de Confluent Kafka, solo se admite el tipo de carga Inicial y Delta.
#XMSG
confirmRemoveReplicationObject=¿Confirma que desea quitar eliminar la replicación?
#XMSG
confirmRemoveReplicationTaskPrompt=Esta acción eliminará las replicaciones existentes. ¿Desea continuar?
#XMSG
confirmTargetConnectionChangePrompt=Esta acción restablecerá la conexión de destino y el contenedor de destino, y eliminará todos los objetos de destino. ¿Desea continuar?
#XMSG
confirmTargetContainerChangePrompt=Esta acción restablecerá el contenedor de destino y eliminará todos los objetos de destino existentes. ¿Desea continuar?
#XMSG
confirmRemoveTransformObject=¿Confirma que desea eliminar el {0} de proyección?
#XMSG
ErrorMsgContainerChange=Ocurrió un error durante el cambio de la ruta del contenedor.
#XMSG
infoForUnsupportedDatasetNoKeys=Los siguientes objetos de origen no se admiten porque no tienen una clave principal:
#XMSG
infoForUnsupportedDatasetView=Los siguientes objetos de origen del tipo Vistas no se admiten:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=El siguiente objeto de origen no se admite porque es una vista SQL que contiene los parámetros de entrada:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Los siguientes objetos de origen no se admiten porque tienen la extracción deshabilitada:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Para las conexiones de Confluent, los únicos formatos de serialización permitidos son AVRO y JSON. Los siguientes objetos no se admiten porque usan un formato de serialización diferente:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=No se puede recuperar el esquema para los siguientes objetos. Seleccione el contexto apropiado o verifique la configuración de registro del esquema
#XTOL: warning dialog header on deleting replication task
deleteHeader=Eliminar
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Google BigQuery no admite la opción Eliminar todo antes de la carga.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=La opción Eliminar todo antes de la carga elimina y recrea el objeto (tema) antes de cada replicación. Esto también elimina todos los mensajes asignados.
#XTOL
DeleteAllBeforeLoadingLTFInfo=La opción Eliminar todo antes de la carga no se admite para este tipo de destino.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Nombre técnico
#XCOL
connBusinessName=Nombre empresarial
#XCOL
connDescriptionName=Descripción
#XCOL
connType=Tipo
#XMSG
connTblNoDataFoundtxt=No se encontraron conexiones
#XMSG
connectionError=Ocurrió un error durante la captura de las conexiones.
#XMSG
connectionCombinationUnsupportedErrorTitle=La combinación de conexión no está admitida
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Actualmente, no se admite la replicación de {0} a {1}.
#XMSG
invalidTargetforSourceHDLFErrorTitle=No se admite la combinación de tipo de conexión.
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=No se admite la replicación desde una conexión de origen con el tipo de conexión SAP HANA Cloud, archivos de lago de datos. Solo puede replicar en {0}SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Seleccionar
#XBUT
containerCancelBtn=Cancelar
#XTOL
containerSelectTooltip=Seleccionar
#XTOL
containerCancelTooltip=Cancelar
#XMSG
containerContainerPathPlcHold=Ruta del contenedor
#XFLD
containerContainertxt=Contenedor
#XFLD
confluentContainerContainertxt=Contexto
#XMSG
infoMessageForSLTSelection=Solo se permite /SLT/ID de transferencia masiva como contenedor. Seleccione un ID de transferencia masiva debajo de SLT (si está disponible) y haga clic en Enviar.
#XMSG
msgFetchContainerFail=Ocurrió un error durante la captura de datos de contenedor.
#XMSG
infoMessageForSLTHidden=Esta conexión no admite carpetas SLT y, por lo tanto, no aparecen en la lista a continuación.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Seleccione un contenedor que tenga subcarpetas.
#XMSG
sftpIncludeSubFolderText=Falso
#XMSG
sftpIncludeSubFolderTextNew=No

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Aún no hay filtros asignados)
#XMSG
failToFetchRemoteMetadata=Ocurrió un error durante la captura de los metadatos.
#XMSG
failToFetchData=Ocurrió un error durante la captura del destino existente.
#XCOL
@loadType=Tipo de carga
#XCOL
@deleteAllBeforeLoading=Eliminar todo antes de la carga

#XMSG
@loading=Cargando…
#XFLD
@selectSourceObjects=Seleccionar objetos de origen
#XMSG
@exceedLimit=No puede importar más de {0} objetos a la vez. Anule la selección de al menos {1} objetos.
#XFLD
@objects=Objetos
#XBUT
@ok=OK
#XBUT
@cancel=Cancelar
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Siguiente
#XBUT
btnAddSelection=Agregar selección
#XTOL
@remoteFromSelection=Quitar de selección
#XMSG
@searchInForSearchField=Buscar en {0}

#XCOL
@name=Nombre técnico
#XCOL
@type=Tipo
#XCOL
@location=Ubicación
#XCOL
@label=Nombre empresarial
#XCOL
@status=Estado

#XFLD
@searchIn=Buscar en:
#XBUT
@available=Disponible
#XBUT
@selection=Selección

#XFLD
@noSourceSubFolder=Tablas y vistas
#XMSG
@alreadyAdded=Ya está presente en el diagrama
#XMSG
@askForFilter=Hay más de {0} elementos. Ingrese una string de filtro para limitar la cantidad de elementos.
#XFLD: success label
lblSuccess=Correcto
#XFLD: ready label
lblReady=Listo
#XFLD: failure label
lblFailed=Error
#XFLD: fetching status label
lblFetchingDetail=Recuperando detalles

#XMSG Place holder text for tree filter control
filterPlaceHolder=Escriba texto para filtrar objetos de nivel superior
#XMSG Place holder text for server search control
serverSearchPlaceholder=Escriba y presione Ingresar para buscar
#XMSG
@deployObjects=Importando {0} objetos…
#XMSG
@deployObjectsStatus=Cantidad de objetos que fueron importados: {0}. Cantidad de objetos que no se pudieron importar: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=No se pudo abrir el navegador del repositorio local.
#XMSG
@openRemoteSourceBrowserError=No se pudieron capturar los objetos de origen.
#XMSG
@openRemoteTargetBrowserError=No se pudieron capturar los objetos de destino.
#XMSG
@validatingTargetsError=Ocurrió un error durante la validación de destinos.
#XMSG
@waitingToImport=Listo para importar

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Se superó el número máximo de objetos. Seleccione un máximo de 500 objetos para un flujo de replicación.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Nombre técnico
#XFLD
sourceObjectBusinessName=Nombre empresarial
#XFLD
sourceNoColumns=Número de columnas
#XFLD
containerLbl=Contenedor

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Debe seleccionar una conexión de origen para el flujo de replicación.
#XMSG
validationSourceContainerNonExist=Debe seleccionar un contenedor para la conexión de origen.
#XMSG
validationTargetNonExist=Debe seleccionar una conexión de destino para el flujo de replicación.
#XMSG
validationTargetContainerNonExist=Debe seleccionar un contenedor para la conexión de destino.
#XMSG
validationTruncateDisabledForObjectTitle=Replicación para almacenamientos de objetos.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=La replicación a un almacenamiento en la nube solo es posible si está configurada la opción Eliminar todo antes de la carga o si el objeto de destino no existe en el destino.{0}{0} A fin de activar la replicación de todos modos para los objetos que no tienen la opción Eliminar todo antes de la carga, asegúrese de que el objeto de destino no exista en el sistema antes de que ejecute el flujo de replicación.
#XMSG
validationTaskNonExist=Debe tener al menos una replicación en el flujo de replicación.
#XMSG
validationTaskTargetMissing=Debe tener un destino para la replicación con el origen: {0}
#XMSG
validationTaskTargetIsSAC=El destino seleccionado es un artefacto de SAC: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=El destino seleccionado no es una tabla local admitida: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Un objeto con este nombre ya existe en el destino. Sin embargo, este objeto no se puede usar como un objeto de destino para un flujo de replicación hasta el repositorio local, ya que no es una tabla local.
#XMSG
validateSourceTargetSystemDifference=Debe seleccionar diferentes combinaciones de contenedor y conexión de origen y destino para el flujo de replicación.
#XMSG
validateDuplicateSources=una o más replicaciones tienen nombres de objeto de origen duplicados: {0}.
#XMSG
validateDuplicateTargets=una o más replicaciones tienen nombres de objeto de destino duplicados: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=El objeto de origen {0} no admite la captura delta, mientras que el objeto de destino {1} la acepta. Debe quitar la replicación.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Debe seleccionar el tipo de carga "Inicial y delta" para la replicación con el nombre de objeto de destino {0}.
#XMSG
validationAutoRenameTarget=Se cambió el nombre de las columnas de destino.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Se agregó una proyección automática y se renombraron las siguientes columnas de destino para admitir la replicación al destino:{1}{1} {0} {1}{1}Esto se debe a uno de los siguientes motivos:{1}{1}{2} Caracteres no admitidos{1}{2} Prefijo reservado
#XMSG
validationAutoRenameTargetDescriptionUpdated=Se agregó una proyección automática y se renombraron las siguientes columnas de destino para admitir la replicación a Google BigQuery:{1}{1} {0} {1}{1}Esto es debido a uno de los siguientes motivos:{1}{1}{2} Nombre de columna reservado{1}{2} Caracteres no admitidos{1}{2} Prefijo reservado
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Se agregó una proyección automática y se renombraron las siguientes columnas de destino para admitir las replicaciones a Confluent:{1}{1} {0} {1}{1}Esto se debe a uno de los siguientes motivos:{1}{1}{2} Nombre de columna reservado{1}{2} Caracteres no admitidos{1}{2} Prefijo reservado
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Se agregó una proyección automática y se renombraron las siguientes columnas de destino para admitir las replicaciones al destino:{1}{1} {0} {1}{1}Esto se debe a uno de los siguientes motivos:{1}{1}{2} Nombre de columna reservado{1}{2} Caracteres no admitidos{1}{2} Prefijo reservado
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Se cambió el nombre del objeto de destino.
#XMSG
autoRenameInfoDesc=Se cambió el nombre del objeto de destino porque incluía caracteres no admitidos. Solo se admiten los siguientes caracteres:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(punto){0}{1}_(guion bajo){0}{1}-(guion)
#XMSG
validationAutoTargetTypeConversion=Se cambiaron los tipos de datos de destino.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Para las siguientes columnas de destino, se cambiaron los tipos de datos de destino porque en Google BigQuery los tipos de datos de origen no se admiten:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=En las siguientes columnas de destino, se cambiaron los tipos de datos de destino porque en la conexión de destino no se admiten los tipos de datos de origen:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Acorte los nombres de columnas de destino.
#XMSG
validationMaxCharLengthGBQTargetDescription=En Google BigQuery, los nombres de columnas pueden utilizar un máximo de 300 caracteres. Use una proyección para acortar los siguientes nombres de columnas de destino:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Las claves principales no se crearán.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=En Google BigQuery, se admite un máximo de 16 claves principales, pero el objeto de origen tiene más claves principales. Ninguna de las claves principales se creará en el objeto de destino.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Una o más columnas de origen tienen tipos de datos que no se pueden definir como claves principales en Google BigQuery. Ninguna de las claves principales se creará en el objeto de destino.{0}{0}Los siguientes tipos de datos de destino son compatibles con los tipos de datos de Google BigQuery para los que se puede definir una clave principal:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1}TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Defina una o más columnas como clave principal.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Debe definir una o más columnas como clave principal; use el diálogo de esquema de origen para hacerlo.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Defina una o más columnas como clave principal.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Debe definir una o más columnas como clave principal que coincidan con las restricciones de clave principal para el objeto de origen. Para hacerlo, vaya a Configurar esquema en las propiedades de su objeto de origen.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Ingrese un valor máximo de partición válido.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=El valor de partición máximo debe ser ≥ 1 y ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Defina una o más columnas como clave principal.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Para replicar un objeto, debe definir una o más columnas de destino como clave principal. Use una proyección para hacerlo.
#XMSG
validateHDLFNoPKExistingDatasetError=Defina una o más columnas como clave principal.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Para replicar datos en un objeto de destino existente, este debe tener una o más columnas definidas como clave principal. {0} Dispone de las siguientes opciones para definir una o más columnas como clave principal: {0} {1} Use el editor de tabla local para cambiar el objeto de destino existente. Luego, vuelva a cargar el flujo de replicación.{0}{1} Cambie el nombre del objeto de destino en el flujo de replicación. Esto creará un objeto nuevo tan pronto como se inicie una ejecución. Luego del cambio de nombre, puede definir una o más columnas como clave principal en una proyección.{0}{1} Asigne el objeto a otro objeto de destino existente en el que una o más columnas ya estén definidas como clave principal.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=El destino seleccionado ya existe en el repositorio: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Los nombres de tablas de capturas delta ya los están utilizando otras tablas del repositorio: {0}. Debe renombrar estos objetos de destino para asegurarse de que los nombres de tablas de capturas delta asociados sean únicos antes de poder guardar el flujo de replicación.
#XMSG
validateConfluentEmptySchema=Definir esquema
#XMSG
validateConfluentEmptySchemaDescUpdated=La tabla de origen no tiene un esquema. Seleccione Configurar esquema para definir uno
#XMSG
validationCSVEncoding=Codificación CSV no válida
#XMSG
validationCSVEncodingDescription=La codificación CSV de la tarea no es válida.
#XMSG
validateConfluentEmptySchema=Seleccione un tipo de datos de destino compatible
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Seleccione un tipo de datos de destino compatible
#XMSG
globalValidateTargetDataTypeDesc=Ocurrió un error con las asignaciones de columna. Vaya a la proyección y asegúrese de que todas las columnas estén asignadas con una columna única, que la columna tenga un tipo de datos compatible y que todas las expresiones definidas sean válidas.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Nombres de columna duplicados.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Los nombres de columna duplicados no se admiten. Use el diálogo de proyección para corregirlos. Los siguientes objetos de destino tienen nombres de columna duplicados: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Nombres de columna duplicados.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Los nombres de columna duplicados no se admiten. Los siguientes objetos de destino tienen nombres de columna duplicados: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Puede haber inconsistencias en los datos.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=El tipo de carga Solo delta no considerará los cambios hechos en el origen entre el último guardado y la siguiente ejecución.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Cambie el tipo de carga a "Inicial".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=La replicación de objetos basados en ABAP que no tienen una clave principal es solo posible para el tipo de carga "Solo inicial".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Deshabilite la captura delta.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Para replicar un objeto que no tiene una clave principal usando un tipo de conexión de origen ABAP, primero debe deshabilitar la captura delta para esta tabla.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Cambie el objeto de destino.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=No se puede usar el objeto de destino porque la captura delta está habilitada. Puede renombrar el objeto de destino y, luego, deshabilitar la captura delta para el nuevo objeto (renombrado), o bien asignar el objeto de origen a un objeto de destino con la captura delta deshabilitada.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Cambie el objeto de destino.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=El objeto de destino no se puede usar porque no tiene la columna técnica requerida __load_package_id. Puede renombrar el objeto de destino usando un nombre que aún no exista. El sistema luego crea un nuevo objeto que tiene la misma definición que el objeto de origen y contiene la columna técnica. De forma alternativa, puede asignar el objeto de destino a un objeto existente que tenga la columna técnica requerida (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Cambie el objeto de destino.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=El objeto de destino no se puede usar porque no tiene la columna técnica requerida __load_record_id. Puede renombrar el objeto de destino usando un nombre que aún no exista. El sistema luego crea un nuevo objeto que tiene la misma definición que el objeto de origen y contiene la columna técnica. De forma alternativa, puede asignar el objeto de destino a un objeto existente que tenga la columna técnica requerida (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Cambie el objeto de destino.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=El objeto de destino no se puede usar porque el tipo de datos de su columna técnica requerida __load_record_id no es "string(44)". Puede renombrar el objeto de destino usando un nombre que aún no exista. El sistema luego crea un nuevo objeto que tiene la misma definición que el objeto de origen y contiene la columna técnica. De forma alternativa, puede asignar el objeto de destino a un objeto existente que tenga la columna técnica requerida (__load_record_id) con el tipo de datos correcto.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Cambie el objeto de destino.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=El objeto de destino no se puede usar porque tiene una clave principal, mientras que el objeto de origen no tiene ninguna. Puede renombrar el objeto de destino usando un nombre que aún no exista. El sistema luego crea un nuevo objeto que tiene la misma definición que el objeto de origen y, consecuentemente, ninguna clave principal. De forma alternativa, puede asignar el objeto de destino a un objeto existente que tiene la columna técnica requerida (__load_package_id) y no tiene una clave principal.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Cambie el objeto de destino.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=El objeto de destino no se puede usar porque tiene una clave principal, mientras que el objeto de origen no tiene ninguna. Puede renombrar el objeto de destino usando un nombre que aún no exista. El sistema luego crea un nuevo objeto que tiene la misma definición que el objeto de origen y, consecuentemente, ninguna clave principal. De forma alternativa, puede asignar el objeto de destino a un objeto existente que tiene la columna técnica requerida (__load_record_id) y no tiene una clave principal.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Cambie el objeto de destino.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=El objeto de destino no se puede usar porque el tipo de datos de su columna técnica requerida __load_package_id no es "binary(>=256)". Puede renombrar el objeto de destino usando un nombre que aún no exista. El sistema luego crea un nuevo objeto que tiene la misma definición que el objeto de origen y contiene la columna técnica. De forma alternativa, puede asignar el objeto de destino a un objeto existente que tenga la columna técnica requerida (__load_package_id) con el tipo de datos correcto.
#XMSG
validationAutoRenameTargetDPID=Se cambió el nombre de las columnas de destino.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Eliminar objeto de origen
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=El objeto de origen no tiene una columna de clave, lo que no se admite en este contexto.
#XMSG
validationAutoRenameTargetDPIDDescription=Se agregó una proyección automática y se renombraron las siguientes columnas de destino para admitir la replicación desde un origen ABAP sin claves:{1}{1} {0} {1}{1}Esto es debido a uno de los siguientes motivos:{1}{1}{2} Nombre de columna reservado{1}{2} Caracteres no admitidos{1}{2} Prefijo reservado
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Replicación a {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=El guardado y la implementación de flujos de replicación que tienen a {0} como destino actualmente no es posible porque estamos llevando a cabo el mantenimiento de esta función.
#XMSG
TargetColumnSkippedLTF=Se omitió la columna de destino.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=La columna de destino se omitió debido a un tipo de datos no admitido. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Columna de tiempo como clave principal.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=El objeto de origen tiene una columna de tiempo como clave principal, lo que no se admite en este contexto.
#XMSG
validateNoPKInLTFTarget=Falta la clave principal.
#XMSG
validateNoPKInLTFTargetDescription=La clave principal no está definida en el destino, lo que no se admite en este contexto.
#XMSG
validateABAPClusterTableLTF=Tabla de clúster de ABAP
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=El objeto de origen es una tabla de clúster de ABAP, lo que no se admite en este contexto.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Parece que aún no agregó datos.
#YINS
welcomeText2=Para iniciar su ´flujo de replicación, seleccione una conexión y un objeto de origen en el lado izquierdo.

#XBUT
wizStep1=Seleccionar conexión de origen
#XBUT
wizStep2=Seleccionar contenedor de origen
#XBUT
wizStep3=Agregar objetos de origen

#XMSG
limitDataset=Se alcanzó el número máximo de objetos. Quite los objetos existentes para agregar nuevos, o cree un nuevo flujo de replicación.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=El flujo de replicación a esta conexión de destino que no es de SAP no se puede iniciar porque no hay un volumen de salida disponible para este mes.
#XMSG
premiumOutBoundRFAdminWarningMsg=Un administrador puede incrementar los bloques de salida premium para este inquilino, lo que haría que el volumen de salida esté disponible para este mes.
#XMSG
messageForToastForDPIDColumn2=Nueva columna agregada al destino para {0} objetos - se necesita para gestionar registros duplicados en conexión con objetos de origen basados en ABAP que no tienen una clave principal.
#XMSG
PremiumInboundWarningMessage=Según el número de flujos de replicación y el volumen de datos para replicación, los recursos de SAP HANA{0}requeridos para replicar datos a través de {1} pueden exceder la capacidad disponible para su inquilino.
#XMSG
PremiumInboundWarningMsg=Según el número de flujos de replicación y el volumen de datos para replicación,{0}los recursos de SAP HANA requeridos para replicar datos a través de "{1}" pueden exceder la capacidad disponible para su inquilino.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Ingrese un nombre de proyección.
#XMSG
emptyTargetColumn=Ingrese un nombre de columna de destino.
#XMSG
emptyTargetColumnBusinessName=Ingrese un nombre empresarial de columna de destino.
#XMSG
invalidTransformName=Ingrese un nombre de proyección.
#XMSG
uniqueColumnName=Renombre la columna de destino.
#XMSG
copySourceColumnLbl=Copiar columnas desde el objeto de origen
#XMSG
renameWarning=Asegúrese de elegir un nombre único al renombrar la tabla de destino. Si la tabla con el nuevo nombre ya existe en el espacio, usará la definición de esa tabla.

#XMSG
uniqueColumnBusinessName=Renombre el nombre empresarial de la columna de destino.
#XMSG
uniqueSourceMapping=Seleccione una columna de origen diferente.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=La columna de origen {0} ya es utilizada por las siguientes columnas de destino:{1}{1}{2}{1}{1} En esta columna de destino o en las otras columnas de destino, seleccione una columna de origen que no esté en uso para guardar la proyección.
#XMSG
uniqueColumnNameDescription=El nombre de columna de destino que ingresó ya existe. Para poder guardar la proyección, necesita ingresar un nombre de columna único.
#XMSG
uniqueColumnBusinessNameDesc=El nombre empresarial de la columna de destino ya existe. Para guardar la proyección, debe ingresar un nombre empresarial de la columna único.
#XMSG
emptySource=Seleccione una columna de origen o ingrese una constante.
#XMSG
emptySourceDescription=Para crear una entrada de asignación válida, necesita seleccionar una columna de origen o ingresar un valor de constante.
#XMSG
emptyExpression=Defina la asignación.
#XMSG
emptyExpressionDescription1=Seleccione la columna de origen a la que desea asignar la columna de destino, o bien marque la casilla de verificación en la columna {0} Funciones/constantes {1}. {2} {2} Las funciones se ingresan automáticamente de acuerdo con el tipo de datos de destino. Los valores de constantes se pueden ingresar de forma manual.
#XMSG
numberExpressionErr=Ingrese un número.
#XMSG
numberExpressionErrDescription=Seleccionó un tipo de dato numérico. Esto significa que solo puede ingresar numerales, además del separador decimal, si corresponde. No use comillas simples.
#XMSG
invalidLength=Ingrese un valor de longitud válido.
#XMSG
invalidLengthDescription=La longitud del tipo de datos debe ser igual o mayor que la longitud de la columna de origen y estar entre 1 y 5000.
#XMSG
invalidMappedLength=Ingrese un valor de longitud válido.
#XMSG
invalidMappedLengthDescription=La longitud del tipo de datos debe ser igual o mayor que la longitud de la columna de origen {0} y estar entre 1 y 5000.
#XMSG
invalidPrecision=Ingrese un valor de precisión válido.
#XMSG
invalidPrecisionDescription=La precisión define el número total de dígitos. La escala define el número de dígitos luego del punto decimal y puede estar entre 0 y la precisión.{0}{0} Ejemplos: {0}{1} Precisión 6, escala 2 corresponde a números como 1234,56.{0}{1} Precisión 6, escala 6 corresponde a números como 0,123546.{0} {0} La precisión y la escala para el destino deben ser compatibles con la precisión y escala para el origen de manera que todos los dígitos del origen se ajusten al campo de destino. Por ejemplo, si tiene una precisión de 6 y una escala de 2 en el origen (y consecuentemente dígitos diferentes de 0 antes del punto decimal), no puede tener una precisión de 6 y una escala de 6 en el destino.
#XMSG
invalidPrimaryKey=Ingrese al menos una clave principal.
#XMSG
invalidPrimaryKeyDescription=Clave principal no definida para este esquema.
#XMSG
invalidMappedPrecision=Ingrese un valor de precisión válido.
#XMSG
invalidMappedPrecisionDescription1=La precisión define el número total de dígitos. La escala define el número de dígitos luego del punto decimal y puede estar entre 0 y la precisión.{0}{0} Ejemplos:{0}{1} Precisión 6, escala 2 corresponde a números como 1234,56.{0}{1} Precisión 6, escala 6 corresponde a números como 0,123546.{0}{0}La precisión del tipo de datos debe ser igual o mayor que la precisión del origen ({2}).
#XMSG
invalidScale=Ingrese un valor de escala válido.
#XMSG
invalidScaleDescription=La precisión define el número total de dígitos. La escala define el número de dígitos luego del punto decimal y puede estar entre 0 y la precisión.{0}{0} Ejemplos: {0}{1} Precisión 6, escala 2 corresponde a números como 1234,56.{0}{1} Precisión 6, escala 6 corresponde a números como 0,123546.{0} {0} La precisión y la escala para el destino deben ser compatibles con la precisión y escala para el origen de manera que todos los dígitos del origen se ajusten al campo de destino. Por ejemplo, si tiene una precisión de 6 y una escala de 2 en el origen (y consecuentemente dígitos diferentes de 0 antes del punto decimal), no puede tener una precisión de 6 y una escala de 6 en el destino.
#XMSG
invalidMappedScale=Ingrese un valor de escala válido.
#XMSG
invalidMappedScaleDescription1=La precisión define el número total de dígitos. La escala define el número de dígitos luego del punto decimal y puede estar entre 0 y la precisión.{0}{0} Ejemplos:{0}{1} Precisión 6, escala 2 corresponde a números como 1234,56.{0}{1} Precisión 6, escala 6 corresponde a números como 0,123546.{0}{0}La escala del tipo de datos debe ser igual o mayor que la escala del origen ({2}).
#XMSG
nonCompatibleDataType=Seleccione un tipo de datos de destino compatible.
#XMSG
nonCompatibleDataTypeDescription1=El tipo de datos que especifica aquí debe ser compatible con el tipo de datos de origen ({0}). {1}{1}Ejemplo: Si su columna de origen tiene el tipo de datos string y contiene letras, no puede usar un tipo de datos decimal para la columna de destino.
#XMSG
invalidColumnCount=Seleccione una columna de origen.
#XMSG
ObjectStoreInvalidScaleORPrecision=Ingrese un valor válido para la precisión y escala.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=El primer valor es la precisión, que define el número total de dígitos. El segundo valor es la escala, que define los dígitos después del punto decimal. Ingrese un valor de escala de destino que sea mayor que el valor de escala de origen y asegúrese de que la diferencia entre la escala de destino ingresada y el valor de precisión sea mayor que la diferencia entre la escala de origen y el valor de precisión.
#XMSG
InvalidPrecisionORScale=Ingrese un valor válido para la precisión y escala.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=El primer valor es la precisión, que define el número total de dígitos. El segundo valor es la escala, que define los dígitos luego del separador decimal.{0}{0}Dado que el tipo de datos de origen no se admite en Google BigQuery, se convierte al tipo de datos de destino DECIMAL. En este caso, la precisión solo se puede definir entre 38 y 76, y la escala, entre 9 y 38. Además, el resultado de la precisión menos la escala, que representa los dígitos antes del separador decimal, debe ser entre 29 y 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=El primer valor es la precisión, que define el número total de dígitos. El segundo valor es la escala, que define los dígitos luego del separador decimal.{0}{0}Dado que el tipo de datos de origen no se admite en Google BigQuery, se convierte al tipo de datos de destino DECIMAL. En este caso, la precisión se debe definir en 20 o más. Además, el resultado de la precisión menos la escala, que refleja los dígitos antes del separador decimal, debe ser 20 o más.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=El primer valor es la precisión, que define el número total de dígitos. El segundo valor es la escala, que define los dígitos luego del separador decimal.{0}{0}Dado que el tipo de datos de origen no se admite en el destino, se convierte al tipo de datos de destino DECIMAL. En este caso, la precisión se debe definir en cualquier número mayor o igual que 1 y menor o igual que 38, y la escala debe ser menor o igual que la precisión.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=El primer valor es la precisión, que define el número total de dígitos. El segundo valor es la escala, que define los dígitos luego del separador decimal.{0}{0}Dado que el tipo de datos de origen no se admite en el destino, se convierte al tipo de datos de destino DECIMAL. En este caso, la precisión se debe definir en 20 o más. Además, el resultado de la precisión menos la escala, que refleja los dígitos antes del separador decimal, debe ser 20 o más.
#XMSG
invalidColumnCountDescription=Para crear una entrada de asignación válida, necesita seleccionar una columna de origen o ingresar un valor de constante.
#XMSG
duplicateColumns=Renombre la columna de destino.
#XMSG
duplicateGBQCDCColumnsDesc=El nombre de la columna de destino se reserva en Google BigQuery. Necesita renombrarlo para poder guardar la proyección.
#XMSG
duplicateConfluentCDCColumnsDesc=El nombre de la columna de destino está reservado en Confluent. Necesita renombrarlo para poder guardar la proyección.
#XMSG
duplicateSignavioCDCColumnsDesc=El nombre de la columna de destino está reservado en SAP Signavio. Necesita renombrarlo para poder guardar la proyección.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=El nombre de la columna de destino está reservado en MS OneLake. Necesita renombrarla para poder guardar la proyección.
#XMSG
duplicateSFTPCDCColumnsDesc=El nombre de la columna de destino está reservado en SFTP. Necesita renombrarla para poder guardar la proyección.
#XMSG
GBQTargetNameWithPrefixUpdated1=El nombre de la columna de destino contiene un prefijo reservado en Google BigQuery. Necesita renombrarlo para poder guardar la proyección. {0}{0}El nombre de la columna de destino no puede comenzar con ninguna de las siguientes strings:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Acorte el nombre de columna de destino.
#XMSG
GBQtargetMaxLengthDesc=En Google BigQuery, un nombre de columna puede usar un máximo de 300 caracteres. Acorte el nombre de columna de destino para poder guardar la proyección.
#XMSG
invalidMappedScalePrecision=La precisión y escala del destino deben ser compatibles con la precisión y escala del origen para que todos los dígitos del origen se ajusten en el campo de destino.
#XMSG
invalidMappedScalePrecisionShortText=Ingrese un valor de precisión y de escala válidos.
#XMSG
validationIncompatiblePKTypeDescProjection3=Una o más columnas de origen tienen tipos de datos que no se pueden definir como claves principales en Google BigQuery. Ninguna de las claves principales se creará en el objeto de destino.{0}{0}Los siguientes tipos de datos de destino son compatibles con los tipos de datos de Google BigQuery para los que se puede definir una clave principal:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2}TIMESTAMP
#XMSG
uncheckColumnMessageId=No se comprobó column __message_id.
#XMSG
uncheckColumnMessageIdDesc=Columna: Clave principal
#XMSG
validationOpCodeInsert=Debe ingresar un valor para insertar.
#XMSG
recommendDifferentPrimaryKey=Le recomendamos seleccionar una clave principal diferente a nivel del elemento.
#XMSG
recommendDifferentPrimaryKeyDesc=Cuando el código de operación ya está definido, se recomienda seleccionar claves principales diferentes para el índice de matrices y los elementos a fin de evitar problemas como, por ejemplo, el duplicado de columnas.
#XMSG
selectPrimaryKeyItemLevel=Debe seleccionar al menos una clave principal a nivel del encabezado y del elemento.
#XMSG
selectPrimaryKeyItemLevelDesc=Cuando se expande una matriz o un mapa, debe seleccionar dos claves principales: una a nivel del encabezado y una a nivel del elemento.
#XMSG
invalidMapKey=Debe seleccionar al menos una clave principal a nivel del encabezado.
#XMSG
invalidMapKeyDesc=Cuando se expande una matriz o un mapa, debe seleccionar una clave principal a nivel del encabezado.
#XFLD
txtSearchFields=Buscar columnas de destino
#XFLD
txtName=Nombre
#XMSG
txtSourceColValidation=Una o más columnas de origen no son compatibles:
#XMSG
txtMappingCount=Asignaciones ({0})
#XMSG
schema=Esquema
#XMSG
sourceColumn=Columnas de origen
#XMSG
warningSourceSchema=Cualquier cambio en el esquema afectará las asignaciones en el diálogo de proyección.
#XCOL
txtTargetColName=Columna de destino (nombre técnico)
#XCOL
txtDataType=Tipo de datos de destino
#XCOL
txtSourceDataType=Tipo de datos de origen
#XCOL
srcColName=Columna de origen (nombre técnico)
#XCOL
precision=Precisión
#XCOL
scale=Escala
#XCOL
functionsOrConstants=Funciones/constantes
#XCOL
txtTargetColBusinessName=Columna de destino (nombre empresarial)
#XCOL
prKey=Clave principal
#XCOL
txtProperties=Propiedades
#XBUT
txtOK=Guardar
#XBUT
txtCancel=Cancelar
#XBUT
txtRemove=Quitar
#XFLD
txtDesc=Descripción
#XMSG
rftdMapping=Asignación
#XFLD
@lblColumnDataType=Tipo de datos
#XFLD
@lblColumnTechnicalName=Nombre técnico
#XBUT
txtAutomap=Asignación automática
#XBUT
txtUp=Arriba
#XBUT
txtDown=Abajo

#XTOL
txtTransformationHeader=Proyección
#XTOL
editTransformation=Editar
#XTOL
primaryKeyToolip=Clave


#XMSG
rftdFilter=Filtro
#XMSG
rftdFilterColumnCount=Origen: {0}({1})
#XTOL
rftdFilterColSearch=Buscar
#XMSG
rftdFilterColNoData=No hay columnas para mostrar
#XMSG
rftdFilteredColNoExps=No hay expresiones de filtro
#XMSG
rftdFilterSelectedColTxt=Agregar filtro para
#XMSG
rftdFilterTxt=Filtro disponible para
#XBUT
rftdFilterSelectedAddColExp=Agregar expresión
#YINS
rftdFilterNoSelectedCol=Seleccione una columna para agregar un filtro.
#XMSG
rftdFilterExp=Expresión de filtro
#XMSG
rftdFilterNotAllowedColumn=No se permite agregar filtros para esta columna.
#XMSG
rftdFilterNotAllowedHead=Columna no admitida
#XMSG
rftdFilterNoExp=No se definió ningún filtro
#XTOL
rftdfilteredTt=Filtrado
#XTOL
rftdremoveexpTt=Elimine la expresión de filtro
#XTOL
validationMessageTt=Mensajes de validación
#XTOL
rftdFilterDateInp=Seleccione una fecha
#XTOL
rftdFilterDateTimeInp=Seleccione una fecha y hora
#XTOL
rftdFilterTimeInp=Seleccione una hora
#XTOL
rftdFilterInp=Ingrese un valor
#XMSG
rftdFilterValidateEmptyMsg={0} expresiones de filtro en la columna {1} están vacías
#XMSG
rftdFilterValidateInvalidNumericMsg={0} expresiones de filtro en la columna {1} contienen valores numéricos no válidos
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=La expresión de filtro debe contener valores numéricos válidos
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Si el esquema del objeto de destino cambió, use la función “Asignar a objeto de destino existente” en la página principal para adaptar los cambios, y vuelva a asignar el objeto de destino con su origen.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Si la tabla de destino ya existe y la asignación incluye un cambio de esquema, debe cambiar la tabla de destino según corresponda antes de implementar el flujo de replicación.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Si la asignación incluye un cambio de esquema, debe cambiar la tabla de destino según corresponda antes de implementar el flujo de replicación.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Las siguientes columnas no admitidas se omitieron de la definición de origen: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Las siguientes columnas no admitidas se omitieron de la definición de destino: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Los siguientes objetos no se admiten porque están expuestos para consumo: {0} {1} {0} {0} Para usar tablas en un flujo de replicación, el uso semántico (en la configuración de la tabla) no se debe establecer en {2}Conjunto de datos analíticos{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=El objeto de destino no se puede usar porque está expuesto para consumo. {0} {0}  Para usar la tabla en un flujo de replicación, el uso semántico (en la configuración de la tabla) no se debe establecer en {1}Conjunto de datos analíticos{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Ya existe un objeto de destino con este nombre. No obstante, no se puede usar porque está expuesto para consumo. {0} {0}  Para usar la tabla en un flujo de replicación, el uso semántico (en la configuración de la tabla) no se debe establecer en {1}Conjunto de datos analíticos{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Un objeto con este nombre ya existe en el destino. {0}Sin embargo, este objeto no se puede usar como un objeto de destino para un flujo de replicación hasta el repositorio local, ya que no es una tabla local.
#XMSG:
targetAutoRenameUpdated=Se cambió el nombre de la columna de destino.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Se cambió el nombre de la columna de destino para permitir las replicaciones en Google BigQuery. Esto es por uno de los siguientes motivos:{0} {1}{2}Nombre de columna reservado{3}{2}Caracteres no admitidos{3}{2}Prefijo reservado{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Se cambió el nombre de la columna de destino para permitir las replicaciones en Confluent. Esto es por uno de los siguientes motivos:{0} {1}{2}Nombre de columna reservado{3}{2}Caracteres no admitidos{3}{2}Prefijo reservado{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Se cambió el nombre de la columna de destino para permitir las replicaciones en el destino. Esto es por uno de los siguientes motivos:{0} {1}{2}Caracteres no admitidos{3}{2}Prefijo reservado{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Se cambió el nombre de la columna de destino para permitir las replicaciones en el destino. Esto es por uno de los siguientes motivos:{0} {1}{2}Nombre de columna reservado{3}{2}Caracteres no admitidos{3}{2}Prefijo reservado{3}{4}
#XMSG:
targetAutoDataType=Se cambió el tipo de datos de destino.
#XMSG:
targetAutoDataTypeDesc=El tipo de datos de destino se cambió a {0} porque el tipo de datos de origen no se admite en Google BigQuery.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=El tipo de datos de destino se cambió a {0} porque el tipo de datos de origen no se admite en la conexión de destino.
#XMSG
projectionGBQUnableToCreateKey=Las claves principales no se crearán.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=En Google BigQuery, se admite un máximo de 16 claves principales, pero el objeto de origen tiene más claves principales. Ninguna de las claves principales se creará en el objeto de destino.
#XMSG
HDLFNoKeyError=Defina una o más columnas como clave principal.
#XMSG
HDLFNoKeyErrorDescription=Para replicar un objeto, debe definir una o más columnas como clave principal.
#XMSG
HDLFNoKeyErrorExistingTarget=Defina una o más columnas como clave principal.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Para replicar datos en un objeto de destino existente, este debe tener una o más columnas definidas como clave principal. {0} {0} Dispone de las siguientes opciones para definir una o más columnas como clave principal: {0}{0}{1} Use el editor de tabla local para cambiar el objeto de destino existente. Luego, vuelva a cargar el flujo de replicación.{0}{0}{1} Cambie el nombre del objeto de destino en el flujo de replicación. Esto creará un objeto nuevo tan pronto como se inicie una ejecución. Luego del cambio de nombre, puede definir una o más columnas como clave principal en una proyección.{0}{0}{1} Asigne el objeto a otro objeto de destino existente en el que una o más columnas ya estén definidas como clave principal.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Se cambió la clave principal.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=En comparación con el objeto de origen, definió diferentes columnas como clave principal para el objeto de destino. Asegúrese de que estas columnas identifiquen de forma única todas las filas para evitar posibles daños en los datos cuando estos se repliquen posteriormente. {0} {0} En el objeto de origen, las siguientes columnas están definidas como clave principal: {0} {1}
#XMSG
duplicateDPIDColumns=Renombre la columna de destino.
#XMSG
duplicateDPIDDColumnsDesc1=Este nombre de columna de destino está reservado para una columna técnica. Ingrese un nombre diferente para guardar la proyección.
#XMSG:
targetAutoRenameDPID=Se cambió el nombre de la columna de destino.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Se cambió el nombre de la columna de destino para permitir las replicaciones desde un origen ABAP sin claves. Esto es por uno de los siguientes motivos:{0} {1}{2}Nombre de columna reservado{3}{2}Caracteres no admitidos{3}{2}Prefijo reservado{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle=Opciones de destino de {0}
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle=Opciones de origen de {0}
#XBUT
connectionSettingSave=Guardar
#XBUT
connectionSettingCancel=Cancelar
#XBUT: Button to keep the object level settings
txtKeep=Conservar
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Sobrescribir
#XFLD
targetConnectionThreadlimit=Límite de subprocesos de origen para carga final (1-100)
#XFLD
connectionThreadLimit=Límite de subprocesos de origen para carga inicial (1-100)
#XFLD
maxConnection=Límite de subprocesos de replicación (1-100)
#XFLD
kafkaNumberOfPartitions=Cantidad de particiones
#XFLD
kafkaReplicationFactor=Factor de replicación
#XFLD
kafkaMessageEncoder=Codificador de mensaje
#XFLD
kafkaMessageCompression=Compresión de mensaje
#XFLD
fileGroupDeltaFilesBy=Agrupar delta por
#XFLD
fileFormat=Tipo de archivo
#XFLD
csvEncoding=Codificación CSV
#XFLD
abapExitLbl=Salida de ABAP
#XFLD
deltaPartition=Recuento de subprocesos de objetos para cargas delta (1-10)
#XFLD
clamping_Data=Error de truncado de datos
#XFLD
fail_On_Incompatible=Error de datos incompatibles
#XFLD
maxPartitionInput=Número máximo de particiones
#XFLD
max_Partition=Definir número máximo de particiones
#XFLD
include_SubFolder=Incluir subcarpetas
#XFLD
fileGlobalPattern=Patrón global para nombre de archivo
#XFLD
fileCompression=Compresión de archivo
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Delimitador de archivo
#XFLD
fileIsHeaderIncluded=Encabezado de archivo
#XFLD
fileOrient=Orient
#XFLD
gbqWriteMode=Modo de escritura
#XFLD
suppressDuplicate=Suprimir duplicados
#XFLD
apacheSpark=Habilitar compatibilidad de Apache Spark
#XFLD
clampingDatatypeCb=Fijar tipos de datos de separador flotante decimal
#XFLD
overwriteDatasetSetting=Sobrescribir opciones de destino en el nivel de objeto
#XFLD
overwriteSourceDatasetSetting=Sobrescribir opciones de origen en el nivel de objeto
#XMSG
kafkaInvalidConnectionSetting=Ingrese un número entre {0} y {1}
#XMSG
MinReplicationThreadErrorMsg=Ingrese un número mayor que {0}.
#XMSG
MaxReplicationThreadErrorMsg=Ingrese un número menor que {0}.
#XMSG
DeltaThreadErrorMsg=Ingrese un valor entre 1 y 10.
#XMSG
MaxPartitionErrorMsg=Ingrese un valor entre 1 <= x <= 2147483647. El valor predeterminado es 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Ingrese un entero entre {0} y {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Usar factor de replicación del corredor de bolsa
#XFLD
serializationFormat=Formato de serialización
#XFLD
compressionType=Tipo de compresión
#XFLD
schemaRegistry=Usar registro de esquema
#XFLD
subjectNameStrat=Estrategia de nombre del evaluado
#XFLD
compatibilityType=Tipo de compatibilidad
#XFLD
confluentTopicName=Nombre del tema
#XFLD
confluentRecordName=Nombre del registro
#XFLD
confluentSubjectNamePreview=Vista previa de nombre del evaluado
#XMSG
serializationChangeToastMsgUpdated2=El formato de serialización cambió a JSON porque el registro de esquema no está activado. Para volver a cambiar el formato de serialización a AVRO, primero debe activar el registro de esquema.
#XBUT
confluentTopicNameInfo=El nombre de tema siempre se basa en el nombre de objeto de destino. Para cambiarlo, cambie el nombre del objeto de destino.
#XMSG
emptyRecordNameValidationHeaderMsg=Ingrese un nombre de registro.
#XMSG
emptyPartionHeader=Ingrese la cantidad de particiones.
#XMSG
invalidPartitionsHeader=Ingrese una cantidad válida de particiones.
#XMSG
invalidpartitionsDesc=Ingrese un número entre 1 y 200.000.
#XMSG
emptyrFactorHeader=Ingrese un factor de replicación.
#XMSG
invalidrFactorHeader=Ingrese un factor de replicación válido.
#XMSG
invalidrFactorDesc=Ingrese un número entre 1 y 32.767.
#XMSG
emptyRecordNameValidationDescMsg=Si se usa el formato de serialización "AVRO", solo se admiten los siguientes caracteres:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(guion bajo)
#XMSG
validRecordNameValidationHeaderMsg=Ingrese un nombre de registro válido.
#XMSG
validRecordNameValidationDescMsgUpdated=Dado que se usa el formato de serialización "AVRO", el nombre de registro solo debe incluir caracteres alfanuméricos (A-Z, a-z, 0-9) y guiones bajos (_). Debe comenzar con una letra o un guion bajo.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=El recuento de subprocesos de objetos para cargas delta se puede establecer tan pronto como uno o más objetos tengan el tipo de carga “Inicial y delta”.
#XMSG
invalidTargetName=Nombre de columna no válido
#XMSG
invalidTargetNameDesc=El nombre de la columna de destino solo debe incluir caracteres alfanuméricos (A-Z, a-z, 0-9) y guiones bajos (_).
#XFLD
consumeOtherSchema=Consumir otras versiones de esquema
#XFLD
ignoreSchemamissmatch=Ignorar disparidad de esquema
#XFLD
confleuntDatatruncation=Error de truncado de datos
#XFLD
isolationLevel=Nivel de aislamiento
#XFLD
confluentOffset=Punto inicial
#XFLD
signavioGroupDeltaFilesByText=Ninguno
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=No
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=No

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Proyecciones
#XBUT
txtAdd=Agregar
#XBUT
txtEdit=Editar
#XMSG
transformationText=Agregue una proyección a la asignación o el filtro de configuración.
#XMSG
primaryKeyRequiredText=Seleccione una clave principal con Configurar esquema.
#XFLD
lblSettings=Opciones
#XFLD
lblTargetSetting={0}: Opciones de destino
#XMSG
@csvRF=Seleccione el archivo que contiene la definición de esquema que quiere aplicar a todos los archivos en la carpeta.
#XFLD
lblSourceColumns=Columnas de origen
#XFLD
lblJsonStructure=Estructura JSON
#XFLD
lblSourceSetting={0}: Opciones de origen
#XFLD
lblSourceSchemaSetting={0}: Opciones de esquema de origen
#XBUT
messageSettings=Configuración de mensajes
#XFLD
lblPropertyTitle1=Propiedades de objeto
#XFLD
lblRFPropertyTitle=Propiedades del flujo de replicación
#XMSG
noDataTxt=No hay columnas para mostrar.
#XMSG
noTargetObjectText=No hay un objeto de destino seleccionado.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Columnas de destino
#XMSG
searchColumns=Buscar columnas
#XTOL
cdcColumnTooltip=Columna para captura delta
#XMSG
sourceNonDeltaSupportErrorUpdated=El objeto de origen no admite la captura delta.
#XMSG
targetCDCColumnAdded=Se agregaron 2 columnas de destino para la captura delta.
#XMSG
deltaPartitionEnable=El límite de subprocesos de objetos para cargas delta se agregó a las opciones de origen.
#XMSG
attributeMappingRemovalTxt=Se están eliminando asignaciones no válidas que no se admiten para el nuevo objeto de destino.
#XMSG
targetCDCColumnRemoved=Se quitaron 2 columnas de destino utilizadas para la captura delta.
#XMSG
replicationLoadTypeChanged=El tipo de carga cambió a "Inicial y delta".
#XMSG
sourceHDLFLoadTypeError=Cambie el tipo de carga a "Inicial y delta".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Para replicar un objeto desde una conexión de origen con el tipo de conexión SAP HANA Cloud, archivos de lago de datos en SAP Datasphere, debe usar el tipo de carga "Inicial y delta".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Habilite la captura delta.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Para replicar un objeto desde una conexión de origen con el tipo de conexión SAP HANA Cloud, archivos de lago de datos en SAP Datasphere, debe habilitar la captura delta.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Cambie el objeto de destino.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=No se puede usar el objeto de destino porque la captura delta no está habilitada. Puede renombrar el objeto de destino (lo que permite que se cree un nuevo objeto con captura delta) o asignarlo a un objeto existente con la captura delta habilitada.
#XMSG
deltaPartitionError=Ingrese un recuento de subprocesos de objetos válido para las cargas delta.
#XMSG
deltaPartitionErrorDescription=Ingrese un valor entre 1 y 10.
#XMSG
deltaPartitionEmptyError=Ingrese un recuento de subprocesos de objetos para las cargas delta.
#XFLD
@lblColumnDescription=Descripción
#XMSG
@lblColumnDescriptionText1=Para propósitos técnico - manejo de registros duplicados causados por problemas durante la replicación de objetos de origen basados en ABAP que no tienen una clave principal.
#XFLD
storageType=Almacenamiento
#XFLD
skipUnmappedColLbl=Omitir columnas no asignadas
#XFLD
abapContentTypeLbl=Tipo de contenido
#XFLD
autoMergeForTargetLbl=Combinar datos automáticamente
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=General
#XFLD
lblBusinessName=Nombre empresarial
#XFLD
lblTechnicalName=Nombre técnico
#XFLD
lblPackage=Paquete
#XFLD
statusPanel=Estado de ejecución
#XBTN: Schedule dropdown menu
SCHEDULE=Programar
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Editar programa
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Eliminar programa
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Crear programa
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Falló la verificación de validación del programa
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=No se puede crear un programa porque el flujo de replicación se está implementando actualmente.{0}Espere hasta que el flujo de replicación se haya implementado.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Para flujos de replicación que contienen objetos con el tipo de carga "inicial y delta", no se puede crear ningún programa.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Para flujos de replicación que contienen objetos con el tipo de carga "Inicial y delta/Solo delta", no se puede crear ningún programa.
#XFLD : Scheduled popover
SCHEDULED=Programado
#XFLD
CREATE_REPLICATION_TEXT=Crear flujo de replicación
#XFLD
EDIT_REPLICATION_TEXT=Editar flujo de replicación
#XFLD
DELETE_REPLICATION_TEXT=Eliminar flujo de replicación
#XFLD
REFRESH_FREQUENCY=Frecuencia
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=El flujo de replicación no se puede implementar porque el programa existente{0} aún no admite el tipo de carga "inicial y delta".{0}{0}Para implementar el flujo de replicación, debe definir los tipos de carga de todos los objetos{0} en "solo inicial". De forma alternativa, puede eliminar el programa, implementar el {0}flujo de replicación y, luego, iniciar una nueva ejecución. Esto luego se ejecutará continuamente{0}, lo que también admite objetos con el tipo de carga "inicial y delta".
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=El flujo de replicación no se puede implementar porque el programa existente{0} aún no admite el tipo de carga "Inicial y delta/Solo delta".{0}{0}Para implementar el flujo de replicación, debe definir los tipos de carga de todos los objetos{0} en "Solo inicial". De forma alternativa, puede eliminar el programa, implementar el {0}flujo de replicación y, luego, iniciar una nueva ejecución. Esto luego se ejecutará continuamente{0}, lo que también admite objetos con el tipo de carga "Inicial y delta/Solo delta".
#XMSG
SCHEDULE_EXCEPTION=Falló la obtención de los detalles del programa
#XFLD: Label for frequency column
everyLabel=Cada
#XFLD: Plural Recurrence text for Hour
hoursLabel=Horas
#XFLD: Plural Recurrence text for Day
daysLabel=Días
#XFLD: Plural Recurrence text for Month
monthsLabel=Meses
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutos
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=No se pudo obtener información sobre la posibilidad de programación.
#XFLD :Paused field
PAUSED=En pausa
#XMSG
navToMonitoring=Abrir en el Supervisor de flujos
#XFLD
statusLbl=Estado
#XFLD
lblLastRunExecuted=Inicio de última ejecución
#XFLD
lblLastExecuted=Última ejecución
#XFLD: Status text for Completed
statusCompleted=Finalizado
#XFLD: Status text for Running
statusRunning=En ejecución
#XFLD: Status text for Failed
statusFailed=Error
#XFLD: Status text for Stopped
statusStopped=Detenido
#XFLD: Status text for Stopping
statusStopping=Detención en curso
#XFLD: Status text for Active
statusActive=Activo
#XFLD: Status text for Paused
statusPaused=En pausa
#XFLD: Status text for not executed
lblNotExecuted=Aún sin ejecutar
#XFLD
messagesSettings=Configuración de mensajes
#XTOL
@validateModel=Mensajes de validación
#XTOL
@hierarchy=Jerarquía
#XTOL
@columnCount=Número de columnas
#XMSG
VAL_PACKAGE_CHANGED=Asignó este objeto al paquete "{1}". Haga clic en "Guardar" para confirmar y validar este cambio. Tenga presente que, luego de guardar este cambio, la asignación a un paquete no se puede deshacer en este editor.
#XMSG
MISSING_DEPENDENCY=Las dependencias del objeto "{0}" no se pueden resolver en el paquete "{1}".
#XFLD
deltaLoadInterval=Intervalo de carga delta
#XFLD
lblHour=Hora (0-24)
#XFLD
lblMinutes=Minutos (0-59)
#XMSG
maxHourOrMinErr=Ingrese un valor entre 0 y {0}.
#XMSG
maxDeltaInterval=El valor máximo del intervalo de carga delta es de 24 horas.{0}Cambie el valor de minuto o de hora de forma acorde.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Ruta de contenedor de destino
#XFLD
confluentSubjectName=Nombre de asunto
#XFLD
confluentSchemaVersion=Versión de esquema
#XFLD
confluentIncludeTechKeyUpdated=Incluir clave técnica
#XFLD
confluentOmitNonExpandedArrays=Omitir matrices no expandidas
#XFLD
confluentExpandArrayOrMap=Expandir matriz o mapa
#XCOL
confluentOperationMapping=Asignación de operaciones
#XCOL
confluentOpCode=Código de operación
#XFLD
confluentInsertOpCode=Insertar
#XFLD
confluentUpdateOpCode=Actualizar
#XFLD
confluentDeleteOpCode=Eliminar
#XFLD
expandArrayOrMapNotSelectedTxt=No seleccionado
#XFLD
confluentSwitchTxtYes=Sí
#XFLD
confluentSwitchTxtNo=No
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Error
#XTIT
executeWarning=Advertencia
#XMSG
executeunsavederror=Guarde su flujo de replicación antes de ejecutarlo.
#XMSG
executemodifiederror=Hay cambios sin guardar en el flujo de replicación. Guarde el flujo de replicación.
#XMSG
executeundeployederror=Debe implementar su flujo de replicación para poder ejecutarlo.
#XMSG
executedeployingerror=Espere a que finalice la implementación.
#XMSG
msgRunStarted=Se inició la ejecución
#XMSG
msgExecuteFail=Se produjo un error al ejecutar el flujo de replicación
#XMSG
titleExecuteBusy=Espere.
#XMSG
msgExecuteBusy=Estamos preparando sus datos para ejecutar el flujo de replicación.
#XTIT
executeConfirmDialog=Advertencia
#XMSG
msgExecuteWithValidations=El flujo de replicación tiene errores de validación. La ejecución del flujo de replicación puede producir un error.
#XMSG
msgRunDeployedVersion=Hay cambios para implementar. Se ejecutará la última versión implementada del flujo de replicación. ¿Desea continuar?
#XBUT
btnExecuteAnyway=Ejecutar de todas maneras
#XBUT
btnExecuteClose=Cerrar
#XBUT
loaderClose=Cerrar
#XTIT
loaderTitle=Cargando
#XMSG
loaderText=Recuperando detalles del servidor
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=El flujo de replicación para esta conexión de destino que no es de SAP no se puede iniciar
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=porque no hay un volumen de salida disponible para este mes.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Un administrador puede incrementar los bloqueos de salida premium para este
#XMSG
premiumOutBoundRFAdminErrMsgPart2=inquilino, lo que haría que el volumen de salida esté disponible para este mes.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Error
#XTIT
deployInfo=Información
#XMSG
deployCheckFailException=Ocurrió una excepción durante la implementación
#XMSG
deployGBQFFDisabled=La implementación de flujos de replicación con una conexión de destino a Google BigQuery actualmente no es posible porque estamos llevando a cabo el mantenimiento de esta función.
#XMSG
deployKAFKAFFDisabled=La implementación de flujos de replicación con una conexión de destino a Apache Kafka actualmente no es posible porque estamos llevando a cabo el mantenimiento de esta función.
#XMSG
deployConfluentDisabled=La implementación de flujos de replicación con una conexión de destino a Confluent Kafka actualmente no es posible porque estamos llevando a cabo el mantenimiento de esta función.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=En los siguientes objetos de destino, los nombres de tablas de capturas delta ya los están utilizando otras tablas del repositorio: {0} Debe renombrar estos objetos de destino para asegurarse de que los nombres de tablas de capturas delta asociados sean únicos antes de poder implementar el flujo de replicación.
#XMSG
deployDWCSourceFFDisabled=La implementación de flujos de replicación que tienen a SAP Datasphere como origen actualmente no es posible porque estamos llevando a cabo el mantenimiento de esta función.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=La implementación de flujos de replicación que contienen tablas locales con habilitación delta como objetos de origen actualmente no es posible porque estamos llevando a cabo el mantenimiento de esta función.
#XMSG
deployHDLFSourceFFDisabled=La implementación de flujos de replicación que tienen conexiones de origen con el tipo de conexión SAP HANA Cloud, archivos de lago de datos actualmente no es posible porque estamos llevando a cabo el mantenimiento.
#XMSG
deployObjectStoreAsSourceFFDisabled=La implementación de flujos de replicación que tiene proveedores de almacenamiento de nube como origen no es posible.
#XMSG
deployConfluentSourceFFDisabled=La implementación de flujos de replicación que tienen Confluent Kafka como origen actualmente no es posible porque estamos llevando a cabo el mantenimiento de esta función.
#XMSG
deployMaxDWCNewTableCrossed=Los flujos de replicación grandes no se pueden "guardar e implementar" de una vez. Guarde su flujo de replicación primero y, luego, impleméntelo.
#XMSG
deployInProgressInfo=La implementación está en curso.
#XMSG
deploySourceObjectInUse=Los objetos de origen {0} ya se usan en flujos de replicación {1}.
#XMSG
deployTargetSourceObjectInUse=Los objetos de origen {0} ya se usan en flujos de replicación {1}. Los objetos de destino {2} ya se usan en flujos de replicación {3}.
#XMSG
deployReplicationFlowCheckError=Error al verificar el flujo de replicación: {0}
#XMSG
preDeployTargetObjectInUse=Los objetos de destino {0} ya se usan en flujos de replicación {1}, y no puede tener el mismo objeto de destino en dos flujos de replicación diferentes. Seleccione otro objeto de destino y vuelva a intentarlo.
#XMSG
runInProgressInfo=El flujo de replicación ya está en ejecución.
#XMSG
deploySignavioTargetFFDisabled=La implementación de flujos de replicación que tienen a SAP Signavio como destino actualmente no es posible porque estamos llevando a cabo el mantenimiento de esta función.
#XMSG
deployHanaViewAsSourceFFDisabled=Actualmente no es posible implementar flujos de replicación que tienen vistas como objetos de origen para la conexión de origen seleccionada. Vuelva a intentarlo más tarde.
#XMSG
deployMsOneLakeTargetFFDisabled=La implementación de flujos de replicación que tienen a MS OneLake como destino actualmente no es posible porque estamos llevando a cabo el mantenimiento de esta función.
#XMSG
deploySFTPTargetFFDisabled=La implementación de flujos de replicación que tienen a SFTP como destino actualmente no es posible porque estamos llevando a cabo el mantenimiento de esta función.
#XMSG
deploySFTPSourceFFDisabled=La implementación de flujos de replicación que tienen a SFTP como origen actualmente no es posible porque estamos llevando a cabo el mantenimiento de esta función.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Nombre técnico
#XFLD
businessNameInRenameTarget=Nombre empresarial
#XTOL
renametargetDialogTitle=Renombrar objeto de destino
#XBUT
targetRenameButton=Renombrar
#XBUT
targetRenameCancel=Cancelar
#XMSG
mandatoryTargetName=Debe ingresar un nombre.
#XMSG
dwcSpecialChar=_(guion bajo) es el único carácter especial permitido.
#XMSG
dwcWithDot=El nombre de tabla de destino puede consistir en letras del alfabeto latino, números, guiones bajos (_) y puntos (.). El primer carácter debe ser una letra, un número o un guion bajo; no puede ser un punto.
#XMSG
nonDwcSpecialChar=Los caracteres especiales permitidos son _(guion bajo) -(guion) .(punto)
#XMSG
firstUnderscorePattern=El nombre no puede comenzar con un _(guion bajo)

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: Ver instrucción SQL de crear tabla
#XMSG
sqlDialogMaxPKWarning=En Google BigQuery, se admite un máximo de 16 claves principales, y el objeto de origen tiene una cantidad mayor. Por lo tanto, no se definen claves principales en esta instrucción.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Una o más columnas de origen tiene tipos de datos que no se pueden definir como claves principales en Google BigQuery. Por lo tanto, no se definen claves principales en este caso. En Google BigQuery, solo los siguientes tipos de datos pueden tener una clave principal: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Copiar y cerrar
#XBUT
closeDDL=Cerrar
#XMSG
copiedToClipboard=Copiado al portapapeles


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=El objeto "{0}" no puede ser parte de una cadena de tareas porque no tiene un fin (debido a que incluye objetos con el tipo de carga Inicial y delta/Solo delta).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=El objeto "{0}" no puede ser parte de una cadena de tareas porque no tiene un fin (debido a que incluye objetos con el tipo de carga Inicial y delta).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=El objeto "{0}" no se puede agregar a la cadena de tareas.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Hay objetos de destino sin guardar. Guarde nuevamente.{0}{0} El comportamiento de esta función cambió: Antes, los objetos de destino solo se creaban en el entorno de destino cuando se implementaba el flujo de replicación.{0} Ahora los objetos ya están creados cuando se guarda el flujo de replicación. Su flujo de replicación se creó antes de este cambio y contiene objetos nuevos.{0} Debe guardar el flujo de replicación una vez más antes de implementarlo para que los objetos nuevos se incluyan correctamente.
#XMSG
confirmChangeContentTypeMessage=Está a punto de cambiar el tipo de contenido. Si lo hace, se eliminarán todas las proyecciones existentes.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Nombre de asunto
#XFLD
schemaDialogVersionName=Versión de esquema
#XFLD
includeTechKey=Incluir clave técnica
#XFLD
segementButtonFlat=Plano
#XFLD
segementButtonNested=Anidado
#XMSG
subjectNamePlaceholder=Buscar nombre de asunto

#XMSG
@EmailNotificationSuccess=Se guarda la configuración de las notificaciones de correo electrónico de tiempo de ejecución.

#XFLD
@RuntimeEmailNotification=Notificación de correo electrónico de tiempo de ejecución

#XBTN
@TXT_SAVE=Guardar


