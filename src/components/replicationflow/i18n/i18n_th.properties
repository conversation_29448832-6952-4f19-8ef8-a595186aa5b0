#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=ผังการทำสำเนา

#XFLD: Edit Schema button text
editSchema=แก้ไข Schema

#XTIT : Properties heading
configSchema=กำหนดรูปแบบ Schema

#XFLD: save changed button text
applyChanges=นำการเปลี่ยนแปลงไปใช้


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=เลือกการเชื่อมต่อต้นทาง
#XFLD
sourceContainernEmptyText=เลือกคอนเทนเนอร์
#XFLD
targetConnectionEmptyText=เลือกการเชื่อมต่อเป้าหมาย
#XFLD
targetContainernEmptyText=เลือกคอนเทนเนอร์
#XFLD
sourceSelectObjectText=เลือกออบเจคต้นทาง
#XFLD
sourceObjectCount=ออบเจคต้นทาง ({0})
#XFLD
targetObjectText=ออบเจคเป้าหมาย
#XFLD
confluentBrowseContext=เลือกเนื้อหา
#XBUT
@retry=ลองใหม่
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=การอัพเกรด Tenant อยู่ระหว่างดำเนินการ

#XTOL
browseSourceConnection=บราวซ์การเชื่อมต่อต้นทาง
#XTOL
browseTargetConnection=บราวซ์การเชื่อมต่อเป้าหมาย
#XTOL
browseSourceContainer=บราวซ์คอนเทนเนอร์ต้นทาง
#XTOL
browseAndAddSourceDataset=เพิ่มออบเจคต้นทาง
#XTOL
browseTargetContainer=บราวซ์คอนเทนเนอร์เป้าหมาย
#XTOL
browseTargetSetting=บราวซ์การกำหนดค่าเป้าหมาย
#XTOL
browseSourceSetting=บราวซ์การกำหนดค่าแหล่งที่มา
#XTOL
sourceDatasetInfo=ข้อมูล
#XTOL
sourceDatasetRemove=ย้ายออก
#XTOL
mappingCount=รายการนี้จะแสดงจำนวนรวมของการแม็ป/นิพจน์ที่ไม่ได้อิงตามชื่อ
#XTOL
filterCount=รายการนี้จะแสดงจำนวนรวมของเงื่อนไขการฟิลเตอร์
#XTOL
loading=กำลังโหลด...
#XCOL
deltaCapture=การจับข้อมูลเดลต้า
#XCOL
deltaCaptureTableName=ตารางการจับข้อมูลเดลต้า
#XCOL
loadType=ประเภทการโหลด
#XCOL
deleteAllBeforeLoading=ลบทั้งหมดก่อนโหลด
#XCOL
transformationsTab=การเลือกเฉพาะส่วน
#XCOL
settingsTab=การกำหนดค่า

#XBUT
renameTargetObjectBtn=เปลี่ยนชื่อออบเจคเป้าหมาย
#XBUT
mapToExistingTargetObjectBtn=แม็ปไปยังออบเจคเป้าหมายที่มีอยู่
#XBUT
changeContainerPathBtn=เปลี่ยนแปลงพาธของคอนเทนเนอร์
#XBUT
viewSQLDDLUpdated=ดูคำสั่งสร้างตารางของ SQL
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=ออบเจคต้นทางไม่รองรับการจับข้อมูลเดลต้า แต่ออบเจคเป้าหมายที่เลือกมีการเปิดใช้งานตัวเลือกการจับข้อมูลเดลต้า
#XMSG
sourceObjectNonDeltaSupportErrorMsg=ไม่สามารถใช้ออบเจคเป้าหมายได้เนื่องจากมีการเปิดใช้งานการจับข้อมูลเดลต้า{0}ในขณะที่ออบเจคต้นทางไม่รองรับการจับข้อมูลเดลต้า{1}คุณสามารถเลือกออบเจคเป้าหมายอื่นที่ไม่รองรับการจับข้อมูลเดลต้า
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=มีออบเจคเป้าหมายที่มีชื่อนี้อยู่แล้ว แต่ไม่สามารถใช้งานได้{0}เนื่องจากมีการเปิดใช้งานการจับข้อมูลเดลต้า ในขณะที่ออบเจคต้นทางไม่{0}รองรับการจับข้อมูลเดลต้า{1}คุณสามารถป้อนชื่อของออบเจคเป้าหมายที่มีอยู่ซึ่งไม่{0}รองรับการจับข้อมูลเดลต้า หรือป้อนชื่อที่ยังไม่มีอยู่
#XBUT
copySQLDDLUpdated=คัดลอกคำสั่งสร้างตารางของ SQL
#XMSG
targetObjExistingNoCDCColumnUpdated=ตารางที่มีอยู่ใน Google BigQuery ต้องรวมคอลัมน์ต่อไปนี้สำหรับการจับข้อมูลการเปลี่ยนแปลง (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=ออบเจคต้นทางต่อไปนี้ไม่ได้รับการสนับสนุนเนื่องจากไม่มีคีย์หลักหรือกำลังใช้การเชื่อมต่อที่ไม่ตรงตามเงื่อนไขในการดึงข้อมูลคีย์หลัก:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=กรุณาดู SAP KBA 3531135 เพื่อดูวิธีการแก้ไขปัญหาที่เป็นไปได้
#XLST: load type list values
initial=เริ่มต้นเท่านั้น
@emailUpdateError=เกิดข้อผิดพลาดในการอัพเดทรายการการแจ้งทางอีเมล์

#XLST
initialDelta=เริ่มต้นและเดลต้า

#XLST
deltaOnly=เดลต้าเท่านั้น
#XMSG
confluentDeltaLoadTypeInfo=สำหรับแหล่งข้อมูล Confluent Kafka รองรับเฉพาะประเภทการโหลด 'เริ่มต้นและเดลต้า'
#XMSG
confirmRemoveReplicationObject=คุณยืนยันว่าต้องการลบการทำสำเนาหรือไม่?
#XMSG
confirmRemoveReplicationTaskPrompt=การดำเนินการนี้จะลบการทำสำเนาที่มีอยู่ออก คุณต้องการดำเนินการต่อหรือไม่?
#XMSG
confirmTargetConnectionChangePrompt=การดำเนินการนี้จะรีเซ็ตการเชื่อมต่อเป้าหมาย คอนเทนเนอร์เป้าหมาย และจะลบออบเจคเป้าหมายทั้งหมด คุณต้องการดำเนินการต่อหรือไม่?
#XMSG
confirmTargetContainerChangePrompt=การดำเนินการนี้จะรีเซ็ตคอนเทนเนอร์เป้าหมายและจะลบออบเจคเป้าหมายทั้งหมดที่มีอยู่ คุณต้องการดำเนินการต่อหรือไม่?
#XMSG
confirmRemoveTransformObject=คุณยืนยันว่าต้องการลบการเลือกเฉพาะส่วน {0} หรือไม่?
#XMSG
ErrorMsgContainerChange=มีข้อผิดพลาดเกิดขึ้นขณะเปลี่ยนแปลงพาธของคอนเทนเนอร์
#XMSG
infoForUnsupportedDatasetNoKeys=ออบเจคต้นทางต่อไปนี้ไม่ได้รับการสนับสนุนเนื่องจากไม่มีคีย์หลัก:
#XMSG
infoForUnsupportedDatasetView=ออบเจคต้นทางประเภท 'มุมมอง' ต่อไปนี้ไม่ได้รับการสนับสนุน:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=ออบเจคต้นทางต่อไปนี้ไม่ได้รับการสนับสนุนเนื่องจากเป็นมุมมอง SQL ที่มีพารามิเตอร์ป้อนข้อมูล:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=ออบเจคต้นทางต่อไปนี้ไม่ได้รับการสนับสนุนเนื่องจากการแยกข้อมูลถูกปิดใช้งานสำหรับออบเจคดังกล่าว:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=สำหรับการเชื่อมต่อ Confluent รูปแบบการซีเรียลไลซ์ที่อนุญาตมีเพียง AVRO และ JSON เท่านั้น ออบเจคต่อไปนี้ไม่ได้รับการสนับสนุนเนื่องจากใช้รูปแบบการซีเรียลไลซ์ที่ต่างกัน:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=ไม่สามารถดึงข้อมูล Schema สำหรับออบเจคต่อไปนี้ กรุณาเลือกเนื้อหาที่เหมาะสมหรือตรวจสอบการกำหนดรูปแบบ Schema Registry
#XTOL: warning dialog header on deleting replication task
deleteHeader=ลบ
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=การกำหนดค่า 'ลบทั้งหมดก่อนโหลด' ไม่ได้รับการสนับสนุนสำหรับ Google BigQuery
#XBUT
DeleteAllBeforeLoadingConfluentInfo=การกำหนดค่า 'ลบทั้งหมดก่อนโหลด' จะลบและจะสร้างออบเจค (หัวข้อ) ใหม่ก่อนการทำสำเนาแต่ละครั้ง โดยจะลบข้อความทั้งหมดที่กำหนดไว้ด้วย
#XTOL
DeleteAllBeforeLoadingLTFInfo=การกำหนดค่า 'ลบทั้งหมดก่อนโหลด' ไม่ได้รับการสนับสนุนสำหรับประเภทเป้าหมายนี้
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=ชื่อทางเทคนิค
#XCOL
connBusinessName=ชื่อทางธุรกิจ
#XCOL
connDescriptionName=คำอธิบาย
#XCOL
connType=ประเภท
#XMSG
connTblNoDataFoundtxt=ไม่พบการเชื่อมต่อ
#XMSG
connectionError=มีข้อผิดพลาดเกิดขึ้นขณะดึงข้อมูลการเชื่อมต่อ
#XMSG
connectionCombinationUnsupportedErrorTitle=การรวมการเชื่อมต่อไม่ได้รับการสนับสนุน
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=การทำสำเนาจาก {0} ไปยัง {1} ยังไม่ได้รับการสนับสนุนในตอนนี้
#XMSG
invalidTargetforSourceHDLFErrorTitle=การรวมประเภทการเชื่อมต่อไม่ได้รับการสนับสนุน
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=การทำสำเนาจากการเชื่อมต่อที่มีประเภทการเชื่อมต่อ SAP HANA Cloud, ไฟล์ Data Lake ไปยัง {0} ไม่ได้รับการสนับสนุน คุณสามารถทำสำเนาไปยัง SAP Datasphere ได้เพียงอย่างเดียว

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=เลือก
#XBUT
containerCancelBtn=ยกเลิก
#XTOL
containerSelectTooltip=เลือก
#XTOL
containerCancelTooltip=ยกเลิก
#XMSG
containerContainerPathPlcHold=พาธของคอนเทนเนอร์
#XFLD
containerContainertxt=คอนเทนเนอร์
#XFLD
confluentContainerContainertxt=เนื้อหา
#XMSG
infoMessageForSLTSelection=อนุญาตให้ใช้เฉพาะ /SLT/ID การโอนจำนวนมากเป็นคอนเทนเนอร์ เลือก ID การโอนจำนวนมากภายใต้ SLT (ถ้ามี) แล้วคลิก 'ส่ง'
#XMSG
msgFetchContainerFail=มีข้อผิดพลาดเกิดขึ้นขณะดึงข้อมูลคอนเทนเนอร์
#XMSG
infoMessageForSLTHidden=การเชื่อมต่อนี้ไม่รองรับแฟ้ม SLT ดังนั้นจะไม่ปรากฏในรายการด้านล่าง

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=เลือกคอนเทนเนอร์ที่มีแฟ้มย่อย
#XMSG
sftpIncludeSubFolderText=เท็จ
#XMSG
sftpIncludeSubFolderTextNew=ไม่ใช่

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(ยังไม่มีการแม็ปฟิลเตอร์)
#XMSG
failToFetchRemoteMetadata=มีข้อผิดพลาดเกิดขึ้นขณะดึงข้อมูลเมต้าดาต้า
#XMSG
failToFetchData=มีข้อผิดพลาดเกิดขึ้นขณะดึงข้อมูลเป้าหมายที่มีอยู่
#XCOL
@loadType=ประเภทการโหลด
#XCOL
@deleteAllBeforeLoading=ลบทั้งหมดก่อนโหลด

#XMSG
@loading=กำลังโหลด...
#XFLD
@selectSourceObjects=เลือกออบเจคต้นทาง
#XMSG
@exceedLimit=คุณไม่สามารถอิมปอร์ตออบเจคมากกว่า {0} รายการพร้อมกัน กรุณายกเลิกการเลือกออบเจคอย่างน้อย {1} รายการ
#XFLD
@objects=ออบเจค
#XBUT
@ok=ตกลง
#XBUT
@cancel=ยกเลิก
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=ถัดไป
#XBUT
btnAddSelection=เพิ่มการเลือก
#XTOL
@remoteFromSelection=ย้ายออกจากการเลือก
#XMSG
@searchInForSearchField=ค้นหาใน: {0}

#XCOL
@name=ชื่อทางเทคนิค
#XCOL
@type=ประเภท
#XCOL
@location=ที่ตั้ง
#XCOL
@label=ชื่อทางธุรกิจ
#XCOL
@status=สถานะ

#XFLD
@searchIn=ค้นหาใน:
#XBUT
@available=พร้อมใช้งาน
#XBUT
@selection=การเลือก

#XFLD
@noSourceSubFolder=ตารางและมุมมอง
#XMSG
@alreadyAdded=มีอยู่แล้วในไดอะแกรม
#XMSG
@askForFilter=มีรายการมากกว่า {0} รายการ กรุณาป้อนสตริงฟิลเตอร์เพื่อจำกัดจำนวนรายการให้แคบลง
#XFLD: success label
lblSuccess=สำเร็จ
#XFLD: ready label
lblReady=พร้อม
#XFLD: failure label
lblFailed=ล้มเหลว
#XFLD: fetching status label
lblFetchingDetail=กำลังดึงข้อมูลรายละเอียด

#XMSG Place holder text for tree filter control
filterPlaceHolder=พิมพ์ข้อความเพื่อฟิลเตอร์ออบเจคระดับบนสุด
#XMSG Place holder text for server search control
serverSearchPlaceholder=พิมพ์แล้วกด Enter เพื่อค้นหา
#XMSG
@deployObjects=กำลังอิมปอร์ต {0} ออบเจค...
#XMSG
@deployObjectsStatus=จำนวนออบเจคที่ถูกอิมปอร์ต: {0} จำนวนออบเจคที่ไม่สามารถอิมปอร์ต: {1}

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=ไม่สามารถเปิดบราวเซอร์พื้นที่เก็บข้อมูลภายใน
#XMSG
@openRemoteSourceBrowserError=ไม่สามารถดึงข้อมูลออบเจคต้นทาง
#XMSG
@openRemoteTargetBrowserError=ไม่สามารถดึงข้อมูลออบเจคเป้าหมาย
#XMSG
@validatingTargetsError=มีข้อผิดพลาดเกิดขึ้นขณะตรวจสอบความถูกต้องของเป้าหมาย
#XMSG
@waitingToImport=พร้อมอิมปอร์ต

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=เกินจำนวนสูงสุดของออบเจคแล้ว กรุณาเลือกออบเจคสูงสุด 500 รายการสำหรับผังการทำสำเนาหนึ่งผัง

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=ชื่อทางเทคนิค
#XFLD
sourceObjectBusinessName=ชื่อทางธุรกิจ
#XFLD
sourceNoColumns=จำนวนคอลัมน์
#XFLD
containerLbl=คอนเทนเนอร์

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=คุณต้องเลือกการเชื่อมต่อต้นทางสำหรับผังการทำสำเนา
#XMSG
validationSourceContainerNonExist=คุณต้องเลือกคอนเทนเนอร์สำหรับการเชื่อมต่อต้นทาง
#XMSG
validationTargetNonExist=คุณต้องเลือกการเชื่อมต่อเป้าหมายสำหรับผังการทำสำเนา
#XMSG
validationTargetContainerNonExist=คุณต้องเลือกคอนเทนเนอร์สำหรับการเชื่อมต่อเป้าหมาย
#XMSG
validationTruncateDisabledForObjectTitle=การทำสำเนาไปยังพื้นที่จัดเก็บออบเจค
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=การทำสำเนาไปยังพื้นที่จัดเก็บบน Cloud จะทำได้ก็ต่อเมื่อมีการกำหนดค่าตัวเลือก ''ลบทั้งหมดก่อนโหลด'' ไว้หรือไม่มีออบเจคเป้าหมายในเป้าหมาย{0}{0} เมื่อต้องการคงการเปิดใช้งานการทำสำเนาสำหรับออบเจคที่ไม่ได้กำหนดค่าตัวเลือก ''ลบทั้งหมดก่อนโหลด'' ไว้ ให้ตรวจสอบให้แน่ใจว่าไม่มีออบเจคเป้าหมายอยู่ในระบบก่อนที่คุณจะดำเนินการผังการทำสำเนา
#XMSG
validationTaskNonExist=คุณต้องมีการทำสำเนาอย่างน้อยหนึ่งรายการในผังการทำสำเนา
#XMSG
validationTaskTargetMissing=คุณต้องมีเป้าหมายสำหรับการทำสำเนาด้วยแหล่งข้อมูล: {0}
#XMSG
validationTaskTargetIsSAC=เป้าหมายที่เลือกคืออาร์ทิแฟกต์ SAC: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=เป้าหมายที่เลือกไม่ใช่ตารางภายในที่รองรับ: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=ออบเจคที่มีชื่อนี้มีอยู่แล้วในเป้าหมาย แต่ออบเจคนี้ไม่สามารถใช้เป็นออบเจคเป้าหมายสำหรับผังการทำสำเนาไปยังพื้นที่เก็บข้อมูลภายในได้เนื่องจากไม่ใช่ตารางภายใน
#XMSG
validateSourceTargetSystemDifference=คุณต้องเลือกการเชื่อมต่อต้นทางและการเชื่อมต่อเป้าหมายอื่นรวมถึงการรวมคอนเทนเนอร์สำหรับผังการทำสำเนา
#XMSG
validateDuplicateSources=การทำสำเนาอย่างน้อยหนึ่งรายการมีชื่อออบเจคต้นทางที่ซ้ำกัน: {0}
#XMSG
validateDuplicateTargets=การทำสำเนาอย่างน้อยหนึ่งรายการมีชื่อออบเจคเป้าหมายที่ซ้ำกัน: {0}
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=ออบเจคต้นทาง {0} ไม่รองรับการจับข้อมูลเดลต้าขณะที่ออบเจคเป้าหมาย {1} รองรับ คุณต้องย้ายการทำสำเนาออก
#XMSG
validationTaskTargetObjectLoadTypeMismatch=คุณต้องเลือกประเภทการโหลด "เริ่มต้นและเดลต้า" สำหรับการทำสำเนาด้วยชื่อออบเจคเป้าหมาย {0}
#XMSG
validationAutoRenameTarget=เปลี่ยนชื่อคอลัมน์เป้าหมายแล้ว
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=เพิ่มการเลือกเฉพาะส่วนอัตโนมัติแล้ว และมีการเปลี่ยนชื่อคอลัมน์เป้าหมายต่อไปนี้เพื่อให้สามารถทำสำเนาไปยังเป้าหมายได้:{1}{1} {0} {1}{1}เนื่องจากสาเหตุใดสาเหตุหนึ่งต่อไปนี้:{1}{1}{2} อักขระที่ไม่ได้รับการสนับสนุน{1}{2} คำนำหน้าที่สงวนไว้
#XMSG
validationAutoRenameTargetDescriptionUpdated=เพิ่มการเลือกเฉพาะส่วนอัตโนมัติแล้ว และมีการเปลี่ยนชื่อคอลัมน์เป้าหมายต่อไปนี้เพื่อให้สามารถทำสำเนาไปยัง Google BigQuery ได้:{1}{1} {0} {1}{1}เนื่องจากสาเหตุใดสาเหตุหนึ่งต่อไปนี้:{1}{1}{2} ชื่อคอลัมน์ที่สงวนไว้{1}{2} อักขระที่ไม่ได้รับการสนับสนุน{1}{2} คำนำหน้าที่สงวนไว้
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=เพิ่มการเลือกเฉพาะส่วนอัตโนมัติแล้ว และมีการเปลี่ยนชื่อคอลัมน์เป้าหมายต่อไปนี้เพื่อให้สามารถทำสำเนาไปยัง Confluent ได้:{1}{1} {0} {1}{1}เนื่องจากสาเหตุใดสาเหตุหนึ่งต่อไปนี้:{1}{1}{2} ชื่อคอลัมน์ที่สงวนไว้{1}{2} อักขระที่ไม่ได้รับการสนับสนุน{1}{2} คำนำหน้าที่สงวนไว้
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=เพิ่มการเลือกเฉพาะส่วนอัตโนมัติแล้ว และมีการเปลี่ยนชื่อคอลัมน์เป้าหมายต่อไปนี้เพื่อให้สามารถทำสำเนาไปยังเป้าหมายได้:{1}{1} {0} {1}{1}เนื่องจากสาเหตุใดสาเหตุหนึ่งต่อไปนี้:{1}{1}{2} ชื่อคอลัมน์ที่สงวนไว้{1}{2} อักขระที่ไม่ได้รับการสนับสนุน{1}{2} คำนำหน้าที่สงวนไว้
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=เปลี่ยนชื่อออบเจคเป้าหมายแล้ว
#XMSG
autoRenameInfoDesc=ออบเจคเป้าหมายถูกเปลี่ยนชื่อเนื่องจากมีอักขระที่ไม่รองรับ โดยจะรองรับเฉพาะอักขระต่อไปนี้เท่านั้น:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}. (จุด){0}{1}_ (เครื่องหมายขีดล่าง){0}{1}- (เครื่องหมายเส้นประ)
#XMSG
validationAutoTargetTypeConversion=เปลี่ยนแปลงประเภทข้อมูลเป้าหมายแล้ว
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=สำหรับคอลัมน์เป้าหมายต่อไปนี้ ประเภทข้อมูลเป้าหมายมีการเปลี่ยนแปลงเนื่องจากใน Google BigQuery ไม่รองรับประเภทข้อมูลต้นฉบับ:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=สำหรับคอลัมน์เป้าหมายต่อไปนี้ ประเภทข้อมูลเป้าหมายมีการเปลี่ยนแปลงเนื่องจากประเภทข้อมูลต้นฉบับไม่ได้รับการสนับสนุนในการเชื่อมต่อเป้าหมาย:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=ย่อชื่อคอลัมน์เป้าหมาย
#XMSG
validationMaxCharLengthGBQTargetDescription=ใน Google BigQuery ชื่อคอลัมน์สามารถใช้อักขระได้สูงสุด 300 ตัว ใช้การเลือกเฉพาะส่วนเพื่อย่อชื่อคอลัมน์เป้าหมายต่อไปนี้:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=คีย์หลักจะไม่ถูกสร้างขึ้น
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=ใน Google BigQuery รองรับคีย์หลักสูงสุด 16 คีย์ แต่ออบเจคต้นทางมีคีย์หลักจำนวนมาก จะไม่มีการสร้างคีย์หลักในออบเจคเป้าหมาย
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=คอลัมน์ต้นทางอย่างน้อยหนึ่งคอลัมน์มีประเภทข้อมูลที่ไม่สามารถกำหนดเป็นคีย์หลักใน Google BigQuery จะไม่มีการสร้างคีย์หลักในออบเจคเป้าหมาย{0}{0} ประเภทข้อมูลเป้าหมายต่อไปนี้เข้ากันได้กับประเภทข้อมูลของ Google BigQuery ซึ่งสามารถกำหนดคีย์หลักได้:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=กำหนดคอลัมน์อย่างน้อยหนึ่งคอลัมน์เป็นคีย์หลัก
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=คุณต้องกำหนดคอลัมน์อย่างน้อยหนึ่งคอลัมน์เป็นคีย์หลักโดยใช้ไดอะลอก Schema แหล่งข้อมูลเพื่อดำเนินการนี้
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=กำหนดคอลัมน์อย่างน้อยหนึ่งคอลัมน์เป็นคีย์หลัก
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=คุณต้องกำหนดคอลัมน์อย่างน้อยหนึ่งคอลัมน์เป็นคีย์หลักที่ตรงกับข้อจำกัดของคีย์หลักสำหรับออบเจคต้นทางของคุณ ไปที่ 'กำหนดรูปแบบ Schema' ในคุณสมบัติของออบเจคต้นทางของคุณเพื่อดำเนินการดังกล่าว
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=ป้อนค่าพาร์ทิชันสูงสุดที่ถูกต้อง
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=ค่าพาร์ทิชันสูงสุดต้องเป็น ≥ 1 และ ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=กำหนดคอลัมน์อย่างน้อยหนึ่งคอลัมน์เป็นคีย์หลัก
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=เมื่อต้องการทำสำเนาออบเจค คุณต้องกำหนดคอลัมน์เป้าหมายอย่างน้อยหนึ่งคอลัมน์เป็นคีย์หลัก ใช้การเลือกเฉพาะส่วนเพื่อดำเนินการนี้
#XMSG
validateHDLFNoPKExistingDatasetError=กำหนดคอลัมน์อย่างน้อยหนึ่งคอลัมน์เป็นคีย์หลัก
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=เมื่อต้องการทำสำเนาข้อมูลไปยังออบเจคเป้าหมายที่มีอยู่ ต้องกำหนดคอลัมน์อย่างน้อยหนึ่งคอลัมน์เป็นคีย์หลัก {0} คุณมีตัวเลือกต่อไปนี้สำหรับการกำหนดคอลัมน์อย่างน้อยหนึ่งคอลัมน์เป็นคีย์หลัก: {0} {1}ใช้เอดิเตอร์ตารางภายในเพื่อเปลี่ยนแปลงออบเจคเป้าหมายที่มีอยู่ จากนั้นรีโหลดผังการทำสำเนา{0}{1}เปลี่ยนชื่อออบเจคเป้าหมายในผังการทำสำเนา การดำเนินการนี้จะสร้างออบเจคใหม่ทันทีที่การดำเนินการเริ่มต้นขึ้น หลังเปลี่ยนชื่อแล้ว คุณสามารถกำหนดคอลัมน์อย่างน้อยหนึ่งคอลัมน์เป็นคีย์หลักในการเลือกเฉพาะส่วน{0}{1}แม็ปออบเจคไปยังออบเจคเป้าหมายอื่นที่มีอยู่ซึ่งมีการกำหนดคอลัมน์อย่างน้อยหนึ่งคอลัมน์เป็นคีย์หลักแล้ว
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=เป้าหมายที่เลือกมีอยู่แล้วในพื้นที่เก็บข้อมูล: {0}
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=ชื่อตารางการจับข้อมูลเดลต้าถูกใช้โดยตารางอื่นในพื้นที่เก็บข้อมูลแล้ว: {0} คุณต้องเปลี่ยนชื่อออบเจคเป้าหมายเหล่านี้เพื่อให้แน่ใจว่าชื่อตารางการจับข้อมูลเดลต้าที่เกี่ยวข้องไม่ซ้ำกันก่อนที่คุณจะเก็บบันทึกผังการทำสำเนา
#XMSG
validateConfluentEmptySchema=กำหนด Schema
#XMSG
validateConfluentEmptySchemaDescUpdated=ตารางต้นทางไม่มี Schema กรุณาเลือก 'กำหนดรูปแบบ Schema' เพื่อกำหนด
#XMSG
validationCSVEncoding=การเข้ารหัส CSV ไม่ถูกต้อง
#XMSG
validationCSVEncodingDescription=การเข้ารหัส CSV ของงานไม่ถูกต้อง
#XMSG
validateConfluentEmptySchema=เลือกประเภทข้อมูลเป้าหมายที่เข้ากันได้
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=เลือกประเภทข้อมูลเป้าหมายที่เข้ากันได้
#XMSG
globalValidateTargetDataTypeDesc=มีข้อผิดพลาดเกิดขึ้นกับการแม็ปคอลัมน์ ไปที่ 'การเลือกเฉพาะส่วน' และตรวจสอบให้แน่ใจว่าคอลัมน์ต้นทางทั้งหมดถูกแม็ปไปยังคอลัมน์ที่ไม่ซ้ำกัน โดยมีคอลัมน์ที่มีประเภทข้อมูลที่เข้ากันได้ และนิพจน์ทั้งหมดที่กำหนดไว้ถูกต้อง
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=ชื่อคอลัมน์ซ้ำกัน
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=ไม่รองรับชื่อคอลัมน์ที่ซ้ำกัน ใช้ไดอะลอกการเลือกเฉพาะส่วนเพื่อแก้ไขปัญหาดังกล่าว ออบเจคเป้าหมายต่อไปนี้มีชื่อคอลัมน์ที่ซ้ำกัน: {0}
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=ชื่อคอลัมน์ซ้ำกัน
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=ไม่รองรับชื่อคอลัมน์ที่ซ้ำกัน ออบเจคเป้าหมายต่อไปนี้มีชื่อคอลัมน์ที่ซ้ำกัน: {0}
#XMSG
deltaOnlyLoadTypeTittle=อาจมีความไม่สอดคล้องกันในข้อมูล
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=ประเภทการโหลด 'เดลต้าเท่านั้น' จะไม่พิจารณาการเปลี่ยนแปลงที่ทำในต้นทางระหว่างการเก็บบันทึกล่าสุดกับการดำเนินการครั้งถัดไป
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=เปลี่ยนประเภทการโหลดเป็น "เริ่มต้น"
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=การทำสำเนาออบเจคที่ใช้ภาษา ABAP ที่ไม่มีคีย์หลักสามารถทำได้เฉพาะกับประเภทการโหลด "เริ่มต้นเท่านั้น"
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=ปิดใช้งานการจับข้อมูลเดลต้า
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=เมื่อต้องการทำสำเนาออบเจคที่ไม่มีคีย์หลักโดยใช้ประเภทการเชื่อมต่อแหล่งข้อมูล ABAP คุณต้องปิดใช้งานการจับข้อมูลเดลต้าสำหรับตารางนี้ก่อน
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=เปลี่ยนแปลงออบเจคเป้าหมาย
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=ไม่สามารถใช้ออบเจคเป้าหมายได้เนื่องจากการจับข้อมูลเดลต้าถูกเปิดใช้งาน คุณสามารถเปลี่ยนชื่อออบเจคเป้าหมายแล้วปิดการจับข้อมูลเดลต้าสำหรับออบเจค (ที่เปลี่ยนชื่อ) ใหม่ หรือแม็ปออบเจคต้นทางไปยังออบเจคเป้าหมายที่มีการปิดใช้งานการจับข้อมูลเดลต้า
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=เปลี่ยนแปลงออบเจคเป้าหมาย
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=ไม่สามารถใช้ออบเจคเป้าหมายได้เนื่องจากไม่มีคอลัมน์ทางเทคนิคที่ต้องการ (__load_package_id) คุณสามารถเปลี่ยนชื่อออบเจคเป้าหมายโดยใช้ชื่อที่ยังไม่มีอยู่ จากนั้นระบบจะสร้างออบเจคใหม่ที่มีข้อกำหนดเดียวกันกับออบเจคต้นทางและมีคอลัมน์ทางเทคนิค หรือคุณจะแม็ปออบเจคเป้าหมายไปยังออบเจคที่มีอยู่ซึ่งมีคอลัมน์ทางเทคนิคที่ต้องการ (__load_package_id) ก็ได้เช่นกัน
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=เปลี่ยนแปลงออบเจคเป้าหมาย
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=ไม่สามารถใช้ออบเจคเป้าหมายได้เนื่องจากไม่มีคอลัมน์ทางเทคนิคที่ต้องการ (__load_record_id) คุณสามารถเปลี่ยนชื่อออบเจคเป้าหมายโดยใช้ชื่อที่ยังไม่มีอยู่ จากนั้นระบบจะสร้างออบเจคใหม่ที่มีข้อกำหนดเดียวกันกับออบเจคต้นทางและมีคอลัมน์ทางเทคนิค หรือคุณจะแม็ปออบเจคเป้าหมายไปยังออบเจคที่มีอยู่ซึ่งมีคอลัมน์ทางเทคนิคที่ต้องการ (__load_record_id) ก็ได้เช่นกัน
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=เปลี่ยนแปลงออบเจคเป้าหมาย
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=ไม่สามารถใช้ออบเจคเป้าหมายได้เนื่องจากประเภทข้อมูลของคอลัมน์ทางเทคนิค (__load_record_id) ไม่ใช่ "string(44)" คุณสามารถเปลี่ยนชื่อออบเจคเป้าหมายโดยใช้ชื่อที่ยังไม่มีอยู่ จากนั้นระบบจะสร้างออบเจคใหม่ที่มีข้อกำหนดเดียวกันกับออบเจคต้นทางและมีประเภทข้อมูลที่ถูกต้อง หรือคุณจะแม็ปออบเจคเป้าหมายไปยังออบเจคที่มีอยู่ซึ่งมีคอลัมน์ทางเทคนิคที่ต้องการ (__load_record_id) ด้วยประเภทข้อมูลที่ถูกต้องก็ได้เช่นกัน
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=เปลี่ยนแปลงออบเจคเป้าหมาย
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=ไม่สามารถใช้ออบเจคเป้าหมายได้เนื่องจากมีคีย์หลัก ขณะที่ออบเจคต้นทางไม่มี คุณสามารถเปลี่ยนชื่อออบเจคเป้าหมายโดยใช้ชื่อที่ยังไม่มีอยู่ จากนั้นระบบจะสร้างออบเจคใหม่ที่มีข้อกำหนดเดียวกันกับออบเจคต้นทางและไม่มีคีย์หลัก หรือคุณจะแม็ปออบเจคเป้าหมายไปยังออบเจคที่มีอยู่ซึ่งมีคอลัมน์ทางเทคนิคที่ต้องการ (__load_package_id) และไม่มีคีย์หลักก็ได้เช่นกัน
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=เปลี่ยนแปลงออบเจคเป้าหมาย
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=ไม่สามารถใช้ออบเจคเป้าหมายได้เนื่องจากมีคีย์หลัก ขณะที่ออบเจคต้นทางไม่มี คุณสามารถเปลี่ยนชื่อออบเจคเป้าหมายโดยใช้ชื่อที่ยังไม่มีอยู่ จากนั้นระบบจะสร้างออบเจคใหม่ที่มีข้อกำหนดเดียวกันกับออบเจคต้นทางและไม่มีคีย์หลัก หรือคุณจะแม็ปออบเจคเป้าหมายไปยังออบเจคที่มีอยู่ซึ่งมีคอลัมน์ทางเทคนิคที่ต้องการ (__load_record_id) และไม่มีคีย์หลักก็ได้เช่นกัน
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=เปลี่ยนแปลงออบเจคเป้าหมาย
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=ไม่สามารถใช้ออบเจคเป้าหมายได้เนื่องจากประเภทข้อมูลของคอลัมน์ทางเทคนิค (__load_package_id) ไม่ใช่ "binary(>=256)" คุณสามารถเปลี่ยนชื่อออบเจคเป้าหมายโดยใช้ชื่อที่ยังไม่มีอยู่ จากนั้นระบบจะสร้างออบเจคใหม่ที่มีข้อกำหนดเดียวกันกับออบเจคต้นทางและมีประเภทข้อมูลที่ถูกต้อง หรือคุณจะแม็ปออบเจคเป้าหมายไปยังออบเจคที่มีอยู่ซึ่งมีคอลัมน์ทางเทคนิคที่ต้องการ (__load_package_id) ด้วยประเภทข้อมูลที่ถูกต้องก็ได้เช่นกัน
#XMSG
validationAutoRenameTargetDPID=เปลี่ยนชื่อคอลัมน์เป้าหมายแล้ว
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=ย้ายออบเจคต้นทางออก
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=ออบเจคต้นทางไม่มีคอลัมน์ของคีย์ ซึ่งไม่ได้รับการสนับสนุนในเนื้อหานี้
#XMSG
validationAutoRenameTargetDPIDDescription=เพิ่มการเลือกเฉพาะส่วนอัตโนมัติแล้ว และมีการเปลี่ยนชื่อคอลัมน์เป้าหมายต่อไปนี้เพื่อให้สามารถทำสำเนาจากแหล่งข้อมูล ABAP ที่ไม่มีคีย์:{1}{1} {0} {1}{1}เนื่องจากสาเหตุใดสาเหตุหนึ่งต่อไปนี้:{1}{1}{2} ชื่อคอลัมน์ที่สงวนไว้{1}{2} อักขระที่ไม่ได้รับการสนับสนุน{1}{2} คำนำหน้าที่สงวนไว้
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=การทำสำเนาไปยัง {0}
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=การเก็บบันทึกและการปรับใช้ผังการทำสำเนาที่มี {0} เป็นเป้าหมายไม่สามารถดำเนินการได้ในขณะนี้ เนื่องจากเรากำลังดำเนินการปรับปรุงฟังก์ชันนี้
#XMSG
TargetColumnSkippedLTF=คอลัมน์เป้าหมายถูกข้าม
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=คอลัมน์เป้าหมายถูกข้ามเนื่องจากประเภทข้อมูลที่ไม่ได้รับการสนับสนุน {0}{1}
#XMSG
validatePKTimeColumnLTF1=คอลัมน์เวลาเป็นคีย์หลัก
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=ออบเจคต้นทางมีคอลัมน์เวลาเป็นคีย์หลัก ซึ่งไม่ได้รับการสนับสนุนในเนื้อหานี้
#XMSG
validateNoPKInLTFTarget=คีย์หลักขาดหายไป
#XMSG
validateNoPKInLTFTargetDescription=คีย์หลักไม่ได้ถูกกำหนดในเป้าหมาย ซึ่งไม่ได้รับการสนับสนุนในเนื้อหานี้
#XMSG
validateABAPClusterTableLTF=ตารางคลัสเตอร์ ABAP
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=ออบเจคต้นทางคือตารางคลัสเตอร์ ABAP ซึ่งไม่ได้รับการสนับสนุนในเนื้อหานี้
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=ดูเหมือนว่าคุณยังไม่ได้เพิ่มข้อมูลใดๆ
#YINS
welcomeText2=เมื่อต้องการเริ่มต้นผังการทำสำเนา ให้เลือกการเชื่อมต่อและออบเจคต้นทางที่ด้านซ้าย

#XBUT
wizStep1=เลือกการเชื่อมต่อต้นทาง
#XBUT
wizStep2=เลือกคอนเทนเนอร์ต้นทาง
#XBUT
wizStep3=เพิ่มออบเจคต้นทาง

#XMSG
limitDataset=ถึงจำนวนสูงสุดของออบเจคแล้ว กรุณาย้ายออบเจคที่มีอยู่เพื่อเพิ่มออบเจคใหม่ หรือสร้างผังการทำสำเนาใหม่
#XMSG
premiumOutBoundRFCannotStartWarningMsg=ผังการทำสำเนาไปยังการเชื่อมต่อเป้าหมายที่ไม่ใช่ SAP นี้ไม่สามารถเริ่มต้นได้เนื่องจากไม่มีปริมาณขาออกสำหรับเดือนนี้
#XMSG
premiumOutBoundRFAdminWarningMsg=ผู้ดูแลระบบสามารถเพิ่มบล็อคพรีเมียมขาออกสำหรับ Tenant นี้ เพื่อให้มีปริมาณขาออกสำหรับเดือนนี้
#XMSG
messageForToastForDPIDColumn2=มีการเพิ่มคอลัมน์ใหม่ในเป้าหมายสำหรับออบเจค {0} รายการ - จำเป็นสำหรับการจัดการเรคคอร์ดที่ซ้ำกันในการเชื่อมต่อกับออบเจคต้นทางที่ใช้ภาษา ABAP ที่ไม่มีคีย์หลัก
#XMSG
PremiumInboundWarningMessage=ทรัพยากรของ SAP HANA {0}ที่จำเป็นสำหรับการทำสำเนาข้อมูลผ่าน {1} อาจเกินความจุที่มีอยู่สำหรับ Tenant ของคุณ ทั้งนี้ขึ้นอยู่กับจำนวนผังการทำสำเนาและปริมาณข้อมูลที่จะทำสำเนา
#XMSG
PremiumInboundWarningMsg={0}ทรัพยากรของ SAP HANA ที่จำเป็นสำหรับการทำสำเนาข้อมูลผ่าน "{1}" อาจเกินความจุที่มีอยู่สำหรับ Tenant ของคุณ ทั้งนี้ขึ้นอยู่กับจำนวนผังการทำสำเนาและปริมาณข้อมูลที่จะทำสำเนา
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=ป้อนชื่อการเลือกเฉพาะส่วน
#XMSG
emptyTargetColumn=ป้อนชื่อคอลัมน์เป้าหมาย
#XMSG
emptyTargetColumnBusinessName=ป้อนชื่อทางธุรกิจของคอลัมน์เป้าหมาย
#XMSG
invalidTransformName=ป้อนชื่อการเลือกเฉพาะส่วน
#XMSG
uniqueColumnName=เปลี่ยนชื่อคอลัมน์เป้าหมาย
#XMSG
copySourceColumnLbl=คัดลอกคอลัมน์จากออบเจคต้นทาง
#XMSG
renameWarning=ตรวจสอบให้แน่ใจว่าเลือกชื่อที่ไม่ซ้ำขณะเปลี่ยนชื่อตารางเป้าหมาย ถ้าตารางที่มีชื่อใหม่มีอยู่แล้วในพื้นที่นั้น ระบบจะใช้ข้อกำหนดของตารางนั้น

#XMSG
uniqueColumnBusinessName=เปลี่ยนชื่อทางธุรกิจของคอลัมน์เป้าหมาย
#XMSG
uniqueSourceMapping=เลือกคอลัมน์ต้นทางอื่น
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=คอลัมน์ต้นทาง {0} ถูกใช้แล้วโดยคอลัมน์เป้าหมายต่อไปนี้:{1}{1}{2}{1}{1} สำหรับคอลัมน์เป้าหมายนี้หรือสำหรับคอลัมน์เป้าหมายอื่น ให้เลือกคอลัมน์ต้นทางที่ยังไม่ได้ใช้เพื่อเก็บบันทึกการเลือกเฉพาะส่วน
#XMSG
uniqueColumnNameDescription=ชื่อคอลัมน์เป้าหมายที่คุณป้อนมีอยู่แล้ว เมื่อต้องการเก็บบันทึกการเลือกเฉพาะส่วน คุณต้องป้อนชื่อคอลัมน์ที่ไม่ซ้ำกัน
#XMSG
uniqueColumnBusinessNameDesc=ชื่อทางธุรกิจของคอลัมน์เป้าหมายมีอยู่แล้ว เมื่อต้องการเก็บบันทึกการเลือกเฉพาะส่วน คุณต้องป้อนชื่อทางธุรกิจของคอลัมน์ที่ไม่ซ้ำกัน
#XMSG
emptySource=เลือกคอลัมน์ต้นทางหรือป้อนค่าคงที่
#XMSG
emptySourceDescription=เมื่อต้องการสร้างรายการการแม็ปที่ถูกต้อง คุณต้องเลือกคอลัมน์ต้นทางหรือป้อนค่าคงที่
#XMSG
emptyExpression=กำหนดการแม็ป
#XMSG
emptyExpressionDescription1=เลือกคอลัมน์ต้นทางที่คุณต้องการแม็ปคอลัมน์เป้าหมาย หรือทำเครื่องหมายที่เช็คบ็อกซ์ในคอลัมน์ {0}ฟังก์ชัน/ค่าคงที่{1} {2} {2} ฟังก์ชันจะถูกป้อนโดยอัตโนมัติตามประเภทข้อมูลเป้าหมาย ส่วนค่าคงที่จะถูกป้อนโดยผู้ใช้
#XMSG
numberExpressionErr=ป้อนตัวเลข
#XMSG
numberExpressionErrDescription=คุณเลือกประเภทข้อมูลที่เป็นตัวเลข ซึ่งหมายความว่าคุณสามารถป้อนได้เฉพาะตัวเลข รวมถึงจุดทศนิยม (ถ้ามี) ห้ามใช้เครื่องหมายอัญประกาศเดี่ยว
#XMSG
invalidLength=ป้อนค่าความยาวที่ถูกต้อง
#XMSG
invalidLengthDescription=ความยาวของประเภทข้อมูลต้องเท่ากับหรือมากกว่าความยาวของคอลัมน์ต้นทาง และสามารถอยู่ระหว่าง 1 ถึง 5,000
#XMSG
invalidMappedLength=ป้อนค่าความยาวที่ถูกต้อง
#XMSG
invalidMappedLengthDescription=ความยาวของประเภทข้อมูลต้องเท่ากับหรือมากกว่าความยาวของคอลัมน์ต้นทาง {0} และสามารถอยู่ระหว่าง 1 ถึง 5,000
#XMSG
invalidPrecision=ป้อนค่าความแม่นยำที่ถูกต้อง
#XMSG
invalidPrecisionDescription=ความแม่นยำจะกำหนดจำนวนหลักทั้งหมด สเกลจะกำหนดจำนวนหลักหลังจุดทศนิยม และสามารถอยู่ระหว่าง 0 ถึงความแม่นยำ{0}{0} ตัวอย่างเช่น: {0}{1} ความแม่นยำ 6 สเกล 2 สอดคล้องกับตัวเลข เช่น 1234.56{0}{1} ความแม่นยำ 6 สเกล 6 สอดคล้องกับตัวเลข เช่น 0.123546{0} {0} ความแม่นยำและสเกลของเป้าหมายต้องเข้ากันได้กับความแม่นยำและสเกลของแหล่งข้อมูล เพื่อให้ตัวเลขทั้งหมดจากแหล่งข้อมูลพอดีกับฟิลด์เป้าหมาย ตัวอย่างเช่น หากคุณมีความแม่นยำ 6 และสเกล 2 ในแหล่งข้อมูล (และเป็นตัวเลขอื่นที่ไม่ใช่ 0 ก่อนจุดทศนิยม) คุณจะไม่สามารถมีความแม่นยำ 6 และสเกล 6 ในเป้าหมาย
#XMSG
invalidPrimaryKey=ป้อนคีย์หลักอย่างน้อยหนึ่งรายการ
#XMSG
invalidPrimaryKeyDescription=คีย์หลักไม่ได้ถูกกำหนดไว้สำหรับ Schema นี้
#XMSG
invalidMappedPrecision=ป้อนค่าความแม่นยำที่ถูกต้อง
#XMSG
invalidMappedPrecisionDescription1=ความแม่นยำจะกำหนดจำนวนหลักทั้งหมด สเกลจะกำหนดจำนวนหลักหลังจุดทศนิยม และสามารถอยู่ระหว่าง 0 ถึงความแม่นยำ{0}{0} ตัวอย่างเช่น:{0}{1} ความแม่นยำ 6 สเกล 2 สอดคล้องกับตัวเลข เช่น 1234.56{0}{1} ความแม่นยำ 6 สเกล 6 สอดคล้องกับตัวเลข เช่น 0.123546{0}{0}ความแม่นยำของประเภทข้อมูลต้องเท่ากับหรือมากกว่าความแม่นยำของแหล่งข้อมูล ({2})
#XMSG
invalidScale=ป้อนค่าสเกลที่ถูกต้อง
#XMSG
invalidScaleDescription=ความแม่นยำจะกำหนดจำนวนหลักทั้งหมด สเกลจะกำหนดจำนวนหลักหลังจุดทศนิยม และสามารถอยู่ระหว่าง 0 ถึงความแม่นยำ{0}{0} ตัวอย่างเช่น: {0}{1} ความแม่นยำ 6 สเกล 2 สอดคล้องกับตัวเลข เช่น 1234.56{0}{1} ความแม่นยำ 6 สเกล 6 สอดคล้องกับตัวเลข เช่น 0.123546{0} {0} ความแม่นยำและสเกลของเป้าหมายต้องเข้ากันได้กับความแม่นยำและสเกลของแหล่งข้อมูล เพื่อให้ตัวเลขทั้งหมดจากแหล่งข้อมูลพอดีกับฟิลด์เป้าหมาย ตัวอย่างเช่น หากคุณมีความแม่นยำ 6 และสเกล 2 ในแหล่งข้อมูล (และเป็นตัวเลขอื่นที่ไม่ใช่ 0 ก่อนจุดทศนิยม) คุณจะไม่สามารถมีความแม่นยำ 6 และสเกล 6 ในเป้าหมาย
#XMSG
invalidMappedScale=ป้อนค่าสเกลที่ถูกต้อง
#XMSG
invalidMappedScaleDescription1=ความแม่นยำจะกำหนดจำนวนหลักทั้งหมด สเกลจะกำหนดจำนวนหลักหลังจุดทศนิยม และสามารถอยู่ระหว่าง 0 ถึงความแม่นยำ{0}{0} ตัวอย่างเช่น:{0}{1} ความแม่นยำ 6 สเกล 2 สอดคล้องกับตัวเลข เช่น 1234.56{0}{1} ความแม่นยำ 6 สเกล 6 สอดคล้องกับตัวเลข เช่น 0.123546{0}{0}สเกลของประเภทข้อมูลต้องเท่ากับหรือมากกว่าสเกลของแหล่งข้อมูล ({2})
#XMSG
nonCompatibleDataType=เลือกประเภทข้อมูลเป้าหมายที่เข้ากันได้
#XMSG
nonCompatibleDataTypeDescription1=ประเภทข้อมูลที่คุณระบุที่นี่ต้องเข้ากันได้กับประเภทข้อมูลต้นฉบับ ({0}) {1}{1} ตัวอย่างเช่น ถ้าคอลัมน์ต้นทางของคุณมีประเภทข้อมูลสตริงและมีตัวอักษร คุณจะไม่สามารถใช้ประเภทข้อมูลทศนิยมสำหรับเป้าหมายของคุณ
#XMSG
invalidColumnCount=เลือกคอลัมน์ต้นทาง
#XMSG
ObjectStoreInvalidScaleORPrecision=ป้อนค่าที่ถูกต้องสำหรับความแม่นยำและสเกล
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=ค่าแรกคือความแม่นยำที่กำหนดจำนวนหลักทั้งหมด ค่าที่สองคือสเกลที่กำหนดจำนวนหลักหลังจุดทศนิยม ป้อนค่าสเกลเป้าหมายที่มากกว่าค่าสเกลต้นทางและตรวจสอบให้แน่ใจว่าผลต่างระหว่างสเกลเป้าหมายที่ป้อนกับค่าความแม่นยำมากกว่าผลต่างระหว่างสเกลต้นทางกับค่าความแม่นยำ
#XMSG
InvalidPrecisionORScale=ป้อนค่าที่ถูกต้องสำหรับความแม่นยำและสเกล
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=ค่าแรกคือความแม่นยำที่กำหนดจำนวนหลักทั้งหมด ค่าที่สองคือสเกลที่กำหนดจำนวนหลักหลังจุดทศนิยม{0}{0}เนื่องจากประเภทข้อมูลต้นฉบับไม่ได้รับการสนับสนุนใน Google BigQuery จึงมีการแปลงเป็นประเภทข้อมูลเป้าหมาย DECIMAL ในกรณีนี้ สามารถกำหนดความแม่นยำระหว่าง 38 ถึง 76 และสเกลระหว่าง 9 ถึง 38 เท่านั้น นอกจากนี้ผลลัพธ์ของความแม่นยำลบด้วยสเกล ซึ่งแสดงจำนวนหลักก่อนจุดทศนิยม ต้องอยู่ระหว่าง 29 ถึง 38
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=ค่าแรกคือความแม่นยำที่กำหนดจำนวนหลักทั้งหมด ค่าที่สองคือสเกลที่กำหนดจำนวนหลักหลังจุดทศนิยม{0}{0}เนื่องจากประเภทข้อมูลต้นฉบับไม่ได้รับการสนับสนุนใน Google BigQuery จึงมีการแปลงเป็นประเภทข้อมูลเป้าหมาย DECIMAL ในกรณีนี้ ต้องกำหนดความแม่นยำเป็น 20 หรือมากกว่า นอกจากนี้ผลลัพธ์ของความแม่นยำลบด้วยสเกล ซึ่งแสดงตัวเลขก่อนจุดทศนิยม ต้องเป็น 20 หรือมากกว่า
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=ค่าแรกคือความแม่นยำที่กำหนดจำนวนหลักทั้งหมด ค่าที่สองคือสเกลที่กำหนดจำนวนหลักหลังจุดทศนิยม{0}{0}เนื่องจากประเภทข้อมูลต้นฉบับไม่ได้รับการสนับสนุนในเป้าหมาย จึงมีการแปลงเป็นประเภทข้อมูลเป้าหมาย DECIMAL ในกรณีนี้ ต้องกำหนดความแม่นยำเป็นตัวเลขที่มากกว่าหรือเท่ากับ 1 และน้อยกว่าหรือเท่ากับ 38 และสเกลน้อยกว่าหรือเท่ากับความแม่นยำ
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=ค่าแรกคือความแม่นยำที่กำหนดจำนวนหลักทั้งหมด ค่าที่สองคือสเกลที่กำหนดจำนวนหลักหลังจุดทศนิยม{0}{0}เนื่องจากประเภทข้อมูลต้นฉบับไม่ได้รับการสนับสนุนในเป้าหมาย จึงมีการแปลงเป็นประเภทข้อมูลเป้าหมาย DECIMAL ในกรณีนี้ ต้องกำหนดความแม่นยำเป็น 20 หรือมากกว่า นอกจากนี้ผลลัพธ์ของความแม่นยำลบด้วยสเกล ซึ่งแสดงตัวเลขก่อนจุดทศนิยม ต้องเป็น 20 หรือมากกว่า
#XMSG
invalidColumnCountDescription=เมื่อต้องการสร้างรายการการแม็ปที่ถูกต้อง คุณต้องเลือกคอลัมน์ต้นทางหรือป้อนค่าคงที่
#XMSG
duplicateColumns=เปลี่ยนชื่อคอลัมน์เป้าหมาย
#XMSG
duplicateGBQCDCColumnsDesc=ชื่อคอลัมน์เป้าหมายถูกสงวนไว้ใน Google BigQuery คุณต้องเปลี่ยนชื่อเพื่อให้สามารถเก็บบันทึกการเลือกเฉพาะส่วนได้
#XMSG
duplicateConfluentCDCColumnsDesc=ชื่อคอลัมน์เป้าหมายถูกสงวนไว้ใน Confluent คุณต้องเปลี่ยนชื่อเพื่อให้สามารถเก็บบันทึกการเลือกเฉพาะส่วนได้
#XMSG
duplicateSignavioCDCColumnsDesc=ชื่อคอลัมน์เป้าหมายถูกสงวนไว้ใน SAP Signavio คุณต้องเปลี่ยนชื่อเพื่อให้สามารถเก็บบันทึกการเลือกเฉพาะส่วนได้
#XMSG
duplicateMsOneLakeCDCColumnsDesc=ชื่อคอลัมน์เป้าหมายถูกสงวนไว้ใน MS OneLake คุณต้องเปลี่ยนชื่อเพื่อให้สามารถเก็บบันทึกการเลือกเฉพาะส่วนได้
#XMSG
duplicateSFTPCDCColumnsDesc=ชื่อคอลัมน์เป้าหมายถูกสงวนไว้ใน SFTP คุณต้องเปลี่ยนชื่อเพื่อให้สามารถเก็บบันทึกการเลือกเฉพาะส่วนได้
#XMSG
GBQTargetNameWithPrefixUpdated1=ชื่อคอลัมน์เป้าหมายมีคำนำหน้าที่สงวนไว้ใน Google BigQuery คุณต้องเปลี่ยนชื่อเพื่อให้สามารถเก็บบันทึกการเลือกเฉพาะส่วนได้ {0}{0}ชื่อคอลัมน์เป้าหมายต้องไม่ขึ้นต้นด้วยสตริงใดๆ ต่อไปนี้:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=ย่อชื่อคอลัมน์เป้าหมาย
#XMSG
GBQtargetMaxLengthDesc=ใน Google BigQuery ชื่อคอลัมน์สามารถใช้อักขระได้สูงสุด 300 ตัว ย่อชื่อคอลัมน์เป้าหมายให้สั้นลงเพื่อให้สามารถเก็บบันทึกการเลือกเฉพาะส่วนได้
#XMSG
invalidMappedScalePrecision=ความแม่นยำและสเกลของเป้าหมายต้องเข้ากันได้กับความแม่นยำและสเกลของแหล่งข้อมูล เพื่อให้ตัวเลขทั้งหมดจากแหล่งข้อมูลพอดีกับฟิลด์เป้าหมาย
#XMSG
invalidMappedScalePrecisionShortText=ป้อนค่าสเกลและความแม่นยำที่ถูกต้อง
#XMSG
validationIncompatiblePKTypeDescProjection3=คอลัมน์ต้นทางอย่างน้อยหนึ่งคอลัมน์มีประเภทข้อมูลที่ไม่สามารถกำหนดเป็นคีย์หลักใน Google BigQuery จะไม่มีการสร้างคีย์หลักในออบเจคเป้าหมาย{0}{0} ประเภทข้อมูลเป้าหมายต่อไปนี้เข้ากันได้กับประเภทข้อมูลของ Google BigQuery ซึ่งสามารถกำหนดคีย์หลักได้:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=ยกเลิกการเลือก column __message_id
#XMSG
uncheckColumnMessageIdDesc=คอลัมน์: คีย์หลัก
#XMSG
validationOpCodeInsert=คุณต้องป้อนค่าสำหรับการแทรก
#XMSG
recommendDifferentPrimaryKey=เราขอแนะนำให้คุณเลือกคีย์หลักอื่นที่ระดับรายการ
#XMSG
recommendDifferentPrimaryKeyDesc=เมื่อกำหนดรหัสการดำเนินการแล้ว ขอแนะนำให้เลือกคีย์หลักอื่นสำหรับดัชนีอาร์เรย์และรายการ เพื่อหลีกเลี่ยงปัญหาต่างๆ เช่น การซ้ำซ้อนของคอลัมน์ เป็นต้น
#XMSG
selectPrimaryKeyItemLevel=คุณต้องเลือกคีย์หลักอย่างน้อยหนึ่งรายการสำหรับทั้งระดับส่วนหัวและระดับรายการ
#XMSG
selectPrimaryKeyItemLevelDesc=เมื่อขยายอาร์เรย์หรือแผนที่ คุณต้องเลือกคีย์หลักสองรายการ หนึ่งรายการที่ระดับส่วนหัวและอีกหนึ่งรายการที่ระดับรายการ
#XMSG
invalidMapKey=คุณต้องเลือกคีย์หลักอย่างน้อยหนึ่งรายการที่ระดับส่วนหัว
#XMSG
invalidMapKeyDesc=เมื่อขยายอาร์เรย์หรือแผนที่ คุณต้องเลือกคีย์หลักที่ระดับส่วนหัว
#XFLD
txtSearchFields=ค้นหาคอลัมน์เป้าหมาย
#XFLD
txtName=ชื่อ
#XMSG
txtSourceColValidation=คอลัมน์ต้นทางอย่างน้อยหนึ่งคอลัมน์ไม่ได้รับการสนับสนุน:
#XMSG
txtMappingCount=การแม็ป ({0})
#XMSG
schema=Schema
#XMSG
sourceColumn=คอลัมน์ต้นทาง
#XMSG
warningSourceSchema=การเปลี่ยนแปลงใดๆ ที่ทำกับ Schema จะส่งผลต่อการแม็ปในไดอะลอกการเลือกเฉพาะส่วน
#XCOL
txtTargetColName=คอลัมน์เป้าหมาย (ชื่อทางเทคนิค)
#XCOL
txtDataType=ประเภทข้อมูลเป้าหมาย
#XCOL
txtSourceDataType=ประเภทข้อมูลต้นฉบับ
#XCOL
srcColName=คอลัมน์ต้นทาง (ชื่อทางเทคนิค)
#XCOL
precision=ความแม่นยำ
#XCOL
scale=สเกล
#XCOL
functionsOrConstants=ฟังก์ชัน/ค่าคงที่
#XCOL
txtTargetColBusinessName=คอลัมน์เป้าหมาย (ชื่อทางธุรกิจ)
#XCOL
prKey=คีย์หลัก
#XCOL
txtProperties=คุณสมบัติ
#XBUT
txtOK=เก็บบันทึก
#XBUT
txtCancel=ยกเลิก
#XBUT
txtRemove=ย้ายออก
#XFLD
txtDesc=คำอธิบาย
#XMSG
rftdMapping=การแม็ป
#XFLD
@lblColumnDataType=ประเภทข้อมูล
#XFLD
@lblColumnTechnicalName=ชื่อทางเทคนิค
#XBUT
txtAutomap=แม็ปอัตโนมัติ
#XBUT
txtUp=ขึ้น
#XBUT
txtDown=ลง

#XTOL
txtTransformationHeader=การเลือกเฉพาะส่วน
#XTOL
editTransformation=แก้ไข
#XTOL
primaryKeyToolip=คีย์


#XMSG
rftdFilter=ฟิลเตอร์
#XMSG
rftdFilterColumnCount=แหล่งข้อมูล: {0} ({1})
#XTOL
rftdFilterColSearch=ค้นหา
#XMSG
rftdFilterColNoData=ไม่มีคอลัมน์ที่จะแสดง
#XMSG
rftdFilteredColNoExps=ไม่มีนิพจน์ฟิลเตอร์
#XMSG
rftdFilterSelectedColTxt=เพิ่มฟิลเตอร์สำหรับ
#XMSG
rftdFilterTxt=ฟิลเตอร์ใช้ได้กับ
#XBUT
rftdFilterSelectedAddColExp=เพิ่มนิพจน์
#YINS
rftdFilterNoSelectedCol=เลือกคอลัมน์เพื่อเพิ่มฟิลเตอร์
#XMSG
rftdFilterExp=นิพจน์ฟิลเตอร์
#XMSG
rftdFilterNotAllowedColumn=การเพิ่มฟิลเตอร์ไม่ได้รับการสนับสนุนสำหรับคอลัมน์นี้
#XMSG
rftdFilterNotAllowedHead=คอลัมน์ที่ไม่รองรับ
#XMSG
rftdFilterNoExp=ไม่ได้กำหนดฟิลเตอร์ไว้
#XTOL
rftdfilteredTt=ฟิลเตอร์แล้ว
#XTOL
rftdremoveexpTt=ย้ายนิพจน์ฟิลเตอร์ออก
#XTOL
validationMessageTt=ข้อความการตรวจสอบความถูกต้อง
#XTOL
rftdFilterDateInp=เลือกวันที่
#XTOL
rftdFilterDateTimeInp=เลือกวัน/เวลา
#XTOL
rftdFilterTimeInp=เลือกเวลา
#XTOL
rftdFilterInp=ป้อนค่า
#XMSG
rftdFilterValidateEmptyMsg=นิพจน์ฟิลเตอร์ {0} ในคอลัมน์ {1} ว่างเปล่า
#XMSG
rftdFilterValidateInvalidNumericMsg=นิพจน์ฟิลเตอร์ {0} ในคอลัมน์ {1} มีค่าตัวเลขที่ไม่ถูกต้อง
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=นิพจน์ฟิลเตอร์ต้องมีค่าตัวเลขที่ถูกต้อง
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=ถ้ามีการเปลี่ยนแปลง Schema ของออบเจคเป้าหมาาย ให้ใช้ฟังก์ชัน “แม็ปไปยังออบเจคเป้าหมายที่มีอยู่” ในหน้าหลักเพื่อปรับปรุงการเปลี่ยนแปลงและแม็ปออบเจคเป้าหมายกับแหล่งที่มาอีกครั้ง
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=ถ้าตารางเป้าหมายมีอยู่แล้วและการแม็ปมีการเปลี่ยนแปลง Schema คุณต้องเปลี่ยนแปลงตารางเป้าหมายให้สอดคล้องกันก่อนที่จะปรับใช้ผังการทำสำเนา
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=ถ้าการแม็ปของคุณเกี่ยวข้องกับการเปลี่ยนแปลง Schema คุณต้องเปลี่ยนแปลงตารางเป้าหมายให้สอดคล้องกันก่อนที่จะปรับใช้ผังการทำสำเนา
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=คอลัมน์ที่ไม่สนับสนุนต่อไปนี้ถูกข้ามจากข้อกำหนดของต้นทาง: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=คอลัมน์ที่ไม่สนับสนุนต่อไปนี้ถูกข้ามจากข้อกำหนดของเป้าหมาย: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=ออบเจคต่อไปนี้ไม่ได้รับการสนับสนุนเนื่องจากมีการแสดงสำหรับการใช้: {0} {1} {0} {0} เมื่อต้องการใช้ตารางในผังการทำสำเนา ต้องไม่กำหนดการใช้เชิงความหมาย (ในการกำหนดค่าตาราง) เป็น {2}ชุดข้อมูลเชิงวิเคราะห์{2}
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=ไม่สามารถใช้ออบเจคเป้าหมายได้เนื่องจากมีการแสดงสำหรับการใช้: {0} {0} เมื่อต้องการใช้ตารางในผังการทำสำเนา ต้องไม่กำหนดการใช้เชิงความหมาย (ในการกำหนดค่าตาราง) เป็น {1}ชุดข้อมูลเชิงวิเคราะห์{1}
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=ออบเจคเป้าหมายที่มีชื่อนี้มีอยู่แล้วแต่ไม่สามารถใช้ได้เนื่องจากมีการแสดงสำหรับการใช้: {0} {0} เมื่อต้องการใช้ตารางในผังการทำสำเนา ต้องไม่กำหนดการใช้เชิงความหมาย (ในการกำหนดค่าตาราง) เป็น {1}ชุดข้อมูลเชิงวิเคราะห์{1}
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=ออบเจคที่มีชื่อนี้มีอยู่แล้วในเป้าหมาย {0}แต่ออบเจคนี้ไม่สามารถใช้เป็นออบเจคเป้าหมายสำหรับผังการทำสำเนาไปยังพื้นที่เก็บข้อมูลภายในได้เนื่องจากไม่ใช่ตารางภายใน
#XMSG:
targetAutoRenameUpdated=เปลี่ยนชื่อคอลัมน์เป้าหมายแล้ว
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=คอลัมน์เป้าหมายถูกเปลี่ยนชื่อเพื่อให้สามารถทำสำเนาใน Google BigQuery ได้เนื่องด้วยเหตุผลข้อใดข้อหนึ่งต่อไปนี้:{0} {1}{2}ชื่อคอลัมน์ที่สงวนไว้{3}{2}อักขระที่ไม่ได้รับการสนับสนุน{3}{2}คำนำหน้าที่สงวนไว้{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=คอลัมน์เป้าหมายถูกเปลี่ยนชื่อเพื่อให้สามารถทำสำเนาใน Confluent ได้เนื่องด้วยเหตุผลข้อใดข้อหนึ่งต่อไปนี้:{0} {1}{2}ชื่อคอลัมน์ที่สงวนไว้{3}{2}อักขระที่ไม่ได้รับการสนับสนุน{3}{2}คำนำหน้าที่สงวนไว้{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=คอลัมน์เป้าหมายถูกเปลี่ยนชื่อเพื่อให้สามารถทำสำเนาไปยังเป้าหมายได้เนื่องด้วยเหตุผลข้อใดข้อหนึ่งต่อไปนี้:{0} {1}{2}อักขระที่ไม่ได้รับการสนับสนุน{3}{2}คำนำหน้าที่สงวนไว้{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=คอลัมน์เป้าหมายถูกเปลี่ยนชื่อเพื่อให้สามารถทำสำเนาไปยังเป้าหมายได้เนื่องด้วยเหตุผลข้อใดข้อหนึ่งต่อไปนี้:{0} {1}{2}ชื่อคอลัมน์ที่สงวนไว้{3}{2}อักขระที่ไม่ได้รับการสนับสนุน{3}{2}คำนำหน้าที่สงวนไว้{3}{4}
#XMSG:
targetAutoDataType=เปลี่ยนแปลงประเภทข้อมูลเป้าหมายแล้ว
#XMSG:
targetAutoDataTypeDesc=ประเภทข้อมูลเป้าหมายถูกเปลี่ยนเป็น {0} เนื่องจากประเภทข้อมูลต้นฉบับไม่ได้รับการสนับสนุนใน Google BigQuery
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=ประเภทข้อมูลเป้าหมายถูกเปลี่ยนเป็น {0} เนื่องจากประเภทข้อมูลต้นฉบับไม่ได้รับการสนับสนุนในการเชื่อมต่อเป้าหมาย
#XMSG
projectionGBQUnableToCreateKey=คีย์หลักจะไม่ถูกสร้างขึ้น
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=ใน Google BigQuery รองรับคีย์หลักสูงสุด 16 คีย์ แต่ออบเจคต้นทางมีคีย์หลักจำนวนมาก จะไม่มีการสร้างคีย์หลักในออบเจคเป้าหมาย
#XMSG
HDLFNoKeyError=กำหนดคอลัมน์อย่างน้อยหนึ่งคอลัมน์เป็นคีย์หลัก
#XMSG
HDLFNoKeyErrorDescription=เมื่อต้องการทำสำเนาออบเจค คุณต้องกำหนดคอลัมน์เป้าหมายอย่างน้อยหนึ่งคอลัมน์เป็นคีย์หลัก
#XMSG
HDLFNoKeyErrorExistingTarget=กำหนดคอลัมน์อย่างน้อยหนึ่งคอลัมน์เป็นคีย์หลัก
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=เมื่อต้องการทำสำเนาข้อมูลไปยังออบเจคเป้าหมายที่มีอยู่ ต้องกำหนดคอลัมน์อย่างน้อยหนึ่งคอลัมน์เป็นคีย์หลัก {0} {0} คุณมีตัวเลือกต่อไปนี้สำหรับการกำหนดคอลัมน์อย่างน้อยหนึ่งคอลัมน์เป็นคีย์หลัก: {0}{0}{1} ใช้เอดิเตอร์ตารางภายในเพื่อเปลี่ยนแปลงออบเจคเป้าหมายที่มีอยู่ จากนั้นรีโหลดผังการทำสำเนาเปลี่ยนชื่อออบเจคเป้าหมายในผังการทำสำเนา{0}{0}{1} การดำเนินการนี้จะสร้างออบเจคใหม่ทันทีที่การดำเนินการเริ่มต้นขึ้น หลังเปลี่ยนชื่อแล้ว คุณสามารถกำหนดคอลัมน์อย่างน้อยหนึ่งคอลัมน์เป็นคีย์หลักในการเลือกเฉพาะส่วน{0}{0}{1} แม็ปออบเจคไปยังออบเจคเป้าหมายอื่นที่มีอยู่ซึ่งมีการกำหนดคอลัมน์อย่างน้อยหนึ่งคอลัมน์เป็นคีย์หลักแล้ว
#XMSG
HDLFSourceTargetDifferentKeysWarning=เปลี่ยนแปลงคีย์หลักแล้ว
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=เมื่อเปรียบเทียบกับออบเจคต้นทาง คุณได้กำหนดคอลัมน์ที่แตกต่างกันเป็นคีย์หลักสำหรับออบเจคเป้าหมาย กรุณาตรวจสอบให้แน่ใจว่าคอลัมน์เหล่านี้ระบุแถวทั้งหมดโดยไม่ซ้ำกันเพื่อหลีกเลี่ยงความเสียหายของข้อมูลที่อาจเกิดขึ้นเมื่อทำสำเนาข้อมูลในภายหลัง {0} {0} ในออบเจคต้นทาง คอลัมน์ต่อไปนี้จะถูกกำหนดเป็นคีย์หลัก: {0} {1}
#XMSG
duplicateDPIDColumns=เปลี่ยนชื่อคอลัมน์เป้าหมาย
#XMSG
duplicateDPIDDColumnsDesc1=ชื่อคอลัมน์เป้าหมายนี้ถูกสงวนไว้สำหรับคอลัมน์ทางเทคนิค ป้อนชื่ออื่นเพื่อเก็บบันทึกการเลือกเฉพาะส่วน
#XMSG:
targetAutoRenameDPID=เปลี่ยนชื่อคอลัมน์เป้าหมายแล้ว
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=คอลัมน์เป้าหมายถูกเปลี่ยนชื่อเพื่อให้สามารถทำสำเนาจากแหล่งข้อมูล ABAP ที่ไม่มีคีย์ เนื่องด้วยเหตุผลข้อใดข้อหนึ่งต่อไปนี้:{0} {1}{2}ชื่อคอลัมน์ที่สงวนไว้{3}{2}อักขระที่ไม่ได้รับการสนับสนุน{3}{2}คำนำหน้าที่สงวนไว้{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle=การกำหนดค่าเป้าหมาย {0}
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle=การกำหนดค่าแหล่งที่มา {0}
#XBUT
connectionSettingSave=เก็บบันทึก
#XBUT
connectionSettingCancel=ยกเลิก
#XBUT: Button to keep the object level settings
txtKeep=เก็บ
#XBUT: Button to overwrite the Object level settings
txtOverwrite=เขียนทับ
#XFLD
targetConnectionThreadlimit=ขีดจำกัดของ Thread เป้าหมายสำหรับการโหลดเริ่มต้น (1-100)
#XFLD
connectionThreadLimit=ขีดจำกัดของ Thread ต้นทางสำหรับการโหลดเริ่มต้น (1-100)
#XFLD
maxConnection=ขีดจำกัดของ Thread การทำสำเนา (1-100)
#XFLD
kafkaNumberOfPartitions=จำนวนพาร์ทิชัน
#XFLD
kafkaReplicationFactor=แฟกเตอร์การทำสำเนา
#XFLD
kafkaMessageEncoder=ตัวเข้ารหัสข้อความ
#XFLD
kafkaMessageCompression=การบีบอัดข้อความ
#XFLD
fileGroupDeltaFilesBy=จัดกลุ่มเดลต้าตาม
#XFLD
fileFormat=ประเภทไฟล์
#XFLD
csvEncoding=การเข้ารหัส CSV
#XFLD
abapExitLbl=ABAP Exit
#XFLD
deltaPartition=จำนวน Thread ของออบเจคสำหรับการโหลดเดลต้า (1-10)
#XFLD
clamping_Data=ล้มเหลวในการตัดทอนข้อมูล
#XFLD
fail_On_Incompatible=ล้มเหลวเนื่องจากข้อมูลเข้ากันไม่ได้
#XFLD
maxPartitionInput=จำนวนพาร์ทิชันสูงสุด
#XFLD
max_Partition=กำหนดจำนวนพาร์ทิชันสูงสุด
#XFLD
include_SubFolder=รวมแฟ้มย่อย
#XFLD
fileGlobalPattern=รูปแบบทั่วไปสำหรับชื่อไฟล์
#XFLD
fileCompression=การบีบอัดไฟล์
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=ตัวคั่นไฟล์
#XFLD
fileIsHeaderIncluded=ส่วนหัวของไฟล์
#XFLD
fileOrient=การวางแนว
#XFLD
gbqWriteMode=โหมดการเขียน
#XFLD
suppressDuplicate=ไม่แสดงรายการที่ซ้ำ
#XFLD
apacheSpark=เปิดใช้งานความเข้ากันได้ของ Apache Spark
#XFLD
clampingDatatypeCb=จำกัดประเภทข้อมูลตัวเลขที่มีจุดทศนิยม
#XFLD
overwriteDatasetSetting=เขียนทับการกำหนดค่าเป้าหมายในระดับออบเจค
#XFLD
overwriteSourceDatasetSetting=เขียนทับการกำหนดค่าแหล่งที่มาในระดับออบเจค
#XMSG
kafkaInvalidConnectionSetting=ป้อนตัวเลขระหว่าง {0} ถึง {1}
#XMSG
MinReplicationThreadErrorMsg=ป้อนตัวเลขที่มากกว่า {0}
#XMSG
MaxReplicationThreadErrorMsg=ป้อนตัวเลขที่ต่ำกว่า {0}
#XMSG
DeltaThreadErrorMsg=ป้อนค่าระหว่าง 1 ถึง 10
#XMSG
MaxPartitionErrorMsg=ป้อนค่าระหว่าง 1 <= x <= 2147483647 ค่าตั้งต้นคือ 10
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=ป้อนจำนวนเต็มระหว่าง {0} ถึง {1}
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=ใช้แฟกเตอร์การทำสำเนาของโบรกเกอร์
#XFLD
serializationFormat=รูปแบบการซีเรียลไลซ์
#XFLD
compressionType=ประเภทการบีบอัด
#XFLD
schemaRegistry=ใช้ Schema Registry
#XFLD
subjectNameStrat=กลยุทธ์ชื่อเรื่อง
#XFLD
compatibilityType=ประเภทความเข้ากันได้
#XFLD
confluentTopicName=ชื่อหัวข้อ
#XFLD
confluentRecordName=ชื่อเรคคอร์ด
#XFLD
confluentSubjectNamePreview=การแสดงตัวอย่างชื่อเรื่อง
#XMSG
serializationChangeToastMsgUpdated2=รูปแบบการซีเรียลไลซ์ถูกเปลี่ยนเป็น JSON เนื่องจากไม่ได้เปิดใช้งาน Schema Registry เมื่อต้องการเปลี่ยนรูปแบบการซีเรียลไลซ์กลับเป็น AVRO คุณต้องเปิดใช้งาน Schema Registry ก่อน
#XBUT
confluentTopicNameInfo=ชื่อหัวข้อจะอิงตามชื่อออบเจคเป้าหมายเสมอ คุณสามารถเปลี่ยนชื่อหัวข้อได้โดยการเปลี่ยนชื่อออบเจคเป้าหมาย
#XMSG
emptyRecordNameValidationHeaderMsg=ป้อนชื่อเรคคอร์ด
#XMSG
emptyPartionHeader=ป้อนจำนวนพาร์ทิชัน
#XMSG
invalidPartitionsHeader=ป้อนจำนวนพาร์ทิชันที่ถูกต้อง
#XMSG
invalidpartitionsDesc=ป้อนตัวเลขระหว่าง 1 ถึง 200,000
#XMSG
emptyrFactorHeader=ป้อนแฟกเตอร์การทำสำเนา
#XMSG
invalidrFactorHeader=ป้อนแฟกเตอร์การทำสำเนาที่ถูกต้อง
#XMSG
invalidrFactorDesc=ป้อนตัวเลขระหว่าง 1 ถึง 32,767
#XMSG
emptyRecordNameValidationDescMsg=หากใช้รูปแบบการซีเรียลไลซ์ "AVRO" จะรองรับเฉพาะอักขระต่อไปนี้:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _ (เครื่องหมายขีดล่าง)
#XMSG
validRecordNameValidationHeaderMsg=ป้อนชื่อเรคคอร์ดที่ถูกต้อง
#XMSG
validRecordNameValidationDescMsgUpdated=เนื่องจากมีการใช้รูปแบบการซีเรียลไลซ์ "AVRO" ชื่อเรคคอร์ดต้องประกอบด้วยอักขระตัวอักษรผสมตัวเลข (A-Z, a-z, 0-9) และเครื่องหมายขีดล่าง (_) เท่านั้น โดยต้องขึ้นต้นด้วยตัวอักษรหรือเครื่องหมายขีดล่าง
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=“จำนวน Thread ของออบเจคสำหรับการโหลดเดลต้า” สามารถกำหนดค่าได้ทันทีที่ออบเจคอย่างน้อยหนึ่งรายการมีประเภทการโหลด “เริ่มต้นและเดลต้า”
#XMSG
invalidTargetName=ชื่อคอลัมน์ไม่ถูกต้อง
#XMSG
invalidTargetNameDesc=ชื่อคอลัมน์เป้าหมายต้องประกอบด้วยอักขระตัวอักษรผสมตัวเลข (A-Z, a-z, 0-9) และเครื่องหมายขีดล่าง (_) เท่านั้น
#XFLD
consumeOtherSchema=ใช้ Schema เวอร์ชันอื่นๆ
#XFLD
ignoreSchemamissmatch=ละเว้น Schema ที่ไม่ตรงกัน
#XFLD
confleuntDatatruncation=ล้มเหลวในการตัดทอนข้อมูล
#XFLD
isolationLevel=ระดับการแยก
#XFLD
confluentOffset=จุดเริ่มต้น
#XFLD
signavioGroupDeltaFilesByText=ไม่มี
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=ไม่ใช่
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=ไม่ใช่

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=การเลือกเฉพาะส่วน
#XBUT
txtAdd=เพิ่ม
#XBUT
txtEdit=แก้ไข
#XMSG
transformationText=เพิ่มการเลือกเฉพาะส่วนเพื่อกำหนดค่าฟิลเตอร์หรือการแม็ป
#XMSG
primaryKeyRequiredText=เลือกคีย์หลักด้วย 'กำหนดรูปแบบ Schema'
#XFLD
lblSettings=การกำหนดค่า
#XFLD
lblTargetSetting={0}: การกำหนดค่าเป้าหมาย
#XMSG
@csvRF=เลือกไฟล์ที่มีข้อกำหนด Schema ที่คุณต้องการนำไปใช้กับไฟล์ทั้งหมดในแฟ้ม
#XFLD
lblSourceColumns=คอลัมน์ต้นทาง
#XFLD
lblJsonStructure=โครงสร้าง JSON
#XFLD
lblSourceSetting={0}: การกำหนดค่าแหล่งที่มา
#XFLD
lblSourceSchemaSetting={0}: การกำหนดค่า Schema แหล่งข้อมูล
#XBUT
messageSettings=การกำหนดค่าข้อความ
#XFLD
lblPropertyTitle1=คุณสมบัติของออบเจค
#XFLD
lblRFPropertyTitle=คุณสมบัติของผังการทำสำเนา
#XMSG
noDataTxt=ไม่มีคอลัมน์ที่จะแสดง
#XMSG
noTargetObjectText=ไม่มีการเลือกออบเจคเป้าหมาย
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=คอลัมน์เป้าหมาย
#XMSG
searchColumns=ค้นหาคอลัมน์
#XTOL
cdcColumnTooltip=คอลัมน์การจับข้อมูลเดลต้า
#XMSG
sourceNonDeltaSupportErrorUpdated=ออบเจคต้นทางไม่รองรับการจับข้อมูลเดลต้า
#XMSG
targetCDCColumnAdded=เพิ่ม 2 คอลัมน์เป้าหมายสำหรับการจับข้อมูลเดลต้าแล้ว
#XMSG
deltaPartitionEnable=เพิ่ม "ขีดจำกัด Thread ของออบเจคสำหรับการโหลดเดลต้า" ในการกำหนดค่าแหล่งที่มาแล้ว
#XMSG
attributeMappingRemovalTxt=กำลังย้ายการแม็ปที่ไม่ถูกต้องซึ่งไม่ได้รับการรองรับสำหรับออบเจคเป้าหมายใหม่ออก
#XMSG
targetCDCColumnRemoved=คอลัมน์เป้าหมาย 2 คอลัมน์ที่ใช้สำหรับการจับข้อมูลเดลต้าถูกย้ายออกแล้ว
#XMSG
replicationLoadTypeChanged=ประเภทการโหลดถูกเปลี่ยนเป็น "เริ่มต้นและเดลต้า"
#XMSG
sourceHDLFLoadTypeError=เปลี่ยนประเภทการโหลดเป็น "เริ่มต้นและเดลต้า"
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=เมื่อต้องการทำสำเนาออบเจคจากการเชื่อมต่อต้นทางที่มีประเภทการเชื่อมต่อ SAP HANA Cloud, ไฟล์ Data Lake ไปยัง SAP Datasphere คุณต้องใช้ประเภทการโหลด "เริ่มต้นและเดลต้า"
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=เปิดใช้งานการจับข้อมูลเดลต้า
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=เมื่อต้องการทำสำเนาออบเจคจากการเชื่อมต่อต้นทางที่มีประเภทการเชื่อมต่อ SAP HANA Cloud, ไฟล์ Data Lake ไปยัง SAP Datasphere คุณต้องใช้เปิดใช้งานการจับข้อมูลเดลต้า
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=เปลี่ยนแปลงออบเจคเป้าหมาย
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=ไม่สามารถใช้ออบเจคเป้าหมายได้เนื่องจากการจับข้อมูลเดลต้าถูกปิดใช้งาน คุณสามารถเปลี่ยนชื่อออบเจคเป้าหมาย (ซึ่งจะทำให้มีการสร้างออบเจคใหม่ที่มีการจับข้อมูลเดลต้า) หรือแม็ปออบเจคดังกล่าวกับออบเจคที่มีอยู่ที่มีการเปิดใช้งานการจับข้อมูลเดลต้า
#XMSG
deltaPartitionError=ป้อนจำนวน Thread ของออบเจคที่ถูกต้องสำหรับการโหลดเดลต้า
#XMSG
deltaPartitionErrorDescription=ป้อนค่าระหว่าง 1 ถึง 10
#XMSG
deltaPartitionEmptyError=ป้อนจำนวน Thread ของออบเจคสำหรับการโหลดเดลต้า
#XFLD
@lblColumnDescription=คำอธิบาย
#XMSG
@lblColumnDescriptionText1=เพื่อวัตถุประสงค์ทางเทคนิค - การจัดการเรคคอร์ดที่ซ้ำกันที่เกิดจากปัญหาระหว่างการทำสำเนาออบเจคต้นทางที่ใช้ภาษา ABAP ที่ไม่มีคีย์หลัก
#XFLD
storageType=พื้นที่จัดเก็บ
#XFLD
skipUnmappedColLbl=ข้ามคอลัมน์ที่ยังไม่ได้แม็ป
#XFLD
abapContentTypeLbl=ประเภทเนื้อหา
#XFLD
autoMergeForTargetLbl=ผสานข้อมูลโดยอัตโนมัติ
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=ทั่วไป
#XFLD
lblBusinessName=ชื่อทางธุรกิจ
#XFLD
lblTechnicalName=ชื่อทางเทคนิค
#XFLD
lblPackage=แพคเกจ
#XFLD
statusPanel=สถานะการดำเนินการ
#XBTN: Schedule dropdown menu
SCHEDULE=กำหนดการ
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=แก้ไขกำหนดการ
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=ลบกำหนดการ
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=สร้างกำหนดการ
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=การตรวจสอบความถูกต้องของกำหนดการล้มเหลว
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=ไม่สามารถสร้างกำหนดการได้เนื่องจากกำลังปรับใช้ผังการทำสำเนา{0}กรุณารอจนกว่าจะมีการปรับใช้ผังการทำสำเนา
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=สำหรับผังการทำสำเนาที่มีออบเจคที่มีประเภทการโหลด "เริ่มต้นและเดลต้า" จะไม่สามารถสร้างกำหนดการได้
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=สำหรับผังการทำสำเนาที่มีออบเจคที่มีประเภทการโหลด "เริ่มต้นและเดลต้า/เดลต้าเท่านั้น" จะไม่สามารถสร้างกำหนดการได้
#XFLD : Scheduled popover
SCHEDULED=จัดกำหนดการแล้ว
#XFLD
CREATE_REPLICATION_TEXT=สร้างผังการทำสำเนา
#XFLD
EDIT_REPLICATION_TEXT=แก้ไขผังการทำสำเนา
#XFLD
DELETE_REPLICATION_TEXT=ลบผังการทำสำเนา
#XFLD
REFRESH_FREQUENCY=ความถี่
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=ไม่สามารถปรับใช้ผังการทำสำเนาได้เนื่องจากกำหนดการที่มีอยู่{0}ยังไม่รองรับประเภทการโหลด "เริ่มต้นและเดลต้า"{0}{0}เมื่อต้องการปรับใช้ผังการทำสำเนา คุณต้องกำหนดประเภทการโหลดของออบเจคทั้งหมด{0}เป็น "เริ่มต้นเท่านั้น" หรือคุณสามารถลบกำหนดการและปรับใช้{0}ผังการทำสำเนา แล้วเริ่มต้นการดำเนินการใหม่ การดำเนินการนี้จะส่งผลให้{0}มีการดำเนินการแบบไม่มีที่สิ้นสุด ซึ่งจะรองรับออบเจคที่มีประเภทการโหลด "เริ่มต้นและเดลต้า"
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=ไม่สามารถปรับใช้ผังการทำสำเนาได้เนื่องจากกำหนดการที่มีอยู่{0}ยังไม่รองรับประเภทการโหลด "เริ่มต้นและเดลต้า/เดลต้าเท่านั้น"{0}{0}เมื่อต้องการปรับใช้ผังการทำสำเนา คุณต้องกำหนดประเภทการโหลดของออบเจคทั้งหมด{0}เป็น "เริ่มต้นเท่านั้น" หรือคุณสามารถลบกำหนดการและปรับใช้{0}ผังการทำสำเนา แล้วเริ่มต้นการดำเนินการใหม่ การดำเนินการนี้จะส่งผลให้{0}มีการดำเนินการแบบไม่มีที่สิ้นสุด ซึ่งจะรองรับออบเจคที่มีประเภทการโหลด "เริ่มต้นและเดลต้า/เดลต้าเท่านั้น"
#XMSG
SCHEDULE_EXCEPTION=ไม่สามารถดึงรายละเอียดกำหนดการ
#XFLD: Label for frequency column
everyLabel=ทุก
#XFLD: Plural Recurrence text for Hour
hoursLabel=ชั่วโมง
#XFLD: Plural Recurrence text for Day
daysLabel=วัน
#XFLD: Plural Recurrence text for Month
monthsLabel=เดือน
#XFLD: Plural Recurrence text for Minutes
minutesLabel=นาที
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=ไม่สามารถดึงข้อมูลเกี่ยวกับความเป็นไปได้ของกำหนดการ
#XFLD :Paused field
PAUSED=ถูกหยุดชั่วคราว
#XMSG
navToMonitoring=เปิดในตัวติดตามตรวจสอบผัง
#XFLD
statusLbl=สถานะ
#XFLD
lblLastRunExecuted=เริ่มต้นการดำเนินการครั้งล่าสุด
#XFLD
lblLastExecuted=การดำเนินการครั้งล่าสุด
#XFLD: Status text for Completed
statusCompleted=เสร็จสมบูรณ์
#XFLD: Status text for Running
statusRunning=กำลังดำเนินการ
#XFLD: Status text for Failed
statusFailed=ล้มเหลว
#XFLD: Status text for Stopped
statusStopped=ถูกหยุด
#XFLD: Status text for Stopping
statusStopping=กำลังหยุด
#XFLD: Status text for Active
statusActive=ใช้งาน
#XFLD: Status text for Paused
statusPaused=ถูกหยุดชั่วคราว
#XFLD: Status text for not executed
lblNotExecuted=ยังไม่ได้ดำเนินการ
#XFLD
messagesSettings=การกำหนดค่าข้อความ
#XTOL
@validateModel=ข้อความการตรวจสอบความถูกต้อง
#XTOL
@hierarchy=ลำดับชั้น
#XTOL
@columnCount=จำนวนคอลัมน์
#XMSG
VAL_PACKAGE_CHANGED=คุณได้กำหนดออบเจคนี้ให้กับแพคเกจ "{1}" คลิก "เก็บบันทึก" เพื่อยืนยันและตรวจสอบการเปลี่ยนแปลงนี้ กรุณาจำไว้ว่าการกำหนดให้กับแพคเกจไม่สามารถเลิกทำได้ในเอดิเตอร์นี้หลังจากที่คุณเก็บบันทึก
#XMSG
MISSING_DEPENDENCY=ไม่สามารถแก้ไขความสัมพันธ์ของออบเจค "{0}" ในแพคเกจ "{1}"
#XFLD
deltaLoadInterval=ช่วงการโหลดเดลต้า
#XFLD
lblHour=ชั่วโมง (0-24)
#XFLD
lblMinutes=นาที (0-59)
#XMSG
maxHourOrMinErr=ป้อนค่าระหว่าง 0 ถึง {0}
#XMSG
maxDeltaInterval=ค่าสูงสุดของช่วงการโหลดเดลต้าคือ 24 ชั่วโมง{0}เปลี่ยนแปลงค่านาทีหรือค่าชั่วโมงให้สอดคล้องกัน
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=พาธของคอนเทนเนอร์เป้าหมาย
#XFLD
confluentSubjectName=ชื่อเรื่อง
#XFLD
confluentSchemaVersion=เวอร์ชันของ Schema
#XFLD
confluentIncludeTechKeyUpdated=รวมคีย์ทางเทคนิค
#XFLD
confluentOmitNonExpandedArrays=ละเว้นอาร์เรย์ที่ไม่ขยาย
#XFLD
confluentExpandArrayOrMap=ขยายอาร์เรย์หรือแผนที่
#XCOL
confluentOperationMapping=การแม็ปการดำเนินการ
#XCOL
confluentOpCode=รหัสการดำเนินการ
#XFLD
confluentInsertOpCode=แทรก
#XFLD
confluentUpdateOpCode=อัพเดท
#XFLD
confluentDeleteOpCode=ลบ
#XFLD
expandArrayOrMapNotSelectedTxt=ไม่ได้เลือก
#XFLD
confluentSwitchTxtYes=ใช่
#XFLD
confluentSwitchTxtNo=ไม่ใช่
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=ข้อผิดพลาด
#XTIT
executeWarning=คำเตือน
#XMSG
executeunsavederror=เก็บบันทึกผังการทำสำเนาของคุณก่อนที่จะดำเนินการ
#XMSG
executemodifiederror=มีการเปลี่ยนแปลงที่ไม่ได้เก็บบันทึกในผังการทำสำเนา กรุณาเก็บบันทึกผังการทำสำเนา
#XMSG
executeundeployederror=คุณต้องปรับใช้ผังการทำสำเนาของคุณก่อนจึงจะสามารถดำเนินการได้
#XMSG
executedeployingerror=กรุณารอให้การปรับใช้เสร็จสิ้น
#XMSG
msgRunStarted=เริ่มดำเนินการแล้ว
#XMSG
msgExecuteFail=ไม่สามารถดำเนินการผังการทำสำเนา
#XMSG
titleExecuteBusy=กรุณารอสักครู่
#XMSG
msgExecuteBusy=เรากำลังจัดเตรียมข้อมูลของคุณเพื่อดำเนินการผังการทำสำเนา
#XTIT
executeConfirmDialog=คำเตือน
#XMSG
msgExecuteWithValidations=ผังการทำสำเนามีข้อผิดพลาดในการตรวจสอบความถูกต้อง ซึ่งอาจทำให้การดำเนินการผังการทำสำเนาล้มเหลว
#XMSG
msgRunDeployedVersion=มีการเปลี่ยนแปลงให้ปรับใช้ เวอร์ชันผังการทำสำเนาที่ปรับใช้ล่าสุดจะถูกดำเนินการ คุณต้องการดำเนินการต่อหรือไม่?
#XBUT
btnExecuteAnyway=ยืนยันการดำเนินการ
#XBUT
btnExecuteClose=ปิด
#XBUT
loaderClose=ปิด
#XTIT
loaderTitle=กำลังโหลด
#XMSG
loaderText=กำลังดึงข้อมูลรายละเอียดจากเซิร์ฟเวอร์
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=ผังการทำสำเนาไปยังการเชื่อมต่อเป้าหมายที่ไม่ใช่ SAP นี้ไม่สามารถเริ่มต้นได้
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=เนื่องจากไม่มีปริมาณขาออกสำหรับเดือนนี้
#XMSG
premiumOutBoundRFAdminErrMsgPart1=ผู้ดูแลระบบสามารถเพิ่มบล็อคพรีเมียมขาออกสำหรับ
#XMSG
premiumOutBoundRFAdminErrMsgPart2=Tenant นี้ เพื่อให้มีปริมาณขาออกสำหรับเดือนนี้


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=ข้อผิดพลาด
#XTIT
deployInfo=ข้อมูล
#XMSG
deployCheckFailException=มีข้อยกเว้นเกิดขึ้นระหว่างการปรับใช้
#XMSG
deployGBQFFDisabled=การปรับใช้ผังการทำสำเนาโดยมีการเชื่อมต่อเป้าหมายไปยัง Google BigQuery ไม่สามารถดำเนินการได้ในขณะนี้ เนื่องจากเรากำลังดำเนินการปรับปรุงฟังก์ชันนี้
#XMSG
deployKAFKAFFDisabled=การปรับใช้ผังการทำสำเนาโดยมีการเชื่อมต่อเป้าหมายไปยัง Apache Kafka ไม่สามารถดำเนินการได้ในขณะนี้ เนื่องจากเรากำลังดำเนินการปรับปรุงฟังก์ชันนี้
#XMSG
deployConfluentDisabled=การปรับใช้ผังการทำสำเนาโดยมีการเชื่อมต่อเป้าหมายไปยัง Confluent Kafka ไม่สามารถดำเนินการได้ในขณะนี้ เนื่องจากเรากำลังดำเนินการปรับปรุงฟังก์ชันนี้
#XMSG
deployInValidDWCTargetDeltaCaptureObject=สำหรับออบเจคเป้าหมายต่อไปนี้ ชื่อตารางการจับข้อมูลเดลต้าถูกใช้โดยตารางอื่นในพื้นที่เก็บข้อมูลแล้ว: {0} คุณต้องเปลี่ยนชื่อออบเจคเป้าหมายเหล่านี้เพื่อให้แน่ใจว่าชื่อตารางการจับข้อมูลเดลต้าที่เกี่ยวข้องไม่ซ้ำกันก่อนที่คุณจะปรับใช้ผังการทำสำเนา
#XMSG
deployDWCSourceFFDisabled=การปรับใช้ผังการทำสำเนาที่มี SAP Datasphere เป็นแหล่งข้อมูลไม่สามารถดำเนินการได้ในขณะนี้ เนื่องจากเรากำลังดำเนินการปรับปรุงฟังก์ชันนี้
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=การปรับใช้ผังการทำสำเนาที่มีตารางภายในที่ใช้เดลต้าได้เป็นออบเจคต้นทางไม่สามารถดำเนินการได้ในขณะนี้ เนื่องจากเรากำลังดำเนินการปรับปรุงฟังก์ชันนี้
#XMSG
deployHDLFSourceFFDisabled=การปรับใช้ผังการทำสำเนาที่มีการเชื่อมต่อต้นทางที่มีประเภทการเชื่อมต่อ SAP HANA Cloud, ไฟล์ Data Lake ไม่สามารถดำเนินการได้ในขณะนี้ เนื่องจากเรากำลังดำเนินการปรับปรุง
#XMSG
deployObjectStoreAsSourceFFDisabled=การปรับใช้ผังการทำสำเนาที่มีผู้ให้บริการพื้นที่จัดเก็บบน Cloud เป็นแหล่งที่มานั้นไม่สามารถทำได้ในขณะนี้
#XMSG
deployConfluentSourceFFDisabled=การปรับใช้ผังการทำสำเนาที่มี Confluent Kafka เป็นแหล่งข้อมูลไม่สามารถดำเนินการได้ในขณะนี้ เนื่องจากเรากำลังดำเนินการปรับปรุงฟังก์ชันนี้
#XMSG
deployMaxDWCNewTableCrossed=สำหรับผังการทำสำเนาขนาดใหญ่ ไม่สามารถ "เก็บบันทึกและปรับใช้" ได้ในคราวเดียว กรุณาเก็บบันทึกผังการทำสำเนาของคุณก่อนแล้วจึงปรับใช้
#XMSG
deployInProgressInfo=การปรับใช้อยู่ระหว่างดำเนินการแล้ว
#XMSG
deploySourceObjectInUse=ออบเจคต้นทาง {0} ถูกใช้อยู่แล้วในผังการทำสำเนา {1}
#XMSG
deployTargetSourceObjectInUse=ออบเจคต้นทาง {0} ถูกใช้อยู่แล้วในผังการทำสำเนา {1} ออบเจคเป้าหมาย {2} ถูกใช้อยู่แล้วในผังการทำสำเนา {3}
#XMSG
deployReplicationFlowCheckError=เกิดข้อผิดพลาดขณะตรวจสอบผังการทำสำเนา: {0}
#XMSG
preDeployTargetObjectInUse=ออบเจคเป้าหมาย {0} ถูกใช้อยู่แล้วในผังการทำสำเนา {1} และคุณไม่สามารถมีออบเจคเป้าหมายเดียวกันในผังการทำสำเนาสองผังที่แตกต่างกัน กรุณาเลือกออบเจคเป้าหมายอื่นแล้วลองอีกครั้ง
#XMSG
runInProgressInfo=ผังการทำสำเนากำลังดำเนินการอยู่
#XMSG
deploySignavioTargetFFDisabled=การปรับใช้ผังการทำสำเนาที่มี SAP Signavio เป็นเป้าหมายไม่สามารถดำเนินการได้ในขณะนี้ เนื่องจากเรากำลังดำเนินการปรับปรุงฟังก์ชันนี้
#XMSG
deployHanaViewAsSourceFFDisabled=การปรับใช้ผังการทำสำเนาที่มีมุมมองเป็นออบเจคต้นทางสำหรับการเชื่อมต่อต้นทางที่เลือกไม่สามารถดำเนินการได้ในขณะนี้ กรุณาลองอีกครั้งภายหลัง
#XMSG
deployMsOneLakeTargetFFDisabled=การปรับใช้ผังการทำสำเนาที่มี MS OneLake เป็นเป้าหมายไม่สามารถดำเนินการได้ในขณะนี้ เนื่องจากเรากำลังดำเนินการปรับปรุงฟังก์ชันนี้
#XMSG
deploySFTPTargetFFDisabled=การปรับใช้ผังการทำสำเนาที่มี SFTP เป็นเป้าหมายไม่สามารถดำเนินการได้ในขณะนี้ เนื่องจากเรากำลังดำเนินการปรับปรุงฟังก์ชันนี้
#XMSG
deploySFTPSourceFFDisabled=การปรับใช้ผังการทำสำเนาที่มี SFTP เป็นแหล่งที่มาไม่สามารถดำเนินการได้ในขณะนี้ เนื่องจากเรากำลังดำเนินการปรับปรุงฟังก์ชันนี้
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=ชื่อทางเทคนิค
#XFLD
businessNameInRenameTarget=ชื่อทางธุรกิจ
#XTOL
renametargetDialogTitle=เปลี่ยนชื่อออบเจคเป้าหมาย
#XBUT
targetRenameButton=เปลี่ยนชื่อ
#XBUT
targetRenameCancel=ยกเลิก
#XMSG
mandatoryTargetName=คุณต้องป้อนชื่อ
#XMSG
dwcSpecialChar=_ (เครื่องหมายขีดล่าง) เป็นอักขระพิเศษตัวเดียวที่อนุญาตให้ใช้
#XMSG
dwcWithDot=ชื่อตารางเป้าหมายสามารถมีตัวอักษรละติน ตัวเลข เครื่องหมายขีดล่าง (_) และเครื่องหมายมหัพภาค (.) โดยอักขระตัวแรกต้องเป็นตัวอักษร ตัวเลข หรือเครื่องหมายขีดล่าง (ไม่ใช่เครื่องหมายมหัพภาค)
#XMSG
nonDwcSpecialChar=อักขระพิเศษที่อนุญาตให้ใช้คือ _ (เครื่องหมายขีดล่าง) - (เครื่องหมายยัติภังค์) และ . (จุด)
#XMSG
firstUnderscorePattern=ชื่อต้องไม่ขึ้นต้นด้วย _ (เครื่องหมายขีดล่าง)

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: ดูคำสั่งสร้างตารางของ SQL
#XMSG
sqlDialogMaxPKWarning=ใน Google BigQuery สนับสนุนคีย์หลักสูงสุด 16 รายการ และออบเจคต้นทางมีจำนวนมากกว่า ดังนั้นจึงไม่มีการกำหนดคีย์หลักในคำสั่งนี้
#XMSG
sqlDialogIncomptiblePKTypeWarning=คอลัมน์ต้นทางอย่างน้อยหนึ่งคอลัมน์มีประเภทข้อมูลที่ไม่สามารถกำหนดเป็นคีย์หลักใน Google BigQuery ได้ ดังนั้นจึงไม่มีการกำหนดคีย์หลักในกรณีนี้ ใน Google BigQuery เฉพาะประเภทข้อมูลต่อไปนี้เท่านั้นที่มีคีย์หลักได้: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=คัดลอกและปิด
#XBUT
closeDDL=ปิด
#XMSG
copiedToClipboard=คัดลอกไปยังคลิปบอร์ดแล้ว


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=ออบเจค ''{0}'' ไม่สามารถเป็นส่วนหนึ่งของเชนงานได้เนื่องจากไม่มีจุดสิ้นสุด (เนื่องจากมีออบเจคที่มีประเภทการโหลด ''เริ่มต้นและเดลต้า/เดลต้าเท่านั้น'')
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=ออบเจค ''{0}'' ไม่สามารถเป็นส่วนหนึ่งของเชนงานได้เนื่องจากไม่มีจุดสิ้นสุด (เนื่องจากมีออบเจคที่มีประเภทการโหลด ''เริ่มต้นและเดลต้า'')
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=ไม่สามารถเพิ่มออบเจค "{0}" ในเชนงาน


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=มีออบเจคเป้าหมายที่ยังไม่ได้เก็บบันทึก กรุณาเก็บบันทึกอีกครั้ง{0}{0} ลักษณะการทำงานของฟีเจอร์นี้มีการเปลี่ยนแปลง: ในอดีต ออบเจคเป้าหมายจะถูกสร้างขึ้นเฉพาะในสภาพแวดล้อมเป้าหมายเมื่อมีการปรับใช้ผังการทำสำเนา{0} แต่ตอนนี้ออบเจคจะถูกสร้างขึ้นเมื่อมีการเก็บบันทึกผังการทำสำเนา ผังการทำสำเนาของคุณถูกสร้างขึ้นก่อนการเปลี่ยนแปลงนี้และมีออบเจคใหม่{0} คุณต้องเก็บบันทึกผังการทำสำเนาอีกครั้งก่อนปรับใช้เพื่อให้ออบเจคใหม่ถูกรวมไว้อย่างถูกต้อง
#XMSG
confirmChangeContentTypeMessage=คุณกำลังจะเปลี่ยนแปลงประเภทเนื้อหา หากคุณดำเนินการดังกล่าว การเลือกเฉพาะส่วนทั้งหมดที่มีอยู่จะถูกลบออก

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=ชื่อเรื่อง
#XFLD
schemaDialogVersionName=เวอร์ชันของ Schema
#XFLD
includeTechKey=รวมคีย์ทางเทคนิค
#XFLD
segementButtonFlat=ราบ
#XFLD
segementButtonNested=ซ้อนกัน
#XMSG
subjectNamePlaceholder=ค้นหาชื่อเรื่อง

#XMSG
@EmailNotificationSuccess=เก็บบันทึกการกำหนดรูปแบบการแจ้งทางอีเมล์เกี่ยวกับรันไทม์แล้ว

#XFLD
@RuntimeEmailNotification=การแจ้งทางอีเมล์เกี่ยวกับรันไทม์

#XBTN
@TXT_SAVE=เก็บบันทึก


