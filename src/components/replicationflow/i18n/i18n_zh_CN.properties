#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=复制流

#XFLD: Edit Schema button text
editSchema=编辑模式

#XTIT : Properties heading
configSchema=配置模式

#XFLD: save changed button text
applyChanges=应用更改


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=选择源连接
#XFLD
sourceContainernEmptyText=选择容器
#XFLD
targetConnectionEmptyText=选择目标连接
#XFLD
targetContainernEmptyText=选择容器
#XFLD
sourceSelectObjectText=选择源对象
#XFLD
sourceObjectCount=源对象（{0}）
#XFLD
targetObjectText=目标对象
#XFLD
confluentBrowseContext=选择上下文
#XBUT
@retry=重试
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=正在进行租户升级。

#XTOL
browseSourceConnection=浏览源连接
#XTOL
browseTargetConnection=浏览目标连接
#XTOL
browseSourceContainer=浏览源容器
#XTOL
browseAndAddSourceDataset=添加源对象
#XTOL
browseTargetContainer=浏览目标容器
#XTOL
browseTargetSetting=浏览目标设置
#XTOL
browseSourceSetting=浏览源设置
#XTOL
sourceDatasetInfo=信息
#XTOL
sourceDatasetRemove=移除
#XTOL
mappingCount=这表示不是基于名称的映射/表达式的总数。
#XTOL
filterCount=这表示筛选条件的总数。
#XTOL
loading=正在加载...
#XCOL
deltaCapture=增量捕获
#XCOL
deltaCaptureTableName=增量捕获表
#XCOL
loadType=加载类型
#XCOL
deleteAllBeforeLoading=加载前全部删除
#XCOL
transformationsTab=投影
#XCOL
settingsTab=设置

#XBUT
renameTargetObjectBtn=重命名目标对象
#XBUT
mapToExistingTargetObjectBtn=映射到现有目标对象
#XBUT
changeContainerPathBtn=更改容器路径
#XBUT
viewSQLDDLUpdated=查看 SQL 创建表语句
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=源对象不支持增量捕获，但所选的目标对象启用了增量捕获选项。
#XMSG
sourceObjectNonDeltaSupportErrorMsg=没能使用目标对象，因为它启用了增量捕获，{0} 而源对象不支持增量捕获。{1} 可以选择其他不支持增量捕获的目标对象。
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=已经存在使用这个名称的目标对象。但是这个目标对象不能使用，{0} 因为它启用了增量捕获，而源对象不 {0} 支持增量捕获。{1} 你可以输入不 {0} 支持增量捕获的现有目标对象的名称，也可以输入还不存在的名称。
#XBUT
copySQLDDLUpdated=复制 SQL 创建表语句
#XMSG
targetObjExistingNoCDCColumnUpdated=Google BigQuery 中现有的表必须包含以下列，才能捕获数据更改 （CDC）：{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=不支持以下源对象，原因是没有主键，或者使用的连接不满足检索主键的条件：
#XMSG
new_infoForUnsupportedDatasetNoKeys2=请参考 SAP KBA 3531135，了解可能的解决方案。
#XLST: load type list values
initial=仅限初始
@emailUpdateError=更新电子邮件通知列表时出错

#XLST
initialDelta=初始和增量

#XLST
deltaOnly=仅限增量
#XMSG
confluentDeltaLoadTypeInfo=对于 Confluent Kafka 源，只支持加载类型 "初始和增量"。
#XMSG
confirmRemoveReplicationObject=是否确认要删除复制？
#XMSG
confirmRemoveReplicationTaskPrompt=这个操作将删除现有复制。是否要继续？
#XMSG
confirmTargetConnectionChangePrompt=这个操作将重置目标连接、目标容器并删除所有目标对象。是否要继续？
#XMSG
confirmTargetContainerChangePrompt=这个操作将重置目标容器并删除所有现有目标对象。是否要继续？
#XMSG
confirmRemoveTransformObject=是否确认要删除投影 {0}？
#XMSG
ErrorMsgContainerChange=更改容器路径时出错。
#XMSG
infoForUnsupportedDatasetNoKeys=以下源对象没有主键，因此不受支持：
#XMSG
infoForUnsupportedDatasetView=不支持以下视图类型的源对象：
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=不支持以下源对象，因为它是包含输入参数的 SQL 视图：
#XMSG
infoForUnsupportedDatasetExtractionDisabled=由于已禁用提取，以下源对象不受支持：
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=对于 Confluent 连接，只可以使用序列化格式 AVRO 和 JSON。下列对象使用其他序列化格式，因此不受支持：
#XMSG
infoForUnsupportedDatasetSchemaNotFound=没能获取以下对象的模式。请选择合适的产品上下文或验证模式注册表配置
#XTOL: warning dialog header on deleting replication task
deleteHeader=删除
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Google BigQuery 不支持 "加载前全部删除" 设置。
#XBUT
DeleteAllBeforeLoadingConfluentInfo="加载前全部删除" 设置会在每次复制前删除并重新创建对象（主题），这也会删除所有分配的消息。
#XTOL
DeleteAllBeforeLoadingLTFInfo=这种目标类型不支持 "加载前全部删除" 设置。
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=技术名称
#XCOL
connBusinessName=业务名称
#XCOL
connDescriptionName=说明
#XCOL
connType=类型
#XMSG
connTblNoDataFoundtxt=没有找到连接
#XMSG
connectionError=获取连接时出错。
#XMSG
connectionCombinationUnsupportedErrorTitle=不支持连接组合
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=目前不支持从 {0} 复制到 {1}。
#XMSG
invalidTargetforSourceHDLFErrorTitle=连接类型组合不受支持
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=不支持从连接类型为 "SAP HANA Cloud，数据湖文件" 的连接复制到 {0}。只可以复制到 SAP Datasphere。

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=选择
#XBUT
containerCancelBtn=取消
#XTOL
containerSelectTooltip=选择
#XTOL
containerCancelTooltip=取消
#XMSG
containerContainerPathPlcHold=容器路径
#XFLD
containerContainertxt=容器
#XFLD
confluentContainerContainertxt=上下文
#XMSG
infoMessageForSLTSelection=只能使用 /SLT/批量传输 ID 作为容器。选择 SLT（如有）下的批量传输 ID 并点击提交。
#XMSG
msgFetchContainerFail=获取容器数据时出错。
#XMSG
infoMessageForSLTHidden=这个连接不支持 SLT 文件夹，因此它们不会出现在下面的列表中。

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=请选择包含子文件夹的容器。
#XMSG
sftpIncludeSubFolderText=假
#XMSG
sftpIncludeSubFolderTextNew=否

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=（还没有筛选映射）
#XMSG
failToFetchRemoteMetadata=获取元数据时出错。
#XMSG
failToFetchData=获取现有目标时出错。
#XCOL
@loadType=加载类型
#XCOL
@deleteAllBeforeLoading=加载前全部删除

#XMSG
@loading=正在加载...
#XFLD
@selectSourceObjects=选择源对象
#XMSG
@exceedLimit=一次导入的对象不可以超过 {0} 个。 请至少取消选择 {1} 个对象。
#XFLD
@objects=对象
#XBUT
@ok=确定
#XBUT
@cancel=取消
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=下一步
#XBUT
btnAddSelection=添加选择
#XTOL
@remoteFromSelection=从选择中移除
#XMSG
@searchInForSearchField=在 {0} 中搜索

#XCOL
@name=技术名称
#XCOL
@type=类型
#XCOL
@location=位置
#XCOL
@label=业务名称
#XCOL
@status=状态

#XFLD
@searchIn=搜索范围：
#XBUT
@available=可用
#XBUT
@selection=选择

#XFLD
@noSourceSubFolder=表和视图
#XMSG
@alreadyAdded=已在图表中
#XMSG
@askForFilter=超过 {0} 项。请输入筛选字符串，减少项目数。
#XFLD: success label
lblSuccess=成功
#XFLD: ready label
lblReady=就绪
#XFLD: failure label
lblFailed=失败
#XFLD: fetching status label
lblFetchingDetail=正在获取详细信息

#XMSG Place holder text for tree filter control
filterPlaceHolder=键入文本以筛选顶级对象
#XMSG Place holder text for server search control
serverSearchPlaceholder=键入并按回车键搜索
#XMSG
@deployObjects=正在导入 {0} 个对象...
#XMSG
@deployObjectsStatus=已导入的对象数：{0}。无法导入的对象数：{1}。

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=没能打开本地资源库浏览器。
#XMSG
@openRemoteSourceBrowserError=没能获取源对象。
#XMSG
@openRemoteTargetBrowserError=没能获取目标对象。
#XMSG
@validatingTargetsError=验证目标时出错。
#XMSG
@waitingToImport=可导入

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=已超出对象的最大数。一个复制流最多可选择 500 个对象。

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=技术名称
#XFLD
sourceObjectBusinessName=业务名称
#XFLD
sourceNoColumns=列数
#XFLD
containerLbl=容器

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=请选择复制流的源连接。
#XMSG
validationSourceContainerNonExist=请选择源连接的容器。
#XMSG
validationTargetNonExist=请选择复制流的目标连接。
#XMSG
validationTargetContainerNonExist=请选择目标连接的容器。
#XMSG
validationTruncateDisabledForObjectTitle=复制到对象存储。
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=只有已设置 "加载前全部删除" 选项或目标中不存在目标对象时，才能复制到云存储。{0}{0}如果仍然要为没有设置 "加载前全部删除" 选项的对象启用复制，请确保运行复制流之前系统中不存在目标对象。
#XMSG
validationTaskNonExist=复制流中必须至少有一项复制。
#XMSG
validationTaskTargetMissing=源为 {0} 的复制必须拥有目标。
#XMSG
validationTaskTargetIsSAC=选定目标为 SAC 项目：{0}
#XMSG
validationTaskTargetIsNonDwcTableKey=选定目标不是支持的本地表：{0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=目标中已存在具有这个名称的对象。但是，由于这个对象不是本地表，因此不可以用作本地资源库复制流的目标对象。
#XMSG
validateSourceTargetSystemDifference=请为复制流选择不同的连接（源和目标）与容器组合。
#XMSG
validateDuplicateSources=一项或多项复制具有重复的源对象名称：{0}。
#XMSG
validateDuplicateTargets=一项或多项复制具有重复的目标对象名称：{0}。
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=源对象 {0} 不支持增量捕获，而目标对象 {1} 支持。请移除复制。
#XMSG
validationTaskTargetObjectLoadTypeMismatch=请为目标对象名称为 {0} 的复制选择加载类型 "初始和增量"。
#XMSG
validationAutoRenameTarget=已重命名目标列。
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=为了能够复制到目标，已经添加自动投影，并已重命名以下目标列：{1}{1} {0} {1}{1}这是以下某个原因造成的：{1}{1}{2} 字符不受支持{1}{2} 前缀已存在
#XMSG
validationAutoRenameTargetDescriptionUpdated=为了能够复制到 Google BigQuery，已添加自动投影，并已重命名以下目标列：{1}{1} {0} {1}{1}这是以下某个原因造成的：{1}{1}{2}列名称已存在{1}{2} 字符不受支持{1}{2} 前缀已存在
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=为了能够复制到 Confluent，已经添加自动投影，并已重命名以下目标列：{1}{1} {0} {1}{1} 这是以下某个原因造成的：{1}{1}{2} 列名称已经存在 {1}{2} 字符不受支持 {1}{2} 前缀已经存在
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=为了能够复制到目标，已经添加自动投影，并已重命名以下目标列：{1}{1} {0} {1}{1} 这是以下某个原因造成的：{1}{1}{2} 列名称已经存在 {1}{2} 字符不受支持 {1}{2} 前缀已经存在
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=已重命名目标对象。
#XMSG
autoRenameInfoDesc=已重命名目标对象，因为这个对象包含不支持的字符。 只支持以下字符：{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.（点）{0}{1}_（下划线）{0}{1}-（破折号）  
#XMSG
validationAutoTargetTypeConversion=已更改目标数据类型。
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=对于以下目标列，已更改目标数据类型，因为 Google BigQuery 中不支持源数据类型：{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=对于以下目标列，已更改目标数据类型，因为目标连接不支持源数据类型：{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=缩短目标列名称。
#XMSG
validationMaxCharLengthGBQTargetDescription=在 Google BigQuery 中，列名称最多可使用 300 个字符。请使用投影缩短以下目标列名称：{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=无法创建主键。
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=在 Google BigQuery 中，最多支持 16 个主键，但源对象的主键数超过这一数量。目标对象中将不会创建任何主键。
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=一个或多个源列的数据类型不能在 Google BigQuery 中定义为主键。目标对象中将不会创建任何主键。{0}{0}以下目标数据类型与 Google BigQuery 数据类型兼容，可为它们定义主键：{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=定义一列或多列作为主键。
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=需要定义一列或多列作为主键，可以使用源模式对话框执行这项操作。
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=定义一列或多列作为主键。
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=必须为源对象定义一个或多个与主键约束匹配的列作为主键。请转到源对象属性的 "配置模式" 完成设置。
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=输入有效的最大分区值。
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=最大分区值需 ≥ 1 且 ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=定义一个或多个列作为主键。
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=要复制对象，需要定义一个或多个目标列作为主键。可以使用投影执行这项操作。
#XMSG
validateHDLFNoPKExistingDatasetError=定义一个或多个列作为主键。
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=要复制数据到现有目标对象，这个对象需要有一个或多个列定义为主键。 {0} 你可以使用以下方法来定义一个或多个列作为主键：{0} {1} 使用本地表编辑器更改现有目标对象。然后重新加载复制流。{0}{1} 重命名复制流中的目标对象。这样，运行一启动就会创建一个新对象。重命名之后，你可以定义一个或多个列作为投影中的主键。{0}{1} 将对象映射到另一个现有目标对象，这个对象已经定义一个或多个列作为主键。
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=资源库中已经存在选定目标：{0}。
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=增量捕获表名称已被资源库中的其他表使用：{0}。请重命名这些目标对象，确保关联的增量捕获表名称是唯一的，这样才能保存复制流。
#XMSG
validateConfluentEmptySchema=定义模式
#XMSG
validateConfluentEmptySchemaDescUpdated=源表没有配置模式。请选择 "配置模式"，定义模式
#XMSG
validationCSVEncoding=CSV 编码无效
#XMSG
validationCSVEncodingDescription=任务的 CSV 编码无效。
#XMSG
validateConfluentEmptySchema=请选择兼容的目标数据类型
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=请选择兼容的目标数据类型
#XMSG
globalValidateTargetDataTypeDesc=列映射时出错。请转至 "投影"，确保所有源列都映射到唯一列，并且该列的数据类型与之兼容，同时确保所有定义的表达式都有效。
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=列名称重复。
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=不支持重复的列名称。请使用投影对话框予以解决。以下目标对象的列名称重复：{0}。
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=列名称重复。
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=不支持重复的列名称。以下目标对象的列名称重复：{0}。
#XMSG
deltaOnlyLoadTypeTittle=数据可能存在不一致的情况。
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=“仅限增量”加载类型不会考虑上次保存和下次运行之间在源中所做的更改。
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=更改加载类型为 "初始"。
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=只有 "仅限初始" 加载类型可以复制基于 ABAP 且没有主键的对象。
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=禁用增量捕获。
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=要使用源连接类型 ABAP 复制没有主键的对象，需要先禁用这个表的增量捕获。
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=更改目标对象。
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=不可以使用目标对象，因为启用了增量捕获。你可以重命名目标对象，然后关闭新（重命名）对象的增量捕获，也可以将源对象映射到禁用了增量捕获的目标对象。
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=更改目标对象。
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=不可以使用目标对象，因为它没有所需的技术列 __load_package_id。你可以使用还不存在的名称重命名目标对象。系统会创建一个与源对象具有相同定义且包含技术列的新对象。或者，你也可以将目标对象映射到具有所需技术列（__load_package_id）的现有对象。
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=更改目标对象。
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=不可以使用目标对象，因为它没有所需的技术列  __load_record_id。你可以使用还不存在的名称重命名目标对象。系统会创建一个与源对象具有相同定义且包含技术列的新对象。或者，你也可以将目标对象映射到具有所需技术列（__load_record_id）的现有对象。
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=更改目标对象。
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=不可以使用目标对象，因为它的技术列  __load_record_id 的数据类型不是 "string(44)"。你可以使用还不存在的名称重命名目标对象。系统会创建一个与源对象具有相同定义，因而数据类型正确的新对象。或者，你也可以将目标对象映射到具有所需技术列（__load_record_id）且数据类型正确的现有对象。
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=更改目标对象。
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=不可以使用目标对象，因为它存在主键，而源对象没有主键。你可以使用还不存在的名称重命名目标对象。系统会创建一个与源对象具有相同定义，因而没有主键的新对象。或者，你也可以将目标对象映射到具有所需技术列（__load_package_id）且没有主键的现有对象。
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=更改目标对象。
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=不可以使用目标对象，因为它存在主键，而源对象没有主键。你可以使用还不存在的名称重命名目标对象。系统会创建一个与源对象具有相同定义，因而没有主键的新对象。或者，你也可以将目标对象映射到具有所需技术列（__load_record_id）且没有主键的现有对象。
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=更改目标对象。
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=不可以使用目标对象，因为它的技术列 __load_package_id 的数据类型不是 "binary(>=256)"。你可以使用还不存在的名称重命名目标对象。系统会创建一个与源对象具有相同定义，因而数据类型正确的新对象。或者，你也可以将目标对象映射到具有所需技术列（__load_package_id）且数据类型正确的现有对象。
#XMSG
validationAutoRenameTargetDPID=已重命名目标列。
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=移除源对象。
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=源对象没有键列，而本上下文中不支持这种情况。
#XMSG
validationAutoRenameTargetDPIDDescription=为了从无键 ABAP 源进行复制，已添加自动投影，并已重命名以下目标列：{1}{1} {0} {1}{1}这是由于以下其中一个原因造成的：{1}{1}{2}列名称已存在{1}{2} 字符不受支持{1}{2} 前缀已存在
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=复制到 {0}。
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=目前不可以保存和部署以 {0} 作为目标的复制流，我们正在对这项功能进行维护。
#XMSG
TargetColumnSkippedLTF=已跳过目标列。
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=数据类型不受支持，已跳过目标列。{0}{1}
#XMSG
validatePKTimeColumnLTF1=时间列作为主键。
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=源对象有一个时间列作为主键，而本上下文中不支持这种情况。
#XMSG
validateNoPKInLTFTarget=缺少主键。
#XMSG
validateNoPKInLTFTargetDescription=目标中没有定义主键，本上下文中不支持这种情况。
#XMSG
validateABAPClusterTableLTF=ABAP 簇表。
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=源对象是 ABAP 簇表，而本上下文中不支持这种情况。
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=你似乎还没有添加任何数据。
#YINS
welcomeText2=要启动复制流，请选择连接和左侧的源对象。

#XBUT
wizStep1=选择源连接
#XBUT
wizStep2=选择源容器
#XBUT
wizStep3=添加源对象

#XMSG
limitDataset=已达到最大对象数。请移除现有对象，再添加新对象，或创建新的复制流。
#XMSG
premiumOutBoundRFCannotStartWarningMsg=没能启动到这个非 SAP 目标连接的复制流，因为本月无可用数据导出量。
#XMSG
premiumOutBoundRFAdminWarningMsg=管理员可以为这个租户增加附加数据导出块，确保本月有可用的数据导出量。
#XMSG
messageForToastForDPIDColumn2=已向 {0} 个对象的目标添加新列 - 用于处理与没有主键的基于 ABAP 的源对象相关的重复记录。
#XMSG
PremiumInboundWarningMessage=根据复制流的数量和要复制的数据量，通过 {1} 复制数据所需的 SAP HANA 资源 {0} 可能会超出租户的可用容量。
#XMSG
PremiumInboundWarningMsg=根据复制流的数量和要复制的数据量，通过 "{1}" 复制数据所需的 SAP HANA 资源 {0} 可能会超出租户的可用容量。
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=请输入投影名称。
#XMSG
emptyTargetColumn=请输入目标列名称。
#XMSG
emptyTargetColumnBusinessName=请输入目标列业务名称。
#XMSG
invalidTransformName=请输入投影名称。
#XMSG
uniqueColumnName=请重命名目标列。
#XMSG
copySourceColumnLbl=从源对象复制列
#XMSG
renameWarning=重命名目标表时，请确保选择唯一的名称。如果空间中已经存在使用新名称的表，将会使用此表的定义。

#XMSG
uniqueColumnBusinessName=重新命名目标列业务名称。
#XMSG
uniqueSourceMapping=请选择其他源列。
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=源列 {0} 已由以下目标列使用：{1}{1}{2}{1}{1} 为了保存投影，请为这个目标列或其他目标列选择一个没有被使用的源列。
#XMSG
uniqueColumnNameDescription=输入的目标列名称已存在。要保存投影，请指定唯一的列名称。
#XMSG
uniqueColumnBusinessNameDesc=目标列业务名称已存在。要保存投影，请输入唯一的列业务名称。
#XMSG
emptySource=请选择源列或输入常量。
#XMSG
emptySourceDescription=要创建有效的映射条目，请选择源列或输入常量值。
#XMSG
emptyExpression=请定义映射。
#XMSG
emptyExpressionDescription1=选择要映射目标列的源列，或者选中 "{0} 函数/常量 {1}" 列的复选框。系统会根据目标数据类型自动输入函数 {2} {2}。你可以手动输入常量值。
#XMSG
numberExpressionErr=请输入数字。
#XMSG
numberExpressionErrDescription=你选择的是数字数据类型。这意味着只能输入数字以及小数点（若适用）。请不要使用单引号。
#XMSG
invalidLength=请输入有效的长度值。
#XMSG
invalidLengthDescription=数据类型的长度必须等于或大于源列的长度，可以介于 1 到 5000 之间。
#XMSG
invalidMappedLength=请输入有效的长度值。
#XMSG
invalidMappedLengthDescription=数据类型的长度必须等于或大于源列 {0} 的长度，可以介于 1 到 5000 之间。
#XMSG
invalidPrecision=请输入有效的精度值。
#XMSG
invalidPrecisionDescription=精度是指数字的总位数。标度是指小数点后数字的位数，可以介于 0 到精度之间。{0}{0}示例：{0}{1}精度为 6 标度为 2 对应的数字类似于 1234.56。{0}{1}精度为 6 标度为 6 对应的数字类似于 0.123546。{0} {0} 目标的精度和标度要与源的精度和标度兼容，这样源的所有数字才能放入目标字段。例如，源的精度为 6，标度为 2（因此，小数点前的数字不能为 0），目标就不能是精度为 6，标度为 6。
#XMSG
invalidPrimaryKey=请至少输入一个主键。
#XMSG
invalidPrimaryKeyDescription=没有为这个模式定义主键。
#XMSG
invalidMappedPrecision=请输入有效的精度值。
#XMSG
invalidMappedPrecisionDescription1=精度是指数字的总位数。标度是指小数点后数字的位数，可以介于 0 到精度之间。{0}{0}示例：{0}{1}精度为 6 标度为 2 对应的数字类似于 1234.56。{0}{1}精度为 6 标度为 6 对应的数字类似于 0.123546。{0}{0}数据类型的精度必须等于或大于源 ({2}) 的精度。
#XMSG
invalidScale=请输入有效的标度值。
#XMSG
invalidScaleDescription=精度是指数字的总位数。标度是指小数点后数字的位数，可以介于 0 到精度之间。{0}{0}示例：{0}{1}精度为 6 标度为 2 对应的数字类似于 1234.56。{0}{1}精度为 6 标度为 6 对应的数字类似于 0.123546。{0} {0} 目标的精度和标度要与源的精度和标度兼容，这样源的所有数字才能放入目标字段。例如，源的精度为 6，标度为 2（因此，小数点前的数字不能为 0），目标就不能是精度为 6，标度为 6。
#XMSG
invalidMappedScale=请输入有效的标度值。
#XMSG
invalidMappedScaleDescription1=精度是指数字的总位数。标度是指小数点后数字的位数，可以介于 0 到精度之间。{0}{0}示例：{0}{1}精度为 6 标度为 2 对应的数字类似于 1234.56。{0}{1}精度为 6 标度为 6 对应的数字类似于 0.123546。{0}{0}数据类型的标度必须等于或大于源 ({2}) 的标度。
#XMSG
nonCompatibleDataType=请选择兼容的目标数据类型。
#XMSG
nonCompatibleDataTypeDescription1=在这里指定的数据类型一定要与源数据类型（{0}）兼容。{1}{1}示例：如果源列使用 STRING 数据类型并包含字母，目标不能使用 DECIMAL 数据类型。
#XMSG
invalidColumnCount=请选择源列。
#XMSG
ObjectStoreInvalidScaleORPrecision=请输入有效的精度和标度。
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=第一个值是精度，即数字的总位数。第二个值是标度，即小数点后数字的位数。输入一个大于源标度的目标标度，并确保输入的目标标度和精度值之间的差值大于源标度和精度值之间的差值。
#XMSG
InvalidPrecisionORScale=请输入有效的精度和标度。
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=第一个值是精度，即数字的总位数。第二个值是标度，即小数点后数字的位数。{0}{0}因为 Google BigQuery 不支持源数据类型，所以会转换为目标数据类型 DECIMAL。在这种情况下，定义的精度只能在 38 到 76 之间，而定义的标度只能在 9 到 38 之间。而且，精度减去标度的结果，即小数点前数字的位数，必须在 29 到 38 之间。
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=第一个值是精度，即数字的总位数。第二个值是标度，即小数点后数字的位数。{0}{0}因为 Google BigQuery 不支持源数据类型，所以会转换为目标数据类型 DECIMAL。在这种情况下，定义的精度必须等于或大于 20。此外，精度减去标度的结果，即小数点前数字的位数，必须等于或大于 20。
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=第一个值是精度，即数字的总位数。第二个值是标度，即小数点后数字的位数。{0}{0}因为目标不支持源数据类型，所以会转换为目标数据类型 DECIMAL。在这种情况下，定义的精度需要是大于或等于 1 且小于或等于 38 的数字，而且标度要小于或等于精度。
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=第一个值是精度，即数字的总位数。第二个值是标度，即小数点后数字的位数。{0}{0}因为目标不支持源数据类型，所以会转换为目标数据类型 DECIMAL。在这种情况下，定义的精度需要等于或大于 20。此外，精度减去标度的结果，即小数点前数字的位数，需要等于或大于 20。
#XMSG
invalidColumnCountDescription=要创建有效的映射条目，需要选择源列或输入常量值。
#XMSG
duplicateColumns=请重命名目标列。
#XMSG
duplicateGBQCDCColumnsDesc=Google BigQuery 中已存在这个目标列名称。需要重命名才能保存投影。
#XMSG
duplicateConfluentCDCColumnsDesc=Confluent 中已存在这个目标列名称。需要重命名才能保存投影。
#XMSG
duplicateSignavioCDCColumnsDesc=SAP Signavio 中已存在这个目标列名称。需要重命名才能保存投影。
#XMSG
duplicateMsOneLakeCDCColumnsDesc=MS OneLake 中已存在这个目标列名称。需要重命名才能保存投影。
#XMSG
duplicateSFTPCDCColumnsDesc=SFTP 中已存在这个目标列名称。需要重命名才能保存投影。
#XMSG
GBQTargetNameWithPrefixUpdated1=目标列名称包含的前缀已存在于 Google BigQuery 中。需要重命名才能保存投影。{0}{0}目标列名称不能以下列字符串开头：{0}{0}{1}{2}_TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=请缩短目标列名称。
#XMSG
GBQtargetMaxLengthDesc=在 Google BigQuery 中，列名称最多可以使用 300 个字符。必须缩短目标列名称，才能保存投影。
#XMSG
invalidMappedScalePrecision=目标列的精度和标度必须与源列的精度和标度兼容，源列的所有数字才能放入目标字段。
#XMSG
invalidMappedScalePrecisionShortText=请输入有效的精度和标度值。
#XMSG
validationIncompatiblePKTypeDescProjection3=一个或多个源列的数据类型不能在 Google BigQuery 中定义为主键。目标对象中将不会创建任何主键。{0}{0}以下目标数据类型与 Google BigQuery 数据类型兼容，可为它们定义主键：{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=取消勾选 column __message_id。
#XMSG
uncheckColumnMessageIdDesc=列：主键
#XMSG
validationOpCodeInsert=需要输入插入值。
#XMSG
recommendDifferentPrimaryKey=建议选择项目级别的不同主键。
#XMSG
recommendDifferentPrimaryKeyDesc=如果已定义操作代码，建议为数组索引和项目选择不同的主键，以避免出现列重复等问题。
#XMSG
selectPrimaryKeyItemLevel=需要为抬头和项目级别都选择至少一个主键。
#XMSG
selectPrimaryKeyItemLevelDesc=展开数组或映射时，需要选择两个主键，一个在抬头级别，一个在项目级别。
#XMSG
invalidMapKey=需要在抬头级别选择至少一个主键。
#XMSG
invalidMapKeyDesc=展开数组或映射时，需要在抬头级别选择主键。
#XFLD
txtSearchFields=搜索目标列
#XFLD
txtName=名称
#XMSG
txtSourceColValidation=不支持一个或多个源列：
#XMSG
txtMappingCount=映射（{0}）
#XMSG
schema=模式
#XMSG
sourceColumn=源列
#XMSG
warningSourceSchema=对模式所做的任何更改都会影响投影对话框中的映射。
#XCOL
txtTargetColName=目标列（技术名称）
#XCOL
txtDataType=目标数据类型
#XCOL
txtSourceDataType=源数据类型
#XCOL
srcColName=源列（技术名称）
#XCOL
precision=精度
#XCOL
scale=标度
#XCOL
functionsOrConstants=函数/常量
#XCOL
txtTargetColBusinessName=目标列（业务名称）
#XCOL
prKey=主键
#XCOL
txtProperties=属性
#XBUT
txtOK=保存
#XBUT
txtCancel=取消
#XBUT
txtRemove=移除
#XFLD
txtDesc=说明
#XMSG
rftdMapping=映射
#XFLD
@lblColumnDataType=数据类型
#XFLD
@lblColumnTechnicalName=技术名称
#XBUT
txtAutomap=自动映射
#XBUT
txtUp=上
#XBUT
txtDown=下

#XTOL
txtTransformationHeader=投影
#XTOL
editTransformation=编辑
#XTOL
primaryKeyToolip=键


#XMSG
rftdFilter=筛选器
#XMSG
rftdFilterColumnCount=源：{0}({1})
#XTOL
rftdFilterColSearch=搜索
#XMSG
rftdFilterColNoData=没有要显示的列
#XMSG
rftdFilteredColNoExps=没有筛选器表达式
#XMSG
rftdFilterSelectedColTxt=添加此筛选器
#XMSG
rftdFilterTxt=筛选器可用于
#XBUT
rftdFilterSelectedAddColExp=添加表达式
#YINS
rftdFilterNoSelectedCol=选择要添加筛选器的列。
#XMSG
rftdFilterExp=筛选器表达式
#XMSG
rftdFilterNotAllowedColumn=这个列不支持添加筛选器。
#XMSG
rftdFilterNotAllowedHead=列不受支持
#XMSG
rftdFilterNoExp=未定义筛选器
#XTOL
rftdfilteredTt=已筛选
#XTOL
rftdremoveexpTt=移除筛选器表达式
#XTOL
validationMessageTt=验证消息
#XTOL
rftdFilterDateInp=选择日期
#XTOL
rftdFilterDateTimeInp=选择日期时间
#XTOL
rftdFilterTimeInp=选择时间
#XTOL
rftdFilterInp=输入值
#XMSG
rftdFilterValidateEmptyMsg={1} 列中的 {0} 筛选器表达式为空
#XMSG
rftdFilterValidateInvalidNumericMsg={1} 列中的 {0} 筛选器表达式包含无效的数值
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=筛选器表达式必须包含有效的数值
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=如果目标对象模式发生了变化，请使用主页上的 "映射到现有目标对象" 功能来适应变化，并将目标对象重新映射到源。
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=如果目标表已存在，并且映射包含模式更改，请在部署复制流之前先相应更改目标表。
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=如果映射涉及模式更改，请在部署复制流之前先相应更改目标表。
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=已从源定义跳过以下不支持的列：{0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=已从目标定义跳过以下不支持的列：{0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=以下对象已经公开以供使用，所以不受支持：{0} {1} {0} {0} 要在复制流中使用表，语义用法（在表设置中）不可以设置为{2}分析数据集{2}。
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=目标对象已经公开以供使用，所以没能使用。{0} {0} 要在复制流中使用表，语义用法（在表设置中）不可以设置为{1}分析数据集{1}。
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=使用这个名称的目标对象已经存在，但已公开以供使用，所以没能使用。{0} {0} 要在复制流中使用表，语义用法（在表设置中）不可以设置为{1}分析数据集{1}。
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=目标中已存在具有这个名称的对象。{0}但是，由于这个对象不是本地表，因此不可以用作本地资源库复制流的目标对象。
#XMSG:
targetAutoRenameUpdated=已重命名目标列。
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=为了在 Google BigQuery 中进行复制，目标列已重命名。这是由于以下其中一个原因造成的：{0}{1}{2}列名称已存在{3}{2}字符不受支持{3}{2}前缀已存在{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=为了在 Confluent 中进行复制，目标列已重命名。这是由于以下其中一个原因造成的：{0} {1} {2} 列名称已存在{3}{2}字符不受支持 {3}{2}前缀已存在 {3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=为了在目标中进行复制，目标列已重命名。这是由于以下其中一个原因造成的：{0} {1}{2}字符不受支持 {3}{2}前缀已存在 {3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=为了在目标中进行复制，目标列已重命名。这是由于以下其中一个原因造成的：{0}{1}{2}列名称已存在{3}{2}字符不受支持 {3}{2}前缀已存在 {3}{4}
#XMSG:
targetAutoDataType=目标数据类型已更改。
#XMSG:
targetAutoDataTypeDesc=目标数据类型已更改为 {0}，因为 Google BigQuery 不支持源数据类型。
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=目标数据类型已更改为 {0}，因为目标连接不支持源数据类型。
#XMSG
projectionGBQUnableToCreateKey=无法创建主键。
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=在 Google BigQuery 中，最多支持 16 个主键，但源对象的主键数超过这一数量。目标对象中将不会创建任何主键。
#XMSG
HDLFNoKeyError=定义一个或多个列作为主键。
#XMSG
HDLFNoKeyErrorDescription=要复制对象，需要定义一个或多个列作为主键。
#XMSG
HDLFNoKeyErrorExistingTarget=定义一个或多个列作为主键。
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=要复制数据到现有目标对象，这个对象需要有一个或多个列定义为主键。{0}{0} 你可以使用以下方法来定义一个或多个列作为主键：{0}{0}{1}使用本地表编辑器更改现有目标对象。然后重新加载复制流。{0}{0}{1} 重命名复制流中的目标对象。这样，运行一启动就会创建一个新对象。重命名之后，你可以定义一个或多个列作为投影中的主键。{0}{0}{1} 将对象映射到另一个现有目标对象，这个对象已经定义一个或多个列作为主键。
#XMSG
HDLFSourceTargetDifferentKeysWarning=主键已更改。
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=相比源对象，你定义了不同的列作为目标对象的主键。请确保这些列唯一识别所有行，以便在稍后复制数据时，避免出现可能的数据损坏。{0} {0} 在源对象中，以下列定义为主键：{0} {1}
#XMSG
duplicateDPIDColumns=请重命名目标列。
#XMSG
duplicateDPIDDColumnsDesc1=技术列已存在这个目标列名称。请输入其他名称来保存投影。
#XMSG:
targetAutoRenameDPID=已重命名目标列。
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=为了从无键 ABAP 源进行复制，目标列已重命名。这是由于以下其中一个原因造成的：{0} {1} {2} 列名称已存在 {3}{2} 字符不受支持 {3}{2} 前缀已存在{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle={0} 目标设置
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle={0} 源设置
#XBUT
connectionSettingSave=保存
#XBUT
connectionSettingCancel=取消
#XBUT: Button to keep the object level settings
txtKeep=保留
#XBUT: Button to overwrite the Object level settings
txtOverwrite=覆盖
#XFLD
targetConnectionThreadlimit=初始加载的目标线程限制 (1-100)
#XFLD
connectionThreadLimit=初始加载的源线程限制 (1-100)
#XFLD
maxConnection=复制线程限制 (1-100)
#XFLD
kafkaNumberOfPartitions=分区数
#XFLD
kafkaReplicationFactor=复制因子
#XFLD
kafkaMessageEncoder=消息编码器
#XFLD
kafkaMessageCompression=消息压缩
#XFLD
fileGroupDeltaFilesBy=增量分组方式
#XFLD
fileFormat=文件类型
#XFLD
csvEncoding=CSV 编码
#XFLD
abapExitLbl=ABAP 退出
#XFLD
deltaPartition=增量加载的对象线程计数 (1-10)
#XFLD
clamping_Data=数据截断导致失败
#XFLD
fail_On_Incompatible=数据不兼容导致失败
#XFLD
maxPartitionInput=最大分区数
#XFLD
max_Partition=定义最大分区数
#XFLD
include_SubFolder=包括子文件夹
#XFLD
fileGlobalPattern=文件名的全局模式
#XFLD
fileCompression=文件压缩
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=文件分隔符
#XFLD
fileIsHeaderIncluded=文件标题
#XFLD
fileOrient=定向
#XFLD
gbqWriteMode=写入模式
#XFLD
suppressDuplicate=禁止重复项
#XFLD
apacheSpark=启用 Apache Spark 兼容性
#XFLD
clampingDatatypeCb=限制取值范围的十进制浮点数据类型
#XFLD
overwriteDatasetSetting=覆盖对象级别的目标设置
#XFLD
overwriteSourceDatasetSetting=覆盖对象级别的源设置
#XMSG
kafkaInvalidConnectionSetting=输入介于 {0} 和 {1} 之间的数字。
#XMSG
MinReplicationThreadErrorMsg=输入大于 {0} 的数字。
#XMSG
MaxReplicationThreadErrorMsg=输入小于 {0} 的数字。
#XMSG
DeltaThreadErrorMsg=输入 1 到 10 之间的值。
#XMSG
MaxPartitionErrorMsg=输入 1 <= x <= 2147483647 之间的值。默认值为 10。
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=输入介于 {0} 和 {1} 之间的整数。
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=使用代理的复制因子
#XFLD
serializationFormat=序列化格式
#XFLD
compressionType=压缩类型
#XFLD
schemaRegistry=使用模式注册表
#XFLD
subjectNameStrat=主题名称策略
#XFLD
compatibilityType=兼容类型
#XFLD
confluentTopicName=主题名称
#XFLD
confluentRecordName=记录名称
#XFLD
confluentSubjectNamePreview=主题名称预览
#XMSG
serializationChangeToastMsgUpdated2=没有启用模式注册表，序列化格式已更改为 JSON。 要将序列化格式更改回 AVRO，需要先启用模式注册表。
#XBUT
confluentTopicNameInfo=主题名称始终基于目标对象名称。 你可以通过重命名目标对象来进行更改。
#XMSG
emptyRecordNameValidationHeaderMsg=输入记录名称。
#XMSG
emptyPartionHeader=输入分区数。
#XMSG
invalidPartitionsHeader=输入有效的分区数。
#XMSG
invalidpartitionsDesc=输入介于 1 和 200,000 之间的数字。
#XMSG
emptyrFactorHeader=输入复制因子。
#XMSG
invalidrFactorHeader=输入有效的复制因子。
#XMSG
invalidrFactorDesc=输入介于 1 和 32,767 之间的数字。
#XMSG
emptyRecordNameValidationDescMsg=如果使用序列化格式 "AVRO"，只支持以下字符：{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _（下划线）
#XMSG
validRecordNameValidationHeaderMsg=输入有效的记录名称。
#XMSG
validRecordNameValidationDescMsgUpdated=使用的序列化格式为 "AVRO"，因此记录名称只可以包含字母数字（A-Z、a-z、0-9）和下划线（_）字符，并且需要以字母或下划线开头。
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=只要一个或多个对象的加载类型是 "初始和增量"，就可以设置 "增量加载的对象线程计数"。 
#XMSG
invalidTargetName=列名称无效
#XMSG
invalidTargetNameDesc=目标列名称只可以包含字母数字（A-Z、a-z、0-9）和下划线（_）字符。
#XFLD
consumeOtherSchema=使用其他模式版本
#XFLD
ignoreSchemamissmatch=忽略模式不匹配
#XFLD
confleuntDatatruncation=数据截断导致失败
#XFLD
isolationLevel=隔离级别
#XFLD
confluentOffset=起点
#XFLD
signavioGroupDeltaFilesByText=无
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=否
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=否

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=投影
#XBUT
txtAdd=添加
#XBUT
txtEdit=编辑
#XMSG
transformationText=添加投影，以设置筛选器或映射。
#XMSG
primaryKeyRequiredText=通过配置模式选择主键。
#XFLD
lblSettings=设置
#XFLD
lblTargetSetting={0}：目标设置
#XMSG
@csvRF=选择包含要应用于文件夹中所有文件的模式定义的文件。
#XFLD
lblSourceColumns=源列
#XFLD
lblJsonStructure=JSON 结构
#XFLD
lblSourceSetting={0}：源设置
#XFLD
lblSourceSchemaSetting={0}：源模式设置
#XBUT
messageSettings=消息设置
#XFLD
lblPropertyTitle1=对象属性
#XFLD
lblRFPropertyTitle=复制流属性
#XMSG
noDataTxt=没有待显示的列。
#XMSG
noTargetObjectText=没有选择目标对象。
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=目标列
#XMSG
searchColumns=搜索列
#XTOL
cdcColumnTooltip=增量捕获列
#XMSG
sourceNonDeltaSupportErrorUpdated=源对象不支持增量捕获。
#XMSG
targetCDCColumnAdded=已添加 2 个目标列，用于增量捕获。
#XMSG
deltaPartitionEnable=已向源设置添加增量加载的对象线程限制。
#XMSG
attributeMappingRemovalTxt=正在移除新目标对象不支持的无效映射。
#XMSG
targetCDCColumnRemoved=已移除用于增量捕获的 2 个目标列。
#XMSG
replicationLoadTypeChanged=加载类型已更改为 "初始和增量"。
#XMSG
sourceHDLFLoadTypeError=更改加载类型为 "初始和增量"。
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=要从连接类型为 SAP HANA Cloud，数据湖文件的源连接复制对象到 SAP Datasphere，需要使用加载类型 "初始和增量"。
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=启用增量捕获。
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=要从连接类型为 SAP HANA Cloud，数据湖文件的源连接复制对象到 SAP Datasphere，需要启用增量捕获。
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=更改目标对象。
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=不可以使用目标对象，因为禁用了增量捕获。你可以重命名目标对象（允许创建启用增量捕获的新对象），或者将其映射到启用了增量捕获的现有对象。
#XMSG
deltaPartitionError=输入增量加载的有效对象线程计数。
#XMSG
deltaPartitionErrorDescription=输入 1 到 10 之间的值。
#XMSG
deltaPartitionEmptyError=输入增量加载的对象线程计数。
#XFLD
@lblColumnDescription=说明
#XMSG
@lblColumnDescriptionText1=出于技术目的，处理在复制基于 ABAP 且没有主键的源对象期间出现的问题而导致的重复记录。
#XFLD
storageType=存储
#XFLD
skipUnmappedColLbl=跳过没有映射的列
#XFLD
abapContentTypeLbl=内容类型
#XFLD
autoMergeForTargetLbl=自动合并数据
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=常规
#XFLD
lblBusinessName=业务名称
#XFLD
lblTechnicalName=技术名称
#XFLD
lblPackage=包
#XFLD
statusPanel=运行状态
#XBTN: Schedule dropdown menu
SCHEDULE=计划
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=编辑 计划
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=删除 计划
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=创建 计划
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=计划验证检查失败
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=当前正在部署复制流，所以不能创建计划。{0} 请等待复制流部署完成。
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=如果复制流包含加载类型为 "初始和增量" 的对象，则不可以创建计划。
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=如果复制流包含加载类型为“初始和增量/仅限增量”的对象，则不可以创建计划。
#XFLD : Scheduled popover
SCHEDULED=已计划
#XFLD
CREATE_REPLICATION_TEXT=创建复制流
#XFLD
EDIT_REPLICATION_TEXT=编辑复制流
#XFLD
DELETE_REPLICATION_TEXT=删除复制流
#XFLD
REFRESH_FREQUENCY=频率
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=现有计划 {0} 还不支持加载类型 "初始和增量"，所以不可以部署复制流。{0}{0}要部署复制流，需要将所有对象 {0} 的加载类型设置为 "仅限初始"。也可以删除计划，部署 {0} 复制流，然后启动新运行。这样就会持续运行 {0}，并且还支持加载类型为 "初始和增量" 的对象。
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=现有计划{0}还不支持加载类型“初始和增量/仅限增量”，所以不可以部署复制流。{0}{0}要部署复制流，需要将所有对象{0}的加载类型设置为“仅限初始”。也可以删除计划，部署{0}复制流，然后启动新运行。这样就会持续运行{0}，并且还支持加载类型为“初始和增量/仅限增量”的对象。
#XMSG
SCHEDULE_EXCEPTION=没能获取计划详细信息
#XFLD: Label for frequency column
everyLabel=每
#XFLD: Plural Recurrence text for Hour
hoursLabel=小时
#XFLD: Plural Recurrence text for Day
daysLabel=天
#XFLD: Plural Recurrence text for Month
monthsLabel=个月
#XFLD: Plural Recurrence text for Minutes
minutesLabel=分钟
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=没能获取计划可能性信息。
#XFLD :Paused field
PAUSED=已暂停
#XMSG
navToMonitoring=在流监控器中打开
#XFLD
statusLbl=状态
#XFLD
lblLastRunExecuted=上次运行开始
#XFLD
lblLastExecuted=上次运行
#XFLD: Status text for Completed
statusCompleted=已完成
#XFLD: Status text for Running
statusRunning=运行中
#XFLD: Status text for Failed
statusFailed=失败
#XFLD: Status text for Stopped
statusStopped=已停止
#XFLD: Status text for Stopping
statusStopping=正在停止
#XFLD: Status text for Active
statusActive=已激活
#XFLD: Status text for Paused
statusPaused=已暂停
#XFLD: Status text for not executed
lblNotExecuted=还没运行
#XFLD
messagesSettings=消息设置
#XTOL
@validateModel=验证消息
#XTOL
@hierarchy=层次结构
#XTOL
@columnCount=列数
#XMSG
VAL_PACKAGE_CHANGED=已经将这个对象分配给包 "{1}"。点击 "保存"，确认并验证这项更改。请注意，保存后将不能在这个编辑器中撤销对包的分配。
#XMSG
MISSING_DEPENDENCY=对象 "{0}" 的依赖项不可以在包 "{1}" 中解析。
#XFLD
deltaLoadInterval=增量加载间隔
#XFLD
lblHour=小时 (0-24)
#XFLD
lblMinutes=分钟 (0-59)
#XMSG
maxHourOrMinErr=请输入 0 至 {0} 之间的值
#XMSG
maxDeltaInterval=增量加载间隔的最大值是 24 小时。{0} 相应地更改分钟值或小时值。
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=目标容器路径
#XFLD
confluentSubjectName=主题名称
#XFLD
confluentSchemaVersion=模式版本
#XFLD
confluentIncludeTechKeyUpdated=包含技术键
#XFLD
confluentOmitNonExpandedArrays=忽略未展开的数组
#XFLD
confluentExpandArrayOrMap=展开数组或映射
#XCOL
confluentOperationMapping=操作映射
#XCOL
confluentOpCode=操作代码
#XFLD
confluentInsertOpCode=插入
#XFLD
confluentUpdateOpCode=更新
#XFLD
confluentDeleteOpCode=删除
#XFLD
expandArrayOrMapNotSelectedTxt=未选择
#XFLD
confluentSwitchTxtYes=是
#XFLD
confluentSwitchTxtNo=否
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=错误
#XTIT
executeWarning=警告
#XMSG
executeunsavederror=在运行前保存复制流。
#XMSG
executemodifiederror=复制流中存在没有保存的更改。请保存复制流。
#XMSG
executeundeployederror=请先部署复制流，然后再运行。
#XMSG
executedeployingerror=请等待部署完成。
#XMSG
msgRunStarted=已开始运行
#XMSG
msgExecuteFail=没能运行复制流
#XMSG
titleExecuteBusy=请稍候。
#XMSG
msgExecuteBusy=正在准备数据，以便运行复制流。
#XTIT
executeConfirmDialog=警告
#XMSG
msgExecuteWithValidations=复制流存在验证错误。运行复制流可能失败。
#XMSG
msgRunDeployedVersion=存在要部署的更改。将运行复制流的上次部署版本。是否要继续？
#XBUT
btnExecuteAnyway=仍要运行
#XBUT
btnExecuteClose=关闭
#XBUT
loaderClose=关闭
#XTIT
loaderTitle=正在加载
#XMSG
loaderText=正在从服务器获取详细信息
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=没能启动到这个非 SAP 目标连接的复制流，
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=因为本月无可用数据导出量。
#XMSG
premiumOutBoundRFAdminErrMsgPart1=管理员可以为这个租户增加附加数据导出块，
#XMSG
premiumOutBoundRFAdminErrMsgPart2=确保本月有可用的数据导出量。


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=错误
#XTIT
deployInfo=信息
#XMSG
deployCheckFailException=部署期间出现异常
#XMSG
deployGBQFFDisabled=目前无法通过与 Google BigQuery 的目标连接来部署复制流，因为我们正在对这项功能进行维护。
#XMSG
deployKAFKAFFDisabled=当前不能部署目标连接到 Apache Kafka 的复制流，我们正在维护这项功能。
#XMSG
deployConfluentDisabled=目前，不可以部署目标连接到 Confluent Kafka 的复制流，我们正在维护这项功能。
#XMSG
deployInValidDWCTargetDeltaCaptureObject=对于以下目标对象，增量捕获表名称已被资源库中的其他表使用：{0}。请重命名这些目标对象，确保关联的增量捕获表名称是唯一的，这样才能部署复制流。
#XMSG
deployDWCSourceFFDisabled=目前不可以部署以 SAP Datasphere 作为源的复制流，我们正在维护这项功能。
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=目前不可以部署包含启用了增量捕获的本地表作为源对象的复制流，我们正在维护这项功能。
#XMSG
deployHDLFSourceFFDisabled=正在进行维护，当前不可以部署包含连接类型为 "SAP HANA Cloud，数据湖文件" 的源连接的复制流。
#XMSG
deployObjectStoreAsSourceFFDisabled=目前不可以部署以云存储提供商作为源的复制流。
#XMSG
deployConfluentSourceFFDisabled=目前不可以部署以 Confluent Kafka 作为源的复制流，我们正在维护这项功能。
#XMSG
deployMaxDWCNewTableCrossed=对于大型复制流程，不可以一次性 "保存并部署"。请先保存复制流程，然后再部署。
#XMSG
deployInProgressInfo=正在部署。
#XMSG
deploySourceObjectInUse=源对象 {0} 已在复制流 {1} 中使用。
#XMSG
deployTargetSourceObjectInUse=源对象 {0} 已在复制流 {1} 中使用。目标对象 {2} 已在复制流 {3} 中使用。
#XMSG
deployReplicationFlowCheckError=验证复制流时出错：{0}
#XMSG
preDeployTargetObjectInUse=目标对象 {0} 已在复制流 {1} 中使用，并且在两个不同的复制流中不能具有相同的目标对象。请选择另一个目标对象，然后重试。
#XMSG
runInProgressInfo=复制流已经在运行。
#XMSG
deploySignavioTargetFFDisabled=目前不可以部署以 SAP Signavio 作为目标的复制流，我们正在对这项功能进行维护。
#XMSG
deployHanaViewAsSourceFFDisabled=目前不可以为选定源连接部署以视图作为源对象的复制流。请稍后重试。
#XMSG
deployMsOneLakeTargetFFDisabled=目前不可以部署以 MS OneLake 作为目标的复制流，我们正在对这项功能进行维护。
#XMSG
deploySFTPTargetFFDisabled=目前不可以部署以 SFTP 作为目标的复制流，我们正在对这项功能进行维护。
#XMSG
deploySFTPSourceFFDisabled=目前不可以部署以 SFTP 作为源的复制流，我们正在对这项功能进行维护。
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=技术名称
#XFLD
businessNameInRenameTarget=业务名称
#XTOL
renametargetDialogTitle=重命名目标对象
#XBUT
targetRenameButton=重命名
#XBUT
targetRenameCancel=取消
#XMSG
mandatoryTargetName=请输入名称。
#XMSG
dwcSpecialChar=_（下划线）是唯一允许的特殊字符。
#XMSG
dwcWithDot=目标表的名称可以由拉丁字母、数字、下划线（_）和句点（.）组成。第一个字符必须是字母、数字或下划线（不能是句点）。
#XMSG
nonDwcSpecialChar=可以使用的特殊字符包括 _（下划线）、-（连字符）和.（点）
#XMSG
firstUnderscorePattern=名称不得以 _（下划线）开头

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}：查看 SQL 创建表语句
#XMSG
sqlDialogMaxPKWarning=在 Google BigQuery 中，最多支持 16 个主键，源对象的主键数已超过这一限制。因此，在这个语句中不会定义任何主键。
#XMSG
sqlDialogIncomptiblePKTypeWarning=一个或多个源列的数据类型无法在 Google BigQuery 中定义为主键。因此，在这种情况下不会定义任何主键。在 Google BigQuery 中，只有以下数据类型才能拥有主键：BIGNUMERIC、BOOLEAN、DATE、DATETIME、INT64、NUMERIC、STRING、TIMESTAMP
#XBUT
copyAndCloseDDL=复制并关闭
#XBUT
closeDDL=关闭
#XMSG
copiedToClipboard=已复制到剪贴板


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=对象“{0}”不可以是任务链的一部分，因为它不会终止（它包含加载类型为“初始和增量/仅限增量”的对象）。
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=对象“{0}”不可以是任务链的一部分，因为它不会终止（它包含加载类型为“初始和增量”的对象）。
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=没能将对象 "{0}" 添加到任务链。


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=存在没有保存的目标对象。请重新保存。{0}{0}这项功能的行为已发生变化：过去，只有在部署复制流时，才会在目标环境中创建目标对象。{0}现在，在保存复制流时，对象便已创建。你的复制流是在这次更改之前创建的，并且包含新对象。{0}你需要在部署之前再次保存复制流，以便正确包含新对象。
#XMSG
confirmChangeContentTypeMessage=你即将更改内容类型。如果这样做，所有现有的投影都将被删除。

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=主题名称
#XFLD
schemaDialogVersionName=模式版本
#XFLD
includeTechKey=包含技术键
#XFLD
segementButtonFlat=平展
#XFLD
segementButtonNested=嵌套
#XMSG
subjectNamePlaceholder=搜索主题名称

#XMSG
@EmailNotificationSuccess=运行时电子邮件通知的配置已保存。

#XFLD
@RuntimeEmailNotification=运行时电子邮件通知

#XBTN
@TXT_SAVE=保存


