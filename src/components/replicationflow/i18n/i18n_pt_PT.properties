#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Fluxo de replicação

#XFLD: Edit Schema button text
editSchema=Editar esquema

#XTIT : Properties heading
configSchema=Configurar esquema

#XFLD: save changed button text
applyChanges=Aplicar alterações


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Selecionar ligação de origem
#XFLD
sourceContainernEmptyText=Selecionar contentor
#XFLD
targetConnectionEmptyText=Selecionar ligação de destino
#XFLD
targetContainernEmptyText=Selecionar contentor
#XFLD
sourceSelectObjectText=Selecionar objeto de origem
#XFLD
sourceObjectCount=Objetos de origem ({0})
#XFLD
targetObjectText=Objetos de destino
#XFLD
confluentBrowseContext=Selecionar contexto
#XBUT
@retry=Repetir
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Atualização de inquilino em curso.

#XTOL
browseSourceConnection=Procurar ligação de origem
#XTOL
browseTargetConnection=Procurar ligação de destino
#XTOL
browseSourceContainer=Procurar contentor de origem
#XTOL
browseAndAddSourceDataset=Adicionar objetos de origem
#XTOL
browseTargetContainer=Procurar contentor de destino
#XTOL
browseTargetSetting=Procurar definições de destino
#XTOL
browseSourceSetting=Procurar definições de origem
#XTOL
sourceDatasetInfo=Informação
#XTOL
sourceDatasetRemove=Remover
#XTOL
mappingCount=Isto representa o número total de mapeamentos/expressões não baseados no nome.
#XTOL
filterCount=Isto representa o número total de condições de filtro.
#XTOL
loading=A carregar...
#XCOL
deltaCapture=Captura delta
#XCOL
deltaCaptureTableName=Tabela de captura delta
#XCOL
loadType=Carregar tipo
#XCOL
deleteAllBeforeLoading=Eliminar tudo antes de carregar
#XCOL
transformationsTab=Projeções
#XCOL
settingsTab=Definições

#XBUT
renameTargetObjectBtn=Renomear objeto de destino
#XBUT
mapToExistingTargetObjectBtn=Mapear para objeto de destino existente
#XBUT
changeContainerPathBtn=Alterar caminho do contentor
#XBUT
viewSQLDDLUpdated=Visualizar instrução SQL Criar tabela
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=O objeto de origem não suporta captura delta, mas o objeto de destino selecionado tem a opção de captura delta ativada.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=O objeto de destino não pode ser utilizado porque a captura delta está ativada,{0}enquanto o objeto de origem não suporta a captura delta.{1}Pode selecionar outro objeto de destino que não suporte a captura delta.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Já existe um objeto de destino com este nome. Contudo, não pode ser utilizado{0}porque a captura delta está ativada, enquanto o objeto de origem{0}não suporta a captura delta.{1}Pode introduzir o nome de um objeto de destino existente que não{0}suporte a captura delta ou introduzir um nome que ainda não exista.
#XBUT
copySQLDDLUpdated=Copiar instrução SQL Criar tabela
#XMSG
targetObjExistingNoCDCColumnUpdated=As tabelas existentes no Google BigQuery têm de incluir as seguintes colunas para a captura de dados de alteração (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Os seguintes objetos de origem não são suportados porque não têm uma chave primária ou estão a utilizar uma ligação que não cumpre as condições para obter a chave primária:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Consulte SAP KBA 3531135 para uma possível solução.
#XLST: load type list values
initial=Apenas inicial
@emailUpdateError=Erro ao atualizar lista de notificações por e-mail

#XLST
initialDelta=Inicial e delta

#XLST
deltaOnly=Só delta
#XMSG
confluentDeltaLoadTypeInfo=Para a origem de Confluent Kafka, só são suportados os tipos de carregamento Inicial e Delta.
#XMSG
confirmRemoveReplicationObject=Confirma que pretende eliminar a replicação?
#XMSG
confirmRemoveReplicationTaskPrompt=Esta ação eliminará as replicações existentes. Pretende continuar?
#XMSG
confirmTargetConnectionChangePrompt=Esta ação reporá a ligação de destino, o contentor de destino e eliminará todos os objetos de destino. Pretende continuar?
#XMSG
confirmTargetContainerChangePrompt=Esta ação reporá o contentor de destino e eliminará todos os objetos de destino existentes. Pretende continuar?
#XMSG
confirmRemoveTransformObject=Confirma que pretende eliminar a projeção {0}?
#XMSG
ErrorMsgContainerChange=Ocorreu um erro ao alterar o caminho do contentor.
#XMSG
infoForUnsupportedDatasetNoKeys=Os objetos de origem que se seguem não são suportados porque não têm uma chave primária:
#XMSG
infoForUnsupportedDatasetView=Os seguintes objetos de origem do tipo Vistas não são suportados:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Os seguinte objeto de origem não é suportado, uma vez que é uma vista SQL que contém parâmetros de entrada:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Os objetos de origem que se seguem não são suportados porque têm a extração desativada:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Para ligações Confluent, os únicos formatos de serialização permitidos são AVRO e JSON. Os seguintes objetos não são suportados porque utilizam um formato de serialização diferente:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Impossível obter o esquema para os seguintes objetos. Selecione o contexto apropriado ou verifique a configuração do registro do esquema
#XTOL: warning dialog header on deleting replication task
deleteHeader=Eliminar
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=A definição Eliminar tudo antes de carregar não é suportada para o Google BigQuery.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=A definição Eliminar tudo antes de carregar elimina e recria o objeto (tópico) antes de cada replicação. Isso também elimina todas as mensagens atribuídas.
#XTOL
DeleteAllBeforeLoadingLTFInfo=A definição Eliminar tudo antes de carregar não é suportada para este tipo de destino.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Nome técnico
#XCOL
connBusinessName=Nome comercial
#XCOL
connDescriptionName=Descrição
#XCOL
connType=Tipo
#XMSG
connTblNoDataFoundtxt=Nenhuma ligação encontrada
#XMSG
connectionError=Ocorreu um erro ao obter ligações.
#XMSG
connectionCombinationUnsupportedErrorTitle=A combinação de ligações não é suportada.
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=A replicação de {0} para {1} não é atualmente suportada.
#XMSG
invalidTargetforSourceHDLFErrorTitle=A combinação de tipos de ligação não é suportada.
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=A replicação de uma ligação com o tipo de ligação SAP HANA Cloud, ficheiros Data Lake para {0} não é suportada. Só pode replicar para o SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Selecionar
#XBUT
containerCancelBtn=Cancelar
#XTOL
containerSelectTooltip=Selecionar
#XTOL
containerCancelTooltip=Cancelar
#XMSG
containerContainerPathPlcHold=Caminho de contentor
#XFLD
containerContainertxt=Contentor
#XFLD
confluentContainerContainertxt=Contexto
#XMSG
infoMessageForSLTSelection=Só o ID de transferência em massa /SLT/ é permitido como contentor. Selecione um ID de transferência em massa em SLT (se disponível) e clique em Submeter.
#XMSG
msgFetchContainerFail=Ocorreu um erro ao obter dados do contentor.
#XMSG
infoMessageForSLTHidden=Esta ligação não suporta pastas SLT; por isso, elas não aparecem na lista abaixo.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Selecione um contentor que contenha subpastas.
#XMSG
sftpIncludeSubFolderText=Falso
#XMSG
sftpIncludeSubFolderTextNew=Não

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Ainda nenhum mapeamento de filtro)
#XMSG
failToFetchRemoteMetadata=Ocorreu um erro ao obter metadados.
#XMSG
failToFetchData=Ocorreu um erro ao obter o destino existente.
#XCOL
@loadType=Carregar tipo
#XCOL
@deleteAllBeforeLoading=Eliminar tudo antes de carregar

#XMSG
@loading=A carregar...
#XFLD
@selectSourceObjects=Selecionar objetos de origem
#XMSG
@exceedLimit=Não pode importar mais do que {0} objetos de cada vez. Desmarque pelo menos {1} objetos.
#XFLD
@objects=Objetos
#XBUT
@ok=OK
#XBUT
@cancel=Cancelar
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Seguinte
#XBUT
btnAddSelection=Adicionar seleção
#XTOL
@remoteFromSelection=Remover da seleção
#XMSG
@searchInForSearchField=Procurar em {0}

#XCOL
@name=Nome técnico
#XCOL
@type=Tipo
#XCOL
@location=Localização
#XCOL
@label=Nome comercial
#XCOL
@status=Estado

#XFLD
@searchIn=Procurar em:
#XBUT
@available=Disponível
#XBUT
@selection=Seleção

#XFLD
@noSourceSubFolder=Tabelas e vistas
#XMSG
@alreadyAdded=Já existe no diagrama
#XMSG
@askForFilter=Existem mais de {0} itens. Introduza uma cadeia de filtro para restringir o número de itens.
#XFLD: success label
lblSuccess=Sucesso
#XFLD: ready label
lblReady=Pronto
#XFLD: failure label
lblFailed=Falhado
#XFLD: fetching status label
lblFetchingDetail=A obter detalhes

#XMSG Place holder text for tree filter control
filterPlaceHolder=Digitar texto para filtrar objetos de nível superior
#XMSG Place holder text for server search control
serverSearchPlaceholder=Digitar e premir Enter para procurar
#XMSG
@deployObjects=Importar {0}objetos...
#XMSG
@deployObjectsStatus=Número de objetos que foram importados: {0}. Número de objetos que não puderam ser importados: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Falha ao abrir o browser de repositório local.
#XMSG
@openRemoteSourceBrowserError=Falha ao obter objetos de origem.
#XMSG
@openRemoteTargetBrowserError=Falha ao obter objetos de destino.
#XMSG
@validatingTargetsError=Ocorreu um erro ao validar destinos.
#XMSG
@waitingToImport=Pronto para importação

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=O número máximo de objetos foi excedido. Selecione um máximo de 500 objetos para um fluxo de replicação.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Nome técnico
#XFLD
sourceObjectBusinessName=Nome comercial
#XFLD
sourceNoColumns=Número de colunas
#XFLD
containerLbl=Contentor

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Tem de selecionar uma ligação de origem para o fluxo de replicação.
#XMSG
validationSourceContainerNonExist=Tem de selecionar um contentor para a ligação de origem.
#XMSG
validationTargetNonExist=Tem de selecionar uma ligação de destino para o fluxo de replicação.
#XMSG
validationTargetContainerNonExist=Tem de selecionar um contentor para a ligação de destino.
#XMSG
validationTruncateDisabledForObjectTitle=Replicação para armazenamentos de objetos.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=A replicação num armazenamento na Cloud só é possível se a opção Eliminar tudo antes de carregar estiver definida ou o objeto de destino não existir no destino.{0}{0} Para ainda permitir a replicação de objetos para os quais a opção Eliminar tudo antes de carregar não está definida, certifique-se de que o objeto de destino não existe no sistema antes de executar o fluxo de replicação.
#XMSG
validationTaskNonExist=Tem de ter pelo menos uma replicação no fluxo de replicação.
#XMSG
validationTaskTargetMissing=Tem de ter um destino para a replicação com a origem: {0}
#XMSG
validationTaskTargetIsSAC=O destino selecionado é um artefacto SAC: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=O destino selecionado não é uma tabela local suportada: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Já existe um objeto com este nome no destino. No entanto, este objeto não pode ser utilizado como um objeto de destino para um fluxo de replicação para o repositório local, uma vez que não é uma tabela local.
#XMSG
validateSourceTargetSystemDifference=Tem de selecionar diferentes combinações de ligação de origem e destino e contentor para o fluxo de replicação.
#XMSG
validateDuplicateSources=uma ou mais replicações têm nomes de objeto de origem duplicados: {0}.
#XMSG
validateDuplicateTargets=uma ou mais replicações têm nomes de objeto de destino duplicados: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=O objeto de origem {0} não suporta captura delta, enquanto o objeto de destino {1} sim. Deve remover a replicação.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Tem de selecionar o tipo de carregamento "Inicial e delta" para a replicação com o nome do objeto de destino {0}.
#XMSG
validationAutoRenameTarget=As colunas de destino foram renomeadas.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Foi adicionada uma projeção automática e as seguintes colunas de destino foram renomeadas para permitir a replicação no destino:{1}{1} {0} {1}{1}Isto deve-se a um dos seguintes motivos:{1}{1}{2} Carateres não suportados{1}{2} Prefixo reservado
#XMSG
validationAutoRenameTargetDescriptionUpdated=Foi adicionada uma projeção automática e as seguintes colunas de destino foram renomeadas para permitir a replicação para o Google BigQuery:{1}{1} {0} {1}{1}Isto é devido a um dos motivos seguintes:{1}{1}{2} Nome de coluna reservado{1}{2} Carateres não suportados{1}{2} Prefixo reservado
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Foi adicionada uma projeção automática e as seguintes colunas de destino foram renomeadas para permitir replicações para o Confluent:{1}{1} {0} {1}{1}Isto deve-se a um dos seguintes motivos:{1}{1}{2} Nome de coluna reservado{1}{2} Carateres não suportados{1}{2} Prefixo reservado
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Foi adicionada uma projeção automática e as seguintes colunas de destino foram renomeadas para permitir replicações para o destino:{1}{1} {0} {1}{1}Isto deve-se a um dos seguintes motivos:{1}{1}{2} Nome de coluna reservado{1}{2} Carateres não suportados{1}{2} Prefixo reservado
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=O objeto de destino foi renomeado.
#XMSG
autoRenameInfoDesc=O objeto de destino foi renomeado porque continha carateres não suportados. Apenas são suportados os seguintes carateres:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(ponto){0}{1}_(sublinhado){0}{1}-(travessão)
#XMSG
validationAutoTargetTypeConversion=Os tipos de dados de destino foram alterados.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Para as colunas de destino seguintes, os tipos de dados de destino foram alterados, porque no Google BigQuery os tipos de dados de origem não são suportados:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Para as colunas de destino seguintes, os tipos de dados de destino foram alterados, porque os tipos de dados de origem não são suportados na ligação de destino:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Encurtar nomes de coluna de destino.
#XMSG
validationMaxCharLengthGBQTargetDescription=No Google BigQuery, os nomes de coluna podem utilizar, no máximo, 300 carateres. Utilize uma projeção para encurtar os seguintes nomes de coluna de destino:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Não serão criadas chaves primárias.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=No Google BigQuery, é suportado um máximo de 16 chaves primárias, mas o objeto de origem tem um número maior de chaves primárias. Nenhuma das chaves primárias será criada no objeto de destino.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Uma ou mais colunas de origem têm tipos de dados que não podem ser definidos como chaves primárias no Google BigQuery. Nenhuma das chaves primárias será criada no objeto de destino.{0}{0}Os seguintes tipos de dados de destino são compatíveis com os tipos de dados do Google BigQuery para os quais é possível definir uma chave primária:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Defina uma ou mais colunas como chave primária.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Tem de definir uma ou mais colunas como chave primária, utilizando o diálogo do esquema de origem para o fazer.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Defina uma ou mais colunas como chave primária.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Deve definir uma ou mais colunas como chave primária que correspondam às restrições de chave primária para o objeto de origem. Para isso, vá para Configurar esquema nas propriedades de objeto de origem.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Introduzir um valor válido de partição máxima.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=O valor da partição máxima deve ser ≥ 1 e ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Defina uma ou mais colunas como chave primária.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Para replicar um objeto, tem de definir uma ou mais colunas de destino como chave primária. Utilize uma projeção para o fazer.
#XMSG
validateHDLFNoPKExistingDatasetError=Defina uma ou mais colunas como chave primária.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Para replicar dados para um objeto de destino existente, este deve ter uma ou mais colunas definidas como chave primária. {0} Tem as seguintes opções para definir uma ou mais colunas como chave primária: {0} {1} Utilize o editor de tabela local para alterar o objeto de destino existente. Em seguida, recarregue o fluxo de replicação.{0}{1} Renomeie o objeto de destino no fluxo de replicação. Isso criará um novo objeto assim que uma execução for iniciada. Depois de renomear, poderá definir uma ou mais colunas como chave primária numa projeção.{0}{1} Mapeie o objeto para outro objeto de destino existente no qual uma ou mais colunas já estão definidas como chave primária.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=O destino selecionado já existe no repositório: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Os nomes da tabela de captura delta já são utilizados por outras tabelas no repositório: {0} Tem de renomear esses objetos de destino para garantir os nomes da tabela de captura delta associados são exclusivos, antes de guardar o fluxo de replicação.
#XMSG
validateConfluentEmptySchema=Definir esquema
#XMSG
validateConfluentEmptySchemaDescUpdated=A tabela de origem não tem um esquema. Escolha Configurar esquema para definir um
#XMSG
validationCSVEncoding=Codificação CSV inválida
#XMSG
validationCSVEncodingDescription=A codificação CSV da tarefa não é válida.
#XMSG
validateConfluentEmptySchema=Selecionar um tipo de dados de destino compatível
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Selecionar um tipo de dados de destino compatível
#XMSG
globalValidateTargetDataTypeDesc=Ocorreu um erro com os mapeamentos de coluna. Vá para Projeção e certifique-se de que todas as colunas de origem são mapeadas com uma única coluna, com uma coluna que tem um tipo de dados compatível, e de que todas as expressões definidas são válidas.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Nomes de coluna duplicados.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Não são suportados nomes de coluna duplicados. Utilize o diálogo de projeção para corrigi-los. Os seguintes objetos de destino têm nomes de coluna duplicados: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Nomes de coluna duplicados.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Não são suportados nomes de coluna duplicados. Os seguintes objetos de destino têm nomes de coluna duplicados: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Podem existir inconsistências nos dados.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=O tipo de carregamento Só delta não irá considerar as alterações feitas na origem entre a última gravação e a próxima execução.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Altere o tipo de carregamento para "inicial".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Só é possível replicar objetos baseados em ABAP que não tenham uma chave primária para o tipo de carregamento "Só inicial".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Desativar a captura delta.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Para replicar um objeto que não tenha uma chave primária utilizando o tipo de ligação de tipo ABAP, é necessário desativar primeiro a captura delta para esta tabela.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Altere o objeto de destino.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=O objeto de destino não pode ser utilizado porque a captura delta está desativada. Pode renomear o objeto de destino e depois desativar a captura delta para o novo objeto (renomeado) ou mapear o objeto de origem para um objeto de destino para o qual a captura delta esteja desativada.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Altere o objeto de destino.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Não é possível utilizar o objeto de destino, porque não tem a coluna técnica __load_package_id necessária. Pode renomear o objeto de destino utilizando um nome que ainda não exista. Em seguida, o sistema cria um novo objeto que tenha a mesma definição que o objeto de origem e contenha a coluna técnica. Como alternativa, pode mapear o objeto de destino para um objeto existente que tenha a coluna técnica necessária (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Altere o objeto de destino.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Não é possível utilizar o objeto de destino, porque não tem a coluna técnica __load_record_id necessária. Pode renomear o objeto de destino utilizando um nome que ainda não exista. Em seguida, o sistema cria um novo objeto que tenha a mesma definição que o objeto de origem e contenha a coluna técnica. Como alternativa, pode mapear o objeto de destino para um objeto existente que tenha a coluna técnica necessária (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Altere o objeto de destino.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Não é possível utilizar o objeto de destino, porque não tem a coluna técnica __load_record_id necessária. Pode renomear o objeto de destino utilizando um nome que ainda não exista. Em seguida, o sistema cria um novo objeto que tenha a mesma definição que o objeto de origem e contenha a coluna técnica. Como alternativa, pode mapear o objeto de destino para um objeto existente que tenha a coluna técnica necessária  (__load_record_id).
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Altere o objeto de destino.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Não é possível utilizar o objeto de destino, porque tem uma chave primária enquanto o objeto de origem não tem nenhuma. Você pode renomear o objeto de destino utilizando um nome que ainda não existe. Em seguida, o sistema cria um novo objeto que tenha a mesma definição que o objeto de origem e, consequentemente, nenhuma chave primária. Como alternativa, pode mapear o objeto de destino para um objeto existente que tenha a coluna técnica necessária (__load_package_id) e não tenha uma chave primária.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Altere o objeto de destino.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Não é possível utilizar o objeto de destino, porque tem uma chave primária enquanto o objeto de origem não tem nenhuma. Você pode renomear o objeto de destino utilizando um nome que ainda não existe. Em seguida, o sistema cria um novo objeto que tenha a mesma definição que o objeto de origem e, consequentemente, nenhuma chave primária. Como alternativa, pode mapear o objeto de destino para um objeto existente que tenha a coluna técnica necessária (__load_record_id) e não tenha uma chave primária.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Altere o objeto de destino.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Não é possível utilizar o objeto de destino, porque o tipo de dados da sua coluna técnica __load_package_id não é "binário(>=256)". Pode renomear o objeto de destino utilizando um nome que ainda não exista. Em seguida, o sistema cria um novo objeto que tenha a mesma definição que o objeto de origem e, consequentemente, o tipo de dados correto. Como alternativa, pode mapear o objeto de destino para um objeto existente que tenha a coluna técnica necessária (__load_package_id) com o tipo de dados correto.
#XMSG
validationAutoRenameTargetDPID=As colunas de destino foram renomeadas.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Remover objeto de origem.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=O objeto de origem não tem uma coluna chave, que não é suportada neste contexto.
#XMSG
validationAutoRenameTargetDPIDDescription=Foi adicionada uma projeção automática e as seguintes colunas de destino foram renomeadas para permitir a replicação do ABAP sem as chaves: {1}{1} {0} {1}{1}Isto é devido a um dos motivos seguintes:{1}{1}{2} Nome de coluna reservado{1}{2} Carateres não suportados{1}{2} Prefixo reservado
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Replicação para {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Guardar e implementar fluxos de replicação que tenham o {0} como destino não é possível de momento porque estamos a executar a manutenção nesta função.
#XMSG
TargetColumnSkippedLTF=A coluna de destino foi ignorada.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=A coluna de destino foi ignorada devido a um tipo de dados não suportado. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Coluna de tempo como chave primária.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=O objeto de origem tem uma coluna de tempo como chave primária, que não é suportada neste contexto.
#XMSG
validateNoPKInLTFTarget=Chave primária em falta.
#XMSG
validateNoPKInLTFTargetDescription=A chave primária não está definida no destino, o que não é suportado neste contexto.
#XMSG
validateABAPClusterTableLTF=Tabela cluster ABAP.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=O objeto de origem é uma tabela cluster ABAP, que não é suportada neste contexto.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Parece que ainda não adicionou dados.
#YINS
welcomeText2=Para iniciar o seu fluxo de replicação, selecione uma ligação e um objeto de destino no lado esquerdo.

#XBUT
wizStep1=Selecionar ligação de origem
#XBUT
wizStep2=Selecionar contentor de origem
#XBUT
wizStep3=Adicionar objetos de origem

#XMSG
limitDataset=O número máximo de objetos foi atingido. Remova objetos existentes para adicionar novos ou crie um novo fluxo de replicação.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=O fluxo de replicação para esta ligação de destino não SAP não pode ser iniciado porque não está disponível volume de saída para este mês.
#XMSG
premiumOutBoundRFAdminWarningMsg=Um administrador pode aumentar os blocos de saída premium para este inquilino, tornando disponível volume de saída para este mês.
#XMSG
messageForToastForDPIDColumn2=Nova coluna adicionada para o destino para {0} objetos - necessário para tratar de registros duplicados em conexão com os objetos de origem baseados em ABAP que não têm uma chave primária.
#XMSG
PremiumInboundWarningMessage=Dependendo do número de fluxos de replicação e do volume de dados a replicar, os recursos do SAP HANA{0}necessários para a replicação de dados através de {1} poderá exceder a capacidade disponível para o seu inquilino.
#XMSG
PremiumInboundWarningMsg=Dependendo do número de fluxos de replicação e do volume de dados a replicar,{0}os recursos do SAP HANA necessários para a replicação de dados através de "{1}" poderão exceder a capacidade disponível para o seu inquilino.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Introduza um nome de projeção.
#XMSG
emptyTargetColumn=Introduza um nome de coluna de destino.
#XMSG
emptyTargetColumnBusinessName=Introduza um nome comercial de coluna de destino.
#XMSG
invalidTransformName=Introduza um nome de projeção.
#XMSG
uniqueColumnName=Renomeie a coluna de destino.
#XMSG
copySourceColumnLbl=Copiar colunas do objeto de origem
#XMSG
renameWarning=Certifique-se de que escolhe um nome exclusivo ao mudar o nome da tabela de destino. Se a tabela com o novo nome já existir no espaço, irá utilizar a definição dessa tabela.

#XMSG
uniqueColumnBusinessName=Renomeie o nome comercial da coluna de destino.
#XMSG
uniqueSourceMapping=Selecione uma coluna de origem diferente.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=A coluna de origem {0} já é utilizada pelas seguintes colunas de destino:{1}{1}{2}{1}{1} Para esta coluna de destino ou para as outras colunas de destino, selecione uma coluna de origem que ainda não esteja a ser utilizada para guardar a projeção.
#XMSG
uniqueColumnNameDescription=O nome da coluna de destino que introduziu já existe. Para poder guardar a projeção, tem de introduzir um nome de coluna exclusivo.
#XMSG
uniqueColumnBusinessNameDesc=O nome comercial da coluna de destino já existe. Para guardar a projeção, tem de introduzir um nome comercial de coluna exclusivo.
#XMSG
emptySource=Selecione uma coluna de origem ou insira uma constante.
#XMSG
emptySourceDescription=Para criar uma entrada de mapeamento válida, tem de selecionar uma coluna de origem ou inserir um valor constante.
#XMSG
emptyExpression=Defina o mapeamento.
#XMSG
emptyExpressionDescription1=Selecione a coluna de origem para a qual pretende mapear a coluna de destino ou marque a caixa de verificação na coluna {0} Funções/Constantes{1}. {2} {2} As funções são introduzidas automaticamente de acordo com o tipo de dados de destino. Os valores de constante podem ser introduzidos manualmente.
#XMSG
numberExpressionErr=Introduza um número.
#XMSG
numberExpressionErrDescription=Selecionou um tipo de dados numérico. Isso significa que só pode introduzir numerais, mais a vírgula decimal, se aplicável. Não utilize apas simples.
#XMSG
invalidLength=Introduza um valor de comprimento válido.
#XMSG
invalidLengthDescription=O comprimento do tipo de dados deve ser igual ou superior ao comprimento da coluna de origem e pode ser entre 1 e 5000.
#XMSG
invalidMappedLength=Introduza um valor de comprimento válido.
#XMSG
invalidMappedLengthDescription=O comprimento do tipo de dados deve ser igual ou superior ao comprimento da coluna de origem {0} e pode ser entre 1 e 5000.
#XMSG
invalidPrecision=Introduza um valor de precisão válido.
#XMSG
invalidPrecisionDescription=A precisão define o número total de dígitos. A escala define o número de dígitos após a vírgula decimal e pode ser entre 0 e a precisão.{0}{0} Exemplos: {0}{1} precisão 6, escala 2 corresponde a números como 1234,56.{0}{1} precisão 6, escala 6 corresponde a números como 0,123546.{0} {0} A precisão e a escala para o destino devem ser compatíveis com a precisão e a escala para a origem, de modo a que todos os dígitos da origem caibam no campo de destino. Por exemplo, se tiver precisão 6 e escala 2 na origem (e, consequentemente, dígitos diferentes de 0 antes da vírgula decimal), não pode ter precisão 6 e escala 6 no destino.
#XMSG
invalidPrimaryKey=Introduza pelo menos uma chave primária.
#XMSG
invalidPrimaryKeyDescription=Chave primária não definida para este esquema.
#XMSG
invalidMappedPrecision=Introduza um valor de precisão válido.
#XMSG
invalidMappedPrecisionDescription1=A precisão define o número total de dígitos. A escala define o número de dígitos após a vírgula decimal e pode ser entre 0 e a precisão.{0}{0} Exemplos:{0}{1}  precisão 6, escala 2 corresponde a números como 1234,56.{0}{1}  precisão 6, escala 6 corresponde a números como 0,123546.{0}{0}A precisão do tipo de dados deve ser igual ou superior à precisão da origem ({2}).
#XMSG
invalidScale=Introduza um valor de escala válido.
#XMSG
invalidScaleDescription=A precisão define o número total de dígitos. A escala define o número de dígitos após a vírgula decimal e pode ser entre 0 e a precisão.{0}{0} Exemplos: {0}{1} precisão 6, escala 2 corresponde a números como 1234,56.{0}{1} precisão 6, escala 6 corresponde a números como 0,123546.{0} {0} A precisão e a escala para o destino devem ser compatíveis com a precisão e a escala para a origem, de modo a que todos os dígitos da origem caibam no campo de destino. Por exemplo, se tiver precisão 6 e escala 2 na origem (e, consequentemente, dígitos diferentes de 0 antes da vírgula decimal), não pode ter precisão 6 e escala 6 no destino.
#XMSG
invalidMappedScale=Introduza um valor de escala válido.
#XMSG
invalidMappedScaleDescription1=A precisão define o número total de dígitos. A escala define o número de dígitos após a vírgula decimal e pode ser entre 0 e a precisão.{0}{0} Exemplos:{0}{1}  precisão 6, escala 2 corresponde a números como 1234,56.{0}{1}  precisão 6, escala 6 corresponde a números como 0,123546.{0}{0}A escala do tipo de dados deve ser igual ou superior à escala da origem ({2}).
#XMSG
nonCompatibleDataType=Selecione um tipo de dados de destino compatível.
#XMSG
nonCompatibleDataTypeDescription1=O tipo de dados que especificar aqui deve ser compatível com o tipo de dados da origem ({0}). {1}{1} Exemplo: se a sua coluna de origem tiver o tipo de dados Cadeia e contiver letras, não pode utilizar um tipo de dados Decimal para o destino.
#XMSG
invalidColumnCount=Selecione uma coluna de origem.
#XMSG
ObjectStoreInvalidScaleORPrecision=Introduza um valor válido para precisão e escala.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=O primeiro valor é a precisão que define o número total de dígitos. O segundo valor é a escala que define os dígitos a seguir à vírgula decimal. Introduza um valor da escala de destino que seja superior ao valor da escala de origem e certifique-se de que a diferença entre o valor da escala de destino introduzido e o valor da precisão é superior à diferença entre o valor da escala de origem e o valor da precisão.
#XMSG
InvalidPrecisionORScale=Introduza um valor válido para precisão e escala.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=O primeiro valor é a precisão, que define o número total de dígitos. O segundo valor é a escala, que define os dígitos depois da vírgula decimal.{0}{0}Uma vez que o tipo de dados de origem não é suportado no Google BigQuery, é convertido para o tipo de dados de destino DECIMAL. Neste caso, a precisão só pode ser definida entre 38 e 76 e a escala entre 9 e 38. Além disso, o resultado da precisão menos a escala, que representa os dígitos antes da vírgula decimal, deve estar entre 29 e 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=O primeiro valor é a precisão, que define o número total de dígitos. O segundo valor é a escala, que define os dígitos depois da vírgula decimal.{0}{0}Uma vez que o tipo de dados de origem não é suportado no Google BigQuery, é convertido para o tipo de dados de destino DECIMAL. Neste caso, a precisão tem de ser definida como 20 ou maior. Além disso, o resultado da precisão menos a escala, que reflete os dígitos antes da vírgula decimal, deve ser 20 ou maior.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=O primeiro valor é a precisão, que define o número total de dígitos. O segundo valor é a escala, que define os dígitos depois da vírgula decimal.{0}{0}Uma vez que o tipo de dados de origem não é suportado no destino, é convertido para o tipo de dados de destino DECIMAL. Neste caso, a precisão tem de ser definida como um número superior ou igual a 1 e inferior ou igual a 38, e a escala inferior ou igual à precisão.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=O primeiro valor é a precisão, que define o número total de dígitos. O segundo valor é a escala, que define os dígitos depois da vírgula decimal.{0}{0}Uma vez que o tipo de dados de origem não é suportado no destino, é convertido para o tipo de dados de destino DECIMAL. Neste caso, a precisão tem de ser definida como 20 ou maior. Além disso, o resultado da precisão menos a escala, que reflete os dígitos antes da vírgula decimal, deve ser 20 ou maior.
#XMSG
invalidColumnCountDescription=Para criar uma entrada de mapeamento válida, tem de selecionar uma coluna de origem ou inserir um valor constante.
#XMSG
duplicateColumns=Renomeie a coluna de destino.
#XMSG
duplicateGBQCDCColumnsDesc=O nome da coluna de destino está reservado no Google BigQuery. Tem de o renomear para poder guardar a projeção.
#XMSG
duplicateConfluentCDCColumnsDesc=O nome da coluna de destino está reservado no Confluent. Tem de o renomear para poder guardar a projeção.
#XMSG
duplicateSignavioCDCColumnsDesc=O nome da coluna de destino está reservado no SAP Signavio. Tem de o renomear para poder guardar a projeção.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=O nome da coluna de destino está reservado no MS OneLake. Tem de o renomear para poder guardar a projeção.
#XMSG
duplicateSFTPCDCColumnsDesc=O nome da coluna de destino está reservado no SFTP. Tem de o renomear para poder guardar a projeção.
#XMSG
GBQTargetNameWithPrefixUpdated1=O nome da coluna de destino contém um prefixo que está reservado no Google BigQuery. Tem de o renomear para poder guardar a projeção. {0}{0}O nome da coluna de destino não pode começar com qualquer uma das seguintes cadeias:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Encurte o nome de coluna de destino.
#XMSG
GBQtargetMaxLengthDesc=No Google BigQuery, um nome de coluna pode utilizar, no máximo, 300 carateres. Encurte o nome de coluna de destino para poder guardar a projeção.
#XMSG
invalidMappedScalePrecision=A precisão e a escala para o destino devem ser compatíveis com a precisão e a escala para a origem de modo a que todos os dígitos da origem caibam no campo de destino.
#XMSG
invalidMappedScalePrecisionShortText=Introduza um valor de precisão e de escala válido.
#XMSG
validationIncompatiblePKTypeDescProjection3=Uma ou mais colunas de origem têm tipos de dados que não podem ser definidos como chaves primárias no Google BigQuery. Nenhuma das chaves primárias será criada no objeto de destino.{0}{0}Os seguintes tipos de dados de destino são compatíveis com os tipos de dados do Google BigQuery para os quais é possível definir uma chave primária:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Desmarcar column __message_id.
#XMSG
uncheckColumnMessageIdDesc=Coluna: chave primária
#XMSG
validationOpCodeInsert=Tem de introduzir um valor para inserção.
#XMSG
recommendDifferentPrimaryKey=Recomendamos que selecione uma chave primária diferente ao nível do item.
#XMSG
recommendDifferentPrimaryKeyDesc=Se o código da operação já estiver definido, recomendamos a seleção de chaves primárias diferentes para o índice da matriz e os itens, para evitar problemas como a duplicação da coluna, por exemplo.
#XMSG
selectPrimaryKeyItemLevel=Deve selecionar pelo menos uma chave primária para o nível do cabeçalho e do item.
#XMSG
selectPrimaryKeyItemLevelDesc=Quando uma matriz ou um mapa é expandido, deve selecionar duas chaves primárias, uma ao nível do cabeçalho e outra ao nível do item.
#XMSG
invalidMapKey=Deve selecionar pelo menos uma chave primária ao nível do cabeçalho.
#XMSG
invalidMapKeyDesc=Quando uma matriz ou um mapa é expandido, deve selecionar uma chave primária ao nível do cabeçalho.
#XFLD
txtSearchFields=Procurar colunas de destino
#XFLD
txtName=Nome
#XMSG
txtSourceColValidation=Uma ou mais colunas de origem não são suportadas:
#XMSG
txtMappingCount=Mapeamentos ({0})
#XMSG
schema=Esquema
#XMSG
sourceColumn=Colunas de origem
#XMSG
warningSourceSchema=Qualquer alteração efetuada no esquema afetará os mapeamentos no diálogo de projeção.
#XCOL
txtTargetColName=Coluna de destino (nome técnico)
#XCOL
txtDataType=Tipo de dados de destino
#XCOL
txtSourceDataType=Tipo de dados de origem
#XCOL
srcColName=Coluna de origem (nome técnico)
#XCOL
precision=Precisão
#XCOL
scale=Escala
#XCOL
functionsOrConstants=Funções/constantes
#XCOL
txtTargetColBusinessName=Coluna de destino (nome comercial)
#XCOL
prKey=Chave primária
#XCOL
txtProperties=Propriedades
#XBUT
txtOK=Guardar
#XBUT
txtCancel=Cancelar
#XBUT
txtRemove=Remover
#XFLD
txtDesc=Descrição
#XMSG
rftdMapping=Mapeamento
#XFLD
@lblColumnDataType=Tipo de dados
#XFLD
@lblColumnTechnicalName=Nome técnico
#XBUT
txtAutomap=Mapear automaticamente
#XBUT
txtUp=Para cima
#XBUT
txtDown=Para baixo

#XTOL
txtTransformationHeader=Projeção
#XTOL
editTransformation=Editar
#XTOL
primaryKeyToolip=Chave


#XMSG
rftdFilter=Filtro
#XMSG
rftdFilterColumnCount=Origem: {0}({1})
#XTOL
rftdFilterColSearch=Procurar
#XMSG
rftdFilterColNoData=Nenhuma coluna para visualizar
#XMSG
rftdFilteredColNoExps=Nenhuma expressão de filtro
#XMSG
rftdFilterSelectedColTxt=Adicionar filtro para
#XMSG
rftdFilterTxt=Filtro disponível para
#XBUT
rftdFilterSelectedAddColExp=Adicionar expressão
#YINS
rftdFilterNoSelectedCol=Selecionar uma coluna para adicionar o filtro.
#XMSG
rftdFilterExp=Expressão de filtro
#XMSG
rftdFilterNotAllowedColumn=A adição de filtros não é suportada para esta coluna.
#XMSG
rftdFilterNotAllowedHead=Coluna não suportada
#XMSG
rftdFilterNoExp=Não foram definidos filtros
#XTOL
rftdfilteredTt=Filtrado
#XTOL
rftdremoveexpTt=Remover expressão de filtro
#XTOL
validationMessageTt=Mensagens de validação
#XTOL
rftdFilterDateInp=Selecionar uma data
#XTOL
rftdFilterDateTimeInp=Selecionar uma data/hora
#XTOL
rftdFilterTimeInp=Selecione uma hora
#XTOL
rftdFilterInp=Introduzir um valor
#XMSG
rftdFilterValidateEmptyMsg={0} expressões de filtro na coluna {1} estão vazias
#XMSG
rftdFilterValidateInvalidNumericMsg={0} expressões de filtro na coluna {1} contêm valores numéricos inválidos
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=A expressão de filtro tem de conter valores numéricos válidos
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Se o esquema do objeto de destino tiver sido alterado, utilize a função "Mapear para objeto de destino existente" na página principal para adaptar as alterações e volte a mapear o objeto de destino com a sua origem.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Se existir a tabela de destino e o mapeamento incluir uma alteração de esquema, deve alterar a tabela de destino em conformidade, antes de implementar o fluxo de replicação.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Se o seu mapeamento envolver uma alteração de esquema, deve alterar a tabela de destino em conformidade, antes de implementar o fluxo de replicação.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=As seguintes colunas não suportadas foram ignoradas na definição de origem: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=As seguintes colunas não suportadas foram ignoradas na definição de destino: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Os seguintes objetos não são suportados porque estão expostos para consumo: {0} {1} {0} {0} Para utilizar tabelas num fluxo de replicação, a utilização semântica (nas definições da tabela) não deve estar definida como {2}Conjunto de dados de análise{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=O objeto de destino não pode ser utilizado porque está exposto para consumo. {0} {0} Para utilizar a tabela num fluxo de replicação, a utilização semântica (nas definições da tabela) não deve estar definida como {1}Conjunto de dados de análise{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Já existe um objeto de destino com este nome. No entanto, ele não pode ser utilizado porque está exposto para consumo. {0} {0} Para utilizar a tabela num fluxo de replicação, a utilização semântica (nas definições da tabela) não deve estar definida como {1}Conjunto de dados de análise{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Já existe um objeto com este nome no destino. {0}No entanto, este objeto não pode ser utilizado como um objeto de destino para um fluxo de replicação para o repositório local, uma vez que não é uma tabela local.
#XMSG:
targetAutoRenameUpdated=A coluna de destino foi renomeada.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=A coluna de destino foi renomeada para permitir replicações no Google BigQuery. Isso deve-se a um dos seguintes motivos: {0} {1}{2}Nome de coluna reservado{3}{2}Carateres não suportados{3}{2}Prefixo reservado{3}{4}.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=A coluna de destino foi renomeada para permitir replicações no Confluent. Isso deve-se a um dos seguintes motivos: {0} {1}{2}Nome de coluna reservado{3}{2}Carateres não suportados{3}{2}Prefixo reservado{3}{4}.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=A coluna de destino foi renomeada para permitir replicações no destino. Isso deve-se a um dos seguintes motivos: {0} {1}{2}Carateres não suportados{3}{2}Prefixo reservado{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=A coluna de destino foi renomeada para permitir replicações no destino. Isso deve-se a um dos seguintes motivos: {0} {1}{2}Nome de coluna reservado{3}{2}Carateres não suportados{3}{2}Prefixo reservado{3}{4}
#XMSG:
targetAutoDataType=O tipo de dados de destino foi alterado.
#XMSG:
targetAutoDataTypeDesc=O tipo de dados de destino foi alterado para {0} porque o tipo de dados de origem não é suportado no Google BigQuery.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=O tipo de dados de destino foi alterado para {0} porque o tipo de dados de origem não é suportado na ligação de destino.
#XMSG
projectionGBQUnableToCreateKey=Não serão criadas chaves primárias.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=No Google BigQuery, é suportado um máximo de 16 chaves primárias, mas o objeto de origem tem um número maior de chaves primárias. Nenhuma das chaves primárias será criada no objeto de destino.
#XMSG
HDLFNoKeyError=Defina uma ou mais colunas como chave primária.
#XMSG
HDLFNoKeyErrorDescription=Para replicar um objeto, tem de definir uma ou mais colunas como chave primária.
#XMSG
HDLFNoKeyErrorExistingTarget=Defina uma ou mais colunas como chave primária.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Para replicar dados para um objeto de destino existente, este deve ter uma ou mais colunas definidas como chave primária. {0} {0} Tem as seguintes opções para definir uma ou mais colunas como chave primária: {0}{0}{1} Utilize o editor de tabela local para alterar o objeto de destino existente. Em seguida, recarregue o fluxo de replicação. {0}{0}{1} Renomeie o objeto de destino no fluxo de replicação. Isso criará um novo objeto assim que uma execução for iniciada. Depois de renomear, poderá definir uma ou mais colunas como chave primária numa projeção. {0}{0}{1} Mapeie o objeto para outro objeto de destino existente no qual uma ou mais colunas já estão definidas como chave primária.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Chave primária alterada.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=Em comparação com o objeto de origem, definiu colunas diferentes como chave primária para o objeto de destino. Certifique-se de que essas colunas identificam exclusivamente todas as linhas para evitar uma eventual corrupção de dados ao replicar os dados mais tarde. {0} {0}  No objeto de origem, as seguintes colunas estão definidas como chave primária: {0} {1}
#XMSG
duplicateDPIDColumns=Renomeie a coluna de destino.
#XMSG
duplicateDPIDDColumnsDesc1=Este nome de coluna de destino está reservado para uma coluna técnica. Introduza um nome diferente para guardar a projeção.
#XMSG:
targetAutoRenameDPID=A coluna de destino foi renomeada.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=A coluna de destino foi renomeada para permitir replicações da fonte ABAP sem chaves. Isto deve-se a um dos seguintes motivos: {0} {1}{2}Nome de coluna reservado{3}{2}Carateres não suportados{3}{2}Prefixo reservado{3}{4}.
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle={0} Definições de destino
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle={0} Definições de origem
#XBUT
connectionSettingSave=Guardar
#XBUT
connectionSettingCancel=Cancelar
#XBUT: Button to keep the object level settings
txtKeep=Manter
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Substituir
#XFLD
targetConnectionThreadlimit=Limite de threads de destino para carregamento inicial (1-100)
#XFLD
connectionThreadLimit=Limite de threads de origem para carregamento inicial (1-100)
#XFLD
maxConnection=Limite de threads de replicação (1-100)
#XFLD
kafkaNumberOfPartitions=Número de partições
#XFLD
kafkaReplicationFactor=Fator de replicação
#XFLD
kafkaMessageEncoder=Codificador de mensagens
#XFLD
kafkaMessageCompression=Compressão de mensagem
#XFLD
fileGroupDeltaFilesBy=Agrupar delta por
#XFLD
fileFormat=Tipo de ficheiro
#XFLD
csvEncoding=Codificação CSV
#XFLD
abapExitLbl=Saída do ABAP
#XFLD
deltaPartition=Número de threads de objeto para carregamentos delta (1-10)
#XFLD
clamping_Data=Falha na truncagem de dados
#XFLD
fail_On_Incompatible=Falha nos dados incompatíveis
#XFLD
maxPartitionInput=Número máximo de partições
#XFLD
max_Partition=Definir número máximo de partições
#XFLD
include_SubFolder=Incluir subpastas
#XFLD
fileGlobalPattern=Padrão global para nome do ficheiro
#XFLD
fileCompression=Compressão de ficheiro
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Delimitador de ficheiro
#XFLD
fileIsHeaderIncluded=Cabeçalho do ficheiro
#XFLD
fileOrient=Oriente
#XFLD
gbqWriteMode=Modo de escrita
#XFLD
suppressDuplicate=Suprimir duplicados
#XFLD
apacheSpark=Ativar a compatibilidade Apache Spark
#XFLD
clampingDatatypeCb=Fixar tipos de dados de vírgula decimal flutuante
#XFLD
overwriteDatasetSetting=Substituir definições de destino ao nível do objeto
#XFLD
overwriteSourceDatasetSetting=Substituir definições de origem ao nível do objeto
#XMSG
kafkaInvalidConnectionSetting=Introduzir número entre {0} e {1}.
#XMSG
MinReplicationThreadErrorMsg=Introduza um número maior que {0}.
#XMSG
MaxReplicationThreadErrorMsg=Introduza um número menor que {0}.
#XMSG
DeltaThreadErrorMsg=Introduza um valor entre 1 e 10.
#XMSG
MaxPartitionErrorMsg=Introduza um valor entre 1 <= x <= 2147483647. O valor predefinido é 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Introduza um número inteiro entre {0} e {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Utilizar fator de replicação do agente
#XFLD
serializationFormat=Formato de serialização
#XFLD
compressionType=Tipo de compressão
#XFLD
schemaRegistry=Utilizar registro de esquema
#XFLD
subjectNameStrat=Estratégia do nome do assunto
#XFLD
compatibilityType=Tipo de compatibilidade
#XFLD
confluentTopicName=Nome do tópico
#XFLD
confluentRecordName=Nome do registo
#XFLD
confluentSubjectNamePreview=Pré-visualização do nome do assunto
#XMSG
serializationChangeToastMsgUpdated2=O formato de serialização foi alterado para JSON pois o registo de esquema não está ativado. Para alterar o formato de serialização de volta para AVRO, deve ativar o registo de esquema primeiro.
#XBUT
confluentTopicNameInfo=O nome do tópico é sempre baseado no nome do objeto de destino. Pode alterá-lo renomeando o objeto de destino.
#XMSG
emptyRecordNameValidationHeaderMsg=Introduza um nome de registo.
#XMSG
emptyPartionHeader=Introduza o número de partições.
#XMSG
invalidPartitionsHeader=Introduza um número válido de partições.
#XMSG
invalidpartitionsDesc=Introduza um número entre 1 e 200.000.
#XMSG
emptyrFactorHeader=Introduza um fator de replicação.
#XMSG
invalidrFactorHeader=Introduza um fator de replicação válido.
#XMSG
invalidrFactorDesc=Introduza um número entre 1 e 32.767.
#XMSG
emptyRecordNameValidationDescMsg=Se o formato de serialização "AVRO" for utilizado, só serão suportados os seguintes carateres:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(sublinhado)
#XMSG
validRecordNameValidationHeaderMsg=Introduza um nome de registo válido.
#XMSG
validRecordNameValidationDescMsgUpdated=Uma vez que é utilizado o formato de serialização "AVRO", o nome do registo deve ser constituído apenas por carateres alfanuméricos (A-Z, a-z, 0-9) e sublinhado (_). Deve começar com uma letra ou um sublinhado.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=O “Número de threads de objeto para carregamentos delta” pode ser definido assim que um ou mais objetos tiverem o tipo de carregamento "inicial e delta".
#XMSG
invalidTargetName=Nome de coluna inválido
#XMSG
invalidTargetNameDesc=O nome da coluna de destino deve ser composto apenas por carateres alfanuméricos (A-Z, a-z, 0-9) e sublinhado (_).
#XFLD
consumeOtherSchema=Consumir outras versões do esquema
#XFLD
ignoreSchemamissmatch=Ignorar não correspondência de esquema
#XFLD
confleuntDatatruncation=Falha na truncagem de dados
#XFLD
isolationLevel=Nível de isolamento
#XFLD
confluentOffset=Ponto de início
#XFLD
signavioGroupDeltaFilesByText=Nenhum
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=Não
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=Não

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Projeções
#XBUT
txtAdd=Adicionar
#XBUT
txtEdit=Editar
#XMSG
transformationText=Adicione uma projeção para configurar o filtro ou o mapeamento.
#XMSG
primaryKeyRequiredText=Selecione uma chave primária com Configurar esquema.
#XFLD
lblSettings=Definições
#XFLD
lblTargetSetting={0}: definições de destino
#XMSG
@csvRF=Selecione o ficheiro que contém a definição do esquema que quer aplicar a todos os ficheiros na pasta.
#XFLD
lblSourceColumns=Colunas de origem
#XFLD
lblJsonStructure=Estrutura JSON
#XFLD
lblSourceSetting={0}: definições de origem
#XFLD
lblSourceSchemaSetting={0}: definições do esquema de origem
#XBUT
messageSettings=Definições de mensagem
#XFLD
lblPropertyTitle1=Propriedades de objeto
#XFLD
lblRFPropertyTitle=Propriedades do fluxo de replicação
#XMSG
noDataTxt=Não existem colunas para visualizar.
#XMSG
noTargetObjectText=Não existe objeto de destino selecionado.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Colunas de destino
#XMSG
searchColumns=Procurar colunas
#XTOL
cdcColumnTooltip=Coluna para captura delta
#XMSG
sourceNonDeltaSupportErrorUpdated=O objeto de origem não suporta a captura delta.
#XMSG
targetCDCColumnAdded=Foram adicionadas 2 colunas de destino para a captura delta.
#XMSG
deltaPartitionEnable=Limite de threads de objeto para carregamentos delta adicionado às definições de origem.
#XMSG
attributeMappingRemovalTxt=A remover mapeamentos inválidos que não são suportados para o novo objeto de destino.
#XMSG
targetCDCColumnRemoved=Foram removidas 2 colunas de destino utilizadas para a captura delta.
#XMSG
replicationLoadTypeChanged=Tipo de carregamento alterado para "Inicial e delta".
#XMSG
sourceHDLFLoadTypeError=Altere o tipo de carregamento para "inicial e delta".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Para replicar um objeto de uma ligação de origem com o tipo de ligação SAP HANA Cloud, ficheiros Data Lake para SAP Datasphere, tem de utilizar o tipo de carregamento "inicial e delta".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Ative a captura delta.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Para replicar um objeto de uma ligação de origem com o tipo de ligação SAP HANA Cloud, ficheiros Data Lake para SAP Datasphere, tem de ativar a captura delta.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Altere o objeto de destino.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=O objeto de destino não pode ser utilizado porque a captura delta está desativada. Pode renomear o objeto de destino (o que permite a criação de um novo objeto com captura delta) ou mapeá-lo para um objeto existente com captura delta ativada.
#XMSG
deltaPartitionError=Introduza um número de threads de objeto válido para carregamentos delta.
#XMSG
deltaPartitionErrorDescription=Introduza um valor entre 1 e 10.
#XMSG
deltaPartitionEmptyError=Introduza um número de threads de objeto para carregamentos delta.
#XFLD
@lblColumnDescription=Descrição
#XMSG
@lblColumnDescriptionText1=Para fins técnicos - processamento de registos duplicados causados por problemas durante a replicação de objetos de origem baseados em ABAP que não têm uma chave primária.
#XFLD
storageType=Armazenamento
#XFLD
skipUnmappedColLbl=Ignorar colunas não mapeadas
#XFLD
abapContentTypeLbl=Tipo de conteúdo
#XFLD
autoMergeForTargetLbl=Unir dados automaticamente
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Geral
#XFLD
lblBusinessName=Nome comercial
#XFLD
lblTechnicalName=Nome técnico
#XFLD
lblPackage=Pacote
#XFLD
statusPanel=Estado de execução
#XBTN: Schedule dropdown menu
SCHEDULE=Agendar
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Editar agenda
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Eliminar agenda
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Criar agenda
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Verificação de validação de agenda falhada
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Não é possível criar um agendamento porque o fluxo de replicação está a ser implementado. {0}.Aguarde que o fluxo de replicação seja implementado.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Não é possível criar um agendamento para fluxos de replicação que contêm objetos com o tipo de carregamento "Inicial e delta".
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Não é possível criar um agendamento para fluxos de replicação que contêm objetos com o tipo de carregamento "Inicial e delta/Só delta".
#XFLD : Scheduled popover
SCHEDULED=Agendado
#XFLD
CREATE_REPLICATION_TEXT=Criar um fluxo de replicação
#XFLD
EDIT_REPLICATION_TEXT=Editar um fluxo de replicação
#XFLD
DELETE_REPLICATION_TEXT=Eliminar um fluxo de replicação
#XFLD
REFRESH_FREQUENCY=Frequência
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=O fluxo de replicação não pode ser implementado porque a agenda existente{0} ainda não suporta o tipo de carregamento "Inicial e delta".{0}{0}Para implementar o fluxo de replicação, tem de definir os tipos de carregamento de todos os objetos{0} como "Apenas inicial". Em alternativa, pode eliminar a agenda, implementar o fluxo de replicação{0} e iniciar uma nova execução. Isso resulta numa execução sem {0}fim que também suporta objetos com o tipo de carregamento "Inicial e delta".
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=O fluxo de replicação não pode ser implementado porque a agenda existente{0} ainda não suporta o tipo de carregamento "Inicial e delta/Só delta".{0}{0}Para implementar o fluxo de replicação, tem de definir os tipos de carregamento de todos os objetos{0} como "Apenas inicial". Em alternativa, pode eliminar a agenda, implementar o fluxo de replicação{0} e iniciar uma nova execução. Isso resulta numa execução sem {0}fim que também suporta objetos com o tipo de carregamento "Inicial e delta/Só delta".
#XMSG
SCHEDULE_EXCEPTION=Falha ao obter os detalhes da agenda
#XFLD: Label for frequency column
everyLabel=A cada
#XFLD: Plural Recurrence text for Hour
hoursLabel=Horas
#XFLD: Plural Recurrence text for Day
daysLabel=Dias
#XFLD: Plural Recurrence text for Month
monthsLabel=Meses
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutos
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Falha ao obter informações sobre a possibilidade de agendamento.
#XFLD :Paused field
PAUSED=Interrompido
#XMSG
navToMonitoring=Abrir no monitor de fluxos
#XFLD
statusLbl=Estado
#XFLD
lblLastRunExecuted=Início da última execução
#XFLD
lblLastExecuted=Última execução
#XFLD: Status text for Completed
statusCompleted=Concluído
#XFLD: Status text for Running
statusRunning=Em execução
#XFLD: Status text for Failed
statusFailed=Falhado
#XFLD: Status text for Stopped
statusStopped=Parado
#XFLD: Status text for Stopping
statusStopping=A parar
#XFLD: Status text for Active
statusActive=Ativo
#XFLD: Status text for Paused
statusPaused=Interrompido
#XFLD: Status text for not executed
lblNotExecuted=Ainda não executado
#XFLD
messagesSettings=Definições de mensagem
#XTOL
@validateModel=Mensagens de validação
#XTOL
@hierarchy=Hierarquia
#XTOL
@columnCount=Número de colunas
#XMSG
VAL_PACKAGE_CHANGED=Atribuiu este objeto ao pacote "{1}". Clique em Guardar para confirmar e validar esta alteração. Considere que a atribuição a um pacote não pode ser anulada neste editor depois de guardar.
#XMSG
MISSING_DEPENDENCY=As dependências do objeto "{0}" não podem ser resolvidas no pacote {1}.
#XFLD
deltaLoadInterval=Intervalo de carregamento delta
#XFLD
lblHour=Horas (0-24)
#XFLD
lblMinutes=Minutos (0-59)
#XMSG
maxHourOrMinErr=Introduza um valor entre 0 e {0}
#XMSG
maxDeltaInterval=O valor máximo do intervalo de carregamento delta é 24 horas.{0}Altere o valor do minuto ou o valor da hora correspondentemente.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Caminho do contentor de destino
#XFLD
confluentSubjectName=Nome do assunto
#XFLD
confluentSchemaVersion=Versão do esquema
#XFLD
confluentIncludeTechKeyUpdated=Incluir chave técnica
#XFLD
confluentOmitNonExpandedArrays=Omitir matrizes não expandidas
#XFLD
confluentExpandArrayOrMap=Expandir matriz ou mapa
#XCOL
confluentOperationMapping=Mapeamento de operação
#XCOL
confluentOpCode=Código de operação
#XFLD
confluentInsertOpCode=Inserir
#XFLD
confluentUpdateOpCode=Atualizar
#XFLD
confluentDeleteOpCode=Eliminar
#XFLD
expandArrayOrMapNotSelectedTxt=Não selecionado
#XFLD
confluentSwitchTxtYes=Sim
#XFLD
confluentSwitchTxtNo=Não
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Erro
#XTIT
executeWarning=Aviso
#XMSG
executeunsavederror=Guarde o seu fluxo de replicação antes de executá-lo.
#XMSG
executemodifiederror=Existem alterações não guardadas no fluxo de replicação. Guarde o fluxo de replicação.
#XMSG
executeundeployederror=Tem de implementar o seu fluxo de replicação para poder executá-lo.
#XMSG
executedeployingerror=Aguarde a conclusão da implementação.
#XMSG
msgRunStarted=Execução iniciada
#XMSG
msgExecuteFail=Falha ao executar o fluxo de replicação
#XMSG
titleExecuteBusy=Aguarde.
#XMSG
msgExecuteBusy=Estamos a preparar os seus dados para executar o fluxo de replicação.
#XTIT
executeConfirmDialog=Aviso
#XMSG
msgExecuteWithValidations=O fluxo de replicação tem erros de validação. A execução do fluxo de replicação poderá resultar numa falha.
#XMSG
msgRunDeployedVersion=Existem alterações para implementar. A última versão implementada do fluxo de replicação será executada. Pretende continuar?
#XBUT
btnExecuteAnyway=Executar na mesma
#XBUT
btnExecuteClose=Fechar
#XBUT
loaderClose=Fechar
#XTIT
loaderTitle=Carregamento
#XMSG
loaderText=A obter detalhes do servidor
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=O fluxo de replicação para esta ligação de destino não SAP não pode ser iniciado
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=porque não está disponível volume de saída para este mês.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Um administrador pode aumentar os blocos de saída premium para este
#XMSG
premiumOutBoundRFAdminErrMsgPart2=inquilino, tornando disponível volume de saída para este mês.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Erro
#XTIT
deployInfo=Informação
#XMSG
deployCheckFailException=Ocorreu uma exceção durante a implementação
#XMSG
deployGBQFFDisabled=De momento, não é possível implementar fluxos de replicação com uma ligação de destino no Google BigQuery porque estamos a executar a manutenção nesta função.
#XMSG
deployKAFKAFFDisabled=De momento, não é possível implementar fluxos de replicação com uma ligação de destino no Apache Kafka porque estamos a executar a manutenção nesta função.
#XMSG
deployConfluentDisabled=De momento, não é possível implementar fluxos de replicação com uma ligação de destino para o Confluent Kafka porque estamos a efetuar a manutenção nesta função.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Para os seguintes objetos de destino, os nomes da tabela de captura delta já são utilizados por outras tabelas no repositório: {0} Tem de renomear esses objetos de destino para garantir os nomes da tabela de captura delta associados são exclusivos, antes de implementar o fluxo de replicação.
#XMSG
deployDWCSourceFFDisabled=A implementação de fluxos de replicação que tenham o SAP Datasphere como origem não é possível de momento porque estamos a executar a manutenção nesta função.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=A implementação de fluxos de replicação que contenham tabelas locais compatíveis com delta como objetos de origem não é possível de momento porque estamos a executar a manutenção nesta função.
#XMSG
deployHDLFSourceFFDisabled=A implementação de fluxos de replicação que tenham ligações de origem com o tipo de ligação SAP HANA Cloud,  ficheiros Data Lake, não é possível de momento porque estamos a executar a manutenção.
#XMSG
deployObjectStoreAsSourceFFDisabled=A implementação de fluxos de replicação que tenham um fornecedor de armazenamento na Cloud como origem não é possível de momento.
#XMSG
deployConfluentSourceFFDisabled=A implementação de fluxos de replicação que tenham o Confluent Kafka como origem não é possível de momento porque estamos a executar a manutenção nesta função.
#XMSG
deployMaxDWCNewTableCrossed=Para grandes fluxos de replicação, não é possível "guardar e implementar" de uma só vez. Guarde primeiro o fluxo de replicação e depois implemente-o.
#XMSG
deployInProgressInfo=A implementação já está em curso.
#XMSG
deploySourceObjectInUse=Os objetos de origem {0} já estão a ser utilizados nos fluxos de replicação {1}.
#XMSG
deployTargetSourceObjectInUse=Os objetos de origem {0} já estão a ser utilizados nos fluxos de replicação {1}. Os objetos de destino {2} já estão a ser utilizados nos fluxos de replicação {3}.
#XMSG
deployReplicationFlowCheckError=Erro ao verificar fluxo de replicação: {0}
#XMSG
preDeployTargetObjectInUse=Os objetos de destino {0} já estão a ser utilizados em fluxos de replicação {1} e não pode ter o mesmo objeto de destino em dois fluxos de replicação diferentes. Selecione outro objeto de destino e tente novamente.
#XMSG
runInProgressInfo=O fluxo de replicação já está em execução.
#XMSG
deploySignavioTargetFFDisabled=Implementar fluxos de replicação que tenham o SAP Signavio como destino não é possível de momento porque estamos a executar a manutenção nesta função.
#XMSG
deployHanaViewAsSourceFFDisabled=Implementar fluxos de replicação que tenham vistas como objetos de origem para a ligação de origem selecionada não é possível de momento. Tente novamente mais tarde.
#XMSG
deployMsOneLakeTargetFFDisabled=Implementar fluxos de replicação que tenham o MS OneLake como destino não é possível de momento porque estamos a executar a manutenção nesta função.
#XMSG
deploySFTPTargetFFDisabled=Implementar fluxos de replicação que tenham o SFTP como destino não é possível de momento porque estamos a executar a manutenção nesta função.
#XMSG
deploySFTPSourceFFDisabled=Implementar fluxos de replicação que tenham o SFTP como origem não é possível de momento porque estamos a executar a manutenção nesta função.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Nome técnico
#XFLD
businessNameInRenameTarget=Nome comercial
#XTOL
renametargetDialogTitle=Renomear objeto de destino
#XBUT
targetRenameButton=Renomear
#XBUT
targetRenameCancel=Cancelar
#XMSG
mandatoryTargetName=Tem de introduzir um nome.
#XMSG
dwcSpecialChar=O _ (sublinhado) é o único caráter especial permitido.
#XMSG
dwcWithDot=O nome da tabela de destino pode ser constituído por letras latinas, números, sublinhados (_) e pontos finais (.). O primeiro caráter deve ser uma letra, um número ou um sublinhado (não um ponto final).
#XMSG
nonDwcSpecialChar=Os carateres especiais permitidos são _ (sublinhado) º (hífen) . (ponto).
#XMSG
firstUnderscorePattern=O nome não deve começar com _ (sublinhado)

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: visualizar instrução SQL Criar tabela
#XMSG
sqlDialogMaxPKWarning=No Google BigQuery, são suportadas no máximo 16 chaves primárias, e o objeto de origem tem um número maior. Portanto, não são definidas chaves primárias nesta declaração.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Uma ou mais colunas de origem têm tipos de dados que não podem ser definidos como chaves primárias no Google BigQuery. Por isso, não são definidas chaves primárias neste caso. No Google BigQuery, apenas os seguintes tipos de dados podem ter uma chave primária: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Copiar e fechar
#XBUT
closeDDL=Fechar
#XMSG
copiedToClipboard=Copiado para área de transferência


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=O objeto ''{0}'' não pode fazer parte de uma cadeia de tarefas porque não tem um fim (uma vez que inclui objetos com o tipo de carregamento Inicial e delta/Só delta).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=O objeto ''{0}'' não pode fazer parte de uma cadeia de tarefas porque não tem um fim (uma vez que inclui objetos com o tipo de carregamento inicial e delta).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Não é possível adicionar o objeto "{0}" à cadeia de tarefas.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Existem objetos de destino não guardados. Guarde novamente.{0}{0} O comportamento desta funcionalidade foi alterado: no passado, os objetos de destino só eram criados no ambiente de destino quando o fluxo de replicação era implementado.{0} Agora, os objetos já estão criados quando o fluxo de replicação é guardado. O seu fluxo de replicação foi criado antes desta alteração e contém novos objetos.{0} Deve guardar o fluxo de replicação mais uma vez antes de implementá-lo para que os novos objetos sejam incluídos corretamente.
#XMSG
confirmChangeContentTypeMessage=Está prestes a alterar o tipo de conteúdo. Se o fizer, todas as projeções existentes serão eliminadas.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Nome do assunto
#XFLD
schemaDialogVersionName=Versão do esquema
#XFLD
includeTechKey=Incluir chave técnica
#XFLD
segementButtonFlat=Plano
#XFLD
segementButtonNested=Aninhado
#XMSG
subjectNamePlaceholder=Procurar nome do assunto

#XMSG
@EmailNotificationSuccess=A configuração das notificações por e-mail em tempo de execução foi guardada.

#XFLD
@RuntimeEmailNotification=Notificação por e-mail em tempo de execução

#XBTN
@TXT_SAVE=Guardar


