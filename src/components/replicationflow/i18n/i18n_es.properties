#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Flujo de replicación

#XFLD: Edit Schema button text
editSchema=Editar esquema

#XTIT : Properties heading
configSchema=Configurar esquema

#XFLD: save changed button text
applyChanges=Aplicar modificaciones


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Seleccionar conexión de origen
#XFLD
sourceContainernEmptyText=Seleccionar contenedor
#XFLD
targetConnectionEmptyText=Seleccionar conexión de destino
#XFLD
targetContainernEmptyText=Seleccionar contenedor
#XFLD
sourceSelectObjectText=Seleccionar objeto de origen
#XFLD
sourceObjectCount=Objetos fuente ({0})
#XFLD
targetObjectText=Objetos de destino
#XFLD
confluentBrowseContext=Seleccionar contexto
#XBUT
@retry=Reintentar
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Actualización de arrendatario en curso.

#XTOL
browseSourceConnection=Examinar conexión de origen
#XTOL
browseTargetConnection=Examinar conexión de destino
#XTOL
browseSourceContainer=Examinar contenedor de origen
#XTOL
browseAndAddSourceDataset=Añadir objetos de origen
#XTOL
browseTargetContainer=Examinar contenedor de destino
#XTOL
browseTargetSetting=Examinar opciones de destino
#XTOL
browseSourceSetting=Examinar opciones de origen
#XTOL
sourceDatasetInfo=Información
#XTOL
sourceDatasetRemove=Quitar
#XTOL
mappingCount=Representa la cantidad total de asignaciones/expresiones no basadas en nombres.
#XTOL
filterCount=Representa la cantidad total de condiciones de filtro.
#XTOL
loading=Cargando...
#XCOL
deltaCapture=Captura delta
#XCOL
deltaCaptureTableName=Tabla de captura delta
#XCOL
loadType=Tipo de carga
#XCOL
deleteAllBeforeLoading=Eliminar todo antes de cargar
#XCOL
transformationsTab=Proyecciones
#XCOL
settingsTab=Opciones

#XBUT
renameTargetObjectBtn=Renombrar objeto de destino
#XBUT
mapToExistingTargetObjectBtn=Asignar a un objeto de destino existente
#XBUT
changeContainerPathBtn=Modificar ruta de contenedor
#XBUT
viewSQLDDLUpdated=Ver sentencia SQL Crear tabla
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=El objeto de origen no admite la captura delta, pero el objeto de destino seleccionado tiene activada la opción de captura delta.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=El objeto de destino no se puede utilizar porque la captura delta está activada,{0}mientras que el objeto de origen no admite la captura delta.{1}Puede seleccionar otro objeto de destino que no admita la captura delta.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Ya existe un objeto de destino con este nombre. Sin embargo, no se puede utilizar{0}porque la captura delta está activada, mientras que el objeto de origen no{0}admite la captura delta.{1}Puede introducir el nombre de un objeto de destino existente que no{0}admite la captura delta o introducir un nombre que aún no existe.
#XBUT
copySQLDDLUpdated=Copiar sentencia SQL Crear tabla
#XMSG
targetObjExistingNoCDCColumnUpdated=Las tablas que existan en Google BigQuery deben incluir las columnas siguientes para la captura de datos de modificación (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=No se admiten los siguientes objetos fuente porque no tienen ninguna clave primaria o porque utilizan una conexión que no cumple las condiciones para recuperar la clave primaria:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Consulte el SAP Knowledge Base Article 3531135 para encontrar una posible solución.
#XLST: load type list values
initial=Solo inicial
@emailUpdateError=Error al actualizar la lista de notificaciones por correo electrónico

#XLST
initialDelta=Inicial y delta

#XLST
deltaOnly=Solo delta
#XMSG
confluentDeltaLoadTypeInfo=Para el origen de Confluent Kafka, solo se admite el tipo de carga Inicial y Delta.
#XMSG
confirmRemoveReplicationObject=¿Seguro que desea eliminar la replicación?
#XMSG
confirmRemoveReplicationTaskPrompt=Esta acción eliminará las replicaciones existentes. ¿Desea continuar?
#XMSG
confirmTargetConnectionChangePrompt=Esta acción restablecerá la conexión de destino y el contenedor de destino y eliminará todos los objetos de destino. ¿Desea continuar?
#XMSG
confirmTargetContainerChangePrompt=Esta acción restablecerá el contenedor de destino y eliminará todos los objetos de destino existentes. ¿Desea continuar?
#XMSG
confirmRemoveTransformObject=¿Confirma que desea eliminar la proyección {0}?
#XMSG
ErrorMsgContainerChange=Se ha producido un error al modificar la ruta del contenedor.
#XMSG
infoForUnsupportedDatasetNoKeys=Los siguientes objetos de origen no son compatibles porque no tienen clave primaria:
#XMSG
infoForUnsupportedDatasetView=Los siguientes objetos de origen del tipo Vistas no son compatibles:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=El siguiente objeto de origen no es compatible porque es una vista SQL que contiene parámetros de entrada:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Los siguientes objetos de origen no son compatibles porque tienen desactivada la extracción:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Para las conexiones confluentes, los únicos formatos de serialización permitidos son AVRO y JSON. Los siguientes objetos no están permitidos porque utilizan un formato de serialización diferente.
#XMSG
infoForUnsupportedDatasetSchemaNotFound=No se puede obtener el esquema de los siguientes objetos. Seleccione el contexto adecuado o verifique la configuración del registro del esquema
#XTOL: warning dialog header on deleting replication task
deleteHeader=Eliminar
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=La opción Eliminar todo antes de cargar no es compatible con Google BigQuery.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=La opción Eliminar todo antes de cargar elimina y recrea el objeto (tema) antes de cada replicación. Esto también elimina todos los mensajes asignados.
#XTOL
DeleteAllBeforeLoadingLTFInfo=La opción Eliminar todo antes de cargar no es compatible con este tipo de destino.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Nombre técnico
#XCOL
connBusinessName=Nombre empresarial
#XCOL
connDescriptionName=Descripción
#XCOL
connType=Tipo
#XMSG
connTblNoDataFoundtxt=No se ha encontrado ninguna conexión
#XMSG
connectionError=Se ha producido un error al obtener las conexiones.
#XMSG
connectionCombinationUnsupportedErrorTitle=No se admite la combinación de conexiones
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Actualmente no se admite la replicación de {0} a {1}.
#XMSG
invalidTargetforSourceHDLFErrorTitle=No se admite la combinación del tipo de conexión
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=La replicación de una conexión con el tipo de conexión Archivos de SAP HANA Cloud, data lake en {0} no se admite. Solo puede replicar en SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Seleccionar
#XBUT
containerCancelBtn=Cancelar
#XTOL
containerSelectTooltip=Seleccionar
#XTOL
containerCancelTooltip=Cancelar
#XMSG
containerContainerPathPlcHold=Ruta del contenedor
#XFLD
containerContainertxt=Contenedor
#XFLD
confluentContainerContainertxt=Contexto
#XMSG
infoMessageForSLTSelection=Solo se permite el uso de /SLT/ID de transferencia en masa como contenedor. Seleccione un ID de transferencia en masa en SLT (si está disponible) y haga clic en "Enviar".
#XMSG
msgFetchContainerFail=Se ha producido un error al recuperar los datos del contenedor.
#XMSG
infoMessageForSLTHidden=Esta conexión no admite carpetas SLT, por lo que no aparecen en la lista siguiente.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Seleccione un contenedor que contenga subcarpetas.
#XMSG
sftpIncludeSubFolderText=Falso
#XMSG
sftpIncludeSubFolderTextNew=No

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Aún no hay ninguna asignación de filtros)
#XMSG
failToFetchRemoteMetadata=Se ha producido un error al obtener los metadatos.
#XMSG
failToFetchData=Se ha producido un error al obtener el destino existente.
#XCOL
@loadType=Tipo de carga
#XCOL
@deleteAllBeforeLoading=Eliminar todo antes de cargar

#XMSG
@loading=Cargando...
#XFLD
@selectSourceObjects=Seleccionar objetos de origen
#XMSG
@exceedLimit=No puede importar más de {0} objetos a la vez. Anule la selección de al menos {1} objetos.
#XFLD
@objects=Objetos
#XBUT
@ok=OK
#XBUT
@cancel=Cancelar
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Siguiente
#XBUT
btnAddSelection=Añadir selección
#XTOL
@remoteFromSelection=Quitar de la selección
#XMSG
@searchInForSearchField=Buscar en {0}

#XCOL
@name=Nombre técnico
#XCOL
@type=Tipo
#XCOL
@location=Ubicación
#XCOL
@label=Nombre empresarial
#XCOL
@status=Estado

#XFLD
@searchIn=Buscar en:
#XBUT
@available=Disponible
#XBUT
@selection=Selección

#XFLD
@noSourceSubFolder=Tablas y vistas
#XMSG
@alreadyAdded=Ya presente en el diagrama
#XMSG
@askForFilter=Hay más de {0} elementos. Introduzca una cadena de filtro para reducir el número de elementos.
#XFLD: success label
lblSuccess=Correcto
#XFLD: ready label
lblReady=Listo
#XFLD: failure label
lblFailed=Fallido
#XFLD: fetching status label
lblFetchingDetail=Obteniendo detalles

#XMSG Place holder text for tree filter control
filterPlaceHolder=Escriba texto para filtrar objetos iniciales
#XMSG Place holder text for server search control
serverSearchPlaceholder=Escriba y pulse Intro para buscar
#XMSG
@deployObjects=Se están importando {0} objetos...
#XMSG
@deployObjectsStatus=Número de objetos importados: {0}. Número de objetos que no se han podido importar: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=No se ha podido abrir el navegador del repositorio local.
#XMSG
@openRemoteSourceBrowserError=No se han podido obtener los objetos de origen.
#XMSG
@openRemoteTargetBrowserError=No se han podido obtener los objetos de destino.
#XMSG
@validatingTargetsError=Se ha producido un error al validar los destinos.
#XMSG
@waitingToImport=Listo para importar

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Se ha superado el número máximo de objetos. Seleccione un máximo de 500 objetos para un flujo de replicación.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Nombre técnico
#XFLD
sourceObjectBusinessName=Nombre empresarial
#XFLD
sourceNoColumns=Cantidad de columnas
#XFLD
containerLbl=Contenedor

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Debe seleccionar una conexión de origen para el flujo de replicación.
#XMSG
validationSourceContainerNonExist=Debe seleccionar un contenedor para la conexión de origen.
#XMSG
validationTargetNonExist=Debe seleccionar una conexión de destino para el flujo de replicación.
#XMSG
validationTargetContainerNonExist=Debe seleccionar un contenedor para la conexión de destino.
#XMSG
validationTruncateDisabledForObjectTitle=Replicación en almacenes de objetos.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Solo es posible realizar la replicación en un almacén en la nube si la opción Eliminar todo antes de cargar está definida o el objeto de destino no existe en el destino.{0}{0} Para poder seguir realizando la replicación para objetos para los que la opción Eliminar todo antes de cargar no está definida, asegúrese de que el objeto de destino no existe en el sistema antes de ejecutar el flujo de replicación.
#XMSG
validationTaskNonExist=Debe tener al menos una replicación en el flujo de replicación.
#XMSG
validationTaskTargetMissing=Debe tener un destino para la replicación con el origen: {0}
#XMSG
validationTaskTargetIsSAC=El destino seleccionado es un artefacto de SAC: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=El destino seleccionado no es una tabla local compatible: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Ya existe un objeto con este nombre en el destino. No obstante, este objeto no se puede utilizar como objeto de destino para un flujo de replicación en el repositorio local porque no es una tabla local.
#XMSG
validateSourceTargetSystemDifference=Debe seleccionar distintas combinaciones de conexión de origen y destino y de contenedor para el flujo de replicación.
#XMSG
validateDuplicateSources=Una o más replicaciones tienen nombres de objeto de origen duplicados: {0}.
#XMSG
validateDuplicateTargets=Una o más replicaciones tienen nombres de objeto de destino duplicados: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=El objeto de origen {0} no admite la captura delta, mientras que el objeto de destino {1} sí. Debe quitar la replicación.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Debe seleccionar el tipo de carga "Inicial y delta" para la replicación con el nombre del objeto de destino {0}.
#XMSG
validationAutoRenameTarget=Se han renombrado las columnas de destino.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Se ha añadido una proyección automática y se han renombrado las columnas de destino siguientes para permitir la replicación en el destino:{1}{1} {0} {1}{1}Esto se debe a uno de los siguientes motivos:{1}{1}{2} Caracteres no admitidos{1}{2} Prefijo reservado
#XMSG
validationAutoRenameTargetDescriptionUpdated=Se ha añadido una proyección automática y se han renombrado las columnas de destino siguientes para permitir la replicación en Google BigQuery:{1}{1} {0} {1}{1}Esto se debe a uno de los siguientes motivos:{1}{1}{2} Nombre de columna reservado{1}{2} Caracteres no admitidos{1}{2} Prefijo reservado
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Se ha añadido una proyección automática y se han renombrado las columnas de destino siguientes para permitir las replicaciones en Confluent:{1}{1} {0} {1}{1}Esto se debe a uno de los siguientes motivos:{1}{1}{2} Nombre de columna reservado{1}{2} Caracteres no admitidos{1}{2} Prefijo reservado
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Se ha añadido una proyección automática y se han renombrado las columnas de destino siguientes para permitir las replicaciones en el destino:{1}{1} {0} {1}{1}Esto se debe a uno de los siguientes motivos:{1}{1}{2} Nombre de columna reservado{1}{2} Caracteres no admitidos{1}{2} Prefijo reservado
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Se ha renombrado el objeto de destino.
#XMSG
autoRenameInfoDesc=Se ha renombrado el objeto de destino porque contenía caracteres no admitidos. Solo se admiten los siguientes caracteres:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(punto){0}{1}_(guion bajo){0}{1}-(guion)
#XMSG
validationAutoTargetTypeConversion=Se han modificado los tipos de datos de destino.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Para las columnas de destino siguientes se han cambiado los tipos de datos de destino porque Google BigQuery no admite los tipos de datos de origen:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Para las siguientes columnas de destino se han cambiado los tipos de datos de destino porque en los tipos de datos de origen no se admiten en la conexión de destino: {1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Reduzca los nombres de columna de destino.
#XMSG
validationMaxCharLengthGBQTargetDescription=En Google BigQuery, los nombres de columna pueden utilizar 300 caracteres como máximo. Utilice una proyección para acortar los nombres de las columnas de destino siguientes:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=No se crearán claves primarias.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=En Google BigQuery se admiten 16 claves primarias como máximo, pero el objeto de origen tiene más. No se creará ninguna de las claves primarias en el objeto de destino.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Una o más columnas de origen tienen tipos de datos que no se pueden definir como claves primarias en Google BigQuery. No se creará ninguna de las claves primarias en el objeto de destino.{0}{0}Los siguientes tipos de datos de destino son compatibles con los tipos de datos de BigQuery para los que se puede definir una clave primaria:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Defina una o más columnas como claves primarias.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Debe definir una o más columnas como claves primarias; para ello, debe utilizar el diálogo de esquema fuente.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Defina una o más columnas como claves primarias.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Debe definir una o más columnas como clave primaria que coincidan con las restricciones de clave primaria de su objeto de origen. Para ello, vaya a "Configurar esquema" en las propiedades de su objeto de origen.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Introduzca un valor de partición máximo válido.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=El valor máximo de partición debe ser ≥ 1 y ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Defina una o más columnas como clave primaria.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Para replicar un objeto, debe definir una o más columnas de destino como clave primaria. Utilice una proyección para hacerlo.
#XMSG
validateHDLFNoPKExistingDatasetError=Defina una o más columnas como clave primaria.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Para replicar datos en un objeto de destino existente, debe tener una o más columnas definidas como clave primaria. {0} Cuenta con las siguientes opciones para definir una o más columnas como clave primaria: {0} {1} Utilice el editor de tablas locales para modificar el objeto de destino existente. A continuación, vuelva a cargar el flujo de replicación.{0}{1} Renombre el objeto de destino en el flujo de replicación, de modo que se creará otro objeto en cuanto se inicie una ejecución. Tras renombrarlo, puede definir una o más columnas como clave primaria en una proyección.{0}{1} Asigne el objeto a otro objeto de destino existente en el que ya se hayan definido como clave primaria una o más columnas.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=El destino seleccionado ya existe en el repositorio: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Los nombres de las tablas de captura delta ya se utilizan en otras tablas del repositorio: {0}. Para poder guardar el flujo de replicación, antes debe cambiar el nombre de estos objetos de destino a fin de garantizar que los nombres de las tablas de captura delta sean unívocos.
#XMSG
validateConfluentEmptySchema=Definir esquema
#XMSG
validateConfluentEmptySchemaDescUpdated=La tabla fuente no tiene ningún esquema. Seleccione Configurar esquema para definir uno
#XMSG
validationCSVEncoding=Codificación CSV no válida
#XMSG
validationCSVEncodingDescription=La codificación CSV de la tarea no es válida.
#XMSG
validateConfluentEmptySchema=Seleccione un tipo de datos de destino compatible
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Seleccione un tipo de datos de destino compatible
#XMSG
globalValidateTargetDataTypeDesc=Se ha producido un error con las asignaciones de columna. Vaya a Proyección y asegúrese de que todas las columnas de origen están asignadas a una columna unívoca, con una columna que tenga un tipo de datos compatible y que todas las expresiones definidas sean válidas.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Nombres de columnas duplicados.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=No se admiten nombres de columnas duplicados. Utilice el cuadro de diálogo Proyección para corregirlos. Los siguientes objetos de destino tienen nombres de columnas duplicados: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Nombres de columnas duplicados.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=No se admiten nombres de columnas duplicados. Los siguientes objetos de destino tienen nombres de columnas duplicados: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Puede haber inconsistencias en los datos.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=El tipo de carga Solo delta no tendrá en cuenta las modificaciones realizadas en la fuente entre la última vez que se guardó y la siguiente ejecución.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Modifique el tipo de carga a "Inicial".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=La replicación de objetos basados en ABAP que no tienen una clave principal solo es posible para el tipo de carga "Solo inicial".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Desactivar la captura delta.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Para replicar un objeto que no tiene una clave principal utilizando el tipo de conexión de origen ABAP, primero debe desactivar la captura delta para esta tabla.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Modificar objeto de destino.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=El objeto de destino no se puede utilizar porque la captura delta está activada. Puede renombrar el objeto de destino y luego desactivar la captura delta para el objeto nuevo (renombrado), o asignar el objeto de origen a un objeto de destino para el cual la captura delta está desactivada.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Modificar objeto de destino.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=El objeto de destino no se puede utilizar porque no tiene la columna técnica requerida __load_package_id. Puede cambiar el nombre del objeto de destino utilizando un nombre que aún no existe. Luego, el sistema crea un nuevo objeto que tiene la misma definición que el objeto de origen y contiene la columna técnica. De forma alternativa, puede asignar el objeto de destino a un objeto existente que tenga la columna técnica requerida (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Modificar objeto de destino.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=El objeto de destino no se puede utilizar porque no tiene la columna técnica requerida __load_record_id. Puede cambiar el nombre del objeto de destino utilizando un nombre que aún no existe. Luego, el sistema crea un nuevo objeto que tiene la misma definición que el objeto de origen y contiene la columna técnica. De forma alternativa, puede asignar el objeto de destino a un objeto existente que tenga la columna técnica requerida (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Modificar objeto de destino.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=El objeto de destino no se puede utilizar porque el tipo de datos de su columna técnica __load_record_id no es "string(44)". Puede renombrar el objeto de destino utilizando un nombre que aún no existe. Luego, el sistema crea un nuevo objeto que tiene la misma definición que el objeto de origen y, en consecuencia, el tipo de datos correcto. De forma alternativa, puede asignar el objeto de destino a un objeto existente que tenga la columna técnica requerida (__load_record_id) con el tipo de datos correcto.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Modificar objeto de destino.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=El objeto de destino no se puede utilizar porque tiene una clave principal, mientras que el objeto de origen no tiene ninguna. Puede renombrar el objeto de destino utilizando un nombre que aún no existe. Luego, el sistema crea un nuevo objeto que tiene la misma definición que el objeto de origen y, en consecuencia, no tiene clave principal. De forma alternativa, puede asignar el objeto de destino a un objeto existente que tenga la columna técnica requerida (__load_package_id) y no tenga una clave principal.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Modificar objeto de destino.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=El objeto de destino no se puede utilizar porque tiene una clave principal, mientras que el objeto de origen no tiene ninguna. Puede renombrar el objeto de destino utilizando un nombre que aún no existe. Luego, el sistema crea un nuevo objeto que tiene la misma definición que el objeto de origen y, en consecuencia, no tiene clave principal. De forma alternativa, puede asignar el objeto de destino a un objeto existente que tenga la columna técnica requerida (__load_record_id) y no tenga una clave principal.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Modificar objeto de destino.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=El objeto de destino no se puede utilizar porque el tipo de datos de su columna técnica __load_package_id no es " binary(>=256)". Puede renombrar el objeto de destino utilizando un nombre que aún no existe. Luego, el sistema crea un nuevo objeto que tiene la misma definición que el objeto de origen y, en consecuencia, el tipo de datos correcto. De forma alternativa, puede asignar el objeto de destino a un objeto existente que tenga la columna técnica requerida (__load_package_id) con el tipo de datos correcto.
#XMSG
validationAutoRenameTargetDPID=Se han renombrado las columnas de destino.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Quitar objeto de origen.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=El objeto de origen tiene una columna clave primaria, lo cual no se admite en este contexto.
#XMSG
validationAutoRenameTargetDPIDDescription=Se ha añadido una proyección automática y se han renombrado las siguientes columnas de etiquetas para permitir la replicación desde una fuente ABAP sin claves: {1}{1} {0} {1}{1}Esto se debe a uno de los siguientes motivos:{1}{1}{2} Nombre de columna reservada{1}{2} Caracteres no admitidos{1}{2} Prefijo reservado
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Replicación a {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Actualmente no es posible guardar ni desplegar flujos de replicación que tengan {0} como destino porque estamos llevando a cabo tareas de mantenimiento en esta función.
#XMSG
TargetColumnSkippedLTF=Se ha omitido la columna de destino.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Se ha omitido la columna de destino debido a un tipo de datos no admitido. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Columna de tiempo como clave primaria.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=El objeto de origen tiene una columna de tiempo como clave primaria, lo cual es incompatible en este contexto.
#XMSG
validateNoPKInLTFTarget=Falta la clave principal.
#XMSG
validateNoPKInLTFTargetDescription=La clave principal no está definida en el destino, lo cual no es compatible en este contexto.
#XMSG
validateABAPClusterTableLTF=Tabla de cluster de ABAP.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=El objeto de origen es unha tabla de cluster de ABAP que no se admite en este contexto.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Parece que aún no ha añadido datos.
#YINS
welcomeText2=Para iniciar el flujo de replicación, seleccione una conexión y un objeto de origen a la izquierda.

#XBUT
wizStep1=Seleccionar conexión de origen
#XBUT
wizStep2=Seleccionar contenedor de origen
#XBUT
wizStep3=Añadir objetos de origen

#XMSG
limitDataset=Se ha alcanzado el número máximo de objetos. Quite los objetos existentes para añadir nuevos o cree un flujo de replicación.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=No se puede iniciar el flujo de replicación para esta conexión de destino ajena a SAP porque no hay volumen de salida disponible para este mes.
#XMSG
premiumOutBoundRFAdminWarningMsg=Un administrador puede incrementar los bloques de salida premium para este arrendatario con el fin de que haya volumen de salida disponible para este mes.
#XMSG
messageForToastForDPIDColumn2=Se ha añadido una nueva columna al destino para {0} objetos: es necesario para gestionar registros duplicados en relación con objetos de origen basados en ABAP que no tienen una clave principal.
#XMSG
PremiumInboundWarningMessage=Dependiendo de la cantidad de flujos de replicación y el volumen de datos que se replicarán, los recursos de SAP HANA {0}necesarios para replicar datos {1} pueden exceder la capacidad disponible para su arrendatario.
#XMSG
PremiumInboundWarningMsg=Dependiendo de la cantidad de flujos de replicación y el volumen de datos que se replicarán, {0}los recursos de SAP HANA necesarios para replicar datos mediante "{1}" pueden exceder la capacidad disponible para su arrendatario.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Indique un nombre de proyección.
#XMSG
emptyTargetColumn=Debe introducir un nombre para la columna de destino.
#XMSG
emptyTargetColumnBusinessName=Introduzca un nombre empresarial en la columna de destino.
#XMSG
invalidTransformName=Indique un nombre de proyección.
#XMSG
uniqueColumnName=Renombre la columna de destino.
#XMSG
copySourceColumnLbl=Copiar columnas del objeto de origen
#XMSG
renameWarning=Asegúrese de elegir un nombre único al renombrar la tabla de destino. Si la tabla con el nuevo nombre ya existe en el espacio, se usará la definición de esa tabla.

#XMSG
uniqueColumnBusinessName=Renombrar el nombre empresarial de la columna de destino.
#XMSG
uniqueSourceMapping=Seleccione una columna de origen diferente.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=Las siguientes columnas ya están usando la columna de origen {0}:{1}{1}{2}{1}{1} Para esta columna de destino o para las otras, seleccione una columna de origen que no esté en uso para guardar la proyección.
#XMSG
uniqueColumnNameDescription=El nombre de la columna de destino que ha introducido ya existe. Para poder guardar la proyección, debe introducir un nombre de columna unívoco.
#XMSG
uniqueColumnBusinessNameDesc=El nombre empresarial de la columna de destino ya existe. Para guardar la proyección, debe introducir un nombre empresarial de columna unívoco.
#XMSG
emptySource=Seleccione una columna o indique una constante.
#XMSG
emptySourceDescription=Para crear una entrada de asignación válida, debe seleccionar una columna fuente o introducir un valor constante.
#XMSG
emptyExpression=Definir asignación
#XMSG
emptyExpressionDescription1=Seleccione la columna de origen a la que quiere asignar la columna de destino o bien marque la casilla de selección de la columna {0} Funciones/constantes {1}. {2} {2} Las funciones se introducen automáticamente según el tipo de datos de destino. Los valores constantes pueden introducirse manualmente.
#XMSG
numberExpressionErr=Introduzca un número.
#XMSG
numberExpressionErrDescription=Ha seleccionado un tipo de datos numérico. Esto significa que solo puede introducir numerales más la coma decimal si es necesario. No utilice comillas simples.
#XMSG
invalidLength=Indique un valor de longitud válido.
#XMSG
invalidLengthDescription=La longitud del tipo de datos debe ser igual o mayor a la longitud de la columna de origen y puede estar entre el 1 y el 5000.
#XMSG
invalidMappedLength=Indique un valor de longitud válido.
#XMSG
invalidMappedLengthDescription=La longitud del tipo de datos debe ser igual o mayor a la longitud de la columna de origen {0} y puede estar entre el 1 y el 5000.
#XMSG
invalidPrecision=Indique un valor de precisión válido.
#XMSG
invalidPrecisionDescription=La precisión define el número total de dígitos. La escala define el número de dígitos después de la coma decimal y puede estar entre el 0 y la precisión.{0}{0} Ejemplos: {0}{1} precisión 6, escala 2 corresponde a números como 1234,56.{0}{1} Precisión 6, escala 6 corresponde a números como 0,123546.{0} {0} La precisión y la escala del destino deben ser compatibles con la precisión y la escala del origen, de modo que todos los dígitos del origen quepan en el campo de destino. Por ejemplo, si tiene una precisión 6 y una escala 2 en el origen (y, en consecuencia, dígitos distintos de 0 antes de la coma decimal), no podrá tener una precisión 6 y una escala 6 en el destino.
#XMSG
invalidPrimaryKey=Introduzca al menos una clave primaria.
#XMSG
invalidPrimaryKeyDescription=No se ha definido la clave primaria para este esquema.
#XMSG
invalidMappedPrecision=Indique un valor de precisión válido.
#XMSG
invalidMappedPrecisionDescription1=La precisión define el número total de dígitos. La escala define el número de dígitos después de la coma decimal y puede estar entre el 0 y la precisión.{0}{0} Ejemplos:{0}{1} precisión 6, escala 2 corresponde a números como 1234,56.{0}{1} Precisión 6, escala 6 corresponde a números como 0,123546.{0}{0}La precisión del tipo de datos debe ser igual o mayor a la precisión de origen ({2}).
#XMSG
invalidScale=Indique un valor de escala válido.
#XMSG
invalidScaleDescription=La precisión define el número total de dígitos. La escala define el número de dígitos después de la coma decimal y puede estar entre el 0 y la precisión.{0}{0} Ejemplos: {0}{1} precisión 6, escala 2 corresponde a números como 1234,56.{0}{1} Precisión 6, escala 6 corresponde a números como 0,123546.{0} {0} La precisión y la escala del destino deben ser compatibles con la precisión y la escala del origen, de modo que todos los dígitos del origen quepan en el campo de destino. Por ejemplo, si tiene una precisión 6 y una escala 2 en el origen (y, en consecuencia, dígitos distintos de 0 antes de la coma decimal), no podrá tener una precisión 6 y una escala 6 en el destino.
#XMSG
invalidMappedScale=Indique un valor de escala válido.
#XMSG
invalidMappedScaleDescription1=La precisión define el número total de dígitos. La escala define el número de dígitos después de la coma decimal y puede estar entre el 0 y la precisión.{0}{0} Ejemplos:{0}{1} precisión 6, escala 2 corresponde a números como 1234,56.{0}{1} Precisión 6, escala 6 corresponde a números como 0,123546.{0}{0}La escala del tipo de datos debe ser igual o mayor a la escala de origen ({2}).
#XMSG
nonCompatibleDataType=Seleccione un tipo de datos de destino compatible.
#XMSG
nonCompatibleDataTypeDescription1=El tipo de datos que especifique aquí debe ser compatible con el de origen ({0}). {1}{1} Ejemplo: si la columna de origen tiene el tipo de datos cadena y contiene letras, no puede utilizar un tipo de datos decimal para el destino.
#XMSG
invalidColumnCount=Seleccione una columna.
#XMSG
ObjectStoreInvalidScaleORPrecision=Introduzca un valor válido para la precisión y la escala.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=El primer valor es la precisión, que define la cantidad total de dígitos. El segundo valor es la escala, que define los dígitos después del decimal. Introduzca un valor de escala de destino que sea mayor que el valor de escala de origen y asegúrese de que la diferencia entre la escala de destino introducida y el valor de precisión sea mayor que la diferencia entre la escala de origen y el valor de precisión.
#XMSG
InvalidPrecisionORScale=Introduzca un valor válido para la precisión y la escala.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=El primer valor es la precisión, que define el número total de dígitos. El segundo valor es la escala, que define los dígitos después de la coma decimal.{0}{0}Dado que el tipo de datos de origen no se admite en Google BigQuery, se convertirá al tipo de datos de destino DECIMAL. En este caso, solo se puede definir una precisión comprendida entre 38 y 76, y una escala comprendida entre 9 y 38. Además, el resultado de la escala negativa de la precisión, que representa los dígitos antes de la coma digital, debe estar comprendido entre 29 y 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=El primer valor es la precisión, que define el número total de dígitos. El segundo valor es la escala, que define los dígitos después de la coma decimal.{0}{0}Dado que el tipo de datos de origen no se admite en Google BigQuery, se convertirá al tipo de datos de destino DECIMAL. En este caso, se debe definir una precisión de 20 o más. Además, el resultado de la escala negativa de la precisión, que refleja los dígitos antes de la coma digital, debe ser 20 o más.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=El primer valor es la precisión, que define el número total de dígitos. El segundo valor es la escala, que define los dígitos después de la coma decimal.{0}{0}Dado que el tipo de datos de origen no se admite en el destino, se convierte al tipo de datos de destino DECIMAL. En este caso, la precisión debe definirse con cualquier número mayor que o igual a 1 y menor que o igual a 38, y la escala debe ser menor que o igual a la precisión.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=El primer valor es la precisión, que define el número total de dígitos. El segundo valor es la escala, que define los dígitos después de la coma decimal.{0}{0}Dado que el tipo de datos de origen no se admite en el destino, se convertirá al tipo de datos de destino DECIMAL. En este caso, se debe definir una precisión de 20 o más. Además, el resultado de la escala negativa de la precisión, que refleja los dígitos antes de la coma digital, debe ser 20 o más.
#XMSG
invalidColumnCountDescription=Para crear una entrada de asignación válida, debe seleccionar una columna fuente o introducir un valor constante.
#XMSG
duplicateColumns=Renombre la columna de destino.
#XMSG
duplicateGBQCDCColumnsDesc=El nombre de la columna de destino está reservado en Google BigQuery. Debe renombrarla para poder guardar la proyección.
#XMSG
duplicateConfluentCDCColumnsDesc=El nombre de la columna de destino está reservado en Confluent. Debe renombrarla para poder guardar la proyección.
#XMSG
duplicateSignavioCDCColumnsDesc=El nombre de la columna de destino está reservado en SAP Signavio. Debe renombrarla para poder guardar la proyección.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=El nombre de la columna de destino está reservado en MS OneLake. Debe renombrarla para poder guardar la proyección.
#XMSG
duplicateSFTPCDCColumnsDesc=El nombre de la columna de destino está reservado en SFTP. Debe renombrarla para poder guardar la proyección.
#XMSG
GBQTargetNameWithPrefixUpdated1=El nombre de la columna de destino contiene un prefijo que está reservado en Google BigQuery. Tiene que renombrarla para poder guardar la proyección. {0}{0}El nombre de la columna destino no puede comenzar con ninguna de las cadenas siguientes:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Acorte el nombre de columna de destino.
#XMSG
GBQtargetMaxLengthDesc=En Google BigQuery, el nombre de columna no puede exceder los 300 caracteres. Acorte el nombre de columna destino para poder guardar la proyección.
#XMSG
invalidMappedScalePrecision=La precisión y la escala para el destino deben ser compatibles con la precisión y la escala para el origen, de forma que todos los dígitos de origen se adapten al campo de destino.
#XMSG
invalidMappedScalePrecisionShortText=Introduzca un valor válido de precisión y escala.
#XMSG
validationIncompatiblePKTypeDescProjection3=Una o más columnas de origen tienen tipos de datos que no se pueden definir como claves primarias en Google BigQuery. No se creará ninguna de las claves primarias en el objeto de destino.{0}{0}Los siguientes tipos de datos de destino son compatibles con los tipos de datos de BigQuery para los que se puede definir una clave primaria:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Desmarque la columna __message_id.
#XMSG
uncheckColumnMessageIdDesc=Columna: Clave primaria
#XMSG
validationOpCodeInsert=Debe introducir un valor para Insertar.
#XMSG
recommendDifferentPrimaryKey=Le recomendamos que seleccione una clave principal diferente en el nivel de posición.
#XMSG
recommendDifferentPrimaryKeyDesc=Cuando el código de operación ya está definido, se recomienda seleccionar claves principales diferentes para el índice del matriz y las posiciones, para evitar problemas como por ejemplo duplicación de columnas.
#XMSG
selectPrimaryKeyItemLevel=Debe seleccionar al menos una clave principal tanto para el encabezado como para el nivel de posición.
#XMSG
selectPrimaryKeyItemLevelDesc=Cuando se despliega una matriz o un mapa, debe seleccionar dos claves principales, una en el nivel de encabezado y otra en el nivel de posición.
#XMSG
invalidMapKey=Debe seleccionar al menos una clave principal en el nivel de encabezado.
#XMSG
invalidMapKeyDesc=Cuando se despliega una matriz o un mapa, debe seleccionar una clave principal en el nivel de encabezado.
#XFLD
txtSearchFields=Buscar columnas de destino
#XFLD
txtName=Nombre
#XMSG
txtSourceColValidation=Hay una o más columnas de origen que no se admiten:
#XMSG
txtMappingCount=Asignaciones ({0})
#XMSG
schema=Esquema
#XMSG
sourceColumn=Columnas fuente
#XMSG
warningSourceSchema=Todas las modificaciones hechas en el esquema afectarán a las asignaciones en el diálogo de proyección.
#XCOL
txtTargetColName=Columna de destino (nombre técnico)
#XCOL
txtDataType=Tipo de datos de destino
#XCOL
txtSourceDataType=Tipo de datos de fuente
#XCOL
srcColName=Columna fuente (nombre técnico)
#XCOL
precision=Precisión
#XCOL
scale=Escala
#XCOL
functionsOrConstants=Funciones / Constantes
#XCOL
txtTargetColBusinessName=Columna de destino (nombre empresarial)
#XCOL
prKey=Clave primaria
#XCOL
txtProperties=Propiedades
#XBUT
txtOK=Guardar
#XBUT
txtCancel=Cancelar
#XBUT
txtRemove=Quitar
#XFLD
txtDesc=Descripción
#XMSG
rftdMapping=Asignación
#XFLD
@lblColumnDataType=Tipo de datos
#XFLD
@lblColumnTechnicalName=Nombre técnico
#XBUT
txtAutomap=Asignación automática
#XBUT
txtUp=Arriba
#XBUT
txtDown=Abajo

#XTOL
txtTransformationHeader=Proyección
#XTOL
editTransformation=Editar
#XTOL
primaryKeyToolip=Clave


#XMSG
rftdFilter=Filtrar
#XMSG
rftdFilterColumnCount=Fuente: {0}({1})
#XTOL
rftdFilterColSearch=Buscar
#XMSG
rftdFilterColNoData=No hay columnas para visualizar
#XMSG
rftdFilteredColNoExps=Sin expresiones de filtro
#XMSG
rftdFilterSelectedColTxt=Añadir filtro para
#XMSG
rftdFilterTxt=Filtro disponible para
#XBUT
rftdFilterSelectedAddColExp=Añadir expresión
#YINS
rftdFilterNoSelectedCol=Seleccione una columna para añadir el filtro.
#XMSG
rftdFilterExp=Expresión de filtro
#XMSG
rftdFilterNotAllowedColumn=No se admite la adición de filtros para esta columna.
#XMSG
rftdFilterNotAllowedHead=No hay ninguna columna admitida
#XMSG
rftdFilterNoExp=No se ha definido ningún filtro
#XTOL
rftdfilteredTt=Filtrados
#XTOL
rftdremoveexpTt=Quitar expresión de filtro
#XTOL
validationMessageTt=Mensajes de validación
#XTOL
rftdFilterDateInp=Seleccione una fecha
#XTOL
rftdFilterDateTimeInp=Seleccione una fecha y hora
#XTOL
rftdFilterTimeInp=Seleccione una hora
#XTOL
rftdFilterInp=Introduzca un valor
#XMSG
rftdFilterValidateEmptyMsg={0} expresiones de filtro de la columna {1} están vacías.
#XMSG
rftdFilterValidateInvalidNumericMsg={0} expresiones de filtro de la columna {1} contienen valores numéricos no válidos.
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=La expresión de filtro debe contener valores numéricos válidos
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Si el esquema de objetos de destino ha cambiado, utilice la función “Asignar a un objeto de destino existente” en la página principal para adaptar las modificaciones y vuelva a asignar el objeto de destino con su fuente.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Si la tabla de destino ya existe y la asignación incluye una modificación de esquema, debe modificar la tabla en consecuencia antes de desplegar el flujo de replicación.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Si la asignación implica una modificación de esquema, debe modificar la tabla en consecuencia antes de desplegar el flujo de replicación.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Las siguientes columnas no admitidas se han omitido de la definición de origen: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Las siguientes columnas no admitidas se han omitido de la definición de destino: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Los siguientes objetos no se admiten porque están expuestos para su consumo: {0} {1} {0} {0} Para utilizar tablas en un flujo de replicación, el uso semántico (en las opciones de tabla) no debe definirse en {2}Conjunto de datos analíticos{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=El objeto de destino no se puede utilizar porque está expuesto para su consumo. {0} {0} Para utilizar la tabla en un flujo de replicación, el uso semántico (en las opciones de tabla) no debe definirse en {1}Conjunto de datos analíticos{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Ya existe un objeto de destino con este nombre. Sin embargo, no se puede utilizar porque está expuesto para su consumo. {0} {0} Para utilizar la tabla en un flujo de replicación, el uso semántico (en las opciones de tabla) no debe definirse en {1}Conjunto de datos analíticos{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Ya existe un objeto con este nombre en el destino. {0}No obstante, este objeto no se puede utilizar como objeto de destino para un flujo de replicación en el repositorio local porque no es una tabla local.
#XMSG:
targetAutoRenameUpdated=Se ha renombrado la columna de destino.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=La columna de destino se ha renombrado para permitir replicaciones en Google BigQuery. El motivo puede ser uno de los siguientes:{0} {1}{2}Nombre de columna reservado{3}{2}Caracteres no admitidos{3}{2}Prefijo reservado{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=La columna de destino se ha renombrado para permitir replicaciones en Confluent. El motivo puede ser uno de los siguientes:{0} {1}{2}Nombre de columna reservado{3}{2}Caracteres no admitidos{3}{2}Prefijo reservado{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=La columna de destino se ha renombrado para permitir replicaciones en el destino. El motivo puede ser uno de los siguientes:{0} {1}{2}Caracteres no admitidos{3}{2}Prefijo reservado{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=La columna de destino se ha renombrado para permitir replicaciones en el destino. El motivo puede ser uno de los siguientes:{0} {1}{2}Nombre de columna reservada{3}{2}Caracteres no admitidos{3}{2}Prefijo reservado{3}{4}
#XMSG:
targetAutoDataType=Se ha modificado el tipo de datos de destino.
#XMSG:
targetAutoDataTypeDesc=El tipo de datos de destino se ha cambiado a {0} porque el tipo de datos de origen no es compatible con Google BigQuery.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=El tipo de datos de destino se ha cambiado a {0} porque el tipo de datos de origen no es compatible con la conexión de destino.
#XMSG
projectionGBQUnableToCreateKey=No se crearán claves primarias.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=En Google BigQuery se admiten 16 claves primarias como máximo, pero el objeto de origen tiene más. No se creará ninguna de las claves primarias en el objeto de destino.
#XMSG
HDLFNoKeyError=Defina una o más columnas como clave primaria.
#XMSG
HDLFNoKeyErrorDescription=Para replicar un objeto, debe definir una o más columnas de destino como clave primaria.
#XMSG
HDLFNoKeyErrorExistingTarget=Defina una o más columnas como clave primaria.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Para replicar datos en un objeto de destino existente, debe tener una o más columnas definidas como clave primaria. {0} {0} Cuenta con las siguientes opciones para definir una o más columnas como clave primaria: {0}{0}{1} Utilice el editor de tablas locales para modificar el objeto de destino existente. A continuación, vuelva a cargar el flujo de replicación. {0}{0}{1} Renombre el objeto de destino en el flujo de replicación, de modo que se creará otro objeto en cuanto se inicie una ejecución. Tras renombrarlo, puede definir una o más columnas como clave primaria en una proyección. {0}{0}{1}Asigne el objeto a otro objeto de destino existente en el que ya se hayan definido como clave primaria una o más columnas.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Se ha modificado la clave primaria.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=En comparación con el objeto de origen, ha definido distintas columnas como clave principal para el objeto de destino. Asegúrese de que estas columnas identifiquen de forma unívoca todas las filas para evitar posibles daños en los datos al replicarlos más tarde. {0} {0} En el objeto de origen, las siguientes columnas se definen como clave primaria: {0} {1}
#XMSG
duplicateDPIDColumns=Renombrar la columna de destino.
#XMSG
duplicateDPIDDColumnsDesc1=El nombre de esta columna de destino está reservado para una columna técnica. Introduzca un nombre diferente para guardar la proyección.
#XMSG:
targetAutoRenameDPID=Se ha renombrado la columna de destino.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=La columna de destino se ha renombrado para permitir replicaciones desde el origen ABAP sin claves. El motivo puede ser uno de los siguientes:{0} {1}{2}Nombre de columna reservado{3}{2}Caracteres no admitidos{3}{2}Prefijo reservado{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle=Opciones de destino de {0}
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle=Opciones de origen de {0}
#XBUT
connectionSettingSave=Guardar
#XBUT
connectionSettingCancel=Cancelar
#XBUT: Button to keep the object level settings
txtKeep=Mantener
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Sobrescribir
#XFLD
targetConnectionThreadlimit=Límite de threads de destino para carga inicial (1-100)
#XFLD
connectionThreadLimit=Límite de threads de origen para carga inicial (1-100)
#XFLD
maxConnection=Límite de threads de replicación (1-100)
#XFLD
kafkaNumberOfPartitions=Número de particiones
#XFLD
kafkaReplicationFactor=Factor de replicación
#XFLD
kafkaMessageEncoder=Codificador de mensajes
#XFLD
kafkaMessageCompression=Compresión de mensajes
#XFLD
fileGroupDeltaFilesBy=Agrupar delta por
#XFLD
fileFormat=Tipo de archivo
#XFLD
csvEncoding=Codificación CSV
#XFLD
abapExitLbl=Exit ABAP
#XFLD
deltaPartition=Recuento de threads de objetos para cargas delta (1-10)
#XFLD
clamping_Data=Error al truncar los datos
#XFLD
fail_On_Incompatible=Error en datos incompatibles
#XFLD
maxPartitionInput=Número máximo de particiones
#XFLD
max_Partition=Definir número máximo de particiones
#XFLD
include_SubFolder=Incluir subcarpetas
#XFLD
fileGlobalPattern=Patrón global para nombre de archivo
#XFLD
fileCompression=Compresión de archivo
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Delimitador de archivo
#XFLD
fileIsHeaderIncluded=Cabecera de archivo
#XFLD
fileOrient=Orientación
#XFLD
gbqWriteMode=Modo de escritura
#XFLD
suppressDuplicate=Suprimir duplicados
#XFLD
apacheSpark=Activar la compatibilidad con Apache Spark
#XFLD
clampingDatatypeCb=Fijar tipos de datos de punto flotante decimal
#XFLD
overwriteDatasetSetting=Sobrescribir las opciones de destino a nivel de objeto
#XFLD
overwriteSourceDatasetSetting=Sobrescribir las opciones de origen a nivel de objeto
#XMSG
kafkaInvalidConnectionSetting=Introduzca un número entre {0} y {1}.
#XMSG
MinReplicationThreadErrorMsg=Indique un número mayor que {0}.
#XMSG
MaxReplicationThreadErrorMsg=Indique un número menor que {0}.
#XMSG
DeltaThreadErrorMsg=Introduzca un valor entre 1 y 10.
#XMSG
MaxPartitionErrorMsg=Introduzca un valor entre 1 <= x <= 2147483647. El valor predeterminado es 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Introduzca un número entero entre {0} y {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Utilice el factor de replicación del corredor
#XFLD
serializationFormat=Formato de serialización
#XFLD
compressionType=Tipo de compresión
#XFLD
schemaRegistry=Utilizar registro de esquema
#XFLD
subjectNameStrat=Estrategia de nombre de asunto
#XFLD
compatibilityType=Tipo de compatibilidad
#XFLD
confluentTopicName=Nombre de tema
#XFLD
confluentRecordName=Nombre de registro
#XFLD
confluentSubjectNamePreview=Vista previa de nombre de asunto
#XMSG
serializationChangeToastMsgUpdated2=El formato de serialización ha cambiado a JSON porque el registro de esquema no está activado. Para volver a cambiar el formato de serialización a AVRO, primero debe activar el registro de esquema.
#XBUT
confluentTopicNameInfo=El nombre del tema siempre se basa en el nombre del objeto de destino. Puede modificarlo cambiando el nombre del objeto de destino.
#XMSG
emptyRecordNameValidationHeaderMsg=Introduzca un nombre de registro.
#XMSG
emptyPartionHeader=Introduzca el número de particiones.
#XMSG
invalidPartitionsHeader=Introduzca un número válido de particiones.
#XMSG
invalidpartitionsDesc=Introduzca un número entre 1 y 200.000.
#XMSG
emptyrFactorHeader=Introduzca un factor de replicación.
#XMSG
invalidrFactorHeader=Introduzca un factor de replicación válido.
#XMSG
invalidrFactorDesc=Introduzca un número entre 1 y 32.767.
#XMSG
emptyRecordNameValidationDescMsg=Si se utiliza el formato de serialización "AVRO", solo se admiten los siguientes caracteres:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(guion bajo)
#XMSG
validRecordNameValidationHeaderMsg=Introduzca un nombre de registro válido.
#XMSG
validRecordNameValidationDescMsgUpdated=Debido a que se utiliza el formato de serialización "AVRO", el nombre del registro debe consistir únicamente en caracteres alfanuméricos (A-Z, a-z, 0-9) y guiones bajos (_). Debe comenzar con una letra o un guion bajo.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=La opción "Recuento de threads de objetos para cargas delta" se puede configurar tan pronto como uno o más objetos tengan el tipo de carga "Inicial y delta".
#XMSG
invalidTargetName=Nombre de columna no válido
#XMSG
invalidTargetNameDesc=El nombre de la columna de destino debe constar únicamente de caracteres alfanuméricos (A-Z, a-z, 0-9) y guiones bajos (_).
#XFLD
consumeOtherSchema=Consumir otras versiones del esquema
#XFLD
ignoreSchemamissmatch=Ignorar las diferencias del esquema
#XFLD
confleuntDatatruncation=Error al truncar los datos
#XFLD
isolationLevel=Nivel de aislamiento
#XFLD
confluentOffset=Punto de partida
#XFLD
signavioGroupDeltaFilesByText=Ninguno
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=No
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=No

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Proyecciones
#XBUT
txtAdd=Añadir
#XBUT
txtEdit=Editar
#XMSG
transformationText=Añada una proyección para configurar el filtro o la asignación.
#XMSG
primaryKeyRequiredText=Seleccione una clave primaria con Configurar esquema.
#XFLD
lblSettings=Opciones
#XFLD
lblTargetSetting={0} opciones de destino
#XMSG
@csvRF=Seleccione el archivo que contiene la definición de esquema que desea aplicar a todos los archivos de la carpeta.
#XFLD
lblSourceColumns=Columnas fuente
#XFLD
lblJsonStructure=Estructura JSON
#XFLD
lblSourceSetting={0}: opciones de origen
#XFLD
lblSourceSchemaSetting={0}: opciones de esquema de origen
#XBUT
messageSettings=Opciones de mensaje
#XFLD
lblPropertyTitle1=Propiedades de objeto
#XFLD
lblRFPropertyTitle=Propiedades de flujo de replicación
#XMSG
noDataTxt=No hay columnas para visualizar.
#XMSG
noTargetObjectText=No hay ningún objeto de destino seleccionado.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Columnas de destino
#XMSG
searchColumns=Buscar columnas
#XTOL
cdcColumnTooltip=Columna para captura delta
#XMSG
sourceNonDeltaSupportErrorUpdated=El objeto de origen no admite la captura delta.
#XMSG
targetCDCColumnAdded=Se han añadido 2 columnas de destino para la captura delta.
#XMSG
deltaPartitionEnable=Límite de threads de objeto para cargas delta añadido a las opciones de origen.
#XMSG
attributeMappingRemovalTxt=Eliminar asignaciones no válidas que no son compatibles con el nuevo objeto de destino.
#XMSG
targetCDCColumnRemoved=Se han quitado 2 columnas de destino utilizadas para la captura delta.
#XMSG
replicationLoadTypeChanged=Se ha modificado el tipo de carga a "Inicial y delta".
#XMSG
sourceHDLFLoadTypeError=Modifique el tipo de carga a "Inicial y delta".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Para replicar un objeto de una conexión de origen con el tipo Archivos de SAP HANA Cloud, data lake en SAP Datasphere, debe utilizar el tipo de carga "inicial y delta".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Active la captura delta.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Para replicar un objeto de una conexión de origen con el tipo Archivos de SAP HANA Cloud, data lake en SAP Datasphere, debe activar la captura delta.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Modifique el objeto de destino.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=El objeto de destino no se puede utilizar porque la captura delta usada está desactivada. Puede renombrar el objeto de destino (que permite un nuevo objeto con captura delta para crear) o asignarlo a un objeto existente con la captura delta activada.
#XMSG
deltaPartitionError=Introduzca un recuento de threads de objeto válido para cargas delta.
#XMSG
deltaPartitionErrorDescription=Introduzca un valor entre 1 y 10.
#XMSG
deltaPartitionEmptyError=Introduzca un recuento de threads de objeto para cargas delta.
#XFLD
@lblColumnDescription=Descripción
#XMSG
@lblColumnDescriptionText1=Para fines técnicos: gestión de registros duplicados debido a problemas durante la replicación de objetos de origen basados en ABAP que no tienen una clave principal.
#XFLD
storageType=Almacenamiento
#XFLD
skipUnmappedColLbl=Omitir columnas no asignadas
#XFLD
abapContentTypeLbl=Tipo de contenido
#XFLD
autoMergeForTargetLbl=Fusionar datos automáticamente
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=General
#XFLD
lblBusinessName=Nombre empresarial
#XFLD
lblTechnicalName=Nombre técnico
#XFLD
lblPackage=Paquete
#XFLD
statusPanel=Estado de ejecución
#XBTN: Schedule dropdown menu
SCHEDULE=Programa
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Editar programa
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Eliminar programa
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Crear programa
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Error al verificar la validación del programa
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=No se puede crear una programación porque el flujo de replicación se está desplegando actualmente.{0}Espere hasta que se haya desplegado el flujo de replicación.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Para los flujos de replicación que contienen objetos con el tipo de carga "inicial y delta" no se puede crear ningún programa.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Para los flujos de replicación que contienen objetos con el tipo de carga "inicial y delta/solo delta", no se puede crear ningún programa.
#XFLD : Scheduled popover
SCHEDULED=Programado
#XFLD
CREATE_REPLICATION_TEXT=Crear un flujo de replicación
#XFLD
EDIT_REPLICATION_TEXT=Editar un flujo de replicación
#XFLD
DELETE_REPLICATION_TEXT=Eliminar un flujo de replicación
#XFLD
REFRESH_FREQUENCY=Frecuencia
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=El flujo de replicación no se puede desplegar porque la programación existente{0} todavía no admite el tipo de carga "inicial y delta".{0}{0}Para desplegar el flujo de replicación, debe configurar los tipos de carga de todos los objetos{0} en "solo inicial". Como alternativa, puede eliminar la programación, desplegar el {0}flujo de replicación y, a continuación, iniciar una nueva ejecución. Esto da como resultado una ejecución sin {0}fin, que también admite objetos con el tipo de carga "inicial y delta".
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=El flujo de replicación no se puede desplegar porque la programación existente{0} todavía no admite el tipo de carga "Inicial y Delta/Solo delta".{0}{0}Para desplegar el flujo de replicación, debe configurar los tipos de carga de todos los objetos{0} en "solo inicial". Como alternativa, puede eliminar la programación, desplegar el {0}flujo de replicación y, a continuación, iniciar una nueva ejecución. Esto da como resultado una ejecución sin {0}fin, que también admite objetos con el tipo de carga "Inicial y Delta/Solo delta".
#XMSG
SCHEDULE_EXCEPTION=Error al obtener detalles del programa
#XFLD: Label for frequency column
everyLabel=Cada
#XFLD: Plural Recurrence text for Hour
hoursLabel=horas
#XFLD: Plural Recurrence text for Day
daysLabel=días
#XFLD: Plural Recurrence text for Month
monthsLabel=meses
#XFLD: Plural Recurrence text for Minutes
minutesLabel=minutos
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=No se ha podido obtener la información sobre la posibilidad de programación.
#XFLD :Paused field
PAUSED=En pausa
#XMSG
navToMonitoring=Abrir en monitor de flujos
#XFLD
statusLbl=Estado
#XFLD
lblLastRunExecuted=Inicio de última ejecución
#XFLD
lblLastExecuted=Última ejecución
#XFLD: Status text for Completed
statusCompleted=Concluido
#XFLD: Status text for Running
statusRunning=En ejecución
#XFLD: Status text for Failed
statusFailed=Erróneo
#XFLD: Status text for Stopped
statusStopped=Parado
#XFLD: Status text for Stopping
statusStopping=Parando
#XFLD: Status text for Active
statusActive=Activo
#XFLD: Status text for Paused
statusPaused=Interrumpido
#XFLD: Status text for not executed
lblNotExecuted=Aún no se ha ejecutado
#XFLD
messagesSettings=Opciones de mensajes
#XTOL
@validateModel=Mensajes de validación
#XTOL
@hierarchy=Jerarquía
#XTOL
@columnCount=Cantidad de columnas
#XMSG
VAL_PACKAGE_CHANGED=Ha asignado este objeto al paquete "{1}". Haga clic en Guardar para confirmar y validar esta modificación. Tenga en cuenta que la asignación a un paquete no se puede deshacer en este editor tras guardar.
#XMSG
MISSING_DEPENDENCY=Las dependencias del objeto "{0}" no se pueden resolver en el paquete "{1}".
#XFLD
deltaLoadInterval=Intervalo de carga delta
#XFLD
lblHour=Horas (0-24)
#XFLD
lblMinutes=Minutos (0-59)
#XMSG
maxHourOrMinErr=Introduzca un valor comprendido entre 0 y {0}
#XMSG
maxDeltaInterval=El valor máximo del intervalo de carga delta es de 24 horas.{0}Modifique el valor de minuto o de hora según corresponda.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Ruta del contenedor de destino
#XFLD
confluentSubjectName=Nombre de asunto
#XFLD
confluentSchemaVersion=Versión de esquema
#XFLD
confluentIncludeTechKeyUpdated=Incluir clave técnica
#XFLD
confluentOmitNonExpandedArrays=Omitir matrices no desplegadas
#XFLD
confluentExpandArrayOrMap=Desplegar matriz o mapa
#XCOL
confluentOperationMapping=Asignación de operaciones
#XCOL
confluentOpCode=Código de operación
#XFLD
confluentInsertOpCode=Insertar
#XFLD
confluentUpdateOpCode=Actualizar
#XFLD
confluentDeleteOpCode=Eliminar
#XFLD
expandArrayOrMapNotSelectedTxt=No seleccionado
#XFLD
confluentSwitchTxtYes=Sí
#XFLD
confluentSwitchTxtNo=No
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Error
#XTIT
executeWarning=Advertencia
#XMSG
executeunsavederror=Guarde el flujo de replicación antes de ejecutarlo.
#XMSG
executemodifiederror=Hay modificaciones sin guardar en el flujo de replicación. Guárdelo.
#XMSG
executeundeployederror=Debe desplegar el flujo de replicación para poder ejecutarlo.
#XMSG
executedeployingerror=Espere a que acabe el despliegue.
#XMSG
msgRunStarted=Se ha iniciado la ejecución
#XMSG
msgExecuteFail=No se ha podido ejecutar el flujo de replicación.
#XMSG
titleExecuteBusy=Espere.
#XMSG
msgExecuteBusy=Estamos preparando sus datos para ejecutar el flujo de replicación.
#XTIT
executeConfirmDialog=Advertencia
#XMSG
msgExecuteWithValidations=El flujo de replicación tiene errores de validación. Esto puede provocar errores al ejecutarlo.
#XMSG
msgRunDeployedVersion=Hay modificaciones para desplegar. Se ejecutará la última versión desplegada del flujo de replicación. ¿Desea continuar?
#XBUT
btnExecuteAnyway=Ejecutar de todas formas
#XBUT
btnExecuteClose=Cerrar
#XBUT
loaderClose=Cerrar
#XTIT
loaderTitle=Cargando
#XMSG
loaderText=Obteniendo detalles del servidor
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=No se puede iniciar el flujo de replicación para esta conexión de destino ajena a SAP
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=porque no hay volumen de salida disponible para este mes.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Un administrador puede incrementar los bloques de salida premium para este
#XMSG
premiumOutBoundRFAdminErrMsgPart2=arrendatario con el fin de que haya volumen de salida disponible para este mes.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Error
#XTIT
deployInfo=Información
#XMSG
deployCheckFailException=Se ha producido una excepción durante el despliegue
#XMSG
deployGBQFFDisabled=Actualmente no es posible desplegar flujos de replicación con una conexión de destino a Google BigQuery porque esta función se encuentra en mantenimiento.
#XMSG
deployKAFKAFFDisabled=Actualmente no es posible desplegar flujos de replicación con una conexión de destino a Apache Kafka porque esta función se encuentra en mantenimiento.
#XMSG
deployConfluentDisabled=Actualmente no es posible desplegar flujos de replicación con una conexión de destino a Confluent Kafka porque esta función se encuentra en mantenimiento.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Para los siguientes objetos de destino, los nombres de las tablas de captura delta ya se utilizan en otras tablas del repositorio: {0} Debe renombrar estos objetos de destino para asegurarse de que los nombres de las tablas de captura delta asociados sean unívocos para poder desplegar el flujo de replicación.
#XMSG
deployDWCSourceFFDisabled=Actualmente no es posible desplegar flujos de replicación que tengan SAP Datasphere como origen porque estamos realizando mantenimiento en esta función.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Actualmente no es posible desplegar flujos de replicación que contengan tablas locales activadas para delta como objetos de origen porque estamos realizando tareas de mantenimiento en esta función.
#XMSG
deployHDLFSourceFFDisabled=Actualmente no es posible desplegar flujos de replicación que tengan conexiones de origen con el tipo de conexión Archivos de SAP HANA Cloud, data lake, porque estamos llevando a cabo tareas de mantenimiento.
#XMSG
deployObjectStoreAsSourceFFDisabled=Actualmente no se pueden desplegar flujos de replicación que tengan como origen un proveedor de almacenamiento en la nube.
#XMSG
deployConfluentSourceFFDisabled=Actualmente no es posible desplegar flujos de replicación que tengan SAP Confluent Kafka como origen porque estamos realizando mantenimiento en esta función.
#XMSG
deployMaxDWCNewTableCrossed=No es posible "guardar y desplegar" a la vez los flujos de replicación de grandes dimensiones. Guarde primero el flujo de replicación y despliéguelo después.
#XMSG
deployInProgressInfo=El despliegue ya está en curso.
#XMSG
deploySourceObjectInUse=Los objetos de origen {0} ya se están utilizando en los flujos de replicación {1}.
#XMSG
deployTargetSourceObjectInUse=Los objetos de origen {0} ya se están utilizando en los flujos de replicación {1}. Los objetos de destino {2} ya se están utilizando en los flujos de replicación {3}.
#XMSG
deployReplicationFlowCheckError=Error al verificar el flujo de replicación: {0}
#XMSG
preDeployTargetObjectInUse=Los objetos de destino {0} ya se están utilizando en los flujos de replicación {1}, y no puede tener el mismo objeto de destino en dos flujos de replicación diferentes. Seleccione otro objeto de destino e inténtelo de nuevo.
#XMSG
runInProgressInfo=El flujo de replicación ya se está ejecutando.
#XMSG
deploySignavioTargetFFDisabled=Actualmente no es posible desplegar flujos de replicación que tengan SAP Signavio como destino porque estamos llevando a cabo tareas de mantenimiento en esta función.
#XMSG
deployHanaViewAsSourceFFDisabled=Actualmente no es posible desplegar flujos de replicación que tengan vistas como objetos de origen para la conexión de origen seleccionada. Inténtelo de nuevo más tarde.
#XMSG
deployMsOneLakeTargetFFDisabled=Actualmente no es posible desplegar flujos de replicación que tengan MS OneLake como destino porque estamos llevando a cabo tareas de mantenimiento en esta función.
#XMSG
deploySFTPTargetFFDisabled=Actualmente no es posible desplegar flujos de replicación que tengan SFTP como destino porque estamos llevando a cabo tareas de mantenimiento en esta función.
#XMSG
deploySFTPSourceFFDisabled=Actualmente no es posible desplegar flujos de replicación que tengan SFTP como origen porque estamos llevando a cabo tareas de mantenimiento en esta función.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Nombre técnico
#XFLD
businessNameInRenameTarget=Nombre empresarial
#XTOL
renametargetDialogTitle=Renombrar objeto de destino
#XBUT
targetRenameButton=Renombrar
#XBUT
targetRenameCancel=Cancelar
#XMSG
mandatoryTargetName=Debe introducir un nombre.
#XMSG
dwcSpecialChar=_(guion bajo) es el único carácter especial permitido.
#XMSG
dwcWithDot=El nombre de la tabla de destino puede contener letras latinas, números, guiones bajos (_) y puntos (.). El primer carácter debe ser una letra, un número o un guion bajo (pero no un punto).
#XMSG
nonDwcSpecialChar=Los caracteres especiales permitidos son: _(guion bajo) -(guion) .(punto)
#XMSG
firstUnderscorePattern=El nombre no puede comenzar con _ (guion bajo).

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: Ver sentencia SQL Crear tabla
#XMSG
sqlDialogMaxPKWarning=En Google BigQuery, se admite un máximo de 16 claves primarias y el objeto de origen tiene un número más largo. Por lo tanto, no se han definido claves primarias en esta sentencia.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Una o más columnas fuente tienen tipos de datos que no se pueden definir como claves primarias en Google BigQuery. Por lo tanto, no se han definido claves primarias en este caso. En Google BigQuery, solo los siguientes tipos de datos puden tener clave primaria: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Copiar y cerrar
#XBUT
closeDDL=Cerrar
#XMSG
copiedToClipboard=Copiado en el portapapeles


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=El objeto "{0}" no puede ser parte de una cadena de tareas porque no tiene un fin (ya que incluye objetos con el tipo de carga Inicial y Delta/Solo delta).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=El objeto "{0}" no puede ser parte de una cadena de tareas porque no tiene un fin (ya que incluye objetos con el tipo de carga Inicial y Delta).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=El objeto "{0}" no puede añadirse a la cadena de tareas.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Hay objetos de destino sin guardar. Vuelva a guardarlos.{0}{0} El comportamiento de esta función ha cambiado: antes, los objetos de destino solo se creaban en el entorno de destino cuando se desplegaba el flujo de replicación.{0} Ahora, los objetos se crean cuando se guarda el flujo de replicación. Su flujo de replicación se creó antes de este cambio y contiene objetos nuevos.{0} Vuelva a guardar el flujo de replicación antes de desplegarlo para que los objetos nuevos se incluyan correctamente.
#XMSG
confirmChangeContentTypeMessage=Está a punto de cambiar el tipo de contenido. Si lo hace, se eliminarán todas las proyecciones existentes.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Nombre de asunto
#XFLD
schemaDialogVersionName=Versión de esquema
#XFLD
includeTechKey=Incluir clave técnica
#XFLD
segementButtonFlat=Plano
#XFLD
segementButtonNested=Anidado
#XMSG
subjectNamePlaceholder=Buscar nombre de asunto

#XMSG
@EmailNotificationSuccess=Se ha guardado la configuración de las notificaciones por correo electrónico en tiempo de ejecución.

#XFLD
@RuntimeEmailNotification=Notificación por correo electrónico de tiempo de ejecución

#XBTN
@TXT_SAVE=Guardar


