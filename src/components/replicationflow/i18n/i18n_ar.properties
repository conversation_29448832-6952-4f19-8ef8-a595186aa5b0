#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=تدفق النسخ المتماثل

#XFLD: Edit Schema button text
editSchema=تحرير المخطط

#XTIT : Properties heading
configSchema=تكوين المخطط

#XFLD: save changed button text
applyChanges=تطبيق التغييرات


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=تحديد الاتصال المصدر
#XFLD
sourceContainernEmptyText=تحديد الحاوية
#XFLD
targetConnectionEmptyText=تحديد الاتصال المستهدف
#XFLD
targetContainernEmptyText=تحديد الحاوية
#XFLD
sourceSelectObjectText=تحديد الكائن المصدر
#XFLD
sourceObjectCount=الكائنات المصدر ({0})
#XFLD
targetObjectText=الكائنات المستهدفة
#XFLD
confluentBrowseContext=تحديد السياق
#XBUT
@retry=إعادة المحاولة
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=ترقية الوحدة المستضافة قيد التنفيذ.

#XTOL
browseSourceConnection=استعراض الاتصال المصدر
#XTOL
browseTargetConnection=استعراض الاتصال المستهدف
#XTOL
browseSourceContainer=استعراض الحاوية المصدر
#XTOL
browseAndAddSourceDataset=إضافة الكائنات المصدر
#XTOL
browseTargetContainer=استعراض الحاوية المستهدفة
#XTOL
browseTargetSetting=استعراض الإعدادات المستهدفة
#XTOL
browseSourceSetting=استعراض الإعدادات المصدر
#XTOL
sourceDatasetInfo=معلومات
#XTOL
sourceDatasetRemove=إزالة
#XTOL
mappingCount=يمثل هذا العدد الإجمالي لعمليات الربط/التعبيرات التي لا تستند إلى الاسم.
#XTOL
filterCount=يمثل هذا العدد الإجمالي لشروط التصفية.
#XTOL
loading=جارٍ التحميل...
#XCOL
deltaCapture=التقاط الفرق
#XCOL
deltaCaptureTableName=جدول التقاط الفرق
#XCOL
loadType=نوع التحميل
#XCOL
deleteAllBeforeLoading=حذف الكل قبل التحميل
#XCOL
transformationsTab=التقديرات
#XCOL
settingsTab=الإعدادات

#XBUT
renameTargetObjectBtn=إعادة تسمية الكائن المستهدف
#XBUT
mapToExistingTargetObjectBtn=ربط بالكائن المستهدف الموجود
#XBUT
changeContainerPathBtn=تغيير مسار الحاوية
#XBUT
viewSQLDDLUpdated=عرض SQL - إنشاء عبارة الجدول
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=لا يدعم الكائن المصدر التقاط الفرق، لكن الكائن المستهدف المحدد به خيار التقاط الفرق ممكّنًا.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=لا يمكن استخدام الكائن المستهدف لأن تحديد الفرق ممكَّن،{0}بينما لا يدعم الكائن المصدر قدرة الفرق.{1}يمكنك تحديد كائن مستهدف آخر لا يدعم تحديد الفرق.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=يوجد كائن مستهدف بهذا الاسم بالفعل. ومع ذلك، لا يمكن استخدامه {0}لأنه تم تمكين تحديد الفرق، {0}بينما لا يدعم الكائن المصدر قدرة الفرق.{1}يمكنك إما إدخال اسم كائن مستهدف موجود {0}لا يدعم التقاط الفرق، أو إدخال اسم غير موجود بعد.
#XBUT
copySQLDDLUpdated=نسخ SQL - إنشاء عبارة الجدول
#XMSG
targetObjExistingNoCDCColumnUpdated=يجب أن تتضمن الجداول الموجودة في Google BigQuery الأعمدة التالية لتسجيل بيانات التغيير (CDC):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=الكائنات المصدر التالية غير مدعومة لأنها لا تحتوي على مفتاح أساسي، أو تستخدم اتصالاً لا يستوفي شروط استرجاع المفتاح الأساسي:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=يُرجى الرجوع إلى SAP KBA 3531135 للعثور على حل ممكن.
#XLST: load type list values
initial=أولي فقط
@emailUpdateError=حدث خطأ في تحديث قائمة إشعارات البريد الإلكتروني

#XLST
initialDelta=أولي وفرق

#XLST
deltaOnly=الفرق فقط
#XMSG
confluentDeltaLoadTypeInfo=بالنسبة إلى مصدر Kafka Contreing، يتم دعم نوع التحميل "أولي وفرق" فقط.
#XMSG
confirmRemoveReplicationObject=هل تريد بالتأكيد حذف النسخ المتماثل؟
#XMSG
confirmRemoveReplicationTaskPrompt=سيؤدي هذا الإجراء إلى حذف عمليات النسخ المتماثل الموجودة. هل تريد الاستمرار؟
#XMSG
confirmTargetConnectionChangePrompt=سيؤدي هذا الإجراء إلى إعادة تعيين الاتصال المستهدف والحاوية المستهدفة وحذف جميع الكائنات المستهدفة. هل تريد الاستمرار؟
#XMSG
confirmTargetContainerChangePrompt=سيؤدي هذا الإجراء إلى إعادة تعيين الحاوية المستهدفة وحذف جميع الكائنات المستهدفة الموجودة. هل تريد الاستمرار؟
#XMSG
confirmRemoveTransformObject=هل تريد بالتأكيد حذف التقدير {0}؟
#XMSG
ErrorMsgContainerChange=حدث خطأ أثناء تغيير مسار الحاوية.
#XMSG
infoForUnsupportedDatasetNoKeys=الكائنات المصدر التالية غير مدعومة لأنها لا تحتوي على مفتاح أساسي:
#XMSG
infoForUnsupportedDatasetView=الكائنات المصدر التالية من النوع طرق العرض غير مدعومة:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=الكائن المصدر التالي غير مدعوم لأنه عرض SQL يحتوي على معامِلات الإدخال:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=الكائنات المصدر التالية غير مدعومة لأن الاستخراج معطل لها:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=بالنسبة لاتصالات Confluent، فإن تنسيقات إنشاء التسلسل المسموح بها فقط هي AVRO وJSON. الكائنات التالية غير مدعومة لأنها تستخدم تنسيق إنشاء تسلسل مختلفًا:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=يتعذر استدعاء المخطط للكائنات التالية. يُرجى تحديد السياق المناسب أو التحقق من صحة تكوين سجل المخطط
#XTOL: warning dialog header on deleting replication task
deleteHeader=حذف
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=إعداد حذف الكل قبل التحميل غير مدعوم في Google BigQuery.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=إعداد حذف الكل قبل يحذف ويعيد إنشاء الكائن (الموضوع) قبل كل عملية نسخ متماثل. كما يحذف جميع الرسائل المعينة.
#XTOL
DeleteAllBeforeLoadingLTFInfo=إعداد حذف الكل قبل غير مدعوم لهذا النوع المستهدف.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=الاسم التقني
#XCOL
connBusinessName=الاسم التجاري
#XCOL
connDescriptionName=الوصف
#XCOL
connType=النوع
#XMSG
connTblNoDataFoundtxt=لم يتم العثور على أي اتصالات
#XMSG
connectionError=حدث خطأ أثناء استدعاء الاتصالات.
#XMSG
connectionCombinationUnsupportedErrorTitle=مجموعة الاتصالات غير مدعومة
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=النسخ المتماثل من {0} إلى {1} غير مدعوم حاليًا.
#XMSG
invalidTargetforSourceHDLFErrorTitle=مجموعة أنواع الاتصالات غير مدعومة
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=النسخ المتماثل من اتصال بملفات نوع الاتصال مستودع البيانات لقاعدة بيانات SAP HANA Cloud إلى {0} غير مدعوم. يمكنك النسخ المتماثل فقط إلى SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=تحديد
#XBUT
containerCancelBtn=إلغاء
#XTOL
containerSelectTooltip=تحديد
#XTOL
containerCancelTooltip=إلغاء
#XMSG
containerContainerPathPlcHold=مسار الحاوية
#XFLD
containerContainertxt=حاوية
#XFLD
confluentContainerContainertxt=السياق
#XMSG
infoMessageForSLTSelection=مسموح فقط بـ SLT/معرف النقل الشامل كحاوية. حدد معرّف نقل شامل ضمن SLT (إذا كان متوفرًا) وانقر فوق إرسال.
#XMSG
msgFetchContainerFail=حدث خطأ أثناء استدعاء بيانات الحاوية.
#XMSG
infoMessageForSLTHidden=لا يدعم هذا الاتصال مجلدات SLT، لذلك لا تظهر في القائمة أدناه.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=حدد حاوية تحتوي على مجلدات فرعية بها.
#XMSG
sftpIncludeSubFolderText=خطأ
#XMSG
sftpIncludeSubFolderTextNew=لا

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(لا يوجد ربط عامل تصفية حتى الآن)
#XMSG
failToFetchRemoteMetadata=حدث خطأ أثناء استدعاء بيانات التعريف.
#XMSG
failToFetchData=حدث خطأ أثناء استدعاء الهدف الموجود.
#XCOL
@loadType=نوع التحميل
#XCOL
@deleteAllBeforeLoading=حذف الكل قبل التحميل

#XMSG
@loading=جارٍ التحميل...
#XFLD
@selectSourceObjects=تحديد الكائنات المصدر
#XMSG
@exceedLimit=لا يمكنك استيراد أكثر من {0} من الكائنات في المرة الواحدة. يرجى إلغاء تحديد {1} من الكائنات على الأقل.
#XFLD
@objects=الكائنات
#XBUT
@ok=موافق
#XBUT
@cancel=إلغاء
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=التالي
#XBUT
btnAddSelection=إضافة التحديد
#XTOL
@remoteFromSelection=إزالة من التحديد
#XMSG
@searchInForSearchField=بحث في {0}

#XCOL
@name=الاسم التقني
#XCOL
@type=النوع
#XCOL
@location=الموقع
#XCOL
@label=الاسم التجاري
#XCOL
@status=الحالة

#XFLD
@searchIn=بحث في:
#XBUT
@available=متوفر
#XBUT
@selection=تحديد

#XFLD
@noSourceSubFolder=جداول وطرق عرض
#XMSG
@alreadyAdded=موجود بالفعل في المخطط
#XMSG
@askForFilter=يوجد أكثر من {0} بنود. يُرجى إدخال سلسلة تصفية لتضييق عدد البنود.
#XFLD: success label
lblSuccess=نجاح
#XFLD: ready label
lblReady=جاهز
#XFLD: failure label
lblFailed=فشل
#XFLD: fetching status label
lblFetchingDetail=تفاصيل الاستدعاء

#XMSG Place holder text for tree filter control
filterPlaceHolder=اكتب نصًا لتصفية كائنات المستوى الأعلى
#XMSG Place holder text for server search control
serverSearchPlaceholder=اكتب واضغط على إدخال للبحث
#XMSG
@deployObjects=جارٍ استيراد كائنات {0}...
#XMSG
@deployObjectsStatus=عدد الكائنات التي تم استيرادها: {0}. عدد الكائنات التي تعذر استيرادها: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=فشل فتح مستعرض المستودع المحلي.
#XMSG
@openRemoteSourceBrowserError=فشل استدعاء الكائنات المصدر.
#XMSG
@openRemoteTargetBrowserError=فشل استدعاء الكائنات المستهدفة.
#XMSG
@validatingTargetsError=حدث خطأ أثناء التحقق من صحة الأهداف.
#XMSG
@waitingToImport=جاهز للاستيراد

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=تم تجاوز الحد الأقصى لعدد الكائنات. حدد 500 كائن بحد أقصى لتدفق نسخ متماثل واحد.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=الاسم التقني
#XFLD
sourceObjectBusinessName=الاسم التجاري
#XFLD
sourceNoColumns=عدد الأعمدة
#XFLD
containerLbl=حاوية

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=يجب تحديد اتصال مصدر لتدفق النسخ المتماثل.
#XMSG
validationSourceContainerNonExist=يجب تحديد حاوية للاتصال المصدر.
#XMSG
validationTargetNonExist=يجب تحديد اتصال مستهدف لتدفق النسخ المتماثل.
#XMSG
validationTargetContainerNonExist=يجب تحديد حاوية للاتصال المستهدف.
#XMSG
validationTruncateDisabledForObjectTitle=النسخ المتماثل إلى مخازن الكائنات.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=لا يتم النسخ المتماثل في التخزين السحابي إلا في حالة تعيين خيار حذف الكل قبل التحميل أو عدم وجود الكائن المستهدف في الهدف.{0}{0} للاستمرار في تمكين النسخ المتماثل للكائنات التي لم يتم تعيين خيار حذف الكل قبل التحميل لها، تأكد من عدم وجود الكائن المستهدف في النظام قبل تشغيل تدفق النسخ المتماثل.
#XMSG
validationTaskNonExist=يجب أن يكون لديك نسخ متماثل واحد على الأقل في تدفق النسخ المتماثل.
#XMSG
validationTaskTargetMissing=يجب أن يكون لديك هدف للنسخ المتماثل مع المصدر: {0}
#XMSG
validationTaskTargetIsSAC=الهدف المحدد هو نموذج SAC: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=الهدف المحدد ليس جدولًا محليًا مدعومًا: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=يوجد كائن بهذا الاسم بالفعل في الهدف. ومع ذلك، لا يمكن استخدام هذا الكائن ككائن مستهدف لتدفق نسخ متماثل إلى المستودع المحلي، لأنه ليس جدولاً محليًا.
#XMSG
validateSourceTargetSystemDifference=يجب تحديد مجموعات مختلفة من الاتصالات والحاويات المصدر والمستهدفة لتدفق النسخ المتماثل.
#XMSG
validateDuplicateSources=يحتوي نسخ متماثل واحد أو أكثر على أسماء كائنات مصدر مكررة: {0}.
#XMSG
validateDuplicateTargets=يحتوي نسخ متماثل واحد أو أكثر على أسماء كائنات مستهدفة مكررة: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=الكائن المصدر {0} لا يدعم التقاط الفرق، بينما يدعمه الكائن المستهدف {1}. يجب عليك إزالة النسخ المتماثل.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=يجب أن تحدد نوع التحميل "أولي والفرق" للنسخ المتماثل باسم الكائن المستهدف {0}.
#XMSG
validationAutoRenameTarget=تمت إعادة تسمية الأعمدة المستهدفة.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=تمت إضافة تقدير تلقائي وتمت إعادة تسمية الأعمدة المستهدفة التالية للسماح بالنسخ المتماثل إلى الهدف:{1}{1} {0} {1}{1}يرجع هذا إلى أحد الأسباب التالية:{1}{1}{2} الحروف غير المدعومة{1}{2} البادئة المحجوزة
#XMSG
validationAutoRenameTargetDescriptionUpdated=تمت إضافة التقدير التلقائي، وتمت إعادة تسمية الأعمدة المستهدفة التالية للسماح بالنسخ المتماثل إلى Google BigQuery:{1}{1} {0} {1}{1}هذا يرجع إلى أحد الأسباب التالية:{1}{1}{2} اسم العمود المحجوز{1}{2}حروف غير مدعومة{1}{2}بادئة محجوزة
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=تمت إضافة تقدير تلقائي وتمت إعادة تسمية الأعمدة المستهدفة التالية للسماح بعمليات النسخ المتماثل لـ Confluent:{1}{1} {0} {1}{1}هذا يرجع إلى أحد الأسباب التالية:{1}{1}{2} اسم العمود المحجوز{1}{2}حروف غير مدعومة{1}{2}بادئة محجوزة
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=تمت إضافة تقدير تلقائي وتمت إعادة تسمية الأعمدة المستهدفة التالية للسماح بعمليات النسخ المتماثل إلى الهدف:{1}{1} {0} {1}{1}هذا يرجع إلى أحد الأسباب التالية:{1}{1}{2} اسم العمود المحجوز{1}{2} الحروف غير المدعومة{1}{2} البادئة المحجوزة
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=تمت إعادة تسمية الكائن المستهدف.
#XMSG
autoRenameInfoDesc=تمت إعادة تسمية الكائن المستهدف لأنه يحتوي على حروف غير مدعومة. ولا يتم دعم سوى الأحرف التالية فقط:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(نقطة){0}{1}_(شرطة سفلية){0}{1}-(شرطة)
#XMSG
validationAutoTargetTypeConversion=تم تغيير أنواع البيانات المستهدفة.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=بالنسبة إلى الأعمدة المستهدفة التالية، تم تغيير أنواع البيانات المستهدفة، لأنه في Google BigQuery، لا يتم دعم أنواع البيانات المصدر:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=بالنسبة إلى الأعمدة المستهدفة التالية، تم تغيير أنواع البيانات المستهدفة لأن أنواع البيانات المصدر غير مدعومة في الاتصال المستهدف:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=قم بتقصير أسماء الأعمدة المستهدفة.
#XMSG
validationMaxCharLengthGBQTargetDescription=في Google BigQuery، يمكن أن تحتوي أسماء الأعمدة على 300 حرف كحد أقصى. استخدم تقديرًا لتقصير أسماء الأعمدة المستهدفة التالية:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=لن يتم إنشاء المفاتيح الأساسية.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=في Google BigQuery، يتم دعم 16 مفتاحًا أساسيًا كحد أقصى، ولكن الكائن المصدر يحتوي على عدد أكبر من المفاتيح الأساسية. لن يتم إنشاء أي من المفاتيح الأساسية في الكائن المستهدف.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=يحتوي عمود مصدر واحد أو عدة أعمدة على أنواع بيانات لا يمكن تعريفها كمفاتيح أساسية في Google BigQuery. لن يتم إنشاء أي من المفاتيح الأساسية في الكائن المستهدف.{0}{0}أنواع البيانات المستهدفة التالية متوافقة مع أنواع بيانات Google BigQuery التي يمكن تحديد مفتاح أساسي لها: {0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=حدد عمودًا واحدًا أو أكثر كمفتاح أساسي.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=يجب أن تحدد عمودًا واحدًا أو أكثر كمفتاح أساسي، استخدم مربع حوار المخطط لتفعل ذلك.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=حدد عمودًا واحدًا أو أكثر كمفتاح أساسي.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=يجب عليك تحديد عمود واحد أو أكثر كمفتاح أساسي يطابق قيود المفتاح الأساسي للكائن المصدر الخاص بك. انتقل إلى تكوين المخطط في خصائص الكائن المصدر لديك للقيام بذلك.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=أدخل أقصى قيمة تقسيم صالحة.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=يجب أن يكون الحد الأقصى لقيمة التقسيم ≥ 1 و ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=حدد عمودًا واحدًا أو أكثر كمفتاح أساسي.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=لنسخ كائن نسخًا متماثلًا، يجب عليك تحديد عمود مستهدف واحد أو أكثر كمفتاح أساسي. واستخدم تقديرًا للقيام بذلك.
#XMSG
validateHDLFNoPKExistingDatasetError=حدد عمودًا واحدًا أو أكثر كمفتاح أساسي.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=لنسخ البيانات نسخًا متماثلًا إلى كائن مستهدف موجود، يجب أن يحتوي على عمود واحد أو أكثر يتم تحديده كمفتاح أساسي.  {0}لديك الخيارات التالية لتحديد عمود واحد أو أكثر كمفتاح أساسي: {0}{1}استخدم محرِّر الجدول المحلي لتغيير الكائن المستهدف الموجود. ثم أعد تحميل تدفق النسخ المتماثل.{0}{1}أعد تسمية الكائن المستهدف في تدفق النسخ المتماثل. سيؤدي هذا إلى إنشاء كائن جديد بمجرد بدء التشغيل. بعد إعادة التسمية، يمكنك تحديد عمود واحد أو أكثر كمفتاح أساسي في العرض.{0}{1}ربط الكائن بكائن مستهدف آخر موجود والذي يتم فيه بالفعل تحديد عمود واحد أو أكثر على أنه المفتاح الأساسي.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=الهدف المحدد موجود بالفعل في المستودع: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=إن أسماء جداول التقاط الفرق مستخدمة بالفعل بواسطة جداول أخرى في المستودع: {0} يجب إعادة تسمية هذه الكائنات المستهدفة للتأكد من أن أسماء جداول التقاط الفرق المرتبطة فريدة قبل أن تتمكن من حفظ تدفق النسخ المتماثل.
#XMSG
validateConfluentEmptySchema=تحديد المخطط
#XMSG
validateConfluentEmptySchemaDescUpdated=لا يحتوي الجدول المصدر على مخطط. اختر تكوين المخطط لتحديد مخطط
#XMSG
validationCSVEncoding=ترميز القيم المفصولة بفواصل (CSV) غير صالح
#XMSG
validationCSVEncodingDescription=ترميز القيم المفصولة بفواصل (CSV) للمهمة غير صالح.
#XMSG
validateConfluentEmptySchema=حدد نوع بيانات مستهدفًا متوافقًا
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=حدد نوع بيانات مستهدفًا متوافقًا
#XMSG
globalValidateTargetDataTypeDesc=حدث خطأ في عمليات ربط الأعمدة. انتقل إلى "العرض المركَّز" وتأكد من ربط جميع الأعمدة المصدر بعمود فريد، بعمود يحتوي على نوع بيانات متوافق، وأن جميع التعبيرات المحددة صالحة.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=قم بتكرار أسماء الأعمدة.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=أسماء الأعمدة المكررة غير مدعومة. استخدم مربع حوار التقدير لإصلاحها. تحتوي الكائنات المستهدفة التالية على أسماء أعمدة مكررة: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=قم بتكرار أسماء الأعمدة.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=أسماء الأعمدة المكررة غير مدعومة. تحتوي الكائنات المستهدفة التالية على أسماء أعمدة مكررة: {0}.
#XMSG
deltaOnlyLoadTypeTittle=قد يكون هناك حالات عدم اتساق في البيانات.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=لن يأخذ نوع تحميل الفرق فقط في الاعتبار التغييرات التي تم إجراؤها في المصدر بين آخر عملية حفظ والتشغيل التالي.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=تغيير نوع الحمل إلى "أولي".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=النسخ المتماثل للكائنات المستندة إلى ABAP التي لا تحتوي على مفتاح أساسي يكون ممكنًا فقط لنوع التحميل "الأولي فقط".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=تعطيل التقاط الفرق.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=لنسخ كائن نسخًا متماثلاً ليس به مفتاح أساسي باستخدام نوع الاتصال المصدر ABAP، يجب عليك تعطيل التقاط الفرق لهذا الجدول.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=قم بتغيير الكائن المستهدف.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=لا يمكن استخدام الكائن المستهدف بسبب تمكين التقاط الفرق. ويمكنك إما إعادة تسمية الكائن المستهدف ثم إيقاف تشغيل التقاط الفرق للكائن (معاد تسميته) الجديد أو ربط الكائن المصدر بالكائن المستهدف الذي تم تعطيل له التقاط الفرق.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=قم بتغيير الكائن المستهدف.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=يتعذر استخدام الكائن المستهدف لأنه ليس به العمود __load_package_id التقني. يمكنك إعادة تسمية الكائن المستهدف باستخدام اسم غير موجود بعد. يقوم النظام بعد ذلك بإنشاء كائن جديد له نفس تعريف الكائن المصدر ويحتوي على العمود التقني. وبدلاً من ذلك، يمكنك ربط الكائن المستهدف إلى كائن موجود يحتوي على العمود التقني المطلوب (__load_package_id)
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=قم بتغيير الكائن المستهدف.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=يتعذر استخدام الكائن المستهدف لأنه ليس به العمود __load_record_id التقني. يمكنك إعادة تسمية الكائن المستهدف باستخدام اسم غير موجود بعد. يقوم النظام بعد ذلك بإنشاء كائن جديد له نفس تعريف الكائن المصدر ويحتوي على العمود التقني. وبدلاً من ذلك، يمكنك ربط الكائن المستهدف إلى كائن موجود يحتوي على العمود التقني المطلوب (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=قم بتغيير الكائن المستهدف.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=يتعذر استخدام الكائن المستهدف لأن نوع بيانات العمود __load_record_id التقني الخاص به ليس "string(44)". يمكنك إعادة تسمية الكائن المستهدف باستخدام اسم غير موجود بعد. يقوم النظام بعد ذلك بإنشاء كائن جديد له نفس تعريف الكائن المصدر وبالتالي نوع البيانات الصحيح. وبدلاً من ذلك، يمكنك ربط الكائن المستهدف إلى كائن موجود يحتوي على العمود التقني المطلوب (__load_record_id) بنوع البيانات الصحيح. 
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=قم بتغيير الكائن المستهدف.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=يتعذر استخدام الكائن المستهدف لأنه يحتوي على مفتاح أساسي، بينما لا يحتوي الكائن المصدر على أي مفتاح. يمكنك إعادة تسمية الكائن المستهدف باستخدام اسم غير موجود بعد. يقوم النظام بعد ذلك بإنشاء كائن جديد له نفس تعريف الكائن المصدر وبالتالي لا يوجد به مفتاح أساسي. وبدلاً من ذلك، يمكنك ربط الكائن المستهدف إلى كائن موجود يحتوي على العمود التقني المطلوب (__load_package_id) ولا يحتوي على مفتاح أساسي.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=قم بتغيير الكائن المستهدف.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=يتعذر استخدام الكائن المستهدف لأنه يحتوي على مفتاح أساسي، بينما لا يحتوي الكائن المصدر على أي مفتاح. يمكنك إعادة تسمية الكائن المستهدف باستخدام اسم غير موجود بعد. يقوم النظام بعد ذلك بإنشاء كائن جديد له نفس تعريف الكائن المصدر وبالتالي لا يوجد به مفتاح أساسي. وبدلاً من ذلك، يمكنك ربط الكائن المستهدف إلى كائن موجود يحتوي على العمود التقني المطلوب (__load_record_id) ولا يحتوي على مفتاح أساسي.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=قم بتغيير الكائن المستهدف.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=يتعذر استخدام الكائن المستهدف لأن نوع بيانات العمود column __load_package_id التقني الخاص به ليس "binary(>=256)". يمكنك إعادة تسمية الكائن المستهدف باستخدام اسم غير موجود بعد. يقوم النظام بعد ذلك بإنشاء كائن جديد له نفس تعريف الكائن المصدر وبالتالي نوع البيانات الصحيح. وبدلاً من ذلك، يمكنك ربط الكائن المستهدف إلى كائن موجود يحتوي على العمود التقني المطلوب (__load_package_id) بنوع البيانات الصحيح.
#XMSG
validationAutoRenameTargetDPID=تمت إعادة تسمية الأعمدة المستهدفة.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=قم بإزالة الكائن المصدر.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=لا يحتوي الكائن المصدر على عمود مفتاحي، وهذا غير مدعوم في هذا السياق.
#XMSG
validationAutoRenameTargetDPIDDescription=تمت إضافة التقدير التلقائي، وتمت إعادة تسمية الأعمدة المستهدفة التالية للسماح بالنسخ المتماثل من مصدر ABAP بدون مفاتيح:{1}{1} {0} {1}{1}هذا يرجع إلى أحد الأسباب التالية:{1}{1}{2} اسم العمود المحجوز{1}{2}حروف غير مدعومة{1}{2}بادئة محجوزة
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=نسخ متماثل إلى {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=حفظ تدفقات النسخ المتماثل التي تحتوي على {0} ونشرها لأن هدفها غير ممكن حاليًا لأننا نقوم بإجراء معالجة على هذه الوظيفة.
#XMSG
TargetColumnSkippedLTF=تم تخطي العمود المستهدف.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=تم تخطي العمود المستهدف بسبب عدم دعم نوع البيانات. {0}{1}
#XMSG
validatePKTimeColumnLTF1=عمود الوقت كمفتاح أساسي.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=يحتوي الكائن المصدر على عمود وقت كمفتاح أساسي، وهو غير مدعوم في هذا السياق.
#XMSG
validateNoPKInLTFTarget=المفتاح الأساسي مفقود.
#XMSG
validateNoPKInLTFTargetDescription=لم يتم تحديد المفتاح الأساسي في الهدف، وهو غير مدعوم في هذا السياق.
#XMSG
validateABAPClusterTableLTF=جدول تجمعات ABAP.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=الكائن المصدر هو جدول تجمعات ABAP، وهذا غير مدعوم في هذا السياق.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=يبدو أنك لم تقم بإضافة أي بيانات حتى الآن.
#YINS
welcomeText2=لبدء تدفق النسخ المتماثل، حدد اتصالاً وكائن مصدر على الجانب الأيمن.

#XBUT
wizStep1=تحديد الاتصال المصدر
#XBUT
wizStep2=تحديد الحاوية المصدر
#XBUT
wizStep3=إضافة الكائنات المصدر

#XMSG
limitDataset=تم الوصول إلى الحد الأقصى لعدد الكائنات. قم بإزالة الكائنات الموجودة لإضافة كائنات جديدة، أو إنشاء تدفق نسخ متماثل جديد.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=لا يمكن بدء تدفق النسخ المتماثل إلى هذا الاتصال المستهدف بخلاف SAP نظرًا لعدم توفر حجم خارجي لهذا الشهر.
#XMSG
premiumOutBoundRFAdminWarningMsg=يمكن للمسؤول زيادة المجموعات الخارجية المميزة لهذا الوحدة المستضافة، مما يجعل الحجم الصادر متوفرًا لهذا الشهر.
#XMSG
messageForToastForDPIDColumn2=تمت إضافة عمود جديد إلى الهدف الخاص بـ {0} من الكائنات - وهو ضروري للتعامل مع السجلات المكررة فيما يتعلق بالكائنات المصدر المستندة إلى ABAP والتي لا تحتوي على مفتاح أساسي.
#XMSG
PremiumInboundWarningMessage=استنادًا إلى عدد تدفقات النسخ المتماثل وحجم البيانات المطلوب نسخه نسخًا متماثلًا، قد تتجاوز {0}موارد SAP HANA المطلوبة لنسخ البيانات نسخًا متماثلاً عبر "{1}" السعة المتوفرة لوحدتك المستضافة.
#XMSG
PremiumInboundWarningMsg=استنادًا إلى عدد تدفقات النسخ المتماثل وحجم البيانات المطلوب نسخه نسخًا متماثلًا، قد تتجاوز {0}موارد SAP HANA المطلوبة لنسخ البيانات نسخًا متماثلاً عبر "{1}" السعة المتوفرة لوحدتك المستضافة.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=أدخل اسم تقدير.
#XMSG
emptyTargetColumn=أدخل اسم العمود المستهدف.
#XMSG
emptyTargetColumnBusinessName=أدخل الاسم التجاري للعمود المستهدف.
#XMSG
invalidTransformName=أدخل اسم تقدير.
#XMSG
uniqueColumnName=قم بإعادة تسمية العمود المستهدف.
#XMSG
copySourceColumnLbl=نسخ الأعمدة من الكائن المصدر
#XMSG
renameWarning=تأكد من اختيار اسم فريد عند إعادة تسمية الجدول المستهدف. إذا كان الجدول بالاسم الجديد موجودًا بالفعل في المساحة، فسيتم استخدام تعريف ذلك الجدول.

#XMSG
uniqueColumnBusinessName=إعادة تسمية الاسم التجاري للعمود المستهدف.
#XMSG
uniqueSourceMapping=حدد عمودًا مصدرًا مختلفًا.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=العمود  المصدر {0} مستخدم بالفعل بواسطة الأعمدة المستهدفة التالية:{1}{1}{2}{1}{1} بالنسبة لهذا العمود المستهدف أو للأعمدة المستهدفة الأخرى، حدد عمودًا مصدرًا غير مستخدم بالفعل لحفظ التقدير.
#XMSG
uniqueColumnNameDescription=اسم العمود المستهدف الذي أدخلته موجود بالفعل. لتتمكن من حفظ التقدير، تحتاج إلى إدخال اسم عمود فريد.
#XMSG
uniqueColumnBusinessNameDesc=الاسم التجاري للعمود المستهدف موجود بالفعل. لحفظ التقدير، يجب عليك إدخال اسم تجاري فريد للعمود.
#XMSG
emptySource=حدد العمود المصدر أو أدخل قيمة ثابتة.
#XMSG
emptySourceDescription=لإنشاء إدخال ربط صالح، تحتاج إلى تحديد عمود مصدر أو إدخال قيمة ثابتة.
#XMSG
emptyExpression=حدد الربط.
#XMSG
emptyExpressionDescription1=حدد إما العمود المصدر الذي تريد ربط العمود المستهدف به، أو حدد خانة الاختيار في العمود {0} الوظائف / القيم الثابتة {1}. {2} {2} يتم إدخال الوظائف تلقائيًا وفقًا لنوع البيانات المستهدف. يمكن إدخال القيم الثابتة يدويًا.
#XMSG
numberExpressionErr=أدخل رقمًا.
#XMSG
numberExpressionErrDescription=لقد حددت نوع بيانات رقمي. هذا يعني أنه يمكنك فقط إدخال الأرقام، بالإضافة إلى النقطة العشرية إن أمكن. لا تستخدم علامات الاقتباس الفردية.
#XMSG
invalidLength=أدخل قيمة طول صالحة.
#XMSG
invalidLengthDescription=يجب أن يكون طول نوع البيانات مساويًا لطول العمود المصدر أو أكبر منه ويمكن أن يتراوح بين 1 و5000.
#XMSG
invalidMappedLength=أدخل قيمة طول صالحة.
#XMSG
invalidMappedLengthDescription=يجب أن يكون طول نوع البيانات مساويًا لطول العمود المصدر {0} أو أكبر منه ويمكن أن يتراوح بين 1 و5000.
#XMSG
invalidPrecision=أدخل قيمة دقة صالحة.
#XMSG
invalidPrecisionDescription=تحدد الدقة إجمالي عدد الأرقام. يحدد المقياس عدد الأرقام بعد النقطة العشرية ويمكن أن يكون بين 0 والدقة.{0}{0} أمثلة: {0}{1} الدقة 6، المقياس 2 يتوافق مع أرقام مثل 1234.56.{0}{1} الدقة 6، المقياس 6 يتوافق مع أرقام مثل 0.123546.{0} {0} يجب أن تكون دقة ومقياس الهدف متوافقين مع الدقة والمقياس للمصدر حتى تتلاءم جميع الأرقام من المصدر مع الحقل المستهدف. على سبيل المثال، إذا كان لديك الدقة 6 والمقياس 2 في المصدر (وبالتبعية أرقام بخلاف 0 قبل النقطة العشرية)، فلا يمكنك الحصول على الدقة 6 والمقياس 6 في الهدف.
#XMSG
invalidPrimaryKey=أدخِل مفتاحًا أساسيًا واحدًا على الأقل.
#XMSG
invalidPrimaryKeyDescription=لم يتم تحديد المفتاح الأساسي لهذا المخطط.
#XMSG
invalidMappedPrecision=أدخل قيمة دقة صالحة.
#XMSG
invalidMappedPrecisionDescription1=تحدد الدقة إجمالي عدد الأرقام. يحدد المقياس عدد الأرقام بعد النقطة العشرية ويمكن أن يكون بين 0 والدقة.{0}{0} أمثلة:{0}{1} الدقة 6، المقياس 2 يتوافق مع أرقام مثل 1234.56.{0}{1} الدقة 6، المقياس 6 يتوافق مع أرقام مثل 0.123546.{0}{0}يجب أن تكون دقة نوع البيانات مساوية لدقة المصدر أو أكبر منها ({2}).
#XMSG
invalidScale=أدخل قيمة مقياس صالحة.
#XMSG
invalidScaleDescription=تحدد الدقة إجمالي عدد الأرقام. يحدد المقياس عدد الأرقام بعد النقطة العشرية ويمكن أن يكون بين 0 والدقة.{0}{0} أمثلة: {0}{1} الدقة 6، المقياس 2 يتوافق مع أرقام مثل 1234.56.{0}{1} الدقة 6، المقياس 6 يتوافق مع أرقام مثل 0.123546.{0} {0} يجب أن تكون دقة ومقياس الهدف متوافقين مع الدقة والمقياس للمصدر حتى تتلاءم جميع الأرقام من المصدر مع الحقل المستهدف. على سبيل المثال، إذا كان لديك الدقة 6 والمقياس 2 في المصدر (وبالتبعية أرقام بخلاف 0 قبل النقطة العشرية)، فلا يمكنك الحصول على الدقة 6 والمقياس 6 في الهدف.
#XMSG
invalidMappedScale=أدخل قيمة مقياس صالحة.
#XMSG
invalidMappedScaleDescription1=تحدد الدقة إجمالي عدد الأرقام. يحدد المقياس عدد الأرقام بعد النقطة العشرية ويمكن أن يكون بين 0 والدقة.{0}{0} أمثلة:{0}{1} الدقة 6، المقياس 2 يتوافق مع أرقام مثل 1234.56.{0}{1} الدقة 6، المقياس 6 يتوافق مع أرقام مثل 0.123546.{0}{0}يجب أن يكون مقياس نوع البيانات مساويًا لمقياس المصدر ({2}) أو أكبر منه.
#XMSG
nonCompatibleDataType=حدد نوع بيانات مستهدف متوافق.
#XMSG
nonCompatibleDataTypeDescription1=يجب أن يكون نوع البيانات الذي تحدده هنا متوافقًا مع نوع بيانات المصدر ({0}). {1}{1} مثال: إذا كان العمود المصدر يحتوي على سلسلة نوع بيانات ويحتوي على حروف، فلا يمكنك استخدام نوع بيانات عشري لهدفك.
#XMSG
invalidColumnCount=حدد العمود المصدر.
#XMSG
ObjectStoreInvalidScaleORPrecision=أدخل قيمة صالحة للدقة والمقياس.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=القيمة الأولى هي الدقة التي تحدد إجمالي عدد الأرقام. القيمة الثانية هي المقياس الذي يحدد الأرقام بعد النقطة العشرية. أدخل قيمة مقياس مستهدف أكبر من قيمة المقياس المصدر وتأكد من أن الفرق بين المقياس المستهدف المدخل وقيمة الدقة أكبر من الفرق بين المقياس المصدر وقيمة الدقة.
#XMSG
InvalidPrecisionORScale=أدخل قيمة صالحة للدقة والمقياس.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=القيمة الأولى هي الدقة التي تحدد إجمالي عدد الأرقام. القيمة الثانية هي المقياس، الذي يحدد الأرقام بعد النقطة العشرية.{0}{0}نظرًا لأن نوع بيانات المصدر غير مدعوم في Google BigQuery، يتم تحويله إلى نوع البيانات المستهدف DECIMAL. وفي هذه الحالة، لا يمكن تحديد الدقة إلا بين 38 و76، والمقياس بين 9 و38. وعلاوة على ذلك، يجب أن تكون نتيجة الدقة ناقص المقياس، التي تمثل الأرقام قبل النقطة العشرية، بين 29 و38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=القيمة الأولى هي الدقة التي تحدد إجمالي عدد الأرقام. القيمة الثانية هي المقياس، الذي يحدد الأرقام بعد النقطة العشرية.{0}{0}نظرًا لأن نوع بيانات المصدر غير مدعوم في Google BigQuery، يتم تحويله إلى نوع البيانات المستهدف DECIMAL. في هذه الحالة، يجب تحديد الدقة على أنها 20 أو أكثر. بالإضافة إلى ذلك، يجب أن تكون نتيجة الدقة ناقص المقياس، التي تعكس الأرقام قبل النقطة العشرية، 20 أو أكثر.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=القيمة الأولى هي الدقة التي تحدد إجمالي عدد الأرقام. القيمة الثانية هي المقياس، الذي يحدد الأرقام بعد النقطة العشرية. {0}{0}نظرًا لأن نوع البيانات المصدر غير مدعوم في الهدف، يتم تحويله إلى نوع البيانات المستهدف DECIMAL (عشري). في هذه الحالة، يجب تحديد الدقة بأي رقم أكبر من 1 أو يساويه وأقل من 38 أو يساويه، والمقياس أقل من الدقة أو يساويها.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=القيمة الأولى هي الدقة التي تحدد إجمالي عدد الأرقام. القيمة الثانية هي المقياس، الذي يحدد الأرقام بعد النقطة العشرية. {0}{0}نظرًا لأن نوع بيانات المصدر غير مدعوم في الهدف، يتم تحويله إلى نوع البيانات المستهدف DECIMAL (عشري). في هذه الحالة، يجب تحديد الدقة على أنها 20 أو أكبر. بالإضافة إلى ذلك، يجب أن تكون نتيجة الدقة ناقص المقياس، التي تعكس الأرقام قبل النقطة العشرية، 20 أو أكبر.
#XMSG
invalidColumnCountDescription=لإنشاء إدخال ربط صالح، تحتاج إلى تحديد عمود مصدر أو إدخال قيمة ثابتة.
#XMSG
duplicateColumns=قم بإعادة تسمية العمود المستهدف.
#XMSG
duplicateGBQCDCColumnsDesc=اسم العمود المستهدف محجوز في Google BigQuery. تحتاج إلى إعادة تسميته حتى تتمكن من حفظ التقدير.
#XMSG
duplicateConfluentCDCColumnsDesc=اسم العمود المستهدف محجوز في Confluent. يجب عليك إعادة تسميته حتى تتمكن من حفظ التقدير.
#XMSG
duplicateSignavioCDCColumnsDesc=اسم العمود المستهدف محجوز في SAP Signavio. يجب عليك إعادة تسميته حتى تتمكن من حفظ التقدير.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=اسم العمود المستهدف محجوز في MS OneLake. يجب عليك إعادة تسميته حتى تتمكن من حفظ التقدير.
#XMSG
duplicateSFTPCDCColumnsDesc=اسم العمود المستهدف محجوز في SFTP. ويجب عليك إعادة تسميته لتتمكن من حفظ التقدير.
#XMSG
GBQTargetNameWithPrefixUpdated1=يحتوي اسم العمود المستهدف على بادئة محجوزة في Google BigQuery. يجب عليك إعادة تسميته لتتمكن من حفظ التقدير. {0}{0}لا يمكن أن يبدأ اسم العمود المستهدف بأي من السلاسل التالية:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=قم بتقصير اسم العمود المستهدف.
#XMSG
GBQtargetMaxLengthDesc=في Google BigQuery، يمكن أن يستخدم اسم العمود 300 حرف كحد أقصى. قم بتقصير اسم العمود المستهدف لتتمكن من حفظ التقدير.
#XMSG
invalidMappedScalePrecision=يجب أن تكون الدقة والمقياس للهدف متوافقين مع الدقة والمقياس للمصدر بحيث تتلاءم جميع الأرقام من المصدر مع المجال المستهدف.
#XMSG
invalidMappedScalePrecisionShortText=أدخل قيمة صالحة للدقة والمقياس.
#XMSG
validationIncompatiblePKTypeDescProjection3=يحتوي عمود مصدر واحد أو عدة أعمدة على أنواع بيانات لا يمكن تعريفها كمفاتيح أساسية في Google BigQuery. لن يتم إنشاء أي من المفاتيح الأساسية في الكائن المستهدف.{0}{0}أنواع البيانات المستهدفة التالية متوافقة مع أنواع بيانات Google BigQuery التي يمكن تحديد مفتاح أساسي لها:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=قم بإلغاء تحديد العمود __message_id.
#XMSG
uncheckColumnMessageIdDesc=العمود: المفتاح الأساسي
#XMSG
validationOpCodeInsert=يجب إدخال قيمة للإدراج.
#XMSG
recommendDifferentPrimaryKey=من المستحسن اختيار مفتاح أساسي مختلف على مستوى العنصر.
#XMSG
recommendDifferentPrimaryKeyDesc=عند تحديد رمز العملية مسبقًا، من المستحسن اختيار مفاتيح أساسية مختلفة لفهرس المصفوفة والعناصر، لتجنب المشاكل مثل تكرار الأعمدة على سبيل المثال.
#XMSG
selectPrimaryKeyItemLevel=يجب عليك اختيار مفتاح أساسي واحد على الأقل لكل من مستوى العنصر والمقدمة.
#XMSG
selectPrimaryKeyItemLevelDesc=عند توسيع مصفوفة أو خريطة، يجب عليك اختيار مفتاحين أساسيين، أحدهما على مستوى المقدمة والآخر على مستوى العنصر.
#XMSG
invalidMapKey=يجب عليك اختيار مفتاح أساسي واحد على الأقل على مستوى المقدمة.
#XMSG
invalidMapKeyDesc=عند توسيع مصفوفة أو خريطة، يجب عليك تحديد مفتاح أساسي على مستوى المقدمة.
#XFLD
txtSearchFields=البحث عن الأعمدة المستهدفة
#XFLD
txtName=الاسم
#XMSG
txtSourceColValidation=عمود مصدر واحد أو أكثر غير مدعوم:
#XMSG
txtMappingCount=عمليات الربط ({0})
#XMSG
schema=المخطط
#XMSG
sourceColumn=الأعمدة المصدر
#XMSG
warningSourceSchema=سيؤثر أي تغيير يتم إجراؤه على المخطط على عمليات الربط في مربع حوار التقدير.
#XCOL
txtTargetColName=العمود المستهدف (الاسم التقني)
#XCOL
txtDataType=نوع البيانات المستهدفة
#XCOL
txtSourceDataType=نوع البيانات المصدر
#XCOL
srcColName=العمود المصدر (الاسم التقني)
#XCOL
precision=الدقة
#XCOL
scale=المقياس
#XCOL
functionsOrConstants=الوظائف/القيم الثابتة
#XCOL
txtTargetColBusinessName=العمود المستهدف (الاسم التجاري)
#XCOL
prKey=المفتاح الأساسي
#XCOL
txtProperties=الخصائص
#XBUT
txtOK=حفظ
#XBUT
txtCancel=إلغاء
#XBUT
txtRemove=إزالة
#XFLD
txtDesc=الوصف
#XMSG
rftdMapping=ربط
#XFLD
@lblColumnDataType=نوع البيانات
#XFLD
@lblColumnTechnicalName=الاسم التقني
#XBUT
txtAutomap=ربط تلقائي
#XBUT
txtUp=لأعلى
#XBUT
txtDown=لأسفل

#XTOL
txtTransformationHeader=التقدير
#XTOL
editTransformation=تحرير
#XTOL
primaryKeyToolip=المفتاح


#XMSG
rftdFilter=تصفية
#XMSG
rftdFilterColumnCount=المصدر: {0}({1})
#XTOL
rftdFilterColSearch=بحث
#XMSG
rftdFilterColNoData=لا توجد أعمدة لعرضها
#XMSG
rftdFilteredColNoExps=لا توجد تعبيرات تصفية
#XMSG
rftdFilterSelectedColTxt=إضافة عامل تصفية لـ
#XMSG
rftdFilterTxt=تصفية متوفرة لـ
#XBUT
rftdFilterSelectedAddColExp=إضافة تعبير
#YINS
rftdFilterNoSelectedCol=حدد عمودًا لإضافة عامل تصفية.
#XMSG
rftdFilterExp=تعبير التصفية
#XMSG
rftdFilterNotAllowedColumn=إضافة عوامل التصفية غير مدعوم لهذا العمود.
#XMSG
rftdFilterNotAllowedHead=عمود غير مدعوم
#XMSG
rftdFilterNoExp=لم يتم تحديد أي عامل تصفية
#XTOL
rftdfilteredTt=تمت التصفية
#XTOL
rftdremoveexpTt=إزالة تعبير التصفية
#XTOL
validationMessageTt=رسائل التحقق من الصحة
#XTOL
rftdFilterDateInp=تحديد تاريخ
#XTOL
rftdFilterDateTimeInp=تحديد وقت/تاريخ
#XTOL
rftdFilterTimeInp=تحديد وقت
#XTOL
rftdFilterInp=إدخال قيمة
#XMSG
rftdFilterValidateEmptyMsg={0} من تعبيرات التصفية في عمود {1} فارغة
#XMSG
rftdFilterValidateInvalidNumericMsg={0} من تعبيرات التصفية في عمود {1} تحتوي على قيم رقمية غير صالحة
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=يجب أن يحتوي تعبير التصفية على قيم رقمية صالحة
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=إذا تم تغيير مخطط الكائن المستهدف، فاستخدم الوظيفة 'ربط بالكائن المستهدف الموجود' في الصفحة الرئيسية لتهيئة التغييرات وإعادة ربط الكائن المستهدف بمصدره مرة أخرى.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=إذا كان الجدول المستهدف موجودًا بالفعل ويتضمن الربط تغيير مخطط، فيجب تغيير الجدول المستهدف وفقًا لذلك قبل نشر تدفق النسخ المتماثل.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=إذا كان الربط الخاص بك يتضمن تغيير مخطط، فيجب تغيير الجدول المستهدف وفقًا لذلك قبل نشر تدفق النسخ المتماثل.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=تم تخطي الأعمدة التالية غير المدعومة من التعريف المصدر: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=تم تخطي الأعمدة التالية غير المدعومة من التعريف المستهدف: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=الكائنات التالية غير مدعومة لأنها معرضة للاستخدام: {0} {1} {0} {0} لاستخدام الجداول في تدفق النسخ المتماثل، يجب عدم تعيين الاستخدام الدلالي (في إعدادات الجدول) إلى {2}مجموعة البيانات التحليلية{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=لا يمكن استخدام الكائن المستهدف لأنه معرض للاستخدام. {0} {0} لاستخدام الجدول في تدفق النسخ المتماثل، يجب عدم تعيين الاستخدام الدلالي (في إعدادات الجدول) إلى {1}مجموعة البيانات التحليلية{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=يوجد كائن مستهدف بهذا الاسم بالفعل. ومع ذلك، لا يمكن استخدامه لأنه مُعرَّض للاستخدام. {0} {0} لاستخدام الجدول في تدفق النسخ المتماثل، يجب عدم تعيين الاستخدام الدلالي (في إعدادات الجدول) إلى {1}مجموعة البيانات التحليلية{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=يوجد كائن بهذا الاسم بالفعل في الهدف. {0}ومع ذلك، لا يمكن استخدام هذا الكائن ككائن مستهدف لتدفق النسخ المتماثل إلى المستودع المحلي، لأنه ليس جدولاً محليًا.
#XMSG:
targetAutoRenameUpdated=تمت إعادة تسمية العمود المستهدف.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=تمت إعادة تسمية العمود المستهدف للسماح بعمليات النسخ المتماثل في Google BigQuery. يرجع ذلك إلى أحد الأسباب التالية:{0} {1}{2}اسم العمود المحجوز{3}{2}الحروف غير المدعومة{3}{2}البادئة المحجوزة{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=تمت إعادة تسمية العمود المستهدف للسماح بعمليات النسخ المتماثل في Confluent. يرجع ذلك إلى أحد الأسباب التالية:{0} {1}{2}اسم العمود المحجوز{3}{2}الحروف غير المدعومة{3}{2}البادئة المحجوزة{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=تمت إعادة تسمية العمود المستهدف للسماح بعمليات النسخ المتماثل إلى الهدف. يرجع ذلك إلى أحد الأسباب التالية:{0} {1}{2}حروف غير مدعومة{3}{2}بادئة محجوزة{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=تمت إعادة تسمية العمود المستهدف للسماح بعمليات النسخ المتماثل إلى الهدف. يرجع ذلك إلى أحد الأسباب التالية:{0} {1}{2}اسم العمود المحجوز{3}{2}الحروف غير المدعومة{3}{2}البادئة المحجوزة{3}{4}
#XMSG:
targetAutoDataType=تم تغيير نوع البيانات المستهدف.
#XMSG:
targetAutoDataTypeDesc=تم تغيير نوع البيانات المستهدف إلى {0} لأن نوع بيانات المصدر غير مدعوم في Google BigQuery.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=تم تغيير نوع البيانات المستهدف إلى {0} لأن نوع البيانات المصدر غير مدعوم في الاتصال المستهدف.
#XMSG
projectionGBQUnableToCreateKey=لن يتم إنشاء المفاتيح الأساسية.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=في Google BigQuery، يتم دعم 16 مفتاحًا أساسيًا كحد أقصى، ولكن الكائن المصدر يحتوي على عدد أكبر من المفاتيح الأساسية. لن يتم إنشاء أي من المفاتيح الأساسية في الكائن المستهدف.
#XMSG
HDLFNoKeyError=حدد عمودًا واحدًا أو أكثر كمفتاح أساسي.
#XMSG
HDLFNoKeyErrorDescription=لنسخ كائن نسخًا متماثلًا، يجب عليك تحديد عمود واحد أو أكثر كمفتاح أساسي.
#XMSG
HDLFNoKeyErrorExistingTarget=حدد عمودًا واحدًا أو أكثر كمفتاح أساسي.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=لنسخ البيانات نسخًا متماثلًا إلى كائن مستهدف موجود، يجب أن يحتوي على عمود واحد أو أكثر يتم تحديده كمفتاح أساسي. {0} {0} لديك الخيارات التالية لتحديد عمود واحد أو أكثر كمفتاح أساسي: {0}{0}{1} استخدم محرِّر الجدول المحلي لتغيير الكائن المستهدف الموجود. ثم أعد تحميل تدفق النسخ المتماثل.{0}{0}{1} أعد تسمية الكائن المستهدف في تدفق النسخ المتماثل. سيؤدي هذا إلى إنشاء كائن جديد بمجرد بدء التشغيل. بعد إعادة التسمية، يمكنك تحديد عمود واحد أو أكثر كمفتاح أساسي في التقدير. {0}{0}{1} يمكنك ربط الكائن بكائن مستهدف آخر موجود والذي يتم فيه بالفعل تحديد عمود واحد أو أكثر على أنه المفتاح الأساسي.
#XMSG
HDLFSourceTargetDifferentKeysWarning=تم تغيير المفتاح الأساسي.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=مقارنةً بالكائن المصدر، لقد حددت أعمدة مختلفة كمفتاح أساسي للكائن المستهدف. تأكد من أن هذه الأعمدة تحدد جميع الصفوف بشكل فريد لتجنب تلف البيانات المحتمل عند نسخ البيانات نسخًا متماثلًا لاحقًا. {0} {0} في الكائن المصدر، يتم تحديد الأعمدة التالية كمفتاح أساسي: {0} {1}
#XMSG
duplicateDPIDColumns=قم بإعادة تسمية العمود المستهدف.
#XMSG
duplicateDPIDDColumnsDesc1=اسم العمود المستهدف هذا محجوز للعمود التقني. أدخل اسمًا مختلفًا لحفظ التقدير.
#XMSG:
targetAutoRenameDPID=تمت إعادة تسمية العمود المستهدف.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=تمت إعادة تسمية العمود المستهدف للسماح بعمليات النسخ المتماثل من مصدر ABAP بدون مفاتيح. يرجع ذلك إلى أحد الأسباب التالية:{0} {1}{2}اسم العمود المحجوز{3}{2}الحروف غير المدعومة{3}{2}البادئة المحجوزة{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle={0} من الإعدادات المستهدفة
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle={0} من الإعدادات المصدر
#XBUT
connectionSettingSave=حفظ
#XBUT
connectionSettingCancel=إلغاء
#XBUT: Button to keep the object level settings
txtKeep=احتفاظ
#XBUT: Button to overwrite the Object level settings
txtOverwrite=استبدال
#XFLD
targetConnectionThreadlimit=حد سلسلة رسائل مستهدفة للتحميل الأولي (1-100)
#XFLD
connectionThreadLimit=حد سلسلة رسائل المصدر للتحميل الأولي (1-100)
#XFLD
maxConnection=حد سلسلة رسائل النسخ المتماثل (1-100)
#XFLD
kafkaNumberOfPartitions=عدد التقسيمات
#XFLD
kafkaReplicationFactor=عامل النسخ المتماثل
#XFLD
kafkaMessageEncoder=أداة ترميز الرسالة
#XFLD
kafkaMessageCompression=ضغط الرسالة
#XFLD
fileGroupDeltaFilesBy=فرق المجموعة حسب
#XFLD
fileFormat=نوع الملف
#XFLD
csvEncoding=ترميز CSV
#XFLD
abapExitLbl=مساحة تخصيص ABAP
#XFLD
deltaPartition=عدد سلاسل رسائل الكائنات لعمليات تحميل الفرق (1-10)
#XFLD
clamping_Data=فشل في قطع البيانات
#XFLD
fail_On_Incompatible=فشل في البيانات غير المتوافقة
#XFLD
maxPartitionInput=الحد الأقصى لعدد التقسيمات
#XFLD
max_Partition=تحديد الحد الأقصى لعدد التقسيمات
#XFLD
include_SubFolder=تضمين المجلدات الفرعية
#XFLD
fileGlobalPattern=النمط العام لاسم الملف
#XFLD
fileCompression=ضغط الملف
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=محدِّد الملف
#XFLD
fileIsHeaderIncluded=مقدمة الملف
#XFLD
fileOrient=التوجيه
#XFLD
gbqWriteMode=نمط الكتابة
#XFLD
suppressDuplicate=حذف التكرارات
#XFLD
apacheSpark=تمكين توافق Apache Spark
#XFLD
clampingDatatypeCb=تثبيت أنواع بيانات النقطة العائمة العشرية
#XFLD
overwriteDatasetSetting=استبدال الإعدادات المستهدفة على مستوى الكائن
#XFLD
overwriteSourceDatasetSetting=استبدال إعدادات المصدر على مستوى الكائن
#XMSG
kafkaInvalidConnectionSetting=أدخل الرقم بين {0} و{1}.
#XMSG
MinReplicationThreadErrorMsg=أدخل رقمًا أكبر من {0}.
#XMSG
MaxReplicationThreadErrorMsg=أدخل رقمًا أقل من {0}.
#XMSG
DeltaThreadErrorMsg=أدخل قيمة بين 1 و10.
#XMSG
MaxPartitionErrorMsg=أدخل قيمة بين 1 <= x <= 2147483647. القيمة الافتراضية هي 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=أدخل عددًا صحيحًا بين {0} و{1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=استخدام عامل النسخ المتماثل للوسيط
#XFLD
serializationFormat=تنسيق التسلسل
#XFLD
compressionType=نوع الضغط
#XFLD
schemaRegistry=استخدام سجل المخططات
#XFLD
subjectNameStrat=إستراتيجية اسم الموضوع
#XFLD
compatibilityType=نوع التوافق
#XFLD
confluentTopicName=اسم الموضوع
#XFLD
confluentRecordName=اسم السجل
#XFLD
confluentSubjectNamePreview=معاينة اسم الموضوع
#XMSG
serializationChangeToastMsgUpdated2=تم تغيير تنسيق إنشاء التسلسل إلى JSON نظرًا لعدم تمكين سجل المخطط. لتغيير تنسيق التسلسل مرة أخرى إلى AVRO، يجب عليك تمكين سجل المخطط أولاً.
#XBUT
confluentTopicNameInfo=يعتمد اسم الموضوع دائمًا على اسم الكائن المستهدف. ويمكنك تغييره من خلال إعادة تسمية الكائن المستهدف.
#XMSG
emptyRecordNameValidationHeaderMsg=أدخل اسم سجل.
#XMSG
emptyPartionHeader=أدخل عدد التقسيمات.
#XMSG
invalidPartitionsHeader=أدخل عددًا صالحًا من التقسيمات.
#XMSG
invalidpartitionsDesc=أدخل رقمًا بين 1 و200000.
#XMSG
emptyrFactorHeader=أدخل عامل نسخ متماثل.
#XMSG
invalidrFactorHeader=أدخل عامل نسخ متماثل صالحًا.
#XMSG
invalidrFactorDesc=أدخل رقمًا بين 1 و32767.
#XMSG
emptyRecordNameValidationDescMsg=إذا تم استخدام تنسيق التسلسل "AVRO"، فلن يتم دعم سوى الحروف التالية:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(الشرطة السفلية)
#XMSG
validRecordNameValidationHeaderMsg=أدخل اسم سجل صالحًا.
#XMSG
validRecordNameValidationDescMsgUpdated=نظرًا لاستخدام تنسيق التسلسل "AVRO"، يجب أن يتكون اسم السجل من أحرف أبجدية رقمية (A-Z، a-z، 0-9) وشرطة سفلية (_) فقط. يجب أن يبدأ بحرف أو شرطة سفلية.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=يمكن تعيين 'عدد سلاسل رسائل الكائنات لعمليات تحميل الفرق' بمجرد أن يكون كائن واحد أو أكثر بنوع التحميل 'أولي وفرق'.
#XMSG
invalidTargetName=اسم العمود غير صالح
#XMSG
invalidTargetNameDesc=يجب أن يتكون اسم العمود المستهدف من حروف أبجدية رقمية (A-Z، a-z، 0-9) وشرطة سفلية (_) فقط.
#XFLD
consumeOtherSchema=استهلاك إصدارات المخططات الأخرى
#XFLD
ignoreSchemamissmatch=تجاهل عدم تطابق المخطط
#XFLD
confleuntDatatruncation=فشل في قطع البيانات
#XFLD
isolationLevel=مستوى العزل
#XFLD
confluentOffset=نقطة البداية
#XFLD
signavioGroupDeltaFilesByText=لا شيء
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=لا
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=لا

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=التقديرات
#XBUT
txtAdd=إضافة
#XBUT
txtEdit=تحرير
#XMSG
transformationText=أضف تقديرًا لإعداد عامل تصفية أو ربط.
#XMSG
primaryKeyRequiredText=حدد مفتاحًا أساسيًا مع تكوين المخطط.
#XFLD
lblSettings=الإعدادات
#XFLD
lblTargetSetting={0}: الإعدادات المستهدفة
#XMSG
@csvRF=حدد الملف الذي يحتوي على تعريف المخطط الذي تريد تطبيقه على جميع الملفات في المجلد.
#XFLD
lblSourceColumns=الأعمدة المصدر
#XFLD
lblJsonStructure=بنية JSON 
#XFLD
lblSourceSetting={0}: الإعدادات المصدر
#XFLD
lblSourceSchemaSetting={0}: الإعدادات المصدر للمخطط
#XBUT
messageSettings=إعدادات الرسالة
#XFLD
lblPropertyTitle1=خصائص الكائن
#XFLD
lblRFPropertyTitle=خصائص تدفق النسخ المتماثل
#XMSG
noDataTxt=لا توجد أعمدة لعرضها.
#XMSG
noTargetObjectText=لم يتم تحديد كائن مستهدف.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=الأعمدة المستهدفة
#XMSG
searchColumns=بحث عن الأعمدة
#XTOL
cdcColumnTooltip=عمود التقاط الفرق
#XMSG
sourceNonDeltaSupportErrorUpdated=الكائن المصدر لا يدعم التقاط الفرق.
#XMSG
targetCDCColumnAdded=تم إضافة عمودين مستهدفين لتحميل الفرق.
#XMSG
deltaPartitionEnable=تمت إضافة حد سلسلة رسائل الكائن لعمليات تحميل الفرق إلى إعدادات المصدر.
#XMSG
attributeMappingRemovalTxt=قم بإزالة عمليات الربط غير الصالحة وغير المدعومة للكائن المستهدف الجديد.
#XMSG
targetCDCColumnRemoved=تم إزالة عمودين مستهدفين مستخدمين لتحميل الفرق.
#XMSG
replicationLoadTypeChanged=تم تغيير نوع التحميل إلى "أولي والفرق".
#XMSG
sourceHDLFLoadTypeError=قم بتغيير نوع التحميل إلى "أولي والفرق".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=لنسخ كائن نسخًا متماثلًا من اتصال مصدر بنوع الاتصال ملفات مستودع البيانات لقاعدة بيانات SAP HANA Cloud إلى SAP Datasphere، يجب عليك استخدام نوع التحميل "أولي والفرق".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=تمكين التقاط الفرق.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=لنسخ كائن نسخًا متماثلًا من اتصال مصدر بنوع الاتصال ملفات مستودع البيانات لقاعدة بيانات SAP HANA Cloud إلى SAP Datasphere، يجب عليك تمكين التقاط الفرق.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=قم بتغيير الكائن المستهدف.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=لا يمكن استخدام الكائن المستهدف بسبب تعطيل التقاط الفرق. ويمكنك إما إعادة تسمية الكائن المستهدف (الذي يسمح بإنشاء كائن جديد مع التقاط الفرق) أو ربطه بكائن موجود مع تمكين التقاط الفرق.
#XMSG
deltaPartitionError=أدخل عدد سلاسل رسائل كائنات صالحًا لعمليات تحميل الفرق.
#XMSG
deltaPartitionErrorDescription=أدخل قيمة بين 1 و10.
#XMSG
deltaPartitionEmptyError=أدخل عدد سلاسل رسائل كائنات لعمليات تحميل الفرق.
#XFLD
@lblColumnDescription=الوصف
#XMSG
@lblColumnDescriptionText1=للأغراض التقنية - معالجة السجلات المكررة الناتجة عن مشكلات أثناء النسخ المتماثل لكائنات المصدر المستندة إلى ABAP والتي لا تحتوي على مفتاح أساسي.
#XFLD
storageType=التخزين
#XFLD
skipUnmappedColLbl=تخطي الأعمدة غير المربوطة
#XFLD
abapContentTypeLbl=نوع المحتوى
#XFLD
autoMergeForTargetLbl=دمج البيانات تلقائيًا
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=عام
#XFLD
lblBusinessName=الاسم التجاري
#XFLD
lblTechnicalName=الاسم التقني
#XFLD
lblPackage=الحزمة
#XFLD
statusPanel=حالة التشغيل
#XBTN: Schedule dropdown menu
SCHEDULE=الجدول الزمني
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=تحرير الجدول الزمني
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=حذف الجدول الزمني
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=إنشاء الجدول الزمني
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=فشل التحقق من صحة الجدول الزمني
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=لا يمكن إنشاء جدول زمني لأنه يتم حاليًا نشر تدفق النسخ المتماثل.{0}يرجى الانتظار حتى يتم نشر تدفق النسخ المتماثل.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=بالنسبة إلى تدفقات النسخ المتماثل التي تحتوي على كائنات بنوع التحميل "أولي والفرق"، لا يمكن إنشاء جدول زمني.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=بالنسبة لتدفقات النسخ المتماثل التي تحتوي على كائنات بنوع التحميل "أولي وفرق/فرق فقط"، لا يمكن إنشاء جدول زمني.
#XFLD : Scheduled popover
SCHEDULED=مجدوَل
#XFLD
CREATE_REPLICATION_TEXT=إنشاء تدفق نسخ متماثل
#XFLD
EDIT_REPLICATION_TEXT=تحرير تدفق نسخ متماثل
#XFLD
DELETE_REPLICATION_TEXT=حذف تدفق نسخ متماثل
#XFLD
REFRESH_FREQUENCY=التكرار
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=لا يمكن نشر تدفق النسخ المتماثل لأن الجدول الزمني الموجود{0} لا يدعم بعد نوع التحميل "أولي وفرق".{0}{0}لنشر تدفق النسخ المتماثل، يجب عليك تعيين أنواع التحميل لجميع الكائنات{0} إلى "أولي فقط". وبدلاً من ذلك، يمكنك حذف الجدول الزمني، ونشر تدفق النسخ المتماثل{0}، ثم بدء تشغيل جديد. يؤدي ذلك إلى التشغيل بلا نهاية{0}، والذي يدعم أيضًا الكائنات بنوع التحميل "أولي وفرق".
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=لا يمكن نشر تدفق النسخ المتماثل لأن الجدول الزمني الموجود{0} لا يدعم بعد نوع التحميل "أولي وفرق/فرق فقط".{0}{0}لنشر تدفق النسخ المتماثل، يجب عليك تعيين أنواع التحميل لجميع الكائنات{0} إلى "أولي فقط". وبدلاً من ذلك، يمكنك حذف الجدول الزمني، ونشر تدفق النسخ المتماثل{0}، ثم بدء تشغيل جديد. يؤدي ذلك إلى التشغيل بلا نهاية{0}، والذي يدعم أيضًا الكائنات بنوع التحميل "أولي وفرق/فرق فقط".
#XMSG
SCHEDULE_EXCEPTION=فشل الحصول على تفاصيل الجدول الزمني
#XFLD: Label for frequency column
everyLabel=كل
#XFLD: Plural Recurrence text for Hour
hoursLabel=ساعات
#XFLD: Plural Recurrence text for Day
daysLabel=أيام
#XFLD: Plural Recurrence text for Month
monthsLabel=شهور
#XFLD: Plural Recurrence text for Minutes
minutesLabel=دقائق
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=فشل الحصول على معلومات حول إمكانية الجدولة.
#XFLD :Paused field
PAUSED=متوقف مؤقتًا
#XMSG
navToMonitoring=فتح في مراقبة التدفقات
#XFLD
statusLbl=الحالة
#XFLD
lblLastRunExecuted=بداية آخر تشغيل
#XFLD
lblLastExecuted=آخر تشغيل
#XFLD: Status text for Completed
statusCompleted=مكتمل
#XFLD: Status text for Running
statusRunning=قيد التشغيل
#XFLD: Status text for Failed
statusFailed=فشل
#XFLD: Status text for Stopped
statusStopped=موقوف
#XFLD: Status text for Stopping
statusStopping=جارٍ الإيقاف
#XFLD: Status text for Active
statusActive=نشط
#XFLD: Status text for Paused
statusPaused=متوقف مؤقتًا
#XFLD: Status text for not executed
lblNotExecuted=لم يتم التشغيل حتى الآن
#XFLD
messagesSettings=إعدادات الرسائل
#XTOL
@validateModel=رسائل التحقق من الصحة
#XTOL
@hierarchy=التسلسل الهرمي
#XTOL
@columnCount=عدد الأعمدة
#XMSG
VAL_PACKAGE_CHANGED=لقد قمت بتعيين هذا الكائن للحزمة "{1}". انقر فوق "حفظ" لتأكيد هذا التغيير والتحقق من صحته. لاحظ أنه لا يمكن التراجع عن التعيين لحزمة في هذا المحرر بعد الحفظ.
#XMSG
MISSING_DEPENDENCY=لا يمكن حل تبعيات الكائن "{0}" في الحزمة "{1}".
#XFLD
deltaLoadInterval=فاصل تحميل الفرق
#XFLD
lblHour=الساعات (0-24)
#XFLD
lblMinutes=الدقائق (0-59)
#XMSG
maxHourOrMinErr=أدخل قيمة بين 0 و{0}
#XMSG
maxDeltaInterval=الحد الأقصى لقيمة فاصل تحميل الفرق هو 14 ساعة.{0}قم بتغيير قيمة الدقائق أو الساعات وفقًا لذلك.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=مسار الحاوية المستهدفة
#XFLD
confluentSubjectName=اسم الموضوع
#XFLD
confluentSchemaVersion=إصدار المخطط
#XFLD
confluentIncludeTechKeyUpdated=تضمين المفتاح التقني
#XFLD
confluentOmitNonExpandedArrays=حذف المصفوفات غير الموسعة
#XFLD
confluentExpandArrayOrMap=توسيع المصفوفة أو الخريطة
#XCOL
confluentOperationMapping=ربط العمليات
#XCOL
confluentOpCode=رمز العملية (Opcode)
#XFLD
confluentInsertOpCode=إدراج
#XFLD
confluentUpdateOpCode=تحديث
#XFLD
confluentDeleteOpCode=حذف
#XFLD
expandArrayOrMapNotSelectedTxt=غير محدد
#XFLD
confluentSwitchTxtYes=نعم
#XFLD
confluentSwitchTxtNo=لا
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=خطأ
#XTIT
executeWarning=تحذير
#XMSG
executeunsavederror=احفظ تدفق النسخ المتماثل الخاص بك قبل تشغيله.
#XMSG
executemodifiederror=توجد تغييرات غير محفوظة في تدفق النسخ المتماثل. يُرجى حفظ تدفق النسخ المتماثل.
#XMSG
executeundeployederror=يجب عليك نشر تدفق النسخ المتماثل الخاص بك قبل تشغيله.
#XMSG
executedeployingerror=يرجى الانتظار حتى ينتهي النشر.
#XMSG
msgRunStarted=تم بدء التشغيل
#XMSG
msgExecuteFail=فشل تشغيل تدفق النسخ المتماثل.
#XMSG
titleExecuteBusy=الرجاء الانتظار.
#XMSG
msgExecuteBusy=نقوم بتحضير بياناتك لتشغيل تدفق النسخ المتماثل.
#XTIT
executeConfirmDialog=تحذير
#XMSG
msgExecuteWithValidations=يحتوي تدفق النسخ المتماثل على أخطاء تحقق من الصحة. قد يتسبب تشغيل تدفق النسخ المتماثل في حدوث فشل.
#XMSG
msgRunDeployedVersion=توجد تغييرات للنشر. سيتم تشغيل آخر إصدار منشور لتدفق النسخ المتماثل. هل تريد المتابعة؟
#XBUT
btnExecuteAnyway=تشغيل على أي حال
#XBUT
btnExecuteClose=إغلاق
#XBUT
loaderClose=إغلاق
#XTIT
loaderTitle=تحميل
#XMSG
loaderText=استدعاء التفاصيل من الخادم
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=لا يمكن بدء تدفق النسخ المتماثل إلى هذا الاتصال المستهدف بخلاف SAP
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=لأنه لا يوجد حجم صادر متوفر لهذا الشهر.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=يمكن للمسؤول زيادة المجموعات الخارجية المميزة لهذه
#XMSG
premiumOutBoundRFAdminErrMsgPart2=الوحدة المستضافة، مما يجعل الحجم الصادر متوفرًا لهذا الشهر.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=خطأ
#XTIT
deployInfo=معلومات
#XMSG
deployCheckFailException=حدث استثناء أثناء النشر
#XMSG
deployGBQFFDisabled=لا يمكن حاليًا نشر تدفقات النسخ المتماثل باستخدام اتصال مستهدف بـ Google BigQuery لأننا نجري معالجة على هذه الوظيفة.
#XMSG
deployKAFKAFFDisabled=لا يمكن حاليًا نشر تدفقات النسخ المتماثل باستخدام اتصال مستهدف بـ Apache Kafka لأننا نجري معالجة على هذه الوظيفة.
#XMSG
deployConfluentDisabled=لا يمكن نشر تدفقات النسخ المتماثل باستخدام اتصال مستهدف بنظام Conflreing Kafka حاليًا لأننا نقوم بإجراء معالجة على هذه الوظيفة.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=بالنسبة للكائنات المستهدفة التالية، فإن أسماء جداول التقاط الفرق مستخدمة بالفعل بواسطة جداول أخرى في المستودع: {0} يجب إعادة تسمية هذه الكائنات المستهدفة للتأكد من أن أسماء جداول التقاط الفرق المرتبطة فريدة قبل أن تتمكن من نشر تدفق النسخ المتماثل.
#XMSG
deployDWCSourceFFDisabled=لا يمكن نشر تدفقات النسخ المتماثل التي تحتوي على SAP Datasphere كمصدر لها حاليًا لأننا نقوم بإجراء معالجة على هذه الدالة.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=لا يمكن نشر تدفقات النسخ المتماثل التي تحتوي على جداول محلية ممكَّنة للفرق نظرًا لأن الكائنات المصدر لا يمكن استخدامها حاليًا لأننا نقوم بمعالجة هذه الوظيفة.
#XMSG
deployHDLFSourceFFDisabled=نشر تدفقات النسخ المتماثل التي تحتوي على اتصالات مصدر بنوع الاتصال ملفات مستودع البيانات لقاعدة بيانات SAP HANA Cloud غير ممكن حاليًا لأننا نقوم بإجراء المعالجة.
#XMSG
deployObjectStoreAsSourceFFDisabled=نشر تدفقات النسخ المتماثل التي لها مزودي التخزين السحابي كمصدر لها غير ممكنة حاليًا.
#XMSG
deployConfluentSourceFFDisabled=لا يمكن نشر تدفقات النسخ المتماثل التي تحتوي على Confluent Kafka كمصدر لها حاليًا لأننا نقوم بإجراء معالجة على هذه الوظيفة.
#XMSG
deployMaxDWCNewTableCrossed=بالنسبة لتدفقات النسخ المتماثل الكبيرة، فلا يمكن 'حفظها ونشرها' دفعة واحدة. يُرجى حفظ تدفق النسخ المتماثل أولاً ثم نشره.
#XMSG
deployInProgressInfo=النشر قيد التنفيذ بالفعل.
#XMSG
deploySourceObjectInUse=الكائنات المصدر {0} قيد الاستخدام بالفعل في تدفقات النسخ المتماثل {1}.
#XMSG
deployTargetSourceObjectInUse=الكائنات المصدر {0} قيد الاستخدام بالفعل في تدفقات النسخ المتماثل {1}. الكائنات المستهدفة {2} قيد الاستخدام بالفعل في تدفقات النسخ المتماثل {3}.
#XMSG
deployReplicationFlowCheckError=حدث خطأ أثناء التحقق من تدفق النسخ المتماثل: {0}
#XMSG
preDeployTargetObjectInUse=يتم بالفعل استخدام الكائنات المستهدفة {0} في تدفقات النسخ المتماثل {1}، ولا يمكنك الحصول على الكائن المستهدف نفسه في تدفقين مختلفين للنسخ المتماثل. حدد كائنًا مستهدفًا آخر وحاول مرة أخرى.
#XMSG
runInProgressInfo=يتم تشغيل تدفق التكرار بالفعل.
#XMSG
deploySignavioTargetFFDisabled=نشر تدفقات النسخ المتماثل التي تحتوي على SAP Signavio لأن هدفها غير ممكن حاليًا لأننا نقوم بإجراء معالجة على هذه الوظيفة.
#XMSG
deployHanaViewAsSourceFFDisabled=لا يمكن حاليًا نشر تدفقات النسخ المتماثل التي تحتوي على طرق عرض ككائنات مصدر للاتصال المصدر المحدد. حاول مرة أخرى لاحقًا.
#XMSG
deployMsOneLakeTargetFFDisabled=نشر تدفقات النسخ المتماثل التي تحتوي على MS OneLake لأن هدفها غير ممكن حاليًا لأننا نقوم بإجراء معالجة على هذه الوظيفة.
#XMSG
deploySFTPTargetFFDisabled=نشر تدفقات النسخ المتماثل التي تحتوي على SFTP لأن هدفها غير ممكن حاليًا لأننا نقوم بإجراء معالجة على هذه الوظيفة.
#XMSG
deploySFTPSourceFFDisabled=نشر تدفقات النسخ المتماثل التي تحتوي على SFTP كمصدر لها غير ممكن حاليًا لأننا نقوم بإجراء معالجة على هذه الوظيفة.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=الاسم التقني
#XFLD
businessNameInRenameTarget=الاسم التجاري
#XTOL
renametargetDialogTitle=إعادة تسمية الكائن المستهدف
#XBUT
targetRenameButton=إعادة تسمية
#XBUT
targetRenameCancel=إلغاء
#XMSG
mandatoryTargetName=يجب عليك إدخال اسم.
#XMSG
dwcSpecialChar=_(الشرطة السفلية) هي الحرف الخاص الوحيد المسموح به.
#XMSG
dwcWithDot=اسم الجدول المستهدف يمكن أن يحتوي على أحرف لاتينية وأرقام وشُرَط سفلية (_) ونقاط (.). الحرف الأول يجب أن يكون حرفًا أو رقمًا أو شرطة سفلية (وليس نقطة).
#XMSG
nonDwcSpecialChar=الحروف الخاصة المسموح بها هي _(الشرطة السفلية) -(الواصلة) .(النقطة)
#XMSG
firstUnderscorePattern=يجب ألا يبدأ الاسم بـ _(شرطة سفلية)

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: عرض SQL - إنشاء عبارة الجدول
#XMSG
sqlDialogMaxPKWarning=في Google BigQuery، يتم دعم 16 مفتاحًا أساسيًا كحد أقصى، ويحتوي الكائن المصدر على رقم أكبر. لذلك، لم يتم تحديد أي مفاتيح أساسية في هذه العبارة.
#XMSG
sqlDialogIncomptiblePKTypeWarning=يحتوي عمود مصدر واحد أو أكثر على أنواع بيانات لا يمكن تحديدها كمفاتيح أساسية في Google BigQuery. ولذلك، لم يتم تحديد مفاتيح أساسية في هذه الحالة. في Google BigQuery، يمكن أن تحتوي أنواع البيانات التالية فقط على مفتاح أساسي: BIGNUMERIC، BOOLEAN، DATE، DATETIME، INT64 ، NUMERIC، STRING، TIMESTAMP
#XBUT
copyAndCloseDDL=نسخ وإغلاق
#XBUT
closeDDL=إغلاق
#XMSG
copiedToClipboard=تم النسخ إلى الحافظة


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=لا يمكن أن يكون الكائن ''{0}'' جزءًا من سلسلة مهام لعدم وجود نهاية له (حيث يتضمن كائنات بنوع التحميل "أولي وفرق/فرق فقط").
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=لا يمكن أن يكون الكائن ''{0}'' جزءًا من سلسلة مهام لعدم وجود نهاية له (حيث يتضمن كائنات بنوع التحميل "أولي وفرق").
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=لا يمكن إضافة الكائن "{0}" إلى سلسلة المهام.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=توجد كائنات مستهدفة غير محفوظة. يُرجى الحفظ مرة أخرى. {0}{0} تم تغيير سلوك هذه الميزة: في الماضي، تم إنشاء الكائنات المستهدفة فقط في البيئة المستهدفة عند نشر تدفق النسخ المتماثل.{0} يتم الآن إنشاء الكائنات بالفعل عند حفظ تدفق النسخ المتماثل. تم إنشاء تدفق النسخ المتماثل قبل هذا التغيير ويحتوي على كائنات جديدة.{0} تحتاج إلى حفظ تدفق النسخ المتماثل مرة أخرى قبل نشره بحيث يتم تضمين الكائنات الجديدة بشكل صحيح.
#XMSG
confirmChangeContentTypeMessage=أنت على وشك تغيير نوع المحتوى. إذا قمت بذلك، فسيتم حذف جميع التقديرات الموجودة.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=اسم الموضوع
#XFLD
schemaDialogVersionName=إصدار المخطط
#XFLD
includeTechKey=تضمين المفتاح التقني
#XFLD
segementButtonFlat=بسيط
#XFLD
segementButtonNested=متداخل
#XMSG
subjectNamePlaceholder=البحث عن اسم الموضوع

#XMSG
@EmailNotificationSuccess=يتم حفظ تكوين إشعارات البريد الإلكتروني لوقت التشغيل.

#XFLD
@RuntimeEmailNotification=إشعار البريد الإلكتروني لوقت التشغيل

#XBTN
@TXT_SAVE=حفظ


