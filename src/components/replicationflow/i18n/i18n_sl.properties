#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Tok podvajanja

#XFLD: Edit Schema button text
editSchema=<PERSON>rejanje sheme

#XTIT : Properties heading
configSchema=Konfiguriranje sheme

#XFLD: save changed button text
applyChanges=Uveljavite spremembe


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Izberite izvorno povezavo
#XFLD
sourceContainernEmptyText=Izberite zbirnik
#XFLD
targetConnectionEmptyText=Izberite ciljno povezavo
#XFLD
targetContainernEmptyText=Izberite zbirnik
#XFLD
sourceSelectObjectText=Izbira izvornega objekta
#XFLD
sourceObjectCount=Izvorni objekti ({0})
#XFLD
targetObjectText=Ciljni objekti
#XFLD
confluentBrowseContext=Izbira konteksta
#XBUT
@retry=Poskusi znova
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Nadgradnja najemnika je v teku.

#XTOL
browseSourceConnection=Poišči izvorno povezavo
#XTOL
browseTargetConnection=Poišči ciljno povezavo
#XTOL
browseSourceContainer=Poišči izvorni zbirnik
#XTOL
browseAndAddSourceDataset=Dodaj izvorne objekte
#XTOL
browseTargetContainer=Poišči ciljni zbirnik
#XTOL
browseTargetSetting=Poišči ciljne nastavitve
#XTOL
browseSourceSetting=Poišči izvorne nastavitve
#XTOL
sourceDatasetInfo=Informacije
#XTOL
sourceDatasetRemove=Odstrani
#XTOL
mappingCount=To predstavlja skupno število preslikav/izrazov, ki ne temeljijo na imenu.
#XTOL
filterCount=To predstavlja skupno število pogojev filtra.
#XTOL
loading=Nalaganje poteka ...
#XCOL
deltaCapture=Zajemanje delta
#XCOL
deltaCaptureTableName=Tabela zajemanja delte
#XCOL
loadType=Vrsta nalaganja
#XCOL
deleteAllBeforeLoading=Izbriši vse pred prenašanjem
#XCOL
transformationsTab=Projekcije
#XCOL
settingsTab=Nastavitve

#XBUT
renameTargetObjectBtn=Preimenuj ciljni objekt
#XBUT
mapToExistingTargetObjectBtn=Preslikaj v obstoječi ciljni objekt
#XBUT
changeContainerPathBtn=Spremeni pot zbirnika
#XBUT
viewSQLDDLUpdated=Prikaži SQL – kreiraj izjavo tabele
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Izvorni objekt ne podpira zajemanja delta, izbrani ciljni objekt pa ima možnost zajemanja delta omogočeno.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Ciljnega objekta ni mogoče uporabiti, ker je omogočeno zajemanje delta,{0}izvorni objekt pa ne podpira zajemanja delta.{1}Izberete lahko drug ciljni objekt, ki pa podpira zajemanje delta.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Ciljni objekt s tem imenom že obstaja. Ni ga pa mogoče uporabiti,{0}ker je omogočeno zajemanje delta, izvorni objekt pa ne podpira{0}zajemanja delta.{1}Lahko vnesete ime obstoječega ciljnega objekta, ki ne podpira{0}zajemanja delta ali pa vnesete ime, ki še ne obstaja.
#XBUT
copySQLDDLUpdated=Kopiraj SQL – kreiraj izjavo tabele
#XMSG
targetObjExistingNoCDCColumnUpdated=Obstoječe tabele v Google BigQuery morajo vključevati naslednje stolpce za zajem podatkov sprememb (CDC)):{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Naslednji izvorni objekti niso podprti, ker nimajo primarnega ključa ali uporabljajo povezavo, ki ne izpolnjuje pogojev za priklic primarnega ključa:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Glejte SAP KBA 3531135 za morebitno rešitev.
#XLST: load type list values
initial=Samo začetno
@emailUpdateError=Napaka pri posodobitvi seznama e-poštnih obvestil

#XLST
initialDelta=Začetno in delta

#XLST
deltaOnly=Samo delta
#XMSG
confluentDeltaLoadTypeInfo=Za vir Confluent Kafka sta podprti le vrsti prenosa Začetni in Delta.
#XMSG
confirmRemoveReplicationObject=Ali res želite izbrisati replikacijo?
#XMSG
confirmRemoveReplicationTaskPrompt=S tem dejanjem boste izbrisali obstoječe replikacije. Želite nadaljevati?
#XMSG
confirmTargetConnectionChangePrompt=S tem dejanjem boste ponastavili ciljno povezavo in ciljni zbirnik ter izbrisali vse ciljne objekte. Želite nadaljevati?
#XMSG
confirmTargetContainerChangePrompt=S tem dejanjem boste ponastavili ciljni zbirnik ter izbrisali vse obstoječe ciljne objekte. Želite nadaljevati?
#XMSG
confirmRemoveTransformObject=Ali res želite izbrisati projekcijo {0}?
#XMSG
ErrorMsgContainerChange=Pri spreminjanju poti zbirnika je prišlo do napake.
#XMSG
infoForUnsupportedDatasetNoKeys=Naslednji izvorni objekti niso podprti, ker nimajo primarnega ključa:
#XMSG
infoForUnsupportedDatasetView=Naslednji izvorni objekti vrste Ogledi niso podprti:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Naslednji izvorni objekt ni podprt, ker je pogled SQL, ki vsebuje vhodne parametre:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Naslednji izvorni objekti niso podprti, ker ekstrakcija zanje ni onemogočena:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Za povezave Confluent sta edini dovoljeni obliki serializacije AVRO in JSON. Naslednji objekti niso podprti, ker uporabljajo drugačno obliko serializacije:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Sheme ni mogoče priklicati za naslednje objekte. Izberite ustrezni kontekst ali potrdite konfiguracijo registra sheme.
#XTOL: warning dialog header on deleting replication task
deleteHeader=Izbriši
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Google BigQuery ne podpira nastavitve izbrisa vsega pred nalaganjem.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Nastavitev izbrisa vsega pred izbriše in znova ustvari objekt (temo) pred vsakim podvajanjem. Izbrišejo se tudi vsa dodeljena sporočila.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Nastavitev izbrisa vsega pred ni podprta za to ciljno vrsto.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Tehnično ime
#XCOL
connBusinessName=Poslovno ime
#XCOL
connDescriptionName=Opis
#XCOL
connType=Tip
#XMSG
connTblNoDataFoundtxt=Povezave niso najdene
#XMSG
connectionError=Pri priklicu povezav je prišlo do napake.
#XMSG
connectionCombinationUnsupportedErrorTitle=Kombinacija povezav ni podprta
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Podvajanje iz {0} v {1} trenutno ni podprto.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Kombinacija vrst povezav ni podprta
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Podvajanje iz izvorne povezave z vrsto povezave SAP HANA Cloud, Data Lake Files v {0} ni podprto. Podvojite lahko le v SAP Datasphere.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Izbira
#XBUT
containerCancelBtn=Preklic
#XTOL
containerSelectTooltip=Izbira
#XTOL
containerCancelTooltip=Preklic
#XMSG
containerContainerPathPlcHold=Pot zbirnika
#XFLD
containerContainertxt=Zbirnik
#XFLD
confluentContainerContainertxt=Kontekst
#XMSG
infoMessageForSLTSelection=Kot zbirnik je dovoljeno samo /SLT/ID masovnega prenosa. V SLT izberite ID masovnega prenosa (če je na voljo) in kliknite Oddaj.
#XMSG
msgFetchContainerFail=Pri priklicu podatkov zbirnika je prišlo do napake.
#XMSG
infoMessageForSLTHidden=Ta povezava ne podpira map SLT, zato niso prikazane na spodnjem seznamu.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Izberite zbirnik, ki vsebuje podmape.
#XMSG
sftpIncludeSubFolderText=Napačno
#XMSG
sftpIncludeSubFolderTextNew=Ne

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Preslikava filtra še ne obstaja)
#XMSG
failToFetchRemoteMetadata=Pri priklicu metapodatkov je prišlo do napake.
#XMSG
failToFetchData=Pri priklicu obstoječega cilja je prišlo do napake.
#XCOL
@loadType=Vrsta nalaganja
#XCOL
@deleteAllBeforeLoading=Izbriši vse pred prenašanjem

#XMSG
@loading=Nalaganje poteka ...
#XFLD
@selectSourceObjects=Izbira izvornih objektov
#XMSG
@exceedLimit=Ne morete uvoziti več kot {0} objektov hkrati. Prekličite izbiro vsaj {1} objektov.
#XFLD
@objects=Objekti
#XBUT
@ok=V redu
#XBUT
@cancel=Preklic
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Naprej
#XBUT
btnAddSelection=Dodaj izbiro
#XTOL
@remoteFromSelection=Odstrani iz izbire
#XMSG
@searchInForSearchField=Iskanje v {0}

#XCOL
@name=Tehnično ime
#XCOL
@type=Tip
#XCOL
@location=Lokacija
#XCOL
@label=Poslovno ime
#XCOL
@status=Status

#XFLD
@searchIn=Iskanje v:
#XBUT
@available=Na voljo
#XBUT
@selection=Izbira

#XFLD
@noSourceSubFolder=Tabele in pogledi
#XMSG
@alreadyAdded=Že prisotno v diagramu
#XMSG
@askForFilter=Število elementov presega {0}. Vnesite niz filtra, da zmanjšate število elementov.
#XFLD: success label
lblSuccess=Uspešno
#XFLD: ready label
lblReady=Pripravljeno
#XFLD: failure label
lblFailed=Ni uspelo
#XFLD: fetching status label
lblFetchingDetail=Priklic podrobnosti

#XMSG Place holder text for tree filter control
filterPlaceHolder=Vnesite besedilo za filtriranje objektov na najvišji ravni
#XMSG Place holder text for server search control
serverSearchPlaceholder=Vnesite in pritisnite Enter za iskanje
#XMSG
@deployObjects=Poteka uvažanje objektov ({0}) ...
#XMSG
@deployObjectsStatus=Število uvoženih objektov: {0}. Število objektov, ki jih ni bilo mogoče uvoziti: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Odpiranje lokalnega brskalnik po odložišču ni uspelo.
#XMSG
@openRemoteSourceBrowserError=Priklic izvornih objektov ni uspel.
#XMSG
@openRemoteTargetBrowserError=Priklic ciljnih objektov ni uspel.
#XMSG
@validatingTargetsError=Pri preverjanju veljavnosti ciljev je prišlo do napake.
#XMSG
@waitingToImport=Pripravljeno za uvoz

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Največje število objektov je preseženo. Izberite največ 500 objektov za en tok replikacije.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Tehnično ime
#XFLD
sourceObjectBusinessName=Poslovno ime
#XFLD
sourceNoColumns=Število stolpcev
#XFLD
containerLbl=Zbirnik

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Za tok replikacije morate izbrati izvorno povezavo.
#XMSG
validationSourceContainerNonExist=Za izvorno povezavo morate izbrati zbirnik.
#XMSG
validationTargetNonExist=Za tok replikacije morate izbrati ciljno povezavo.
#XMSG
validationTargetContainerNonExist=Za ciljno povezavo morate izbrati zbirnik.
#XMSG
validationTruncateDisabledForObjectTitle=Podvajanje v shrambe objekta.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Podvajanje v shrambo v oblaku je mogoče le, če je možnost Izbris vseh pred nalaganjem nastavljena ali ciljni objekt ne obstaja v cilju.{0}{0}Če še vedno želite omogočiti podvajanje za objekte, za katere možnost Obreži ni nastavljena, zagotovite, da ciljni objekt ne obstaja v sistemu, in na nato zaženite tok podvajanja.
#XMSG
validationTaskNonExist=V toku replikacije mora biti vsaj eno replikacijo.
#XMSG
validationTaskTargetMissing=Imeti morate cilj za replikacijo z virom: {0}
#XMSG
validationTaskTargetIsSAC=Izbrani cilj je artefakt SAC: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Izbrani cilj ni podprta lokalna tabela: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Objekt s tem imenom že obstaja v cilju. Vendar tega objekta ne smete uporabiti za ciljni objekt toka podvajanja v lokalnem odložišču, saj ni lokalna tabela.
#XMSG
validateSourceTargetSystemDifference=Za tok replikacije morate izbrati različne kombinacije izvornih in ciljnih povezav ter zbirnikov.
#XMSG
validateDuplicateSources=Ena ali več replikacij ima podvojena imena izvornih objektov: {0}.
#XMSG
validateDuplicateTargets=Ena ali več replikacij ima podvojena imena ciljnih objektov: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=Izvorni objekt {0} ne podpira zajemanja delta, ciljni objekt {1} pa ga. Odstranite replikacijo.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Izbrati morate vrsto prenosa "Začetno in delta" za replikacijo z imenom ciljnega objekta {0}.
#XMSG
validationAutoRenameTarget=Ciljni stolpci so bili preimenovani.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Dodana je bila samodejna projekcija, naslednji ciljni stolpci pa so bili preimenovani za omogočenje podvojitve v cilj:{1}{1} {0} {1}{1}To je bilo potrebno zaradi enega od naslednjih razlogov:{1}{1}{2} Nepodprti znaki{1}{2} Rezervirana predpona
#XMSG
validationAutoRenameTargetDescriptionUpdated=Samodejna projekcija je bila dodana, naslednji ciljni stolpci pa so bili preimenovani za omogočenje podvojitve v Google BigQuery:{1}{1} {0} {1}{1}To je potrebno zaradi enega od naslednjih razlogov:{1}{1}{2} Rezervirano ime stolpca{1}{2} Nepodprti znaki{1}{2} Rezervirana predpona
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Samodejna projekcija je bila dodana, naslednji ciljni stolpci pa so bili preimenovani za omogočenje podvojitve v aplikaciji Confluent:{1}{1} {0} {1}{1}To je bilo potrebno zaradi enega od naslednjih razlogov: {1}{1}{2} Rezervirano ime stolpca{1}{2} Nepodprti znaki{1}{2} Rezervirana predpona
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Samodejna projekcija je bila dodana, naslednji ciljni stolpci pa so bili preimenovani za omogočenje podvojitve v cilju:{1}{1} {0} {1}{1}To je bilo potrebno zaradi enega od naslednjih razlogov: {1}{1}{2} Rezervirano ime stolpca{1}{2} Nepodprti znaki{1}{2} Rezervirana predpona
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Ciljni objekt je bil preimenovan.
#XMSG
autoRenameInfoDesc=Ciljni objekt je bil preimenovan, ker je vseboval nepodprte znake. Podprti so le naslednji znaki:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(pika){0}{1}_(podčrtaj ){0}{1}-(pomišljaj)
#XMSG
validationAutoTargetTypeConversion=Ciljni podatkovni tipi so bili spremenjeni.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Za naslednje ciljne stolpce so bili spremenjeni ciljni podatkovnih tipov, ker izvorni podatkovni tipi v Google BigQuery niso podprti:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Za naslednje ciljne stolpce so bili spremenjeni ciljni podatkovni tipi, ker izvorni podatkovni tipi v ciljni povezavi niso podprti:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Skrajšajte imena ciljnega stolpca.
#XMSG
validationMaxCharLengthGBQTargetDescription=V Google BigQuery lahko imena stolpca uporabijo največ 300 znakov. Uporabite projekcijo za krajšanje naslednjih imen ciljnega stolpca:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Primarnih ključev ni mogoče ustvariti.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=Google BigQuery podpira največ 16 primarnih ključev. Izvorni objekt ima večje število primarnih ključev. Primarni ključi ne bodo kreirani v ciljnem objektu.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=En ali več izvornih stolpcev ima podatkovne tipe, ki jih ni mogoče opredeliti kot primarni ključ v Google BigQuery. Primarni ključi ne bodo ustvarjeni v ciljnem objektu.{0}{0}Naslednji podatkovni tipi so združljivi s podatkovnimi tipi Google BigQuery, za katere je mogoče opredeliti primarni ključ :{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Določite enega ali več stolpcev kot primarni ključ.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Enega ali več stolpcev morate določiti kot primarni ključ, za kar uporabite pogovorno okno izvorne sheme.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Določite enega ali več stolpcev kot primarni ključ.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Določite morate enega ali več stolpcev kot primarni ključ, ki se ujema z omejitvami primarnega ključa za vaš izvorni objekt. Za to pojdite na Konfiguriranje sheme v lastnostih vašega izvornega objekta.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Vnesite veljavno največjo vrednost particije.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Največja vrednost particije mora biti ≥ 1 in ≤ 2147483647
#XMSG
validateHDLFNoPKDatasetError=Določite enega ali več stolpcev kot primarni ključ.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Da podvojite objekt, morate določiti enega ali več ciljnih stolpcev kot primarni ključ. Da to storite, uporabite projekcijo.
#XMSG
validateHDLFNoPKExistingDatasetError=Določite enega ali več stolpcev kot primarni ključ.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Da podvojite podatke v obstoječi ciljni objekt, morate imeti enega ali več stolpcev, ki so določeni kot primarni ključ. {0} Za določitev enega ali več stolpcev kot primarnega ključa imate na voljo naslednje možnosti: {0} {1} Z lokalnim urejevalnikom tabel spremenite obstoječi ciljni objekt. Nato ponovno naložite tok podvajanja.{0}{1} Preimenujte ciljni objekt v toku podvajanja. To bo ustvarilo nov objekt takoj, ko se začne izvajanje. Po preimenovanju lahko določite enega ali več stolpcev kot primarni ključ v projekciji.{0}{1} Preslikajte objekt v drug obstoječi ciljni objekt, v katerem je eden ali več stolpcev že določenih kot primarni ključ.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Izbrani cilj že obstaja v odložišču: {0}.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Imena tabele zajemanja delte že uporabljajo druge tabele v odložišču: {0} Te ciljne objekte morate preimenovati, da zagotovite, da so povezana imena tabel zajemanja delte enolična, preden lahko shranite tok podvajanja.
#XMSG
validateConfluentEmptySchema=Določitev sheme
#XMSG
validateConfluentEmptySchemaDescUpdated=Izvorna tabela nima sheme. Če jo želite določiti, izberite Konfiguracija sheme
#XMSG
validationCSVEncoding=Neveljavno kodiranje CSV
#XMSG
validationCSVEncodingDescription=Kodiranje CSV naloge ni veljavno.
#XMSG
validateConfluentEmptySchema=Izberite združljiv ciljni podatkovni tip
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Izberite združljiv ciljni podatkovni tip
#XMSG
globalValidateTargetDataTypeDesc=Pri preslikavah stolpcev je prišlo do napake. Pojdite na Projekcijo in preverite, ali so vsi izvorni stolpci preslikani z enoznačnim stolpcem, s stolpcem, ki ima združljiv podatkovni tip, in ali so vsi opredeljeni izrazi veljavni.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Podvojena imena stolpcev.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Podvojena imena stolpcev niso podprta. Uporabite pogovorno okno projekcije, da jih popravite. Naslednji ciljni objekti imajo podvojena imena stolpcev: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Podvojena imena stolpcev.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Podvojena imena stolpcev niso podprta. Naslednji ciljni objekti imajo podvojena imena stolpcev: {0}.
#XMSG
deltaOnlyLoadTypeTittle=V podatkih so lahko nekonsistentnosti.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Vrsta prenosa Le delta ne bo upoštevala sprememb, narejenih v viru med zadnjim shranjevanjem in naslednjim izvajanjem.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Spremenite vrsto nalaganja v "Začetno".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Podvajanje objektov na osnovi ABAP, ki nimajo primarnega ključa, je možnos samo za vrsto nalaganja "Samo začetno".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Onemogoči zajemanje delta.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Da bi podvojili objekt, ki nima primarnega ključa z izvorno povezavo vrste ABAP, morate najprej onemogočiti zajemanje delta za to tabelo.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Spremenite ciljni objekt.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Ciljnega objekta ni mogoče uporabiti, ker je omogočeno zajemanje delta. Lahko bodisi spremenite ciljni objekt in nato izklopite zajemanje delta za novi (preimenovani) objekt bodisi preslikate izvorni objekt v izvorni objekt, za katerega je zajemanje delta onemogočeno.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Spremenite ciljni objekt.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Ciljnega objekta ni mogoče uporabiti, ker nima zahtevanega tehničnega stolpca __load_package_id. Ciljni objekt lahko preimenujete z imenom, ki še ne obstaja. Sistem bo nato ustvaril nov objekt, ki bo enako opredeljen kot izvorni objekt in bo vseboval tehnični stolpec. Namesto tega lahko preslikate ciljni objekt v obstoječi objekt, ki ima zahtevani tehnični stolpec (__load_package_id).
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Spremenite ciljni objekt.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Ciljnega objekta ni mogoče uporabiti, ker nima zahtevanega tehničnega stolpca __load_record_id. Ciljni objekt lahko preimenujete z imenom, ki še ne obstaja. Sistem bo nato ustvaril nov objekt, ki bo enako opredeljen kot izvorni objekt in bo vseboval tehnični stolpec. Namesto tega lahko preslikate ciljni objekt v obstoječi objekt, ki ima zahtevani tehnični stolpec (__load_record_id).
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Spremenite ciljni objekt.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Ciljnega objekta ni mogoče uporabiti zaradi vrste podatkov njegovega tehničnega stolpca __load_record_id is not "string(44)". Ciljni objekt lahko preimenujete z imenom, ki še ne obstaja. Sistem bo nato ustvaril nov objekt, ki bo enako opredeljen kot izvorni objekt in bo posledično imel pravo vrsto podatkov. Namesto tega lahko preslikate ciljni objekt v obstoječi objekt, ki ima zahtevani tehnični stolpec (__load_record_id) z ustrezno vrsto podatkov.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Spremenite ciljni objekt.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Ciljnega objekta ni mogoče uporabiti, ker ima primarni ključ, medtem ko ga izvorni objekt nima. Ciljni objekt lahko preimenujete z imenom, ki še ne obstaja. Sistem bo nato ustvaril nov objekt, ki bo enako opredeljen kot izvorni objekt in posledično ne bo vseboval primarnega ključa. Namesto tega lahko preslikate ciljni objekt v obstoječi objekt, ki ima zahtevani tehnični stolpec (__load_package_id) in nima primarnega ključa.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Spremenite ciljni objekt.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Ciljnega objekta ni mogoče uporabiti, ker ima primarni ključ, medtem ko ga izvorni objekt nima. Ciljni objekt lahko preimenujete z imenom, ki še ne obstaja. Sistem bo nato ustvaril nov objekt, ki bo enako opredeljen kot izvorni objekt in posledično ne bo vseboval primarnega ključa. Namesto tega lahko preslikate ciljni objekt v obstoječi objekt, ki ima zahtevani tehnični stolpec (__load_record_id) in nima primarnega ključa.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Spremenite ciljni objekt.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Ciljnega objekta ni mogoče uporabiti zaradi vrste podatkov njegovega tehničnega stolpca __load_package_id is not "binary(>=256)". Ciljni objekt lahko preimenujete z imenom, ki še ne obstaja. Sistem bo nato ustvaril nov objekt, ki bo enako opredeljen kot izvorni objekt in bo posledično imel pravo vrsto podatkov. Namesto tega lahko preslikate ciljni objekt v obstoječi objekt, ki ima zahtevani tehnični stolpec (__load_package_id) z ustrezno vrsto podatkov.
#XMSG
validationAutoRenameTargetDPID=Ciljni stolpci so bili preimenovani.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Odstranite izvorni objekt.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Izvorni objekt nima stolpca Ključ, ki ni podprt v tem kontekstu.
#XMSG
validationAutoRenameTargetDPIDDescription=Samodejna projekcija je bila dodana, naslednji ciljni stolpci pa so bili preimenovani za omogočenje podvojitve iz vira ABAP brez ključev:{1}{1} {0} {1}{1} To je potrebno zaradi enega od naslednjih razlogov: {1}{1}{2} Rezervirano ime stolpca{1}{2} Nepodprti znaki{1}{2} Rezervirana predpona
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Podvajanje v {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Shranjevanje in postavitev tokov podvajanja, ki imajo {0} za cilj trenutno nista mogoča, ker izvajamo vzdrževanje te funkcije.
#XMSG
TargetColumnSkippedLTF=Ciljni stolpec je bil preskočen.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Ciljni stolpec je bil preskočen zaradi nepodprtega podatkovnega tipa. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Časovni stolpec kot primarni ključ.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Izvorni objekt ima časovni stolpec s primarnim ključem, ki ni podprt v tem kontekstu.
#XMSG
validateNoPKInLTFTarget=Manjka primarni ključ.
#XMSG
validateNoPKInLTFTargetDescription=Primarni ključ ni določen v cilju, kar ni podprto v tem kontekstu.
#XMSG
validateABAPClusterTableLTF=Tabela gruč ABAP.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Izvorni objekt je v tabeli gruč ABAP, ki ni podprta v tem kontekstu.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Videti je, da še niste dodali podatkov.
#YINS
welcomeText2=Za zagon toka replikacije izberite povezavo in izvorni objekt na levi.

#XBUT
wizStep1=Izberite izvorno povezavo
#XBUT
wizStep2=Izberite izvorni vsebnik
#XBUT
wizStep3=Dodaj izvorne objekte

#XMSG
limitDataset=Največje število objektov je preseženo. Odstranite obstoječe objekte in dodajte nove ali ustvarite nov tok replikacije.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Toka replikacije do te ne-SAP-jeve ciljne povezave, ni mogoče zagnati, ker za ta mesec ni na voljo nobene izhodne količine.
#XMSG
premiumOutBoundRFAdminWarningMsg=Skrbnik lahko poveča premijske izhodne blokade za tega najemnika, tako da bo izhodna količina na voljo za ta mesec.
#XMSG
messageForToastForDPIDColumn2=Nov stolpec dodan v cilj za {0} objektov - potrebno za ravnanje s podvojenimi zapisi v zvezi z izvornimi objekti na osnovi ABAP, ki nimajo primarnega ključa.
#XMSG
PremiumInboundWarningMessage=Glede na število tokov replikacije in količino podatkov, ki jih je treba replicirati, lahko resursi SAP HANA{0}, potrebni za replikacijo podatkov{1}, presežejo razpoložljive zmogljivosti vašega najemnika.
#XMSG
PremiumInboundWarningMsg=Glede na število tokov replikacije in količino podatkov, ki jih je treba replicirati, lahko {0}resursi SAP HANA, potrebni za replikacijo podatkov{1}, presežejo razpoložljive zmogljivosti vašega najemnika.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Vnesite ime projekcije.
#XMSG
emptyTargetColumn=Vnesite ime ciljnega stolpca.
#XMSG
emptyTargetColumnBusinessName=Vnesite poslovno ime ciljnega stolpca
#XMSG
invalidTransformName=Vnesite ime projekcije.
#XMSG
uniqueColumnName=Preimenujte ciljni stolpec.
#XMSG
copySourceColumnLbl=Kopirajte stolpce iz izvornega objekta
#XMSG
renameWarning=Prepričajte se, da boste pri preimenovanju ciljne tabele izbrali enoznačni naziv. Če table z novim imenom že obstaja v prostoru, bo uporabila definicijo te tabele.

#XMSG
uniqueColumnBusinessName=Preimenujte poslovno ime ciljnega stolpca:
#XMSG
uniqueSourceMapping=Izberite drug izvorni stolpec.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=Izvorni stolpec {0} že uporabljajo naslednji ciljni stolpci:{1}{1}{2}{1}{1} Za ta ciljni stolpec ali za druge ciljne stolpce izberite izvorni stolpec, ki še ni v uporabi, da shranite projekcijo.
#XMSG
uniqueColumnNameDescription=Ime ciljnega stolpca, ki ste ga vnesli, že obstaja. Če želite shraniti projekcijo, morate tukaj vnesti enolično ime stolpca.
#XMSG
uniqueColumnBusinessNameDesc=Poslovno ime ciljnega stolpca že obstaja. Če želite shraniti projekcijo, vnesite enolično poslovno ime stolpca.
#XMSG
emptySource=Izberite izvorni stolpec ali vnesite konstanto.
#XMSG
emptySourceDescription=Če želite ustvariti veljavni vnos preslikave, morate izbrati izvorni stolpec ali vnesti vrednost konstante.
#XMSG
emptyExpression=Opredelite preslikavo.
#XMSG
emptyExpressionDescription1=Izberite izvorni stolpec, v katerega želite preslikati ciljni stolpec, ali izberite potrditveno polje v stolpcu Funkcije{0} konstante {1}. {2} {2} Funkcije se avtomatično vnesejo glede na tip ciljnih podatkov. Vrednosti konstante lahko vnesete ročno.
#XMSG
numberExpressionErr=Vnesite številko.
#XMSG
numberExpressionErrDescription=Izbrali ste številski podatkovni tip. To pomeni, da lahko vnesete le številke in decimalno mesto, če je to potrebno. Ne uporabljajte enojnih narekovajev.
#XMSG
invalidLength=Vnesite veljavno vrednost dolžine.
#XMSG
invalidLengthDescription=Dolžina podatkovnega tipa mora biti enaka ali večja od dolžine izvornega stolpca in je lahko med 1 in 5000.
#XMSG
invalidMappedLength=Vnesite veljavno vrednost dolžine.
#XMSG
invalidMappedLengthDescription=Dolžina podatkovnega tipa mora biti enaka ali večja od dolžine izvornega stolpca {0} in je lahko med 1 in 5000.
#XMSG
invalidPrecision=Vnesite veljavno vrednost natančnosti.
#XMSG
invalidPrecisionDescription=Natančnost določa  število števk. Lestvica določa število števk po decimalnem mestu in je lahko med 0 in natančnostjo.{0}{0} Primeri: {0}{1} Natančnost 6, lestvica 2 ustreza številkam, kot je 1234,56.{0}{1} Natančnost 6, lestvica 6 ustreza številkam, kot je 0,123546.{0} {0} Natančnost in lestvica za cilj morata biti združljiva z natančnostjo in lestvico za izvor, da bodo vse števke iz izvora imele prostor v ciljnem polju. Na primer, če imate natančnost 6 in lestvico 2 v izvoru (in posledično števke, ki niso 0 pred decimalnim mestom), ne morete vnesti natančnosti 6 in lestvice 6 v cilju.
#XMSG
invalidPrimaryKey=Vnesite vsaj en primarni ključ.
#XMSG
invalidPrimaryKeyDescription=Primarni ključ ni določen za to shemo.
#XMSG
invalidMappedPrecision=Vnesite veljavno vrednost natančnosti.
#XMSG
invalidMappedPrecisionDescription1=Natančnost določa  število števk. Lestvica določa število števk po decimalnem mestu in je lahko med 0 in natančnostjo.{0}{0} Primeri: {0}{1} Natančnost 6, lestvica 2 ustreza številkam, kot je 1234,56.{0}{1} Natančnost 6, lestvica 6 ustreza številkam, kot je 0,123546.{0}{0}Natančnost podatkovnega tipa mora biti enak ali večji od natančnosti izvora ({2}).
#XMSG
invalidScale=Vnesite veljavno vrednost lestvice.
#XMSG
invalidScaleDescription=Natančnost določa  število števk. Lestvica določa število števk po decimalnem mestu in je lahko med 0 in natančnostjo.{0}{0} Primeri: {0}{1} Natančnost 6, lestvica 2 ustreza številkam, kot je 1234,56.{0}{1} Natančnost 6, lestvica 6 ustreza številkam, kot je 0,123546.{0} {0} Natančnost in lestvica za cilj morata biti združljiva z natančnostjo in lestvico za izvor, da bodo vse števke iz izvora imele prostor v ciljnem polju. Na primer, če imate natančnost 6 in lestvico 2 v izvoru (in posledično števke, ki niso 0 pred decimalnim mestom), ne morete vnesti natančnosti 6 in lestvice 6 v cilju.
#XMSG
invalidMappedScale=Vnesite veljavno vrednost lestvice.
#XMSG
invalidMappedScaleDescription1=Natančnost določa število števk. Velikost določa število števk za decimalnim mestom in je lahko med 0 in natančnostjo.{0}{0} Primeri: {0}{1} Natančnost 6, velikost 2 pomeni števila, kot je 1234,56.{0}{1} Natančnost 6, velikost 6 pomeni števila, kot je 0,123546.{0}{0} Velikost vrste podatkov mora biti enaka ali večja od velikosti izvora ({2}).
#XMSG
nonCompatibleDataType=Izberite združljivi ciljni podatkovni tip.
#XMSG
nonCompatibleDataTypeDescription1=Podatkovni tip, ki ste ga tukaj navedli, mora biti združljiv z izvornim podatkovnim tipom ({0}). {1}{1} Primer: če ima vaš izvorni stolpec niz podatkovnega tipa in vsebuje črke, za cilj ne morete uporabiti decimalnega podatkovnega tipa.
#XMSG
invalidColumnCount=Izberite izvorni stolpec.
#XMSG
ObjectStoreInvalidScaleORPrecision=Vnesite veljavno vrednost za natančnost in merilo.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=Prva vrednost je natančnost, ki določa skupno število števk. Druga vrednost je lestvica, ki določa števke za decimalno vejico. Vnesite ciljno vrednost lestvice, ki je večja od vrednosti izvorne lestvice, in se prepričajte, da je razlika med vneseno ciljno vrednostjo lestvice in natančnostjo večja od razlike med izvorno vrednostjo lestvice in natančnostjo.
#XMSG
InvalidPrecisionORScale=Vnesite veljavno vrednost za natančnost in merilo.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=Prva vrednost je natančnost, ki določa skupno število števk. Druga vrednost je lestvica, ki določa števke za decimalno vejico.{0}{0}Ker tip izvornih podatkov ni podprt v Google BigQuery, se pretvori v tip ciljnih podatkov DECIMAL. V tem primeru je lahko natančnost določena samo med 38 in 76, lestvica pa med 9 in 38. Poleg tega mora biti rezultat natančnosti minus lestvice, ki predstavlja števke pred decimalno vejico, med 29 in 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=Prva vrednost je natančnost, ki določa skupno število števk. Druga vrednost je lestvica, ki določa števke za decimalno vejico.{0}{0}Ker tip izvornih podatkov ni podprt v Google BigQuery, se pretvori v tip ciljnih podatkov DECIMAL. V tem primeru mora biti natančnost določena kot 20 ali več. Poleg tega mora biti rezultat natančnosti minus lestvice, ki odraža števke pred decimalno vejico, 20 ali več.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=Prva vrednost je natančnost, ki določa skupno število števk. Druga vrednost je merilo, ki določa števke za decimalno vejico.{0}{0}Ker izvorni podatkovni tip ni podprt v cilju, se pretvori v ciljni podatkovni tip DECIMAL. V tem primeru mora biti natančnost določena s katerokoli številko, večjo od ali enako 1 in manjšo od ali enako 38, in merilo mora biti manjše od ali enako natančnosti.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=Prva vrednost je natančnost, ki določa skupno število števk. Druga vrednost je lestvica, ki določa števke za decimalno vejico.{0}{0}Ker tip izvornih podatkov ni podprt v cilju, se pretvori v tip ciljnih podatkov DECIMAL. V tem primeru mora biti natančnost določena kot 20 ali več. Poleg tega mora biti rezultat natančnosti minus lestvice, ki odraža števke pred decimalno vejico, 20 ali več.
#XMSG
invalidColumnCountDescription=Če želite ustvariti veljavni vnos preslikave, morate izbrati izvorni stolpec ali vnesti vrednost konstante.
#XMSG
duplicateColumns=Preimenujte ciljni stolpec.
#XMSG
duplicateGBQCDCColumnsDesc=Ime ciljnega stolpca je rezervirano v Google BigQuery. Če želite shraniti projekcijo, ga morate preimenovati.
#XMSG
duplicateConfluentCDCColumnsDesc=Ime ciljnega stolpca je rezervirano v Confluent. Če želite shraniti projekcijo, ga morate preimenovati.
#XMSG
duplicateSignavioCDCColumnsDesc=Ime ciljnega stolpca je rezervirano v SAP Signavio. Če želite shraniti projekcijo, ga morate preimenovati.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Ime ciljnega stolpca je rezervirano v MS OneLake. Če želite shraniti projekcijo, ga morate preimenovati.
#XMSG
duplicateSFTPCDCColumnsDesc=Ime ciljnega stolpca je rezervirano v SFTP. Če želite shraniti projekcijo, ga morate preimenovati.
#XMSG
GBQTargetNameWithPrefixUpdated1=Ime ciljnega stolpca vsebuje predpono, ki je rezervirana v Google BigQuery. Da bo mogoče shraniti projekcijo, morate preimenovati stolpec. {0}{0}Ime ciljnega stolpca se ne sme začeti s katerim od teh nizov:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Skrajšajte ime ciljnega stolpca.
#XMSG
GBQtargetMaxLengthDesc=V Google BigQuery ima lahko ime stolpca največ 300 znakov. Skrajšajte ime ciljnega stolpca, da boste lahko shranili projekcijo.
#XMSG
invalidMappedScalePrecision=Natančnost in lestvica za cilj mora biti združljiva z natančnostjo in lestvico za izvor, da bodo vse števke iz vira imele prostor v ciljnem polju.
#XMSG
invalidMappedScalePrecisionShortText=Vnesite veljavno natančnost in vrednost lestvice.
#XMSG
validationIncompatiblePKTypeDescProjection3=En ali več izvornih stolpcev ima podatkovne tipe, ki jih ni mogoče opredeliti kot primarni ključ v Google BigQuery. Primarni ključi ne bodo ustvarjeni v ciljnem objektu.{0}{0}Naslednji podatkovni tipi so združljivi s podatkovnimi tipi Google BigQuery, za katere je mogoče opredeliti primarni ključ :{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Odznačite stolpec __message_id.
#XMSG
uncheckColumnMessageIdDesc=Stolpec: Primarni ključ
#XMSG
validationOpCodeInsert=Za Vnos morate vnesti vrednost.
#XMSG
recommendDifferentPrimaryKey=Priporočamo, da izberete drugačen primarni ključ na ravni postavke.
#XMSG
recommendDifferentPrimaryKeyDesc=Ko je koda postopka že določena, je priporočljivo izbrati različne primarne ključe za indeks matrike in elemente, da se izognete težavam, kot je na primer podvajanje stolpcev.
#XMSG
selectPrimaryKeyItemLevel=Izbrati morate vsaj en primarni ključ za raven glave in raven elementa.
#XMSG
selectPrimaryKeyItemLevelDesc=Ko je matrika ali zemljevid razširjen, morate izbrati dva primarna ključa, enega na ravni glave in enega na ravni elementa.
#XMSG
invalidMapKey=Izbrati morate vsaj en primarni ključ na ravni glave.
#XMSG
invalidMapKeyDesc=Ko je matrika ali zemljevid razširjen, morate izbrati primarni ključ na ravni glave.
#XFLD
txtSearchFields=Iskanje ciljnih stolpcev
#XFLD
txtName=Ime
#XMSG
txtSourceColValidation=Eden ali več izvornih stolpcev ni podprtih:
#XMSG
txtMappingCount=Preslikave ({0})
#XMSG
schema=Shema
#XMSG
sourceColumn=Izvorni stolpci
#XMSG
warningSourceSchema=Vsaka sprememba v shemi bo vplivala na preslikave v pogovornem oknu projekcije.
#XCOL
txtTargetColName=Ciljni stolpec (tehnično ime)
#XCOL
txtDataType=Ciljni podatkovni tip
#XCOL
txtSourceDataType=Vrsta izvornih podatkov
#XCOL
srcColName=Izvorni stolpec (tehnično ime)
#XCOL
precision=Natančnost
#XCOL
scale=Lestvica
#XCOL
functionsOrConstants=Funkcije/konstante
#XCOL
txtTargetColBusinessName=Ciljni stolpec (poslovno ime)
#XCOL
prKey=Primarni ključ
#XCOL
txtProperties=Lastnosti
#XBUT
txtOK=Shrani
#XBUT
txtCancel=Preklic
#XBUT
txtRemove=Odstrani
#XFLD
txtDesc=Opis
#XMSG
rftdMapping=Preslikava
#XFLD
@lblColumnDataType=Podatkovni tip
#XFLD
@lblColumnTechnicalName=Tehnično ime
#XBUT
txtAutomap=Samodejna preslikava
#XBUT
txtUp=Navzgor
#XBUT
txtDown=Navzdol

#XTOL
txtTransformationHeader=Projekcija
#XTOL
editTransformation=Uredi
#XTOL
primaryKeyToolip=Ključ


#XMSG
rftdFilter=Filter
#XMSG
rftdFilterColumnCount=Izvor: {0}({1})
#XTOL
rftdFilterColSearch=Iskanje
#XMSG
rftdFilterColNoData=Ni stolpcev za prikaz
#XMSG
rftdFilteredColNoExps=Brez izrazov filtra
#XMSG
rftdFilterSelectedColTxt=Dodajanje filtra za
#XMSG
rftdFilterTxt=Filter je na voljo za
#XBUT
rftdFilterSelectedAddColExp=Dodaj izraz
#YINS
rftdFilterNoSelectedCol=Izberite stolpec, da dodate filter.
#XMSG
rftdFilterExp=Izraz filtra
#XMSG
rftdFilterNotAllowedColumn=Dodajanje filtrov ni podprto za ta stolpec.
#XMSG
rftdFilterNotAllowedHead=Nepodprt stolpec
#XMSG
rftdFilterNoExp=Noben filter ni določen
#XTOL
rftdfilteredTt=Filtrirano
#XTOL
rftdremoveexpTt=Odstrani izraz filtra
#XTOL
validationMessageTt=Sporočila preverjanja veljavnosti
#XTOL
rftdFilterDateInp=Izberite datum
#XTOL
rftdFilterDateTimeInp=Izberite datum in čas
#XTOL
rftdFilterTimeInp=Izberite čas
#XTOL
rftdFilterInp=Vnos vrednosti
#XMSG
rftdFilterValidateEmptyMsg=Izrazi filtra {0} v stolpcu {1} so prazni
#XMSG
rftdFilterValidateInvalidNumericMsg=Izrazi filtra {0} v stolpcu {1} vsebujejo neveljavne numerične vrednosti
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Izraz filtra mora vsebovati veljavne numerične vrednosti
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Če je bila shema ciljnega objekta spremenjena, uporabite funkcijo "Preslikaj v obstoječi ciljni objekt" na glavni strani, da prilagodite spremembe in znova preslikate ciljni objekt z njegovim virom.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Če ciljna tabela že obstaja in preslikava vključuje spremembo sheme, morate ciljno tabelo ustrezno spremeniti, preden postavite tok replikacije.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Če preslikava vključuje spremembo sheme, morate ciljno tabelo ustrezno spremeniti, preden postavite tok replikacije.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Naslednji nepodprti stolpci so bili preskočeni iz opredelitve vira: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Naslednji nepodprti stolpci so bili preskočeni iz opredelitve cilja: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Naslednji objekti niso podprti, ker so izpostavljeni za porabo: {0} {1} {0} {0} Če želite uporabiti tabele v toku podvajanja, semantična uporaba (v nastavitvah tabele) ne sme biti nastavljena na {2}Analitični podatkovni niz{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Ciljnega objekta ni mogoče uporabiti, ker je izpostavljen za porabo. {0} {0} Če želite uporabiti tabelo v toku podvajanja, semantična uporaba (v nastavitvah tabele) ne sme biti nastavljena na {1}Analitični podatkovni niz{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Ciljni objekt s tem imenom že obstaja. Vendar ga ni mogoč uporabiti, ker je izpostavljen za porabo. {0} {0}Če želite uporabiti tabelo v toku podvajanja, semantična uporaba (v nastavitvah tabele) ne sme biti nastavljena na {1}Analitični podatkovni niz{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Objekt s tem imenom že obstaja v cilju. {0}Vendar tega objekta ne smete uporabiti za ciljni objekt toka podvajanja v lokalnem odložišču, saj ni lokalna tabela.
#XMSG:
targetAutoRenameUpdated=Ciljni stolpci so bili preimenovani.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Ciljni stolpec je bil preimenovan za omogočenje podvojitve v Google BigQuery. Temu je tako iz naslednjih razlogov:{0} {1}{2}Rezervirano ime stolpca{3}{2}nepodprti znaki{3}{2}rezervirana predpona{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Ciljni stolpec je bil preimenovan za omogočenje podvajanj v aplikaciji Confluent. To je bilo potrebno zaradi enega od naslednjih razlogov:{0} {1}{2}Rezervirano ime stolpca{3}{2}Nepodprti znaki{3}{2}Rezervirana predpona{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Ciljni stolpec je bil preimenovan za omogočenje podvajanj v cilju. To je bilo potrebno zaradi enega od naslednjih razlogov:{0} {1}{2}Nepodprti znaki{3}{2}Rezervirana predponaC{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Ciljni stolpec je bil preimenovan za omogočenje podvajanj v cilju. To je bilo potrebno zaradi enega od naslednjih razlogov:{0} {1}{2}Rezervirano ime stolpca{3} {2}Nepodprti znaki{3}{2}Rezervirana predpona{3}{4}
#XMSG:
targetAutoDataType=Ciljni podatkovni tip je bil spremenjen.
#XMSG:
targetAutoDataTypeDesc=Ciljni podatkovni tip je bil spremenjen na {0}, ker tip izvornih podatkov ni podprt v Google BigQuery.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Ciljni podatkovni tip je bil spremenjen v {0}, ker izvorni podatkovni tip ni podprt v ciljni povezavi.
#XMSG
projectionGBQUnableToCreateKey=Primarnih ključev ni mogoče ustvariti.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=Google BigQuery podpira največ 16 primarnih ključev. Izvorni objekt ima večje število primarnih ključev. Primarni ključi ne bodo kreirani v ciljnem objektu.
#XMSG
HDLFNoKeyError=Določite enega ali več stolpcev kot primarni ključ.
#XMSG
HDLFNoKeyErrorDescription=Da podvojite objekt, morate določiti enega ali več ciljnih stolpcev kot primarni ključ.
#XMSG
HDLFNoKeyErrorExistingTarget=Določite enega ali več stolpcev kot primarni ključ.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Da podvojite podatke v obstoječi ciljni objekt, morate imeti enega ali več stolpcev, ki so določeni kot primarni ključ.{0} {0} Za določitev enega ali več stolpcev kot primarnega ključa imate na voljo naslednje možnosti:{0}{0}{1} Z lokalnim urejevalnikom tabel spremenite obstoječi ciljni objekt. Nato ponovno naložite tok podvajanja.{0}{0}{1} Preimenujte ciljni objekt v toku podvajanja. To bo ustvarilo nov objekt takoj, ko se začne izvajanje. Po preimenovanju lahko določite enega ali več stolpcev kot primarni ključ v projekciji.{0}{0}{1} Preslikajte objekt v drug obstoječi ciljni objekt, v katerem je eden ali več stolpcev že določenih kot primarni ključ.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Primarni ključ je spremenjen.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=V primerjavi z izvornim objektom ste kot primarni ključ ciljnega objekta določili različne stolpce. Poskrbite, da bodo ti stolpci enoznačno določali vse vrstice, da se izognete morebitnim poškodbam podatkov pri poznejšem podvajanju podatkov. {0} {0} V izvornem objektu so kot primarni ključ določeni naslednji stolpci: {0} {1}
#XMSG
duplicateDPIDColumns=Preimenujte ciljni stolpec.
#XMSG
duplicateDPIDDColumnsDesc1=Ime ciljnega stolpca je rezervirano za tehnični stolpec. Vnesite drugačno ime, da shranite projekcijo.
#XMSG:
targetAutoRenameDPID=Ciljni stolpci so bili preimenovani.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Ciljni stolpec je bil preimenovan za omogočenje podvajanj iz vira ABAP brez ključev. To je bilo potrebno zaradi enega od naslednjih razlogov:{0} {1}{2} Rezervirano ime stolpca{3}{2}Nepodprti znaki{3}{2}Rezervirana predpona{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle={0} ciljnih nastavitev
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle={0} izvornih nastavitev
#XBUT
connectionSettingSave=Shrani
#XBUT
connectionSettingCancel=Preklic
#XBUT: Button to keep the object level settings
txtKeep=Ohrani
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Prepiši
#XFLD
targetConnectionThreadlimit=Omejitev ciljnih niti za prenos začetnih podatkov (1-100)
#XFLD
connectionThreadLimit=Omejitev izvornih niti za prenos začetnih podatkov (1-100)
#XFLD
maxConnection=Omejitev niti podvajanja (1-100)
#XFLD
kafkaNumberOfPartitions=Število particij
#XFLD
kafkaReplicationFactor=Faktor replikacije
#XFLD
kafkaMessageEncoder=Kodirnik sporočil
#XFLD
kafkaMessageCompression=Stiskanje sporočila
#XFLD
fileGroupDeltaFilesBy=Združevanje delt po
#XFLD
fileFormat=Vrsta datoteke
#XFLD
csvEncoding=Kodiranje CSV
#XFLD
abapExitLbl=Izhod ABAP
#XFLD
deltaPartition=Štetje niti objekta za prenos delta podatkov (1-10)
#XFLD
clamping_Data=Neuspelo pri krajšanju podatkov
#XFLD
fail_On_Incompatible=Neuspelo pri nezdružljivih podatkih
#XFLD
maxPartitionInput=Maksimalno število particij
#XFLD
max_Partition=Določite največje število particij
#XFLD
include_SubFolder=Vključi podmape
#XFLD
fileGlobalPattern=Globalni vzorec za ime datoteke
#XFLD
fileCompression=Stiskanje datoteke
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Ločilo datoteke
#XFLD
fileIsHeaderIncluded=Glava datoteke
#XFLD
fileOrient=Usmerjenost
#XFLD
gbqWriteMode=Način zapisovanja
#XFLD
suppressDuplicate=Skrij dvojnike
#XFLD
apacheSpark=Omogoči združljivost z Apache Spark
#XFLD
clampingDatatypeCb=Omejitev podatkovnih tipov z decimalno plavajočo vejico
#XFLD
overwriteDatasetSetting=Prepiši ciljne nastavitve na ravni objekta
#XFLD
overwriteSourceDatasetSetting=Prepiši izvorne nastavitve na ravni objekta
#XMSG
kafkaInvalidConnectionSetting=Vnesite število med {0} in {1}.
#XMSG
MinReplicationThreadErrorMsg=Vnesite številko, večjo od {0}.
#XMSG
MaxReplicationThreadErrorMsg=Vnesite številko, manjšo od {0}.
#XMSG
DeltaThreadErrorMsg=Vnesite vrednost med 1 in 10.
#XMSG
MaxPartitionErrorMsg=Vnesite vrednost med 1 <= x <= 2147483647. Privzeta vrednost je 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Vnesite celo število med {0} in {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Uporaba faktorja podvajanja posrednika
#XFLD
serializationFormat=Oblika serializacije
#XFLD
compressionType=Vrsta stiskanja
#XFLD
schemaRegistry=Uporaba registra shem
#XFLD
subjectNameStrat=Strategija imena subjekta
#XFLD
compatibilityType=Vrsta združljivosti
#XFLD
confluentTopicName=Ime teme
#XFLD
confluentRecordName=Ime zapisa
#XFLD
confluentSubjectNamePreview=Predogled imena subjekta
#XMSG
serializationChangeToastMsgUpdated2=Oblika serializacije je spremenjena v JSON, ker register shem ni omogočen. Če želite obliko serializacije spremeniti nazaj v AVRO, morate najprej omogočiti register shem.
#XBUT
confluentTopicNameInfo=Ime teme vedno temelji na imenu ciljnega objekta. Spremenite ga lahko tako, da preimenujete ciljni objekt.
#XMSG
emptyRecordNameValidationHeaderMsg=Vnesite ime zapisa.
#XMSG
emptyPartionHeader=Vnesite število particij.
#XMSG
invalidPartitionsHeader=Vnesite veljavno število particij.
#XMSG
invalidpartitionsDesc=Vnesite število med 1 in 200.000.
#XMSG
emptyrFactorHeader=Vnesite faktor podvajanja.
#XMSG
invalidrFactorHeader=Vnesite veljaven faktor podvajanja.
#XMSG
invalidrFactorDesc=Vnesite številko med 1 in 32.767.
#XMSG
emptyRecordNameValidationDescMsg=Če je v uporabi oblika serializacije "AVRO", so podprti le naslednji znaki:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(podčrtaj)
#XMSG
validRecordNameValidationHeaderMsg=Vnesite veljavno ime zapisa.
#XMSG
validRecordNameValidationDescMsgUpdated=Ker je v uporabi oblika serializacije "AVRO", mora ime zapisa vsebovati samo črkovno-številske znake (A-Z, a-z, 0-9) in podčrtaje (_). Začeti se mora s črko ali podčrtajem.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled="Štetje niti objekta za prenos delta podatkov" lahko nastavite, ko ima eden ali več objektov vrsto prenosa "Začetno in delta".
#XMSG
invalidTargetName=Neveljavno ime stolpca
#XMSG
invalidTargetNameDesc=Ime ciljnega stolpca mora vsebovati samo črkovno-številske znake (A-Z, a-z, 0-9) in podčrtaj (_).
#XFLD
consumeOtherSchema=Poraba ostalih različic sheme
#XFLD
ignoreSchemamissmatch=Prezri neujemanje shem
#XFLD
confleuntDatatruncation=Krajšanje podatkov ni uspelo
#XFLD
isolationLevel=Raven izoliranosti
#XFLD
confluentOffset=Izhodišče
#XFLD
signavioGroupDeltaFilesByText=Brez
#XFLD
signavioFileFormatText=Parket
#XFLD
signavioSparkCompatibilityParquetText=Ne
#XFLD
siganvioFileCompressionText=Hitro
#XFLD
siganvioSuppressDuplicatesText=Ne

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Projekcije
#XBUT
txtAdd=Dodaj
#XBUT
txtEdit=Uredi
#XMSG
transformationText=Dodajte projekcijo za nastavitev filtra ali preslikave.
#XMSG
primaryKeyRequiredText=Izberite primarni ključ s konfiguracijo sheme.
#XFLD
lblSettings=Nastavitve
#XFLD
lblTargetSetting={0}: Ciljne nastavitve
#XMSG
@csvRF=Izberite datoteko, ki vsebuje definicijo shemo, ki jo želite uporabiti za vse datoteke v mapi.
#XFLD
lblSourceColumns=Izvorni stolpci
#XFLD
lblJsonStructure=Struktura JSON
#XFLD
lblSourceSetting={0}: Izvorne nastavitve
#XFLD
lblSourceSchemaSetting={0}: Izvorne nastavitve sheme
#XBUT
messageSettings=Nastavitve sporočil
#XFLD
lblPropertyTitle1=Lastnosti objekta
#XFLD
lblRFPropertyTitle=Lastnosti toka podvajanja
#XMSG
noDataTxt=Ni stolpcev za prikaz.
#XMSG
noTargetObjectText=Ni izbranega ciljnega objekta.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Ciljni stolpci
#XMSG
searchColumns=Iskanje stolpcev
#XTOL
cdcColumnTooltip=Stolpec za zajemanje delta
#XMSG
sourceNonDeltaSupportErrorUpdated=Izvorni objekt ne podpira zajemanja delta.
#XMSG
targetCDCColumnAdded=2 ciljna stolpca sta bila dodana za zajemanje delta.
#XMSG
deltaPartitionEnable=Omejitev niti objekta za prenose delta podatkov je dodana v izvorne nastavitve.
#XMSG
attributeMappingRemovalTxt=Odstranitev neveljavnih preslikav, ki niso podprte za nov ciljni objekt.
#XMSG
targetCDCColumnRemoved=2 ciljna stolpca, ki se uporabljata za zajemanje delta, sta bila odstranjena.
#XMSG
replicationLoadTypeChanged=Vrsta prevzema spremenjena na "Začetno in delta".
#XMSG
sourceHDLFLoadTypeError=Spremeni vrsto prevzema na "Začetno in delta".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Če želite podvojiti objekt iz izvorne povezave z vrsto povezave SAP HANA Cloud, Data Lake Files v SAP Datasphere, morate uporabiti vrsto prevzema "Začetno in delta".
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Omogoči zajemanje delta
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Če želite podvojiti objekt iz izvorne povezave z vrsto povezave SAP HANA Cloud, Data Lake Files v SAP Datasphere, morate omogočiti zajemanje delta.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Spremenite ciljni objekt.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Ciljnega objekta ni mogoče uporabiti, ker je onemogočeno zajemanje delta. Ciljni objekt lahko preimenujete (kar omogoči ustvarjanje novega objekta z zajemanjem delta) ali ga preslikate na obstoječi objekt z omogočenim zajemanjem delta.
#XMSG
deltaPartitionError=Vnesite veljavno štetje niti objekta za prenose delta podatkov.
#XMSG
deltaPartitionErrorDescription=Vnesite vrednost med 1 in 10.
#XMSG
deltaPartitionEmptyError=Vnesite štetje niti objekta za prenose delta podatkov.
#XFLD
@lblColumnDescription=Opis
#XMSG
@lblColumnDescriptionText1=Za tehnične namene - ravnanje s podvojenimi zapisi, ki so posledica težav med podvajanjem izvornih objektov na osnovi ABAP, ki nimajo primarnega ključa.
#XFLD
storageType=Prostor za shranjevanje
#XFLD
skipUnmappedColLbl=Preskok nepreslikanih stolpcev
#XFLD
abapContentTypeLbl=Vrsta vsebine
#XFLD
autoMergeForTargetLbl=Samodejna združitev podatkov
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Splošno
#XFLD
lblBusinessName=Poslovno ime
#XFLD
lblTechnicalName=Tehnično ime
#XFLD
lblPackage=Paket
#XFLD
statusPanel=Status izvajanja
#XBTN: Schedule dropdown menu
SCHEDULE=Časovni načrt
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Uredi časovni načrt
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Izbriši časovni načrt
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Ustvari časovni načrt
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Neuspešno preverjanje veljavnosti časovnega načrta
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Časovnega načrta ni mogoče ustvariti, ker se trenutno vzpostavlja tok replikacije.{0}Počakajte, da se vzpostavi tok replikacije.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Za tokove podvajanja, ki vsebujejo objekte z vrsto prevzema "Začetno in delta", ni mogoče ustvariti časovnega načrta.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Za tokove podvajanja, ki vsebujejo objekte z vrsto prevzema "Začetno in Delta/samo Delta", ni mogoče ustvariti časovnega načrta.
#XFLD : Scheduled popover
SCHEDULED=Načrtovano
#XFLD
CREATE_REPLICATION_TEXT=Ustvari tok replikacije
#XFLD
EDIT_REPLICATION_TEXT=Uredi tok replikacije
#XFLD
DELETE_REPLICATION_TEXT=Izbriši tok replikacije
#XFLD
REFRESH_FREQUENCY=Pogostost
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Toka podvajanja ni mogoče vzpostaviti, ker obstoječi časovni načrt {0} ne podpira vrste prevzema "Začetno in delta". {0}{0}Da vzpostavite tok podvajanja, morate nastaviti vrste prevzema vseh objektov {0} na "Samo začetno". Druga možnost je, da izbrišete časovni načrt, vzpostavite tok podvajanja {0} in nato zaženete novo izvajanje. Posledica tega je izvajanje brez {0}konca, ki podpira tudi objekte z vrsto prevzema "Začetno in delta".
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Toka podvajanja ni mogoče vzpostaviti, ker obstoječi časovni načrt {0} ne podpira vrste prevzema "Začetno in Delta/samo Delta". {0}{0}Da vzpostavite tok podvajanja, morate nastaviti vrste prevzema vseh objektov {0} na "Samo začetno". Druga možnost je, da izbrišete časovni načrt, vzpostavite tok podvajanja {0} in nato zaženete novo izvajanje. Posledica tega je izvajanje brez {0}konca, ki podpira tudi objekte z vrsto prevzema "Začetno in Delta/samo Delta".
#XMSG
SCHEDULE_EXCEPTION=Neuspešno pridobivanje podrobnosti o časovnem načrtu
#XFLD: Label for frequency column
everyLabel=Vsakih
#XFLD: Plural Recurrence text for Hour
hoursLabel=ur
#XFLD: Plural Recurrence text for Day
daysLabel=dni
#XFLD: Plural Recurrence text for Month
monthsLabel=mesecev
#XFLD: Plural Recurrence text for Minutes
minutesLabel=minut
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Podatkov o možnosti načrtovanja ni mogoče pridobiti.
#XFLD :Paused field
PAUSED=Začasno zaustavljeno
#XMSG
navToMonitoring=Odpiranje v monitorju toka
#XFLD
statusLbl=Status
#XFLD
lblLastRunExecuted=Začetek zadnjega izvajanja
#XFLD
lblLastExecuted=Zadnje izvajanje
#XFLD: Status text for Completed
statusCompleted=Dokončano
#XFLD: Status text for Running
statusRunning=Se izvaja
#XFLD: Status text for Failed
statusFailed=Ni uspelo
#XFLD: Status text for Stopped
statusStopped=Zaustavljeno
#XFLD: Status text for Stopping
statusStopping=Se zaustavlja
#XFLD: Status text for Active
statusActive=Aktivno
#XFLD: Status text for Paused
statusPaused=Začasno zaustavljeno
#XFLD: Status text for not executed
lblNotExecuted=Še ni izvedeno
#XFLD
messagesSettings=Nastavitve sporočil
#XTOL
@validateModel=Sporočila preverjanja veljavnosti
#XTOL
@hierarchy=Hierarhija
#XTOL
@columnCount=Število stolpcev
#XMSG
VAL_PACKAGE_CHANGED=Ta objekt ste dodelili paketu "{1}". Da potrdite in preverite veljavnost te spremembe, kliknite "Shrani". Upoštevajte, da v tem urejevalniku po shranjevanju dodelitve paketu ni mogoče razveljaviti.
#XMSG
MISSING_DEPENDENCY=Odvisnosti objekta "{0}" ni mogoče razrešiti v paketu "{1}".
#XFLD
deltaLoadInterval=Interval prevzema podatkov delta
#XFLD
lblHour=Ure (0-24)
#XFLD
lblMinutes=Minute (0-59)
#XMSG
maxHourOrMinErr=Vnesite vrednost med 0 in {0}
#XMSG
maxDeltaInterval=Največja vrednost intervala prevzema podatkov delta je 24 ur.{0}Ustrezno spremenite vrednost ure ali minute.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Pot ciljnega stolpca
#XFLD
confluentSubjectName=Ime subjekta
#XFLD
confluentSchemaVersion=Različica sheme
#XFLD
confluentIncludeTechKeyUpdated=Vključi tehnični ključ
#XFLD
confluentOmitNonExpandedArrays=Izpusti nerazširjene matrike
#XFLD
confluentExpandArrayOrMap=Razširi matriko ali zemljevid
#XCOL
confluentOperationMapping=Preslikava postopka
#XCOL
confluentOpCode=Koda postopka
#XFLD
confluentInsertOpCode=Vstavljanje
#XFLD
confluentUpdateOpCode=Posodobitev
#XFLD
confluentDeleteOpCode=Brisanje
#XFLD
expandArrayOrMapNotSelectedTxt=Ni izbrano
#XFLD
confluentSwitchTxtYes=Da
#XFLD
confluentSwitchTxtNo=Ne
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Napaka
#XTIT
executeWarning=Opozorilo
#XMSG
executeunsavederror=Shranite tok replikacije, preden se začne izvajati.
#XMSG
executemodifiederror=V toku replikacije obstajajo neshranjene spremembe. Shranite tok replikacije.
#XMSG
executeundeployederror=Preden lahko zaženete tok replikacije, ga morate postaviti.
#XMSG
executedeployingerror=Počakajte, da se postavitev konča.
#XMSG
msgRunStarted=Izvajanje zagnano
#XMSG
msgExecuteFail=Izvajanje toka replikacije ni uspelo.
#XMSG
titleExecuteBusy=Počakajte.
#XMSG
msgExecuteBusy=Poteka priprava vaših podatkov za izvedbo toka replikacije.
#XTIT
executeConfirmDialog=Opozorilo
#XMSG
msgExecuteWithValidations=Pri preverjanju veljavnosti toka replikacije je prišlo do napak. Izvedba toka replikacije morda ne bo uspela.
#XMSG
msgRunDeployedVersion=Obstajajo spremembe, ki jih je treba postaviti. Izvedla se bo zadnja uvedena različica toka replikacije. Želite nadaljevati?
#XBUT
btnExecuteAnyway=Vseeno izvedi
#XBUT
btnExecuteClose=Zapri
#XBUT
loaderClose=Zapri
#XTIT
loaderTitle=Poteka nalaganje
#XMSG
loaderText=Priklic podrobnosti iz strežnika
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Toka replikacije do te ne-SAP-jeve ciljne povezave, ni mogoče zagnati,
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=ker za ta mesec ni na voljo nobene izhodne količine.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Skrbnik lahko poveča premijsko izhodno količino za tega
#XMSG
premiumOutBoundRFAdminErrMsgPart2=najemnika, tako da bo izhodna količina na voljo za ta mesec.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Napaka
#XTIT
deployInfo=Informacije
#XMSG
deployCheckFailException=Pri postavitvi je prišlo do izjeme
#XMSG
deployGBQFFDisabled=Postavitev tokov replikacije s ciljno povezavo z Google BigQuery trenutno ni na voljo, ker izvajamo vzdrževanje te funkcije.
#XMSG
deployKAFKAFFDisabled=Postavitev tokov podvajanja s ciljno povezavo z Apache Kafka trenutno ni mogoča, ker izvajamo vzdrževanje te funkcije.
#XMSG
deployConfluentDisabled=Postavitev tokov podvajanja s ciljno povezavo z Confluent Kafka trenutno ni mogoča, ker izvajamo vzdrževanje te funkcije.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Za naslednje ciljne objekte imena tabel zajemanja delte že uporabljajo druge tabele v odložišču: {0} Te ciljne objekte morate preimenovati, da zagotovite, da so povezana imena tabel zajemanja delte enolična, preden lahko postavite tok replikacije.
#XMSG
deployDWCSourceFFDisabled=Postavitev tokov podvajanja, ki imajo za vir SAP Datasphere trenutno ni mogoča, ker izvajamo vzdrževanje te funkcije.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Postavitev tokov podvajanja, ki kot izvorne objekte vsebujejo lokalne tabele, ki podpirajo delto, trenutno ni mogoča, ker izvajamo vzdrževanje te funkcije.
#XMSG
deployHDLFSourceFFDisabled=Zagon tokov podvajanja, ki imajo izvorne povezave z vrsto povezave SAP HANA Cloud, datoteke v jezeru podatkov, trenutno ni mogoč, ker izvajamo vzdrževanje.
#XMSG
deployObjectStoreAsSourceFFDisabled=Tokov replikacije, katerih vir so ponudniki shranjevanja v oblaku, trenutno ni mogoče vzpostaviti.
#XMSG
deployConfluentSourceFFDisabled=Postavitev tokov podvajanja, ki imajo za vir Confluent Kafka trenutno ni mogoča, ker izvajamo vzdrževanje te funkcije.
#XMSG
deployMaxDWCNewTableCrossed=Velikih tokov podvajanja ni mogoče shraniti in postaviti naenkrat. Tok podvajanja morate naprej shraniti in ga nato postaviti.
#XMSG
deployInProgressInfo=Postavljanje je že v teku.
#XMSG
deploySourceObjectInUse=Izvorni objekti {0} se že uporabljajo v tokovih podvajanja {1}.
#XMSG
deployTargetSourceObjectInUse=Izvorni objekti {0} se že uporabljajo v tokovih podvajanja {1}. Ciljni objekti {2} se že uporabljajo v tokovih podvajanja {3}.
#XMSG
deployReplicationFlowCheckError=Napaka pri preverjanju toka podvajanja: {0}
#XMSG
preDeployTargetObjectInUse=Ciljni objekti {0} že v uporabi v tokovih podvajanja {1}, isti ciljni objekt pa ne morete imeti v dveh različnih tokovih podvajanja. Izberite drug ciljni objekt in poskusite znova.
#XMSG
runInProgressInfo=Tok replikacije se že izvaja.
#XMSG
deploySignavioTargetFFDisabled=Postavitev tokov podvajanja, ki imajo SAP Signavio za cilj trenutno nista mogoča, ker izvajamo vzdrževanje te funkcije.
#XMSG
deployHanaViewAsSourceFFDisabled=Postavitev tokov podvajanja, ki imajo poglede kot izvorne objekte za izbrano povezavo virov, trenutno ni mogoče. Poskusite ponovno kasneje.
#XMSG
deployMsOneLakeTargetFFDisabled=Postavitev tokov podvajanja, ki imajo MS OneLake za cilj trenutno nista mogoča, ker izvajamo vzdrževanje te funkcije.
#XMSG
deploySFTPTargetFFDisabled=Postavitev tokov podvajanja, ki imajo SFTP za cilj, trenutno ni mogoča, ker izvajamo vzdrževanje te funkcije.
#XMSG
deploySFTPSourceFFDisabled=Postavitev tokov podvajanja, ki imajo za vir SFTP, trenutno ni mogoča, ker izvajamo vzdrževanje te funkcije.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Tehnično ime
#XFLD
businessNameInRenameTarget=Poslovno ime
#XTOL
renametargetDialogTitle=Preimenuj ciljni objekt
#XBUT
targetRenameButton=Preimenuj
#XBUT
targetRenameCancel=Preklic
#XMSG
mandatoryTargetName=Vnesti morate ime.
#XMSG
dwcSpecialChar=_ (podčrtaj) je edini dovoljeni posebni znak.
#XMSG
dwcWithDot=Ime ciljne tabele je lahko sestavljeno iz latinskih črk, številk, podčrtajev (_) in pik (.). Prvi znak mora biti črka, številka ali podčrtaj (ne pika).
#XMSG
nonDwcSpecialChar=Dovoljeni posebni znaki so _ (podčrtaj) - (vezaj) . (pika)
#XMSG
firstUnderscorePattern=Ime se ne sme začeti s _(podčrtaj).

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: Prikaži SQL Ustvari izjavo tabele
#XMSG
sqlDialogMaxPKWarning=V Google BigQuery je podprtih največ 16 primarnih ključev in izvorni objekt ima veliko število. Zato primarni ključi niso opredeljeni v tej izjavi.
#XMSG
sqlDialogIncomptiblePKTypeWarning=En ali več izvornih stolpcev ima podatkovne tipe, ki jih ni mogoče opredeliti kot primarne ključe v Google BigQuery. Zato primarni ključi niso opredeljeni v tem primeru. V Google BigQuery lahko imajo primarni ključ samo naslednji podatkovni tipi: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Kopiraj in zapri
#XBUT
closeDDL=Zapri
#XMSG
copiedToClipboard=Kopirano v odložišče


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Objekt "{0}" ne more biti del verige opravil, saj nima konca (vključuje objekte z vrsto povezave Začetno in Delta/samo Delta).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Objekt "{0}" ne more biti del verige opravil, saj nima konca (vključuje objekte z vrsto povezave Začetno in Delta).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Objekta "{0}" ni mogoče dodati verigi nalog.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Obstajajo neshranjeni ciljni objekti. Znova jih shranite.{0}{0} Vedenje te funkcije se je spremenilo: V preteklosti so se ciljni objekti ustvarjali samo v ciljnem okolju, ko je bil tok podvajanja postavljen.{0} Zdaj se objekti ustvarijo že, ko shranite tok podvajanja. Vaš tok podvajanja je bil ustvarjen pred to spremembo in vsebuje nove objekte. {0}Tok podvajanja morate znova shraniti preden ga postavite, da bodo novi objekti pravilno vključeni.
#XMSG
confirmChangeContentTypeMessage=Spremenili bomo vrsto vsebine. V tem primeru bodo izbrisane vse obstoječe projekcije.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Ime subjekta
#XFLD
schemaDialogVersionName=Različica sheme
#XFLD
includeTechKey=Vključi tehnični ključ
#XFLD
segementButtonFlat=Ploščato
#XFLD
segementButtonNested=Ugnezdeno
#XMSG
subjectNamePlaceholder=Iskanje imena subjekta

#XMSG
@EmailNotificationSuccess=Konfiguracija časa izvajanja e-poštnih obvestil je shranjena.

#XFLD
@RuntimeEmailNotification=E-poštno obvestilo o času izvajanja

#XBTN
@TXT_SAVE=Shrani


