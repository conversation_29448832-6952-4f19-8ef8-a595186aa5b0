#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=복제 흐름

#XFLD: Edit Schema button text
editSchema=스키마 편집

#XTIT : Properties heading
configSchema=스키마 구성

#XFLD: save changed button text
applyChanges=변경 내용 적용


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=소스 연결 선택
#XFLD
sourceContainernEmptyText=컨테이너 선택
#XFLD
targetConnectionEmptyText=대상 연결 선택
#XFLD
targetContainernEmptyText=컨테이너 선택
#XFLD
sourceSelectObjectText=소스 오브젝트 선택
#XFLD
sourceObjectCount=소스 오브젝트({0})
#XFLD
targetObjectText=대상 오브젝트
#XFLD
confluentBrowseContext=컨텍스트 선택
#XBUT
@retry=재시도
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=테넌트 업그레이드 진행 중입니다.

#XTOL
browseSourceConnection=소스 연결 찾아보기
#XTOL
browseTargetConnection=대상 연결 찾아보기
#XTOL
browseSourceContainer=소스 컨테이너 찾아보기
#XTOL
browseAndAddSourceDataset=소스 오브젝트 추가
#XTOL
browseTargetContainer=대상 컨테이너 찾아보기
#XTOL
browseTargetSetting=대상 설정 찾아보기
#XTOL
browseSourceSetting=소스 설정 찾아보기
#XTOL
sourceDatasetInfo=정보
#XTOL
sourceDatasetRemove=제거
#XTOL
mappingCount=이름 기준이 아닌 총 매핑/표현식의 수를 표시합니다.
#XTOL
filterCount=총 필터 조건 수를 표시합니다.
#XTOL
loading=로드 중...
#XCOL
deltaCapture=델타 캡처
#XCOL
deltaCaptureTableName=델타 캡처 테이블
#XCOL
loadType=로드 유형
#XCOL
deleteAllBeforeLoading=로드하기 전에 모두 삭제
#XCOL
transformationsTab=프로젝션
#XCOL
settingsTab=설정

#XBUT
renameTargetObjectBtn=대상 오브젝트 이름 바꾸기
#XBUT
mapToExistingTargetObjectBtn=기존 대상 오브젝트에 매핑
#XBUT
changeContainerPathBtn=컨테이너 경로 변경
#XBUT
viewSQLDDLUpdated=SQL 테이블 생성 문 보기
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=소스 오브젝트는 델타 캡처를 지원하지 않지만 선택한 대상 오브젝트에는 델타 캡처 옵션이 활성화되어 있습니다.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=델타 캡처가 활성화되어 있으므로 대상 오브젝트를 사용할 수 없지만,{0}소스 오브젝트는 델타 캡처를 지원하지 않습니다.{1}델타 캡처를 지원하지 않는 다른 대상 오브젝트를 선택할 수 있습니다.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=이 이름을 가진 대상 오브젝트가 이미 있습니다. 단, 소스 오브젝트는{0}델타 캡처를 지원하지 않는 반면 델타 캡처가 활성화되어 있으므로{0}사용할 수 없습니다.{1}델타 캡처를 지원하지는 않는 기존 대상 오브젝트의 이름을{0}입력하거나, 아직 존재하지 않는 이름을 입력할 수 있습니다.
#XBUT
copySQLDDLUpdated=SQL 테이블 생성 문 복사
#XMSG
targetObjExistingNoCDCColumnUpdated=변경 데이터 캡처(CDC)를 위해 Google BigQuery의 기존 테이블에는 다음과 같은 열이 포함되어야 합니다.{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=다음 소스 오브젝트는 기본 키가 없거나 기본 키를 검색하는 데 필요한 조건을 충족하지 않는 연결을 사용하고 있기 때문에 지원되지 않습니다.
#XMSG
new_infoForUnsupportedDatasetNoKeys2=가능한 해결 방법을 알아보려면 SAP KBA 3531135의 내용을 참조하십시오.
#XLST: load type list values
initial=초기만
@emailUpdateError=전자메일 통지 리스트 업데이트 중 오류 발생

#XLST
initialDelta=초기 및 델타

#XLST
deltaOnly=델타만
#XMSG
confluentDeltaLoadTypeInfo=Confluent Kafka 소스인 경우, 초기 및 델타 로드 유형만 지원됩니다.
#XMSG
confirmRemoveReplicationObject=복제의 삭제를 확인하시겠습니까?
#XMSG
confirmRemoveReplicationTaskPrompt=이 액션은 기존 복제를 삭제합니다. 계속하시겠습니까?
#XMSG
confirmTargetConnectionChangePrompt=이 액션은 대상 연결과 대상 컨테이너를 재설정하고 모든 대상 오브젝트를 삭제합니다. 계속하시겠습니까?
#XMSG
confirmTargetContainerChangePrompt=이 액션은 대상 컨테이너를 재설정하고 기존의 모든 대상 오브젝트를 삭제합니다. 계속하시겠습니까?
#XMSG
confirmRemoveTransformObject=프로젝션 {0}의 삭제를 확인하시겠습니까?
#XMSG
ErrorMsgContainerChange=컨테이너 경로를 변경하는 중 오류가 발생했습니다.
#XMSG
infoForUnsupportedDatasetNoKeys=다음 소스 오브젝트는 기본 키가 없기 때문에 지원되지 않음:
#XMSG
infoForUnsupportedDatasetView=뷰 유형의 다음 소스 오브젝트는 지원되지 않음:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=다음 소스 오브젝트는 입력 매개변수를 포함하는 SQL 뷰이므로 지원되지 않습니다.
#XMSG
infoForUnsupportedDatasetExtractionDisabled=다음 소스 오브젝트는 추출이 비활성화되어 있으므로 지원되지 않음:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Confluent 연결에서는 AVRO 및 JSON만 일련화 형식으로 허용됩니다. 다음 오브젝트는 다른 일련화 형식을 사용하기 때문에 지원되지 않습니다.
#XMSG
infoForUnsupportedDatasetSchemaNotFound=다음 오브젝트에 대한 스키마를 가져올 수 없습니다. 적절한 컨텍스트를 선택하거나 스키마 레지스트리 구성을 확인하십시오.
#XTOL: warning dialog header on deleting replication task
deleteHeader=삭제
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Google BigQuery에는 "로딩 전 모두 삭제" 설정이 지원되지 않습니다.
#XBUT
DeleteAllBeforeLoadingConfluentInfo="로딩 전 모두 삭제" 설정은 각 복제 전에 오브젝트(주제)를 삭제하고 다시 생성합니다. 지정된 메시지도 모두 삭제됩니다.
#XTOL
DeleteAllBeforeLoadingLTFInfo=이 대상 유형에는 "로딩 전 모두 삭제" 설정이 지원되지 않습니다.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=기술적 이름
#XCOL
connBusinessName=업무 이름
#XCOL
connDescriptionName=내역
#XCOL
connType=유형
#XMSG
connTblNoDataFoundtxt=연결 없음
#XMSG
connectionError=연결을 가져오는 중 오류가 발생했습니다.
#XMSG
connectionCombinationUnsupportedErrorTitle=연결 조합이 지원되지 않습니다.
#XMSG
connectionCombinationUnsupportedErrorMsgTxt={0}에서 {1}(으)로의 복제는 현재 지원되지 않습니다.
#XMSG
invalidTargetforSourceHDLFErrorTitle=연결 유형 조합이 지원되지 않습니다.
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=연결 유형이 SAP HANA Cloud, 데이터 레이크 파일인 연결에서 {0}(으)로의 복제는 지원되지 않습니다. SAP Datasphere에만 복제할 수 있습니다.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=선택
#XBUT
containerCancelBtn=취소
#XTOL
containerSelectTooltip=선택
#XTOL
containerCancelTooltip=취소
#XMSG
containerContainerPathPlcHold=컨테이너 경로
#XFLD
containerContainertxt=컨테이너
#XFLD
confluentContainerContainertxt=컨텍스트
#XMSG
infoMessageForSLTSelection=/SLT/일괄 전송 ID만 컨테이너로 허용됩니다. SLT 아래에서 일괄 전송 ID를 선택하고(있는 경우) 제출을 클릭하십시오.
#XMSG
msgFetchContainerFail=컨테이너 데이터를 가져오는 중 오류가 발생했습니다.
#XMSG
infoMessageForSLTHidden=이 연결은 SLT 폴더를 지원하지 않으므로 아래 리스트에 표시되지 않습니다.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=하위 폴더가 포함된 컨테이너를 선택하십시오.
#XMSG
sftpIncludeSubFolderText=거짓
#XMSG
sftpIncludeSubFolderTextNew=아니오

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(필터 매핑이 아직 없음)
#XMSG
failToFetchRemoteMetadata=메타 데이터를 가져오는 중 오류가 발생했습니다.
#XMSG
failToFetchData=기존 대상을 가져오는 중 오류가 발생했습니다.
#XCOL
@loadType=로드 유형
#XCOL
@deleteAllBeforeLoading=로드하기 전에 모두 삭제

#XMSG
@loading=로드 중...
#XFLD
@selectSourceObjects=소스 오브젝트 선택
#XMSG
@exceedLimit=한 번에 {0}개를 초과하는 오브젝트를 임포트할 수 없습니다. {1}개 이상의 오브젝트에 대한 선택을 해제하십시오.
#XFLD
@objects=오브젝트
#XBUT
@ok=확인
#XBUT
@cancel=취소
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=다음
#XBUT
btnAddSelection=선택 추가
#XTOL
@remoteFromSelection=선택에서 제거
#XMSG
@searchInForSearchField={0}에서 검색

#XCOL
@name=기술적 이름
#XCOL
@type=유형
#XCOL
@location=위치
#XCOL
@label=업무 이름
#XCOL
@status=상태

#XFLD
@searchIn=검색 위치:
#XBUT
@available=사용 가능
#XBUT
@selection=선택

#XFLD
@noSourceSubFolder=테이블 및 뷰
#XMSG
@alreadyAdded=이미 다이어그램에 있습니다.
#XMSG
@askForFilter=항목이 {0}개 이상 있습니다. 항목 수를 줄이려면 필터 문자열을 입력하십시오.
#XFLD: success label
lblSuccess=성공
#XFLD: ready label
lblReady=준비됨
#XFLD: failure label
lblFailed=실패
#XFLD: fetching status label
lblFetchingDetail=세부사항을 가져오는 중

#XMSG Place holder text for tree filter control
filterPlaceHolder=최상위 레벨 오브젝트를 필터링할 텍스트를 입력하십시오.
#XMSG Place holder text for server search control
serverSearchPlaceholder=텍스트를 입력하고 Enter를 눌러 검색하십시오.
#XMSG
@deployObjects={0}개 오브젝트를 임포트 중...
#XMSG
@deployObjectsStatus=임포트한 오브젝트 수: {0}. 임포트할 수 없는 오브젝트 수: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=로컬 저장소 브라우저를 열지 못했습니다.
#XMSG
@openRemoteSourceBrowserError=소스 오브젝트를 가져오지 못했습니다.
#XMSG
@openRemoteTargetBrowserError=대상 오브젝트를 가져오지 못했습니다.
#XMSG
@validatingTargetsError=대상 유효성 확인 중 오류가 발생했습니다.
#XMSG
@waitingToImport=임포트 가능

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=최대 오브젝트 수를 초과했습니다. 하나의 복제 흐름에 대해 최대 500개의 오브젝트를 선택하십시오.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=기술적 이름
#XFLD
sourceObjectBusinessName=업무 이름
#XFLD
sourceNoColumns=열 수
#XFLD
containerLbl=컨테이너

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=복제 흐름의 소스 연결을 선택해야 합니다.
#XMSG
validationSourceContainerNonExist=소스 연결의 컨테이너를 선택해야 합니다.
#XMSG
validationTargetNonExist=복제 흐름의 대상 연결을 선택해야 합니다.
#XMSG
validationTargetContainerNonExist=대상 연결의 컨테이너를 선택해야 합니다.
#XMSG
validationTruncateDisabledForObjectTitle=오브젝트 저장소로 복제
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget="로딩 전 모두 삭제" 옵션이 설정되어 있거나 대상 오브젝트가 대상에 존재하지 않는 경우에만 클라우드 저장소로 복제할 수 있습니다.{0}{0} "로딩 전 모두 삭제" 옵션이 설정되지 않은 오브젝트에 대한 복제를 활성화하려면, 복제 흐름 실행 전에 대상 오브젝트가 시스템에 없어야 합니다.
#XMSG
validationTaskNonExist=복제 흐름에 복제가 하나 이상 있어야 합니다.
#XMSG
validationTaskTargetMissing=다음 소스가 있는 복제의 대상이 있어야 합니다. {0}
#XMSG
validationTaskTargetIsSAC=선택된 대상은 SAC 아티팩트: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=선택한 대상은 지원되는 로컬 테이블이 아닙니다. {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=이 이름의 오브젝트가 대상에 이미 있습니다. 하지만 이 오브젝트는 로컬 테이블이 아니므로 로컬 저장소에 대한 복제 흐름의 대상 오브젝트로 사용될 수 없습니다.
#XMSG
validateSourceTargetSystemDifference=복제 흐름에 대해 다른 소스와 대상 연결 및 컨테이너 조합을 선택해야 합니다.
#XMSG
validateDuplicateSources=하나 이상의 복제에 중복 소스 오브젝트 이름이 있습니다({0}).
#XMSG
validateDuplicateTargets=하나 이상의 복제에 중복 대상 오브젝트 이름이 있습니다({0}).
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=소스 오브젝트 {0}은(는) 델타 캡처를 지원하지 않지만 대상 오브젝트 {1}은(는) 델타 캡처를 지원합니다. 복제를 제거해야 합니다.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=대상 오브젝트 이름이 {0}인 복제에 대해 로드 유형 "초기 및 델타"를 선택해야 합니다.
#XMSG
validationAutoRenameTarget=대상 열 이름이 변경되었습니다.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=자동 프로젝션이 추가되었으며, 다음 대상 열의 이름이 대상에 복제될 수 있도록 허용됨:{1}{1} {0} {1}{1}원인은 다음 중 하나임: {1}{1}{2} 지원되지 않는 문자{1}{2} 예약된 접두부
#XMSG
validationAutoRenameTargetDescriptionUpdated=자동 프로젝션이 추가되었으며, 다음 대상 열의 이름이 Google BigQuery로 복제할 수 있도록 바뀌었습니다.{1}{1} {0} {1}{1}이는 다음 사유 중 하나 때문입니다.{1}{1}{2} 예약된 열 이름{1}{2} 지원되지 않는 문자{1}{2} 예약된 접두부
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=자동 프로젝션이 추가되었으며, 다음 대상 열의 이름이 Confluent로 복제할 수 있도록 바뀌었습니다.{1}{1} {0} {1}{1}이는 다음 사유 중 하나 때문입니다.{1}{1}{2} 예약된 열 이름{1}{2} 지원되지 않는 문자{1}{2}예약된 접두부
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=자동 프로젝션이 추가되었으며, 다음 대상 열의 이름이 대상으로 복제할 수 있도록 바뀌었습니다.{1}{1} {0} {1}{1}이는 다음 사유 중 하나 때문입니다.{1}{1}{2} 예약된 열 이름{1}{2} 지원되지 않는 문자{1}{2}예약된 접두부
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=대상 오브젝트 이름이 변경되었습니다.
#XMSG
autoRenameInfoDesc=지원되지 않는 문자가 포함되어 있어서 대상 오브젝트의 이름이 변경되었습니다. 다음 문자만 지원됩니다. {0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(점){0}{1}_(밑줄){0}{1}-(대시)
#XMSG
validationAutoTargetTypeConversion=대상 데이터 유형이 변경되었습니다.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=다음 대상 열의 경우 Google BigQuery에서 소스 데이터 유형이 지원되지 않기 때문에 대상 데이터 유형이 변경되었습니다.{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=다음 대상 열의 경우 대상 연결에서 소스 데이터 유형이 지원되지 않기 때문에 대상 데이터 유형이 변경되었습니다({1}{1}{0}).
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=대상 열 이름을 줄입니다.
#XMSG
validationMaxCharLengthGBQTargetDescription=Google BigQuery에서 열 이름은 최대 300자까지 입력할 수 있습니다. 프로젝션을 사용하여 다음 대상 열 이름을 줄이십시오.{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=기본 키가 생성되지 않습니다.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=Google BigQuery에서 최대 16개의 기본 키가 지원되지만 소스 오브젝트에 기본 키가 더 많이 있습니다. 대상 오브젝트에 기본 키가 생성되지 않습니다.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=하나 이상의 소스 열에 Google BigQuery에서 기본 키로 정의할 수 없는 데이터 유형이 있습니다. 기본 키는 대상 오브젝트에 생성되지 않습니다.{0}{0}다음 대상 데이터 유형은 기본 키를 정의할 수 있는 Google BigQuery 데이터 유형과 호환됩니다. {0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=하나 이상의 열을 기본 키로 정의하십시오.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=이 작업을 수행하려면 소스 스키마 대화 상자를 사용하여 하나 이상의 열을 기본 키로 정의해야 합니다.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=하나 이상의 열을 기본 키로 정의하십시오.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=소스 오브젝트의 기본 키 제약 조건과 일치하는 열을 하나 이상 기본 키로 정의해야 합니다. 이렇게 하려면 소스 오브젝트 속성에서 스키마 구성으로 이동하십시오.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=올바른 최대 파티션 값을 입력하십시오.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=최대 파티션 값은 1 이상 2147483647 이하여야 합니다.
#XMSG
validateHDLFNoPKDatasetError=하나 이상의 열을 기본 키로 정의하십시오.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=오브젝트를 복제하려면 하나 이상의 대상 열을 기본 키로 정의해야 합니다. 이 작업을 수행하려면 프로젝션을 사용하십시오.
#XMSG
validateHDLFNoPKExistingDatasetError=하나 이상의 열을 기본 키로 정의하십시오.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=데이터를 기존의 대상 오브젝트로 복제하려면 여기에 기본 키로 정의된 열이 하나 이상 있어야 합니다. {0} 하나 이상의 열을 기본 키로 정의하는 다음과 같은 옵션이 있습니다.{0} {1}로컬 테이블 편집기로 기존의 대상 오브젝트를 변경합니다. 그런 다음 복제 흐름을 다시 로드합니다. {0}{1}복제 흐름에서 대상 오브젝트 이름을 바꿉니다. 이렇게 하면 실행이 시작되자마자 새로운 오브젝트가 생성됩니다. 이름을 바꾼 후에 하나 이상의 열을 기본 키로 정의할 수 있습니다.{0}{1}오브젝트를 하나 이상의 열이 이미 기본 키로 정의되어 있는 기존의 다른 대상 오브젝트로 매핑합니다.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=선택한 대상이 저장소 {0}에 이미 있습니다. 
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=델타 캡처 테이블 이름이 이미 저장소 {0}의 다른 테이블에 사용되고 있습니다. 이 대상 오브젝트 이름을 변경하여 관련 델타 캡처 테이블에 고유한 이름을 지정해야만 복제 흐름을 저장할 수 있습니다.
#XMSG
validateConfluentEmptySchema=스키마 정의
#XMSG
validateConfluentEmptySchemaDescUpdated=소스 테이블에 스키마가 없습니다. "스키마 구성"을 선택하여 스키마를 정의하십시오.
#XMSG
validationCSVEncoding=잘못된 CSV 인코딩
#XMSG
validationCSVEncodingDescription=태스크의 CSV 인코딩이 잘못되었습니다.
#XMSG
validateConfluentEmptySchema=호환 가능한 대상 데이터 유형을 선택하십시오.
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=호환 가능한 대상 데이터 유형을 선택하십시오.
#XMSG
globalValidateTargetDataTypeDesc=열 매핑에 오류가 발생했습니다. "예측"으로 이동하여 모든 소스 열이 호환 가능한 데이터 유형의 고유한 열과 매핑되어 있는지, 정의된 모든 표현식이 유효한지 확인하십시오.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=중복된 열 이름
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=열 이름이 중복되면 안 됩니다. 프로젝션 대화 상자에서 수정하십시오. 다음 대상 오브젝트에 중복된 열 이름이 있음: {0}
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=중복된 열 이름
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=열 이름이 중복되면 안 됩니다. 다음 대상 오브젝트에 중복된 열 이름이 있음: {0}
#XMSG
deltaOnlyLoadTypeTittle=데이터에 불일치가 있을 수 있습니다.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated="델타만" 로드 유형은 마지막 저장과 다음 실행 사이의 소스 변경사항을 고려하지 않습니다.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=로드 유형을 "초기"로 변경하십시오.
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=기본 키가 없는 ABAP 기반 오브젝트 복제는 로드 유형이 "초기만"인 경우에만 가능합니다.
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=델타 캡처를 비활성화하십시오.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=소스 연결 유형 ABAP을 사용하여 기본 키가 없는 오브젝트를 복제하려면 먼저 이 테이블에 대한 델타 캡처를 비활성화해야 합니다.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=대상 오브젝트를 변경하십시오.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=델타 캡처가 활성화되어 대상 오브젝트를 사용할 수 없습니다. 대상 오브젝트의 이름을 변경한 다음 신규(이름이 변경된) 오브젝트에 대한 델타 캡처를 끄거나, 소스 오브젝트를 델타 캡처가 비활성화된 대상 오브젝트에 매핑할 수 있습니다.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=대상 오브젝트를 변경하십시오.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=필수 기술 열 __load_package_id가 없어서 대상 오브젝트를 사용할 수 없습니다. 아직 존재하지 않는 이름을 사용하여 대상 오브젝트의 이름을 변경할 수 있습니다. 그러면 시스템에서 소스 오브젝트와 정의가 동일하고 해당 기술 열을 포함하는 새로운 오브젝트를 생성합니다. 또는 필수 기술 열(__load_package_id)이 있는 기존 오브젝트에 대상 오브젝트를 매핑할 수 있습니다. 
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=대상 오브젝트를 변경하십시오.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=필수 기술 열 __load_record_id가 없어서 대상 오브젝트를 사용할 수 없습니다. 아직 존재하지 않는 이름을 사용하여 대상 오브젝트의 이름을 변경할 수 있습니다. 그러면 시스템에서 소스 오브젝트와 정의가 동일하고 해당 기술 열을 포함하는 새로운 오브젝트를 생성합니다. 또는 필수 기술 열(__load_record_id)이 있는 기존 오브젝트에 대상 오브젝트를 매핑할 수 있습니다.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=대상 오브젝트를 변경하십시오.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=기술 열 __load_record_id의 데이터 유형이 "string(44)"이 아니므로 대상 오브젝트를 사용할 수 없습니다. 아직 존재하지 않는 이름을 사용하여 대상 오브젝트의 이름을 변경할 수 있습니다. 그러면 시스템에서 소스 오브젝트와 정의가 동일하고 결과적으로 올바른 데이터 유형을 가진 새로운 오브젝트를 생성합니다. 또는 올바른 데이터 유형의 필수 기술 열(__load_record_id)이 있는 기존 오브젝트에 대상 오브젝트를 매핑할 수 있습니다.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=대상 오브젝트를 변경하십시오.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=대상 오브젝트에는 기본 키가 있지만 소스 오브젝트에는 기본 키가 없으므로 대상 오브젝트를 사용할 수 없습니다. 아직 존재하지 않는 이름을 사용하여 대상 오브젝트의 이름을 변경할 수 있습니다. 그러면 시스템에서 소스 오브젝트와 정의가 동일하고 결과적으로 기본 키가 없는 새로운 오브젝트를 생성합니다. 또는 필수 기술 열(__load_package_id)이 있고 기본 키가 없는 기존 오브젝트에 대상 오브젝트를 매핑할 수 있습니다.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=대상 오브젝트를 변경하십시오.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=대상 오브젝트에는 기본 키가 있지만 소스 오브젝트에는 기본 키가 없으므로 대상 오브젝트를 사용할 수 없습니다. 아직 존재하지 않는 이름을 사용하여 대상 오브젝트의 이름을 변경할 수 있습니다. 그러면 시스템에서 소스 오브젝트와 정의가 동일하고 결과적으로 기본 키가 없는 새로운 오브젝트를 생성합니다. 또는 필수 기술 열(__load_record_id)이 있고 기본 키가 없는 기존 오브젝트에 대상 오브젝트를 매핑할 수 있습니다.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=대상 오브젝트를 변경하십시오.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=기술 열 __load_package_id의 데이터 유형이 "binary(>=256)"가 아니므로 대상 오브젝트를 사용할 수 없습니다. 아직 존재하지 않는 이름을 사용하여 대상 오브젝트의 이름을 변경할 수 있습니다. 그러면 시스템에서 소스 오브젝트와 정의가 동일하고 결과적으로 올바른 데이터 유형을 가진 새로운 오브젝트를 생성합니다. 또는 올바른 데이터 유형의 필수 기술 열(__load_package_id)이 있는 기존 오브젝트에 대상 오브젝트를 매핑할 수 있습니다.
#XMSG
validationAutoRenameTargetDPID=대상 열 이름이 변경되었습니다.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=소스 오브젝트를 제거하십시오.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=소스 오브젝트에 이 컨텍스트에서 지원되지 않는 키 열이 없습니다.
#XMSG
validationAutoRenameTargetDPIDDescription=자동 프로젝션이 추가되었으며,  키 없이 ABAP 소스에서 복제할 수 있도록 다음 대상 열의 이름이 바뀌었습니다({1}{1}  {0} {1}{1}). 이는 다음 사유 중 하나 때문입니다. - {1}{1}{2} 예약된 열 이름, {1}{2} 지원되지 않는 문자, {1}{2} 예약된 접두부
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle={0}(으)로의 복제
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=이 기능에 대한 유지보수를 진행 중이어서 {0}을(를) 대상으로 사용하는 복제 흐름을 저장 및 배포할 수 없습니다.
#XMSG
TargetColumnSkippedLTF=대상 열이 스킵되었습니다.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=데이터 유형이 지원되지 않아서 대상 열이 스킵되었습니다. {0}{1}
#XMSG
validatePKTimeColumnLTF1=기본 키 역할을 하는 시간 열입니다.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=소스 오브젝트에 기본 키 역할을 하는 시간 열이 있지만 이 컨텍스트에서 지원되지 않습니다.
#XMSG
validateNoPKInLTFTarget=기본 키가 없습니다.
#XMSG
validateNoPKInLTFTargetDescription=기본 키가 대상에 정의되어 있지 않습니다. 이 컨텍스트에서 지원되지 않습니다.
#XMSG
validateABAPClusterTableLTF=ABAP 클러스터 테이블입니다.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=소스 오브젝트가 이 컨텍스트에서 지원되지 않는 ABAP 클러스터 테이블입니다.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=아직 데이터를 추가하지 않은 것 같습니다.
#YINS
welcomeText2=복제 흐름을 시작하려면 왼쪽에서 연결과 소스 오브젝트를 선택하십시오.

#XBUT
wizStep1=소스 연결 선택
#XBUT
wizStep2=소스 컨테이너 선택
#XBUT
wizStep3=소스 오브젝트 추가

#XMSG
limitDataset=최대 오브젝트 수에 도달했습니다. 기존 오브젝트를 제거하여 새 오브젝트를 추가하거나 새 복제 흐름을 생성합니다.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=이번 달에 사용 가능한 아웃바운드 볼륨이 없어서 이 SAP 이외의 대상 연결에 대한 복제 흐름을 시작할 수 없습니다.
#XMSG
premiumOutBoundRFAdminWarningMsg=관리자가 이 테넌트의 프리미엄 아웃바운드 블록을 늘려서 이번 달에 사용 가능한 아웃바운드 볼륨을 작성할 수 있습니다.
#XMSG
messageForToastForDPIDColumn2={0}개 오브젝트의 대상에 새로운 열이 추가되었습니다. 기본 키가 없는 ABAP 기반 소스 오브젝트와 관련하여 중복 레코드를 처리하는 데 필요합니다.
#XMSG
PremiumInboundWarningMessage=복제 흐름 수와 복제할 데이터 볼륨에 따라, {1}을(를) 통해 데이터를 복제하는 데 필요한 SAP HANA 리소스{0}가 테넌트의 가용 용량을 초과할 수 있습니다.
#XMSG
PremiumInboundWarningMsg=복제 흐름 수와 복제할 데이터 볼륨에 따라, "{1}"을(를) 통해 데이터를 복제하는 데 필요한 {0}SAP HANA 리소스가 테넌트의 가용 용량을 초과할 수 있습니다.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=프로젝션 이름을 입력하십시오.
#XMSG
emptyTargetColumn=대상 열 이름을 입력하십시오.
#XMSG
emptyTargetColumnBusinessName=대상 열 업무 이름을 입력하십시오.
#XMSG
invalidTransformName=프로젝션 이름을 입력하십시오.
#XMSG
uniqueColumnName=대상 열 이름을 변경하십시오.
#XMSG
copySourceColumnLbl=소스 오브젝트에서 열 복사
#XMSG
renameWarning=대상 테이블 이름 변경 시 반드시 고유한 이름을 선택하십시오. 신규 이름의 테이블이 공간에 이미 존재할 경우 해당 테이블의 정의를 사용하게 됩니다.

#XMSG
uniqueColumnBusinessName=대상 열 업무 이름을 변경하십시오.
#XMSG
uniqueSourceMapping=다른 소스 열을 선택하십시오.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=소스 열 {0}은(는) 다음 대상 열에서 이미 사용되었음: {1}{1}{2}{1}{1} 이 대상 열 또는 다른 대상 열의 경우 아직 사용되지 않은 소스 열을 선택하여 예측을 저장하십시오.
#XMSG
uniqueColumnNameDescription=입력한 대상 열 이름이 이미 있습니다. 프로젝션을 저장하려면 고유한 열 이름을 입력해야 합니다.
#XMSG
uniqueColumnBusinessNameDesc=대상 열 업무 이름이 이미 있습니다. 프로젝션을 저장하려면 고유한 열 업무 이름을 입력해야 합니다.
#XMSG
emptySource=소스 열을 선택하거나 상수를 입력하십시오.
#XMSG
emptySourceDescription=유효한 매핑 엔트리를 생성하려면 소스 열을 선택하거나 상수 값을 입력해야 합니다.
#XMSG
emptyExpression=매핑을 정의하십시오.
#XMSG
emptyExpressionDescription1=대상 열을 매핑하려는 소스 열을 선택하거나 {0} 함수/상수 {1} 열에서 체크박스를 선택하십시오. 대상 데이터 유형에 따라 {2} {2} 함수가 자동으로 입력됩니다. 상수 값은 직접 입력할 수 있습니다.
#XMSG
numberExpressionErr=숫자를 입력하십시오.
#XMSG
numberExpressionErrDescription=숫자 데이터 유형을 선택했습니다. 즉, 숫자만 입력할 수 있으며 해당하는 경우 소수점도 입력할 수 있습니다. 작은따옴표를 사용하지 마십시오.
#XMSG
invalidLength=유효한 길이 값을 입력하십시오.
#XMSG
invalidLengthDescription=데이터 유형의 길이는 소스 열의 길이보다 크거나 같아야 하며 1에서 5000 사이일 수 있습니다.
#XMSG
invalidMappedLength=유효한 길이 값을 입력하십시오.
#XMSG
invalidMappedLengthDescription=데이터 유형의 길이는 소스 열 {0}의 길이보다 크거나 같아야 하며 1에서 5000 사이일 수 있습니다.
#XMSG
invalidPrecision=유효한 정밀도 값을 입력하십시오.
#XMSG
invalidPrecisionDescription=정밀도는 총 자릿수를 정의합니다. 배율은 소수점 이하 자릿수를 정의하며 0에서 정밀도 사이일 수 있습니다. {0}{0} 예: {0}{1}정밀도 6, 스케일 2는 1234.56과 같은 숫자에 해당합니다.{0}{1} 정밀도 6, 스케일 6은 0.123546과 같은 숫자에 해당합니다.{0} {0} 소스의 모든 숫자가 대상 필드에 맞도록 대상의 정밀도 및 스케일은 소스의 정밀도 및 스케일과 호환되어야 합니다. 예를 들어, 소스에 정밀도 6과 소수점 이하 자릿수 2가 있는 경우(결과적으로 소수점 앞에 0이 아닌 숫자) 대상에는 정밀도 6과 소수점 이하 자릿수 6이 있을 수 없습니다.
#XMSG
invalidPrimaryKey=기본 키를 하나 이상 입력하십시오.
#XMSG
invalidPrimaryKeyDescription=이 스키마에 대해 기본 키가 정의되지 않았습니다.
#XMSG
invalidMappedPrecision=유효한 정밀도 값을 입력하십시오.
#XMSG
invalidMappedPrecisionDescription1=정밀도는 총 자릿수를 정의합니다. 배율은 소수점 이하 자릿수를 정의하며 0에서 정밀도 사이일 수 있습니다. {0}{0} 예:{0}{1}정밀도 6, 스케일 2는 1234.56과 같은 숫자에 해당합니다.{0}{1} 정밀도 6, 소수 자릿수 6은 0.123546과 같은 숫자에 해당합니다.{0}{0}데이터 유형의 정밀도는 소스({2})의 정밀도보다 크거나 같아야 합니다.
#XMSG
invalidScale=유효한 스케일 값을 입력하십시오.
#XMSG
invalidScaleDescription=정밀도는 총 자릿수를 정의합니다. 배율은 소수점 이하 자릿수를 정의하며 0에서 정밀도 사이일 수 있습니다. {0}{0} 예: {0}{1}정밀도 6, 스케일 2는 1234.56과 같은 숫자에 해당합니다.{0}{1} 정밀도 6, 스케일 6은 0.123546과 같은 숫자에 해당합니다.{0} {0} 소스의 모든 숫자가 대상 필드에 맞도록 대상의 정밀도 및 스케일은 소스의 정밀도 및 스케일과 호환되어야 합니다. 예를 들어, 소스에 정밀도 6과 소수점 이하 자릿수 2가 있는 경우(결과적으로 소수점 앞에 0이 아닌 숫자) 대상에는 정밀도 6과 소수점 이하 자릿수 6이 있을 수 없습니다.
#XMSG
invalidMappedScale=유효한 스케일 값을 입력하십시오.
#XMSG
invalidMappedScaleDescription1=정밀도는 총 자릿수를 정의합니다. 배율은 소수점 이하 자릿수를 정의하며 0에서 정밀도 사이일 수 있습니다. {0}{0} 예:{0}{1}정밀도 6, 스케일 2는 1234.56과 같은 숫자에 해당합니다.{0}{1} 정밀도 6, 소수 자릿수 6은 0.123546과 같은 숫자에 해당합니다.{0}{0}데이터 유형의 스케일은 소스({2})의 스케일보다 크거나 같아야 합니다.
#XMSG
nonCompatibleDataType=호환 가능한 대상 데이터 유형을 선택하십시오.
#XMSG
nonCompatibleDataTypeDescription1=여기에서 지정하는 데이터 유형은 소스 데이터 유형({0})과 호환되어야 합니다. {1}{1}예: 소스 열의 데이터 유형이 문자열(string)이고 문자가 포함되어 있을 경우 대상에 소수(decimal) 데이터 유형을 사용할 수 없습니다.
#XMSG
invalidColumnCount=소스 열을 선택하십시오.
#XMSG
ObjectStoreInvalidScaleORPrecision=정밀도와 스케일에 유효한 값을 입력하십시오.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=첫 번째 값은 정밀도이며 총 자릿수를 정의합니다. 두 번째 값은 스케일이며 소수점 이하 자릿수를 정의합니다. 소스 스케일 값보다 큰 대상 스케일 값을 입력하고 입력한 대상 스케일과 정밀도 값의 차이가 소스 스케일과 정밀도 값의 차이보다 큰지 확인하십시오.
#XMSG
InvalidPrecisionORScale=정밀도와 스케일에 유효한 값을 입력하십시오.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=첫 번째 값은 총 자릿수를 정의하는 정밀도입니다. 두 번째 값은 소수점 이후의 자릿 수를 정의하는 소수 자릿수입니다.{0}{0}Google BigQuery에서 소스 데이터 유형이 지원되지 않으므로 대상 데이터 유형 DECIMAL로 변환됩니다. 이 경우 정밀도는 38에서 76 사이로 정의할 수 있고 소수 자릿수는 9에서 38 사이로 정의할 수 있습니다. 또한 정밀도에서 소수 자릿수를 뺀 값은 소수점 앞의 자릿수를 나타내며 29에서 38 사이여야 합니다.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=첫 번째 값은 총 자릿수를 정의하는 정밀도입니다. 두 번째 값은 소수점 이후의 자릿 수를 정의하는 소수 자릿수입니다.{0}{0}Google BigQuery에서 소스 데이터 유형이 지원되지 않으므로 대상 데이터 유형 DECIMAL로 변환됩니다. 이 경우 정밀도는 20 이하여야 합니다. 또한 정밀도에서 소수 자릿수를 뺀 값은 소수점 앞의 자릿수를 나타내며 20 이상이어야 합니다.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=첫 번째 값은 총 자릿수를 정의하는 정밀도입니다. 두 번째 값은 소수점 이후의 자릿수를 정의하는 스케일입니다.{0}{0}대상에서 소스 데이터 유형이 지원되지 않으므로 대상 데이터 유형 DECIMAL로 변환됩니다. 이 경우 정밀도는 1 이상, 38 이하인 숫자로 정의되어야 하며 스케일 값은 정밀도보다 작거나 같아야 합니다.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=첫 번째 값은 총 자릿수를 정의하는 정밀도입니다. 두 번째 값은 소수점 이후의 자릿수를 정의하는 스케일입니다.{0}{0}대상에서 소스 데이터 유형이 지원되지 않으므로 대상 데이터 유형 DECIMAL로 변환됩니다. 이 경우 정밀도 값은 20 이상으로 정의되어야 합니다. 또한 정밀도에서 스케일을 뺀 값은 소수점 앞의 자릿수를 나타내며 20 이상이어야 합니다.
#XMSG
invalidColumnCountDescription=유효한 매핑 엔트리를 생성하려면 소스 열을 선택하거나 상수 값을 입력해야 합니다.
#XMSG
duplicateColumns=대상 열 이름을 변경하십시오.
#XMSG
duplicateGBQCDCColumnsDesc=대상 열 이름은 Google BigQuery에 예약되어 있습니다. 프로젝션을 저장하려면 이름을 바꿔야 합니다.
#XMSG
duplicateConfluentCDCColumnsDesc=대상 열 이름은 융합에 예약되어 있습니다. 프로젝션을 저장하려면 이름을 바꿔야 합니다.
#XMSG
duplicateSignavioCDCColumnsDesc=대상 열 이름은 SAP Signavio에 예약되어 있습니다. 프로젝션을 저장하려면 이름을 바꿔야 합니다.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=대상 열 이름은 MS OneLake에 예약되어 있습니다. 프로젝션을 저장하려면 이름을 바꿔야 합니다.
#XMSG
duplicateSFTPCDCColumnsDesc=대상 열 이름은 SFTP에 예약되어 있습니다. 프로젝션을 저장하려면 이름을 바꿔야 합니다.
#XMSG
GBQTargetNameWithPrefixUpdated1=대상 열 이름에는 Google BigQuery에 예약된 접두부가 포함되어 있습니다. 프로젝션을 저장하려면 이름을 바꿔야 합니다. {0}{0}대상 열 이름은 다음 문자열로 시작할 수 없습니다. {0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=대상 열 이름을 줄이십시오.
#XMSG
GBQtargetMaxLengthDesc=Google BigQuery에서 열 이름은 최대 300자를 사용할 수 있습니다. 프로젝션을 저장할 수 있도록 대상 열 이름을 줄이십시오.
#XMSG
invalidMappedScalePrecision=소스의 모든 숫자가 대상 필드에 맞도록 대상의 정밀도 및 스케일은 소스의 정밀도 및 스케일과 호환되어야 합니다.
#XMSG
invalidMappedScalePrecisionShortText=유효한 정확도 및 스케일 값을 입력하십시오.
#XMSG
validationIncompatiblePKTypeDescProjection3=하나 이상의 소스 열에 Google BigQuery에서 기본 키로 정의할 수 없는 데이터 유형이 있습니다. 기본 키는 대상 오브젝트에 생성되지 않습니다.{0}{0}다음 대상 데이터 유형은 기본 키를 정의할 수 있는 Google BigQuery 데이터 유형과 호환됩니다. {1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=column __message_id의 선택을 해제하십시오.
#XMSG
uncheckColumnMessageIdDesc=열: 기본 키
#XMSG
validationOpCodeInsert=삽입할 값을 입력해야 합니다.
#XMSG
recommendDifferentPrimaryKey=항목 레벨에서 다른 기본 키를 선택할 것을 권장합니다.
#XMSG
recommendDifferentPrimaryKeyDesc=작업 코드가 이미 정의되어 있으면 배열 인덱스와 항목에 대해 다른 기본 키를 선택하여 열 중복 등의 문제를 방지하는 것이 권장됩니다.
#XMSG
selectPrimaryKeyItemLevel=헤더 레벨과 항목 레벨 모두에 대해 하나 이상의 기본 키를 선택해야 합니다.
#XMSG
selectPrimaryKeyItemLevelDesc=배열 또는 맵이 펼쳐져 있으면 헤더 레벨과 항목 레벨에서 각각 하나씩, 두 개의 기본 키를 선택해야 합니다.
#XMSG
invalidMapKey=헤더 레벨에서 하나 이상의 기본 키를 선택해야 합니다.
#XMSG
invalidMapKeyDesc=배열 또는 맵이 펼쳐져 있으면 헤더 레벨에서 기본 키를 선택해야 합니다.
#XFLD
txtSearchFields=대상 열 검색
#XFLD
txtName=이름
#XMSG
txtSourceColValidation=하나 이상의 소스 열이 지원되지 않습니다.
#XMSG
txtMappingCount=매핑({0})
#XMSG
schema=스키마
#XMSG
sourceColumn=소스 열
#XMSG
warningSourceSchema=스키마에 대한 모든 변경사항은 프로젝션 대화 상자의 매핑에 영향을 미칩니다.
#XCOL
txtTargetColName=대상 열(기술적 이름)
#XCOL
txtDataType=대상 데이터 유형
#XCOL
txtSourceDataType=소스 데이터 유형
#XCOL
srcColName=소스 열(기술적 이름)
#XCOL
precision=정밀도
#XCOL
scale=스케일
#XCOL
functionsOrConstants=함수/상수
#XCOL
txtTargetColBusinessName=대상 열(업무 이름)
#XCOL
prKey=기본 키
#XCOL
txtProperties=속성
#XBUT
txtOK=저장
#XBUT
txtCancel=취소
#XBUT
txtRemove=제거
#XFLD
txtDesc=내역
#XMSG
rftdMapping=매핑
#XFLD
@lblColumnDataType=데이터 유형
#XFLD
@lblColumnTechnicalName=기술적 이름
#XBUT
txtAutomap=자동 매핑
#XBUT
txtUp=위로 이동
#XBUT
txtDown=아래로 이동

#XTOL
txtTransformationHeader=프로젝션
#XTOL
editTransformation=편집
#XTOL
primaryKeyToolip=키


#XMSG
rftdFilter=필터
#XMSG
rftdFilterColumnCount=소스: {0}({1})
#XTOL
rftdFilterColSearch=검색
#XMSG
rftdFilterColNoData=표시할 열이 없습니다.
#XMSG
rftdFilteredColNoExps=필터 표현식이 없습니다.
#XMSG
rftdFilterSelectedColTxt=필터 추가 대상
#XMSG
rftdFilterTxt=다음에 사용 가능한 필터
#XBUT
rftdFilterSelectedAddColExp=표현식 추가
#YINS
rftdFilterNoSelectedCol=필터를 추가할 열을 선택하십시오.
#XMSG
rftdFilterExp=필터 표현식
#XMSG
rftdFilterNotAllowedColumn=이 열에서는 필터를 추가할 수 없습니다.
#XMSG
rftdFilterNotAllowedHead=열이 지원되지 않습니다.
#XMSG
rftdFilterNoExp=필터가 정의되지 않았습니다.
#XTOL
rftdfilteredTt=필터링됨
#XTOL
rftdremoveexpTt=필터 표현식 제거
#XTOL
validationMessageTt=유효성 확인 메시지
#XTOL
rftdFilterDateInp=일자 선택
#XTOL
rftdFilterDateTimeInp=일자/시간 선택
#XTOL
rftdFilterTimeInp=시간 선택
#XTOL
rftdFilterInp=값을 입력하십시오.
#XMSG
rftdFilterValidateEmptyMsg={0} 필터 표현식({1} 열)이 비어있습니다.
#XMSG
rftdFilterValidateInvalidNumericMsg={0} 필터 표현식({1} 열)에 잘못된 숫자 값이 있습니다.
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=필터 표현식에는 유효한 숫자 값이 포함되어야 합니다.
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=대상 오브젝트 스키마가 변경된 경우, 기본 페이지에서 "기존 대상 오브젝트에 매핑" 기능을 사용하여 변경 내용을 적용한 후 해당 소스로 대상 오브젝트를 다시 매핑하십시오.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=대상 테이블이 이미 있고 매핑에 스키마 변경이 포함된 경우, 복제 흐름을 배포하기 전에 대상 테이블을 적절히 변경해야 합니다.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=매핑에 스키마 변경이 포함된 경우, 복제 흐름을 배포하기 전에 대상 테이블을 적절히 변경해야 합니다.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=지원되지 않는 다음 열은 소스 정의에서 건너뜀: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=지원되지 않는 다음 열은 대상 정의에서 건너뜀: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=다음 오브젝트는 사용을 위해 표시되지 않아서 지원되지 않습니다. {0} {1} {0} {0} 복제 흐름에서 테이블을 사용하려면 (테이블 설정에서) 시맨틱 사용을 {2}분석 데이터세트{2}로 설정하면 안 됩니다.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=대상 오브젝트는 사용을 위해 표시되지 않아서 사용할 수 없습니다. {0} {0}  복제 흐름에서 테이블을 사용하려면 (테이블 설정에서) 시맨틱 사용을 {1}분석 데이터세트{1}로 설정하면 안 됩니다.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=이 이름을 가진 오브젝트가 이미 있습니다. 그러나, 사용을 위해 표시되었으므로 사용할 수 없습니다. {0} {0} 복제 흐름에서 테이블을 사용하려면 (테이블 설정에서) 시맨틱 사용을 {1}분석 데이터세트{1}로 설정하면 안 됩니다.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=이 이름의 오브젝트가 대상에 이미 있습니다. {0}하지만 이 오브젝트는 로컬 테이블이 아니므로 로컬 저장소에 대한 복제 흐름의 대상 오브젝트로 사용될 수 없습니다.
#XMSG:
targetAutoRenameUpdated=대상 열 이름이 변경되었습니다.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Google BigQuery에서 복제를 허용하도록 대상 열의 이름이 바뀌었습니다. 다음 사유 중 하나 때문입니다.{0} {1}{2}예약된 열 이름{3}{2}지원되지 않은 문자{3}{2}예약된 접두부{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Confluent에서 복제를 허용하도록 대상 열의 이름이 바뀌었습니다. 다음 사유 중 하나 때문입니다. {0} {1}{2}예약된 열 이름{3}{2}지원되지 않은 문자{3}{2}예약된 접두부{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=대상으로의 복제가 허용되도록 대상 열의 이름이 바뀌었습니다. 원인은 다음 중 하나입니다. {0} {1}{2}지원되지 않는 문자{3}{2}예약된 접두부{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=대상으로의 복제가 허용되도록 대상 열의 이름이 바뀌었습니다. 이는 다음 사유 중 하나 때문입니다. {0} {1}{2}예약된 열 이름{3}{2}지원되지 않는 문자{3}{2}예약된 접두부{3}{4}
#XMSG:
targetAutoDataType=대상 데이터 유형이 변경되었습니다.
#XMSG:
targetAutoDataTypeDesc=소스 데이터 유형이 Google BigQuery에서 지원되지 않으므로 대상 데이터 유형이 {0}(으)로 변경되었습니다.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=소스 데이터 유형이 대상 연결에서 지원되지 않으므로 대상 데이터 유형이 {0}(으)로 변경되었습니다.
#XMSG
projectionGBQUnableToCreateKey=기본 키가 생성되지 않습니다.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=Google BigQuery에서 최대 16개의 기본 키가 지원되지만 소스 오브젝트에 기본 키가 더 많이 있습니다. 대상 오브젝트에 기본 키가 생성되지 않습니다.
#XMSG
HDLFNoKeyError=하나 이상의 열을 기본 키로 정의하십시오.
#XMSG
HDLFNoKeyErrorDescription=오브젝트를 복제하려면 하나 이상의 열을 기본 키로 정의해야 합니다. 
#XMSG
HDLFNoKeyErrorExistingTarget=하나 이상의 열을 기본 키로 정의하십시오.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=데이터를 기존의 대상 오브젝트로 복제하려면 여기에 기본 키로 정의된 열이 하나 이상 있어야 합니다. {0} {0} 하나 이상의 열을 기본 키로 정의하는 다음과 같은 옵션이 있음: {0}{0}{1}로컬 테이블 편집기로 기존의 대상 오브젝트를 변경합니다. 그런 다음 복제 흐름을 다시 로드합니다.{0}{0}{1} 제 흐름에서 대상 오브젝트 이름을 바꿉니다. 이렇게 하면 실행이 시작되자마자 새로운 오브젝트가 생성됩니다. 이름을 바꾼 후에 하나 이상의 열을 기본 키로 정의할 수 있습니다.{0}{0}{1} 오브젝트를 하나 이상의 열이 이미 기본 키로 정의되어 있는 기존의 다른 대상 오브젝트로 매핑합니다.
#XMSG
HDLFSourceTargetDifferentKeysWarning=기본 키가 변경되었습니다.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=소스 오브젝트와 비교할 때, 대상 오브젝트의 기본 키로 다른 열을 정의했습니다. 나중에 데이터를 복제할 때 데이터 손상이 일어나는 것을 막기 위해 이 열이 모든 행을 고유하게 식별하는지 확인하십시오.{0} {0} 소스 오브젝트에서 다음 열이 기본 키로 정의되어 있음: {0} {1}
#XMSG
duplicateDPIDColumns=대상 열 이름을 변경하십시오.
#XMSG
duplicateDPIDDColumnsDesc1=이 대상 열 이름은 기술 열 이름으로 예약되어 있습니다. 프로젝션을 저장하려면 다른 이름을 입력하십시오.
#XMSG:
targetAutoRenameDPID=대상 열 이름이 변경되었습니다.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=키 없이 ABAP 소스에서 복제할 수 있도록 대상 열의 이름이 변경되었습니다. 이는 다음 사유 중 하나 때문입니다. - {0} {1}{2} 예약된 열 이름, {3}{2} 지원되지 않는 문자, {3}{2} 예약된 접두사 {3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle={0} 대상 설정
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle={0} 소스 설정
#XBUT
connectionSettingSave=저장
#XBUT
connectionSettingCancel=취소
#XBUT: Button to keep the object level settings
txtKeep=유지
#XBUT: Button to overwrite the Object level settings
txtOverwrite=덮어쓰기
#XFLD
targetConnectionThreadlimit=초기 로드의 대상 스레드 한도(1-100)
#XFLD
connectionThreadLimit=초기 로드의 소스 스레드 한도(1-100)
#XFLD
maxConnection=복제 스레드 한도(1-100)
#XFLD
kafkaNumberOfPartitions=파티션 수
#XFLD
kafkaReplicationFactor=복제 계수
#XFLD
kafkaMessageEncoder=메시지 인코더
#XFLD
kafkaMessageCompression=메시지 압축
#XFLD
fileGroupDeltaFilesBy=그룹 델타 기준
#XFLD
fileFormat=파일 유형
#XFLD
csvEncoding=CSV 인코딩
#XFLD
abapExitLbl=ABAP Exit
#XFLD
deltaPartition=델타 로드의 오브젝트 스레드 수(1-10)
#XFLD
clamping_Data=데이터 자르기 실패
#XFLD
fail_On_Incompatible=호환되지 않는 데이터에서 오류 발생
#XFLD
maxPartitionInput=최대 파티션 수
#XFLD
max_Partition=최대 파티션 수 정의
#XFLD
include_SubFolder=하위 폴더 포함
#XFLD
fileGlobalPattern=파일 이름의 글로벌 패턴
#XFLD
fileCompression=파일 압축
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=파일 구분자
#XFLD
fileIsHeaderIncluded=파일 헤더
#XFLD
fileOrient=방향
#XFLD
gbqWriteMode=쓰기 모드
#XFLD
suppressDuplicate=중복 표시 안 함
#XFLD
apacheSpark=Apache Spark 호환성 사용
#XFLD
clampingDatatypeCb=십진 부동 소수점 데이터 유형 고정
#XFLD
overwriteDatasetSetting=오브젝트 레벨에서 대상 설정 덮어쓰기
#XFLD
overwriteSourceDatasetSetting=오브젝트 레벨에서 소스 설정 덮어쓰기
#XMSG
kafkaInvalidConnectionSetting={0} ~ {1} 사이의 숫자를 입력하십시오.
#XMSG
MinReplicationThreadErrorMsg={0}보다 큰 숫자를 입력하십시오.
#XMSG
MaxReplicationThreadErrorMsg={0}보다 작은 숫자를 입력하십시오.
#XMSG
DeltaThreadErrorMsg=1과 10 사이의 값을 입력하십시오.
#XMSG
MaxPartitionErrorMsg=1 <= x <= 2147483647 사이의 값을 입력하십시오. 기본값은 10입니다.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal={0} 및 {1} 사이의 정수를 입력하십시오.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=브로커의 복제 계수 사용
#XFLD
serializationFormat=일련화 형식
#XFLD
compressionType=압축 유형
#XFLD
schemaRegistry=스키마 레지스트리 사용
#XFLD
subjectNameStrat=주제 이름 전략
#XFLD
compatibilityType=호환성 유형
#XFLD
confluentTopicName=토픽 이름
#XFLD
confluentRecordName=레코드 이름
#XFLD
confluentSubjectNamePreview=주제 이름 미리보기
#XMSG
serializationChangeToastMsgUpdated2=스키마 레지스트리가 활성화되어 있지 않아서 일련화 형식이 JSON으로 변경되었습니다. 일련화 형식을 AVRO로 다시 변경하려면 먼저 스키마 레지스트리를 활성화해야 합니다.
#XBUT
confluentTopicNameInfo=토픽 이름은 항상 대상 오브젝트 이름을 기반으로 합니다. 대상 오브젝트의 이름을 변경하면 토픽 이름을 변경할 수 있습니다.
#XMSG
emptyRecordNameValidationHeaderMsg=레코드 이름을 입력하십시오.
#XMSG
emptyPartionHeader=파티션 수를 입력하십시오.
#XMSG
invalidPartitionsHeader=유효한 파티션 수를 입력하십시오.
#XMSG
invalidpartitionsDesc=1~200,000 사이의 숫자를 입력하십시오.
#XMSG
emptyrFactorHeader=복제 계수를 입력하십시오.
#XMSG
invalidrFactorHeader=유효한 복제 계수를 입력하십시오.
#XMSG
invalidrFactorDesc=1~32,767 사이의 숫자를 입력하십시오.
#XMSG
emptyRecordNameValidationDescMsg=일련화 형식 "AVRO"를 사용하는 경우, 다음 문자만 지원됨: {0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(밑줄)
#XMSG
validRecordNameValidationHeaderMsg=유효한 레코드 이름을 입력하십시오.
#XMSG
validRecordNameValidationDescMsgUpdated=일련화 형식 "AVRO"가 사용되므로, 레코드 이름은 영숫자(A-Z, a-z, 0-9)와 밑줄(_)로만 구성되어야 합니다. 문자나 밑줄로 시작해야 합니다.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=하나 이상의 오브젝트에 "초기 및 델타" 로드 유형이 있으면 즉시 "델타 로드의 오브젝트 스레드 수"를 설정할 수 있습니다.
#XMSG
invalidTargetName=잘못된 열 이름
#XMSG
invalidTargetNameDesc=대상 열 이름은 영숫자(A-Z, a-z, 0-9)와 밑줄(_)로만 구성되어야 합니다.
#XFLD
consumeOtherSchema=다른 스키마 버전 사용
#XFLD
ignoreSchemamissmatch=스키마 불일치 무시
#XFLD
confleuntDatatruncation=데이터 자르기 실패
#XFLD
isolationLevel=격리 레벨
#XFLD
confluentOffset=시작점
#XFLD
signavioGroupDeltaFilesByText=없음
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=아니오
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=아니오

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=프로젝션
#XBUT
txtAdd=추가
#XBUT
txtEdit=편집
#XMSG
transformationText=필터 또는 매핑을 설정하려면 프로젝션을 추가하십시오.
#XMSG
primaryKeyRequiredText=스키마 구성을 사용하여 기본 키를 선택하십시오.
#XFLD
lblSettings=설정
#XFLD
lblTargetSetting={0}: 대상 설정
#XMSG
@csvRF=폴더의 모든 파일에 적용하려는 스키마 정의가 포함된 파일을 선택합니다.
#XFLD
lblSourceColumns=소스 열
#XFLD
lblJsonStructure=JSON 구조
#XFLD
lblSourceSetting={0}: 소스 설정
#XFLD
lblSourceSchemaSetting={0}: 소스 스키마 설정
#XBUT
messageSettings=메시지 설정
#XFLD
lblPropertyTitle1=오브젝트 속성
#XFLD
lblRFPropertyTitle=복제 흐름 속성
#XMSG
noDataTxt=표시할 열이 없습니다.
#XMSG
noTargetObjectText=대상 오브젝트를 선택하지 않았습니다.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=대상 열
#XMSG
searchColumns=열 검색
#XTOL
cdcColumnTooltip=델타 캡쳐 열
#XMSG
sourceNonDeltaSupportErrorUpdated=소스 오브젝트에서는 델타 캡처가 지원되지 않습니다.
#XMSG
targetCDCColumnAdded=2개의 대상 열이 델타 캡쳐에 추가되었습니다.
#XMSG
deltaPartitionEnable=델타 로드에 대한 오브젝트 스레드 한도가 소스 설정에 추가되었습니다.
#XMSG
attributeMappingRemovalTxt=신규 대상 오브젝트에 대해 지원되지 않는 잘못된 매핑을 제거하는 중입니다.
#XMSG
targetCDCColumnRemoved=델타 캡쳐에 사용되는 2개의 대상 열이 제거되었습니다.
#XMSG
replicationLoadTypeChanged=로드 유형이 "초기 및 델타"로 변경되었습니다.
#XMSG
sourceHDLFLoadTypeError=로드 유형을 "초기 및 델타"로 변경하십시오. .
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=SAP HANA Cloud, 데이터 레이크 파일 연결 유형을 사용하여 소스 연결에서 SAP Datasphere로 오브젝트를 복제하려면 로드 유형 "초기 및 델타"를 사용해야 합니다.
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=델타 캡처를 활성화하십시오.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=SAP HANA Cloud, 데이터 레이크 파일 연결 유형을 사용하여 소스 연결에서 SAP Datasphere로 오브젝트를 복제하려면 델타 캡처를 활성화해야 합니다.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=대상 오브젝트를 변경하십시오.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=델타 캡처가 비활성화되어 대상 오브젝트를 사용할 수 없습니다. 대상 오브젝트의 이름을 변경하거나(델타 캡처를 사용하는 신규 오브젝트를 생성할 수 있음) 델타 캡처가 활성화된 기존 오브젝트에 매핑할 수 있습니다.
#XMSG
deltaPartitionError=델타 로드에 유효한 오브젝트 스레드 수를 입력하십시오.
#XMSG
deltaPartitionErrorDescription=1과 10 사이의 값을 입력하십시오.
#XMSG
deltaPartitionEmptyError=델타 로드의 오브젝트 스레드 수를 입력하십시오.
#XFLD
@lblColumnDescription=내역
#XMSG
@lblColumnDescriptionText1=기술적 목적 - 기본 키가 없는 ABAP 기반 소스 오브젝트를 복제하는 동안 발생하는 이슈로 인한 중복 레코드를 처리합니다.
#XFLD
storageType=저장소
#XFLD
skipUnmappedColLbl=매핑되지 않은 열 스킵
#XFLD
abapContentTypeLbl=컨텐트 유형
#XFLD
autoMergeForTargetLbl=데이터 자동 병합
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=일반
#XFLD
lblBusinessName=업무 이름
#XFLD
lblTechnicalName=기술적 이름
#XFLD
lblPackage=패키지
#XFLD
statusPanel=실행 상태
#XBTN: Schedule dropdown menu
SCHEDULE=일정
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=일정 편집
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=일정 삭제
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=일정 생성
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=일정 유효성 확인 실패
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=복제 흐름이 현재 배포 중이어서 일정을 생성할 수 없습니다.{0}복제 흐름이 배포될 때까지 기다리십시오.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=로드 유형이 "초기 및 델타"인 오브젝트를 포함하는 복제 흐름에서는 일정을 생성할 수 없습니다.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=로드 유형이 "초기 및 델타/델타만"인 오브젝트를 포함하는 복제 흐름에서는 일정을 생성할 수 없습니다.
#XFLD : Scheduled popover
SCHEDULED=예정됨
#XFLD
CREATE_REPLICATION_TEXT=복제 흐름 생성
#XFLD
EDIT_REPLICATION_TEXT=복제 흐름 편집
#XFLD
DELETE_REPLICATION_TEXT=복제 흐름 삭제
#XFLD
REFRESH_FREQUENCY=주기
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=기존 일정에서 "초기 및 델타"라는 로드 유형을 지원하지 않아서{0}복제 흐름을 배포할 수 없습니다.{0}{0}복제 흐름을 배포하려면 모든 오브젝트의 로드 유형을{0} "초기만"으로 설정해야 합니다. 또는 일정을 삭제하고 복제 흐름을 배포한 후에{0} 새로운 실행을 시작할 수 있습니다. 그러면 계속해서 실행되고 로드 유형이 "초기 및 델타"인{0} 오브젝트를 지원합니다.
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=기존 일정에서 "초기 및 델타/델타만"라는 로드 유형을 지원하지 않아서{0}복제 흐름을 배포할 수 없습니다.{0}{0}복제 흐름을 배포하려면 모든 오브젝트의 로드 유형을{0} "초기만"으로 설정해야 합니다. 또는 일정을 삭제하고 복제 흐름을 배포한 후에{0} 새로운 실행을 시작할 수 있습니다. 그러면 계속해서 실행되고{0} 로드 유형이 "초기 및 델타/델타만"인 오브젝트를 지원합니다.
#XMSG
SCHEDULE_EXCEPTION=일정 세부사항을 가져오지 못했습니다.
#XFLD: Label for frequency column
everyLabel=간격
#XFLD: Plural Recurrence text for Hour
hoursLabel=시간
#XFLD: Plural Recurrence text for Day
daysLabel=일
#XFLD: Plural Recurrence text for Month
monthsLabel=개월
#XFLD: Plural Recurrence text for Minutes
minutesLabel=분
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=일정 계획 가능 여부에 대한 정보를 가져오지 못했습니다.
#XFLD :Paused field
PAUSED=일시 중지됨
#XMSG
navToMonitoring=흐름 모니터에서 열기
#XFLD
statusLbl=상태
#XFLD
lblLastRunExecuted=최종 실행 시작
#XFLD
lblLastExecuted=최종 실행
#XFLD: Status text for Completed
statusCompleted=완료됨
#XFLD: Status text for Running
statusRunning=실행 중
#XFLD: Status text for Failed
statusFailed=실패
#XFLD: Status text for Stopped
statusStopped=중지됨
#XFLD: Status text for Stopping
statusStopping=중지 중
#XFLD: Status text for Active
statusActive=활성
#XFLD: Status text for Paused
statusPaused=일시 중지됨
#XFLD: Status text for not executed
lblNotExecuted=아직 실행 안 함
#XFLD
messagesSettings=메시지 설정
#XTOL
@validateModel=유효성 확인 메시지
#XTOL
@hierarchy=계층구조
#XTOL
@columnCount=열 수
#XMSG
VAL_PACKAGE_CHANGED=이 오브젝트를 패키지 "{1}"에 지정했습니다. "저장"을 클릭하여 이 변경사항을 확인하고 유효성을 확인합니다. 저장 후에는 이 편집기에서 패키지에 대한 지정을 취소할 수 없습니다.
#XMSG
MISSING_DEPENDENCY=오브젝트 "{0}"의 종속성을 "{1}" 패키지에서 결정할 수 없습니다.
#XFLD
deltaLoadInterval=델타 로드 간격
#XFLD
lblHour=시(0-24)
#XFLD
lblMinutes=분(0-59)
#XMSG
maxHourOrMinErr=0 ~ {0} 사이의 값을 입력하십시오.
#XMSG
maxDeltaInterval=델타 로드 간격의 최대값은 24시간입니다.{0}분 값 또는 시 값을 적절히 변경하십시오.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=대상 컨테이너 경로
#XFLD
confluentSubjectName=주제 이름
#XFLD
confluentSchemaVersion=스키마 버전
#XFLD
confluentIncludeTechKeyUpdated=기술 키 포함
#XFLD
confluentOmitNonExpandedArrays=펼치지 않은 배열 생략
#XFLD
confluentExpandArrayOrMap=배열 또는 맵 펼치기
#XCOL
confluentOperationMapping=작업 매핑
#XCOL
confluentOpCode=작업 코드
#XFLD
confluentInsertOpCode=삽입
#XFLD
confluentUpdateOpCode=업데이트
#XFLD
confluentDeleteOpCode=삭제
#XFLD
expandArrayOrMapNotSelectedTxt=선택 안 함
#XFLD
confluentSwitchTxtYes=예
#XFLD
confluentSwitchTxtNo=아니오
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=오류
#XTIT
executeWarning=경고
#XMSG
executeunsavederror=실행하기 전에 복제 흐름을 저장하십시오.
#XMSG
executemodifiederror=복제 흐름에 저장되지 않은 변경사항이 있습니다. 복제 흐름을 저장하십시오.
#XMSG
executeundeployederror=복제 흐름을 실행하려면 먼저 배포해야 합니다.
#XMSG
executedeployingerror=배포가 종료될 때까지 기다리십시오.
#XMSG
msgRunStarted=실행이 시작되었습니다.
#XMSG
msgExecuteFail=복제 흐름을 실행하지 못했습니다.
#XMSG
titleExecuteBusy=잠시 기다려 주십시오.
#XMSG
msgExecuteBusy=복제 흐름 실행을 위해 데이터를 준비하고 있습니다.
#XTIT
executeConfirmDialog=경고
#XMSG
msgExecuteWithValidations=복제 흐름에 유효성 확인 오류가 있습니다. 복제 흐름을 실행하면 오류가 발생할 수 있습니다.
#XMSG
msgRunDeployedVersion=배포할 변경사항이 있습니다. 마지막으로 배포된 버전의 복제 흐름이 실행됩니다. 계속하시겠습니까?
#XBUT
btnExecuteAnyway=실행
#XBUT
btnExecuteClose=닫기
#XBUT
loaderClose=닫기
#XTIT
loaderTitle=로드하는 중
#XMSG
loaderText=서버에서 세부사항을 가져오는 중
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=이 SAP 이외의 대상 연결에 대해 복제 흐름을 시작할 수 없습니다.
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=이번 달에 사용 가능한 아웃바운드 볼륨이 없습니다.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=관리자가 이 테넌트의 프리미엄 아웃바운드 블록을 늘려서
#XMSG
premiumOutBoundRFAdminErrMsgPart2=이번 달에 사용 가능한 아웃바운드 볼륨을 작성할 수 있습니다.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=오류
#XTIT
deployInfo=정보
#XMSG
deployCheckFailException=배포 중 예외가 발생했습니다.
#XMSG
deployGBQFFDisabled=이 기능에 대한 유지보수를 진행 중이어서 현재 Google BigQuery로의 대상 연결을 사용하여 복제 흐름을 배포할 수 없습니다.
#XMSG
deployKAFKAFFDisabled=이 기능에 대한 유지보수를 진행 중이어서 현재 Apache Kafka로의 대상 연결을 사용하여 복제 흐름을 배포할 수 없습니다.
#XMSG
deployConfluentDisabled=이 기능에 대한 유지보수를 진행 중이어서 현재 Confluent Kafka로의 대상 연결을 사용하여 복제 흐름을 배포할 수 없습니다.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=다음 대상 오브젝트에서, 델타 캡처 테이블 이름이 이미 저장소의 다른 테이블에 사용되고 있습니다({0}). 이 대상 오브젝트 이름을 변경하여 관련 델타 캡처 테이블에 고유한 이름을 지정해야만 복제 흐름을 배포할 수 있습니다.
#XMSG
deployDWCSourceFFDisabled=이 기능에 대한 유지보수를 진행 중이어서 SAP Datasphere를 소스로 사용하는 복제 흐름을 배포할 수 없습니다.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=현재 이 기능에 대한 유지보수를 진행 중이어서 델타 지원 로컬 테이블을 소스 오브젝트로 포함하는 복제 흐름을 배포하는 것은 불가능합니다.
#XMSG
deployHDLFSourceFFDisabled=연결 유형이 SAP HANA Cloud, 데이터 레이크 파일인 소스 연결을 가진 복제 흐름을 현재 배포할 수 없습니다. 유지보수를 수행 중이기 때문입니다.
#XMSG
deployObjectStoreAsSourceFFDisabled=클라우드 저장소 제공자를 소스로 하는 복제 흐름은 현재 배포할 수 없습니다.
#XMSG
deployConfluentSourceFFDisabled=이 기능에 대한 유지보수를 진행 중이어서 Confluent Kafka를 소스로 사용하는 복제 흐름을 현재 배포할 수 없습니다.
#XMSG
deployMaxDWCNewTableCrossed=큰 복제 흐름에서는 모든 항목을 한번에 "저장 및 배포"할 수 없습니다. 먼저 복제 흐름을 저장한 다음 배포하십시오.
#XMSG
deployInProgressInfo=배포가 이미 진행 중입니다.
#XMSG
deploySourceObjectInUse=소스 오브젝트 {0}은(는) 이미 복제 흐름 {1}에 사용되고 있습니다.
#XMSG
deployTargetSourceObjectInUse=소스 오브젝트 {0}은(는) 이미 복제 흐름 {1}에 사용되고 있습니다. 대상 오브젝트 {2}은(는) 이미 복제 흐름 {3}에 사용되고 있습니다. 
#XMSG
deployReplicationFlowCheckError=복제 흐름 검증 중 오류 발생: {0}
#XMSG
preDeployTargetObjectInUse={0} 대상 오브젝트가 복제 흐름 {1}에서 이미 사용되고 있으며 두 개의 서로 다른 복제 흐름에서 동일한 대상 오브젝트를 가질 수 없습니다. 다른 대상 오브젝트를 선택하고 다시 시도하십시오.
#XMSG
runInProgressInfo=복제 흐름이 이미 실행 중입니다.
#XMSG
deploySignavioTargetFFDisabled=이 기능에 대한 유지보수를 수행 중이므로 SAP Signavio를 대상으로 사용하는 복제 흐름을 배포할 수 없습니다.
#XMSG
deployHanaViewAsSourceFFDisabled=선택한 소스 연결에 대해 뷰를 소스 오브젝트로 사용하는 복제 흐름을 배포하는 것은 현재 불가능합니다. 나중에 다시 시도하십시오.
#XMSG
deployMsOneLakeTargetFFDisabled=이 기능에 대한 유지보수를 수행 중이므로 MS OneLake를 대상으로 사용하는 복제 흐름을 배포할 수 없습니다.
#XMSG
deploySFTPTargetFFDisabled=이 기능에 대한 유지보수를 수행 중이므로 SFTP를 대상으로 사용하는 복제 흐름을 배포할 수 없습니다.
#XMSG
deploySFTPSourceFFDisabled=이 기능에 대한 유지보수를 수행 중이므로 SFTP를 소스로 사용하는 복제 흐름을 배포할 수 없습니다.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=기술적 이름
#XFLD
businessNameInRenameTarget=업무 이름
#XTOL
renametargetDialogTitle=대상 오브젝트 이름 바꾸기
#XBUT
targetRenameButton=이름 바꾸기
#XBUT
targetRenameCancel=취소
#XMSG
mandatoryTargetName=이름을 입력해야 합니다.
#XMSG
dwcSpecialChar=_(밑줄)은 허용되는 유일한 특수 문자입니다.
#XMSG
dwcWithDot=대상 테이블 이름은 라틴 문자, 숫자, 밑줄(_), 마침표(.)로 구성될 수 있습니다. 첫 번째 문자는 문자, 숫자 또는 밑줄(마침표 아님)이어야 합니다.
#XMSG
nonDwcSpecialChar=허용되는 특수 문자는 _(밑줄) -(하이픈), .(마침표)입니다.
#XMSG
firstUnderscorePattern=이름은 _(밑줄)로 시작하면 안 됩니다.

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: SQL 테이블 생성 문 보기
#XMSG
sqlDialogMaxPKWarning=Google BigQuery에서는 최대 16개의 기본 키가 지원되며 소스 오브젝트의 수가 더 많습니다. 따라서 이 문에는 기본 키가 정의되어 있지 않습니다.
#XMSG
sqlDialogIncomptiblePKTypeWarning=하나 이상의 소스 열에 Google BigQuery에서 기본 키로 정의할 수 없는 데이터 유형이 있습니다. 따라서 이 경우에는 기본 키가 정의되지 않습니다. Google BigQuery에서는 다음 데이터 유형만 기본 키를 가질 수 있습니다(BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP).
#XBUT
copyAndCloseDDL=복사 후 닫기
#XBUT
closeDDL=닫기
#XMSG
copiedToClipboard=클립보드에 복사되었습니다.


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=''{0}'' 오브젝트에는 끝이 없으므로(초기 및 델타/델타만 로드 유형의 오브젝트를 포함하므로) 태스크 체인에 속할 수 없습니다.
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=''{0}'' 오브젝트에는 끝이 없으므로(초기 및 델타 로드 유형의 오브젝트를 포함하므로) 태스크 체인에 속할 수 없습니다.
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE="{0}" 오브젝트를 태스크 체인에 추가할 수 없습니다.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=저장되지 않은 대상 오브젝트가 있습니다. 다시 저장하십시오.{0}{0} 이 기능의 동작이 변경되었습니다. 이전에는, 복제 흐름이 배포된 시점에 대상 환경에만 대상 오브젝트가 생성되었습니다.{0} 이제는 복제 흐름이 저장될 때 오브젝트가 이미 생성된 상태입니다. 이 변경이 있기 전에 복제 흐름이 생성되었으며 새로운 오브젝트를 포함합니다.{0} 복제 흐름을 배포하기 전에 다시 한 번 저장해야만 새로운 오브젝트가 문제없이 포함될 수 있습니다.
#XMSG
confirmChangeContentTypeMessage=컨텐트 유형을 변경하려 합니다. 계속하면 기존 프로젝션이 모두 삭제됩니다.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=주제 이름
#XFLD
schemaDialogVersionName=스키마 버전
#XFLD
includeTechKey=기술 키 포함
#XFLD
segementButtonFlat=플랫
#XFLD
segementButtonNested=중첩됨
#XMSG
subjectNamePlaceholder=주제 이름 검색

#XMSG
@EmailNotificationSuccess=런타임 전자메일 통지의 구성이 저장되었습니다.

#XFLD
@RuntimeEmailNotification=런타임 전자메일 통지

#XBTN
@TXT_SAVE=저장


