#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Replicatiestroom

#XFLD: Edit Schema button text
editSchema=Schema bewerken

#XTIT : Properties heading
configSchema=Schema configureren

#XFLD: save changed button text
applyChanges=Wijzigingen toepassen


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Bronverbinding selecteren
#XFLD
sourceContainernEmptyText=Container selecteren
#XFLD
targetConnectionEmptyText=Doelverbinding selecteren
#XFLD
targetContainernEmptyText=Container selecteren
#XFLD
sourceSelectObjectText=Bronobject selecteren
#XFLD
sourceObjectCount=Bronobjecten ({0})
#XFLD
targetObjectText=Doelobjecten
#XFLD
confluentBrowseContext=Context selecteren
#XBUT
@retry=Opnieuw proberen
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Tenantupgrade in uitvoering.

#XTOL
browseSourceConnection=Bronverbinding browsen
#XTOL
browseTargetConnection=Doelverbinding browsen
#XTOL
browseSourceContainer=Broncontainer browsen
#XTOL
browseAndAddSourceDataset=Bronobjecten toevoegen
#XTOL
browseTargetContainer=Doelcontainer browsen
#XTOL
browseTargetSetting=Doelinstellingen browsen
#XTOL
browseSourceSetting=Broninstellingen browsen
#XTOL
sourceDatasetInfo=Info
#XTOL
sourceDatasetRemove=Verwijderen
#XTOL
mappingCount=Dit geeft het totaal aantal toewijzingen/uitdrukkingen weer die niet op naam zijn gebaseerd.
#XTOL
filterCount=Die geeft het totale aantal filtercondities weer.
#XTOL
loading=Bezig met laden...
#XCOL
deltaCapture=Deltaopname
#XCOL
deltaCaptureTableName=Tabel Deltaopname
#XCOL
loadType=Type laden
#XCOL
deleteAllBeforeLoading=Alles verwijderen voor het laden
#XCOL
transformationsTab=Projecties
#XCOL
settingsTab=Instellingen

#XBUT
renameTargetObjectBtn=Doelobject hernoemen
#XBUT
mapToExistingTargetObjectBtn=Aan bestaand doelobject toewijzen
#XBUT
changeContainerPathBtn=Containerpad wijzigen
#XBUT
viewSQLDDLUpdated=SQL-statement Create Table weergeven
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Het bronobject ondersteunt geen deltaopname, maar het geselecteerde doelobject heeft de deltaopnameoptie ingeschakeld.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Doelobject kan niet worden gebruikt omdat delta-opname is ingeschakeld,{0}hoewel bronobject delta-opname niet ondersteunt.{1}U kunt een ander doelobject selecteren dat geen delta-opname ondersteunt.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Het doelobject met deze naam bestaat al. Het kan echter niet worden gebruikt{0}omdat delta-opname is ingeschakeld, hoewel bronobject{0}delta-opname niet ondersteunt.{1}U kunt de naam van een bestaand doelobject invoeren dat delta-opname{0}niet ondersteunt, of een naam invoeren die nog niet bestaat.
#XBUT
copySQLDDLUpdated=SQL-statement Create Table kopiëren
#XMSG
targetObjExistingNoCDCColumnUpdated=Bestaande tabellen in Google BigQuery moeten de volgende kolommen voor wijzigingsgegevensinvoer omvatten (CDC):{0}{0}{1} operation_flag {0}{1}  is_deleted{0}{1}  recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=De volgende bronobjecten worden niet ondersteund omdat zij geen primaire key hebben, of omdat zij een verbinding gebruiken die niet aan de voorwaarden voldoet om de primaire key op te halen:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Zie SAP KBA 3531135 voor een mogelijke oplossing.
#XLST: load type list values
initial=Alleen initieel
@emailUpdateError=Fout bij bijwerken e-mailmeldingenlijst

#XLST
initialDelta=Initieel en delta

#XLST
deltaOnly=Alleen delta
#XMSG
confluentDeltaLoadTypeInfo=Voor een Confluent Kafka-bron worden alleen de laadtypen Initieel en Delta ondersteund.
#XMSG
confirmRemoveReplicationObject=Wilt u de verwijdering van de replicatie bevestigen?
#XMSG
confirmRemoveReplicationTaskPrompt=Deze actie verwijdert bestaande replicaties. Doorgaan?
#XMSG
confirmTargetConnectionChangePrompt=Deze actie reset de doelverbinding, doelcontainer en alle doelobjecten verwijderen. Doorgaan?
#XMSG
confirmTargetContainerChangePrompt=Deze actie reset de doelcontainer en verwijdert alle bestaande doelobjecten. Doorgaan?
#XMSG
confirmRemoveTransformObject=Verwijdering van projectie {0} bevestigen?
#XMSG
ErrorMsgContainerChange=Er is een fout opgetreden bij het wijzigen van het containerpad.
#XMSG
infoForUnsupportedDatasetNoKeys=De volgende bronobjecten worden niet ondersteund omdat ze geen primaire sleutel hebben:
#XMSG
infoForUnsupportedDatasetView=De volgende bronobjecten van het type Views worden niet ondersteund:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Het volgende bronobject wordt niet ondersteund omdat het een SQL-view is die invoerparameters bevat:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=De volgende bronobjecten worden niet ondersteund omdat extractie voor hun is uitgeschakeld:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=De toegestane serialiseringsindelingen voor Confluent-verbindingen zijn AVRO en JSON. De volgende objecten worden niet ondersteund omdat zij een andere serialiseringsindeling gebruiken:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Het schema voor de volgende objecten kan niet worden opgehaald. Selecteer de passende context of verifieer schemaregisterconfiguratie
#XTOL: warning dialog header on deleting replication task
deleteHeader=Verwijderen
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=De instelling 'Alles verwijderen voor het laden' wordt niet ondersteund voor Goggle BigQuery.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=De instelling 'Alles verwijderen voor het laden' verwijdert het object (onderwerp) en maakt het opnieuw aan voor elke replicatie. Ook alle toegewezen berichten worden hierbij verwijderd.
#XTOL
DeleteAllBeforeLoadingLTFInfo=De instelling 'Alles verwijderen voor' wordt niet ondersteund voor dit doeltype.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Technische naam
#XCOL
connBusinessName=Bedrijfsnaam
#XCOL
connDescriptionName=Omschrijving
#XCOL
connType=Type
#XMSG
connTblNoDataFoundtxt=Geen verbindingen gevonden
#XMSG
connectionError=Er is een fout opgetreden bij het ophalen van verbindingen.
#XMSG
connectionCombinationUnsupportedErrorTitle=Verbindingscombinatie niet ondersteund
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Replicatie van {0} naar {1} momenteel niet ondersteund.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Verbindingstypecombinatie wordt niet ondersteund
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Repliceren van een verbinding met het verbindingstype SAP HANA Cloud, Data Lake Files naar {0} wordt niet ondersteund. U kunt alleen naar SAP Datasphere repliceren.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Selecteren
#XBUT
containerCancelBtn=Annuleren
#XTOL
containerSelectTooltip=Selecteren
#XTOL
containerCancelTooltip=Annuleren
#XMSG
containerContainerPathPlcHold=Containerpad
#XFLD
containerContainertxt=Container
#XFLD
confluentContainerContainertxt=Context
#XMSG
infoMessageForSLTSelection=Alleen /SLT/massatransport-ID is toegestaan als container. Selecteer een massatransport-ID onder SLT (indien beschikbaar) en klik op verzenden.
#XMSG
msgFetchContainerFail=Er is een fout opgetreden bij het ophalen van containergegevens.
#XMSG
infoMessageForSLTHidden=Deze verbinding ondersteunt geen SLT-mappen, hierdoor verschijnen zij niet in de onderstaande lijst.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Selecteer een container die submappen bevat.
#XMSG
sftpIncludeSubFolderText=False
#XMSG
sftpIncludeSubFolderTextNew=Nee

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(nog geen filtermapping)
#XMSG
failToFetchRemoteMetadata=Er is een fout opgetreden bij het ophalen van metagegevens.
#XMSG
failToFetchData=Er is een fout opgetreden bij het ophalen van het bestaande doel.
#XCOL
@loadType=Laadtype
#XCOL
@deleteAllBeforeLoading=Alles verwijderen voor het laden

#XMSG
@loading=Bezig met laden...
#XFLD
@selectSourceObjects=Bronobjecten selecteren
#XMSG
@exceedLimit=U kunt niet meer dan {0} objecten tegelijk importeren. Deselecteer minstens {1} objecten.
#XFLD
@objects=Objecten
#XBUT
@ok=OK
#XBUT
@cancel=Annuleren
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Volgende
#XBUT
btnAddSelection=Selectie toevoegen
#XTOL
@remoteFromSelection=Verwijderen uit selectie
#XMSG
@searchInForSearchField=Zoeken in {0}

#XCOL
@name=Technische naam
#XCOL
@type=Type
#XCOL
@location=Locatie
#XCOL
@label=Bedrijfsnaam
#XCOL
@status=Status

#XFLD
@searchIn=Zoeken in:
#XBUT
@available=Beschikbaar
#XBUT
@selection=Selectie

#XFLD
@noSourceSubFolder=Tabellen en views
#XMSG
@alreadyAdded=Al aanwezig in diagram
#XMSG
@askForFilter=Er zijn meer dan {0} items. Voer een filterstring in om het aantal filters te beperken.
#XFLD: success label
lblSuccess=Gelukt
#XFLD: ready label
lblReady=Gereed
#XFLD: failure label
lblFailed=Mislukt
#XFLD: fetching status label
lblFetchingDetail=Details worden opgehaald

#XMSG Place holder text for tree filter control
filterPlaceHolder=Tekst invoeren om beginobjecten te filteren
#XMSG Place holder text for server search control
serverSearchPlaceholder=Invoeren en op Enter drukken om te zoeken
#XMSG
@deployObjects={0} objecten importeren...
#XMSG
@deployObjectsStatus=Aantal objecten dat niet is geïmporteerd: {0}. Het aantal objecten dat niet kan worden geïmporteerd: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Kon lokale respositorybrowser niet openen.
#XMSG
@openRemoteSourceBrowserError=Kon bronobjecten niet ophalen.
#XMSG
@openRemoteTargetBrowserError=Kon doelobjecten niet ophalen.
#XMSG
@validatingTargetsError=Er is een fout opgetreden bij het valideren van doelen.
#XMSG
@waitingToImport=Gereed voor import

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Het maximumaantal objecten is overschreden. Selecteer maximaal 500 objecten voor één replicatiestroom.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Technische naam
#XFLD
sourceObjectBusinessName=Bedrijfsnaam
#XFLD
sourceNoColumns=Aantal kolommen
#XFLD
containerLbl=Container

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=U moet een bronverbinding selecteren voor de replicatiestroom.
#XMSG
validationSourceContainerNonExist=U moet een container selecteren voor de bronverbinding.
#XMSG
validationTargetNonExist=U moet een doelverbinding selecteren voor de replicatiestroom.
#XMSG
validationTargetContainerNonExist=U moet een container selecteren voor de doelverbinding.
#XMSG
validationTruncateDisabledForObjectTitle=Replicatie naar objectopslag.
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Replicatie naar een cloudopslag is alleen mogelijk als de optie ''Alles verwijderen voor het laden'' is ingesteld of als het doelobject niet bestaat in het doel.{0}{0} Om toch replicatie in te schakelen voor objecten waarvoor de optie ''Alles verwijderen voor het laden'' niet is ingesteld, zorgt u ervoor dat het doelobject nog niet in het systeem bestaat voordat u de replicatiestroom uitvoert.
#XMSG
validationTaskNonExist=U moet minstens een replicatie in de replicatiestroom instellen.
#XMSG
validationTaskTargetMissing=U moet een doel hebben voor de replicatie met de bron: {0}
#XMSG
validationTaskTargetIsSAC=Geselecteerd doel is een SAC-artefact: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Geselecteerde doel is geen ondersteunde lokale tabel: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Een object met deze naam bestaat al in het doel. Het object kan echter niet worden gebruikt als doelobject voor een replicatiestroom naar de lokale repository, omdat dit geen lokale tabel is.
#XMSG
validateSourceTargetSystemDifference=U moet andere bron- en doelverbinding en containercombinaties selecteren voor replicatiestroom.
#XMSG
validateDuplicateSources=een of meer replicaties hebben dubbele bronobjectnamen: {0}.
#XMSG
validateDuplicateTargets=een of meer replicaties hebben dubbele doelobjectnamen: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=Het bronobject {0} ondersteunt geen deltaopname, maar het doelobject {1} wel. U moet de replicatie verwijderen.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=U moet het laadtype "Initieel en delta" selecteren voor de replicatie met doelobjectnaam {0}.
#XMSG
validationAutoRenameTarget=Er zijn doelkolommen hernoemd.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Er is een automatische projectie toegevoegd en de volgende doelkolommen zijn hernoemd om replicaties naar het doel mogelijk te maken:{1}{1} {0} {1}{1}Dit heeft een van de volgende redenen:{1}{1}{2} Niet-ondersteunde tekens{1}{2} Gereserveerd voorvoegsel
#XMSG
validationAutoRenameTargetDescriptionUpdated=Er is een automatische projectie toegevoegd en de volgende doelkolommen zijn hernoemd om replicatie naar Google BigQuery mogelijk te maken:{1}{1} {0} {1}{1}Dit heeft een van de volgende redenen:{1}{1}{2} Gereserveerde kolomnaam{1}{2} Niet-ondersteunde tekens{1}{2} Gereserveerd voorvoegsel
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Er is een automatische projectie toegevoegd en de volgende doelkolommen zijn hernoemd om replicaties naar Confluent mogelijk te maken:{1}{1} {0} {1}{1}Dit heeft een van de volgende redenen:{1}{1}{2} Gereserveerde kolomnaam{1}{2} Niet-ondersteunde tekens{1}{2} Gereserveerd voorvoegsel
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Er is een automatische projectie toegevoegd en de volgende doelkolommen zijn hernoemd om replicaties naar doel mogelijk te maken:{1}{1} {0} {1}{1}Dit heeft een van de volgende redenen:{1}{1}{2} Gereserveerde kolomnaam{1}{2} Niet-ondersteunde tekens{1}{2} Gereserveerd voorvoegsel
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Doelobject is hernoemd.
#XMSG
autoRenameInfoDesc=Het doelobject is hernoemd omdat het niet-ondersteunde tekens bevatte. Alleen de volgende tekens worden ondersteund:{0}{0}{1}A-Z{0}{1}a-z{0}{1}0-9{0}{1}.(punt){0}{1}_(underscore){0}{1}-(dash)
#XMSG
validationAutoTargetTypeConversion=Er zijn doelgegevenstypen gewijzigd.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Voor de volgende doelkolommen zijn de doelgegevenstypen gewijzigd omdat de brongegevenstypen in Google BigQuery niet worden ondersteund:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Voor de volgende doelkolommen zijn de doelgegevenstypen gewijzigd omdat de brongegevenstypen niet worden ondersteund in de doelverbinding:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Kort doelkolomnamen in.
#XMSG
validationMaxCharLengthGBQTargetDescription=In Google BigQuery kunnen voor kolomnamen maximaal 300 tekens worden gebruikt. Gebruik een projectie om de volgende doelkolomnamen in te korten:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Er kunnen geen primaire keys worden gemaakt.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=In Google BigQuery worden maximaal 16 primaire keys ondersteund, maar het bronobject heeft een groter aantal primaire keys. Geen van de primaire keys wordt gemaakt in het doelobject.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Een of meer bronkolommen bevatten gegevenstypen die niet als primaire keys kunnen worden gedefinieerd in Google BigQuery. Geen van de primaire keys wordt in het doelobject gemaakt.{0}{0}De volgende doelgegevenstypen zijn compatibel met Google BigQuery-gegevenstypen waarvoor primaire key kan worden gedefinieerd:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Definieer een of meer kolommen als primaire sleutel.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=U moet hiervoor een of meer kolommen definiëren als bronschemadialoog van de primaire sleutel.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Definieer een of meer kolommen als primaire sleutel.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=U moet een of meer kolommen definiëren als primaire sleutel die overeenkomt met de primaire sleutelbeperkingen voor uw bronobject. Ga hiervoor naar Schema configureren in de eigenschappen van uw bronobject.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Voer een geldige maximale partitiewaarde in.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Max. partitiewaarde moet ≥ 1 en ≤ 2147483647 zijn
#XMSG
validateHDLFNoPKDatasetError=Definieer een of meer kolommen als primaire sleutel.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Om een object te repliceren, moet u een of meerdere doelkolommen definiëren als primaire sleutel. Gebruik hiervoor een prognose.
#XMSG
validateHDLFNoPKExistingDatasetError=Definieer een of meer kolommen als primaire sleutel.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Om gegevens naar een bestaand doelobject te repliceren, moet het een of meerdere kolommen hebben die gedefinieerd zijn als primaire sleutel. {0} U hebt de volgende opties om een of meerdere kolommen als primaire sleutel te definiëren: {0} {1} Gebruik de lokale tabeleditor om het bestaande doelobject te wijzigen. Laad vervolgens de replicatiestroom opnieuw.{0}{1} Hernoem het doelobject in de replicatiestroom. Hierdoor wordt een nieuw object gecreëerd zodra een run begint. Nadat dit is hernoemd, kunt u een of meerdere kolommen als primaire sleutel definiëren in een prognose.{0}{1} Wijs het object toe aan een ander bestaand doelobject waarin een of meerdere kolommen al als primaire sleutel zijn gedefinieerd.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Geselecteerde doel "{0}" bestaat al in de repository.
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Voor de volgende doelobjecten worden de namen van de deltaopnametabel al gebruikt door andere tabellen in de repository: {0} u moet deze doelobjecten hernoemen om te zorgen dat de geassocieerde namen van deltaopnametabel uniek zijn voordat u de replicatiestroom kunt implementeren.
#XMSG
validateConfluentEmptySchema=Schema definiëren
#XMSG
validateConfluentEmptySchemaDescUpdated=Brontabel heeft geen schema. Kies Schema configureren om er een te definiëren
#XMSG
validationCSVEncoding=Ongeldige CSV-codering
#XMSG
validationCSVEncodingDescription=De CSV-codering van de taak is niet geldig.
#XMSG
validateConfluentEmptySchema=Selecteer een compatibel doelgegevenstype
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Selecteer een compatibel doelgegevenstype
#XMSG
globalValidateTargetDataTypeDesc=Er is een fout opgetreden bij kolomtoewijzing. Ga naar Projectie en zorg ervoor dat alle bronkolommen aan een unieke kolom zijn toegewezen (met een kolom die een compatibel doelgegevenstype heeft) en dat alle gedefinieerde uitdrukkingen geldig zijn.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Kolomnamen dupliceren.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Dubbele kolomnamen worden niet ondersteund. Gebruik het projectiedialoogvenster om dit op te lossen. De volgende doelobjecten hebben dubbele kolomnamen: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Kolomnamen dupliceren.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Dubbele kolomnamen worden niet ondersteund. De volgende doelobjecten hebben dubbele kolomnamen: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Er kunnen inconsistenties in de gegevens zijn.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Het laadtype Alleen delta houdt geen rekening met de wijzigingen die in de bron zijn aangebracht tussen de laatste keer opslaan en de volgende run.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Wijzig het laadtype in "Initieel".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Het repliceren van op ABAP gebaseerde objecten die geen primaire key hebben is alleen mogelijk voor het laadtype "Alleen initieel".
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Deltaopname uitschakelen.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Om een object te repliceren dat geen primaire key heeft en het type source-verbinding ABAP heeft, moet u eerst deltaopname uitschakelen voor deze tabel.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Doelobject wijzigen.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Het doelobject kan niet worden gebruikt omdat deltaopname is ingeschakeld. U kunt het doelobject een andere naam geven en vervolgens deltaopname uitschakelen voor het nieuwe object (met de nieuwe naam) of het bronobject toewijzen aan een doelobject waarvoor deltaopname is uitgeschakeld.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Doelobject wijzigen.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Het doelobject kan niet worden gebruikt omdat het niet de vereiste technische kolom __load_package_id heeft. U kunt het doelobject een andere naam geven die nog niet bestaat. Het systeem maakt dan een nieuw object aan dat dezelfde definitie heeft als het bronobject en de technische kolom bevat. U kunt ook het doelobject toewijzen aan een bestaand object dat de vereiste technische kolom (__load_package_id) bevat.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Doelobject wijzigen.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Het doelobject kan niet worden gebruikt omdat het niet de vereiste technische kolom __load_record_id heeft. U kunt het doelobject een andere naam geven die nog niet bestaat. Het systeem maakt dan een nieuw object aan dat dezelfde definitie heeft als het bronobject en de technische kolom bevat. U kunt ook het doelobject toewijzen aan een bestaand object dat de vereiste technische kolom (__load_record_id) bevat.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Doelobject wijzigen.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Het doelobject kan niet worden gebruikt omdat het gegevenstype van de technische kolom __load_record_id daarvan niet "string(44)" is. U kunt het doelobject een andere naam geven die nog niet bestaat. Het systeem maakt dan een nieuw object aan dat dezelfde definitie heeft als het bronobject en daardoor het juiste gegevenstype heeft. U kunt ook het doelobject toewijzen aan een bestaand object dat de vereiste technische kolom (__load_record_id) met het juiste gegevenstype bevat.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Doelobject wijzigen.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Het doelobject kan niet worden gebruikt omdat het een primaire key heeft, terwijl het bronobject er geen heeft. U kunt het doelobject een andere naam geven die nog niet bestaat. Het systeem maakt dan een nieuw object aan dat dezelfde definitie heeft als het bronobject en daardoor geen primaire key heeft. U kunt ook het doelobject toewijzen aan een bestaand object dat de vereiste technische kolom (__load_package_id) bevat en geen primaire key heeft.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Doelobject wijzigen.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Het doelobject kan niet worden gebruikt omdat het een primaire key heeft, terwijl het bronobject er geen heeft. U kunt het doelobject een andere naam geven die nog niet bestaat. Het systeem maakt dan een nieuw object aan dat dezelfde definitie heeft als het bronobject en daardoor geen primaire key heeft. U kunt ook het doelobject toewijzen aan een bestaand object dat de vereiste technische kolom (__load_record_id) bevat en geen primaire key heeft.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Doelobject wijzigen.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Het doelobject kan niet worden gebruikt omdat het gegevenstype van de technische kolom __load_package_id daarvan niet "binary(>=256)" is. U kunt het doelobject een andere naam geven die nog niet bestaat. Het systeem maakt dan een nieuw object aan dat dezelfde definitie heeft als het bronobject en daardoor het juiste gegevenstype heeft. U kunt ook het doelobject toewijzen aan een bestaand object dat de vereiste technische kolom (__load_package_id) met het juiste gegevenstype bevat.
#XMSG
validationAutoRenameTargetDPID=Er zijn doelkolommen hernoemd.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Verwijder het bronobject.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Het bronobject heeft geen sleutelkolom, wat niet wordt ondersteund in deze context.
#XMSG
validationAutoRenameTargetDPIDDescription=Er is een automatische projectie toegevoegd en de volgende doelkolommen zijn hernoemd om replicatie van een ABAP-bron zonder keys mogelijk te maken:{1}{1} {0} {1}{1}Dit heeft een van de volgende redenen:{1}{1}{2} Gereserveerde kolomnaam{1}{2} Niet-ondersteunde tekens{1}{2} Gereserveerd voorvoegsel
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Replicatie naar {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Opslaan en implementeren van replicatiestromen met {0} als doel is momenteel niet mogelijk omdat wij onderhoud uitvoeren aan deze functie.
#XMSG
TargetColumnSkippedLTF=Doelkolom is overgeslagen.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Doelkolom is overgeslagen door niet-ondersteund gegevenstype. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Tijdkolom met primaire sleutel.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Het bronobject heeft een tijdkolom als primaire sleutel, wat niet wordt ondersteund in deze context.
#XMSG
validateNoPKInLTFTarget=Primaire sleutel ontbreekt.
#XMSG
validateNoPKInLTFTargetDescription=Primaire sleutel is niet gedefinieerd in doel, dit wordt niet ondersteund in deze context.
#XMSG
validateABAPClusterTableLTF=ABAP-clustertabel.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Het bronobject is een ABAP-clustertabel, wat niet wordt ondersteund in deze context.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=U lijkt nog geen gegevens te hebben toegevoegd.
#YINS
welcomeText2=Om uw replicatiestroom te starten, selecteert u een verbinding en een bronobject op de linkerzijde.

#XBUT
wizStep1=Bronverbinding selecteren
#XBUT
wizStep2=Broncontainer selecteren
#XBUT
wizStep3=Bronobjecten toevoegen

#XMSG
limitDataset=Het maximumaantal objecten is bereikt. Verwijder bestaande objecten om nieuwe toe te voegen, of maak een nieuwe replicatiestroom.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=De replicatiestroom voor deze niet-SAP-doelverbinding kan niet worden gestart omdat er geen uitgaand volume beschikbaar is voor deze maand.
#XMSG
premiumOutBoundRFAdminWarningMsg=Een beheerder kan de blokken premium uitgaand voor deze tenant verhogen om uitgaand volume beschikbaar te maken voor deze maand.
#XMSG
messageForToastForDPIDColumn2=Nieuwe kolom toegevoegd aan doel voor {0} objecten - vereist voor afhandeling van dubbele records in verbinding met bronobjecten op basis van ABAP die geen primaire key hebben.
#XMSG
PremiumInboundWarningMessage=Afhankelijk van het aantal replicatiestromen en te repliceren gegevensvolume, overschrijden de SAP HANA-resources{0} die vereist zijn voor het repliceren van gegevens via {1} mogelijk de beschikbare capaciteit voor uw tenant.
#XMSG
PremiumInboundWarningMsg=Afhankelijk van het aantal replicatiestromen en te repliceren gegevensvolume, overschrijden de SAP HANA-resources{0} die vereist zijn voor het repliceren van gegevens via "{1}" mogelijk de beschikbare capaciteit voor uw tenant.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Voer een geldige projectienaam in.
#XMSG
emptyTargetColumn=Voer een doelkolomnaam.
#XMSG
emptyTargetColumnBusinessName=Voer een Bedrijfsnaam voor de doelkolom in.
#XMSG
invalidTransformName=Voer een geldige projectienaam in.
#XMSG
uniqueColumnName=Hernoem doelkolom.
#XMSG
copySourceColumnLbl=Kolommen uit bronobject kopiëren
#XMSG
renameWarning=Zorg dat u een unieke naam kiest wanneer u de naam van de doeltabel wijzigt. Als er al een tabel met de nieuwe naam bestaat in de ruimte, wordt de definitie van die tabel gebruikt.

#XMSG
uniqueColumnBusinessName=Bedrijfsnaam doelkolom hernoemen
#XMSG
uniqueSourceMapping=Selecteer een andere bronkolom.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=De bronkolom {0} wordt al gebruikt door de volgende doelkolommen:{1}{1}{2}{1}{1} Voor deze doelkolom of voor de andere doelkolommen selecteer u een bronkolom die nog niet in gebruik is om de prognose op te slaan.
#XMSG
uniqueColumnNameDescription=De doelkolomnaam die die u heeft ingevoerd bestaat al. Om de projectie op te slaan, moet u hier een unieke kolomnaam invoeren.
#XMSG
uniqueColumnBusinessNameDesc=De bedrijfsnaam van de doelkolom die u hebt ingevoerd bestaat al. Om de prognose op te slaan, moet u hier een unieke bedrijfsnaam voor de kolom invoeren.
#XMSG
emptySource=Selecteer een bronkolom of voer een constante waarde in.
#XMSG
emptySourceDescription=Om een geldig toewijzingsgegeven te maken, moet u een bronkolom selecteren of een constante waarde invoeren.
#XMSG
emptyExpression=Toewijzing definiëren.
#XMSG
emptyExpressionDescription1=Selecteer een bronkolom waaraan u een doelkolom wilt toewijzen of selecteer het aankruisvakje in de kolom {0} Functies/Constanten{1}. {2} {2} Functies worden automatisch ingevoerd volgens het doelgegevenstype. Constante waarden kunnen handmatig worden ingevoerd.
#XMSG
numberExpressionErr=Voer getal in
#XMSG
numberExpressionErrDescription=U heeft een numeriek gegevenstype geselecteerd. Dit betekent dat u hier alleen cijfers kunt invoeren, plus de decimalen (indien van toepassing). Gebruik geen enkele aanhalingstekens.
#XMSG
invalidLength=Voer een geldige lengtewaarde in.
#XMSG
invalidLengthDescription=De lengte van het gegevenstype moet gelijk zijn aan d elengte van de bronkolom en kan tussen 1 en 5000 zijn.
#XMSG
invalidMappedLength=Voer een geldige lengtewaarde in.
#XMSG
invalidMappedLengthDescription=De lengte van het gegevenstype moet gelijk zijn aan de lengte van de bronkolom {0} en kan tussen 1 en 5000 zijn.
#XMSG
invalidPrecision=Voer een geldige precisiewaarde in.
#XMSG
invalidPrecisionDescription=Precisie definieert het totaalaantal cijfers. Schaal definieert het aantal cijfers na het decimale punt en kan tussen 0 en precisie liggen.{0}{0} Voorbeelden: {0}{1} precisie 6, schaal 2 komt overeen met cijfers zoals 1234,56.{0}{1} Precisie 6, schaal 6 komt overeen met cijfers zoals 0,123546.{0} {0} Precisie en schaal voor het doel moeten compatibel zijn met precisie en schaal voor de bron, zodat alle cijfers uit de bron passen in het doelveld. Voorbeeld: als u precisie 6 en schaal 2 heeft in de bron (en cijfers behalve 0 voor het decimale punt) kunt u niet precisie 6 en schaal 6 in het doel hebben.
#XMSG
invalidPrimaryKey=Voer minstens één primaire sleutel in.
#XMSG
invalidPrimaryKeyDescription=Primaire sleutel niet gedefinieerd voor dit schema.
#XMSG
invalidMappedPrecision=Voer een geldige precisiewaarde in.
#XMSG
invalidMappedPrecisionDescription1=Precisie definieert het totaalaantal cijfers. Schaal definieert het aantal cijfers na het decimale punt en kan tussen 0 en precisie liggen.{0}{0} Voorbeelden:{0}{1} precisie 6, schaal 2 komt overeen met cijfers zoals 1234,56.{0}{1} Precisie 6, schaal 6 komt overeen met cijfers zoals 0,123546.{0}{0}De precisie van het gegevenstype moet gelijk zijn aan of groter zijn dan de precisie van de bron ({2}).
#XMSG
invalidScale=Voer een geldige staffelwaarde in.
#XMSG
invalidScaleDescription=Precisie definieert het totaalaantal cijfers. Schaal definieert het aantal cijfers na het decimale punt en kan tussen 0 en precisie liggen.{0}{0} Voorbeelden: {0}{1} precisie 6, schaal 2 komt overeen met cijfers zoals 1234,56.{0}{1} Precisie 6, schaal 6 komt overeen met cijfers zoals 0,123546.{0} {0} Precisie en schaal voor het doel moeten compatibel zijn met precisie en schaal voor de bron, zodat alle cijfers uit de bron passen in het doelveld. Voorbeeld: als u precisie 6 en schaal 2 heeft in de bron (en cijfers behalve 0 voor het decimale punt) kunt u niet precisie 6 en schaal 6 in het doel hebben.
#XMSG
invalidMappedScale=Voer een geldige staffelwaarde in.
#XMSG
invalidMappedScaleDescription1=Precisie definieert het totaalaantal cijfers. Schaal definieert het aantal cijfers na het decimale punt en kan tussen 0 en precisie liggen.{0}{0} Voorbeelden: {0}{1}precisie 6, schaal 2 komt overeen met cijfers zoals 1234,56.{0}{1} Precisie 6, schaal 6 komt overeen met cijfers zoals 0,123546.{0}{0} De schaal van het gegevenstype moet gelijk zijn aan of groter zijn dan de schaal van de bron ({2}).
#XMSG
nonCompatibleDataType=Selecteer een compatibel doelgegevenstype.
#XMSG
nonCompatibleDataTypeDescription1=Het gegevenstype dat u hier opgeeft moet compatibel zijn met het brongegevenstype ({0}). {1}{1} Voorbeeld: als uw bronkolom gegevenstype string heeft en letters bevat, kunt u geen decimaal gegevenstype voor uw doel gebruiken.
#XMSG
invalidColumnCount=Selecteer ten minste één bronkolom.
#XMSG
ObjectStoreInvalidScaleORPrecision=Voer een geldige waarde in voor precisie en schaal.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=De eerste waarde is de precisie die het totale aantal cijfers definieert. De tweede waarde is de schaal die de cijfers na het decimaalteken definieert. Voer de doelschaalwaarde in die groter is dan de bronschaalwaarde en zorg dat het verschil tussen de ingevoerde doelschaal en precisiewaarde groter is dan het verschil tussen de bronschaal en precisiewaarde.
#XMSG
InvalidPrecisionORScale=Voer een geldige waarde in voor precisie en schaal.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=De eerste waarde is de precisie, die het totaal aantal cijfers definieert. De tweede waarde is de schaal, die de cijfers na de komma definieert.{0}{0}Omdat het brongegevenstype niet wordt ondersteund in Google BigQuery, wordt het geconverteerd naar het doelgegevenstype DECIMAL. In dit geval kan de precisie alleen worden gedefinieerd tussen 38 en 76, en de schaal tussen 9 en38. Bovendien moet het resultaat van precisie minus schaal, dat staat voor het aantal cijfers voor de komma, liggen tussen 29 en 38.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1={0}De eerste waarde is de precisie, die het totaal aantal cijfers definieert. De tweede waarde is de schaal, die de cijfers na de komma definieert.{0}Omdat het brongegevenstype niet wordt ondersteund in Google BigQuery, wordt het geconverteerd naar het doelgegevenstype DECIMAL. In dit geval moet de precisie worden gedefinieerd als 20 of hoger. Daarnaast moet het resultaat van precisie minus schaal, dat staat voor het aantal cijfers voor de komma, 20 of hoger zijn.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=De eerste waarde is de precisie, die het totaal aantal cijfers definieert. De tweede waarde is de schaal, die de cijfers na de komma definieert.{0}{0}Omdat het brongegevenstype niet wordt ondersteund in het doel, wordt het geconverteerd naar het doelgegevenstype DECIMAL. In dit geval moet de precisie worden gedefinieerd meer dan of gelijk aan 1 en minder dan of gelijk aan 38 en de schaal minder dan of gelijk aan de precisie.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=De eerste waarde is de precisie, die het totaal aantal cijfers definieert. De tweede waarde is de schaal, die de cijfers na de komma definieert.{0}{0}Omdat het brongegevenstype niet wordt ondersteund in het doel, wordt het geconverteerd naar het doelgegevenstype DECIMAL. In dit geval moet de precisie worden gedefinieerd als 20 of hoger. Daarnaast moet het resultaat van precisie minus schaal, dat staat voor het aantal cijfers voor de komma, 20 of hoger zijn.
#XMSG
invalidColumnCountDescription=Om een geldig toewijzingsgegeven te maken, moet u een bronkolom selecteren of een constante waarde invoeren.
#XMSG
duplicateColumns=Hernoem doelkolom.
#XMSG
duplicateGBQCDCColumnsDesc=Doelkolomnaam is gereserveerd in Google BigQuery. U moet deze hernoemen om projectie op te kunnen slaan.
#XMSG
duplicateConfluentCDCColumnsDesc=Doelkolomnaam is gereserveerd in Confluent. U moet deze hernoemen om projectie op te kunnen slaan.
#XMSG
duplicateSignavioCDCColumnsDesc=Doelkolomnaam is gereserveerd in SAP Signavio. U moet deze hernoemen om projectie op te kunnen slaan.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Doelkolomnaam is gereserveerd in MS OneLake. U moet deze hernoemen om projectie op te kunnen slaan.
#XMSG
duplicateSFTPCDCColumnsDesc=Doelkolomnaam is gereserveerd in SFTP. Hernoem deze om projectie op te kunnen slaan.
#XMSG
GBQTargetNameWithPrefixUpdated1=De doelkolomnaam bevat een prefix die gereserveerd is in Google BigQuery. U moet deze hernoemen om de projectie te kunnen opslaan. {0}{0}De doelkolomnaam mag niet beginnen met een van de volgende strings:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Kort doelkolomnaam in.
#XMSG
GBQtargetMaxLengthDesc=In Google BigQuery kan een kolomnaam van maximaal 300 tekens worden gebruikt. Kort de doelkolomnaam in om projectie op te slaan.
#XMSG
invalidMappedScalePrecision=Precisie en schaal voor doel moeten compatibel zijn met precisie en schaal voor bron zodat alle cijfers uit bron in het doelveld passen.
#XMSG
invalidMappedScalePrecisionShortText=Voer een geldige precisie- en schaalwaarde in.
#XMSG
validationIncompatiblePKTypeDescProjection3=Een of meer bronkolommen bevatten gegevenstypen die niet als primaire keys kunnen worden gedefinieerd in Google BigQuery. Geen van de primaire keys wordt in het doelobject gemaakt.{0}{0}De volgende doelgegevenstypen zijn compatibel met Google BigQuery-gegevenstypen waarvoor een primaire key kan worden gedefinieerd:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Schakel kolom __message_id uit.
#XMSG
uncheckColumnMessageIdDesc=Kolom: primaire key
#XMSG
validationOpCodeInsert=U moet een waarde invullen voor 'Invoegen'.
#XMSG
recommendDifferentPrimaryKey=Wij raden aan dat u een andere primaire sleutel op positieniveau selecteert.
#XMSG
recommendDifferentPrimaryKeyDesc=Als de operatiecode al is gedefinieerd, wordt er aanbevolen om andere primaire sleutels te selecteren voor de arrayindex en de posities om problemen zoals kolomduplicatie te voorkomen.
#XMSG
selectPrimaryKeyItemLevel=U moet minstens één primaire sleutel selecteren voor zowel het kop- als het positieniveau.
#XMSG
selectPrimaryKeyItemLevelDesc=Als een array of een kaart wordt weergegeven, moet u twee primaire sleutels selecteren, een op kopniveau en een op positieniveau.
#XMSG
invalidMapKey=U moet minstens één primaire sleutel op kopniveau selecteren.
#XMSG
invalidMapKeyDesc=Als een array of een kaart wordt weergegeven, moet u een primaire sleutel op kopniveau selecteren.
#XFLD
txtSearchFields=Doelkolommen zoeken
#XFLD
txtName=Naam
#XMSG
txtSourceColValidation=Een of meerdere bronkolommen worden niet ondersteund:
#XMSG
txtMappingCount=Toewijzingen ({0})
#XMSG
schema=Schema
#XMSG
sourceColumn=Bronkolommen
#XMSG
warningSourceSchema=Wijzigingen in schema hebben invloed op toewijzingen in projectiedialoog.
#XCOL
txtTargetColName=Doelkolom (technische naam)
#XCOL
txtDataType=Doelgegevenstype
#XCOL
txtSourceDataType=Type gegevensbron
#XCOL
srcColName=Bronkolom (technische naam)
#XCOL
precision=Precisie
#XCOL
scale=Schaal
#XCOL
functionsOrConstants=Functies/constante waarden
#XCOL
txtTargetColBusinessName=Doelkolom (bedrijfsnaam)
#XCOL
prKey=Primaire key
#XCOL
txtProperties=Eigenschappen
#XBUT
txtOK=Opslaan
#XBUT
txtCancel=Annuleren
#XBUT
txtRemove=Verwijderen
#XFLD
txtDesc=Omschrijving
#XMSG
rftdMapping=Toewijzing
#XFLD
@lblColumnDataType=Gegevenstype
#XFLD
@lblColumnTechnicalName=Technische naam
#XBUT
txtAutomap=Automatisch toewijzen
#XBUT
txtUp=Omhoog
#XBUT
txtDown=Omlaag

#XTOL
txtTransformationHeader=Projectie
#XTOL
editTransformation=Bewerken
#XTOL
primaryKeyToolip=Sleutel


#XMSG
rftdFilter=Filter
#XMSG
rftdFilterColumnCount=Bron: {0}({1})
#XTOL
rftdFilterColSearch=Zoeken
#XMSG
rftdFilterColNoData=Geen kolommen om weer te geven
#XMSG
rftdFilteredColNoExps=Geen filteruitdrukkingen
#XMSG
rftdFilterSelectedColTxt=Filter toevoegen voor
#XMSG
rftdFilterTxt=Filter beschikbaar voor
#XBUT
rftdFilterSelectedAddColExp=Uitdrukking toevoegen
#YINS
rftdFilterNoSelectedCol=Selecteer een kolom om filter toe te voegen
#XMSG
rftdFilterExp=Filteruitdrukking
#XMSG
rftdFilterNotAllowedColumn=Voor deze kolom wordt het toevoegen van filters niet ondersteund.
#XMSG
rftdFilterNotAllowedHead=Kolom wordt niet ondersteund
#XMSG
rftdFilterNoExp=Geen filter gedefinieerd
#XTOL
rftdfilteredTt=Gefilterd
#XTOL
rftdremoveexpTt=Filteruitdrukking verwijderen
#XTOL
validationMessageTt=Validatiemeldingen
#XTOL
rftdFilterDateInp=Datum selecteren
#XTOL
rftdFilterDateTimeInp=Datum/tijd selecteren
#XTOL
rftdFilterTimeInp=Selecteer een tijd
#XTOL
rftdFilterInp=Waarde invoeren
#XMSG
rftdFilterValidateEmptyMsg={0} filteruitdrukkingen in {1} kolom leeg
#XMSG
rftdFilterValidateInvalidNumericMsg={0} filteruitdrukkingen op {1} kolom bevat ongeldige numerieke waarden
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Filteruitdrukking moet geldige numerieke waarden bevatten
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Als doelobjectschema is gewijzigd, gebruikt u de functie "Toewijzen aan bestaand doelobject" op de hoofdpagina om de wijzigingen aan te passen en het doelobject met de bijhorende bron opnieuw toe te wijzen.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Als de doeltabel al bestaat en de toewijzing een schemawijziging omvat, moet u de doeltabel dienovereenkomstig wijzigen voordat u de replicatiestroom implementeert.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Als bij uw toewijzing een schemawijziging betrokken is, moet u de doeltabel dienovereenkomstig wijzigen voordat u de replicatiestroom implementeert.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=De volgende niet-ondersteunde kolommen zijn overgeslagen voor brondefinitie: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=De volgende niet-ondersteunde kolommen zijn overgeslagen voor doeldefinitie: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=De volgende objecten worden niet ondersteund omdat ze worden weergegeven voor gebruik: {0} {1} {0} {0} Om tabellen in een replicatieflow te gebruiken, mag het semantisch gebruik (in de tabelinstellingen) niet zijn ingesteld op {2}Analytische gegevensset{2}.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Het doelobject kan niet worden gebruikt omdat het wordt weergegeven voor gebruik. {0} {0}  Om de tabel in een replicatieflow te gebruiken, mag het semantisch gebruik (in de tabelinstellingen) niet zijn ingesteld op {1}Analytische gegevensset{1}.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Er bestaat al een doelobject met deze naam. Deze kan echter niet worden gebruikt omdat het wordt weergegeven voor gebruik. {0} {0} Om de tabel in een replicatieflow te gebruiken, mag het semantisch gebruik (in de tabelinstellingen) niet zijn ingesteld op {1}Analytische gegevensset{1}.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Een object met deze naam bestaat al in het doel. {0}Het object kan echter niet worden gebruikt als doelobject voor een replicatiestroom naar de lokale repository, omdat dit geen lokale tabel is.
#XMSG:
targetAutoRenameUpdated=Doelkolom is hernoemd.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=De doelkolom is hernoemd om replicaties toe te staan in Google BigQuery. Dit komt door een van de volgende redenen:{0} {1}{2}gereserveerde kolomnaam{3}{2}niet-ondersteunde tekens{3}{2}gereserveerde prefix{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=De doelkolom is hernoemd om replicaties toe te staan in Confluent. Dit komt door een van de volgende redenen:{0} {1}{2}gereserveerde kolomnaam{3}{2}niet-ondersteunde tekens{3}{2}gereserveerde prefix{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=De doelkolom is hernoemd om replicaties toe te staan in het doel. Dit komt door een van de volgende redenen:{0} {1}{2}niet-ondersteunde tekens{3}{2}gereserveerde prefix{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=De doelkolom is hernoemd om replicaties naar het doel toe te staan. Dit komt door een van de volgende redenen:{0} {1}{2}gereserveerde kolomnaam{3}{2}niet-ondersteunde tekens{3}{2}gereserveerde prefix{3}{4}
#XMSG:
targetAutoDataType=Doelgegevenstype is gewijzigd.
#XMSG:
targetAutoDataTypeDesc=Doelgegevenstype is gewijzigd in {0} omdat brongegevenstype niet wordt ondersteund in Google BigQuery.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Doelgegevenstype is gewijzigd in {0} omdat brongegevenstype niet wordt ondersteund in de doelverbinding.
#XMSG
projectionGBQUnableToCreateKey=Er kunnen geen primaire keys worden gemaakt.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=In Google BigQuery worden maximaal 16 primaire keys ondersteund, maar het bronobject heeft een groter aantal primaire keys. Geen van de primaire keys wordt gemaakt in het doelobject.
#XMSG
HDLFNoKeyError=Definieer een of meer kolommen als primaire sleutel.
#XMSG
HDLFNoKeyErrorDescription=Om een object te repliceren, moet u een of meer kolommen definiëren als een primaire sleutel.
#XMSG
HDLFNoKeyErrorExistingTarget=Definieer een of meer kolommen als primaire sleutel.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Om gegevens naar een bestaand doelobject te repliceren, moet het een of meerdere kolommen hebben die gedefinieerd zijn als primaire sleutel. {0} {0} U hebt de volgende opties om een of meerdere kolommen als primaire sleutel te definiëren: {0}{0}{1} Gebruik de lokale tabeleditor om het bestaande doelobject te wijzigen. Laad vervolgens de replicatiestroom opnieuw.{0}{0}{1} Hernoem het doelobject in de replicatiestroom. Hierdoor wordt een nieuw object gecreëerd zodra een run begint. Nadat dit is hernoemd, kunt u een of meerdere kolommen als primaire sleutel definiëren in een prognose.{0}{0}{1} Wijs het object toe aan een ander bestaand doelobject waarin een of meerdere kolommen al als primaire sleutel zijn gedefinieerd.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Primaire sleutel gewijzigd.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=In vergelijking met het bronobject, heeft u andere kolommen als de primaire sleutel gedefinieerd voor het doelobject. Zorg ervoor dat deze kolommen alle rijen uniek identificeren om mogelijke gegevenscorruptie te voorkomen wanneer de gegevens later worden gerepliceerd. {0} {0} In het bronobject worden de volgende kolommen gedefinieerd als de primaire sleutel: {0} {1}
#XMSG
duplicateDPIDColumns=Hernoem doelkolom.
#XMSG
duplicateDPIDDColumnsDesc1=Deze doelkolomnaam is gereserveerd voor een technische kolom. Voer een andere naam in om de prognose op te slaan.
#XMSG:
targetAutoRenameDPID=Doelkolom is hernoemd.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=De doelkolom is hernoemd om replicaties toe te staan vanaf een ABAP-bron zonder keys. Dit komt door een van de volgende redenen:{0} {1}{2}gereserveerde kolomnaam{3}{2}niet-ondersteunde tekens{3}{2}gereserveerde prefix{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle={0} doelinstellingen
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle={0} Broninstellingen
#XBUT
connectionSettingSave=Opslaan
#XBUT
connectionSettingCancel=Annuleren
#XBUT: Button to keep the object level settings
txtKeep=Behouden
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Overschrijven
#XFLD
targetConnectionThreadlimit=Doelthreadlimiet voor initiële gegevensovername (1-100)
#XFLD
connectionThreadLimit=Bronthreadlimiet voor initiële gegevensovername (1-100)
#XFLD
maxConnection=Limiet replicatiethread (1-100)
#XFLD
kafkaNumberOfPartitions=Aantal partities
#XFLD
kafkaReplicationFactor=Replicatiefactor
#XFLD
kafkaMessageEncoder=Berichtencoder
#XFLD
kafkaMessageCompression=Berichtencompressie
#XFLD
fileGroupDeltaFilesBy=Delta groeperen op
#XFLD
fileFormat=Bestandstype
#XFLD
csvEncoding=CSV-codering
#XFLD
abapExitLbl=ABAP-exit
#XFLD
deltaPartition=Objectthreadaantal voor deltagegevensovername (1-10)
#XFLD
clamping_Data=Fout bij gegevensafkapping
#XFLD
fail_On_Incompatible=Fout bij incompatibele gegevens
#XFLD
maxPartitionInput=Maximum aantal partities
#XFLD
max_Partition=Max. aantal partities definiëren
#XFLD
include_SubFolder=Submappen opnemen
#XFLD
fileGlobalPattern=Globaal patroon voor bestandsnaam
#XFLD
fileCompression=Bestandscompressie
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Scheidingsteken bestand
#XFLD
fileIsHeaderIncluded=Bestandskop
#XFLD
fileOrient=Oriënteren
#XFLD
gbqWriteMode=Schrijfmodus
#XFLD
suppressDuplicate=Duplicaten onderdrukken
#XFLD
apacheSpark=Compatibiliteit Apache Spark inschakelen
#XFLD
clampingDatatypeCb=Gegevenstypen decimaal floating point klemmen
#XFLD
overwriteDatasetSetting=Doelinstellingen op objectniveau overschrijven
#XFLD
overwriteSourceDatasetSetting=Broninstellingen op objectniveau overschrijven
#XMSG
kafkaInvalidConnectionSetting=Nummer tussen {0} en {1} invoeren.
#XMSG
MinReplicationThreadErrorMsg=Voer een getal in dat groter is dan {0}.
#XMSG
MaxReplicationThreadErrorMsg=Voer een getal in dat kleiner is dan {0}.
#XMSG
DeltaThreadErrorMsg=Voer een waarde in tussen 1 en 10.
#XMSG
MaxPartitionErrorMsg=Voer een waarde in tussen 1 <= x <= 2147483647. De standaardwaarde is 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Voer een geheel getal in tussen {0} en {1}.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Replicatiefactor van de broker gebruiken
#XFLD
serializationFormat=Serialiseringsindeling
#XFLD
compressionType=Compressietype
#XFLD
schemaRegistry=Schemaregister gebruiken
#XFLD
subjectNameStrat=Subjectnaamstrategie
#XFLD
compatibilityType=Compatibiliteitstype
#XFLD
confluentTopicName=Onderwerpnaam
#XFLD
confluentRecordName=Recordnaam
#XFLD
confluentSubjectNamePreview=Preview subjectnaam
#XMSG
serializationChangeToastMsgUpdated2=Serialiseringsindeling gewijzigd in JSON omdat schemaregister niet is ingeschakeld. Om de serialiseringsindeling weer op AVRO te zetten, moet u eerst het schemaregister inschakelen.
#XBUT
confluentTopicNameInfo=De onderwerpnaam is altijd gebaseerd op de naam van het doelobject. U kunt deze wijzigen door het doelobject te hernoemen.
#XMSG
emptyRecordNameValidationHeaderMsg=Voer een recordnaam in.
#XMSG
emptyPartionHeader=Voer het aantal partities in.
#XMSG
invalidPartitionsHeader=Voer een geldig aantal partities in.
#XMSG
invalidpartitionsDesc=Voer een getal tussen 1 en 200.000 in.
#XMSG
emptyrFactorHeader=Voer een replicatiefactor in.
#XMSG
invalidrFactorHeader=Voer een geldige replicatiefactor in.
#XMSG
invalidrFactorDesc=Voer een getal tussen 1 en 32.767 in.
#XMSG
emptyRecordNameValidationDescMsg=Als de serialiseringsindeling "AVRO" wordt gebruikt, dan worden alleen de volgende tekens ondersteund:{0}{1} A-Z{0}{1} a-z{0}{1} 0-9{0}{1} _(onderstrepingsteken)
#XMSG
validRecordNameValidationHeaderMsg=Voer een geldige recordnaam in.
#XMSG
validRecordNameValidationDescMsgUpdated=Omdat de serialiseringsindeling "AVRO" wordt gebruikt, mag de recordnaam alleen uit alfanumerieke (A-Z, a-z, 0-9) en onderstepingstekens (_) bestaan. De naam moet beginnen met een letter of een onderstrepingsteken.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=“Objectthreadaantal voor deltagegevensovernamen” kan worden ingesteld zodra een of meerdere objecten laadtype "Initieel en delta".
#XMSG
invalidTargetName=Ongeldige kolomnaam
#XMSG
invalidTargetNameDesc=De doelkolomnaam mag alleen uit alfanumerieke (A-Z, a-z, 0-9) en onderstrepingstekens (_) bestaan.
#XFLD
consumeOtherSchema=Andere schemaversies gebruiken
#XFLD
ignoreSchemamissmatch=Niet kloppend schema negeren
#XFLD
confleuntDatatruncation=Fout bij gegevensafkapping
#XFLD
isolationLevel=Isolatieniveau
#XFLD
confluentOffset=Beginpunt
#XFLD
signavioGroupDeltaFilesByText=Geen
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=Nee
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=Nee

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Projecties
#XBUT
txtAdd=Toevoegen
#XBUT
txtEdit=Bewerken
#XMSG
transformationText=Voeg een projectie toe om filter of toewijzing in te stellen.
#XMSG
primaryKeyRequiredText=Selecteer een primaire sleutel met Schema configureren.
#XFLD
lblSettings=Instellingen
#XFLD
lblTargetSetting={0}: doelinstellingen
#XMSG
@csvRF=Selecteer het bestand dat de schemadefinitie bevat die u wilt toepassen op alle bestanden in de map.
#XFLD
lblSourceColumns=Bronkolommen
#XFLD
lblJsonStructure=JSON-structuur
#XFLD
lblSourceSetting={0}: broninstellingen
#XFLD
lblSourceSchemaSetting={0}: bronschema-instellingen
#XBUT
messageSettings=Instellingen berichten
#XFLD
lblPropertyTitle1=Objecteigenschappen
#XFLD
lblRFPropertyTitle=Replicatiestroomeigenschappen
#XMSG
noDataTxt=Er zijn geen weer te geven kolommen.
#XMSG
noTargetObjectText=Er is geen doelobject geselecteerd.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Doelkolommen
#XMSG
searchColumns=Kolommen zoeken
#XTOL
cdcColumnTooltip=Kolom voor deltaopname
#XMSG
sourceNonDeltaSupportErrorUpdated=Bronobject ondersteunt geen deltaopname.
#XMSG
targetCDCColumnAdded=2 doelkolommen zijn toegevoegd voor deltaopname.
#XMSG
deltaPartitionEnable=Objectthreadlimiet voor deltagegevensovername toegevoegd aan broninstellingen.
#XMSG
attributeMappingRemovalTxt=Ongeldige toewijzingen verwijderen die niet worden ondersteund voor nieuw doelobject.
#XMSG
targetCDCColumnRemoved=2 doelkolommen die worden gebruikt voor deltaopname zijn verwijderd.
#XMSG
replicationLoadTypeChanged=Laadtype gewijzigd in "Initieel en delta".
#XMSG
sourceHDLFLoadTypeError=Wijzig het laadtype in "Initieel en delta".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Om een object van een gegevensbron met verbindingstype SAP HANA Cloud, Data Lake Files naar SAP Datasphere te repliceren, moet u laadtype "initieel en delta" gebruiken.
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Deltaopname inschakelen.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Om een object te repliceren van een bronverbinding met het verbindingstype SAP HANA Cloud, Data Lake Files naar SAP Datasphere, moet u deltaopname inschakelen.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Doelobject wijzigen.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Het doelobject kan niet worden gebruikt, omdat deltaopname is uitgeschakeld. U kunt ofwel het doelobject hernoemen (waardoor u een nieuw object met deltaopname kunt aanmaken) of het toewijzen aan een bestaand object waarvoor deltaopname is ingeschakeld.
#XMSG
deltaPartitionError=Voer een geldig objectthreadaantal in voor deltagegevensovernamen.
#XMSG
deltaPartitionErrorDescription=Voer een waarde in tussen 1 en 10.
#XMSG
deltaPartitionEmptyError=Voer een objectthreadaantal in voor deltagegevensovernamen.
#XFLD
@lblColumnDescription=Omschrijving
#XMSG
@lblColumnDescriptionText1=Voor technische doeleinden - afhandeling van dubbele records veroorzaakt door problemen bij replicatie van bronobjecten op basis van ABAP die geen primaire key hebben.
#XFLD
storageType=Opslag
#XFLD
skipUnmappedColLbl=Niet-toegewezen kolommen overslaan
#XFLD
abapContentTypeLbl=Type inhoud
#XFLD
autoMergeForTargetLbl=Gegevens automatisch samenvoegen
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Algemeen
#XFLD
lblBusinessName=Bedrijfsnaam
#XFLD
lblTechnicalName=Technische naam
#XFLD
lblPackage=Pakket
#XFLD
statusPanel=Runstatus
#XBTN: Schedule dropdown menu
SCHEDULE=Planning
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Planning bewerken
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Planning verwijderen
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Planning maken
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Planningsvalidatiecontrole mislukt
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Een planning kan niet worden gemaakt omdat de replicatiestroom momenteel wordt geïmplementeerd.{0}Wacht totdat de replicatiestroom is geïmplementeerd.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Voor replicatiestromen die objecten bevatten met laadtype "Initieel en delta" kan geen planning worden gemaakt.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Voor replicatiestromen die objecten bevatten met laadtype "Initieel en delta/alleen delta" kan geen planning worden gemaakt.
#XFLD : Scheduled popover
SCHEDULED=Gepland
#XFLD
CREATE_REPLICATION_TEXT=Een replicatiestroom maken
#XFLD
EDIT_REPLICATION_TEXT=Een replicatiestroom bewerken
#XFLD
DELETE_REPLICATION_TEXT=Een replicatiestroom verwijderen
#XFLD
REFRESH_FREQUENCY=Frequentie
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=De replicatiestroom kan niet worden geïmplementeerd omdat de bestaande planning {0} laadtype "Initieel en delta"{0}{0} nog niet ondersteunt. Om de replicatiestroom te implementeren stelt u de laadtypen van alle objecten in {0} op "Alleen initieel". Anders kunt u de planning verwijderen, de {0}replicatiestroom implementeren en een nieuwe run starten. Dit resulteert dan in een run zonder {0}einde, wat ook objecten met het laadtype "Initieel en delta" ondersteunt.
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=De replicatiestroom kan niet worden geïmplementeerd omdat de bestaande planning {0} laadtype "Initieel en delta/alleen delta"{0}{0} nog niet ondersteunt. Om de replicatiestroom te implementeren stelt u de laadtypen van alle objecten in {0} op "Alleen initieel". Anders kunt u de planning verwijderen, de {0}replicatiestroom implementeren en een nieuwe run starten. Dit resulteert dan in een run zonder {0}einde, wat ook objecten met het laadtype "Initieel en delta/alleen delta" ondersteunt.
#XMSG
SCHEDULE_EXCEPTION=Planningsdetails ophalen niet mogelijk
#XFLD: Label for frequency column
everyLabel=Elk(e)
#XFLD: Plural Recurrence text for Hour
hoursLabel=Uur
#XFLD: Plural Recurrence text for Day
daysLabel=Dagen
#XFLD: Plural Recurrence text for Month
monthsLabel=Maanden
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuten
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Informatie ophalen over mogelijkheid tot planning mislukt.
#XFLD :Paused field
PAUSED=Gepauzeerd
#XMSG
navToMonitoring=Openen in stroommonitor
#XFLD
statusLbl=Status
#XFLD
lblLastRunExecuted=Start laatste run
#XFLD
lblLastExecuted=Laatste uitvoering
#XFLD: Status text for Completed
statusCompleted=Voltooid
#XFLD: Status text for Running
statusRunning=Wordt uitgevoerd
#XFLD: Status text for Failed
statusFailed=Mislukt
#XFLD: Status text for Stopped
statusStopped=Gestopt
#XFLD: Status text for Stopping
statusStopping=Wordt gestopt
#XFLD: Status text for Active
statusActive=Actief
#XFLD: Status text for Paused
statusPaused=Gepauzeerd
#XFLD: Status text for not executed
lblNotExecuted=Nog niet uitgevoerd
#XFLD
messagesSettings=Instellingen berichten
#XTOL
@validateModel=Validatiemeldingen
#XTOL
@hierarchy=Hiërarchie
#XTOL
@columnCount=Aantal kolommen
#XMSG
VAL_PACKAGE_CHANGED=U hebt dit object aan pakket {1} toegewezen. Klik op "Opslaan" om deze wijziging te bevestigen en te valideren. Let op: een toewijzing aan een pakket kan in deze editor niet meer ongedaan worden gemaakt nadat u hebt opgeslagen.
#XMSG
MISSING_DEPENDENCY=Afhankelijkheden van object ''{0}" kunnen niet worden opgelost in pakket {1}.
#XFLD
deltaLoadInterval=Interval deltaladen
#XFLD
lblHour=Uren (0-24)
#XFLD
lblMinutes=Minuten (0-59)
#XMSG
maxHourOrMinErr=Voer een waarde in tussen 0 en {0}
#XMSG
maxDeltaInterval=De maximumwaarde van de interval deltaladen is 24 uur.{0}Wijzig de minuutwaarde of de uurwaarde op basis hiervan.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Doelcontainerpad
#XFLD
confluentSubjectName=Naam onderwerp
#XFLD
confluentSchemaVersion=Schemaversie
#XFLD
confluentIncludeTechKeyUpdated=Technische sleutel opnemen
#XFLD
confluentOmitNonExpandedArrays=Niet-weergegeven arrays weglaten
#XFLD
confluentExpandArrayOrMap=Array of kaart weergeven
#XCOL
confluentOperationMapping=Berwerkingstoewijzing
#XCOL
confluentOpCode=Opcode
#XFLD
confluentInsertOpCode=Invoegen
#XFLD
confluentUpdateOpCode=Bijwerken
#XFLD
confluentDeleteOpCode=Verwijderen
#XFLD
expandArrayOrMapNotSelectedTxt=Niet geselecteerd
#XFLD
confluentSwitchTxtYes=Ja
#XFLD
confluentSwitchTxtNo=Nee
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Fout
#XTIT
executeWarning=Waarschuwing
#XMSG
executeunsavederror=Sla uw replicatiestroom op voordat u deze uitvoert.
#XMSG
executemodifiederror=De replicatiestroom omvat wijzigingen die niet zijn opgeslagen. Sla de replicatiestroom op.
#XMSG
executeundeployederror=U moet uw replicatiestroom implementeren voordat u deze kunt uitvoeren.
#XMSG
executedeployingerror=Even geduld totdat de implementatie is voltooid.
#XMSG
msgRunStarted=Uitvoering is gestart
#XMSG
msgExecuteFail=Kon replicatiestroom niet uitvoeren.
#XMSG
titleExecuteBusy=Even geduld a.u.b.
#XMSG
msgExecuteBusy=Uw gegevens worden voorbereid voor de replicatiestroom.
#XTIT
executeConfirmDialog=Waarschuwing
#XMSG
msgExecuteWithValidations=De replicatiestroom heeft validatiefouten. Het uitvoeren van de replicatiestroom kan mislukken.
#XMSG
msgRunDeployedVersion=Er zijn wijzigingen die moeten worden geïmplementeerd. De laatst geïmplementeerde versie van de replicatiestroom wordt uitgevoerd. Wilt u doorgaan?
#XBUT
btnExecuteAnyway=Toch uitvoeren
#XBUT
btnExecuteClose=Sluiten
#XBUT
loaderClose=Sluiten
#XTIT
loaderTitle=Bezig met laden
#XMSG
loaderText=De details worden opgehaald van de server.
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=De replicatiestroom naar deze niet-SAP-doelverbinding kan niet worden gestart
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=omdat er geen uitgaand volume beschikbaar is voor deze maand.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Een beheerder kan de blokken voor premium uitgaand verhogen voor deze
#XMSG
premiumOutBoundRFAdminErrMsgPart2=tenant, waardoor er meer uitgaand volume beschikbaar is voor deze maand.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Fout
#XTIT
deployInfo=Informatie
#XMSG
deployCheckFailException=Uitzondering opgetreden tijdens implementatie
#XMSG
deployGBQFFDisabled=Replicatiestromen met een doelverbinding met Google BigQuery implementeren is momenteel niet mogelijk omdat we onderhoud uitvoeren aan deze functie.
#XMSG
deployKAFKAFFDisabled=Replicatiestromen met een doelverbinding met Apache Kafka implementeren is momenteel niet mogelijk omdat we onderhoud uitvoeren aan deze functie.
#XMSG
deployConfluentDisabled=Replicatiestromen met een doelverbinding met Confluent Kafka implementeren is momenteel niet mogelijk omdat we onderhoud uitvoeren aan deze functie.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Voor de volgende doelobjecten worden de namen van de deltaopnametabel al gebruikt door andere tabellen in de repository: {0} u moet deze doelobjecten hernoemen om te zorgen dat de geassocieerde namen van deltaopnametabel uniek zijn voordat u de replicatiestroom kunt implementeren.
#XMSG
deployDWCSourceFFDisabled=Implementeren van replicatiestromen met SAP Datasphere als bron momenteel niet mogelijk vanwege lopend onderhoud voor deze functie.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Implementeren van replicatiestromen die lokale tabellen met ingeschakelde delta als bronobjecten bevatten, is momenteel niet mogelijk omdat wij onderhoud uitvoeren voor deze functie.
#XMSG
deployHDLFSourceFFDisabled=Implementeren van replicatiestromen met bronverbindingen van het type SAP HANA Cloud, Data Lake Files is momenteel niet mogelijk vanwege lopend onderhoud.
#XMSG
deployObjectStoreAsSourceFFDisabled=Replicatiestromen implementeren die cloudopslagproviders hebben als bron is momenteel niet mogelijk.
#XMSG
deployConfluentSourceFFDisabled=Replicatiestromen implementeren met Confluent Kafka als bron is momenteel niet mogelijk omdat wij onderhoud uitvoeren over deze functie.
#XMSG
deployMaxDWCNewTableCrossed=Voor grote replicatiestromen is het niet mogelijk om deze in een keer "op te slaan en te implementeren". Sla uw replicatiestroom eerst op en implementeer deze daarna.
#XMSG
deployInProgressInfo=Implementatie is al in behandeling.
#XMSG
deploySourceObjectInUse=Bronobjecten {0} worden al gebruikt in replicatiestromen {1}.
#XMSG
deployTargetSourceObjectInUse=Bronobjecten {0} worden al gebruikt in replicatiestromen {1}. Doelobjecten {2} worden al gebruikt in replicatiestromen {3}.
#XMSG
deployReplicationFlowCheckError=Fout bij verifiëren replicatiestroom: {0}
#XMSG
preDeployTargetObjectInUse=Doelobjecten {0} worden al gebruikt in replicatiestromen {1} en u kunt niet hetzelfde doelobject hebben in twee verschillende replicatiestromen. Selecteer een ander doelobject en probeer het opnieuw.
#XMSG
runInProgressInfo=Replicatiestroom wordt al uitgevoerd.
#XMSG
deploySignavioTargetFFDisabled=Implementeren van replicatiestromen met SAP Signavio als doel is momenteel niet mogelijk omdat wij onderhoud uitvoeren aan deze functie.
#XMSG
deployHanaViewAsSourceFFDisabled=Het implementeren van replicatiestromen met views als bronobjecten voor de geselecteerde bronverbinding is momenteel niet mogelijk. Probeer het later opnieuw.
#XMSG
deployMsOneLakeTargetFFDisabled=Implementeren van replicatiestromen met MS OneLake als doel is momenteel niet mogelijk is omdat wij onderhoud uitvoeren aan deze functie.
#XMSG
deploySFTPTargetFFDisabled=Implementeren van replicatiestromen met SFTP als doel is momenteel niet mogelijk is omdat wij onderhoud uitvoeren aan deze functie.
#XMSG
deploySFTPSourceFFDisabled=Implementeren van replicatiestromen met SFTP als bron is momenteel niet mogelijk omdat wij onderhoud uitvoeren aan deze functie.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Technische naam
#XFLD
businessNameInRenameTarget=Bedrijfsnaam
#XTOL
renametargetDialogTitle=Doelobject hernoemen
#XBUT
targetRenameButton=Hernoemen
#XBUT
targetRenameCancel=Annuleren
#XMSG
mandatoryTargetName=Voer een naam in.
#XMSG
dwcSpecialChar=_(onderstrepingsteken) is het enige toegestane speciale teken.
#XMSG
dwcWithDot=De doeltabelnaam kan bestaan uit Latijnse letters, nummers, onderstrepingstekens (_) en punten (.). Het eerste teken moet een letter, nummer of onderstrepingsteken (geen punt) zijn.
#XMSG
nonDwcSpecialChar=Toegestane speciale tekens zijn _(onderstrepingsteken) -(koppelteken) .(punt)
#XMSG
firstUnderscorePattern=Naam mag niet beginnen met _(onderstrepingsteken)

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: statement SQL Create Table weergeven
#XMSG
sqlDialogMaxPKWarning=In Google BigQuery worden maximaal 16 primaire keys ondersteund en het bronobject heeft een groot nummer. Er zijn daarom geen primaire keys gedefinieerd in deze statement.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Een of meerdere bronkolommen hebben gegevenstypen die niet kunnen worden gedefinieerd als primaire keys in Google BigQuery. Er zijn daarom in dit geval geen primaire keys gedefinieerd. In Google BigQuery hebben alleen de volgende gegevenstypen een primaire key: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Kopiëren en sluiten
#XBUT
closeDDL=Sluiten
#XMSG
copiedToClipboard=Gekopieerd naar klembord


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Object ''{0}'' kan geen onderdeel zijn van een taakketen omdat het geen einde heeft (aangezien het objecten omvat met het laadtype Initieel en delta/alleen delta).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Object ''{0}'' kan geen onderdeel zijn van een taakketen omdat het geen einde heeft (aangezien het objecten omvat met het laadtype Initieel en delta).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Object "{0}" kan niet worden toegevoegd aan taakketen.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Er zijn niet-opgeslagen doelobjecten. Sla opnieuw op.{0}{0} Het gedrag van deze functie is gewijzigd: in het verleden werden doelobjecten alleen gecreëerd in de doelomgeving als de replicatiestroom was geïmplementeerd.{0} De objecten worden nu gecreëerd als de replicatiestroom wordt opgeslagen. Uw replicatiestroom is gecreëerd voor deze wijziging en bevat nieuwe objecten.{0} U moet de replicatiestroom opnieuw opslaan voordat u implementeert zodat de nieuwe object correct worden opgenomen.
#XMSG
confirmChangeContentTypeMessage=U staat op het punt om het type inhoud te wijzigen. Als u dit doet, worden alle bestaande prognoses verwijderd.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Naam onderwerp
#XFLD
schemaDialogVersionName=Schemaversie
#XFLD
includeTechKey=Technische sleutel opnemen
#XFLD
segementButtonFlat=Plat
#XFLD
segementButtonNested=Genest
#XMSG
subjectNamePlaceholder=Naam onderwerp zoeken

#XMSG
@EmailNotificationSuccess=Configuratie van runtime e-mailmeldingen is opgeslagen.

#XFLD
@RuntimeEmailNotification=E-mail runtimemelding

#XBTN
@TXT_SAVE=Opslaan


