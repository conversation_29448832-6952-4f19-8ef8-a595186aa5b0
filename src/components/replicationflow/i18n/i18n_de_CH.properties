#~~~ WorkBench ~~~~~~~~~~~~~~~~~~

#XTIT: Save Dialog param
@modelNameRF=Replikationsfluss

#XFLD: Edit Schema button text
editSchema=Schema bearbeiten

#XTIT : Properties heading
configSchema=Schema konfigurieren

#XFLD: save changed button text
applyChanges=Änderungen übernehmen


#~~~ RFBuilder Main Table ~~~~~~~~~~~~~~~~~~

#XFLD
sourceConnectionEmptyText=Quellverbindung auswählen
#XFLD
sourceContainernEmptyText=Container auswählen
#XFLD
targetConnectionEmptyText=Zielverbindung auswählen
#XFLD
targetContainernEmptyText=Container auswählen
#XFLD
sourceSelectObjectText=Quellobjekt auswählen
#XFLD
sourceObjectCount=Quellobjekte ({0})
#XFLD
targetObjectText=Zielobjekte
#XFLD
confluentBrowseContext=Kontext auswählen
#XBUT
@retry=Wiederholen
#XMSG: Tenant upgrade in progress
tenantUpgradeInProgress=Tenant-Upgrade wird ausgeführt.

#XTOL
browseSourceConnection=Quellverbindung durchsuchen
#XTOL
browseTargetConnection=Zielverbindung durchsuchen
#XTOL
browseSourceContainer=Quellcontainer durchsuchen
#XTOL
browseAndAddSourceDataset=Quellobjekte hinzufügen
#XTOL
browseTargetContainer=Zielcontainer durchsuchen
#XTOL
browseTargetSetting=Zieleinstellungen durchsuchen
#XTOL
browseSourceSetting=Quelleinstellungen durchsuchen
#XTOL
sourceDatasetInfo=Info
#XTOL
sourceDatasetRemove=Entfernen
#XTOL
mappingCount=Repräsentiert die Gesamtzahl nicht namensbasierter Zuordnungen/Ausdrücke.
#XTOL
filterCount=Repräsentiert die Gesamtzahl der Filterbedingungen.
#XTOL
loading=Ladevorgang läuft …
#XCOL
deltaCapture=Delta-Erfassung
#XCOL
deltaCaptureTableName=Tabelle für Delta-Erfassung
#XCOL
loadType=Ladetyp
#XCOL
deleteAllBeforeLoading=Alles vor dem Laden löschen
#XCOL
transformationsTab=Projektionen
#XCOL
settingsTab=Einstellungen

#XBUT
renameTargetObjectBtn=Zielobjekt umbenennen
#XBUT
mapToExistingTargetObjectBtn=Vorhandenem Zielobjekt zuordnen
#XBUT
changeContainerPathBtn=Containerpfad ändern
#XBUT
viewSQLDDLUpdated=SQL-Anweisung zum Anlegen der Tabelle anzeigen
#XMSG
sourceObjectNonDeltaSupportErrorMainEditorUpdated=Delta-Erfassung wird vom Quellobjekt nicht unterstützt. Für das ausgewählte Zielobjekt ist die Option Delta-Erfassung jedoch aktiviert.
#XMSG
sourceObjectNonDeltaSupportErrorMsg=Das Zielobjekt kann nicht verwendet werden, da die Delta-Erfassung aktiviert ist,{0}während das Quellobjekt keine Delta-Erfassung unterstützt.{1}Sie können ein anderes Zielobjekt auswählen, das die Delta-Erfassung nicht unterstützt.
#XMSG
sourceObjectNonDeltaSupportErrorMsgForRenamedTarget=Ein Zielobjekt mit diesem Namen ist bereits vorhanden. Es kann jedoch nicht verwendet werden,{0}da die Delta-Erfassung aktiviert ist, während das Quellobjekt keine{0}Delta-Erfassung unterstützt.{1}Sie können entweder den Namen eines vorhandenen Zielobjekts eingeben, das{0}die Delta-Erfassung nicht unterstützt, oder einen Namen eingeben, der noch nicht vorhanden ist.
#XBUT
copySQLDDLUpdated=SQL-Anweisung zum Anlegen der Tabelle kopieren
#XMSG
targetObjExistingNoCDCColumnUpdated=Vorhandene Tabellen in Google BigQuery müssen folgende Spalten für die Erfassung von Datenänderungen (CDC) enthalten:{0}{0}{1} operation_flag{0}{1} is_deleted{0}{1} recordstamp
#XMSG
new_infoForUnsupportedDatasetNoKeys=Die folgenden Quellobjekte werden nicht unterstützt, da sie keinen Primärschlüssel aufweisen oder da sie eine Verbindung verwenden, die die Bedingungen zum Abrufen des Primärschlüssels nicht erfüllt:
#XMSG
new_infoForUnsupportedDatasetNoKeys2=Informationen zu einer möglichen Lösung des Problems finden Sie im SAP-Wissensdatenbankartikel 3531135.
#XLST: load type list values
initial=Nur initial
@emailUpdateError=Fehler beim Aktualisieren der E-Mail-Benachrichtigungsliste

#XLST
initialDelta=Initial und Delta

#XLST
deltaOnly=Nur Delta
#XMSG
confluentDeltaLoadTypeInfo=Für Confluent-Kafka-Quellen wird nur der Ladetyp "Initial und Delta" unterstützt.
#XMSG
confirmRemoveReplicationObject=Möchten Sie die Replikation wirklich löschen?
#XMSG
confirmRemoveReplicationTaskPrompt=Durch diese Aktion werden vorhandene Replikationen gelöscht. Möchten Sie fortfahren?
#XMSG
confirmTargetConnectionChangePrompt=Durch diese Aktion werden die Zielverbindung und der Zielcontainer zurückgesetzt, und alle Zielobjekte werden gelöscht. Möchten Sie fortfahren?
#XMSG
confirmTargetContainerChangePrompt=Durch diese Aktion wird der Zielcontainer zurückgesetzt, und alle vorhandenen Zielobjekte werden gelöscht. Möchten Sie fortfahren?
#XMSG
confirmRemoveTransformObject=Möchten Sie die Projektion {0} wirklich löschen?
#XMSG
ErrorMsgContainerChange=Beim Ändern des Containerpfades ist ein Fehler aufgetreten.
#XMSG
infoForUnsupportedDatasetNoKeys=Die folgenden Quellobjekte werden nicht unterstützt, da sie keinen Primärschlüssel aufweisen:
#XMSG
infoForUnsupportedDatasetView=Die folgenden Quellobjekte vom Typ "Views" werden nicht unterstützt:
#XMSG
infoForUnsupportedSourceAsSQLViewWithParams=Das folgende Quellobjekt wird nicht unterstützt, da es sich hierbei um einen SQL-View mit Eingabeparametern handelt:
#XMSG
infoForUnsupportedDatasetExtractionDisabled=Die folgenden Quellobjekte werden nicht unterstützt, da die Extraktion für diese deaktiviert ist:
#XMSG
infoForUnsupportedDatasetSchemaTypeUpdated=Bei Confluent-Verbindungen sind die einzigen zulässigen Serialisierungsformate AVRO und JSON. Die folgenden Objekte werden nicht unterstützt, da sie ein anderes Serialisierungsformat verwenden:
#XMSG
infoForUnsupportedDatasetSchemaNotFound=Das Schema konnte nicht für die folgenden Objekte abgerufen werden. Wählen Sie den entsprechenden Kontext aus oder verifizieren Sie die Konfiguration der Schema-Registry.
#XTOL: warning dialog header on deleting replication task
deleteHeader=Löschen
#XBUT
disabledDeleteAllBeforeLoadingForGBQInfo=Die Einstellung "Alles vor dem Laden löschen" wird für Google BigQuery nicht unterstützt.
#XBUT
DeleteAllBeforeLoadingConfluentInfo=Mit der Einstellung "Alles vor dem Laden löschen" wird das Objekt (Thema) vor jeder Replikation gelöscht und neu angelegt. Dabei werden auch alle zugeordneten Nachrichten gelöscht.
#XTOL
DeleteAllBeforeLoadingLTFInfo=Die Einstellung "Alles vor dem Laden löschen" wird für diesen Zieltyp nicht unterstützt.
#~~~ Connection Browser ~~~~~~~~~~~~~~~~~~

#XCOL
connTechnicalName=Technischer Name
#XCOL
connBusinessName=Betriebswirtschaftlicher Name
#XCOL
connDescriptionName=Beschreibung
#XCOL
connType=Typ
#XMSG
connTblNoDataFoundtxt=Keine Verbindungen gefunden
#XMSG
connectionError=Beim Abrufen der Verbindungen ist ein Fehler aufgetreten.
#XMSG
connectionCombinationUnsupportedErrorTitle=Verbindungskombination wird nicht unterstützt
#XMSG
connectionCombinationUnsupportedErrorMsgTxt=Replikation von {0} zu {1} wird derzeit nicht unterstützt.
#XMSG
invalidTargetforSourceHDLFErrorTitle=Verbindungstypkombination wird nicht unterstützt
#XMSG Error message when invalid target is chosen after selecting HDLF as source
invalidTargetforSourceHDLFError=Die Replikation aus einer Verbindung mit dem Verbindungstyp "SAP HANA Cloud, Data-Lake-Dateien" in {0} wird nicht unterstützt. Die Replikation ist nur in SAP Datasphere möglich.

#~~~ Container Browser ~~~~~~~~~~~~~~~~~~

#XBUT
containerSelectBtn=Auswählen
#XBUT
containerCancelBtn=Abbrechen
#XTOL
containerSelectTooltip=Auswählen
#XTOL
containerCancelTooltip=Abbrechen
#XMSG
containerContainerPathPlcHold=Containerpfad
#XFLD
containerContainertxt=Container
#XFLD
confluentContainerContainertxt=Kontext
#XMSG
infoMessageForSLTSelection=Als Container ist nur /SLT/Massenübernahme-ID zulässig. Wählen Sie eine Massenübernahme-ID unter SLT (sofern verfügbar), und klicken Sie auf "Absenden".
#XMSG
msgFetchContainerFail=Beim Abrufen der Container-Daten ist ein Fehler aufgetreten.
#XMSG
infoMessageForSLTHidden=Diese Verbindung unterstützt keine SLT-Ordner. Daher werden diese in der folgenden Liste nicht angezeigt.

#XMSG
infoForHavingSubFoldersForObjectstoreContainer=Wählen Sie einen Container aus, der Unterordner enthält.
#XMSG
sftpIncludeSubFolderText=Falsch
#XMSG
sftpIncludeSubFolderTextNew=Nein

#~~~ Start of Dataset Browser ~~~~~~~~~~~~~~~~~~

#XMSG
@noFilterMappingYet=(Noch keine Filterzuordnung)
#XMSG
failToFetchRemoteMetadata=Beim Abrufen der Metadaten ist ein Fehler aufgetreten.
#XMSG
failToFetchData=Beim Abrufen des vorhandenen Ziels ist ein Fehler aufgetreten.
#XCOL
@loadType=Ladetyp
#XCOL
@deleteAllBeforeLoading=Alles vor dem Laden löschen

#XMSG
@loading=Ladevorgang läuft …
#XFLD
@selectSourceObjects=Quellobjekt auswählen
#XMSG
@exceedLimit=Sie können nicht mehr als {0} Objekte auf einmal importieren. Heben Sie die Auswahl von mindestens {1} Objekten auf.
#XFLD
@objects=Objekte
#XBUT
@ok=OK
#XBUT
@cancel=Abbrechen
#XBUT: remote object browser has 2 steps, search and select, press next and go to selected screen
@next=Weiter
#XBUT
btnAddSelection=Auswahl hinzufügen
#XTOL
@remoteFromSelection=Aus Auswahl entfernen
#XMSG
@searchInForSearchField=In {0} suchen

#XCOL
@name=Technischer Name
#XCOL
@type=Typ
#XCOL
@location=Speicherort
#XCOL
@label=Betriebswirtschaftlicher Name
#XCOL
@status=Status

#XFLD
@searchIn=Suchen in:
#XBUT
@available=Verfügbar
#XBUT
@selection=Auswahl

#XFLD
@noSourceSubFolder=Tabellen und Views
#XMSG
@alreadyAdded=Bereits im Diagramm vorhanden
#XMSG
@askForFilter=Es gibt mehr als {0} Elemente. Geben Sie eine Filterzeichenfolge ein, um die Anzahl der Elemente einzuschränken.
#XFLD: success label
lblSuccess=Erfolgreich
#XFLD: ready label
lblReady=Bereit
#XFLD: failure label
lblFailed=Fehlgeschlagen
#XFLD: fetching status label
lblFetchingDetail=Details werden abgerufen ...

#XMSG Place holder text for tree filter control
filterPlaceHolder=Text zum Filtern von Objekten der obersten Ebene eingeben
#XMSG Place holder text for server search control
serverSearchPlaceholder=Zum Suchen Text eingeben und mit Eingabetaste bestätigen
#XMSG
@deployObjects={0} Objekte werden importiert …
#XMSG
@deployObjectsStatus=Anzahl der importierten Objekte: {0}. Anzahl der Objekte, die nicht importiert werden konnte: {1}.

# @TODO : Verify texts with UA
#XMSG
@openRepositoryBrowserError=Lokaler Repository-Browser konnte nicht geöffnet werden.
#XMSG
@openRemoteSourceBrowserError=Quellobjekte konnten nicht abgerufen werden.
#XMSG
@openRemoteTargetBrowserError=Zielobjekte konnten nicht abgerufen werden.
#XMSG
@validatingTargetsError=Bei der Validierung der Ziele ist ein Fehler aufgetreten.
#XMSG
@waitingToImport=Bereit für Import

#~~~ End of Datset ~~~~~~~~~~~~~~~~~~

#XMSG: Information message telling user that he can’t add more replications
datasetLimitCrossed=Die maximale Anzahl an Objekten wurde überschritten. Wählen Sie maximal 500 Objekte für einen Replikationsfluss aus.

#~~~ Dataset Info Popover ~~~~~~~~~~~~~~~~~~

#XFLD
sourceObjectName=Technischer Name
#XFLD
sourceObjectBusinessName=Betriebswirtschaftlicher Name
#XFLD
sourceNoColumns=Anzahl der Spalten
#XFLD
containerLbl=Container

#~~~ RFBuilder Main Table validation~~~~~~~~~~~~~~~~~~

#XMSG
validationSourceNonExist=Sie müssen eine Quellverbindung für den Replikationsfluss auswählen.
#XMSG
validationSourceContainerNonExist=Sie müssen einen Container für die Quellverbindung auswählen.
#XMSG
validationTargetNonExist=Sie müssen eine Zielverbindung für den Replikationsfluss auswählen.
#XMSG
validationTargetContainerNonExist=Sie müssen einen Container für die Zielverbindung auswählen.
#XMSG
validationTruncateDisabledForObjectTitle=Replikation in Objektspeicher
#XMSG
validationDeleteAllBeforeLoadingDisabledForObjectStoreTarget=Die Replikation in einen Cloud-Speicher ist nur möglich, wenn entweder die Option "Alles vor dem Laden löschen" festgelegt ist oder das Zielobjekt im Ziel nicht vorhanden ist.{0}{0} Um die Replikation für Objekte, für die die Option "Alles vor dem Laden löschen" nicht festgelegt ist, dennoch zu aktivieren, darf das Zielobjekt nicht im System vorhanden sein, wenn Sie den Replikationsfluss ausführen.
#XMSG
validationTaskNonExist=Der Replikationsfluss muss mindestens eine Replikation enthalten.
#XMSG
validationTaskTargetMissing=Für die Replikation mit der Quelle {0} muss ein Ziel vorliegen.
#XMSG
validationTaskTargetIsSAC=Ausgewähltes Ziel ist ein SAC-Artefakt: {0}
#XMSG
validationTaskTargetIsNonDwcTableKey=Ausgewähltes Ziel ist keine unterstützte lokale Tabelle: {0}
#XMSG
validationTaskTargetIsNonDwcTableDescription=Ein Objekt mit diesem Namen ist im Ziel bereits vorhanden. Dieses Objekt kann jedoch nicht als Zielobjekt für einen Replikationsfluss in das lokale Repository verwendet werden, da es sich dabei nicht um eine lokale Tabelle handelt.
#XMSG
validateSourceTargetSystemDifference=Für den Replikationsfluss müssen unterschiedliche Kombinationen aus Quell- und Zielverbindungen und Container vorhanden sein.
#XMSG
validateDuplicateSources=Eine oder mehrere Replikationen weisen doppelte Quellobjektnamen auf: {0}.
#XMSG
validateDuplicateTargets=Eine oder mehrere Replikationen weisen doppelte Zielobjektnamen auf: {0}.
#XMSG
validationTaskSourceObjectTargetObjectDeltaMismatchUpdated=Delta-Erfassung wird nicht vom Quellobjekt {0}, jedoch vom Zielobjekt {1} unterstützt. Sie müssen die Replikation entfernen.
#XMSG
validationTaskTargetObjectLoadTypeMismatch=Sie müssen den Ladetyp "Initial und Delta" für die Replikation mit dem Zielobjektnamen {0} auswählen.
#XMSG
validationAutoRenameTarget=Die Zielspalten wurden umbenannt.
#XMSG: 0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionDWC2=Eine automatische Projektion wurde hinzugefügt, und folgende Zielspalten wurden umbenannt, um die Replikation in das Ziel zu ermöglichen:{1}{1} {0} {1}{1}Dies hat einen der folgenden Gründe:{1}{1}{2} Nicht unterstützte Zeichen{1}{2} Reserviertes Präfix
#XMSG
validationAutoRenameTargetDescriptionUpdated=Eine automatische Projektion wurde hinzugefügt, und folgende Zielspalten wurden umbenannt, um die Replikation in Google BigQuery zu ermöglichen:{1}{1} {0} {1}{1}Dies hat einen der folgenden Gründe:{1}{1}{2} Reservierter Spaltenname{1}{2} Nicht unterstützte Zeichen{1}{2} Reserviertes Präfix
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionConfluent=Eine automatische Projektion wurde hinzugefügt, und folgende Zielspalten wurden umbenannt, um Replikationen in Confluent zu ermöglichen:{1}{1} {0} {1}{1}Dies hat einen der folgenden Gründe:{1}{1}{2} Reservierter Spaltenname{1}{2} Nicht unterstützte Zeichen{1}{2} Reserviertes Präfix
#XMSG:0 - Renamed column names, 1 - new line, 2 - Bullet point
validationAutoRenameTargetDescriptionGeneric=Eine automatische Projektion wurde hinzugefügt, und folgende Zielspalten wurden umbenannt, um Replikationen ins Ziel zu ermöglichen:{1}{1} {0} {1}{1}Dies hat einen der folgenden Gründe:{1}{1}{2} Reservierter Spaltenname{1}{2} Nicht unterstützte Zeichen{1}{2} Reserviertes Präfix
#XMSG
validationAutoRenameTargetSubTitle={0}
#XMSG
autoRenameInfo=Das Zielobjekt wurde umbenannt.
#XMSG
autoRenameInfoDesc=Das Zielobjekt wurde umbenannt, weil es nicht unterstützte Zeichen enthielt. Es werden nur die folgenden Zeichen unterstützt: {0}{0}{1}A bis Z{0}{1}a bis z{0}{1}0 bis 9{0}{1}. (Punkt){0}{1}_ (Unterstrich){0}{1}- (Bindestrich)
#XMSG
validationAutoTargetTypeConversion=Die Zieldatentypen wurden geändert.
#XMSG
validationAutoTargetTypeConversionDescriptionUpdated=Für folgende Zielspalten wurden die Zieldatentypen geändert, weil die Quelldatentypen in Google BigQuery nicht unterstützt werden:{1}{1}{0}
#XMSG: 0 - Converted datatype col names , 1 - new line , 2 - Connection Name ( Can be SAP Datasphere or SAP Datasphere (HDL_FILES))
validationAutoTargetTypeConversionDescriptionDWC3=Für folgende Zielspalten wurden die Zieldatentypen geändert, da die Quelldatentypen nicht in der Zielverbindung unterstützt werden:{1}{1}{0}
#XMSG
validationAutoTargetTypeConversionSubTitle={0}
#XMSG
validationMaxCharLengthGBQTarget=Kürzen Sie die Zielspaltennamen.
#XMSG
validationMaxCharLengthGBQTargetDescription=In Google BigQuery können Spaltennamen maximal 300 Zeichen lang sein. Verwenden Sie eine Projektion, um folgende Zielspaltennamen zu kürzen:{1}{0}
#XMSG
validationMaxCharLengthGBQTargetSubTitle={0}
#XMSG
validationGBQUnableToCreateKey=Die Primärschlüssel werden nicht angelegt.
#XMSG
validationGBQUnableToCreateKeyMaxKeyCombinationDescription=In Google BigQuery werden maximal 16 Primärschlüssel unterstützt, das Quellobjekt enthält jedoch mehr Primärschlüssel. Keiner der Primärschlüssel wird im Zielobjekt angelegt.
#XMSG
validationGBQUnableToCreateKeySubTitle={0}
#XMSG
validationIncompatiblePKTypeDescUpdated4=Mindestens eine Quellspalte weist einen Datentyp auf, der nicht als Primärschlüssel in Google BigQuery definiert werden kann. Keiner der Primärschlüssel wird im Zielobjekt angelegt.{0}{0}Die folgenden Zieldatentypen sind mit den Datentypen von Google BigQuery kompatibel, für die ein Primärschlüssel definiert werden kann:{0}{0}{1} BOOLEAN{0}{1} DATE{0}{1} DECIMAL{0}{1} INT32{0}{1} INT64{0}{1} NUMERIC{0}{1} STRING{0}{1} TIMESTAMP
#XMSG
validateObjectStoreNoPKDatasetError=Legen Sie mindestens eine Spalte als Primärschlüssel fest.
#XMSG
validateObjectStoreNoPKDatasetErrorDescription1=Sie müssen mindestens eine Spalte als Primärschlüssel definieren. Verwenden Sie dazu den Quellschema-Dialog.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetError=Legen Sie mindestens eine Spalte als Primärschlüssel fest.
#XMSG
validateHanaViewAsSourceWithNoPKDatasetErrorDescription=Sie müssen mindestens eine Spalte als Primärschlüssel festlegen, die den Integritätsbedingungen in Bezug auf Primärschlüssel für Ihr Quellobjekt entspricht. Navigieren Sie hierfür in den Eigenschaften Ihres Quellobjekts zu "Schema konfigurieren".
#XMSG
validateHanaViewAsSourceWithNoPKDatasetSubtitle={0}
#XMSG
validateMaxPartitionValueError=Geben Sie einen gültigen Wert für die maximale Anzahl der Partitionen ein.
#XMSG
validateMaxPartitionValueErrorDescriptionUpdated=Der Wert für die maximale Anzahl der Partitionen muss ≥ 1 und ≤ 2147483647 sein.
#XMSG
validateHDLFNoPKDatasetError=Legen Sie mindestens eine Spalte als Primärschlüssel fest.
#XMSG
validateHDLFNoPKDatasetSubtitle={0}
#XMSG
validateHDLFNoPKDatasetErrorDescription1=Um ein Objekt zu replizieren, müssen Sie mindestens eine Zielspalte als Primärschlüssel festlegen. Verwenden Sie dazu eine Projektion.
#XMSG
validateHDLFNoPKExistingDatasetError=Legen Sie mindestens eine Spalte als Primärschlüssel fest.
#XMSG
validateHDLFNoPKExistingDatasetSubtitle={0}
#XMSG {0} {1} being used for formatting
validateHDLFNoPKExistingDatasetErrorDescription1=Um ein Objekt zu replizieren, muss mindestens eine Spalte als Primärschlüssel festgelegt sein. Verwenden Sie dazu eine Projektion. {0} Sie haben folgende Möglichkeiten, eine oder mehrere Spalten als Primärschlüssel festzulegen: {0} {1} Verwenden Sie den lokalen Tabelleneditor, um das vorhandene Zielobjekt zu ändern. Laden Sie dann den Replikationsfluss neu.{0}{1} Benennen Sie das Zielobjekt im Replikationsfluss um, sodass ein neues Objekt angelegt wird, sobald ein Lauf gestartet wird. Nach der Umbenennung können Sie eine oder mehrere Spalten als Primärschlüssel in einer Projektion festlegen.{0}{1} Ordnen Sie das Objekt einem anderen vorhandenen Zielobjekt zu, in dem bereits mindestens eine Spalte als Primärschlüssel festgelegt ist.
#XMSG: Error duplicate target object technical name validation message : {0} technical name of the object
validationDuplicateTechnicalNameKey=Ausgewähltes Ziel ist im Repository bereits vorhanden: {0}
#XMSG: {0} delta capture name i.e. MyTable_Delta
validationDuplicateTechnicalNameDescription=Die Namen der Tabellen für die Delta-Erfassung werden bereits von anderen Tabellen im Repository verwendet: {0}. Bevor Sie den Replikationsfluss sichern können, müssen Sie die Zielobjekte umbenennen, damit die Namen der zugehörigen Tabellen für die Delta-Erfassung eindeutig sind.
#XMSG
validateConfluentEmptySchema=Schema definieren
#XMSG
validateConfluentEmptySchemaDescUpdated=Die Quelltabelle weist kein Schema auf. Wählen Sie "Schema konfigurieren", um eins zu definieren.
#XMSG
validationCSVEncoding=Ungültige CSV-Kodierung
#XMSG
validationCSVEncodingDescription=Die CSV-Kodierung der Aufgabe ist ungültig.
#XMSG
validateConfluentEmptySchema=Wählen Sie einen kompatiblen Zieldatentyp aus.
#XMSG
validateTargetDataTypeSubtitle={0}
#XMSG:
globalValidateTargetDataType=Wählen Sie einen kompatiblen Zieldatentyp aus.
#XMSG
globalValidateTargetDataTypeDesc=Bei der Spaltenzuordnung ist ein Fehler aufgetreten. Wechseln Sie zu Projektion, und stellen Sie sicher, dass alle Quellspalten einer eindeutigen Spalte oder einer Spalte mit einem kompatiblen Datentyp zugeordnet sind und dass alle definierten Ausdrücke gültig sind.
#XMSG: Error for new datasets with duplicate columns
validateDuplicateColumnsForNewTargets=Doppelte Spaltennamen.
#XMSG: Error for existing datasets with duplicate columns
validateDuplicateColumnsForNewTargetsDescription=Doppelte Spaltennamen werden nicht unterstützt. Verwenden Sie den Projektionsdialog, um sie zu korrigieren. Die folgenden Zielobjekte haben doppelte Spaltennamen: {0}.
#XMS: Warning for new datasets with duplicate columns
validateDuplicateColumnsExistingTargets=Doppelte Spaltennamen.
#XMSG: Warn description for existing datasets with duplicate columns
validateDuplicateColumnsExistingTargetsDescription=Doppelte Spaltennamen werden nicht unterstützt. Die folgenden Zielobjekte haben doppelte Spaltennamen: {0}.
#XMSG
deltaOnlyLoadTypeTittle=Es kann zu Inkonsistenzen in den Daten kommen.
#XMSG
deltaOnlyLoadTypeDescriptionUpdated=Beim Ladetyp "Nur Delta" keine Änderungen berücksichtigt, die in der Quelle zwischen dem letzten Sichern und dem nächsten Lauf vorgenommen wurden.
#XMSG
deltaOnlyLoadTypeSubtitle={0}
#~~~ ABAP Source without key Main Table validations~~~~~~~~~~~~~~~~~~
#XMSG
sourceABAPWithoutKeyLoadTypeError=Ändern Sie den Ladetyp in "Initial".
#XMSG
sourceABAPWithoutKeyLoadTypeErrorDescription1=Das Replizieren von ABAP-basierten Objekten ohne Primärschlüssel ist nur mit dem Ladetyp "Nur initial" möglich.
#XMSG
sourceABAPWithoutKeyLoadTypeSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget=Deaktivieren Sie die Delta-Erfassung.
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForNewTargetDescription1=Um ein Objekt ohne Primärschlüssel mit dem Quellverbindungstyp ABAP zu replizieren, müssen Sie zunächst die Delta-Erfassung für diese Tabelle deaktivieren.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitle={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTarget1=Ändern Sie das Zielobjekt.
#XMSG
sourceABAPWithoutKeyDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceABAPWithoutKeyDeltaCaptureErrorForExistingTargetDescription1=Das Zielobjekt kann nicht verwendet werden, da die Delta-Erfassung aktiviert ist. Sie können das Zielobjekt entweder umbenennen und dann die Delta-Erfassung für das neue (umbenannte) Objekt deaktivieren oder das Quellobjekt einem Zielobjekt mit aktivierter Delta-Erfassung zuordnen.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATarget=Ändern Sie das Zielobjekt.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingHANATargetDescription=Das Zielobjekt kann nicht verwendet werden, da es nicht über die erforderliche technische Spalte __load_package_id verfügt. Sie können das Zielobjekt umbenennen, indem Sie einen noch nicht vorhandenen Namen verwenden. Das System legt dann ein neues Objekt an, das die gleiche Definition wie das Quellobjekt hat und die technische Spalte enthält. Alternativ können Sie das Zielobjekt einem vorhandenen Objekt zuordnen, das die erforderliche technische Spalte (__load_package_id) enthält.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTarget1=Ändern Sie das Zielobjekt.
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyNoDPIDColForExistingNonABAPTargetDescription1=Das Zielobjekt kann nicht verwendet werden, da es nicht über die erforderliche technische Spalte __load_record_id verfügt. Sie können das Zielobjekt umbenennen, indem Sie einen noch nicht vorhandenen Namen verwenden. Das System legt dann ein neues Objekt an, das die gleiche Definition wie das Quellobjekt hat und die technische Spalte enthält. Alternativ können Sie das Zielobjekt einem vorhandenen Objekt zuordnen, das die erforderliche technische Spalte (__load_record_id) enthält.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTarget1=Ändern Sie das Zielobjekt.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingNonABAPTargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingNonABAPTargetDescription1=Das Zielobjekt kann nicht verwendet werden, da der Datentyp der zugehörigen technischen Spalte __load_record_id nicht "string(44)" ist. Sie können das Zielobjekt umbenennen, indem Sie einen noch nicht vorhandenen Namen verwenden. Das System legt dann ein neues Objekt an, das die gleiche Definition wie das Quellobjekt hat und somit den korrekten Datentyp aufweist. Alternativ können Sie das Zielobjekt einem vorhandenen Objekt zuordnen, das die erforderliche technische Spalte (__load_record_id) mit dem korrekten Datentyp enthält.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1=Ändern Sie das Zielobjekt.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingHANATarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingHANATargetDescription2=Das Zielobjekt kann nicht verwendet werden, da es über einen Primärschlüssel verfügt, das Quellobjekt jedoch nicht. Sie können das Zielobjekt umbenennen, indem Sie einen noch nicht vorhandenen Namen verwenden. Das System legt dann ein neues Objekt an, das die gleiche Definition wie das Quellobjekt hat und somit keinen Primärschlüssel aufweist. Alternativ können Sie das Zielobjekt einem vorhandenen Objekt zuordnen, das die erforderliche technische Spalte (__load_package_id) enthält, aber keinen Primärschlüssel.
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTarget=Ändern Sie das Zielobjekt.
#XMSG
sourceABAPWithoutKeyPKMismatchSubtitleForExistingNonABAPTarget={0}
#XMSG
sourceABAPWithoutKeyPKMismatchErrorForExistingNonABAPTargetDescription=Das Zielobjekt kann nicht verwendet werden, da es über einen Primärschlüssel verfügt, das Quellobjekt jedoch nicht. Sie können das Zielobjekt umbenennen, indem Sie einen noch nicht vorhandenen Namen verwenden. Das System legt dann ein neues Objekt an, dass die gleiche Definition wie das Quellobjekt hat und somit keinen Primärschlüssel aufweist. Alternativ können Sie das Zielobjekt einem vorhandenen Objekt zuordnen, das die erforderliche technische Spalte (__load_record_id) enthält, aber keinen Primärschlüssel.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1=Ändern Sie das Zielobjekt.
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeForExistingHANATargetSubtitle={0}
#XMSG
sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATargetDescription2=Das Zielobjekt kann nicht verwendet werden, da der Datentyp der zugehörigen technischen Spalte __load_package_id nicht "binary(>=256)" ist. Sie können das Zielobjekt umbenenne, indem Sie einen noch nicht vorhandenen Namen verwenden. Das System legt dann ein neues Objekt an, das die gleiche Definition wie das Quellobjekt hat und somit den korrekten Datentyp aufweist. Alternativ können Sie das Zielobjekt einem vorhandenen Objekt zuordnen, das die erforderliche technische Spalte (__load_package_id) mit dem korrekten Datentyp enthält.
#XMSG
validationAutoRenameTargetDPID=Die Zielspalten wurden umbenannt.
#XMSG
validationAutoRenameTargetDPIDSubTitle={0}
#XMSG
sourceABAPWithoutKeyWithLTF=Quellobjekt entfernen.
#XMSG
sourceABAPWithoutKeyWithLTFSubtitle={0}
#XMSG
sourceABAPWithoutKeyWithLTFDescription=Das Quellobjekt weist keine Schlüsselspalte auf, was in diesem Kontext nicht unterstützt wird.
#XMSG
validationAutoRenameTargetDPIDDescription=Eine automatische Projektion wurde hinzugefügt, und folgende Zielspalten wurden umbenannt, um die Replikation aus der ABAP-Quelle ohne Schlüssel zu ermöglichen:{1}{1} {0} {1}{1}Dies hat einen der folgenden Gründe:{1}{1}{2} Reservierter Spaltenname{1}{2} Nicht unterstützte Zeichen{1}{2} Reserviertes Präfix
#XMSG generic title for any target system not supported when FF off
targetXSystemFFDisabledTitle=Replikation nach {0}.
#XMSG generic descripition for any target system not supported when FF off
targetXSystemFFDisabledDesciption=Das Sichern und Aktivieren von Replikationsflüssen, deren Ziel {0} ist, ist derzeit nicht möglich, da eine Wartung für diese Funktion durchgeführt wird.
#XMSG
TargetColumnSkippedLTF=Zielspalte wurde übersprungen.
#XMSG
TargetColumnSkippedLTFSubtitle={0}
#XMSG
TargetColumnSkippedLTFDescription=Zielspalte wurde aufgrund einen nicht unterstützten Datentyps übersprungen. {0}{1}
#XMSG
validatePKTimeColumnLTF1=Zeitspalte als Primärschlüssel.
#XMSG
validatePKTimeColumnLTFSubtitle={0}
#XMSG
validatePKTimeColumnLTFDescription=Das Quellobjekt weist eine Zeitspalte als Primärschlüssel auf, was in diesem Kontext nicht unterstützt wird.
#XMSG
validateNoPKInLTFTarget=Primärschlüssel fehlt.
#XMSG
validateNoPKInLTFTargetDescription=Primärschlüssel ist im Ziel nicht definiert, was in diesem Kontext nicht unterstützt wird.
#XMSG
validateABAPClusterTableLTF=ABAP-Cluster-Tabelle.
#XMSG
validateABAPClusterTableLTFSubtitle={0}
#XMSG
validateABAPClusterTableLTFDescription=Das Quellobjekt ist eine ABAP-Cluster-Tabelle, die in diesem Kontext nicht unterstützt wird.
#~~~ RFBuilder Welcome Panel ~~~~~~~~~~~~~~~~~~

#YINS
welcomeText1=Sie haben offenbar noch keine Daten hinzugefügt.
#YINS
welcomeText2=Um Ihren Replikationsfluss zu starten, wählen Sie eine Verbindung und ein Quellobjekt auf der linken Seite aus.

#XBUT
wizStep1=Quellverbindung auswählen
#XBUT
wizStep2=Quellcontainer auswählen
#XBUT
wizStep3=Quellobjekte hinzufügen

#XMSG
limitDataset=Die maximale Anzahl an Objekten wurde erreicht. Entfernen Sie vorhandene Objekte, um neue hinzufügen zu können, oder legen Sie einen neuen Replikationsfluss an.
#XMSG
premiumOutBoundRFCannotStartWarningMsg=Der Replikationsfluss für diese SAP-fremde Zielverbindung kann nicht gestartet werden, da für diesen Monat kein Ausgangsvolumen verfügbar ist.
#XMSG
premiumOutBoundRFAdminWarningMsg=Ein Administrator kann die Premiumausgangsblöcke für diesen Tenant erhöhen, wodurch Ausgangsvolumen für diesen Monat verfügbar wird.
#XMSG
messageForToastForDPIDColumn2=Dem Ziel wurde eine neue Spalte für {0} Objekte hinzugefügt. Dies ist erforderlich für die Behandlung doppelter Datensätze in Verbindung mit ABAP-basierten Quellobjekten, die keinen Primärschlüssel enthalten.
#XMSG
PremiumInboundWarningMessage=Je nach Anzahl der Replikationsflüsse und des zu replizierenden Datenvolumens können die für die{0}Datenreplikation über {1} benötigten SAP-HANA-Ressourcen die verfügbaren Kapazitäten Ihres Tenants übersteigen.
#XMSG
PremiumInboundWarningMsg=Je nach Anzahl der Replikationsflüsse und des zu replizierenden Datenvolumens{0}können die für die Datenreplikation über "{1}" benötigten SAP-HANA-Ressourcen die verfügbaren Kapazitäten Ihres Tenants übersteigen.
#~~~ Transformation Dialog ~~~~~~~~~~~~~~~~~~


#XMSG: validation messages
emptyTransformationName=Geben Sie einen Projektionsnamen ein.
#XMSG
emptyTargetColumn=Geben Sie einen Zielspaltennamen ein.
#XMSG
emptyTargetColumnBusinessName=Geben Sie einen betriebswirtschaftlichen Namen für die Zielspalte ein.
#XMSG
invalidTransformName=Geben Sie einen Projektionsnamen ein.
#XMSG
uniqueColumnName=Benennen Sie die Zielspalte um.
#XMSG
copySourceColumnLbl=Spalten aus Quellobjekt kopieren
#XMSG
renameWarning=Stellen Sie sicher, dass Sie beim Umbenennen der Zieltabelle einen eindeutigen Namen wählen. Wenn die Tabelle mit dem neuen Namen in dem Space bereits vorhanden ist, wird die Definition dieser Tabelle verwendet.

#XMSG
uniqueColumnBusinessName=Betriebswirtschaftlichen Namen der Zielspalte umbenennen
#XMSG
uniqueSourceMapping=Wählen Sie eine andere Quellspalte aus.
#XMSG: 0=Source column name, 1=new line character, 2=Target column name
uniqueSourceMappingDesc=Die Quellspalte {0} wird bereits von den folgenden Zielspalten verwendet:{1}{1}{2}{1}{1} Wählen Sie für diese Zielspalte oder für die anderen Zielspalten eine noch nicht verwendete Quellspalte aus, um die Projektion zu sichern.
#XMSG
uniqueColumnNameDescription=Der von Ihnen eingegebene Zielspaltenname ist bereits vorhanden. Um die Projektion sichern zu können, müssen Sie einen eindeutigen Spaltennamen eingeben.
#XMSG
uniqueColumnBusinessNameDesc=Der betriebswirtschaftliche Name der Zielspalte ist bereits vorhanden. Um die Projektion zu sichern, müssen Sie einen eindeutigen betriebswirtschaftlichen Namen für die Spalte eingeben.
#XMSG
emptySource=Wählen Sie eine Quellspalte aus oder geben Sie eine Konstante ein.
#XMSG
emptySourceDescription=Um einen gültigen Zuordnungseintrag anzulegen, müssen Sie eine Quellspalte auswählen oder einen Konstantenwert eingeben.
#XMSG
emptyExpression=Definieren Sie die Zuordnung.
#XMSG
emptyExpressionDescription1=Wählen Sie entweder die Quellspalte aus, die Sie der Zielspalte zuordnen möchten, oder aktivieren Sie das Kontrollkästchen in der Spalte {0} Funktionen/Konstanten {1}. {2} {2} Die Funktionen werden entsprechend ihrem Zieldatentyp automatisch eingegeben. Die Konstantenwerte können manuell eingegeben werden.
#XMSG
numberExpressionErr=Geben Sie eine Zahl ein.
#XMSG
numberExpressionErrDescription=Sie haben einen numerischen Datentyp ausgewählt, d. h. Sie können nur Zahlen und bei Bedarf ein Dezimalzeichen eingeben. Verwenden Sie keine einfachen Anführungszeichen.
#XMSG
invalidLength=Geben Sie einen gültigen Längenwert ein.
#XMSG
invalidLengthDescription=Die Länge des Datentyps muss größer oder gleich der Länge der Quellspalte sein und kann zwischen 1 und 5000 liegen.
#XMSG
invalidMappedLength=Geben Sie einen gültigen Längenwert ein.
#XMSG
invalidMappedLengthDescription=Die Länge des Datentyps muss größer oder gleich der Länge der Quellspalte {0} sein und kann zwischen 1 und 5000 liegen.
#XMSG
invalidPrecision=Geben Sie einen gültigen Präzisionswert ein.
#XMSG
invalidPrecisionDescription=Die Präzision definiert die Gesamtanzahl der Ziffern. Die Skalierung definiert die Anzahl der Nachkommastellen und kann zwischen 0 und der Präzision liegen.{0}{0} Beispiel: {0}{1} Eine Präzision von 6 und Skalierung von 2 entspricht Zahlen wie 1234,56.{0}{1} Eine Präzision von 6 und Skalierung von 6 entspricht Zahlen wie 0,123456.{0} {0} Die Präzision und die Skalierung des Ziels müssen mit der Präzision und der Skalierung der Quelle kompatibel sein, damit alle Ziffern aus der Quelle in das Zielfeld passen. Beispiel: Bei einer Präzision von 6 und Skalierung von 2 in der Quelle (d. h. Ziffern größer 0 vor dem Dezimalzeichen) ist eine Präzision und Skalierung von 6 im Ziel nicht möglich.
#XMSG
invalidPrimaryKey=Geben Sie mindestens einen Primärschlüssel ein.
#XMSG
invalidPrimaryKeyDescription=Primärschlüssel nicht für dieses Schema definiert.
#XMSG
invalidMappedPrecision=Geben Sie einen gültigen Präzisionswert ein.
#XMSG
invalidMappedPrecisionDescription1=Die Präzision definiert die Gesamtanzahl der Ziffern. Die Skalierung definiert die Anzahl der Nachkommastellen und kann zwischen 0 und der Präzision liegen.{0}{0} Beispiel:{0}{1} Eine Präzision von 6 und Skalierung von 2 entspricht Zahlen wie 1234,56.{0}{1} Eine Präzision von 6 und Skalierung von 6 entspricht Zahlen wie 0,123456.{0}{0}Die Präzision des Datentyps muss größer oder gleich der Präzision der Quelle ({2}) sein.
#XMSG
invalidScale=Geben Sie einen gültigen Skalierungswert ein.
#XMSG
invalidScaleDescription=Die Präzision definiert die Gesamtanzahl der Ziffern. Die Skalierung definiert die Anzahl der Nachkommastellen und kann zwischen 0 und der Präzision liegen.{0}{0} Beispiel: {0}{1} Eine Präzision von 6 und Skalierung von 2 entspricht Zahlen wie 1234,56.{0}{1} Eine Präzision von 6 und Skalierung von 6 entspricht Zahlen wie 0,123456.{0} {0} Die Präzision und die Skalierung des Ziels müssen mit der Präzision und der Skalierung der Quelle kompatibel sein, damit alle Ziffern aus der Quelle in das Zielfeld passen. Beispiel: Bei einer Präzision von 6 und Skalierung von 2 in der Quelle (d. h. Ziffern größer 0 vor dem Dezimalzeichen) ist eine Präzision und Skalierung von 6 im Ziel nicht möglich.
#XMSG
invalidMappedScale=Geben Sie einen gültigen Skalierungswert ein.
#XMSG
invalidMappedScaleDescription1=Die Präzision definiert die Gesamtanzahl der Ziffern. Die Skalierung definiert die Anzahl der Nachkommastellen und kann zwischen 0 und der Präzision liegen.{0}{0} Beispiel:{0}{1} Eine Präzision von 6 und Skalierung von 2 entspricht Zahlen wie 1234,56.{0}{1} Eine Präzision von 6 und Skalierung von 6 entspricht Zahlen wie 0,123456.{0}{0}Die Skalierung des Datentyps muss größer oder gleich der Präzision der Quelle ({2}) sein.
#XMSG
nonCompatibleDataType=Wählen Sie einen kompatiblen Zieldatentyp aus.
#XMSG
nonCompatibleDataTypeDescription1=Der hier angegebene Datentyp muss mit dem Datentyp der Quelle ({0}) kompatibel sein. {1}{1} Beispiel: Wenn die Quellspalte den Datentyp STRING aufweist und Buchstaben enthält, können Sie für Ihr Ziel nicht den Datentyp DECIMAL verwenden.
#XMSG
invalidColumnCount=Wählen Sie eine Quellspalte aus.
#XMSG
ObjectStoreInvalidScaleORPrecision=Geben Sie einen gültigen Wert für Präzision und Skalierung ein.
#XMSG
ObjectStoreInvalidScaleORPrecisionUpdated2=Der erste Wert ist die Präzision, durch die die Gesamtzahl an Ziffern definiert wird. Der zweite Wert ist die Skalierung, durch die die Ziffern nach dem Dezimalzeichen definiert werden. Geben Sie einen Zielskalierungswert ein, der größer ist als der Quellskalierungswert und stellen Sie sicher, dass die Differenz zwischen dem eingegebenen Skalierungs- und Präzisionswert des Ziels größer ist als die Differenz zwischen dem Skalierungs- und Präzisionswert der Quelle.
#XMSG
InvalidPrecisionORScale=Geben Sie einen gültigen Wert für Präzision und Skalierung ein.
#XMSG
GBQNonCompatibleDataTypeDecfloatUpdated2=Der erste Wert ist die Präzision, durch die die Gesamtzahl an Ziffern definiert wird. Der zweite Wert ist die Skalierung, durch die die Ziffern nach dem Dezimalzeichen definiert werden.{0}{0}Da der Quelldatentyp nicht in Google BigQuery unterstützt wird, wird er in den Zieldatentyp DECIMAL konvertiert. In diesem Fall kann die Präzision nur zwischen 38 und 76 definiert werden, und die Skalierung zwischen 9 und 38. Darüber hinaus muss das Ergebnis von Präzision minus Skalierung, das die Ziffern vor dem Dezimalzeichen repräsentiert, zwischen 29 und 38 liegen.
#XMSG
GBQNonCompatibleDataTypeUnit64Updated1=Der erste Wert ist die Präzision, durch die die Gesamtzahl an Ziffern definiert wird. Der zweite Wert ist die Skalierung, durch die die Ziffern nach dem Dezimalzeichen definiert werden.{0}{0}Da der Quelldatentyp nicht in Google BigQuery unterstützt wird, wird er in den Zieldatentyp DECIMAL konvertiert. In diesem Fall muss die Präzision als 20 oder größer definiert werden. Zusätzlich muss das Ergebnis von Präzision minus Skalierung, das die Ziffern vor dem Dezimalzeichen repräsentiert, 20 oder größer sein.
#XMSG
DWCLTFInvalidPrecisionOrScaleDescription3=Der erste Wert ist die Präzision, durch die die Gesamtzahl an Ziffern definiert wird. Der zweite Wert ist die Skalierung, durch die die Ziffern nach dem Dezimalzeichen definiert werden.{0}{0}Da der Quelldatentyp nicht im Ziel unterstützt wird, wird er in den Zieldatentyp DECIMAL konvertiert. In diesem Fall muss die Präzision durch eine Zahl, die größer gleich 1 und kleiner gleich 38 ist, definiert werden, und die Skalierung muss kleiner gleich die Präzision sein.
#XMSG
DWCLTFNonCompatibleDataTypeUnit64Description1=Der erste Wert ist die Präzision, durch die die Gesamtzahl an Ziffern definiert wird. Der zweite Wert ist die Skalierung, durch die die Ziffern nach dem Dezimalzeichen definiert werden.{0}{0}Da der Quelldatentyp nicht im Ziel unterstützt wird, wird er in den Zieldatentyp DECIMAL konvertiert. In diesem Fall muss die Präzision mit 20 oder größer definiert werden. Zusätzlich muss das Ergebnis von Präzision minus Skalierung, das die Ziffern vor dem Dezimalzeichen repräsentiert, 20 oder größer sein.
#XMSG
invalidColumnCountDescription=Um einen gültigen Zuordnungseintrag anzulegen, müssen Sie eine Quellspalte auswählen oder einen Konstantenwert eingeben.
#XMSG
duplicateColumns=Benennen Sie die Zielspalte um.
#XMSG
duplicateGBQCDCColumnsDesc=Der Zielspaltenname ist in Google BigQuery reserviert. Sie müssen ihn umbenennen, um die Projektion sichern zu können.
#XMSG
duplicateConfluentCDCColumnsDesc=Der Zielspaltenname ist in Confluent reserviert. Sie müssen ihn umbenennen, um die Projektion sichern zu können.
#XMSG
duplicateSignavioCDCColumnsDesc=Der Zielspaltenname ist in SAP signavio reserviert. Sie müssen ihn umbenennen, um die Projektion sichern zu können.
#XMSG
duplicateMsOneLakeCDCColumnsDesc=Der Zielspaltenname ist in Microsoft OneLake reserviert. Sie müssen ihn umbenennen, um die Projektion sichern zu können.
#XMSG
duplicateSFTPCDCColumnsDesc=Der Zielspaltenname ist in SFTP reserviert. Sie müssen ihn umbenennen, um die Projektion sichern zu können.
#XMSG
GBQTargetNameWithPrefixUpdated1=Der Zielspaltenname enthält ein Präfix, das in Google BigQuey reserviert ist. Sie müssen ihn umbenennen, um die Projektion sichern zu können. {0}{0}Der Zielspaltenname darf nicht mit einer der folgenden Zeichenfolgen beginnen:{0}{0}{1}{2} _TABLE_{3}{2}_FILE_{3}{2}_PARTITION{3}{2} _ROW_TIMESTAMP{3}{2} __ROOT__{3}{2} _COLIDENTIFIER{3}{4}
#XMSG
GBQtargetMaxLength=Kürzen Sie den Zielspaltennamen.
#XMSG
GBQtargetMaxLengthDesc=In Google BigQuery können Spaltennamen maximal 300 Zeichen lang sein. Kürzen Sie den Zielspaltennamen, um die Projektion sichern zu können.
#XMSG
invalidMappedScalePrecision=Die Präzision und die Skalierung des Ziels müssen mit der Präzision und der Skalierung der Quelle kompatibel sein, damit alle Ziffern aus der Quelle in das Zielfeld passen.
#XMSG
invalidMappedScalePrecisionShortText=Geben Sie einen gültigen Präzisions- und Skalierungswert ein.
#XMSG
validationIncompatiblePKTypeDescProjection3=Mindestens eine Quellspalte weist einen Datentyp auf, der nicht als Primärschlüssel in Google BigQuery definiert werden kann. Keiner der Primärschlüssel wird im Zielobjekt angelegt.{0}{0}Die folgenden Zieldatentypen sind mit den Datentypen von Google BigQuery kompatibel, für die ein Primärschlüssel definiert werden kann:{1}{2} BOOLEAN{3}{2} DATE{3}{2} DECIMAL{3}{2} INT32{3}{2} INT64{3}{2} NUMERIC{3}{2} STRING{3}{2} TIMESTAMP
#XMSG
uncheckColumnMessageId=Entmarkieren Sie die Spalte __message_id.
#XMSG
uncheckColumnMessageIdDesc=Spalte: Primärschlüssel
#XMSG
validationOpCodeInsert=Sie müssen einen Wert für INSERT eingeben.
#XMSG
recommendDifferentPrimaryKey=Wir empfehlen, einen anderen Primärschlüssel auf Elementebene auszuwählen.
#XMSG
recommendDifferentPrimaryKeyDesc=Wenn der Vorgangscode bereits definiert ist, empfehlen wir, andere Primärschlüssel für den Array-Index und die Elemente auszuwählen, um Probleme wie z. B. doppelte Spalten zu vermeiden.
#XMSG
selectPrimaryKeyItemLevel=Sie müssen mindestens einen Primärschlüssel sowohl auf Kopf- als auch auf Elementebene auswählen.
#XMSG
selectPrimaryKeyItemLevelDesc=Sie müssen beim Aufklappen eines Array oder einer Karte zwei Primärschlüssel auswählen, einen auf Kopf- und einen auf Elementebene.
#XMSG
invalidMapKey=Sie müssen mindestens einen Primärschlüssel auf Kopfebene auswählen.
#XMSG
invalidMapKeyDesc=Sie müssen beim Aufklappen eines Array oder einer Karte einen Primärschlüssel auf Kopfebene auswählen.
#XFLD
txtSearchFields=Zielspalten suchen
#XFLD
txtName=Name
#XMSG
txtSourceColValidation=Mindestens eine Quellspalte wird nicht unterstützt:
#XMSG
txtMappingCount=Zuordnungen ({0})
#XMSG
schema=Schema
#XMSG
sourceColumn=Quellspalten
#XMSG
warningSourceSchema=Alle an diesem Schema vorgenommenen Änderungen haben Auswirklungen auf die Zuordnungen im Projektionsdialog.
#XCOL
txtTargetColName=Zielspalte (technischer Name)
#XCOL
txtDataType=Zieldatentyp
#XCOL
txtSourceDataType=Quelldatentyp
#XCOL
srcColName=Quellspalte (technischer Name)
#XCOL
precision=Präzision
#XCOL
scale=Skalierung
#XCOL
functionsOrConstants=Funktionen/Konstanten
#XCOL
txtTargetColBusinessName=Zielspalte (betriebswirtschaftlicher Name)
#XCOL
prKey=Primärschlüssel
#XCOL
txtProperties=Eigenschaften
#XBUT
txtOK=Sichern
#XBUT
txtCancel=Abbrechen
#XBUT
txtRemove=Entfernen
#XFLD
txtDesc=Beschreibung
#XMSG
rftdMapping=Zuordnung
#XFLD
@lblColumnDataType=Datentyp
#XFLD
@lblColumnTechnicalName=Technischer Name
#XBUT
txtAutomap=Automatisch zuordnen
#XBUT
txtUp=Nach oben
#XBUT
txtDown=Nach unten

#XTOL
txtTransformationHeader=Projektion
#XTOL
editTransformation=Bearbeiten
#XTOL
primaryKeyToolip=Schlüssel


#XMSG
rftdFilter=Filtern
#XMSG
rftdFilterColumnCount=Quelle: {0} ({1})
#XTOL
rftdFilterColSearch=Suchen
#XMSG
rftdFilterColNoData=Keine Spalten für die Anzeige
#XMSG
rftdFilteredColNoExps=Keine Filterausdrücke
#XMSG
rftdFilterSelectedColTxt=Filter hinzufügen für
#XMSG
rftdFilterTxt=Filter verfügbar für
#XBUT
rftdFilterSelectedAddColExp=Ausdruck hinzufügen
#YINS
rftdFilterNoSelectedCol=Wählen Sie eine Spalte aus, zu der ein Filter hinzugefügt werden soll.
#XMSG
rftdFilterExp=Filterausdruck
#XMSG
rftdFilterNotAllowedColumn=Das Hinzufügen von Filtern wird für diese Spalte nicht unterstützt.
#XMSG
rftdFilterNotAllowedHead=Keine unterstützte Spalte
#XMSG
rftdFilterNoExp=Kein Filter definiert
#XTOL
rftdfilteredTt=Gefiltert
#XTOL
rftdremoveexpTt=Filterausdruck entfernen
#XTOL
validationMessageTt=Validierungsmeldungen
#XTOL
rftdFilterDateInp=Datum auswählen
#XTOL
rftdFilterDateTimeInp=Datum und Uhrzeit auswählen.
#XTOL
rftdFilterTimeInp=Uhrzeit auswählen
#XTOL
rftdFilterInp=Wert eingeben
#XMSG
rftdFilterValidateEmptyMsg={0} Filterausdrücke für Spalte {1} sind leer
#XMSG
rftdFilterValidateInvalidNumericMsg={0} Filterausdrücke für Spalte {1} enthalten ungültige numerische Werte
#XMSG
rftdFilterValidateAddExpInvalidNumericMsg=Filterausdruck muss gültige numerische Werte enthalten
#XMSG: Information Message telling user how to adapt changes done to dwc target tables (using table editor) which were prev used in RF builder
messageForExistingDWCTargetModification=Wenn sich das Schema des Zielobjekts geändert hat, verwenden Sie auf der Hauptseite die Funktion "Vorhandenem Zielobjekt zuordnen", um die Änderungen zu übernehmen, und ordnen Sie das Zielobjekt wieder seiner Quelle zu.
#XMSG: Information Message telling user to change target table before deploying replication flow
messageForNewColumnBeforeDeployment=Wenn die Zieltabelle bereits vorhanden ist und die Zuordnung eine Schemaänderung umfasst, müssen Sie die Zieltabelle vor dem Aktivieren des Replikationsflusses entsprechend ändern.
#XMSG: Information message telling user that user needs to manually change target table for any schema change before deploying replication flow if it has already been deployed.
messageForNewColumnAfterDeployment=Wenn Ihre Zuordnung eine Schemaänderung umfasst, müssen Sie die Zieltabelle vor dem Aktivieren des Replikationsflusses entsprechend ändern.
#XMSG: Information message telling user that unsupported cdc & Lob  columns are skipped in source
messageForSourceInvalidColumns=Die folgenden nicht unterstützten Spalten wurden in der Quelldefinition übersprungen: {0}
#XMSG: Information message telling user that unsupported cdc & Lob columns are skipped in target
messageForTargetInvalidColumns=Die folgenden nicht unterstützten Spalten wurden in der Zieldefinition übersprungen: {0}
#XMSG : Information message for Unsupported SAC Datasets. {0} {2} has been used for formatting purpose, and should not be altered. {1} contains the array of unsupported datasets along with the newlines and •
errorForDatasetFromSAC1=Die folgenden Objekte werden nicht unterstützt, da sie für die Verwendung verfügbar gemacht wurden: {0} {1} {0} {0} Um Tabellen in einem Replikationsfluss zu verwenden, darf die semantische Verwendung (in den Tabelleneinstellungen) nicht auf {2}Analytisches Datenset{2} festgelegt sein.
#XMSG : Information message for Unsupported SAC Dataset while map to exist. {0} is used for new lines , {1} is used for ""
errorForMapToExistingOfSACDataset1=Das Zielobjekt kann nicht verwendet werden, da es für die Verwendung verfügbar gemacht wurde. {0} {0} Um die Tabelle in einem Replikationsfluss zu verwenden, darf die semantische Verwendung (in den Tabelleneinstellungen) nicht auf {1}Analytisches Datenset{1} festgelegt sein.
#XMSG : Information message for Unsupported SAC Dataset while rename. {0} is used for new lines , {1} is used for ""
errorForRenameToSACDataset1=Ein Zielobjekt mit diesem Namen ist bereits vorhanden. Es kann jedoch nicht verwendet werden, da es für die Verwendung verfügbar gemacht wurde. {0} {0} Um die Tabelle in einem Replikationsfluss zu verwenden, darf die semantische Verwendung (in den Tabelleneinstellungen) nicht auf {1}Analytisches Datenset{1} festgelegt sein.
#XMSG: Information message for Unsupported non DWC_LOCAL_TABLE Dataset while rename. {0} is used for new lines
errorForRenameToUnsupportedDWCDataset=Ein Objekt mit diesem Namen ist im Ziel bereits vorhanden. {0}Dieses Objekt kann jedoch nicht als Zielobjekt für einen Replikationsfluss im lokalen Repository verwendet werden, da es sich dabei nicht um eine lokale Tabelle handelt.
#XMSG:
targetAutoRenameUpdated=Die Zielspalte wurde umbenannt.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDescUpdated1=Die Zielspalte wurde umbenannt, um Replikationen in Google BigQuery zu ermöglichen. Dies hat einen der folgenden Gründe:{0} {1}{2}Reservierter Spaltenname{3}{2}Nicht unterstützte Zeichen{3}{2}Reserviertes Präfix{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetConfluentAutoRenameDesc=Die Zielspalte wurde umbenannt, um Replikationen in Confluent zu ermöglichen. Dies hat einen der folgenden Gründe:{0} {1}{2}Reservierter Spaltenname{3}{2}Nicht unterstützte Zeichen{3}{2}Reserviertes Präfix{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetDatasphereAutoRenameDesc1=Die Zielspalte wurde umbenannt, um Replikationen in das Ziel zu ermöglichen. Dies hat einen der folgenden Gründe:{0} {1}{2}Nicht unterstützte Zeichen{3}{2}Reserviertes Präfix{3}{4}
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameGenericDesc=Die Zielspalte wurde umbenannt, um Replikationen in das Ziel zu ermöglichen. Dies hat einen der folgenden Gründe:{0} {1}{2}Reservierter Spaltenname{3}{2}Nicht unterstützte Zeichen{3}{2}Reserviertes Präfix{3}{4}
#XMSG:
targetAutoDataType=Der Zieldatentyp wurden geändert.
#XMSG:
targetAutoDataTypeDesc=Der Zieldatentyp wurde in {0} geändert, da der Quelldatentyp in Google BigQuery nicht unterstützt wird.
#XMSG: 0 - Datatype , 1 - Connection Name, can be SAP Datasphere or SAP Datasphere ( HDL_FILES)
targetAutoDataTypeDWC2=Der Zieldatentyp wurde in {0} geändert, da der Quelldatentyp nicht in der Zielverbindung unterstützt wird.
#XMSG
projectionGBQUnableToCreateKey=Die Primärschlüssel werden nicht angelegt.
#XMSG
projectionGBQUnableToCreateKeyMaxKeyCombinationDescription=In Google BigQuery werden maximal 16 Primärschlüssel unterstützt, das Quellobjekt enthält jedoch mehr Primärschlüssel. Keiner der Primärschlüssel wird im Zielobjekt angelegt.
#XMSG
HDLFNoKeyError=Legen Sie mindestens eine Spalte als Primärschlüssel fest.
#XMSG
HDLFNoKeyErrorDescription=Um ein Objekt zu replizieren, müssen Sie mindestens eine Spalte als Primärschlüssel festlegen.
#XMSG
HDLFNoKeyErrorExistingTarget=Legen Sie mindestens eine Spalte als Primärschlüssel fest.
#XMSG Arguments {0} and {1} are being used for formatting
HDLFNoKeyErrorExistingTargetDescription3=Um ein Objekt zu replizieren, muss mindestens eine Spalte als Primärschlüssel festgelegt sein. Verwenden Sie dazu eine Projektion. {0} {0} Sie haben folgende Möglichkeiten, eine oder mehrere Spalten als Primärschlüssel festzulegen: {0}{0}{1} Verwenden Sie den lokalen Tabelleneditor, um das vorhandene Zielobjekt zu ändern. Laden Sie dann den Replikationsfluss neu.{0}{0}{1} Benennen Sie das Zielobjekt im Replikationsfluss um, sodass ein neues Objekt angelegt wird, sobald ein Lauf gestartet wird. Nach der Umbenennung können Sie eine oder mehrere Spalten als Primärschlüssel in einer Projektion festlegen.{0}{0}{1} Ordnen Sie das Objekt einem anderen vorhandenen Zielobjekt zu, in dem bereits mindestens eine Spalte als Primärschlüssel festgelegt ist.
#XMSG
HDLFSourceTargetDifferentKeysWarning=Primärschlüssel wurde geändert.
#XMSG
HDLFSourceTargetDifferentKeysWarningDescription1=Für das Zielobjekt haben Sie andere Spalten als Primärschlüssel festgelegt als für das Quellobjekt. Stellen Sie sicher, dass diese Spalten alle Zeilen eindeutig identifizieren, um eine mögliche Datenbeschädigung bei der späteren Replikation der Daten zu vermeiden. {0} {0} Im Quellobjekt sind die folgenden Spalten als Primärschlüssel festgelegt: {0} {1}
#XMSG
duplicateDPIDColumns=Benennen Sie die Zielspalte um.
#XMSG
duplicateDPIDDColumnsDesc1=Dieser Zielspaltenname ist für eine technische Spalte reserviert. Geben Sie einen anderen Namen ein, um die Projektion zu sichern.
#XMSG:
targetAutoRenameDPID=Die Zielspalte wurde umbenannt.
#XMSG:0 - new line tag, (1,4) - Unordered list tag, (2,3) - Ordered list tag
targetAutoRenameDPIDDesc=Die Zielspalte wurde umbenannt, um Replikationen aus der ABAP-Quelle ohne Schlüssel zu ermöglichen. Dies hat einen der folgenden Gründe:{0} {1}{2}Reservierter Spaltenname{3}{2}Nicht unterstützte Zeichen{3}{2}Reserviertes Präfix{3}{4}
#~~~ Connection Setting Dialog ~~~~~~~~~~~~~~~~~~

#XTIT: Title text for Target Settings of selected container or object
targetSettingTitle={0} Zieleinstellungen
#XTIT: Title text for Source Settings of selected container or object
sourceSettingTitle={0} Quelleinstellungen
#XBUT
connectionSettingSave=Sichern
#XBUT
connectionSettingCancel=Abbrechen
#XBUT: Button to keep the object level settings
txtKeep=Beibehalten
#XBUT: Button to overwrite the Object level settings
txtOverwrite=Überschreiben
#XFLD
targetConnectionThreadlimit=Ziel-Thread-Grenze für Erstdatenübernahme (1–100)
#XFLD
connectionThreadLimit=Quell-Thread-Grenze für Erstdatenübernahme (1–100)
#XFLD
maxConnection=Replikations-Thread-Grenze (1–100)
#XFLD
kafkaNumberOfPartitions=Anzahl der Partitionen
#XFLD
kafkaReplicationFactor=Replikationsfaktor
#XFLD
kafkaMessageEncoder=Meldungsencoder
#XFLD
kafkaMessageCompression=Meldungskomprimierung
#XFLD
fileGroupDeltaFilesBy=Delta gruppieren nach
#XFLD
fileFormat=Dateityp
#XFLD
csvEncoding=CSV-Codierung
#XFLD
abapExitLbl=ABAP Exit
#XFLD
deltaPartition=Objekt-Thread-Anzahl für Delta-Datenübernahme (1–10)
#XFLD
clamping_Data=Bei Datenkürzung fehlschlagen
#XFLD
fail_On_Incompatible=Bei inkompatiblen Daten fehlschlagen
#XFLD
maxPartitionInput=Maximale Anzahl der Partitionen
#XFLD
max_Partition=Maximale Anzahl der Partitionen definieren
#XFLD
include_SubFolder=Unterordner einschließen
#XFLD
fileGlobalPattern=Globales Muster für Dateinamen
#XFLD
fileCompression=Dateikomprimierung
#XFLD: csv files have deliminiters like comma, colon, semicolon
fileColumnDelimiter=Dateitrennzeichen
#XFLD
fileIsHeaderIncluded=Dateikopf
#XFLD
fileOrient=Ausrichtung
#XFLD
gbqWriteMode=Schreibmodus
#XFLD
suppressDuplicate=Duplikate unterdrücken
#XFLD
apacheSpark=Apache-Spark-Kompatibilität aktivieren
#XFLD
clampingDatatypeCb=Datentypen mit dezimalem Gleitpunkt eingrenzen
#XFLD
overwriteDatasetSetting=Zieleinstellungen auf Objektebene überschreiben
#XFLD
overwriteSourceDatasetSetting=Quelleinstellungen auf Objektebene überschreiben
#XMSG
kafkaInvalidConnectionSetting=Geben Sie eine Zahl zwischen {0} und {1} ein.
#XMSG
MinReplicationThreadErrorMsg=Geben Sie eine Zahl größer als {0} ein.
#XMSG
MaxReplicationThreadErrorMsg=Geben Sie eine Zahl kleiner als {0} ein.
#XMSG
DeltaThreadErrorMsg=Geben Sie einen Wert zwischen 1 und 10 ein.
#XMSG
MaxPartitionErrorMsg=Geben Sie einen Wert zwischen 1 <= x <= 2147483647 ein. Der Standardwert ist 10.
#XMSG
MaxReplicationThreadErrorMsgForNonDecimal=Geben Sie eine Ganzzahl zwischen {0} und {1} ein.
#XFLD: Inherit value from the Confluent host broker
useDefaultConfluentFactor=Replikationsfaktor des Brokers verwenden
#XFLD
serializationFormat=Serialisierungsformat
#XFLD
compressionType=Komprimierungstyp
#XFLD
schemaRegistry=Schema-Registry verwenden
#XFLD
subjectNameStrat=Subject-Name-Strategie
#XFLD
compatibilityType=Kompatibilitätstyp
#XFLD
confluentTopicName=Topic-Name
#XFLD
confluentRecordName=Record-Name
#XFLD
confluentSubjectNamePreview=Subject-Name-Vorschau
#XMSG
serializationChangeToastMsgUpdated2=Das Serialisierungsformat wurde in JSON geändert, da die Schema-Registry nicht aktiviert ist. Um das Serialisierungsformat wieder in AVRO zu ändern, müssen Sie zunächst die Schema-Registry aktivieren.
#XBUT
confluentTopicNameInfo=Der Topic-Name basiert immer auf dem Namen des Zielobjekts. Sie können ihn ändern, indem Sie das Zielobjekt umbenennen.
#XMSG
emptyRecordNameValidationHeaderMsg=Geben Sie einen Record-Namen ein.
#XMSG
emptyPartionHeader=Geben Sie die Anzahl der Partitionen ein.
#XMSG
invalidPartitionsHeader=Geben Sie eine gültige Anzahl von Partitionen ein.
#XMSG
invalidpartitionsDesc=Geben Sie eine Zahl zwischen 1 und 200.000 ein.
#XMSG
emptyrFactorHeader=Geben Sie einen Replikationsfaktor ein.
#XMSG
invalidrFactorHeader=Geben Sie einen gültigen Replikationsfaktor ein.
#XMSG
invalidrFactorDesc=Geben Sie eine Zahl zwischen 1 und 32.767 ein.
#XMSG
emptyRecordNameValidationDescMsg=Wenn das Serialisierungsformat "AVRO" verwendet wird, werden nur die folgenden Zeichen unterstützt:{0}{1} A bis Z{0}{1} a bis z{0}{1} 0 bis 9{0}{1} _ (Unterstrich)
#XMSG
validRecordNameValidationHeaderMsg=Geben Sie einen gültigen Record-Namen ein.
#XMSG
validRecordNameValidationDescMsgUpdated=Da das Serialisierungsformat "AVRO" verwendet wird, darf der Name des Datensatzes nur aus alphanumerischen Zeichen (A-Z, a-z, 0-9) und Unterstrichen (_) bestehen. Er muss mit einem Buchstaben oder einem Unterstrich beginnen.
#XMSG
targetObjectNameSubTitle={0}
#XMSG
deltaPartitionnotEnabled=Die "Objekt-Thread-Anzahl für Delta-Datenübernahme" kann festgelegt werden, sobald mindestens ein Objekt den Ladetyp "Initial und Delta" aufweist.
#XMSG
invalidTargetName=Ungültiger Spaltenname
#XMSG
invalidTargetNameDesc=Der Name der Zielspalte darf nur aus alphanumerischen Zeichen (A-Z, a-z, 0-9) und Unterstrichen (_) bestehen.
#XFLD
consumeOtherSchema=Andere Schemaversionen verwenden
#XFLD
ignoreSchemamissmatch=Schemakonflikt ignorieren
#XFLD
confleuntDatatruncation=Bei Datenkürzung fehlschlagen
#XFLD
isolationLevel=Isolationsebene
#XFLD
confluentOffset=Startpunkt
#XFLD
signavioGroupDeltaFilesByText=Ohne
#XFLD
signavioFileFormatText=Parquet
#XFLD
signavioSparkCompatibilityParquetText=Nein
#XFLD
siganvioFileCompressionText=Snappy
#XFLD
siganvioSuppressDuplicatesText=Nein

#~~~~ Replication Task properties panel ~~~~~~~~~~~

#XFLD
lblTransformationProperties=Projektionen
#XBUT
txtAdd=Hinzufügen
#XBUT
txtEdit=Bearbeiten
#XMSG
transformationText=Fügen Sie eine Projektion hinzu, um Filter oder Zuordnung einzurichten.
#XMSG
primaryKeyRequiredText=Wählen Sie über "Schema konfigurieren" einen Primärschlüssel aus.
#XFLD
lblSettings=Einstellungen
#XFLD
lblTargetSetting={0}: Zieleinstellungen
#XMSG
@csvRF=Wählen Sie die Datei aus, die die Schemadefinition enthält, die Sie auf alle Dateien im Ordner anwenden möchten.
#XFLD
lblSourceColumns=Quellspalten
#XFLD
lblJsonStructure=JSON-Struktur
#XFLD
lblSourceSetting={0}: Quelleinstellungen
#XFLD
lblSourceSchemaSetting={0}: Quellschemaeinstellungen
#XBUT
messageSettings=Meldungseinstellungen
#XFLD
lblPropertyTitle1=Objekteigenschaften
#XFLD
lblRFPropertyTitle=Replikationsflusseigenschaften
#XMSG
noDataTxt=Es sind keine Spalten verfügbar.
#XMSG
noTargetObjectText=Es ist kein Zielobjekt ausgewählt.
#XLNK Ignore for translation
txtLessThen=<
#XFLD
targetColumns=Zielspalten
#XMSG
searchColumns=Spalten suchen
#XTOL
cdcColumnTooltip=Spalte für die Delta-Erfassung
#XMSG
sourceNonDeltaSupportErrorUpdated=Das Quellobjekt unterstützt keine Delta-Datenerfassung.
#XMSG
targetCDCColumnAdded=Zwei Zielspalten wurden für die Delta-Erfassung hinzugefügt.
#XMSG
deltaPartitionEnable=Objekt-Thread-Anzahl für Delta-Datenübernahme wurde den Quelleinstellungen hinzugefügt.
#XMSG
attributeMappingRemovalTxt=Ungültige Zuordnungen, die für das neue Zielobjekt nicht unterstützt werden, werden entfernt.
#XMSG
targetCDCColumnRemoved=Zwei für die Delta-Erfassung verwendete Zielspalten wurden entfernt.
#XMSG
replicationLoadTypeChanged=Ladetyp geändert in "Initial und Delta".
#XMSG
sourceHDLFLoadTypeError=Ändern Sie den Ladetyp in "Initial und Delta".
#XMSG
sourceHDLFLoadTypeSubtitle={0}
#XMSG
sourceHDLFLoadTypeErrorDescription1=Um ein Objekt aus einer Quellverbindung mit dem Verbindungstyp "SAP HANA Cloud, Data-Lake-Dateien" in SAP Datasphere zu replizieren, müssen Sie den Ladetyp "Initial und Delta" verwenden.
#XMSG
sourceHDLFDeltaCaptureErrorForNewTarget=Aktivieren Sie die Delta-Erfassung.
#XMSG
sourceHDLFDeltaCaptureSubtitleForNewTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForNewTargetDescription1=Um ein Objekt aus einer Quellverbindung mit dem Verbindungstyp "SAP HANA Cloud, Data-Lake-Dateien" in SAP Datasphere zu replizieren, müssen Sie die Delta-Erfassung aktivieren.
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTarget=Ändern Sie das Zielobjekt.
#XMSG
sourceHDLFDeltaCaptureSubtitleForExistingTarget={0}
#XMSG
sourceHDLFDeltaCaptureErrorForExistingTargetDescription1=Das Zielobjekt kann nicht verwendet werden, da die Delta-Erfassung deaktiviert ist. Sie können das Zielobjekt entweder umbenennen (wodurch Sie ein neues Objekt mit Delta-Erfassung anlegen können) oder es einem vorhandenen Objekt mit aktivierter Delta-Erfassung zuordnen.
#XMSG
deltaPartitionError=Geben Sie eine gültige Objekt-Thread-Anzahl für die Delta-Datenübernahme ein.
#XMSG
deltaPartitionErrorDescription=Geben Sie einen Wert zwischen 1 und 10 ein.
#XMSG
deltaPartitionEmptyError=Geben Sie eine Objekt-Thread-Anzahl für die Delta-Datenübernahme ein.
#XFLD
@lblColumnDescription=Beschreibung
#XMSG
@lblColumnDescriptionText1=Für technische Zwecke: Behandlung von doppelten Datensätzen, die durch Probleme bei der Replikation von ABAP-basierten Quellobjekten ohne Primärschlüssel verursacht werden.
#XFLD
storageType=Speicher
#XFLD
skipUnmappedColLbl=Nicht zugeordnete Spalten überspringen
#XFLD
abapContentTypeLbl=Inhaltstyp
#XFLD
autoMergeForTargetLbl=Daten automatisch zusammenführen
#~~~~~~ Replication flow properties panel ~~~~~~~~~

#XFLD
lblGeneral=Allgemein
#XFLD
lblBusinessName=Betriebswirtschaftlicher Name
#XFLD
lblTechnicalName=Technischer Name
#XFLD
lblPackage=Paket
#XFLD
statusPanel=Laufstatus
#XBTN: Schedule dropdown menu
SCHEDULE=Zeitplan
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE=Zeitplan bearbeiten
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE=Zeitplan löschen
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE=Zeitplan anlegen
#XFLD: empty cell
EMPTY_CELL=---
#XMSG:
SCHEDULE_CHECK_FAILED=Validierungsprüfung für Zeitplan fehlgeschlagen
#XMSG: error when creating schedule when in deploying state
msgReplicationFlowDeployScheduleError=Ein Zeitplan kann nicht angelegt werden, da der Replikationsfluss gerade aktiviert wird.{0}Warten Sie, bis der Replikationsfluss aktiviert wurde.
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=Für Replikationsflüsse, die Objekte mit dem Ladetyp "Initial und Delta" enthalten, kann kein Zeitplan angelegt werden.
#XMSG
msgReplicationFlowScheduleErrorTextUpdated=Für Replikationsflüsse, die Objekte mit dem Ladetyp "Initial und Delta/Nur Delta" enthalten, kann kein Zeitplan angelegt werden.
#XFLD : Scheduled popover
SCHEDULED=Eingeplant
#XFLD
CREATE_REPLICATION_TEXT=Replikationsfluss anlegen
#XFLD
EDIT_REPLICATION_TEXT=Replikationsfluss bearbeiten
#XFLD
DELETE_REPLICATION_TEXT=Replikationsfluss löschen
#XFLD
REFRESH_FREQUENCY=Häufigkeit
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT=Der Replikationsfluss kann nicht aktiviert werden, da der vorhandene Zeitplan{0} den Ladetyp "Initial und Delta" noch nicht unterstützt.{0}{0}Um den Replikationsfluss zu aktivieren, müssen Sie den Ladetyp aller Objekte{0} auf "Nur initial" setzen. Alternativ dazu können Sie den Zeitplan löschen, den {0}Replikationsfluss aktivieren und dann einen neuen Lauf starten. So erzeugen Sie einen Lauf ohne {0}Ende, der auch Objekte mit dem Ladetyp "Initial und Delta" unterstützt.
#XMSG
SCHEDULE_ERROR_DEPLOY_VALIDATION_TEXT_UPDATED=Der Replikationsfluss kann nicht aktiviert werden, da der vorhandene Zeitplan{0} den Ladetyp "Initial und Delta/Nur Delta" noch nicht unterstützt.{0}{0}Um den Replikationsfluss zu aktivieren, müssen Sie den Ladetyp aller Objekte{0} auf "Nur initial" setzen. Alternativ dazu können Sie den Zeitplan löschen, den {0}Replikationsfluss aktivieren und dann einen neuen Lauf starten. So erzeugen Sie einen Lauf ohne {0}Ende, der auch Objekte mit dem Ladetyp "Initial und Delta/Nur Delta" unterstützt.
#XMSG
SCHEDULE_EXCEPTION=Details des Zeitplans konnten nicht abgerufen werden
#XFLD: Label for frequency column
everyLabel=Alle
#XFLD: Plural Recurrence text for Hour
hoursLabel=Stunden
#XFLD: Plural Recurrence text for Day
daysLabel=Tage
#XFLD: Plural Recurrence text for Month
monthsLabel=Monate
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minuten
#XMSG
VALIDATE_EXCEPTION_SCHEDULE_POSSIBLE=Informationen über Einplanbarkeit konnten nicht abgerufen werden.
#XFLD :Paused field
PAUSED=Pausiert
#XMSG
navToMonitoring=In Fluss-Monitor öffnen
#XFLD
statusLbl=Status
#XFLD
lblLastRunExecuted=Start des letzten Laufs
#XFLD
lblLastExecuted=Letzter Lauf
#XFLD: Status text for Completed
statusCompleted=Abgeschlossen
#XFLD: Status text for Running
statusRunning=Wird ausgeführt
#XFLD: Status text for Failed
statusFailed=Fehlgeschlagen
#XFLD: Status text for Stopped
statusStopped=Gestoppt
#XFLD: Status text for Stopping
statusStopping=Wird gestoppt
#XFLD: Status text for Active
statusActive=Aktiv
#XFLD: Status text for Paused
statusPaused=Pausiert
#XFLD: Status text for not executed
lblNotExecuted=Noch nicht ausgeführt
#XFLD
messagesSettings=Meldungseinstellungen
#XTOL
@validateModel=Validierungsmeldungen
#XTOL
@hierarchy=Hierarchie
#XTOL
@columnCount=Anzahl der Spalten
#XMSG
VAL_PACKAGE_CHANGED=Sie haben dieses Objekt dem Paket "{1}" zugeordnet. Wählen Sie "Sichern", um diese Änderung zu bestätigen und zu validieren. Beachten Sie, dass die Zuordnung zu einem Paket nach dem Sichern in diesem Editor nicht mehr rückgängig gemacht werden kann.
#XMSG
MISSING_DEPENDENCY=Abhängigkeiten von Objekt "{0}" können nicht im Paket "{1}" gelöst werden.
#XFLD
deltaLoadInterval=Intervall für Delta-Datenübernahme
#XFLD
lblHour=Stunden (0–24)
#XFLD
lblMinutes=Minuten (0–59)
#XMSG
maxHourOrMinErr=Geben Sie einen Wert zwischen 0 und {0} ein.
#XMSG
maxDeltaInterval=Der Höchstwert des Intervalls für die Delta-Datenübernahme ist 24 Stunden.{0}Ändern Sie den Wert für Minuten oder Stunden entsprechend.
#~~~ Container Path ~~~~~~~~~~~~~~~~~~
#XFLD
targetContainernPathText=Zielcontainerpfad
#XFLD
confluentSubjectName=Subject-Name
#XFLD
confluentSchemaVersion=Schemaversion
#XFLD
confluentIncludeTechKeyUpdated=Technischen Schlüssel einschließen
#XFLD
confluentOmitNonExpandedArrays=Nicht aufgeklappte Arrays auslassen
#XFLD
confluentExpandArrayOrMap=Array oder Karte aufklappen
#XCOL
confluentOperationMapping=Vorgangszuordnung
#XCOL
confluentOpCode=Vorgangscode
#XFLD
confluentInsertOpCode=Einfügen
#XFLD
confluentUpdateOpCode=Aktualisieren
#XFLD
confluentDeleteOpCode=Löschen
#XFLD
expandArrayOrMapNotSelectedTxt=Nicht ausgewählt
#XFLD
confluentSwitchTxtYes=Ja
#XFLD
confluentSwitchTxtNo=Nein
#~~~ RF Execute  ~~~~~~~~~~~~~~~~~~

#XTIT
executeError=Fehler
#XTIT
executeWarning=Warnung
#XMSG
executeunsavederror=Sichern Sie den Replikationsfluss vor der Ausführung.
#XMSG
executemodifiederror=Im Replikationsfluss liegen nicht gesicherte Änderungen vor. Sichern Sie den Replikationsfluss.
#XMSG
executeundeployederror=Sie müssen Ihren Replikationsfluss aktivieren, bevor Sie ihn ausführen können.
#XMSG
executedeployingerror=Warten Sie, bis die Aktivierung abgeschlossen wurde.
#XMSG
msgRunStarted=Lauf wurde gestartet
#XMSG
msgExecuteFail=Replikationsfluss konnte nicht ausgeführt werden.
#XMSG
titleExecuteBusy=Bitte warten.
#XMSG
msgExecuteBusy=Ihre Daten werden für die Ausführung des Replikationsflusses vorbereitet.
#XTIT
executeConfirmDialog=Warnung
#XMSG
msgExecuteWithValidations=Replikationsfluss weist Validierungsfehler auf. Die Ausführung des Replikationsflusses kann fehlschlagen.
#XMSG
msgRunDeployedVersion=Es gibt Änderungen, die aktiviert werden müssen. Die letzte aktivierte Version des Replikationsflusses wird ausgeführt. Möchten Sie fortfahren?
#XBUT
btnExecuteAnyway=Trotzdem ausführen
#XBUT
btnExecuteClose=Schließen
#XBUT
loaderClose=Schließen
#XTIT
loaderTitle=Wird geladen
#XMSG
loaderText=Details werden vom Server abgerufen
#XMSG
premiumOutBoundRFCannotStartErrMsgPart1=Der Replikationsfluss für diese SAP-fremde Zielverbindung kann nicht gestartet werden,
#XMSG
premiumOutBoundRFCannotStartErrMsgPart2=da für diesen Monat kein Ausgangsvolumen verfügbar ist.
#XMSG
premiumOutBoundRFAdminErrMsgPart1=Ein Administrator kann die Premiumausgangsblöcke für diesen Tenant
#XMSG
premiumOutBoundRFAdminErrMsgPart2=erhöhen, wodurch Ausgangsvolumen für diesen Monat verfügbar wird.


#~~~ RF Deploy ~~~~~~~~~~~~~~
#XTIT
deployError=Fehler
#XTIT
deployInfo=Informationen
#XMSG
deployCheckFailException=Ausnahme bei der Aktivierung aufgetreten
#XMSG
deployGBQFFDisabled=Das Aktivieren von Replikationsflüssen mit einer Zielverbindung zu Google BigQuery ist derzeit nicht möglich, da eine Wartung für diese Funktion durchgeführt wird.
#XMSG
deployKAFKAFFDisabled=Das Aktivieren von Replikationsflüssen mit einer Zielverbindung zu Apache Kafka ist derzeit nicht möglich, da eine Wartung für diese Funktion durchgeführt wird.
#XMSG
deployConfluentDisabled=Das Aktivieren von Replikationsflüssen mit einer Zielverbindung zu Confluent Kafka ist derzeit nicht möglich, da eine Wartung für diese Funktion durchgeführt wird.
#XMSG
deployInValidDWCTargetDeltaCaptureObject=Die Namen der Tabellen für die Delta-Erfassung werden für die folgenden Zielobjekte bereits von anderen Tabellen im Repository verwendet: {0}. Bevor Sie den Replikationsfluss aktivieren können, müssen Sie die Zielobjekte umbenennen, damit die Namen der zugehörigen Tabellen für die Delta-Erfassung eindeutig sind.
#XMSG
deployDWCSourceFFDisabled=Das Aktivieren von Replikationsflüssen, deren Quelle SAP Datasphere ist, ist derzeit nicht möglich, da eine Wartung für diese Funktion durchgeführt wird.
#XMSG
deployDWCSourceWithDeltaEnabledInitialFFDisabled=Das Aktivieren von Replikationsflüssen, die deltafähige lokale Tabellen als Quellobjekte enthalten, ist derzeit nicht möglich, da eine Wartung für diese Funktion durchgeführt wird.
#XMSG
deployHDLFSourceFFDisabled=Das Aktivieren von Replikationsflüssen, die Quellverbindungen mit dem Verbindungstyp SAP HANA Cloud, Data Lake Files aufweisen, ist derzeit nicht möglich, da eine Wartung durchgeführt wird.
#XMSG
deployObjectStoreAsSourceFFDisabled=Das Aktivieren von Replikationsflüssen, die Cloud-Speicher-Provider als Quelle haben, ist derzeit nicht möglich.
#XMSG
deployConfluentSourceFFDisabled=Das Aktivieren von Replikationsflüssen, deren Quelle Confluent Kafka ist, ist derzeit nicht möglich, da eine Wartung für diese Funktion durchgeführt wird.
#XMSG
deployMaxDWCNewTableCrossed=Große Replikationsflüsse können nicht gleichzeitig gesichert und aktiviert werden. Sichern Sie Ihren Replikationsfluss zuerst, und aktivieren Sie ihn dann.
#XMSG
deployInProgressInfo=Aktivierung wird durchgeführt.
#XMSG
deploySourceObjectInUse=Die Quellobjekte {0} werden bereits in den Replikationsflüssen {1} verwendet.
#XMSG
deployTargetSourceObjectInUse=Die Quellobjekte {0} werden bereits in den Replikationsflüssen {1} verwendet. Die Zielobjekte {2} werden bereits in den Replikationsflüssen {3} verwendet.
#XMSG
deployReplicationFlowCheckError=Fehler bei Verifizierung von Replikationsfluss: {0}
#XMSG
preDeployTargetObjectInUse=Die Zielobjekte {0} werden bereits in Replikationsflüssen {1} verwendet. Sie können nicht dasselbe Zielobjekt in zwei verschiedenen Replikationsflüssen haben. Wählen Sie ein anderes Zielobjekt aus, und versuchen Sie es erneut.
#XMSG
runInProgressInfo=Replikationsfluss wird bereits ausgeführt.
#XMSG
deploySignavioTargetFFDisabled=Die Aktivierung von Replikationsflüssen, deren Ziel SAP Signavio ist, ist derzeit nicht möglich, da eine Wartung für diese Funktion durchgeführt wird.
#XMSG
deployHanaViewAsSourceFFDisabled=Die Aktivierung von Replikationsflüssen, die Views als Quellobjekte für die ausgewählte Quellverbindung aufweisen, ist derzeit nicht möglich. Versuchen Sie es später erneut.
#XMSG
deployMsOneLakeTargetFFDisabled=Die Aktivierung von Replikationsflüssen, deren Ziel Microsoft OneLake ist, ist derzeit nicht möglich, da eine Wartung für diese Funktion durchgeführt wird.
#XMSG
deploySFTPTargetFFDisabled=Die Aktivierung von Replikationsflüssen, deren Ziel SFTP ist, ist derzeit nicht möglich, da eine Wartung für diese Funktion durchgeführt wird.
#XMSG
deploySFTPSourceFFDisabled=Die Aktivierung von Replikationsflüssen, deren Quelle SFTP ist, ist derzeit nicht möglich, da eine Wartung für diese Funktion durchgeführt wird.
#~~~ Rename Target ~~~~~~~~~~~~~~~~~~~
#XFLD
renameTargetFieldName=Technischer Name
#XFLD
businessNameInRenameTarget=Betriebswirtschaftlicher Name
#XTOL
renametargetDialogTitle=Zielobjekt umbenennen
#XBUT
targetRenameButton=Umbenennen
#XBUT
targetRenameCancel=Abbrechen
#XMSG
mandatoryTargetName=Sie müssen einen Namen eingeben.
#XMSG
dwcSpecialChar=Unterstriche (_) sind das einzige zulässige Sonderzeichen.
#XMSG
dwcWithDot=Der Zieltabellenname kann aus lateinischen Buchstaben, Zahlen, Unterstrichen (_) und Punkten (.) bestehen. Das erste Zeichen muss ein Buchstabe, eine Zahl oder ein Unterstrich sein (kein Punkt).
#XMSG
nonDwcSpecialChar=Zulässige Sonderzeichen sind Unterstriche (_), Bindestriche (-) und Punkte (.)
#XMSG
firstUnderscorePattern=Name darf nicht mit einem Unterstrich (_) beginnen.

#~~~~ DDL dialog ~~~~~~~~~~~~~~~~~#
#XTIT
sqlTargetDDL={0}: SQL-Anweisung zum Anlegen der Tabelle anzeigen
#XMSG
sqlDialogMaxPKWarning=In Google BigQuery werden maximal 16 Primärschlüssel unterstützt. Das Quellobjekt weist mehr auf, weshalb in dieser Anweisung keine Primärschlüssel definiert werden.
#XMSG
sqlDialogIncomptiblePKTypeWarning=Mindestens eine Quellspalte weist einen Datentyp auf, der nicht als Primärschlüssel in Google BigQuery definiert werden kann. Deshalb werden in diesem Fall keine Primärschlüssel definiert. In Google BigQuery können nur die folgenden Datentypen einen Primärschlüssel haben: BIGNUMERIC, BOOLEAN, DATE, DATETIME, INT64, NUMERIC, STRING, TIMESTAMP
#XBUT
copyAndCloseDDL=Kopieren und schließen
#XBUT
closeDDL=Schließen
#XMSG
copiedToClipboard=In Zwischenablage kopiert


#~~~~ RF Service backend translated text ~~~~~~~~~~~~~~~~~#
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY=Das Objekt ''''{0}" kann nicht Teil einer Aufgabenkette sein, da es kein Ende hat (da es Objekte mit dem Ladetyp "Initial und Delta/Nur Delta" enthält).
#XMSG: Message
TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF=Das Objekt ''''{0}" kann nicht Teil einer Aufgabenkette sein, da es kein Ende hat (da es Objekte mit dem Ladetyp "Initial und Delta" enthält).
#XMSG: Cannot be Added to the chain
NOT_CHAINABLE=Das Objekt "{0}" kann nicht zur Aufgabenkette hinzugefügt werden.


#~~~ RF Open ~~~~~~~~~~~~~~
#XMSG: Information prompt to user when he opens an older replication flow with dsp target objects that are not saved yet. {0} is used for new lines
openReplicationFlowUnsavedTargetObject=Es liegen nicht gesicherte Änderungen vor. Sichern Sie sie erneut.{0}{0} Das Verhalten dieser Funktion hat sich geändert: In der Vergangenheit wurden die Zielobjekte in der Zielumgebung erst beim Aktivieren des Replikationsflusses angelegt.{0} Jetzt werden die Objekte bereits beim Sichern des Replikationsflusses angelegt. Ihr Replikationsfluss wurde vor dieser Änderung angelegt und enthält neue Objekte.{0} Sie müssen den Replikationsfluss vor dem Aktivieren erneut sichern, damit die neuen Objekte ordnungsgemäß berücksichtigt werden.
#XMSG
confirmChangeContentTypeMessage=Sie sind im Begriff, den Inhaltstyp zu ändern. Wenn sie ihn ändern, werden alle vorhandenen Projektionen gelöscht.

#~~~ Schema change Dialog ~~~~~~~~~~~~~~
#XFLD
schemaDialogSubjectName=Subject-Name
#XFLD
schemaDialogVersionName=Schemaversion
#XFLD
includeTechKey=Technischen Schlüssel einschließen
#XFLD
segementButtonFlat=Flach
#XFLD
segementButtonNested=Verschachtelt
#XMSG
subjectNamePlaceholder=Subject-Name suchen

#XMSG
@EmailNotificationSuccess=Die Konfiguration der Laufzeit-E-Mail-Benachrichtigungen wird gesichert.

#XFLD
@RuntimeEmailNotification=Laufzeit-E-Mail-Benachrichtigung

#XBTN
@TXT_SAVE=Sichern


